<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android" android:compileSdkVersion="34" android:compileSdkVersionCodename="14" package="com.community.oneroom" platformBuildVersionCode="34" platformBuildVersionName="14">
    <uses-permission android:name="android.permission.INTERNET"/>
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE"/>
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE"/>
    <uses-permission android:name="android.permission.VIBRATE"/>
    <uses-permission android:name="com.google.android.gms.permission.AD_ID"/>
    <uses-permission android:name="android.permission.READ_CALENDAR"/>
    <uses-permission android:name="android.permission.WRITE_CALENDAR"/>
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION"/>
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION"/>
    <uses-permission android:name="android.permission.READ_SETTINGS"/>
    <uses-permission android:name="android.permission.ACCESS_NOTIFICATION_POLICY"/>
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO"/>
    <uses-permission android:name="android.permission.FPOST_NOTIFICATIONS"/>
    <uses-permission android:name="android.permission.USE_FULL_SCREEN_INTENT"/>
    <uses-permission android:name="android.perrmission.INTERNET"/>
    <uses-permission android:name="com.huawei.android.launcher.permission.CHANGE_BADGE"/>
    <queries>
        <provider android:authorities="com.transsion.XOSauncher.unreadprovider"/>
        <provider android:authorities="com.transsion.hilaauncher.unreadprovider"/>
        <provider android:authorities="com.transsion.itel.launcher.unreadprovider"/>
        <provider android:authorities="com.transsion.walauncher.unreadprovider"/>
        <package android:name="com.whatsapp"/>
        <package android:name="com.whatsapp.w4b"/>
        <package android:name="com.gbwhatsapp"/>
        <package android:name="com.instagram.android"/>
        <package android:name="com.infinix.xshare"/>
        <package android:name="com.talpa.share"/>
        <package android:name="cn.xender"/>
        <package android:name="com.google.android.youtube"/>
        <package android:name="org.telegram.messenger.web"/>
        <intent>
            <action android:name="android.intent.action.VIEW"/>
            <category android:name="android.intent.category.BROWSABLE"/>
            <data android:scheme="https"/>
        </intent>
        <intent>
            <action android:name="android.support.customtabs.action.CustomTabsService"/>
        </intent>
        <intent>
            <action android:name="com.android.vending.billing.InAppBillingService.BIND"/>
        </intent>
        <package android:name="com.transsnet.store"/>
        <intent>
            <action android:name="android.intent.action.MAIN"/>
        </intent>
        <intent>
            <action android:name="android.intent.action.VIEW"/>
        </intent>
        <package android:name="com.community.oneroom"/>
        <package android:name="com.facebook.katana"/>
        <intent>
            <action android:name="android.speech.RecognitionService"/>
        </intent>
    </queries>
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW"/>
    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS"/>
    <uses-permission android:name="android.permission.ACCESS_MEDIA_LOCATION"/>
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE"/>
    <uses-permission android:name="android.permission.READ_MEDIA_VISUAL_USER_SELECTED"/>
    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO"/>
    <uses-feature android:name="android.hardware.camera" android:required="false"/>
    <uses-feature android:name="android.hardware.camera.autofocus" android:required="false"/>
    <uses-feature android:name="android.hardware.wifi.direct" android:required="false"/>
    <uses-permission android:name="android.permission.CAMERA"/>
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE"/>
    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE"/>
    <uses-permission android:name="android.permission.NEARBY_WIFI_DEVICES"/>
    <uses-permission android:name="android.permission.WAKE_LOCK"/>
    <uses-permission android:name="android.permission.MANAGE_WIFI_NETWORK_SELECTION"/>
    <uses-permission android:name="android.permission.WRITE_SETTINGS"/>
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS"/>
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES"/>
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED"/>
    <uses-permission android:name="android.permission.REORDER_TASKS"/>
    <uses-permission android:name="android.permission.DISABLE_KEYGUARD"/>
    <uses-permission android:name="android.permission.READ_SYNC_SETTINGS"/>
    <uses-permission android:name="android.permission.WRITE_SYNC_SETTINGS"/>
    <uses-permission android:name="android.permission.GET_ACCOUNTS"/>
    <uses-permission android:name="android.permission.AUTHENTICATE_ACCOUNTS"/>
    <uses-permission android:name="com.sec.android.provider.badge.permission.READ"/>
    <uses-permission android:name="com.sec.android.provider.badge.permission.WRITE"/>
    <uses-permission android:name="com.vivo.notification.permission.BADGE_ICON"/>
    <uses-permission android:name="android.permission.CALL_PHONE"/>
    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID"/>
    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION"/>
    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_TOPICS"/>
    <uses-permission android:name="com.android.vending.BILLING"/>
    <uses-permission android:name="com.transsion.tpush.permission.WRITE_PROVIDER"/>
    <uses-permission android:name="com.transsion.tpush.permission.READ_PROVIDER"/>
    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE"/>
    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE"/>
    <uses-feature android:glEsVersion="0x00020000" android:required="true"/>
    <uses-permission android:name="com.transsion.dataservice.permission.READ"/>
    <uses-permission android:name="com.transsion.dataservice.permission.WRITE"/>
    <meta-data android:name="tran_athena_version" android:value="3.0.0.5"/>
    <permission android:name="com.community.oneroom.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" android:protectionLevel="signature"/>
    <uses-permission android:name="com.community.oneroom.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"/>
    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES"/>
    <meta-data android:name="tran_gslb_version" android:value="1.0.3.1"/>
    <meta-data android:name="tran_http_version" android:value="1.0.53.10"/>
    <meta-data android:name="tran_coreutil_version" android:value="1.0.61.08"/>
    <uses-permission android:name="android.permission.RECORD_AUDIO"/>
    <uses-feature android:name="android.hardware.microphone" android:required="false"/>
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK"/>
    <uses-feature android:name="android.hardware.camera.front" android:required="false"/>
    <uses-feature android:name="android.hardware.camera.flash" android:required="false"/>
    <uses-feature android:name="android.hardware.screen.landscape" android:required="false"/>
    <uses-feature android:name="android.hardware.wifi" android:required="false"/>
    <meta-data android:name="tran_json_version" android:value="********"/>
    <application android:allowBackup="false" android:appComponentFactory="androidx.core.app.CoreComponentFactory" android:dataExtractionRules="@xml/data_extraction_rules" android:fullBackupContent="@xml/backup_rules" android:hardwareAccelerated="true" android:icon="@mipmap/ic_launcher" android:label="@string/base_app_name" android:largeHeap="true" android:name="com.transsion.subroom.app.SubRoomApp" android:networkSecurityConfig="@xml/network_security_config" android:requestLegacyExternalStorage="true" android:supportsRtl="true" android:theme="@style/Theme.Subroom.NoActionBar">
        <activity android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize" android:exported="false" android:name="com.google.android.gms.ads.AdActivity" android:theme="@style/ThemeTrans"/>
        <activity android:configChanges="locale|layoutDirection" android:launchMode="singleTask" android:name="com.cloud.hisavana.sdk.common.activity.HisavanaSplashActivity" android:theme="@style/ThemeTrans"/>
        <activity android:exported="false" android:name="com.transsion.subroom.activity.GuideActivity"/>
        <activity android:launchMode="singleTask" android:name="com.transsion.subroom.activity.NotAvailableActivity"/>
        <activity android:configChanges="navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize" android:exported="true" android:launchMode="singleTask" android:name="com.transsion.subroom.activity.MainActivity" android:screenOrientation="behind" android:theme="@style/Theme.Subroom.NoActionBarAd" android:windowSoftInputMode="stateAlwaysHidden|adjustPan"/>
        <activity android:exported="true" android:name="com.transsion.subroom.activity.SplashActivity" android:theme="@style/AppTheme.AppStart.Compat">
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>
        </activity>
        <activity android:exported="true" android:launchMode="singleTask" android:name="com.transsion.subroom.deeplink.DeepLinkHandler" android:theme="@style/AppTheme.AppStart">
            <intent-filter>
                <action android:name="android.intent.action.VIEW"/>
                <category android:name="android.intent.category.DEFAULT"/>
                <category android:name="android.intent.category.BROWSABLE"/>
                <data android:host="com.community.oneroom" android:scheme="oneroom"/>
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW"/>
                <category android:name="android.intent.category.DEFAULT"/>
                <category android:name="android.intent.category.BROWSABLE"/>
                <data android:host="com.community.moviebox" android:scheme="oneroom"/>
            </intent-filter>
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW"/>
                <category android:name="android.intent.category.DEFAULT"/>
                <category android:name="android.intent.category.BROWSABLE"/>
                <data android:host="v.aoneroom.com" android:scheme="https"/>
                <data android:host="h5.aoneroom.com" android:scheme="https"/>
                <data android:host="moviebox.ng" android:scheme="https"/>
                <data android:host="m.mvbrowse.com" android:scheme="https"/>
                <data android:host="v.moviebox.ng" android:scheme="https"/>
            </intent-filter>
        </activity>
        <provider android:authorities="com.community.oneroom.firebaseinitprovider" android:directBootAware="true" android:enabled="false" android:exported="false" android:initOrder="100" android:name="com.google.firebase.provider.FirebaseInitProvider"/>
        <provider android:authorities="com.community.oneroom.androidx-startup" android:exported="false" android:name="androidx.startup.InitializationProvider">
            <meta-data android:name="androidx.emoji2.text.EmojiCompatInitializer" android:value="androidx.startup"/>
            <meta-data android:name="androidx.lifecycle.ProcessLifecycleInitializer" android:value="androidx.startup"/>
            <meta-data android:name="androidx.profileinstaller.ProfileInstallerInitializer" android:value="androidx.startup"/>
        </provider>
        <meta-data android:name="com.aliyun.alivc_license.licensekey" android:value="volXpoYXKsoRJDDPt89df7215938a4eb88c536e67cd49923c"/>
        <meta-data android:name="com.aliyun.alivc_license.licensefile" android:value="assets/license.crt"/>
        <activity android:exported="false" android:name="com.transsion.member.MemberActivity"/>
        <activity android:exported="false" android:name="com.transsion.member.history.PointsHistoryActivity"/>
        <activity android:exported="false" android:name="com.transsion.home.category.PlayListActivity"/>
        <activity android:exported="false" android:name="com.transsion.home.category.CategoryActivity" android:screenOrientation="behind"/>
        <activity android:exported="false" android:name="com.transsion.home.activity.OperateActivity"/>
        <activity android:exported="false" android:name="com.transsion.home.activity.FilterActivity"/>
        <activity android:configChanges="navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize" android:exported="false" android:name="com.transsion.home.activity.MovieFilterActivity"/>
        <activity android:exported="false" android:launchMode="singleTop" android:name="com.transsion.home.activity.RankAllActivity" android:screenOrientation="behind"/>
        <activity android:exported="false" android:name="com.transsion.room.activity.HotRoomsActivity">
            <meta-data android:name="android.app.lib_name" android:value=""/>
        </activity>
        <activity android:name="com.transsion.room.activity.MyRoomActivity"/>
        <activity android:name="com.transsion.room.activity.CreateRoomActivity"/>
        <activity android:name="com.transsion.room.activity.RoomDetailActivity"/>
        <activity android:name="com.transsion.room.activity.RoomHomeActivity"/>
        <activity android:name="com.transsion.room.activity.RoomListActivity"/>
        <activity android:name="com.transsion.room.activity.OthersRoomListActivity"/>
        <activity android:autoRemoveFromRecents="true" android:configChanges="keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize" android:excludeFromRecents="true" android:exported="false" android:name="com.transsion.videodetail.StreamDetailActivity" android:supportsPictureInPicture="true" android:taskAffinity="com.transsion.videodetail.StreamDetailActivity"/>
        <activity android:configChanges="keyboardHidden|orientation|screenSize" android:exported="false" android:name="com.transsion.videodetail.music.ui.MusicDetailActivity"/>
        <activity android:autoRemoveFromRecents="true" android:configChanges="keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize" android:excludeFromRecents="true" android:exported="false" android:name="com.transsion.moviedetail.activity.MovieDetailActivity" android:supportsPictureInPicture="true" android:taskAffinity="com.transsion.moviedetail.activity.MovieDetailActivity"/>
        <activity android:exported="false" android:name="com.transsion.moviedetail.staff.MovieStaffActivity" android:screenOrientation="fullSensor"/>
        <activity android:exported="false" android:name="com.transsion.moviedetail.activity.MoviePosterActivity"/>
        <activity android:exported="false" android:name="com.transsion.moviedetail.activity.SubjectListActivity"/>
        <activity android:configChanges="keyboardHidden|orientation|screenSize" android:exported="false" android:launchMode="singleTask" android:name="com.transsion.player.longvideo.ui.TestLongVodActivity"/>
        <activity android:configChanges="keyboardHidden|orientation|screenSize" android:exported="false" android:name="com.transsion.postdetail.ui.activity.PostDetailActivity"/>
        <activity android:exported="false" android:name="com.transsion.postdetail.comment.ui.CommentListActivity"/>
        <activity android:configChanges="keyboardHidden|orientation|screenSize" android:exported="false" android:name="com.transsion.postdetail.ui.activity.PostDetailVideoActivity"/>
        <activity android:autoRemoveFromRecents="true" android:configChanges="keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize" android:excludeFromRecents="true" android:exported="true" android:label="@string/video_player" android:name="com.transsion.postdetail.ui.activity.LocalVideoDetailActivity" android:supportsPictureInPicture="true" android:taskAffinity="com.transsion.postdetail.ui.activity.LocalVideoDetailActivity">
            <intent-filter>
                <action android:name="android.intent.action.SEND"/>
                <category android:name="android.intent.category.DEFAULT"/>
                <data android:mimeType="video/*"/>
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW"/>
                <category android:name="android.intent.category.DEFAULT"/>
                <category android:name="android.intent.category.BROWSABLE"/>
                <data android:scheme="file"/>
                <data android:scheme="content"/>
                <data android:scheme="http"/>
                <data android:scheme="https"/>
                <data android:mimeType="audio/*"/>
                <data android:mimeType="video/*"/>
            </intent-filter>
        </activity>
        <activity android:autoRemoveFromRecents="true" android:configChanges="keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize" android:excludeFromRecents="true" android:exported="false" android:name="com.transsion.postdetail.shorttv.ShortTvListActivity" android:supportsPictureInPicture="true" android:taskAffinity="com.transsion.postdetail.shorttv.ShortTvListActivity" android:windowSoftInputMode="adjustPan"/>
        <activity android:configChanges="keyboardHidden|orientation|screenSize" android:exported="false" android:launchMode="singleTask" android:name="com.transsion.postdetail.shorttv.ShortTVFavoriteActivity" android:windowSoftInputMode="adjustPan"/>
        <activity android:exported="false" android:name="com.transsion.usercenter.laboratory.MccActivity"/>
        <activity android:exported="false" android:name="com.transsion.usercenter.setting.labelsfeedback.LabelsFeedbackActivity" android:windowSoftInputMode="adjustResize"/>
        <activity android:exported="false" android:name="com.transsion.usercenter.setting.dev.DevActivity"/>
        <activity android:exported="false" android:name="com.transsion.usercenter.FollowActivity"/>
        <activity android:exported="false" android:name="com.transsion.usercenter.UserCenterActivity"/>
        <activity android:exported="false" android:name="com.transsion.usercenter.ProfileEditActivity"/>
        <activity android:exported="false" android:name="com.transsion.usercenter.ProfileSettingAboutUsActivity"/>
        <activity android:exported="false" android:name="com.transsion.usercenter.profile.ProfileActivity"/>
        <activity android:exported="false" android:name="com.transsion.usercenter.profile.see.ProfileSeeActivity"/>
        <activity android:exported="false" android:name="com.transsion.usercenter.edit.ProfileEditCenterActivity"/>
        <activity android:exported="false" android:name="com.transsion.usercenter.setting.SettingActivity"/>
        <activity android:exported="false" android:name="com.transsion.usercenter.setting.feedback.UserSettingFeedbackActivity"/>
        <activity android:exported="false" android:name="com.transsion.usercenter.setting.WebViewActivity"/>
        <activity android:exported="false" android:name="com.transsion.usercenter.setting.SettingAboutUsActivity"/>
        <activity android:exported="false" android:name="com.transsion.usercenter.message.UserMessageActivity"/>
        <activity android:exported="false" android:name="com.transsion.usercenter.message.UserRoomMessageActivity"/>
        <activity android:exported="false" android:name="com.transsion.usercenter.message.detail.MessageDetailActivity"/>
        <activity android:exported="false" android:name="com.transsion.usercenter.laboratory.LaboratoryActivity"/>
        <activity android:exported="false" android:name="com.transsion.usercenter.profile.ProfileQRCodeActivity"/>
        <activity android:exported="false" android:launchMode="singleTask" android:name="com.transsion.usercenter.setting.SettingNoticeActivity"/>
        <activity android:name="com.transsion.edcation.list.MyCourseListActivity"/>
        <activity android:name="com.transsion.edcation.history.EducationHistoryActivity"/>
        <activity android:exported="false" android:name="com.transsnet.downloader.activity.AllHistoricalPlayRecordActivity"/>
        <activity android:launchMode="singleTop" android:name="com.transsnet.downloader.activity.DownloadPanelActivity"/>
        <activity android:launchMode="singleTop" android:name="com.transsnet.downloader.activity.DownloadSeriesListActivity"/>
        <activity android:name="com.transsnet.downloader.activity.TransferActivity"/>
        <service android:exported="false" android:name="com.transsnet.downloader.guard.DownloadGuardService">
            <intent-filter>
                <action android:name="com.transsnet.downloader.manager.guard.DOWNLOAD_SERVICE"/>
            </intent-filter>
        </service>
        <service android:name="com.transsnet.downloader.core.ForegroundService"/>
        <provider android:authorities="com.community.oneroom.download.fileprovider" android:exported="false" android:grantUriPermissions="true" android:name="androidx.core.content.FileProvider">
            <meta-data android:name="android.support.FILE_PROVIDER_PATHS" android:resource="@xml/filepaths"/>
        </provider>
        <activity android:exported="false" android:name="com.transsion.commercialization.pslink.PsLinkActivity"/>
        <activity android:exported="false" android:name="com.transsion.commercialization.aha.AhaGameAllActivity"/>
        <activity android:name="com.transsion.payment.lib.strategy.ui.PayWebActivity"/>
        <activity android:name="com.transsion.publish.ui.SelectImageActivity"/>
        <activity android:name="com.transsion.publish.ui.SelectVideoActivity"/>
        <activity android:name="com.transsion.publish.ui.SelectMusicActivity"/>
        <activity android:name="com.transsion.publish.ui.FilmReviewActivity" android:windowSoftInputMode="adjustResize"/>
        <activity android:name="com.transsion.publish.ui.GalleryActivity"/>
        <activity android:name="com.transsion.publish.ui.ClippingImageActivity"/>
        <activity android:name="com.transsion.publish.ui.VideoPreviewActivity"/>
        <activity android:name="com.transsion.publish.ui.SelectLocationActivity"/>
        <service android:name="com.transsion.publish.PublishService"/>
        <activity android:exported="false" android:name="com.transsion.ninegridview.preview.GifImagePreviewActivity" android:theme="@style/Theme.AppCompat.Translucent">
            <meta-data android:name="android.app.lib_name" android:value=""/>
        </activity>
        <activity android:name="com.transsion.ninegridview.preview.ImagePreviewActivity" android:theme="@style/Theme.AppCompat.Translucent"/>
        <activity android:exported="false" android:name="com.transsion.wrapperad.middle.splash.NonSplashActivity"/>
        <activity android:configChanges="locale|layoutDirection" android:exported="false" android:launchMode="singleTask" android:name="com.transsion.wrapperad.middle.intercept.video.NonVideoAdActivity" android:theme="@style/AdTransparentActivityTheme"/>
        <activity android:configChanges="locale|layoutDirection" android:exported="false" android:launchMode="singleTask" android:name="com.transsion.wrapperad.middle.intercept.interstitial.NonInterstitialAdActivity" android:theme="@style/AdTransparentActivityTheme"/>
        <meta-data android:name="com.google.android.gms.ads.APPLICATION_ID" android:value="ca-app-pub-5464101117258718~2034480240"/>
        <activity android:name="com.transsnet.login.interest.LoginInterestActivity"/>
        <activity android:name="com.transsnet.login.LoginActivity" android:windowSoftInputMode="stateAlwaysVisible"/>
        <activity android:name="com.transsnet.login.country.LoginSelectCountryActivity"/>
        <activity android:name="com.transsnet.login.phone.LoginPhoneCodeActivity" android:windowSoftInputMode="stateAlwaysVisible"/>
        <activity android:launchMode="singleTop" android:name="com.transsnet.login.LoginLikeActivity" android:theme="@style/TranslucentStyle"/>
        <activity android:launchMode="singleTop" android:name="com.transsnet.login.phone.LoginPwdActivity"/>
        <activity android:launchMode="singleTop" android:name="com.transsnet.login.phone.LoginSetPwdActivity"/>
        <activity android:launchMode="singleTop" android:name="com.transsnet.login.email.LoginEmailPwdActivity"/>
        <activity android:name="com.transsion.fission.FissionInvitationCodeActivity" android:windowSoftInputMode="adjustResize"/>
        <activity android:exported="false" android:name="com.transsion.transfer.wifi.permission.PermissionsActivity"/>
        <activity android:exported="false" android:name="com.transsion.transfer.wifi.ui.WifiP2PTestActivity"/>
        <activity android:exported="false" android:launchMode="singleTop" android:name="com.transsion.transfer.wifi.ui.WifiConnectActivity"/>
        <activity android:exported="false" android:launchMode="singleTop" android:name="com.transsion.transfer.wifi.ui.WifiCreateActivity"/>
        <activity android:exported="false" android:name="com.transsion.transfer.impl.TransferStatusActivity"/>
        <activity android:configChanges="keyboardHidden|orientation|screenSize" android:name="com.transsion.web.activity.WebActivity"/>
        <activity android:exported="false" android:name="com.transsion.push.PushTestActivity"/>
        <service android:exported="false" android:name="com.transsion.push.helper.NotificationRefreshService"/>
        <receiver android:exported="true" android:name="com.transsion.push.receiver.NotificationReceiver">
            <intent-filter>
                <action android:name="com.community.oneroom.notification_delete"/>
                <action android:name="com.community.oneroom.last_permanent_msg"/>
                <action android:name="com.community.oneroom.next_permanent_msg"/>
                <action android:name="android.intent.action.SCREEN_OFF"/>
                <action android:name="android.intent.action.SCREEN_ON"/>
                <action android:name="android.intent.action.USER_PRESENT"/>
                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED"/>
                <action android:name="android.intent.action.ACTION_POWER_CONNECTED"/>
            </intent-filter>
        </receiver>
        <service android:enabled="true" android:exported="true" android:name="com.transsion.al.ka.AccountService">
            <intent-filter>
                <action android:name="android.accounts.AccountAuthenticator"/>
            </intent-filter>
            <meta-data android:name="android.accounts.AccountAuthenticator" android:resource="@xml/account_auth"/>
        </service>
        <service android:enabled="true" android:exported="true" android:name="com.transsion.al.ka.AccountSyncService">
            <intent-filter>
                <action android:name="android.content.SyncAdapter"/>
            </intent-filter>
            <meta-data android:name="android.content.SyncAdapter" android:resource="@xml/sync_adapter"/>
        </service>
        <provider android:authorities="com.community.oneroom.storage.account.sync" android:name="com.transsion.al.ka.AccountSyncContentProvider"/>
        <provider android:authorities="com.community.oneroom.storage.contacts.sync" android:enabled="true" android:exported="true" android:name="com.transsion.al.ka.FastCleanerProvider" android:syncable="true">
            <meta-data android:name="android.content.ContactDirectory" android:value="true"/>
        </provider>
        <receiver android:exported="true" android:name="com.transsion.al.ka.BootReceiver">
            <intent-filter android:priority="**********">
                <action android:name="android.intent.action.PACKAGE_RESTARTED"/>
                <action android:name="android.intent.action.BOOT_COMPLETED"/>
                <action android:name="android.intent.action.UID_REMOVED"/>
                <action android:name="android.intent.action.TIME_SET"/>
            </intent-filter>
        </receiver>
        <activity android:exported="false" android:name="com.tn.tranpay.activity.TranPayWebActivity" android:theme="@style/Theme.TranPay"/>
        <activity android:exported="true" android:name="com.tn.tranpay.activity.PayActivity" android:theme="@style/Theme.TranPay"/>
        <activity android:exported="false" android:name="com.transsion.spwaitkiller.test.TestSpWait2Activity"/>
        <activity android:exported="true" android:name="com.transsion.spwaitkiller.test.TestSpWaitActivity"/>
        <meta-data android:name="tran_hisavana_version" android:value="3.1.4.4"/>
        <provider android:authorities="com.community.oneroom.HisavanaConfigContentProvider" android:exported="false" android:name="com.hisavana.mediation.config.ConfigContentProvider"/>
        <uses-library android:name="org.apache.http.legacy" android:required="false"/>
        <meta-data android:name="com.google.android.gms.ads.flag.OPTIMIZE_INITIALIZATION" android:value="true"/>
        <meta-data android:name="com.google.android.gms.ads.flag.OPTIMIZE_AD_LOADING" android:value="true"/>
        <provider android:authorities="com.community.oneroom.mobileadsinitprovider" android:exported="false" android:initOrder="100" android:name="com.google.android.gms.ads.MobileAdsInitProvider"/>
        <service android:enabled="true" android:exported="false" android:name="com.google.android.gms.ads.AdService"/>
        <activity android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize" android:exported="false" android:name="com.google.android.gms.ads.OutOfContextTestingActivity"/>
        <activity android:excludeFromRecents="true" android:exported="false" android:launchMode="singleTask" android:name="com.google.android.gms.ads.NotificationHandlerActivity" android:taskAffinity="" android:theme="@android:style/Theme.Translucent.NoTitleBar"/>
        <property android:name="android.adservices.AD_SERVICES_CONFIG" android:resource="@xml/gma_ad_services_config"/>
        <service android:directBootAware="false" android:enabled="@bool/enable_system_alarm_service_default" android:exported="false" android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"/>
        <service android:directBootAware="false" android:enabled="@bool/enable_system_job_service_default" android:exported="true" android:name="androidx.work.impl.background.systemjob.SystemJobService" android:permission="android.permission.BIND_JOB_SERVICE"/>
        <service android:directBootAware="false" android:enabled="@bool/enable_system_foreground_service_default" android:exported="false" android:name="androidx.work.impl.foreground.SystemForegroundService"/>
        <receiver android:directBootAware="false" android:enabled="true" android:exported="false" android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"/>
        <receiver android:directBootAware="false" android:enabled="false" android:exported="false" android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy">
            <intent-filter>
                <action android:name="android.intent.action.ACTION_POWER_CONNECTED"/>
                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED"/>
            </intent-filter>
        </receiver>
        <receiver android:directBootAware="false" android:enabled="false" android:exported="false" android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy">
            <intent-filter>
                <action android:name="android.intent.action.BATTERY_OKAY"/>
                <action android:name="android.intent.action.BATTERY_LOW"/>
            </intent-filter>
        </receiver>
        <receiver android:directBootAware="false" android:enabled="false" android:exported="false" android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy">
            <intent-filter>
                <action android:name="android.intent.action.DEVICE_STORAGE_LOW"/>
                <action android:name="android.intent.action.DEVICE_STORAGE_OK"/>
            </intent-filter>
        </receiver>
        <receiver android:directBootAware="false" android:enabled="false" android:exported="false" android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy">
            <intent-filter>
                <action android:name="android.net.conn.CONNECTIVITY_CHANGE"/>
            </intent-filter>
        </receiver>
        <receiver android:directBootAware="false" android:enabled="false" android:exported="false" android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver">
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED"/>
                <action android:name="android.intent.action.TIME_SET"/>
                <action android:name="android.intent.action.TIMEZONE_CHANGED"/>
            </intent-filter>
        </receiver>
        <receiver android:directBootAware="false" android:enabled="@bool/enable_system_alarm_service_default" android:exported="false" android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver">
            <intent-filter>
                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies"/>
            </intent-filter>
        </receiver>
        <receiver android:directBootAware="false" android:enabled="true" android:exported="true" android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver" android:permission="android.permission.DUMP">
            <intent-filter>
                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS"/>
            </intent-filter>
        </receiver>
        <service android:directBootAware="true" android:exported="false" android:name="androidx.room.MultiInstanceInvalidationService"/>
        <meta-data android:name="com.google.android.play.billingclient.version" android:value="6.1.0"/>
        <activity android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize" android:exported="false" android:name="com.android.billingclient.api.ProxyBillingActivity" android:theme="@android:style/Theme.Translucent.NoTitleBar"/>
        <meta-data android:name="com.bumptech.glide.integration.okhttp3.OkHttpGlideModule" android:value="GlideModule"/>
        <service android:directBootAware="true" android:exported="false" android:name="com.google.firebase.components.ComponentDiscoveryService">
            <meta-data android:name="com.google.firebase.components:com.google.firebase.perf.ktx.FirebasePerfKtxRegistrar" android:value="com.google.firebase.components.ComponentRegistrar"/>
            <meta-data android:name="com.google.firebase.components:com.google.firebase.perf.FirebasePerfRegistrar" android:value="com.google.firebase.components.ComponentRegistrar"/>
            <meta-data android:name="com.google.firebase.components:com.google.firebase.crashlytics.ktx.FirebaseCrashlyticsKtxRegistrar" android:value="com.google.firebase.components.ComponentRegistrar"/>
            <meta-data android:name="com.google.firebase.components:com.google.firebase.remoteconfig.ktx.FirebaseRemoteConfigKtxRegistrar" android:value="com.google.firebase.components.ComponentRegistrar"/>
            <meta-data android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar" android:value="com.google.firebase.components.ComponentRegistrar"/>
            <meta-data android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar" android:value="com.google.firebase.components.ComponentRegistrar"/>
            <meta-data android:name="com.google.firebase.components:com.google.firebase.crashlytics.ndk.CrashlyticsNdkRegistrar" android:value="com.google.firebase.components.ComponentRegistrar"/>
            <meta-data android:name="com.google.firebase.components:com.google.firebase.analytics.ktx.FirebaseAnalyticsKtxRegistrar" android:value="com.google.firebase.components.ComponentRegistrar"/>
            <meta-data android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar" android:value="com.google.firebase.components.ComponentRegistrar"/>
            <meta-data android:name="com.google.firebase.components:com.google.firebase.iid.Registrar" android:value="com.google.firebase.components.ComponentRegistrar"/>
            <meta-data android:name="com.google.firebase.components:com.google.firebase.remoteconfig.RemoteConfigRegistrar" android:value="com.google.firebase.components.ComponentRegistrar"/>
            <meta-data android:name="com.google.firebase.components:com.google.firebase.abt.component.AbtRegistrar" android:value="com.google.firebase.components.ComponentRegistrar"/>
            <meta-data android:name="com.google.firebase.components:com.google.firebase.crashlytics.CrashlyticsRegistrar" android:value="com.google.firebase.components.ComponentRegistrar"/>
            <meta-data android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar" android:value="com.google.firebase.components.ComponentRegistrar"/>
            <meta-data android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar" android:value="com.google.firebase.components.ComponentRegistrar"/>
            <meta-data android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar" android:value="com.google.firebase.components.ComponentRegistrar"/>
            <meta-data android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar" android:value="com.google.firebase.components.ComponentRegistrar"/>
            <meta-data android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar" android:value="com.google.firebase.components.ComponentRegistrar"/>
        </service>
        <provider android:authorities="com.community.oneroom.firebaseperfprovider" android:exported="false" android:initOrder="101" android:name="com.google.firebase.perf.provider.FirebasePerfProvider"/>
        <activity android:exported="false" android:name="com.cloud.hisavana.sdk.common.activity.AdvertiserLinkActivity" android:screenOrientation="behind" android:theme="@style/NoTranslucentStatusBar"/>
        <activity android:exported="false" android:multiprocess="true" android:name="com.cloud.hisavana.sdk.common.activity.PersonalCloseAdActivity" android:screenOrientation="behind" android:theme="@style/NoTranslucentStatusBar"/>
        <activity android:exported="false" android:name="com.cloud.hisavana.sdk.common.activity.OfflineLandingActivity" android:screenOrientation="behind" android:theme="@style/NoTranslucentStatusBar"/>
        <activity android:exported="false" android:name="com.cloud.hisavana.sdk.common.activity.AgentPageLandingActivity" android:screenOrientation="behind" android:theme="@style/NoTranslucentStatusBar"/>
        <activity android:exported="false" android:name="com.cloud.hisavana.sdk.common.activity.EWOfficialActivity" android:screenOrientation="behind" android:theme="@style/NoTranslucentStatusBar"/>
        <activity android:exported="false" android:name="com.cloud.hisavana.sdk.common.activity.OnlineLandingActivity" android:screenOrientation="behind" android:theme="@style/NoTranslucentStatusBar"/>
        <meta-data android:name="com.google.android.gms.ads.INTEGRATION_MANAGER" android:value="webview"/>
        <activity android:configChanges="locale|uiMode|layoutDirection" android:exported="false" android:launchMode="singleTask" android:multiprocess="true" android:name="com.cloud.hisavana.sdk.common.activity.TAdInterstitialActivity" android:theme="@style/transcutestyle"/>
        <activity android:configChanges="locale|layoutDirection" android:exported="false" android:launchMode="singleTask" android:name="com.cloud.hisavana.sdk.common.activity.CustomDialogActivity" android:theme="@style/transcutestyle"/>
        <activity android:configChanges="locale|layoutDirection" android:exported="false" android:launchMode="singleTask" android:name="com.cloud.hisavana.sdk.common.activity.TAdWebFormsActivity" android:screenOrientation="behind" android:theme="@style/transcutestyle"/>
        <provider android:authorities="com.community.oneroom.HisavanaContentProvider" android:exported="false" android:name="com.cloud.hisavana.sdk.database.HisavanaContentProvider"/>
        <provider android:authorities="com.community.oneroom.provider" android:exported="false" android:grantUriPermissions="true" android:name="com.cloud.hisavana.sdk.common.HisavanaFileProvider">
            <meta-data android:name="android.support.FILE_PROVIDER_PATHS" android:resource="@xml/hisavana_provider_paths"/>
        </provider>
        <activity android:exported="false" android:label="@string/places_autocomplete_label" android:name="com.google.android.libraries.places.widget.AutocompleteActivity" android:theme="@style/PlacesAutocompleteOverlay" android:windowSoftInputMode="adjustResize"/>
        <activity android:configChanges="keyboardHidden|orientation|screenSize" android:exported="false" android:name="com.blankj.utilcode.util.UtilsTransActivity4MainProcess" android:theme="@style/ActivityTranslucent" android:windowSoftInputMode="stateAlwaysHidden"/>
        <activity android:configChanges="keyboardHidden|orientation|screenSize" android:exported="false" android:multiprocess="true" android:name="com.blankj.utilcode.util.UtilsTransActivity" android:theme="@style/ActivityTranslucent" android:windowSoftInputMode="stateAlwaysHidden"/>
        <provider android:authorities="com.community.oneroom.utilcode.fileprovider" android:exported="false" android:grantUriPermissions="true" android:name="com.blankj.utilcode.util.UtilsFileProvider">
            <meta-data android:name="android.support.FILE_PROVIDER_PATHS" android:resource="@xml/util_code_provider_paths"/>
        </provider>
        <service android:exported="false" android:name="com.blankj.utilcode.util.MessengerUtils$ServerService">
            <intent-filter>
                <action android:name="com.community.oneroom.messenger"/>
            </intent-filter>
        </service>
        <activity android:excludeFromRecents="true" android:exported="false" android:name="com.transsion.pushui.activity.TransparentActivity" android:taskAffinity="com.transsion.pushui" android:theme="@android:style/Theme.Translucent.NoTitleBar"/>
        <activity android:exported="true" android:name="com.transsion.pushui.activity.PushActivity">
            <intent-filter>
                <action android:name="tpush.intent.action.TPUSH"/>
            </intent-filter>
        </activity>
        <receiver android:exported="true" android:name="com.transsion.push.broadcast.FCMMessageReceiver" android:permission="com.google.android.c2dm.permission.SEND">
            <intent-filter android:priority="999">
                <action android:name="com.google.android.c2dm.intent.RECEIVE"/>
                <action android:name="com.google.android.c2dm.intent.REGISTRATION"/>
                <category android:name="com.community.oneroom"/>
            </intent-filter>
        </receiver>
        <service android:exported="true" android:name="com.transsion.push.service.PushJobIntentService" android:permission="android.permission.BIND_JOB_SERVICE"/>
        <receiver android:enabled="true" android:exported="false" android:name="com.transsion.push.broadcast.HandlerBroadcastReceiver">
            <intent-filter android:priority="999">
                <action android:name="tpush.intent.action.TPUSH_MSG_RECEIVER"/>
            </intent-filter>
        </receiver>
        <meta-data android:name="tran_push_version" android:value="1.8.5.02"/>
        <meta-data android:name="tpush_app_id" android:value="8534b43bc794414987957009b5060377"/>
        <meta-data android:name="tpush_app_key" android:value="8fc969cd4a4b4e9190c38322875b0f09"/>
        <receiver android:exported="true" android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver" android:permission="com.google.android.c2dm.permission.SEND">
            <intent-filter>
                <action android:name="com.google.android.c2dm.intent.RECEIVE"/>
            </intent-filter>
            <meta-data android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED" android:value="true"/>
        </receiver>
        <service android:directBootAware="true" android:exported="false" android:name="com.google.firebase.messaging.FirebaseMessagingService">
            <intent-filter android:priority="-500">
                <action android:name="com.google.firebase.MESSAGING_EVENT"/>
            </intent-filter>
        </service>
        <meta-data android:name="com.bumptech.glide.integration.cronet.CronetGlideModule" android:value="GlideModule"/>
        <activity android:excludeFromRecents="true" android:exported="false" android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity" android:theme="@android:style/Theme.Translucent.NoTitleBar"/>
        <service android:exported="true" android:name="com.google.android.gms.auth.api.signin.RevocationBoundService" android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION" android:visibleToInstantApps="true"/>
        <receiver android:enabled="true" android:exported="false" android:name="com.google.android.gms.measurement.AppMeasurementReceiver"/>
        <service android:enabled="true" android:exported="false" android:name="com.google.android.gms.measurement.AppMeasurementService"/>
        <service android:enabled="true" android:exported="false" android:name="com.google.android.gms.measurement.AppMeasurementJobService" android:permission="android.permission.BIND_JOB_SERVICE"/>
        <activity android:configChanges="keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize" android:exported="false" android:name="com.facebook.ads.AudienceNetworkActivity" android:theme="@android:style/Theme.Translucent.NoTitleBar"/>
        <provider android:authorities="com.community.oneroom.AudienceNetworkContentProvider" android:exported="false" android:name="com.facebook.ads.AudienceNetworkContentProvider"/>
        <activity android:exported="false" android:name="com.google.android.gms.common.api.GoogleApiActivity" android:theme="@android:style/Theme.Translucent.NoTitleBar"/>
        <activity android:configChanges="keyboardHidden|orientation|screenSize" android:launchMode="standard" android:name="com.bytedance.sdk.openadsdk.activity.TTLandingPageActivity" android:theme="@style/tt_landing_page"/>
        <activity android:configChanges="keyboardHidden|orientation|screenSize" android:launchMode="standard" android:name="com.bytedance.sdk.openadsdk.activity.TTPlayableLandingPageActivity" android:theme="@style/tt_landing_page"/>
        <activity android:configChanges="keyboardHidden|orientation|screenSize" android:launchMode="standard" android:name="com.bytedance.sdk.openadsdk.activity.TTVideoLandingPageLink2Activity" android:theme="@style/tt_landing_page"/>
        <activity android:launchMode="standard" android:name="com.bytedance.sdk.openadsdk.activity.TTDelegateActivity" android:theme="@android:style/Theme.Translucent.NoTitleBar"/>
        <activity android:launchMode="standard" android:name="com.bytedance.sdk.openadsdk.activity.TTWebsiteActivity" android:theme="@style/tt_privacy_landing_page"/>
        <service android:name="com.bytedance.sdk.openadsdk.multipro.aidl.BinderPoolService"/>
        <activity android:configChanges="keyboardHidden|orientation|screenSize" android:launchMode="standard" android:name="com.bytedance.sdk.openadsdk.activity.TTAppOpenAdActivity" android:theme="@style/tt_app_open_ad_no_animation"/>
        <activity android:configChanges="keyboardHidden|orientation|screenSize" android:launchMode="standard" android:name="com.bytedance.sdk.openadsdk.activity.TTRewardVideoActivity" android:theme="@style/tt_full_screen_new"/>
        <activity android:configChanges="keyboardHidden|orientation|screenSize" android:launchMode="standard" android:name="com.bytedance.sdk.openadsdk.activity.TTRewardExpressVideoActivity" android:theme="@style/tt_full_screen_new"/>
        <activity android:configChanges="keyboardHidden|orientation|screenSize" android:launchMode="standard" android:name="com.bytedance.sdk.openadsdk.activity.TTFullScreenVideoActivity" android:theme="@style/tt_full_screen_new"/>
        <activity android:configChanges="keyboardHidden|orientation|screenSize" android:launchMode="standard" android:name="com.bytedance.sdk.openadsdk.activity.TTFullScreenExpressVideoActivity" android:theme="@style/tt_full_screen_new"/>
        <activity android:configChanges="keyboardHidden|orientation|screenSize" android:launchMode="standard" android:name="com.bytedance.sdk.openadsdk.activity.TTInterstitialActivity" android:theme="@style/tt_full_screen_interaction"/>
        <activity android:configChanges="keyboardHidden|orientation|screenSize" android:launchMode="standard" android:name="com.bytedance.sdk.openadsdk.activity.TTInterstitialExpressActivity" android:theme="@style/tt_full_screen_interaction"/>
        <uses-library android:name="android.ext.adservices" android:required="false"/>
        <service android:name="com.transsion.athena.aidl.AthenaTrackService"/>
        <meta-data android:name="com.google.android.gms.version" android:value="@integer/google_play_services_version"/>
        <receiver android:directBootAware="false" android:enabled="true" android:exported="true" android:name="androidx.profileinstaller.ProfileInstallReceiver" android:permission="android.permission.DUMP">
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE"/>
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SKIP_FILE"/>
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE"/>
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION"/>
            </intent-filter>
        </receiver>
        <service android:exported="false" android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery">
            <meta-data android:name="backend:com.google.android.datatransport.cct.CctBackendFactory" android:value="cct"/>
        </service>
        <service android:exported="false" android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService" android:permission="android.permission.BIND_JOB_SERVICE"/>
        <receiver android:exported="false" android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"/>
        <activity android:configChanges="keyboardHidden|orientation|screenSize|fontScale" android:launchMode="singleTop" android:name="com.transsion.search.activity.SearchManagerActivity" android:screenOrientation="behind"/>
        <receiver android:exported="true" android:name="com.transsion.player.mediasession.MusicIntentReceiver">
            <intent-filter>
                <action android:name="android.intent.action.MEDIA_BUTTON"/>
            </intent-filter>
        </receiver>
        <receiver android:exported="false" android:name="com.transsion.player.mediasession.MusicNotificationBroadcastReceiver"/>
        <service android:enabled="true" android:exported="true" android:foregroundServiceType="mediaPlayback" android:name="com.transsion.player.mediasession.MediaService"/>
        <service android:exported="false" android:foregroundServiceType="dataSync" android:name="com.transsion.player.exo.DemoDownloadService">
            <intent-filter>
                <action android:name="androidx.media3.exoplayer.downloadService.action.RESTART"/>
                <category android:name="android.intent.category.DEFAULT"/>
            </intent-filter>
        </service>
        <service android:exported="true" android:name="androidx.media3.exoplayer.scheduler.PlatformScheduler$PlatformSchedulerService" android:permission="android.permission.BIND_JOB_SERVICE"/>
        <meta-data android:name="com.bumptech.glide.integration.webp.WebpGlideModule" android:value="GlideModule"/>
        <provider android:authorities="com.community.oneroom.backgroundLibrary" android:exported="false" android:multiprocess="true" android:name="com.noober.background.BackgroundContentProvider"/>
        <activity android:clearTaskOnLaunch="true" android:name="com.journeyapps.barcodescanner.CaptureActivity" android:screenOrientation="sensorLandscape" android:stateNotNeeded="true" android:theme="@style/zxing_CaptureTheme" android:windowSoftInputMode="stateAlwaysHidden"/>
        <meta-data android:name="gateway_secret_test" android:value="Xqn2nnO41/L92o1iuXhSLHTbXvY4Z5ZZ62m8mSLA"/>
        <meta-data android:name="gateway_secret_online" android:value="76iRl07s0xSN9jqmEWAt79EBJZulIQIsV64FZr2O"/>
        <uses-library android:name="androidx.window.extensions" android:required="false"/>
        <uses-library android:name="androidx.window.sidecar" android:required="false"/>
        <activity android:exported="false" android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity" android:stateNotNeeded="true" android:theme="@style/Theme.PlayCore.Transparent"/>
        <activity android:configChanges="keyboardHidden|orientation|screenSize" android:excludeFromRecents="true" android:name="com.mbridge.msdk.reward.player.MBRewardVideoActivity" android:theme="@android:style/Theme.Translucent.NoTitleBar"/>
        <meta-data android:name="trans_crypto_sdk_version" android:value="1.0.9.01"/>
        <activity android:configChanges="keyboard|orientation" android:excludeFromRecents="true" android:exported="false" android:name="com.mbridge.msdk.activity.MBCommonActivity" android:theme="@style/mbridge_transparent_common_activity_style"/>
        <activity android:name="com.mbridge.msdk.out.LoadingActivity"/>
    </application>
</manifest>
