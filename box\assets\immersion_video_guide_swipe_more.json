{"v": "5.1.8", "fr": 25, "ip": 0, "op": 50, "w": 560, "h": 400, "nm": "Swipe up for more", "ddd": 0, "assets": [{"id": "comp_4", "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "“手”轮廓 2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.584], "y": [0]}, "n": ["0p667_1_0p584_0"], "t": 0, "s": [-58.506], "e": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 10, "s": [0], "e": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 30, "s": [0], "e": [-58.506]}, {"t": 45}], "ix": 10}, "p": {"a": 0, "k": [549, 226.5, 0], "ix": 2}, "a": {"a": 0, "k": [102.5, 106, 0], "ix": 1}, "s": {"a": 0, "k": [200, 200, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "n": "0p667_1_0p167_0", "t": 0, "s": [{"i": [[-4.548, 2.086], [-3.192, -4.536], [0, 0], [0, 0], [-1.328, -9.953], [0, 0], [0, 0], [4.69, -3.793], [6.027, -1.615], [5.247, 0.508], [2.485, 1.716], [0, 0], [0.575, 0.485], [0, 0], [-3.214, 3.843], [-3.837, -3.23], [0, 0], [1.861, 5.182], [0, 0]], "o": [[5.267, -2.415], [0, 0], [0, 0], [9.921, -1.314], [0, 0], [0, 0], [0.524, 6.025], [-5.179, 4.188], [-4.984, 1.336], [-3.138, -0.304], [0, 0], [-0.645, -0.318], [0, 0], [-3.836, -3.23], [3.213, -3.842], [0, 0], [0, 0], [-1.779, -4.955], [-1.927, -4.261]], "v": [[-33.664, -43.966], [-21.329, -38.869], [-8.573, -15.534], [17.907, -19.04], [38.274, -3.398], [38.366, -2.566], [40.444, 21.347], [33.78, 37.053], [16.97, 45.757], [1.624, 46.998], [-6.922, 43.873], [-7.002, 43.837], [-8.84, 42.634], [-36.627, 19.241], [-37.754, 6.435], [-24.989, 5.327], [-20.795, 8.86], [-25.069, -7.095], [-37.851, -32.581]], "c": true}], "e": [{"i": [[-4.833, 1.295], [-1.3, -4.85], [0, 0], [0, 0], [-1.328, -9.953], [0, 0], [0, 0], [4.69, -3.793], [6.027, -1.615], [5.247, 0.508], [2.485, 1.716], [0, 0], [0.575, 0.485], [0, 0], [-3.214, 3.843], [-3.837, -3.23], [0, 0], [0, 0], [0, 0]], "o": [[4.833, -1.295], [0, 0], [0, 0], [9.921, -1.314], [0, 0], [0, 0], [0.524, 6.025], [-5.179, 4.188], [-4.984, 1.336], [-3.138, -0.304], [0, 0], [-0.645, -0.318], [0, 0], [-3.836, -3.23], [3.213, -3.842], [0, 0], [0, 0], [0, 0], [-1.299, -4.85]], "v": [[-26.171, -46.211], [-15.067, -39.774], [-8.573, -15.534], [17.907, -19.04], [38.274, -3.398], [38.366, -2.566], [40.444, 21.347], [33.78, 37.053], [16.971, 45.757], [1.624, 46.998], [-6.922, 43.873], [-7.002, 43.837], [-8.84, 42.634], [-36.627, 19.241], [-37.754, 6.435], [-24.989, 5.327], [-20.795, 8.86], [-25.069, -7.095], [-32.569, -35.085]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 1, "y": 0}, "n": "0p667_1_1_0", "t": 5, "s": [{"i": [[-4.833, 1.295], [-1.3, -4.85], [0, 0], [0, 0], [-1.328, -9.953], [0, 0], [0, 0], [4.69, -3.793], [6.027, -1.615], [5.247, 0.508], [2.485, 1.716], [0, 0], [0.575, 0.485], [0, 0], [-3.214, 3.843], [-3.837, -3.23], [0, 0], [0, 0], [0, 0]], "o": [[4.833, -1.295], [0, 0], [0, 0], [9.921, -1.314], [0, 0], [0, 0], [0.524, 6.025], [-5.179, 4.188], [-4.984, 1.336], [-3.138, -0.304], [0, 0], [-0.645, -0.318], [0, 0], [-3.836, -3.23], [3.213, -3.842], [0, 0], [0, 0], [0, 0], [-1.299, -4.85]], "v": [[-26.171, -46.211], [-15.067, -39.774], [-8.573, -15.534], [17.907, -19.04], [38.274, -3.398], [38.366, -2.566], [40.444, 21.347], [33.78, 37.053], [16.971, 45.757], [1.624, 46.998], [-6.922, 43.873], [-7.002, 43.837], [-8.84, 42.634], [-36.627, 19.241], [-37.754, 6.435], [-24.989, 5.327], [-20.795, 8.86], [-25.069, -7.095], [-32.569, -35.085]], "c": true}], "e": [{"i": [[-4.833, 1.295], [-1.3, -4.85], [0, 0], [0, 0], [-1.328, -9.953], [0, 0], [0, 0], [4.69, -3.793], [6.027, -1.615], [5.247, 0.508], [2.485, 1.716], [0, 0], [0.575, 0.485], [0, 0], [-3.214, 3.843], [-3.837, -3.23], [0, 0], [0, 0], [0, 0]], "o": [[4.833, -1.295], [0, 0], [0, 0], [9.921, -1.314], [0, 0], [0, 0], [0.524, 6.025], [-5.179, 4.188], [-4.984, 1.336], [-3.138, -0.304], [0, 0], [-0.645, -0.318], [0, 0], [-3.836, -3.23], [3.213, -3.842], [0, 0], [0, 0], [0, 0], [-1.299, -4.85]], "v": [[-26.171, -46.211], [-15.067, -39.774], [-8.573, -15.534], [17.907, -19.04], [38.274, -3.398], [38.366, -2.566], [40.444, 21.347], [33.78, 37.053], [16.971, 45.757], [1.624, 46.998], [-6.922, 43.873], [-7.002, 43.837], [-8.84, 42.634], [-36.627, 19.241], [-37.754, 6.435], [-24.989, 5.327], [-20.795, 8.86], [-25.069, -7.095], [-32.569, -35.085]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.616, "y": 0}, "n": "0p667_1_0p616_0", "t": 10, "s": [{"i": [[-4.833, 1.295], [-1.3, -4.85], [0, 0], [0, 0], [-1.328, -9.953], [0, 0], [0, 0], [4.69, -3.793], [6.027, -1.615], [5.247, 0.508], [2.485, 1.716], [0, 0], [0.575, 0.485], [0, 0], [-3.214, 3.843], [-3.837, -3.23], [0, 0], [0, 0], [0, 0]], "o": [[4.833, -1.295], [0, 0], [0, 0], [9.921, -1.314], [0, 0], [0, 0], [0.524, 6.025], [-5.179, 4.188], [-4.984, 1.336], [-3.138, -0.304], [0, 0], [-0.645, -0.318], [0, 0], [-3.836, -3.23], [3.213, -3.842], [0, 0], [0, 0], [0, 0], [-1.299, -4.85]], "v": [[-26.171, -46.211], [-15.067, -39.774], [-8.573, -15.534], [17.907, -19.04], [38.274, -3.398], [38.366, -2.566], [40.444, 21.347], [33.78, 37.053], [16.971, 45.757], [1.624, 46.998], [-6.922, 43.873], [-7.002, 43.837], [-8.84, 42.634], [-36.627, 19.241], [-37.754, 6.435], [-24.989, 5.327], [-20.795, 8.86], [-25.069, -7.095], [-32.569, -35.085]], "c": true}], "e": [{"i": [[-4.833, 1.295], [-1.3, -4.85], [0, 0], [0, 0], [-1.328, -9.953], [0, 0], [0, 0], [4.69, -3.793], [6.027, -1.615], [5.247, 0.508], [2.485, 1.716], [0, 0], [0.575, 0.485], [0, 0], [-3.214, 3.843], [-3.837, -3.23], [0, 0], [0, 0], [0, 0]], "o": [[4.833, -1.295], [0, 0], [0, 0], [9.921, -1.314], [0, 0], [0, 0], [0.524, 6.025], [-5.179, 4.188], [-4.984, 1.336], [-3.138, -0.304], [0, 0], [-0.645, -0.318], [0, 0], [-3.836, -3.23], [3.213, -3.842], [0, 0], [0, 0], [0, 0], [-1.299, -4.85]], "v": [[-26.171, -46.211], [-15.067, -39.774], [-8.573, -15.534], [17.907, -19.04], [38.274, -3.398], [38.366, -2.566], [40.444, 21.347], [33.78, 37.053], [16.971, 45.757], [1.624, 46.998], [-6.922, 43.873], [-7.002, 43.837], [-8.84, 42.634], [-36.627, 19.241], [-37.754, 6.435], [-24.989, 5.327], [-20.795, 8.86], [-25.069, -7.095], [-32.569, -35.085]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 1, "y": 0}, "n": "0p667_1_1_0", "t": 30, "s": [{"i": [[-4.833, 1.295], [-1.3, -4.85], [0, 0], [0, 0], [-1.328, -9.953], [0, 0], [0, 0], [4.69, -3.793], [6.027, -1.615], [5.247, 0.508], [2.485, 1.716], [0, 0], [0.575, 0.485], [0, 0], [-3.214, 3.843], [-3.837, -3.23], [0, 0], [0, 0], [0, 0]], "o": [[4.833, -1.295], [0, 0], [0, 0], [9.921, -1.314], [0, 0], [0, 0], [0.524, 6.025], [-5.179, 4.188], [-4.984, 1.336], [-3.138, -0.304], [0, 0], [-0.645, -0.318], [0, 0], [-3.836, -3.23], [3.213, -3.842], [0, 0], [0, 0], [0, 0], [-1.299, -4.85]], "v": [[-26.171, -46.211], [-15.067, -39.774], [-8.573, -15.534], [17.907, -19.04], [38.274, -3.398], [38.366, -2.566], [40.444, 21.347], [33.78, 37.053], [16.971, 45.757], [1.624, 46.998], [-6.922, 43.873], [-7.002, 43.837], [-8.84, 42.634], [-36.627, 19.241], [-37.754, 6.435], [-24.989, 5.327], [-20.795, 8.86], [-25.069, -7.095], [-32.569, -35.085]], "c": true}], "e": [{"i": [[-8.041, 0.537], [-1.092, -6.578], [0, 0], [0, 0], [-1.328, -9.953], [0, 0], [0, 0], [4.69, -3.793], [6.027, -1.615], [5.247, 0.508], [2.485, 1.716], [0, 0], [0.575, 0.485], [0, 0], [-3.214, 3.843], [-3.837, -3.23], [0, 0], [0.997, 5.415], [0.195, 3]], "o": [[6.71, -0.448], [1.436, 8.647], [0, 0], [9.921, -1.314], [0, 0], [0, 0], [0.524, 6.025], [-5.179, 4.188], [-4.984, 1.336], [-3.138, -0.304], [0, 0], [-0.645, -0.318], [0, 0], [-3.836, -3.23], [3.213, -3.842], [0, 0], [0, 0], [-0.833, -2.863], [-0.346, -5.329]], "v": [[-21.424, -47.063], [-12.693, -34.436], [-8.573, -15.534], [17.907, -19.04], [38.274, -3.398], [38.366, -2.566], [40.444, 21.347], [33.78, 37.053], [16.971, 45.757], [1.624, 46.998], [-6.922, 43.873], [-7.002, 43.837], [-8.84, 42.634], [-36.627, 19.241], [-37.754, 6.435], [-24.989, 5.327], [-20.795, 8.86], [-25.524, -10.197], [-29.012, -35.858]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 1, "y": 0}, "n": "0p667_1_1_0", "t": 35, "s": [{"i": [[-8.041, 0.537], [-1.092, -6.578], [0, 0], [0, 0], [-1.328, -9.953], [0, 0], [0, 0], [4.69, -3.793], [6.027, -1.615], [5.247, 0.508], [2.485, 1.716], [0, 0], [0.575, 0.485], [0, 0], [-3.214, 3.843], [-3.837, -3.23], [0, 0], [0.997, 5.415], [0.195, 3]], "o": [[6.71, -0.448], [1.436, 8.647], [0, 0], [9.921, -1.314], [0, 0], [0, 0], [0.524, 6.025], [-5.179, 4.188], [-4.984, 1.336], [-3.138, -0.304], [0, 0], [-0.645, -0.318], [0, 0], [-3.836, -3.23], [3.213, -3.842], [0, 0], [0, 0], [-0.833, -2.863], [-0.346, -5.329]], "v": [[-21.424, -47.063], [-12.693, -34.436], [-8.573, -15.534], [17.907, -19.04], [38.274, -3.398], [38.366, -2.566], [40.444, 21.347], [33.78, 37.053], [16.971, 45.757], [1.624, 46.998], [-6.922, 43.873], [-7.002, 43.837], [-8.84, 42.634], [-36.627, 19.241], [-37.754, 6.435], [-24.989, 5.327], [-20.795, 8.86], [-25.524, -10.197], [-29.012, -35.858]], "c": true}], "e": [{"i": [[-4.833, 1.295], [-1.3, -4.85], [0, 0], [0, 0], [-1.328, -9.953], [0, 0], [0, 0], [4.69, -3.793], [6.027, -1.615], [5.247, 0.508], [2.485, 1.716], [0, 0], [0.575, 0.485], [0, 0], [-3.214, 3.843], [-3.837, -3.23], [0, 0], [0, 0], [0, 0]], "o": [[4.833, -1.295], [0, 0], [0, 0], [9.921, -1.314], [0, 0], [0, 0], [0.524, 6.025], [-5.179, 4.188], [-4.984, 1.336], [-3.138, -0.304], [0, 0], [-0.645, -0.318], [0, 0], [-3.836, -3.23], [3.213, -3.842], [0, 0], [0, 0], [0, 0], [-1.299, -4.85]], "v": [[-26.171, -46.211], [-15.067, -39.774], [-8.573, -15.534], [17.907, -19.04], [38.274, -3.398], [38.366, -2.566], [40.444, 21.347], [33.78, 37.053], [16.971, 45.757], [1.624, 46.998], [-6.922, 43.873], [-7.002, 43.837], [-8.84, 42.634], [-36.627, 19.241], [-37.754, 6.435], [-24.989, 5.327], [-20.795, 8.86], [-25.069, -7.095], [-32.569, -35.085]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 1, "y": 0}, "n": "0p833_1_1_0", "t": 40, "s": [{"i": [[-4.833, 1.295], [-1.3, -4.85], [0, 0], [0, 0], [-1.328, -9.953], [0, 0], [0, 0], [4.69, -3.793], [6.027, -1.615], [5.247, 0.508], [2.485, 1.716], [0, 0], [0.575, 0.485], [0, 0], [-3.214, 3.843], [-3.837, -3.23], [0, 0], [0, 0], [0, 0]], "o": [[4.833, -1.295], [0, 0], [0, 0], [9.921, -1.314], [0, 0], [0, 0], [0.524, 6.025], [-5.179, 4.188], [-4.984, 1.336], [-3.138, -0.304], [0, 0], [-0.645, -0.318], [0, 0], [-3.836, -3.23], [3.213, -3.842], [0, 0], [0, 0], [0, 0], [-1.299, -4.85]], "v": [[-26.171, -46.211], [-15.067, -39.774], [-8.573, -15.534], [17.907, -19.04], [38.274, -3.398], [38.366, -2.566], [40.444, 21.347], [33.78, 37.053], [16.971, 45.757], [1.624, 46.998], [-6.922, 43.873], [-7.002, 43.837], [-8.84, 42.634], [-36.627, 19.241], [-37.754, 6.435], [-24.989, 5.327], [-20.795, 8.86], [-25.069, -7.095], [-32.569, -35.085]], "c": true}], "e": [{"i": [[-4.461, 2.266], [-4.115, -5.803], [0, 0], [0, 0], [-1.328, -9.953], [0, 0], [0, 0], [4.69, -3.793], [6.027, -1.615], [5.247, 0.508], [2.485, 1.716], [0, 0], [0.575, 0.485], [0, 0], [-3.214, 3.843], [-3.837, -3.23], [0, 0], [1.861, 5.182], [0, 0]], "o": [[4.251, -2.159], [0, 0], [0, 0], [9.921, -1.314], [0, 0], [0, 0], [0.524, 6.025], [-5.179, 4.188], [-4.984, 1.336], [-3.138, -0.304], [0, 0], [-0.645, -0.318], [0, 0], [-3.836, -3.23], [3.213, -3.842], [0, 0], [0, 0], [-1.779, -4.955], [-1.927, -4.261]], "v": [[-33.664, -43.966], [-21.118, -38.046], [-8.573, -15.534], [17.907, -19.04], [38.274, -3.398], [38.366, -2.566], [40.444, 21.347], [33.78, 37.053], [16.97, 45.757], [1.624, 46.998], [-6.922, 43.873], [-7.002, 43.837], [-8.84, 42.634], [-36.627, 19.241], [-37.754, 6.435], [-24.989, 5.327], [-20.795, 8.86], [-25.069, -7.095], [-37.851, -32.581]], "c": true}]}, {"t": 45}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "合并路径 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [41.217, 47.756], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 3, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 150, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 0, "nm": "“播放按钮1/Swipe up for more”轮廓 合成 1", "refId": "comp_5", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [280, 200, 0], "ix": 2}, "a": {"a": 0, "k": [280, 200, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "hasMask": true, "masksProperties": [{"inv": false, "mode": "a", "pt": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[364.094, 52.875], [195.914, 52.891], [195.93, 340.156], [364.11, 340.141]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "蒙版 1"}], "w": 560, "h": 400, "ip": 0, "op": 150, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "“屏幕”轮廓", "sr": 1, "ks": {"o": {"a": 0, "k": 20, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [280, 197, 0], "ix": 2}, "a": {"a": 0, "k": [42, 72, 0], "ix": 1}, "s": {"a": 0, "k": [200, 200, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-2.209, 0], [0, 0], [0, -2.209], [0, 0], [2.209, 0], [0, 0], [0, 2.209], [0, 0]], "o": [[0, 0], [2.209, 0], [0, 0], [0, 2.209], [0, 0], [-2.209, 0], [0, 0], [0, -2.209]], "v": [[-38, -72], [38, -72], [42, -68], [42, 68], [38, 72], [-38, 72], [-42, 68], [-42, -68]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "合并路径 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [42, 72], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 3, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 150, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "“手机”轮廓", "sr": 1, "ks": {"o": {"a": 0, "k": 80, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [280, 203, 0], "ix": 2}, "a": {"a": 0, "k": [45, 85, 0], "ix": 1}, "s": {"a": 0, "k": [200, 200, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-4.418, 0], [0, 0], [0, -4.418], [0, 0], [4.418, 0], [0, 0], [0, 4.418], [0, 0]], "o": [[0, 0], [4.418, 0], [0, 0], [0, 4.418], [0, 0], [-4.418, 0], [0, 0], [0, -4.418]], "v": [[-37, -85], [37, -85], [45, -77], [45, 77], [37, 85], [-37, 85], [-45, 77], [-45, -77]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "合并路径 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [45, 85], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 3, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 150, "st": 0, "bm": 0}]}, {"id": "comp_5", "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "“播放按钮1/Swipe up for more”轮廓", "sr": 1, "ks": {"o": {"a": 0, "k": 80, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "n": "0p667_1_0p333_0", "t": 5, "s": [280, 343, 0], "e": [280, 53, 0], "to": [0, -48.3333320617676, 0], "ti": [0, 48.3333320617676, 0]}, {"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.333, "y": 0.333}, "n": "0p667_0p667_0p333_0p333", "t": 15, "s": [280, 53, 0], "e": [280, 53, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 30}], "ix": 2}, "a": {"a": 0, "k": [42.5, 93, 0], "ix": 1}, "s": {"a": 0, "k": [200, 200, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[1.964, 1.2], [0, 0], [0.766, 0], [0, -2.301], [0, 0], [-0.4, -0.654], [-1.963, 1.2], [0, 0], [-0.345, 0.564]], "o": [[0, 0], [-0.654, -0.4], [-2.301, 0], [0, 0], [0, 0.767], [1.2, 1.964], [0, 0], [0.564, -0.344], [1.2, -1.964]], "v": [[14.549, -3.85], [-10.794, -19.337], [-12.965, -19.949], [-17.132, -15.781], [-17.132, 15.193], [-16.521, 17.366], [-10.794, 18.749], [14.549, 3.261], [15.932, 1.879]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "合并路径 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [44.799, 165.198], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 3, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-42, 0.5], [42, 0.5], [42, -0.5], [-42, -0.5]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "合并路径 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [42.25, 92.404], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 2", "np": 3, "cix": 2, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-1.963, 1.2], [0, 0], [-0.345, 0.564], [1.964, 1.2], [0, 0], [0.766, 0], [0, -2.301], [0, 0], [-0.4, -0.655]], "o": [[0, 0], [0.564, -0.345], [1.2, -1.963], [0, 0], [-0.654, -0.4], [-2.302, 0], [0, 0], [0, 0.766], [1.2, 1.963]], "v": [[-10.794, 18.748], [14.549, 3.261], [15.933, 1.878], [14.549, -3.85], [-10.794, -19.337], [-12.966, -19.948], [-17.133, -15.782], [-17.133, 15.193], [-16.521, 17.366]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "合并路径 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [44.799, 20.198], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 3", "np": 3, "cix": 2, "ix": 3, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 150, "st": 0, "bm": 0}]}], "layers": [{"ddd": 0, "ind": 1, "ty": 0, "nm": "预合成 1", "refId": "comp_4", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [280, 200, 0], "ix": 2}, "a": {"a": 0, "k": [280, 200, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 560, "h": 400, "ip": 0, "op": 150, "st": 0, "bm": 0}], "markers": []}