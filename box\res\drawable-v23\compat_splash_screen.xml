<?xml version="1.0" encoding="utf-8"?>
<layer-list
  xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:gravity="fill">
        <color android:color="?windowSplashScreenBackground" />
    </item>
    <item android:gravity="center" android:height="@dimen/splashscreen_icon_size_with_background" android:width="@dimen/splashscreen_icon_size_with_background" android:drawable="@drawable/icon_background" />
    <item android:gravity="center" android:height="@dimen/splashscreen_icon_size_with_background" android:width="@dimen/splashscreen_icon_size_with_background" android:drawable="?windowSplashScreenAnimatedIcon" />
    <item android:gravity="center" android:height="@dimen/splashscreen_icon_mask_size_with_background" android:width="@dimen/splashscreen_icon_mask_size_with_background">
        <shape android:shape="oval">
            <solid android:color="@android:color/transparent" />
            <stroke android:width="@dimen/splashscreen_icon_mask_stroke_with_background" android:color="?windowSplashScreenBackground" />
        </shape>
    </item>
</layer-list>
