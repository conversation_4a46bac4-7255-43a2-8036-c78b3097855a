<?xml version="1.0" encoding="utf-8"?>
<layer-list
  xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:gravity="fill_horizontal|center" android:id="@android:id/background">
        <shape android:shape="rectangle">
            <size android:height="@dimen/update_progress_height" />
            <solid android:color="@color/update_progress_background_bar_color" />
            <corners android:radius="@dimen/update_progress_height" />
        </shape>
    </item>
    <item android:gravity="fill_horizontal|center" android:id="@android:id/secondaryProgress">
        <scale android:scaleWidth="100%">
            <shape android:shape="rectangle">
                <size android:height="@dimen/update_progress_height" />
                <solid android:color="@color/update_fill_secondary_color" />
                <corners android:radius="@dimen/update_progress_height" />
            </shape>
        </scale>
    </item>
    <item android:gravity="fill_horizontal|center" android:id="@android:id/progress">
        <scale android:scaleWidth="100%">
            <shape android:shape="rectangle">
                <size android:height="@dimen/update_progress_height" />
                <corners android:radius="@dimen/update_progress_height" />
                <solid android:color="@color/update_progress_bar_color" />
            </shape>
        </scale>
    </item>
</layer-list>
