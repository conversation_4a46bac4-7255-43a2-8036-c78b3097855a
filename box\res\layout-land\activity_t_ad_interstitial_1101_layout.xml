<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:gravity="center" android:orientation="horizontal" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <FrameLayout android:layout_width="220.0dip" android:layout_height="220.0dip">
        <FrameLayout android:id="@id/main_layout" android:layout_width="fill_parent" android:layout_height="fill_parent">
            <com.cloud.sdk.commonutil.widget.TranCircleImageView android:id="@id/iv_main_image" android:layout_width="fill_parent" android:layout_height="fill_parent" android:adjustViewBounds="true" app:bottomLeftRadiusYL="8.0dip" app:topLeftRadiusYL="8.0dip" />
            <com.cloud.hisavana.sdk.api.view.AdDisclaimerView android:layout_gravity="bottom" android:id="@id/ad_disclaimer_view" android:layout_width="fill_parent" android:layout_height="@dimen/ad_disclaimer_height" />
        </FrameLayout>
        <include android:layout_gravity="start|top" android:id="@id/ad_flag" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="8.0dip" android:layout_marginStart="8.0dip" android:layout_marginEnd="8.0dip" layout="@layout/include_ad_flag" />
        <ImageView android:layout_gravity="start|bottom" android:id="@id/im_volume" android:visibility="gone" android:layout_width="25.0dip" android:layout_height="25.0dip" android:layout_marginBottom="8.0dip" android:src="@drawable/hisavana_volume_close" android:layout_marginStart="8.0dip" />
    </FrameLayout>
    <FrameLayout android:layout_width="220.0dip" android:layout_height="220.0dip">
        <LinearLayout android:orientation="vertical" android:id="@id/llRoot" android:background="@drawable/ssp_bg_ffffff_0_8_0_8" android:visibility="visible" android:layout_width="fill_parent" android:layout_height="fill_parent">
            <com.cloud.sdk.commonutil.widget.TranCircleImageView android:layout_gravity="center" android:id="@id/ivIcon" android:layout_width="44.0dip" android:layout_height="44.0dip" android:layout_marginTop="41.0dip" app:bottomLeftRadiusYL="4.0dip" app:bottomRightRadiusYL="4.0dip" app:topLeftRadiusYL="4.0dip" app:topRightRadiusYL="4.0dip" />
            <TextView android:textSize="14.0sp" android:textColor="#ff222222" android:ellipsize="end" android:layout_gravity="center" android:id="@id/tvName" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="8.0dip" android:text="" android:lines="1" />
            <TextView android:textSize="11.0sp" android:textColor="#ff787878" android:ellipsize="end" android:gravity="center" android:layout_gravity="center" android:id="@id/tvDescription" android:visibility="visible" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginLeft="24.0dip" android:layout_marginTop="4.0dip" android:layout_marginRight="24.0dip" android:text="" android:maxLines="2" android:lineSpacingExtra="4.0dip" />
            <TextView android:textStyle="bold" android:textColor="#ffffffff" android:ellipsize="end" android:gravity="center" android:layout_gravity="center" android:id="@id/tvBtn" android:background="@drawable/ssp_bg_0052e2_4_4_4_4" android:layout_width="160.0dip" android:layout_height="32.0dip" android:layout_marginTop="13.0dip" android:text="" android:lines="1" />
            <com.cloud.hisavana.sdk.api.view.StoreMarkView android:layout_gravity="center" android:id="@id/ps_mark_view" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="10.0dip" />
        </LinearLayout>
        <ImageView android:layout_gravity="end|top" android:id="@id/ivCancel" android:layout_width="24.0dip" android:layout_height="24.0dip" android:layout_marginTop="8.0dip" android:src="@drawable/ssp_sdk_cancel" android:layout_marginEnd="8.0dip" />
    </FrameLayout>
</LinearLayout>
