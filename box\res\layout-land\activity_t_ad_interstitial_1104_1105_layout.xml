<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:gravity="center" android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <FrameLayout android:layout_width="222.0dip" android:layout_height="148.0dip" android:layout_marginTop="12.0dip">
        <FrameLayout android:id="@id/main_layout" android:layout_width="fill_parent" android:layout_height="fill_parent">
            <com.cloud.sdk.commonutil.widget.TranCircleImageView android:id="@id/iv_main_image" android:layout_width="fill_parent" android:layout_height="fill_parent" android:adjustViewBounds="true" app:topLeftRadiusYL="8.0dip" app:topRightRadiusYL="8.0dip" />
            <com.cloud.hisavana.sdk.api.view.AdDisclaimerView android:layout_gravity="bottom" android:id="@id/ad_disclaimer_view" android:layout_width="fill_parent" android:layout_height="@dimen/ad_disclaimer_height" />
        </FrameLayout>
        <include android:layout_gravity="start|top" android:id="@id/ad_flag" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="8.0dip" android:layout_marginStart="8.0dip" android:layout_marginEnd="8.0dip" layout="@layout/include_ad_flag" />
        <ImageView android:layout_gravity="start|bottom" android:id="@id/im_volume" android:visibility="gone" android:layout_width="24.0dip" android:layout_height="24.0dip" android:layout_marginBottom="8.0dip" android:src="@drawable/hisavana_volume_close" android:layout_marginStart="8.0dip" />
    </FrameLayout>
    <LinearLayout android:orientation="vertical" android:id="@id/llRoot" android:background="@drawable/ssp_bg_ffffff_0_0_8_8" android:visibility="visible" android:layout_width="222.0dip" android:layout_height="124.0dip" app:layout_constraintBottom_toTopOf="@id/ivCancel" app:layout_constraintEnd_toEndOf="@id/main_layout" app:layout_constraintHorizontal_bias="0.0" app:layout_constraintStart_toStartOf="@id/main_layout" app:layout_constraintTop_toBottomOf="@id/main_layout">
        <LinearLayout android:layout_gravity="center" android:orientation="horizontal" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginLeft="24.0dip" android:layout_marginTop="16.0dip" android:layout_marginRight="24.0dip">
            <com.cloud.sdk.commonutil.widget.TranCircleImageView android:id="@id/ivIcon" android:visibility="visible" android:layout_width="11.0dip" android:layout_height="11.0dip" app:bottomLeftRadiusYL="2.0dip" app:bottomRightRadiusYL="2.0dip" app:topLeftRadiusYL="2.0dip" app:topRightRadiusYL="2.0dip" />
            <TextView android:textSize="11.0sp" android:textColor="#ff787878" android:ellipsize="end" android:layout_gravity="center" android:id="@id/tvName" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="" android:lines="1" android:layout_marginStart="4.0dip" />
        </LinearLayout>
        <TextView android:textSize="11.0sp" android:textColor="#ff222222" android:ellipsize="end" android:gravity="center" android:layout_gravity="center" android:id="@id/tvDescription" android:visibility="visible" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginLeft="24.0dip" android:layout_marginTop="8.0dip" android:layout_marginRight="24.0dip" android:text="" android:maxLines="2" android:lineSpacingExtra="5.0dip" />
        <TextView android:textStyle="bold" android:textColor="#ffffffff" android:ellipsize="end" android:gravity="center" android:layout_gravity="center" android:id="@id/tvBtn" android:background="@drawable/ssp_bg_0052e2_4_4_4_4" android:layout_width="149.0dip" android:layout_height="25.0dip" android:layout_marginTop="12.0dip" android:layout_marginBottom="6.0dip" android:text="" android:lines="1" />
        <com.cloud.hisavana.sdk.api.view.StoreMarkView android:layout_gravity="center" android:id="@id/ps_mark_view" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="4.0dip" />
    </LinearLayout>
    <ImageView android:id="@id/ivCancel" android:layout_width="30.0dip" android:layout_height="30.0dip" android:layout_marginTop="12.0dip" android:src="@drawable/ssp_sdk_cancel" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="@id/main_layout" app:layout_constraintStart_toStartOf="@id/main_layout" app:layout_constraintTop_toBottomOf="@id/llRoot" />
</LinearLayout>
