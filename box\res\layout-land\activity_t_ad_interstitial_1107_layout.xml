<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:gravity="center" android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <FrameLayout android:id="@id/llRoot" android:layout_width="140.0dip" android:layout_height="250.0dip">
        <FrameLayout android:id="@id/main_layout" android:layout_width="fill_parent" android:layout_height="fill_parent">
            <com.cloud.sdk.commonutil.widget.TranCircleImageView android:id="@id/iv_main_image" android:layout_width="fill_parent" android:layout_height="fill_parent" android:adjustViewBounds="true" app:bottomLeftRadiusYL="8.0dip" app:bottomRightRadiusYL="8.0dip" app:topLeftRadiusYL="8.0dip" app:topRightRadiusYL="8.0dip" />
            <com.cloud.hisavana.sdk.api.view.AdDisclaimerView android:layout_gravity="bottom" android:id="@id/ad_disclaimer_view" android:layout_width="fill_parent" android:layout_height="@dimen/ad_disclaimer_height" />
        </FrameLayout>
        <include android:layout_gravity="start|top" android:id="@id/ad_flag" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="8.0dip" android:layout_marginStart="8.0dip" android:layout_marginEnd="8.0dip" layout="@layout/include_ad_flag" />
        <com.cloud.hisavana.sdk.api.view.StoreMarkView android:id="@id/ps_mark_view" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="25.0dip" android:layout_marginStart="@dimen/hisavana_ad_dimen_4" />
        <ImageView android:layout_gravity="start|bottom" android:id="@id/im_volume" android:visibility="gone" android:layout_width="24.0dip" android:layout_height="24.0dip" android:layout_marginBottom="8.0dip" android:src="@drawable/hisavana_volume_close" android:layout_marginStart="8.0dip" />
    </FrameLayout>
    <ImageView android:id="@id/ivCancel" android:layout_width="32.0dip" android:layout_height="32.0dip" android:layout_marginTop="16.0dip" android:layout_marginBottom="24.0dip" android:src="@drawable/ssp_sdk_cancel" />
</LinearLayout>
