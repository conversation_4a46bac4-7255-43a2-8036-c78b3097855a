<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.constraintlayout.widget.Guideline android:orientation="horizontal" android:id="@id/topGuideline" android:layout_width="wrap_content" android:layout_height="wrap_content" app:layout_constraintGuide_begin="20.0dip" />
    <androidx.constraintlayout.widget.Guideline android:orientation="horizontal" android:id="@id/bottomGuideline" android:layout_width="wrap_content" android:layout_height="wrap_content" app:layout_constraintGuide_end="60.0dip" />
    <androidx.constraintlayout.widget.Barrier android:id="@id/leftBarrier" android:layout_width="wrap_content" android:layout_height="wrap_content" app:barrierDirection="end" app:constraint_referenced_ids="iv" />
    <androidx.constraintlayout.widget.Barrier android:id="@id/rightBarrier" android:layout_width="wrap_content" android:layout_height="wrap_content" app:barrierDirection="start" app:constraint_referenced_ids="iv" />
    <com.google.android.material.imageview.ShapeableImageView android:id="@id/iv" android:layout_width="wrap_content" android:layout_height="wrap_content" android:scaleType="fitCenter" app:layout_constraintBottom_toTopOf="@id/bottomGuideline" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/topGuideline" app:layout_constraintWidth_max="240.0dip" app:layout_constraintWidth_min="0.0dip" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/ivClose" android:layout_width="28.0dip" android:layout_height="28.0dip" android:layout_marginTop="20.0dip" android:layout_marginBottom="12.0dip" android:src="@mipmap/co_close" android:scaleType="fitCenter" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>
