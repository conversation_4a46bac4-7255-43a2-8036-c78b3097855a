<?xml version="1.0" encoding="utf-8"?>
<merge android:id="@id/material_timepicker_container" android:layout_width="wrap_content" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.constraintlayout.helper.widget.Flow android:orientation="vertical" android:id="@id/material_clock_display_and_toggle" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginTop="20.0dip" app:constraint_referenced_ids="material_clock_display,material_clock_period_toggle" app:flow_verticalGap="@dimen/material_clock_period_toggle_vertical_gap" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <include android:id="@id/material_clock_display" android:layout_width="wrap_content" android:layout_height="wrap_content" layout="@layout/material_clock_display" />
    <include android:id="@id/material_clock_period_toggle" android:layout_width="0.0dip" android:layout_height="wrap_content" layout="@layout/material_clock_period_toggle_land" />
    <com.google.android.material.timepicker.ClockFaceView android:id="@id/material_clock_face" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginStart="@dimen/clock_face_margin_start" app:layout_constraintStart_toEndOf="@id/material_clock_display_and_toggle" app:layout_constraintTop_toTopOf="parent" />
</merge>
