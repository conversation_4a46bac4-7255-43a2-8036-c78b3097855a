<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:paddingTop="12.0dip" android:layout_width="fill_parent" android:layout_height="wrap_content" android:paddingStart="@dimen/dimens_12"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <RelativeLayout android:id="@id/search_hot_rank_item_image_linear" android:layout_width="100.0dip" android:layout_height="140.0dip" android:paddingStart="0.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent">
        <com.google.android.material.imageview.ShapeableImageView android:id="@id/search_hot_rank_item_image" android:layout_width="fill_parent" android:layout_height="fill_parent" android:scaleType="centerCrop" app:shapeAppearance="@style/roundStyle_4" />
        <TextView android:textSize="13.0sp" android:textColor="@color/white_80" android:gravity="center" android:id="@id/search_hot_rank_item_corner" android:background="@drawable/bg_bottom_gray" android:paddingTop="2.0dip" android:paddingBottom="2.0dip" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_alignParentBottom="true" android:paddingVertical="2.0dip" />
        <androidx.appcompat.widget.AppCompatTextView android:textSize="14.0sp" android:textColor="@color/white" android:gravity="center" android:id="@id/search_hot_rank_item_index_text" android:background="@drawable/bg_hot_subject_no4" android:layout_width="28.0dip" android:layout_height="28.0dip" android:includeFontPadding="false" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" style="@style/style_medium_small_text" />
    </RelativeLayout>
    <androidx.appcompat.widget.AppCompatTextView android:textSize="18.0sp" android:textStyle="bold" android:textColor="@color/text_01" android:ellipsize="end" android:id="@id/search_hot_rank_item_title_text" android:layout_width="0.0dip" android:maxLines="2" android:layout_marginStart="@dimen/dp_8" app:layout_constraintBottom_toTopOf="@id/search_hot_rank_item_subtitle_text" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/search_hot_rank_item_image_linear" app:layout_constraintTop_toTopOf="@id/search_hot_rank_item_image_linear" app:layout_constraintVertical_chainStyle="packed" style="@style/style_regula_bigger_text" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="14.0sp" android:textColor="#ff92969e" android:ellipsize="end" android:id="@id/search_hot_rank_item_subtitle_text" android:layout_width="0.0dip" android:layout_marginTop="@dimen/dp_8" android:maxLines="1" android:layout_marginStart="@dimen/dp_8" app:layout_constraintBottom_toBottomOf="@id/search_hot_rank_item_image_linear" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/search_hot_rank_item_image_linear" app:layout_constraintTop_toBottomOf="@id/search_hot_rank_item_title_text" style="@style/style_regular_text" />
</androidx.constraintlayout.widget.ConstraintLayout>
