<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="12.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.google.android.material.imageview.ShapeableImageView android:id="@id/search_result_provider_group_cover" android:layout_width="100.0dip" android:layout_height="100.0dip" android:scaleType="centerCrop" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:shapeAppearance="@style/roundStyle_4" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="14.0sp" android:textColor="@color/white" android:gravity="center" android:id="@id/search_result_provider_group_index_text" android:background="@drawable/bg_hot_subject_no4" android:visibility="gone" android:layout_width="20.0dip" android:layout_height="20.0dip" android:includeFontPadding="false" app:layout_constraintStart_toStartOf="@id/search_result_provider_group_cover" app:layout_constraintTop_toTopOf="@id/search_result_provider_group_cover" style="@style/style_medium_small_text" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/search_result_provider_group_arrow" android:layout_width="24.0dip" android:layout_height="24.0dip" android:src="@drawable/ic_result_arrow" app:layout_constraintBottom_toBottomOf="@id/search_result_provider_group_cover" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="@id/search_result_provider_group_cover" />
    <LinearLayout android:orientation="vertical" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginLeft="8.0dip" android:layout_marginRight="8.0dip" android:layout_marginHorizontal="8.0dip" app:layout_constraintBottom_toBottomOf="@id/search_result_provider_group_cover" app:layout_constraintEnd_toStartOf="@id/search_result_provider_group_arrow" app:layout_constraintStart_toEndOf="@id/search_result_provider_group_cover" app:layout_constraintTop_toTopOf="@id/search_result_provider_group_cover">
        <androidx.appcompat.widget.AppCompatTextView android:textSize="@dimen/text_size_14" android:textColor="@color/white" android:ellipsize="end" android:id="@id/search_result_provider_group_title" android:layout_width="fill_parent" android:layout_height="wrap_content" android:maxLines="1" style="@style/style_medium_text" />
        <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/white_60" android:ellipsize="end" android:gravity="center_vertical" android:id="@id/search_result_provider_group_member" android:layout_width="fill_parent" android:layout_height="wrap_content" android:maxLines="1" android:drawablePadding="6.0dip" android:drawableStart="@drawable/search_suggest_staff" style="@style/style_regular_text" />
        <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/white_60" android:ellipsize="end" android:id="@id/search_result_provider_group_des" android:layout_width="fill_parent" android:layout_height="wrap_content" android:maxLines="1" style="@style/style_regular_text" />
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
