<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.coordinatorlayout.widget.CoordinatorLayout android:id="@id/cdl" android:layout_width="fill_parent" android:layout_height="fill_parent">
        <com.google.android.material.appbar.AppBarLayout android:orientation="vertical" android:id="@id/appBar" android:layout_width="fill_parent" android:layout_height="wrap_content">
            <com.google.android.material.appbar.CollapsingToolbarLayout android:id="@id/toolbar_layout" android:layout_width="fill_parent" android:layout_height="wrap_content" app:contentScrim="@color/transparent" app:layout_scrollFlags="scroll|exitUntilCollapsed">
                <androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="wrap_content">
                    <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_staff" android:background="@color/movie_staff_avatar_bg" android:layout_width="fill_parent" android:layout_height="0.0dip" android:scaleType="centerCrop" app:layout_constraintDimensionRatio="h,480:480" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
                    <View android:background="@drawable/bg_movie_detail_toolbar" android:layout_width="fill_parent" android:layout_height="88.0dip" app:layout_constraintTop_toTopOf="parent" />
                    <View android:background="@drawable/bg_movie_staff_name" android:layout_width="fill_parent" android:layout_height="144.0dip" app:layout_constraintBottom_toBottomOf="@id/iv_staff" />
                    <androidx.appcompat.widget.AppCompatTextView android:textSize="18.0sp" android:textColor="@color/white" android:ellipsize="end" android:gravity="start" android:id="@id/tv_staff_name" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginBottom="57.0dip" android:maxLines="1" android:singleLine="true" android:shadowColor="@color/black_30" android:shadowDx="0.0" android:shadowDy="1.0" android:shadowRadius="3.0" android:layout_marginStart="12.0dip" android:layout_marginEnd="32.0dip" app:layout_constraintBottom_toBottomOf="@id/iv_staff" app:layout_constraintEnd_toStartOf="@id/iv_want_see" app:layout_constraintHorizontal_chainStyle="spread_inside" app:layout_constraintStart_toStartOf="parent" app:layout_constraintWidth_default="wrap" style="@style/style_import_text" />
                    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/white_60" android:ellipsize="end" android:id="@id/tv_staff_desc" android:layout_width="0.0dip" android:layout_marginTop="4.0dip" android:maxLines="2" android:lineSpacingExtra="2.0dip" android:layout_marginStart="12.0dip" android:layout_marginEnd="32.0dip" app:layout_constraintEnd_toStartOf="@id/sp" app:layout_constraintHorizontal_chainStyle="spread_inside" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tv_staff_name" app:layout_constraintWidth_default="wrap" style="@style/style_regular_text" />
                    <Space android:id="@id/sp" android:visibility="visible" android:layout_width="24.0dip" android:layout_height="24.0dip" android:layout_marginLeft="16.0dip" android:layout_marginRight="16.0dip" android:layout_marginBottom="49.0dip" app:layout_constraintBottom_toBottomOf="@id/iv_staff" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/tv_staff_desc" />
                    <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_want_see" android:visibility="visible" android:layout_width="24.0dip" android:layout_height="24.0dip" android:layout_marginLeft="12.0dip" android:layout_marginRight="12.0dip" android:layout_marginBottom="49.0dip" android:src="@mipmap/movie_detail_icon_want_to_see_white" app:layout_constraintBottom_toBottomOf="@id/iv_staff" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/tv_staff_name" />
                    <View android:background="@drawable/bg_staff_drawing_top_16dp" android:layout_width="fill_parent" android:layout_height="16.0dip" app:layout_constraintBottom_toBottomOf="@id/iv_staff" />
                    <com.transsion.moviedetail.view.InfoExtendView android:id="@id/infoExtendView" android:paddingBottom="24.0dip" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="wrap_content" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/iv_staff" />
                </androidx.constraintlayout.widget.ConstraintLayout>
                <androidx.appcompat.widget.Toolbar android:id="@id/toolbar" android:layout_width="fill_parent" android:layout_height="48.0dip" app:contentInsetLeft="0.0dip" app:contentInsetStart="0.0dip" app:layout_collapseMode="pin">
                    <androidx.constraintlayout.widget.ConstraintLayout android:visibility="visible" android:clipChildren="false" android:layout_width="fill_parent" android:layout_height="fill_parent" android:layout_marginLeft="12.0dip" android:layout_marginRight="12.0dip" app:layout_constraintTop_toTopOf="parent">
                        <androidx.appcompat.widget.AppCompatImageView android:id="@id/ivBack" android:layout_width="24.0dip" android:layout_height="24.0dip" android:src="@mipmap/icon_white_back" android:scaleType="centerCrop" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
                        <com.google.android.material.imageview.ShapeableImageView android:id="@id/ivCoverSmall" android:visibility="gone" android:layout_width="24.0dip" android:layout_height="32.0dip" android:layout_marginStart="8.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toEndOf="@id/ivBack" app:layout_constraintTop_toTopOf="parent" app:shapeAppearanceOverlay="@style/corner_style_2" />
                        <androidx.appcompat.widget.AppCompatTextView android:textSize="18.0sp" android:textColor="@color/text_01" android:ellipsize="end" android:id="@id/tvTitle" android:visibility="gone" android:layout_width="0.0dip" android:lines="1" android:textAlignment="viewStart" android:layout_marginStart="8.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toStartOf="@id/ivDownload" app:layout_constraintStart_toEndOf="@id/ivCoverSmall" app:layout_constraintTop_toTopOf="parent" style="@style/style_regula_bigger_text" />
                        <androidx.appcompat.widget.AppCompatImageView android:id="@id/ivDownload" android:layout_width="24.0dip" android:layout_height="24.0dip" android:src="@drawable/ic_image_download" android:tint="@color/color_download_selector" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent" />
                        <com.transsion.moviedetail.view.MovieDetailShareView android:id="@id/ivShare" android:layout_width="24.0dip" android:layout_height="24.0dip" android:scaleType="centerCrop" android:layout_marginEnd="16.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toStartOf="@id/ivDownload" app:layout_constraintTop_toTopOf="parent" />
                    </androidx.constraintlayout.widget.ConstraintLayout>
                </androidx.appcompat.widget.Toolbar>
            </com.google.android.material.appbar.CollapsingToolbarLayout>
            <TextView android:textSize="18.0sp" android:textStyle="bold" android:textColor="@color/text_01" android:id="@id/tv_filmography" android:background="@color/movie_staff_bg" android:paddingTop="12.0dip" android:paddingBottom="12.0dip" android:layout_width="fill_parent" android:layout_height="wrap_content" android:text="@string/movie_staff_filmography" android:paddingStart="12.0dip" android:paddingEnd="12.0dip" />
        </com.google.android.material.appbar.AppBarLayout>
        <androidx.recyclerview.widget.RecyclerView android:id="@id/rv" android:layout_width="fill_parent" android:layout_height="fill_parent" app:layout_behavior="@string/appbar_scrolling_view_behavior" />
    </androidx.coordinatorlayout.widget.CoordinatorLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
