<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="wrap_content" android:paddingStart="@dimen/dimens_12" android:paddingEnd="@dimen/dimens_12"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.google.android.material.imageview.ShapeableImageView android:id="@id/rank_item_image" android:layout_width="108.0dip" android:layout_height="150.0dip" android:scaleType="centerCrop" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:shapeAppearanceOverlay="@style/roundStyle_4" />
    <com.tn.lib.view.CornerTextView android:id="@id/rank_item_corner" android:layout_width="wrap_content" android:layout_height="wrap_content" app:layout_constraintEnd_toEndOf="@id/rank_item_image" app:layout_constraintTop_toTopOf="@id/rank_item_image" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/rank_item_rank_tag" android:layout_width="wrap_content" android:layout_height="wrap_content" app:layout_constraintStart_toStartOf="@id/rank_item_image" app:layout_constraintTop_toTopOf="@id/rank_item_image" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/common_white" android:id="@id/rank_item_tag_rank" android:layout_marginTop="4.0dip" android:layout_marginStart="3.0dip" app:layout_constraintStart_toStartOf="@id/rank_item_rank_tag" app:layout_constraintTop_toTopOf="@id/rank_item_rank_tag" style="@style/style_import_text" />
    <com.tn.lib.widget.TnTextView android:textColor="@color/pair_text_191F2B" android:ellipsize="end" android:id="@id/rank_item_title" android:layout_width="0.0dip" android:maxLines="2" android:layout_marginStart="8.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/rank_item_image" app:layout_constraintTop_toTopOf="parent" style="@style/style_import_text" />
    <TextView android:textColor="@color/pair_text_61656D" android:ellipsize="end" android:id="@id/rank_item_des" android:layout_width="0.0dip" android:layout_height="0.0dip" app:layout_constraintBottom_toTopOf="@id/rank_item_download" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="@id/rank_item_title" app:layout_constraintTop_toBottomOf="@id/rank_item_title" style="@style/style_regular_text" />
    <com.transsnet.downloader.widget.DownloadView android:id="@id/rank_item_download" android:background="@drawable/bg_btn_01" android:paddingLeft="8.0dip" android:paddingRight="8.0dip" android:visibility="invisible" android:layout_width="wrap_content" android:layout_height="28.0dip" app:layout_constraintBottom_toBottomOf="@id/rank_item_image" app:layout_constraintEnd_toEndOf="parent" />
    <TextView android:textColor="@color/color_ffaa0f" android:id="@id/rank_item_imdb" android:layout_width="wrap_content" android:layout_height="wrap_content" android:drawablePadding="2.0dip" android:drawableStart="@drawable/tt_star" app:layout_constraintBottom_toBottomOf="@id/rank_item_download" app:layout_constraintStart_toStartOf="@id/rank_item_title" app:layout_constraintTop_toTopOf="@id/rank_item_download" style="@style/style_title_text" />
</androidx.constraintlayout.widget.ConstraintLayout>
