<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/divider" style="@style/style_card_view_home_subject"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <FrameLayout android:id="@id/adContainer" android:paddingLeft="250.0dip" android:paddingRight="250.0dip" android:layout_width="fill_parent" android:layout_height="wrap_content" />
</androidx.cardview.widget.CardView>
