<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.viewpager2.widget.ViewPager2 android:id="@id/view_pager" android:layout_width="fill_parent" android:layout_height="0.0dip" app:layout_constraintBottom_toTopOf="@id/indicator" app:layout_constraintTop_toTopOf="parent" />
    <com.transsion.subroom.guide.Indicator android:orientation="horizontal" android:id="@id/indicator" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginBottom="12.0dip" app:CustomHorizontalMargin="10.0dip" app:CustomIndicatorSize="2" app:CustomSelectedIndex="0" app:layout_constraintBottom_toTopOf="@id/btn_continue" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" />
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/white" android:id="@id/btn_continue" android:layout_width="fill_parent" android:layout_height="44.0dip" android:layout_marginLeft="32.0dip" android:layout_marginRight="32.0dip" android:layout_marginBottom="60.0dip" android:text="@string/continue_" android:layout_marginHorizontal="32.0dip" app:layout_constraintBottom_toBottomOf="parent" style="@style/style_main_btn_h42" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textColor="@color/text_01" android:gravity="center" android:id="@id/btn_skip" android:layout_width="72.0dip" android:layout_height="36.0dip" android:layout_marginBottom="16.0dip" android:text="@string/skip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>
