<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:background="@color/pair_EDF0F51" android:fitsSystemWindows="true" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.tn.lib.view.TitleLayout android:id="@id/toolbar" android:background="@color/pair_FFFFFF2" android:layout_width="0.0dip" android:layout_height="wrap_content" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <androidx.recyclerview.widget.RecyclerView android:id="@id/recyclerView" android:paddingBottom="@dimen/dp_20" android:clipToPadding="false" android:layout_width="0.0dip" android:layout_height="0.0dip" android:layout_marginLeft="12.0dip" android:layout_marginRight="12.0dip" android:layout_marginBottom="@dimen/dp_12" android:layout_marginHorizontal="12.0dip" app:layout_constraintBottom_toTopOf="@id/submitButton" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/toolbar" />
    <androidx.appcompat.widget.AppCompatButton android:enabled="false" android:textSize="16.0sp" android:textColor="@color/gray_dark_00" android:gravity="center" android:id="@id/submitButton" android:background="@drawable/libui_main_btn_selector" android:layout_width="0.0dip" android:layout_height="40.0dip" android:layout_marginBottom="16.0dip" android:text="@string/submit" android:textAllCaps="false" android:layout_marginStart="16.0dip" android:layout_marginEnd="16.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" style="@style/robot_bold" />
</androidx.constraintlayout.widget.ConstraintLayout>
