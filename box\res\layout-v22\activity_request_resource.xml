<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.constraintlayout.widget.ConstraintLayout android:orientation="vertical" android:id="@id/cl" android:background="@drawable/bg_clear_dialog" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_alignParentBottom="true">
        <View android:background="@drawable/bg_btn_module_07" android:layout_width="fill_parent" android:layout_height="68.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
        <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_back" android:paddingTop="12.0dip" android:paddingBottom="12.0dip" android:layout_width="wrap_content" android:layout_height="wrap_content" android:src="@mipmap/ic_close" android:paddingStart="12.0dip" android:paddingEnd="12.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent" />
        <androidx.appcompat.widget.AppCompatTextView android:textSize="18.0sp" android:textStyle="bold" android:textColor="@color/text_01" android:id="@id/tv_title" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="16.0dip" android:text="@string/request_movies_or_tv_shows" android:paddingEnd="44.0dip" android:layout_marginStart="16.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
        <androidx.appcompat.widget.AppCompatTextView android:textSize="14.0sp" android:textColor="@color/text_02" android:id="@id/tv_tips" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="8.0dip" android:text="Can't find your favourite movie or TV show? Suggest it here and let us work our magic! " android:layout_marginStart="16.0dip" android:layout_marginEnd="16.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tv_title" />
        <androidx.appcompat.widget.AppCompatEditText android:textSize="14.0sp" android:textColor="@color/text_06" android:textColorHint="@color/text_05" android:gravity="start|top" android:id="@id/et_content" android:background="@drawable/bg_request_edit_slide" android:paddingTop="12.0dip" android:paddingBottom="30.0dip" android:focusable="true" android:layout_width="fill_parent" android:layout_height="96.0dip" android:layout_marginTop="24.0dip" android:hint="@string/hint_movie_tv" android:maxLength="1000" android:textDirection="locale" android:paddingStart="12.0dip" android:paddingEnd="12.0dip" android:layout_marginStart="16.0dip" android:layout_marginEnd="16.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tv_tips" />
        <androidx.appcompat.widget.AppCompatTextView android:enabled="false" android:textStyle="bold" android:textColor="@color/common_white" android:gravity="center" android:id="@id/btn_top" android:background="@drawable/bg_comfirm" android:layout_width="0.0dip" android:layout_height="36.0dip" android:layout_marginLeft="16.0dip" android:layout_marginTop="23.0dip" android:layout_marginRight="16.0dip" android:text="@string/submit" android:layout_marginHorizontal="16.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/et_content" style="@style/style_medium_text" />
        <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/gray_40" android:id="@id/tv_bottom_tips" android:layout_width="fill_parent" android:layout_height="55.0dip" android:layout_marginLeft="16.0dip" android:layout_marginTop="16.0dip" android:layout_marginRight="16.0dip" android:layout_marginBottom="24.0dip" android:layout_marginHorizontal="16.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/btn_top" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</RelativeLayout>
