<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:orientation="vertical" android:background="@color/bg_01" android:fitsSystemWindows="true" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.transsion.publish.view.CustomHeader android:id="@id/sv_title_bar" android:background="@color/transparent" android:layout_width="fill_parent" android:layout_height="wrap_content" app:isBack="false" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:showEdit="true" app:titleValue="@string/upload_select_photos_title" />
    <androidx.recyclerview.widget.RecyclerView android:id="@id/select_video_recycler" android:layout_width="fill_parent" android:layout_height="0.0dip" android:layout_marginBottom="52.0dip" android:overScrollMode="never" android:layout_marginStart="3.0dip" android:layout_marginEnd="3.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/sv_title_bar" />
    <ProgressBar android:layout_gravity="center" android:id="@id/select_video_loading" android:visibility="visible" android:layout_width="28.0dip" android:layout_height="28.0dip" android:layout_centerInParent="true" android:indeterminateTint="@color/color_2FF58B" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintLeft_toLeftOf="parent" app:layout_constraintRight_toRightOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <LinearLayout android:orientation="vertical" android:id="@id/sv_no_content_view" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintLeft_toLeftOf="parent" app:layout_constraintRight_toRightOf="parent" app:layout_constraintTop_toTopOf="parent">
        <ImageView android:layout_width="wrap_content" android:layout_height="wrap_content" android:src="@mipmap/ic_no_content" />
        <TextView android:textColor="#ff999999" android:layout_gravity="center_horizontal" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="16.0dip" android:text="@string/no_content" />
    </LinearLayout>
    <LinearLayout android:gravity="center_horizontal" android:orientation="vertical" android:id="@id/sv_lock_view" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginStart="47.0dip" android:layout_marginEnd="47.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintLeft_toLeftOf="parent" app:layout_constraintRight_toRightOf="parent" app:layout_constraintTop_toTopOf="parent">
        <ImageView android:layout_width="wrap_content" android:layout_height="wrap_content" android:src="@mipmap/ic_no_content" />
        <TextView android:textSize="12.0dip" android:textColor="#ff666666" android:layout_gravity="center_horizontal" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="16.0dip" android:text="@string/select_image_no_permission" />
        <TextView android:textSize="12.0dip" android:textColor="#ff884dff" android:layout_gravity="center_horizontal" android:id="@id/sv_tv_grant" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="13.0dip" android:text="@string/select_image_grant_permission" />
    </LinearLayout>
    <TextView android:textSize="@dimen/text_size_14" android:textColor="@color/text_02" android:id="@id/selectNumTV" android:background="@color/bg_01" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginBottom="17.0dip" android:maxLines="1" android:layout_marginStart="12.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" />
    <TextView android:textSize="@dimen/text_size_14" android:textColor="#ff191f2b" android:gravity="center" android:id="@id/confirmTV" android:background="@drawable/bg_linear_r4" android:paddingLeft="12.0dip" android:paddingRight="12.0dip" android:layout_width="wrap_content" android:layout_height="28.0dip" android:layout_margin="12.0dip" android:text="@string/profile_crop_confirm" android:includeFontPadding="false" android:paddingHorizontal="12.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" style="@style/robot_bold" />
</androidx.constraintlayout.widget.ConstraintLayout>
