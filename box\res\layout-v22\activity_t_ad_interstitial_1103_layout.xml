<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.constraintlayout.widget.ConstraintLayout android:id="@id/main_layout" android:layout_width="0.0dip" android:layout_height="0.0dip" android:layout_marginLeft="24.0dip" android:layout_marginRight="24.0dip" android:layout_marginHorizontal="24.0dip" app:layout_constraintBottom_toTopOf="@id/llRoot" app:layout_constraintDimensionRatio="1:1" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:layout_constraintVertical_chainStyle="packed">
        <com.cloud.sdk.commonutil.widget.TranCircleImageView android:id="@id/iv_main_image" android:layout_width="fill_parent" android:layout_height="fill_parent" android:adjustViewBounds="true" app:topLeftRadiusYL="8.0dip" app:topRightRadiusYL="8.0dip" />
        <com.cloud.hisavana.sdk.api.view.AdDisclaimerView android:id="@id/ad_disclaimer_view" android:layout_width="0.0dip" android:layout_height="@dimen/ad_disclaimer_height" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
    <include android:id="@id/ad_flag" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginTop="12.0dip" android:layout_marginStart="12.0dip" android:layout_marginEnd="12.0dip" app:layout_constraintEnd_toEndOf="@id/main_layout" app:layout_constraintStart_toStartOf="@id/main_layout" app:layout_constraintTop_toTopOf="@id/main_layout" layout="@layout/include_ad_flag" />
    <ImageView android:id="@id/im_volume" android:visibility="gone" android:layout_width="24.0dip" android:layout_height="24.0dip" android:layout_marginTop="7.0dip" android:src="@drawable/hisavana_volume_close" android:layout_marginStart="26.0dip" app:layout_constraintStart_toStartOf="@id/main_layout" app:layout_constraintTop_toTopOf="@id/main_layout" />
    <LinearLayout android:orientation="vertical" android:id="@id/llRoot" android:background="@drawable/ssp_bg_ffffff_0_0_8_8" android:visibility="visible" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginLeft="24.0dip" android:layout_marginRight="24.0dip" android:layout_marginHorizontal="24.0dip" app:layout_constraintBottom_toTopOf="@id/ivCancel" app:layout_constraintEnd_toEndOf="@id/main_layout" app:layout_constraintHorizontal_bias="0.0" app:layout_constraintStart_toStartOf="@id/main_layout" app:layout_constraintTop_toBottomOf="@id/main_layout">
        <TextView android:textSize="14.0sp" android:textColor="#ff222222" android:ellipsize="end" android:layout_gravity="center" android:id="@id/tvName" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginLeft="24.0dip" android:layout_marginTop="16.0dip" android:layout_marginRight="24.0dip" android:text="" android:lines="1" android:layout_marginHorizontal="24.0dip" />
        <TextView android:textSize="11.0sp" android:textColor="#ff787878" android:ellipsize="end" android:gravity="center" android:layout_gravity="center" android:id="@id/tvDescription" android:visibility="visible" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginLeft="24.0dip" android:layout_marginTop="4.0dip" android:layout_marginRight="24.0dip" android:text="" android:lines="2" android:lineSpacingExtra="5.0dip" android:layout_marginHorizontal="24.0dip" />
        <TextView android:textStyle="bold" android:textColor="#ffffffff" android:ellipsize="end" android:gravity="center" android:layout_gravity="center" android:id="@id/tvBtn" android:background="@drawable/ssp_bg_0052e2_4_4_4_4" android:layout_width="186.0dip" android:layout_height="32.0dip" android:layout_marginTop="12.0dip" android:layout_marginBottom="6.0dip" android:text="" android:lines="1" />
        <com.cloud.hisavana.sdk.api.view.StoreMarkView android:layout_gravity="center" android:id="@id/ps_mark_view" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginBottom="9.0dip" />
    </LinearLayout>
    <ImageView android:id="@id/ivCancel" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="16.0dip" android:src="@drawable/ssp_sdk_cancel" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="@id/main_layout" app:layout_constraintStart_toStartOf="@id/main_layout" app:layout_constraintTop_toBottomOf="@id/llRoot" />
</androidx.constraintlayout.widget.ConstraintLayout>
