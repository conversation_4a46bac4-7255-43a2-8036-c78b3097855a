<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:background="@color/white" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <ImageView android:id="@id/im_volume" android:visibility="gone" android:layout_width="24.0dip" android:layout_height="24.0dip" android:layout_marginTop="16.0dip" android:src="@drawable/hisavana_volume_close" android:layout_marginStart="26.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <include android:id="@id/ad_flag" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginTop="21.0dip" android:layout_marginStart="12.0dip" android:layout_marginEnd="12.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" layout="@layout/include_ad_flag" />
    <FrameLayout android:id="@id/fl_content" android:layout_width="fill_parent" android:layout_height="0.0dip" android:layout_marginLeft="16.0dip" android:layout_marginRight="16.0dip" android:layout_marginHorizontal="16.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintDimensionRatio="h,16:9" app:layout_constraintTop_toTopOf="parent">
        <androidx.constraintlayout.widget.ConstraintLayout android:layout_gravity="center" android:id="@id/main_layout" android:background="@color/black" android:layout_width="fill_parent" android:layout_height="fill_parent" android:adjustViewBounds="true" />
        <com.cloud.sdk.commonutil.widget.TranCircleImageView android:id="@id/iv_main_image" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="fill_parent" android:adjustViewBounds="true" />
        <com.cloud.hisavana.sdk.api.view.AdDisclaimerView android:layout_gravity="bottom" android:id="@id/ad_disclaimer_view" android:layout_width="fill_parent" android:layout_height="@dimen/ad_disclaimer_height" />
    </FrameLayout>
    <LinearLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="wrap_content" app:layout_constraintBottom_toTopOf="@id/fl_content" app:layout_constraintTop_toBottomOf="@id/im_volume">
        <com.cloud.sdk.commonutil.widget.TranCircleImageView android:layout_gravity="center" android:id="@id/ivIcon" android:layout_width="80.0dip" android:layout_height="80.0dip" android:layout_marginTop="16.0dip" app:bottomLeftRadiusYL="4.0dip" app:bottomRightRadiusYL="4.0dip" app:topLeftRadiusYL="4.0dip" app:topRightRadiusYL="4.0dip" />
        <TextView android:textSize="22.0sp" android:textColor="#ff222222" android:ellipsize="end" android:layout_gravity="center" android:id="@id/tvName" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="9.0dip" android:text="" android:lines="1" />
        <TextView android:textSize="16.0sp" android:textColor="#ff787878" android:ellipsize="end" android:gravity="center" android:layout_gravity="center" android:id="@id/tvDescription" android:visibility="visible" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginLeft="24.0dip" android:layout_marginTop="2.0dip" android:layout_marginRight="24.0dip" android:text="" android:maxLines="2" android:lineSpacingExtra="4.0dip" android:layout_marginHorizontal="24.0dip" />
    </LinearLayout>
    <LinearLayout android:orientation="horizontal" android:id="@id/llRoot" android:visibility="visible" android:layout_width="fill_parent" android:layout_height="48.0dip" android:layout_marginLeft="16.0dip" android:layout_marginRight="16.0dip" android:layout_marginHorizontal="16.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintTop_toBottomOf="@id/fl_content">
        <TextView android:textSize="16.0sp" android:textStyle="bold" android:textColor="#ff222222" android:ellipsize="end" android:gravity="center" android:id="@id/ivCancel" android:background="@drawable/ssp_bg_border_e6e6e6_radius_4" android:layout_width="0.0dip" android:layout_height="48.0dip" android:text="@string/interstitial_close" android:lines="1" android:layout_weight="1.0" />
        <TextView android:textSize="16.0sp" android:textStyle="bold" android:textColor="#ffffffff" android:ellipsize="end" android:gravity="center" android:layout_gravity="center" android:id="@id/tvBtn" android:background="@drawable/ssp_bg_0052e2_4_4_4_4" android:layout_width="0.0dip" android:layout_height="48.0dip" android:text="" android:lines="1" android:layout_weight="1.0" android:layout_marginStart="16.0dip" />
    </LinearLayout>
    <com.cloud.hisavana.sdk.api.view.StoreMarkView android:id="@id/ps_mark_view" android:layout_width="wrap_content" android:layout_height="wrap_content" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/llRoot" />
</androidx.constraintlayout.widget.ConstraintLayout>
