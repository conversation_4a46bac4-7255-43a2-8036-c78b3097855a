<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.constraintlayout.widget.ConstraintLayout android:id="@id/llRoot" android:layout_width="0.0dip" android:layout_height="0.0dip" android:layout_marginLeft="36.0dip" android:layout_marginTop="56.0dip" android:layout_marginRight="36.0dip" android:layout_marginHorizontal="36.0dip" app:layout_constraintBottom_toTopOf="@id/ivCancel" app:layout_constraintDimensionRatio="9:16" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:layout_constraintVertical_chainStyle="packed" />
    <com.cloud.hisavana.sdk.api.view.AdDisclaimerView android:id="@id/ad_disclaimer_view" android:layout_width="0.0dip" android:layout_height="@dimen/ad_disclaimer_height" app:layout_constraintBottom_toBottomOf="@id/llRoot" app:layout_constraintEnd_toEndOf="@id/llRoot" app:layout_constraintStart_toStartOf="@id/llRoot" />
    <include android:id="@id/ad_flag" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginTop="12.0dip" android:layout_marginStart="12.0dip" android:layout_marginEnd="12.0dip" app:layout_constraintEnd_toEndOf="@id/llRoot" app:layout_constraintStart_toStartOf="@id/llRoot" app:layout_constraintTop_toTopOf="@id/llRoot" layout="@layout/include_ad_flag" />
    <com.cloud.hisavana.sdk.api.view.StoreMarkView android:id="@id/ps_mark_view" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_margin="@dimen/hisavana_ad_dimen_12" app:layout_constraintStart_toStartOf="@id/llRoot" app:layout_constraintTop_toTopOf="@id/llRoot" />
    <ImageView android:id="@id/ivCancel" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="16.0dip" android:layout_marginBottom="56.0dip" android:src="@drawable/ssp_sdk_cancel" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="@id/llRoot" app:layout_constraintStart_toStartOf="@id/llRoot" app:layout_constraintTop_toBottomOf="@id/llRoot" />
</androidx.constraintlayout.widget.ConstraintLayout>
