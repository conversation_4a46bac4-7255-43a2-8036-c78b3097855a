<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:id="@id/rllayout" android:background="@color/base_color_black" android:layout_width="fill_parent" android:layout_height="fill_parent" style="@style/SystemBarTint_Style"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <FrameLayout android:layout_width="fill_parent" android:layout_height="0.0dip" app:layout_constraintBottom_toTopOf="@id/bottomLine" app:layout_constraintTop_toBottomOf="@id/clTitle">
        <com.transsion.publish.view.MatchParentVideoView android:layout_gravity="center" android:id="@id/vv_video" android:layout_width="fill_parent" android:layout_height="fill_parent" />
        <ImageView android:id="@id/coverIV" android:layout_width="fill_parent" android:layout_height="fill_parent" android:scaleType="centerInside" />
    </FrameLayout>
    <RelativeLayout android:id="@id/clTitle" android:layout_width="fill_parent" android:layout_height="44.0dip" app:layout_constraintTop_toTopOf="parent">
        <androidx.appcompat.widget.AppCompatImageButton android:id="@id/btn_back" android:background="@null" android:padding="10.0dip" android:layout_width="42.0dip" android:layout_height="fill_parent" android:src="@mipmap/icon_white_back" android:scaleType="centerInside" android:layout_marginStart="5.0dip" android:layout_alignParentStart="true" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
        <FrameLayout android:id="@id/fl_clear" android:layout_width="wrap_content" android:layout_height="fill_parent" android:layout_centerInParent="true" android:paddingStart="16.0dip" android:paddingEnd="16.0dip" android:layout_alignParentEnd="true">
            <TextView android:textSize="14.0dip" android:textColor="@color/white" android:gravity="center" android:layout_gravity="center" android:id="@id/tv_delete" android:background="@drawable/ic_video_clear" android:layout_width="23.0dip" android:layout_height="24.0dip" />
        </FrameLayout>
        <LinearLayout android:gravity="center" android:orientation="horizontal" android:id="@id/llSelect" android:layout_width="wrap_content" android:layout_height="fill_parent" android:layout_alignParentEnd="true">
            <TextView android:textSize="@dimen/text_size_12" android:textColor="@color/update_color_191F2B" android:gravity="center" android:id="@id/tvNumber" android:background="@drawable/ic_select_number_bro" android:layout_width="20.0dip" android:layout_height="20.0dip" android:layout_marginEnd="4.0dip" />
            <TextView android:textSize="@dimen/text_size_14" android:textColor="@color/text_01" android:gravity="center" android:id="@id/tvSelect" android:layout_width="wrap_content" android:layout_height="fill_parent" android:text="@string/select" android:drawablePadding="8.0dip" android:layout_marginEnd="12.0dip" style="@style/robot_medium" />
        </LinearLayout>
    </RelativeLayout>
    <ImageView android:id="@id/playIV" android:layout_width="80.0dip" android:layout_height="80.0dip" android:src="@mipmap/ic_play_round" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <ProgressBar android:id="@id/clip_loading" android:visibility="gone" android:layout_width="28.0dip" android:layout_height="28.0dip" android:layout_centerInParent="true" android:indeterminateTint="@color/color_2FF58B" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <View android:id="@id/bottomLine" android:background="@color/line_01" android:layout_width="fill_parent" android:layout_height="1.0dip" android:layout_marginBottom="52.0dip" app:layout_constraintBottom_toBottomOf="parent" />
    <TextView android:textSize="@dimen/text_size_14" android:textColor="@color/text_02" android:id="@id/selectNumTV" android:background="@color/bg_01" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginBottom="17.0dip" android:maxLines="1" android:layout_marginStart="12.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" />
    <TextView android:textSize="@dimen/text_size_14" android:textColor="#ff191f2b" android:gravity="center" android:id="@id/confirmTV" android:background="@drawable/bg_linear_r4" android:paddingLeft="12.0dip" android:paddingRight="12.0dip" android:layout_width="wrap_content" android:layout_height="28.0dip" android:layout_margin="12.0dip" android:text="@string/profile_crop_confirm" android:includeFontPadding="false" android:paddingHorizontal="12.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" style="@style/robot_bold" />
    <androidx.constraintlayout.widget.Group android:id="@id/bottomGroup" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" app:constraint_referenced_ids="bottomLine,confirmTV,selectNumTV" />
</androidx.constraintlayout.widget.ConstraintLayout>
