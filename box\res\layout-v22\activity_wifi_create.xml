<?xml version="1.0" encoding="utf-8"?>
<androidx.appcompat.widget.LinearLayoutCompat android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.tn.lib.view.TitleLayout android:id="@id/toolBar" android:layout_width="fill_parent" android:layout_height="48.0dip" app:isShowBack="true" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:titleText="Connect Device" />
    <androidx.core.widget.NestedScrollView android:background="@drawable/transfer_create_wifi_bg" android:layout_width="fill_parent" android:layout_height="fill_parent">
        <androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="fill_parent">
            <androidx.constraintlayout.widget.ConstraintLayout android:orientation="vertical" android:id="@id/llUp" android:background="@drawable/transfer_wifi_create_up" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginLeft="16.0dip" android:layout_marginTop="16.0dip" android:layout_marginRight="16.0dip" android:layout_marginHorizontal="16.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent">
                <LinearLayout android:layout_gravity="center" android:orientation="horizontal" android:id="@id/llSend" android:paddingLeft="16.0dip" android:paddingTop="8.0dip" android:paddingRight="16.0dip" android:paddingBottom="8.0dip" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="16.0dip" android:paddingHorizontal="16.0dip" android:paddingVertical="8.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent">
                    <androidx.appcompat.widget.AppCompatImageView android:layout_gravity="center_vertical" android:layout_width="wrap_content" android:layout_height="wrap_content" android:src="@drawable/transfer_wifi_sender" android:layout_marginEnd="4.0dip" />
                    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/brand" android:gravity="center" android:layout_gravity="center_vertical" android:id="@id/tvSend" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/transfer_wifi_create_send" style="@style/robot_medium" />
                </LinearLayout>
                <com.google.android.material.imageview.ShapeableImageView android:layout_gravity="center" android:id="@id/ivTransWifiQrCode" android:layout_width="208.0dip" android:layout_height="208.0dip" android:layout_marginTop="8.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/llSend" app:shapeAppearance="@style/corner_style_4" />
                <androidx.appcompat.widget.AppCompatTextView android:textSize="14.0sp" android:textColor="@color/text_01" android:gravity="center" android:id="@id/tvWifiSsid" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginLeft="24.0dip" android:layout_marginTop="16.0dip" android:layout_marginRight="24.0dip" android:layout_marginHorizontal="24.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/ivTransWifiQrCode" style="@style/robot_medium" />
                <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/text_02" android:gravity="center" android:id="@id/tvWaitingForReceiver" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginLeft="24.0dip" android:layout_marginTop="8.0dip" android:layout_marginRight="24.0dip" android:layout_marginBottom="40.0dip" android:text="@string/transfer_wifi_create_waiting_for_receiver" android:layout_marginHorizontal="24.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tvWifiSsid" />
                <androidx.constraintlayout.widget.Group android:id="@id/groupSuccessLayout" android:visibility="visible" android:layout_width="wrap_content" android:layout_height="wrap_content" app:constraint_referenced_ids="ivTransWifiQrCode,tvWifiSsid,tvWaitingForReceiver" />
                <androidx.appcompat.widget.AppCompatImageView android:id="@id/ivError" android:layout_width="250.0dip" android:layout_height="150.0dip" android:layout_marginTop="8.0dip" android:src="@drawable/transfer_wifi_error" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/llSend" />
                <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/text_03" android:gravity="center" android:id="@id/tvErrorTip" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginLeft="24.0dip" android:layout_marginRight="24.0dip" android:text="@string/transfer_wifi_connect_create_qr_error_tip" android:layout_marginHorizontal="24.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/ivError" />
                <androidx.appcompat.widget.AppCompatTextView android:textSize="14.0sp" android:textColor="@color/text_01" android:gravity="center" android:id="@id/tvRetry" android:background="@drawable/transfer_wifi_apk_bg" android:paddingLeft="56.0dip" android:paddingTop="12.0dip" android:paddingRight="56.0dip" android:paddingBottom="12.0dip" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="16.0dip" android:layout_marginBottom="64.0dip" android:text="@string/transfer_wifi_connect_create_retry" android:paddingHorizontal="56.0dip" android:paddingVertical="12.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tvErrorTip" />
                <androidx.constraintlayout.widget.Group android:id="@id/groupErrorLayout" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" app:constraint_referenced_ids="ivError,tvErrorTip,tvRetry" />
            </androidx.constraintlayout.widget.ConstraintLayout>
            <androidx.appcompat.widget.LinearLayoutCompat android:orientation="vertical" android:background="@drawable/transfer_wifi_create_down" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginLeft="16.0dip" android:layout_marginTop="-1.0dip" android:layout_marginRight="16.0dip" android:layout_marginHorizontal="16.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/llUp">
                <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textColor="@color/text_01" android:gravity="center" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginLeft="24.0dip" android:layout_marginTop="40.0dip" android:layout_marginRight="24.0dip" android:text="@string/transfer_wifi_create_download_now" android:layout_marginHorizontal="24.0dip" style="@style/robot_medium" />
                <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textColor="@color/text_01" android:gravity="center" android:id="@id/tvShareQrCode" android:background="@drawable/transfer_wifi_apk_bg" android:layout_width="fill_parent" android:layout_height="40.0dip" android:layout_marginLeft="24.0dip" android:layout_marginTop="24.0dip" android:layout_marginRight="24.0dip" android:text="@string/transfer_wifi_create_share_apk_qr_code" android:layout_marginHorizontal="24.0dip" style="@style/robot_medium" />
                <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textColor="@color/text_01" android:gravity="center" android:id="@id/tvShareLink" android:background="@drawable/transfer_wifi_apk_bg" android:layout_width="fill_parent" android:layout_height="40.0dip" android:layout_marginLeft="24.0dip" android:layout_marginTop="16.0dip" android:layout_marginRight="24.0dip" android:layout_marginBottom="60.0dip" android:text="@string/transfer_wifi_create_share_apk_link" android:layout_marginHorizontal="24.0dip" style="@style/robot_medium" />
            </androidx.appcompat.widget.LinearLayoutCompat>
            <View android:background="@drawable/transfer_wifi_dashed_line" android:layout_width="fill_parent" android:layout_height="3.0dip" android:layout_marginLeft="28.0dip" android:layout_marginRight="28.0dip" android:layout_marginHorizontal="28.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/llUp" />
            <ProgressBar android:id="@id/progressBar" android:visibility="gone" android:layout_width="24.0dip" android:layout_height="24.0dip" android:layout_marginTop="165.0dip" android:indeterminateTint="@color/text_03" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
            <com.tn.lib.view.SwitchButton android:id="@id/switchButton" android:visibility="gone" android:layout_width="50.0dip" android:layout_height="wrap_content" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.core.widget.NestedScrollView>
</androidx.appcompat.widget.LinearLayoutCompat>
