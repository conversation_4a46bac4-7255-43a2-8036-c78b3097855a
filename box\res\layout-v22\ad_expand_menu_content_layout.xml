<?xml version="1.0" encoding="utf-8"?>
<merge android:layout_width="wrap_content" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <ImageView android:id="@id/arrow_up" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginBottom="-1.0dip" android:src="@drawable/arrow_up" android:layout_marginStart="0.0dip" />
    <LinearLayout android:orientation="vertical" android:id="@id/content" android:background="@drawable/ssp_bg_white_radius" android:paddingLeft="@dimen/dp_8" android:paddingTop="@dimen/dp_12" android:paddingRight="@dimen/dp_8" android:paddingBottom="@dimen/dp_12" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_below="@id/arrow_up" android:paddingHorizontal="@dimen/dp_8" android:paddingVertical="@dimen/dp_12">
        <TextView android:textSize="14.0sp" android:textColor="#ff0f1a2f" android:paddingLeft="@dimen/dp_4" android:paddingRight="@dimen/dp_4" android:layout_width="fill_parent" android:layout_height="@dimen/dp_16" android:text="@string/ad_icon" android:paddingHorizontal="@dimen/dp_4" />
        <TextView android:textSize="10.0sp" android:textColor="#ff6f7682" android:id="@id/advertiser_info" android:paddingLeft="@dimen/dp_4" android:paddingRight="@dimen/dp_4" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="2.0dip" android:minHeight="14.0dip" android:maxLines="5" android:paddingHorizontal="@dimen/dp_4" />
        <View android:background="#fff1f2f3" android:paddingLeft="@dimen/dp_4" android:paddingRight="@dimen/dp_4" android:layout_width="fill_parent" android:layout_height="0.5dip" android:layout_marginTop="7.0dip" android:paddingHorizontal="@dimen/dp_4" />
        <com.cloud.hisavana.sdk.common.widget.expandmenu.AdExpandMenuItemView android:id="@id/personalise" android:layout_width="fill_parent" android:layout_height="32.0dip" android:layout_marginTop="8.5dip" />
        <com.cloud.hisavana.sdk.common.widget.expandmenu.AdExpandMenuItemView android:id="@id/about" android:layout_width="fill_parent" android:layout_height="32.0dip" />
        <com.cloud.hisavana.sdk.common.widget.expandmenu.AdExpandMenuItemView android:id="@id/copy_link" android:layout_width="fill_parent" android:layout_height="32.0dip" />
    </LinearLayout>
    <ImageView android:id="@id/arrow_down" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="-1.0dip" android:src="@drawable/arrow_down" android:layout_below="@id/content" android:layout_marginStart="0.0dip" />
</merge>
