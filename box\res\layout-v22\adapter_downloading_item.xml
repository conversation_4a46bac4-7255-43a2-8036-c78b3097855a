<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:id="@id/cl_root" android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <include android:id="@id/card_cover" android:layout_width="96.0dip" android:layout_height="54.0dip" android:layout_marginTop="8.0dip" android:layout_marginBottom="12.0dip" android:layout_marginStart="12.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" layout="@layout/layout_download_item_cover" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="14.0sp" android:textColor="@color/text_01" android:ellipsize="end" android:id="@id/tv_name" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="2.0dip" android:maxLines="1" android:layout_marginStart="8.0dip" app:layout_constrainedWidth="true" app:layout_constraintEnd_toStartOf="@id/tv_ep" app:layout_constraintHorizontal_bias="0.0" app:layout_constraintHorizontal_chainStyle="packed" app:layout_constraintStart_toEndOf="@id/card_cover" app:layout_constraintTop_toTopOf="@id/card_cover" style="@style/style_medium_text" />
    <com.tn.lib.widget.TnTextView android:textColor="@color/text_01" android:ellipsize="end" android:id="@id/tv_ep" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:maxLines="1" android:layout_marginStart="4.0dip" app:layout_constraintEnd_toEndOf="@id/pb_download" app:layout_constraintStart_toEndOf="@id/tv_name" app:layout_constraintTop_toTopOf="@id/tv_name" app:layout_constraintVertical_chainStyle="packed" style="@style/style_medium_text" />
    <ProgressBar android:id="@id/pb_download" android:layout_width="0.0dip" android:layout_height="2.0dip" android:layout_marginBottom="19.0dip" android:max="100" android:progressDrawable="@drawable/bg_progress_bar_pause" android:layout_marginStart="8.0dip" android:layout_marginEnd="8.0dip" app:layout_constraintBottom_toBottomOf="@id/card_cover" app:layout_constraintEnd_toStartOf="@id/iv_state_bg" app:layout_constraintStart_toEndOf="@id/card_cover" style="?android:progressBarStyleHorizontal" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="10.0sp" android:textColor="@color/white_60" android:id="@id/tv_state_progress" android:layout_width="0.0dip" app:layout_constraintBottom_toBottomOf="@id/card_cover" app:layout_constraintEnd_toStartOf="@id/tv_time_or_status" app:layout_constraintStart_toStartOf="@id/pb_download" style="@style/style_regular_text" />
    <com.tn.lib.widget.TnTextView android:textSize="10.0sp" android:textColor="@color/white_80" android:ellipsize="end" android:id="@id/tv_time_or_status" android:layout_width="wrap_content" android:layout_marginTop="4.0dip" android:scaleType="centerCrop" android:text="@string/str_waiting" android:maxLines="1" app:layout_constraintBottom_toBottomOf="@id/card_cover" app:layout_constraintEnd_toEndOf="@id/pb_download" style="@style/style_regular_text" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_state_bg" android:layout_width="20.0dip" android:layout_height="20.0dip" android:layout_marginBottom="2.0dip" android:layout_marginEnd="12.0dip" app:layout_constraintBottom_toBottomOf="@id/card_cover" app:layout_constraintEnd_toEndOf="parent" app:srcCompat="@drawable/selector_download_list_status" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_more" android:paddingTop="8.0dip" android:paddingBottom="8.0dip" android:layout_width="wrap_content" android:layout_height="wrap_content" android:scaleType="centerCrop" android:paddingStart="5.0dip" android:paddingEnd="12.0dip" android:paddingVertical="8.0dip" app:layout_constraintBottom_toBottomOf="@id/tv_name" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="@id/tv_name" app:srcCompat="@drawable/ic_download_more" />
</androidx.constraintlayout.widget.ConstraintLayout>
