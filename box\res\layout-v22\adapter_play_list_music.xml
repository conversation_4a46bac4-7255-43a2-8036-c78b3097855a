<?xml version="1.0" encoding="utf-8"?>
<FrameLayout android:id="@id/item_root" android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <View android:id="@id/bg_gradient" android:background="@drawable/bg_category_gradient" android:layout_width="fill_parent" android:layout_height="84.0dip" />
    <androidx.appcompat.widget.LinearLayoutCompat android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="wrap_content">
        <View android:id="@id/divider" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="16.0dip" />
        <androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="84.0dip">
            <androidx.appcompat.widget.AppCompatTextView android:textSize="20.0sp" android:textColor="@color/text_01" android:gravity="start" android:id="@id/tvRankNum" android:layout_width="28.0dip" android:layout_marginStart="16.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" style="@style/style_import_text" />
            <com.google.android.material.imageview.ShapeableImageView android:id="@id/iv_cover" android:layout_width="120.0dip" android:layout_height="68.0dip" android:src="@mipmap/ic_default_video" android:scaleType="centerCrop" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toEndOf="@id/tvRankNum" app:layout_constraintTop_toTopOf="parent" app:layout_goneMarginStart="@dimen/dp_16" app:shapeAppearanceOverlay="@style/corner_style_4" />
            <com.tn.lib.view.CornerTextView android:id="@id/iv_playlist_page_corner" android:layout_width="wrap_content" android:layout_height="wrap_content" app:layout_constraintEnd_toEndOf="@id/iv_cover" app:layout_constraintTop_toTopOf="@id/iv_cover" />
            <androidx.appcompat.widget.AppCompatTextView android:textSize="14.0sp" android:textColor="@color/text_01" android:ellipsize="end" android:id="@id/tv_title" android:layout_width="0.0dip" android:layout_height="wrap_content" android:text="@string/no_network" android:lines="2" android:includeFontPadding="false" android:textAlignment="viewStart" android:layout_marginStart="8.0dip" android:layout_marginEnd="16.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/iv_cover" app:layout_constraintTop_toTopOf="@id/iv_cover" style="@style/style_medium_text" />
            <com.transsnet.downloader.widget.DownloadView android:gravity="center_vertical" android:id="@id/ll_download" android:background="@drawable/bg_btn_01_radius_4" android:paddingLeft="12.0dip" android:paddingRight="12.0dip" android:layout_width="wrap_content" android:layout_height="28.0dip" android:layout_marginStart="8.0dip" android:paddingHorizontal="12.0dip" app:layout_constraintBottom_toBottomOf="@id/iv_cover" app:layout_constraintStart_toEndOf="@id/iv_cover" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.appcompat.widget.LinearLayoutCompat>
</FrameLayout>
