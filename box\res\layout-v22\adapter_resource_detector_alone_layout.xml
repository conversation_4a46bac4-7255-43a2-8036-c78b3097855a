<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:id="@id/itemRoot" android:background="@drawable/bg_bg_02_4dp" android:paddingLeft="@dimen/dp_8" android:paddingRight="@dimen/dp_8" android:layout_width="fill_parent" android:layout_height="wrap_content" android:paddingHorizontal="@dimen/dp_8"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.transsnet.downloader.widget.DownloadView android:id="@id/innerIvDownload" android:layout_width="16.0dip" android:layout_height="16.0dip" app:iconSrc="@mipmap/movie_download_green" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent" app:tips_textColor="@color/main" />
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/text_01" android:ellipsize="end" android:id="@id/tvTitle" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginTop="7.0dip" android:maxLines="1" android:textAlignment="viewStart" android:layout_marginEnd="8.0dip" app:layout_constraintEnd_toStartOf="@id/innerIvDownload" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" style="@style/style_medium_text" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:ellipsize="end" android:id="@id/tvSize" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginBottom="7.0dip" android:maxLines="1" android:textAlignment="viewStart" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="@id/tvTitle" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tvTitle" />
</androidx.constraintlayout.widget.ConstraintLayout>
