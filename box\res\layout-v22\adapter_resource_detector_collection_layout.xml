<?xml version="1.0" encoding="utf-8"?>
<com.noober.background.view.BLConstraintLayout android:id="@id/itemRoot" android:paddingLeft="@dimen/dp_8" android:paddingRight="@dimen/dp_8" android:layout_width="fill_parent" android:layout_height="44.0dip" android:paddingHorizontal="@dimen/dp_8" app:bl_corners_radius="4.0dip" app:bl_solid_color="@color/white_20"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.transsnet.downloader.widget.DownloadView android:id="@id/innerIvDownload" android:layout_width="16.0dip" android:layout_height="16.0dip" app:iconSrc="@mipmap/movie_download_green" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent" app:tips_textColor="@color/main" />
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/text_01" android:ellipsize="end" android:id="@id/tvTitle" android:visibility="gone" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginTop="7.0dip" android:maxLines="1" android:layout_marginEnd="4.0dip" app:layout_constraintEnd_toStartOf="@id/innerIvDownload" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" style="@style/style_medium_text" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/white_60" android:ellipsize="end" android:id="@id/tvSize" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginBottom="7.0dip" android:maxLines="1" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="@id/tvTitle" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tvTitle" />
    <androidx.constraintlayout.widget.Group android:id="@id/group" android:visibility="visible" android:layout_width="wrap_content" android:layout_height="wrap_content" app:constraint_referenced_ids="innerIvDownload,tvTitle,tvSize" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="@dimen/sp_12" android:textStyle="bold" android:textColor="@color/text_01" android:id="@id/tvAll" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:drawablePadding="4.0dip" android:drawableEnd="@mipmap/movie_arrow_right" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" style="@style/style_medium_text" />
</com.noober.background.view.BLConstraintLayout>
