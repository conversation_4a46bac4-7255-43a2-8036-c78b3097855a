<?xml version="1.0" encoding="utf-8"?>
<FrameLayout android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.constraintlayout.widget.ConstraintLayout android:id="@id/clRoot" android:background="#ffffffff" android:layout_width="fill_parent" android:layout_height="54.0dip">
        <com.cloud.sdk.commonutil.widget.TranCircleImageView android:id="@id/ivIcon" android:layout_width="36.0dip" android:layout_height="36.0dip" app:bottomLeftRadiusYL="4.0dip" app:bottomRightRadiusYL="4.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:topLeftRadiusYL="4.0dip" app:topRightRadiusYL="4.0dip" />
        <TextView android:textSize="12.0sp" android:textStyle="bold" android:textColor="#ffffffff" android:gravity="center" android:id="@id/tvBtn" android:background="@drawable/ssp_bg_0052e2_4_4_4_4" android:paddingLeft="5.0dip" android:paddingRight="5.0dip" android:layout_width="wrap_content" android:layout_height="24.0dip" android:text="sadkfaks" android:lines="1" android:maxLength="20" android:layout_marginEnd="12.0dip" android:paddingHorizontal="5.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent" />
        <TextView android:textSize="14.0sp" android:textColor="#ff222222" android:ellipsize="end" android:id="@id/tvName" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginLeft="10.0dip" android:layout_marginRight="10.0dip" android:text="" android:lines="1" android:layout_marginHorizontal="10.0dip" app:layout_constraintBottom_toTopOf="@id/tvDescription" app:layout_constraintEnd_toStartOf="@id/tvBtn" app:layout_constraintStart_toEndOf="@id/ivIcon" app:layout_constraintTop_toTopOf="@id/ivIcon" />
        <TextView android:textSize="11.0sp" android:textColor="#ff787878" android:ellipsize="end" android:id="@id/tvDescription" android:visibility="visible" android:layout_width="0.0dip" android:layout_height="wrap_content" android:text="" android:singleLine="true" app:layout_constraintBottom_toBottomOf="@id/ivIcon" app:layout_constraintEnd_toEndOf="@id/tvName" app:layout_constraintStart_toStartOf="@id/tvName" app:layout_constraintTop_toBottomOf="@id/tvName" />
        <include android:id="@id/ad_flag" android:layout_width="wrap_content" android:layout_height="wrap_content" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" layout="@layout/include_ad_flag_no_close" />
        <com.cloud.hisavana.sdk.api.view.AdCloseView android:id="@id/ad_close_view" android:layout_width="@dimen/ad_badge_height" android:layout_height="@dimen/ad_badge_height" android:layout_marginEnd="4.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="@id/ad_flag" />
        <com.cloud.hisavana.sdk.api.view.StoreMarkView android:id="@id/ps_mark_view" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginBottom="3.0dip" android:layout_marginEnd="@dimen/hisavana_ad_dimen_4" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</FrameLayout>
