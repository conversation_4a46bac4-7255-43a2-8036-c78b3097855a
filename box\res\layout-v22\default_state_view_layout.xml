<?xml version="1.0" encoding="utf-8"?>
<androidx.appcompat.widget.LinearLayoutCompat android:gravity="center_horizontal" android:orientation="vertical" android:id="@id/stateRoot" android:paddingBottom="16.0dip" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.tn.lib.view.TitleLayout android:id="@id/llTitle" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="wrap_content" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/ivDefaultImage" android:layout_width="fill_parent" android:layout_height="170.0dip" android:src="@mipmap/ic_no_network" android:scaleType="fitCenter" />
    <TextView android:textColor="@color/white_60" android:gravity="center" android:id="@id/tvDesc" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="-20.0dip" android:text="@string/no_network" android:layout_marginStart="16.0dip" android:layout_marginEnd="16.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/ivDefaultImage" style="@style/style_regular_text" />
    <TextView android:textSize="12.0sp" android:textColor="@color/white_80" android:gravity="center" android:id="@id/tv_retry" android:background="@drawable/btn_state_view_bg" android:paddingLeft="12.0dip" android:paddingRight="12.0dip" android:layout_width="wrap_content" android:layout_height="32.0dip" android:layout_marginTop="12.0dip" android:minWidth="140.0dip" android:text="@string/home_retry_text" android:paddingHorizontal="12.0dip" style="@style/style_medium_text" />
</androidx.appcompat.widget.LinearLayoutCompat>
