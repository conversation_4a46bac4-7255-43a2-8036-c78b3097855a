<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:background="@drawable/libui_common_dialog_bg" android:paddingBottom="24.0dip" android:layout_width="280.0dip" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_back" android:paddingTop="12.0dip" android:paddingBottom="12.0dip" android:layout_width="wrap_content" android:layout_height="wrap_content" android:src="@mipmap/ic_close" android:paddingStart="12.0dip" android:paddingEnd="12.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_icon" android:background="@mipmap/ic_download_tips" android:layout_width="56.0dip" android:layout_height="56.0dip" android:layout_marginTop="24.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textColor="@color/text_01" android:gravity="center" android:id="@id/tv_tips" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="18.0dip" android:text="@string/intercept_dialog_tips" android:layout_marginStart="24.0dip" android:layout_marginEnd="24.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/iv_icon" />
    <View android:id="@id/btn_top" android:background="@drawable/bg_btn_01" android:layout_width="0.0dip" android:layout_height="36.0dip" android:layout_marginLeft="24.0dip" android:layout_marginTop="23.0dip" android:layout_marginRight="24.0dip" android:layout_marginHorizontal="24.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tv_tips" style="@style/style_medium_text" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/ivAdIcon" android:layout_width="wrap_content" android:layout_height="wrap_content" android:src="@mipmap/ic_ad" android:layout_marginEnd="4.0dip" app:layout_constraintBottom_toBottomOf="@id/btn_top" app:layout_constraintEnd_toStartOf="@id/tvAdWatchAVideo" app:layout_constraintHorizontal_chainStyle="packed" app:layout_constraintStart_toStartOf="@id/btn_top" app:layout_constraintTop_toTopOf="@id/btn_top" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textColor="@color/common_white" android:id="@id/tvAdWatchAVideo" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/str_watch_a_video" app:layout_constraintBottom_toBottomOf="@id/btn_top" app:layout_constraintEnd_toEndOf="@id/btn_top" app:layout_constraintStart_toEndOf="@id/ivAdIcon" app:layout_constraintTop_toTopOf="@id/btn_top" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/ivAdLoading" android:layout_width="15.0dip" android:layout_height="15.0dip" app:layout_constraintBottom_toBottomOf="@id/btn_top" app:layout_constraintEnd_toEndOf="@id/btn_top" app:layout_constraintStart_toStartOf="@id/btn_top" app:layout_constraintTop_toTopOf="@id/btn_top" />
    <TextView android:textSize="16.0sp" android:textColor="@color/text_01" android:gravity="center" android:id="@id/btn_bottom" android:background="@drawable/bg_invite_8dp" android:visibility="gone" android:layout_width="0.0dip" android:layout_height="36.0dip" android:layout_marginLeft="24.0dip" android:layout_marginTop="8.0dip" android:layout_marginRight="24.0dip" android:text="@string/str_invite_whatsapp_friends" android:drawableLeft="@drawable/ic_movie_share" android:drawablePadding="4.0dip" android:paddingStart="25.0dip" android:paddingEnd="25.0dip" android:layout_marginHorizontal="24.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/btn_top" style="@style/style_medium_text" />
</androidx.constraintlayout.widget.ConstraintLayout>
