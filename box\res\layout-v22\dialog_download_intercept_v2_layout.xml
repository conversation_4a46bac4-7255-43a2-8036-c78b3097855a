<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.appcompat.widget.LinearLayoutCompat android:orientation="vertical" android:id="@id/llContent" android:background="@drawable/ps_link_16_bg" android:paddingBottom="20.0dip" android:visibility="visible" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginLeft="40.0dip" android:layout_marginRight="40.0dip" android:layout_marginHorizontal="40.0dip">
        <androidx.appcompat.widget.AppCompatImageView android:layout_gravity="center_horizontal" android:id="@id/ivIcon" android:layout_width="40.0dip" android:layout_height="40.0dip" android:layout_marginTop="16.0dip" android:src="@mipmap/co_download" />
        <TextView android:textSize="16.0sp" android:textColor="@color/text_01" android:gravity="center" android:id="@id/tvTips" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginLeft="16.0dip" android:layout_marginTop="16.0dip" android:layout_marginRight="16.0dip" android:text="@string/co_download_dialog_title" android:layout_marginHorizontal="16.0dip" style="@style/style_regula_bigger_text" />
        <androidx.constraintlayout.widget.ConstraintLayout android:orientation="vertical" android:background="@drawable/ps_bg_app_8_bg" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginLeft="16.0dip" android:layout_marginTop="16.0dip" android:layout_marginRight="16.0dip" android:layout_marginHorizontal="16.0dip">
            <androidx.recyclerview.widget.RecyclerView android:id="@id/rv" android:layout_width="fill_parent" android:layout_height="wrap_content" android:overScrollMode="never" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
            <androidx.appcompat.widget.AppCompatImageView android:id="@id/ivRefresh" android:layout_width="16.0dip" android:layout_height="16.0dip" android:src="@mipmap/co_refresh" android:tint="@color/brand" android:layout_marginEnd="4.0dip" app:layout_constraintBottom_toBottomOf="@id/tvTryMore" app:layout_constraintEnd_toStartOf="@id/tvTryMore" app:layout_constraintHorizontal_chainStyle="packed" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="@id/tvTryMore" app:layout_constraintVertical_chainStyle="packed" />
            <TextView android:textColor="@color/brand" android:gravity="center" android:layout_gravity="center" android:id="@id/tvTryMore" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="8.0dip" android:layout_marginBottom="8.0dip" android:text="Try More" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/ivRefresh" app:layout_constraintTop_toBottomOf="@id/rv" />
        </androidx.constraintlayout.widget.ConstraintLayout>
        <androidx.appcompat.widget.LinearLayoutCompat android:orientation="horizontal" android:id="@id/llLine" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginLeft="16.0dip" android:layout_marginTop="12.0dip" android:layout_marginRight="16.0dip" android:layout_marginHorizontal="16.0dip">
            <View android:layout_gravity="center_vertical" android:background="@color/line_01" android:layout_width="0.0dip" android:layout_height="1.0dip" android:layout_weight="1.0" />
            <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/text_03" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginLeft="8.0dip" android:layout_marginRight="8.0dip" android:text="@string/member_guide_dialog_or" android:layout_marginHorizontal="8.0dip" />
            <View android:layout_gravity="center_vertical" android:background="@color/line_01" android:layout_width="0.0dip" android:layout_height="1.0dip" android:layout_weight="1.0" />
        </androidx.appcompat.widget.LinearLayoutCompat>
        <androidx.constraintlayout.widget.ConstraintLayout android:id="@id/cl2MemberInfo" android:background="@drawable/ps_bg_app_8_bg" android:visibility="visible" android:layout_width="fill_parent" android:layout_height="36.0dip" android:layout_marginLeft="16.0dip" android:layout_marginTop="8.0dip" android:layout_marginRight="16.0dip" android:layout_marginHorizontal="16.0dip">
            <TextView android:textSize="14.0sp" android:textColor="@color/text_01" android:id="@id/tvGetAd" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/member_guide_dialog_get_ad" android:layout_marginStart="4.0dip" android:layout_marginEnd="4.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toStartOf="@id/ivPremium" app:layout_constraintHorizontal_chainStyle="packed" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" style="@style/style_medium_text" />
            <androidx.appcompat.widget.AppCompatTextView android:textSize="10.0sp" android:textStyle="bold" android:textColor="#ff130f26" android:layout_gravity="end" android:id="@id/ivPremium" android:background="@drawable/bg_days_left" android:paddingLeft="4.0dip" android:paddingTop="2.0dip" android:paddingRight="4.0dip" android:paddingBottom="2.0dip" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/member_download_premium" android:drawablePadding="2.0dip" android:drawableStart="@mipmap/ic_premium" android:paddingHorizontal="4.0dip" android:paddingVertical="2.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/tvGetAd" app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>
        <androidx.constraintlayout.widget.ConstraintLayout android:id="@id/cl2Task" android:background="@drawable/ps_bg_app_8_bg" android:visibility="visible" android:layout_width="fill_parent" android:layout_height="36.0dip" android:layout_marginLeft="16.0dip" android:layout_marginTop="8.0dip" android:layout_marginRight="16.0dip" android:layout_marginHorizontal="16.0dip">
            <TextView android:textSize="14.0sp" android:textColor="@color/text_01" android:id="@id/tvCompleteTask" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/member_guide_dialog_complete_task" android:layout_marginStart="4.0dip" android:layout_marginEnd="4.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toStartOf="@id/ivTaskPremium" app:layout_constraintHorizontal_chainStyle="packed" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" style="@style/style_medium_text" />
            <androidx.appcompat.widget.AppCompatTextView android:textSize="10.0sp" android:textStyle="bold" android:textColor="#ff130f26" android:layout_gravity="end" android:id="@id/ivTaskPremium" android:background="@drawable/bg_days_left" android:paddingLeft="4.0dip" android:paddingTop="2.0dip" android:paddingRight="4.0dip" android:paddingBottom="2.0dip" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/member_download_premium" android:drawablePadding="2.0dip" android:drawableStart="@mipmap/ic_premium" android:paddingHorizontal="4.0dip" android:paddingVertical="2.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/tvCompleteTask" app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.appcompat.widget.LinearLayoutCompat>
    <androidx.appcompat.widget.AppCompatImageView android:layout_gravity="center_horizontal" android:id="@id/ivClose" android:layout_width="48.0dip" android:layout_height="48.0dip" android:layout_marginTop="24.0dip" android:src="@mipmap/co_close" />
</LinearLayout>
