<?xml version="1.0" encoding="utf-8"?>
<FrameLayout android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.constraintlayout.widget.ConstraintLayout android:layout_gravity="center" android:id="@id/clRoot" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginLeft="40.0dip" android:layout_marginRight="40.0dip" android:layout_marginHorizontal="40.0dip">
        <View android:id="@id/MeBg1" android:background="@drawable/me_bg_guide_dialog" android:layout_width="fill_parent" android:layout_height="132.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
        <androidx.appcompat.widget.AppCompatImageView android:layout_width="88.0dip" android:layout_height="92.0dip" android:layout_marginTop="10.0dip" android:src="@drawable/me_icon_premium_3" android:layout_marginEnd="9.0dip" app:layout_constraintEnd_toEndOf="@id/MeBg1" app:layout_constraintTop_toTopOf="@id/MeBg1" />
        <TextView android:textSize="20.0sp" android:textStyle="bold" android:textColor="@color/white" android:id="@id/tvTitle" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginLeft="24.0dip" android:layout_marginTop="24.0dip" android:layout_marginRight="24.0dip" android:text="@string/member_guide_dialog_title" android:layout_marginHorizontal="24.0dip" app:layout_constraintEnd_toEndOf="@id/MeBg1" app:layout_constraintStart_toStartOf="@id/MeBg1" app:layout_constraintTop_toTopOf="@id/MeBg1" />
        <TextView android:textSize="12.0sp" android:textColor="@color/white_60" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="8.0dip" android:text="@string/member_guide_dialog_title_2" app:layout_constraintStart_toStartOf="@id/tvTitle" app:layout_constraintTop_toBottomOf="@id/tvTitle" />
        <androidx.constraintlayout.widget.ConstraintLayout android:id="@id/MeBg2" android:background="@drawable/me_bg2_16_guide_dialog" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="108.0dip" app:layout_constraintEnd_toEndOf="@id/MeBg1" app:layout_constraintStart_toStartOf="@id/MeBg1" app:layout_constraintTop_toTopOf="@id/MeBg1">
            <androidx.recyclerview.widget.RecyclerView android:id="@id/rvMemberRights" android:layout_width="fill_parent" android:layout_height="wrap_content" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="@id/MeBg2" />
            <View android:id="@id/viewBtnBg" android:background="@drawable/me_bg_8_1" android:layout_width="0.0dip" android:layout_height="48.0dip" android:layout_marginLeft="24.0dip" android:layout_marginRight="24.0dip" android:layout_marginBottom="24.0dip" android:layout_marginHorizontal="24.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="@id/MeBg2" app:layout_constraintStart_toStartOf="@id/MeBg2" app:layout_constraintTop_toBottomOf="@id/rvMemberRights" />
            <androidx.appcompat.widget.AppCompatImageView android:id="@id/ivPremium" android:layout_width="wrap_content" android:layout_height="wrap_content" android:src="@drawable/me_icon_premium_2" app:layout_constraintBottom_toBottomOf="@id/viewBtnBg" app:layout_constraintEnd_toStartOf="@id/tvGetAd" app:layout_constraintHorizontal_chainStyle="packed" app:layout_constraintStart_toStartOf="@id/viewBtnBg" app:layout_constraintTop_toTopOf="@id/viewBtnBg" />
            <TextView android:textSize="14.0sp" android:textColor="#ff191f2b" android:id="@id/tvGetAd" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/member_guide_dialog_get_ad" android:layout_marginStart="4.0dip" app:layout_constraintBottom_toTopOf="@id/tvPay" app:layout_constraintEnd_toEndOf="@id/viewBtnBg" app:layout_constraintStart_toEndOf="@id/ivPremium" app:layout_constraintTop_toTopOf="@id/viewBtnBg" app:layout_constraintVertical_chainStyle="packed" style="@style/style_medium_text" />
            <TextView android:textSize="10.0sp" android:textColor="#ff191f2b" android:id="@id/tvPay" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/member_guide_dialog_get_per" app:layout_constraintBottom_toBottomOf="@id/viewBtnBg" app:layout_constraintStart_toStartOf="@id/tvGetAd" app:layout_constraintTop_toBottomOf="@id/tvGetAd" />
        </androidx.constraintlayout.widget.ConstraintLayout>
        <androidx.appcompat.widget.AppCompatImageView android:id="@id/ivClose" android:layout_width="36.0dip" android:layout_height="36.0dip" android:layout_marginTop="24.0dip" android:src="@drawable/me_icon_popup_close" app:layout_constraintEnd_toEndOf="@id/MeBg2" app:layout_constraintStart_toStartOf="@id/MeBg2" app:layout_constraintTop_toBottomOf="@id/MeBg2" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</FrameLayout>
