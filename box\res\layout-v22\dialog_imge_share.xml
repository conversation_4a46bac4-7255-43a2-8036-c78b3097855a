<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:background="@color/transparent" android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <View android:background="@drawable/libui_bottom_dialog_bg" android:layout_width="fill_parent" android:layout_height="287.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_close" android:layout_width="40.0dip" android:layout_height="40.0dip" android:src="@mipmap/web_ic_close" android:scaleType="center" android:layout_marginEnd="4.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <com.google.android.material.imageview.ShapeableImageView android:id="@id/iv_share" android:layout_width="64.0dip" android:layout_height="92.0dip" android:layout_marginTop="40.0dip" android:scaleType="fitCenter" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:shapeAppearanceOverlay="@style/corner_style_4" />
    <TextView android:textSize="14.0sp" android:textColor="@color/text_01" android:ellipsize="end" android:id="@id/tv_share_title" android:paddingTop="16.0dip" android:layout_width="fill_parent" android:layout_height="wrap_content" android:text="@string/player_share_friends" android:maxLines="1" android:textAllCaps="false" android:paddingStart="16.0dip" android:paddingEnd="16.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/iv_share" />
    <androidx.recyclerview.widget.RecyclerView android:id="@id/share_list" android:layout_width="fill_parent" android:layout_height="79.0dip" android:layout_marginLeft="16.0dip" android:layout_marginTop="24.0dip" android:layout_marginRight="16.0dip" android:layout_marginHorizontal="16.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintTop_toBottomOf="@id/tv_share_title" />
</androidx.constraintlayout.widget.ConstraintLayout>
