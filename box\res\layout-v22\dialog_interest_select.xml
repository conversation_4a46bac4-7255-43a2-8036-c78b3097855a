<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:orientation="vertical" android:background="@drawable/bg_module02_12_dialog" android:paddingLeft="@dimen/dp_16" android:paddingTop="@dimen/dimens_24" android:paddingRight="@dimen/dp_16" android:paddingBottom="@dimen/dimens_24" android:layout_width="fill_parent" android:layout_height="wrap_content" android:paddingHorizontal="@dimen/dp_16" android:paddingVertical="@dimen/dimens_24"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.appcompat.widget.AppCompatTextView android:textSize="20.0sp" android:textStyle="bold" android:textColor="@color/text_01" android:layout_gravity="center_horizontal" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/course_interest_title" />
    <ProgressBar android:layout_gravity="center_horizontal" android:id="@id/loadView" android:layout_width="24.0dip" android:layout_height="24.0dip" android:layout_marginTop="60.0dip" android:layout_marginBottom="60.0dip" android:indeterminateTint="@color/brand" android:layout_marginVertical="60.0dip" />
    <com.tn.lib.view.FlowLayout android:gravity="center" android:id="@id/interestLayout" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="@dimen/dp_16" app:flChildSpacing="@dimen/dp_12" app:flFlow="true" app:flRowSpacing="@dimen/dp_12" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="@dimen/sp_14" android:textColor="@color/white" android:gravity="center" android:layout_gravity="center_horizontal" android:id="@id/tvConfirm" android:background="@drawable/ad_shape_btn_bg" android:layout_width="fill_parent" android:layout_height="36.0dip" android:layout_marginTop="@dimen/dp_16" android:text="@string/str_confirm" style="@style/robot_medium" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="@dimen/sp_14" android:textColor="@color/text_02" android:layout_gravity="center_horizontal" android:id="@id/tvSkip" android:paddingLeft="@dimen/dp_16" android:paddingTop="@dimen/dp_8" android:paddingRight="@dimen/dp_16" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/ad_skip" android:includeFontPadding="false" android:paddingHorizontal="@dimen/dp_16" style="@style/robot_medium" />
</LinearLayout>
