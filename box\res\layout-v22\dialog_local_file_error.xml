<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:background="@drawable/libui_common_dialog_bg" android:paddingBottom="24.0dip" android:layout_width="280.0dip" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_close" android:paddingTop="12.0dip" android:paddingBottom="12.0dip" android:layout_width="wrap_content" android:layout_height="wrap_content" android:src="@mipmap/ic_close" android:paddingStart="12.0dip" android:paddingEnd="12.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textColor="@color/text_01" android:gravity="center" android:id="@id/tv_tips" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="26.0dip" android:text="@string/download_file_not_exist_tips" android:lineSpacingExtra="3.0dip" android:layout_marginStart="24.0dip" android:layout_marginEnd="24.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <View android:id="@id/btn_top" android:background="@drawable/bg_btn_01" android:layout_width="0.0dip" android:layout_height="36.0dip" android:layout_marginLeft="24.0dip" android:layout_marginTop="18.0dip" android:layout_marginRight="24.0dip" android:layout_marginHorizontal="24.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tv_tips" style="@style/style_medium_text" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textColor="@color/common_white" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/download_again" android:drawablePadding="5.0dip" android:drawableStart="@mipmap/ic_download_white" app:layout_constraintBottom_toBottomOf="@id/btn_top" app:layout_constraintEnd_toEndOf="@id/btn_top" app:layout_constraintStart_toStartOf="@id/btn_top" app:layout_constraintTop_toTopOf="@id/btn_top" />
    <TextView android:textSize="16.0sp" android:textColor="@color/text_01" android:gravity="center" android:id="@id/btn_bottom" android:background="@drawable/bg_invite_8dp" android:layout_width="0.0dip" android:layout_height="36.0dip" android:layout_marginLeft="24.0dip" android:layout_marginTop="8.0dip" android:layout_marginRight="24.0dip" android:text="@string/delete" android:drawablePadding="4.0dip" android:paddingStart="25.0dip" android:paddingEnd="25.0dip" android:layout_marginHorizontal="24.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/btn_top" style="@style/style_medium_text" />
</androidx.constraintlayout.widget.ConstraintLayout>
