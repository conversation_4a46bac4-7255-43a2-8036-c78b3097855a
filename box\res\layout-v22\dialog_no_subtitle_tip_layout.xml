<?xml version="1.0" encoding="utf-8"?>
<FrameLayout android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.constraintlayout.widget.ConstraintLayout android:background="@drawable/post_detail_shape_subtitle_list_dialog_main_bg" android:layout_width="280.0dip" android:layout_height="wrap_content">
        <TextView android:textSize="16.0sp" android:textColor="@color/white" android:gravity="center" android:id="@id/tvTip" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginLeft="24.0dip" android:layout_marginTop="24.0dip" android:layout_marginRight="24.0dip" android:text="@string/no_subtitle_tip" android:layout_marginHorizontal="24.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
        <androidx.appcompat.widget.LinearLayoutCompat android:orientation="horizontal" android:id="@id/llSelect" android:padding="12.0dip" android:layout_width="wrap_content" android:layout_height="wrap_content" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tvTip">
            <ImageView android:layout_gravity="center_vertical" android:id="@id/ivSelect" android:layout_width="12.0dip" android:layout_height="12.0dip" android:layout_marginEnd="4.0dip" app:srcCompat="@drawable/selector_download_group_check" />
            <TextView android:textColor="@color/subtitle_reset_text_unselected" android:layout_gravity="center_vertical" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/no_subtitle_again" />
        </androidx.appcompat.widget.LinearLayoutCompat>
        <TextView android:textSize="14.0sp" android:textColor="@color/white" android:gravity="center" android:id="@id/tvBtnCancel" android:background="@drawable/bg_btn_subtitle_08" android:layout_width="112.0dip" android:layout_height="36.0dip" android:layout_marginTop="4.0dip" android:layout_marginBottom="20.0dip" android:text="@string/cancel" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toStartOf="@id/tvBtnDownload" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/llSelect" />
        <TextView android:textSize="14.0sp" android:textColor="@color/white" android:gravity="center" android:id="@id/tvBtnDownload" android:background="@drawable/bg_btn_subtitle_download_08" android:layout_width="112.0dip" android:layout_height="36.0dip" android:text="@string/no_subtitle_download" app:layout_constraintBottom_toBottomOf="@id/tvBtnCancel" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/tvBtnCancel" app:layout_constraintTop_toTopOf="@id/tvBtnCancel" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</FrameLayout>
