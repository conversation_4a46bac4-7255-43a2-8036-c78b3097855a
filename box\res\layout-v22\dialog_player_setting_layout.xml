<?xml version="1.0" encoding="utf-8"?>
<FrameLayout android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.constraintlayout.widget.ConstraintLayout android:background="@drawable/land_play_8_bg" android:layout_width="fill_parent" android:layout_height="fill_parent" android:layout_marginTop="12.0dip" android:layout_marginBottom="12.0dip" android:layout_marginEnd="12.0dip" android:layout_marginVertical="12.0dip">
        <androidx.appcompat.widget.AppCompatImageView android:id="@id/ivIcon" android:layout_width="18.0dip" android:layout_height="18.0dip" android:layout_marginTop="16.0dip" android:src="@mipmap/ic_player_float" android:layout_marginStart="16.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
        <androidx.appcompat.widget.AppCompatTextView android:textSize="14.0sp" android:textColor="@color/white" android:id="@id/tvTitle" android:layout_width="0.0dip" android:layout_height="wrap_content" android:text="@string/long_video_setting_auto_minip_player" android:layout_marginStart="8.0dip" app:layout_constraintEnd_toStartOf="@id/switchBtn" app:layout_constraintStart_toEndOf="@id/ivIcon" app:layout_constraintTop_toTopOf="@id/ivIcon" style="@style/style_medium_text" />
        <com.tn.lib.view.SwitchButton android:id="@id/switchBtn" android:clickable="false" android:layout_width="40.0dip" android:layout_height="24.0dip" android:layout_marginEnd="16.0dip" app:layout_constraintBottom_toBottomOf="@id/tvTitle" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="@id/tvTitle" />
        <androidx.appcompat.widget.AppCompatTextView android:textSize="10.0sp" android:textColor="@color/white_80" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginTop="4.0dip" android:text="@string/long_video_setting_auto_minip_player_tip" android:layout_marginEnd="16.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="@id/ivIcon" app:layout_constraintTop_toBottomOf="@id/tvTitle" style="@style/style_regular_text" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</FrameLayout>
