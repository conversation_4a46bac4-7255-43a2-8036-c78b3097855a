<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:background="@drawable/libui_common_dialog_bg" android:paddingTop="28.0dip" android:paddingBottom="20.0dip" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginLeft="20.0dip" android:layout_marginRight="20.0dip" android:maxWidth="280.0dip" android:layout_marginHorizontal="20.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textColor="@color/text_01" android:gravity="center" android:id="@id/tv_title" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/qr_code_camera_permission_title" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" style="@style/style_medium_text" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textColor="@color/text_02" android:gravity="center" android:id="@id/tv_tips" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginLeft="16.0dip" android:layout_marginTop="12.0dip" android:layout_marginRight="16.0dip" android:text="@string/qr_code_camera_permission_tips" android:layout_marginHorizontal="16.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tv_title" style="@style/style_regula_bigger_text" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textColor="@color/text_01" android:gravity="center" android:id="@id/btn_cancel" android:background="@drawable/libui_sub_btn2_selector" android:layout_width="116.0dip" android:layout_height="36.0dip" android:layout_marginTop="16.0dip" android:text="@string/close" android:layout_marginStart="16.0dip" android:layout_marginEnd="4.0dip" app:layout_constraintEnd_toStartOf="@id/btn_ok" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tv_tips" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textColor="@color/common_white" android:gravity="center" android:id="@id/btn_ok" android:background="@drawable/libui_main_btn_normal" android:layout_width="116.0dip" android:layout_height="36.0dip" android:layout_marginTop="16.0dip" android:text="@string/ok" android:layout_marginStart="4.0dip" android:layout_marginEnd="16.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/btn_cancel" app:layout_constraintTop_toBottomOf="@id/tv_tips" />
</androidx.constraintlayout.widget.ConstraintLayout>
