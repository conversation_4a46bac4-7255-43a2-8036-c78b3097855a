<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.constraintlayout.widget.ConstraintLayout android:id="@id/iv_search_container" android:background="@drawable/bg_search_guide" android:layout_width="300.0dip" android:layout_height="36.0dip" android:layout_marginTop="10.0dip" android:layout_marginStart="12.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent">
        <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/text_03" android:ellipsize="end" android:gravity="start" android:layout_width="fill_parent" android:layout_height="wrap_content" android:text="@string/search_guide_title" android:drawablePadding="8.0dip" android:drawableStart="@mipmap/ic_search_movie" android:layout_marginStart="12.0dip" android:layout_marginEnd="80.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
        <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_ges" android:layout_width="40.0dip" android:layout_height="40.0dip" android:layout_marginTop="4.0dip" android:src="@mipmap/ic_guide_tip" android:layout_marginEnd="32.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
    <androidx.constraintlayout.widget.ConstraintLayout android:id="@id/bubble_container" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginTop="4.0dip" android:layout_marginStart="12.0dip" android:layout_marginEnd="32.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/iv_search_container">
        <com.tn.lib.view.RoundedArrowImageView android:id="@id/iv_rounded" android:layout_width="fill_parent" android:layout_height="0.0dip" android:src="@mipmap/ic_gradient_common_bg" android:scaleType="fitXY" app:arrowIVPositionOffset="200.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
        <TextView android:textSize="14.0sp" android:textColor="@color/white" android:paddingLeft="12.0dip" android:paddingTop="12.0dip" android:paddingRight="12.0dip" android:paddingBottom="12.0dip" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="8.0dip" android:text="@string/search_guide_tip" android:maxLines="1" android:paddingHorizontal="12.0dip" android:paddingVertical="12.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
