<?xml version="1.0" encoding="utf-8"?>
<androidx.appcompat.widget.LinearLayoutCompat android:gravity="center_vertical" android:background="?selectableItemBackground" android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/white" android:id="@id/tv_subtitle" android:layout_width="0.0dip" android:layout_marginTop="14.0dip" android:layout_marginBottom="14.0dip" android:layout_weight="1.0" android:textDirection="locale" android:layout_marginStart="16.0dip" android:breakStrategy="simple" style="@style/style_regula_bigger_text" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_check" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginStart="8.0dip" android:layout_marginEnd="16.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:srcCompat="@mipmap/ic_subtitle_download" />
    <ProgressBar android:id="@id/progress" android:layout_width="16.0dip" android:layout_height="16.0dip" android:layout_marginStart="8.0dip" android:layout_marginEnd="24.0dip" android:indeterminateTint="@color/brand" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
</androidx.appcompat.widget.LinearLayoutCompat>
