<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:gravity="center_horizontal" android:orientation="vertical" android:paddingLeft="40.0dip" android:paddingRight="40.0dip" android:layout_width="fill_parent" android:layout_height="wrap_content" android:paddingHorizontal="40.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.constraintlayout.widget.ConstraintLayout android:background="@drawable/bg_dialog_transfer_guide_16" android:layout_width="fill_parent" android:layout_height="wrap_content">
        <TextView android:textSize="18.0sp" android:textColor="@color/white" android:ellipsize="end" android:id="@id/title" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginLeft="16.0dip" android:layout_marginTop="24.0dip" android:layout_marginRight="16.0dip" android:text="How to transfer" android:layout_marginHorizontal="16.0dip" app:layout_constraintLeft_toLeftOf="parent" app:layout_constraintRight_toRightOf="parent" app:layout_constraintTop_toTopOf="parent" style="@style/style_extra_import_text" />
        <androidx.viewpager2.widget.ViewPager2 android:id="@id/guide_pager" android:layout_width="248.0dip" android:layout_height="164.0dip" android:layout_marginLeft="16.0dip" android:layout_marginTop="24.0dip" android:layout_marginRight="16.0dip" android:layout_marginHorizontal="16.0dip" app:layout_constraintLeft_toLeftOf="parent" app:layout_constraintRight_toRightOf="parent" app:layout_constraintTop_toBottomOf="@id/title" />
        <TextView android:textSize="14.0sp" android:textColor="@color/white" android:id="@id/guide_desc" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginTop="12.0dip" android:minHeight="80.0dip" android:text="Select the files you want to share, and click Send button." app:layout_constraintLeft_toLeftOf="@id/guide_pager" app:layout_constraintRight_toRightOf="@id/guide_pager" app:layout_constraintTop_toBottomOf="@id/guide_pager" />
        <TextView android:textSize="16.0sp" android:textColor="@color/white" android:gravity="center" android:id="@id/next" android:background="@drawable/libui_main_btn_selector" android:layout_width="160.0dip" android:layout_height="36.0dip" android:layout_marginTop="24.0dip" android:layout_marginBottom="24.0dip" android:text="@string/download_tab_transfer_tips_next" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintRight_toRightOf="@id/guide_desc" app:layout_constraintTop_toBottomOf="@id/guide_desc" />
        <com.tn.lib.view.indicator.CircleIndicator android:id="@id/indicator" android:layout_width="0.0dip" android:layout_height="8.0dip" app:layout_constraintBottom_toBottomOf="@id/next" app:layout_constraintLeft_toLeftOf="@id/guide_desc" app:layout_constraintRight_toLeftOf="@id/next" app:layout_constraintTop_toTopOf="@id/next" />
    </androidx.constraintlayout.widget.ConstraintLayout>
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/ivClose" android:layout_width="36.0dip" android:layout_height="36.0dip" android:layout_marginTop="30.0dip" android:src="@mipmap/ic_transfer_tips_dialog_close" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" />
</LinearLayout>
