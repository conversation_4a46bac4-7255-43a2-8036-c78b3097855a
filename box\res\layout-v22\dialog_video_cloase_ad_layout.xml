<?xml version="1.0" encoding="utf-8"?>
<FrameLayout android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.constraintlayout.widget.ConstraintLayout android:id="@id/closeAdLayout" android:background="@drawable/ad_shape_btn_05_bg" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginLeft="40.0dip" android:layout_marginRight="40.0dip" android:layout_marginHorizontal="40.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent">
        <TextView android:textSize="16.0sp" android:textColor="@color/black" android:id="@id/tvTitle" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="24.0dip" android:text="@string/ad_close_ad" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" style="@style/style_title_text" />
        <TextView android:textSize="16.0sp" android:textColor="#ff191f2b" android:gravity="center" android:id="@id/tvDesc" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginTop="16.0dip" android:text="@string/ad_close_desc" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tvTitle" />
        <TextView android:textSize="14.0sp" android:textColor="#ff191f2b" android:gravity="center" android:id="@id/tvCloseAd" android:background="@drawable/ad_shape_btn_06_bg" android:paddingTop="10.0dip" android:paddingBottom="10.0dip" android:layout_width="112.0dip" android:layout_height="wrap_content" android:layout_marginTop="16.0dip" android:layout_marginBottom="24.0dip" android:text="@string/ad_close_ad_2" android:layout_marginEnd="8.0dip" android:paddingVertical="10.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toStartOf="@id/tvResumeAd" app:layout_constraintHorizontal_chainStyle="packed" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tvDesc" />
        <TextView android:textSize="14.0sp" android:textColor="@color/white" android:gravity="center" android:id="@id/tvResumeAd" android:background="@drawable/ad_shape_btn_07_bg" android:paddingTop="10.0dip" android:paddingBottom="10.0dip" android:layout_width="112.0dip" android:layout_height="wrap_content" android:text="@string/ad_resume_ad" android:paddingVertical="10.0dip" app:layout_constraintBottom_toBottomOf="@id/tvCloseAd" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/tvCloseAd" app:layout_constraintTop_toTopOf="@id/tvCloseAd" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</FrameLayout>
