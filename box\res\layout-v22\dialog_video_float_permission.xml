<?xml version="1.0" encoding="utf-8"?>
<com.noober.background.view.BLConstraintLayout android:paddingBottom="24.0dip" android:layout_width="360.0dip" android:layout_height="fill_parent" app:bl_solid_color="@color/gray_dark_30_90"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_close" android:paddingTop="12.0dip" android:paddingBottom="12.0dip" android:layout_width="wrap_content" android:layout_height="wrap_content" android:src="@mipmap/ic_close" android:paddingStart="12.0dip" android:paddingEnd="12.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <com.noober.background.view.BLView android:id="@id/v_icon_bg" android:layout_width="56.0dip" android:layout_height="56.0dip" android:layout_marginTop="24.0dip" android:scaleType="center" app:bl_corners_radius="36.0dip" app:layout_constraintBottom_toTopOf="@id/tv_subtitle" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:layout_constraintVertical_chainStyle="packed" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_icon" android:layout_width="56.0dip" android:layout_height="56.0dip" android:src="@mipmap/video_float_ic_permission_img" android:scaleType="center" app:layout_constraintBottom_toBottomOf="@id/v_icon_bg" app:layout_constraintEnd_toEndOf="@id/v_icon_bg" app:layout_constraintStart_toStartOf="@id/v_icon_bg" app:layout_constraintTop_toTopOf="@id/v_icon_bg" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textColor="@color/text_01" android:gravity="center" android:id="@id/tv_subtitle" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginLeft="16.0dip" android:layout_marginTop="16.0dip" android:layout_marginRight="16.0dip" android:text="@string/video_float_tips_v2" android:layout_marginHorizontal="16.0dip" app:layout_constraintBottom_toTopOf="@id/tv_button" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/v_icon_bg" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textColor="@color/white" android:gravity="center" android:id="@id/tv_button" android:background="@drawable/bg_btn_01" android:layout_width="0.0dip" android:layout_height="43.0dip" android:layout_marginLeft="16.0dip" android:layout_marginTop="24.0dip" android:layout_marginRight="16.0dip" android:text="@string/video_float_enable" android:layout_marginHorizontal="16.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tv_subtitle" style="@style/style_title_text" />
</com.noober.background.view.BLConstraintLayout>
