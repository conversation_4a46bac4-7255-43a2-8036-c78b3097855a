<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:id="@id/ad_unit" android:paddingLeft="8.0dip" android:paddingRight="8.0dip" android:layout_width="fill_parent" android:layout_height="wrap_content" android:paddingHorizontal="8.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.hisavana.mediation.ad.TStoreMarkView android:id="@id/store_mark_view" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_alignParentTop="true" android:layout_alignParentStart="true" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <com.hisavana.mediation.ad.TAdChoicesView android:id="@id/adChoicesView" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" app:layout_constraintStart_toEndOf="@id/store_mark_view" app:layout_constraintTop_toTopOf="@id/store_mark_view" />
    <androidx.cardview.widget.CardView android:id="@id/ad_card_view" android:layout_width="wrap_content" android:layout_height="wrap_content" app:cardCornerRadius="12.0dip" app:cardElevation="0.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/store_mark_view">
        <com.hisavana.mediation.ad.TIconView android:id="@id/native_ad_icon" android:layout_width="52.0dip" android:layout_height="52.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/store_mark_view" />
    </androidx.cardview.widget.CardView>
    <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textColor="@color/text_01" android:ellipsize="end" android:id="@id/native_ad_title" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginBottom="5.0dip" android:lines="1" android:layout_marginStart="8.0dip" android:layout_marginEnd="4.0dip" app:layout_constraintEnd_toStartOf="@id/call_to_action" app:layout_constraintStart_toEndOf="@id/ad_card_view" app:layout_constraintTop_toTopOf="@id/ad_card_view" style="@style/robot_medium" />
    <androidx.appcompat.widget.AppCompatImageView android:layout_gravity="center_vertical" android:id="@id/ivStar" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="2.0dip" android:src="@drawable/ad_link_star" app:layout_constraintStart_toStartOf="@id/native_ad_title" app:layout_constraintTop_toBottomOf="@id/native_ad_title" />
    <TextView android:textSize="12.0sp" android:textColor="@color/text_02" android:id="@id/tvStarNum" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginStart="2.0dip" app:layout_constraintBottom_toBottomOf="@id/ivStar" app:layout_constraintStart_toEndOf="@id/ivStar" app:layout_constraintTop_toTopOf="@id/ivStar" />
    <View android:id="@id/tvLine" android:background="@color/ad_line" android:layout_width="1.0dip" android:layout_height="8.0dip" android:layout_marginStart="4.0dip" app:layout_constraintBottom_toBottomOf="@id/ivStar" app:layout_constraintStart_toEndOf="@id/tvStarNum" app:layout_constraintTop_toTopOf="@id/ivStar" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/ivDownloadNum" android:layout_width="wrap_content" android:layout_height="wrap_content" android:src="@mipmap/co_download_num" android:layout_marginStart="4.0dip" app:layout_constraintBottom_toBottomOf="@id/ivStar" app:layout_constraintStart_toEndOf="@id/tvLine" app:layout_constraintTop_toTopOf="@id/ivStar" />
    <TextView android:textSize="12.0sp" android:textColor="@color/text_02" android:id="@id/tvSizeNum" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginStart="2.0dip" app:layout_constraintBottom_toBottomOf="@id/ivStar" app:layout_constraintStart_toEndOf="@id/ivDownloadNum" app:layout_constraintTop_toTopOf="@id/ivStar" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/text_02" android:ellipsize="end" android:id="@id/native_ad_body" android:layout_width="0.0dip" android:layout_height="wrap_content" android:maxLines="1" android:layout_marginStart="8.0dip" android:layout_marginEnd="4.0dip" app:layout_constraintBottom_toBottomOf="@id/ad_card_view" app:layout_constraintEnd_toStartOf="@id/call_to_action" app:layout_constraintStart_toEndOf="@id/ad_card_view" />
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/white" android:gravity="center" android:layout_gravity="center_vertical" android:id="@id/call_to_action" android:background="@drawable/libui_main_btn_normal" android:padding="8.0dip" android:layout_width="wrap_content" android:layout_height="36.0dip" android:textAllCaps="false" app:layout_constraintBottom_toBottomOf="@id/ad_card_view" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="@id/ad_card_view" />
</androidx.constraintlayout.widget.ConstraintLayout>
