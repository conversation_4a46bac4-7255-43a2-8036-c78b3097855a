<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <View android:id="@id/bgView" android:layout_width="fill_parent" android:layout_height="0.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="14.0sp" android:textColor="@color/text_01" android:ellipsize="end" android:gravity="center_vertical" android:id="@id/textView" android:layout_width="0.0dip" android:layout_height="wrap_content" android:minHeight="50.0dip" android:maxLines="2" android:includeFontPadding="false" android:layout_marginStart="@dimen/dp_12" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" style="@style/style_regular_text" />
    <androidx.appcompat.widget.AppCompatCheckBox android:id="@id/radioButton" android:clickable="false" android:layout_width="16.0dip" android:layout_height="16.0dip" android:button="@drawable/selector_green_radio" android:layout_marginEnd="@dimen/dp_12" app:layout_constraintBottom_toBottomOf="@id/textView" app:layout_constraintEnd_toEndOf="@id/textView" app:layout_constraintTop_toTopOf="@id/textView" />
    <View android:id="@id/divider" android:background="@color/border" android:layout_width="fill_parent" android:layout_height="1.0px" android:layout_marginLeft="@dimen/dp_12" android:layout_marginRight="@dimen/dp_12" android:layout_marginHorizontal="@dimen/dp_12" app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>
