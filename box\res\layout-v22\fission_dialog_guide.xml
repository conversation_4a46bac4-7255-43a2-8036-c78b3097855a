<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="fill_parent" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintTop_toTopOf="parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/img" android:layout_width="290.0dip" android:layout_height="280.0dip" android:src="@mipmap/fission_guide_bg" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <TextView android:textSize="24.0sp" android:textColor="@color/color_003325" android:id="@id/tv_title" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="48.0dip" android:text="@string/fission_invite_earn" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" style="@style/style_title_text" />
    <TextView android:textSize="14.0sp" android:textColor="@color/color_003325" android:id="@id/tv_invite_desc" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="80.0dip" android:text="@string/fission_invite_earn_desc" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" style="@style/style_medium_small_text" />
    <TextView android:textSize="32.0sp" android:textColor="@color/color_07B84E" android:id="@id/tv_invite_earn" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="110.0dip" android:text="35 DAYS" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" style="@style/style_import_text" />
    <TextView android:textSize="24.0sp" android:textColor="@color/color_07B84E" android:id="@id/tv_invite_earn_desc" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="-6.0dip" android:text="@string/fission_get_premium_desc" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tv_invite_earn" style="@style/style_import_text" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="14.0sp" android:textColor="@color/color_08C962" android:gravity="center" android:id="@id/tv_join" android:background="@color/white" android:layout_width="0.0dip" android:layout_height="40.0dip" android:layout_marginLeft="21.0dip" android:layout_marginTop="115.0dip" android:layout_marginRight="21.0dip" android:layout_marginBottom="16.0dip" android:text="@string/fission_join_now" android:textAllCaps="false" android:layout_marginHorizontal="21.0dip" app:layout_constraintBottom_toBottomOf="@id/img" app:layout_constraintEnd_toEndOf="@id/img" app:layout_constraintStart_toStartOf="@id/img" style="@style/style_import_text" />
    <androidx.appcompat.widget.AppCompatImageButton android:id="@id/iv_close" android:background="@null" android:padding="24.0dip" android:layout_width="wrap_content" android:layout_height="wrap_content" android:src="@mipmap/fission_guide_close" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/img" />
</androidx.constraintlayout.widget.ConstraintLayout>
