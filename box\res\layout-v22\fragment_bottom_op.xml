<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.transsion.home.view.BlurredSectorView android:id="@id/trending_bottom_bg" android:layout_width="fill_parent" android:layout_height="300.0dip" android:layout_marginBottom="48.0dip" android:layout_marginStart="-100.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" />
    <FrameLayout android:id="@id/bottom_op_container" android:layout_width="fill_parent" android:layout_height="fill_parent" />
    <androidx.viewpager2.widget.ViewPager2 android:id="@id/viewPager" android:layout_width="fill_parent" android:layout_height="0.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <androidx.constraintlayout.widget.ConstraintLayout android:gravity="center_vertical" android:orientation="horizontal" android:id="@id/bottom_op_search_liner" android:paddingLeft="@dimen/dp_12" android:paddingRight="@dimen/dp_12" android:clipChildren="false" android:layout_width="fill_parent" android:layout_height="wrap_content" android:paddingHorizontal="@dimen/dp_12" app:layout_constraintTop_toTopOf="parent">
        <androidx.appcompat.widget.AppCompatImageView android:id="@id/bottom_op_mb_logo" android:layout_width="30.0dip" android:layout_height="36.0dip" app:layout_constraintBottom_toBottomOf="@id/bottom_op_search_text" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="@id/bottom_op_search_text" app:srcCompat="@mipmap/ic_home_mb_logo" />
        <com.tn.lib.widget.TnTextView android:textSize="@dimen/text_size_16" android:textColor="@color/white_80" android:gravity="start|center" android:id="@id/bottom_op_search_text" android:background="@drawable/bg_radius_4_color_white" android:layout_width="0.0dip" android:layout_height="36.0dip" android:layout_marginTop="4.0dip" android:text="@string/search_hint_music" android:singleLine="true" android:drawablePadding="8.0dip" android:paddingStart="12.0dip" android:paddingEnd="12.0dip" android:layout_marginStart="12.0dip" app:drawableStartCompat="@drawable/ic_search_fit_dark" app:drawableTint="@color/white_80" app:layout_constraintEnd_toStartOf="@id/bottom_op_search_gamestore" app:layout_constraintStart_toEndOf="@id/bottom_op_mb_logo" app:layout_constraintTop_toTopOf="parent" />
        <FrameLayout android:id="@id/bottom_op_search_gamestore" android:visibility="gone" android:layout_width="40.0dip" android:layout_height="0.0dip" android:scaleType="fitCenter" android:layout_marginStart="16.0dip" app:layout_constraintBottom_toBottomOf="@id/bottom_op_search_text" app:layout_constraintEnd_toStartOf="@id/bottom_op_search_appstore" app:layout_constraintStart_toEndOf="@id/bottom_op_search_text" app:layout_constraintTop_toTopOf="@id/bottom_op_search_text" />
        <androidx.appcompat.widget.AppCompatImageView android:id="@id/bottom_op_search_appstore" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginStart="16.0dip" app:layout_constraintBottom_toBottomOf="@id/bottom_op_search_text" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/bottom_op_search_gamestore" app:layout_constraintTop_toTopOf="@id/bottom_op_search_text" app:srcCompat="@mipmap/ic_play_store" app:tint="@color/text_01" />
    </androidx.constraintlayout.widget.ConstraintLayout>
    <net.lucode.hackware.magicindicator.MagicIndicator android:id="@id/magicIndicator" android:background="@android:color/transparent" android:layout_width="0.0dip" android:layout_height="34.0dip" android:layout_marginTop="4.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/bottom_op_search_liner" />
    <View android:id="@id/viewLine" android:background="@color/line_01" android:visibility="gone" android:layout_width="0.0dip" android:layout_height="1.0dip" app:layout_constraintEnd_toEndOf="@id/magicIndicator" app:layout_constraintStart_toStartOf="@id/magicIndicator" app:layout_constraintTop_toBottomOf="@id/magicIndicator" />
</androidx.constraintlayout.widget.ConstraintLayout>
