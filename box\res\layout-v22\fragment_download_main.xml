<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:orientation="vertical" android:id="@id/clRootView" android:background="@color/bg_01" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <ImageView android:id="@id/v_top_bg" android:layout_width="0.0dip" android:layout_height="wrap_content" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_back" android:paddingTop="8.0dip" android:paddingBottom="8.0dip" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:paddingStart="12.0dip" android:paddingEnd="8.0dip" android:paddingVertical="8.0dip" app:layout_constraintBottom_toBottomOf="@id/tv_title" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="@id/tv_title" app:srcCompat="@mipmap/base_back_black" />
    <com.tn.lib.widget.TnTextView android:textSize="18.0sp" android:textColor="@color/text_01" android:id="@id/tv_title" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="12.5dip" android:text="@string/str_downloads" app:layout_constraintStart_toEndOf="@id/iv_back" app:layout_constraintTop_toBottomOf="@id/v_top_bg" app:layout_goneMarginStart="12.0dip" style="@style/style_extra_import_text" />
    <com.transsion.baseui.widget.GradientTextView android:gravity="center_vertical" android:id="@id/tv_transfer" android:paddingLeft="12.0dip" android:paddingRight="12.0dip" android:layout_width="wrap_content" android:layout_height="30.0dip" android:text="@string/download_tab_name_transfer" android:drawablePadding="4.0dip" android:layout_marginStart="12.0dip" android:layout_marginEnd="12.0dip" android:paddingHorizontal="12.0dip" app:bl_corners_radius="15.0dip" app:bl_solid_color="@color/white_10" app:drawableStartCompat="@mipmap/ic_transfer_enter" app:gradientTvCenterColor="@color/brand_new_gradient_center" app:gradientTvEndColor="@color/brand_new_gradient_end" app:gradientTvStartColor="@color/brand_new_gradient_start" app:layout_constraintBottom_toBottomOf="@id/tv_title" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="@id/tv_title" style="@style/style_medium_text" />
    <View android:id="@id/v_line" android:background="@color/line_01" android:layout_width="fill_parent" android:layout_height="1.0dip" android:layout_marginTop="12.5dip" app:layout_constraintTop_toBottomOf="@id/tv_title" />
    <FrameLayout android:id="@id/fl_container" android:layout_width="fill_parent" android:layout_height="0.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintTop_toBottomOf="@id/v_line" />
</androidx.constraintlayout.widget.ConstraintLayout>
