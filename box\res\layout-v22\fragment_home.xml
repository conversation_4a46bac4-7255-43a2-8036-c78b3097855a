<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.transsion.home.view.BlurredSectorView android:id="@id/trending_bottom_bg" android:layout_width="300.0dip" android:layout_height="300.0dip" android:layout_marginBottom="48.0dip" android:layout_marginStart="-100.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" />
    <androidx.viewpager2.widget.ViewPager2 android:id="@id/view_pager" android:layout_width="fill_parent" android:layout_height="0.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <androidx.constraintlayout.widget.ConstraintLayout android:gravity="center_vertical" android:orientation="horizontal" android:id="@id/ll_tab" android:paddingLeft="@dimen/dp_12" android:paddingRight="@dimen/dp_12" android:clipChildren="false" android:layout_width="fill_parent" android:layout_height="wrap_content" android:paddingHorizontal="@dimen/dp_12" app:layout_constraintTop_toTopOf="parent">
        <FrameLayout android:id="@id/search_left_container" android:layout_width="wrap_content" android:layout_height="36.0dip" android:layout_marginEnd="12.0dip" app:layout_constraintBottom_toBottomOf="@id/tv_search" app:layout_constraintEnd_toStartOf="@id/tv_search" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="@id/tv_search">
            <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_mb_logo" android:layout_width="30.0dip" android:layout_height="fill_parent" app:srcCompat="@mipmap/ic_home_mb_logo" />
            <FrameLayout android:id="@id/flPhoneCenter" android:visibility="gone" android:layout_width="40.0dip" android:layout_height="fill_parent" android:scaleType="fitCenter" />
        </FrameLayout>
        <com.tn.lib.widget.TnTextView android:textSize="@dimen/text_size_16" android:textColor="@color/white_80" android:gravity="start|center" android:id="@id/tv_search" android:background="@drawable/bg_radius_4_color_white" android:layout_width="0.0dip" android:layout_height="36.0dip" android:layout_marginTop="4.0dip" android:text="@string/search_hint_input" android:singleLine="true" android:drawablePadding="8.0dip" android:paddingStart="12.0dip" android:paddingEnd="12.0dip" app:drawableStartCompat="@drawable/ic_search_fit_dark" app:drawableTint="@color/white_80" app:layout_constraintEnd_toStartOf="@id/flGameCenter" app:layout_constraintStart_toEndOf="@id/search_left_container" app:layout_constraintTop_toTopOf="parent" style="@style/style_regular_text" />
        <FrameLayout android:id="@id/flGameCenter" android:visibility="gone" android:layout_width="40.0dip" android:layout_height="0.0dip" android:scaleType="fitCenter" android:layout_marginStart="16.0dip" app:layout_constraintBottom_toBottomOf="@id/tv_search" app:layout_constraintEnd_toStartOf="@id/ivUpdateApp" app:layout_constraintStart_toEndOf="@id/tv_search" app:layout_constraintTop_toTopOf="@id/tv_search" />
        <androidx.appcompat.widget.AppCompatImageView android:id="@id/ivUpdateApp" android:paddingLeft="2.0dip" android:paddingTop="4.0dip" android:paddingRight="2.0dip" android:paddingBottom="4.0dip" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginStart="16.0dip" android:paddingHorizontal="2.0dip" android:paddingVertical="4.0dip" app:layout_constraintBottom_toBottomOf="@id/tv_search" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/flGameCenter" app:layout_constraintTop_toTopOf="@id/tv_search" app:srcCompat="@drawable/ic_update_app" />
        <TextSwitcher android:id="@id/text_switcher" android:layout_width="0.0dip" android:layout_height="wrap_content" android:textAlignment="viewStart" android:paddingStart="36.0dip" android:paddingEnd="12.0dip" app:layout_constraintBottom_toBottomOf="@id/tv_search" app:layout_constraintEnd_toEndOf="@id/tv_search" app:layout_constraintStart_toStartOf="@id/tv_search" app:layout_constraintTop_toTopOf="@id/tv_search" />
    </androidx.constraintlayout.widget.ConstraintLayout>
    <net.lucode.hackware.magicindicator.MagicIndicator android:id="@id/magic_indicator" android:layout_width="fill_parent" android:layout_height="34.0dip" android:layout_marginTop="4.0dip" android:layout_marginBottom="8.0dip" app:layout_constraintTop_toBottomOf="@id/ll_tab" />
</androidx.constraintlayout.widget.ConstraintLayout>
