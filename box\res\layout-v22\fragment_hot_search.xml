<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView android:id="@id/search_hot_root_scroll" android:paddingTop="12.0dip" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.constraintlayout.widget.ConstraintLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="fill_parent" app:layout_constraintTop_toTopOf="parent">
        <androidx.constraintlayout.widget.ConstraintLayout android:id="@id/rl_tips" android:paddingLeft="12.0dip" android:paddingRight="12.0dip" android:layout_width="fill_parent" android:layout_height="wrap_content" android:paddingHorizontal="12.0dip" app:layout_constraintTop_toTopOf="parent">
            <TextView android:textSize="16.0sp" android:textColor="@color/white" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/search_recent" android:fontFamily="@font/mulish_semi_bold" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" style="@style/style_import_text" />
            <TextView android:textSize="14.0sp" android:textColor="@color/white_80" android:id="@id/tv_clear" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/search_clear" android:drawablePadding="2.0dip" android:paddingStart="15.0dip" app:drawableStartCompat="@drawable/icon_history_clear" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent" />
            <androidx.recyclerview.widget.RecyclerView android:id="@id/rv_history" android:layout_width="fill_parent" android:layout_height="wrap_content" android:overScrollMode="never" android:nestedScrollingEnabled="false" app:layout_constraintTop_toBottomOf="@id/tv_clear" />
            <View android:layout_width="fill_parent" android:layout_height="20.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/rv_history" />
        </androidx.constraintlayout.widget.ConstraintLayout>
        <androidx.constraintlayout.widget.ConstraintLayout android:id="@id/search_hot_everyone_linear" android:visibility="invisible" android:layout_width="fill_parent" android:layout_height="wrap_content" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/rl_tips">
            <ImageView android:id="@id/search_hot_everyone_title_image" android:layout_width="@dimen/dimens_24" android:layout_height="@dimen/dimens_24" android:src="@drawable/ic_hot_everyone" android:contentDescription="@string/everyone_search_title" android:layout_marginStart="12.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
            <TextView android:textSize="16.0sp" android:textColor="@color/white" android:id="@id/search_hot_everyone_title_text" android:text="@string/everyone_search_title" app:layout_constraintBottom_toBottomOf="@id/search_hot_everyone_title_image" app:layout_constraintStart_toEndOf="@id/search_hot_everyone_title_image" app:layout_constraintTop_toTopOf="@id/search_hot_everyone_title_image" style="@style/style_import_text" />
            <androidx.recyclerview.widget.RecyclerView android:id="@id/everyone_search_rv_history" android:paddingLeft="12.0dip" android:paddingRight="12.0dip" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="8.0dip" android:overScrollMode="never" android:nestedScrollingEnabled="false" android:paddingHorizontal="12.0dip" app:layout_constraintLeft_toLeftOf="parent" app:layout_constraintTop_toBottomOf="@id/search_hot_everyone_title_image" />
        </androidx.constraintlayout.widget.ConstraintLayout>
        <com.hisavana.mediation.ad.TAdNativeView android:id="@id/tAdNativeView" android:paddingLeft="12.0dip" android:paddingRight="12.0dip" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="wrap_content" android:paddingHorizontal="12.0dip" app:layout_constraintTop_toBottomOf="@id/search_hot_everyone_linear" />
        <RelativeLayout android:gravity="center_vertical" android:orientation="horizontal" android:id="@id/search_hot_rank_magic_indicator_ll" android:visibility="invisible" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="12.0dip" app:layout_constraintTop_toBottomOf="@id/search_hot_everyone_linear">
            <ImageView android:id="@id/search_hot_rank_magic_indicator_image" android:layout_width="20.0dip" android:layout_height="20.0dip" android:layout_marginTop="8.0dip" android:src="@drawable/ic_hot_tab" android:contentDescription="@string/everyone_search_title" android:layout_marginStart="12.0dip" />
            <net.lucode.hackware.magicindicator.MagicIndicator android:id="@id/search_hot_rank_magic_indicator" android:layout_width="wrap_content" android:layout_height="36.0dip" android:layout_marginStart="-6.0dip" android:layout_toEndOf="@id/search_hot_rank_magic_indicator_image" />
        </RelativeLayout>
        <View android:id="@id/search_hot_rank_indicator_linear" android:background="@color/ad_line" android:layout_width="fill_parent" android:layout_height="1.0px" android:layout_marginLeft="12.0dip" android:layout_marginRight="12.0dip" android:layout_marginHorizontal="12.0dip" app:layout_constraintTop_toBottomOf="@id/search_hot_rank_magic_indicator_ll" />
        <androidx.viewpager2.widget.ViewPager2 android:id="@id/search_hot_rank_view_pager" android:clipToPadding="true" android:layout_width="fill_parent" android:layout_height="wrap_content" app:layout_constraintTop_toBottomOf="@id/search_hot_rank_indicator_linear" />
        <ProgressBar android:id="@id/progress_bar" android:layout_width="23.0dip" android:layout_height="23.0dip" android:layout_marginTop="60.0dip" android:indeterminateTint="@color/brand" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/rl_tips" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.core.widget.NestedScrollView>
