<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="76.0dip" android:layout_marginLeft="12.0dip" android:layout_marginRight="12.0dip" android:layout_marginHorizontal="12.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.google.android.material.imageview.ShapeableImageView android:id="@id/view1" android:background="@color/module_01" android:layout_width="96.0dip" android:layout_height="0.0dip" android:layout_marginTop="16.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toStartOf="@id/view2" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:shapeAppearanceOverlay="@style/corner_style_4" />
    <com.google.android.material.imageview.ShapeableImageView android:id="@id/view2" android:background="@color/module_01" android:layout_width="0.0dip" android:layout_height="10.0dip" android:layout_marginStart="8.0dip" app:layout_constraintBottom_toTopOf="@id/view3" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/view1" app:layout_constraintTop_toTopOf="parent" app:layout_constraintVertical_chainStyle="packed" app:shapeAppearanceOverlay="@style/corner_style_4" />
    <com.google.android.material.imageview.ShapeableImageView android:id="@id/view3" android:background="@color/module_01" android:layout_width="0.0dip" android:layout_height="10.0dip" android:layout_marginTop="8.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toStartOf="@id/view4" app:layout_constraintStart_toStartOf="@id/view2" app:layout_constraintTop_toBottomOf="@id/view2" app:shapeAppearanceOverlay="@style/corner_style_4" />
    <View android:id="@id/view4" android:layout_width="0.0dip" android:layout_height="0.0dip" app:layout_constraintBottom_toBottomOf="@id/view3" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/view3" app:layout_constraintTop_toTopOf="@id/view3" />
</androidx.constraintlayout.widget.ConstraintLayout>
