<?xml version="1.0" encoding="utf-8"?>
<FrameLayout android:background="@color/bg_01" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <TextView android:textColor="@color/text_01" android:layout_gravity="start" android:layout_margin="12.0dip" android:text="@string/downloaded" style="@style/style_regula_bigger_text" />
    <androidx.appcompat.widget.AppCompatImageView android:layout_gravity="end" android:id="@id/iv_close" android:layout_width="48.0dip" android:layout_height="48.0dip" android:src="@mipmap/libui_ic_close_dialog" android:scaleType="center" />
    <View android:background="@color/line_01" android:layout_width="fill_parent" android:layout_height="1.0dip" android:layout_marginTop="47.0dip" />
    <androidx.recyclerview.widget.RecyclerView android:id="@id/recycler_view" android:layout_width="fill_parent" android:layout_height="fill_parent" android:layout_marginLeft="4.0dip" android:layout_marginTop="48.0dip" android:layout_marginRight="4.0dip" android:layout_marginHorizontal="4.0dip" />
</FrameLayout>
