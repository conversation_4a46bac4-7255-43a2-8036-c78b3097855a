<?xml version="1.0" encoding="utf-8"?>
<androidx.appcompat.widget.LinearLayoutCompat android:orientation="vertical" android:background="@color/yellow_dark_10" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.constraintlayout.widget.ConstraintLayout android:id="@id/titleLayout" android:background="@color/gray_dark_00" android:layout_width="fill_parent" android:layout_height="wrap_content">
        <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_back" android:visibility="gone" android:layout_width="44.0dip" android:layout_height="44.0dip" android:src="@mipmap/icon_white_back" android:scaleType="center" android:layout_marginStart="8.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:shapeAppearanceOverlay="@style/ImgRoundedStyle" />
        <androidx.appcompat.widget.AppCompatTextView android:textSize="@dimen/sp_16" android:textStyle="bold" android:textColor="@color/white" android:gravity="center" android:id="@id/iv_premium" android:layout_width="wrap_content" android:layout_height="@dimen/dimens_48" android:text="@string/tab_member" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
        <LinearLayout android:gravity="center_vertical" android:orientation="horizontal" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginEnd="16.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent">
            <androidx.appcompat.widget.AppCompatTextView android:textSize="@dimen/sp_14" android:textColor="@color/white_60" android:gravity="right|center" android:id="@id/tv_promo_code" android:layout_width="wrap_content" android:layout_height="@dimen/dimens_48" android:text="@string/member_promo_code_title" />
            <ImageView android:layout_width="12.0dip" android:layout_height="12.0dip" android:src="@mipmap/ic_arrow_right" android:scaleType="fitXY" />
        </LinearLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
    <androidx.core.widget.NestedScrollView android:id="@id/scroll_view" android:tag="scrollView" android:persistentDrawingCache="animation" android:layout_width="fill_parent" android:layout_height="0.0dip" android:layout_weight="1.0" app:layout_behavior="@string/appbar_scrolling_view_behavior" app:layout_scrollFlags="scroll">
        <androidx.appcompat.widget.LinearLayoutCompat android:orientation="vertical" android:paddingBottom="0.0dip" android:layout_width="fill_parent" android:layout_height="0.0dip">
            <androidx.constraintlayout.widget.ConstraintLayout android:id="@id/top_layout" android:layout_width="fill_parent" android:layout_height="wrap_content" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent">
                <androidx.constraintlayout.widget.ConstraintLayout android:id="@id/iv_info" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginTop="@dimen/dp_4" android:layout_marginEnd="16.0dip" app:layout_constraintEnd_toStartOf="@id/iv_premium_mask" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent">
                    <View android:background="@drawable/bg_avatar" android:layout_width="26.0dip" android:layout_height="26.0dip" android:layout_marginStart="15.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
                    <com.google.android.material.imageview.ShapeableImageView android:id="@id/iv_avatar_premium" android:layout_width="24.0dip" android:layout_height="24.0dip" android:src="@mipmap/visitor_avatar" android:layout_marginStart="16.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:shapeAppearanceOverlay="@style/ImgRoundedStyle" />
                    <androidx.appcompat.widget.AppCompatTextView android:textSize="@dimen/text_size_14" android:textStyle="bold" android:textColor="@color/white" android:ellipsize="end" android:id="@id/iv_name" android:layout_width="0.0dip" android:layout_height="wrap_content" android:lines="1" android:singleLine="true" android:includeFontPadding="false" android:textAlignment="textStart" android:layout_marginStart="8.0dip" android:layout_marginEnd="16.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/iv_avatar_premium" app:layout_constraintTop_toTopOf="parent" />
                </androidx.constraintlayout.widget.ConstraintLayout>
                <com.transsion.baseui.widget.GradientBorderView android:id="@id/iv_free_container" android:background="@drawable/bg_premium_12_radius" android:visibility="visible" android:layout_width="fill_parent" android:layout_height="55.0dip" android:layout_marginLeft="@dimen/dp_16" android:layout_marginTop="@dimen/dp_10" android:layout_marginRight="@dimen/dp_16" android:layout_marginHorizontal="@dimen/dp_16" app:borderViewEndColor="@color/yellow_light_80" app:borderViewStartColor="@color/yellow_light_80" app:borderWidth="0.0dip" app:bottomLeftCornerRadius="12.0dip" app:bottomRightCornerRadius="12.0dip" app:gradientOrientation="horizontal" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/iv_info" app:topLeftCornerRadius="12.0dip" app:topRightCornerRadius="32.0dip">
                    <TextView android:textSize="@dimen/dimens_20" android:textStyle="bold" android:textColor="@color/white_80" android:gravity="start" android:paddingLeft="@dimen/dp_4" android:paddingTop="2.0dip" android:paddingRight="@dimen/dp_4" android:paddingBottom="2.0dip" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/member_free_plan" android:includeFontPadding="false" android:layout_marginStart="@dimen/dp_16" android:paddingHorizontal="@dimen/dp_4" android:paddingVertical="2.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
                    <com.transsion.member.widget.PointView android:id="@id/iv_free_points" android:layout_width="wrap_content" android:layout_height="28.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" />
                </com.transsion.baseui.widget.GradientBorderView>
                <com.transsion.baseui.widget.GradientBorderView android:id="@id/iv_premium_container" android:background="@drawable/bg_premium_12_radius" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginLeft="@dimen/dp_16" android:layout_marginTop="@dimen/dp_10" android:layout_marginRight="@dimen/dp_16" android:layout_marginHorizontal="@dimen/dp_16" app:borderViewEndColor="@color/yellow_light_60" app:borderViewStartColor="@color/yellow_light_60" app:borderWidth="1.0dip" app:bottomLeftCornerRadius="12.0dip" app:bottomRightCornerRadius="12.0dip" app:cornerRadius="12.0dip" app:gradientOrientation="vertical" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/iv_free_container" app:topLeftCornerRadius="12.0dip" app:topRightCornerRadius="12.0dip">
                    <androidx.constraintlayout.widget.ConstraintLayout android:layout_width="wrap_content" android:layout_height="fill_parent" android:layout_marginBottom="16.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent">
                        <androidx.appcompat.widget.AppCompatTextView android:textSize="@dimen/dimens_20" android:textStyle="bold" android:textColor="@color/yellow_light_50" android:gravity="start" android:id="@id/iv_premium_title" android:paddingBottom="2.0dip" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="15.0dip" android:text="@string/member_premium_title" android:includeFontPadding="false" android:layout_marginStart="16.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" style="@style/style_extra_import_text" />
                        <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textStyle="bold" android:textColor="@color/gray_dark_10" android:id="@id/iv_premium_info" android:background="@drawable/bg_days_left" android:paddingLeft="4.0dip" android:paddingTop="2.0dip" android:paddingRight="4.0dip" android:paddingBottom="2.0dip" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="17.0dip" android:text="@string/member_days_left" android:includeFontPadding="false" android:drawablePadding="2.0dip" android:drawableStart="@mipmap/ic_premium" android:layout_marginStart="2.0dip" android:paddingHorizontal="4.0dip" android:paddingVertical="2.0dip" app:layout_constraintStart_toEndOf="@id/iv_premium_title" app:layout_constraintTop_toTopOf="parent" />
                        <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/white_80" android:id="@id/iv_next_billing_date" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="@dimen/dp_4" android:layout_marginBottom="@dimen/dp_4" android:text="@string/member_next_billing_date" android:includeFontPadding="false" android:layout_marginStart="16.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/iv_premium_info" />
                    </androidx.constraintlayout.widget.ConstraintLayout>
                    <com.transsion.member.widget.PointView android:id="@id/iv_premium_points" android:layout_width="wrap_content" android:layout_height="28.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" />
                    <androidx.appcompat.widget.AppCompatImageView android:layout_width="fill_parent" android:layout_height="fill_parent" android:src="@mipmap/ic_premium_layer" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
                </com.transsion.baseui.widget.GradientBorderView>
                <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_premium_mask" android:visibility="visible" android:layout_width="72.0dip" android:layout_height="72.0dip" android:layout_marginTop="-36.0dip" android:src="@mipmap/ic_premium_mask" android:layout_marginEnd="16.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="@id/iv_premium_container" />
            </androidx.constraintlayout.widget.ConstraintLayout>
            <LinearLayout android:orientation="vertical" android:background="@drawable/bg_container" android:paddingBottom="0.0dip" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="16.0dip">
                <com.transsion.baseui.widget.GradientBorderView android:background="@drawable/bg_premium_title" android:paddingTop="@dimen/dp_12" android:paddingBottom="@dimen/dp_12" android:layout_width="fill_parent" android:layout_height="wrap_content" android:paddingVertical="@dimen/dp_12" app:borderViewEndColor="@color/brand_00" app:borderViewStartColor="@color/yellow_light_80" app:borderWidth="1.0dip" app:bottomLeftCornerRadius="0.0dip" app:bottomRightCornerRadius="0.0dip" app:gradientOrientation="vertical" app:topLeftCornerRadius="12.0dip" app:topRightCornerRadius="12.0dip">
                    <androidx.appcompat.widget.AppCompatTextView android:textSize="@dimen/dimens_sp_18" android:textStyle="bold" android:textColor="@color/yellow_light_50" android:gravity="center" android:id="@id/iv_detail_title" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginLeft="16.0dip" android:layout_marginRight="16.0dip" android:text="@string/member_extend_your_premium_benefits" android:includeFontPadding="false" android:layout_marginHorizontal="16.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" />
                </com.transsion.baseui.widget.GradientBorderView>
                <com.transsion.member.widget.PremiumInfoView android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginLeft="16.0dip" android:layout_marginTop="16.0dip" android:layout_marginRight="16.0dip" android:layout_marginHorizontal="16.0dip" />
                <androidx.appcompat.widget.LinearLayoutCompat android:orientation="vertical" android:background="@drawable/bg_sku_12_radius" android:paddingTop="12.0dip" android:paddingBottom="12.0dip" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginLeft="16.0dip" android:layout_marginTop="12.0dip" android:layout_marginRight="16.0dip" android:layout_marginBottom="16.0dip" android:layout_marginHorizontal="16.0dip" android:paddingVertical="12.0dip">
                    <androidx.recyclerview.widget.RecyclerView android:id="@id/iv_sku_list" android:background="@color/transparent" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginLeft="4.0dip" android:layout_marginRight="4.0dip" android:layout_marginHorizontal="4.0dip" />
                    <androidx.appcompat.widget.LinearLayoutCompat android:orientation="horizontal" android:id="@id/iv_or" android:paddingLeft="12.0dip" android:paddingRight="12.0dip" android:layout_width="fill_parent" android:layout_height="14.0dip" android:layout_marginTop="8.0dip" android:layout_marginBottom="8.0dip" android:layout_marginVertical="8.0dip" android:paddingHorizontal="12.0dip">
                        <View android:layout_gravity="center_vertical" android:background="@color/white_10" android:layout_width="0.0dip" android:layout_height="1.0dip" android:layout_weight="1.0" />
                        <TextView android:textSize="@dimen/sp_12" android:textColor="@color/white_60" android:gravity="center_vertical" android:layout_gravity="center" android:layout_width="wrap_content" android:layout_height="fill_parent" android:layout_marginLeft="8.0dip" android:layout_marginRight="8.0dip" android:text="@string/member_or" android:includeFontPadding="false" android:layout_marginHorizontal="8.0dip" style="@style/style_medium_text" />
                        <View android:layout_gravity="center_vertical" android:background="@color/white_10" android:layout_width="0.0dip" android:layout_height="1.0dip" android:layout_weight="1.0" />
                    </androidx.appcompat.widget.LinearLayoutCompat>
                    <androidx.recyclerview.widget.RecyclerView android:id="@id/iv_redeem_list" android:background="@color/transparent" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginLeft="4.0dip" android:layout_marginRight="4.0dip" android:layout_marginHorizontal="4.0dip" />
                </androidx.appcompat.widget.LinearLayoutCompat>
                <com.transsion.baseui.widget.GradientBorderView android:id="@id/task_container_bar" android:background="@drawable/bg_member_task_container_top" android:layout_width="fill_parent" android:layout_height="wrap_content" app:borderViewEndColor="@color/brand_00" app:borderViewStartColor="@color/yellow_light_80" app:borderWidth="1.0dip" app:gradientOrientation="vertical" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:topLeftCornerRadius="12.0dip" app:topRightCornerRadius="12.0dip">
                    <TextView android:textSize="18.0sp" android:textStyle="bold" android:textColor="@color/warning_50" android:gravity="center" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginLeft="16.0dip" android:layout_marginTop="12.0dip" android:layout_marginRight="16.0dip" android:layout_marginBottom="12.0dip" android:text="@string/member_task_title" android:lines="2" android:layout_marginHorizontal="16.0dip" android:layout_marginVertical="12.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
                </com.transsion.baseui.widget.GradientBorderView>
                <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/gray_dark_40" android:gravity="start" android:id="@id/iv_subscription_desc" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginLeft="30.0dip" android:layout_marginTop="16.0dip" android:layout_marginRight="30.0dip" android:layout_marginBottom="18.0dip" android:text="@string/member_subscription_desc" android:lineSpacingExtra="@dimen/dp_4" android:layout_marginHorizontal="30.0dip" />
                <androidx.recyclerview.widget.RecyclerView android:id="@id/iv_task_list" android:background="@color/transparent" android:paddingBottom="@dimen/tab_bottom_show_height" android:clipToPadding="false" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginLeft="12.0dip" android:layout_marginRight="12.0dip" android:overScrollMode="never" android:layout_marginHorizontal="12.0dip" />
            </LinearLayout>
        </androidx.appcompat.widget.LinearLayoutCompat>
    </androidx.core.widget.NestedScrollView>
</androidx.appcompat.widget.LinearLayoutCompat>
