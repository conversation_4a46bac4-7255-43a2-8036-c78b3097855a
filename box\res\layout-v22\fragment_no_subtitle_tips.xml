<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <TextView android:textSize="18.0sp" android:textColor="@color/white" android:id="@id/tv_title" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="16.0dip" android:layout_marginStart="16.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" style="@style/style_medium_text" />
    <TextView android:textSize="14.0sp" android:textColor="@color/white_60" android:gravity="center" android:id="@id/tv_tips" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginLeft="16.0dip" android:layout_marginTop="89.0dip" android:layout_marginRight="16.0dip" android:text="@string/no_subtitle_tip" android:layout_marginHorizontal="16.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" style="@style/style_regular_text" />
    <com.transsion.baseui.widget.EditTextWithClear android:textSize="14.0sp" android:textColor="@color/white" android:gravity="center_vertical" android:id="@id/et_search_keyword" android:background="@drawable/post_detail_shape_subtitle_search_edit" android:paddingTop="10.0dip" android:paddingBottom="10.0dip" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginLeft="16.0dip" android:layout_marginTop="40.0dip" android:layout_marginRight="16.0dip" android:maxLines="3" android:drawablePadding="8.0dip" android:inputType="text" android:imeOptions="actionSearch" android:drawableStart="@drawable/ic_search" android:paddingStart="36.0dip" android:paddingEnd="12.0dip" android:backgroundTint="@color/white_10" android:layout_marginHorizontal="16.0dip" app:et_clear_ic="@mipmap/ic_input_close_2" app:et_close_withoutfocus="true" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tv_tips" style="@style/style_regula_bigger_text" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_search_keyword" android:layout_width="16.0dip" android:layout_height="16.0dip" android:layout_marginStart="12.0dip" app:layout_constraintBottom_toBottomOf="@id/et_search_keyword" app:layout_constraintStart_toStartOf="@id/et_search_keyword" app:layout_constraintTop_toTopOf="@id/et_search_keyword" app:srcCompat="@drawable/ic_search" />
    <TextView android:textSize="14.0sp" android:textColor="@color/white" android:gravity="center" android:id="@id/tv_download" android:background="@drawable/bg_btn_subtitle_download_08" android:layout_width="0.0dip" android:layout_height="36.0dip" android:layout_marginLeft="16.0dip" android:layout_marginTop="16.0dip" android:layout_marginRight="16.0dip" android:text="@string/subtitle_download_online" android:layout_marginHorizontal="16.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/et_search_keyword" style="@style/style_medium_text" />
    <androidx.appcompat.widget.LinearLayoutCompat android:orientation="horizontal" android:id="@id/ll_select" android:padding="16.0dip" android:layout_width="wrap_content" android:layout_height="wrap_content" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tv_download">
        <ImageView android:layout_gravity="center_vertical" android:id="@id/iv_select" android:layout_width="12.0dip" android:layout_height="12.0dip" android:layout_marginEnd="4.0dip" app:srcCompat="@drawable/selector_download_group_check" />
        <TextView android:textSize="14.0sp" android:textColor="@color/subtitle_text_tips" android:layout_gravity="center_vertical" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginBottom="0.5dip" android:text="@string/no_subtitle_again" style="@style/style_regular_text" />
    </androidx.appcompat.widget.LinearLayoutCompat>
</androidx.constraintlayout.widget.ConstraintLayout>
