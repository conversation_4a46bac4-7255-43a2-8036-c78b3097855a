<?xml version="1.0" encoding="utf-8"?>
<androidx.appcompat.widget.LinearLayoutCompat android:orientation="vertical" android:id="@id/llLayout" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.coordinatorlayout.widget.CoordinatorLayout android:clipChildren="false" android:layout_width="fill_parent" android:layout_height="0.0dip" android:layout_weight="1.0">
        <com.google.android.material.appbar.AppBarLayout android:orientation="vertical" android:id="@id/appbar" android:clipChildren="false" android:layout_width="fill_parent" android:layout_height="wrap_content" android:stateListAnimator="@null" app:layout_behavior="com.transsion.usercenter.profile.FixAppBarLayoutBehavior">
            <com.google.android.material.appbar.CollapsingToolbarLayout android:id="@id/collapsing" android:layout_width="fill_parent" android:layout_height="wrap_content" app:layout_scrollFlags="scroll|exitUntilCollapsed|snap" app:titleEnabled="false">
                <androidx.appcompat.widget.AppCompatImageView android:id="@id/area1_bg" android:tag="largeScale" android:layout_width="fill_parent" android:layout_height="200.0dip" android:src="@mipmap/profile_visitor_bg" android:scaleType="centerCrop" app:layout_collapseMode="parallax" />
                <androidx.constraintlayout.widget.ConstraintLayout android:id="@id/top_layout" android:layout_width="fill_parent" android:layout_height="wrap_content">
                    <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_back" android:visibility="gone" android:layout_width="44.0dip" android:layout_height="44.0dip" android:src="@mipmap/icon_white_back" android:scaleType="center" android:alpha="0.0" android:layout_marginStart="8.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:shapeAppearanceOverlay="@style/ImgRoundedStyle" />
                    <com.google.android.material.imageview.ShapeableImageView android:id="@id/iv_avatar" android:layout_width="56.0dip" android:layout_height="56.0dip" android:layout_marginTop="44.0dip" android:src="@mipmap/profile_visitor_avatar" android:layout_marginStart="16.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:shapeAppearanceOverlay="@style/ImgRoundedStyle" />
                    <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textColor="@color/common_white" android:ellipsize="end" android:gravity="start" android:id="@id/tv_user_name" android:layout_width="0.0dip" android:layout_height="wrap_content" android:maxLines="1" android:singleLine="true" android:layout_marginStart="8.0dip" android:layout_marginEnd="16.0dip" app:layout_constraintBottom_toTopOf="@id/tv_user_id" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/iv_avatar" app:layout_constraintTop_toTopOf="@id/iv_avatar" app:layout_constraintVertical_chainStyle="packed" style="@style/style_import_text" />
                    <com.tn.lib.widget.TnTextView android:textSize="12.0sp" android:textColor="@color/white_60" android:id="@id/tv_user_id" android:layout_width="wrap_content" android:layout_height="wrap_content" android:shadowColor="@color/cl45_30_p" android:shadowDx="0.0" android:shadowDy="0.0" android:shadowRadius="3.0" android:layout_marginStart="8.0dip" app:layout_constraintBottom_toBottomOf="@id/iv_avatar" app:layout_constraintStart_toEndOf="@id/iv_avatar" app:layout_constraintTop_toBottomOf="@id/tv_user_name" style="@style/style_regular_text" />
                    <com.tn.lib.widget.TnTextView android:textSize="12.0sp" android:textColor="@color/cl36" android:id="@id/tv_gender" android:background="@drawable/profile_shape_gender_bg" android:layout_width="wrap_content" android:layout_height="18.0dip" android:layout_marginTop="18.0dip" android:drawablePadding="4.0dip" android:drawableStart="@mipmap/profile_man" android:paddingStart="4.0dip" android:paddingEnd="4.0dip" android:layout_marginStart="16.0dip" android:drawableTint="@color/white" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/iv_avatar" style="@style/style_regular_text" />
                    <com.tn.lib.widget.TnTextView android:textSize="12.0sp" android:textColor="@color/cl36" android:id="@id/tv_edit" android:background="@drawable/profile_shape_gender_bg" android:layout_width="wrap_content" android:layout_height="18.0dip" android:layout_marginTop="18.0dip" android:text="@string/profile_edit_info" android:drawablePadding="4.0dip" android:drawableStart="@mipmap/profile_edit_info" android:paddingStart="4.0dip" android:paddingEnd="4.0dip" android:layout_marginStart="8.0dip" app:layout_constraintStart_toEndOf="@id/tv_gender" app:layout_constraintTop_toBottomOf="@id/iv_avatar" style="@style/style_regular_text" />
                    <com.tn.lib.widget.TnTextView android:textSize="14.0sp" android:textColor="@color/brand" android:gravity="center" android:id="@id/tv_login" android:background="@drawable/profile_shape_login_bg" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="36.0dip" android:layout_margin="16.0dip" android:text="@string/profile_login" app:layout_constraintTop_toBottomOf="@id/iv_avatar" style="@style/robot_medium" />
                    <View android:id="@id/v_area2_bg" android:background="@drawable/profile_area2_bg" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="140.0dip" android:layout_marginTop="16.0dip" app:layout_constraintTop_toBottomOf="@id/tv_login" />
                    <androidx.appcompat.widget.LinearLayoutCompat android:orientation="vertical" android:id="@id/llTopLayout" android:background="@drawable/profile_area2_bg" android:paddingTop="16.0dip" android:paddingBottom="24.0dip" android:layout_width="fill_parent" android:layout_height="0.0dip" android:layout_marginTop="16.0dip" android:paddingStart="16.0dip" android:paddingEnd="16.0dip" app:layout_constraintTop_toBottomOf="@id/tv_login" app:layout_goneMarginTop="50.0dip">
                        <include android:id="@id/entranceLayout" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginBottom="@dimen/dimens_16" layout="@layout/profile_layout_entrance" />
                        <androidx.appcompat.widget.LinearLayoutCompat android:orientation="horizontal" android:id="@id/scroll_area2" android:layout_width="fill_parent" android:layout_height="wrap_content">
                            <com.transsion.usercenter.widget.MyRoomView android:id="@id/myRoomView1" android:layout_width="wrap_content" android:layout_height="wrap_content" />
                            <View android:id="@id/viewPlaceholder1" android:visibility="visible" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_weight="1.0" />
                            <com.transsion.usercenter.widget.MyRoomView android:id="@id/myRoomView2" android:visibility="visible" android:layout_width="wrap_content" android:layout_height="wrap_content" />
                            <View android:id="@id/viewPlaceholder2" android:visibility="visible" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_weight="1.0" />
                            <View android:id="@id/viewPlaceholder3" android:visibility="gone" android:layout_width="8.0dip" android:layout_height="wrap_content" />
                            <com.transsion.usercenter.widget.MyRoomView android:id="@id/myRoomView3" android:layout_width="wrap_content" android:layout_height="wrap_content" />
                        </androidx.appcompat.widget.LinearLayoutCompat>
                    </androidx.appcompat.widget.LinearLayoutCompat>
                    <View android:id="@id/viewLine" android:background="@color/line_02" android:layout_width="fill_parent" android:layout_height="8.0dip" app:layout_constraintBottom_toBottomOf="@id/llTopLayout" />
                    <androidx.appcompat.widget.LinearLayoutCompat android:orientation="vertical" android:id="@id/llBlock" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="400.0dip" android:layout_marginStart="47.0dip" android:layout_marginEnd="47.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="@id/llTopLayout">
                        <com.tn.lib.widget.TnTextView android:textSize="18.0sp" android:textColor="@color/text_02" android:gravity="center" android:id="@id/tvBlocked" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="72.0dip" android:text="@string/str_block_blocked" style="@style/style_regular_text" />
                        <com.tn.lib.widget.TnTextView android:textSize="14.0sp" android:textColor="@color/text_02" android:gravity="center" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="8.0dip" android:text="@string/str_block_desc" style="@style/style_regular_text" />
                    </androidx.appcompat.widget.LinearLayoutCompat>
                </androidx.constraintlayout.widget.ConstraintLayout>
                <com.tn.lib.view.NoTouchToolBar android:id="@id/toolbar" android:tag="toolbar" android:background="@color/bg_01" android:layout_width="fill_parent" android:layout_height="wrap_content" app:layout_collapseMode="pin">
                    <androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="44.0dip">
                        <com.tn.lib.widget.TnTextView android:id="@id/iv_back_black" android:visibility="gone" android:layout_width="30.0dip" android:layout_height="44.0dip" android:scaleType="center" android:alpha="1.0" android:drawableStart="@mipmap/base_back_black" android:layout_marginStart="2.5dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintHorizontal_chainStyle="packed" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:shapeAppearanceOverlay="@style/ImgRoundedStyle" style="@style/style_regular_text" />
                        <com.google.android.material.imageview.ShapeableImageView android:id="@id/iv_title_avatar" android:layout_width="28.0dip" android:layout_height="28.0dip" android:src="@mipmap/profile_visitor_avatar" android:alpha="0.0" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintHorizontal_chainStyle="packed" app:layout_constraintStart_toEndOf="@id/iv_back_black" app:layout_constraintTop_toTopOf="parent" app:shapeAppearanceOverlay="@style/ImgRoundedStyle" />
                        <com.tn.lib.widget.TnTextView android:textSize="14.0sp" android:textColor="@color/text_01" android:ellipsize="end" android:gravity="start" android:id="@id/tv_title_user_name" android:layout_width="0.0dip" android:layout_height="wrap_content" android:maxLines="1" android:singleLine="true" android:alpha="0.0" android:paddingStart="4.0dip" android:paddingEnd="@dimen/dp_12" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toStartOf="@id/iv_qr_code_blank" app:layout_constraintHorizontal_bias="0.0" app:layout_constraintStart_toEndOf="@id/iv_title_avatar" app:layout_constraintTop_toTopOf="parent" app:layout_constraintWidth_default="wrap" style="@style/style_regular_text" />
                        <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_setting_blank" android:layout_width="24.0dip" android:layout_height="24.0dip" android:src="@mipmap/profile_setting_" android:alpha="0.0" android:layout_marginEnd="12.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent" app:tint="@color/text_01" />
                        <View android:id="@id/setting_blank_red_tips" android:background="@drawable/bg_red_notice" android:visibility="gone" android:layout_width="6.0dip" android:layout_height="6.0dip" app:layout_constraintBottom_toTopOf="@id/iv_setting_blank" app:layout_constraintEnd_toEndOf="@id/iv_setting_blank" app:layout_constraintStart_toEndOf="@id/iv_setting_blank" app:layout_constraintTop_toTopOf="@id/iv_setting_blank" />
                        <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_more_blank" android:layout_width="24.0dip" android:layout_height="24.0dip" android:src="@mipmap/ic_more_black" android:alpha="0.0" android:layout_marginEnd="16.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent" app:tint="@color/text_01" />
                        <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_notice_blank" android:layout_width="24.0dip" android:layout_height="24.0dip" android:src="@mipmap/profile_notice_" android:alpha="0.0" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="@id/iv_notice" app:layout_constraintTop_toTopOf="parent" app:tint="@color/text_01" />
                        <View android:id="@id/view_red_blank" android:background="@drawable/bg_red_notice" android:visibility="gone" android:layout_width="6.0dip" android:layout_height="6.0dip" app:layout_constraintBottom_toTopOf="@id/iv_notice_blank" app:layout_constraintEnd_toEndOf="@id/iv_notice_blank" app:layout_constraintStart_toEndOf="@id/iv_notice_blank" app:layout_constraintTop_toTopOf="@id/iv_notice_blank" />
                        <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_update_blank" android:visibility="gone" android:layout_width="24.0dip" android:layout_height="24.0dip" android:src="@mipmap/profile_update_" android:alpha="0.0" android:layout_marginEnd="12.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toStartOf="@id/iv_qr_code_blank" app:layout_constraintTop_toTopOf="parent" app:tint="@color/text_01" />
                        <View android:id="@id/update_blank_red_tips" android:background="@drawable/bg_red_notice" android:layout_width="6.0dip" android:layout_height="6.0dip" android:alpha="0.0" app:layout_constraintBottom_toTopOf="@id/iv_update_blank" app:layout_constraintEnd_toEndOf="@id/iv_update_blank" app:layout_constraintStart_toEndOf="@id/iv_update_blank" app:layout_constraintTop_toTopOf="@id/iv_update_blank" />
                        <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_qr_code_blank" android:layout_width="24.0dip" android:layout_height="24.0dip" android:src="@mipmap/profile_qr_code" android:tint="@color/text_01" android:alpha="0.0" android:layout_marginEnd="12.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toStartOf="@id/iv_notice_blank" app:layout_constraintTop_toTopOf="parent" />
                        <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_setting" android:visibility="gone" android:layout_width="24.0dip" android:layout_height="24.0dip" android:layout_marginTop="10.0dip" android:layout_marginBottom="10.0dip" android:src="@mipmap/profile_setting" android:layout_marginEnd="12.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent" />
                        <View android:id="@id/setting_red_tips" android:background="@drawable/bg_red_notice" android:visibility="gone" android:layout_width="6.0dip" android:layout_height="6.0dip" app:layout_constraintBottom_toTopOf="@id/iv_setting" app:layout_constraintEnd_toEndOf="@id/iv_setting" app:layout_constraintStart_toEndOf="@id/iv_setting" app:layout_constraintTop_toTopOf="@id/iv_setting" />
                        <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_more" android:visibility="gone" android:layout_width="24.0dip" android:layout_height="24.0dip" android:layout_marginTop="10.0dip" android:layout_marginBottom="10.0dip" android:src="@mipmap/ic_more_white" android:layout_marginEnd="16.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent" />
                        <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_notice" android:layout_width="24.0dip" android:layout_height="24.0dip" android:layout_marginTop="10.0dip" android:layout_marginBottom="10.0dip" android:src="@mipmap/profile_notice" android:layout_marginEnd="10.0dip" app:layout_constraintEnd_toStartOf="@id/iv_setting" app:layout_constraintTop_toTopOf="parent" app:layout_goneMarginEnd="@dimen/dimens_48" />
                        <View android:id="@id/view_red" android:background="@drawable/bg_red_notice" android:visibility="gone" android:layout_width="6.0dip" android:layout_height="6.0dip" app:layout_constraintBottom_toTopOf="@id/iv_notice" app:layout_constraintEnd_toEndOf="@id/iv_notice" app:layout_constraintStart_toEndOf="@id/iv_notice" app:layout_constraintTop_toTopOf="@id/iv_notice" />
                        <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_update" android:visibility="gone" android:layout_width="24.0dip" android:layout_height="24.0dip" android:layout_marginTop="10.0dip" android:layout_marginBottom="10.0dip" android:src="@mipmap/profile_update" android:layout_marginEnd="12.0dip" app:layout_constraintEnd_toStartOf="@id/iv_qr_code" app:layout_constraintTop_toTopOf="parent" />
                        <androidx.appcompat.widget.AppCompatImageView android:id="@id/update_red_tips" android:background="@drawable/bg_red_notice" android:visibility="gone" android:layout_width="6.0dip" android:layout_height="6.0dip" app:layout_constraintBottom_toTopOf="@id/iv_update" app:layout_constraintEnd_toEndOf="@id/iv_update" app:layout_constraintStart_toEndOf="@id/iv_update" app:layout_constraintTop_toTopOf="@id/iv_update" />
                        <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_qr_code" android:layout_width="24.0dip" android:layout_height="24.0dip" android:layout_marginTop="10.0dip" android:layout_marginBottom="10.0dip" android:src="@mipmap/profile_qr_code" android:layout_marginEnd="12.0dip" app:layout_constraintEnd_toStartOf="@id/iv_notice" app:layout_constraintTop_toTopOf="parent" />
                    </androidx.constraintlayout.widget.ConstraintLayout>
                </com.tn.lib.view.NoTouchToolBar>
            </com.google.android.material.appbar.CollapsingToolbarLayout>
            <com.google.android.material.tabs.TabLayout android:id="@id/tabs" android:background="@color/bg_01" android:layout_width="fill_parent" android:layout_height="40.0dip" app:tabIndicator="@drawable/indicator_profile_tab" app:tabIndicatorFullWidth="false" app:tabRippleColor="@null" app:tabSelectedTextColor="@color/text_01" app:tabTextAppearance="@style/style_tab_layout" app:tabTextColor="@color/text_02" style="@style/ProfileTabLayout" />
            <View android:id="@id/divider" android:background="@color/line_01" android:layout_width="fill_parent" android:layout_height="1.0dip" app:layout_constraintTop_toBottomOf="@id/tabs" />
        </com.google.android.material.appbar.AppBarLayout>
        <androidx.viewpager2.widget.ViewPager2 android:id="@id/view_pager" android:tag="viewPager" android:background="@color/bg_02" android:persistentDrawingCache="animation" android:layout_width="fill_parent" android:layout_height="fill_parent" android:overScrollMode="never" app:layout_behavior="@string/appbar_scrolling_view_behavior" app:layout_scrollFlags="scroll" />
        <View android:id="@id/viewTheCover" android:tag="viewPager" android:background="@color/bg_01" android:persistentDrawingCache="animation" android:layout_width="fill_parent" android:layout_height="fill_parent" android:overScrollMode="never" app:layout_behavior="@string/appbar_scrolling_view_behavior" app:layout_scrollFlags="scroll" />
    </androidx.coordinatorlayout.widget.CoordinatorLayout>
    <FrameLayout android:id="@id/adContainerP" android:layout_width="fill_parent" android:layout_height="wrap_content">
        <FrameLayout android:id="@id/adContainerTop" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="wrap_content" />
        <FrameLayout android:id="@id/adContainerBottom" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="wrap_content" />
    </FrameLayout>
</androidx.appcompat.widget.LinearLayoutCompat>
