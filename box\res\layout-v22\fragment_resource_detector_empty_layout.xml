<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:paddingLeft="12.0dip" android:paddingRight="12.0dip" android:paddingBottom="16.0dip" android:layout_width="fill_parent" android:layout_height="wrap_content" android:paddingHorizontal="12.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.appcompat.widget.AppCompatTextView android:textSize="18.0sp" android:textStyle="bold" android:textColor="@color/text_01" android:id="@id/innerTvTitle" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="24.0dip" android:text="@string/name_resource" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/text_02" android:id="@id/innerTvInfo" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="2.0dip" android:text="@string/source_info" android:maxLines="1" android:includeFontPadding="false" android:textAlignment="viewStart" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/innerTvTitle" style="@style/style_regular_text" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/innerIcon" android:layout_width="wrap_content" android:layout_height="wrap_content" android:src="@mipmap/movie_source_info" android:layout_marginStart="4.0dip" android:layout_marginEnd="8.0dip" app:layout_constraintBottom_toBottomOf="@id/innerTvInfo" app:layout_constraintStart_toEndOf="@id/innerTvInfo" app:layout_constraintTop_toTopOf="@id/innerTvInfo" app:layout_goneMarginEnd="2.0dip" />
    <androidx.cardview.widget.CardView android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginLeft="1.0dip" android:layout_marginTop="12.0dip" android:layout_marginRight="1.0dip" android:textAlignment="center" android:layout_marginHorizontal="1.0dip" app:cardBackgroundColor="@color/resources_tip_bg_color" app:cardCornerRadius="8.0dip" app:cardElevation="0.0dip" app:cardMaxElevation="0.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintHorizontal_bias="0.0" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/innerTvInfo" app:layout_constraintVertical_bias="1.0">
        <com.transsion.baseui.widget.ResourcesRequestView android:id="@id/iv_resources" android:layout_width="fill_parent" android:layout_height="fill_parent" />
    </androidx.cardview.widget.CardView>
</androidx.constraintlayout.widget.ConstraintLayout>
