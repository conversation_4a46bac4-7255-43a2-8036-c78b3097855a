<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:paddingBottom="16.0dip" android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.appcompat.widget.AppCompatTextView android:textSize="14.0sp" android:textColor="@color/text_02" android:id="@id/iv_seasons" android:background="@drawable/selector_video_detail_seasons_bg" android:paddingLeft="8.0dip" android:paddingTop="6.0dip" android:paddingRight="8.0dip" android:paddingBottom="6.0dip" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:drawablePadding="2.0dip" android:drawableEnd="@mipmap/ic_arrow_down" android:layout_marginEnd="12.0dip" android:paddingHorizontal="8.0dip" android:paddingVertical="6.0dip" app:layout_constraintBottom_toBottomOf="@id/innerTvInfo" app:layout_constraintEnd_toEndOf="parent" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="18.0sp" android:textStyle="bold" android:textColor="@color/text_01" android:id="@id/innerTvTitle" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="24.0dip" android:text="@string/name_resource" android:layout_marginStart="12.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/text_02" android:id="@id/innerTvInfo" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="2.0dip" android:text="@string/movie_detail_upload_by" android:maxLines="1" android:includeFontPadding="false" android:textAlignment="viewStart" android:layout_marginStart="12.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/innerTvTitle" style="@style/style_regular_text" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/text_02" android:ellipsize="end" android:id="@id/innerTvName" android:layout_width="wrap_content" android:layout_height="wrap_content" android:maxLines="1" android:includeFontPadding="false" android:textAlignment="viewStart" android:layout_marginStart="4.0dip" app:layout_constrainedWidth="true" app:layout_constraintBottom_toBottomOf="@id/innerTvInfo" app:layout_constraintEnd_toStartOf="@id/innerIcon" app:layout_constraintStart_toEndOf="@id/innerTvInfo" app:layout_constraintTop_toTopOf="@id/innerTvInfo" app:layout_constraintWidth_max="88.0dip" style="@style/style_medium_text" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/innerIcon" android:layout_width="wrap_content" android:layout_height="wrap_content" android:src="@mipmap/movie_source_info" android:layout_marginStart="4.0dip" android:layout_marginEnd="8.0dip" app:layout_constraintBottom_toBottomOf="@id/innerTvInfo" app:layout_constraintStart_toEndOf="@id/innerTvName" app:layout_constraintTop_toTopOf="@id/innerTvInfo" app:layout_goneMarginEnd="2.0dip" />
    <androidx.recyclerview.widget.RecyclerView android:id="@id/resourceRv" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginLeft="4.0dip" android:layout_marginTop="12.0dip" android:layout_marginRight="4.0dip" android:layout_marginHorizontal="4.0dip" app:layout_constraintEnd_toStartOf="@id/tv_more" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/innerTvInfo" app:layout_goneMarginTop="12.0dip" />
    <com.noober.background.view.BLTextView android:textColor="@color/text_01" android:gravity="center_vertical" android:id="@id/tv_more" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="0.0dip" android:text="@string/all" android:paddingStart="8.0dip" android:paddingEnd="12.0dip" app:bl_corners_leftRadius="4.0dip" app:bl_gradient_angle="0" app:bl_gradient_endColor="@color/transparent" app:bl_gradient_startColor="#05ffffff" app:layout_constraintBottom_toBottomOf="@id/resourceRv" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="@id/resourceRv" style="@style/style_medium_text" />
    <com.noober.background.view.BLView android:id="@id/trans_v_bg" android:visibility="gone" android:layout_width="20.0dip" android:layout_height="0.0dip" app:bl_gradient_angle="0" app:bl_gradient_endColor="@color/bg_01" app:bl_gradient_startColor="@color/transparent" app:layout_constraintBottom_toBottomOf="@id/tv_more" app:layout_constraintEnd_toStartOf="@id/tv_more" app:layout_constraintTop_toTopOf="@id/tv_more" />
    <FrameLayout android:id="@id/lf_loading" android:visibility="gone" android:layout_width="0.0dip" android:layout_height="0.0dip" app:layout_constraintBottom_toBottomOf="@id/resourceRv" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="@id/resourceRv">
        <ProgressBar android:layout_gravity="center" android:id="@id/load_view" android:layout_width="28.0dip" android:layout_height="28.0dip" android:layout_centerInParent="true" android:indeterminateTint="@color/brand" />
    </FrameLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
