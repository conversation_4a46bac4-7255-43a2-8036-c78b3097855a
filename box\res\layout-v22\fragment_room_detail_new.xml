<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <ImageView android:id="@id/iv_top_bg" android:background="@color/post_detail_top_bg" android:layout_width="fill_parent" android:layout_height="0.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <androidx.coordinatorlayout.widget.CoordinatorLayout android:layout_width="fill_parent" android:layout_height="0.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/iv_back">
        <com.google.android.material.appbar.AppBarLayout android:id="@id/app_bar" android:layout_width="fill_parent" android:layout_height="wrap_content">
            <androidx.constraintlayout.widget.ConstraintLayout android:orientation="vertical" android:id="@id/cl_top_content" android:background="@color/post_detail_top_bg" android:paddingLeft="12.0dip" android:paddingRight="12.0dip" android:paddingBottom="12.0dip" android:layout_width="fill_parent" android:layout_height="wrap_content" android:paddingHorizontal="12.0dip" app:layout_scrollFlags="scroll|exitUntilCollapsed">
                <com.tn.lib.view.expand.ExpandView android:textSize="12.0sp" android:textColor="@color/white_60" android:id="@id/ev_room_des" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="72.0dip" app:layout_constraintTop_toTopOf="parent" app:layout_goneMarginBottom="0.0dip" app:pop_expand_bg_Color="@color/transparent" app:pop_expand_hint_color="@color/text_02" app:pop_hint_text_size="12.0sp" app:pop_reverse_Lines="2" app:pop_shrink_bg_color="@color/transparent" app:pop_shrink_hint_color="@color/text_02" style="@style/style_regular_text" />
                <com.transsion.room.view.roundimage.PileLayout android:id="@id/pl_member_ic" android:layout_width="wrap_content" android:layout_height="24.0dip" android:layout_marginTop="8.0dip" app:PileLayout_pileWidth="8.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/ev_room_des" />
                <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/white_60" android:gravity="center" android:id="@id/tv_member" android:layout_width="wrap_content" android:layout_height="30.0dip" android:text="@string/x_members" android:includeFontPadding="false" android:layout_marginStart="12.0dip" app:drawableStartCompat="@drawable/ic_room_member" app:layout_constraintBottom_toBottomOf="@id/pl_member_ic" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="@id/pl_member_ic" style="@style/style_regular_text" />
                <androidx.constraintlayout.widget.Group android:id="@id/group_member" android:layout_width="wrap_content" android:layout_height="wrap_content" app:constraint_referenced_ids="pl_member_ic,tv_member" />
            </androidx.constraintlayout.widget.ConstraintLayout>
            <FrameLayout android:id="@id/fl_tab" android:background="@color/post_detail_top_bg" android:layout_width="fill_parent" android:layout_height="44.0dip">
                <net.lucode.hackware.magicindicator.MagicIndicator android:layout_gravity="bottom" android:id="@id/magic_indicator" android:background="@drawable/bg_post_detail_tab" android:layout_width="fill_parent" android:layout_height="fill_parent" />
            </FrameLayout>
        </com.google.android.material.appbar.AppBarLayout>
        <com.transsion.baseui.widget.NestedScrollableHost android:layout_width="fill_parent" android:layout_height="fill_parent" app:layout_behavior="@string/appbar_scrolling_view_behavior">
            <androidx.viewpager2.widget.ViewPager2 android:id="@id/view_pager" android:layout_width="fill_parent" android:layout_height="fill_parent" />
        </com.transsion.baseui.widget.NestedScrollableHost>
    </androidx.coordinatorlayout.widget.CoordinatorLayout>
    <View android:id="@id/v_title_bar_bg" android:background="@color/post_detail_top_bg" android:layout_width="fill_parent" android:layout_height="48.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/iv_top_bg" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_back" android:layout_width="48.0dip" android:layout_height="48.0dip" android:src="@mipmap/icon_white_back" android:scaleType="center" app:layout_collapseMode="pin" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/iv_top_bg" />
    <androidx.appcompat.widget.AppCompatImageView android:layout_gravity="end" android:id="@id/iv_share" android:visibility="gone" android:layout_width="48.0dip" android:layout_height="48.0dip" android:src="@drawable/ic_movie_share" android:scaleType="center" app:layout_collapseMode="pin" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toBottomOf="@id/iv_top_bg" />
    <LinearLayout android:id="@id/ll_header_root" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginTop="54.0dip" android:layout_marginStart="12.0dip" android:layout_marginEnd="12.0dip" app:layout_collapseMode="parallax" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/iv_top_bg">
        <com.google.android.material.imageview.ShapeableImageView android:id="@id/iv_room_cover" android:background="@color/gray_dark_20" android:layout_width="52.0dip" android:layout_height="52.0dip" android:scaleType="centerCrop" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:shapeAppearanceOverlay="@style/roundStyle_4" />
        <androidx.appcompat.widget.AppCompatTextView android:textSize="18.0sp" android:textColor="@color/text_01" android:ellipsize="end" android:layout_gravity="center_vertical" android:id="@id/tv_title" android:layout_width="0.0dip" android:layout_height="wrap_content" android:text="fanclubfanclubfanclubfanclubfanclubfanclubfanclubfanclubfanclubfanclubfanclubfanclubfanclubfanclubfanclub" android:maxLines="2" android:includeFontPadding="false" android:layout_weight="1.0" android:layout_marginStart="8.0dip" style="@style/style_extra_import_text" />
        <com.tn.lib.view.RoomJoinAnimationView android:layout_gravity="center_vertical" android:id="@id/v_join" android:layout_width="wrap_content" android:layout_height="28.0dip" android:layout_marginStart="8.0dip" app:jv_style="text" app:jv_text_join_bg="@color/white" app:jv_text_join_text_color="@color/resources_button_color" app:jv_text_joined_bg="@color/white_20" app:jv_text_joined_text_color="@color/white_80" app:jv_text_loading_bg="@color/white_20" app:jv_text_loading_color="@color/white_80" app:jv_text_loading_size="20.0dip" app:jv_text_text_size="14.0sp" />
    </LinearLayout>
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_publish" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginBottom="48.0dip" android:layout_marginEnd="12.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:srcCompat="@drawable/ic_room_publish" />
</androidx.constraintlayout.widget.ConstraintLayout>
