<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <net.lucode.hackware.magicindicator.MagicIndicator android:id="@id/search_result_magic_indicator" android:layout_width="fill_parent" android:layout_height="36.0dip" app:layout_constraintTop_toTopOf="parent" />
    <View android:id="@id/search_result_magic_indicator_divider" android:background="@color/border" android:layout_width="fill_parent" android:layout_height="0.5dip" android:layout_marginLeft="12.0dip" android:layout_marginRight="12.0dip" android:layout_marginHorizontal="12.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/search_result_magic_indicator" />
    <androidx.viewpager2.widget.ViewPager2 android:id="@id/search_result_view_pager" android:layout_width="fill_parent" android:layout_height="0.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintTop_toBottomOf="@id/search_result_magic_indicator_divider" />
    <ProgressBar android:id="@id/search_result_progress_bar" android:layout_width="23.0dip" android:layout_height="23.0dip" android:indeterminateTint="@color/brand_new_50" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <FrameLayout android:id="@id/search_result_empty_view" android:paddingBottom="156.0dip" android:layout_width="wrap_content" android:layout_height="wrap_content" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>
