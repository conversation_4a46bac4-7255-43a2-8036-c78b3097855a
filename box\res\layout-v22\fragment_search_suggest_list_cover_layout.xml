<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:paddingTop="8.0dip" android:paddingBottom="8.0dip" android:layout_width="fill_parent" android:layout_height="wrap_content" android:paddingVertical="8.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <View android:id="@id/background" android:background="@drawable/bg_association" android:layout_width="fill_parent" android:layout_height="128.0dip" android:layout_marginLeft="16.0dip" android:layout_marginRight="16.0dip" android:layout_marginHorizontal="16.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <androidx.appcompat.widget.AppCompatImageView android:layout_width="120.0dip" android:layout_height="120.0dip" android:layout_marginTop="8.0dip" android:src="@mipmap/ic_search_cup" app:layout_constraintEnd_toEndOf="@id/background" app:layout_constraintTop_toTopOf="parent" />
    <include layout="@layout/item_associate_subject_common" />
</androidx.constraintlayout.widget.ConstraintLayout>
