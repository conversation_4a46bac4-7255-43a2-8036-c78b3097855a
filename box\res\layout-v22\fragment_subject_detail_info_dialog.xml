<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:background="@color/module_01" android:focusable="true" android:clickable="true" android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.appcompat.widget.LinearLayoutCompat android:gravity="center_vertical" android:orientation="horizontal" android:id="@id/tv_header_toolbar" android:layout_width="fill_parent" android:layout_height="48.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent">
        <androidx.appcompat.widget.AppCompatTextView android:textSize="@dimen/dimens_sp_18" android:textStyle="bold" android:textColor="@color/white" android:gravity="center_vertical" android:layout_width="0.0dip" android:layout_height="fill_parent" android:text="@string/movie_detail_more_details" android:layout_weight="1.0" android:layout_marginStart="12.0dip" />
        <androidx.appcompat.widget.AppCompatImageView android:id="@id/tv_close" android:layout_width="44.0dip" android:layout_height="44.0dip" android:src="@mipmap/ic_close" android:scaleType="center" />
    </androidx.appcompat.widget.LinearLayoutCompat>
    <View android:background="@color/border" android:layout_width="fill_parent" android:layout_height="1.0dip" app:layout_constraintTop_toBottomOf="@id/tv_header_toolbar" />
    <androidx.core.widget.NestedScrollView android:tag="scrollView" android:persistentDrawingCache="animation" android:layout_width="fill_parent" android:layout_height="0.0dip" app:layout_behavior="@string/appbar_scrolling_view_behavior" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tv_header_toolbar" app:layout_scrollFlags="scroll">
        <androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="wrap_content">
            <com.google.android.material.imageview.ShapeableImageView android:id="@id/ivMovieCover" android:layout_width="60.0dip" android:layout_height="84.0dip" android:layout_marginTop="16.0dip" android:scaleType="centerCrop" android:layout_marginStart="12.0dip" app:layout_constraintEnd_toStartOf="@id/tvMovieTitle" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:shapeAppearanceOverlay="@style/roundStyle_4" />
            <View android:id="@id/ivMovieCoverMask" android:background="@drawable/bg_video_cover_mask" android:layout_width="50.0dip" android:layout_height="20.0dip" app:layout_constraintBottom_toBottomOf="@id/ivMovieCover" app:layout_constraintEnd_toEndOf="@id/ivMovieCover" app:layout_constraintStart_toStartOf="@id/ivMovieCover" />
            <View android:background="@drawable/ic_preview" android:layout_width="@dimen/dimens_12" android:layout_height="@dimen/dimens_12" android:layout_marginBottom="@dimen/dp_4" android:layout_marginEnd="@dimen/dp_4" app:layout_constraintBottom_toBottomOf="@id/ivMovieCover" app:layout_constraintEnd_toEndOf="@id/ivMovieCover" />
            <androidx.appcompat.widget.AppCompatTextView android:textSize="18.0sp" android:textColor="@color/white" android:ellipsize="end" android:gravity="start|bottom" android:id="@id/tvMovieTitle" android:layout_width="0.0dip" android:layout_height="wrap_content" android:textAlignment="viewStart" android:layout_marginStart="12.0dip" android:layout_marginEnd="12.0dip" app:layout_constraintBottom_toTopOf="@id/ll_score" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/ivMovieCover" app:layout_constraintTop_toTopOf="@id/ivMovieCover" app:layout_constraintVertical_chainStyle="packed" style="@style/style_import_text" />
            <LinearLayout android:gravity="start" android:orientation="horizontal" android:id="@id/ll_score" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginTop="4.0dip" android:layout_marginBottom="3.0dip" app:layout_constraintEnd_toEndOf="@id/tvMovieTitle" app:layout_constraintStart_toStartOf="@id/tvMovieTitle" app:layout_constraintTop_toBottomOf="@id/tvMovieTitle">
                <androidx.appcompat.widget.AppCompatImageView android:id="@id/ivMovieContent" android:layout_width="wrap_content" android:layout_height="wrap_content" android:src="@drawable/ic_tag_movie" android:tint="@color/gray_40" app:layout_constraintEnd_toStartOf="@id/tvMovieContent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="@id/tvMovieContent" style="@style/style_regular_text" />
                <View android:id="@id/view_separator" android:background="@color/gray_dark_40" android:layout_width="1.0dip" android:layout_height="fill_parent" android:layout_marginLeft="4.0dip" android:layout_marginTop="3.0dip" android:layout_marginRight="4.0dip" android:layout_marginBottom="3.0dip" android:layout_marginHorizontal="4.0dip" android:layout_marginVertical="3.0dip" />
                <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_score" android:layout_width="12.0dip" android:layout_height="12.0dip" android:src="@mipmap/home_ic_score" android:scaleType="centerInside" android:layout_marginEnd="2.0dip" />
                <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/yellow_dark_70" android:ellipsize="end" android:id="@id/tv_score" android:layout_width="wrap_content" android:layout_height="wrap_content" android:maxLines="1" android:includeFontPadding="false" android:layout_marginStart="2.0dip" />
                <com.transsion.moviedetail.view.CustomTextViewGroup android:id="@id/tv_restrict" android:layout_width="wrap_content" android:layout_height="fill_parent" app:text="18+" />
                <com.transsion.moviedetail.view.CustomTextViewGroup android:id="@id/tv_time" android:layout_width="wrap_content" android:layout_height="fill_parent" app:text="2024" />
                <com.transsion.moviedetail.view.CustomTextViewGroup android:id="@id/tv_country" android:layout_width="wrap_content" android:layout_height="fill_parent" app:text="United States" />
                <com.transsion.moviedetail.view.CustomTextViewGroup android:id="@id/tv_type" android:layout_width="wrap_content" android:layout_height="fill_parent" app:text="Actions" />
                <View android:gravity="center" android:id="@id/v_seasons_line" android:background="@color/gray_dark_40" android:layout_width="1.0dip" android:layout_height="fill_parent" android:layout_marginLeft="4.0dip" android:layout_marginTop="3.0dip" android:layout_marginRight="4.0dip" android:layout_marginBottom="3.0dip" android:layout_marginHorizontal="4.0dip" android:layout_marginVertical="3.0dip" />
            </LinearLayout>
            <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/text_02" android:id="@id/tv_seasons" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="4.0dip" app:layout_constraintStart_toStartOf="@id/ll_score" app:layout_constraintTop_toBottomOf="@id/ll_score" />
            <FrameLayout android:id="@id/game_container" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="64.0dip" android:layout_marginLeft="16.0dip" android:layout_marginTop="24.0dip" android:layout_marginRight="16.0dip" android:layout_marginHorizontal="16.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/ivMovieCover" />
            <com.transsion.moviedetail.view.InfoExtendView android:id="@id/infoExtendView" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="16.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/game_container" app:layout_goneMarginTop="24.0dip" />
            <FrameLayout android:id="@id/fl_starring" android:layout_width="fill_parent" android:layout_height="wrap_content" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/infoExtendView" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.core.widget.NestedScrollView>
</androidx.constraintlayout.widget.ConstraintLayout>
