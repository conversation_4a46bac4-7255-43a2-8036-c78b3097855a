<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.appcompat.widget.LinearLayoutCompat android:gravity="center_vertical" android:orientation="horizontal" android:id="@id/tv_movie_title_container" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginLeft="12.0dip" android:layout_marginTop="8.0dip" android:layout_marginRight="12.0dip" android:layout_marginHorizontal="12.0dip" app:layout_constraintBottom_toTopOf="@id/ll_score" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent">
        <androidx.appcompat.widget.AppCompatTextView android:textSize="18.0sp" android:textColor="@color/text_01" android:ellipsize="end" android:gravity="start|bottom" android:id="@id/tvMovieTitle" android:layout_width="wrap_content" android:layout_height="wrap_content" android:maxLines="1" android:textAlignment="viewStart" android:drawableTint="@color/text_01" app:drawableEndCompat="@mipmap/movie_detail_ic_arrow_right_white" style="@style/style_import_text" />
    </androidx.appcompat.widget.LinearLayoutCompat>
    <LinearLayout android:gravity="center" android:orientation="horizontal" android:id="@id/ll_score" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="4.0dip" android:layout_marginBottom="3.0dip" app:layout_constraintBottom_toBottomOf="@id/tv_movie_title_container" app:layout_constraintStart_toStartOf="@id/tv_movie_title_container" app:layout_constraintTop_toBottomOf="@id/tv_movie_title_container">
        <androidx.appcompat.widget.AppCompatImageView android:id="@id/ivMovieContent" android:layout_width="wrap_content" android:layout_height="wrap_content" android:src="@drawable/ic_tag_edu" android:tint="@color/gray_40" app:layout_constraintEnd_toStartOf="@id/tvMovieContent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="@id/tvMovieContent" style="@style/style_regular_text" />
        <com.transsion.moviedetail.view.CustomTextViewGroup android:id="@id/tv_category" android:layout_width="wrap_content" android:layout_height="wrap_content" app:text="School" />
        <com.transsion.moviedetail.view.CustomTextViewGroup android:id="@id/tv_genre" android:layout_width="wrap_content" android:layout_height="wrap_content" app:text="Science" />
        <com.transsion.moviedetail.view.CustomTextViewGroup android:id="@id/tv_duration" android:layout_width="wrap_content" android:layout_height="wrap_content" app:text="2h27m" />
        <com.transsion.moviedetail.view.CustomTextViewGroup android:id="@id/tv_students" android:layout_width="wrap_content" android:layout_height="wrap_content" app:text="228 students" />
    </LinearLayout>
    <View android:id="@id/v_detail_hot_zone" android:layout_width="0.0dip" android:layout_height="0.0dip" app:layout_constraintBottom_toBottomOf="@id/ll_score" app:layout_constraintEnd_toEndOf="@id/tv_movie_title_container" app:layout_constraintStart_toStartOf="@id/tv_movie_title_container" app:layout_constraintTop_toTopOf="@id/tv_movie_title_container" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/text_02" android:ellipsize="end" android:id="@id/tvDes" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginLeft="12.0dip" android:layout_marginTop="8.0dip" android:layout_marginRight="12.0dip" android:maxLines="2" android:layout_marginHorizontal="12.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/ll_score" />
    <FrameLayout android:id="@id/extension_container" android:layout_width="fill_parent" android:layout_height="32.0dip" android:layout_marginTop="8.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tvDes" />
</androidx.constraintlayout.widget.ConstraintLayout>
