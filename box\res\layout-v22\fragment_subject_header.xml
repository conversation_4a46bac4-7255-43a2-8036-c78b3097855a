<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.google.android.material.imageview.ShapeableImageView android:id="@id/ivMovieCover" android:visibility="gone" android:layout_width="60.0dip" android:layout_height="84.0dip" android:layout_marginTop="16.0dip" android:scaleType="centerCrop" android:layout_marginStart="12.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:shapeAppearanceOverlay="@style/roundStyle_4" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_preview" android:visibility="gone" android:layout_width="@dimen/dimens_12" android:layout_height="@dimen/dimens_12" android:layout_marginBottom="@dimen/dp_4" android:layout_marginEnd="@dimen/dp_4" app:layout_constraintBottom_toBottomOf="@id/ivMovieCover" app:layout_constraintEnd_toEndOf="@id/ivMovieCover" app:srcCompat="@drawable/ic_preview" />
    <androidx.appcompat.widget.LinearLayoutCompat android:gravity="center_vertical" android:orientation="horizontal" android:id="@id/tv_movie_title_container" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginLeft="12.0dip" android:layout_marginTop="8.0dip" android:layout_marginRight="12.0dip" android:layout_marginHorizontal="12.0dip" app:layout_constraintBottom_toTopOf="@id/ll_score" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/ivMovieCover" app:layout_constraintTop_toTopOf="parent">
        <androidx.appcompat.widget.AppCompatTextView android:textSize="18.0sp" android:textColor="@color/text_01" android:ellipsize="end" android:gravity="start|bottom" android:id="@id/tvMovieTitle" android:layout_width="wrap_content" android:layout_height="wrap_content" android:maxLines="1" android:textAlignment="viewStart" android:drawableTint="@color/text_01" app:drawableEndCompat="@mipmap/movie_detail_ic_arrow_right_white" style="@style/style_import_text" />
    </androidx.appcompat.widget.LinearLayoutCompat>
    <LinearLayout android:gravity="center" android:orientation="horizontal" android:id="@id/ll_score" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="4.0dip" android:layout_marginBottom="3.0dip" app:layout_constraintBottom_toBottomOf="@id/tv_movie_title_container" app:layout_constraintStart_toStartOf="@id/tv_movie_title_container" app:layout_constraintTop_toBottomOf="@id/tv_movie_title_container">
        <androidx.appcompat.widget.AppCompatImageView android:id="@id/ivMovieContent" android:layout_width="wrap_content" android:layout_height="wrap_content" android:src="@drawable/ic_tag_movie" android:tint="@color/gray_40" app:layout_constraintEnd_toStartOf="@id/tvMovieContent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="@id/tvMovieContent" style="@style/style_regular_text" />
        <View android:background="@color/gray_dark_40" android:layout_width="1.0dip" android:layout_height="fill_parent" android:layout_marginLeft="4.0dip" android:layout_marginTop="3.0dip" android:layout_marginRight="4.0dip" android:layout_marginBottom="3.0dip" android:layout_marginHorizontal="4.0dip" android:layout_marginVertical="3.0dip" />
        <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_score" android:layout_width="12.0dip" android:layout_height="12.0dip" android:src="@mipmap/home_ic_score" android:scaleType="centerInside" android:layout_marginEnd="2.0dip" />
        <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/yellow_dark_70" android:ellipsize="end" android:id="@id/tv_score" android:layout_width="wrap_content" android:layout_height="wrap_content" android:maxLines="1" android:includeFontPadding="false" android:layout_marginStart="2.0dip" />
        <com.transsion.moviedetail.view.CustomTextViewGroup android:id="@id/tv_restrict" android:layout_width="wrap_content" android:layout_height="fill_parent" app:text="18+" />
        <com.transsion.moviedetail.view.CustomTextViewGroup android:id="@id/tv_time" android:layout_width="wrap_content" android:layout_height="fill_parent" app:text="2024" />
        <com.transsion.moviedetail.view.CustomTextViewGroup android:id="@id/tv_country" android:layout_width="wrap_content" android:layout_height="fill_parent" app:text="China" />
        <com.transsion.moviedetail.view.CustomTextViewGroup android:id="@id/tv_type" android:layout_width="wrap_content" android:layout_height="fill_parent" app:text="Actions" />
        <com.transsion.moviedetail.view.CustomTextViewGroup android:id="@id/tv_seasons" android:layout_width="wrap_content" android:layout_height="fill_parent" app:text="10 seasons" />
        <View android:gravity="center" android:id="@id/v_seasons_line_2" android:background="@color/gray_dark_40" android:visibility="gone" android:layout_width="1.0dip" android:layout_height="fill_parent" android:layout_marginLeft="4.0dip" android:layout_marginTop="3.0dip" android:layout_marginRight="4.0dip" android:layout_marginBottom="3.0dip" android:layout_marginHorizontal="4.0dip" android:layout_marginVertical="3.0dip" />
    </LinearLayout>
    <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/text_02" android:id="@id/tv_seasons_2" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="4.0dip" app:layout_constraintStart_toStartOf="@id/ll_score" app:layout_constraintTop_toBottomOf="@id/ll_score" />
    <View android:id="@id/v_detail_hot_zone" android:layout_width="0.0dip" android:layout_height="0.0dip" app:layout_constraintBottom_toBottomOf="@id/tv_seasons_2" app:layout_constraintEnd_toEndOf="@id/tv_movie_title_container" app:layout_constraintStart_toStartOf="@id/tv_movie_title_container" app:layout_constraintTop_toTopOf="@id/tv_movie_title_container" />
    <FrameLayout android:id="@id/extension_container" android:layout_width="fill_parent" android:layout_height="32.0dip" android:layout_marginTop="8.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/ll_score" />
</androidx.constraintlayout.widget.ConstraintLayout>
