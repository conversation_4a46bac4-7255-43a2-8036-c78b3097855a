<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/ivBack" android:paddingTop="16.0dip" android:paddingBottom="16.0dip" android:layout_width="wrap_content" android:layout_height="48.0dip" android:src="@drawable/subtitle_left" android:layout_marginStart="12.0dip" android:paddingVertical="16.0dip" app:layout_constraintEnd_toStartOf="@id/tvStyle" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textColor="@color/white" android:ellipsize="end" android:gravity="center_vertical" android:id="@id/tvStyle" android:layout_width="0.0dip" android:layout_height="wrap_content" android:text="@string/subtitle_download_subtitle" android:layout_marginStart="2.0dip" app:layout_constraintBottom_toBottomOf="@id/ivBack" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/ivBack" app:layout_constraintTop_toTopOf="@id/ivBack" style="@style/style_import_text" />
    <com.transsion.baseui.widget.EditTextWithClear android:textSize="14.0sp" android:textColor="@color/white" android:gravity="center_vertical" android:id="@id/et_search_keyword" android:background="@drawable/post_detail_shape_subtitle_search_edit" android:paddingTop="10.0dip" android:paddingBottom="10.0dip" android:layout_width="0.0dip" android:layout_height="36.0dip" android:layout_marginTop="51.0dip" android:maxLines="1" android:drawablePadding="8.0dip" android:inputType="text" android:imeOptions="actionSearch" android:drawableStart="@drawable/ic_search" android:paddingStart="36.0dip" android:paddingEnd="12.0dip" android:layout_marginStart="16.0dip" android:backgroundTint="@color/white_10" app:et_clear_ic="@mipmap/ic_input_close_2" app:et_close_withoutfocus="true" app:layout_constraintEnd_toStartOf="@id/tv_search" app:layout_constraintHorizontal_weight="1.0" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" style="@style/style_regula_bigger_text" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_search_keyword" android:layout_width="16.0dip" android:layout_height="16.0dip" android:layout_marginStart="12.0dip" app:layout_constraintBottom_toBottomOf="@id/et_search_keyword" app:layout_constraintStart_toStartOf="@id/et_search_keyword" app:layout_constraintTop_toTopOf="@id/et_search_keyword" app:srcCompat="@drawable/ic_search" />
    <TextView android:textSize="14.0sp" android:textColor="@color/gray_dark_00" android:gravity="center" android:id="@id/tv_search" android:background="@drawable/bg_btn_subtitle_download_08" android:layout_width="wrap_content" android:layout_height="0.0dip" android:text="@string/subtitle_search" android:paddingStart="12.0dip" android:paddingEnd="12.0dip" android:layout_marginStart="16.0dip" android:layout_marginEnd="16.0dip" app:layout_constraintBottom_toBottomOf="@id/et_search_keyword" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/et_search_keyword" app:layout_constraintTop_toTopOf="@id/et_search_keyword" style="@style/style_import_text" />
    <net.lucode.hackware.magicindicator.MagicIndicator android:id="@id/magic_indicator" android:layout_width="0.0dip" android:layout_height="30.0dip" android:layout_marginTop="12.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/et_search_keyword" />
    <com.transsion.baseui.widget.NestedScrollableHost android:id="@id/nsh_content" android:layout_width="0.0dip" android:layout_height="0.0dip" android:layout_marginTop="12.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/magic_indicator">
        <androidx.viewpager2.widget.ViewPager2 android:id="@id/view_pager" android:layout_width="fill_parent" android:layout_height="fill_parent" />
    </com.transsion.baseui.widget.NestedScrollableHost>
</androidx.constraintlayout.widget.ConstraintLayout>
