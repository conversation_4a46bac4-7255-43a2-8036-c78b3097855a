<?xml version="1.0" encoding="utf-8"?>
<androidx.appcompat.widget.LinearLayoutCompat android:orientation="vertical" android:id="@id/root" android:fitsSystemWindows="true" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.constraintlayout.widget.ConstraintLayout android:id="@id/tool_bar" android:layout_width="fill_parent" android:layout_height="44.0dip">
        <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_back" android:layout_width="wrap_content" android:layout_height="44.0dip" android:src="@mipmap/libui_ic_back_black" android:tint="@color/web_title_btn" android:contentDescription="@null" android:paddingStart="16.0dip" android:paddingEnd="8.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
        <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_close" android:layout_width="wrap_content" android:layout_height="44.0dip" android:src="@mipmap/web_close" android:tint="@color/web_title_btn" android:contentDescription="@null" android:paddingStart="8.0dip" android:paddingEnd="8.0dip" app:layout_constraintStart_toEndOf="@id/iv_back" app:layout_constraintTop_toTopOf="parent" />
        <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_right" android:layout_width="wrap_content" android:layout_height="44.0dip" android:src="@mipmap/ic_refresh_black" android:tint="@color/web_title_btn" android:contentDescription="@null" android:paddingStart="8.0dip" android:paddingEnd="16.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent" />
        <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/text_01" android:ellipsize="end" android:gravity="center" android:id="@id/tv_titleText" android:layout_width="0.0dip" android:layout_marginLeft="90.0dip" android:layout_marginRight="90.0dip" android:maxLines="1" android:layout_marginHorizontal="90.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" style="@style/style_title_text" />
    </androidx.constraintlayout.widget.ConstraintLayout>
    <FrameLayout android:id="@id/fl_web" android:layout_width="fill_parent" android:layout_height="fill_parent">
        <include android:id="@id/web_pay_include_loading" layout="@layout/web_loading_default_layout" />
        <ProgressBar android:layout_gravity="center" android:id="@id/progress" android:visibility="gone" android:layout_width="23.0dip" android:layout_height="23.0dip" android:indeterminateTint="@color/brand" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    </FrameLayout>
</androidx.appcompat.widget.LinearLayoutCompat>
