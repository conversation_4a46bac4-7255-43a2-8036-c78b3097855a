<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:paddingLeft="@dimen/dp_4" android:paddingRight="@dimen/dp_4" android:layout_width="fill_parent" android:layout_height="fill_parent" android:paddingHorizontal="@dimen/dp_4"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.transsion.home.view.filter.expand.TabExpandView android:orientation="vertical" android:id="@id/channelExpand" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="@dimen/dp_16" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <com.transsion.home.view.filter.expand.FilterExpandView android:orientation="vertical" android:id="@id/filterExpand" android:layout_width="fill_parent" android:layout_height="wrap_content" app:layout_constraintTop_toBottomOf="@id/channelExpand" />
    <ViewStub android:id="@id/loading_stub" android:layout="@layout/home_empty_view_loading" android:layout_width="wrap_content" android:layout_height="wrap_content" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/filterExpand" />
    <ViewStub android:id="@id/no_result_stub" android:layout="@layout/home_empty_no_result" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="91.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/filterExpand" />
    <ViewStub android:id="@id/no_network_stub" android:layout="@layout/home_empty_no_network" android:layout_width="fill_parent" android:layout_height="fill_parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>
