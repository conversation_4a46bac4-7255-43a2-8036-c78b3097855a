<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.appcompat.widget.AppCompatTextView android:id="@id/tv_title" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="16.0dip" android:text="@string/relative_subject_title" android:layout_marginStart="16.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" style="@style/style_title_text" />
    <androidx.constraintlayout.widget.ConstraintLayout android:id="@id/background" android:paddingTop="16.0dip" android:paddingBottom="16.0dip" android:layout_width="fill_parent" android:layout_height="wrap_content" android:paddingVertical="16.0dip" app:layout_constraintTop_toBottomOf="@id/tv_title">
        <include layout="@layout/item_associate_subject_common" />
    </androidx.constraintlayout.widget.ConstraintLayout>
    <View android:id="@id/divider" android:background="@color/line_01" android:layout_width="fill_parent" android:layout_height="8.0dip" app:layout_constraintTop_toBottomOf="@id/background" />
</androidx.constraintlayout.widget.ConstraintLayout>
