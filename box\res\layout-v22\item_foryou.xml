<?xml version="1.0" encoding="utf-8"?>
<FrameLayout android:id="@id/item_root" android:layout_width="fill_parent" android:layout_height="112.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.google.android.material.imageview.ShapeableImageView android:layout_gravity="start|center" android:id="@id/iv_cover" android:layout_width="72.0dip" android:layout_height="96.0dip" android:scaleType="centerCrop" android:layout_marginStart="16.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:shapeAppearanceOverlay="@style/corner_style_4" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="14.0sp" android:textColor="@color/text_01" android:ellipsize="end" android:layout_gravity="start" android:id="@id/tv_title" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="8.0dip" android:text="@string/no_network" android:lines="1" android:layout_marginStart="96.0dip" android:layout_marginEnd="16.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/iv_cover" app:layout_constraintTop_toTopOf="@id/iv_cover" style="@style/style_medium_text" />
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/text_03" android:ellipsize="end" android:gravity="start" android:layout_gravity="start" android:id="@id/tv_desc" android:visibility="visible" android:layout_width="wrap_content" android:layout_marginTop="30.0dip" android:maxLines="2" android:drawablePadding="4.0dip" android:layout_marginStart="96.0dip" android:layout_marginEnd="16.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/iv_cover" app:layout_constraintTop_toBottomOf="@id/tv_title" style="@style/style_regular_text" />
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/yellow_dark_70" android:layout_gravity="start|bottom" android:id="@id/tv_score" android:layout_marginBottom="18.0dip" android:drawablePadding="2.0dip" android:drawableStart="@drawable/ic_category_star" android:layout_marginStart="96.0dip" app:layout_constraintBottom_toBottomOf="@id/iv_cover" app:layout_constraintStart_toEndOf="@id/iv_cover" style="@style/style_medium_text" />
    <com.transsnet.downloader.widget.DownloadView android:gravity="center_vertical" android:layout_gravity="end|bottom" android:id="@id/ll_download" android:background="@drawable/bg_btn_01" android:paddingLeft="12.0dip" android:paddingRight="12.0dip" android:layout_width="wrap_content" android:layout_height="28.0dip" android:layout_marginTop="16.0dip" android:layout_marginBottom="12.0dip" android:layout_marginEnd="16.0dip" android:paddingHorizontal="12.0dip" app:layout_constraintBottom_toBottomOf="@id/tv_score" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="@id/tv_score" />
</FrameLayout>
