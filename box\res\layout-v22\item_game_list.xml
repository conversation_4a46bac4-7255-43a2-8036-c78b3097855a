<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:paddingLeft="8.0dip" android:paddingRight="8.0dip" android:layout_width="fill_parent" android:layout_height="wrap_content" android:paddingHorizontal="8.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.tn.lib.widget.TnTextView android:textSize="18.0sp" android:textColor="@color/text_01" android:ellipsize="end" android:id="@id/tv_game_title" android:layout_width="0.0dip" android:layout_height="wrap_content" android:maxLines="1" android:layout_marginStart="4.0dip" app:layout_constraintEnd_toStartOf="@id/tv_game_center" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" style="@style/robot_bold" />
    <com.tn.lib.widget.TnTextView android:textSize="14.0sp" android:id="@id/tv_game_center" android:layout_width="wrap_content" android:layout_height="wrap_content" android:includeFontPadding="false" android:layout_marginEnd="4.0dip" app:drawableEndCompat="@mipmap/ic_arrow_right" app:drawableTint="@color/text_02" app:layout_constraintBottom_toBottomOf="@id/tv_game_title" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="@id/tv_game_title" style="@style/robot_medium" />
    <androidx.recyclerview.widget.RecyclerView android:id="@id/game_list" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="8.0dip" app:layout_constraintTop_toBottomOf="@id/tv_game_title" />
</androidx.constraintlayout.widget.ConstraintLayout>
