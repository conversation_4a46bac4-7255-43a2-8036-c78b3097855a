<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.google.android.material.imageview.ShapeableImageView android:id="@id/iv_cover" android:layout_width="fill_parent" android:layout_height="0.0dip" android:scaleType="centerCrop" app:layout_constraintDimensionRatio="H,107:149" app:layout_constraintTop_toTopOf="parent" app:shapeAppearanceOverlay="@style/roundStyle_trending" />
    <com.tn.lib.view.CornerTextView android:id="@id/tv_tips" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_margin="2.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent" app:rectangleShape="true" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="@dimen/text_size_12" android:textColor="@color/white_80" android:ellipsize="end" android:id="@id/tv_title" android:background="@drawable/bg_trending_rank_title" android:paddingLeft="4.0dip" android:paddingTop="4.0dip" android:paddingRight="4.0dip" android:paddingBottom="6.0dip" android:layout_width="0.0dip" android:maxLines="1" android:paddingHorizontal="4.0dip" app:layout_constraintEnd_toEndOf="@id/iv_cover" app:layout_constraintStart_toStartOf="@id/iv_cover" app:layout_constraintTop_toBottomOf="@id/iv_cover" style="@style/style_regular_text" />
</androidx.constraintlayout.widget.ConstraintLayout>
