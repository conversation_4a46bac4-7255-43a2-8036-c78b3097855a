<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="wrap_content" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.google.android.material.imageview.ShapeableImageView android:id="@id/main_op_movie_rank_image" android:layout_width="wrap_content" android:layout_height="wrap_content" android:scaleType="centerCrop" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:shapeAppearanceOverlay="@style/roundStyle_trending" />
    <com.tn.lib.view.CornerTextView android:id="@id/main_op_movie_rank_corner" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_margin="2.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent" app:rectangleShape="true" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="@dimen/text_size_12" android:textColor="@color/white_80" android:ellipsize="end" android:id="@id/main_op_movie_rank_title" android:background="@drawable/bg_trending_rank_title" android:paddingLeft="4.0dip" android:paddingTop="4.0dip" android:paddingRight="4.0dip" android:paddingBottom="6.0dip" android:layout_width="0.0dip" android:maxLines="1" android:paddingHorizontal="4.0dip" app:layout_constraintEnd_toEndOf="@id/main_op_movie_rank_image" app:layout_constraintStart_toStartOf="@id/main_op_movie_rank_image" app:layout_constraintTop_toBottomOf="@id/main_op_movie_rank_image" style="@style/style_regular_text" />
</androidx.constraintlayout.widget.ConstraintLayout>
