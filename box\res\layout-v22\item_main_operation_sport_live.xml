<?xml version="1.0" encoding="utf-8"?>
<com.transsion.baseui.widget.RoundedConstraintLayout android:layout_width="wrap_content" android:layout_height="wrap_content" app:cornerRadius="4.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.google.android.material.imageview.ShapeableImageView android:id="@id/main_op_sport_live_image" android:layout_width="162.0dip" android:layout_height="90.0dip" android:scaleType="centerCrop" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <View android:background="@color/black_40" android:layout_width="0.0dip" android:layout_height="0.0dip" app:layout_constraintBottom_toBottomOf="@id/main_op_sport_live_image" app:layout_constraintEnd_toEndOf="@id/main_op_sport_live_image" app:layout_constraintStart_toStartOf="@id/main_op_sport_live_image" app:layout_constraintTop_toTopOf="@id/main_op_sport_live_image" />
    <androidx.constraintlayout.widget.ConstraintLayout android:layout_width="wrap_content" android:layout_height="wrap_content" app:layout_constraintBottom_toTopOf="@id/main_op_sport_live_title_text" app:layout_constraintEnd_toEndOf="@id/main_op_sport_live_image" app:layout_constraintStart_toStartOf="@id/main_op_sport_live_image" app:layout_constraintTop_toTopOf="parent">
        <ImageView android:id="@id/main_op_sport_live_vs" android:layout_width="25.0dip" android:layout_height="25.0dip" android:src="@mipmap/ic_op_sport_vs" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:tint="@color/white_80" />
        <FrameLayout android:id="@id/main_op_sport_live_team1_container" android:layout_width="32.0dip" android:layout_height="32.0dip" android:layout_marginEnd="12.0dip" app:layout_constraintBottom_toBottomOf="@id/main_op_sport_live_vs" app:layout_constraintEnd_toStartOf="@id/main_op_sport_live_vs" app:layout_constraintTop_toTopOf="@id/main_op_sport_live_vs">
            <com.google.android.material.imageview.ShapeableImageView android:layout_gravity="center" android:id="@id/main_op_sport_live_team1_image" android:layout_width="32.0dip" android:layout_height="32.0dip" android:src="@mipmap/ic_op_live_default" android:scaleType="centerCrop" app:shapeAppearanceOverlay="@style/circle_style" />
        </FrameLayout>
        <FrameLayout android:layout_width="32.0dip" android:layout_height="32.0dip" android:layout_marginStart="12.0dip" app:layout_constraintBottom_toBottomOf="@id/main_op_sport_live_vs" app:layout_constraintStart_toEndOf="@id/main_op_sport_live_vs" app:layout_constraintTop_toTopOf="@id/main_op_sport_live_vs">
            <com.google.android.material.imageview.ShapeableImageView android:layout_gravity="center" android:id="@id/main_op_sport_live_team2_image" android:layout_width="32.0dip" android:layout_height="32.0dip" android:src="@mipmap/ic_op_live_default" android:scaleType="centerCrop" app:shapeAppearanceOverlay="@style/circle_style" />
        </FrameLayout>
        <TextView android:textSize="@dimen/text_size_12" android:textColor="@color/white_60" android:ellipsize="end" android:id="@id/main_op_sport_live_time_text" android:layout_width="wrap_content" android:layout_marginTop="2.0dip" android:maxLines="1" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/main_op_sport_live_team1_container" style="@style/style_regular_text" />
    </androidx.constraintlayout.widget.ConstraintLayout>
    <TextView android:textSize="11.0sp" android:textColor="@color/white_80" android:gravity="center" android:id="@id/main_op_sport_live_status_text" android:background="@drawable/bg_corner_tips_rectangle_2" android:paddingLeft="6.0dip" android:paddingRight="6.0dip" android:layout_width="wrap_content" android:layout_height="18.0dip" android:layout_margin="4.0dip" android:text="@string/sport_live_upcoming" android:paddingHorizontal="6.0dip" app:layout_constraintStart_toStartOf="@id/main_op_sport_live_image" app:layout_constraintTop_toTopOf="@id/main_op_sport_live_image" style="@style/style_medium_text" />
    <TextView android:textSize="@dimen/text_size_12" android:textColor="@color/white_80" android:ellipsize="end" android:id="@id/main_op_sport_live_title_text" android:background="@drawable/bg_trending_rank_title" android:paddingLeft="4.0dip" android:paddingTop="4.0dip" android:paddingRight="4.0dip" android:paddingBottom="6.0dip" android:layout_width="0.0dip" android:maxLines="2" android:paddingHorizontal="4.0dip" app:layout_constraintEnd_toEndOf="@id/main_op_sport_live_image" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/main_op_sport_live_image" style="@style/style_regular_text" />
</com.transsion.baseui.widget.RoundedConstraintLayout>
