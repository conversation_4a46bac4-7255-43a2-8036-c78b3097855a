<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="wrap_content" android:paddingStart="@dimen/dimens_12" android:paddingEnd="@dimen/dimens_12"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.google.android.material.imageview.ShapeableImageView android:id="@id/rank_item_image" android:layout_width="100.0dip" android:layout_height="56.0dip" android:scaleType="centerCrop" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:shapeAppearanceOverlay="@style/roundStyle_8" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/rank_item_rank_tag" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_margin="4.0dip" app:layout_constraintStart_toStartOf="@id/rank_item_image" app:layout_constraintTop_toTopOf="@id/rank_item_image" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="@dimen/sp_12" android:textColor="@color/white" android:id="@id/rank_item_rank_duration" android:background="@drawable/bg_post_title_tag" android:paddingLeft="@dimen/dp_4" android:paddingTop="2.0dip" android:paddingRight="@dimen/dp_4" android:paddingBottom="2.0dip" android:layout_marginBottom="4.0dip" android:layout_marginEnd="4.0dip" android:backgroundTint="@color/black_40" android:paddingHorizontal="@dimen/dp_4" android:paddingVertical="2.0dip" app:layout_constraintBottom_toBottomOf="@id/rank_item_image" app:layout_constraintEnd_toEndOf="@id/rank_item_image" style="@style/style_medium_text" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/common_white" android:id="@id/rank_item_tag_rank" android:layout_marginTop="4.0dip" android:layout_marginStart="3.0dip" app:layout_constraintStart_toStartOf="@id/rank_item_rank_tag" app:layout_constraintTop_toTopOf="@id/rank_item_rank_tag" style="@style/style_import_text" />
    <com.tn.lib.widget.TnTextView android:textColor="@color/text_01" android:ellipsize="end" android:id="@id/rank_item_title" android:layout_width="0.0dip" android:maxLines="2" android:layout_marginStart="8.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/rank_item_image" app:layout_constraintTop_toTopOf="parent" style="@style/style_import_text" />
    <TextView android:textColor="@color/text_03" android:ellipsize="end" android:gravity="center_vertical" android:id="@id/rank_item_des" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginBottom="2.0dip" android:drawablePadding="1.0dip" android:drawableStart="@drawable/ic_education_student" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="@id/rank_item_title" style="@style/style_regular_text" />
</androidx.constraintlayout.widget.ConstraintLayout>
