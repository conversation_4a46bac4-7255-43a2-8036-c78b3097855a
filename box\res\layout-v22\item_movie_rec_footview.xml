<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:id="@id/item" android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.google.android.material.imageview.ShapeableImageView android:id="@id/iv_cover" android:layout_width="fill_parent" android:layout_height="150.0dip" android:scaleType="centerCrop" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:shapeAppearance="@style/corner_style_top_4" />
    <com.noober.background.view.BLTextView android:textSize="11.0sp" android:textColor="@color/white" android:gravity="center" android:id="@id/tv_download_foryou_corner" android:paddingLeft="6.0dip" android:paddingRight="6.0dip" android:layout_width="wrap_content" android:layout_height="18.0dip" android:layout_margin="2.0dip" android:paddingHorizontal="6.0dip" app:bl_corners_radius="2.0dip" app:bl_solid_color="@color/black_60" app:layout_constraintEnd_toEndOf="@id/iv_cover" app:layout_constraintTop_toTopOf="@id/iv_cover" style="@style/style_medium_text" />
    <com.noober.background.view.BLView android:id="@id/v_name_bg" android:layout_width="0.0dip" android:layout_height="25.0dip" app:bl_corners_bottomRadius="4.0dip" app:bl_solid_color="@color/white_6" app:layout_constraintEnd_toEndOf="@id/iv_cover" app:layout_constraintStart_toStartOf="@id/iv_cover" app:layout_constraintTop_toBottomOf="@id/iv_cover" />
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/text_01" android:ellipsize="end" android:gravity="start" android:id="@id/tv_title" android:paddingLeft="4.0dip" android:paddingRight="4.0dip" android:layout_width="0.0dip" android:maxLines="1" android:includeFontPadding="false" android:textAlignment="viewStart" android:paddingHorizontal="4.0dip" app:layout_constraintBottom_toBottomOf="@id/v_name_bg" app:layout_constraintEnd_toEndOf="@id/v_name_bg" app:layout_constraintStart_toStartOf="@id/v_name_bg" app:layout_constraintTop_toTopOf="@id/v_name_bg" style="@style/style_regular_text" />
</androidx.constraintlayout.widget.ConstraintLayout>
