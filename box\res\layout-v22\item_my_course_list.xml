<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:paddingTop="@dimen/dp_12" android:layout_width="fill_parent" android:layout_height="wrap_content" android:paddingStart="@dimen/dimens_12" android:paddingEnd="@dimen/dimens_12"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.google.android.material.imageview.ShapeableImageView android:id="@id/item_image" android:layout_width="120.0dip" android:layout_height="68.0dip" android:scaleType="fitCenter" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:shapeAppearanceOverlay="@style/roundStyle_8" />
    <TextView android:textColor="@color/text_01" android:ellipsize="end" android:id="@id/item_title" android:layout_width="0.0dip" android:maxLines="2" android:layout_marginStart="8.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/item_image" app:layout_constraintTop_toTopOf="parent" style="@style/style_import_text" />
    <TextView android:textColor="@color/resources_tip_color" android:ellipsize="end" android:id="@id/item_progress" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginBottom="@dimen/dp_8" app:layout_constraintBottom_toBottomOf="@id/item_image" app:layout_constraintEnd_toStartOf="@id/item_jump" app:layout_constraintStart_toStartOf="@id/item_title" style="@style/style_regular_text" />
    <TextView android:textSize="@dimen/sp_12" android:textColor="@color/white" android:gravity="center" android:id="@id/item_jump" android:background="@drawable/ad_shape_btn_bg" android:paddingLeft="16.0dip" android:paddingRight="16.0dip" android:visibility="invisible" android:layout_width="wrap_content" android:layout_height="@dimen/dimens_24" android:text="@string/course_learn" android:paddingHorizontal="16.0dip" app:layout_constraintBottom_toBottomOf="@id/item_image" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/item_progress" />
</androidx.constraintlayout.widget.ConstraintLayout>
