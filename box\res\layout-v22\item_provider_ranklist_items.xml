<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="@dimen/dp_12" android:layout_marginBottom="@dimen/dp_12" android:layout_marginVertical="@dimen/dp_12"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.google.android.material.imageview.ShapeableImageView android:id="@id/sub_operation_rankinglist_cover" android:layout_width="120.0dip" android:layout_height="68.0dip" android:scaleType="centerCrop" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:shapeAppearanceOverlay="@style/roundStyle_4" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/sub_operation_rankinglist_rank" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="4.0dip" android:layout_marginStart="4.0dip" app:layout_constraintStart_toStartOf="@id/sub_operation_rankinglist_cover" app:layout_constraintTop_toTopOf="@id/sub_operation_rankinglist_cover" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/common_white" android:id="@id/sub_operation_rankinglist_text" android:layout_marginTop="4.0dip" android:layout_marginStart="3.0dip" app:layout_constraintStart_toStartOf="@id/sub_operation_rankinglist_rank" app:layout_constraintTop_toTopOf="@id/sub_operation_rankinglist_rank" style="@style/style_import_text" />
    <ImageView android:id="@id/sub_operation_rankinglist_add_icon" android:background="@drawable/ad_shape_circle" android:padding="4.0dip" android:layout_width="24.0dip" android:layout_height="24.0dip" android:layout_margin="8.0dip" android:src="@mipmap/ic_add" android:backgroundTint="@color/black_50" app:layout_constraintBottom_toBottomOf="@id/sub_operation_rankinglist_cover" app:layout_constraintEnd_toEndOf="@id/sub_operation_rankinglist_cover" />
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/text_01" android:ellipsize="end" android:id="@id/sub_operation_rankinglist_title" android:layout_width="0.0dip" android:maxLines="3" android:layout_marginStart="8.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/sub_operation_rankinglist_cover" app:layout_constraintTop_toTopOf="parent" style="@style/style_medium_text" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/text_02" android:ellipsize="end" android:id="@id/sub_operation_rankinglist_tag" android:layout_width="0.0dip" android:layout_height="wrap_content" android:maxLines="1" android:layout_marginStart="8.0dip" app:layout_constraintBottom_toBottomOf="@id/sub_operation_rankinglist_cover" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/sub_operation_rankinglist_cover" style="@style/style_regular_text" />
</androidx.constraintlayout.widget.ConstraintLayout>
