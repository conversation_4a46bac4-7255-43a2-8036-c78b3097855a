<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:paddingTop="12.0dip" android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.google.android.material.imageview.ShapeableImageView android:id="@id/iv_cover" android:layout_width="52.0dip" android:layout_height="52.0dip" android:scaleType="centerCrop" android:layout_marginStart="12.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:shapeAppearance="@style/roundStyle_2" />
    <com.noober.background.view.BLTextView android:textSize="10.0sp" android:textColor="@color/white_80" android:ellipsize="end" android:gravity="center" android:id="@id/tv_new_count" android:paddingLeft="2.0dip" android:paddingRight="2.0dip" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="16.0dip" android:layout_marginTop="2.0dip" android:maxLines="1" android:layout_marginStart="2.0dip" android:paddingHorizontal="2.0dip" app:bl_corners_radius="2.0dip" app:bl_solid_color="@color/black_60" app:layout_constraintStart_toStartOf="@id/iv_cover" app:layout_constraintTop_toTopOf="@id/iv_cover" style="@style/style_medium_text" />
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/white_80" android:ellipsize="end" android:gravity="start" android:id="@id/tv_title" android:layout_width="0.0dip" android:maxLines="1" android:textAlignment="viewStart" android:layout_marginStart="8.0dip" app:layout_constraintBottom_toTopOf="@id/tv_member" app:layout_constraintEnd_toStartOf="@id/v_join" app:layout_constraintStart_toEndOf="@id/iv_cover" app:layout_constraintTop_toTopOf="@id/iv_cover" app:layout_constraintVertical_chainStyle="packed" app:layout_goneMarginEnd="12.0dip" style="@style/style_medium_text" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="11.0sp" android:textColor="@color/white_60" android:gravity="center" android:id="@id/tv_member" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/x_members" android:includeFontPadding="false" app:drawableStartCompat="@drawable/ic_room_member" app:layout_constraintStart_toStartOf="@id/tv_title" app:layout_constraintTop_toBottomOf="@id/tv_title" style="@style/style_regular_text" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="11.0sp" android:textColor="@color/white_60" android:ellipsize="end" android:gravity="start" android:id="@id/tv_tag" android:layout_width="0.0dip" android:maxLines="1" android:textAlignment="viewStart" app:layout_constraintBottom_toBottomOf="@id/iv_cover" app:layout_constraintEnd_toStartOf="@id/v_join" app:layout_constraintStart_toStartOf="@id/tv_title" app:layout_constraintTop_toBottomOf="@id/tv_member" app:layout_goneMarginEnd="12.0dip" style="@style/style_regular_text" />
    <com.tn.lib.view.RoomJoinAnimationView android:id="@id/v_join" android:visibility="gone" android:layout_width="40.0dip" android:layout_height="40.0dip" android:paddingEnd="12.0dip" app:jv_style="image" app:layout_constraintBottom_toBottomOf="@id/iv_cover" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="@id/iv_cover" />
    <com.noober.background.view.BLView android:id="@id/v_new_post_bg" android:layout_width="fill_parent" android:layout_height="44.0dip" android:layout_marginLeft="12.0dip" android:layout_marginTop="8.0dip" android:layout_marginRight="12.0dip" android:layout_marginHorizontal="12.0dip" app:bl_corners_radius="4.0dip" app:bl_solid_color="@color/white_6" app:layout_constraintTop_toBottomOf="@id/iv_cover" />
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/white_60" android:ellipsize="end" android:gravity="start" android:id="@id/tv_new_post_content" android:layout_width="0.0dip" android:maxLines="1" android:textAlignment="viewStart" android:layout_marginStart="8.0dip" android:layout_marginEnd="4.0dip" app:layout_constraintBottom_toBottomOf="@id/v_new_post_bg" app:layout_constraintEnd_toStartOf="@id/fl_new_post_cover" app:layout_constraintStart_toStartOf="@id/v_new_post_bg" app:layout_constraintTop_toTopOf="@id/v_new_post_bg" app:layout_goneMarginEnd="8.0dip" style="@style/style_regular_text" />
    <FrameLayout android:id="@id/fl_new_post_cover" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginEnd="8.0dip" app:layout_constraintBottom_toBottomOf="@id/v_new_post_bg" app:layout_constraintEnd_toEndOf="@id/v_new_post_bg" app:layout_constraintTop_toTopOf="@id/v_new_post_bg">
        <include android:id="@id/layout_new_post_image" android:visibility="gone" layout="@layout/layout_room_new_post_image" />
        <include android:id="@id/layout_new_post_video" android:visibility="gone" layout="@layout/layout_room_new_post_video" />
    </FrameLayout>
    <androidx.constraintlayout.widget.Group android:id="@id/group_new_post" android:layout_width="wrap_content" android:layout_height="wrap_content" app:constraint_referenced_ids="v_new_post_bg,         tv_new_post_content,fl_new_post_cover" />
    <View android:id="@id/v_line" android:background="@color/line_01" android:layout_width="0.0dip" android:layout_height="1.0dip" android:layout_marginLeft="12.0dip" android:layout_marginTop="12.0dip" android:layout_marginRight="12.0dip" android:layout_marginHorizontal="12.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/v_new_post_bg" />
</androidx.constraintlayout.widget.ConstraintLayout>
