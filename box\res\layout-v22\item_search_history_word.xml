<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:gravity="center_vertical" android:orientation="horizontal" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="8.0dip" android:maxWidth="156.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/text_01" android:ellipsize="end" android:gravity="start|center" android:id="@id/search_history_text" android:background="@drawable/bg_search_history" android:paddingLeft="@dimen/dp_8" android:paddingTop="@dimen/dp_4" android:paddingRight="@dimen/dp_8" android:paddingBottom="@dimen/dp_4" android:layout_width="wrap_content" android:layout_height="wrap_content" android:maxLines="1" android:singleLine="true" android:paddingHorizontal="@dimen/dp_8" android:paddingVertical="@dimen/dp_4" style="@style/style_regula_bigger_text" />
    <View android:layout_width="4.0dip" android:layout_height="20.0dip" />
    <View android:id="@id/search_history_item_divider" android:visibility="gone" android:layout_width="4.0dip" android:layout_height="20.0dip" />
    <ImageView android:id="@id/search_hot_history_more_image" android:paddingTop="4.0dip" android:paddingBottom="4.0dip" android:layout_width="wrap_content" android:layout_height="wrap_content" android:src="@drawable/ic_hide" android:contentDescription="@string/more" android:paddingVertical="4.0dip" app:tint="@color/white_80" />
</LinearLayout>
