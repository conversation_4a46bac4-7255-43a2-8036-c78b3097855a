<?xml version="1.0" encoding="utf-8"?>
<FrameLayout android:layout_width="wrap_content" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <TextView android:textSize="12.0sp" android:textColor="@color/text_01" android:ellipsize="end" android:gravity="center" android:id="@id/tagTV" android:background="@drawable/bg_radius2_white20" android:paddingLeft="4.0dip" android:paddingRight="4.0dip" android:layout_width="wrap_content" android:layout_height="20.0dip" android:maxLines="1" android:includeFontPadding="false" android:layout_marginEnd="@dimen/dp_8" android:paddingHorizontal="4.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
</FrameLayout>
