<?xml version="1.0" encoding="utf-8"?>
<merge android:clipChildren="false" android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.google.android.material.imageview.ShapeableImageView android:id="@id/ivCover" android:layout_width="104.0dip" android:layout_height="146.0dip" android:layout_marginTop="8.0dip" android:scaleType="centerCrop" android:layout_marginStart="16.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:shapeAppearance="@style/corner_style_4" />
    <com.tn.lib.view.CornerTextView android:id="@id/ivSearchCorner" android:layout_width="wrap_content" android:layout_height="wrap_content" app:layout_constraintEnd_toEndOf="@id/ivCover" app:layout_constraintTop_toTopOf="@id/ivCover" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textColor="@color/text_01" android:ellipsize="end" android:id="@id/tvSubject" android:visibility="visible" android:layout_width="0.0dip" android:maxLines="2" android:textAlignment="viewStart" android:layout_marginStart="8.0dip" android:layout_marginEnd="16.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/ivCover" app:layout_constraintTop_toTopOf="@id/ivCover" style="@style/style_medium_text" />
    <LinearLayout android:orientation="horizontal" android:id="@id/tagContentLL" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginTop="@dimen/dp_8" android:layout_marginStart="@dimen/dp_8" android:layout_marginEnd="@dimen/dp_8" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/ivCover" app:layout_constraintTop_toBottomOf="@id/tvSubject" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/text_02" android:ellipsize="end" android:gravity="start|center" android:id="@id/tvSubjectYear" android:layout_width="0.0dip" android:layout_marginTop="6.0dip" android:maxLines="2" android:drawablePadding="4.0dip" android:textAlignment="viewStart" android:layout_marginEnd="16.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="@id/tvSubject" app:layout_constraintTop_toBottomOf="@id/tagContentLL" style="@style/style_regula_bigger_text" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="14.0sp" android:textColor="@color/yellow_dark_70" android:gravity="center" android:id="@id/tvSubjectScore" android:layout_height="0.0dip" android:drawablePadding="2.0dip" android:drawableStart="@drawable/ic_category_star" android:layout_marginStart="8.0dip" app:layout_constraintBottom_toBottomOf="@id/llDownload" app:layout_constraintStart_toEndOf="@id/ivCover" app:layout_constraintTop_toTopOf="@id/llDownload" app:layout_constraintVertical_chainStyle="spread" style="@style/style_medium_text" />
    <com.transsnet.downloader.widget.DownloadView android:gravity="center_vertical" android:id="@id/llDownload" android:background="@drawable/bg_btn_01" android:paddingLeft="@dimen/dimens_24" android:paddingRight="@dimen/dimens_24" android:layout_width="wrap_content" android:layout_height="32.0dip" android:maxWidth="180.0dip" android:minWidth="120.0dip" android:layout_marginEnd="16.0dip" android:paddingHorizontal="@dimen/dimens_24" app:layout_constraintBottom_toBottomOf="@id/ivCover" app:layout_constraintEnd_toEndOf="parent" />
    <androidx.recyclerview.widget.RecyclerView android:id="@id/rvSeasons" android:layout_width="fill_parent" android:layout_height="32.0dip" android:layout_marginLeft="12.0dip" android:layout_marginTop="16.0dip" android:layout_marginRight="12.0dip" android:layout_marginHorizontal="12.0dip" app:layout_constraintTop_toBottomOf="@id/ivCover" />
    <View android:id="@id/line" android:background="@color/line_01" android:layout_width="fill_parent" android:layout_height="8.0dip" android:layout_marginTop="16.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintTop_toBottomOf="@id/rvSeasons" />
</merge>
