<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.google.android.material.imageview.ShapeableImageView android:id="@id/iv_cover" android:layout_width="91.0dip" android:layout_height="130.0dip" android:scaleType="centerCrop" android:layout_marginStart="16.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:shapeAppearanceOverlay="@style/roundStyle_4" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0dip" android:textColor="@color/text_01" android:gravity="start" android:id="@id/tv_title" android:layout_width="0.0dip" android:layout_height="wrap_content" android:includeFontPadding="false" android:layout_marginStart="8.0dip" android:layout_marginEnd="16.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/iv_cover" app:layout_constraintTop_toTopOf="@id/iv_cover" style="@style/style_title_text" />
    <androidx.recyclerview.widget.RecyclerView android:id="@id/rv_list" android:layout_width="0.0dip" android:layout_height="16.0dip" android:layout_marginTop="5.0dip" app:layout_constraintEnd_toEndOf="@id/tv_title" app:layout_constraintStart_toStartOf="@id/tv_title" app:layout_constraintTop_toBottomOf="@id/tv_title" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0dip" android:textColor="@color/text_02" android:ellipsize="end" android:gravity="start" android:id="@id/tv_type" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginTop="5.0dip" android:lines="1" android:includeFontPadding="false" android:textAlignment="viewStart" app:layout_constraintBottom_toBottomOf="@id/iv_cover" app:layout_constraintEnd_toEndOf="@id/tv_title" app:layout_constraintHorizontal_bias="0.0" app:layout_constraintStart_toStartOf="@id/tv_title" app:layout_constraintTop_toBottomOf="@id/rv_list" app:layout_constraintVertical_bias="0.0" style="@style/style_regular_text" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0dip" android:textColor="@color/text_02" android:ellipsize="end" android:gravity="start" android:id="@id/tv_desc" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginTop="5.0dip" android:maxLines="2" android:includeFontPadding="false" android:textAlignment="viewStart" app:layout_constraintBottom_toBottomOf="@id/iv_cover" app:layout_constraintEnd_toEndOf="@id/tv_title" app:layout_constraintHorizontal_bias="0.0" app:layout_constraintStart_toStartOf="@id/tv_title" app:layout_constraintTop_toBottomOf="@id/tv_type" app:layout_constraintVertical_bias="0.0" style="@style/style_regular_text" />
    <com.tn.lib.widget.TnTextView android:textSize="12.0dip" android:textColor="@color/white" android:ellipsize="end" android:gravity="center" android:id="@id/btn_download" android:background="@drawable/bg_btn_01_radius_4" android:paddingLeft="8.0dip" android:paddingTop="7.0dip" android:paddingRight="8.0dip" android:paddingBottom="7.0dip" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/str_download" android:maxLines="1" android:drawablePadding="2.0dip" android:paddingHorizontal="8.0dip" android:paddingVertical="7.0dip" app:drawableStartCompat="@mipmap/ic_download_white" app:layout_constraintBottom_toBottomOf="@id/iv_cover" app:layout_constraintStart_toStartOf="@id/tv_title" style="@style/style_medium_text" />
    <androidx.constraintlayout.widget.Group android:id="@id/group" android:layout_width="wrap_content" android:layout_height="wrap_content" app:constraint_referenced_ids="iv_cover,tv_title,rv_list,tv_type,tv_desc,btn_download" />
    <FrameLayout android:id="@id/flAdContainer" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginLeft="16.0dip" android:layout_marginRight="16.0dip" android:layout_marginHorizontal="16.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>
