<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="fill_parent" android:layout_marginLeft="@dimen/dp_4" android:layout_marginRight="@dimen/dp_4" android:layout_marginHorizontal="@dimen/dp_4"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.google.android.material.imageview.ShapeableImageView android:id="@id/home_sub_pager_item_image" android:layout_width="fill_parent" android:layout_height="360.0dip" android:scaleType="centerCrop" app:layout_constraintTop_toTopOf="parent" app:shapeAppearance="@style/roundStyle_4" />
    <View android:background="@drawable/black_30p_to_0p" android:layout_width="fill_parent" android:layout_height="360.0dip" app:layout_constraintTop_toTopOf="parent" />
    <com.transsnet.downloader.widget.DownloadView android:id="@id/sub_operation_banner_download" android:background="@drawable/bg_btn_01_radius_4" android:paddingLeft="10.0dip" android:paddingRight="10.0dip" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="32.0dip" android:layout_marginBottom="8.0dip" android:layout_marginEnd="8.0dip" android:paddingHorizontal="10.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintRight_toRightOf="parent" />
    <TextView android:textSize="16.0sp" android:textColor="@color/white" android:ellipsize="end" android:id="@id/sub_operation_banner_title" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="8.0dip" android:maxLines="1" android:layout_marginStart="8.0dip" android:layout_marginEnd="8.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>
