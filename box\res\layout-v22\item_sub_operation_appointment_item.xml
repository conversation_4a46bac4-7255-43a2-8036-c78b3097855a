<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="104.0dip" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.google.android.material.imageview.ShapeableImageView android:id="@id/sub_operation_appointment_image" android:layout_width="fill_parent" android:layout_height="134.0dip" android:scaleType="centerCrop" app:layout_constraintTop_toTopOf="parent" app:shapeAppearance="@style/roundStyle_trending" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="@dimen/text_size_10" android:textColor="@color/gray_dark_10" android:gravity="center" android:id="@id/sub_operation_appointment_corner" android:background="@mipmap/bg_appointment_corner" android:layout_width="23.0dip" android:layout_height="32.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" style="@style/style_extra_import_text" />
    <TextView android:textSize="11.0sp" android:textColor="@color/white_70" android:id="@id/sub_operation_appointment_booked" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginBottom="8.0dip" app:layout_constraintBottom_toBottomOf="@id/sub_operation_appointment_image" app:layout_constraintEnd_toEndOf="@id/sub_operation_appointment_image" app:layout_constraintStart_toStartOf="@id/sub_operation_appointment_image" style="@style/style_regular_text" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="@dimen/text_size_12" android:textColor="@color/white_80" android:id="@id/sub_operation_appointment_item_title" android:background="#0fffffff" android:padding="4.0dip" android:layout_width="0.0dip" android:singleLine="true" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/sub_operation_appointment_image" style="@style/style_regular_text" />
    <com.transsion.baseui.widget.RoundedConstraintLayout android:id="@id/sub_operation_appointment_unchecked" android:background="@drawable/bg_trending_rank_title" android:paddingLeft="4.0dip" android:paddingRight="4.0dip" android:paddingBottom="6.0dip" android:layout_width="fill_parent" android:layout_height="wrap_content" android:paddingHorizontal="4.0dip" app:layout_constraintTop_toBottomOf="@id/sub_operation_appointment_item_title">
        <ImageView android:id="@id/sub_operation_appointment_unchecked_icon" android:layout_width="16.0dip" android:layout_height="16.0dip" android:src="@mipmap/ic_sub_operation_appointment" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
        <com.transsion.baseui.widget.text.GradientTextView android:textSize="11.0sp" android:id="@id/sub_operation_appointment_unchecked_text" android:text="@string/remind_me" android:singleLine="true" android:layout_marginStart="4.0dip" app:layout_constraintBottom_toBottomOf="@id/sub_operation_appointment_unchecked_icon" app:layout_constraintStart_toEndOf="@id/sub_operation_appointment_unchecked_icon" app:layout_constraintTop_toTopOf="@id/sub_operation_appointment_unchecked_icon" style="@style/style_import_text" />
    </com.transsion.baseui.widget.RoundedConstraintLayout>
    <com.transsion.baseui.widget.RoundedConstraintLayout android:id="@id/sub_operation_appointment_checked" android:background="@drawable/bg_trending_rank_title" android:paddingLeft="4.0dip" android:paddingRight="4.0dip" android:paddingBottom="6.0dip" android:visibility="invisible" android:layout_width="fill_parent" android:layout_height="wrap_content" android:paddingHorizontal="4.0dip" app:cornerRadius="4.0dip" app:layout_constraintTop_toBottomOf="@id/sub_operation_appointment_item_title">
        <ImageView android:id="@id/sub_operation_appointment_checked_icon" android:layout_width="16.0dip" android:layout_height="16.0dip" android:src="@mipmap/ic_sub_operation_appointment_checked" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:tint="@color/white_60" />
        <androidx.appcompat.widget.AppCompatTextView android:textSize="11.0sp" android:textColor="@color/white_60" android:text="@string/reminder_set" android:singleLine="true" android:layout_marginStart="4.0dip" app:layout_constraintBottom_toBottomOf="@id/sub_operation_appointment_checked_icon" app:layout_constraintStart_toEndOf="@id/sub_operation_appointment_checked_icon" app:layout_constraintTop_toTopOf="@id/sub_operation_appointment_checked_icon" style="@style/style_import_text" />
    </com.transsion.baseui.widget.RoundedConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
