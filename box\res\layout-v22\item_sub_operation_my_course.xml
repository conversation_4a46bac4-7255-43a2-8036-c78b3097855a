<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:paddingLeft="@dimen/dp_12" android:paddingRight="@dimen/dp_12" android:layout_width="fill_parent" android:layout_height="wrap_content" android:paddingHorizontal="@dimen/dp_12"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <RelativeLayout android:id="@id/sub_operation_course_title" android:layout_width="fill_parent" android:layout_height="wrap_content" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent">
        <com.tn.lib.widget.TnTextView android:textSize="@dimen/dimens_sp_18" android:textColor="@color/text_01" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/education_add_course" style="@style/style_import_text" />
        <com.tn.lib.widget.TnTextView android:textColor="@color/text_02" android:ellipsize="end" android:maxWidth="68.0dip" android:text="@string/str_more" android:maxLines="1" android:includeFontPadding="false" android:layout_centerVertical="true" android:layout_alignParentEnd="true" app:drawableEndCompat="@mipmap/ic_arrow_right" app:drawableTint="@color/gray_0_60" style="@style/style_medium_text" />
    </RelativeLayout>
    <com.transsion.baseui.widget.RoundedConstraintLayout android:layout_width="fill_parent" android:layout_height="60.0dip" android:layout_marginTop="8.0dip" app:cornerRadius="4.0dip" app:layout_constraintTop_toBottomOf="@id/sub_operation_course_title">
        <LinearLayout android:orientation="horizontal" android:id="@id/sub_operation_course_start_liner" android:background="@color/pair_FFFFFF" android:padding="8.0dip" android:layout_width="fill_parent" android:layout_height="wrap_content">
            <ImageView android:background="@drawable/ad_shape_circle" android:padding="6.0dip" android:layout_width="44.0dip" android:layout_height="44.0dip" android:src="@mipmap/ic_my_course_add" android:backgroundTint="@color/sub_operation_filter_bg" />
            <TextView android:textSize="@dimen/sp_14" android:textColor="@color/text_01" android:ellipsize="end" android:layout_gravity="center" android:layout_width="0.0dip" android:layout_marginLeft="8.0dip" android:layout_marginRight="8.0dip" android:text="@string/course_start_add" android:maxLines="2" android:layout_weight="1.0" android:layout_marginHorizontal="8.0dip" style="@style/style_medium_text" />
            <TextView android:textSize="@dimen/sp_12" android:textColor="@color/white" android:gravity="center" android:layout_gravity="center" android:id="@id/sub_operation_course_explore" android:background="@drawable/bg_btn_01_radius_4" android:paddingLeft="8.0dip" android:paddingRight="8.0dip" android:layout_width="wrap_content" android:layout_height="28.0dip" android:minWidth="64.0dip" android:text="@string/course_explore" android:paddingHorizontal="8.0dip" style="@style/style_medium_text" />
        </LinearLayout>
        <LinearLayout android:gravity="center" android:orientation="horizontal" android:id="@id/sub_operation_course_item_liner" android:background="@color/pair_FFFFFF" android:padding="8.0dip" android:visibility="invisible" android:layout_width="fill_parent" android:layout_height="wrap_content">
            <com.google.android.material.imageview.ShapeableImageView android:id="@id/sub_operation_course_item_image" android:layout_width="80.0dip" android:layout_height="45.0dip" android:scaleType="centerCrop" app:shapeAppearance="@style/roundStyle_4" />
            <androidx.constraintlayout.widget.ConstraintLayout android:layout_width="0.0dip" android:layout_height="fill_parent" android:layout_marginLeft="8.0dip" android:layout_marginRight="8.0dip" android:layout_weight="1.0" android:layout_marginHorizontal="8.0dip">
                <TextView android:textSize="12.0sp" android:textColor="@color/text_01" android:ellipsize="end" android:id="@id/sub_operation_course_item_title_text" android:maxLines="2" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" style="@style/style_medium_text" />
                <TextView android:textColor="@color/text_03" android:id="@id/sub_operation_course_item_percent_text" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" style="@style/style_regular_text" />
                <View android:id="@id/sub_operation_course_item_percent_bg" android:background="@drawable/bg_module_05_radius_4" android:layout_width="0.0dip" android:layout_height="4.0dip" android:layout_marginEnd="8.0dip" app:layout_constraintBottom_toBottomOf="@id/sub_operation_course_item_percent_text" app:layout_constraintEnd_toStartOf="@id/sub_operation_course_item_percent_text" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="@id/sub_operation_course_item_percent_text" />
                <View android:id="@id/sub_operation_course_item_percent_view" android:background="@drawable/bg_brand_corner_4" android:layout_width="0.0dip" android:layout_height="4.0dip" android:layout_marginEnd="8.0dip" app:layout_constraintBottom_toBottomOf="@id/sub_operation_course_item_percent_text" app:layout_constraintStart_toStartOf="@id/sub_operation_course_item_percent_bg" app:layout_constraintTop_toTopOf="@id/sub_operation_course_item_percent_text" app:layout_constraintWidth_percent="0.0" />
            </androidx.constraintlayout.widget.ConstraintLayout>
            <TextView android:textSize="@dimen/sp_12" android:textColor="@color/white" android:gravity="center" android:layout_gravity="center" android:id="@id/sub_operation_course_learn" android:background="@drawable/bg_btn_01_radius_4" android:paddingLeft="8.0dip" android:paddingRight="8.0dip" android:layout_width="wrap_content" android:layout_height="28.0dip" android:minWidth="64.0dip" android:text="@string/course_learn" android:paddingHorizontal="8.0dip" style="@style/style_medium_text" />
        </LinearLayout>
    </com.transsion.baseui.widget.RoundedConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
