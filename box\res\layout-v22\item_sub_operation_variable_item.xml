<?xml version="1.0" encoding="utf-8"?>
<com.transsion.baseui.widget.RoundedConstraintLayout android:layout_width="wrap_content" android:layout_height="wrap_content" app:cornerRadius="4.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.google.android.material.imageview.ShapeableImageView android:id="@id/sub_operation_variable_image" android:layout_width="wrap_content" android:layout_height="wrap_content" android:scaleType="centerCrop" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="@dimen/text_size_12" android:textColor="@color/white_80" android:id="@id/sub_operation_variable_item_title" android:background="@drawable/bg_trending_rank_title" android:paddingLeft="4.0dip" android:paddingTop="4.0dip" android:paddingRight="4.0dip" android:paddingBottom="6.0dip" android:layout_width="0.0dip" android:singleLine="true" android:paddingHorizontal="4.0dip" app:layout_constraintEnd_toEndOf="@id/sub_operation_variable_image" app:layout_constraintStart_toStartOf="@id/sub_operation_variable_image" app:layout_constraintTop_toBottomOf="@id/sub_operation_variable_image" style="@style/style_medium_text" />
</com.transsion.baseui.widget.RoundedConstraintLayout>
