<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:background="@drawable/bg_sku_12_radius" android:paddingTop="12.0dip" android:paddingBottom="12.0dip" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginLeft="16.0dip" android:layout_marginTop="12.0dip" android:layout_marginRight="16.0dip" android:layout_marginBottom="16.0dip" android:layout_marginHorizontal="16.0dip" android:paddingVertical="12.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.transsion.member.view.InviteUserView android:id="@id/task_invite_user_view" android:visibility="gone" android:layout_width="0.0dip" android:layout_height="0.0dip" app:iuv_activeItemLayout="@layout/layout_item_invite_user_active" app:iuv_doneItemLayout="@layout/layout_item_invite_user_done" app:iuv_loadMoreEnable="true" app:iuv_loadMoreItemLayout="@layout/layout_item_invite_user_loadmore" app:iuv_unActiveItemLayout="@layout/layout_item_invite_user_unactive" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <TextView android:textSize="15.0sp" android:gravity="center" android:id="@id/vdPause" android:paddingBottom="7.0dip" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginLeft="16.0dip" android:layout_marginTop="5.0dip" android:layout_marginRight="16.0dip" android:layout_marginBottom="5.0dip" android:text="" android:fontFamily="@font/mulish_extra_bold" android:layout_marginHorizontal="16.0dip" android:paddingVertical="12.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <TextView android:textSize="14.0sp" android:textStyle="bold" android:textColor="@color/gray_dark_00" android:gravity="center" android:id="@id/claim_all_bt" android:background="@drawable/bg_member_btn_6" android:paddingTop="6.0dip" android:paddingBottom="6.0dip" android:visibility="gone" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginTop="12.0dip" android:text="@string/task_invite_user_view_get_premium_text" android:layout_marginEnd="4.0dip" android:paddingVertical="6.0dip" app:layout_constraintEnd_toStartOf="@id/invite_tips_bt" app:layout_constraintHorizontal_weight="1.0" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/task_invite_user_view" />
    <TextView android:textSize="14.0sp" android:textStyle="bold" android:textColor="@color/gray_dark_00" android:ellipsize="middle" android:gravity="center" android:id="@id/invite_tips_bt" android:background="@drawable/bg_member_btn_6" android:paddingTop="6.0dip" android:paddingBottom="6.0dip" android:visibility="gone" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginTop="12.0dip" android:text="@string/task_invite_user_view_get_tip_text" android:singleLine="true" android:layout_marginStart="4.0dip" android:paddingVertical="6.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintHorizontal_weight="1.0" app:layout_constraintStart_toEndOf="@id/claim_all_bt" app:layout_constraintTop_toBottomOf="@id/task_invite_user_view" />
    <TextView android:textSize="14.0sp" android:textStyle="bold" android:textColor="@color/gray_dark_00" android:gravity="center" android:id="@id/vdVideoTime" android:background="@drawable/bg_member_btn_6" android:paddingTop="6.0dip" android:paddingBottom="6.0dip" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginTop="12.0dip" android:text="Donasi" android:onClick="Donasi" android:layout_marginEnd="4.0dip" android:paddingVertical="6.0dip" app:layout_constraintEnd_toStartOf="@id/vdSeekbar" app:layout_constraintHorizontal_weight="1.0" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/vdPause" />
    <TextView android:textSize="14.0sp" android:textStyle="bold" android:textColor="@color/gray_dark_00" android:ellipsize="middle" android:gravity="center" android:id="@id/vdSeekbar" android:background="@drawable/bg_member_btn_6" android:paddingTop="6.0dip" android:paddingBottom="6.0dip" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginTop="12.0dip" android:text="Facebook" android:singleLine="true" android:onClick="Telegram" android:layout_marginStart="4.0dip" android:paddingVertical="6.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintHorizontal_weight="1.0" app:layout_constraintStart_toEndOf="@id/vdVideoTime" app:layout_constraintTop_toBottomOf="@id/vdPause" />
</androidx.constraintlayout.widget.ConstraintLayout>
