<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:background="@drawable/selector_video_detail_episode_bg" android:layout_width="fill_parent" android:layout_height="44.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/selector_video_detail_episode_name" android:gravity="center" android:layout_gravity="center" android:id="@id/tv_name" android:paddingTop="14.0dip" android:paddingBottom="14.0dip" android:paddingVertical="14.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintHorizontal_chainStyle="packed" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" style="@style/style_medium_text" />
    <androidx.appcompat.widget.AppCompatImageView android:layout_gravity="end" android:id="@id/iv_download_status" android:background="@drawable/shape_video_detail_download_status_bg" android:paddingLeft="4.0dip" android:paddingTop="1.0dip" android:paddingRight="4.0dip" android:paddingBottom="1.0dip" android:layout_width="16.0dip" android:layout_height="14.0dip" android:src="@mipmap/video_detail_ic_episode_download_status" android:paddingHorizontal="4.0dip" android:paddingVertical="1.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>
