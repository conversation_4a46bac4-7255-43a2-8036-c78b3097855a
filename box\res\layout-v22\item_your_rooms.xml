<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.google.android.material.imageview.ShapeableImageView android:id="@id/iv_cover" android:background="@color/gray_dark_20" android:layout_width="fill_parent" android:layout_height="78.0dip" android:scaleType="centerCrop" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:shapeAppearance="@style/corner_style_top_4" />
    <com.noober.background.view.BLTextView android:textSize="13.0sp" android:textColor="@color/white_80" android:ellipsize="end" android:gravity="center" android:id="@id/tv_new_count" android:paddingLeft="6.0dip" android:paddingRight="6.0dip" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="18.0dip" android:layout_marginTop="2.0dip" android:maxLines="1" android:layout_marginStart="2.0dip" android:paddingHorizontal="6.0dip" app:bl_corners_radius="2.0dip" app:bl_solid_color="@color/black_60" app:layout_constraintStart_toStartOf="@id/iv_cover" app:layout_constraintTop_toTopOf="@id/iv_cover" style="@style/style_medium_text" />
    <com.noober.background.view.BLView android:id="@id/v_name_bg" android:layout_width="0.0dip" android:layout_height="25.0dip" app:bl_corners_bottomRadius="4.0dip" app:bl_solid_color="@color/white_6" app:layout_constraintEnd_toEndOf="@id/iv_cover" app:layout_constraintStart_toStartOf="@id/iv_cover" app:layout_constraintTop_toBottomOf="@id/iv_cover" />
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/text_01" android:ellipsize="end" android:gravity="center" android:id="@id/tv_name" android:paddingLeft="4.0dip" android:paddingRight="4.0dip" android:layout_width="0.0dip" android:layout_marginTop="4.0dip" android:maxLines="1" android:paddingHorizontal="4.0dip" app:layout_constraintEnd_toEndOf="@id/iv_cover" app:layout_constraintStart_toStartOf="@id/iv_cover" app:layout_constraintTop_toBottomOf="@id/iv_cover" style="@style/style_regula_bigger_text" />
    <com.noober.background.view.BLView android:id="@id/v_stroke" android:layout_width="0.0dip" android:layout_height="0.0dip" app:bl_corners_radius="4.0dip" app:bl_stroke_color="@color/white_10" app:bl_stroke_width="1.0dip" app:layout_constraintBottom_toBottomOf="@id/v_name_bg" app:layout_constraintEnd_toEndOf="@id/iv_cover" app:layout_constraintStart_toStartOf="@id/iv_cover" app:layout_constraintTop_toTopOf="@id/iv_cover" />
</androidx.constraintlayout.widget.ConstraintLayout>
