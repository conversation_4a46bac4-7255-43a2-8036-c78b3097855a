<?xml version="1.0" encoding="utf-8"?>
<merge
  xmlns:android="http://schemas.android.com/apk/res/android">
    <TextView android:id="@id/notification_title_tv" android:layout_width="wrap_content" android:layout_height="wrap_content" android:maxLines="2" android:layout_below="@id/header" android:lineHeight="17.0dip" style="@style/Push_Notification_Title" />
    <ImageView android:id="@id/notification_content_image" android:layout_width="fill_parent" android:layout_height="168.0dip" android:layout_marginTop="4.0dip" android:src="@mipmap/notification_placeholder" android:scaleType="centerCrop" android:layout_below="@id/notification_title_tv" />
    <ImageView android:id="@id/notification_last" android:layout_width="24.0dip" android:layout_height="24.0dip" android:src="@mipmap/ic_push_left" android:layout_alignTop="@id/notification_content_image" android:layout_alignBottom="@id/notification_content_image" android:layout_marginStart="8.0dip" />
    <ImageView android:id="@id/notification_next" android:layout_width="24.0dip" android:layout_height="24.0dip" android:src="@mipmap/ic_push_right" android:layout_alignTop="@id/notification_content_image" android:layout_alignBottom="@id/notification_content_image" android:layout_marginEnd="8.0dip" android:layout_alignEnd="@id/notification_content_image" />
    <TextView android:textColor="@color/white" android:gravity="center" android:id="@id/page_num" android:background="@drawable/bg_push_page_num" android:paddingLeft="8.0dip" android:paddingTop="2.0dip" android:paddingRight="8.0dip" android:paddingBottom="2.0dip" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginBottom="8.0dip" android:layout_alignBottom="@id/notification_content_image" android:layout_marginEnd="8.0dip" android:layout_alignEnd="@id/notification_content_image" android:paddingHorizontal="8.0dip" android:paddingVertical="2.0dip" />
</merge>
