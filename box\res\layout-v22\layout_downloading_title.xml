<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:clipChildren="false" android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textColor="@color/text_01" android:id="@id/tv_title" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/str_downloading_title" android:includeFontPadding="false" android:layout_marginStart="12.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" style="@style/style_import_text" />
    <com.transsion.baseui.widget.GradientTextView android:layout_gravity="end" android:id="@id/tv_transfer" android:paddingLeft="12.0dip" android:paddingTop="14.5dip" android:paddingRight="12.0dip" android:paddingBottom="14.5dip" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/download_title_manager" android:includeFontPadding="false" android:paddingHorizontal="12.0dip" android:paddingVertical="14.5dip" app:gradientTvCenterColor="@color/brand_new_gradient_center" app:gradientTvEndColor="@color/brand_new_gradient_end" app:gradientTvStartColor="@color/brand_new_gradient_start" app:layout_constraintBottom_toBottomOf="@id/tv_title" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="@id/tv_title" style="@style/style_medium_small_text" />
</androidx.constraintlayout.widget.ConstraintLayout>
