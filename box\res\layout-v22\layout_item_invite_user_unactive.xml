<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:background="@drawable/bg_item_member_check_in_8" android:layout_width="fill_parent" android:layout_height="76.0dip" android:minWidth="60.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <ImageView android:id="@id/tag_iv" android:layout_width="20.0dip" android:layout_height="20.0dip" android:layout_marginTop="8.0dip" android:src="@mipmap/ic_member_small" android:scaleType="fitXY" android:importantForAccessibility="no" app:layout_constraintLeft_toLeftOf="parent" app:layout_constraintRight_toRightOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <TextView android:textSize="14.0sp" android:textStyle="bold" android:textColor="@color/white" android:ellipsize="end" android:gravity="center" android:id="@id/member_reward" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="4.0dip" android:layout_marginBottom="8.0dip" android:text="+5 Day" android:lines="1" app:layout_constraintBottom_toTopOf="@id/member_invite_user_tv" app:layout_constraintLeft_toLeftOf="parent" app:layout_constraintRight_toRightOf="parent" app:layout_constraintTop_toBottomOf="@id/tag_iv" />
    <TextView android:textSize="12.0sp" android:textStyle="normal" android:textColor="@color/white_60" android:gravity="center" android:id="@id/member_invite_user_tv" android:background="@drawable/bg_item_task_done_bottom_8" android:paddingLeft="2.0dip" android:paddingRight="2.0dip" android:layout_width="fill_parent" android:layout_height="20.0dip" android:minWidth="60.0dip" android:text="Friend 7" android:paddingHorizontal="2.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>
