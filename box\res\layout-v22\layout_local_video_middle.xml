<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:id="@id/middle_root" android:background="@color/bg_01" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <View android:id="@id/guideline" android:background="@mipmap/post_detail_local_video_bg" android:layout_width="0.0dip" android:layout_height="0.0dip" app:layout_constraintDimensionRatio="h,16:9" app:layout_constraintLeft_toLeftOf="parent" app:layout_constraintRight_toRightOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <FrameLayout android:id="@id/video_land_surface" android:layout_width="0.0dip" android:layout_height="0.0dip" app:layout_constraintBottom_toBottomOf="@id/guideline" app:layout_constraintEnd_toEndOf="@id/guideline" app:layout_constraintStart_toStartOf="@id/guideline" app:layout_constraintTop_toTopOf="@id/guideline" />
    <View android:id="@id/v_gesture" android:layout_width="0.0dip" android:layout_height="0.0dip" android:layout_marginStart="10.0dip" android:layout_marginEnd="10.0dip" app:layout_constraintBottom_toBottomOf="@id/guideline" app:layout_constraintEnd_toEndOf="@id/guideline" app:layout_constraintStart_toStartOf="@id/guideline" app:layout_constraintTop_toTopOf="@id/guideline" />
    <View android:id="@id/land_view1" android:background="@drawable/post_detail_shape_local_top_bg" android:layout_width="fill_parent" android:layout_height="80.0dip" app:layout_constraintTop_toTopOf="@id/guideline" />
    <View android:id="@id/land_view2" android:background="@drawable/post_detail_shape_local_bottom_bg" android:layout_width="fill_parent" android:layout_height="80.0dip" app:layout_constraintBottom_toBottomOf="@id/guideline" />
    <FrameLayout android:id="@id/flRootSubtitle" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginLeft="12.0dip" android:layout_marginRight="12.0dip" android:layout_marginBottom="16.0dip" android:layout_marginHorizontal="12.0dip" app:layout_constraintBottom_toBottomOf="@id/guideline" app:layout_constraintEnd_toEndOf="@id/guideline" app:layout_constraintStart_toStartOf="@id/guideline">
        <androidx.appcompat.widget.LinearLayoutCompat android:orientation="vertical" android:id="@id/flSubtitle" android:layout_width="fill_parent" android:layout_height="wrap_content">
            <com.avery.subtitle.widget.SimpleSubtitleView android:textSize="16.0sp" android:textColor="@color/white" android:ellipsize="end" android:gravity="center" android:layout_gravity="center" android:id="@id/vSubtitleTop" android:paddingLeft="6.0dip" android:paddingTop="3.0dip" android:paddingRight="6.0dip" android:paddingBottom="3.0dip" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:shadowColor="@color/black_90" android:shadowDx="2.0" android:shadowDy="1.0" android:shadowRadius="2.0" android:lineSpacingExtra="1.0dip" android:layout_marginStart="24.0dip" android:layout_marginEnd="24.0dip" android:paddingHorizontal="6.0dip" android:paddingVertical="3.0dip" />
            <com.avery.subtitle.widget.SimpleSubtitleView android:textSize="16.0sp" android:textColor="@color/white" android:ellipsize="end" android:gravity="center" android:layout_gravity="center" android:id="@id/vSubtitleBottom" android:paddingLeft="6.0dip" android:paddingTop="3.0dip" android:paddingRight="6.0dip" android:paddingBottom="3.0dip" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="2.0dip" android:shadowColor="@color/black_90" android:shadowDx="2.0" android:shadowDy="1.0" android:shadowRadius="2.0" android:lineSpacingExtra="1.0dip" android:layout_marginStart="24.0dip" android:layout_marginEnd="24.0dip" android:paddingHorizontal="6.0dip" android:paddingVertical="3.0dip" />
        </androidx.appcompat.widget.LinearLayoutCompat>
    </FrameLayout>
    <androidx.appcompat.widget.AppCompatImageView android:layout_gravity="end|center" android:id="@id/iv_feedback" android:layout_width="38.0dip" android:layout_height="wrap_content" android:layout_marginTop="10.0dip" app:layout_constraintBottom_toTopOf="@id/tvHelpTip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="@id/vd_land_iv_back" app:layout_constraintVertical_chainStyle="packed" app:srcCompat="@mipmap/ic_feedback_transparent" />
    <TextView android:textSize="8.0sp" android:textColor="@color/white" android:gravity="center|top" android:id="@id/tvHelpTip" android:layout_width="0.0dip" android:layout_height="20.0dip" android:text="@string/title_help" app:layout_constraintBottom_toBottomOf="@id/vd_land_iv_back" app:layout_constraintEnd_toEndOf="@id/iv_feedback" app:layout_constraintStart_toStartOf="@id/iv_feedback" app:layout_constraintTop_toBottomOf="@id/iv_feedback" />
    <androidx.constraintlayout.widget.Group android:id="@id/vd_land_toolbar" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" app:constraint_referenced_ids="iv_feedback,tvHelpTip" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="18.0sp" android:textColor="#ccffffff" android:id="@id/vd_land_center_progress" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" app:layout_constraintBottom_toBottomOf="@id/guideline" app:layout_constraintEnd_toEndOf="@id/guideline" app:layout_constraintStart_toStartOf="@id/guideline" app:layout_constraintTop_toTopOf="@id/guideline" />
    <LinearLayout android:gravity="center" android:id="@id/vd_surface_loading" android:layout_width="wrap_content" android:layout_height="wrap_content" app:layout_constraintBottom_toBottomOf="@id/guideline" app:layout_constraintEnd_toEndOf="@id/guideline" app:layout_constraintStart_toStartOf="@id/guideline" app:layout_constraintTop_toTopOf="@id/guideline">
        <ProgressBar android:layout_width="20.0dip" android:layout_height="20.0dip" android:indeterminateTint="@color/main" />
        <TextView android:textSize="14.0sp" android:textColor="@color/white" android:id="@id/tv_loading" android:layout_width="wrap_content" android:layout_height="wrap_content" android:minWidth="67.0dip" android:text="@string/post_loading" android:shadowColor="@color/black_50" android:shadowRadius="3.0" android:layout_marginStart="8.0dip" style="@style/style_medium_text" />
        <TextView android:textSize="14.0sp" android:textColor="@color/white" android:id="@id/tv_speed" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/post_loading" android:shadowColor="@color/black_50" android:shadowRadius="3.0" style="@style/style_medium_text" />
    </LinearLayout>
    <LinearLayout android:orientation="horizontal" android:id="@id/vd_land_bottom_controller" android:layout_width="0.0dip" android:layout_height="40.0dip" app:layout_constraintBottom_toBottomOf="@id/guideline" app:layout_constraintEnd_toEndOf="@id/guideline" app:layout_constraintStart_toStartOf="@id/guideline">
        <androidx.appcompat.widget.AppCompatImageView android:layout_gravity="center_vertical" android:id="@id/vd_pause" android:layout_width="48.0dip" android:layout_height="40.0dip" android:src="@mipmap/icon_player_pause" android:scaleType="center" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" />
        <com.tn.lib.view.SecondariesSeekBar android:layout_gravity="center_vertical" android:id="@id/vd_seekbar" android:background="@color/transparent" android:layout_width="0.0dip" android:layout_height="24.0dip" android:maxHeight="2.0dip" android:minHeight="2.0dip" android:layout_weight="1.0" android:layout_marginStart="4.0dip" android:layout_marginEnd="12.0dip" app:layout_constraintBottom_toBottomOf="@id/vd_pause" app:layout_constraintEnd_toStartOf="@id/vd_video_time" app:layout_constraintStart_toEndOf="@id/vd_pause" app:layout_constraintTop_toTopOf="@id/vd_pause" app:ssb_bar_center_color="@color/main_gradient_center" app:ssb_bar_end_color="@color/main_gradient_end" app:ssb_bar_start_color="@color/main_gradient_start" app:ssb_bg_color="@color/white_30" app:ssb_secondaries_color="@color/white" app:ssb_thumb_color="@color/white" app:ssb_thumb_size="8.0dip" />
        <TextView android:textSize="11.0sp" android:textColor="@color/white" android:gravity="end" android:layout_gravity="end|center" android:id="@id/vd_video_time" android:layout_width="wrap_content" android:layout_height="wrap_content" android:minWidth="78.5sp" android:text="00:00/00:00" android:shadowColor="@color/cl31_50_p" android:shadowDy="2.0" android:shadowRadius="2.0" android:layout_marginEnd="6.0dip" app:layout_constraintBottom_toBottomOf="@id/vd_pause" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="@id/vd_pause" />
        <androidx.appcompat.widget.AppCompatImageView android:layout_gravity="center_vertical" android:id="@id/iv_float" android:layout_width="36.0dip" android:layout_height="40.0dip" android:src="@mipmap/ic_player_float" android:scaleType="center" />
        <androidx.appcompat.widget.AppCompatImageView android:layout_gravity="end|center" android:id="@id/vd_screen_change" android:layout_width="36.0dip" android:layout_height="40.0dip" android:src="@mipmap/icon_player_full_screen" android:scaleType="center" android:layout_marginEnd="6.0dip" app:layout_constraintEnd_toEndOf="parent" />
    </LinearLayout>
    <androidx.appcompat.widget.AppCompatTextView android:textSize="@dimen/sp_14" android:textColor="@color/white" android:gravity="center" android:id="@id/tvPressSpeed" android:background="@drawable/bg_radius4_black60" android:paddingLeft="@dimen/dp_12" android:paddingRight="@dimen/dp_12" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="@dimen/dimens_24" android:layout_marginTop="32.0dip" android:text="2x" android:includeFontPadding="false" android:drawablePadding="@dimen/dp_4" android:drawableEnd="@drawable/ic_quick_speed" android:paddingHorizontal="@dimen/dp_12" app:layout_constraintEnd_toEndOf="@id/vd_land_bottom_controller" app:layout_constraintStart_toStartOf="@id/vd_land_bottom_controller" app:layout_constraintTop_toTopOf="@id/guideline" />
    <TextView android:textSize="12.0sp" android:textColor="@color/white" android:gravity="center_vertical" android:id="@id/tv_toast_2" android:background="@drawable/post_detail_shape_black_trans70_8dp" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="32.0dip" android:layout_marginBottom="8.0dip" android:paddingStart="12.0dip" android:paddingEnd="12.0dip" android:layout_marginStart="12.0dip" app:layout_constraintBottom_toTopOf="@id/tv_toast_1" app:layout_constraintStart_toStartOf="@id/guideline" style="@style/style_medium_text" />
    <TextView android:textSize="12.0sp" android:textColor="@color/white" android:gravity="center_vertical" android:id="@id/tv_toast_1" android:background="@drawable/post_detail_shape_black_trans70_8dp" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="32.0dip" android:layout_marginBottom="8.0dip" android:paddingStart="12.0dip" android:paddingEnd="12.0dip" android:layout_marginStart="12.0dip" app:layout_constraintBottom_toTopOf="@id/vd_land_bottom_controller" app:layout_constraintStart_toStartOf="@id/guideline" style="@style/style_medium_text" />
    <ViewStub android:id="@id/vs_load_failed" android:layout="@layout/layout_local_video_load_failed" android:layout_width="wrap_content" android:layout_height="wrap_content" app:layout_constraintBottom_toBottomOf="@id/guideline" app:layout_constraintEnd_toEndOf="@id/guideline" app:layout_constraintStart_toStartOf="@id/guideline" app:layout_constraintTop_toTopOf="@id/guideline" />
    <ViewStub android:id="@id/vs_replay" android:layout="@layout/layout_local_video_load_replay" android:layout_width="0.0dip" android:layout_height="0.0dip" app:layout_constraintBottom_toBottomOf="@id/guideline" app:layout_constraintEnd_toEndOf="@id/guideline" app:layout_constraintStart_toStartOf="@id/guideline" app:layout_constraintTop_toTopOf="@id/guideline" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/vd_land_iv_back" android:paddingTop="10.0dip" android:paddingBottom="10.0dip" android:layout_width="44.0dip" android:layout_height="44.0dip" android:src="@mipmap/icon_white_back" android:scaleType="center" android:paddingStart="10.0dip" android:paddingEnd="10.0dip" android:layout_marginStart="6.0dip" app:layout_constraintStart_toStartOf="@id/guideline" app:layout_constraintTop_toTopOf="@id/guideline" />
    <include android:id="@id/layout_bv" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="68.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" layout="@layout/orplayer_layout_brightness_volume" />
    <androidx.coordinatorlayout.widget.CoordinatorLayout android:id="@id/cdl" android:layout_width="fill_parent" android:layout_height="0.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintTop_toBottomOf="@id/vd_land_bottom_controller">
        <com.google.android.material.appbar.AppBarLayout android:orientation="vertical" android:id="@id/appBar" android:layout_width="fill_parent" android:layout_height="wrap_content">
            <com.google.android.material.appbar.CollapsingToolbarLayout android:id="@id/toolbar_layout" android:layout_width="fill_parent" android:layout_height="wrap_content" app:contentScrim="@color/transparent" app:layout_scrollFlags="scroll|exitUntilCollapsed">
                <androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="wrap_content">
                    <FrameLayout android:id="@id/fl_download_tips" android:background="@color/bg_02" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="40.0dip" app:layout_constraintTop_toTopOf="parent">
                        <androidx.appcompat.widget.AppCompatImageView android:layout_gravity="center_vertical" android:layout_width="16.0dip" android:layout_height="16.0dip" android:src="@mipmap/ic_download_detail" android:tint="@color/text_02" android:layout_marginStart="12.0dip" />
                        <TextView android:textColor="@color/text_02" android:layout_gravity="center_vertical" android:id="@id/tv_download_tips" android:layout_width="fill_parent" android:layout_height="wrap_content" android:text="@string/download_playing_downloading_tips" android:layout_marginStart="40.0dip" android:layout_marginEnd="92.0dip" style="@style/style_regular_text" />
                        <TextView android:textColor="@color/main" android:gravity="center" android:layout_gravity="end|center" android:id="@id/tv_download_btn" android:background="@drawable/post_detail_shape_download_pause_bg" android:layout_width="wrap_content" android:layout_height="24.0dip" android:layout_marginTop="6.0dip" android:layout_marginBottom="6.0dip" android:text="@string/download_playing_pause" android:paddingStart="8.0dip" android:paddingEnd="8.0dip" android:layout_marginStart="16.0dip" android:layout_marginEnd="16.0dip" style="@style/style_medium_small_text" />
                    </FrameLayout>
                    <FrameLayout android:id="@id/adContainerP" android:layout_width="fill_parent" android:layout_height="wrap_content" app:layout_constraintTop_toBottomOf="@id/fl_download_tips">
                        <FrameLayout android:id="@id/adContainerTop" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="12.0dip" />
                        <FrameLayout android:id="@id/adContainerBottom" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="12.0dip" />
                    </FrameLayout>
                    <FrameLayout android:id="@id/subjectDetailLayout" android:layout_width="fill_parent" android:layout_height="wrap_content" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/adContainerP" />
                    <com.transsion.postdetail.ui.view.LocalVideoMiddleHeaderView android:id="@id/resourceHeaderView" android:layout_width="fill_parent" android:layout_height="wrap_content" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/subjectDetailLayout" />
                </androidx.constraintlayout.widget.ConstraintLayout>
            </com.google.android.material.appbar.CollapsingToolbarLayout>
            <LinearLayout android:orientation="horizontal" android:background="@color/bg_01" android:layout_width="fill_parent" android:layout_height="38.0dip">
                <net.lucode.hackware.magicindicator.MagicIndicator android:layout_gravity="bottom" android:id="@id/magic_indicator" android:layout_width="0.0dip" android:layout_height="38.0dip" android:layout_weight="1.0" />
                <com.transsion.publish.view.PublishStateView android:layout_gravity="center_vertical" android:id="@id/ivPublish" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginEnd="12.0dip" />
            </LinearLayout>
            <View android:background="@color/line_01" android:layout_width="fill_parent" android:layout_height="1.0dip" />
        </com.google.android.material.appbar.AppBarLayout>
        <com.transsion.baseui.widget.NestedScrollableHost android:layout_width="fill_parent" android:layout_height="fill_parent" app:layout_behavior="@string/appbar_scrolling_view_behavior">
            <androidx.viewpager2.widget.ViewPager2 android:id="@id/rec_view_pager" android:layout_width="fill_parent" android:layout_height="fill_parent" />
        </com.transsion.baseui.widget.NestedScrollableHost>
    </androidx.coordinatorlayout.widget.CoordinatorLayout>
    <FrameLayout android:id="@id/fl_series_list_container" android:layout_width="fill_parent" android:layout_height="0.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintTop_toBottomOf="@id/vd_land_bottom_controller" />
    <FrameLayout android:id="@id/fl_content_loading" android:background="@color/bg_01" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="0.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintTop_toBottomOf="@id/vd_land_bottom_controller">
        <ProgressBar android:layout_gravity="center" android:layout_width="23.0dip" android:layout_height="23.0dip" android:indeterminateTint="@color/brand" />
    </FrameLayout>
    <include android:id="@id/layout_sync_adjust" layout="@layout/layout_subtitle_sync_adjust" />
</androidx.constraintlayout.widget.ConstraintLayout>
