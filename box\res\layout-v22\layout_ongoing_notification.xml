<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:orientation="vertical" android:background="@mipmap/img_toolbar_notification_bg" android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <LinearLayout android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="12.0dip" android:layout_marginStart="16.0dip" android:layout_marginEnd="8.0dip">
        <ImageView android:layout_width="20.0dip" android:layout_height="20.0dip" android:src="@drawable/push_small_logo" />
        <TextView android:textSize="12.0sp" android:textColor="@color/white" android:gravity="start|center" android:layout_width="0.0dip" android:layout_height="fill_parent" android:text="@string/app_name" android:layout_weight="1.0" />
        <ImageView android:layout_gravity="end" android:id="@id/notice_v_setting" android:layout_width="28.0dip" android:layout_height="wrap_content" android:src="@mipmap/ic_notification_setting" android:scaleType="center" />
    </LinearLayout>
    <LinearLayout android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginLeft="16.0dip" android:layout_marginTop="8.0dip" android:layout_marginRight="16.0dip" android:layout_marginBottom="12.0dip" android:layout_marginHorizontal="16.0dip">
        <LinearLayout android:gravity="center" android:id="@id/notice_ll_left" android:background="@drawable/shape_toolbar_btn_bg" android:layout_width="0.0dip" android:layout_height="40.0dip" android:layout_weight="1.0" android:layout_marginEnd="8.0dip">
            <ImageView android:id="@id/notice_iv_left" android:layout_width="16.0dip" android:layout_height="16.0dip" android:src="@mipmap/ic_notification_top_10" />
            <TextView android:textSize="14.0sp" android:textColor="@color/white" android:gravity="center" android:layout_gravity="center" android:id="@id/notice_tv_left" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/tool_notice_top_10" android:layout_marginStart="2.0dip" />
        </LinearLayout>
        <LinearLayout android:gravity="center" android:id="@id/notice_ll_right" android:background="@drawable/shape_toolbar_btn_bg" android:layout_width="0.0dip" android:layout_height="40.0dip" android:layout_weight="1.0" android:layout_marginStart="8.0dip">
            <ImageView android:id="@id/notice_iv_right" android:layout_width="16.0dip" android:layout_height="16.0dip" android:src="@mipmap/ic_notification_search" />
            <TextView android:textSize="14.0sp" android:textColor="@color/white" android:gravity="center" android:layout_gravity="center" android:id="@id/notice_tv_right" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/tool_notice_search" android:layout_marginStart="2.0dip" />
        </LinearLayout>
    </LinearLayout>
</LinearLayout>
