<?xml version="1.0" encoding="utf-8"?>
<com.noober.background.view.BLLinearLayout android:layout_width="fill_parent" android:layout_height="46.0dip" app:bl_corners_radius="4.0dip" app:bl_solid_color="@color/module_05"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.constraintlayout.widget.ConstraintLayout android:layout_gravity="center_vertical" android:id="@id/cl_subject_content" android:layout_width="0.0dip" android:layout_height="fill_parent" android:layout_weight="1.0">
        <com.google.android.material.imageview.ShapeableImageView android:id="@id/iv_subject_cover" android:background="@color/gray_dark_20" android:layout_width="32.0dip" android:layout_height="32.0dip" android:scaleType="centerCrop" android:layout_marginStart="4.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:shapeAppearanceOverlay="@style/roundStyle_4" />
        <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/white_80" android:ellipsize="end" android:gravity="start" android:id="@id/tv_subject_name" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginLeft="4.0dip" android:layout_marginRight="4.0dip" android:maxLines="1" android:includeFontPadding="false" android:textAlignment="viewStart" android:layout_marginHorizontal="4.0dip" app:layout_constrainedWidth="true" app:layout_constraintBottom_toTopOf="@id/iv_subject_tag" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/iv_subject_cover" app:layout_constraintTop_toTopOf="parent" app:layout_constraintVertical_chainStyle="packed" style="@style/style_medium_text" />
        <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_subject_tag" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="2.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="@id/tv_subject_name" app:layout_constraintTop_toBottomOf="@id/tv_subject_name" app:srcCompat="@drawable/ic_tag_movie" />
        <View android:id="@id/v_subject_line_1" android:background="@color/white_20" android:layout_width="1.0dip" android:layout_height="8.0dip" android:layout_marginStart="4.0dip" app:layout_constraintBottom_toBottomOf="@id/iv_subject_tag" app:layout_constraintStart_toEndOf="@id/iv_subject_tag" app:layout_constraintTop_toTopOf="@id/iv_subject_tag" style="@style/style_import_text" />
        <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/white_60" android:ellipsize="end" android:id="@id/tv_subject_date" android:layout_width="0.0dip" android:layout_height="wrap_content" android:maxLines="1" android:includeFontPadding="false" android:layout_marginStart="4.0dip" app:layout_constrainedWidth="true" app:layout_constraintBottom_toBottomOf="@id/iv_subject_tag" app:layout_constraintStart_toEndOf="@id/v_subject_line_1" app:layout_constraintTop_toTopOf="@id/iv_subject_tag" style="@style/style_regula_bigger_text" />
        <View android:id="@id/v_subject_line_2" android:background="@color/white_20" android:layout_width="1.0dip" android:layout_height="8.0dip" android:layout_marginStart="4.0dip" app:layout_constraintBottom_toBottomOf="@id/iv_subject_tag" app:layout_constraintStart_toEndOf="@id/tv_subject_date" app:layout_constraintTop_toTopOf="@id/iv_subject_tag" style="@style/style_import_text" />
        <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/white_60" android:ellipsize="end" android:gravity="start" android:id="@id/tv_subject_genre" android:layout_width="0.0dip" android:layout_height="wrap_content" android:maxLines="1" android:includeFontPadding="false" android:textAlignment="viewStart" android:layout_marginStart="4.0dip" android:layout_marginEnd="4.0dip" app:layout_constrainedWidth="true" app:layout_constraintBottom_toBottomOf="@id/iv_subject_tag" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/v_subject_line_2" app:layout_constraintTop_toTopOf="@id/iv_subject_tag" style="@style/style_regula_bigger_text" />
    </androidx.constraintlayout.widget.ConstraintLayout>
    <View android:layout_gravity="center_vertical" android:id="@id/v_subject_room_line" android:background="@color/line_01" android:layout_width="1.0dip" android:layout_height="24.0dip" />
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/white_80" android:ellipsize="end" android:gravity="start|center" android:id="@id/tv_room_tag" android:layout_width="0.0dip" android:layout_height="fill_parent" android:maxLines="1" android:includeFontPadding="false" android:drawablePadding="4.0dip" android:layout_weight="1.0" android:textAlignment="viewStart" android:paddingStart="8.0dip" android:paddingEnd="8.0dip" app:drawableStartCompat="@mipmap/post_ic_room" style="@style/style_medium_text" />
</com.noober.background.view.BLLinearLayout>
