<?xml version="1.0" encoding="utf-8"?>
<FrameLayout android:id="@id/fl_video" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginLeft="3.0dip" android:layout_marginRight="3.0dip" android:layout_marginHorizontal="3.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.google.android.material.imageview.ShapeableImageView android:id="@id/iv_cover" android:background="@color/skeleton" android:layout_width="fill_parent" android:layout_height="fill_parent" android:scaleType="centerCrop" android:adjustViewBounds="true" app:shapeAppearanceOverlay="@style/roundStyle_4" />
    <com.noober.background.view.BLView android:id="@id/v_cover_stroke" android:layout_width="fill_parent" android:layout_height="fill_parent" app:bl_corners_radius="4.0dip" app:bl_stroke_color="@color/white_10" app:bl_stroke_width="1.0dip" />
    <androidx.appcompat.widget.AppCompatImageView android:layout_gravity="center" android:id="@id/iv_video_play" android:layout_width="48.0dip" android:layout_height="48.0dip" app:srcCompat="@mipmap/movie_detail_audio_pause" />
    <com.noober.background.view.BLTextView android:textSize="11.0sp" android:textColor="@color/white" android:layout_gravity="start|bottom" android:id="@id/tv_video_duration" android:paddingLeft="3.0dip" android:paddingTop="1.0dip" android:paddingRight="3.0dip" android:paddingBottom="1.0dip" android:layout_margin="6.0dip" android:includeFontPadding="false" android:paddingHorizontal="3.0dip" android:paddingVertical="1.0dip" app:bl_corners_radius="4.0dip" app:bl_solid_color="@color/black_70" style="@style/style_regular_text" />
</FrameLayout>
