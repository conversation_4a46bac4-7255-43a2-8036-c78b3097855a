<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:orientation="vertical" android:paddingLeft="12.0dip" android:paddingRight="12.0dip" android:layout_width="fill_parent" android:layout_height="fill_parent" android:paddingHorizontal="12.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <LinearLayout android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="12.0dip">
        <View android:background="@color/module_04" android:layout_width="36.0dip" android:layout_height="36.0dip" android:layout_marginTop="12.0dip" />
        <LinearLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginStart="12.0dip">
            <View android:background="@color/module_04" android:layout_width="240.0dip" android:layout_height="18.0dip" android:layout_marginTop="12.0dip" />
            <View android:background="@color/module_04" android:layout_width="120.0dip" android:layout_height="15.0dip" android:layout_marginTop="6.0dip" />
            <View android:background="@color/module_04" android:layout_width="fill_parent" android:layout_height="16.0dip" android:layout_marginTop="6.0dip" />
            <View android:background="@color/module_04" android:layout_width="fill_parent" android:layout_height="16.0dip" android:layout_marginTop="6.0dip" />
            <View android:background="@color/module_04" android:layout_width="150.0dip" android:layout_height="16.0dip" android:layout_marginTop="6.0dip" />
            <View android:background="@color/module_04" android:layout_width="fill_parent" android:layout_height="141.0dip" android:layout_marginTop="6.0dip" />
            <FrameLayout android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="10.0dip">
                <View android:background="@color/module_04" android:layout_width="20.0dip" android:layout_height="20.0dip" />
                <View android:layout_gravity="center_horizontal" android:background="@color/module_04" android:layout_width="20.0dip" android:layout_height="20.0dip" />
                <View android:layout_gravity="end" android:background="@color/module_04" android:layout_width="20.0dip" android:layout_height="20.0dip" />
            </FrameLayout>
        </LinearLayout>
    </LinearLayout>
    <View android:background="@color/module_04" android:layout_width="fill_parent" android:layout_height="1.0dip" android:layout_marginTop="16.0dip" />
    <LinearLayout android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="12.0dip">
        <View android:background="@color/module_04" android:layout_width="36.0dip" android:layout_height="36.0dip" android:layout_marginTop="12.0dip" />
        <LinearLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginStart="12.0dip">
            <View android:background="@color/module_04" android:layout_width="240.0dip" android:layout_height="18.0dip" android:layout_marginTop="12.0dip" />
            <View android:background="@color/module_04" android:layout_width="120.0dip" android:layout_height="15.0dip" android:layout_marginTop="6.0dip" />
            <View android:background="@color/module_04" android:layout_width="fill_parent" android:layout_height="16.0dip" android:layout_marginTop="6.0dip" />
            <View android:background="@color/module_04" android:layout_width="fill_parent" android:layout_height="16.0dip" android:layout_marginTop="6.0dip" />
            <View android:background="@color/module_04" android:layout_width="150.0dip" android:layout_height="16.0dip" android:layout_marginTop="6.0dip" />
            <View android:background="@color/module_04" android:layout_width="fill_parent" android:layout_height="141.0dip" android:layout_marginTop="6.0dip" />
            <FrameLayout android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="10.0dip">
                <View android:background="@color/module_04" android:layout_width="20.0dip" android:layout_height="20.0dip" />
                <View android:layout_gravity="center_horizontal" android:background="@color/module_04" android:layout_width="20.0dip" android:layout_height="20.0dip" />
                <View android:layout_gravity="end" android:background="@color/module_04" android:layout_width="20.0dip" android:layout_height="20.0dip" />
            </FrameLayout>
        </LinearLayout>
    </LinearLayout>
    <View android:background="@color/module_04" android:layout_width="fill_parent" android:layout_height="1.0dip" android:layout_marginTop="16.0dip" />
    <LinearLayout android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="12.0dip">
        <View android:background="@color/module_04" android:layout_width="36.0dip" android:layout_height="36.0dip" android:layout_marginTop="12.0dip" />
        <LinearLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginStart="12.0dip">
            <View android:background="@color/module_04" android:layout_width="240.0dip" android:layout_height="18.0dip" android:layout_marginTop="12.0dip" />
            <View android:background="@color/module_04" android:layout_width="120.0dip" android:layout_height="15.0dip" android:layout_marginTop="6.0dip" />
            <View android:background="@color/module_04" android:layout_width="fill_parent" android:layout_height="16.0dip" android:layout_marginTop="6.0dip" />
            <View android:background="@color/module_04" android:layout_width="fill_parent" android:layout_height="16.0dip" android:layout_marginTop="6.0dip" />
            <View android:background="@color/module_04" android:layout_width="150.0dip" android:layout_height="16.0dip" android:layout_marginTop="6.0dip" />
            <View android:background="@color/module_04" android:layout_width="fill_parent" android:layout_height="141.0dip" android:layout_marginTop="6.0dip" />
            <FrameLayout android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="10.0dip">
                <View android:background="@color/module_04" android:layout_width="20.0dip" android:layout_height="20.0dip" />
                <View android:layout_gravity="center_horizontal" android:background="@color/module_04" android:layout_width="20.0dip" android:layout_height="20.0dip" />
                <View android:layout_gravity="end" android:background="@color/module_04" android:layout_width="20.0dip" android:layout_height="20.0dip" />
            </FrameLayout>
        </LinearLayout>
    </LinearLayout>
    <View android:background="@color/module_04" android:layout_width="fill_parent" android:layout_height="1.0dip" android:layout_marginTop="16.0dip" />
</LinearLayout>
