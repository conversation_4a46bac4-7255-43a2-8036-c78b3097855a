<?xml version="1.0" encoding="utf-8"?>
<merge android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <FrameLayout android:id="@id/fl_container" android:layout_width="0.0dip" android:layout_height="0.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_cover" android:background="@color/black" android:layout_width="0.0dip" android:layout_height="0.0dip" android:scaleType="fitCenter" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <View android:id="@id/up_background" android:background="@drawable/post_detail_gradient_up_50" android:layout_width="fill_parent" android:layout_height="100.0dip" app:layout_constraintTop_toTopOf="parent" />
    <View android:id="@id/bottom_background" android:background="@drawable/post_detail_gradient_bottom_50" android:layout_width="fill_parent" android:layout_height="240.0dip" app:layout_constraintBottom_toBottomOf="parent" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_play_status" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:src="@mipmap/post_detail_short_tv_pause" android:layout_marginStart="16.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <Space android:id="@id/progress_guideline" android:layout_width="0.0dip" android:layout_height="1.5dip" android:layout_marginBottom="16.0dip" android:progressDrawable="@drawable/post_detail_imm_video_progress" android:layout_marginStart="16.0dip" android:layout_marginEnd="16.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" />
    <ProgressBar android:id="@id/progress_bar" android:layout_width="0.0dip" android:layout_height="2.0dip" android:progressDrawable="@drawable/post_detail_imm_video_progress" app:layout_constraintBottom_toBottomOf="@id/progress_guideline" app:layout_constraintEnd_toEndOf="@id/progress_guideline" app:layout_constraintStart_toStartOf="@id/progress_guideline" style="@android:style/Widget.ProgressBar.Horizontal" />
    <androidx.appcompat.widget.AppCompatSeekBar android:layout_gravity="center_vertical" android:id="@id/seek_bar" android:background="@color/transparent" android:focusable="false" android:visibility="gone" android:clickable="false" android:layout_width="0.0dip" android:layout_height="24.0dip" android:maxHeight="4.0dip" android:progress="0" android:progressDrawable="@drawable/post_detail_layer_seekbar" android:minHeight="2.0dip" android:thumb="@drawable/post_detail_shape_seekbar_bar" android:thumbOffset="0.0dip" android:layout_weight="1.0" android:splitTrack="false" app:layout_constraintBottom_toBottomOf="@id/progress_guideline" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="@id/progress_guideline" />
    <com.transsion.postdetail.ui.view.VideoProgressDragGestureView android:id="@id/v_progress_gesture" android:layout_width="0.0dip" android:layout_height="0.0dip" android:layout_marginTop="-20.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="@id/progress_bar" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="14.0sp" android:textColor="@color/white" android:gravity="center" android:id="@id/tv_progress_des" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginBottom="12.0dip" android:minWidth="150.0dip" app:layout_constraintBottom_toTopOf="@id/progress_guideline" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_share" android:layout_width="40.0dip" android:layout_height="40.0dip" android:layout_marginBottom="130.0dip" android:scaleType="center" android:layout_marginEnd="12.0dip" app:layout_constraintBottom_toTopOf="@id/progress_guideline" app:layout_constraintEnd_toEndOf="parent" app:srcCompat="@mipmap/post_detail_ic_video_share" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="11.0sp" android:textColor="@color/white" android:gravity="center_horizontal" android:id="@id/tv_favorite" android:layout_width="40.0dip" android:layout_height="wrap_content" android:layout_marginBottom="16.0dip" android:shadowColor="@color/base_color_80000000" android:shadowRadius="3.0" app:drawableTopCompat="@drawable/post_detail_selector_short_tv_favorite_big" app:layout_constraintBottom_toTopOf="@id/iv_share" app:layout_constraintEnd_toEndOf="@id/iv_share" app:layout_constraintStart_toStartOf="@id/iv_share" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_download" android:layout_width="40.0dip" android:layout_height="40.0dip" android:layout_marginBottom="20.0dip" app:layout_constraintBottom_toTopOf="@id/tv_favorite" app:layout_constraintEnd_toEndOf="@id/iv_share" app:layout_constraintStart_toStartOf="@id/iv_share" app:srcCompat="@mipmap/ic_short_tv_download" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_download_icon" android:visibility="gone" android:layout_width="16.0dip" android:layout_height="16.0dip" android:layout_marginBottom="4.0dip" android:layout_marginEnd="2.0dip" app:layout_constraintBottom_toBottomOf="@id/iv_download" app:layout_constraintEnd_toEndOf="@id/iv_download" app:srcCompat="@mipmap/ic_short_tv_downloading" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="14.0sp" android:textColor="@color/white" android:ellipsize="end" android:gravity="start|center" android:layout_gravity="start|center" android:id="@id/tv_episode" android:background="@drawable/short_tv_bg_radius_8_color_white" android:paddingLeft="8.0dip" android:paddingRight="8.0dip" android:layout_width="fill_parent" android:layout_height="32.0dip" android:layout_marginBottom="24.0dip" android:singleLine="true" android:drawablePadding="4.0dip" android:drawableStart="@mipmap/post_detail_ic_video_short_tv_list" android:drawableEnd="@mipmap/ic_arrow_up_white" android:layout_marginStart="16.0dip" android:layout_marginEnd="64.0dip" android:paddingHorizontal="8.0dip" app:layout_constraintBottom_toTopOf="@id/progress_guideline" app:layout_constraintStart_toStartOf="parent" style="@style/style_medium_text" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textColor="@color/white" android:ellipsize="end" android:gravity="start|center" android:id="@id/tv_name" android:visibility="gone" android:layout_width="0.0dip" android:layout_height="wrap_content" android:minHeight="44.0dip" android:maxLines="2" android:textAlignment="viewStart" android:layout_marginStart="8.0dip" android:layout_marginEnd="14.0dip" app:layout_constraintBottom_toBottomOf="@id/iv_short_cover" app:layout_constraintEnd_toStartOf="@id/iv_share" app:layout_constraintStart_toEndOf="@id/iv_short_cover" app:layout_constraintTop_toTopOf="@id/iv_short_cover" style="@style/style_import_text" />
    <com.google.android.material.imageview.ShapeableImageView android:id="@id/iv_short_cover" android:padding="0.5dip" android:visibility="visible" android:layout_width="30.0dip" android:layout_height="40.0dip" android:layout_marginBottom="8.0dip" android:scaleType="centerCrop" app:layout_constraintBottom_toTopOf="@id/ns_post_des" app:layout_constraintEnd_toStartOf="@id/tv_title" app:layout_constraintStart_toStartOf="@id/progress_guideline" app:shapeAppearance="@style/roundStyle_2" app:strokeColor="@color/white_10" app:strokeWidth="1.0dip" />
    <com.transsion.baseui.widget.NestedScrollableHost android:id="@id/ns_post_des" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginBottom="12.0dip" app:layout_constrainedHeight="true" app:layout_constraintBottom_toTopOf="@id/tv_episode" app:layout_constraintEnd_toEndOf="@id/tv_episode" app:layout_constraintHeight_max="108.0dip" app:layout_constraintStart_toStartOf="@id/tv_episode">
        <com.transsion.postdetail.ui.view.ImmNestedScrollView android:scrollbarThumbVertical="@drawable/post_detail_imm_video_scrollbar_thumb" android:scrollbars="vertical" android:layout_width="fill_parent" android:layout_height="wrap_content" android:fadeScrollbars="false">
            <FrameLayout android:layout_width="fill_parent" android:layout_height="fill_parent">
                <com.tn.lib.view.expand.ExpandView android:textSize="14.0sp" android:textColor="@color/white" android:id="@id/ev_post_des" android:layout_width="fill_parent" android:layout_height="wrap_content" app:layout_goneMarginBottom="0.0dip" app:pop_expand_hint_color="@color/white" app:pop_hint_text_size="10.0sp" app:pop_reverse_Lines="2" app:pop_shrink_hint_color="@color/white" />
            </FrameLayout>
        </com.transsion.postdetail.ui.view.ImmNestedScrollView>
    </com.transsion.baseui.widget.NestedScrollableHost>
    <androidx.constraintlayout.widget.ConstraintLayout android:id="@id/tool_bar" android:fitsSystemWindows="true" android:layout_width="fill_parent" android:layout_height="wrap_content" app:layout_constraintTop_toTopOf="parent">
        <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_back" android:padding="12.0dip" android:layout_width="48.0dip" android:layout_height="48.0dip" android:src="@mipmap/icon_white_back" android:scaleType="center" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
        <com.tn.lib.widget.TnTextView android:textColor="@color/common_white" android:ellipsize="end" android:gravity="start" android:id="@id/tv_title" android:layout_width="0.0dip" android:layout_height="wrap_content" android:singleLine="true" android:layout_marginEnd="16.0dip" app:layout_constraintBottom_toBottomOf="@id/iv_back" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/iv_back" app:layout_constraintTop_toTopOf="@id/iv_back" style="@style/style_title_text" />
    </androidx.constraintlayout.widget.ConstraintLayout>
    <androidx.constraintlayout.widget.Group android:id="@id/group_content" android:layout_width="wrap_content" android:layout_height="wrap_content" app:constraint_referenced_ids="iv_share,iv_download,         tv_episode,tv_favorite,tool_bar,tv_name,         iv_short_cover,ns_post_des" />
</merge>
