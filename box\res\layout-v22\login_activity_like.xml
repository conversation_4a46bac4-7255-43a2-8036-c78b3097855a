<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <View android:id="@id/view_blank" android:layout_width="fill_parent" android:layout_height="0.0dip" android:layout_weight="1.0" />
    <LinearLayout android:orientation="vertical" android:id="@id/rl_login" android:background="@drawable/login_shape_like_activity" android:layout_width="fill_parent" android:layout_height="wrap_content">
        <androidx.appcompat.widget.AppCompatImageView android:layout_gravity="end|top" android:id="@id/ll_close" android:paddingTop="12.0dip" android:layout_width="wrap_content" android:layout_height="wrap_content" android:src="@mipmap/ic_close_black" android:paddingStart="12.0dip" android:paddingEnd="12.0dip" />
        <androidx.appcompat.widget.AppCompatTextView android:textSize="20.0sp" android:textColor="@color/text_01" android:layout_gravity="center_horizontal" android:id="@id/tv_title" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/login_sign_up_for_app" style="@style/style_medium_text" />
        <FrameLayout android:id="@id/btn_email_login" android:background="@drawable/liui_main_gradient_bg_8dp" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="32.0dip" android:layout_marginStart="24.0dip" android:layout_marginEnd="24.0dip">
            <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textColor="@color/white" android:gravity="center" android:layout_gravity="center" android:layout_width="wrap_content" android:layout_height="48.0dip" android:text="@string/login_continue_with_email" android:drawablePadding="8.0dip" android:textAllCaps="false" android:drawableStart="@mipmap/login_email_icon" style="@style/style_medium_text" />
        </FrameLayout>
        <FrameLayout android:id="@id/btn_gp_login" android:background="@drawable/login_shape_google_btn_bg" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="16.0dip" android:layout_marginStart="24.0dip" android:layout_marginEnd="24.0dip">
            <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textColor="#ff191f2b" android:gravity="center" android:layout_gravity="center" android:layout_width="wrap_content" android:layout_height="48.0dip" android:text="@string/login_continue_with_gp" android:drawablePadding="8.0dip" android:textAllCaps="false" android:drawableStart="@mipmap/login_google_icon" style="@style/style_medium_text" />
        </FrameLayout>
        <LinearLayout android:gravity="center" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="24.0dip">
            <View android:background="@color/login_color_line" android:layout_width="0.0dip" android:layout_height="1.0dip" android:layout_weight="1.0" android:layout_marginStart="24.0dip" android:layout_marginEnd="8.0dip" />
            <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/login_color_policy" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/login_or" style="@style/style_medium_text" />
            <View android:background="@color/login_color_line" android:layout_width="0.0dip" android:layout_height="1.0dip" android:layout_weight="1.0" android:layout_marginStart="8.0dip" android:layout_marginEnd="24.0dip" />
        </LinearLayout>
        <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textColor="@color/login_color_policy_link" android:gravity="center" android:id="@id/tv_log_in" android:background="@color/login_color_line" android:layout_width="fill_parent" android:layout_height="48.0dip" android:layout_marginLeft="24.0dip" android:layout_marginTop="16.0dip" android:layout_marginRight="24.0dip" android:text="@string/login_log_in" android:textAllCaps="false" android:layout_marginHorizontal="24.0dip" style="@style/style_medium_text" />
        <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/login_color_policy" android:gravity="center" android:layout_gravity="center_horizontal" android:id="@id/tv_privacy" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="24.0dip" android:layout_marginBottom="24.0dip" android:text="@string/login_sign_up_privacy" android:lineSpacingExtra="5.0dip" android:layout_marginStart="35.0dip" android:layout_marginEnd="35.0dip" />
    </LinearLayout>
</LinearLayout>
