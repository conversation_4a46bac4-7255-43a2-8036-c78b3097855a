<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:orientation="vertical" android:background="@color/bg_01" android:fitsSystemWindows="true" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/btn_back" android:layout_width="wrap_content" android:layout_height="48.0dip" android:contentDescription="@null" android:paddingStart="16.0dip" android:paddingEnd="8.0dip" app:srcCompat="@mipmap/libui_ic_back_black" app:tint="@color/text_01" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="24.0sp" android:textColor="@color/text_01" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginLeft="32.0dip" android:layout_marginTop="24.0dip" android:layout_marginRight="32.0dip" android:text="@string/login_enter_phone_number" android:layout_marginHorizontal="32.0dip" style="@style/style_medium_small_text" />
    <LinearLayout android:gravity="center_vertical" android:orientation="horizontal" android:id="@id/ll_input" android:layout_width="fill_parent" android:layout_height="45.0dip" android:layout_marginLeft="32.0dip" android:layout_marginTop="24.0dip" android:layout_marginRight="32.0dip" android:layout_marginHorizontal="32.0dip">
        <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textColor="@color/text_01" android:textColorHint="@color/login_color_et_hint" android:gravity="center" android:id="@id/tv_phone_country_code" android:background="@null" android:layout_width="wrap_content" android:layout_height="fill_parent" android:hint="@string/login_select_country_code" android:singleLine="true" android:inputType="number" android:textDirection="ltr" style="@style/style_regular_text" />
        <androidx.appcompat.widget.AppCompatImageView android:layout_gravity="center_vertical" android:layout_width="wrap_content" android:layout_height="wrap_content" android:src="@mipmap/login_arrow_down" android:tint="@color/text_01" android:layout_marginStart="7.0dip" android:layout_marginEnd="7.0dip" />
        <View android:layout_gravity="center_vertical" android:background="@color/line_01" android:layout_width="1.0dip" android:layout_height="23.0dip" />
        <androidx.appcompat.widget.AppCompatEditText android:textSize="16.0sp" android:textColor="@color/text_01" android:textColorHint="@color/login_color_et_hint" android:gravity="start|center" android:id="@id/et_phone" android:background="@null" android:focusable="true" android:layout_width="0.0dip" android:layout_height="fill_parent" android:hint="@string/login_your_phone_number" android:singleLine="true" android:maxLength="18" android:digits="\ 0123456789" android:layout_weight="1.0" android:inputType="phone" android:textDirection="locale" android:textAlignment="viewStart" android:paddingStart="8.0dip" android:paddingEnd="20.0dip" style="@style/LoginEditTextTheme" />
        <androidx.appcompat.widget.AppCompatImageButton android:layout_gravity="end|center" android:id="@id/btn_clear" android:background="@null" android:paddingTop="15.0dip" android:paddingBottom="15.0dip" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:src="@mipmap/login_clear" android:paddingStart="10.0dip" android:paddingEnd="0.0dip" app:layout_constraintBottom_toBottomOf="@id/tv_phone_country_code" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="@id/tv_phone_country_code" />
    </LinearLayout>
    <View android:layout_gravity="bottom" android:background="@color/line_01" android:layout_width="fill_parent" android:layout_height="1.0dip" android:layout_marginLeft="32.0dip" android:layout_marginRight="32.0dip" android:layout_marginHorizontal="32.0dip" app:layout_constraintTop_toBottomOf="@id/ll_input" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="13.0sp" android:textColor="@color/error_50" android:id="@id/tv_tips" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginLeft="32.0dip" android:layout_marginTop="12.0dip" android:layout_marginRight="32.0dip" android:layout_marginHorizontal="32.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/ll_input" style="@style/style_regular_text" />
    <androidx.appcompat.widget.AppCompatButton android:textColor="@color/base_color_white" android:gravity="center" android:layout_gravity="end" android:id="@id/btn_login" android:background="@drawable/login_selector_login_btn" android:layout_width="fill_parent" android:layout_height="40.0dip" android:layout_marginLeft="32.0dip" android:layout_marginTop="16.0dip" android:layout_marginRight="32.0dip" android:text="@string/login_next" android:textAllCaps="false" android:layout_marginHorizontal="32.0dip" app:layout_constraintBottom_toBottomOf="parent" />
    <androidx.appcompat.widget.AppCompatButton android:textColor="@color/login_color_sec_text" android:gravity="center" android:layout_gravity="end" android:id="@id/btn_email" android:background="@color/login_color_sec_bg" android:layout_width="fill_parent" android:layout_height="40.0dip" android:layout_marginLeft="32.0dip" android:layout_marginTop="16.0dip" android:layout_marginRight="32.0dip" android:text="@string/login_use_email_continue" android:textAllCaps="false" android:layout_marginHorizontal="32.0dip" app:layout_constraintBottom_toBottomOf="parent" style="@style/style_medium_text" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/login_color_policy" android:gravity="center" android:layout_gravity="center_horizontal" android:id="@id/tv_privacy" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="16.0dip" android:text="@string/login_sign_up_privacy" android:lineSpacingExtra="5.0dip" android:layout_marginStart="35.0dip" android:layout_marginEnd="35.0dip" />
</LinearLayout>
