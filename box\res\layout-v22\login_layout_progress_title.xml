<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/btn_back" android:layout_width="wrap_content" android:layout_height="48.0dip" android:contentDescription="@null" android:paddingStart="16.0dip" android:paddingEnd="8.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:srcCompat="@mipmap/libui_ic_back_black" app:tint="@color/text_01" />
    <com.transsnet.login.widget.LoginProgressBar android:id="@id/progress" android:layout_width="fill_parent" android:layout_height="8.0dip" android:layout_marginLeft="60.0dip" android:layout_marginRight="60.0dip" android:max="900" android:progress="0" android:progressDrawable="@drawable/login_progress" android:layout_marginHorizontal="60.0dip" app:layout_constraintBottom_toBottomOf="@id/btn_back" app:layout_constraintTop_toTopOf="@id/btn_back" style="@style/Widget.AppCompat.ProgressBar.Horizontal" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/login_color_progress_tv" android:id="@id/tv_progress" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="1/3" android:layout_marginEnd="24.0dip" app:layout_constraintBottom_toBottomOf="@id/btn_back" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="@id/btn_back" style="@style/style_medium_text" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textColor="@color/login_color_progress_tv" android:id="@id/tv_title" android:layout_width="wrap_content" android:layout_height="wrap_content" app:layout_constraintBottom_toBottomOf="@id/btn_back" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="@id/btn_back" style="@style/style_medium_text" />
</androidx.constraintlayout.widget.ConstraintLayout>
