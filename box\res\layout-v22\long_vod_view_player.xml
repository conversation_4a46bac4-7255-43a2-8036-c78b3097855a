<?xml version="1.0" encoding="utf-8"?>
<merge android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.constraintlayout.widget.ConstraintLayout android:id="@id/clPlayer" android:layout_width="0.0dip" android:layout_height="fill_parent" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toStartOf="@id/flLandAdGroup" app:layout_constraintHorizontal_chainStyle="spread" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:layout_constraintVertical_bias="0.0">
        <ImageView android:id="@id/v_top_space" android:layout_width="0.0dip" android:layout_height="wrap_content" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
        <View android:id="@id/guideline" android:layout_width="0.0dip" android:layout_height="0.0dip" app:layout_constraintDimensionRatio="h,16:9" app:layout_constraintLeft_toLeftOf="parent" app:layout_constraintRight_toRightOf="parent" app:layout_constraintTop_toBottomOf="@id/v_top_space" />
        <com.transsion.player.ui.longvideo.ORLongVodPlayerView android:id="@id/or_long_vod_view" android:layout_width="0.0dip" android:layout_height="0.0dip" app:layout_constraintBottom_toBottomOf="@id/guideline" app:layout_constraintEnd_toEndOf="@id/guideline" app:layout_constraintStart_toStartOf="@id/guideline" app:layout_constraintTop_toTopOf="@id/guideline" />
        <androidx.appcompat.widget.AppCompatImageView android:id="@id/or_long_vod_iv_bg" android:visibility="gone" android:layout_width="0.0dip" android:layout_height="0.0dip" android:scaleType="centerCrop" app:layout_constraintBottom_toBottomOf="@id/guideline" app:layout_constraintEnd_toEndOf="@id/guideline" app:layout_constraintStart_toStartOf="@id/guideline" app:layout_constraintTop_toTopOf="@id/guideline" />
        <androidx.appcompat.widget.AppCompatTextView android:textSize="18.0sp" android:textColor="@color/white" android:id="@id/tv_center_progress" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:includeFontPadding="false" app:layout_constraintBottom_toBottomOf="@id/guideline" app:layout_constraintEnd_toEndOf="@id/guideline" app:layout_constraintStart_toStartOf="@id/guideline" app:layout_constraintTop_toTopOf="@id/guideline" />
        <FrameLayout android:id="@id/flRootSubtitle" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginLeft="12.0dip" android:layout_marginRight="12.0dip" android:layout_marginBottom="24.0dip" android:layout_marginHorizontal="12.0dip" app:layout_constraintBottom_toBottomOf="@id/guideline" app:layout_constraintEnd_toEndOf="@id/guideline" app:layout_constraintStart_toStartOf="@id/guideline">
            <androidx.appcompat.widget.LinearLayoutCompat android:orientation="vertical" android:id="@id/flSubtitle" android:layout_width="fill_parent" android:layout_height="wrap_content">
                <com.avery.subtitle.widget.SimpleSubtitleView android:textSize="16.0sp" android:textColor="@color/white" android:ellipsize="end" android:gravity="center" android:layout_gravity="center" android:id="@id/vSubtitleTop" android:paddingLeft="6.0dip" android:paddingTop="3.0dip" android:paddingRight="6.0dip" android:paddingBottom="3.0dip" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:shadowColor="@color/black_90" android:shadowDx="2.0" android:shadowDy="1.0" android:shadowRadius="2.0" android:lineSpacingExtra="1.0dip" android:layout_marginStart="24.0dip" android:layout_marginEnd="24.0dip" android:paddingHorizontal="6.0dip" android:paddingVertical="3.0dip" />
                <com.avery.subtitle.widget.SimpleSubtitleView android:textSize="16.0sp" android:textColor="@color/white" android:ellipsize="end" android:gravity="center" android:layout_gravity="center" android:id="@id/vSubtitleBottom" android:paddingLeft="6.0dip" android:paddingTop="3.0dip" android:paddingRight="6.0dip" android:paddingBottom="3.0dip" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="2.0dip" android:shadowColor="@color/black_90" android:shadowDx="2.0" android:shadowDy="1.0" android:shadowRadius="2.0" android:lineSpacingExtra="1.0dip" android:layout_marginStart="24.0dip" android:layout_marginEnd="24.0dip" android:paddingHorizontal="6.0dip" android:paddingVertical="3.0dip" />
            </androidx.appcompat.widget.LinearLayoutCompat>
        </FrameLayout>
        <include android:id="@id/layout_middle" layout="@layout/long_vod_view_player_middle" />
        <include android:id="@id/layout_land" layout="@layout/long_vod_view_player_land" />
        <include android:id="@id/layout_top_tool_bar" layout="@layout/long_vod_layout_top_tool" />
        <androidx.appcompat.widget.AppCompatTextView android:textSize="@dimen/sp_14" android:textColor="@color/white" android:gravity="center" android:id="@id/tvPressSpeed" android:background="@drawable/bg_radius4_black60" android:paddingLeft="@dimen/dp_12" android:paddingRight="@dimen/dp_12" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="@dimen/dimens_24" android:layout_marginTop="32.0dip" android:text="2x" android:includeFontPadding="false" android:drawablePadding="@dimen/dp_4" android:drawableEnd="@drawable/ic_quick_speed" android:paddingHorizontal="@dimen/dp_12" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
        <ViewStub android:id="@id/vs_replay" android:layout="@layout/long_vod_layout_replay" android:layout_width="0.0dip" android:layout_height="0.0dip" app:layout_constraintBottom_toBottomOf="@id/guideline" app:layout_constraintEnd_toEndOf="@id/guideline" app:layout_constraintStart_toStartOf="@id/guideline" app:layout_constraintTop_toTopOf="@id/guideline" />
        <ViewStub android:id="@id/vs_load_fail" android:layout="@layout/long_vod_layout_fail" android:layout_width="0.0dip" android:layout_height="0.0dip" app:layout_constraintBottom_toBottomOf="@id/guideline" app:layout_constraintEnd_toEndOf="@id/guideline" app:layout_constraintStart_toStartOf="@id/guideline" app:layout_constraintTop_toTopOf="@id/guideline" />
        <ViewStub android:id="@id/vs_toast" android:layout="@layout/long_vod_layout_toast" android:layout_width="wrap_content" android:layout_height="wrap_content" app:layout_constraintBottom_toBottomOf="@id/guideline" app:layout_constraintStart_toStartOf="@id/guideline" />
        <FrameLayout android:layout_gravity="center" android:id="@id/flPauseAdGroup" android:visibility="gone" android:layout_width="0.0dip" android:layout_height="0.0dip" android:layout_marginLeft="60.0dip" android:layout_marginTop="30.0dip" android:layout_marginRight="60.0dip" android:layout_marginBottom="30.0dip" android:layout_marginHorizontal="60.0dip" android:layout_marginVertical="30.0dip" app:layout_constraintBottom_toBottomOf="@id/guideline" app:layout_constraintEnd_toEndOf="@id/guideline" app:layout_constraintStart_toStartOf="@id/guideline" app:layout_constraintTop_toTopOf="@id/guideline">
            <FrameLayout android:layout_gravity="center" android:id="@id/adContainer" android:layout_width="fill_parent" android:layout_height="fill_parent" android:minHeight="100.0dip" />
            <ImageView android:layout_gravity="end|top" android:id="@id/ivAdPauseClose" android:layout_width="wrap_content" android:layout_height="wrap_content" android:src="@mipmap/ad_close" />
        </FrameLayout>
        <ViewStub android:id="@id/vs_mobile_data" android:layout="@layout/long_vod_layout_mobile_data" android:layout_width="0.0dip" android:layout_height="0.0dip" app:layout_constraintBottom_toBottomOf="@id/guideline" app:layout_constraintEnd_toEndOf="@id/guideline" app:layout_constraintStart_toStartOf="@id/guideline" app:layout_constraintTop_toTopOf="@id/guideline" />
    </androidx.constraintlayout.widget.ConstraintLayout>
    <FrameLayout android:id="@id/flLandAdGroup" android:background="@color/module_01" android:visibility="gone" android:layout_width="100.0dip" android:layout_height="fill_parent" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/clPlayer" app:layout_constraintTop_toTopOf="parent">
        <FrameLayout android:id="@id/flLandAd" android:layout_width="fill_parent" android:layout_height="fill_parent" android:layout_margin="15.0dip" />
        <com.transsion.postdetail.ui.view.AdCountDownView android:layout_gravity="end|top" android:id="@id/adCountDownView" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="26.0dip" android:layout_marginEnd="16.0dip" />
        <androidx.appcompat.widget.AppCompatImageView android:id="@id/adCloseView" android:visibility="visible" android:layout_width="wrap_content" android:layout_height="wrap_content" app:layout_constraintStart_toStartOf="@id/fl_native_ad" app:layout_constraintTop_toTopOf="@id/fl_native_ad" app:srcCompat="@mipmap/ad_close" />
    </FrameLayout>
</merge>
