<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="fill_parent" app:layout_constraintBottom_toBottomOf="@id/guideline" app:layout_constraintEnd_toEndOf="@id/guideline" app:layout_constraintStart_toStartOf="@id/guideline" app:layout_constraintTop_toTopOf="@id/guideline"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <View android:id="@id/v_land_space_start" android:layout_width="0.0dip" android:layout_height="0.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <View android:id="@id/v_land_space_end" android:layout_width="0.0dip" android:layout_height="0.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <ViewStub android:id="@id/vs_forward" android:layout="@layout/long_vod_land_double_click" android:layout_width="fill_parent" android:layout_height="fill_parent" />
    <com.google.android.material.imageview.ShapeableImageView android:id="@id/iv_brand_ad_logo" android:layout_width="96.0dip" android:layout_height="32.0dip" android:layout_marginTop="16.0dip" android:alpha="0.8" android:layout_marginStart="16.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:shapeAppearanceOverlay="@style/roundStyle_2" />
    <FrameLayout android:id="@id/fl_native_ad" android:background="@color/white" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent">
        <FrameLayout android:layout_gravity="center" android:id="@id/v_native_ad" android:layout_width="fill_parent" android:layout_height="fill_parent" />
        <TextView android:textSize="10.0sp" android:textColor="@color/white" android:gravity="center" android:id="@id/v_native_ad_countdown" android:background="@drawable/ad_shape_dp_2_black_trans_50" android:paddingTop="2.0dip" android:paddingBottom="2.0dip" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="10.0dip" android:minWidth="26.0dip" android:layout_marginStart="12.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    </FrameLayout>
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/ad_close_view" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="-12.0dip" android:layout_marginStart="-12.0dip" app:layout_constraintStart_toStartOf="@id/fl_native_ad" app:layout_constraintTop_toTopOf="@id/fl_native_ad" app:srcCompat="@mipmap/ad_close" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_play" android:visibility="gone" android:layout_width="70.0dip" android:layout_height="70.0dip" android:src="@mipmap/post_ic_video_pause" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <FrameLayout android:id="@id/game_container" android:visibility="gone" android:layout_width="280.0dip" android:layout_height="64.0dip" android:layout_marginBottom="80.0dip" android:layout_marginStart="12.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toEndOf="@id/v_land_space_start" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_game_close" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="-12.0dip" android:layout_marginEnd="-12.0dip" app:layout_constraintEnd_toEndOf="@id/game_container" app:layout_constraintTop_toTopOf="@id/game_container" app:srcCompat="@mipmap/ic_trans_full_close" />
    <View android:id="@id/land_gradient_top" android:background="@drawable/long_vod_shape_player_top_bg" android:layout_width="fill_parent" android:layout_height="96.0dip" app:layout_constraintTop_toTopOf="parent" />
    <View android:id="@id/land_gradient_bottom" android:background="@drawable/long_vod_shape_player_bottom_bg" android:layout_width="fill_parent" android:layout_height="96.0dip" app:layout_constraintBottom_toBottomOf="parent" />
    <androidx.constraintlayout.widget.ConstraintLayout android:id="@id/cl_bottom_control" android:layout_width="0.0dip" android:layout_height="wrap_content" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toStartOf="@id/v_land_space_end" app:layout_constraintStart_toEndOf="@id/v_land_space_start">
        <TextView android:textSize="11.0sp" android:textColor="@color/white" android:id="@id/tv_land_cur_time" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginBottom="16.0dip" android:shadowColor="@color/black_50" android:shadowDy="2.0" android:shadowRadius="2.0" android:layout_marginStart="12.0dip" app:layout_constraintBottom_toTopOf="@id/iv_land_pause" app:layout_constraintStart_toStartOf="parent" />
        <com.tn.lib.view.SecondariesSeekBar android:id="@id/seek_bar_land" android:background="@color/transparent" android:layout_width="0.0dip" android:layout_height="24.0dip" android:layout_weight="1.0" android:layout_marginStart="4.0dip" app:layout_constraintBottom_toBottomOf="@id/tv_land_cur_time" app:layout_constraintEnd_toStartOf="@id/tv_land_total_time" app:layout_constraintStart_toEndOf="@id/tv_land_cur_time" app:layout_constraintTop_toTopOf="@id/tv_land_cur_time" app:ssb_bar_center_color="@color/brand_new_gradient_center" app:ssb_bar_end_color="@color/brand_new_gradient_center" app:ssb_bar_start_color="@color/brand_new_gradient_start" app:ssb_bg_color="@color/white_30" app:ssb_secondaries_color="@color/white" app:ssb_thumb_color="@color/white" app:ssb_thumb_size="8.0dip" />
        <TextView android:textSize="11.0sp" android:textColor="@color/white" android:gravity="end" android:id="@id/tv_land_total_time" android:layout_width="wrap_content" android:layout_height="wrap_content" android:shadowColor="@color/black_50" android:shadowDy="2.0" android:shadowRadius="2.0" android:layout_marginEnd="12.0dip" app:layout_constraintBottom_toBottomOf="@id/tv_land_cur_time" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="@id/tv_land_cur_time" />
        <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_land_pause" android:layout_width="32.0dip" android:layout_height="32.0dip" android:layout_marginBottom="16.0dip" android:src="@mipmap/icon_player_pause" android:scaleType="center" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" />
        <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/white" android:ellipsize="end" android:gravity="center_vertical" android:id="@id/tvPlayNext" android:visibility="gone" android:layout_width="32.0dip" android:layout_height="32.0dip" android:maxWidth="94.0dip" android:maxLines="1" android:drawablePadding="6.0dip" android:drawableStart="@drawable/ic_play_next" android:layout_marginStart="@dimen/dp_8" app:layout_constraintBottom_toBottomOf="@id/iv_land_pause" app:layout_constraintStart_toEndOf="@id/iv_land_pause" app:layout_constraintTop_toTopOf="@id/iv_land_pause" style="@style/style_medium_text" />
        <TextView android:textSize="14.0sp" android:textColor="@color/white" android:gravity="center" android:id="@id/tv_land_bitrate" android:layout_width="wrap_content" android:layout_height="24.0dip" app:layout_constraintBottom_toBottomOf="@id/tv_play_speed" app:layout_constraintEnd_toEndOf="@id/tv_land_total_time" app:layout_constraintTop_toTopOf="@id/tv_play_speed" style="@style/style_medium_text" />
        <com.transsion.postdetail.ui.view.ImmSpeedView android:textSize="12.0sp" android:textColor="@color/white" android:gravity="start|center" android:id="@id/tv_play_speed" android:layout_width="wrap_content" android:layout_height="24.0dip" android:drawablePadding="@dimen/dp_4" android:drawableStart="@drawable/ic_player_speed" android:layout_marginEnd="20.0dip" app:layout_constraintBottom_toBottomOf="@id/iv_land_pause" app:layout_constraintEnd_toStartOf="@id/tv_land_bitrate" app:layout_constraintTop_toTopOf="@id/iv_land_pause" style="@style/style_medium_text" />
        <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/white" android:ellipsize="end" android:gravity="center_vertical" android:id="@id/tv_language" android:layout_width="wrap_content" android:layout_height="24.0dip" android:maxWidth="108.0dip" android:text="@string/language" android:maxLines="1" android:includeFontPadding="false" android:drawablePadding="@dimen/dp_4" android:drawableStart="@drawable/ic_player_language" android:layout_marginEnd="20.0dip" app:layout_constraintBottom_toBottomOf="@id/iv_land_pause" app:layout_constraintEnd_toStartOf="@id/tv_play_speed" app:layout_constraintTop_toTopOf="@id/iv_land_pause" style="@style/style_medium_text" />
        <LinearLayout android:orientation="horizontal" android:id="@id/ll_play_scale" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginEnd="20.0dip" app:layout_constraintBottom_toBottomOf="@id/iv_land_pause" app:layout_constraintEnd_toStartOf="@id/tv_language" app:layout_constraintTop_toTopOf="@id/iv_land_pause" app:layout_goneMarginEnd="18.0dip">
            <com.transsion.postdetail.ui.view.ImmScaleView android:id="@id/iv_play_scale" android:paddingTop="@dimen/dp_12" android:paddingBottom="@dimen/dp_12" android:layout_width="24.0dip" android:layout_height="wrap_content" android:src="@mipmap/ic_player_screen" android:paddingEnd="@dimen/dp_4" android:paddingVertical="@dimen/dp_12" />
            <TextView android:textSize="12.0sp" android:textColor="@color/white" android:ellipsize="end" android:layout_gravity="center_vertical" android:id="@id/tv_play_scale" android:layout_width="wrap_content" android:layout_height="wrap_content" android:maxWidth="64.0dip" android:text="@string/fit" android:maxLines="1" style="@style/style_medium_text" />
        </LinearLayout>
        <androidx.constraintlayout.widget.Group android:id="@id/group_control_pk" android:layout_width="wrap_content" android:layout_height="wrap_content" app:constraint_referenced_ids="tv_language" />
    </androidx.constraintlayout.widget.ConstraintLayout>
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/white" android:gravity="center_vertical" android:id="@id/iv_lock" android:padding="12.0dip" android:layout_width="wrap_content" android:layout_height="48.0dip" android:layout_marginLeft="30.0dip" android:layout_marginRight="30.0dip" android:text="@string/play_tap_lock" android:drawablePadding="@dimen/dp_6" android:drawableStart="@drawable/player_selector_lock" android:layout_marginHorizontal="30.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" style="@style/style_medium_text" />
    <include android:id="@id/centerControlLayout" android:layout_width="wrap_content" android:layout_height="wrap_content" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" layout="@layout/layout_play_center_control" />
    <FrameLayout android:id="@id/fl_playing_10_ad_group" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginBottom="16.0dip" android:layout_marginEnd="16.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" />
    <ViewStub android:id="@id/vs_forward_guide" android:layout="@layout/layout_local_video_land_double_click_guide" android:layout_width="fill_parent" android:layout_height="fill_parent" />
    <LinearLayout android:gravity="center" android:orientation="vertical" android:id="@id/ll_float_tips" android:background="@color/black" android:focusable="true" android:visibility="gone" android:clickable="true" android:layout_width="0.0dip" android:layout_height="0.0dip" android:drawableTop="@mipmap/video_float_ic_tips" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent">
        <androidx.appcompat.widget.AppCompatImageView android:layout_width="wrap_content" android:layout_height="wrap_content" app:srcCompat="@mipmap/video_float_ic_tips" />
        <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/white_40" android:id="@id/tv_float_tips" android:background="@color/black" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="8.0dip" android:text="@string/video_float_player_tips" style="@style/style_title_text" />
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
