<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:paddingBottom="2.0dip" android:layout_width="wrap_content" android:layout_height="wrap_content" android:accessibilityPaneTitle="@string/material_timepicker_select_time"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <TextView android:id="@id/header_title" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="16.0dip" android:text="@string/material_timepicker_select_time" android:importantForAccessibility="no" android:layout_marginStart="24.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" style="?materialTimePickerTitleStyle" />
    <com.google.android.material.timepicker.TimePickerView android:id="@id/material_timepicker_view" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginLeft="24.0dip" android:layout_marginTop="@dimen/material_clock_face_margin_top" android:layout_marginRight="24.0dip" android:layout_marginBottom="@dimen/material_clock_face_margin_bottom" app:layout_constraintBottom_toTopOf="@id/material_timepicker_mode_button" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <ViewStub android:id="@id/material_textinput_timepicker" android:layout="@layout/material_textinput_timepicker" android:inflatedId="@id/material_textinput_timepicker" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginLeft="24.0dip" android:layout_marginTop="44.0dip" android:layout_marginRight="24.0dip" android:layout_marginBottom="@dimen/material_clock_face_margin_bottom" app:layout_constraintBottom_toTopOf="@id/material_timepicker_mode_button" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <com.google.android.material.button.MaterialButton android:id="@id/material_timepicker_mode_button" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginStart="12.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" style="?imageButtonStyle" />
    <Button android:id="@id/material_timepicker_cancel_button" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="2.0dip" android:minWidth="72.0dip" android:text="@string/mtrl_timepicker_cancel" android:layout_marginEnd="8.0dip" app:layout_constraintEnd_toStartOf="@id/material_timepicker_ok_button" app:layout_constraintTop_toTopOf="@id/material_timepicker_mode_button" style="?borderlessButtonStyle" />
    <Button android:id="@id/material_timepicker_ok_button" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="2.0dip" android:minWidth="64.0dip" android:text="@string/mtrl_timepicker_confirm" android:layout_marginEnd="8.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="@id/material_timepicker_mode_button" style="?borderlessButtonStyle" />
</androidx.constraintlayout.widget.ConstraintLayout>
