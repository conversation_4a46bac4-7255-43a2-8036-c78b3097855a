<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:paddingLeft="12.0dip" android:paddingRight="12.0dip" android:layout_width="fill_parent" android:layout_height="56.0dip" android:layout_marginBottom="@dimen/dp_4" android:paddingHorizontal="12.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.google.android.material.imageview.ShapeableImageView android:id="@id/avatarIV" android:visibility="gone" android:layout_width="48.0dip" android:layout_height="48.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:shapeAppearanceOverlay="@style/circle_style" />
    <com.tn.lib.widget.TnTextView android:textSize="16.0sp" android:textColor="@color/text_01" android:ellipsize="end" android:gravity="center" android:id="@id/nameTv" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="4.0dip" android:tint="@color/white_60" android:maxLines="1" android:includeFontPadding="false" android:drawablePadding="4.0dip" android:drawableEnd="@mipmap/ic_arrow_right" android:layout_marginStart="12.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintHorizontal_bias="0.0" app:layout_constraintStart_toEndOf="@id/avatarIV" app:layout_constraintTop_toTopOf="parent" app:layout_goneMarginStart="0.0dip" style="@style/robot_bold" />
    <TextView android:textSize="12.0sp" android:textColor="@color/text_02" android:gravity="center_vertical" android:id="@id/genderTv" android:background="@drawable/bg_radius_4_color_white_10p" android:paddingLeft="8.0dip" android:paddingRight="8.0dip" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="20.0dip" android:layout_marginBottom="4.0dip" android:includeFontPadding="false" android:drawableStart="@mipmap/profile_man" android:paddingHorizontal="8.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="@id/nameTv" style="@style/style_tip_text" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/text_02" android:gravity="center" android:id="@id/idTv" android:background="@drawable/bg_radius_4_color_white_10p" android:paddingLeft="8.0dip" android:paddingRight="8.0dip" android:layout_width="wrap_content" android:layout_height="20.0dip" android:layout_marginBottom="4.0dip" android:includeFontPadding="false" android:drawablePadding="4.0dip" android:layout_marginStart="8.0dip" android:paddingHorizontal="8.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toEndOf="@id/genderTv" app:layout_goneMarginStart="0.0dip" style="@style/style_tip_text" />
    <TextView android:textColor="@color/text_05" android:gravity="center" android:id="@id/loginTv" android:background="@drawable/bg_linear_r4" android:paddingLeft="12.0dip" android:paddingRight="12.0dip" android:layout_width="wrap_content" android:layout_height="28.0dip" android:text="@string/user_login" android:paddingHorizontal="12.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent" style="@style/robot_bold" />
</androidx.constraintlayout.widget.ConstraintLayout>
