<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="62.0dip" android:layout_marginTop="8.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <View android:background="@drawable/bg_radius_6_color_white_6p" android:layout_width="fill_parent" android:layout_height="fill_parent" android:layout_marginLeft="@dimen/dp_12" android:layout_marginRight="@dimen/dp_12" android:layout_marginHorizontal="@dimen/dp_12" />
    <ImageView android:id="@id/icIV" android:layout_width="20.0dip" android:layout_height="20.0dip" android:layout_marginStart="24.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="14.0sp" android:textColor="@color/text_02" android:ellipsize="end" android:gravity="start|center" android:id="@id/titleTv" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginLeft="8.0dip" android:layout_marginRight="8.0dip" android:maxLines="1" android:includeFontPadding="false" android:layout_marginHorizontal="8.0dip" app:layout_constraintBottom_toTopOf="@id/desTv" app:layout_constraintEnd_toStartOf="@id/btnTv" app:layout_constraintStart_toEndOf="@id/icIV" app:layout_constraintTop_toTopOf="parent" app:layout_constraintVertical_chainStyle="packed" style="@style/robot_bold" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/text_03" android:id="@id/desTv" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginTop="4.0dip" android:includeFontPadding="false" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="@id/titleTv" app:layout_constraintStart_toStartOf="@id/titleTv" app:layout_constraintTop_toBottomOf="@id/titleTv" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/text_02" android:gravity="center" android:id="@id/btnTv" android:background="@drawable/bg_radius_4_color_white_10p" android:paddingLeft="8.0dip" android:paddingRight="8.0dip" android:layout_width="wrap_content" android:layout_height="24.0dip" android:includeFontPadding="false" android:drawablePadding="4.0dip" android:layout_marginEnd="4.0dip" android:paddingHorizontal="8.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toStartOf="@id/arrowIV" app:layout_constraintTop_toTopOf="parent" app:layout_goneMarginEnd="24.0dip" style="@style/robot_bold" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/arrowIV" android:layout_width="@dimen/dp_16" android:layout_height="@dimen/dp_16" android:src="@drawable/user_setting_arrow" android:layout_marginEnd="24.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent" app:tint="@color/white_60" />
</androidx.constraintlayout.widget.ConstraintLayout>
