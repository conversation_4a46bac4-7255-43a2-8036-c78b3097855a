<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <View android:id="@id/guideline" android:layout_width="0.0dip" android:layout_height="0.0dip" app:layout_constraintDimensionRatio="h,16:9" app:layout_constraintLeft_toLeftOf="parent" app:layout_constraintRight_toRightOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <androidx.cardview.widget.CardView android:id="@id/card_view" android:layout_width="0.0dip" android:layout_height="0.0dip" android:elevation="0.0dip" app:cardCornerRadius="8.0dip" app:layout_constraintBottom_toBottomOf="@id/guideline" app:layout_constraintEnd_toEndOf="@id/guideline" app:layout_constraintStart_toStartOf="@id/guideline" app:layout_constraintTop_toTopOf="@id/guideline" app:xhg_mask_drawable="@drawable/ad_shape_btn_11_bg">
        <com.hisavana.mediation.ad.TMediaView android:id="@id/native_ad_media" android:background="@android:color/black" android:layout_width="fill_parent" android:layout_height="fill_parent" />
    </androidx.cardview.widget.CardView>
    <androidx.appcompat.widget.LinearLayoutCompat android:orientation="horizontal" android:paddingLeft="2.0dip" android:paddingRight="2.0dip" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginLeft="2.0dip" android:layout_marginTop="6.0dip" android:layout_marginRight="2.0dip" android:minHeight="10.0dip" android:elevation="2.0dip" android:layout_marginHorizontal="2.0dip" android:paddingHorizontal="2.0dip" app:layout_constraintEnd_toEndOf="@id/card_view" app:layout_constraintStart_toStartOf="@id/card_view" app:layout_constraintTop_toTopOf="@id/card_view">
        <androidx.cardview.widget.CardView android:layout_gravity="center_vertical" android:id="@id/adChoicesViewCard" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginStart="4.0dip" app:cardBackgroundColor="@color/transparent" app:cardCornerRadius="4.0dip" app:cardElevation="0.0dip">
            <com.hisavana.mediation.ad.TAdChoicesView android:id="@id/native_ad_choices" android:layout_width="wrap_content" android:layout_height="wrap_content" />
        </androidx.cardview.widget.CardView>
        <com.transsion.wrapperad.view.AdTagView android:layout_gravity="center_vertical" android:id="@id/adIcon" android:layout_width="wrap_content" android:layout_height="16.0dip" android:layout_marginStart="4.0dip" />
        <androidx.constraintlayout.widget.ConstraintLayout android:layout_gravity="center_vertical" android:id="@id/store_mark_container" android:background="@drawable/ad_shape_store_mark_bg" android:layout_width="wrap_content" android:layout_height="16.0dip" android:layout_marginStart="4.0dip">
            <com.hisavana.mediation.ad.TStoreMarkView android:layout_gravity="center" android:id="@id/store_mark_view" android:layout_width="wrap_content" android:layout_height="wrap_content" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.appcompat.widget.LinearLayoutCompat>
    <View android:background="@drawable/ad_shape_bg_0_0_8_8" android:layout_width="0.0dip" android:layout_height="56.0dip" android:elevation="2.0dip" app:layout_constraintBottom_toBottomOf="@id/guideline" app:layout_constraintEnd_toEndOf="@id/guideline" app:layout_constraintStart_toStartOf="@id/guideline" />
    <androidx.cardview.widget.CardView android:id="@id/native_ad_icon_card" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginBottom="8.0dip" android:layout_marginStart="8.0dip" app:cardCornerRadius="4.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent">
        <com.hisavana.mediation.ad.TIconView android:layout_gravity="center_vertical" android:id="@id/native_ad_icon" android:layout_width="32.0dip" android:layout_height="32.0dip" />
    </androidx.cardview.widget.CardView>
    <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/white" android:ellipsize="end" android:id="@id/native_ad_title" android:layout_width="0.0dip" android:layout_height="wrap_content" android:maxLines="1" android:layout_marginStart="8.0dip" android:layout_marginEnd="8.0dip" android:elevation="2.0dip" app:layout_constraintEnd_toStartOf="@id/native_ad_action" app:layout_constraintStart_toEndOf="@id/native_ad_icon_card" app:layout_constraintTop_toTopOf="@id/native_ad_icon_card" style="@style/style_import_text" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="10.0sp" android:textColor="@color/white_60" android:ellipsize="end" android:id="@id/native_ad_des" android:layout_width="0.0dip" android:layout_height="wrap_content" android:maxLines="1" android:elevation="2.0dip" app:layout_constraintBottom_toBottomOf="@id/native_ad_icon_card" app:layout_constraintEnd_toEndOf="@id/native_ad_title" app:layout_constraintStart_toStartOf="@id/native_ad_title" app:layout_constraintTop_toBottomOf="@id/native_ad_title" style="@style/style_regular_text" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/gray_dark_00" android:ellipsize="end" android:gravity="center" android:id="@id/native_ad_action" android:background="@drawable/ad_shape_btn_4_bg" android:paddingLeft="6.0dip" android:paddingTop="2.0dip" android:paddingRight="6.0dip" android:paddingBottom="2.0dip" android:layout_width="wrap_content" android:layout_height="wrap_content" android:maxLines="1" android:textAllCaps="false" android:layout_marginEnd="8.0dip" android:elevation="2.0dip" android:paddingHorizontal="6.0dip" android:paddingVertical="2.0dip" app:layout_constraintBottom_toBottomOf="@id/native_ad_icon_card" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="@id/native_ad_icon_card" style="@style/style_import_text" />
</androidx.constraintlayout.widget.ConstraintLayout>
