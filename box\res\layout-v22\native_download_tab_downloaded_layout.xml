<?xml version="1.0" encoding="utf-8"?>
<FrameLayout android:background="@color/module_04" android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="wrap_content">
        <androidx.cardview.widget.CardView android:id="@id/flRoot" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginLeft="8.0dip" android:layout_marginTop="4.0dip" android:layout_marginRight="8.0dip" android:layout_marginBottom="4.0dip" android:layout_marginHorizontal="8.0dip" android:layout_marginVertical="4.0dip" app:cardCornerRadius="4.0dip" app:cardElevation="0.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent">
            <com.hisavana.mediation.ad.TMediaView android:id="@id/coverview" android:background="@drawable/ad_shape_btn_16_bg" android:layout_width="112.0dip" android:layout_height="56.0dip" app:sspScaleType="centerCrop" />
        </androidx.cardview.widget.CardView>
        <androidx.cardview.widget.CardView android:id="@id/adChoicesViewCard" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginStart="2.0dip" card_view:cardBackgroundColor="@color/transparent" card_view:cardCornerRadius="4.0dip" card_view:cardElevation="0.0dip" card_view:layout_constraintBottom_toBottomOf="@id/adIcon" card_view:layout_constraintStart_toStartOf="@id/flRoot" card_view:layout_constraintTop_toTopOf="@id/adIcon" xmlns:card_view="http://schemas.android.com/apk/res-auto">
            <com.hisavana.mediation.ad.TAdChoicesView android:id="@id/adChoicesView" android:layout_width="wrap_content" android:layout_height="wrap_content" />
        </androidx.cardview.widget.CardView>
        <com.transsion.wrapperad.view.AdTagView android:id="@id/adIcon" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="2.0dip" android:src="@mipmap/ad_icon_1" android:layout_marginStart="4.0dip" app:layout_constraintStart_toEndOf="@id/adChoicesViewCard" app:layout_constraintTop_toTopOf="@id/flRoot" />
        <androidx.appcompat.widget.LinearLayoutCompat android:orientation="vertical" android:paddingTop="5.0dip" android:paddingBottom="5.0dip" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginLeft="8.0dip" android:layout_marginRight="8.0dip" android:layout_marginHorizontal="8.0dip" android:paddingVertical="5.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/flRoot" app:layout_constraintTop_toTopOf="parent">
            <androidx.constraintlayout.widget.ConstraintLayout android:layout_gravity="start" android:id="@id/store_mark_container" android:background="@drawable/ad_shape_btn_16_bg" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginBottom="2.0dip">
                <com.hisavana.mediation.ad.TStoreMarkView android:layout_gravity="center" android:id="@id/store_mark_view" android:layout_width="wrap_content" android:layout_height="wrap_content" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>
            <TextView android:textSize="14.0sp" android:textStyle="bold" android:textColor="@color/text_01" android:ellipsize="end" android:id="@id/native_ad_body" android:layout_width="fill_parent" android:layout_height="wrap_content" android:maxLines="1" android:includeFontPadding="false" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/coverview" app:layout_constraintTop_toTopOf="@id/coverview" style="@style/style_regular_text" />
            <androidx.appcompat.widget.LinearLayoutCompat android:orientation="horizontal" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="2.0dip">
                <androidx.cardview.widget.CardView android:layout_gravity="center_vertical" android:id="@id/native_ad_icon_card" android:layout_width="wrap_content" android:layout_height="wrap_content" card_view:cardBackgroundColor="@color/transparent" card_view:cardCornerRadius="6.0dip" card_view:cardElevation="0.0dip" xmlns:card_view="http://schemas.android.com/apk/res-auto">
                    <com.hisavana.mediation.ad.TIconView android:id="@id/native_ad_icon" android:layout_width="12.0dip" android:layout_height="12.0dip" />
                </androidx.cardview.widget.CardView>
                <TextView android:textSize="12.0sp" android:textColor="@color/text_03" android:ellipsize="end" android:gravity="center_vertical" android:layout_gravity="center_vertical" android:id="@id/native_ad_title" android:layout_width="wrap_content" android:layout_height="wrap_content" android:lines="1" android:includeFontPadding="false" android:layout_weight="1.0" android:layout_marginStart="4.0dip" android:layout_marginEnd="2.0dip" style="@style/style_regular_text" />
                <androidx.appcompat.widget.LinearLayoutCompat android:gravity="center" android:layout_gravity="end" android:orientation="horizontal" android:background="@drawable/ad_shape_btn_12_bg" android:paddingLeft="4.0dip" android:paddingRight="4.0dip" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="2.0dip" android:minWidth="68.0dip" android:paddingHorizontal="4.0dip" app:layout_constraintEnd_toEndOf="parent">
                    <androidx.appcompat.widget.AppCompatTextView android:textSize="10.0sp" android:textColor="@color/brand" android:ellipsize="end" android:gravity="center_vertical" android:layout_gravity="center_vertical" android:id="@id/call_to_action" android:layout_width="wrap_content" android:layout_height="20.0dip" android:text="DOWNLOAD" android:maxLines="1" android:includeFontPadding="false" android:textAllCaps="false" app:layout_constraintEnd_toEndOf="parent" style="@style/style_medium_text" />
                    <androidx.appcompat.widget.AppCompatImageView android:layout_gravity="center" android:layout_width="wrap_content" android:layout_height="wrap_content" android:src="@mipmap/ic_right" />
                </androidx.appcompat.widget.LinearLayoutCompat>
            </androidx.appcompat.widget.LinearLayoutCompat>
        </androidx.appcompat.widget.LinearLayoutCompat>
    </androidx.constraintlayout.widget.ConstraintLayout>
</FrameLayout>
