<?xml version="1.0" encoding="utf-8"?>
<FrameLayout android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <View android:layout_width="fill_parent" android:layout_height="fill_parent" />
    <androidx.constraintlayout.widget.ConstraintLayout android:paddingTop="100.0dip" android:layout_width="fill_parent" android:layout_height="fill_parent">
        <com.hisavana.mediation.ad.TMediaView android:id="@id/coverview" android:layout_width="fill_parent" android:layout_height="fill_parent" android:layout_marginBottom="80.0dip" />
        <androidx.appcompat.widget.LinearLayoutCompat android:orientation="horizontal" android:paddingLeft="2.0dip" android:paddingTop="6.0dip" android:paddingRight="2.0dip" android:layout_width="0.0dip" android:layout_height="wrap_content" android:minHeight="10.0dip" android:paddingHorizontal="2.0dip" app:layout_constraintEnd_toEndOf="@id/coverview" app:layout_constraintStart_toStartOf="@id/coverview" app:layout_constraintTop_toTopOf="@id/coverview">
            <androidx.cardview.widget.CardView android:layout_gravity="center_vertical" android:id="@id/adChoicesViewCard" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginStart="4.0dip" card_view:cardBackgroundColor="@color/transparent" card_view:cardCornerRadius="4.0dip" card_view:cardElevation="0.0dip" xmlns:card_view="http://schemas.android.com/apk/res-auto">
                <com.hisavana.mediation.ad.TAdChoicesView android:id="@id/adChoicesView" android:layout_width="wrap_content" android:layout_height="wrap_content" />
            </androidx.cardview.widget.CardView>
            <com.transsion.wrapperad.view.AdTagView android:layout_gravity="center_vertical" android:id="@id/adIcon" android:layout_width="wrap_content" android:layout_height="16.0dip" android:layout_marginStart="4.0dip" />
            <androidx.constraintlayout.widget.ConstraintLayout android:layout_gravity="center_vertical" android:id="@id/store_mark_container" android:background="@drawable/ad_shape_store_mark_bg" android:layout_width="wrap_content" android:layout_height="16.0dip" android:layout_marginStart="4.0dip">
                <com.hisavana.mediation.ad.TStoreMarkView android:layout_gravity="center" android:id="@id/store_mark_view" android:layout_width="wrap_content" android:layout_height="wrap_content" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.appcompat.widget.LinearLayoutCompat>
        <View android:background="@drawable/ad_shape_bg_bg_ad" android:layout_width="0.0dip" android:layout_height="320.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" />
        <androidx.appcompat.widget.AppCompatTextView android:textSize="11.0sp" android:textColor="@color/white" android:gravity="center_horizontal" android:id="@id/tvLike" android:layout_width="40.0dip" android:layout_height="wrap_content" android:layout_marginBottom="20.0dip" android:text="59" android:shadowColor="#80000000" android:shadowRadius="3.0" android:drawablePadding="2.0dip" app:drawableTopCompat="@mipmap/ad_ic_video_like" app:layout_constraintBottom_toTopOf="@id/tvComment" app:layout_constraintEnd_toEndOf="@id/ivShare" />
        <androidx.appcompat.widget.AppCompatImageView android:id="@id/tvComment" android:layout_width="40.0dip" android:layout_height="40.0dip" android:layout_marginBottom="20.0dip" android:scaleType="center" app:layout_constraintBottom_toTopOf="@id/ivShare" app:layout_constraintEnd_toEndOf="@id/ivShare" app:srcCompat="@mipmap/ad_ic_video_comment" />
        <androidx.appcompat.widget.AppCompatImageView android:id="@id/ivShare" android:layout_width="40.0dip" android:layout_height="40.0dip" android:layout_marginBottom="26.0dip" android:scaleType="center" android:layout_marginEnd="12.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:srcCompat="@mipmap/ad_ic_video_share" />
        <androidx.constraintlayout.widget.ConstraintLayout android:id="@id/cl_subject_res" android:background="@drawable/ad_shape_imm_video_subject_bg" android:layout_width="0.0dip" android:layout_height="56.0dip" android:layout_marginBottom="74.0dip" android:alpha="0.8" android:layout_marginStart="16.0dip" android:layout_marginEnd="12.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toStartOf="@id/ivShare" app:layout_constraintStart_toStartOf="parent" app:layout_goneMarginBottom="0.0dip">
            <com.transsion.wrapperad.hi.MaskLayout android:id="@id/maskView" android:layout_width="44.0dip" android:layout_height="44.0dip" android:layout_marginStart="6.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:xhg_mask_drawable="@drawable/ad_shape_dp_4">
                <com.hisavana.mediation.ad.TIconView android:id="@id/native_ad_icon" android:layout_width="44.0dip" android:layout_height="44.0dip" android:scaleType="centerCrop" />
            </com.transsion.wrapperad.hi.MaskLayout>
            <TextView android:textSize="14.0sp" android:textColor="@android:color/white" android:ellipsize="end" android:gravity="start|center" android:id="@id/native_ad_title" android:layout_width="0.0dip" android:layout_height="fill_parent" android:maxLines="2" android:layout_marginStart="4.0dip" android:layout_marginEnd="8.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toStartOf="@id/v_subject_res_line" app:layout_constraintStart_toEndOf="@id/maskView" app:layout_constraintTop_toTopOf="parent" style="@style/style_regular_text" />
            <View android:id="@id/v_subject_res_line" android:background="#33ffffff" android:layout_width="1.0dip" android:layout_height="14.0dip" android:layout_marginEnd="8.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toStartOf="@id/call_to_action" app:layout_constraintTop_toTopOf="parent" />
            <androidx.appcompat.widget.AppCompatTextView android:textSize="14.0sp" android:textColor="@color/brand_dark_50" android:id="@id/call_to_action" android:background="@android:color/transparent" android:paddingLeft="12.0dip" android:paddingRight="12.0dip" android:layout_width="wrap_content" android:layout_height="wrap_content" android:minWidth="68.0dip" android:text="Download" android:textAllCaps="false" android:layout_marginEnd="8.0dip" android:paddingHorizontal="12.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent" style="@style/style_medium_text" />
        </androidx.constraintlayout.widget.ConstraintLayout>
        <TextView android:textSize="14.0sp" android:textColor="@android:color/white" android:ellipsize="end" android:gravity="start|center" android:id="@id/native_ad_body" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginTop="12.0dip" android:maxLines="2" android:layout_marginEnd="12.0dip" app:layout_constraintEnd_toStartOf="@id/ivShare" app:layout_constraintStart_toStartOf="@id/cl_subject_res" app:layout_constraintTop_toBottomOf="@id/cl_subject_res" style="@style/style_regular_text" />
        <ProgressBar android:id="@id/progress_bar" android:layout_width="fill_parent" android:layout_height="2.0dip" android:max="100" android:progress="50" android:progressDrawable="@drawable/progress_bg" app:layout_constraintBottom_toBottomOf="parent" style="?android:progressBarStyleHorizontal" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</FrameLayout>
