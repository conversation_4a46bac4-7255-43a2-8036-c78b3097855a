<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.constraintlayout.widget.ConstraintLayout android:background="@color/black" android:layout_width="fill_parent" android:layout_height="fill_parent" android:layout_above="@id/cl_bottom">
        <com.hisavana.mediation.ad.TMediaView android:id="@id/native_ad_media" android:background="@android:color/black" android:layout_width="0.0dip" android:layout_height="fill_parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintHorizontal_bias="0.0" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:layout_constraintVertical_bias="1.0" />
        <androidx.appcompat.widget.LinearLayoutCompat android:orientation="horizontal" android:paddingLeft="2.0dip" android:paddingRight="2.0dip" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginLeft="2.0dip" android:layout_marginTop="6.0dip" android:layout_marginRight="2.0dip" android:minHeight="10.0dip" android:layout_marginHorizontal="2.0dip" android:paddingHorizontal="2.0dip" app:layout_constraintEnd_toEndOf="@id/native_ad_media" app:layout_constraintStart_toStartOf="@id/native_ad_media" app:layout_constraintTop_toTopOf="@id/native_ad_media">
            <androidx.cardview.widget.CardView android:layout_gravity="center_vertical" android:id="@id/adChoicesViewCard" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginStart="2.0dip" card_view:cardBackgroundColor="@color/transparent" card_view:cardCornerRadius="4.0dip" card_view:cardElevation="0.0dip" xmlns:card_view="http://schemas.android.com/apk/res-auto">
                <com.hisavana.mediation.ad.TAdChoicesView android:id="@id/adChoicesView" android:layout_width="wrap_content" android:layout_height="wrap_content" />
            </androidx.cardview.widget.CardView>
            <com.transsion.wrapperad.view.AdTagView android:layout_gravity="center_vertical" android:id="@id/adIcon" android:layout_width="wrap_content" android:layout_height="16.0dip" android:layout_marginStart="4.0dip" />
        </androidx.appcompat.widget.LinearLayoutCompat>
        <androidx.constraintlayout.widget.ConstraintLayout android:layout_gravity="center_vertical" android:id="@id/store_mark_container" android:background="@color/black_50" android:layout_width="fill_parent" android:layout_height="16.0dip" app:layout_constraintBottom_toBottomOf="@id/native_ad_media" app:layout_constraintStart_toStartOf="@id/native_ad_media">
            <com.hisavana.mediation.ad.TStoreMarkView android:layout_gravity="center_vertical" android:id="@id/store_mark_view" android:layout_width="wrap_content" android:layout_height="wrap_content" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
    <androidx.constraintlayout.widget.ConstraintLayout android:id="@id/cl_bottom" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="46.0dip" android:layout_alignParentBottom="true">
        <TextView android:textSize="12.0sp" android:textColor="@color/video_ad_title" android:ellipsize="end" android:id="@id/native_ad_title" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginTop="5.0dip" android:lines="1" android:layout_marginStart="5.0dip" android:layout_marginEnd="5.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" style="@style/style_medium_text" />
        <com.hisavana.mediation.ad.TIconView android:id="@id/native_ad_icon" android:layout_width="15.0dip" android:layout_height="15.0dip" android:layout_marginBottom="5.0dip" android:layout_marginStart="5.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" />
        <TextView android:textSize="10.0sp" android:textColor="@color/video_ad_des" android:ellipsize="end" android:gravity="center_vertical" android:id="@id/native_ad_des" android:layout_width="0.0dip" android:layout_height="0.0dip" android:lines="1" android:layout_marginStart="4.0dip" android:layout_marginEnd="5.0dip" app:layout_constraintBottom_toBottomOf="@id/native_ad_icon" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/native_ad_icon" app:layout_constraintTop_toTopOf="@id/native_ad_icon" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</RelativeLayout>
