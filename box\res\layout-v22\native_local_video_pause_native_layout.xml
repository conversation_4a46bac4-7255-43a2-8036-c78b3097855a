<?xml version="1.0" encoding="utf-8"?>
<androidx.appcompat.widget.LinearLayoutCompat android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="wrap_content">
        <View android:id="@id/guideline" android:layout_width="0.0dip" android:layout_height="0.0dip" app:layout_constraintDimensionRatio="h,16:9" app:layout_constraintLeft_toLeftOf="parent" app:layout_constraintRight_toRightOf="parent" app:layout_constraintTop_toTopOf="parent" />
        <com.hisavana.mediation.ad.TMediaView android:id="@id/coverview" android:background="@android:color/black" android:layout_width="0.0dip" android:layout_height="0.0dip" app:layout_constraintBottom_toBottomOf="@id/guideline" app:layout_constraintEnd_toEndOf="@id/guideline" app:layout_constraintStart_toStartOf="@id/guideline" app:layout_constraintTop_toTopOf="@id/guideline" />
        <androidx.appcompat.widget.LinearLayoutCompat android:orientation="horizontal" android:paddingLeft="2.0dip" android:paddingRight="2.0dip" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginLeft="2.0dip" android:layout_marginTop="6.0dip" android:layout_marginRight="2.0dip" android:minHeight="10.0dip" android:layout_marginHorizontal="2.0dip" android:paddingHorizontal="2.0dip" app:layout_constraintEnd_toEndOf="@id/coverview" app:layout_constraintStart_toStartOf="@id/coverview" app:layout_constraintTop_toTopOf="@id/coverview">
            <androidx.cardview.widget.CardView android:layout_gravity="center_vertical" android:id="@id/adChoicesViewCard" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginStart="4.0dip" card_view:cardBackgroundColor="@color/transparent" card_view:cardCornerRadius="4.0dip" card_view:cardElevation="0.0dip" xmlns:card_view="http://schemas.android.com/apk/res-auto">
                <com.hisavana.mediation.ad.TAdChoicesView android:id="@id/adChoicesView" android:layout_width="wrap_content" android:layout_height="wrap_content" />
            </androidx.cardview.widget.CardView>
            <com.transsion.wrapperad.view.AdTagView android:layout_gravity="center_vertical" android:id="@id/adIcon" android:layout_width="wrap_content" android:layout_height="16.0dip" android:layout_marginStart="4.0dip" />
        </androidx.appcompat.widget.LinearLayoutCompat>
        <androidx.constraintlayout.widget.ConstraintLayout android:layout_gravity="center_vertical" android:id="@id/store_mark_container" android:background="@color/black_50" android:layout_width="fill_parent" android:layout_height="16.0dip" android:layout_marginStart="4.0dip" app:layout_constraintBottom_toBottomOf="@id/coverview" app:layout_constraintStart_toStartOf="@id/coverview">
            <com.hisavana.mediation.ad.TStoreMarkView android:layout_gravity="center" android:id="@id/store_mark_view" android:layout_width="wrap_content" android:layout_height="wrap_content" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
    <androidx.constraintlayout.widget.ConstraintLayout android:id="@id/clText" android:background="@color/white" android:paddingLeft="8.0dip" android:paddingTop="4.0dip" android:paddingRight="8.0dip" android:paddingBottom="8.0dip" android:layout_width="fill_parent" android:layout_height="0.0dip" android:layout_weight="1.0" android:paddingHorizontal="8.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="@id/coverview" app:layout_constraintStart_toStartOf="@id/coverview" app:layout_constraintTop_toBottomOf="@id/coverview">
        <TextView android:textSize="10.0sp" android:textColor="@color/gray_light_80" android:ellipsize="end" android:id="@id/native_ad_title" android:layout_width="0.0dip" android:layout_height="wrap_content" android:lines="1" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" style="@style/style_medium_text" />
        <com.transsion.wrapperad.hi.MaskLayout android:id="@id/mask_ic" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="6.0dip" android:layout_marginBottom="8.0dip" app:layout_constraintStart_toStartOf="@id/native_ad_title" app:layout_constraintTop_toBottomOf="@id/native_ad_title">
            <androidx.cardview.widget.CardView android:layout_width="wrap_content" android:layout_height="wrap_content" app:cardCornerRadius="2.0dip" app:cardElevation="0.0dip">
                <com.hisavana.mediation.ad.TIconView android:id="@id/native_ad_icon" android:layout_width="12.0dip" android:layout_height="12.0dip" />
            </androidx.cardview.widget.CardView>
        </com.transsion.wrapperad.hi.MaskLayout>
        <TextView android:textSize="10.0sp" android:textColor="@color/gray_light_50" android:ellipsize="end" android:id="@id/native_ad_body" android:layout_width="0.0dip" android:layout_height="wrap_content" android:lines="1" android:layout_marginStart="4.0dip" app:layout_constraintBottom_toBottomOf="@id/mask_ic" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/mask_ic" app:layout_constraintTop_toTopOf="@id/mask_ic" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.appcompat.widget.LinearLayoutCompat>
