<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:id="@id/ad_unit" android:background="@android:color/white" android:padding="@dimen/hisavana_ad_dimen_12" android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <TextView android:textStyle="normal" android:textColor="@color/hisavana_ad_color_222222" android:id="@id/hisavana_native_ad_body" android:layout_marginRight="@dimen/hisavana_ad_dimen_12" android:text="公益性广告投放" app:layout_constraintBottom_toTopOf="@id/group" app:layout_constraintLeft_toLeftOf="parent" app:layout_constraintRight_toLeftOf="@id/hisavana_call_to_action" app:layout_constraintTop_toTopOf="parent" style="@style/native_body_style1" />
    <TextView android:textSize="@dimen/hisavana_ad_dimen_12sp" android:textColor="@android:color/white" android:gravity="center" android:layout_gravity="center_vertical" android:id="@id/hisavana_call_to_action" android:background="@drawable/hisavana_bg_blue_radius" android:paddingLeft="@dimen/hisavana_ad_dimen_24" android:paddingRight="@dimen/hisavana_ad_dimen_24" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="我是按钮文本test" android:textAllCaps="false" android:paddingHorizontal="@dimen/hisavana_ad_dimen_24" app:layout_constraintBottom_toTopOf="@id/ps_mark_view" app:layout_constraintRight_toRightOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <com.cloud.hisavana.sdk.api.view.StoreMarkView android:id="@id/ps_mark_view" android:layout_width="wrap_content" android:layout_height="wrap_content" app:layout_constraintBottom_toTopOf="@id/group" app:layout_constraintRight_toRightOf="parent" />
    <androidx.constraintlayout.widget.Group android:id="@id/group" android:layout_width="fill_parent" android:layout_height="fill_parent" app:constraint_referenced_ids="hisavana_native_ad_body,hisavana_call_to_action" app:layout_constraintBottom_toTopOf="@id/hisavana_coverview" />
    <com.cloud.hisavana.sdk.api.view.MediaView android:id="@id/hisavana_coverview" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="@dimen/hisavana_ad_dimen_8" app:layout_constraintLeft_toLeftOf="parent" app:layout_constraintRight_toRightOf="parent" app:layout_constraintTop_toBottomOf="@id/group" />
    <com.cloud.sdk.commonutil.widget.TranCircleImageView android:id="@id/hisavana_native_ad_icon" android:layout_width="@dimen/hisavana_ad_dimen_14" android:layout_height="@dimen/hisavana_ad_dimen_14" app:layout_constraintBottom_toBottomOf="@id/hisavana_native_ad_title" app:layout_constraintLeft_toLeftOf="parent" app:layout_constraintTop_toTopOf="@id/hisavana_native_ad_title" />
    <TextView android:id="@id/hisavana_native_ad_title" android:layout_marginLeft="@dimen/hisavana_ad_dimen_8" android:layout_marginTop="@dimen/hisavana_ad_dimen_5" android:text="eagllwin" android:layout_marginEnd="@dimen/hisavana_ad_dimen_12" app:layout_constraintEnd_toStartOf="@id/ad_close_view" app:layout_constraintStart_toEndOf="@id/hisavana_native_ad_icon" app:layout_constraintTop_toBottomOf="@id/hisavana_coverview" app:layout_goneMarginEnd="0.0dip" app:layout_goneMarginLeft="0.0dip" style="@style/native_title_style" />
    <include android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="@dimen/hisavana_ad_dimen_8" android:layout_marginRight="@dimen/hisavana_ad_dimen_8" app:layout_constraintRight_toRightOf="@id/hisavana_coverview" app:layout_constraintTop_toTopOf="@id/hisavana_coverview" layout="@layout/include_ad_flag" />
</androidx.constraintlayout.widget.ConstraintLayout>
