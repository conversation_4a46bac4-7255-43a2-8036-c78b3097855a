<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:id="@id/ad_unit" android:background="@drawable/ssp_bg_ffffff_0_0_8_8" android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.cloud.hisavana.sdk.api.view.MediaView android:id="@id/hisavana_coverview" android:layout_width="fill_parent" android:layout_height="wrap_content" app:layout_constraintLeft_toLeftOf="parent" app:layout_constraintRight_toRightOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <androidx.constraintlayout.widget.ConstraintLayout android:paddingLeft="@dimen/hisavana_ad_dimen_12" android:paddingRight="@dimen/hisavana_ad_dimen_12" android:paddingBottom="@dimen/hisavana_ad_dimen_12" android:layout_width="fill_parent" android:layout_height="wrap_content" android:paddingHorizontal="@dimen/hisavana_ad_dimen_12" app:layout_constraintTop_toBottomOf="@id/hisavana_coverview">
        <com.cloud.sdk.commonutil.widget.TranCircleImageView android:id="@id/hisavana_native_ad_icon" android:layout_width="@dimen/hisavana_ad_dimen_14" android:layout_height="@dimen/hisavana_ad_dimen_14" android:layout_marginTop="@dimen/hisavana_ad_dimen_10" app:layout_constraintLeft_toLeftOf="parent" app:layout_constraintTop_toTopOf="parent" />
        <TextView android:id="@id/hisavana_native_ad_title" android:layout_marginLeft="@dimen/hisavana_ad_dimen_4" android:text="eagllwin" app:layout_constraintBottom_toBottomOf="@id/hisavana_native_ad_icon" app:layout_constraintLeft_toRightOf="@id/hisavana_native_ad_icon" app:layout_constraintRight_toRightOf="parent" app:layout_constraintTop_toTopOf="@id/hisavana_native_ad_icon" style="@style/native_title_style1" />
        <TextView android:textSize="@dimen/hisavana_ad_dimen_11sp" android:id="@id/hisavana_native_ad_body" android:layout_width="fill_parent" android:layout_marginTop="@dimen/hisavana_ad_dimen_6" android:text="Make marketing more convenient and efficient." app:layout_constraintLeft_toLeftOf="parent" app:layout_constraintRight_toRightOf="parent" app:layout_constraintTop_toBottomOf="@id/hisavana_native_ad_icon" style="@style/native_body_style1" />
        <TextView android:textSize="@dimen/hisavana_ad_dimen_12sp" android:textColor="@android:color/white" android:gravity="center" android:layout_gravity="center_vertical" android:id="@id/hisavana_call_to_action" android:background="@drawable/hisavana_bg_blue_radius" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="@dimen/hisavana_ad_dimen_6" android:layout_marginBottom="9.0dip" android:text="INSTALL" android:textAllCaps="false" app:layout_constraintBottom_toTopOf="@id/ps_mark_view" app:layout_constraintLeft_toLeftOf="parent" app:layout_constraintTop_toBottomOf="@id/hisavana_native_ad_body" />
        <com.cloud.hisavana.sdk.api.view.StoreMarkView android:id="@id/ps_mark_view" android:layout_width="wrap_content" android:layout_height="wrap_content" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
    <include android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="@dimen/hisavana_ad_dimen_8" android:layout_marginRight="@dimen/hisavana_ad_dimen_8" app:layout_constraintRight_toRightOf="@id/hisavana_coverview" app:layout_constraintTop_toTopOf="@id/hisavana_coverview" layout="@layout/include_ad_flag" />
</androidx.constraintlayout.widget.ConstraintLayout>
