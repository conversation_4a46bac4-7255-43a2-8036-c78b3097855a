<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="wrap_content" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.transsion.baseui.widget.RoundedConstraintLayout android:layout_width="wrap_content" android:layout_height="wrap_content" app:bottomRightRadius="12.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent">
        <androidx.appcompat.widget.AppCompatImageView android:layout_width="0.0dip" android:layout_height="0.0dip" android:src="@mipmap/ic_points_bg" android:scaleType="fitXY" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
        <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_points_img" android:layout_width="@dimen/dp_20" android:layout_height="@dimen/dimens_20" android:src="@mipmap/ic_points" android:layout_marginStart="@dimen/dp_16" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
        <androidx.appcompat.widget.AppCompatTextView android:textSize="20.0sp" android:textColor="@color/gray_light_80" android:gravity="center_vertical" android:id="@id/iv_points_text" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="2.0dip" android:layout_marginBottom="2.0dip" android:includeFontPadding="false" android:drawableEnd="@mipmap/ic_points_right" android:layout_marginStart="2.0dip" android:layout_marginEnd="12.0dip" android:layout_marginVertical="2.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/iv_points_img" app:layout_constraintTop_toTopOf="parent" style="@style/style_extra_import_text" />
    </com.transsion.baseui.widget.RoundedConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
