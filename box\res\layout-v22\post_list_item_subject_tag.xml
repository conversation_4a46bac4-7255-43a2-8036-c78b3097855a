<?xml version="1.0" encoding="utf-8"?>
<merge
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.appcompat.widget.LinearLayoutCompat android:gravity="center_vertical" android:orientation="horizontal" android:id="@id/ll_subject" android:clipChildren="true" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="12.0dip" app:layout_constraintTop_toBottomOf="@id/cl_cover">
        <androidx.appcompat.widget.LinearLayoutCompat android:orientation="vertical" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_weight="1.0" android:layout_marginEnd="4.0dip">
            <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/text_01" android:ellipsize="end" android:id="@id/tv_subject" android:visibility="visible" android:layout_marginBottom="2.0dip" android:maxLines="1" android:layout_marginEnd="4.0dip" style="@style/style_medium_text" />
            <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/text_03" android:ellipsize="end" android:gravity="center_vertical" android:id="@id/tv_subject_year" android:visibility="visible" android:maxLines="1" android:drawablePadding="4.0dip" style="@style/style_regula_bigger_text" />
        </androidx.appcompat.widget.LinearLayoutCompat>
        <com.transsnet.downloader.widget.DownloadView android:gravity="center_vertical" android:id="@id/ll_download" android:background="@drawable/bg_btn_01_radius_4" android:paddingLeft="12.0dip" android:paddingRight="12.0dip" android:layout_width="wrap_content" android:layout_height="32.0dip" android:paddingHorizontal="12.0dip" />
    </androidx.appcompat.widget.LinearLayoutCompat>
</merge>
