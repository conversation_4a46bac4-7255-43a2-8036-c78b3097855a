<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.constraintlayout.widget.ConstraintLayout android:id="@id/cl_content" android:background="@color/common_white" android:paddingBottom="24.0dip" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginLeft="45.0dip" android:layout_marginRight="45.0dip" android:layout_marginHorizontal="45.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintTop_toTopOf="parent">
        <androidx.appcompat.widget.AppCompatImageView android:id="@id/img_top" android:layout_width="fill_parent" android:layout_height="0.0dip" android:src="@mipmap/ic_notice_permission_tips" android:scaleType="fitXY" app:layout_constraintDimensionRatio="h,280:96" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
        <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textColor="#ff191f2b" android:gravity="center" android:id="@id/tv_privacy" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginLeft="20.0dip" android:layout_marginTop="8.0dip" android:layout_marginRight="20.0dip" android:text="@string/permission_notice_tips" android:layout_marginHorizontal="20.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/img_top" style="@style/style_regula_bigger_text" />
        <androidx.appcompat.widget.AppCompatTextView android:textSize="14.0sp" android:textColor="#ff191f2b" android:gravity="center" android:id="@id/tv_later" android:background="#fff2f5fa" android:layout_width="0.0dip" android:layout_height="36.0dip" android:layout_marginTop="16.0dip" android:text="@string/permission_notice_later" android:layout_marginStart="24.0dip" android:layout_marginEnd="4.0dip" app:layout_constraintEnd_toStartOf="@id/tv_allow" app:layout_constraintHorizontal_weight="1.0" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tv_privacy" style="@style/style_medium_text" />
        <androidx.appcompat.widget.AppCompatTextView android:textSize="14.0sp" android:textColor="@color/white" android:gravity="center" android:id="@id/tv_allow" android:background="@drawable/libui_main_btn_selector" android:layout_width="0.0dip" android:layout_height="36.0dip" android:layout_marginTop="16.0dip" android:text="@string/permission_notice_allow" android:layout_marginStart="4.0dip" android:layout_marginEnd="24.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintHorizontal_weight="1.0" app:layout_constraintStart_toEndOf="@id/tv_later" app:layout_constraintTop_toBottomOf="@id/tv_privacy" style="@style/style_medium_text" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
