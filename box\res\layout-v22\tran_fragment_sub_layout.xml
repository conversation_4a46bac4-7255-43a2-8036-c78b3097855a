<?xml version="1.0" encoding="utf-8"?>
<androidx.appcompat.widget.LinearLayoutCompat android:gravity="center_horizontal" android:orientation="vertical" android:id="@id/root" android:background="@drawable/bg_pay_dialog" android:focusable="true" android:focusableInTouchMode="true" android:clickable="true" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="48.0dip">
        <FrameLayout android:id="@id/iv_back" android:layout_width="44.0dip" android:layout_height="44.0dip" android:layout_marginTop="0.0dip" android:layout_marginStart="0.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent">
            <androidx.appcompat.widget.AppCompatImageView android:layout_gravity="center" android:layout_width="20.0dip" android:layout_height="20.0dip" android:src="@mipmap/ic_back" />
        </FrameLayout>
        <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textStyle="bold" android:textColor="#ff181f2b" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/pay_online_payment" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
        <View android:background="#ffedf0f5" android:layout_width="fill_parent" android:layout_height="1.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" />
        <FrameLayout android:layout_width="44.0dip" android:layout_height="44.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent">
            <androidx.appcompat.widget.AppCompatImageView android:layout_gravity="center" android:id="@id/iv_close" android:layout_width="20.0dip" android:layout_height="20.0dip" android:src="@mipmap/ic_close" />
        </FrameLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
    <androidx.core.widget.NestedScrollView android:id="@id/scroll_view" android:tag="scrollView" android:paddingLeft="12.0dip" android:paddingRight="12.0dip" android:persistentDrawingCache="animation" android:layout_width="fill_parent" android:layout_height="0.0dip" android:layout_weight="1.0" android:paddingHorizontal="12.0dip" app:layout_behavior="@string/appbar_scrolling_view_behavior" app:layout_scrollFlags="scroll">
        <androidx.appcompat.widget.LinearLayoutCompat android:orientation="vertical" android:paddingLeft="20.0dip" android:paddingTop="12.0dip" android:paddingRight="20.0dip" android:paddingBottom="12.0dip" android:layout_width="fill_parent" android:layout_height="0.0dip" android:paddingHorizontal="20.0dip" android:paddingVertical="12.0dip">
            <androidx.appcompat.widget.LinearLayoutCompat android:gravity="center_horizontal" android:layout_gravity="center_horizontal" android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="16.0dip" android:layout_marginBottom="16.0dip" android:layout_marginVertical="16.0dip">
                <androidx.appcompat.widget.AppCompatTextView android:textSize="14.0sp" android:textColor="@color/payment_text" android:id="@id/iv_company" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="0.0dip" />
                <androidx.appcompat.widget.AppCompatTextView android:textSize="24.0sp" android:textStyle="bold" android:textColor="@color/payment_text" android:id="@id/iv_amount" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="10.0dip" />
                <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/payment_text" android:gravity="center" android:id="@id/iv_desc" android:layout_width="wrap_content" android:layout_height="wrap_content" android:fontFamily="sans-serif-medium" />
                <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/payment_text" android:gravity="center" android:id="@id/iv_order_id" android:layout_width="wrap_content" android:layout_height="wrap_content" android:fontFamily="sans-serif-medium" />
                <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/payment_text" android:gravity="center" android:id="@id/iv_payment_method" android:layout_width="wrap_content" android:layout_height="wrap_content" android:fontFamily="sans-serif-medium" />
            </androidx.appcompat.widget.LinearLayoutCompat>
            <View android:background="#ffedf0f5" android:layout_width="fill_parent" android:layout_height="1.0dip" />
            <androidx.appcompat.widget.AppCompatTextView android:textSize="14.0sp" android:textStyle="bold" android:textColor="@color/payment_text" android:gravity="center" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="16.0dip" android:text="@string/pay_please_fill_in_your_information" />
            <androidx.constraintlayout.widget.ConstraintLayout android:id="@id/iv_phone_container" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="12.0dip">
                <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textColor="@color/payment_text" android:gravity="center" android:id="@id/iv_phone_code" android:layout_width="wrap_content" android:layout_height="fill_parent" app:layout_constraintBottom_toBottomOf="@id/input_phone" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="@id/input_phone" />
                <View android:id="@id/view" android:background="#ffb2b2b2" android:layout_width="0.5dip" android:layout_height="16.0dip" android:layout_marginStart="8.0dip" app:layout_constraintBottom_toBottomOf="@id/iv_phone_code" app:layout_constraintStart_toEndOf="@id/iv_phone_code" app:layout_constraintTop_toTopOf="@id/iv_phone_code" />
                <androidx.appcompat.widget.AppCompatEditText android:textSize="16.0sp" android:textColor="@color/payment_text" android:textColorHint="#ff767b85" android:ellipsize="end" android:gravity="start|center" android:id="@id/input_phone" android:background="@android:color/transparent" android:layout_width="0.0dip" android:layout_height="44.0dip" android:hint="@string/pay_phone_number" android:inputType="number" android:layout_marginStart="8.0dip" android:layout_marginEnd="8.0dip" app:layout_constraintEnd_toStartOf="@id/clear_phone_button" app:layout_constraintStart_toEndOf="@id/view" app:layout_constraintTop_toTopOf="parent" />
                <androidx.appcompat.widget.AppCompatImageView android:id="@id/clear_phone_button" android:visibility="gone" android:layout_width="16.0dip" android:layout_height="16.0dip" android:src="@mipmap/ic_clear" android:layout_marginEnd="8.0dip" app:layout_constraintBottom_toBottomOf="@id/input_phone" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="@id/input_phone" />
                <View android:id="@id/iv_phone_container_line" android:background="#ffe4e6eb" android:layout_width="fill_parent" android:layout_height="1.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/input_phone" />
                <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="#fff03930" android:id="@id/iv_input_phone_error" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="4.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/iv_phone_container_line" />
            </androidx.constraintlayout.widget.ConstraintLayout>
            <androidx.constraintlayout.widget.ConstraintLayout android:id="@id/iv_cnic_container" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="12.0dip">
                <androidx.appcompat.widget.AppCompatEditText android:textSize="16.0sp" android:textColor="@color/payment_text" android:textColorHint="#ff767b85" android:ellipsize="end" android:gravity="start|center" android:id="@id/input_cnic" android:background="@android:color/transparent" android:layout_width="0.0dip" android:layout_height="44.0dip" android:hint="@string/pay_cnic_number" android:inputType="number" android:layout_marginEnd="8.0dip" app:layout_constraintEnd_toStartOf="@id/clear_cnic_button" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
                <androidx.appcompat.widget.AppCompatImageView android:id="@id/clear_cnic_button" android:visibility="gone" android:layout_width="16.0dip" android:layout_height="16.0dip" android:src="@mipmap/ic_clear" android:layout_marginEnd="8.0dip" app:layout_constraintBottom_toBottomOf="@id/input_cnic" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="@id/input_cnic" />
                <View android:id="@id/iv_cnic_container_line" android:background="#ffe4e6eb" android:layout_width="fill_parent" android:layout_height="1.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/input_cnic" />
                <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="#fff03930" android:id="@id/iv_input_cnic_error" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="4.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/iv_cnic_container_line" />
            </androidx.constraintlayout.widget.ConstraintLayout>
            <androidx.appcompat.widget.AppCompatTextView android:enabled="false" android:textSize="16.0sp" android:textStyle="bold" android:textColor="#ffffffff" android:gravity="center" android:id="@id/iv_pay_button" android:background="@drawable/bg_pay_button" android:layout_width="fill_parent" android:layout_height="40.0dip" android:layout_marginTop="16.0dip" android:text="@string/pay_now" />
        </androidx.appcompat.widget.LinearLayoutCompat>
    </androidx.core.widget.NestedScrollView>
</androidx.appcompat.widget.LinearLayoutCompat>
