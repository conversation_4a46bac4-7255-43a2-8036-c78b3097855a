<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <View android:background="@color/white" android:layout_width="0.0dip" android:layout_height="0.0dip" app:layout_constraintBottom_toBottomOf="@id/ll_ignore" app:layout_constraintEnd_toEndOf="@id/updateTop" app:layout_constraintStart_toStartOf="@id/updateTop" app:layout_constraintTop_toBottomOf="@id/updateTop" />
    <View android:id="@id/updateTop" android:background="@mipmap/icon_update_top" android:layout_width="300.0dip" android:layout_height="174.0dip" android:layout_marginTop="20.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <TextView android:theme="@style/Update_Title" android:id="@id/updateTitleTv" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="20.0dip" android:text="@string/text_update_dialog_title" app:layout_constraintEnd_toStartOf="@id/updateVersionTv" app:layout_constraintHorizontal_chainStyle="packed" app:layout_constraintStart_toStartOf="@id/updateTop" app:layout_constraintTop_toBottomOf="@id/updateTop" />
    <TextView android:theme="@style/Update_Title" android:id="@id/updateVersionTv" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/text_update_version" android:layout_marginStart="3.0dip" app:layout_constraintBottom_toBottomOf="@id/updateTitleTv" app:layout_constraintEnd_toEndOf="@id/updateTop" app:layout_constraintStart_toEndOf="@id/updateTitleTv" app:layout_constraintTop_toTopOf="@id/updateTitleTv" />
    <TextView android:id="@id/currentVersionTv" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/text_update_current_version" android:maxLines="1" android:singleLine="true" android:layout_marginStart="10.0dip" app:layout_constraintBottom_toBottomOf="@id/updateVersionTv" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/updateVersionTv" app:layout_constraintTop_toTopOf="@id/updateVersionTv" />
    <com.cloud.tupdate.view.MaxHeightScrollView android:scrollbarThumbVertical="@drawable/scrollbar_ver_thumb" android:id="@id/contentScrollView" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginLeft="16.0dip" android:layout_marginTop="8.0dip" android:layout_marginRight="16.0dip" android:overScrollMode="never" android:layout_marginHorizontal="16.0dip" app:layout_constraintEnd_toEndOf="@id/updateTop" app:layout_constraintStart_toStartOf="@id/updateTop" app:layout_constraintTop_toBottomOf="@id/updateTitleTv" app:max_height="60.0dip">
        <TextView android:textSize="14.0sp" android:textColor="@color/base_color_666666" android:id="@id/updateContentTv" android:layout_width="fill_parent" android:layout_height="wrap_content" android:text="@string/text_update_content" />
    </com.cloud.tupdate.view.MaxHeightScrollView>
    <View android:id="@id/contentScrollViewTop" android:background="@drawable/update_bg_scrollview_top" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="33.0dip" android:layout_marginEnd="18.0dip" app:layout_constraintEnd_toEndOf="@id/contentScrollView" app:layout_constraintStart_toStartOf="@id/contentScrollView" app:layout_constraintTop_toTopOf="@id/contentScrollView" />
    <View android:id="@id/contentScrollViewBottom" android:background="@drawable/update_bg_scrollview_bottom" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="33.0dip" android:layout_marginEnd="18.0dip" app:layout_constraintBottom_toBottomOf="@id/contentScrollView" app:layout_constraintEnd_toEndOf="@id/contentScrollView" app:layout_constraintStart_toStartOf="@id/contentScrollView" />
    <LinearLayout android:orientation="horizontal" android:id="@id/ll_ignore" android:background="@color/white" android:paddingLeft="16.0dip" android:paddingRight="16.0dip" android:visibility="visible" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginTop="16.0dip" android:paddingHorizontal="16.0dip" app:layout_constraintEnd_toEndOf="@id/updateTop" app:layout_constraintStart_toStartOf="@id/updateTop" app:layout_constraintTop_toBottomOf="@id/contentScrollView">
        <androidx.appcompat.widget.AppCompatCheckBox android:gravity="center" android:layout_gravity="center_vertical" android:id="@id/updateIgnoreCb" android:background="@null" android:layout_width="15.0dip" android:layout_height="14.0dip" android:button="@drawable/update_checkbox_button" />
        <TextView android:textSize="14.0sp" android:textColor="@color/base_color_666666" android:id="@id/ignoreUpdateTips" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/text_update_ignore" android:layout_marginStart="4.0dip" />
    </LinearLayout>
    <LinearLayout android:gravity="center_horizontal" android:orientation="horizontal" android:id="@id/bottom_layout" android:background="@drawable/update_bottom_bg" android:paddingTop="16.0dip" android:paddingBottom="32.0dip" android:layout_width="0.0dip" android:layout_height="wrap_content" app:layout_constraintEnd_toEndOf="@id/updateTop" app:layout_constraintStart_toStartOf="@id/updateTop" app:layout_constraintTop_toBottomOf="@id/ll_ignore" style="@style/dialogBtnLayoutStyle">
        <View android:id="@id/updateBottomDivider" android:layout_width="0.0px" android:layout_height="fill_parent" />
        <androidx.appcompat.widget.AppCompatButton android:textSize="21.0sp" android:textColor="@color/base_color_white" android:gravity="center" android:id="@id/updatePositiveBtn" android:background="@drawable/update_button_shape" android:layout_width="200.0dip" android:layout_height="wrap_content" android:text="@string/text_update_button" android:textAllCaps="false" android:stateListAnimator="@null" />
    </LinearLayout>
    <View android:id="@id/guideline" android:layout_width="0.0dip" android:layout_height="0.0dip" android:layout_marginTop="15.0dip" app:layout_constraintEnd_toEndOf="@id/updateTop" app:layout_constraintStart_toStartOf="@id/updateTop" app:layout_constraintTop_toTopOf="@id/updateTop" />
    <androidx.appcompat.widget.AppCompatButton android:id="@id/updateNegativeBtn" android:background="@mipmap/icon_update_close" android:layout_width="24.0dip" android:layout_height="24.0dip" android:stateListAnimator="@null" app:layout_constraintBottom_toTopOf="@id/guideline" app:layout_constraintEnd_toEndOf="@id/updateTop" />
</androidx.constraintlayout.widget.ConstraintLayout>
