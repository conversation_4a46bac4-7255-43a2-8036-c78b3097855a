<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:fitsSystemWindows="true" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.tn.lib.view.TitleLayout android:id="@id/toolbar" android:layout_width="fill_parent" android:layout_height="wrap_content" app:layout_constraintTop_toTopOf="parent" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textColor="@color/text_01" android:id="@id/tv_title_desc" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="16.0dip" android:text="@string/user_setting_feedback_desc" android:layout_marginStart="16.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/toolbar" style="@style/robot_medium" />
    <androidx.appcompat.widget.AppCompatEditText android:textSize="14.0sp" android:textColor="@color/text_01" android:textColorHint="@color/text_03" android:gravity="start" android:layout_gravity="center_horizontal" android:id="@id/et_desc" android:background="@drawable/user_shape_setting_feedback_et" android:paddingTop="12.0dip" android:paddingBottom="64.0dip" android:layout_width="fill_parent" android:layout_height="240.0dip" android:layout_marginTop="8.0dip" android:hint="@string/user_setting_feedback_desc_tips" android:maxLength="500" android:lineSpacingExtra="5.0dip" android:paddingStart="12.0dip" android:paddingEnd="12.0dip" android:layout_marginStart="16.0dip" android:layout_marginEnd="16.0dip" app:layout_constraintTop_toBottomOf="@id/tv_title_desc" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/img_background" android:background="@drawable/bg_add_img" android:layout_width="48.0dip" android:layout_height="48.0dip" android:layout_marginBottom="12.0dip" android:scaleType="centerCrop" android:layout_marginStart="12.0dip" app:layout_constraintBottom_toBottomOf="@id/et_desc" app:layout_constraintStart_toStartOf="@id/et_desc" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_add_image" android:layout_width="wrap_content" android:layout_height="wrap_content" android:src="@mipmap/user_add_image" app:layout_constraintBottom_toBottomOf="@id/img_background" app:layout_constraintEnd_toEndOf="@id/img_background" app:layout_constraintStart_toStartOf="@id/img_background" app:layout_constraintTop_toTopOf="@id/img_background" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/text_03" android:gravity="center" android:id="@id/tv_desc_num" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginBottom="8.0dip" android:text="0/500" android:lineSpacingExtra="5.0dip" android:layout_marginStart="8.0dip" android:layout_marginEnd="8.0dip" app:layout_constraintBottom_toBottomOf="@id/et_desc" app:layout_constraintEnd_toEndOf="@id/et_desc" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textColor="@color/text_01" android:id="@id/whatsapp_hint" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginLeft="16.0dip" android:layout_marginTop="16.0dip" android:layout_marginRight="16.0dip" android:text="@string/input_whatsapp" android:layout_marginHorizontal="16.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/et_desc" style="@style/style_medium_small_text" />
    <LinearLayout android:gravity="center_vertical" android:orientation="horizontal" android:id="@id/ll_input" android:background="@drawable/user_shape_setting_feedback_et" android:paddingLeft="4.0dip" android:paddingRight="4.0dip" android:layout_width="fill_parent" android:layout_height="45.0dip" android:layout_marginLeft="16.0dip" android:layout_marginTop="12.0dip" android:layout_marginRight="16.0dip" android:layout_marginHorizontal="16.0dip" android:paddingHorizontal="4.0dip" app:layout_constraintStart_toStartOf="@id/whatsapp_hint" app:layout_constraintTop_toBottomOf="@id/whatsapp_hint">
        <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textColor="@color/text_01" android:gravity="center" android:id="@id/tv_phone_country_code" android:layout_width="wrap_content" android:layout_height="fill_parent" android:text="@string/login_select_country_code" android:singleLine="true" android:textDirection="ltr" style="@style/style_regular_text" />
        <androidx.appcompat.widget.AppCompatImageView android:layout_gravity="center_vertical" android:layout_width="wrap_content" android:layout_height="wrap_content" android:src="@mipmap/login_arrow_down" android:tint="@color/text_01" android:layout_marginStart="7.0dip" android:layout_marginEnd="7.0dip" />
        <View android:layout_gravity="center_vertical" android:background="@color/line_01" android:layout_width="1.0dip" android:layout_height="23.0dip" />
        <androidx.appcompat.widget.AppCompatEditText android:textSize="16.0sp" android:textColor="@color/text_01" android:textColorHint="@color/text_05" android:gravity="start|center" android:id="@id/et_phone" android:background="@null" android:focusable="true" android:layout_width="0.0dip" android:layout_height="fill_parent" android:hint="@string/phone_number" android:singleLine="true" android:maxLength="18" android:digits="\ 0123456789" android:layout_weight="1.0" android:inputType="phone" android:textCursorDrawable="@drawable/post_cursor" android:textDirection="locale" android:textAlignment="viewStart" android:paddingStart="8.0dip" android:paddingEnd="20.0dip" />
    </LinearLayout>
    <androidx.appcompat.widget.AppCompatButton android:enabled="false" android:textSize="16.0sp" android:textColor="@color/libui_main_btn_text_color_selector" android:gravity="center" android:layout_gravity="center_horizontal" android:id="@id/btn_submit" android:background="@drawable/libui_main_btn_selector" android:layout_width="fill_parent" android:layout_height="40.0dip" android:layout_marginBottom="32.0dip" android:text="@string/user_setting_feedback_submit" android:textAllCaps="false" android:layout_marginStart="16.0dip" android:layout_marginEnd="16.0dip" app:layout_constraintBottom_toBottomOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>
