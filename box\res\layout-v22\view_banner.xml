<?xml version="1.0" encoding="utf-8"?>
<FrameLayout android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.google.android.material.imageview.ShapeableImageView android:id="@id/image" android:layout_width="fill_parent" android:layout_height="fill_parent" android:scaleType="centerCrop" app:shapeAppearanceOverlay="@style/roundStyle_8" />
    <View android:id="@id/shadow" android:background="@drawable/black_30p_to_0p" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="36.0dip" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textColor="@color/white" android:ellipsize="end" android:id="@id/title" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="8.0dip" android:maxLines="1" android:layout_marginStart="8.0dip" android:layout_marginEnd="8.0dip" style="@style/robot_medium" />
    <com.transsnet.downloader.widget.DownloadView android:layout_gravity="end|bottom" android:id="@id/download" android:background="@drawable/bg_btn_01" android:paddingLeft="10.0dip" android:paddingRight="10.0dip" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="28.0dip" android:layout_marginBottom="8.0dip" android:layout_marginEnd="8.0dip" android:paddingHorizontal="10.0dip" />
</FrameLayout>
