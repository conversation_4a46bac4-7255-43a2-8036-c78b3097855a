<?xml version="1.0" encoding="utf-8"?>
<androidx.appcompat.widget.AppCompatTextView android:textSize="@dimen/text_size_12" android:textColor="@color/text_01" android:gravity="center_vertical" android:layout_gravity="bottom" android:id="@id/open_network_tip" android:background="@color/warn_no_network" android:paddingLeft="12.0dip" android:paddingRight="12.0dip" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="52.0dip" android:text="@string/open_network_tip" android:drawablePadding="4.0dip" android:paddingHorizontal="12.0dip" app:drawableStartCompat="@mipmap/ic_open_network_warning"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto" />
