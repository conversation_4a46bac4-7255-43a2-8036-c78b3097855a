<?xml version="1.0" encoding="utf-8"?>
<merge android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <View android:id="@id/top_guideline" android:background="@color/bg_01" android:layout_width="0.0dip" android:layout_height="52.0dip" app:layout_constraintBottom_toTopOf="@id/bottom_guideline" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" />
    <com.noober.background.view.BLView android:id="@id/v_top_bg" android:layout_width="0.0dip" android:layout_height="40.0dip" android:layout_marginLeft="16.0dip" android:layout_marginRight="16.0dip" android:layout_marginHorizontal="16.0dip" app:bl_corners_radius="4.0dip" app:bl_stroke_color="@color/border" app:bl_stroke_width="2.0dip" app:layout_constraintBottom_toTopOf="@id/bottom_guideline" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_top_icon" android:layout_width="wrap_content" android:layout_height="wrap_content" android:tint="@color/text_01" android:layout_marginStart="12.0dip" app:layout_constraintBottom_toBottomOf="@id/v_top_bg" app:layout_constraintStart_toStartOf="@id/v_top_bg" app:layout_constraintTop_toTopOf="@id/v_top_bg" app:srcCompat="@mipmap/transfer_ic_link" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="14.0sp" android:textColor="@color/text_03" android:ellipsize="end" android:id="@id/tv_top_phone_model" android:layout_width="0.0dip" android:layout_height="wrap_content" android:maxLines="1" android:layout_marginStart="4.0dip" app:layout_constrainedWidth="true" app:layout_constraintBottom_toBottomOf="@id/v_top_bg" app:layout_constraintEnd_toStartOf="@id/tv_top_disconnect" app:layout_constraintStart_toEndOf="@id/iv_top_icon" app:layout_constraintTop_toTopOf="@id/v_top_bg" app:layout_goneMarginEnd="12.0dip" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="14.0sp" android:textColor="@color/text_01" android:id="@id/tv_top_disconnect" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginLeft="12.0dip" android:layout_marginRight="12.0dip" android:text="@string/download_transfer_btn_disconnect" android:maxLines="1" android:layout_marginHorizontal="12.0dip" app:layout_constraintBottom_toBottomOf="@id/v_top_bg" app:layout_constraintEnd_toEndOf="@id/v_top_bg" app:layout_constraintStart_toEndOf="@id/tv_top_phone_model" app:layout_constraintTop_toTopOf="@id/v_top_bg" />
    <androidx.constraintlayout.widget.Group android:id="@id/group_top_connect" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" app:constraint_referenced_ids="top_guideline,v_top_bg,         iv_top_icon,tv_top_phone_model" />
    <View android:id="@id/bottom_guideline" android:background="@color/bg_01" android:layout_width="0.0dip" android:layout_height="64.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_close" android:paddingLeft="8.0dip" android:paddingRight="8.0dip" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:scaleType="center" android:layout_marginStart="10.0dip" android:paddingHorizontal="8.0dip" app:layout_constraintBottom_toTopOf="@id/tv_close" app:layout_constraintStart_toStartOf="@id/bottom_guideline" app:layout_constraintTop_toTopOf="@id/bottom_guideline" app:layout_constraintVertical_chainStyle="packed" app:srcCompat="@drawable/ic_close_text_01" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/text_01" android:id="@id/tv_close" android:paddingLeft="8.0dip" android:paddingRight="8.0dip" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="2.0dip" android:text="@string/download_transfer_btn_close" android:paddingHorizontal="8.0dip" app:layout_constraintBottom_toBottomOf="@id/bottom_guideline" app:layout_constraintEnd_toEndOf="@id/iv_close" app:layout_constraintStart_toStartOf="@id/iv_close" app:layout_constraintTop_toBottomOf="@id/iv_close" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_send_list" android:paddingLeft="8.0dip" android:paddingRight="8.0dip" android:layout_width="wrap_content" android:layout_height="wrap_content" android:scaleType="center" android:paddingHorizontal="8.0dip" app:layout_constraintBottom_toBottomOf="@id/iv_close" app:layout_constraintEnd_toEndOf="@id/iv_close" app:layout_constraintStart_toStartOf="@id/iv_close" app:layout_constraintTop_toTopOf="@id/iv_close" app:srcCompat="@drawable/ic_transfer_send_none" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="10.0dip" android:textColor="@color/text_01" android:id="@id/tv_send_list_count" android:layout_width="wrap_content" android:layout_height="wrap_content" app:layout_constraintBottom_toBottomOf="@id/iv_close" app:layout_constraintEnd_toEndOf="@id/iv_close" app:layout_constraintStart_toStartOf="@id/iv_close" app:layout_constraintTop_toTopOf="@id/iv_close" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/text_01" android:id="@id/tv_send_list_tips" android:paddingLeft="8.0dip" android:paddingRight="8.0dip" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/download_transfer_btn_list" android:paddingHorizontal="8.0dip" app:layout_constraintBottom_toBottomOf="@id/tv_close" app:layout_constraintEnd_toEndOf="@id/tv_close" app:layout_constraintStart_toStartOf="@id/tv_close" app:layout_constraintTop_toTopOf="@id/tv_close" />
    <androidx.constraintlayout.widget.Group android:id="@id/group_list" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" app:constraint_referenced_ids="iv_send_list,tv_send_list_count         ,tv_send_list_tips" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_reset" android:paddingLeft="8.0dip" android:paddingRight="8.0dip" android:layout_width="wrap_content" android:layout_height="wrap_content" android:scaleType="center" android:layout_marginEnd="10.0dip" android:paddingHorizontal="8.0dip" app:layout_constraintBottom_toTopOf="@id/tv_reset" app:layout_constraintEnd_toEndOf="@id/bottom_guideline" app:layout_constraintTop_toTopOf="@id/bottom_guideline" app:layout_constraintVertical_chainStyle="packed" app:srcCompat="@drawable/ic_reset_text_01" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/text_01" android:id="@id/tv_reset" android:paddingLeft="8.0dip" android:paddingRight="8.0dip" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="2.0dip" android:text="@string/download_transfer_btn_reset" android:paddingHorizontal="8.0dip" app:layout_constraintBottom_toBottomOf="@id/bottom_guideline" app:layout_constraintEnd_toEndOf="@id/iv_reset" app:layout_constraintStart_toStartOf="@id/iv_reset" app:layout_constraintTop_toBottomOf="@id/iv_reset" />
    <View android:id="@id/v_send" android:background="@drawable/shape_download_group_button" android:layout_width="0.0dip" android:layout_height="40.0dip" android:layout_marginLeft="31.0dip" android:layout_marginRight="31.0dip" android:layout_marginHorizontal="31.0dip" app:layout_constraintBottom_toBottomOf="@id/bottom_guideline" app:layout_constraintEnd_toStartOf="@id/tv_reset" app:layout_constraintHorizontal_chainStyle="packed" app:layout_constraintStart_toEndOf="@id/iv_close" app:layout_constraintTop_toTopOf="@id/bottom_guideline" />
    <FrameLayout android:id="@id/fl_send" android:layout_width="wrap_content" android:layout_height="wrap_content" app:layout_constraintBottom_toBottomOf="@id/v_send" app:layout_constraintEnd_toEndOf="@id/v_send" app:layout_constraintStart_toStartOf="@id/v_send" app:layout_constraintTop_toTopOf="@id/v_send">
        <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textColor="@color/white" android:ellipsize="end" android:id="@id/tv_send" android:paddingLeft="10.0dip" android:paddingRight="10.0dip" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginLeft="16.0dip" android:layout_marginRight="16.0dip" android:text="@string/download_transfer_btn_send" android:lines="1" android:drawablePadding="4.0dip" android:layout_marginHorizontal="16.0dip" android:paddingHorizontal="10.0dip" style="@style/style_medium_text" />
        <ProgressBar android:layout_gravity="center" android:id="@id/pb_loading" android:visibility="gone" android:layout_width="23.0dip" android:layout_height="23.0dip" android:indeterminateTint="@color/white" app:layout_constraintBottom_toBottomOf="@id/v_send" app:layout_constraintEnd_toEndOf="@id/v_send" app:layout_constraintStart_toStartOf="@id/v_send" app:layout_constraintTop_toTopOf="@id/v_send" />
    </FrameLayout>
    <View android:id="@id/v_send_def" android:background="@drawable/shape_download_group_button" android:layout_width="0.0dip" android:layout_height="40.0dip" android:layout_marginStart="16.0dip" android:layout_marginEnd="8.0dip" app:layout_constraintBottom_toBottomOf="@id/bottom_guideline" app:layout_constraintEnd_toStartOf="@id/v_receive_def" app:layout_constraintStart_toStartOf="@id/bottom_guideline" app:layout_constraintTop_toTopOf="@id/bottom_guideline" app:layout_goneMarginEnd="8.0dip" app:layout_goneMarginStart="16.0dip" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textColor="@color/white" android:ellipsize="end" android:id="@id/tv_send_def" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginLeft="16.0dip" android:layout_marginRight="16.0dip" android:text="@string/download_transfer_btn_send" android:lines="1" android:drawablePadding="4.0dip" android:layout_marginHorizontal="16.0dip" app:drawableStartCompat="@mipmap/ic_transfer_btn_send" app:layout_constraintBottom_toBottomOf="@id/v_send_def" app:layout_constraintEnd_toEndOf="@id/v_send_def" app:layout_constraintStart_toStartOf="@id/v_send_def" app:layout_constraintTop_toTopOf="@id/v_send_def" style="@style/style_medium_text" />
    <View android:id="@id/v_receive_def" android:background="@drawable/shape_download_group_button" android:layout_width="0.0dip" android:layout_height="40.0dip" android:layout_marginStart="8.0dip" android:layout_marginEnd="16.0dip" app:layout_constraintBottom_toBottomOf="@id/bottom_guideline" app:layout_constraintEnd_toEndOf="@id/bottom_guideline" app:layout_constraintStart_toEndOf="@id/v_send_def" app:layout_constraintTop_toTopOf="@id/bottom_guideline" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textColor="@color/white" android:ellipsize="end" android:id="@id/tv_receive_def" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginLeft="16.0dip" android:layout_marginRight="16.0dip" android:text="@string/download_transfer_btn_receive" android:lines="1" android:drawablePadding="4.0dip" android:layout_marginHorizontal="16.0dip" app:drawableStartCompat="@mipmap/ic_transfer_btn_receive" app:layout_constraintBottom_toBottomOf="@id/v_receive_def" app:layout_constraintEnd_toEndOf="@id/v_receive_def" app:layout_constraintStart_toStartOf="@id/v_receive_def" app:layout_constraintTop_toTopOf="@id/v_receive_def" style="@style/style_medium_text" />
    <androidx.constraintlayout.widget.Group android:id="@id/group_send" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" app:constraint_referenced_ids="tv_reset,iv_reset,fl_send,v_send" />
    <androidx.constraintlayout.widget.Group android:id="@id/group_def" android:layout_width="wrap_content" android:layout_height="wrap_content" app:constraint_referenced_ids="v_send_def,tv_send_def,         v_receive_def,tv_receive_def" />
</merge>
