<?xml version="1.0" encoding="utf-8"?>
<merge android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textColor="@color/text_01" android:id="@id/tv_title" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/join_rooms_you_like" android:layout_marginStart="12.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" style="@style/style_import_text" />
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/text_02" android:ellipsize="end" android:id="@id/tvMore" android:paddingTop="8.0dip" android:paddingBottom="8.0dip" android:maxWidth="68.0dip" android:text="@string/str_more" android:maxLines="1" android:includeFontPadding="false" android:layout_marginEnd="@dimen/dimens_12" android:paddingVertical="8.0dip" app:drawableEndCompat="@mipmap/ic_arrow_right" app:drawableTint="@color/gray_0_60" app:layout_constraintBottom_toBottomOf="@id/tv_title" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="@id/tv_title" style="@style/style_import_text" />
    <androidx.recyclerview.widget.RecyclerView android:id="@id/recyclerView" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginLeft="8.0dip" android:layout_marginTop="8.0dip" android:layout_marginRight="8.0dip" android:layout_marginHorizontal="8.0dip" app:layout_constraintTop_toBottomOf="@id/tv_title" />
    <View android:id="@id/all_bg" android:background="@drawable/bg_trending_room_bottom" android:layout_width="fill_parent" android:layout_height="47.0dip" android:layout_marginLeft="12.0dip" android:layout_marginTop="8.0dip" android:layout_marginRight="12.0dip" android:layout_marginHorizontal="12.0dip" app:layout_constraintTop_toBottomOf="@id/recyclerView" />
    <TextView android:textSize="@dimen/text_size_14" android:textColor="@color/white" android:id="@id/tv_banner" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/explore" android:drawablePadding="2.0dip" android:drawableEnd="@mipmap/libui_ic_more" android:drawableTint="@color/white" app:layout_constraintBottom_toBottomOf="@id/all_bg" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" style="@style/style_medium_text" />
</merge>
