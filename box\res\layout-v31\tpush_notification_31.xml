<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <RelativeLayout android:layout_width="fill_parent" android:layout_height="wrap_content">
        <TextView android:id="@id/tpush_actionBtn" android:layout_width="wrap_content" android:layout_height="@dimen/tpush_notification_action_btn_height" android:includeFontPadding="false" android:layout_centerVertical="true" android:layout_alignParentEnd="true" style="@style/tpush_notification_button" />
        <LinearLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_alignParentTop="true" android:layout_marginEnd="10.0dip" android:layout_toStartOf="@id/tpush_actionBtn" android:layout_alignParentStart="true">
            <TextView android:id="@id/tpush_titleTv" android:layout_width="wrap_content" android:layout_height="wrap_content" android:includeFontPadding="false" style="@style/tpush_notification_large_title" />
            <TextView android:id="@id/tpush_descriptionTv" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="4.0dip" style="@style/tpush_notification_title" />
        </LinearLayout>
    </RelativeLayout>
</LinearLayout>
