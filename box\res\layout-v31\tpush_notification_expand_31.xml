<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <RelativeLayout android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="0.0dip">
        <ImageView android:id="@id/tpush_largeIconImg" android:layout_width="34.0dip" android:layout_height="34.0dip" android:scaleType="fitXY" android:layout_centerVertical="true" android:layout_marginEnd="0.0dip" />
        <TextView android:id="@id/tpush_actionBtn" android:layout_width="wrap_content" android:layout_height="@dimen/tpush_notification_action_btn_height" android:includeFontPadding="false" android:layout_centerVertical="true" style="@style/tpush_notification_button" />
        <TextView android:gravity="top" android:id="@id/tpush_titleTv" android:layout_width="wrap_content" android:layout_height="wrap_content" android:includeFontPadding="false" android:layout_alignTop="@id/tpush_largeIconImg" android:layout_marginStart="10.0dip" android:layout_marginEnd="10.0dip" android:layout_toStartOf="@id/tpush_actionBtn" android:layout_toEndOf="@id/tpush_largeIconImg" style="@style/tpush_notification_large_title" />
        <TextView android:id="@id/tpush_descriptionTv" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="2.0dip" android:maxLines="1" android:layout_below="@id/tpush_titleTv" android:layout_marginStart="10.0dip" android:layout_marginEnd="10.0dip" android:layout_toStartOf="@id/tpush_actionBtn" android:layout_toEndOf="@id/tpush_largeIconImg" style="@style/tpush_notification_title" />
    </RelativeLayout>
</LinearLayout>
