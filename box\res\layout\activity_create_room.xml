<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView android:layout_width="fill_parent" android:layout_height="fill_parent" android:fillViewport="true"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="fill_parent">
        <com.tn.lib.view.TitleLayout android:id="@id/tool_bar" android:layout_width="fill_parent" android:layout_height="wrap_content" app:isShowBack="true" app:layout_constraintTop_toTopOf="parent" app:titleText="@string/create_room_title" />
        <com.google.android.material.imageview.ShapeableImageView android:id="@id/iv_avatar" android:layout_width="96.0dip" android:layout_height="96.0dip" android:layout_marginTop="32.0dip" android:src="@drawable/ic_avatar_default" android:scaleType="centerCrop" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tool_bar" app:shapeAppearance="@style/circle_style" />
        <androidx.appcompat.widget.AppCompatImageView android:layout_width="24.0dip" android:layout_height="24.0dip" android:layout_marginTop="32.0dip" android:src="@drawable/ic_change_avatar" app:layout_constraintBottom_toBottomOf="@id/iv_avatar" app:layout_constraintEnd_toEndOf="@id/iv_avatar" />
        <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/text_01" android:id="@id/tv_name" android:layout_marginTop="32.0dip" android:text="@string/community_name" android:layout_marginStart="16.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/iv_avatar" style="@style/style_title_text" />
        <androidx.appcompat.widget.AppCompatEditText android:textSize="14.0sp" android:textColor="@color/text_02" android:textColorHint="@color/text_05" android:gravity="start|center" android:id="@id/et_community_name" android:background="@drawable/bg_edit_border" android:padding="12.0dip" android:focusable="true" android:layout_width="fill_parent" android:layout_height="40.0dip" android:layout_marginTop="8.0dip" android:hint="@string/community_name" android:singleLine="true" android:maxLength="50" android:textDirection="locale" android:layout_marginStart="16.0dip" android:layout_marginEnd="16.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tv_name" />
        <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/text_01" android:id="@id/tv_desc" android:layout_marginTop="32.0dip" android:text="@string/community_profile" android:layout_marginStart="16.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/et_community_name" style="@style/style_title_text" />
        <androidx.appcompat.widget.AppCompatEditText android:textSize="14.0sp" android:textColor="@color/text_02" android:textColorHint="@color/text_05" android:gravity="start|top" android:id="@id/et_community_desc" android:background="@drawable/bg_edit_border" android:paddingTop="12.0dip" android:paddingBottom="30.0dip" android:focusable="true" android:layout_width="fill_parent" android:layout_height="189.0dip" android:layout_marginTop="8.0dip" android:hint="@string/community_profile_hint" android:maxLength="1000" android:textDirection="locale" android:paddingStart="12.0dip" android:paddingEnd="12.0dip" android:layout_marginStart="16.0dip" android:layout_marginEnd="16.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tv_desc" />
        <androidx.appcompat.widget.AppCompatTextView android:enabled="false" android:textColor="@color/libui_main_btn_text_color_selector" android:gravity="center" android:id="@id/tv_comfirm" android:background="@drawable/bg_comfirm" android:layout_width="204.0dip" android:layout_height="36.0dip" android:layout_marginBottom="96.0dip" android:text="@string/str_comfirm" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" style="@style/style_title_text" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.core.widget.NestedScrollView>
