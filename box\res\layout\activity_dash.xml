<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:background="#ff000000" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <TextureView android:id="@id/tv" android:layout_width="fill_parent" android:layout_height="fill_parent" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <ImageView android:id="@id/iv" android:layout_width="fill_parent" android:layout_height="fill_parent" />
    <Button android:textColor="#ff000000" android:gravity="center" android:id="@id/tv_index" android:layout_width="fill_parent" android:layout_height="50.0dip" android:text="切换清晰度" app:layout_constraintBottom_toBottomOf="parent" />
    <Button android:textColor="#ff000000" android:gravity="center" android:id="@id/tv_seek" android:layout_width="fill_parent" android:layout_height="50.0dip" android:layout_marginBottom="80.0dip" android:text="快进10s" app:layout_constraintBottom_toBottomOf="parent" />
    <Button android:textColor="#ff000000" android:gravity="center" android:id="@id/tv_speed" android:layout_width="fill_parent" android:layout_height="50.0dip" android:layout_marginBottom="150.0dip" android:text="设置倍速" app:layout_constraintBottom_toBottomOf="parent" />
    <LinearLayout android:orientation="vertical" android:id="@id/ll_index" android:layout_width="100.0dip" android:layout_height="wrap_content" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>
