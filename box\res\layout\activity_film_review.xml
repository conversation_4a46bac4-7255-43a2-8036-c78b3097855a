<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout android:fitsSystemWindows="true" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.transsion.publish.view.CustomPublishHeader android:id="@id/sv_title_bar" android:layout_width="fill_parent" android:layout_height="54.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <FrameLayout android:id="@id/fl_content" android:layout_width="fill_parent" android:layout_height="fill_parent" android:layout_below="@id/sv_title_bar" />
</RelativeLayout>
