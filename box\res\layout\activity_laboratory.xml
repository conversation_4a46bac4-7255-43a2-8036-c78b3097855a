<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:orientation="vertical" android:background="@color/bg_01" android:fitsSystemWindows="true" android:layout_width="fill_parent" android:layout_height="fill_parent" android:divider="@drawable/shape_lab_divider_line_all" android:showDividers="middle"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <LinearLayout android:gravity="center_vertical" android:layout_width="fill_parent" android:layout_height="50.0dip">
        <androidx.appcompat.widget.AppCompatImageButton android:id="@id/btn_back" android:background="@null" android:padding="10.0dip" android:layout_width="44.0dip" android:layout_height="wrap_content" android:src="@mipmap/base_back_black" android:scaleType="centerInside" android:tint="@color/text_01" android:layout_marginStart="6.0dip" />
        <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textColor="@color/text_01" android:gravity="center" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/laboratory" android:layout_weight="1.0" android:layout_marginEnd="50.0dip" style="@style/style_extra_import_text" />
    </LinearLayout>
    <androidx.core.widget.NestedScrollView android:layout_width="fill_parent" android:layout_height="fill_parent">
        <LinearLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="fill_parent" android:divider="@drawable/shape_lab_divider_line" android:showDividers="middle">
            <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/text_02" android:gravity="center_vertical" android:id="@id/tv_lane" android:background="?selectableItemBackground" android:layout_width="fill_parent" android:layout_height="50.0dip" android:layout_marginTop="16.0dip" android:text="@string/lane" android:drawablePadding="10.0dip" android:paddingStart="15.0dip" android:paddingEnd="15.0dip" app:drawableEndCompat="@drawable/ic_group_more_png" app:drawableStartCompat="@drawable/ic_lab" style="@style/style_import_text" />
            <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/text_02" android:gravity="center_vertical" android:id="@id/tv_select_brand" android:background="?selectableItemBackground" android:layout_width="fill_parent" android:layout_height="50.0dip" android:text="select_brand" android:drawablePadding="10.0dip" android:paddingStart="15.0dip" android:paddingEnd="15.0dip" app:drawableEndCompat="@drawable/ic_group_more_png" app:drawableStartCompat="@drawable/ic_lab" style="@style/style_import_text" />
            <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/text_02" android:gravity="center_vertical" android:id="@id/tv_app_info" android:background="?selectableItemBackground" android:layout_width="fill_parent" android:layout_height="50.0dip" android:text="@string/device_or_user_information" android:drawablePadding="10.0dip" android:paddingStart="15.0dip" android:paddingEnd="15.0dip" app:drawableEndCompat="@drawable/ic_group_more_png" app:drawableStartCompat="@drawable/ic_lab" style="@style/style_import_text" />
            <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/text_02" android:gravity="center_vertical" android:id="@id/tvIso" android:background="?selectableItemBackground" android:layout_width="fill_parent" android:layout_height="50.0dip" android:text="National information" android:drawablePadding="10.0dip" android:paddingStart="15.0dip" android:paddingEnd="15.0dip" app:drawableEndCompat="@drawable/ic_group_more_png" app:drawableStartCompat="@drawable/ic_lab" style="@style/style_import_text" />
            <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/text_02" android:gravity="center_vertical" android:id="@id/tvChannel" android:background="?selectableItemBackground" android:layout_width="fill_parent" android:layout_height="50.0dip" android:text="Channel" android:drawablePadding="10.0dip" android:paddingStart="15.0dip" android:paddingEnd="15.0dip" app:drawableEndCompat="@drawable/ic_group_more_png" app:drawableStartCompat="@drawable/ic_lab" style="@style/style_import_text" />
            <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/text_02" android:gravity="center_vertical" android:id="@id/tvStreaming" android:background="?selectableItemBackground" android:layout_width="fill_parent" android:layout_height="50.0dip" android:text="Streaming" android:drawablePadding="10.0dip" android:paddingStart="15.0dip" android:paddingEnd="15.0dip" app:drawableEndCompat="@drawable/ic_group_more_png" app:drawableStartCompat="@drawable/ic_lab" style="@style/style_import_text" />
            <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/text_01" android:gravity="center_vertical" android:id="@id/tvDebugJS" android:layout_width="fill_parent" android:layout_height="50.0dip" android:text="DebugJS" android:drawablePadding="10.0dip" android:paddingStart="15.0dip" android:paddingEnd="15.0dip" app:drawableEndCompat="@drawable/ic_group_more_png" app:drawableStartCompat="@drawable/ic_lab" style="@style/style_import_text" />
            <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/text_01" android:gravity="center_vertical" android:id="@id/tvWebTest" android:layout_width="fill_parent" android:layout_height="50.0dip" android:text="WebTest" android:drawablePadding="10.0dip" android:paddingStart="15.0dip" android:paddingEnd="15.0dip" app:drawableEndCompat="@drawable/ic_group_more_png" app:drawableStartCompat="@drawable/ic_lab" style="@style/style_import_text" />
            <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/text_01" android:gravity="center_vertical" android:id="@id/tvHttpHost" android:layout_width="fill_parent" android:layout_height="50.0dip" android:text="HttpHost" android:drawablePadding="10.0dip" android:paddingStart="15.0dip" android:paddingEnd="15.0dip" app:drawableEndCompat="@drawable/ic_group_more_png" app:drawableStartCompat="@drawable/ic_lab" style="@style/style_import_text" />
            <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/text_01" android:gravity="center_vertical" android:id="@id/tvNotification" android:layout_width="fill_parent" android:layout_height="50.0dip" android:text="Notification-Test" android:drawablePadding="10.0dip" android:paddingStart="15.0dip" android:paddingEnd="15.0dip" app:drawableEndCompat="@drawable/ic_group_more_png" app:drawableStartCompat="@drawable/ic_lab" style="@style/style_import_text" />
            <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/text_01" android:gravity="center_vertical" android:id="@id/tv_location_mock" android:layout_width="fill_parent" android:layout_height="50.0dip" android:text="LocationMock-Test" android:drawablePadding="10.0dip" android:paddingStart="15.0dip" android:paddingEnd="15.0dip" app:drawableEndCompat="@drawable/ic_group_more_png" app:drawableStartCompat="@drawable/ic_lab" style="@style/style_import_text" />
            <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/text_01" android:gravity="center_vertical" android:id="@id/tvKillApp" android:background="@color/brand" android:layout_width="fill_parent" android:layout_height="50.0dip" android:text="Restart The APP" android:drawablePadding="10.0dip" android:paddingStart="15.0dip" android:paddingEnd="15.0dip" app:drawableEndCompat="@drawable/ic_group_more_png" app:drawableStartCompat="@drawable/ic_lab" style="@style/style_import_text" />
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
</LinearLayout>
