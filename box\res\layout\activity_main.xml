<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:orientation="vertical" android:background="@color/module_01" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <FrameLayout android:id="@id/container" android:layout_width="fill_parent" android:layout_height="0.0dip" android:layout_weight="1.0" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintTop_toTopOf="parent" app:layout_constraintVertical_weight="1.0" />
    <ImageView android:id="@id/tabBg" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="56.0dip" android:src="@drawable/bg_main_tab_bottom" android:scaleType="fitXY" android:layoutDirection="ltr" app:layout_constraintBottom_toBottomOf="@id/tab_bottom" />
    <View android:id="@id/leftBg" android:background="@mipmap/bg_bottom_left" android:layout_width="8.0dip" android:layout_height="56.0dip" android:layoutDirection="ltr" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toStartOf="@id/leftTwoBg" app:layout_constraintHorizontal_chainStyle="packed" app:layout_constraintStart_toStartOf="parent" />
    <ImageView android:id="@id/leftTwoBg" android:layout_width="0.0dip" android:layout_height="56.0dip" android:src="@mipmap/bg_bottom_noun" android:scaleType="fitXY" android:layoutDirection="ltr" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toStartOf="@id/centerBg" app:layout_constraintHorizontal_weight="1.0" app:layout_constraintStart_toEndOf="@id/leftBg" />
    <View android:id="@id/centerBg" android:background="@mipmap/bg_bottom_center" android:layout_width="82.0dip" android:layout_height="56.0dip" android:layoutDirection="ltr" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toStartOf="@id/rightTwoBg" app:layout_constraintStart_toEndOf="@id/leftTwoBg" />
    <ImageView android:id="@id/rightTwoBg" android:layout_width="0.0dip" android:layout_height="56.0dip" android:src="@mipmap/bg_bottom_noun" android:scaleType="fitXY" android:layoutDirection="ltr" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toStartOf="@id/rightBg" app:layout_constraintHorizontal_weight="1.0" app:layout_constraintStart_toEndOf="@id/centerBg" />
    <View android:id="@id/rightBg" android:background="@mipmap/bg_bottom_right" android:layout_width="8.0dip" android:layout_height="56.0dip" android:layoutDirection="ltr" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" />
    <androidx.constraintlayout.widget.Group android:id="@id/largeBottomBg" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" app:constraint_referenced_ids="rightBg,rightTwoBg,centerBg,leftBg,leftTwoBg" />
    <com.google.android.material.tabs.TabLayout android:id="@id/tab_bottom" android:background="@color/transparent" android:layout_width="fill_parent" android:layout_height="80.0dip" app:layout_constraintBottom_toBottomOf="parent" app:tabBackground="@color/transparent" app:tabIndicatorHeight="0.0dip" app:tabMode="fixed" app:tabPaddingEnd="0.0dip" app:tabPaddingStart="0.0dip" app:tabRippleColor="@android:color/transparent" />
</androidx.constraintlayout.widget.ConstraintLayout>
