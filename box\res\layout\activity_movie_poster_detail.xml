<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.constraintlayout.widget.ConstraintLayout android:id="@id/topLayout" android:layout_width="fill_parent" android:layout_height="wrap_content" app:layout_constraintTop_toTopOf="parent">
        <androidx.appcompat.widget.AppCompatImageView android:id="@id/ivCover" android:background="@color/movie_staff_avatar_bg" android:layout_width="fill_parent" android:layout_height="0.0dip" android:scaleType="centerCrop" app:layout_constraintDimensionRatio="h,360:480" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
        <View android:background="@drawable/bg_movie_detail_toolbar" android:layout_width="fill_parent" android:layout_height="88.0dip" app:layout_constraintTop_toTopOf="parent" />
        <View android:background="@drawable/bg_movie_staff_name" android:layout_width="fill_parent" android:layout_height="144.0dip" app:layout_constraintBottom_toBottomOf="@id/ivCover" />
        <androidx.appcompat.widget.AppCompatImageView android:id="@id/ivBack" android:padding="@dimen/dp_12" android:layout_width="48.0dip" android:layout_height="48.0dip" android:src="@mipmap/icon_white_back" android:scaleType="centerCrop" android:layout_marginStart="@dimen/dp_4" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
        <androidx.appcompat.widget.AppCompatImageView android:id="@id/ivDownload" android:layout_width="@dimen/dimens_24" android:layout_height="@dimen/dimens_24" android:src="@drawable/ic_image_download" android:tint="@color/color_download_selector" android:layout_marginEnd="@dimen/dp_16" app:layout_constraintBottom_toBottomOf="@id/ivBack" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="@id/ivBack" />
        <androidx.appcompat.widget.AppCompatImageView android:id="@id/ivShare" android:layout_width="24.0dip" android:layout_height="24.0dip" android:src="@drawable/ic_movie_share" android:tint="@color/color_download_selector" android:layout_marginEnd="16.0dip" app:layout_constraintBottom_toBottomOf="@id/ivBack" app:layout_constraintEnd_toStartOf="@id/ivDownload" app:layout_constraintTop_toTopOf="@id/ivBack" />
        <androidx.appcompat.widget.AppCompatTextView android:textSize="18.0sp" android:textColor="@color/white" android:ellipsize="end" android:gravity="start" android:id="@id/tvMovieName" android:layout_width="0.0dip" android:layout_height="wrap_content" android:maxLines="1" android:singleLine="true" android:shadowColor="@color/black_30" android:shadowDx="0.0" android:shadowDy="1.0" android:shadowRadius="3.0" android:layout_marginStart="16.0dip" android:layout_marginEnd="32.0dip" app:layout_constraintBottom_toTopOf="@id/tvMovieDesc" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintHorizontal_chainStyle="spread_inside" app:layout_constraintStart_toStartOf="parent" style="@style/style_import_text" />
        <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/white_60" android:ellipsize="end" android:id="@id/tvMovieDesc" android:layout_width="0.0dip" android:layout_marginTop="4.0dip" android:layout_marginBottom="40.0dip" android:maxLines="2" android:lineSpacingExtra="2.0dip" android:layout_marginStart="16.0dip" android:layout_marginEnd="32.0dip" app:layout_constraintBottom_toBottomOf="@id/ivCover" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" style="@style/style_regular_text" />
        <View android:background="@drawable/bg_staff_drawing_top_16dp" android:layout_width="fill_parent" android:layout_height="16.0dip" app:layout_constraintBottom_toBottomOf="@id/ivCover" />
    </androidx.constraintlayout.widget.ConstraintLayout>
    <androidx.core.widget.NestedScrollView android:id="@id/scrollView" android:layout_width="fill_parent" android:layout_height="wrap_content" app:layout_constraintTop_toBottomOf="@id/topLayout">
        <LinearLayout android:orientation="vertical" android:id="@id/llContent" android:layout_width="fill_parent" android:layout_height="wrap_content">
            <TextView android:textSize="18.0sp" android:textStyle="bold" android:textColor="@color/text_01" android:id="@id/tvStaffTitle" android:background="@color/movie_staff_bg" android:paddingTop="12.0dip" android:paddingBottom="12.0dip" android:layout_width="fill_parent" android:layout_height="wrap_content" android:text="@string/starring" android:paddingStart="16.0dip" android:paddingEnd="16.0dip" />
            <androidx.recyclerview.widget.RecyclerView android:id="@id/rvStaff" android:layout_width="fill_parent" android:layout_height="fill_parent" />
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
</androidx.constraintlayout.widget.ConstraintLayout>
