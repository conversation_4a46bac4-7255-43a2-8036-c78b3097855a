<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:id="@id/rlRoot" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <TextureView android:id="@id/textureView" android:layout_width="0.0dip" android:layout_height="0.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <androidx.appcompat.widget.LinearLayoutCompat android:orientation="horizontal" android:id="@id/viewCd" android:layout_width="fill_parent" android:layout_height="28.0dip" android:layout_marginLeft="16.0dip" android:layout_marginTop="24.0dip" android:layout_marginRight="16.0dip" app:layout_constraintEnd_toEndOf="@id/textureView" app:layout_constraintTop_toTopOf="@id/textureView">
        <androidx.appcompat.widget.AppCompatImageView android:id="@id/ivVolume" android:layout_width="28.0dip" android:layout_height="28.0dip" android:src="@mipmap/ad_video_04" />
        <View android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_weight="1.0" />
        <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/white" android:gravity="center_vertical" android:id="@id/tvCd" android:background="@drawable/ad_shape_btn_04_bg" android:paddingLeft="12.0dip" android:paddingRight="12.0dip" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="fill_parent" app:layout_constraintBottom_toBottomOf="@id/viewCd" app:layout_constraintStart_toStartOf="@id/viewCd" app:layout_constraintTop_toTopOf="@id/viewCd" />
        <androidx.appcompat.widget.AppCompatImageView android:gravity="center_vertical" android:id="@id/ivClose" android:layout_width="28.0dip" android:layout_height="28.0dip" android:src="@mipmap/ad_video_03" android:layout_marginStart="18.0dip" />
    </androidx.appcompat.widget.LinearLayoutCompat>
    <androidx.constraintlayout.widget.ConstraintLayout android:id="@id/clAdInfo" android:background="@drawable/ad_shape_btn_08_bg" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginLeft="16.0dip" android:layout_marginRight="16.0dip" android:layout_marginBottom="24.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent">
        <com.google.android.material.imageview.ShapeableImageView android:id="@id/tvAvatar" android:layout_width="48.0dip" android:layout_height="48.0dip" android:layout_marginTop="12.0dip" android:layout_marginBottom="12.0dip" android:layout_marginStart="12.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:shapeAppearance="@style/corner_style_8" />
        <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textColor="#ff191f2b" android:ellipsize="end" android:id="@id/tvTitle" android:layout_width="0.0dip" android:layout_height="wrap_content" android:maxLines="1" android:layout_marginStart="12.0dip" android:layout_marginEnd="12.0dip" app:layout_constraintBottom_toTopOf="@id/tvDesc" app:layout_constraintEnd_toStartOf="@id/tvBtn" app:layout_constraintStart_toEndOf="@id/tvAvatar" app:layout_constraintTop_toTopOf="@id/tvAvatar" />
        <androidx.appcompat.widget.AppCompatTextView android:textColor="#ff61656d" android:ellipsize="end" android:id="@id/tvDesc" android:layout_width="0.0dip" android:layout_height="wrap_content" android:maxLines="1" app:layout_constraintBottom_toBottomOf="@id/tvAvatar" app:layout_constraintEnd_toEndOf="@id/tvTitle" app:layout_constraintStart_toStartOf="@id/tvTitle" app:layout_constraintTop_toBottomOf="@id/tvTitle" />
        <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/white" android:ellipsize="end" android:gravity="center" android:id="@id/tvBtn" android:background="@drawable/ad_shape_btn_bg" android:paddingLeft="16.0dip" android:paddingRight="16.0dip" android:layout_width="wrap_content" android:layout_height="32.0dip" android:maxLines="1" android:layout_marginStart="12.0dip" android:layout_marginEnd="12.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
    <androidx.constraintlayout.widget.ConstraintLayout android:orientation="vertical" android:id="@id/clAdEndLayout" android:background="@color/black_80" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="fill_parent">
        <androidx.appcompat.widget.AppCompatImageView android:gravity="center_vertical" android:layout_gravity="end" android:id="@id/ivAdClose" android:layout_width="28.0dip" android:layout_height="28.0dip" android:layout_marginTop="24.0dip" android:src="@mipmap/ad_video_01" android:layout_marginStart="18.0dip" android:layout_marginEnd="16.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent" />
        <com.google.android.material.imageview.ShapeableImageView android:id="@id/tvAdAvatar" android:layout_width="80.0dip" android:layout_height="80.0dip" app:layout_constraintBottom_toTopOf="@id/tvAdTitle" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:layout_constraintVertical_chainStyle="packed" app:shapeAppearance="@style/corner_style_20" />
        <androidx.appcompat.widget.AppCompatTextView android:textSize="24.0sp" android:textColor="@color/white" android:ellipsize="end" android:gravity="center" android:id="@id/tvAdTitle" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginLeft="20.0dip" android:layout_marginTop="20.0dip" android:layout_marginRight="20.0dip" android:maxLines="1" app:layout_constraintBottom_toTopOf="@id/tvAdDesc" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tvAdAvatar" />
        <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textColor="@color/white_80" android:ellipsize="end" android:gravity="center" android:id="@id/tvAdDesc" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginLeft="20.0dip" android:layout_marginTop="8.0dip" android:layout_marginRight="20.0dip" android:maxLines="1" app:layout_constraintBottom_toTopOf="@id/tvAdBtn" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tvAdTitle" />
        <androidx.appcompat.widget.AppCompatTextView android:textSize="18.0sp" android:textColor="@color/white" android:ellipsize="end" android:gravity="center" android:id="@id/tvAdBtn" android:background="@drawable/ad_shape_btn_09_bg" android:paddingLeft="16.0dip" android:paddingRight="16.0dip" android:layout_width="160.0dip" android:layout_height="44.0dip" android:layout_marginTop="24.0dip" android:maxLines="1" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tvAdDesc" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
