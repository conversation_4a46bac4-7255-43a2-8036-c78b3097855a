<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.tn.lib.view.TitleLayout android:id="@id/toolBar" android:layout_width="fill_parent" android:layout_height="48.0dip" app:isShowBack="true" app:layout_constraintBottom_toTopOf="@id/ivHeader" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:titleText="Preparations" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/ivHeader" android:layout_width="fill_parent" android:layout_height="160.0dip" android:layout_marginLeft="16.0dip" android:layout_marginTop="16.0dip" android:layout_marginRight="16.0dip" android:src="@mipmap/bg_permission_request" app:layout_constraintTop_toBottomOf="@id/toolBar" />
    <androidx.recyclerview.widget.RecyclerView android:id="@id/rv_permissions" android:layout_width="fill_parent" android:layout_height="fill_parent" android:layout_marginTop="24.0dip" app:layout_constrainedHeight="true" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintBottom_toTopOf="@id/tvNext" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/ivHeader" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="19.0sp" android:textColor="@color/white_100" android:gravity="center" android:id="@id/tvNext" android:background="@drawable/bg_btn_01" android:clickable="false" android:layout_width="fill_parent" android:layout_height="40.0dip" android:layout_marginLeft="16.0dip" android:layout_marginRight="16.0dip" android:layout_marginBottom="24.0dip" android:text="@string/transfer_wifi_create_next" android:alpha="0.3" app:layout_constraintBottom_toBottomOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>
