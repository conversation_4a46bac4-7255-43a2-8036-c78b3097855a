<?xml version="1.0" encoding="utf-8"?>
<androidx.appcompat.widget.LinearLayoutCompat android:orientation="vertical" android:background="@color/gray_dark_00" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <include android:id="@id/titleLayout" layout="@layout/layout_title_dark" />
    <LinearLayout android:gravity="center_vertical" android:orientation="horizontal" android:background="@drawable/bg_sku_12_radius" android:layout_width="fill_parent" android:layout_height="@dimen/dimens_48" android:layout_marginTop="@dimen/dp_12" android:layout_marginStart="@dimen/dp_16" android:layout_marginEnd="@dimen/dp_16">
        <TextView android:textSize="@dimen/dp_16" android:textStyle="bold" android:textColor="@color/white" android:layout_width="0.0dip" android:layout_height="wrap_content" android:text="@string/points_current_points" android:includeFontPadding="false" android:layout_weight="1.0" android:layout_marginStart="@dimen/dp_12" />
        <androidx.appcompat.widget.AppCompatTextView android:textSize="@dimen/dp_20" android:textStyle="bold" android:textColor="@color/white" android:id="@id/tvTotalCoin" android:layout_width="wrap_content" android:layout_height="wrap_content" android:drawablePadding="@dimen/dp_2" android:layout_marginEnd="@dimen/dp_12" app:drawableStartCompat="@mipmap/ic_points" />
    </LinearLayout>
    <FrameLayout android:orientation="vertical" android:id="@id/container" android:layout_width="fill_parent" android:layout_height="0.0dip" android:layout_marginTop="@dimen/dp_10" android:layout_weight="1.0" />
</androidx.appcompat.widget.LinearLayoutCompat>
