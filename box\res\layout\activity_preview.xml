<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:id="@id/rootView" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <View android:id="@id/statusSpace" android:background="@color/black" android:layout_width="fill_parent" android:layout_height="@dimen/default_status_bar_height" app:layout_constraintTop_toTopOf="parent" />
    <com.transsion.ninegridview.preview.HackyViewPager android:id="@id/viewPager" android:layout_width="fill_parent" android:layout_height="fill_parent" />
    <ImageView android:id="@id/iv_back" android:paddingTop="@dimen/dp_12" android:paddingBottom="@dimen/dp_12" android:layout_width="@dimen/dp_56" android:layout_height="48.0dip" android:src="@mipmap/icon_white_back" android:scaleType="fitCenter" android:paddingStart="@dimen/dp_16" android:paddingEnd="@dimen/dp_16" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/statusSpace" />
    <TextView android:textColor="@color/cl38" android:id="@id/tv_pager" android:layout_marginTop="48.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" style="@style/style_regula_bigger_text" />
    <View android:id="@id/bg_share" android:background="@drawable/bg_white_r8_light" android:layout_width="0.0dip" android:layout_height="36.0dip" android:layout_marginBottom="40.0dip" android:layout_marginStart="@dimen/dp_16" android:layout_marginEnd="@dimen/dp_8" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toStartOf="@id/bg_download" app:layout_constraintStart_toStartOf="parent" />
    <View android:id="@id/bg_download" android:background="@drawable/ad_shape_btn_bg" android:layout_width="0.0dip" android:layout_height="36.0dip" android:layout_marginBottom="40.0dip" android:layout_marginEnd="@dimen/dp_16" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/bg_share" />
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/gray_light_80" android:gravity="center" android:id="@id/tv_share" android:layout_width="wrap_content" android:layout_height="36.0dip" android:text="@string/want_to_see_share" android:drawablePadding="@dimen/dp_4" android:drawableStart="@drawable/ic_movie_share" app:drawableTint="@color/gray_light_80" app:layout_constraintBottom_toBottomOf="@id/bg_share" app:layout_constraintEnd_toEndOf="@id/bg_share" app:layout_constraintStart_toStartOf="@id/bg_share" />
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/white" android:gravity="center" android:id="@id/tv_download" android:layout_width="wrap_content" android:layout_height="36.0dip" android:text="Save" android:drawablePadding="@dimen/dp_4" android:drawableStart="@drawable/ic_image_download" app:layout_constraintBottom_toBottomOf="@id/bg_download" app:layout_constraintEnd_toEndOf="@id/bg_download" app:layout_constraintStart_toStartOf="@id/bg_download" />
</androidx.constraintlayout.widget.ConstraintLayout>
