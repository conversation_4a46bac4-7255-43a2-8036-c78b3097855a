<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.tn.lib.view.TitleLayout android:id="@id/titleLayout" android:layout_width="fill_parent" android:layout_height="wrap_content" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:titleText="@string/profile_setting_about_us" />
    <ImageView android:id="@id/ivLogo" android:layout_width="96.0dip" android:layout_height="96.0dip" android:layout_marginTop="87.0dip" android:src="@mipmap/ic_launcher" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/titleLayout" />
    <TextView android:textSize="20.0sp" android:textColor="@color/base_color_333333" android:id="@id/tvAppName" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="20.0dip" android:text="@string/profile_setting_about_us_app_name" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/ivLogo" />
    <TextView android:textSize="16.0sp" android:textColor="@color/base_color_8D8D8D" android:id="@id/tvAppVersion" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="16.0dip" android:text="V 1.0" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tvAppName" />
    <TextView android:textSize="12.0sp" android:gravity="center_horizontal" android:id="@id/tvAgreement" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginBottom="40.0dip" android:text="@string/profile_setting_about_us_app_agreement" android:layout_marginStart="35.0dip" android:layout_marginEnd="35.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>
