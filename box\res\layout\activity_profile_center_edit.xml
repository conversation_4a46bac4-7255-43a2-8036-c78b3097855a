<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout android:orientation="vertical" android:fitsSystemWindows="true" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <com.tn.lib.view.TitleLayout android:id="@id/toolbar" android:layout_width="fill_parent" android:layout_height="wrap_content" />
    <FrameLayout android:id="@id/fl_content" android:layout_width="fill_parent" android:layout_height="fill_parent" android:layout_below="@id/toolbar" />
    <ProgressBar android:id="@id/load_view" android:visibility="gone" android:layout_width="28.0dip" android:layout_height="28.0dip" android:layout_centerInParent="true" android:indeterminateTint="@color/main" />
</RelativeLayout>
