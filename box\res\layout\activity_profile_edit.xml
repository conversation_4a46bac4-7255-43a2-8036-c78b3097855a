<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.tn.lib.view.TitleLayout android:id="@id/titleLayout" android:layout_width="fill_parent" android:layout_height="wrap_content" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:titleText="@string/profile_edit_title" />
    <ImageView android:id="@id/ivAvatar" android:layout_width="96.0dip" android:layout_height="96.0dip" android:layout_marginTop="8.0dip" android:src="@mipmap/profile_default_avatar" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/titleLayout" />
    <ImageView android:layout_width="24.0dip" android:layout_height="24.0dip" android:src="@mipmap/profile_camera" app:layout_constraintBottom_toBottomOf="@id/ivAvatar" app:layout_constraintEnd_toEndOf="@id/ivAvatar" />
    <TextView android:textSize="16.0sp" android:textColor="@color/base_color_333333" android:gravity="center" android:id="@id/tvNickName" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="8.0dip" android:text="@string/profile_edit_nickname" android:paddingStart="29.0dip" android:paddingEnd="29.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/ivAvatar" />
    <ImageView android:id="@id/ivRight" android:layout_width="wrap_content" android:layout_height="wrap_content" android:src="@mipmap/ic_right_black" app:layout_constraintBottom_toBottomOf="@id/tvNickName" app:layout_constraintEnd_toEndOf="@id/tvNickName" app:layout_constraintTop_toTopOf="@id/tvNickName" />
    <com.transsion.usercenter.ProfileSettingTitleLayout android:id="@id/titleGender" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="16.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tvNickName" />
    <com.transsion.usercenter.ProfileSettingTitleLayout android:id="@id/titleBirth" android:layout_width="fill_parent" android:layout_height="wrap_content" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/titleGender" />
    <com.transsion.usercenter.ProfileSettingTitleLayout android:id="@id/titleRegion" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="wrap_content" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/titleBirth" />
</androidx.constraintlayout.widget.ConstraintLayout>
