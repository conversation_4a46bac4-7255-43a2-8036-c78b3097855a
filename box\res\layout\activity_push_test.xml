<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <EditText android:id="@id/edTitle" android:layout_width="fill_parent" android:layout_height="wrap_content" android:hint="通知标题" app:layout_constraintTop_toTopOf="parent" />
    <androidx.appcompat.widget.AppCompatButton android:id="@id/show_permanent_notification" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="100.0dip" android:text="常驻通知" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <androidx.appcompat.widget.AppCompatButton android:id="@id/show_notification" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="20.0dip" android:text="普通通知" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/show_permanent_notification" />
</androidx.constraintlayout.widget.ConstraintLayout>
