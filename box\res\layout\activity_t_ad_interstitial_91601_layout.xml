<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:background="@color/black" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.constraintlayout.widget.ConstraintLayout android:id="@id/main_layout" android:background="@color/black" android:layout_width="fill_parent" android:layout_height="fill_parent" />
    <ProgressBar android:id="@id/ad_progress" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="2.0dip" android:progressDrawable="@drawable/hisavana_ad_video_progress_bg" android:indeterminateTint="#ff008438" app:layout_constraintBottom_toBottomOf="parent" style="@style/Widget.AppCompat.ProgressBar.Horizontal" />
    <com.cloud.sdk.commonutil.widget.TranCircleImageView android:id="@id/iv_main_image" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="fill_parent" android:adjustViewBounds="true" />
    <ImageView android:id="@id/im_volume" android:visibility="gone" android:layout_width="24.0dip" android:layout_height="24.0dip" android:layout_marginTop="16.0dip" android:src="@drawable/hisavana_volume_close" android:layout_marginStart="16.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <ImageView android:id="@id/ivCancel" android:layout_width="24.0dip" android:layout_height="24.0dip" android:src="@drawable/ssp_sdk_cancel" android:layout_marginEnd="12.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="@id/im_volume" />
    <androidx.constraintlayout.widget.ConstraintLayout android:id="@id/llRoot" android:background="@drawable/ssp_bg_80000000_radius_4" android:visibility="visible" android:layout_width="fill_parent" android:layout_height="72.0dip" android:layout_marginLeft="16.0dip" android:layout_marginRight="16.0dip" android:layout_marginBottom="24.0dip" app:layout_constraintBottom_toBottomOf="parent">
        <com.cloud.sdk.commonutil.widget.TranCircleImageView android:id="@id/ivIcon" android:layout_width="48.0dip" android:layout_height="48.0dip" android:layout_marginStart="12.0dip" app:bottomLeftRadiusYL="4.0dip" app:bottomRightRadiusYL="4.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:topLeftRadiusYL="4.0dip" app:topRightRadiusYL="4.0dip" />
        <TextView android:textSize="16.0sp" android:textStyle="bold" android:textColor="#ffffffff" android:ellipsize="end" android:gravity="center" android:layout_gravity="center" android:id="@id/tvBtn" android:background="@drawable/ssp_bg_0052e2_4_4_4_4" android:paddingLeft="8.0dip" android:paddingRight="8.0dip" android:layout_width="wrap_content" android:layout_height="28.0dip" android:text="" android:lines="1" android:layout_weight="1.0" android:layout_marginEnd="12.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent" />
        <LinearLayout android:orientation="vertical" android:paddingLeft="10.0dip" android:paddingRight="10.0dip" android:layout_width="0.0dip" android:layout_height="wrap_content" app:layout_constraintBottom_toBottomOf="@id/ivIcon" app:layout_constraintEnd_toStartOf="@id/tvBtn" app:layout_constraintStart_toEndOf="@id/ivIcon" app:layout_constraintTop_toTopOf="@id/ivIcon">
            <TextView android:textSize="14.0sp" android:textColor="#ffffffff" android:ellipsize="end" android:id="@id/tvName" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="" android:lines="1" />
            <TextView android:textSize="10.0sp" android:textColor="#ffffffff" android:ellipsize="end" android:gravity="center" android:id="@id/tvDescription" android:visibility="visible" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="5.0dip" android:text="" android:maxLines="2" android:lineSpacingExtra="4.0dip" />
        </LinearLayout>
        <com.cloud.hisavana.sdk.api.view.StoreMarkView android:id="@id/ps_mark_view" android:layout_width="wrap_content" android:layout_height="wrap_content" app:layout_constraintBottom_toTopOf="@id/tvBtn" app:layout_constraintEnd_toEndOf="@id/tvBtn" app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
    <include android:id="@id/ad_flag" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginBottom="8.0dip" android:layout_marginStart="16.0dip" android:layout_marginEnd="16.0dip" app:layout_constraintBottom_toTopOf="@id/llRoot" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" layout="@layout/include_ad_flag" />
    <com.cloud.hisavana.sdk.api.view.AdDisclaimerView android:id="@id/ad_disclaimer_view" android:layout_width="0.0dip" android:layout_height="@dimen/ad_disclaimer_height" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>
