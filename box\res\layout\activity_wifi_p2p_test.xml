<?xml version="1.0" encoding="utf-8"?>
<androidx.appcompat.widget.LinearLayoutCompat android:orientation="vertical" android:id="@id/main" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <androidx.appcompat.widget.AppCompatButton android:gravity="center" android:id="@id/btnWifiCreate" android:layout_width="fill_parent" android:layout_height="40.0dip" android:text="WifiCreate" android:textAllCaps="false" />
    <androidx.appcompat.widget.AppCompatButton android:gravity="center" android:id="@id/btnWifiConnect" android:layout_width="fill_parent" android:layout_height="40.0dip" android:text="WifiConnect" android:textAllCaps="false" />
</androidx.appcompat.widget.LinearLayoutCompat>
