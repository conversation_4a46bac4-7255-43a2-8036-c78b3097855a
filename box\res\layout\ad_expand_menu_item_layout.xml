<?xml version="1.0" encoding="utf-8"?>
<merge android:orientation="horizontal" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <ImageView android:layout_gravity="center_vertical" android:id="@id/left_image_view" android:layout_width="14.0dip" android:layout_height="14.0dip" android:scaleType="centerInside" android:layout_marginStart="4.0dip" />
    <TextView android:textColor="#ff0f1a2f" android:layout_gravity="center_vertical" android:id="@id/message_text_view" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginStart="4.0dip" android:layout_marginEnd="4.0dip" />
    <Space android:layout_width="0.0dip" android:layout_height="0.0dip" android:layout_weight="1.0" />
    <ImageView android:layout_gravity="end|center" android:id="@id/right_image_view" android:layout_width="14.0dip" android:layout_height="14.0dip" android:src="@drawable/indicator_right" android:scaleType="centerInside" android:layout_marginEnd="4.0dip" />
</merge>
