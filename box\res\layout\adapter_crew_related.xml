<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:id="@id/root" android:layout_width="wrap_content" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.google.android.material.imageview.ShapeableImageView android:id="@id/iv_staff_avatar" android:layout_width="88.0dip" android:layout_height="118.0dip" android:src="@mipmap/movie_staff_default_avatar" android:scaleType="centerCrop" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:shapeAppearanceOverlay="@style/corner_style_4" />
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/text_01" android:ellipsize="end" android:gravity="center" android:id="@id/tv_staff_name" android:layout_marginTop="8.0dip" android:maxLines="2" app:layout_constraintEnd_toEndOf="@id/iv_staff_avatar" app:layout_constraintStart_toStartOf="@id/iv_staff_avatar" app:layout_constraintTop_toBottomOf="@id/iv_staff_avatar" style="@style/style_regular_text" />
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/text_02" android:ellipsize="end" android:gravity="center" android:id="@id/tv_staff_job" android:visibility="gone" android:layout_marginTop="4.0dip" android:maxLines="2" app:layout_constraintEnd_toEndOf="@id/iv_staff_avatar" app:layout_constraintStart_toStartOf="@id/iv_staff_avatar" app:layout_constraintTop_toBottomOf="@id/tv_staff_name" style="@style/style_tip_text" />
</androidx.constraintlayout.widget.ConstraintLayout>
