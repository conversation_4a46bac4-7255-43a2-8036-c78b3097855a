<?xml version="1.0" encoding="utf-8"?>
<FrameLayout android:layout_width="fill_parent" android:layout_height="56.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.noober.background.view.BLView android:layout_gravity="center" android:id="@id/v_all_ep_btn" android:layout_width="fill_parent" android:layout_height="40.0dip" android:layout_marginStart="12.0dip" android:layout_marginEnd="12.0dip" app:bl_corners_radius="4.0dip" app:bl_solid_color="@color/white_10" />
    <com.tn.lib.widget.TnTextView android:textColor="@color/text_01" android:layout_gravity="center" android:id="@id/tv_all_episodes" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/download_series_all_episodes" android:drawablePadding="2.0dip" app:drawableEndCompat="@drawable/libui_ic_medium_arrow_right" style="@style/style_medium_text" />
</FrameLayout>
