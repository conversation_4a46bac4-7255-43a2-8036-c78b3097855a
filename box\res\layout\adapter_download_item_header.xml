<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <View android:id="@id/vTopSpace" android:layout_width="fill_parent" android:layout_height="12.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <com.tn.lib.widget.TnTextView android:textSize="14.0sp" android:textColor="@color/text_02" android:gravity="center_vertical" android:id="@id/tvTitle" android:layout_width="wrap_content" android:layout_height="28.0dip" android:layout_marginStart="16.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/vTopSpace" />
    <TextView android:textSize="14.0sp" android:textColor="@color/brand" android:id="@id/tvManager" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/download_title_manager" android:layout_marginEnd="16.0dip" app:layout_constraintBottom_toBottomOf="@id/tvTitle" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="@id/tvTitle" />
</androidx.constraintlayout.widget.ConstraintLayout>
