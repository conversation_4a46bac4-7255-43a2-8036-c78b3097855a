<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:gravity="center" android:paddingBottom="2.0dip" android:layout_width="fill_parent" android:layout_height="28.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.tn.lib.widget.TnTextView android:textColor="@color/text_01" android:layout_gravity="bottom" android:id="@id/tv_title" android:layout_marginEnd="4.0dip" style="@style/style_medium_text" />
    <androidx.appcompat.widget.AppCompatImageView android:layout_gravity="bottom" android:id="@id/iv_hide_more" android:layout_width="wrap_content" android:layout_height="wrap_content" android:tint="@color/text_01" app:srcCompat="@mipmap/libui_ic_down_more" />
</LinearLayout>
