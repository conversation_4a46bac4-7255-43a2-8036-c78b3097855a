<?xml version="1.0" encoding="utf-8"?>
<FrameLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="55.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <TextView android:textSize="16.0sp" android:textColor="@color/text_01" android:ellipsize="end" android:gravity="center" android:layout_gravity="center" android:id="@id/tv_title" android:layout_width="fill_parent" android:layout_height="fill_parent" android:lines="1" android:paddingStart="15.0dip" android:paddingEnd="15.0dip" />
    <View android:layout_gravity="bottom" android:background="@color/line_01" android:layout_width="fill_parent" android:layout_height="1.0dip" />
</FrameLayout>
