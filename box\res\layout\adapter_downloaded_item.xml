<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:id="@id/cl_item_root" android:paddingBottom="16.0dip" android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <include android:id="@id/card_cover" android:layout_width="96.0dip" android:layout_height="54.0dip" android:layout_marginTop="4.0dip" android:layout_marginStart="12.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" layout="@layout/layout_download_item_cover" />
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/text_01" android:ellipsize="end" android:id="@id/tv_name" android:layout_width="wrap_content" android:layout_height="wrap_content" android:maxLines="1" android:layout_marginStart="8.0dip" app:layout_constrainedWidth="true" app:layout_constraintBottom_toTopOf="@id/tv_file_size" app:layout_constraintEnd_toStartOf="@id/tv_ep" app:layout_constraintHorizontal_bias="0.0" app:layout_constraintHorizontal_chainStyle="packed" app:layout_constraintStart_toEndOf="@id/card_cover" app:layout_constraintTop_toTopOf="@id/card_cover" app:layout_constraintVertical_chainStyle="packed" style="@style/style_medium_text" />
    <com.tn.lib.widget.TnTextView android:textColor="@color/text_01" android:ellipsize="end" android:id="@id/tv_ep" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:maxLines="1" android:layout_marginStart="2.0dip" app:layout_constraintBottom_toBottomOf="@id/tv_name" app:layout_constraintEnd_toStartOf="@id/iv_more" app:layout_constraintStart_toEndOf="@id/tv_name" app:layout_constraintTop_toTopOf="@id/tv_name" app:layout_constraintVertical_chainStyle="packed" style="@style/style_medium_text" />
    <com.tn.lib.widget.TnTextView android:textSize="10.0sp" android:textColor="@color/white_60" android:id="@id/tv_series_count" android:visibility="gone" app:layout_constraintBottom_toBottomOf="@id/tv_file_size" app:layout_constraintStart_toEndOf="@id/tv_series_count" app:layout_constraintStart_toStartOf="@id/tv_name" app:layout_constraintTop_toTopOf="@id/tv_file_size" style="@style/style_regular_text" />
    <View android:id="@id/tv_series_line" android:background="@color/white_20" android:visibility="gone" android:layout_width="1.0dip" android:layout_height="6.0dip" android:layout_marginStart="4.0dip" app:layout_constraintBottom_toBottomOf="@id/tv_file_size" app:layout_constraintStart_toEndOf="@id/tv_series_count" app:layout_constraintTop_toTopOf="@id/tv_file_size" />
    <com.tn.lib.widget.TnTextView android:textSize="10.0sp" android:textColor="@color/white_60" android:id="@id/tv_file_size" android:layout_marginTop="2.0dip" android:layout_marginStart="4.0dip" app:layout_constraintBottom_toTopOf="@id/tv_read_progress" app:layout_constraintStart_toEndOf="@id/tv_series_line" app:layout_constraintTop_toBottomOf="@id/tv_name" app:layout_goneMarginStart="0.0dip" style="@style/style_regular_text" />
    <com.tn.lib.widget.TnTextView android:textSize="10.0sp" android:textColor="@color/white_60" android:id="@id/tv_save_file_name" android:visibility="gone" android:drawablePadding="2.0dip" android:drawableStart="@mipmap/ic_download_save_file_icon" android:layout_marginStart="4.0dip" app:layout_constraintBottom_toBottomOf="@id/tv_file_size" app:layout_constraintStart_toEndOf="@id/tv_file_size" app:layout_constraintTop_toTopOf="@id/tv_file_size" style="@style/style_regular_text" />
    <com.tn.lib.widget.TnTextView android:textColor="@color/white_80" android:id="@id/tv_read_progress" android:layout_marginTop="2.0dip" app:layout_constraintBottom_toBottomOf="@id/card_cover" app:layout_constraintStart_toStartOf="@id/tv_name" app:layout_constraintTop_toBottomOf="@id/tv_file_size" style="@style/style_regular_text" />
    <com.tn.lib.widget.TnTextView android:textSize="12.0sp" android:textColor="@color/color_07B84E" android:gravity="center" android:id="@id/tv_save_to" android:background="@drawable/download_shape_save_to_bg" android:paddingLeft="8.0dip" android:paddingRight="8.0dip" android:visibility="gone" android:layout_height="24.0dip" android:text="@string/download_save_to_dot" android:layout_marginEnd="12.0dip" app:layout_constraintBottom_toBottomOf="@id/card_cover" app:layout_constraintEnd_toEndOf="parent" style="@style/style_medium_text" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_more" android:paddingTop="6.0dip" android:paddingBottom="6.0dip" android:layout_width="wrap_content" android:layout_height="wrap_content" android:scaleType="centerCrop" android:paddingStart="5.0dip" android:paddingEnd="12.0dip" app:layout_constraintBottom_toBottomOf="@id/tv_name" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="@id/tv_name" app:srcCompat="@drawable/ic_download_more" />
    <com.tn.lib.widget.TnTextView android:textSize="12.0sp" android:textColor="@color/gray_dark_00" android:gravity="center" android:id="@id/tv_btn" android:background="@drawable/bg_btn_01_new" android:paddingLeft="8.0dip" android:paddingRight="8.0dip" android:layout_height="20.0dip" android:layout_marginBottom="2.0dip" android:minWidth="48.0dip" android:text="@string/play" android:layout_marginEnd="12.0dip" app:layout_constraintBottom_toBottomOf="@id/card_cover" app:layout_constraintEnd_toEndOf="parent" style="@style/style_import_text" />
</androidx.constraintlayout.widget.ConstraintLayout>
