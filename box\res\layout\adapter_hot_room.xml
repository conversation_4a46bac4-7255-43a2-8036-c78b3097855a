<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.google.android.material.imageview.ShapeableImageView android:id="@id/iv_cover" android:layout_width="48.0dip" android:layout_height="48.0dip" android:layout_marginTop="12.0dip" android:layout_marginBottom="12.0dip" android:scaleType="centerCrop" android:layout_marginStart="16.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:shapeAppearance="@style/roundStyle_4" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textColor="@color/text_01" android:ellipsize="end" android:id="@id/tv_name" android:layout_width="184.0dip" android:layout_marginTop="4.0dip" android:maxLines="1" android:layout_marginStart="8.0dip" app:layout_constraintLeft_toRightOf="@id/iv_cover" app:layout_constraintTop_toTopOf="@id/iv_cover" style="@style/style_medium_text" />
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/text_03" android:ellipsize="end" android:id="@id/tv_desc" android:layout_width="184.0dip" android:layout_marginTop="2.0dip" android:maxLines="1" app:layout_constraintLeft_toLeftOf="@id/tv_name" app:layout_constraintTop_toBottomOf="@id/tv_name" style="@style/style_regular_text" />
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/text_03" android:id="@id/tv_focus_num" android:layout_marginEnd="7.0dip" app:layout_constraintBottom_toBottomOf="@id/tv_name" app:layout_constraintRight_toLeftOf="@id/iv_more" app:layout_constraintTop_toTopOf="@id/tv_name" style="@style/style_regular_text" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_more" android:layout_width="16.0dip" android:layout_height="16.0dip" android:src="@mipmap/libui_ic_more" android:layout_marginEnd="12.0dip" app:layout_constraintBottom_toBottomOf="@id/iv_cover" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="@id/iv_cover" app:tint="@color/text_01" />
    <com.transsion.room.view.roundimage.PileLayout android:id="@id/pl_member_ic" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="2.0dip" app:PileLayout_pileWidth="8.0dip" app:layout_constraintEnd_toEndOf="@id/tv_focus_num" app:layout_constraintTop_toBottomOf="@id/tv_focus_num" />
</androidx.constraintlayout.widget.ConstraintLayout>
