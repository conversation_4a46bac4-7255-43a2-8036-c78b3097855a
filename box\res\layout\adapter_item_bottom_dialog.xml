<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="60.0dip" android:layout_marginStart="16.0dip" android:layout_marginEnd="16.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/text_01" android:ellipsize="middle" android:id="@id/tv_name" android:layout_width="fill_parent" android:layout_marginTop="13.0dip" android:singleLine="true" android:layout_marginEnd="92.0dip" app:layout_constraintEnd_toEndOf="@id/fl_ctr" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" style="@style/style_medium_text" />
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/text_03" android:id="@id/tv_process" android:layout_marginTop="4.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tv_name" style="@style/style_regular_text" />
    <View android:id="@id/dot_1" android:background="@color/text_03" android:layout_width="1.0dip" android:layout_height="1.0dip" android:layout_marginStart="6.0dip" app:layout_constraintBottom_toBottomOf="@id/tv_process" app:layout_constraintStart_toEndOf="@id/tv_process" app:layout_constraintTop_toTopOf="@id/tv_process" />
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/text_03" android:id="@id/tv_size" android:layout_marginTop="5.0dip" android:layout_marginStart="6.0dip" app:layout_constraintStart_toEndOf="@id/dot_1" app:layout_constraintTop_toBottomOf="@id/tv_name" style="@style/style_regular_text" />
    <FrameLayout android:id="@id/fl_ctr" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginEnd="8.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toStartOf="@id/iv_del" app:layout_constraintTop_toTopOf="parent">
        <com.google.android.material.imageview.ShapeableImageView android:layout_gravity="center" android:id="@id/iv_subject" android:layout_width="32.0dip" android:layout_height="32.0dip" android:scaleType="centerCrop" app:shapeAppearance="@style/circle_style" app:srcCompat="@drawable/play_progress_bg" />
        <ProgressBar android:layout_gravity="center" android:id="@id/pb_subject" android:visibility="gone" android:layout_width="32.0dip" android:layout_height="32.0dip" android:max="100" android:progressDrawable="@drawable/circle_progress_bar_list" style="?android:progressBarStyleHorizontal" />
    </FrameLayout>
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_del" android:padding="8.0dip" android:layout_width="32.0dip" android:layout_height="32.0dip" android:src="@mipmap/close" android:scaleType="centerCrop" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>
