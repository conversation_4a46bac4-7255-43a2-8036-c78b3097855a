<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="72.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <ImageView android:id="@id/ivAvatar" android:layout_width="48.0dip" android:layout_height="48.0dip" android:src="@mipmap/profile_default_avatar" android:layout_marginStart="16.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <TextView android:textSize="14.0sp" android:textStyle="bold" android:textColor="@color/base_color_333333" android:id="@id/tvNickname" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="nickname" android:layout_marginStart="8.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toEndOf="@id/ivAvatar" app:layout_constraintTop_toTopOf="parent" />
    <TextView android:textSize="14.0sp" android:textColor="@color/base_color_333333" android:id="@id/btnFollow" android:background="@drawable/btn_bg_following" android:paddingTop="5.0dip" android:paddingBottom="11.0dip" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/user_center_follow_following" android:paddingStart="12.0dip" android:paddingEnd="11.0dip" android:layout_marginEnd="17.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>
