<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:background="@drawable/bg_personal_adapter_item" android:layout_width="fill_parent" android:layout_height="132.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.google.android.material.imageview.ShapeableImageView android:id="@id/iv_cover" android:layout_width="48.0dip" android:layout_height="48.0dip" android:layout_marginTop="12.0dip" android:scaleType="centerCrop" android:layout_marginStart="28.0dip" android:layout_marginEnd="28.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:shapeAppearance="@style/roundStyle_8" />
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/text_01" android:ellipsize="end" android:gravity="center" android:id="@id/tv_name" android:layout_width="88.0dip" android:layout_marginTop="8.0dip" android:maxLines="2" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/iv_cover" style="@style/style_regular_text" />
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/text_03" android:id="@id/tv_focus_num" android:layout_marginBottom="12.0dip" android:layout_marginStart="8.0dip" android:layout_marginEnd="8.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" style="@style/style_regular_text" />
</androidx.constraintlayout.widget.ConstraintLayout>
