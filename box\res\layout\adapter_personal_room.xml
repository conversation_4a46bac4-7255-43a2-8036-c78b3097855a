<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:background="@drawable/bg_personal_adapter_item" android:layout_width="100.0dip" android:layout_height="126.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.google.android.material.imageview.ShapeableImageView android:id="@id/iv_cover" android:layout_width="46.0dip" android:layout_height="46.0dip" android:layout_marginTop="12.0dip" android:scaleType="centerCrop" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:shapeAppearance="@style/circle_style" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="10.0sp" android:textColor="@color/cl41" android:gravity="center" android:id="@id/tv_post_num" android:background="@drawable/libui_message_tip" android:maxWidth="20.0dip" android:maxHeight="16.0dip" app:layout_constraintEnd_toEndOf="@id/iv_cover" app:layout_constraintTop_toTopOf="@id/iv_cover" style="@style/style_tip_text" />
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/cl32_ff_p" android:ellipsize="end" android:gravity="center" android:id="@id/tv_name" android:layout_width="84.0dip" android:layout_marginTop="8.0dip" android:maxLines="2" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/iv_cover" style="@style/style_regular_text" />
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/cl34" android:id="@id/tv_focus_num" android:layout_marginBottom="12.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" style="@style/style_regular_text" />
</androidx.constraintlayout.widget.ConstraintLayout>
