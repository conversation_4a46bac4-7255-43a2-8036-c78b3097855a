<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:paddingBottom="16.0dip" android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.google.android.material.imageview.ShapeableImageView android:id="@id/file_image" android:background="@color/module_04" android:layout_width="68.0dip" android:layout_height="68.0dip" android:scaleType="centerCrop" android:importantForAccessibility="no" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:shapeAppearance="@style/corner_style_8" />
    <TextView android:textSize="14.0sp" android:textStyle="bold" android:textColor="@color/text_01" android:ellipsize="middle" android:id="@id/file_name" android:layout_width="0.0dip" android:layout_height="wrap_content" android:singleLine="true" android:layout_marginStart="8.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/file_image" app:layout_constraintTop_toTopOf="@id/file_image" />
    <TextView android:textSize="12.0sp" android:textColor="@color/text_03" android:id="@id/file_trans_ratio" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="4.0dip" android:lines="1" android:layout_marginStart="8.0dip" app:layout_constraintStart_toEndOf="@id/file_image" app:layout_constraintTop_toBottomOf="@id/file_name" />
    <ProgressBar android:id="@id/file_trans_ratio_pb" android:layout_width="0.0dip" android:layout_height="2.0dip" android:layout_marginTop="4.0dip" android:max="100" android:layout_marginStart="8.0dip" android:layout_marginEnd="36.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/file_image" app:layout_constraintTop_toBottomOf="@id/file_trans_ratio" style="@style/TransferProgressBar" />
    <ImageView android:id="@id/file_trans_retry" android:layout_width="28.0dip" android:layout_height="28.0dip" android:src="@mipmap/transfer_ic_retry" android:importantForAccessibility="no" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/file_trans_ratio_pb" app:layout_constraintTop_toBottomOf="@id/file_name" />
    <TextView android:textSize="12.0sp" android:textColor="@color/text_03" android:id="@id/file_trans_state" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="4.0dip" android:lines="1" android:layout_marginStart="8.0dip" app:layout_constraintStart_toEndOf="@id/file_image" app:layout_constraintTop_toBottomOf="@id/file_trans_ratio_pb" />
</androidx.constraintlayout.widget.ConstraintLayout>
