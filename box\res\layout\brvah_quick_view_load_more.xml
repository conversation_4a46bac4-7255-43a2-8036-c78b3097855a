<?xml version="1.0" encoding="utf-8"?>
<FrameLayout android:layout_width="fill_parent" android:layout_height="40.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <LinearLayout android:gravity="center" android:orientation="horizontal" android:id="@id/load_more_loading_view" android:layout_width="fill_parent" android:layout_height="fill_parent">
        <ProgressBar android:id="@id/loading_progress" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginEnd="4.0dip" style="?android:progressBarStyleSmall" />
        <TextView android:textSize="14.0sp" android:textColor="@android:color/black" android:id="@id/loading_text" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/brvah_loading" android:layout_marginStart="4.0dip" />
    </LinearLayout>
    <FrameLayout android:id="@id/load_more_load_fail_view" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="fill_parent">
        <TextView android:layout_gravity="center" android:id="@id/tv_prompt" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/brvah_load_failed" />
    </FrameLayout>
    <FrameLayout android:id="@id/load_more_load_complete_view" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="fill_parent">
        <TextView android:textColor="@android:color/darker_gray" android:layout_gravity="center" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/brvah_load_complete" />
    </FrameLayout>
    <FrameLayout android:id="@id/load_more_load_end_view" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="fill_parent">
        <TextView android:textColor="@android:color/darker_gray" android:layout_gravity="center" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/brvah_load_end" />
    </FrameLayout>
</FrameLayout>
