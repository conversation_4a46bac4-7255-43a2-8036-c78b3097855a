<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:gravity="center" android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.appcompat.widget.AppCompatTextView android:textSize="@dimen/sp_14" android:textStyle="bold" android:textColor="@color/white" android:gravity="center_vertical" android:id="@android:id/message" android:background="@drawable/bg_claim_succeed" android:padding="16.0dip" android:layout_width="fill_parent" android:layout_height="wrap_content" android:text="@string/member_claimed_succeed_1_day" android:drawablePadding="8.0dip" android:drawableStart="@mipmap/ic_succeed" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
</LinearLayout>
