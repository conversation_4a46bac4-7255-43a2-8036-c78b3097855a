<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout android:layout_width="fill_parent" android:layout_height="48.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <ImageView android:id="@id/iv_back" android:layout_width="wrap_content" android:layout_height="44.0dip" android:src="@mipmap/libui_ic_back_black" android:layout_centerVertical="true" android:layout_marginStart="16.0dip" app:tint="@color/white" />
    <com.tn.lib.widget.TnTextView android:textSize="18.0sp" android:textColor="@color/text_01" android:id="@id/tv_title" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_centerInParent="true" style="@style/style_medium_text" />
    <ImageView android:id="@id/iv_search" android:layout_width="24.0dip" android:layout_height="24.0dip" android:layout_centerVertical="true" android:layout_marginEnd="16.0dip" android:layout_alignParentEnd="true" />
    <com.tn.lib.widget.TnTextView android:textSize="16.0sp" android:textColor="@color/color_000000" android:gravity="center" android:id="@id/tv_edit" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="fill_parent" android:layout_centerVertical="true" android:paddingStart="16.0dip" android:paddingEnd="16.0dip" android:layout_alignParentEnd="true" style="@style/style_medium_text" />
    <View android:background="@color/line_01" android:layout_width="fill_parent" android:layout_height="1.0dip" android:layout_alignParentBottom="true" />
</RelativeLayout>
