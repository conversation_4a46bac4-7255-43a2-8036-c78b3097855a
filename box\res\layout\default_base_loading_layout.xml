<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:id="@id/post_detail_loading" android:background="@color/bg_02" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.constraintlayout.widget.ConstraintLayout android:id="@id/toolbar_d" android:layout_width="fill_parent" android:layout_height="44.0dip" app:layout_constraintTop_toTopOf="parent">
        <androidx.appcompat.widget.AppCompatImageView android:id="@id/ivBack" android:padding="12.0dip" android:layout_width="50.0dip" android:layout_height="50.0dip" android:src="@mipmap/libui_ic_back_black" android:scaleType="centerCrop" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
        <View android:id="@id/header_d" android:background="@drawable/bg_radius_14_color_f7f7f7" android:layout_width="28.0dip" android:layout_height="28.0dip" android:layout_marginStart="4.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toEndOf="@id/ivBack" app:layout_constraintTop_toTopOf="parent" />
        <View android:background="@drawable/bg_radius_14_color_f7f7f7" android:layout_width="83.0dip" android:layout_height="18.0dip" android:layout_marginStart="4.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toEndOf="@id/header_d" app:layout_constraintTop_toTopOf="parent" />
        <View android:background="@color/skeleton" android:layout_width="fill_parent" android:layout_height="1.0dip" app:layout_constraintBottom_toBottomOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
    <View android:id="@id/divider1" android:background="@drawable/bg_radius_14_color_f7f7f7" android:layout_width="240.0dip" android:layout_height="18.0dip" android:layout_marginTop="24.0dip" android:layout_marginStart="16.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/toolbar_d" />
    <View android:id="@id/divider2" android:background="@drawable/bg_radius_5_color_f7f7f7" android:layout_width="fill_parent" android:layout_height="8.0dip" android:layout_marginTop="16.0dip" android:layout_marginStart="@dimen/left_margin" android:layout_marginEnd="@dimen/left_margin" app:layout_constraintTop_toBottomOf="@id/divider1" />
    <View android:id="@id/divider3" android:background="@drawable/bg_radius_5_color_f7f7f7" android:layout_width="fill_parent" android:layout_height="8.0dip" android:layout_marginTop="8.0dip" android:layout_marginStart="@dimen/left_margin" android:layout_marginEnd="@dimen/left_margin" app:layout_constraintTop_toBottomOf="@id/divider2" />
    <View android:id="@id/divider4" android:background="@drawable/bg_radius_5_color_f7f7f7" android:layout_width="fill_parent" android:layout_height="8.0dip" android:layout_marginTop="8.0dip" android:layout_marginStart="@dimen/left_margin" android:layout_marginEnd="@dimen/left_margin" app:layout_constraintTop_toBottomOf="@id/divider3" />
    <View android:id="@id/divider5" android:background="@drawable/bg_radius_5_color_f7f7f7" android:layout_width="fill_parent" android:layout_height="8.0dip" android:layout_marginTop="8.0dip" android:layout_marginStart="@dimen/left_margin" android:layout_marginEnd="@dimen/left_margin" app:layout_constraintTop_toBottomOf="@id/divider4" />
    <View android:id="@id/divider6" android:background="@drawable/bg_radius_5_color_f7f7f7" android:layout_width="200.0dip" android:layout_height="8.0dip" android:layout_marginTop="8.0dip" android:layout_marginStart="@dimen/left_margin" android:layout_marginEnd="@dimen/left_margin" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/divider5" />
    <View android:id="@id/b1" android:background="@drawable/divider_4_grey" android:layout_width="0.0dip" android:layout_height="104.0dip" android:layout_marginTop="16.0dip" android:layout_marginStart="16.0dip" app:layout_constraintEnd_toStartOf="@id/b2" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/divider6" />
    <View android:id="@id/b2" android:background="@drawable/divider_4_grey" android:layout_width="0.0dip" android:layout_height="104.0dip" android:layout_marginTop="16.0dip" android:layout_marginStart="16.0dip" app:layout_constraintEnd_toStartOf="@id/b3" app:layout_constraintStart_toEndOf="@id/b1" app:layout_constraintTop_toBottomOf="@id/divider6" />
    <View android:id="@id/b3" android:background="@drawable/divider_4_grey" android:layout_width="0.0dip" android:layout_height="104.0dip" android:layout_marginTop="16.0dip" android:layout_marginStart="16.0dip" android:layout_marginEnd="16.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/b2" app:layout_constraintTop_toBottomOf="@id/divider6" />
    <View android:id="@id/divider7" android:background="@drawable/divider_4_grey" android:layout_width="fill_parent" android:layout_height="42.0dip" android:layout_marginTop="16.0dip" android:layout_marginStart="@dimen/left_margin" android:layout_marginEnd="@dimen/left_margin" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/b1" />
    <View android:id="@id/divider8" android:background="@drawable/bg_radius_5_color_f7f7f7" android:layout_width="72.0dip" android:layout_height="8.0dip" android:layout_marginTop="16.0dip" android:layout_marginStart="@dimen/left_margin" android:layout_marginEnd="@dimen/left_margin" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/divider7" />
    <View android:id="@id/divider9" android:layout_width="fill_parent" android:layout_height="8.0dip" android:layout_marginTop="24.0dip" app:layout_constraintTop_toBottomOf="@id/divider8" />
    <ProgressBar android:layout_width="23.0dip" android:layout_height="23.0dip" android:layout_marginTop="345.0dip" android:indeterminateTint="@color/brand" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>
