<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <View android:id="@id/view1" android:background="@color/skeleton" android:layout_width="144.0dip" android:layout_height="82.0dip" android:layout_marginTop="16.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <View android:id="@id/view2" android:background="@color/skeleton" android:layout_width="0.0dip" android:layout_height="16.0dip" android:layout_marginStart="12.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/view1" app:layout_constraintTop_toTopOf="@id/view1" />
    <View android:id="@id/view3" android:background="@color/skeleton" android:layout_width="82.0dip" android:layout_height="16.0dip" android:layout_marginTop="5.0dip" app:layout_constraintStart_toStartOf="@id/view2" app:layout_constraintTop_toBottomOf="@id/view2" />
    <com.google.android.material.imageview.ShapeableImageView android:id="@id/view4" android:background="@color/skeleton" android:layout_width="16.0dip" android:layout_height="16.0dip" android:layout_marginTop="5.0dip" app:layout_constraintStart_toStartOf="@id/view2" app:layout_constraintTop_toBottomOf="@id/view3" app:shapeAppearanceOverlay="@style/roundStyle_10" />
    <View android:id="@id/view5" android:background="@color/skeleton" android:layout_width="73.0dip" android:layout_height="16.0dip" android:layout_marginStart="7.0dip" app:layout_constraintBottom_toBottomOf="@id/view4" app:layout_constraintStart_toEndOf="@id/view4" app:layout_constraintTop_toTopOf="@id/view4" />
</androidx.constraintlayout.widget.ConstraintLayout>
