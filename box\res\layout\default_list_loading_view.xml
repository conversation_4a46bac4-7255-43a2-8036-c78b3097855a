<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.appcompat.widget.LinearLayoutCompat android:id="@id/toolbar_d" android:layout_width="fill_parent" android:layout_height="44.0dip" app:layout_constraintTop_toTopOf="parent">
        <androidx.appcompat.widget.AppCompatImageView android:id="@id/ivBack" android:layout_width="wrap_content" android:layout_height="44.0dip" android:paddingStart="16.0dip" android:paddingEnd="8.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:srcCompat="@mipmap/libui_ic_back_black" app:tint="@color/text_01" />
    </androidx.appcompat.widget.LinearLayoutCompat>
    <include android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="-24.0dip" app:layout_constraintTop_toBottomOf="@id/toolbar_d" layout="@layout/default_rv_loading_layout" />
    <ProgressBar android:layout_width="23.0dip" android:layout_height="23.0dip" android:layout_marginTop="345.0dip" android:indeterminateTint="@color/brand" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>
