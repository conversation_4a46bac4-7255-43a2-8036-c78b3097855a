<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:id="@id/post_detail_loading" android:background="@color/whit_black_not_transform" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <View android:id="@id/viewTopBg" android:background="@color/bg_03" android:layout_width="fill_parent" android:layout_height="240.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/ivBack" android:padding="12.0dip" android:layout_width="50.0dip" android:layout_height="50.0dip" android:layout_marginTop="50.0dip" android:src="@mipmap/icon_white_back" android:scaleType="centerCrop" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <View android:id="@id/view4" android:background="@drawable/bg_skeleton_drawing_4dp" android:layout_width="179.0dip" android:layout_height="14.0dip" android:layout_marginTop="16.0dip" android:layout_marginStart="16.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/viewTopBg" />
    <View android:id="@id/view6" android:background="@drawable/bg_skeleton_drawing_4dp" android:layout_width="213.0dip" android:layout_height="12.0dip" android:layout_marginTop="8.0dip" android:layout_marginStart="16.0dip" android:layout_marginEnd="16.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/view4" />
    <View android:id="@id/view71" android:background="@drawable/bg_skeleton_drawing_4dp" android:layout_width="160.0dip" android:layout_height="14.0dip" android:layout_marginTop="35.0dip" android:layout_marginStart="16.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/view6" />
    <View android:id="@id/view81" android:background="@drawable/bg_skeleton_drawing_8dp" android:layout_width="0.0dip" android:layout_height="86.0dip" android:layout_marginTop="15.0dip" android:layout_marginStart="16.0dip" android:layout_marginEnd="8.0dip" app:layout_constraintEnd_toStartOf="@id/view82" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/view71" />
    <View android:id="@id/view82" android:background="@drawable/bg_skeleton_drawing_8dp" android:layout_width="0.0dip" android:layout_height="86.0dip" android:layout_marginTop="15.0dip" android:layout_marginStart="8.0dip" android:layout_marginEnd="16.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/view81" app:layout_constraintTop_toBottomOf="@id/view71" />
    <View android:id="@id/view91" android:background="@drawable/bg_skeleton_drawing_8dp" android:layout_width="0.0dip" android:layout_height="86.0dip" android:layout_marginTop="15.0dip" android:layout_marginStart="16.0dip" android:layout_marginEnd="8.0dip" app:layout_constraintEnd_toStartOf="@id/view92" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/view81" />
    <View android:id="@id/view92" android:background="@drawable/bg_skeleton_drawing_8dp" android:layout_width="0.0dip" android:layout_height="86.0dip" android:layout_marginTop="15.0dip" android:layout_marginStart="8.0dip" android:layout_marginEnd="16.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/view91" app:layout_constraintTop_toBottomOf="@id/view81" />
    <View android:id="@id/view10" android:background="@drawable/bg_skeleton_drawing_4dp" android:layout_width="68.0dip" android:layout_height="14.0dip" android:layout_marginTop="28.0dip" android:layout_marginStart="16.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/view91" />
    <View android:id="@id/view11" android:background="@drawable/bg_skeleton_drawing_8dp" android:layout_width="0.0dip" android:layout_height="151.0dip" android:layout_marginTop="15.0dip" android:layout_marginStart="16.0dip" android:layout_marginEnd="16.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/view10" />
</androidx.constraintlayout.widget.ConstraintLayout>
