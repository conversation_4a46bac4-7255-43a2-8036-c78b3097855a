<?xml version="1.0" encoding="utf-8"?>
<FrameLayout android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.github.mmin18.widget.RealtimeBlurView android:id="@id/blur_view" android:layout_width="fill_parent" android:layout_height="fill_parent" app:realtimeBlurRadius="40.0dip" app:realtimeOverlayColor="#4d000000" />
    <androidx.constraintlayout.widget.ConstraintLayout android:layout_gravity="center" android:background="@drawable/bg_adult_dialog_corner_r_6" android:padding="20.0dip" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginStart="40.0dip" android:layout_marginEnd="40.0dip">
        <TextView android:textSize="16.0sp" android:textStyle="bold" android:textColor="@color/base_color_333333" android:id="@id/tv_title" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="4.0dip" android:text="@string/adults_only" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
        <TextView android:textSize="14.0sp" android:textColor="@color/base_color_333333" android:gravity="center" android:id="@id/tv_content" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="8.0dip" android:text="@string/adults_content" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tv_title" />
        <TextView android:textSize="14.0sp" android:textColor="@color/base_color_333333" android:gravity="center" android:id="@id/btn_negative" android:background="@drawable/bg_dialog_nega_button" android:layout_width="fill_parent" android:layout_height="36.0dip" android:layout_marginTop="16.0dip" android:text="@string/back" app:layout_constrainedWidth="true" app:layout_constraintEnd_toStartOf="@id/btn_positive" app:layout_constraintHorizontal_chainStyle="packed" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tv_content" />
        <TextView android:textSize="14.0sp" android:textColor="@color/white" android:gravity="center" android:id="@id/btn_positive" android:background="@drawable/bg_dialog_pos_button" android:layout_width="fill_parent" android:layout_height="36.0dip" android:layout_marginTop="16.0dip" android:text="@string/continue_" android:layout_marginStart="8.0dip" app:layout_constrainedWidth="true" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/btn_negative" app:layout_constraintTop_toBottomOf="@id/tv_content" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</FrameLayout>
