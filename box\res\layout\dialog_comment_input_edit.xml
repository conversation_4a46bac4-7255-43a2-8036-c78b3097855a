<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:id="@id/comment_input_layout" android:background="@color/bg_01" android:paddingBottom="6.0dip" android:layout_width="fill_parent" android:layout_height="wrap_content" android:minHeight="@dimen/comment_input_bg_height"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <View android:background="@color/dialog_line" android:layout_width="fill_parent" android:layout_height="1.0dip" app:layout_constraintTop_toTopOf="parent" />
    <androidx.constraintlayout.widget.ConstraintLayout android:id="@id/ll_editor" android:background="@drawable/comment_input_edit_bg" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="12.0dip" android:layout_marginStart="12.0dip" android:layout_marginEnd="12.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintRight_toRightOf="parent" app:layout_constraintTop_toTopOf="parent">
        <com.google.android.material.imageview.ShapeableImageView android:id="@id/iv_selected_image" android:visibility="gone" android:layout_width="64.0dip" android:layout_height="64.0dip" android:layout_marginTop="12.0dip" android:layout_marginBottom="12.0dip" android:scaleType="centerCrop" android:layout_marginStart="12.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:shapeAppearance="@style/corner_style_4" />
        <androidx.appcompat.widget.AppCompatImageView android:id="@id/icon_delete_image" android:layout_width="20.0dip" android:layout_height="20.0dip" android:layout_marginTop="4.0dip" android:layout_marginEnd="4.0dip" app:layout_constraintEnd_toEndOf="@id/iv_selected_image" app:layout_constraintTop_toTopOf="@id/iv_selected_image" app:srcCompat="@drawable/icon_post_image_delete" />
        <androidx.appcompat.widget.AppCompatEditText android:enabled="false" android:textSize="@dimen/text_size_16" android:textColor="@color/white" android:textColorHint="@color/text_02" android:gravity="start|center" android:id="@id/comment_input_edit_text" android:background="@color/transparent" android:paddingTop="4.0dip" android:paddingBottom="4.0dip" android:visibility="visible" android:layout_width="fill_parent" android:layout_height="wrap_content" android:maxHeight="120.0dip" android:minHeight="40.0dip" android:hint="@string/comment_hint_add" android:cursorVisible="true" android:maxLength="300" android:layout_marginStart="12.0dip" android:layout_marginEnd="12.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/iv_selected_image" style="@style/style_regular_text" />
    </androidx.constraintlayout.widget.ConstraintLayout>
    <androidx.appcompat.widget.LinearLayoutCompat android:gravity="center_vertical" android:id="@id/ll_tint" android:background="@drawable/comment_input_edit_bg" android:visibility="gone" android:layout_width="0.0dip" android:layout_height="36.0dip" android:layout_marginTop="6.0dip" android:layout_marginStart="16.0dip" app:layout_constraintEnd_toStartOf="@id/tv_like" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent">
        <androidx.appcompat.widget.AppCompatTextView android:enabled="false" android:textSize="@dimen/text_size_14" android:textColor="@color/text_06" android:textColorHint="@color/text_05" android:ellipsize="end" android:gravity="start|top" android:autoLink="none" android:id="@id/tv_hint" android:layout_width="0.0dip" android:layout_height="wrap_content" android:maxHeight="88.0dip" android:hint="@string/comment_hint_add" android:maxLines="1" android:singleLine="true" android:maxLength="200" android:layout_weight="1.0" android:layout_marginStart="12.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintRight_toRightOf="parent" app:layout_constraintTop_toTopOf="parent" />
        <View android:background="@color/cl36" android:layout_width="1.0dip" android:layout_height="16.0dip" android:layout_marginTop="10.0dip" android:layout_marginBottom="10.0dip" android:layout_marginEnd="12.0dip" />
        <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/text_06" android:gravity="center_vertical" android:id="@id/tv_comment" android:layout_marginEnd="12.0dip" app:drawableStartCompat="@mipmap/movie_detail_icon_comment" app:drawableTint="@color/text_06" style="@style/style_regula_bigger_text" />
    </androidx.appcompat.widget.LinearLayoutCompat>
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/text_06" android:gravity="center_vertical" android:id="@id/tv_like" android:visibility="gone" android:drawablePadding="5.0dip" android:paddingStart="14.0dip" app:drawableStartCompat="@mipmap/movie_detail_icon_like" app:drawableTint="@color/text_06" app:layout_constraintBottom_toBottomOf="@id/ll_tint" app:layout_constraintEnd_toStartOf="@id/tv_share" app:layout_constraintStart_toEndOf="@id/ll_tint" app:layout_constraintTop_toTopOf="@id/ll_tint" style="@style/style_regula_bigger_text" />
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/text_06" android:gravity="center_vertical" android:id="@id/tv_share" android:paddingTop="5.0dip" android:paddingBottom="5.0dip" android:visibility="gone" android:layout_height="fill_parent" android:paddingStart="12.0dip" android:paddingEnd="16.0dip" app:constraintSet="@id/tv_comment" app:drawableStartCompat="@mipmap/movie_detail_icon_black_share" app:drawableTint="@color/text_06" app:layout_constraintBottom_toBottomOf="@id/ll_tint" app:layout_constraintEnd_toStartOf="@id/iv_download" app:layout_constraintStart_toEndOf="@id/tv_like" app:layout_constraintTop_toTopOf="@id/ll_tint" style="@style/style_regula_bigger_text" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_download" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:paddingEnd="16.0dip" app:drawableTint="@color/text_06" app:layout_constraintBottom_toBottomOf="@id/ll_tint" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/tv_share" app:layout_constraintTop_toTopOf="@id/ll_tint" app:srcCompat="@mipmap/movie_detail_icon_black_download" />
    <View android:id="@id/comment_input_disable_click" android:layout_width="fill_parent" android:layout_height="0.0dip" app:layout_constraintBottom_toBottomOf="@id/ll_editor" app:layout_constraintTop_toTopOf="parent" />
    <ProgressBar android:id="@id/progress_bar" android:visibility="gone" android:layout_width="30.0dip" android:layout_height="30.0dip" android:indeterminateTint="@color/main" app:layout_constraintBottom_toBottomOf="@id/comment_input_edit_post" app:layout_constraintEnd_toEndOf="@id/comment_input_edit_post" app:layout_constraintTop_toTopOf="@id/comment_input_edit_post" />
    <com.google.android.material.imageview.ShapeableImageView android:id="@id/iv_choose_image" android:layout_width="24.0dip" android:layout_height="24.0dip" android:layout_marginTop="12.0dip" android:src="@drawable/icon_post_select_image" android:layout_marginStart="14.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/ll_editor" />
    <TextView android:textSize="16.0sp" android:textColor="@color/text_02" android:id="@id/comment_input_edit_limit" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginEnd="16.0dip" app:layout_constraintBottom_toBottomOf="@id/comment_input_edit_post" app:layout_constraintEnd_toStartOf="@id/comment_input_edit_post" app:layout_constraintTop_toTopOf="@id/comment_input_edit_post" style="@style/style_regular_text" />
    <androidx.appcompat.widget.AppCompatTextView android:enabled="false" android:textSize="16.0sp" android:textColor="@color/gray_dark_00" android:gravity="center_vertical" android:id="@id/comment_input_edit_post" android:background="@drawable/libui_main_btn_selector" android:visibility="gone" android:layout_height="28.0dip" android:layout_marginTop="10.0dip" android:text="@string/film_review_post" android:paddingStart="15.0dip" android:paddingEnd="15.0dip" android:layout_marginEnd="11.0dip" app:layout_constraintEnd_toEndOf="@id/ll_editor" app:layout_constraintTop_toBottomOf="@id/ll_editor" style="@style/style_import_text" />
</androidx.constraintlayout.widget.ConstraintLayout>
