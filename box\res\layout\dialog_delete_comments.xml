<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:orientation="vertical" android:background="@drawable/comment_delete_bg" android:paddingBottom="8.0dip" android:layout_width="fill_parent" android:layout_height="wrap_content" android:showDividers="middle"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <TextView android:textColor="@color/white" android:gravity="center" android:id="@id/tv_copy_comment" android:background="@drawable/comment_delete_btn_selector" android:layout_width="fill_parent" android:layout_height="48.0dip" android:layout_marginTop="8.0dip" style="@style/style_medium_text" />
    <TextView android:background="@color/white_10" android:layout_width="fill_parent" android:layout_height="1.0dip" />
    <TextView android:textColor="@color/white" android:gravity="center" android:id="@id/tv_report_comment" android:background="@drawable/comment_delete_btn_selector" android:layout_width="fill_parent" android:layout_height="48.0dip" android:layout_marginTop="8.0dip" style="@style/style_medium_text" />
    <TextView android:background="@color/white_10" android:layout_width="fill_parent" android:layout_height="1.0dip" />
    <TextView android:textColor="@color/base_color_FA5546" android:gravity="center" android:id="@id/tv_delete_comment" android:background="@drawable/comment_delete_btn_selector" android:layout_width="fill_parent" android:layout_height="48.0dip" style="@style/style_medium_text" />
    <TextView android:id="@id/line" android:background="@color/white_10" android:layout_width="fill_parent" android:layout_height="1.0dip" />
    <TextView android:textColor="@color/white" android:gravity="center" android:id="@id/tv_cancel" android:background="@drawable/comment_delete_btn_selector" android:layout_width="fill_parent" android:layout_height="48.0dip" style="@style/style_medium_text" />
</LinearLayout>
