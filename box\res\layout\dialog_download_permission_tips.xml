<?xml version="1.0" encoding="utf-8"?>
<FrameLayout android:background="@color/transparent" android:layout_width="wrap_content" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.constraintlayout.widget.ConstraintLayout android:gravity="center_horizontal" android:layout_gravity="center|bottom" android:orientation="vertical" android:background="@drawable/libui_common_dialog_bg" android:layout_width="280.0dip" android:layout_height="wrap_content">
        <androidx.appcompat.widget.AppCompatTextView android:textStyle="bold" android:textColor="@color/text_01" android:gravity="center" android:id="@id/tv_title" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="24.0dip" android:text="@string/download_permission_denied" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" style="@style/style_title_text" />
        <androidx.appcompat.widget.AppCompatTextView android:textSize="14.0sp" android:textColor="@color/text_02" android:gravity="center" android:id="@id/tv_desc" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginTop="8.0dip" android:text="@string/download_permission_denied_tips" android:paddingStart="10.0dip" android:paddingEnd="10.0dip" android:layout_marginStart="24.0dip" android:layout_marginEnd="24.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tv_title" style="@style/style_regula_bigger_text" />
        <androidx.appcompat.widget.AppCompatTextView android:textSize="14.0sp" android:textColor="@color/white" android:gravity="center" android:id="@id/tv_positive" android:background="@drawable/shape_download_group_button" android:layout_width="150.0dip" android:layout_height="40.0dip" android:layout_marginTop="15.0dip" android:layout_marginBottom="24.0dip" android:text="@string/go_to_setting" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tv_desc" style="@style/style_medium_text" />
        <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_navigation" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_margin="12.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent" app:srcCompat="@mipmap/libui_ic_close_dialog" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</FrameLayout>
