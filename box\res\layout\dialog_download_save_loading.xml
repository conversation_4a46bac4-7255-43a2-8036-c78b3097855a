<?xml version="1.0" encoding="utf-8"?>
<FrameLayout android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="110.0dip">
        <View android:id="@id/v_bg" android:background="@drawable/shape_download_save_loading_bg" android:layout_width="fill_parent" android:layout_height="48.0dip" android:layout_marginStart="16.0dip" android:layout_marginEnd="16.0dip" app:layout_constraintTop_toTopOf="parent" />
        <com.tn.lib.view.CircleProgressBar android:layout_gravity="center" android:id="@id/progress_bar" android:layout_width="22.0dip" android:layout_height="22.0dip" android:layout_marginStart="16.0dip" app:layout_constraintBottom_toBottomOf="@id/v_bg" app:layout_constraintStart_toStartOf="@id/v_bg" app:layout_constraintTop_toTopOf="@id/v_bg" app:progressBgColor="@color/white_30" app:progressMax="100" app:progressRadius="10.0dip" app:progressRingsColor="@color/white" app:progressStrokesWidth="2.0dip" />
        <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/white" android:gravity="center" android:layout_gravity="center_horizontal" android:id="@id/tv_tips" android:text="@string/download_saving_to_album" android:layout_marginStart="8.0dip" app:layout_constraintBottom_toBottomOf="@id/v_bg" app:layout_constraintStart_toEndOf="@id/progress_bar" app:layout_constraintTop_toTopOf="@id/v_bg" style="@style/style_medium_text" />
        <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/white" android:gravity="center" android:layout_gravity="center_horizontal" android:id="@id/tv_progress" app:layout_constraintBottom_toBottomOf="@id/v_bg" app:layout_constraintStart_toEndOf="@id/tv_tips" app:layout_constraintTop_toTopOf="@id/v_bg" style="@style/style_medium_text" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</FrameLayout>
