<?xml version="1.0" encoding="utf-8"?>
<FrameLayout android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.constraintlayout.widget.ConstraintLayout android:background="@drawable/download_bg_module_16" android:layout_width="fill_parent" android:layout_height="wrap_content">
        <TextView android:textSize="16.0sp" android:textColor="@color/text_01" android:gravity="center" android:id="@id/tvResumeAll" android:layout_width="fill_parent" android:layout_height="54.0dip" android:text="@string/download_resume_all" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" style="@style/style_medium_text" />
        <View android:id="@id/viewLine1" android:background="@color/line_01" android:layout_width="fill_parent" android:layout_height="1.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tvResumeAll" />
        <TextView android:textSize="16.0sp" android:textColor="@color/text_01" android:gravity="center" android:id="@id/tvDownloadXXSizeAtOnce" android:layout_width="wrap_content" android:layout_height="54.0dip" android:text="@string/download_xx_size_at_once" app:layout_constraintEnd_toStartOf="@id/viewBgPremium" app:layout_constraintHorizontal_chainStyle="packed" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/viewLine1" style="@style/style_medium_text" />
        <View android:id="@id/viewBgPremium" android:background="@drawable/download_2_bg_4" android:layout_width="63.0dip" android:layout_height="16.0dip" android:layout_marginStart="4.0dip" app:layout_constraintBottom_toBottomOf="@id/tvDownloadXXSizeAtOnce" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/tvDownloadXXSizeAtOnce" app:layout_constraintTop_toTopOf="@id/tvDownloadXXSizeAtOnce" />
        <androidx.appcompat.widget.AppCompatImageView android:id="@id/ivPremium" android:layout_width="12.0dip" android:layout_height="12.0dip" android:src="@drawable/me_icon_premium_2" app:layout_constraintBottom_toBottomOf="@id/viewBgPremium" app:layout_constraintEnd_toStartOf="@id/tvPremium" app:layout_constraintHorizontal_chainStyle="packed" app:layout_constraintStart_toStartOf="@id/viewBgPremium" app:layout_constraintTop_toTopOf="@id/viewBgPremium" />
        <TextView android:textSize="10.0sp" android:textColor="#ff130f26" android:id="@id/tvPremium" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/download_premium" android:layout_marginStart="2.0dip" app:layout_constraintBottom_toBottomOf="@id/viewBgPremium" app:layout_constraintEnd_toEndOf="@id/viewBgPremium" app:layout_constraintStart_toEndOf="@id/ivPremium" app:layout_constraintTop_toTopOf="@id/viewBgPremium" />
        <View android:id="@id/viewLine2" android:background="@color/line_01" android:layout_width="fill_parent" android:layout_height="1.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tvDownloadXXSizeAtOnce" />
        <androidx.constraintlayout.widget.Group android:id="@id/groupPremium" android:layout_width="wrap_content" android:layout_height="wrap_content" app:constraint_referenced_ids="tvResumeAll,viewLine1,tvDownloadXXSizeAtOnce     ,viewBgPremium,ivPremium,tvPremium,viewLine2" />
        <TextView android:textSize="16.0sp" android:textColor="@color/text_01" android:gravity="center" android:id="@id/tvBatteryPermission" android:layout_width="fill_parent" android:layout_height="54.0dip" android:text="@string/download_in_background" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/viewLine2" style="@style/style_medium_text" />
        <View android:id="@id/viewLine3" android:background="@color/line_01" android:layout_width="fill_parent" android:layout_height="1.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tvBatteryPermission" />
        <androidx.constraintlayout.widget.Group android:id="@id/groupBatteryPermission" android:layout_width="wrap_content" android:layout_height="wrap_content" app:constraint_referenced_ids="tvBatteryPermission,viewLine3" />
        <TextView android:textSize="16.0sp" android:textColor="@color/text_01" android:gravity="center" android:id="@id/tvCancel" android:layout_width="fill_parent" android:layout_height="54.0dip" android:text="@string/download_cancel" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/viewLine3" style="@style/style_medium_text" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</FrameLayout>
