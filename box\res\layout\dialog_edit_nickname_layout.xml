<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:background="@drawable/libui_common_dialog_bg" android:paddingBottom="20.0dip" android:layout_width="280.0dip" android:layout_height="wrap_content" android:minHeight="140.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <TextView android:id="@id/tv_title" android:visibility="visible" android:layout_marginTop="28.0dip" android:text="@string/profile_change_name" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" style="@style/style_title_text" />
    <EditText android:textSize="14.0sp" android:textColor="@color/base_color_333333" android:gravity="start|center" android:id="@id/ed_msg" android:background="@drawable/bg_dialog_edit_nickname" android:paddingTop="9.0dip" android:paddingBottom="9.0dip" android:layout_width="fill_parent" android:layout_height="36.0dip" android:layout_marginTop="8.0dip" android:maxLines="1" android:inputType="text" android:textCursorDrawable="@drawable/cursor_color" android:paddingStart="8.0dip" android:paddingEnd="8.0dip" android:layout_marginStart="20.0dip" android:layout_marginEnd="20.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tv_title" />
    <CheckBox android:textColor="@color/cl34" android:id="@id/cb" android:background="@null" android:visibility="gone" android:layout_height="16.0dip" android:layout_marginTop="8.0dip" android:button="@null" android:includeFontPadding="false" android:drawablePadding="4.0dip" android:drawableStart="@drawable/libui_ic_check_selector" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/ed_msg" style="@style/style_regula_bigger_text" />
    <TextView android:id="@id/tv_left" android:layout_width="wrap_content" android:layout_marginTop="16.0dip" android:minWidth="116.0dip" android:text="@string/profile_dialog_cancel" android:layout_marginStart="20.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/cb" style="@style/styleDialogBtnLeft" />
    <TextView android:id="@id/tv_right" android:layout_width="wrap_content" android:layout_marginTop="16.0dip" android:minWidth="116.0dip" android:text="@string/profile_dialog_yes" android:layout_marginEnd="20.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toBottomOf="@id/cb" style="@style/styleDialogBtnRight" />
    <ImageView android:id="@id/iv_close" android:padding="8.0dip" android:visibility="gone" android:layout_width="32.0dip" android:layout_height="32.0dip" android:src="@mipmap/libui_ic_close_dialog" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>
