<?xml version="1.0" encoding="utf-8"?>
<com.transsion.baseui.widget.GradientBorderView android:id="@id/clRoot" android:background="@drawable/bg_purchase_dialog" android:layout_width="fill_parent" android:layout_height="wrap_content" app:borderViewEndColor="#00f3c883" app:borderViewStartColor="#80f3c883" app:borderWidth="1.0dip" app:bottomLeftCornerRadius="0.0dip" app:bottomRightCornerRadius="0.0dip" app:gradientOrientation="vertical" app:topLeftCornerRadius="16.0dip" app:topRightCornerRadius="16.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.appcompat.widget.AppCompatImageView android:layout_width="fill_parent" android:layout_height="76.0dip" android:src="@mipmap/ic_purchased_layer" android:layout_centerHorizontal="true" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <ImageView android:id="@id/iv_premium_mask" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="12.0dip" android:src="@mipmap/ic_premium_mask" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <ImageView android:id="@id/iv_close" android:paddingLeft="8.0dip" android:paddingTop="8.0dip" android:paddingRight="8.0dip" android:paddingBottom="8.0dip" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="12.0dip" android:src="@mipmap/ic_purchase_close" android:layout_marginEnd="12.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <TextView android:textSize="@dimen/dimens_sp_18" android:textStyle="bold" android:textColor="@color/white" android:gravity="center_horizontal" android:id="@id/iv_title" android:paddingLeft="20.0dip" android:paddingRight="20.0dip" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="@dimen/dp_16" android:text="@string/member_successfully_upgraded_to_premium" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/iv_premium_mask" />
    <TextView android:textSize="@dimen/sp_14" android:textColor="@color/white" android:id="@id/iv_detail" android:paddingLeft="32.0dip" android:paddingRight="32.0dip" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="@dimen/dp_8" android:text="@string/member_enjoy_your_benefits_now" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/iv_title" />
    <androidx.appcompat.widget.LinearLayoutCompat android:gravity="center" android:layout_gravity="center" android:id="@id/explore" android:background="@mipmap/ic_monthly_bg" android:layout_width="fill_parent" android:layout_height="48.0dip" android:layout_marginLeft="32.0dip" android:layout_marginTop="24.0dip" android:layout_marginRight="32.0dip" android:layout_marginBottom="32.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/iv_detail">
        <androidx.appcompat.widget.AppCompatTextView android:textSize="@dimen/sp_16" android:textStyle="bold" android:textColor="@color/black" android:id="@id/explore_title" android:text="@string/member_explore_now" style="@style/style_medium_text" />
    </androidx.appcompat.widget.LinearLayoutCompat>
</com.transsion.baseui.widget.GradientBorderView>
