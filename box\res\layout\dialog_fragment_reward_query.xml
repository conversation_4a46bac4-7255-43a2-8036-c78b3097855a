<?xml version="1.0" encoding="utf-8"?>
<FrameLayout android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.constraintlayout.widget.ConstraintLayout android:layout_gravity="center" android:id="@id/clRoot" android:background="@drawable/bg_reward_query" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginLeft="40.0dip" android:layout_marginRight="40.0dip">
        <TextView android:textSize="@dimen/text_size_16" android:textStyle="bold" android:textColor="@color/white" android:gravity="center_horizontal" android:id="@id/tvTitle" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginLeft="24.0dip" android:layout_marginTop="24.0dip" android:layout_marginRight="24.0dip" android:text="@string/member_reward_query_title_1_day" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
        <TextView android:textSize="@dimen/text_size_14" android:textColor="@color/white" android:gravity="center_horizontal" android:id="@id/tvDesc" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginLeft="24.0dip" android:layout_marginTop="16.0dip" android:layout_marginRight="24.0dip" android:text="@string/member_reward_desc" android:textAlignment="center" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tvTitle" />
        <androidx.appcompat.widget.LinearLayoutCompat android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginLeft="24.0dip" android:layout_marginTop="16.0dip" android:layout_marginRight="24.0dip" android:layout_marginBottom="24.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tvDesc">
            <androidx.appcompat.widget.AppCompatTextView android:textSize="@dimen/text_size_14" android:textStyle="bold" android:textColor="@color/white" android:gravity="center" android:id="@id/tv_later" android:background="@drawable/bg_query_later" android:layout_width="0.0dip" android:layout_height="36.0dip" android:text="@string/member_reward_later" android:layout_weight="1.0" android:layout_marginEnd="8.0dip" />
            <com.transsion.baseui.widget.GradientBorderView android:id="@id/iv_confirm_container" android:background="@drawable/bg_query_confirm" android:visibility="visible" android:layout_width="0.0dip" android:layout_height="36.0dip" android:layout_weight="1.0" app:borderViewEndColor="@color/white_40" app:borderViewStartColor="@color/white" app:borderWidth="1.0dip" app:bottomLeftCornerRadius="8.0dip" app:bottomRightCornerRadius="8.0dip" app:gradientOrientation="vertical" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/iv_info" app:topLeftCornerRadius="8.0dip" app:topRightCornerRadius="8.0dip">
                <androidx.appcompat.widget.AppCompatTextView android:textSize="@dimen/text_size_14" android:textStyle="bold" android:textColor="#ff191f2b" android:gravity="center" android:id="@id/tv_confirm" android:layout_width="0.0dip" android:layout_height="36.0dip" android:text="@string/member_reward_confirm" android:layout_weight="1.0" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
            </com.transsion.baseui.widget.GradientBorderView>
        </androidx.appcompat.widget.LinearLayoutCompat>
    </androidx.constraintlayout.widget.ConstraintLayout>
</FrameLayout>
