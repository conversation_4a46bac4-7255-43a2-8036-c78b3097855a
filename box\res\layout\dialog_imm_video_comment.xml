<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:background="@drawable/libui_bottom_dialog_bg" android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <ImageView android:id="@id/iv_close" android:padding="4.0dip" android:layout_width="24.0dip" android:layout_height="24.0dip" android:layout_marginTop="16.0dip" android:src="@mipmap/ic_close_black" android:layout_marginEnd="12.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <FrameLayout android:id="@id/fl_comment_container" android:layout_width="fill_parent" android:layout_height="wrap_content" android:minHeight="200.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <View android:id="@id/v_bottom" android:background="@color/module_02" android:layout_width="0.0dip" android:layout_height="48.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" />
    <View android:id="@id/divider" android:background="@color/dialog_line" android:layout_width="fill_parent" android:layout_height="1.0dip" app:layout_constraintBottom_toTopOf="@id/v_bottom" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="@dimen/text_size_14" android:textColor="@color/text_03" android:textColorHint="@color/text_03" android:ellipsize="end" android:gravity="start|center" android:id="@id/tv_comment" android:background="@drawable/comment_input_edit_bg" android:layout_width="0.0dip" android:layout_height="36.0dip" android:hint="@string/comment_hint_add" android:paddingStart="12.0dip" android:paddingEnd="12.0dip" android:layout_marginStart="12.0dip" android:layout_marginEnd="16.0dip" app:layout_constraintBottom_toBottomOf="@id/v_bottom" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="@id/v_bottom" app:layout_goneMarginStart="16.0dip" />
</androidx.constraintlayout.widget.ConstraintLayout>
