<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:orientation="vertical" android:background="@drawable/libui_common_dialog_bg" android:paddingTop="20.0dip" android:paddingBottom="20.0dip" android:layout_width="wrap_content" android:layout_height="wrap_content" android:divider="@drawable/shape_lab_divider_line_all" android:minWidth="270.0dip" android:showDividers="middle"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <androidx.appcompat.widget.AppCompatTextView android:textSize="20.0sp" android:textColor="@color/text_01" android:gravity="center" android:layout_gravity="center_horizontal" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginBottom="10.0dip" android:text="brand" style="@style/style_extra_import_text" />
    <LinearLayout android:gravity="center_horizontal" android:background="@color/btn_bg_01" android:layout_width="fill_parent" android:layout_height="wrap_content">
        <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textColor="@color/text_02" android:gravity="center_vertical" android:layout_width="wrap_content" android:layout_height="40.0dip" android:text="current_brand" android:paddingStart="15.0dip" android:paddingEnd="15.0dip" />
        <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textColor="@color/main" android:gravity="center_vertical" android:id="@id/tv_lane_cur" android:layout_width="wrap_content" android:layout_height="40.0dip" android:text="" />
    </LinearLayout>
    <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textColor="@color/text_02" android:gravity="center" android:id="@id/tv_lane_tecno" android:background="?selectableItemBackground" android:layout_width="fill_parent" android:layout_height="40.0dip" android:text="TECNO" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textColor="@color/text_02" android:gravity="center" android:id="@id/tv_lane_infinix" android:background="?selectableItemBackground" android:layout_width="fill_parent" android:layout_height="40.0dip" android:text="Infinix" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textColor="@color/text_02" android:gravity="center" android:id="@id/tv_lane_itel" android:background="?selectableItemBackground" android:layout_width="fill_parent" android:layout_height="40.0dip" android:text="Itel" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textColor="@color/text_02" android:gravity="center" android:id="@id/tv_lane_samsung" android:background="?selectableItemBackground" android:layout_width="fill_parent" android:layout_height="40.0dip" android:text="samsung" />
    <LinearLayout android:gravity="center_vertical" android:orientation="horizontal" android:layout_width="fill_parent" android:layout_height="wrap_content" android:paddingStart="15.0dip" android:paddingEnd="15.0dip">
        <com.transsion.baseui.widget.EditTextWithClear android:textSize="16.0sp" android:textColor="@color/text_02" android:textColorHint="@color/text_03" android:id="@id/et_custom_brand" android:background="@null" android:layout_width="fill_parent" android:layout_height="40.0dip" android:hint="input custom brand" android:layout_weight="1.0" android:layout_marginEnd="15.0dip" />
        <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textColor="@color/text_02" android:gravity="center" android:id="@id/btn_custom_brand" android:background="@drawable/libui_sub_btn2_selector" android:paddingTop="5.0dip" android:paddingBottom="5.0dip" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="ok" android:paddingStart="15.0dip" android:paddingEnd="15.0dip" />
    </LinearLayout>
    <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textColor="@color/main" android:gravity="center" android:id="@id/tv_reset" android:background="?selectableItemBackground" android:layout_width="fill_parent" android:layout_height="40.0dip" android:text="Reset" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="14.0sp" android:textColor="@color/text_02" android:gravity="center" android:layout_gravity="center_horizontal" android:id="@id/btn_close" android:background="@drawable/libui_sub_btn2_selector" android:layout_width="116.0dip" android:layout_height="30.0dip" android:layout_marginTop="16.0dip" android:text="Cancel" />
</LinearLayout>
