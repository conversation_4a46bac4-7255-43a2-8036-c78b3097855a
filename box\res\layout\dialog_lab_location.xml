<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:background="@drawable/libui_common_dialog_bg" android:paddingTop="20.0dip" android:paddingBottom="20.0dip" android:layout_width="wrap_content" android:layout_height="wrap_content" android:minWidth="320.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textStyle="bold" android:textColor="@color/text_01" android:gravity="center" android:id="@id/tv_lat" android:layout_width="wrap_content" android:layout_height="50.0dip" android:text="latitude: " android:layout_marginStart="12.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <com.transsion.baseui.widget.EditTextWithClear android:textSize="16.0sp" android:textColor="@color/text_01" android:textColorHint="@color/text_03" android:id="@id/et_lat" android:layout_width="0.0dip" android:layout_height="wrap_content" android:hint="lat" android:layout_marginStart="8.0dip" android:layout_marginEnd="15.0dip" app:layout_constraintBottom_toBottomOf="@id/tv_lat" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/tv_lat" app:layout_constraintTop_toTopOf="@id/tv_lat" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textStyle="bold" android:textColor="@color/text_01" android:gravity="center" android:id="@id/tv_lon" android:layout_width="wrap_content" android:layout_height="50.0dip" android:text="longitude: " android:layout_marginStart="12.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tv_lat" />
    <com.transsion.baseui.widget.EditTextWithClear android:textSize="16.0sp" android:textColor="@color/text_01" android:textColorHint="@color/text_03" android:id="@id/et_lon" android:layout_width="0.0dip" android:layout_height="wrap_content" android:hint="lon" android:layout_marginStart="8.0dip" android:layout_marginEnd="15.0dip" app:layout_constraintBottom_toBottomOf="@id/tv_lon" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/tv_lon" app:layout_constraintTop_toTopOf="@id/tv_lon" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textStyle="bold" android:textColor="@color/text_01" android:gravity="center" android:id="@id/tv_name" android:layout_width="wrap_content" android:layout_height="50.0dip" android:text="Name: " android:layout_marginStart="12.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tv_lon" />
    <com.transsion.baseui.widget.EditTextWithClear android:textSize="16.0sp" android:textColor="@color/text_01" android:textColorHint="@color/text_03" android:id="@id/et_name" android:layout_width="0.0dip" android:layout_height="wrap_content" android:hint="name" android:layout_marginStart="8.0dip" android:layout_marginEnd="15.0dip" app:layout_constraintBottom_toBottomOf="@id/tv_name" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/tv_name" app:layout_constraintTop_toTopOf="@id/tv_name" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textStyle="bold" android:textColor="@color/text_01" android:gravity="center" android:id="@id/tv_address" android:layout_width="wrap_content" android:layout_height="50.0dip" android:text="Name: " android:layout_marginStart="12.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tv_name" />
    <com.transsion.baseui.widget.EditTextWithClear android:textSize="16.0sp" android:textColor="@color/text_01" android:textColorHint="@color/text_03" android:id="@id/et_address" android:layout_width="0.0dip" android:layout_height="wrap_content" android:hint="name" android:layout_marginStart="8.0dip" android:layout_marginEnd="15.0dip" app:layout_constraintBottom_toBottomOf="@id/tv_address" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/tv_name" app:layout_constraintTop_toTopOf="@id/tv_address" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textColor="@color/text_01" android:gravity="center" android:id="@id/btn_rest" android:background="@drawable/libui_sub_btn2_normal" android:layout_width="116.0dip" android:layout_height="36.0dip" android:layout_marginTop="16.0dip" android:text="Reset" android:layout_marginStart="4.0dip" android:layout_marginEnd="16.0dip" app:layout_constraintEnd_toStartOf="@id/btn_ok" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tv_address" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textColor="@color/base_color_white" android:gravity="center" android:id="@id/btn_ok" android:background="@drawable/libui_main_btn_selector" android:layout_width="116.0dip" android:layout_height="36.0dip" android:text="Mock" android:layout_marginStart="4.0dip" android:layout_marginEnd="16.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/btn_rest" app:layout_constraintTop_toTopOf="@id/btn_rest" />
</androidx.constraintlayout.widget.ConstraintLayout>
