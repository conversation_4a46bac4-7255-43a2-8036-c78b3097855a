<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:background="@drawable/libui_common_dialog_bg" android:paddingTop="20.0dip" android:paddingBottom="20.0dip" android:layout_width="wrap_content" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textStyle="bold" android:textColor="@color/text_01" android:gravity="center" android:id="@id/tv_title" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/enter_password" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <com.google.android.material.textfield.TextInputLayout android:id="@id/til_pwd" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginTop="5.0dip" android:layout_marginStart="30.0dip" android:layout_marginEnd="30.0dip" app:boxBackgroundColor="@color/bg_01" app:boxStrokeColor="@color/text_02" app:boxStrokeWidth="1.0dip" app:counterTextColor="@color/text_02" app:helperTextTextColor="@color/text_02" app:hintTextColor="@color/main" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tv_title">
        <EditText android:textColor="@color/text_02" android:id="@id/et_pwd" android:background="@color/transparent" android:layout_width="fill_parent" android:layout_height="wrap_content" android:inputType="textPassword" />
    </com.google.android.material.textfield.TextInputLayout>
    <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textColor="@color/text_01" android:gravity="center" android:id="@id/btn_cancel" android:background="@drawable/libui_sub_btn2_selector" android:layout_width="116.0dip" android:layout_height="36.0dip" android:layout_marginTop="15.0dip" android:text="@string/cancel" android:layout_marginStart="16.0dip" android:layout_marginEnd="4.0dip" app:layout_constraintEnd_toStartOf="@id/btn_ok" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/til_pwd" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textColor="@color/base_color_white" android:gravity="center" android:id="@id/btn_ok" android:background="@drawable/libui_main_btn_selector" android:layout_width="116.0dip" android:layout_height="36.0dip" android:text="@string/ok" android:layout_marginStart="4.0dip" android:layout_marginEnd="16.0dip" app:layout_constraintBottom_toBottomOf="@id/btn_cancel" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/btn_cancel" app:layout_constraintTop_toTopOf="@id/btn_cancel" />
</androidx.constraintlayout.widget.ConstraintLayout>
