<?xml version="1.0" encoding="utf-8"?>
<FrameLayout android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <LinearLayout android:orientation="vertical" android:background="@drawable/libui_common_dialog_bg" android:paddingLeft="20.0dip" android:paddingRight="20.0dip" android:layout_width="240.0dip" android:layout_height="wrap_content" android:layout_marginLeft="40.0dip" android:layout_marginRight="40.0dip">
        <TextView android:gravity="center" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="10.0dip" android:text="播放模式" />
        <RadioGroup android:orientation="vertical" android:id="@id/radioGroup" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="10.0dip">
            <RadioButton android:id="@id/radioButtonDownload" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="DOWNLOAD" />
            <RadioButton android:id="@id/radioButtonStream" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="STREAM" />
            <LinearLayout android:layout_gravity="center" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="10.0dip">
                <Button android:id="@id/btnCancel" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="Cancel" android:textAllCaps="false" />
                <View android:layout_width="0.0dip" android:layout_height="1.0dip" android:layout_weight="1.0" />
                <Button android:id="@id/btnOk" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="OK" />
            </LinearLayout>
        </RadioGroup>
    </LinearLayout>
</FrameLayout>
