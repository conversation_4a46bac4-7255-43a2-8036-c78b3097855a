<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:background="@drawable/libui_common_dialog_bg" android:paddingTop="20.0dip" android:paddingBottom="20.0dip" android:layout_width="wrap_content" android:layout_height="wrap_content" android:minWidth="320.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textStyle="bold" android:textColor="@color/text_01" android:gravity="center" android:id="@id/tv_title" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="input url" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <com.transsion.baseui.widget.EditTextWithClear android:textSize="16.0sp" android:textColor="@color/text_01" android:textColorHint="@color/text_03" android:id="@id/et_web" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginTop="16.0dip" android:hint="url" android:layout_marginStart="15.0dip" android:layout_marginEnd="15.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tv_title" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textColor="@color/base_color_white" android:gravity="center" android:id="@id/btn_ok" android:background="@drawable/libui_main_btn_selector" android:layout_width="116.0dip" android:layout_height="36.0dip" android:layout_marginTop="16.0dip" android:text="@string/ok" android:layout_marginStart="4.0dip" android:layout_marginEnd="16.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/et_web" />
</androidx.constraintlayout.widget.ConstraintLayout>
