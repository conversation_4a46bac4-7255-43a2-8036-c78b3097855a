<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout android:id="@id/comment_input_layout" android:background="@drawable/comment_input_bg" android:paddingBottom="6.0dip" android:layout_width="fill_parent" android:layout_height="wrap_content" android:minHeight="80.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <RelativeLayout android:id="@id/rl_add" android:paddingTop="12.0dip" android:layout_width="fill_parent" android:layout_height="40.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintRight_toRightOf="parent" app:layout_constraintTop_toTopOf="parent">
        <TextView android:textSize="14.0dip" android:textColor="@color/color_ff999999" android:id="@id/tv_cancel" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/cancel" android:paddingStart="16.0dip" android:paddingEnd="16.0dip" />
        <TextView android:textSize="14.0dip" android:textColor="@color/cl01" android:id="@id/tv_add" android:layout_width="wrap_content" android:layout_height="16.0dip" android:text="@string/add" android:paddingStart="30.0dip" android:paddingEnd="16.0dip" android:layout_alignParentEnd="true" />
    </RelativeLayout>
    <RelativeLayout android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_below="@id/rl_add">
        <com.transsion.publish.view.LinkEditText android:enabled="false" android:textSize="@dimen/text_size_14" android:textColor="@color/base_color_333333" android:textColorHint="@color/base_color_999999" android:gravity="start|top" android:autoLink="none" android:id="@id/comment_input_edit_text" android:background="@drawable/link_input_edit_bg" android:paddingTop="10.0dip" android:paddingBottom="10.0dip" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="6.0dip" android:layout_marginBottom="6.0dip" android:maxHeight="88.0dip" android:minHeight="35.0dip" android:hint="@string/comment_hint_input" android:cursorVisible="true" android:textCursorDrawable="@drawable/post_cursor" android:paddingStart="12.0dip" android:paddingEnd="77.0dip" android:layout_marginStart="16.0dip" android:layout_marginEnd="16.0dip" />
        <RelativeLayout android:id="@id/rl_clear" android:paddingTop="15.0dip" android:paddingBottom="15.0dip" android:visibility="invisible" android:layout_width="40.0dip" android:layout_height="wrap_content" android:layout_centerVertical="true" android:layout_alignParentEnd="true">
            <ImageView android:layout_width="14.0dip" android:layout_height="14.0dip" android:src="@mipmap/ic_input_close" android:layout_marginEnd="24.0dip" />
        </RelativeLayout>
    </RelativeLayout>
    <androidx.appcompat.widget.AppCompatTextView android:enabled="false" android:textSize="@dimen/text_size_14" android:textColor="@color/base_color_333333" android:textColorHint="@color/base_color_999999" android:ellipsize="end" android:gravity="start|top" android:autoLink="none" android:id="@id/tv_hint" android:paddingTop="10.0dip" android:paddingBottom="10.0dip" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="6.0dip" android:layout_marginBottom="6.0dip" android:maxHeight="88.0dip" android:minHeight="35.0dip" android:hint="@string/comment_hint_input" android:maxLines="1" android:singleLine="true" android:maxLength="200" android:layout_below="@id/rl_add" android:paddingStart="12.0dip" android:paddingEnd="57.0dip" android:layout_marginStart="15.0dip" android:layout_marginEnd="15.0dip" />
    <View android:id="@id/comment_input_disable_click" android:layout_width="fill_parent" android:layout_height="0.0dip" app:layout_constraintTop_toTopOf="parent" />
</RelativeLayout>
