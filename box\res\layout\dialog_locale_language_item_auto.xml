<?xml version="1.0" encoding="utf-8"?>
<FrameLayout android:background="?selectableItemBackground" android:layout_width="fill_parent" android:layout_height="55.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textColor="@color/text_01" android:layout_gravity="center_horizontal" android:id="@id/tv_title" android:layout_marginTop="10.0dip" android:text="@string/auto" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" style="@style/style_regula_bigger_text" />
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/text_02" android:layout_gravity="center_horizontal" android:id="@id/tv_language" android:layout_marginTop="31.0dip" android:text="@string/system_language" style="@style/style_regular_text" />
    <View android:layout_gravity="bottom" android:background="@color/line_01" android:layout_width="fill_parent" android:layout_height="1.0dip" />
</FrameLayout>
