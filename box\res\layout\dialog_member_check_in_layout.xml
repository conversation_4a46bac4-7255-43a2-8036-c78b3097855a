<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:gravity="center_horizontal" android:orientation="vertical" android:paddingLeft="40.0dip" android:paddingRight="40.0dip" android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.constraintlayout.widget.ConstraintLayout android:background="@drawable/bg_dialog_member_check_in_16" android:layout_width="fill_parent" android:layout_height="wrap_content">
        <View android:background="@mipmap/bg_member_check_in_title" android:layout_width="fill_parent" android:layout_height="64.0dip" app:layout_constraintTop_toTopOf="parent" />
        <ImageView android:id="@id/ic_tips" android:layout_width="96.0dip" android:layout_height="70.0dip" android:src="@mipmap/ic_member_check_in_top" android:scaleType="fitXY" android:importantForAccessibility="no" app:layout_constraintRight_toRightOf="parent" app:layout_constraintTop_toTopOf="parent" />
        <com.transsion.member.view.GradientTextView android:textSize="20.0sp" android:ellipsize="end" android:id="@id/title" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="24.0dip" android:text="@string/member_check_in_title" android:lines="2" android:layout_marginStart="16.0dip" android:layout_marginEnd="102.0dip" app:endColor="@color/check_in_dialog_view_title_e" app:layout_constraintLeft_toLeftOf="parent" app:layout_constraintRight_toLeftOf="@id/ic_tips" app:layout_constraintTop_toTopOf="parent" app:startColor="@color/check_in_dialog_view_title_e" style="@style/style_extra_import_text" />
        <TextView android:textSize="12.0sp" android:textStyle="normal" android:textColor="@color/text_02" android:gravity="center_vertical" android:id="@id/point_tips" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginTop="12.0dip" android:text="@string/member_check_in_points" android:drawablePadding="4.0dip" android:drawableStart="@mipmap/ic_member_points" android:layout_marginStart="16.0dip" app:layout_constraintEnd_toStartOf="@id/member_tips" app:layout_constraintHorizontal_weight="1.0" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/title" />
        <TextView android:textSize="12.0sp" android:textStyle="normal" android:textColor="@color/text_02" android:gravity="center_vertical" android:id="@id/member_tips" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginTop="12.0dip" android:text="@string/member_check_in_premium" android:drawablePadding="4.0dip" android:drawableStart="@mipmap/ic_member_small" android:layout_marginEnd="16.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintHorizontal_weight="1.0" app:layout_constraintStart_toEndOf="@id/point_tips" app:layout_constraintTop_toBottomOf="@id/title" />
        <com.transsion.member.view.CheckInView android:id="@id/member_check_in_view" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginLeft="13.0dip" android:layout_marginTop="12.0dip" android:layout_marginRight="13.0dip" app:civ_activeEndLayout="@layout/layout_item_check_in_end_active_style1" app:civ_activeItemLayout="@layout/layout_item_check_in_active_style1" app:civ_doneEndLayout="@layout/layout_item_check_in_end_done_style1" app:civ_doneItemLayout="@layout/layout_item_check_in_done_style1" app:civ_length="7" app:civ_unActiveEndLayout="@layout/layout_item_check_in_end_unactive_style1" app:civ_unActiveItemLayout="@layout/layout_item_check_in_unactive_style1" app:layout_constraintLeft_toLeftOf="parent" app:layout_constraintRight_toRightOf="parent" app:layout_constraintTop_toBottomOf="@id/point_tips" />
        <com.transsion.member.view.GradientTextView android:textSize="16.0sp" android:textStyle="bold" android:textColor="@color/text_12" android:id="@id/go_purchase_member" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginLeft="16.0dip" android:layout_marginTop="16.0dip" android:layout_marginRight="16.0dip" android:text="@string/member_check_in_guide_text" android:drawableRight="@mipmap/ic_member_go_purchase" app:endColor="@color/check_in_dialog_view_purchase_button_2" app:layout_constraintLeft_toLeftOf="parent" app:layout_constraintRight_toRightOf="parent" app:layout_constraintTop_toBottomOf="@id/member_check_in_view" app:startColor="@color/check_in_dialog_view_purchase_button_1" />
        <CheckBox android:textColor="@color/text_02" android:gravity="center_vertical" android:id="@id/dialog_ignore" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="12.0dip" android:layout_marginBottom="24.0dip" android:checked="false" android:button="@drawable/cb_member_dialog_ignore" android:text="@string/member_check_in_ignore_tips" android:paddingStart="2.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintLeft_toLeftOf="parent" app:layout_constraintRight_toRightOf="parent" app:layout_constraintTop_toBottomOf="@id/go_purchase_member" />
    </androidx.constraintlayout.widget.ConstraintLayout>
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/ivClose" android:layout_width="36.0dip" android:layout_height="36.0dip" android:layout_marginTop="30.0dip" android:src="@drawable/me_icon_popup_close" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" />
</LinearLayout>
