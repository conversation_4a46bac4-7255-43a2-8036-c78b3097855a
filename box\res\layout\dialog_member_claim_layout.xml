<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_gravity="center" android:id="@id/clRoot" android:background="@color/gray_dark_30" android:layout_width="fill_parent" android:layout_height="50.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/ivIcon" android:layout_width="40.0dip" android:layout_height="40.0dip" android:layout_marginTop="4.0dip" android:src="@mipmap/ic_premium_mask" android:layout_marginStart="@dimen/dp_12" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <TextView android:textSize="@dimen/sp_12" android:textStyle="bold" android:textColor="@color/white" android:id="@id/tvTitle" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginStart="60.0dip" app:layout_constraintBottom_toTopOf="@id/tvSubTitle" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:layout_constraintVertical_chainStyle="packed" />
    <TextView android:textSize="12.0sp" android:textColor="@color/white_60" android:id="@id/tvSubTitle" android:layout_width="wrap_content" android:layout_height="wrap_content" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="@id/tvTitle" app:layout_constraintTop_toBottomOf="@id/tvTitle" />
    <TextView android:textSize="@dimen/sp_12" android:textStyle="bold" android:textColor="@color/gray_dark_00" android:gravity="center" android:id="@id/btnClaim" android:background="@drawable/bg_member_btn_6" android:layout_width="100.0dip" android:layout_height="@dimen/dimens_24" android:text="@string/member_claim_now" android:layout_marginEnd="@dimen/dp_4" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toStartOf="@id/ivClose" app:layout_constraintTop_toTopOf="parent" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/ivClose" android:padding="@dimen/dp_4" android:layout_width="24.0dip" android:layout_height="wrap_content" android:src="@drawable/mbridge_reward_popview_close" android:layout_marginEnd="@dimen/dp_12" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>
