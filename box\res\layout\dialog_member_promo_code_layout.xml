<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:gravity="center_horizontal" android:orientation="vertical" android:background="@color/transparent" android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.transsion.baseui.widget.GradientBorderView android:id="@id/promo_code_title_layout" android:background="@drawable/bg_member_promo_code_container_top" android:layout_width="fill_parent" android:layout_height="wrap_content" app:borderViewEndColor="#00664d01" app:borderViewStartColor="#ff664d01" app:borderWidth="1.0dip" app:gradientOrientation="vertical" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:topLeftCornerRadius="12.0dip" app:topRightCornerRadius="12.0dip">
        <ImageView android:id="@id/promo_code_dialog_close" android:layout_width="20.0dip" android:layout_height="20.0dip" android:layout_margin="16.0dip" android:src="@mipmap/ic_gray_close" android:scaleType="fitXY" android:importantForAccessibility="no" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent" />
    </com.transsion.baseui.widget.GradientBorderView>
    <LinearLayout android:gravity="center_horizontal" android:orientation="vertical" android:background="@color/gray_dark_00" android:layout_width="fill_parent" android:layout_height="wrap_content">
        <ImageView android:layout_width="72.0dip" android:layout_height="72.0dip" android:layout_margin="16.0dip" android:src="@mipmap/img_member_promo_code" android:scaleType="fitXY" android:importantForAccessibility="no" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent" />
        <TextView android:textSize="16.0sp" android:textColor="@color/yellow_light_50" android:gravity="center" android:layout_width="fill_parent" android:layout_height="wrap_content" android:text="@string/member_promo_code_desc" />
        <androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginLeft="28.0dip" android:layout_marginTop="20.0dip" android:layout_marginRight="28.0dip">
            <androidx.appcompat.widget.AppCompatEditText android:textSize="16.0sp" android:textColor="@color/white" android:ellipsize="start" android:id="@id/promo_code_input_et" android:background="@drawable/bg_member_promo_code_input" android:padding="12.0dip" android:layout_width="fill_parent" android:layout_height="wrap_content" android:singleLine="true" android:maxLength="30" android:inputType="textVisiblePassword" android:textCursorDrawable="@drawable/member_input_cursor" android:paddingEnd="40.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
            <ImageView android:id="@id/promo_code_input_clear_iv" android:visibility="gone" android:layout_width="20.0dip" android:layout_height="20.0dip" android:src="@mipmap/ic_input_close_2" android:layout_centerVertical="true" android:importantForAccessibility="no" android:layout_marginEnd="12.0dip" android:layout_alignParentEnd="true" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>
        <TextView android:textSize="14.0sp" android:textColor="@color/error_50" android:id="@id/promo_code_input_error_tips" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginLeft="28.0dip" android:layout_marginTop="4.0dip" android:layout_marginRight="28.0dip" />
        <TextView android:textSize="16.0sp" android:textStyle="bold" android:textColor="@color/gray_dark_00" android:gravity="center" android:id="@id/promo_code_input_confirm" android:background="@drawable/bg_btn_4_selector" android:layout_width="fill_parent" android:layout_height="40.0dip" android:layout_marginLeft="28.0dip" android:layout_marginTop="46.0dip" android:layout_marginRight="28.0dip" android:layout_marginBottom="16.0dip" android:text="@string/member_promo_code_apply" />
    </LinearLayout>
</LinearLayout>
