<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:layout_gravity="bottom" android:orientation="vertical" android:background="@drawable/libui_bottom_dialog_bg_12dp" android:focusable="true" android:clickable="true" android:layout_width="fill_parent" android:layout_height="153.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/text_06" android:gravity="center" android:layout_width="fill_parent" android:layout_height="48.0dip" android:text="@string/post_confirm_title" />
    <View android:background="@color/dialog_line" android:layout_width="fill_parent" android:layout_height="1.0dip" />
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/base_color_FA5546" android:gravity="center" android:id="@id/post_confirm" android:layout_width="fill_parent" android:layout_height="48.0dip" android:text="@string/post_confirm" />
    <View android:background="@color/dialog_line" android:layout_width="fill_parent" android:layout_height="8.0dip" />
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/text_06" android:gravity="center" android:id="@id/post_cancel" android:layout_width="fill_parent" android:layout_height="48.0dip" android:text="@string/post_cancel" />
</LinearLayout>
