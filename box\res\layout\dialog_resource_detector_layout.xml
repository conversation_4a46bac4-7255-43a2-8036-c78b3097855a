<?xml version="1.0" encoding="utf-8"?>
<FrameLayout android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.constraintlayout.widget.ConstraintLayout android:layout_gravity="bottom" android:background="@drawable/bg_download_detail" android:layout_width="fill_parent" android:layout_height="wrap_content" android:paddingStart="16.0dip" android:paddingEnd="16.0dip">
        <androidx.appcompat.widget.AppCompatImageView android:id="@id/ivClose" android:layout_width="16.0dip" android:layout_height="16.0dip" android:layout_marginTop="12.0dip" android:src="@mipmap/ic_close_black" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent" />
        <androidx.appcompat.widget.AppCompatTextView android:textSize="18.0sp" android:textStyle="bold" android:textColor="@color/text_01" android:ellipsize="end" android:id="@id/tvTitle" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginTop="4.0dip" android:maxLines="1" android:textAlignment="viewStart" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/ivClose" />
        <androidx.appcompat.widget.AppCompatTextView android:textSize="14.0sp" android:textColor="@color/text_02" android:ellipsize="end" android:id="@id/tvUrl" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginTop="8.0dip" android:maxLines="2" android:textAlignment="viewStart" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tvTitle" />
        <androidx.appcompat.widget.AppCompatTextView android:textSize="14.0sp" android:textColor="@color/text_02" android:id="@id/tvSourceTitle" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="16.0dip" android:text="@string/base_ui_source" app:layout_constraintEnd_toStartOf="@id/tvSourceData" app:layout_constraintHorizontal_chainStyle="spread" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tvUrl" />
        <androidx.appcompat.widget.AppCompatTextView android:textSize="14.0sp" android:ellipsize="end" android:id="@id/tvSourceData" android:layout_width="0.0dip" android:layout_height="wrap_content" android:maxLines="1" android:textAlignment="viewStart" app:layout_constraintBottom_toBottomOf="@id/tvSourceTitle" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/tvSourceTitle" app:layout_constraintTop_toTopOf="@id/tvSourceTitle" />
        <androidx.appcompat.widget.AppCompatTextView android:textSize="14.0sp" android:textColor="@color/text_02" android:id="@id/tvSizeTitle" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="8.0dip" android:text="@string/base_ui_size" app:layout_constraintStart_toStartOf="@id/tvSourceTitle" app:layout_constraintTop_toBottomOf="@id/tvSourceTitle" />
        <androidx.appcompat.widget.AppCompatTextView android:textSize="14.0sp" android:textColor="@color/text_01" android:ellipsize="end" android:id="@id/tvSizeData" android:layout_width="0.0dip" android:layout_height="wrap_content" android:maxLines="1" android:textAlignment="viewStart" app:layout_constraintBottom_toBottomOf="@id/tvSizeTitle" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/tvSizeTitle" app:layout_constraintTop_toTopOf="@id/tvSizeTitle" />
        <androidx.appcompat.widget.AppCompatTextView android:textSize="14.0sp" android:textColor="@color/text_02" android:id="@id/tvDateTitle" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="8.0dip" android:text="@string/base_ui_date" app:layout_constraintStart_toStartOf="@id/tvSourceTitle" app:layout_constraintTop_toBottomOf="@id/tvSizeTitle" />
        <androidx.appcompat.widget.AppCompatTextView android:textSize="14.0sp" android:textColor="@color/text_01" android:ellipsize="end" android:id="@id/tvDateData" android:layout_width="0.0dip" android:layout_height="wrap_content" android:maxLines="1" android:textAlignment="viewStart" app:layout_constraintBottom_toBottomOf="@id/tvDateTitle" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/tvDateTitle" app:layout_constraintTop_toTopOf="@id/tvDateTitle" />
        <androidx.appcompat.widget.AppCompatTextView android:textSize="14.0sp" android:textColor="@color/text_02" android:id="@id/tvUploadedBy" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginTop="16.0dip" android:layout_marginBottom="48.0dip" android:text="@string/base_ui_uploaded_by" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tvDateTitle" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</FrameLayout>
