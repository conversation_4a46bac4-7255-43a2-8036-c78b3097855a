<?xml version="1.0" encoding="utf-8"?>
<com.noober.background.view.BLLinearLayout android:orientation="vertical" android:paddingLeft="16.0dip" android:paddingTop="24.0dip" android:paddingRight="16.0dip" android:layout_width="fill_parent" android:layout_height="wrap_content" app:bl_corners_radius="16.0dip" app:bl_solid_color="@color/gray_dark_30"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/white" android:gravity="center" android:layout_gravity="center_horizontal" android:id="@id/tv_title" android:text="@string/notification" style="@style/style_title_text" />
    <com.tn.lib.view.MaxHeightNestedScrollView android:id="@id/ns_tips" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="16.0dip">
        <androidx.appcompat.widget.AppCompatTextView android:textSize="14.0sp" android:textColor="@color/white" android:gravity="center" android:id="@id/tv_tips" android:layout_width="fill_parent" android:layout_height="wrap_content" android:text="@string/video_float_tips" />
    </com.tn.lib.view.MaxHeightNestedScrollView>
    <com.noober.background.view.BLTextView android:textColor="@color/common_white" android:gravity="center" android:id="@id/tv_confirm" android:layout_width="fill_parent" android:layout_height="36.0dip" android:layout_marginTop="16.0dip" android:text="@string/movie_detail_confirm_and_watch" app:bl_corners_radius="4.0dip" app:bl_gradient_angle="0" app:bl_gradient_centerColor="@color/main_gradient_center" app:bl_gradient_endColor="@color/main_gradient_end" app:bl_gradient_startColor="@color/main_gradient_start" style="@style/style_title_text" />
    <com.noober.background.view.BLTextView android:textColor="@color/common_white" android:gravity="center" android:id="@id/tv_back" android:layout_width="fill_parent" android:layout_height="36.0dip" android:layout_marginTop="8.0dip" android:text="@string/back" app:bl_corners_radius="4.0dip" app:bl_solid_color="@color/white_10" style="@style/style_title_text" />
    <androidx.appcompat.widget.LinearLayoutCompat android:layout_gravity="center_horizontal" android:orientation="horizontal" android:id="@id/ll_select" android:paddingTop="8.0dip" android:paddingBottom="24.0dip" android:layout_width="wrap_content" android:layout_height="wrap_content">
        <ImageView android:layout_gravity="center_vertical" android:id="@id/iv_select" android:layout_width="12.0dip" android:layout_height="12.0dip" android:layout_marginEnd="4.0dip" app:srcCompat="@drawable/selector_check_square" />
        <TextView android:textSize="14.0sp" android:textColor="@color/subtitle_text_tips" android:layout_gravity="center_vertical" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginBottom="0.5dip" android:text="@string/no_subtitle_again" />
    </androidx.appcompat.widget.LinearLayoutCompat>
</com.noober.background.view.BLLinearLayout>
