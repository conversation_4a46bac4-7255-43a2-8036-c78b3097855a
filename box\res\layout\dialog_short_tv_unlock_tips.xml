<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:background="@drawable/post_detail_shape_short_tv_dialog_bottom_bg" android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_close" android:padding="16.0dip" android:layout_width="wrap_content" android:layout_height="wrap_content" android:src="@mipmap/web_ic_close" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <androidx.constraintlayout.widget.ConstraintLayout android:id="@id/iv_bg" android:background="@drawable/post_detail_shape_short_tv_ad_tips_bg" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginLeft="16.0dip" android:layout_marginTop="64.0dip" android:layout_marginRight="16.0dip" android:layout_marginBottom="64.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintTop_toTopOf="parent">
        <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_ad" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginStart="16.0dip" app:layout_constraintBottom_toBottomOf="@id/iv_bg" app:layout_constraintStart_toStartOf="@id/iv_bg" app:layout_constraintTop_toTopOf="@id/iv_bg" app:srcCompat="@mipmap/ic_short_tv_ad" />
        <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textStyle="bold" android:textColor="@color/white" android:id="@id/tv_watch" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginTop="13.0dip" android:text="@string/short_tv_watch_ad" android:layout_marginStart="7.0dip" app:layout_constraintEnd_toStartOf="@id/tv_unlock" app:layout_constraintStart_toEndOf="@id/iv_ad" app:layout_constraintTop_toTopOf="@id/iv_bg" style="@style/style_medium_text" />
        <androidx.appcompat.widget.AppCompatTextView android:textSize="14.0sp" android:textStyle="bold" android:textColor="#ff07b84e" android:gravity="start" android:id="@id/tv_episode" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginLeft="7.0dip" android:layout_marginRight="7.0dip" android:layout_marginBottom="15.0dip" android:text="@string/short_tv_watch_ad_tips" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toEndOf="@id/iv_ad" app:layout_constraintTop_toBottomOf="@id/tv_watch" style="@style/style_medium_text" />
        <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textColor="@color/white" android:gravity="center" android:id="@id/tv_unlock" android:background="@drawable/libui_main_btn_selector" android:paddingLeft="8.0dip" android:paddingRight="8.0dip" android:layout_width="wrap_content" android:layout_height="32.0dip" android:layout_marginLeft="16.0dip" android:layout_marginRight="16.0dip" android:minWidth="83.0dip" android:text="@string/short_tv_unlock" app:layout_constraintBottom_toBottomOf="@id/iv_bg" app:layout_constraintEnd_toEndOf="@id/iv_bg" app:layout_constraintTop_toTopOf="@id/iv_bg" style="@style/style_medium_text" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
