<?xml version="1.0" encoding="utf-8"?>
<FrameLayout android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.constraintlayout.widget.ConstraintLayout android:background="@drawable/bg_speech_recognizer" android:layout_width="fill_parent" android:layout_height="fill_parent">
        <FrameLayout android:id="@id/llTop" android:background="@drawable/bg_sr_dialog_top" android:layout_width="0.0dip" android:layout_height="44.0dip" app:layout_constraintBottom_toTopOf="@id/tvResult" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent">
            <androidx.appcompat.widget.AppCompatImageView android:layout_gravity="end|center" android:id="@id/ivClose" android:padding="8.0dip" android:layout_width="44.0dip" android:layout_height="fill_parent" android:src="@drawable/sr_close" android:layout_marginEnd="4.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent" />
        </FrameLayout>
        <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textColor="@color/text_01" android:gravity="start" android:id="@id/tvResult" android:layout_width="0.0dip" android:layout_height="0.0dip" android:layout_marginLeft="16.0dip" android:layout_marginRight="16.0dip" app:layout_constraintBottom_toTopOf="@id/viewBg" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/llTop" />
        <View android:id="@id/viewBg" android:layout_width="140.0dip" android:layout_height="140.0dip" android:layout_marginTop="24.0dip" app:layout_constraintBottom_toTopOf="@id/tvTip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tvResult" />
        <androidx.appcompat.widget.AppCompatTextView android:id="@id/tvTip" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="12.0dip" android:layout_marginBottom="44.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/viewBg" />
        <com.transsion.search.speech.SpeechRecognizerVolumeCircleView android:id="@id/volumeCircleView" android:layout_width="110.0dip" android:layout_height="110.0dip" app:layout_constraintBottom_toBottomOf="@id/viewBg" app:layout_constraintEnd_toEndOf="@id/viewBg" app:layout_constraintStart_toStartOf="@id/viewBg" app:layout_constraintTop_toTopOf="@id/viewBg" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</FrameLayout>
