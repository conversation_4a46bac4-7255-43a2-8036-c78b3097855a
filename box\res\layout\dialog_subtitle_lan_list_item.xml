<?xml version="1.0" encoding="utf-8"?>
<androidx.appcompat.widget.LinearLayoutCompat android:gravity="center_vertical" android:background="?selectableItemBackground" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layoutDirection="locale"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/white" android:ellipsize="end" android:gravity="center_vertical" android:id="@id/tvSubtitle" android:layout_width="0.0dip" android:layout_height="40.0dip" android:maxLines="1" android:layout_weight="1.0" android:textDirection="locale" android:layout_marginStart="24.0dip" style="@style/style_medium_text" />
    <androidx.appcompat.widget.AppCompatTextView android:id="@id/ivBilingual" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginStart="16.0dip" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/ivState" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginStart="16.0dip" android:layout_marginEnd="16.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:srcCompat="@drawable/selector_download_group_check" />
    <ProgressBar android:id="@id/progress" android:visibility="gone" android:layout_width="16.0dip" android:layout_height="16.0dip" android:layout_marginStart="16.0dip" android:layout_marginEnd="16.0dip" android:indeterminateTint="@color/brand" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
</androidx.appcompat.widget.LinearLayoutCompat>
