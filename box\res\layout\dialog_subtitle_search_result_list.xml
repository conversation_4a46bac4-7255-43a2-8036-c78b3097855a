<?xml version="1.0" encoding="utf-8"?>
<FrameLayout android:id="@id/fl_root" android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.recyclerview.widget.RecyclerView android:id="@id/recycler_view" android:layout_width="fill_parent" android:layout_height="wrap_content" android:minHeight="120.0dip" app:layout_constrainedHeight="true" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tv_title" />
    <androidx.appcompat.widget.LinearLayoutCompat android:gravity="center" android:id="@id/ll_loading" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="fill_parent" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent">
        <ProgressBar android:layout_gravity="center" android:id="@id/view_load" android:layout_width="24.0dip" android:layout_height="24.0dip" android:indeterminateTint="@color/white" />
    </androidx.appcompat.widget.LinearLayoutCompat>
</FrameLayout>
