<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:background="@drawable/libui_common_dialog_bg" android:paddingTop="28.0dip" android:paddingBottom="24.0dip" android:layout_width="wrap_content" android:layout_height="wrap_content" android:maxWidth="280.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textStyle="bold" android:textColor="@color/text_01" android:gravity="center" android:id="@id/tv_title" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginLeft="24.0dip" android:layout_marginRight="24.0dip" android:text="@string/transfer_disconnect_title" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="14.0sp" android:textColor="@color/text_01" android:gravity="center" android:id="@id/tv_tips" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginTop="16.0dip" android:text="@string/transfer_disconnect_tips" android:layout_marginStart="24.0dip" android:layout_marginEnd="24.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tv_title" />
    <View android:id="@id/v_ling_1" android:layout_width="fill_parent" android:layout_height="1.0dip" android:layout_marginTop="16.0dip" app:layout_constraintTop_toBottomOf="@id/tv_tips" />
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/text_01" android:gravity="center" android:id="@id/btn_no" android:background="@drawable/bg_shape_confirm_dialog_btn" android:layout_width="0.0dip" android:layout_height="36.0dip" android:text="@string/transfer_cancel" android:layout_marginStart="24.0dip" android:layout_marginEnd="4.0dip" app:layout_constraintEnd_toStartOf="@id/btn_yes" app:layout_constraintHorizontal_weight="1.0" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/v_ling_1" style="@style/style_medium_text" />
    <com.noober.background.view.BLTextView android:textColor="@color/white" android:gravity="center" android:id="@id/btn_yes" android:layout_width="0.0dip" android:layout_height="36.0dip" android:text="@string/transfer_disconnect" android:layout_marginStart="4.0dip" android:layout_marginEnd="24.0dip" app:bl_corners_radius="8.0dip" app:bl_solid_color="@color/error_red" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintHorizontal_weight="1.0" app:layout_constraintStart_toEndOf="@id/btn_no" app:layout_constraintTop_toBottomOf="@id/v_ling_1" style="@style/style_medium_text" />
</androidx.constraintlayout.widget.ConstraintLayout>
