<?xml version="1.0" encoding="utf-8"?>
<merge android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <androidx.appcompat.widget.LinearLayoutCompat android:gravity="center" android:layout_gravity="center" android:id="@id/ll_download_inner" android:layout_width="fill_parent" android:layout_height="fill_parent">
        <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_icon" android:layout_width="16.0dip" android:layout_height="16.0dip" android:src="@mipmap/ic_download_red" />
        <com.tn.lib.widget.TnTextView android:textSize="12.0sp" android:textColor="@color/white" android:ellipsize="end" android:id="@id/tv_tips" android:text="@string/download_movie" android:maxLines="1" android:layout_marginStart="2.0dip" style="@style/style_medium_text" />
    </androidx.appcompat.widget.LinearLayoutCompat>
</merge>
