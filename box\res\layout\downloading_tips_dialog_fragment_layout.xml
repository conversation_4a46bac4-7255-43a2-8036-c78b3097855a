<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:background="@drawable/bg_btn_dialog_gray" android:focusable="true" android:clickable="true" android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <View android:background="@drawable/bg_btn_module_07" android:layout_width="fill_parent" android:layout_height="48.0dip" android:layout_marginTop="2.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/ivDownload" android:layout_width="24.0dip" android:layout_height="24.0dip" android:layout_marginTop="12.0dip" android:layout_marginStart="16.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:srcCompat="@drawable/icon_light_download_black" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="14.0sp" android:textColor="@color/text_01" android:id="@id/tvTitle" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/str_downloading_title" android:layout_marginStart="8.0dip" app:layout_constraintBottom_toBottomOf="@id/ivDownload" app:layout_constraintStart_toEndOf="@id/ivDownload" app:layout_constraintTop_toTopOf="@id/ivDownload" />
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/main" android:id="@id/tvFileSize" android:layout_width="wrap_content" android:layout_height="wrap_content" app:layout_constraintBottom_toBottomOf="@id/tvTitle" app:layout_constraintStart_toEndOf="@id/tvTitle" app:layout_constraintTop_toTopOf="@id/tvTitle" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="14.0sp" android:textColor="@color/text_01" android:id="@id/tvTitleCopy" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/str_downloading_file_size" app:layout_constraintBottom_toBottomOf="@id/tvTitle" app:layout_constraintStart_toEndOf="@id/tvFileSize" app:layout_constraintTop_toTopOf="@id/tvTitle" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/ivClose" android:layout_width="wrap_content" android:layout_height="wrap_content" android:src="@mipmap/icon_download_light_close" android:layout_marginEnd="16.0dip" app:layout_constraintBottom_toBottomOf="@id/ivDownload" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="@id/ivDownload" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="14.0sp" android:textColor="@color/text_01" android:id="@id/tvDes" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginTop="18.0dip" android:text="@string/str_downloading_tips_new" android:layout_marginStart="16.0dip" android:layout_marginEnd="16.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/ivDownload" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="14.0sp" android:textColor="@color/text_01" android:gravity="center" android:id="@id/tvBtnDetails" android:background="@drawable/bg_btn_module_05" android:layout_width="0.0dip" android:layout_height="36.0dip" android:layout_marginTop="30.0dip" android:layout_marginBottom="16.0dip" android:text="@string/view_downloads" android:layout_marginStart="16.0dip" app:layout_constraintBottom_toTopOf="@id/card_native_ad" app:layout_constraintEnd_toStartOf="@id/tvBtnPlayNow" app:layout_constraintHorizontal_chainStyle="spread" app:layout_constraintHorizontal_weight="1.0" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tvDes" style="@style/style_medium_text" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="14.0sp" android:textColor="@color/white" android:gravity="center" android:id="@id/tvBtnPlayNow" android:background="@drawable/bg_btn_01" android:layout_width="0.0dip" android:layout_height="36.0dip" android:text="@string/watch_now" android:layout_marginStart="16.0dip" android:layout_marginEnd="16.0dip" app:layout_constraintBottom_toBottomOf="@id/tvBtnDetails" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintHorizontal_weight="1.0" app:layout_constraintStart_toEndOf="@id/tvBtnDetails" app:layout_constraintTop_toTopOf="@id/tvBtnDetails" style="@style/style_medium_text" />
    <androidx.cardview.widget.CardView android:id="@id/card_native_ad" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginBottom="16.0dip" android:layout_marginStart="16.0dip" android:layout_marginEnd="16.0dip" app:cardBackgroundColor="@color/module_02" app:cardCornerRadius="8.0dip" app:cardElevation="0.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent">
        <FrameLayout android:id="@id/adContainer" android:layout_width="fill_parent" android:layout_height="wrap_content" />
    </androidx.cardview.widget.CardView>
</androidx.constraintlayout.widget.ConstraintLayout>
