<?xml version="1.0" encoding="utf-8"?>
<merge
  xmlns:android="http://schemas.android.com/apk/res/android">
    <View android:id="@id/exo_controls_background" android:background="@color/exo_black_opacity_60" android:layout_width="0.0dip" android:layout_height="0.0dip" />
    <FrameLayout android:layout_gravity="bottom" android:id="@id/exo_bottom_bar" android:background="@color/exo_bottom_bar_background" android:layout_width="fill_parent" android:layout_height="@dimen/exo_styled_bottom_bar_height" android:layout_marginTop="@dimen/exo_styled_bottom_bar_margin_top" android:layoutDirection="ltr">
        <LinearLayout android:layout_gravity="start|center" android:id="@id/exo_time" android:paddingLeft="@dimen/exo_styled_bottom_bar_time_padding" android:paddingRight="@dimen/exo_styled_bottom_bar_time_padding" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layoutDirection="ltr" android:paddingStart="@dimen/exo_styled_bottom_bar_time_padding" android:paddingEnd="@dimen/exo_styled_bottom_bar_time_padding">
            <TextView android:id="@id/exo_position" style="@style/ExoStyledControls.TimeText.Position" />
            <TextView style="@style/ExoStyledControls.TimeText.Separator" />
            <TextView android:id="@id/exo_duration" style="@style/ExoStyledControls.TimeText.Duration" />
        </LinearLayout>
        <LinearLayout android:layout_gravity="end|center" android:id="@id/exo_basic_controls" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layoutDirection="ltr">
            <ImageButton android:id="@id/exo_vr" style="@style/ExoStyledControls.Button.Bottom.VR" />
            <ImageButton android:id="@id/exo_shuffle" style="@style/ExoStyledControls.Button.Bottom.Shuffle" />
            <ImageButton android:id="@id/exo_repeat_toggle" style="@style/ExoStyledControls.Button.Bottom.RepeatToggle" />
            <ImageButton android:id="@id/exo_subtitle" style="@style/ExoStyledControls.Button.Bottom.CC" />
            <ImageButton android:id="@id/exo_settings" style="@style/ExoStyledControls.Button.Bottom.Settings" />
            <ImageButton android:id="@id/exo_fullscreen" style="@style/ExoStyledControls.Button.Bottom.FullScreen" />
            <ImageButton android:id="@id/exo_overflow_show" style="@style/ExoStyledControls.Button.Bottom.OverflowShow" />
        </LinearLayout>
        <HorizontalScrollView android:layout_gravity="end|center" android:id="@id/exo_extra_controls_scroll_view" android:visibility="invisible" android:layout_width="wrap_content" android:layout_height="wrap_content">
            <LinearLayout android:id="@id/exo_extra_controls" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layoutDirection="ltr">
                <ImageButton android:id="@id/exo_overflow_hide" style="@style/ExoStyledControls.Button.Bottom.OverflowHide" />
            </LinearLayout>
        </HorizontalScrollView>
    </FrameLayout>
    <View android:layout_gravity="bottom" android:id="@id/exo_progress_placeholder" android:layout_width="fill_parent" android:layout_height="@dimen/exo_styled_progress_layout_height" android:layout_marginBottom="@dimen/exo_styled_progress_margin_bottom" />
    <LinearLayout android:gravity="center_vertical" android:layout_gravity="end|bottom" android:orientation="horizontal" android:id="@id/exo_minimal_controls" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginBottom="@dimen/exo_styled_minimal_controls_margin_bottom" android:layoutDirection="ltr">
        <ImageButton android:id="@id/exo_minimal_fullscreen" style="@style/ExoStyledControls.Button.Bottom.FullScreen" />
    </LinearLayout>
    <LinearLayout android:gravity="center" android:layout_gravity="center" android:id="@id/exo_center_controls" android:background="@android:color/transparent" android:padding="@dimen/exo_styled_controls_padding" android:clipToPadding="false" android:layout_width="wrap_content" android:layout_height="wrap_content">
        <ImageButton android:id="@id/exo_prev" style="@style/ExoStyledControls.Button.Center.Previous" />
        <include layout="@layout/exo_styled_player_control_rewind_button" />
        <ImageButton android:id="@id/exo_play_pause" style="@style/ExoStyledControls.Button.Center.PlayPause" />
        <include layout="@layout/exo_styled_player_control_ffwd_button" />
        <ImageButton android:id="@id/exo_next" style="@style/ExoStyledControls.Button.Center.Next" />
    </LinearLayout>
</merge>
