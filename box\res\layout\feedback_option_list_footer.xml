<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textColor="@color/text_01" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="20.0dip" android:layout_marginBottom="8.0dip" android:text="@string/feedback_tell_us_little_more" android:includeFontPadding="false" style="@style/robot_medium" />
    <androidx.constraintlayout.widget.ConstraintLayout android:background="@drawable/shape_clip_corners_1" android:padding="12.0dip" android:layout_width="fill_parent" android:layout_height="180.0dip">
        <com.transsion.usercenter.setting.labelsfeedback.UCEditText android:textSize="14.0sp" android:textColor="@color/pair_text_191F2B" android:textColorHint="@color/pair_text_92969E" android:gravity="start" android:id="@id/editText" android:paddingTop="0.0dip" android:layout_width="0.0dip" android:layout_height="99.0dip" android:hint="@string/feedback_please_input_description" android:maxLength="500" android:backgroundTint="@color/clear" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintHorizontal_bias="0.0" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
        <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/pair_text_92969E" android:id="@id/countTextView" android:layout_width="wrap_content" android:layout_height="wrap_content" app:layout_constraintBottom_toBottomOf="@id/editText" app:layout_constraintEnd_toEndOf="@id/editText" />
        <View android:id="@id/view6" android:background="@color/pair_E4E6EB" android:layout_width="fill_parent" android:layout_height="1.0dip" android:layout_marginTop="12.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/editText" />
        <com.google.android.material.imageview.ShapeableImageView android:id="@id/imageView" android:background="@color/pair_EDF0F5" android:layout_width="32.0dip" android:layout_height="32.0dip" android:layout_marginTop="12.0dip" android:src="@mipmap/user_add_image1" android:scaleType="center" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/view6" app:shapeAppearanceOverlay="@style/roundStyle_4" />
        <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/pair_text_61656D" android:layout_width="0.0dip" android:layout_height="wrap_content" android:text="@string/feedback_provide_screenshot" android:layout_marginStart="8.0dip" app:layout_constraintBottom_toBottomOf="@id/imageView" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/imageView" app:layout_constraintTop_toTopOf="@id/imageView" />
    </androidx.constraintlayout.widget.ConstraintLayout>
    <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textColor="@color/pair_text_191F2B" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="20.0dip" android:layout_marginBottom="8.0dip" android:text="@string/feedback_can_get_back" style="@style/robot_medium" />
    <LinearLayout android:orientation="horizontal" android:background="@drawable/shape_clip_corners_1" android:layout_width="fill_parent" android:layout_height="40.0dip">
        <androidx.appcompat.widget.AppCompatEditText android:textSize="14.0sp" android:textColor="@color/pair_text_191F2B" android:textColorHint="@color/pair_text_92969E" android:gravity="center" android:id="@id/countryEditText" android:focusable="false" android:focusableInTouchMode="false" android:layout_width="wrap_content" android:layout_height="fill_parent" android:hint="@string/login_select_country_code" android:maxLines="1" android:inputType="none" android:paddingStart="12.0dip" android:paddingEnd="5.0dip" android:backgroundTint="@color/clear" />
        <androidx.appcompat.widget.AppCompatImageView android:layout_gravity="center_vertical" android:layout_width="wrap_content" android:layout_height="wrap_content" android:src="@mipmap/login_arrow_down" android:tint="@color/text_01" />
        <View android:layout_gravity="center_vertical" android:background="@color/pair_E4E6EB" android:layout_width="0.5dip" android:layout_height="16.0dip" android:layout_marginStart="5.0dip" android:layout_marginEnd="5.0dip" />
        <androidx.appcompat.widget.AppCompatEditText android:textSize="14.0sp" android:textColor="@color/pair_text_191F2B" android:textColorHint="@color/pair_text_92969E" android:gravity="center_vertical" android:id="@id/phoneEditText" android:layout_width="0.0dip" android:layout_height="fill_parent" android:hint="@string/phone_number" android:maxLines="1" android:layout_weight="1.0" android:inputType="number" android:backgroundTint="@color/clear" />
    </LinearLayout>
</LinearLayout>
