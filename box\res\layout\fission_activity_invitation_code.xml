<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:fitsSystemWindows="true" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.tn.lib.view.TitleLayout android:id="@id/tool_bar" android:background="@color/module_01" android:layout_width="fill_parent" android:layout_height="wrap_content" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <androidx.appcompat.widget.AppCompatEditText android:textSize="@dimen/hisavana_ad_dimen_14sp" android:textColorHint="#ff92969e" android:gravity="start" android:id="@id/et_invitation_code" android:background="#ffedf0f5" android:padding="12.0dip" android:layout_width="fill_parent" android:layout_height="112.0dip" android:layout_marginLeft="16.0dip" android:layout_marginTop="64.0dip" android:layout_marginRight="16.0dip" android:hint="@string/fission_invitation_code_hint" android:maxLines="1" android:singleLine="true" android:maxLength="8" android:inputType="number" app:layout_constraintTop_toTopOf="parent" />
    <androidx.appcompat.widget.AppCompatButton android:textColor="@color/base_color_white" android:gravity="center" android:layout_gravity="end" android:id="@id/btn_submit" android:background="@drawable/fission_selector_submit_btn" android:layout_width="fill_parent" android:layout_height="38.0dip" android:layout_marginLeft="16.0dip" android:layout_marginRight="16.0dip" android:layout_marginBottom="48.0dip" android:text="@string/submit" android:textAllCaps="false" app:layout_constraintBottom_toBottomOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>
