<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:orientation="vertical" android:background="@color/module_02" android:layout_width="fill_parent" android:layout_height="wrap_content" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintTop_toTopOf="parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <TextView android:textSize="16.0sp" android:layout_gravity="center_horizontal" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="24.0dip" android:text="@string/fission_what_invitation_code" style="@style/style_title_text" />
    <TextView android:textSize="14.0sp" android:textColor="@color/text_01" android:gravity="center" android:layout_gravity="center_horizontal" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginLeft="24.0dip" android:layout_marginTop="16.0dip" android:layout_marginRight="24.0dip" android:text="@string/fission_what_invitation_code_desc" style="@style/style_regular_text" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="14.0sp" android:gravity="center" android:id="@id/btn_ok" android:background="@color/color_invitaion_helt_btn_bg" android:layout_width="fill_parent" android:layout_height="40.0dip" android:layout_marginLeft="24.0dip" android:layout_marginTop="16.0dip" android:layout_marginRight="24.0dip" android:layout_marginBottom="24.0dip" android:text="@string/fission_ok" android:textAllCaps="false" app:layout_constraintBottom_toBottomOf="@id/img" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" style="@style/style_title_text" />
</LinearLayout>
