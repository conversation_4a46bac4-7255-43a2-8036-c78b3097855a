<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/white" android:ellipsize="end" android:gravity="center_vertical" android:id="@id/tvText" android:layout_width="wrap_content" android:layout_height="40.0dip" android:layout_marginBottom="12.0dip" android:text="@string/subtitle_download_other_subtitle" android:maxLines="1" android:layout_marginStart="24.0dip" app:layout_constrainedWidth="true" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toStartOf="@id/ivImage" app:layout_constraintHorizontal_bias="0.0" app:layout_constraintHorizontal_chainStyle="packed" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/ivImage" android:layout_width="wrap_content" android:layout_height="wrap_content" android:src="@drawable/subtitle_arrow_left_white" android:layout_marginStart="2.0dip" app:layout_constraintBottom_toBottomOf="@id/tvText" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/tvText" app:layout_constraintTop_toTopOf="@id/tvText" />
</androidx.constraintlayout.widget.ConstraintLayout>
