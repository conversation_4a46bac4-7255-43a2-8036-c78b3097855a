<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:background="@drawable/libui_bottom_dialog_bg" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.appcompat.widget.LinearLayoutCompat android:gravity="center_vertical" android:id="@id/ll_ctl" android:layout_width="fill_parent" android:layout_height="48.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent">
        <net.lucode.hackware.magicindicator.MagicIndicator android:layout_gravity="center_vertical" android:id="@id/magic_indicator" android:layout_width="0.0dip" android:layout_height="40.0dip" android:layout_marginTop="2.0dip" android:layout_marginBottom="2.0dip" android:layout_weight="1.0" android:layout_marginStart="16.0dip" android:layout_marginEnd="10.0dip" />
        <androidx.appcompat.widget.AppCompatImageView android:layout_gravity="right|center" android:id="@id/iv_clear" android:layout_width="16.0dip" android:layout_height="16.0dip" android:src="@mipmap/icon_delete_light" android:tint="@color/text_03" android:layout_marginEnd="16.0dip" />
    </androidx.appcompat.widget.LinearLayoutCompat>
    <androidx.viewpager2.widget.ViewPager2 android:id="@id/view_pager" android:layout_width="fill_parent" android:layout_height="fill_parent" android:layout_marginTop="48.0dip" android:layout_marginBottom="48.0dip" app:layout_constraintTop_toTopOf="parent" />
    <View android:id="@id/line1" android:background="@color/border" android:layout_width="fill_parent" android:layout_height="1.0dip" app:layout_constraintBottom_toTopOf="@id/tv_close" />
    <TextView android:textSize="16.0sp" android:textStyle="bold" android:textColor="@color/text_01" android:gravity="center" android:id="@id/tv_close" android:layout_width="fill_parent" android:layout_height="48.0dip" android:text="@string/str_close" app:layout_constraintBottom_toBottomOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>
