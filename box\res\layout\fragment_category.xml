<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.google.android.material.appbar.AppBarLayout android:orientation="vertical" android:id="@id/app_bar" android:layout_width="fill_parent" android:layout_height="wrap_content">
        <com.google.android.material.appbar.CollapsingToolbarLayout android:layout_width="fill_parent" android:layout_height="wrap_content" app:layout_scrollFlags="scroll|exitUntilCollapsed">
            <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_cover" android:visibility="visible" android:layout_width="fill_parent" android:layout_height="227.0dip" android:scaleType="centerCrop" />
            <View android:background="@drawable/bg_category_cover_gradient" android:layout_width="fill_parent" android:layout_height="227.0dip" />
            <androidx.appcompat.widget.AppCompatTextView android:textSize="22.0sp" android:textColor="@color/common_white" android:ellipsize="end" android:gravity="center" android:layout_gravity="bottom" android:id="@id/tvTitle_expand" android:visibility="visible" android:layout_width="fill_parent" android:layout_marginBottom="87.0dip" android:maxLines="2" android:layout_marginStart="26.0dip" android:layout_marginEnd="26.0dip" style="@style/style_import_text" />
            <androidx.appcompat.widget.Toolbar android:id="@id/toolbar" android:visibility="visible" android:layout_width="fill_parent" android:layout_height="wrap_content" app:contentInsetLeft="0.0dip" app:contentInsetStart="0.0dip" app:layout_collapseMode="pin">
                <androidx.constraintlayout.widget.ConstraintLayout android:visibility="visible" android:layout_width="fill_parent" android:layout_height="44.0dip" android:layout_marginStart="16.0dip" android:layout_marginEnd="16.0dip" app:layout_constraintTop_toTopOf="parent">
                    <androidx.appcompat.widget.AppCompatImageView android:id="@id/ivBack" android:layout_width="24.0dip" android:layout_height="24.0dip" android:src="@mipmap/icon_white_back" android:scaleType="centerCrop" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
                    <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textColor="@color/text_01" android:ellipsize="end" android:id="@id/tvTitle" android:visibility="gone" android:layout_width="0.0dip" android:maxLines="1" android:layout_marginStart="8.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/ivBack" app:layout_constraintTop_toTopOf="parent" style="@style/style_regula_bigger_text" />
                </androidx.constraintlayout.widget.ConstraintLayout>
            </androidx.appcompat.widget.Toolbar>
        </com.google.android.material.appbar.CollapsingToolbarLayout>
    </com.google.android.material.appbar.AppBarLayout>
    <androidx.recyclerview.widget.RecyclerView android:layout_gravity="top" android:id="@id/recycler_view" android:layout_width="fill_parent" android:layout_height="fill_parent" android:layout_marginTop="-40.0dip" app:layout_anchorGravity="top" app:layout_behavior="com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior" />
</androidx.coordinatorlayout.widget.CoordinatorLayout>
