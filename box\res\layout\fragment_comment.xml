<?xml version="1.0" encoding="utf-8"?>
<androidx.appcompat.widget.LinearLayoutCompat android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/text_01" android:id="@id/tv_comment" android:layout_marginTop="16.0dip" android:text="@string/comments" android:layout_marginStart="16.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" style="@style/style_medium_text" />
    <androidx.recyclerview.widget.RecyclerView android:id="@id/comment_list" android:visibility="visible" android:layout_width="fill_parent" android:layout_height="fill_parent" android:layout_marginTop="4.0dip" android:minHeight="250.0dip" android:layout_weight="1.0" app:layout_constraintTop_toBottomOf="@id/tv_comment" />
    <View android:id="@id/comment_input_bg" android:background="@drawable/comment_input_bg" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="100.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintTop_toBottomOf="@id/comment_list" />
    <LinearLayout android:gravity="center" android:id="@id/ll_loading" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="wrap_content" android:minHeight="250.0dip" app:layout_constraintTop_toBottomOf="@id/tv_comment">
        <ProgressBar android:layout_gravity="center" android:id="@id/view_load" android:layout_width="15.0dip" android:layout_height="15.0dip" android:indeterminateTint="@color/main" />
        <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/text_02" android:layout_gravity="center" android:id="@id/tv_loading" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/post_loading" android:layout_marginStart="8.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    </LinearLayout>
    <Space android:layout_width="fill_parent" android:layout_height="@dimen/comment_input_bg_height" />
</androidx.appcompat.widget.LinearLayoutCompat>
