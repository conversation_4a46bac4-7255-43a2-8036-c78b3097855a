<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:orientation="vertical" android:id="@id/ll_root" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <FrameLayout android:layout_width="fill_parent" android:layout_height="fill_parent">
        <androidx.recyclerview.widget.RecyclerView android:id="@id/recycler_view" android:layout_width="fill_parent" android:layout_height="fill_parent" app:adv_layout_empty="@layout/download_empty_default" app:adv_layout_error="@layout/download_empty_default" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintTop_toBottomOf="@id/tv_title" />
        <androidx.core.widget.NestedScrollView android:id="@id/sv_empty_root" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="fill_parent">
            <LinearLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="fill_parent">
                <FrameLayout android:id="@id/fl_empty_root" android:layout_width="fill_parent" android:layout_height="wrap_content" />
                <com.transsnet.downloader.widget.DownloadPanelForYouFootView android:id="@id/foot_for_you" android:layout_width="fill_parent" android:layout_height="wrap_content" />
            </LinearLayout>
        </androidx.core.widget.NestedScrollView>
    </FrameLayout>
</LinearLayout>
