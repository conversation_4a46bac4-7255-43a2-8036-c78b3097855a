<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_gravity="bottom" android:background="@drawable/libui_bottom_dialog_bg" android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/text_01" android:id="@id/tv_title" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="12.0dip" android:text="@string/str_download_dialog_title" android:layout_marginStart="16.0dip" android:layout_marginEnd="16.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" style="@style/style_regula_bigger_text" />
    <ProgressBar android:id="@id/progress_bar" android:layout_width="0.0dip" android:layout_height="3.0dip" android:layout_marginTop="12.0dip" android:max="100" android:progressDrawable="@drawable/progress_download_analyzing" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tv_title" style="@android:style/Widget.ProgressBar.Horizontal" />
    <com.airbnb.lottie.LottieAnimationView android:id="@id/lav_download_analyzing" android:layout_width="48.0dip" android:layout_height="48.0dip" android:layout_marginTop="27.0dip" android:layout_marginBottom="53.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/progress_bar" app:lottie_fileName="download_analyzing_anima.json" />
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/text_02" android:ellipsize="end" android:gravity="center" android:id="@id/tv_download_analyzing" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginTop="4.0dip" android:text="@string/download_analyzing" android:maxLines="1" android:layout_marginStart="32.0dip" android:layout_marginEnd="32.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/lav_download_analyzing" />
</androidx.constraintlayout.widget.ConstraintLayout>
