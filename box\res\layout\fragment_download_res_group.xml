<?xml version="1.0" encoding="utf-8"?>
<FrameLayout android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.recyclerview.widget.RecyclerView android:id="@id/recycler_view" android:layout_width="fill_parent" android:layout_height="wrap_content" android:minHeight="200.0dip" />
    <LinearLayout android:gravity="center" android:id="@id/ll_loading" android:background="@color/module_01" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="fill_parent" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tv_title">
        <ProgressBar android:layout_gravity="center" android:id="@id/view_load" android:layout_width="15.0dip" android:layout_height="15.0dip" android:layout_marginTop="-65.0dip" android:indeterminateTint="@color/main" />
        <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/text_02" android:layout_gravity="center" android:id="@id/tv_loading" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="-65.0dip" android:text="@string/base_loading" android:layout_marginStart="8.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    </LinearLayout>
    <LinearLayout android:gravity="center" android:orientation="vertical" android:id="@id/ll_not_net" android:background="@color/module_02" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="fill_parent">
        <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/text_02" android:gravity="center" android:layout_gravity="center" android:id="@id/tv_no_network_content" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="-65.0dip" android:text="@string/home_no_network_content" android:layout_marginStart="40.0dip" android:layout_marginEnd="40.0dip" style="@style/style_regula_bigger_text" />
        <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/main" android:layout_gravity="center" android:id="@id/tv_retry" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="17.0dip" android:text="@string/retry_text" android:drawablePadding="4.0dip" android:layout_marginStart="8.0dip" app:drawableEndCompat="@mipmap/libui_ic_more_small_base_color" app:drawableTint="@color/main" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    </LinearLayout>
</FrameLayout>
