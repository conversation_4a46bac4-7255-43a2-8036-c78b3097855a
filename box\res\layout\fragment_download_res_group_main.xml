<?xml version="1.0" encoding="utf-8"?>
<FrameLayout android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.constraintlayout.widget.ConstraintLayout android:background="@drawable/libui_bottom_dialog_bg" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="98.0dip">
        <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/text_01" android:gravity="center_vertical" android:id="@id/tv_title" android:layout_width="wrap_content" android:layout_height="48.0dip" android:text="@string/download_movie" android:layout_marginStart="16.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" style="@style/style_regula_bigger_text" />
        <ImageView android:id="@id/iv_close" android:padding="4.0dip" android:layout_width="24.0dip" android:layout_height="24.0dip" android:src="@mipmap/ic_close_black" android:layout_marginEnd="12.0dip" app:layout_constraintBottom_toBottomOf="@id/tv_title" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="@id/tv_title" />
        <androidx.coordinatorlayout.widget.CoordinatorLayout android:id="@id/cl_root" android:layout_width="0.0dip" android:layout_height="wrap_content" app:layout_constraintBottom_toTopOf="@id/line" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tv_title">
            <com.google.android.material.appbar.AppBarLayout android:orientation="vertical" android:background="@color/module_01" android:layout_width="fill_parent" android:layout_height="wrap_content" app:elevation="0.0dip">
                <com.transsnet.downloader.widget.DownloadResolutionTabView android:id="@id/v_language_resolution" android:visibility="visible" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginStart="16.0dip" android:layout_marginEnd="16.0dip" app:bl_corners_radius="4.0dip" app:bl_solid_color="@color/download_module_1" app:layout_scrollFlags="scroll|enterAlways" />
                <net.lucode.hackware.magicindicator.MagicIndicator android:id="@id/magic_indicator" android:paddingBottom="4.0dip" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="28.0dip" />
            </com.google.android.material.appbar.AppBarLayout>
            <FrameLayout android:id="@id/fl_content" android:layout_width="fill_parent" android:layout_height="fill_parent" app:layout_behavior="@string/appbar_scrolling_view_behavior" />
        </androidx.coordinatorlayout.widget.CoordinatorLayout>
        <View android:id="@id/line" android:background="@color/download_dialog_line" android:layout_width="0.0dip" android:layout_height="1.0dip" app:layout_constraintBottom_toTopOf="@id/v_bottom" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" />
        <View android:id="@id/v_bottom" android:background="@color/module_01" android:layout_width="0.0dip" android:layout_height="68.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" />
        <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_check" android:layout_width="wrap_content" android:layout_height="0.0dip" android:layout_marginStart="16.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="@id/v_bottom" app:srcCompat="@drawable/selector_download_group_check" />
        <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/text_01" android:gravity="center_vertical" android:id="@id/tv_select_all" android:layout_width="wrap_content" android:layout_height="0.0dip" android:text="@string/download_select_all" android:paddingStart="8.0dip" android:paddingEnd="13.0dip" app:layout_constraintBottom_toBottomOf="@id/v_bottom" app:layout_constraintStart_toEndOf="@id/iv_check" app:layout_constraintTop_toTopOf="@id/v_bottom" style="@style/style_medium_text" />
        <FrameLayout android:id="@id/fl_select_all_bg" android:background="@color/module_01" android:focusable="true" android:visibility="gone" android:clickable="true" android:layout_width="0.0dip" android:layout_height="0.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="@id/tv_select_all" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="@id/v_bottom">
            <ProgressBar android:layout_gravity="center" android:id="@id/progress_bar_select_all" android:layout_width="12.0dip" android:layout_height="12.0dip" android:indeterminateTint="@color/main" />
        </FrameLayout>
        <androidx.appcompat.widget.LinearLayoutCompat android:gravity="center" android:id="@id/btn_download" android:background="@drawable/shape_download_group_button" android:layout_width="0.0dip" android:layout_height="36.0dip" android:layout_marginBottom="2.0dip" android:layout_marginStart="16.0dip" android:layout_marginEnd="16.0dip" app:layout_constraintBottom_toBottomOf="@id/iv_check" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/tv_select_all" app:layout_constraintTop_toTopOf="@id/iv_check" style="@style/style_medium_text">
            <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_btn_download_icon" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginStart="6.0dip" app:layout_constraintBottom_toBottomOf="@id/btn_download" app:layout_constraintEnd_toStartOf="@id/tv_btn_download" app:layout_constraintHorizontal_chainStyle="packed" app:layout_constraintStart_toStartOf="@id/btn_download" app:layout_constraintTop_toTopOf="@id/btn_download" app:srcCompat="@mipmap/ic_download_white" />
            <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/white" android:ellipsize="end" android:id="@id/tv_btn_download" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginBottom="1.0dip" android:text="@string/str_download" android:maxLines="1" android:paddingEnd="6.0dip" android:layout_marginStart="2.0dip" app:layout_constraintBottom_toBottomOf="@id/btn_download" app:layout_constraintEnd_toEndOf="@id/btn_download" app:layout_constraintStart_toEndOf="@id/iv_btn_download_icon" app:layout_constraintTop_toTopOf="@id/btn_download" style="@style/style_medium_text" />
        </androidx.appcompat.widget.LinearLayoutCompat>
        <ProgressBar android:id="@id/progress_bar_btn_download" android:visibility="gone" android:layout_width="12.0dip" android:layout_height="12.0dip" android:indeterminateTint="@color/white" app:layout_constraintBottom_toBottomOf="@id/btn_download" app:layout_constraintEnd_toEndOf="@id/btn_download" app:layout_constraintStart_toStartOf="@id/btn_download" app:layout_constraintTop_toTopOf="@id/btn_download" />
        <View android:id="@id/v_content_gap" android:background="@color/transparent" android:focusable="true" android:visibility="gone" android:clickable="true" android:layout_width="0.0dip" android:layout_height="0.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="@id/cl_root" />
        <LinearLayout android:gravity="center" android:orientation="vertical" android:id="@id/ll_not_net" android:background="@color/module_01" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="0.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintTop_toTopOf="parent">
            <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/text_02" android:layout_gravity="center" android:id="@id/tv_no_network_content" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/home_no_network_content" style="@style/style_regula_bigger_text" />
            <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/main" android:layout_gravity="center" android:id="@id/tv_retry" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="17.0dip" android:text="@string/retry_text" android:drawablePadding="4.0dip" android:layout_marginStart="8.0dip" app:drawableEndCompat="@mipmap/libui_ic_more_small_base_color" app:drawableTint="@color/main" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
        </LinearLayout>
        <com.noober.background.view.BLFrameLayout android:id="@id/fl_loading" android:visibility="gone" android:layout_width="120.0dip" android:layout_height="120.0dip" android:layout_marginTop="20.0dip" app:bl_corners_radius="8.0dip" app:bl_solid_color="@color/black_70" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent">
            <ProgressBar android:layout_gravity="center" android:id="@id/view_load" android:layout_width="44.0dip" android:layout_height="44.0dip" android:layout_marginTop="-12.0dip" android:indeterminateTint="@color/white" />
            <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/white" android:layout_gravity="center|bottom" android:id="@id/tv_tips" android:layout_marginBottom="16.0dip" android:text="@string/base_loading" app:layout_constraintBottom_toBottomOf="@id/v_bg" app:layout_constraintStart_toEndOf="@id/progress_bar" app:layout_constraintTop_toTopOf="@id/v_bg" style="@style/style_regular_text" />
        </com.noober.background.view.BLFrameLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</FrameLayout>
