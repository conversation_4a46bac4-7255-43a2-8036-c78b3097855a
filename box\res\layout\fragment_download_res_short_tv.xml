<?xml version="1.0" encoding="utf-8"?>
<com.noober.background.view.BLConstraintLayout android:layout_gravity="bottom" android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="wrap_content" app:bl_corners_topLeftRadius="8.0dip" app:bl_corners_topRightRadius="8.0dip" app:bl_solid_color="@color/night_bg_color"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.appcompat.widget.AppCompatTextView android:textSize="18.0sp" android:textStyle="bold" android:textColor="@color/white" android:ellipsize="end" android:id="@id/tv_title" android:layout_width="0.0dip" android:layout_height="wrap_content" android:lines="1" android:layout_marginStart="16.0dip" android:layout_marginEnd="12.0dip" app:layout_constraintBottom_toTopOf="@id/title_line" app:layout_constraintEnd_toStartOf="@id/iv_close" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <ImageView android:id="@id/iv_close" android:padding="4.0dip" android:layout_width="24.0dip" android:layout_height="24.0dip" android:src="@mipmap/ic_close_black" android:scaleType="center" android:layout_marginEnd="12.0dip" app:layout_constraintBottom_toBottomOf="@id/tv_title" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="@id/tv_title" />
    <View android:id="@id/title_line" android:background="@color/white_10" android:layout_width="0.0dip" android:layout_height="1.0dip" android:layout_marginTop="49.0dip" android:layout_marginStart="16.0dip" android:layout_marginEnd="16.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <androidx.coordinatorlayout.widget.CoordinatorLayout android:id="@id/cl_root" android:layout_width="fill_parent" android:layout_height="420.0dip" app:layout_constraintBottom_toTopOf="@id/bottom_line" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/title_line">
        <com.google.android.material.appbar.AppBarLayout android:orientation="vertical" android:id="@id/app_bar_layout" android:background="@color/night_bg_color" android:layout_width="fill_parent" android:layout_height="wrap_content" app:elevation="0.0dip">
            <androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="fill_parent" app:layout_scrollFlags="scroll|exitUntilCollapsed">
                <com.google.android.material.imageview.ShapeableImageView android:id="@id/iv_cover" android:layout_width="91.0dip" android:layout_height="130.0dip" android:layout_marginTop="16.0dip" android:scaleType="centerCrop" android:layout_marginStart="16.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:shapeAppearanceOverlay="@style/roundStyle_8" />
                <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textStyle="bold" android:textColor="@color/white" android:ellipsize="end" android:id="@id/tv_title_2" android:layout_width="0.0dip" android:layout_height="wrap_content" android:maxLines="2" android:layout_marginStart="8.0dip" android:layout_marginEnd="16.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/iv_cover" app:layout_constraintTop_toTopOf="@id/iv_cover" />
                <androidx.recyclerview.widget.RecyclerView android:id="@id/recycler_view_tags" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginTop="8.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="@id/tv_title_2" app:layout_constraintTop_toBottomOf="@id/tv_title_2" />
                <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/night_tips_color" android:ellipsize="end" android:id="@id/tv_genre" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginTop="8.0dip" android:lines="1" android:layout_marginEnd="16.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="@id/tv_title_2" app:layout_constraintTop_toBottomOf="@id/recycler_view_tags" />
                <com.noober.background.view.BLView android:id="@id/v_play" android:layout_width="0.0dip" android:layout_height="36.0dip" android:layout_marginTop="16.0dip" android:layout_marginStart="16.0dip" android:layout_marginEnd="8.0dip" app:bl_corners_radius="4.0dip" app:bl_solid_color="@color/white" app:layout_constraintEnd_toStartOf="@id/iv_favorite" app:layout_constraintHorizontal_weight="0.63" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/iv_cover" app:layout_constraintVertical_chainStyle="packed" style="@style/style_medium_text" />
                <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/night_dark_color" android:id="@id/tv_play_title" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/play" android:drawablePadding="4.0dip" app:drawableStartCompat="@mipmap/ic_play_dark" app:layout_constraintBottom_toBottomOf="@id/v_play" app:layout_constraintEnd_toEndOf="@id/v_play" app:layout_constraintStart_toStartOf="@id/v_play" app:layout_constraintTop_toTopOf="@id/v_play" style="@style/style_medium_text" />
                <com.noober.background.view.BLImageView android:id="@id/iv_favorite" android:layout_width="0.0dip" android:layout_height="36.0dip" android:scaleType="center" android:layout_marginEnd="8.0dip" app:bl_corners_radius="4.0dip" app:bl_solid_color="@color/white" app:layout_constraintBottom_toBottomOf="@id/v_play" app:layout_constraintEnd_toStartOf="@id/iv_share" app:layout_constraintHorizontal_weight="0.18" app:layout_constraintStart_toEndOf="@id/v_play" app:layout_constraintTop_toTopOf="@id/v_play" app:srcCompat="@drawable/selector_download_short_tv_favorite" />
                <com.noober.background.view.BLImageView android:id="@id/iv_share" android:layout_width="0.0dip" android:layout_height="36.0dip" android:scaleType="center" android:layout_marginEnd="16.0dip" app:bl_corners_radius="4.0dip" app:bl_solid_color="@color/white" app:layout_constraintBottom_toBottomOf="@id/v_play" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintHorizontal_weight="0.18" app:layout_constraintStart_toEndOf="@id/iv_favorite" app:layout_constraintTop_toTopOf="@id/v_play" app:srcCompat="@mipmap/ic_download_short_tv_share" />
                <com.transsnet.downloader.widget.DownloadInfoExtendView android:id="@id/iev_info" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginTop="24.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/v_play" />
            </androidx.constraintlayout.widget.ConstraintLayout>
            <TextView android:textSize="18.0sp" android:textStyle="bold" android:textColor="@color/white" android:id="@id/tv_download_title" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="16.0dip" android:text="@string/str_download" android:layout_marginStart="16.0dip" style="@style/style_title_text" />
            <com.google.android.material.tabs.TabLayout android:id="@id/tab_ep_title" android:background="@color/transparent" android:layout_width="fill_parent" android:layout_height="wrap_content" app:tabIndicatorColor="@color/white" app:tabIndicatorHeight="0.0dip" app:tabMode="scrollable" app:tabSelectedTextColor="@color/main" app:tabTextColor="@color/white" />
        </com.google.android.material.appbar.AppBarLayout>
        <androidx.recyclerview.widget.RecyclerView android:id="@id/recycler_view" android:layout_width="fill_parent" android:layout_height="wrap_content" app:layout_behavior="@string/appbar_scrolling_view_behavior" />
    </androidx.coordinatorlayout.widget.CoordinatorLayout>
    <View android:id="@id/bottom_line" android:background="@color/white_10" android:layout_width="0.0dip" android:layout_height="1.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="@id/v_bottom" />
    <View android:id="@id/v_bottom" android:layout_width="0.0dip" android:layout_height="72.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_check" android:paddingBottom="2.0dip" android:layout_width="wrap_content" android:layout_height="0.0dip" android:layout_marginStart="16.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="@id/v_bottom" app:srcCompat="@drawable/selector_download_group_check" />
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/white" android:gravity="center_vertical" android:id="@id/tv_select_all" android:paddingBottom="2.0dip" android:layout_width="wrap_content" android:layout_height="0.0dip" android:text="@string/download_select_all" android:paddingStart="8.0dip" android:paddingEnd="13.0dip" app:layout_constraintBottom_toBottomOf="@id/v_bottom" app:layout_constraintStart_toEndOf="@id/iv_check" app:layout_constraintTop_toTopOf="@id/v_bottom" style="@style/style_medium_text" />
    <FrameLayout android:id="@id/fl_select_all_bg" android:background="@color/night_bg_color" android:focusable="true" android:visibility="gone" android:clickable="true" android:layout_width="0.0dip" android:layout_height="0.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="@id/tv_select_all" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/bottom_line">
        <ProgressBar android:layout_gravity="center" android:id="@id/progress_bar_select_all" android:layout_width="12.0dip" android:layout_height="12.0dip" android:indeterminateTint="@color/main" />
    </FrameLayout>
    <androidx.appcompat.widget.LinearLayoutCompat android:gravity="center" android:id="@id/btn_download" android:background="@drawable/shape_download_group_button" android:layout_width="0.0dip" android:layout_height="36.0dip" android:layout_marginBottom="2.0dip" android:layout_marginStart="16.0dip" android:layout_marginEnd="16.0dip" app:layout_constraintBottom_toBottomOf="@id/iv_check" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/tv_select_all" app:layout_constraintTop_toTopOf="@id/iv_check" style="@style/style_medium_text">
        <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_btn_download_icon" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginStart="6.0dip" app:layout_constraintBottom_toBottomOf="@id/btn_download" app:layout_constraintEnd_toStartOf="@id/tv_btn_download" app:layout_constraintHorizontal_chainStyle="packed" app:layout_constraintStart_toStartOf="@id/btn_download" app:layout_constraintTop_toTopOf="@id/btn_download" app:srcCompat="@mipmap/ic_download_white" />
        <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/white" android:ellipsize="end" android:id="@id/tv_btn_download" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginBottom="1.0dip" android:text="@string/str_download" android:maxLines="1" android:paddingEnd="6.0dip" android:layout_marginStart="2.0dip" app:layout_constraintBottom_toBottomOf="@id/btn_download" app:layout_constraintEnd_toEndOf="@id/btn_download" app:layout_constraintStart_toEndOf="@id/iv_btn_download_icon" app:layout_constraintTop_toTopOf="@id/btn_download" />
    </androidx.appcompat.widget.LinearLayoutCompat>
    <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/white" android:id="@id/tv_download_ep_count" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginBottom="1.0dip" android:layout_marginStart="2.0dip" app:layout_constraintEnd_toEndOf="@id/btn_download" app:layout_constraintStart_toStartOf="@id/btn_download" app:layout_constraintTop_toBottomOf="@id/btn_download" />
    <ProgressBar android:id="@id/progress_bar_btn_download" android:visibility="gone" android:layout_width="12.0dip" android:layout_height="12.0dip" android:indeterminateTint="@color/white" app:layout_constraintBottom_toBottomOf="@id/btn_download" app:layout_constraintEnd_toEndOf="@id/btn_download" app:layout_constraintStart_toStartOf="@id/btn_download" app:layout_constraintTop_toTopOf="@id/btn_download" />
    <FrameLayout android:id="@id/fl_state" android:background="@color/night_bg_color" android:visibility="gone" android:layout_width="0.0dip" android:layout_height="0.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/title_line" />
    <View android:id="@id/v_content_gap" android:background="@color/transparent" android:focusable="true" android:visibility="gone" android:clickable="true" android:layout_width="0.0dip" android:layout_height="0.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="@id/cl_root" />
    <com.noober.background.view.BLFrameLayout android:id="@id/fl_download_loading" android:visibility="gone" android:layout_width="120.0dip" android:layout_height="120.0dip" android:layout_marginTop="92.0dip" app:bl_corners_radius="8.0dip" app:bl_solid_color="@color/black_70" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent">
        <ProgressBar android:layout_gravity="center" android:id="@id/view_load" android:layout_width="44.0dip" android:layout_height="44.0dip" android:layout_marginTop="-12.0dip" android:indeterminateTint="@color/white" />
        <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/white" android:layout_gravity="center|bottom" android:id="@id/tv_tips" android:layout_marginBottom="16.0dip" android:text="@string/base_loading" app:layout_constraintBottom_toBottomOf="@id/v_bg" app:layout_constraintStart_toEndOf="@id/progress_bar" app:layout_constraintTop_toTopOf="@id/v_bg" style="@style/style_regular_text" />
    </com.noober.background.view.BLFrameLayout>
</com.noober.background.view.BLConstraintLayout>
