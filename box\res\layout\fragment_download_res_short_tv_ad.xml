<?xml version="1.0" encoding="utf-8"?>
<com.noober.background.view.BLConstraintLayout android:layout_width="fill_parent" android:layout_height="wrap_content" app:bl_corners_radius="8.0dip" app:bl_solid_color="@color/night_bg_color"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_close" android:padding="16.0dip" android:layout_width="wrap_content" android:layout_height="wrap_content" android:src="@mipmap/web_ic_close" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <com.noober.background.view.BLView android:id="@id/v_bg" android:layout_width="fill_parent" android:layout_height="64.0dip" android:layout_marginLeft="16.0dip" android:layout_marginTop="64.0dip" android:layout_marginRight="16.0dip" android:layout_marginBottom="64.0dip" app:bl_corners_radius="8.0dip" app:bl_stroke_color="@color/white_20" app:bl_stroke_width="1.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_ad" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginStart="16.0dip" app:layout_constraintBottom_toBottomOf="@id/v_bg" app:layout_constraintStart_toStartOf="@id/v_bg" app:layout_constraintTop_toTopOf="@id/v_bg" app:srcCompat="@mipmap/ic_download_short_tv_ad" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="14.0sp" android:textStyle="bold" android:textColor="@color/white" android:ellipsize="end" android:id="@id/tv_watch" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginTop="13.0dip" android:text="@string/download_short_tv_watch_ad_tips" android:maxLines="2" android:layout_marginStart="7.0dip" app:layout_constraintBottom_toTopOf="@id/tv_ep" app:layout_constraintEnd_toStartOf="@id/tv_unlock" app:layout_constraintStart_toEndOf="@id/iv_ad" app:layout_constraintTop_toTopOf="@id/v_bg" style="@style/style_medium_text" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="14.0sp" android:textStyle="bold" android:textColor="@color/main" android:id="@id/tv_ep" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginBottom="15.0dip" android:layout_marginEnd="16.0dip" app:layout_constraintBottom_toBottomOf="@id/v_bg" app:layout_constraintEnd_toStartOf="@id/tv_unlock" app:layout_constraintStart_toEndOf="@id/iv_ad" app:layout_constraintStart_toStartOf="@id/tv_watch" app:layout_constraintTop_toBottomOf="@id/tv_watch" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textColor="@color/white" android:gravity="center" android:id="@id/tv_unlock" android:background="@drawable/libui_main_btn_selector" android:layout_width="wrap_content" android:layout_height="32.0dip" android:text="@string/unlock" android:paddingStart="16.0dip" android:paddingEnd="16.0dip" android:layout_marginEnd="16.0dip" app:layout_constraintBottom_toBottomOf="@id/v_bg" app:layout_constraintEnd_toEndOf="@id/v_bg" app:layout_constraintTop_toTopOf="@id/v_bg" style="@style/style_medium_text" />
</com.noober.background.view.BLConstraintLayout>
