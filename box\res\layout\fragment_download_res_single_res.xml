<?xml version="1.0" encoding="utf-8"?>
<FrameLayout android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.constraintlayout.widget.ConstraintLayout android:background="@drawable/libui_bottom_dialog_bg" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="98.0dip">
        <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/text_01" android:gravity="center_vertical" android:id="@id/tv_title" android:layout_width="wrap_content" android:layout_height="48.0dip" android:text="@string/str_download_dialog_title" android:layout_marginStart="16.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" style="@style/style_regula_bigger_text" />
        <ImageView android:id="@id/iv_close" android:padding="4.0dip" android:layout_width="wrap_content" android:layout_height="wrap_content" android:src="@mipmap/ic_close_black" android:layout_marginEnd="12.0dip" app:layout_constraintBottom_toBottomOf="@id/tv_title" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="@id/tv_title" />
        <androidx.appcompat.widget.LinearLayoutCompat android:orientation="vertical" android:id="@id/ll_content" android:layout_width="fill_parent" android:layout_height="0.0dip" app:layout_constraintBottom_toTopOf="@id/line" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tv_title" app:layout_constraintVertical_bias="1.0">
            <com.transsnet.downloader.widget.DownloadPathEntranceView android:id="@id/v_path_entrance" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginStart="16.0dip" android:layout_marginEnd="16.0dip" app:bl_corners_radius="8.0dip" app:bl_solid_color="@color/download_module_1" />
            <include android:id="@id/included_content" layout="@layout/item_bottom_dialog_download_ana_group" />
        </androidx.appcompat.widget.LinearLayoutCompat>
        <View android:id="@id/line" android:background="@color/download_dialog_line" android:layout_width="0.0dip" android:layout_height="1.0dip" app:layout_constraintBottom_toTopOf="@id/v_bottom" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" />
        <View android:id="@id/v_bottom" android:background="@color/module_01" android:layout_width="0.0dip" android:layout_height="68.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" />
        <com.transsnet.downloader.widget.DownloadView android:id="@id/tv_download" android:background="@drawable/bg_btn_01" android:layout_width="0.0dip" android:layout_height="36.0dip" android:layout_marginStart="16.0dip" android:layout_marginEnd="16.0dip" app:layout_constraintBottom_toBottomOf="@id/v_bottom" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="@id/v_bottom" app:layout_constraintTop_toTopOf="@id/v_bottom" app:tips_textSize="14.0sp" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</FrameLayout>
