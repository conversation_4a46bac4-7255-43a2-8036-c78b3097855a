<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:background="@color/module_01" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.appcompat.widget.LinearLayoutCompat android:gravity="center_vertical" android:orientation="horizontal" android:id="@id/tv_header_toolbar" android:layout_width="fill_parent" android:layout_height="48.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent">
        <androidx.appcompat.widget.AppCompatTextView android:textSize="@dimen/dimens_sp_18" android:textStyle="bold" android:textColor="@color/white" android:gravity="center_vertical" android:layout_width="0.0dip" android:layout_height="fill_parent" android:text="@string/movie_detail_more_details" android:layout_weight="1.0" android:layout_marginStart="12.0dip" />
        <androidx.appcompat.widget.AppCompatImageView android:id="@id/tv_close" android:layout_width="44.0dip" android:layout_height="44.0dip" android:src="@mipmap/ic_close" android:scaleType="center" />
    </androidx.appcompat.widget.LinearLayoutCompat>
    <View android:background="@color/border" android:layout_width="fill_parent" android:layout_height="1.0dip" app:layout_constraintTop_toBottomOf="@id/tv_header_toolbar" />
    <androidx.core.widget.NestedScrollView android:tag="scrollView" android:persistentDrawingCache="animation" android:layout_width="fill_parent" android:layout_height="0.0dip" app:layout_behavior="@string/appbar_scrolling_view_behavior" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tv_header_toolbar" app:layout_scrollFlags="scroll">
        <androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="wrap_content">
            <androidx.appcompat.widget.AppCompatTextView android:textSize="18.0sp" android:textColor="@color/white" android:ellipsize="end" android:gravity="start|center" android:id="@id/tvMovieTitle" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginLeft="12.0dip" android:layout_marginTop="12.0dip" android:layout_marginRight="12.0dip" android:textAlignment="viewStart" app:layout_constraintBottom_toTopOf="@id/ll_score" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:layout_constraintVertical_chainStyle="packed" style="@style/style_import_text" />
            <LinearLayout android:gravity="start" android:orientation="horizontal" android:id="@id/ll_score" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginTop="4.0dip" android:layout_marginBottom="12.0dip" app:layout_constraintBottom_toTopOf="@id/infoExtendView" app:layout_constraintEnd_toEndOf="@id/tvMovieTitle" app:layout_constraintStart_toStartOf="@id/tvMovieTitle" app:layout_constraintTop_toBottomOf="@id/tvMovieTitle">
                <androidx.appcompat.widget.AppCompatImageView android:id="@id/ivMovieContent" android:layout_width="wrap_content" android:layout_height="wrap_content" android:src="@drawable/ic_tag_edu" android:tint="@color/gray_40" app:layout_constraintEnd_toStartOf="@id/tvMovieContent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="@id/tvMovieContent" style="@style/style_regular_text" />
                <com.transsion.moviedetail.view.CustomTextViewGroup android:id="@id/tv_category" android:layout_width="wrap_content" android:layout_height="wrap_content" app:text="School" />
                <com.transsion.moviedetail.view.CustomTextViewGroup android:id="@id/tv_genre" android:layout_width="wrap_content" android:layout_height="wrap_content" app:text="Science" />
                <com.transsion.moviedetail.view.CustomTextViewGroup android:id="@id/tv_duration" android:layout_width="wrap_content" android:layout_height="wrap_content" app:text="2h27m" />
                <com.transsion.moviedetail.view.CustomTextViewGroup android:id="@id/tv_students" android:layout_width="wrap_content" android:layout_height="wrap_content" app:text="228 students" />
                <androidx.constraintlayout.helper.widget.Flow android:id="@id/flow" android:layout_width="0.0dip" android:layout_height="wrap_content" app:constraint_referenced_ids="ivMovieContent,tv_category,tv_genre,tv_duration,tv_students" app:flow_horizontalBias="0.0" app:flow_horizontalStyle="packed" app:flow_wrapMode="chain" />
            </LinearLayout>
            <com.transsion.moviedetail.view.InfoExtendView android:id="@id/infoExtendView" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="16.0dip" android:layout_marginBottom="16.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/ll_score" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.core.widget.NestedScrollView>
</androidx.constraintlayout.widget.ConstraintLayout>
