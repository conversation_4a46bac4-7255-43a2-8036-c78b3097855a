<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.coordinatorlayout.widget.CoordinatorLayout android:id="@id/cl_root" android:layout_width="0.0dip" android:layout_height="0.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent">
        <com.google.android.material.appbar.AppBarLayout android:orientation="vertical" android:background="@color/bg_01" android:layout_width="fill_parent" android:layout_height="wrap_content" app:elevation="0.0dip">
            <LinearLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="wrap_content" app:layout_scrollFlags="scroll|exitUntilCollapsed">
                <FrameLayout android:id="@id/flTopCardAd" android:layout_width="fill_parent" android:layout_height="wrap_content" />
                <FrameLayout android:id="@id/flAppDownloadAd" android:layout_width="fill_parent" android:layout_height="wrap_content" />
                <FrameLayout android:id="@id/flDownloadingContainer" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="wrap_content" />
            </LinearLayout>
            <LinearLayout android:gravity="center_vertical" android:background="@color/bg_01" android:layout_width="fill_parent" android:layout_height="36.0dip">
                <com.tn.lib.widget.TnTextView android:textSize="16.0sp" android:textColor="@color/text_01" android:layout_width="0.0dip" android:layout_height="wrap_content" android:text="@string/str_downloaded_title" android:layout_weight="1.0" android:layout_marginStart="12.0sp" style="@style/style_import_text" />
                <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/white_60" android:id="@id/tv_available_size" android:layout_width="wrap_content" android:layout_height="wrap_content" android:includeFontPadding="false" style="@style/style_medium_small_text" />
                <com.tn.lib.widget.TnTextView android:textColor="@color/white_60" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/available" android:includeFontPadding="false" android:layout_marginStart="4.0dip" android:layout_marginEnd="12.0dip" style="@style/style_regular_text" />
            </LinearLayout>
            <net.lucode.hackware.magicindicator.MagicIndicator android:id="@id/magic_indicator" android:paddingBottom="8.0dip" android:layout_width="fill_parent" android:layout_height="36.0dip" />
        </com.google.android.material.appbar.AppBarLayout>
        <com.transsion.baseui.widget.NestedScrollableHost android:layout_width="fill_parent" android:layout_height="fill_parent" app:layout_behavior="@string/appbar_scrolling_view_behavior">
            <androidx.viewpager2.widget.ViewPager2 android:id="@id/view_pager" android:layout_width="fill_parent" android:layout_height="fill_parent" />
        </com.transsion.baseui.widget.NestedScrollableHost>
    </androidx.coordinatorlayout.widget.CoordinatorLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
