<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout android:id="@id/rl_film" android:background="@color/bg_01" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.transsion.publish.view.ObservableScrollView android:id="@id/scrollView" android:scrollbars="vertical" android:layout_width="fill_parent" android:layout_height="fill_parent" android:layout_marginBottom="50.0dip">
        <LinearLayout android:orientation="vertical" android:id="@id/ll_top" android:paddingBottom="100.0dip" android:layout_width="fill_parent" android:layout_height="wrap_content">
            <FrameLayout android:id="@id/fl_top" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_below="@id/view_line">
                <RelativeLayout android:id="@id/rl_star" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_below="@id/view_line">
                    <TextView android:textSize="12.0dip" android:textColor="@color/color_ff999999" android:id="@id/tv_click_stars" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="24.0dip" android:text="@string/film_review_click_stars" android:layout_centerHorizontal="true" />
                    <RatingBar android:id="@id/rb_star" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="16.0dip" android:numStars="10" android:rating="0.0" android:stepSize="1.0" android:layout_below="@id/tv_click_stars" android:layout_centerInParent="true" style="@style/RadingStyle" />
                    <TextView android:textSize="16.0dip" android:textColor="#ff333333" android:id="@id/tv_star_tips" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="10.0dip" android:layout_below="@id/rb_star" android:layout_centerInParent="true" />
                </RelativeLayout>
                <RelativeLayout android:id="@id/rl_add_cover" android:background="@color/base_color_f7f7f7" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="185.0dip" android:layout_below="@id/view_line" android:layout_marginStart="16.0dip" android:layout_marginEnd="16.0dip">
                    <ImageView android:id="@id/iv_cover_ic" android:layout_width="32.0dip" android:layout_height="32.0dip" android:layout_marginTop="66.0dip" android:src="@drawable/ic_add_cover" android:layout_centerHorizontal="true" />
                    <TextView android:textSize="12.0sp" android:textColor="@color/base_color_999999" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="4.0dip" android:text="@string/cover_save_add" android:layout_below="@id/iv_cover_ic" android:layout_centerHorizontal="true" />
                    <ImageView android:id="@id/iv_cover" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="fill_parent" android:scaleType="centerCrop" />
                </RelativeLayout>
            </FrameLayout>
            <FrameLayout android:id="@id/fr_title" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="wrap_content">
                <EditText android:textSize="14.0dip" android:textColor="@color/base_color_333333" android:textColorHint="@color/base_color_999999" android:gravity="start|center" android:id="@id/et_title" android:background="@color/transparent" android:scrollbars="vertical" android:layout_width="fill_parent" android:layout_height="wrap_content" android:maxHeight="98.0dip" android:minHeight="49.0dip" android:hint="@string/film_review_title_hint" android:layout_below="@id/fl_top" android:textCursorDrawable="@drawable/post_cursor" android:paddingStart="16.0dip" android:paddingEnd="16.0dip" />
            </FrameLayout>
            <TextView android:textSize="10.0dip" android:textColor="@color/base_color_999999" android:layout_gravity="end" android:id="@id/tv_title_tips" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="00" android:layout_marginEnd="16.0dip" />
            <View android:id="@id/view_line2" android:background="@color/color_eeeeee" android:layout_width="fill_parent" android:layout_height="0.0dip" android:layout_below="@id/tv_title_tips" android:layout_marginStart="16.0dip" android:layout_marginEnd="16.0dip" />
            <EditText android:textSize="16.0sp" android:textColor="@color/white" android:textColorHint="@color/white_80" android:textColorLink="@color/color_4c9ffe" android:gravity="start|center" android:autoLink="web|email" android:linksClickable="false" android:id="@id/et_des" android:background="@color/transparent" android:paddingBottom="28.0dip" android:focusable="true" android:focusableInTouchMode="true" android:scrollbars="none" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="10.0dip" android:hint="@string/film_review_des_hint" android:cursorVisible="true" android:maxLength="1000" android:layout_below="@id/view_line2" android:textCursorDrawable="@drawable/post_cursor" android:paddingStart="16.0dip" android:paddingEnd="16.0dip" style="@style/style_regular_text" />
            <RelativeLayout android:orientation="vertical" android:id="@id/ll_list" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_alignParentBottom="true" android:paddingStart="16.0dip" android:paddingEnd="16.0dip" android:nestedScrollingEnabled="false">
                <androidx.recyclerview.widget.RecyclerView android:id="@id/rv_select" android:layout_width="fill_parent" android:layout_height="wrap_content" />
                <androidx.recyclerview.widget.RecyclerView android:id="@id/rv_link" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="8.0dip" android:layout_below="@id/rv_select" />
            </RelativeLayout>
        </LinearLayout>
    </com.transsion.publish.view.ObservableScrollView>
    <com.tn.lib.view.bubbleview.BubbleTextView android:textColor="@color/color_191F2B" android:id="@id/activity_tip" android:paddingLeft="@dimen/dp_12" android:paddingTop="@dimen/dp_12" android:paddingRight="@dimen/dp_12" android:paddingBottom="@dimen/dp_12" android:visibility="gone" android:layout_width="220.0dip" android:layout_height="wrap_content" android:layout_marginBottom="8.0dip" android:includeFontPadding="false" android:drawablePadding="8.0dip" android:layout_above="@id/oper_view" android:layout_marginStart="10.0dip" app:angle="8.0dip" app:arrowHeight="10.0dip" app:arrowLocation="bottom" app:arrowPosition="103.0dip" app:arrowWidth="16.0dip" app:bubbleColor="@color/white" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" style="@style/style_regular_text" />
    <com.transsion.publish.view.operation.OperationBarView android:id="@id/oper_view" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_alignParentBottom="true" />
    <com.transsion.publish.view.operation.OperationVerticalBarView android:id="@id/oper_vertical_view" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_alignParentBottom="true" />
    <com.tn.lib.view.LoadingAnimView android:id="@id/loading_view" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_centerInParent="true" />
</RelativeLayout>
