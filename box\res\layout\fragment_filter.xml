<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <FrameLayout android:layout_width="fill_parent" android:layout_height="fill_parent">
        <androidx.swiperefreshlayout.widget.SwipeRefreshLayout android:id="@id/swipe_refresh" android:layout_width="fill_parent" android:layout_height="fill_parent">
            <androidx.recyclerview.widget.RecyclerView android:id="@id/movie_list" android:paddingLeft="8.0dip" android:paddingRight="8.0dip" android:layout_width="fill_parent" android:layout_height="fill_parent" />
        </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>
        <include android:id="@id/ll_tab_filter" android:background="@color/module_01" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="30.0dip" app:layout_constraintTop_toBottomOf="@id/ll_tab_movie" layout="@layout/home_layout_filter_float" />
    </FrameLayout>
</LinearLayout>
