<?xml version="1.0" encoding="utf-8"?>
<FrameLayout android:id="@id/root" android:background="@color/cl31" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/thumb" android:layout_width="fill_parent" android:layout_height="fill_parent" android:scaleType="centerCrop" />
    <FrameLayout android:id="@id/video_container" android:layout_width="fill_parent" android:layout_height="fill_parent" />
    <com.transsion.photoview.PhotoView android:id="@id/video_cover" android:layout_width="fill_parent" android:layout_height="fill_parent" android:src="@mipmap/ic_default_video" />
</FrameLayout>
