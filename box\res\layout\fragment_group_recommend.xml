<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <LinearLayout android:orientation="horizontal" android:id="@id/ll_top" android:layout_width="fill_parent" android:layout_height="48.0dip">
        <RelativeLayout android:layout_width="fill_parent" android:layout_height="fill_parent" android:layout_marginTop="8.0dip" android:layout_marginBottom="8.0dip" android:layout_weight="1.0" android:layout_marginStart="16.0dip" android:layout_marginEnd="16.0dip">
            <TextView android:textSize="@dimen/text_size_14" android:textColor="@color/text_01" android:textColorHint="@color/base_color_999999" android:gravity="center_vertical" android:autoLink="none" android:id="@id/comment_input_edit_text" android:background="@drawable/bg_search_bg_01" android:layout_width="fill_parent" android:layout_height="fill_parent" android:maxHeight="88.0dip" android:minHeight="35.0dip" android:hint="@string/search" android:singleLine="true" android:maxLength="200" android:inputType="text" android:imeOptions="actionSearch" android:textCursorDrawable="@drawable/cursor_color_p" android:paddingStart="32.0dip" android:paddingEnd="34.0dip" />
            <ImageView android:layout_width="16.0dip" android:layout_height="16.0dip" android:src="@drawable/ic_search_ic" android:layout_centerVertical="true" android:layout_marginStart="12.0dip" />
            <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_clear" android:padding="6.0dip" android:visibility="gone" android:layout_width="26.0dip" android:layout_height="fill_parent" android:src="@drawable/ic_clear" android:tint="@color/text_03" android:layout_centerVertical="true" android:layout_alignEnd="@id/comment_input_edit_text" />
        </RelativeLayout>
        <TextView android:textSize="14.0dip" android:textColor="@color/text_01" android:gravity="center" android:id="@id/tv_cancel" android:layout_width="wrap_content" android:layout_height="fill_parent" android:text="@string/str_Cancel" android:paddingStart="8.0dip" android:paddingEnd="16.0dip" android:layout_alignParentEnd="true" />
    </LinearLayout>
    <RelativeLayout android:id="@id/rl_tips" android:layout_width="fill_parent" android:layout_height="48.0dip" android:layout_below="@id/ll_top" android:paddingStart="15.0dip" android:paddingEnd="15.0dip">
        <ImageView android:id="@id/icon_group" android:layout_width="17.0dip" android:layout_height="16.0dip" android:src="@drawable/ic_group_gray" android:layout_centerVertical="true" />
        <TextView android:textSize="14.0sp" android:textColor="@color/text_01" android:id="@id/tv_group_select" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/film_review_room" android:layout_centerVertical="true" android:paddingStart="8.0dip" android:layout_marginEnd="15.0dip" android:layout_toEndOf="@id/icon_group" />
        <View android:background="@color/line_01" android:layout_width="fill_parent" android:layout_height="1.0dip" android:layout_alignParentBottom="true" />
        <ImageView android:layout_gravity="end|center" android:id="@id/iv_select" android:visibility="gone" android:layout_width="20.0dip" android:layout_height="20.0dip" android:src="@drawable/ic_group_right_select" android:layout_centerVertical="true" android:layout_marginEnd="16.0dip" android:layout_alignParentEnd="true" />
    </RelativeLayout>
    <androidx.recyclerview.widget.RecyclerView android:id="@id/rv" android:layout_width="fill_parent" android:layout_height="fill_parent" android:layout_below="@id/rl_tips" />
    <ProgressBar android:layout_gravity="center" android:id="@id/load_view" android:visibility="gone" android:layout_width="28.0dip" android:layout_height="28.0dip" android:layout_centerInParent="true" android:indeterminateTint="@color/brand" />
</RelativeLayout>
