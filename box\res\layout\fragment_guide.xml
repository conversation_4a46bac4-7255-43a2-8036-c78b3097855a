<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/guide_image" android:layout_width="fill_parent" android:layout_height="412.0dip" android:layout_marginLeft="25.0dip" android:layout_marginTop="20.0dip" android:layout_marginRight="25.0dip" android:src="@mipmap/ic_guide1" app:layout_constraintTop_toTopOf="parent" />
    <View android:id="@id/bg_transparent" android:background="@drawable/transparent_from_0_100" android:layout_width="fill_parent" android:layout_height="232.0dip" app:layout_constraintBottom_toBottomOf="@id/guide_image" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="28.0sp" android:textColor="@color/text_01" android:gravity="center_horizontal" android:id="@id/guide_title" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginLeft="14.0dip" android:layout_marginRight="14.0dip" android:layout_marginBottom="12.0dip" android:text="@string/guide_title1" app:layout_constraintBottom_toTopOf="@id/guide_desc" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" style="@style/robot_medium" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textColor="@color/text_01" android:gravity="center_horizontal" android:id="@id/guide_desc" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginBottom="24.0dip" android:text="@string/guide_desc1" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" />
    <androidx.appcompat.widget.LinearLayoutCompat android:gravity="center_vertical" android:id="@id/ll_no_network" android:background="@drawable/bg_guide_no_network" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="56.0dip" android:layout_marginLeft="16.0dip" android:layout_marginTop="16.0dip" android:layout_marginRight="16.0dip" app:layout_constraintTop_toTopOf="parent">
        <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/white" android:id="@id/no_network_tip" android:layout_width="0.0dip" android:layout_height="wrap_content" android:text="@string/guide_no_network_tip" android:layout_weight="1.0" android:layout_marginStart="16.0dip" android:layout_marginEnd="12.0dip" style="@style/style_medium_text" />
        <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/update_color_191F2B" android:id="@id/tv_setting" android:background="@drawable/bg_guide_setting" android:paddingLeft="12.0dip" android:paddingTop="8.0dip" android:paddingRight="12.0dip" android:paddingBottom="8.0dip" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/profile_setting" android:layout_marginEnd="12.0dip" />
    </androidx.appcompat.widget.LinearLayoutCompat>
</androidx.constraintlayout.widget.ConstraintLayout>
