<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.tn.lib.widget.TnTextView android:textSize="16.0sp" android:textColor="@color/text_01" android:gravity="center_vertical" android:id="@id/tvTitle" android:layout_width="wrap_content" android:layout_height="40.0dip" android:text="@string/download_watch_history" android:layout_marginStart="16.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" style="@style/style_medium_text" />
    <com.tn.lib.widget.TnTextView android:textColor="@color/text_02" android:gravity="center_vertical" android:id="@id/tvAll" android:layout_width="wrap_content" android:layout_height="40.0dip" android:text="@string/download_watch_history_all" android:drawableEnd="@drawable/libui_ic_medium_arrow_right" android:paddingStart="10.0dip" android:paddingEnd="10.0dip" android:layout_marginEnd="6.0dip" app:layout_constraintBottom_toBottomOf="@id/tvTitle" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="@id/tvTitle" style="@style/style_medium_text" />
    <androidx.recyclerview.widget.RecyclerView android:id="@id/historyRv" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="4.0dip" android:layout_marginBottom="16.0dip" app:layout_constraintBottom_toTopOf="@id/vLine" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tvTitle" />
    <View android:id="@id/vLine" android:background="@color/download_history_line" android:layout_width="0.0dip" android:layout_height="6.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>
