<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:orientation="vertical" android:id="@id/llEmpty" android:layout_width="fill_parent" android:layout_height="fill_parent" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <View android:layout_width="fill_parent" android:layout_height="0.0dip" android:layout_weight="1.0" />
    <androidx.appcompat.widget.AppCompatImageView android:layout_width="fill_parent" android:layout_height="wrap_content" android:src="@mipmap/transfer_list_empty" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="14.0sp" android:textColor="@color/text_01" android:gravity="center" android:layout_width="fill_parent" android:layout_height="wrap_content" android:text="@string/music_empty_tip_1" style="@style/style_import_text" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/text_02" android:gravity="center" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginLeft="30.0dip" android:layout_marginRight="30.0dip" android:text="@string/music_empty_tip_2" android:alpha="0.8" />
    <TextView android:textSize="14.0sp" android:textColor="@color/white" android:gravity="center" android:layout_gravity="center" android:id="@id/tvExploreNow" android:background="@drawable/music_explore_now_bg" android:paddingLeft="30.0dip" android:paddingRight="30.0dip" android:layout_width="wrap_content" android:layout_height="40.0dip" android:layout_marginTop="16.0dip" android:text="@string/music_empty_tip_explore_now" style="@style/style_import_text" />
    <View android:layout_width="fill_parent" android:layout_height="0.0dip" android:layout_weight="1.0" />
</LinearLayout>
