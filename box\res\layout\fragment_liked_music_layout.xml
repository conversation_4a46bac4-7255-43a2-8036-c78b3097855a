<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <FrameLayout android:id="@id/flPlayer" android:layout_width="wrap_content" android:layout_height="wrap_content" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <View android:id="@id/viewTopPlaceholder" android:layout_width="0.0dip" android:layout_height="0.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="14.0sp" android:textColor="@color/text_02" android:gravity="center" android:id="@id/tvVideosCount" android:layout_width="wrap_content" android:layout_height="28.0dip" android:layout_marginTop="16.0dip" android:text="@string/music_videos" android:layout_marginStart="12.0dip" app:layout_constraintEnd_toStartOf="@id/tvPlayAll" app:layout_constraintHorizontal_chainStyle="spread_inside" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/viewTopPlaceholder" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/text_01" android:gravity="center" android:id="@id/tvPlayAll" android:background="@drawable/music_play_all" android:paddingLeft="8.0dip" android:paddingRight="8.0dip" android:layout_width="wrap_content" android:layout_height="0.0dip" android:text="@string/music_play_all" android:layout_marginEnd="12.0dip" app:layout_constraintBottom_toBottomOf="@id/tvVideosCount" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/tvVideosCount" app:layout_constraintTop_toTopOf="@id/tvVideosCount" />
    <androidx.recyclerview.widget.RecyclerView android:id="@id/likedRv" android:paddingBottom="@dimen/tab_bottom_show_height" android:visibility="visible" android:clipToPadding="false" android:layout_width="0.0dip" android:layout_height="0.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tvVideosCount" />
    <com.transsion.videodetail.music.widget.MusicLikedListEmptyView android:id="@id/emptyView" android:visibility="gone" android:layout_width="0.0dip" android:layout_height="0.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/viewTopPlaceholder" />
    <com.transsion.videodetail.music.widget.MusicLikedListLoadingView android:id="@id/loadingView" android:visibility="gone" android:layout_width="0.0dip" android:layout_height="0.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/viewTopPlaceholder" />
</androidx.constraintlayout.widget.ConstraintLayout>
