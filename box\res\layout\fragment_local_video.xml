<?xml version="1.0" encoding="utf-8"?>
<FrameLayout android:background="#ff000000" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <include android:id="@id/local_video_portrait" layout="@layout/layout_local_video_portrait" />
    <include android:id="@id/local_video_land" layout="@layout/layout_local_video_land" />
    <include android:id="@id/local_video_middle" layout="@layout/layout_local_video_middle" />
    <FrameLayout android:layout_gravity="center" android:id="@id/flPauseAdGroup" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content">
        <FrameLayout android:layout_gravity="center" android:id="@id/adContainer" android:layout_width="260.0dip" android:layout_height="wrap_content" android:layout_marginTop="30.0dip" android:layout_marginBottom="30.0dip" android:minHeight="100.0dip" />
        <ImageView android:layout_gravity="end|top" android:id="@id/ivAdPauseClose" android:layout_width="24.0dip" android:layout_height="24.0dip" android:src="@mipmap/ad_close" />
    </FrameLayout>
</FrameLayout>
