<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <View android:id="@id/sub_movie_header_bg" android:background="@drawable/home_title_gradient_bg" android:layout_width="fill_parent" android:layout_height="80.0dip" />
    <include android:id="@id/ll_tab_movie" android:layout_width="fill_parent" android:layout_height="wrap_content" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" layout="@layout/home_layout_movie_tabs" />
    <View android:id="@id/popup_filter_view_linear" android:background="@color/line_01" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="1.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/ll_tab_movie" />
    <com.transsion.home.view.filter.popup.PopupFilterView android:orientation="horizontal" android:id="@id/popup_filter_view" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginLeft="12.0dip" android:layout_marginTop="12.0dip" android:layout_marginRight="12.0dip" android:layout_marginBottom="8.0dip" app:layout_constraintTop_toBottomOf="@id/popup_filter_view_linear" />
    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout android:id="@id/swipe_refresh" android:background="@color/bg_01" android:layout_width="fill_parent" android:layout_height="fill_parent">
        <androidx.recyclerview.widget.RecyclerView android:id="@id/movie_list" android:paddingLeft="8.0dip" android:paddingRight="8.0dip" android:layout_width="fill_parent" android:layout_height="fill_parent" />
    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>
</LinearLayout>
