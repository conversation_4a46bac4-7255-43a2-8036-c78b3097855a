<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <View android:id="@id/v_top_space" android:layout_width="0.0dip" android:layout_height="0.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <FrameLayout android:id="@id/fl_player" android:layout_width="fill_parent" android:layout_height="0.0dip" app:layout_constraintDimensionRatio="h,16:9" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/v_top_space" />
    <View android:background="@drawable/bg_movie_detail_toolbar" android:layout_width="fill_parent" android:layout_height="80.0dip" app:layout_constraintTop_toTopOf="parent" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/ivBack" android:paddingTop="12.0dip" android:paddingBottom="12.0dip" android:layout_width="wrap_content" android:layout_height="wrap_content" android:src="@mipmap/icon_white_back" android:scaleType="centerCrop" android:paddingStart="16.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/v_top_space" />
    <androidx.coordinatorlayout.widget.CoordinatorLayout android:id="@id/cdl" android:layout_width="fill_parent" android:layout_height="0.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintTop_toBottomOf="@id/fl_player">
        <com.google.android.material.appbar.AppBarLayout android:orientation="vertical" android:id="@id/appBar" android:layout_width="fill_parent" android:layout_height="wrap_content">
            <com.google.android.material.appbar.CollapsingToolbarLayout android:id="@id/toolbar_layout" android:layout_width="fill_parent" android:layout_height="wrap_content" app:contentScrim="@color/transparent" app:layout_scrollFlags="scroll|exitUntilCollapsed">
                <androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="wrap_content">
                    <FrameLayout android:id="@id/subjectDetailLayout" android:layout_width="fill_parent" android:layout_height="wrap_content" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
                    <FrameLayout android:id="@id/resourceDetectorGroup" android:layout_width="fill_parent" android:layout_height="wrap_content" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/subjectDetailLayout" />
                    <FrameLayout android:id="@id/game_container" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="64.0dip" android:layout_marginLeft="12.0dip" android:layout_marginRight="12.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/resourceDetectorGroup" />
                </androidx.constraintlayout.widget.ConstraintLayout>
            </com.google.android.material.appbar.CollapsingToolbarLayout>
            <LinearLayout android:orientation="horizontal" android:background="@color/bg_01" android:layout_width="fill_parent" android:layout_height="38.0dip">
                <net.lucode.hackware.magicindicator.MagicIndicator android:id="@id/magic_indicator" android:layout_width="0.0dip" android:layout_height="fill_parent" android:layout_weight="1.0" />
                <com.transsion.publish.view.PublishStateView android:layout_gravity="center_vertical" android:id="@id/ivPublish" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginEnd="12.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent" />
            </LinearLayout>
            <View android:background="@color/line_01" android:layout_width="fill_parent" android:layout_height="1.0dip" />
        </com.google.android.material.appbar.AppBarLayout>
        <com.transsion.baseui.widget.NestedScrollableHost android:layout_width="fill_parent" android:layout_height="fill_parent" app:layout_behavior="@string/appbar_scrolling_view_behavior">
            <androidx.viewpager2.widget.ViewPager2 android:id="@id/rec_view_pager" android:layout_width="fill_parent" android:layout_height="fill_parent" />
        </com.transsion.baseui.widget.NestedScrollableHost>
    </androidx.coordinatorlayout.widget.CoordinatorLayout>
    <com.tn.lib.view.bubbleview.BubbleTextView android:textColor="@color/white" android:id="@id/activity_tip" android:paddingLeft="@dimen/dp_12" android:paddingTop="@dimen/dp_12" android:paddingRight="@dimen/dp_12" android:paddingBottom="@dimen/dp_12" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginBottom="8.0dip" android:text="@string/earn_up_to_2GB_mobile_data" android:includeFontPadding="false" android:drawablePadding="8.0dip" android:drawableEnd="@mipmap/ic_bubble_close" app:angle="8.0dip" app:arrowHeight="10.0dip" app:arrowLocation="bottom" app:arrowPosition="103.0dip" app:arrowWidth="16.0dip" app:bubbleColor="@color/black_70" app:layout_constraintBottom_toTopOf="@id/btn_download" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" />
    <com.transsnet.downloader.widget.DownloadView android:id="@id/btn_download" android:visibility="gone" android:layout_width="0.0dip" android:layout_height="0.0dip" android:layout_marginBottom="0.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" />
    <FrameLayout android:id="@id/fl_bottom_dialog_container" android:layout_width="fill_parent" android:layout_height="0.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintTop_toBottomOf="@id/fl_player" />
    <FrameLayout android:id="@id/fl_full_player_container" android:layout_width="0.0dip" android:layout_height="0.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <include android:id="@id/layout_sync_adjust" layout="@layout/long_vod_layout_subtitle_sync_adjust" />
</androidx.constraintlayout.widget.ConstraintLayout>
