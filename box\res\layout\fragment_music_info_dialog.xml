<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:background="@color/module_01" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.appcompat.widget.LinearLayoutCompat android:gravity="center_vertical" android:orientation="horizontal" android:id="@id/tv_header_toolbar" android:layout_width="fill_parent" android:layout_height="48.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent">
        <androidx.appcompat.widget.AppCompatTextView android:textSize="@dimen/dimens_sp_18" android:textStyle="bold" android:textColor="@color/white" android:gravity="center_vertical" android:layout_width="0.0dip" android:layout_height="fill_parent" android:text="@string/movie_detail_more_details" android:layout_weight="1.0" android:layout_marginStart="12.0dip" />
        <androidx.appcompat.widget.AppCompatImageView android:id="@id/tv_close" android:layout_width="44.0dip" android:layout_height="44.0dip" android:src="@mipmap/ic_close" android:scaleType="center" />
    </androidx.appcompat.widget.LinearLayoutCompat>
    <View android:background="@color/border" android:layout_width="fill_parent" android:layout_height="1.0dip" app:layout_constraintTop_toBottomOf="@id/tv_header_toolbar" />
    <androidx.core.widget.NestedScrollView android:tag="scrollView" android:persistentDrawingCache="animation" android:layout_width="0.0dip" android:layout_height="0.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tv_header_toolbar" app:layout_scrollFlags="scroll">
        <androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="wrap_content">
            <androidx.appcompat.widget.AppCompatTextView android:textSize="18.0sp" android:textColor="@color/white" android:ellipsize="end" android:gravity="start|center" android:id="@id/tvMovieTitle" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginLeft="12.0dip" android:layout_marginTop="12.0dip" android:layout_marginRight="12.0dip" android:textAlignment="viewStart" app:layout_constraintBottom_toTopOf="@id/tvTag" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:layout_constraintVertical_chainStyle="packed" style="@style/style_import_text" />
            <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/text_02" android:ellipsize="end" android:gravity="start" android:id="@id/tvTag" android:layout_width="0.0dip" android:layout_marginTop="4.0dip" android:maxLines="1" android:drawablePadding="@dimen/dp_4" android:lineSpacingExtra="2.0dip" android:drawableStart="@drawable/ic_tag_audio" android:textAlignment="viewStart" android:layout_marginEnd="12.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="@id/tvMovieTitle" app:layout_constraintTop_toBottomOf="@id/tvMovieTitle" style="@style/style_regular_text" />
            <LinearLayout android:id="@id/tv_music_avatar" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginLeft="12.0dip" android:layout_marginTop="@dimen/dp_12" android:layout_marginRight="12.0dip" app:layout_constraintTop_toBottomOf="@id/tvTag">
                <com.google.android.material.imageview.ShapeableImageView android:id="@id/ivAvatar" android:layout_width="28.0dip" android:layout_height="28.0dip" app:shapeAppearanceOverlay="@style/circle_style" />
                <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/text_02" android:ellipsize="end" android:layout_gravity="center_vertical" android:id="@id/tvDes" android:layout_width="wrap_content" android:layout_height="wrap_content" android:maxLines="1" android:layout_marginStart="8.0dip" />
            </LinearLayout>
            <com.transsion.moviedetail.view.InfoExtendView android:id="@id/infoExtendView" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="16.0dip" android:layout_marginBottom="16.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tv_music_avatar" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.core.widget.NestedScrollView>
</androidx.constraintlayout.widget.ConstraintLayout>
