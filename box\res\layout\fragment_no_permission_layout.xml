<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/ivDefaultImage" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="134.0dip" android:layout_marginBottom="16.0dip" android:src="@mipmap/ic_no_permission" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <com.tn.lib.widget.TnTextView android:textColor="@color/text_02" android:gravity="center" android:id="@id/tvDesc" android:layout_width="0.0dip" android:layout_height="wrap_content" android:text="@string/download_no_permission_new_tips" android:layout_marginStart="66.0dip" android:layout_marginEnd="66.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/ivDefaultImage" style="@style/style_regular_text" />
    <com.tn.lib.widget.TnTextView android:textColor="@color/text_01" android:gravity="center" android:id="@id/btn" android:background="@drawable/bg_btn_permission" android:visibility="visible" android:layout_width="144.0dip" android:layout_height="40.0dip" android:layout_marginTop="16.0dip" android:text="@string/download_no_permission_btn" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tvDesc" style="@style/style_medium_text" />
</androidx.constraintlayout.widget.ConstraintLayout>
