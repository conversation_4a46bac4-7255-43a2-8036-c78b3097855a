<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:id="@id/clRootView" android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textStyle="bold" android:textColor="@color/text_01" android:id="@id/tvComments" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginTop="16.0dip" android:layout_marginStart="16.0dip" app:layout_constraintEnd_toStartOf="@id/ivRight" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <androidx.constraintlayout.widget.Group android:id="@id/groupNoCommentYet" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" app:constraint_referenced_ids="ivRight,itemCommentUserAvatar,itemCommentUserName,itemCommentContent,itemCommentData,itemCommentReply" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/ivRight" android:layout_width="16.0dip" android:layout_height="16.0dip" android:src="@drawable/post_icon_arrow_right" android:layout_marginEnd="16.0dip" app:layout_constraintBottom_toBottomOf="@id/tvComments" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="@id/tvComments" />
    <com.google.android.material.imageview.ShapeableImageView android:id="@id/itemCommentUserAvatar" android:layout_width="32.0dip" android:layout_height="32.0dip" android:layout_marginTop="16.0dip" android:scaleType="centerCrop" android:layout_marginStart="16.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tvComments" app:shapeAppearanceOverlay="@style/circle_style" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="@dimen/text_size_12" android:textColor="@color/text_05" android:ellipsize="end" android:id="@id/itemCommentUserName" android:layout_width="wrap_content" android:layout_height="wrap_content" android:maxWidth="200.0dip" android:maxLines="1" android:singleLine="true" android:layout_marginStart="8.0dip" android:layout_marginEnd="52.0dip" app:layout_constraintStart_toEndOf="@id/itemCommentUserAvatar" app:layout_constraintTop_toTopOf="@id/itemCommentUserAvatar" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="@dimen/text_size_14" android:textColor="@color/text_01" android:ellipsize="end" android:id="@id/itemCommentContent" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginTop="4.0dip" android:maxLines="4" app:layout_constraintEnd_toStartOf="@id/itemCommentLikeIcon" app:layout_constraintStart_toStartOf="@id/itemCommentUserName" app:layout_constraintTop_toBottomOf="@id/itemCommentUserName" />
    <androidx.appcompat.widget.AppCompatImageView android:enabled="false" android:id="@id/itemCommentLikeIcon" android:visibility="gone" android:layout_width="17.0dip" android:layout_height="16.0dip" android:layout_marginEnd="16.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="@id/itemCommentUserName" app:srcCompat="@drawable/comment_item_like_selector" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="@dimen/text_size_12" android:textColor="@color/text_05" android:id="@id/itemCommentData" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="8.0dip" app:layout_constraintStart_toStartOf="@id/itemCommentUserName" app:layout_constraintTop_toBottomOf="@id/itemCommentContent" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="@dimen/text_size_12" android:textColor="@color/text_05" android:id="@id/itemCommentReply" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/comment_reply" android:layout_marginStart="15.0dip" app:layout_constraintBottom_toBottomOf="@id/itemCommentData" app:layout_constraintStart_toEndOf="@id/itemCommentData" app:layout_constraintTop_toTopOf="@id/itemCommentData" />
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/text_06" android:gravity="center" android:id="@id/tvNoCommentYet" android:visibility="gone" android:layout_width="0.0dip" android:layout_height="wrap_content" android:text="@string/no_comment_yet" app:layout_constraintBottom_toTopOf="@id/postDetailOperationView" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tvComments" />
    <com.transsion.postdetail.ui.view.PostDetailOperationView android:id="@id/postDetailOperationView" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginTop="9.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/itemCommentReply" app:layout_goneMarginTop="46.0dip" />
    <View android:background="@color/border_3" android:layout_width="0.0dip" android:layout_height="6.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/postDetailOperationView" />
</androidx.constraintlayout.widget.ConstraintLayout>
