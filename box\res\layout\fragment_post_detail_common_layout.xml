<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:id="@id/cl_container" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.coordinatorlayout.widget.CoordinatorLayout android:id="@id/clContent" android:layout_width="fill_parent" android:layout_height="0.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent">
        <com.google.android.material.appbar.AppBarLayout android:id="@id/appBar" android:visibility="visible" android:layout_width="fill_parent" android:layout_height="wrap_content">
            <com.google.android.material.appbar.CollapsingToolbarLayout android:id="@id/toolbarLayout" android:layout_width="fill_parent" android:layout_height="wrap_content" app:layout_scrollFlags="scroll|exitUntilCollapsed">
                <androidx.constraintlayout.widget.ConstraintLayout android:id="@id/contentView" android:layout_width="fill_parent" android:layout_height="wrap_content">
                    <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_cover" android:layout_width="fill_parent" android:layout_height="@dimen/post_surface_height" android:src="@color/cl31" android:scaleType="centerCrop" app:layout_constraintTop_toTopOf="parent" />
                    <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_audio_wave" android:layout_width="wrap_content" android:layout_height="wrap_content" android:src="@mipmap/post_detail_ic_audio_player" app:layout_constraintBottom_toBottomOf="@id/iv_cover" app:layout_constraintEnd_toEndOf="@id/iv_cover" app:layout_constraintStart_toStartOf="@id/iv_cover" app:layout_constraintTop_toTopOf="@id/iv_cover" />
                    <androidx.constraintlayout.widget.ConstraintLayout android:id="@id/ad_bottom_controller" android:background="@drawable/libui_mask_cl45_30p_to_0p" android:layout_width="fill_parent" android:layout_height="48.0dip" app:layout_constraintBottom_toBottomOf="@id/iv_cover">
                        <androidx.appcompat.widget.AppCompatImageView android:id="@id/ad_pause" android:layout_width="24.0dip" android:layout_height="24.0dip" android:src="@drawable/post_icon_pro_pause" android:scaleType="center" android:layout_marginStart="12.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintLeft_toLeftOf="parent" app:layout_constraintTop_toTopOf="parent" />
                        <androidx.appcompat.widget.AppCompatSeekBar android:id="@id/ad_seekbar" android:background="@color/transparent" android:layout_width="0.0dip" android:layout_height="24.0dip" android:maxHeight="2.0dip" android:progress="0" android:progressDrawable="@drawable/post_detail_layer_seekbar" android:minHeight="2.0dip" android:thumb="@drawable/post_detail_shape_seekbar_bar" android:layout_marginStart="6.0dip" android:layout_marginEnd="12.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintLeft_toRightOf="@id/ad_pause" app:layout_constraintRight_toLeftOf="@id/ad_audio_time" app:layout_constraintTop_toTopOf="parent" />
                        <TextView android:textSize="11.0sp" android:textColor="@color/white" android:id="@id/ad_audio_time" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginEnd="12.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintRight_toRightOf="parent" app:layout_constraintTop_toTopOf="parent" />
                    </androidx.constraintlayout.widget.ConstraintLayout>
                    <androidx.constraintlayout.widget.Group android:id="@id/groupAudio" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" app:constraint_referenced_ids="iv_cover,iv_audio_wave,ad_bottom_controller" />
                    <LinearLayout android:orientation="vertical" android:id="@id/llTop" android:layout_width="fill_parent" android:layout_height="fill_parent" app:layout_constraintTop_toBottomOf="@id/iv_cover" app:layout_goneMarginTop="44.0dip">
                        <View android:layout_width="fill_parent" android:layout_height="16.0dip" />
                        <include android:id="@id/tvPostTitle" layout="@layout/post_detail_title" />
                        <include android:id="@id/clRating" layout="@layout/post_detail_rating" />
                        <include android:id="@id/tvPostDesc" layout="@layout/post_detail_desc" />
                        <com.transsion.ninegridview.video.NineGridVideoView android:id="@id/nine_grid" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginBottom="8.0dip" />
                        <com.transsion.postdetail.ui.view.PostDetailSubjectView android:id="@id/postDetailSubjectView" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginStart="4.0dip" android:layout_marginEnd="4.0dip" />
                        <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/cl12" android:ellipsize="end" android:gravity="center_vertical" android:id="@id/tvSubject" android:background="@drawable/post_detail_group_v2_bg" android:paddingTop="4.0dip" android:paddingBottom="4.0dip" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="16.0dip" android:maxLines="1" android:paddingStart="6.0dip" android:paddingEnd="6.0dip" android:layout_marginStart="16.0dip" app:drawableEndCompat="@mipmap/ic_group_arrow" app:drawableStartCompat="@mipmap/post_ic_group" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" style="@style/style_regular_text" />
                        <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/text_03" android:id="@id/tvPostTime" android:layout_marginTop="16.0dip" android:layout_marginStart="16.0dip" app:layout_constraintBottom_toBottomOf="@id/tvName" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="@id/tvName" style="@style/style_regular_text" />
                        <View android:background="@color/border_2" android:layout_width="fill_parent" android:layout_height="1.0dip" android:layout_marginTop="16.0dip" android:layout_marginStart="16.0dip" android:layout_marginEnd="16.0dip" />
                    </LinearLayout>
                </androidx.constraintlayout.widget.ConstraintLayout>
                <androidx.appcompat.widget.Toolbar android:id="@id/toolbar" android:background="@color/bg_01" android:layout_width="fill_parent" android:layout_height="44.0dip" app:contentInsetLeft="0.0dip" app:contentInsetStart="0.0dip" app:layout_collapseMode="pin">
                    <androidx.constraintlayout.widget.ConstraintLayout android:visibility="visible" android:layout_width="fill_parent" android:layout_height="fill_parent" app:layout_constraintTop_toTopOf="parent">
                        <androidx.appcompat.widget.AppCompatImageView android:id="@id/ivBack" android:layout_width="24.0dip" android:layout_height="24.0dip" android:src="@mipmap/movie_detail_icon_black_back" android:scaleType="centerCrop" android:layout_marginStart="16.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
                        <com.google.android.material.imageview.ShapeableImageView android:id="@id/ivAvatar" android:visibility="visible" android:layout_width="28.0dip" android:layout_height="28.0dip" android:layout_marginStart="2.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toEndOf="@id/ivBack" app:layout_constraintTop_toTopOf="parent" app:shapeAppearanceOverlay="@style/circle_style" />
                        <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/text_01" android:ellipsize="end" android:id="@id/tvTitle" android:layout_width="0.0dip" android:lines="1" android:textAlignment="viewStart" android:layout_marginStart="4.0dip" android:layout_marginEnd="@dimen/right_margin" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/ivAvatar" app:layout_constraintTop_toTopOf="parent" style="@style/style_regula_bigger_text" />
                        <View android:id="@id/viewTitleLine" android:background="@color/border" android:layout_width="0.0dip" android:layout_height="1.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" />
                    </androidx.constraintlayout.widget.ConstraintLayout>
                </androidx.appcompat.widget.Toolbar>
            </com.google.android.material.appbar.CollapsingToolbarLayout>
        </com.google.android.material.appbar.AppBarLayout>
        <FrameLayout android:id="@id/flContainer" android:visibility="visible" android:layout_width="fill_parent" android:layout_height="fill_parent" app:layout_behavior="@string/appbar_scrolling_view_behavior" />
    </androidx.coordinatorlayout.widget.CoordinatorLayout>
    <View android:id="@id/viewBg" android:background="@color/border_2" android:visibility="visible" android:layout_width="0.0dip" android:layout_height="1.0dip" app:layout_constraintBottom_toTopOf="@id/postDetailOperationView" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" />
    <com.transsion.postdetail.ui.view.PostDetailOperationView android:id="@id/postDetailOperationView" android:visibility="visible" android:layout_width="fill_parent" android:layout_height="wrap_content" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>
