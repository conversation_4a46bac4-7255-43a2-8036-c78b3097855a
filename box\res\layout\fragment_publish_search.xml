<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <LinearLayout android:orientation="horizontal" android:id="@id/ll_top" android:layout_width="fill_parent" android:layout_height="48.0dip" android:paddingEnd="16.0dip">
        <ImageView android:layout_gravity="center_vertical" android:id="@id/iv_cancel" android:paddingTop="4.0dip" android:paddingBottom="4.0dip" android:layout_width="wrap_content" android:layout_height="wrap_content" android:src="@mipmap/base_back_black" android:paddingStart="16.0dip" />
        <RelativeLayout android:layout_width="fill_parent" android:layout_height="fill_parent" android:layout_marginTop="8.0dip" android:layout_marginBottom="8.0dip" android:layout_weight="1.0" android:layout_marginStart="8.0dip">
            <EditText android:textSize="@dimen/text_size_14" android:textColor="@color/text_01" android:textColorHint="@color/base_color_999999" android:gravity="center_vertical" android:autoLink="none" android:id="@id/comment_input_edit_text" android:background="@drawable/bg_search_bg_01" android:layout_width="fill_parent" android:layout_height="fill_parent" android:maxHeight="88.0dip" android:minHeight="35.0dip" android:hint="@string/search_hint_input" android:singleLine="true" android:maxLength="200" android:inputType="text" android:imeOptions="actionSearch" android:textCursorDrawable="@drawable/cursor_color_p" android:paddingStart="32.0dip" android:paddingEnd="34.0dip" />
            <ImageView android:layout_width="16.0dip" android:layout_height="16.0dip" android:src="@drawable/ic_search_ic" android:layout_centerVertical="true" android:layout_marginStart="12.0dip" />
            <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_clear" android:padding="6.0dip" android:visibility="gone" android:layout_width="26.0dip" android:layout_height="fill_parent" android:src="@drawable/ic_clear" android:tint="@color/text_03" android:layout_centerVertical="true" android:layout_alignEnd="@id/comment_input_edit_text" />
        </RelativeLayout>
        <TextView android:textSize="14.0dip" android:textColor="@color/brand" android:gravity="center" android:id="@id/tv_search" android:layout_width="wrap_content" android:layout_height="fill_parent" android:text="@string/search" android:paddingStart="8.0dip" android:layout_alignParentEnd="true" />
    </LinearLayout>
    <View android:background="@color/line_01" android:layout_width="fill_parent" android:layout_height="0.5dip" android:layout_below="@id/ll_top" />
    <RelativeLayout android:id="@id/rl_tips" android:layout_width="fill_parent" android:layout_height="35.0dip" android:layout_marginTop="16.0dip" android:layout_below="@id/ll_top" android:paddingStart="16.0dip">
        <TextView android:textSize="16.0dip" android:textColor="@color/text_01" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/search_recent" android:layout_alignParentStart="true" />
        <TextView android:textSize="16.0dip" android:textColor="@color/text_01" android:id="@id/tv_clear" android:layout_width="wrap_content" android:layout_height="fill_parent" android:text="@string/search_clear" android:paddingStart="15.0dip" android:paddingEnd="15.0dip" android:layout_alignParentEnd="true" />
    </RelativeLayout>
    <androidx.recyclerview.widget.RecyclerView android:id="@id/rv" android:layout_width="fill_parent" android:layout_height="fill_parent" android:layout_below="@id/rl_tips" />
    <ProgressBar android:id="@id/load_view" android:visibility="gone" android:layout_width="28.0dip" android:layout_height="28.0dip" android:layout_centerInParent="true" android:indeterminateTint="@color/main" />
</RelativeLayout>
