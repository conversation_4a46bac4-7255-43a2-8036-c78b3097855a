<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <ImageView android:layout_gravity="center_vertical" android:id="@id/iv_cancel" android:layout_width="52.0dip" android:layout_height="@dimen/dimens_24" android:layout_marginTop="@dimen/dp_12" android:src="@mipmap/movie_detail_icon_white_back" android:paddingStart="12.0dip" android:paddingEnd="16.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <EditText android:textSize="@dimen/text_size_14" android:textColor="@color/text_01" android:textColorHint="@color/text_02" android:gravity="center_vertical" android:autoLink="none" android:id="@id/inputET" android:background="@drawable/bg_search_bg_01" android:layout_width="0.0dip" android:layout_height="36.0dip" android:layout_marginTop="@dimen/dp_6" android:maxHeight="88.0dip" android:minHeight="35.0dip" android:hint="@string/search_hint_input" android:singleLine="true" android:maxLength="200" android:inputType="text" android:imeOptions="actionSearch" android:textCursorDrawable="@drawable/cursor_color_p" android:paddingStart="36.0dip" android:paddingEnd="66.0dip" android:layout_marginEnd="@dimen/dp_12" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/iv_cancel" app:layout_constraintTop_toTopOf="parent" style="@style/robot_medium" />
    <ImageView android:layout_width="16.0dip" android:layout_height="16.0dip" android:src="@drawable/ic_search_edt" android:layout_marginStart="12.0dip" app:layout_constraintBottom_toBottomOf="@id/inputET" app:layout_constraintStart_toStartOf="@id/inputET" app:layout_constraintTop_toTopOf="@id/inputET" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/clearIV" android:padding="11.0dip" android:visibility="gone" android:layout_width="36.0dip" android:layout_height="36.0dip" android:src="@drawable/ic_clear" android:tint="@color/color_B2B2B2" android:layout_centerVertical="true" app:layout_constraintBottom_toBottomOf="@id/inputET" app:layout_constraintEnd_toEndOf="@id/inputET" app:layout_constraintTop_toTopOf="@id/inputET" />
    <TextView android:textSize="@dimen/text_size_14" android:textColor="@color/color_2FF58B" android:gravity="center" android:id="@id/searchTV" android:layout_width="wrap_content" android:layout_height="36.0dip" android:text="@string/search" android:paddingStart="8.0dip" android:paddingEnd="12.0dip" app:layout_constraintBottom_toBottomOf="@id/inputET" app:layout_constraintEnd_toEndOf="@id/inputET" app:layout_constraintTop_toTopOf="@id/inputET" style="@style/robot_bold" />
    <View android:id="@id/line" android:background="@color/line_01" android:layout_width="fill_parent" android:layout_height="1.0px" android:layout_marginTop="6.0dip" app:layout_constraintTop_toBottomOf="@id/inputET" />
    <TextView android:textSize="@dimen/text_size_16" android:textColor="@color/text_01" android:id="@id/recentTV" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="@dimen/dp_12" android:text="@string/search_publish_recent_title" android:layout_marginStart="@dimen/dp_12" android:layout_alignParentStart="true" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/line" style="@style/robot_bold" />
    <androidx.recyclerview.widget.RecyclerView android:id="@id/rv" android:paddingTop="@dimen/dp_8" android:clipToPadding="false" android:layout_width="fill_parent" android:layout_height="0.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintTop_toBottomOf="@id/recentTV" />
    <ProgressBar android:id="@id/loadView" android:visibility="gone" android:layout_width="36.0dip" android:layout_height="36.0dip" android:layout_centerInParent="true" android:indeterminateTint="@color/white" app:layout_constraintBottom_toBottomOf="@id/rv" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="@id/rv" />
</androidx.constraintlayout.widget.ConstraintLayout>
