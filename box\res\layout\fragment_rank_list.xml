<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.recyclerview.widget.RecyclerView android:id="@id/rank_all_list_recycler" android:layout_width="fill_parent" android:layout_height="fill_parent" />
    <androidx.constraintlayout.widget.ConstraintLayout android:id="@id/rank_list_loading_frame" android:background="@color/bg_01" android:visibility="invisible" android:layout_width="fill_parent" android:layout_height="fill_parent">
        <ProgressBar android:layout_gravity="center_horizontal" android:visibility="visible" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" style="@style/style_progress" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
