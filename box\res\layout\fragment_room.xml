<?xml version="1.0" encoding="utf-8"?>
<androidx.appcompat.widget.LinearLayoutCompat android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.tn.lib.view.TitleLayout android:id="@id/tool_bar" android:layout_width="fill_parent" android:layout_height="wrap_content" app:isShowBack="true" app:titleText="@string/str_hot_room" />
    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout android:id="@id/swipe_refresh" android:layout_width="fill_parent" android:layout_height="fill_parent">
        <com.tn.lib.view.AdvRecyclerView android:id="@id/rv_room" android:layout_width="fill_parent" android:layout_height="fill_parent" app:adv_layout_empty="@layout/room_empty_default" app:adv_layout_error="@layout/adv_error_default" />
    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>
</androidx.appcompat.widget.LinearLayoutCompat>
