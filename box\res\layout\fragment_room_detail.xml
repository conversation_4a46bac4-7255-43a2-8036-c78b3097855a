<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:id="@id/room_detail" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.coordinatorlayout.widget.CoordinatorLayout android:id="@id/content" android:layout_width="fill_parent" android:layout_height="fill_parent">
        <com.google.android.material.appbar.AppBarLayout android:orientation="vertical" android:id="@id/app_bar" android:layout_width="fill_parent" android:layout_height="wrap_content">
            <com.google.android.material.appbar.CollapsingToolbarLayout android:id="@id/toolbar_layout" android:layout_width="fill_parent" android:layout_height="wrap_content" app:layout_scrollFlags="scroll|exitUntilCollapsed">
                <androidx.constraintlayout.widget.ConstraintLayout android:id="@id/cl_bar" android:layout_width="fill_parent" android:layout_height="wrap_content">
                    <androidx.appcompat.widget.AppCompatImageView android:id="@id/bg_transparent" android:visibility="visible" android:layout_width="fill_parent" android:layout_height="wrap_content" android:src="@color/cl45_30_p" android:scaleType="centerCrop" app:layout_constraintTop_toTopOf="parent" />
                    <com.google.android.material.imageview.ShapeableImageView android:id="@id/iv_room_blur_cover" android:visibility="gone" android:layout_width="56.0dip" android:layout_height="56.0dip" android:layout_marginTop="100.0dip" android:scaleType="centerCrop" android:layout_marginStart="16.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:shapeAppearanceOverlay="@style/roundStyle_8" />
                    <com.google.android.material.imageview.ShapeableImageView android:id="@id/iv_room_cover" android:layout_width="56.0dip" android:layout_height="56.0dip" android:layout_marginTop="100.0dip" android:src="@color/cl37" android:scaleType="centerCrop" android:layout_marginStart="16.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:shapeAppearanceOverlay="@style/roundStyle_8" />
                    <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textColor="@color/cl38" android:ellipsize="end" android:gravity="start|center" android:id="@id/tv_room_title" android:layout_width="168.0dip" android:lines="3" android:textAlignment="viewStart" android:layout_marginStart="8.0dip" app:layout_constraintBottom_toBottomOf="@id/iv_room_cover" app:layout_constraintStart_toEndOf="@id/iv_room_cover" app:layout_constraintTop_toTopOf="@id/iv_room_cover" style="@style/style_import_text" />
                    <com.tn.lib.view.CheckInAnimationView android:gravity="center" android:id="@id/tvCheckIn" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginEnd="16.0dip" app:layout_constraintBottom_toBottomOf="@id/iv_room_cover" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="@id/iv_room_cover" />
                    <TextView android:textSize="14.0sp" android:textColor="@color/white_ff_e" android:gravity="center" android:id="@id/tv_edit" android:background="@drawable/libui_sub_join_normal_tran" android:visibility="gone" android:layout_width="81.0dip" android:layout_height="26.0dip" android:text="@string/str_edit" android:layout_marginEnd="23.0dip" app:layout_constraintBottom_toBottomOf="@id/iv_room_cover" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="@id/iv_room_cover" style="@style/style_regular_text" />
                    <com.tn.lib.view.expand.ExpandView android:textColor="@color/white_ff_e" android:gravity="start" android:id="@id/tv_room_info_desc" android:layout_width="fill_parent" android:layout_marginTop="8.0dip" android:textAlignment="viewStart" android:paddingStart="18.0dip" android:paddingEnd="18.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/iv_room_cover" app:pop_expand_bg_Color="@color/base_white_70_p" app:pop_expand_hint_color="@color/cl01" app:pop_hint_text_size="10.0sp" app:pop_reverse_Lines="2" app:pop_shrink_bg_color="@color/base_white_70_p" app:pop_shrink_hint_color="@color/cl01" style="@style/style_regular_text" />
                    <com.transsion.room.view.roundimage.PileLayout android:id="@id/pl_member_ic" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="12.0dip" android:layout_marginBottom="24.0dip" android:layout_marginStart="16.0dip" app:PileLayout_pileWidth="4.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tv_room_info_desc" />
                    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/white_ff_e" android:gravity="start" android:id="@id/tv_member_count" android:visibility="gone" android:text="@string/member_count_check_in" android:drawablePadding="2.0dip" android:layout_marginStart="8.0dip" app:layout_constraintBottom_toBottomOf="@id/pl_member_ic" app:layout_constraintStart_toEndOf="@id/pl_member_ic" app:layout_constraintTop_toTopOf="@id/pl_member_ic" style="@style/style_regular_text" />
                </androidx.constraintlayout.widget.ConstraintLayout>
                <androidx.appcompat.widget.Toolbar android:id="@id/toolbar" android:layout_width="fill_parent" android:layout_height="44.0dip" app:contentInsetLeft="0.0dip" app:contentInsetStart="0.0dip" app:layout_collapseMode="pin">
                    <androidx.constraintlayout.widget.ConstraintLayout android:visibility="visible" android:layout_width="fill_parent" android:layout_height="fill_parent" app:layout_constraintTop_toTopOf="parent">
                        <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_back" android:layout_width="24.0dip" android:layout_height="24.0dip" android:src="@mipmap/icon_white_back" android:scaleType="centerCrop" android:layout_marginStart="16.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
                        <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_share" android:layout_width="24.0dip" android:layout_height="24.0dip" android:src="@mipmap/libui_ic_base_whit_share" android:scaleType="centerCrop" android:layout_marginEnd="12.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toStartOf="@id/ivPublish" app:layout_constraintTop_toTopOf="parent" app:layout_goneMarginEnd="12.0dip" />
                        <com.transsion.publish.view.PublishStateView android:id="@id/ivPublish" android:layout_width="24.0dip" android:layout_height="24.0dip" android:layout_marginEnd="16.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent" />
                        <com.google.android.material.imageview.ShapeableImageView android:id="@id/iv_cover_small" android:visibility="gone" android:layout_width="28.0dip" android:layout_height="28.0dip" android:scaleType="centerCrop" android:layout_marginStart="8.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toEndOf="@id/iv_back" app:layout_constraintTop_toTopOf="parent" app:shapeAppearanceOverlay="@style/circle_style" />
                        <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/text_01" android:ellipsize="end" android:id="@id/tv_title" android:visibility="gone" android:layout_width="0.0dip" android:lines="1" android:layout_marginStart="8.0dip" android:layout_marginEnd="8.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toStartOf="@id/toolbar_frame" app:layout_constraintStart_toEndOf="@id/iv_cover_small" app:layout_constraintTop_toTopOf="parent" style="@style/style_regula_bigger_text" />
                        <FrameLayout android:id="@id/toolbar_frame" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginEnd="12.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toStartOf="@id/iv_share" app:layout_constraintTop_toTopOf="parent">
                            <com.tn.lib.view.ToolBarCheckInAnimationView android:gravity="center" android:id="@id/toolbarTvCheckIn" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" />
                            <TextView android:textSize="12.0sp" android:textColor="@color/brand" android:gravity="center" android:id="@id/toolbar_tv_edit" android:background="@drawable/libui_sub_join_normal" android:visibility="gone" android:layout_width="72.0dip" android:layout_height="26.0dip" android:text="@string/str_edit" style="@style/style_regular_text" />
                        </FrameLayout>
                    </androidx.constraintlayout.widget.ConstraintLayout>
                </androidx.appcompat.widget.Toolbar>
            </com.google.android.material.appbar.CollapsingToolbarLayout>
            <include android:id="@id/ll_tab_room" android:visibility="visible" android:layout_width="fill_parent" android:layout_height="wrap_content" layout="@layout/room_detail_layout_room_tabs" />
        </com.google.android.material.appbar.AppBarLayout>
        <androidx.viewpager2.widget.ViewPager2 android:id="@id/view_pager" android:tag="viewPager" android:background="@color/bg_02" android:visibility="visible" android:persistentDrawingCache="animation" android:layout_width="fill_parent" android:layout_height="fill_parent" app:layout_behavior="@string/appbar_scrolling_view_behavior" app:layout_scrollFlags="scroll" />
    </androidx.coordinatorlayout.widget.CoordinatorLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
