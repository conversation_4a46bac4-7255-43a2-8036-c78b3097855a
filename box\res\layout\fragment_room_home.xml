<?xml version="1.0" encoding="utf-8"?>
<com.transsion.baseui.widget.NestedSwipeRefreshLayout android:id="@id/swipe_refresh" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.coordinatorlayout.widget.CoordinatorLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="fill_parent">
        <com.google.android.material.appbar.AppBarLayout android:orientation="vertical" android:id="@id/appBar" android:layout_width="fill_parent" android:layout_height="wrap_content">
            <com.google.android.material.appbar.CollapsingToolbarLayout android:id="@id/toolbar_layout" android:layout_width="fill_parent" android:layout_height="wrap_content" app:contentScrim="@color/transparent" app:layout_scrollFlags="scroll|exitUntilCollapsed">
                <LinearLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="wrap_content">
                    <com.transsion.room.widget.MyRoomsView android:id="@id/v_your_rooms" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="wrap_content" />
                    <com.transsion.room.widget.RecommendRoomsView android:id="@id/v_recommend_rooms" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="wrap_content" />
                </LinearLayout>
            </com.google.android.material.appbar.CollapsingToolbarLayout>
            <net.lucode.hackware.magicindicator.MagicIndicator android:layout_gravity="bottom" android:id="@id/magic_indicator" android:background="@color/bg_01" android:layout_width="fill_parent" android:layout_height="38.0dip" />
        </com.google.android.material.appbar.AppBarLayout>
        <com.transsion.baseui.widget.NestedScrollableHost android:layout_width="fill_parent" android:layout_height="fill_parent" app:layout_behavior="@string/appbar_scrolling_view_behavior">
            <androidx.viewpager2.widget.ViewPager2 android:id="@id/view_pager" android:layout_width="fill_parent" android:layout_height="fill_parent" />
        </com.transsion.baseui.widget.NestedScrollableHost>
        <androidx.appcompat.widget.AppCompatImageView android:layout_gravity="end|bottom" android:id="@id/iv_publish" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginBottom="48.0dip" android:layout_marginEnd="12.0dip" app:srcCompat="@drawable/ic_room_publish" />
    </androidx.coordinatorlayout.widget.CoordinatorLayout>
</com.transsion.baseui.widget.NestedSwipeRefreshLayout>
