<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout android:paddingTop="12.0dip" android:paddingBottom="32.0dip" android:layout_width="fill_parent" android:layout_height="fill_parent" android:paddingStart="12.0dip" android:paddingEnd="38.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <ImageView android:id="@id/search_rank_pager_type" android:layout_width="64.0dip" android:layout_height="64.0dip" android:scaleType="fitXY" android:layout_alignParentTop="true" android:layout_alignParentStart="true" />
    <com.transsion.baseui.widget.GradientBorderView android:background="@drawable/bg_rank_list" android:paddingBottom="12.0dip" android:layout_width="fill_parent" android:layout_height="wrap_content" app:borderViewEndColor="@color/transparent" app:borderViewStartColor="@color/white_6" app:borderWidth="1.0dip" app:bottomLeftCornerRadius="6.0dip" app:bottomRightCornerRadius="6.0dip" app:gradientOrientation="horizontal" app:topLeftCornerRadius="6.0dip">
        <androidx.recyclerview.widget.RecyclerView android:id="@id/search_rank_pager_recycler" android:layout_width="fill_parent" android:layout_height="wrap_content" android:overScrollMode="never" />
    </com.transsion.baseui.widget.GradientBorderView>
</RelativeLayout>
