<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:background="@color/gray_dark_00" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <LinearLayout android:orientation="horizontal" android:id="@id/ll_top" android:paddingLeft="12.0dip" android:paddingRight="12.0dip" android:layout_width="fill_parent" android:layout_height="48.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent">
        <androidx.appcompat.widget.AppCompatImageView android:layout_gravity="center_vertical" android:id="@id/iv_cancel" android:layout_width="24.0dip" android:layout_height="24.0dip" android:src="@drawable/libui_ic_base_left" android:tint="@color/white" />
        <RelativeLayout android:layout_width="0.0dip" android:layout_height="fill_parent" android:layout_marginTop="8.0dip" android:layout_marginBottom="8.0dip" android:layout_weight="1.0" android:layout_marginStart="8.0dip">
            <EditText android:textSize="@dimen/text_size_14" android:textColor="@color/white" android:textColorHint="@color/white_60" android:ellipsize="end" android:gravity="center_vertical" android:autoLink="none" android:id="@id/comment_input_edit_text" android:background="@drawable/bg_search_bg_02" android:layout_width="fill_parent" android:layout_height="fill_parent" android:maxHeight="88.0dip" android:minHeight="35.0dip" android:hint="@string/search_hint_input" android:maxLines="1" android:singleLine="true" android:maxLength="200" android:inputType="text" android:imeOptions="actionSearch" android:textCursorDrawable="@drawable/cursor_color_p" android:paddingStart="32.0dip" android:paddingEnd="60.0dip" style="@style/style_regular_text" />
            <ImageView android:layout_width="16.0dip" android:layout_height="16.0dip" android:src="@drawable/ic_search_ic" android:layout_centerVertical="true" android:layout_marginStart="12.0dip" app:tint="@color/white_80" />
            <TextView android:textSize="14.0sp" android:textColor="@color/brand_new_50" android:gravity="center" android:id="@id/tv_search" android:layout_width="wrap_content" android:layout_height="fill_parent" android:text="@string/search" android:layout_marginStart="12.0dip" android:layout_marginEnd="12.0dip" android:layout_alignEnd="@id/comment_input_edit_text" />
            <ImageView android:id="@id/search_edit_clear" android:padding="5.0dip" android:visibility="invisible" android:layout_width="wrap_content" android:layout_height="wrap_content" android:src="@drawable/ic_clear" android:layout_centerVertical="true" android:contentDescription="@string/clear" android:layout_marginEnd="7.0dip" android:layout_alignEnd="@id/comment_input_edit_text" />
        </RelativeLayout>
        <com.transsion.search.speech.SpeechRecognizerView android:layout_gravity="center" android:id="@id/sRView" android:layout_width="24.0dip" android:layout_height="wrap_content" android:layout_marginStart="10.0dip" />
    </LinearLayout>
    <View android:id="@id/top_line" android:background="@color/border" android:layout_width="fill_parent" android:layout_height="0.5dip" android:layout_below="@id/ll_top" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/ll_top" />
    <FrameLayout android:id="@id/search_fragment_container" android:layout_width="fill_parent" android:layout_height="0.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/top_line" />
</androidx.constraintlayout.widget.ConstraintLayout>
