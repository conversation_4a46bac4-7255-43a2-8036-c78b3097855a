<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:id="@id/itemRoot" android:layout_width="fill_parent" android:layout_height="44.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/ivSearchIcon" android:layout_width="16.0dip" android:layout_height="16.0dip" android:src="@drawable/ic_search_suggest_l_n" android:layout_marginStart="16.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <TextView android:textSize="14.0sp" android:textColor="@color/text_01" android:ellipsize="end" android:gravity="start|center" android:id="@id/tvSearchSuggest" android:layout_width="0.0dip" android:layout_height="fill_parent" android:lines="1" android:textAlignment="viewStart" android:layout_marginStart="12.0dip" android:layout_marginEnd="16.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/ivSearchIcon" app:layout_constraintTop_toTopOf="parent" style="@style/style_medium_text" />
</androidx.constraintlayout.widget.ConstraintLayout>
