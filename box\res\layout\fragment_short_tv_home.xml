<?xml version="1.0" encoding="utf-8"?>
<FrameLayout android:orientation="vertical" android:background="@color/bg_02" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <com.transsion.baseui.widget.NestedScrollableHost android:layout_width="fill_parent" android:layout_height="fill_parent">
        <androidx.viewpager2.widget.ViewPager2 android:id="@id/view_pager" android:layout_width="fill_parent" android:layout_height="fill_parent" />
    </com.transsion.baseui.widget.NestedScrollableHost>
    <net.lucode.hackware.magicindicator.MagicIndicator android:id="@id/magic_indicator" android:layout_width="fill_parent" android:layout_height="32.0dip" android:layout_marginStart="4.0dip" />
</FrameLayout>
