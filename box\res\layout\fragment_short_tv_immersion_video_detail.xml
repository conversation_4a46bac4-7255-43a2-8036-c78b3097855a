<?xml version="1.0" encoding="utf-8"?>
<FrameLayout android:background="@color/black" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.transsion.baseui.widget.NestedScrollableHost android:layout_width="fill_parent" android:layout_height="fill_parent">
        <androidx.recyclerview.widget.RecyclerView android:id="@id/recycler_view" android:layout_width="fill_parent" android:layout_height="fill_parent" />
    </com.transsion.baseui.widget.NestedScrollableHost>
    <ProgressBar android:layout_gravity="center" android:id="@id/pb_loading" android:visibility="gone" android:layout_width="23.0dip" android:layout_height="23.0dip" android:indeterminateTint="@color/brand" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <androidx.constraintlayout.widget.ConstraintLayout android:id="@id/tool_bar" android:layout_width="fill_parent" android:layout_height="48.0dip">
        <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_back" android:layout_width="30.0dip" android:layout_height="30.0dip" android:src="@mipmap/icon_white_back" android:scaleType="center" android:layout_marginStart="16.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
        <com.tn.lib.widget.TnTextView android:textColor="@color/common_white" android:id="@id/tv_title" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/for_you" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" style="@style/style_title_text" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</FrameLayout>
