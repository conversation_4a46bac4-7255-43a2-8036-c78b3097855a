<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.google.android.material.imageview.ShapeableImageView android:id="@id/ivMovieBlurCover" android:visibility="visible" android:layout_width="fill_parent" android:layout_height="0.0dip" android:scaleType="centerCrop" app:layout_constraintDimensionRatio="h,360:203" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <com.google.android.material.imageview.ShapeableImageView android:id="@id/ivMovieCover" android:layout_width="0.0dip" android:layout_height="0.0dip" android:scaleType="centerCrop" app:layout_constraintBottom_toBottomOf="@id/ivMovieBlurCover" app:layout_constraintEnd_toEndOf="@id/ivMovieBlurCover" app:layout_constraintStart_toStartOf="@id/ivMovieBlurCover" app:layout_constraintTop_toTopOf="@id/ivMovieBlurCover" />
</androidx.constraintlayout.widget.ConstraintLayout>
