<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.constraintlayout.widget.Guideline android:orientation="horizontal" android:id="@id/sub_operation_main_guide" android:layout_width="wrap_content" android:layout_height="wrap_content" app:layout_constraintGuide_begin="0.0dip" />
    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout android:id="@id/sub_operation_main_refresh" android:layout_width="fill_parent" android:layout_height="0.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintTop_toBottomOf="@id/sub_operation_main_guide">
        <androidx.recyclerview.widget.RecyclerView android:id="@id/sub_operation_main_recycler" android:paddingBottom="@dimen/tab_bottom_show_height" android:clipToPadding="false" android:layout_width="fill_parent" android:layout_height="fill_parent" />
    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>
    <View android:id="@id/sub_operation_header_bg" android:background="@drawable/home_title_gradient_bg" android:layout_width="fill_parent" android:layout_height="80.0dip" app:layout_constraintTop_toTopOf="parent" />
    <include android:id="@id/sub_operation_loading" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="0.0dip" app:layout_constraintTop_toBottomOf="@id/sub_operation_header_bg" layout="@layout/layout_post_list_default" />
</androidx.constraintlayout.widget.ConstraintLayout>
