<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <LinearLayout android:id="@id/ll_title" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginTop="8.0dip" android:layout_marginStart="12.0dip" android:layout_marginEnd="41.0dip" app:layout_constraintEnd_toStartOf="@id/ivCollection" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent">
        <androidx.appcompat.widget.AppCompatTextView android:textSize="18.0sp" android:textColor="@color/text_01" android:ellipsize="end" android:gravity="start|bottom" android:id="@id/tvMovieTitle" android:layout_width="wrap_content" android:layout_height="wrap_content" android:maxLines="1" android:textAlignment="viewStart" app:drawableEndCompat="@mipmap/movie_detail_ic_arrow_right_white" app:drawableTint="@color/text_01" app:layout_constraintEnd_toStartOf="@id/ivCollection" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" style="@style/style_import_text" />
    </LinearLayout>
    <View android:id="@id/v_line" android:background="@color/line_01" android:layout_width="1.0dip" android:layout_height="24.0dip" app:layout_constraintBottom_toBottomOf="@id/ll_title" app:layout_constraintEnd_toStartOf="@id/ivCollection" app:layout_constraintStart_toEndOf="@id/ll_title" app:layout_constraintTop_toTopOf="@id/ll_title" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/ivCollection" android:layout_width="24.0dip" android:layout_height="24.0dip" android:src="@drawable/music_collection_0" android:layout_marginEnd="12.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/ll_title" app:layout_constraintTop_toTopOf="@id/ll_title" />
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/text_02" android:ellipsize="end" android:gravity="start" android:id="@id/tvTag" android:layout_width="0.0dip" android:layout_marginTop="8.0dip" android:maxLines="1" android:drawablePadding="@dimen/dp_4" android:lineSpacingExtra="2.0dip" android:drawableStart="@drawable/ic_tag_edu" android:textAlignment="viewStart" android:layout_marginEnd="4.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="@id/ll_title" app:layout_constraintTop_toBottomOf="@id/ll_title" style="@style/style_regular_text" />
    <View android:id="@id/v_detail_hot_zone" android:layout_width="0.0dip" android:layout_height="0.0dip" app:layout_constraintBottom_toBottomOf="@id/tvTag" app:layout_constraintEnd_toEndOf="@id/ll_title" app:layout_constraintStart_toStartOf="@id/ll_title" app:layout_constraintTop_toTopOf="@id/ll_title" />
    <FrameLayout android:id="@id/extension_container" android:layout_width="fill_parent" android:layout_height="32.0dip" android:layout_marginTop="8.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tvTag" />
</androidx.constraintlayout.widget.ConstraintLayout>
