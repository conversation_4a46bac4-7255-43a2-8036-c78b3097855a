<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.google.android.material.imageview.ShapeableImageView android:id="@id/ivMovieCover" android:layout_width="50.0dip" android:layout_height="70.0dip" android:layout_marginTop="16.0dip" android:scaleType="centerCrop" android:layout_marginStart="16.0dip" app:layout_constraintEnd_toStartOf="@id/tvMovieTitle" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:shapeAppearanceOverlay="@style/roundStyle_4" />
    <View android:id="@id/ivMovieCoverMask" android:background="@drawable/bg_movie_cover_mask" android:layout_width="50.0dip" android:layout_height="20.0dip" app:layout_constraintBottom_toBottomOf="@id/ivMovieCover" app:layout_constraintEnd_toEndOf="@id/ivMovieCover" app:layout_constraintStart_toStartOf="@id/ivMovieCover" />
    <View android:id="@id/ivCoverZoom" android:background="@drawable/ic_preview" android:layout_width="@dimen/dimens_12" android:layout_height="@dimen/dimens_12" android:layout_marginBottom="@dimen/dp_4" android:layout_marginEnd="@dimen/dp_4" app:layout_constraintBottom_toBottomOf="@id/ivMovieCover" app:layout_constraintEnd_toEndOf="@id/ivMovieCover" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="18.0sp" android:textColor="@color/text_01" android:ellipsize="end" android:gravity="start|bottom" android:id="@id/tvMovieTitle" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginTop="16.0dip" android:maxLines="2" android:textAlignment="viewStart" android:layout_marginStart="8.0dip" android:layout_marginEnd="16.0dip" app:layout_constraintBottom_toTopOf="@id/ll_score" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/ivMovieCover" app:layout_constraintTop_toTopOf="parent" app:layout_constraintVertical_chainStyle="packed" style="@style/style_import_text" />
    <LinearLayout android:gravity="center" android:orientation="horizontal" android:id="@id/ll_score" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="4.0dip" android:layout_marginBottom="3.0dip" app:layout_constraintBottom_toBottomOf="@id/ivMovieCover" app:layout_constraintStart_toStartOf="@id/tvMovieTitle" app:layout_constraintTop_toBottomOf="@id/tvMovieTitle">
        <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_score" android:layout_width="12.0dip" android:layout_height="12.0dip" android:src="@mipmap/home_ic_score" android:scaleType="centerInside" android:layout_marginEnd="2.0dip" />
        <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/yellow_dark_70" android:ellipsize="end" android:id="@id/tv_score" android:layout_width="wrap_content" android:layout_height="wrap_content" android:maxLines="1" android:includeFontPadding="false" android:layout_marginStart="2.0dip" />
        <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/text_02" android:ellipsize="end" android:id="@id/tv_time" android:layout_width="wrap_content" android:layout_height="wrap_content" android:maxLines="1" android:includeFontPadding="false" android:layout_marginStart="1.0dip" android:layout_marginEnd="16.0dip" />
    </LinearLayout>
    <androidx.constraintlayout.widget.Barrier android:id="@id/barrierMovieCoverBottom" android:layout_width="wrap_content" android:layout_height="wrap_content" app:barrierDirection="bottom" app:constraint_referenced_ids="ivMovieCover,tvMovieTitle" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/ivMovieContent" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="1.0dip" android:src="@drawable/ic_tag_movie" android:tint="@color/gray_40" android:layout_marginStart="16.0dip" android:layout_marginEnd="4.0dip" app:layout_constraintEnd_toStartOf="@id/tvMovieContent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="@id/tvMovieContent" style="@style/style_regular_text" />
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/text_02" android:ellipsize="end" android:gravity="start" android:id="@id/tvMovieContent" android:layout_width="0.0dip" android:layout_marginTop="8.0dip" android:maxLines="2" android:includeFontPadding="false" android:lineSpacingExtra="2.0dip" android:textAlignment="viewStart" android:layout_marginEnd="16.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/ivMovieContent" app:layout_constraintTop_toBottomOf="@id/barrierMovieCoverBottom" style="@style/style_regular_text" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_subtitle_tag" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="6.0dip" android:src="@mipmap/movie_detail_ic_subtitle_tag" android:tint="@color/gray_40" android:layout_marginEnd="4.0dip" app:layout_constraintHorizontal_bias="0.0" app:layout_constraintHorizontal_chainStyle="packed" app:layout_constraintStart_toStartOf="@id/ivMovieContent" app:layout_constraintTop_toBottomOf="@id/tvMovieContent" style="@style/style_regular_text" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/text_01" android:id="@id/tv_subtitle_more" android:visibility="gone" android:layout_width="0.0dip" android:layout_height="14.0dip" android:layout_marginTop="4.0dip" android:minWidth="28.0dip" android:text="@string/more" android:layout_marginEnd="16.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toBottomOf="@id/tvMovieContent" style="@style/style_regular_text" />
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/text_02" android:ellipsize="end" android:gravity="start" android:id="@id/tv_subtitle_tag" android:layout_width="0.0dip" android:layout_marginTop="4.0dip" android:maxLines="1" android:lineSpacingExtra="2.0dip" android:textAlignment="viewStart" android:layout_marginEnd="4.0dip" app:layout_constraintEnd_toStartOf="@id/tv_subtitle_more" app:layout_constraintStart_toStartOf="@id/tvMovieContent" app:layout_constraintTop_toBottomOf="@id/tvMovieContent" style="@style/style_regular_text" />
</androidx.constraintlayout.widget.ConstraintLayout>
