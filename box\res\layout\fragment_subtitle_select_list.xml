<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.appcompat.widget.AppCompatTextView android:textSize="18.0sp" android:textColor="@color/white" android:gravity="center" android:id="@id/tvTitle" android:layout_width="wrap_content" android:layout_height="52.0dip" android:text="@string/subtitle" android:layout_marginStart="16.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" style="@style/style_medium_text" />
    <com.tn.lib.view.SwitchButton android:id="@id/switchBtn" android:clickable="false" android:layout_width="40.0dip" android:layout_height="24.0dip" android:layout_marginEnd="16.0dip" app:layout_constraintBottom_toBottomOf="@id/tvTitle" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="@id/tvTitle" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="18.0sp" android:textColor="@color/white" android:gravity="center" android:id="@id/tvBilingual" android:layout_width="wrap_content" android:layout_height="40.0dip" android:text="@string/bilingual" android:layout_marginStart="16.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tvTitle" style="@style/style_medium_text" />
    <com.tn.lib.view.SwitchButton android:id="@id/switchBilingual" android:clickable="false" android:layout_width="40.0dip" android:layout_height="24.0dip" android:layout_marginEnd="16.0dip" app:layout_constraintBottom_toBottomOf="@id/tvBilingual" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="@id/tvBilingual" />
    <androidx.recyclerview.widget.RecyclerView android:id="@id/recycler_view" android:layout_width="0.0dip" android:layout_height="0.0dip" app:layout_constraintBottom_toTopOf="@id/viewOptionsBg" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tvBilingual" />
    <androidx.appcompat.widget.LinearLayoutCompat android:orientation="horizontal" android:id="@id/viewOptionsBg" android:background="@drawable/subtitle_search_0_0_8_8_bg" android:layout_width="0.0dip" android:layout_height="44.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/recycler_view">
        <androidx.appcompat.widget.LinearLayoutCompat android:layout_gravity="center_vertical" android:orientation="horizontal" android:id="@id/llOptions" android:layout_width="0.0dip" android:layout_height="fill_parent" android:layout_weight="1.0">
            <Space android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_weight="1.0" />
            <androidx.appcompat.widget.AppCompatImageView android:layout_gravity="center_vertical" android:id="@id/ivOptions" android:layout_width="14.0dip" android:layout_height="14.0dip" android:src="@mipmap/ic_subtitle_options" />
            <Space android:layout_width="6.0dip" android:layout_height="wrap_content" />
            <androidx.appcompat.widget.AppCompatTextView android:textSize="14.0sp" android:textColor="@color/white" android:ellipsize="end" android:gravity="center_vertical" android:id="@id/tvOptions" android:layout_width="wrap_content" android:layout_height="44.0dip" android:maxWidth="108.0dip" android:text="@string/subtitle_style" android:maxLines="1" android:includeFontPadding="false" style="@style/style_medium_text" />
            <Space android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_weight="1.0" />
        </androidx.appcompat.widget.LinearLayoutCompat>
        <View android:layout_gravity="center_vertical" android:id="@id/viewLine" android:background="@color/white_10" android:layout_width="1.0dip" android:layout_height="20.0dip" />
        <androidx.appcompat.widget.LinearLayoutCompat android:layout_gravity="center_vertical" android:orientation="horizontal" android:id="@id/llSync" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_weight="1.0" app:layout_constraintBottom_toBottomOf="@id/viewOptionsBg" app:layout_constraintEnd_toEndOf="@id/viewOptionsBg" app:layout_constraintStart_toEndOf="@id/viewLine">
            <Space android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_weight="1.0" />
            <androidx.appcompat.widget.AppCompatImageView android:layout_gravity="center_vertical" android:id="@id/ivSync" android:layout_width="14.0dip" android:layout_height="14.0dip" android:src="@mipmap/ic_subtitle_sync" />
            <Space android:layout_width="6.0dip" android:layout_height="wrap_content" />
            <androidx.appcompat.widget.AppCompatTextView android:textSize="14.0sp" android:textColor="@color/white" android:ellipsize="end" android:gravity="center_vertical" android:id="@id/tvSync" android:layout_width="wrap_content" android:layout_height="44.0dip" android:maxWidth="108.0dip" android:text="@string/subtitle_delay" android:maxLines="1" android:includeFontPadding="false" style="@style/style_medium_text" />
            <Space android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_weight="1.0" />
        </androidx.appcompat.widget.LinearLayoutCompat>
    </androidx.appcompat.widget.LinearLayoutCompat>
</androidx.constraintlayout.widget.ConstraintLayout>
