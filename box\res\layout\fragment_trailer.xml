<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <TextureView android:id="@id/textureView" android:visibility="visible" android:layout_width="fill_parent" android:layout_height="fill_parent" android:scaleType="centerCrop" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <com.google.android.material.imageview.ShapeableImageView android:id="@id/ivMovieBlurCover" android:visibility="visible" android:layout_width="fill_parent" android:layout_height="fill_parent" android:scaleType="centerCrop" />
    <com.google.android.material.imageview.ShapeableImageView android:id="@id/ivMovieCover" android:layout_width="fill_parent" android:layout_height="fill_parent" android:scaleType="centerCrop" />
    <View android:id="@id/v_tap" android:layout_width="fill_parent" android:layout_height="fill_parent" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_play" android:layout_width="wrap_content" android:layout_height="wrap_content" android:src="@mipmap/icon_trailer_play" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <ProgressBar android:id="@id/progress" android:layout_width="20.0dip" android:layout_height="20.0dip" android:indeterminateTint="@color/main" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toStartOf="@id/tv_loading" app:layout_constraintHorizontal_chainStyle="packed" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <TextView android:textSize="14.0sp" android:textColor="@color/white" android:id="@id/tv_loading" android:layout_width="wrap_content" android:layout_height="wrap_content" android:minWidth="67.0dip" android:text="@string/post_loading" android:shadowColor="@color/black_50" android:shadowRadius="3.0" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/progress" app:layout_constraintTop_toTopOf="parent" style="@style/style_medium_text" />
    <androidx.constraintlayout.widget.Group android:id="@id/group_loading" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" app:constraint_referenced_ids="progress,tv_loading" />
    <androidx.appcompat.widget.AppCompatImageView android:layout_gravity="center_vertical" android:id="@id/vd_pause" android:layout_width="32.0dip" android:layout_height="32.0dip" android:layout_marginBottom="8.0dip" android:src="@drawable/post_icon_pause" android:scaleType="center" android:layout_marginStart="12.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" />
    <com.tn.lib.view.SecondariesSeekBar android:layout_gravity="center_vertical" android:id="@id/vd_seekbar" android:background="@color/transparent" android:layout_width="0.0dip" android:layout_height="24.0dip" android:maxHeight="2.0dip" android:minHeight="2.0dip" android:layout_weight="1.0" android:layout_marginStart="8.0dip" android:layout_marginEnd="12.0dip" app:layout_constraintBottom_toBottomOf="@id/vd_pause" app:layout_constraintEnd_toStartOf="@id/vd_video_time" app:layout_constraintStart_toEndOf="@id/vd_pause" app:layout_constraintTop_toTopOf="@id/vd_pause" app:ssb_bar_center_color="@color/main_gradient_center" app:ssb_bar_end_color="@color/main_gradient_end" app:ssb_bar_start_color="@color/main_gradient_start" app:ssb_bg_color="@color/white_30" app:ssb_secondaries_color="@color/white" app:ssb_thumb_color="@color/white" app:ssb_thumb_size="8.0dip" />
    <TextView android:textSize="11.0sp" android:textColor="@color/white" android:gravity="end" android:layout_gravity="end|center" android:id="@id/vd_video_time" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="00:00/00:00" android:shadowColor="@color/cl31_50_p" android:shadowDy="2.0" android:shadowRadius="2.0" android:layout_marginEnd="12.0dip" app:layout_constraintBottom_toBottomOf="@id/vd_pause" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="@id/vd_pause" />
    <androidx.constraintlayout.widget.Group android:id="@id/group_progress" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" app:constraint_referenced_ids="vd_pause,vd_seekbar,vd_video_time" />
    <ViewStub android:id="@id/vs_load_failed" android:layout="@layout/layout_local_video_load_failed" android:layout_width="fill_parent" android:layout_height="fill_parent" />
</androidx.constraintlayout.widget.ConstraintLayout>
