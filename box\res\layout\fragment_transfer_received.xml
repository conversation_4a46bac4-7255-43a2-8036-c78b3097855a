<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout android:padding="16.0dip" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.recyclerview.widget.RecyclerView android:id="@id/transfer_receive_file_list" android:layout_width="fill_parent" android:layout_height="fill_parent" android:overScrollMode="never" />
    <LinearLayout android:gravity="center" android:orientation="vertical" android:id="@id/transfer_sent_file_list_empty" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_centerInParent="true">
        <ImageView android:layout_width="wrap_content" android:layout_height="wrap_content" android:src="@mipmap/transfer_list_empty" android:importantForAccessibility="no" />
        <TextView android:textSize="12.0sp" android:textColor="@color/gray_light_40" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/transfer_file_empty_tips" />
    </LinearLayout>
</RelativeLayout>
