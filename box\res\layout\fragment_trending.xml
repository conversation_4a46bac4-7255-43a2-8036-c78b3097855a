<?xml version="1.0" encoding="utf-8"?>
<FrameLayout android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout android:id="@id/swipe_refresh" android:layout_width="fill_parent" android:layout_height="fill_parent">
        <androidx.recyclerview.widget.RecyclerView android:id="@id/recycleView" android:paddingBottom="@dimen/tab_bottom_show_height" android:clipToPadding="false" android:layout_width="fill_parent" android:layout_height="fill_parent" />
    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>
    <include android:id="@id/loading_bg" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="fill_parent" layout="@layout/layout_post_list_default" />
    <View android:id="@id/trending_header_bg" android:background="@drawable/home_title_gradient_bg" android:layout_width="fill_parent" android:layout_height="80.0dip" />
    <FrameLayout android:layout_gravity="end|bottom" android:id="@id/flWidget" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginBottom="140.0dip" />
</FrameLayout>
