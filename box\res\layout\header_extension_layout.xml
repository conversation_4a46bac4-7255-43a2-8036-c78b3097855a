<?xml version="1.0" encoding="utf-8"?>
<HorizontalScrollView android:scrollbars="none" android:layout_width="fill_parent" android:layout_height="wrap_content" android:fillViewport="true"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <LinearLayout android:orientation="horizontal" android:paddingLeft="12.0dip" android:paddingRight="12.0dip" android:layout_width="wrap_content" android:layout_height="32.0dip">
        <androidx.constraintlayout.widget.ConstraintLayout android:id="@id/tv_music_avatar" android:background="@drawable/bg_info_icon" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="fill_parent" android:paddingStart="4.0dip" android:paddingEnd="12.0dip" android:layout_marginEnd="8.0dip">
            <com.google.android.material.imageview.ShapeableImageView android:id="@id/ivAvatar" android:layout_width="24.0dip" android:layout_height="24.0dip" android:layout_marginTop="4.0dip" android:layout_marginBottom="4.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:shapeAppearanceOverlay="@style/circle_style" />
            <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/text_01" android:ellipsize="end" android:id="@id/tvDes" android:layout_width="wrap_content" android:layout_height="wrap_content" android:maxLines="1" android:layout_marginStart="8.0dip" app:layout_constraintBottom_toBottomOf="@id/ivAvatar" app:layout_constraintStart_toEndOf="@id/ivAvatar" app:layout_constraintTop_toTopOf="@id/ivAvatar" />
        </androidx.constraintlayout.widget.ConstraintLayout>
        <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/text_01" android:gravity="center" android:id="@id/tv_remind" android:background="@drawable/bg_info_icon" android:paddingLeft="12.0dip" android:paddingRight="12.0dip" android:layout_width="wrap_content" android:layout_height="fill_parent" android:text="@string/movie_detail_add_to_list" android:drawablePadding="4.0dip" android:drawableStart="@drawable/selector_movie_detail_remind" android:layout_marginEnd="8.0dip" />
        <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/text_01" android:gravity="center_vertical" android:id="@id/tv_add" android:background="@drawable/bg_info_icon" android:paddingLeft="12.0dip" android:paddingRight="12.0dip" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="fill_parent" android:text="@string/course_list" android:drawablePadding="4.0dip" android:drawableStart="@drawable/selector_movie_detail_course" android:layout_marginEnd="8.0dip" app:drawableTint="@color/text_01" />
        <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/text_01" android:gravity="center" android:id="@id/tv_share" android:background="@drawable/bg_info_icon" android:paddingLeft="12.0dip" android:paddingRight="12.0dip" android:layout_width="wrap_content" android:layout_height="fill_parent" android:text="@string/want_to_see_share" android:drawablePadding="4.0dip" android:drawableStart="@mipmap/movie_detail_ic_info_share_white" android:layout_marginEnd="8.0dip" />
        <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/text_01" android:gravity="center" android:id="@id/tv_download" android:background="@drawable/bg_info_icon" android:paddingLeft="12.0dip" android:paddingRight="12.0dip" android:layout_width="wrap_content" android:layout_height="fill_parent" android:text="@string/download_movie" android:drawablePadding="4.0dip" android:drawableStart="@mipmap/movie_detail_ic_info_download" android:layout_marginEnd="8.0dip" />
        <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/text_01" android:gravity="center" android:id="@id/tv_download_page" android:background="@drawable/bg_info_icon" android:paddingLeft="12.0dip" android:paddingRight="12.0dip" android:layout_width="wrap_content" android:layout_height="fill_parent" android:text="@string/view_downloads" android:drawablePadding="4.0dip" android:drawableStart="@mipmap/movie_detail_ic_info_view_download" />
    </LinearLayout>
</HorizontalScrollView>
