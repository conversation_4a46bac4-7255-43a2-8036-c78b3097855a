<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.google.android.material.imageview.ShapeableImageView android:id="@id/iv_cover" android:layout_width="fill_parent" android:layout_height="149.0dip" android:scaleType="centerCrop" app:layout_constraintTop_toTopOf="parent" app:shapeAppearanceOverlay="@style/roundStyle_4" />
    <com.tn.lib.view.CornerTextView android:id="@id/tv_tips" android:layout_width="wrap_content" android:layout_height="wrap_content" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <View android:id="@id/view" android:background="@drawable/home_mask_movie" android:layout_width="fill_parent" android:layout_height="48.0dip" app:layout_constraintBottom_toBottomOf="@id/iv_cover" />
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/color_score" android:id="@id/tv_score" android:layout_marginBottom="8.0dip" android:layout_marginEnd="8.0dip" app:layout_constraintBottom_toBottomOf="@id/iv_cover" app:layout_constraintEnd_toEndOf="parent" style="@style/style_medium_text" />
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/text_01" android:ellipsize="end" android:id="@id/tv_title" android:layout_marginTop="6.0dip" android:maxLines="2" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/iv_cover" style="@style/style_medium_text" />
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/cl34" android:ellipsize="end" android:id="@id/tv_tabs" android:visibility="gone" android:layout_marginTop="2.0dip" android:maxLines="1" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tv_title" style="@style/style_regular_text" />
    <androidx.constraintlayout.widget.Group android:id="@id/group" android:layout_width="wrap_content" android:layout_height="wrap_content" app:constraint_referenced_ids="iv_cover,view,tv_score,tv_title,tv_tabs" />
    <FrameLayout android:id="@id/adContainer" android:background="@android:color/transparent" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="wrap_content" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>
