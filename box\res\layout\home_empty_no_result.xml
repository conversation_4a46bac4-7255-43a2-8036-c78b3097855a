<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <TextView android:textColor="@color/text_01" android:id="@id/tv_no_result" android:layout_marginBottom="20.0dip" android:text="@string/no_filter_result" app:layout_constraintBottom_toTopOf="@id/tv_reset" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" style="@style/style_regular_text" />
    <TextView android:textSize="@dimen/sp_14" android:textColor="@color/text_01" android:gravity="center" android:id="@id/tv_reset" android:background="@drawable/btn_gray" android:layout_width="88.0dip" android:layout_height="36.0dip" android:text="@string/home_reset_text" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>
