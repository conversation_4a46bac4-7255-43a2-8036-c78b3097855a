<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.appcompat.widget.LinearLayoutCompat android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="fill_parent">
        <include layout="@layout/home_empty_view_loading_item_two_column" />
        <include layout="@layout/home_empty_view_loading_item_two_column" />
        <include layout="@layout/home_empty_view_loading_item_two_column" />
        <include layout="@layout/home_empty_view_loading_item_two_column" />
    </androidx.appcompat.widget.LinearLayoutCompat>
    <ProgressBar android:layout_width="23.0dip" android:layout_height="23.0dip" android:layout_marginTop="250.0dip" android:indeterminateTint="@color/brand" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>
