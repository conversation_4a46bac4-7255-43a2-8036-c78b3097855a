<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <View android:id="@id/viewBg" android:background="@drawable/ps_link_8_border_bg" android:layout_width="106.0dip" android:layout_height="214.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <com.google.android.material.imageview.ShapeableImageView android:id="@id/ivIcon" android:layout_width="82.0dip" android:layout_height="82.0dip" android:layout_marginTop="12.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="@id/viewBg" app:shapeAppearance="@style/roundStyle_12" />
    <TextView android:textSize="16.0sp" android:textColor="@color/text_01" android:ellipsize="end" android:gravity="center_horizontal" android:id="@id/tvName" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginTop="4.0dip" android:lines="2" app:layout_constraintEnd_toEndOf="@id/ivIcon" app:layout_constraintStart_toStartOf="@id/ivIcon" app:layout_constraintTop_toBottomOf="@id/ivIcon" style="@style/robot_medium" />
    <androidx.appcompat.widget.LinearLayoutCompat android:orientation="horizontal" android:id="@id/ll" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginTop="12.0dip" app:layout_constraintEnd_toEndOf="@id/ivIcon" app:layout_constraintStart_toStartOf="@id/ivIcon" app:layout_constraintTop_toBottomOf="@id/tvName">
        <androidx.appcompat.widget.AppCompatImageView android:layout_gravity="center_vertical" android:id="@id/ivStar" android:layout_width="wrap_content" android:layout_height="wrap_content" android:src="@drawable/ps_link_star" />
        <TextView android:textSize="12.0sp" android:textColor="@color/text_03" android:ellipsize="end" android:layout_gravity="center_vertical" android:id="@id/tvStarNum" android:layout_width="wrap_content" android:layout_height="wrap_content" android:lines="1" android:layout_marginStart="2.0dip" />
        <TextView android:layout_gravity="center_vertical" android:id="@id/tvLine" android:background="@color/line_01" android:layout_width="1.0dip" android:layout_height="8.0dip" android:layout_marginStart="4.0dip" />
        <TextView android:textSize="12.0sp" android:textColor="@color/text_03" android:ellipsize="end" android:layout_gravity="center_vertical" android:id="@id/tvSizeNum" android:layout_width="wrap_content" android:layout_height="wrap_content" android:lines="1" android:layout_marginStart="4.0dip" />
    </androidx.appcompat.widget.LinearLayoutCompat>
    <com.transsion.commercialization.pslink.PsLinkDownLoadButton android:id="@id/tvButton" android:layout_width="82.0dip" android:layout_height="32.0dip" android:layout_marginTop="4.0dip" android:layout_marginBottom="12.0dip" app:border_radius="8.0dip" app:layout_constraintBottom_toBottomOf="@id/viewBg" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:progress_textSize="14.0sp" />
</androidx.constraintlayout.widget.ConstraintLayout>
