<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginLeft="12.0dip" android:layout_marginRight="12.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.google.android.material.imageview.ShapeableImageView android:id="@id/ivIcon" android:layout_width="64.0dip" android:layout_height="64.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:shapeAppearance="@style/roundStyle_12" />
    <com.tn.lib.widget.TnTextView android:textSize="16.0sp" android:textColor="@color/text_01" android:ellipsize="end" android:id="@id/tvName" android:layout_width="0.0dip" android:layout_height="wrap_content" android:lines="1" android:textAlignment="viewStart" android:layout_marginStart="8.0dip" android:layout_marginEnd="12.0dip" app:layout_constraintEnd_toStartOf="@id/tvButton" app:layout_constraintStart_toEndOf="@id/ivIcon" app:layout_constraintTop_toTopOf="@id/ivIcon" style="@style/robot_medium" />
    <androidx.appcompat.widget.LinearLayoutCompat android:layout_width="0.0dip" android:layout_height="wrap_content" app:layout_constraintBottom_toTopOf="@id/tvDescription" app:layout_constraintEnd_toStartOf="@id/tvButton" app:layout_constraintStart_toStartOf="@id/tvName" app:layout_constraintTop_toBottomOf="@id/tvName">
        <androidx.appcompat.widget.AppCompatImageView android:layout_gravity="center_vertical" android:id="@id/ivStar" android:layout_width="wrap_content" android:layout_height="wrap_content" android:src="@drawable/ps_link_star" android:layout_marginEnd="2.0dip" />
        <com.tn.lib.widget.TnTextView android:textSize="12.0sp" android:textColor="@color/text_03" android:ellipsize="end" android:gravity="start|center" android:layout_gravity="center_vertical" android:id="@id/tvStarNum" android:layout_width="wrap_content" android:layout_height="wrap_content" android:lines="1" android:textAlignment="viewStart" />
        <com.tn.lib.widget.TnTextView android:gravity="start|center" android:layout_gravity="center_vertical" android:id="@id/tvLine" android:background="@color/line_01" android:layout_width="1.0dip" android:layout_height="8.0dip" android:textAlignment="viewStart" android:layout_marginStart="4.0dip" />
        <com.tn.lib.widget.TnTextView android:textSize="12.0sp" android:textColor="@color/text_03" android:ellipsize="end" android:gravity="start|center" android:layout_gravity="center_vertical" android:id="@id/tvSizeNum" android:layout_width="wrap_content" android:layout_height="wrap_content" android:lines="1" android:textAlignment="viewStart" android:layout_marginStart="4.0dip" />
    </androidx.appcompat.widget.LinearLayoutCompat>
    <com.tn.lib.widget.TnTextView android:ellipsize="end" android:id="@id/tvDescription" android:layout_width="0.0dip" android:layout_height="wrap_content" android:lines="1" android:textAlignment="viewStart" android:layout_marginEnd="12.0dip" app:layout_constraintBottom_toBottomOf="@id/ivIcon" app:layout_constraintEnd_toStartOf="@id/tvButton" app:layout_constraintStart_toStartOf="@id/tvName" />
    <com.transsion.commercialization.pslink.PsLinkDownLoadButton android:id="@id/tvButton" android:layout_width="82.0dip" android:layout_height="32.0dip" app:border_radius="8.0dip" app:border_width="0.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent" app:progress_textSize="14.0sp" style="@style/robot_medium" />
</androidx.constraintlayout.widget.ConstraintLayout>
