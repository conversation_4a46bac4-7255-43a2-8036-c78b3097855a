<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView android:layout_width="wrap_content" android:layout_height="wrap_content" app:cardCornerRadius="6.0dip" app:cardElevation="0.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.constraintlayout.widget.ConstraintLayout android:layout_width="wrap_content" android:layout_height="wrap_content">
        <androidx.appcompat.widget.AppCompatImageView android:id="@id/ivBgBurl" android:layout_width="160.0dip" android:layout_height="62.0dip" android:scaleType="centerCrop" android:alpha="0.16" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:shapeAppearanceOverlay="@style/corner_style_6" />
        <com.google.android.material.imageview.ShapeableImageView android:id="@id/ivAppIcon" android:layout_width="46.0dip" android:layout_height="46.0dip" android:layout_marginStart="8.0dip" app:layout_constraintBottom_toBottomOf="@id/ivBgBurl" app:layout_constraintStart_toStartOf="@id/ivBgBurl" app:layout_constraintTop_toTopOf="@id/ivBgBurl" app:shapeAppearanceOverlay="@style/corner_style_8" />
        <TextView android:textSize="12.0sp" android:textColor="@color/white_80" android:ellipsize="end" android:id="@id/tvAppName" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginLeft="8.0dip" android:layout_marginRight="8.0dip" android:maxLines="1" app:layout_constraintEnd_toEndOf="@id/ivBgBurl" app:layout_constraintStart_toEndOf="@id/ivAppIcon" app:layout_constraintTop_toTopOf="@id/ivAppIcon" style="@style/style_medium_text" />
        <androidx.appcompat.widget.AppCompatImageView android:layout_gravity="center_vertical" android:id="@id/ivStar" android:layout_width="12.0dip" android:layout_height="12.0dip" android:src="@drawable/app_download_star" app:layout_constraintBottom_toBottomOf="@id/tvInstall" app:layout_constraintStart_toStartOf="@id/tvAppName" app:layout_constraintTop_toTopOf="@id/tvInstall" />
        <TextView android:textSize="12.0sp" android:textColor="@color/yellow_dark_60" android:gravity="start" android:id="@id/tvStarNum" android:layout_width="0.0dip" android:layout_height="wrap_content" android:maxLines="1" android:layout_marginStart="2.0dip" app:layout_constraintBottom_toBottomOf="@id/tvInstall" app:layout_constraintEnd_toStartOf="@id/tvInstall" app:layout_constraintStart_toEndOf="@id/ivStar" app:layout_constraintTop_toTopOf="@id/tvInstall" />
        <TextView android:textSize="12.0sp" android:textColor="@color/gray_dark_00" android:gravity="center" android:id="@id/tvInstall" android:background="@drawable/bg_gradient_brand_4" android:paddingLeft="8.0dip" android:paddingRight="8.0dip" android:layout_width="wrap_content" android:layout_height="20.0dip" android:text="@string/download_apps_to_be_intalled_txt" android:layout_marginEnd="8.0dip" app:layout_constraintBottom_toBottomOf="@id/ivAppIcon" app:layout_constraintEnd_toEndOf="parent" style="@style/style_import_text" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.cardview.widget.CardView>
