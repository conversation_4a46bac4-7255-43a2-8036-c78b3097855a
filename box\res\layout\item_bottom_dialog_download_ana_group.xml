<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="60.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_check" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginStart="16.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:srcCompat="@drawable/selector_download_group_check" />
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/text_01" android:ellipsize="end" android:id="@id/tv_title" android:layout_marginTop="2.0dip" android:maxLines="1" android:layout_marginStart="8.0dip" app:layout_constrainedWidth="true" app:layout_constraintBottom_toTopOf="@id/tv_size" app:layout_constraintEnd_toStartOf="@id/tv_ep" app:layout_constraintHorizontal_bias="0.0" app:layout_constraintHorizontal_chainStyle="packed" app:layout_constraintStart_toEndOf="@id/iv_check" app:layout_constraintTop_toTopOf="parent" app:layout_constraintVertical_chainStyle="packed" app:layout_goneMarginEnd="16.0dip" style="@style/style_medium_text" />
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/text_01" android:ellipsize="end" android:id="@id/tv_ep" android:maxLines="1" android:layout_marginStart="8.0dip" android:layout_marginEnd="16.0dip" app:layout_constraintBottom_toBottomOf="@id/tv_title" app:layout_constraintEnd_toStartOf="@id/iv_premium" app:layout_constraintHorizontal_bias="0.0" app:layout_constraintHorizontal_chainStyle="packed" app:layout_constraintStart_toEndOf="@id/tv_title" app:layout_constraintTop_toTopOf="@id/tv_title" style="@style/style_medium_text" />
    <com.transsnet.downloader.widget.DownloadPremiumView android:layout_gravity="end" android:id="@id/iv_premium" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="13.0dip" android:layout_marginEnd="16.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/tv_ep" app:layout_constraintTop_toTopOf="parent" />
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/text_03" android:ellipsize="end" android:id="@id/tv_size" android:layout_marginTop="4.0dip" android:lines="1" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="@id/tv_title" app:layout_constraintTop_toBottomOf="@id/tv_title" app:layout_goneMarginStart="8.0dip" style="@style/style_regular_text" />
</androidx.constraintlayout.widget.ConstraintLayout>
