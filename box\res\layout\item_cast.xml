<?xml version="1.0" encoding="utf-8"?>
<FrameLayout android:id="@id/item_root" android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <View android:id="@id/bg_gradient" android:background="@drawable/bg_category_gradient" android:layout_width="fill_parent" android:layout_height="102.0dip" />
    <androidx.appcompat.widget.LinearLayoutCompat android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="wrap_content">
        <View android:id="@id/divider" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="16.0dip" />
        <androidx.constraintlayout.widget.ConstraintLayout android:paddingTop="8.0dip" android:paddingBottom="8.0dip" android:layout_width="fill_parent" android:layout_height="wrap_content">
            <com.google.android.material.imageview.ShapeableImageView android:id="@id/iv_cover" android:layout_width="90.0dip" android:layout_height="130.0dip" android:src="@mipmap/ic_default_video" android:scaleType="centerCrop" android:layout_marginStart="16.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:shapeAppearanceOverlay="@style/corner_style_8" />
            <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/text_01" android:ellipsize="end" android:id="@id/tv_title" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginTop="16.0dip" android:text="@string/no_network" android:lines="1" android:textAlignment="viewStart" android:layout_marginStart="8.0dip" android:layout_marginEnd="16.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/iv_cover" app:layout_constraintTop_toTopOf="@id/iv_cover" style="@style/style_medium_text" />
            <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_cast" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="8.0dip" android:tint="@color/text_09" android:layout_marginStart="8.0dip" app:layout_constraintStart_toEndOf="@id/iv_cover" app:layout_constraintTop_toBottomOf="@id/tv_title" app:srcCompat="@mipmap/ic_cast" />
            <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/text_03" android:ellipsize="end" android:id="@id/tv_desc" android:visibility="visible" android:layout_width="0.0dip" android:maxLines="2" android:textAlignment="viewStart" android:layout_marginStart="2.0dip" android:layout_marginEnd="8.0dip" app:layout_constraintBottom_toBottomOf="@id/iv_cast" app:layout_constraintEnd_toStartOf="@id/iv_arrow" app:layout_constraintStart_toEndOf="@id/iv_cast" app:layout_constraintTop_toTopOf="@id/iv_cast" style="@style/style_regular_text" />
            <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/text_03" android:id="@id/tv_subject_num" app:layout_constraintBottom_toBottomOf="@id/iv_cover" app:layout_constraintStart_toStartOf="@id/iv_cast" app:layout_constraintTop_toBottomOf="@id/tv_desc" style="@style/style_regular_text" />
            <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_arrow" android:layout_width="wrap_content" android:layout_height="wrap_content" android:tint="@color/text_10" android:layout_marginEnd="16.0dip" app:layout_constraintBottom_toBottomOf="@id/iv_cover" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="@id/iv_cover" app:srcCompat="@mipmap/ic_arrow_right" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.appcompat.widget.LinearLayoutCompat>
</FrameLayout>
