<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:id="@id/item_comment_layout" android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <ImageView android:id="@id/item_comment_sub_user_avatar" android:layout_width="20.0dip" android:layout_height="20.0dip" android:layout_marginTop="12.0dip" android:scaleType="centerCrop" android:layout_marginStart="55.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="@dimen/text_size_14" android:textColor="@color/white_60" android:ellipsize="end" android:id="@id/item_comment_sub_user_name" android:layout_width="wrap_content" android:layout_height="wrap_content" android:maxWidth="180.0dip" android:maxLines="1" android:singleLine="true" android:layout_marginStart="8.0dip" app:layout_constraintStart_toEndOf="@id/item_comment_sub_user_avatar" app:layout_constraintTop_toTopOf="@id/item_comment_sub_user_avatar" style="@style/style_regular_text" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_comment_reply" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:src="@mipmap/comment_arrow_reply" android:layout_marginStart="4.0dip" android:layout_marginEnd="4.0dip" app:layout_constraintBottom_toBottomOf="@id/item_comment_sub_user_name" app:layout_constraintEnd_toStartOf="@id/tv_comment_sub_reply_user_name" app:layout_constraintStart_toEndOf="@id/item_comment_sub_user_name" app:layout_constraintTop_toTopOf="@id/item_comment_sub_user_name" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="@dimen/text_size_12" android:textColor="@color/white_60" android:ellipsize="end" android:gravity="start" android:id="@id/tv_comment_sub_reply_user_name" android:visibility="gone" android:layout_width="0.0dip" android:layout_height="wrap_content" android:singleLine="true" android:layout_marginEnd="12.0dip" app:layout_constraintBottom_toBottomOf="@id/item_comment_sub_user_name" app:layout_constraintEnd_toStartOf="@id/item_comment_sub_like_icon" app:layout_constraintStart_toEndOf="@id/iv_comment_reply" app:layout_constraintTop_toTopOf="@id/item_comment_sub_user_name" style="@style/style_regular_text" />
    <TextView android:textSize="@dimen/text_size_16" android:textColor="@color/white_80" android:gravity="start|center" android:id="@id/item_comment_sub_content" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginTop="4.0dip" android:includeFontPadding="false" android:textAlignment="viewStart" android:layout_marginEnd="52.0dip" app:layout_constrainedWidth="true" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="@id/item_comment_sub_user_name" app:layout_constraintTop_toBottomOf="@id/item_comment_sub_user_name" style="@style/style_regular_text" />
    <androidx.appcompat.widget.AppCompatImageView android:enabled="false" android:id="@id/item_comment_sub_like_icon" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginEnd="16.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="@id/item_comment_sub_content" app:srcCompat="@drawable/comment_item_like_selector" />
    <TextView android:enabled="false" android:textSize="@dimen/text_size_12" android:textColor="@color/comment_item_like_color" android:id="@id/item_comment_sub_like_count" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="2.0dip" app:layout_constraintEnd_toEndOf="@id/item_comment_sub_like_icon" app:layout_constraintStart_toStartOf="@id/item_comment_sub_like_icon" app:layout_constraintTop_toBottomOf="@id/item_comment_sub_like_icon" />
    <View android:id="@id/item_comment_sub_like_click_bg" android:layout_width="52.0dip" android:layout_height="0.0dip" app:layout_constraintBottom_toBottomOf="@id/item_comment_sub_like_count" app:layout_constraintEnd_toEndOf="@id/item_comment_sub_like_icon" app:layout_constraintStart_toStartOf="@id/item_comment_sub_like_icon" app:layout_constraintTop_toTopOf="parent" />
    <com.google.android.material.imageview.ShapeableImageView android:id="@id/sub_comment_cover" android:visibility="gone" android:layout_width="100.0dip" android:layout_height="100.0dip" android:layout_marginTop="6.0dip" android:scaleType="centerCrop" app:layout_constraintStart_toStartOf="@id/item_comment_sub_user_name" app:layout_constraintTop_toBottomOf="@id/item_comment_sub_content" app:shapeAppearanceOverlay="@style/ImgRoundedStyle_4dp" />
    <TextView android:textSize="@dimen/text_size_14" android:textColor="@color/white_60" android:id="@id/item_comment_sub_data" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginStart="4.0dip" android:layout_marginEnd="60.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/item_comment_sub_user_name" app:layout_constraintTop_toTopOf="@id/item_comment_sub_user_avatar" style="@style/style_regular_text" />
    <TextView android:textSize="@dimen/text_size_14" android:textColor="@color/white_60" android:id="@id/item_comment_sub_reply" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="8.0dip" android:text="@string/comment_reply" app:layout_constraintStart_toStartOf="@id/item_comment_sub_user_name" app:layout_constraintTop_toBottomOf="@id/sub_comment_cover" style="@style/style_regular_text" />
    <View android:visibility="invisible" android:layout_width="1.0dip" android:layout_height="12.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/item_comment_sub_reply" />
</androidx.constraintlayout.widget.ConstraintLayout>
