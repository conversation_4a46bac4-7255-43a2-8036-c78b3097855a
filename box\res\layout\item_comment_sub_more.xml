<?xml version="1.0" encoding="utf-8"?>
<FrameLayout android:orientation="horizontal" android:paddingBottom="12.0dip" android:layout_width="wrap_content" android:layout_height="wrap_content" android:minHeight="28.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <ProgressBar android:layout_gravity="top" android:id="@id/item_comment_sub_more_loading" android:visibility="gone" android:layout_width="16.0dip" android:layout_height="16.0dip" android:layout_marginStart="82.0dip" android:indeterminateTint="@color/main" />
    <LinearLayout android:layout_gravity="top" android:orientation="horizontal" android:id="@id/item_comment_sub_more_layout" android:layout_width="wrap_content" android:layout_height="wrap_content">
        <View android:layout_gravity="center_vertical" android:background="@color/text_07" android:layout_width="20.0dip" android:layout_height="1.0dip" android:layout_marginStart="55.0dip" />
        <TextView android:textSize="@dimen/text_size_14" android:textColor="#ffabc2f8" android:layout_gravity="center_vertical" android:id="@id/item_comment_sub_more_txt" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginStart="10.0dip" style="@style/style_medium_text" />
        <ImageView android:layout_gravity="center_vertical" android:layout_width="wrap_content" android:layout_height="wrap_content" android:src="@mipmap/comment_sub_more_icon" android:layout_marginStart="5.0dip" app:tint="@color/text_07" />
    </LinearLayout>
</FrameLayout>
