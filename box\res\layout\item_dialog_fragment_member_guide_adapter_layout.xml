<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:orientation="horizontal" android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/ivMemberRightIcon" android:layout_width="36.0dip" android:layout_height="36.0dip" android:src="@drawable/me_icon_ad" android:layout_marginStart="24.0dip" />
    <TextView android:textSize="16.0sp" android:textColor="@color/text_01" android:layout_gravity="center_vertical" android:id="@id/tvMemberRightTitle" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginStart="12.0dip" />
</LinearLayout>
