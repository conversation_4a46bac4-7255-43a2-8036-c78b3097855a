<?xml version="1.0" encoding="utf-8"?>
<androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textColor="@drawable/post_detail_selector_subtitle_item_text_protrait" android:gravity="center" android:id="@id/tv_subtitle" android:layout_width="fill_parent" android:layout_height="54.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto" />
