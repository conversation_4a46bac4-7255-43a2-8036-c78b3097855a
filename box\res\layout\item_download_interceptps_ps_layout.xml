<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.google.android.material.imageview.ShapeableImageView android:id="@id/ivAppIcon" android:layout_width="52.0dip" android:layout_height="52.0dip" android:layout_marginStart="8.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:shapeAppearanceOverlay="@style/corner_style_8" />
    <TextView android:textSize="16.0sp" android:textColor="@color/text_01" android:ellipsize="end" android:id="@id/tvAppName" android:layout_width="0.0dip" android:layout_height="wrap_content" android:maxLines="1" android:layout_marginStart="8.0dip" android:layout_marginEnd="8.0dip" app:layout_constraintEnd_toStartOf="@id/tvInstall" app:layout_constraintStart_toEndOf="@id/ivAppIcon" app:layout_constraintTop_toTopOf="parent" app:layout_constraintVertical_chainStyle="packed" style="@style/style_medium_text" />
    <androidx.appcompat.widget.AppCompatImageView android:layout_gravity="center_vertical" android:id="@id/ivStar" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="1.0dip" android:src="@drawable/ps_link_star" app:layout_constraintStart_toStartOf="@id/tvAppName" app:layout_constraintTop_toBottomOf="@id/tvAppName" />
    <TextView android:textSize="12.0sp" android:textColor="@color/text_02" android:id="@id/tvStarNum" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginStart="2.0dip" app:layout_constraintBottom_toBottomOf="@id/ivStar" app:layout_constraintStart_toEndOf="@id/ivStar" app:layout_constraintTop_toTopOf="@id/ivStar" />
    <View android:id="@id/tvLine" android:background="@color/ad_line" android:layout_width="1.0dip" android:layout_height="8.0dip" android:layout_marginStart="4.0dip" app:layout_constraintBottom_toBottomOf="@id/ivStar" app:layout_constraintStart_toEndOf="@id/tvStarNum" app:layout_constraintTop_toTopOf="@id/ivStar" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/ivDownloadNum" android:layout_width="wrap_content" android:layout_height="wrap_content" android:src="@mipmap/co_download_num" android:layout_marginStart="4.0dip" app:layout_constraintBottom_toBottomOf="@id/ivStar" app:layout_constraintStart_toEndOf="@id/tvLine" app:layout_constraintTop_toTopOf="@id/ivStar" />
    <TextView android:textSize="12.0sp" android:textColor="@color/text_02" android:id="@id/tvSizeNum" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginStart="2.0dip" app:layout_constraintBottom_toBottomOf="@id/ivStar" app:layout_constraintStart_toEndOf="@id/ivDownloadNum" app:layout_constraintTop_toTopOf="@id/ivStar" />
    <TextView android:textSize="12.0sp" android:textColor="@color/text_02" android:ellipsize="end" android:id="@id/tvDesc" android:layout_width="0.0dip" android:layout_height="wrap_content" android:maxLines="1" android:layout_marginStart="2.0dip" android:layout_marginEnd="8.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toStartOf="@id/tvInstall" app:layout_constraintStart_toStartOf="@id/tvAppName" />
    <TextView android:textSize="14.0sp" android:textColor="@color/white" android:gravity="center" android:id="@id/tvInstall" android:background="@drawable/ps_link_8_button_02_bg" android:padding="8.0dip" android:layout_width="wrap_content" android:layout_height="36.0dip" android:text="@string/ps_link_install" android:layout_marginEnd="8.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>
