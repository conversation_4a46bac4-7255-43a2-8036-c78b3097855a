<?xml version="1.0" encoding="utf-8"?>
<com.noober.background.view.BLLinearLayout android:gravity="center" android:layout_width="72.0dip" android:layout_height="28.0dip" app:bl_corners_radius="4.0dip" app:bl_selected_solid_color="@color/main_10" app:bl_selected_stroke_color="@color/main" app:bl_stroke_width="1.0dip" app:bl_unSelected_solid_color="@color/module_06" app:bl_unSelected_stroke_color="@color/module_06" style="@style/style_medium_text"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_resolution" android:layout_width="wrap_content" android:layout_height="wrap_content" android:scaleType="fitCenter" android:layout_marginEnd="2.0dip" />
    <com.noober.background.view.BLTextView android:id="@id/tv_resolution" android:layout_width="wrap_content" android:layout_height="wrap_content" app:bl_selected_textColor="@color/main" app:bl_unSelected_textColor="@color/text_01" style="@style/style_medium_text" />
</com.noober.background.view.BLLinearLayout>
