<?xml version="1.0" encoding="utf-8"?>
<FrameLayout android:paddingBottom="16.0dip" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginStart="16.0dip" android:layout_marginEnd="16.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.noober.background.view.BLTextView android:textColor="@color/error_50" android:gravity="center_vertical" android:id="@id/tv_unavailable_tips" android:paddingTop="8.0dip" android:paddingBottom="8.0dip" android:layout_width="fill_parent" android:layout_height="wrap_content" android:text="@string/download_redownload_tips" android:drawablePadding="8.0dip" android:paddingStart="16.0dip" android:paddingEnd="16.0dip" app:bl_corners_radius="8.0dip" app:bl_solid_color="@color/error_module" app:drawableStartCompat="@mipmap/ic_download_error_tips" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
</FrameLayout>
