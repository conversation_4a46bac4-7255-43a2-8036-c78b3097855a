<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:paddingBottom="16.0dip" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginStart="16.0dip" android:layout_marginEnd="16.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.noober.background.view.BLView android:id="@id/v_phone_storage_bg" android:layout_width="0.0dip" android:layout_height="124.0dip" app:bl_corners_radius="8.0dip" app:bl_solid_color="@color/download_module_1" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:layout_goneMarginTop="48.0dip" />
    <com.noober.background.view.BLImageView android:id="@id/iv_phone_storage_ic" android:layout_width="44.0dip" android:layout_height="44.0dip" android:layout_marginTop="16.0dip" android:src="@drawable/ic_download_phone_storage" android:scaleType="center" android:layout_marginStart="16.0dip" app:bl_corners_radius="8.0dip" app:bl_solid_color="@color/download_module_2" app:layout_constraintStart_toStartOf="@id/v_phone_storage_bg" app:layout_constraintTop_toTopOf="@id/v_phone_storage_bg" app:layout_constraintVertical_chainStyle="packed" />
    <TextView android:textColor="@color/text_01" android:id="@id/tv_phone_storage_title" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/str_download_dialog_path_phone_storage" android:layout_marginStart="8.0dip" app:layout_constraintBottom_toTopOf="@id/tv_phone_storage_available_size" app:layout_constraintStart_toEndOf="@id/iv_phone_storage_ic" app:layout_constraintTop_toTopOf="@id/iv_phone_storage_ic" style="@style/style_medium_text" />
    <TextView android:textSize="12.0sp" android:textColor="@color/text_02" android:id="@id/tv_phone_storage_available_size" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginStart="8.0dip" app:layout_constraintBottom_toBottomOf="@id/iv_phone_storage_ic" app:layout_constraintStart_toEndOf="@id/iv_phone_storage_ic" app:layout_constraintTop_toBottomOf="@id/tv_phone_storage_title" />
    <com.noober.background.view.BLView android:id="@id/v_phone_storage_album_bg" android:layout_width="0.0dip" android:layout_height="44.0dip" android:layout_marginTop="8.0dip" android:layout_marginStart="16.0dip" android:layout_marginEnd="16.0dip" app:bl_corners_radius="8.0dip" app:bl_solid_color="@color/download_module_2" app:layout_constraintEnd_toEndOf="@id/v_phone_storage_bg" app:layout_constraintStart_toStartOf="@id/v_phone_storage_bg" app:layout_constraintTop_toBottomOf="@id/iv_phone_storage_ic" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_phone_storage_album_ic" android:layout_width="24.0dip" android:layout_height="24.0dip" android:src="@mipmap/ic_download_file" android:layout_marginStart="10.0dip" app:layout_constraintBottom_toBottomOf="@id/v_phone_storage_album_bg" app:layout_constraintStart_toStartOf="@id/v_phone_storage_album_bg" app:layout_constraintTop_toTopOf="@id/v_phone_storage_album_bg" />
    <TextView android:textColor="@color/text_01" android:gravity="center" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/str_download_dialog_path_albums" android:layout_marginStart="10.0dip" app:layout_constraintBottom_toBottomOf="@id/v_phone_storage_album_bg" app:layout_constraintStart_toEndOf="@id/iv_phone_storage_album_ic" app:layout_constraintTop_toTopOf="@id/v_phone_storage_album_bg" style="@style/style_medium_text" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_album_check" android:layout_width="32.0dip" android:layout_height="0.0dip" android:scaleType="center" android:layout_marginEnd="8.0dip" app:layout_constraintBottom_toBottomOf="@id/v_phone_storage_album_bg" app:layout_constraintEnd_toEndOf="@id/v_phone_storage_album_bg" app:layout_constraintTop_toTopOf="@id/v_phone_storage_album_bg" app:srcCompat="@drawable/selector_download_path_check" />
</androidx.constraintlayout.widget.ConstraintLayout>
