<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:paddingBottom="16.0dip" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginStart="16.0dip" android:layout_marginEnd="16.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.noober.background.view.BLView android:id="@id/v_sd_bg" android:layout_width="0.0dip" android:layout_height="76.0dip" app:bl_corners_radius="8.0dip" app:bl_solid_color="@color/download_module_1" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <com.noober.background.view.BLImageView android:id="@id/iv_sd_ic" android:layout_width="44.0dip" android:layout_height="44.0dip" android:layout_marginTop="16.0dip" android:src="@drawable/ic_download_sdcard" android:scaleType="center" android:layout_marginStart="16.0dip" app:bl_corners_radius="8.0dip" app:bl_solid_color="@color/download_module_2" app:layout_constraintStart_toStartOf="@id/v_sd_bg" app:layout_constraintTop_toTopOf="@id/v_sd_bg" app:layout_constraintVertical_chainStyle="packed" />
    <TextView android:textColor="@color/text_01" android:id="@id/tv_sd_title" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginStart="8.0dip" app:layout_constraintBottom_toTopOf="@id/tv_sd_size" app:layout_constraintStart_toEndOf="@id/iv_sd_ic" app:layout_constraintTop_toTopOf="@id/iv_sd_ic" style="@style/style_medium_text" />
    <TextView android:textSize="12.0sp" android:textColor="@color/text_02" android:id="@id/tv_sd_size" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginStart="8.0dip" app:layout_constraintBottom_toBottomOf="@id/iv_sd_ic" app:layout_constraintStart_toEndOf="@id/iv_sd_ic" app:layout_constraintTop_toBottomOf="@id/tv_sd_title" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_sdcard_check" android:layout_width="32.0dip" android:layout_height="0.0dip" android:scaleType="center" android:layout_marginEnd="8.0dip" app:layout_constraintBottom_toBottomOf="@id/v_sd_bg" app:layout_constraintEnd_toEndOf="@id/v_sd_bg" app:layout_constraintTop_toTopOf="@id/v_sd_bg" app:srcCompat="@drawable/selector_download_path_check" />
</androidx.constraintlayout.widget.ConstraintLayout>
