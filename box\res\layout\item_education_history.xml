<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="@dimen/dp_12"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.google.android.material.imageview.ShapeableImageView android:id="@id/education_history_cover" android:layout_width="120.0dip" android:layout_height="68.0dip" android:scaleType="centerCrop" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:shapeAppearanceOverlay="@style/roundStyle_8" />
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/text_01" android:ellipsize="end" android:id="@id/education_history_title" android:layout_width="0.0dip" android:maxLines="3" android:layout_marginStart="8.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/education_history_cover" app:layout_constraintTop_toTopOf="parent" style="@style/style_medium_text" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/text_03" android:ellipsize="end" android:id="@id/education_history_tag" android:layout_width="wrap_content" android:layout_height="wrap_content" android:maxLines="1" android:layout_marginStart="8.0dip" app:layout_constraintBottom_toBottomOf="@id/education_history_learn" app:layout_constraintStart_toEndOf="@id/education_history_cover" app:layout_constraintTop_toTopOf="@id/education_history_learn" style="@style/style_regular_text" />
    <TextView android:textColor="@color/white" android:gravity="center" android:id="@id/education_history_learn" android:background="@drawable/bg_btn_01" android:layout_width="64.0dip" android:layout_height="24.0dip" android:text="@string/course_learn" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" style="@style/style_medium_text" />
</androidx.constraintlayout.widget.ConstraintLayout>
