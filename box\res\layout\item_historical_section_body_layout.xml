<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:id="@id/historyRoot" android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.cardview.widget.CardView android:id="@id/cardCore" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginBottom="16.0dip" android:layout_marginStart="16.0dip" app:cardCornerRadius="8.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent">
        <com.google.android.material.imageview.ShapeableImageView android:id="@id/ivCore" android:layout_width="120.0dip" android:layout_height="68.0dip" android:scaleType="centerCrop" />
        <androidx.appcompat.widget.AppCompatImageView android:layout_width="26.0dip" android:layout_height="26.0dip" android:src="@mipmap/left_top_shadow" />
        <androidx.appcompat.widget.AppCompatImageView android:id="@id/ivCornerMark" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="6.0dip" android:src="@mipmap/ic_audio_download_historical" android:layout_marginStart="6.0dip" />
        <View android:layout_gravity="bottom" android:background="@drawable/download_mask_cl45_30p_to_0p" android:layout_width="120.0dip" android:layout_height="28.0dip" />
        <androidx.appcompat.widget.AppCompatTextView android:textSize="11.0sp" android:textColor="@color/white" android:layout_gravity="end|bottom" android:id="@id/tvTime" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginBottom="4.0dip" android:layout_marginEnd="4.0dip" style="@style/robot_medium" />
        <ProgressBar android:layout_gravity="bottom" android:id="@id/progressBar" android:layout_width="120.0dip" android:layout_height="3.0dip" android:progress="70" android:progressDrawable="@drawable/download_progress_drawable_trans" style="?android:progressBarStyleHorizontal" />
        <ViewStub android:id="@id/viewStub" android:layout="@layout/view_stub_local_file_delete_layout" android:layout_width="120.0dip" android:layout_height="68.0dip" />
    </androidx.cardview.widget.CardView>
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/ivTopRightCornerPoint" android:paddingBottom="20.0dip" android:layout_width="wrap_content" android:layout_height="wrap_content" android:scaleType="centerCrop" android:paddingStart="5.0dip" android:paddingEnd="16.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent" app:srcCompat="@drawable/ic_download_more" app:tint="@color/white" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/white_60" android:id="@id/tvProgress" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="8.0dip" android:textAlignment="viewStart" android:layout_marginStart="8.0dip" app:layout_constraintBottom_toBottomOf="@id/cardCore" app:layout_constraintStart_toEndOf="@id/cardCore" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="14.0sp" android:textColor="@color/text_01" android:ellipsize="end" android:gravity="start" android:id="@id/tvTitle" android:layout_width="0.0dip" android:layout_height="wrap_content" android:maxLines="2" android:textAlignment="viewStart" android:layout_marginStart="8.0dip" app:layout_constraintEnd_toStartOf="@id/ivTopRightCornerPoint" app:layout_constraintStart_toEndOf="@id/cardCore" app:layout_constraintTop_toTopOf="@id/cardCore" style="@style/style_medium_text" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="14.0sp" android:textColor="@color/text_01" android:ellipsize="end" android:gravity="start" android:id="@id/tvEpisode" android:visibility="gone" android:maxLines="1" android:textAlignment="viewStart" app:layout_constraintStart_toStartOf="@id/tvTitle" app:layout_constraintTop_toBottomOf="@id/tvTitle" style="@style/style_medium_text" />
    <androidx.appcompat.widget.AppCompatImageView android:layout_width="24.0dip" android:layout_height="24.0dip" android:layout_marginEnd="@dimen/dp_12" app:layout_constraintBottom_toBottomOf="@id/cardCore" app:layout_constraintEnd_toEndOf="parent" app:srcCompat="@mipmap/ic_play_transparent" />
</androidx.constraintlayout.widget.ConstraintLayout>
