<?xml version="1.0" encoding="utf-8"?>
<com.transsion.baseui.widget.RoundedConstraintLayout android:layout_width="wrap_content" android:layout_height="130.0dip" app:cornerRadius="8.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <View android:id="@id/item_honor_background" android:background="@color/base_black_50_p" android:layout_width="fill_parent" android:layout_height="fill_parent" />
    <com.transsion.home.view.VerticalRoundedRectView android:id="@id/item_honor_dot_start" android:layout_width="4.0dip" android:layout_height="fill_parent" android:layout_marginTop="9.0dip" android:layout_marginStart="5.0dip" app:layout_constraintStart_toStartOf="parent" app:rectColor="#cc000000" app:rectCorner="1.0dip" app:rectCount="13" app:rectHeight="4.0dip" app:rectSpacing="5.0dip" app:rectWidth="4.0dip" />
    <com.transsion.home.view.VerticalRoundedRectView android:id="@id/item_honor_dot_end" android:layout_width="4.0dip" android:layout_height="fill_parent" android:layout_marginTop="9.0dip" android:layout_marginEnd="5.0dip" app:layout_constraintEnd_toEndOf="parent" app:rectColor="#cc000000" app:rectCorner="1.0dip" app:rectCount="13" app:rectHeight="4.0dip" app:rectSpacing="5.0dip" app:rectWidth="4.0dip" />
    <LinearLayout android:gravity="center" android:orientation="vertical" android:id="@id/item_honor_content" android:layout_width="wrap_content" android:layout_height="wrap_content" android:paddingStart="@dimen/dimens_16" android:paddingEnd="@dimen/dimens_16" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent">
        <ImageView android:id="@id/item_honor_level_icon" android:layout_width="60.5dip" android:layout_height="56.0dip" />
        <com.tn.lib.widget.TnTextView android:textSize="@dimen/text_size_10" android:textColor="@color/white" android:ellipsize="end" android:id="@id/item_honor_level_title" android:layout_marginTop="2.0dip" android:maxLines="1" style="@style/style_import_text" />
        <com.tn.lib.widget.TnTextView android:textSize="@dimen/text_size_10" android:textColor="@color/color_92969E" android:ellipsize="end" android:gravity="center" android:id="@id/item_honor_level_subtitle" android:maxLines="2" style="@style/style_regular_text" />
    </LinearLayout>
    <include android:id="@id/itemClaimLayout" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" layout="@layout/layout_home_member_claim" />
</com.transsion.baseui.widget.RoundedConstraintLayout>
