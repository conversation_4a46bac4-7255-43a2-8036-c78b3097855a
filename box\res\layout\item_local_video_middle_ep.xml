<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:background="@drawable/post_detail_episode_bg" android:layout_width="fill_parent" android:layout_height="44.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <TextView android:textSize="14.0sp" android:textColor="@color/post_detail_short_tv_episode_tv_color" android:id="@id/tv_title" android:layout_marginStart="8.0dip" app:layout_constraintBottom_toTopOf="@id/tv_size" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:layout_constraintVertical_chainStyle="packed" style="@style/style_medium_text" />
    <TextView android:textColor="@color/white_60" android:id="@id/tv_size" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="@id/tv_title" app:layout_constraintTop_toBottomOf="@id/tv_title" style="@style/style_regular_text" />
</androidx.constraintlayout.widget.ConstraintLayout>
