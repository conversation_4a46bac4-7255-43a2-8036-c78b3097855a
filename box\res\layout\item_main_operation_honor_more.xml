<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="wrap_content" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.google.android.material.imageview.ShapeableImageView android:id="@id/main_op_honor_image" android:layout_width="91.0dip" android:layout_height="130.0dip" android:scaleType="centerCrop" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:shapeAppearanceOverlay="@style/roundStyle_4" />
    <View android:id="@id/main_op_honor_more_mask" android:background="@drawable/shape_downloading_cover_fg" android:visibility="gone" android:layout_width="91.0dip" android:layout_height="130.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <com.tn.lib.widget.TnTextView android:textColor="@color/common_white" android:ellipsize="end" android:id="@id/main_op_honor_more_text" android:visibility="gone" android:maxWidth="68.0dip" android:text="@string/short_tv_category_view_all" android:maxLines="1" android:includeFontPadding="false" app:drawableEndCompat="@mipmap/ic_arrow_right" app:drawableTint="@color/common_white" app:layout_constraintBottom_toBottomOf="@id/main_op_honor_image" app:layout_constraintEnd_toEndOf="@id/main_op_honor_image" app:layout_constraintStart_toStartOf="@id/main_op_honor_image" app:layout_constraintTop_toTopOf="@id/main_op_honor_image" style="@style/style_medium_text" />
</androidx.constraintlayout.widget.ConstraintLayout>
