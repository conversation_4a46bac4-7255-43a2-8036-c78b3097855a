<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="wrap_content" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.google.android.material.imageview.ShapeableImageView android:id="@id/main_op_movie_rank_image" android:layout_width="wrap_content" android:layout_height="wrap_content" android:scaleType="centerCrop" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:shapeAppearanceOverlay="@style/roundStyle_4" />
    <com.tn.lib.view.CornerTextView android:id="@id/main_op_movie_rank_corner" android:layout_width="wrap_content" android:layout_height="wrap_content" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <View android:id="@id/main_op_movie_rank_more_mask" android:background="@drawable/bg_brand_corner_4" android:layout_width="wrap_content" android:layout_height="wrap_content" android:backgroundTint="@color/black_50" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <com.tn.lib.widget.TnTextView android:textColor="@color/text_02" android:ellipsize="end" android:maxWidth="68.0dip" android:text="@string/str_more" android:maxLines="1" android:includeFontPadding="false" app:drawableEndCompat="@mipmap/ic_arrow_right" app:drawableTint="@color/gray_0_60" app:layout_constraintBottom_toBottomOf="@id/main_op_movie_rank_image" app:layout_constraintEnd_toEndOf="@id/main_op_movie_rank_image" app:layout_constraintStart_toStartOf="@id/main_op_movie_rank_image" app:layout_constraintTop_toTopOf="@id/main_op_movie_rank_image" style="@style/style_medium_text" />
</androidx.constraintlayout.widget.ConstraintLayout>
