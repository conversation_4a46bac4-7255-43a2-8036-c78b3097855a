<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout android:id="@id/rlLayout" android:layout_width="fill_parent" android:layout_height="wrap_content" android:paddingStart="12.0dip" android:paddingEnd="12.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.google.android.material.imageview.ShapeableImageView android:id="@id/imAvatar" android:background="#fff7f7f7" android:layout_width="32.0dip" android:layout_height="32.0dip" android:layout_marginTop="11.0dip" android:scaleType="centerCrop" android:layout_marginEnd="10.0dip" android:layout_alignParentStart="true" app:shapeAppearanceOverlay="@style/circle_style" />
    <View android:id="@id/view_red_blank" android:background="@drawable/bg_red_notice" android:visibility="gone" android:layout_width="6.0dip" android:layout_height="6.0dip" android:layout_marginTop="3.0dip" android:layout_alignTop="@id/imAvatar" android:layout_marginEnd="3.0dip" android:layout_alignEnd="@id/imAvatar" />
    <TextView android:textSize="12.0dip" android:textColor="@color/white_80" android:ellipsize="end" android:id="@id/tvNickName" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="11.0dip" android:text="vskit" android:singleLine="true" android:layout_marginEnd="4.0dip" android:layout_toEndOf="@id/imAvatar" style="@style/style_medium_text" />
    <TextView android:textSize="12.0dip" android:textColor="@color/white_60" android:ellipsize="end" android:id="@id/tvTime" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="11.0dip" android:text="time" android:maxLines="1" android:layout_toStartOf="@id/imCover" android:layout_toEndOf="@id/tvNickName" style="@style/style_regular_text" />
    <TextView android:textSize="14.0dip" android:textColor="@color/text_06" android:ellipsize="end" android:gravity="start|center" android:id="@id/tvContent" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="6.0dip" android:text="content..............." android:maxLines="2" android:includeFontPadding="false" android:layout_below="@id/tvNickName" android:textAlignment="viewStart" android:layout_marginEnd="24.0dip" android:layout_toStartOf="@id/imCover" android:layout_toEndOf="@id/imAvatar" app:layout_constrainedWidth="true" style="@style/style_regular_text" />
    <ImageView android:id="@id/imCover" android:layout_width="56.0dip" android:layout_height="56.0dip" android:layout_marginTop="20.0dip" android:scaleType="centerCrop" android:layout_centerVertical="true" android:layout_alignParentEnd="true" />
    <View android:background="@color/white_10" android:layout_width="fill_parent" android:layout_height="1.0dip" android:layout_alignParentBottom="true" />
</RelativeLayout>
