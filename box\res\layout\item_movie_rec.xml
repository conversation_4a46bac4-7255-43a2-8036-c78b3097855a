<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:id="@id/item" android:layout_width="92.0dip" android:layout_height="wrap_content" android:layout_marginStart="8.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.google.android.material.imageview.ShapeableImageView android:id="@id/iv_cover" android:layout_width="92.0dip" android:layout_height="128.0dip" android:scaleType="centerCrop" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:shapeAppearanceOverlay="@style/roundStyle_4" />
    <com.tn.lib.view.CornerTextView android:id="@id/tv_download_corner" android:layout_width="wrap_content" android:layout_height="wrap_content" app:layout_constraintEnd_toEndOf="@id/iv_cover" app:layout_constraintTop_toTopOf="@id/iv_cover" />
    <View android:background="@drawable/home_mask_ranking" android:layout_width="92.0dip" android:layout_height="48.0dip" app:layout_constraintBottom_toBottomOf="@id/iv_cover" app:layout_constraintStart_toStartOf="parent" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/icon_download" android:layout_width="16.0dip" android:layout_height="16.0dip" android:layout_marginStart="8.0dip" app:layout_constraintBottom_toBottomOf="@id/tv_score" app:layout_constraintStart_toStartOf="@id/iv_cover" app:layout_constraintTop_toTopOf="@id/tv_score" app:srcCompat="@mipmap/ic_download_red" />
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/color_score" android:id="@id/tv_score" android:visibility="visible" android:layout_marginBottom="8.0dip" android:shadowColor="@color/black_30" android:shadowRadius="3.0" android:layout_marginEnd="8.0dip" app:layout_constraintBottom_toBottomOf="@id/iv_cover" app:layout_constraintEnd_toEndOf="@id/iv_cover" style="@style/style_medium_text" />
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/text_01" android:ellipsize="end" android:id="@id/tv_title" android:layout_marginTop="8.0dip" android:maxLines="1" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/iv_cover" style="@style/style_medium_text" />
    <com.transsnet.downloader.widget.DownloadView android:id="@id/download" android:layout_width="92.0dip" android:layout_height="28.0dip" android:layout_marginTop="8.0dip" app:iconSrc="@mipmap/ad_icon_download_green" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tv_title" app:tips_textColor="@color/brand" style="@style/style_sub_btn3" />
</androidx.constraintlayout.widget.ConstraintLayout>
