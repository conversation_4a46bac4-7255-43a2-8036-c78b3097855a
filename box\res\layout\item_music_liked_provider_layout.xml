<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.google.android.material.imageview.ShapeableImageView android:id="@id/ivCover" android:layout_width="100.0dip" android:layout_height="56.0dip" android:layout_marginStart="12.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:shapeAppearanceOverlay="@style/corner_style_4" />
    <View android:id="@id/viewMask" android:background="@color/black_50" android:visibility="gone" android:layout_width="0.0dip" android:layout_height="0.0dip" app:layout_constraintBottom_toBottomOf="@id/ivCover" app:layout_constraintEnd_toEndOf="@id/ivCover" app:layout_constraintStart_toStartOf="@id/ivCover" app:layout_constraintTop_toTopOf="@id/ivCover" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/ivState" android:layout_width="16.0dip" android:layout_height="16.0dip" android:tint="@color/brand" app:layout_constraintBottom_toBottomOf="@id/ivCover" app:layout_constraintEnd_toEndOf="@id/ivCover" app:layout_constraintStart_toStartOf="@id/ivCover" app:layout_constraintTop_toTopOf="@id/ivCover" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textColor="@color/text_01" android:ellipsize="end" android:id="@id/tvTitle" android:layout_width="0.0dip" android:layout_height="wrap_content" android:maxLines="2" android:layout_marginStart="8.0dip" android:layout_marginEnd="12.0dip" app:layout_constraintBottom_toTopOf="@id/tvAuthor" app:layout_constraintEnd_toStartOf="@id/ivNextPlay" app:layout_constraintStart_toEndOf="@id/ivCover" app:layout_constraintTop_toTopOf="@id/ivCover" app:layout_constraintVertical_chainStyle="packed" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/ivNextPlay" android:visibility="gone" android:layout_width="24.0dip" android:layout_height="24.0dip" android:src="@drawable/music_iv_play_next" android:layout_marginEnd="12.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/tvTitle" app:layout_constraintTop_toTopOf="parent" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/text_02" android:id="@id/tvAuthor" android:layout_width="0.0dip" android:layout_height="wrap_content" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="@id/tvTitle" app:layout_constraintStart_toStartOf="@id/tvTitle" app:layout_constraintTop_toBottomOf="@id/tvTitle" />
</androidx.constraintlayout.widget.ConstraintLayout>
