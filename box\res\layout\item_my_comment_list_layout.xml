<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:paddingLeft="12.0dip" android:paddingRight="12.0dip" android:layout_width="fill_parent" android:layout_height="wrap_content" android:minHeight="@dimen/dp_84"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.google.android.material.imageview.ShapeableImageView android:id="@id/avatarIV" android:layout_width="32.0dip" android:layout_height="32.0dip" android:layout_marginTop="12.0dip" android:scaleType="centerCrop" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:shapeAppearanceOverlay="@style/circle_style" />
    <com.google.android.material.imageview.ShapeableImageView android:id="@id/contentIV" android:layout_width="51.0dip" android:layout_height="34.0dip" android:layout_marginTop="12.0dip" android:scaleType="centerCrop" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/nameTV" app:layout_constraintTop_toTopOf="parent" app:shapeAppearanceOverlay="@style/roundStyle_2" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="14.0sp" android:textColor="@color/text_02" android:ellipsize="end" android:id="@id/nameTV" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginTop="14.0dip" android:maxLines="1" android:includeFontPadding="false" android:layout_marginStart="12.0dip" android:layout_marginEnd="8.0dip" app:layout_constraintEnd_toStartOf="@id/contentIV" app:layout_constraintStart_toEndOf="@id/avatarIV" app:layout_constraintTop_toTopOf="parent" style="@style/robot_bold" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/white_60" android:ellipsize="end" android:id="@id/timeTV" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginTop="@dimen/dp_2" android:maxLines="1" android:includeFontPadding="false" app:layout_constraintEnd_toEndOf="@id/nameTV" app:layout_constraintStart_toStartOf="@id/nameTV" app:layout_constraintTop_toBottomOf="@id/nameTV" style="@style/robot_medium" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="14.0sp" android:textColor="@color/text_02" android:ellipsize="end" android:id="@id/commentTV" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="6.0dip" android:maxLines="1" android:includeFontPadding="false" android:layout_marginEnd="4.0dip" app:layout_constraintStart_toStartOf="@id/nameTV" app:layout_constraintTop_toBottomOf="@id/timeTV" style="@style/robot_medium" />
    <View android:background="@color/line_01" android:layout_width="fill_parent" android:layout_height="1.0dip" app:layout_constraintBottom_toBottomOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>
