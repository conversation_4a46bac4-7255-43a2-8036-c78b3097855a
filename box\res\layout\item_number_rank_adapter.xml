<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="wrap_content" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <ImageView android:id="@id/op_item_number_rank_index_image" android:layout_width="44.0dip" android:layout_height="73.0dip" android:scaleType="centerInside" android:contentDescription="@string/play" app:layout_constraintBottom_toBottomOf="@id/op_item_number_rank_poster" app:layout_constraintRight_toLeftOf="@id/op_item_number_rank_poster" />
    <com.google.android.material.imageview.ShapeableImageView android:id="@id/op_item_number_rank_poster" android:layout_width="105.0dip" android:layout_height="147.0dip" android:scaleType="centerCrop" android:layout_marginStart="-2.0dip" app:layout_constraintLeft_toRightOf="@id/op_item_number_rank_index_image" app:layout_constraintTop_toTopOf="parent" app:shapeAppearance="@style/roundStyle_4" />
    <com.tn.lib.view.CornerTextView android:id="@id/op_item_number_rank_corner" android:layout_width="wrap_content" android:layout_height="wrap_content" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <com.tn.lib.widget.TnTextView android:textSize="@dimen/sp_14" android:textColor="@color/text_01" android:ellipsize="end" android:id="@id/op_item_number_rank_title" android:layout_width="0.0dip" android:layout_marginTop="8.0dip" android:maxLines="2" app:layout_constraintEnd_toEndOf="@id/op_item_number_rank_poster" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/op_item_number_rank_poster" style="@style/style_medium_text" />
</androidx.constraintlayout.widget.ConstraintLayout>
