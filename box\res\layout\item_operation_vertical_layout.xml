<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:layout_width="fill_parent" android:layout_height="48.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <ImageView android:layout_gravity="start|center" android:id="@id/image" android:layout_width="22.0dip" android:layout_height="22.0dip" android:layout_marginStart="16.0dip" />
    <com.tn.lib.widget.TnTextView android:textSize="16.0sp" android:textColor="#ff333333" android:layout_gravity="start|center" android:id="@id/desc" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="" android:layout_marginStart="8.0dip" />
</LinearLayout>
