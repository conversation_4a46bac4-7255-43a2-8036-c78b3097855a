<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginLeft="16.0dip" android:layout_marginRight="16.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/ivIcon" android:layout_width="32.0dip" android:layout_height="32.0dip" android:layout_marginTop="3.0dip" android:src="@mipmap/ic_launcher" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <com.tn.lib.widget.TnTextView android:textSize="16.0sp" android:textColor="@color/text_01" android:ellipsize="end" android:id="@id/tvTitle" android:layout_width="0.0dip" android:layout_height="wrap_content" android:maxLines="2" android:layout_marginStart="12.0dip" android:layout_marginEnd="24.0dip" app:layout_constraintEnd_toStartOf="@id/ivDone" app:layout_constraintStart_toEndOf="@id/ivIcon" app:layout_constraintTop_toTopOf="parent" style="@style/robot_medium" />
    <com.tn.lib.widget.TnTextView android:textSize="12.0sp" android:textColor="@color/text_02" android:ellipsize="end" android:id="@id/tvContent" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginTop="2.0dip" android:maxLines="2" android:layout_marginStart="12.0dip" app:layout_constraintEnd_toEndOf="@id/tvTitle" app:layout_constraintStart_toEndOf="@id/ivIcon" app:layout_constraintTop_toBottomOf="@id/tvTitle" />
    <FrameLayout android:id="@id/ivDone" android:background="@drawable/bg_ok" android:layout_width="80.0dip" android:layout_height="32.0dip" android:layout_marginTop="3.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent">
        <androidx.appcompat.widget.AppCompatImageView android:layout_gravity="center" android:layout_width="wrap_content" android:layout_height="wrap_content" android:src="@drawable/ic_ok" />
    </FrameLayout>
    <com.tn.lib.widget.TnTextView android:textSize="14.0sp" android:textColor="@color/white_100" android:ellipsize="end" android:gravity="center" android:id="@id/tvAction" android:background="@drawable/bg_btn_01" android:layout_width="80.0dip" android:layout_height="32.0dip" android:maxLines="1" app:layout_constraintEnd_toEndOf="@id/ivDone" app:layout_constraintTop_toTopOf="@id/ivDone" />
</androidx.constraintlayout.widget.ConstraintLayout>
