<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:orientation="horizontal" android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <TextView android:textSize="@dimen/sp_14" android:textStyle="bold" android:textColor="@color/white" android:ellipsize="end" android:id="@id/tvDes" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginTop="@dimen/dp_10" android:maxLines="2" android:layout_marginStart="@dimen/dp_16" android:layout_marginEnd="32.0dip" app:layout_constraintEnd_toStartOf="@id/tvCoin" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <TextView android:textSize="@dimen/sp_14" android:textColor="@color/white_60" android:id="@id/tvTime" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="@dimen/dp_4" android:layout_marginBottom="@dimen/dp_10" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="@id/tvDes" app:layout_constraintTop_toBottomOf="@id/tvDes" />
    <ImageView android:id="@id/ivIcon" android:layout_width="@dimen/dp_16" android:layout_height="@dimen/dp_16" android:src="@mipmap/ic_member_points" android:layout_marginEnd="@dimen/dp_4" app:layout_constraintBottom_toBottomOf="@id/tvCoin" app:layout_constraintEnd_toStartOf="@id/tvCoin" app:layout_constraintTop_toTopOf="@id/tvCoin" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="@dimen/sp_14" android:textStyle="bold" android:textColor="@color/white" android:id="@id/tvCoin" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginEnd="@dimen/dp_16" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/tvDes" app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>
