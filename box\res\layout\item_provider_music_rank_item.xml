<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="@dimen/dp_8" android:layout_marginBottom="@dimen/dp_8"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.appcompat.widget.AppCompatTextView android:textSize="20.0sp" android:textColor="@color/text_01" android:id="@id/tvRankNum" android:layout_width="28.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" style="@style/style_import_text" />
    <com.google.android.material.imageview.ShapeableImageView android:id="@id/ivCover" android:layout_width="120.0dip" android:layout_height="68.0dip" android:scaleType="centerCrop" app:layout_constraintStart_toEndOf="@id/tvRankNum" app:layout_constraintTop_toTopOf="parent" app:shapeAppearanceOverlay="@style/roundStyle_4" />
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/text_01" android:ellipsize="end" android:id="@id/tvTitle" android:layout_width="0.0dip" android:maxLines="3" android:layout_marginStart="8.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/ivCover" app:layout_constraintTop_toTopOf="parent" style="@style/style_medium_text" />
</androidx.constraintlayout.widget.ConstraintLayout>
