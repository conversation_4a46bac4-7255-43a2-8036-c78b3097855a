<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView android:background="@drawable/post_bg_module_01_8dp" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginStart="16.0dip" android:layout_marginEnd="16.0dip" app:cardCornerRadius="8.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="wrap_content">
        <com.google.android.material.imageview.ShapeableImageView android:id="@id/iv_cover" android:visibility="visible" android:layout_width="fill_parent" android:layout_height="84.0dip" android:src="@mipmap/ic_audio_default_bg_light" android:scaleType="centerCrop" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:shapeAppearanceOverlay="@style/roundStyle_8" />
        <com.google.android.material.imageview.ShapeableImageView android:id="@id/iv_audio" android:layout_width="64.0dip" android:layout_height="64.0dip" android:src="@mipmap/post_audio_scroll_table" android:scaleType="centerCrop" android:layout_marginStart="16.0dip" app:layout_constraintBottom_toBottomOf="@id/iv_cover" app:layout_constraintStart_toStartOf="@id/iv_cover" app:layout_constraintTop_toTopOf="@id/iv_cover" />
        <com.google.android.material.imageview.ShapeableImageView android:id="@id/iv_audio_cover_small" android:layout_width="24.0dip" android:layout_height="24.0dip" android:src="@mipmap/ic_audio_default_bg_light" android:scaleType="centerCrop" app:layout_constraintBottom_toBottomOf="@id/iv_audio" app:layout_constraintEnd_toEndOf="@id/iv_audio" app:layout_constraintStart_toStartOf="@id/iv_audio" app:layout_constraintTop_toTopOf="@id/iv_audio" app:shapeAppearanceOverlay="@style/ImgRoundedStyle" />
        <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textColor="@color/white" android:ellipsize="end" android:gravity="start" android:id="@id/tvAudioTitle" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginBottom="4.0dip" android:maxLines="1" android:textAlignment="viewStart" android:layout_marginStart="8.0dip" android:layout_marginEnd="8.0dip" app:layout_constraintBottom_toTopOf="@id/tvTime" app:layout_constraintEnd_toStartOf="@id/ivAudioPlay" app:layout_constraintStart_toEndOf="@id/iv_audio" app:layout_constraintTop_toTopOf="@id/iv_audio" app:layout_constraintVertical_chainStyle="packed" style="@style/style_medium_small_text" />
        <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/white_60" android:id="@id/tvTime" android:layout_width="wrap_content" android:layout_height="wrap_content" app:layout_constraintBottom_toBottomOf="@id/iv_audio" app:layout_constraintStart_toStartOf="@id/tvAudioTitle" app:layout_constraintTop_toBottomOf="@id/tvAudioTitle" />
        <androidx.appcompat.widget.AppCompatImageView android:id="@id/ivAudioPlay" android:layout_width="32.0dip" android:layout_height="32.0dip" android:src="@drawable/post_video_play" android:layout_marginEnd="12.0dip" app:layout_constraintBottom_toBottomOf="@id/iv_audio" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="@id/iv_audio" />
        <com.google.android.material.imageview.ShapeableImageView android:id="@id/ivSubjectCover" android:background="@color/bg_01" android:layout_width="40.0dip" android:layout_height="40.0dip" android:layout_marginTop="12.0dip" android:layout_marginBottom="12.0dip" android:scaleType="centerCrop" android:layout_marginStart="12.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/iv_cover" app:shapeAppearanceOverlay="@style/roundStyle_4" />
        <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/text_01" android:ellipsize="end" android:id="@id/tvSubject" android:visibility="visible" android:layout_width="0.0dip" android:layout_marginBottom="3.0dip" android:maxLines="1" android:textAlignment="viewStart" android:layout_marginEnd="4.0dip" app:layout_constraintBottom_toTopOf="@id/tvSubjectYear" app:layout_constraintEnd_toStartOf="@id/llDownload" app:layout_constraintStart_toStartOf="@id/tvSubjectYear" style="@style/style_medium_text" />
        <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/text_03" android:ellipsize="end" android:gravity="start|center" android:id="@id/tvSubjectYear" android:visibility="visible" android:layout_width="0.0dip" android:maxLines="1" android:drawablePadding="4.0dip" android:textAlignment="viewStart" android:layout_marginStart="8.0dip" android:layout_marginEnd="8.0dip" app:layout_constraintBottom_toBottomOf="@id/ivSubjectCover" app:layout_constraintEnd_toStartOf="@id/llDownload" app:layout_constraintStart_toEndOf="@id/ivSubjectCover" style="@style/style_regula_bigger_text" />
        <com.transsnet.downloader.widget.DownloadView android:gravity="center_vertical" android:id="@id/llDownload" android:background="@drawable/bg_btn_01" android:layout_width="88.0dip" android:layout_height="32.0dip" android:paddingStart="8.0dip" android:paddingEnd="8.0dip" android:layout_marginEnd="12.0dip" app:layout_constraintBottom_toBottomOf="@id/ivSubjectCover" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="@id/ivSubjectCover" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.cardview.widget.CardView>
