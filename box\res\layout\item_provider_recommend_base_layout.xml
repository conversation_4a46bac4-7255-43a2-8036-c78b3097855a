<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView android:background="@drawable/post_bg_module_01_8dp" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginStart="16.0dip" android:layout_marginEnd="16.0dip" app:cardCornerRadius="8.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="fill_parent">
        <com.transsion.ninegridview.video.NineGridVideoView android:id="@id/nine_grid" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginBottom="6.0dip" app:enable_click="false" app:layout_constraintBottom_toTopOf="@id/tvSubject" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
        <androidx.appcompat.widget.AppCompatImageView android:id="@id/ivGaussianBg" android:background="@drawable/post_detail_bg_0_0" android:layout_width="0.0dip" android:layout_height="40.0dip" app:layout_constraintBottom_toBottomOf="@id/nine_grid" app:layout_constraintEnd_toEndOf="@id/nine_grid" app:layout_constraintStart_toStartOf="@id/nine_grid" />
        <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/white" android:id="@id/tvTime" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginBottom="6.0dip" android:layout_marginEnd="6.0dip" app:layout_constraintBottom_toBottomOf="@id/nine_grid" app:layout_constraintEnd_toEndOf="@id/nine_grid" />
        <androidx.appcompat.widget.AppCompatImageView android:id="@id/ivCenterIcon" android:layout_width="48.0dip" android:layout_height="48.0dip" android:minWidth="30.0dip" android:minHeight="30.0dip" app:layout_constraintBottom_toBottomOf="@id/nine_grid" app:layout_constraintEnd_toEndOf="@id/nine_grid" app:layout_constraintStart_toStartOf="@id/nine_grid" app:layout_constraintTop_toTopOf="@id/nine_grid" />
        <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/text_01" android:ellipsize="end" android:id="@id/tvSubject" android:visibility="visible" android:layout_width="0.0dip" android:layout_marginBottom="3.0dip" android:maxLines="1" android:textAlignment="viewStart" android:layout_marginEnd="4.0dip" app:layout_constraintBottom_toTopOf="@id/tvSubjectYear" app:layout_constraintEnd_toStartOf="@id/llDownload" app:layout_constraintStart_toStartOf="@id/tvSubjectYear" style="@style/style_medium_text" />
        <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/text_03" android:ellipsize="end" android:gravity="start|center" android:id="@id/tvSubjectYear" android:visibility="visible" android:layout_width="0.0dip" android:maxLines="1" android:drawablePadding="4.0dip" android:textAlignment="viewStart" android:layout_marginStart="8.0dip" android:layout_marginEnd="8.0dip" app:layout_constraintBottom_toBottomOf="@id/ivSubjectCover" app:layout_constraintEnd_toStartOf="@id/llDownload" app:layout_constraintStart_toEndOf="@id/ivSubjectCover" style="@style/style_regula_bigger_text" />
        <com.google.android.material.imageview.ShapeableImageView android:id="@id/ivSubjectCover" android:background="@color/bg_01" android:layout_width="48.0dip" android:layout_height="64.0dip" android:scaleType="centerCrop" android:layout_marginStart="12.0dip" app:layout_constraintBottom_toBottomOf="@id/llDownload" app:layout_constraintStart_toStartOf="parent" app:shapeAppearanceOverlay="@style/roundStyle_4" />
        <com.transsnet.downloader.widget.DownloadView android:gravity="center_vertical" android:id="@id/llDownload" android:background="@drawable/bg_btn_01" android:layout_width="88.0dip" android:layout_height="32.0dip" android:layout_marginBottom="12.0dip" android:paddingStart="8.0dip" android:paddingEnd="8.0dip" android:layout_marginEnd="12.0dip" app:layout_constraintBottom_toTopOf="@id/tvTitle" app:layout_constraintEnd_toEndOf="parent" />
        <androidx.appcompat.widget.AppCompatTextView android:textSize="14.0sp" android:textColor="@color/text_02" android:ellipsize="end" android:id="@id/tvTitle" android:visibility="visible" android:layout_width="0.0dip" android:layout_marginBottom="12.0dip" android:maxLines="1" android:textAlignment="viewStart" android:layout_marginStart="12.0dip" android:layout_marginEnd="12.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" style="@style/style_regular_text" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.cardview.widget.CardView>
