<?xml version="1.0" encoding="utf-8"?>
<merge android:paddingBottom="4.0dip" android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <View android:id="@id/v_header_click_hot_zone" android:layout_width="0.0dip" android:layout_height="0.0dip" app:layout_constraintBottom_toBottomOf="@id/fl_cover" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <FrameLayout android:id="@id/fl_cover" android:layout_width="36.0dip" android:layout_height="36.0dip" android:layout_marginTop="12.0dip" android:layout_marginStart="12.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent">
        <com.google.android.material.imageview.ShapeableImageView android:id="@id/iv_room_cover" android:layout_width="36.0dip" android:layout_height="36.0dip" android:src="@drawable/ic_group_def_bg" android:scaleType="centerCrop" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:shapeAppearanceOverlay="@style/roundStyle_4" />
        <com.noober.background.view.BLView android:id="@id/v_room_cover_stroke" android:layout_width="36.0dip" android:layout_height="36.0dip" app:bl_corners_radius="4.0dip" app:bl_stroke_color="@color/white_10" app:bl_stroke_width="1.0dip" />
        <com.google.android.material.imageview.ShapeableImageView android:id="@id/iv_user_avatar_2" android:background="@color/gray_dark_20" android:visibility="gone" android:layout_width="36.0dip" android:layout_height="36.0dip" android:scaleType="centerCrop" app:shapeAppearanceOverlay="@style/circle_style" />
        <com.noober.background.view.BLView android:id="@id/v_user_avatar_2_stroke" android:visibility="gone" android:layout_width="36.0dip" android:layout_height="36.0dip" app:bl_corners_radius="18.0dip" app:bl_stroke_color="@color/white_10" app:bl_stroke_width="1.0dip" />
    </FrameLayout>
    <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textColor="@color/white_80" android:ellipsize="end" android:id="@id/tv_room_name" android:layout_width="wrap_content" android:layout_height="wrap_content" android:lines="1" android:includeFontPadding="false" android:layout_marginStart="12.0dip" app:layout_constrainedWidth="true" app:layout_constraintEnd_toStartOf="@id/tv_post_date" app:layout_constraintHorizontal_bias="0.0" app:layout_constraintHorizontal_chainStyle="packed" app:layout_constraintStart_toEndOf="@id/fl_cover" app:layout_constraintTop_toTopOf="@id/fl_cover" style="@style/style_medium_text" />
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/white_60" android:id="@id/tv_post_date" android:layout_width="wrap_content" android:layout_height="wrap_content" android:includeFontPadding="false" android:layout_marginStart="4.0dip" android:layout_marginEnd="12.0dip" app:layout_constraintBottom_toBottomOf="@id/tv_room_name" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/tv_room_name" app:layout_constraintTop_toTopOf="@id/tv_room_name" app:layout_goneMarginStart="12.0dip" style="@style/style_regula_bigger_text" />
    <com.google.android.material.imageview.ShapeableImageView android:id="@id/iv_user_avatar" android:background="@color/gray_dark_20" android:layout_width="16.0dip" android:layout_height="16.0dip" android:layout_marginTop="8.0dip" android:scaleType="centerCrop" app:layout_constraintStart_toStartOf="@id/tv_room_name" app:layout_constraintTop_toBottomOf="@id/tv_room_name" app:shapeAppearanceOverlay="@style/circle_style" />
    <View android:id="@id/v_location_line" android:background="@color/white_20" android:visibility="gone" android:layout_width="1.0dip" android:layout_height="8.0dip" android:layout_marginStart="4.0dip" app:layout_constraintBottom_toBottomOf="@id/iv_user_avatar" app:layout_constraintStart_toEndOf="@id/iv_user_avatar" app:layout_constraintTop_toTopOf="@id/iv_user_avatar" style="@style/style_import_text" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_location_icon" android:visibility="gone" android:layout_width="14.0dip" android:layout_height="14.0dip" android:scaleType="center" android:tint="@color/white_60" android:layout_marginStart="4.0dip" app:layout_constraintBottom_toBottomOf="@id/tv_user_name_or_location" app:layout_constraintStart_toEndOf="@id/v_location_line" app:layout_constraintTop_toTopOf="@id/tv_user_name_or_location" app:layout_goneMarginStart="0.0dip" app:srcCompat="@drawable/ic_location" />
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/white_60" android:ellipsize="end" android:gravity="start|center" android:id="@id/tv_user_name_or_location" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginTop="8.0dip" android:maxLines="1" android:includeFontPadding="false" android:textAlignment="viewStart" android:layout_marginEnd="12.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/iv_location_icon" app:layout_constraintTop_toBottomOf="@id/tv_room_name" app:layout_goneMarginStart="4.0dip" style="@style/style_regula_bigger_text" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textColor="@color/white_80" android:gravity="start" android:id="@id/tv_post_content" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginTop="8.0dip" android:includeFontPadding="false" android:textAlignment="viewStart" android:layout_marginEnd="12.0dip" app:layout_constrainedWidth="true" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="@id/tv_room_name" app:layout_constraintTop_toBottomOf="@id/tv_user_name_or_location" style="@style/style_regular_text" />
    <FrameLayout android:id="@id/fl_content" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginLeft="9.0dip" android:layout_marginTop="8.0dip" android:layout_marginRight="9.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/fl_cover" app:layout_constraintTop_toBottomOf="@id/tv_post_content">
        <com.tn.lib.view.NoScrollRecyclerView2 android:id="@id/recycler_view_post" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginLeft="3.0dip" android:layout_marginRight="3.0dip" />
        <include android:id="@id/layout_content_video" android:visibility="gone" layout="@layout/layout_room_post_content_video" />
    </FrameLayout>
    <include android:id="@id/layout_bottom_module" android:layout_width="0.0dip" android:layout_height="40.0dip" android:layout_marginLeft="12.0dip" android:layout_marginTop="10.0dip" android:layout_marginRight="12.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/fl_cover" app:layout_constraintTop_toBottomOf="@id/fl_content" layout="@layout/layout_post_item_bottom_module" />
    <View android:id="@id/v_post_like" android:layout_width="0.0dip" android:layout_height="40.0dip" app:layout_constraintEnd_toStartOf="@id/v_post_comment" app:layout_constraintStart_toEndOf="@id/fl_cover" app:layout_constraintTop_toBottomOf="@id/layout_bottom_module" />
    <View android:id="@id/v_post_comment" android:layout_width="0.0dip" android:layout_height="40.0dip" app:layout_constraintEnd_toStartOf="@id/v_post_share" app:layout_constraintStart_toEndOf="@id/v_post_like" app:layout_constraintTop_toTopOf="@id/v_post_like" />
    <View android:id="@id/v_post_share" android:layout_width="0.0dip" android:layout_height="40.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/v_post_comment" app:layout_constraintTop_toTopOf="@id/v_post_like" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textColor="@color/white_60" android:ellipsize="end" android:gravity="center_vertical" android:id="@id/tv_post_like" android:layout_width="0.0dip" android:layout_height="wrap_content" android:maxLines="1" android:includeFontPadding="false" android:drawablePadding="4.0dip" android:layout_marginStart="12.0dip" app:drawableStartCompat="@drawable/ic_post_like" app:layout_constrainedWidth="true" app:layout_constraintBottom_toBottomOf="@id/v_post_like" app:layout_constraintStart_toStartOf="@id/v_post_like" app:layout_constraintTop_toTopOf="@id/v_post_like" style="@style/style_regula_bigger_text" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textColor="@color/white_60" android:ellipsize="end" android:gravity="center_vertical" android:id="@id/tv_post_comment" android:layout_width="wrap_content" android:layout_height="wrap_content" android:maxLines="1" android:includeFontPadding="false" android:drawablePadding="4.0dip" android:layout_marginStart="12.0dip" app:drawableStartCompat="@drawable/ic_post_comment" app:layout_constrainedWidth="true" app:layout_constraintBottom_toBottomOf="@id/v_post_comment" app:layout_constraintEnd_toEndOf="@id/v_post_comment" app:layout_constraintStart_toStartOf="@id/v_post_comment" app:layout_constraintTop_toTopOf="@id/v_post_comment" style="@style/style_regula_bigger_text" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_post_share" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginEnd="12.0dip" app:layout_constraintBottom_toBottomOf="@id/v_post_share" app:layout_constraintEnd_toEndOf="@id/v_post_share" app:layout_constraintTop_toTopOf="@id/v_post_share" app:srcCompat="@drawable/ic_post_share" />
    <View android:id="@id/v_bottom_line" android:background="@color/line_01" android:layout_width="fill_parent" android:layout_height="1.0dip" android:layout_marginLeft="12.0dip" android:layout_marginTop="6.0dip" android:layout_marginRight="12.0dip" app:layout_constraintTop_toBottomOf="@id/v_post_like" />
</merge>
