<?xml version="1.0" encoding="utf-8"?>
<FrameLayout android:layout_width="fill_parent" android:layout_height="106.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <RelativeLayout android:id="@id/rl_add" android:background="@drawable/add_image_bg_radius" android:layout_width="fill_parent" android:layout_height="106.0dip">
        <ImageView android:id="@id/iv_add" android:layout_width="16.0dip" android:layout_height="16.0dip" android:layout_marginTop="32.0dip" android:src="@drawable/post_image_add_icon" android:layout_centerHorizontal="true" />
        <TextView android:textSize="16.0sp" android:textColor="@color/white_80" android:id="@id/tv_number" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="8.0dip" android:text="0/9" android:layout_below="@id/iv_add" android:layout_centerHorizontal="true" style="@style/style_regular_text" />
    </RelativeLayout>
</FrameLayout>
