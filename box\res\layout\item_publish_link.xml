<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <LinearLayout android:orientation="horizontal" android:id="@id/layout" android:background="@drawable/bg_link_radius" android:layout_width="fill_parent" android:layout_height="42.0dip" android:layout_marginBottom="8.0dip">
        <ImageView android:layout_gravity="center" android:id="@id/iv_cover" android:layout_width="42.0dip" android:layout_height="42.0dip" android:src="@drawable/ic_link_urls" android:layout_marginEnd="8.0dip" />
        <RelativeLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="fill_parent" android:layout_weight="1.0">
            <TextView android:textSize="14.0dip" android:textColor="#ff333333" android:ellipsize="end" android:id="@id/tv_title" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="Title...................................." android:maxLines="1" android:singleLine="true" android:layout_centerVertical="true" />
            <TextView android:textSize="10.0dip" android:textColor="#ff666666" android:id="@id/tv_desc" android:visibility="gone" android:layout_width="190.0dip" android:layout_height="11.0dip" android:text="des" android:layout_below="@id/tv_title" />
        </RelativeLayout>
        <FrameLayout android:layout_gravity="center" android:id="@id/rl_close" android:visibility="visible" android:layout_width="wrap_content" android:layout_height="fill_parent" android:paddingStart="9.0dip" android:paddingEnd="9.0dip">
            <ImageView android:layout_gravity="center" android:id="@id/iv_close" android:layout_width="14.0dip" android:layout_height="14.0dip" android:src="@drawable/ic_link_close" />
        </FrameLayout>
    </LinearLayout>
</LinearLayout>
