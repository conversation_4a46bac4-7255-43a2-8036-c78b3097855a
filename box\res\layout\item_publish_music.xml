<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <LinearLayout android:orientation="horizontal" android:background="@drawable/bg_link_radius" android:layout_width="fill_parent" android:layout_height="42.0dip" android:layout_marginBottom="8.0dip">
        <RelativeLayout android:layout_width="42.0dip" android:layout_height="42.0dip" android:layout_marginEnd="8.0dip">
            <ImageView android:id="@id/iv_cover" android:layout_width="42.0dip" android:layout_height="42.0dip" android:src="@drawable/ic_default_audio" />
            <ImageView android:id="@id/iv_play" android:layout_width="10.0dip" android:layout_height="10.0dip" android:src="@drawable/ic_audio_play" android:layout_alignParentBottom="true" android:layout_alignParentEnd="true" />
        </RelativeLayout>
        <LinearLayout android:gravity="start|center" android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="fill_parent" android:layout_weight="1.0">
            <TextView android:textSize="14.0dip" android:textColor="#ff333333" android:ellipsize="end" android:id="@id/tv_title" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="Title...................................." android:maxLines="1" android:singleLine="true" />
            <TextView android:textSize="10.0dip" android:textColor="#ff666666" android:ellipsize="end" android:id="@id/tv_desc" android:visibility="visible" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="2.0dip" android:text="des" android:singleLine="true" android:layout_below="@id/tv_title" />
        </LinearLayout>
        <FrameLayout android:layout_gravity="center" android:id="@id/rl_close" android:layout_width="wrap_content" android:layout_height="fill_parent" android:paddingStart="9.0dip" android:paddingEnd="9.0dip">
            <ImageView android:layout_gravity="center" android:id="@id/iv_close" android:layout_width="14.0dip" android:layout_height="14.0dip" android:src="@drawable/ic_link_close" />
        </FrameLayout>
    </LinearLayout>
</LinearLayout>
