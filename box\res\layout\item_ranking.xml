<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:id="@id/item_root" android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.google.android.material.imageview.ShapeableImageView android:id="@id/iv_cover" android:layout_width="fill_parent" android:layout_height="153.0dip" android:scaleType="centerCrop" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:shapeAppearanceOverlay="@style/roundStyle_4" />
    <View android:background="@drawable/home_mask_ranking" android:layout_width="fill_parent" android:layout_height="48.0dip" app:layout_constraintBottom_toBottomOf="@id/iv_cover" />
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/color_score" android:id="@id/tv_score" android:visibility="gone" android:layout_marginBottom="8.0dip" android:shadowColor="@color/black_30" android:shadowRadius="3.0" android:layout_marginEnd="8.0dip" app:layout_constraintBottom_toBottomOf="@id/iv_cover" app:layout_constraintEnd_toEndOf="parent" style="@style/style_medium_text" />
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/text_01" android:ellipsize="end" android:id="@id/tv_title" android:layout_marginTop="6.0dip" android:maxLines="1" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/iv_cover" style="@style/style_medium_text" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="10.0sp" android:textColor="@color/rank_tag_color" android:ellipsize="end" android:id="@id/tv_tag" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="4.0dip" android:maxLines="1" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tv_title" style="@style/robot_medium" />
</androidx.constraintlayout.widget.ConstraintLayout>
