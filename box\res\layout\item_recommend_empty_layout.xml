<?xml version="1.0" encoding="utf-8"?>
<androidx.appcompat.widget.LinearLayoutCompat android:orientation="vertical" android:padding="16.0dip" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <TextView android:textSize="16.0sp" android:textColor="@color/base_color_333333" android:layout_width="fill_parent" android:layout_height="wrap_content" android:text="@string/for_you" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <include layout="@layout/default_item_empty_layout" />
    <include layout="@layout/default_item_empty_layout" />
    <include layout="@layout/default_item_empty_layout" />
    <include layout="@layout/default_item_empty_layout" />
    <include layout="@layout/default_item_empty_layout" />
    <include layout="@layout/default_item_empty_layout" />
    <include layout="@layout/default_item_empty_layout" />
    <include layout="@layout/default_item_empty_layout" />
</androidx.appcompat.widget.LinearLayoutCompat>
