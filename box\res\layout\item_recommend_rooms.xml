<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:id="@id/cl_root" android:layout_width="104.0dip" android:layout_height="104.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.noober.background.view.BLView android:layout_width="fill_parent" android:layout_height="fill_parent" app:bl_corners_radius="8.0dip" app:bl_solid_color="@color/white_6" />
    <com.google.android.material.imageview.ShapeableImageView android:id="@id/iv_cover_bg" android:layout_width="fill_parent" android:layout_height="fill_parent" android:scaleType="centerCrop" android:alpha="0.16" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:shapeAppearance="@style/roundStyle_8" />
    <com.noober.background.view.BLView android:id="@id/v_stroke" android:layout_width="fill_parent" android:layout_height="fill_parent" app:bl_corners_radius="8.0dip" app:bl_stroke_color="@color/white_10" app:bl_stroke_width="1.0dip" />
    <com.google.android.material.imageview.ShapeableImageView android:id="@id/iv_cover" android:layout_width="30.0dip" android:layout_height="30.0dip" android:layout_marginTop="8.5dip" android:scaleType="centerCrop" android:layout_marginStart="8.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:shapeAppearance="@style/roundStyle_4" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="13.0sp" android:textColor="@color/white_80" android:id="@id/tv_members" android:layout_width="0.0dip" android:layout_marginTop="8.5dip" android:includeFontPadding="false" android:layout_marginStart="4.0dip" android:layout_marginEnd="8.0dip" app:drawableStartCompat="@drawable/ic_room_member" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent" style="@style/style_regular_text" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="13.0sp" android:textColor="@color/white_80" android:ellipsize="end" android:id="@id/tv_title" android:layout_width="0.0dip" android:layout_marginTop="4.0dip" android:maxLines="2" android:includeFontPadding="false" android:layout_marginEnd="8.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintHorizontal_bias="1.0" app:layout_constraintStart_toStartOf="@id/iv_cover" app:layout_constraintTop_toBottomOf="@id/iv_cover" app:layout_constraintVertical_chainStyle="packed" style="@style/style_medium_small_text" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="13.0sp" android:textColor="@color/white_60" android:ellipsize="end" android:gravity="start" android:id="@id/tv_tag" android:layout_width="0.0dip" android:maxLines="1" android:includeFontPadding="false" android:textAlignment="viewStart" app:layout_constraintBottom_toBottomOf="@id/v_join" app:layout_constraintEnd_toStartOf="@id/v_join" app:layout_constraintStart_toStartOf="@id/iv_cover" app:layout_constraintTop_toTopOf="@id/v_join" style="@style/style_regular_text" />
    <com.tn.lib.view.RoomJoinAnimationView android:id="@id/v_join" android:layout_width="24.0dip" android:layout_height="24.0dip" android:layout_marginBottom="5.0dip" android:layout_marginEnd="5.0dip" app:jv_style="image" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>
