<?xml version="1.0" encoding="utf-8"?>
<com.noober.background.view.BLFrameLayout android:id="@id/fl_root" android:layout_width="fill_parent" android:layout_height="48.0dip" app:bl_selected_solid_color="@color/bg_01" app:bl_unSelected_solid_color="@color/module_04"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <View android:id="@id/v_selected" android:background="@color/main" android:layout_width="3.0dip" android:layout_height="fill_parent" />
    <com.noober.background.view.BLTextView android:ellipsize="end" android:layout_gravity="center_vertical" android:id="@id/tv_title" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginLeft="12.0dip" android:layout_marginRight="12.0dip" android:maxLines="2" app:bl_selected_textColor="@color/main" app:bl_unSelected_textColor="@color/white_60" style="@style/style_regula_bigger_text" />
</com.noober.background.view.BLFrameLayout>
