<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:id="@id/clLayout" android:paddingTop="12.0dip" android:layout_width="fill_parent" android:layout_height="wrap_content" android:paddingStart="12.0dip" android:paddingEnd="12.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.google.android.material.imageview.ShapeableImageView android:id="@id/iv_avatar" android:layout_width="32.0dip" android:layout_height="32.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:shapeAppearance="@style/circle_style" />
    <View android:id="@id/view_red_blank" android:background="@drawable/bg_red_notice" android:visibility="gone" android:layout_width="6.0dip" android:layout_height="6.0dip" app:layout_constraintEnd_toEndOf="@id/iv_avatar" app:layout_constraintTop_toTopOf="@id/iv_avatar" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textColor="@color/white_80" android:ellipsize="end" android:gravity="start|center" android:id="@id/tv_avatar" android:layout_width="0.0dip" android:layout_height="wrap_content" android:maxLines="1" android:includeFontPadding="false" android:textAlignment="viewStart" android:layout_marginStart="12.0dip" android:layout_marginEnd="12.0dip" app:layout_constrainedWidth="true" app:layout_constraintEnd_toStartOf="@id/iv_comment" app:layout_constraintStart_toEndOf="@id/iv_avatar" app:layout_constraintTop_toTopOf="parent" style="@style/style_medium_text" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="14.0sp" android:textColor="@color/white_60" android:ellipsize="end" android:id="@id/tv_date" android:layout_width="0.0dip" android:layout_height="wrap_content" android:maxLines="1" android:layout_marginStart="12.0dip" android:layout_marginEnd="12.0dip" app:layout_constraintEnd_toStartOf="@id/iv_comment" app:layout_constraintStart_toEndOf="@id/iv_avatar" app:layout_constraintTop_toBottomOf="@id/tv_avatar" style="@style/style_regular_text" />
    <com.google.android.material.imageview.ShapeableImageView android:id="@id/iv_comment" android:layout_width="51.0dip" android:layout_height="34.0dip" android:scaleType="centerCrop" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent" app:shapeAppearance="@style/corner_style_2" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textColor="@color/white_80" android:ellipsize="end" android:gravity="start|center" android:id="@id/tv_content" android:visibility="gone" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginTop="6.0dip" android:maxLines="2" android:includeFontPadding="false" android:textAlignment="viewStart" android:layout_marginStart="12.0dip" app:layout_constrainedWidth="true" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/iv_avatar" app:layout_constraintTop_toBottomOf="@id/tv_date" style="@style/style_regular_text" />
    <com.google.android.material.imageview.ShapeableImageView android:id="@id/comment_cover" android:visibility="gone" android:layout_width="100.0dip" android:layout_height="100.0dip" android:layout_marginTop="6.0dip" android:scaleType="centerCrop" android:layout_marginStart="12.0dip" app:layout_constraintStart_toEndOf="@id/iv_avatar" app:layout_constraintTop_toBottomOf="@id/tv_content" app:shapeAppearanceOverlay="@style/ImgRoundedStyle_4dp" />
    <View android:id="@id/line" android:background="@color/white_20" android:layout_width="fill_parent" android:layout_height="1.0dip" android:layout_marginTop="8.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/comment_cover" />
</androidx.constraintlayout.widget.ConstraintLayout>
