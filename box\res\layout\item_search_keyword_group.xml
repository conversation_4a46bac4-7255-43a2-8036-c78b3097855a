<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout android:layout_width="wrap_content" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/text_01" android:ellipsize="end" android:gravity="start|center" android:id="@id/tv_keyword" android:background="@drawable/bg_search_history" android:paddingLeft="@dimen/dp_8" android:paddingTop="@dimen/dp_4" android:paddingRight="@dimen/dp_8" android:paddingBottom="@dimen/dp_4" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="@dimen/dp_8" android:maxWidth="156.0dip" android:maxLines="1" android:singleLine="true" android:layout_centerVertical="true" android:layout_marginEnd="@dimen/dp_8" style="@style/style_regula_bigger_text" />
</RelativeLayout>
