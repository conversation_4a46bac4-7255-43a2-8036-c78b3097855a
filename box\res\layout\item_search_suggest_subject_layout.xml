<?xml version="1.0" encoding="utf-8"?>
<FrameLayout android:id="@id/item_root" android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.appcompat.widget.LinearLayoutCompat android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="wrap_content">
        <View android:id="@id/divider" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="16.0dip" />
        <androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="112.0dip">
            <com.google.android.material.imageview.ShapeableImageView android:id="@id/iv_cover" android:layout_width="72.0dip" android:layout_height="96.0dip" android:src="@mipmap/ic_default_video" android:scaleType="centerCrop" android:layout_marginStart="20.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:shapeAppearanceOverlay="@style/corner_style_4" />
            <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textColor="@color/text_01" android:ellipsize="end" android:id="@id/tv_title" android:layout_width="0.0dip" android:layout_height="wrap_content" android:text="@string/no_network" android:lines="1" android:textAlignment="viewStart" android:layout_marginStart="8.0dip" android:layout_marginEnd="16.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/iv_cover" app:layout_constraintTop_toTopOf="@id/iv_cover" style="@style/style_medium_text" />
            <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/text_03" android:ellipsize="end" android:id="@id/tv_desc" android:visibility="visible" android:layout_width="0.0dip" android:layout_marginTop="4.0dip" android:maxLines="2" android:drawablePadding="4.0dip" android:textAlignment="viewStart" android:layout_marginStart="8.0dip" android:layout_marginEnd="16.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/iv_cover" app:layout_constraintTop_toBottomOf="@id/tv_title" style="@style/style_regular_text" />
            <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/yellow_dark_70" android:id="@id/tv_score" android:layout_marginBottom="10.0dip" android:drawablePadding="2.0dip" android:drawableStart="@drawable/ic_category_star" android:layout_marginStart="8.0dip" app:layout_constraintBottom_toBottomOf="@id/iv_cover" app:layout_constraintStart_toEndOf="@id/iv_cover" style="@style/style_medium_text" />
            <com.transsnet.downloader.widget.DownloadView android:gravity="center_vertical" android:id="@id/ll_download" android:background="@drawable/bg_btn_01" android:layout_width="wrap_content" android:layout_height="28.0dip" android:minWidth="88.0dip" android:layout_marginEnd="16.0dip" app:layout_constraintBottom_toBottomOf="@id/tv_score" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="@id/tv_score" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.appcompat.widget.LinearLayoutCompat>
</FrameLayout>
