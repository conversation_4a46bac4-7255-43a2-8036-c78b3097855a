<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.google.android.material.imageview.ShapeableImageView android:id="@id/ivCover" android:layout_width="72.0dip" android:layout_height="96.0dip" android:layout_marginBottom="16.0dip" android:scaleType="centerCrop" android:layout_marginStart="16.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:shapeAppearance="@style/roundStyle_4" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/ivRight" android:layout_width="16.0dip" android:layout_height="16.0dip" android:src="@drawable/ic_arrow_right_l_n" android:layout_marginEnd="16.0dip" app:layout_constraintBottom_toBottomOf="@id/ivCover" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="@id/ivCover" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="14.0sp" android:textColor="@color/text_01" android:ellipsize="end" android:id="@id/tvTitle" android:layout_width="0.0dip" android:layout_height="wrap_content" android:maxLines="1" android:layout_marginStart="8.0dip" android:layout_marginEnd="16.0dip" app:layout_constraintBottom_toTopOf="@id/tvDes" app:layout_constraintEnd_toStartOf="@id/ivRight" app:layout_constraintStart_toEndOf="@id/ivCover" app:layout_constraintTop_toTopOf="@id/ivCover" app:layout_constraintVertical_chainStyle="packed" style="@style/style_medium_text" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/text_03" android:ellipsize="end" android:id="@id/tvDes" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginTop="6.0dip" android:maxLines="1" app:layout_constraintBottom_toTopOf="@id/tvWorks" app:layout_constraintEnd_toEndOf="@id/tvTitle" app:layout_constraintStart_toStartOf="@id/tvTitle" app:layout_constraintTop_toBottomOf="@id/tvTitle" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/text_03" android:ellipsize="end" android:id="@id/tvWorks" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginTop="6.0dip" android:maxLines="1" app:layout_constraintBottom_toBottomOf="@id/ivCover" app:layout_constraintEnd_toEndOf="@id/tvTitle" app:layout_constraintStart_toStartOf="@id/tvTitle" app:layout_constraintTop_toBottomOf="@id/tvDes" />
</androidx.constraintlayout.widget.ConstraintLayout>
