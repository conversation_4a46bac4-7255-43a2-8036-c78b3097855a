<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:id="@id/rvLinkage" android:focusable="true" android:clickable="true" android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <View android:id="@id/grad2" android:background="@drawable/bg_association_grad2" android:layout_width="60.0dip" android:layout_height="80.0dip" android:layout_marginStart="28.0dip" app:layout_constraintBottom_toBottomOf="@id/ivCover" app:layout_constraintStart_toStartOf="@id/ivCover" />
    <View android:id="@id/grad1" android:background="@drawable/bg_association_grad1" android:layout_width="66.0dip" android:layout_height="88.0dip" android:layout_marginStart="14.0dip" app:layout_constraintBottom_toBottomOf="@id/ivCover" app:layout_constraintStart_toStartOf="@id/ivCover" />
    <com.google.android.material.imageview.ShapeableImageView android:id="@id/ivCover" android:layout_width="72.0dip" android:layout_height="96.0dip" android:layout_marginBottom="16.0dip" android:src="@drawable/ic_list_cover" android:scaleType="centerCrop" android:layout_marginStart="16.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" app:shapeAppearance="@style/roundStyle_4" />
    <TextView android:textSize="18.0sp" android:textColor="@color/text_01" android:id="@id/tvTitle" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginStart="8.0dip" android:layout_marginEnd="12.0dip" app:layout_constraintEnd_toStartOf="@id/ivRight" app:layout_constraintStart_toEndOf="@id/grad2" app:layout_constraintTop_toTopOf="@id/grad2" style="@style/style_title_text" />
    <TextView android:ellipsize="end" android:id="@id/tvSubTitle" android:layout_width="0.0dip" android:layout_height="wrap_content" android:lines="2" app:layout_constraintEnd_toEndOf="@id/tvTitle" app:layout_constraintStart_toStartOf="@id/tvTitle" app:layout_constraintTop_toBottomOf="@id/tvTitle" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/ivRight" android:layout_width="16.0dip" android:layout_height="16.0dip" android:src="@drawable/ic_arrow_right_l_n" android:layout_marginEnd="16.0dip" app:layout_constraintBottom_toBottomOf="@id/ivCover" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="@id/ivCover" />
</androidx.constraintlayout.widget.ConstraintLayout>
