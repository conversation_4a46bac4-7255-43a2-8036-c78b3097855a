<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout android:layout_width="wrap_content" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <ImageView android:id="@id/sv_item_cover" android:layout_width="fill_parent" android:layout_height="fill_parent" android:scaleType="centerCrop" />
    <RelativeLayout android:id="@id/rl_select" android:paddingTop="6.0dip" android:layout_width="36.0dip" android:layout_height="36.0dip" android:paddingEnd="6.0dip" android:layout_alignParentEnd="true">
        <TextView android:textSize="@dimen/text_size_12" android:textColor="#ff191f2b" android:gravity="center" android:id="@id/tv_select" android:background="@drawable/bg_linear_r16" android:layout_width="20.0dip" android:layout_height="20.0dip" android:includeFontPadding="false" android:layout_alignParentEnd="true" style="@style/robot_medium" />
    </RelativeLayout>
    <TextView android:textColor="#ffeeeeee" android:layout_gravity="bottom" android:id="@id/sv_item_duration" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_margin="4.0dip" />
    <View android:id="@id/view_masking" android:background="@color/black_60" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="fill_parent" />
</RelativeLayout>
