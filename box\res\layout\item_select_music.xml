<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout android:layout_width="fill_parent" android:layout_height="72.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <LinearLayout android:paddingTop="12.0dip" android:layout_width="fill_parent" android:layout_height="wrap_content" android:paddingStart="16.0dip" android:paddingEnd="16.0dip">
        <RelativeLayout android:layout_width="48.0dip" android:layout_height="48.0dip" android:layout_marginEnd="8.0dip">
            <ImageView android:id="@id/sv_item_cover" android:layout_width="48.0dip" android:layout_height="48.0dip" android:src="@drawable/ic_default_audio" />
            <ImageView android:id="@id/iv_play" android:visibility="gone" android:layout_width="12.0dip" android:layout_height="12.0dip" android:src="@drawable/ic_audio_play" android:layout_alignParentBottom="true" android:layout_alignParentEnd="true" />
        </RelativeLayout>
        <LinearLayout android:gravity="start|center" android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="fill_parent" android:layout_weight="1.0">
            <TextView android:textSize="14.0dip" android:textColor="@color/base_color_333333" android:ellipsize="end" android:id="@id/tv_title" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="Title......" android:singleLine="true" />
            <TextView android:textSize="12.0dip" android:textColor="@color/base_color_999999" android:id="@id/tv_desc" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="desc" android:layout_below="@id/tv_title" />
        </LinearLayout>
        <TextView android:textSize="11.0dip" android:textColor="@color/white" android:gravity="center" android:layout_gravity="center" android:id="@id/tv_select" android:background="@drawable/ic_audio_select" android:visibility="visible" android:layout_width="20.0dip" android:layout_height="20.0dip" />
    </LinearLayout>
    <View android:id="@id/view_masking" android:background="@color/white" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="fill_parent" />
</RelativeLayout>
