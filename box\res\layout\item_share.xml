<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_gravity="top" android:layout_width="60.0dip" android:layout_height="wrap_content" android:layout_marginStart="0.0dip" android:layout_marginEnd="0.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <TextView android:textSize="10.0sp" android:textColor="@color/text_07" android:ellipsize="end" android:gravity="center" android:id="@id/tv_share" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/player_copy_link" android:maxLines="1" android:drawablePadding="5.0dip" android:textAllCaps="false" app:drawableTopCompat="@mipmap/player_ic_copy_link" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>
