<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:paddingTop="8.0dip" android:paddingBottom="8.0dip" android:layout_width="wrap_content" android:layout_height="wrap_content" android:minHeight="204.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.google.android.material.imageview.ShapeableImageView android:id="@id/iv_cover" android:layout_width="91.0dip" android:layout_height="130.0dip" android:scaleType="centerCrop" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:shapeAppearanceOverlay="@style/roundStyle_4" />
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/text_01" android:ellipsize="end" android:id="@id/tv_title" android:layout_width="0.0dip" android:layout_marginTop="8.0dip" android:maxLines="2" app:layout_constraintEnd_toEndOf="@id/iv_cover" app:layout_constraintStart_toStartOf="@id/iv_cover" app:layout_constraintTop_toBottomOf="@id/iv_cover" style="@style/style_medium_text" />
    <androidx.recyclerview.widget.RecyclerView android:id="@id/rv_list" android:visibility="gone" android:layout_width="0.0dip" android:layout_height="16.0dip" android:layout_marginTop="4.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="@id/iv_cover" app:layout_constraintTop_toBottomOf="@id/tv_title" />
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/text_02" android:id="@id/tv_ep" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginTop="4.0dip" app:layout_constraintEnd_toEndOf="@id/iv_cover" app:layout_constraintStart_toStartOf="@id/iv_cover" app:layout_constraintTop_toBottomOf="@id/rv_list" style="@style/style_regular_text" />
</androidx.constraintlayout.widget.ConstraintLayout>
