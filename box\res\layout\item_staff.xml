<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:id="@id/item_root" android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.google.android.material.imageview.ShapeableImageView android:id="@id/iv_cover" android:layout_width="fill_parent" android:layout_height="153.0dip" android:scaleType="centerCrop" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:shapeAppearanceOverlay="@style/roundStyle_8" />
    <com.tn.lib.widget.TnTextView android:textColor="@color/text_01" android:ellipsize="end" android:id="@id/tv_title" android:layout_marginTop="6.0dip" android:maxLines="1" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/iv_cover" style="@style/style_medium_text" />
</androidx.constraintlayout.widget.ConstraintLayout>
