<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <View android:id="@id/home_sub_pager_items_status" android:layout_width="fill_parent" android:layout_height="0.0dip" app:layout_constraintTop_toTopOf="parent" />
    <ImageView android:id="@id/home_sub_pager_item_image" android:background="@color/base_black_15_p" android:layout_width="fill_parent" android:layout_height="320.0dip" android:scaleType="centerCrop" app:layout_constraintTop_toBottomOf="@id/home_sub_pager_items_status" />
    <TextureView android:id="@id/home_sub_pager_item_texture" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="320.0dip" app:layout_constraintTop_toBottomOf="@id/home_sub_pager_items_status" />
    <ImageView android:id="@id/home_sub_pager_item_mute" android:layout_width="24.0dip" android:layout_height="24.0dip" android:layout_marginBottom="78.0dip" android:src="@mipmap/ad_volumeoff" android:scaleType="centerCrop" android:layout_marginEnd="8.0dip" app:layout_constraintBottom_toBottomOf="@id/home_sub_pager_item_texture" app:layout_constraintEnd_toEndOf="@id/home_sub_pager_item_texture" />
    <View android:id="@id/home_sub_pager_items_top_mask" android:layout_width="fill_parent" android:layout_height="110.0dip" app:layout_constraintTop_toBottomOf="@id/home_sub_pager_items_status" />
    <View android:background="@drawable/bg_home_sub_banner_background" android:layout_width="fill_parent" android:layout_height="120.0dip" app:layout_constraintBottom_toBottomOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>
