<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.transsion.baseui.widget.OperateScrollableHost android:id="@id/sub_operation_view_scroll_helper" android:layout_width="fill_parent" android:layout_height="wrap_content" app:layout_constraintTop_toTopOf="parent">
        <androidx.viewpager2.widget.ViewPager2 android:id="@id/sub_operation_view_pager" android:layout_width="fill_parent" android:layout_height="wrap_content" app:layout_constraintLeft_toLeftOf="parent" app:layout_constraintTop_toTopOf="parent" />
    </com.transsion.baseui.widget.OperateScrollableHost>
    <com.transsion.baseui.widget.OperateScrollableHost android:id="@id/sub_operation_card_helper" android:clipChildren="false" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layerType="software" app:layout_constraintBottom_toBottomOf="parent">
        <androidx.viewpager2.widget.ViewPager2 android:layout_gravity="bottom" android:id="@id/cardPager" android:clipChildren="false" android:layout_width="fill_parent" android:layout_height="66.0dip" android:layout_marginStart="12.0dip" android:layout_marginEnd="120.0dip" />
        <com.transsion.baseui.recycler.InterceptView android:layout_gravity="end" android:id="@id/interestLayout" android:layout_width="120.0dip" android:layout_height="66.0dip" />
    </com.transsion.baseui.widget.OperateScrollableHost>
</androidx.constraintlayout.widget.ConstraintLayout>
