<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <View android:id="@id/sub_operation_banner_space" android:layout_width="fill_parent" android:layout_height="80.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <View android:id="@id/sub_operation_banner_bg" android:layout_width="fill_parent" android:layout_height="wrap_content" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <com.transsion.baseui.widget.OperateScrollableHost android:id="@id/sub_operation_view_scroll_helper" android:clipChildren="false" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="8.0dip" android:layerType="software" app:layout_constraintTop_toBottomOf="@id/sub_operation_banner_space">
        <androidx.viewpager2.widget.ViewPager2 android:id="@id/sub_operation_horizontal_view_pager" android:clipChildren="false" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginLeft="48.0dip" android:layout_marginRight="48.0dip" app:layout_constraintLeft_toLeftOf="parent" app:layout_constraintTop_toTopOf="parent" />
    </com.transsion.baseui.widget.OperateScrollableHost>
</androidx.constraintlayout.widget.ConstraintLayout>
