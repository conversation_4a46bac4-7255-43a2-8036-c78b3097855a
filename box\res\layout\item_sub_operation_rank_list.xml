<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <RelativeLayout android:id="@id/sub_operation_ranklist_title_linear" android:paddingLeft="@dimen/dp_12" android:paddingRight="@dimen/dp_12" android:layout_width="fill_parent" android:layout_height="wrap_content" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent">
        <com.tn.lib.widget.TnTextView android:textSize="@dimen/dimens_sp_18" android:textColor="@color/text_01" android:id="@id/sub_operation_ranklist_title_text" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/education_add_course" style="@style/style_import_text" />
        <com.tn.lib.widget.TnTextView android:textColor="@color/text_02" android:ellipsize="end" android:maxWidth="68.0dip" android:text="@string/str_more" android:maxLines="1" android:includeFontPadding="false" android:layout_centerVertical="true" android:layout_alignParentEnd="true" app:drawableEndCompat="@mipmap/ic_arrow_right" app:drawableTint="@color/gray_0_60" style="@style/style_medium_text" />
    </RelativeLayout>
    <com.transsion.baseui.widget.OperateScrollableHost android:id="@id/sub_operation_ranklist_title_tab_layout" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="8.0dip" app:layout_constraintTop_toBottomOf="@id/sub_operation_ranklist_title_linear">
        <androidx.recyclerview.widget.RecyclerView android:id="@id/sub_operation_ranklist_title_tab_recycler" android:layout_width="wrap_content" android:layout_height="wrap_content" />
    </com.transsion.baseui.widget.OperateScrollableHost>
    <com.transsion.baseui.widget.OperateScrollableHost android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="8.0dip" app:layout_constraintTop_toBottomOf="@id/sub_operation_ranklist_title_tab_layout">
        <androidx.recyclerview.widget.RecyclerView android:id="@id/sub_operation_ranklist_recycler" android:layout_width="wrap_content" android:layout_height="wrap_content" />
    </com.transsion.baseui.widget.OperateScrollableHost>
</androidx.constraintlayout.widget.ConstraintLayout>
