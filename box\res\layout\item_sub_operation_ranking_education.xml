<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.google.android.material.imageview.ShapeableImageView android:id="@id/sub_operation_ranking_education_cover" android:layout_width="fill_parent" android:layout_height="92.0dip" android:scaleType="centerCrop" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:shapeAppearanceOverlay="@style/roundStyle_4" />
    <ImageView android:id="@id/sub_operation_ranking_education_add_icon" android:background="@drawable/ad_shape_circle" android:padding="4.0dip" android:layout_width="24.0dip" android:layout_height="24.0dip" android:layout_margin="8.0dip" android:src="@mipmap/ic_add" android:backgroundTint="@color/black_50" app:layout_constraintBottom_toBottomOf="@id/sub_operation_ranking_education_cover" app:layout_constraintEnd_toEndOf="@id/sub_operation_ranking_education_cover" />
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/text_01" android:ellipsize="end" android:id="@id/sub_operation_ranking_education_title" android:layout_marginTop="8.0dip" android:maxLines="2" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/sub_operation_ranking_education_cover" style="@style/style_medium_text" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/text_02" android:ellipsize="end" android:id="@id/sub_operation_ranking_education_tag" android:layout_width="wrap_content" android:layout_height="wrap_content" android:maxLines="1" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/sub_operation_ranking_education_title" style="@style/style_regular_text" />
</androidx.constraintlayout.widget.ConstraintLayout>
