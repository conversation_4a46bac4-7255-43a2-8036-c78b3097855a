<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <TextView android:textSize="@dimen/text_size_16" android:textColor="@color/pair_text_191F2B" android:id="@id/sub_operation_variable_title" android:layout_width="wrap_content" android:layout_height="wrap_content" android:paddingStart="@dimen/dimens_12" android:paddingEnd="@dimen/dimens_12" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" style="@style/robot_bold" />
    <com.transsion.baseui.widget.OperateScrollableHost android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="@dimen/dp_8" app:layout_constraintTop_toBottomOf="@id/sub_operation_variable_title">
        <androidx.recyclerview.widget.RecyclerView android:id="@id/sub_operation_variable_recycle" android:layout_width="fill_parent" android:layout_height="wrap_content" />
    </com.transsion.baseui.widget.OperateScrollableHost>
</androidx.constraintlayout.widget.ConstraintLayout>
