<?xml version="1.0" encoding="utf-8"?>
<FrameLayout android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/base_color_999999" android:layout_gravity="center" android:id="@id/tv_no_more" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="0.0dip" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="11.0sp" android:textColor="@color/base_color_999999" android:layout_gravity="center" android:id="@id/tv_err" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/base_load_err" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/base_color_999999" android:layout_gravity="center" android:id="@id/tv_complete" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" />
    <ProgressBar android:layout_gravity="center" android:id="@id/load_view" android:layout_width="14.0dip" android:layout_height="14.0dip" android:layout_marginTop="3.0dip" android:layout_marginBottom="3.0dip" android:indeterminateTint="@color/brand" />
</FrameLayout>
