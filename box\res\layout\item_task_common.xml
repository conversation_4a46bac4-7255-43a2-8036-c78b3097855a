<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:background="@drawable/bg_task_background" android:paddingTop="@dimen/dp_8" android:paddingBottom="@dimen/dp_8" android:layout_width="0.0dip" android:layout_height="0.0dip" android:paddingStart="@dimen/dimens_12" android:paddingEnd="@dimen/dimens_12"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.tn.lib.widget.TnTextView android:textColor="@color/white" android:ellipsize="end" android:id="@id/member_item_task_title" android:layout_width="0.0dip" android:maxLines="2" android:layout_marginEnd="@dimen/dp_8" app:layout_constraintEnd_toStartOf="@id/member_item_task_button" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" style="@style/style_import_text" />
    <TextView android:textColor="@color/yellow_50" android:gravity="center" android:id="@id/member_item_task_button" android:layout_width="0.0dip" android:layout_height="0.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent" style="@style/style_import_text" />
    <ImageView android:id="@id/member_item_task_points_icon" android:layout_width="0.0dip" android:layout_height="0.0dip" app:layout_constraintBottom_toBottomOf="@id/member_item_task_points_text" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="@id/member_item_task_points_text" />
    <TextView android:textColor="@color/white" android:id="@id/member_item_task_points_text" android:layout_marginTop="0.0dip" android:layout_marginStart="0.0dip" app:layout_constraintStart_toEndOf="@id/member_item_task_points_icon" app:layout_constraintTop_toBottomOf="@id/member_item_task_title" style="@style/style_import_text" />
</androidx.constraintlayout.widget.ConstraintLayout>
