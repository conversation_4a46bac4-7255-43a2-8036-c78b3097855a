<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="0.0dip" android:layout_height="0.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.tn.lib.widget.TnTextView android:textSize="@dimen/dimens_16" android:textColor="@color/common_white" android:ellipsize="end" android:id="@id/member_item_task_title" android:layout_width="0.0dip" android:maxLines="1" android:paddingEnd="16.0dip" app:layout_constraintEnd_toStartOf="@id/member_item_task_title_right" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" style="@style/style_import_text" />
    <com.tn.lib.widget.TnTextView android:textColor="@color/white_60" android:id="@id/member_item_task_title_right" android:visibility="gone" android:layout_width="0.0dip" android:layout_height="0.0dip" android:text="@string/task_invite_user_detail_text" android:drawableRight="@mipmap/ic_arrow_right" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>
