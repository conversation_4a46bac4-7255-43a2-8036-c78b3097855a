<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <include android:id="@id/card_cover" android:layout_width="96.0dip" android:layout_height="54.0dip" android:layout_marginTop="12.0dip" android:layout_marginBottom="8.0dip" android:layout_marginStart="16.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" layout="@layout/layout_download_item_cover" />
    <View android:id="@id/v_series_top_bg_2" android:background="@drawable/shape_download_series_top_bg_2" android:visibility="gone" android:layout_width="72.0dip" android:layout_height="12.0dip" app:layout_constraintBottom_toTopOf="@id/card_cover" app:layout_constraintEnd_toEndOf="@id/card_cover" app:layout_constraintStart_toStartOf="@id/card_cover" />
    <View android:id="@id/v_series_top_bg" android:background="@drawable/shape_download_series_top_bg" android:visibility="gone" android:layout_width="84.0dip" android:layout_height="6.0dip" app:layout_constraintBottom_toTopOf="@id/card_cover" app:layout_constraintEnd_toEndOf="@id/card_cover" app:layout_constraintStart_toStartOf="@id/card_cover" />
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/text_01" android:ellipsize="end" android:id="@id/tv_name" android:layout_width="wrap_content" android:layout_height="wrap_content" android:maxLines="1" android:layout_marginStart="8.0dip" app:layout_constrainedWidth="true" app:layout_constraintEnd_toStartOf="@id/tv_ep" app:layout_constraintHorizontal_bias="0.0" app:layout_constraintHorizontal_chainStyle="packed" app:layout_constraintStart_toEndOf="@id/card_cover" app:layout_constraintTop_toTopOf="@id/card_cover" style="@style/style_medium_text" />
    <com.tn.lib.widget.TnTextView android:textColor="@color/text_01" android:ellipsize="end" android:id="@id/tv_ep" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:maxLines="1" android:layout_marginStart="2.0dip" app:layout_constraintEnd_toStartOf="@id/v_more" app:layout_constraintStart_toEndOf="@id/tv_name" app:layout_constraintTop_toTopOf="@id/tv_name" app:layout_constraintVertical_chainStyle="packed" style="@style/style_medium_text" />
    <com.tn.lib.widget.TnTextView android:textColor="@color/text_08" android:id="@id/tv_file_size" android:layout_marginTop="4.0dip" android:drawablePadding="4.0dip" app:layout_constraintStart_toStartOf="@id/tv_name" app:layout_constraintTop_toBottomOf="@id/tv_name" style="@style/style_regular_text" />
    <com.tn.lib.widget.TnTextView android:textColor="@color/text_08" android:id="@id/tv_process" android:layout_marginBottom="2.0dip" app:layout_constraintBottom_toBottomOf="@id/card_cover" app:layout_constraintStart_toStartOf="@id/tv_name" style="@style/style_regular_text" />
    <View android:id="@id/v_more" android:layout_width="50.0dip" android:layout_height="0.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_more" android:layout_width="20.0dip" android:layout_height="20.0dip" android:layout_marginEnd="16.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent" app:srcCompat="@drawable/ic_download_check_unselected_text_02" />
    <com.tn.lib.widget.TnTextView android:textSize="11.0dip" android:textColor="@color/white" android:id="@id/tv_more_count" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" app:layout_constraintBottom_toBottomOf="@id/iv_more" app:layout_constraintEnd_toEndOf="@id/iv_more" app:layout_constraintStart_toStartOf="@id/iv_more" app:layout_constraintTop_toTopOf="@id/iv_more" />
</androidx.constraintlayout.widget.ConstraintLayout>
