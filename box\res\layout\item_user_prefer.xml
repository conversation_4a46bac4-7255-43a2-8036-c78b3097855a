<?xml version="1.0" encoding="utf-8"?>
<TextView android:textSize="16.0sp" android:textColor="@drawable/selector_user_prefer_item_text" android:gravity="center" android:id="@id/tv_perfer" android:background="@drawable/selector_user_prefer_item_bg" android:paddingTop="5.0dip" android:paddingBottom="5.0dip" android:layout_width="wrap_content" android:layout_height="48.0dip" android:paddingStart="16.0dip" android:paddingEnd="16.0dip" style="@style/style_medium_text"
  xmlns:android="http://schemas.android.com/apk/res/android" />
