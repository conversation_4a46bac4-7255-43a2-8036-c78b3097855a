<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:paddingLeft="@dimen/dp_12" android:paddingRight="@dimen/dp_12" android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.google.android.material.imageview.ShapeableImageView android:id="@id/ivCover" android:layout_width="44.0dip" android:layout_height="62.0dip" android:scaleType="centerCrop" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:shapeAppearanceOverlay="@style/roundStyle_4" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="14.0sp" android:textColor="@color/text_02" android:ellipsize="end" android:id="@id/tvSubject" android:visibility="visible" android:layout_width="0.0dip" android:maxLines="1" android:includeFontPadding="false" android:textAlignment="viewStart" android:layout_marginStart="8.0dip" app:layout_constraintBottom_toTopOf="@id/tvTag" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/ivCover" app:layout_constraintTop_toTopOf="@id/ivCover" app:layout_constraintVertical_chainStyle="packed" style="@style/style_medium_text" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/white_60" android:ellipsize="end" android:gravity="start|center" android:id="@id/tvTag" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginTop="6.0dip" android:maxLines="1" android:includeFontPadding="false" android:textAlignment="viewStart" app:layout_constraintBottom_toBottomOf="@id/ivCover" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="@id/tvSubject" app:layout_constraintTop_toBottomOf="@id/tvSubject" style="@style/style_regula_bigger_text" />
    <View android:background="@color/line_01" android:layout_width="fill_parent" android:layout_height="1.0px" android:layout_marginTop="@dimen/dp_12" android:layout_marginBottom="@dimen/dp_12" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintTop_toBottomOf="@id/ivCover" />
</androidx.constraintlayout.widget.ConstraintLayout>
