<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.google.android.material.imageview.ShapeableImageView android:id="@id/ivCover" android:layout_width="fill_parent" android:layout_height="146.0dip" android:scaleType="centerCrop" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:shapeAppearanceOverlay="@style/roundStyle_4" />
    <com.tn.lib.view.CornerTextView android:id="@id/ivSearchCorner" android:layout_width="wrap_content" android:layout_height="wrap_content" app:layout_constraintEnd_toEndOf="@id/ivCover" app:layout_constraintTop_toTopOf="@id/ivCover" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/tt_white" android:gravity="center|bottom" android:id="@id/tvSeasons" android:background="@drawable/bg_cover_seasons" android:paddingLeft="4.0dip" android:paddingRight="4.0dip" android:paddingBottom="4.0dip" android:layout_width="fill_parent" android:layout_height="104.0dip" android:layout_marginTop="6.0dip" app:layout_constraintBottom_toBottomOf="@id/ivCover" app:layout_constraintEnd_toEndOf="@id/ivCover" app:layout_constraintStart_toStartOf="@id/ivCover" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="14.0sp" android:textColor="@color/text_01" android:ellipsize="end" android:id="@id/tvSubject" android:visibility="visible" android:layout_width="0.0dip" android:layout_marginTop="8.0dip" android:maxLines="1" android:textAlignment="viewStart" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="@id/ivCover" app:layout_constraintTop_toBottomOf="@id/ivCover" style="@style/style_medium_text" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/text_02" android:ellipsize="end" android:gravity="start|center" android:id="@id/tvSubjectYear" android:layout_width="0.0dip" android:layout_marginTop="4.0dip" android:maxLines="1" android:drawablePadding="4.0dip" android:textAlignment="viewStart" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="@id/ivCover" app:layout_constraintTop_toBottomOf="@id/tvSubject" style="@style/style_regula_bigger_text" />
</androidx.constraintlayout.widget.ConstraintLayout>
