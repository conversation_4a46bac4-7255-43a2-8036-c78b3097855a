<?xml version="1.0" encoding="utf-8"?>
<FrameLayout android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.constraintlayout.widget.ConstraintLayout android:background="@drawable/libui_common_dialog_bg" android:paddingTop="28.0dip" android:paddingBottom="20.0dip" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginStart="40.0dip" android:layout_marginEnd="40.0dip">
        <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textStyle="bold" android:textColor="@color/text_02" android:gravity="center" android:id="@id/tvTitle" android:layout_width="0.0dip" android:layout_height="wrap_content" android:text="@string/str_block" android:layout_marginStart="24.0dip" android:layout_marginEnd="24.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
        <TextView android:textSize="14.0sp" android:textColor="@color/text_02" android:id="@id/tvDesc" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginTop="8.0dip" android:text="@string/block_desc" android:layout_marginStart="24.0dip" android:layout_marginEnd="24.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tvTitle" />
        <TextView android:textSize="14.0sp" android:textColor="@color/text_02" android:gravity="center" android:id="@id/tvCancel" android:background="@drawable/libui_sub_btn2_normal" android:layout_width="0.0dip" android:layout_height="36.0dip" android:layout_marginTop="16.0dip" android:text="@string/cancel" android:layout_marginStart="20.0dip" app:layout_constraintEnd_toStartOf="@id/tvBlock" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tvDesc" />
        <TextView android:textSize="14.0sp" android:textColor="@color/white" android:gravity="center" android:id="@id/tvBlock" android:background="@drawable/libui_main_btn_normal" android:layout_width="0.0dip" android:layout_height="0.0dip" android:text="@string/str_block" android:layout_marginStart="8.0dip" android:layout_marginEnd="20.0dip" app:layout_constraintBottom_toBottomOf="@id/tvCancel" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/tvCancel" app:layout_constraintTop_toTopOf="@id/tvCancel" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</FrameLayout>
