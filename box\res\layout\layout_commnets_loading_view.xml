<?xml version="1.0" encoding="utf-8"?>
<FrameLayout android:id="@id/post_detail_loading" android:background="@color/whit_black_not_transform" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <ProgressBar android:layout_gravity="center_horizontal" android:layout_width="30.0dip" android:layout_height="30.0dip" android:layout_marginTop="235.0dip" android:indeterminateTint="@color/brand" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
</FrameLayout>
