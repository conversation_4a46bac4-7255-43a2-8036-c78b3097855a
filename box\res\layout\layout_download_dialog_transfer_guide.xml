<?xml version="1.0" encoding="utf-8"?>
<merge android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <View android:layout_gravity="center_horizontal" android:background="@color/main_gradient_center_color" android:layout_width="2.0dip" android:layout_height="75.0dip" android:layout_marginTop="75.0dip" />
    <com.noober.background.view.BLView android:layout_gravity="center_horizontal" android:layout_width="12.0dip" android:layout_height="12.0dip" android:layout_marginTop="147.0dip" app:bl_corners_radius="8.0dip" app:bl_solid_color="@color/main_gradient_center_color" />
    <com.noober.background.view.BLFrameLayout android:id="@id/fl_guide_content" android:padding="12.0dip" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginStart="20.0dip" android:layout_marginEnd="20.0dip" app:bl_corners_radius="8.0dip" app:bl_gradient_angle="45" app:bl_gradient_endColor="@color/main_gradient_end" app:bl_gradient_startColor="@color/main_gradient_start">
        <TextView android:textColor="@color/white" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/download_path_guide_title" style="@style/style_medium_text" />
        <TextView android:textColor="@color/white" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="24.0dip" android:text="@string/download_path_guide_tips" android:layout_marginEnd="25.0dip" style="@style/style_regular_text" />
        <ImageView android:layout_gravity="end" android:id="@id/iv_guide_close" android:layout_width="11.0dip" android:layout_height="11.0dip" android:layout_marginTop="2.5dip" android:src="@mipmap/ic_close_black" android:layout_marginEnd="2.5dip" app:tint="@color/white" />
        <com.tn.lib.view.CircleProgressBar android:layout_gravity="end" android:id="@id/progress_bar_guide_close" android:layout_width="16.0dip" android:layout_height="16.0dip" app:progressBgColor="@color/white_40" app:progressMax="100" app:progressRadius="7.0dip" app:progressRingsColor="@color/white" app:progressStrokesWidth="1.5dip" />
    </com.noober.background.view.BLFrameLayout>
</merge>
