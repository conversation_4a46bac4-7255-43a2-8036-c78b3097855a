<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.constraintlayout.widget.ConstraintLayout android:paddingBottom="60.0dip" android:layout_width="fill_parent" android:layout_height="fill_parent">
        <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_empty" android:layout_width="200.0dip" android:layout_height="120.0dip" android:layout_marginTop="40.0dip" android:src="@mipmap/ic_no_content" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
        <com.tn.lib.widget.TnTextView android:textSize="14.0sp" android:textColor="@color/text_03" android:id="@id/tv_reset" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/newcomer_guide_download_tips" app:layout_constraintBottom_toBottomOf="@id/iv_empty" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" />
        <com.noober.background.view.BLTextView android:textColor="@color/text_01" android:gravity="center_vertical" android:id="@id/tv_transfer" android:paddingLeft="12.0dip" android:paddingRight="12.0dip" android:layout_width="wrap_content" android:layout_height="32.0dip" android:layout_marginTop="12.0dip" android:text="@string/download_empty_transfer_tips" android:drawablePadding="4.0dip" app:bl_corners_radius="4.0dip" app:bl_solid_color="@color/module_04" app:drawableStartCompat="@mipmap/ic_download_empty_transfer" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tv_reset" style="@style/style_medium_text" />
        <androidx.appcompat.widget.LinearLayoutCompat android:orientation="vertical" android:id="@id/ll_movie_rec" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="60.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tv_transfer">
            <com.tn.lib.widget.TnTextView android:textSize="16.0sp" android:textColor="@color/text_01" android:id="@id/tv_empty_title" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/download_for_free_tip" android:layout_marginStart="16.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tv_reset" style="@style/robot_medium" />
            <com.tn.lib.view.NoNetworkSmallView android:id="@id/no_network_view" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="112.0dip" android:layout_marginLeft="16.0dip" android:layout_marginTop="12.0dip" android:layout_marginRight="16.0dip" app:layout_constraintTop_toBottomOf="@id/tv_empty_title" />
            <com.tn.lib.view.RecyclerviewCompatibleViewPager android:id="@id/movie_rec_list" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="12.0dip" app:layout_constraintTop_toBottomOf="@id/tv_empty_title" />
        </androidx.appcompat.widget.LinearLayoutCompat>
        <View android:id="@id/v_line" android:background="@color/download_dialog_line" android:layout_width="fill_parent" android:layout_height="6.0dip" android:layout_marginTop="18.0dip" app:layout_constraintTop_toBottomOf="@id/ll_movie_rec" />
        <com.tn.lib.widget.TnTextView android:textSize="20.0sp" android:textColor="@color/text_01" android:id="@id/tv_tips_1_title" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="24.0dip" android:text="@string/download_tips1" android:drawablePadding="2.0dip" app:drawableEndCompat="@mipmap/ic_newcomer_guide_download_tips" app:drawableStartCompat="@mipmap/ic_newcomer_guide_download_tips" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/v_line" style="@style/style_extra_import_text" />
        <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_tips_1" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginTop="8.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tv_tips_1_title" app:srcCompat="@mipmap/img_newcomer_guide_download_1" />
        <com.tn.lib.widget.TnTextView android:textSize="20.0sp" android:textColor="@color/text_01" android:id="@id/tv_tips_0_title" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="24.0dip" android:text="@string/download_tips0" android:drawablePadding="2.0dip" app:drawableEndCompat="@mipmap/ic_newcomer_guide_download_tips" app:drawableStartCompat="@mipmap/ic_newcomer_guide_download_tips" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/iv_tips_1" style="@style/style_extra_import_text" />
        <com.tn.lib.widget.TnTextView android:textSize="16.0sp" android:textColor="@color/text_01" android:gravity="center" android:id="@id/tv_tips_0" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginTop="8.0dip" android:text="@string/download_tips_desc0" android:drawablePadding="2.0dip" android:layout_marginStart="24.0dip" android:layout_marginEnd="24.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tv_tips_0_title" style="@style/robot_medium" />
        <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_tips_0" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginTop="8.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tv_tips_0" app:srcCompat="@mipmap/img_newcomer_guide_download_0" />
        <View android:background="@drawable/bg_shape_newcomer_guide_dw_title_bg" android:layout_width="0.0dip" android:layout_height="4.0dip" android:layout_marginBottom="4.0dip" android:layout_marginStart="22.0dip" android:layout_marginEnd="22.0dip" app:layout_constraintBottom_toBottomOf="@id/tv_tips_2_title" app:layout_constraintEnd_toEndOf="@id/tv_tips_2_title" app:layout_constraintStart_toStartOf="@id/tv_tips_2_title" />
        <com.tn.lib.widget.TnTextView android:textSize="20.0sp" android:textColor="@color/text_01" android:id="@id/tv_tips_2_title" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="24.0dip" android:text="@string/download_tips2" android:drawablePadding="2.0dip" app:drawableEndCompat="@mipmap/ic_newcomer_guide_download_tips" app:drawableStartCompat="@mipmap/ic_newcomer_guide_download_tips" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/iv_tips_0" style="@style/style_extra_import_text" />
        <com.tn.lib.widget.TnTextView android:textSize="16.0sp" android:textColor="@color/text_01" android:gravity="center" android:id="@id/tv_tips_2" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginTop="8.0dip" android:text="@string/download_tips_desc1" android:drawablePadding="2.0dip" android:layout_marginStart="24.0dip" android:layout_marginEnd="24.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tv_tips_2_title" style="@style/robot_medium" />
        <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_tips_2" android:layout_width="0.0dip" android:layout_height="wrap_content" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tv_tips_2" app:srcCompat="@mipmap/img_newcomer_guide_download_2" />
        <com.tn.lib.widget.TnTextView android:textSize="16.0sp" android:textColor="@color/text_01" android:gravity="center" android:id="@id/tv_tips_3" android:layout_width="0.0dip" android:layout_height="wrap_content" android:text="@string/download_tips_desc2" android:drawablePadding="2.0dip" android:layout_marginStart="24.0dip" android:layout_marginEnd="24.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/iv_tips_2" style="@style/robot_medium" />
        <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_tips_3" android:layout_width="0.0dip" android:layout_height="wrap_content" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tv_tips_3" app:srcCompat="@mipmap/img_newcomer_guide_download_3" />
        <com.tn.lib.widget.TnTextView android:textSize="16.0sp" android:textColor="@color/text_01" android:gravity="center" android:id="@id/tv_tips_4" android:layout_width="0.0dip" android:layout_height="wrap_content" android:text="@string/download_tips_desc3" android:drawablePadding="2.0dip" android:layout_marginStart="24.0dip" android:layout_marginEnd="24.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/iv_tips_3" style="@style/robot_medium" />
        <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_tips_4" android:layout_width="0.0dip" android:layout_height="wrap_content" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tv_tips_4" app:srcCompat="@mipmap/img_newcomer_guide_download_4" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.core.widget.NestedScrollView>
