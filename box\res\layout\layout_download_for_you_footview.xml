<?xml version="1.0" encoding="utf-8"?>
<merge android:paddingBottom="17.0dip" android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.tn.lib.widget.TnTextView android:textSize="16.0sp" android:textColor="@color/text_01" android:id="@id/tv_title" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="8.0dip" android:text="@string/download_title_for_you" android:layout_marginStart="12.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" style="@style/style_import_text" />
    <androidx.recyclerview.widget.RecyclerView android:id="@id/recycler_view" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginLeft="8.0dip" android:layout_marginTop="8.0dip" android:layout_marginRight="8.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tv_title" />
    <com.noober.background.view.BLView android:id="@id/v_refresh" android:layout_width="0.0dip" android:layout_height="40.0dip" android:layout_marginLeft="12.0dip" android:layout_marginTop="12.0dip" android:layout_marginRight="12.0dip" app:bl_corners_radius="4.0dip" app:bl_solid_color="@color/white_10" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/recycler_view" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_refresh_progress" android:layout_width="16.0dip" android:layout_height="16.0dip" android:src="@mipmap/ic_refresh" android:tint="@color/white_80" android:indeterminateBehavior="repeat" android:layout_marginEnd="2.0dip" app:layout_constraintBottom_toBottomOf="@id/v_refresh" app:layout_constraintEnd_toStartOf="@id/tv_refresh" app:layout_constraintHorizontal_chainStyle="packed" app:layout_constraintStart_toStartOf="@id/v_refresh" app:layout_constraintTop_toTopOf="@id/v_refresh" />
    <com.tn.lib.widget.TnTextView android:textColor="@color/white_80" android:id="@id/tv_refresh" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/download_btn_refresh" android:layout_marginStart="4.0dip" app:layout_constraintBottom_toBottomOf="@id/v_refresh" app:layout_constraintEnd_toEndOf="@id/v_refresh" app:layout_constraintStart_toEndOf="@id/iv_refresh_progress" app:layout_constraintTop_toTopOf="@id/v_refresh" style="@style/style_medium_text" />
    <androidx.constraintlayout.widget.Group android:id="@id/group_refresh" android:layout_width="wrap_content" android:layout_height="wrap_content" app:constraint_referenced_ids="v_refresh,         iv_refresh_progress,tv_refresh" />
</merge>
