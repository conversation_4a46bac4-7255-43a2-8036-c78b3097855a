<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView android:layout_width="96.0dip" android:layout_height="54.0dip" app:cardCornerRadius="4.0dip" app:cardElevation="0.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <TextView android:id="@id/textView2" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="bg_progress_bar_pause" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_cover" android:layout_width="fill_parent" android:layout_height="fill_parent" android:scaleType="centerCrop" />
    <View android:background="@color/black_40" android:layout_width="fill_parent" android:layout_height="fill_parent" />
    <androidx.appcompat.widget.AppCompatImageView android:layout_width="26.0dip" android:layout_height="26.0dip" app:srcCompat="@mipmap/ic_download_item_top_left_bg" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_type" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_margin="4.0dip" android:tint="@color/white" app:srcCompat="@mipmap/home_ic_movie_white" />
    <View android:layout_gravity="bottom" android:background="@drawable/download_shape_iv_bottom_bg" android:layout_width="fill_parent" android:layout_height="22.0dip" />
    <include android:id="@id/layout_no_file_tips" android:visibility="gone" layout="@layout/view_stub_local_file_delete_layout" />
    <androidx.appcompat.widget.AppCompatImageView android:layout_gravity="center" android:id="@id/iv_play" android:layout_width="wrap_content" android:layout_height="wrap_content" app:srcCompat="@mipmap/ic_download_item_pause" />
    <ProgressBar android:layout_gravity="bottom" android:id="@id/progress_bar" android:layout_width="fill_parent" android:layout_height="2.0dip" android:progressDrawable="@drawable/bg_progress_bar_pause" style="@android:style/Widget.ProgressBar.Horizontal" />
</androidx.cardview.widget.CardView>
