<?xml version="1.0" encoding="utf-8"?>
<merge android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_icon" android:layout_width="44.0dip" android:layout_height="44.0dip" android:layout_marginStart="8.0dip" app:layout_constraintBottom_toBottomOf="@id/ll_content" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="@id/ll_content" app:srcCompat="@mipmap/ic_download_path" />
    <LinearLayout android:gravity="center_vertical" android:orientation="vertical" android:id="@id/ll_content" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginTop="8.0dip" android:layout_marginBottom="8.0dip" android:minHeight="44.0dip" android:layout_marginStart="8.0dip" app:layout_constraintBottom_toTopOf="@id/cl_permission" app:layout_constraintEnd_toStartOf="@id/tv_change" app:layout_constraintStart_toEndOf="@id/iv_icon" app:layout_constraintTop_toTopOf="parent">
        <TextView android:textSize="12.0sp" android:textColor="@color/text_01" android:ellipsize="end" android:gravity="start" android:id="@id/tv_path_name" android:layout_width="wrap_content" android:maxLines="2" android:lineSpacingExtra="-2.0dip" android:textAlignment="viewStart" android:layout_marginStart="8.0dip" style="@style/style_medium_text" />
        <TextView android:textSize="12.0sp" android:textColor="@color/text_02" android:id="@id/tv_available_size" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="2.0dip" android:layout_marginStart="8.0dip" />
    </LinearLayout>
    <TextView android:textSize="12.0sp" android:textColor="@color/main" android:id="@id/tv_change" android:text="@string/change" android:drawablePadding="4.0dip" android:layout_marginEnd="8.0dip" app:drawableEndCompat="@mipmap/ic_arrow_right" app:drawableTint="@color/main" app:layout_constraintBottom_toBottomOf="@id/ll_content" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="@id/ll_content" style="@style/style_medium_text" />
    <androidx.constraintlayout.widget.ConstraintLayout android:id="@id/cl_permission" android:background="@color/download_module_2" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginBottom="12.0dip" android:layout_marginStart="12.0dip" android:layout_marginEnd="12.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintTop_toBottomOf="@id/ll_content">
        <TextView android:textSize="12.0sp" android:textColor="@color/text_01" android:ellipsize="end" android:gravity="start" android:id="@id/tv_permission_title" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginTop="12.0dip" android:text="@string/download_permission_title" android:maxLines="2" android:lineSpacingExtra="-3.0dip" android:textAlignment="viewStart" android:layout_marginStart="8.0dip" app:layout_constraintBottom_toTopOf="@id/tv_permission_tips" app:layout_constraintEnd_toStartOf="@id/tv_permission_btn" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:layout_constraintVertical_chainStyle="packed" style="@style/style_medium_small_text" />
        <TextView android:textSize="12.0sp" android:textColor="@color/text_02" android:ellipsize="end" android:gravity="start" android:id="@id/tv_permission_tips" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginBottom="12.0dip" android:text="@string/download_permission_tips" android:maxLines="2" android:lineSpacingExtra="-3.0dip" android:textAlignment="viewStart" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toStartOf="@id/tv_permission_btn" app:layout_constraintStart_toStartOf="@id/tv_permission_title" app:layout_constraintTop_toBottomOf="@id/tv_permission_title" />
        <TextView android:textSize="12.0sp" android:textColor="@color/main" android:gravity="center_vertical" android:id="@id/tv_permission_btn" android:layout_width="wrap_content" android:layout_height="0.0dip" android:text="@string/download_permission_btn" android:layout_marginEnd="12.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent" style="@style/style_medium_text" />
    </androidx.constraintlayout.widget.ConstraintLayout>
    <androidx.constraintlayout.widget.Group android:id="@id/group_permission" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" app:constraint_referenced_ids="cl_permission" />
</merge>
