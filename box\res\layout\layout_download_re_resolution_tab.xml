<?xml version="1.0" encoding="utf-8"?>
<merge android:layout_width="fill_parent" android:layout_height="wrap_content" app:bl_corners_radius="8.0dip" app:bl_solid_color="@color/download_module_1"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.appcompat.widget.AppCompatTextView android:textSize="14.0sp" android:textColor="@color/text_02" android:id="@id/iv_seasons" android:background="@drawable/selector_video_detail_seasons_bg" android:paddingLeft="8.0dip" android:paddingTop="6.0dip" android:paddingRight="8.0dip" android:paddingBottom="6.0dip" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:drawablePadding="2.0dip" android:drawableEnd="@mipmap/ic_arrow_down" android:layout_marginEnd="12.0dip" app:layout_constraintBottom_toBottomOf="@id/innerTvInfo" app:layout_constraintEnd_toEndOf="parent" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="18.0sp" android:textStyle="bold" android:textColor="@color/text_01" android:id="@id/innerTvTitle" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="8.0dip" android:text="@string/download_video_detail_resources" android:includeFontPadding="false" android:drawablePadding="3.0dip" android:layout_marginStart="8.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/text_02" android:ellipsize="end" android:id="@id/innerTvInfo" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="2.0dip" android:text="@string/download_video_detail_upload_by" android:maxLines="1" android:includeFontPadding="false" android:textAlignment="viewStart" app:layout_constrainedWidth="true" app:layout_constraintEnd_toStartOf="@id/innerTvName" app:layout_constraintHorizontal_bias="0.0" app:layout_constraintHorizontal_chainStyle="packed" app:layout_constraintStart_toStartOf="@id/innerTvTitle" app:layout_constraintTop_toBottomOf="@id/innerTvTitle" style="@style/style_regular_text" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/text_02" android:ellipsize="end" android:id="@id/innerTvName" android:layout_width="wrap_content" android:layout_height="wrap_content" android:maxLines="1" android:includeFontPadding="false" android:textAlignment="viewStart" android:layout_marginStart="4.0dip" app:layout_constrainedWidth="true" app:layout_constraintBottom_toBottomOf="@id/innerTvInfo" app:layout_constraintEnd_toStartOf="@id/innerIcon" app:layout_constraintHorizontal_bias="0.0" app:layout_constraintHorizontal_chainStyle="packed" app:layout_constraintStart_toEndOf="@id/innerTvInfo" app:layout_constraintTop_toTopOf="@id/innerTvInfo" app:layout_constraintWidth_max="60.0dip" style="@style/style_medium_text" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/innerIcon" android:layout_width="wrap_content" android:layout_height="wrap_content" android:src="@mipmap/movie_source_info" android:layout_marginStart="4.0dip" android:layout_marginEnd="16.0dip" app:layout_constraintBottom_toBottomOf="@id/innerTvInfo" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/innerTvName" app:layout_constraintTop_toTopOf="@id/innerTvInfo" app:layout_goneMarginEnd="2.0dip" />
    <androidx.recyclerview.widget.RecyclerView android:id="@id/quality_recycler_view" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="9.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/innerTvInfo" />
    <Space android:id="@id/v_bottom_space" android:layout_width="fill_parent" android:layout_height="8.0dip" app:layout_constraintTop_toBottomOf="@id/quality_recycler_view" />
</merge>
