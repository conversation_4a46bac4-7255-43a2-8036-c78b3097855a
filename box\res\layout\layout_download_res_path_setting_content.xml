<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:background="@drawable/libui_bottom_dialog_bg" android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/text_01" android:gravity="center_vertical" android:id="@id/tv_title" android:layout_width="wrap_content" android:layout_height="48.0dip" android:text="@string/download_transfer_path_title" android:layout_marginStart="16.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" style="@style/style_regula_bigger_text" />
    <ImageView android:id="@id/iv_close" android:padding="4.0dip" android:layout_width="24.0dip" android:layout_height="24.0dip" android:src="@mipmap/ic_close_black" android:layout_marginEnd="12.0dip" app:layout_constraintBottom_toBottomOf="@id/tv_title" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="@id/tv_title" />
    <androidx.recyclerview.widget.RecyclerView android:id="@id/recycler_view" android:layout_width="0.0dip" android:layout_height="wrap_content" app:layout_constraintBottom_toTopOf="@id/line" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tv_title" />
    <View android:id="@id/line" android:background="@color/download_dialog_line" android:layout_width="0.0dip" android:layout_height="1.0dip" app:layout_constraintBottom_toTopOf="@id/v_bottom" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" />
    <View android:id="@id/v_bottom" android:background="@color/module_01" android:layout_width="0.0dip" android:layout_height="68.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" />
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/white" android:gravity="center" android:id="@id/tv_btn_confirm" android:background="@drawable/shape_download_group_button" android:layout_width="0.0dip" android:layout_height="36.0dip" android:text="@string/confirm" android:layout_marginStart="16.0dip" android:layout_marginEnd="16.0dip" app:layout_constraintBottom_toBottomOf="@id/v_bottom" app:layout_constraintEnd_toEndOf="@id/v_bottom" app:layout_constraintStart_toStartOf="@id/v_bottom" app:layout_constraintTop_toTopOf="@id/v_bottom" style="@style/style_medium_text" />
    <View android:id="@id/v_loading_intercept" android:visibility="gone" android:clickable="false" android:layout_width="0.0dip" android:layout_height="0.0dip" app:layout_constraintBottom_toBottomOf="@id/tv_btn_confirm" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="@id/iv_close" />
    <com.noober.background.view.BLFrameLayout android:id="@id/fl_loading" android:visibility="gone" android:layout_width="120.0dip" android:layout_height="120.0dip" android:layout_marginTop="20.0dip" app:bl_corners_radius="8.0dip" app:bl_solid_color="@color/black_70" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent">
        <com.tn.lib.view.CircleProgressBar android:layout_gravity="center_horizontal" android:id="@id/progress_bar" android:layout_width="44.0dip" android:layout_height="44.0dip" android:layout_marginTop="26.0dip" app:layout_constraintBottom_toBottomOf="@id/v_bg" app:layout_constraintStart_toStartOf="@id/v_bg" app:layout_constraintTop_toTopOf="@id/v_bg" app:progressBgColor="@color/white_30" app:progressMax="100" app:progressRadius="19.5dip" app:progressRingsColor="@color/white" app:progressStrokesWidth="5.0dip" />
        <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/white" android:layout_gravity="center|bottom" android:id="@id/tv_tips" android:layout_marginBottom="16.0dip" android:text="@string/download_save_ing" app:layout_constraintBottom_toBottomOf="@id/v_bg" app:layout_constraintStart_toEndOf="@id/progress_bar" app:layout_constraintTop_toTopOf="@id/v_bg" style="@style/style_medium_text" />
        <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/white" android:gravity="center" android:layout_gravity="center_horizontal" android:id="@id/tv_progress" android:layout_marginTop="38.0dip" app:layout_constraintBottom_toBottomOf="@id/v_bg" app:layout_constraintStart_toEndOf="@id/tv_tips" app:layout_constraintTop_toTopOf="@id/v_bg" style="@style/style_medium_text" />
        <androidx.appcompat.widget.AppCompatImageView android:layout_gravity="end" android:id="@id/iv_save_close" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="5.0dip" android:tint="@color/white" android:layout_marginEnd="5.0dip" app:srcCompat="@mipmap/ic_close" />
    </com.noober.background.view.BLFrameLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
