<?xml version="1.0" encoding="utf-8"?>
<merge android:padding="8.0dip" android:layout_width="fill_parent" android:layout_height="48.0dip" app:bl_corners_radius="8.0dip" app:bl_solid_color="@color/module_dark_80"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.noober.background.view.BLImageView android:layout_gravity="start|center" android:layout_width="24.0dip" android:layout_height="24.0dip" android:src="@drawable/ic_download_more" app:bl_corners_radius="4.0dip" app:bl_solid_color="@color/white" />
    <TextView android:textColor="@color/white" android:layout_gravity="center_vertical" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/download_transfer_later_tips" android:layout_marginStart="44.0dip" android:layout_marginEnd="48.0dip" style="@style/style_medium_text" />
    <ImageView android:layout_gravity="end|center" android:id="@id/iv_close" android:layout_width="11.0dip" android:layout_height="11.0dip" android:src="@mipmap/ic_close_black" android:layout_marginEnd="18.5dip" app:tint="@color/white" />
    <com.tn.lib.view.CircleProgressBar android:layout_gravity="end|center" android:id="@id/progress_bar_close" android:layout_width="16.0dip" android:layout_height="16.0dip" android:layout_marginEnd="16.0dip" app:progressBgColor="@color/white_40" app:progressMax="100" app:progressRadius="7.0dip" app:progressRingsColor="@color/white" app:progressStrokesWidth="1.5dip" />
</merge>
