<?xml version="1.0" encoding="utf-8"?>
<com.tn.lib.view.bubbleview.BubbleGradientLinearLayout android:layout_width="230.0dip" android:layout_height="wrap_content" app:angle="8.0dip" app:arrowHeight="6.0dip" app:arrowLocation="top" app:arrowPosition="55.0dip" app:arrowWidth="10.0dip" app:gradientCenterColor="@color/main_gradient_center" app:gradientEndColor="@color/main_gradient_end" app:gradientStartColor="@color/main_gradient_start" app:gradient_orientation="horizontal"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <TextView android:textSize="14.0sp" android:textColor="@color/white" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="8.0dip" android:layout_marginBottom="6.0dip" android:text="@string/download_play_guide" android:layout_marginStart="16.0dip" android:layout_marginEnd="16.0dip" />
</com.tn.lib.view.bubbleview.BubbleGradientLinearLayout>
