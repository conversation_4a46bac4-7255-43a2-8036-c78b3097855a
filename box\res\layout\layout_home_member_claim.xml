<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:gravity="center_horizontal" android:layout_gravity="center" android:orientation="vertical" android:id="@id/clRoot" android:layout_width="wrap_content" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/ivIcon" android:layout_width="@dimen/dimens_48" android:layout_height="@dimen/dimens_48" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <TextView android:textSize="10.0sp" android:textStyle="bold" android:textColor="@color/white" android:id="@id/tvTitle" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="@dimen/dp_2" app:layout_constraintBottom_toTopOf="@id/tvSubTitle" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:layout_constraintVertical_chainStyle="packed" />
    <TextView android:textSize="10.0sp" android:textColor="@color/white_60" android:id="@id/tvSubTitle" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="@dimen/dp_2" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="@id/tvTitle" app:layout_constraintTop_toBottomOf="@id/tvTitle" />
    <TextView android:textSize="@dimen/sp_12" android:textStyle="bold" android:textColor="@color/gray_dark_00" android:gravity="center" android:id="@id/btnClaim" android:background="@drawable/bg_claim_btn_6" android:layout_width="100.0dip" android:layout_height="@dimen/dimens_24" android:layout_marginTop="@dimen/dp_6" android:text="@string/home_claim_now" />
</LinearLayout>
