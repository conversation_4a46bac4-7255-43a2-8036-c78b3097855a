<?xml version="1.0" encoding="utf-8"?>
<merge android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <FrameLayout android:id="@id/fl_container" android:layout_width="0.0dip" android:layout_height="0.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_cover" android:layout_width="0.0dip" android:layout_height="0.0dip" android:scaleType="fitCenter" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <com.noober.background.view.BLView android:id="@id/up_background" android:layout_width="fill_parent" android:layout_height="256.0dip" app:bl_gradient_endColor="@color/transparent" app:bl_gradient_startColor="@color/black_70" app:layout_constraintTop_toTopOf="parent" />
    <com.noober.background.view.BLView android:id="@id/bottom_background" android:layout_width="fill_parent" android:layout_height="320.0dip" app:bl_gradient_angle="90" app:bl_gradient_endColor="@color/transparent" app:bl_gradient_startColor="@color/black_70" app:layout_constraintBottom_toBottomOf="parent" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_pause" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:src="@mipmap/post_detail_short_tv_pause" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <android.widget.Space android:id="@id/progress_guideline" android:layout_width="0.0dip" android:layout_height="1.5dip" android:layout_marginBottom="40.0dip" android:layout_marginStart="16.0dip" android:layout_marginEnd="16.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" />
    <ProgressBar android:id="@id/progress_bar" android:layout_width="0.0dip" android:layout_height="2.0dip" android:progressDrawable="@drawable/post_detail_imm_video_progress" app:layout_constraintBottom_toBottomOf="@id/progress_guideline" app:layout_constraintEnd_toEndOf="@id/progress_guideline" app:layout_constraintStart_toStartOf="@id/progress_guideline" style="@android:style/Widget.ProgressBar.Horizontal" />
    <com.transsion.postdetail.ui.view.ClipLoading android:id="@id/cl_loading" android:background="@color/imm_video_progress_bg_color" android:visibility="gone" android:layout_width="0.0dip" android:layout_height="1.5dip" android:indeterminate="false" android:indeterminateOnly="false" app:layout_constraintBottom_toBottomOf="@id/progress_guideline" app:layout_constraintEnd_toEndOf="@id/progress_guideline" app:layout_constraintStart_toStartOf="@id/progress_guideline" />
    <androidx.appcompat.widget.AppCompatSeekBar android:id="@id/seek_bar" android:background="@color/transparent" android:focusable="false" android:visibility="gone" android:clickable="false" android:layout_width="0.0dip" android:layout_height="wrap_content" android:maxHeight="4.0dip" android:progress="0" android:progressDrawable="@drawable/post_detail_layer_seekbar" android:thumb="@drawable/post_detail_shape_seekbar_bar" android:thumbOffset="0.0dip" android:splitTrack="false" app:layout_constraintBottom_toBottomOf="@id/progress_guideline" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="@id/progress_guideline" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textColor="@color/white" android:gravity="center" android:id="@id/tv_progress_des" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginBottom="28.0dip" android:minWidth="150.0dip" app:layout_constraintBottom_toTopOf="@id/seek_bar" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" />
    <com.transsion.postdetail.ui.view.VideoProgressDragGestureView android:id="@id/v_progress_gesture" android:layout_width="0.0dip" android:layout_height="0.0dip" android:layout_marginTop="-20.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="@id/progress_bar" />
    <com.noober.background.view.BLTextView android:textColor="@color/white" android:gravity="start|center" android:id="@id/tvShortTvEp" android:visibility="gone" android:layout_width="0.0dip" android:layout_height="32.0dip" android:layout_marginBottom="24.0dip" android:drawablePadding="4.0dip" android:drawableStart="@mipmap/post_detail_ic_video_short_tv_ep" android:paddingStart="8.0dip" android:paddingEnd="8.0dip" android:layout_marginEnd="12.0dip" app:bl_corners_radius="8.0dip" app:bl_solid_color="@color/white_20" app:bl_stroke_color="@color/white_16" app:bl_stroke_width="1.0dip" app:drawableEndCompat="@mipmap/ic_arrow_right_white" app:layout_constraintBottom_toTopOf="@id/progress_guideline" app:layout_constraintEnd_toStartOf="@id/llOperationLayout" app:layout_constraintStart_toStartOf="@id/progress_guideline" style="@style/style_medium_text" />
    <LinearLayout android:orientation="vertical" android:id="@id/llOperationLayout" android:layout_width="40.0dip" android:layout_height="wrap_content" android:layout_marginBottom="24.0dip" android:layout_marginEnd="12.0dip" app:layout_constraintBottom_toBottomOf="@id/progress_guideline" app:layout_constraintEnd_toEndOf="parent">
        <com.google.android.material.imageview.ShapeableImageView android:id="@id/iv_video_avatar" android:layout_width="fill_parent" android:layout_height="40.0dip" android:layout_marginBottom="20.0dip" android:scaleType="centerCrop" app:shapeAppearanceOverlay="@style/circle_style" app:strokeColor="@color/base_color_20_eeeeee" app:strokeWidth="1.0dip" />
        <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_download" android:visibility="gone" android:layout_width="40.0dip" android:layout_height="40.0dip" android:layout_marginBottom="20.0dip" app:srcCompat="@mipmap/ic_short_tv_download" />
        <androidx.appcompat.widget.AppCompatTextView android:textSize="11.0sp" android:textColor="@color/white" android:gravity="center_horizontal" android:id="@id/tv_like" android:layout_width="40.0dip" android:layout_height="wrap_content" android:layout_marginBottom="16.0dip" android:shadowColor="@color/base_color_80000000" android:shadowRadius="3.0" app:drawableTopCompat="@drawable/post_detail_imm_video_like" />
        <androidx.appcompat.widget.AppCompatTextView android:textSize="11.0sp" android:textColor="@color/white" android:gravity="center_horizontal" android:id="@id/tv_favorite" android:visibility="gone" android:layout_width="40.0dip" android:layout_height="wrap_content" android:layout_marginBottom="16.0dip" android:shadowColor="@color/base_color_80000000" android:shadowRadius="3.0" app:drawableTopCompat="@drawable/post_detail_selector_short_tv_favorite_big" />
        <androidx.appcompat.widget.AppCompatTextView android:textSize="11.0sp" android:textColor="@color/white" android:gravity="center_horizontal" android:id="@id/tv_comment" android:layout_width="40.0dip" android:layout_height="wrap_content" android:layout_marginBottom="16.0dip" android:shadowColor="@color/base_color_80000000" android:shadowRadius="3.0" app:drawableTopCompat="@mipmap/post_detail_ic_video_comment" />
        <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_share" android:layout_width="40.0dip" android:layout_height="40.0dip" android:scaleType="center" android:layout_marginEnd="12.0dip" app:srcCompat="@mipmap/post_detail_ic_video_share" />
    </LinearLayout>
    <com.transsion.shorttv.widget.ShortTvAdView android:id="@id/shortTvAdView" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginBottom="12.0dip" android:layout_marginEnd="14.0dip" app:layout_constraintBottom_toTopOf="@id/barrier" app:layout_constraintEnd_toStartOf="@id/llOperationLayout" app:layout_constraintStart_toStartOf="@id/tvShortTvEp" />
    <androidx.constraintlayout.widget.Barrier android:id="@id/barrier" android:layout_width="wrap_content" android:layout_height="wrap_content" app:barrierDirection="top" app:constraint_referenced_ids="layout_subject_room,tvTitle" />
    <include android:id="@id/layout_subject_room" android:layout_width="0.0dip" android:layout_height="40.0dip" android:layout_marginBottom="12.0dip" android:layout_marginEnd="14.0dip" app:layout_constraintBottom_toTopOf="@id/nsPostDes" app:layout_constraintEnd_toStartOf="@id/llOperationLayout" app:layout_constraintStart_toStartOf="@id/progress_guideline" layout="@layout/layout_post_item_bottom_module" />
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/white" android:gravity="start" android:id="@id/tvTitle" android:visibility="gone" android:layout_width="0.0dip" android:layout_marginBottom="13.0dip" android:textAlignment="viewStart" android:layout_marginEnd="14.0dip" app:layout_constraintBottom_toTopOf="@id/nsPostDes" app:layout_constraintEnd_toStartOf="@id/llOperationLayout" app:layout_constraintStart_toEndOf="@id/iv_short_cover" style="@style/style_title_text" />
    <com.google.android.material.imageview.ShapeableImageView android:id="@id/iv_short_cover" android:padding="0.5dip" android:visibility="visible" android:layout_width="21.0dip" android:layout_height="30.0dip" android:scaleType="centerCrop" android:layout_marginEnd="8.0dip" app:layout_constraintBottom_toBottomOf="@id/tvTitle" app:layout_constraintEnd_toStartOf="@id/tvTitle" app:layout_constraintStart_toStartOf="@id/progress_guideline" app:layout_constraintTop_toTopOf="@id/tvTitle" app:shapeAppearance="@style/roundStyle_2" app:strokeColor="@color/white_10" app:strokeWidth="1.0dip" />
    <com.transsion.baseui.widget.NestedScrollableHost android:id="@id/nsPostDes" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginBottom="12.0dip" android:layout_marginEnd="14.0dip" app:layout_constrainedHeight="true" app:layout_constraintBottom_toTopOf="@id/tvShortTvEp" app:layout_constraintEnd_toStartOf="@id/llOperationLayout" app:layout_constraintHeight_max="108.0dip" app:layout_constraintStart_toStartOf="@id/progress_guideline" app:layout_goneMarginBottom="24.0dip">
        <com.transsion.postdetail.ui.view.ImmNestedScrollView android:scrollbarThumbVertical="@drawable/post_detail_imm_video_scrollbar_thumb" android:scrollbars="vertical" android:layout_width="fill_parent" android:layout_height="wrap_content" android:fadeScrollbars="false" app:layout_constrainedHeight="true" app:layout_constraintBottom_toTopOf="@id/progress_guideline" app:layout_constraintHeight_max="108.0dip" app:layout_constraintStart_toStartOf="@id/progress_guideline">
            <FrameLayout android:layout_width="fill_parent" android:layout_height="fill_parent">
                <com.tn.lib.view.expand.ExpandView android:textSize="14.0sp" android:textColor="@color/white" android:id="@id/ev_post_des" android:layout_width="fill_parent" android:layout_height="wrap_content" app:layout_goneMarginBottom="0.0dip" app:pop_expand_hint_color="@color/white" app:pop_hint_text_size="10.0sp" app:pop_reverse_Lines="2" app:pop_shrink_hint_color="@color/white" />
            </FrameLayout>
        </com.transsion.postdetail.ui.view.ImmNestedScrollView>
    </com.transsion.baseui.widget.NestedScrollableHost>
</merge>
