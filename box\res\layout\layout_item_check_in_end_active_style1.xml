<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:background="@drawable/bg_item_member_check_in_8" android:layout_width="120.0dip" android:layout_height="56.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <LinearLayout android:gravity="center" android:orientation="horizontal" android:id="@id/member_check_in_ll" android:layout_width="fill_parent" android:layout_height="wrap_content" app:layout_constraintBottom_toTopOf="@id/member_check_in_rl" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent">
        <ImageView android:id="@id/tag_iv" android:layout_width="20.0dip" android:layout_height="20.0dip" android:src="@mipmap/ic_member_points" android:scaleType="fitXY" android:importantForAccessibility="no" />
        <TextView android:textSize="14.0sp" android:textStyle="bold" android:textColor="@color/text_01" android:gravity="center" android:id="@id/member_point" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="+5" android:layout_marginStart="4.0dip" app:layout_constraintTop_toBottomOf="@id/tag_iv" />
    </LinearLayout>
    <RelativeLayout android:id="@id/member_check_in_rl" android:background="@drawable/bg_item_check_in_active_bottom_8" android:layout_width="fill_parent" android:layout_height="20.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent">
        <TextView android:textSize="14.0sp" android:textStyle="bold" android:textColor="@color/gray_0_1" android:gravity="center" android:id="@id/member_check_in_tv" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/member_claim" android:layout_centerInParent="true" />
        <ProgressBar android:id="@id/member_check_in_pb" android:visibility="gone" android:layout_width="12.0dip" android:layout_height="12.0dip" android:indeterminateDrawable="@drawable/member_loading_black_small" android:layout_centerInParent="true" />
    </RelativeLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
