<?xml version="1.0" encoding="utf-8"?>
<com.tn.lib.view.bubbleview.BubbleGradientConstrainLayout android:layout_width="210.0dip" android:layout_height="wrap_content" app:angle="8.0dip" app:arrowHeight="6.0dip" app:arrowLocation="top" app:arrowPosition="179.0dip" app:arrowWidth="12.0dip" app:gradientCenterColor="@color/main_gradient_center" app:gradientEndColor="@color/main_gradient_end" app:gradientStartColor="@color/main_gradient_start" app:gradient_orientation="horizontal"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.appcompat.widget.AppCompatImageView android:layout_gravity="center_vertical" android:id="@id/iv_tips" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginStart="8.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:srcCompat="@mipmap/ic_tips" />
    <TextView android:textSize="14.0sp" android:textColor="@color/white" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginTop="12.0dip" android:layout_marginBottom="12.0dip" android:text="@string/subtitle_land_float_tips" android:layout_marginStart="4.0dip" android:layout_marginEnd="11.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toStartOf="@id/progress_bar_guide_close" app:layout_constraintStart_toEndOf="@id/iv_tips" app:layout_constraintTop_toTopOf="parent" />
    <ImageView android:layout_gravity="end" android:id="@id/iv_guide_close" android:layout_width="11.0dip" android:layout_height="11.0dip" android:src="@mipmap/ic_close_black" android:layout_marginEnd="12.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent" app:tint="@color/white" />
    <com.tn.lib.view.CircleProgressBar android:layout_gravity="end" android:id="@id/progress_bar_guide_close" android:layout_width="16.0dip" android:layout_height="16.0dip" app:layout_constraintBottom_toBottomOf="@id/iv_guide_close" app:layout_constraintEnd_toEndOf="@id/iv_guide_close" app:layout_constraintStart_toStartOf="@id/iv_guide_close" app:layout_constraintTop_toTopOf="@id/iv_guide_close" app:progressBgColor="@color/white_40" app:progressMax="100" app:progressRadius="7.0dip" app:progressRingsColor="@color/white" app:progressStrokesWidth="1.5dip" />
</com.tn.lib.view.bubbleview.BubbleGradientConstrainLayout>
