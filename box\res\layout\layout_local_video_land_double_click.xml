<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.transsion.postdetail.ui.view.VideoDoubleClickBackgroundView android:id="@id/v_double_click" android:background="@color/black_70" android:layout_width="fill_parent" android:layout_height="fill_parent" />
    <com.airbnb.lottie.LottieAnimationView android:id="@id/lottie_double_click_left" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintHorizontal_bias="0.25" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:lottie_fileName="video_land_double_click_backward.json" app:lottie_repeatMode="restart" />
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/white" android:id="@id/tv_double_click_left" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="8.0dip" android:text="@string/video_double_tap_time" app:layout_constraintEnd_toEndOf="@id/lottie_double_click_left" app:layout_constraintStart_toStartOf="@id/lottie_double_click_left" app:layout_constraintTop_toBottomOf="@id/lottie_double_click_left" style="@style/style_regular_text" />
    <com.airbnb.lottie.LottieAnimationView android:id="@id/lottie_double_click_right" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintHorizontal_bias="0.75" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:lottie_fileName="video_land_double_click_forward.json" app:lottie_repeatMode="restart" />
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/white" android:id="@id/tv_double_click_right" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="8.0dip" android:text="@string/video_double_tap_time" app:layout_constraintEnd_toEndOf="@id/lottie_double_click_right" app:layout_constraintStart_toStartOf="@id/lottie_double_click_right" app:layout_constraintTop_toBottomOf="@id/lottie_double_click_right" style="@style/style_regular_text" />
</androidx.constraintlayout.widget.ConstraintLayout>
