<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:gravity="center" android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <TextView android:textSize="14.0sp" android:textColor="@color/white" android:id="@id/tv_error_tips" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/downloading_play_load_failed" android:shadowColor="@color/black_30" android:shadowRadius="3.0" style="@style/style_medium_text" />
    <TextView android:textSize="14.0sp" android:textColor="@color/white" android:gravity="center" android:id="@id/tv_error_btn" android:background="@drawable/shape_download_group_button" android:layout_width="wrap_content" android:layout_height="36.0dip" android:layout_marginTop="16.0dip" android:minWidth="134.0dip" android:text="@string/turn_on_network" android:paddingStart="15.0dip" android:paddingEnd="15.0dip" style="@style/style_medium_text" />
</LinearLayout>
