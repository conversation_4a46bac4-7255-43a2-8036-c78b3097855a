<?xml version="1.0" encoding="utf-8"?>
<merge android:orientation="vertical" android:paddingBottom="16.0dip" android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.noober.background.view.BLLinearLayout android:gravity="center_vertical" android:id="@id/ll_detail" android:layout_width="wrap_content" android:layout_height="32.0dip" android:layout_marginTop="8.0dip" android:layout_marginStart="12.0dip" app:bl_corners_radius="16.0dip" app:bl_solid_color="@color/module_02" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent">
        <com.google.android.material.imageview.ShapeableImageView android:id="@id/iv_cover" android:layout_width="24.0dip" android:layout_height="24.0dip" android:scaleType="centerCrop" android:layout_marginStart="4.0dip" app:shapeAppearanceOverlay="@style/roundStyle_12" />
        <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/text_01" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/download_details" android:layout_marginStart="4.0dip" android:layout_marginEnd="12.0dip" style="@style/style_regular_text" />
    </com.noober.background.view.BLLinearLayout>
    <com.noober.background.view.BLLinearLayout android:gravity="center_vertical" android:id="@id/ll_download" android:layout_width="wrap_content" android:layout_height="32.0dip" android:layout_marginStart="8.0dip" app:bl_corners_radius="16.0dip" app:bl_solid_color="@color/module_02" app:layout_constraintStart_toEndOf="@id/ll_detail" app:layout_constraintTop_toTopOf="@id/ll_detail">
        <androidx.appcompat.widget.AppCompatImageView android:layout_width="16.0dip" android:layout_height="16.0dip" android:layout_marginStart="12.0dip" app:shapeAppearanceOverlay="@style/roundStyle_12" app:srcCompat="@mipmap/movie_detail_ic_info_download" />
        <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/text_01" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/download_more" android:layout_marginStart="4.0dip" android:layout_marginEnd="12.0dip" style="@style/style_regular_text" />
    </com.noober.background.view.BLLinearLayout>
    <TextView android:id="@id/tv_ep_title" android:visibility="gone" android:layout_width="wrap_content" android:layout_marginTop="24.0dip" android:text="@string/downloaded" android:layout_marginStart="12.0dip" android:layout_marginEnd="16.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/ll_detail" style="@style/style_title_text" />
    <androidx.recyclerview.widget.RecyclerView android:orientation="horizontal" android:id="@id/recycler_view_ep" android:visibility="gone" android:layout_width="0.0dip" android:layout_height="44.0dip" android:layout_marginTop="12.0dip" android:layout_marginBottom="12.0dip" android:layout_marginEnd="12.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tv_ep_title" />
    <com.noober.background.view.BLView android:id="@id/trans_v_bg" android:visibility="gone" android:layout_width="44.0dip" android:layout_height="0.0dip" android:layout_marginEnd="12.0dip" app:bl_gradient_angle="0" app:bl_gradient_endColor="@color/bg_01" app:bl_gradient_startColor="@color/transparent" app:layout_constraintBottom_toBottomOf="@id/tv_more" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="@id/tv_more" />
    <com.noober.background.view.BLTextView android:textColor="@color/text_01" android:gravity="center_vertical" android:id="@id/tv_more" android:visibility="gone" android:layout_width="36.0dip" android:layout_height="0.0dip" android:text="@string/all" android:paddingStart="8.0dip" app:layout_constraintBottom_toBottomOf="@id/recycler_view_ep" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="@id/recycler_view_ep" style="@style/style_medium_text" />
</merge>
