<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:id="@id/portrait_root" android:background="@mipmap/post_detail_local_video_bg" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <FrameLayout android:id="@id/video_portrait_surface" android:layout_width="fill_parent" android:layout_height="fill_parent" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <View android:id="@id/v_gesture" android:layout_width="fill_parent" android:layout_height="fill_parent" android:layout_marginTop="20.0dip" android:layout_marginBottom="20.0dip" android:layout_marginStart="20.0dip" android:layout_marginEnd="20.0dip" app:layout_constraintTop_toTopOf="parent" />
    <View android:id="@id/land_view1" android:background="@drawable/post_detail_shape_local_top_bg" android:layout_width="fill_parent" android:layout_height="96.0dip" app:layout_constraintTop_toTopOf="parent" />
    <View android:id="@id/land_view2" android:background="@drawable/post_detail_shape_local_bottom_bg" android:layout_width="fill_parent" android:layout_height="96.0dip" app:layout_constraintBottom_toBottomOf="parent" />
    <ViewStub android:id="@id/vs_replay" android:layout="@layout/layout_local_video_load_replay" android:layout_width="0.0dip" android:layout_height="0.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <androidx.appcompat.widget.AppCompatImageView android:layout_gravity="start|center" android:id="@id/vd_iv_back" android:layout_width="24.0dip" android:layout_height="48.0dip" android:src="@mipmap/icon_white_back" android:scaleType="center" android:layout_marginStart="16.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textColor="#ffffffff" android:ellipsize="end" android:layout_gravity="start|center" android:id="@id/vd_title" android:layout_width="0.0dip" android:layout_height="wrap_content" android:maxLines="1" android:layout_marginStart="16.0dip" android:layout_marginEnd="5.0dip" app:layout_constraintBottom_toBottomOf="@id/vd_iv_back" app:layout_constraintEnd_toStartOf="@id/iv_feedback" app:layout_constraintStart_toEndOf="@id/vd_iv_back" app:layout_constraintTop_toTopOf="@id/vd_iv_back" style="@style/style_medium_text" />
    <androidx.appcompat.widget.AppCompatImageView android:layout_gravity="end|center" android:id="@id/iv_feedback" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginEnd="16.0dip" app:layout_constraintBottom_toTopOf="@id/tvHelpTip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="@id/vd_iv_back" app:layout_constraintVertical_chainStyle="packed" app:srcCompat="@mipmap/ic_feedback_transparent" />
    <TextView android:textSize="8.0sp" android:textColor="@color/white" android:gravity="center_horizontal" android:id="@id/tvHelpTip" android:layout_width="0.0dip" android:layout_height="wrap_content" android:text="@string/title_help" app:layout_constraintBottom_toBottomOf="@id/vd_iv_back" app:layout_constraintEnd_toEndOf="@id/iv_feedback" app:layout_constraintStart_toStartOf="@id/iv_feedback" app:layout_constraintTop_toBottomOf="@id/iv_feedback" />
    <androidx.constraintlayout.widget.Group android:id="@id/vd_land_toolbar" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" app:constraint_referenced_ids="vd_iv_back,vd_title,iv_feedback,tvHelpTip," />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="28.0sp" android:textColor="#ccffffff" android:id="@id/vd_land_center_progress" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:shadowColor="@color/black_50" android:shadowDy="2.0" android:shadowRadius="2.0" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintLeft_toLeftOf="parent" app:layout_constraintRight_toRightOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <include android:id="@id/vd_include_load" android:visibility="gone" layout="@layout/post_video_loading" />
    <FrameLayout android:id="@id/flRootSubtitle" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginLeft="12.0dip" android:layout_marginRight="12.0dip" android:layout_marginBottom="68.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent">
        <androidx.appcompat.widget.LinearLayoutCompat android:orientation="vertical" android:id="@id/flSubtitle" android:layout_width="fill_parent" android:layout_height="wrap_content">
            <com.avery.subtitle.widget.SimpleSubtitleView android:textSize="16.0sp" android:textColor="@color/white" android:ellipsize="end" android:gravity="center" android:layout_gravity="center" android:id="@id/vSubtitleTop" android:paddingLeft="6.0dip" android:paddingTop="3.0dip" android:paddingRight="6.0dip" android:paddingBottom="3.0dip" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:shadowColor="@color/black_90" android:shadowDx="2.0" android:shadowDy="1.0" android:shadowRadius="2.0" android:lineSpacingExtra="1.0dip" android:layout_marginStart="24.0dip" android:layout_marginEnd="24.0dip" />
            <com.avery.subtitle.widget.SimpleSubtitleView android:textSize="16.0sp" android:textColor="@color/white" android:ellipsize="end" android:gravity="center" android:layout_gravity="center" android:id="@id/vSubtitleBottom" android:paddingLeft="6.0dip" android:paddingTop="3.0dip" android:paddingRight="6.0dip" android:paddingBottom="3.0dip" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="2.0dip" android:shadowColor="@color/black_90" android:shadowDx="2.0" android:shadowDy="1.0" android:shadowRadius="2.0" android:lineSpacingExtra="1.0dip" android:layout_marginStart="24.0dip" android:layout_marginEnd="24.0dip" />
        </androidx.appcompat.widget.LinearLayoutCompat>
    </FrameLayout>
    <androidx.constraintlayout.widget.ConstraintLayout android:id="@id/vd_land_bottom_controller" android:layout_width="fill_parent" android:layout_height="wrap_content" app:layout_constraintBottom_toBottomOf="parent">
        <TextView android:textSize="11.0sp" android:textColor="@color/white" android:gravity="start" android:id="@id/vd_video_time" android:layout_width="wrap_content" android:layout_height="wrap_content" android:minWidth="28.0dip" android:shadowDy="2.0" android:shadowRadius="2.0" android:layout_marginStart="12.0dip" app:layout_constraintBottom_toBottomOf="@id/vd_seekbar" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="@id/vd_seekbar" />
        <com.tn.lib.view.SecondariesSeekBar android:id="@id/vd_seekbar" android:background="@color/transparent" android:layout_width="0.0dip" android:layout_height="24.0dip" android:layout_marginLeft="12.0dip" android:layout_marginRight="12.0dip" android:layout_marginBottom="48.0dip" android:maxHeight="2.0dip" android:minHeight="2.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toStartOf="@id/vd_video_duration" app:layout_constraintStart_toEndOf="@id/vd_video_time" app:ssb_bar_center_color="@color/main_gradient_center" app:ssb_bar_end_color="@color/main_gradient_end" app:ssb_bar_start_color="@color/main_gradient_start" app:ssb_bg_color="@color/white_30" app:ssb_secondaries_color="@color/white" app:ssb_thumb_color="@color/white" app:ssb_thumb_size="8.0dip" />
        <TextView android:textSize="11.0sp" android:textColor="@color/white" android:gravity="end" android:id="@id/vd_video_duration" android:layout_width="wrap_content" android:layout_height="wrap_content" android:minWidth="28.0dip" android:shadowDy="2.0" android:shadowRadius="2.0" android:layout_marginEnd="12.0dip" app:layout_constraintBottom_toBottomOf="@id/vd_seekbar" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="@id/vd_seekbar" />
        <androidx.appcompat.widget.AppCompatImageView android:id="@id/vd_pause" android:layout_width="32.0dip" android:layout_height="32.0dip" android:layout_marginBottom="8.0dip" android:src="@drawable/post_icon_pause" android:scaleType="center" android:layout_marginStart="12.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" />
        <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/white" android:ellipsize="end" android:gravity="center_vertical" android:id="@id/tv_language" android:layout_width="wrap_content" android:layout_height="48.0dip" android:maxWidth="108.0dip" android:text="@string/language" android:maxLines="1" android:includeFontPadding="false" android:drawablePadding="@dimen/dp_4" android:drawableStart="@drawable/ic_player_language" android:layout_marginEnd="@dimen/dp_12" app:layout_constraintBottom_toBottomOf="@id/tvRotate" app:layout_constraintEnd_toStartOf="@id/tvRotate" app:layout_constraintTop_toTopOf="@id/tvRotate" style="@style/style_import_text" />
        <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/white" android:ellipsize="end" android:gravity="center_vertical" android:id="@id/tvRotate" android:layout_width="wrap_content" android:layout_height="wrap_content" android:maxWidth="108.0dip" android:text="@string/rotate" android:maxLines="1" android:includeFontPadding="false" android:drawablePadding="@dimen/dp_4" android:drawableStart="@drawable/rotate" android:layout_marginEnd="@dimen/dp_12" app:layout_constraintBottom_toBottomOf="@id/vd_pause" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="@id/vd_pause" style="@style/style_import_text" />
    </androidx.constraintlayout.widget.ConstraintLayout>
    <androidx.appcompat.widget.AppCompatTextView android:textSize="@dimen/sp_14" android:textColor="@color/white" android:gravity="center" android:id="@id/tvPressSpeed" android:background="@drawable/bg_radius4_black60" android:paddingLeft="@dimen/dp_12" android:paddingRight="@dimen/dp_12" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="@dimen/dimens_24" android:layout_marginTop="32.0dip" android:text="2x" android:includeFontPadding="false" android:drawablePadding="@dimen/dp_4" android:drawableEnd="@drawable/ic_quick_speed" app:layout_constraintEnd_toEndOf="@id/vd_land_bottom_controller" app:layout_constraintStart_toStartOf="@id/vd_land_bottom_controller" app:layout_constraintTop_toTopOf="parent" />
    <TextView android:textColor="@color/white" android:gravity="center_vertical" android:id="@id/tv_toast_2" android:background="@drawable/post_detail_shape_black_trans70_8dp" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="40.0dip" android:layout_marginBottom="132.0dip" android:paddingStart="12.0dip" android:paddingEnd="12.0dip" android:layout_marginStart="12.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" style="@style/style_medium_text" />
    <TextView android:textColor="@color/white" android:gravity="center_vertical" android:id="@id/tv_toast_1" android:background="@drawable/post_detail_shape_black_trans70_8dp" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="40.0dip" android:layout_marginBottom="80.0dip" android:paddingStart="12.0dip" android:paddingEnd="12.0dip" android:layout_marginStart="12.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" style="@style/style_medium_text" />
    <ViewStub android:id="@id/vs_load_failed" android:layout="@layout/layout_local_video_load_failed" android:layout_width="wrap_content" android:layout_height="wrap_content" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <include android:id="@id/layout_bv" layout="@layout/orplayer_layout_brightness_volume" />
    <include android:id="@id/layout_sync_adjust" layout="@layout/layout_subtitle_sync_adjust" />
</androidx.constraintlayout.widget.ConstraintLayout>
