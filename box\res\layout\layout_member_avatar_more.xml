<?xml version="1.0" encoding="utf-8"?>
<com.noober.background.view.BLLinearLayout android:gravity="center" android:id="@id/v_more" android:layout_width="24.0dip" android:layout_height="24.0dip" android:scaleType="centerCrop" app:bl_corners_radius="12.0dip" app:bl_solid_color="@color/gray_dark_30" app:bl_stroke_color="@color/post_detail_top_bg" app:bl_stroke_width="2.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.noober.background.view.BLView android:layout_gravity="center" android:layout_width="2.5dip" android:layout_height="2.5dip" android:layout_marginEnd="2.5dip" app:bl_corners_radius="2.0dip" app:bl_solid_color="@color/white" />
    <com.noober.background.view.BLView android:layout_gravity="center" android:layout_width="2.5dip" android:layout_height="2.5dip" android:layout_marginEnd="2.5dip" app:bl_corners_radius="2.0dip" app:bl_solid_color="@color/white" />
    <com.noober.background.view.BLView android:layout_gravity="center" android:layout_width="2.5dip" android:layout_height="2.5dip" app:bl_corners_radius="2.0dip" app:bl_solid_color="@color/white" />
</com.noober.background.view.BLLinearLayout>
