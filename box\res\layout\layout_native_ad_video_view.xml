<?xml version="1.0" encoding="utf-8"?>
<FrameLayout android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.cloud.hisavana.sdk.common.widget.video.AdVideoView android:layout_gravity="center" android:id="@id/ad_video" android:layout_width="fill_parent" android:layout_height="wrap_content" android:minHeight="1.0dip" app:use_cache="true" />
    <ImageView android:layout_gravity="bottom" android:id="@id/im_volume" android:visibility="gone" android:layout_width="25.0dip" android:layout_height="25.0dip" android:layout_margin="10.0dip" android:src="@drawable/hisavana_volume_close" android:contentDescription="TODO" />
    <ImageView android:layout_gravity="end|bottom" android:id="@id/im_repeat" android:visibility="gone" android:layout_width="25.0dip" android:layout_height="25.0dip" android:layout_margin="10.0dip" android:src="@drawable/hisavana_repeat" />
    <ImageView android:layout_gravity="center" android:id="@id/im_play" android:visibility="gone" android:layout_width="25.0dip" android:layout_height="25.0dip" android:src="@drawable/hisavana_play" />
    <ProgressBar android:layout_gravity="bottom" android:id="@id/ad_progress" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="2.0dip" android:progressDrawable="@drawable/hisavana_ad_video_progress_bg" android:indeterminateTint="#ff008438" style="@style/Widget.AppCompat.ProgressBar.Horizontal" />
</FrameLayout>
