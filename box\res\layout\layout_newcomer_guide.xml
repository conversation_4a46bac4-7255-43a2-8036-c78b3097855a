<?xml version="1.0" encoding="utf-8"?>
<merge android:background="@color/cl31_70_p" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <FrameLayout android:id="@id/v_guide_anima_bg" android:background="@drawable/bg_shape_newcomer_guide_target_anima" android:layout_width="wrap_content" android:layout_height="wrap_content" app:layout_constraintBottom_toBottomOf="@id/v_guide_bg" app:layout_constraintEnd_toEndOf="@id/v_guide_bg" app:layout_constraintStart_toStartOf="@id/v_guide_bg" app:layout_constraintTop_toTopOf="@id/v_guide_bg" />
    <FrameLayout android:id="@id/v_guide_bg" android:background="@drawable/bg_shape_newcomer_guide_target" android:layout_width="wrap_content" android:layout_height="wrap_content" app:layout_constraintBottom_toBottomOf="@id/iv_guide_target" app:layout_constraintEnd_toEndOf="@id/iv_guide_target" app:layout_constraintStart_toStartOf="@id/iv_guide_target" app:layout_constraintTop_toTopOf="@id/iv_guide_target" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_guide_target" android:visibility="invisible" android:layout_width="wrap_content" android:layout_height="wrap_content" app:layout_constraintTop_toTopOf="parent" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_guide_line" android:visibility="invisible" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="4.0dip" android:layout_marginBottom="4.0dip" app:layout_constraintEnd_toEndOf="@id/iv_guide_target" app:layout_constraintStart_toStartOf="@id/iv_guide_target" app:srcCompat="@mipmap/im_newcomer_line" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textColor="@color/white" android:id="@id/tv_guide_tips" android:visibility="invisible" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="8.0dip" android:layout_marginBottom="8.0dip" android:layout_marginStart="24.0dip" android:layout_marginEnd="24.0dip" style="@style/style_medium_text" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/white" android:gravity="center" android:id="@id/tv_guide_button" android:background="@drawable/bg_shape_newcomer_guide_button" android:visibility="invisible" android:layout_width="wrap_content" android:layout_height="32.0dip" android:layout_marginTop="24.0dip" android:layout_marginBottom="24.0dip" android:paddingStart="17.0dip" android:paddingEnd="17.0dip" app:layout_constraintEnd_toEndOf="@id/tv_guide_tips" app:layout_constraintStart_toStartOf="@id/tv_guide_tips" style="@style/robot_medium" />
</merge>
