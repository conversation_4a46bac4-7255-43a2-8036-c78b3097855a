<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <LinearLayout android:layout_width="fill_parent" android:layout_height="wrap_content">
        <LinearLayout android:gravity="center" android:id="@id/notice_ll_left" android:background="@drawable/shape_toolbar_btn_bg_light" android:layout_width="0.0dip" android:layout_height="40.0dip" android:layout_weight="1.0" android:layout_marginEnd="8.0dip">
            <ImageView android:id="@id/notice_iv_left" android:layout_width="16.0dip" android:layout_height="16.0dip" android:src="@mipmap/ic_notification_top_10" app:tint="@color/gray_light_70" />
            <TextView android:textSize="13.0sp" android:textColor="@color/gray_light_70" android:gravity="center" android:layout_gravity="center" android:id="@id/notice_tv_left" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/tool_notice_top_10" android:layout_marginStart="2.0dip" />
        </LinearLayout>
        <LinearLayout android:gravity="center" android:id="@id/notice_ll_right" android:background="@drawable/shape_toolbar_btn_bg_light" android:layout_width="0.0dip" android:layout_height="40.0dip" android:layout_weight="1.0" android:layout_marginStart="8.0dip">
            <ImageView android:id="@id/notice_iv_right" android:layout_width="16.0dip" android:layout_height="16.0dip" android:src="@mipmap/ic_notification_search" app:tint="@color/gray_light_70" />
            <TextView android:textSize="13.0sp" android:textColor="@color/gray_light_70" android:gravity="center" android:layout_gravity="center" android:id="@id/notice_tv_right" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/tool_notice_search" android:layout_marginStart="2.0dip" />
        </LinearLayout>
    </LinearLayout>
    <TextView android:textSize="13.0sp" android:textColor="@color/brand_gradient_start" android:gravity="center" android:id="@id/notice_v_setting" android:paddingTop="8.0dip" android:paddingBottom="8.0dip" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/tool_notice_setting" android:drawablePadding="2.0dip" android:paddingEnd="8.0dip" style="@style/style_medium_small_text" />
</LinearLayout>
