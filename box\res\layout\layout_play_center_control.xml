<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:id="@id/centerControlLayout" android:layout_width="wrap_content" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/ivCenterPause" android:layout_width="54.0dip" android:layout_height="54.0dip" android:src="@drawable/ic_player_pause" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/ivForward" android:layout_width="40.0dip" android:layout_height="40.0dip" android:src="@drawable/ic_forward_10" android:layout_marginStart="60.0dip" app:layout_constraintBottom_toBottomOf="@id/ivCenterPause" app:layout_constraintStart_toEndOf="@id/ivCenterPause" app:layout_constraintTop_toTopOf="@id/ivCenterPause" />
    <ProgressBar android:id="@id/progressBar" android:visibility="gone" android:layout_width="40.0dip" android:layout_height="40.0dip" android:indeterminateTint="@color/main" app:layout_constraintBottom_toBottomOf="@id/ivCenterPause" app:layout_constraintEnd_toEndOf="@id/ivCenterPause" app:layout_constraintStart_toStartOf="@id/ivCenterPause" app:layout_constraintTop_toTopOf="@id/ivCenterPause" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/ivBackward" android:layout_width="40.0dip" android:layout_height="40.0dip" android:src="@drawable/ic_backward_10" android:layout_marginEnd="60.0dip" app:layout_constraintBottom_toBottomOf="@id/ivCenterPause" app:layout_constraintEnd_toStartOf="@id/ivCenterPause" app:layout_constraintTop_toTopOf="@id/ivCenterPause" />
</androidx.constraintlayout.widget.ConstraintLayout>
