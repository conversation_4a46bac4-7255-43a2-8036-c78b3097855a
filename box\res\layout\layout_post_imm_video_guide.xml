<?xml version="1.0" encoding="utf-8"?>
<merge android:gravity="center" android:orientation="vertical" android:background="@color/base_black_30_p" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.tn.lib.view.CustomLottieAnimationView android:id="@id/lav_guide" android:layout_width="240.0dip" android:layout_height="240.0dip" app:lottie_fileName="immersion_video_guide_swipe_more.json" />
    <TextView android:textSize="18.0sp" android:textColor="@color/white" android:gravity="center" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="-20.0dip" android:text="@string/post_video_guide" android:layout_marginStart="16.0dip" android:layout_marginEnd="16.0dip" />
</merge>
