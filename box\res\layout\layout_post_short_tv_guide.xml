<?xml version="1.0" encoding="utf-8"?>
<merge android:background="@color/black_50" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.noober.background.view.BLView android:textColor="@color/white" android:gravity="center_vertical" android:id="@id/tv_short_tv_guide_ep_bg" android:layout_width="0.0dip" android:layout_height="36.0dip" android:layout_marginBottom="90.0dip" android:layout_marginStart="15.0dip" android:layout_marginEnd="65.0dip" app:bl_corners_radius="8.0dip" app:bl_solid_color="@color/short_tv_guide_bg" app:bl_stroke_color="@color/white_16" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" style="@style/style_medium_text" />
    <com.noober.background.view.BLTextView android:textColor="@color/white" android:gravity="center_vertical" android:id="@id/tv_short_tv_guide_ep" android:layout_width="0.0dip" android:layout_height="32.0dip" android:drawablePadding="4.0dip" android:drawableStart="@mipmap/post_detail_ic_video_short_tv_ep" android:paddingStart="7.0dip" android:paddingEnd="7.0dip" android:layout_marginStart="2.0dip" android:layout_marginEnd="2.0dip" app:bl_corners_radius="8.0dip" app:bl_solid_color="@color/white_20" app:bl_stroke_color="@color/white_16" app:bl_stroke_width="1.0dip" app:drawableEndCompat="@mipmap/ic_arrow_right_white" app:layout_constraintBottom_toBottomOf="@id/tv_short_tv_guide_ep_bg" app:layout_constraintEnd_toEndOf="@id/tv_short_tv_guide_ep_bg" app:layout_constraintStart_toStartOf="@id/tv_short_tv_guide_ep_bg" app:layout_constraintTop_toTopOf="@id/tv_short_tv_guide_ep_bg" style="@style/style_medium_text" />
    <com.noober.background.view.BLImageView android:gravity="center_horizontal" android:id="@id/tv_short_tv_guide_list" android:layout_width="42.0dip" android:layout_height="42.0dip" android:layout_marginBottom="38.0dip" android:scaleType="center" android:layout_marginEnd="11.0dip" app:bl_corners_radius="22.0dip" app:bl_solid_color="@color/short_tv_guide_bg" app:layout_constraintBottom_toTopOf="@id/tv_short_tv_guide_ep" app:layout_constraintEnd_toEndOf="parent" app:srcCompat="@mipmap/post_detail_ic_video_short_tv_list" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_short_tv_guide_arrow_1" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginEnd="8.0dip" app:layout_constraintBottom_toBottomOf="@id/tv_short_tv_guide_list" app:layout_constraintEnd_toStartOf="@id/tv_short_tv_guide_list" app:layout_constraintTop_toTopOf="@id/tv_short_tv_guide_list" app:srcCompat="@mipmap/ic_short_tv_guide_arrow_1" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_short_tv_guide_arrow_2" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginBottom="8.0dip" app:layout_constraintBottom_toTopOf="@id/tv_short_tv_guide_ep_bg" app:layout_constraintEnd_toEndOf="@id/tv_short_tv_guide_ep_bg" app:layout_constraintStart_toStartOf="@id/tv_short_tv_guide_ep_bg" app:srcCompat="@mipmap/ic_short_tv_guide_arrow_2" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="18.0sp" android:textColor="@color/white" android:layout_width="wrap_content" android:layout_marginBottom="21.0dip" android:text="@string/short_tv_guide_tips" android:layout_marginEnd="20.0dip" app:layout_constraintBottom_toTopOf="@id/iv_short_tv_guide_arrow_2" app:layout_constraintEnd_toEndOf="@id/tv_short_tv_guide_ep_bg" app:layout_constraintStart_toStartOf="@id/tv_short_tv_guide_ep_bg" style="@style/style_medium_text" />
</merge>
