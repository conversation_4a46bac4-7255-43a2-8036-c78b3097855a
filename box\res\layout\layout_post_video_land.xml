<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:id="@id/land_root" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <FrameLayout android:id="@id/video_land_surface" android:background="@color/black" android:layout_width="fill_parent" android:layout_height="fill_parent" app:layout_constraintTop_toTopOf="parent" />
    <View android:id="@id/land_view1" android:background="@drawable/post_detail_gradient_up_50" android:layout_width="fill_parent" android:layout_height="60.0dip" app:layout_constraintTop_toTopOf="parent" />
    <View android:id="@id/land_view2" android:background="@drawable/post_detail_gradient_bottom_50" android:layout_width="fill_parent" android:layout_height="60.0dip" app:layout_constraintBottom_toBottomOf="parent" />
    <FrameLayout android:id="@id/land_phone_bar" android:layout_width="fill_parent" android:layout_height="24.0dip" app:layout_constraintTop_toTopOf="parent">
        <androidx.appcompat.widget.AppCompatTextView android:textSize="10.0sp" android:textColor="@color/white" android:layout_gravity="center" android:id="@id/land_phone_time" android:layout_width="wrap_content" android:layout_height="wrap_content" />
        <androidx.appcompat.widget.AppCompatTextView android:textSize="11.0sp" android:textColor="@color/white" android:gravity="center_horizontal" android:layout_gravity="end|center" android:id="@id/land_phone_net" android:background="@drawable/post_shape_translate_6dp" android:layout_width="32.0dip" android:layout_height="wrap_content" android:layout_marginEnd="@dimen/video_land_border_distance" />
    </FrameLayout>
    <FrameLayout android:id="@id/vd_land_toolbar" android:layout_width="fill_parent" android:layout_height="44.0dip" android:layout_marginTop="24.0dip" android:layout_marginStart="@dimen/video_land_border_distance" android:layout_marginEnd="@dimen/video_land_border_distance" app:layout_constraintTop_toTopOf="parent">
        <androidx.appcompat.widget.AppCompatImageView android:layout_gravity="center_vertical" android:id="@id/vd_land_iv_back" android:layout_width="30.0dip" android:layout_height="30.0dip" android:src="@mipmap/icon_white_back" android:scaleType="center" />
        <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textColor="#ffffffff" android:ellipsize="end" android:layout_gravity="center_vertical" android:id="@id/vd_title" android:layout_width="fill_parent" android:layout_height="wrap_content" android:maxLines="1" android:layout_marginStart="48.0dip" android:layout_marginEnd="12.0dip" />
    </FrameLayout>
    <androidx.appcompat.widget.AppCompatTextView android:textSize="28.0sp" android:textColor="#ccffffff" android:id="@id/vd_land_center_progress" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintLeft_toLeftOf="parent" app:layout_constraintRight_toRightOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <include android:id="@id/vd_include_load" layout="@layout/post_video_loading" />
    <androidx.constraintlayout.widget.ConstraintLayout android:id="@id/vd_land_bottom_controller" android:layout_width="fill_parent" android:layout_height="48.0dip" android:layout_marginStart="@dimen/video_land_border_distance" android:layout_marginEnd="@dimen/video_land_border_distance" app:layout_constraintBottom_toBottomOf="parent">
        <androidx.appcompat.widget.AppCompatImageView android:id="@id/vd_pause" android:layout_width="24.0dip" android:layout_height="24.0dip" android:src="@drawable/post_icon_pause" android:scaleType="center" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintLeft_toLeftOf="parent" app:layout_constraintTop_toTopOf="parent" />
        <androidx.appcompat.widget.AppCompatSeekBar android:id="@id/vd_seekbar" android:background="@color/transparent" android:layout_width="0.0dip" android:layout_height="24.0dip" android:maxHeight="2.0dip" android:progress="0" android:progressDrawable="@drawable/post_detail_layer_seekbar" android:minHeight="2.0dip" android:thumb="@drawable/post_detail_shape_seekbar_bar" android:layout_marginStart="6.0dip" android:layout_marginEnd="6.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintLeft_toRightOf="@id/vd_pause" app:layout_constraintRight_toLeftOf="@id/vd_video_time" app:layout_constraintTop_toTopOf="parent" />
        <TextView android:textSize="11.0sp" android:textColor="@color/white" android:id="@id/vd_video_time" android:layout_width="wrap_content" android:layout_height="wrap_content" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintRight_toRightOf="parent" app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
