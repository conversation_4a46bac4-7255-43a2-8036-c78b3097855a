<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:background="@drawable/libui_bottom_dialog_bg" android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.appcompat.widget.AppCompatTextView android:id="@id/tv_title" android:layout_marginTop="15.0dip" android:text="@string/report" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" style="@style/style_title_text" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_close" android:layout_width="40.0dip" android:layout_height="40.0dip" android:src="@drawable/player_ic_close" android:scaleType="center" app:layout_constraintBottom_toBottomOf="@id/tv_title" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="@id/tv_title" />
    <androidx.recyclerview.widget.RecyclerView android:id="@id/report_list" android:descendantFocusability="blocksDescendants" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="14.0dip" android:layout_marginBottom="40.0dip" app:layout_constraintBottom_toTopOf="@id/btn_submit" app:layout_constraintTop_toBottomOf="@id/tv_title" />
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/common_white" android:id="@id/btn_submit" android:layout_width="fill_parent" android:layout_marginBottom="16.0dip" android:text="@string/submit" android:layout_marginStart="16.0dip" android:layout_marginEnd="16.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintTop_toBottomOf="@id/report_list" style="@style/style_main_btn_h42" />
</androidx.constraintlayout.widget.ConstraintLayout>
