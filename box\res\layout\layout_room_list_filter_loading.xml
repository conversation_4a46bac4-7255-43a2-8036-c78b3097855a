<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <LinearLayout android:orientation="vertical" android:background="@color/module_04" android:layout_width="96.0dip" android:layout_height="fill_parent">
        <FrameLayout android:background="@color/bg_01" android:layout_width="fill_parent" android:layout_height="48.0dip">
            <View android:layout_gravity="center" android:background="@color/module_04" android:layout_width="fill_parent" android:layout_height="16.0dip" android:layout_marginLeft="12.0dip" android:layout_marginRight="12.0dip" />
        </FrameLayout>
        <View android:background="@color/bg_01" android:layout_width="fill_parent" android:layout_height="16.0dip" android:layout_marginLeft="12.0dip" android:layout_marginTop="16.0dip" android:layout_marginRight="12.0dip" android:layout_marginBottom="16.0dip" />
        <View android:background="@color/bg_01" android:layout_width="fill_parent" android:layout_height="16.0dip" android:layout_marginLeft="12.0dip" android:layout_marginTop="16.0dip" android:layout_marginRight="12.0dip" android:layout_marginBottom="16.0dip" />
        <View android:background="@color/bg_01" android:layout_width="fill_parent" android:layout_height="16.0dip" android:layout_marginLeft="12.0dip" android:layout_marginTop="16.0dip" android:layout_marginRight="12.0dip" android:layout_marginBottom="16.0dip" />
        <View android:background="@color/bg_01" android:layout_width="fill_parent" android:layout_height="16.0dip" android:layout_marginLeft="12.0dip" android:layout_marginTop="16.0dip" android:layout_marginRight="12.0dip" android:layout_marginBottom="16.0dip" />
        <View android:background="@color/bg_01" android:layout_width="fill_parent" android:layout_height="16.0dip" android:layout_marginLeft="12.0dip" android:layout_marginTop="16.0dip" android:layout_marginRight="12.0dip" android:layout_marginBottom="16.0dip" />
        <View android:background="@color/bg_01" android:layout_width="fill_parent" android:layout_height="16.0dip" android:layout_marginLeft="12.0dip" android:layout_marginTop="16.0dip" android:layout_marginRight="12.0dip" android:layout_marginBottom="16.0dip" />
        <View android:background="@color/bg_01" android:layout_width="fill_parent" android:layout_height="16.0dip" android:layout_marginLeft="12.0dip" android:layout_marginTop="16.0dip" android:layout_marginRight="12.0dip" android:layout_marginBottom="16.0dip" />
    </LinearLayout>
    <include layout="@layout/layout_room_list_loading" />
</LinearLayout>
