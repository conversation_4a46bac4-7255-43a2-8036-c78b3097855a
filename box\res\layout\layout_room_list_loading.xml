<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <LinearLayout android:layout_width="fill_parent" android:layout_height="85.0dip">
        <View android:layout_gravity="center_vertical" android:background="@color/module_04" android:layout_width="52.0dip" android:layout_height="52.0dip" android:layout_marginStart="12.0dip" />
        <LinearLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="fill_parent" android:layout_marginTop="17.0dip" android:layout_marginBottom="17.0dip">
            <View android:layout_gravity="center_vertical" android:background="@color/module_04" android:layout_width="154.0dip" android:layout_height="18.0dip" android:layout_marginStart="8.0dip" />
            <View android:layout_gravity="center_vertical" android:background="@color/module_04" android:layout_width="70.0dip" android:layout_height="14.0dip" android:layout_marginTop="2.0dip" android:layout_marginStart="8.0dip" />
            <View android:layout_gravity="center_vertical" android:background="@color/module_04" android:layout_width="154.0dip" android:layout_height="14.0dip" android:layout_marginTop="2.0dip" android:layout_marginStart="8.0dip" />
        </LinearLayout>
    </LinearLayout>
    <LinearLayout android:layout_width="fill_parent" android:layout_height="85.0dip">
        <View android:layout_gravity="center_vertical" android:background="@color/module_04" android:layout_width="52.0dip" android:layout_height="52.0dip" android:layout_marginStart="12.0dip" />
        <LinearLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="fill_parent" android:layout_marginTop="17.0dip" android:layout_marginBottom="17.0dip">
            <View android:layout_gravity="center_vertical" android:background="@color/module_04" android:layout_width="154.0dip" android:layout_height="18.0dip" android:layout_marginStart="8.0dip" />
            <View android:layout_gravity="center_vertical" android:background="@color/module_04" android:layout_width="70.0dip" android:layout_height="14.0dip" android:layout_marginTop="2.0dip" android:layout_marginStart="8.0dip" />
            <View android:layout_gravity="center_vertical" android:background="@color/module_04" android:layout_width="154.0dip" android:layout_height="14.0dip" android:layout_marginTop="2.0dip" android:layout_marginStart="8.0dip" />
        </LinearLayout>
    </LinearLayout>
    <LinearLayout android:layout_width="fill_parent" android:layout_height="85.0dip">
        <View android:layout_gravity="center_vertical" android:background="@color/module_04" android:layout_width="52.0dip" android:layout_height="52.0dip" android:layout_marginStart="12.0dip" />
        <LinearLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="fill_parent" android:layout_marginTop="17.0dip" android:layout_marginBottom="17.0dip">
            <View android:layout_gravity="center_vertical" android:background="@color/module_04" android:layout_width="154.0dip" android:layout_height="18.0dip" android:layout_marginStart="8.0dip" />
            <View android:layout_gravity="center_vertical" android:background="@color/module_04" android:layout_width="70.0dip" android:layout_height="14.0dip" android:layout_marginTop="2.0dip" android:layout_marginStart="8.0dip" />
            <View android:layout_gravity="center_vertical" android:background="@color/module_04" android:layout_width="154.0dip" android:layout_height="14.0dip" android:layout_marginTop="2.0dip" android:layout_marginStart="8.0dip" />
        </LinearLayout>
    </LinearLayout>
    <LinearLayout android:layout_width="fill_parent" android:layout_height="85.0dip">
        <View android:layout_gravity="center_vertical" android:background="@color/module_04" android:layout_width="52.0dip" android:layout_height="52.0dip" android:layout_marginStart="12.0dip" />
        <LinearLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="fill_parent" android:layout_marginTop="17.0dip" android:layout_marginBottom="17.0dip">
            <View android:layout_gravity="center_vertical" android:background="@color/module_04" android:layout_width="154.0dip" android:layout_height="18.0dip" android:layout_marginStart="8.0dip" />
            <View android:layout_gravity="center_vertical" android:background="@color/module_04" android:layout_width="70.0dip" android:layout_height="14.0dip" android:layout_marginTop="2.0dip" android:layout_marginStart="8.0dip" />
            <View android:layout_gravity="center_vertical" android:background="@color/module_04" android:layout_width="154.0dip" android:layout_height="14.0dip" android:layout_marginTop="2.0dip" android:layout_marginStart="8.0dip" />
        </LinearLayout>
    </LinearLayout>
    <LinearLayout android:layout_width="fill_parent" android:layout_height="85.0dip">
        <View android:layout_gravity="center_vertical" android:background="@color/module_04" android:layout_width="52.0dip" android:layout_height="52.0dip" android:layout_marginStart="12.0dip" />
        <LinearLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="fill_parent" android:layout_marginTop="17.0dip" android:layout_marginBottom="17.0dip">
            <View android:layout_gravity="center_vertical" android:background="@color/module_04" android:layout_width="154.0dip" android:layout_height="18.0dip" android:layout_marginStart="8.0dip" />
            <View android:layout_gravity="center_vertical" android:background="@color/module_04" android:layout_width="70.0dip" android:layout_height="14.0dip" android:layout_marginTop="2.0dip" android:layout_marginStart="8.0dip" />
            <View android:layout_gravity="center_vertical" android:background="@color/module_04" android:layout_width="154.0dip" android:layout_height="14.0dip" android:layout_marginTop="2.0dip" android:layout_marginStart="8.0dip" />
        </LinearLayout>
    </LinearLayout>
    <LinearLayout android:layout_width="fill_parent" android:layout_height="85.0dip">
        <View android:layout_gravity="center_vertical" android:background="@color/module_04" android:layout_width="52.0dip" android:layout_height="52.0dip" android:layout_marginStart="12.0dip" />
        <LinearLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="fill_parent" android:layout_marginTop="17.0dip" android:layout_marginBottom="17.0dip">
            <View android:layout_gravity="center_vertical" android:background="@color/module_04" android:layout_width="154.0dip" android:layout_height="18.0dip" android:layout_marginStart="8.0dip" />
            <View android:layout_gravity="center_vertical" android:background="@color/module_04" android:layout_width="70.0dip" android:layout_height="14.0dip" android:layout_marginTop="2.0dip" android:layout_marginStart="8.0dip" />
            <View android:layout_gravity="center_vertical" android:background="@color/module_04" android:layout_width="154.0dip" android:layout_height="14.0dip" android:layout_marginTop="2.0dip" android:layout_marginStart="8.0dip" />
        </LinearLayout>
    </LinearLayout>
    <LinearLayout android:layout_width="fill_parent" android:layout_height="85.0dip">
        <View android:layout_gravity="center_vertical" android:background="@color/module_04" android:layout_width="52.0dip" android:layout_height="52.0dip" android:layout_marginStart="12.0dip" />
        <LinearLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="fill_parent" android:layout_marginTop="17.0dip" android:layout_marginBottom="17.0dip">
            <View android:layout_gravity="center_vertical" android:background="@color/module_04" android:layout_width="154.0dip" android:layout_height="18.0dip" android:layout_marginStart="8.0dip" />
            <View android:layout_gravity="center_vertical" android:background="@color/module_04" android:layout_width="70.0dip" android:layout_height="14.0dip" android:layout_marginTop="2.0dip" android:layout_marginStart="8.0dip" />
            <View android:layout_gravity="center_vertical" android:background="@color/module_04" android:layout_width="154.0dip" android:layout_height="14.0dip" android:layout_marginTop="2.0dip" android:layout_marginStart="8.0dip" />
        </LinearLayout>
    </LinearLayout>
    <LinearLayout android:layout_width="fill_parent" android:layout_height="85.0dip">
        <View android:layout_gravity="center_vertical" android:background="@color/module_04" android:layout_width="52.0dip" android:layout_height="52.0dip" android:layout_marginStart="12.0dip" />
        <LinearLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="fill_parent" android:layout_marginTop="17.0dip" android:layout_marginBottom="17.0dip">
            <View android:layout_gravity="center_vertical" android:background="@color/module_04" android:layout_width="154.0dip" android:layout_height="18.0dip" android:layout_marginStart="8.0dip" />
            <View android:layout_gravity="center_vertical" android:background="@color/module_04" android:layout_width="70.0dip" android:layout_height="14.0dip" android:layout_marginTop="2.0dip" android:layout_marginStart="8.0dip" />
            <View android:layout_gravity="center_vertical" android:background="@color/module_04" android:layout_width="154.0dip" android:layout_height="14.0dip" android:layout_marginTop="2.0dip" android:layout_marginStart="8.0dip" />
        </LinearLayout>
    </LinearLayout>
</LinearLayout>
