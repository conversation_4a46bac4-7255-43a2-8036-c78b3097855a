<?xml version="1.0" encoding="utf-8"?>
<androidx.appcompat.widget.LinearLayoutCompat android:layout_width="fill_parent" android:layout_height="fill_parent" android:layout_marginTop="16.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <androidx.appcompat.widget.AppCompatTextView android:textSize="@dimen/sp_16" android:textStyle="bold" android:textColor="@color/text_01" android:gravity="center_vertical" android:id="@id/iv_latest" android:background="@drawable/shape_short_tv_category_bg" android:paddingLeft="12.0dip" android:paddingRight="12.0dip" android:layout_width="0.0dip" android:layout_height="@dimen/dimens_48" android:text="@string/short_tv_category_latest" android:singleLine="true" android:drawablePadding="8.0dip" android:drawableStart="@mipmap/ic_latest" android:layout_marginStart="16.0dip" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="@dimen/sp_16" android:textStyle="bold" android:textColor="@color/text_01" android:gravity="center_vertical" android:id="@id/iv_hottest" android:background="@drawable/shape_short_tv_category_bg" android:paddingLeft="12.0dip" android:paddingRight="12.0dip" android:layout_width="0.0dip" android:layout_height="@dimen/dimens_48" android:text="@string/short_tv_category_hottest" android:singleLine="true" android:drawablePadding="8.0dip" android:drawableStart="@mipmap/ic_hottest" android:layout_marginStart="8.0dip" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="@dimen/sp_16" android:textStyle="bold" android:textColor="@color/text_01" android:gravity="center" android:id="@id/iv_all" android:background="@drawable/shape_short_tv_category_bg" android:paddingLeft="12.0dip" android:paddingRight="12.0dip" android:layout_width="wrap_content" android:layout_height="@dimen/dimens_48" android:text="@string/short_tv_category_all" android:singleLine="true" android:layout_marginStart="8.0dip" android:layout_marginEnd="16.0dip" />
</androidx.appcompat.widget.LinearLayoutCompat>
