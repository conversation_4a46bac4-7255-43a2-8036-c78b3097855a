<?xml version="1.0" encoding="utf-8"?>
<merge android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="wrap_content">
        <androidx.appcompat.widget.AppCompatTextView android:textSize="18.0sp" android:textStyle="bold" android:gravity="center_vertical" android:id="@id/tv_title" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="16.0dip" android:text="@string/short_tv_my_list" android:layout_marginStart="16.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" style="@style/style_title_text" />
        <androidx.appcompat.widget.AppCompatTextView android:textSize="14.0sp" android:textColor="#ff61656d" android:id="@id/tv_view_all" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="16.0dip" android:text="@string/short_tv_category_view_all" android:drawablePadding="@dimen/dp_4" android:drawableEnd="@mipmap/ic_arrow_right" android:layout_marginEnd="16.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent" style="@style/style_title_text" />
    </androidx.constraintlayout.widget.ConstraintLayout>
    <com.tn.lib.view.HorizontalRecyclerview2 android:id="@id/recycler_view" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="4.0dip" />
    <com.tn.lib.widget.TnTextView android:textSize="18.0sp" android:textStyle="bold" android:id="@id/tv_title_trending" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="8.0dip" android:text="@string/short_tv_most_trending" android:layout_marginStart="16.0dip" style="@style/style_title_text" />
</merge>
