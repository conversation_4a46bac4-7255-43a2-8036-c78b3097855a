<?xml version="1.0" encoding="utf-8"?>
<merge android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <FrameLayout android:id="@id/fl_container" android:layout_width="0.0dip" android:layout_height="0.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_cover" android:layout_width="0.0dip" android:layout_height="0.0dip" android:scaleType="fitCenter" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <com.noober.background.view.BLView android:id="@id/up_background" android:layout_width="fill_parent" android:layout_height="256.0dip" app:bl_gradient_endColor="@color/transparent" app:bl_gradient_startColor="@color/black_70" app:layout_constraintTop_toTopOf="parent" />
    <com.noober.background.view.BLView android:id="@id/bottom_background" android:layout_width="fill_parent" android:layout_height="320.0dip" app:bl_gradient_angle="90" app:bl_gradient_endColor="@color/transparent" app:bl_gradient_startColor="@color/black_70" app:layout_constraintBottom_toBottomOf="parent" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_pause" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:src="@mipmap/post_detail_short_tv_pause" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <android.widget.Space android:id="@id/progress_guideline" android:layout_width="0.0dip" android:layout_height="1.5dip" android:layout_marginBottom="40.0dip" android:layout_marginStart="16.0dip" android:layout_marginEnd="16.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" />
    <ProgressBar android:id="@id/progress_bar" android:layout_width="0.0dip" android:layout_height="2.0dip" android:progressDrawable="@drawable/post_detail_imm_video_progress" app:layout_constraintBottom_toBottomOf="@id/progress_guideline" app:layout_constraintEnd_toEndOf="@id/progress_guideline" app:layout_constraintStart_toStartOf="@id/progress_guideline" style="@android:style/Widget.ProgressBar.Horizontal" />
    <com.transsion.postdetail.ui.view.ClipLoading android:id="@id/cl_loading" android:background="@color/imm_video_progress_bg_color" android:visibility="gone" android:layout_width="0.0dip" android:layout_height="1.5dip" android:indeterminate="false" android:indeterminateOnly="false" app:layout_constraintBottom_toBottomOf="@id/progress_guideline" app:layout_constraintEnd_toEndOf="@id/progress_guideline" app:layout_constraintStart_toStartOf="@id/progress_guideline" />
    <androidx.appcompat.widget.AppCompatSeekBar android:id="@id/seek_bar" android:background="@color/transparent" android:focusable="false" android:visibility="gone" android:clickable="false" android:layout_width="0.0dip" android:layout_height="wrap_content" android:maxHeight="4.0dip" android:progress="0" android:progressDrawable="@drawable/post_detail_layer_seekbar" android:thumb="@drawable/post_detail_shape_seekbar_bar" android:thumbOffset="0.0dip" android:splitTrack="false" app:layout_constraintBottom_toBottomOf="@id/progress_guideline" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="@id/progress_guideline" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textColor="@color/white" android:gravity="center" android:id="@id/tv_progress_des" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginBottom="28.0dip" android:minWidth="150.0dip" app:layout_constraintBottom_toTopOf="@id/seek_bar" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" />
    <com.transsion.postdetail.ui.view.VideoProgressDragGestureView android:id="@id/v_progress_gesture" android:layout_width="0.0dip" android:layout_height="0.0dip" android:layout_marginTop="-20.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="@id/progress_bar" />
    <LinearLayout android:orientation="vertical" android:id="@id/llOperationLayout" android:layout_width="40.0dip" android:layout_height="wrap_content" android:layout_marginBottom="24.0dip" android:layout_marginEnd="12.0dip" app:layout_constraintBottom_toBottomOf="@id/progress_guideline" app:layout_constraintEnd_toEndOf="parent">
        <com.google.android.material.imageview.ShapeableImageView android:id="@id/iv_video_avatar" android:padding="0.5dip" android:visibility="gone" android:layout_width="36.0dip" android:layout_height="36.0dip" android:layout_marginBottom="20.0dip" android:scaleType="centerCrop" app:layout_constraintBottom_toTopOf="@id/iv_download" app:layout_constraintEnd_toEndOf="@id/iv_download" app:layout_constraintStart_toStartOf="@id/iv_download" app:shapeAppearanceOverlay="@style/circle_style" app:strokeColor="@color/base_color_20_eeeeee" app:strokeWidth="1.0dip" />
        <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_download" android:layout_width="40.0dip" android:layout_height="40.0dip" android:layout_marginBottom="20.0dip" app:layout_constraintBottom_toTopOf="@id/tv_like" app:layout_constraintEnd_toEndOf="@id/iv_share" app:layout_constraintStart_toStartOf="@id/iv_share" app:srcCompat="@mipmap/ic_short_tv_download" />
        <androidx.appcompat.widget.AppCompatTextView android:textSize="11.0sp" android:textColor="@color/white" android:gravity="center_horizontal" android:id="@id/tv_like" android:visibility="gone" android:layout_width="40.0dip" android:layout_height="wrap_content" android:layout_marginBottom="16.0dip" android:shadowColor="@color/base_color_80000000" android:shadowRadius="3.0" app:drawableTopCompat="@drawable/post_detail_imm_video_like" app:layout_constraintBottom_toTopOf="@id/tv_favorite" app:layout_constraintEnd_toEndOf="@id/iv_share" app:layout_constraintStart_toStartOf="@id/iv_share" />
        <androidx.appcompat.widget.AppCompatTextView android:textSize="11.0sp" android:textColor="@color/white" android:gravity="center_horizontal" android:id="@id/tv_favorite" android:layout_width="40.0dip" android:layout_height="wrap_content" android:layout_marginBottom="16.0dip" android:shadowColor="@color/base_color_80000000" android:shadowRadius="3.0" app:drawableTopCompat="@drawable/post_detail_selector_short_tv_favorite_big" app:layout_constraintBottom_toTopOf="@id/tv_comment" app:layout_constraintEnd_toEndOf="@id/iv_share" app:layout_constraintStart_toStartOf="@id/iv_share" />
        <androidx.appcompat.widget.AppCompatTextView android:textSize="11.0sp" android:textColor="@color/white" android:gravity="center_horizontal" android:id="@id/tv_comment" android:visibility="gone" android:layout_width="40.0dip" android:layout_height="wrap_content" android:layout_marginBottom="16.0dip" android:shadowColor="@color/base_color_80000000" android:shadowRadius="3.0" app:drawableTopCompat="@mipmap/post_detail_ic_video_comment" app:layout_constraintBottom_toTopOf="@id/iv_share" app:layout_constraintEnd_toEndOf="@id/iv_share" app:layout_constraintStart_toStartOf="@id/iv_share" />
        <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_share" android:layout_width="40.0dip" android:layout_height="40.0dip" android:layout_marginBottom="24.0dip" android:scaleType="center" android:layout_marginEnd="12.0dip" app:layout_constraintBottom_toTopOf="@id/progress_guideline" app:layout_constraintEnd_toEndOf="parent" app:srcCompat="@mipmap/post_detail_ic_video_share" />
    </LinearLayout>
    <com.transsion.shorttv.widget.ShortTvAdView android:id="@id/shortTvAdView" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginBottom="12.0dip" app:layout_constraintBottom_toTopOf="@id/tv_title" app:layout_constraintEnd_toEndOf="@id/tv_title" app:layout_constraintStart_toStartOf="@id/iv_short_cover" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textColor="@color/white" android:ellipsize="end" android:gravity="start|center" android:id="@id/tv_title" android:visibility="gone" android:layout_width="0.0dip" android:layout_height="wrap_content" android:minHeight="44.0dip" android:maxLines="2" android:textAlignment="viewStart" android:layout_marginEnd="14.0dip" app:layout_constraintBottom_toBottomOf="@id/iv_short_cover" app:layout_constraintEnd_toStartOf="@id/llOperationLayout" app:layout_constraintStart_toEndOf="@id/iv_short_cover" app:layout_constraintTop_toTopOf="@id/iv_short_cover" style="@style/style_import_text" />
    <com.google.android.material.imageview.ShapeableImageView android:id="@id/iv_short_cover" android:padding="0.5dip" android:visibility="visible" android:layout_width="30.0dip" android:layout_height="40.0dip" android:layout_marginBottom="8.0dip" android:scaleType="centerCrop" android:layout_marginEnd="8.0dip" app:layout_constraintBottom_toTopOf="@id/tv_desc" app:layout_constraintEnd_toStartOf="@id/tv_title" app:layout_constraintStart_toStartOf="@id/progress_guideline" app:shapeAppearance="@style/roundStyle_2" app:strokeColor="@color/white_10" app:strokeWidth="1.0dip" />
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/white" android:ellipsize="end" android:id="@id/tv_desc" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginBottom="12.0dip" android:lines="1" android:layout_marginEnd="14.0dip" app:layout_constraintBottom_toTopOf="@id/tv_short_tv_ep" app:layout_constraintEnd_toStartOf="@id/llOperationLayout" app:layout_constraintStart_toStartOf="@id/tv_short_tv_ep" style="@style/style_medium_text" />
    <com.noober.background.view.BLTextView android:textColor="@color/white" android:gravity="start|center" android:id="@id/tv_short_tv_ep" android:layout_width="0.0dip" android:layout_height="32.0dip" android:layout_marginBottom="24.0dip" android:drawablePadding="4.0dip" android:drawableStart="@mipmap/post_detail_ic_video_short_tv_list" android:paddingStart="8.0dip" android:paddingEnd="8.0dip" android:layout_marginEnd="12.0dip" app:bl_corners_radius="8.0dip" app:bl_solid_color="@color/white_20" app:bl_stroke_color="@color/white_16" app:bl_stroke_width="1.0dip" app:drawableEndCompat="@mipmap/ic_arrow_right_white" app:layout_constraintBottom_toTopOf="@id/progress_guideline" app:layout_constraintEnd_toStartOf="@id/iv_share" app:layout_constraintStart_toStartOf="@id/progress_guideline" style="@style/style_medium_text" />
    <androidx.constraintlayout.widget.Group android:id="@id/group_content" android:layout_width="wrap_content" android:layout_height="wrap_content" app:constraint_referenced_ids="iv_share,tv_favorite,iv_download,          progress_bar,tv_title,iv_short_cover, tv_short_tv_ep" />
</merge>
