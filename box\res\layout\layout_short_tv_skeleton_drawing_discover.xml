<?xml version="1.0" encoding="utf-8"?>
<com.transsion.baseui.widget.shimmer.ShimmerConstraintLayout android:id="@id/cl_discover_loading" android:background="@color/bg_01" android:layout_width="fill_parent" android:layout_height="fill_parent" android:paddingStart="16.0dip" android:paddingEnd="16.0dip" app:shimmer_auto_start="true" app:shimmer_base_alpha="1.0" app:shimmer_duration="500" app:shimmer_highlight_alpha="0.5" app:shimmer_highlight_color="@color/bg_01" app:shimmer_repeat_count="-1" app:shimmer_repeat_delay="1000" app:shimmer_width_ratio="0.35"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <View android:id="@id/v_bar_space" android:layout_width="fill_parent" android:layout_height="1.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <com.noober.background.view.BLView android:id="@id/v_title" android:layout_width="130.0dip" android:layout_height="18.0dip" android:layout_marginTop="60.0dip" app:bl_corners_radius="4.0dip" app:bl_solid_color="@color/module_04" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/v_bar_space" />
    <include android:id="@id/layout_history_1" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="12.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/v_title" layout="@layout/layout_short_tv_skeleton_drawing_item_history" />
    <include android:id="@id/layout_history_2" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginStart="8.0dip" app:layout_constraintStart_toEndOf="@id/layout_history_1" app:layout_constraintTop_toTopOf="@id/layout_history_1" layout="@layout/layout_short_tv_skeleton_drawing_item_history" />
    <include android:id="@id/layout_history_3" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginStart="8.0dip" app:layout_constraintStart_toEndOf="@id/layout_history_2" app:layout_constraintTop_toTopOf="@id/layout_history_1" layout="@layout/layout_short_tv_skeleton_drawing_item_history" />
    <include android:id="@id/layout_history_4" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginStart="8.0dip" app:layout_constraintStart_toEndOf="@id/layout_history_3" app:layout_constraintTop_toTopOf="@id/layout_history_1" layout="@layout/layout_short_tv_skeleton_drawing_item_history" />
    <include android:id="@id/layout_history_5" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginStart="8.0dip" app:layout_constraintStart_toEndOf="@id/layout_history_4" app:layout_constraintTop_toTopOf="@id/layout_history_1" layout="@layout/layout_short_tv_skeleton_drawing_item_history" />
    <com.noober.background.view.BLView android:id="@id/v_title_2" android:layout_width="130.0dip" android:layout_height="18.0dip" android:layout_marginTop="32.0dip" app:bl_corners_radius="4.0dip" app:bl_solid_color="@color/module_04" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/layout_history_1" />
    <include android:id="@id/layout_history_2_1" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="12.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/v_title_2" layout="@layout/layout_short_tv_skeleton_drawing_item_history" />
    <include android:id="@id/layout_history_2_2" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginStart="8.0dip" app:layout_constraintStart_toEndOf="@id/layout_history_2_1" app:layout_constraintTop_toTopOf="@id/layout_history_2_1" layout="@layout/layout_short_tv_skeleton_drawing_item_history" />
    <include android:id="@id/layout_history_2_3" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginStart="8.0dip" app:layout_constraintStart_toEndOf="@id/layout_history_2_2" app:layout_constraintTop_toTopOf="@id/layout_history_2_1" layout="@layout/layout_short_tv_skeleton_drawing_item_history" />
    <include android:id="@id/layout_history_2_4" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginStart="8.0dip" app:layout_constraintStart_toEndOf="@id/layout_history_2_3" app:layout_constraintTop_toTopOf="@id/layout_history_2_1" layout="@layout/layout_short_tv_skeleton_drawing_item_history" />
    <include android:id="@id/layout_history_2_5" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginStart="8.0dip" app:layout_constraintStart_toEndOf="@id/layout_history_2_4" app:layout_constraintTop_toTopOf="@id/layout_history_1" layout="@layout/layout_short_tv_skeleton_drawing_item_history" />
    <com.noober.background.view.BLView android:id="@id/v_title_trending" android:layout_width="130.0dip" android:layout_height="18.0dip" android:layout_marginTop="34.0dip" app:bl_corners_radius="4.0dip" app:bl_solid_color="@color/module_04" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/layout_history_2_1" />
    <include android:id="@id/layout_trending_1" android:layout_width="fill_parent" android:layout_height="130.0dip" android:layout_marginTop="12.0dip" app:layout_constraintStart_toStartOf="@id/v_title_trending" app:layout_constraintTop_toBottomOf="@id/v_title_trending" layout="@layout/layout_short_tv_skeleton_drawing_item_trending" />
    <include android:id="@id/layout_trending_2" android:layout_width="fill_parent" android:layout_height="130.0dip" android:layout_marginTop="16.0dip" app:layout_constraintStart_toStartOf="@id/v_title_trending" app:layout_constraintTop_toBottomOf="@id/layout_trending_1" layout="@layout/layout_short_tv_skeleton_drawing_item_trending" />
</com.transsion.baseui.widget.shimmer.ShimmerConstraintLayout>
