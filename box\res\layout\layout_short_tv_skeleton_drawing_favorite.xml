<?xml version="1.0" encoding="utf-8"?>
<com.transsion.baseui.widget.shimmer.ShimmerConstraintLayout android:id="@id/cl_favorite_loading" android:background="@color/bg_01" android:layout_width="fill_parent" android:layout_height="fill_parent" android:paddingStart="16.0dip" android:paddingEnd="16.0dip" app:shimmer_auto_start="true" app:shimmer_base_alpha="1.0" app:shimmer_duration="800" app:shimmer_highlight_alpha="0.5" app:shimmer_highlight_color="@color/bg_01" app:shimmer_repeat_count="-1" app:shimmer_repeat_delay="500" app:shimmer_width_ratio="0.35"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <View android:id="@id/v_bar_space" android:layout_width="fill_parent" android:layout_height="1.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <include android:id="@id/layout_trending_1" android:layout_width="fill_parent" android:layout_height="130.0dip" android:layout_marginTop="60.0dip" app:layout_constraintStart_toStartOf="@id/v_bar_space" app:layout_constraintTop_toBottomOf="@id/v_bar_space" layout="@layout/layout_short_tv_skeleton_drawing_item_trending" />
    <include android:id="@id/layout_trending_2" android:layout_width="fill_parent" android:layout_height="130.0dip" android:layout_marginTop="16.0dip" app:layout_constraintStart_toStartOf="@id/v_title_trending" app:layout_constraintTop_toBottomOf="@id/layout_trending_1" layout="@layout/layout_short_tv_skeleton_drawing_item_trending" />
    <include android:id="@id/layout_trending_3" android:layout_width="fill_parent" android:layout_height="130.0dip" android:layout_marginTop="16.0dip" app:layout_constraintStart_toStartOf="@id/v_title_trending" app:layout_constraintTop_toBottomOf="@id/layout_trending_2" layout="@layout/layout_short_tv_skeleton_drawing_item_trending" />
    <include android:id="@id/layout_trending_4" android:layout_width="fill_parent" android:layout_height="130.0dip" android:layout_marginTop="16.0dip" app:layout_constraintStart_toStartOf="@id/v_title_trending" app:layout_constraintTop_toBottomOf="@id/layout_trending_3" layout="@layout/layout_short_tv_skeleton_drawing_item_trending" />
    <include android:id="@id/layout_trending_5" android:layout_width="fill_parent" android:layout_height="130.0dip" android:layout_marginTop="16.0dip" app:layout_constraintStart_toStartOf="@id/v_title_trending" app:layout_constraintTop_toBottomOf="@id/layout_trending_4" layout="@layout/layout_short_tv_skeleton_drawing_item_trending" />
    <include android:id="@id/layout_trending_6" android:layout_width="fill_parent" android:layout_height="130.0dip" android:layout_marginTop="16.0dip" app:layout_constraintStart_toStartOf="@id/v_title_trending" app:layout_constraintTop_toBottomOf="@id/layout_trending_5" layout="@layout/layout_short_tv_skeleton_drawing_item_trending" />
</com.transsion.baseui.widget.shimmer.ShimmerConstraintLayout>
