<?xml version="1.0" encoding="utf-8"?>
<androidx.appcompat.widget.LinearLayoutCompat android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/ivDefaultImage" android:layout_width="fill_parent" android:layout_height="wrap_content" android:src="@mipmap/ic_no_network" />
    <TextView android:textColor="@color/cl34" android:gravity="center" android:layout_gravity="center_horizontal" android:id="@id/tvDesc" android:layout_width="fill_parent" android:layout_height="wrap_content" android:text="@string/no_network_toast" android:layout_marginStart="16.0dip" android:layout_marginEnd="16.0dip" style="@style/style_regular_text" />
    <TextView android:textSize="@dimen/sp_14" android:textColor="@color/text_01" android:gravity="center" android:layout_gravity="center_horizontal" android:id="@id/tv_retry" android:background="@drawable/btn_gray" android:paddingLeft="32.0dip" android:paddingRight="32.0dip" android:layout_width="wrap_content" android:layout_height="40.0dip" android:layout_marginTop="16.0dip" android:layout_marginBottom="60.0dip" android:text="@string/home_retry_text" style="@style/robot_medium" />
</androidx.appcompat.widget.LinearLayoutCompat>
