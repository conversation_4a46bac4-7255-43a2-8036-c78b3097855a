<?xml version="1.0" encoding="utf-8"?>
<merge android:layout_width="fill_parent" android:layout_height="wrap_content" android:paddingEnd="8.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/ivBack" android:paddingTop="16.0dip" android:paddingBottom="16.0dip" android:layout_width="wrap_content" android:layout_height="48.0dip" android:src="@drawable/subtitle_left" android:layout_marginStart="12.0dip" app:layout_constraintEnd_toStartOf="@id/tvStyle" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" style="@style/style_medium_text" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textColor="@color/white" android:ellipsize="end" android:gravity="center_vertical" android:id="@id/tvStyle" android:layout_width="0.0dip" android:layout_height="wrap_content" android:text="@string/subtitle_style_style" android:layout_marginStart="2.0dip" app:layout_constraintBottom_toBottomOf="@id/ivBack" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/ivBack" app:layout_constraintTop_toTopOf="@id/ivBack" style="@style/style_import_text" />
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/white" android:id="@id/tvFontColorTitle" android:layout_width="0.0dip" android:text="@string/font_color" android:layout_marginStart="32.0dip" android:layout_marginEnd="16.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/ivBack" style="@style/style_medium_text" />
    <RadioGroup android:orientation="horizontal" android:id="@id/rg_font_color" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginTop="11.0dip" android:layout_marginStart="16.0dip" app:layout_constraintEnd_toEndOf="@id/tvFontColorTitle" app:layout_constraintStart_toStartOf="@id/tvFontColorTitle" app:layout_constraintTop_toBottomOf="@id/tvFontColorTitle">
        <RadioButton android:id="@id/rb_font_color_black" android:background="@drawable/post_detail_selector_subtitle_options_black" android:layout_width="28.0dip" android:layout_height="28.0dip" android:button="@null" />
        <android.widget.Space android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_weight="1.0" />
        <RadioButton android:id="@id/rb_font_color_white" android:background="@drawable/post_detail_selector_subtitle_options_white" android:layout_width="28.0dip" android:layout_height="28.0dip" android:checked="true" android:button="@null" />
        <android.widget.Space android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_weight="1.0" />
        <RadioButton android:id="@id/rb_font_color_yellor" android:background="@drawable/post_detail_selector_subtitle_options_yellow" android:layout_width="28.0dip" android:layout_height="28.0dip" android:button="@null" />
        <android.widget.Space android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_weight="1.0" />
        <RadioButton android:id="@id/rb_font_color_green" android:background="@drawable/post_detail_selector_subtitle_options_green" android:layout_width="28.0dip" android:layout_height="28.0dip" android:button="@null" />
    </RadioGroup>
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/white" android:id="@id/tv_font_size_title" android:layout_width="0.0dip" android:layout_marginTop="24.0dip" android:text="@string/font_size" app:layout_constraintEnd_toEndOf="@id/tvFontColorTitle" app:layout_constraintStart_toStartOf="@id/tvFontColorTitle" app:layout_constraintTop_toBottomOf="@id/rg_font_color" style="@style/style_medium_text" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_font_size_minus" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="8.0dip" android:src="@mipmap/ic_subtitle_options_minus" app:layout_constraintEnd_toStartOf="@id/seek_bar_font_size" app:layout_constraintHorizontal_chainStyle="packed" app:layout_constraintStart_toStartOf="@id/tvFontColorTitle" app:layout_constraintTop_toBottomOf="@id/tv_font_size_title" />
    <com.tn.lib.view.SecondariesSeekBar android:id="@id/progress_bar_font_size" android:background="@color/transparent" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginStart="2.0dip" android:layout_marginEnd="2.0dip" app:layout_constraintBottom_toBottomOf="@id/seek_bar_font_size" app:layout_constraintEnd_toEndOf="@id/seek_bar_font_size" app:layout_constraintStart_toStartOf="@id/seek_bar_font_size" app:layout_constraintTop_toTopOf="@id/seek_bar_font_size" app:ssb_bar_center_color="@color/main_gradient_center" app:ssb_bar_end_color="@color/main_gradient_end" app:ssb_bar_start_color="@color/main_gradient_start" app:ssb_bg_color="@color/white_30" app:ssb_max="6" app:ssb_progress="2" app:ssb_progress_size="3.0dip" app:ssb_secondaries_color="@color/white" app:ssb_seek_enable="false" app:ssb_thumb_color="@color/white" app:ssb_thumb_size="15.0dip" />
    <com.warkiz.widget.IndicatorSeekBar android:id="@id/seek_bar_font_size" android:layout_width="0.0dip" android:layout_height="50.0dip" android:paddingStart="12.0dip" android:paddingEnd="12.0dip" app:isb_max="6.0" app:isb_min="0.0" app:isb_progress="2.0" app:isb_show_indicator="none" app:isb_thumb_drawable="@drawable/post_detail_shape_subtitle_options_thumb" app:isb_thumb_size="15.0dip" app:isb_tick_marks_drawable="@drawable/post_detail_shape_subtitle_options_marks" app:isb_ticks_count="7" app:isb_track_background_color="@color/transparent" app:isb_track_background_size="3.0dip" app:isb_track_progress_color="@color/transparent" app:isb_track_progress_size="3.0dip" app:layout_constraintBottom_toBottomOf="@id/iv_font_size_minus" app:layout_constraintEnd_toStartOf="@id/iv_font_size_add" app:layout_constraintStart_toEndOf="@id/iv_font_size_minus" app:layout_constraintTop_toTopOf="@id/iv_font_size_minus" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_font_size_add" android:layout_width="wrap_content" android:layout_height="wrap_content" android:src="@mipmap/ic_subtitle_options_add" app:layout_constraintBottom_toBottomOf="@id/iv_font_size_minus" app:layout_constraintEnd_toEndOf="@id/tvFontColorTitle" app:layout_constraintStart_toEndOf="@id/seek_bar_font_size" app:layout_constraintTop_toTopOf="@id/iv_font_size_minus" />
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/white" android:id="@id/tv_position_title" android:layout_width="0.0dip" android:layout_marginTop="24.0dip" android:text="@string/title_position" app:layout_constraintEnd_toEndOf="@id/tvFontColorTitle" app:layout_constraintStart_toStartOf="@id/tvFontColorTitle" app:layout_constraintTop_toBottomOf="@id/iv_font_size_minus" style="@style/style_medium_text" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_position_down" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="8.0dip" android:src="@mipmap/ic_subtitle_options_down" app:layout_constraintEnd_toStartOf="@id/seek_bar_position" app:layout_constraintHorizontal_chainStyle="packed" app:layout_constraintStart_toStartOf="@id/tvFontColorTitle" app:layout_constraintTop_toBottomOf="@id/tv_position_title" />
    <com.tn.lib.view.SecondariesSeekBar android:id="@id/seek_bar_position" android:background="@color/transparent" android:layout_width="0.0dip" android:layout_height="wrap_content" app:layout_constraintBottom_toBottomOf="@id/iv_position_down" app:layout_constraintEnd_toStartOf="@id/iv_position_up" app:layout_constraintStart_toEndOf="@id/iv_position_down" app:layout_constraintTop_toTopOf="@id/iv_position_down" app:ssb_bar_center_color="@color/main_gradient_center" app:ssb_bar_end_color="@color/main_gradient_end" app:ssb_bar_start_color="@color/main_gradient_start" app:ssb_bg_color="@color/white_30" app:ssb_max="100" app:ssb_progress_size="3.0dip" app:ssb_secondaries_color="@color/white" app:ssb_thumb_color="@color/white" app:ssb_thumb_enlarge="1.0" app:ssb_thumb_size="15.0dip" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_position_up" android:layout_width="wrap_content" android:layout_height="wrap_content" android:src="@mipmap/ic_subtitle_options_down" android:rotation="180.0" app:layout_constraintBottom_toBottomOf="@id/iv_position_down" app:layout_constraintEnd_toEndOf="@id/tvFontColorTitle" app:layout_constraintStart_toEndOf="@id/seek_bar_position" app:layout_constraintTop_toTopOf="@id/iv_position_down" />
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/white" android:id="@id/tv_shadow_title" android:text="@string/title_shadow" app:layout_constraintBottom_toBottomOf="@id/switch_shadow" app:layout_constraintStart_toStartOf="@id/tvFontColorTitle" app:layout_constraintTop_toTopOf="@id/switch_shadow" style="@style/style_medium_text" />
    <com.tn.lib.view.SwitchButton android:id="@id/switch_shadow" android:layout_width="48.0dip" android:layout_height="24.0dip" android:layout_marginTop="24.0dip" app:layout_constraintEnd_toEndOf="@id/tvFontColorTitle" app:layout_constraintTop_toBottomOf="@id/iv_position_up" />
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/white" android:id="@id/tv_background_title" android:text="@string/title_background" app:layout_constraintBottom_toBottomOf="@id/switch_background" app:layout_constraintStart_toStartOf="@id/tvFontColorTitle" app:layout_constraintTop_toTopOf="@id/switch_background" style="@style/style_medium_text" />
    <com.tn.lib.view.SwitchButton android:id="@id/switch_background" android:layout_width="48.0dip" android:layout_height="24.0dip" android:layout_marginTop="24.0dip" app:layout_constraintEnd_toEndOf="@id/tvFontColorTitle" app:layout_constraintTop_toBottomOf="@id/switch_shadow" />
    <RadioGroup android:orientation="horizontal" android:id="@id/rg_background" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginTop="11.0dip" app:layout_constraintEnd_toEndOf="@id/tvFontColorTitle" app:layout_constraintStart_toStartOf="@id/tvFontColorTitle" app:layout_constraintTop_toBottomOf="@id/switch_background">
        <RadioButton android:id="@id/rb_background_black" android:background="@drawable/post_detail_selector_subtitle_options_black" android:layout_width="28.0dip" android:layout_height="28.0dip" android:checked="true" android:button="@null" />
        <android.widget.Space android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_weight="1.0" />
        <RadioButton android:id="@id/rb_background_white" android:background="@drawable/post_detail_selector_subtitle_options_white" android:layout_width="28.0dip" android:layout_height="28.0dip" android:button="@null" />
        <android.widget.Space android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_weight="1.0" />
        <RadioButton android:id="@id/rb_background_yellor" android:background="@drawable/post_detail_selector_subtitle_options_yellow" android:layout_width="28.0dip" android:layout_height="28.0dip" android:button="@null" />
        <android.widget.Space android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_weight="1.0" />
        <RadioButton android:id="@id/rb_background_green" android:background="@drawable/post_detail_selector_subtitle_options_green" android:layout_width="28.0dip" android:layout_height="28.0dip" android:button="@null" />
    </RadioGroup>
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/white" android:id="@id/tv_background_opacity_title" android:layout_width="0.0dip" android:layout_marginTop="24.0dip" android:text="@string/title_background_opacity" app:layout_constraintEnd_toEndOf="@id/tvFontColorTitle" app:layout_constraintStart_toStartOf="@id/tvFontColorTitle" app:layout_constraintTop_toBottomOf="@id/rg_background" style="@style/style_medium_text" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_background_minus" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="8.0dip" android:src="@mipmap/ic_subtitle_options_minus" app:layout_constraintEnd_toStartOf="@id/seek_bart_background" app:layout_constraintHorizontal_chainStyle="packed" app:layout_constraintStart_toStartOf="@id/tvFontColorTitle" app:layout_constraintTop_toBottomOf="@id/tv_background_opacity_title" />
    <com.tn.lib.view.SecondariesSeekBar android:id="@id/progress_bar_background" android:background="@color/transparent" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginStart="2.0dip" android:layout_marginEnd="2.0dip" app:layout_constraintBottom_toBottomOf="@id/seek_bart_background" app:layout_constraintEnd_toEndOf="@id/seek_bart_background" app:layout_constraintStart_toStartOf="@id/seek_bart_background" app:layout_constraintTop_toTopOf="@id/seek_bart_background" app:ssb_bar_center_color="@color/main_gradient_center" app:ssb_bar_end_color="@color/main_gradient_end" app:ssb_bar_start_color="@color/main_gradient_start" app:ssb_bg_color="@color/white_30" app:ssb_max="4" app:ssb_progress="3" app:ssb_progress_size="3.0dip" app:ssb_secondaries_color="@color/white" app:ssb_seek_enable="false" app:ssb_thumb_color="@color/white" app:ssb_thumb_size="15.0dip" />
    <com.warkiz.widget.IndicatorSeekBar android:id="@id/seek_bart_background" android:layout_width="0.0dip" android:layout_height="wrap_content" android:paddingStart="12.0dip" android:paddingEnd="12.0dip" app:isb_max="4.0" app:isb_min="0.0" app:isb_progress="3.0" app:isb_show_indicator="none" app:isb_thumb_drawable="@drawable/post_detail_shape_subtitle_options_thumb" app:isb_thumb_size="15.0dip" app:isb_tick_marks_drawable="@drawable/post_detail_shape_subtitle_options_marks" app:isb_ticks_count="5" app:isb_track_background_color="@color/transparent" app:isb_track_background_size="3.0dip" app:isb_track_progress_color="@color/transparent" app:isb_track_progress_size="3.0dip" app:layout_constraintBottom_toBottomOf="@id/iv_background_minus" app:layout_constraintEnd_toStartOf="@id/iv_background_add" app:layout_constraintStart_toEndOf="@id/iv_background_minus" app:layout_constraintTop_toTopOf="@id/iv_background_minus" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_background_add" android:layout_width="wrap_content" android:layout_height="wrap_content" android:src="@mipmap/ic_subtitle_options_add" app:layout_constraintBottom_toBottomOf="@id/iv_background_minus" app:layout_constraintEnd_toEndOf="@id/tvFontColorTitle" app:layout_constraintStart_toEndOf="@id/seek_bart_background" app:layout_constraintTop_toTopOf="@id/iv_background_minus" />
    <androidx.constraintlayout.widget.Group android:id="@id/group_background" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" app:constraint_referenced_ids="rg_background,         tv_background_opacity_title,iv_background_minus,         iv_background_add,progress_bar_background,seek_bart_background" />
    <com.noober.background.view.BLTextView android:textColor="@color/white" android:gravity="center" android:id="@id/tv_reset" android:layout_height="32.0dip" android:layout_marginTop="24.0dip" android:minWidth="84.0dip" android:text="@string/reset" android:paddingStart="24.0dip" android:paddingEnd="24.0dip" app:bl_corners_radius="4.0dip" app:bl_selected_solid_color="@color/white_10" app:bl_selected_textColor="@color/subtitle_reset_text_selected" app:bl_unSelected_solid_color="@color/white_10" app:bl_unSelected_textColor="@color/subtitle_reset_text_unselected" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/iv_background_minus" style="@style/style_medium_text" />
    <android.widget.Space android:layout_width="fill_parent" android:layout_height="32.0dip" app:layout_constraintTop_toBottomOf="@id/tv_reset" />
</merge>
