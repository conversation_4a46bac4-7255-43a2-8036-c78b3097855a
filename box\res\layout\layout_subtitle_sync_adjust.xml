<?xml version="1.0" encoding="utf-8"?>
<androidx.appcompat.widget.LinearLayoutCompat android:orientation="vertical" android:id="@id/ll_sync_adjust" android:focusable="true" android:visibility="gone" android:clickable="true" android:layout_width="64.0dip" android:layout_height="wrap_content" android:layout_marginEnd="24.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.appcompat.widget.AppCompatImageView android:layout_gravity="end" android:id="@id/iv_close" android:layout_width="28.0dip" android:layout_height="28.0dip" android:scaleType="center" android:layout_marginStart="16.0dip" app:srcCompat="@mipmap/ad_close" />
    <androidx.appcompat.widget.LinearLayoutCompat android:gravity="center" android:orientation="vertical" android:background="@drawable/post_detail_shape_subtitle_list_dialog_main_bg" android:layout_width="60.0dip" android:layout_height="144.0dip">
        <androidx.appcompat.widget.AppCompatImageView android:layout_gravity="center_horizontal" android:id="@id/iv_sync_adjust_plus" android:background="?selectableItemBackgroundBorderless" android:layout_width="fill_parent" android:layout_height="50.0dip" android:src="@mipmap/ic_subtitle_sync_adjust_plus" android:scaleType="center" />
        <EditText android:textSize="14.0sp" android:textColor="@color/white" android:gravity="center" android:id="@id/et_sync_adjust" android:layout_width="fill_parent" android:layout_height="wrap_content" android:lines="1" android:scrollHorizontally="true" android:inputType="numberDecimal" android:backgroundTint="@color/white" style="@style/style_medium_text" />
        <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_sync_adjust_minus" android:background="?selectableItemBackgroundBorderless" android:layout_width="fill_parent" android:layout_height="50.0dip" android:src="@mipmap/ic_subtitle_sync_adjust_minus" android:scaleType="center" />
    </androidx.appcompat.widget.LinearLayoutCompat>
</androidx.appcompat.widget.LinearLayoutCompat>
