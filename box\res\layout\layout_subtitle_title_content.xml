<?xml version="1.0" encoding="utf-8"?>
<merge
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <TextView android:textColor="@color/white" android:id="@id/tv_title" android:layout_marginStart="16.0dip" app:layout_constraintBottom_toTopOf="@id/v_title_line" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" style="@style/style_regula_bigger_text" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_title_close" android:layout_width="32.0dip" android:layout_height="32.0dip" android:scaleType="center" android:layout_marginEnd="16.0dip" app:layout_constraintBottom_toTopOf="@id/v_title_line" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent" app:srcCompat="@mipmap/ic_close" />
    <View android:id="@id/v_title_line" android:background="@color/white_10" android:layout_width="fill_parent" android:layout_height="1.0dip" android:layout_marginTop="47.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
</merge>
