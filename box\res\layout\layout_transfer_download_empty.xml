<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_empty" android:layout_width="200.0dip" android:layout_height="120.0dip" android:layout_marginTop="134.0dip" android:src="@mipmap/ic_no_content" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <com.tn.lib.widget.TnTextView android:textSize="14.0sp" android:textColor="@color/text_03" android:id="@id/tv_reset" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/newcomer_guide_download_tips" app:layout_constraintBottom_toBottomOf="@id/iv_empty" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" />
    <com.noober.background.view.BLTextView android:textColor="@color/text_01" android:gravity="center_vertical" android:id="@id/tv_transfer" android:paddingLeft="12.0dip" android:paddingRight="12.0dip" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="32.0dip" android:layout_marginTop="12.0dip" android:text="@string/download_empty_transfer_tips" android:drawablePadding="4.0dip" app:bl_corners_radius="8.0dip" app:bl_solid_color="@color/module_04" app:drawableStartCompat="@mipmap/ic_download_empty_transfer" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tv_reset" style="@style/style_medium_text" />
</androidx.constraintlayout.widget.ConstraintLayout>
