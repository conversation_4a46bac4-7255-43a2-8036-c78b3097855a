<?xml version="1.0" encoding="utf-8"?>
<com.tn.lib.view.bubbleview.BubbleGradientLinearLayout android:orientation="horizontal" android:layout_width="wrap_content" android:layout_height="wrap_content" app:angle="8.0dip" app:arrowHeight="8.0dip" app:arrowLocation="top" app:arrowPosition="225.0dip" app:arrowWidth="12.0dip" app:gradientCenterColor="@color/main_gradient_center" app:gradientEndColor="@color/main_gradient_end" app:gradientStartColor="@color/main_gradient_start" app:gradient_orientation="horizontal"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <TextView android:textSize="14.0sp" android:textColor="@color/white" android:layout_gravity="center_vertical" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginTop="14.0dip" android:layout_marginBottom="8.0dip" android:text="@string/download_tab_transfer_tips" android:layout_weight="1.0" android:layout_marginStart="12.0dip" android:layout_marginEnd="6.0dip" />
    <ImageView android:id="@id/transfer_tips_close" android:padding="6.0dip" android:layout_width="32.0dip" android:layout_height="32.0dip" android:layout_marginTop="12.0dip" android:src="@mipmap/ic_guide_close" android:scaleType="fitXY" android:importantForAccessibility="no" android:layout_marginEnd="12.0dip" />
</com.tn.lib.view.bubbleview.BubbleGradientLinearLayout>
