<?xml version="1.0" encoding="utf-8"?>
<FrameLayout android:background="@color/white" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.appcompat.widget.AppCompatImageView android:layout_width="fill_parent" android:layout_height="360.0dip" android:scaleType="centerCrop" app:layout_constraintTop_toTopOf="parent" app:srcCompat="@mipmap/bg_user_prefer" />
    <LinearLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="fill_parent">
        <FrameLayout android:layout_gravity="end" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="48.0dip" android:layout_marginEnd="16.0dip">
            <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/cl32" android:id="@id/tv_skip" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/user_perfer_skip" style="@style/style_medium_text" />
            <ProgressBar android:layout_gravity="center" android:id="@id/pb_skip" android:visibility="gone" android:layout_width="20.0dip" android:layout_height="20.0dip" android:indeterminateTint="@color/cl01" />
        </FrameLayout>
        <androidx.appcompat.widget.AppCompatTextView android:textSize="24.0sp" android:textColor="@color/cl32" android:id="@id/tv_title" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="18.0dip" android:text="@string/user_perfer_title" android:layout_marginStart="16.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tv_skip" app:layout_goneMarginTop="34.0dip" style="@style/style_medium_text" />
        <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/cl33" android:id="@id/tv_sub_title" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="8.0dip" android:text="@string/user_perfer_sub_title" android:layout_marginStart="16.0dip" android:layout_marginEnd="16.0dip" app:layout_goneMarginTop="34.0dip" style="@style/style_medium_text" />
        <androidx.recyclerview.widget.RecyclerView android:id="@id/recycler_view" android:paddingTop="20.0dip" android:layout_width="fill_parent" android:layout_height="0.0dip" android:layout_marginTop="4.0dip" android:layout_marginBottom="16.0dip" android:layout_weight="1.0" android:layout_marginStart="16.0dip" android:layout_marginEnd="16.0dip" />
        <FrameLayout android:layout_width="fill_parent" android:layout_height="36.0dip" android:layout_marginBottom="32.0dip" android:layout_marginStart="16.0dip" android:layout_marginEnd="16.0dip">
            <androidx.appcompat.widget.AppCompatTextView android:textColor="@drawable/selector_user_prefer_btn_text" android:gravity="center" android:id="@id/tv_submit" android:background="@drawable/selector_user_prefer_btn_bg" android:layout_width="fill_parent" android:layout_height="fill_parent" android:text="@string/user_perfer_submit" style="@style/style_medium_text" />
            <ProgressBar android:layout_gravity="center" android:id="@id/load_view" android:visibility="gone" android:layout_width="20.0dip" android:layout_height="20.0dip" android:indeterminateTint="@color/white" />
        </FrameLayout>
    </LinearLayout>
</FrameLayout>
