<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:id="@id/cl_root" android:fitsSystemWindows="true" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <View android:background="@color/cl45" android:layout_width="fill_parent" android:layout_height="@dimen/post_surface_height" app:layout_constraintTop_toTopOf="parent" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/vd_iv_back" android:layout_width="30.0dip" android:layout_height="30.0dip" android:layout_marginTop="7.0dip" android:layout_marginBottom="7.0dip" android:src="@mipmap/icon_white_back" android:scaleType="center" android:layout_marginStart="6.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <com.google.android.material.imageview.ShapeableImageView android:background="@color/cl38_30_p" android:layout_width="28.0dip" android:layout_height="28.0dip" android:layout_marginStart="2.0dip" app:layout_constraintBottom_toBottomOf="@id/vd_iv_back" app:layout_constraintStart_toEndOf="@id/vd_iv_back" app:layout_constraintTop_toTopOf="@id/vd_iv_back" app:shapeAppearance="@style/circle_style" />
    <com.google.android.material.imageview.ShapeableImageView android:background="@color/cl38_30_p" android:layout_width="83.0dip" android:layout_height="18.0dip" android:layout_marginStart="34.0dip" app:layout_constraintBottom_toBottomOf="@id/vd_iv_back" app:layout_constraintStart_toEndOf="@id/vd_iv_back" app:layout_constraintTop_toTopOf="@id/vd_iv_back" app:shapeAppearance="@style/circle_style" />
    <View android:id="@id/vd_bottom_bg" android:background="@color/cl38" android:layout_width="fill_parent" android:layout_height="0.0dip" android:layout_marginTop="203.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <com.google.android.material.imageview.ShapeableImageView android:background="@color/cl37" android:layout_width="240.0dip" android:layout_height="18.0dip" android:layout_marginTop="16.0dip" android:layout_marginStart="16.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="@id/vd_bottom_bg" app:shapeAppearance="@style/circle_style" />
    <com.google.android.material.imageview.ShapeableImageView android:background="@color/cl37" android:layout_width="0.0dip" android:layout_height="36.0dip" android:layout_marginTop="50.0dip" android:layout_marginStart="16.0dip" android:layout_marginEnd="16.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="@id/vd_bottom_bg" app:shapeAppearance="@style/ImgRoundedStyle_4dp" />
    <com.google.android.material.imageview.ShapeableImageView android:background="@color/cl37" android:layout_width="72.0dip" android:layout_height="8.0dip" android:layout_marginTop="102.0dip" android:layout_marginStart="16.0dip" android:layout_marginEnd="16.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="@id/vd_bottom_bg" app:shapeAppearance="@style/circle_style" />
    <com.google.android.material.imageview.ShapeableImageView android:background="@color/cl37" android:layout_width="fill_parent" android:layout_height="8.0dip" android:layout_marginTop="126.0dip" app:layout_constraintTop_toTopOf="@id/vd_bottom_bg" app:shapeAppearance="@style/circle_style" />
    <ProgressBar android:id="@id/loading_progress" android:layout_width="23.0dip" android:layout_height="23.0dip" android:indeterminateTint="@color/cl01" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>
