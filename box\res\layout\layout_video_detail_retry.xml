<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:id="@id/vd_retry_root" android:visibility="gone" android:fitsSystemWindows="true" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <FrameLayout android:background="@color/cl45" android:layout_width="fill_parent" android:layout_height="@dimen/post_surface_height" app:layout_constraintTop_toTopOf="parent">
        <LinearLayout android:gravity="center" android:layout_gravity="center" android:orientation="vertical" android:layout_width="wrap_content" android:layout_height="wrap_content">
            <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0dip" android:textColor="@color/white" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/post_failed_load" />
            <androidx.appcompat.widget.AppCompatTextView android:id="@id/vd_btn_retry" android:layout_width="96.0dip" android:layout_height="36.0dip" android:layout_marginTop="16.0dip" android:text="@string/post_retry" style="@style/style_main_btn_h36" />
        </LinearLayout>
    </FrameLayout>
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/vd_iv_back" android:layout_width="30.0dip" android:layout_height="30.0dip" android:layout_marginTop="7.0dip" android:layout_marginBottom="7.0dip" android:src="@mipmap/icon_white_back" android:scaleType="center" android:layout_marginStart="6.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <com.google.android.material.imageview.ShapeableImageView android:background="@color/cl38_30_p" android:layout_width="28.0dip" android:layout_height="28.0dip" android:layout_marginStart="2.0dip" app:layout_constraintBottom_toBottomOf="@id/vd_iv_back" app:layout_constraintStart_toEndOf="@id/vd_iv_back" app:layout_constraintTop_toTopOf="@id/vd_iv_back" app:shapeAppearance="@style/circle_style" />
    <com.google.android.material.imageview.ShapeableImageView android:background="@color/cl38_30_p" android:layout_width="83.0dip" android:layout_height="18.0dip" android:layout_marginStart="34.0dip" app:layout_constraintBottom_toBottomOf="@id/vd_iv_back" app:layout_constraintStart_toEndOf="@id/vd_iv_back" app:layout_constraintTop_toTopOf="@id/vd_iv_back" app:shapeAppearance="@style/circle_style" />
    <View android:id="@id/vd_bottom_bg" android:background="@color/cl38" android:layout_width="fill_parent" android:layout_height="0.0dip" android:layout_marginTop="203.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <com.google.android.material.imageview.ShapeableImageView android:background="@color/cl37" android:layout_width="240.0dip" android:layout_height="18.0dip" android:layout_marginTop="16.0dip" android:layout_marginStart="16.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="@id/vd_bottom_bg" app:shapeAppearance="@style/circle_style" />
    <com.google.android.material.imageview.ShapeableImageView android:background="@color/cl37" android:layout_width="0.0dip" android:layout_height="36.0dip" android:layout_marginTop="50.0dip" android:layout_marginStart="16.0dip" android:layout_marginEnd="16.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="@id/vd_bottom_bg" app:shapeAppearance="@style/ImgRoundedStyle_4dp" />
    <com.google.android.material.imageview.ShapeableImageView android:background="@color/cl37" android:layout_width="72.0dip" android:layout_height="8.0dip" android:layout_marginTop="102.0dip" android:layout_marginStart="16.0dip" android:layout_marginEnd="16.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="@id/vd_bottom_bg" app:shapeAppearance="@style/circle_style" />
    <com.google.android.material.imageview.ShapeableImageView android:background="@color/cl37" android:layout_width="fill_parent" android:layout_height="8.0dip" android:layout_marginTop="126.0dip" app:layout_constraintTop_toTopOf="@id/vd_bottom_bg" app:shapeAppearance="@style/circle_style" />
</androidx.constraintlayout.widget.ConstraintLayout>
