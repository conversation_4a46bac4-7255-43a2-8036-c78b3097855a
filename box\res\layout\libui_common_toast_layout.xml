<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:gravity="center" android:orientation="vertical" android:background="@drawable/bg_toast" android:layout_width="wrap_content" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <TextView android:textSize="12.0sp" android:textColor="@color/cl41" android:gravity="center" android:layout_gravity="center" android:id="@android:id/message" android:paddingTop="12.0dip" android:paddingBottom="12.0dip" android:layout_width="wrap_content" android:layout_height="wrap_content" android:paddingStart="20.0dip" android:paddingEnd="20.0dip" />
</LinearLayout>
