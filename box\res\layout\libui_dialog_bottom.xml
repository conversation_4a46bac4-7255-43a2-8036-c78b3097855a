<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:id="@id/root" android:background="@drawable/libui_bottom_dialog_bg_grey" android:layout_width="fill_parent" android:layout_height="wrap_content" android:minHeight="60.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <View android:layout_width="fill_parent" android:layout_height="200.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>
