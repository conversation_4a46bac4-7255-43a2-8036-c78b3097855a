<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.constraintlayout.widget.ConstraintLayout android:background="@drawable/libui_shape_round_corner_black" android:paddingBottom="12.0dip" android:layout_width="140.0dip" android:layout_height="100.0dip" android:layout_centerInParent="true">
        <com.tn.lib.view.YuanProgressBar android:textSize="14.0sp" android:textColor="@color/cl41" android:id="@id/yuan_progress_bar" android:layout_width="55.0dip" android:layout_height="55.0dip" android:layout_marginTop="14.0dip" android:lines="1" app:base_circleColor="@color/color_7d7b81" app:base_yuanCircleStrokeWidth="2.0dip" app:base_yuanProgressStrokeWidth="2.0dip" app:base_yuan_progressColor="@color/cl38" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
        <TextView android:textSize="14.0sp" android:textColor="@color/cl42" android:id="@id/yuan_txt_progress" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/loding" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" />
        <FrameLayout android:id="@id/fl_cancel" android:background="@color/transparent" android:visibility="gone" android:layout_width="30.0dip" android:layout_height="30.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent">
            <ImageView android:layout_gravity="end|top" android:id="@id/iv_cancel" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="4.0dip" android:src="@mipmap/libui_ic_basic_close_white" android:layout_marginEnd="4.0dip" />
        </FrameLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</RelativeLayout>
