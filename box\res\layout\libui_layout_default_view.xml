<?xml version="1.0" encoding="utf-8"?>
<merge android:gravity="center|top" android:orientation="vertical" android:paddingTop="96.0dip" android:layout_width="fill_parent" android:layout_height="fill_parent" android:paddingStart="26.0dip" android:paddingEnd="26.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.tn.lib.view.TRImageView android:id="@id/iv_default_image" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginBottom="16.0dip" android:src="@color/cl36" />
    <TextView android:textColor="@color/text_01" android:id="@id/tv_default_title" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginBottom="8.0dip" style="@style/style_medium_text" />
    <TextView android:textColor="@color/text_03" android:gravity="center" android:id="@id/tv_desc" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginBottom="24.0dip" style="@style/style_regular_text" />
    <LinearLayout android:id="@id/ll_btn" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginBottom="16.0dip" android:layout_marginStart="12.0dip" android:layout_marginEnd="12.0dip">
        <TextView android:textColor="@color/text_01" android:gravity="center" android:id="@id/btn_left" android:background="@drawable/btn_gray" android:visibility="gone" android:layout_width="133.0dip" android:layout_height="36.0dip" android:layout_marginStart="4.0dip" android:layout_marginEnd="4.0dip" style="@style/style_medium_text" />
        <TextView android:textColor="@color/text_01" android:gravity="center" android:id="@id/btn_right" android:background="@drawable/btn_gray" android:visibility="gone" android:layout_width="133.0dip" android:layout_height="36.0dip" android:layout_marginStart="4.0dip" android:layout_marginEnd="4.0dip" style="@style/style_medium_text" />
    </LinearLayout>
    <androidx.appcompat.widget.LinearLayoutCompat android:gravity="center_vertical" android:id="@id/ll_tip" android:layout_width="wrap_content" android:layout_height="wrap_content">
        <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/error_red" android:id="@id/tv_tip_operation" android:layout_width="wrap_content" android:layout_height="wrap_content" style="@style/style_regular_text" />
        <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/error_red" android:id="@id/tv_tip_icon" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginBottom="-1.0dip" android:rotationY="@integer/angle_rtl_180" android:layout_marginStart="2.0dip" app:drawableEndCompat="@mipmap/ic_right_black" style="@style/style_regular_text" />
    </androidx.appcompat.widget.LinearLayoutCompat>
</merge>
