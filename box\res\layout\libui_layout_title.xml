<?xml version="1.0" encoding="utf-8"?>
<merge android:id="@id/cl_titleBar" android:layout_width="fill_parent" android:layout_height="44.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_back" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="44.0dip" android:contentDescription="@null" android:paddingStart="16.0dip" android:paddingEnd="8.0dip" app:layout_constraintStart_toStartOf="parent" app:srcCompat="@mipmap/libui_ic_back_black" app:tint="@color/text_01" />
    <ImageView android:id="@id/iv_menu" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="fill_parent" android:src="@android:drawable/ic_menu_add" android:contentDescription="@null" android:paddingStart="20.0dip" android:paddingEnd="15.0dip" app:layout_constraintBottom_toBottomOf="@id/iv_back" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="@id/iv_back" app:tint="@color/text_01" />
    <TextView android:textSize="14.0sp" android:textColor="@color/text_01" android:gravity="center" android:id="@id/tv_menu" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="fill_parent" android:maxEms="7" android:singleLine="true" android:paddingStart="15.0dip" android:paddingEnd="15.0dip" app:layout_constraintBottom_toBottomOf="@id/iv_back" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="@id/iv_back" />
    <com.tn.lib.widget.TnTextView android:textColor="@color/text_01" android:ellipsize="end" android:gravity="center" android:id="@id/tv_titleText" android:maxLines="1" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" style="@style/style_title_text" />
    <View android:id="@id/view_red" android:visibility="gone" android:layout_width="10.0dip" android:layout_height="10.0dip" android:layout_marginBottom="14.0dip" android:layout_marginEnd="14.0dip" app:layout_constraintBottom_toBottomOf="@id/iv_back" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="@id/iv_back" />
    <View android:id="@id/view_line" android:background="@color/line_01" android:layout_width="fill_parent" android:layout_height="1.0px" app:layout_constraintBottom_toBottomOf="parent" />
</merge>
