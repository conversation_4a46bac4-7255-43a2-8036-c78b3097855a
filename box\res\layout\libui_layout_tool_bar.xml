<?xml version="1.0" encoding="utf-8"?>
<merge android:background="@color/cl38" android:layout_width="fill_parent" android:layout_height="@dimen/toolbar_height"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.tn.lib.view.TRImageView android:id="@id/iv_back" android:padding="10.0dip" android:layout_width="wrap_content" android:layout_height="wrap_content" android:rotationY="@integer/angle_rtl_180" android:layout_marginStart="6.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <androidx.appcompat.widget.AppCompatTextView android:gravity="center" android:id="@id/tv_title" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" style="@style/style_title_text" />
    <com.tn.lib.view.TRImageView android:id="@id/iv_right_action1" android:paddingTop="10.0dip" android:paddingBottom="10.0dip" android:visibility="gone" android:layout_width="28.0dip" android:layout_height="@dimen/toolbar_height" android:src="@mipmap/libui_ic_edit" android:rotationY="@integer/angle_rtl_180" android:paddingStart="2.0dip" android:paddingEnd="2.0dip" android:layout_marginEnd="14.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <com.tn.lib.view.TRImageView android:id="@id/iv_right_action2" android:paddingTop="10.0dip" android:paddingBottom="10.0dip" android:visibility="gone" android:layout_width="28.0dip" android:layout_height="@dimen/toolbar_height" android:src="@mipmap/libui_ic_edit" android:rotationY="@integer/angle_rtl_180" android:paddingStart="2.0dip" android:paddingEnd="2.0dip" android:layout_marginEnd="10.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toStartOf="@id/iv_right_action1" app:layout_constraintTop_toTopOf="parent" />
    <FrameLayout android:id="@id/vp_right_action3" android:paddingTop="10.0dip" android:paddingBottom="10.0dip" android:visibility="gone" android:layout_width="28.0dip" android:layout_height="@dimen/toolbar_height" android:src="@mipmap/libui_ic_edit" android:rotationY="@integer/angle_rtl_180" android:paddingStart="2.0dip" android:paddingEnd="2.0dip" android:layout_marginEnd="10.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toStartOf="@id/iv_right_action2" app:layout_constraintTop_toTopOf="parent" />
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/cl34" android:gravity="center" android:id="@id/tv_right_action" android:layout_height="fill_parent" android:layout_marginEnd="@dimen/right_margin" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent" style="@style/style_regula_bigger_text" />
    <View android:id="@id/view_line" android:background="@color/color_eeeeee" android:layout_width="fill_parent" android:layout_height="1.0dip" app:layout_constraintBottom_toBottomOf="parent" />
</merge>
