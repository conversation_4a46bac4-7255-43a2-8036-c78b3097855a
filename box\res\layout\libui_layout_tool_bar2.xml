<?xml version="1.0" encoding="utf-8"?>
<merge android:background="@color/bg_01" android:layout_width="fill_parent" android:layout_height="@dimen/toolbar_height"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <TextView android:textSize="24.0sp" android:textColor="@color/text_01" android:id="@id/tv_back" android:rotationY="@integer/angle_rtl_180" android:layout_marginStart="@dimen/left_margin" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" style="@style/style_medium_text" />
    <androidx.appcompat.widget.AppCompatTextView android:gravity="center" android:id="@id/tv_title" android:visibility="gone" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" style="@style/style_title_text" />
    <TextView android:textSize="24.0sp" android:textColor="@color/text_01" android:gravity="center" android:id="@id/tv_right_action1" android:rotationY="@integer/angle_rtl_180" android:layout_marginEnd="14.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent" style="@style/style_medium_text" />
    <TextView android:textSize="24.0sp" android:textColor="@color/text_01" android:gravity="center" android:id="@id/tv_right_action2" android:rotationY="@integer/angle_rtl_180" android:layout_marginEnd="10.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toStartOf="@id/tv_right_action1" app:layout_constraintTop_toTopOf="parent" style="@style/style_medium_text" />
    <FrameLayout android:id="@id/vp_right_action3" android:paddingTop="10.0dip" android:paddingBottom="10.0dip" android:layout_width="28.0dip" android:layout_height="@dimen/toolbar_height" android:rotationY="@integer/angle_rtl_180" android:paddingStart="2.0dip" android:paddingEnd="2.0dip" android:layout_marginEnd="10.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toStartOf="@id/tv_right_action2" app:layout_constraintTop_toTopOf="parent" />
    <View android:id="@id/view_line" android:background="@color/line_01" android:layout_width="fill_parent" android:layout_height="1.0dip" app:layout_constraintBottom_toBottomOf="parent" />
</merge>
