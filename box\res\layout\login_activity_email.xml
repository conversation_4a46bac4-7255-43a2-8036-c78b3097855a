<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:orientation="vertical" android:background="@color/bg_01" android:fitsSystemWindows="true" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <include android:id="@id/title" layout="@layout/login_layout_progress_title" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="24.0sp" android:textColor="@color/text_01" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="24.0dip" android:text="@string/login_continue_with_email" android:layout_marginStart="24.0dip" style="@style/style_medium_small_text" />
    <FrameLayout android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginLeft="24.0dip" android:layout_marginTop="24.0dip" android:layout_marginRight="24.0dip">
        <androidx.appcompat.widget.AppCompatEditText android:textSize="16.0sp" android:textColor="@color/text_01" android:textColorHint="@color/login_color_et_hint" android:gravity="start|center" android:id="@id/et_email" android:background="@null" android:focusable="true" android:layout_width="fill_parent" android:layout_height="45.0dip" android:hint="@string/login_enter_email_hint" android:singleLine="true" android:inputType="textEmailAddress" android:imeOptions="actionUnspecified|normal" android:textDirection="locale" android:textAlignment="viewStart" style="@style/LoginEditTextTheme" />
        <androidx.appcompat.widget.AppCompatImageButton android:layout_gravity="end|center" android:id="@id/btn_clear" android:background="@null" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="45.0dip" android:src="@mipmap/login_clear" android:tint="@color/text_03" android:text="@string/login_phone_code_resend" android:paddingStart="10.0dip" android:paddingEnd="10.0dip" app:layout_constraintBottom_toBottomOf="@id/tv_phone_country_code" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="@id/tv_phone_country_code" />
    </FrameLayout>
    <View android:layout_gravity="bottom" android:background="@color/line_01" android:layout_width="fill_parent" android:layout_height="1.0dip" android:layout_marginLeft="24.0dip" android:layout_marginRight="24.0dip" app:layout_constraintTop_toBottomOf="@id/ll_input" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/base_color_FA5546" android:id="@id/tv_tips" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="15.0dip" android:text="@string/login_email_err" android:layout_marginStart="24.0dip" android:layout_marginEnd="24.0dip" style="@style/style_regular_text" />
    <androidx.appcompat.widget.AppCompatButton android:textColor="@color/base_color_white" android:gravity="center" android:layout_gravity="end" android:id="@id/btn_next" android:background="@drawable/login_selector_login_btn" android:layout_width="fill_parent" android:layout_height="40.0dip" android:layout_marginLeft="24.0dip" android:layout_marginTop="16.0dip" android:layout_marginRight="24.0dip" android:text="@string/login_send_code" android:textAllCaps="false" app:layout_constraintBottom_toBottomOf="parent" style="@style/style_medium_text" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/login_color_policy" android:gravity="center" android:layout_gravity="center_horizontal" android:id="@id/tv_privacy" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="16.0dip" android:text="@string/login_sign_up_privacy" android:lineSpacingExtra="5.0dip" android:layout_marginStart="24.0dip" android:layout_marginEnd="24.0dip" />
</LinearLayout>
