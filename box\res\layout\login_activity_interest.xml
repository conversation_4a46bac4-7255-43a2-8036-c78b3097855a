<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:orientation="vertical" android:fitsSystemWindows="true" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <androidx.appcompat.widget.AppCompatTextView android:textSize="14.0sp" android:textColor="@color/base_color_333333" android:gravity="center" android:layout_gravity="end" android:id="@id/tv_skip" android:layout_width="wrap_content" android:layout_height="42.0dip" android:text="@string/login_skip" android:textAllCaps="true" android:paddingStart="16.0dip" android:paddingEnd="16.0dip" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="24.0sp" android:textColor="#ff000000" android:gravity="start" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="39.0dip" android:text="@string/login_interest_tips" android:layout_marginStart="16.0dip" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="14.0sp" android:textColor="@color/base_black_65_p" android:gravity="start" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="8.0dip" android:text="@string/login_interest_tips2" android:layout_marginStart="16.0dip" />
    <androidx.recyclerview.widget.RecyclerView android:id="@id/rv" android:layout_width="fill_parent" android:layout_height="0.0dip" android:layout_marginTop="34.0dip" android:layout_weight="1.0" android:overScrollMode="never" android:paddingStart="12.0dip" android:paddingEnd="12.0dip" />
    <androidx.appcompat.widget.AppCompatTextView android:enabled="false" android:textSize="14.0sp" android:textColor="@color/login_color_interest_btn" android:gravity="center" android:id="@id/tv_post" android:background="@drawable/login_selector_interest_bgn" android:layout_width="fill_parent" android:layout_height="42.0dip" android:layout_marginBottom="31.0dip" android:text="@string/login_interest_explore" android:layout_marginStart="68.0dip" android:layout_marginEnd="68.0dip" />
</LinearLayout>
