<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:orientation="vertical" android:background="@color/bg_01" android:fitsSystemWindows="true" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <include android:id="@id/title" layout="@layout/login_layout_progress_title" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="24.0sp" android:textColor="@color/text_01" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="24.0dip" android:text="@string/login_phone_code_enter" android:layout_marginStart="24.0dip" style="@style/style_medium_small_text" />
    <FrameLayout android:gravity="center_vertical" android:id="@id/ll_input" android:layout_width="fill_parent" android:layout_height="45.0dip" android:layout_marginTop="24.0dip" android:layout_marginStart="24.0dip" android:layout_marginEnd="24.0dip">
        <androidx.appcompat.widget.AppCompatEditText android:textSize="15.0sp" android:textColor="@color/text_06" android:textColorHint="@color/login_color_et_hint" android:gravity="start|center" android:id="@id/et_code" android:background="@null" android:focusable="true" android:layout_width="fill_parent" android:layout_height="fill_parent" android:hint="@string/login_phone_code_hint" android:singleLine="true" android:maxLength="6" android:digits="\ 0123456789" android:inputType="phone" android:textDirection="locale" android:textAlignment="viewStart" style="@style/LoginEditTextTheme" />
        <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textColor="@color/text_01" android:gravity="end|center" android:layout_gravity="end" android:id="@id/btn_resend" android:background="@null" android:layout_width="wrap_content" android:layout_height="fill_parent" android:text="@string/login_phone_code_resend" android:textAllCaps="false" style="@style/style_regular_text" />
    </FrameLayout>
    <View android:layout_gravity="bottom" android:background="@color/line_01" android:layout_width="fill_parent" android:layout_height="1.0dip" android:layout_marginStart="24.0dip" android:layout_marginEnd="24.0dip" app:layout_constraintTop_toBottomOf="@id/ll_input" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/base_color_FA5546" android:id="@id/tv_tips" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="12.0dip" android:layout_marginStart="24.0dip" android:layout_marginEnd="24.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/ll_input" style="@style/style_regular_text" />
    <androidx.appcompat.widget.AppCompatButton android:textColor="@color/base_color_white" android:gravity="center" android:layout_gravity="end" android:id="@id/btn_login" android:background="@drawable/login_selector_login_btn" android:layout_width="fill_parent" android:layout_height="38.0dip" android:layout_marginTop="16.0dip" android:text="@string/login_verify" android:textAllCaps="false" android:layout_marginStart="24.0dip" android:layout_marginEnd="24.0dip" app:layout_constraintBottom_toBottomOf="parent" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/text_03" android:id="@id/tv_code_tips" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="16.0dip" android:text="@string/login_phone_code_tips" android:layout_marginStart="24.0dip" android:layout_marginEnd="24.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/ll_input" style="@style/style_regular_text" />
</LinearLayout>
