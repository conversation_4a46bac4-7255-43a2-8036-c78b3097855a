<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:orientation="vertical" android:background="@color/bg_01" android:fitsSystemWindows="true" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/btn_back" android:background="@color/base_transparent" android:layout_width="wrap_content" android:layout_height="48.0dip" android:src="@mipmap/libui_ic_back_black" android:paddingStart="16.0dip" android:paddingEnd="8.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:tint="@color/text_01" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="24.0sp" android:textColor="@color/text_01" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginLeft="24.0dip" android:layout_marginTop="24.0dip" android:layout_marginRight="24.0dip" android:text="@string/login_with_phone" style="@style/style_medium_small_text" />
    <androidx.constraintlayout.widget.ConstraintLayout android:gravity="center_vertical" android:orientation="horizontal" android:id="@id/ll_input" android:layout_width="fill_parent" android:layout_height="45.0dip" android:layout_marginTop="24.0dip" android:layout_marginStart="24.0dip" android:layout_marginEnd="24.0dip">
        <androidx.appcompat.widget.AppCompatTextView android:textSize="15.0sp" android:textColor="@color/text_01" android:textColorHint="@color/login_color_et_hint" android:gravity="center" android:id="@id/tv_phone_country_code" android:background="@null" android:layout_width="wrap_content" android:layout_height="fill_parent" android:hint="@string/login_select_country_code" android:singleLine="true" android:inputType="number" android:textDirection="ltr" app:layout_constraintStart_toStartOf="parent" style="@style/style_regular_text" />
        <androidx.appcompat.widget.AppCompatImageView android:layout_gravity="center_vertical" android:id="@id/iv_arrow" android:layout_width="wrap_content" android:layout_height="wrap_content" android:src="@mipmap/login_arrow_down" android:tint="@color/text_01" android:layout_marginStart="7.0dip" app:layout_constraintBottom_toBottomOf="@id/tv_phone_country_code" app:layout_constraintStart_toEndOf="@id/tv_phone_country_code" app:layout_constraintTop_toTopOf="@id/tv_phone_country_code" />
        <View android:layout_gravity="center_vertical" android:id="@id/v_line" android:background="@color/line_01" android:layout_width="1.0dip" android:layout_height="23.0dip" android:layout_marginStart="7.0dip" app:layout_constraintBottom_toBottomOf="@id/tv_phone_country_code" app:layout_constraintStart_toEndOf="@id/iv_arrow" app:layout_constraintTop_toTopOf="@id/tv_phone_country_code" />
        <androidx.appcompat.widget.AppCompatEditText android:textSize="15.0sp" android:textColor="@color/text_01" android:textColorHint="@color/login_color_et_hint" android:gravity="start|center" android:id="@id/et_phone" android:background="@null" android:focusable="true" android:layout_width="0.0dip" android:layout_height="fill_parent" android:hint="@string/login_your_phone_number" android:singleLine="true" android:maxLength="18" android:digits="\ 0123456789" android:inputType="phone" android:textDirection="locale" android:textAlignment="viewStart" android:paddingStart="8.0dip" android:paddingEnd="20.0dip" app:layout_constraintBottom_toBottomOf="@id/tv_phone_country_code" app:layout_constraintEnd_toStartOf="@id/btn_clear" app:layout_constraintStart_toEndOf="@id/v_line" app:layout_constraintTop_toTopOf="@id/tv_phone_country_code" style="@style/LoginEditTextTheme" />
        <androidx.appcompat.widget.AppCompatImageButton android:layout_gravity="end|center" android:id="@id/btn_clear" android:background="@null" android:paddingTop="15.0dip" android:paddingBottom="15.0dip" android:layout_width="wrap_content" android:layout_height="wrap_content" android:src="@mipmap/login_clear" android:tint="@color/text_03" android:text="@string/login_phone_code_resend" android:paddingStart="10.0dip" android:paddingEnd="0.0dip" app:layout_constraintBottom_toBottomOf="@id/tv_phone_country_code" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="@id/tv_phone_country_code" style="@style/LoginEditTextTheme" />
    </androidx.constraintlayout.widget.ConstraintLayout>
    <View android:layout_gravity="bottom" android:background="@color/line_01" android:layout_width="fill_parent" android:layout_height="1.0dip" android:layout_marginStart="24.0dip" android:layout_marginEnd="24.0dip" app:layout_constraintTop_toBottomOf="@id/ll_input" />
    <FrameLayout android:gravity="center_vertical" android:layout_width="fill_parent" android:layout_height="45.0dip" android:layout_marginTop="12.0dip" android:layout_marginStart="24.0dip" android:layout_marginEnd="24.0dip">
        <com.transsnet.login.phone.widget.LoginPwdEditText android:textSize="15.0sp" android:textColor="@color/text_06" android:textColorHint="@color/login_color_et_hint" android:gravity="start|center" android:id="@id/et_pwd" android:background="@null" android:focusable="true" android:layout_width="fill_parent" android:layout_height="fill_parent" android:hint="@string/login_pwd_hint" android:singleLine="true" android:maxLength="18" android:inputType="textPassword|textNoSuggestions" android:textDirection="locale" android:textAlignment="viewStart" style="@style/LoginEditTextTheme" />
        <androidx.appcompat.widget.AppCompatImageView android:layout_gravity="end|center" android:id="@id/btn_eye" android:background="@null" android:paddingTop="15.0dip" android:paddingBottom="15.0dip" android:layout_width="wrap_content" android:layout_height="wrap_content" android:src="@drawable/login_phone_pwd_eye" android:tint="@color/text_03" android:paddingStart="10.0dip" android:paddingEnd="0.0dip" />
    </FrameLayout>
    <View android:layout_gravity="bottom" android:background="@color/line_01" android:layout_width="fill_parent" android:layout_height="1.0dip" android:layout_marginStart="24.0dip" android:layout_marginEnd="24.0dip" app:layout_constraintTop_toBottomOf="@id/ll_input" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/base_color_FA5546" android:id="@id/tv_tips" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="15.0dip" android:text="@string/login_phone_code_tips" android:layout_marginStart="24.0dip" android:layout_marginEnd="24.0dip" style="@style/style_regular_text" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/login_color_forget" android:id="@id/tv_forget_pwd" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="15.0dip" android:text="@string/login_pwd_forget" android:layout_marginStart="24.0dip" android:layout_marginEnd="24.0dip" style="@style/style_regular_text" />
    <androidx.appcompat.widget.AppCompatButton android:textColor="@color/base_color_white" android:gravity="center" android:layout_gravity="end" android:id="@id/btn_login" android:background="@drawable/login_selector_login_btn" android:layout_width="fill_parent" android:layout_height="38.0dip" android:layout_marginTop="28.0dip" android:text="@string/login_log_in" android:textAllCaps="false" android:layout_marginStart="24.0dip" android:layout_marginEnd="24.0dip" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/login_color_policy" android:gravity="center" android:layout_gravity="center_horizontal" android:id="@id/tv_privacy" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginLeft="24.0dip" android:layout_marginTop="16.0dip" android:layout_marginRight="24.0dip" android:text="@string/login_sign_up_privacy" android:lineSpacingExtra="5.0dip" />
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/login_color_sec_text" android:gravity="center" android:layout_gravity="center_horizontal" android:id="@id/btn_email" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginLeft="24.0dip" android:layout_marginTop="32.0dip" android:layout_marginRight="24.0dip" android:maxWidth="360.0dip" android:text="@string/login_with_email" android:drawablePadding="8.0dip" android:textAllCaps="false" android:drawableEnd="@mipmap/login_switch_arrow" app:layout_constraintBottom_toBottomOf="parent" style="@style/style_title_text" />
</LinearLayout>
