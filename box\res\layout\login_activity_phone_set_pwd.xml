<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:orientation="vertical" android:background="@color/bg_01" android:fitsSystemWindows="true" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <include android:id="@id/title" layout="@layout/login_layout_progress_title" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="24.0sp" android:textColor="@color/text_01" android:id="@id/tv_welcome" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginLeft="24.0dip" android:layout_marginTop="24.0dip" android:layout_marginRight="24.0dip" android:text="@string/login_pwd_set_now" style="@style/style_medium_small_text" />
    <FrameLayout android:gravity="center_vertical" android:layout_width="fill_parent" android:layout_height="45.0dip" android:layout_marginLeft="24.0dip" android:layout_marginTop="24.0dip" android:layout_marginRight="24.0dip">
        <com.transsnet.login.phone.widget.LoginPwdEditText android:textSize="15.0sp" android:textColor="@color/text_06" android:textColorHint="@color/login_color_et_hint" android:gravity="start|center" android:id="@id/et_pwd" android:background="@null" android:focusable="true" android:layout_width="fill_parent" android:layout_height="fill_parent" android:hint="@string/login_pwd_set_hint" android:singleLine="true" android:maxLength="18" android:inputType="textPassword|textNoSuggestions" android:textDirection="locale" android:textAlignment="viewStart" style="@style/LoginEditTextTheme" />
        <androidx.appcompat.widget.AppCompatImageView android:layout_gravity="end|center" android:id="@id/btn_eye" android:background="@null" android:paddingTop="15.0dip" android:paddingBottom="15.0dip" android:layout_width="wrap_content" android:layout_height="wrap_content" android:src="@drawable/login_phone_pwd_eye" android:tint="@color/text_03" android:paddingStart="10.0dip" android:paddingEnd="0.0dip" />
    </FrameLayout>
    <View android:layout_gravity="bottom" android:background="@color/line_01" android:layout_width="fill_parent" android:layout_height="1.0dip" android:layout_marginLeft="24.0dip" android:layout_marginRight="24.0dip" app:layout_constraintTop_toBottomOf="@id/ll_input" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/base_color_FA5546" android:id="@id/tv_tips" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginLeft="24.0dip" android:layout_marginTop="12.0dip" android:layout_marginRight="24.0dip" android:text="@string/login_pwd_err" style="@style/style_regular_text" />
    <ViewStub android:id="@id/vs_invitation_code" android:layout="@layout/login_layout_invitation_code" android:layout_width="fill_parent" android:layout_height="wrap_content" />
    <androidx.appcompat.widget.AppCompatButton android:textColor="@color/base_color_white" android:gravity="center" android:layout_gravity="end" android:id="@id/btn_login" android:background="@drawable/login_selector_login_btn" android:layout_width="fill_parent" android:layout_height="38.0dip" android:layout_marginLeft="24.0dip" android:layout_marginTop="16.0dip" android:layout_marginRight="24.0dip" android:text="@string/login_pwd_done" android:textAllCaps="false" />
</LinearLayout>
