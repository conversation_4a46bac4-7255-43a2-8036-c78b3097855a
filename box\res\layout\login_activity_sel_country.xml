<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:background="@color/bg_01" android:fitsSystemWindows="true" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/btn_back" android:background="@color/base_transparent" android:layout_width="wrap_content" android:layout_height="44.0dip" android:src="@mipmap/libui_ic_back_black" android:paddingStart="16.0dip" android:paddingEnd="8.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:srcCompat="@mipmap/libui_ic_back_black" app:tint="@color/text_01" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="15.0sp" android:textStyle="bold" android:textColor="@color/text_01" android:gravity="center" android:layout_width="wrap_content" android:layout_height="42.0dip" android:text="@string/login_select_country" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <View android:id="@id/line" android:background="@color/line_01" android:layout_width="fill_parent" android:layout_height="0.5dip" app:layout_constraintTop_toBottomOf="@id/btn_back" />
    <androidx.recyclerview.widget.RecyclerView android:id="@id/recycler_view" android:layout_width="fill_parent" android:layout_height="0.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintTop_toBottomOf="@id/line" />
    <com.transsnet.login.country.widget.SideBar android:layout_gravity="end|center" android:id="@id/side_bar" android:layout_width="20.0dip" android:layout_height="0.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toBottomOf="@id/btn_back" />
</androidx.constraintlayout.widget.ConstraintLayout>
