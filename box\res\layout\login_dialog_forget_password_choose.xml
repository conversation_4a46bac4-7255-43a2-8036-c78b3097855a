<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:padding="20.0dip" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textColor="@color/text_01" android:gravity="center" android:id="@id/tv_tips" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="8.0dip" android:text="@string/login_pwd_forgot_tips" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:gravity="center" android:id="@id/tv_no" android:layout_width="0.0dip" android:layout_height="36.0dip" android:layout_marginTop="16.0dip" android:text="@string/login_pwd_forgot_no" android:layout_marginEnd="4.0dip" app:layout_constraintEnd_toStartOf="@id/tv_yes" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tv_tips" style="@style/style_sub_btn2_h36" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:gravity="center" android:id="@id/tv_yes" android:layout_width="0.0dip" android:layout_height="36.0dip" android:layout_marginTop="16.0dip" android:text="@string/login_pwd_forgot_yes" android:layout_marginStart="4.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/tv_no" app:layout_constraintTop_toBottomOf="@id/tv_tips" style="@style/style_sub_btn2_h36" />
</androidx.constraintlayout.widget.ConstraintLayout>
