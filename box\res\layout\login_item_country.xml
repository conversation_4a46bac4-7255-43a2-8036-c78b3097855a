<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <FrameLayout android:id="@id/index_layout" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="32.0dip">
        <TextView android:textSize="18.0sp" android:textStyle="bold" android:textColor="@color/white" android:layout_gravity="start|center" android:id="@id/index_tv" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="C" android:layout_marginStart="15.0dip" />
    </FrameLayout>
    <FrameLayout android:layout_width="fill_parent" android:layout_height="54.0dip" android:layout_marginStart="15.0dip" android:layout_marginEnd="15.0dip">
        <TextView android:textSize="14.0sp" android:textColor="@color/white" android:ellipsize="end" android:gravity="start|center" android:layout_gravity="start|center" android:id="@id/name" android:layout_width="wrap_content" android:layout_height="wrap_content" android:singleLine="true" />
        <TextView android:textSize="14.0sp" android:textColor="@color/white" android:layout_gravity="end|center" android:id="@id/code" android:layout_width="wrap_content" android:layout_height="wrap_content" android:textDirection="ltr" android:layout_marginEnd="20.0dip" />
    </FrameLayout>
</LinearLayout>
