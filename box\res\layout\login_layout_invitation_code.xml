<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/text_01" android:id="@id/tv_hava_invitation_code" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="36.0dip" android:text="@string/login_hava_invitation_code" android:layout_marginStart="24.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" style="@style/style_regula_bigger_text" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_help" android:padding="4.0dip" android:layout_width="wrap_content" android:layout_height="wrap_content" android:src="@mipmap/login_invitation_help" app:layout_constraintBottom_toBottomOf="@id/tv_hava_invitation_code" app:layout_constraintStart_toEndOf="@id/tv_hava_invitation_code" app:layout_constraintTop_toTopOf="@id/tv_hava_invitation_code" style="@style/style_regula_bigger_text" />
    <androidx.appcompat.widget.AppCompatEditText android:textSize="15.0sp" android:textColor="@color/text_06" android:textColorHint="@color/login_color_et_hint" android:gravity="start|center" android:id="@id/et_code" android:background="@null" android:focusable="true" android:layout_width="fill_parent" android:layout_height="45.0dip" android:layout_marginLeft="24.0dip" android:layout_marginTop="4.0dip" android:layout_marginRight="24.0dip" android:hint="@string/login_invitation_code_hint" android:singleLine="true" android:maxLength="8" android:digits="\ 0123456789" android:inputType="number" android:textDirection="locale" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tv_hava_invitation_code" style="@style/LoginEditTextTheme" />
    <View android:layout_gravity="bottom" android:id="@id/line" android:background="@color/line_01" android:layout_width="fill_parent" android:layout_height="1.0dip" android:layout_marginLeft="24.0dip" android:layout_marginRight="24.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/et_code" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/error_50" android:id="@id/tv_tips" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginLeft="24.0dip" android:layout_marginTop="12.0dip" android:layout_marginRight="24.0dip" android:text="@string/login_invitation_code_err" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/line" style="@style/style_regular_text" />
</androidx.constraintlayout.widget.ConstraintLayout>
