<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:background="@color/black_70" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.transsion.postdetail.ui.view.VideoDoubleClickGuideLineView android:layout_width="1.0dip" android:layout_height="fill_parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintHorizontal_bias="0.333" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <com.transsion.postdetail.ui.view.VideoDoubleClickGuideLineView android:layout_width="1.0dip" android:layout_height="fill_parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintHorizontal_bias="0.666" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/white" android:gravity="center" android:id="@id/ll_left" android:layout_width="0.0dip" android:layout_height="wrap_content" android:text="@string/video_double_tap_rewind" android:drawableTop="@mipmap/ic_video_double_click_guide_left" android:lineSpacingExtra="4.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toStartOf="@id/ll_middle" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" style="@style/style_regular_text" />
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/white" android:gravity="center" android:id="@id/ll_middle" android:layout_width="0.0dip" android:layout_height="wrap_content" android:text="@string/video_double_tap_pause" android:drawableTop="@mipmap/ic_video_double_click_guide_right" android:lineSpacingExtra="4.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toStartOf="@id/ll_right" app:layout_constraintStart_toEndOf="@id/ll_left" app:layout_constraintTop_toTopOf="parent" style="@style/style_regular_text" />
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/white" android:gravity="center" android:id="@id/ll_right" android:layout_width="0.0dip" android:layout_height="wrap_content" android:text="@string/video_double_tap_forward" android:drawableTop="@mipmap/ic_video_double_click_guide_right" android:lineSpacingExtra="4.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/ll_middle" app:layout_constraintTop_toTopOf="parent" style="@style/style_regular_text" />
</androidx.constraintlayout.widget.ConstraintLayout>
