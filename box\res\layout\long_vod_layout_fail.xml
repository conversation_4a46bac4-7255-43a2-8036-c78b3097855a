<?xml version="1.0" encoding="utf-8"?>
<FrameLayout android:orientation="vertical" android:background="@mipmap/post_detail_local_video_bg" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_fail_back" android:layout_width="24.0dip" android:layout_height="24.0dip" android:layout_marginTop="12.0dip" android:src="@mipmap/icon_white_back" android:scaleType="center" android:layout_marginStart="12.0dip" />
    <TextView android:textColor="@color/white" android:gravity="center" android:layout_gravity="center" android:id="@id/tv_fail_title" android:layout_marginBottom="16.0dip" android:text="@string/long_vod_load_failed" style="@style/style_medium_text" />
    <LinearLayout android:gravity="center_horizontal" android:layout_gravity="center" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="26.0dip">
        <TextView android:textColor="@color/white" android:gravity="center" android:layout_gravity="center" android:id="@id/tv_fail_left_btn" android:background="@drawable/long_vod_btn_bg" android:layout_width="134.0dip" android:layout_height="36.0dip" android:text="@string/go_to_setting" android:layout_marginEnd="16.0dip" style="@style/style_medium_small_text" />
        <TextView android:textColor="@color/white" android:gravity="center" android:layout_gravity="center" android:id="@id/tv_fail_right_btn" android:background="@drawable/long_vod_btn_bg" android:layout_width="134.0dip" android:layout_height="36.0dip" android:text="@string/retry_text" style="@style/style_medium_small_text" />
    </LinearLayout>
</FrameLayout>
