<?xml version="1.0" encoding="utf-8"?>
<FrameLayout android:background="@color/black_50" android:clickable="true" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/or_long_vod_iv_mobile_data_bg" android:layout_width="fill_parent" android:layout_height="fill_parent" android:scaleType="centerCrop" />
    <com.noober.background.view.BLTextView android:textSize="12.0sp" android:textColor="@color/white" android:gravity="center" android:layout_gravity="center" android:id="@id/or_long_vod_mobile_btn" android:layout_width="wrap_content" android:layout_height="35.0dip" android:text="@string/video_play_use_mobile_data" android:paddingStart="7.0dip" android:paddingEnd="12.0dip" app:bl_corners_radius="4.0dip" app:bl_solid_color="@color/black_70" app:drawableStartCompat="@drawable/ic_player_play_24" style="@style/style_medium_text" />
</FrameLayout>
