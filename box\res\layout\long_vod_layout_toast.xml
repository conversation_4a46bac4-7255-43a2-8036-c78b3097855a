<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:orientation="vertical" android:paddingBottom="40.0dip" android:layout_width="wrap_content" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <TextView android:textSize="12.0sp" android:textColor="@color/white" android:gravity="center_vertical" android:id="@id/tv_toast_2" android:background="@drawable/post_detail_shape_black_trans70_8dp" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="32.0dip" android:layout_marginBottom="8.0dip" android:paddingStart="12.0dip" android:paddingEnd="12.0dip" android:layout_marginStart="12.0dip" app:layout_constraintBottom_toBottomOf="@id/guideline" app:layout_constraintStart_toStartOf="@id/guideline" style="@style/style_medium_text" />
    <TextView android:textSize="12.0sp" android:textColor="@color/white" android:gravity="center_vertical" android:id="@id/tv_toast_1" android:background="@drawable/post_detail_shape_black_trans70_8dp" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="32.0dip" android:layout_marginBottom="8.0dip" android:paddingStart="12.0dip" android:paddingEnd="12.0dip" android:layout_marginStart="12.0dip" app:layout_constraintBottom_toBottomOf="@id/guideline" app:layout_constraintStart_toStartOf="@id/guideline" style="@style/style_medium_text" />
</LinearLayout>
