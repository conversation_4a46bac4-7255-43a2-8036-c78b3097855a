<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="wrap_content" app:layout_constraintTop_toTopOf="@id/guideline"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <View android:id="@id/v_space_start" android:layout_width="0.0dip" android:layout_height="0.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <View android:id="@id/v_space_end" android:layout_width="0.0dip" android:layout_height="0.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <androidx.appcompat.widget.AppCompatImageView android:layout_gravity="start|center" android:id="@id/iv_back" android:visibility="invisible" android:layout_width="24.0dip" android:layout_height="34.0dip" android:layout_marginTop="12.0dip" android:src="@mipmap/icon_white_back" android:scaleType="center" app:layout_constraintStart_toEndOf="@id/v_space_start" app:layout_constraintTop_toTopOf="parent" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textColor="#ffffffff" android:ellipsize="end" android:layout_gravity="start|center" android:id="@id/vd_title" android:visibility="gone" android:layout_width="0.0dip" android:layout_height="wrap_content" android:maxLines="1" android:layout_marginStart="12.0dip" android:layout_marginEnd="24.0dip" app:layout_constraintBottom_toBottomOf="@id/iv_back" app:layout_constraintEnd_toStartOf="@id/tvFeedback" app:layout_constraintStart_toEndOf="@id/iv_back" app:layout_constraintTop_toTopOf="@id/iv_back" style="@style/style_medium_text" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="8.0sp" android:textColor="@color/white" android:ellipsize="end" android:gravity="center" android:id="@id/tvFeedback" android:layout_width="wrap_content" android:layout_height="wrap_content" android:maxWidth="108.0dip" android:text="@string/title_help" android:maxLines="1" android:includeFontPadding="false" android:drawableTop="@mipmap/ic_feedback_transparent" android:layout_marginEnd="20.0dip" app:layout_constraintBottom_toBottomOf="@id/iv_back" app:layout_constraintEnd_toStartOf="@id/tvSubtitleSetting" app:layout_constraintTop_toTopOf="@id/iv_back" style="@style/style_regular_text" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="8.0sp" android:textColor="@color/white" android:ellipsize="end" android:gravity="center" android:id="@id/tvSubtitleSetting" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:maxWidth="108.0dip" android:text="@string/long_video_setting" android:maxLines="1" android:includeFontPadding="false" android:drawableTop="@drawable/long_setting" android:layout_marginEnd="@dimen/dp_12" app:layout_constraintBottom_toBottomOf="@id/iv_back" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="@id/iv_back" style="@style/style_regular_text" />
</androidx.constraintlayout.widget.ConstraintLayout>
