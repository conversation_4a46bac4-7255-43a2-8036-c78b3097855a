<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="fill_parent" app:layout_constraintEnd_toEndOf="@id/guideline" app:layout_constraintStart_toStartOf="@id/guideline" app:layout_constraintTop_toTopOf="@id/guideline"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <View android:id="@id/middle_guideline" android:layout_width="0.0dip" android:layout_height="0.0dip" app:layout_constraintDimensionRatio="h,16:9" app:layout_constraintLeft_toLeftOf="parent" app:layout_constraintRight_toRightOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <View android:id="@id/middle_gradient_top" android:background="@drawable/long_vod_shape_player_top_bg" android:layout_width="fill_parent" android:layout_height="80.0dip" app:layout_constraintTop_toTopOf="@id/middle_guideline" />
    <View android:id="@id/middle_gradient_bottom" android:background="@drawable/long_vod_shape_player_bottom_bg" android:layout_width="fill_parent" android:layout_height="80.0dip" app:layout_constraintBottom_toBottomOf="@id/middle_guideline" />
    <androidx.appcompat.widget.AppCompatImageView android:layout_gravity="start|center" android:id="@id/iv_back" android:visibility="gone" android:layout_width="24.0dip" android:layout_height="24.0dip" android:layout_marginTop="12.0dip" android:src="@mipmap/icon_white_back" android:scaleType="center" android:layout_marginStart="16.0dip" app:layout_constraintStart_toStartOf="@id/middle_guideline" app:layout_constraintTop_toTopOf="@id/middle_guideline" />
    <include android:id="@id/layout_loading" layout="@layout/long_vod_layout_loading" />
    <LinearLayout android:orientation="horizontal" android:id="@id/ll_middle_bottom_controller" android:layout_width="0.0dip" android:layout_height="32.0dip" app:layout_constraintBottom_toBottomOf="@id/middle_guideline" app:layout_constraintEnd_toEndOf="@id/middle_guideline" app:layout_constraintStart_toStartOf="@id/middle_guideline">
        <androidx.appcompat.widget.AppCompatImageView android:layout_gravity="center_vertical" android:id="@id/iv_middle_pause" android:layout_width="48.0dip" android:layout_height="32.0dip" android:src="@mipmap/icon_player_pause" android:scaleType="center" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" />
        <com.tn.lib.view.SecondariesSeekBar android:layout_gravity="center_vertical" android:id="@id/seek_bar_middle" android:background="@color/transparent" android:layout_width="0.0dip" android:layout_height="24.0dip" android:maxHeight="2.0dip" android:minHeight="2.0dip" android:layout_weight="1.0" android:layout_marginStart="4.0dip" app:layout_constraintBottom_toBottomOf="@id/iv_middle_pause" app:layout_constraintEnd_toStartOf="@id/tv_middle_time" app:layout_constraintStart_toEndOf="@id/iv_middle_pause" app:layout_constraintTop_toTopOf="@id/iv_middle_pause" app:ssb_bar_center_color="@color/main_gradient_center" app:ssb_bar_end_color="@color/main_gradient_end" app:ssb_bar_start_color="@color/main_gradient_start" app:ssb_bg_color="@color/white_30" app:ssb_secondaries_color="@color/white" app:ssb_thumb_color="@color/white" app:ssb_thumb_size="8.0dip" />
        <TextView android:textSize="11.0sp" android:textColor="@color/white" android:gravity="end" android:layout_gravity="end|center" android:id="@id/tv_middle_time" android:layout_width="wrap_content" android:layout_height="wrap_content" android:minWidth="78.5sp" android:text="00:00/00:00" android:shadowColor="@color/cl31_50_p" android:shadowDy="2.0" android:shadowRadius="2.0" app:layout_constraintBottom_toBottomOf="@id/iv_middle_pause" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="@id/iv_middle_pause" />
        <RelativeLayout android:id="@id/iv_float" android:layout_width="42.0dip" android:layout_height="fill_parent">
            <androidx.appcompat.widget.AppCompatImageView android:layout_width="24.0dip" android:layout_height="24.0dip" android:src="@mipmap/ic_player_float" android:scaleType="fitXY" android:layout_centerVertical="true" android:layout_marginStart="12.0dip" android:layout_marginEnd="6.0dip" />
        </RelativeLayout>
        <RelativeLayout android:id="@id/iv_middle_screen_change" android:layout_width="42.0dip" android:layout_height="fill_parent">
            <androidx.appcompat.widget.AppCompatImageView android:layout_width="24.0dip" android:layout_height="24.0dip" android:src="@drawable/rotate" android:scaleType="fitXY" android:layout_centerInParent="true" android:layout_marginStart="6.0dip" android:layout_marginEnd="12.0dip" app:layout_constraintEnd_toEndOf="parent" />
        </RelativeLayout>
    </LinearLayout>
    <LinearLayout android:gravity="center" android:orientation="vertical" android:id="@id/ll_float_tips" android:background="@color/black" android:focusable="true" android:visibility="gone" android:clickable="true" android:layout_width="0.0dip" android:layout_height="0.0dip" android:drawableTop="@mipmap/video_float_ic_tips" app:layout_constraintBottom_toBottomOf="@id/middle_guideline" app:layout_constraintEnd_toEndOf="@id/middle_guideline" app:layout_constraintStart_toStartOf="@id/middle_guideline" app:layout_constraintTop_toTopOf="@id/middle_guideline">
        <androidx.appcompat.widget.AppCompatImageView android:layout_width="wrap_content" android:layout_height="wrap_content" app:srcCompat="@mipmap/video_float_ic_tips" />
        <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/white_40" android:id="@id/tv_float_tips" android:background="@color/black" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="8.0dip" android:text="@string/video_float_player_tips" style="@style/style_title_text" />
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
