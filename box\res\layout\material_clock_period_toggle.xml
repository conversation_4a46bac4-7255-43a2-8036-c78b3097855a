<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.button.MaterialButtonToggleGroup android:orientation="vertical" android:id="@id/material_clock_period_toggle" android:visibility="gone" android:layout_width="@dimen/material_clock_period_toggle_width" android:layout_height="@dimen/material_clock_period_toggle_height" android:contentDescription="@string/material_clock_toggle_content_description" app:checkedButton="@id/material_clock_period_am_button" app:selectionRequired="true" app:singleSelection="true"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.google.android.material.button.MaterialButton android:id="@id/material_clock_period_am_button" android:layout_width="wrap_content" android:text="@string/material_timepicker_am" android:insetTop="8.0dip" style="?materialButtonOutlinedStyle" />
    <com.google.android.material.button.MaterialButton android:id="@id/material_clock_period_pm_button" android:layout_width="wrap_content" android:text="@string/material_timepicker_pm" android:insetBottom="8.0dip" style="?materialButtonOutlinedStyle" />
</com.google.android.material.button.MaterialButtonToggleGroup>
