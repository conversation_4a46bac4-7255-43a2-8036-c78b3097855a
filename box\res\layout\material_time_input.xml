<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.textfield.TextInputLayout android:layout_width="wrap_content" android:layout_height="wrap_content" app:errorIconDrawable="@null" app:hintEnabled="false"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.google.android.material.textfield.TextInputEditText android:layout_width="@dimen/material_clock_display_width" android:layout_height="@dimen/material_clock_display_height" android:textCursorDrawable="@drawable/material_cursor_drawable" />
    <TextView android:id="@id/material_label" android:focusable="false" android:layout_width="wrap_content" android:layout_height="wrap_content" />
</com.google.android.material.textfield.TextInputLayout>
