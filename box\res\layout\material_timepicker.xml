<?xml version="1.0" encoding="utf-8"?>
<merge android:id="@id/material_timepicker_container" android:layout_width="wrap_content" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.constraintlayout.helper.widget.Flow android:orientation="horizontal" android:id="@id/material_clock_display_and_toggle" android:layout_width="0.0dip" android:layout_height="@dimen/material_clock_period_toggle_height" app:constraint_referenced_ids="material_clock_display,material_clock_period_toggle" app:flow_horizontalGap="@dimen/material_clock_period_toggle_horizontal_gap" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <include android:id="@id/material_clock_display" layout="@layout/material_clock_display" />
    <include android:id="@id/material_clock_period_toggle" layout="@layout/material_clock_period_toggle" />
    <com.google.android.material.timepicker.ClockFaceView android:layout_gravity="center" android:id="@id/material_clock_face" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="28.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/material_clock_display_and_toggle" />
</merge>
