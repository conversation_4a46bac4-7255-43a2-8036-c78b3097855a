<?xml version="1.0" encoding="utf-8"?>
<FrameLayout android:background="#73000000" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <RelativeLayout android:layout_gravity="center" android:background="@drawable/mbridge_cm_alertview_bg" android:layout_width="300.0dip" android:layout_height="wrap_content" android:layout_margin="@dimen/mbridge_video_common_alertview_bg_padding">
        <RelativeLayout android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_margin="@dimen/mbridge_video_common_alertview_bg_padding" android:layout_centerInParent="true">
            <TextView android:textSize="@dimen/mbridge_video_common_alertview_title_size" android:textStyle="bold" android:textColor="@color/mbridge_video_common_alertview_title_textcolor" android:gravity="center" android:id="@id/mbridge_video_common_alertview_titleview" android:layout_width="fill_parent" android:layout_height="wrap_content" android:text="确认关闭？" />
            <TextView android:textSize="@dimen/mbridge_video_common_alertview_content_size" android:textColor="@color/mbridge_video_common_alertview_content_textcolor" android:id="@id/mbridge_video_common_alertview_contentview" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="@dimen/mbridge_video_common_alertview_content_margintop" android:text="关闭后您将不会获得任何奖励噢~ " android:layout_below="@id/mbridge_video_common_alertview_titleview" />
            <Button android:textSize="@dimen/mbridge_video_common_alertview_button_textsize" android:textColor="@color/mbridge_video_common_alertview_confirm_button_textcolor" android:gravity="center" android:id="@id/mbridge_video_common_alertview_confirm_button" android:background="@drawable/mbridge_cm_alertview_confirm_bg" android:layout_width="@dimen/mbridge_video_common_alertview_button_width" android:layout_height="@dimen/mbridge_video_common_alertview_button_height" android:layout_marginTop="@dimen/mbridge_video_common_alertview_button_margintop" android:layout_marginBottom="4.0dip" android:text="确认关闭" android:layout_below="@id/mbridge_video_common_alertview_contentview" />
            <Button android:textSize="@dimen/mbridge_video_common_alertview_button_textsize" android:textColor="@color/mbridge_video_common_alertview_cancel_button_textcolor" android:gravity="center" android:id="@id/mbridge_video_common_alertview_cancel_button" android:background="@drawable/mbridge_cm_alertview_cancel_bg" android:layout_width="@dimen/mbridge_video_common_alertview_button_width" android:layout_height="@dimen/mbridge_video_common_alertview_button_height" android:layout_marginTop="@dimen/mbridge_video_common_alertview_button_margintop" android:layout_marginBottom="4.0dip" android:text="继续观看" android:layout_below="@id/mbridge_video_common_alertview_contentview" android:layout_alignParentRight="true" />
        </RelativeLayout>
    </RelativeLayout>
</FrameLayout>
