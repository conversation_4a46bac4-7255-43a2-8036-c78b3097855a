<?xml version="1.0" encoding="utf-8"?>
<FrameLayout android:background="#73000000" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <RelativeLayout android:layout_gravity="center" android:background="@drawable/mbridge_cm_feedback_dialog_view_bg" android:layout_width="300.0dip" android:layout_height="wrap_content" android:layout_margin="@dimen/mbridge_video_common_alertview_bg_padding">
        <LinearLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="270.0dip" android:layout_centerInParent="true">
            <RelativeLayout android:layout_width="fill_parent" android:layout_height="0.0dip" android:layout_weight="1.0">
                <TextView android:textSize="@dimen/mbridge_video_common_alertview_title_size" android:textStyle="bold" android:textColor="@color/mbridge_black" android:gravity="center" android:id="@id/mbridge_video_common_alertview_titleview" android:layout_width="fill_parent" android:layout_height="fill_parent" android:text="用户反馈" android:singleLine="true" android:textAllCaps="false" />
                <Button android:textSize="@dimen/mbridge_video_common_alertview_button_textsize" android:textColor="@color/mbridge_black" android:gravity="center" android:id="@id/mbridge_video_common_alertview_confirm_button" android:background="@null" android:paddingLeft="1.0dip" android:paddingRight="13.0dip" android:layout_width="@dimen/mbridge_video_common_alertview_button_height" android:layout_height="wrap_content" android:text="x" android:layout_alignParentRight="true" android:paddingStart="1.0dip" android:paddingEnd="13.0dip" android:layout_alignParentEnd="true" />
            </RelativeLayout>
            <ScrollView android:id="@id/mbridge_video_common_alertview_contentview_scrollview" android:layout_width="fill_parent" android:layout_height="0.0dip" android:layout_weight="5.0" android:layout_below="@id/mbridge_video_common_alertview_titleview">
                <LinearLayout android:orientation="horizontal" android:id="@id/mbridge_video_common_alertview_contentview" android:layout_width="fill_parent" android:layout_height="wrap_content" />
            </ScrollView>
            <LinearLayout android:gravity="center" android:layout_gravity="center" android:orientation="horizontal" android:background="@drawable/mbridge_cm_feedback_dialog_view_bg" android:layout_width="fill_parent" android:layout_height="0.0dip" android:layout_weight="1.0" android:layout_below="@id/mbridge_video_common_alertview_contentview_scrollview">
                <Button android:textSize="@dimen/mbridge_video_common_alertview_button_textsize" android:textColor="@color/mbridge_white" android:gravity="center" android:id="@id/mbridge_video_common_alertview_cancel_button" android:background="@drawable/mbridge_cm_feedback_choice_btn_bg_pressed" android:layout_width="fill_parent" android:layout_height="@dimen/mbridge_video_common_alertview_button_height" android:layout_marginTop="6.0dip" android:layout_marginBottom="6.0dip" android:text="提交" android:textAllCaps="false" android:layout_marginStart="16.0dip" android:layout_marginEnd="16.0dip" />
            </LinearLayout>
            <Button android:textSize="@dimen/mbridge_video_common_alertview_button_textsize" android:textColor="@color/mbridge_cm_feedback_dialog_chice_bg_pressed" android:id="@id/mbridge_video_common_alertview_private_action_button" android:background="@null" android:layout_width="fill_parent" android:layout_height="0.0dip" android:text="Mintegral隐私政策" android:layout_weight="1.0" android:textAllCaps="false" />
        </LinearLayout>
    </RelativeLayout>
</FrameLayout>
