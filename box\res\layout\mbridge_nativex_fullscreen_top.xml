<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:gravity="center" android:orientation="vertical" android:id="@id/mbridge_full_tv_display_content" android:layout_width="fill_parent" android:layout_height="fill_parent" android:layout_marginTop="133.0dip" android:layout_above="@id/mbridge_full_animation_content"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <ImageView android:id="@id/mbridge_full_tv_display_icon" android:layout_width="100.0dip" android:layout_height="100.0dip" android:layout_marginTop="10.0dip" />
    <TextView android:textSize="30.0sp" android:textColor="#ff000000" android:gravity="center_horizontal" android:id="@id/mbridge_full_tv_display_title" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginLeft="50.0dip" android:layout_marginTop="10.0dip" android:layout_marginRight="50.0dip" android:lines="1" android:singleLine="true" />
    <TextView android:textSize="20.0sp" android:ellipsize="end" android:id="@id/mbridge_full_tv_display_description" android:paddingLeft="4.5dip" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginLeft="35.0dip" android:layout_marginTop="10.0dip" android:layout_marginRight="36.0dip" android:singleLine="true" android:lineSpacingExtra="4.0dip" />
    <com.mbridge.msdk.nativex.view.mbfullview.StarLevelLayoutView android:gravity="center_horizontal" android:id="@id/mbridge_full_tv_feeds_star" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="10.0dip" />
</LinearLayout>
