<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout android:id="@id/mbridge_native_rl_root" android:background="#ff000000" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <TextureView android:id="@id/mbridge_textureview" android:visibility="visible" android:layout_width="fill_parent" android:layout_height="fill_parent" />
    <ImageView android:id="@id/mbridge_iv_pause" android:visibility="gone" android:layout_width="48.0dip" android:layout_height="48.0dip" android:src="@drawable/mbridge_nativex_pause" android:scaleType="fitXY" android:layout_centerInParent="true" />
    <ProgressBar android:id="@id/mbridge_progress" android:layout_width="fill_parent" android:layout_height="2.0dip" android:progressDrawable="@drawable/mbridge_nativex_play_progress" android:layout_alignParentBottom="true" style="?android:progressBarStyleHorizontal" />
    <ImageView android:id="@id/mbridge_iv_sound" android:background="@drawable/mbridge_nativex_sound_bg" android:padding="5.0dip" android:visibility="gone" android:layout_width="25.0dip" android:layout_height="25.0dip" android:layout_marginLeft="5.0dip" android:layout_marginBottom="8.0dip" android:src="@drawable/mbridge_nativex_sound_open" android:layout_alignParentLeft="true" android:layout_alignParentBottom="true" />
    <ImageView android:id="@id/mbridge_iv_sound_animation" android:background="@drawable/mbridge_nativex_sound_bg" android:padding="5.0dip" android:visibility="gone" android:layout_width="20.0dip" android:layout_height="20.0dip" android:layout_marginRight="5.0dip" android:layout_marginBottom="5.0dip" android:src="@drawable/mbridge_nativex_sound_animation" android:layout_alignRight="@id/mbridge_textureview" android:layout_alignBottom="@id/mbridge_textureview" />
    <com.mbridge.msdk.videocommon.view.MyImageView android:id="@id/mbridge_iv_playend_pic" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="fill_parent" android:scaleType="centerCrop" />
    <View android:id="@id/mbridge_view_cover" android:background="#4d000000" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="fill_parent" />
    <ImageView android:id="@id/mbridge_iv_play" android:visibility="gone" android:layout_width="53.0dip" android:layout_height="53.0dip" android:src="@drawable/mbridge_nativex_play" android:scaleType="fitXY" android:layout_centerInParent="true" />
    <LinearLayout android:gravity="center" android:orientation="vertical" android:id="@id/mbridge_ll_loading" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="fill_parent" android:layout_centerInParent="true">
        <ProgressBar android:layout_width="60.0dip" android:layout_height="60.0dip" style="?android:progressBarStyleLarge" />
    </LinearLayout>
</RelativeLayout>
