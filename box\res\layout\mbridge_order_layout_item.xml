<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout android:gravity="center" android:layout_gravity="center" android:id="@id/mbridge_choice_frl" android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <RelativeLayout android:gravity="center" android:id="@id/mbridge_lv_item_rl" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="10.0dip" android:layout_marginBottom="10.0dip">
        <ImageView android:id="@id/mbridge_lv_iv_burl" android:layout_width="fill_parent" android:layout_height="220.0dip" android:scaleType="centerCrop" android:topLeftRadius="10.0dip" android:topRightRadius="10.0dip" />
        <RelativeLayout android:id="@id/mbridge_lv_iv_cover" android:background="@color/mbridge_black_66" android:layout_width="fill_parent" android:layout_height="220.0dip" android:scaleType="fitXY" />
        <com.mbridge.msdk.dycreator.baseview.cusview.MBridgeImageView android:id="@id/mbridge_lv_iv" android:layout_width="fill_parent" android:layout_height="0.0dip" android:layout_margin="20.0dip" android:scaleType="fitXY" android:layout_alignTop="@id/mbridge_lv_iv_burl" android:layout_alignBottom="@id/mbridge_lv_iv_burl" />
        <TextView android:textStyle="bold|italic" android:textColor="@color/mbridge_black" android:id="@id/mbridge_order_viewed_tv" android:background="@drawable/mbridge_reward_shape_order_history" android:paddingLeft="10.0dip" android:paddingTop="8.0dip" android:paddingRight="10.0dip" android:paddingBottom="8.0dip" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_alignLeft="@id/mbridge_lv_iv_burl" android:layout_alignTop="@id/mbridge_lv_iv_burl" android:layout_alignStart="@id/mbridge_lv_iv_burl" />
        <ImageView android:id="@id/mbridge_iv_flag" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_alignBottom="@id/mbridge_lv_iv_burl" android:layout_alignParentRight="true" android:layout_alignParentEnd="true" />
        <RelativeLayout android:background="@drawable/mbridge_reward_shape_order" android:layout_width="fill_parent" android:layout_height="120.0dip" android:layout_below="@id/mbridge_lv_iv">
            <com.mbridge.msdk.videocommon.view.RoundImageView android:id="@id/mbridge_lv_icon_iv" android:layout_width="90.0dip" android:layout_height="90.0dip" android:layout_marginLeft="10.0dip" android:scaleType="fitXY" android:layout_centerVertical="true" android:layout_marginStart="10.0dip" />
            <RelativeLayout android:id="@id/mbridge_text_layout" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginLeft="10.0dip" android:layout_toLeftOf="@id/mbridge_lv_tv_install" android:layout_toRightOf="@id/mbridge_lv_icon_iv" android:layout_centerVertical="true" android:layout_marginStart="10.0dip" android:layout_toStartOf="@id/mbridge_lv_tv_install" android:layout_toEndOf="@id/mbridge_lv_icon_iv">
                <TextView android:textSize="20.0sp" android:textStyle="bold" android:textColor="@color/mbridge_black" android:ellipsize="end" android:gravity="center_vertical" android:id="@id/mbridge_lv_title_tv" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="6.0dip" android:lines="1" />
                <TextView android:textSize="13.0sp" android:ellipsize="end" android:gravity="center_vertical" android:id="@id/mbridge_lv_desc_tv" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="6.0dip" android:lines="1" android:layout_below="@id/mbridge_lv_title_tv" />
            </RelativeLayout>
            <com.mbridge.msdk.video.dynview.widget.MBridgeLevelLayoutView android:gravity="center_vertical" android:orientation="vertical" android:id="@id/mbridge_lv_sv_starlevel" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_below="@id/mbridge_lv_tv_install" android:layout_alignLeft="@id/mbridge_text_layout" android:layout_centerVertical="true" />
            <com.mbridge.msdk.dycreator.baseview.cusview.MBridgeTextView android:textSize="18.0sp" android:textColor="#ffffffff" android:ellipsize="end" android:gravity="center" android:layout_gravity="right|center" android:id="@id/mbridge_lv_tv_install" android:background="@drawable/mbridge_reward_shape_videoend_buttonbg" android:paddingLeft="15.0dip" android:paddingTop="10.0dip" android:paddingRight="15.0dip" android:paddingBottom="10.0dip" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginLeft="5.0dip" android:layout_marginTop="10.0dip" android:layout_marginRight="5.0dip" android:layout_marginBottom="10.0dip" android:maxWidth="110.0dip" android:text="install" android:singleLine="true" android:maxLength="8" android:layout_alignParentRight="true" android:layout_centerVertical="true" android:paddingStart="15.0dip" android:paddingEnd="15.0dip" android:layout_marginStart="5.0dip" android:layout_marginEnd="5.0dip" android:layout_alignParentEnd="true" />
        </RelativeLayout>
    </RelativeLayout>
</RelativeLayout>
