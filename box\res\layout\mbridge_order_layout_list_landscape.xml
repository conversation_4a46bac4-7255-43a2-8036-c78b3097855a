<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout android:gravity="center" android:layout_gravity="center" android:id="@id/mbridge_choice_frl" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <ImageView android:id="@id/mbridge_lv_iv_bg" android:background="@drawable/mbridge_order_layout_list_bg" android:layout_width="fill_parent" android:layout_height="fill_parent" android:scaleType="centerCrop" />
    <HorizontalScrollView android:layout_width="fill_parent" android:layout_height="fill_parent">
        <LinearLayout android:gravity="center" android:layout_gravity="center" android:layout_width="wrap_content" android:layout_height="wrap_content">
            <com.mbridge.msdk.video.dynview.ordercamp.AblGridView android:gravity="center" android:id="@id/mbridge_order_view_h_lv" android:scrollbars="none" android:layout_width="fill_parent" android:layout_height="fill_parent" android:stretchMode="spacingWidthUniform" android:numColumns="auto_fit" />
        </LinearLayout>
    </HorizontalScrollView>
    <RelativeLayout android:id="@id/mbridge_native_order_camp_controller" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginLeft="10.0dip" android:layout_marginRight="10.0dip">
        <com.mbridge.msdk.dycreator.baseview.cusview.MBridgeDyImageView android:id="@id/mbridge_iv_link" android:layout_width="25.0dip" android:layout_height="25.0dip" android:src="@drawable/mbridge_reward_notice" android:layout_centerVertical="true" />
        <com.mbridge.msdk.widget.FeedBackButton android:textSize="11.0sp" android:textColor="@color/mbridge_white" android:gravity="center" android:id="@id/mbridge_native_order_camp_feed_btn" android:paddingLeft="10.0dip" android:paddingRight="10.0dip" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="25.0dip" android:text="@string/mbridge_cm_feedback_btn_text" android:layout_alignTop="@id/mbridge_iv_link" android:layout_toEndOf="@id/mbridge_iv_link" />
        <com.mbridge.msdk.dycreator.baseview.cusview.MBridgeDyImageView android:id="@id/mbridge_order_view_iv_close" android:layout_width="25.0dip" android:layout_height="25.0dip" android:src="@drawable/mbridge_reward_close" android:layout_alignParentRight="true" android:contentDescription="closeButton" />
        <com.mbridge.msdk.dycreator.baseview.cusview.MBridgeTextView android:textSize="12.0sp" android:textColor="@color/mbridge_reward_white" android:gravity="center" android:id="@id/mbridge_tv_reward_status" android:background="@drawable/mbridge_reward_video_time_count_num_bg" android:paddingLeft="10.0dip" android:paddingRight="10.0dip" android:visibility="invisible" android:layout_width="wrap_content" android:layout_height="25.0dip" android:layout_marginLeft="5.0dip" android:text="@string/mbridge_reward_video_view_reward_time_complete" android:layout_alignTop="@id/mbridge_iv_link" android:paddingStart="10.0dip" android:paddingEnd="10.0dip" android:layout_toEndOf="@id/mbridge_native_order_camp_feed_btn" />
    </RelativeLayout>
    <ImageView android:id="@id/mbridge_iv_logo" android:layout_width="50.0dip" android:layout_height="18.0dip" android:layout_marginLeft="13.0dip" android:layout_marginTop="13.0dip" android:layout_marginRight="13.0dip" android:layout_marginBottom="5.0dip" android:src="@drawable/mbridge_reward_end_pager_logo" android:layout_alignParentBottom="true" android:layout_centerHorizontal="true" android:layout_marginStart="13.0dip" android:layout_marginEnd="13.0dip" />
</RelativeLayout>
