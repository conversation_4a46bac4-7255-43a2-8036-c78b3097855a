<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout android:id="@id/mbridge_playercommon_rl_root" android:background="#ff000000" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <LinearLayout android:orientation="horizontal" android:id="@id/mbridge_playercommon_ll_sur_container" android:visibility="visible" android:layout_width="fill_parent" android:layout_height="fill_parent" />
    <LinearLayout android:gravity="center" android:orientation="vertical" android:id="@id/mbridge_playercommon_ll_loading" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="fill_parent" android:layout_centerInParent="true">
        <ProgressBar android:id="@id/mbridge_progressBar" android:layout_width="60.0dip" android:layout_height="60.0dip" style="?android:progressBarStyleLarge" />
    </LinearLayout>
</RelativeLayout>
