<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout android:background="@android:color/transparent" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <RelativeLayout android:id="@id/mbridge_video_templete_webview_parent" android:background="@android:color/transparent" android:layout_width="fill_parent" android:layout_height="fill_parent" />
    <com.mbridge.msdk.video.module.MBridgeVideoView android:id="@id/mbridge_video_templete_videoview" android:background="@android:color/transparent" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" />
    <com.mbridge.msdk.video.module.MBridgeContainerView android:id="@id/mbridge_video_templete_container" android:layout_width="wrap_content" android:layout_height="wrap_content" />
    <RelativeLayout android:id="@id/mbridge_video_templete_progressbar" android:background="#ff000000" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="fill_parent">
        <ProgressBar android:layout_width="60.0dip" android:layout_height="60.0dip" android:layout_centerInParent="true" style="?android:progressBarStyleLarge" />
    </RelativeLayout>
</RelativeLayout>
