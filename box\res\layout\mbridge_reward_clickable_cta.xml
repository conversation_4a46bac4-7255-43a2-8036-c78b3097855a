<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:orientation="horizontal" android:id="@id/mbridge_viewgroup_ctaroot" android:background="@color/mbridge_reward_cta_bg" android:paddingLeft="10.0dip" android:paddingTop="10.0dip" android:paddingRight="10.0dip" android:paddingBottom="10.0dip" android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <RelativeLayout android:layout_gravity="center_vertical" android:visibility="visible" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_weight="1.0">
        <com.mbridge.msdk.videocommon.view.MyImageView android:id="@id/mbridge_iv_appicon" android:layout_width="38.0dip" android:layout_height="38.0dip" android:scaleType="fitCenter" android:layout_centerVertical="true" />
        <TextView android:textSize="14.0sp" android:textColor="@color/mbridge_reward_white" android:ellipsize="end" android:gravity="left|center" android:id="@id/mbridge_tv_title" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginLeft="7.0dip" android:layout_toRightOf="@id/mbridge_iv_appicon" android:layout_centerVertical="true" />
    </RelativeLayout>
    <TextView android:textSize="18.0sp" android:textColor="#ffffffff" android:ellipsize="end" android:gravity="center" android:layout_gravity="right|center" android:id="@id/mbridge_tv_install" android:background="@drawable/mbridge_reward_shape_end_pager" android:paddingLeft="25.0dip" android:paddingTop="5.0dip" android:paddingRight="25.0dip" android:paddingBottom="5.0dip" android:layout_width="wrap_content" android:layout_height="wrap_content" android:lines="1" />
</LinearLayout>
