<?xml version="1.0" encoding="utf-8"?>
<com.mbridge.msdk.video.dynview.widget.MBridgeRelativeLayout android:background="@color/mbridge_reward_endcard_hor_bg" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <RelativeLayout android:id="@id/mbridge_native_ec_layout" android:layout_width="fill_parent" android:layout_height="fill_parent">
        <RelativeLayout android:layout_width="fill_parent" android:layout_height="0.0dip" android:layout_above="@id/mbridge_reward_end_card_offer_title_rl" android:layout_alignParentTop="true">
            <com.mbridge.msdk.dycreator.baseview.cusview.MBridgeDyImageView android:id="@id/mbridge_iv_adbanner_bg" android:layout_width="fill_parent" android:layout_height="fill_parent" android:scaleType="fitXY" />
            <com.mbridge.msdk.videocommon.view.RoundImageView android:id="@id/mbridge_iv_adbanner" android:layout_width="320.0dip" android:layout_height="180.0dip" android:layout_margin="10.0sp" android:layout_centerInParent="true" />
            <TextView android:textSize="11.0dip" android:gravity="center" android:id="@id/mbridge_tv_flag" android:background="@color/mbridge_color_999999" android:paddingLeft="3.0dip" android:paddingRight="3.0dip" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="AD" android:layout_alignParentRight="true" android:layout_alignParentBottom="true" />
            <com.mbridge.msdk.dycreator.baseview.cusview.MBridgeDyImageView android:id="@id/mbridge_iv_flag" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_alignParentRight="true" android:layout_alignParentBottom="true" />
        </RelativeLayout>
        <RelativeLayout android:id="@id/mbridge_reward_end_card_offer_title_rl" android:layout_width="fill_parent" android:layout_height="80.0dip" android:layout_alignParentBottom="true">
            <com.mbridge.msdk.videocommon.view.RoundImageView android:id="@id/mbridge_iv_icon" android:layout_width="60.0dip" android:layout_height="60.0dip" android:layout_marginLeft="20.0dip" android:layout_marginRight="10.0dip" android:scaleType="centerCrop" android:layout_centerVertical="true" />
            <TextView android:textSize="20.0sp" android:textStyle="bold" android:textColor="@color/mbridge_black" android:ellipsize="end" android:id="@id/mbridge_tv_apptitle" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginTop="5.0dip" android:maxLines="1" android:layout_toLeftOf="@id/mbridge_tv_cta" android:layout_toRightOf="@id/mbridge_iv_icon" android:layout_alignTop="@id/mbridge_iv_icon" />
            <com.mbridge.msdk.video.dynview.widget.MBridgeLevelLayoutView android:gravity="center_vertical" android:id="@id/mbridge_sv_starlevel" android:layout_width="0.0dip" android:layout_height="fill_parent" android:layout_toLeftOf="@id/mbridge_tv_cta" android:layout_toRightOf="@id/mbridge_iv_icon" android:layout_below="@id/mbridge_tv_apptitle" />
            <TextView android:textSize="18.0sp" android:textStyle="bold" android:textColor="@color/mbridge_white" android:gravity="center" android:id="@id/mbridge_tv_cta" android:layout_width="180.0dip" android:layout_height="65.0dip" android:layout_marginRight="35.0dip" android:layout_above="@id/mbridge_iv_logo" android:layout_alignTop="@id/mbridge_tv_apptitle" android:layout_alignParentRight="true" />
            <com.mbridge.msdk.dycreator.baseview.cusview.MBridgeDyImageView android:id="@id/mbridge_iv_logo" android:layout_width="40.0dip" android:layout_height="10.0dip" android:layout_margin="10.0dip" android:src="@drawable/mbridge_reward_end_pager_logo" android:layout_alignRight="@id/mbridge_tv_cta" android:layout_alignParentBottom="true" />
        </RelativeLayout>
        <com.mbridge.msdk.dycreator.baseview.cusview.MBridgeBaitClickView android:id="@id/mbridge_animation_click_view" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="fill_parent" />
    </RelativeLayout>
    <RelativeLayout android:id="@id/mbridge_native_ec_layer_layout" android:layout_width="fill_parent" android:layout_height="fill_parent" />
    <RelativeLayout android:id="@id/mbridge_native_ec_controller" android:layout_width="fill_parent" android:layout_height="fill_parent">
        <com.mbridge.msdk.dycreator.baseview.cusview.MBridgeDyImageView android:id="@id/mbridge_iv_link" android:layout_width="25.0dip" android:layout_height="25.0dip" android:layout_margin="10.0dip" android:src="@drawable/mbridge_reward_notice" />
        <com.mbridge.msdk.widget.FeedBackButton android:textSize="11.0sp" android:textColor="@color/mbridge_white" android:gravity="center" android:id="@id/mbridge_native_endcard_feed_btn" android:paddingLeft="10.0dip" android:paddingRight="10.0dip" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="25.0dip" android:text="@string/mbridge_cm_feedback_btn_text" android:layout_alignTop="@id/mbridge_iv_link" android:layout_toEndOf="@id/mbridge_iv_link" />
        <com.mbridge.msdk.dycreator.baseview.cusview.MBridgeDyImageView android:id="@id/mbridge_iv_close" android:layout_width="25.0dip" android:layout_height="25.0dip" android:layout_margin="10.0dip" android:src="@drawable/mbridge_reward_close" android:layout_alignParentRight="true" android:contentDescription="closeButton" />
    </RelativeLayout>
</com.mbridge.msdk.video.dynview.widget.MBridgeRelativeLayout>
