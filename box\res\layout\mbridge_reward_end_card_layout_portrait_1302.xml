<?xml version="1.0" encoding="utf-8"?>
<com.mbridge.msdk.dycreator.baseview.cusview.MBridgeFramLayout android:background="@color/mbridge_ee_grey" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <RelativeLayout android:id="@id/mbridge_native_ec_layout" android:background="@drawable/mbridge_reward_end_card_shape_bg" android:layout_width="fill_parent" android:layout_height="fill_parent" android:layout_margin="15.0dip">
        <TextView android:textSize="22.0sp" android:textStyle="bold" android:textColor="@color/mbridge_black" android:ellipsize="end" android:gravity="center_horizontal" android:id="@id/mbridge_tv_apptitle" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="50.0dip" android:scaleType="centerCrop" android:maxLines="1" android:layout_centerHorizontal="true" />
        <com.mbridge.msdk.video.dynview.widget.MBridgeLevelLayoutView android:gravity="center_horizontal" android:id="@id/mbridge_sv_starlevel" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="10.0dip" android:layout_below="@id/mbridge_tv_apptitle" />
        <com.mbridge.msdk.videocommon.view.RoundImageView android:id="@id/mbridge_iv_adbanner" android:layout_width="fill_parent" android:layout_height="200.0dip" android:layout_marginLeft="1.0dip" android:layout_marginTop="-35.0dip" android:layout_marginRight="1.0dip" android:layout_below="@id/mbridge_iv_icon" android:layout_centerInParent="true" />
        <ImageView android:id="@id/mbridge_iv_icon" android:layout_width="70.0dip" android:layout_height="70.0dip" android:layout_marginTop="20.0dip" android:scaleType="centerCrop" android:layout_below="@id/mbridge_sv_starlevel" android:layout_centerHorizontal="true" android:radius="10.0dip" />
        <TextView android:textSize="11.0dip" android:gravity="center" android:id="@id/mbridge_tv_flag" android:paddingLeft="3.0dip" android:paddingRight="3.0dip" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="AD" android:layout_alignParentRight="true" android:layout_alignParentBottom="true" />
        <com.mbridge.msdk.dycreator.baseview.cusview.MBridgeDyImageView android:id="@id/mbridge_iv_flag" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_alignParentRight="true" android:layout_alignParentBottom="true" />
        <TextView android:gravity="center" android:id="@id/mbridge_tv_appdesc" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_margin="15.0dip" android:layout_below="@id/mbridge_iv_adbanner" android:layout_centerHorizontal="true" />
        <TextView android:ellipsize="end" android:gravity="center" android:id="@id/mbridge_tv_cta" android:layout_width="270.0dip" android:layout_height="50.0dip" android:lines="1" android:layout_below="@id/mbridge_tv_appdesc" android:layout_centerHorizontal="true" />
        <com.mbridge.msdk.dycreator.baseview.cusview.MBridgeDyImageView android:id="@id/mbridge_iv_logo" android:layout_width="50.0dip" android:layout_height="18.0dip" android:layout_margin="10.0dip" android:src="@drawable/mbridge_reward_end_pager_logo" android:layout_below="@id/mbridge_tv_cta" android:layout_centerHorizontal="true" />
        <com.mbridge.msdk.dycreator.baseview.cusview.MBridgeBaitClickView android:id="@id/mbridge_animation_click_view" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="fill_parent" />
        <com.mbridge.msdk.dycreator.baseview.cusview.MBridgeDyImageView android:id="@id/mbridge_iv_link" android:layout_width="25.0dip" android:layout_height="25.0dip" android:src="@drawable/mbridge_reward_notice" />
        <com.mbridge.msdk.widget.FeedBackButton android:textSize="11.0sp" android:textColor="@color/mbridge_white" android:gravity="center" android:id="@id/mbridge_native_endcard_feed_btn" android:paddingLeft="10.0dip" android:paddingRight="10.0dip" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="25.0dip" android:text="@string/mbridge_cm_feedback_btn_text" android:layout_alignTop="@id/mbridge_iv_link" android:layout_toEndOf="@id/mbridge_iv_link" />
        <com.mbridge.msdk.dycreator.baseview.cusview.MBridgeDyImageView android:id="@id/mbridge_iv_close" android:layout_width="25.0dip" android:layout_height="25.0dip" android:src="@drawable/mbridge_reward_close" android:layout_alignParentRight="true" android:contentDescription="closeButton" />
    </RelativeLayout>
    <RelativeLayout android:id="@id/mbridge_native_ec_layer_layout" android:layout_width="fill_parent" android:layout_height="fill_parent" />
</com.mbridge.msdk.dycreator.baseview.cusview.MBridgeFramLayout>
