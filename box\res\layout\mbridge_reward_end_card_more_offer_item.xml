<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:gravity="center" android:orientation="vertical" android:id="@id/mbridge_more_offer_ll_item" android:layout_width="wrap_content" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.mbridge.msdk.videocommon.view.RoundImageView android:id="@id/mbridge_reward_end_card_item_iv" android:layout_width="60.0dip" android:layout_height="60.0dip" android:layout_marginLeft="5.0dip" android:layout_marginRight="5.0dip" />
    <TextView android:textSize="9.0sp" android:textColor="@color/mbridge_black" android:ellipsize="end" android:gravity="center" android:id="@id/mbridge_reward_end_card_item_title_tv" android:padding="2.0dip" android:layout_width="60.0dip" android:layout_height="15.0dip" android:layout_marginLeft="5.0dip" android:layout_marginRight="5.0dip" android:maxLines="1" />
</LinearLayout>
