<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout android:background="@color/mbridge_reward_endcard_hor_bg" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <RelativeLayout android:id="@id/mbridge_native_ec_layout" android:layout_width="fill_parent" android:layout_height="fill_parent">
        <View android:id="@id/mbridge_center_view" android:layout_width="1.0dip" android:layout_height="1.0dip" android:layout_centerInParent="true" />
        <RelativeLayout android:layout_width="fill_parent" android:layout_height="fill_parent" android:layout_above="@id/mbridge_center_view">
            <ImageView android:id="@id/mbridge_iv_adbanner_bg" android:layout_width="fill_parent" android:layout_height="fill_parent" android:scaleType="fitXY" />
            <com.mbridge.msdk.videocommon.view.RoundImageView android:id="@id/mbridge_iv_adbanner" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_margin="20.0dip" android:layout_centerInParent="true" />
            <ImageView android:id="@id/mbridge_iv_flag" android:layout_width="wrap_content" android:layout_height="wrap_content" android:src="@drawable/mbridge_reward_flag_en" android:layout_alignParentRight="true" android:layout_alignParentBottom="true" android:layout_alignParentEnd="true" />
        </RelativeLayout>
        <ImageView android:id="@id/mbridge_iv_iconbg" android:background="@drawable/mbridge_reward_end_shape_oval" android:layout_width="64.0dip" android:layout_height="64.0dip" android:layout_centerInParent="true" />
        <ImageView android:id="@id/mbridge_iv_icon" android:layout_width="48.0dip" android:layout_height="48.0dip" android:layout_centerInParent="true" />
        <TextView android:textSize="15.0sp" android:textColor="@color/mbridge_reward_title_textcolor" android:ellipsize="end" android:id="@id/mbridge_tv_apptitle" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="13.0dip" android:maxEms="7" android:singleLine="true" android:layout_below="@id/mbridge_iv_iconbg" android:layout_centerHorizontal="true" />
        <com.mbridge.msdk.videocommon.view.StarLevelView android:orientation="horizontal" android:id="@id/mbridge_sv_starlevel" android:layout_width="wrap_content" android:layout_height="20.0dip" android:layout_marginTop="2.0dip" android:layout_below="@id/mbridge_tv_apptitle" android:layout_centerHorizontal="true" />
        <LinearLayout android:orientation="horizontal" android:id="@id/mbridge_tv_number_layout" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_below="@id/mbridge_sv_starlevel" android:layout_centerHorizontal="true">
            <TextView android:textSize="12.0sp" android:textColor="@color/mbridge_reward_desc_textcolor" android:ellipsize="end" android:layout_gravity="center_vertical" android:layout_width="wrap_content" android:layout_height="fill_parent" android:text="(" android:singleLine="true" />
            <TextView android:textSize="12.0sp" android:textColor="@color/mbridge_reward_desc_textcolor" android:ellipsize="end" android:layout_gravity="center_vertical" android:id="@id/mbridge_tv_number" android:layout_width="wrap_content" android:layout_height="fill_parent" android:singleLine="true" android:drawableLeft="@drawable/mbridge_reward_user" />
        </LinearLayout>
        <TextView android:textSize="12.0sp" android:textColor="@color/mbridge_reward_desc_textcolor" android:ellipsize="end" android:id="@id/mbridge_tv_appdesc" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginLeft="30.0dip" android:layout_marginTop="4.0dip" android:layout_marginRight="30.0dip" android:minLines="3" android:layout_below="@id/mbridge_tv_number_layout" android:layout_centerHorizontal="true" android:lineSpacingMultiplier="1.2" />
        <ImageView android:id="@id/mbridge_iv_logo" android:layout_width="54.0dip" android:layout_height="10.0dip" android:layout_marginBottom="50.0dip" android:src="@drawable/mbridge_reward_end_pager_logo" android:scaleType="fitXY" android:layout_alignParentBottom="true" android:layout_centerHorizontal="true" />
        <TextView android:textSize="24.0sp" android:textColor="@android:color/white" android:gravity="center" android:id="@id/mbridge_tv_cta" android:background="@drawable/mbridge_reward_shape_end_pager" android:layout_width="fill_parent" android:layout_height="50.0dip" android:layout_marginLeft="24.0dip" android:layout_marginRight="24.0dip" android:layout_marginBottom="24.0dip" android:text="Install Now" android:layout_above="@id/mbridge_iv_logo" />
        <RelativeLayout android:gravity="center" android:layout_gravity="center" android:id="@id/mbridge_native_ec_controller" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_margin="9.0dip">
            <ImageView android:id="@id/mbridge_iv_link" android:layout_width="35.0dip" android:layout_height="35.0dip" android:src="@drawable/mbridge_reward_notice" android:layout_alignParentLeft="true" android:contentDescription="noteLinkButton" android:layout_alignParentStart="true" />
            <com.mbridge.msdk.widget.FeedBackButton android:gravity="center" android:id="@id/mbridge_native_endcard_feed_btn" android:paddingLeft="10.0dip" android:paddingRight="10.0dip" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="27.0dip" android:layout_marginLeft="9.0dip" android:layout_marginTop="4.0dip" android:text="@string/mbridge_cm_feedback_btn_text" android:layout_toRightOf="@id/mbridge_iv_link" android:layout_alignTop="@id/mbridge_iv_link" android:layout_marginStart="9.0dip" />
            <ImageView android:id="@id/mbridge_iv_close" android:layout_width="35.0dip" android:layout_height="35.0dip" android:src="@drawable/mbridge_reward_close_ec" android:layout_alignParentRight="true" android:contentDescription="closeButton" android:layout_alignParentEnd="true" />
        </RelativeLayout>
    </RelativeLayout>
</RelativeLayout>
