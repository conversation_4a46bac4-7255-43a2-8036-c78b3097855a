<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout android:id="@id/mbridge_rl_content" android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <TextView android:textSize="19.0sp" android:textColor="@color/mbridge_reward_white" android:gravity="center_horizontal" android:id="@id/mbridge_tv_vasttitle" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/mbridge_reward_endcard_vast_notice" android:layout_centerHorizontal="true" />
    <TextView android:gravity="center_horizontal" android:id="@id/mbridge_tv_vasttag" android:layout_width="1.0dip" android:layout_height="1.0dip" android:layout_centerHorizontal="true" />
    <ImageView android:id="@id/mbridge_iv_vastclose" android:layout_width="60.0dip" android:layout_height="60.0dip" android:layout_marginTop="45.0dip" android:layout_marginRight="35.0dip" android:src="@drawable/mbridge_reward_vast_end_close" android:layout_toLeftOf="@id/mbridge_tv_vasttag" android:contentDescription="closeButton" />
    <ImageView android:id="@id/mbridge_iv_vastok" android:layout_width="60.0dip" android:layout_height="60.0dip" android:layout_marginLeft="35.0dip" android:layout_marginTop="45.0dip" android:src="@drawable/mbridge_reward_vast_end_ok" android:layout_toRightOf="@id/mbridge_tv_vasttag" />
</RelativeLayout>
