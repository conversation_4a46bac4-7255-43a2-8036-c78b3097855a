<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout android:background="@color/mbridge_white" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <ImageView android:id="@id/mbridge_videoview_bg" android:layout_width="fill_parent" android:layout_height="fill_parent" android:scaleType="centerCrop" />
    <RelativeLayout android:layout_width="fill_parent" android:layout_height="0.0dip" android:layout_marginLeft="25.0dip" android:layout_above="@id/mbridge_vfpv_fl" android:layout_alignParentTop="true">
        <com.mbridge.msdk.videocommon.view.RoundImageView android:id="@id/mbridge_reward_icon_riv" android:layout_width="70.0dip" android:layout_height="70.0dip" android:layout_centerVertical="true" />
        <TextView android:textSize="20.0dip" android:textStyle="bold" android:textColor="@color/mbridge_white" android:ellipsize="end" android:id="@id/mbridge_reward_title_tv" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_margin="5.0dip" android:lines="1" android:layout_toRightOf="@id/mbridge_reward_icon_riv" android:layout_alignTop="@id/mbridge_reward_icon_riv" />
        <com.mbridge.msdk.video.dynview.widget.MBridgeLevelLayoutView android:gravity="center_vertical" android:orientation="horizontal" android:id="@id/mbridge_reward_stars_mllv" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_below="@id/mbridge_reward_title_tv" android:layout_alignLeft="@id/mbridge_reward_title_tv" />
    </RelativeLayout>
    <FrameLayout android:id="@id/mbridge_vfpv_fl" android:background="@color/mbridge_black" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_centerInParent="true">
        <com.mbridge.msdk.playercommon.PlayerView android:layout_gravity="center" android:id="@id/mbridge_vfpv" android:layout_width="fill_parent" android:layout_height="220.0dip" />
        <com.mbridge.msdk.dycreator.baseview.cusview.MBridgeBaitClickView android:id="@id/mbridge_animation_click_view" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="220.0dip" />
    </FrameLayout>
    <RelativeLayout android:id="@id/mbridge_top_control" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="15.0dip" android:layout_alignParentTop="true">
        <ImageView android:gravity="center" android:id="@id/mbridge_iv_link" android:visibility="gone" android:layout_width="25.0dip" android:layout_height="25.0dip" android:layout_marginLeft="10.0dip" android:src="@drawable/mbridge_reward_notice" android:layout_alignParentLeft="true" android:layout_marginStart="10.0dip" android:layout_alignParentStart="true" />
        <com.mbridge.msdk.widget.FeedBackButton android:textSize="11.0sp" android:gravity="center" android:id="@id/mbridge_native_endcard_feed_btn" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="25.0dip" android:layout_marginLeft="10.0dip" android:text="@string/mbridge_cm_feedback_btn_text" android:layout_alignTop="@id/mbridge_iv_link" android:layout_alignParentLeft="true" android:layout_marginStart="10.0dip" android:layout_toEndOf="@id/mbridge_iv_link" android:layout_alignParentStart="true" />
        <TextView android:gravity="center" android:id="@id/mbridge_tv_count" android:layout_width="25.0dip" android:layout_height="25.0dip" android:layout_marginLeft="10.0dip" android:layout_marginStart="10.0dip" android:layout_toEndOf="@id/mbridge_native_endcard_feed_btn" />
        <ImageView android:layout_gravity="end" android:id="@id/mbridge_rl_playing_close" android:layout_width="25.0dip" android:layout_height="25.0dip" android:layout_marginRight="10.0dip" android:src="@drawable/mbridge_reward_close" android:layout_alignParentTop="true" android:contentDescription="closeButton" android:layout_marginEnd="10.0dip" android:layout_alignParentEnd="true" />
    </RelativeLayout>
    <LinearLayout android:orientation="vertical" android:id="@id/mbridge_reward_bottom_widget" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_margin="10.0dip" android:layout_alignBottom="@id/mbridge_vfpv_fl">
        <com.mbridge.msdk.dycreator.baseview.cusview.SoundImageView android:id="@id/mbridge_sound_switch" android:background="@drawable/mbridge_reward_end_close_shape_oval" android:padding="6.0dip" android:visibility="visible" android:layout_width="30.0dip" android:layout_height="30.0dip" android:src="@drawable/mbridge_reward_sound_open" android:layout_alignBottom="@id/mbridge_vfpv_fl" />
        <com.mbridge.msdk.dycreator.baseview.cusview.MBridgeSegmentsProgressBar android:id="@id/mbridge_reward_segment_progressbar" android:visibility="invisible" android:layout_width="fill_parent" android:layout_height="wrap_content" />
    </LinearLayout>
    <RelativeLayout android:layout_width="fill_parent" android:layout_height="0.0dip" android:layout_marginTop="50.0dip" android:layout_below="@id/mbridge_vfpv_fl" android:layout_alignParentBottom="true">
        <TextView android:textSize="18.0sp" android:textColor="@color/mbridge_white" android:ellipsize="end" android:gravity="center_horizontal" android:id="@id/mbridge_reward_desc_tv" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginLeft="10.0dip" android:layout_marginRight="10.0dip" android:layout_marginBottom="40.0dip" android:lines="1" android:layout_above="@id/mbridge_reward_click_tv" />
        <com.mbridge.msdk.dycreator.baseview.cusview.MBridgeTextView android:textSize="22.0sp" android:textStyle="bold" android:ellipsize="end" android:gravity="center" android:id="@id/mbridge_reward_click_tv" android:paddingLeft="5.0dip" android:paddingRight="5.0dip" android:layout_width="260.0dip" android:layout_height="50.0dip" android:lines="1" android:layout_centerHorizontal="true" android:layout_centerVertical="true" />
        <ImageView android:id="@id/mbridge_reward_logo_iv" android:layout_width="50.0dip" android:layout_height="18.0dip" android:src="@drawable/mbridge_reward_end_pager_logo" android:layout_below="@id/mbridge_reward_click_tv" android:layout_centerHorizontal="true" />
    </RelativeLayout>
    <com.mbridge.msdk.dycreator.baseview.rewardpopview.MBAcquireRewardPopView android:id="@id/mbridge_reward_popview" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="fill_parent" />
</RelativeLayout>
