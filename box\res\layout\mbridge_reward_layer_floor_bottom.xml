<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout android:id="@id/mbridge_viewgroup_ctaroot" android:background="@color/mbridge_reward_cta_bg" android:layout_width="fill_parent" android:layout_height="60.0dip" android:layout_centerVertical="true"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <ImageView android:id="@id/mbridge_iv_appicon" android:layout_width="40.0dip" android:layout_height="40.0dip" android:layout_marginLeft="5.0dip" android:scaleType="centerCrop" android:layout_centerVertical="true" android:layout_marginStart="5.0dip" />
    <RelativeLayout android:layout_width="wrap_content" android:layout_height="40.0dip" android:layout_marginLeft="3.0dip" android:layout_toLeftOf="@id/mbridge_tv_install" android:layout_toRightOf="@id/mbridge_iv_appicon" android:layout_centerVertical="true" android:layout_alignWithParentIfMissing="true" android:layout_marginStart="3.0dip" android:layout_toStartOf="@id/mbridge_tv_install" android:layout_toEndOf="@id/mbridge_iv_appicon">
        <TextView android:textStyle="bold" android:textColor="@color/mbridge_reward_white" android:ellipsize="end" android:id="@id/mbridge_tv_title" android:layout_width="wrap_content" android:layout_height="wrap_content" android:maxLines="1" />
        <TextView android:textSize="11.0sp" android:textColor="@color/mbridge_reward_white" android:ellipsize="end" android:id="@id/mbridge_tv_desc" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="3.0dip" android:maxLines="1" android:layout_below="@id/mbridge_tv_title" />
    </RelativeLayout>
    <TextView android:textStyle="bold" android:ellipsize="end" android:gravity="center" android:id="@id/mbridge_tv_install" android:paddingLeft="25.0dip" android:paddingRight="25.0dip" android:layout_width="wrap_content" android:layout_height="35.0dip" android:layout_marginLeft="5.0dip" android:layout_marginRight="5.0dip" android:lines="1" android:maxLength="12" android:layout_alignParentRight="true" android:layout_centerVertical="true" android:layout_alignParentEnd="true" />
</RelativeLayout>
