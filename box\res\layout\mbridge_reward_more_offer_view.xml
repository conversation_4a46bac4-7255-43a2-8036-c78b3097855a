<?xml version="1.0" encoding="utf-8"?>
<com.mbridge.msdk.video.dynview.widget.MBridgeRelativeLayout android:layout_gravity="center" android:id="@id/mbridge_reward_end_card_more_offer_rl" android:visibility="invisible" android:layout_width="fill_parent" android:layout_height="60.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.mbridge.msdk.video.dynview.widget.ObservableScrollView android:id="@id/mbridge_moreoffer_hls" android:scrollbars="none" android:layout_width="0.0dip" android:layout_height="fill_parent" android:layout_toRightOf="@id/mbridge_reward_end_card_like_tv" android:layout_alignParentRight="true" android:layout_centerVertical="true" />
    <TextView android:textStyle="bold" android:gravity="center" android:id="@id/mbridge_reward_end_card_like_tv" android:background="@drawable/mbridge_reward_shape_mf_selector" android:paddingLeft="10.0dip" android:paddingRight="10.0dip" android:visibility="invisible" android:layout_width="wrap_content" android:layout_height="fill_parent" />
</com.mbridge.msdk.video.dynview.widget.MBridgeRelativeLayout>
