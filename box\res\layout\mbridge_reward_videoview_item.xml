<?xml version="1.0" encoding="utf-8"?>
<FrameLayout android:background="@color/mbridge_reward_black" android:layout_width="fill_parent" android:layout_height="fill_parent" android:keepScreenOn="true"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <com.mbridge.msdk.playercommon.PlayerView android:id="@id/mbridge_vfpv" android:layout_width="fill_parent" android:layout_height="fill_parent" />
    <com.mbridge.msdk.dycreator.baseview.cusview.MBridgeBaitClickView android:id="@id/mbridge_animation_click_view" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="fill_parent" />
    <com.mbridge.msdk.dycreator.baseview.rewardpopview.MBAcquireRewardPopView android:id="@id/mbridge_reward_popview" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="fill_parent" />
    <com.mbridge.msdk.dycreator.baseview.cusview.SoundImageView android:layout_gravity="bottom" android:id="@id/mbridge_sound_switch" android:visibility="visible" android:layout_width="35.0dip" android:layout_height="35.0dip" android:layout_marginLeft="10.0dip" android:layout_marginBottom="10.0dip" />
    <ProgressBar android:layout_gravity="bottom" android:id="@id/mbridge_video_progress_bar" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="4.0dip" android:progressDrawable="@drawable/mbridge_reward_video_progressbar_bg" style="@android:style/Widget.ProgressBar.Horizontal" />
    <com.mbridge.msdk.dycreator.baseview.cusview.MBridgeSegmentsProgressBar android:layout_gravity="bottom" android:id="@id/mbridge_reward_segment_progressbar" android:visibility="invisible" android:layout_width="fill_parent" android:layout_height="wrap_content" />
    <RelativeLayout android:layout_gravity="end" android:orientation="horizontal" android:id="@id/mbridge_top_control" android:visibility="visible" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_margin="10.0dip">
        <ImageView android:gravity="center" android:id="@id/mbridge_iv_link" android:visibility="gone" android:layout_width="35.0dip" android:layout_height="35.0dip" android:src="@drawable/mbridge_reward_notice" android:layout_alignParentLeft="true" android:layout_marginStart="10.0dip" android:layout_alignParentStart="true" />
        <com.mbridge.msdk.widget.FeedBackButton android:id="@id/mbridge_native_endcard_feed_btn" android:paddingLeft="5.0dip" android:paddingRight="5.0dip" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="30.0dip" android:text="@string/mbridge_cm_feedback_btn_text" android:layout_toRightOf="@id/mbridge_iv_link" android:layout_centerVertical="true" android:layout_toEndOf="@id/mbridge_iv_link" />
        <TextView android:textSize="12.0sp" android:textColor="@color/mbridge_reward_white" android:gravity="center" android:id="@id/mbridge_tv_count" android:background="@drawable/mbridge_reward_video_time_count_num_bg" android:visibility="visible" android:layout_width="30.0dip" android:layout_height="30.0dip" android:layout_marginLeft="5.0dip" android:layout_toRightOf="@id/mbridge_native_endcard_feed_btn" android:layout_centerVertical="true" android:layout_marginStart="5.0dip" android:layout_toEndOf="@id/mbridge_native_endcard_feed_btn" />
        <ImageView android:layout_gravity="end" android:id="@id/mbridge_rl_playing_close" android:layout_width="35.0dip" android:layout_height="35.0dip" android:src="@drawable/mbridge_reward_close" android:contentDescription="closeButton" android:layout_alignParentEnd="true" />
    </RelativeLayout>
</FrameLayout>
