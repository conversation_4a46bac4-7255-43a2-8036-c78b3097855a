<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <com.mbridge.msdk.splash.view.nativeview.MBNoRecycledCrashImageView android:id="@id/mbridge_splash_iv_image_bg" android:layout_width="fill_parent" android:layout_height="fill_parent" />
    <com.mbridge.msdk.splash.view.nativeview.MBNoRecycledCrashImageView android:id="@id/mbridge_splash_iv_image" android:layout_width="fill_parent" android:layout_height="fill_parent" />
    <RelativeLayout android:layout_gravity="center" android:id="@id/mbridge_splash_topcontroller" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_margin="9.0dip">
        <ImageView android:id="@id/mbridge_splash_iv_link" android:layout_width="35.0dip" android:layout_height="35.0dip" android:src="@drawable/mbridge_splash_notice" android:layout_alignParentLeft="true" android:contentDescription="noteLinkButton" android:layout_alignParentStart="true" />
        <com.mbridge.msdk.widget.FeedBackButton android:textSize="12.0sp" android:gravity="center" android:id="@id/mbridge_splash_feedback" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="28.0dip" android:layout_marginLeft="8.0dip" android:text="@string/mbridge_cm_feedback_btn_text" android:layout_toRightOf="@id/mbridge_splash_iv_link" android:layout_centerVertical="true" android:layout_marginStart="8.0dip" />
        <TextView android:textSize="12.0sp" android:textColor="@color/mbridge_white" android:gravity="center" android:id="@id/mbridge_splash_tv_skip" android:background="@drawable/mbridge_splash_close_bg" android:paddingLeft="10.0dip" android:paddingRight="10.0dip" android:layout_width="wrap_content" android:layout_height="28.0dip" android:layout_alignParentRight="true" android:layout_centerVertical="true" android:contentDescription="closeButton" android:paddingStart="10.0dip" android:paddingEnd="10.0dip" android:layout_alignParentEnd="true" />
    </RelativeLayout>
    <RelativeLayout android:id="@id/mbridge_splash_landscape_foreground" android:background="@drawable/mbridge_shape_splash_corners_14" android:padding="12.0dip" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginLeft="10.0dip" android:layout_marginTop="160.0dip" android:layout_marginRight="10.0dip" android:layout_below="@id/mbridge_splash_topcontroller" android:layout_centerVertical="true" android:layout_marginStart="10.0dip" android:layout_marginEnd="10.0dip">
        <com.mbridge.msdk.splash.view.nativeview.MBNoRecycledCrashImageView android:id="@id/mbridge_splash_iv_icon" android:layout_width="40.0dip" android:layout_height="40.0dip" />
        <TextView android:textSize="16.0sp" android:textColor="@color/mbridge_black" android:ellipsize="marquee" android:gravity="center_vertical" android:id="@id/mbridge_splash_tv_title" android:layout_width="wrap_content" android:layout_height="40.0dip" android:layout_marginLeft="10.0dip" android:singleLine="true" android:layout_toRightOf="@id/mbridge_splash_iv_icon" android:layout_marginStart="10.0dip" android:layout_toEndOf="@id/mbridge_splash_iv_icon" />
        <com.mbridge.msdk.splash.view.nativeview.MBNoRecycledCrashImageView android:id="@id/mbridge_splash_iv_foregroundimage" android:layout_width="fill_parent" android:layout_height="185.0dip" android:layout_marginTop="12.0dip" android:layout_below="@id/mbridge_splash_iv_icon" />
        <TextView android:textSize="10.0sp" android:textColor="@android:color/white" android:gravity="center" android:id="@id/mbridge_splash_tv_adrect" android:background="@drawable/mbridge_shape_splash_rightbottom_corners_10" android:paddingLeft="3.0dip" android:layout_width="34.0dip" android:layout_height="14.0dip" android:layout_alignBottom="@id/mbridge_splash_iv_foregroundimage" android:layout_alignParentRight="true" android:paddingStart="3.0dip" android:layout_alignParentEnd="true" />
    </RelativeLayout>
    <RelativeLayout android:id="@id/mbridge_splash_layout_appinfo" android:paddingBottom="10.0dip" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginLeft="10.0dip" android:layout_marginTop="10.0dip" android:layout_marginRight="10.0dip" android:layout_alignParentBottom="true" android:layout_marginStart="10.0dip" android:layout_marginEnd="10.0dip">
        <TextView android:textSize="11.0sp" android:textColor="@color/mbridge_white" android:id="@id/mbridge_splash_tv_appinfo" android:layout_width="wrap_content" android:layout_height="wrap_content" android:lineSpacingMultiplier="1.2" />
        <TextView android:textSize="8.0sp" android:textColor="@color/mbridge_white" android:id="@id/mbridge_splash_tv_privacy" android:background="@drawable/mbridge_splash_button_bg_gray" android:padding="8.0dip" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="隐私政策地址" android:layout_alignParentRight="true" android:layout_centerVertical="true" />
        <TextView android:textSize="8.0sp" android:textColor="@color/mbridge_white" android:id="@id/mbridge_splash_tv_permission" android:background="@drawable/mbridge_splash_button_bg_gray" android:padding="8.0dip" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginRight="6.0dip" android:text="权限信息" android:layout_toLeftOf="@id/mbridge_splash_tv_privacy" android:layout_centerVertical="true" />
        <TextView android:textSize="8.0sp" android:textColor="@color/mbridge_white" android:id="@id/mbridge_splash_tv_app_desc" android:background="@drawable/mbridge_splash_button_bg_gray" android:padding="8.0dip" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginRight="6.0dip" android:text="功能介绍" android:layout_toLeftOf="@id/mbridge_splash_tv_permission" android:layout_centerVertical="true" />
    </RelativeLayout>
    <TextView android:textSize="10.0sp" android:textColor="@android:color/white" android:gravity="center" android:id="@id/mbridge_splash_tv_adcircle" android:background="@drawable/mbridge_shape_splash_circle_14" android:paddingLeft="3.0dip" android:paddingRight="3.0dip" android:layout_width="wrap_content" android:layout_height="14.0dip" android:layout_marginLeft="5.0dip" android:layout_marginTop="5.0dip" android:layout_marginRight="5.0dip" android:layout_marginBottom="5.0dip" android:drawablePadding="3.0dip" android:layout_alignBottom="@id/mbridge_splash_iv_foregroundimage" android:layout_alignParentRight="true" android:layout_alignParentBottom="true" android:paddingStart="3.0dip" android:paddingEnd="3.0dip" android:layout_marginStart="5.0dip" android:layout_marginEnd="5.0dip" android:layout_alignParentEnd="true" />
    <com.mbridge.msdk.splash.view.nativeview.MBSplashClickView android:id="@id/mbridge_splash_tv_click" android:layout_width="fill_parent" android:layout_height="48.0dip" android:layout_marginLeft="40.0dip" android:layout_marginTop="40.0dip" android:layout_marginRight="40.0dip" android:layout_marginBottom="20.0dip" android:layout_above="@id/mbridge_splash_layout_appinfo" android:layout_alignWithParentIfMissing="true" />
</RelativeLayout>
