<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <View android:id="@id/viewBg" android:background="@drawable/member_bg_8" android:layout_width="120.0dip" android:layout_height="120.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <ProgressBar android:layout_gravity="center" android:id="@id/viewLoad" android:layout_width="44.0dip" android:layout_height="44.0dip" android:layout_marginTop="30.0dip" android:indeterminateTint="@color/white" app:layout_constraintEnd_toEndOf="@id/viewBg" app:layout_constraintStart_toStartOf="@id/viewBg" app:layout_constraintTop_toTopOf="@id/viewBg" />
    <TextView android:textSize="12.0sp" android:textColor="@color/white" android:id="@id/tvLoading" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="12.0dip" android:text="Loading…" app:layout_constraintEnd_toEndOf="@id/viewBg" app:layout_constraintStart_toStartOf="@id/viewBg" app:layout_constraintTop_toBottomOf="@id/viewLoad" />
</androidx.constraintlayout.widget.ConstraintLayout>
