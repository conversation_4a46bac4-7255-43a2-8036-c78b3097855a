<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <View android:id="@id/bgView" android:background="@color/white_6" android:layout_width="fill_parent" android:layout_height="0.0dip" android:layout_marginLeft="@dimen/dp_12" android:layout_marginRight="@dimen/dp_12" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <View android:id="@id/divider" android:background="@color/border" android:layout_width="fill_parent" android:layout_height="1.0px" android:layout_marginLeft="24.0dip" android:layout_marginRight="24.0dip" app:layout_constraintBottom_toBottomOf="parent" />
    <ImageView android:id="@id/icIV" android:layout_width="20.0dip" android:layout_height="20.0dip" android:layout_marginTop="@dimen/dp_12" android:layout_marginBottom="@dimen/dp_8" android:src="@drawable/ic_mine_history" android:layout_marginStart="24.0dip" app:layout_constraintBottom_toTopOf="@id/historyRv" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:layout_goneMarginBottom="@dimen/dp_16" app:tint="@color/text_02" />
    <com.tn.lib.widget.TnTextView android:textSize="14.0sp" android:textColor="@color/text_02" android:gravity="start|center" android:id="@id/titleTv" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/download_watch_history" android:layout_marginStart="8.0dip" app:layout_constraintBottom_toBottomOf="@id/icIV" app:layout_constraintStart_toEndOf="@id/icIV" app:layout_constraintTop_toTopOf="@id/icIV" style="@style/robot_bold" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/arrowIV" android:layout_width="@dimen/dp_16" android:layout_height="@dimen/dp_16" android:src="@drawable/user_setting_arrow" android:layout_marginEnd="24.0dip" app:layout_constraintBottom_toBottomOf="@id/icIV" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="@id/icIV" app:tint="@color/white_60" />
    <androidx.recyclerview.widget.RecyclerView android:id="@id/historyRv" android:clipToPadding="false" android:layout_width="fill_parent" android:layout_height="74.0dip" android:layout_marginBottom="@dimen/dp_12" android:paddingStart="@dimen/dp_12" android:paddingEnd="0.0dip" android:layout_marginStart="12.0dip" android:layout_marginEnd="12.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintTop_toBottomOf="@id/icIV" />
</androidx.constraintlayout.widget.ConstraintLayout>
