<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="52.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <View android:id="@id/bgView" android:layout_width="fill_parent" android:layout_height="fill_parent" android:layout_marginLeft="@dimen/dp_12" android:layout_marginRight="@dimen/dp_12" />
    <View android:id="@id/divider" android:background="@color/border" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="1.0px" android:layout_marginLeft="24.0dip" android:layout_marginRight="24.0dip" app:layout_constraintBottom_toBottomOf="parent" />
    <ImageView android:id="@id/icIV" android:layout_width="20.0dip" android:layout_height="20.0dip" android:layout_marginStart="24.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:tint="@color/text_02" />
    <com.tn.lib.widget.TnTextView android:textSize="14.0sp" android:textColor="@color/text_02" android:gravity="start|center" android:id="@id/titleTv" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginStart="8.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toEndOf="@id/icIV" app:layout_constraintTop_toTopOf="parent" style="@style/robot_bold" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="14.0sp" android:textColor="@color/text_03" android:gravity="center" android:id="@id/desTv" android:layout_width="wrap_content" android:layout_height="wrap_content" android:includeFontPadding="false" android:layout_marginEnd="4.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toStartOf="@id/arrowIV" app:layout_constraintTop_toTopOf="parent" style="@style/style_tip_text" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/arrowIV" android:layout_width="@dimen/dp_16" android:layout_height="@dimen/dp_16" android:src="@drawable/user_setting_arrow" android:layout_marginEnd="@dimen/dimens_24" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent" app:tint="@color/white_60" />
</androidx.constraintlayout.widget.ConstraintLayout>
