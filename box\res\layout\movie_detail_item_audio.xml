<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:background="@drawable/bg_white20_6dp" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginLeft="12.0dip" android:layout_marginRight="12.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.appcompat.widget.LinearLayoutCompat android:orientation="vertical" android:id="@id/root" android:layout_width="fill_parent" android:layout_height="wrap_content" app:layout_constraintTop_toTopOf="parent">
        <include layout="@layout/movie_detail_item_header" />
        <androidx.constraintlayout.widget.ConstraintLayout android:id="@id/cl_audio" android:layout_width="fill_parent" android:layout_height="84.0dip" android:layout_marginStart="12.0dip" android:layout_marginEnd="12.0dip" app:layout_constraintTop_toBottomOf="@id/header">
            <com.google.android.material.imageview.ShapeableImageView android:id="@id/iv_cover" android:visibility="visible" android:layout_width="fill_parent" android:layout_height="fill_parent" android:src="@mipmap/post_audio_bg" android:scaleType="centerCrop" app:shapeAppearanceOverlay="@style/roundStyle_8" />
            <com.google.android.material.imageview.ShapeableImageView android:id="@id/iv_audio" android:layout_width="64.0dip" android:layout_height="64.0dip" android:src="@mipmap/post_audio_scroll_table" android:scaleType="centerCrop" android:layout_marginStart="16.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
            <com.google.android.material.imageview.ShapeableImageView android:id="@id/iv_audio_cover_small" android:layout_width="24.0dip" android:layout_height="24.0dip" android:src="@mipmap/post_audio_bg" android:scaleType="centerCrop" app:layout_constraintBottom_toBottomOf="@id/iv_audio" app:layout_constraintEnd_toEndOf="@id/iv_audio" app:layout_constraintStart_toStartOf="@id/iv_audio" app:layout_constraintTop_toTopOf="@id/iv_audio" app:shapeAppearanceOverlay="@style/ImgRoundedStyle" />
            <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_player" android:layout_width="24.0dip" android:layout_height="24.0dip" android:src="@mipmap/movie_detail_audio_pause" app:layout_constraintBottom_toBottomOf="@id/iv_audio" app:layout_constraintEnd_toEndOf="@id/iv_audio" app:layout_constraintStart_toStartOf="@id/iv_audio" app:layout_constraintTop_toTopOf="@id/iv_audio" />
            <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textColor="@color/white" android:ellipsize="end" android:id="@id/tv_title" android:visibility="visible" android:layout_width="0.0dip" android:maxLines="1" android:textAlignment="viewStart" android:layout_marginStart="8.0dip" android:layout_marginEnd="16.0dip" app:layout_constraintBottom_toTopOf="@id/tv_duration" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/iv_audio" app:layout_constraintTop_toTopOf="@id/iv_audio" style="@style/style_medium_text" />
            <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/white" android:ellipsize="end" android:id="@id/tv_duration" android:visibility="visible" android:layout_width="0.0dip" android:layout_height="wrap_content" android:maxLines="1" android:textAlignment="viewStart" android:layout_marginEnd="16.0dip" app:layout_constraintBottom_toBottomOf="@id/iv_audio" app:layout_constraintEnd_toEndOf="@id/tv_title" app:layout_constraintStart_toStartOf="@id/tv_title" app:layout_constraintTop_toBottomOf="@id/tv_title" />
        </androidx.constraintlayout.widget.ConstraintLayout>
        <com.transsion.postdetail.ui.view.PostDetailSubjectView android:id="@id/llResource" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="12.0dip" android:layout_marginStart="12.0dip" android:layout_marginEnd="12.0dip" />
        <com.tn.lib.view.FlowLayout android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginStart="16.0dip" android:layout_marginEnd="16.0dip">
            <include layout="@layout/movie_detail_item_room" />
            <include layout="@layout/movie_detail_item_subject" />
        </com.tn.lib.view.FlowLayout>
        <include layout="@layout/movie_detail_item_bottom" />
    </androidx.appcompat.widget.LinearLayoutCompat>
    <include android:visibility="gone" android:layout_width="0.0dip" android:layout_height="0.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" layout="@layout/movie_layout_limit" />
</androidx.constraintlayout.widget.ConstraintLayout>
