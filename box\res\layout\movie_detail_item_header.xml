<?xml version="1.0" encoding="utf-8"?>
<merge
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.constraintlayout.widget.ConstraintLayout android:id="@id/cl_header" android:layout_width="fill_parent" android:layout_height="56.0dip" android:layout_marginStart="12.0dip" android:layout_marginEnd="12.0dip">
        <com.google.android.material.imageview.ShapeableImageView android:id="@id/header" android:layout_width="32.0dip" android:layout_height="32.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:shapeAppearanceOverlay="@style/circle_style" />
        <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textColor="@color/text_01" android:ellipsize="end" android:id="@id/tvName" android:layout_width="wrap_content" android:maxLines="1" android:layout_marginStart="8.0dip" app:layout_constrainedWidth="true" app:layout_constraintBottom_toBottomOf="@id/header" app:layout_constraintEnd_toStartOf="@id/tvTime" app:layout_constraintHorizontal_bias="0.0" app:layout_constraintHorizontal_chainStyle="packed" app:layout_constraintStart_toEndOf="@id/header" app:layout_constraintTop_toTopOf="@id/header" style="@style/style_medium_text" />
        <androidx.appcompat.widget.AppCompatTextView android:textSize="14.0sp" android:textColor="@color/text_02" android:id="@id/tvTime" android:layout_marginStart="4.0dip" app:layout_constraintBottom_toBottomOf="@id/tvName" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/tvName" app:layout_constraintTop_toTopOf="@id/tvName" style="@style/style_regular_text" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</merge>
