<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:background="@drawable/bg_white20_6dp" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginLeft="12.0dip" android:layout_marginRight="12.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.appcompat.widget.LinearLayoutCompat android:orientation="vertical" android:id="@id/root" android:layout_width="fill_parent" android:layout_height="wrap_content" app:layout_constraintTop_toTopOf="parent">
        <include layout="@layout/movie_detail_item_header" />
        <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textColor="@color/text_01" android:ellipsize="end" android:id="@id/tv_title" android:visibility="gone" android:paddingStart="16.0dip" android:paddingEnd="16.0dip" style="@style/style_medium_text" />
        <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textColor="@color/text_01" android:ellipsize="end" android:id="@id/tv_desc" android:visibility="gone" android:layout_marginTop="4.0dip" android:maxLines="4" android:paddingStart="16.0dip" android:paddingEnd="16.0dip" style="@style/style_regula_bigger_text" />
        <androidx.cardview.widget.CardView android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="8.0dip" android:layout_marginStart="12.0dip" android:layout_marginEnd="12.0dip" app:cardCornerRadius="8.0dip" app:cardElevation="0.0dip" app:cardMaxElevation="0.0dip">
            <com.transsion.ninegridview.video.NineGridVideoView android:id="@id/nine_grid" android:layout_width="fill_parent" android:layout_height="wrap_content" app:ngv_mode="grid" />
        </androidx.cardview.widget.CardView>
        <com.transsion.postdetail.ui.view.PostDetailSubjectView android:id="@id/llResource" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="12.0dip" android:layout_marginStart="12.0dip" android:layout_marginEnd="12.0dip" />
        <com.tn.lib.view.FlowLayout android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginStart="16.0dip" android:layout_marginEnd="16.0dip">
            <include layout="@layout/movie_detail_item_room" />
            <include layout="@layout/movie_detail_item_subject" />
        </com.tn.lib.view.FlowLayout>
        <include layout="@layout/movie_detail_item_bottom" />
    </androidx.appcompat.widget.LinearLayoutCompat>
    <include android:visibility="gone" android:layout_width="0.0dip" android:layout_height="0.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" layout="@layout/movie_layout_limit" />
</androidx.constraintlayout.widget.ConstraintLayout>
