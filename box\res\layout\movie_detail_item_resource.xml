<?xml version="1.0" encoding="utf-8"?>
<merge
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.appcompat.widget.LinearLayoutCompat android:gravity="center_vertical" android:id="@id/llResource" android:background="@drawable/bg_radius_5_color_f7f7f7" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="32.0dip" android:layout_marginTop="6.0dip" android:paddingStart="8.0dip" android:paddingEnd="8.0dip" android:layout_marginStart="16.0dip" android:layout_marginEnd="16.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/ll_link">
        <androidx.appcompat.widget.AppCompatImageView android:id="@id/ivSubjectIcon" android:layout_width="18.0dip" android:layout_height="24.0dip" />
        <TextView android:textSize="14.0sp" android:textColor="@color/base_color_333333" android:ellipsize="end" android:gravity="start|center" android:id="@id/tvSubjectName" android:layout_width="0.0dip" android:layout_height="fill_parent" android:maxLines="1" android:layout_weight="1.0" android:layout_marginStart="4.0dip" />
        <androidx.appcompat.widget.AppCompatImageView android:layout_width="wrap_content" android:layout_height="wrap_content" android:src="@mipmap/ic_download_red" android:layout_marginStart="4.0dip" />
        <TextView android:textSize="14.0sp" android:textColor="@color/cl01" android:id="@id/tvSubjectDownload" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/download_movie" android:layout_marginStart="2.0dip" />
    </androidx.appcompat.widget.LinearLayoutCompat>
</merge>
