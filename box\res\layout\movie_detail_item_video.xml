<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:background="@drawable/bg_white20_6dp" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginLeft="12.0dip" android:layout_marginRight="12.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.appcompat.widget.LinearLayoutCompat android:orientation="vertical" android:id="@id/root" android:layout_width="fill_parent" android:layout_height="wrap_content" app:layout_constraintTop_toTopOf="parent">
        <include layout="@layout/movie_detail_item_header" />
        <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textStyle="bold" android:textColor="@color/text_01" android:ellipsize="end" android:id="@id/tv_title" android:visibility="visible" android:paddingStart="12.0dip" android:paddingEnd="12.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/iv_cover" style="@style/style_medium_text" />
        <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textColor="@color/text_01" android:ellipsize="end" android:id="@id/tv_desc" android:visibility="visible" android:layout_marginTop="4.0dip" android:maxLines="4" android:textAlignment="viewStart" android:paddingStart="12.0dip" android:paddingEnd="12.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tv_title" style="@style/style_regula_bigger_text" />
        <androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="8.0dip" android:layout_marginStart="12.0dip" android:layout_marginEnd="12.0dip">
            <com.google.android.material.imageview.ShapeableImageView android:id="@id/iv_cover" android:visibility="visible" android:layout_width="0.0dip" android:layout_height="100.0dip" android:scaleType="centerCrop" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:shapeAppearanceOverlay="@style/roundStyle_4" />
            <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_player" android:layout_width="@dimen/dimens_48" android:layout_height="@dimen/dimens_48" android:src="@mipmap/movie_detail_video_play" app:layout_constraintBottom_toBottomOf="@id/iv_cover" app:layout_constraintEnd_toEndOf="@id/iv_cover" app:layout_constraintStart_toStartOf="@id/iv_cover" app:layout_constraintTop_toTopOf="@id/iv_cover" />
            <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/white" android:id="@id/tv_play_duration" android:background="@drawable/ad_shape_dp_2_black_trans_50" android:paddingLeft="@dimen/dp_4" android:paddingTop="1.0dip" android:paddingRight="@dimen/dp_4" android:paddingBottom="1.0dip" android:layout_marginBottom="8.0dip" android:layout_marginEnd="8.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" style="@style/style_regular_text" />
        </androidx.constraintlayout.widget.ConstraintLayout>
        <com.transsion.postdetail.ui.view.PostDetailSubjectView android:id="@id/llResource" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="12.0dip" android:layout_marginStart="12.0dip" android:layout_marginEnd="12.0dip" />
        <com.tn.lib.view.FlowLayout android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginStart="16.0dip" android:layout_marginEnd="16.0dip">
            <include layout="@layout/movie_detail_item_room" />
            <include layout="@layout/movie_detail_item_subject" />
        </com.tn.lib.view.FlowLayout>
        <include layout="@layout/movie_detail_item_bottom" />
    </androidx.appcompat.widget.LinearLayoutCompat>
    <include android:visibility="gone" android:layout_width="0.0dip" android:layout_height="0.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" layout="@layout/movie_layout_limit" />
</androidx.constraintlayout.widget.ConstraintLayout>
