<?xml version="1.0" encoding="utf-8"?>
<FrameLayout android:id="@id/fl_limit" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_limit_cover" android:layout_width="fill_parent" android:layout_height="fill_parent" android:layout_marginTop="60.0dip" android:layout_marginBottom="40.0dip" android:scaleType="centerCrop" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="14.0sp" android:textColor="@color/white" android:gravity="center" android:layout_gravity="center" android:id="@id/tv_limit_tips" android:paddingTop="60.0dip" android:paddingBottom="40.0dip" android:layout_width="wrap_content" android:layout_height="fill_parent" android:text="@string/limit_des" android:shadowColor="@color/base_color_80000000" android:shadowRadius="3.0" android:layout_marginStart="20.0dip" android:layout_marginEnd="20.0dip" />
</FrameLayout>
