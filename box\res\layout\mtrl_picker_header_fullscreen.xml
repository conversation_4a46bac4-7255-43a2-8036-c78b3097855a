<?xml version="1.0" encoding="utf-8"?>
<merge android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <LinearLayout android:layout_gravity="center|top" android:orientation="horizontal" android:id="@id/fullscreen_header" android:paddingLeft="@dimen/mtrl_calendar_header_content_padding_fullscreen" android:paddingTop="@dimen/mtrl_calendar_header_content_padding_fullscreen" android:paddingRight="@dimen/mtrl_calendar_header_content_padding_fullscreen" android:layout_width="fill_parent" android:layout_height="@dimen/mtrl_calendar_header_height_fullscreen" android:paddingStart="@dimen/mtrl_calendar_header_content_padding_fullscreen" android:paddingEnd="@dimen/mtrl_calendar_header_content_padding_fullscreen" style="?materialCalendarHeaderLayout">
        <com.google.android.material.button.MaterialButton android:gravity="center" android:layout_gravity="top" android:id="@id/cancel_button" android:padding="12.0dip" android:layout_width="@dimen/mtrl_min_touch_target_size" android:layout_height="@dimen/mtrl_min_touch_target_size" android:insetTop="0.0dip" android:insetBottom="0.0dip" android:contentDescription="@string/mtrl_picker_cancel" app:icon="@drawable/material_ic_clear_black_24dp" style="?materialCalendarHeaderCancelButton" />
        <FrameLayout android:id="@id/mtrl_picker_header_title_and_selection" android:paddingLeft="@dimen/mtrl_calendar_header_text_padding" android:focusable="true" android:focusableInTouchMode="true" android:layout_width="0.0dip" android:layout_height="fill_parent" android:layout_weight="1.0" android:paddingStart="@dimen/mtrl_calendar_header_text_padding">
            <TextView android:gravity="start|bottom" android:layout_gravity="start|top" android:id="@id/mtrl_picker_title_text" android:layout_width="fill_parent" android:layout_height="wrap_content" app:firstBaselineToTopHeight="@dimen/mtrl_calendar_title_baseline_to_top_fullscreen" style="?materialCalendarHeaderTitle" />
            <TextView android:gravity="start|bottom" android:layout_gravity="start|top" android:id="@id/mtrl_picker_header_selection_text" android:paddingBottom="@dimen/mtrl_calendar_pre_l_text_clip_padding" android:layout_width="fill_parent" android:layout_height="wrap_content" app:firstBaselineToTopHeight="@dimen/mtrl_calendar_selection_baseline_to_top_fullscreen" app:lineHeight="@dimen/mtrl_calendar_header_selection_line_height" style="?materialCalendarHeaderSelection" />
        </FrameLayout>
        <LinearLayout android:orientation="@integer/mtrl_calendar_header_orientation" android:layout_width="wrap_content" android:layout_height="wrap_content">
            <com.google.android.material.button.MaterialButton android:layout_gravity="end|top" android:id="@id/confirm_button" android:layout_width="wrap_content" android:layout_height="@dimen/mtrl_min_touch_target_size" android:text="@string/mtrl_picker_save" android:contentDescription="@string/mtrl_picker_save" style="?materialCalendarHeaderConfirmButton" />
            <include layout="@layout/mtrl_picker_header_toggle" />
        </LinearLayout>
    </LinearLayout>
    <View android:layout_gravity="bottom" android:layout_width="fill_parent" android:layout_height="@dimen/mtrl_calendar_header_divider_thickness" style="?materialCalendarHeaderDivider" />
</merge>
