<?xml version="1.0" encoding="utf-8"?>
<FrameLayout android:layout_width="fill_parent" android:layout_height="46.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.transsion.baseui.music.RoundedCornerLayout android:id="@id/roundExpand" android:background="@drawable/bg_music_floating0_4_4_0" android:layout_width="fill_parent" android:layout_height="46.0dip" android:layout_marginStart="-2.0dip" android:layout_marginEnd="96.0dip" app:cornerRadiusBottomRight="4.0dip" app:cornerRadiusTopRight="4.0dip" app:layout_constraintEnd_toStartOf="@id/space" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent">
        <com.noober.background.view.BLView android:layout_width="fill_parent" android:layout_height="fill_parent" app:bl_corners_bottomRightRadius="4.0dip" app:bl_corners_topRightRadius="4.0dip" app:bl_solid_color="@color/black_40" />
        <androidx.appcompat.widget.AppCompatImageView android:id="@id/ivCoverBg" android:background="@color/white_20" android:layout_width="44.0dip" android:layout_height="44.0dip" android:scaleType="center" app:layout_constraintBottom_toTopOf="@id/progress" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:srcCompat="@drawable/iv_music_floating_icon" />
        <androidx.appcompat.widget.AppCompatImageView android:id="@id/ivCover" android:layout_width="44.0dip" android:layout_height="44.0dip" android:scaleType="centerCrop" app:layout_constraintBottom_toTopOf="@id/progress" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
        <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/white_80" android:ellipsize="end" android:id="@id/tvTitle" android:layout_width="0.0dip" android:layout_height="wrap_content" android:maxLines="1" android:layout_marginStart="4.0dip" app:layout_constraintBottom_toTopOf="@id/tvDescription" app:layout_constraintEnd_toStartOf="@id/ivPlayPause" app:layout_constraintStart_toEndOf="@id/ivCover" app:layout_constraintTop_toTopOf="@id/ivCover" app:layout_constraintVertical_chainStyle="packed" style="@style/style_regular_text" />
        <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/white_70" android:ellipsize="end" android:id="@id/tvDescription" android:visibility="gone" android:layout_width="0.0dip" android:layout_height="wrap_content" android:maxLines="1" app:layout_constraintBottom_toBottomOf="@id/ivCover" app:layout_constraintEnd_toEndOf="@id/tvTitle" app:layout_constraintStart_toStartOf="@id/tvTitle" app:layout_constraintTop_toBottomOf="@id/tvTitle" style="@style/style_regular_text" />
        <androidx.appcompat.widget.AppCompatImageView android:id="@id/ivPlayPause" android:layout_width="wrap_content" android:layout_height="fill_parent" android:src="@drawable/music_float_play" android:paddingStart="16.0dip" android:paddingEnd="8.0dip" app:layout_constraintBottom_toBottomOf="@id/ivClose" app:layout_constraintEnd_toStartOf="@id/ivClose" app:layout_constraintTop_toTopOf="@id/ivClose" />
        <androidx.appcompat.widget.AppCompatImageView android:id="@id/ivClose" android:paddingLeft="8.0dip" android:paddingRight="8.0dip" android:layout_width="wrap_content" android:layout_height="fill_parent" android:src="@drawable/music_float_close" android:paddingStart="0.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent" />
        <ProgressBar android:id="@id/progress" android:layout_width="fill_parent" android:layout_height="2.0dip" android:max="10000" android:progressDrawable="@drawable/bg_progress_bar_music" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/ivCover" style="?android:progressBarStyleHorizontal" />
    </com.transsion.baseui.music.RoundedCornerLayout>
    <com.transsion.baseui.music.RoundedCornerLayout android:id="@id/roundFold" android:background="@drawable/bg_music_floating0_4_4_0" android:visibility="gone" android:layout_width="30.0dip" android:layout_height="46.0dip" android:layout_marginStart="-2.0dip" app:layout_constraintBottom_toBottomOf="@id/roundExpand" app:layout_constraintStart_toStartOf="@id/roundExpand" app:layout_constraintTop_toTopOf="@id/roundExpand">
        <com.noober.background.view.BLView android:layout_width="fill_parent" android:layout_height="fill_parent" app:bl_corners_bottomRightRadius="4.0dip" app:bl_corners_topRightRadius="4.0dip" app:bl_solid_color="@color/black_40" />
        <androidx.appcompat.widget.AppCompatImageView android:layout_width="wrap_content" android:layout_height="wrap_content" android:src="@drawable/iv_music_floating_icon" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    </com.transsion.baseui.music.RoundedCornerLayout>
</FrameLayout>
