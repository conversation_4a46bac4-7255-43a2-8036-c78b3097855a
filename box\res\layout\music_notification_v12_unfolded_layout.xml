<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout android:layout_width="fill_parent" android:layout_height="48.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <ImageView android:id="@id/ivCover" android:layout_width="48.0dip" android:layout_height="48.0dip" android:scaleType="centerInside" />
    <ImageView android:id="@id/ivPlayPause" android:layout_width="32.0dip" android:layout_height="32.0dip" android:layout_centerVertical="true" android:layout_alignParentEnd="true" />
    <TextView android:textStyle="bold" android:textColor="@color/text_01" android:ellipsize="end" android:id="@id/tvTitle" android:layout_width="fill_parent" android:layout_height="wrap_content" android:maxLines="1" android:layout_marginStart="12.0dip" android:layout_marginEnd="12.0dip" android:layout_toStartOf="@id/ivPlayPause" android:layout_toEndOf="@id/ivCover" />
    <ProgressBar android:id="@id/progress" android:layout_width="fill_parent" android:layout_height="3.0dip" android:layout_marginTop="6.0dip" android:max="100" android:progress="30" android:progressDrawable="@drawable/player_download_progress_drawable" android:layout_below="@id/tvTitle" android:layout_marginStart="12.0dip" android:layout_marginEnd="12.0dip" android:layout_toStartOf="@id/ivPlayPause" android:layout_toEndOf="@id/ivCover" style="?android:progressBarStyleHorizontal" />
    <TextView android:textSize="12.0sp" android:textColor="@color/text_02" android:id="@id/tvTimeConsuming" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_alignParentBottom="true" android:layout_marginStart="12.0dip" android:layout_toEndOf="@id/ivCover" />
    <TextView android:textSize="12.0sp" android:textColor="@color/text_02" android:id="@id/tvDuration" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_alignParentBottom="true" android:layout_marginEnd="12.0dip" android:layout_toStartOf="@id/ivPlayPause" />
</RelativeLayout>
