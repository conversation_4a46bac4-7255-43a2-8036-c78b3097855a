<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:background="@drawable/ad_shape_btn_11_bg" android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <View android:id="@id/guideline" android:layout_width="0.0dip" android:layout_height="0.0dip" app:layout_constraintDimensionRatio="h,16:9" app:layout_constraintLeft_toLeftOf="parent" app:layout_constraintRight_toRightOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <FrameLayout android:id="@id/flRoot" android:layout_width="fill_parent" android:layout_height="0.0dip" android:layout_marginLeft="6.0dip" android:layout_marginTop="6.0dip" android:layout_marginRight="6.0dip" android:layout_marginBottom="6.0dip" app:layout_constraintDimensionRatio="H,16:9" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent">
        <androidx.cardview.widget.CardView android:id="@id/cardView" android:layout_width="fill_parent" android:layout_height="fill_parent" app:cardCornerRadius="4.0dip" app:cardElevation="0.0dip">
            <com.hisavana.mediation.ad.TMediaView android:id="@id/native_ad_media" android:background="@color/module_06" android:layout_width="fill_parent" android:layout_height="fill_parent" />
        </androidx.cardview.widget.CardView>
    </FrameLayout>
    <com.transsion.wrapperad.hi.MaskLayout android:id="@id/mask_ic" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="6.0dip" android:layout_marginBottom="6.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="@id/flRoot" app:layout_constraintTop_toBottomOf="@id/flRoot" app:xhg_mask_drawable="@drawable/ad_shape_dp_4">
        <com.hisavana.mediation.ad.TIconView android:id="@id/native_ad_icon" android:layout_width="32.0dip" android:layout_height="32.0dip" />
    </com.transsion.wrapperad.hi.MaskLayout>
    <TextView android:textSize="14.0sp" android:textStyle="bold" android:textColor="@color/text_ad_1" android:ellipsize="end" android:gravity="start|center" android:id="@id/native_ad_title" android:layout_width="0.0dip" android:layout_height="wrap_content" android:maxLines="1" android:includeFontPadding="false" android:layout_marginStart="8.0dip" android:layout_marginEnd="12.0dip" app:layout_constraintEnd_toStartOf="@id/native_ad_action" app:layout_constraintStart_toEndOf="@id/mask_ic" app:layout_constraintTop_toTopOf="@id/mask_ic" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/brand" android:ellipsize="end" android:gravity="center" android:id="@id/native_ad_action" android:background="@drawable/ad_shape_dp_8_bg_2" android:paddingLeft="12.0dip" android:paddingTop="9.0dip" android:paddingRight="12.0dip" android:paddingBottom="9.0dip" android:layout_width="wrap_content" android:layout_height="32.0dip" android:minWidth="68.0dip" android:text="@string/ad_more_details" android:maxLines="1" android:includeFontPadding="false" android:textAllCaps="false" android:elevation="0.0dip" app:layout_constraintBottom_toBottomOf="@id/mask_ic" app:layout_constraintEnd_toEndOf="@id/flRoot" app:layout_constraintTop_toTopOf="@id/mask_ic" style="@style/style_medium_text" />
    <TextView android:textSize="12.0sp" android:textColor="@color/text_ad_2" android:ellipsize="end" android:gravity="center_vertical" android:id="@id/native_ad_des" android:layout_width="0.0dip" android:layout_height="wrap_content" android:maxLines="1" android:includeFontPadding="false" android:lineSpacingExtra="0.0dip" android:layout_marginEnd="12.0dip" app:layout_constraintEnd_toStartOf="@id/native_ad_action" app:layout_constraintStart_toStartOf="@id/native_ad_title" app:layout_constraintTop_toBottomOf="@id/native_ad_title" />
    <androidx.appcompat.widget.LinearLayoutCompat android:orientation="horizontal" android:paddingLeft="2.0dip" android:paddingRight="2.0dip" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginLeft="2.0dip" android:layout_marginTop="6.0dip" android:layout_marginRight="2.0dip" android:minHeight="10.0dip" app:layout_constraintEnd_toEndOf="@id/flRoot" app:layout_constraintStart_toStartOf="@id/flRoot" app:layout_constraintTop_toTopOf="@id/flRoot">
        <androidx.cardview.widget.CardView android:layout_gravity="center_vertical" android:id="@id/adChoicesViewCard" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginStart="4.0dip" card_view:cardBackgroundColor="@color/transparent" card_view:cardCornerRadius="4.0dip" card_view:cardElevation="0.0dip" xmlns:card_view="http://schemas.android.com/apk/res-auto">
            <com.hisavana.mediation.ad.TAdChoicesView android:id="@id/native_ad_choices" android:layout_width="wrap_content" android:layout_height="wrap_content" />
        </androidx.cardview.widget.CardView>
        <com.transsion.wrapperad.view.AdTagView android:layout_gravity="center_vertical" android:id="@id/adIcon" android:layout_width="wrap_content" android:layout_height="16.0dip" android:layout_marginStart="4.0dip" />
        <androidx.constraintlayout.widget.ConstraintLayout android:layout_gravity="center_vertical" android:id="@id/store_mark_container" android:background="@drawable/ad_shape_store_mark_bg" android:layout_width="wrap_content" android:layout_height="16.0dip" android:layout_marginStart="4.0dip">
            <com.hisavana.mediation.ad.TStoreMarkView android:layout_gravity="center" android:id="@id/store_mark_view" android:layout_width="wrap_content" android:layout_height="wrap_content" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.appcompat.widget.LinearLayoutCompat>
</androidx.constraintlayout.widget.ConstraintLayout>
