<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView android:layout_width="wrap_content" android:layout_height="wrap_content" app:cardCornerRadius="4.0dip" app:cardElevation="0.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.constraintlayout.widget.ConstraintLayout android:layout_width="wrap_content" android:layout_height="wrap_content">
        <androidx.appcompat.widget.AppCompatImageView android:id="@id/ivBgBurl" android:layout_width="160.0dip" android:layout_height="62.0dip" android:scaleType="centerCrop" android:alpha="0.16" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:shapeAppearanceOverlay="@style/corner_style_6" />
        <androidx.cardview.widget.CardView android:id="@id/ad_card_view" android:layout_width="46.0dip" android:layout_height="46.0dip" android:layout_marginStart="8.0dip" app:cardCornerRadius="8.0dip" app:cardElevation="0.0dip" app:layout_constraintBottom_toBottomOf="@id/ivBgBurl" app:layout_constraintStart_toStartOf="@id/ivBgBurl" app:layout_constraintTop_toTopOf="@id/ivBgBurl">
            <com.hisavana.mediation.ad.TIconView android:id="@id/native_ad_icon" android:layout_width="fill_parent" android:layout_height="fill_parent" />
        </androidx.cardview.widget.CardView>
        <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/white_80" android:ellipsize="end" android:id="@id/native_ad_title" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginLeft="8.0dip" android:layout_marginRight="8.0dip" android:maxLines="1" app:layout_constraintEnd_toEndOf="@id/ivBgBurl" app:layout_constraintStart_toEndOf="@id/ad_card_view" app:layout_constraintTop_toTopOf="@id/ad_card_view" style="@style/style_medium_text" />
        <androidx.appcompat.widget.AppCompatImageView android:layout_gravity="center_vertical" android:id="@id/ivStar" android:layout_width="12.0dip" android:layout_height="12.0dip" android:src="@drawable/ad_link_star" app:layout_constraintBottom_toBottomOf="@id/call_to_action" app:layout_constraintStart_toStartOf="@id/native_ad_title" app:layout_constraintTop_toTopOf="@id/call_to_action" />
        <TextView android:textSize="12.0sp" android:textColor="@color/yellow_dark_60" android:gravity="start" android:id="@id/tvStarNum" android:layout_width="0.0dip" android:layout_height="wrap_content" android:maxLines="1" android:layout_marginStart="2.0dip" app:layout_constraintBottom_toBottomOf="@id/call_to_action" app:layout_constraintEnd_toStartOf="@id/call_to_action" app:layout_constraintStart_toEndOf="@id/ivStar" app:layout_constraintTop_toTopOf="@id/call_to_action" />
        <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/gray_dark_00" android:gravity="center" android:id="@id/call_to_action" android:background="@drawable/bg_gradient_brand_4" android:paddingLeft="8.0dip" android:paddingRight="8.0dip" android:layout_width="wrap_content" android:layout_height="20.0dip" android:layout_marginEnd="8.0dip" app:layout_constraintBottom_toBottomOf="@id/ad_card_view" app:layout_constraintEnd_toEndOf="parent" style="@style/style_import_text" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.cardview.widget.CardView>
