<?xml version="1.0" encoding="utf-8"?>
<FrameLayout android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.cardview.widget.CardView android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginLeft="12.0dip" android:layout_marginTop="12.0dip" android:layout_marginRight="12.0dip" android:layout_marginBottom="12.0dip" app:cardCornerRadius="6.0dip">
        <androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="wrap_content">
            <FrameLayout android:id="@id/flRoot" android:layout_width="0.0dip" android:layout_height="0.0dip" app:cardCornerRadius="6.0dip" app:cardElevation="0.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintDimensionRatio="16:9" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent">
                <com.hisavana.mediation.ad.TMediaView android:id="@id/coverview" android:layout_width="fill_parent" android:layout_height="fill_parent" app:sspScaleType="centerCrop" />
            </FrameLayout>
            <com.transsion.wrapperad.view.AdTagView android:id="@id/adIcon" android:layout_width="wrap_content" android:layout_height="16.0dip" android:layout_marginTop="6.0dip" android:src="@mipmap/ad_icon_1" android:layout_marginStart="6.0dip" app:layout_constraintStart_toStartOf="@id/flRoot" app:layout_constraintTop_toTopOf="@id/flRoot" />
            <androidx.cardview.widget.CardView android:id="@id/adChoicesViewCard" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginStart="4.0dip" app:cardBackgroundColor="@color/transparent" app:cardCornerRadius="4.0dip" app:cardElevation="0.0dip" app:layout_constraintBottom_toBottomOf="@id/adIcon" app:layout_constraintStart_toEndOf="@id/adIcon" app:layout_constraintTop_toTopOf="@id/adIcon">
                <com.hisavana.mediation.ad.TAdChoicesView android:id="@id/adChoicesView" android:layout_width="wrap_content" android:layout_height="wrap_content" />
            </androidx.cardview.widget.CardView>
            <com.hisavana.mediation.ad.TStoreMarkView android:layout_gravity="center_vertical" android:id="@id/store_mark_view" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginStart="4.0dip" app:layout_constraintBottom_toBottomOf="@id/adChoicesViewCard" app:layout_constraintStart_toEndOf="@id/adChoicesViewCard" app:layout_constraintTop_toTopOf="@id/adChoicesViewCard" />
            <androidx.appcompat.widget.AppCompatTextView android:textSize="14.0sp" android:textColor="@color/white" android:gravity="center" android:id="@id/call_to_action" android:background="@drawable/ad_shape_btn_100" android:paddingLeft="12.0dip" android:paddingRight="12.0dip" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginBottom="12.0dip" android:minHeight="28.0dip" android:layout_marginEnd="12.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" style="@style/style_import_text" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.cardview.widget.CardView>
</FrameLayout>
