<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="160.0dip" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.hisavana.mediation.ad.TMediaView android:id="@id/coverview" android:background="@android:color/black" android:layout_width="0.0dip" android:layout_height="90.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <androidx.appcompat.widget.AppCompatImageView android:layout_width="wrap_content" android:layout_height="wrap_content" android:src="@mipmap/ad_icon_2" app:layout_constraintStart_toStartOf="@id/coverview" app:layout_constraintTop_toTopOf="@id/coverview" />
    <androidx.appcompat.widget.LinearLayoutCompat android:orientation="horizontal" android:layout_width="0.0dip" android:layout_height="wrap_content" android:minHeight="10.0dip" app:layout_constraintEnd_toEndOf="@id/coverview" app:layout_constraintStart_toStartOf="@id/coverview" app:layout_constraintTop_toTopOf="@id/coverview">
        <View android:layout_width="0.0dip" android:layout_height="14.0dip" android:layout_weight="1.0" />
        <com.hisavana.mediation.ad.TAdChoicesView android:id="@id/adChoicesView" android:layout_width="wrap_content" android:layout_height="wrap_content" />
        <com.hisavana.mediation.ad.TStoreMarkView android:id="@id/store_mark_view" android:layout_width="wrap_content" android:layout_height="14.0dip" />
    </androidx.appcompat.widget.LinearLayoutCompat>
</androidx.constraintlayout.widget.ConstraintLayout>
