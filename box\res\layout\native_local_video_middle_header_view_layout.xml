<?xml version="1.0" encoding="utf-8"?>
<FrameLayout android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="84.0dip" android:layout_marginStart="16.0dip" android:layout_marginEnd="16.0dip">
        <androidx.cardview.widget.CardView android:id="@id/cardView" android:layout_width="wrap_content" android:layout_height="68.0dip" app:cardCornerRadius="8.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent">
            <com.hisavana.mediation.ad.TMediaView android:id="@id/coverview" android:background="@android:color/black" android:layout_width="120.0dip" android:layout_height="fill_parent" app:sspScaleType="centerCrop" />
            <androidx.appcompat.widget.AppCompatImageView android:layout_width="wrap_content" android:layout_height="wrap_content" android:src="@mipmap/ad_icon_1" />
        </androidx.cardview.widget.CardView>
        <com.hisavana.mediation.ad.TAdChoicesView android:id="@id/adChoicesView" android:layout_width="wrap_content" android:layout_height="wrap_content" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent" />
        <com.hisavana.mediation.ad.TStoreMarkView android:id="@id/store_mark_view" android:layout_width="wrap_content" android:layout_height="wrap_content" app:layout_constraintEnd_toStartOf="@id/adChoicesView" app:layout_constraintTop_toTopOf="@id/adChoicesView" />
        <androidx.appcompat.widget.AppCompatTextView android:textSize="14.0sp" android:textStyle="bold" android:textColor="@color/text_ad_1" android:ellipsize="end" android:id="@id/native_ad_body" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginTop="2.0dip" android:maxLines="2" android:layout_marginStart="8.0dip" android:layout_marginEnd="12.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/cardView" app:layout_constraintTop_toTopOf="@id/cardView" style="@style/style_regular_text" />
        <View android:id="@id/lineView" android:layout_width="1.0dip" android:layout_height="16.0dip" android:layout_marginBottom="6.0dip" android:layout_marginStart="7.0dip" app:layout_constraintBottom_toBottomOf="@id/cardView" app:layout_constraintStart_toEndOf="@id/cardView" />
        <com.hisavana.mediation.ad.TIconView android:id="@id/native_ad_icon" android:layout_width="16.0dip" android:layout_height="16.0dip" app:layout_constraintBottom_toBottomOf="@id/lineView" app:layout_constraintStart_toEndOf="@id/lineView" />
        <TextView android:textSize="12.0sp" android:textColor="@color/text_ad_2" android:ellipsize="end" android:gravity="start|center" android:id="@id/native_ad_title" android:layout_width="0.0dip" android:layout_height="0.0dip" android:lines="1" android:layout_marginStart="4.0dip" android:layout_marginEnd="8.0dip" app:layout_constraintBottom_toBottomOf="@id/lineView" app:layout_constraintEnd_toStartOf="@id/call_to_action" app:layout_constraintStart_toEndOf="@id/native_ad_icon" app:layout_constraintTop_toTopOf="@id/lineView" app:layout_goneMarginStart="0.0dip" style="@style/style_regular_text" />
        <androidx.appcompat.widget.AppCompatTextView android:textSize="10.0sp" android:textColor="@color/white" android:ellipsize="end" android:gravity="center" android:id="@id/call_to_action" android:background="@drawable/ad_shape_btn_bg" android:paddingLeft="12.0dip" android:paddingRight="12.0dip" android:layout_width="0.0dip" android:layout_height="24.0dip" android:maxLines="1" android:textAllCaps="false" android:paddingStart="2.0dip" android:paddingEnd="2.0dip" app:layout_constraintBottom_toBottomOf="@id/cardView" app:layout_constraintEnd_toEndOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</FrameLayout>
