<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.hisavana.mediation.ad.TMediaView android:id="@id/coverview" android:background="@android:color/black" android:layout_width="fill_parent" android:layout_height="200.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:sspScaleType="centerCrop" />
    <androidx.appcompat.widget.AppCompatImageView android:layout_width="wrap_content" android:layout_height="wrap_content" android:src="@mipmap/ad_icon_3" app:layout_constraintBottom_toBottomOf="@id/coverview" app:layout_constraintEnd_toEndOf="@id/coverview" />
    <com.hisavana.mediation.ad.TAdChoicesView android:id="@id/adChoicesView" android:layout_width="wrap_content" android:layout_height="wrap_content" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <com.hisavana.mediation.ad.TStoreMarkView android:id="@id/store_mark_view" android:layout_width="wrap_content" android:layout_height="wrap_content" app:layout_constraintEnd_toStartOf="@id/adChoicesView" app:layout_constraintTop_toTopOf="@id/adChoicesView" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/white" android:ellipsize="end" android:gravity="center" android:id="@id/call_to_action" android:background="@drawable/ad_shape_btn_bg" android:paddingLeft="10.0dip" android:paddingRight="10.0dip" android:layout_width="88.0dip" android:layout_height="32.0dip" android:layout_marginTop="16.0dip" android:layout_marginBottom="16.0dip" android:maxLines="1" android:textAllCaps="false" android:drawableStart="@mipmap/ad_icon_download_whit" android:paddingStart="2.0dip" android:paddingEnd="2.0dip" android:layout_marginEnd="16.0dip" app:layout_constraintBottom_toTopOf="@id/viewLiner" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toBottomOf="@id/coverview" />
    <View android:id="@id/viewLiner" android:background="@color/ad_line" android:layout_width="fill_parent" android:layout_height="1.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" />
    <TextView android:textSize="14.0sp" android:textStyle="bold" android:textColor="@color/text_ad_1" android:ellipsize="end" android:gravity="start|center" android:id="@id/native_ad_title" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginTop="16.0dip" android:lines="1" android:layout_marginStart="14.0dip" android:layout_marginEnd="16.0dip" app:layout_constraintEnd_toStartOf="@id/call_to_action" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/coverview" style="@style/style_regular_text" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/text_ad_2" android:ellipsize="end" android:id="@id/native_ad_body" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginTop="5.0dip" android:maxLines="1" app:layout_constraintEnd_toEndOf="@id/native_ad_title" app:layout_constraintStart_toStartOf="@id/native_ad_title" app:layout_constraintTop_toBottomOf="@id/native_ad_title" style="@style/style_regular_text" />
</androidx.constraintlayout.widget.ConstraintLayout>
