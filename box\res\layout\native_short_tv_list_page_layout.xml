<?xml version="1.0" encoding="utf-8"?>
<FrameLayout android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <View android:layout_width="fill_parent" android:layout_height="fill_parent" />
    <androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="fill_parent">
        <com.hisavana.mediation.ad.TMediaView android:id="@id/coverview" android:layout_width="fill_parent" android:layout_height="fill_parent" android:layout_marginBottom="80.0dip" />
        <com.hisavana.mediation.ad.TAdChoicesView android:id="@id/adChoicesView" android:layout_width="wrap_content" android:layout_height="wrap_content" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/store_mark_view" app:layout_constraintTop_toTopOf="parent" />
        <com.hisavana.mediation.ad.TStoreMarkView android:id="@id/store_mark_view" android:layout_width="wrap_content" android:layout_height="wrap_content" app:layout_constraintEnd_toStartOf="@id/adChoicesView" app:layout_constraintTop_toTopOf="@id/adChoicesView" />
        <View android:background="@drawable/ad_shape_bg_bg_ad" android:layout_width="0.0dip" android:layout_height="320.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" />
        <com.google.android.material.imageview.ShapeableImageView android:id="@id/adIcon" android:padding="0.5dip" android:layout_width="36.0dip" android:layout_height="36.0dip" android:layout_marginBottom="25.0dip" android:src="@mipmap/ad_ic_avatar" android:scaleType="centerCrop" app:layout_constraintBottom_toTopOf="@id/tvLike" app:layout_constraintEnd_toEndOf="@id/ivShare" app:layout_constraintStart_toStartOf="@id/ivShare" app:shapeAppearanceOverlay="@style/circle_style" app:strokeColor="#33eeeeee" app:strokeWidth="1.0dip" />
        <androidx.appcompat.widget.AppCompatTextView android:textSize="11.0sp" android:textColor="@color/white" android:gravity="center_horizontal" android:id="@id/tvLike" android:layout_width="40.0dip" android:layout_height="wrap_content" android:layout_marginBottom="20.0dip" android:text="59" android:shadowColor="#80000000" android:shadowRadius="3.0" android:drawablePadding="2.0dip" app:drawableTopCompat="@mipmap/short_tv_favorite" app:layout_constraintBottom_toTopOf="@id/tvComment" app:layout_constraintEnd_toEndOf="@id/ivShare" />
        <androidx.appcompat.widget.AppCompatImageView android:id="@id/tvComment" android:visibility="gone" android:layout_width="40.0dip" android:layout_height="40.0dip" android:layout_marginBottom="20.0dip" android:scaleType="center" app:layout_constraintBottom_toTopOf="@id/ivShare" app:layout_constraintEnd_toEndOf="@id/ivShare" app:srcCompat="@mipmap/ad_ic_video_comment" />
        <androidx.appcompat.widget.AppCompatImageView android:id="@id/ivShare" android:layout_width="40.0dip" android:layout_height="40.0dip" android:layout_marginBottom="26.0dip" android:scaleType="center" android:layout_marginEnd="12.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:srcCompat="@mipmap/ad_ic_video_share" />
        <androidx.constraintlayout.widget.ConstraintLayout android:id="@id/cl_subject_res" android:background="@drawable/ad_shape_imm_video_subject_bg" android:layout_width="0.0dip" android:layout_height="56.0dip" android:layout_marginBottom="74.0dip" android:alpha="0.8" android:layout_marginStart="16.0dip" android:layout_marginEnd="12.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toStartOf="@id/ivShare" app:layout_constraintStart_toStartOf="parent" app:layout_goneMarginBottom="0.0dip">
            <com.transsion.wrapperad.hi.MaskLayout android:id="@id/maskView" android:layout_width="48.0dip" android:layout_height="48.0dip" android:layout_marginStart="4.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:xhg_mask_drawable="@drawable/ad_shape_dp_4">
                <com.hisavana.mediation.ad.TIconView android:id="@id/native_ad_icon" android:layout_width="48.0dip" android:layout_height="48.0dip" android:scaleType="centerCrop" />
            </com.transsion.wrapperad.hi.MaskLayout>
            <TextView android:textSize="14.0sp" android:textColor="@android:color/white" android:ellipsize="end" android:gravity="start|center" android:id="@id/native_ad_title" android:layout_width="0.0dip" android:layout_height="fill_parent" android:maxLines="2" android:layout_marginStart="4.0dip" android:layout_marginEnd="8.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toStartOf="@id/v_subject_res_line" app:layout_constraintStart_toEndOf="@id/maskView" app:layout_constraintTop_toTopOf="parent" style="@style/style_regular_text" />
            <View android:id="@id/v_subject_res_line" android:background="#33ffffff" android:layout_width="1.0dip" android:layout_height="14.0dip" android:layout_marginEnd="8.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toStartOf="@id/call_to_action" app:layout_constraintTop_toTopOf="parent" />
            <androidx.appcompat.widget.AppCompatTextView android:textSize="14.0sp" android:textColor="#ff07b84e" android:id="@id/call_to_action" android:background="@android:color/transparent" android:paddingLeft="12.0dip" android:paddingRight="12.0dip" android:layout_width="wrap_content" android:layout_height="wrap_content" android:minWidth="68.0dip" android:text="Download" android:includeFontPadding="false" android:drawableStart="@mipmap/ad_icon_download_green" android:layout_marginEnd="8.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent" style="@style/style_medium_text" />
        </androidx.constraintlayout.widget.ConstraintLayout>
        <TextView android:textSize="14.0sp" android:textColor="@android:color/white" android:ellipsize="end" android:gravity="start|center" android:id="@id/native_ad_body" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginTop="12.0dip" android:maxLines="2" android:layout_marginStart="16.0dip" android:layout_marginEnd="12.0dip" app:layout_constraintBottom_toBottomOf="@id/ivShare" app:layout_constraintEnd_toStartOf="@id/ivShare" app:layout_constraintStart_toStartOf="parent" style="@style/style_regular_text" />
        <ProgressBar android:id="@id/progress_bar" android:layout_width="fill_parent" android:layout_height="2.0dip" android:max="100" android:progress="50" android:progressDrawable="@drawable/progress_bg" app:layout_constraintBottom_toBottomOf="parent" style="?android:progressBarStyleHorizontal" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</FrameLayout>
