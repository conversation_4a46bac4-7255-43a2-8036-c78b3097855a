<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:gravity="center" android:id="@id/ad_unit" android:background="@android:color/white" android:padding="@dimen/hisavana_ad_dimen_12" android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.cloud.hisavana.sdk.api.view.MediaView android:id="@id/hisavana_coverview" android:layout_width="107.0dip" android:layout_height="71.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintLeft_toLeftOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <TextView android:id="@id/hisavana_native_ad_body" android:layout_marginLeft="@dimen/hisavana_ad_dimen_12" android:text="Make marketing more convenient and efficient." app:layout_constraintLeft_toRightOf="@id/hisavana_coverview" app:layout_constraintRight_toRightOf="parent" app:layout_constraintTop_toTopOf="parent" style="@style/native_body_style" />
    <com.cloud.sdk.commonutil.widget.TranCircleImageView android:id="@id/hisavana_native_ad_icon" android:layout_width="@dimen/hisavana_ad_dimen_14" android:layout_height="@dimen/hisavana_ad_dimen_14" android:layout_marginLeft="@dimen/hisavana_ad_dimen_12" android:layout_marginRight="@dimen/hisavana_ad_dimen_12" app:layout_constraintBottom_toBottomOf="@id/hisavana_ll" app:layout_constraintLeft_toRightOf="@id/hisavana_coverview" app:layout_constraintTop_toTopOf="@id/hisavana_ll" />
    <TextView android:id="@id/hisavana_native_ad_title" android:layout_marginLeft="@dimen/hisavana_ad_dimen_4" android:layout_marginRight="@dimen/hisavana_ad_dimen_4" android:text="eagllwin" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintLeft_toRightOf="@id/hisavana_native_ad_icon" app:layout_constraintRight_toLeftOf="@id/hisavana_ll" app:layout_constraintTop_toTopOf="@id/hisavana_ll" app:layout_goneMarginLeft="@dimen/hisavana_ad_dimen_12" style="@style/native_title_style" />
    <LinearLayout android:gravity="center_vertical" android:orientation="horizontal" android:id="@id/hisavana_ll" android:layout_width="wrap_content" android:layout_height="wrap_content" app:layout_constraintBottom_toTopOf="@id/ps_mark_view" app:layout_constraintRight_toRightOf="parent">
        <TextView android:id="@id/hisavana_call_to_action" android:text="INSTALL" style="@style/btn_style" />
    </LinearLayout>
    <com.cloud.hisavana.sdk.api.view.StoreMarkView android:id="@id/ps_mark_view" android:layout_width="wrap_content" android:layout_height="wrap_content" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="@id/hisavana_ll" />
    <include android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="@dimen/hisavana_ad_dimen_4" android:layout_marginStart="@dimen/hisavana_ad_dimen_4" app:layout_constraintLeft_toLeftOf="@id/hisavana_coverview" app:layout_constraintTop_toTopOf="@id/hisavana_coverview" layout="@layout/include_ad_flag" />
</androidx.constraintlayout.widget.ConstraintLayout>
