<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:id="@id/ad_unit" android:background="@android:color/white" android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.constraintlayout.widget.ConstraintLayout android:padding="@dimen/hisavana_ad_dimen_12" android:layout_width="fill_parent" android:layout_height="wrap_content" app:layout_constraintLeft_toLeftOf="parent" app:layout_constraintTop_toTopOf="parent">
        <com.cloud.hisavana.sdk.api.view.MediaView android:id="@id/hisavana_coverview" android:layout_width="107.0dip" android:layout_height="71.0dip" android:layout_marginLeft="@dimen/hisavana_ad_dimen_15" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintLeft_toRightOf="@id/hisavana_native_ad_body" app:layout_constraintRight_toRightOf="parent" app:layout_constraintTop_toTopOf="parent" />
        <com.cloud.sdk.commonutil.widget.TranCircleImageView android:id="@id/hisavana_native_ad_icon" android:layout_width="@dimen/hisavana_ad_dimen_14" android:layout_height="@dimen/hisavana_ad_dimen_14" app:layout_constraintLeft_toLeftOf="parent" app:layout_constraintTop_toTopOf="parent" />
        <TextView android:id="@id/hisavana_native_ad_title" android:layout_marginLeft="@dimen/hisavana_ad_dimen_4" android:layout_marginRight="@dimen/hisavana_ad_dimen_15" android:text="eagllwin" app:layout_constraintLeft_toRightOf="@id/hisavana_native_ad_icon" app:layout_constraintRight_toLeftOf="@id/hisavana_coverview" app:layout_constraintTop_toTopOf="parent" app:layout_goneMarginLeft="0.0dip" style="@style/native_title_style" />
        <TextView android:id="@id/hisavana_native_ad_body" android:layout_marginTop="@dimen/hisavana_ad_dimen_4" android:text="Make marketing more convenient and efficient." app:layout_constraintLeft_toLeftOf="parent" app:layout_constraintRight_toLeftOf="@id/hisavana_coverview" app:layout_constraintTop_toBottomOf="@id/hisavana_native_ad_title" style="@style/native_body_style" />
        <TextView android:id="@id/hisavana_call_to_action" android:layout_marginTop="@dimen/hisavana_ad_dimen_8" android:text="INSTALL" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintLeft_toLeftOf="parent" app:layout_constraintTop_toBottomOf="@id/hisavana_native_ad_body" style="@style/btn_style" />
        <com.cloud.hisavana.sdk.api.view.StoreMarkView android:id="@id/ps_mark_view" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginStart="@dimen/hisavana_ad_dimen_4" app:layout_constraintBottom_toBottomOf="@id/hisavana_call_to_action" app:layout_constraintStart_toEndOf="@id/hisavana_call_to_action" app:layout_constraintTop_toTopOf="@id/hisavana_call_to_action" />
    </androidx.constraintlayout.widget.ConstraintLayout>
    <include android:layout_width="wrap_content" android:layout_height="wrap_content" app:layout_constraintRight_toRightOf="parent" app:layout_constraintTop_toTopOf="parent" layout="@layout/include_ad_flag" />
</androidx.constraintlayout.widget.ConstraintLayout>
