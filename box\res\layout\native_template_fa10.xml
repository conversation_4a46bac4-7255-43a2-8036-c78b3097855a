<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:id="@id/ad_unit" android:background="@android:color/white" android:padding="@dimen/hisavana_ad_dimen_12" android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <TextView android:textStyle="normal" android:id="@id/hisavana_native_ad_body" android:layout_width="fill_parent" android:text="In visual China search, the amount of video material is small not find the…" android:layout_marginEnd="@dimen/hisavana_ad_dimen_12" app:layout_constraintLeft_toLeftOf="parent" app:layout_constraintRight_toRightOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:layout_goneMarginEnd="0.0dip" style="@style/native_body_style" />
    <com.cloud.hisavana.sdk.api.view.MediaView android:id="@id/hisavana_coverview" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="@dimen/hisavana_ad_dimen_8" app:layout_constraintLeft_toLeftOf="parent" app:layout_constraintRight_toRightOf="parent" app:layout_constraintTop_toBottomOf="@id/hisavana_native_ad_body" />
    <com.cloud.sdk.commonutil.widget.TranCircleImageView android:id="@id/hisavana_native_ad_icon" android:layout_width="@dimen/hisavana_ad_dimen_32" android:layout_height="@dimen/hisavana_ad_dimen_32" android:layout_marginTop="@dimen/hisavana_ad_dimen_12" app:layout_constraintLeft_toLeftOf="parent" app:layout_constraintTop_toBottomOf="@id/hisavana_coverview" />
    <TextView android:id="@id/hisavana_native_ad_title" android:layout_marginLeft="@dimen/hisavana_ad_dimen_8" android:layout_marginRight="@dimen/hisavana_ad_dimen_8" android:text="eagllwin" app:layout_constraintBottom_toBottomOf="@id/hisavana_native_ad_icon" app:layout_constraintLeft_toRightOf="@id/hisavana_native_ad_icon" app:layout_constraintRight_toLeftOf="@id/hisavana_call_to_action" app:layout_constraintTop_toTopOf="@id/hisavana_native_ad_icon" style="@style/native_title_style1" />
    <TextView android:textSize="@dimen/hisavana_ad_dimen_12sp" android:id="@id/hisavana_call_to_action" android:paddingLeft="@dimen/hisavana_ad_dimen_8" android:paddingRight="@dimen/hisavana_ad_dimen_8" android:text="LEARN MORE" app:layout_constraintBottom_toBottomOf="@id/hisavana_native_ad_title" app:layout_constraintRight_toRightOf="parent" app:layout_constraintTop_toTopOf="@id/hisavana_native_ad_title" style="@style/btn_style" />
    <include android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="@dimen/hisavana_ad_dimen_8" android:layout_marginRight="@dimen/hisavana_ad_dimen_8" app:layout_constraintRight_toRightOf="@id/hisavana_coverview" app:layout_constraintTop_toTopOf="@id/hisavana_coverview" layout="@layout/include_ad_flag" />
    <com.cloud.hisavana.sdk.api.view.StoreMarkView android:id="@id/ps_mark_view" android:layout_width="wrap_content" android:layout_height="wrap_content" app:layout_constraintEnd_toEndOf="@id/hisavana_call_to_action" app:layout_constraintTop_toBottomOf="@id/hisavana_call_to_action" />
</androidx.constraintlayout.widget.ConstraintLayout>
