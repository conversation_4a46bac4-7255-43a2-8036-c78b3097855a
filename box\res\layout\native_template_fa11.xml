<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:id="@id/ad_unit" android:background="@android:color/white" android:padding="@dimen/hisavana_ad_dimen_12" android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.cloud.hisavana.sdk.api.view.MediaView android:id="@id/hisavana_coverview" android:layout_width="fill_parent" android:layout_height="wrap_content" app:layout_constraintLeft_toLeftOf="parent" app:layout_constraintRight_toRightOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <com.cloud.sdk.commonutil.widget.TranCircleImageView android:id="@id/hisavana_native_ad_icon" android:layout_width="@dimen/hisavana_ad_dimen_36" android:layout_height="@dimen/hisavana_ad_dimen_36" android:layout_marginTop="@dimen/hisavana_ad_dimen_10" app:layout_constraintLeft_toLeftOf="parent" app:layout_constraintTop_toBottomOf="@id/hisavana_coverview" />
    <TextView android:id="@id/hisavana_native_ad_title" android:layout_marginLeft="@dimen/hisavana_ad_dimen_8" android:layout_marginRight="@dimen/hisavana_ad_dimen_12" android:text="eagllwin " app:layout_constraintBottom_toTopOf="@id/hisavana_native_ad_body" app:layout_constraintHorizontal_bias="0.0" app:layout_constraintLeft_toRightOf="@id/hisavana_native_ad_icon" app:layout_constraintRight_toLeftOf="@id/barrier2" app:layout_constraintTop_toTopOf="@id/hisavana_native_ad_icon" style="@style/native_title_style1" />
    <TextView android:ellipsize="end" android:id="@id/hisavana_native_ad_body" android:layout_marginLeft="@dimen/hisavana_ad_dimen_8" android:layout_marginRight="@dimen/hisavana_ad_dimen_12" android:text="Make marketing more convenient and efficient. more convenient and effic" android:lines="1" app:layout_constraintBottom_toBottomOf="@id/hisavana_native_ad_icon" app:layout_constraintLeft_toRightOf="@id/hisavana_native_ad_icon" app:layout_constraintRight_toLeftOf="@id/barrier2" app:layout_constraintTop_toBottomOf="@id/hisavana_native_ad_title" style="@style/native_body_style1" />
    <androidx.constraintlayout.widget.Barrier android:id="@id/barrier2" android:layout_width="wrap_content" android:layout_height="wrap_content" app:barrierDirection="left" app:constraint_referenced_ids="hisavana_call_to_action" />
    <TextView android:textSize="@dimen/hisavana_ad_dimen_12sp" android:textColor="@android:color/white" android:gravity="center" android:layout_gravity="center_vertical" android:id="@id/hisavana_call_to_action" android:background="@drawable/hisavana_bg_blue_radius" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="INSTALL" android:textAllCaps="false" app:layout_constraintBottom_toBottomOf="@id/hisavana_native_ad_icon" app:layout_constraintRight_toRightOf="parent" app:layout_constraintTop_toTopOf="@id/hisavana_native_ad_icon" />
    <com.cloud.hisavana.sdk.api.view.StoreMarkView android:id="@id/ps_mark_view" android:layout_width="wrap_content" android:layout_height="wrap_content" app:layout_constraintEnd_toEndOf="@id/hisavana_ll" app:layout_constraintTop_toBottomOf="@id/hisavana_ll" />
    <include android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="@dimen/hisavana_ad_dimen_8" android:layout_marginRight="@dimen/hisavana_ad_dimen_8" app:layout_constraintRight_toRightOf="@id/hisavana_coverview" app:layout_constraintTop_toTopOf="@id/hisavana_coverview" layout="@layout/include_ad_flag" />
</androidx.constraintlayout.widget.ConstraintLayout>
