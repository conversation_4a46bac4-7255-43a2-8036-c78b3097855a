<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:id="@id/ad_unit" android:background="@android:color/black" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.cloud.hisavana.sdk.api.view.MediaView android:id="@id/hisavana_coverview" android:layout_width="fill_parent" android:layout_height="wrap_content" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintLeft_toLeftOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <LinearLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginBottom="@dimen/dp_44" android:layout_marginStart="@dimen/dp_20" app:layout_constraintBottom_toBottomOf="parent">
        <androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="wrap_content">
            <com.cloud.sdk.commonutil.widget.TranCircleImageView android:id="@id/hisavana_native_ad_icon" android:layout_width="@dimen/hisavana_ad_dimen_32" android:layout_height="@dimen/hisavana_ad_dimen_32" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintLeft_toLeftOf="parent" app:radiusYL="@dimen/hisavana_ad_dimen_16" />
            <TextView android:textColor="@android:color/white" android:id="@id/hisavana_native_ad_title" android:layout_width="wrap_content" android:text="eagllwin" app:layout_constrainedWidth="true" app:layout_constraintBottom_toBottomOf="@id/hisavana_native_ad_icon" app:layout_constraintHorizontal_bias="0.0" app:layout_constraintHorizontal_chainStyle="packed" app:layout_constraintLeft_toRightOf="@id/hisavana_native_ad_icon" app:layout_constraintRight_toLeftOf="@id/native_template_ad" app:layout_constraintTop_toTopOf="@id/hisavana_native_ad_icon" style="@style/native_title_style1" />
            <include android:id="@id/native_template_ad" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginLeft="@dimen/dp_4" app:layout_constraintBottom_toBottomOf="@id/hisavana_native_ad_title" app:layout_constraintLeft_toRightOf="@id/hisavana_native_ad_title" app:layout_constraintRight_toRightOf="parent" app:layout_constraintTop_toTopOf="@id/hisavana_native_ad_title" layout="@layout/include_ad_flag" />
        </androidx.constraintlayout.widget.ConstraintLayout>
        <TextView android:textColor="@android:color/white" android:ellipsize="end" android:id="@id/hisavana_native_ad_body" android:layout_marginRight="@dimen/hisavana_ad_dimen_80" android:layout_marginBottom="@dimen/dp_8" android:text="Make marketing more convenient and efficient." android:maxLines="2" style="@style/native_body_style1" />
        <com.cloud.hisavana.sdk.api.view.StoreMarkView android:id="@id/ps_mark_view" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="@dimen/hisavana_ad_dimen_4" android:layout_marginBottom="@dimen/hisavana_ad_dimen_4" />
        <TextView android:textSize="@dimen/hisavana_ad_dimen_12sp" android:id="@id/hisavana_call_to_action" android:paddingTop="@dimen/dp_6" android:paddingBottom="@dimen/dp_6" android:layout_width="fill_parent" android:layout_marginRight="@dimen/hisavana_ad_dimen_80" android:layout_marginBottom="@dimen/dp_10" android:text="INSTALL" style="@style/btn_style" />
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
