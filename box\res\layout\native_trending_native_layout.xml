<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:background="@drawable/ad_shape_btn_14_bg" android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <View android:id="@id/guideline" android:layout_width="0.0dip" android:layout_height="0.0dip" app:layout_constraintDimensionRatio="h,16:9" app:layout_constraintLeft_toLeftOf="parent" app:layout_constraintRight_toRightOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <FrameLayout android:id="@id/flRoot" android:background="@drawable/ad_shape_btn_15_bg" android:layout_width="fill_parent" android:layout_height="0.0dip" android:layout_marginLeft="6.0dip" android:layout_marginTop="6.0dip" android:layout_marginRight="6.0dip" android:layout_marginBottom="6.0dip" app:layout_constraintDimensionRatio="H,16:9" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent">
        <androidx.cardview.widget.CardView android:id="@id/cardView" android:layout_width="fill_parent" android:layout_height="fill_parent" app:cardCornerRadius="4.0dip" app:cardElevation="0.0dip">
            <com.hisavana.mediation.ad.TMediaView android:id="@id/coverview" android:background="@color/module_05" android:layout_width="fill_parent" android:layout_height="fill_parent" />
        </androidx.cardview.widget.CardView>
    </FrameLayout>
    <androidx.appcompat.widget.LinearLayoutCompat android:orientation="horizontal" android:paddingLeft="2.0dip" android:paddingRight="2.0dip" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginLeft="2.0dip" android:layout_marginTop="6.0dip" android:layout_marginRight="2.0dip" android:minHeight="10.0dip" app:layout_constraintEnd_toEndOf="@id/flRoot" app:layout_constraintStart_toStartOf="@id/flRoot" app:layout_constraintTop_toTopOf="@id/flRoot">
        <androidx.cardview.widget.CardView android:layout_gravity="center_vertical" android:id="@id/adChoicesViewCard" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginStart="4.0dip" card_view:cardBackgroundColor="@color/transparent" card_view:cardCornerRadius="4.0dip" card_view:cardElevation="0.0dip" xmlns:card_view="http://schemas.android.com/apk/res-auto">
            <com.hisavana.mediation.ad.TAdChoicesView android:id="@id/adChoicesView" android:layout_width="wrap_content" android:layout_height="wrap_content" />
        </androidx.cardview.widget.CardView>
        <com.transsion.wrapperad.view.AdTagView android:layout_gravity="center_vertical" android:id="@id/adIcon" android:layout_width="wrap_content" android:layout_height="16.0dip" android:layout_marginStart="4.0dip" />
        <androidx.constraintlayout.widget.ConstraintLayout android:layout_gravity="center_vertical" android:id="@id/store_mark_container" android:background="@drawable/ad_shape_store_mark_bg" android:layout_width="wrap_content" android:layout_height="16.0dip" android:layout_marginStart="4.0dip">
            <com.hisavana.mediation.ad.TStoreMarkView android:layout_gravity="center" android:id="@id/store_mark_view" android:layout_width="wrap_content" android:layout_height="wrap_content" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.appcompat.widget.LinearLayoutCompat>
    <View android:id="@id/view" android:layout_width="1.0dip" android:layout_height="32.0dip" android:layout_marginTop="8.0dip" android:layout_marginStart="6.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/flRoot" />
    <com.transsion.wrapperad.hi.MaskLayout android:id="@id/maskLayoutIcon" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginTop="6.0dip" android:layout_marginBottom="6.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toEndOf="@id/view" app:layout_constraintTop_toBottomOf="@id/flRoot" app:xhg_mask_drawable="@drawable/ad_shape_dp_4">
        <com.hisavana.mediation.ad.TIconView android:layout_gravity="center_vertical" android:id="@id/native_ad_icon" android:layout_width="32.0dip" android:layout_height="32.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/coverviewMask" />
    </com.transsion.wrapperad.hi.MaskLayout>
    <androidx.appcompat.widget.AppCompatTextView android:textSize="14.0sp" android:textStyle="bold" android:textColor="@color/text_01" android:ellipsize="end" android:id="@id/native_ad_title" android:layout_width="0.0dip" android:layout_height="wrap_content" android:lines="1" android:includeFontPadding="false" android:layout_marginStart="4.0dip" android:layout_marginEnd="12.0dip" app:layout_constrainedWidth="true" app:layout_constraintEnd_toStartOf="@id/call_to_action" app:layout_constraintStart_toEndOf="@id/maskLayoutIcon" app:layout_constraintTop_toTopOf="@id/maskLayoutIcon" app:layout_goneMarginStart="0.0dip" style="@style/style_regular_text" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/brand" android:gravity="center" android:id="@id/call_to_action" android:background="@drawable/ad_shape_btn_11_bg" android:paddingLeft="12.0dip" android:paddingRight="12.0dip" android:visibility="visible" android:layout_width="wrap_content" android:layout_height="32.0dip" android:minWidth="68.0dip" android:includeFontPadding="false" android:textAllCaps="false" android:layout_marginEnd="6.0dip" app:layout_constraintBottom_toBottomOf="@id/maskLayoutIcon" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="@id/maskLayoutIcon" style="@style/style_medium_text" />
    <TextView android:textSize="12.0sp" android:textColor="@color/text_03" android:ellipsize="end" android:id="@id/native_ad_body" android:layout_width="0.0dip" android:layout_height="wrap_content" android:lines="1" android:includeFontPadding="false" android:layout_marginStart="4.0dip" android:layout_marginEnd="12.0dip" app:layout_constraintBottom_toBottomOf="@id/maskLayoutIcon" app:layout_constraintEnd_toStartOf="@id/call_to_action" app:layout_constraintStart_toEndOf="@id/maskLayoutIcon" style="@style/style_regular_text" />
</androidx.constraintlayout.widget.ConstraintLayout>
