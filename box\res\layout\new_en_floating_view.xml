<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:id="@id/clRoot" android:layout_width="fill_parent" android:layout_height="48.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.constraintlayout.widget.ConstraintLayout android:id="@id/clExpansion" android:background="@drawable/libui_audio_floating_bg_2" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginStart="16.0dip" android:layout_marginEnd="16.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent">
        <TextView android:textSize="14.0sp" android:textColor="@color/white" android:ellipsize="end" android:id="@id/tvAudioName" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginTop="8.0dip" android:text="" android:singleLine="true" android:layout_marginStart="16.0dip" app:layout_constraintEnd_toStartOf="@id/iv_history" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
        <TextView android:textSize="12.0sp" android:textColor="@color/base_color_999999" android:id="@id/tvAudioTime" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginBottom="8.0dip" android:text="@string/str_play_time" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toStartOf="@id/iv_history" app:layout_constraintStart_toStartOf="@id/tvAudioName" app:layout_constraintTop_toBottomOf="@id/tvAudioName" />
        <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_remove" android:layout_width="24.0dip" android:layout_height="24.0dip" android:src="@mipmap/ic_audio_remove" android:layout_marginEnd="8.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent" />
        <ProgressBar android:layout_gravity="center" android:id="@id/pbSubject" android:layout_width="24.0dip" android:layout_height="24.0dip" android:max="100" android:progressDrawable="@drawable/circle_progress_bar" android:layout_marginEnd="12.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toStartOf="@id/iv_remove" app:layout_constraintTop_toTopOf="parent" style="?android:progressBarStyleHorizontal" />
        <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_play" android:padding="5.0dip" android:layout_width="34.0dip" android:layout_height="34.0dip" android:src="@mipmap/ic_audio_ctl_play" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="@id/pbSubject" app:layout_constraintStart_toStartOf="@id/pbSubject" app:layout_constraintTop_toTopOf="parent" />
        <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_history" android:layout_width="24.0dip" android:layout_height="24.0dip" android:src="@mipmap/ic_audio_history" android:layout_marginEnd="12.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toStartOf="@id/iv_play" app:layout_constraintTop_toTopOf="parent" />
        <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_next" android:visibility="gone" android:layout_width="24.0dip" android:layout_height="24.0dip" android:src="@mipmap/ic_audio_next_enable" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toStartOf="@id/iv_history" app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
    <androidx.constraintlayout.widget.ConstraintLayout android:id="@id/clPackUp" android:background="@drawable/libui_audio_floating_bg_3" android:paddingTop="16.0dip" android:paddingBottom="16.0dip" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="0.0dip" android:paddingStart="8.0dip" android:paddingEnd="8.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent">
        <androidx.appcompat.widget.AppCompatImageView android:id="@id/ivPackUp" android:layout_width="16.0dip" android:layout_height="16.0dip" android:src="@mipmap/ic_audio_right" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
