<?xml version="1.0" encoding="utf-8"?>
<merge android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <FrameLayout android:id="@id/video_container" android:visibility="visible" android:layout_width="fill_parent" android:layout_height="fill_parent" />
    <com.google.android.material.imageview.ShapeableImageView android:id="@id/video_cover" android:layout_width="wrap_content" android:layout_height="wrap_content" android:scaleType="centerCrop" app:shapeAppearanceOverlay="@style/ImgRoundedStyle_4dp" />
    <androidx.appcompat.widget.AppCompatImageView android:layout_gravity="end|top" android:id="@id/label_gif" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="4.0dip" android:src="@mipmap/label_gif" android:layout_marginEnd="4.0dip" />
</merge>
