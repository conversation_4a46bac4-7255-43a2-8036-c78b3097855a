<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:orientation="vertical" android:padding="16.0dip" android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <LinearLayout android:gravity="center_vertical" android:orientation="horizontal" android:id="@id/ll_header" android:paddingBottom="16.0dip" android:layout_width="fill_parent" android:layout_height="wrap_content">
        <ImageView android:layout_width="16.0dip" android:layout_height="16.0dip" android:src="@mipmap/ic_launcher" />
        <TextView android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/base_app_name" android:layout_marginStart="10.0dip" style="@style/TextAppearance.Compat.Notification" />
    </LinearLayout>
    <LinearLayout android:orientation="horizontal" android:layout_width="fill_parent" android:layout_height="wrap_content">
        <TextView android:ellipsize="end" android:id="@id/tv_name" android:layout_width="0.0dip" android:layout_height="wrap_content" android:text="@string/notification_download_protection" android:maxLines="1" android:layout_weight="1.0" style="@style/TextAppearance.Compat.Notification.Title" />
        <TextView android:id="@id/tv_epse" android:layout_width="wrap_content" android:layout_height="wrap_content" style="@style/TextAppearance.Compat.Notification.Title" />
    </LinearLayout>
</LinearLayout>
