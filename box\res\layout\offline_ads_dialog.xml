<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:orientation="vertical" android:background="@drawable/offline_dialog_background" android:paddingLeft="24.0dip" android:paddingTop="40.0dip" android:paddingRight="24.0dip" android:paddingBottom="40.0dip" android:layout_width="fill_parent" android:layout_height="wrap_content" android:filterTouchesWhenObscured="true"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <ImageView android:layout_gravity="center" android:id="@id/offline_dialog_image" android:layout_width="42.0dip" android:layout_height="42.0dip" android:src="@drawable/offline_dialog_default_icon_42dp" android:scaleType="centerInside" android:contentDescription="@string/offline_dialog_image_description" />
    <LinearLayout android:orientation="vertical" android:paddingTop="14.0dip" android:layout_width="fill_parent" android:layout_height="wrap_content">
        <TextView android:textColor="@android:color/black" android:gravity="center" android:id="@id/offline_dialog_text" android:layout_width="fill_parent" android:layout_height="wrap_content" android:text="@string/offline_dialog_text" />
        <TextView android:textColor="@android:color/darker_gray" android:gravity="center" android:id="@id/offline_dialog_advertiser_name" android:paddingTop="12.0dip" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="wrap_content" android:text="" />
    </LinearLayout>
</LinearLayout>
