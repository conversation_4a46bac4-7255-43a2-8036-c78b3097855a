<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:gravity="center" android:orientation="horizontal" android:focusable="true" android:focusableInTouchMode="true" android:layout_width="fill_parent" android:layout_height="wrap_content" android:textDirection="locale" android:layoutDirection="locale"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <ImageButton android:id="@id/places_autocomplete_search_button" android:background="@null" android:padding="@dimen/places_autocomplete_button_padding" android:layout_width="wrap_content" android:layout_height="wrap_content" android:src="@drawable/quantum_ic_search_grey600_24" android:layout_weight="0.0" android:contentDescription="@string/places_autocomplete_search_hint" />
    <EditText android:textSize="@dimen/places_autocomplete_search_input_text" android:textColor="@color/places_autocomplete_search_text" android:textColorHint="@color/places_autocomplete_search_hint" android:id="@id/places_autocomplete_search_input" android:background="@null" android:paddingLeft="@dimen/places_autocomplete_search_input_padding" android:paddingRight="@dimen/places_autocomplete_search_input_padding" android:focusable="false" android:focusableInTouchMode="false" android:layout_width="0.0dip" android:layout_height="fill_parent" android:hint="@string/places_autocomplete_search_hint" android:maxLines="1" android:lines="1" android:singleLine="true" android:layout_weight="1.0" android:inputType="textNoSuggestions" />
    <ImageButton android:id="@id/places_autocomplete_clear_button" android:background="@null" android:padding="@dimen/places_autocomplete_button_padding" android:layout_width="wrap_content" android:layout_height="wrap_content" android:src="@drawable/quantum_ic_clear_grey600_24" android:layout_weight="0.0" android:contentDescription="@string/places_autocomplete_clear_button" />
</LinearLayout>
