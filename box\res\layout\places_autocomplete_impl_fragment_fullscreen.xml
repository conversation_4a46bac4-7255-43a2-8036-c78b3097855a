<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="wrap_content" android:textDirection="locale" android:layoutDirection="locale"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <LinearLayout android:orientation="horizontal" android:id="@id/places_autocomplete_search_bar_container" android:background="?colorPrimary" android:layout_width="fill_parent" android:layout_height="wrap_content" android:minHeight="?actionBarSize">
        <include layout="@layout/places_autocomplete_impl_search_bar" />
    </LinearLayout>
    <View android:background="@drawable/places_autocomplete_toolbar_shadow" android:layout_width="fill_parent" android:layout_height="4.0dip" />
    <View android:id="@id/places_autocomplete_search_bar_separator" android:layout_width="0.0dip" android:layout_height="0.0dip" />
    <androidx.recyclerview.widget.RecyclerView android:id="@id/places_autocomplete_list" android:layout_width="fill_parent" android:layout_height="0.0dip" android:cacheColorHint="@android:color/white" android:layout_weight="1.0" android:layout_marginStart="@dimen/places_autocomplete_search_bar_margin" />
    <include layout="@layout/places_autocomplete_impl_error" />
    <include layout="@layout/places_autocomplete_impl_powered_by_google" />
</LinearLayout>
