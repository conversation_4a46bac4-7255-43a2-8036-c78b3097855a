<?xml version="1.0" encoding="utf-8"?>
<merge
  xmlns:android="http://schemas.android.com/apk/res/android">
    <View android:id="@id/places_autocomplete_powered_by_google_separator" android:background="@color/places_autocomplete_separator" android:layout_width="fill_parent" android:layout_height="1.0dip" android:layout_marginStart="@dimen/places_autocomplete_search_bar_margin" />
    <FrameLayout android:layout_width="fill_parent" android:layout_height="wrap_content">
        <ProgressBar android:layout_gravity="center_vertical" android:id="@id/places_autocomplete_progress" android:padding="3.0dip" android:layout_width="@dimen/places_autocomplete_progress_size" android:layout_height="@dimen/places_autocomplete_progress_size" android:indeterminate="true" android:layout_marginStart="@dimen/places_autocomplete_progress_horizontal_margin" android:indeterminateTint="@color/places_autocomplete_progress_tint" android:indeterminateTintMode="src_atop" style="@style/Widget.AppCompat.ProgressBar" />
        <ImageView android:id="@id/places_autocomplete_powered_by_google" android:layout_width="wrap_content" android:layout_height="@dimen/places_autocomplete_powered_by_google_height" android:src="@drawable/places_powered_by_google_light" android:scaleType="center" android:contentDescription="@string/places_powered_by_google" android:layout_marginStart="@dimen/places_autocomplete_search_bar_margin" />
    </FrameLayout>
</merge>
