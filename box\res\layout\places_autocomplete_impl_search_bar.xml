<?xml version="1.0" encoding="utf-8"?>
<merge
  xmlns:android="http://schemas.android.com/apk/res/android">
    <ImageButton android:layout_gravity="center_vertical" android:id="@id/places_autocomplete_back_button" android:background="?selectableItemBackgroundBorderless" android:padding="@dimen/places_autocomplete_search_bar_button_padding" android:layout_width="wrap_content" android:layout_height="wrap_content" android:src="@drawable/quantum_ic_arrow_back_grey600_24" android:contentDescription="@string/places_cancel" android:layout_marginEnd="20.0dip" />
    <EditText android:textSize="20.0sp" android:textColor="@color/places_autocomplete_search_text" android:textColorHint="@color/places_autocomplete_search_hint" android:id="@id/places_autocomplete_search_bar" android:background="@android:color/transparent" android:layout_width="0.0dip" android:layout_height="fill_parent" android:hint="@string/places_autocomplete_search_hint" android:maxLines="1" android:lines="1" android:singleLine="true" android:layout_weight="1.0" android:inputType="textNoSuggestions" android:imeOptions="actionSearch|flagNoExtractUi" android:textCursorDrawable="@null" />
    <ImageButton android:layout_gravity="center_vertical" android:id="@id/places_autocomplete_clear_button" android:background="?selectableItemBackgroundBorderless" android:padding="@dimen/places_autocomplete_search_bar_button_padding" android:layout_width="wrap_content" android:layout_height="wrap_content" android:src="@drawable/quantum_ic_clear_grey600_24" android:contentDescription="@string/places_autocomplete_clear_button" />
</merge>
