<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_gravity="bottom" android:background="@drawable/libui_bottom_dialog_bg" android:paddingBottom="18.0dip" android:focusable="true" android:clickable="true" android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <ImageView android:id="@id/iv_close" android:layout_width="40.0dip" android:layout_height="40.0dip" android:src="@drawable/player_ic_close" android:scaleType="center" android:layout_marginEnd="4.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <TextView android:textSize="14.0sp" android:textColor="@color/text_01" android:ellipsize="end" android:id="@id/tv_share_title" android:paddingTop="12.0dip" android:paddingBottom="12.0dip" android:layout_width="fill_parent" android:layout_height="wrap_content" android:text="@string/player_share_friends" android:maxLines="1" android:textAllCaps="false" android:paddingStart="16.0dip" android:paddingEnd="16.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <androidx.recyclerview.widget.RecyclerView android:id="@id/share_list" android:layout_width="fill_parent" android:layout_height="79.0dip" android:layout_marginTop="12.0dip" app:layout_constraintTop_toBottomOf="@id/tv_share_title" />
</androidx.constraintlayout.widget.ConstraintLayout>
