<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:orientation="vertical" android:background="@drawable/button_normal_bg" android:padding="1.0dip" android:layout_width="wrap_content" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <LinearLayout android:orientation="vertical" android:paddingTop="5.0dip" android:paddingBottom="5.0dip" android:layout_width="93.0dip" android:layout_height="wrap_content">
        <TextView android:id="@id/menu_item2" android:text="@string/str_block" style="@style/menu_item_text_style" />
        <TextView android:id="@id/menu_item1" android:text="@string/str_report" style="@style/menu_item_text_style" />
    </LinearLayout>
</LinearLayout>
