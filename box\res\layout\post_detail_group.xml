<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:id="@id/cl_post_group" android:background="@drawable/post_detail_group_bg" android:layout_width="fill_parent" android:layout_height="42.0dip" android:layout_marginBottom="16.0dip" android:layout_marginStart="16.0dip" android:layout_marginEnd="16.0dip" app:layout_constraintHorizontal_bias="0.5" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" xmlns:app="http://schemas.android.com/apk/res-auto"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <com.google.android.material.imageview.ShapeableImageView android:id="@id/ad_post_group_logo" android:layout_width="42.0dip" android:layout_height="42.0dip" android:scaleType="centerCrop" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:shapeAppearanceOverlay="@style/ImgRoundedStyle_4dp" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="14.0sp" android:textColor="@color/cl32_ff_p" android:ellipsize="end" android:id="@id/ad_post_group_name" android:layout_width="fill_parent" android:layout_marginTop="1.0dip" android:maxLines="1" android:includeFontPadding="false" android:layout_marginStart="50.0dip" android:layout_marginEnd="30.0dip" app:layout_constraintBottom_toTopOf="@id/ad_post_group_count" app:layout_constraintLeft_toRightOf="@id/ad_post_group_logo" app:layout_constraintTop_toTopOf="@id/ad_post_group_logo" style="@style/style_regular_text" />
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/base_color_666666" android:id="@id/ad_post_group_count" android:layout_marginBottom="1.0dip" android:includeFontPadding="false" android:layout_marginStart="8.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintLeft_toRightOf="@id/ad_post_group_logo" app:layout_constraintTop_toBottomOf="@id/ad_post_group_name" style="@style/style_tip_text" />
    <androidx.appcompat.widget.AppCompatImageView android:background="@mipmap/ic_rooms_enter_bg" android:layout_width="42.0dip" android:layout_height="38.0dip" android:scaleType="centerCrop" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <androidx.appcompat.widget.AppCompatImageView android:visibility="visible" android:layout_width="wrap_content" android:layout_height="wrap_content" android:src="@mipmap/movie_detail_arrow_right" android:layout_marginStart="8.0dip" android:layout_marginEnd="8.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>
