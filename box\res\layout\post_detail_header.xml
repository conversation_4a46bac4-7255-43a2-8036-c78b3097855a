<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:id="@id/ll_header" android:paddingBottom="16.0dip" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="16.0dip" android:paddingStart="16.0dip" android:paddingEnd="16.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/ad_post_hide_view"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.google.android.material.imageview.ShapeableImageView android:id="@id/ad_header" android:layout_width="44.0dip" android:layout_height="44.0dip" android:scaleType="centerCrop" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:shapeAppearanceOverlay="@style/circle_style" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textColor="@color/cl32" android:ellipsize="end" android:id="@id/ad_title" android:layout_width="0.0dip" android:maxLines="1" android:layout_marginStart="8.0dip" android:layout_marginEnd="16.0dip" app:layout_constraintBottom_toTopOf="@id/ll_subject" app:layout_constraintEnd_toStartOf="@id/ad_post_date" app:layout_constraintStart_toEndOf="@id/ad_header" app:layout_constraintTop_toTopOf="parent" style="@style/style_medium_text" />
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/cl34" android:id="@id/ad_post_date" app:layout_constraintBottom_toBottomOf="@id/ad_title" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="@id/ad_title" style="@style/style_regular_text" />
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/cl34" android:id="@id/tv_tip" android:text="@string/tip_post" android:layout_marginStart="8.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toEndOf="@id/ad_header" app:layout_constraintTop_toBottomOf="@id/ad_title" style="@style/style_regular_text" />
    <androidx.appcompat.widget.LinearLayoutCompat android:gravity="center_vertical" android:id="@id/ll_subject" android:background="@drawable/movie_detail_subject_bg" android:layout_width="0.0dip" android:layout_height="20.0dip" android:layout_marginStart="6.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/tv_tip" app:layout_constraintTop_toBottomOf="@id/ad_title">
        <com.google.android.material.imageview.ShapeableImageView android:id="@id/iv_subject_cover" android:layout_width="18.0dip" android:layout_height="18.0dip" android:src="@mipmap/post_detail_icon_movie" android:layout_marginStart="2.0dip" app:shapeAppearanceOverlay="@style/roundStyle_10" />
        <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/cl11" android:ellipsize="end" android:id="@id/tv_subject" android:visibility="visible" android:layout_width="0.0dip" android:maxLines="1" android:layout_weight="1.0" android:layout_marginStart="2.0dip" style="@style/style_regular_text" />
        <androidx.appcompat.widget.AppCompatImageView android:layout_width="16.0dip" android:layout_height="16.0dip" android:src="@mipmap/ic_subject_arrow" android:layout_marginEnd="4.0dip" />
    </androidx.appcompat.widget.LinearLayoutCompat>
    <androidx.appcompat.widget.LinearLayoutCompat android:gravity="center_vertical" android:id="@id/llResource" android:background="@drawable/bg_radius_5_color_f7f7f7" android:visibility="gone" android:layout_width="0.0dip" android:layout_height="20.0dip" android:paddingStart="8.0dip" android:paddingEnd="8.0dip" android:layout_marginStart="6.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/tv_tip" app:layout_constraintTop_toBottomOf="@id/ad_title">
        <androidx.appcompat.widget.AppCompatImageView android:id="@id/ivSubjectIcon" android:layout_width="12.0dip" android:layout_height="16.0dip" />
        <TextView android:textSize="12.0sp" android:textColor="@color/base_color_333333" android:ellipsize="end" android:gravity="start|center" android:id="@id/tvSubjectName" android:layout_width="0.0dip" android:layout_height="fill_parent" android:text="我是作品的名字" android:maxLines="1" android:layout_weight="1.0" android:layout_marginStart="4.0dip" />
        <androidx.appcompat.widget.AppCompatImageView android:layout_width="wrap_content" android:layout_height="wrap_content" android:src="@mipmap/ic_download_red" android:layout_marginStart="4.0dip" />
        <TextView android:textSize="12.0sp" android:textColor="@color/cl01" android:id="@id/tvSubjectDownload" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/download_movie" android:layout_marginStart="2.0dip" />
    </androidx.appcompat.widget.LinearLayoutCompat>
</androidx.constraintlayout.widget.ConstraintLayout>
