<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <View android:id="@id/op_item_honor_space" android:layout_width="fill_parent" android:layout_height="80.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <View android:id="@id/op_item_honor_gradient_bg" android:layout_width="fill_parent" android:layout_height="wrap_content" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <com.transsion.baseui.widget.OperateScrollableHost android:layout_width="fill_parent" android:layout_height="wrap_content" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/op_item_honor_space">
        <androidx.recyclerview.widget.RecyclerView android:id="@id/op_item_honor_recycler" android:paddingTop="8.0dip" android:layout_width="fill_parent" android:layout_height="fill_parent" />
    </com.transsion.baseui.widget.OperateScrollableHost>
</androidx.constraintlayout.widget.ConstraintLayout>
