<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.tn.lib.widget.TnTextView android:textSize="@dimen/text_size_16" android:textColor="@color/pair_text_191F2B" android:ellipsize="end" android:id="@id/main_operation_movie_rank_title" android:layout_width="wrap_content" android:layout_height="wrap_content" android:maxLines="2" android:paddingStart="@dimen/dimens_12" android:paddingEnd="82.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" style="@style/robot_bold" />
    <com.tn.lib.widget.TnTextView android:textColor="@color/text_02" android:ellipsize="end" android:id="@id/main_operation_movie_more_text" android:maxWidth="68.0dip" android:text="@string/str_more" android:maxLines="1" android:includeFontPadding="false" android:layout_marginEnd="@dimen/dimens_12" app:drawableEndCompat="@mipmap/ic_arrow_right" app:drawableTint="@color/gray_0_60" app:layout_constraintBottom_toBottomOf="@id/main_operation_movie_rank_title" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="@id/main_operation_movie_rank_title" style="@style/style_medium_text" />
    <com.transsion.baseui.widget.OperateScrollableHost android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="@dimen/dp_8" app:layout_constraintTop_toBottomOf="@id/main_operation_movie_rank_title">
        <androidx.recyclerview.widget.RecyclerView android:id="@id/main_operation_movie_rank_recycler" android:layout_width="fill_parent" android:layout_height="wrap_content" />
    </com.transsion.baseui.widget.OperateScrollableHost>
</androidx.constraintlayout.widget.ConstraintLayout>
