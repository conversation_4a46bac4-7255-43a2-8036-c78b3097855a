<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView app:cardCornerRadius="@dimen/dp_6" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/divider" style="@style/style_card_view_home"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.constraintlayout.widget.ConstraintLayout android:id="@id/root" android:paddingLeft="6.0dip" android:paddingTop="6.0dip" android:paddingRight="6.0dip" android:paddingBottom="12.0dip" android:layout_width="fill_parent" android:layout_height="wrap_content">
        <androidx.constraintlayout.widget.ConstraintLayout android:id="@id/cl_cover" android:layout_width="90.0dip" android:layout_height="122.0dip" app:layout_constraintDimensionRatio="3:4" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent">
            <com.google.android.material.imageview.ShapeableImageView android:id="@id/iv_cover" android:layout_width="fill_parent" android:layout_height="fill_parent" android:scaleType="centerCrop" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:shapeAppearanceOverlay="@style/roundStyle_4" />
            <View android:background="@drawable/home_mask_ranking" android:layout_width="fill_parent" android:layout_height="48.0dip" app:layout_constraintBottom_toBottomOf="@id/iv_cover" />
            <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/color_score" android:id="@id/tv_score" android:visibility="visible" android:layout_marginBottom="8.0dip" android:shadowColor="@color/black_30" android:shadowRadius="3.0" android:layout_marginEnd="8.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" style="@style/style_medium_text" />
        </androidx.constraintlayout.widget.ConstraintLayout>
        <com.google.android.material.imageview.ShapeableImageView android:id="@id/iv_stills" android:layout_width="0.0dip" android:layout_height="122.0dip" android:scaleType="centerCrop" android:layout_marginStart="6.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/cl_cover" app:layout_constraintTop_toTopOf="parent" app:shapeAppearanceOverlay="@style/roundStyle_4" />
        <androidx.appcompat.widget.AppCompatImageView android:id="@id/ic_player" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:src="@mipmap/movie_detail_player" app:layout_constraintBottom_toBottomOf="@id/iv_stills" app:layout_constraintEnd_toEndOf="@id/iv_stills" app:layout_constraintStart_toStartOf="@id/iv_stills" app:layout_constraintTop_toTopOf="@id/iv_stills" />
        <include android:id="@id/ll_subject" layout="@layout/post_list_item_subject_tag" />
        <include android:id="@id/post_title_container" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="12.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/ll_subject" layout="@layout/post_title_container" />
        <View android:id="@id/tag_divider" android:background="@color/line_01" android:layout_width="fill_parent" android:layout_height="1.0dip" android:layout_marginTop="12.0dip" app:layout_constraintTop_toBottomOf="@id/post_title_container" />
        <include android:id="@id/tag_list" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="12.0dip" app:layout_constraintTop_toBottomOf="@id/tag_divider" layout="@layout/post_explains_list" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.cardview.widget.CardView>
