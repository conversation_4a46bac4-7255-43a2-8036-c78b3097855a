<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView app:cardCornerRadius="@dimen/dp_6" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/divider" style="@style/style_card_view_home"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.constraintlayout.widget.ConstraintLayout android:id="@id/root" android:paddingLeft="6.0dip" android:paddingTop="6.0dip" android:paddingRight="6.0dip" android:paddingBottom="12.0dip" android:layout_width="fill_parent" android:layout_height="wrap_content">
        <androidx.constraintlayout.widget.ConstraintLayout android:id="@id/cl_cover" android:layout_width="fill_parent" android:layout_height="0.0dip" app:layout_constraintDimensionRatio="2:1" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent">
            <com.google.android.material.imageview.ShapeableImageView android:id="@id/iv_cover" android:layout_width="fill_parent" android:layout_height="fill_parent" android:scaleType="centerCrop" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:shapeAppearanceOverlay="@style/roundStyle_4" />
            <View android:background="@drawable/home_mask_ranking" android:layout_width="fill_parent" android:layout_height="48.0dip" app:layout_constraintBottom_toBottomOf="@id/iv_cover" />
            <androidx.appcompat.widget.AppCompatTextView android:textSize="@dimen/sp_12" android:textColor="@color/white" android:id="@id/tv_duration" android:background="@drawable/bg_post_title_tag" android:paddingLeft="@dimen/dp_4" android:paddingTop="2.0dip" android:paddingRight="@dimen/dp_4" android:paddingBottom="2.0dip" android:visibility="gone" android:layout_marginBottom="9.0dip" android:layout_marginEnd="12.0dip" android:backgroundTint="@color/black_40" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" style="@style/style_medium_text" />
        </androidx.constraintlayout.widget.ConstraintLayout>
        <androidx.appcompat.widget.LinearLayoutCompat android:gravity="center_vertical" android:orientation="horizontal" android:id="@id/ll_subject" android:clipChildren="true" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="12.0dip" app:layout_constraintTop_toBottomOf="@id/cl_cover">
            <androidx.appcompat.widget.LinearLayoutCompat android:orientation="vertical" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_weight="1.0" android:layout_marginEnd="4.0dip">
                <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/text_01" android:ellipsize="end" android:id="@id/tv_subject" android:visibility="visible" android:layout_marginBottom="2.0dip" android:maxLines="1" android:layout_marginEnd="4.0dip" style="@style/style_medium_text" />
                <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/text_02" android:ellipsize="end" android:gravity="center_vertical" android:id="@id/tv_subject_year" android:visibility="visible" android:maxLines="1" android:drawablePadding="4.0dip" style="@style/style_regula_bigger_text" />
            </androidx.appcompat.widget.LinearLayoutCompat>
            <com.transsnet.downloader.widget.DownloadView android:gravity="center_vertical" android:id="@id/ll_download" android:background="@drawable/bg_btn_01_radius_4" android:paddingLeft="12.0dip" android:paddingRight="12.0dip" android:layout_width="wrap_content" android:layout_height="32.0dip" />
        </androidx.appcompat.widget.LinearLayoutCompat>
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.cardview.widget.CardView>
