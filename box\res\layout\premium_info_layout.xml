<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:orientation="horizontal" android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.appcompat.widget.LinearLayoutCompat android:orientation="horizontal" android:layout_width="fill_parent" android:layout_height="wrap_content" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent">
        <androidx.appcompat.widget.AppCompatTextView android:textSize="@dimen/sp_14" android:textColor="@color/white" android:gravity="center" android:layout_width="0.0dip" android:layout_height="wrap_content" android:text="@string/member_no_disturbing_ads" android:drawableTop="@mipmap/ic_premium_ad" android:drawablePadding="4.0dip" android:layout_weight="1.0" />
        <androidx.appcompat.widget.AppCompatTextView android:textSize="@dimen/sp_14" android:textColor="@color/white" android:gravity="center" android:layout_width="0.0dip" android:layout_height="wrap_content" android:text="@string/member_enjoy_720p_quality_videos" android:drawableTop="@mipmap/ic_premium_hd" android:drawablePadding="4.0dip" android:layout_weight="1.0" />
        <androidx.appcompat.widget.AppCompatTextView android:textSize="@dimen/sp_14" android:textColor="@color/white" android:gravity="center" android:layout_width="0.0dip" android:layout_height="wrap_content" android:text="@string/member_multi_downloads_at_once" android:drawableTop="@mipmap/ic_premium_download" android:drawablePadding="4.0dip" android:layout_weight="1.0" />
    </androidx.appcompat.widget.LinearLayoutCompat>
    <androidx.appcompat.widget.LinearLayoutCompat android:orientation="horizontal" android:paddingBottom="16.0dip" android:layout_width="fill_parent" android:layout_height="fill_parent" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent">
        <Space android:layout_width="0.0dip" android:layout_height="fill_parent" android:layout_weight="1.0" />
        <androidx.appcompat.widget.AppCompatImageView android:layout_gravity="center" android:layout_width="wrap_content" android:layout_height="wrap_content" android:src="@mipmap/ic_premium_add" />
        <Space android:layout_width="0.0dip" android:layout_height="fill_parent" android:layout_weight="1.0" />
        <androidx.appcompat.widget.AppCompatImageView android:layout_gravity="center" android:layout_width="wrap_content" android:layout_height="wrap_content" android:src="@mipmap/ic_premium_add" />
        <Space android:layout_width="0.0dip" android:layout_height="fill_parent" android:layout_weight="1.0" />
    </androidx.appcompat.widget.LinearLayoutCompat>
</androidx.constraintlayout.widget.ConstraintLayout>
