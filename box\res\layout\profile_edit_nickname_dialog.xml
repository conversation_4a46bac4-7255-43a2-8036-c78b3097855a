<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:orientation="vertical" android:background="@drawable/buttom_dialog_bg" android:paddingBottom="7.0dip" android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <EditText android:textSize="14.0sp" android:textColor="@color/base_color_333333" android:id="@id/etNickname" android:background="@drawable/prefile_et_edit_bg" android:layout_width="fill_parent" android:layout_height="36.0dip" android:layout_marginTop="12.0dip" android:hint="@string/profile_edit_nickname_hint" android:singleLine="true" android:imeOptions="actionDone" android:paddingStart="12.0dip" android:paddingEnd="12.0dip" android:layout_marginStart="16.0dip" android:layout_marginEnd="16.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <ImageView android:id="@id/ivClear" android:layout_width="wrap_content" android:layout_height="wrap_content" android:src="@mipmap/profile_et_clear_fill" android:layout_marginEnd="12.0dip" app:layout_constraintBottom_toBottomOf="@id/etNickname" app:layout_constraintEnd_toEndOf="@id/etNickname" app:layout_constraintTop_toTopOf="@id/etNickname" />
    <TextView android:textSize="10.0sp" android:textColor="@color/base_color_b2b2b2" android:id="@id/tvCount" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="4.0dip" android:text="0/30" android:layout_marginEnd="16.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toBottomOf="@id/etNickname" />
</androidx.constraintlayout.widget.ConstraintLayout>
