<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:paddingLeft="12.0dip" android:paddingRight="12.0dip" android:paddingBottom="12.0dip" android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.google.android.material.imageview.ShapeableImageView android:id="@id/coverIv" android:background="@color/cl37" android:layout_width="50.0dip" android:layout_height="70.0dip" android:scaleType="centerCrop" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:shapeAppearanceOverlay="@style/CornerStyle_4" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="14.0sp" android:textColor="@color/text_01" android:ellipsize="end" android:id="@id/nameTv" android:layout_width="0.0dip" android:layout_height="wrap_content" android:maxLines="2" android:includeFontPadding="false" android:textAlignment="viewStart" android:layout_marginStart="8.0dip" android:layout_marginEnd="32.0dip" app:layout_constraintBottom_toTopOf="@id/tagTv" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/coverIv" app:layout_constraintTop_toTopOf="@id/coverIv" app:layout_constraintVertical_chainStyle="packed" style="@style/style_medium_text" />
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/white_60" android:ellipsize="end" android:gravity="start" android:id="@id/tagTv" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginTop="8.0dip" android:tint="@color/white_60" android:maxLines="2" android:drawablePadding="4.0dip" android:paddingStart="0.0dip" android:paddingEnd="4.0dip" android:layout_marginEnd="32.0dip" app:layout_constraintBottom_toTopOf="@id/desTv" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintHorizontal_bias="0.0" app:layout_constraintHorizontal_chainStyle="packed" app:layout_constraintStart_toStartOf="@id/nameTv" app:layout_constraintTop_toBottomOf="@id/nameTv" style="@style/style_regular_text" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/white_60" android:ellipsize="end" android:id="@id/desTv" android:visibility="gone" android:layout_width="0.0dip" android:maxLines="1" android:textAlignment="viewStart" android:layout_marginEnd="32.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="@id/nameTv" app:layout_constraintTop_toBottomOf="@id/tagTv" style="@style/style_regular_text" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="11.0sp" android:textColor="@color/text_02" android:gravity="center" android:id="@id/maskTv" android:background="@color/black_60" android:layout_width="0.0dip" android:layout_height="26.0dip" app:layout_constraintBottom_toBottomOf="@id/coverIv" app:layout_constraintEnd_toEndOf="@id/coverIv" app:layout_constraintStart_toStartOf="@id/coverIv" style="@style/robot_bold" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/jumpTv" android:layout_width="24.0dip" android:layout_height="24.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent" app:srcCompat="@mipmap/ic_play_transparent" />
</androidx.constraintlayout.widget.ConstraintLayout>
