<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:id="@id/cl_download" android:background="@drawable/profile_area2_item_bg" android:padding="8.0dip" android:layout_width="108.0dip" android:layout_height="108.0dip" android:layout_marginTop="16.0dip" android:layout_marginBottom="16.0dip" android:layout_marginStart="4.0dip" android:layout_marginEnd="4.0dip" android:elevation="1.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/cl32" android:text="@string/profile_my_offline" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" style="@style/style_regular_text" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="24.0sp" android:textColor="@color/cl32" android:id="@id/tv_downloads" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="26.0dip" android:text="0" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" style="@style/robot_bold" />
    <com.google.android.material.imageview.ShapeableImageView android:id="@id/iv_download1" android:layout_width="28.0dip" android:layout_height="28.0dip" android:scaleType="centerCrop" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" app:shapeAppearanceOverlay="@style/ImgRoundedStyle_4dp" />
    <com.google.android.material.imageview.ShapeableImageView android:id="@id/iv_download2" android:layout_width="28.0dip" android:layout_height="28.0dip" android:scaleType="centerCrop" android:layout_marginStart="4.0dip" android:layout_marginEnd="4.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:shapeAppearanceOverlay="@style/ImgRoundedStyle_4dp" />
    <com.google.android.material.imageview.ShapeableImageView android:id="@id/iv_download3" android:layout_width="28.0dip" android:layout_height="28.0dip" android:scaleType="centerCrop" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:shapeAppearanceOverlay="@style/ImgRoundedStyle_4dp" />
    <com.tn.lib.widget.TnTextView android:textColor="@color/cl01" android:gravity="center" android:id="@id/tv_find_movie" android:background="@drawable/profile_shape_add_room_bg" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="28.0dip" android:text="@string/profile_find_movies" app:layout_constraintBottom_toBottomOf="parent" style="@style/style_regular_text" />
</androidx.constraintlayout.widget.ConstraintLayout>
