<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:background="@drawable/profile_entrance_bg" android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <TextView android:textSize="@dimen/sp_14" android:textStyle="bold" android:textColor="@color/brand_50" android:ellipsize="end" android:id="@id/tvTitle" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginTop="@dimen/dp_12" android:maxLines="1" android:singleLine="true" android:includeFontPadding="false" android:layout_marginStart="@dimen/dp_12" android:layout_marginEnd="@dimen/dp_10" app:layout_constraintEnd_toStartOf="@id/tvJump" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" style="@style/style_extra_import_text" />
    <TextView android:textSize="@dimen/sp_12" android:textColor="@color/text_02" android:ellipsize="end" android:id="@id/tvDes" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginTop="@dimen/dp_4" android:layout_marginBottom="@dimen/dp_12" android:maxLines="2" android:includeFontPadding="false" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="@id/tvTitle" app:layout_constraintStart_toStartOf="@id/tvTitle" app:layout_constraintTop_toBottomOf="@id/tvTitle" style="@style/style_import_text" />
    <TextView android:textSize="@dimen/sp_12" android:textStyle="bold" android:textColor="@color/white_100" android:gravity="center" android:id="@id/tvJump" android:background="@drawable/ad_shape_btn_bg" android:layout_width="64.0dip" android:layout_height="28.0dip" android:text="@string/profile_go" android:layout_marginEnd="@dimen/dp_12" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent" style="@style/style_import_text" />
</androidx.constraintlayout.widget.ConstraintLayout>
