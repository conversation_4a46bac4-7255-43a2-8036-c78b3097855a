<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:layout_gravity="center_horizontal" android:orientation="vertical" android:background="@color/bg_01" android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <com.tn.lib.widget.TnTextView android:textColor="@color/text_03" android:gravity="center" android:layout_gravity="center_horizontal" android:id="@id/tv_empty_tips" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/profile_not_content" style="@style/style_regula_bigger_text" />
</LinearLayout>
