<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:layout_gravity="center_horizontal" android:orientation="vertical" android:background="@color/bg_01" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <com.tn.lib.widget.TnTextView android:textColor="@color/text_09" android:gravity="center" android:layout_gravity="center_horizontal" android:id="@id/tv_empty_tips" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/profile_guest_tips" style="@style/style_regular_text" />
    <com.tn.lib.widget.TnTextView android:textColor="@color/text_01" android:gravity="center" android:layout_gravity="center_horizontal" android:id="@id/tv_login" android:background="@drawable/btn_gray" android:padding="6.0dip" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="16.0dip" android:text="@string/profile_login" style="@style/style_medium_text" />
</LinearLayout>
