<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <TextView android:textSize="16.0sp" android:textColor="@color/base_color_333333" android:gravity="center_vertical" android:id="@id/tvTitle" android:layout_width="fill_parent" android:layout_height="56.0dip" android:text="title" android:paddingStart="16.0dip" android:paddingEnd="16.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <ImageView android:id="@id/ivRight" android:layout_width="wrap_content" android:layout_height="wrap_content" android:src="@mipmap/profile_setting_right" android:layout_marginEnd="16.0dip" app:layout_constraintBottom_toBottomOf="@id/tvTitle" app:layout_constraintEnd_toEndOf="@id/tvTitle" app:layout_constraintTop_toTopOf="@id/tvTitle" />
    <TextView android:textSize="14.0sp" android:textColor="@color/base_color_b2b2b2" android:id="@id/tvSubtitle" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="title2" android:layout_marginEnd="4.0dip" app:layout_constraintBottom_toBottomOf="@id/tvTitle" app:layout_constraintEnd_toStartOf="@id/ivRight" app:layout_constraintTop_toTopOf="@id/tvTitle" />
</androidx.constraintlayout.widget.ConstraintLayout>
