<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="12.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <LinearLayout android:gravity="center" android:orientation="horizontal" android:background="@drawable/bg_radius_4_color_f7f7f7" android:layout_width="fill_parent" android:layout_height="32.0dip" android:backgroundTint="@color/white_10" app:layout_constraintTop_toTopOf="parent">
        <TextView android:textSize="@dimen/text_size_14" android:textColor="@color/white_80" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/more" style="@style/style_medium_text" />
        <ImageView android:layout_width="16.0dip" android:layout_height="16.0dip" android:src="@mipmap/ic_arrow_right" app:tint="@color/white_80" />
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
