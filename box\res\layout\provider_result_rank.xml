<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:id="@id/rvLinkage" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="12.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <View android:id="@id/grad2" android:background="@drawable/bg_association_grad2" android:layout_width="39.0dip" android:layout_height="54.0dip" android:layout_marginStart="28.0dip" app:layout_constraintBottom_toBottomOf="@id/search_result_provider_rank_cover" app:layout_constraintStart_toStartOf="@id/search_result_provider_rank_cover" />
    <View android:id="@id/grad1" android:background="@drawable/bg_association_grad1" android:layout_width="44.0dip" android:layout_height="62.0dip" android:layout_marginStart="14.0dip" app:layout_constraintBottom_toBottomOf="@id/search_result_provider_rank_cover" app:layout_constraintStart_toStartOf="@id/search_result_provider_rank_cover" />
    <com.google.android.material.imageview.ShapeableImageView android:id="@id/search_result_provider_rank_cover" android:layout_width="50.0dip" android:layout_height="70.0dip" android:src="@drawable/ic_list_cover" android:scaleType="centerCrop" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" app:shapeAppearance="@style/roundStyle_4" />
    <LinearLayout android:gravity="center" android:orientation="vertical" android:layout_width="0.0dip" android:layout_height="70.0dip" android:layout_marginStart="8.0dip" android:layout_marginEnd="8.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toStartOf="@id/search_result_provider_rank_arrow" app:layout_constraintStart_toEndOf="@id/grad2" app:layout_constraintTop_toTopOf="parent">
        <TextView android:textSize="16.0sp" android:textColor="@color/white" android:ellipsize="end" android:id="@id/search_result_provider_rank_title" android:layout_width="fill_parent" android:layout_height="wrap_content" android:maxLines="2" app:layout_constraintEnd_toStartOf="@id/search_result_provider_rank_arrow" app:layout_constraintStart_toEndOf="@id/grad2" app:layout_constraintTop_toTopOf="@id/grad2" style="@style/style_medium_text" />
        <TextView android:textSize="@dimen/text_size_12" android:textColor="@color/white_60" android:ellipsize="end" android:id="@id/search_result_provider_rank_subtitle" android:layout_width="fill_parent" android:layout_height="wrap_content" android:lines="1" app:layout_constraintEnd_toEndOf="@id/search_result_provider_rank_title" app:layout_constraintStart_toStartOf="@id/search_result_provider_rank_title" app:layout_constraintTop_toBottomOf="@id/search_result_provider_rank_title" style="@style/style_regular_text" />
    </LinearLayout>
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/search_result_provider_rank_arrow" android:layout_width="24.0dip" android:layout_height="24.0dip" android:src="@drawable/ic_result_arrow" app:layout_constraintBottom_toBottomOf="@id/search_result_provider_rank_cover" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="@id/search_result_provider_rank_cover" />
</androidx.constraintlayout.widget.ConstraintLayout>
