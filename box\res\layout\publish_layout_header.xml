<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout android:background="@color/bg_01" android:layout_width="fill_parent" android:layout_height="54.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <ImageView android:id="@id/iv_back" android:layout_width="wrap_content" android:layout_height="44.0dip" android:src="@mipmap/icon_white_back" android:layout_centerVertical="true" android:layout_marginStart="16.0dip" />
    <androidx.appcompat.widget.AppCompatTextView android:enabled="false" android:textSize="16.0sp" android:textColor="@color/gray_dark_00" android:ellipsize="end" android:gravity="center" android:id="@id/tv_publish" android:background="@drawable/libui_main_btn_selector" android:visibility="visible" android:layout_width="wrap_content" android:layout_height="28.0dip" android:text="@string/film_review_post_ar" android:maxLines="1" android:layout_centerVertical="true" android:paddingStart="15.0dip" android:paddingEnd="15.0dip" android:layout_marginEnd="12.0dip" android:layout_alignParentEnd="true" style="@style/style_import_text" />
    <RelativeLayout android:gravity="center" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_centerVertical="true" android:layout_marginStart="8.0dip" android:layout_marginEnd="4.0dip" android:layout_toStartOf="@id/tv_publish" android:layout_toEndOf="@id/iv_back">
        <com.google.android.material.imageview.ShapeableImageView android:id="@id/iv_title_image" android:visibility="visible" android:layout_width="24.0dip" android:layout_height="24.0dip" android:scaleType="centerCrop" android:layout_centerVertical="true" app:shapeAppearance="@style/corner_style_4" />
        <com.tn.lib.widget.TnTextView android:textSize="18.0sp" android:textColor="@color/white" android:ellipsize="end" android:id="@id/tv_title" android:layout_width="wrap_content" android:layout_height="wrap_content" android:lines="1" android:layout_marginStart="2.0dip" android:layout_toEndOf="@id/iv_title_image" style="@style/style_import_text" />
    </RelativeLayout>
    <View android:background="@color/white_10" android:layout_width="fill_parent" android:layout_height="1.0dip" android:layout_alignParentBottom="true" />
</RelativeLayout>
