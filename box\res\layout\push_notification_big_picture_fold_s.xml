<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:gravity="center_vertical" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <LinearLayout android:gravity="center_vertical" android:orientation="vertical" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_weight="1.0" android:layout_marginEnd="12.0dip">
        <TextView android:id="@id/notification_title_tv" android:layout_width="wrap_content" android:layout_height="wrap_content" style="@style/Push_Notification_Title" />
        <TextView android:id="@id/notification_content_tv" android:layout_width="wrap_content" android:layout_height="wrap_content" style="@style/Push_Notification_Content" />
    </LinearLayout>
    <RelativeLayout android:layout_width="wrap_content" android:layout_height="wrap_content">
        <ImageView android:id="@id/notification_content_image" android:layout_width="48.0dip" android:layout_height="48.0dip" android:scaleType="centerCrop" />
        <TextView android:textSize="10.0sp" android:textColor="@color/white" android:gravity="center" android:id="@id/page_num" android:background="@drawable/bg_push_page_num2" android:paddingLeft="2.0dip" android:paddingTop="2.0dip" android:paddingRight="2.0dip" android:paddingBottom="2.0dip" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_alignBottom="@id/notification_content_image" android:layout_alignEnd="@id/notification_content_image" />
    </RelativeLayout>
</LinearLayout>
