<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout android:id="@id/notification_view" android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <RelativeLayout android:gravity="center_vertical" android:layout_gravity="end" android:orientation="horizontal" android:id="@id/notification_main_column" android:layout_width="fill_parent" android:layout_height="wrap_content">
        <ImageView android:id="@id/notification_content_image" android:layout_width="@dimen/push_notification_transition_rectangle_img_width" android:layout_height="@dimen/push_notification_transition_rectangle_img_height" android:src="@mipmap/notification_placeholder" android:scaleType="centerCrop" android:layout_marginEnd="12.0dip" />
        <ImageView android:id="@id/notification_logo" android:layout_width="16.0dip" android:layout_height="16.0dip" android:src="@mipmap/ic_notification_logo" android:scaleType="centerInside" android:layout_alignBottom="@id/notification_content_image" android:layout_alignEnd="@id/notification_content_image" />
        <RelativeLayout android:gravity="center_vertical" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_centerVertical="true" android:layout_toEndOf="@id/notification_content_image" android:layout_alignParentEnd="true">
            <LinearLayout android:gravity="center_vertical" android:id="@id/ll_title" android:layout_width="wrap_content" android:layout_height="wrap_content">
                <TextView android:id="@id/notification_title_tv" android:layout_width="0.0dip" android:layout_height="wrap_content" android:text="@string/slogan" android:layout_weight="1.0" style="@style/Push_Notification_Title" />
                <TextView android:textSize="12.0sp" android:textColor="@color/color_notification_title" android:id="@id/time_divider" android:layout_width="5.0dip" android:layout_height="15.0dip" android:text="@string/notification_header_divider_symbol" android:layout_marginStart="4.0dip" android:layout_marginEnd="4.0dip" style="@style/Push_Notification_Title" />
                <DateTimeView android:textAppearance="@android:style/TextAppearance.Material.Notification.Time" android:textSize="12.0sp" android:gravity="bottom" android:id="@id/time" android:layout_width="wrap_content" android:layout_height="wrap_content" android:singleLine="true" android:layout_marginEnd="18.0dip" style="@style/Push_Notification_Title" />
            </LinearLayout>
            <LinearLayout android:orientation="horizontal" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="3.0dip" android:layout_below="@id/ll_title" android:layout_marginEnd="18.0dip">
                <TextView android:id="@id/notification_content_tv" android:layout_width="0.0dip" android:layout_height="wrap_content" android:text="@string/start_shooting" android:layout_weight="1.0" style="@style/Push_Notification_Content_2Line" />
                <LinearLayout android:gravity="center" android:layout_gravity="center" android:id="@id/ll_download" android:background="@drawable/bg_btn_01" android:layout_width="wrap_content" android:layout_height="28.0dip" android:paddingStart="8.0dip" android:paddingEnd="8.0dip">
                    <ImageView android:id="@id/iv_icon" android:layout_width="16.0dip" android:layout_height="16.0dip" android:src="@mipmap/ic_download_red" android:scaleType="centerCrop" />
                    <TextView android:textSize="12.0sp" android:textColor="@color/white" android:id="@id/tv_tips" android:text="@string/download_movie" android:layout_marginStart="2.0dip" style="@style/style_medium_text" />
                </LinearLayout>
            </LinearLayout>
        </RelativeLayout>
    </RelativeLayout>
</RelativeLayout>
