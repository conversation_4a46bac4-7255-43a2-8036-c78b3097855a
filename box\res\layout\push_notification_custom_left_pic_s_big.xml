<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:gravity="center_vertical" android:orientation="horizontal" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <ImageView android:id="@id/notification_content_image" android:layout_width="@dimen/push_notification_transition_square_img_width_s" android:layout_height="@dimen/push_notification_transition_square_img_height_s" android:src="@mipmap/notification_placeholder" android:scaleType="centerCrop" android:layout_marginEnd="10.0dip" />
    <RelativeLayout android:gravity="center_vertical" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginTop="-3.0dip" android:layout_weight="1.0">
        <TextView android:id="@id/notification_title_tv" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/slogan" android:layout_marginEnd="12.0dip" style="@style/Push_Notification_Title" />
        <TextView android:id="@id/notification_content_tv" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/start_shooting" android:layout_below="@id/notification_title_tv" style="@style/Push_Notification_Content_2Line" />
    </RelativeLayout>
    <LinearLayout android:gravity="center" android:layout_gravity="center" android:id="@id/ll_download" android:background="@drawable/bg_btn_01" android:layout_width="wrap_content" android:layout_height="28.0dip" android:paddingStart="8.0dip" android:paddingEnd="8.0dip">
        <ImageView android:id="@id/iv_icon" android:layout_width="16.0dip" android:layout_height="16.0dip" android:src="@mipmap/ic_download_red" android:scaleType="centerCrop" />
        <TextView android:textSize="12.0sp" android:textColor="@color/white" android:id="@id/tv_tips" android:text="@string/download_movie" android:layout_marginStart="2.0dip" style="@style/style_medium_text" />
    </LinearLayout>
</LinearLayout>
