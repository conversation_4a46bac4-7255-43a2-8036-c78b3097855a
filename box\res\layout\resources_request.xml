<?xml version="1.0" encoding="utf-8"?>
<FrameLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <LinearLayout android:gravity="center" android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="wrap_content">
        <TextView android:textSize="16.0sp" android:textStyle="bold" android:textColor="@color/text_01" android:gravity="center" android:id="@id/card_title" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginLeft="40.0dip" android:layout_marginTop="16.0dip" android:layout_marginRight="40.0dip" android:layout_marginBottom="8.0dip" android:lineSpacingExtra="4.0dip" android:textAlignment="center" />
        <TextView android:textSize="12.0sp" android:textColor="@color/text_02" android:gravity="center" android:id="@id/card_detail" android:visibility="visible" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginLeft="40.0dip" android:layout_marginRight="40.0dip" android:layout_marginBottom="6.0dip" android:text="@string/resource_request_detail" android:lineSpacingExtra="4.0dip" android:textAlignment="center" />
        <androidx.constraintlayout.widget.ConstraintLayout android:paddingLeft="16.0dip" android:paddingRight="16.0dip" android:layout_width="fill_parent" android:layout_height="wrap_content">
            <com.tn.lib.view.bubbleview.BubbleTextView android:textSize="12.0sp" android:textColor="@color/resources_tip_color" android:gravity="center_horizontal" android:id="@id/card_tip_gray" android:background="@color/transparent" android:paddingLeft="16.0dip" android:paddingTop="12.0dip" android:paddingRight="16.0dip" android:paddingBottom="12.0dip" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginBottom="8.0dip" android:text="@string/resource_request_tip" android:textAlignment="center" app:angle="8.0dip" app:arrowHeight="10.0dip" app:arrowLocation="bottom" app:arrowPosition="170.0dip" app:arrowWidth="16.0dip" app:bubbleColor="@color/resources_tip_bg_color" app:layout_constraintBottom_toTopOf="@id/iv_centerView" app:layout_constraintEnd_toEndOf="@id/iv_centerView" app:layout_constraintStart_toStartOf="@id/iv_centerView" app:layout_constraintTop_toTopOf="parent" />
            <com.tn.lib.view.bubbleview.BubbleTextView android:textSize="12.0sp" android:textColor="@color/text_01" android:gravity="center_horizontal" android:id="@id/card_tip_white" android:background="@color/transparent" android:paddingLeft="16.0dip" android:paddingTop="12.0dip" android:paddingRight="16.0dip" android:paddingBottom="12.0dip" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginBottom="8.0dip" android:text="@string/resource_request_tip" android:textAlignment="center" app:angle="8.0dip" app:arrowHeight="10.0dip" app:arrowLocation="bottom" app:arrowPosition="170.0dip" app:arrowWidth="16.0dip" app:bubbleColor="@color/resources_tip2_bg_color" app:layout_constraintBottom_toTopOf="@id/iv_centerView" app:layout_constraintEnd_toEndOf="@id/iv_centerView" app:layout_constraintStart_toStartOf="@id/iv_centerView" app:layout_constraintTop_toTopOf="parent" />
            <com.transsion.baseui.widget.DrawableCenterTextView android:textSize="14.0sp" android:textStyle="bold" android:textColor="@color/resources_button_color" android:gravity="center_vertical" android:id="@id/iv_centerView" android:background="@drawable/bg_detector_btn_8dp" android:padding="10.0dip" android:layout_width="0.0dip" android:layout_height="40.0dip" android:layout_marginBottom="16.0dip" android:text="@string/give_me_it" android:drawablePadding="8.0dip" android:drawableStart="@mipmap/ic_alert" android:textAlignment="textStart" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintHorizontal_bias="0.5" app:layout_constraintStart_toStartOf="parent" />
            <TextView android:textSize="10.0sp" android:textStyle="bold" android:textColor="@color/gray_light_80" android:id="@id/numText" android:background="@drawable/bg_num_text_6dp_bottom_right" android:paddingLeft="8.0dip" android:paddingTop="2.0dip" android:paddingRight="8.0dip" android:paddingBottom="2.0dip" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginRight="220.0dip" android:layout_marginBottom="-6.0dip" app:layout_constraintBottom_toTopOf="@id/iv_centerView" app:layout_constraintEnd_toEndOf="@id/iv_centerView" app:layout_constraintTop_toTopOf="parent" app:layout_constraintVertical_bias="1.0" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </LinearLayout>
</FrameLayout>
