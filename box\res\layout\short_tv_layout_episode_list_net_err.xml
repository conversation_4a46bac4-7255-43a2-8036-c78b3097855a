<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:paddingLeft="20.0dip" android:paddingRight="20.0dip" android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.appcompat.widget.AppCompatTextView android:textSize="14.0sp" android:textColor="@color/white" android:ellipsize="end" android:gravity="center" android:id="@id/tv_msg" android:background="@drawable/bg_radius_8_color_white_10p" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/base_ui_connect_to_internet" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" style="@style/style_regular_text" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="14.0sp" android:textColor="@color/white" android:ellipsize="end" android:gravity="center" android:id="@id/tv_retry" android:background="@drawable/bg_radius_8_color_white_10p" android:layout_width="144.0dip" android:layout_height="40.0dip" android:layout_marginLeft="4.0dip" android:layout_marginTop="16.0dip" android:layout_marginRight="4.0dip" android:text="@string/retry_text" app:layout_constraintEnd_toStartOf="@id/tv_setting" app:layout_constraintHorizontal_chainStyle="packed" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tv_msg" style="@style/style_regular_text" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="14.0sp" android:textColor="@color/white" android:ellipsize="end" android:gravity="center" android:id="@id/tv_setting" android:background="@drawable/bg_radius_8_color_white_10p" android:layout_width="144.0dip" android:layout_height="40.0dip" android:layout_marginLeft="4.0dip" android:layout_marginTop="16.0dip" android:layout_marginRight="4.0dip" android:text="@string/go_to_setting" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/tv_retry" app:layout_constraintTop_toBottomOf="@id/tv_msg" style="@style/style_regular_text" />
</androidx.constraintlayout.widget.ConstraintLayout>
