<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView android:id="@id/adRoot" android:background="@color/module_01" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginLeft="12.0dip" android:layout_marginRight="12.0dip" app:cardCornerRadius="8.0dip" app:cardElevation="0.0dip" app:layout_constraintStart_toStartOf="parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <FrameLayout android:id="@id/adTrendingTAdNativeView" android:layout_width="fill_parent" android:layout_height="wrap_content" android:maxHeight="437.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/divider" />
</androidx.cardview.widget.CardView>
