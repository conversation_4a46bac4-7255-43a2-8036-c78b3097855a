<?xml version="1.0" encoding="utf-8"?>
<FrameLayout android:layout_gravity="bottom" android:layout_width="fill_parent" android:layout_height="80.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.constraintlayout.widget.ConstraintLayout android:layout_gravity="center|bottom" android:layout_width="fill_parent" android:layout_height="@dimen/tab_bottom_show_height">
        <androidx.appcompat.widget.AppCompatImageView android:id="@id/image_tab_icon" android:layout_width="24.0dip" android:layout_height="24.0dip" android:src="@mipmap/ic_tab_home_active" app:layout_constraintBottom_toTopOf="@id/tv_tab" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" />
        <com.tn.lib.widget.TnTextView android:textSize="@dimen/text_size_10" android:textColor="@color/text_01" android:ellipsize="end" android:gravity="center_horizontal" android:id="@id/tv_tab" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginBottom="8.0dip" android:text="@string/tab_home" android:maxLines="1" android:drawablePadding="0.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" style="@style/style_regular_text" />
        <View android:id="@id/center" android:layout_width="1.0dip" android:layout_height="1.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
        <View android:id="@id/v_download_tips" android:background="@drawable/shape_download_icon_tips" android:visibility="gone" android:layout_width="6.0dip" android:layout_height="6.0dip" android:layout_marginBottom="14.0dip" android:layout_marginStart="8.0dip" app:layout_constraintBottom_toBottomOf="@id/center" app:layout_constraintStart_toEndOf="@id/center" />
        <androidx.appcompat.widget.AppCompatTextView android:textSize="10.0sp" android:textColor="@color/white" android:gravity="center" android:id="@id/tv_download_status" android:background="@drawable/shape_download_icon_status_home" android:paddingLeft="3.0dip" android:paddingRight="3.0dip" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginBottom="10.0dip" android:minWidth="14.0dip" android:layout_marginStart="4.0dip" app:layout_constraintBottom_toBottomOf="@id/center" app:layout_constraintStart_toEndOf="@id/center" />
        <com.noober.background.view.BLTextView android:textSize="8.0sp" android:textColor="@color/white" android:gravity="center" android:id="@id/tv_red_tips" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginBottom="10.0dip" android:paddingStart="2.0dip" android:paddingEnd="2.0dip" android:layout_marginStart="5.0dip" app:bl_corners_bottomLeftRadius="1.0dip" app:bl_corners_bottomRightRadius="3.0dip" app:bl_corners_topLeftRadius="3.0dip" app:bl_corners_topRightRadius="3.0dip" app:bl_solid_color="@color/error_50" app:layout_constraintBottom_toBottomOf="@id/center" app:layout_constraintStart_toEndOf="@id/center" style="@style/style_medium_small_text" />
        <ImageView android:gravity="center" android:id="@id/image_red_tips" android:paddingTop="3.0dip" android:paddingBottom="3.0dip" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:src="@drawable/bg_red_notice" android:layout_marginStart="-3.0dip" app:layout_constraintBaseline_toTopOf="@id/tv_tab" app:layout_constraintLeft_toRightOf="@id/tv_tab" />
        <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_download_fail" android:visibility="gone" android:layout_width="14.0dip" android:layout_height="14.0dip" android:layout_marginBottom="10.0dip" android:layout_marginStart="4.0dip" app:layout_constraintBottom_toBottomOf="@id/center" app:layout_constraintStart_toEndOf="@id/center" app:srcCompat="@mipmap/ic_download_status_fail_home" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</FrameLayout>
