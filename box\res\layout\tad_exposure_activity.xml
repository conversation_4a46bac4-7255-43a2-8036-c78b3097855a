<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:orientation="vertical" android:id="@id/main_content" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <FrameLayout android:id="@id/layout_title" android:background="#ffffffff" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="@dimen/dimens_48">
        <ImageView android:layout_gravity="start|center" android:id="@id/im_back" android:layout_width="@dimen/dimens_24" android:layout_height="@dimen/dimens_24" android:src="@drawable/login_back_vuid" android:scaleType="center" android:layout_marginStart="@dimen/dimens_16" />
        <TextView android:textSize="@dimen/dimens_sp_18" android:textStyle="bold" android:textColor="#ff222222" android:ellipsize="end" android:gravity="center" android:layout_gravity="center" android:id="@id/tv_title" android:layout_width="wrap_content" android:layout_height="fill_parent" android:maxWidth="200.0dip" android:lines="1" />
    </FrameLayout>
    <FrameLayout android:id="@id/layout_content" android:layout_width="fill_parent" android:layout_height="0.0dip" android:layout_weight="1.0">
        <FrameLayout android:id="@id/fl_webview" android:layout_width="fill_parent" android:layout_height="fill_parent" />
        <ProgressBar android:layout_gravity="center" android:id="@id/pb_progress" android:layout_width="wrap_content" android:layout_height="wrap_content" android:progressDrawable="@drawable/ad_pg" />
    </FrameLayout>
    <LinearLayout android:gravity="center" android:orientation="vertical" android:id="@id/layout_no_net" android:background="#ffffffff" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="0.0dip" android:layout_weight="1.0">
        <ImageView android:layout_width="wrap_content" android:layout_height="wrap_content" android:src="@drawable/ssp_no_network" android:scaleType="center" />
        <TextView android:textSize="12.0sp" android:textColor="#ff92969e" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/text_no_network" />
    </LinearLayout>
</LinearLayout>
