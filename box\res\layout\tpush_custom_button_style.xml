<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:orientation="vertical" android:paddingTop="@dimen/tpush_notification_padding_top_p" android:layout_width="fill_parent" android:layout_height="wrap_content" android:paddingStart="@dimen/tpush_notification_padding_start" android:paddingEnd="@dimen/tpush_notification_padding_end"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <LinearLayout android:orientation="horizontal" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layoutDirection="locale">
        <ImageView android:layout_gravity="center_vertical" android:id="@id/tpush_smallIconImg" android:layout_width="@dimen/tpush_notification_small_icon_size" android:layout_height="@dimen/tpush_notification_small_icon_size" android:scaleType="fitXY" />
        <TextView android:layout_gravity="center_vertical" android:id="@id/tpush_smallTitleTv" android:layout_width="wrap_content" android:layout_height="wrap_content" android:includeFontPadding="false" android:lineSpacingExtra="0.0dip" android:textDirection="locale" android:layout_marginStart="8.0dip" style="@style/tpush_notification_small_title" />
        <TextView android:layout_gravity="center_vertical" android:id="@id/tpush_smallTitleSplitTv" android:layout_width="wrap_content" android:layout_height="wrap_content" android:includeFontPadding="false" android:lineSpacingExtra="0.0dip" android:textDirection="locale" style="@style/tpush_notification_small_title" />
        <TextView android:layout_gravity="center_vertical" android:id="@id/tpush_smallTitleTimeTv" android:layout_width="wrap_content" android:layout_height="wrap_content" android:includeFontPadding="false" android:lineSpacingExtra="0.0dip" android:textDirection="locale" style="@style/tpush_notification_small_title" />
    </LinearLayout>
    <RelativeLayout android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="12.0dip" android:layout_marginBottom="16.0dip">
        <ImageView android:id="@id/tpush_largeIconImg" android:layout_width="@dimen/tpush_notification_large_icon_size" android:layout_height="@dimen/tpush_notification_large_icon_size" android:scaleType="fitXY" android:layout_centerVertical="true" android:layout_marginEnd="16.0dip" />
        <TextView android:id="@id/tpush_actionBtn" android:layout_width="wrap_content" android:layout_height="28.0dip" android:layout_centerVertical="true" style="@style/tpush_notification_button" />
        <LinearLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginEnd="12.0dip" android:layout_toStartOf="@id/tpush_actionBtn" android:layout_toEndOf="@id/tpush_largeIconImg">
            <TextView android:id="@id/tpush_titleTv" android:layout_width="wrap_content" android:layout_height="wrap_content" style="@style/tpush_notification_large_title" />
            <TextView android:id="@id/tpush_descriptionTv" android:layout_width="wrap_content" android:layout_height="wrap_content" style="@style/tpush_notification_title" />
        </LinearLayout>
    </RelativeLayout>
</LinearLayout>
