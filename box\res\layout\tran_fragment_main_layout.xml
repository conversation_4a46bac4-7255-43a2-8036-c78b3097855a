<?xml version="1.0" encoding="utf-8"?>
<androidx.appcompat.widget.LinearLayoutCompat android:gravity="center_horizontal" android:orientation="vertical" android:id="@id/main_layout" android:background="@drawable/bg_pay_dialog" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="48.0dip">
        <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textStyle="bold" android:textColor="#ff181f2b" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/pay_online_payment" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
        <View android:background="#ffedf0f5" android:layout_width="fill_parent" android:layout_height="1.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" />
        <FrameLayout android:layout_width="44.0dip" android:layout_height="44.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent">
            <androidx.appcompat.widget.AppCompatImageView android:layout_gravity="center" android:id="@id/iv_close" android:layout_width="20.0dip" android:layout_height="20.0dip" android:src="@mipmap/ic_close" />
        </FrameLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
    <androidx.core.widget.NestedScrollView android:id="@id/scroll_view" android:tag="scrollView" android:persistentDrawingCache="animation" android:layout_width="fill_parent" android:layout_height="0.0dip" android:layout_weight="1.0" app:layout_behavior="@string/appbar_scrolling_view_behavior" app:layout_scrollFlags="scroll">
        <androidx.appcompat.widget.LinearLayoutCompat android:orientation="vertical" android:paddingBottom="0.0dip" android:layout_width="fill_parent" android:layout_height="0.0dip">
            <androidx.appcompat.widget.LinearLayoutCompat android:gravity="center_horizontal" android:layout_gravity="center_horizontal" android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="16.0dip" android:layout_marginBottom="16.0dip">
                <androidx.appcompat.widget.AppCompatTextView android:textSize="14.0sp" android:textColor="@color/payment_text" android:id="@id/iv_company" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="0.0dip" />
                <androidx.appcompat.widget.AppCompatTextView android:textSize="24.0sp" android:textStyle="bold" android:textColor="@color/payment_text" android:id="@id/iv_amount" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="10.0dip" />
                <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/payment_text" android:id="@id/iv_desc" android:layout_width="wrap_content" android:layout_height="wrap_content" android:fontFamily="sans-serif-medium" />
                <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/payment_text" android:id="@id/iv_order_id" android:layout_width="wrap_content" android:layout_height="wrap_content" android:fontFamily="sans-serif-medium" />
            </androidx.appcompat.widget.LinearLayoutCompat>
            <View android:background="#ffedf0f5" android:layout_width="fill_parent" android:layout_height="1.0dip" />
            <androidx.appcompat.widget.AppCompatTextView android:textSize="14.0sp" android:textStyle="bold" android:textColor="@color/payment_text" android:layout_gravity="center" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="16.0dip" android:text="@string/pay_please_choose_a_payment_method" />
            <androidx.constraintlayout.widget.ConstraintLayout android:background="@drawable/bg_payments" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginLeft="12.0dip" android:layout_marginTop="16.0dip" android:layout_marginRight="12.0dip" android:layout_marginBottom="16.0dip">
                <androidx.recyclerview.widget.RecyclerView android:id="@id/iv_recycler" android:background="@drawable/bg_payments" android:layout_width="fill_parent" android:layout_height="wrap_content" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.appcompat.widget.LinearLayoutCompat>
    </androidx.core.widget.NestedScrollView>
</androidx.appcompat.widget.LinearLayoutCompat>
