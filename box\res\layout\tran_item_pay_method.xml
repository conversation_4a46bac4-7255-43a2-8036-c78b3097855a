<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:gravity="center_vertical" android:orientation="horizontal" android:id="@id/item_root" android:paddingLeft="12.0dip" android:paddingRight="12.0dip" android:layout_width="fill_parent" android:layout_height="52.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.appcompat.widget.LinearLayoutCompat android:gravity="center_vertical" android:orientation="horizontal" android:layout_width="fill_parent" android:layout_height="52.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent">
        <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_icon" android:layout_width="44.0dip" android:layout_height="28.0dip" />
        <androidx.appcompat.widget.AppCompatTextView android:textSize="14.0sp" android:textStyle="bold" android:textColor="@color/payment_text" android:id="@id/iv_title" android:layout_width="wrap_content" android:layout_height="wrap_content" android:includeFontPadding="false" android:layout_marginStart="8.0dip" />
        <androidx.appcompat.widget.AppCompatTextView android:textColor="#ffff8f37" android:id="@id/iv_tag_recommend" android:background="@drawable/bg_tag_recommend" android:paddingLeft="6.0dip" android:paddingTop="3.0dip" android:paddingRight="6.0dip" android:paddingBottom="3.0dip" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/pay_recommend" android:includeFontPadding="false" android:fontFamily="sans-serif-medium" android:layout_marginStart="8.0dip" />
        <androidx.appcompat.widget.AppCompatTextView android:textColor="#ff1fbdff" android:id="@id/iv_tag_frequently" android:background="@drawable/bg_tag_frequently_used" android:paddingLeft="6.0dip" android:paddingTop="3.0dip" android:paddingRight="6.0dip" android:paddingBottom="3.0dip" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/pay_frequently_used" android:includeFontPadding="false" android:fontFamily="sans-serif-medium" android:layout_marginStart="8.0dip" />
        <View android:layout_width="0.0dip" android:layout_height="fill_parent" android:layout_weight="1.0" />
        <androidx.appcompat.widget.AppCompatImageView android:layout_width="16.0dip" android:layout_height="16.0dip" android:src="@mipmap/ic_arrow_right" android:layout_marginStart="8.0dip" />
    </androidx.appcompat.widget.LinearLayoutCompat>
    <View android:id="@id/iv_line" android:background="#ffe4e6eb" android:layout_width="fill_parent" android:layout_height="1.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>
