<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:orientation="vertical" android:id="@id/loading" android:background="@color/white" android:layout_width="fill_parent" android:layout_height="fill_parent" android:paddingStart="16.0dip" android:paddingEnd="16.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <View android:id="@id/view1" android:background="@color/cl37" android:layout_width="fill_parent" android:layout_height="10.0dip" android:layout_marginTop="16.0dip" app:layout_constraintTop_toTopOf="parent" />
    <View android:id="@id/view2" android:background="@color/cl37" android:layout_width="fill_parent" android:layout_height="10.0dip" android:layout_marginTop="10.0dip" app:layout_constraintTop_toBottomOf="@id/view1" />
    <View android:id="@id/view3" android:background="@color/cl37" android:layout_width="fill_parent" android:layout_height="10.0dip" android:layout_marginTop="10.0dip" app:layout_constraintTop_toBottomOf="@id/view2" />
    <View android:background="@color/cl37" android:layout_width="220.0dip" android:layout_height="10.0dip" android:layout_marginTop="10.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/view3" />
</androidx.constraintlayout.widget.ConstraintLayout>
