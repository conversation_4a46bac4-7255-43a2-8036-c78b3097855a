<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:background="@drawable/update_score_bg" android:layout_width="@dimen/update_dialog_width" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <TextView android:theme="@style/Update_Title" android:textColor="#ff333333" android:gravity="center" android:id="@id/tv_score_title" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginLeft="24.0dip" android:layout_marginTop="20.0dip" android:layout_marginRight="24.0dip" app:layout_constrainedWidth="true" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <TextView android:theme="@style/Update_Content" android:ellipsize="end" android:gravity="center" android:id="@id/tv_score_content" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginLeft="24.0dip" android:layout_marginTop="8.0dip" android:layout_marginRight="24.0dip" android:maxLines="3" app:layout_constrainedWidth="true" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tv_score_title" app:layout_goneMarginTop="20.0dip" />
    <View android:id="@id/dialog_divider" android:background="@color/update_divider_color" android:visibility="visible" android:layout_width="fill_parent" android:layout_height="1.0px" android:layout_marginTop="16.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tv_score_content" />
    <LinearLayout android:orientation="horizontal" android:id="@id/bottom_layout" android:layout_width="0.0dip" android:layout_height="48.0dip" android:layoutDirection="locale" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/dialog_divider" style="@style/dialogBtnLayoutStyle">
        <Button android:theme="@style/Update_NegativeBtn" android:gravity="center" android:id="@id/btn_negative" android:background="@null" android:layout_width="0.0dip" android:layout_height="fill_parent" android:text="@string/text_update_cancel" android:layout_weight="1.0" android:textAllCaps="false" android:stateListAnimator="@null" />
        <View android:id="@id/updateBottomDivider" android:background="@color/update_divider_color" android:layout_width="1.0px" android:layout_height="fill_parent" />
        <Button android:theme="@style/Update_PositiveBtn" android:gravity="center" android:id="@id/btn_positive" android:background="@null" android:layout_width="0.0dip" android:layout_height="fill_parent" android:text="@string/text_update_button" android:layout_weight="1.0" android:textAllCaps="false" android:stateListAnimator="@null" />
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
