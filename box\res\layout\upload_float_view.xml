<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout android:clickable="true" android:layout_width="fill_parent" android:layout_height="44.0dip" android:layout_marginTop="75.0dip" android:layout_marginStart="8.0dip" android:layout_marginEnd="8.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <RelativeLayout android:id="@id/ufv_view2" android:background="@drawable/bg_post_radius" android:visibility="visible" android:layout_width="fill_parent" android:layout_height="fill_parent" android:paddingStart="16.0dip">
        <ImageView android:id="@id/ufv_iv_retry" android:visibility="visible" android:layout_width="24.0dip" android:layout_height="24.0dip" android:src="@drawable/icon_post_group" android:scaleType="center" android:layout_centerVertical="true" />
        <TextView android:textSize="12.0dip" android:textColor="@color/white" android:id="@id/ufv_tv_status" android:visibility="visible" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/postint_state_posted" android:layout_centerVertical="true" android:layout_marginStart="4.0dip" android:layout_toEndOf="@id/ufv_iv_retry" style="@style/style_regular_text" />
        <TextView android:textSize="12.0dip" android:textColor="@color/white" android:gravity="center" android:layout_gravity="center" android:id="@id/ufv_tv_progress" android:visibility="visible" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="" android:layout_centerVertical="true" android:layout_toEndOf="@id/ufv_tv_status" style="@style/style_regular_text" />
        <TextView android:textSize="12.0dip" android:textColor="@color/white" android:gravity="center" android:layout_gravity="center" android:id="@id/right_state" android:background="@drawable/bg_button_upload_float_common" android:visibility="visible" android:layout_width="wrap_content" android:layout_height="28.0dip" android:text="@string/postint_state_cancel" android:layout_centerVertical="true" android:paddingStart="16.0dip" android:paddingEnd="16.0dip" android:layout_marginEnd="12.0dip" android:layout_alignParentEnd="true" style="@style/style_import_text" />
        <ImageView android:id="@id/ufv_iv_fail" android:focusable="true" android:visibility="gone" android:clickable="true" android:layout_width="25.0dip" android:layout_height="25.0dip" android:src="@drawable/upload_ic_failed" android:scaleType="center" android:layout_marginEnd="21.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintRight_toRightOf="parent" app:layout_constraintTop_toTopOf="parent" />
        <LinearLayout android:orientation="vertical" android:id="@id/ufv_iv_posting" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginStart="10.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintLeft_toLeftOf="parent" app:layout_constraintTop_toTopOf="parent">
            <TextView android:textSize="12.0dip" android:textColor="#ff333333" android:layout_width="fill_parent" android:layout_height="wrap_content" android:text="@string/posting" />
            <TextView android:textSize="12.0dip" android:textColor="#ff333333" android:layout_width="fill_parent" android:layout_height="wrap_content" android:text="@string/please_do_not_exit_the_current_app" />
        </LinearLayout>
    </RelativeLayout>
    <FrameLayout android:id="@id/ufv_view1" android:background="#00000000" android:visibility="gone" android:layout_width="45.0dip" android:layout_height="fill_parent">
        <ImageView android:id="@id/ufv_iv_cover" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="fill_parent" />
        <View android:id="@id/ufv_iv_cover_layer" android:background="@drawable/upload_float_cover_layer" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="fill_parent" />
        <com.tn.lib.view.YuanProgressBar android:textSize="10.0sp" android:textStyle="normal" android:textColor="@color/cl41" android:gravity="center" android:layout_gravity="center" android:id="@id/ufv_bar" android:padding="0.0dip" android:layout_width="28.0dip" android:layout_height="28.0dip" android:lines="1" app:base_circleColor="@color/color_7d7b81" app:base_yuanCircleStrokeWidth="1.0dip" app:base_yuanProgressStrokeWidth="2.0dip" app:base_yuan_progressColor="@color/cl38" />
    </FrameLayout>
</RelativeLayout>
