<?xml version="1.0" encoding="utf-8"?>
<FrameLayout android:layout_width="wrap_content" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <ImageView android:id="@id/sv_item_cover" android:layout_width="fill_parent" android:layout_height="fill_parent" android:scaleType="centerCrop" />
    <RelativeLayout android:layout_gravity="end" android:id="@id/rl_select" android:paddingTop="6.0dip" android:layout_width="36.0dip" android:layout_height="36.0dip" android:paddingEnd="6.0dip">
        <TextView android:textSize="@dimen/text_size_12" android:textColor="@color/update_color_191F2B" android:gravity="center" android:id="@id/tv_select" android:background="@drawable/bg_linear_r16" android:layout_width="20.0dip" android:layout_height="20.0dip" android:includeFontPadding="false" android:layout_alignParentEnd="true" style="@style/robot_medium" />
    </RelativeLayout>
    <View android:id="@id/sv_item_layer" android:background="@color/black_60" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="fill_parent" />
    <View android:layout_gravity="bottom" android:background="@drawable/upload_bottom_layer_4dp" android:layout_width="fill_parent" android:layout_height="24.0dip" />
    <TextView android:textSize="@dimen/text_size_10" android:textColor="@color/white" android:gravity="center" android:layout_gravity="bottom" android:id="@id/sv_item_duration" android:background="@drawable/bg_radius4_black60" android:paddingLeft="4.0dip" android:paddingRight="4.0dip" android:layout_width="wrap_content" android:layout_height="16.0dip" android:layout_margin="4.0dip" style="@style/robot_bold" />
</FrameLayout>
