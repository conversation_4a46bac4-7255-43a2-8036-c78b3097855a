<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:orientation="vertical" android:background="@color/white" android:fitsSystemWindows="true" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="42.0dip">
        <androidx.appcompat.widget.AppCompatImageButton android:id="@id/btn_back" android:background="@null" android:padding="10.0dip" android:layout_width="42.0dip" android:layout_height="fill_parent" android:src="@mipmap/base_back_black" android:scaleType="centerInside" android:layout_marginStart="5.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
        <androidx.appcompat.widget.AppCompatTextView android:textSize="15.0sp" android:textStyle="bold" android:textColor="@color/base_color_333333" android:gravity="center" android:id="@id/tv_title" android:layout_width="wrap_content" android:layout_height="wrap_content" android:maxLines="1" android:singleLine="true" android:layout_marginStart="48.0dip" android:layout_marginEnd="48.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
    <LinearLayout android:orientation="vertical" android:id="@id/rlContent" android:layout_width="fill_parent" android:layout_height="fill_parent" />
</LinearLayout>
