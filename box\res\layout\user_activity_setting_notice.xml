<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.tn.lib.view.TitleLayout android:id="@id/tool_bar" android:background="@color/module_01" android:layout_width="fill_parent" android:layout_height="wrap_content" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <View android:id="@id/v_notice_bg" android:background="@color/module_01" android:layout_width="fill_parent" android:layout_height="56.0dip" app:layout_constraintTop_toBottomOf="@id/tool_bar" />
    <com.tn.lib.widget.TnTextView android:textSize="16.0sp" android:textColor="@color/text_01" android:ellipsize="end" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/show_notification_widget" android:layout_weight="1.0" android:layout_marginStart="16.0dip" app:layout_constraintBottom_toBottomOf="@id/v_notice_bg" app:layout_constraintStart_toStartOf="@id/v_notice_bg" app:layout_constraintTop_toTopOf="@id/v_notice_bg" />
    <com.tn.lib.view.SwitchButton android:id="@id/switch_button" android:layout_width="48.0dip" android:layout_height="28.0dip" android:layout_marginEnd="16.0dip" app:layout_constraintBottom_toBottomOf="@id/v_notice_bg" app:layout_constraintEnd_toEndOf="@id/v_notice_bg" app:layout_constraintTop_toTopOf="@id/v_notice_bg" />
    <com.tn.lib.widget.TnTextView android:textSize="14.0sp" android:textColor="@color/text_01" android:ellipsize="end" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="6.0dip" android:text="@string/show_notification_widget_tips" android:layout_weight="1.0" android:paddingStart="16.0dip" android:paddingEnd="@dimen/dp_16" app:layout_constraintStart_toStartOf="@id/v_notice_bg" app:layout_constraintTop_toBottomOf="@id/v_notice_bg" />
</androidx.constraintlayout.widget.ConstraintLayout>
