<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:background="@drawable/libui_common_dialog_bg" android:paddingTop="28.0dip" android:paddingBottom="20.0dip" android:layout_width="wrap_content" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textColor="@color/text_01" android:gravity="center" android:id="@id/tv_tips" android:layout_width="wrap_content" android:layout_height="40.0dip" android:text="@string/logout_tips" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textColor="@color/text_01" android:gravity="center" android:id="@id/btn_yes" android:background="@drawable/libui_sub_btn2_selector" android:layout_width="116.0dip" android:layout_height="36.0dip" android:layout_marginTop="16.0dip" android:text="@string/yes" android:layout_marginStart="16.0dip" android:layout_marginEnd="4.0dip" app:layout_constraintEnd_toStartOf="@id/btn_no" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tv_tips" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textColor="@color/common_white" android:gravity="center" android:id="@id/btn_no" android:background="@drawable/libui_main_btn_normal" android:layout_width="116.0dip" android:layout_height="36.0dip" android:layout_marginTop="16.0dip" android:text="@string/no" android:layout_marginStart="4.0dip" android:layout_marginEnd="16.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/btn_yes" app:layout_constraintTop_toBottomOf="@id/tv_tips" />
</androidx.constraintlayout.widget.ConstraintLayout>
