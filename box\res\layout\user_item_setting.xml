<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:paddingLeft="@dimen/dp_12" android:paddingRight="@dimen/dp_12" android:layout_width="fill_parent" android:layout_height="52.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <View android:id="@id/divider" android:background="@color/border" android:layout_width="fill_parent" android:layout_height="1.0px" android:layout_marginLeft="@dimen/dp_12" android:layout_marginRight="@dimen/dp_12" app:layout_constraintTop_toTopOf="parent" />
    <View android:id="@id/bgView" android:layout_width="fill_parent" android:layout_height="fill_parent" />
    <com.tn.lib.widget.TnTextView android:textSize="14.0sp" android:textColor="@color/text_01" android:gravity="start|center" android:id="@id/tv_title" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/user_setting_update" android:layout_marginStart="@dimen/dp_12" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_arrow" android:layout_width="15.0dip" android:layout_height="15.0dip" android:src="@drawable/user_setting_arrow" android:layout_marginEnd="@dimen/dp_12" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent" app:tint="@color/gray_60" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/text_03" android:id="@id/tv_content" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginEnd="4.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toStartOf="@id/iv_arrow" app:layout_constraintTop_toTopOf="parent" />
    <com.transsion.baseui.widget.text.GradientTextView android:textSize="12.0sp" android:textColor="@color/white" android:id="@id/tv_tips" android:paddingLeft="4.0dip" android:paddingRight="4.0dip" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toStartOf="@id/iv_arrow" app:layout_constraintTop_toTopOf="parent" style="@style/robot_medium" />
    <ProgressBar android:id="@id/progress_bar" android:visibility="gone" android:layout_width="15.0dip" android:layout_height="15.0dip" android:layout_marginEnd="4.0dip" android:indeterminateTint="@color/text_03" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toStartOf="@id/iv_arrow" app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>
