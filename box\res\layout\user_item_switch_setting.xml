<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:id="@id/llRoot" android:layout_width="fill_parent" android:layout_height="wrap_content" android:minHeight="52.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/rv"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <View android:id="@id/divider" android:background="@color/border" android:layout_width="fill_parent" android:layout_height="1.0px" android:layout_marginLeft="@dimen/dimens_24" android:layout_marginRight="@dimen/dimens_24" app:layout_constraintTop_toTopOf="parent" />
    <View android:id="@id/bgView" android:layout_width="fill_parent" android:layout_height="0.0dip" android:layout_marginLeft="@dimen/dp_12" android:layout_marginRight="@dimen/dp_12" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <com.tn.lib.widget.TnTextView android:textSize="14.0sp" android:textColor="@color/text_01" android:ellipsize="end" android:id="@id/tvTitle" android:layout_width="0.0dip" android:layout_height="wrap_content" android:includeFontPadding="false" android:layout_marginStart="24.0dip" app:layout_constraintBottom_toBottomOf="@id/switchBtn" app:layout_constraintEnd_toStartOf="@id/switchBtn" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="@id/switchBtn" style="@style/robot_medium" />
    <com.tn.lib.view.SwitchButton android:id="@id/switchBtn" android:clickable="false" android:layout_width="40.0dip" android:layout_height="24.0dip" android:layout_marginTop="@dimen/dp_12" android:layout_marginEnd="24.0dip" app:layout_constraintBottom_toTopOf="@id/tvDes" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent" app:layout_goneMarginBottom="16.0dip" />
    <com.tn.lib.widget.TnTextView android:textSize="12.0sp" android:textColor="@color/white_60" android:ellipsize="end" android:id="@id/tvDes" android:paddingLeft="@dimen/dimens_24" android:paddingTop="@dimen/dp_8" android:paddingRight="@dimen/dimens_24" android:paddingBottom="@dimen/dp_12" android:visibility="gone" android:layout_width="0.0dip" android:layout_height="wrap_content" android:includeFontPadding="false" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/switchBtn" style="@style/robot_medium" />
</androidx.constraintlayout.widget.ConstraintLayout>
