<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.appcompat.widget.LinearLayoutCompat android:gravity="center" android:id="@id/ll_like" android:background="@drawable/bg_btn_module_05" android:layout_width="162.0dip" android:layout_height="44.0dip" android:layout_marginTop="12.0dip" android:layout_marginBottom="12.0dip" android:layout_marginStart="12.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent">
        <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/text_02" android:gravity="center" android:id="@id/tv_like" android:layout_width="wrap_content" android:layout_height="fill_parent" android:text="@string/profile_like" android:drawablePadding="1.0dip" android:drawableStart="@drawable/user_message_like_icon" style="@style/style_medium_text" />
    </androidx.appcompat.widget.LinearLayoutCompat>
    <androidx.appcompat.widget.LinearLayoutCompat android:gravity="center" android:id="@id/ll_comment" android:background="@drawable/bg_btn_module_05" android:layout_width="162.0dip" android:layout_height="44.0dip" android:layout_marginTop="12.0dip" android:layout_marginBottom="12.0dip" android:layout_marginEnd="12.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent">
        <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/text_02" android:gravity="center" android:id="@id/tv_comment" android:layout_width="wrap_content" android:layout_height="fill_parent" android:text="@string/name_comments" android:drawablePadding="1.0dip" android:drawableStart="@drawable/user_message_comment_icon" style="@style/style_medium_text" />
    </androidx.appcompat.widget.LinearLayoutCompat>
</androidx.constraintlayout.widget.ConstraintLayout>
