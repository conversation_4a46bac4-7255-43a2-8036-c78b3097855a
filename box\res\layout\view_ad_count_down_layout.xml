<?xml version="1.0" encoding="utf-8"?>
<FrameLayout android:layout_width="wrap_content" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <LinearLayout android:layout_gravity="center" android:orientation="horizontal" android:id="@id/llCountDown" android:background="@drawable/post_view_count_down_12" android:layout_width="wrap_content" android:layout_height="16.0dip">
        <TextView android:textSize="10.0sp" android:textColor="@color/white" android:layout_gravity="center_vertical" android:id="@id/tvCountDown" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginStart="4.0dip" />
        <View android:id="@id/view" android:visibility="visible" android:layout_width="4.0dip" android:layout_height="1.0dip" />
        <TextView android:textSize="10.0sp" android:textColor="@color/white" android:layout_gravity="center_vertical" android:id="@id/tvGetAdFree" android:layout_width="wrap_content" android:layout_height="wrap_content" />
        <ImageView android:layout_gravity="center_vertical" android:id="@id/ivRight" android:layout_width="wrap_content" android:layout_height="wrap_content" android:src="@drawable/view_count_down_back" android:layout_marginEnd="2.0dip" />
    </LinearLayout>
</FrameLayout>
