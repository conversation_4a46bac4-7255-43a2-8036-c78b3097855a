<?xml version="1.0" encoding="utf-8"?>
<merge android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.google.android.material.imageview.ShapeableImageView android:layout_gravity="center_vertical" android:id="@id/ivAdAvatar" android:layout_width="28.0dip" android:layout_height="28.0dip" android:layout_marginTop="8.0dip" android:layout_marginBottom="8.0dip" android:scaleType="centerCrop" android:layout_marginStart="4.0dip" app:shapeAppearanceOverlay="@style/corner_style_8" app:strokeColor="@color/base_color_20_eeeeee" app:strokeWidth="1.0dip" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="11.0sp" android:textColor="@color/white" android:ellipsize="end" android:gravity="start|center" android:id="@id/tvDes" android:layout_width="0.0dip" android:layout_height="fill_parent" android:layout_marginLeft="4.0dip" android:layout_marginTop="8.0dip" android:layout_marginRight="4.0dip" android:layout_marginBottom="8.0dip" android:maxLines="2" android:shadowColor="@color/base_color_80000000" android:shadowRadius="3.0" android:layout_weight="1.0" />
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/white" android:ellipsize="end" android:layout_gravity="center_vertical" android:id="@id/tvBtn" android:background="@drawable/ad_shape_btn_bg" android:paddingLeft="8.0dip" android:paddingTop="5.0dip" android:paddingRight="8.0dip" android:paddingBottom="5.0dip" android:layout_width="78.0dip" android:layout_height="wrap_content" android:lines="1" android:drawablePadding="4.0dip" android:layout_marginEnd="4.0dip" style="@style/style_regular_text" />
</merge>
