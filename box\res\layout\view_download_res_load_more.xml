<?xml version="1.0" encoding="utf-8"?>
<FrameLayout android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/main" android:layout_gravity="center_horizontal" android:id="@id/tv_no_more" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" />
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/main" android:layout_gravity="center_horizontal" android:id="@id/tv_err" android:visibility="visible" android:layout_width="wrap_content" android:layout_height="0.0dip" />
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/main" android:layout_gravity="center_horizontal" android:id="@id/tv_complete" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="0.0dip" />
    <ProgressBar android:layout_gravity="center_horizontal" android:id="@id/load_view" android:layout_width="19.0dip" android:layout_height="19.0dip" android:layout_marginTop="15.0dip" android:indeterminateTint="@color/main" />
</FrameLayout>
