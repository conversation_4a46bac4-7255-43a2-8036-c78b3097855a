<?xml version="1.0" encoding="utf-8"?>
<merge android:clipChildren="false" android:layout_width="24.0dip" android:layout_height="24.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <ImageView android:id="@id/iv_download_icon" android:layout_width="24.0dip" android:layout_height="24.0dip" android:scaleType="centerInside" app:srcCompat="@drawable/ic_download_home_fit_dark" />
    <View android:layout_gravity="end|top" android:id="@id/v_download_tips" android:background="@drawable/shape_download_icon_tips" android:visibility="gone" android:layout_width="6.0dip" android:layout_height="6.0dip" android:layout_marginTop="-3.0dip" android:layout_marginEnd="-3.0dip" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="10.0dip" android:textColor="@color/white" android:gravity="center" android:layout_gravity="end|bottom" android:id="@id/tv_download_status" android:background="@drawable/shape_download_icon_status" android:paddingBottom="1.0px" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:minWidth="14.0dip" android:paddingStart="3.0dip" android:paddingEnd="3.0dip" android:layout_marginEnd="-4.0dip" />
    <androidx.appcompat.widget.AppCompatImageView android:layout_gravity="end|bottom" android:id="@id/iv_download_fail" android:visibility="gone" android:layout_width="14.0dip" android:layout_height="14.0dip" android:layout_marginEnd="-4.0dip" app:srcCompat="@mipmap/ic_download_status_fail" />
</merge>
