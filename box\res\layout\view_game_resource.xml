<?xml version="1.0" encoding="utf-8"?>
<merge android:layout_width="fill_parent" android:layout_height="64.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.google.android.material.imageview.ShapeableImageView android:id="@id/iv_cover" android:layout_width="48.0dip" android:layout_height="48.0dip" android:scaleType="centerCrop" android:layout_marginStart="8.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:shapeAppearance="@style/roundStyle_8" />
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/text_01" android:ellipsize="end" android:id="@id/tv_title" android:layout_width="0.0dip" android:lines="1" android:layout_marginStart="8.0dip" android:layout_marginEnd="8.0dip" app:layout_constraintEnd_toStartOf="@id/tv_ps_btn" app:layout_constraintStart_toEndOf="@id/iv_cover" app:layout_constraintTop_toTopOf="@id/iv_cover" app:layout_constraintVertical_chainStyle="packed" style="@style/style_medium_text" />
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/text_03" android:id="@id/tv_star" android:drawablePadding="2.0dip" app:drawableStartCompat="@mipmap/ic_game_star" app:layout_constraintStart_toStartOf="@id/tv_title" app:layout_constraintTop_toBottomOf="@id/tv_title" style="@style/style_regular_text" />
    <View android:id="@id/v_line" android:background="@color/text_03" android:layout_width="1.0dip" android:layout_height="8.0dip" android:layout_marginTop="1.0dip" android:layout_marginStart="4.0dip" app:layout_constraintBottom_toBottomOf="@id/tv_star" app:layout_constraintStart_toEndOf="@id/tv_star" app:layout_constraintTop_toTopOf="@id/tv_star" />
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/text_03" android:id="@id/tv_size" android:layout_marginStart="4.0dip" app:layout_constraintBottom_toBottomOf="@id/tv_star" app:layout_constraintStart_toEndOf="@id/v_line" app:layout_constraintTop_toTopOf="@id/tv_star" style="@style/style_regular_text" />
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/text_03" android:ellipsize="end" android:id="@id/tv_category" android:layout_width="0.0dip" android:lines="1" android:layout_marginEnd="8.0dip" app:layout_constraintBottom_toBottomOf="@id/iv_cover" app:layout_constraintEnd_toEndOf="@id/tv_title" app:layout_constraintStart_toStartOf="@id/tv_title" app:layout_constraintTop_toBottomOf="@id/tv_star" style="@style/style_regular_text" />
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/white" android:gravity="center" android:id="@id/tv_aha_btn" android:background="@drawable/bg_btn_01" android:layout_width="64.0dip" android:layout_height="32.0dip" android:text="@string/play" android:layout_marginEnd="8.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent" style="@style/style_medium_text" />
    <com.transsion.commercialization.pslink.PsLinkDownLoadButton android:id="@id/tv_ps_btn" android:visibility="invisible" android:layout_width="64.0dip" android:layout_height="32.0dip" android:layout_marginEnd="8.0dip" app:bg_style="main_gradient" app:border_radius="8.0dip" app:border_width="0.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent" app:progress_textSize="14.0sp" />
</merge>
