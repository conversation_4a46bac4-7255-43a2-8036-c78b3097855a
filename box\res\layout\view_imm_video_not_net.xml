<?xml version="1.0" encoding="utf-8"?>
<merge android:background="@mipmap/post_detail_local_video_bg"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/white" android:id="@id/tv_title" android:text="@string/no_network_title" app:layout_constraintBottom_toTopOf="@id/guideline" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:layout_constraintVertical_chainStyle="packed" style="@style/style_medium_text" />
    <android.widget.Space android:id="@id/guideline" android:layout_width="1.0dip" android:layout_height="36.0dip" android:layout_marginTop="16.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tv_title" />
    <com.noober.background.view.BLTextView android:textColor="@color/white" android:gravity="center" android:id="@id/go_to_setting" android:layout_height="36.0dip" android:minWidth="134.0dip" android:text="@string/go_to_setting" android:paddingStart="22.0dip" android:paddingEnd="22.0dip" android:layout_marginEnd="8.0dip" app:bl_corners_radius="8.0dip" app:bl_solid_color="@color/white_10" app:layout_constraintBottom_toBottomOf="@id/guideline" app:layout_constraintEnd_toStartOf="@id/guideline" app:layout_constraintTop_toTopOf="@id/guideline" style="@style/style_medium_text" />
    <com.noober.background.view.BLTextView android:textColor="@color/white" android:gravity="center" android:id="@id/retry" android:layout_height="36.0dip" android:minWidth="134.0dip" android:text="@string/retry_text" android:paddingStart="22.0dip" android:paddingEnd="22.0dip" android:layout_marginStart="8.0dip" app:bl_corners_radius="8.0dip" app:bl_solid_color="@color/white_10" app:layout_constraintBottom_toBottomOf="@id/guideline" app:layout_constraintStart_toEndOf="@id/guideline" app:layout_constraintTop_toTopOf="@id/guideline" style="@style/style_medium_text" />
</merge>
