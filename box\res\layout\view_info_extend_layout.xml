<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.appcompat.widget.AppCompatTextView android:textSize="18.0sp" android:textStyle="bold" android:textColor="@color/text_01" android:id="@id/tvInfoTitle" android:text="@string/movie_info" android:includeFontPadding="false" android:layout_marginStart="12.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" style="@style/style_medium_text" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="14.0sp" android:textColor="@color/gray_40" android:ellipsize="end" android:id="@id/innerTvInfo" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="2.0dip" android:text="@string/movie_detail_upload_by" android:maxLines="1" android:includeFontPadding="false" android:textAlignment="viewStart" app:layout_constrainedWidth="true" app:layout_constraintEnd_toStartOf="@id/innerTvName" app:layout_constraintHorizontal_bias="0.0" app:layout_constraintHorizontal_chainStyle="packed" app:layout_constraintStart_toStartOf="@id/tvInfoTitle" app:layout_constraintTop_toBottomOf="@id/tvInfoTitle" style="@style/style_regular_text" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="14.0sp" android:textColor="@color/gray_40" android:ellipsize="end" android:id="@id/innerTvName" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="2.0dip" android:maxLines="1" android:includeFontPadding="false" android:textAlignment="viewStart" android:layout_marginStart="4.0dip" app:layout_constrainedWidth="true" app:layout_constraintEnd_toStartOf="@id/innerIcon" app:layout_constraintHorizontal_bias="0.0" app:layout_constraintHorizontal_chainStyle="packed" app:layout_constraintStart_toEndOf="@id/innerTvInfo" app:layout_constraintTop_toBottomOf="@id/tvInfoTitle" style="@style/style_medium_text" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/innerIcon" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:src="@mipmap/movie_source_info" android:layout_marginStart="4.0dip" android:layout_marginEnd="12.0dip" app:layout_constraintBottom_toBottomOf="@id/innerTvName" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/innerTvName" app:layout_constraintTop_toTopOf="@id/innerTvName" app:layout_goneMarginEnd="2.0dip" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/text_02" android:id="@id/tvInfoContent" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginTop="4.0dip" android:includeFontPadding="false" android:textAlignment="viewStart" android:layout_marginStart="12.0dip" android:layout_marginEnd="12.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/innerTvInfo" />
</androidx.constraintlayout.widget.ConstraintLayout>
