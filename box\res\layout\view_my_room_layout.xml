<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:id="@id/clDownload" android:background="@drawable/profile_area2_item_bg" android:padding="8.0dip" android:layout_width="104.0dip" android:layout_height="wrap_content" android:layout_marginTop="16.0dip" android:layout_marginBottom="16.0dip" android:layout_marginStart="4.0dip" android:layout_marginEnd="4.0dip" android:elevation="1.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.tn.lib.widget.TnTextView android:textColor="@color/text_01" android:ellipsize="end" android:id="@id/tvTitle" android:layout_width="0.0dip" android:maxLines="1" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" style="@style/style_regular_text" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="24.0sp" android:textColor="@color/text_01" android:id="@id/tvNumber" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="14.0dip" android:text="0" android:includeFontPadding="false" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tvTitle" style="@style/robot_bold" />
    <androidx.constraintlayout.widget.Group android:id="@id/groupPlaceholder" android:visibility="visible" android:layout_width="wrap_content" android:layout_height="wrap_content" app:constraint_referenced_ids="ivPlaceholder1,ivPlaceholder2,ivPlaceholder3" />
    <com.google.android.material.imageview.ShapeableImageView android:id="@id/ivPlaceholder1" android:layout_width="26.0dip" android:layout_height="26.0dip" android:layout_marginTop="10.0dip" android:src="@drawable/profile_shape_dotted_line_img" android:scaleType="centerCrop" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toStartOf="@id/ivPlaceholder2" app:layout_constraintHorizontal_chainStyle="spread_inside" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tvNumber" app:shapeAppearanceOverlay="@style/ImgRoundedStyle_4dp" />
    <com.google.android.material.imageview.ShapeableImageView android:id="@id/ivPlaceholder2" android:layout_width="26.0dip" android:layout_height="26.0dip" android:src="@drawable/profile_shape_dotted_line_img" android:scaleType="centerCrop" app:layout_constraintBottom_toBottomOf="@id/ivPlaceholder1" app:layout_constraintEnd_toStartOf="@id/ivPlaceholder3" app:layout_constraintStart_toEndOf="@id/ivPlaceholder1" app:shapeAppearanceOverlay="@style/ImgRoundedStyle_4dp" />
    <com.google.android.material.imageview.ShapeableImageView android:id="@id/ivPlaceholder3" android:layout_width="26.0dip" android:layout_height="26.0dip" android:src="@drawable/profile_shape_dotted_line_img" android:scaleType="centerCrop" app:layout_constraintBottom_toBottomOf="@id/ivPlaceholder1" app:layout_constraintEnd_toStartOf="@id/ivPlaceholder4" app:layout_constraintStart_toEndOf="@id/ivPlaceholder2" app:shapeAppearanceOverlay="@style/ImgRoundedStyle_4dp" />
    <com.google.android.material.imageview.ShapeableImageView android:id="@id/ivPlaceholder4" android:visibility="gone" android:layout_width="26.0dip" android:layout_height="26.0dip" android:src="@drawable/profile_shape_dotted_line_img" android:scaleType="centerCrop" app:layout_constraintBottom_toBottomOf="@id/ivPlaceholder1" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/ivPlaceholder3" app:shapeAppearanceOverlay="@style/ImgRoundedStyle_4dp" />
    <com.tn.lib.widget.TnTextView android:textColor="@color/brand" android:ellipsize="end" android:gravity="center" android:id="@id/btnFind" android:background="@drawable/profile_shape_add_room_bg" android:visibility="gone" android:layout_width="0.0dip" android:layout_height="26.0dip" android:layout_marginTop="10.0dip" android:maxLines="1" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tvNumber" style="@style/style_regular_text" />
</androidx.constraintlayout.widget.ConstraintLayout>
