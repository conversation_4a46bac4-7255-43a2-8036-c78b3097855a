<?xml version="1.0" encoding="utf-8"?>
<merge
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.tn.lib.view.TitleLayout android:id="@id/ll_title" android:layout_width="fill_parent" android:layout_height="wrap_content" app:layout_constraintTop_toTopOf="parent" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/bg_no_connection" android:layout_width="fill_parent" android:layout_height="wrap_content" android:src="@mipmap/bg_no_network" android:scaleType="fitXY" app:layout_constraintTop_toBottomOf="@id/ll_title" />
    <androidx.appcompat.widget.AppCompatImageView android:layout_width="wrap_content" android:layout_height="wrap_content" android:src="@mipmap/no_wifi_tip_big" app:layout_constraintBottom_toBottomOf="@id/bg_no_connection" app:layout_constraintEnd_toEndOf="@id/bg_no_connection" app:layout_constraintStart_toStartOf="@id/bg_no_connection" app:layout_constraintTop_toTopOf="@id/bg_no_connection" />
    <com.tn.lib.widget.TnTextView android:textSize="24.0sp" android:textColor="@color/text_01" android:id="@id/no_connection_title" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/no_network_title" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/bg_no_connection" style="@style/robot_medium" />
    <androidx.appcompat.widget.LinearLayoutCompat android:orientation="vertical" android:id="@id/ll_no_connection_tip" android:background="@drawable/bg_no_connection_restore_tip" android:paddingLeft="16.0dip" android:paddingTop="16.0dip" android:paddingRight="16.0dip" android:paddingBottom="16.0dip" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginLeft="16.0dip" android:layout_marginTop="10.0dip" android:layout_marginRight="16.0dip" app:layout_constraintTop_toBottomOf="@id/no_connection_title">
        <com.tn.lib.widget.TnTextView android:textColor="@color/text_01" android:gravity="start" android:text="@string/no_connection_restore_title" style="@style/style_medium_text" />
        <com.tn.lib.widget.TnTextView android:textSize="12.0sp" android:textColor="@color/text_01" android:gravity="center_vertical" android:layout_marginTop="8.0dip" android:text="@string/no_connection_restore_content_1" android:drawablePadding="8.0dip" app:drawableStartCompat="@mipmap/ic_no_network_tips_1" style="@style/style_regula_bigger_text" />
        <com.tn.lib.widget.TnTextView android:textSize="12.0sp" android:textColor="@color/text_01" android:gravity="center_vertical" android:layout_marginTop="8.0dip" android:text="@string/no_connection_restore_content_2" android:drawablePadding="8.0dip" app:drawableStartCompat="@mipmap/ic_no_network_tips_2" style="@style/style_regula_bigger_text" />
        <com.tn.lib.widget.TnTextView android:textSize="12.0sp" android:textColor="@color/text_01" android:gravity="center_vertical" android:layout_marginTop="8.0dip" android:text="@string/no_connection_restore_content_3" android:drawablePadding="8.0dip" app:drawableStartCompat="@mipmap/ic_no_network_tips_3" style="@style/style_regula_bigger_text" />
    </androidx.appcompat.widget.LinearLayoutCompat>
    <com.tn.lib.widget.TnTextView android:id="@id/retry" android:layout_width="240.0dip" android:layout_height="40.0dip" android:layout_marginTop="32.0dip" android:text="@string/home_retry_text" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/ll_no_connection_tip" style="@style/style_main_btn_h42" />
    <com.tn.lib.widget.TnTextView android:textSize="@dimen/text_size_16" android:id="@id/go_to_setting" android:layout_width="240.0dip" android:layout_height="40.0dip" android:layout_marginTop="16.0dip" android:text="@string/go_to_setting" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/retry" style="@style/style_sub_btn3" />
</merge>
