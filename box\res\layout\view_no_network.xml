<?xml version="1.0" encoding="utf-8"?>
<merge android:background="@mipmap/bg_network_view" android:paddingBottom="20.0dip" android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/white" android:gravity="center_vertical" android:id="@id/tv_title" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="18.0dip" android:text="@string/no_network_title" android:drawablePadding="4.0dip" app:drawableStartCompat="@mipmap/no_wifi_tip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" style="@style/style_title_text" />
    <Space android:id="@id/space" android:layout_width="wrap_content" android:layout_height="wrap_content" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" />
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/white" android:id="@id/go_to_setting" android:background="@drawable/bg_go_to_setting" android:layout_width="120.0dip" android:layout_height="36.0dip" android:layout_marginTop="16.0dip" android:text="@string/go_to_setting" android:layout_marginEnd="8.0dip" app:layout_constraintEnd_toEndOf="@id/space" app:layout_constraintTop_toBottomOf="@id/tv_title" style="@style/style_main_btn_h36" />
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/common_white" android:gravity="center" android:id="@id/retry" android:background="@drawable/libui_main_btn_normal" android:layout_width="120.0dip" android:layout_height="36.0dip" android:layout_marginTop="16.0dip" android:text="@string/home_retry_text" android:layout_marginStart="8.0dip" app:layout_constraintStart_toStartOf="@id/space" app:layout_constraintTop_toBottomOf="@id/tv_title" style="@style/style_regula_bigger_text" />
    <ProgressBar android:layout_gravity="center" android:id="@id/progress_bar" android:visibility="gone" android:layout_width="24.0dip" android:layout_height="24.0dip" android:indeterminateTint="@color/common_white" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
</merge>
