<?xml version="1.0" encoding="utf-8"?>
<merge android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <androidx.appcompat.widget.LinearLayoutCompat android:gravity="center_vertical" android:id="@id/play_list_title" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginLeft="4.0dip" android:layout_marginRight="4.0dip">
        <androidx.appcompat.widget.LinearLayoutCompat android:gravity="center_vertical" android:id="@id/tag_group" android:background="@drawable/bg_tag_group" android:paddingLeft="6.0dip" android:paddingRight="6.0dip" android:layout_width="wrap_content" android:layout_height="20.0dip">
            <androidx.appcompat.widget.AppCompatImageView android:id="@id/tag_icon" android:layout_width="12.0dip" android:layout_height="12.0dip" />
            <com.tn.lib.widget.TnTextView android:textSize="12.0sp" android:textColor="@color/white" android:id="@id/tag_name" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginStart="1.0dip" style="@style/robot_medium" />
        </androidx.appcompat.widget.LinearLayoutCompat>
        <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_tag" android:visibility="gone" android:layout_width="40.0dip" android:layout_height="20.0dip" />
        <com.tn.lib.widget.TnTextView android:textSize="18.0sp" android:textColor="@color/text_01" android:ellipsize="end" android:id="@id/tv_title" android:layout_width="wrap_content" android:layout_height="wrap_content" android:maxLines="1" android:layout_weight="1.0" android:layout_marginStart="4.0dip" android:layout_marginEnd="8.0dip" style="@style/robot_bold" />
        <ImageView android:layout_width="wrap_content" android:layout_height="wrap_content" android:src="@mipmap/ic_arrow_right" android:tint="@color/gray_0_60" />
    </androidx.appcompat.widget.LinearLayoutCompat>
    <com.transsion.banner.view.HRecyclerView android:id="@id/rv_list" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="8.0dip" />
    <com.tn.lib.widget.TnTextView android:textSize="14.0sp" android:textColor="@color/text_01" android:ellipsize="end" android:gravity="center" android:id="@id/check_list" android:background="@drawable/libui_sub_btn4_bg" android:paddingLeft="12.0dip" android:paddingRight="12.0dip" android:layout_width="fill_parent" android:layout_height="40.0dip" android:layout_marginLeft="4.0dip" android:layout_marginTop="8.0dip" android:layout_marginRight="4.0dip" android:maxLines="1" style="@style/robot_medium" />
</merge>
