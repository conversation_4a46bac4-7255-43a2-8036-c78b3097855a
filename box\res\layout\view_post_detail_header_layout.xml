<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:id="@id/llHeaderRootView" android:paddingBottom="16.0dip" android:layout_width="fill_parent" android:layout_height="wrap_content" android:paddingStart="16.0dip" android:paddingEnd="16.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/ad_post_hide_view"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.google.android.material.imageview.ShapeableImageView android:id="@id/ivAvatar" android:layout_width="44.0dip" android:layout_height="44.0dip" android:layout_marginTop="16.0dip" android:scaleType="centerCrop" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:shapeAppearanceOverlay="@style/circle_style" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textColor="@color/text_01" android:ellipsize="end" android:id="@id/tvName" android:layout_width="0.0dip" android:maxLines="1" android:layout_marginStart="8.0dip" android:layout_marginEnd="16.0dip" app:layout_constraintBottom_toTopOf="@id/llSubject" app:layout_constraintEnd_toStartOf="@id/tvPostTime" app:layout_constraintStart_toEndOf="@id/ivAvatar" app:layout_constraintTop_toTopOf="@id/ivAvatar" style="@style/style_medium_text" />
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/text_03" android:id="@id/tvPostTime" app:layout_constraintBottom_toBottomOf="@id/tvName" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="@id/tvName" style="@style/style_regular_text" />
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/text_03" android:id="@id/tvTip" android:text="@string/post_at" app:layout_constraintBottom_toBottomOf="@id/ivAvatar" app:layout_constraintStart_toStartOf="@id/tvName" style="@style/style_regular_text" />
    <androidx.constraintlayout.widget.ConstraintLayout android:gravity="center_vertical" android:id="@id/llSubject" android:layout_width="0.0dip" android:layout_height="0.0dip" android:layout_marginStart="6.0dip" app:layout_constraintBottom_toBottomOf="@id/tvTip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/tvTip" app:layout_constraintTop_toTopOf="@id/tvTip">
        <View android:background="@drawable/movie_detail_room_bg" android:layout_width="0.0dip" android:layout_height="fill_parent" app:layout_constraintBottom_toBottomOf="@id/tvSubject" app:layout_constraintEnd_toEndOf="@id/tvSubject" app:layout_constraintStart_toStartOf="@id/tvSubject" app:layout_constraintTop_toTopOf="@id/tvSubject" />
        <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/cl12" android:ellipsize="end" android:id="@id/tvSubject" android:layout_width="wrap_content" android:layout_height="fill_parent" android:maxLines="1" app:drawableEndCompat="@mipmap/ic_group_arrow" app:drawableStartCompat="@mipmap/post_ic_group" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" style="@style/style_regular_text" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
