<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:gravity="center_vertical" android:background="@color/bg_01" android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <View android:id="@id/viewLike" android:layout_width="0.0dip" android:layout_height="44.0dip" app:layout_constraintEnd_toStartOf="@id/viewComment" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/ivLike" android:layout_width="wrap_content" android:layout_height="0.0dip" app:layout_constraintBottom_toBottomOf="@id/viewLike" app:layout_constraintStart_toStartOf="@id/viewLike" app:layout_constraintTop_toTopOf="@id/viewLike" app:srcCompat="@drawable/ic_movie_like" />
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/text_01" android:id="@id/tvLike" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginStart="4.0dip" app:layout_constraintBottom_toBottomOf="@id/viewLike" app:layout_constraintStart_toEndOf="@id/ivLike" app:layout_constraintTop_toTopOf="@id/viewLike" style="@style/style_medium_text" />
    <View android:id="@id/viewComment" android:layout_width="0.0dip" android:layout_height="0.0dip" app:layout_constraintBottom_toBottomOf="@id/viewLike" app:layout_constraintEnd_toStartOf="@id/viewShare" app:layout_constraintStart_toEndOf="@id/viewLike" app:layout_constraintTop_toTopOf="@id/viewLike" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/ivComment" android:layout_width="wrap_content" android:layout_height="0.0dip" app:layout_constraintBottom_toBottomOf="@id/viewComment" app:layout_constraintEnd_toStartOf="@id/tvComment" app:layout_constraintHorizontal_chainStyle="packed" app:layout_constraintStart_toStartOf="@id/viewComment" app:layout_constraintTop_toTopOf="@id/viewComment" app:srcCompat="@drawable/ic_movie_comment" />
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/text_01" android:id="@id/tvComment" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginStart="4.0dip" app:layout_constraintBottom_toBottomOf="@id/viewComment" app:layout_constraintEnd_toEndOf="@id/viewComment" app:layout_constraintStart_toEndOf="@id/ivComment" app:layout_constraintTop_toTopOf="@id/viewComment" style="@style/style_medium_text" />
    <View android:id="@id/viewShare" android:layout_width="0.0dip" android:layout_height="0.0dip" app:layout_constraintBottom_toBottomOf="@id/viewLike" app:layout_constraintEnd_toStartOf="@id/viewDownload" app:layout_constraintStart_toEndOf="@id/viewComment" app:layout_constraintTop_toTopOf="@id/viewLike" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/ivShare" android:layout_width="wrap_content" android:layout_height="0.0dip" app:layout_constraintBottom_toBottomOf="@id/viewShare" app:layout_constraintEnd_toEndOf="@id/viewShare" app:layout_constraintTop_toTopOf="@id/viewShare" app:srcCompat="@drawable/ic_movie_share" />
    <View android:id="@id/viewDownload" android:visibility="gone" android:layout_width="0.0dip" android:layout_height="0.0dip" app:layout_constraintBottom_toBottomOf="@id/viewLike" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/viewShare" app:layout_constraintTop_toTopOf="@id/viewLike" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/ivDownload" android:layout_width="wrap_content" android:layout_height="0.0dip" app:layout_constraintBottom_toBottomOf="@id/viewDownload" app:layout_constraintEnd_toEndOf="@id/viewDownload" app:layout_constraintStart_toStartOf="@id/viewDownload" app:layout_constraintTop_toTopOf="@id/viewDownload" app:srcCompat="@drawable/ic_movie_download" />
</androidx.constraintlayout.widget.ConstraintLayout>
