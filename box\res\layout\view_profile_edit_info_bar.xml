<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout android:id="@id/rlLayout" android:layout_width="fill_parent" android:layout_height="56.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <TextView android:textSize="14.0sp" android:textColor="@color/text_01" android:id="@id/tv_profileleft" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="llll" android:layout_centerVertical="true" android:layout_marginStart="16.0dip" android:layout_alignParentStart="true" />
    <TextView android:textSize="14.0sp" android:textColor="@color/text_03" android:id="@id/tv_profileright" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/profile_empty_select" android:layout_centerVertical="true" android:layout_marginEnd="4.0dip" android:layout_toStartOf="@id/iv_profile_more" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_profile_more" android:layout_width="16.0dip" android:layout_height="16.0dip" android:src="@drawable/ic_more_edit" android:layout_centerVertical="true" android:layout_marginEnd="16.0dip" android:layout_alignParentEnd="true" app:tint="@color/text_01" />
    <View android:id="@id/view_line" android:background="@color/line_01" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="1.0dip" android:layout_alignParentBottom="true" />
</RelativeLayout>
