<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:id="@id/clSubjectRoot" android:background="@drawable/bg_select_publish_subject" android:layout_width="fill_parent" android:layout_height="40.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.google.android.material.imageview.ShapeableImageView android:id="@id/ivGaussianBlur" android:layout_width="32.0dip" android:layout_height="32.0dip" android:layout_marginTop="4.0dip" android:layout_marginBottom="4.0dip" android:layout_marginStart="4.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:shapeAppearance="@style/corner_style_4" />
    <com.google.android.material.imageview.ShapeableImageView android:id="@id/ivCover" android:layout_width="0.0dip" android:layout_height="0.0dip" android:scaleType="centerCrop" app:layout_constraintBottom_toBottomOf="@id/ivGaussianBlur" app:layout_constraintEnd_toEndOf="@id/ivGaussianBlur" app:layout_constraintStart_toStartOf="@id/ivGaussianBlur" app:layout_constraintTop_toTopOf="@id/ivGaussianBlur" app:shapeAppearance="@style/corner_style_4" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="14.0sp" android:textColor="@color/text_02" android:ellipsize="end" android:id="@id/tvSubjectTitle" android:layout_width="0.0dip" android:layout_height="wrap_content" android:maxLines="1" android:textAlignment="viewStart" android:layout_marginStart="8.0dip" android:layout_marginEnd="12.0dip" app:layout_constraintBottom_toTopOf="@id/tvSubjectYear" app:layout_constraintEnd_toStartOf="@id/llDownload" app:layout_constraintStart_toEndOf="@id/ivCover" app:layout_constraintTop_toTopOf="@id/ivCover" style="@style/style_import_text" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/text_03" android:ellipsize="end" android:gravity="start|center" android:id="@id/tvSubjectYear" android:layout_width="0.0dip" android:layout_height="wrap_content" android:maxLines="1" android:drawablePadding="4.0dip" android:textAlignment="viewStart" app:drawableStartCompat="@drawable/ic_tag_movie" app:layout_constraintBottom_toBottomOf="@id/ivGaussianBlur" app:layout_constraintEnd_toEndOf="@id/tvSubjectTitle" app:layout_constraintStart_toStartOf="@id/tvSubjectTitle" app:layout_constraintTop_toBottomOf="@id/tvSubjectTitle" />
    <com.transsnet.downloader.widget.DownloadView android:id="@id/llDownload" android:background="@drawable/bg_btn_01" android:visibility="gone" android:layout_width="88.0dip" android:layout_height="32.0dip" android:layout_marginEnd="12.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>
