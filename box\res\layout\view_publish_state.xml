<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout android:id="@id/rl_layout" android:layout_width="wrap_content" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:cp="http://schemas.android.com/apk/res-auto">
    <ImageView android:id="@id/iv_btn" android:layout_width="24.0dip" android:layout_height="24.0dip" android:src="@drawable/ic_select_number_bro_bg" />
    <com.transsion.publish.view.CircleProgressBar android:id="@id/circles_bar" android:visibility="visible" android:layout_width="24.0dip" android:layout_height="24.0dip" cp:ra="8.0dip" cp:ringsColor="@color/cl01ss" cp:strokesWidth="1.5dip" cp:textsColor="#ff010219" />
    <TextView android:textSize="@dimen/dp_16" android:textColor="@color/text_01" android:id="@id/tvContent" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/film_review_post" android:paddingStart="@dimen/dp_4" android:paddingEnd="0.0dip" android:layout_toEndOf="@id/iv_btn" style="@style/robot_medium" />
</RelativeLayout>
