<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:gravity="center_horizontal" android:orientation="vertical" android:id="@id/ll_empty" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_tips" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="128.0dip" android:src="@mipmap/ic_no_content" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="13.0sp" android:textColor="@color/base_color_999999" android:ellipsize="end" android:gravity="center_horizontal" android:layout_gravity="center_horizontal" android:id="@id/tv_empty_tips" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/user_follower_empty" android:maxLines="2" android:layout_marginStart="16.0dip" android:layout_marginEnd="16.0dip" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="@dimen/sp_14" android:textColor="@color/text_01" android:gravity="center" android:id="@id/tv_retry" android:background="@drawable/btn_gray" android:visibility="gone" android:layout_width="96.0dip" android:layout_height="34.0dip" android:layout_marginTop="@dimen/dp_20" android:text="@string/home_retry_text" />
</LinearLayout>
