<?xml version="1.0" encoding="utf-8"?>
<merge android:paddingTop="12.0dip" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.appcompat.widget.AppCompatTextView android:textSize="14.0sp" android:textColor="@color/text_01" android:ellipsize="end" android:gravity="start" android:id="@id/tv_title" android:layout_width="0.0dip" android:layout_height="wrap_content" android:text="@string/Related_Community" android:maxLines="1" android:textAlignment="viewStart" android:layout_marginStart="12.0dip" android:layout_marginEnd="8.0dip" app:layout_constraintEnd_toStartOf="@id/tv_more" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" style="@style/style_import_text" />
    <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/text_07" android:id="@id/tv_more" android:paddingTop="8.0dip" android:paddingBottom="8.0dip" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/all" android:drawablePadding="2.0dip" android:layout_marginEnd="12.0dip" app:drawableEndCompat="@mipmap/libui_ic_more" app:layout_constraintBottom_toBottomOf="@id/tv_title" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="@id/tv_title" style="@style/style_regula_bigger_text" />
    <androidx.recyclerview.widget.RecyclerView android:id="@id/recyclerView" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginLeft="8.0dip" android:layout_marginTop="8.0dip" android:layout_marginRight="8.0dip" app:layout_constraintTop_toBottomOf="@id/tv_title" />
    <View android:background="@color/line_01" android:layout_width="fill_parent" android:layout_height="1.0dip" android:layout_marginLeft="12.0dip" android:layout_marginTop="12.0dip" android:layout_marginRight="12.0dip" app:layout_constraintTop_toBottomOf="@id/recyclerView" />
</merge>
