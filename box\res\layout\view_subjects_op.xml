<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginLeft="16.0dip" android:layout_marginTop="16.0dip" android:layout_marginRight="16.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.google.android.material.imageview.ShapeableImageView android:id="@id/iv_cover" android:background="@color/cl37" android:layout_width="54.0dip" android:layout_height="72.0dip" android:scaleType="centerCrop" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:shapeAppearanceOverlay="@style/roundStyle_4" />
    <View android:background="@drawable/libui_mask_cl45_50p_to_0p" android:layout_width="54.0dip" android:layout_height="26.0dip" app:layout_constraintBottom_toBottomOf="@id/iv_cover" app:layout_constraintStart_toStartOf="parent" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="10.0sp" android:textColor="@color/white" android:id="@id/tv_score" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginBottom="4.0dip" android:shadowColor="@color/black_30" android:shadowRadius="3.0" android:layout_marginEnd="4.0dip" app:layout_constraintBottom_toBottomOf="@id/iv_cover" app:layout_constraintEnd_toEndOf="@id/iv_cover" style="@style/style_medium_text" />
    <androidx.appcompat.widget.LinearLayoutCompat android:orientation="vertical" android:id="@id/ll_content" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginStart="8.0dip" android:layout_marginEnd="8.0dip" app:layout_constraintBottom_toBottomOf="@id/iv_cover" app:layout_constraintEnd_toStartOf="@id/ll_download" app:layout_constraintStart_toEndOf="@id/iv_cover" app:layout_constraintTop_toTopOf="@id/iv_cover">
        <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/common_white" android:ellipsize="end" android:id="@id/tv_title" android:layout_width="wrap_content" android:layout_height="wrap_content" android:maxLines="2" style="@style/style_import_text" />
        <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/white_40" android:ellipsize="end" android:id="@id/tv_time" android:visibility="visible" android:layout_marginTop="2.0dip" android:lines="1" android:drawablePadding="4.0dip" android:drawableStart="@drawable/ic_tag_movie" style="@style/style_regular_text" />
        <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/white_40" android:ellipsize="end" android:id="@id/tv_download_size" android:visibility="gone" android:layout_marginTop="2.0dip" android:lines="1" android:drawablePadding="4.0dip" android:drawableStart="@mipmap/ic_download" style="@style/style_regular_text" />
    </androidx.appcompat.widget.LinearLayoutCompat>
    <com.transsnet.downloader.widget.DownloadView android:gravity="center_vertical" android:id="@id/ll_download" android:background="@drawable/download_bg_white" android:paddingLeft="12.0dip" android:paddingRight="12.0dip" android:layout_width="wrap_content" android:layout_height="32.0dip" android:layout_marginTop="2.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent" app:tips_textColor="@color/white" app:tips_textSize="12.0sp" />
</androidx.constraintlayout.widget.ConstraintLayout>
