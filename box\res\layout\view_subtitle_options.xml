<?xml version="1.0" encoding="utf-8"?>
<merge android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <View android:id="@id/v_bg_start" android:layout_width="0.0dip" android:layout_height="fill_parent" android:layout_weight="1.0" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toStartOf="@id/v_bg_end" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <View android:id="@id/v_bg_end" android:background="@color/subtitle_options_bg_color" android:layout_width="256.0dip" android:layout_height="fill_parent" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <androidx.core.widget.NestedScrollView android:paddingTop="8.0dip" android:layout_width="0.0dip" android:layout_height="0.0dip" app:layout_constraintBottom_toBottomOf="@id/v_bg_end" app:layout_constraintEnd_toEndOf="@id/v_bg_end" app:layout_constraintStart_toStartOf="@id/v_bg_end" app:layout_constraintTop_toTopOf="@id/v_bg_end">
        <include android:id="@id/layout_options" android:layout_width="fill_parent" android:layout_height="wrap_content" layout="@layout/layout_subtitle_options" />
    </androidx.core.widget.NestedScrollView>
</merge>
