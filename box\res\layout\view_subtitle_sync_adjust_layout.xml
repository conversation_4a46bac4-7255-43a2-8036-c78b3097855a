<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:id="@id/ll_sync_adjust" android:focusable="true" android:clickable="true" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layoutDirection="locale"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.appcompat.widget.AppCompatTextView android:textSize="14.0sp" android:textColor="@color/white" android:id="@id/tvTitle" android:layout_width="0.0dip" android:layout_height="wrap_content" app:layout_constraintEnd_toStartOf="@id/iv_sync_adjust_plus" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <com.noober.background.view.BLImageView android:id="@id/iv_sync_adjust_minus" android:layout_width="40.0dip" android:layout_height="40.0dip" android:scaleType="center" app:bl_corners_leftRadius="5.0dip" app:bl_gradient_angle="0" app:bl_gradient_centerColor="@color/brand_new_gradient_center" app:bl_gradient_endColor="@color/brand_new_gradient_end" app:bl_gradient_startColor="@color/brand_new_gradient_start" app:layout_constraintEnd_toStartOf="@id/et_sync_adjust" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tvTitle" app:srcCompat="@drawable/subtitle_sync_adjust_minus" />
    <androidx.appcompat.widget.AppCompatEditText android:textSize="14.0sp" android:textColor="@color/white" android:gravity="center" android:id="@id/et_sync_adjust" android:background="@color/subtitle_list_dialog_main_bg_color" android:layout_width="60.0dip" android:layout_height="0.0dip" android:lines="1" android:scrollHorizontally="true" android:inputType="numberDecimal" android:fontFamily="sans-serif-medium" app:layout_constraintBottom_toBottomOf="@id/iv_sync_adjust_minus" app:layout_constraintStart_toEndOf="@id/iv_sync_adjust_minus" app:layout_constraintTop_toTopOf="@id/iv_sync_adjust_minus" />
    <com.noober.background.view.BLImageView android:id="@id/iv_sync_adjust_plus" android:layout_width="40.0dip" android:layout_height="40.0dip" android:scaleType="center" app:bl_corners_rightRadius="5.0dip" app:bl_gradient_angle="0" app:bl_gradient_centerColor="@color/brand_new_gradient_center" app:bl_gradient_endColor="@color/brand_new_gradient_end" app:bl_gradient_startColor="@color/brand_new_gradient_start" app:layout_constraintBottom_toBottomOf="@id/et_sync_adjust" app:layout_constraintStart_toEndOf="@id/et_sync_adjust" app:layout_constraintTop_toTopOf="@id/et_sync_adjust" app:srcCompat="@drawable/subtitle_sync_adjust_plus" />
</androidx.constraintlayout.widget.ConstraintLayout>
