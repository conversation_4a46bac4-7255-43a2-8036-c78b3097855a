<?xml version="1.0" encoding="utf-8"?>
<merge android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <View android:id="@id/v_top_bg" android:layout_width="fill_parent" android:layout_height="fill_parent" />
    <com.noober.background.view.BLFrameLayout android:layout_gravity="bottom" android:id="@id/fl_content" android:focusable="true" android:clickable="true" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="156.0dip" app:bl_corners_topRadius="8.0dip" app:bl_solid_color="@color/bg_01">
        <androidx.appcompat.widget.AppCompatTextView android:textColor="@color/text_01" android:ellipsize="end" android:gravity="center_vertical" android:id="@id/tv_title" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="16.0dip" android:maxLines="1" android:layout_marginStart="16.0dip" android:layout_marginEnd="42.0dip" style="@style/style_regula_bigger_text" />
        <androidx.appcompat.widget.AppCompatImageView android:layout_gravity="end" android:id="@id/iv_close" android:padding="4.0dip" android:layout_width="30.0dip" android:layout_height="30.0dip" android:layout_marginTop="6.0dip" android:src="@mipmap/ic_close_black" android:scaleType="center" android:layout_marginEnd="6.0dip" />
        <androidx.recyclerview.widget.RecyclerView android:id="@id/recycler_view" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="40.0dip" android:minHeight="375.0dip" />
    </com.noober.background.view.BLFrameLayout>
</merge>
