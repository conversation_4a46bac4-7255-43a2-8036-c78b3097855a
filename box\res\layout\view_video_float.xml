<?xml version="1.0" encoding="utf-8"?>
<merge android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <FrameLayout android:id="@id/fl_player_container" android:layout_width="0.0dip" android:layout_height="0.0dip" app:layout_constraintDimensionRatio="h,16:9" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <com.avery.subtitle.widget.SimpleSubtitleView android:textSize="12.0sp" android:textColor="@color/white" android:ellipsize="end" android:gravity="center|bottom" android:id="@id/v_subtitle" android:paddingLeft="8.0dip" android:paddingTop="8.0dip" android:paddingRight="8.0dip" android:paddingBottom="12.0dip" android:layout_width="wrap_content" android:layout_height="wrap_content" android:shadowColor="@color/black_90" android:shadowDx="2.0" android:shadowDy="1.0" android:shadowRadius="2.0" android:lineSpacingExtra="1.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" />
    <View android:id="@id/v_trans_float" android:background="@color/black_20" android:layout_width="0.0dip" android:layout_height="0.0dip" app:layout_constraintBottom_toBottomOf="@id/fl_player_container" app:layout_constraintEnd_toEndOf="@id/fl_player_container" app:layout_constraintStart_toStartOf="@id/fl_player_container" app:layout_constraintTop_toTopOf="@id/fl_player_container" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_full" android:layout_width="40.0dip" android:layout_height="40.0dip" android:layout_marginTop="-2.0dip" android:src="@mipmap/video_float_ic_full" android:scaleType="center" android:layout_marginEnd="-2.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_close" android:layout_width="40.0dip" android:layout_height="40.0dip" android:layout_marginTop="-2.0dip" android:src="@mipmap/video_float_ic_close" android:scaleType="center" android:layout_marginEnd="-2.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_play" android:layout_width="28.0dip" android:layout_height="28.0dip" android:src="@drawable/video_float_selector_play_pause" android:scaleType="fitXY" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_backward" android:layout_width="24.0dip" android:layout_height="24.0dip" android:src="@mipmap/video_float_ic_backward" android:scaleType="fitXY" android:layout_marginEnd="20.0dip" app:layout_constraintBottom_toBottomOf="@id/iv_play" app:layout_constraintEnd_toStartOf="@id/iv_play" app:layout_constraintTop_toTopOf="@id/iv_play" />
    <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_forward" android:layout_width="24.0dip" android:layout_height="24.0dip" android:src="@mipmap/video_float_ic_forward" android:scaleType="fitXY" android:layout_marginStart="20.0dip" app:layout_constraintBottom_toBottomOf="@id/iv_play" app:layout_constraintStart_toEndOf="@id/iv_play" app:layout_constraintTop_toTopOf="@id/iv_play" />
    <androidx.constraintlayout.widget.Group android:id="@id/group_control" android:layout_width="wrap_content" android:layout_height="wrap_content" app:constraint_referenced_ids="iv_full,iv_close,iv_play,         iv_backward,iv_forward,v_trans_float" />
</merge>
