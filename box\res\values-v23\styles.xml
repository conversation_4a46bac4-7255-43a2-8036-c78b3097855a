<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Menu" parent="@android:style/TextAppearance.Material.Widget.ActionBar.Menu" />
    <style name="Base.TextAppearance.AppCompat.Widget.Button.Inverse" parent="@android:style/TextAppearance.Material.Widget.Button.Inverse" />
    <style name="Base.Theme.AppCompat" parent="@style/Base.V23.Theme.AppCompat" />
    <style name="Base.Theme.AppCompat.Light" parent="@style/Base.V23.Theme.AppCompat.Light" />
    <style name="Base.Widget.AppCompat.ActionButton.Overflow" parent="@android:style/Widget.Material.ActionButton.Overflow" />
    <style name="Base.Widget.AppCompat.Button.Borderless.Colored" parent="@android:style/Widget.Material.Button.Borderless.Colored" />
    <style name="Base.Widget.AppCompat.Button.Colored" parent="@android:style/Widget.Material.Button.Colored">
        <item name="android:textAppearance">@style/TextAppearance.AppCompat.Widget.Button.Colored</item>
    </style>
    <style name="Base.Widget.AppCompat.EditText" parent="@android:style/Widget.Material.EditText">
        <item name="android:breakStrategy">simple</item>
        <item name="android:hyphenationFrequency">none</item>
    </style>
    <style name="Base.Widget.AppCompat.RatingBar.Indicator" parent="@android:style/Widget.Material.RatingBar.Indicator" />
    <style name="Base.Widget.AppCompat.RatingBar.Small" parent="@android:style/Widget.Material.RatingBar.Small" />
    <style name="Base.Widget.AppCompat.Spinner.Underlined" parent="@android:style/Widget.Material.Spinner.Underlined" />
    <style name="Base.Widget.AppCompat.TextView" parent="@android:style/Widget.Material.TextView">
        <item name="android:breakStrategy">high_quality</item>
        <item name="android:hyphenationFrequency">none</item>
    </style>
    <style name="CardView" parent="@style/Base.CardView">
        <item name="cardBackgroundColor">?android:colorBackgroundFloating</item>
    </style>
    <style name="ThemeOverlay.Material3.AutoCompleteTextView" parent="@style/Base.ThemeOverlay.Material3.AutoCompleteTextView">
        <item name="colorControlNormal">@color/m3_textfield_indicator_text_color</item>
    </style>
    <style name="ThemeOverlay.Material3.Chip" parent="">
        <item name="colorControlNormal">@color/m3_chip_text_color</item>
    </style>
    <style name="ThemeOverlay.Material3.Chip.Assist" parent="">
        <item name="colorControlNormal">@color/m3_assist_chip_icon_tint_color</item>
    </style>
    <style name="ThemeOverlay.Material3.TextInputEditText" parent="@style/Base.ThemeOverlay.Material3.TextInputEditText">
        <item name="colorControlNormal">@color/m3_textfield_indicator_text_color</item>
    </style>
    <style name="Widget.Material3.CompoundButton.CheckBox" parent="@style/Base.Widget.Material3.CompoundButton.CheckBox">
        <item name="android:background">@drawable/m3_selection_control_ripple</item>
    </style>
    <style name="Widget.Material3.CompoundButton.RadioButton" parent="@style/Base.Widget.Material3.CompoundButton.RadioButton">
        <item name="android:background">@drawable/m3_radiobutton_ripple</item>
    </style>
    <style name="Widget.Material3.CompoundButton.Switch" parent="@style/Base.Widget.Material3.CompoundButton.Switch">
        <item name="android:background">@drawable/m3_selection_control_ripple</item>
    </style>
    <style name="Widget.Material3.TabLayout.OnSurface" parent="@style/Base.Widget.Material3.TabLayout.OnSurface">
        <item name="android:background">@drawable/m3_tabs_transparent_background</item>
    </style>
    <style name="Widget.MaterialComponents.PopupMenu.ContextMenu" parent="@style/Base.Widget.MaterialComponents.PopupMenu.ContextMenu">
        <item name="android:popupBackground">?popupMenuBackground</item>
        <item name="android:popupElevation">8.0dip</item>
    </style>
    <style name="Widget.MaterialComponents.PopupMenu.ListPopupWindow" parent="@style/Base.Widget.MaterialComponents.PopupMenu.ListPopupWindow">
        <item name="android:popupBackground">?popupMenuBackground</item>
        <item name="android:popupElevation">8.0dip</item>
    </style>
    <style name="Base.V23.Theme.AppCompat" parent="@style/Base.V22.Theme.AppCompat">
        <item name="actionBarItemBackground">?android:actionBarItemBackground</item>
        <item name="actionMenuTextAppearance">?android:actionMenuTextAppearance</item>
        <item name="actionMenuTextColor">?android:actionMenuTextColor</item>
        <item name="actionOverflowButtonStyle">?android:actionOverflowButtonStyle</item>
        <item name="controlBackground">@drawable/abc_control_background_material</item>
        <item name="ratingBarStyleIndicator">?android:ratingBarStyleIndicator</item>
        <item name="ratingBarStyleSmall">?android:ratingBarStyleSmall</item>
    </style>
    <style name="Base.V23.Theme.AppCompat.Light" parent="@style/Base.V22.Theme.AppCompat.Light">
        <item name="actionBarItemBackground">?android:actionBarItemBackground</item>
        <item name="actionMenuTextAppearance">?android:actionMenuTextAppearance</item>
        <item name="actionMenuTextColor">?android:actionMenuTextColor</item>
        <item name="actionOverflowButtonStyle">?android:actionOverflowButtonStyle</item>
        <item name="controlBackground">@drawable/abc_control_background_material</item>
        <item name="ratingBarStyleIndicator">?android:ratingBarStyleIndicator</item>
        <item name="ratingBarStyleSmall">?android:ratingBarStyleSmall</item>
    </style>
</resources>
