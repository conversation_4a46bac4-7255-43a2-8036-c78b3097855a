<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="AppTheme.AppStart" parent="@style/Theme.AppCompat.Light.NoActionBar">
        <item name="android:screenOrientation">portrait</item>
        <item name="android:windowBackground">@drawable/background_launcher</item>
        <item name="android:windowTranslucentStatus">false</item>
        <item name="android:windowTranslucentNavigation">false</item>
        <item name="android:colorPrimary">@color/white</item>
        <item name="android:colorPrimaryDark">@color/transparent</item>
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
        <item name="android:windowLightStatusBar">true</item>
        <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>
    </style>
    <style name="AppTheme.AppStart.Compat" parent="@style/AppTheme.AppStart">
        <item name="android:windowAnimationStyle">@style/activityAnimation</item>
    </style>
    <style name="Base.Theme.SplashScreen" parent="@style/Base.v27.Theme.SplashScreen" />
    <style name="Base.Theme.SplashScreen.Light" parent="@style/Base.v27.Theme.SplashScreen.Light" />
    <style name="mbridge_transparent_common_activity_style" parent="@style/mbridge_common_activity_style" />
    <style name="tpush_notification_large_title">
        <item name="android:textSize">14.0sp</item>
        <item name="android:textColor">@color/tpush_notification_text_title</item>
        <item name="android:ellipsize">end</item>
        <item name="android:maxLines">1</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:textDirection">locale</item>
    </style>
    <style name="Base.v27.Theme.SplashScreen" parent="@style/Base.v21.Theme.SplashScreen">
        <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>
    </style>
    <style name="Base.v27.Theme.SplashScreen.Light" parent="@style/Base.v21.Theme.SplashScreen.Light">
        <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>
    </style>
</resources>
