<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="AppTheme.AppStart.Compat" parent="@style/Theme.SplashScreen">
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowSplashScreenBrandingImage">@mipmap/launch_tips</item>
        <item name="postSplashScreenTheme">@style/AppTheme.AppStart</item>
        <item name="windowSplashScreenAnimatedIcon">@mipmap/launch_logo</item>
        <item name="windowSplashScreenAnimationDuration">200</item>
        <item name="windowSplashScreenBackground">@color/gray_dark_00</item>
    </style>
    <style name="Theme.Material3.DynamicColors.Dark" parent="@style/Theme.Material3.Dark">
        <item name="android:colorBackground">@color/m3_sys_color_dynamic_dark_background</item>
        <item name="android:textColorPrimary">@color/m3_dynamic_dark_default_color_primary_text</item>
        <item name="android:textColorPrimaryDisableOnly">@color/m3_dynamic_dark_primary_text_disable_only</item>
        <item name="android:textColorSecondary">@color/m3_dynamic_dark_default_color_secondary_text</item>
        <item name="android:textColorPrimaryInverse">@color/m3_dynamic_default_color_primary_text</item>
        <item name="android:textColorSecondaryInverse">@color/m3_dynamic_default_color_secondary_text</item>
        <item name="android:textColorHintInverse">@color/m3_dynamic_hint_foreground</item>
        <item name="android:textColorHighlight">@color/m3_dynamic_dark_highlighted_text</item>
        <item name="android:textColorHint">@color/m3_dynamic_dark_hint_foreground</item>
        <item name="android:textColorTertiary">@color/m3_dynamic_dark_default_color_secondary_text</item>
        <item name="android:textColorTertiaryInverse">@color/m3_dynamic_default_color_secondary_text</item>
        <item name="android:textColorPrimaryInverseDisableOnly">@color/m3_dynamic_primary_text_disable_only</item>
        <item name="android:textColorAlertDialogListItem">@color/m3_dynamic_dark_default_color_primary_text</item>
        <item name="android:textColorHighlightInverse">@color/m3_dynamic_highlighted_text</item>
        <item name="colorError">@color/m3_sys_color_dark_error</item>
        <item name="colorErrorContainer">@color/m3_sys_color_dark_error_container</item>
        <item name="colorOnBackground">@color/m3_sys_color_dynamic_dark_on_background</item>
        <item name="colorOnError">@color/m3_sys_color_dark_on_error</item>
        <item name="colorOnErrorContainer">@color/m3_sys_color_dark_on_error_container</item>
        <item name="colorOnPrimary">@color/m3_sys_color_dynamic_dark_on_primary</item>
        <item name="colorOnPrimaryContainer">@color/m3_sys_color_dynamic_dark_on_primary_container</item>
        <item name="colorOnPrimaryFixed">@color/m3_sys_color_dynamic_on_primary_fixed</item>
        <item name="colorOnPrimaryFixedVariant">@color/m3_sys_color_dynamic_on_primary_fixed_variant</item>
        <item name="colorOnSecondary">@color/m3_sys_color_dynamic_dark_on_secondary</item>
        <item name="colorOnSecondaryContainer">@color/m3_sys_color_dynamic_dark_on_secondary_container</item>
        <item name="colorOnSecondaryFixed">@color/m3_sys_color_dynamic_on_secondary_fixed</item>
        <item name="colorOnSecondaryFixedVariant">@color/m3_sys_color_dynamic_on_secondary_fixed_variant</item>
        <item name="colorOnSurface">@color/m3_sys_color_dynamic_dark_on_surface</item>
        <item name="colorOnSurfaceInverse">@color/m3_sys_color_dynamic_dark_inverse_on_surface</item>
        <item name="colorOnSurfaceVariant">@color/m3_sys_color_dynamic_dark_on_surface_variant</item>
        <item name="colorOnTertiary">@color/m3_sys_color_dynamic_dark_on_tertiary</item>
        <item name="colorOnTertiaryContainer">@color/m3_sys_color_dynamic_dark_on_tertiary_container</item>
        <item name="colorOnTertiaryFixed">@color/m3_sys_color_dynamic_on_tertiary_fixed</item>
        <item name="colorOnTertiaryFixedVariant">@color/m3_sys_color_dynamic_on_tertiary_fixed_variant</item>
        <item name="colorOutline">@color/m3_sys_color_dynamic_dark_outline</item>
        <item name="colorPrimary">@color/m3_sys_color_dynamic_dark_primary</item>
        <item name="colorPrimaryContainer">@color/m3_sys_color_dynamic_dark_primary_container</item>
        <item name="colorPrimaryFixed">@color/m3_sys_color_dynamic_primary_fixed</item>
        <item name="colorPrimaryFixedDim">@color/m3_sys_color_dynamic_primary_fixed_dim</item>
        <item name="colorPrimaryInverse">@color/m3_sys_color_dynamic_dark_inverse_primary</item>
        <item name="colorSecondary">@color/m3_sys_color_dynamic_dark_secondary</item>
        <item name="colorSecondaryContainer">@color/m3_sys_color_dynamic_dark_secondary_container</item>
        <item name="colorSecondaryFixed">@color/m3_sys_color_dynamic_secondary_fixed</item>
        <item name="colorSecondaryFixedDim">@color/m3_sys_color_dynamic_secondary_fixed_dim</item>
        <item name="colorSurface">@color/m3_sys_color_dynamic_dark_surface</item>
        <item name="colorSurfaceBright">@color/m3_sys_color_dynamic_dark_surface_bright</item>
        <item name="colorSurfaceContainer">@color/m3_sys_color_dynamic_dark_surface_container</item>
        <item name="colorSurfaceContainerHigh">@color/m3_sys_color_dynamic_dark_surface_container_high</item>
        <item name="colorSurfaceContainerHighest">@color/m3_sys_color_dynamic_dark_surface_container_highest</item>
        <item name="colorSurfaceContainerLow">@color/m3_sys_color_dynamic_dark_surface_container_low</item>
        <item name="colorSurfaceContainerLowest">@color/m3_sys_color_dynamic_dark_surface_container_lowest</item>
        <item name="colorSurfaceDim">@color/m3_sys_color_dynamic_dark_surface_dim</item>
        <item name="colorSurfaceInverse">@color/m3_sys_color_dynamic_dark_inverse_surface</item>
        <item name="colorSurfaceVariant">@color/m3_sys_color_dynamic_dark_surface_variant</item>
        <item name="colorTertiary">@color/m3_sys_color_dynamic_dark_tertiary</item>
        <item name="colorTertiaryContainer">@color/m3_sys_color_dynamic_dark_tertiary_container</item>
        <item name="colorTertiaryFixed">@color/m3_sys_color_dynamic_tertiary_fixed</item>
        <item name="colorTertiaryFixedDim">@color/m3_sys_color_dynamic_tertiary_fixed_dim</item>
        <item name="isMaterial3DynamicColorApplied">true</item>
    </style>
    <style name="Theme.Material3.DynamicColors.Light" parent="@style/Theme.Material3.Light">
        <item name="android:colorBackground">@color/m3_sys_color_dynamic_light_background</item>
        <item name="android:textColorPrimary">@color/m3_dynamic_default_color_primary_text</item>
        <item name="android:textColorPrimaryDisableOnly">@color/m3_dynamic_primary_text_disable_only</item>
        <item name="android:textColorSecondary">@color/m3_dynamic_default_color_secondary_text</item>
        <item name="android:textColorPrimaryInverse">@color/m3_dynamic_dark_default_color_primary_text</item>
        <item name="android:textColorSecondaryInverse">@color/m3_dynamic_dark_default_color_secondary_text</item>
        <item name="android:textColorHintInverse">@color/m3_dynamic_dark_hint_foreground</item>
        <item name="android:textColorHighlight">@color/m3_dynamic_highlighted_text</item>
        <item name="android:textColorHint">@color/m3_dynamic_hint_foreground</item>
        <item name="android:textColorTertiary">@color/m3_dynamic_default_color_secondary_text</item>
        <item name="android:textColorTertiaryInverse">@color/m3_dynamic_dark_default_color_secondary_text</item>
        <item name="android:textColorPrimaryInverseDisableOnly">@color/m3_dynamic_dark_primary_text_disable_only</item>
        <item name="android:textColorAlertDialogListItem">@color/m3_dynamic_default_color_primary_text</item>
        <item name="android:textColorHighlightInverse">@color/m3_dynamic_dark_highlighted_text</item>
        <item name="colorError">@color/m3_sys_color_light_error</item>
        <item name="colorErrorContainer">@color/m3_sys_color_light_error_container</item>
        <item name="colorOnBackground">@color/m3_sys_color_dynamic_light_on_background</item>
        <item name="colorOnError">@color/m3_sys_color_light_on_error</item>
        <item name="colorOnErrorContainer">@color/m3_sys_color_light_on_error_container</item>
        <item name="colorOnPrimary">@color/m3_sys_color_dynamic_light_on_primary</item>
        <item name="colorOnPrimaryContainer">@color/m3_sys_color_dynamic_light_on_primary_container</item>
        <item name="colorOnPrimaryFixed">@color/m3_sys_color_dynamic_on_primary_fixed</item>
        <item name="colorOnPrimaryFixedVariant">@color/m3_sys_color_dynamic_on_primary_fixed_variant</item>
        <item name="colorOnSecondary">@color/m3_sys_color_dynamic_light_on_secondary</item>
        <item name="colorOnSecondaryContainer">@color/m3_sys_color_dynamic_light_on_secondary_container</item>
        <item name="colorOnSecondaryFixed">@color/m3_sys_color_dynamic_on_secondary_fixed</item>
        <item name="colorOnSecondaryFixedVariant">@color/m3_sys_color_dynamic_on_secondary_fixed_variant</item>
        <item name="colorOnSurface">@color/m3_sys_color_dynamic_light_on_surface</item>
        <item name="colorOnSurfaceInverse">@color/m3_sys_color_dynamic_light_inverse_on_surface</item>
        <item name="colorOnSurfaceVariant">@color/m3_sys_color_dynamic_light_on_surface_variant</item>
        <item name="colorOnTertiary">@color/m3_sys_color_dynamic_light_on_tertiary</item>
        <item name="colorOnTertiaryContainer">@color/m3_sys_color_dynamic_light_on_tertiary_container</item>
        <item name="colorOnTertiaryFixed">@color/m3_sys_color_dynamic_on_tertiary_fixed</item>
        <item name="colorOnTertiaryFixedVariant">@color/m3_sys_color_dynamic_on_tertiary_fixed_variant</item>
        <item name="colorOutline">@color/m3_sys_color_dynamic_light_outline</item>
        <item name="colorPrimary">@color/m3_sys_color_dynamic_light_primary</item>
        <item name="colorPrimaryContainer">@color/m3_sys_color_dynamic_light_primary_container</item>
        <item name="colorPrimaryFixed">@color/m3_sys_color_dynamic_primary_fixed</item>
        <item name="colorPrimaryFixedDim">@color/m3_sys_color_dynamic_primary_fixed_dim</item>
        <item name="colorPrimaryInverse">@color/m3_sys_color_dynamic_light_inverse_primary</item>
        <item name="colorSecondary">@color/m3_sys_color_dynamic_light_secondary</item>
        <item name="colorSecondaryContainer">@color/m3_sys_color_dynamic_light_secondary_container</item>
        <item name="colorSecondaryFixed">@color/m3_sys_color_dynamic_secondary_fixed</item>
        <item name="colorSecondaryFixedDim">@color/m3_sys_color_dynamic_secondary_fixed_dim</item>
        <item name="colorSurface">@color/m3_sys_color_dynamic_light_surface</item>
        <item name="colorSurfaceBright">@color/m3_sys_color_dynamic_light_surface_bright</item>
        <item name="colorSurfaceContainer">@color/m3_sys_color_dynamic_light_surface_container</item>
        <item name="colorSurfaceContainerHigh">@color/m3_sys_color_dynamic_light_surface_container_high</item>
        <item name="colorSurfaceContainerHighest">@color/m3_sys_color_dynamic_light_surface_container_highest</item>
        <item name="colorSurfaceContainerLow">@color/m3_sys_color_dynamic_light_surface_container_low</item>
        <item name="colorSurfaceContainerLowest">@color/m3_sys_color_dynamic_light_surface_container_lowest</item>
        <item name="colorSurfaceDim">@color/m3_sys_color_dynamic_light_surface_dim</item>
        <item name="colorSurfaceInverse">@color/m3_sys_color_dynamic_light_inverse_surface</item>
        <item name="colorSurfaceVariant">@color/m3_sys_color_dynamic_light_surface_variant</item>
        <item name="colorTertiary">@color/m3_sys_color_dynamic_light_tertiary</item>
        <item name="colorTertiaryContainer">@color/m3_sys_color_dynamic_light_tertiary_container</item>
        <item name="colorTertiaryFixed">@color/m3_sys_color_dynamic_tertiary_fixed</item>
        <item name="colorTertiaryFixedDim">@color/m3_sys_color_dynamic_tertiary_fixed_dim</item>
        <item name="isMaterial3DynamicColorApplied">true</item>
    </style>
    <style name="Theme.SplashScreen" parent="@style/Base.Theme.SplashScreen.DayNight">
        <item name="android:windowSplashScreenBackground">?windowSplashScreenBackground</item>
        <item name="android:windowSplashScreenAnimatedIcon">?windowSplashScreenAnimatedIcon</item>
        <item name="android:windowSplashScreenAnimationDuration">?windowSplashScreenAnimationDuration</item>
    </style>
    <style name="Theme.SplashScreen.IconBackground" parent="@style/Theme.SplashScreen">
        <item name="android:windowSplashScreenIconBackgroundColor">?windowSplashScreenIconBackgroundColor</item>
    </style>
    <style name="ThemeOverlay.Material3.DynamicColors.Dark" parent="">
        <item name="android:colorBackground">@color/m3_sys_color_dynamic_dark_background</item>
        <item name="android:textColorPrimary">@color/m3_dynamic_dark_default_color_primary_text</item>
        <item name="android:textColorPrimaryDisableOnly">@color/m3_dynamic_dark_primary_text_disable_only</item>
        <item name="android:textColorSecondary">@color/m3_dynamic_dark_default_color_secondary_text</item>
        <item name="android:textColorPrimaryInverse">@color/m3_dynamic_default_color_primary_text</item>
        <item name="android:textColorSecondaryInverse">@color/m3_dynamic_default_color_secondary_text</item>
        <item name="android:textColorHintInverse">@color/m3_dynamic_hint_foreground</item>
        <item name="android:textColorHighlight">@color/m3_dynamic_dark_highlighted_text</item>
        <item name="android:textColorHint">@color/m3_dynamic_dark_hint_foreground</item>
        <item name="android:textColorTertiary">@color/m3_dynamic_dark_default_color_secondary_text</item>
        <item name="android:textColorTertiaryInverse">@color/m3_dynamic_default_color_secondary_text</item>
        <item name="android:textColorPrimaryInverseDisableOnly">@color/m3_dynamic_primary_text_disable_only</item>
        <item name="android:textColorAlertDialogListItem">@color/m3_dynamic_dark_default_color_primary_text</item>
        <item name="android:textColorHighlightInverse">@color/m3_dynamic_highlighted_text</item>
        <item name="colorError">@color/material_dynamic_color_dark_error</item>
        <item name="colorErrorContainer">@color/material_dynamic_color_dark_error_container</item>
        <item name="colorOnBackground">@color/m3_sys_color_dynamic_dark_on_background</item>
        <item name="colorOnError">@color/material_dynamic_color_dark_on_error</item>
        <item name="colorOnErrorContainer">@color/material_dynamic_color_dark_on_error_container</item>
        <item name="colorOnPrimary">@color/m3_sys_color_dynamic_dark_on_primary</item>
        <item name="colorOnPrimaryContainer">@color/m3_sys_color_dynamic_dark_on_primary_container</item>
        <item name="colorOnPrimaryFixed">@color/m3_sys_color_dynamic_on_primary_fixed</item>
        <item name="colorOnPrimaryFixedVariant">@color/m3_sys_color_dynamic_on_primary_fixed_variant</item>
        <item name="colorOnSecondary">@color/m3_sys_color_dynamic_dark_on_secondary</item>
        <item name="colorOnSecondaryContainer">@color/m3_sys_color_dynamic_dark_on_secondary_container</item>
        <item name="colorOnSecondaryFixed">@color/m3_sys_color_dynamic_on_secondary_fixed</item>
        <item name="colorOnSecondaryFixedVariant">@color/m3_sys_color_dynamic_on_secondary_fixed_variant</item>
        <item name="colorOnSurface">@color/m3_sys_color_dynamic_dark_on_surface</item>
        <item name="colorOnSurfaceInverse">@color/m3_sys_color_dynamic_dark_inverse_on_surface</item>
        <item name="colorOnSurfaceVariant">@color/m3_sys_color_dynamic_dark_on_surface_variant</item>
        <item name="colorOnTertiary">@color/m3_sys_color_dynamic_dark_on_tertiary</item>
        <item name="colorOnTertiaryContainer">@color/m3_sys_color_dynamic_dark_on_tertiary_container</item>
        <item name="colorOnTertiaryFixed">@color/m3_sys_color_dynamic_on_tertiary_fixed</item>
        <item name="colorOnTertiaryFixedVariant">@color/m3_sys_color_dynamic_on_tertiary_fixed_variant</item>
        <item name="colorOutline">@color/m3_sys_color_dynamic_dark_outline</item>
        <item name="colorOutlineVariant">@color/m3_sys_color_dynamic_dark_outline_variant</item>
        <item name="colorPrimary">@color/m3_sys_color_dynamic_dark_primary</item>
        <item name="colorPrimaryContainer">@color/m3_sys_color_dynamic_dark_primary_container</item>
        <item name="colorPrimaryFixed">@color/m3_sys_color_dynamic_primary_fixed</item>
        <item name="colorPrimaryFixedDim">@color/m3_sys_color_dynamic_primary_fixed_dim</item>
        <item name="colorPrimaryInverse">@color/m3_sys_color_dynamic_dark_inverse_primary</item>
        <item name="colorSecondary">@color/m3_sys_color_dynamic_dark_secondary</item>
        <item name="colorSecondaryContainer">@color/m3_sys_color_dynamic_dark_secondary_container</item>
        <item name="colorSecondaryFixed">@color/m3_sys_color_dynamic_secondary_fixed</item>
        <item name="colorSecondaryFixedDim">@color/m3_sys_color_dynamic_secondary_fixed_dim</item>
        <item name="colorSurface">@color/m3_sys_color_dynamic_dark_surface</item>
        <item name="colorSurfaceBright">@color/m3_sys_color_dynamic_dark_surface_bright</item>
        <item name="colorSurfaceContainer">@color/m3_sys_color_dynamic_dark_surface_container</item>
        <item name="colorSurfaceContainerHigh">@color/m3_sys_color_dynamic_dark_surface_container_high</item>
        <item name="colorSurfaceContainerHighest">@color/m3_sys_color_dynamic_dark_surface_container_highest</item>
        <item name="colorSurfaceContainerLow">@color/m3_sys_color_dynamic_dark_surface_container_low</item>
        <item name="colorSurfaceContainerLowest">@color/m3_sys_color_dynamic_dark_surface_container_lowest</item>
        <item name="colorSurfaceDim">@color/m3_sys_color_dynamic_dark_surface_dim</item>
        <item name="colorSurfaceInverse">@color/m3_sys_color_dynamic_dark_inverse_surface</item>
        <item name="colorSurfaceVariant">@color/m3_sys_color_dynamic_dark_surface_variant</item>
        <item name="colorTertiary">@color/m3_sys_color_dynamic_dark_tertiary</item>
        <item name="colorTertiaryContainer">@color/m3_sys_color_dynamic_dark_tertiary_container</item>
        <item name="colorTertiaryFixed">@color/m3_sys_color_dynamic_tertiary_fixed</item>
        <item name="colorTertiaryFixedDim">@color/m3_sys_color_dynamic_tertiary_fixed_dim</item>
        <item name="isMaterial3DynamicColorApplied">true</item>
    </style>
    <style name="ThemeOverlay.Material3.DynamicColors.Light" parent="">
        <item name="android:colorBackground">@color/m3_sys_color_dynamic_light_background</item>
        <item name="android:textColorPrimary">@color/m3_dynamic_default_color_primary_text</item>
        <item name="android:textColorPrimaryDisableOnly">@color/m3_dynamic_primary_text_disable_only</item>
        <item name="android:textColorSecondary">@color/m3_dynamic_default_color_secondary_text</item>
        <item name="android:textColorPrimaryInverse">@color/m3_dynamic_dark_default_color_primary_text</item>
        <item name="android:textColorSecondaryInverse">@color/m3_dynamic_dark_default_color_secondary_text</item>
        <item name="android:textColorHintInverse">@color/m3_dynamic_dark_hint_foreground</item>
        <item name="android:textColorHighlight">@color/m3_dynamic_highlighted_text</item>
        <item name="android:textColorHint">@color/m3_dynamic_hint_foreground</item>
        <item name="android:textColorTertiary">@color/m3_dynamic_default_color_secondary_text</item>
        <item name="android:textColorTertiaryInverse">@color/m3_dynamic_dark_default_color_secondary_text</item>
        <item name="android:textColorPrimaryInverseDisableOnly">@color/m3_dynamic_dark_primary_text_disable_only</item>
        <item name="android:textColorAlertDialogListItem">@color/m3_dynamic_default_color_primary_text</item>
        <item name="android:textColorHighlightInverse">@color/m3_dynamic_dark_highlighted_text</item>
        <item name="colorError">@color/material_dynamic_color_light_error</item>
        <item name="colorErrorContainer">@color/material_dynamic_color_light_error_container</item>
        <item name="colorOnBackground">@color/m3_sys_color_dynamic_light_on_background</item>
        <item name="colorOnError">@color/material_dynamic_color_light_on_error</item>
        <item name="colorOnErrorContainer">@color/material_dynamic_color_light_on_error_container</item>
        <item name="colorOnPrimary">@color/m3_sys_color_dynamic_light_on_primary</item>
        <item name="colorOnPrimaryContainer">@color/m3_sys_color_dynamic_light_on_primary_container</item>
        <item name="colorOnPrimaryFixed">@color/m3_sys_color_dynamic_on_primary_fixed</item>
        <item name="colorOnPrimaryFixedVariant">@color/m3_sys_color_dynamic_on_primary_fixed_variant</item>
        <item name="colorOnSecondary">@color/m3_sys_color_dynamic_light_on_secondary</item>
        <item name="colorOnSecondaryContainer">@color/m3_sys_color_dynamic_light_on_secondary_container</item>
        <item name="colorOnSecondaryFixed">@color/m3_sys_color_dynamic_on_secondary_fixed</item>
        <item name="colorOnSecondaryFixedVariant">@color/m3_sys_color_dynamic_on_secondary_fixed_variant</item>
        <item name="colorOnSurface">@color/m3_sys_color_dynamic_light_on_surface</item>
        <item name="colorOnSurfaceInverse">@color/m3_sys_color_dynamic_light_inverse_on_surface</item>
        <item name="colorOnSurfaceVariant">@color/m3_sys_color_dynamic_light_on_surface_variant</item>
        <item name="colorOnTertiary">@color/m3_sys_color_dynamic_light_on_tertiary</item>
        <item name="colorOnTertiaryContainer">@color/m3_sys_color_dynamic_light_on_tertiary_container</item>
        <item name="colorOnTertiaryFixed">@color/m3_sys_color_dynamic_on_tertiary_fixed</item>
        <item name="colorOnTertiaryFixedVariant">@color/m3_sys_color_dynamic_on_tertiary_fixed_variant</item>
        <item name="colorOutline">@color/m3_sys_color_dynamic_light_outline</item>
        <item name="colorOutlineVariant">@color/m3_sys_color_dynamic_light_outline_variant</item>
        <item name="colorPrimary">@color/m3_sys_color_dynamic_light_primary</item>
        <item name="colorPrimaryContainer">@color/m3_sys_color_dynamic_light_primary_container</item>
        <item name="colorPrimaryFixed">@color/m3_sys_color_dynamic_primary_fixed</item>
        <item name="colorPrimaryFixedDim">@color/m3_sys_color_dynamic_primary_fixed_dim</item>
        <item name="colorPrimaryInverse">@color/m3_sys_color_dynamic_light_inverse_primary</item>
        <item name="colorSecondary">@color/m3_sys_color_dynamic_light_secondary</item>
        <item name="colorSecondaryContainer">@color/m3_sys_color_dynamic_light_secondary_container</item>
        <item name="colorSecondaryFixed">@color/m3_sys_color_dynamic_secondary_fixed</item>
        <item name="colorSecondaryFixedDim">@color/m3_sys_color_dynamic_secondary_fixed_dim</item>
        <item name="colorSurface">@color/m3_sys_color_dynamic_light_surface</item>
        <item name="colorSurfaceBright">@color/m3_sys_color_dynamic_light_surface_bright</item>
        <item name="colorSurfaceContainer">@color/m3_sys_color_dynamic_light_surface_container</item>
        <item name="colorSurfaceContainerHigh">@color/m3_sys_color_dynamic_light_surface_container_high</item>
        <item name="colorSurfaceContainerHighest">@color/m3_sys_color_dynamic_light_surface_container_highest</item>
        <item name="colorSurfaceContainerLow">@color/m3_sys_color_dynamic_light_surface_container_low</item>
        <item name="colorSurfaceContainerLowest">@color/m3_sys_color_dynamic_light_surface_container_lowest</item>
        <item name="colorSurfaceDim">@color/m3_sys_color_dynamic_light_surface_dim</item>
        <item name="colorSurfaceInverse">@color/m3_sys_color_dynamic_light_inverse_surface</item>
        <item name="colorSurfaceVariant">@color/m3_sys_color_dynamic_light_surface_variant</item>
        <item name="colorTertiary">@color/m3_sys_color_dynamic_light_tertiary</item>
        <item name="colorTertiaryContainer">@color/m3_sys_color_dynamic_light_tertiary_container</item>
        <item name="colorTertiaryFixed">@color/m3_sys_color_dynamic_tertiary_fixed</item>
        <item name="colorTertiaryFixedDim">@color/m3_sys_color_dynamic_tertiary_fixed_dim</item>
        <item name="isMaterial3DynamicColorApplied">true</item>
    </style>
    <style name="ThemeTrans" parent="@android:style/Theme.Translucent.NoTitleBar">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowFullscreen">true</item>
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    <style name="ThemeTransHot" parent="@android:style/Theme.Translucent.NoTitleBar">
        <item name="android:windowBackground">@drawable/background_launcher</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowAnimationStyle">@style/activityAnimation</item>
        <item name="android:windowFullscreen">true</item>
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    <style name="tpush_notification_large_title">
        <item name="android:textSize">13.0sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/tpush_notification_text_title</item>
        <item name="android:ellipsize">end</item>
        <item name="android:maxLines">1</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:textDirection">locale</item>
    </style>
</resources>
