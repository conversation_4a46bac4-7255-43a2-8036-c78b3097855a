<?xml version="1.0" encoding="utf-8"?>
<resources>
    <attr name="CustomHorizontalMargin" format="dimension" />
    <attr name="CustomIndicatorSize" format="integer" />
    <attr name="CustomSelectedIndex" format="integer" />
    <attr name="PileLayout_pileWidth" format="dimension" />
    <attr name="PileLayout_vertivalSpace" format="dimension" />
    <attr name="SharedValue" format="integer" />
    <attr name="SharedValueId" format="reference" />
    <attr name="action" format="string" />
    <attr name="actionBarDivider" format="reference" />
    <attr name="actionBarItemBackground" format="reference" />
    <attr name="actionBarPopupTheme" format="reference" />
    <attr name="actionBarSize" format="dimension">
        <enum name="wrap_content" value="0" />
    </attr>
    <attr name="actionBarSplitStyle" format="reference" />
    <attr name="actionBarStyle" format="reference" />
    <attr name="actionBarTabBarStyle" format="reference" />
    <attr name="actionBarTabStyle" format="reference" />
    <attr name="actionBarTabTextStyle" format="reference" />
    <attr name="actionBarTheme" format="reference" />
    <attr name="actionBarWidgetTheme" format="reference" />
    <attr name="actionButtonStyle" format="reference" />
    <attr name="actionDropDownStyle" format="reference" />
    <attr name="actionLayout" format="reference" />
    <attr name="actionMenuTextAppearance" format="reference" />
    <attr name="actionMenuTextColor" format="reference|color" />
    <attr name="actionModeBackground" format="reference" />
    <attr name="actionModeCloseButtonStyle" format="reference" />
    <attr name="actionModeCloseContentDescription" format="string" />
    <attr name="actionModeCloseDrawable" format="reference" />
    <attr name="actionModeCopyDrawable" format="reference" />
    <attr name="actionModeCutDrawable" format="reference" />
    <attr name="actionModeFindDrawable" format="reference" />
    <attr name="actionModePasteDrawable" format="reference" />
    <attr name="actionModePopupWindowStyle" format="reference" />
    <attr name="actionModeSelectAllDrawable" format="reference" />
    <attr name="actionModeShareDrawable" format="reference" />
    <attr name="actionModeSplitBackground" format="reference" />
    <attr name="actionModeStyle" format="reference" />
    <attr name="actionModeTheme" format="reference" />
    <attr name="actionModeWebSearchDrawable" format="reference" />
    <attr name="actionOverflowButtonStyle" format="reference" />
    <attr name="actionOverflowMenuStyle" format="reference" />
    <attr name="actionProviderClass" format="string" />
    <attr name="actionTextColorAlpha" format="float" />
    <attr name="actionViewClass" format="string" />
    <attr name="activeIndicatorLabelPadding" format="dimension" />
    <attr name="activityAction" format="string" />
    <attr name="activityChooserViewStyle" format="reference" />
    <attr name="activityName" format="string" />
    <attr name="adSize" format="string" />
    <attr name="adSizes" format="string" />
    <attr name="adUnitId" format="string" />
    <attr name="ad_marker_color" format="color" />
    <attr name="ad_marker_width" format="dimension" />
    <attr name="addElevationShadow" format="boolean" />
    <attr name="adv_layout_empty" format="reference" />
    <attr name="adv_layout_error" format="reference" />
    <attr name="adv_layout_progress" format="reference" />
    <attr name="alertDialogButtonGroupStyle" format="reference" />
    <attr name="alertDialogCenterButtons" format="boolean" />
    <attr name="alertDialogStyle" format="reference" />
    <attr name="alertDialogTheme" format="reference" />
    <attr name="alignContent">
        <enum name="flex_start" value="0" />
        <enum name="flex_end" value="1" />
        <enum name="center" value="2" />
        <enum name="space_between" value="3" />
        <enum name="space_around" value="4" />
        <enum name="stretch" value="5" />
    </attr>
    <attr name="alignItems">
        <enum name="flex_start" value="0" />
        <enum name="flex_end" value="1" />
        <enum name="center" value="2" />
        <enum name="baseline" value="3" />
        <enum name="stretch" value="4" />
    </attr>
    <attr name="allowStacking" format="boolean" />
    <attr name="alpha" format="float" />
    <attr name="alphabeticModifiers">
        <flag name="META" value="0x00010000" />
        <flag name="CTRL" value="0x00001000" />
        <flag name="ALT" value="0x00000002" />
        <flag name="SHIFT" value="0x00000001" />
        <flag name="SYM" value="0x00000004" />
        <flag name="FUNCTION" value="0x00000008" />
    </attr>
    <attr name="altSrc" format="reference" />
    <attr name="alwaysExpand" format="boolean" />
    <attr name="ambientEnabled" format="boolean" />
    <attr name="angle" format="dimension" />
    <attr name="animateCircleAngleTo">
        <enum name="bestChoice" value="0" />
        <enum name="closest" value="1" />
        <enum name="clockwise" value="2" />
        <enum name="antiClockwise" value="3" />
        <enum name="constraint" value="4" />
    </attr>
    <attr name="animateMenuItems" format="boolean" />
    <attr name="animateNavigationIcon" format="boolean" />
    <attr name="animateRelativeTo" format="reference" />
    <attr name="animationMode">
        <enum name="slide" value="0" />
        <enum name="fade" value="1" />
    </attr>
    <attr name="animation_enabled" format="boolean" />
    <attr name="appBarLayoutStyle" format="reference" />
    <attr name="applyMotionScene" format="boolean" />
    <attr name="arcMode">
        <enum name="startVertical" value="0" />
        <enum name="startHorizontal" value="1" />
        <enum name="flip" value="2" />
    </attr>
    <attr name="argType" format="string" />
    <attr name="arrowCenter" format="boolean" />
    <attr name="arrowHeadLength" format="dimension" />
    <attr name="arrowHeight" format="dimension" />
    <attr name="arrowIVHeight" format="dimension" />
    <attr name="arrowIVPositionOffset" format="dimension" />
    <attr name="arrowIVWidth" format="dimension" />
    <attr name="arrowLocation">
        <enum name="left" value="0" />
        <enum name="right" value="1" />
        <enum name="top" value="2" />
        <enum name="bottom" value="3" />
    </attr>
    <attr name="arrowPosition" format="dimension" />
    <attr name="arrowShaftLength" format="dimension" />
    <attr name="arrowWidth" format="dimension" />
    <attr name="artwork_display_mode">
        <enum name="off" value="0" />
        <enum name="fit" value="1" />
        <enum name="fill" value="2" />
    </attr>
    <attr name="arv_connect_tips" format="string" />
    <attr name="attributeName" format="string" />
    <attr name="autoAdjustToWithinGrandparentBounds" format="boolean" />
    <attr name="autoCompleteMode">
        <enum name="continuousVelocity" value="0" />
        <enum name="spring" value="1" />
    </attr>
    <attr name="autoCompleteTextViewStyle" format="reference" />
    <attr name="autoShowKeyboard" format="boolean" />
    <attr name="autoSizeMaxTextSize" format="dimension" />
    <attr name="autoSizeMinTextSize" format="dimension" />
    <attr name="autoSizePresetSizes" format="reference" />
    <attr name="autoSizeStepGranularity" format="dimension" />
    <attr name="autoSizeTextType">
        <enum name="none" value="0" />
        <enum name="uniform" value="1" />
    </attr>
    <attr name="autoTransition">
        <enum name="none" value="0" />
        <enum name="jumpToStart" value="1" />
        <enum name="jumpToEnd" value="2" />
        <enum name="animateToStart" value="3" />
        <enum name="animateToEnd" value="4" />
    </attr>
    <attr name="auto_show" format="boolean" />
    <attr name="backHandlingEnabled" format="boolean" />
    <attr name="backIconRes" format="reference" />
    <attr name="background" format="reference" />
    <attr name="backgroundColor" format="color" />
    <attr name="backgroundInsetBottom" format="dimension" />
    <attr name="backgroundInsetEnd" format="dimension" />
    <attr name="backgroundInsetStart" format="dimension" />
    <attr name="backgroundInsetTop" format="dimension" />
    <attr name="backgroundOverlayColorAlpha" format="float" />
    <attr name="backgroundSplit" format="reference|color" />
    <attr name="backgroundStacked" format="reference|color" />
    <attr name="backgroundTint" format="color" />
    <attr name="backgroundTintMode">
        <enum name="src_over" value="3" />
        <enum name="src_in" value="5" />
        <enum name="src_atop" value="9" />
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
        <enum name="add" value="16" />
    </attr>
    <attr name="badgeGravity">
        <enum name="TOP_END" value="8388661" />
        <enum name="TOP_START" value="8388659" />
        <enum name="BOTTOM_END" value="8388693" />
        <enum name="BOTTOM_START" value="8388691" />
    </attr>
    <attr name="badgeHeight" format="dimension" />
    <attr name="badgeRadius" format="dimension" />
    <attr name="badgeShapeAppearance" format="reference" />
    <attr name="badgeShapeAppearanceOverlay" format="reference" />
    <attr name="badgeStyle" format="reference" />
    <attr name="badgeText" format="string" />
    <attr name="badgeTextAppearance" format="reference" />
    <attr name="badgeTextColor" format="color" />
    <attr name="badgeVerticalPadding" format="dimension" />
    <attr name="badgeWidePadding" format="dimension" />
    <attr name="badgeWidth" format="dimension" />
    <attr name="badgeWithTextHeight" format="dimension" />
    <attr name="badgeWithTextRadius" format="dimension" />
    <attr name="badgeWithTextShapeAppearance" format="reference" />
    <attr name="badgeWithTextShapeAppearanceOverlay" format="reference" />
    <attr name="badgeWithTextWidth" format="dimension" />
    <attr name="banner_auto_loop" format="boolean" />
    <attr name="banner_indicator_gravity">
        <enum name="left" value="0" />
        <enum name="center" value="1" />
        <enum name="right" value="2" />
    </attr>
    <attr name="banner_indicator_height" format="dimension" />
    <attr name="banner_indicator_margin" format="dimension" />
    <attr name="banner_indicator_marginBottom" format="dimension" />
    <attr name="banner_indicator_marginLeft" format="dimension" />
    <attr name="banner_indicator_marginRight" format="dimension" />
    <attr name="banner_indicator_marginTop" format="dimension" />
    <attr name="banner_indicator_normal_color" format="reference|color" />
    <attr name="banner_indicator_normal_width" format="dimension" />
    <attr name="banner_indicator_radius" format="dimension" />
    <attr name="banner_indicator_selected_color" format="reference|color" />
    <attr name="banner_indicator_selected_width" format="dimension" />
    <attr name="banner_indicator_space" format="dimension" />
    <attr name="banner_infinite_loop" format="boolean" />
    <attr name="banner_loop_time" format="integer" />
    <attr name="banner_orientation">
        <enum name="horizontal" value="0" />
        <enum name="vertical" value="1" />
    </attr>
    <attr name="banner_radius" format="dimension" />
    <attr name="banner_round_bottom_left" format="boolean" />
    <attr name="banner_round_bottom_right" format="boolean" />
    <attr name="banner_round_top_left" format="boolean" />
    <attr name="banner_round_top_right" format="boolean" />
    <attr name="banner_viewpager_height" format="dimension" />
    <attr name="barLength" format="dimension" />
    <attr name="bar_gravity">
        <enum name="center" value="0" />
        <enum name="bottom" value="1" />
    </attr>
    <attr name="bar_height" format="dimension" />
    <attr name="barrierAllowsGoneWidgets" format="boolean" />
    <attr name="barrierDirection">
        <enum name="left" value="0" />
        <enum name="right" value="1" />
        <enum name="top" value="2" />
        <enum name="bottom" value="3" />
        <enum name="start" value="5" />
        <enum name="end" value="6" />
    </attr>
    <attr name="barrierMargin" format="dimension" />
    <attr name="base_circleColor" format="color" />
    <attr name="base_insideColor" format="color" />
    <attr name="base_yuanCircleStrokeWidth" format="dimension" />
    <attr name="base_yuanProgressStrokeWidth" format="dimension" />
    <attr name="base_yuan_progressColor" format="color" />
    <attr name="behavior_autoHide" format="boolean" />
    <attr name="behavior_autoShrink" format="boolean" />
    <attr name="behavior_draggable" format="boolean" />
    <attr name="behavior_expandedOffset" format="reference|dimension" />
    <attr name="behavior_fitToContents" format="boolean" />
    <attr name="behavior_halfExpandedRatio" format="reference|float" />
    <attr name="behavior_hideable" format="boolean" />
    <attr name="behavior_overlapTop" format="dimension" />
    <attr name="behavior_peekHeight" format="dimension">
        <enum name="auto" value="-1" />
    </attr>
    <attr name="behavior_saveFlags">
        <flag name="peekHeight" value="0x00000001" />
        <flag name="fitToContents" value="0x00000002" />
        <flag name="hideable" value="0x00000004" />
        <flag name="skipCollapsed" value="0x00000008" />
        <flag name="all" value="0xffffffff" />
        <flag name="none" value="0x00000000" />
    </attr>
    <attr name="behavior_significantVelocityThreshold" format="dimension" />
    <attr name="behavior_skipCollapsed" format="boolean" />
    <attr name="bg_style">
        <enum name="bg_08" value="1" />
        <enum name="main_gradient" value="2" />
    </attr>
    <attr name="bl_activated_textColor" format="color" />
    <attr name="bl_active_textColor" format="color" />
    <attr name="bl_anim_auto_start" format="boolean" />
    <attr name="bl_checkable_drawable" format="reference|color" />
    <attr name="bl_checkable_gradient_angle" format="integer" />
    <attr name="bl_checkable_gradient_centerColor" format="color" />
    <attr name="bl_checkable_gradient_centerX" format="float" />
    <attr name="bl_checkable_gradient_centerY" format="float" />
    <attr name="bl_checkable_gradient_endColor" format="color" />
    <attr name="bl_checkable_gradient_gradientRadius" format="dimension" />
    <attr name="bl_checkable_gradient_startColor" format="color" />
    <attr name="bl_checkable_gradient_type">
        <enum name="linear" value="0" />
        <enum name="radial" value="1" />
        <enum name="sweep" value="2" />
    </attr>
    <attr name="bl_checkable_gradient_useLevel" format="boolean" />
    <attr name="bl_checkable_solid_color" format="color" />
    <attr name="bl_checkable_stroke_color" format="color" />
    <attr name="bl_checkable_textColor" format="color" />
    <attr name="bl_checked_button_drawable" format="reference|color" />
    <attr name="bl_checked_drawable" format="reference|color" />
    <attr name="bl_checked_gradient_angle" format="integer" />
    <attr name="bl_checked_gradient_centerColor" format="color" />
    <attr name="bl_checked_gradient_centerX" format="float" />
    <attr name="bl_checked_gradient_centerY" format="float" />
    <attr name="bl_checked_gradient_endColor" format="color" />
    <attr name="bl_checked_gradient_gradientRadius" format="dimension" />
    <attr name="bl_checked_gradient_startColor" format="color" />
    <attr name="bl_checked_gradient_type">
        <enum name="linear" value="0" />
        <enum name="radial" value="1" />
        <enum name="sweep" value="2" />
    </attr>
    <attr name="bl_checked_gradient_useLevel" format="boolean" />
    <attr name="bl_checked_solid_color" format="color" />
    <attr name="bl_checked_stroke_color" format="color" />
    <attr name="bl_checked_textColor" format="color" />
    <attr name="bl_corners_bottomLeftRadius" format="dimension" />
    <attr name="bl_corners_bottomRadius" format="dimension" />
    <attr name="bl_corners_bottomRightRadius" format="dimension" />
    <attr name="bl_corners_leftRadius" format="dimension" />
    <attr name="bl_corners_radius" format="dimension" />
    <attr name="bl_corners_rightRadius" format="dimension" />
    <attr name="bl_corners_topLeftRadius" format="dimension" />
    <attr name="bl_corners_topRadius" format="dimension" />
    <attr name="bl_corners_topRightRadius" format="dimension" />
    <attr name="bl_duration" format="integer" />
    <attr name="bl_duration_item0" format="integer" />
    <attr name="bl_duration_item1" format="integer" />
    <attr name="bl_duration_item10" format="integer" />
    <attr name="bl_duration_item11" format="integer" />
    <attr name="bl_duration_item12" format="integer" />
    <attr name="bl_duration_item13" format="integer" />
    <attr name="bl_duration_item14" format="integer" />
    <attr name="bl_duration_item2" format="integer" />
    <attr name="bl_duration_item3" format="integer" />
    <attr name="bl_duration_item4" format="integer" />
    <attr name="bl_duration_item5" format="integer" />
    <attr name="bl_duration_item6" format="integer" />
    <attr name="bl_duration_item7" format="integer" />
    <attr name="bl_duration_item8" format="integer" />
    <attr name="bl_duration_item9" format="integer" />
    <attr name="bl_enabled_drawable" format="reference|color" />
    <attr name="bl_enabled_gradient_angle" format="integer" />
    <attr name="bl_enabled_gradient_centerColor" format="color" />
    <attr name="bl_enabled_gradient_centerX" format="float" />
    <attr name="bl_enabled_gradient_centerY" format="float" />
    <attr name="bl_enabled_gradient_endColor" format="color" />
    <attr name="bl_enabled_gradient_gradientRadius" format="dimension" />
    <attr name="bl_enabled_gradient_startColor" format="color" />
    <attr name="bl_enabled_gradient_type">
        <enum name="linear" value="0" />
        <enum name="radial" value="1" />
        <enum name="sweep" value="2" />
    </attr>
    <attr name="bl_enabled_gradient_useLevel" format="boolean" />
    <attr name="bl_enabled_solid_color" format="color" />
    <attr name="bl_enabled_stroke_color" format="color" />
    <attr name="bl_enabled_textColor" format="color" />
    <attr name="bl_expanded_textColor" format="color" />
    <attr name="bl_focused_activated" format="reference|color" />
    <attr name="bl_focused_drawable" format="reference|color" />
    <attr name="bl_focused_gradient_angle" format="integer" />
    <attr name="bl_focused_gradient_centerColor" format="color" />
    <attr name="bl_focused_gradient_centerX" format="float" />
    <attr name="bl_focused_gradient_centerY" format="float" />
    <attr name="bl_focused_gradient_endColor" format="color" />
    <attr name="bl_focused_gradient_gradientRadius" format="dimension" />
    <attr name="bl_focused_gradient_startColor" format="color" />
    <attr name="bl_focused_gradient_type">
        <enum name="linear" value="0" />
        <enum name="radial" value="1" />
        <enum name="sweep" value="2" />
    </attr>
    <attr name="bl_focused_gradient_useLevel" format="boolean" />
    <attr name="bl_focused_hovered" format="reference|color" />
    <attr name="bl_focused_solid_color" format="color" />
    <attr name="bl_focused_stroke_color" format="color" />
    <attr name="bl_focused_textColor" format="color" />
    <attr name="bl_frame_drawable_item0" format="reference" />
    <attr name="bl_frame_drawable_item1" format="reference" />
    <attr name="bl_frame_drawable_item10" format="reference" />
    <attr name="bl_frame_drawable_item11" format="reference" />
    <attr name="bl_frame_drawable_item12" format="reference" />
    <attr name="bl_frame_drawable_item13" format="reference" />
    <attr name="bl_frame_drawable_item14" format="reference" />
    <attr name="bl_frame_drawable_item2" format="reference" />
    <attr name="bl_frame_drawable_item3" format="reference" />
    <attr name="bl_frame_drawable_item4" format="reference" />
    <attr name="bl_frame_drawable_item5" format="reference" />
    <attr name="bl_frame_drawable_item6" format="reference" />
    <attr name="bl_frame_drawable_item7" format="reference" />
    <attr name="bl_frame_drawable_item8" format="reference" />
    <attr name="bl_frame_drawable_item9" format="reference" />
    <attr name="bl_function" format="string" />
    <attr name="bl_gradient_angle" format="integer" />
    <attr name="bl_gradient_centerColor" format="color" />
    <attr name="bl_gradient_centerX" format="float" />
    <attr name="bl_gradient_centerY" format="float" />
    <attr name="bl_gradient_endColor" format="color" />
    <attr name="bl_gradient_gradientRadius" format="dimension" />
    <attr name="bl_gradient_startColor" format="color" />
    <attr name="bl_gradient_type">
        <enum name="linear" value="0" />
        <enum name="radial" value="1" />
        <enum name="sweep" value="2" />
    </attr>
    <attr name="bl_gradient_useLevel" format="boolean" />
    <attr name="bl_multi_selector1" format="string" />
    <attr name="bl_multi_selector2" format="string" />
    <attr name="bl_multi_selector3" format="string" />
    <attr name="bl_multi_selector4" format="string" />
    <attr name="bl_multi_selector5" format="string" />
    <attr name="bl_multi_selector6" format="string" />
    <attr name="bl_multi_text_selector1" format="string" />
    <attr name="bl_multi_text_selector2" format="string" />
    <attr name="bl_multi_text_selector3" format="string" />
    <attr name="bl_multi_text_selector4" format="string" />
    <attr name="bl_multi_text_selector5" format="string" />
    <attr name="bl_multi_text_selector6" format="string" />
    <attr name="bl_oneshot" format="boolean" />
    <attr name="bl_padding_bottom" format="dimension" />
    <attr name="bl_padding_left" format="dimension" />
    <attr name="bl_padding_right" format="dimension" />
    <attr name="bl_padding_top" format="dimension" />
    <attr name="bl_position">
        <enum name="left" value="1" />
        <enum name="top" value="2" />
        <enum name="right" value="4" />
        <enum name="bottom" value="8" />
    </attr>
    <attr name="bl_pressed_color" format="color" />
    <attr name="bl_pressed_drawable" format="reference|color" />
    <attr name="bl_pressed_gradient_angle" format="integer" />
    <attr name="bl_pressed_gradient_centerColor" format="color" />
    <attr name="bl_pressed_gradient_centerX" format="float" />
    <attr name="bl_pressed_gradient_centerY" format="float" />
    <attr name="bl_pressed_gradient_endColor" format="color" />
    <attr name="bl_pressed_gradient_gradientRadius" format="dimension" />
    <attr name="bl_pressed_gradient_startColor" format="color" />
    <attr name="bl_pressed_gradient_type">
        <enum name="linear" value="0" />
        <enum name="radial" value="1" />
        <enum name="sweep" value="2" />
    </attr>
    <attr name="bl_pressed_gradient_useLevel" format="boolean" />
    <attr name="bl_pressed_solid_color" format="color" />
    <attr name="bl_pressed_stroke_color" format="color" />
    <attr name="bl_pressed_textColor" format="color" />
    <attr name="bl_ripple_color" format="color" />
    <attr name="bl_ripple_enable" format="boolean" />
    <attr name="bl_selected_drawable" format="reference|color" />
    <attr name="bl_selected_gradient_angle" format="integer" />
    <attr name="bl_selected_gradient_centerColor" format="color" />
    <attr name="bl_selected_gradient_centerX" format="float" />
    <attr name="bl_selected_gradient_centerY" format="float" />
    <attr name="bl_selected_gradient_endColor" format="color" />
    <attr name="bl_selected_gradient_gradientRadius" format="dimension" />
    <attr name="bl_selected_gradient_startColor" format="color" />
    <attr name="bl_selected_gradient_type">
        <enum name="linear" value="0" />
        <enum name="radial" value="1" />
        <enum name="sweep" value="2" />
    </attr>
    <attr name="bl_selected_gradient_useLevel" format="boolean" />
    <attr name="bl_selected_solid_color" format="color" />
    <attr name="bl_selected_stroke_color" format="color" />
    <attr name="bl_selected_textColor" format="color" />
    <attr name="bl_shape">
        <enum name="rectangle" value="0" />
        <enum name="oval" value="1" />
        <enum name="line" value="2" />
        <enum name="ring" value="3" />
    </attr>
    <attr name="bl_shape_alpha" format="float" />
    <attr name="bl_size_height" format="dimension">
        <enum name="wrap_content" value="-2" />
        <enum name="match_parent" value="-1" />
    </attr>
    <attr name="bl_size_width" format="dimension">
        <enum name="wrap_content" value="-2" />
        <enum name="match_parent" value="-1" />
    </attr>
    <attr name="bl_solid_color" format="color" />
    <attr name="bl_stroke_color" format="color" />
    <attr name="bl_stroke_dashGap" format="dimension" />
    <attr name="bl_stroke_dashWidth" format="dimension" />
    <attr name="bl_stroke_position">
        <flag name="left" value="0x00000002" />
        <flag name="top" value="0x00000004" />
        <flag name="right" value="0x00000008" />
        <flag name="bottom" value="0x00000010" />
    </attr>
    <attr name="bl_stroke_width" format="dimension" />
    <attr name="bl_text_gradient_endColor" format="reference|color" />
    <attr name="bl_text_gradient_orientation">
        <enum name="vertical" value="0" />
        <enum name="horizontal" value="1" />
    </attr>
    <attr name="bl_text_gradient_startColor" format="reference|color" />
    <attr name="bl_unActivated_textColor" format="color" />
    <attr name="bl_unActive_textColor" format="color" />
    <attr name="bl_unCheckable_drawable" format="reference|color" />
    <attr name="bl_unCheckable_gradient_angle" format="integer" />
    <attr name="bl_unCheckable_gradient_centerColor" format="color" />
    <attr name="bl_unCheckable_gradient_centerX" format="float" />
    <attr name="bl_unCheckable_gradient_centerY" format="float" />
    <attr name="bl_unCheckable_gradient_endColor" format="color" />
    <attr name="bl_unCheckable_gradient_gradientRadius" format="dimension" />
    <attr name="bl_unCheckable_gradient_startColor" format="color" />
    <attr name="bl_unCheckable_gradient_type">
        <enum name="linear" value="0" />
        <enum name="radial" value="1" />
        <enum name="sweep" value="2" />
    </attr>
    <attr name="bl_unCheckable_gradient_useLevel" format="boolean" />
    <attr name="bl_unCheckable_solid_color" format="color" />
    <attr name="bl_unCheckable_stroke_color" format="color" />
    <attr name="bl_unCheckable_textColor" format="color" />
    <attr name="bl_unChecked_button_drawable" format="reference|color" />
    <attr name="bl_unChecked_drawable" format="reference|color" />
    <attr name="bl_unChecked_gradient_angle" format="integer" />
    <attr name="bl_unChecked_gradient_centerColor" format="color" />
    <attr name="bl_unChecked_gradient_centerX" format="float" />
    <attr name="bl_unChecked_gradient_centerY" format="float" />
    <attr name="bl_unChecked_gradient_endColor" format="color" />
    <attr name="bl_unChecked_gradient_gradientRadius" format="dimension" />
    <attr name="bl_unChecked_gradient_startColor" format="color" />
    <attr name="bl_unChecked_gradient_type">
        <enum name="linear" value="0" />
        <enum name="radial" value="1" />
        <enum name="sweep" value="2" />
    </attr>
    <attr name="bl_unChecked_gradient_useLevel" format="boolean" />
    <attr name="bl_unChecked_solid_color" format="color" />
    <attr name="bl_unChecked_stroke_color" format="color" />
    <attr name="bl_unChecked_textColor" format="color" />
    <attr name="bl_unEnabled_drawable" format="reference|color" />
    <attr name="bl_unEnabled_gradient_angle" format="integer" />
    <attr name="bl_unEnabled_gradient_centerColor" format="color" />
    <attr name="bl_unEnabled_gradient_centerX" format="float" />
    <attr name="bl_unEnabled_gradient_centerY" format="float" />
    <attr name="bl_unEnabled_gradient_endColor" format="color" />
    <attr name="bl_unEnabled_gradient_gradientRadius" format="dimension" />
    <attr name="bl_unEnabled_gradient_startColor" format="color" />
    <attr name="bl_unEnabled_gradient_type">
        <enum name="linear" value="0" />
        <enum name="radial" value="1" />
        <enum name="sweep" value="2" />
    </attr>
    <attr name="bl_unEnabled_gradient_useLevel" format="boolean" />
    <attr name="bl_unEnabled_solid_color" format="color" />
    <attr name="bl_unEnabled_stroke_color" format="color" />
    <attr name="bl_unEnabled_textColor" format="color" />
    <attr name="bl_unExpanded_textColor" format="color" />
    <attr name="bl_unFocused_activated" format="reference|color" />
    <attr name="bl_unFocused_drawable" format="reference|color" />
    <attr name="bl_unFocused_gradient_angle" format="integer" />
    <attr name="bl_unFocused_gradient_centerColor" format="color" />
    <attr name="bl_unFocused_gradient_centerX" format="float" />
    <attr name="bl_unFocused_gradient_centerY" format="float" />
    <attr name="bl_unFocused_gradient_endColor" format="color" />
    <attr name="bl_unFocused_gradient_gradientRadius" format="dimension" />
    <attr name="bl_unFocused_gradient_startColor" format="color" />
    <attr name="bl_unFocused_gradient_type">
        <enum name="linear" value="0" />
        <enum name="radial" value="1" />
        <enum name="sweep" value="2" />
    </attr>
    <attr name="bl_unFocused_gradient_useLevel" format="boolean" />
    <attr name="bl_unFocused_hovered" format="reference|color" />
    <attr name="bl_unFocused_solid_color" format="color" />
    <attr name="bl_unFocused_stroke_color" format="color" />
    <attr name="bl_unFocused_textColor" format="color" />
    <attr name="bl_unPressed_drawable" format="reference|color" />
    <attr name="bl_unPressed_gradient_angle" format="integer" />
    <attr name="bl_unPressed_gradient_centerColor" format="color" />
    <attr name="bl_unPressed_gradient_centerX" format="float" />
    <attr name="bl_unPressed_gradient_centerY" format="float" />
    <attr name="bl_unPressed_gradient_endColor" format="color" />
    <attr name="bl_unPressed_gradient_gradientRadius" format="dimension" />
    <attr name="bl_unPressed_gradient_startColor" format="color" />
    <attr name="bl_unPressed_gradient_type">
        <enum name="linear" value="0" />
        <enum name="radial" value="1" />
        <enum name="sweep" value="2" />
    </attr>
    <attr name="bl_unPressed_gradient_useLevel" format="boolean" />
    <attr name="bl_unPressed_solid_color" format="color" />
    <attr name="bl_unPressed_stroke_color" format="color" />
    <attr name="bl_unPressed_textColor" format="color" />
    <attr name="bl_unSelected_drawable" format="reference|color" />
    <attr name="bl_unSelected_gradient_angle" format="integer" />
    <attr name="bl_unSelected_gradient_centerColor" format="color" />
    <attr name="bl_unSelected_gradient_centerX" format="float" />
    <attr name="bl_unSelected_gradient_centerY" format="float" />
    <attr name="bl_unSelected_gradient_endColor" format="color" />
    <attr name="bl_unSelected_gradient_gradientRadius" format="dimension" />
    <attr name="bl_unSelected_gradient_startColor" format="color" />
    <attr name="bl_unSelected_gradient_type">
        <enum name="linear" value="0" />
        <enum name="radial" value="1" />
        <enum name="sweep" value="2" />
    </attr>
    <attr name="bl_unSelected_gradient_useLevel" format="boolean" />
    <attr name="bl_unSelected_solid_color" format="color" />
    <attr name="bl_unSelected_stroke_color" format="color" />
    <attr name="bl_unSelected_textColor" format="color" />
    <attr name="bl_unpressed_color" format="color" />
    <attr name="blendSrc" format="reference" />
    <attr name="borderColorYL" format="color" />
    <attr name="borderRound" format="dimension" />
    <attr name="borderRoundPercent" format="float" />
    <attr name="borderSpaceYL" format="dimension" />
    <attr name="borderViewEndColor" format="color" />
    <attr name="borderViewStartColor" format="color" />
    <attr name="borderWidth" format="dimension" />
    <attr name="borderWidthYL" format="dimension" />
    <attr name="border_radius" format="dimension" />
    <attr name="border_width" format="dimension" />
    <attr name="borderlessButtonStyle" format="reference" />
    <attr name="bottomAppBarStyle" format="reference" />
    <attr name="bottomInsetScrimEnabled" format="boolean" />
    <attr name="bottomLeftCornerRadius" format="dimension" />
    <attr name="bottomLeftRadius" format="dimension" />
    <attr name="bottomLeftRadiusYL" format="dimension" />
    <attr name="bottomLeftRadius_xYL" format="dimension" />
    <attr name="bottomLeftRadius_yYL" format="dimension" />
    <attr name="bottomNavigationStyle" format="reference" />
    <attr name="bottomRightCornerRadius" format="dimension" />
    <attr name="bottomRightRadius" format="dimension" />
    <attr name="bottomRightRadiusYL" format="dimension" />
    <attr name="bottomRightRadius_xYL" format="dimension" />
    <attr name="bottomRightRadius_yYL" format="dimension" />
    <attr name="bottomSheetDialogTheme" format="reference" />
    <attr name="bottomSheetDragHandleStyle" format="reference" />
    <attr name="bottomSheetStyle" format="reference" />
    <attr name="boxBackgroundColor" format="color" />
    <attr name="boxBackgroundMode">
        <enum name="none" value="0" />
        <enum name="filled" value="1" />
        <enum name="outline" value="2" />
    </attr>
    <attr name="boxCollapsedPaddingTop" format="dimension" />
    <attr name="boxCornerRadiusBottomEnd" format="dimension" />
    <attr name="boxCornerRadiusBottomStart" format="dimension" />
    <attr name="boxCornerRadiusTopEnd" format="dimension" />
    <attr name="boxCornerRadiusTopStart" format="dimension" />
    <attr name="boxStrokeColor" format="color" />
    <attr name="boxStrokeErrorColor" format="color" />
    <attr name="boxStrokeWidth" format="dimension" />
    <attr name="boxStrokeWidthFocused" format="dimension" />
    <attr name="brightness" format="float" />
    <attr name="bubbleAlpha" format="integer" />
    <attr name="bubbleColor" format="color" />
    <attr name="buffered_color" format="color" />
    <attr name="buttonBarButtonStyle" format="reference" />
    <attr name="buttonBarNegativeButtonStyle" format="reference" />
    <attr name="buttonBarNeutralButtonStyle" format="reference" />
    <attr name="buttonBarPositiveButtonStyle" format="reference" />
    <attr name="buttonBarStyle" format="reference" />
    <attr name="buttonCompat" format="reference" />
    <attr name="buttonGravity">
        <flag name="center_vertical" value="0x00000010" />
        <flag name="top" value="0x00000030" />
        <flag name="bottom" value="0x00000050" />
    </attr>
    <attr name="buttonIcon" format="reference" />
    <attr name="buttonIconDimen" format="dimension" />
    <attr name="buttonIconTint" format="reference|color" />
    <attr name="buttonIconTintMode">
        <enum name="src_over" value="3" />
        <enum name="src_in" value="5" />
        <enum name="src_atop" value="9" />
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
        <enum name="add" value="16" />
    </attr>
    <attr name="buttonPanelSideLayout" format="reference" />
    <attr name="buttonSize" format="reference">
        <enum name="standard" value="0" />
        <enum name="wide" value="1" />
        <enum name="icon_only" value="2" />
    </attr>
    <attr name="buttonStyle" format="reference" />
    <attr name="buttonStyleSmall" format="reference" />
    <attr name="buttonTint" format="color" />
    <attr name="buttonTintMode">
        <enum name="src_over" value="3" />
        <enum name="src_in" value="5" />
        <enum name="src_atop" value="9" />
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
        <enum name="add" value="16" />
    </attr>
    <attr name="cameraBearing" format="float" />
    <attr name="cameraMaxZoomPreference" format="float" />
    <attr name="cameraMinZoomPreference" format="float" />
    <attr name="cameraTargetLat" format="float" />
    <attr name="cameraTargetLng" format="float" />
    <attr name="cameraTilt" format="float" />
    <attr name="cameraZoom" format="float" />
    <attr name="cardBackgroundColor" format="color" />
    <attr name="cardCornerRadius" format="dimension" />
    <attr name="cardElevation" format="dimension" />
    <attr name="cardForegroundColor" format="color" />
    <attr name="cardMaxElevation" format="dimension" />
    <attr name="cardPreventCornerOverlap" format="boolean" />
    <attr name="cardUseCompatPadding" format="boolean" />
    <attr name="cardViewStyle" format="reference" />
    <attr name="carousel_alignment">
        <enum name="start" value="0" />
        <enum name="center" value="1" />
    </attr>
    <attr name="carousel_backwardTransition" format="reference" />
    <attr name="carousel_emptyViewsBehavior">
        <enum name="invisible" value="4" />
        <enum name="gone" value="8" />
    </attr>
    <attr name="carousel_firstView" format="reference" />
    <attr name="carousel_forwardTransition" format="reference" />
    <attr name="carousel_infinite" format="boolean" />
    <attr name="carousel_nextState" format="reference" />
    <attr name="carousel_previousState" format="reference" />
    <attr name="carousel_touchUpMode">
        <enum name="immediateStop" value="1" />
        <enum name="carryVelocity" value="2" />
    </attr>
    <attr name="carousel_touchUp_dampeningFactor" format="float" />
    <attr name="carousel_touchUp_velocityThreshold" format="float" />
    <attr name="centerIfNoTextEnabled" format="boolean" />
    <attr name="chainUseRtl" format="boolean" />
    <attr name="checkMarkCompat" format="reference" />
    <attr name="checkMarkTint" format="color" />
    <attr name="checkMarkTintMode">
        <enum name="src_over" value="3" />
        <enum name="src_in" value="5" />
        <enum name="src_atop" value="9" />
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
        <enum name="add" value="16" />
    </attr>
    <attr name="checkboxStyle" format="reference" />
    <attr name="checkedButton" format="reference" />
    <attr name="checkedChip" format="reference" />
    <attr name="checkedIcon" format="reference" />
    <attr name="checkedIconEnabled" format="boolean" />
    <attr name="checkedIconGravity">
        <enum name="TOP_END" value="8388661" />
        <enum name="TOP_START" value="8388659" />
        <enum name="BOTTOM_END" value="8388693" />
        <enum name="BOTTOM_START" value="8388691" />
    </attr>
    <attr name="checkedIconMargin" format="dimension" />
    <attr name="checkedIconSize" format="dimension" />
    <attr name="checkedIconTint" format="color" />
    <attr name="checkedIconVisible" format="boolean" />
    <attr name="checkedState">
        <enum name="unchecked" value="0" />
        <enum name="checked" value="1" />
        <enum name="indeterminate" value="2" />
    </attr>
    <attr name="checkedTextViewStyle" format="reference" />
    <attr name="chipBackgroundColor" format="color" />
    <attr name="chipCornerRadius" format="dimension" />
    <attr name="chipEndPadding" format="dimension" />
    <attr name="chipGroupStyle" format="reference" />
    <attr name="chipIcon" format="reference" />
    <attr name="chipIconEnabled" format="boolean" />
    <attr name="chipIconSize" format="dimension" />
    <attr name="chipIconTint" format="color" />
    <attr name="chipIconVisible" format="boolean" />
    <attr name="chipMinHeight" format="dimension" />
    <attr name="chipMinTouchTargetSize" format="dimension" />
    <attr name="chipSpacing" format="dimension" />
    <attr name="chipSpacingHorizontal" format="dimension" />
    <attr name="chipSpacingVertical" format="dimension" />
    <attr name="chipStandaloneStyle" format="reference" />
    <attr name="chipStartPadding" format="dimension" />
    <attr name="chipStrokeColor" format="color" />
    <attr name="chipStrokeWidth" format="dimension" />
    <attr name="chipStyle" format="reference" />
    <attr name="chipSurfaceColor" format="color" />
    <attr name="circleCrop" format="boolean" />
    <attr name="circleRadius" format="dimension" />
    <attr name="circularProgressIndicatorStyle" format="reference" />
    <attr name="circularflow_angles" format="string" />
    <attr name="circularflow_defaultAngle" format="float" />
    <attr name="circularflow_defaultRadius" format="dimension" />
    <attr name="circularflow_radiusInDP" format="string" />
    <attr name="circularflow_viewCenter" format="reference" />
    <attr name="civ_activeEndLayout" format="reference" />
    <attr name="civ_activeItemLayout" format="reference" />
    <attr name="civ_doneEndLayout" format="reference" />
    <attr name="civ_doneItemLayout" format="reference" />
    <attr name="civ_endSpanCount" format="integer" />
    <attr name="civ_horizontalSpace" format="dimension" />
    <attr name="civ_length" format="integer" />
    <attr name="civ_spanCount" format="integer" />
    <attr name="civ_unActiveEndLayout" format="reference" />
    <attr name="civ_unActiveItemLayout" format="reference" />
    <attr name="civ_verticalSpace" format="dimension" />
    <attr name="clearTop" format="boolean" />
    <attr name="clearsTag" format="reference" />
    <attr name="clickAction">
        <flag name="toggle" value="0x00000011" />
        <flag name="transitionToEnd" value="0x00000001" />
        <flag name="transitionToStart" value="0x00000010" />
        <flag name="jumpToEnd" value="0x00000100" />
        <flag name="jumpToStart" value="0x00001000" />
    </attr>
    <attr name="clockFaceBackgroundColor" format="color" />
    <attr name="clockHandColor" format="color" />
    <attr name="clockIcon" format="reference" />
    <attr name="clockNumberTextColor" format="color" />
    <attr name="closeIcon" format="reference" />
    <attr name="closeIconEnabled" format="boolean" />
    <attr name="closeIconEndPadding" format="dimension" />
    <attr name="closeIconSize" format="dimension" />
    <attr name="closeIconStartPadding" format="dimension" />
    <attr name="closeIconTint" format="color" />
    <attr name="closeIconVisible" format="boolean" />
    <attr name="closeItemLayout" format="reference" />
    <attr name="collapseContentDescription" format="string" />
    <attr name="collapseIcon" format="reference" />
    <attr name="collapsedSize" format="dimension" />
    <attr name="collapsedTitleGravity">
        <flag name="top" value="0x00000030" />
        <flag name="bottom" value="0x00000050" />
        <flag name="left" value="0x00000003" />
        <flag name="right" value="0x00000005" />
        <flag name="center_vertical" value="0x00000010" />
        <flag name="fill_vertical" value="0x00000070" />
        <flag name="center_horizontal" value="0x00000001" />
        <flag name="center" value="0x00000011" />
        <flag name="start" value="0x00800003" />
        <flag name="end" value="0x00800005" />
    </attr>
    <attr name="collapsedTitleTextAppearance" format="reference" />
    <attr name="collapsedTitleTextColor" format="reference|color" />
    <attr name="collapsingToolbarLayoutLargeSize" format="reference" />
    <attr name="collapsingToolbarLayoutLargeStyle" format="reference" />
    <attr name="collapsingToolbarLayoutMediumSize" format="reference" />
    <attr name="collapsingToolbarLayoutMediumStyle" format="reference" />
    <attr name="collapsingToolbarLayoutStyle" format="reference" />
    <attr name="color" format="color" />
    <attr name="colorAccent" format="color" />
    <attr name="colorBackgroundFloating" format="color" />
    <attr name="colorButtonNormal" format="color" />
    <attr name="colorContainer" format="color" />
    <attr name="colorControlActivated" format="color" />
    <attr name="colorControlHighlight" format="color" />
    <attr name="colorControlNormal" format="color" />
    <attr name="colorError" format="reference|color" />
    <attr name="colorErrorContainer" format="color" />
    <attr name="colorOnBackground" format="reference|string|integer|boolean|color|float|dimension|fraction" />
    <attr name="colorOnContainer" format="color" />
    <attr name="colorOnContainerUnchecked" format="color" />
    <attr name="colorOnError" format="color" />
    <attr name="colorOnErrorContainer" format="color" />
    <attr name="colorOnPrimary" format="color" />
    <attr name="colorOnPrimaryContainer" format="color" />
    <attr name="colorOnPrimaryFixed" format="color" />
    <attr name="colorOnPrimaryFixedVariant" format="color" />
    <attr name="colorOnPrimarySurface" format="color" />
    <attr name="colorOnSecondary" format="color" />
    <attr name="colorOnSecondaryContainer" format="color" />
    <attr name="colorOnSecondaryFixed" format="color" />
    <attr name="colorOnSecondaryFixedVariant" format="color" />
    <attr name="colorOnSurface" format="color" />
    <attr name="colorOnSurfaceInverse" format="color" />
    <attr name="colorOnSurfaceVariant" format="color" />
    <attr name="colorOnTertiary" format="color" />
    <attr name="colorOnTertiaryContainer" format="color" />
    <attr name="colorOnTertiaryFixed" format="color" />
    <attr name="colorOnTertiaryFixedVariant" format="color" />
    <attr name="colorOutline" format="color" />
    <attr name="colorOutlineVariant" format="color" />
    <attr name="colorPrimary" format="color" />
    <attr name="colorPrimaryContainer" format="color" />
    <attr name="colorPrimaryDark" format="color" />
    <attr name="colorPrimaryFixed" format="color" />
    <attr name="colorPrimaryFixedDim" format="color" />
    <attr name="colorPrimaryInverse" format="color" />
    <attr name="colorPrimarySurface" format="color" />
    <attr name="colorPrimaryVariant" format="color" />
    <attr name="colorScheme" format="reference">
        <enum name="dark" value="0" />
        <enum name="light" value="1" />
        <enum name="auto" value="2" />
    </attr>
    <attr name="colorSecondary" format="color" />
    <attr name="colorSecondaryContainer" format="color" />
    <attr name="colorSecondaryFixed" format="color" />
    <attr name="colorSecondaryFixedDim" format="color" />
    <attr name="colorSecondaryVariant" format="color" />
    <attr name="colorSurface" format="color" />
    <attr name="colorSurfaceBright" format="color" />
    <attr name="colorSurfaceContainer" format="color" />
    <attr name="colorSurfaceContainerHigh" format="color" />
    <attr name="colorSurfaceContainerHighest" format="color" />
    <attr name="colorSurfaceContainerLow" format="color" />
    <attr name="colorSurfaceContainerLowest" format="color" />
    <attr name="colorSurfaceDim" format="color" />
    <attr name="colorSurfaceInverse" format="color" />
    <attr name="colorSurfaceVariant" format="color" />
    <attr name="colorSwitchThumbNormal" format="color" />
    <attr name="colorTertiary" format="color" />
    <attr name="colorTertiaryContainer" format="color" />
    <attr name="colorTertiaryFixed" format="color" />
    <attr name="colorTertiaryFixedDim" format="color" />
    <attr name="commitIcon" format="reference" />
    <attr name="compatShadowEnabled" format="boolean" />
    <attr name="constraintRotate">
        <enum name="none" value="0" />
        <enum name="right" value="1" />
        <enum name="left" value="2" />
        <enum name="x_right" value="3" />
        <enum name="x_left" value="4" />
    </attr>
    <attr name="constraintSet" format="reference" />
    <attr name="constraintSetEnd" format="reference" />
    <attr name="constraintSetStart" format="reference" />
    <attr name="constraint_referenced_ids" format="string" />
    <attr name="constraint_referenced_tags" format="string" />
    <attr name="constraints" format="reference" />
    <attr name="content" format="reference" />
    <attr name="contentDescription" format="string" />
    <attr name="contentInsetEnd" format="dimension" />
    <attr name="contentInsetEndWithActions" format="dimension" />
    <attr name="contentInsetLeft" format="dimension" />
    <attr name="contentInsetRight" format="dimension" />
    <attr name="contentInsetStart" format="dimension" />
    <attr name="contentInsetStartWithNavigation" format="dimension" />
    <attr name="contentPadding" format="dimension" />
    <attr name="contentPaddingBottom" format="dimension" />
    <attr name="contentPaddingEnd" format="dimension" />
    <attr name="contentPaddingLeft" format="dimension" />
    <attr name="contentPaddingRight" format="dimension" />
    <attr name="contentPaddingStart" format="dimension" />
    <attr name="contentPaddingTop" format="dimension" />
    <attr name="contentScrim" format="color" />
    <attr name="contrast" format="float" />
    <attr name="controlBackground" format="reference" />
    <attr name="controller_layout_id" format="reference" />
    <attr name="coordinatorLayoutStyle" format="reference" />
    <attr name="coplanarSiblingViewId" format="reference" />
    <attr name="corner" format="dimension" />
    <attr name="cornerColor" format="color" />
    <attr name="cornerFamily">
        <enum name="rounded" value="0" />
        <enum name="cut" value="1" />
    </attr>
    <attr name="cornerFamilyBottomLeft">
        <enum name="rounded" value="0" />
        <enum name="cut" value="1" />
    </attr>
    <attr name="cornerFamilyBottomRight">
        <enum name="rounded" value="0" />
        <enum name="cut" value="1" />
    </attr>
    <attr name="cornerFamilyTopLeft">
        <enum name="rounded" value="0" />
        <enum name="cut" value="1" />
    </attr>
    <attr name="cornerFamilyTopRight">
        <enum name="rounded" value="0" />
        <enum name="cut" value="1" />
    </attr>
    <attr name="cornerRadius" format="dimension" />
    <attr name="cornerRadiusBottomLeft" format="dimension" />
    <attr name="cornerRadiusBottomRight" format="dimension" />
    <attr name="cornerRadiusTopLeft" format="dimension" />
    <attr name="cornerRadiusTopRight" format="dimension" />
    <attr name="cornerSize" format="dimension|fraction" />
    <attr name="cornerSizeBottomLeft" format="dimension|fraction" />
    <attr name="cornerSizeBottomRight" format="dimension|fraction" />
    <attr name="cornerSizeTopLeft" format="dimension|fraction" />
    <attr name="cornerSizeTopRight" format="dimension|fraction" />
    <attr name="counterEnabled" format="boolean" />
    <attr name="counterMaxLength" format="integer" />
    <attr name="counterOverflowTextAppearance" format="reference" />
    <attr name="counterOverflowTextColor" format="reference" />
    <attr name="counterTextAppearance" format="reference" />
    <attr name="counterTextColor" format="reference" />
    <attr name="crossfade" format="float" />
    <attr name="currentState" format="reference" />
    <attr name="cursorColor" format="color" />
    <attr name="cursorErrorColor" format="color" />
    <attr name="curveFit">
        <enum name="spline" value="0" />
        <enum name="linear" value="1" />
    </attr>
    <attr name="customBoolean" format="boolean" />
    <attr name="customColorDrawableValue" format="color" />
    <attr name="customColorValue" format="color" />
    <attr name="customDimension" format="dimension" />
    <attr name="customFloatValue" format="float" />
    <attr name="customIntegerValue" format="integer" />
    <attr name="customNavigationLayout" format="reference" />
    <attr name="customPixelDimension" format="dimension" />
    <attr name="customReference" format="reference" />
    <attr name="customStringValue" format="string" />
    <attr name="data" format="string" />
    <attr name="dataPattern" format="string" />
    <attr name="dayInvalidStyle" format="reference" />
    <attr name="daySelectedStyle" format="reference" />
    <attr name="dayStyle" format="reference" />
    <attr name="dayTodayStyle" format="reference" />
    <attr name="defaultDuration" format="integer" />
    <attr name="defaultMarginsEnabled" format="boolean" />
    <attr name="defaultNavHost" format="boolean" />
    <attr name="defaultQueryHint" format="string" />
    <attr name="defaultScrollFlagsEnabled" format="boolean" />
    <attr name="defaultState" format="reference" />
    <attr name="default_artwork" format="reference" />
    <attr name="deltaPolarAngle" format="float" />
    <attr name="deltaPolarRadius" format="float" />
    <attr name="deriveConstraintsFrom" format="reference" />
    <attr name="descText" format="string" />
    <attr name="descTextColor" format="color" />
    <attr name="descTextSize" format="dimension" />
    <attr name="destination" format="reference" />
    <attr name="dialogCornerRadius" format="dimension" />
    <attr name="dialogPreferredPadding" format="dimension" />
    <attr name="dialogTheme" format="reference" />
    <attr name="displayOptions">
        <flag name="none" value="0x00000000" />
        <flag name="useLogo" value="0x00000001" />
        <flag name="showHome" value="0x00000002" />
        <flag name="homeAsUp" value="0x00000004" />
        <flag name="showTitle" value="0x00000008" />
        <flag name="showCustom" value="0x00000010" />
        <flag name="disableHome" value="0x00000020" />
    </attr>
    <attr name="divider" format="reference" />
    <attr name="dividerColor" format="reference|color" />
    <attr name="dividerDrawable" format="reference" />
    <attr name="dividerDrawableHorizontal" format="reference" />
    <attr name="dividerDrawableVertical" format="reference" />
    <attr name="dividerHorizontal" format="reference" />
    <attr name="dividerInsetEnd" format="dimension" />
    <attr name="dividerInsetStart" format="dimension" />
    <attr name="dividerPadding" format="dimension" />
    <attr name="dividerThickness" format="dimension" />
    <attr name="dividerVertical" format="reference" />
    <attr name="dragDirection">
        <enum name="dragUp" value="0" />
        <enum name="dragDown" value="1" />
        <enum name="dragLeft" value="2" />
        <enum name="dragRight" value="3" />
        <enum name="dragStart" value="4" />
        <enum name="dragEnd" value="5" />
        <enum name="dragClockwise" value="6" />
        <enum name="dragAnticlockwise" value="7" />
    </attr>
    <attr name="dragScale" format="float" />
    <attr name="dragThreshold" format="float" />
    <attr name="drawPath">
        <enum name="none" value="0" />
        <enum name="path" value="1" />
        <enum name="pathRelative" value="2" />
        <enum name="deltaRelative" value="3" />
        <enum name="asConfigured" value="4" />
        <enum name="rectangles" value="5" />
    </attr>
    <attr name="drawableBottomCompat" format="reference" />
    <attr name="drawableEndCompat" format="reference" />
    <attr name="drawableLeftCompat" format="reference" />
    <attr name="drawableRightCompat" format="reference" />
    <attr name="drawableSize" format="dimension" />
    <attr name="drawableStartCompat" format="reference" />
    <attr name="drawableTint" format="color" />
    <attr name="drawableTintMode">
        <enum name="src_over" value="3" />
        <enum name="src_in" value="5" />
        <enum name="src_atop" value="9" />
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
        <enum name="add" value="16" />
    </attr>
    <attr name="drawableTopCompat" format="reference" />
    <attr name="drawerArrowStyle" format="reference" />
    <attr name="drawerLayoutCornerSize" format="dimension" />
    <attr name="drawerLayoutStyle" format="reference" />
    <attr name="dropDownBackgroundTint" format="color" />
    <attr name="dropDownListViewStyle" format="reference" />
    <attr name="dropdownListPreferredItemHeight" format="dimension" />
    <attr name="duration" format="integer" />
    <attr name="dynamicColorThemeOverlay" format="reference" />
    <attr name="editTextBackground" format="reference" />
    <attr name="editTextColor" format="reference|color" />
    <attr name="editTextStyle" format="reference" />
    <attr name="elevation" format="dimension" />
    <attr name="elevationOverlayAccentColor" format="color" />
    <attr name="elevationOverlayColor" format="color" />
    <attr name="elevationOverlayEnabled" format="boolean" />
    <attr name="emojiCompatEnabled" format="boolean" />
    <attr name="empty_icon" format="reference" />
    <attr name="empty_icon_margin_bottom" format="dimension" />
    <attr name="empty_icon_margin_top" format="dimension" />
    <attr name="empty_refresh_height" format="dimension" />
    <attr name="empty_refresh_icon" format="reference" />
    <attr name="empty_refresh_icon_margin_end" format="dimension" />
    <attr name="empty_refresh_text" format="reference|string" />
    <attr name="empty_refresh_text_color" format="color" />
    <attr name="empty_refresh_text_size" format="dimension" />
    <attr name="empty_refresh_width" format="dimension" />
    <attr name="empty_text" format="reference|string" />
    <attr name="empty_text_color" format="color" />
    <attr name="empty_text_margin_bottom" format="dimension" />
    <attr name="empty_text_margin_top" format="dimension" />
    <attr name="empty_text_size" format="dimension" />
    <attr name="empty_type" format="integer">
        <enum name="A_full" value="0" />
        <enum name="B_half" value="1" />
    </attr>
    <attr name="enableEdgeToEdge" format="boolean" />
    <attr name="enable_click" format="boolean" />
    <attr name="endColor" format="color" />
    <attr name="endIconCheckable" format="boolean" />
    <attr name="endIconContentDescription" format="string" />
    <attr name="endIconDrawable" format="reference" />
    <attr name="endIconMinSize" format="dimension" />
    <attr name="endIconMode">
        <enum name="custom" value="-1" />
        <enum name="none" value="0" />
        <enum name="password_toggle" value="1" />
        <enum name="clear_text" value="2" />
        <enum name="dropdown_menu" value="3" />
    </attr>
    <attr name="endIconScaleType">
        <enum name="fitXY" value="0" />
        <enum name="fitStart" value="1" />
        <enum name="fitCenter" value="2" />
        <enum name="fitEnd" value="3" />
        <enum name="center" value="4" />
        <enum name="centerCrop" value="5" />
        <enum name="centerInside" value="6" />
    </attr>
    <attr name="endIconTint" format="color" />
    <attr name="endIconTintMode">
        <enum name="src_over" value="3" />
        <enum name="src_in" value="5" />
        <enum name="src_atop" value="9" />
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
    </attr>
    <attr name="enforceMaterialTheme" format="boolean" />
    <attr name="enforceTextAppearance" format="boolean" />
    <attr name="ensureMinTouchTargetSize" format="boolean" />
    <attr name="enterAnim" format="reference" />
    <attr name="errorAccessibilityLabel" format="string" />
    <attr name="errorAccessibilityLiveRegion" format="integer" />
    <attr name="errorContentDescription" format="string" />
    <attr name="errorEnabled" format="boolean" />
    <attr name="errorIconDrawable" format="reference" />
    <attr name="errorIconTint" format="reference" />
    <attr name="errorIconTintMode">
        <enum name="src_over" value="3" />
        <enum name="src_in" value="5" />
        <enum name="src_atop" value="9" />
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
    </attr>
    <attr name="errorShown" format="boolean" />
    <attr name="errorTextAppearance" format="reference" />
    <attr name="errorTextColor" format="color" />
    <attr name="et_clear_ic" format="reference|color" />
    <attr name="et_close_withoutfocus" format="boolean" />
    <attr name="exitAnim" format="reference" />
    <attr name="expandActivityOverflowButtonDrawable" format="reference" />
    <attr name="expanded" format="boolean" />
    <attr name="expandedHintEnabled" format="boolean" />
    <attr name="expandedTitleGravity">
        <flag name="top" value="0x00000030" />
        <flag name="bottom" value="0x00000050" />
        <flag name="left" value="0x00000003" />
        <flag name="right" value="0x00000005" />
        <flag name="center_vertical" value="0x00000010" />
        <flag name="fill_vertical" value="0x00000070" />
        <flag name="center_horizontal" value="0x00000001" />
        <flag name="center" value="0x00000011" />
        <flag name="start" value="0x00800003" />
        <flag name="end" value="0x00800005" />
    </attr>
    <attr name="expandedTitleMargin" format="dimension" />
    <attr name="expandedTitleMarginBottom" format="dimension" />
    <attr name="expandedTitleMarginEnd" format="dimension" />
    <attr name="expandedTitleMarginStart" format="dimension" />
    <attr name="expandedTitleMarginTop" format="dimension" />
    <attr name="expandedTitleTextAppearance" format="reference" />
    <attr name="expandedTitleTextColor" format="reference|color" />
    <attr name="extendMotionSpec" format="reference" />
    <attr name="extendStrategy">
        <enum name="auto" value="0" />
        <enum name="wrap_content" value="1" />
        <enum name="match_parent" value="2" />
    </attr>
    <attr name="extendedFloatingActionButtonPrimaryStyle" format="reference" />
    <attr name="extendedFloatingActionButtonSecondaryStyle" format="reference" />
    <attr name="extendedFloatingActionButtonStyle" format="reference" />
    <attr name="extendedFloatingActionButtonSurfaceStyle" format="reference" />
    <attr name="extendedFloatingActionButtonTertiaryStyle" format="reference" />
    <attr name="extraMultilineHeightEnabled" format="boolean" />
    <attr name="fabAlignmentMode">
        <enum name="center" value="0" />
        <enum name="end" value="1" />
    </attr>
    <attr name="fabAlignmentModeEndMargin" format="dimension" />
    <attr name="fabAnchorMode">
        <enum name="embed" value="0" />
        <enum name="cradle" value="1" />
    </attr>
    <attr name="fabAnimationMode">
        <enum name="scale" value="0" />
        <enum name="slide" value="1" />
    </attr>
    <attr name="fabCradleMargin" format="dimension" />
    <attr name="fabCradleRoundedCornerRadius" format="dimension" />
    <attr name="fabCradleVerticalOffset" format="dimension" />
    <attr name="fabCustomSize" format="dimension" />
    <attr name="fabSize">
        <enum name="auto" value="-1" />
        <enum name="normal" value="0" />
        <enum name="mini" value="1" />
    </attr>
    <attr name="fastScrollEnabled" format="boolean" />
    <attr name="fastScrollHorizontalThumbDrawable" format="reference" />
    <attr name="fastScrollHorizontalTrackDrawable" format="reference" />
    <attr name="fastScrollVerticalThumbDrawable" format="reference" />
    <attr name="fastScrollVerticalTrackDrawable" format="reference" />
    <attr name="finishPrimaryWithSecondary" format="boolean" />
    <attr name="finishSecondaryWithPrimary" format="boolean" />
    <attr name="firstBaselineToTopHeight" format="dimension" />
    <attr name="flChildSpacing" format="dimension">
        <enum name="auto" value="-65536" />
    </attr>
    <attr name="flChildSpacingForLastRow" format="dimension">
        <enum name="auto" value="-65536" />
        <enum name="align" value="-65537" />
    </attr>
    <attr name="flFlow" format="boolean" />
    <attr name="flMaxRows" format="integer" />
    <attr name="flMinChildSpacing" format="dimension" />
    <attr name="flRowSpacing" format="dimension">
        <enum name="auto" value="-65536" />
    </attr>
    <attr name="flRowVerticalGravity">
        <enum name="auto" value="-65536" />
        <enum name="top" value="48" />
        <enum name="center" value="16" />
        <enum name="bottom" value="80" />
    </attr>
    <attr name="flRtl" format="boolean" />
    <attr name="flexDirection">
        <enum name="row" value="0" />
        <enum name="row_reverse" value="1" />
        <enum name="column" value="2" />
        <enum name="column_reverse" value="3" />
    </attr>
    <attr name="flexWrap">
        <enum name="nowrap" value="0" />
        <enum name="wrap" value="1" />
        <enum name="wrap_reverse" value="2" />
    </attr>
    <attr name="floatingActionButtonLargePrimaryStyle" format="reference" />
    <attr name="floatingActionButtonLargeSecondaryStyle" format="reference" />
    <attr name="floatingActionButtonLargeStyle" format="reference" />
    <attr name="floatingActionButtonLargeSurfaceStyle" format="reference" />
    <attr name="floatingActionButtonLargeTertiaryStyle" format="reference" />
    <attr name="floatingActionButtonPrimaryStyle" format="reference" />
    <attr name="floatingActionButtonSecondaryStyle" format="reference" />
    <attr name="floatingActionButtonSmallPrimaryStyle" format="reference" />
    <attr name="floatingActionButtonSmallSecondaryStyle" format="reference" />
    <attr name="floatingActionButtonSmallStyle" format="reference" />
    <attr name="floatingActionButtonSmallSurfaceStyle" format="reference" />
    <attr name="floatingActionButtonSmallTertiaryStyle" format="reference" />
    <attr name="floatingActionButtonStyle" format="reference" />
    <attr name="floatingActionButtonSurfaceStyle" format="reference" />
    <attr name="floatingActionButtonTertiaryStyle" format="reference" />
    <attr name="flow_firstHorizontalBias" format="float" />
    <attr name="flow_firstHorizontalStyle">
        <enum name="spread" value="0" />
        <enum name="spread_inside" value="1" />
        <enum name="packed" value="2" />
    </attr>
    <attr name="flow_firstVerticalBias" format="float" />
    <attr name="flow_firstVerticalStyle">
        <enum name="spread" value="0" />
        <enum name="spread_inside" value="1" />
        <enum name="packed" value="2" />
    </attr>
    <attr name="flow_horizontalAlign">
        <enum name="start" value="0" />
        <enum name="end" value="1" />
        <enum name="center" value="2" />
    </attr>
    <attr name="flow_horizontalBias" format="float" />
    <attr name="flow_horizontalGap" format="dimension" />
    <attr name="flow_horizontalStyle">
        <enum name="spread" value="0" />
        <enum name="spread_inside" value="1" />
        <enum name="packed" value="2" />
    </attr>
    <attr name="flow_lastHorizontalBias" format="float" />
    <attr name="flow_lastHorizontalStyle">
        <enum name="spread" value="0" />
        <enum name="spread_inside" value="1" />
        <enum name="packed" value="2" />
    </attr>
    <attr name="flow_lastVerticalBias" format="float" />
    <attr name="flow_lastVerticalStyle">
        <enum name="spread" value="0" />
        <enum name="spread_inside" value="1" />
        <enum name="packed" value="2" />
    </attr>
    <attr name="flow_maxElementsWrap" format="integer" />
    <attr name="flow_padding" format="dimension" />
    <attr name="flow_verticalAlign">
        <enum name="top" value="0" />
        <enum name="bottom" value="1" />
        <enum name="center" value="2" />
        <enum name="baseline" value="3" />
    </attr>
    <attr name="flow_verticalBias" format="float" />
    <attr name="flow_verticalGap" format="dimension" />
    <attr name="flow_verticalStyle">
        <enum name="spread" value="0" />
        <enum name="spread_inside" value="1" />
        <enum name="packed" value="2" />
    </attr>
    <attr name="flow_wrapMode">
        <enum name="none" value="0" />
        <enum name="chain" value="1" />
        <enum name="aligned" value="2" />
        <enum name="chain2" value="3" />
    </attr>
    <attr name="font" format="reference" />
    <attr name="fontFamily" format="string" />
    <attr name="fontProviderAuthority" format="string" />
    <attr name="fontProviderCerts" format="reference" />
    <attr name="fontProviderFetchStrategy">
        <enum name="blocking" value="0" />
        <enum name="async" value="1" />
    </attr>
    <attr name="fontProviderFetchTimeout" format="integer">
        <enum name="forever" value="-1" />
    </attr>
    <attr name="fontProviderPackage" format="string" />
    <attr name="fontProviderQuery" format="string" />
    <attr name="fontProviderSystemFontFamily" format="string" />
    <attr name="fontStyle">
        <enum name="normal" value="0" />
        <enum name="italic" value="1" />
    </attr>
    <attr name="fontVariationSettings" format="string" />
    <attr name="fontWeight" format="integer" />
    <attr name="forceApplySystemWindowInsetTop" format="boolean" />
    <attr name="forceDefaultNavigationOnClickListener" format="boolean" />
    <attr name="foregroundInsidePadding" format="boolean" />
    <attr name="framePosition" format="integer" />
    <attr name="freezesAnimation" format="boolean" />
    <attr name="gapBetweenBars" format="dimension" />
    <attr name="gestureInsetBottomIgnored" format="boolean" />
    <attr name="gifSource" format="reference|string" />
    <attr name="goIcon" format="reference" />
    <attr name="gradientCenterColor" format="color" />
    <attr name="gradientEndColor" format="color" />
    <attr name="gradientOrientation">
        <enum name="horizontal" value="0" />
        <enum name="vertical" value="1" />
    </attr>
    <attr name="gradientStartColor" format="color" />
    <attr name="gradientTvCenterColor" format="color" />
    <attr name="gradientTvEndColor" format="color" />
    <attr name="gradientTvStartColor" format="color" />
    <attr name="gradient_orientation">
        <enum name="vertical" value="0" />
        <enum name="horizontal" value="1" />
        <enum name="top_left_bottom_right" value="2" />
        <enum name="top_right_bottom_left" value="3" />
        <enum name="bottom_left_top_right" value="4" />
        <enum name="bottom_right_top_left" value="5" />
    </attr>
    <attr name="graph" format="reference" />
    <attr name="guidelineUseRtl" format="boolean" />
    <attr name="haloColor" format="color" />
    <attr name="haloRadius" format="dimension" />
    <attr name="headerLayout" format="reference" />
    <attr name="height" format="dimension" />
    <attr name="helperText" format="string" />
    <attr name="helperTextEnabled" format="boolean" />
    <attr name="helperTextTextAppearance" format="reference" />
    <attr name="helperTextTextColor" format="color" />
    <attr name="hideAnimationBehavior">
        <enum name="none" value="0" />
        <enum name="outward" value="1" />
        <enum name="inward" value="2" />
        <enum name="escape" value="3" />
    </attr>
    <attr name="hideMotionSpec" format="reference" />
    <attr name="hideNavigationIcon" format="boolean" />
    <attr name="hideOnContentScroll" format="boolean" />
    <attr name="hideOnScroll" format="boolean" />
    <attr name="hide_during_ads" format="boolean" />
    <attr name="hide_on_touch" format="boolean" />
    <attr name="hintAnimationEnabled" format="boolean" />
    <attr name="hintEnabled" format="boolean" />
    <attr name="hintTextAppearance" format="reference" />
    <attr name="hintTextColor" format="color" />
    <attr name="homeAsUpIndicator" format="reference" />
    <attr name="homeLayout" format="reference" />
    <attr name="horizontalOffset" format="dimension" />
    <attr name="horizontalOffsetWithText" format="dimension" />
    <attr name="hoveredFocusedTranslationZ" format="dimension" />
    <attr name="icon" format="reference" />
    <attr name="iconEndPadding" format="dimension" />
    <attr name="iconGravity">
        <flag name="start" value="0x00000001" />
        <flag name="textStart" value="0x00000002" />
        <flag name="end" value="0x00000003" />
        <flag name="textEnd" value="0x00000004" />
        <flag name="top" value="0x00000010" />
        <flag name="textTop" value="0x00000020" />
    </attr>
    <attr name="iconPadding" format="dimension" />
    <attr name="iconSize" format="dimension" />
    <attr name="iconSrc" format="reference" />
    <attr name="iconStartPadding" format="dimension" />
    <attr name="iconTint" format="color" />
    <attr name="iconTintMode">
        <enum name="src_over" value="3" />
        <enum name="src_in" value="5" />
        <enum name="src_atop" value="9" />
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
        <enum name="add" value="16" />
    </attr>
    <attr name="iconifiedByDefault" format="boolean" />
    <attr name="ifTagNotSet" format="reference" />
    <attr name="ifTagSet" format="reference" />
    <attr name="imageAspectRatio" format="float" />
    <attr name="imageAspectRatioAdjust">
        <enum name="none" value="0" />
        <enum name="adjust_width" value="1" />
        <enum name="adjust_height" value="2" />
    </attr>
    <attr name="imageButtonStyle" format="reference" />
    <attr name="imagePanX" format="float" />
    <attr name="imagePanY" format="float" />
    <attr name="imageRotate" format="float" />
    <attr name="imageZoom" format="float" />
    <attr name="indeterminateAnimationType">
        <enum name="contiguous" value="0" />
        <enum name="disjoint" value="1" />
    </attr>
    <attr name="indeterminateProgressStyle" format="reference" />
    <attr name="indicator">
        <flag name="BallPulse" value="0x00000000" />
        <flag name="BallGridPulse" value="0x00000001" />
        <flag name="BallClipRotate" value="0x00000002" />
        <flag name="BallClipRotatePulse" value="0x00000003" />
        <flag name="SquareSpin" value="0x00000004" />
        <flag name="BallClipRotateMultiple" value="0x00000005" />
        <flag name="BallPulseRise" value="0x00000006" />
        <flag name="BallRotate" value="0x00000007" />
        <flag name="CubeTransition" value="0x00000008" />
        <flag name="BallZigZag" value="0x00000009" />
        <flag name="BallZigZagDeflect" value="0x0000000a" />
        <flag name="BallTrianglePath" value="0x0000000b" />
        <flag name="BallScale" value="0x0000000c" />
        <flag name="LineScale" value="0x0000000d" />
        <flag name="LineScaleParty" value="0x0000000e" />
        <flag name="BallScaleMultiple" value="0x0000000f" />
        <flag name="BallPulseSync" value="0x00000010" />
        <flag name="BallBeat" value="0x00000011" />
        <flag name="LineScalePulseOut" value="0x00000012" />
        <flag name="LineScalePulseOutRapid" value="0x00000013" />
        <flag name="BallScaleRipple" value="0x00000014" />
        <flag name="BallScaleRippleMultiple" value="0x00000015" />
        <flag name="BallSpinFadeLoader" value="0x00000016" />
        <flag name="LineSpinFadeLoader" value="0x00000017" />
        <flag name="TriangleSkewSpin" value="0x00000018" />
        <flag name="Pacman" value="0x00000019" />
        <flag name="BallGridBeat" value="0x0000001a" />
        <flag name="SemiCircleSpin" value="0x0000001b" />
    </attr>
    <attr name="indicatorColor" format="reference|color" />
    <attr name="indicatorDirectionCircular">
        <enum name="clockwise" value="0" />
        <enum name="counterclockwise" value="1" />
    </attr>
    <attr name="indicatorDirectionLinear">
        <enum name="leftToRight" value="0" />
        <enum name="rightToLeft" value="1" />
        <enum name="startToEnd" value="2" />
        <enum name="endToStart" value="3" />
    </attr>
    <attr name="indicatorInset" format="dimension" />
    <attr name="indicatorSize" format="dimension" />
    <attr name="indicatorTrackGapSize" format="dimension" />
    <attr name="indicator_color" format="color" />
    <attr name="indicator_drawable_selected" format="reference" />
    <attr name="indicator_drawable_unselected" format="reference" />
    <attr name="indicator_height" format="dimension" />
    <attr name="indicator_margin" format="dimension" />
    <attr name="indicator_selected_height" format="dimension" />
    <attr name="indicator_selected_width" format="dimension" />
    <attr name="indicator_width" format="dimension" />
    <attr name="initialActivityCount" format="string" />
    <attr name="innerBackgroundColor" format="color" />
    <attr name="insetForeground" format="reference|color" />
    <attr name="isBack" format="boolean" />
    <attr name="isChatting" format="boolean" />
    <attr name="isLightTheme" format="boolean" />
    <attr name="isMaterial3DynamicColorApplied" format="boolean" />
    <attr name="isMaterial3Theme" format="boolean" />
    <attr name="isMaterialTheme" format="boolean" />
    <attr name="isOpaque" format="boolean" />
    <attr name="isOpenAnimator" format="boolean" />
    <attr name="isShowBack" format="boolean" />
    <attr name="is_auto_play" format="boolean" />
    <attr name="isb_clear_default_padding" format="boolean" />
    <attr name="isb_indicator_color" format="reference|color" />
    <attr name="isb_indicator_content_layout" format="reference" />
    <attr name="isb_indicator_text_color" format="reference|color" />
    <attr name="isb_indicator_text_size" format="reference|dimension" />
    <attr name="isb_indicator_top_content_layout" format="reference" />
    <attr name="isb_max" format="float" />
    <attr name="isb_min" format="float" />
    <attr name="isb_only_thumb_draggable" format="boolean" />
    <attr name="isb_progress" format="float" />
    <attr name="isb_progress_value_float" format="boolean" />
    <attr name="isb_r2l" format="boolean" />
    <attr name="isb_seek_smoothly" format="boolean" />
    <attr name="isb_show_indicator">
        <enum name="none" value="0" />
        <enum name="circular_bubble" value="1" />
        <enum name="rounded_rectangle" value="2" />
        <enum name="rectangle" value="3" />
        <enum name="custom" value="4" />
    </attr>
    <attr name="isb_show_thumb_text" format="boolean" />
    <attr name="isb_show_tick_marks_type">
        <enum name="none" value="0" />
        <enum name="oval" value="1" />
        <enum name="square" value="2" />
        <enum name="divider" value="3" />
    </attr>
    <attr name="isb_show_tick_texts" format="boolean" />
    <attr name="isb_thumb_adjust_auto" format="boolean" />
    <attr name="isb_thumb_color" format="reference|color" />
    <attr name="isb_thumb_drawable" format="reference" />
    <attr name="isb_thumb_size" format="reference|dimension" />
    <attr name="isb_thumb_text_color" format="reference|color" />
    <attr name="isb_tick_marks_color" format="reference|color" />
    <attr name="isb_tick_marks_drawable" format="reference" />
    <attr name="isb_tick_marks_ends_hide" format="boolean" />
    <attr name="isb_tick_marks_size" format="reference|dimension" />
    <attr name="isb_tick_marks_swept_hide" format="boolean" />
    <attr name="isb_tick_texts_array" format="reference" />
    <attr name="isb_tick_texts_color" format="reference|color" />
    <attr name="isb_tick_texts_size" format="reference|dimension" />
    <attr name="isb_tick_texts_typeface">
        <enum name="normal" value="0" />
        <enum name="monospace" value="1" />
        <enum name="sans" value="2" />
        <enum name="serif" value="3" />
    </attr>
    <attr name="isb_ticks_count" format="integer" />
    <attr name="isb_track_background_color" format="reference|color" />
    <attr name="isb_track_background_size" format="reference|dimension" />
    <attr name="isb_track_progress_color" format="reference|color" />
    <attr name="isb_track_progress_size" format="reference|dimension" />
    <attr name="isb_track_rounded_corners" format="boolean" />
    <attr name="isb_user_seekable" format="boolean" />
    <attr name="itemActiveIndicatorStyle" format="reference" />
    <attr name="itemBackground" format="reference" />
    <attr name="itemFillColor" format="color" />
    <attr name="itemHorizontalPadding" format="dimension" />
    <attr name="itemHorizontalTranslationEnabled" format="boolean" />
    <attr name="itemIconPadding" format="dimension" />
    <attr name="itemIconSize" format="dimension" />
    <attr name="itemIconTint" format="color" />
    <attr name="itemMaxLines" format="integer" min="1" />
    <attr name="itemMinHeight" format="dimension" />
    <attr name="itemPadding" format="dimension" />
    <attr name="itemPaddingBottom" format="dimension" />
    <attr name="itemPaddingTop" format="dimension" />
    <attr name="itemRippleColor" format="color" />
    <attr name="itemShapeAppearance" format="reference" />
    <attr name="itemShapeAppearanceOverlay" format="reference" />
    <attr name="itemShapeFillColor" format="color" />
    <attr name="itemShapeInsetBottom" format="dimension" />
    <attr name="itemShapeInsetEnd" format="dimension" />
    <attr name="itemShapeInsetStart" format="dimension" />
    <attr name="itemShapeInsetTop" format="dimension" />
    <attr name="itemSpacing" format="dimension" />
    <attr name="itemStrokeColor" format="color" />
    <attr name="itemStrokeWidth" format="dimension" />
    <attr name="itemTextAppearance" format="reference" />
    <attr name="itemTextAppearanceActive" format="reference" />
    <attr name="itemTextAppearanceActiveBoldEnabled" format="boolean" />
    <attr name="itemTextAppearanceInactive" format="reference" />
    <attr name="itemTextColor" format="color" />
    <attr name="itemVerticalPadding" format="dimension" />
    <attr name="iuv_activeItemLayout" format="reference" />
    <attr name="iuv_doneItemLayout" format="reference" />
    <attr name="iuv_inviteViewHorizontalSpace" format="dimension" />
    <attr name="iuv_length" format="integer" />
    <attr name="iuv_loadMoreEnable" format="boolean" />
    <attr name="iuv_loadMoreItemLayout" format="reference" />
    <attr name="iuv_unActiveItemLayout" format="reference" />
    <attr name="justifyContent">
        <enum name="flex_start" value="0" />
        <enum name="flex_end" value="1" />
        <enum name="center" value="2" />
        <enum name="space_between" value="3" />
        <enum name="space_around" value="4" />
        <enum name="space_evenly" value="5" />
    </attr>
    <attr name="jv_style">
        <enum name="image" value="0" />
        <enum name="text" value="1" />
    </attr>
    <attr name="jv_text_join_bg" format="color" />
    <attr name="jv_text_join_text_color" format="color" />
    <attr name="jv_text_joined_bg" format="color" />
    <attr name="jv_text_joined_text_color" format="color" />
    <attr name="jv_text_loading_bg" format="color" />
    <attr name="jv_text_loading_color" format="color" />
    <attr name="jv_text_loading_size" format="dimension" />
    <attr name="jv_text_text_size" format="dimension" />
    <attr name="keep_content_on_player_reset" format="boolean" />
    <attr name="keyPositionType">
        <enum name="deltaRelative" value="0" />
        <enum name="pathRelative" value="1" />
        <enum name="parentRelative" value="2" />
    </attr>
    <attr name="keyboardIcon" format="reference" />
    <attr name="keylines" format="reference" />
    <attr name="lStar" format="float" />
    <attr name="labelBehavior">
        <enum name="floating" value="0" />
        <enum name="withinBounds" value="1" />
        <enum name="gone" value="2" />
        <enum name="visible" value="3" />
    </attr>
    <attr name="labelStyle" format="reference" />
    <attr name="labelVisibilityMode">
        <enum name="auto" value="-1" />
        <enum name="selected" value="0" />
        <enum name="labeled" value="1" />
        <enum name="unlabeled" value="2" />
    </attr>
    <attr name="largeFontVerticalOffsetAdjustment" format="dimension" />
    <attr name="lastBaselineToBottomHeight" format="dimension" />
    <attr name="lastItemDecorated" format="boolean" />
    <attr name="latLngBoundsNorthEastLatitude" format="float" />
    <attr name="latLngBoundsNorthEastLongitude" format="float" />
    <attr name="latLngBoundsSouthWestLatitude" format="float" />
    <attr name="latLngBoundsSouthWestLongitude" format="float" />
    <attr name="launchSingleTop" format="boolean" />
    <attr name="layout" format="reference" />
    <attr name="layoutDescription" format="reference" />
    <attr name="layoutDuringTransition">
        <enum name="ignoreRequest" value="0" />
        <enum name="honorRequest" value="1" />
        <enum name="callMeasure" value="2" />
    </attr>
    <attr name="layoutManager" format="string" />
    <attr name="layout_alignSelf">
        <enum name="auto" value="-1" />
        <enum name="flex_start" value="0" />
        <enum name="flex_end" value="1" />
        <enum name="center" value="2" />
        <enum name="baseline" value="3" />
        <enum name="stretch" value="4" />
    </attr>
    <attr name="layout_anchor" format="reference" />
    <attr name="layout_anchorGravity">
        <flag name="top" value="0x00000030" />
        <flag name="bottom" value="0x00000050" />
        <flag name="left" value="0x00000003" />
        <flag name="right" value="0x00000005" />
        <flag name="center_vertical" value="0x00000010" />
        <flag name="fill_vertical" value="0x00000070" />
        <flag name="center_horizontal" value="0x00000001" />
        <flag name="fill_horizontal" value="0x00000007" />
        <flag name="center" value="0x00000011" />
        <flag name="fill" value="0x00000077" />
        <flag name="clip_vertical" value="0x00000080" />
        <flag name="clip_horizontal" value="0x00000008" />
        <flag name="start" value="0x00800003" />
        <flag name="end" value="0x00800005" />
    </attr>
    <attr name="layout_behavior" format="string" />
    <attr name="layout_collapseMode">
        <enum name="none" value="0" />
        <enum name="pin" value="1" />
        <enum name="parallax" value="2" />
    </attr>
    <attr name="layout_collapseParallaxMultiplier" format="float" />
    <attr name="layout_constrainedHeight" format="boolean" />
    <attr name="layout_constrainedWidth" format="boolean" />
    <attr name="layout_constraintBaseline_creator" format="integer" />
    <attr name="layout_constraintBaseline_toBaselineOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintBaseline_toBottomOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintBaseline_toTopOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintBottom_creator" format="integer" />
    <attr name="layout_constraintBottom_toBottomOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintBottom_toTopOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintCircle" format="reference" />
    <attr name="layout_constraintCircleAngle" format="float" />
    <attr name="layout_constraintCircleRadius" format="dimension" />
    <attr name="layout_constraintDimensionRatio" format="string" />
    <attr name="layout_constraintEnd_toEndOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintEnd_toStartOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintGuide_begin" format="dimension" />
    <attr name="layout_constraintGuide_end" format="dimension" />
    <attr name="layout_constraintGuide_percent" format="float" />
    <attr name="layout_constraintHeight" format="string|dimension">
        <enum name="match_parent" value="-1" />
        <enum name="wrap_content" value="-2" />
        <enum name="match_constraint" value="-3" />
        <enum name="wrap_content_constrained" value="-4" />
    </attr>
    <attr name="layout_constraintHeight_default">
        <enum name="spread" value="0" />
        <enum name="wrap" value="1" />
        <enum name="percent" value="2" />
    </attr>
    <attr name="layout_constraintHeight_max" format="dimension">
        <enum name="wrap" value="-2" />
    </attr>
    <attr name="layout_constraintHeight_min" format="dimension">
        <enum name="wrap" value="-2" />
    </attr>
    <attr name="layout_constraintHeight_percent" format="float" />
    <attr name="layout_constraintHorizontal_bias" format="float" />
    <attr name="layout_constraintHorizontal_chainStyle">
        <enum name="spread" value="0" />
        <enum name="spread_inside" value="1" />
        <enum name="packed" value="2" />
    </attr>
    <attr name="layout_constraintHorizontal_weight" format="float" />
    <attr name="layout_constraintLeft_creator" format="integer" />
    <attr name="layout_constraintLeft_toLeftOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintLeft_toRightOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintRight_creator" format="integer" />
    <attr name="layout_constraintRight_toLeftOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintRight_toRightOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintStart_toEndOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintStart_toStartOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintTag" format="string" />
    <attr name="layout_constraintTop_creator" format="integer" />
    <attr name="layout_constraintTop_toBottomOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintTop_toTopOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintVertical_bias" format="float" />
    <attr name="layout_constraintVertical_chainStyle">
        <enum name="spread" value="0" />
        <enum name="spread_inside" value="1" />
        <enum name="packed" value="2" />
    </attr>
    <attr name="layout_constraintVertical_weight" format="float" />
    <attr name="layout_constraintWidth" format="string|dimension">
        <enum name="match_parent" value="-1" />
        <enum name="wrap_content" value="-2" />
        <enum name="match_constraint" value="-3" />
        <enum name="wrap_content_constrained" value="-4" />
    </attr>
    <attr name="layout_constraintWidth_default">
        <enum name="spread" value="0" />
        <enum name="wrap" value="1" />
        <enum name="percent" value="2" />
    </attr>
    <attr name="layout_constraintWidth_max" format="dimension">
        <enum name="wrap" value="-2" />
    </attr>
    <attr name="layout_constraintWidth_min" format="dimension">
        <enum name="wrap" value="-2" />
    </attr>
    <attr name="layout_constraintWidth_percent" format="float" />
    <attr name="layout_dodgeInsetEdges">
        <flag name="none" value="0x00000000" />
        <flag name="top" value="0x00000030" />
        <flag name="bottom" value="0x00000050" />
        <flag name="left" value="0x00000003" />
        <flag name="right" value="0x00000005" />
        <flag name="start" value="0x00800003" />
        <flag name="end" value="0x00800005" />
        <flag name="all" value="0x00000077" />
    </attr>
    <attr name="layout_editor_absoluteX" format="dimension" />
    <attr name="layout_editor_absoluteY" format="dimension" />
    <attr name="layout_empty" format="reference" />
    <attr name="layout_error" format="reference" />
    <attr name="layout_flexBasisPercent" format="fraction" />
    <attr name="layout_flexGrow" format="float" />
    <attr name="layout_flexShrink" format="float" />
    <attr name="layout_goneMarginBaseline" format="dimension" />
    <attr name="layout_goneMarginBottom" format="dimension" />
    <attr name="layout_goneMarginEnd" format="dimension" />
    <attr name="layout_goneMarginLeft" format="dimension" />
    <attr name="layout_goneMarginRight" format="dimension" />
    <attr name="layout_goneMarginStart" format="dimension" />
    <attr name="layout_goneMarginTop" format="dimension" />
    <attr name="layout_insetEdge">
        <enum name="none" value="0" />
        <enum name="top" value="48" />
        <enum name="bottom" value="80" />
        <enum name="left" value="3" />
        <enum name="right" value="5" />
        <enum name="start" value="8388611" />
        <enum name="end" value="8388613" />
    </attr>
    <attr name="layout_keyline" format="integer" />
    <attr name="layout_marginBaseline" format="dimension" />
    <attr name="layout_maxHeight" format="dimension" />
    <attr name="layout_maxWidth" format="dimension" />
    <attr name="layout_minHeight" format="dimension" />
    <attr name="layout_minWidth" format="dimension" />
    <attr name="layout_optimizationLevel">
        <flag name="none" value="0x00000000" />
        <flag name="legacy" value="0x00000000" />
        <flag name="standard" value="0x00000101" />
        <flag name="direct" value="0x00000001" />
        <flag name="barrier" value="0x00000002" />
        <flag name="chains" value="0x00000004" />
        <flag name="dimensions" value="0x00000008" />
        <flag name="ratio" value="0x00000010" />
        <flag name="groups" value="0x00000020" />
        <flag name="graph" value="0x00000040" />
        <flag name="graph_wrap" value="0x00000080" />
        <flag name="cache_measures" value="0x00000100" />
        <flag name="dependency_ordering" value="0x00000200" />
        <flag name="grouping" value="0x00000400" />
    </attr>
    <attr name="layout_order" format="integer" />
    <attr name="layout_progress" format="reference" />
    <attr name="layout_scrollEffect">
        <enum name="none" value="0" />
        <enum name="compress" value="1" />
    </attr>
    <attr name="layout_scrollFlags">
        <flag name="noScroll" value="0x00000000" />
        <flag name="scroll" value="0x00000001" />
        <flag name="exitUntilCollapsed" value="0x00000002" />
        <flag name="enterAlways" value="0x00000004" />
        <flag name="enterAlwaysCollapsed" value="0x00000008" />
        <flag name="snap" value="0x00000010" />
        <flag name="snapMargins" value="0x00000020" />
    </attr>
    <attr name="layout_scrollInterpolator" format="reference" />
    <attr name="layout_wrapBefore" format="boolean" />
    <attr name="layout_wrapBehaviorInParent">
        <enum name="included" value="0" />
        <enum name="horizontal_only" value="1" />
        <enum name="vertical_only" value="2" />
        <enum name="skipped" value="3" />
    </attr>
    <attr name="left_text" format="reference|string" />
    <attr name="liftOnScroll" format="boolean" />
    <attr name="liftOnScrollColor" format="color" />
    <attr name="liftOnScrollTargetViewId" format="reference" />
    <attr name="limitBoundsTo" format="reference" />
    <attr name="line" format="boolean" />
    <attr name="lineColor" format="color" />
    <attr name="lineHeight" format="dimension" />
    <attr name="lineSpacing" format="dimension" />
    <attr name="linearProgressIndicatorStyle" format="reference" />
    <attr name="listChoiceBackgroundIndicator" format="reference" />
    <attr name="listChoiceIndicatorMultipleAnimated" format="reference" />
    <attr name="listChoiceIndicatorSingleAnimated" format="reference" />
    <attr name="listDividerAlertDialog" format="reference" />
    <attr name="listItemLayout" format="reference" />
    <attr name="listLayout" format="reference" />
    <attr name="listMenuViewStyle" format="reference" />
    <attr name="listPopupWindowStyle" format="reference" />
    <attr name="listPreferredItemHeight" format="dimension" />
    <attr name="listPreferredItemHeightLarge" format="dimension" />
    <attr name="listPreferredItemHeightSmall" format="dimension" />
    <attr name="listPreferredItemPaddingEnd" format="dimension" />
    <attr name="listPreferredItemPaddingLeft" format="dimension" />
    <attr name="listPreferredItemPaddingRight" format="dimension" />
    <attr name="listPreferredItemPaddingStart" format="dimension" />
    <attr name="liteMode" format="boolean" />
    <attr name="logo" format="reference" />
    <attr name="logoAdjustViewBounds" format="boolean" />
    <attr name="logoDescription" format="string" />
    <attr name="logoScaleType">
        <enum name="matrix" value="0" />
        <enum name="fitXY" value="1" />
        <enum name="fitStart" value="2" />
        <enum name="fitCenter" value="3" />
        <enum name="fitEnd" value="4" />
        <enum name="center" value="5" />
        <enum name="centerCrop" value="6" />
        <enum name="centerInside" value="7" />
    </attr>
    <attr name="loopCount" format="integer" />
    <attr name="lottieAnimationViewStyle" format="reference" />
    <attr name="lottie_asyncUpdates">
        <enum name="automatic" value="0" />
        <enum name="enabled" value="1" />
        <enum name="disabled" value="2" />
    </attr>
    <attr name="lottie_autoPlay" format="boolean" />
    <attr name="lottie_cacheComposition" format="boolean" />
    <attr name="lottie_clipTextToBoundingBox" format="boolean" />
    <attr name="lottie_clipToCompositionBounds" format="boolean" />
    <attr name="lottie_colorFilter" format="color" />
    <attr name="lottie_defaultFontFileExtension" format="string" />
    <attr name="lottie_enableMergePathsForKitKatAndAbove" format="boolean" />
    <attr name="lottie_fallbackRes" format="reference" />
    <attr name="lottie_fileName" format="string" />
    <attr name="lottie_ignoreDisabledSystemAnimations" format="boolean" />
    <attr name="lottie_imageAssetsFolder" format="string" />
    <attr name="lottie_loop" format="boolean" />
    <attr name="lottie_progress" format="float" />
    <attr name="lottie_rawRes" format="reference" />
    <attr name="lottie_renderMode">
        <enum name="automatic" value="0" />
        <enum name="hardware" value="1" />
        <enum name="software" value="2" />
    </attr>
    <attr name="lottie_repeatCount" format="integer" />
    <attr name="lottie_repeatMode">
        <enum name="restart" value="1" />
        <enum name="reverse" value="2" />
    </attr>
    <attr name="lottie_speed" format="float" />
    <attr name="lottie_url" format="string" />
    <attr name="lottie_useCompositionFrameRate" format="boolean" />
    <attr name="mapType">
        <enum name="none" value="0" />
        <enum name="normal" value="1" />
        <enum name="satellite" value="2" />
        <enum name="terrain" value="3" />
        <enum name="hybrid" value="4" />
    </attr>
    <attr name="marginHorizontal" format="dimension" />
    <attr name="marginLeftSystemWindowInsets" format="boolean" />
    <attr name="marginRightSystemWindowInsets" format="boolean" />
    <attr name="marginTopSystemWindowInsets" format="boolean" />
    <attr name="materialAlertDialogBodyTextStyle" format="reference" />
    <attr name="materialAlertDialogButtonSpacerVisibility" format="integer" />
    <attr name="materialAlertDialogTheme" format="reference" />
    <attr name="materialAlertDialogTitleIconStyle" format="reference" />
    <attr name="materialAlertDialogTitlePanelStyle" format="reference" />
    <attr name="materialAlertDialogTitleTextStyle" format="reference" />
    <attr name="materialButtonOutlinedStyle" format="reference" />
    <attr name="materialButtonStyle" format="reference" />
    <attr name="materialButtonToggleGroupStyle" format="reference" />
    <attr name="materialCalendarDay" format="reference" />
    <attr name="materialCalendarDayOfWeekLabel" format="reference" />
    <attr name="materialCalendarFullscreenTheme" format="reference" />
    <attr name="materialCalendarHeaderCancelButton" format="reference" />
    <attr name="materialCalendarHeaderConfirmButton" format="reference" />
    <attr name="materialCalendarHeaderDivider" format="reference" />
    <attr name="materialCalendarHeaderLayout" format="reference" />
    <attr name="materialCalendarHeaderSelection" format="reference" />
    <attr name="materialCalendarHeaderTitle" format="reference" />
    <attr name="materialCalendarHeaderToggleButton" format="reference" />
    <attr name="materialCalendarMonth" format="reference" />
    <attr name="materialCalendarMonthNavigationButton" format="reference" />
    <attr name="materialCalendarStyle" format="reference" />
    <attr name="materialCalendarTheme" format="reference" />
    <attr name="materialCalendarYearNavigationButton" format="reference" />
    <attr name="materialCardViewElevatedStyle" format="reference" />
    <attr name="materialCardViewFilledStyle" format="reference" />
    <attr name="materialCardViewOutlinedStyle" format="reference" />
    <attr name="materialCardViewStyle" format="reference" />
    <attr name="materialCircleRadius" format="dimension" />
    <attr name="materialClockStyle" format="reference" />
    <attr name="materialDisplayDividerStyle" format="reference" />
    <attr name="materialDividerHeavyStyle" format="reference|string|integer|boolean|color|float|dimension|fraction" />
    <attr name="materialDividerStyle" format="reference|string|integer|boolean|color|float|dimension|fraction" />
    <attr name="materialIconButtonFilledStyle" format="reference" />
    <attr name="materialIconButtonFilledTonalStyle" format="reference" />
    <attr name="materialIconButtonOutlinedStyle" format="reference" />
    <attr name="materialIconButtonStyle" format="reference" />
    <attr name="materialSearchBarStyle" format="reference" />
    <attr name="materialSearchViewPrefixStyle" format="reference" />
    <attr name="materialSearchViewStyle" format="reference" />
    <attr name="materialSearchViewToolbarHeight" format="dimension" />
    <attr name="materialSearchViewToolbarStyle" format="reference" />
    <attr name="materialSwitchStyle" format="reference|string|integer|boolean|color|float|dimension|fraction" />
    <attr name="materialThemeOverlay" format="reference" />
    <attr name="materialTimePickerStyle" format="reference" />
    <attr name="materialTimePickerTheme" format="reference" />
    <attr name="materialTimePickerTitleStyle" format="reference" />
    <attr name="max" format="integer" />
    <attr name="maxAcceleration" format="float" />
    <attr name="maxActionInlineWidth" format="dimension" />
    <attr name="maxButtonHeight" format="dimension" />
    <attr name="maxCharacterCount" format="integer" />
    <attr name="maxHeight" format="dimension" />
    <attr name="maxImageSize" format="dimension" />
    <attr name="maxLine" format="integer" />
    <attr name="maxLines" format="integer" />
    <attr name="maxNumber" format="integer" />
    <attr name="maxVelocity" format="float" />
    <attr name="maxWidth" format="dimension" />
    <attr name="max_height" format="dimension" />
    <attr name="mbridge_click" format="string" />
    <attr name="mbridge_data" format="string" />
    <attr name="mbridge_effect" format="string" />
    <attr name="mbridge_effect_strategy" format="string" />
    <attr name="mbridge_report">
        <flag name="mbridgeAttached" value="0x00000001" />
        <flag name="mbridgeDetached" value="0x00000002" />
        <flag name="mbridgeClicked" value="0x00000003" />
    </attr>
    <attr name="mbridge_strategy" format="string" />
    <attr name="measureWithLargestChild" format="boolean" />
    <attr name="menu" format="reference" />
    <attr name="menuAlignmentMode">
        <enum name="auto" value="0" />
        <enum name="start" value="1" />
    </attr>
    <attr name="menuGravity">
        <enum name="top" value="49" />
        <enum name="center" value="17" />
        <enum name="bottom" value="81" />
    </attr>
    <attr name="menuRes" format="reference" />
    <attr name="methodName" format="string" />
    <attr name="mimeType" format="string" />
    <attr name="minHeight" format="dimension" />
    <attr name="minHideDelay" format="integer" />
    <attr name="minSeparation" format="dimension" />
    <attr name="minTouchTargetSize" format="dimension" />
    <attr name="minWidth" format="dimension" />
    <attr name="mock_diagonalsColor" format="color" />
    <attr name="mock_label" format="string" />
    <attr name="mock_labelBackgroundColor" format="color" />
    <attr name="mock_labelColor" format="color" />
    <attr name="mock_showDiagonals" format="boolean" />
    <attr name="mock_showLabel" format="boolean" />
    <attr name="motionDebug">
        <enum name="NO_DEBUG" value="0" />
        <enum name="SHOW_PROGRESS" value="1" />
        <enum name="SHOW_PATH" value="2" />
        <enum name="SHOW_ALL" value="3" />
    </attr>
    <attr name="motionDurationExtraLong1" format="integer" />
    <attr name="motionDurationExtraLong2" format="integer" />
    <attr name="motionDurationExtraLong3" format="integer" />
    <attr name="motionDurationExtraLong4" format="integer" />
    <attr name="motionDurationLong1" format="integer" />
    <attr name="motionDurationLong2" format="integer" />
    <attr name="motionDurationLong3" format="integer" />
    <attr name="motionDurationLong4" format="integer" />
    <attr name="motionDurationMedium1" format="integer" />
    <attr name="motionDurationMedium2" format="integer" />
    <attr name="motionDurationMedium3" format="integer" />
    <attr name="motionDurationMedium4" format="integer" />
    <attr name="motionDurationShort1" format="integer" />
    <attr name="motionDurationShort2" format="integer" />
    <attr name="motionDurationShort3" format="integer" />
    <attr name="motionDurationShort4" format="integer" />
    <attr name="motionEasingAccelerated" format="string" />
    <attr name="motionEasingDecelerated" format="string" />
    <attr name="motionEasingEmphasized" format="string" />
    <attr name="motionEasingEmphasizedAccelerateInterpolator" format="reference" />
    <attr name="motionEasingEmphasizedDecelerateInterpolator" format="reference" />
    <attr name="motionEasingEmphasizedInterpolator" format="reference" />
    <attr name="motionEasingLinear" format="string" />
    <attr name="motionEasingLinearInterpolator" format="reference" />
    <attr name="motionEasingStandard" format="string" />
    <attr name="motionEasingStandardAccelerateInterpolator" format="reference" />
    <attr name="motionEasingStandardDecelerateInterpolator" format="reference" />
    <attr name="motionEasingStandardInterpolator" format="reference" />
    <attr name="motionEffect_alpha" format="float" />
    <attr name="motionEffect_end" format="integer" />
    <attr name="motionEffect_move">
        <enum name="auto" value="-1" />
        <enum name="north" value="0" />
        <enum name="south" value="1" />
        <enum name="east" value="2" />
        <enum name="west" value="3" />
    </attr>
    <attr name="motionEffect_start" format="integer" />
    <attr name="motionEffect_strict" format="boolean" />
    <attr name="motionEffect_translationX" format="dimension" />
    <attr name="motionEffect_translationY" format="dimension" />
    <attr name="motionEffect_viewTransition" format="reference" />
    <attr name="motionInterpolator" format="reference|string">
        <enum name="easeInOut" value="0" />
        <enum name="easeIn" value="1" />
        <enum name="easeOut" value="2" />
        <enum name="linear" value="3" />
        <enum name="bounce" value="4" />
        <enum name="overshoot" value="5" />
        <enum name="anticipate" value="6" />
    </attr>
    <attr name="motionPath" format="string">
        <enum name="linear" value="0" />
        <enum name="arc" value="1" />
    </attr>
    <attr name="motionPathRotate" format="float" />
    <attr name="motionProgress" format="float" />
    <attr name="motionStagger" format="float" />
    <attr name="motionTarget" format="reference|string" />
    <attr name="motion_postLayoutCollision" format="boolean" />
    <attr name="motion_triggerOnCollision" format="reference" />
    <attr name="moveWhenScrollAtTop" format="boolean" />
    <attr name="multiChoiceItemLayout" format="reference" />
    <attr name="navGraph" format="reference" />
    <attr name="navigationContentDescription" format="string" />
    <attr name="navigationIcon" format="reference" />
    <attr name="navigationIconTint" format="color" />
    <attr name="navigationMode">
        <enum name="normal" value="0" />
        <enum name="listMode" value="1" />
        <enum name="tabMode" value="2" />
    </attr>
    <attr name="navigationRailStyle" format="reference" />
    <attr name="navigationViewStyle" format="reference" />
    <attr name="nestedScrollFlags">
        <flag name="none" value="0x00000000" />
        <flag name="disablePostScroll" value="0x00000001" />
        <flag name="disableScroll" value="0x00000002" />
        <flag name="supportScrollUp" value="0x00000004" />
    </attr>
    <attr name="nestedScrollViewStyle" format="reference" />
    <attr name="nestedScrollable" format="boolean" />
    <attr name="ngv_gridSpacing" format="dimension" />
    <attr name="ngv_maxSize" format="integer" />
    <attr name="ngv_mode">
        <enum name="fill" value="0" />
        <enum name="grid" value="1" />
    </attr>
    <attr name="ngv_singleImageRatio" format="float" />
    <attr name="ngv_singleImageSize" format="dimension" />
    <attr name="normal_drawable" format="reference" />
    <attr name="nullable" format="boolean" />
    <attr name="number" format="integer" />
    <attr name="numericModifiers">
        <flag name="META" value="0x00010000" />
        <flag name="CTRL" value="0x00001000" />
        <flag name="ALT" value="0x00000002" />
        <flag name="SHIFT" value="0x00000001" />
        <flag name="SYM" value="0x00000004" />
        <flag name="FUNCTION" value="0x00000008" />
    </attr>
    <attr name="offsetAlignmentMode">
        <enum name="edge" value="0" />
        <enum name="legacy" value="1" />
    </attr>
    <attr name="onCross" format="string" />
    <attr name="onHide" format="boolean" />
    <attr name="onNegativeCross" format="string" />
    <attr name="onPositiveCross" format="string" />
    <attr name="onShow" format="boolean" />
    <attr name="onStateTransition">
        <enum name="actionDown" value="1" />
        <enum name="actionUp" value="2" />
        <enum name="actionDownUp" value="3" />
        <enum name="sharedValueSet" value="4" />
        <enum name="sharedValueUnset" value="5" />
    </attr>
    <attr name="onTouchUp">
        <enum name="autoComplete" value="0" />
        <enum name="autoCompleteToStart" value="1" />
        <enum name="autoCompleteToEnd" value="2" />
        <enum name="stop" value="3" />
        <enum name="decelerate" value="4" />
        <enum name="decelerateAndComplete" value="5" />
        <enum name="neverCompleteToStart" value="6" />
        <enum name="neverCompleteToEnd" value="7" />
    </attr>
    <attr name="overlapAnchor" format="boolean" />
    <attr name="overlay" format="boolean" />
    <attr name="paddingBottomNoButtons" format="dimension" />
    <attr name="paddingBottomSystemWindowInsets" format="boolean" />
    <attr name="paddingEnd" format="dimension" />
    <attr name="paddingLeftSystemWindowInsets" format="boolean" />
    <attr name="paddingRightSystemWindowInsets" format="boolean" />
    <attr name="paddingStart" format="dimension" />
    <attr name="paddingStartSystemWindowInsets" format="boolean" />
    <attr name="paddingTopNoTitle" format="dimension" />
    <attr name="paddingTopSystemWindowInsets" format="boolean" />
    <attr name="panelBackground" format="reference" />
    <attr name="panelMenuListTheme" format="reference" />
    <attr name="panelMenuListWidth" format="dimension" />
    <attr name="passwordToggleContentDescription" format="string" />
    <attr name="passwordToggleDrawable" format="reference" />
    <attr name="passwordToggleEnabled" format="boolean" />
    <attr name="passwordToggleTint" format="color" />
    <attr name="passwordToggleTintMode">
        <enum name="src_over" value="3" />
        <enum name="src_in" value="5" />
        <enum name="src_atop" value="9" />
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
    </attr>
    <attr name="pathMotionArc">
        <enum name="none" value="0" />
        <enum name="startVertical" value="1" />
        <enum name="startHorizontal" value="2" />
        <enum name="flip" value="3" />
    </attr>
    <attr name="path_percent" format="float" />
    <attr name="percentHeight" format="float" />
    <attr name="percentTextColor" format="color" />
    <attr name="percentTextSize" format="dimension" />
    <attr name="percentWidth" format="float" />
    <attr name="percentX" format="float" />
    <attr name="percentY" format="float" />
    <attr name="perpendicularPath_percent" format="float" />
    <attr name="pivotAnchor" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="placeholderActivityName" format="string" />
    <attr name="placeholderText" format="string" />
    <attr name="placeholderTextAppearance" format="reference" />
    <attr name="placeholderTextColor" format="color" />
    <attr name="placeholder_emptyVisibility">
        <enum name="visible" value="0" />
        <enum name="invisible" value="4" />
        <enum name="gone" value="8" />
    </attr>
    <attr name="placementId" format="string" />
    <attr name="played_ad_marker_color" format="color" />
    <attr name="played_color" format="color" />
    <attr name="player_layout_id" format="reference" />
    <attr name="polarRelativeTo" format="reference" />
    <attr name="popEnterAnim" format="reference" />
    <attr name="popExitAnim" format="reference" />
    <attr name="popUpTo" format="reference" />
    <attr name="popUpToInclusive" format="boolean" />
    <attr name="popUpToSaveState" format="boolean" />
    <attr name="pop_Init_state">
        <enum name="shrink" value="0" />
        <enum name="expand" value="1" />
    </attr>
    <attr name="pop_collapse_gap" format="reference|string" />
    <attr name="pop_ellipsis" format="reference|string" />
    <attr name="pop_expand_bg_Color" format="reference|color" />
    <attr name="pop_expand_gap" format="reference|string" />
    <attr name="pop_expand_hint" format="reference|string" />
    <attr name="pop_expand_hint_color" format="reference|color" />
    <attr name="pop_hash_tag_color" format="reference|color" />
    <attr name="pop_hint_text_size" format="dimension" />
    <attr name="pop_reverse_Lines" format="reference|integer" />
    <attr name="pop_show_expand_hint" format="reference|boolean" />
    <attr name="pop_show_hint_icon" format="boolean" />
    <attr name="pop_show_shrink_hint" format="reference|boolean" />
    <attr name="pop_shrink_bg_color" format="reference|color" />
    <attr name="pop_shrink_hint" format="reference|string" />
    <attr name="pop_shrink_hint_color" format="reference|color" />
    <attr name="pop_toggle_enabled" format="reference|boolean" />
    <attr name="popupMenuBackground" format="reference" />
    <attr name="popupMenuStyle" format="reference" />
    <attr name="popupTheme" format="reference" />
    <attr name="popupWindowStyle" format="reference" />
    <attr name="postSplashScreenTheme" format="reference" />
    <attr name="prefixText" format="string" />
    <attr name="prefixTextAppearance" format="reference" />
    <attr name="prefixTextColor" format="color" />
    <attr name="preserveIconSpacing" format="boolean" />
    <attr name="pressedTranslationZ" format="dimension" />
    <attr name="primaryActivityName" format="string" />
    <attr name="progress" format="integer" />
    <attr name="progressBarPadding" format="dimension" />
    <attr name="progressBarStyle" format="reference" />
    <attr name="progressBgColor" format="color" />
    <attr name="progressCurrent" format="integer" />
    <attr name="progressMax" format="integer" />
    <attr name="progressRadius" format="dimension" />
    <attr name="progressRingsColor" format="color" />
    <attr name="progressStrokesWidth" format="dimension" />
    <attr name="progressTextsColor" format="color" />
    <attr name="progress_textSize" format="dimension" />
    <attr name="quantizeMotionInterpolator" format="reference|string">
        <enum name="easeInOut" value="0" />
        <enum name="easeIn" value="1" />
        <enum name="easeOut" value="2" />
        <enum name="linear" value="3" />
        <enum name="bounce" value="4" />
        <enum name="overshoot" value="5" />
    </attr>
    <attr name="quantizeMotionPhase" format="float" />
    <attr name="quantizeMotionSteps" format="integer" />
    <attr name="queryBackground" format="reference" />
    <attr name="queryHint" format="string" />
    <attr name="queryPatterns" format="reference" />
    <attr name="ra" format="dimension" />
    <attr name="radioButtonStyle" format="reference" />
    <attr name="radiusYL" format="dimension" />
    <attr name="rangeFillColor" format="color" />
    <attr name="ratingBarStyle" format="reference" />
    <attr name="ratingBarStyleIndicator" format="reference" />
    <attr name="ratingBarStyleSmall" format="reference" />
    <attr name="reactiveGuide_animateChange" format="boolean" />
    <attr name="reactiveGuide_applyToAllConstraintSets" format="boolean" />
    <attr name="reactiveGuide_applyToConstraintSet" format="reference" />
    <attr name="reactiveGuide_valueId" format="reference" />
    <attr name="realtimeBlurRadius" format="dimension" />
    <attr name="realtimeDownsampleFactor" format="float" />
    <attr name="realtimeOverlayColor" format="color" />
    <attr name="rectColor" format="color" />
    <attr name="rectCorner" format="dimension" />
    <attr name="rectCount" format="integer" />
    <attr name="rectHeight" format="dimension" />
    <attr name="rectSpacing" format="dimension" />
    <attr name="rectWidth" format="dimension" />
    <attr name="rectangleShape" format="boolean" />
    <attr name="recyclerViewStyle" format="reference" />
    <attr name="region_heightLessThan" format="dimension" />
    <attr name="region_heightMoreThan" format="dimension" />
    <attr name="region_widthLessThan" format="dimension" />
    <attr name="region_widthMoreThan" format="dimension" />
    <attr name="removeEmbeddedFabElevation" format="boolean" />
    <attr name="repeat_toggle_modes">
        <flag name="none" value="0x00000000" />
        <flag name="one" value="0x00000001" />
        <flag name="all" value="0x00000002" />
    </attr>
    <attr name="resize_mode">
        <enum name="fit" value="0" />
        <enum name="fixed_width" value="1" />
        <enum name="fixed_height" value="2" />
        <enum name="fill" value="3" />
        <enum name="zoom" value="4" />
    </attr>
    <attr name="restoreState" format="boolean" />
    <attr name="reverse" format="boolean" />
    <attr name="reverseLayout" format="boolean" />
    <attr name="rightValue" format="reference|string" />
    <attr name="right_icon" format="reference" />
    <attr name="right_icon_status" format="boolean" />
    <attr name="right_text" format="reference|string" />
    <attr name="ringBackgroungColor" format="color" />
    <attr name="ringColor" format="color" />
    <attr name="ringWidth" format="dimension" />
    <attr name="ringsColor" format="color" />
    <attr name="rippleColor" format="color" />
    <attr name="rotationCenterId" format="reference" />
    <attr name="round" format="dimension" />
    <attr name="roundCap" format="boolean" />
    <attr name="roundPercent" format="float" />
    <attr name="route" format="string" />
    <attr name="saturation" format="float" />
    <attr name="scaleFromTextSize" format="dimension" />
    <attr name="scaleTypeYL">
        <enum name="top" value="0" />
        <enum name="center" value="1" />
        <enum name="bottom" value="2" />
        <enum name="fitXY" value="3" />
    </attr>
    <attr name="scanFrameHeight" format="dimension" />
    <attr name="scanFrameWidth" format="dimension" />
    <attr name="scopeUris" format="reference|string" />
    <attr name="screen_type" format="integer" />
    <attr name="scrimAnimationDuration" format="integer" />
    <attr name="scrimBackground" format="reference|color" />
    <attr name="scrimVisibleHeightTrigger" format="dimension" />
    <attr name="scroll_maxHeight" format="dimension" />
    <attr name="scrubber_color" format="color" />
    <attr name="scrubber_disabled_size" format="dimension" />
    <attr name="scrubber_dragged_size" format="dimension" />
    <attr name="scrubber_drawable" format="reference" />
    <attr name="scrubber_enabled_size" format="dimension" />
    <attr name="searchHintIcon" format="reference" />
    <attr name="searchIcon" format="reference" />
    <attr name="searchPrefixText" format="string" />
    <attr name="searchViewStyle" format="reference" />
    <attr name="secondaryActivityAction" format="string" />
    <attr name="secondaryActivityName" format="string" />
    <attr name="seekBarStyle" format="reference" />
    <attr name="selectableItemBackground" format="reference" />
    <attr name="selectableItemBackgroundBorderless" format="reference" />
    <attr name="selected_drawable" format="reference" />
    <attr name="selectionRequired" format="boolean" />
    <attr name="selectorSize" format="dimension" />
    <attr name="setsTag" format="reference" />
    <attr name="shadowColor" format="color" />
    <attr name="shadowOffsetX" format="dimension" />
    <attr name="shadowOffsetY" format="dimension" />
    <attr name="shadowRadius" format="dimension" />
    <attr name="shapeAppearance" format="reference" />
    <attr name="shapeAppearanceCornerExtraLarge" format="reference" />
    <attr name="shapeAppearanceCornerExtraSmall" format="reference" />
    <attr name="shapeAppearanceCornerLarge" format="reference" />
    <attr name="shapeAppearanceCornerMedium" format="reference" />
    <attr name="shapeAppearanceCornerSmall" format="reference" />
    <attr name="shapeAppearanceLargeComponent" format="reference" />
    <attr name="shapeAppearanceMediumComponent" format="reference" />
    <attr name="shapeAppearanceOverlay" format="reference" />
    <attr name="shapeAppearanceSmallComponent" format="reference" />
    <attr name="shapeCornerFamily">
        <enum name="rounded" value="0" />
        <enum name="cut" value="1" />
    </attr>
    <attr name="shimmer_auto_start" format="boolean" />
    <attr name="shimmer_base_alpha" format="float" />
    <attr name="shimmer_base_color" format="color" />
    <attr name="shimmer_clip_to_children" format="boolean" />
    <attr name="shimmer_colored" format="boolean" />
    <attr name="shimmer_direction">
        <enum name="left_to_right" value="0" />
        <enum name="top_to_bottom" value="1" />
        <enum name="right_to_left" value="2" />
        <enum name="bottom_to_top" value="3" />
    </attr>
    <attr name="shimmer_dropoff" format="float" />
    <attr name="shimmer_duration" format="integer" />
    <attr name="shimmer_fixed_height" format="dimension" />
    <attr name="shimmer_fixed_width" format="dimension" />
    <attr name="shimmer_height_ratio" format="float" />
    <attr name="shimmer_highlight_alpha" format="float" />
    <attr name="shimmer_highlight_color" format="color" />
    <attr name="shimmer_intensity" format="float" />
    <attr name="shimmer_repeat_count" format="integer" />
    <attr name="shimmer_repeat_delay" format="integer" />
    <attr name="shimmer_repeat_mode">
        <enum name="restart" value="1" />
        <enum name="reverse" value="2" />
    </attr>
    <attr name="shimmer_shape">
        <enum name="linear" value="0" />
        <enum name="radial" value="1" />
    </attr>
    <attr name="shimmer_tilt" format="float" />
    <attr name="shimmer_width_ratio" format="float" />
    <attr name="shortcutMatchRequired" format="boolean" />
    <attr name="shouldRemoveExpandedCorners" format="boolean" />
    <attr name="showAnimationBehavior">
        <enum name="none" value="0" />
        <enum name="outward" value="1" />
        <enum name="inward" value="2" />
    </attr>
    <attr name="showAsAction">
        <flag name="never" value="0x00000000" />
        <flag name="ifRoom" value="0x00000001" />
        <flag name="always" value="0x00000002" />
        <flag name="withText" value="0x00000004" />
        <flag name="collapseActionView" value="0x00000008" />
    </attr>
    <attr name="showDelay" format="integer" />
    <attr name="showDivider">
        <flag name="none" value="0x00000000" />
        <flag name="beginning" value="0x00000001" />
        <flag name="middle" value="0x00000002" />
        <flag name="end" value="0x00000004" />
    </attr>
    <attr name="showDividerHorizontal">
        <flag name="none" value="0x00000000" />
        <flag name="beginning" value="0x00000001" />
        <flag name="middle" value="0x00000002" />
        <flag name="end" value="0x00000004" />
    </attr>
    <attr name="showDividerVertical">
        <flag name="none" value="0x00000000" />
        <flag name="beginning" value="0x00000001" />
        <flag name="middle" value="0x00000002" />
        <flag name="end" value="0x00000004" />
    </attr>
    <attr name="showDividers">
        <flag name="none" value="0x00000000" />
        <flag name="beginning" value="0x00000001" />
        <flag name="middle" value="0x00000002" />
        <flag name="end" value="0x00000004" />
    </attr>
    <attr name="showEdit" format="boolean" />
    <attr name="showLine" format="boolean" />
    <attr name="showMarker" format="boolean" />
    <attr name="showMotionSpec" format="reference" />
    <attr name="showPaths" format="boolean" />
    <attr name="showSearch" format="boolean" />
    <attr name="showText" format="boolean" />
    <attr name="showTitle" format="boolean" />
    <attr name="show_buffering">
        <enum name="never" value="0" />
        <enum name="when_playing" value="1" />
        <enum name="always" value="2" />
    </attr>
    <attr name="show_fastforward_button" format="boolean" />
    <attr name="show_next_button" format="boolean" />
    <attr name="show_previous_button" format="boolean" />
    <attr name="show_rewind_button" format="boolean" />
    <attr name="show_shuffle_button" format="boolean" />
    <attr name="show_subtitle_button" format="boolean" />
    <attr name="show_timeout" format="integer" />
    <attr name="show_vr_button" format="boolean" />
    <attr name="shrinkMotionSpec" format="reference" />
    <attr name="shutter_background_color" format="color" />
    <attr name="sideSheetDialogTheme" format="reference" />
    <attr name="sideSheetModalStyle" format="reference" />
    <attr name="simpleItemLayout" format="reference" />
    <attr name="simpleItemSelectedColor" format="color" />
    <attr name="simpleItemSelectedRippleColor" format="color" />
    <attr name="simpleItems" format="reference" />
    <attr name="singleChoiceItemLayout" format="reference" />
    <attr name="singleLine" format="boolean" />
    <attr name="singleSelection" format="boolean" />
    <attr name="sizePercent" format="float" />
    <attr name="sliderStyle" format="reference" />
    <attr name="snackbarButtonStyle" format="reference" />
    <attr name="snackbarStyle" format="reference" />
    <attr name="snackbarTextViewStyle" format="reference" />
    <attr name="spanCount" format="integer" />
    <attr name="spinBars" format="boolean" />
    <attr name="spinnerDropDownItemStyle" format="reference" />
    <attr name="spinnerStyle" format="reference" />
    <attr name="splashScreenIconSize" format="dimension" />
    <attr name="splitLayoutDirection">
        <enum name="locale" value="0" />
        <enum name="ltr" value="1" />
        <enum name="rtl" value="2" />
    </attr>
    <attr name="splitMinSmallestWidth" format="dimension" />
    <attr name="splitMinWidth" format="dimension" />
    <attr name="splitRatio" format="float" />
    <attr name="splitTrack" format="boolean" />
    <attr name="springBoundary">
        <flag name="overshoot" value="0x00000000" />
        <flag name="bounceStart" value="0x00000001" />
        <flag name="bounceEnd" value="0x00000002" />
        <flag name="bounceBoth" value="0x00000003" />
    </attr>
    <attr name="springDamping" format="float" />
    <attr name="springMass" format="float" />
    <attr name="springStiffness" format="float" />
    <attr name="springStopThreshold" format="float" />
    <attr name="srcCompat" format="reference" />
    <attr name="ssb_bar_center_color" format="color" />
    <attr name="ssb_bar_end_color" format="color" />
    <attr name="ssb_bar_start_color" format="color" />
    <attr name="ssb_bg_color" format="color" />
    <attr name="ssb_max" format="integer" />
    <attr name="ssb_progress" format="integer" />
    <attr name="ssb_progress_size" format="reference|dimension" />
    <attr name="ssb_secondaries_color" format="color" />
    <attr name="ssb_seek_enable" format="boolean" />
    <attr name="ssb_thumb_color" format="color" />
    <attr name="ssb_thumb_enlarge" format="float" />
    <attr name="ssb_thumb_size" format="reference|dimension" />
    <attr name="sspScaleType">
        <enum name="matrix" value="0" />
        <enum name="fitXY" value="1" />
        <enum name="fitStart" value="2" />
        <enum name="fitCenter" value="3" />
        <enum name="fitEnd" value="4" />
        <enum name="center" value="5" />
        <enum name="centerCrop" value="6" />
        <enum name="centerInside" value="7" />
    </attr>
    <attr name="stackFromEnd" format="boolean" />
    <attr name="staggered" format="float" />
    <attr name="startAngle" format="float" />
    <attr name="startColor" format="color" />
    <attr name="startDestination" format="reference" />
    <attr name="startIconCheckable" format="boolean" />
    <attr name="startIconContentDescription" format="string" />
    <attr name="startIconDrawable" format="reference" />
    <attr name="startIconMinSize" format="dimension" />
    <attr name="startIconScaleType">
        <enum name="fitXY" value="0" />
        <enum name="fitStart" value="1" />
        <enum name="fitCenter" value="2" />
        <enum name="fitEnd" value="3" />
        <enum name="center" value="4" />
        <enum name="centerCrop" value="5" />
        <enum name="centerInside" value="6" />
    </attr>
    <attr name="startIconTint" format="color" />
    <attr name="startIconTintMode">
        <enum name="src_over" value="3" />
        <enum name="src_in" value="5" />
        <enum name="src_atop" value="9" />
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
    </attr>
    <attr name="state_above_anchor" format="boolean" />
    <attr name="state_collapsed" format="boolean" />
    <attr name="state_collapsible" format="boolean" />
    <attr name="state_dragged" format="boolean" />
    <attr name="state_error" format="boolean" />
    <attr name="state_indeterminate" format="boolean" />
    <attr name="state_liftable" format="boolean" />
    <attr name="state_lifted" format="boolean" />
    <attr name="state_with_icon" format="boolean" />
    <attr name="statusBarBackground" format="reference|color" />
    <attr name="statusBarForeground" format="color" />
    <attr name="statusBarScrim" format="color" />
    <attr name="storeTextColor" format="color" />
    <attr name="storeTextSize" format="dimension" />
    <attr name="strokeColor" format="color" />
    <attr name="strokeWidth" format="dimension" />
    <attr name="strokesWidth" format="dimension" />
    <attr name="subMenuArrow" format="reference" />
    <attr name="subheaderColor" format="reference|color" />
    <attr name="subheaderInsetEnd" format="dimension" />
    <attr name="subheaderInsetStart" format="dimension" />
    <attr name="subheaderTextAppearance" format="reference" />
    <attr name="submitBackground" format="reference" />
    <attr name="subtitle" format="string" />
    <attr name="subtitleCentered" format="boolean" />
    <attr name="subtitleTextAppearance" format="reference" />
    <attr name="subtitleTextColor" format="color" />
    <attr name="subtitleTextStyle" format="reference" />
    <attr name="suffixText" format="string" />
    <attr name="suffixTextAppearance" format="reference" />
    <attr name="suffixTextColor" format="color" />
    <attr name="suggestionRowLayout" format="reference" />
    <attr name="surface_type">
        <enum name="none" value="0" />
        <enum name="surface_view" value="1" />
        <enum name="texture_view" value="2" />
        <enum name="spherical_gl_surface_view" value="3" />
        <enum name="video_decoder_gl_surface_view" value="4" />
    </attr>
    <attr name="swipeRefreshLayoutProgressSpinnerBackgroundColor" format="color" />
    <attr name="switchMinWidth" format="dimension" />
    <attr name="switchPadding" format="dimension" />
    <attr name="switchStyle" format="reference" />
    <attr name="switchTextAppearance" format="reference" />
    <attr name="switch_ball_color" format="color" />
    <attr name="switch_bg_color" format="color" />
    <attr name="switch_checked_bg_color" format="color" />
    <attr name="switch_gradient_end_color" format="color" />
    <attr name="switch_gradient_start_color" format="color" />
    <attr name="switch_radius" format="integer" />
    <attr name="tabBackground" format="reference" />
    <attr name="tabContentStart" format="dimension" />
    <attr name="tabGravity">
        <enum name="fill" value="0" />
        <enum name="center" value="1" />
        <enum name="start" value="2" />
    </attr>
    <attr name="tabIconTint" format="color" />
    <attr name="tabIconTintMode">
        <enum name="src_over" value="3" />
        <enum name="src_in" value="5" />
        <enum name="src_atop" value="9" />
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
        <enum name="add" value="16" />
    </attr>
    <attr name="tabIndicator" format="reference" />
    <attr name="tabIndicatorAnimationDuration" format="integer" />
    <attr name="tabIndicatorAnimationMode">
        <enum name="linear" value="0" />
        <enum name="elastic" value="1" />
        <enum name="fade" value="2" />
    </attr>
    <attr name="tabIndicatorColor" format="color" />
    <attr name="tabIndicatorFullWidth" format="boolean" />
    <attr name="tabIndicatorGravity">
        <enum name="bottom" value="0" />
        <enum name="center" value="1" />
        <enum name="top" value="2" />
        <enum name="stretch" value="3" />
    </attr>
    <attr name="tabIndicatorHeight" format="dimension" />
    <attr name="tabInlineLabel" format="boolean" />
    <attr name="tabMaxWidth" format="dimension" />
    <attr name="tabMinWidth" format="dimension" />
    <attr name="tabMode">
        <enum name="scrollable" value="0" />
        <enum name="fixed" value="1" />
        <enum name="auto" value="2" />
    </attr>
    <attr name="tabPadding" format="dimension" />
    <attr name="tabPaddingBottom" format="dimension" />
    <attr name="tabPaddingEnd" format="dimension" />
    <attr name="tabPaddingStart" format="dimension" />
    <attr name="tabPaddingTop" format="dimension" />
    <attr name="tabRippleColor" format="color" />
    <attr name="tabSecondaryStyle" format="reference" />
    <attr name="tabSelectedTextAppearance" format="reference" />
    <attr name="tabSelectedTextColor" format="color" />
    <attr name="tabStyle" format="reference" />
    <attr name="tabTextAppearance" format="reference" />
    <attr name="tabTextColor" format="color" />
    <attr name="tabUnboundedRipple" format="boolean" />
    <attr name="targetId" format="reference" />
    <attr name="targetPackage" format="string" />
    <attr name="telltales_tailColor" format="color" />
    <attr name="telltales_tailScale" format="float" />
    <attr name="telltales_velocityMode">
        <enum name="layout" value="0" />
        <enum name="postLayout" value="1" />
        <enum name="staticPostLayout" value="2" />
        <enum name="staticLayout" value="3" />
    </attr>
    <attr name="text" format="string" />
    <attr name="textAllCaps" format="reference|boolean" />
    <attr name="textAppearanceBody1" format="reference" />
    <attr name="textAppearanceBody2" format="reference" />
    <attr name="textAppearanceBodyLarge" format="reference" />
    <attr name="textAppearanceBodyMedium" format="reference" />
    <attr name="textAppearanceBodySmall" format="reference" />
    <attr name="textAppearanceButton" format="reference" />
    <attr name="textAppearanceCaption" format="reference" />
    <attr name="textAppearanceDisplayLarge" format="reference" />
    <attr name="textAppearanceDisplayMedium" format="reference" />
    <attr name="textAppearanceDisplaySmall" format="reference" />
    <attr name="textAppearanceHeadline1" format="reference" />
    <attr name="textAppearanceHeadline2" format="reference" />
    <attr name="textAppearanceHeadline3" format="reference" />
    <attr name="textAppearanceHeadline4" format="reference" />
    <attr name="textAppearanceHeadline5" format="reference" />
    <attr name="textAppearanceHeadline6" format="reference" />
    <attr name="textAppearanceHeadlineLarge" format="reference" />
    <attr name="textAppearanceHeadlineMedium" format="reference" />
    <attr name="textAppearanceHeadlineSmall" format="reference" />
    <attr name="textAppearanceLabelLarge" format="reference" />
    <attr name="textAppearanceLabelMedium" format="reference" />
    <attr name="textAppearanceLabelSmall" format="reference" />
    <attr name="textAppearanceLargePopupMenu" format="reference" />
    <attr name="textAppearanceLineHeightEnabled" format="boolean" />
    <attr name="textAppearanceListItem" format="reference" />
    <attr name="textAppearanceListItemSecondary" format="reference" />
    <attr name="textAppearanceListItemSmall" format="reference" />
    <attr name="textAppearanceOverline" format="reference" />
    <attr name="textAppearancePopupMenuHeader" format="reference" />
    <attr name="textAppearanceSearchResultSubtitle" format="reference" />
    <attr name="textAppearanceSearchResultTitle" format="reference" />
    <attr name="textAppearanceSmallPopupMenu" format="reference" />
    <attr name="textAppearanceSubtitle1" format="reference" />
    <attr name="textAppearanceSubtitle2" format="reference" />
    <attr name="textAppearanceTitleLarge" format="reference" />
    <attr name="textAppearanceTitleMedium" format="reference" />
    <attr name="textAppearanceTitleSmall" format="reference" />
    <attr name="textBackground" format="reference" />
    <attr name="textBackgroundPanX" format="float" />
    <attr name="textBackgroundPanY" format="float" />
    <attr name="textBackgroundRotate" format="float" />
    <attr name="textBackgroundZoom" format="float" />
    <attr name="textColorAlertDialogListItem" format="reference|color" />
    <attr name="textColorSearchUrl" format="reference|color" />
    <attr name="textEndPadding" format="dimension" />
    <attr name="textFillColor" format="color" />
    <attr name="textInputFilledDenseStyle" format="reference" />
    <attr name="textInputFilledExposedDropdownMenuStyle" format="reference" />
    <attr name="textInputFilledStyle" format="reference" />
    <attr name="textInputLayoutFocusedRectEnabled" format="boolean" />
    <attr name="textInputOutlinedDenseStyle" format="reference" />
    <attr name="textInputOutlinedExposedDropdownMenuStyle" format="reference" />
    <attr name="textInputOutlinedStyle" format="reference" />
    <attr name="textInputStyle" format="reference" />
    <attr name="textLocale" format="string" />
    <attr name="textOutlineColor" format="color" />
    <attr name="textOutlineThickness" format="dimension" />
    <attr name="textPanX" format="float" />
    <attr name="textPanY" format="float" />
    <attr name="textStartPadding" format="dimension" />
    <attr name="textsColor" format="color" />
    <attr name="textureBlurFactor" format="integer" />
    <attr name="textureEffect">
        <enum name="none" value="0" />
        <enum name="frost" value="1" />
    </attr>
    <attr name="textureHeight" format="dimension" />
    <attr name="textureWidth" format="dimension" />
    <attr name="theme" format="reference" />
    <attr name="thickness" format="dimension" />
    <attr name="thumbColor" format="color" />
    <attr name="thumbElevation" format="dimension" />
    <attr name="thumbHeight" format="dimension" />
    <attr name="thumbIcon" format="reference" />
    <attr name="thumbIconSize" format="dimension" />
    <attr name="thumbIconTint" format="color" />
    <attr name="thumbIconTintMode">
        <enum name="src_over" value="3" />
        <enum name="src_in" value="5" />
        <enum name="src_atop" value="9" />
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
        <enum name="add" value="16" />
    </attr>
    <attr name="thumbRadius" format="dimension" />
    <attr name="thumbStrokeColor" format="color" />
    <attr name="thumbStrokeWidth" format="dimension" />
    <attr name="thumbTextPadding" format="dimension" />
    <attr name="thumbTint" format="color" />
    <attr name="thumbTintMode">
        <enum name="src_over" value="3" />
        <enum name="src_in" value="5" />
        <enum name="src_atop" value="9" />
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
        <enum name="add" value="16" />
    </attr>
    <attr name="thumbTrackGapSize" format="dimension" />
    <attr name="thumbWidth" format="dimension" />
    <attr name="tickColor" format="color" />
    <attr name="tickColorActive" format="color" />
    <attr name="tickColorInactive" format="color" />
    <attr name="tickMark" format="reference" />
    <attr name="tickMarkTint" format="color" />
    <attr name="tickMarkTintMode">
        <enum name="src_over" value="3" />
        <enum name="src_in" value="5" />
        <enum name="src_atop" value="9" />
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
        <enum name="add" value="16" />
    </attr>
    <attr name="tickRadiusActive" format="dimension" />
    <attr name="tickRadiusInactive" format="dimension" />
    <attr name="tickVisible" format="boolean" />
    <attr name="time_bar_min_update_interval" format="integer" />
    <attr name="tint" format="color" />
    <attr name="tintMode">
        <enum name="src_over" value="3" />
        <enum name="src_in" value="5" />
        <enum name="src_atop" value="9" />
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
        <enum name="add" value="16" />
    </attr>
    <attr name="tintNavigationIcon" format="boolean" />
    <attr name="tips_textColor" format="color" />
    <attr name="tips_textSize" format="dimension" />
    <attr name="tips_textVisibility" format="integer" />
    <attr name="title" format="string" />
    <attr name="titleBackgroundColor" format="color" />
    <attr name="titleCentered" format="boolean" />
    <attr name="titleCollapseMode">
        <enum name="scale" value="0" />
        <enum name="fade" value="1" />
    </attr>
    <attr name="titleEnabled" format="boolean" />
    <attr name="titleGravity">
        <enum name="center" value="0" />
        <enum name="start" value="1" />
    </attr>
    <attr name="titleMargin" format="dimension" />
    <attr name="titleMarginBottom" format="dimension" />
    <attr name="titleMarginEnd" format="dimension" />
    <attr name="titleMarginStart" format="dimension" />
    <attr name="titleMarginTop" format="dimension" />
    <attr name="titleMargins" format="dimension" />
    <attr name="titlePositionInterpolator" format="reference" />
    <attr name="titleText" format="string" />
    <attr name="titleTextAppearance" format="reference" />
    <attr name="titleTextColor" format="color" />
    <attr name="titleTextEllipsize">
        <enum name="start" value="0" />
        <enum name="middle" value="1" />
        <enum name="end" value="2" />
        <enum name="marquee" value="3" />
    </attr>
    <attr name="titleTextSize" format="dimension" />
    <attr name="titleTextStyle" format="reference" />
    <attr name="titleValue" format="reference|string" />
    <attr name="toggleCheckedStateOnClick" format="boolean" />
    <attr name="toolbarId" format="reference" />
    <attr name="toolbarNavigationButtonStyle" format="reference" />
    <attr name="toolbarStyle" format="reference" />
    <attr name="toolbarSurfaceStyle" format="reference" />
    <attr name="tooltipForegroundColor" format="reference|color" />
    <attr name="tooltipFrameBackground" format="reference" />
    <attr name="tooltipStyle" format="reference" />
    <attr name="tooltipText" format="string" />
    <attr name="topInsetScrimEnabled" format="boolean" />
    <attr name="topLeftCornerRadius" format="dimension" />
    <attr name="topLeftRadius" format="dimension" />
    <attr name="topLeftRadiusYL" format="dimension" />
    <attr name="topLeftRadius_xYL" format="dimension" />
    <attr name="topLeftRadius_yYL" format="dimension" />
    <attr name="topRightCornerRadius" format="dimension" />
    <attr name="topRightRadius" format="dimension" />
    <attr name="topRightRadiusYL" format="dimension" />
    <attr name="topRightRadius_xYL" format="dimension" />
    <attr name="topRightRadius_yYL" format="dimension" />
    <attr name="touchAnchorId" format="reference" />
    <attr name="touchAnchorSide">
        <enum name="top" value="0" />
        <enum name="left" value="1" />
        <enum name="right" value="2" />
        <enum name="bottom" value="3" />
        <enum name="middle" value="4" />
        <enum name="start" value="5" />
        <enum name="end" value="6" />
    </attr>
    <attr name="touchRegionId" format="reference" />
    <attr name="touch_target_height" format="dimension" />
    <attr name="track" format="reference" />
    <attr name="trackColor" format="color" />
    <attr name="trackColorActive" format="color" />
    <attr name="trackColorInactive" format="color" />
    <attr name="trackCornerRadius" format="dimension" />
    <attr name="trackDecoration" format="reference" />
    <attr name="trackDecorationTint" format="color" />
    <attr name="trackDecorationTintMode">
        <enum name="src_over" value="3" />
        <enum name="src_in" value="5" />
        <enum name="src_atop" value="9" />
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
        <enum name="add" value="16" />
    </attr>
    <attr name="trackHeight" format="dimension" />
    <attr name="trackInsideCornerSize" format="dimension" />
    <attr name="trackStopIndicatorSize" format="dimension" />
    <attr name="trackThickness" format="dimension" />
    <attr name="trackTint" format="color" />
    <attr name="trackTintMode">
        <enum name="src_over" value="3" />
        <enum name="src_in" value="5" />
        <enum name="src_atop" value="9" />
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
        <enum name="add" value="16" />
    </attr>
    <attr name="transformPivotTarget" format="reference" />
    <attr name="transitionDisable" format="boolean" />
    <attr name="transitionEasing" format="string">
        <enum name="standard" value="0" />
        <enum name="accelerate" value="1" />
        <enum name="decelerate" value="2" />
        <enum name="linear" value="3" />
    </attr>
    <attr name="transitionFlags">
        <flag name="none" value="0x00000000" />
        <flag name="beginOnFirstDraw" value="0x00000001" />
        <flag name="disableIntraAutoTransition" value="0x00000002" />
        <flag name="onInterceptTouchReturnSwipe" value="0x00000004" />
    </attr>
    <attr name="transitionPathRotate" format="float" />
    <attr name="transitionShapeAppearance" format="reference" />
    <attr name="triggerId" format="reference" />
    <attr name="triggerReceiver" format="reference" />
    <attr name="triggerSlack" format="float" />
    <attr name="ttcIndex" format="integer" />
    <attr name="uiCompass" format="boolean" />
    <attr name="uiMapToolbar" format="boolean" />
    <attr name="uiRotateGestures" format="boolean" />
    <attr name="uiScrollGestures" format="boolean" />
    <attr name="uiScrollGesturesDuringRotateOrZoom" format="boolean" />
    <attr name="uiTiltGestures" format="boolean" />
    <attr name="uiZoomControls" format="boolean" />
    <attr name="uiZoomGestures" format="boolean" />
    <attr name="unplayed_color" format="color" />
    <attr name="upDuration" format="integer" />
    <attr name="uri" format="string" />
    <attr name="useCompatPadding" format="boolean" />
    <attr name="useDrawerArrowDrawable" format="boolean" />
    <attr name="useMaterialThemeColors" format="boolean" />
    <attr name="useViewLifecycle" format="boolean" />
    <attr name="use_artwork" format="boolean" />
    <attr name="use_cache" format="boolean" />
    <attr name="use_controller" format="boolean" />
    <attr name="values" format="reference" />
    <attr name="verticalOffset" format="dimension" />
    <attr name="verticalOffsetWithText" format="dimension" />
    <attr name="viewInflaterClass" format="string" />
    <attr name="viewTransitionMode">
        <enum name="currentState" value="0" />
        <enum name="allStates" value="1" />
        <enum name="noState" value="2" />
    </attr>
    <attr name="viewTransitionOnCross" format="reference" />
    <attr name="viewTransitionOnNegativeCross" format="reference" />
    <attr name="viewTransitionOnPositiveCross" format="reference" />
    <attr name="visibilityMode">
        <enum name="normal" value="0" />
        <enum name="ignore" value="1" />
    </attr>
    <attr name="voiceIcon" format="reference" />
    <attr name="warmth" format="float" />
    <attr name="waveDecay" format="integer" />
    <attr name="waveOffset" format="float|dimension" />
    <attr name="wavePeriod" format="float" />
    <attr name="wavePhase" format="float" />
    <attr name="waveShape" format="string">
        <enum name="sin" value="0" />
        <enum name="square" value="1" />
        <enum name="triangle" value="2" />
        <enum name="sawtooth" value="3" />
        <enum name="reverseSawtooth" value="4" />
        <enum name="cos" value="5" />
        <enum name="bounce" value="6" />
    </attr>
    <attr name="waveVariesBy">
        <enum name="position" value="0" />
        <enum name="path" value="1" />
    </attr>
    <attr name="wheelview_dividerColor" format="color" />
    <attr name="wheelview_gravity">
        <enum name="center" value="17" />
        <enum name="left" value="3" />
        <enum name="right" value="5" />
    </attr>
    <attr name="wheelview_lineSpacingMultiplier" format="float" />
    <attr name="wheelview_textColorCenter" format="color" />
    <attr name="wheelview_textColorOut" format="color" />
    <attr name="wheelview_textSize" format="dimension" />
    <attr name="windowActionBar" format="boolean" />
    <attr name="windowActionBarOverlay" format="boolean" />
    <attr name="windowActionModeOverlay" format="boolean" />
    <attr name="windowFixedHeightMajor" format="dimension|fraction" />
    <attr name="windowFixedHeightMinor" format="dimension|fraction" />
    <attr name="windowFixedWidthMajor" format="dimension|fraction" />
    <attr name="windowFixedWidthMinor" format="dimension|fraction" />
    <attr name="windowMinWidthMajor" format="dimension|fraction" />
    <attr name="windowMinWidthMinor" format="dimension|fraction" />
    <attr name="windowNoTitle" format="boolean" />
    <attr name="windowSplashScreenAnimatedIcon" format="reference" />
    <attr name="windowSplashScreenAnimationDuration" format="integer" />
    <attr name="windowSplashScreenBackground" format="color" />
    <attr name="windowSplashScreenIconBackgroundColor" format="color" />
    <attr name="xhg_mask_drawable" format="reference" />
    <attr name="yearSelectedStyle" format="reference" />
    <attr name="yearStyle" format="reference" />
    <attr name="yearTodayStyle" format="reference" />
    <attr name="zOrderOnTop" format="boolean" />
    <attr name="zxing_framing_rect_height" format="dimension" />
    <attr name="zxing_framing_rect_width" format="dimension" />
    <attr name="zxing_possible_result_points" format="color" />
    <attr name="zxing_preview_scaling_strategy">
        <enum name="centerCrop" value="1" />
        <enum name="fitCenter" value="2" />
        <enum name="fitXY" value="3" />
    </attr>
    <attr name="zxing_result_view" format="color" />
    <attr name="zxing_scanner_layout" format="reference" />
    <attr name="zxing_use_texture_view" format="boolean" />
    <attr name="zxing_viewfinder_laser" format="color" />
    <attr name="zxing_viewfinder_laser_visibility" format="boolean" />
    <attr name="zxing_viewfinder_mask" format="color" />
</resources>
