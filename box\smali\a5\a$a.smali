.class public final La5/a$a;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = La5/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation


# static fields
.field public static final a:La5/a;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    new-instance v0, La5/a;

    invoke-direct {v0}, La5/a;-><init>()V

    sput-object v0, La5/a$a;->a:La5/a;

    return-void
.end method

.method public static synthetic a()La5/a;
    .locals 1

    sget-object v0, La5/a$a;->a:La5/a;

    return-object v0
.end method
