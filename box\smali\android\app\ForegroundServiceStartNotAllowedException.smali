.class public final synthetic Landroid/app/ForegroundServiceStartNotAllowedException;
.super Landroid/app/ServiceStartNotAllowedException;

# interfaces
.implements Landroid/os/Parcelable;


# direct methods
.method static synthetic constructor <clinit>()V
    .locals 1

    new-instance v0, Ljava/lang/NoClassDefFoundError;

    invoke-direct {v0}, Ljava/lang/NoClassDefFoundError;-><init>()V

    throw v0
.end method
