.class public interface abstract Landroid/support/v4/media/session/MediaSessionCompat$OnActiveChangeListener;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroid/support/v4/media/session/MediaSessionCompat;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "OnActiveChangeListener"
.end annotation


# virtual methods
.method public abstract onActiveChanged()V
.end method
