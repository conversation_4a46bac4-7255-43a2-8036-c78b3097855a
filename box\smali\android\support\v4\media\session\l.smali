.class public final synthetic Landroid/support/v4/media/session/l;
.super Ljava/lang/Object;


# direct methods
.method public static bridge synthetic a(Landroid/media/session/PlaybackState$Builder;Landroid/os/Bundle;)Landroid/media/session/PlaybackState$Builder;
    .locals 0

    invoke-virtual {p0, p1}, Landroid/media/session/PlaybackState$Builder;->setExtras(Landroid/os/Bundle;)Landroid/media/session/PlaybackState$Builder;

    move-result-object p0

    return-object p0
.end method
