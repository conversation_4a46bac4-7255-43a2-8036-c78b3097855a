.class public final Landroidx/appcompat/R$anim;
.super Ljava/lang/Object;


# static fields
.field public static abc_fade_in:I = 0x7f010000

.field public static abc_fade_out:I = 0x7f010001

.field public static abc_grow_fade_in_from_bottom:I = 0x7f010002

.field public static abc_popup_enter:I = 0x7f010003

.field public static abc_popup_exit:I = 0x7f010004

.field public static abc_shrink_fade_out_from_bottom:I = 0x7f010005

.field public static abc_slide_in_bottom:I = 0x7f010006

.field public static abc_slide_in_top:I = 0x7f010007

.field public static abc_slide_out_bottom:I = 0x7f010008

.field public static abc_slide_out_top:I = 0x7f010009

.field public static abc_tooltip_enter:I = 0x7f01000a

.field public static abc_tooltip_exit:I = 0x7f01000b

.field public static btn_checkbox_to_checked_box_inner_merged_animation:I = 0x7f010011

.field public static btn_checkbox_to_checked_box_outer_merged_animation:I = 0x7f010012

.field public static btn_checkbox_to_checked_icon_null_animation:I = 0x7f010013

.field public static btn_checkbox_to_unchecked_box_inner_merged_animation:I = 0x7f010014

.field public static btn_checkbox_to_unchecked_check_path_merged_animation:I = 0x7f010015

.field public static btn_checkbox_to_unchecked_icon_null_animation:I = 0x7f010016

.field public static btn_radio_to_off_mtrl_dot_group_animation:I = 0x7f010017

.field public static btn_radio_to_off_mtrl_ring_outer_animation:I = 0x7f010018

.field public static btn_radio_to_off_mtrl_ring_outer_path_animation:I = 0x7f010019

.field public static btn_radio_to_on_mtrl_dot_group_animation:I = 0x7f01001a

.field public static btn_radio_to_on_mtrl_ring_outer_animation:I = 0x7f01001b

.field public static btn_radio_to_on_mtrl_ring_outer_path_animation:I = 0x7f01001c


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
