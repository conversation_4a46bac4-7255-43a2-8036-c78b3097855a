.class public final Landroidx/appcompat/R$styleable;
.super Ljava/lang/Object;


# static fields
.field public static ActionBar:[I = null

.field public static ActionBarLayout:[I = null

.field public static ActionBarLayout_android_layout_gravity:I = 0x0

.field public static ActionBar_background:I = 0x0

.field public static ActionBar_backgroundSplit:I = 0x1

.field public static ActionBar_backgroundStacked:I = 0x2

.field public static ActionBar_contentInsetEnd:I = 0x3

.field public static ActionBar_contentInsetEndWithActions:I = 0x4

.field public static ActionBar_contentInsetLeft:I = 0x5

.field public static ActionBar_contentInsetRight:I = 0x6

.field public static ActionBar_contentInsetStart:I = 0x7

.field public static ActionBar_contentInsetStartWithNavigation:I = 0x8

.field public static ActionBar_customNavigationLayout:I = 0x9

.field public static ActionBar_displayOptions:I = 0xa

.field public static ActionBar_divider:I = 0xb

.field public static ActionBar_elevation:I = 0xc

.field public static ActionBar_height:I = 0xd

.field public static ActionBar_hideOnContentScroll:I = 0xe

.field public static ActionBar_homeAsUpIndicator:I = 0xf

.field public static ActionBar_homeLayout:I = 0x10

.field public static ActionBar_icon:I = 0x11

.field public static ActionBar_indeterminateProgressStyle:I = 0x12

.field public static ActionBar_itemPadding:I = 0x13

.field public static ActionBar_logo:I = 0x14

.field public static ActionBar_navigationMode:I = 0x15

.field public static ActionBar_popupTheme:I = 0x16

.field public static ActionBar_progressBarPadding:I = 0x17

.field public static ActionBar_progressBarStyle:I = 0x18

.field public static ActionBar_subtitle:I = 0x19

.field public static ActionBar_subtitleTextStyle:I = 0x1a

.field public static ActionBar_title:I = 0x1b

.field public static ActionBar_titleTextStyle:I = 0x1c

.field public static ActionMenuItemView:[I = null

.field public static ActionMenuItemView_android_minWidth:I = 0x0

.field public static ActionMenuView:[I = null

.field public static ActionMode:[I = null

.field public static ActionMode_background:I = 0x0

.field public static ActionMode_backgroundSplit:I = 0x1

.field public static ActionMode_closeItemLayout:I = 0x2

.field public static ActionMode_height:I = 0x3

.field public static ActionMode_subtitleTextStyle:I = 0x4

.field public static ActionMode_titleTextStyle:I = 0x5

.field public static ActivityChooserView:[I = null

.field public static ActivityChooserView_expandActivityOverflowButtonDrawable:I = 0x0

.field public static ActivityChooserView_initialActivityCount:I = 0x1

.field public static AlertDialog:[I = null

.field public static AlertDialog_android_layout:I = 0x0

.field public static AlertDialog_buttonIconDimen:I = 0x1

.field public static AlertDialog_buttonPanelSideLayout:I = 0x2

.field public static AlertDialog_listItemLayout:I = 0x3

.field public static AlertDialog_listLayout:I = 0x4

.field public static AlertDialog_multiChoiceItemLayout:I = 0x5

.field public static AlertDialog_showTitle:I = 0x6

.field public static AlertDialog_singleChoiceItemLayout:I = 0x7

.field public static AppCompatEmojiHelper:[I = null

.field public static AppCompatImageView:[I = null

.field public static AppCompatImageView_android_src:I = 0x0

.field public static AppCompatImageView_srcCompat:I = 0x1

.field public static AppCompatImageView_tint:I = 0x2

.field public static AppCompatImageView_tintMode:I = 0x3

.field public static AppCompatSeekBar:[I = null

.field public static AppCompatSeekBar_android_thumb:I = 0x0

.field public static AppCompatSeekBar_tickMark:I = 0x1

.field public static AppCompatSeekBar_tickMarkTint:I = 0x2

.field public static AppCompatSeekBar_tickMarkTintMode:I = 0x3

.field public static AppCompatTextHelper:[I = null

.field public static AppCompatTextHelper_android_drawableBottom:I = 0x2

.field public static AppCompatTextHelper_android_drawableEnd:I = 0x6

.field public static AppCompatTextHelper_android_drawableLeft:I = 0x3

.field public static AppCompatTextHelper_android_drawableRight:I = 0x4

.field public static AppCompatTextHelper_android_drawableStart:I = 0x5

.field public static AppCompatTextHelper_android_drawableTop:I = 0x1

.field public static AppCompatTextHelper_android_textAppearance:I = 0x0

.field public static AppCompatTextView:[I = null

.field public static AppCompatTextView_android_textAppearance:I = 0x0

.field public static AppCompatTextView_autoSizeMaxTextSize:I = 0x1

.field public static AppCompatTextView_autoSizeMinTextSize:I = 0x2

.field public static AppCompatTextView_autoSizePresetSizes:I = 0x3

.field public static AppCompatTextView_autoSizeStepGranularity:I = 0x4

.field public static AppCompatTextView_autoSizeTextType:I = 0x5

.field public static AppCompatTextView_drawableBottomCompat:I = 0x6

.field public static AppCompatTextView_drawableEndCompat:I = 0x7

.field public static AppCompatTextView_drawableLeftCompat:I = 0x8

.field public static AppCompatTextView_drawableRightCompat:I = 0x9

.field public static AppCompatTextView_drawableStartCompat:I = 0xa

.field public static AppCompatTextView_drawableTint:I = 0xb

.field public static AppCompatTextView_drawableTintMode:I = 0xc

.field public static AppCompatTextView_drawableTopCompat:I = 0xd

.field public static AppCompatTextView_emojiCompatEnabled:I = 0xe

.field public static AppCompatTextView_firstBaselineToTopHeight:I = 0xf

.field public static AppCompatTextView_fontFamily:I = 0x10

.field public static AppCompatTextView_fontVariationSettings:I = 0x11

.field public static AppCompatTextView_lastBaselineToBottomHeight:I = 0x12

.field public static AppCompatTextView_lineHeight:I = 0x13

.field public static AppCompatTextView_textAllCaps:I = 0x14

.field public static AppCompatTextView_textLocale:I = 0x15

.field public static AppCompatTheme:[I = null

.field public static AppCompatTheme_actionBarDivider:I = 0x2

.field public static AppCompatTheme_actionBarItemBackground:I = 0x3

.field public static AppCompatTheme_actionBarPopupTheme:I = 0x4

.field public static AppCompatTheme_actionBarSize:I = 0x5

.field public static AppCompatTheme_actionBarSplitStyle:I = 0x6

.field public static AppCompatTheme_actionBarStyle:I = 0x7

.field public static AppCompatTheme_actionBarTabBarStyle:I = 0x8

.field public static AppCompatTheme_actionBarTabStyle:I = 0x9

.field public static AppCompatTheme_actionBarTabTextStyle:I = 0xa

.field public static AppCompatTheme_actionBarTheme:I = 0xb

.field public static AppCompatTheme_actionBarWidgetTheme:I = 0xc

.field public static AppCompatTheme_actionButtonStyle:I = 0xd

.field public static AppCompatTheme_actionDropDownStyle:I = 0xe

.field public static AppCompatTheme_actionMenuTextAppearance:I = 0xf

.field public static AppCompatTheme_actionMenuTextColor:I = 0x10

.field public static AppCompatTheme_actionModeBackground:I = 0x11

.field public static AppCompatTheme_actionModeCloseButtonStyle:I = 0x12

.field public static AppCompatTheme_actionModeCloseContentDescription:I = 0x13

.field public static AppCompatTheme_actionModeCloseDrawable:I = 0x14

.field public static AppCompatTheme_actionModeCopyDrawable:I = 0x15

.field public static AppCompatTheme_actionModeCutDrawable:I = 0x16

.field public static AppCompatTheme_actionModeFindDrawable:I = 0x17

.field public static AppCompatTheme_actionModePasteDrawable:I = 0x18

.field public static AppCompatTheme_actionModePopupWindowStyle:I = 0x19

.field public static AppCompatTheme_actionModeSelectAllDrawable:I = 0x1a

.field public static AppCompatTheme_actionModeShareDrawable:I = 0x1b

.field public static AppCompatTheme_actionModeSplitBackground:I = 0x1c

.field public static AppCompatTheme_actionModeStyle:I = 0x1d

.field public static AppCompatTheme_actionModeTheme:I = 0x1e

.field public static AppCompatTheme_actionModeWebSearchDrawable:I = 0x1f

.field public static AppCompatTheme_actionOverflowButtonStyle:I = 0x20

.field public static AppCompatTheme_actionOverflowMenuStyle:I = 0x21

.field public static AppCompatTheme_activityChooserViewStyle:I = 0x22

.field public static AppCompatTheme_alertDialogButtonGroupStyle:I = 0x23

.field public static AppCompatTheme_alertDialogCenterButtons:I = 0x24

.field public static AppCompatTheme_alertDialogStyle:I = 0x25

.field public static AppCompatTheme_alertDialogTheme:I = 0x26

.field public static AppCompatTheme_android_windowAnimationStyle:I = 0x1

.field public static AppCompatTheme_android_windowIsFloating:I = 0x0

.field public static AppCompatTheme_autoCompleteTextViewStyle:I = 0x27

.field public static AppCompatTheme_borderlessButtonStyle:I = 0x28

.field public static AppCompatTheme_buttonBarButtonStyle:I = 0x29

.field public static AppCompatTheme_buttonBarNegativeButtonStyle:I = 0x2a

.field public static AppCompatTheme_buttonBarNeutralButtonStyle:I = 0x2b

.field public static AppCompatTheme_buttonBarPositiveButtonStyle:I = 0x2c

.field public static AppCompatTheme_buttonBarStyle:I = 0x2d

.field public static AppCompatTheme_buttonStyle:I = 0x2e

.field public static AppCompatTheme_buttonStyleSmall:I = 0x2f

.field public static AppCompatTheme_checkboxStyle:I = 0x30

.field public static AppCompatTheme_checkedTextViewStyle:I = 0x31

.field public static AppCompatTheme_colorAccent:I = 0x32

.field public static AppCompatTheme_colorBackgroundFloating:I = 0x33

.field public static AppCompatTheme_colorButtonNormal:I = 0x34

.field public static AppCompatTheme_colorControlActivated:I = 0x35

.field public static AppCompatTheme_colorControlHighlight:I = 0x36

.field public static AppCompatTheme_colorControlNormal:I = 0x37

.field public static AppCompatTheme_colorError:I = 0x38

.field public static AppCompatTheme_colorPrimary:I = 0x39

.field public static AppCompatTheme_colorPrimaryDark:I = 0x3a

.field public static AppCompatTheme_colorSwitchThumbNormal:I = 0x3b

.field public static AppCompatTheme_controlBackground:I = 0x3c

.field public static AppCompatTheme_dialogCornerRadius:I = 0x3d

.field public static AppCompatTheme_dialogPreferredPadding:I = 0x3e

.field public static AppCompatTheme_dialogTheme:I = 0x3f

.field public static AppCompatTheme_dividerHorizontal:I = 0x40

.field public static AppCompatTheme_dividerVertical:I = 0x41

.field public static AppCompatTheme_dropDownListViewStyle:I = 0x42

.field public static AppCompatTheme_dropdownListPreferredItemHeight:I = 0x43

.field public static AppCompatTheme_editTextBackground:I = 0x44

.field public static AppCompatTheme_editTextColor:I = 0x45

.field public static AppCompatTheme_editTextStyle:I = 0x46

.field public static AppCompatTheme_homeAsUpIndicator:I = 0x47

.field public static AppCompatTheme_imageButtonStyle:I = 0x48

.field public static AppCompatTheme_listChoiceBackgroundIndicator:I = 0x49

.field public static AppCompatTheme_listChoiceIndicatorMultipleAnimated:I = 0x4a

.field public static AppCompatTheme_listChoiceIndicatorSingleAnimated:I = 0x4b

.field public static AppCompatTheme_listDividerAlertDialog:I = 0x4c

.field public static AppCompatTheme_listMenuViewStyle:I = 0x4d

.field public static AppCompatTheme_listPopupWindowStyle:I = 0x4e

.field public static AppCompatTheme_listPreferredItemHeight:I = 0x4f

.field public static AppCompatTheme_listPreferredItemHeightLarge:I = 0x50

.field public static AppCompatTheme_listPreferredItemHeightSmall:I = 0x51

.field public static AppCompatTheme_listPreferredItemPaddingEnd:I = 0x52

.field public static AppCompatTheme_listPreferredItemPaddingLeft:I = 0x53

.field public static AppCompatTheme_listPreferredItemPaddingRight:I = 0x54

.field public static AppCompatTheme_listPreferredItemPaddingStart:I = 0x55

.field public static AppCompatTheme_panelBackground:I = 0x56

.field public static AppCompatTheme_panelMenuListTheme:I = 0x57

.field public static AppCompatTheme_panelMenuListWidth:I = 0x58

.field public static AppCompatTheme_popupMenuStyle:I = 0x59

.field public static AppCompatTheme_popupWindowStyle:I = 0x5a

.field public static AppCompatTheme_radioButtonStyle:I = 0x5b

.field public static AppCompatTheme_ratingBarStyle:I = 0x5c

.field public static AppCompatTheme_ratingBarStyleIndicator:I = 0x5d

.field public static AppCompatTheme_ratingBarStyleSmall:I = 0x5e

.field public static AppCompatTheme_searchViewStyle:I = 0x5f

.field public static AppCompatTheme_seekBarStyle:I = 0x60

.field public static AppCompatTheme_selectableItemBackground:I = 0x61

.field public static AppCompatTheme_selectableItemBackgroundBorderless:I = 0x62

.field public static AppCompatTheme_spinnerDropDownItemStyle:I = 0x63

.field public static AppCompatTheme_spinnerStyle:I = 0x64

.field public static AppCompatTheme_switchStyle:I = 0x65

.field public static AppCompatTheme_textAppearanceLargePopupMenu:I = 0x66

.field public static AppCompatTheme_textAppearanceListItem:I = 0x67

.field public static AppCompatTheme_textAppearanceListItemSecondary:I = 0x68

.field public static AppCompatTheme_textAppearanceListItemSmall:I = 0x69

.field public static AppCompatTheme_textAppearancePopupMenuHeader:I = 0x6a

.field public static AppCompatTheme_textAppearanceSearchResultSubtitle:I = 0x6b

.field public static AppCompatTheme_textAppearanceSearchResultTitle:I = 0x6c

.field public static AppCompatTheme_textAppearanceSmallPopupMenu:I = 0x6d

.field public static AppCompatTheme_textColorAlertDialogListItem:I = 0x6e

.field public static AppCompatTheme_textColorSearchUrl:I = 0x6f

.field public static AppCompatTheme_toolbarNavigationButtonStyle:I = 0x70

.field public static AppCompatTheme_toolbarStyle:I = 0x71

.field public static AppCompatTheme_tooltipForegroundColor:I = 0x72

.field public static AppCompatTheme_tooltipFrameBackground:I = 0x73

.field public static AppCompatTheme_viewInflaterClass:I = 0x74

.field public static AppCompatTheme_windowActionBar:I = 0x75

.field public static AppCompatTheme_windowActionBarOverlay:I = 0x76

.field public static AppCompatTheme_windowActionModeOverlay:I = 0x77

.field public static AppCompatTheme_windowFixedHeightMajor:I = 0x78

.field public static AppCompatTheme_windowFixedHeightMinor:I = 0x79

.field public static AppCompatTheme_windowFixedWidthMajor:I = 0x7a

.field public static AppCompatTheme_windowFixedWidthMinor:I = 0x7b

.field public static AppCompatTheme_windowMinWidthMajor:I = 0x7c

.field public static AppCompatTheme_windowMinWidthMinor:I = 0x7d

.field public static AppCompatTheme_windowNoTitle:I = 0x7e

.field public static ButtonBarLayout:[I = null

.field public static ButtonBarLayout_allowStacking:I = 0x0

.field public static CheckedTextView:[I = null

.field public static CheckedTextView_android_checkMark:I = 0x0

.field public static CheckedTextView_checkMarkCompat:I = 0x1

.field public static CheckedTextView_checkMarkTint:I = 0x2

.field public static CheckedTextView_checkMarkTintMode:I = 0x3

.field public static CompoundButton:[I = null

.field public static CompoundButton_android_button:I = 0x0

.field public static CompoundButton_buttonCompat:I = 0x1

.field public static CompoundButton_buttonTint:I = 0x2

.field public static CompoundButton_buttonTintMode:I = 0x3

.field public static DrawerArrowToggle:[I = null

.field public static DrawerArrowToggle_arrowHeadLength:I = 0x0

.field public static DrawerArrowToggle_arrowShaftLength:I = 0x1

.field public static DrawerArrowToggle_barLength:I = 0x2

.field public static DrawerArrowToggle_color:I = 0x3

.field public static DrawerArrowToggle_drawableSize:I = 0x4

.field public static DrawerArrowToggle_gapBetweenBars:I = 0x5

.field public static DrawerArrowToggle_spinBars:I = 0x6

.field public static DrawerArrowToggle_thickness:I = 0x7

.field public static LinearLayoutCompat:[I = null

.field public static LinearLayoutCompat_Layout:[I = null

.field public static LinearLayoutCompat_Layout_android_layout_gravity:I = 0x0

.field public static LinearLayoutCompat_Layout_android_layout_height:I = 0x2

.field public static LinearLayoutCompat_Layout_android_layout_weight:I = 0x3

.field public static LinearLayoutCompat_Layout_android_layout_width:I = 0x1

.field public static LinearLayoutCompat_android_baselineAligned:I = 0x2

.field public static LinearLayoutCompat_android_baselineAlignedChildIndex:I = 0x3

.field public static LinearLayoutCompat_android_gravity:I = 0x0

.field public static LinearLayoutCompat_android_orientation:I = 0x1

.field public static LinearLayoutCompat_android_weightSum:I = 0x4

.field public static LinearLayoutCompat_divider:I = 0x5

.field public static LinearLayoutCompat_dividerPadding:I = 0x6

.field public static LinearLayoutCompat_measureWithLargestChild:I = 0x7

.field public static LinearLayoutCompat_showDividers:I = 0x8

.field public static ListPopupWindow:[I = null

.field public static ListPopupWindow_android_dropDownHorizontalOffset:I = 0x0

.field public static ListPopupWindow_android_dropDownVerticalOffset:I = 0x1

.field public static MenuGroup:[I = null

.field public static MenuGroup_android_checkableBehavior:I = 0x5

.field public static MenuGroup_android_enabled:I = 0x0

.field public static MenuGroup_android_id:I = 0x1

.field public static MenuGroup_android_menuCategory:I = 0x3

.field public static MenuGroup_android_orderInCategory:I = 0x4

.field public static MenuGroup_android_visible:I = 0x2

.field public static MenuItem:[I = null

.field public static MenuItem_actionLayout:I = 0xd

.field public static MenuItem_actionProviderClass:I = 0xe

.field public static MenuItem_actionViewClass:I = 0xf

.field public static MenuItem_alphabeticModifiers:I = 0x10

.field public static MenuItem_android_alphabeticShortcut:I = 0x9

.field public static MenuItem_android_checkable:I = 0xb

.field public static MenuItem_android_checked:I = 0x3

.field public static MenuItem_android_enabled:I = 0x1

.field public static MenuItem_android_icon:I = 0x0

.field public static MenuItem_android_id:I = 0x2

.field public static MenuItem_android_menuCategory:I = 0x5

.field public static MenuItem_android_numericShortcut:I = 0xa

.field public static MenuItem_android_onClick:I = 0xc

.field public static MenuItem_android_orderInCategory:I = 0x6

.field public static MenuItem_android_title:I = 0x7

.field public static MenuItem_android_titleCondensed:I = 0x8

.field public static MenuItem_android_visible:I = 0x4

.field public static MenuItem_contentDescription:I = 0x11

.field public static MenuItem_iconTint:I = 0x12

.field public static MenuItem_iconTintMode:I = 0x13

.field public static MenuItem_numericModifiers:I = 0x14

.field public static MenuItem_showAsAction:I = 0x15

.field public static MenuItem_tooltipText:I = 0x16

.field public static MenuView:[I = null

.field public static MenuView_android_headerBackground:I = 0x4

.field public static MenuView_android_horizontalDivider:I = 0x2

.field public static MenuView_android_itemBackground:I = 0x5

.field public static MenuView_android_itemIconDisabledAlpha:I = 0x6

.field public static MenuView_android_itemTextAppearance:I = 0x1

.field public static MenuView_android_verticalDivider:I = 0x3

.field public static MenuView_android_windowAnimationStyle:I = 0x0

.field public static MenuView_preserveIconSpacing:I = 0x7

.field public static MenuView_subMenuArrow:I = 0x8

.field public static PopupWindow:[I = null

.field public static PopupWindowBackgroundState:[I = null

.field public static PopupWindowBackgroundState_state_above_anchor:I = 0x0

.field public static PopupWindow_android_popupAnimationStyle:I = 0x1

.field public static PopupWindow_android_popupBackground:I = 0x0

.field public static PopupWindow_overlapAnchor:I = 0x2

.field public static RecycleListView:[I = null

.field public static RecycleListView_paddingBottomNoButtons:I = 0x0

.field public static RecycleListView_paddingTopNoTitle:I = 0x1

.field public static SearchView:[I = null

.field public static SearchView_android_focusable:I = 0x1

.field public static SearchView_android_hint:I = 0x4

.field public static SearchView_android_imeOptions:I = 0x6

.field public static SearchView_android_inputType:I = 0x5

.field public static SearchView_android_maxWidth:I = 0x2

.field public static SearchView_android_text:I = 0x3

.field public static SearchView_android_textAppearance:I = 0x0

.field public static SearchView_animateMenuItems:I = 0x7

.field public static SearchView_animateNavigationIcon:I = 0x8

.field public static SearchView_autoShowKeyboard:I = 0x9

.field public static SearchView_backHandlingEnabled:I = 0xa

.field public static SearchView_backgroundTint:I = 0xb

.field public static SearchView_closeIcon:I = 0xc

.field public static SearchView_commitIcon:I = 0xd

.field public static SearchView_defaultQueryHint:I = 0xe

.field public static SearchView_goIcon:I = 0xf

.field public static SearchView_headerLayout:I = 0x10

.field public static SearchView_hideNavigationIcon:I = 0x11

.field public static SearchView_iconifiedByDefault:I = 0x12

.field public static SearchView_layout:I = 0x13

.field public static SearchView_queryBackground:I = 0x14

.field public static SearchView_queryHint:I = 0x15

.field public static SearchView_searchHintIcon:I = 0x16

.field public static SearchView_searchIcon:I = 0x17

.field public static SearchView_searchPrefixText:I = 0x18

.field public static SearchView_submitBackground:I = 0x19

.field public static SearchView_suggestionRowLayout:I = 0x1a

.field public static SearchView_useDrawerArrowDrawable:I = 0x1b

.field public static SearchView_voiceIcon:I = 0x1c

.field public static Spinner:[I = null

.field public static Spinner_android_dropDownWidth:I = 0x3

.field public static Spinner_android_entries:I = 0x0

.field public static Spinner_android_popupBackground:I = 0x1

.field public static Spinner_android_prompt:I = 0x2

.field public static Spinner_popupTheme:I = 0x4

.field public static SwitchCompat:[I = null

.field public static SwitchCompat_android_textOff:I = 0x1

.field public static SwitchCompat_android_textOn:I = 0x0

.field public static SwitchCompat_android_thumb:I = 0x2

.field public static SwitchCompat_showText:I = 0x3

.field public static SwitchCompat_splitTrack:I = 0x4

.field public static SwitchCompat_switchMinWidth:I = 0x5

.field public static SwitchCompat_switchPadding:I = 0x6

.field public static SwitchCompat_switchTextAppearance:I = 0x7

.field public static SwitchCompat_thumbTextPadding:I = 0x8

.field public static SwitchCompat_thumbTint:I = 0x9

.field public static SwitchCompat_thumbTintMode:I = 0xa

.field public static SwitchCompat_track:I = 0xb

.field public static SwitchCompat_trackTint:I = 0xc

.field public static SwitchCompat_trackTintMode:I = 0xd

.field public static TextAppearance:[I = null

.field public static TextAppearance_android_fontFamily:I = 0xa

.field public static TextAppearance_android_shadowColor:I = 0x6

.field public static TextAppearance_android_shadowDx:I = 0x7

.field public static TextAppearance_android_shadowDy:I = 0x8

.field public static TextAppearance_android_shadowRadius:I = 0x9

.field public static TextAppearance_android_textColor:I = 0x3

.field public static TextAppearance_android_textColorHint:I = 0x4

.field public static TextAppearance_android_textColorLink:I = 0x5

.field public static TextAppearance_android_textFontWeight:I = 0xb

.field public static TextAppearance_android_textSize:I = 0x0

.field public static TextAppearance_android_textStyle:I = 0x2

.field public static TextAppearance_android_typeface:I = 0x1

.field public static TextAppearance_fontFamily:I = 0xc

.field public static TextAppearance_fontVariationSettings:I = 0xd

.field public static TextAppearance_textAllCaps:I = 0xe

.field public static TextAppearance_textLocale:I = 0xf

.field public static Toolbar:[I = null

.field public static Toolbar_android_gravity:I = 0x0

.field public static Toolbar_android_minHeight:I = 0x1

.field public static Toolbar_buttonGravity:I = 0x2

.field public static Toolbar_collapseContentDescription:I = 0x3

.field public static Toolbar_collapseIcon:I = 0x4

.field public static Toolbar_contentInsetEnd:I = 0x5

.field public static Toolbar_contentInsetEndWithActions:I = 0x6

.field public static Toolbar_contentInsetLeft:I = 0x7

.field public static Toolbar_contentInsetRight:I = 0x8

.field public static Toolbar_contentInsetStart:I = 0x9

.field public static Toolbar_contentInsetStartWithNavigation:I = 0xa

.field public static Toolbar_logo:I = 0xb

.field public static Toolbar_logoDescription:I = 0xc

.field public static Toolbar_maxButtonHeight:I = 0xd

.field public static Toolbar_menu:I = 0xe

.field public static Toolbar_navigationContentDescription:I = 0xf

.field public static Toolbar_navigationIcon:I = 0x10

.field public static Toolbar_popupTheme:I = 0x11

.field public static Toolbar_subtitle:I = 0x12

.field public static Toolbar_subtitleTextAppearance:I = 0x13

.field public static Toolbar_subtitleTextColor:I = 0x14

.field public static Toolbar_title:I = 0x15

.field public static Toolbar_titleMargin:I = 0x16

.field public static Toolbar_titleMarginBottom:I = 0x17

.field public static Toolbar_titleMarginEnd:I = 0x18

.field public static Toolbar_titleMarginStart:I = 0x19

.field public static Toolbar_titleMarginTop:I = 0x1a

.field public static Toolbar_titleMargins:I = 0x1b

.field public static Toolbar_titleTextAppearance:I = 0x1c

.field public static Toolbar_titleTextColor:I = 0x1d

.field public static View:[I = null

.field public static ViewBackgroundHelper:[I = null

.field public static ViewBackgroundHelper_android_background:I = 0x0

.field public static ViewBackgroundHelper_backgroundTint:I = 0x1

.field public static ViewBackgroundHelper_backgroundTintMode:I = 0x2

.field public static ViewStubCompat:[I = null

.field public static ViewStubCompat_android_id:I = 0x0

.field public static ViewStubCompat_android_inflatedId:I = 0x2

.field public static ViewStubCompat_android_layout:I = 0x1

.field public static View_android_focusable:I = 0x1

.field public static View_android_theme:I = 0x0

.field public static View_paddingEnd:I = 0x2

.field public static View_paddingStart:I = 0x3

.field public static View_theme:I = 0x4


# direct methods
.method public static constructor <clinit>()V
    .locals 8

    const/16 v0, 0x1d

    new-array v1, v0, [I

    fill-array-data v1, :array_0

    sput-object v1, Landroidx/appcompat/R$styleable;->ActionBar:[I

    const v1, 0x10100b3

    filled-new-array {v1}, [I

    move-result-object v2

    sput-object v2, Landroidx/appcompat/R$styleable;->ActionBarLayout:[I

    const v2, 0x101013f

    filled-new-array {v2}, [I

    move-result-object v2

    sput-object v2, Landroidx/appcompat/R$styleable;->ActionMenuItemView:[I

    const/4 v2, 0x0

    new-array v3, v2, [I

    sput-object v3, Landroidx/appcompat/R$styleable;->ActionMenuView:[I

    const/4 v3, 0x6

    new-array v4, v3, [I

    fill-array-data v4, :array_1

    sput-object v4, Landroidx/appcompat/R$styleable;->ActionMode:[I

    const v4, 0x7f040349

    const v5, 0x7f0403f4

    filled-new-array {v4, v5}, [I

    move-result-object v4

    sput-object v4, Landroidx/appcompat/R$styleable;->ActivityChooserView:[I

    const/16 v4, 0x8

    new-array v5, v4, [I

    fill-array-data v5, :array_2

    sput-object v5, Landroidx/appcompat/R$styleable;->AlertDialog:[I

    new-array v2, v2, [I

    sput-object v2, Landroidx/appcompat/R$styleable;->AppCompatEmojiHelper:[I

    const v2, 0x7f04075a

    const v5, 0x7f04075b

    const v6, 0x1010119

    const v7, 0x7f040694

    filled-new-array {v6, v7, v2, v5}, [I

    move-result-object v2

    sput-object v2, Landroidx/appcompat/R$styleable;->AppCompatImageView:[I

    const v2, 0x7f040754

    const v5, 0x7f040755

    const v6, 0x1010142

    const v7, 0x7f040753

    filled-new-array {v6, v7, v2, v5}, [I

    move-result-object v2

    sput-object v2, Landroidx/appcompat/R$styleable;->AppCompatSeekBar:[I

    const/4 v2, 0x7

    new-array v2, v2, [I

    fill-array-data v2, :array_3

    sput-object v2, Landroidx/appcompat/R$styleable;->AppCompatTextHelper:[I

    const/16 v2, 0x16

    new-array v2, v2, [I

    fill-array-data v2, :array_4

    sput-object v2, Landroidx/appcompat/R$styleable;->AppCompatTextView:[I

    const/16 v2, 0x7f

    new-array v2, v2, [I

    fill-array-data v2, :array_5

    sput-object v2, Landroidx/appcompat/R$styleable;->AppCompatTheme:[I

    const v2, 0x7f04003f

    filled-new-array {v2}, [I

    move-result-object v2

    sput-object v2, Landroidx/appcompat/R$styleable;->ButtonBarLayout:[I

    const v2, 0x7f04020c

    const v5, 0x7f04020d

    const v6, 0x1010108

    const v7, 0x7f04020b

    filled-new-array {v6, v7, v2, v5}, [I

    move-result-object v2

    sput-object v2, Landroidx/appcompat/R$styleable;->CheckedTextView:[I

    const v2, 0x7f0401ed

    const v5, 0x7f0401ee

    const v6, 0x1010107

    const v7, 0x7f0401e3

    filled-new-array {v6, v7, v2, v5}, [I

    move-result-object v2

    sput-object v2, Landroidx/appcompat/R$styleable;->CompoundButton:[I

    new-array v2, v4, [I

    fill-array-data v2, :array_6

    sput-object v2, Landroidx/appcompat/R$styleable;->DrawerArrowToggle:[I

    const/16 v2, 0x9

    new-array v4, v2, [I

    fill-array-data v4, :array_7

    sput-object v4, Landroidx/appcompat/R$styleable;->LinearLayoutCompat:[I

    const v4, 0x10100f5

    const v5, 0x1010181

    const v6, 0x10100f4

    filled-new-array {v1, v6, v4, v5}, [I

    move-result-object v1

    sput-object v1, Landroidx/appcompat/R$styleable;->LinearLayoutCompat_Layout:[I

    const v1, 0x10102ac

    const v4, 0x10102ad

    filled-new-array {v1, v4}, [I

    move-result-object v1

    sput-object v1, Landroidx/appcompat/R$styleable;->ListPopupWindow:[I

    new-array v1, v3, [I

    fill-array-data v1, :array_8

    sput-object v1, Landroidx/appcompat/R$styleable;->MenuGroup:[I

    const/16 v1, 0x17

    new-array v1, v1, [I

    fill-array-data v1, :array_9

    sput-object v1, Landroidx/appcompat/R$styleable;->MenuItem:[I

    new-array v1, v2, [I

    fill-array-data v1, :array_a

    sput-object v1, Landroidx/appcompat/R$styleable;->MenuView:[I

    const v1, 0x10102c9

    const v2, 0x7f040594

    const v3, 0x1010176

    filled-new-array {v3, v1, v2}, [I

    move-result-object v1

    sput-object v1, Landroidx/appcompat/R$styleable;->PopupWindow:[I

    const v1, 0x7f0406ae

    filled-new-array {v1}, [I

    move-result-object v1

    sput-object v1, Landroidx/appcompat/R$styleable;->PopupWindowBackgroundState:[I

    const v1, 0x7f040596

    const v2, 0x7f04059d

    filled-new-array {v1, v2}, [I

    move-result-object v1

    sput-object v1, Landroidx/appcompat/R$styleable;->RecycleListView:[I

    new-array v0, v0, [I

    fill-array-data v0, :array_b

    sput-object v0, Landroidx/appcompat/R$styleable;->SearchView:[I

    const v0, 0x1010262

    const v1, 0x7f0405d3

    const v2, 0x10100b2

    const v4, 0x101017b

    filled-new-array {v2, v3, v4, v0, v1}, [I

    move-result-object v0

    sput-object v0, Landroidx/appcompat/R$styleable;->Spinner:[I

    const/16 v0, 0xe

    new-array v0, v0, [I

    fill-array-data v0, :array_c

    sput-object v0, Landroidx/appcompat/R$styleable;->SwitchCompat:[I

    const/16 v0, 0x10

    new-array v0, v0, [I

    fill-array-data v0, :array_d

    sput-object v0, Landroidx/appcompat/R$styleable;->TextAppearance:[I

    const/16 v0, 0x1e

    new-array v0, v0, [I

    fill-array-data v0, :array_e

    sput-object v0, Landroidx/appcompat/R$styleable;->Toolbar:[I

    const v0, 0x7f04059b

    const v1, 0x7f04073f

    const/high16 v2, 0x1010000

    const v3, 0x10100da

    const v4, 0x7f040598

    filled-new-array {v2, v3, v4, v0, v1}, [I

    move-result-object v0

    sput-object v0, Landroidx/appcompat/R$styleable;->View:[I

    const v0, 0x7f040073

    const v1, 0x7f040074

    const v2, 0x10100d4

    filled-new-array {v2, v0, v1}, [I

    move-result-object v0

    sput-object v0, Landroidx/appcompat/R$styleable;->ViewBackgroundHelper:[I

    const v0, 0x10100f2

    const v1, 0x10100f3

    const v2, 0x10100d0

    filled-new-array {v2, v0, v1}, [I

    move-result-object v0

    sput-object v0, Landroidx/appcompat/R$styleable;->ViewStubCompat:[I

    return-void

    nop

    :array_0
    .array-data 4
        0x7f04006a
        0x7f040071
        0x7f040072
        0x7f0402a4
        0x7f0402a5
        0x7f0402a6
        0x7f0402a7
        0x7f0402a8
        0x7f0402a9
        0x7f0402d9
        0x7f0402f4
        0x7f0402f5
        0x7f040318
        0x7f0403bb
        0x7f0403c3
        0x7f0403cb
        0x7f0403cc
        0x7f0403d0
        0x7f0403e4
        0x7f040431
        0x7f0404d6
        0x7f04057d
        0x7f0405d3
        0x7f0405dd
        0x7f0405de
        0x7f0406c5
        0x7f0406c9
        0x7f040760
        0x7f040772
    .end array-data

    :array_1
    .array-data 4
        0x7f04006a
        0x7f040071
        0x7f04024f
        0x7f0403bb
        0x7f0406c9
        0x7f040772
    .end array-data

    :array_2
    .array-data 4
        0x10100f2
        0x7f0401e6
        0x7f0401e9
        0x7f0404ca
        0x7f0404cb
        0x7f040578
        0x7f04066b
        0x7f04067d
    .end array-data

    :array_3
    .array-data 4
        0x1010034
        0x101016d
        0x101016e
        0x101016f
        0x1010170
        0x1010392
        0x1010393
    .end array-data

    :array_4
    .array-data 4
        0x1010034
        0x7f040061
        0x7f040062
        0x7f040063
        0x7f040064
        0x7f040065
        0x7f040304
        0x7f040305
        0x7f040306
        0x7f040307
        0x7f040309
        0x7f04030a
        0x7f04030b
        0x7f04030c
        0x7f04031c
        0x7f04036c
        0x7f04039a
        0x7f0403a3
        0x7f04045f
        0x7f0404c3
        0x7f0406fd
        0x7f040734
    .end array-data

    :array_5
    .array-data 4
        0x1010057
        0x10100ae
        0x7f040008
        0x7f040009
        0x7f04000a
        0x7f04000b
        0x7f04000c
        0x7f04000d
        0x7f04000e
        0x7f04000f
        0x7f040010
        0x7f040011
        0x7f040012
        0x7f040013
        0x7f040014
        0x7f040016
        0x7f040017
        0x7f040018
        0x7f040019
        0x7f04001a
        0x7f04001b
        0x7f04001c
        0x7f04001d
        0x7f04001e
        0x7f04001f
        0x7f040020
        0x7f040021
        0x7f040022
        0x7f040023
        0x7f040024
        0x7f040025
        0x7f040026
        0x7f040027
        0x7f040028
        0x7f04002e
        0x7f040039
        0x7f04003a
        0x7f04003b
        0x7f04003c
        0x7f04005f
        0x7f0401be
        0x7f0401de
        0x7f0401df
        0x7f0401e0
        0x7f0401e1
        0x7f0401e2
        0x7f0401eb
        0x7f0401ec
        0x7f04020e
        0x7f040219
        0x7f04025c
        0x7f04025d
        0x7f04025e
        0x7f040260
        0x7f040261
        0x7f040262
        0x7f040263
        0x7f04027c
        0x7f04027e
        0x7f040294
        0x7f0402b3
        0x7f0402f1
        0x7f0402f2
        0x7f0402f3
        0x7f0402fa
        0x7f0402ff
        0x7f040311
        0x7f040312
        0x7f040315
        0x7f040316
        0x7f040317
        0x7f0403cb
        0x7f0403de
        0x7f0404c6
        0x7f0404c7
        0x7f0404c8
        0x7f0404c9
        0x7f0404cc
        0x7f0404cd
        0x7f0404ce
        0x7f0404cf
        0x7f0404d0
        0x7f0404d1
        0x7f0404d2
        0x7f0404d3
        0x7f0404d4
        0x7f04059f
        0x7f0405a0
        0x7f0405a1
        0x7f0405d2
        0x7f0405d4
        0x7f0405ee
        0x7f0405f1
        0x7f0405f2
        0x7f0405f3
        0x7f04062e
        0x7f040631
        0x7f040632
        0x7f040633
        0x7f040687
        0x7f040688
        0x7f0406d2
        0x7f040714
        0x7f040716
        0x7f040717
        0x7f040718
        0x7f04071a
        0x7f04071b
        0x7f04071c
        0x7f04071d
        0x7f040728
        0x7f040729
        0x7f040776
        0x7f040777
        0x7f040779
        0x7f04077a
        0x7f0407b9
        0x7f0407cd
        0x7f0407ce
        0x7f0407cf
        0x7f0407d0
        0x7f0407d1
        0x7f0407d2
        0x7f0407d3
        0x7f0407d4
        0x7f0407d5
        0x7f0407d6
    .end array-data

    :array_6
    .array-data 4
        0x7f040051
        0x7f040058
        0x7f04009d
        0x7f04025b
        0x7f040308
        0x7f0403aa
        0x7f040686
        0x7f040740
    .end array-data

    :array_7
    .array-data 4
        0x10100af
        0x10100c4
        0x1010126
        0x1010127
        0x1010128
        0x7f0402f5
        0x7f0402fd
        0x7f040538
        0x7f040663
    .end array-data

    :array_8
    .array-data 4
        0x101000e
        0x10100d0
        0x1010194
        0x10101de
        0x10101df
        0x10101e0
    .end array-data

    :array_9
    .array-data 4
        0x1010002
        0x101000e
        0x10100d0
        0x1010106
        0x1010194
        0x10101de
        0x10101df
        0x10101e1
        0x10101e2
        0x10101e3
        0x10101e4
        0x10101e5
        0x101026f
        0x7f040015
        0x7f040029
        0x7f04002b
        0x7f040041
        0x7f0402a3
        0x7f0403d7
        0x7f0403d8
        0x7f04058b
        0x7f04065e
        0x7f04077c
    .end array-data

    :array_a
    .array-data 4
        0x10100ae
        0x101012c
        0x101012d
        0x101012e
        0x101012f
        0x1010130
        0x1010131
        0x7f0405d9
        0x7f0406bf
    .end array-data

    :array_b
    .array-data 4
        0x1010034
        0x10100da
        0x101011f
        0x101014f
        0x1010150
        0x1010220
        0x1010264
        0x7f040047
        0x7f040048
        0x7f040060
        0x7f040068
        0x7f040073
        0x7f040248
        0x7f040299
        0x7f0402e6
        0x7f0403ad
        0x7f0403ba
        0x7f0403c2
        0x7f0403d9
        0x7f040466
        0x7f0405ea
        0x7f0405eb
        0x7f04062b
        0x7f04062c
        0x7f04062d
        0x7f0406c4
        0x7f0406cd
        0x7f0407b0
        0x7f0407bf
    .end array-data

    :array_c
    .array-data 4
        0x1010124
        0x1010125
        0x1010142
        0x7f04066a
        0x7f04068e
        0x7f0406d0
        0x7f0406d1
        0x7f0406d3
        0x7f04074b
        0x7f04074c
        0x7f04074d
        0x7f04078c
        0x7f040798
        0x7f040799
    .end array-data

    :array_d
    .array-data 4
        0x1010095
        0x1010096
        0x1010097
        0x1010098
        0x101009a
        0x101009b
        0x1010161
        0x1010162
        0x1010163
        0x1010164
        0x10103ac
        0x1010585
        0x7f04039a
        0x7f0403a3
        0x7f0406fd
        0x7f040734
    .end array-data

    :array_e
    .array-data 4
        0x10100af
        0x1010140
        0x7f0401e4
        0x7f040250
        0x7f040251
        0x7f0402a4
        0x7f0402a5
        0x7f0402a6
        0x7f0402a7
        0x7f0402a8
        0x7f0402a9
        0x7f0404d6
        0x7f0404d8
        0x7f040528
        0x7f040539
        0x7f04057a
        0x7f04057b
        0x7f0405d3
        0x7f0406c5
        0x7f0406c7
        0x7f0406c8
        0x7f040760
        0x7f040766
        0x7f040767
        0x7f040768
        0x7f040769
        0x7f04076a
        0x7f04076b
        0x7f04076e
        0x7f04076f
    .end array-data
.end method

.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
