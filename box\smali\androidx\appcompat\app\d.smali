.class public interface abstract Landroidx/appcompat/app/d;
.super Ljava/lang/Object;


# virtual methods
.method public abstract onSupportActionModeFinished(Ll/b;)V
.end method

.method public abstract onSupportActionModeStarted(Ll/b;)V
.end method

.method public abstract onWindowStartingSupportActionMode(Ll/b$a;)Ll/b;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end method
