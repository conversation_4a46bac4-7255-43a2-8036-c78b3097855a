.class public interface abstract Landroidx/appcompat/view/menu/l;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Landroidx/appcompat/view/menu/l$a;
    }
.end annotation


# virtual methods
.method public abstract b(Landroidx/appcompat/view/menu/f;Z)V
.end method

.method public abstract c(Landroidx/appcompat/view/menu/f;Landroidx/appcompat/view/menu/h;)Z
.end method

.method public abstract d(Landroidx/appcompat/view/menu/l$a;)V
.end method

.method public abstract e(Landroid/os/Parcelable;)V
.end method

.method public abstract f(Landroidx/appcompat/view/menu/q;)Z
.end method

.method public abstract g()Landroid/os/Parcelable;
.end method

.method public abstract getId()I
.end method

.method public abstract h(Z)V
.end method

.method public abstract i()Z
.end method

.method public abstract j(Landroidx/appcompat/view/menu/f;Landroidx/appcompat/view/menu/h;)Z
.end method

.method public abstract k(Landroid/content/Context;Landroidx/appcompat/view/menu/f;)V
.end method
