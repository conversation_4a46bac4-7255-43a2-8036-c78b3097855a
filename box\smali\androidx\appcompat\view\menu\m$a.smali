.class public interface abstract Landroidx/appcompat/view/menu/m$a;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/appcompat/view/menu/m;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "a"
.end annotation


# virtual methods
.method public abstract getItemData()Landroidx/appcompat/view/menu/h;
.end method

.method public abstract initialize(Landroidx/appcompat/view/menu/h;I)V
.end method

.method public abstract prefersCondensedTitle()Z
.end method
