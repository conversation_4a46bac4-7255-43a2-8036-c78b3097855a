.class public interface abstract Landroidx/appcompat/widget/AppCompatSpinner$g;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/appcompat/widget/AppCompatSpinner;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "g"
.end annotation


# virtual methods
.method public abstract a()Z
.end method

.method public abstract b(Landroid/graphics/drawable/Drawable;)V
.end method

.method public abstract c()I
.end method

.method public abstract dismiss()V
.end method

.method public abstract e(I)V
.end method

.method public abstract f()Ljava/lang/CharSequence;
.end method

.method public abstract g()Landroid/graphics/drawable/Drawable;
.end method

.method public abstract h(Ljava/lang/CharSequence;)V
.end method

.method public abstract i(I)V
.end method

.method public abstract j(I)V
.end method

.method public abstract k(II)V
.end method

.method public abstract l()I
.end method

.method public abstract m(<PERSON>roid/widget/ListAdapter;)V
.end method
