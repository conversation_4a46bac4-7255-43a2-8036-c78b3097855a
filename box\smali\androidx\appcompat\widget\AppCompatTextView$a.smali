.class public interface abstract Landroidx/appcompat/widget/AppCompatTextView$a;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/appcompat/widget/AppCompatTextView;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "a"
.end annotation


# virtual methods
.method public abstract a([II)V
.end method

.method public abstract b(I)V
.end method

.method public abstract c()I
.end method

.method public abstract d()I
.end method

.method public abstract e(IF)V
.end method

.method public abstract f()[I
.end method

.method public abstract g()Landroid/view/textclassifier/TextClassifier;
.end method

.method public abstract h()I
.end method

.method public abstract i(Landroid/view/textclassifier/TextClassifier;)V
    .param p1    # Landroid/view/textclassifier/TextClassifier;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
.end method

.method public abstract j(IIII)V
.end method

.method public abstract k(I)V
.end method

.method public abstract l()I
.end method

.method public abstract m(I)V
.end method
