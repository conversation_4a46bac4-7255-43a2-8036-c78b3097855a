.class public interface abstract Landroidx/appcompat/widget/d0;
.super Ljava/lang/Object;


# virtual methods
.method public abstract d(Landroidx/appcompat/view/menu/f;Landroid/view/MenuItem;)V
    .param p1    # Landroidx/appcompat/view/menu/f;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p2    # Landroid/view/MenuItem;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
.end method

.method public abstract n(Landroidx/appcompat/view/menu/f;Landroid/view/MenuItem;)V
    .param p1    # Landroidx/appcompat/view/menu/f;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p2    # Landroid/view/MenuItem;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
.end method
