.class public interface abstract Landroidx/appcompat/widget/j0;
.super Ljava/lang/Object;

# interfaces
.implements Landroid/widget/SpinnerAdapter;


# virtual methods
.method public abstract getDropDownViewTheme()Landroid/content/res/Resources$Theme;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end method

.method public abstract setDropDownViewTheme(Landroid/content/res/Resources$Theme;)V
    .param p1    # Landroid/content/res/Resources$Theme;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
.end method
