.class public interface abstract Landroidx/appcompat/widget/v;
.super Ljava/lang/Object;


# virtual methods
.method public abstract canShowOverflowMenu()Z
.end method

.method public abstract dismissPopups()V
.end method

.method public abstract hideOverflowMenu()Z
.end method

.method public abstract initFeature(I)V
.end method

.method public abstract isOverflowMenuShowPending()Z
.end method

.method public abstract isOverflowMenuShowing()Z
.end method

.method public abstract setMenu(Landroid/view/Menu;Landroidx/appcompat/view/menu/l$a;)V
.end method

.method public abstract setMenuPrepared()V
.end method

.method public abstract setWindowCallback(Landroid/view/Window$Callback;)V
.end method

.method public abstract setWindowTitle(Ljava/lang/CharSequence;)V
.end method

.method public abstract showOverflowMenu()Z
.end method
