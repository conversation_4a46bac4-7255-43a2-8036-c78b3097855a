.class public interface abstract Landroidx/appcompat/widget/w;
.super Ljava/lang/Object;


# virtual methods
.method public abstract a()Z
.end method

.method public abstract b(I)V
.end method

.method public abstract c()Landroid/view/Menu;
.end method

.method public abstract canShowOverflowMenu()Z
.end method

.method public abstract collapseActionView()V
.end method

.method public abstract d()I
.end method

.method public abstract e(IJ)Landroidx/core/view/ViewPropertyAnimatorCompat;
.end method

.method public abstract f()Z
.end method

.method public abstract g()Landroid/view/ViewGroup;
.end method

.method public abstract getContext()Landroid/content/Context;
.end method

.method public abstract getTitle()Ljava/lang/CharSequence;
.end method

.method public abstract h(Z)V
.end method

.method public abstract hideOverflowMenu()Z
.end method

.method public abstract i()V
.end method

.method public abstract isOverflowMenuShowPending()Z
.end method

.method public abstract isOverflowMenuShowing()Z
.end method

.method public abstract j(Z)V
.end method

.method public abstract k()V
.end method

.method public abstract l(Landroidx/appcompat/widget/ScrollingTabContainerView;)V
.end method

.method public abstract m(Landroid/util/SparseArray;)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/util/SparseArray<",
            "Landroid/os/Parcelable;",
            ">;)V"
        }
    .end annotation
.end method

.method public abstract n(I)V
.end method

.method public abstract o(Landroidx/appcompat/view/menu/l$a;Landroidx/appcompat/view/menu/f$a;)V
.end method

.method public abstract p(Landroid/util/SparseArray;)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/util/SparseArray<",
            "Landroid/os/Parcelable;",
            ">;)V"
        }
    .end annotation
.end method

.method public abstract q()Z
.end method

.method public abstract r()I
.end method

.method public abstract s()V
.end method

.method public abstract setIcon(I)V
.end method

.method public abstract setIcon(Landroid/graphics/drawable/Drawable;)V
.end method

.method public abstract setMenu(Landroid/view/Menu;Landroidx/appcompat/view/menu/l$a;)V
.end method

.method public abstract setMenuPrepared()V
.end method

.method public abstract setVisibility(I)V
.end method

.method public abstract setWindowCallback(Landroid/view/Window$Callback;)V
.end method

.method public abstract setWindowTitle(Ljava/lang/CharSequence;)V
.end method

.method public abstract showOverflowMenu()Z
.end method
