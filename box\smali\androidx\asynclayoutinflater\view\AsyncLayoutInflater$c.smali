.class public Landroidx/asynclayoutinflater/view/AsyncLayoutInflater$c;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/asynclayoutinflater/view/AsyncLayoutInflater;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "c"
.end annotation


# instance fields
.field public a:Landroidx/asynclayoutinflater/view/AsyncLayoutInflater;

.field public b:Landroid/view/ViewGroup;

.field public c:I

.field public d:Landroid/view/View;

.field public e:Landroidx/asynclayoutinflater/view/AsyncLayoutInflater$d;


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
