.class public final Landroidx/autofill/R$id;
.super Ljava/lang/Object;


# static fields
.field public static accessibility_action_clickable_span:I = 0x7f0a0034

.field public static accessibility_custom_action_0:I = 0x7f0a0035

.field public static accessibility_custom_action_1:I = 0x7f0a0036

.field public static accessibility_custom_action_10:I = 0x7f0a0037

.field public static accessibility_custom_action_11:I = 0x7f0a0038

.field public static accessibility_custom_action_12:I = 0x7f0a0039

.field public static accessibility_custom_action_13:I = 0x7f0a003a

.field public static accessibility_custom_action_14:I = 0x7f0a003b

.field public static accessibility_custom_action_15:I = 0x7f0a003c

.field public static accessibility_custom_action_16:I = 0x7f0a003d

.field public static accessibility_custom_action_17:I = 0x7f0a003e

.field public static accessibility_custom_action_18:I = 0x7f0a003f

.field public static accessibility_custom_action_19:I = 0x7f0a0040

.field public static accessibility_custom_action_2:I = 0x7f0a0041

.field public static accessibility_custom_action_20:I = 0x7f0a0042

.field public static accessibility_custom_action_21:I = 0x7f0a0043

.field public static accessibility_custom_action_22:I = 0x7f0a0044

.field public static accessibility_custom_action_23:I = 0x7f0a0045

.field public static accessibility_custom_action_24:I = 0x7f0a0046

.field public static accessibility_custom_action_25:I = 0x7f0a0047

.field public static accessibility_custom_action_26:I = 0x7f0a0048

.field public static accessibility_custom_action_27:I = 0x7f0a0049

.field public static accessibility_custom_action_28:I = 0x7f0a004a

.field public static accessibility_custom_action_29:I = 0x7f0a004b

.field public static accessibility_custom_action_3:I = 0x7f0a004c

.field public static accessibility_custom_action_30:I = 0x7f0a004d

.field public static accessibility_custom_action_31:I = 0x7f0a004e

.field public static accessibility_custom_action_4:I = 0x7f0a004f

.field public static accessibility_custom_action_5:I = 0x7f0a0050

.field public static accessibility_custom_action_6:I = 0x7f0a0051

.field public static accessibility_custom_action_7:I = 0x7f0a0052

.field public static accessibility_custom_action_8:I = 0x7f0a0053

.field public static accessibility_custom_action_9:I = 0x7f0a0054

.field public static action_container:I = 0x7f0a0060

.field public static action_divider:I = 0x7f0a0062

.field public static action_image:I = 0x7f0a0063

.field public static action_text:I = 0x7f0a0069

.field public static actions:I = 0x7f0a006a

.field public static async:I = 0x7f0a00ab

.field public static blocking:I = 0x7f0a00d0

.field public static chronometer:I = 0x7f0a0142

.field public static dialog_button:I = 0x7f0a01c2

.field public static forever:I = 0x7f0a02b7

.field public static icon:I = 0x7f0a030f

.field public static icon_group:I = 0x7f0a0312

.field public static info:I = 0x7f0a0337

.field public static italic:I = 0x7f0a034a

.field public static line1:I = 0x7f0a04e8

.field public static line3:I = 0x7f0a04e9

.field public static normal:I = 0x7f0a06ae

.field public static notification_background:I = 0x7f0a06b7

.field public static notification_main_column:I = 0x7f0a06bc

.field public static notification_main_column_container:I = 0x7f0a06bd

.field public static right_icon:I = 0x7f0a0796

.field public static right_side:I = 0x7f0a0798

.field public static tag_accessibility_actions:I = 0x7f0a08d4

.field public static tag_accessibility_clickable_spans:I = 0x7f0a08d5

.field public static tag_accessibility_heading:I = 0x7f0a08d6

.field public static tag_accessibility_pane_title:I = 0x7f0a08d7

.field public static tag_screen_reader_focusable:I = 0x7f0a08e1

.field public static tag_transition_group:I = 0x7f0a08e3

.field public static tag_unhandled_key_event_manager:I = 0x7f0a08e4

.field public static tag_unhandled_key_listeners:I = 0x7f0a08e5

.field public static text:I = 0x7f0a08ea

.field public static text2:I = 0x7f0a08eb

.field public static time:I = 0x7f0a0902

.field public static title:I = 0x7f0a0908


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
