.class public Landroidx/browser/customtabs/f$a;
.super Landroidx/browser/customtabs/CustomTabsClient;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Landroidx/browser/customtabs/f;->onServiceConnected(Landroid/content/ComponentName;Landroid/os/IBinder;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field public final synthetic d:Landroidx/browser/customtabs/f;


# direct methods
.method public constructor <init>(Landroidx/browser/customtabs/f;Le/b;Landroid/content/ComponentName;Landroid/content/Context;)V
    .locals 0

    iput-object p1, p0, Landroidx/browser/customtabs/f$a;->d:Landroidx/browser/customtabs/f;

    invoke-direct {p0, p2, p3, p4}, Landroidx/browser/customtabs/CustomTabsClient;-><init>(Le/b;Landroid/content/ComponentName;Landroid/content/Context;)V

    return-void
.end method
