.class public final Landroidx/cardview/R$attr;
.super Ljava/lang/Object;


# static fields
.field public static cardBackgroundColor:I = 0x7f0401f6

.field public static cardCornerRadius:I = 0x7f0401f7

.field public static cardElevation:I = 0x7f0401f8

.field public static cardMaxElevation:I = 0x7f0401fa

.field public static cardPreventCornerOverlap:I = 0x7f0401fb

.field public static cardUseCompatPadding:I = 0x7f0401fc

.field public static cardViewStyle:I = 0x7f0401fd

.field public static contentPadding:I = 0x7f0402aa

.field public static contentPaddingBottom:I = 0x7f0402ab

.field public static contentPaddingLeft:I = 0x7f0402ad

.field public static contentPaddingRight:I = 0x7f0402ae

.field public static contentPaddingTop:I = 0x7f0402b0


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
