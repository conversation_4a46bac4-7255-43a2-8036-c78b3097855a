.class public final Landroidx/collection/i;
.super Ljava/lang/Object;


# annotations
.annotation runtime Lkotlin/Metadata;
.end annotation

.annotation build Lkotlin/jvm/internal/SourceDebugExtension;
.end annotation


# static fields
.field public static final a:Landroidx/collection/z;

.field public static final b:[F


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Landroidx/collection/z;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Landroidx/collection/z;-><init>(I)V

    sput-object v0, Landroidx/collection/i;->a:Landroidx/collection/z;

    new-array v0, v1, [F

    sput-object v0, Landroidx/collection/i;->b:[F

    return-void
.end method

.method public static final a()[F
    .locals 1

    sget-object v0, Landroidx/collection/i;->b:[F

    return-object v0
.end method
