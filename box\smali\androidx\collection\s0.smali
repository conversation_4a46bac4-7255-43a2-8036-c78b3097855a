.class public final Landroidx/collection/s0;
.super Ljava/lang/Object;


# annotations
.annotation runtime Lkotlin/Metadata;
.end annotation


# static fields
.field public static final a:Landroidx/collection/l0;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Landroidx/collection/l0<",
            "Ljava/lang/Object;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Landroidx/collection/l0;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Landroidx/collection/l0;-><init>(I)V

    sput-object v0, Landroidx/collection/s0;->a:Landroidx/collection/l0;

    return-void
.end method

.method public static final a()Landroidx/collection/l0;
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<K:",
            "Ljava/lang/Object;",
            ">()",
            "Landroidx/collection/l0<",
            "TK;>;"
        }
    .end annotation

    new-instance v0, Landroidx/collection/l0;

    const/4 v1, 0x1

    const/4 v2, 0x0

    const/4 v3, 0x0

    invoke-direct {v0, v3, v1, v2}, Landroidx/collection/l0;-><init>(IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-object v0
.end method
