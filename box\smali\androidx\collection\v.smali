.class public final Landroidx/collection/v;
.super Ljava/lang/Object;


# annotations
.annotation runtime Lkotlin/Metadata;
.end annotation

.annotation build Lkotlin/jvm/internal/SourceDebugExtension;
.end annotation


# static fields
.field public static final a:Landroidx/collection/h0;

.field public static final b:[J


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Landroidx/collection/h0;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Landroidx/collection/h0;-><init>(I)V

    sput-object v0, Landroidx/collection/v;->a:Landroidx/collection/h0;

    new-array v0, v1, [J

    sput-object v0, Landroidx/collection/v;->b:[J

    return-void
.end method

.method public static final a()[J
    .locals 1

    sget-object v0, Landroidx/collection/v;->b:[J

    return-object v0
.end method
