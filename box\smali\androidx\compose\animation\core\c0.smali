.class public interface abstract Landroidx/compose/animation/core/c0;
.super Ljava/lang/Object;

# interfaces
.implements Landroidx/compose/animation/core/g;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "<T:",
        "Ljava/lang/Object;",
        ">",
        "Ljava/lang/Object;",
        "Landroidx/compose/animation/core/g<",
        "TT;>;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation
