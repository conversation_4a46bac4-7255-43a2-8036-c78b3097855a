.class public final Landroidx/compose/animation/core/c1$a;
.super Ljava/lang/Object;

# interfaces
.implements Landroidx/compose/animation/core/q;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Landroidx/compose/animation/core/c1;-><init>(Landroidx/compose/animation/core/e0;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation


# instance fields
.field public final synthetic a:Landroidx/compose/animation/core/e0;


# direct methods
.method public constructor <init>(Landroidx/compose/animation/core/e0;)V
    .locals 0

    iput-object p1, p0, Landroidx/compose/animation/core/c1$a;->a:Landroidx/compose/animation/core/e0;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public get(I)Landroidx/compose/animation/core/e0;
    .locals 0

    iget-object p1, p0, Landroidx/compose/animation/core/c1$a;->a:Landroidx/compose/animation/core/e0;

    return-object p1
.end method
