.class public interface abstract Landroidx/compose/animation/core/e0;
.super Ljava/lang/Object;

# interfaces
.implements Landroidx/compose/animation/core/g;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Landroidx/compose/animation/core/g<",
        "Ljava/lang/Float;",
        ">;"
    }
.end annotation

.annotation runtime L<PERSON>lin/Metadata;
.end annotation


# virtual methods
.method public abstract a(Landroidx/compose/animation/core/s0;)Landroidx/compose/animation/core/c1;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<V:",
            "Landroidx/compose/animation/core/o;",
            ">(",
            "Landroidx/compose/animation/core/s0<",
            "Ljava/lang/Float;",
            "TV;>;)",
            "Landroidx/compose/animation/core/c1<",
            "TV;>;"
        }
    .end annotation
.end method

.method public abstract b(FFF)F
.end method

.method public abstract c(JFFF)F
.end method

.method public abstract d(JFFF)F
.end method

.method public abstract e(FFF)J
.end method
