.class public final Landroidx/compose/animation/core/l0;
.super Ljava/lang/Object;


# annotations
.annotation runtime L<PERSON>lin/Metadata;
.end annotation

.annotation build Lkotlin/jvm/internal/SourceDebugExtension;
.end annotation


# direct methods
.method public static final a(DDDDD)J
    .locals 12

    const-wide/high16 v0, 0x4000000000000000L    # 2.0

    mul-double v0, v0, p2

    invoke-static {p0, p1}, Ljava/lang/Math;->sqrt(D)D

    move-result-wide v2

    mul-double v0, v0, v2

    mul-double v2, v0, v0

    const-wide/high16 v4, 0x4010000000000000L    # 4.0

    mul-double v4, v4, p0

    sub-double/2addr v2, v4

    neg-double v0, v0

    invoke-static {v2, v3}, Landroidx/compose/animation/core/t;->a(D)Landroidx/compose/animation/core/s;

    move-result-object v4

    invoke-static {v4}, Landroidx/compose/animation/core/s;->b(Landroidx/compose/animation/core/s;)D

    move-result-wide v5

    add-double/2addr v5, v0

    invoke-static {v4, v5, v6}, Landroidx/compose/animation/core/s;->d(Landroidx/compose/animation/core/s;D)V

    invoke-static {v4}, Landroidx/compose/animation/core/s;->b(Landroidx/compose/animation/core/s;)D

    move-result-wide v5

    const-wide/high16 v7, 0x3fe0000000000000L    # 0.5

    mul-double v5, v5, v7

    invoke-static {v4, v5, v6}, Landroidx/compose/animation/core/s;->d(Landroidx/compose/animation/core/s;D)V

    invoke-static {v4}, Landroidx/compose/animation/core/s;->a(Landroidx/compose/animation/core/s;)D

    move-result-wide v5

    mul-double v5, v5, v7

    invoke-static {v4, v5, v6}, Landroidx/compose/animation/core/s;->c(Landroidx/compose/animation/core/s;D)V

    invoke-static {v2, v3}, Landroidx/compose/animation/core/t;->a(D)Landroidx/compose/animation/core/s;

    move-result-object v3

    invoke-static {v3}, Landroidx/compose/animation/core/s;->b(Landroidx/compose/animation/core/s;)D

    move-result-wide v5

    const/4 v2, -0x1

    int-to-double v9, v2

    mul-double v5, v5, v9

    invoke-static {v3, v5, v6}, Landroidx/compose/animation/core/s;->d(Landroidx/compose/animation/core/s;D)V

    invoke-static {v3}, Landroidx/compose/animation/core/s;->a(Landroidx/compose/animation/core/s;)D

    move-result-wide v5

    mul-double v5, v5, v9

    invoke-static {v3, v5, v6}, Landroidx/compose/animation/core/s;->c(Landroidx/compose/animation/core/s;D)V

    invoke-static {v3}, Landroidx/compose/animation/core/s;->b(Landroidx/compose/animation/core/s;)D

    move-result-wide v5

    add-double/2addr v5, v0

    invoke-static {v3, v5, v6}, Landroidx/compose/animation/core/s;->d(Landroidx/compose/animation/core/s;D)V

    invoke-static {v3}, Landroidx/compose/animation/core/s;->b(Landroidx/compose/animation/core/s;)D

    move-result-wide v0

    mul-double v0, v0, v7

    invoke-static {v3, v0, v1}, Landroidx/compose/animation/core/s;->d(Landroidx/compose/animation/core/s;D)V

    invoke-static {v3}, Landroidx/compose/animation/core/s;->a(Landroidx/compose/animation/core/s;)D

    move-result-wide v0

    mul-double v0, v0, v7

    invoke-static {v3, v0, v1}, Landroidx/compose/animation/core/s;->c(Landroidx/compose/animation/core/s;D)V

    move-object v2, v4

    move-wide v4, p2

    move-wide/from16 v6, p4

    move-wide/from16 v8, p6

    move-wide/from16 v10, p8

    invoke-static/range {v2 .. v11}, Landroidx/compose/animation/core/l0;->d(Landroidx/compose/animation/core/s;Landroidx/compose/animation/core/s;DDDD)J

    move-result-wide v0

    return-wide v0
.end method

.method public static final b(FFFFF)J
    .locals 10

    const/4 v0, 0x0

    cmpg-float v0, p1, v0

    if-nez v0, :cond_0

    const-wide p0, 0x8637bd05af6L

    return-wide p0

    :cond_0
    float-to-double v0, p0

    float-to-double v2, p1

    float-to-double v4, p2

    float-to-double v6, p3

    float-to-double v8, p4

    invoke-static/range {v0 .. v9}, Landroidx/compose/animation/core/l0;->a(DDDDD)J

    move-result-wide p0

    return-wide p0
.end method

.method public static final c(Landroidx/compose/animation/core/s;DDD)D
    .locals 20

    move-wide/from16 v0, p5

    invoke-virtual/range {p0 .. p0}, Landroidx/compose/animation/core/s;->f()D

    move-result-wide v2

    mul-double v4, v2, p1

    sub-double v6, p3, v4

    div-double v8, v0, p1

    invoke-static {v8, v9}, Ljava/lang/Math;->abs(D)D

    move-result-wide v8

    invoke-static {v8, v9}, Ljava/lang/Math;->log(D)D

    move-result-wide v8

    div-double/2addr v8, v2

    div-double v10, v0, v6

    invoke-static {v10, v11}, Ljava/lang/Math;->abs(D)D

    move-result-wide v10

    invoke-static {v10, v11}, Ljava/lang/Math;->log(D)D

    move-result-wide v10

    move-wide v14, v10

    const/4 v13, 0x0

    :goto_0
    const/4 v12, 0x6

    if-ge v13, v12, :cond_0

    div-double/2addr v14, v2

    invoke-static {v14, v15}, Ljava/lang/Math;->abs(D)D

    move-result-wide v14

    invoke-static {v14, v15}, Ljava/lang/Math;->log(D)D

    move-result-wide v14

    sub-double v14, v10, v14

    add-int/lit8 v13, v13, 0x1

    goto :goto_0

    :cond_0
    div-double/2addr v14, v2

    invoke-static {v8, v9}, Ljava/lang/Double;->isInfinite(D)Z

    move-result v10

    const/4 v11, 0x1

    if-nez v10, :cond_1

    invoke-static {v8, v9}, Ljava/lang/Double;->isNaN(D)Z

    move-result v10

    if-nez v10, :cond_1

    const/4 v10, 0x1

    goto :goto_1

    :cond_1
    const/4 v10, 0x0

    :goto_1
    xor-int/2addr v10, v11

    if-eqz v10, :cond_2

    move-wide v8, v14

    goto :goto_3

    :cond_2
    invoke-static {v14, v15}, Ljava/lang/Double;->isInfinite(D)Z

    move-result v10

    if-nez v10, :cond_3

    invoke-static {v14, v15}, Ljava/lang/Double;->isNaN(D)Z

    move-result v10

    if-nez v10, :cond_3

    const/4 v10, 0x1

    goto :goto_2

    :cond_3
    const/4 v10, 0x0

    :goto_2
    xor-int/2addr v10, v11

    if-eqz v10, :cond_4

    goto :goto_3

    :cond_4
    invoke-static {v8, v9, v14, v15}, Ljava/lang/Math;->max(DD)D

    move-result-wide v8

    :goto_3
    add-double v12, v4, v6

    neg-double v12, v12

    mul-double v14, v2, v6

    div-double/2addr v12, v14

    mul-double v14, v2, v12

    invoke-static {v14, v15}, Ljava/lang/Math;->exp(D)D

    move-result-wide v16

    mul-double v16, v16, p1

    mul-double v18, v6, v12

    invoke-static {v14, v15}, Ljava/lang/Math;->exp(D)D

    move-result-wide v14

    mul-double v18, v18, v14

    add-double v14, v16, v18

    invoke-static {v12, v13}, Ljava/lang/Double;->isNaN(D)Z

    move-result v10

    if-nez v10, :cond_6

    const-wide/16 v16, 0x0

    cmpg-double v10, v12, v16

    if-gtz v10, :cond_5

    goto :goto_4

    :cond_5
    cmpl-double v10, v12, v16

    if-lez v10, :cond_7

    neg-double v12, v14

    cmpg-double v10, v12, v0

    if-gez v10, :cond_7

    cmpg-double v10, v6, v16

    if-gez v10, :cond_6

    cmpl-double v10, p1, v16

    if-lez v10, :cond_6

    move-wide/from16 v8, v16

    :cond_6
    :goto_4
    neg-double v0, v0

    goto :goto_5

    :cond_7
    const-wide/high16 v8, 0x4000000000000000L    # 2.0

    div-double/2addr v8, v2

    neg-double v8, v8

    div-double v12, p1, v6

    sub-double/2addr v8, v12

    :goto_5
    const-wide v12, 0x7fefffffffffffffL    # Double.MAX_VALUE

    const/4 v10, 0x0

    :goto_6
    const-wide v14, 0x3f50624dd2f1a9fcL    # 0.001

    cmpl-double v16, v12, v14

    if-lez v16, :cond_8

    const/16 v12, 0x64

    if-ge v10, v12, :cond_8

    add-int/lit8 v10, v10, 0x1

    mul-double v12, v6, v8

    add-double v12, p1, v12

    mul-double v14, v2, v8

    invoke-static {v14, v15}, Ljava/lang/Math;->exp(D)D

    move-result-wide v16

    mul-double v12, v12, v16

    add-double/2addr v12, v0

    move-wide/from16 p3, v0

    int-to-double v0, v11

    add-double/2addr v0, v14

    mul-double v0, v0, v6

    add-double/2addr v0, v4

    invoke-static {v14, v15}, Ljava/lang/Math;->exp(D)D

    move-result-wide v14

    mul-double v0, v0, v14

    div-double/2addr v12, v0

    sub-double v0, v8, v12

    sub-double/2addr v8, v0

    invoke-static {v8, v9}, Ljava/lang/Math;->abs(D)D

    move-result-wide v12

    move-wide v8, v0

    move-wide/from16 v0, p3

    goto :goto_6

    :cond_8
    return-wide v8
.end method

.method public static final d(Landroidx/compose/animation/core/s;Landroidx/compose/animation/core/s;DDDD)J
    .locals 10

    move-wide v0, p4

    const-wide/16 v2, 0x0

    cmpg-double v4, p6, v2

    if-nez v4, :cond_0

    cmpg-double v5, v0, v2

    if-nez v5, :cond_0

    const-wide/16 v0, 0x0

    return-wide v0

    :cond_0
    if-gez v4, :cond_1

    neg-double v0, v0

    :cond_1
    invoke-static/range {p6 .. p7}, Ljava/lang/Math;->abs(D)D

    move-result-wide v4

    const-wide/high16 v2, 0x3ff0000000000000L    # 1.0

    cmpl-double v6, p2, v2

    if-lez v6, :cond_2

    move-object v2, p0

    move-object v3, p1

    move-wide v6, v0

    move-wide/from16 v8, p8

    invoke-static/range {v2 .. v9}, Landroidx/compose/animation/core/l0;->e(Landroidx/compose/animation/core/s;Landroidx/compose/animation/core/s;DDD)D

    move-result-wide v0

    goto :goto_0

    :cond_2
    cmpg-double v6, p2, v2

    if-gez v6, :cond_3

    move-wide p1, v4

    move-wide p3, v0

    move-wide/from16 p5, p8

    invoke-static/range {p0 .. p6}, Landroidx/compose/animation/core/l0;->g(Landroidx/compose/animation/core/s;DDD)D

    move-result-wide v0

    goto :goto_0

    :cond_3
    move-wide p1, v4

    move-wide p3, v0

    move-wide/from16 p5, p8

    invoke-static/range {p0 .. p6}, Landroidx/compose/animation/core/l0;->c(Landroidx/compose/animation/core/s;DDD)D

    move-result-wide v0

    :goto_0
    const-wide v2, 0x408f400000000000L    # 1000.0

    mul-double v0, v0, v2

    double-to-long v0, v0

    return-wide v0
.end method

.method public static final e(Landroidx/compose/animation/core/s;Landroidx/compose/animation/core/s;DDD)D
    .locals 29

    move-wide/from16 v0, p6

    invoke-virtual/range {p0 .. p0}, Landroidx/compose/animation/core/s;->f()D

    move-result-wide v12

    invoke-virtual/range {p1 .. p1}, Landroidx/compose/animation/core/s;->f()D

    move-result-wide v14

    mul-double v2, v12, p2

    sub-double v2, v2, p4

    sub-double v16, v12, v14

    div-double v10, v2, v16

    sub-double v18, p2, v10

    div-double v2, v0, v18

    invoke-static {v2, v3}, Ljava/lang/Math;->abs(D)D

    move-result-wide v2

    invoke-static {v2, v3}, Ljava/lang/Math;->log(D)D

    move-result-wide v2

    div-double/2addr v2, v12

    div-double v4, v0, v10

    invoke-static {v4, v5}, Ljava/lang/Math;->abs(D)D

    move-result-wide v4

    invoke-static {v4, v5}, Ljava/lang/Math;->log(D)D

    move-result-wide v4

    div-double/2addr v4, v14

    invoke-static {v2, v3}, Ljava/lang/Double;->isInfinite(D)Z

    move-result v6

    const/16 v20, 0x0

    const/4 v7, 0x1

    if-nez v6, :cond_0

    invoke-static {v2, v3}, Ljava/lang/Double;->isNaN(D)Z

    move-result v6

    if-nez v6, :cond_0

    const/4 v6, 0x1

    goto :goto_0

    :cond_0
    const/4 v6, 0x0

    :goto_0
    xor-int/2addr v6, v7

    if-eqz v6, :cond_1

    move-wide/from16 v21, v4

    goto :goto_3

    :cond_1
    invoke-static {v4, v5}, Ljava/lang/Double;->isInfinite(D)Z

    move-result v6

    if-nez v6, :cond_2

    invoke-static {v4, v5}, Ljava/lang/Double;->isNaN(D)Z

    move-result v6

    if-nez v6, :cond_2

    const/4 v6, 0x1

    goto :goto_1

    :cond_2
    const/4 v6, 0x0

    :goto_1
    xor-int/2addr v6, v7

    if-eqz v6, :cond_3

    :goto_2
    move-wide/from16 v21, v2

    goto :goto_3

    :cond_3
    invoke-static {v2, v3, v4, v5}, Ljava/lang/Math;->max(DD)D

    move-result-wide v2

    goto :goto_2

    :goto_3
    mul-double v23, v18, v12

    neg-double v2, v10

    mul-double v2, v2, v14

    div-double v2, v23, v2

    invoke-static {v2, v3}, Ljava/lang/Math;->log(D)D

    move-result-wide v2

    sub-double v4, v14, v12

    div-double v6, v2, v4

    invoke-static {v6, v7}, Ljava/lang/Double;->isNaN(D)Z

    move-result v2

    if-nez v2, :cond_4

    const-wide/16 v25, 0x0

    cmpg-double v2, v6, v25

    if-gtz v2, :cond_5

    :cond_4
    move-wide/from16 v27, v10

    goto :goto_4

    :cond_5
    cmpl-double v2, v6, v25

    if-lez v2, :cond_7

    move-wide/from16 v2, v18

    move-wide v4, v12

    move-wide v8, v10

    move-wide/from16 v27, v10

    move-wide v10, v14

    invoke-static/range {v2 .. v11}, Landroidx/compose/animation/core/l0;->f(DDDDD)D

    move-result-wide v2

    neg-double v2, v2

    cmpg-double v4, v2, v0

    if-gez v4, :cond_8

    cmpl-double v2, v27, v25

    if-lez v2, :cond_6

    cmpg-double v2, v18, v25

    if-gez v2, :cond_6

    move-wide/from16 v21, v25

    :cond_6
    :goto_4
    neg-double v0, v0

    goto :goto_5

    :cond_7
    move-wide/from16 v27, v10

    :cond_8
    mul-double v10, v27, v14

    mul-double v10, v10, v14

    neg-double v2, v10

    mul-double v4, v23, v12

    div-double/2addr v2, v4

    invoke-static {v2, v3}, Ljava/lang/Math;->log(D)D

    move-result-wide v2

    div-double v21, v2, v16

    :goto_5
    mul-double v2, v12, v21

    invoke-static {v2, v3}, Ljava/lang/Math;->exp(D)D

    move-result-wide v2

    mul-double v2, v2, v23

    mul-double v10, v27, v14

    mul-double v4, v14, v21

    invoke-static {v4, v5}, Ljava/lang/Math;->exp(D)D

    move-result-wide v4

    mul-double v4, v4, v10

    add-double/2addr v2, v4

    invoke-static {v2, v3}, Ljava/lang/Math;->abs(D)D

    move-result-wide v2

    const-wide v4, 0x3f1a36e2eb1c432dL    # 1.0E-4

    cmpg-double v6, v2, v4

    if-gez v6, :cond_9

    return-wide v21

    :cond_9
    const-wide v2, 0x7fefffffffffffffL    # Double.MAX_VALUE

    const/4 v4, 0x0

    :goto_6
    const-wide v5, 0x3f50624dd2f1a9fcL    # 0.001

    cmpl-double v7, v2, v5

    if-lez v7, :cond_a

    const/16 v2, 0x64

    if-ge v4, v2, :cond_a

    add-int/lit8 v4, v4, 0x1

    mul-double v2, v12, v21

    invoke-static {v2, v3}, Ljava/lang/Math;->exp(D)D

    move-result-wide v5

    mul-double v5, v5, v18

    mul-double v7, v14, v21

    invoke-static {v7, v8}, Ljava/lang/Math;->exp(D)D

    move-result-wide v16

    mul-double v16, v16, v27

    add-double v5, v5, v16

    add-double/2addr v5, v0

    invoke-static {v2, v3}, Ljava/lang/Math;->exp(D)D

    move-result-wide v2

    mul-double v2, v2, v23

    invoke-static {v7, v8}, Ljava/lang/Math;->exp(D)D

    move-result-wide v7

    mul-double v7, v7, v10

    add-double/2addr v2, v7

    div-double/2addr v5, v2

    sub-double v2, v21, v5

    sub-double v21, v21, v2

    invoke-static/range {v21 .. v22}, Ljava/lang/Math;->abs(D)D

    move-result-wide v5

    move-wide/from16 v21, v2

    move-wide v2, v5

    goto :goto_6

    :cond_a
    return-wide v21
.end method

.method public static final f(DDDDD)D
    .locals 0

    mul-double p2, p2, p4

    invoke-static {p2, p3}, Ljava/lang/Math;->exp(D)D

    move-result-wide p2

    mul-double p0, p0, p2

    mul-double p8, p8, p4

    invoke-static {p8, p9}, Ljava/lang/Math;->exp(D)D

    move-result-wide p2

    mul-double p6, p6, p2

    add-double/2addr p0, p6

    return-wide p0
.end method

.method public static final g(Landroidx/compose/animation/core/s;DDD)D
    .locals 4

    invoke-virtual {p0}, Landroidx/compose/animation/core/s;->f()D

    move-result-wide v0

    mul-double v2, v0, p1

    sub-double/2addr p3, v2

    invoke-virtual {p0}, Landroidx/compose/animation/core/s;->e()D

    move-result-wide v2

    div-double/2addr p3, v2

    mul-double p1, p1, p1

    mul-double p3, p3, p3

    add-double/2addr p1, p3

    invoke-static {p1, p2}, Ljava/lang/Math;->sqrt(D)D

    move-result-wide p0

    div-double/2addr p5, p0

    invoke-static {p5, p6}, Ljava/lang/Math;->log(D)D

    move-result-wide p0

    div-double/2addr p0, v0

    return-wide p0
.end method
