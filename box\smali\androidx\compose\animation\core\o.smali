.class public abstract Landroidx/compose/animation/core/o;
.super Ljava/lang/Object;


# annotations
.annotation runtime L<PERSON>lin/Metadata;
.end annotation


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public synthetic constructor <init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    invoke-direct {p0}, Landroidx/compose/animation/core/o;-><init>()V

    return-void
.end method


# virtual methods
.method public abstract a(I)F
.end method

.method public abstract b()I
.end method

.method public abstract c()Landroidx/compose/animation/core/o;
.end method

.method public abstract d()V
.end method

.method public abstract e(IF)V
.end method
