.class public final Landroidx/compose/animation/core/s;
.super Ljava/lang/Object;


# annotations
.annotation runtime Lkotlin/Metadata;
.end annotation

.annotation build Lkotlin/jvm/internal/SourceDebugExtension;
.end annotation


# instance fields
.field public a:D

.field public b:D


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(DD)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-wide p1, p0, Landroidx/compose/animation/core/s;->a:D

    iput-wide p3, p0, Landroidx/compose/animation/core/s;->b:D

    return-void
.end method

.method public static final synthetic a(Landroidx/compose/animation/core/s;)D
    .locals 2

    iget-wide v0, p0, Landroidx/compose/animation/core/s;->b:D

    return-wide v0
.end method

.method public static final synthetic b(Landroidx/compose/animation/core/s;)D
    .locals 2

    iget-wide v0, p0, Landroidx/compose/animation/core/s;->a:D

    return-wide v0
.end method

.method public static final synthetic c(Landroidx/compose/animation/core/s;D)V
    .locals 0

    iput-wide p1, p0, Landroidx/compose/animation/core/s;->b:D

    return-void
.end method

.method public static final synthetic d(Landroidx/compose/animation/core/s;D)V
    .locals 0

    iput-wide p1, p0, Landroidx/compose/animation/core/s;->a:D

    return-void
.end method


# virtual methods
.method public final e()D
    .locals 2

    iget-wide v0, p0, Landroidx/compose/animation/core/s;->b:D

    return-wide v0
.end method

.method public equals(Ljava/lang/Object;)Z
    .locals 7

    const/4 v0, 0x1

    if-ne p0, p1, :cond_0

    return v0

    :cond_0
    instance-of v1, p1, Landroidx/compose/animation/core/s;

    const/4 v2, 0x0

    if-nez v1, :cond_1

    return v2

    :cond_1
    check-cast p1, Landroidx/compose/animation/core/s;

    iget-wide v3, p0, Landroidx/compose/animation/core/s;->a:D

    iget-wide v5, p1, Landroidx/compose/animation/core/s;->a:D

    invoke-static {v3, v4, v5, v6}, Ljava/lang/Double;->compare(DD)I

    move-result v1

    if-eqz v1, :cond_2

    return v2

    :cond_2
    iget-wide v3, p0, Landroidx/compose/animation/core/s;->b:D

    iget-wide v5, p1, Landroidx/compose/animation/core/s;->b:D

    invoke-static {v3, v4, v5, v6}, Ljava/lang/Double;->compare(DD)I

    move-result p1

    if-eqz p1, :cond_3

    return v2

    :cond_3
    return v0
.end method

.method public final f()D
    .locals 2

    iget-wide v0, p0, Landroidx/compose/animation/core/s;->a:D

    return-wide v0
.end method

.method public hashCode()I
    .locals 3

    iget-wide v0, p0, Landroidx/compose/animation/core/s;->a:D

    invoke-static {v0, v1}, Landroidx/compose/animation/core/r;->a(D)I

    move-result v0

    mul-int/lit8 v0, v0, 0x1f

    iget-wide v1, p0, Landroidx/compose/animation/core/s;->b:D

    invoke-static {v1, v2}, Landroidx/compose/animation/core/r;->a(D)I

    move-result v1

    add-int/2addr v0, v1

    return v0
.end method

.method public toString()Ljava/lang/String;
    .locals 3

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "ComplexDouble(_real="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-wide v1, p0, Landroidx/compose/animation/core/s;->a:D

    invoke-virtual {v0, v1, v2}, Ljava/lang/StringBuilder;->append(D)Ljava/lang/StringBuilder;

    const-string v1, ", _imaginary="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-wide v1, p0, Landroidx/compose/animation/core/s;->b:D

    invoke-virtual {v0, v1, v2}, Ljava/lang/StringBuilder;->append(D)Ljava/lang/StringBuilder;

    const/16 v1, 0x29

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
