.class public interface abstract Landroidx/compose/animation/core/z0;
.super Ljava/lang/Object;

# interfaces
.implements Landroidx/compose/animation/core/b1;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "<V:",
        "Landroidx/compose/animation/core/o;",
        ">",
        "Ljava/lang/Object;",
        "Landroidx/compose/animation/core/b1<",
        "TV;>;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation


# virtual methods
.method public abstract b()I
.end method

.method public abstract d()I
.end method
