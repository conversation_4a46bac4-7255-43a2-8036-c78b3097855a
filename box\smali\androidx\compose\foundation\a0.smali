.class public final Landroidx/compose/foundation/a0;
.super Landroidx/compose/ui/node/i;


# annotations
.annotation runtime Lkotlin/Metadata;
.end annotation


# instance fields
.field public p:Landroidx/compose/ui/node/f;


# direct methods
.method public constructor <init>(Landroidx/compose/ui/node/f;)V
    .locals 0

    invoke-direct {p0}, Landroidx/compose/ui/node/i;-><init>()V

    iput-object p1, p0, Landroidx/compose/foundation/a0;->p:Landroidx/compose/ui/node/f;

    invoke-virtual {p0, p1}, Landroidx/compose/ui/node/i;->J1(Landroidx/compose/ui/node/f;)Landroidx/compose/ui/node/f;

    return-void
.end method


# virtual methods
.method public final P1(Landroidx/compose/ui/node/f;)V
    .locals 1

    iget-object v0, p0, Landroidx/compose/foundation/a0;->p:Landroidx/compose/ui/node/f;

    invoke-virtual {p0, v0}, Landroidx/compose/ui/node/i;->M1(Landroidx/compose/ui/node/f;)V

    iput-object p1, p0, Landroidx/compose/foundation/a0;->p:Landroidx/compose/ui/node/f;

    invoke-virtual {p0, p1}, Landroidx/compose/ui/node/i;->J1(Landroidx/compose/ui/node/f;)Landroidx/compose/ui/node/f;

    return-void
.end method
