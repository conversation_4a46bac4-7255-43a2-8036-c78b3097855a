.class public final Landroidx/compose/foundation/c0;
.super Ljava/lang/Object;

# interfaces
.implements Landroidx/compose/foundation/y;


# annotations
.annotation runtime Lkotlin/Metadata;
.end annotation


# static fields
.field public static final a:Landroidx/compose/foundation/c0;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    new-instance v0, Landroidx/compose/foundation/c0;

    invoke-direct {v0}, Landroidx/compose/foundation/c0;-><init>()V

    sput-object v0, Landroidx/compose/foundation/c0;->a:Landroidx/compose/foundation/c0;

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public a(Le0/c;)V
    .locals 0

    invoke-interface {p1}, Le0/c;->c1()V

    return-void
.end method
