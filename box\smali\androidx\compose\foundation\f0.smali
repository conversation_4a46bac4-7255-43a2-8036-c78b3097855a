.class public interface abstract Landroidx/compose/foundation/f0;
.super Ljava/lang/Object;


# annotations
.annotation runtime L<PERSON><PERSON>/Metadata;
.end annotation


# virtual methods
.method public abstract a()Z
.end method

.method public abstract b()Landroidx/compose/ui/f;
.end method

.method public abstract c(JILkotlin/jvm/functions/Function1;)J
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(JI",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Ld0/g;",
            "Ld0/g;",
            ">;)J"
        }
    .end annotation
.end method

.method public abstract d(JLkotlin/jvm/functions/Function2;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(J",
            "Lkotlin/jvm/functions/Function2<",
            "-",
            "Lv0/z;",
            "-",
            "Lkotlin/coroutines/Continuation<",
            "-",
            "Lv0/z;",
            ">;+",
            "Ljava/lang/Object;",
            ">;",
            "Lkotlin/coroutines/Continuation<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation
.end method
