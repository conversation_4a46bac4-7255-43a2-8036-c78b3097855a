.class public final synthetic Landroidx/compose/foundation/gestures/c;
.super Ljava/lang/Object;


# direct methods
.method public static a(Landroidx/compose/foundation/gestures/d;FFF)F
    .locals 0

    sget-object p0, Landroidx/compose/foundation/gestures/d;->a:Landroidx/compose/foundation/gestures/d$a;

    invoke-virtual {p0, p1, p2, p3}, Landroidx/compose/foundation/gestures/d$a;->a(FFF)F

    move-result p0

    return p0
.end method

.method public static b(Landroidx/compose/foundation/gestures/d;)Landroidx/compose/animation/core/g;
    .locals 0

    sget-object p0, Landroidx/compose/foundation/gestures/d;->a:Landroidx/compose/foundation/gestures/d$a;

    invoke-virtual {p0}, Landroidx/compose/foundation/gestures/d$a;->c()Landroidx/compose/animation/core/g;

    move-result-object p0

    return-object p0
.end method
