.class public final Landroidx/compose/foundation/gestures/d$a$a;
.super Ljava/lang/Object;

# interfaces
.implements Landroidx/compose/foundation/gestures/d;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/compose/foundation/gestures/d$a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public synthetic a(FFF)F
    .locals 0

    invoke-static {p0, p1, p2, p3}, Landroidx/compose/foundation/gestures/c;->a(Landroidx/compose/foundation/gestures/d;FFF)F

    move-result p1

    return p1
.end method

.method public synthetic b()Landroidx/compose/animation/core/g;
    .locals 1

    invoke-static {p0}, Landroidx/compose/foundation/gestures/c;->b(Landroidx/compose/foundation/gestures/d;)Landroidx/compose/animation/core/g;

    move-result-object v0

    return-object v0
.end method
