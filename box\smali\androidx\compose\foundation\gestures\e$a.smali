.class public final Landroidx/compose/foundation/gestures/e$a;
.super Landroidx/compose/foundation/gestures/e;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/compose/foundation/gestures/e;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation


# static fields
.field public static final a:Landroidx/compose/foundation/gestures/e$a;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    new-instance v0, Landroidx/compose/foundation/gestures/e$a;

    invoke-direct {v0}, Landroidx/compose/foundation/gestures/e$a;-><init>()V

    sput-object v0, Landroidx/compose/foundation/gestures/e$a;->a:Landroidx/compose/foundation/gestures/e$a;

    return-void
.end method

.method public constructor <init>()V
    .locals 1

    const/4 v0, 0x0

    invoke-direct {p0, v0}, Landroidx/compose/foundation/gestures/e;-><init>(<PERSON><PERSON><PERSON>/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method
