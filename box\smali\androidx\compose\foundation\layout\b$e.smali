.class public interface abstract Landroidx/compose/foundation/layout/b$e;
.super Ljava/lang/Object;

# interfaces
.implements Landroidx/compose/foundation/layout/b$d;
.implements Landroidx/compose/foundation/layout/b$k;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/compose/foundation/layout/b;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "e"
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation

.annotation build Lkotlin/jvm/internal/SourceDebugExtension;
.end annotation
