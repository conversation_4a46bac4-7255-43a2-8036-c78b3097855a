.class public final Landroidx/compose/foundation/layout/e;
.super Landroidx/compose/ui/f$c;

# interfaces
.implements Landroidx/compose/ui/node/a1;


# annotations
.annotation runtime Lkotlin/Metadata;
.end annotation


# instance fields
.field public n:Landroidx/compose/ui/b;

.field public o:Z


# direct methods
.method public constructor <init>(Landroidx/compose/ui/b;Z)V
    .locals 0

    invoke-direct {p0}, Landroidx/compose/ui/f$c;-><init>()V

    iput-object p1, p0, Landroidx/compose/foundation/layout/e;->n:Landroidx/compose/ui/b;

    iput-boolean p2, p0, Landroidx/compose/foundation/layout/e;->o:Z

    return-void
.end method


# virtual methods
.method public final J1()Landroidx/compose/ui/b;
    .locals 1

    iget-object v0, p0, Landroidx/compose/foundation/layout/e;->n:Landroidx/compose/ui/b;

    return-object v0
.end method

.method public final K1()Z
    .locals 1

    iget-boolean v0, p0, Landroidx/compose/foundation/layout/e;->o:Z

    return v0
.end method

.method public L1(Lv0/e;Ljava/lang/Object;)Landroidx/compose/foundation/layout/e;
    .locals 0

    return-object p0
.end method

.method public final M1(Landroidx/compose/ui/b;)V
    .locals 0

    iput-object p1, p0, Landroidx/compose/foundation/layout/e;->n:Landroidx/compose/ui/b;

    return-void
.end method

.method public final N1(Z)V
    .locals 0

    iput-boolean p1, p0, Landroidx/compose/foundation/layout/e;->o:Z

    return-void
.end method

.method public bridge synthetic v(Lv0/e;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    invoke-virtual {p0, p1, p2}, Landroidx/compose/foundation/layout/e;->L1(Lv0/e;Ljava/lang/Object;)Landroidx/compose/foundation/layout/e;

    move-result-object p1

    return-object p1
.end method
