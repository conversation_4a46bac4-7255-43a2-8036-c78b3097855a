.class public interface abstract Landroidx/compose/foundation/layout/h0;
.super Ljava/lang/Object;


# annotations
.annotation runtime L<PERSON><PERSON>/Metadata;
.end annotation


# virtual methods
.method public abstract a(Lv0/e;Landroidx/compose/ui/unit/LayoutDirection;)I
.end method

.method public abstract b(Lv0/e;Landroidx/compose/ui/unit/LayoutDirection;)I
.end method

.method public abstract c(Lv0/e;)I
.end method

.method public abstract d(Lv0/e;)I
.end method
