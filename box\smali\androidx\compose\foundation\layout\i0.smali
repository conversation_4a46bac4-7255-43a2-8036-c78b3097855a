.class public final Landroidx/compose/foundation/layout/i0;
.super Ljava/lang/Object;


# annotations
.annotation runtime Lkotlin/Metadata;
.end annotation

.annotation build Lkotlin/jvm/internal/SourceDebugExtension;
.end annotation


# direct methods
.method public static final a(IIII)Landroidx/compose/foundation/layout/h0;
    .locals 1

    new-instance v0, Landroidx/compose/foundation/layout/o;

    invoke-direct {v0, p0, p1, p2, p3}, Landroidx/compose/foundation/layout/o;-><init>(IIII)V

    return-object v0
.end method

.method public static final b(Landroidx/compose/foundation/layout/h0;Landroidx/compose/foundation/layout/h0;)Landroidx/compose/foundation/layout/h0;
    .locals 1

    new-instance v0, Landroidx/compose/foundation/layout/n;

    invoke-direct {v0, p0, p1}, Landroidx/compose/foundation/layout/n;-><init>(Landroidx/compose/foundation/layout/h0;Landroidx/compose/foundation/layout/h0;)V

    return-object v0
.end method

.method public static final c(Landroidx/compose/foundation/layout/h0;Landroidx/compose/foundation/layout/h0;)Landroidx/compose/foundation/layout/h0;
    .locals 1

    new-instance v0, Landroidx/compose/foundation/layout/f0;

    invoke-direct {v0, p0, p1}, Landroidx/compose/foundation/layout/f0;-><init>(Landroidx/compose/foundation/layout/h0;Landroidx/compose/foundation/layout/h0;)V

    return-object v0
.end method
