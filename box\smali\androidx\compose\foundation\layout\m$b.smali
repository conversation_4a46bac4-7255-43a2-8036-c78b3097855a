.class public final Landroidx/compose/foundation/layout/m$b;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/compose/foundation/layout/m;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "b"
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public synthetic constructor <init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    invoke-direct {p0}, Landroidx/compose/foundation/layout/m$b;-><init>()V

    return-void
.end method


# virtual methods
.method public final a(Landroidx/compose/ui/b$b;)Landroidx/compose/foundation/layout/m;
    .locals 1

    new-instance v0, Landroidx/compose/foundation/layout/m$d;

    invoke-direct {v0, p1}, Landroidx/compose/foundation/layout/m$d;-><init>(Landroidx/compose/ui/b$b;)V

    return-object v0
.end method
