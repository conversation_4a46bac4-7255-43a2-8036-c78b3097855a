.class public abstract Landroidx/compose/foundation/layout/m;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Landroidx/compose/foundation/layout/m$a;,
        Landroidx/compose/foundation/layout/m$b;,
        Landroidx/compose/foundation/layout/m$c;,
        Landroidx/compose/foundation/layout/m$d;,
        Landroidx/compose/foundation/layout/m$e;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation


# static fields
.field public static final a:Landroidx/compose/foundation/layout/m$b;

.field public static final b:Landroidx/compose/foundation/layout/m;

.field public static final c:Landroidx/compose/foundation/layout/m;

.field public static final d:Landroidx/compose/foundation/layout/m;


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Landroidx/compose/foundation/layout/m$b;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Landroidx/compose/foundation/layout/m$b;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Landroidx/compose/foundation/layout/m;->a:Landroidx/compose/foundation/layout/m$b;

    sget-object v0, Landroidx/compose/foundation/layout/m$a;->e:Landroidx/compose/foundation/layout/m$a;

    sput-object v0, Landroidx/compose/foundation/layout/m;->b:Landroidx/compose/foundation/layout/m;

    sget-object v0, Landroidx/compose/foundation/layout/m$e;->e:Landroidx/compose/foundation/layout/m$e;

    sput-object v0, Landroidx/compose/foundation/layout/m;->c:Landroidx/compose/foundation/layout/m;

    sget-object v0, Landroidx/compose/foundation/layout/m$c;->e:Landroidx/compose/foundation/layout/m$c;

    sput-object v0, Landroidx/compose/foundation/layout/m;->d:Landroidx/compose/foundation/layout/m;

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public synthetic constructor <init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    invoke-direct {p0}, Landroidx/compose/foundation/layout/m;-><init>()V

    return-void
.end method


# virtual methods
.method public abstract a(ILandroidx/compose/ui/unit/LayoutDirection;Landroidx/compose/ui/layout/k0;I)I
.end method

.method public b(Landroidx/compose/ui/layout/k0;)Ljava/lang/Integer;
    .locals 0

    const/4 p1, 0x0

    return-object p1
.end method

.method public c()Z
    .locals 1

    const/4 v0, 0x0

    return v0
.end method
