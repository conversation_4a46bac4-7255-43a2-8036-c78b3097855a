.class public interface abstract Landroidx/compose/foundation/layout/t;
.super Ljava/lang/Object;


# annotations
.annotation runtime L<PERSON><PERSON>/Metadata;
.end annotation


# virtual methods
.method public abstract a()F
.end method

.method public abstract b(Landroidx/compose/ui/unit/LayoutDirection;)F
.end method

.method public abstract c(Landroidx/compose/ui/unit/LayoutDirection;)F
.end method

.method public abstract d()F
.end method
