.class public interface abstract Landroidx/compose/foundation/layout/x;
.super Ljava/lang/Object;


# annotations
.annotation runtime L<PERSON>lin/Metadata;
.end annotation


# virtual methods
.method public abstract a(IIIIZ)J
.end method

.method public abstract b(I[I[ILandroidx/compose/ui/layout/y;)V
.end method

.method public abstract d([Landroidx/compose/ui/layout/k0;Landroidx/compose/ui/layout/y;I[III[IIII)Landroidx/compose/ui/layout/w;
.end method

.method public abstract e(Landroidx/compose/ui/layout/k0;)I
.end method

.method public abstract f(Landroidx/compose/ui/layout/k0;)I
.end method
