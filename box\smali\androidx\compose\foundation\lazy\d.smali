.class public final Landroidx/compose/foundation/lazy/d;
.super Ljava/lang/Object;


# annotations
.annotation runtime L<PERSON><PERSON>/Metadata;
.end annotation


# direct methods
.method public static final a(Landroidx/compose/foundation/lazy/LazyListState;Z)Landroidx/compose/foundation/lazy/layout/z;
    .locals 1

    new-instance v0, Landroidx/compose/foundation/lazy/d$a;

    invoke-direct {v0, p0, p1}, Landroidx/compose/foundation/lazy/d$a;-><init>(Landroidx/compose/foundation/lazy/LazyListState;Z)V

    return-object v0
.end method
