.class public interface abstract Landroidx/compose/foundation/lazy/layout/m;
.super Ljava/lang/Object;


# annotations
.annotation runtime L<PERSON><PERSON>/Metadata;
.end annotation


# virtual methods
.method public abstract a(Ljava/lang/Object;)I
.end method

.method public abstract b(I)Ljava/lang/Object;
.end method

.method public abstract c(I)Ljava/lang/Object;
.end method

.method public abstract g(ILjava/lang/Object;Landroidx/compose/runtime/i;I)V
.end method

.method public abstract getItemCount()I
.end method
