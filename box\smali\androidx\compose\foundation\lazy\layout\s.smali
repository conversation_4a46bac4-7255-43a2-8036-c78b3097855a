.class public interface abstract Landroidx/compose/foundation/lazy/layout/s;
.super Ljava/lang/Object;


# annotations
.annotation runtime L<PERSON><PERSON>/Metadata;
.end annotation


# virtual methods
.method public abstract a()J
.end method

.method public abstract b()I
.end method

.method public abstract c(Z)V
.end method

.method public abstract d()I
.end method

.method public abstract e()Z
.end method

.method public abstract f(IIII)V
.end method

.method public abstract getIndex()I
.end method

.method public abstract getKey()Ljava/lang/Object;
.end method

.method public abstract h()I
.end method

.method public abstract i(I)Ljava/lang/Object;
.end method

.method public abstract j(I)J
.end method

.method public abstract k()I
.end method
