.class public interface abstract Landroidx/compose/foundation/lazy/layout/t;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "<T::",
        "Landroidx/compose/foundation/lazy/layout/s;",
        ">",
        "Ljava/lang/Object;"
    }
.end annotation

.annotation runtime L<PERSON><PERSON>/Metadata;
.end annotation


# virtual methods
.method public abstract a(IIIJ)Landroidx/compose/foundation/lazy/layout/s;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(IIIJ)TT;"
        }
    .end annotation
.end method
