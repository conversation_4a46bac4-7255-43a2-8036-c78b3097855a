.class public interface abstract Landroidx/compose/foundation/lazy/layout/z;
.super Ljava/lang/Object;


# annotations
.annotation runtime L<PERSON><PERSON>/Metadata;
.end annotation


# virtual methods
.method public abstract a()I
.end method

.method public abstract b()F
.end method

.method public abstract c(ILkotlin/coroutines/Continuation;)Ljava/lang/Object;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I",
            "Lkotlin/coroutines/Continuation<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation
.end method

.method public abstract d()Landroidx/compose/ui/semantics/b;
.end method

.method public abstract e()I
.end method

.method public abstract f()F
.end method
