.class public interface abstract Landroidx/compose/foundation/lazy/r;
.super Ljava/lang/Object;


# annotations
.annotation runtime L<PERSON><PERSON>/Metadata;
.end annotation


# virtual methods
.method public abstract a(Landroidx/compose/foundation/lazy/layout/d0;I)V
.end method

.method public abstract b(Landroidx/compose/foundation/lazy/p;FLandroidx/compose/foundation/lazy/l;)V
.end method

.method public abstract c()Landroidx/compose/foundation/lazy/layout/i0;
.end method

.method public abstract d(Landroidx/compose/foundation/lazy/p;Landroidx/compose/foundation/lazy/l;)V
.end method
