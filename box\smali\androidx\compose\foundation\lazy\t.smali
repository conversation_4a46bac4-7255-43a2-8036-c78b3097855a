.class public final synthetic Landroidx/compose/foundation/lazy/t;
.super Ljava/lang/Object;


# direct methods
.method public static synthetic a(Landroidx/compose/foundation/lazy/u;Lja<PERSON>/lang/Object;Ljava/lang/Object;L<PERSON><PERSON>/jvm/functions/Function3;ILjava/lang/Object;)V
    .locals 1

    if-nez p5, :cond_2

    and-int/lit8 p5, p4, 0x1

    const/4 v0, 0x0

    if-eqz p5, :cond_0

    move-object p1, v0

    :cond_0
    and-int/lit8 p4, p4, 0x2

    if-eqz p4, :cond_1

    move-object p2, v0

    :cond_1
    invoke-interface {p0, p1, p2, p3}, Landroidx/compose/foundation/lazy/u;->a(Ljava/lang/Object;Ljava/lang/Object;Lkotlin/jvm/functions/Function3;)V

    return-void

    :cond_2
    new-instance p0, Ljava/lang/UnsupportedOperationException;

    const-string p1, "Super calls with default arguments not supported in this target, function: item"

    invoke-direct {p0, p1}, <PERSON><PERSON><PERSON>/lang/UnsupportedOperationException;-><init>(Ljava/lang/String;)V

    throw p0
.end method
