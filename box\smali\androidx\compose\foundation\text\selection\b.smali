.class public interface abstract Landroidx/compose/foundation/text/selection/b;
.super Ljava/lang/Object;


# annotations
.annotation runtime L<PERSON><PERSON>/Metadata;
.end annotation


# virtual methods
.method public abstract a()V
.end method

.method public abstract b(J)Z
.end method

.method public abstract c(JLandroidx/compose/foundation/text/selection/g;)Z
.end method

.method public abstract d(JLandroidx/compose/foundation/text/selection/g;)Z
.end method

.method public abstract e(J)Z
.end method
