.class public final Landroidx/compose/foundation/text/selection/g$a;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/compose/foundation/text/selection/g;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation


# static fields
.field public static final synthetic a:Landroidx/compose/foundation/text/selection/g$a;

.field public static final b:Landroidx/compose/foundation/text/selection/g;

.field public static final c:Landroidx/compose/foundation/text/selection/g;

.field public static final d:Landroidx/compose/foundation/text/selection/g;

.field public static final e:Landroidx/compose/foundation/text/selection/g;

.field public static final f:Landroidx/compose/foundation/text/selection/g;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    new-instance v0, Landroidx/compose/foundation/text/selection/g$a;

    invoke-direct {v0}, Landroidx/compose/foundation/text/selection/g$a;-><init>()V

    sput-object v0, Landroidx/compose/foundation/text/selection/g$a;->a:Landroidx/compose/foundation/text/selection/g$a;

    new-instance v0, Landroidx/compose/foundation/text/selection/f;

    invoke-direct {v0}, Landroidx/compose/foundation/text/selection/f;-><init>()V

    sput-object v0, Landroidx/compose/foundation/text/selection/g$a;->b:Landroidx/compose/foundation/text/selection/g;

    new-instance v0, Landroidx/compose/foundation/text/selection/f;

    invoke-direct {v0}, Landroidx/compose/foundation/text/selection/f;-><init>()V

    sput-object v0, Landroidx/compose/foundation/text/selection/g$a;->c:Landroidx/compose/foundation/text/selection/g;

    new-instance v0, Landroidx/compose/foundation/text/selection/f;

    invoke-direct {v0}, Landroidx/compose/foundation/text/selection/f;-><init>()V

    sput-object v0, Landroidx/compose/foundation/text/selection/g$a;->d:Landroidx/compose/foundation/text/selection/g;

    new-instance v0, Landroidx/compose/foundation/text/selection/f;

    invoke-direct {v0}, Landroidx/compose/foundation/text/selection/f;-><init>()V

    sput-object v0, Landroidx/compose/foundation/text/selection/g$a;->e:Landroidx/compose/foundation/text/selection/g;

    new-instance v0, Landroidx/compose/foundation/text/selection/f;

    invoke-direct {v0}, Landroidx/compose/foundation/text/selection/f;-><init>()V

    sput-object v0, Landroidx/compose/foundation/text/selection/g$a;->f:Landroidx/compose/foundation/text/selection/g;

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final a()Landroidx/compose/foundation/text/selection/g;
    .locals 1

    sget-object v0, Landroidx/compose/foundation/text/selection/g$a;->b:Landroidx/compose/foundation/text/selection/g;

    return-object v0
.end method

.method public final b()Landroidx/compose/foundation/text/selection/g;
    .locals 1

    sget-object v0, Landroidx/compose/foundation/text/selection/g$a;->e:Landroidx/compose/foundation/text/selection/g;

    return-object v0
.end method

.method public final c()Landroidx/compose/foundation/text/selection/g;
    .locals 1

    sget-object v0, Landroidx/compose/foundation/text/selection/g$a;->d:Landroidx/compose/foundation/text/selection/g;

    return-object v0
.end method
