.class public interface abstract Landroidx/compose/foundation/text/selection/j;
.super Ljava/lang/Object;


# annotations
.annotation runtime Lkotlin/Metadata;
.end annotation


# virtual methods
.method public abstract a()J
.end method

.method public abstract b()Landroidx/collection/t;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Landroidx/collection/t<",
            "Landroidx/compose/foundation/text/selection/e;",
            ">;"
        }
    .end annotation
.end method

.method public abstract c(J)V
.end method

.method public abstract d(Landroidx/compose/foundation/text/selection/d;)V
.end method

.method public abstract e(J)V
.end method

.method public abstract f(Landroidx/compose/ui/layout/m;JJZLandroidx/compose/foundation/text/selection/g;Z)Z
.end method

.method public abstract g()V
.end method

.method public abstract h(Landroidx/compose/foundation/text/selection/d;)Landroidx/compose/foundation/text/selection/d;
.end method

.method public abstract i(Landroidx/compose/ui/layout/m;JLandroidx/compose/foundation/text/selection/g;Z)V
.end method
