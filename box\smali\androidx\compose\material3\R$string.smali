.class public final Landroidx/compose/material3/R$string;
.super Ljava/lang/Object;


# static fields
.field public static bottom_sheet_collapse_description:I = 0x7f12006e

.field public static bottom_sheet_dismiss_description:I = 0x7f12006f

.field public static bottom_sheet_drag_handle_description:I = 0x7f120070

.field public static bottom_sheet_expand_description:I = 0x7f120071

.field public static collapsed:I = 0x7f1200af

.field public static date_input_headline:I = 0x7f1200ff

.field public static date_input_headline_description:I = 0x7f120100

.field public static date_input_invalid_for_pattern:I = 0x7f120101

.field public static date_input_invalid_not_allowed:I = 0x7f120102

.field public static date_input_invalid_year_range:I = 0x7f120103

.field public static date_input_label:I = 0x7f120104

.field public static date_input_no_input_description:I = 0x7f120105

.field public static date_input_title:I = 0x7f120106

.field public static date_picker_headline:I = 0x7f120107

.field public static date_picker_headline_description:I = 0x7f120108

.field public static date_picker_navigate_to_year_description:I = 0x7f120109

.field public static date_picker_no_selection_description:I = 0x7f12010a

.field public static date_picker_scroll_to_earlier_years:I = 0x7f12010b

.field public static date_picker_scroll_to_later_years:I = 0x7f12010c

.field public static date_picker_switch_to_calendar_mode:I = 0x7f12010d

.field public static date_picker_switch_to_day_selection:I = 0x7f12010e

.field public static date_picker_switch_to_input_mode:I = 0x7f12010f

.field public static date_picker_switch_to_next_month:I = 0x7f120110

.field public static date_picker_switch_to_previous_month:I = 0x7f120111

.field public static date_picker_switch_to_year_selection:I = 0x7f120112

.field public static date_picker_title:I = 0x7f120113

.field public static date_picker_today_description:I = 0x7f120114

.field public static date_picker_year_picker_pane_title:I = 0x7f120115

.field public static date_range_input_invalid_range_input:I = 0x7f120116

.field public static date_range_input_title:I = 0x7f120117

.field public static date_range_picker_day_in_range:I = 0x7f120118

.field public static date_range_picker_end_headline:I = 0x7f120119

.field public static date_range_picker_scroll_to_next_month:I = 0x7f12011a

.field public static date_range_picker_scroll_to_previous_month:I = 0x7f12011b

.field public static date_range_picker_start_headline:I = 0x7f12011c

.field public static date_range_picker_title:I = 0x7f12011d

.field public static dialog:I = 0x7f120127

.field public static expanded:I = 0x7f120224

.field public static m3c_bottom_sheet_pane_title:I = 0x7f12033f

.field public static search_bar_search:I = 0x7f12058d

.field public static snackbar_dismiss:I = 0x7f1205ec

.field public static suggestions_available:I = 0x7f120667

.field public static time_picker_am:I = 0x7f120698

.field public static time_picker_hour:I = 0x7f120699

.field public static time_picker_hour_24h_suffix:I = 0x7f12069a

.field public static time_picker_hour_selection:I = 0x7f12069b

.field public static time_picker_hour_suffix:I = 0x7f12069c

.field public static time_picker_hour_text_field:I = 0x7f12069d

.field public static time_picker_minute:I = 0x7f12069e

.field public static time_picker_minute_selection:I = 0x7f12069f

.field public static time_picker_minute_suffix:I = 0x7f1206a0

.field public static time_picker_minute_text_field:I = 0x7f1206a1

.field public static time_picker_period_toggle_description:I = 0x7f1206a2

.field public static time_picker_pm:I = 0x7f1206a3

.field public static tooltip_long_press_label:I = 0x7f1206be

.field public static tooltip_pane_description:I = 0x7f1206bf


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
