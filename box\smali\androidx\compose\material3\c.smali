.class public final Landroidx/compose/material3/c;
.super Ljava/lang/Object;


# annotations
.annotation runtime Lkotlin/Metadata;
.end annotation


# static fields
.field public static final a:Landroidx/compose/ui/text/x;


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Landroidx/compose/ui/text/x;

    const/4 v1, 0x1

    invoke-direct {v0, v1}, Landroidx/compose/ui/text/x;-><init>(Z)V

    sput-object v0, Landroidx/compose/material3/c;->a:Landroidx/compose/ui/text/x;

    return-void
.end method

.method public static final a()Landroidx/compose/ui/text/x;
    .locals 1

    sget-object v0, Landroidx/compose/material3/c;->a:Landroidx/compose/ui/text/x;

    return-object v0
.end method
