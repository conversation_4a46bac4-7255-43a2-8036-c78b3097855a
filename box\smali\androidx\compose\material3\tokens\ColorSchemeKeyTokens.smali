.class public final enum Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;
.super Ljava/lang/Enum;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Enum<",
        "Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation


# static fields
.field private static final synthetic $VALUES:[Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

.field public static final enum Background:Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

.field public static final enum Error:Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

.field public static final enum ErrorContainer:Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

.field public static final enum InverseOnSurface:Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

.field public static final enum InversePrimary:Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

.field public static final enum InverseSurface:Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

.field public static final enum OnBackground:Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

.field public static final enum OnError:Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

.field public static final enum OnErrorContainer:Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

.field public static final enum OnPrimary:Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

.field public static final enum OnPrimaryContainer:Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

.field public static final enum OnSecondary:Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

.field public static final enum OnSecondaryContainer:Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

.field public static final enum OnSurface:Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

.field public static final enum OnSurfaceVariant:Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

.field public static final enum OnTertiary:Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

.field public static final enum OnTertiaryContainer:Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

.field public static final enum Outline:Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

.field public static final enum OutlineVariant:Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

.field public static final enum Primary:Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

.field public static final enum PrimaryContainer:Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

.field public static final enum Scrim:Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

.field public static final enum Secondary:Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

.field public static final enum SecondaryContainer:Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

.field public static final enum Surface:Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

.field public static final enum SurfaceTint:Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

.field public static final enum SurfaceVariant:Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

.field public static final enum Tertiary:Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

.field public static final enum TertiaryContainer:Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;


# direct methods
.method private static final synthetic $values()[Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;
    .locals 3

    const/16 v0, 0x1d

    new-array v0, v0, [Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

    const/4 v1, 0x0

    sget-object v2, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;->Background:Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

    aput-object v2, v0, v1

    const/4 v1, 0x1

    sget-object v2, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;->Error:Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

    aput-object v2, v0, v1

    const/4 v1, 0x2

    sget-object v2, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;->ErrorContainer:Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

    aput-object v2, v0, v1

    const/4 v1, 0x3

    sget-object v2, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;->InverseOnSurface:Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

    aput-object v2, v0, v1

    const/4 v1, 0x4

    sget-object v2, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;->InversePrimary:Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

    aput-object v2, v0, v1

    const/4 v1, 0x5

    sget-object v2, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;->InverseSurface:Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

    aput-object v2, v0, v1

    const/4 v1, 0x6

    sget-object v2, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;->OnBackground:Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

    aput-object v2, v0, v1

    const/4 v1, 0x7

    sget-object v2, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;->OnError:Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

    aput-object v2, v0, v1

    const/16 v1, 0x8

    sget-object v2, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;->OnErrorContainer:Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

    aput-object v2, v0, v1

    const/16 v1, 0x9

    sget-object v2, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;->OnPrimary:Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

    aput-object v2, v0, v1

    const/16 v1, 0xa

    sget-object v2, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;->OnPrimaryContainer:Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

    aput-object v2, v0, v1

    const/16 v1, 0xb

    sget-object v2, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;->OnSecondary:Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

    aput-object v2, v0, v1

    const/16 v1, 0xc

    sget-object v2, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;->OnSecondaryContainer:Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

    aput-object v2, v0, v1

    const/16 v1, 0xd

    sget-object v2, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;->OnSurface:Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

    aput-object v2, v0, v1

    const/16 v1, 0xe

    sget-object v2, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;->OnSurfaceVariant:Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

    aput-object v2, v0, v1

    const/16 v1, 0xf

    sget-object v2, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;->OnTertiary:Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

    aput-object v2, v0, v1

    const/16 v1, 0x10

    sget-object v2, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;->OnTertiaryContainer:Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

    aput-object v2, v0, v1

    const/16 v1, 0x11

    sget-object v2, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;->Outline:Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

    aput-object v2, v0, v1

    const/16 v1, 0x12

    sget-object v2, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;->OutlineVariant:Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

    aput-object v2, v0, v1

    const/16 v1, 0x13

    sget-object v2, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;->Primary:Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

    aput-object v2, v0, v1

    const/16 v1, 0x14

    sget-object v2, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;->PrimaryContainer:Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

    aput-object v2, v0, v1

    const/16 v1, 0x15

    sget-object v2, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;->Scrim:Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

    aput-object v2, v0, v1

    const/16 v1, 0x16

    sget-object v2, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;->Secondary:Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

    aput-object v2, v0, v1

    const/16 v1, 0x17

    sget-object v2, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;->SecondaryContainer:Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

    aput-object v2, v0, v1

    const/16 v1, 0x18

    sget-object v2, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;->Surface:Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

    aput-object v2, v0, v1

    const/16 v1, 0x19

    sget-object v2, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;->SurfaceTint:Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

    aput-object v2, v0, v1

    const/16 v1, 0x1a

    sget-object v2, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;->SurfaceVariant:Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

    aput-object v2, v0, v1

    const/16 v1, 0x1b

    sget-object v2, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;->Tertiary:Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

    aput-object v2, v0, v1

    const/16 v1, 0x1c

    sget-object v2, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;->TertiaryContainer:Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

    aput-object v2, v0, v1

    return-object v0
.end method

.method static constructor <clinit>()V
    .locals 3

    new-instance v0, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

    const-string v1, "Background"

    const/4 v2, 0x0

    invoke-direct {v0, v1, v2}, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;->Background:Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

    new-instance v0, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

    const-string v1, "Error"

    const/4 v2, 0x1

    invoke-direct {v0, v1, v2}, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;->Error:Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

    new-instance v0, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

    const-string v1, "ErrorContainer"

    const/4 v2, 0x2

    invoke-direct {v0, v1, v2}, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;->ErrorContainer:Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

    new-instance v0, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

    const-string v1, "InverseOnSurface"

    const/4 v2, 0x3

    invoke-direct {v0, v1, v2}, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;->InverseOnSurface:Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

    new-instance v0, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

    const-string v1, "InversePrimary"

    const/4 v2, 0x4

    invoke-direct {v0, v1, v2}, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;->InversePrimary:Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

    new-instance v0, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

    const-string v1, "InverseSurface"

    const/4 v2, 0x5

    invoke-direct {v0, v1, v2}, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;->InverseSurface:Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

    new-instance v0, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

    const-string v1, "OnBackground"

    const/4 v2, 0x6

    invoke-direct {v0, v1, v2}, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;->OnBackground:Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

    new-instance v0, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

    const-string v1, "OnError"

    const/4 v2, 0x7

    invoke-direct {v0, v1, v2}, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;->OnError:Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

    new-instance v0, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

    const-string v1, "OnErrorContainer"

    const/16 v2, 0x8

    invoke-direct {v0, v1, v2}, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;->OnErrorContainer:Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

    new-instance v0, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

    const-string v1, "OnPrimary"

    const/16 v2, 0x9

    invoke-direct {v0, v1, v2}, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;->OnPrimary:Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

    new-instance v0, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

    const-string v1, "OnPrimaryContainer"

    const/16 v2, 0xa

    invoke-direct {v0, v1, v2}, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;->OnPrimaryContainer:Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

    new-instance v0, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

    const-string v1, "OnSecondary"

    const/16 v2, 0xb

    invoke-direct {v0, v1, v2}, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;->OnSecondary:Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

    new-instance v0, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

    const-string v1, "OnSecondaryContainer"

    const/16 v2, 0xc

    invoke-direct {v0, v1, v2}, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;->OnSecondaryContainer:Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

    new-instance v0, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

    const-string v1, "OnSurface"

    const/16 v2, 0xd

    invoke-direct {v0, v1, v2}, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;->OnSurface:Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

    new-instance v0, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

    const-string v1, "OnSurfaceVariant"

    const/16 v2, 0xe

    invoke-direct {v0, v1, v2}, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;->OnSurfaceVariant:Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

    new-instance v0, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

    const-string v1, "OnTertiary"

    const/16 v2, 0xf

    invoke-direct {v0, v1, v2}, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;->OnTertiary:Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

    new-instance v0, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

    const-string v1, "OnTertiaryContainer"

    const/16 v2, 0x10

    invoke-direct {v0, v1, v2}, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;->OnTertiaryContainer:Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

    new-instance v0, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

    const-string v1, "Outline"

    const/16 v2, 0x11

    invoke-direct {v0, v1, v2}, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;->Outline:Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

    new-instance v0, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

    const-string v1, "OutlineVariant"

    const/16 v2, 0x12

    invoke-direct {v0, v1, v2}, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;->OutlineVariant:Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

    new-instance v0, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

    const-string v1, "Primary"

    const/16 v2, 0x13

    invoke-direct {v0, v1, v2}, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;->Primary:Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

    new-instance v0, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

    const-string v1, "PrimaryContainer"

    const/16 v2, 0x14

    invoke-direct {v0, v1, v2}, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;->PrimaryContainer:Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

    new-instance v0, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

    const-string v1, "Scrim"

    const/16 v2, 0x15

    invoke-direct {v0, v1, v2}, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;->Scrim:Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

    new-instance v0, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

    const-string v1, "Secondary"

    const/16 v2, 0x16

    invoke-direct {v0, v1, v2}, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;->Secondary:Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

    new-instance v0, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

    const-string v1, "SecondaryContainer"

    const/16 v2, 0x17

    invoke-direct {v0, v1, v2}, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;->SecondaryContainer:Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

    new-instance v0, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

    const-string v1, "Surface"

    const/16 v2, 0x18

    invoke-direct {v0, v1, v2}, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;->Surface:Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

    new-instance v0, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

    const-string v1, "SurfaceTint"

    const/16 v2, 0x19

    invoke-direct {v0, v1, v2}, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;->SurfaceTint:Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

    new-instance v0, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

    const-string v1, "SurfaceVariant"

    const/16 v2, 0x1a

    invoke-direct {v0, v1, v2}, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;->SurfaceVariant:Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

    new-instance v0, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

    const-string v1, "Tertiary"

    const/16 v2, 0x1b

    invoke-direct {v0, v1, v2}, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;->Tertiary:Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

    new-instance v0, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

    const-string v1, "TertiaryContainer"

    const/16 v2, 0x1c

    invoke-direct {v0, v1, v2}, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;->TertiaryContainer:Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

    invoke-static {}, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;->$values()[Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

    move-result-object v0

    sput-object v0, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;->$VALUES:[Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

    return-void
.end method

.method private constructor <init>(Ljava/lang/String;I)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation

    invoke-direct {p0, p1, p2}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    return-void
.end method

.method public static valueOf(Ljava/lang/String;)Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;
    .locals 1

    const-class v0, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

    invoke-static {v0, p0}, Ljava/lang/Enum;->valueOf(Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Enum;

    move-result-object p0

    check-cast p0, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

    return-object p0
.end method

.method public static values()[Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;
    .locals 1

    sget-object v0, Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;->$VALUES:[Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

    invoke-virtual {v0}, [Ljava/lang/Object;->clone()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [Landroidx/compose/material3/tokens/ColorSchemeKeyTokens;

    return-object v0
.end method
