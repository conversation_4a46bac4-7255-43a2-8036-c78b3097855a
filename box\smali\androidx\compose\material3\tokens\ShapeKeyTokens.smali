.class public final enum Landroidx/compose/material3/tokens/ShapeKeyTokens;
.super Ljava/lang/Enum;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Enum<",
        "Landroidx/compose/material3/tokens/ShapeKeyTokens;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation


# static fields
.field private static final synthetic $VALUES:[Landroidx/compose/material3/tokens/ShapeKeyTokens;

.field public static final enum CornerExtraLarge:Landroidx/compose/material3/tokens/ShapeKeyTokens;

.field public static final enum CornerExtraLargeTop:Landroidx/compose/material3/tokens/ShapeKeyTokens;

.field public static final enum CornerExtraSmall:Landroidx/compose/material3/tokens/ShapeKeyTokens;

.field public static final enum CornerExtraSmallTop:Landroidx/compose/material3/tokens/ShapeKeyTokens;

.field public static final enum CornerFull:Landroidx/compose/material3/tokens/ShapeKeyTokens;

.field public static final enum CornerLarge:Landroidx/compose/material3/tokens/ShapeKeyTokens;

.field public static final enum CornerLargeEnd:Landroidx/compose/material3/tokens/ShapeKeyTokens;

.field public static final enum CornerLargeTop:Landroidx/compose/material3/tokens/ShapeKeyTokens;

.field public static final enum CornerMedium:Landroidx/compose/material3/tokens/ShapeKeyTokens;

.field public static final enum CornerNone:Landroidx/compose/material3/tokens/ShapeKeyTokens;

.field public static final enum CornerSmall:Landroidx/compose/material3/tokens/ShapeKeyTokens;


# direct methods
.method private static final synthetic $values()[Landroidx/compose/material3/tokens/ShapeKeyTokens;
    .locals 3

    const/16 v0, 0xb

    new-array v0, v0, [Landroidx/compose/material3/tokens/ShapeKeyTokens;

    const/4 v1, 0x0

    sget-object v2, Landroidx/compose/material3/tokens/ShapeKeyTokens;->CornerExtraLarge:Landroidx/compose/material3/tokens/ShapeKeyTokens;

    aput-object v2, v0, v1

    const/4 v1, 0x1

    sget-object v2, Landroidx/compose/material3/tokens/ShapeKeyTokens;->CornerExtraLargeTop:Landroidx/compose/material3/tokens/ShapeKeyTokens;

    aput-object v2, v0, v1

    const/4 v1, 0x2

    sget-object v2, Landroidx/compose/material3/tokens/ShapeKeyTokens;->CornerExtraSmall:Landroidx/compose/material3/tokens/ShapeKeyTokens;

    aput-object v2, v0, v1

    const/4 v1, 0x3

    sget-object v2, Landroidx/compose/material3/tokens/ShapeKeyTokens;->CornerExtraSmallTop:Landroidx/compose/material3/tokens/ShapeKeyTokens;

    aput-object v2, v0, v1

    const/4 v1, 0x4

    sget-object v2, Landroidx/compose/material3/tokens/ShapeKeyTokens;->CornerFull:Landroidx/compose/material3/tokens/ShapeKeyTokens;

    aput-object v2, v0, v1

    const/4 v1, 0x5

    sget-object v2, Landroidx/compose/material3/tokens/ShapeKeyTokens;->CornerLarge:Landroidx/compose/material3/tokens/ShapeKeyTokens;

    aput-object v2, v0, v1

    const/4 v1, 0x6

    sget-object v2, Landroidx/compose/material3/tokens/ShapeKeyTokens;->CornerLargeEnd:Landroidx/compose/material3/tokens/ShapeKeyTokens;

    aput-object v2, v0, v1

    const/4 v1, 0x7

    sget-object v2, Landroidx/compose/material3/tokens/ShapeKeyTokens;->CornerLargeTop:Landroidx/compose/material3/tokens/ShapeKeyTokens;

    aput-object v2, v0, v1

    const/16 v1, 0x8

    sget-object v2, Landroidx/compose/material3/tokens/ShapeKeyTokens;->CornerMedium:Landroidx/compose/material3/tokens/ShapeKeyTokens;

    aput-object v2, v0, v1

    const/16 v1, 0x9

    sget-object v2, Landroidx/compose/material3/tokens/ShapeKeyTokens;->CornerNone:Landroidx/compose/material3/tokens/ShapeKeyTokens;

    aput-object v2, v0, v1

    const/16 v1, 0xa

    sget-object v2, Landroidx/compose/material3/tokens/ShapeKeyTokens;->CornerSmall:Landroidx/compose/material3/tokens/ShapeKeyTokens;

    aput-object v2, v0, v1

    return-object v0
.end method

.method static constructor <clinit>()V
    .locals 3

    new-instance v0, Landroidx/compose/material3/tokens/ShapeKeyTokens;

    const-string v1, "CornerExtraLarge"

    const/4 v2, 0x0

    invoke-direct {v0, v1, v2}, Landroidx/compose/material3/tokens/ShapeKeyTokens;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/material3/tokens/ShapeKeyTokens;->CornerExtraLarge:Landroidx/compose/material3/tokens/ShapeKeyTokens;

    new-instance v0, Landroidx/compose/material3/tokens/ShapeKeyTokens;

    const-string v1, "CornerExtraLargeTop"

    const/4 v2, 0x1

    invoke-direct {v0, v1, v2}, Landroidx/compose/material3/tokens/ShapeKeyTokens;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/material3/tokens/ShapeKeyTokens;->CornerExtraLargeTop:Landroidx/compose/material3/tokens/ShapeKeyTokens;

    new-instance v0, Landroidx/compose/material3/tokens/ShapeKeyTokens;

    const-string v1, "CornerExtraSmall"

    const/4 v2, 0x2

    invoke-direct {v0, v1, v2}, Landroidx/compose/material3/tokens/ShapeKeyTokens;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/material3/tokens/ShapeKeyTokens;->CornerExtraSmall:Landroidx/compose/material3/tokens/ShapeKeyTokens;

    new-instance v0, Landroidx/compose/material3/tokens/ShapeKeyTokens;

    const-string v1, "CornerExtraSmallTop"

    const/4 v2, 0x3

    invoke-direct {v0, v1, v2}, Landroidx/compose/material3/tokens/ShapeKeyTokens;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/material3/tokens/ShapeKeyTokens;->CornerExtraSmallTop:Landroidx/compose/material3/tokens/ShapeKeyTokens;

    new-instance v0, Landroidx/compose/material3/tokens/ShapeKeyTokens;

    const-string v1, "CornerFull"

    const/4 v2, 0x4

    invoke-direct {v0, v1, v2}, Landroidx/compose/material3/tokens/ShapeKeyTokens;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/material3/tokens/ShapeKeyTokens;->CornerFull:Landroidx/compose/material3/tokens/ShapeKeyTokens;

    new-instance v0, Landroidx/compose/material3/tokens/ShapeKeyTokens;

    const-string v1, "CornerLarge"

    const/4 v2, 0x5

    invoke-direct {v0, v1, v2}, Landroidx/compose/material3/tokens/ShapeKeyTokens;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/material3/tokens/ShapeKeyTokens;->CornerLarge:Landroidx/compose/material3/tokens/ShapeKeyTokens;

    new-instance v0, Landroidx/compose/material3/tokens/ShapeKeyTokens;

    const-string v1, "CornerLargeEnd"

    const/4 v2, 0x6

    invoke-direct {v0, v1, v2}, Landroidx/compose/material3/tokens/ShapeKeyTokens;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/material3/tokens/ShapeKeyTokens;->CornerLargeEnd:Landroidx/compose/material3/tokens/ShapeKeyTokens;

    new-instance v0, Landroidx/compose/material3/tokens/ShapeKeyTokens;

    const-string v1, "CornerLargeTop"

    const/4 v2, 0x7

    invoke-direct {v0, v1, v2}, Landroidx/compose/material3/tokens/ShapeKeyTokens;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/material3/tokens/ShapeKeyTokens;->CornerLargeTop:Landroidx/compose/material3/tokens/ShapeKeyTokens;

    new-instance v0, Landroidx/compose/material3/tokens/ShapeKeyTokens;

    const-string v1, "CornerMedium"

    const/16 v2, 0x8

    invoke-direct {v0, v1, v2}, Landroidx/compose/material3/tokens/ShapeKeyTokens;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/material3/tokens/ShapeKeyTokens;->CornerMedium:Landroidx/compose/material3/tokens/ShapeKeyTokens;

    new-instance v0, Landroidx/compose/material3/tokens/ShapeKeyTokens;

    const-string v1, "CornerNone"

    const/16 v2, 0x9

    invoke-direct {v0, v1, v2}, Landroidx/compose/material3/tokens/ShapeKeyTokens;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/material3/tokens/ShapeKeyTokens;->CornerNone:Landroidx/compose/material3/tokens/ShapeKeyTokens;

    new-instance v0, Landroidx/compose/material3/tokens/ShapeKeyTokens;

    const-string v1, "CornerSmall"

    const/16 v2, 0xa

    invoke-direct {v0, v1, v2}, Landroidx/compose/material3/tokens/ShapeKeyTokens;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/material3/tokens/ShapeKeyTokens;->CornerSmall:Landroidx/compose/material3/tokens/ShapeKeyTokens;

    invoke-static {}, Landroidx/compose/material3/tokens/ShapeKeyTokens;->$values()[Landroidx/compose/material3/tokens/ShapeKeyTokens;

    move-result-object v0

    sput-object v0, Landroidx/compose/material3/tokens/ShapeKeyTokens;->$VALUES:[Landroidx/compose/material3/tokens/ShapeKeyTokens;

    return-void
.end method

.method private constructor <init>(Ljava/lang/String;I)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation

    invoke-direct {p0, p1, p2}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    return-void
.end method

.method public static valueOf(Ljava/lang/String;)Landroidx/compose/material3/tokens/ShapeKeyTokens;
    .locals 1

    const-class v0, Landroidx/compose/material3/tokens/ShapeKeyTokens;

    invoke-static {v0, p0}, Ljava/lang/Enum;->valueOf(Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Enum;

    move-result-object p0

    check-cast p0, Landroidx/compose/material3/tokens/ShapeKeyTokens;

    return-object p0
.end method

.method public static values()[Landroidx/compose/material3/tokens/ShapeKeyTokens;
    .locals 1

    sget-object v0, Landroidx/compose/material3/tokens/ShapeKeyTokens;->$VALUES:[Landroidx/compose/material3/tokens/ShapeKeyTokens;

    invoke-virtual {v0}, [Ljava/lang/Object;->clone()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [Landroidx/compose/material3/tokens/ShapeKeyTokens;

    return-object v0
.end method
