.class public final enum Landroidx/compose/material3/tokens/TypographyKeyTokens;
.super Ljava/lang/Enum;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Enum<",
        "Landroidx/compose/material3/tokens/TypographyKeyTokens;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation


# static fields
.field private static final synthetic $VALUES:[Landroidx/compose/material3/tokens/TypographyKeyTokens;

.field public static final enum BodyLarge:Landroidx/compose/material3/tokens/TypographyKeyTokens;

.field public static final enum BodyMedium:Landroidx/compose/material3/tokens/TypographyKeyTokens;

.field public static final enum BodySmall:Landroidx/compose/material3/tokens/TypographyKeyTokens;

.field public static final enum DisplayLarge:Landroidx/compose/material3/tokens/TypographyKeyTokens;

.field public static final enum DisplayMedium:Landroidx/compose/material3/tokens/TypographyKeyTokens;

.field public static final enum DisplaySmall:Landroidx/compose/material3/tokens/TypographyKeyTokens;

.field public static final enum HeadlineLarge:Landroidx/compose/material3/tokens/TypographyKeyTokens;

.field public static final enum HeadlineMedium:Landroidx/compose/material3/tokens/TypographyKeyTokens;

.field public static final enum HeadlineSmall:Landroidx/compose/material3/tokens/TypographyKeyTokens;

.field public static final enum LabelLarge:Landroidx/compose/material3/tokens/TypographyKeyTokens;

.field public static final enum LabelMedium:Landroidx/compose/material3/tokens/TypographyKeyTokens;

.field public static final enum LabelSmall:Landroidx/compose/material3/tokens/TypographyKeyTokens;

.field public static final enum TitleLarge:Landroidx/compose/material3/tokens/TypographyKeyTokens;

.field public static final enum TitleMedium:Landroidx/compose/material3/tokens/TypographyKeyTokens;

.field public static final enum TitleSmall:Landroidx/compose/material3/tokens/TypographyKeyTokens;


# direct methods
.method private static final synthetic $values()[Landroidx/compose/material3/tokens/TypographyKeyTokens;
    .locals 3

    const/16 v0, 0xf

    new-array v0, v0, [Landroidx/compose/material3/tokens/TypographyKeyTokens;

    const/4 v1, 0x0

    sget-object v2, Landroidx/compose/material3/tokens/TypographyKeyTokens;->BodyLarge:Landroidx/compose/material3/tokens/TypographyKeyTokens;

    aput-object v2, v0, v1

    const/4 v1, 0x1

    sget-object v2, Landroidx/compose/material3/tokens/TypographyKeyTokens;->BodyMedium:Landroidx/compose/material3/tokens/TypographyKeyTokens;

    aput-object v2, v0, v1

    const/4 v1, 0x2

    sget-object v2, Landroidx/compose/material3/tokens/TypographyKeyTokens;->BodySmall:Landroidx/compose/material3/tokens/TypographyKeyTokens;

    aput-object v2, v0, v1

    const/4 v1, 0x3

    sget-object v2, Landroidx/compose/material3/tokens/TypographyKeyTokens;->DisplayLarge:Landroidx/compose/material3/tokens/TypographyKeyTokens;

    aput-object v2, v0, v1

    const/4 v1, 0x4

    sget-object v2, Landroidx/compose/material3/tokens/TypographyKeyTokens;->DisplayMedium:Landroidx/compose/material3/tokens/TypographyKeyTokens;

    aput-object v2, v0, v1

    const/4 v1, 0x5

    sget-object v2, Landroidx/compose/material3/tokens/TypographyKeyTokens;->DisplaySmall:Landroidx/compose/material3/tokens/TypographyKeyTokens;

    aput-object v2, v0, v1

    const/4 v1, 0x6

    sget-object v2, Landroidx/compose/material3/tokens/TypographyKeyTokens;->HeadlineLarge:Landroidx/compose/material3/tokens/TypographyKeyTokens;

    aput-object v2, v0, v1

    const/4 v1, 0x7

    sget-object v2, Landroidx/compose/material3/tokens/TypographyKeyTokens;->HeadlineMedium:Landroidx/compose/material3/tokens/TypographyKeyTokens;

    aput-object v2, v0, v1

    const/16 v1, 0x8

    sget-object v2, Landroidx/compose/material3/tokens/TypographyKeyTokens;->HeadlineSmall:Landroidx/compose/material3/tokens/TypographyKeyTokens;

    aput-object v2, v0, v1

    const/16 v1, 0x9

    sget-object v2, Landroidx/compose/material3/tokens/TypographyKeyTokens;->LabelLarge:Landroidx/compose/material3/tokens/TypographyKeyTokens;

    aput-object v2, v0, v1

    const/16 v1, 0xa

    sget-object v2, Landroidx/compose/material3/tokens/TypographyKeyTokens;->LabelMedium:Landroidx/compose/material3/tokens/TypographyKeyTokens;

    aput-object v2, v0, v1

    const/16 v1, 0xb

    sget-object v2, Landroidx/compose/material3/tokens/TypographyKeyTokens;->LabelSmall:Landroidx/compose/material3/tokens/TypographyKeyTokens;

    aput-object v2, v0, v1

    const/16 v1, 0xc

    sget-object v2, Landroidx/compose/material3/tokens/TypographyKeyTokens;->TitleLarge:Landroidx/compose/material3/tokens/TypographyKeyTokens;

    aput-object v2, v0, v1

    const/16 v1, 0xd

    sget-object v2, Landroidx/compose/material3/tokens/TypographyKeyTokens;->TitleMedium:Landroidx/compose/material3/tokens/TypographyKeyTokens;

    aput-object v2, v0, v1

    const/16 v1, 0xe

    sget-object v2, Landroidx/compose/material3/tokens/TypographyKeyTokens;->TitleSmall:Landroidx/compose/material3/tokens/TypographyKeyTokens;

    aput-object v2, v0, v1

    return-object v0
.end method

.method static constructor <clinit>()V
    .locals 3

    new-instance v0, Landroidx/compose/material3/tokens/TypographyKeyTokens;

    const-string v1, "BodyLarge"

    const/4 v2, 0x0

    invoke-direct {v0, v1, v2}, Landroidx/compose/material3/tokens/TypographyKeyTokens;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/material3/tokens/TypographyKeyTokens;->BodyLarge:Landroidx/compose/material3/tokens/TypographyKeyTokens;

    new-instance v0, Landroidx/compose/material3/tokens/TypographyKeyTokens;

    const-string v1, "BodyMedium"

    const/4 v2, 0x1

    invoke-direct {v0, v1, v2}, Landroidx/compose/material3/tokens/TypographyKeyTokens;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/material3/tokens/TypographyKeyTokens;->BodyMedium:Landroidx/compose/material3/tokens/TypographyKeyTokens;

    new-instance v0, Landroidx/compose/material3/tokens/TypographyKeyTokens;

    const-string v1, "BodySmall"

    const/4 v2, 0x2

    invoke-direct {v0, v1, v2}, Landroidx/compose/material3/tokens/TypographyKeyTokens;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/material3/tokens/TypographyKeyTokens;->BodySmall:Landroidx/compose/material3/tokens/TypographyKeyTokens;

    new-instance v0, Landroidx/compose/material3/tokens/TypographyKeyTokens;

    const-string v1, "DisplayLarge"

    const/4 v2, 0x3

    invoke-direct {v0, v1, v2}, Landroidx/compose/material3/tokens/TypographyKeyTokens;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/material3/tokens/TypographyKeyTokens;->DisplayLarge:Landroidx/compose/material3/tokens/TypographyKeyTokens;

    new-instance v0, Landroidx/compose/material3/tokens/TypographyKeyTokens;

    const-string v1, "DisplayMedium"

    const/4 v2, 0x4

    invoke-direct {v0, v1, v2}, Landroidx/compose/material3/tokens/TypographyKeyTokens;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/material3/tokens/TypographyKeyTokens;->DisplayMedium:Landroidx/compose/material3/tokens/TypographyKeyTokens;

    new-instance v0, Landroidx/compose/material3/tokens/TypographyKeyTokens;

    const-string v1, "DisplaySmall"

    const/4 v2, 0x5

    invoke-direct {v0, v1, v2}, Landroidx/compose/material3/tokens/TypographyKeyTokens;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/material3/tokens/TypographyKeyTokens;->DisplaySmall:Landroidx/compose/material3/tokens/TypographyKeyTokens;

    new-instance v0, Landroidx/compose/material3/tokens/TypographyKeyTokens;

    const-string v1, "HeadlineLarge"

    const/4 v2, 0x6

    invoke-direct {v0, v1, v2}, Landroidx/compose/material3/tokens/TypographyKeyTokens;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/material3/tokens/TypographyKeyTokens;->HeadlineLarge:Landroidx/compose/material3/tokens/TypographyKeyTokens;

    new-instance v0, Landroidx/compose/material3/tokens/TypographyKeyTokens;

    const-string v1, "HeadlineMedium"

    const/4 v2, 0x7

    invoke-direct {v0, v1, v2}, Landroidx/compose/material3/tokens/TypographyKeyTokens;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/material3/tokens/TypographyKeyTokens;->HeadlineMedium:Landroidx/compose/material3/tokens/TypographyKeyTokens;

    new-instance v0, Landroidx/compose/material3/tokens/TypographyKeyTokens;

    const-string v1, "HeadlineSmall"

    const/16 v2, 0x8

    invoke-direct {v0, v1, v2}, Landroidx/compose/material3/tokens/TypographyKeyTokens;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/material3/tokens/TypographyKeyTokens;->HeadlineSmall:Landroidx/compose/material3/tokens/TypographyKeyTokens;

    new-instance v0, Landroidx/compose/material3/tokens/TypographyKeyTokens;

    const-string v1, "LabelLarge"

    const/16 v2, 0x9

    invoke-direct {v0, v1, v2}, Landroidx/compose/material3/tokens/TypographyKeyTokens;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/material3/tokens/TypographyKeyTokens;->LabelLarge:Landroidx/compose/material3/tokens/TypographyKeyTokens;

    new-instance v0, Landroidx/compose/material3/tokens/TypographyKeyTokens;

    const-string v1, "LabelMedium"

    const/16 v2, 0xa

    invoke-direct {v0, v1, v2}, Landroidx/compose/material3/tokens/TypographyKeyTokens;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/material3/tokens/TypographyKeyTokens;->LabelMedium:Landroidx/compose/material3/tokens/TypographyKeyTokens;

    new-instance v0, Landroidx/compose/material3/tokens/TypographyKeyTokens;

    const-string v1, "LabelSmall"

    const/16 v2, 0xb

    invoke-direct {v0, v1, v2}, Landroidx/compose/material3/tokens/TypographyKeyTokens;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/material3/tokens/TypographyKeyTokens;->LabelSmall:Landroidx/compose/material3/tokens/TypographyKeyTokens;

    new-instance v0, Landroidx/compose/material3/tokens/TypographyKeyTokens;

    const-string v1, "TitleLarge"

    const/16 v2, 0xc

    invoke-direct {v0, v1, v2}, Landroidx/compose/material3/tokens/TypographyKeyTokens;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/material3/tokens/TypographyKeyTokens;->TitleLarge:Landroidx/compose/material3/tokens/TypographyKeyTokens;

    new-instance v0, Landroidx/compose/material3/tokens/TypographyKeyTokens;

    const-string v1, "TitleMedium"

    const/16 v2, 0xd

    invoke-direct {v0, v1, v2}, Landroidx/compose/material3/tokens/TypographyKeyTokens;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/material3/tokens/TypographyKeyTokens;->TitleMedium:Landroidx/compose/material3/tokens/TypographyKeyTokens;

    new-instance v0, Landroidx/compose/material3/tokens/TypographyKeyTokens;

    const-string v1, "TitleSmall"

    const/16 v2, 0xe

    invoke-direct {v0, v1, v2}, Landroidx/compose/material3/tokens/TypographyKeyTokens;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/material3/tokens/TypographyKeyTokens;->TitleSmall:Landroidx/compose/material3/tokens/TypographyKeyTokens;

    invoke-static {}, Landroidx/compose/material3/tokens/TypographyKeyTokens;->$values()[Landroidx/compose/material3/tokens/TypographyKeyTokens;

    move-result-object v0

    sput-object v0, Landroidx/compose/material3/tokens/TypographyKeyTokens;->$VALUES:[Landroidx/compose/material3/tokens/TypographyKeyTokens;

    return-void
.end method

.method private constructor <init>(Ljava/lang/String;I)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation

    invoke-direct {p0, p1, p2}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    return-void
.end method

.method public static valueOf(Ljava/lang/String;)Landroidx/compose/material3/tokens/TypographyKeyTokens;
    .locals 1

    const-class v0, Landroidx/compose/material3/tokens/TypographyKeyTokens;

    invoke-static {v0, p0}, Ljava/lang/Enum;->valueOf(Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Enum;

    move-result-object p0

    check-cast p0, Landroidx/compose/material3/tokens/TypographyKeyTokens;

    return-object p0
.end method

.method public static values()[Landroidx/compose/material3/tokens/TypographyKeyTokens;
    .locals 1

    sget-object v0, Landroidx/compose/material3/tokens/TypographyKeyTokens;->$VALUES:[Landroidx/compose/material3/tokens/TypographyKeyTokens;

    invoke-virtual {v0}, [Ljava/lang/Object;->clone()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [Landroidx/compose/material3/tokens/TypographyKeyTokens;

    return-object v0
.end method
