.class final Landroidx/compose/material/DraggableAnchorsElement;
.super Landroidx/compose/ui/node/l0;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "<T:",
        "Ljava/lang/Object;",
        ">",
        "Landroidx/compose/ui/node/l0<",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation

.annotation build Lkotlin/jvm/internal/SourceDebugExtension;
.end annotation


# direct methods
.method public static final synthetic o(Landroidx/compose/material/DraggableAnchorsElement;)Lkotlin/jvm/functions/Function2;
    .locals 0

    const/4 p0, 0x0

    throw p0
.end method

.method public static final synthetic p(Landroidx/compose/material/DraggableAnchorsElement;)Landroidx/compose/foundation/gestures/Orientation;
    .locals 0

    const/4 p0, 0x0

    throw p0
.end method

.method public static final synthetic q(Landroidx/compose/material/DraggableAnchorsElement;)Landroidx/compose/material/a;
    .locals 0

    const/4 p0, 0x0

    throw p0
.end method
