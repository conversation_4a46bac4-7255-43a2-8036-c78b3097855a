.class public final Landroidx/compose/material/i$a;
.super Ljava/lang/Object;

# interfaces
.implements Landroidx/compose/ui/graphics/z1;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Landroidx/compose/material/i;->b(Lt/g;)Landroidx/compose/ui/node/f;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation


# instance fields
.field public final synthetic a:Landroidx/compose/material/i;


# direct methods
.method public constructor <init>(Landroidx/compose/material/i;)V
    .locals 0

    iput-object p1, p0, Landroidx/compose/material/i$a;->a:Landroidx/compose/material/i;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final a()J
    .locals 2

    iget-object v0, p0, Landroidx/compose/material/i$a;->a:Landroidx/compose/material/i;

    invoke-static {v0}, Landroidx/compose/material/i;->c(Landroidx/compose/material/i;)J

    move-result-wide v0

    return-wide v0
.end method
