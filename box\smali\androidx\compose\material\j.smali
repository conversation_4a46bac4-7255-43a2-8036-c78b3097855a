.class public interface abstract Landroidx/compose/material/j;
.super Ljava/lang/Object;


# annotations
.annotation runtime Lkotlin/Metadata;
.end annotation


# virtual methods
.method public abstract a(ZZLandroidx/compose/runtime/i;I)Landroidx/compose/runtime/f3;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(ZZ",
            "Landroidx/compose/runtime/i;",
            "I)",
            "Landroidx/compose/runtime/f3<",
            "Landroidx/compose/ui/graphics/w1;",
            ">;"
        }
    .end annotation
.end method

.method public abstract b(ZZLandroidx/compose/runtime/i;I)Landroidx/compose/runtime/f3;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(ZZ",
            "Landroidx/compose/runtime/i;",
            "I)",
            "Landroidx/compose/runtime/f3<",
            "Landroidx/compose/ui/graphics/w1;",
            ">;"
        }
    .end annotation
.end method

.method public abstract c(ZLandroidx/compose/runtime/i;I)Landroidx/compose/runtime/f3;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(Z",
            "Landroidx/compose/runtime/i;",
            "I)",
            "Landroidx/compose/runtime/f3<",
            "Landroidx/compose/ui/graphics/w1;",
            ">;"
        }
    .end annotation
.end method
