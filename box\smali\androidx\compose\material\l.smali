.class public final Landroidx/compose/material/l;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Landroidx/compose/material/l$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation

.annotation runtime Lkotlin/jvm/JvmInline;
.end annotation


# static fields
.field public static final a:Landroidx/compose/material/l$a;

.field public static final b:I

.field public static final c:I

.field public static final d:I

.field public static final e:I

.field public static final f:I

.field public static final g:I

.field public static final h:I


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Landroidx/compose/material/l$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Landroidx/compose/material/l$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Landroidx/compose/material/l;->a:Landroidx/compose/material/l$a;

    const/4 v0, 0x0

    invoke-static {v0}, Landroidx/compose/material/l;->h(I)I

    move-result v0

    sput v0, Landroidx/compose/material/l;->b:I

    const/4 v0, 0x1

    invoke-static {v0}, Landroidx/compose/material/l;->h(I)I

    move-result v0

    sput v0, Landroidx/compose/material/l;->c:I

    const/4 v0, 0x2

    invoke-static {v0}, Landroidx/compose/material/l;->h(I)I

    move-result v0

    sput v0, Landroidx/compose/material/l;->d:I

    const/4 v0, 0x3

    invoke-static {v0}, Landroidx/compose/material/l;->h(I)I

    move-result v0

    sput v0, Landroidx/compose/material/l;->e:I

    const/4 v0, 0x4

    invoke-static {v0}, Landroidx/compose/material/l;->h(I)I

    move-result v0

    sput v0, Landroidx/compose/material/l;->f:I

    const/4 v0, 0x5

    invoke-static {v0}, Landroidx/compose/material/l;->h(I)I

    move-result v0

    sput v0, Landroidx/compose/material/l;->g:I

    const/4 v0, 0x6

    invoke-static {v0}, Landroidx/compose/material/l;->h(I)I

    move-result v0

    sput v0, Landroidx/compose/material/l;->h:I

    return-void
.end method

.method public static final synthetic a()I
    .locals 1

    sget v0, Landroidx/compose/material/l;->c:I

    return v0
.end method

.method public static final synthetic b()I
    .locals 1

    sget v0, Landroidx/compose/material/l;->d:I

    return v0
.end method

.method public static final synthetic c()I
    .locals 1

    sget v0, Landroidx/compose/material/l;->e:I

    return v0
.end method

.method public static final synthetic d()I
    .locals 1

    sget v0, Landroidx/compose/material/l;->f:I

    return v0
.end method

.method public static final synthetic e()I
    .locals 1

    sget v0, Landroidx/compose/material/l;->b:I

    return v0
.end method

.method public static final synthetic f()I
    .locals 1

    sget v0, Landroidx/compose/material/l;->h:I

    return v0
.end method

.method public static final synthetic g()I
    .locals 1

    sget v0, Landroidx/compose/material/l;->g:I

    return v0
.end method

.method public static h(I)I
    .locals 0

    return p0
.end method

.method public static final i(II)Z
    .locals 0

    if-ne p0, p1, :cond_0

    const/4 p0, 0x1

    goto :goto_0

    :cond_0
    const/4 p0, 0x0

    :goto_0
    return p0
.end method
