.class public abstract Landroidx/compose/material/ripple/RippleNode;
.super Landroidx/compose/ui/f$c;

# interfaces
.implements Landroidx/compose/ui/node/d;
.implements Landroidx/compose/ui/node/n;
.implements Landroidx/compose/ui/node/w;


# annotations
.annotation runtime Lkotlin/Metadata;
.end annotation

.annotation build Lkotlin/jvm/internal/SourceDebugExtension;
.end annotation


# instance fields
.field public final n:Lt/g;

.field public final o:Z

.field public final p:F

.field public final q:Landroidx/compose/ui/graphics/z1;

.field public final r:Lkotlin/jvm/functions/Function0;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlin/jvm/functions/Function0<",
            "Landroidx/compose/material/ripple/c;",
            ">;"
        }
    .end annotation
.end field

.field public final s:Z

.field public t:Landroidx/compose/material/ripple/StateLayer;

.field public u:F

.field public v:J

.field public w:Z

.field public final x:Landroidx/collection/k0;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Landroidx/collection/k0<",
            "Lt/k;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Lt/g;ZFLandroidx/compose/ui/graphics/z1;Lkotlin/jvm/functions/Function0;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lt/g;",
            "ZF",
            "Landroidx/compose/ui/graphics/z1;",
            "Lkotlin/jvm/functions/Function0<",
            "Landroidx/compose/material/ripple/c;",
            ">;)V"
        }
    .end annotation

    invoke-direct {p0}, Landroidx/compose/ui/f$c;-><init>()V

    iput-object p1, p0, Landroidx/compose/material/ripple/RippleNode;->n:Lt/g;

    iput-boolean p2, p0, Landroidx/compose/material/ripple/RippleNode;->o:Z

    iput p3, p0, Landroidx/compose/material/ripple/RippleNode;->p:F

    iput-object p4, p0, Landroidx/compose/material/ripple/RippleNode;->q:Landroidx/compose/ui/graphics/z1;

    iput-object p5, p0, Landroidx/compose/material/ripple/RippleNode;->r:Lkotlin/jvm/functions/Function0;

    sget-object p1, Ld0/m;->b:Ld0/m$a;

    invoke-virtual {p1}, Ld0/m$a;->b()J

    move-result-wide p1

    iput-wide p1, p0, Landroidx/compose/material/ripple/RippleNode;->v:J

    new-instance p1, Landroidx/collection/k0;

    const/4 p2, 0x1

    const/4 p3, 0x0

    const/4 p4, 0x0

    invoke-direct {p1, p4, p2, p3}, Landroidx/collection/k0;-><init>(IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    iput-object p1, p0, Landroidx/compose/material/ripple/RippleNode;->x:Landroidx/collection/k0;

    return-void
.end method

.method public synthetic constructor <init>(Lt/g;ZFLandroidx/compose/ui/graphics/z1;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    invoke-direct/range {p0 .. p5}, Landroidx/compose/material/ripple/RippleNode;-><init>(Lt/g;ZFLandroidx/compose/ui/graphics/z1;Lkotlin/jvm/functions/Function0;)V

    return-void
.end method

.method public static final synthetic J1(Landroidx/compose/material/ripple/RippleNode;)Z
    .locals 0

    iget-boolean p0, p0, Landroidx/compose/material/ripple/RippleNode;->w:Z

    return p0
.end method

.method public static final synthetic K1(Landroidx/compose/material/ripple/RippleNode;)Lt/g;
    .locals 0

    iget-object p0, p0, Landroidx/compose/material/ripple/RippleNode;->n:Lt/g;

    return-object p0
.end method

.method public static final synthetic L1(Landroidx/compose/material/ripple/RippleNode;)Landroidx/collection/k0;
    .locals 0

    iget-object p0, p0, Landroidx/compose/material/ripple/RippleNode;->x:Landroidx/collection/k0;

    return-object p0
.end method

.method public static final synthetic M1(Landroidx/compose/material/ripple/RippleNode;Lt/k;)V
    .locals 0

    invoke-virtual {p0, p1}, Landroidx/compose/material/ripple/RippleNode;->U1(Lt/k;)V

    return-void
.end method

.method public static final synthetic N1(Landroidx/compose/material/ripple/RippleNode;Lt/f;Lkotlinx/coroutines/k0;)V
    .locals 0

    invoke-virtual {p0, p1, p2}, Landroidx/compose/material/ripple/RippleNode;->W1(Lt/f;Lkotlinx/coroutines/k0;)V

    return-void
.end method


# virtual methods
.method public synthetic G0()V
    .locals 0

    invoke-static {p0}, Landroidx/compose/ui/node/m;->a(Landroidx/compose/ui/node/n;)V

    return-void
.end method

.method public abstract O1(Lt/k$b;JF)V
.end method

.method public abstract P1(Le0/g;)V
.end method

.method public final Q1()Z
    .locals 1

    iget-boolean v0, p0, Landroidx/compose/material/ripple/RippleNode;->o:Z

    return v0
.end method

.method public final R1()Lkotlin/jvm/functions/Function0;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlin/jvm/functions/Function0<",
            "Landroidx/compose/material/ripple/c;",
            ">;"
        }
    .end annotation

    iget-object v0, p0, Landroidx/compose/material/ripple/RippleNode;->r:Lkotlin/jvm/functions/Function0;

    return-object v0
.end method

.method public final S1()J
    .locals 2

    iget-object v0, p0, Landroidx/compose/material/ripple/RippleNode;->q:Landroidx/compose/ui/graphics/z1;

    invoke-interface {v0}, Landroidx/compose/ui/graphics/z1;->a()J

    move-result-wide v0

    return-wide v0
.end method

.method public final T1()J
    .locals 2

    iget-wide v0, p0, Landroidx/compose/material/ripple/RippleNode;->v:J

    return-wide v0
.end method

.method public final U1(Lt/k;)V
    .locals 3

    instance-of v0, p1, Lt/k$b;

    if-eqz v0, :cond_0

    check-cast p1, Lt/k$b;

    iget-wide v0, p0, Landroidx/compose/material/ripple/RippleNode;->v:J

    iget v2, p0, Landroidx/compose/material/ripple/RippleNode;->u:F

    invoke-virtual {p0, p1, v0, v1, v2}, Landroidx/compose/material/ripple/RippleNode;->O1(Lt/k$b;JF)V

    goto :goto_0

    :cond_0
    instance-of v0, p1, Lt/k$c;

    if-eqz v0, :cond_1

    check-cast p1, Lt/k$c;

    invoke-virtual {p1}, Lt/k$c;->a()Lt/k$b;

    move-result-object p1

    invoke-virtual {p0, p1}, Landroidx/compose/material/ripple/RippleNode;->V1(Lt/k$b;)V

    goto :goto_0

    :cond_1
    instance-of v0, p1, Lt/k$a;

    if-eqz v0, :cond_2

    check-cast p1, Lt/k$a;

    invoke-virtual {p1}, Lt/k$a;->a()Lt/k$b;

    move-result-object p1

    invoke-virtual {p0, p1}, Landroidx/compose/material/ripple/RippleNode;->V1(Lt/k$b;)V

    :cond_2
    :goto_0
    return-void
.end method

.method public abstract V1(Lt/k$b;)V
.end method

.method public final W1(Lt/f;Lkotlinx/coroutines/k0;)V
    .locals 3

    iget-object v0, p0, Landroidx/compose/material/ripple/RippleNode;->t:Landroidx/compose/material/ripple/StateLayer;

    if-nez v0, :cond_0

    new-instance v0, Landroidx/compose/material/ripple/StateLayer;

    iget-boolean v1, p0, Landroidx/compose/material/ripple/RippleNode;->o:Z

    iget-object v2, p0, Landroidx/compose/material/ripple/RippleNode;->r:Lkotlin/jvm/functions/Function0;

    invoke-direct {v0, v1, v2}, Landroidx/compose/material/ripple/StateLayer;-><init>(ZLkotlin/jvm/functions/Function0;)V

    invoke-static {p0}, Landroidx/compose/ui/node/o;->a(Landroidx/compose/ui/node/n;)V

    iput-object v0, p0, Landroidx/compose/material/ripple/RippleNode;->t:Landroidx/compose/material/ripple/StateLayer;

    :cond_0
    invoke-virtual {v0, p1, p2}, Landroidx/compose/material/ripple/StateLayer;->c(Lt/f;Lkotlinx/coroutines/k0;)V

    return-void
.end method

.method public l(J)V
    .locals 3

    const/4 v0, 0x1

    iput-boolean v0, p0, Landroidx/compose/material/ripple/RippleNode;->w:Z

    invoke-static {p0}, Landroidx/compose/ui/node/g;->i(Landroidx/compose/ui/node/f;)Lv0/e;

    move-result-object v0

    invoke-static {p1, p2}, Lv0/u;->d(J)J

    move-result-wide p1

    iput-wide p1, p0, Landroidx/compose/material/ripple/RippleNode;->v:J

    iget p1, p0, Landroidx/compose/material/ripple/RippleNode;->p:F

    invoke-static {p1}, Ljava/lang/Float;->isNaN(F)Z

    move-result p1

    if-eqz p1, :cond_0

    iget-boolean p1, p0, Landroidx/compose/material/ripple/RippleNode;->o:Z

    iget-wide v1, p0, Landroidx/compose/material/ripple/RippleNode;->v:J

    invoke-static {v0, p1, v1, v2}, Landroidx/compose/material/ripple/d;->a(Lv0/e;ZJ)F

    move-result p1

    goto :goto_0

    :cond_0
    iget p1, p0, Landroidx/compose/material/ripple/RippleNode;->p:F

    invoke-interface {v0, p1}, Lv0/e;->O0(F)F

    move-result p1

    :goto_0
    iput p1, p0, Landroidx/compose/material/ripple/RippleNode;->u:F

    iget-object p1, p0, Landroidx/compose/material/ripple/RippleNode;->x:Landroidx/collection/k0;

    iget-object p2, p1, Landroidx/collection/ObjectList;->a:[Ljava/lang/Object;

    iget p1, p1, Landroidx/collection/ObjectList;->b:I

    const/4 v0, 0x0

    :goto_1
    if-ge v0, p1, :cond_1

    aget-object v1, p2, v0

    check-cast v1, Lt/k;

    invoke-virtual {p0, v1}, Landroidx/compose/material/ripple/RippleNode;->U1(Lt/k;)V

    add-int/lit8 v0, v0, 0x1

    goto :goto_1

    :cond_1
    iget-object p1, p0, Landroidx/compose/material/ripple/RippleNode;->x:Landroidx/collection/k0;

    invoke-virtual {p1}, Landroidx/collection/k0;->f()V

    return-void
.end method

.method public synthetic m(Landroidx/compose/ui/layout/m;)V
    .locals 0

    invoke-static {p0, p1}, Landroidx/compose/ui/node/v;->a(Landroidx/compose/ui/node/w;Landroidx/compose/ui/layout/m;)V

    return-void
.end method

.method public final o1()Z
    .locals 1

    iget-boolean v0, p0, Landroidx/compose/material/ripple/RippleNode;->s:Z

    return v0
.end method

.method public t1()V
    .locals 6

    invoke-virtual {p0}, Landroidx/compose/ui/f$c;->j1()Lkotlinx/coroutines/k0;

    move-result-object v0

    const/4 v1, 0x0

    const/4 v2, 0x0

    new-instance v3, Landroidx/compose/material/ripple/RippleNode$onAttach$1;

    const/4 v4, 0x0

    invoke-direct {v3, p0, v4}, Landroidx/compose/material/ripple/RippleNode$onAttach$1;-><init>(Landroidx/compose/material/ripple/RippleNode;Lkotlin/coroutines/Continuation;)V

    const/4 v4, 0x3

    const/4 v5, 0x0

    invoke-static/range {v0 .. v5}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/k0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/q1;

    return-void
.end method

.method public y(Le0/c;)V
    .locals 4

    invoke-interface {p1}, Le0/c;->c1()V

    iget-object v0, p0, Landroidx/compose/material/ripple/RippleNode;->t:Landroidx/compose/material/ripple/StateLayer;

    if-eqz v0, :cond_0

    iget v1, p0, Landroidx/compose/material/ripple/RippleNode;->u:F

    invoke-virtual {p0}, Landroidx/compose/material/ripple/RippleNode;->S1()J

    move-result-wide v2

    invoke-virtual {v0, p1, v1, v2, v3}, Landroidx/compose/material/ripple/StateLayer;->b(Le0/g;FJ)V

    :cond_0
    invoke-virtual {p0, p1}, Landroidx/compose/material/ripple/RippleNode;->P1(Le0/g;)V

    return-void
.end method
