.class public interface abstract Landroidx/compose/material/ripple/i;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Landroidx/compose/material/ripple/i$a;
    }
.end annotation

.annotation runtime Lkotlin/Deprecated;
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation


# static fields
.field public static final a:Landroidx/compose/material/ripple/i$a;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    sget-object v0, Landroidx/compose/material/ripple/i$a;->a:Landroidx/compose/material/ripple/i$a;

    sput-object v0, Landroidx/compose/material/ripple/i;->a:Landroidx/compose/material/ripple/i$a;

    return-void
.end method


# virtual methods
.method public abstract a(Landroidx/compose/runtime/i;I)J
    .annotation runtime Lkotlin/Deprecated;
    .end annotation
.end method

.method public abstract b(Landroidx/compose/runtime/i;I)Landroidx/compose/material/ripple/c;
    .annotation runtime L<PERSON><PERSON>/Deprecated;
    .end annotation
.end method
