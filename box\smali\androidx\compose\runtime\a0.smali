.class public interface abstract Landroidx/compose/runtime/a0;
.super Ljava/lang/Object;


# annotations
.annotation runtime L<PERSON><PERSON>/Metadata;
.end annotation


# virtual methods
.method public abstract a(Landroidx/compose/runtime/z;)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroidx/compose/runtime/z<",
            "*>;)V"
        }
    .end annotation
.end method

.method public abstract b(Landroidx/compose/runtime/z;)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroidx/compose/runtime/z<",
            "*>;)V"
        }
    .end annotation
.end method
