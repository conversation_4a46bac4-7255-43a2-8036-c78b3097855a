.class public interface abstract Landroidx/compose/runtime/a2;
.super Ljava/lang/Object;


# annotations
.annotation runtime L<PERSON><PERSON>/Metadata;
.end annotation


# virtual methods
.method public abstract a(Lkotlin/jvm/functions/Function0;)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation
.end method

.method public abstract b(Landroidx/compose/runtime/h;III)V
.end method

.method public abstract c(Landroidx/compose/runtime/b2;)V
.end method

.method public abstract d(Landroidx/compose/runtime/h;III)V
.end method

.method public abstract e(Landroidx/compose/runtime/b2;III)V
.end method
