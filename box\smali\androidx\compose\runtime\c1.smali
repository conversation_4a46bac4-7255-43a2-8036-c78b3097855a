.class public interface abstract Landroidx/compose/runtime/c1;
.super Ljava/lang/Object;

# interfaces
.implements Landroidx/compose/runtime/h0;
.implements Landroidx/compose/runtime/i1;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Landroidx/compose/runtime/h0;",
        "Landroidx/compose/runtime/i1<",
        "Ljava/lang/Float;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation


# virtual methods
.method public abstract A(F)V
.end method

.method public abstract a()F
.end method

.method public abstract getValue()Ljava/lang/Float;
.end method

.method public abstract m(F)V
.end method
