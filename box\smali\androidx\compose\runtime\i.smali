.class public interface abstract Landroidx/compose/runtime/i;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Landroidx/compose/runtime/i$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation


# static fields
.field public static final a:Landroidx/compose/runtime/i$a;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    sget-object v0, Landroidx/compose/runtime/i$a;->a:Landroidx/compose/runtime/i$a;

    sput-object v0, Landroidx/compose/runtime/i;->a:Landroidx/compose/runtime/i$a;

    return-void
.end method


# virtual methods
.method public abstract A(Ljava/lang/Object;)Z
.end method

.method public abstract B()V
.end method

.method public abstract C(ILjava/lang/Object;)V
.end method

.method public abstract D()V
.end method

.method public abstract E(Landroidx/compose/runtime/t1;)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroidx/compose/runtime/t1<",
            "*>;)V"
        }
    .end annotation
.end method

.method public abstract F(ILjava/lang/Object;)V
.end method

.method public abstract G(Lkotlin/jvm/functions/Function0;)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">(",
            "Lkotlin/jvm/functions/Function0<",
            "+TT;>;)V"
        }
    .end annotation
.end method

.method public abstract H()V
.end method

.method public abstract I()V
.end method

.method public abstract J()Z
.end method

.method public abstract K(Landroidx/compose/runtime/u1;)V
.end method

.method public abstract L()V
.end method

.method public abstract M()I
.end method

.method public abstract N()Landroidx/compose/runtime/m;
.end method

.method public abstract O()V
.end method

.method public abstract P()V
.end method

.method public abstract Q(Ljava/lang/Object;)Z
.end method

.method public abstract R(I)V
.end method

.method public abstract S([Landroidx/compose/runtime/t1;)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "([",
            "Landroidx/compose/runtime/t1<",
            "*>;)V"
        }
    .end annotation
.end method

.method public abstract a(Z)Z
.end method

.method public abstract b(F)Z
.end method

.method public abstract c(I)Z
.end method

.method public abstract d(J)Z
.end method

.method public abstract e()Z
.end method

.method public abstract f(Z)V
.end method

.method public abstract g(I)Landroidx/compose/runtime/i;
.end method

.method public abstract h()Z
.end method

.method public abstract i()Landroidx/compose/runtime/f;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Landroidx/compose/runtime/f<",
            "*>;"
        }
    .end annotation
.end method

.method public abstract j()Landroidx/compose/runtime/g2;
.end method

.method public abstract k(Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<V:",
            "Ljava/lang/Object;",
            "T:",
            "Ljava/lang/Object;",
            ">(TV;",
            "Lkotlin/jvm/functions/Function2<",
            "-TT;-TV;",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation
.end method

.method public abstract l(Landroidx/compose/runtime/q;)Ljava/lang/Object;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">(",
            "Landroidx/compose/runtime/q<",
            "TT;>;)TT;"
        }
    .end annotation
.end method

.method public abstract m()Lkotlin/coroutines/CoroutineContext;
.end method

.method public abstract n()Landroidx/compose/runtime/s;
.end method

.method public abstract o()V
.end method

.method public abstract p(Ljava/lang/Object;)V
.end method

.method public abstract q()V
.end method

.method public abstract r()V
.end method

.method public abstract s()V
.end method

.method public abstract t(Lkotlin/jvm/functions/Function0;)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation
.end method

.method public abstract u()V
.end method

.method public abstract v()Landroidx/compose/runtime/u1;
.end method

.method public abstract w()V
.end method

.method public abstract x(I)V
.end method

.method public abstract y()Ljava/lang/Object;
.end method

.method public abstract z()Lb0/a;
.end method
