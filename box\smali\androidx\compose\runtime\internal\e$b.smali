.class public final Landroidx/compose/runtime/internal/e$b;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/compose/runtime/internal/e;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "b"
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public synthetic constructor <init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    invoke-direct {p0}, Landroidx/compose/runtime/internal/e$b;-><init>()V

    return-void
.end method


# virtual methods
.method public final a()Landroidx/compose/runtime/internal/e;
    .locals 1

    invoke-static {}, Landroidx/compose/runtime/internal/e;->w()Landroidx/compose/runtime/internal/e;

    move-result-object v0

    return-object v0
.end method
