.class public final Landroidx/compose/runtime/n;
.super Ljava/lang/Object;


# annotations
.annotation runtime L<PERSON>lin/Metadata;
.end annotation


# static fields
.field public static final a:Landroidx/compose/runtime/n1;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    invoke-static {}, Landroidx/compose/runtime/internal/f;->a()Landroidx/compose/runtime/internal/e;

    move-result-object v0

    sput-object v0, Landroidx/compose/runtime/n;->a:Landroidx/compose/runtime/n1;

    return-void
.end method

.method public static final synthetic a()Landroidx/compose/runtime/n1;
    .locals 1

    sget-object v0, Landroidx/compose/runtime/n;->a:Landroidx/compose/runtime/n1;

    return-object v0
.end method
