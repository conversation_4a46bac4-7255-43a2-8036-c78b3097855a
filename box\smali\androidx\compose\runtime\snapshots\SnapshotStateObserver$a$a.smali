.class public final Landroidx/compose/runtime/snapshots/SnapshotStateObserver$a$a;
.super Ljava/lang/Object;

# interfaces
.implements Landroidx/compose/runtime/a0;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Landroidx/compose/runtime/snapshots/SnapshotStateObserver$a;-><init>(Lkotlin/jvm/functions/Function1;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation


# instance fields
.field public final synthetic a:Landroidx/compose/runtime/snapshots/SnapshotStateObserver$a;


# direct methods
.method public constructor <init>(Landroidx/compose/runtime/snapshots/SnapshotStateObserver$a;)V
    .locals 0

    iput-object p1, p0, Landroidx/compose/runtime/snapshots/SnapshotStateObserver$a$a;->a:Landroidx/compose/runtime/snapshots/SnapshotStateObserver$a;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public a(Landroidx/compose/runtime/z;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroidx/compose/runtime/z<",
            "*>;)V"
        }
    .end annotation

    iget-object p1, p0, Landroidx/compose/runtime/snapshots/SnapshotStateObserver$a$a;->a:Landroidx/compose/runtime/snapshots/SnapshotStateObserver$a;

    invoke-static {p1}, Landroidx/compose/runtime/snapshots/SnapshotStateObserver$a;->a(Landroidx/compose/runtime/snapshots/SnapshotStateObserver$a;)I

    move-result v0

    add-int/lit8 v0, v0, -0x1

    invoke-static {p1, v0}, Landroidx/compose/runtime/snapshots/SnapshotStateObserver$a;->b(Landroidx/compose/runtime/snapshots/SnapshotStateObserver$a;I)V

    return-void
.end method

.method public b(Landroidx/compose/runtime/z;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroidx/compose/runtime/z<",
            "*>;)V"
        }
    .end annotation

    iget-object p1, p0, Landroidx/compose/runtime/snapshots/SnapshotStateObserver$a$a;->a:Landroidx/compose/runtime/snapshots/SnapshotStateObserver$a;

    invoke-static {p1}, Landroidx/compose/runtime/snapshots/SnapshotStateObserver$a;->a(Landroidx/compose/runtime/snapshots/SnapshotStateObserver$a;)I

    move-result v0

    add-int/lit8 v0, v0, 0x1

    invoke-static {p1, v0}, Landroidx/compose/runtime/snapshots/SnapshotStateObserver$a;->b(Landroidx/compose/runtime/snapshots/SnapshotStateObserver$a;I)V

    return-void
.end method
