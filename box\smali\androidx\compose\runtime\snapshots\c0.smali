.class public interface abstract Landroidx/compose/runtime/snapshots/c0;
.super Ljava/lang/Object;


# annotations
.annotation runtime Lkotlin/Metadata;
.end annotation


# virtual methods
.method public abstract v(Landroidx/compose/runtime/snapshots/e0;)V
.end method

.method public abstract y()Landroidx/compose/runtime/snapshots/e0;
.end method

.method public abstract z(Landroidx/compose/runtime/snapshots/e0;Landroidx/compose/runtime/snapshots/e0;Landroidx/compose/runtime/snapshots/e0;)Landroidx/compose/runtime/snapshots/e0;
.end method
