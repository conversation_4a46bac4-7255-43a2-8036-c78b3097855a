.class public interface abstract Landroidx/compose/runtime/snapshots/r;
.super Ljava/lang/Object;

# interfaces
.implements Landroidx/compose/runtime/i1;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "<T:",
        "Ljava/lang/Object;",
        ">",
        "Ljava/lang/Object;",
        "Landroidx/compose/runtime/i1<",
        "TT;>;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation


# virtual methods
.method public abstract c()Landroidx/compose/runtime/v2;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Landroidx/compose/runtime/v2<",
            "TT;>;"
        }
    .end annotation
.end method
