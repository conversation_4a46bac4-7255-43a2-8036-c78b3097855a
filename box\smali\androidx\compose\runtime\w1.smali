.class public interface abstract Landroidx/compose/runtime/w1;
.super Ljava/lang/Object;


# annotations
.annotation runtime L<PERSON><PERSON>/Metadata;
.end annotation


# virtual methods
.method public abstract a(Ljava/lang/Object;)V
.end method

.method public abstract h(Landroidx/compose/runtime/RecomposeScopeImpl;)V
.end method

.method public abstract k(Landroidx/compose/runtime/RecomposeScopeImpl;Ljava/lang/Object;)Landroidx/compose/runtime/InvalidationResult;
.end method
