.class public final Landroidx/compose/runtime/x0;
.super Ljava/lang/Object;


# annotations
.annotation runtime Lkotlin/Metadata;
.end annotation


# instance fields
.field public final a:Landroidx/compose/runtime/j2;


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Landroidx/compose/runtime/j2;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Landroidx/compose/runtime/x0;->a:Landroidx/compose/runtime/j2;

    return-void
.end method


# virtual methods
.method public final a()Landroidx/compose/runtime/j2;
    .locals 1

    iget-object v0, p0, Landroidx/compose/runtime/x0;->a:Landroidx/compose/runtime/j2;

    return-object v0
.end method
