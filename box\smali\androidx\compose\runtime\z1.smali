.class public final Landroidx/compose/runtime/z1;
.super Landroidx/compose/runtime/c3;


# annotations
.annotation runtime L<PERSON><PERSON>/Metadata;
.end annotation


# instance fields
.field public final a:Landroidx/compose/runtime/c3;

.field public final b:I


# direct methods
.method public constructor <init>(Landroidx/compose/runtime/c3;I)V
    .locals 1

    const/4 v0, 0x0

    invoke-direct {p0, v0}, Landroidx/compose/runtime/c3;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    iput-object p1, p0, Landroidx/compose/runtime/z1;->a:Landroidx/compose/runtime/c3;

    iput p2, p0, Landroidx/compose/runtime/z1;->b:I

    return-void
.end method
