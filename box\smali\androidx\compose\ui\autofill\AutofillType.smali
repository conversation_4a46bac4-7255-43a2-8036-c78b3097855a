.class public final enum Landroidx/compose/ui/autofill/AutofillType;
.super Ljava/lang/Enum;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Enum<",
        "Landroidx/compose/ui/autofill/AutofillType;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation


# static fields
.field private static final synthetic $VALUES:[Landroidx/compose/ui/autofill/AutofillType;

.field public static final enum AddressAuxiliaryDetails:Landroidx/compose/ui/autofill/AutofillType;

.field public static final enum AddressCountry:Landroidx/compose/ui/autofill/AutofillType;

.field public static final enum AddressLocality:Landroidx/compose/ui/autofill/AutofillType;

.field public static final enum AddressRegion:Landroidx/compose/ui/autofill/AutofillType;

.field public static final enum AddressStreet:Landroidx/compose/ui/autofill/AutofillType;

.field public static final enum BirthDateDay:Landroidx/compose/ui/autofill/AutofillType;

.field public static final enum BirthDateFull:Landroidx/compose/ui/autofill/AutofillType;

.field public static final enum BirthDateMonth:Landroidx/compose/ui/autofill/AutofillType;

.field public static final enum BirthDateYear:Landroidx/compose/ui/autofill/AutofillType;

.field public static final enum CreditCardExpirationDate:Landroidx/compose/ui/autofill/AutofillType;

.field public static final enum CreditCardExpirationDay:Landroidx/compose/ui/autofill/AutofillType;

.field public static final enum CreditCardExpirationMonth:Landroidx/compose/ui/autofill/AutofillType;

.field public static final enum CreditCardExpirationYear:Landroidx/compose/ui/autofill/AutofillType;

.field public static final enum CreditCardNumber:Landroidx/compose/ui/autofill/AutofillType;

.field public static final enum CreditCardSecurityCode:Landroidx/compose/ui/autofill/AutofillType;

.field public static final enum EmailAddress:Landroidx/compose/ui/autofill/AutofillType;

.field public static final enum Gender:Landroidx/compose/ui/autofill/AutofillType;

.field public static final enum NewPassword:Landroidx/compose/ui/autofill/AutofillType;

.field public static final enum NewUsername:Landroidx/compose/ui/autofill/AutofillType;

.field public static final enum Password:Landroidx/compose/ui/autofill/AutofillType;

.field public static final enum PersonFirstName:Landroidx/compose/ui/autofill/AutofillType;

.field public static final enum PersonFullName:Landroidx/compose/ui/autofill/AutofillType;

.field public static final enum PersonLastName:Landroidx/compose/ui/autofill/AutofillType;

.field public static final enum PersonMiddleInitial:Landroidx/compose/ui/autofill/AutofillType;

.field public static final enum PersonMiddleName:Landroidx/compose/ui/autofill/AutofillType;

.field public static final enum PersonNamePrefix:Landroidx/compose/ui/autofill/AutofillType;

.field public static final enum PersonNameSuffix:Landroidx/compose/ui/autofill/AutofillType;

.field public static final enum PhoneCountryCode:Landroidx/compose/ui/autofill/AutofillType;

.field public static final enum PhoneNumber:Landroidx/compose/ui/autofill/AutofillType;

.field public static final enum PhoneNumberDevice:Landroidx/compose/ui/autofill/AutofillType;

.field public static final enum PhoneNumberNational:Landroidx/compose/ui/autofill/AutofillType;

.field public static final enum PostalAddress:Landroidx/compose/ui/autofill/AutofillType;

.field public static final enum PostalCode:Landroidx/compose/ui/autofill/AutofillType;

.field public static final enum PostalCodeExtended:Landroidx/compose/ui/autofill/AutofillType;

.field public static final enum SmsOtpCode:Landroidx/compose/ui/autofill/AutofillType;

.field public static final enum Username:Landroidx/compose/ui/autofill/AutofillType;


# direct methods
.method private static final synthetic $values()[Landroidx/compose/ui/autofill/AutofillType;
    .locals 3

    const/16 v0, 0x24

    new-array v0, v0, [Landroidx/compose/ui/autofill/AutofillType;

    const/4 v1, 0x0

    sget-object v2, Landroidx/compose/ui/autofill/AutofillType;->EmailAddress:Landroidx/compose/ui/autofill/AutofillType;

    aput-object v2, v0, v1

    const/4 v1, 0x1

    sget-object v2, Landroidx/compose/ui/autofill/AutofillType;->Username:Landroidx/compose/ui/autofill/AutofillType;

    aput-object v2, v0, v1

    const/4 v1, 0x2

    sget-object v2, Landroidx/compose/ui/autofill/AutofillType;->Password:Landroidx/compose/ui/autofill/AutofillType;

    aput-object v2, v0, v1

    const/4 v1, 0x3

    sget-object v2, Landroidx/compose/ui/autofill/AutofillType;->NewUsername:Landroidx/compose/ui/autofill/AutofillType;

    aput-object v2, v0, v1

    const/4 v1, 0x4

    sget-object v2, Landroidx/compose/ui/autofill/AutofillType;->NewPassword:Landroidx/compose/ui/autofill/AutofillType;

    aput-object v2, v0, v1

    const/4 v1, 0x5

    sget-object v2, Landroidx/compose/ui/autofill/AutofillType;->PostalAddress:Landroidx/compose/ui/autofill/AutofillType;

    aput-object v2, v0, v1

    const/4 v1, 0x6

    sget-object v2, Landroidx/compose/ui/autofill/AutofillType;->PostalCode:Landroidx/compose/ui/autofill/AutofillType;

    aput-object v2, v0, v1

    const/4 v1, 0x7

    sget-object v2, Landroidx/compose/ui/autofill/AutofillType;->CreditCardNumber:Landroidx/compose/ui/autofill/AutofillType;

    aput-object v2, v0, v1

    const/16 v1, 0x8

    sget-object v2, Landroidx/compose/ui/autofill/AutofillType;->CreditCardSecurityCode:Landroidx/compose/ui/autofill/AutofillType;

    aput-object v2, v0, v1

    const/16 v1, 0x9

    sget-object v2, Landroidx/compose/ui/autofill/AutofillType;->CreditCardExpirationDate:Landroidx/compose/ui/autofill/AutofillType;

    aput-object v2, v0, v1

    const/16 v1, 0xa

    sget-object v2, Landroidx/compose/ui/autofill/AutofillType;->CreditCardExpirationMonth:Landroidx/compose/ui/autofill/AutofillType;

    aput-object v2, v0, v1

    const/16 v1, 0xb

    sget-object v2, Landroidx/compose/ui/autofill/AutofillType;->CreditCardExpirationYear:Landroidx/compose/ui/autofill/AutofillType;

    aput-object v2, v0, v1

    const/16 v1, 0xc

    sget-object v2, Landroidx/compose/ui/autofill/AutofillType;->CreditCardExpirationDay:Landroidx/compose/ui/autofill/AutofillType;

    aput-object v2, v0, v1

    const/16 v1, 0xd

    sget-object v2, Landroidx/compose/ui/autofill/AutofillType;->AddressCountry:Landroidx/compose/ui/autofill/AutofillType;

    aput-object v2, v0, v1

    const/16 v1, 0xe

    sget-object v2, Landroidx/compose/ui/autofill/AutofillType;->AddressRegion:Landroidx/compose/ui/autofill/AutofillType;

    aput-object v2, v0, v1

    const/16 v1, 0xf

    sget-object v2, Landroidx/compose/ui/autofill/AutofillType;->AddressLocality:Landroidx/compose/ui/autofill/AutofillType;

    aput-object v2, v0, v1

    const/16 v1, 0x10

    sget-object v2, Landroidx/compose/ui/autofill/AutofillType;->AddressStreet:Landroidx/compose/ui/autofill/AutofillType;

    aput-object v2, v0, v1

    const/16 v1, 0x11

    sget-object v2, Landroidx/compose/ui/autofill/AutofillType;->AddressAuxiliaryDetails:Landroidx/compose/ui/autofill/AutofillType;

    aput-object v2, v0, v1

    const/16 v1, 0x12

    sget-object v2, Landroidx/compose/ui/autofill/AutofillType;->PostalCodeExtended:Landroidx/compose/ui/autofill/AutofillType;

    aput-object v2, v0, v1

    const/16 v1, 0x13

    sget-object v2, Landroidx/compose/ui/autofill/AutofillType;->PersonFullName:Landroidx/compose/ui/autofill/AutofillType;

    aput-object v2, v0, v1

    const/16 v1, 0x14

    sget-object v2, Landroidx/compose/ui/autofill/AutofillType;->PersonFirstName:Landroidx/compose/ui/autofill/AutofillType;

    aput-object v2, v0, v1

    const/16 v1, 0x15

    sget-object v2, Landroidx/compose/ui/autofill/AutofillType;->PersonLastName:Landroidx/compose/ui/autofill/AutofillType;

    aput-object v2, v0, v1

    const/16 v1, 0x16

    sget-object v2, Landroidx/compose/ui/autofill/AutofillType;->PersonMiddleName:Landroidx/compose/ui/autofill/AutofillType;

    aput-object v2, v0, v1

    const/16 v1, 0x17

    sget-object v2, Landroidx/compose/ui/autofill/AutofillType;->PersonMiddleInitial:Landroidx/compose/ui/autofill/AutofillType;

    aput-object v2, v0, v1

    const/16 v1, 0x18

    sget-object v2, Landroidx/compose/ui/autofill/AutofillType;->PersonNamePrefix:Landroidx/compose/ui/autofill/AutofillType;

    aput-object v2, v0, v1

    const/16 v1, 0x19

    sget-object v2, Landroidx/compose/ui/autofill/AutofillType;->PersonNameSuffix:Landroidx/compose/ui/autofill/AutofillType;

    aput-object v2, v0, v1

    const/16 v1, 0x1a

    sget-object v2, Landroidx/compose/ui/autofill/AutofillType;->PhoneNumber:Landroidx/compose/ui/autofill/AutofillType;

    aput-object v2, v0, v1

    const/16 v1, 0x1b

    sget-object v2, Landroidx/compose/ui/autofill/AutofillType;->PhoneNumberDevice:Landroidx/compose/ui/autofill/AutofillType;

    aput-object v2, v0, v1

    const/16 v1, 0x1c

    sget-object v2, Landroidx/compose/ui/autofill/AutofillType;->PhoneCountryCode:Landroidx/compose/ui/autofill/AutofillType;

    aput-object v2, v0, v1

    const/16 v1, 0x1d

    sget-object v2, Landroidx/compose/ui/autofill/AutofillType;->PhoneNumberNational:Landroidx/compose/ui/autofill/AutofillType;

    aput-object v2, v0, v1

    const/16 v1, 0x1e

    sget-object v2, Landroidx/compose/ui/autofill/AutofillType;->Gender:Landroidx/compose/ui/autofill/AutofillType;

    aput-object v2, v0, v1

    const/16 v1, 0x1f

    sget-object v2, Landroidx/compose/ui/autofill/AutofillType;->BirthDateFull:Landroidx/compose/ui/autofill/AutofillType;

    aput-object v2, v0, v1

    const/16 v1, 0x20

    sget-object v2, Landroidx/compose/ui/autofill/AutofillType;->BirthDateDay:Landroidx/compose/ui/autofill/AutofillType;

    aput-object v2, v0, v1

    const/16 v1, 0x21

    sget-object v2, Landroidx/compose/ui/autofill/AutofillType;->BirthDateMonth:Landroidx/compose/ui/autofill/AutofillType;

    aput-object v2, v0, v1

    const/16 v1, 0x22

    sget-object v2, Landroidx/compose/ui/autofill/AutofillType;->BirthDateYear:Landroidx/compose/ui/autofill/AutofillType;

    aput-object v2, v0, v1

    const/16 v1, 0x23

    sget-object v2, Landroidx/compose/ui/autofill/AutofillType;->SmsOtpCode:Landroidx/compose/ui/autofill/AutofillType;

    aput-object v2, v0, v1

    return-object v0
.end method

.method static constructor <clinit>()V
    .locals 3

    new-instance v0, Landroidx/compose/ui/autofill/AutofillType;

    const-string v1, "EmailAddress"

    const/4 v2, 0x0

    invoke-direct {v0, v1, v2}, Landroidx/compose/ui/autofill/AutofillType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/ui/autofill/AutofillType;->EmailAddress:Landroidx/compose/ui/autofill/AutofillType;

    new-instance v0, Landroidx/compose/ui/autofill/AutofillType;

    const-string v1, "Username"

    const/4 v2, 0x1

    invoke-direct {v0, v1, v2}, Landroidx/compose/ui/autofill/AutofillType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/ui/autofill/AutofillType;->Username:Landroidx/compose/ui/autofill/AutofillType;

    new-instance v0, Landroidx/compose/ui/autofill/AutofillType;

    const-string v1, "Password"

    const/4 v2, 0x2

    invoke-direct {v0, v1, v2}, Landroidx/compose/ui/autofill/AutofillType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/ui/autofill/AutofillType;->Password:Landroidx/compose/ui/autofill/AutofillType;

    new-instance v0, Landroidx/compose/ui/autofill/AutofillType;

    const-string v1, "NewUsername"

    const/4 v2, 0x3

    invoke-direct {v0, v1, v2}, Landroidx/compose/ui/autofill/AutofillType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/ui/autofill/AutofillType;->NewUsername:Landroidx/compose/ui/autofill/AutofillType;

    new-instance v0, Landroidx/compose/ui/autofill/AutofillType;

    const-string v1, "NewPassword"

    const/4 v2, 0x4

    invoke-direct {v0, v1, v2}, Landroidx/compose/ui/autofill/AutofillType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/ui/autofill/AutofillType;->NewPassword:Landroidx/compose/ui/autofill/AutofillType;

    new-instance v0, Landroidx/compose/ui/autofill/AutofillType;

    const-string v1, "PostalAddress"

    const/4 v2, 0x5

    invoke-direct {v0, v1, v2}, Landroidx/compose/ui/autofill/AutofillType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/ui/autofill/AutofillType;->PostalAddress:Landroidx/compose/ui/autofill/AutofillType;

    new-instance v0, Landroidx/compose/ui/autofill/AutofillType;

    const-string v1, "PostalCode"

    const/4 v2, 0x6

    invoke-direct {v0, v1, v2}, Landroidx/compose/ui/autofill/AutofillType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/ui/autofill/AutofillType;->PostalCode:Landroidx/compose/ui/autofill/AutofillType;

    new-instance v0, Landroidx/compose/ui/autofill/AutofillType;

    const-string v1, "CreditCardNumber"

    const/4 v2, 0x7

    invoke-direct {v0, v1, v2}, Landroidx/compose/ui/autofill/AutofillType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/ui/autofill/AutofillType;->CreditCardNumber:Landroidx/compose/ui/autofill/AutofillType;

    new-instance v0, Landroidx/compose/ui/autofill/AutofillType;

    const-string v1, "CreditCardSecurityCode"

    const/16 v2, 0x8

    invoke-direct {v0, v1, v2}, Landroidx/compose/ui/autofill/AutofillType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/ui/autofill/AutofillType;->CreditCardSecurityCode:Landroidx/compose/ui/autofill/AutofillType;

    new-instance v0, Landroidx/compose/ui/autofill/AutofillType;

    const-string v1, "CreditCardExpirationDate"

    const/16 v2, 0x9

    invoke-direct {v0, v1, v2}, Landroidx/compose/ui/autofill/AutofillType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/ui/autofill/AutofillType;->CreditCardExpirationDate:Landroidx/compose/ui/autofill/AutofillType;

    new-instance v0, Landroidx/compose/ui/autofill/AutofillType;

    const-string v1, "CreditCardExpirationMonth"

    const/16 v2, 0xa

    invoke-direct {v0, v1, v2}, Landroidx/compose/ui/autofill/AutofillType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/ui/autofill/AutofillType;->CreditCardExpirationMonth:Landroidx/compose/ui/autofill/AutofillType;

    new-instance v0, Landroidx/compose/ui/autofill/AutofillType;

    const-string v1, "CreditCardExpirationYear"

    const/16 v2, 0xb

    invoke-direct {v0, v1, v2}, Landroidx/compose/ui/autofill/AutofillType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/ui/autofill/AutofillType;->CreditCardExpirationYear:Landroidx/compose/ui/autofill/AutofillType;

    new-instance v0, Landroidx/compose/ui/autofill/AutofillType;

    const-string v1, "CreditCardExpirationDay"

    const/16 v2, 0xc

    invoke-direct {v0, v1, v2}, Landroidx/compose/ui/autofill/AutofillType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/ui/autofill/AutofillType;->CreditCardExpirationDay:Landroidx/compose/ui/autofill/AutofillType;

    new-instance v0, Landroidx/compose/ui/autofill/AutofillType;

    const-string v1, "AddressCountry"

    const/16 v2, 0xd

    invoke-direct {v0, v1, v2}, Landroidx/compose/ui/autofill/AutofillType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/ui/autofill/AutofillType;->AddressCountry:Landroidx/compose/ui/autofill/AutofillType;

    new-instance v0, Landroidx/compose/ui/autofill/AutofillType;

    const-string v1, "AddressRegion"

    const/16 v2, 0xe

    invoke-direct {v0, v1, v2}, Landroidx/compose/ui/autofill/AutofillType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/ui/autofill/AutofillType;->AddressRegion:Landroidx/compose/ui/autofill/AutofillType;

    new-instance v0, Landroidx/compose/ui/autofill/AutofillType;

    const-string v1, "AddressLocality"

    const/16 v2, 0xf

    invoke-direct {v0, v1, v2}, Landroidx/compose/ui/autofill/AutofillType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/ui/autofill/AutofillType;->AddressLocality:Landroidx/compose/ui/autofill/AutofillType;

    new-instance v0, Landroidx/compose/ui/autofill/AutofillType;

    const-string v1, "AddressStreet"

    const/16 v2, 0x10

    invoke-direct {v0, v1, v2}, Landroidx/compose/ui/autofill/AutofillType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/ui/autofill/AutofillType;->AddressStreet:Landroidx/compose/ui/autofill/AutofillType;

    new-instance v0, Landroidx/compose/ui/autofill/AutofillType;

    const-string v1, "AddressAuxiliaryDetails"

    const/16 v2, 0x11

    invoke-direct {v0, v1, v2}, Landroidx/compose/ui/autofill/AutofillType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/ui/autofill/AutofillType;->AddressAuxiliaryDetails:Landroidx/compose/ui/autofill/AutofillType;

    new-instance v0, Landroidx/compose/ui/autofill/AutofillType;

    const-string v1, "PostalCodeExtended"

    const/16 v2, 0x12

    invoke-direct {v0, v1, v2}, Landroidx/compose/ui/autofill/AutofillType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/ui/autofill/AutofillType;->PostalCodeExtended:Landroidx/compose/ui/autofill/AutofillType;

    new-instance v0, Landroidx/compose/ui/autofill/AutofillType;

    const-string v1, "PersonFullName"

    const/16 v2, 0x13

    invoke-direct {v0, v1, v2}, Landroidx/compose/ui/autofill/AutofillType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/ui/autofill/AutofillType;->PersonFullName:Landroidx/compose/ui/autofill/AutofillType;

    new-instance v0, Landroidx/compose/ui/autofill/AutofillType;

    const-string v1, "PersonFirstName"

    const/16 v2, 0x14

    invoke-direct {v0, v1, v2}, Landroidx/compose/ui/autofill/AutofillType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/ui/autofill/AutofillType;->PersonFirstName:Landroidx/compose/ui/autofill/AutofillType;

    new-instance v0, Landroidx/compose/ui/autofill/AutofillType;

    const-string v1, "PersonLastName"

    const/16 v2, 0x15

    invoke-direct {v0, v1, v2}, Landroidx/compose/ui/autofill/AutofillType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/ui/autofill/AutofillType;->PersonLastName:Landroidx/compose/ui/autofill/AutofillType;

    new-instance v0, Landroidx/compose/ui/autofill/AutofillType;

    const-string v1, "PersonMiddleName"

    const/16 v2, 0x16

    invoke-direct {v0, v1, v2}, Landroidx/compose/ui/autofill/AutofillType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/ui/autofill/AutofillType;->PersonMiddleName:Landroidx/compose/ui/autofill/AutofillType;

    new-instance v0, Landroidx/compose/ui/autofill/AutofillType;

    const-string v1, "PersonMiddleInitial"

    const/16 v2, 0x17

    invoke-direct {v0, v1, v2}, Landroidx/compose/ui/autofill/AutofillType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/ui/autofill/AutofillType;->PersonMiddleInitial:Landroidx/compose/ui/autofill/AutofillType;

    new-instance v0, Landroidx/compose/ui/autofill/AutofillType;

    const-string v1, "PersonNamePrefix"

    const/16 v2, 0x18

    invoke-direct {v0, v1, v2}, Landroidx/compose/ui/autofill/AutofillType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/ui/autofill/AutofillType;->PersonNamePrefix:Landroidx/compose/ui/autofill/AutofillType;

    new-instance v0, Landroidx/compose/ui/autofill/AutofillType;

    const-string v1, "PersonNameSuffix"

    const/16 v2, 0x19

    invoke-direct {v0, v1, v2}, Landroidx/compose/ui/autofill/AutofillType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/ui/autofill/AutofillType;->PersonNameSuffix:Landroidx/compose/ui/autofill/AutofillType;

    new-instance v0, Landroidx/compose/ui/autofill/AutofillType;

    const-string v1, "PhoneNumber"

    const/16 v2, 0x1a

    invoke-direct {v0, v1, v2}, Landroidx/compose/ui/autofill/AutofillType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/ui/autofill/AutofillType;->PhoneNumber:Landroidx/compose/ui/autofill/AutofillType;

    new-instance v0, Landroidx/compose/ui/autofill/AutofillType;

    const-string v1, "PhoneNumberDevice"

    const/16 v2, 0x1b

    invoke-direct {v0, v1, v2}, Landroidx/compose/ui/autofill/AutofillType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/ui/autofill/AutofillType;->PhoneNumberDevice:Landroidx/compose/ui/autofill/AutofillType;

    new-instance v0, Landroidx/compose/ui/autofill/AutofillType;

    const-string v1, "PhoneCountryCode"

    const/16 v2, 0x1c

    invoke-direct {v0, v1, v2}, Landroidx/compose/ui/autofill/AutofillType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/ui/autofill/AutofillType;->PhoneCountryCode:Landroidx/compose/ui/autofill/AutofillType;

    new-instance v0, Landroidx/compose/ui/autofill/AutofillType;

    const-string v1, "PhoneNumberNational"

    const/16 v2, 0x1d

    invoke-direct {v0, v1, v2}, Landroidx/compose/ui/autofill/AutofillType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/ui/autofill/AutofillType;->PhoneNumberNational:Landroidx/compose/ui/autofill/AutofillType;

    new-instance v0, Landroidx/compose/ui/autofill/AutofillType;

    const-string v1, "Gender"

    const/16 v2, 0x1e

    invoke-direct {v0, v1, v2}, Landroidx/compose/ui/autofill/AutofillType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/ui/autofill/AutofillType;->Gender:Landroidx/compose/ui/autofill/AutofillType;

    new-instance v0, Landroidx/compose/ui/autofill/AutofillType;

    const-string v1, "BirthDateFull"

    const/16 v2, 0x1f

    invoke-direct {v0, v1, v2}, Landroidx/compose/ui/autofill/AutofillType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/ui/autofill/AutofillType;->BirthDateFull:Landroidx/compose/ui/autofill/AutofillType;

    new-instance v0, Landroidx/compose/ui/autofill/AutofillType;

    const-string v1, "BirthDateDay"

    const/16 v2, 0x20

    invoke-direct {v0, v1, v2}, Landroidx/compose/ui/autofill/AutofillType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/ui/autofill/AutofillType;->BirthDateDay:Landroidx/compose/ui/autofill/AutofillType;

    new-instance v0, Landroidx/compose/ui/autofill/AutofillType;

    const-string v1, "BirthDateMonth"

    const/16 v2, 0x21

    invoke-direct {v0, v1, v2}, Landroidx/compose/ui/autofill/AutofillType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/ui/autofill/AutofillType;->BirthDateMonth:Landroidx/compose/ui/autofill/AutofillType;

    new-instance v0, Landroidx/compose/ui/autofill/AutofillType;

    const-string v1, "BirthDateYear"

    const/16 v2, 0x22

    invoke-direct {v0, v1, v2}, Landroidx/compose/ui/autofill/AutofillType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/ui/autofill/AutofillType;->BirthDateYear:Landroidx/compose/ui/autofill/AutofillType;

    new-instance v0, Landroidx/compose/ui/autofill/AutofillType;

    const-string v1, "SmsOtpCode"

    const/16 v2, 0x23

    invoke-direct {v0, v1, v2}, Landroidx/compose/ui/autofill/AutofillType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/ui/autofill/AutofillType;->SmsOtpCode:Landroidx/compose/ui/autofill/AutofillType;

    invoke-static {}, Landroidx/compose/ui/autofill/AutofillType;->$values()[Landroidx/compose/ui/autofill/AutofillType;

    move-result-object v0

    sput-object v0, Landroidx/compose/ui/autofill/AutofillType;->$VALUES:[Landroidx/compose/ui/autofill/AutofillType;

    return-void
.end method

.method private constructor <init>(Ljava/lang/String;I)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation

    invoke-direct {p0, p1, p2}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    return-void
.end method

.method public static valueOf(Ljava/lang/String;)Landroidx/compose/ui/autofill/AutofillType;
    .locals 1

    const-class v0, Landroidx/compose/ui/autofill/AutofillType;

    invoke-static {v0, p0}, Ljava/lang/Enum;->valueOf(Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Enum;

    move-result-object p0

    check-cast p0, Landroidx/compose/ui/autofill/AutofillType;

    return-object p0
.end method

.method public static values()[Landroidx/compose/ui/autofill/AutofillType;
    .locals 1

    sget-object v0, Landroidx/compose/ui/autofill/AutofillType;->$VALUES:[Landroidx/compose/ui/autofill/AutofillType;

    invoke-virtual {v0}, [Ljava/lang/Object;->clone()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [Landroidx/compose/ui/autofill/AutofillType;

    return-object v0
.end method
