.class public final Landroidx/compose/ui/b$a;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/compose/ui/b;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation


# static fields
.field public static final synthetic a:Landroidx/compose/ui/b$a;

.field public static final b:Landroidx/compose/ui/b;

.field public static final c:Landroidx/compose/ui/b;

.field public static final d:Landroidx/compose/ui/b;

.field public static final e:Landroidx/compose/ui/b;

.field public static final f:Landroidx/compose/ui/b;

.field public static final g:Landroidx/compose/ui/b;

.field public static final h:Landroidx/compose/ui/b;

.field public static final i:Landroidx/compose/ui/b;

.field public static final j:Landroidx/compose/ui/b;

.field public static final k:Landroidx/compose/ui/b$c;

.field public static final l:Landroidx/compose/ui/b$c;

.field public static final m:Landroidx/compose/ui/b$c;

.field public static final n:Landroidx/compose/ui/b$b;

.field public static final o:Landroidx/compose/ui/b$b;

.field public static final p:Landroidx/compose/ui/b$b;


# direct methods
.method static constructor <clinit>()V
    .locals 4

    new-instance v0, Landroidx/compose/ui/b$a;

    invoke-direct {v0}, Landroidx/compose/ui/b$a;-><init>()V

    sput-object v0, Landroidx/compose/ui/b$a;->a:Landroidx/compose/ui/b$a;

    new-instance v0, Landroidx/compose/ui/c;

    const/high16 v1, -0x40800000    # -1.0f

    invoke-direct {v0, v1, v1}, Landroidx/compose/ui/c;-><init>(FF)V

    sput-object v0, Landroidx/compose/ui/b$a;->b:Landroidx/compose/ui/b;

    new-instance v0, Landroidx/compose/ui/c;

    const/4 v2, 0x0

    invoke-direct {v0, v2, v1}, Landroidx/compose/ui/c;-><init>(FF)V

    sput-object v0, Landroidx/compose/ui/b$a;->c:Landroidx/compose/ui/b;

    new-instance v0, Landroidx/compose/ui/c;

    const/high16 v3, 0x3f800000    # 1.0f

    invoke-direct {v0, v3, v1}, Landroidx/compose/ui/c;-><init>(FF)V

    sput-object v0, Landroidx/compose/ui/b$a;->d:Landroidx/compose/ui/b;

    new-instance v0, Landroidx/compose/ui/c;

    invoke-direct {v0, v1, v2}, Landroidx/compose/ui/c;-><init>(FF)V

    sput-object v0, Landroidx/compose/ui/b$a;->e:Landroidx/compose/ui/b;

    new-instance v0, Landroidx/compose/ui/c;

    invoke-direct {v0, v2, v2}, Landroidx/compose/ui/c;-><init>(FF)V

    sput-object v0, Landroidx/compose/ui/b$a;->f:Landroidx/compose/ui/b;

    new-instance v0, Landroidx/compose/ui/c;

    invoke-direct {v0, v3, v2}, Landroidx/compose/ui/c;-><init>(FF)V

    sput-object v0, Landroidx/compose/ui/b$a;->g:Landroidx/compose/ui/b;

    new-instance v0, Landroidx/compose/ui/c;

    invoke-direct {v0, v1, v3}, Landroidx/compose/ui/c;-><init>(FF)V

    sput-object v0, Landroidx/compose/ui/b$a;->h:Landroidx/compose/ui/b;

    new-instance v0, Landroidx/compose/ui/c;

    invoke-direct {v0, v2, v3}, Landroidx/compose/ui/c;-><init>(FF)V

    sput-object v0, Landroidx/compose/ui/b$a;->i:Landroidx/compose/ui/b;

    new-instance v0, Landroidx/compose/ui/c;

    invoke-direct {v0, v3, v3}, Landroidx/compose/ui/c;-><init>(FF)V

    sput-object v0, Landroidx/compose/ui/b$a;->j:Landroidx/compose/ui/b;

    new-instance v0, Landroidx/compose/ui/c$b;

    invoke-direct {v0, v1}, Landroidx/compose/ui/c$b;-><init>(F)V

    sput-object v0, Landroidx/compose/ui/b$a;->k:Landroidx/compose/ui/b$c;

    new-instance v0, Landroidx/compose/ui/c$b;

    invoke-direct {v0, v2}, Landroidx/compose/ui/c$b;-><init>(F)V

    sput-object v0, Landroidx/compose/ui/b$a;->l:Landroidx/compose/ui/b$c;

    new-instance v0, Landroidx/compose/ui/c$b;

    invoke-direct {v0, v3}, Landroidx/compose/ui/c$b;-><init>(F)V

    sput-object v0, Landroidx/compose/ui/b$a;->m:Landroidx/compose/ui/b$c;

    new-instance v0, Landroidx/compose/ui/c$a;

    invoke-direct {v0, v1}, Landroidx/compose/ui/c$a;-><init>(F)V

    sput-object v0, Landroidx/compose/ui/b$a;->n:Landroidx/compose/ui/b$b;

    new-instance v0, Landroidx/compose/ui/c$a;

    invoke-direct {v0, v2}, Landroidx/compose/ui/c$a;-><init>(F)V

    sput-object v0, Landroidx/compose/ui/b$a;->o:Landroidx/compose/ui/b$b;

    new-instance v0, Landroidx/compose/ui/c$a;

    invoke-direct {v0, v3}, Landroidx/compose/ui/c$a;-><init>(F)V

    sput-object v0, Landroidx/compose/ui/b$a;->p:Landroidx/compose/ui/b$b;

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final a()Landroidx/compose/ui/b;
    .locals 1

    sget-object v0, Landroidx/compose/ui/b$a;->i:Landroidx/compose/ui/b;

    return-object v0
.end method

.method public final b()Landroidx/compose/ui/b;
    .locals 1

    sget-object v0, Landroidx/compose/ui/b$a;->j:Landroidx/compose/ui/b;

    return-object v0
.end method

.method public final c()Landroidx/compose/ui/b;
    .locals 1

    sget-object v0, Landroidx/compose/ui/b$a;->h:Landroidx/compose/ui/b;

    return-object v0
.end method

.method public final d()Landroidx/compose/ui/b;
    .locals 1

    sget-object v0, Landroidx/compose/ui/b$a;->f:Landroidx/compose/ui/b;

    return-object v0
.end method

.method public final e()Landroidx/compose/ui/b;
    .locals 1

    sget-object v0, Landroidx/compose/ui/b$a;->g:Landroidx/compose/ui/b;

    return-object v0
.end method

.method public final f()Landroidx/compose/ui/b$b;
    .locals 1

    sget-object v0, Landroidx/compose/ui/b$a;->o:Landroidx/compose/ui/b$b;

    return-object v0
.end method

.method public final g()Landroidx/compose/ui/b;
    .locals 1

    sget-object v0, Landroidx/compose/ui/b$a;->e:Landroidx/compose/ui/b;

    return-object v0
.end method

.method public final h()Landroidx/compose/ui/b$c;
    .locals 1

    sget-object v0, Landroidx/compose/ui/b$a;->l:Landroidx/compose/ui/b$c;

    return-object v0
.end method

.method public final i()Landroidx/compose/ui/b$b;
    .locals 1

    sget-object v0, Landroidx/compose/ui/b$a;->n:Landroidx/compose/ui/b$b;

    return-object v0
.end method

.method public final j()Landroidx/compose/ui/b$c;
    .locals 1

    sget-object v0, Landroidx/compose/ui/b$a;->k:Landroidx/compose/ui/b$c;

    return-object v0
.end method

.method public final k()Landroidx/compose/ui/b;
    .locals 1

    sget-object v0, Landroidx/compose/ui/b$a;->c:Landroidx/compose/ui/b;

    return-object v0
.end method

.method public final l()Landroidx/compose/ui/b;
    .locals 1

    sget-object v0, Landroidx/compose/ui/b$a;->d:Landroidx/compose/ui/b;

    return-object v0
.end method

.method public final m()Landroidx/compose/ui/b;
    .locals 1

    sget-object v0, Landroidx/compose/ui/b$a;->b:Landroidx/compose/ui/b;

    return-object v0
.end method
