.class public interface abstract Landroidx/compose/ui/b;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Landroidx/compose/ui/b$a;,
        Landroidx/compose/ui/b$b;,
        Landroidx/compose/ui/b$c;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation


# static fields
.field public static final a:Landroidx/compose/ui/b$a;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    sget-object v0, Landroidx/compose/ui/b$a;->a:Landroidx/compose/ui/b$a;

    sput-object v0, Landroidx/compose/ui/b;->a:Landroidx/compose/ui/b$a;

    return-void
.end method


# virtual methods
.method public abstract a(JJLandroidx/compose/ui/unit/LayoutDirection;)J
.end method
