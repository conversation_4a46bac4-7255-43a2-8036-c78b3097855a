.class public final synthetic Landroidx/compose/ui/contentcapture/g;
.super Ljava/lang/Object;


# direct methods
.method public static bridge synthetic a(Landroid/view/translation/ViewTranslationRequest$Builder;)Landroid/view/translation/ViewTranslationRequest;
    .locals 0

    invoke-virtual {p0}, Landroid/view/translation/ViewTranslationRequest$Builder;->build()Landroid/view/translation/ViewTranslationRequest;

    move-result-object p0

    return-object p0
.end method
