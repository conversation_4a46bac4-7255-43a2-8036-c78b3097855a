.class public interface abstract Landroidx/compose/ui/contentcapture/m;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Landroidx/compose/ui/contentcapture/m$a;
    }
.end annotation

.annotation runtime L<PERSON>lin/Metadata;
.end annotation


# static fields
.field public static final d0:Landroidx/compose/ui/contentcapture/m$a;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    sget-object v0, Landroidx/compose/ui/contentcapture/m$a;->a:Landroidx/compose/ui/contentcapture/m$a;

    sput-object v0, Landroidx/compose/ui/contentcapture/m;->d0:Landroidx/compose/ui/contentcapture/m$a;

    return-void
.end method
