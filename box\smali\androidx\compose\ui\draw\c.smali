.class public final Landroidx/compose/ui/draw/c;
.super Ljava/lang/Object;

# interfaces
.implements Lv0/e;


# annotations
.annotation runtime Lkotlin/Metadata;
.end annotation

.annotation build Lkotlin/jvm/internal/SourceDebugExtension;
.end annotation


# instance fields
.field public a:Landroidx/compose/ui/draw/a;

.field public b:Landroidx/compose/ui/draw/i;

.field public c:Le0/c;

.field public d:Lkotlin/jvm/functions/Function0;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlin/jvm/functions/Function0<",
            "+",
            "Landroidx/compose/ui/graphics/c4;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>()V
    .locals 1

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    sget-object v0, Landroidx/compose/ui/draw/j;->a:Landroidx/compose/ui/draw/j;

    iput-object v0, p0, Landroidx/compose/ui/draw/c;->a:Landroidx/compose/ui/draw/a;

    return-void
.end method


# virtual methods
.method public final D(Lkotlin/jvm/functions/Function0;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function0<",
            "+",
            "Landroidx/compose/ui/graphics/c4;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Landroidx/compose/ui/draw/c;->d:Lkotlin/jvm/functions/Function0;

    return-void
.end method

.method public synthetic H0(F)F
    .locals 0

    invoke-static {p0, p1}, Lv0/d;->b(Lv0/e;F)F

    move-result p1

    return p1
.end method

.method public L0()F
    .locals 1

    iget-object v0, p0, Landroidx/compose/ui/draw/c;->a:Landroidx/compose/ui/draw/a;

    invoke-interface {v0}, Landroidx/compose/ui/draw/a;->getDensity()Lv0/e;

    move-result-object v0

    invoke-interface {v0}, Lv0/n;->L0()F

    move-result v0

    return v0
.end method

.method public synthetic M(F)J
    .locals 2

    invoke-static {p0, p1}, Lv0/m;->b(Lv0/n;F)J

    move-result-wide v0

    return-wide v0
.end method

.method public synthetic N(J)F
    .locals 0

    invoke-static {p0, p1, p2}, Lv0/m;->a(Lv0/n;J)F

    move-result p1

    return p1
.end method

.method public synthetic O0(F)F
    .locals 0

    invoke-static {p0, p1}, Lv0/d;->d(Lv0/e;F)F

    move-result p1

    return p1
.end method

.method public synthetic W(F)J
    .locals 2

    invoke-static {p0, p1}, Lv0/d;->f(Lv0/e;F)J

    move-result-wide v0

    return-wide v0
.end method

.method public synthetic Y0(J)J
    .locals 0

    invoke-static {p0, p1, p2}, Lv0/d;->e(Lv0/e;J)J

    move-result-wide p1

    return-wide p1
.end method

.method public final a()J
    .locals 2

    iget-object v0, p0, Landroidx/compose/ui/draw/c;->a:Landroidx/compose/ui/draw/a;

    invoke-interface {v0}, Landroidx/compose/ui/draw/a;->a()J

    move-result-wide v0

    return-wide v0
.end method

.method public getDensity()F
    .locals 1

    iget-object v0, p0, Landroidx/compose/ui/draw/c;->a:Landroidx/compose/ui/draw/a;

    invoke-interface {v0}, Landroidx/compose/ui/draw/a;->getDensity()Lv0/e;

    move-result-object v0

    invoke-interface {v0}, Lv0/e;->getDensity()F

    move-result v0

    return v0
.end method

.method public final getLayoutDirection()Landroidx/compose/ui/unit/LayoutDirection;
    .locals 1

    iget-object v0, p0, Landroidx/compose/ui/draw/c;->a:Landroidx/compose/ui/draw/a;

    invoke-interface {v0}, Landroidx/compose/ui/draw/a;->getLayoutDirection()Landroidx/compose/ui/unit/LayoutDirection;

    move-result-object v0

    return-object v0
.end method

.method public synthetic j0(F)I
    .locals 0

    invoke-static {p0, p1}, Lv0/d;->a(Lv0/e;F)I

    move-result p1

    return p1
.end method

.method public final l()Landroidx/compose/ui/draw/i;
    .locals 1

    iget-object v0, p0, Landroidx/compose/ui/draw/c;->b:Landroidx/compose/ui/draw/i;

    return-object v0
.end method

.method public final m(Lkotlin/jvm/functions/Function1;)Landroidx/compose/ui/draw/i;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Le0/c;",
            "Lkotlin/Unit;",
            ">;)",
            "Landroidx/compose/ui/draw/i;"
        }
    .end annotation

    new-instance v0, Landroidx/compose/ui/draw/i;

    invoke-direct {v0, p1}, Landroidx/compose/ui/draw/i;-><init>(Lkotlin/jvm/functions/Function1;)V

    iput-object v0, p0, Landroidx/compose/ui/draw/c;->b:Landroidx/compose/ui/draw/i;

    return-object v0
.end method

.method public synthetic p0(J)F
    .locals 0

    invoke-static {p0, p1, p2}, Lv0/d;->c(Lv0/e;J)F

    move-result p1

    return p1
.end method

.method public final v(Landroidx/compose/ui/draw/a;)V
    .locals 0

    iput-object p1, p0, Landroidx/compose/ui/draw/c;->a:Landroidx/compose/ui/draw/a;

    return-void
.end method

.method public final y(Le0/c;)V
    .locals 0

    iput-object p1, p0, Landroidx/compose/ui/draw/c;->c:Le0/c;

    return-void
.end method

.method public final z(Landroidx/compose/ui/draw/i;)V
    .locals 0

    iput-object p1, p0, Landroidx/compose/ui/draw/c;->b:Landroidx/compose/ui/draw/i;

    return-void
.end method
