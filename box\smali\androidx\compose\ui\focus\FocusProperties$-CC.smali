.class public final synthetic Landroidx/compose/ui/focus/FocusProperties$-CC;
.super Ljava/lang/Object;


# direct methods
.method public static a(Landroidx/compose/ui/focus/m;)Landroidx/compose/ui/focus/FocusRequester;
    .locals 0

    sget-object p0, Landroidx/compose/ui/focus/FocusRequester;->b:Landroidx/compose/ui/focus/FocusRequester$a;

    invoke-virtual {p0}, Landroidx/compose/ui/focus/FocusRequester$a;->b()Landroidx/compose/ui/focus/FocusRequester;

    move-result-object p0

    return-object p0
.end method

.method public static b(Landroidx/compose/ui/focus/m;)Landroidx/compose/ui/focus/FocusRequester;
    .locals 0

    sget-object p0, Landroidx/compose/ui/focus/FocusRequester;->b:Landroidx/compose/ui/focus/FocusRequester$a;

    invoke-virtual {p0}, Landroidx/compose/ui/focus/FocusRequester$a;->b()Landroidx/compose/ui/focus/FocusRequester;

    move-result-object p0

    return-object p0
.end method

.method public static c(Landroidx/compose/ui/focus/m;)Lkotlin/jvm/functions/Function1;
    .locals 0

    sget-object p0, Landroidx/compose/ui/focus/FocusProperties$enter$1;->INSTANCE:Landroidx/compose/ui/focus/FocusProperties$enter$1;

    return-object p0
.end method

.method public static d(Landroidx/compose/ui/focus/m;)Lkotlin/jvm/functions/Function1;
    .locals 0

    sget-object p0, Landroidx/compose/ui/focus/FocusProperties$exit$1;->INSTANCE:Landroidx/compose/ui/focus/FocusProperties$exit$1;

    return-object p0
.end method

.method public static e(Landroidx/compose/ui/focus/m;)Landroidx/compose/ui/focus/FocusRequester;
    .locals 0

    sget-object p0, Landroidx/compose/ui/focus/FocusRequester;->b:Landroidx/compose/ui/focus/FocusRequester$a;

    invoke-virtual {p0}, Landroidx/compose/ui/focus/FocusRequester$a;->b()Landroidx/compose/ui/focus/FocusRequester;

    move-result-object p0

    return-object p0
.end method

.method public static f(Landroidx/compose/ui/focus/m;)Landroidx/compose/ui/focus/FocusRequester;
    .locals 0

    sget-object p0, Landroidx/compose/ui/focus/FocusRequester;->b:Landroidx/compose/ui/focus/FocusRequester$a;

    invoke-virtual {p0}, Landroidx/compose/ui/focus/FocusRequester$a;->b()Landroidx/compose/ui/focus/FocusRequester;

    move-result-object p0

    return-object p0
.end method

.method public static g(Landroidx/compose/ui/focus/m;)Landroidx/compose/ui/focus/FocusRequester;
    .locals 0

    sget-object p0, Landroidx/compose/ui/focus/FocusRequester;->b:Landroidx/compose/ui/focus/FocusRequester$a;

    invoke-virtual {p0}, Landroidx/compose/ui/focus/FocusRequester$a;->b()Landroidx/compose/ui/focus/FocusRequester;

    move-result-object p0

    return-object p0
.end method

.method public static h(Landroidx/compose/ui/focus/m;)Landroidx/compose/ui/focus/FocusRequester;
    .locals 0

    sget-object p0, Landroidx/compose/ui/focus/FocusRequester;->b:Landroidx/compose/ui/focus/FocusRequester$a;

    invoke-virtual {p0}, Landroidx/compose/ui/focus/FocusRequester$a;->b()Landroidx/compose/ui/focus/FocusRequester;

    move-result-object p0

    return-object p0
.end method

.method public static i(Landroidx/compose/ui/focus/m;)Landroidx/compose/ui/focus/FocusRequester;
    .locals 0

    sget-object p0, Landroidx/compose/ui/focus/FocusRequester;->b:Landroidx/compose/ui/focus/FocusRequester$a;

    invoke-virtual {p0}, Landroidx/compose/ui/focus/FocusRequester$a;->b()Landroidx/compose/ui/focus/FocusRequester;

    move-result-object p0

    return-object p0
.end method

.method public static j(Landroidx/compose/ui/focus/m;)Landroidx/compose/ui/focus/FocusRequester;
    .locals 0

    sget-object p0, Landroidx/compose/ui/focus/FocusRequester;->b:Landroidx/compose/ui/focus/FocusRequester$a;

    invoke-virtual {p0}, Landroidx/compose/ui/focus/FocusRequester$a;->b()Landroidx/compose/ui/focus/FocusRequester;

    move-result-object p0

    return-object p0
.end method
