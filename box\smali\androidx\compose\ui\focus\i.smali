.class public final Landroidx/compose/ui/focus/i;
.super Ljava/lang/Object;


# annotations
.annotation runtime L<PERSON><PERSON>/Deprecated;
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation


# instance fields
.field public final a:Landroidx/compose/ui/focus/m;


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Landroidx/compose/ui/focus/m;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Landroidx/compose/ui/focus/i;->a:Landroidx/compose/ui/focus/m;

    return-void
.end method
