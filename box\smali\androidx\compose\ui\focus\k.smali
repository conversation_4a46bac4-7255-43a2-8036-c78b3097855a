.class public interface abstract Landroidx/compose/ui/focus/k;
.super Ljava/lang/Object;

# interfaces
.implements Landroidx/compose/ui/focus/h;


# annotations
.annotation runtime L<PERSON>lin/Metadata;
.end annotation


# virtual methods
.method public abstract a(Landroidx/compose/ui/focus/d;)V
.end method

.method public abstract b(Landroidx/compose/ui/focus/b;Ld0/i;)Z
.end method

.method public abstract c()Landroidx/compose/ui/focus/y;
.end method

.method public abstract d(Landroid/view/KeyEvent;)Z
.end method

.method public abstract e(Ll0/c;)Z
.end method

.method public abstract f(ILd0/i;Lkotlin/jvm/functions/Function1;)Ljava/lang/Boolean;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I",
            "Ld0/i;",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Landroidx/compose/ui/focus/FocusTargetNode;",
            "Ljava/lang/Boolean;",
            ">;)",
            "Ljava/lang/Boolean;"
        }
    .end annotation
.end method

.method public abstract g(Landroidx/compose/ui/focus/FocusTargetNode;)V
.end method

.method public abstract h()Landroidx/compose/ui/f;
.end method

.method public abstract i(Landroid/view/KeyEvent;Lkotlin/jvm/functions/Function0;)Z
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/view/KeyEvent;",
            "Lkotlin/jvm/functions/Function0<",
            "Ljava/lang/Boolean;",
            ">;)Z"
        }
    .end annotation
.end method

.method public abstract j(ZZZI)Z
.end method

.method public abstract k()Landroidx/compose/ui/focus/u;
.end method

.method public abstract l(Landroidx/compose/ui/focus/o;)V
.end method

.method public abstract m()Ld0/i;
.end method

.method public abstract n()V
.end method
