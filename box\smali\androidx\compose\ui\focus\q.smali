.class public final Landroidx/compose/ui/focus/q;
.super Landroidx/compose/ui/f$c;

# interfaces
.implements Landroidx/compose/ui/focus/o;


# annotations
.annotation runtime Lkotlin/Metadata;
.end annotation


# instance fields
.field public n:Landroidx/compose/ui/focus/r;


# direct methods
.method public constructor <init>(Landroidx/compose/ui/focus/r;)V
    .locals 0

    invoke-direct {p0}, Landroidx/compose/ui/f$c;-><init>()V

    iput-object p1, p0, Landroidx/compose/ui/focus/q;->n:Landroidx/compose/ui/focus/r;

    return-void
.end method


# virtual methods
.method public A0(Landroidx/compose/ui/focus/m;)V
    .locals 1

    iget-object v0, p0, Landroidx/compose/ui/focus/q;->n:Landroidx/compose/ui/focus/r;

    invoke-interface {v0, p1}, Landroidx/compose/ui/focus/r;->b(Landroidx/compose/ui/focus/m;)V

    return-void
.end method

.method public final J1(Landroidx/compose/ui/focus/r;)V
    .locals 0

    iput-object p1, p0, Landroidx/compose/ui/focus/q;->n:Landroidx/compose/ui/focus/r;

    return-void
.end method
