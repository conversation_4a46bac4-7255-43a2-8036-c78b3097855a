.class public final synthetic Landroidx/compose/ui/g;
.super Ljava/lang/Object;


# direct methods
.method public static a(Landroidx/compose/ui/f$b;L<PERSON>lin/jvm/functions/Function1;)Z
    .locals 0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)<PERSON>java/lang/Object;

    move-result-object p0

    check-cast p0, <PERSON><PERSON><PERSON>/lang/Boolean;

    invoke-virtual {p0}, <PERSON><PERSON>va/lang/Boolean;->booleanValue()Z

    move-result p0

    return p0
.end method

.method public static b(Landroidx/compose/ui/f$b;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)Ljava/lang/Object;
    .locals 0

    invoke-interface {p2, p1, p0}, Lkotlin/jvm/functions/Function2;->invoke(Ljava/lang/Object;Ljava/lang/Object;)<PERSON>java/lang/Object;

    move-result-object p0

    return-object p0
.end method
