.class public final Landroidx/compose/ui/graphics/e1;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Landroidx/compose/ui/graphics/e1$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation

.annotation runtime Lkotlin/jvm/JvmInline;
.end annotation


# static fields
.field public static final A:I

.field public static final B:I

.field public static final C:I

.field public static final D:I

.field public static final a:Landroidx/compose/ui/graphics/e1$a;

.field public static final b:I

.field public static final c:I

.field public static final d:I

.field public static final e:I

.field public static final f:I

.field public static final g:I

.field public static final h:I

.field public static final i:I

.field public static final j:I

.field public static final k:I

.field public static final l:I

.field public static final m:I

.field public static final n:I

.field public static final o:I

.field public static final p:I

.field public static final q:I

.field public static final r:I

.field public static final s:I

.field public static final t:I

.field public static final u:I

.field public static final v:I

.field public static final w:I

.field public static final x:I

.field public static final y:I

.field public static final z:I


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Landroidx/compose/ui/graphics/e1$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Landroidx/compose/ui/graphics/e1$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Landroidx/compose/ui/graphics/e1;->a:Landroidx/compose/ui/graphics/e1$a;

    const/4 v0, 0x0

    invoke-static {v0}, Landroidx/compose/ui/graphics/e1;->D(I)I

    move-result v0

    sput v0, Landroidx/compose/ui/graphics/e1;->b:I

    const/4 v0, 0x1

    invoke-static {v0}, Landroidx/compose/ui/graphics/e1;->D(I)I

    move-result v0

    sput v0, Landroidx/compose/ui/graphics/e1;->c:I

    const/4 v0, 0x2

    invoke-static {v0}, Landroidx/compose/ui/graphics/e1;->D(I)I

    move-result v0

    sput v0, Landroidx/compose/ui/graphics/e1;->d:I

    const/4 v0, 0x3

    invoke-static {v0}, Landroidx/compose/ui/graphics/e1;->D(I)I

    move-result v0

    sput v0, Landroidx/compose/ui/graphics/e1;->e:I

    const/4 v0, 0x4

    invoke-static {v0}, Landroidx/compose/ui/graphics/e1;->D(I)I

    move-result v0

    sput v0, Landroidx/compose/ui/graphics/e1;->f:I

    const/4 v0, 0x5

    invoke-static {v0}, Landroidx/compose/ui/graphics/e1;->D(I)I

    move-result v0

    sput v0, Landroidx/compose/ui/graphics/e1;->g:I

    const/4 v0, 0x6

    invoke-static {v0}, Landroidx/compose/ui/graphics/e1;->D(I)I

    move-result v0

    sput v0, Landroidx/compose/ui/graphics/e1;->h:I

    const/4 v0, 0x7

    invoke-static {v0}, Landroidx/compose/ui/graphics/e1;->D(I)I

    move-result v0

    sput v0, Landroidx/compose/ui/graphics/e1;->i:I

    const/16 v0, 0x8

    invoke-static {v0}, Landroidx/compose/ui/graphics/e1;->D(I)I

    move-result v0

    sput v0, Landroidx/compose/ui/graphics/e1;->j:I

    const/16 v0, 0x9

    invoke-static {v0}, Landroidx/compose/ui/graphics/e1;->D(I)I

    move-result v0

    sput v0, Landroidx/compose/ui/graphics/e1;->k:I

    const/16 v0, 0xa

    invoke-static {v0}, Landroidx/compose/ui/graphics/e1;->D(I)I

    move-result v0

    sput v0, Landroidx/compose/ui/graphics/e1;->l:I

    const/16 v0, 0xb

    invoke-static {v0}, Landroidx/compose/ui/graphics/e1;->D(I)I

    move-result v0

    sput v0, Landroidx/compose/ui/graphics/e1;->m:I

    const/16 v0, 0xc

    invoke-static {v0}, Landroidx/compose/ui/graphics/e1;->D(I)I

    move-result v0

    sput v0, Landroidx/compose/ui/graphics/e1;->n:I

    const/16 v0, 0xd

    invoke-static {v0}, Landroidx/compose/ui/graphics/e1;->D(I)I

    move-result v0

    sput v0, Landroidx/compose/ui/graphics/e1;->o:I

    const/16 v0, 0xe

    invoke-static {v0}, Landroidx/compose/ui/graphics/e1;->D(I)I

    move-result v0

    sput v0, Landroidx/compose/ui/graphics/e1;->p:I

    const/16 v0, 0xf

    invoke-static {v0}, Landroidx/compose/ui/graphics/e1;->D(I)I

    move-result v0

    sput v0, Landroidx/compose/ui/graphics/e1;->q:I

    const/16 v0, 0x10

    invoke-static {v0}, Landroidx/compose/ui/graphics/e1;->D(I)I

    move-result v0

    sput v0, Landroidx/compose/ui/graphics/e1;->r:I

    const/16 v0, 0x11

    invoke-static {v0}, Landroidx/compose/ui/graphics/e1;->D(I)I

    move-result v0

    sput v0, Landroidx/compose/ui/graphics/e1;->s:I

    const/16 v0, 0x12

    invoke-static {v0}, Landroidx/compose/ui/graphics/e1;->D(I)I

    move-result v0

    sput v0, Landroidx/compose/ui/graphics/e1;->t:I

    const/16 v0, 0x13

    invoke-static {v0}, Landroidx/compose/ui/graphics/e1;->D(I)I

    move-result v0

    sput v0, Landroidx/compose/ui/graphics/e1;->u:I

    const/16 v0, 0x14

    invoke-static {v0}, Landroidx/compose/ui/graphics/e1;->D(I)I

    move-result v0

    sput v0, Landroidx/compose/ui/graphics/e1;->v:I

    const/16 v0, 0x15

    invoke-static {v0}, Landroidx/compose/ui/graphics/e1;->D(I)I

    move-result v0

    sput v0, Landroidx/compose/ui/graphics/e1;->w:I

    const/16 v0, 0x16

    invoke-static {v0}, Landroidx/compose/ui/graphics/e1;->D(I)I

    move-result v0

    sput v0, Landroidx/compose/ui/graphics/e1;->x:I

    const/16 v0, 0x17

    invoke-static {v0}, Landroidx/compose/ui/graphics/e1;->D(I)I

    move-result v0

    sput v0, Landroidx/compose/ui/graphics/e1;->y:I

    const/16 v0, 0x18

    invoke-static {v0}, Landroidx/compose/ui/graphics/e1;->D(I)I

    move-result v0

    sput v0, Landroidx/compose/ui/graphics/e1;->z:I

    const/16 v0, 0x19

    invoke-static {v0}, Landroidx/compose/ui/graphics/e1;->D(I)I

    move-result v0

    sput v0, Landroidx/compose/ui/graphics/e1;->A:I

    const/16 v0, 0x1a

    invoke-static {v0}, Landroidx/compose/ui/graphics/e1;->D(I)I

    move-result v0

    sput v0, Landroidx/compose/ui/graphics/e1;->B:I

    const/16 v0, 0x1b

    invoke-static {v0}, Landroidx/compose/ui/graphics/e1;->D(I)I

    move-result v0

    sput v0, Landroidx/compose/ui/graphics/e1;->C:I

    const/16 v0, 0x1c

    invoke-static {v0}, Landroidx/compose/ui/graphics/e1;->D(I)I

    move-result v0

    sput v0, Landroidx/compose/ui/graphics/e1;->D:I

    return-void
.end method

.method public static final synthetic A()I
    .locals 1

    sget v0, Landroidx/compose/ui/graphics/e1;->i:I

    return v0
.end method

.method public static final synthetic B()I
    .locals 1

    sget v0, Landroidx/compose/ui/graphics/e1;->e:I

    return v0
.end method

.method public static final synthetic C()I
    .locals 1

    sget v0, Landroidx/compose/ui/graphics/e1;->m:I

    return v0
.end method

.method public static D(I)I
    .locals 0

    return p0
.end method

.method public static final E(II)Z
    .locals 0

    if-ne p0, p1, :cond_0

    const/4 p0, 0x1

    goto :goto_0

    :cond_0
    const/4 p0, 0x0

    :goto_0
    return p0
.end method

.method public static F(I)I
    .locals 0

    return p0
.end method

.method public static G(I)Ljava/lang/String;
    .locals 1

    sget v0, Landroidx/compose/ui/graphics/e1;->b:I

    invoke-static {p0, v0}, Landroidx/compose/ui/graphics/e1;->E(II)Z

    move-result v0

    if-eqz v0, :cond_0

    const-string p0, "Clear"

    goto/16 :goto_0

    :cond_0
    sget v0, Landroidx/compose/ui/graphics/e1;->c:I

    invoke-static {p0, v0}, Landroidx/compose/ui/graphics/e1;->E(II)Z

    move-result v0

    if-eqz v0, :cond_1

    const-string p0, "Src"

    goto/16 :goto_0

    :cond_1
    sget v0, Landroidx/compose/ui/graphics/e1;->d:I

    invoke-static {p0, v0}, Landroidx/compose/ui/graphics/e1;->E(II)Z

    move-result v0

    if-eqz v0, :cond_2

    const-string p0, "Dst"

    goto/16 :goto_0

    :cond_2
    sget v0, Landroidx/compose/ui/graphics/e1;->e:I

    invoke-static {p0, v0}, Landroidx/compose/ui/graphics/e1;->E(II)Z

    move-result v0

    if-eqz v0, :cond_3

    const-string p0, "SrcOver"

    goto/16 :goto_0

    :cond_3
    sget v0, Landroidx/compose/ui/graphics/e1;->f:I

    invoke-static {p0, v0}, Landroidx/compose/ui/graphics/e1;->E(II)Z

    move-result v0

    if-eqz v0, :cond_4

    const-string p0, "DstOver"

    goto/16 :goto_0

    :cond_4
    sget v0, Landroidx/compose/ui/graphics/e1;->g:I

    invoke-static {p0, v0}, Landroidx/compose/ui/graphics/e1;->E(II)Z

    move-result v0

    if-eqz v0, :cond_5

    const-string p0, "SrcIn"

    goto/16 :goto_0

    :cond_5
    sget v0, Landroidx/compose/ui/graphics/e1;->h:I

    invoke-static {p0, v0}, Landroidx/compose/ui/graphics/e1;->E(II)Z

    move-result v0

    if-eqz v0, :cond_6

    const-string p0, "DstIn"

    goto/16 :goto_0

    :cond_6
    sget v0, Landroidx/compose/ui/graphics/e1;->i:I

    invoke-static {p0, v0}, Landroidx/compose/ui/graphics/e1;->E(II)Z

    move-result v0

    if-eqz v0, :cond_7

    const-string p0, "SrcOut"

    goto/16 :goto_0

    :cond_7
    sget v0, Landroidx/compose/ui/graphics/e1;->j:I

    invoke-static {p0, v0}, Landroidx/compose/ui/graphics/e1;->E(II)Z

    move-result v0

    if-eqz v0, :cond_8

    const-string p0, "DstOut"

    goto/16 :goto_0

    :cond_8
    sget v0, Landroidx/compose/ui/graphics/e1;->k:I

    invoke-static {p0, v0}, Landroidx/compose/ui/graphics/e1;->E(II)Z

    move-result v0

    if-eqz v0, :cond_9

    const-string p0, "SrcAtop"

    goto/16 :goto_0

    :cond_9
    sget v0, Landroidx/compose/ui/graphics/e1;->l:I

    invoke-static {p0, v0}, Landroidx/compose/ui/graphics/e1;->E(II)Z

    move-result v0

    if-eqz v0, :cond_a

    const-string p0, "DstAtop"

    goto/16 :goto_0

    :cond_a
    sget v0, Landroidx/compose/ui/graphics/e1;->m:I

    invoke-static {p0, v0}, Landroidx/compose/ui/graphics/e1;->E(II)Z

    move-result v0

    if-eqz v0, :cond_b

    const-string p0, "Xor"

    goto/16 :goto_0

    :cond_b
    sget v0, Landroidx/compose/ui/graphics/e1;->n:I

    invoke-static {p0, v0}, Landroidx/compose/ui/graphics/e1;->E(II)Z

    move-result v0

    if-eqz v0, :cond_c

    const-string p0, "Plus"

    goto/16 :goto_0

    :cond_c
    sget v0, Landroidx/compose/ui/graphics/e1;->o:I

    invoke-static {p0, v0}, Landroidx/compose/ui/graphics/e1;->E(II)Z

    move-result v0

    if-eqz v0, :cond_d

    const-string p0, "Modulate"

    goto/16 :goto_0

    :cond_d
    sget v0, Landroidx/compose/ui/graphics/e1;->p:I

    invoke-static {p0, v0}, Landroidx/compose/ui/graphics/e1;->E(II)Z

    move-result v0

    if-eqz v0, :cond_e

    const-string p0, "Screen"

    goto/16 :goto_0

    :cond_e
    sget v0, Landroidx/compose/ui/graphics/e1;->q:I

    invoke-static {p0, v0}, Landroidx/compose/ui/graphics/e1;->E(II)Z

    move-result v0

    if-eqz v0, :cond_f

    const-string p0, "Overlay"

    goto/16 :goto_0

    :cond_f
    sget v0, Landroidx/compose/ui/graphics/e1;->r:I

    invoke-static {p0, v0}, Landroidx/compose/ui/graphics/e1;->E(II)Z

    move-result v0

    if-eqz v0, :cond_10

    const-string p0, "Darken"

    goto/16 :goto_0

    :cond_10
    sget v0, Landroidx/compose/ui/graphics/e1;->s:I

    invoke-static {p0, v0}, Landroidx/compose/ui/graphics/e1;->E(II)Z

    move-result v0

    if-eqz v0, :cond_11

    const-string p0, "Lighten"

    goto/16 :goto_0

    :cond_11
    sget v0, Landroidx/compose/ui/graphics/e1;->t:I

    invoke-static {p0, v0}, Landroidx/compose/ui/graphics/e1;->E(II)Z

    move-result v0

    if-eqz v0, :cond_12

    const-string p0, "ColorDodge"

    goto/16 :goto_0

    :cond_12
    sget v0, Landroidx/compose/ui/graphics/e1;->u:I

    invoke-static {p0, v0}, Landroidx/compose/ui/graphics/e1;->E(II)Z

    move-result v0

    if-eqz v0, :cond_13

    const-string p0, "ColorBurn"

    goto/16 :goto_0

    :cond_13
    sget v0, Landroidx/compose/ui/graphics/e1;->v:I

    invoke-static {p0, v0}, Landroidx/compose/ui/graphics/e1;->E(II)Z

    move-result v0

    if-eqz v0, :cond_14

    const-string p0, "HardLight"

    goto :goto_0

    :cond_14
    sget v0, Landroidx/compose/ui/graphics/e1;->w:I

    invoke-static {p0, v0}, Landroidx/compose/ui/graphics/e1;->E(II)Z

    move-result v0

    if-eqz v0, :cond_15

    const-string p0, "Softlight"

    goto :goto_0

    :cond_15
    sget v0, Landroidx/compose/ui/graphics/e1;->x:I

    invoke-static {p0, v0}, Landroidx/compose/ui/graphics/e1;->E(II)Z

    move-result v0

    if-eqz v0, :cond_16

    const-string p0, "Difference"

    goto :goto_0

    :cond_16
    sget v0, Landroidx/compose/ui/graphics/e1;->y:I

    invoke-static {p0, v0}, Landroidx/compose/ui/graphics/e1;->E(II)Z

    move-result v0

    if-eqz v0, :cond_17

    const-string p0, "Exclusion"

    goto :goto_0

    :cond_17
    sget v0, Landroidx/compose/ui/graphics/e1;->z:I

    invoke-static {p0, v0}, Landroidx/compose/ui/graphics/e1;->E(II)Z

    move-result v0

    if-eqz v0, :cond_18

    const-string p0, "Multiply"

    goto :goto_0

    :cond_18
    sget v0, Landroidx/compose/ui/graphics/e1;->A:I

    invoke-static {p0, v0}, Landroidx/compose/ui/graphics/e1;->E(II)Z

    move-result v0

    if-eqz v0, :cond_19

    const-string p0, "Hue"

    goto :goto_0

    :cond_19
    sget v0, Landroidx/compose/ui/graphics/e1;->B:I

    invoke-static {p0, v0}, Landroidx/compose/ui/graphics/e1;->E(II)Z

    move-result v0

    if-eqz v0, :cond_1a

    const-string p0, "Saturation"

    goto :goto_0

    :cond_1a
    sget v0, Landroidx/compose/ui/graphics/e1;->C:I

    invoke-static {p0, v0}, Landroidx/compose/ui/graphics/e1;->E(II)Z

    move-result v0

    if-eqz v0, :cond_1b

    const-string p0, "Color"

    goto :goto_0

    :cond_1b
    sget v0, Landroidx/compose/ui/graphics/e1;->D:I

    invoke-static {p0, v0}, Landroidx/compose/ui/graphics/e1;->E(II)Z

    move-result p0

    if-eqz p0, :cond_1c

    const-string p0, "Luminosity"

    goto :goto_0

    :cond_1c
    const-string p0, "Unknown"

    :goto_0
    return-object p0
.end method

.method public static final synthetic a()I
    .locals 1

    sget v0, Landroidx/compose/ui/graphics/e1;->b:I

    return v0
.end method

.method public static final synthetic b()I
    .locals 1

    sget v0, Landroidx/compose/ui/graphics/e1;->C:I

    return v0
.end method

.method public static final synthetic c()I
    .locals 1

    sget v0, Landroidx/compose/ui/graphics/e1;->u:I

    return v0
.end method

.method public static final synthetic d()I
    .locals 1

    sget v0, Landroidx/compose/ui/graphics/e1;->t:I

    return v0
.end method

.method public static final synthetic e()I
    .locals 1

    sget v0, Landroidx/compose/ui/graphics/e1;->r:I

    return v0
.end method

.method public static final synthetic f()I
    .locals 1

    sget v0, Landroidx/compose/ui/graphics/e1;->x:I

    return v0
.end method

.method public static final synthetic g()I
    .locals 1

    sget v0, Landroidx/compose/ui/graphics/e1;->d:I

    return v0
.end method

.method public static final synthetic h()I
    .locals 1

    sget v0, Landroidx/compose/ui/graphics/e1;->l:I

    return v0
.end method

.method public static final synthetic i()I
    .locals 1

    sget v0, Landroidx/compose/ui/graphics/e1;->h:I

    return v0
.end method

.method public static final synthetic j()I
    .locals 1

    sget v0, Landroidx/compose/ui/graphics/e1;->j:I

    return v0
.end method

.method public static final synthetic k()I
    .locals 1

    sget v0, Landroidx/compose/ui/graphics/e1;->f:I

    return v0
.end method

.method public static final synthetic l()I
    .locals 1

    sget v0, Landroidx/compose/ui/graphics/e1;->y:I

    return v0
.end method

.method public static final synthetic m()I
    .locals 1

    sget v0, Landroidx/compose/ui/graphics/e1;->v:I

    return v0
.end method

.method public static final synthetic n()I
    .locals 1

    sget v0, Landroidx/compose/ui/graphics/e1;->A:I

    return v0
.end method

.method public static final synthetic o()I
    .locals 1

    sget v0, Landroidx/compose/ui/graphics/e1;->s:I

    return v0
.end method

.method public static final synthetic p()I
    .locals 1

    sget v0, Landroidx/compose/ui/graphics/e1;->D:I

    return v0
.end method

.method public static final synthetic q()I
    .locals 1

    sget v0, Landroidx/compose/ui/graphics/e1;->o:I

    return v0
.end method

.method public static final synthetic r()I
    .locals 1

    sget v0, Landroidx/compose/ui/graphics/e1;->z:I

    return v0
.end method

.method public static final synthetic s()I
    .locals 1

    sget v0, Landroidx/compose/ui/graphics/e1;->q:I

    return v0
.end method

.method public static final synthetic t()I
    .locals 1

    sget v0, Landroidx/compose/ui/graphics/e1;->n:I

    return v0
.end method

.method public static final synthetic u()I
    .locals 1

    sget v0, Landroidx/compose/ui/graphics/e1;->B:I

    return v0
.end method

.method public static final synthetic v()I
    .locals 1

    sget v0, Landroidx/compose/ui/graphics/e1;->p:I

    return v0
.end method

.method public static final synthetic w()I
    .locals 1

    sget v0, Landroidx/compose/ui/graphics/e1;->w:I

    return v0
.end method

.method public static final synthetic x()I
    .locals 1

    sget v0, Landroidx/compose/ui/graphics/e1;->c:I

    return v0
.end method

.method public static final synthetic y()I
    .locals 1

    sget v0, Landroidx/compose/ui/graphics/e1;->k:I

    return v0
.end method

.method public static final synthetic z()I
    .locals 1

    sget v0, Landroidx/compose/ui/graphics/e1;->g:I

    return v0
.end method
