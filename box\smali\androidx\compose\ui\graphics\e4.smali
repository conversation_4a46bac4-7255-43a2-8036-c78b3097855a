.class public interface abstract Landroidx/compose/ui/graphics/e4;
.super Ljava/lang/Object;

# interfaces
.implements Lv0/e;


# annotations
.annotation runtime L<PERSON>lin/Metadata;
.end annotation


# virtual methods
.method public abstract A()F
.end method

.method public abstract B()F
.end method

.method public abstract C()F
.end method

.method public abstract F()F
.end method

.method public abstract J0(Landroidx/compose/ui/graphics/c5;)V
.end method

.method public abstract b(F)V
.end method

.method public abstract c(F)V
.end method

.method public abstract d(F)V
.end method

.method public abstract e(Landroidx/compose/ui/graphics/y4;)V
.end method

.method public abstract f(F)V
.end method

.method public abstract g(F)V
.end method

.method public abstract h(F)V
.end method

.method public abstract i(F)V
.end method

.method public abstract i0()J
.end method

.method public abstract j(F)V
.end method

.method public abstract k(F)V
.end method

.method public abstract k0(J)V
.end method

.method public abstract n(I)V
.end method

.method public abstract o()F
.end method

.method public abstract q()F
.end method

.method public abstract r(J)V
.end method

.method public abstract s()F
.end method

.method public abstract t(Z)V
.end method

.method public abstract u(J)V
.end method

.method public abstract w()F
.end method

.method public abstract x(F)V
.end method
