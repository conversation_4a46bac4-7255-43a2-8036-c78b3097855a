.class public final Landroidx/compose/ui/graphics/f0;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Landroidx/compose/ui/graphics/f0$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation


# direct methods
.method public static final a(I)Landroid/graphics/BlendMode;
    .locals 2
    .annotation build Landroidx/annotation/RequiresApi;
        value = 0x1d
    .end annotation

    sget-object v0, Landroidx/compose/ui/graphics/e1;->a:Landroidx/compose/ui/graphics/e1$a;

    invoke-virtual {v0}, Landroidx/compose/ui/graphics/e1$a;->a()I

    move-result v1

    invoke-static {p0, v1}, Landroidx/compose/ui/graphics/e1;->E(II)Z

    move-result v1

    if-eqz v1, :cond_0

    invoke-static {}, Landroidx/compose/ui/graphics/a;->a()Landroid/graphics/BlendMode;

    move-result-object p0

    goto/16 :goto_0

    :cond_0
    invoke-virtual {v0}, Landroidx/compose/ui/graphics/e1$a;->x()I

    move-result v1

    invoke-static {p0, v1}, Landroidx/compose/ui/graphics/e1;->E(II)Z

    move-result v1

    if-eqz v1, :cond_1

    invoke-static {}, Landroidx/compose/ui/graphics/c;->a()Landroid/graphics/BlendMode;

    move-result-object p0

    goto/16 :goto_0

    :cond_1
    invoke-virtual {v0}, Landroidx/compose/ui/graphics/e1$a;->g()I

    move-result v1

    invoke-static {p0, v1}, Landroidx/compose/ui/graphics/e1;->E(II)Z

    move-result v1

    if-eqz v1, :cond_2

    invoke-static {}, Landroidx/compose/ui/graphics/o;->a()Landroid/graphics/BlendMode;

    move-result-object p0

    goto/16 :goto_0

    :cond_2
    invoke-virtual {v0}, Landroidx/compose/ui/graphics/e1$a;->B()I

    move-result v1

    invoke-static {p0, v1}, Landroidx/compose/ui/graphics/e1;->E(II)Z

    move-result v1

    if-eqz v1, :cond_3

    invoke-static {}, Landroidx/compose/ui/graphics/n;->a()Landroid/graphics/BlendMode;

    move-result-object p0

    goto/16 :goto_0

    :cond_3
    invoke-virtual {v0}, Landroidx/compose/ui/graphics/e1$a;->k()I

    move-result v1

    invoke-static {p0, v1}, Landroidx/compose/ui/graphics/e1;->E(II)Z

    move-result v1

    if-eqz v1, :cond_4

    invoke-static {}, Landroidx/compose/ui/graphics/p;->a()Landroid/graphics/BlendMode;

    move-result-object p0

    goto/16 :goto_0

    :cond_4
    invoke-virtual {v0}, Landroidx/compose/ui/graphics/e1$a;->z()I

    move-result v1

    invoke-static {p0, v1}, Landroidx/compose/ui/graphics/e1;->E(II)Z

    move-result v1

    if-eqz v1, :cond_5

    invoke-static {}, Landroidx/compose/ui/graphics/q;->a()Landroid/graphics/BlendMode;

    move-result-object p0

    goto/16 :goto_0

    :cond_5
    invoke-virtual {v0}, Landroidx/compose/ui/graphics/e1$a;->i()I

    move-result v1

    invoke-static {p0, v1}, Landroidx/compose/ui/graphics/e1;->E(II)Z

    move-result v1

    if-eqz v1, :cond_6

    invoke-static {}, Landroidx/compose/ui/graphics/r;->a()Landroid/graphics/BlendMode;

    move-result-object p0

    goto/16 :goto_0

    :cond_6
    invoke-virtual {v0}, Landroidx/compose/ui/graphics/e1$a;->A()I

    move-result v1

    invoke-static {p0, v1}, Landroidx/compose/ui/graphics/e1;->E(II)Z

    move-result v1

    if-eqz v1, :cond_7

    invoke-static {}, Landroidx/compose/ui/graphics/s;->a()Landroid/graphics/BlendMode;

    move-result-object p0

    goto/16 :goto_0

    :cond_7
    invoke-virtual {v0}, Landroidx/compose/ui/graphics/e1$a;->j()I

    move-result v1

    invoke-static {p0, v1}, Landroidx/compose/ui/graphics/e1;->E(II)Z

    move-result v1

    if-eqz v1, :cond_8

    invoke-static {}, Landroidx/compose/ui/graphics/t;->a()Landroid/graphics/BlendMode;

    move-result-object p0

    goto/16 :goto_0

    :cond_8
    invoke-virtual {v0}, Landroidx/compose/ui/graphics/e1$a;->y()I

    move-result v1

    invoke-static {p0, v1}, Landroidx/compose/ui/graphics/e1;->E(II)Z

    move-result v1

    if-eqz v1, :cond_9

    invoke-static {}, Landroidx/compose/ui/graphics/u;->a()Landroid/graphics/BlendMode;

    move-result-object p0

    goto/16 :goto_0

    :cond_9
    invoke-virtual {v0}, Landroidx/compose/ui/graphics/e1$a;->h()I

    move-result v1

    invoke-static {p0, v1}, Landroidx/compose/ui/graphics/e1;->E(II)Z

    move-result v1

    if-eqz v1, :cond_a

    invoke-static {}, Landroidx/compose/ui/graphics/l;->a()Landroid/graphics/BlendMode;

    move-result-object p0

    goto/16 :goto_0

    :cond_a
    invoke-virtual {v0}, Landroidx/compose/ui/graphics/e1$a;->C()I

    move-result v1

    invoke-static {p0, v1}, Landroidx/compose/ui/graphics/e1;->E(II)Z

    move-result v1

    if-eqz v1, :cond_b

    invoke-static {}, Landroidx/compose/ui/graphics/w;->a()Landroid/graphics/BlendMode;

    move-result-object p0

    goto/16 :goto_0

    :cond_b
    invoke-virtual {v0}, Landroidx/compose/ui/graphics/e1$a;->t()I

    move-result v1

    invoke-static {p0, v1}, Landroidx/compose/ui/graphics/e1;->E(II)Z

    move-result v1

    if-eqz v1, :cond_c

    invoke-static {}, Landroidx/compose/ui/graphics/x;->a()Landroid/graphics/BlendMode;

    move-result-object p0

    goto/16 :goto_0

    :cond_c
    invoke-virtual {v0}, Landroidx/compose/ui/graphics/e1$a;->q()I

    move-result v1

    invoke-static {p0, v1}, Landroidx/compose/ui/graphics/e1;->E(II)Z

    move-result v1

    if-eqz v1, :cond_d

    invoke-static {}, Landroidx/compose/ui/graphics/y;->a()Landroid/graphics/BlendMode;

    move-result-object p0

    goto/16 :goto_0

    :cond_d
    invoke-virtual {v0}, Landroidx/compose/ui/graphics/e1$a;->v()I

    move-result v1

    invoke-static {p0, v1}, Landroidx/compose/ui/graphics/e1;->E(II)Z

    move-result v1

    if-eqz v1, :cond_e

    invoke-static {}, Landroidx/compose/ui/graphics/z;->a()Landroid/graphics/BlendMode;

    move-result-object p0

    goto/16 :goto_0

    :cond_e
    invoke-virtual {v0}, Landroidx/compose/ui/graphics/e1$a;->s()I

    move-result v1

    invoke-static {p0, v1}, Landroidx/compose/ui/graphics/e1;->E(II)Z

    move-result v1

    if-eqz v1, :cond_f

    invoke-static {}, Landroidx/compose/ui/graphics/a0;->a()Landroid/graphics/BlendMode;

    move-result-object p0

    goto/16 :goto_0

    :cond_f
    invoke-virtual {v0}, Landroidx/compose/ui/graphics/e1$a;->e()I

    move-result v1

    invoke-static {p0, v1}, Landroidx/compose/ui/graphics/e1;->E(II)Z

    move-result v1

    if-eqz v1, :cond_10

    invoke-static {}, Landroidx/compose/ui/graphics/b0;->a()Landroid/graphics/BlendMode;

    move-result-object p0

    goto/16 :goto_0

    :cond_10
    invoke-virtual {v0}, Landroidx/compose/ui/graphics/e1$a;->o()I

    move-result v1

    invoke-static {p0, v1}, Landroidx/compose/ui/graphics/e1;->E(II)Z

    move-result v1

    if-eqz v1, :cond_11

    invoke-static {}, Landroidx/compose/ui/graphics/c0;->a()Landroid/graphics/BlendMode;

    move-result-object p0

    goto/16 :goto_0

    :cond_11
    invoke-virtual {v0}, Landroidx/compose/ui/graphics/e1$a;->d()I

    move-result v1

    invoke-static {p0, v1}, Landroidx/compose/ui/graphics/e1;->E(II)Z

    move-result v1

    if-eqz v1, :cond_12

    invoke-static {}, Landroidx/compose/ui/graphics/d0;->a()Landroid/graphics/BlendMode;

    move-result-object p0

    goto/16 :goto_0

    :cond_12
    invoke-virtual {v0}, Landroidx/compose/ui/graphics/e1$a;->c()I

    move-result v1

    invoke-static {p0, v1}, Landroidx/compose/ui/graphics/e1;->E(II)Z

    move-result v1

    if-eqz v1, :cond_13

    invoke-static {}, Landroidx/compose/ui/graphics/b;->a()Landroid/graphics/BlendMode;

    move-result-object p0

    goto/16 :goto_0

    :cond_13
    invoke-virtual {v0}, Landroidx/compose/ui/graphics/e1$a;->m()I

    move-result v1

    invoke-static {p0, v1}, Landroidx/compose/ui/graphics/e1;->E(II)Z

    move-result v1

    if-eqz v1, :cond_14

    invoke-static {}, Landroidx/compose/ui/graphics/d;->a()Landroid/graphics/BlendMode;

    move-result-object p0

    goto/16 :goto_0

    :cond_14
    invoke-virtual {v0}, Landroidx/compose/ui/graphics/e1$a;->w()I

    move-result v1

    invoke-static {p0, v1}, Landroidx/compose/ui/graphics/e1;->E(II)Z

    move-result v1

    if-eqz v1, :cond_15

    invoke-static {}, Landroidx/compose/ui/graphics/e;->a()Landroid/graphics/BlendMode;

    move-result-object p0

    goto/16 :goto_0

    :cond_15
    invoke-virtual {v0}, Landroidx/compose/ui/graphics/e1$a;->f()I

    move-result v1

    invoke-static {p0, v1}, Landroidx/compose/ui/graphics/e1;->E(II)Z

    move-result v1

    if-eqz v1, :cond_16

    invoke-static {}, Landroidx/compose/ui/graphics/f;->a()Landroid/graphics/BlendMode;

    move-result-object p0

    goto :goto_0

    :cond_16
    invoke-virtual {v0}, Landroidx/compose/ui/graphics/e1$a;->l()I

    move-result v1

    invoke-static {p0, v1}, Landroidx/compose/ui/graphics/e1;->E(II)Z

    move-result v1

    if-eqz v1, :cond_17

    invoke-static {}, Landroidx/compose/ui/graphics/g;->a()Landroid/graphics/BlendMode;

    move-result-object p0

    goto :goto_0

    :cond_17
    invoke-virtual {v0}, Landroidx/compose/ui/graphics/e1$a;->r()I

    move-result v1

    invoke-static {p0, v1}, Landroidx/compose/ui/graphics/e1;->E(II)Z

    move-result v1

    if-eqz v1, :cond_18

    invoke-static {}, Landroidx/compose/ui/graphics/h;->a()Landroid/graphics/BlendMode;

    move-result-object p0

    goto :goto_0

    :cond_18
    invoke-virtual {v0}, Landroidx/compose/ui/graphics/e1$a;->n()I

    move-result v1

    invoke-static {p0, v1}, Landroidx/compose/ui/graphics/e1;->E(II)Z

    move-result v1

    if-eqz v1, :cond_19

    invoke-static {}, Landroidx/compose/ui/graphics/i;->a()Landroid/graphics/BlendMode;

    move-result-object p0

    goto :goto_0

    :cond_19
    invoke-virtual {v0}, Landroidx/compose/ui/graphics/e1$a;->u()I

    move-result v1

    invoke-static {p0, v1}, Landroidx/compose/ui/graphics/e1;->E(II)Z

    move-result v1

    if-eqz v1, :cond_1a

    invoke-static {}, Landroidx/compose/ui/graphics/j;->a()Landroid/graphics/BlendMode;

    move-result-object p0

    goto :goto_0

    :cond_1a
    invoke-virtual {v0}, Landroidx/compose/ui/graphics/e1$a;->b()I

    move-result v1

    invoke-static {p0, v1}, Landroidx/compose/ui/graphics/e1;->E(II)Z

    move-result v1

    if-eqz v1, :cond_1b

    invoke-static {}, Landroidx/compose/ui/graphics/k;->a()Landroid/graphics/BlendMode;

    move-result-object p0

    goto :goto_0

    :cond_1b
    invoke-virtual {v0}, Landroidx/compose/ui/graphics/e1$a;->p()I

    move-result v0

    invoke-static {p0, v0}, Landroidx/compose/ui/graphics/e1;->E(II)Z

    move-result p0

    if-eqz p0, :cond_1c

    invoke-static {}, Landroidx/compose/ui/graphics/m;->a()Landroid/graphics/BlendMode;

    move-result-object p0

    goto :goto_0

    :cond_1c
    invoke-static {}, Landroidx/compose/ui/graphics/n;->a()Landroid/graphics/BlendMode;

    move-result-object p0

    :goto_0
    return-object p0
.end method

.method public static final b(Landroid/graphics/BlendMode;)I
    .locals 1
    .annotation build Landroidx/annotation/RequiresApi;
        value = 0x1d
    .end annotation

    sget-object v0, Landroidx/compose/ui/graphics/f0$a;->a:[I

    invoke-static {p0}, Landroidx/compose/ui/graphics/v;->a(Landroid/graphics/BlendMode;)I

    move-result p0

    aget p0, v0, p0

    packed-switch p0, :pswitch_data_0

    sget-object p0, Landroidx/compose/ui/graphics/e1;->a:Landroidx/compose/ui/graphics/e1$a;

    invoke-virtual {p0}, Landroidx/compose/ui/graphics/e1$a;->B()I

    move-result p0

    goto/16 :goto_0

    :pswitch_0
    sget-object p0, Landroidx/compose/ui/graphics/e1;->a:Landroidx/compose/ui/graphics/e1$a;

    invoke-virtual {p0}, Landroidx/compose/ui/graphics/e1$a;->p()I

    move-result p0

    goto/16 :goto_0

    :pswitch_1
    sget-object p0, Landroidx/compose/ui/graphics/e1;->a:Landroidx/compose/ui/graphics/e1$a;

    invoke-virtual {p0}, Landroidx/compose/ui/graphics/e1$a;->b()I

    move-result p0

    goto/16 :goto_0

    :pswitch_2
    sget-object p0, Landroidx/compose/ui/graphics/e1;->a:Landroidx/compose/ui/graphics/e1$a;

    invoke-virtual {p0}, Landroidx/compose/ui/graphics/e1$a;->u()I

    move-result p0

    goto/16 :goto_0

    :pswitch_3
    sget-object p0, Landroidx/compose/ui/graphics/e1;->a:Landroidx/compose/ui/graphics/e1$a;

    invoke-virtual {p0}, Landroidx/compose/ui/graphics/e1$a;->n()I

    move-result p0

    goto/16 :goto_0

    :pswitch_4
    sget-object p0, Landroidx/compose/ui/graphics/e1;->a:Landroidx/compose/ui/graphics/e1$a;

    invoke-virtual {p0}, Landroidx/compose/ui/graphics/e1$a;->r()I

    move-result p0

    goto/16 :goto_0

    :pswitch_5
    sget-object p0, Landroidx/compose/ui/graphics/e1;->a:Landroidx/compose/ui/graphics/e1$a;

    invoke-virtual {p0}, Landroidx/compose/ui/graphics/e1$a;->l()I

    move-result p0

    goto/16 :goto_0

    :pswitch_6
    sget-object p0, Landroidx/compose/ui/graphics/e1;->a:Landroidx/compose/ui/graphics/e1$a;

    invoke-virtual {p0}, Landroidx/compose/ui/graphics/e1$a;->f()I

    move-result p0

    goto/16 :goto_0

    :pswitch_7
    sget-object p0, Landroidx/compose/ui/graphics/e1;->a:Landroidx/compose/ui/graphics/e1$a;

    invoke-virtual {p0}, Landroidx/compose/ui/graphics/e1$a;->w()I

    move-result p0

    goto/16 :goto_0

    :pswitch_8
    sget-object p0, Landroidx/compose/ui/graphics/e1;->a:Landroidx/compose/ui/graphics/e1$a;

    invoke-virtual {p0}, Landroidx/compose/ui/graphics/e1$a;->m()I

    move-result p0

    goto/16 :goto_0

    :pswitch_9
    sget-object p0, Landroidx/compose/ui/graphics/e1;->a:Landroidx/compose/ui/graphics/e1$a;

    invoke-virtual {p0}, Landroidx/compose/ui/graphics/e1$a;->c()I

    move-result p0

    goto/16 :goto_0

    :pswitch_a
    sget-object p0, Landroidx/compose/ui/graphics/e1;->a:Landroidx/compose/ui/graphics/e1$a;

    invoke-virtual {p0}, Landroidx/compose/ui/graphics/e1$a;->d()I

    move-result p0

    goto/16 :goto_0

    :pswitch_b
    sget-object p0, Landroidx/compose/ui/graphics/e1;->a:Landroidx/compose/ui/graphics/e1$a;

    invoke-virtual {p0}, Landroidx/compose/ui/graphics/e1$a;->o()I

    move-result p0

    goto/16 :goto_0

    :pswitch_c
    sget-object p0, Landroidx/compose/ui/graphics/e1;->a:Landroidx/compose/ui/graphics/e1$a;

    invoke-virtual {p0}, Landroidx/compose/ui/graphics/e1$a;->e()I

    move-result p0

    goto/16 :goto_0

    :pswitch_d
    sget-object p0, Landroidx/compose/ui/graphics/e1;->a:Landroidx/compose/ui/graphics/e1$a;

    invoke-virtual {p0}, Landroidx/compose/ui/graphics/e1$a;->s()I

    move-result p0

    goto/16 :goto_0

    :pswitch_e
    sget-object p0, Landroidx/compose/ui/graphics/e1;->a:Landroidx/compose/ui/graphics/e1$a;

    invoke-virtual {p0}, Landroidx/compose/ui/graphics/e1$a;->v()I

    move-result p0

    goto :goto_0

    :pswitch_f
    sget-object p0, Landroidx/compose/ui/graphics/e1;->a:Landroidx/compose/ui/graphics/e1$a;

    invoke-virtual {p0}, Landroidx/compose/ui/graphics/e1$a;->q()I

    move-result p0

    goto :goto_0

    :pswitch_10
    sget-object p0, Landroidx/compose/ui/graphics/e1;->a:Landroidx/compose/ui/graphics/e1$a;

    invoke-virtual {p0}, Landroidx/compose/ui/graphics/e1$a;->t()I

    move-result p0

    goto :goto_0

    :pswitch_11
    sget-object p0, Landroidx/compose/ui/graphics/e1;->a:Landroidx/compose/ui/graphics/e1$a;

    invoke-virtual {p0}, Landroidx/compose/ui/graphics/e1$a;->C()I

    move-result p0

    goto :goto_0

    :pswitch_12
    sget-object p0, Landroidx/compose/ui/graphics/e1;->a:Landroidx/compose/ui/graphics/e1$a;

    invoke-virtual {p0}, Landroidx/compose/ui/graphics/e1$a;->h()I

    move-result p0

    goto :goto_0

    :pswitch_13
    sget-object p0, Landroidx/compose/ui/graphics/e1;->a:Landroidx/compose/ui/graphics/e1$a;

    invoke-virtual {p0}, Landroidx/compose/ui/graphics/e1$a;->y()I

    move-result p0

    goto :goto_0

    :pswitch_14
    sget-object p0, Landroidx/compose/ui/graphics/e1;->a:Landroidx/compose/ui/graphics/e1$a;

    invoke-virtual {p0}, Landroidx/compose/ui/graphics/e1$a;->j()I

    move-result p0

    goto :goto_0

    :pswitch_15
    sget-object p0, Landroidx/compose/ui/graphics/e1;->a:Landroidx/compose/ui/graphics/e1$a;

    invoke-virtual {p0}, Landroidx/compose/ui/graphics/e1$a;->A()I

    move-result p0

    goto :goto_0

    :pswitch_16
    sget-object p0, Landroidx/compose/ui/graphics/e1;->a:Landroidx/compose/ui/graphics/e1$a;

    invoke-virtual {p0}, Landroidx/compose/ui/graphics/e1$a;->i()I

    move-result p0

    goto :goto_0

    :pswitch_17
    sget-object p0, Landroidx/compose/ui/graphics/e1;->a:Landroidx/compose/ui/graphics/e1$a;

    invoke-virtual {p0}, Landroidx/compose/ui/graphics/e1$a;->z()I

    move-result p0

    goto :goto_0

    :pswitch_18
    sget-object p0, Landroidx/compose/ui/graphics/e1;->a:Landroidx/compose/ui/graphics/e1$a;

    invoke-virtual {p0}, Landroidx/compose/ui/graphics/e1$a;->k()I

    move-result p0

    goto :goto_0

    :pswitch_19
    sget-object p0, Landroidx/compose/ui/graphics/e1;->a:Landroidx/compose/ui/graphics/e1$a;

    invoke-virtual {p0}, Landroidx/compose/ui/graphics/e1$a;->B()I

    move-result p0

    goto :goto_0

    :pswitch_1a
    sget-object p0, Landroidx/compose/ui/graphics/e1;->a:Landroidx/compose/ui/graphics/e1$a;

    invoke-virtual {p0}, Landroidx/compose/ui/graphics/e1$a;->g()I

    move-result p0

    goto :goto_0

    :pswitch_1b
    sget-object p0, Landroidx/compose/ui/graphics/e1;->a:Landroidx/compose/ui/graphics/e1$a;

    invoke-virtual {p0}, Landroidx/compose/ui/graphics/e1$a;->x()I

    move-result p0

    goto :goto_0

    :pswitch_1c
    sget-object p0, Landroidx/compose/ui/graphics/e1;->a:Landroidx/compose/ui/graphics/e1$a;

    invoke-virtual {p0}, Landroidx/compose/ui/graphics/e1$a;->a()I

    move-result p0

    :goto_0
    return p0

    :pswitch_data_0
    .packed-switch 0x1
        :pswitch_1c
        :pswitch_1b
        :pswitch_1a
        :pswitch_19
        :pswitch_18
        :pswitch_17
        :pswitch_16
        :pswitch_15
        :pswitch_14
        :pswitch_13
        :pswitch_12
        :pswitch_11
        :pswitch_10
        :pswitch_f
        :pswitch_e
        :pswitch_d
        :pswitch_c
        :pswitch_b
        :pswitch_a
        :pswitch_9
        :pswitch_8
        :pswitch_7
        :pswitch_6
        :pswitch_5
        :pswitch_4
        :pswitch_3
        :pswitch_2
        :pswitch_1
        :pswitch_0
    .end packed-switch
.end method

.method public static final c(I)Landroid/graphics/PorterDuff$Mode;
    .locals 2

    sget-object v0, Landroidx/compose/ui/graphics/e1;->a:Landroidx/compose/ui/graphics/e1$a;

    invoke-virtual {v0}, Landroidx/compose/ui/graphics/e1$a;->a()I

    move-result v1

    invoke-static {p0, v1}, Landroidx/compose/ui/graphics/e1;->E(II)Z

    move-result v1

    if-eqz v1, :cond_0

    sget-object p0, Landroid/graphics/PorterDuff$Mode;->CLEAR:Landroid/graphics/PorterDuff$Mode;

    goto/16 :goto_0

    :cond_0
    invoke-virtual {v0}, Landroidx/compose/ui/graphics/e1$a;->x()I

    move-result v1

    invoke-static {p0, v1}, Landroidx/compose/ui/graphics/e1;->E(II)Z

    move-result v1

    if-eqz v1, :cond_1

    sget-object p0, Landroid/graphics/PorterDuff$Mode;->SRC:Landroid/graphics/PorterDuff$Mode;

    goto/16 :goto_0

    :cond_1
    invoke-virtual {v0}, Landroidx/compose/ui/graphics/e1$a;->g()I

    move-result v1

    invoke-static {p0, v1}, Landroidx/compose/ui/graphics/e1;->E(II)Z

    move-result v1

    if-eqz v1, :cond_2

    sget-object p0, Landroid/graphics/PorterDuff$Mode;->DST:Landroid/graphics/PorterDuff$Mode;

    goto/16 :goto_0

    :cond_2
    invoke-virtual {v0}, Landroidx/compose/ui/graphics/e1$a;->B()I

    move-result v1

    invoke-static {p0, v1}, Landroidx/compose/ui/graphics/e1;->E(II)Z

    move-result v1

    if-eqz v1, :cond_3

    sget-object p0, Landroid/graphics/PorterDuff$Mode;->SRC_OVER:Landroid/graphics/PorterDuff$Mode;

    goto/16 :goto_0

    :cond_3
    invoke-virtual {v0}, Landroidx/compose/ui/graphics/e1$a;->k()I

    move-result v1

    invoke-static {p0, v1}, Landroidx/compose/ui/graphics/e1;->E(II)Z

    move-result v1

    if-eqz v1, :cond_4

    sget-object p0, Landroid/graphics/PorterDuff$Mode;->DST_OVER:Landroid/graphics/PorterDuff$Mode;

    goto/16 :goto_0

    :cond_4
    invoke-virtual {v0}, Landroidx/compose/ui/graphics/e1$a;->z()I

    move-result v1

    invoke-static {p0, v1}, Landroidx/compose/ui/graphics/e1;->E(II)Z

    move-result v1

    if-eqz v1, :cond_5

    sget-object p0, Landroid/graphics/PorterDuff$Mode;->SRC_IN:Landroid/graphics/PorterDuff$Mode;

    goto/16 :goto_0

    :cond_5
    invoke-virtual {v0}, Landroidx/compose/ui/graphics/e1$a;->i()I

    move-result v1

    invoke-static {p0, v1}, Landroidx/compose/ui/graphics/e1;->E(II)Z

    move-result v1

    if-eqz v1, :cond_6

    sget-object p0, Landroid/graphics/PorterDuff$Mode;->DST_IN:Landroid/graphics/PorterDuff$Mode;

    goto/16 :goto_0

    :cond_6
    invoke-virtual {v0}, Landroidx/compose/ui/graphics/e1$a;->A()I

    move-result v1

    invoke-static {p0, v1}, Landroidx/compose/ui/graphics/e1;->E(II)Z

    move-result v1

    if-eqz v1, :cond_7

    sget-object p0, Landroid/graphics/PorterDuff$Mode;->SRC_OUT:Landroid/graphics/PorterDuff$Mode;

    goto/16 :goto_0

    :cond_7
    invoke-virtual {v0}, Landroidx/compose/ui/graphics/e1$a;->j()I

    move-result v1

    invoke-static {p0, v1}, Landroidx/compose/ui/graphics/e1;->E(II)Z

    move-result v1

    if-eqz v1, :cond_8

    sget-object p0, Landroid/graphics/PorterDuff$Mode;->DST_OUT:Landroid/graphics/PorterDuff$Mode;

    goto/16 :goto_0

    :cond_8
    invoke-virtual {v0}, Landroidx/compose/ui/graphics/e1$a;->y()I

    move-result v1

    invoke-static {p0, v1}, Landroidx/compose/ui/graphics/e1;->E(II)Z

    move-result v1

    if-eqz v1, :cond_9

    sget-object p0, Landroid/graphics/PorterDuff$Mode;->SRC_ATOP:Landroid/graphics/PorterDuff$Mode;

    goto/16 :goto_0

    :cond_9
    invoke-virtual {v0}, Landroidx/compose/ui/graphics/e1$a;->h()I

    move-result v1

    invoke-static {p0, v1}, Landroidx/compose/ui/graphics/e1;->E(II)Z

    move-result v1

    if-eqz v1, :cond_a

    sget-object p0, Landroid/graphics/PorterDuff$Mode;->DST_ATOP:Landroid/graphics/PorterDuff$Mode;

    goto :goto_0

    :cond_a
    invoke-virtual {v0}, Landroidx/compose/ui/graphics/e1$a;->C()I

    move-result v1

    invoke-static {p0, v1}, Landroidx/compose/ui/graphics/e1;->E(II)Z

    move-result v1

    if-eqz v1, :cond_b

    sget-object p0, Landroid/graphics/PorterDuff$Mode;->XOR:Landroid/graphics/PorterDuff$Mode;

    goto :goto_0

    :cond_b
    invoke-virtual {v0}, Landroidx/compose/ui/graphics/e1$a;->t()I

    move-result v1

    invoke-static {p0, v1}, Landroidx/compose/ui/graphics/e1;->E(II)Z

    move-result v1

    if-eqz v1, :cond_c

    sget-object p0, Landroid/graphics/PorterDuff$Mode;->ADD:Landroid/graphics/PorterDuff$Mode;

    goto :goto_0

    :cond_c
    invoke-virtual {v0}, Landroidx/compose/ui/graphics/e1$a;->v()I

    move-result v1

    invoke-static {p0, v1}, Landroidx/compose/ui/graphics/e1;->E(II)Z

    move-result v1

    if-eqz v1, :cond_d

    sget-object p0, Landroid/graphics/PorterDuff$Mode;->SCREEN:Landroid/graphics/PorterDuff$Mode;

    goto :goto_0

    :cond_d
    invoke-virtual {v0}, Landroidx/compose/ui/graphics/e1$a;->s()I

    move-result v1

    invoke-static {p0, v1}, Landroidx/compose/ui/graphics/e1;->E(II)Z

    move-result v1

    if-eqz v1, :cond_e

    sget-object p0, Landroid/graphics/PorterDuff$Mode;->OVERLAY:Landroid/graphics/PorterDuff$Mode;

    goto :goto_0

    :cond_e
    invoke-virtual {v0}, Landroidx/compose/ui/graphics/e1$a;->e()I

    move-result v1

    invoke-static {p0, v1}, Landroidx/compose/ui/graphics/e1;->E(II)Z

    move-result v1

    if-eqz v1, :cond_f

    sget-object p0, Landroid/graphics/PorterDuff$Mode;->DARKEN:Landroid/graphics/PorterDuff$Mode;

    goto :goto_0

    :cond_f
    invoke-virtual {v0}, Landroidx/compose/ui/graphics/e1$a;->o()I

    move-result v1

    invoke-static {p0, v1}, Landroidx/compose/ui/graphics/e1;->E(II)Z

    move-result v1

    if-eqz v1, :cond_10

    sget-object p0, Landroid/graphics/PorterDuff$Mode;->LIGHTEN:Landroid/graphics/PorterDuff$Mode;

    goto :goto_0

    :cond_10
    invoke-virtual {v0}, Landroidx/compose/ui/graphics/e1$a;->q()I

    move-result v0

    invoke-static {p0, v0}, Landroidx/compose/ui/graphics/e1;->E(II)Z

    move-result p0

    if-eqz p0, :cond_11

    sget-object p0, Landroid/graphics/PorterDuff$Mode;->MULTIPLY:Landroid/graphics/PorterDuff$Mode;

    goto :goto_0

    :cond_11
    sget-object p0, Landroid/graphics/PorterDuff$Mode;->SRC_OVER:Landroid/graphics/PorterDuff$Mode;

    :goto_0
    return-object p0
.end method
