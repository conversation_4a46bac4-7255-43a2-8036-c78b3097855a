.class public interface abstract Landroidx/compose/ui/graphics/layer/GraphicsLayerImpl;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Landroidx/compose/ui/graphics/layer/GraphicsLayerImpl$Companion;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation


# static fields
.field public static final a:Landroidx/compose/ui/graphics/layer/GraphicsLayerImpl$Companion;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    sget-object v0, Landroidx/compose/ui/graphics/layer/GraphicsLayerImpl$Companion;->a:Landroidx/compose/ui/graphics/layer/GraphicsLayerImpl$Companion;

    sput-object v0, Landroidx/compose/ui/graphics/layer/GraphicsLayerImpl;->a:Landroidx/compose/ui/graphics/layer/GraphicsLayerImpl$Companion;

    return-void
.end method


# virtual methods
.method public abstract A()F
.end method

.method public abstract B()F
.end method

.method public abstract C()F
.end method

.method public abstract D()J
.end method

.method public abstract E()J
.end method

.method public abstract F()F
.end method

.method public abstract G()Landroid/graphics/Matrix;
.end method

.method public abstract H(Lv0/e;Landroidx/compose/ui/unit/LayoutDirection;Landroidx/compose/ui/graphics/layer/GraphicsLayer;Lkotlin/jvm/functions/Function1;)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lv0/e;",
            "Landroidx/compose/ui/unit/LayoutDirection;",
            "Landroidx/compose/ui/graphics/layer/GraphicsLayer;",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Le0/g;",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation
.end method

.method public abstract I(Z)V
.end method

.method public abstract J(Landroid/graphics/Outline;J)V
.end method

.method public abstract K(J)V
.end method

.method public abstract L(I)V
.end method

.method public abstract M()F
.end method

.method public abstract N(Landroidx/compose/ui/graphics/o1;)V
.end method

.method public abstract a()F
.end method

.method public abstract b(F)V
.end method

.method public abstract c(F)V
.end method

.method public abstract d(F)V
.end method

.method public abstract e(Landroidx/compose/ui/graphics/y4;)V
.end method

.method public abstract f(F)V
.end method

.method public abstract g(F)V
.end method

.method public abstract h(F)V
.end method

.method public abstract i(F)V
.end method

.method public abstract j(F)V
.end method

.method public abstract k(F)V
.end method

.method public abstract l()Landroidx/compose/ui/graphics/x1;
.end method

.method public abstract m()V
.end method

.method public abstract n()I
.end method

.method public abstract o()F
.end method

.method public abstract p()Z
.end method

.method public abstract q()F
.end method

.method public abstract r(J)V
.end method

.method public abstract s()F
.end method

.method public abstract t(Z)V
.end method

.method public abstract u(J)V
.end method

.method public abstract v()Landroidx/compose/ui/graphics/y4;
.end method

.method public abstract w()F
.end method

.method public abstract x(F)V
.end method

.method public abstract y()I
.end method

.method public abstract z(IIJ)V
.end method
