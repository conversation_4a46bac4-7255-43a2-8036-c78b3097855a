.class public final Landroidx/compose/ui/graphics/layer/v0;
.super Ljava/lang/Object;


# annotations
.annotation build Landroidx/annotation/RequiresApi;
    value = 0x1c
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation


# static fields
.field public static final a:Landroidx/compose/ui/graphics/layer/v0;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    new-instance v0, Landroidx/compose/ui/graphics/layer/v0;

    invoke-direct {v0}, Landroidx/compose/ui/graphics/layer/v0;-><init>()V

    sput-object v0, Landroidx/compose/ui/graphics/layer/v0;->a:Landroidx/compose/ui/graphics/layer/v0;

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final a(Landroid/view/View;)V
    .locals 0

    invoke-static {p1}, Landroidx/compose/ui/graphics/layer/t0;->a(Landroid/view/View;)V

    return-void
.end method

.method public final b(Landroid/view/View;I)V
    .locals 0

    invoke-static {p1, p2}, Landroidx/compose/ui/graphics/layer/u0;->a(Landroid/view/View;I)V

    return-void
.end method

.method public final c(Landroid/view/View;I)V
    .locals 0

    invoke-static {p1, p2}, Landroidx/compose/ui/graphics/layer/s0;->a(Landroid/view/View;I)V

    return-void
.end method
