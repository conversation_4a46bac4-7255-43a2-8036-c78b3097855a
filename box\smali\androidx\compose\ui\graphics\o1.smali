.class public interface abstract Landroidx/compose/ui/graphics/o1;
.super Ljava/lang/Object;


# annotations
.annotation runtime Lkotlin/Metadata;
.end annotation


# virtual methods
.method public abstract a(FFFFI)V
.end method

.method public abstract b(Landroidx/compose/ui/graphics/Path;I)V
.end method

.method public abstract c(FF)V
.end method

.method public abstract d(FF)V
.end method

.method public abstract e(Ld0/i;Landroidx/compose/ui/graphics/o4;)V
.end method

.method public abstract f(FFFFLandroidx/compose/ui/graphics/o4;)V
.end method

.method public abstract g(ILjava/util/List;Landroidx/compose/ui/graphics/o4;)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I",
            "Ljava/util/List<",
            "Ld0/g;",
            ">;",
            "Landroidx/compose/ui/graphics/o4;",
            ")V"
        }
    .end annotation
.end method

.method public abstract h(Landroidx/compose/ui/graphics/g4;JJJJLandroidx/compose/ui/graphics/o4;)V
.end method

.method public abstract i(Ld0/i;Landroidx/compose/ui/graphics/o4;)V
.end method

.method public abstract j()V
.end method

.method public abstract k()V
.end method

.method public abstract l(Ld0/i;I)V
.end method

.method public abstract m(JJLandroidx/compose/ui/graphics/o4;)V
.end method

.method public abstract n()V
.end method

.method public abstract o()V
.end method

.method public abstract p([F)V
.end method

.method public abstract q(Landroidx/compose/ui/graphics/Path;Landroidx/compose/ui/graphics/o4;)V
.end method

.method public abstract r(JFLandroidx/compose/ui/graphics/o4;)V
.end method

.method public abstract s(FFFFFFLandroidx/compose/ui/graphics/o4;)V
.end method
