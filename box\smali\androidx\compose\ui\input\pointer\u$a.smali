.class public final Landroidx/compose/ui/input/pointer/u$a;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/compose/ui/input/pointer/u;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation


# static fields
.field public static final synthetic a:Landroidx/compose/ui/input/pointer/u$a;

.field public static final b:Landroidx/compose/ui/input/pointer/u;

.field public static final c:Landroidx/compose/ui/input/pointer/u;

.field public static final d:Landroidx/compose/ui/input/pointer/u;

.field public static final e:Landroidx/compose/ui/input/pointer/u;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    new-instance v0, Landroidx/compose/ui/input/pointer/u$a;

    invoke-direct {v0}, Landroidx/compose/ui/input/pointer/u$a;-><init>()V

    sput-object v0, Landroidx/compose/ui/input/pointer/u$a;->a:Landroidx/compose/ui/input/pointer/u$a;

    invoke-static {}, Landroidx/compose/ui/input/pointer/x;->c()Landroidx/compose/ui/input/pointer/u;

    move-result-object v0

    sput-object v0, Landroidx/compose/ui/input/pointer/u$a;->b:Landroidx/compose/ui/input/pointer/u;

    invoke-static {}, Landroidx/compose/ui/input/pointer/x;->b()Landroidx/compose/ui/input/pointer/u;

    move-result-object v0

    sput-object v0, Landroidx/compose/ui/input/pointer/u$a;->c:Landroidx/compose/ui/input/pointer/u;

    invoke-static {}, Landroidx/compose/ui/input/pointer/x;->e()Landroidx/compose/ui/input/pointer/u;

    move-result-object v0

    sput-object v0, Landroidx/compose/ui/input/pointer/u$a;->d:Landroidx/compose/ui/input/pointer/u;

    invoke-static {}, Landroidx/compose/ui/input/pointer/x;->d()Landroidx/compose/ui/input/pointer/u;

    move-result-object v0

    sput-object v0, Landroidx/compose/ui/input/pointer/u$a;->e:Landroidx/compose/ui/input/pointer/u;

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final a()Landroidx/compose/ui/input/pointer/u;
    .locals 1

    sget-object v0, Landroidx/compose/ui/input/pointer/u$a;->b:Landroidx/compose/ui/input/pointer/u;

    return-object v0
.end method
