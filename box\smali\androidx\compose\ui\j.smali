.class public interface abstract Landroidx/compose/ui/j;
.super Ljava/lang/Object;

# interfaces
.implements Lkotlin/coroutines/CoroutineContext$Element;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Landroidx/compose/ui/j$a;,
        Landroidx/compose/ui/j$b;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation


# static fields
.field public static final c0:Landroidx/compose/ui/j$b;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    sget-object v0, Landroidx/compose/ui/j$b;->a:Landroidx/compose/ui/j$b;

    sput-object v0, Landroidx/compose/ui/j;->c0:Landroidx/compose/ui/j$b;

    return-void
.end method


# virtual methods
.method public abstract f()F
.end method
