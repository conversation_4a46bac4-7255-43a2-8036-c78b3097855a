.class public final Landroidx/compose/ui/layout/LayoutNodeSubcompositionsState$e;
.super Ljava/lang/Object;

# interfaces
.implements Landroidx/compose/ui/layout/SubcomposeLayoutState$a;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Landroidx/compose/ui/layout/LayoutNodeSubcompositionsState;->D(Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)Landroidx/compose/ui/layout/SubcomposeLayoutState$a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public synthetic a(Ljava/lang/Object;Lkotlin/jvm/functions/Function1;)V
    .locals 0

    invoke-static {p0, p1, p2}, Landroidx/compose/ui/layout/r0;->c(Landroidx/compose/ui/layout/SubcomposeLayoutState$a;Ljava/lang/Object;Lkotlin/jvm/functions/Function1;)V

    return-void
.end method

.method public synthetic b()I
    .locals 1

    invoke-static {p0}, Landroidx/compose/ui/layout/r0;->a(Landroidx/compose/ui/layout/SubcomposeLayoutState$a;)I

    move-result v0

    return v0
.end method

.method public synthetic c(IJ)V
    .locals 0

    invoke-static {p0, p1, p2, p3}, Landroidx/compose/ui/layout/r0;->b(Landroidx/compose/ui/layout/SubcomposeLayoutState$a;IJ)V

    return-void
.end method

.method public dispose()V
    .locals 0

    return-void
.end method
