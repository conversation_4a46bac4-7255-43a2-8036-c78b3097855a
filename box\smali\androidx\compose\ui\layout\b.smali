.class public interface abstract Landroidx/compose/ui/layout/b;
.super Ljava/lang/Object;

# interfaces
.implements Landroidx/compose/ui/node/x;


# annotations
.annotation runtime L<PERSON>lin/Metadata;
.end annotation


# virtual methods
.method public abstract e1(Landroidx/compose/ui/layout/k0$a;Landroidx/compose/ui/layout/m;)Z
.end method

.method public abstract q0(Landroidx/compose/ui/layout/c;Landroidx/compose/ui/layout/t;J)Landroidx/compose/ui/layout/w;
.end method

.method public abstract u0(J)Z
.end method
