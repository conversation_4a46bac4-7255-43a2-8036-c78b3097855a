.class public final Landroidx/compose/ui/layout/b0;
.super Ljava/lang/Object;

# interfaces
.implements Landroidx/compose/ui/layout/t0;


# annotations
.annotation runtime L<PERSON><PERSON>/Metadata;
.end annotation


# static fields
.field public static final a:Landroidx/compose/ui/layout/b0;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    new-instance v0, Landroidx/compose/ui/layout/b0;

    invoke-direct {v0}, Landroidx/compose/ui/layout/b0;-><init>()V

    sput-object v0, Landroidx/compose/ui/layout/b0;->a:Landroidx/compose/ui/layout/b0;

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public a(Landroidx/compose/ui/layout/t0$a;)V
    .locals 0

    invoke-virtual {p1}, Landroidx/compose/ui/layout/t0$a;->clear()V

    return-void
.end method

.method public b(<PERSON>ja<PERSON>/lang/Object;Ljava/lang/Object;)Z
    .locals 0

    const/4 p1, 0x0

    return p1
.end method
