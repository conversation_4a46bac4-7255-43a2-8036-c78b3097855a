.class public final Landroidx/compose/ui/layout/g;
.super Ljava/lang/Object;


# annotations
.annotation runtime Lkotlin/Metadata;
.end annotation


# direct methods
.method public static final synthetic a(JJ)F
    .locals 0

    invoke-static {p0, p1, p2, p3}, Landroidx/compose/ui/layout/g;->e(JJ)F

    move-result p0

    return p0
.end method

.method public static final synthetic b(JJ)F
    .locals 0

    invoke-static {p0, p1, p2, p3}, Landroidx/compose/ui/layout/g;->f(JJ)F

    move-result p0

    return p0
.end method

.method public static final synthetic c(JJ)F
    .locals 0

    invoke-static {p0, p1, p2, p3}, Landroidx/compose/ui/layout/g;->g(JJ)F

    move-result p0

    return p0
.end method

.method public static final synthetic d(JJ)F
    .locals 0

    invoke-static {p0, p1, p2, p3}, Landroidx/compose/ui/layout/g;->h(JJ)F

    move-result p0

    return p0
.end method

.method public static final e(JJ)F
    .locals 0

    invoke-static {p2, p3}, Ld0/m;->g(J)F

    move-result p2

    invoke-static {p0, p1}, Ld0/m;->g(J)F

    move-result p0

    div-float/2addr p2, p0

    return p2
.end method

.method public static final f(JJ)F
    .locals 1

    invoke-static {p0, p1, p2, p3}, Landroidx/compose/ui/layout/g;->h(JJ)F

    move-result v0

    invoke-static {p0, p1, p2, p3}, Landroidx/compose/ui/layout/g;->e(JJ)F

    move-result p0

    invoke-static {v0, p0}, Ljava/lang/Math;->max(FF)F

    move-result p0

    return p0
.end method

.method public static final g(JJ)F
    .locals 1

    invoke-static {p0, p1, p2, p3}, Landroidx/compose/ui/layout/g;->h(JJ)F

    move-result v0

    invoke-static {p0, p1, p2, p3}, Landroidx/compose/ui/layout/g;->e(JJ)F

    move-result p0

    invoke-static {v0, p0}, Ljava/lang/Math;->min(FF)F

    move-result p0

    return p0
.end method

.method public static final h(JJ)F
    .locals 0

    invoke-static {p2, p3}, Ld0/m;->i(J)F

    move-result p2

    invoke-static {p0, p1}, Ld0/m;->i(J)F

    move-result p0

    div-float/2addr p2, p0

    return p2
.end method
