.class public interface abstract Landroidx/compose/ui/layout/m;
.super Ljava/lang/Object;


# annotations
.annotation runtime L<PERSON>lin/Metadata;
.end annotation


# virtual methods
.method public abstract E()Z
.end method

.method public abstract I(J)J
.end method

.method public abstract L(Landroidx/compose/ui/layout/m;Z)Ld0/i;
.end method

.method public abstract R()Landroidx/compose/ui/layout/m;
.end method

.method public abstract Y(J)J
.end method

.method public abstract p()J
.end method

.method public abstract y(Landroidx/compose/ui/layout/m;JZ)J
.end method

.method public abstract z(Landroidx/compose/ui/layout/m;J)J
.end method
