.class public interface abstract Landroidx/compose/ui/layout/s0;
.super Ljava/lang/Object;

# interfaces
.implements Landroidx/compose/ui/layout/y;


# annotations
.annotation runtime L<PERSON><PERSON>/Metadata;
.end annotation


# virtual methods
.method public abstract J(Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/jvm/functions/Function2<",
            "-",
            "Landroidx/compose/runtime/i;",
            "-",
            "Ljava/lang/Integer;",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/util/List<",
            "Landroidx/compose/ui/layout/t;",
            ">;"
        }
    .end annotation
.end method
