.class public interface abstract Landroidx/compose/ui/modifier/h;
.super Ljava/lang/Object;

# interfaces
.implements Landroidx/compose/ui/modifier/k;
.implements Landroidx/compose/ui/node/f;


# annotations
.annotation runtime Lkotlin/Metadata;
.end annotation

.annotation build Lkotlin/jvm/internal/SourceDebugExtension;
.end annotation


# virtual methods
.method public abstract b0()Landroidx/compose/ui/modifier/f;
.end method
