.class public interface abstract Landroidx/compose/ui/node/ComposeUiNode;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Landroidx/compose/ui/node/ComposeUiNode$Companion;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation

.annotation build Lkotlin/PublishedApi;
.end annotation


# static fields
.field public static final e0:Landroidx/compose/ui/node/ComposeUiNode$Companion;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    sget-object v0, Landroidx/compose/ui/node/ComposeUiNode$Companion;->a:Landroidx/compose/ui/node/ComposeUiNode$Companion;

    sput-object v0, Landroidx/compose/ui/node/ComposeUiNode;->e0:Landroidx/compose/ui/node/ComposeUiNode$Companion;

    return-void
.end method


# virtual methods
.method public abstract b(Lv0/e;)V
.end method

.method public abstract c(Landroidx/compose/ui/unit/LayoutDirection;)V
.end method

.method public abstract d(I)V
.end method

.method public abstract e(Landroidx/compose/ui/platform/r2;)V
.end method

.method public abstract g(Landroidx/compose/ui/layout/u;)V
.end method

.method public abstract h(Landroidx/compose/ui/f;)V
.end method

.method public abstract i(Landroidx/compose/runtime/s;)V
.end method
