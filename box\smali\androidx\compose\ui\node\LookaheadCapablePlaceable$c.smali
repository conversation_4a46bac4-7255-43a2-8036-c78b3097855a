.class public final Landroidx/compose/ui/node/LookaheadCapablePlaceable$c;
.super Ljava/lang/Object;

# interfaces
.implements Landroidx/compose/ui/layout/o0;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Landroidx/compose/ui/node/LookaheadCapablePlaceable;->f1()Landroidx/compose/ui/layout/o0;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation


# instance fields
.field public final synthetic a:Landroidx/compose/ui/node/LookaheadCapablePlaceable;


# direct methods
.method public constructor <init>(Landroidx/compose/ui/node/LookaheadCapablePlaceable;)V
    .locals 0

    iput-object p1, p0, Landroidx/compose/ui/node/LookaheadCapablePlaceable$c;->a:Landroidx/compose/ui/node/LookaheadCapablePlaceable;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public synthetic H0(F)F
    .locals 0

    invoke-static {p0, p1}, Lv0/d;->b(Lv0/e;F)F

    move-result p1

    return p1
.end method

.method public L0()F
    .locals 1

    iget-object v0, p0, Landroidx/compose/ui/node/LookaheadCapablePlaceable$c;->a:Landroidx/compose/ui/node/LookaheadCapablePlaceable;

    invoke-interface {v0}, Lv0/n;->L0()F

    move-result v0

    return v0
.end method

.method public synthetic M(F)J
    .locals 2

    invoke-static {p0, p1}, Lv0/m;->b(Lv0/n;F)J

    move-result-wide v0

    return-wide v0
.end method

.method public synthetic N(J)F
    .locals 0

    invoke-static {p0, p1, p2}, Lv0/m;->a(Lv0/n;J)F

    move-result p1

    return p1
.end method

.method public synthetic O0(F)F
    .locals 0

    invoke-static {p0, p1}, Lv0/d;->d(Lv0/e;F)F

    move-result p1

    return p1
.end method

.method public synthetic W(F)J
    .locals 2

    invoke-static {p0, p1}, Lv0/d;->f(Lv0/e;F)J

    move-result-wide v0

    return-wide v0
.end method

.method public synthetic Y0(J)J
    .locals 0

    invoke-static {p0, p1, p2}, Lv0/d;->e(Lv0/e;J)J

    move-result-wide p1

    return-wide p1
.end method

.method public getDensity()F
    .locals 1

    iget-object v0, p0, Landroidx/compose/ui/node/LookaheadCapablePlaceable$c;->a:Landroidx/compose/ui/node/LookaheadCapablePlaceable;

    invoke-interface {v0}, Lv0/e;->getDensity()F

    move-result v0

    return v0
.end method

.method public synthetic j0(F)I
    .locals 0

    invoke-static {p0, p1}, Lv0/d;->a(Lv0/e;F)I

    move-result p1

    return p1
.end method

.method public synthetic p0(J)F
    .locals 0

    invoke-static {p0, p1, p2}, Lv0/d;->c(Lv0/e;J)F

    move-result p1

    return p1
.end method
