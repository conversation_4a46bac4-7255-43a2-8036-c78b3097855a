.class public final Landroidx/compose/ui/node/NodeCoordinator$c;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/compose/ui/node/NodeCoordinator;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "c"
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public synthetic constructor <init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    invoke-direct {p0}, Landroidx/compose/ui/node/NodeCoordinator$c;-><init>()V

    return-void
.end method


# virtual methods
.method public final a()Landroidx/compose/ui/node/NodeCoordinator$d;
    .locals 1

    invoke-static {}, Landroidx/compose/ui/node/NodeCoordinator;->t1()Landroidx/compose/ui/node/NodeCoordinator$d;

    move-result-object v0

    return-object v0
.end method

.method public final b()Landroidx/compose/ui/node/NodeCoordinator$d;
    .locals 1

    invoke-static {}, Landroidx/compose/ui/node/NodeCoordinator;->u1()Landroidx/compose/ui/node/NodeCoordinator$d;

    move-result-object v0

    return-object v0
.end method
