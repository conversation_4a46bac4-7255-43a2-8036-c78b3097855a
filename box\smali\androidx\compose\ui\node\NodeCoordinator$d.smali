.class public interface abstract Landroidx/compose/ui/node/NodeCoordinator$d;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/compose/ui/node/NodeCoordinator;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "d"
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation


# virtual methods
.method public abstract a()I
.end method

.method public abstract b(Landroidx/compose/ui/f$c;)Z
.end method

.method public abstract c(Landroidx/compose/ui/node/LayoutNode;JLandroidx/compose/ui/node/q;ZZ)V
.end method

.method public abstract d(Landroidx/compose/ui/node/LayoutNode;)Z
.end method
