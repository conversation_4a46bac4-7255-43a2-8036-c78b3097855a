.class public interface abstract Landroidx/compose/ui/node/a;
.super Ljava/lang/Object;

# interfaces
.implements Landroidx/compose/ui/layout/t;


# annotations
.annotation runtime L<PERSON>lin/Metadata;
.end annotation


# virtual methods
.method public abstract D()Landroidx/compose/ui/node/a;
.end method

.method public abstract H()V
.end method

.method public abstract K()Landroidx/compose/ui/node/NodeCoordinator;
.end method

.method public abstract U(Lkotlin/jvm/functions/Function1;)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Landroidx/compose/ui/node/a;",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation
.end method

.method public abstract a0()V
.end method

.method public abstract l()Z
.end method

.method public abstract m()Landroidx/compose/ui/node/AlignmentLines;
.end method

.method public abstract requestLayout()V
.end method

.method public abstract v()Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Map<",
            "Landroidx/compose/ui/layout/a;",
            "Ljava/lang/Integer;",
            ">;"
        }
    .end annotation
.end method
