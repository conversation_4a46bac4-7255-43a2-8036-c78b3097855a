.class public final Landroidx/compose/ui/node/c;
.super Ljava/lang/Object;


# annotations
.annotation runtime L<PERSON>lin/Metadata;
.end annotation

.annotation runtime Lkotlin/jvm/JvmInline;
.end annotation


# direct methods
.method public static a([I)[I
    .locals 0

    return-object p0
.end method

.method public static final b([II)I
    .locals 1

    invoke-static {p0}, Landroidx/compose/ui/node/c;->c([I)I

    move-result v0

    add-int/2addr p1, v0

    aget p0, p0, p1

    return p0
.end method

.method public static final c([I)I
    .locals 0

    array-length p0, p0

    div-int/lit8 p0, p0, 0x2

    return p0
.end method

.method public static final d([III)V
    .locals 1

    invoke-static {p0}, Landroidx/compose/ui/node/c;->c([I)I

    move-result v0

    add-int/2addr p1, v0

    aput p2, p0, p1

    return-void
.end method
