.class public final synthetic Landroidx/compose/ui/node/d1;
.super Ljava/lang/Object;


# direct methods
.method public static a(Landroidx/compose/ui/node/e1;)Z
    .locals 0

    const/4 p0, 0x0

    return p0
.end method

.method public static b(Landroidx/compose/ui/node/e1;)V
    .locals 0

    invoke-interface {p0}, Landroidx/compose/ui/node/e1;->D0()V

    return-void
.end method

.method public static c(Landroidx/compose/ui/node/e1;)V
    .locals 0

    invoke-interface {p0}, Landroidx/compose/ui/node/e1;->D0()V

    return-void
.end method

.method public static d(Landroidx/compose/ui/node/e1;)Z
    .locals 0

    const/4 p0, 0x0

    return p0
.end method
