.class public interface abstract Landroidx/compose/ui/node/e1;
.super Ljava/lang/Object;

# interfaces
.implements Landroidx/compose/ui/node/f;


# annotations
.annotation runtime L<PERSON><PERSON>/Metadata;
.end annotation


# virtual methods
.method public abstract D0()V
.end method

.method public abstract I0()V
.end method

.method public abstract R(Landroidx/compose/ui/input/pointer/q;Landroidx/compose/ui/input/pointer/PointerEventPass;J)V
.end method

.method public abstract X0()Z
.end method

.method public abstract Y()Z
.end method

.method public abstract a1()V
.end method
