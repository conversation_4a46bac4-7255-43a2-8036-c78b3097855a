.class public interface abstract Landroidx/compose/ui/node/i1;
.super Ljava/lang/Object;

# interfaces
.implements Landroidx/compose/ui/node/f;


# annotations
.annotation runtime L<PERSON><PERSON>/Metadata;
.end annotation


# virtual methods
.method public abstract U()Z
.end method

.method public abstract Z0(Landroidx/compose/ui/semantics/q;)V
.end method

.method public abstract b1()Z
.end method
