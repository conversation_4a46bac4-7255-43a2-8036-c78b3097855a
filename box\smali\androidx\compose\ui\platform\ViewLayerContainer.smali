.class public final Landroidx/compose/ui/platform/ViewLayerContainer;
.super Landroidx/compose/ui/platform/DrawChildContainer;


# annotations
.annotation runtime L<PERSON><PERSON>/Metadata;
.end annotation


# static fields
.field public static final $stable:I


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 0

    invoke-direct {p0, p1}, Landroidx/compose/ui/platform/DrawChildContainer;-><init>(Landroid/content/Context;)V

    return-void
.end method


# virtual methods
.method public dispatchDraw(Landroid/graphics/Canvas;)V
    .locals 0

    return-void
.end method

.method public final dispatchGetDisplayList()V
    .locals 0

    return-void
.end method
