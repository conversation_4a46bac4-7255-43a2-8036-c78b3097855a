.class public final synthetic Landroidx/compose/ui/platform/a3;
.super Ljava/lang/Object;

# interfaces
.implements Landroidx/compose/ui/platform/b3;


# direct methods
.method public synthetic constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final a(Landroid/view/View;)Landroidx/compose/runtime/Recomposer;
    .locals 0

    invoke-static {p1}, Landroidx/compose/ui/platform/b3$a;->a(Landroid/view/View;)Landroidx/compose/runtime/Recomposer;

    move-result-object p1

    return-object p1
.end method
