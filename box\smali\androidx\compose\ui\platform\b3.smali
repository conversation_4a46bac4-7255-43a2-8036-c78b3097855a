.class public interface abstract Landroidx/compose/ui/platform/b3;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Landroidx/compose/ui/platform/b3$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation


# static fields
.field public static final a:Landroidx/compose/ui/platform/b3$a;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    sget-object v0, Landroidx/compose/ui/platform/b3$a;->a:Landroidx/compose/ui/platform/b3$a;

    sput-object v0, Landroidx/compose/ui/platform/b3;->a:Landroidx/compose/ui/platform/b3$a;

    return-void
.end method


# virtual methods
.method public abstract a(Landroid/view/View;)Landroidx/compose/runtime/Recomposer;
.end method
