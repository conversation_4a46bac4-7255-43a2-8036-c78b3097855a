.class public interface abstract Landroidx/compose/ui/platform/w2;
.super Ljava/lang/Object;

# interfaces
.implements Landroidx/compose/ui/node/g1;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Landroidx/compose/ui/platform/w2$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation


# static fields
.field public static final h0:Landroidx/compose/ui/platform/w2$a;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    sget-object v0, Landroidx/compose/ui/platform/w2$a;->a:Landroidx/compose/ui/platform/w2$a;

    sput-object v0, Landroidx/compose/ui/platform/w2;->h0:Landroidx/compose/ui/platform/w2$a;

    return-void
.end method
