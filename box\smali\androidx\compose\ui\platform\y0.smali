.class public interface abstract Landroidx/compose/ui/platform/y0;
.super Ljava/lang/Object;


# annotations
.annotation runtime L<PERSON><PERSON>/Metadata;
.end annotation


# virtual methods
.method public abstract A(I)V
.end method

.method public abstract B()I
.end method

.method public abstract C(F)V
.end method

.method public abstract D(F)V
.end method

.method public abstract E(Landroid/graphics/Outline;)V
.end method

.method public abstract F(I)V
.end method

.method public abstract G(Z)V
.end method

.method public abstract H(Landroidx/compose/ui/graphics/p1;Landroidx/compose/ui/graphics/Path;Lkotlin/jvm/functions/Function1;)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroidx/compose/ui/graphics/p1;",
            "Landroidx/compose/ui/graphics/Path;",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Landroidx/compose/ui/graphics/o1;",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation
.end method

.method public abstract I(I)V
.end method

.method public abstract J()F
.end method

.method public abstract a()F
.end method

.method public abstract b(F)V
.end method

.method public abstract c(F)V
.end method

.method public abstract d(F)V
.end method

.method public abstract e(Landroidx/compose/ui/graphics/y4;)V
.end method

.method public abstract f(F)V
.end method

.method public abstract g(F)V
.end method

.method public abstract getHeight()I
.end method

.method public abstract getWidth()I
.end method

.method public abstract h(F)V
.end method

.method public abstract i(F)V
.end method

.method public abstract j(F)V
.end method

.method public abstract k(F)V
.end method

.method public abstract l()I
.end method

.method public abstract m()V
.end method

.method public abstract n(I)V
.end method

.method public abstract o()I
.end method

.method public abstract p()Z
.end method

.method public abstract q(Landroid/graphics/Canvas;)V
.end method

.method public abstract r(Z)V
.end method

.method public abstract s(IIII)Z
.end method

.method public abstract t(F)V
.end method

.method public abstract u(I)V
.end method

.method public abstract v()Z
.end method

.method public abstract w()I
.end method

.method public abstract x()Z
.end method

.method public abstract y(Z)Z
.end method

.method public abstract z(Landroid/graphics/Matrix;)V
.end method
