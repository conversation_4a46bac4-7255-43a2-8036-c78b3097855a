.class public final Landroidx/compose/ui/text/font/c0$a$a;
.super Ljava/lang/Object;

# interfaces
.implements Landroidx/compose/ui/text/font/c0;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/compose/ui/text/font/c0$a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public synthetic a(Landroidx/compose/ui/text/font/u;)Landroidx/compose/ui/text/font/u;
    .locals 0

    invoke-static {p0, p1}, Landroidx/compose/ui/text/font/b0;->d(Landroidx/compose/ui/text/font/c0;Landroidx/compose/ui/text/font/u;)Landroidx/compose/ui/text/font/u;

    move-result-object p1

    return-object p1
.end method

.method public synthetic b(I)I
    .locals 0

    invoke-static {p0, p1}, Landroidx/compose/ui/text/font/b0;->b(Landroidx/compose/ui/text/font/c0;I)I

    move-result p1

    return p1
.end method

.method public synthetic c(I)I
    .locals 0

    invoke-static {p0, p1}, Landroidx/compose/ui/text/font/b0;->c(Landroidx/compose/ui/text/font/c0;I)I

    move-result p1

    return p1
.end method

.method public synthetic d(Landroidx/compose/ui/text/font/i;)Landroidx/compose/ui/text/font/i;
    .locals 0

    invoke-static {p0, p1}, Landroidx/compose/ui/text/font/b0;->a(Landroidx/compose/ui/text/font/c0;Landroidx/compose/ui/text/font/i;)Landroidx/compose/ui/text/font/i;

    move-result-object p1

    return-object p1
.end method
