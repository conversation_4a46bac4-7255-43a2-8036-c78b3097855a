.class public interface abstract Landroidx/compose/ui/text/font/c0;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Landroidx/compose/ui/text/font/c0$a;
    }
.end annotation

.annotation runtime L<PERSON>lin/Metadata;
.end annotation


# static fields
.field public static final a:Landroidx/compose/ui/text/font/c0$a;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    sget-object v0, Landroidx/compose/ui/text/font/c0$a;->a:Landroidx/compose/ui/text/font/c0$a;

    sput-object v0, Landroidx/compose/ui/text/font/c0;->a:Landroidx/compose/ui/text/font/c0$a;

    return-void
.end method


# virtual methods
.method public abstract a(Landroidx/compose/ui/text/font/u;)Landroidx/compose/ui/text/font/u;
.end method

.method public abstract b(I)I
.end method

.method public abstract c(I)I
.end method

.method public abstract d(Landroidx/compose/ui/text/font/i;)Landroidx/compose/ui/text/font/i;
.end method
