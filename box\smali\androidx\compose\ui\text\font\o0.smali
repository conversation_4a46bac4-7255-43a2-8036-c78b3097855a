.class public interface abstract Landroidx/compose/ui/text/font/o0;
.super Ljava/lang/Object;

# interfaces
.implements Landroidx/compose/runtime/f3;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Landroidx/compose/ui/text/font/o0$a;,
        Landroidx/compose/ui/text/font/o0$b;
    }
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Landroidx/compose/runtime/f3<",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation


# virtual methods
.method public abstract e()Z
.end method
