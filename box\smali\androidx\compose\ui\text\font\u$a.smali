.class public final Landroidx/compose/ui/text/font/u$a;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/compose/ui/text/font/u;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public synthetic constructor <init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    invoke-direct {p0}, Landroidx/compose/ui/text/font/u$a;-><init>()V

    return-void
.end method


# virtual methods
.method public final a()Landroidx/compose/ui/text/font/u;
    .locals 1

    invoke-static {}, Landroidx/compose/ui/text/font/u;->a()Landroidx/compose/ui/text/font/u;

    move-result-object v0

    return-object v0
.end method

.method public final b()Landroidx/compose/ui/text/font/u;
    .locals 1

    invoke-static {}, Landroidx/compose/ui/text/font/u;->b()Landroidx/compose/ui/text/font/u;

    move-result-object v0

    return-object v0
.end method

.method public final c()Landroidx/compose/ui/text/font/u;
    .locals 1

    invoke-static {}, Landroidx/compose/ui/text/font/u;->e()Landroidx/compose/ui/text/font/u;

    move-result-object v0

    return-object v0
.end method

.method public final d()Landroidx/compose/ui/text/font/u;
    .locals 1

    invoke-static {}, Landroidx/compose/ui/text/font/u;->f()Landroidx/compose/ui/text/font/u;

    move-result-object v0

    return-object v0
.end method

.method public final e()Landroidx/compose/ui/text/font/u;
    .locals 1

    invoke-static {}, Landroidx/compose/ui/text/font/u;->g()Landroidx/compose/ui/text/font/u;

    move-result-object v0

    return-object v0
.end method
