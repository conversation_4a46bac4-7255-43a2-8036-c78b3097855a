.class public final Landroidx/compose/ui/text/font/u;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/lang/Comparable;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Landroidx/compose/ui/text/font/u$a;
    }
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Ljava/lang/Comparable<",
        "Landroidx/compose/ui/text/font/u;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation


# static fields
.field public static final b:Landroidx/compose/ui/text/font/u$a;

.field public static final c:Landroidx/compose/ui/text/font/u;

.field public static final d:Landroidx/compose/ui/text/font/u;

.field public static final e:Landroidx/compose/ui/text/font/u;

.field public static final f:Landroidx/compose/ui/text/font/u;

.field public static final g:Landroidx/compose/ui/text/font/u;

.field public static final h:Landroidx/compose/ui/text/font/u;

.field public static final i:Landroidx/compose/ui/text/font/u;

.field public static final j:Landroidx/compose/ui/text/font/u;

.field public static final k:Landroidx/compose/ui/text/font/u;

.field public static final l:Landroidx/compose/ui/text/font/u;

.field public static final m:Landroidx/compose/ui/text/font/u;

.field public static final n:Landroidx/compose/ui/text/font/u;

.field public static final o:Landroidx/compose/ui/text/font/u;

.field public static final p:Landroidx/compose/ui/text/font/u;

.field public static final q:Landroidx/compose/ui/text/font/u;

.field public static final r:Landroidx/compose/ui/text/font/u;

.field public static final s:Landroidx/compose/ui/text/font/u;

.field public static final t:Landroidx/compose/ui/text/font/u;

.field public static final u:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Landroidx/compose/ui/text/font/u;",
            ">;"
        }
    .end annotation
.end field


# instance fields
.field public final a:I


# direct methods
.method static constructor <clinit>()V
    .locals 11

    new-instance v0, Landroidx/compose/ui/text/font/u$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Landroidx/compose/ui/text/font/u$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Landroidx/compose/ui/text/font/u;->b:Landroidx/compose/ui/text/font/u$a;

    new-instance v0, Landroidx/compose/ui/text/font/u;

    const/16 v1, 0x64

    invoke-direct {v0, v1}, Landroidx/compose/ui/text/font/u;-><init>(I)V

    sput-object v0, Landroidx/compose/ui/text/font/u;->c:Landroidx/compose/ui/text/font/u;

    new-instance v1, Landroidx/compose/ui/text/font/u;

    const/16 v2, 0xc8

    invoke-direct {v1, v2}, Landroidx/compose/ui/text/font/u;-><init>(I)V

    sput-object v1, Landroidx/compose/ui/text/font/u;->d:Landroidx/compose/ui/text/font/u;

    new-instance v2, Landroidx/compose/ui/text/font/u;

    const/16 v3, 0x12c

    invoke-direct {v2, v3}, Landroidx/compose/ui/text/font/u;-><init>(I)V

    sput-object v2, Landroidx/compose/ui/text/font/u;->e:Landroidx/compose/ui/text/font/u;

    new-instance v3, Landroidx/compose/ui/text/font/u;

    const/16 v4, 0x190

    invoke-direct {v3, v4}, Landroidx/compose/ui/text/font/u;-><init>(I)V

    sput-object v3, Landroidx/compose/ui/text/font/u;->f:Landroidx/compose/ui/text/font/u;

    new-instance v4, Landroidx/compose/ui/text/font/u;

    const/16 v5, 0x1f4

    invoke-direct {v4, v5}, Landroidx/compose/ui/text/font/u;-><init>(I)V

    sput-object v4, Landroidx/compose/ui/text/font/u;->g:Landroidx/compose/ui/text/font/u;

    new-instance v5, Landroidx/compose/ui/text/font/u;

    const/16 v6, 0x258

    invoke-direct {v5, v6}, Landroidx/compose/ui/text/font/u;-><init>(I)V

    sput-object v5, Landroidx/compose/ui/text/font/u;->h:Landroidx/compose/ui/text/font/u;

    new-instance v6, Landroidx/compose/ui/text/font/u;

    const/16 v7, 0x2bc

    invoke-direct {v6, v7}, Landroidx/compose/ui/text/font/u;-><init>(I)V

    sput-object v6, Landroidx/compose/ui/text/font/u;->i:Landroidx/compose/ui/text/font/u;

    new-instance v7, Landroidx/compose/ui/text/font/u;

    const/16 v8, 0x320

    invoke-direct {v7, v8}, Landroidx/compose/ui/text/font/u;-><init>(I)V

    sput-object v7, Landroidx/compose/ui/text/font/u;->j:Landroidx/compose/ui/text/font/u;

    new-instance v8, Landroidx/compose/ui/text/font/u;

    const/16 v9, 0x384

    invoke-direct {v8, v9}, Landroidx/compose/ui/text/font/u;-><init>(I)V

    sput-object v8, Landroidx/compose/ui/text/font/u;->k:Landroidx/compose/ui/text/font/u;

    sput-object v0, Landroidx/compose/ui/text/font/u;->l:Landroidx/compose/ui/text/font/u;

    sput-object v1, Landroidx/compose/ui/text/font/u;->m:Landroidx/compose/ui/text/font/u;

    sput-object v2, Landroidx/compose/ui/text/font/u;->n:Landroidx/compose/ui/text/font/u;

    sput-object v3, Landroidx/compose/ui/text/font/u;->o:Landroidx/compose/ui/text/font/u;

    sput-object v4, Landroidx/compose/ui/text/font/u;->p:Landroidx/compose/ui/text/font/u;

    sput-object v5, Landroidx/compose/ui/text/font/u;->q:Landroidx/compose/ui/text/font/u;

    sput-object v6, Landroidx/compose/ui/text/font/u;->r:Landroidx/compose/ui/text/font/u;

    sput-object v7, Landroidx/compose/ui/text/font/u;->s:Landroidx/compose/ui/text/font/u;

    sput-object v8, Landroidx/compose/ui/text/font/u;->t:Landroidx/compose/ui/text/font/u;

    const/16 v9, 0x9

    new-array v9, v9, [Landroidx/compose/ui/text/font/u;

    const/4 v10, 0x0

    aput-object v0, v9, v10

    const/4 v0, 0x1

    aput-object v1, v9, v0

    const/4 v0, 0x2

    aput-object v2, v9, v0

    const/4 v0, 0x3

    aput-object v3, v9, v0

    const/4 v0, 0x4

    aput-object v4, v9, v0

    const/4 v0, 0x5

    aput-object v5, v9, v0

    const/4 v0, 0x6

    aput-object v6, v9, v0

    const/4 v0, 0x7

    aput-object v7, v9, v0

    const/16 v0, 0x8

    aput-object v8, v9, v0

    invoke-static {v9}, Lkotlin/collections/CollectionsKt;->o([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v0

    sput-object v0, Landroidx/compose/ui/text/font/u;->u:Ljava/util/List;

    return-void
.end method

.method public constructor <init>(I)V
    .locals 2

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput p1, p0, Landroidx/compose/ui/text/font/u;->a:I

    const/4 v0, 0x1

    if-gt v0, p1, :cond_0

    const/16 v0, 0x3e9

    if-ge p1, v0, :cond_0

    return-void

    :cond_0
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "Font weight can be in range [1, 1000]. Current value: "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    new-instance v0, Ljava/lang/IllegalArgumentException;

    invoke-virtual {p1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-direct {v0, p1}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw v0
.end method

.method public static final synthetic a()Landroidx/compose/ui/text/font/u;
    .locals 1

    sget-object v0, Landroidx/compose/ui/text/font/u;->o:Landroidx/compose/ui/text/font/u;

    return-object v0
.end method

.method public static final synthetic b()Landroidx/compose/ui/text/font/u;
    .locals 1

    sget-object v0, Landroidx/compose/ui/text/font/u;->f:Landroidx/compose/ui/text/font/u;

    return-object v0
.end method

.method public static final synthetic e()Landroidx/compose/ui/text/font/u;
    .locals 1

    sget-object v0, Landroidx/compose/ui/text/font/u;->g:Landroidx/compose/ui/text/font/u;

    return-object v0
.end method

.method public static final synthetic f()Landroidx/compose/ui/text/font/u;
    .locals 1

    sget-object v0, Landroidx/compose/ui/text/font/u;->h:Landroidx/compose/ui/text/font/u;

    return-object v0
.end method

.method public static final synthetic g()Landroidx/compose/ui/text/font/u;
    .locals 1

    sget-object v0, Landroidx/compose/ui/text/font/u;->i:Landroidx/compose/ui/text/font/u;

    return-object v0
.end method


# virtual methods
.method public bridge synthetic compareTo(Ljava/lang/Object;)I
    .locals 0

    check-cast p1, Landroidx/compose/ui/text/font/u;

    invoke-virtual {p0, p1}, Landroidx/compose/ui/text/font/u;->h(Landroidx/compose/ui/text/font/u;)I

    move-result p1

    return p1
.end method

.method public equals(Ljava/lang/Object;)Z
    .locals 3

    const/4 v0, 0x1

    if-ne p0, p1, :cond_0

    return v0

    :cond_0
    instance-of v1, p1, Landroidx/compose/ui/text/font/u;

    const/4 v2, 0x0

    if-nez v1, :cond_1

    return v2

    :cond_1
    iget v1, p0, Landroidx/compose/ui/text/font/u;->a:I

    check-cast p1, Landroidx/compose/ui/text/font/u;

    iget p1, p1, Landroidx/compose/ui/text/font/u;->a:I

    if-eq v1, p1, :cond_2

    return v2

    :cond_2
    return v0
.end method

.method public h(Landroidx/compose/ui/text/font/u;)I
    .locals 1

    iget v0, p0, Landroidx/compose/ui/text/font/u;->a:I

    iget p1, p1, Landroidx/compose/ui/text/font/u;->a:I

    invoke-static {v0, p1}, Lkotlin/jvm/internal/Intrinsics;->i(II)I

    move-result p1

    return p1
.end method

.method public hashCode()I
    .locals 1

    iget v0, p0, Landroidx/compose/ui/text/font/u;->a:I

    return v0
.end method

.method public final i()I
    .locals 1

    iget v0, p0, Landroidx/compose/ui/text/font/u;->a:I

    return v0
.end method

.method public toString()Ljava/lang/String;
    .locals 2

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "FontWeight(weight="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget v1, p0, Landroidx/compose/ui/text/font/u;->a:I

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const/16 v1, 0x29

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
