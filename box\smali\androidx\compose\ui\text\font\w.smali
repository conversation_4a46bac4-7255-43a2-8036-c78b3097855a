.class public final Landroidx/compose/ui/text/font/w;
.super Ljava/lang/Object;


# annotations
.annotation build Landroidx/annotation/RequiresApi;
    value = 0x1f
.end annotation

.annotation runtime L<PERSON>lin/Metadata;
.end annotation


# static fields
.field public static final a:Landroidx/compose/ui/text/font/w;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    new-instance v0, Landroidx/compose/ui/text/font/w;

    invoke-direct {v0}, Landroidx/compose/ui/text/font/w;-><init>()V

    sput-object v0, Landroidx/compose/ui/text/font/w;->a:Landroidx/compose/ui/text/font/w;

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final a(Landroid/content/Context;)I
    .locals 0
    .annotation build Landroidx/annotation/RequiresApi;
        value = 0x1f
    .end annotation

    invoke-virtual {p1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    move-result-object p1

    invoke-virtual {p1}, Landroid/content/res/Resources;->getConfiguration()Landroid/content/res/Configuration;

    move-result-object p1

    invoke-static {p1}, Landroidx/compose/ui/platform/k;->a(Landroid/content/res/Configuration;)I

    move-result p1

    return p1
.end method
