.class public final Landroidx/compose/ui/text/input/o$a;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/compose/ui/text/input/o;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public synthetic constructor <init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    invoke-direct {p0}, Landroidx/compose/ui/text/input/o$a;-><init>()V

    return-void
.end method


# virtual methods
.method public final a()I
    .locals 1

    invoke-static {}, Landroidx/compose/ui/text/input/o;->a()I

    move-result v0

    return v0
.end method

.method public final b()I
    .locals 1

    invoke-static {}, Landroidx/compose/ui/text/input/o;->b()I

    move-result v0

    return v0
.end method

.method public final c()I
    .locals 1

    invoke-static {}, Landroidx/compose/ui/text/input/o;->c()I

    move-result v0

    return v0
.end method

.method public final d()I
    .locals 1

    invoke-static {}, Landroidx/compose/ui/text/input/o;->d()I

    move-result v0

    return v0
.end method

.method public final e()I
    .locals 1

    invoke-static {}, Landroidx/compose/ui/text/input/o;->e()I

    move-result v0

    return v0
.end method

.method public final f()I
    .locals 1

    invoke-static {}, Landroidx/compose/ui/text/input/o;->f()I

    move-result v0

    return v0
.end method

.method public final g()I
    .locals 1

    invoke-static {}, Landroidx/compose/ui/text/input/o;->g()I

    move-result v0

    return v0
.end method

.method public final h()I
    .locals 1

    invoke-static {}, Landroidx/compose/ui/text/input/o;->h()I

    move-result v0

    return v0
.end method
