.class public interface abstract Landroidx/compose/ui/text/input/q;
.super Ljava/lang/Object;


# annotations
.annotation runtime Lkot<PERSON>/Deprecated;
.end annotation

.annotation runtime L<PERSON>lin/Metadata;
.end annotation


# virtual methods
.method public abstract a(Landroid/view/KeyEvent;)V
.end method

.method public abstract b(Landroidx/compose/ui/text/input/y;)V
.end method

.method public abstract c(ZZZZZZ)V
.end method

.method public abstract d(I)V
.end method

.method public abstract e(Ljava/util/List;)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "+",
            "Landroidx/compose/ui/text/input/m;",
            ">;)V"
        }
    .end annotation
.end method
