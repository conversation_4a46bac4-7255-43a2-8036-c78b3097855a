.class public interface abstract Landroidx/compose/ui/text/l;
.super Ljava/lang/Object;


# annotations
.annotation runtime Lkotlin/Metadata;
.end annotation


# virtual methods
.method public abstract a()F
.end method

.method public abstract b(Landroidx/compose/ui/graphics/o1;JLandroidx/compose/ui/graphics/b5;Landroidx/compose/ui/text/style/i;Le0/h;I)V
.end method

.method public abstract c(I)Landroidx/compose/ui/text/style/ResolvedTextDirection;
.end method

.method public abstract d(I)F
.end method

.method public abstract e(I)Ld0/i;
.end method

.method public abstract f()F
.end method

.method public abstract g(I)I
.end method

.method public abstract getHeight()F
.end method

.method public abstract getWidth()F
.end method

.method public abstract h(IZ)I
.end method

.method public abstract i()I
.end method

.method public abstract j(I)F
.end method

.method public abstract k()Z
.end method

.method public abstract l(F)I
.end method

.method public abstract m(I)F
.end method

.method public abstract n(J[FI)V
.end method

.method public abstract o()F
.end method

.method public abstract p(I)I
.end method

.method public abstract q(I)Landroidx/compose/ui/text/style/ResolvedTextDirection;
.end method

.method public abstract r(I)F
.end method

.method public abstract s(I)Ld0/i;
.end method

.method public abstract t()Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Ld0/i;",
            ">;"
        }
    .end annotation
.end method

.method public abstract u(Landroidx/compose/ui/graphics/o1;Landroidx/compose/ui/graphics/l1;FLandroidx/compose/ui/graphics/b5;Landroidx/compose/ui/text/style/i;Le0/h;I)V
.end method
