.class public interface abstract Landroidx/compose/ui/text/style/l;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Landroidx/compose/ui/text/style/l$a;,
        Landroidx/compose/ui/text/style/l$b;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation


# static fields
.field public static final a:Landroidx/compose/ui/text/style/l$a;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    sget-object v0, Landroidx/compose/ui/text/style/l$a;->a:Landroidx/compose/ui/text/style/l$a;

    sput-object v0, Landroidx/compose/ui/text/style/l;->a:Landroidx/compose/ui/text/style/l$a;

    return-void
.end method


# virtual methods
.method public abstract a()F
.end method

.method public abstract b(Lkotlin/jvm/functions/Function0;)Landroidx/compose/ui/text/style/l;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function0<",
            "+",
            "Landroidx/compose/ui/text/style/l;",
            ">;)",
            "Landroidx/compose/ui/text/style/l;"
        }
    .end annotation
.end method

.method public abstract c()J
.end method

.method public abstract d(Landroidx/compose/ui/text/style/l;)Landroidx/compose/ui/text/style/l;
.end method

.method public abstract e()Landroidx/compose/ui/graphics/l1;
.end method
