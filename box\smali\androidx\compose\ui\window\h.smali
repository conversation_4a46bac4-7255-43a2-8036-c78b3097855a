.class public Landroidx/compose/ui/window/h;
.super Ljava/lang/Object;

# interfaces
.implements Landroidx/compose/ui/window/f;


# annotations
.annotation runtime L<PERSON><PERSON>/Metadata;
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public a(Landroid/view/View;Landroid/graphics/Rect;)V
    .locals 0

    invoke-virtual {p1, p2}, Landroid/view/View;->getWindowVisibleDisplayFrame(Landroid/graphics/Rect;)V

    return-void
.end method
