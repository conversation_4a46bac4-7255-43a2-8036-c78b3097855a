.class public interface abstract Landroidx/constraintlayout/core/b$a;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/constraintlayout/core/b;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "a"
.end annotation


# virtual methods
.method public abstract a(Landroidx/constraintlayout/core/SolverVariable;)Z
.end method

.method public abstract b(I)Landroidx/constraintlayout/core/SolverVariable;
.end method

.method public abstract c(Landroidx/constraintlayout/core/SolverVariable;FZ)V
.end method

.method public abstract clear()V
.end method

.method public abstract d()V
.end method

.method public abstract e(Landroidx/constraintlayout/core/SolverVariable;Z)F
.end method

.method public abstract f(Landroidx/constraintlayout/core/b;Z)F
.end method

.method public abstract g(Landroidx/constraintlayout/core/SolverVariable;F)V
.end method

.method public abstract getCurrentSize()I
.end method

.method public abstract h(I)F
.end method

.method public abstract i(Landroidx/constraintlayout/core/SolverVariable;)F
.end method

.method public abstract j(F)V
.end method
