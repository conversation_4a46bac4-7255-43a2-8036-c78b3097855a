.class public Landroidx/constraintlayout/core/motion/utils/KeyCycleOscillator$b;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/constraintlayout/core/motion/utils/KeyCycleOscillator;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "b"
.end annotation


# instance fields
.field public a:I

.field public b:F

.field public c:F

.field public d:F

.field public e:F


# direct methods
.method public constructor <init>(IFFFF)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput p1, p0, Landroidx/constraintlayout/core/motion/utils/KeyCycleOscillator$b;->a:I

    iput p5, p0, Landroidx/constraintlayout/core/motion/utils/KeyCycleOscillator$b;->b:F

    iput p3, p0, Landroidx/constraintlayout/core/motion/utils/KeyCycleOscillator$b;->c:F

    iput p2, p0, Landroidx/constraintlayout/core/motion/utils/KeyCycleOscillator$b;->d:F

    iput p4, p0, Landroidx/constraintlayout/core/motion/utils/KeyCycleOscillator$b;->e:F

    return-void
.end method
