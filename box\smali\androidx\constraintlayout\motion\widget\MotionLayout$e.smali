.class public interface abstract Landroidx/constraintlayout/motion/widget/MotionLayout$e;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/constraintlayout/motion/widget/MotionLayout;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "e"
.end annotation


# virtual methods
.method public abstract a(Landroid/view/MotionEvent;)V
.end method

.method public abstract b()F
.end method

.method public abstract c()F
.end method

.method public abstract d(I)V
.end method

.method public abstract recycle()V
.end method
