.class public interface abstract Landroidx/constraintlayout/motion/widget/MotionLayout$h;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/constraintlayout/motion/widget/MotionLayout;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "h"
.end annotation


# virtual methods
.method public abstract onTransitionChange(Landroidx/constraintlayout/motion/widget/MotionLayout;IIF)V
.end method

.method public abstract onTransitionCompleted(Landroidx/constraintlayout/motion/widget/MotionLayout;I)V
.end method

.method public abstract onTransitionStarted(Landroidx/constraintlayout/motion/widget/MotionLayout;II)V
.end method

.method public abstract onTransitionTrigger(Landroidx/constraintlayout/motion/widget/MotionLayout;IZF)V
.end method
