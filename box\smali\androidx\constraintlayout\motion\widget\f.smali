.class public Landroidx/constraintlayout/motion/widget/f;
.super Landroidx/constraintlayout/motion/widget/d;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Landroidx/constraintlayout/motion/widget/f$a;
    }
.end annotation


# instance fields
.field public g:Ljava/lang/String;

.field public h:I

.field public i:I

.field public j:Ljava/lang/String;

.field public k:F

.field public l:F

.field public m:F

.field public n:F

.field public o:I

.field public p:F

.field public q:F

.field public r:F

.field public s:F

.field public t:F

.field public u:F

.field public v:F

.field public w:F

.field public x:F

.field public y:F

.field public z:F


# direct methods
.method public constructor <init>()V
    .locals 3

    invoke-direct {p0}, Landroidx/constraintlayout/motion/widget/d;-><init>()V

    const/4 v0, 0x0

    iput-object v0, p0, Landroidx/constraintlayout/motion/widget/f;->g:Ljava/lang/String;

    const/4 v1, 0x0

    iput v1, p0, Landroidx/constraintlayout/motion/widget/f;->h:I

    const/4 v1, -0x1

    iput v1, p0, Landroidx/constraintlayout/motion/widget/f;->i:I

    iput-object v0, p0, Landroidx/constraintlayout/motion/widget/f;->j:Ljava/lang/String;

    const/high16 v0, 0x7fc00000    # Float.NaN

    iput v0, p0, Landroidx/constraintlayout/motion/widget/f;->k:F

    const/4 v2, 0x0

    iput v2, p0, Landroidx/constraintlayout/motion/widget/f;->l:F

    iput v2, p0, Landroidx/constraintlayout/motion/widget/f;->m:F

    iput v0, p0, Landroidx/constraintlayout/motion/widget/f;->n:F

    iput v1, p0, Landroidx/constraintlayout/motion/widget/f;->o:I

    iput v0, p0, Landroidx/constraintlayout/motion/widget/f;->p:F

    iput v0, p0, Landroidx/constraintlayout/motion/widget/f;->q:F

    iput v0, p0, Landroidx/constraintlayout/motion/widget/f;->r:F

    iput v0, p0, Landroidx/constraintlayout/motion/widget/f;->s:F

    iput v0, p0, Landroidx/constraintlayout/motion/widget/f;->t:F

    iput v0, p0, Landroidx/constraintlayout/motion/widget/f;->u:F

    iput v0, p0, Landroidx/constraintlayout/motion/widget/f;->v:F

    iput v0, p0, Landroidx/constraintlayout/motion/widget/f;->w:F

    iput v0, p0, Landroidx/constraintlayout/motion/widget/f;->x:F

    iput v0, p0, Landroidx/constraintlayout/motion/widget/f;->y:F

    iput v0, p0, Landroidx/constraintlayout/motion/widget/f;->z:F

    const/4 v0, 0x4

    iput v0, p0, Landroidx/constraintlayout/motion/widget/d;->d:I

    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    iput-object v0, p0, Landroidx/constraintlayout/motion/widget/d;->e:Ljava/util/HashMap;

    return-void
.end method

.method public static synthetic A(Landroidx/constraintlayout/motion/widget/f;F)F
    .locals 0

    iput p1, p0, Landroidx/constraintlayout/motion/widget/f;->x:F

    return p1
.end method

.method public static synthetic B(Landroidx/constraintlayout/motion/widget/f;)F
    .locals 0

    iget p0, p0, Landroidx/constraintlayout/motion/widget/f;->y:F

    return p0
.end method

.method public static synthetic C(Landroidx/constraintlayout/motion/widget/f;F)F
    .locals 0

    iput p1, p0, Landroidx/constraintlayout/motion/widget/f;->y:F

    return p1
.end method

.method public static synthetic D(Landroidx/constraintlayout/motion/widget/f;)F
    .locals 0

    iget p0, p0, Landroidx/constraintlayout/motion/widget/f;->z:F

    return p0
.end method

.method public static synthetic E(Landroidx/constraintlayout/motion/widget/f;F)F
    .locals 0

    iput p1, p0, Landroidx/constraintlayout/motion/widget/f;->z:F

    return p1
.end method

.method public static synthetic F(Landroidx/constraintlayout/motion/widget/f;)F
    .locals 0

    iget p0, p0, Landroidx/constraintlayout/motion/widget/f;->n:F

    return p0
.end method

.method public static synthetic G(Landroidx/constraintlayout/motion/widget/f;F)F
    .locals 0

    iput p1, p0, Landroidx/constraintlayout/motion/widget/f;->n:F

    return p1
.end method

.method public static synthetic H(Landroidx/constraintlayout/motion/widget/f;)I
    .locals 0

    iget p0, p0, Landroidx/constraintlayout/motion/widget/f;->h:I

    return p0
.end method

.method public static synthetic I(Landroidx/constraintlayout/motion/widget/f;)F
    .locals 0

    iget p0, p0, Landroidx/constraintlayout/motion/widget/f;->m:F

    return p0
.end method

.method public static synthetic J(Landroidx/constraintlayout/motion/widget/f;F)F
    .locals 0

    iput p1, p0, Landroidx/constraintlayout/motion/widget/f;->m:F

    return p1
.end method

.method public static synthetic K(Landroidx/constraintlayout/motion/widget/f;I)I
    .locals 0

    iput p1, p0, Landroidx/constraintlayout/motion/widget/f;->h:I

    return p1
.end method

.method public static synthetic L(Landroidx/constraintlayout/motion/widget/f;Ljava/lang/String;)Ljava/lang/String;
    .locals 0

    iput-object p1, p0, Landroidx/constraintlayout/motion/widget/f;->j:Ljava/lang/String;

    return-object p1
.end method

.method public static synthetic M(Landroidx/constraintlayout/motion/widget/f;)I
    .locals 0

    iget p0, p0, Landroidx/constraintlayout/motion/widget/f;->i:I

    return p0
.end method

.method public static synthetic N(Landroidx/constraintlayout/motion/widget/f;I)I
    .locals 0

    iput p1, p0, Landroidx/constraintlayout/motion/widget/f;->i:I

    return p1
.end method

.method public static synthetic O(Landroidx/constraintlayout/motion/widget/f;)F
    .locals 0

    iget p0, p0, Landroidx/constraintlayout/motion/widget/f;->k:F

    return p0
.end method

.method public static synthetic P(Landroidx/constraintlayout/motion/widget/f;F)F
    .locals 0

    iput p1, p0, Landroidx/constraintlayout/motion/widget/f;->k:F

    return p1
.end method

.method public static synthetic Q(Landroidx/constraintlayout/motion/widget/f;)F
    .locals 0

    iget p0, p0, Landroidx/constraintlayout/motion/widget/f;->l:F

    return p0
.end method

.method public static synthetic R(Landroidx/constraintlayout/motion/widget/f;F)F
    .locals 0

    iput p1, p0, Landroidx/constraintlayout/motion/widget/f;->l:F

    return p1
.end method

.method public static synthetic S(Landroidx/constraintlayout/motion/widget/f;)I
    .locals 0

    iget p0, p0, Landroidx/constraintlayout/motion/widget/f;->o:I

    return p0
.end method

.method public static synthetic T(Landroidx/constraintlayout/motion/widget/f;I)I
    .locals 0

    iput p1, p0, Landroidx/constraintlayout/motion/widget/f;->o:I

    return p1
.end method

.method public static synthetic U(Landroidx/constraintlayout/motion/widget/f;)F
    .locals 0

    iget p0, p0, Landroidx/constraintlayout/motion/widget/f;->p:F

    return p0
.end method

.method public static synthetic V(Landroidx/constraintlayout/motion/widget/f;F)F
    .locals 0

    iput p1, p0, Landroidx/constraintlayout/motion/widget/f;->p:F

    return p1
.end method

.method public static synthetic W(Landroidx/constraintlayout/motion/widget/f;)F
    .locals 0

    iget p0, p0, Landroidx/constraintlayout/motion/widget/f;->q:F

    return p0
.end method

.method public static synthetic X(Landroidx/constraintlayout/motion/widget/f;F)F
    .locals 0

    iput p1, p0, Landroidx/constraintlayout/motion/widget/f;->q:F

    return p1
.end method

.method public static synthetic m(Landroidx/constraintlayout/motion/widget/f;)F
    .locals 0

    iget p0, p0, Landroidx/constraintlayout/motion/widget/f;->r:F

    return p0
.end method

.method public static synthetic n(Landroidx/constraintlayout/motion/widget/f;F)F
    .locals 0

    iput p1, p0, Landroidx/constraintlayout/motion/widget/f;->r:F

    return p1
.end method

.method public static synthetic o(Landroidx/constraintlayout/motion/widget/f;Ljava/lang/String;)Ljava/lang/String;
    .locals 0

    iput-object p1, p0, Landroidx/constraintlayout/motion/widget/f;->g:Ljava/lang/String;

    return-object p1
.end method

.method public static synthetic p(Landroidx/constraintlayout/motion/widget/f;)F
    .locals 0

    iget p0, p0, Landroidx/constraintlayout/motion/widget/f;->t:F

    return p0
.end method

.method public static synthetic q(Landroidx/constraintlayout/motion/widget/f;F)F
    .locals 0

    iput p1, p0, Landroidx/constraintlayout/motion/widget/f;->t:F

    return p1
.end method

.method public static synthetic r(Landroidx/constraintlayout/motion/widget/f;)F
    .locals 0

    iget p0, p0, Landroidx/constraintlayout/motion/widget/f;->u:F

    return p0
.end method

.method public static synthetic s(Landroidx/constraintlayout/motion/widget/f;F)F
    .locals 0

    iput p1, p0, Landroidx/constraintlayout/motion/widget/f;->u:F

    return p1
.end method

.method public static synthetic t(Landroidx/constraintlayout/motion/widget/f;)F
    .locals 0

    iget p0, p0, Landroidx/constraintlayout/motion/widget/f;->s:F

    return p0
.end method

.method public static synthetic u(Landroidx/constraintlayout/motion/widget/f;F)F
    .locals 0

    iput p1, p0, Landroidx/constraintlayout/motion/widget/f;->s:F

    return p1
.end method

.method public static synthetic v(Landroidx/constraintlayout/motion/widget/f;)F
    .locals 0

    iget p0, p0, Landroidx/constraintlayout/motion/widget/f;->v:F

    return p0
.end method

.method public static synthetic w(Landroidx/constraintlayout/motion/widget/f;F)F
    .locals 0

    iput p1, p0, Landroidx/constraintlayout/motion/widget/f;->v:F

    return p1
.end method

.method public static synthetic x(Landroidx/constraintlayout/motion/widget/f;)F
    .locals 0

    iget p0, p0, Landroidx/constraintlayout/motion/widget/f;->w:F

    return p0
.end method

.method public static synthetic y(Landroidx/constraintlayout/motion/widget/f;F)F
    .locals 0

    iput p1, p0, Landroidx/constraintlayout/motion/widget/f;->w:F

    return p1
.end method

.method public static synthetic z(Landroidx/constraintlayout/motion/widget/f;)F
    .locals 0

    iget p0, p0, Landroidx/constraintlayout/motion/widget/f;->x:F

    return p0
.end method


# virtual methods
.method public Y(Ljava/util/HashMap;)V
    .locals 24
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/HashMap<",
            "Ljava/lang/String;",
            "Lc1/c;",
            ">;)V"
        }
    .end annotation

    move-object/from16 v0, p0

    move-object/from16 v1, p1

    invoke-virtual/range {p1 .. p1}, Ljava/util/HashMap;->keySet()Ljava/util/Set;

    move-result-object v2

    invoke-interface {v2}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v2

    :cond_0
    :goto_0
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    move-result v3

    if-eqz v3, :cond_6

    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Ljava/lang/String;

    const-string v4, "CUSTOM"

    invoke-virtual {v3, v4}, Ljava/lang/String;->startsWith(Ljava/lang/String;)Z

    move-result v4

    if-eqz v4, :cond_3

    const/4 v4, 0x7

    invoke-virtual {v3, v4}, Ljava/lang/String;->substring(I)Ljava/lang/String;

    move-result-object v4

    iget-object v5, v0, Landroidx/constraintlayout/motion/widget/d;->e:Ljava/util/HashMap;

    invoke-virtual {v5, v4}, Ljava/util/HashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v4

    move-object v14, v4

    check-cast v14, Landroidx/constraintlayout/widget/ConstraintAttribute;

    if-eqz v14, :cond_0

    invoke-virtual {v14}, Landroidx/constraintlayout/widget/ConstraintAttribute;->d()Landroidx/constraintlayout/widget/ConstraintAttribute$AttributeType;

    move-result-object v4

    sget-object v5, Landroidx/constraintlayout/widget/ConstraintAttribute$AttributeType;->FLOAT_TYPE:Landroidx/constraintlayout/widget/ConstraintAttribute$AttributeType;

    if-eq v4, v5, :cond_1

    goto :goto_0

    :cond_1
    invoke-virtual {v1, v3}, Ljava/util/HashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    move-object v5, v3

    check-cast v5, Lc1/c;

    if-nez v5, :cond_2

    goto :goto_0

    :cond_2
    iget v6, v0, Landroidx/constraintlayout/motion/widget/d;->a:I

    iget v7, v0, Landroidx/constraintlayout/motion/widget/f;->i:I

    iget-object v8, v0, Landroidx/constraintlayout/motion/widget/f;->j:Ljava/lang/String;

    iget v9, v0, Landroidx/constraintlayout/motion/widget/f;->o:I

    iget v10, v0, Landroidx/constraintlayout/motion/widget/f;->k:F

    iget v11, v0, Landroidx/constraintlayout/motion/widget/f;->l:F

    iget v12, v0, Landroidx/constraintlayout/motion/widget/f;->m:F

    invoke-virtual {v14}, Landroidx/constraintlayout/widget/ConstraintAttribute;->e()F

    move-result v13

    invoke-virtual/range {v5 .. v14}, Landroidx/constraintlayout/core/motion/utils/KeyCycleOscillator;->e(IILjava/lang/String;IFFFFLjava/lang/Object;)V

    goto :goto_0

    :cond_3
    invoke-virtual {v0, v3}, Landroidx/constraintlayout/motion/widget/f;->Z(Ljava/lang/String;)F

    move-result v23

    invoke-static/range {v23 .. v23}, Ljava/lang/Float;->isNaN(F)Z

    move-result v4

    if-eqz v4, :cond_4

    goto :goto_0

    :cond_4
    invoke-virtual {v1, v3}, Ljava/util/HashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    move-object v15, v3

    check-cast v15, Lc1/c;

    if-nez v15, :cond_5

    goto :goto_0

    :cond_5
    iget v3, v0, Landroidx/constraintlayout/motion/widget/d;->a:I

    iget v4, v0, Landroidx/constraintlayout/motion/widget/f;->i:I

    iget-object v5, v0, Landroidx/constraintlayout/motion/widget/f;->j:Ljava/lang/String;

    iget v6, v0, Landroidx/constraintlayout/motion/widget/f;->o:I

    iget v7, v0, Landroidx/constraintlayout/motion/widget/f;->k:F

    iget v8, v0, Landroidx/constraintlayout/motion/widget/f;->l:F

    iget v9, v0, Landroidx/constraintlayout/motion/widget/f;->m:F

    move/from16 v16, v3

    move/from16 v17, v4

    move-object/from16 v18, v5

    move/from16 v19, v6

    move/from16 v20, v7

    move/from16 v21, v8

    move/from16 v22, v9

    invoke-virtual/range {v15 .. v23}, Landroidx/constraintlayout/core/motion/utils/KeyCycleOscillator;->d(IILjava/lang/String;IFFFF)V

    goto/16 :goto_0

    :cond_6
    return-void
.end method

.method public Z(Ljava/lang/String;)F
    .locals 2

    invoke-virtual {p1}, Ljava/lang/String;->hashCode()I

    invoke-virtual {p1}, Ljava/lang/String;->hashCode()I

    move-result v0

    const/4 v1, -0x1

    sparse-switch v0, :sswitch_data_0

    goto/16 :goto_0

    :sswitch_0
    const-string v0, "wavePhase"

    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_0

    goto/16 :goto_0

    :cond_0
    const/16 v1, 0xd

    goto/16 :goto_0

    :sswitch_1
    const-string v0, "waveOffset"

    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_1

    goto/16 :goto_0

    :cond_1
    const/16 v1, 0xc

    goto/16 :goto_0

    :sswitch_2
    const-string v0, "alpha"

    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_2

    goto/16 :goto_0

    :cond_2
    const/16 v1, 0xb

    goto/16 :goto_0

    :sswitch_3
    const-string v0, "transitionPathRotate"

    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_3

    goto/16 :goto_0

    :cond_3
    const/16 v1, 0xa

    goto/16 :goto_0

    :sswitch_4
    const-string v0, "elevation"

    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_4

    goto/16 :goto_0

    :cond_4
    const/16 v1, 0x9

    goto/16 :goto_0

    :sswitch_5
    const-string v0, "rotation"

    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_5

    goto/16 :goto_0

    :cond_5
    const/16 v1, 0x8

    goto/16 :goto_0

    :sswitch_6
    const-string v0, "scaleY"

    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_6

    goto :goto_0

    :cond_6
    const/4 v1, 0x7

    goto :goto_0

    :sswitch_7
    const-string v0, "scaleX"

    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_7

    goto :goto_0

    :cond_7
    const/4 v1, 0x6

    goto :goto_0

    :sswitch_8
    const-string v0, "progress"

    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_8

    goto :goto_0

    :cond_8
    const/4 v1, 0x5

    goto :goto_0

    :sswitch_9
    const-string v0, "translationZ"

    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_9

    goto :goto_0

    :cond_9
    const/4 v1, 0x4

    goto :goto_0

    :sswitch_a
    const-string v0, "translationY"

    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_a

    goto :goto_0

    :cond_a
    const/4 v1, 0x3

    goto :goto_0

    :sswitch_b
    const-string v0, "translationX"

    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_b

    goto :goto_0

    :cond_b
    const/4 v1, 0x2

    goto :goto_0

    :sswitch_c
    const-string v0, "rotationY"

    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_c

    goto :goto_0

    :cond_c
    const/4 v1, 0x1

    goto :goto_0

    :sswitch_d
    const-string v0, "rotationX"

    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_d

    goto :goto_0

    :cond_d
    const/4 v1, 0x0

    :goto_0
    packed-switch v1, :pswitch_data_0

    const-string v0, "CUSTOM"

    invoke-virtual {p1, v0}, Ljava/lang/String;->startsWith(Ljava/lang/String;)Z

    move-result v0

    if-nez v0, :cond_e

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "  UNKNOWN  "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    const-string v0, "WARNING! KeyCycle"

    invoke-static {v0, p1}, Landroid/util/Log;->v(Ljava/lang/String;Ljava/lang/String;)I

    :cond_e
    const/high16 p1, 0x7fc00000    # Float.NaN

    return p1

    :pswitch_0
    iget p1, p0, Landroidx/constraintlayout/motion/widget/f;->m:F

    return p1

    :pswitch_1
    iget p1, p0, Landroidx/constraintlayout/motion/widget/f;->l:F

    return p1

    :pswitch_2
    iget p1, p0, Landroidx/constraintlayout/motion/widget/f;->p:F

    return p1

    :pswitch_3
    iget p1, p0, Landroidx/constraintlayout/motion/widget/f;->s:F

    return p1

    :pswitch_4
    iget p1, p0, Landroidx/constraintlayout/motion/widget/f;->q:F

    return p1

    :pswitch_5
    iget p1, p0, Landroidx/constraintlayout/motion/widget/f;->r:F

    return p1

    :pswitch_6
    iget p1, p0, Landroidx/constraintlayout/motion/widget/f;->w:F

    return p1

    :pswitch_7
    iget p1, p0, Landroidx/constraintlayout/motion/widget/f;->v:F

    return p1

    :pswitch_8
    iget p1, p0, Landroidx/constraintlayout/motion/widget/f;->n:F

    return p1

    :pswitch_9
    iget p1, p0, Landroidx/constraintlayout/motion/widget/f;->z:F

    return p1

    :pswitch_a
    iget p1, p0, Landroidx/constraintlayout/motion/widget/f;->y:F

    return p1

    :pswitch_b
    iget p1, p0, Landroidx/constraintlayout/motion/widget/f;->x:F

    return p1

    :pswitch_c
    iget p1, p0, Landroidx/constraintlayout/motion/widget/f;->u:F

    return p1

    :pswitch_d
    iget p1, p0, Landroidx/constraintlayout/motion/widget/f;->t:F

    return p1

    :sswitch_data_0
    .sparse-switch
        -0x4a771f66 -> :sswitch_d
        -0x4a771f65 -> :sswitch_c
        -0x490b9c39 -> :sswitch_b
        -0x490b9c38 -> :sswitch_a
        -0x490b9c37 -> :sswitch_9
        -0x3bab3dd3 -> :sswitch_8
        -0x3621dfb2 -> :sswitch_7
        -0x3621dfb1 -> :sswitch_6
        -0x266f082 -> :sswitch_5
        -0x42d1a3 -> :sswitch_4
        0x2382115 -> :sswitch_3
        0x589b15e -> :sswitch_2
        0x94e04ec -> :sswitch_1
        0x5b327a02 -> :sswitch_0
    .end sparse-switch

    :pswitch_data_0
    .packed-switch 0x0
        :pswitch_d
        :pswitch_c
        :pswitch_b
        :pswitch_a
        :pswitch_9
        :pswitch_8
        :pswitch_7
        :pswitch_6
        :pswitch_5
        :pswitch_4
        :pswitch_3
        :pswitch_2
        :pswitch_1
        :pswitch_0
    .end packed-switch
.end method

.method public a(Ljava/util/HashMap;)V
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/HashMap<",
            "Ljava/lang/String;",
            "Lc1/d;",
            ">;)V"
        }
    .end annotation

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "add "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p1}, Ljava/util/HashMap;->size()I

    move-result v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v1, " values"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    const-string v1, "KeyCycle"

    const/4 v2, 0x2

    invoke-static {v1, v0, v2}, Landroidx/constraintlayout/motion/widget/a;->g(Ljava/lang/String;Ljava/lang/String;I)V

    invoke-virtual {p1}, Ljava/util/HashMap;->keySet()Ljava/util/Set;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :cond_0
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_10

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/lang/String;

    invoke-virtual {p1, v1}, Ljava/util/HashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Landroidx/constraintlayout/core/motion/utils/i;

    if-nez v3, :cond_1

    goto :goto_0

    :cond_1
    invoke-virtual {v1}, Ljava/lang/String;->hashCode()I

    invoke-virtual {v1}, Ljava/lang/String;->hashCode()I

    move-result v4

    const/4 v5, -0x1

    sparse-switch v4, :sswitch_data_0

    goto/16 :goto_1

    :sswitch_0
    const-string v4, "wavePhase"

    invoke-virtual {v1, v4}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v4

    if-nez v4, :cond_2

    goto/16 :goto_1

    :cond_2
    const/16 v5, 0xd

    goto/16 :goto_1

    :sswitch_1
    const-string v4, "waveOffset"

    invoke-virtual {v1, v4}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v4

    if-nez v4, :cond_3

    goto/16 :goto_1

    :cond_3
    const/16 v5, 0xc

    goto/16 :goto_1

    :sswitch_2
    const-string v4, "alpha"

    invoke-virtual {v1, v4}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v4

    if-nez v4, :cond_4

    goto/16 :goto_1

    :cond_4
    const/16 v5, 0xb

    goto/16 :goto_1

    :sswitch_3
    const-string v4, "transitionPathRotate"

    invoke-virtual {v1, v4}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v4

    if-nez v4, :cond_5

    goto/16 :goto_1

    :cond_5
    const/16 v5, 0xa

    goto/16 :goto_1

    :sswitch_4
    const-string v4, "elevation"

    invoke-virtual {v1, v4}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v4

    if-nez v4, :cond_6

    goto/16 :goto_1

    :cond_6
    const/16 v5, 0x9

    goto/16 :goto_1

    :sswitch_5
    const-string v4, "rotation"

    invoke-virtual {v1, v4}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v4

    if-nez v4, :cond_7

    goto/16 :goto_1

    :cond_7
    const/16 v5, 0x8

    goto/16 :goto_1

    :sswitch_6
    const-string v4, "scaleY"

    invoke-virtual {v1, v4}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v4

    if-nez v4, :cond_8

    goto :goto_1

    :cond_8
    const/4 v5, 0x7

    goto :goto_1

    :sswitch_7
    const-string v4, "scaleX"

    invoke-virtual {v1, v4}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v4

    if-nez v4, :cond_9

    goto :goto_1

    :cond_9
    const/4 v5, 0x6

    goto :goto_1

    :sswitch_8
    const-string v4, "progress"

    invoke-virtual {v1, v4}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v4

    if-nez v4, :cond_a

    goto :goto_1

    :cond_a
    const/4 v5, 0x5

    goto :goto_1

    :sswitch_9
    const-string v4, "translationZ"

    invoke-virtual {v1, v4}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v4

    if-nez v4, :cond_b

    goto :goto_1

    :cond_b
    const/4 v5, 0x4

    goto :goto_1

    :sswitch_a
    const-string v4, "translationY"

    invoke-virtual {v1, v4}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v4

    if-nez v4, :cond_c

    goto :goto_1

    :cond_c
    const/4 v5, 0x3

    goto :goto_1

    :sswitch_b
    const-string v4, "translationX"

    invoke-virtual {v1, v4}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v4

    if-nez v4, :cond_d

    goto :goto_1

    :cond_d
    const/4 v5, 0x2

    goto :goto_1

    :sswitch_c
    const-string v4, "rotationY"

    invoke-virtual {v1, v4}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v4

    if-nez v4, :cond_e

    goto :goto_1

    :cond_e
    const/4 v5, 0x1

    goto :goto_1

    :sswitch_d
    const-string v4, "rotationX"

    invoke-virtual {v1, v4}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v4

    if-nez v4, :cond_f

    goto :goto_1

    :cond_f
    const/4 v5, 0x0

    :goto_1
    packed-switch v5, :pswitch_data_0

    const-string v3, "CUSTOM"

    invoke-virtual {v1, v3}, Ljava/lang/String;->startsWith(Ljava/lang/String;)Z

    move-result v3

    if-nez v3, :cond_0

    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v4, "  UNKNOWN  "

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    const-string v3, "WARNING KeyCycle"

    invoke-static {v3, v1}, Landroid/util/Log;->v(Ljava/lang/String;Ljava/lang/String;)I

    goto/16 :goto_0

    :pswitch_0
    iget v1, p0, Landroidx/constraintlayout/motion/widget/d;->a:I

    iget v4, p0, Landroidx/constraintlayout/motion/widget/f;->m:F

    invoke-virtual {v3, v1, v4}, Landroidx/constraintlayout/core/motion/utils/i;->c(IF)V

    goto/16 :goto_0

    :pswitch_1
    iget v1, p0, Landroidx/constraintlayout/motion/widget/d;->a:I

    iget v4, p0, Landroidx/constraintlayout/motion/widget/f;->l:F

    invoke-virtual {v3, v1, v4}, Landroidx/constraintlayout/core/motion/utils/i;->c(IF)V

    goto/16 :goto_0

    :pswitch_2
    iget v1, p0, Landroidx/constraintlayout/motion/widget/d;->a:I

    iget v4, p0, Landroidx/constraintlayout/motion/widget/f;->p:F

    invoke-virtual {v3, v1, v4}, Landroidx/constraintlayout/core/motion/utils/i;->c(IF)V

    goto/16 :goto_0

    :pswitch_3
    iget v1, p0, Landroidx/constraintlayout/motion/widget/d;->a:I

    iget v4, p0, Landroidx/constraintlayout/motion/widget/f;->s:F

    invoke-virtual {v3, v1, v4}, Landroidx/constraintlayout/core/motion/utils/i;->c(IF)V

    goto/16 :goto_0

    :pswitch_4
    iget v1, p0, Landroidx/constraintlayout/motion/widget/d;->a:I

    iget v4, p0, Landroidx/constraintlayout/motion/widget/f;->q:F

    invoke-virtual {v3, v1, v4}, Landroidx/constraintlayout/core/motion/utils/i;->c(IF)V

    goto/16 :goto_0

    :pswitch_5
    iget v1, p0, Landroidx/constraintlayout/motion/widget/d;->a:I

    iget v4, p0, Landroidx/constraintlayout/motion/widget/f;->r:F

    invoke-virtual {v3, v1, v4}, Landroidx/constraintlayout/core/motion/utils/i;->c(IF)V

    goto/16 :goto_0

    :pswitch_6
    iget v1, p0, Landroidx/constraintlayout/motion/widget/d;->a:I

    iget v4, p0, Landroidx/constraintlayout/motion/widget/f;->w:F

    invoke-virtual {v3, v1, v4}, Landroidx/constraintlayout/core/motion/utils/i;->c(IF)V

    goto/16 :goto_0

    :pswitch_7
    iget v1, p0, Landroidx/constraintlayout/motion/widget/d;->a:I

    iget v4, p0, Landroidx/constraintlayout/motion/widget/f;->v:F

    invoke-virtual {v3, v1, v4}, Landroidx/constraintlayout/core/motion/utils/i;->c(IF)V

    goto/16 :goto_0

    :pswitch_8
    iget v1, p0, Landroidx/constraintlayout/motion/widget/d;->a:I

    iget v4, p0, Landroidx/constraintlayout/motion/widget/f;->n:F

    invoke-virtual {v3, v1, v4}, Landroidx/constraintlayout/core/motion/utils/i;->c(IF)V

    goto/16 :goto_0

    :pswitch_9
    iget v1, p0, Landroidx/constraintlayout/motion/widget/d;->a:I

    iget v4, p0, Landroidx/constraintlayout/motion/widget/f;->z:F

    invoke-virtual {v3, v1, v4}, Landroidx/constraintlayout/core/motion/utils/i;->c(IF)V

    goto/16 :goto_0

    :pswitch_a
    iget v1, p0, Landroidx/constraintlayout/motion/widget/d;->a:I

    iget v4, p0, Landroidx/constraintlayout/motion/widget/f;->y:F

    invoke-virtual {v3, v1, v4}, Landroidx/constraintlayout/core/motion/utils/i;->c(IF)V

    goto/16 :goto_0

    :pswitch_b
    iget v1, p0, Landroidx/constraintlayout/motion/widget/d;->a:I

    iget v4, p0, Landroidx/constraintlayout/motion/widget/f;->x:F

    invoke-virtual {v3, v1, v4}, Landroidx/constraintlayout/core/motion/utils/i;->c(IF)V

    goto/16 :goto_0

    :pswitch_c
    iget v1, p0, Landroidx/constraintlayout/motion/widget/d;->a:I

    iget v4, p0, Landroidx/constraintlayout/motion/widget/f;->u:F

    invoke-virtual {v3, v1, v4}, Landroidx/constraintlayout/core/motion/utils/i;->c(IF)V

    goto/16 :goto_0

    :pswitch_d
    iget v1, p0, Landroidx/constraintlayout/motion/widget/d;->a:I

    iget v4, p0, Landroidx/constraintlayout/motion/widget/f;->t:F

    invoke-virtual {v3, v1, v4}, Landroidx/constraintlayout/core/motion/utils/i;->c(IF)V

    goto/16 :goto_0

    :cond_10
    return-void

    nop

    :sswitch_data_0
    .sparse-switch
        -0x4a771f66 -> :sswitch_d
        -0x4a771f65 -> :sswitch_c
        -0x490b9c39 -> :sswitch_b
        -0x490b9c38 -> :sswitch_a
        -0x490b9c37 -> :sswitch_9
        -0x3bab3dd3 -> :sswitch_8
        -0x3621dfb2 -> :sswitch_7
        -0x3621dfb1 -> :sswitch_6
        -0x266f082 -> :sswitch_5
        -0x42d1a3 -> :sswitch_4
        0x2382115 -> :sswitch_3
        0x589b15e -> :sswitch_2
        0x94e04ec -> :sswitch_1
        0x5b327a02 -> :sswitch_0
    .end sparse-switch

    :pswitch_data_0
    .packed-switch 0x0
        :pswitch_d
        :pswitch_c
        :pswitch_b
        :pswitch_a
        :pswitch_9
        :pswitch_8
        :pswitch_7
        :pswitch_6
        :pswitch_5
        :pswitch_4
        :pswitch_3
        :pswitch_2
        :pswitch_1
        :pswitch_0
    .end packed-switch
.end method

.method public b()Landroidx/constraintlayout/motion/widget/d;
    .locals 1

    new-instance v0, Landroidx/constraintlayout/motion/widget/f;

    invoke-direct {v0}, Landroidx/constraintlayout/motion/widget/f;-><init>()V

    invoke-virtual {v0, p0}, Landroidx/constraintlayout/motion/widget/f;->c(Landroidx/constraintlayout/motion/widget/d;)Landroidx/constraintlayout/motion/widget/d;

    move-result-object v0

    return-object v0
.end method

.method public c(Landroidx/constraintlayout/motion/widget/d;)Landroidx/constraintlayout/motion/widget/d;
    .locals 1

    invoke-super {p0, p1}, Landroidx/constraintlayout/motion/widget/d;->c(Landroidx/constraintlayout/motion/widget/d;)Landroidx/constraintlayout/motion/widget/d;

    check-cast p1, Landroidx/constraintlayout/motion/widget/f;

    iget-object v0, p1, Landroidx/constraintlayout/motion/widget/f;->g:Ljava/lang/String;

    iput-object v0, p0, Landroidx/constraintlayout/motion/widget/f;->g:Ljava/lang/String;

    iget v0, p1, Landroidx/constraintlayout/motion/widget/f;->h:I

    iput v0, p0, Landroidx/constraintlayout/motion/widget/f;->h:I

    iget v0, p1, Landroidx/constraintlayout/motion/widget/f;->i:I

    iput v0, p0, Landroidx/constraintlayout/motion/widget/f;->i:I

    iget-object v0, p1, Landroidx/constraintlayout/motion/widget/f;->j:Ljava/lang/String;

    iput-object v0, p0, Landroidx/constraintlayout/motion/widget/f;->j:Ljava/lang/String;

    iget v0, p1, Landroidx/constraintlayout/motion/widget/f;->k:F

    iput v0, p0, Landroidx/constraintlayout/motion/widget/f;->k:F

    iget v0, p1, Landroidx/constraintlayout/motion/widget/f;->l:F

    iput v0, p0, Landroidx/constraintlayout/motion/widget/f;->l:F

    iget v0, p1, Landroidx/constraintlayout/motion/widget/f;->m:F

    iput v0, p0, Landroidx/constraintlayout/motion/widget/f;->m:F

    iget v0, p1, Landroidx/constraintlayout/motion/widget/f;->n:F

    iput v0, p0, Landroidx/constraintlayout/motion/widget/f;->n:F

    iget v0, p1, Landroidx/constraintlayout/motion/widget/f;->o:I

    iput v0, p0, Landroidx/constraintlayout/motion/widget/f;->o:I

    iget v0, p1, Landroidx/constraintlayout/motion/widget/f;->p:F

    iput v0, p0, Landroidx/constraintlayout/motion/widget/f;->p:F

    iget v0, p1, Landroidx/constraintlayout/motion/widget/f;->q:F

    iput v0, p0, Landroidx/constraintlayout/motion/widget/f;->q:F

    iget v0, p1, Landroidx/constraintlayout/motion/widget/f;->r:F

    iput v0, p0, Landroidx/constraintlayout/motion/widget/f;->r:F

    iget v0, p1, Landroidx/constraintlayout/motion/widget/f;->s:F

    iput v0, p0, Landroidx/constraintlayout/motion/widget/f;->s:F

    iget v0, p1, Landroidx/constraintlayout/motion/widget/f;->t:F

    iput v0, p0, Landroidx/constraintlayout/motion/widget/f;->t:F

    iget v0, p1, Landroidx/constraintlayout/motion/widget/f;->u:F

    iput v0, p0, Landroidx/constraintlayout/motion/widget/f;->u:F

    iget v0, p1, Landroidx/constraintlayout/motion/widget/f;->v:F

    iput v0, p0, Landroidx/constraintlayout/motion/widget/f;->v:F

    iget v0, p1, Landroidx/constraintlayout/motion/widget/f;->w:F

    iput v0, p0, Landroidx/constraintlayout/motion/widget/f;->w:F

    iget v0, p1, Landroidx/constraintlayout/motion/widget/f;->x:F

    iput v0, p0, Landroidx/constraintlayout/motion/widget/f;->x:F

    iget v0, p1, Landroidx/constraintlayout/motion/widget/f;->y:F

    iput v0, p0, Landroidx/constraintlayout/motion/widget/f;->y:F

    iget p1, p1, Landroidx/constraintlayout/motion/widget/f;->z:F

    iput p1, p0, Landroidx/constraintlayout/motion/widget/f;->z:F

    return-object p0
.end method

.method public bridge synthetic clone()Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/CloneNotSupportedException;
        }
    .end annotation

    invoke-virtual {p0}, Landroidx/constraintlayout/motion/widget/f;->b()Landroidx/constraintlayout/motion/widget/d;

    move-result-object v0

    return-object v0
.end method

.method public d(Ljava/util/HashSet;)V
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/HashSet<",
            "Ljava/lang/String;",
            ">;)V"
        }
    .end annotation

    iget v0, p0, Landroidx/constraintlayout/motion/widget/f;->p:F

    invoke-static {v0}, Ljava/lang/Float;->isNaN(F)Z

    move-result v0

    if-nez v0, :cond_0

    const-string v0, "alpha"

    invoke-virtual {p1, v0}, Ljava/util/HashSet;->add(Ljava/lang/Object;)Z

    :cond_0
    iget v0, p0, Landroidx/constraintlayout/motion/widget/f;->q:F

    invoke-static {v0}, Ljava/lang/Float;->isNaN(F)Z

    move-result v0

    if-nez v0, :cond_1

    const-string v0, "elevation"

    invoke-virtual {p1, v0}, Ljava/util/HashSet;->add(Ljava/lang/Object;)Z

    :cond_1
    iget v0, p0, Landroidx/constraintlayout/motion/widget/f;->r:F

    invoke-static {v0}, Ljava/lang/Float;->isNaN(F)Z

    move-result v0

    if-nez v0, :cond_2

    const-string v0, "rotation"

    invoke-virtual {p1, v0}, Ljava/util/HashSet;->add(Ljava/lang/Object;)Z

    :cond_2
    iget v0, p0, Landroidx/constraintlayout/motion/widget/f;->t:F

    invoke-static {v0}, Ljava/lang/Float;->isNaN(F)Z

    move-result v0

    if-nez v0, :cond_3

    const-string v0, "rotationX"

    invoke-virtual {p1, v0}, Ljava/util/HashSet;->add(Ljava/lang/Object;)Z

    :cond_3
    iget v0, p0, Landroidx/constraintlayout/motion/widget/f;->u:F

    invoke-static {v0}, Ljava/lang/Float;->isNaN(F)Z

    move-result v0

    if-nez v0, :cond_4

    const-string v0, "rotationY"

    invoke-virtual {p1, v0}, Ljava/util/HashSet;->add(Ljava/lang/Object;)Z

    :cond_4
    iget v0, p0, Landroidx/constraintlayout/motion/widget/f;->v:F

    invoke-static {v0}, Ljava/lang/Float;->isNaN(F)Z

    move-result v0

    if-nez v0, :cond_5

    const-string v0, "scaleX"

    invoke-virtual {p1, v0}, Ljava/util/HashSet;->add(Ljava/lang/Object;)Z

    :cond_5
    iget v0, p0, Landroidx/constraintlayout/motion/widget/f;->w:F

    invoke-static {v0}, Ljava/lang/Float;->isNaN(F)Z

    move-result v0

    if-nez v0, :cond_6

    const-string v0, "scaleY"

    invoke-virtual {p1, v0}, Ljava/util/HashSet;->add(Ljava/lang/Object;)Z

    :cond_6
    iget v0, p0, Landroidx/constraintlayout/motion/widget/f;->s:F

    invoke-static {v0}, Ljava/lang/Float;->isNaN(F)Z

    move-result v0

    if-nez v0, :cond_7

    const-string v0, "transitionPathRotate"

    invoke-virtual {p1, v0}, Ljava/util/HashSet;->add(Ljava/lang/Object;)Z

    :cond_7
    iget v0, p0, Landroidx/constraintlayout/motion/widget/f;->x:F

    invoke-static {v0}, Ljava/lang/Float;->isNaN(F)Z

    move-result v0

    if-nez v0, :cond_8

    const-string v0, "translationX"

    invoke-virtual {p1, v0}, Ljava/util/HashSet;->add(Ljava/lang/Object;)Z

    :cond_8
    iget v0, p0, Landroidx/constraintlayout/motion/widget/f;->y:F

    invoke-static {v0}, Ljava/lang/Float;->isNaN(F)Z

    move-result v0

    if-nez v0, :cond_9

    const-string v0, "translationY"

    invoke-virtual {p1, v0}, Ljava/util/HashSet;->add(Ljava/lang/Object;)Z

    :cond_9
    iget v0, p0, Landroidx/constraintlayout/motion/widget/f;->z:F

    invoke-static {v0}, Ljava/lang/Float;->isNaN(F)Z

    move-result v0

    if-nez v0, :cond_a

    const-string v0, "translationZ"

    invoke-virtual {p1, v0}, Ljava/util/HashSet;->add(Ljava/lang/Object;)Z

    :cond_a
    iget-object v0, p0, Landroidx/constraintlayout/motion/widget/d;->e:Ljava/util/HashMap;

    invoke-virtual {v0}, Ljava/util/HashMap;->size()I

    move-result v0

    if-lez v0, :cond_b

    iget-object v0, p0, Landroidx/constraintlayout/motion/widget/d;->e:Ljava/util/HashMap;

    invoke-virtual {v0}, Ljava/util/HashMap;->keySet()Ljava/util/Set;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_b

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/lang/String;

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "CUSTOM,"

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p1, v1}, Ljava/util/HashSet;->add(Ljava/lang/Object;)Z

    goto :goto_0

    :cond_b
    return-void
.end method

.method public e(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 1

    sget-object v0, Landroidx/constraintlayout/widget/R$styleable;->KeyCycle:[I

    invoke-virtual {p1, p2, v0}, Landroid/content/Context;->obtainStyledAttributes(Landroid/util/AttributeSet;[I)Landroid/content/res/TypedArray;

    move-result-object p1

    invoke-static {p0, p1}, Landroidx/constraintlayout/motion/widget/f$a;->a(Landroidx/constraintlayout/motion/widget/f;Landroid/content/res/TypedArray;)V

    return-void
.end method
