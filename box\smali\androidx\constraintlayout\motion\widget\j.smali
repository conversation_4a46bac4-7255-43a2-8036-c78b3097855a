.class public Landroidx/constraintlayout/motion/widget/j;
.super Landroidx/constraintlayout/motion/widget/d;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Landroidx/constraintlayout/motion/widget/j$a;
    }
.end annotation


# instance fields
.field public g:Ljava/lang/String;

.field public h:I

.field public i:F

.field public j:F

.field public k:F

.field public l:F

.field public m:F

.field public n:F

.field public o:F

.field public p:F

.field public q:F

.field public r:F

.field public s:F

.field public t:F

.field public u:I

.field public v:Ljava/lang/String;

.field public w:F

.field public x:F


# direct methods
.method public constructor <init>()V
    .locals 2

    invoke-direct {p0}, Landroidx/constraintlayout/motion/widget/d;-><init>()V

    const/4 v0, -0x1

    iput v0, p0, Landroidx/constraintlayout/motion/widget/j;->h:I

    const/high16 v0, 0x7fc00000    # Float.NaN

    iput v0, p0, Landroidx/constraintlayout/motion/widget/j;->i:F

    iput v0, p0, Landroidx/constraintlayout/motion/widget/j;->j:F

    iput v0, p0, Landroidx/constraintlayout/motion/widget/j;->k:F

    iput v0, p0, Landroidx/constraintlayout/motion/widget/j;->l:F

    iput v0, p0, Landroidx/constraintlayout/motion/widget/j;->m:F

    iput v0, p0, Landroidx/constraintlayout/motion/widget/j;->n:F

    iput v0, p0, Landroidx/constraintlayout/motion/widget/j;->o:F

    iput v0, p0, Landroidx/constraintlayout/motion/widget/j;->p:F

    iput v0, p0, Landroidx/constraintlayout/motion/widget/j;->q:F

    iput v0, p0, Landroidx/constraintlayout/motion/widget/j;->r:F

    iput v0, p0, Landroidx/constraintlayout/motion/widget/j;->s:F

    iput v0, p0, Landroidx/constraintlayout/motion/widget/j;->t:F

    const/4 v1, 0x0

    iput v1, p0, Landroidx/constraintlayout/motion/widget/j;->u:I

    const/4 v1, 0x0

    iput-object v1, p0, Landroidx/constraintlayout/motion/widget/j;->v:Ljava/lang/String;

    iput v0, p0, Landroidx/constraintlayout/motion/widget/j;->w:F

    const/4 v0, 0x0

    iput v0, p0, Landroidx/constraintlayout/motion/widget/j;->x:F

    const/4 v0, 0x3

    iput v0, p0, Landroidx/constraintlayout/motion/widget/d;->d:I

    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    iput-object v0, p0, Landroidx/constraintlayout/motion/widget/d;->e:Ljava/util/HashMap;

    return-void
.end method

.method public static synthetic A(Landroidx/constraintlayout/motion/widget/j;F)F
    .locals 0

    iput p1, p0, Landroidx/constraintlayout/motion/widget/j;->r:F

    return p1
.end method

.method public static synthetic B(Landroidx/constraintlayout/motion/widget/j;)F
    .locals 0

    iget p0, p0, Landroidx/constraintlayout/motion/widget/j;->s:F

    return p0
.end method

.method public static synthetic C(Landroidx/constraintlayout/motion/widget/j;F)F
    .locals 0

    iput p1, p0, Landroidx/constraintlayout/motion/widget/j;->s:F

    return p1
.end method

.method public static synthetic D(Landroidx/constraintlayout/motion/widget/j;)F
    .locals 0

    iget p0, p0, Landroidx/constraintlayout/motion/widget/j;->t:F

    return p0
.end method

.method public static synthetic E(Landroidx/constraintlayout/motion/widget/j;F)F
    .locals 0

    iput p1, p0, Landroidx/constraintlayout/motion/widget/j;->t:F

    return p1
.end method

.method public static synthetic F(Landroidx/constraintlayout/motion/widget/j;)F
    .locals 0

    iget p0, p0, Landroidx/constraintlayout/motion/widget/j;->k:F

    return p0
.end method

.method public static synthetic G(Landroidx/constraintlayout/motion/widget/j;F)F
    .locals 0

    iput p1, p0, Landroidx/constraintlayout/motion/widget/j;->k:F

    return p1
.end method

.method public static synthetic H(Landroidx/constraintlayout/motion/widget/j;)I
    .locals 0

    iget p0, p0, Landroidx/constraintlayout/motion/widget/j;->h:I

    return p0
.end method

.method public static synthetic I(Landroidx/constraintlayout/motion/widget/j;I)I
    .locals 0

    iput p1, p0, Landroidx/constraintlayout/motion/widget/j;->h:I

    return p1
.end method

.method public static synthetic J(Landroidx/constraintlayout/motion/widget/j;Ljava/lang/String;)Ljava/lang/String;
    .locals 0

    iput-object p1, p0, Landroidx/constraintlayout/motion/widget/j;->v:Ljava/lang/String;

    return-object p1
.end method

.method public static synthetic K(Landroidx/constraintlayout/motion/widget/j;)I
    .locals 0

    iget p0, p0, Landroidx/constraintlayout/motion/widget/j;->u:I

    return p0
.end method

.method public static synthetic L(Landroidx/constraintlayout/motion/widget/j;I)I
    .locals 0

    iput p1, p0, Landroidx/constraintlayout/motion/widget/j;->u:I

    return p1
.end method

.method public static synthetic M(Landroidx/constraintlayout/motion/widget/j;)F
    .locals 0

    iget p0, p0, Landroidx/constraintlayout/motion/widget/j;->w:F

    return p0
.end method

.method public static synthetic N(Landroidx/constraintlayout/motion/widget/j;F)F
    .locals 0

    iput p1, p0, Landroidx/constraintlayout/motion/widget/j;->w:F

    return p1
.end method

.method public static synthetic O(Landroidx/constraintlayout/motion/widget/j;)F
    .locals 0

    iget p0, p0, Landroidx/constraintlayout/motion/widget/j;->x:F

    return p0
.end method

.method public static synthetic P(Landroidx/constraintlayout/motion/widget/j;F)F
    .locals 0

    iput p1, p0, Landroidx/constraintlayout/motion/widget/j;->x:F

    return p1
.end method

.method public static synthetic Q(Landroidx/constraintlayout/motion/widget/j;)F
    .locals 0

    iget p0, p0, Landroidx/constraintlayout/motion/widget/j;->o:F

    return p0
.end method

.method public static synthetic R(Landroidx/constraintlayout/motion/widget/j;F)F
    .locals 0

    iput p1, p0, Landroidx/constraintlayout/motion/widget/j;->o:F

    return p1
.end method

.method public static synthetic S(Landroidx/constraintlayout/motion/widget/j;)F
    .locals 0

    iget p0, p0, Landroidx/constraintlayout/motion/widget/j;->l:F

    return p0
.end method

.method public static synthetic T(Landroidx/constraintlayout/motion/widget/j;F)F
    .locals 0

    iput p1, p0, Landroidx/constraintlayout/motion/widget/j;->l:F

    return p1
.end method

.method public static synthetic m(Landroidx/constraintlayout/motion/widget/j;)F
    .locals 0

    iget p0, p0, Landroidx/constraintlayout/motion/widget/j;->i:F

    return p0
.end method

.method public static synthetic n(Landroidx/constraintlayout/motion/widget/j;F)F
    .locals 0

    iput p1, p0, Landroidx/constraintlayout/motion/widget/j;->i:F

    return p1
.end method

.method public static synthetic o(Landroidx/constraintlayout/motion/widget/j;)F
    .locals 0

    iget p0, p0, Landroidx/constraintlayout/motion/widget/j;->j:F

    return p0
.end method

.method public static synthetic p(Landroidx/constraintlayout/motion/widget/j;)F
    .locals 0

    iget p0, p0, Landroidx/constraintlayout/motion/widget/j;->m:F

    return p0
.end method

.method public static synthetic q(Landroidx/constraintlayout/motion/widget/j;F)F
    .locals 0

    iput p1, p0, Landroidx/constraintlayout/motion/widget/j;->m:F

    return p1
.end method

.method public static synthetic r(Landroidx/constraintlayout/motion/widget/j;F)F
    .locals 0

    iput p1, p0, Landroidx/constraintlayout/motion/widget/j;->j:F

    return p1
.end method

.method public static synthetic s(Landroidx/constraintlayout/motion/widget/j;Ljava/lang/String;)Ljava/lang/String;
    .locals 0

    iput-object p1, p0, Landroidx/constraintlayout/motion/widget/j;->g:Ljava/lang/String;

    return-object p1
.end method

.method public static synthetic t(Landroidx/constraintlayout/motion/widget/j;)F
    .locals 0

    iget p0, p0, Landroidx/constraintlayout/motion/widget/j;->p:F

    return p0
.end method

.method public static synthetic u(Landroidx/constraintlayout/motion/widget/j;F)F
    .locals 0

    iput p1, p0, Landroidx/constraintlayout/motion/widget/j;->p:F

    return p1
.end method

.method public static synthetic v(Landroidx/constraintlayout/motion/widget/j;)F
    .locals 0

    iget p0, p0, Landroidx/constraintlayout/motion/widget/j;->n:F

    return p0
.end method

.method public static synthetic w(Landroidx/constraintlayout/motion/widget/j;F)F
    .locals 0

    iput p1, p0, Landroidx/constraintlayout/motion/widget/j;->n:F

    return p1
.end method

.method public static synthetic x(Landroidx/constraintlayout/motion/widget/j;)F
    .locals 0

    iget p0, p0, Landroidx/constraintlayout/motion/widget/j;->q:F

    return p0
.end method

.method public static synthetic y(Landroidx/constraintlayout/motion/widget/j;F)F
    .locals 0

    iput p1, p0, Landroidx/constraintlayout/motion/widget/j;->q:F

    return p1
.end method

.method public static synthetic z(Landroidx/constraintlayout/motion/widget/j;)F
    .locals 0

    iget p0, p0, Landroidx/constraintlayout/motion/widget/j;->r:F

    return p0
.end method


# virtual methods
.method public U(Ljava/util/HashMap;)V
    .locals 10
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/HashMap<",
            "Ljava/lang/String;",
            "Lc1/f;",
            ">;)V"
        }
    .end annotation

    invoke-virtual {p1}, Ljava/util/HashMap;->keySet()Ljava/util/Set;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :cond_0
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_f

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/lang/String;

    invoke-virtual {p1, v1}, Ljava/util/HashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    move-object v3, v2

    check-cast v3, Lc1/f;

    if-nez v3, :cond_1

    goto :goto_0

    :cond_1
    const-string v2, "CUSTOM"

    invoke-virtual {v1, v2}, Ljava/lang/String;->startsWith(Ljava/lang/String;)Z

    move-result v2

    const/4 v4, 0x7

    if-eqz v2, :cond_2

    invoke-virtual {v1, v4}, Ljava/lang/String;->substring(I)Ljava/lang/String;

    move-result-object v1

    iget-object v2, p0, Landroidx/constraintlayout/motion/widget/d;->e:Ljava/util/HashMap;

    invoke-virtual {v2, v1}, Ljava/util/HashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    move-object v6, v1

    check-cast v6, Landroidx/constraintlayout/widget/ConstraintAttribute;

    if-eqz v6, :cond_0

    move-object v4, v3

    check-cast v4, Lc1/f$b;

    iget v5, p0, Landroidx/constraintlayout/motion/widget/d;->a:I

    iget v7, p0, Landroidx/constraintlayout/motion/widget/j;->w:F

    iget v8, p0, Landroidx/constraintlayout/motion/widget/j;->u:I

    iget v9, p0, Landroidx/constraintlayout/motion/widget/j;->x:F

    invoke-virtual/range {v4 .. v9}, Lc1/f$b;->j(ILandroidx/constraintlayout/widget/ConstraintAttribute;FIF)V

    goto :goto_0

    :cond_2
    invoke-virtual {v1}, Ljava/lang/String;->hashCode()I

    move-result v2

    const/4 v5, -0x1

    sparse-switch v2, :sswitch_data_0

    :goto_1
    const/4 v4, -0x1

    goto/16 :goto_2

    :sswitch_0
    const-string v2, "alpha"

    invoke-virtual {v1, v2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v2

    if-nez v2, :cond_3

    goto :goto_1

    :cond_3
    const/16 v4, 0xb

    goto/16 :goto_2

    :sswitch_1
    const-string v2, "transitionPathRotate"

    invoke-virtual {v1, v2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v2

    if-nez v2, :cond_4

    goto :goto_1

    :cond_4
    const/16 v4, 0xa

    goto/16 :goto_2

    :sswitch_2
    const-string v2, "elevation"

    invoke-virtual {v1, v2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v2

    if-nez v2, :cond_5

    goto :goto_1

    :cond_5
    const/16 v4, 0x9

    goto/16 :goto_2

    :sswitch_3
    const-string v2, "rotation"

    invoke-virtual {v1, v2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v2

    if-nez v2, :cond_6

    goto :goto_1

    :cond_6
    const/16 v4, 0x8

    goto :goto_2

    :sswitch_4
    const-string v2, "scaleY"

    invoke-virtual {v1, v2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v2

    if-nez v2, :cond_e

    goto :goto_1

    :sswitch_5
    const-string v2, "scaleX"

    invoke-virtual {v1, v2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v2

    if-nez v2, :cond_7

    goto :goto_1

    :cond_7
    const/4 v4, 0x6

    goto :goto_2

    :sswitch_6
    const-string v2, "progress"

    invoke-virtual {v1, v2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v2

    if-nez v2, :cond_8

    goto :goto_1

    :cond_8
    const/4 v4, 0x5

    goto :goto_2

    :sswitch_7
    const-string v2, "translationZ"

    invoke-virtual {v1, v2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v2

    if-nez v2, :cond_9

    goto :goto_1

    :cond_9
    const/4 v4, 0x4

    goto :goto_2

    :sswitch_8
    const-string v2, "translationY"

    invoke-virtual {v1, v2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v2

    if-nez v2, :cond_a

    goto :goto_1

    :cond_a
    const/4 v4, 0x3

    goto :goto_2

    :sswitch_9
    const-string v2, "translationX"

    invoke-virtual {v1, v2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v2

    if-nez v2, :cond_b

    goto :goto_1

    :cond_b
    const/4 v4, 0x2

    goto :goto_2

    :sswitch_a
    const-string v2, "rotationY"

    invoke-virtual {v1, v2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v2

    if-nez v2, :cond_c

    goto :goto_1

    :cond_c
    const/4 v4, 0x1

    goto :goto_2

    :sswitch_b
    const-string v2, "rotationX"

    invoke-virtual {v1, v2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v2

    if-nez v2, :cond_d

    goto/16 :goto_1

    :cond_d
    const/4 v4, 0x0

    :cond_e
    :goto_2
    packed-switch v4, :pswitch_data_0

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "UNKNOWN addValues \""

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v1, "\""

    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    const-string v2, "KeyTimeCycles"

    invoke-static {v2, v1}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    goto/16 :goto_0

    :pswitch_0
    iget v1, p0, Landroidx/constraintlayout/motion/widget/j;->i:F

    invoke-static {v1}, Ljava/lang/Float;->isNaN(F)Z

    move-result v1

    if-nez v1, :cond_0

    iget v4, p0, Landroidx/constraintlayout/motion/widget/d;->a:I

    iget v5, p0, Landroidx/constraintlayout/motion/widget/j;->i:F

    iget v6, p0, Landroidx/constraintlayout/motion/widget/j;->w:F

    iget v7, p0, Landroidx/constraintlayout/motion/widget/j;->u:I

    iget v8, p0, Landroidx/constraintlayout/motion/widget/j;->x:F

    invoke-virtual/range {v3 .. v8}, Landroidx/constraintlayout/core/motion/utils/n;->b(IFFIF)V

    goto/16 :goto_0

    :pswitch_1
    iget v1, p0, Landroidx/constraintlayout/motion/widget/j;->n:F

    invoke-static {v1}, Ljava/lang/Float;->isNaN(F)Z

    move-result v1

    if-nez v1, :cond_0

    iget v4, p0, Landroidx/constraintlayout/motion/widget/d;->a:I

    iget v5, p0, Landroidx/constraintlayout/motion/widget/j;->n:F

    iget v6, p0, Landroidx/constraintlayout/motion/widget/j;->w:F

    iget v7, p0, Landroidx/constraintlayout/motion/widget/j;->u:I

    iget v8, p0, Landroidx/constraintlayout/motion/widget/j;->x:F

    invoke-virtual/range {v3 .. v8}, Landroidx/constraintlayout/core/motion/utils/n;->b(IFFIF)V

    goto/16 :goto_0

    :pswitch_2
    iget v1, p0, Landroidx/constraintlayout/motion/widget/j;->j:F

    invoke-static {v1}, Ljava/lang/Float;->isNaN(F)Z

    move-result v1

    if-nez v1, :cond_0

    iget v4, p0, Landroidx/constraintlayout/motion/widget/d;->a:I

    iget v5, p0, Landroidx/constraintlayout/motion/widget/j;->j:F

    iget v6, p0, Landroidx/constraintlayout/motion/widget/j;->w:F

    iget v7, p0, Landroidx/constraintlayout/motion/widget/j;->u:I

    iget v8, p0, Landroidx/constraintlayout/motion/widget/j;->x:F

    invoke-virtual/range {v3 .. v8}, Landroidx/constraintlayout/core/motion/utils/n;->b(IFFIF)V

    goto/16 :goto_0

    :pswitch_3
    iget v1, p0, Landroidx/constraintlayout/motion/widget/j;->k:F

    invoke-static {v1}, Ljava/lang/Float;->isNaN(F)Z

    move-result v1

    if-nez v1, :cond_0

    iget v4, p0, Landroidx/constraintlayout/motion/widget/d;->a:I

    iget v5, p0, Landroidx/constraintlayout/motion/widget/j;->k:F

    iget v6, p0, Landroidx/constraintlayout/motion/widget/j;->w:F

    iget v7, p0, Landroidx/constraintlayout/motion/widget/j;->u:I

    iget v8, p0, Landroidx/constraintlayout/motion/widget/j;->x:F

    invoke-virtual/range {v3 .. v8}, Landroidx/constraintlayout/core/motion/utils/n;->b(IFFIF)V

    goto/16 :goto_0

    :pswitch_4
    iget v1, p0, Landroidx/constraintlayout/motion/widget/j;->p:F

    invoke-static {v1}, Ljava/lang/Float;->isNaN(F)Z

    move-result v1

    if-nez v1, :cond_0

    iget v4, p0, Landroidx/constraintlayout/motion/widget/d;->a:I

    iget v5, p0, Landroidx/constraintlayout/motion/widget/j;->p:F

    iget v6, p0, Landroidx/constraintlayout/motion/widget/j;->w:F

    iget v7, p0, Landroidx/constraintlayout/motion/widget/j;->u:I

    iget v8, p0, Landroidx/constraintlayout/motion/widget/j;->x:F

    invoke-virtual/range {v3 .. v8}, Landroidx/constraintlayout/core/motion/utils/n;->b(IFFIF)V

    goto/16 :goto_0

    :pswitch_5
    iget v1, p0, Landroidx/constraintlayout/motion/widget/j;->o:F

    invoke-static {v1}, Ljava/lang/Float;->isNaN(F)Z

    move-result v1

    if-nez v1, :cond_0

    iget v4, p0, Landroidx/constraintlayout/motion/widget/d;->a:I

    iget v5, p0, Landroidx/constraintlayout/motion/widget/j;->o:F

    iget v6, p0, Landroidx/constraintlayout/motion/widget/j;->w:F

    iget v7, p0, Landroidx/constraintlayout/motion/widget/j;->u:I

    iget v8, p0, Landroidx/constraintlayout/motion/widget/j;->x:F

    invoke-virtual/range {v3 .. v8}, Landroidx/constraintlayout/core/motion/utils/n;->b(IFFIF)V

    goto/16 :goto_0

    :pswitch_6
    iget v1, p0, Landroidx/constraintlayout/motion/widget/j;->t:F

    invoke-static {v1}, Ljava/lang/Float;->isNaN(F)Z

    move-result v1

    if-nez v1, :cond_0

    iget v4, p0, Landroidx/constraintlayout/motion/widget/d;->a:I

    iget v5, p0, Landroidx/constraintlayout/motion/widget/j;->t:F

    iget v6, p0, Landroidx/constraintlayout/motion/widget/j;->w:F

    iget v7, p0, Landroidx/constraintlayout/motion/widget/j;->u:I

    iget v8, p0, Landroidx/constraintlayout/motion/widget/j;->x:F

    invoke-virtual/range {v3 .. v8}, Landroidx/constraintlayout/core/motion/utils/n;->b(IFFIF)V

    goto/16 :goto_0

    :pswitch_7
    iget v1, p0, Landroidx/constraintlayout/motion/widget/j;->s:F

    invoke-static {v1}, Ljava/lang/Float;->isNaN(F)Z

    move-result v1

    if-nez v1, :cond_0

    iget v4, p0, Landroidx/constraintlayout/motion/widget/d;->a:I

    iget v5, p0, Landroidx/constraintlayout/motion/widget/j;->s:F

    iget v6, p0, Landroidx/constraintlayout/motion/widget/j;->w:F

    iget v7, p0, Landroidx/constraintlayout/motion/widget/j;->u:I

    iget v8, p0, Landroidx/constraintlayout/motion/widget/j;->x:F

    invoke-virtual/range {v3 .. v8}, Landroidx/constraintlayout/core/motion/utils/n;->b(IFFIF)V

    goto/16 :goto_0

    :pswitch_8
    iget v1, p0, Landroidx/constraintlayout/motion/widget/j;->r:F

    invoke-static {v1}, Ljava/lang/Float;->isNaN(F)Z

    move-result v1

    if-nez v1, :cond_0

    iget v4, p0, Landroidx/constraintlayout/motion/widget/d;->a:I

    iget v5, p0, Landroidx/constraintlayout/motion/widget/j;->r:F

    iget v6, p0, Landroidx/constraintlayout/motion/widget/j;->w:F

    iget v7, p0, Landroidx/constraintlayout/motion/widget/j;->u:I

    iget v8, p0, Landroidx/constraintlayout/motion/widget/j;->x:F

    invoke-virtual/range {v3 .. v8}, Landroidx/constraintlayout/core/motion/utils/n;->b(IFFIF)V

    goto/16 :goto_0

    :pswitch_9
    iget v1, p0, Landroidx/constraintlayout/motion/widget/j;->q:F

    invoke-static {v1}, Ljava/lang/Float;->isNaN(F)Z

    move-result v1

    if-nez v1, :cond_0

    iget v4, p0, Landroidx/constraintlayout/motion/widget/d;->a:I

    iget v5, p0, Landroidx/constraintlayout/motion/widget/j;->q:F

    iget v6, p0, Landroidx/constraintlayout/motion/widget/j;->w:F

    iget v7, p0, Landroidx/constraintlayout/motion/widget/j;->u:I

    iget v8, p0, Landroidx/constraintlayout/motion/widget/j;->x:F

    invoke-virtual/range {v3 .. v8}, Landroidx/constraintlayout/core/motion/utils/n;->b(IFFIF)V

    goto/16 :goto_0

    :pswitch_a
    iget v1, p0, Landroidx/constraintlayout/motion/widget/j;->m:F

    invoke-static {v1}, Ljava/lang/Float;->isNaN(F)Z

    move-result v1

    if-nez v1, :cond_0

    iget v4, p0, Landroidx/constraintlayout/motion/widget/d;->a:I

    iget v5, p0, Landroidx/constraintlayout/motion/widget/j;->m:F

    iget v6, p0, Landroidx/constraintlayout/motion/widget/j;->w:F

    iget v7, p0, Landroidx/constraintlayout/motion/widget/j;->u:I

    iget v8, p0, Landroidx/constraintlayout/motion/widget/j;->x:F

    invoke-virtual/range {v3 .. v8}, Landroidx/constraintlayout/core/motion/utils/n;->b(IFFIF)V

    goto/16 :goto_0

    :pswitch_b
    iget v1, p0, Landroidx/constraintlayout/motion/widget/j;->l:F

    invoke-static {v1}, Ljava/lang/Float;->isNaN(F)Z

    move-result v1

    if-nez v1, :cond_0

    iget v4, p0, Landroidx/constraintlayout/motion/widget/d;->a:I

    iget v5, p0, Landroidx/constraintlayout/motion/widget/j;->l:F

    iget v6, p0, Landroidx/constraintlayout/motion/widget/j;->w:F

    iget v7, p0, Landroidx/constraintlayout/motion/widget/j;->u:I

    iget v8, p0, Landroidx/constraintlayout/motion/widget/j;->x:F

    invoke-virtual/range {v3 .. v8}, Landroidx/constraintlayout/core/motion/utils/n;->b(IFFIF)V

    goto/16 :goto_0

    :cond_f
    return-void

    :sswitch_data_0
    .sparse-switch
        -0x4a771f66 -> :sswitch_b
        -0x4a771f65 -> :sswitch_a
        -0x490b9c39 -> :sswitch_9
        -0x490b9c38 -> :sswitch_8
        -0x490b9c37 -> :sswitch_7
        -0x3bab3dd3 -> :sswitch_6
        -0x3621dfb2 -> :sswitch_5
        -0x3621dfb1 -> :sswitch_4
        -0x266f082 -> :sswitch_3
        -0x42d1a3 -> :sswitch_2
        0x2382115 -> :sswitch_1
        0x589b15e -> :sswitch_0
    .end sparse-switch

    :pswitch_data_0
    .packed-switch 0x0
        :pswitch_b
        :pswitch_a
        :pswitch_9
        :pswitch_8
        :pswitch_7
        :pswitch_6
        :pswitch_5
        :pswitch_4
        :pswitch_3
        :pswitch_2
        :pswitch_1
        :pswitch_0
    .end packed-switch
.end method

.method public a(Ljava/util/HashMap;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/HashMap<",
            "Ljava/lang/String;",
            "Lc1/d;",
            ">;)V"
        }
    .end annotation

    new-instance p1, Ljava/lang/IllegalArgumentException;

    const-string v0, " KeyTimeCycles do not support SplineSet"

    invoke-direct {p1, v0}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public b()Landroidx/constraintlayout/motion/widget/d;
    .locals 1

    new-instance v0, Landroidx/constraintlayout/motion/widget/j;

    invoke-direct {v0}, Landroidx/constraintlayout/motion/widget/j;-><init>()V

    invoke-virtual {v0, p0}, Landroidx/constraintlayout/motion/widget/j;->c(Landroidx/constraintlayout/motion/widget/d;)Landroidx/constraintlayout/motion/widget/d;

    move-result-object v0

    return-object v0
.end method

.method public c(Landroidx/constraintlayout/motion/widget/d;)Landroidx/constraintlayout/motion/widget/d;
    .locals 1

    invoke-super {p0, p1}, Landroidx/constraintlayout/motion/widget/d;->c(Landroidx/constraintlayout/motion/widget/d;)Landroidx/constraintlayout/motion/widget/d;

    check-cast p1, Landroidx/constraintlayout/motion/widget/j;

    iget-object v0, p1, Landroidx/constraintlayout/motion/widget/j;->g:Ljava/lang/String;

    iput-object v0, p0, Landroidx/constraintlayout/motion/widget/j;->g:Ljava/lang/String;

    iget v0, p1, Landroidx/constraintlayout/motion/widget/j;->h:I

    iput v0, p0, Landroidx/constraintlayout/motion/widget/j;->h:I

    iget v0, p1, Landroidx/constraintlayout/motion/widget/j;->u:I

    iput v0, p0, Landroidx/constraintlayout/motion/widget/j;->u:I

    iget v0, p1, Landroidx/constraintlayout/motion/widget/j;->w:F

    iput v0, p0, Landroidx/constraintlayout/motion/widget/j;->w:F

    iget v0, p1, Landroidx/constraintlayout/motion/widget/j;->x:F

    iput v0, p0, Landroidx/constraintlayout/motion/widget/j;->x:F

    iget v0, p1, Landroidx/constraintlayout/motion/widget/j;->t:F

    iput v0, p0, Landroidx/constraintlayout/motion/widget/j;->t:F

    iget v0, p1, Landroidx/constraintlayout/motion/widget/j;->i:F

    iput v0, p0, Landroidx/constraintlayout/motion/widget/j;->i:F

    iget v0, p1, Landroidx/constraintlayout/motion/widget/j;->j:F

    iput v0, p0, Landroidx/constraintlayout/motion/widget/j;->j:F

    iget v0, p1, Landroidx/constraintlayout/motion/widget/j;->k:F

    iput v0, p0, Landroidx/constraintlayout/motion/widget/j;->k:F

    iget v0, p1, Landroidx/constraintlayout/motion/widget/j;->n:F

    iput v0, p0, Landroidx/constraintlayout/motion/widget/j;->n:F

    iget v0, p1, Landroidx/constraintlayout/motion/widget/j;->l:F

    iput v0, p0, Landroidx/constraintlayout/motion/widget/j;->l:F

    iget v0, p1, Landroidx/constraintlayout/motion/widget/j;->m:F

    iput v0, p0, Landroidx/constraintlayout/motion/widget/j;->m:F

    iget v0, p1, Landroidx/constraintlayout/motion/widget/j;->o:F

    iput v0, p0, Landroidx/constraintlayout/motion/widget/j;->o:F

    iget v0, p1, Landroidx/constraintlayout/motion/widget/j;->p:F

    iput v0, p0, Landroidx/constraintlayout/motion/widget/j;->p:F

    iget v0, p1, Landroidx/constraintlayout/motion/widget/j;->q:F

    iput v0, p0, Landroidx/constraintlayout/motion/widget/j;->q:F

    iget v0, p1, Landroidx/constraintlayout/motion/widget/j;->r:F

    iput v0, p0, Landroidx/constraintlayout/motion/widget/j;->r:F

    iget p1, p1, Landroidx/constraintlayout/motion/widget/j;->s:F

    iput p1, p0, Landroidx/constraintlayout/motion/widget/j;->s:F

    return-object p0
.end method

.method public bridge synthetic clone()Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/CloneNotSupportedException;
        }
    .end annotation

    invoke-virtual {p0}, Landroidx/constraintlayout/motion/widget/j;->b()Landroidx/constraintlayout/motion/widget/d;

    move-result-object v0

    return-object v0
.end method

.method public d(Ljava/util/HashSet;)V
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/HashSet<",
            "Ljava/lang/String;",
            ">;)V"
        }
    .end annotation

    iget v0, p0, Landroidx/constraintlayout/motion/widget/j;->i:F

    invoke-static {v0}, Ljava/lang/Float;->isNaN(F)Z

    move-result v0

    if-nez v0, :cond_0

    const-string v0, "alpha"

    invoke-virtual {p1, v0}, Ljava/util/HashSet;->add(Ljava/lang/Object;)Z

    :cond_0
    iget v0, p0, Landroidx/constraintlayout/motion/widget/j;->j:F

    invoke-static {v0}, Ljava/lang/Float;->isNaN(F)Z

    move-result v0

    if-nez v0, :cond_1

    const-string v0, "elevation"

    invoke-virtual {p1, v0}, Ljava/util/HashSet;->add(Ljava/lang/Object;)Z

    :cond_1
    iget v0, p0, Landroidx/constraintlayout/motion/widget/j;->k:F

    invoke-static {v0}, Ljava/lang/Float;->isNaN(F)Z

    move-result v0

    if-nez v0, :cond_2

    const-string v0, "rotation"

    invoke-virtual {p1, v0}, Ljava/util/HashSet;->add(Ljava/lang/Object;)Z

    :cond_2
    iget v0, p0, Landroidx/constraintlayout/motion/widget/j;->l:F

    invoke-static {v0}, Ljava/lang/Float;->isNaN(F)Z

    move-result v0

    if-nez v0, :cond_3

    const-string v0, "rotationX"

    invoke-virtual {p1, v0}, Ljava/util/HashSet;->add(Ljava/lang/Object;)Z

    :cond_3
    iget v0, p0, Landroidx/constraintlayout/motion/widget/j;->m:F

    invoke-static {v0}, Ljava/lang/Float;->isNaN(F)Z

    move-result v0

    if-nez v0, :cond_4

    const-string v0, "rotationY"

    invoke-virtual {p1, v0}, Ljava/util/HashSet;->add(Ljava/lang/Object;)Z

    :cond_4
    iget v0, p0, Landroidx/constraintlayout/motion/widget/j;->q:F

    invoke-static {v0}, Ljava/lang/Float;->isNaN(F)Z

    move-result v0

    if-nez v0, :cond_5

    const-string v0, "translationX"

    invoke-virtual {p1, v0}, Ljava/util/HashSet;->add(Ljava/lang/Object;)Z

    :cond_5
    iget v0, p0, Landroidx/constraintlayout/motion/widget/j;->r:F

    invoke-static {v0}, Ljava/lang/Float;->isNaN(F)Z

    move-result v0

    if-nez v0, :cond_6

    const-string v0, "translationY"

    invoke-virtual {p1, v0}, Ljava/util/HashSet;->add(Ljava/lang/Object;)Z

    :cond_6
    iget v0, p0, Landroidx/constraintlayout/motion/widget/j;->s:F

    invoke-static {v0}, Ljava/lang/Float;->isNaN(F)Z

    move-result v0

    if-nez v0, :cond_7

    const-string v0, "translationZ"

    invoke-virtual {p1, v0}, Ljava/util/HashSet;->add(Ljava/lang/Object;)Z

    :cond_7
    iget v0, p0, Landroidx/constraintlayout/motion/widget/j;->n:F

    invoke-static {v0}, Ljava/lang/Float;->isNaN(F)Z

    move-result v0

    if-nez v0, :cond_8

    const-string v0, "transitionPathRotate"

    invoke-virtual {p1, v0}, Ljava/util/HashSet;->add(Ljava/lang/Object;)Z

    :cond_8
    iget v0, p0, Landroidx/constraintlayout/motion/widget/j;->o:F

    invoke-static {v0}, Ljava/lang/Float;->isNaN(F)Z

    move-result v0

    if-nez v0, :cond_9

    const-string v0, "scaleX"

    invoke-virtual {p1, v0}, Ljava/util/HashSet;->add(Ljava/lang/Object;)Z

    :cond_9
    iget v0, p0, Landroidx/constraintlayout/motion/widget/j;->p:F

    invoke-static {v0}, Ljava/lang/Float;->isNaN(F)Z

    move-result v0

    if-nez v0, :cond_a

    const-string v0, "scaleY"

    invoke-virtual {p1, v0}, Ljava/util/HashSet;->add(Ljava/lang/Object;)Z

    :cond_a
    iget v0, p0, Landroidx/constraintlayout/motion/widget/j;->t:F

    invoke-static {v0}, Ljava/lang/Float;->isNaN(F)Z

    move-result v0

    if-nez v0, :cond_b

    const-string v0, "progress"

    invoke-virtual {p1, v0}, Ljava/util/HashSet;->add(Ljava/lang/Object;)Z

    :cond_b
    iget-object v0, p0, Landroidx/constraintlayout/motion/widget/d;->e:Ljava/util/HashMap;

    invoke-virtual {v0}, Ljava/util/HashMap;->size()I

    move-result v0

    if-lez v0, :cond_c

    iget-object v0, p0, Landroidx/constraintlayout/motion/widget/d;->e:Ljava/util/HashMap;

    invoke-virtual {v0}, Ljava/util/HashMap;->keySet()Ljava/util/Set;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_c

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/lang/String;

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "CUSTOM,"

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p1, v1}, Ljava/util/HashSet;->add(Ljava/lang/Object;)Z

    goto :goto_0

    :cond_c
    return-void
.end method

.method public e(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 1

    sget-object v0, Landroidx/constraintlayout/widget/R$styleable;->KeyTimeCycle:[I

    invoke-virtual {p1, p2, v0}, Landroid/content/Context;->obtainStyledAttributes(Landroid/util/AttributeSet;[I)Landroid/content/res/TypedArray;

    move-result-object p1

    invoke-static {p0, p1}, Landroidx/constraintlayout/motion/widget/j$a;->a(Landroidx/constraintlayout/motion/widget/j;Landroid/content/res/TypedArray;)V

    return-void
.end method

.method public h(Ljava/util/HashMap;)V
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/HashMap<",
            "Ljava/lang/String;",
            "Ljava/lang/Integer;",
            ">;)V"
        }
    .end annotation

    iget v0, p0, Landroidx/constraintlayout/motion/widget/j;->h:I

    const/4 v1, -0x1

    if-ne v0, v1, :cond_0

    return-void

    :cond_0
    iget v0, p0, Landroidx/constraintlayout/motion/widget/j;->i:F

    invoke-static {v0}, Ljava/lang/Float;->isNaN(F)Z

    move-result v0

    if-nez v0, :cond_1

    iget v0, p0, Landroidx/constraintlayout/motion/widget/j;->h:I

    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v0

    const-string v1, "alpha"

    invoke-virtual {p1, v1, v0}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    :cond_1
    iget v0, p0, Landroidx/constraintlayout/motion/widget/j;->j:F

    invoke-static {v0}, Ljava/lang/Float;->isNaN(F)Z

    move-result v0

    if-nez v0, :cond_2

    iget v0, p0, Landroidx/constraintlayout/motion/widget/j;->h:I

    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v0

    const-string v1, "elevation"

    invoke-virtual {p1, v1, v0}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    :cond_2
    iget v0, p0, Landroidx/constraintlayout/motion/widget/j;->k:F

    invoke-static {v0}, Ljava/lang/Float;->isNaN(F)Z

    move-result v0

    if-nez v0, :cond_3

    iget v0, p0, Landroidx/constraintlayout/motion/widget/j;->h:I

    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v0

    const-string v1, "rotation"

    invoke-virtual {p1, v1, v0}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    :cond_3
    iget v0, p0, Landroidx/constraintlayout/motion/widget/j;->l:F

    invoke-static {v0}, Ljava/lang/Float;->isNaN(F)Z

    move-result v0

    if-nez v0, :cond_4

    iget v0, p0, Landroidx/constraintlayout/motion/widget/j;->h:I

    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v0

    const-string v1, "rotationX"

    invoke-virtual {p1, v1, v0}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    :cond_4
    iget v0, p0, Landroidx/constraintlayout/motion/widget/j;->m:F

    invoke-static {v0}, Ljava/lang/Float;->isNaN(F)Z

    move-result v0

    if-nez v0, :cond_5

    iget v0, p0, Landroidx/constraintlayout/motion/widget/j;->h:I

    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v0

    const-string v1, "rotationY"

    invoke-virtual {p1, v1, v0}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    :cond_5
    iget v0, p0, Landroidx/constraintlayout/motion/widget/j;->q:F

    invoke-static {v0}, Ljava/lang/Float;->isNaN(F)Z

    move-result v0

    if-nez v0, :cond_6

    iget v0, p0, Landroidx/constraintlayout/motion/widget/j;->h:I

    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v0

    const-string v1, "translationX"

    invoke-virtual {p1, v1, v0}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    :cond_6
    iget v0, p0, Landroidx/constraintlayout/motion/widget/j;->r:F

    invoke-static {v0}, Ljava/lang/Float;->isNaN(F)Z

    move-result v0

    if-nez v0, :cond_7

    iget v0, p0, Landroidx/constraintlayout/motion/widget/j;->h:I

    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v0

    const-string v1, "translationY"

    invoke-virtual {p1, v1, v0}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    :cond_7
    iget v0, p0, Landroidx/constraintlayout/motion/widget/j;->s:F

    invoke-static {v0}, Ljava/lang/Float;->isNaN(F)Z

    move-result v0

    if-nez v0, :cond_8

    iget v0, p0, Landroidx/constraintlayout/motion/widget/j;->h:I

    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v0

    const-string v1, "translationZ"

    invoke-virtual {p1, v1, v0}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    :cond_8
    iget v0, p0, Landroidx/constraintlayout/motion/widget/j;->n:F

    invoke-static {v0}, Ljava/lang/Float;->isNaN(F)Z

    move-result v0

    if-nez v0, :cond_9

    iget v0, p0, Landroidx/constraintlayout/motion/widget/j;->h:I

    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v0

    const-string v1, "transitionPathRotate"

    invoke-virtual {p1, v1, v0}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    :cond_9
    iget v0, p0, Landroidx/constraintlayout/motion/widget/j;->o:F

    invoke-static {v0}, Ljava/lang/Float;->isNaN(F)Z

    move-result v0

    if-nez v0, :cond_a

    iget v0, p0, Landroidx/constraintlayout/motion/widget/j;->h:I

    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v0

    const-string v1, "scaleX"

    invoke-virtual {p1, v1, v0}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    :cond_a
    iget v0, p0, Landroidx/constraintlayout/motion/widget/j;->o:F

    invoke-static {v0}, Ljava/lang/Float;->isNaN(F)Z

    move-result v0

    if-nez v0, :cond_b

    iget v0, p0, Landroidx/constraintlayout/motion/widget/j;->h:I

    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v0

    const-string v1, "scaleY"

    invoke-virtual {p1, v1, v0}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    :cond_b
    iget v0, p0, Landroidx/constraintlayout/motion/widget/j;->t:F

    invoke-static {v0}, Ljava/lang/Float;->isNaN(F)Z

    move-result v0

    if-nez v0, :cond_c

    iget v0, p0, Landroidx/constraintlayout/motion/widget/j;->h:I

    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v0

    const-string v1, "progress"

    invoke-virtual {p1, v1, v0}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    :cond_c
    iget-object v0, p0, Landroidx/constraintlayout/motion/widget/d;->e:Ljava/util/HashMap;

    invoke-virtual {v0}, Ljava/util/HashMap;->size()I

    move-result v0

    if-lez v0, :cond_d

    iget-object v0, p0, Landroidx/constraintlayout/motion/widget/d;->e:Ljava/util/HashMap;

    invoke-virtual {v0}, Ljava/util/HashMap;->keySet()Ljava/util/Set;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_d

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/lang/String;

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "CUSTOM,"

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    iget v2, p0, Landroidx/constraintlayout/motion/widget/j;->h:I

    invoke-static {v2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v2

    invoke-virtual {p1, v1, v2}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_0

    :cond_d
    return-void
.end method
