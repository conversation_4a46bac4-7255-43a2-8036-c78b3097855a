.class public Landroidx/constraintlayout/motion/widget/t$a;
.super Ljava/lang/Object;

# interfaces
.implements Landroidx/constraintlayout/widget/d$a;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Landroidx/constraintlayout/motion/widget/t;->h(Landroidx/constraintlayout/motion/widget/s;Z)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field public final synthetic a:Landroidx/constraintlayout/motion/widget/s;

.field public final synthetic b:I

.field public final synthetic c:Z

.field public final synthetic d:I

.field public final synthetic e:Landroidx/constraintlayout/motion/widget/t;


# direct methods
.method public constructor <init>(Landroidx/constraintlayout/motion/widget/t;Landroidx/constraintlayout/motion/widget/s;IZI)V
    .locals 0

    iput-object p1, p0, Landroidx/constraintlayout/motion/widget/t$a;->e:Landroidx/constraintlayout/motion/widget/t;

    iput-object p2, p0, Landroidx/constraintlayout/motion/widget/t$a;->a:Landroidx/constraintlayout/motion/widget/s;

    iput p3, p0, Landroidx/constraintlayout/motion/widget/t$a;->b:I

    iput-boolean p4, p0, Landroidx/constraintlayout/motion/widget/t$a;->c:Z

    iput p5, p0, Landroidx/constraintlayout/motion/widget/t$a;->d:I

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
