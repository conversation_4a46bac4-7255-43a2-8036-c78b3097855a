.class public final Landroidx/constraintlayout/widget/R$attr;
.super Ljava/lang/Object;


# static fields
.field public static SharedValue:I = 0x7f040005

.field public static SharedValueId:I = 0x7f040006

.field public static actionBarDivider:I = 0x7f040008

.field public static actionBarItemBackground:I = 0x7f040009

.field public static actionBarPopupTheme:I = 0x7f04000a

.field public static actionBarSize:I = 0x7f04000b

.field public static actionBarSplitStyle:I = 0x7f04000c

.field public static actionBarStyle:I = 0x7f04000d

.field public static actionBarTabBarStyle:I = 0x7f04000e

.field public static actionBarTabStyle:I = 0x7f04000f

.field public static actionBarTabTextStyle:I = 0x7f040010

.field public static actionBarTheme:I = 0x7f040011

.field public static actionBarWidgetTheme:I = 0x7f040012

.field public static actionButtonStyle:I = 0x7f040013

.field public static actionDropDownStyle:I = 0x7f040014

.field public static actionLayout:I = 0x7f040015

.field public static actionMenuTextAppearance:I = 0x7f040016

.field public static actionMenuTextColor:I = 0x7f040017

.field public static actionModeBackground:I = 0x7f040018

.field public static actionModeCloseButtonStyle:I = 0x7f040019

.field public static actionModeCloseDrawable:I = 0x7f04001b

.field public static actionModeCopyDrawable:I = 0x7f04001c

.field public static actionModeCutDrawable:I = 0x7f04001d

.field public static actionModeFindDrawable:I = 0x7f04001e

.field public static actionModePasteDrawable:I = 0x7f04001f

.field public static actionModePopupWindowStyle:I = 0x7f040020

.field public static actionModeSelectAllDrawable:I = 0x7f040021

.field public static actionModeShareDrawable:I = 0x7f040022

.field public static actionModeSplitBackground:I = 0x7f040023

.field public static actionModeStyle:I = 0x7f040024

.field public static actionModeWebSearchDrawable:I = 0x7f040026

.field public static actionOverflowButtonStyle:I = 0x7f040027

.field public static actionOverflowMenuStyle:I = 0x7f040028

.field public static actionProviderClass:I = 0x7f040029

.field public static actionViewClass:I = 0x7f04002b

.field public static activityChooserViewStyle:I = 0x7f04002e

.field public static alertDialogButtonGroupStyle:I = 0x7f040039

.field public static alertDialogCenterButtons:I = 0x7f04003a

.field public static alertDialogStyle:I = 0x7f04003b

.field public static alertDialogTheme:I = 0x7f04003c

.field public static allowStacking:I = 0x7f04003f

.field public static alpha:I = 0x7f040040

.field public static alphabeticModifiers:I = 0x7f040041

.field public static altSrc:I = 0x7f040042

.field public static animateCircleAngleTo:I = 0x7f040046

.field public static animateRelativeTo:I = 0x7f040049

.field public static applyMotionScene:I = 0x7f04004d

.field public static arcMode:I = 0x7f04004e

.field public static arrowHeadLength:I = 0x7f040051

.field public static arrowShaftLength:I = 0x7f040058

.field public static attributeName:I = 0x7f04005c

.field public static autoCompleteMode:I = 0x7f04005e

.field public static autoCompleteTextViewStyle:I = 0x7f04005f

.field public static autoSizeMaxTextSize:I = 0x7f040061

.field public static autoSizeMinTextSize:I = 0x7f040062

.field public static autoSizePresetSizes:I = 0x7f040063

.field public static autoSizeStepGranularity:I = 0x7f040064

.field public static autoSizeTextType:I = 0x7f040065

.field public static autoTransition:I = 0x7f040066

.field public static background:I = 0x7f04006a

.field public static backgroundSplit:I = 0x7f040071

.field public static backgroundStacked:I = 0x7f040072

.field public static backgroundTint:I = 0x7f040073

.field public static backgroundTintMode:I = 0x7f040074

.field public static barLength:I = 0x7f04009d

.field public static barrierAllowsGoneWidgets:I = 0x7f0400a0

.field public static barrierDirection:I = 0x7f0400a1

.field public static barrierMargin:I = 0x7f0400a2

.field public static blendSrc:I = 0x7f0401b3

.field public static borderRound:I = 0x7f0401b5

.field public static borderRoundPercent:I = 0x7f0401b6

.field public static borderlessButtonStyle:I = 0x7f0401be

.field public static brightness:I = 0x7f0401da

.field public static buttonBarButtonStyle:I = 0x7f0401de

.field public static buttonBarNegativeButtonStyle:I = 0x7f0401df

.field public static buttonBarNeutralButtonStyle:I = 0x7f0401e0

.field public static buttonBarPositiveButtonStyle:I = 0x7f0401e1

.field public static buttonBarStyle:I = 0x7f0401e2

.field public static buttonCompat:I = 0x7f0401e3

.field public static buttonGravity:I = 0x7f0401e4

.field public static buttonIconDimen:I = 0x7f0401e6

.field public static buttonPanelSideLayout:I = 0x7f0401e9

.field public static buttonStyle:I = 0x7f0401eb

.field public static buttonStyleSmall:I = 0x7f0401ec

.field public static buttonTint:I = 0x7f0401ed

.field public static buttonTintMode:I = 0x7f0401ee

.field public static carousel_backwardTransition:I = 0x7f0401ff

.field public static carousel_emptyViewsBehavior:I = 0x7f040200

.field public static carousel_firstView:I = 0x7f040201

.field public static carousel_forwardTransition:I = 0x7f040202

.field public static carousel_infinite:I = 0x7f040203

.field public static carousel_nextState:I = 0x7f040204

.field public static carousel_previousState:I = 0x7f040205

.field public static carousel_touchUpMode:I = 0x7f040206

.field public static carousel_touchUp_dampeningFactor:I = 0x7f040207

.field public static carousel_touchUp_velocityThreshold:I = 0x7f040208

.field public static chainUseRtl:I = 0x7f04020a

.field public static checkboxStyle:I = 0x7f04020e

.field public static checkedTextViewStyle:I = 0x7f040219

.field public static circleRadius:I = 0x7f04022f

.field public static circularflow_angles:I = 0x7f040231

.field public static circularflow_defaultAngle:I = 0x7f040232

.field public static circularflow_defaultRadius:I = 0x7f040233

.field public static circularflow_radiusInDP:I = 0x7f040234

.field public static circularflow_viewCenter:I = 0x7f040235

.field public static clearsTag:I = 0x7f040242

.field public static clickAction:I = 0x7f040243

.field public static closeIcon:I = 0x7f040248

.field public static closeItemLayout:I = 0x7f04024f

.field public static collapseContentDescription:I = 0x7f040250

.field public static collapseIcon:I = 0x7f040251

.field public static color:I = 0x7f04025b

.field public static colorAccent:I = 0x7f04025c

.field public static colorBackgroundFloating:I = 0x7f04025d

.field public static colorButtonNormal:I = 0x7f04025e

.field public static colorControlActivated:I = 0x7f040260

.field public static colorControlHighlight:I = 0x7f040261

.field public static colorControlNormal:I = 0x7f040262

.field public static colorError:I = 0x7f040263

.field public static colorPrimary:I = 0x7f04027c

.field public static colorPrimaryDark:I = 0x7f04027e

.field public static colorSwitchThumbNormal:I = 0x7f040294

.field public static commitIcon:I = 0x7f040299

.field public static constraintRotate:I = 0x7f04029b

.field public static constraintSet:I = 0x7f04029c

.field public static constraintSetEnd:I = 0x7f04029d

.field public static constraintSetStart:I = 0x7f04029e

.field public static constraint_referenced_ids:I = 0x7f04029f

.field public static constraint_referenced_tags:I = 0x7f0402a0

.field public static constraints:I = 0x7f0402a1

.field public static content:I = 0x7f0402a2

.field public static contentDescription:I = 0x7f0402a3

.field public static contentInsetEnd:I = 0x7f0402a4

.field public static contentInsetEndWithActions:I = 0x7f0402a5

.field public static contentInsetLeft:I = 0x7f0402a6

.field public static contentInsetRight:I = 0x7f0402a7

.field public static contentInsetStart:I = 0x7f0402a8

.field public static contentInsetStartWithNavigation:I = 0x7f0402a9

.field public static contrast:I = 0x7f0402b2

.field public static controlBackground:I = 0x7f0402b3

.field public static crossfade:I = 0x7f0402ce

.field public static currentState:I = 0x7f0402cf

.field public static curveFit:I = 0x7f0402d2

.field public static customBoolean:I = 0x7f0402d3

.field public static customColorDrawableValue:I = 0x7f0402d4

.field public static customColorValue:I = 0x7f0402d5

.field public static customDimension:I = 0x7f0402d6

.field public static customFloatValue:I = 0x7f0402d7

.field public static customIntegerValue:I = 0x7f0402d8

.field public static customNavigationLayout:I = 0x7f0402d9

.field public static customPixelDimension:I = 0x7f0402da

.field public static customReference:I = 0x7f0402db

.field public static customStringValue:I = 0x7f0402dc

.field public static defaultDuration:I = 0x7f0402e3

.field public static defaultQueryHint:I = 0x7f0402e6

.field public static defaultState:I = 0x7f0402e8

.field public static deltaPolarAngle:I = 0x7f0402ea

.field public static deltaPolarRadius:I = 0x7f0402eb

.field public static deriveConstraintsFrom:I = 0x7f0402ec

.field public static dialogCornerRadius:I = 0x7f0402f1

.field public static dialogPreferredPadding:I = 0x7f0402f2

.field public static dialogTheme:I = 0x7f0402f3

.field public static displayOptions:I = 0x7f0402f4

.field public static divider:I = 0x7f0402f5

.field public static dividerHorizontal:I = 0x7f0402fa

.field public static dividerPadding:I = 0x7f0402fd

.field public static dividerVertical:I = 0x7f0402ff

.field public static dragDirection:I = 0x7f040300

.field public static dragScale:I = 0x7f040301

.field public static dragThreshold:I = 0x7f040302

.field public static drawPath:I = 0x7f040303

.field public static drawableBottomCompat:I = 0x7f040304

.field public static drawableEndCompat:I = 0x7f040305

.field public static drawableLeftCompat:I = 0x7f040306

.field public static drawableRightCompat:I = 0x7f040307

.field public static drawableSize:I = 0x7f040308

.field public static drawableStartCompat:I = 0x7f040309

.field public static drawableTint:I = 0x7f04030a

.field public static drawableTintMode:I = 0x7f04030b

.field public static drawableTopCompat:I = 0x7f04030c

.field public static drawerArrowStyle:I = 0x7f04030d

.field public static dropDownListViewStyle:I = 0x7f040311

.field public static dropdownListPreferredItemHeight:I = 0x7f040312

.field public static duration:I = 0x7f040313

.field public static editTextBackground:I = 0x7f040315

.field public static editTextColor:I = 0x7f040316

.field public static editTextStyle:I = 0x7f040317

.field public static elevation:I = 0x7f040318

.field public static expandActivityOverflowButtonDrawable:I = 0x7f040349

.field public static firstBaselineToTopHeight:I = 0x7f04036c

.field public static flow_firstHorizontalBias:I = 0x7f040386

.field public static flow_firstHorizontalStyle:I = 0x7f040387

.field public static flow_firstVerticalBias:I = 0x7f040388

.field public static flow_firstVerticalStyle:I = 0x7f040389

.field public static flow_horizontalAlign:I = 0x7f04038a

.field public static flow_horizontalBias:I = 0x7f04038b

.field public static flow_horizontalGap:I = 0x7f04038c

.field public static flow_horizontalStyle:I = 0x7f04038d

.field public static flow_lastHorizontalBias:I = 0x7f04038e

.field public static flow_lastHorizontalStyle:I = 0x7f04038f

.field public static flow_lastVerticalBias:I = 0x7f040390

.field public static flow_lastVerticalStyle:I = 0x7f040391

.field public static flow_maxElementsWrap:I = 0x7f040392

.field public static flow_padding:I = 0x7f040393

.field public static flow_verticalAlign:I = 0x7f040394

.field public static flow_verticalBias:I = 0x7f040395

.field public static flow_verticalGap:I = 0x7f040396

.field public static flow_verticalStyle:I = 0x7f040397

.field public static flow_wrapMode:I = 0x7f040398

.field public static font:I = 0x7f040399

.field public static fontFamily:I = 0x7f04039a

.field public static fontProviderAuthority:I = 0x7f04039b

.field public static fontProviderCerts:I = 0x7f04039c

.field public static fontProviderFetchStrategy:I = 0x7f04039d

.field public static fontProviderFetchTimeout:I = 0x7f04039e

.field public static fontProviderPackage:I = 0x7f04039f

.field public static fontProviderQuery:I = 0x7f0403a0

.field public static fontStyle:I = 0x7f0403a2

.field public static fontVariationSettings:I = 0x7f0403a3

.field public static fontWeight:I = 0x7f0403a4

.field public static framePosition:I = 0x7f0403a8

.field public static gapBetweenBars:I = 0x7f0403aa

.field public static goIcon:I = 0x7f0403ad

.field public static guidelineUseRtl:I = 0x7f0403b7

.field public static height:I = 0x7f0403bb

.field public static hideOnContentScroll:I = 0x7f0403c3

.field public static homeAsUpIndicator:I = 0x7f0403cb

.field public static homeLayout:I = 0x7f0403cc

.field public static icon:I = 0x7f0403d0

.field public static iconTint:I = 0x7f0403d7

.field public static iconTintMode:I = 0x7f0403d8

.field public static iconifiedByDefault:I = 0x7f0403d9

.field public static ifTagNotSet:I = 0x7f0403da

.field public static ifTagSet:I = 0x7f0403db

.field public static imageButtonStyle:I = 0x7f0403de

.field public static imagePanX:I = 0x7f0403df

.field public static imagePanY:I = 0x7f0403e0

.field public static imageRotate:I = 0x7f0403e1

.field public static imageZoom:I = 0x7f0403e2

.field public static indeterminateProgressStyle:I = 0x7f0403e4

.field public static initialActivityCount:I = 0x7f0403f4

.field public static isLightTheme:I = 0x7f0403f9

.field public static itemPadding:I = 0x7f040431

.field public static keyPositionType:I = 0x7f040457

.field public static lastBaselineToBottomHeight:I = 0x7f04045f

.field public static layout:I = 0x7f040466

.field public static layoutDescription:I = 0x7f040467

.field public static layoutDuringTransition:I = 0x7f040468

.field public static layout_constrainedHeight:I = 0x7f040470

.field public static layout_constrainedWidth:I = 0x7f040471

.field public static layout_constraintBaseline_creator:I = 0x7f040472

.field public static layout_constraintBaseline_toBaselineOf:I = 0x7f040473

.field public static layout_constraintBaseline_toBottomOf:I = 0x7f040474

.field public static layout_constraintBaseline_toTopOf:I = 0x7f040475

.field public static layout_constraintBottom_creator:I = 0x7f040476

.field public static layout_constraintBottom_toBottomOf:I = 0x7f040477

.field public static layout_constraintBottom_toTopOf:I = 0x7f040478

.field public static layout_constraintCircle:I = 0x7f040479

.field public static layout_constraintCircleAngle:I = 0x7f04047a

.field public static layout_constraintCircleRadius:I = 0x7f04047b

.field public static layout_constraintDimensionRatio:I = 0x7f04047c

.field public static layout_constraintEnd_toEndOf:I = 0x7f04047d

.field public static layout_constraintEnd_toStartOf:I = 0x7f04047e

.field public static layout_constraintGuide_begin:I = 0x7f04047f

.field public static layout_constraintGuide_end:I = 0x7f040480

.field public static layout_constraintGuide_percent:I = 0x7f040481

.field public static layout_constraintHeight:I = 0x7f040482

.field public static layout_constraintHeight_default:I = 0x7f040483

.field public static layout_constraintHeight_max:I = 0x7f040484

.field public static layout_constraintHeight_min:I = 0x7f040485

.field public static layout_constraintHeight_percent:I = 0x7f040486

.field public static layout_constraintHorizontal_bias:I = 0x7f040487

.field public static layout_constraintHorizontal_chainStyle:I = 0x7f040488

.field public static layout_constraintHorizontal_weight:I = 0x7f040489

.field public static layout_constraintLeft_creator:I = 0x7f04048a

.field public static layout_constraintLeft_toLeftOf:I = 0x7f04048b

.field public static layout_constraintLeft_toRightOf:I = 0x7f04048c

.field public static layout_constraintRight_creator:I = 0x7f04048d

.field public static layout_constraintRight_toLeftOf:I = 0x7f04048e

.field public static layout_constraintRight_toRightOf:I = 0x7f04048f

.field public static layout_constraintStart_toEndOf:I = 0x7f040490

.field public static layout_constraintStart_toStartOf:I = 0x7f040491

.field public static layout_constraintTag:I = 0x7f040492

.field public static layout_constraintTop_creator:I = 0x7f040493

.field public static layout_constraintTop_toBottomOf:I = 0x7f040494

.field public static layout_constraintTop_toTopOf:I = 0x7f040495

.field public static layout_constraintVertical_bias:I = 0x7f040496

.field public static layout_constraintVertical_chainStyle:I = 0x7f040497

.field public static layout_constraintVertical_weight:I = 0x7f040498

.field public static layout_constraintWidth:I = 0x7f040499

.field public static layout_constraintWidth_default:I = 0x7f04049a

.field public static layout_constraintWidth_max:I = 0x7f04049b

.field public static layout_constraintWidth_min:I = 0x7f04049c

.field public static layout_constraintWidth_percent:I = 0x7f04049d

.field public static layout_editor_absoluteX:I = 0x7f04049f

.field public static layout_editor_absoluteY:I = 0x7f0404a0

.field public static layout_goneMarginBaseline:I = 0x7f0404a6

.field public static layout_goneMarginBottom:I = 0x7f0404a7

.field public static layout_goneMarginEnd:I = 0x7f0404a8

.field public static layout_goneMarginLeft:I = 0x7f0404a9

.field public static layout_goneMarginRight:I = 0x7f0404aa

.field public static layout_goneMarginStart:I = 0x7f0404ab

.field public static layout_goneMarginTop:I = 0x7f0404ac

.field public static layout_marginBaseline:I = 0x7f0404af

.field public static layout_optimizationLevel:I = 0x7f0404b4

.field public static layout_wrapBehaviorInParent:I = 0x7f0404bb

.field public static limitBoundsTo:I = 0x7f0404c0

.field public static lineHeight:I = 0x7f0404c3

.field public static listChoiceBackgroundIndicator:I = 0x7f0404c6

.field public static listChoiceIndicatorMultipleAnimated:I = 0x7f0404c7

.field public static listChoiceIndicatorSingleAnimated:I = 0x7f0404c8

.field public static listDividerAlertDialog:I = 0x7f0404c9

.field public static listItemLayout:I = 0x7f0404ca

.field public static listLayout:I = 0x7f0404cb

.field public static listMenuViewStyle:I = 0x7f0404cc

.field public static listPopupWindowStyle:I = 0x7f0404cd

.field public static listPreferredItemHeight:I = 0x7f0404ce

.field public static listPreferredItemHeightLarge:I = 0x7f0404cf

.field public static listPreferredItemHeightSmall:I = 0x7f0404d0

.field public static listPreferredItemPaddingEnd:I = 0x7f0404d1

.field public static listPreferredItemPaddingLeft:I = 0x7f0404d2

.field public static listPreferredItemPaddingRight:I = 0x7f0404d3

.field public static listPreferredItemPaddingStart:I = 0x7f0404d4

.field public static logo:I = 0x7f0404d6

.field public static logoDescription:I = 0x7f0404d8

.field public static maxAcceleration:I = 0x7f040526

.field public static maxButtonHeight:I = 0x7f040528

.field public static maxHeight:I = 0x7f04052a

.field public static maxVelocity:I = 0x7f04052f

.field public static maxWidth:I = 0x7f040530

.field public static measureWithLargestChild:I = 0x7f040538

.field public static menu:I = 0x7f040539

.field public static methodName:I = 0x7f04053d

.field public static minHeight:I = 0x7f04053f

.field public static minWidth:I = 0x7f040543

.field public static mock_diagonalsColor:I = 0x7f040544

.field public static mock_label:I = 0x7f040545

.field public static mock_labelBackgroundColor:I = 0x7f040546

.field public static mock_labelColor:I = 0x7f040547

.field public static mock_showDiagonals:I = 0x7f040548

.field public static mock_showLabel:I = 0x7f040549

.field public static motionDebug:I = 0x7f04054a

.field public static motionEffect_alpha:I = 0x7f040567

.field public static motionEffect_end:I = 0x7f040568

.field public static motionEffect_move:I = 0x7f040569

.field public static motionEffect_start:I = 0x7f04056a

.field public static motionEffect_strict:I = 0x7f04056b

.field public static motionEffect_translationX:I = 0x7f04056c

.field public static motionEffect_translationY:I = 0x7f04056d

.field public static motionEffect_viewTransition:I = 0x7f04056e

.field public static motionInterpolator:I = 0x7f04056f

.field public static motionPathRotate:I = 0x7f040571

.field public static motionProgress:I = 0x7f040572

.field public static motionStagger:I = 0x7f040573

.field public static motionTarget:I = 0x7f040574

.field public static motion_postLayoutCollision:I = 0x7f040575

.field public static motion_triggerOnCollision:I = 0x7f040576

.field public static moveWhenScrollAtTop:I = 0x7f040577

.field public static multiChoiceItemLayout:I = 0x7f040578

.field public static navigationContentDescription:I = 0x7f04057a

.field public static navigationIcon:I = 0x7f04057b

.field public static navigationMode:I = 0x7f04057d

.field public static nestedScrollFlags:I = 0x7f040580

.field public static numericModifiers:I = 0x7f04058b

.field public static onCross:I = 0x7f04058d

.field public static onHide:I = 0x7f04058e

.field public static onNegativeCross:I = 0x7f04058f

.field public static onPositiveCross:I = 0x7f040590

.field public static onShow:I = 0x7f040591

.field public static onStateTransition:I = 0x7f040592

.field public static onTouchUp:I = 0x7f040593

.field public static overlapAnchor:I = 0x7f040594

.field public static overlay:I = 0x7f040595

.field public static paddingBottomNoButtons:I = 0x7f040596

.field public static paddingEnd:I = 0x7f040598

.field public static paddingStart:I = 0x7f04059b

.field public static paddingTopNoTitle:I = 0x7f04059d

.field public static panelBackground:I = 0x7f04059f

.field public static panelMenuListTheme:I = 0x7f0405a0

.field public static panelMenuListWidth:I = 0x7f0405a1

.field public static pathMotionArc:I = 0x7f0405a7

.field public static path_percent:I = 0x7f0405a8

.field public static percentHeight:I = 0x7f0405a9

.field public static percentWidth:I = 0x7f0405ac

.field public static percentX:I = 0x7f0405ad

.field public static percentY:I = 0x7f0405ae

.field public static perpendicularPath_percent:I = 0x7f0405af

.field public static pivotAnchor:I = 0x7f0405b0

.field public static placeholder_emptyVisibility:I = 0x7f0405b5

.field public static polarRelativeTo:I = 0x7f0405ba

.field public static popupMenuStyle:I = 0x7f0405d2

.field public static popupTheme:I = 0x7f0405d3

.field public static popupWindowStyle:I = 0x7f0405d4

.field public static preserveIconSpacing:I = 0x7f0405d9

.field public static progressBarPadding:I = 0x7f0405dd

.field public static progressBarStyle:I = 0x7f0405de

.field public static quantizeMotionInterpolator:I = 0x7f0405e7

.field public static quantizeMotionPhase:I = 0x7f0405e8

.field public static quantizeMotionSteps:I = 0x7f0405e9

.field public static queryBackground:I = 0x7f0405ea

.field public static queryHint:I = 0x7f0405eb

.field public static radioButtonStyle:I = 0x7f0405ee

.field public static ratingBarStyle:I = 0x7f0405f1

.field public static ratingBarStyleIndicator:I = 0x7f0405f2

.field public static ratingBarStyleSmall:I = 0x7f0405f3

.field public static reactiveGuide_animateChange:I = 0x7f0405f4

.field public static reactiveGuide_applyToAllConstraintSets:I = 0x7f0405f5

.field public static reactiveGuide_applyToConstraintSet:I = 0x7f0405f6

.field public static reactiveGuide_valueId:I = 0x7f0405f7

.field public static region_heightLessThan:I = 0x7f040603

.field public static region_heightMoreThan:I = 0x7f040604

.field public static region_widthLessThan:I = 0x7f040605

.field public static region_widthMoreThan:I = 0x7f040606

.field public static rotationCenterId:I = 0x7f040616

.field public static round:I = 0x7f040617

.field public static roundPercent:I = 0x7f040619

.field public static saturation:I = 0x7f04061b

.field public static scaleFromTextSize:I = 0x7f04061c

.field public static searchHintIcon:I = 0x7f04062b

.field public static searchIcon:I = 0x7f04062c

.field public static searchViewStyle:I = 0x7f04062e

.field public static seekBarStyle:I = 0x7f040631

.field public static selectableItemBackground:I = 0x7f040632

.field public static selectableItemBackgroundBorderless:I = 0x7f040633

.field public static setsTag:I = 0x7f040637

.field public static showAsAction:I = 0x7f04065e

.field public static showDividers:I = 0x7f040663

.field public static showPaths:I = 0x7f040668

.field public static showText:I = 0x7f04066a

.field public static showTitle:I = 0x7f04066b

.field public static singleChoiceItemLayout:I = 0x7f04067d

.field public static sizePercent:I = 0x7f040680

.field public static spinBars:I = 0x7f040686

.field public static spinnerDropDownItemStyle:I = 0x7f040687

.field public static spinnerStyle:I = 0x7f040688

.field public static splitTrack:I = 0x7f04068e

.field public static springBoundary:I = 0x7f04068f

.field public static springDamping:I = 0x7f040690

.field public static springMass:I = 0x7f040691

.field public static springStiffness:I = 0x7f040692

.field public static springStopThreshold:I = 0x7f040693

.field public static srcCompat:I = 0x7f040694

.field public static staggered:I = 0x7f0406a3

.field public static state_above_anchor:I = 0x7f0406ae

.field public static subMenuArrow:I = 0x7f0406bf

.field public static submitBackground:I = 0x7f0406c4

.field public static subtitle:I = 0x7f0406c5

.field public static subtitleTextAppearance:I = 0x7f0406c7

.field public static subtitleTextColor:I = 0x7f0406c8

.field public static subtitleTextStyle:I = 0x7f0406c9

.field public static suggestionRowLayout:I = 0x7f0406cd

.field public static switchMinWidth:I = 0x7f0406d0

.field public static switchPadding:I = 0x7f0406d1

.field public static switchStyle:I = 0x7f0406d2

.field public static switchTextAppearance:I = 0x7f0406d3

.field public static targetId:I = 0x7f0406f7

.field public static telltales_tailColor:I = 0x7f0406f9

.field public static telltales_tailScale:I = 0x7f0406fa

.field public static telltales_velocityMode:I = 0x7f0406fb

.field public static textAllCaps:I = 0x7f0406fd

.field public static textAppearanceLargePopupMenu:I = 0x7f040714

.field public static textAppearanceListItem:I = 0x7f040716

.field public static textAppearanceListItemSecondary:I = 0x7f040717

.field public static textAppearanceListItemSmall:I = 0x7f040718

.field public static textAppearancePopupMenuHeader:I = 0x7f04071a

.field public static textAppearanceSearchResultSubtitle:I = 0x7f04071b

.field public static textAppearanceSearchResultTitle:I = 0x7f04071c

.field public static textAppearanceSmallPopupMenu:I = 0x7f04071d

.field public static textBackground:I = 0x7f040723

.field public static textBackgroundPanX:I = 0x7f040724

.field public static textBackgroundPanY:I = 0x7f040725

.field public static textBackgroundRotate:I = 0x7f040726

.field public static textBackgroundZoom:I = 0x7f040727

.field public static textColorAlertDialogListItem:I = 0x7f040728

.field public static textColorSearchUrl:I = 0x7f040729

.field public static textFillColor:I = 0x7f04072b

.field public static textLocale:I = 0x7f040734

.field public static textOutlineColor:I = 0x7f040735

.field public static textOutlineThickness:I = 0x7f040736

.field public static textPanX:I = 0x7f040737

.field public static textPanY:I = 0x7f040738

.field public static textureBlurFactor:I = 0x7f04073b

.field public static textureEffect:I = 0x7f04073c

.field public static textureHeight:I = 0x7f04073d

.field public static textureWidth:I = 0x7f04073e

.field public static theme:I = 0x7f04073f

.field public static thickness:I = 0x7f040740

.field public static thumbTextPadding:I = 0x7f04074b

.field public static thumbTint:I = 0x7f04074c

.field public static thumbTintMode:I = 0x7f04074d

.field public static tickMark:I = 0x7f040753

.field public static tickMarkTint:I = 0x7f040754

.field public static tickMarkTintMode:I = 0x7f040755

.field public static tint:I = 0x7f04075a

.field public static tintMode:I = 0x7f04075b

.field public static title:I = 0x7f040760

.field public static titleMargin:I = 0x7f040766

.field public static titleMarginBottom:I = 0x7f040767

.field public static titleMarginEnd:I = 0x7f040768

.field public static titleMarginStart:I = 0x7f040769

.field public static titleMarginTop:I = 0x7f04076a

.field public static titleMargins:I = 0x7f04076b

.field public static titleTextAppearance:I = 0x7f04076e

.field public static titleTextColor:I = 0x7f04076f

.field public static titleTextStyle:I = 0x7f040772

.field public static toolbarNavigationButtonStyle:I = 0x7f040776

.field public static toolbarStyle:I = 0x7f040777

.field public static tooltipForegroundColor:I = 0x7f040779

.field public static tooltipFrameBackground:I = 0x7f04077a

.field public static tooltipText:I = 0x7f04077c

.field public static touchAnchorId:I = 0x7f040788

.field public static touchAnchorSide:I = 0x7f040789

.field public static touchRegionId:I = 0x7f04078a

.field public static track:I = 0x7f04078c

.field public static trackTint:I = 0x7f040798

.field public static trackTintMode:I = 0x7f040799

.field public static transformPivotTarget:I = 0x7f04079a

.field public static transitionDisable:I = 0x7f04079b

.field public static transitionEasing:I = 0x7f04079c

.field public static transitionFlags:I = 0x7f04079d

.field public static transitionPathRotate:I = 0x7f04079e

.field public static triggerId:I = 0x7f0407a0

.field public static triggerReceiver:I = 0x7f0407a1

.field public static triggerSlack:I = 0x7f0407a2

.field public static ttcIndex:I = 0x7f0407a3

.field public static upDuration:I = 0x7f0407ad

.field public static viewInflaterClass:I = 0x7f0407b9

.field public static viewTransitionMode:I = 0x7f0407ba

.field public static viewTransitionOnCross:I = 0x7f0407bb

.field public static viewTransitionOnNegativeCross:I = 0x7f0407bc

.field public static viewTransitionOnPositiveCross:I = 0x7f0407bd

.field public static visibilityMode:I = 0x7f0407be

.field public static voiceIcon:I = 0x7f0407bf

.field public static warmth:I = 0x7f0407c0

.field public static waveDecay:I = 0x7f0407c1

.field public static waveOffset:I = 0x7f0407c2

.field public static wavePeriod:I = 0x7f0407c3

.field public static wavePhase:I = 0x7f0407c4

.field public static waveShape:I = 0x7f0407c5

.field public static waveVariesBy:I = 0x7f0407c6

.field public static windowActionBar:I = 0x7f0407cd

.field public static windowActionBarOverlay:I = 0x7f0407ce

.field public static windowActionModeOverlay:I = 0x7f0407cf

.field public static windowFixedHeightMajor:I = 0x7f0407d0

.field public static windowFixedHeightMinor:I = 0x7f0407d1

.field public static windowFixedWidthMajor:I = 0x7f0407d2

.field public static windowFixedWidthMinor:I = 0x7f0407d3

.field public static windowMinWidthMajor:I = 0x7f0407d4

.field public static windowMinWidthMinor:I = 0x7f0407d5

.field public static windowNoTitle:I = 0x7f0407d6


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
