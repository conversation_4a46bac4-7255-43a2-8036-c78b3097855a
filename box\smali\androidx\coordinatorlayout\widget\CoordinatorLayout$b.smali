.class public interface abstract Landroidx/coordinatorlayout/widget/CoordinatorLayout$b;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/coordinatorlayout/widget/CoordinatorLayout;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "b"
.end annotation


# virtual methods
.method public abstract getBehavior()Landroidx/coordinatorlayout/widget/CoordinatorLayout$c;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end method
