.class public final Landroidx/core/splashscreen/R$attr;
.super Ljava/lang/Object;


# static fields
.field public static postSplashScreenTheme:I = 0x7f0405d5

.field public static splashScreenIconSize:I = 0x7f040689

.field public static windowSplashScreenAnimatedIcon:I = 0x7f0407d7

.field public static windowSplashScreenAnimationDuration:I = 0x7f0407d8

.field public static windowSplashScreenBackground:I = 0x7f0407d9

.field public static windowSplashScreenIconBackgroundColor:I = 0x7f0407da


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
