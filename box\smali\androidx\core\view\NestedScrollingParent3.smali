.class public interface abstract Landroidx/core/view/NestedScrollingParent3;
.super Ljava/lang/Object;

# interfaces
.implements Landroidx/core/view/NestedScrollingParent2;


# virtual methods
.method public abstract onNestedScroll(Landroid/view/View;IIIII[I)V
    .param p1    # Landroid/view/View;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p7    # [I
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
.end method
