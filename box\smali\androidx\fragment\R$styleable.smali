.class public final Landroidx/fragment/R$styleable;
.super Ljava/lang/Object;


# static fields
.field public static Fragment:[I = null

.field public static FragmentContainerView:[I = null

.field public static FragmentContainerView_android_name:I = 0x0

.field public static FragmentContainerView_android_tag:I = 0x1

.field public static Fragment_android_id:I = 0x1

.field public static Fragment_android_name:I = 0x0

.field public static Fragment_android_tag:I = 0x2


# direct methods
.method public static constructor <clinit>()V
    .locals 3

    const v0, 0x1010003

    const v1, 0x10100d0

    const v2, 0x10100d1

    filled-new-array {v0, v1, v2}, [I

    move-result-object v1

    sput-object v1, Landroidx/fragment/R$styleable;->Fragment:[I

    filled-new-array {v0, v2}, [I

    move-result-object v0

    sput-object v0, Landroidx/fragment/R$styleable;->FragmentContainerView:[I

    return-void
.end method

.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
