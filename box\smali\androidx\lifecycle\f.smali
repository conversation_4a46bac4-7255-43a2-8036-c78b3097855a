.class public interface abstract Landroidx/lifecycle/f;
.super Ljava/lang/Object;

# interfaces
.implements Landroidx/lifecycle/t;


# annotations
.annotation runtime Lkotlin/Metadata;
.end annotation


# virtual methods
.method public abstract onCreate(Landroidx/lifecycle/u;)V
.end method

.method public abstract onDestroy(Landroidx/lifecycle/u;)V
.end method

.method public abstract onPause(Landroidx/lifecycle/u;)V
.end method

.method public abstract onResume(Landroidx/lifecycle/u;)V
.end method

.method public abstract onStart(Landroidx/lifecycle/u;)V
.end method

.method public abstract onStop(Landroidx/lifecycle/u;)V
.end method
