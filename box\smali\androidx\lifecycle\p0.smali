.class public final Landroidx/lifecycle/p0;
.super Ljava/lang/Object;


# annotations
.annotation runtime Lkotlin/Metadata;
.end annotation

.annotation build Lkotlin/jvm/JvmName;
.end annotation

.annotation build Lkotlin/jvm/internal/SourceDebugExtension;
.end annotation


# static fields
.field public static final a:Lw1/a$b;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lw1/a$b<",
            "Lk4/e;",
            ">;"
        }
    .end annotation

    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final b:Lw1/a$b;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lw1/a$b<",
            "Landroidx/lifecycle/z0;",
            ">;"
        }
    .end annotation

    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final c:Lw1/a$b;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lw1/a$b<",
            "Landroid/os/Bundle;",
            ">;"
        }
    .end annotation

    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 1

    new-instance v0, Landroidx/lifecycle/p0$b;

    invoke-direct {v0}, Landroidx/lifecycle/p0$b;-><init>()V

    sput-object v0, Landroidx/lifecycle/p0;->a:Lw1/a$b;

    new-instance v0, Landroidx/lifecycle/p0$c;

    invoke-direct {v0}, Landroidx/lifecycle/p0$c;-><init>()V

    sput-object v0, Landroidx/lifecycle/p0;->b:Lw1/a$b;

    new-instance v0, Landroidx/lifecycle/p0$a;

    invoke-direct {v0}, Landroidx/lifecycle/p0$a;-><init>()V

    sput-object v0, Landroidx/lifecycle/p0;->c:Lw1/a$b;

    return-void
.end method

.method public static final a(Lk4/e;Landroidx/lifecycle/z0;Ljava/lang/String;Landroid/os/Bundle;)Landroidx/lifecycle/m0;
    .locals 1

    invoke-static {p0}, Landroidx/lifecycle/p0;->d(Lk4/e;)Landroidx/lifecycle/SavedStateHandlesProvider;

    move-result-object p0

    invoke-static {p1}, Landroidx/lifecycle/p0;->e(Landroidx/lifecycle/z0;)Landroidx/lifecycle/q0;

    move-result-object p1

    invoke-virtual {p1}, Landroidx/lifecycle/q0;->b()Ljava/util/Map;

    move-result-object v0

    invoke-interface {v0, p2}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroidx/lifecycle/m0;

    if-nez v0, :cond_0

    sget-object v0, Landroidx/lifecycle/m0;->f:Landroidx/lifecycle/m0$a;

    invoke-virtual {p0, p2}, Landroidx/lifecycle/SavedStateHandlesProvider;->a(Ljava/lang/String;)Landroid/os/Bundle;

    move-result-object p0

    invoke-virtual {v0, p0, p3}, Landroidx/lifecycle/m0$a;->a(Landroid/os/Bundle;Landroid/os/Bundle;)Landroidx/lifecycle/m0;

    move-result-object v0

    invoke-virtual {p1}, Landroidx/lifecycle/q0;->b()Ljava/util/Map;

    move-result-object p0

    invoke-interface {p0, p2, v0}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    :cond_0
    return-object v0
.end method

.method public static final b(Lw1/a;)Landroidx/lifecycle/m0;
    .locals 4

    const-string v0, "<this>"

    invoke-static {p0, v0}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    sget-object v0, Landroidx/lifecycle/p0;->a:Lw1/a$b;

    invoke-virtual {p0, v0}, Lw1/a;->a(Lw1/a$b;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lk4/e;

    if-eqz v0, :cond_2

    sget-object v1, Landroidx/lifecycle/p0;->b:Lw1/a$b;

    invoke-virtual {p0, v1}, Lw1/a;->a(Lw1/a$b;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Landroidx/lifecycle/z0;

    if-eqz v1, :cond_1

    sget-object v2, Landroidx/lifecycle/p0;->c:Lw1/a$b;

    invoke-virtual {p0, v2}, Lw1/a;->a(Lw1/a$b;)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Landroid/os/Bundle;

    sget-object v3, Landroidx/lifecycle/w0$d;->c:Lw1/a$b;

    invoke-virtual {p0, v3}, Lw1/a;->a(Lw1/a$b;)Ljava/lang/Object;

    move-result-object p0

    check-cast p0, Ljava/lang/String;

    if-eqz p0, :cond_0

    invoke-static {v0, v1, p0, v2}, Landroidx/lifecycle/p0;->a(Lk4/e;Landroidx/lifecycle/z0;Ljava/lang/String;Landroid/os/Bundle;)Landroidx/lifecycle/m0;

    move-result-object p0

    return-object p0

    :cond_0
    new-instance p0, Ljava/lang/IllegalArgumentException;

    const-string v0, "CreationExtras must have a value by `VIEW_MODEL_KEY`"

    invoke-direct {p0, v0}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p0

    :cond_1
    new-instance p0, Ljava/lang/IllegalArgumentException;

    const-string v0, "CreationExtras must have a value by `VIEW_MODEL_STORE_OWNER_KEY`"

    invoke-direct {p0, v0}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p0

    :cond_2
    new-instance p0, Ljava/lang/IllegalArgumentException;

    const-string v0, "CreationExtras must have a value by `SAVED_STATE_REGISTRY_OWNER_KEY`"

    invoke-direct {p0, v0}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p0
.end method

.method public static final c(Lk4/e;)V
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T::",
            "Lk4/e;",
            ":",
            "Landroidx/lifecycle/z0;",
            ">(TT;)V"
        }
    .end annotation

    const-string v0, "<this>"

    invoke-static {p0, v0}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-interface {p0}, Landroidx/lifecycle/u;->getLifecycle()Landroidx/lifecycle/Lifecycle;

    move-result-object v0

    invoke-virtual {v0}, Landroidx/lifecycle/Lifecycle;->b()Landroidx/lifecycle/Lifecycle$State;

    move-result-object v0

    sget-object v1, Landroidx/lifecycle/Lifecycle$State;->INITIALIZED:Landroidx/lifecycle/Lifecycle$State;

    if-eq v0, v1, :cond_1

    sget-object v1, Landroidx/lifecycle/Lifecycle$State;->CREATED:Landroidx/lifecycle/Lifecycle$State;

    if-ne v0, v1, :cond_0

    goto :goto_0

    :cond_0
    new-instance p0, Ljava/lang/IllegalArgumentException;

    const-string v0, "Failed requirement."

    invoke-virtual {v0}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-direct {p0, v0}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p0

    :cond_1
    :goto_0
    invoke-interface {p0}, Lk4/e;->getSavedStateRegistry()Lk4/c;

    move-result-object v0

    const-string v1, "androidx.lifecycle.internal.SavedStateHandlesProvider"

    invoke-virtual {v0, v1}, Lk4/c;->c(Ljava/lang/String;)Lk4/c$c;

    move-result-object v0

    if-nez v0, :cond_2

    new-instance v0, Landroidx/lifecycle/SavedStateHandlesProvider;

    invoke-interface {p0}, Lk4/e;->getSavedStateRegistry()Lk4/c;

    move-result-object v2

    move-object v3, p0

    check-cast v3, Landroidx/lifecycle/z0;

    invoke-direct {v0, v2, v3}, Landroidx/lifecycle/SavedStateHandlesProvider;-><init>(Lk4/c;Landroidx/lifecycle/z0;)V

    invoke-interface {p0}, Lk4/e;->getSavedStateRegistry()Lk4/c;

    move-result-object v2

    invoke-virtual {v2, v1, v0}, Lk4/c;->h(Ljava/lang/String;Lk4/c$c;)V

    invoke-interface {p0}, Landroidx/lifecycle/u;->getLifecycle()Landroidx/lifecycle/Lifecycle;

    move-result-object p0

    new-instance v1, Landroidx/lifecycle/n0;

    invoke-direct {v1, v0}, Landroidx/lifecycle/n0;-><init>(Landroidx/lifecycle/SavedStateHandlesProvider;)V

    invoke-virtual {p0, v1}, Landroidx/lifecycle/Lifecycle;->a(Landroidx/lifecycle/t;)V

    :cond_2
    return-void
.end method

.method public static final d(Lk4/e;)Landroidx/lifecycle/SavedStateHandlesProvider;
    .locals 1

    const-string v0, "<this>"

    invoke-static {p0, v0}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-interface {p0}, Lk4/e;->getSavedStateRegistry()Lk4/c;

    move-result-object p0

    const-string v0, "androidx.lifecycle.internal.SavedStateHandlesProvider"

    invoke-virtual {p0, v0}, Lk4/c;->c(Ljava/lang/String;)Lk4/c$c;

    move-result-object p0

    instance-of v0, p0, Landroidx/lifecycle/SavedStateHandlesProvider;

    if-eqz v0, :cond_0

    check-cast p0, Landroidx/lifecycle/SavedStateHandlesProvider;

    goto :goto_0

    :cond_0
    const/4 p0, 0x0

    :goto_0
    if-eqz p0, :cond_1

    return-object p0

    :cond_1
    new-instance p0, Ljava/lang/IllegalStateException;

    const-string v0, "enableSavedStateHandles() wasn\'t called prior to createSavedStateHandle() call"

    invoke-direct {p0, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw p0
.end method

.method public static final e(Landroidx/lifecycle/z0;)Landroidx/lifecycle/q0;
    .locals 2

    const-string v0, "<this>"

    invoke-static {p0, v0}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    new-instance v0, Landroidx/lifecycle/w0;

    new-instance v1, Landroidx/lifecycle/p0$d;

    invoke-direct {v1}, Landroidx/lifecycle/p0$d;-><init>()V

    invoke-direct {v0, p0, v1}, Landroidx/lifecycle/w0;-><init>(Landroidx/lifecycle/z0;Landroidx/lifecycle/w0$c;)V

    const-string p0, "androidx.lifecycle.internal.SavedStateHandlesVM"

    const-class v1, Landroidx/lifecycle/q0;

    invoke-virtual {v0, p0, v1}, Landroidx/lifecycle/w0;->b(Ljava/lang/String;Ljava/lang/Class;)Landroidx/lifecycle/u0;

    move-result-object p0

    check-cast p0, Landroidx/lifecycle/q0;

    return-object p0
.end method
