.class public final Landroidx/navigation/fragment/R$styleable;
.super Ljava/lang/Object;


# static fields
.field public static DialogFragmentNavigator:[I

.field public static DialogFragmentNavigator_android_name:I

.field public static FragmentNavigator:[I

.field public static FragmentNavigator_android_name:I

.field public static NavHostFragment:[I

.field public static NavHostFragment_defaultNavHost:I


# direct methods
.method public static constructor <clinit>()V
    .locals 2

    const v0, 0x1010003

    filled-new-array {v0}, [I

    move-result-object v1

    sput-object v1, Landroidx/navigation/fragment/R$styleable;->DialogFragmentNavigator:[I

    filled-new-array {v0}, [I

    move-result-object v0

    sput-object v0, Landroidx/navigation/fragment/R$styleable;->FragmentNavigator:[I

    const v0, 0x7f0402e5

    filled-new-array {v0}, [I

    move-result-object v0

    sput-object v0, Landroidx/navigation/fragment/R$styleable;->NavHostFragment:[I

    return-void
.end method

.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
