.class public final Landroidx/recyclerview/R$attr;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/recyclerview/R;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "attr"
.end annotation


# static fields
.field public static fastScrollEnabled:I = 0x7f040365

.field public static fastScrollHorizontalThumbDrawable:I = 0x7f040366

.field public static fastScrollHorizontalTrackDrawable:I = 0x7f040367

.field public static fastScrollVerticalThumbDrawable:I = 0x7f040368

.field public static fastScrollVerticalTrackDrawable:I = 0x7f040369

.field public static layoutManager:I = 0x7f040469

.field public static recyclerViewStyle:I = 0x7f040602

.field public static reverseLayout:I = 0x7f04060c

.field public static spanCount:I = 0x7f040685

.field public static stackFromEnd:I = 0x7f0406a2


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
