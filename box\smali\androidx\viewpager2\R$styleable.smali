.class public final Landroidx/viewpager2/R$styleable;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/viewpager2/R;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "styleable"
.end annotation


# static fields
.field public static ViewPager2:[I

.field public static ViewPager2_android_orientation:I


# direct methods
.method public static constructor <clinit>()V
    .locals 1

    const v0, 0x10100c4

    filled-new-array {v0}, [I

    move-result-object v0

    sput-object v0, Landroidx/viewpager2/R$styleable;->ViewPager2:[I

    return-void
.end method

.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
