.class public interface abstract Lathena/a;
.super Ljava/lang/Object;

# interfaces
.implements Landroid/os/IInterface;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lathena/a$a;
    }
.end annotation


# virtual methods
.method public abstract q3(Ljava/lang/String;Lcom/transsion/athena/data/TrackData;J)V
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroid/os/RemoteException;
        }
    .end annotation
.end method
