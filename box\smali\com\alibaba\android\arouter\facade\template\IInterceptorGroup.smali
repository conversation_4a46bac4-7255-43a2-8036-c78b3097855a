.class public interface abstract Lcom/alibaba/android/arouter/facade/template/IInterceptorGroup;
.super Ljava/lang/Object;


# virtual methods
.method public abstract loadInto(Ljava/util/Map;)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Map<",
            "Ljava/lang/Integer;",
            "Ljava/lang/Class<",
            "+",
            "Lcom/alibaba/android/arouter/facade/template/IInterceptor;",
            ">;>;)V"
        }
    .end annotation
.end method
