.class public interface abstract Lcom/alibaba/android/arouter/facade/template/IProviderGroup;
.super Ljava/lang/Object;


# virtual methods
.method public abstract loadInto(Ljava/util/Map;)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Lcom/alibaba/android/arouter/facade/model/RouteMeta;",
            ">;)V"
        }
    .end annotation
.end method
