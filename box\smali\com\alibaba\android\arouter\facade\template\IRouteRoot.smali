.class public interface abstract Lcom/alibaba/android/arouter/facade/template/IRouteRoot;
.super Ljava/lang/Object;


# virtual methods
.method public abstract loadInto(Ljava/util/Map;)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/Class<",
            "+",
            "Lcom/alibaba/android/arouter/facade/template/IRouteGroup;",
            ">;>;)V"
        }
    .end annotation
.end method
