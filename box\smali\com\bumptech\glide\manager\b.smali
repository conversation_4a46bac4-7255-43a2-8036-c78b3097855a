.class public final synthetic Lcom/bumptech/glide/manager/b;
.super Ljava/lang/Object;


# direct methods
.method public static bridge synthetic a(Landroid/net/ConnectivityManager;Landroid/net/ConnectivityManager$NetworkCallback;)V
    .locals 0

    invoke-virtual {p0, p1}, Landroid/net/ConnectivityManager;->registerDefaultNetworkCallback(Landroid/net/ConnectivityManager$NetworkCallback;)V

    return-void
.end method
