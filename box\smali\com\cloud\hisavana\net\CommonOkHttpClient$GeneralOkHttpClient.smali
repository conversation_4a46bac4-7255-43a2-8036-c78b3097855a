.class Lcom/cloud/hisavana/net/CommonOkHttpClient$GeneralOkHttpClient;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/cloud/hisavana/net/CommonOkHttpClient;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "GeneralOkHttpClient"
.end annotation


# static fields
.field public static final a:Lokhttp3/OkHttpClient;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    invoke-static {}, Lcom/cloud/hisavana/net/CommonOkHttpClient$GeneralOkHttpClient;->b()Lokhttp3/OkHttpClient$a;

    move-result-object v0

    invoke-virtual {v0}, Lokhttp3/OkHttpClient$a;->c()Lokhttp3/OkHttpClient;

    move-result-object v0

    sput-object v0, Lcom/cloud/hisavana/net/CommonOkHttpClient$GeneralOkHttpClient;->a:Lokhttp3/OkHttpClient;

    return-void
.end method

.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static synthetic a()Lokhttp3/OkHttpClient;
    .locals 1

    sget-object v0, Lcom/cloud/hisavana/net/CommonOkHttpClient$GeneralOkHttpClient;->a:Lokhttp3/OkHttpClient;

    return-object v0
.end method

.method public static b()Lokhttp3/OkHttpClient$a;
    .locals 6

    new-instance v0, Lkk/e;

    invoke-static {}, Lcom/cloud/hisavana/net/CommonOkHttpClient;->a()Lokhttp3/p$c;

    move-result-object v1

    const/4 v2, 0x0

    sget-boolean v3, Lcom/cloud/hisavana/net/CommonOkHttpClient;->a:Z

    invoke-direct {v0, v1, v2, v3}, Lkk/e;-><init>(Lokhttp3/p$c;IZ)V

    sget-boolean v1, Lcom/cloud/hisavana/net/CommonOkHttpClient;->a:Z

    const-wide/16 v2, 0x1e

    if-eqz v1, :cond_1

    new-instance v1, Lokhttp3/OkHttpClient$a;

    invoke-direct {v1}, Lokhttp3/OkHttpClient$a;-><init>()V

    invoke-static {}, Lcom/cloud/hisavana/net/ssl/HttpsTrustManager;->a()Ljavax/net/ssl/SSLSocketFactory;

    move-result-object v4

    new-instance v5, Lcom/cloud/hisavana/net/ssl/HttpsTrustManager;

    invoke-direct {v5}, Lcom/cloud/hisavana/net/ssl/HttpsTrustManager;-><init>()V

    invoke-virtual {v1, v4, v5}, Lokhttp3/OkHttpClient$a;->U(Ljavax/net/ssl/SSLSocketFactory;Ljavax/net/ssl/X509TrustManager;)Lokhttp3/OkHttpClient$a;

    move-result-object v1

    new-instance v4, Lcom/cloud/hisavana/net/ssl/HttpsTrustManager$TrustAllHostnameVerifier;

    invoke-direct {v4}, Lcom/cloud/hisavana/net/ssl/HttpsTrustManager$TrustAllHostnameVerifier;-><init>()V

    invoke-virtual {v1, v4}, Lokhttp3/OkHttpClient$a;->P(Ljavax/net/ssl/HostnameVerifier;)Lokhttp3/OkHttpClient$a;

    move-result-object v1

    sget-object v4, Ljava/util/concurrent/TimeUnit;->SECONDS:Ljava/util/concurrent/TimeUnit;

    invoke-virtual {v1, v2, v3, v4}, Lokhttp3/OkHttpClient$a;->e(JLjava/util/concurrent/TimeUnit;)Lokhttp3/OkHttpClient$a;

    move-result-object v1

    invoke-virtual {v1, v2, v3, v4}, Lokhttp3/OkHttpClient$a;->S(JLjava/util/concurrent/TimeUnit;)Lokhttp3/OkHttpClient$a;

    move-result-object v1

    invoke-virtual {v1, v2, v3, v4}, Lokhttp3/OkHttpClient$a;->V(JLjava/util/concurrent/TimeUnit;)Lokhttp3/OkHttpClient$a;

    move-result-object v1

    invoke-static {}, Lcom/cloud/hisavana/net/CommonOkHttpClient;->b()Lokhttp3/n;

    move-result-object v2

    if-eqz v2, :cond_0

    invoke-static {}, Lcom/cloud/hisavana/net/CommonOkHttpClient;->b()Lokhttp3/n;

    move-result-object v2

    goto :goto_0

    :cond_0
    new-instance v2, Lokhttp3/n;

    invoke-direct {v2}, Lokhttp3/n;-><init>()V

    :goto_0
    invoke-virtual {v1, v2}, Lokhttp3/OkHttpClient$a;->g(Lokhttp3/n;)Lokhttp3/OkHttpClient$a;

    move-result-object v1

    invoke-virtual {v1, v0}, Lokhttp3/OkHttpClient$a;->i(Lokhttp3/p$c;)Lokhttp3/OkHttpClient$a;

    move-result-object v0

    goto :goto_2

    :cond_1
    new-instance v1, Lokhttp3/OkHttpClient$a;

    invoke-direct {v1}, Lokhttp3/OkHttpClient$a;-><init>()V

    sget-object v4, Ljava/util/concurrent/TimeUnit;->SECONDS:Ljava/util/concurrent/TimeUnit;

    invoke-virtual {v1, v2, v3, v4}, Lokhttp3/OkHttpClient$a;->e(JLjava/util/concurrent/TimeUnit;)Lokhttp3/OkHttpClient$a;

    move-result-object v1

    invoke-virtual {v1, v2, v3, v4}, Lokhttp3/OkHttpClient$a;->S(JLjava/util/concurrent/TimeUnit;)Lokhttp3/OkHttpClient$a;

    move-result-object v1

    invoke-virtual {v1, v2, v3, v4}, Lokhttp3/OkHttpClient$a;->V(JLjava/util/concurrent/TimeUnit;)Lokhttp3/OkHttpClient$a;

    move-result-object v1

    invoke-static {}, Lcom/cloud/hisavana/net/CommonOkHttpClient;->b()Lokhttp3/n;

    move-result-object v2

    if-eqz v2, :cond_2

    invoke-static {}, Lcom/cloud/hisavana/net/CommonOkHttpClient;->b()Lokhttp3/n;

    move-result-object v2

    goto :goto_1

    :cond_2
    new-instance v2, Lokhttp3/n;

    invoke-direct {v2}, Lokhttp3/n;-><init>()V

    :goto_1
    invoke-virtual {v1, v2}, Lokhttp3/OkHttpClient$a;->g(Lokhttp3/n;)Lokhttp3/OkHttpClient$a;

    move-result-object v1

    invoke-virtual {v1, v0}, Lokhttp3/OkHttpClient$a;->i(Lokhttp3/p$c;)Lokhttp3/OkHttpClient$a;

    move-result-object v0

    :goto_2
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "getBuilder: isTestRequest = "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    sget-boolean v2, Lcom/cloud/hisavana/net/CommonOkHttpClient;->a:Z

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-static {v1}, Lcom/cloud/sdk/commonutil/util/c;->netLog(Ljava/lang/String;)V

    return-object v0
.end method
