.class public Lcom/cloud/hisavana/net/CommonRequest;
.super Ljava/lang/Object;


# direct methods
.method private constructor <init>()V
    .locals 2

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    new-instance v0, Ljava/lang/IllegalStateException;

    const-string v1, "Utility class"

    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw v0
.end method

.method public static a(Ljava/lang/String;Lcom/cloud/hisavana/net/RequestParams;Lcom/cloud/hisavana/net/impl/IHttpCallback;)Lokhttp3/w;
    .locals 3

    :try_start_0
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0, p0}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    const-string p0, "?"

    invoke-virtual {v0, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    if-eqz p1, :cond_0

    invoke-virtual {p1}, Lcom/cloud/hisavana/net/RequestParams;->c()Ljava/util/concurrent/ConcurrentHashMap;

    move-result-object p0

    invoke-virtual {p0}, Ljava/util/concurrent/ConcurrentHashMap;->isEmpty()Z

    move-result p0

    if-nez p0, :cond_0

    invoke-virtual {p1}, Lcom/cloud/hisavana/net/RequestParams;->c()Ljava/util/concurrent/ConcurrentHashMap;

    move-result-object p0

    invoke-virtual {p0}, Ljava/util/concurrent/ConcurrentHashMap;->entrySet()Ljava/util/Set;

    move-result-object p0

    invoke-interface {p0}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object p0

    :goto_0
    invoke-interface {p0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_0

    invoke-interface {p0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/util/Map$Entry;

    invoke-interface {v1}, Ljava/util/Map$Entry;->getKey()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Ljava/lang/String;

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v2, "="

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-interface {v1}, Ljava/util/Map$Entry;->getValue()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v1, "&"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    goto :goto_0

    :catch_0
    move-exception p0

    goto :goto_1

    :cond_0
    const/4 p0, 0x0

    if-eqz p1, :cond_1

    new-instance v1, Lokhttp3/w$a;

    invoke-direct {v1}, Lokhttp3/w$a;-><init>()V

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->length()I

    move-result v2

    add-int/lit8 v2, v2, -0x1

    invoke-virtual {v0, p0, v2}, Ljava/lang/StringBuilder;->substring(II)Ljava/lang/String;

    move-result-object p0

    invoke-virtual {v1, p0}, Lokhttp3/w$a;->n(Ljava/lang/String;)Lokhttp3/w$a;

    move-result-object p0

    invoke-virtual {p1}, Lcom/cloud/hisavana/net/RequestParams;->b()Ljava/util/concurrent/ConcurrentHashMap;

    move-result-object p1

    invoke-static {p1}, Lcom/cloud/hisavana/net/CommonRequest;->c(Ljava/util/Map;)Lokhttp3/r;

    move-result-object p1

    invoke-virtual {p0, p1}, Lokhttp3/w$a;->i(Lokhttp3/r;)Lokhttp3/w$a;

    move-result-object p0

    invoke-virtual {p0}, Lokhttp3/w$a;->f()Lokhttp3/w$a;

    move-result-object p0

    invoke-virtual {p0}, Lokhttp3/w$a;->b()Lokhttp3/w;

    move-result-object p0

    return-object p0

    :cond_1
    new-instance p1, Lokhttp3/w$a;

    invoke-direct {p1}, Lokhttp3/w$a;-><init>()V

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->length()I

    move-result v1

    add-int/lit8 v1, v1, -0x1

    invoke-virtual {v0, p0, v1}, Ljava/lang/StringBuilder;->substring(II)Ljava/lang/String;

    move-result-object p0

    invoke-virtual {p1, p0}, Lokhttp3/w$a;->n(Ljava/lang/String;)Lokhttp3/w$a;

    move-result-object p0

    invoke-virtual {p0}, Lokhttp3/w$a;->f()Lokhttp3/w$a;

    move-result-object p0

    invoke-virtual {p0}, Lokhttp3/w$a;->b()Lokhttp3/w;

    move-result-object p0
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    return-object p0

    :goto_1
    const/4 p1, 0x0

    if-eqz p2, :cond_2

    const/16 v0, 0x1ea

    invoke-interface {p2, v0, p1, p0}, Lcom/cloud/hisavana/net/impl/IHttpCallback;->e(I[BLjava/lang/Throwable;)V

    :cond_2
    return-object p1
.end method

.method public static b(Ljava/lang/String;Lcom/cloud/hisavana/net/RequestParams;Lcom/cloud/hisavana/net/impl/IHttpCallback;)Lokhttp3/w;
    .locals 1

    :try_start_0
    new-instance v0, Lokhttp3/w$a;

    invoke-direct {v0}, Lokhttp3/w$a;-><init>()V

    invoke-virtual {v0, p0}, Lokhttp3/w$a;->n(Ljava/lang/String;)Lokhttp3/w$a;

    move-result-object p0

    invoke-virtual {p1}, Lcom/cloud/hisavana/net/RequestParams;->b()Ljava/util/concurrent/ConcurrentHashMap;

    move-result-object v0

    invoke-static {v0}, Lcom/cloud/hisavana/net/CommonRequest;->c(Ljava/util/Map;)Lokhttp3/r;

    move-result-object v0

    invoke-virtual {p0, v0}, Lokhttp3/w$a;->i(Lokhttp3/r;)Lokhttp3/w$a;

    move-result-object p0

    invoke-virtual {p1}, Lcom/cloud/hisavana/net/RequestParams;->d()Ljava/lang/Object;

    move-result-object p1

    invoke-static {p1}, Lcom/cloud/hisavana/net/CommonRequest;->d(Ljava/lang/Object;)Lokhttp3/x;

    move-result-object p1

    invoke-virtual {p0, p1}, Lokhttp3/w$a;->k(Lokhttp3/x;)Lokhttp3/w$a;

    move-result-object p0

    invoke-virtual {p0}, Lokhttp3/w$a;->b()Lokhttp3/w;

    move-result-object p0
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    return-object p0

    :catch_0
    move-exception p0

    const/4 p1, 0x0

    if-eqz p2, :cond_0

    const/16 v0, 0x1ea

    invoke-interface {p2, v0, p1, p0}, Lcom/cloud/hisavana/net/impl/IHttpCallback;->e(I[BLjava/lang/Throwable;)V

    :cond_0
    return-object p1
.end method

.method public static c(Ljava/util/Map;)Lokhttp3/r;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ">;)",
            "Lokhttp3/r;"
        }
    .end annotation

    if-nez p0, :cond_0

    new-instance p0, Ljava/util/HashMap;

    invoke-direct {p0}, Ljava/util/HashMap;-><init>()V

    :cond_0
    invoke-interface {p0}, Ljava/util/Map;->isEmpty()Z

    move-result v0

    if-nez v0, :cond_1

    invoke-static {p0}, Lokhttp3/r;->d(Ljava/util/Map;)Lokhttp3/r;

    move-result-object p0

    return-object p0

    :cond_1
    new-instance p0, Lokhttp3/r$a;

    invoke-direct {p0}, Lokhttp3/r$a;-><init>()V

    invoke-virtual {p0}, Lokhttp3/r$a;->e()Lokhttp3/r;

    move-result-object p0

    return-object p0
.end method

.method public static d(Ljava/lang/Object;)Lokhttp3/x;
    .locals 1

    invoke-static {p0}, Lcom/cloud/sdk/commonutil/gsonutil/GsonUtil;->d(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p0

    sget-object v0, Lcom/cloud/hisavana/net/ContentType;->JSON:Lcom/cloud/hisavana/net/ContentType;

    invoke-virtual {v0}, Lcom/cloud/hisavana/net/ContentType;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lokhttp3/u;->g(Ljava/lang/String;)Lokhttp3/u;

    move-result-object v0

    invoke-static {p0, v0}, Lcom/cloud/hisavana/net/CommonRequest;->e(Ljava/lang/String;Lokhttp3/u;)Lokhttp3/x;

    move-result-object p0

    return-object p0
.end method

.method public static e(Ljava/lang/String;Lokhttp3/u;)Lokhttp3/x;
    .locals 0

    invoke-static {p1, p0}, Lokhttp3/x;->create(Lokhttp3/u;Ljava/lang/String;)Lokhttp3/x;

    move-result-object p0

    return-object p0
.end method
