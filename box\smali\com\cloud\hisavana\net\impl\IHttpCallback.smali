.class public interface abstract Lcom/cloud/hisavana/net/impl/IHttpCallback;
.super Ljava/lang/Object;


# virtual methods
.method public abstract a()V
.end method

.method public abstract b()V
.end method

.method public abstract c(I[BLjava/lang/String;)V
.end method

.method public abstract d(I[B)V
.end method

.method public abstract e(I[BLjava/lang/Throwable;)V
.end method

.method public abstract f(I[BLokhttp3/r;)V
.end method
