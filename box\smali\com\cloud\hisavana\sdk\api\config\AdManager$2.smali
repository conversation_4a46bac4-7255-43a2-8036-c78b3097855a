.class Lcom/cloud/hisavana/sdk/api/config/AdManager$2;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/cloud/hisavana/sdk/api/config/AdManager;->b(Lcom/cloud/hisavana/sdk/api/config/AdManager$b;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 0

    invoke-static {}, Lcom/cloud/sdk/commonutil/util/m;->n()V

    invoke-static {}, Lf7/b;->r0()V

    return-void
.end method
