.class Lcom/cloud/hisavana/sdk/api/config/AdManager$3;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/cloud/hisavana/sdk/api/config/AdManager;->d()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 12

    invoke-static {}, Lg7/a;->d()Lg7/a;

    move-result-object v0

    const-string v1, "default_file_save_finished"

    invoke-virtual {v0, v1}, Lg7/a;->b(Ljava/lang/String;)Z

    move-result v0

    invoke-static {}, Lg7/a;->d()Lg7/a;

    move-result-object v1

    invoke-static {}, Lcom/cloud/hisavana/sdk/api/config/AdManager;->c()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object v2

    invoke-virtual {v2}, Ljava/util/concurrent/atomic/AtomicLong;->toString()Ljava/lang/String;

    move-result-object v2

    const/4 v3, 0x0

    invoke-virtual {v1, v2, v3}, Lg7/a;->f(Ljava/lang/String;I)I

    move-result v1

    invoke-static {}, Lg7/a;->d()Lg7/a;

    move-result-object v2

    const-string v4, "default_local_version"

    invoke-virtual {v2, v4}, Lg7/a;->g(Ljava/lang/String;)J

    move-result-wide v4

    invoke-static {}, Lcom/cloud/hisavana/sdk/m;->a()Lcom/cloud/hisavana/sdk/m;

    move-result-object v2

    new-instance v6, Ljava/lang/StringBuilder;

    invoke-direct {v6}, Ljava/lang/StringBuilder;-><init>()V

    const-string v7, "defaultVersion is "

    invoke-virtual {v6, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-static {}, Lcom/cloud/hisavana/sdk/api/config/AdManager;->c()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object v7

    invoke-virtual {v6, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v7, ",  loaclVersion is "

    invoke-virtual {v6, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v6, v4, v5}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    const-string v7, ", times is "

    invoke-virtual {v6, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v6, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v6}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v6

    const-string v7, "ssp"

    invoke-virtual {v2, v7, v6}, Lcom/cloud/sdk/commonutil/util/c;->d(Ljava/lang/String;Ljava/lang/String;)V

    invoke-static {}, Lcom/cloud/hisavana/sdk/api/config/AdManager;->c()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object v2

    invoke-virtual {v2}, Ljava/util/concurrent/atomic/AtomicLong;->get()J

    move-result-wide v8

    const-wide/16 v10, 0x0

    cmp-long v2, v8, v10

    if-lez v2, :cond_3

    invoke-static {}, Lcom/cloud/hisavana/sdk/api/config/AdManager;->c()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object v2

    invoke-virtual {v2}, Ljava/util/concurrent/atomic/AtomicLong;->get()J

    move-result-wide v8

    cmp-long v2, v4, v8

    if-gez v2, :cond_3

    const/4 v2, 0x3

    if-ge v1, v2, :cond_3

    const/4 v0, 0x1

    add-int/2addr v1, v0

    invoke-static {}, Lg7/a;->d()Lg7/a;

    move-result-object v2

    invoke-static {}, Lcom/cloud/hisavana/sdk/api/config/AdManager;->c()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object v4

    invoke-virtual {v4}, Ljava/util/concurrent/atomic/AtomicLong;->toString()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v2, v4, v1}, Lg7/a;->n(Ljava/lang/String;I)V

    const/4 v1, 0x0

    :try_start_0
    invoke-static {}, Lgm/a;->a()Landroid/content/Context;

    move-result-object v2

    invoke-virtual {v2}, Landroid/content/Context;->getAssets()Landroid/content/res/AssetManager;

    move-result-object v2

    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    sget-object v5, Lcom/cloud/hisavana/sdk/api/config/AdManager;->b:Ljava/lang/String;

    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v5, ".zip"

    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v2, v4}, Landroid/content/res/AssetManager;->open(Ljava/lang/String;)Ljava/io/InputStream;

    move-result-object v1

    sget-object v2, Lcom/cloud/hisavana/sdk/w;->a:Lcom/cloud/hisavana/sdk/w;

    invoke-virtual {v2, v1}, Lcom/cloud/hisavana/sdk/w;->g(Ljava/io/InputStream;)Z

    move-result v3
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    if-eqz v1, :cond_0

    goto :goto_0

    :catchall_0
    move-exception v0

    goto :goto_2

    :catch_0
    :try_start_1
    invoke-static {}, Lcom/cloud/hisavana/sdk/m;->a()Lcom/cloud/hisavana/sdk/m;

    move-result-object v2

    const-string v4, "Please check whether the default advertising zip package is configured correctly"

    invoke-virtual {v2, v7, v4}, Lcom/cloud/sdk/commonutil/util/c;->e(Ljava/lang/String;Ljava/lang/String;)V
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    if-eqz v1, :cond_1

    :goto_0
    :try_start_2
    invoke-virtual {v1}, Ljava/io/InputStream;->close()V
    :try_end_2
    .catch Ljava/lang/Exception; {:try_start_2 .. :try_end_2} :catch_1

    goto :goto_1

    :catch_1
    nop

    :cond_0
    :goto_1
    if-eqz v3, :cond_1

    sget-object v1, Lcom/cloud/hisavana/sdk/y;->a:Lcom/cloud/hisavana/sdk/y;

    invoke-virtual {v1}, Lcom/cloud/hisavana/sdk/y;->m()V

    sget-object v1, Lcom/cloud/hisavana/sdk/w;->a:Lcom/cloud/hisavana/sdk/w;

    new-instance v2, Lcom/cloud/hisavana/sdk/api/config/AdManager$3$a;

    invoke-direct {v2, p0}, Lcom/cloud/hisavana/sdk/api/config/AdManager$3$a;-><init>(Lcom/cloud/hisavana/sdk/api/config/AdManager$3;)V

    invoke-virtual {v1, v0, v2}, Lcom/cloud/hisavana/sdk/w;->f(ZLcom/cloud/hisavana/sdk/x$d;)V

    :cond_1
    return-void

    :goto_2
    if-eqz v1, :cond_2

    :try_start_3
    invoke-virtual {v1}, Ljava/io/InputStream;->close()V
    :try_end_3
    .catch Ljava/lang/Exception; {:try_start_3 .. :try_end_3} :catch_2

    :catch_2
    :cond_2
    throw v0

    :cond_3
    if-nez v0, :cond_4

    sget-object v0, Lcom/cloud/hisavana/sdk/y;->a:Lcom/cloud/hisavana/sdk/y;

    invoke-virtual {v0}, Lcom/cloud/hisavana/sdk/y;->m()V

    :cond_4
    sget-object v0, Lcom/cloud/hisavana/sdk/w;->a:Lcom/cloud/hisavana/sdk/w;

    invoke-virtual {v0}, Lcom/cloud/hisavana/sdk/w;->p()V

    return-void
.end method
