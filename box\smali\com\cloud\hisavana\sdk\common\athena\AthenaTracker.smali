.class public Lcom/cloud/hisavana/sdk/common/athena/AthenaTracker;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/cloud/hisavana/sdk/common/athena/AthenaTracker$a;
    }
.end annotation


# static fields
.field public static a:Z


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public static A(Ljava/lang/String;I)V
    .locals 1

    new-instance v0, Lcom/cloud/hisavana/sdk/common/athena/AthenaTracker$5;

    invoke-direct {v0, p0, p1}, Lcom/cloud/hisavana/sdk/common/athena/AthenaTracker$5;-><init>(Ljava/lang/String;I)V

    invoke-static {v0}, Lf7/c;->b(Ljava/lang/Runnable;)V

    return-void
.end method

.method public static B(Lcom/cloud/hisavana/sdk/data/bean/response/AdsDTO;)V
    .locals 1

    new-instance v0, Lcom/cloud/hisavana/sdk/common/athena/AthenaTracker$3;

    invoke-direct {v0, p0}, Lcom/cloud/hisavana/sdk/common/athena/AthenaTracker$3;-><init>(Lcom/cloud/hisavana/sdk/data/bean/response/AdsDTO;)V

    invoke-static {v0}, Lf7/c;->b(Ljava/lang/Runnable;)V

    return-void
.end method

.method public static C(Lcom/cloud/hisavana/sdk/data/bean/response/AdsDTO;Lcom/cloud/hisavana/sdk/data/bean/request/FormBean;)V
    .locals 1

    new-instance v0, Lcom/cloud/hisavana/sdk/common/athena/AthenaTracker$17;

    invoke-direct {v0, p0, p1}, Lcom/cloud/hisavana/sdk/common/athena/AthenaTracker$17;-><init>(Lcom/cloud/hisavana/sdk/data/bean/response/AdsDTO;Lcom/cloud/hisavana/sdk/data/bean/request/FormBean;)V

    invoke-static {v0}, Lf7/c;->b(Ljava/lang/Runnable;)V

    return-void
.end method

.method public static D(Lcom/cloud/hisavana/sdk/data/bean/response/AdsDTO;Ljava/lang/String;Ljava/lang/String;)V
    .locals 1

    new-instance v0, Lcom/cloud/hisavana/sdk/common/athena/AthenaTracker$26;

    invoke-direct {v0, p0, p1, p2}, Lcom/cloud/hisavana/sdk/common/athena/AthenaTracker$26;-><init>(Lcom/cloud/hisavana/sdk/data/bean/response/AdsDTO;Ljava/lang/String;Ljava/lang/String;)V

    invoke-static {v0}, Lf7/c;->b(Ljava/lang/Runnable;)V

    return-void
.end method

.method public static E(Lcom/cloud/hisavana/sdk/data/bean/response/AdsDTO;IIIIILjava/lang/String;JII)V
    .locals 13

    new-instance v12, Lcom/cloud/hisavana/sdk/common/athena/AthenaTracker$9;

    move-object v0, v12

    move-object v1, p0

    move v2, p1

    move v3, p2

    move/from16 v4, p3

    move/from16 v5, p4

    move/from16 v6, p5

    move-object/from16 v7, p6

    move-wide/from16 v8, p7

    move/from16 v10, p9

    move/from16 v11, p10

    invoke-direct/range {v0 .. v11}, Lcom/cloud/hisavana/sdk/common/athena/AthenaTracker$9;-><init>(Lcom/cloud/hisavana/sdk/data/bean/response/AdsDTO;IIIIILjava/lang/String;JII)V

    invoke-static {v12}, Lf7/c;->b(Ljava/lang/Runnable;)V

    return-void
.end method

.method public static F(Lcom/cloud/hisavana/sdk/data/bean/response/AdsDTO;IILjava/lang/String;IJ)V
    .locals 11

    const/4 v1, 0x1

    const/4 v4, 0x1

    const-string v6, ""

    const/4 v9, 0x1

    const/4 v10, 0x0

    move-object v0, p0

    move v2, p1

    move v3, p2

    move v5, p4

    move-wide/from16 v7, p5

    invoke-static/range {v0 .. v10}, Lcom/cloud/hisavana/sdk/common/athena/AthenaTracker;->E(Lcom/cloud/hisavana/sdk/data/bean/response/AdsDTO;IIIIILjava/lang/String;JII)V

    return-void
.end method

.method public static G(Lcom/cloud/hisavana/sdk/ext/attr/AttrData;)V
    .locals 1

    new-instance v0, Lcom/cloud/hisavana/sdk/common/athena/b;

    invoke-direct {v0, p0}, Lcom/cloud/hisavana/sdk/common/athena/b;-><init>(Lcom/cloud/hisavana/sdk/ext/attr/AttrData;)V

    invoke-static {v0}, Lf7/c;->b(Ljava/lang/Runnable;)V

    return-void
.end method

.method public static H()V
    .locals 1

    new-instance v0, Lcom/cloud/hisavana/sdk/common/athena/AthenaTracker$1;

    invoke-direct {v0}, Lcom/cloud/hisavana/sdk/common/athena/AthenaTracker$1;-><init>()V

    invoke-static {v0}, Lf7/c;->b(Ljava/lang/Runnable;)V

    return-void
.end method

.method public static I(Lcom/cloud/hisavana/sdk/data/bean/response/AdsDTO;I)V
    .locals 1

    new-instance v0, Lcom/cloud/hisavana/sdk/common/athena/AthenaTracker$30;

    invoke-direct {v0, p0, p1}, Lcom/cloud/hisavana/sdk/common/athena/AthenaTracker$30;-><init>(Lcom/cloud/hisavana/sdk/data/bean/response/AdsDTO;I)V

    invoke-static {v0}, Lf7/c;->b(Ljava/lang/Runnable;)V

    return-void
.end method

.method public static J(ZILjava/lang/String;Ljava/lang/String;IILjava/lang/String;Ljava/lang/String;Ljava/util/Map;I)V
    .locals 12
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(ZI",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            "II",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/Object;",
            ">;I)V"
        }
    .end annotation

    new-instance v11, Lcom/cloud/hisavana/sdk/common/athena/AthenaTracker$21;

    move-object v0, v11

    move v1, p0

    move v2, p1

    move-object v3, p2

    move-object v4, p3

    move/from16 v5, p4

    move/from16 v6, p5

    move-object/from16 v7, p6

    move-object/from16 v8, p7

    move-object/from16 v9, p8

    move/from16 v10, p9

    invoke-direct/range {v0 .. v10}, Lcom/cloud/hisavana/sdk/common/athena/AthenaTracker$21;-><init>(ZILjava/lang/String;Ljava/lang/String;IILjava/lang/String;Ljava/lang/String;Ljava/util/Map;I)V

    invoke-static {v11}, Lf7/c;->b(Ljava/lang/Runnable;)V

    return-void
.end method

.method public static K(Lcom/cloud/hisavana/sdk/data/bean/response/AdsDTO;)V
    .locals 2

    if-nez p0, :cond_0

    return-void

    :cond_0
    invoke-virtual {p0}, Lcom/cloud/hisavana/sdk/data/bean/response/AdsDTO;->isVastTypeAd()Z

    move-result v0

    if-eqz v0, :cond_1

    invoke-virtual {p0}, Lcom/cloud/hisavana/sdk/data/bean/response/AdsDTO;->getVideoInfo()Lcom/cloud/hisavana/sdk/common/bean/VastData;

    move-result-object v0

    if-eqz v0, :cond_1

    invoke-virtual {v0}, Lcom/cloud/hisavana/sdk/common/bean/VastData;->getProgressData()Lcom/cloud/hisavana/sdk/common/bean/ProgressData;

    move-result-object v0

    if-eqz v0, :cond_1

    const/4 v1, 0x3

    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    invoke-virtual {v0, v1}, Lcom/cloud/hisavana/sdk/common/bean/ProgressData;->setVideoPlayTimeType(Ljava/lang/Integer;)V

    invoke-static {p0, v0}, Lcom/cloud/hisavana/sdk/common/athena/AthenaTracker;->W(Lcom/cloud/hisavana/sdk/data/bean/response/AdsDTO;Lcom/cloud/hisavana/sdk/common/bean/ProgressData;)V

    :cond_1
    new-instance v0, Lcom/cloud/hisavana/sdk/common/athena/AthenaTracker$19;

    invoke-direct {v0, p0}, Lcom/cloud/hisavana/sdk/common/athena/AthenaTracker$19;-><init>(Lcom/cloud/hisavana/sdk/data/bean/response/AdsDTO;)V

    invoke-static {v0}, Lf7/c;->b(Ljava/lang/Runnable;)V

    return-void
.end method

.method public static L(Ljava/lang/String;II)V
    .locals 1

    new-instance v0, Lcom/cloud/hisavana/sdk/common/athena/AthenaTracker$42;

    invoke-direct {v0, p0, p1, p2}, Lcom/cloud/hisavana/sdk/common/athena/AthenaTracker$42;-><init>(Ljava/lang/String;II)V

    invoke-static {v0}, Lf7/c;->b(Ljava/lang/Runnable;)V

    return-void
.end method

.method public static M(Lcom/cloud/hisavana/sdk/data/bean/response/AdsDTO;)V
    .locals 1

    new-instance v0, Lcom/cloud/hisavana/sdk/common/athena/AthenaTracker$2;

    invoke-direct {v0, p0}, Lcom/cloud/hisavana/sdk/common/athena/AthenaTracker$2;-><init>(Lcom/cloud/hisavana/sdk/data/bean/response/AdsDTO;)V

    invoke-static {v0}, Lf7/c;->b(Ljava/lang/Runnable;)V

    return-void
.end method

.method public static N(Lcom/cloud/hisavana/sdk/data/bean/response/AdsDTO;Ljava/lang/String;)V
    .locals 1

    new-instance v0, Lcom/cloud/hisavana/sdk/common/athena/AthenaTracker$44;

    invoke-direct {v0, p0, p1}, Lcom/cloud/hisavana/sdk/common/athena/AthenaTracker$44;-><init>(Lcom/cloud/hisavana/sdk/data/bean/response/AdsDTO;Ljava/lang/String;)V

    invoke-static {v0}, Lf7/c;->b(Ljava/lang/Runnable;)V

    return-void
.end method

.method public static O(Ljava/lang/String;IIJ)V
    .locals 7

    new-instance v6, Lcom/cloud/hisavana/sdk/common/athena/AthenaTracker$10;

    move-object v0, v6

    move-object v1, p0

    move v2, p1

    move v3, p2

    move-wide v4, p3

    invoke-direct/range {v0 .. v5}, Lcom/cloud/hisavana/sdk/common/athena/AthenaTracker$10;-><init>(Ljava/lang/String;IIJ)V

    invoke-static {v6}, Lf7/c;->b(Ljava/lang/Runnable;)V

    return-void
.end method

.method public static P(Lcom/cloud/hisavana/sdk/data/bean/response/AdsDTO;I)V
    .locals 1

    new-instance v0, Lcom/cloud/hisavana/sdk/common/athena/AthenaTracker$29;

    invoke-direct {v0, p0, p1}, Lcom/cloud/hisavana/sdk/common/athena/AthenaTracker$29;-><init>(Lcom/cloud/hisavana/sdk/data/bean/response/AdsDTO;I)V

    invoke-static {v0}, Lf7/c;->b(Ljava/lang/Runnable;)V

    return-void
.end method

.method public static Q(Lcom/cloud/hisavana/sdk/data/bean/response/AdsDTO;)V
    .locals 1

    new-instance v0, Lcom/cloud/hisavana/sdk/common/athena/AthenaTracker$24;

    invoke-direct {v0, p0}, Lcom/cloud/hisavana/sdk/common/athena/AthenaTracker$24;-><init>(Lcom/cloud/hisavana/sdk/data/bean/response/AdsDTO;)V

    invoke-static {v0}, Lf7/c;->b(Ljava/lang/Runnable;)V

    return-void
.end method

.method public static R(Lcom/cloud/hisavana/sdk/data/bean/request/AdxImpBean;)V
    .locals 1

    new-instance v0, Lcom/cloud/hisavana/sdk/common/athena/AthenaTracker$13;

    invoke-direct {v0, p0}, Lcom/cloud/hisavana/sdk/common/athena/AthenaTracker$13;-><init>(Lcom/cloud/hisavana/sdk/data/bean/request/AdxImpBean;)V

    invoke-static {v0}, Lf7/c;->b(Ljava/lang/Runnable;)V

    return-void
.end method

.method public static S(Ljava/util/List;Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;Lcom/cloud/hisavana/sdk/data/bean/request/AdxImpBean;I)V
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lcom/cloud/hisavana/sdk/data/bean/response/AdsDTO;",
            ">;",
            "Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;",
            "Lcom/cloud/hisavana/sdk/data/bean/request/AdxImpBean;",
            "I)V"
        }
    .end annotation

    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    if-eqz p0, :cond_0

    invoke-interface {p0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p0

    :goto_0
    invoke-interface {p0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_0

    invoke-interface {p0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/cloud/hisavana/sdk/data/bean/response/AdsDTO;

    invoke-interface {v0, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_0

    :cond_0
    new-instance p0, Lcom/cloud/hisavana/sdk/common/athena/AthenaTracker$14;

    invoke-direct {p0, v0, p1, p2, p3}, Lcom/cloud/hisavana/sdk/common/athena/AthenaTracker$14;-><init>(Ljava/util/List;Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;Lcom/cloud/hisavana/sdk/data/bean/request/AdxImpBean;I)V

    invoke-static {p0}, Lf7/c;->b(Ljava/lang/Runnable;)V

    return-void
.end method

.method public static T(Ljava/lang/String;I)V
    .locals 1

    new-instance v0, Lcom/cloud/hisavana/sdk/common/athena/AthenaTracker$41;

    invoke-direct {v0, p0, p1}, Lcom/cloud/hisavana/sdk/common/athena/AthenaTracker$41;-><init>(Ljava/lang/String;I)V

    invoke-static {v0}, Lf7/c;->b(Ljava/lang/Runnable;)V

    return-void
.end method

.method public static U(Lcom/cloud/hisavana/sdk/data/bean/response/AdsDTO;Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;ILjava/lang/String;)V
    .locals 1

    new-instance v0, Lcom/cloud/hisavana/sdk/common/athena/AthenaTracker$33;

    invoke-direct {v0, p0, p1, p2, p3}, Lcom/cloud/hisavana/sdk/common/athena/AthenaTracker$33;-><init>(Lcom/cloud/hisavana/sdk/data/bean/response/AdsDTO;Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;ILjava/lang/String;)V

    invoke-static {v0}, Lf7/c;->b(Ljava/lang/Runnable;)V

    return-void
.end method

.method public static V(Lcom/cloud/hisavana/sdk/data/bean/response/AdsDTO;Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;)V
    .locals 1

    new-instance v0, Lcom/cloud/hisavana/sdk/common/athena/AthenaTracker$32;

    invoke-direct {v0, p0, p1}, Lcom/cloud/hisavana/sdk/common/athena/AthenaTracker$32;-><init>(Lcom/cloud/hisavana/sdk/data/bean/response/AdsDTO;Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;)V

    invoke-static {v0}, Lf7/c;->b(Ljava/lang/Runnable;)V

    return-void
.end method

.method public static W(Lcom/cloud/hisavana/sdk/data/bean/response/AdsDTO;Lcom/cloud/hisavana/sdk/common/bean/ProgressData;)V
    .locals 1

    new-instance v0, Lcom/cloud/hisavana/sdk/common/athena/AthenaTracker$40;

    invoke-direct {v0, p0, p1}, Lcom/cloud/hisavana/sdk/common/athena/AthenaTracker$40;-><init>(Lcom/cloud/hisavana/sdk/data/bean/response/AdsDTO;Lcom/cloud/hisavana/sdk/common/bean/ProgressData;)V

    invoke-static {v0}, Lf7/c;->b(Ljava/lang/Runnable;)V

    return-void
.end method

.method public static X(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;ILjava/lang/String;Ljava/lang/String;IJ)V
    .locals 11

    invoke-static {p1}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    if-eqz v0, :cond_0

    return-void

    :cond_0
    new-instance v0, Lcom/cloud/hisavana/sdk/common/athena/AthenaTracker$39;

    move-object v1, v0

    move-object v2, p0

    move-object v3, p1

    move-object v4, p2

    move v5, p3

    move-object v6, p4

    move-object/from16 v7, p5

    move/from16 v8, p6

    move-wide/from16 v9, p7

    invoke-direct/range {v1 .. v10}, Lcom/cloud/hisavana/sdk/common/athena/AthenaTracker$39;-><init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;ILjava/lang/String;Ljava/lang/String;IJ)V

    invoke-static {v0}, Lf7/c;->b(Ljava/lang/Runnable;)V

    return-void
.end method

.method public static Y(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;IIJ)V
    .locals 9

    const/4 v0, -0x1

    if-eq p4, v0, :cond_0

    invoke-static {p1}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    if-eqz v0, :cond_0

    return-void

    :cond_0
    new-instance v0, Lcom/cloud/hisavana/sdk/common/athena/AthenaTracker$38;

    move-object v1, v0

    move-object v2, p0

    move-object v3, p1

    move-object v4, p2

    move v5, p3

    move v6, p4

    move-wide v7, p5

    invoke-direct/range {v1 .. v8}, Lcom/cloud/hisavana/sdk/common/athena/AthenaTracker$38;-><init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;IIJ)V

    invoke-static {v0}, Lf7/c;->b(Ljava/lang/Runnable;)V

    return-void
.end method

.method public static Z(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V
    .locals 1

    invoke-static {p1}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    if-eqz v0, :cond_0

    return-void

    :cond_0
    new-instance v0, Lcom/cloud/hisavana/sdk/common/athena/AthenaTracker$36;

    invoke-direct {v0, p0, p1, p2, p3}, Lcom/cloud/hisavana/sdk/common/athena/AthenaTracker$36;-><init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    invoke-static {v0}, Lf7/c;->b(Ljava/lang/Runnable;)V

    return-void
.end method

.method public static synthetic a(Lcom/cloud/hisavana/sdk/ext/attr/AttrData;)V
    .locals 0

    invoke-static {p0}, Lcom/cloud/hisavana/sdk/common/athena/AthenaTracker;->j(Lcom/cloud/hisavana/sdk/ext/attr/AttrData;)V

    return-void
.end method

.method public static a0(Lcom/cloud/hisavana/sdk/data/bean/response/AdsDTO;)V
    .locals 1

    new-instance v0, Lcom/cloud/hisavana/sdk/common/athena/AthenaTracker$16;

    invoke-direct {v0, p0}, Lcom/cloud/hisavana/sdk/common/athena/AthenaTracker$16;-><init>(Lcom/cloud/hisavana/sdk/data/bean/response/AdsDTO;)V

    invoke-static {v0}, Lf7/c;->b(Ljava/lang/Runnable;)V

    return-void
.end method

.method public static synthetic b(Lcom/cloud/hisavana/sdk/ext/attr/AttrData;)V
    .locals 0

    invoke-static {p0}, Lcom/cloud/hisavana/sdk/common/athena/AthenaTracker;->i(Lcom/cloud/hisavana/sdk/ext/attr/AttrData;)V

    return-void
.end method

.method public static synthetic c(Landroid/os/Bundle;)V
    .locals 0

    invoke-static {p0}, Lcom/cloud/hisavana/sdk/common/athena/AthenaTracker;->e(Landroid/os/Bundle;)V

    return-void
.end method

.method public static synthetic d(Lcom/cloud/hisavana/sdk/data/bean/response/AdsDTO;Landroid/os/Bundle;)V
    .locals 0

    invoke-static {p0, p1}, Lcom/cloud/hisavana/sdk/common/athena/AthenaTracker;->f(Lcom/cloud/hisavana/sdk/data/bean/response/AdsDTO;Landroid/os/Bundle;)V

    return-void
.end method

.method public static e(Landroid/os/Bundle;)V
    .locals 2

    invoke-static {}, Lcom/transsion/core/utils/e;->e()I

    move-result v0

    add-int/lit8 v0, v0, -0x1

    const-string v1, "screen_angular"

    invoke-virtual {p0, v1, v0}, Landroid/os/BaseBundle;->putInt(Ljava/lang/String;I)V

    return-void
.end method

.method public static f(Lcom/cloud/hisavana/sdk/data/bean/response/AdsDTO;Landroid/os/Bundle;)V
    .locals 1

    if-eqz p0, :cond_2

    if-nez p1, :cond_0

    goto :goto_0

    :cond_0
    invoke-virtual {p0}, Lcom/cloud/hisavana/sdk/data/bean/response/AdsDTO;->getDisplayRule()Lcom/cloud/hisavana/sdk/common/constant/Constants$AdDisplayRule;

    move-result-object p0

    sget-object v0, Lcom/cloud/hisavana/sdk/common/constant/Constants$AdDisplayRule;->RU:Lcom/cloud/hisavana/sdk/common/constant/Constants$AdDisplayRule;

    if-eq p0, v0, :cond_1

    return-void

    :cond_1
    const-string p0, "compliance"

    const-string v0, "1"

    invoke-virtual {p1, p0, v0}, Landroid/os/BaseBundle;->putString(Ljava/lang/String;Ljava/lang/String;)V

    :cond_2
    :goto_0
    return-void
.end method

.method public static g(Landroid/os/Bundle;)Landroid/os/Bundle;
    .locals 2

    if-nez p0, :cond_0

    new-instance p0, Landroid/os/Bundle;

    invoke-direct {p0}, Landroid/os/Bundle;-><init>()V

    :cond_0
    invoke-static {}, Lgm/a;->a()Landroid/content/Context;

    move-result-object v0

    invoke-static {v0}, La7/s;->c(Landroid/content/Context;)Landroid/content/pm/PackageInfo;

    move-result-object v0

    if-eqz v0, :cond_1

    iget v0, v0, Landroid/content/pm/PackageInfo;->versionCode:I

    invoke-static {v0}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    move-result-object v0

    const-string v1, "ps_version"

    invoke-virtual {p0, v1, v0}, Landroid/os/BaseBundle;->putString(Ljava/lang/String;Ljava/lang/String;)V

    :cond_1
    invoke-static {}, Lgm/a;->a()Landroid/content/Context;

    move-result-object v0

    invoke-static {v0}, Lcom/cloud/sdk/commonutil/util/MitNetUtil;->c(Landroid/content/Context;)Z

    move-result v0

    const-string v1, "net"

    invoke-virtual {p0, v1, v0}, Landroid/os/BaseBundle;->putInt(Ljava/lang/String;I)V

    sget-object v0, Lcom/cloud/hisavana/sdk/api/config/AdManager;->b:Ljava/lang/String;

    const-string v1, "app_id"

    invoke-virtual {p0, v1, v0}, Landroid/os/BaseBundle;->putString(Ljava/lang/String;Ljava/lang/String;)V

    invoke-static {p0}, Lf7/c;->a(Landroid/os/Bundle;)Landroid/os/Bundle;

    move-result-object p0

    return-object p0
.end method

.method public static h(ZLandroid/content/Context;)V
    .locals 3

    sget-boolean v0, Lcom/cloud/hisavana/sdk/common/athena/AthenaTracker;->a:Z

    if-nez v0, :cond_0

    const/16 v0, 0x96b

    const-string v1, "SSP"

    const/4 v2, 0x0

    invoke-static {p1, v1, v0, p0, v2}, Lcom/transsion/ga/AthenaAnalytics;->F(Landroid/content/Context;Ljava/lang/String;IZZ)V

    const/16 v0, 0x223d

    invoke-static {p1, v1, v0, p0, v2}, Lcom/transsion/ga/AthenaAnalytics;->F(Landroid/content/Context;Ljava/lang/String;IZZ)V

    const/4 p0, 0x1

    sput-boolean p0, Lcom/cloud/hisavana/sdk/common/athena/AthenaTracker;->a:Z

    :cond_0
    return-void
.end method

.method public static synthetic i(Lcom/cloud/hisavana/sdk/ext/attr/AttrData;)V
    .locals 0

    invoke-static {p0}, Lcom/cloud/hisavana/sdk/common/athena/AthenaTracker$a;->a(Lcom/cloud/hisavana/sdk/ext/attr/AttrData;)V

    return-void
.end method

.method public static synthetic j(Lcom/cloud/hisavana/sdk/ext/attr/AttrData;)V
    .locals 0

    invoke-static {p0}, Lcom/cloud/hisavana/sdk/common/athena/AthenaTracker$a;->b(Lcom/cloud/hisavana/sdk/ext/attr/AttrData;)V

    return-void
.end method

.method public static k(Lcom/cloud/hisavana/sdk/data/bean/response/AdsDTO;J)V
    .locals 1

    new-instance v0, Lcom/cloud/hisavana/sdk/common/athena/AthenaTracker$25;

    invoke-direct {v0, p0, p1, p2}, Lcom/cloud/hisavana/sdk/common/athena/AthenaTracker$25;-><init>(Lcom/cloud/hisavana/sdk/data/bean/response/AdsDTO;J)V

    invoke-static {v0}, Lf7/c;->b(Ljava/lang/Runnable;)V

    return-void
.end method

.method public static l(Ljava/util/List;Ljava/lang/String;ILjava/lang/String;IJLjava/lang/String;ZLjava/lang/String;I)V
    .locals 13
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lcom/cloud/hisavana/sdk/data/bean/response/AdsDTO;",
            ">;",
            "Ljava/lang/String;",
            "I",
            "Ljava/lang/String;",
            "IJ",
            "Ljava/lang/String;",
            "Z",
            "Ljava/lang/String;",
            "I)V"
        }
    .end annotation

    move-object v0, p0

    new-instance v1, Ljava/util/ArrayList;

    invoke-direct {v1}, Ljava/util/ArrayList;-><init>()V

    if-eqz v0, :cond_0

    invoke-interface {v1, p0}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    :cond_0
    new-instance v12, Lcom/cloud/hisavana/sdk/common/athena/AthenaTracker$22;

    move-object v0, v12

    move-object v2, p1

    move v3, p2

    move-object/from16 v4, p3

    move/from16 v5, p4

    move-wide/from16 v6, p5

    move-object/from16 v8, p7

    move/from16 v9, p8

    move-object/from16 v10, p9

    move/from16 v11, p10

    invoke-direct/range {v0 .. v11}, Lcom/cloud/hisavana/sdk/common/athena/AthenaTracker$22;-><init>(Ljava/util/List;Ljava/lang/String;ILjava/lang/String;IJLjava/lang/String;ZLjava/lang/String;I)V

    invoke-static {v12}, Lf7/c;->b(Ljava/lang/Runnable;)V

    return-void
.end method

.method public static m(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/util/List;Ljava/util/List;ZI)V
    .locals 9
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;",
            "Ljava/util/List<",
            "Ljava/lang/Integer;",
            ">;ZI)V"
        }
    .end annotation

    new-instance v8, Lcom/cloud/hisavana/sdk/common/athena/AthenaTracker$31;

    move-object v0, v8

    move-object v1, p0

    move-object v2, p1

    move-object v3, p2

    move-object v4, p3

    move-object v5, p4

    move v6, p5

    move v7, p6

    invoke-direct/range {v0 .. v7}, Lcom/cloud/hisavana/sdk/common/athena/AthenaTracker$31;-><init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/util/List;Ljava/util/List;ZI)V

    invoke-static {v8}, Lf7/c;->b(Ljava/lang/Runnable;)V

    return-void
.end method

.method public static n(Lcom/cloud/hisavana/sdk/data/bean/response/AdsDTO;Ljava/lang/String;)V
    .locals 1

    new-instance v0, Lcom/cloud/hisavana/sdk/common/athena/AthenaTracker$28;

    invoke-direct {v0, p0, p1}, Lcom/cloud/hisavana/sdk/common/athena/AthenaTracker$28;-><init>(Lcom/cloud/hisavana/sdk/data/bean/response/AdsDTO;Ljava/lang/String;)V

    invoke-static {v0}, Lf7/c;->b(Ljava/lang/Runnable;)V

    return-void
.end method

.method public static o(Lcom/cloud/hisavana/sdk/data/bean/response/AdsDTO;)V
    .locals 2

    if-eqz p0, :cond_0

    invoke-virtual {p0}, Lcom/cloud/hisavana/sdk/data/bean/response/AdsDTO;->getSource()I

    move-result v0

    const/4 v1, 0x4

    if-ne v0, v1, :cond_0

    return-void

    :cond_0
    new-instance v0, Lcom/cloud/hisavana/sdk/common/athena/AthenaTracker$27;

    invoke-direct {v0, p0}, Lcom/cloud/hisavana/sdk/common/athena/AthenaTracker$27;-><init>(Lcom/cloud/hisavana/sdk/data/bean/response/AdsDTO;)V

    invoke-static {v0}, Lf7/c;->b(Ljava/lang/Runnable;)V

    return-void
.end method

.method public static p(Ljava/lang/String;Ljava/lang/String;I)V
    .locals 1

    new-instance v0, Lcom/cloud/hisavana/sdk/common/athena/AthenaTracker$43;

    invoke-direct {v0, p0, p1, p2}, Lcom/cloud/hisavana/sdk/common/athena/AthenaTracker$43;-><init>(Ljava/lang/String;Ljava/lang/String;I)V

    invoke-static {v0}, Lf7/c;->b(Ljava/lang/Runnable;)V

    return-void
.end method

.method public static q(Lcom/cloud/hisavana/sdk/data/bean/response/AdsDTO;Z)V
    .locals 1

    new-instance v0, Lcom/cloud/hisavana/sdk/common/athena/AthenaTracker$37;

    invoke-direct {v0, p0, p1}, Lcom/cloud/hisavana/sdk/common/athena/AthenaTracker$37;-><init>(Lcom/cloud/hisavana/sdk/data/bean/response/AdsDTO;Z)V

    invoke-static {v0}, Lf7/c;->b(Ljava/lang/Runnable;)V

    return-void
.end method

.method public static r(Ljava/lang/String;Ljava/lang/String;ZLjava/lang/String;IIZ)V
    .locals 9

    new-instance v8, Lcom/cloud/hisavana/sdk/common/athena/AthenaTracker$35;

    move-object v0, v8

    move-object v1, p0

    move-object v2, p1

    move v3, p2

    move-object v4, p3

    move v5, p4

    move v6, p5

    move v7, p6

    invoke-direct/range {v0 .. v7}, Lcom/cloud/hisavana/sdk/common/athena/AthenaTracker$35;-><init>(Ljava/lang/String;Ljava/lang/String;ZLjava/lang/String;IIZ)V

    invoke-static {v8}, Lf7/c;->b(Ljava/lang/Runnable;)V

    return-void
.end method

.method public static s(Ljava/lang/String;Ljava/lang/String;Z)V
    .locals 1

    new-instance v0, Lcom/cloud/hisavana/sdk/common/athena/AthenaTracker$34;

    invoke-direct {v0, p0, p1, p2}, Lcom/cloud/hisavana/sdk/common/athena/AthenaTracker$34;-><init>(Ljava/lang/String;Ljava/lang/String;Z)V

    invoke-static {v0}, Lf7/c;->b(Ljava/lang/Runnable;)V

    return-void
.end method

.method public static t(Ljava/lang/String;Lcom/cloud/hisavana/sdk/data/bean/response/AdsDTO;Ljava/lang/Boolean;)V
    .locals 2

    if-eqz p1, :cond_0

    invoke-virtual {p1}, Lcom/cloud/hisavana/sdk/data/bean/response/AdsDTO;->getSource()I

    move-result v0

    const/4 v1, 0x4

    if-ne v0, v1, :cond_0

    return-void

    :cond_0
    new-instance v0, Lcom/cloud/hisavana/sdk/common/athena/AthenaTracker$18;

    invoke-direct {v0, p0, p1, p2}, Lcom/cloud/hisavana/sdk/common/athena/AthenaTracker$18;-><init>(Ljava/lang/String;Lcom/cloud/hisavana/sdk/data/bean/response/AdsDTO;Ljava/lang/Boolean;)V

    invoke-static {v0}, Lf7/c;->b(Ljava/lang/Runnable;)V

    return-void
.end method

.method public static u(Lcom/cloud/hisavana/sdk/ext/attr/AttrData;)V
    .locals 1

    new-instance v0, Lcom/cloud/hisavana/sdk/common/athena/a;

    invoke-direct {v0, p0}, Lcom/cloud/hisavana/sdk/common/athena/a;-><init>(Lcom/cloud/hisavana/sdk/ext/attr/AttrData;)V

    invoke-static {v0}, Lf7/c;->b(Ljava/lang/Runnable;)V

    return-void
.end method

.method public static v(ILjava/lang/String;)V
    .locals 1

    new-instance v0, Lcom/cloud/hisavana/sdk/common/athena/AthenaTracker$11;

    invoke-direct {v0, p0, p1}, Lcom/cloud/hisavana/sdk/common/athena/AthenaTracker$11;-><init>(ILjava/lang/String;)V

    invoke-static {v0}, Lf7/c;->b(Ljava/lang/Runnable;)V

    return-void
.end method

.method public static w(ILjava/lang/String;IILjava/lang/String;Ljava/lang/String;)V
    .locals 8

    new-instance v7, Lcom/cloud/hisavana/sdk/common/athena/AthenaTracker$12;

    move-object v0, v7

    move v1, p0

    move-object v2, p1

    move v3, p2

    move v4, p3

    move-object v5, p4

    move-object v6, p5

    invoke-direct/range {v0 .. v6}, Lcom/cloud/hisavana/sdk/common/athena/AthenaTracker$12;-><init>(ILjava/lang/String;IILjava/lang/String;Ljava/lang/String;)V

    invoke-static {v7}, Lf7/c;->b(Ljava/lang/Runnable;)V

    return-void
.end method

.method public static x(Lcom/cloud/hisavana/sdk/data/bean/response/AdsDTO;Ljava/lang/String;Z)V
    .locals 1

    new-instance v0, Lcom/cloud/hisavana/sdk/common/athena/AthenaTracker$8;

    invoke-direct {v0, p0, p1, p2}, Lcom/cloud/hisavana/sdk/common/athena/AthenaTracker$8;-><init>(Lcom/cloud/hisavana/sdk/data/bean/response/AdsDTO;Ljava/lang/String;Z)V

    invoke-static {v0}, Lf7/c;->b(Ljava/lang/Runnable;)V

    return-void
.end method

.method public static y(Ljava/util/List;Ljava/lang/String;Ljava/lang/String;J)V
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lcom/cloud/hisavana/sdk/data/bean/response/AdsDTO;",
            ">;",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            "J)V"
        }
    .end annotation

    new-instance v1, Ljava/util/ArrayList;

    invoke-direct {v1}, Ljava/util/ArrayList;-><init>()V

    if-eqz p0, :cond_0

    invoke-interface {v1, p0}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    :cond_0
    new-instance p0, Lcom/cloud/hisavana/sdk/common/athena/AthenaTracker$23;

    move-object v0, p0

    move-object v2, p1

    move-object v3, p2

    move-wide v4, p3

    invoke-direct/range {v0 .. v5}, Lcom/cloud/hisavana/sdk/common/athena/AthenaTracker$23;-><init>(Ljava/util/List;Ljava/lang/String;Ljava/lang/String;J)V

    invoke-static {p0}, Lf7/c;->b(Ljava/lang/Runnable;)V

    return-void
.end method

.method public static z(Ljava/lang/String;)V
    .locals 1

    new-instance v0, Lcom/cloud/hisavana/sdk/common/athena/AthenaTracker$4;

    invoke-direct {v0, p0}, Lcom/cloud/hisavana/sdk/common/athena/AthenaTracker$4;-><init>(Ljava/lang/String;)V

    invoke-static {v0}, Lf7/c;->b(Ljava/lang/Runnable;)V

    return-void
.end method
