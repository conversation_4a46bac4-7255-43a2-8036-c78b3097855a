.class public Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;
.super Ljava/lang/Object;


# static fields
.field public static final CODE_ADM_SCALE_TAKE_FAILED:I = 0xbbe

.field public static final CODE_ADS_ARE_FILTERED:I = 0xfa5

.field public static final CODE_AD_DATA_IS_NULL:I = 0xfa2

.field public static final CODE_AD_IMPRESSION_LIMIT:I = 0xfa8

.field public static final CODE_AD_MATERIAL_TAKE_FAILED:I = 0xbba

.field public static final CODE_AD_SHOW_NOT_IN_INTERVAL:I = 0xfa9

.field public static final CODE_AD_TYPE_MISMATCH:I = 0xfa4

.field public static final CODE_AD_UNIT_CONFIG_IS_EMPTY:I = 0xfab

.field public static final CODE_BITMAP_TAKE_FAILED:I = 0xbbf

.field public static final CODE_DESTROY_BEFORE_FILLING:I = 0xfaa

.field public static final CODE_INTERVAL_NOT_REACHED:I = 0x7d5

.field public static final CODE_LOAD_FAILED_CAUSE_DESTROY:I = 0xfac

.field public static final CODE_LOAD_FAILED_CAUSE_LOADING:I = 0xfab

.field public static final CODE_MEDIA_LOAD_TIME_OUT:I = 0xfa1

.field public static final CODE_NETWORK_NOT_CONNECTED:I = 0x7d4

.field public static final CODE_NETWORK_REQUEST_TIME_OUT:I = 0x7d3

.field public static final CODE_PLACEMENT_ID_MISMATCH:I = 0xfa3

.field public static final CODE_RESPONSE_IS_NULL:I = 0x7d2

.field public static final CODE_RESPONSE_PARSING_FAILED:I = 0x7d1

.field public static final CODE_SPLASH_MATERIAL_LOAD_FAILED:I = 0xbb9

.field public static final CODE_STORE_MATERIAL_TAKE_FAILED:I = 0xbbb

.field public static final CODE_SUCCESS:I = 0x0

.field public static final CODE_TAKE_AD_FAILED:I = 0xfa6

.field public static final CODE_UNKNOWN:I = -0x1

.field public static final CODE_ZIP_MATERIAL_DECOMPRESS_FAILED:I = 0xbbd

.field public static final CODE_ZIP_MATERIAL_TAKE_FAILED:I = 0xbbc

.field public static final ERROR_ADM_SCALE_TAKE_FAILED:Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;

.field public static final ERROR_ADS_ARE_FILTERED:Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;

.field public static final ERROR_AD_DATA_IS_NULL:Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;

.field public static final ERROR_AD_IMPRESSION_LIMIT:Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;

.field public static final ERROR_AD_MATERIAL_TAKE_FAILED:Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;

.field public static final ERROR_AD_SHOW_NOT_IN_INTERVAL:Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;

.field public static final ERROR_AD_TYPE_MISMATCH:Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;

.field public static final ERROR_AD_UNIT_CONFIG_IS_EMPTY:Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;

.field public static final ERROR_BITMAP_TAKE_FAILED:Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;

.field public static final ERROR_DESTROY_BEFORE_FILLING:Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;

.field public static final ERROR_LOAD_FAILED_CAUSE_DESTROY:Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;

.field public static final ERROR_LOAD_FAILED_CAUSE_LOADING:Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;

.field public static final ERROR_MEDIA_LOAD_TIME_OUT:Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;

.field public static final ERROR_NETWORK_NOT_CONNECTED:Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;

.field public static final ERROR_NETWORK_REQUEST_TIME_OUT:Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;

.field public static final ERROR_PLACEMENT_ID_MISMATCH:Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;

.field public static final ERROR_RESPONSE_IS_NULL:Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;

.field public static final ERROR_RESPONSE_PARSING_FAILED:Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;

.field public static final ERROR_STORE_MATERIAL_TAKE_FAILED:Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;

.field public static final ERROR_TAKE_AD_FAILED:Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;

.field public static final ERROR_UNKNOWN:Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;

.field public static final ERROR_ZIP_MATERIAL_DECOMPRESS_FAILED:Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;

.field public static final ERROR_ZIP_MATERIAL_TAKE_FAILED:Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;

.field private static final MAIN_VIDEO_DOWNLOAD_FAIL:I = 0xbc1

.field public static final MAIN_VIDEO_DOWNLOAD_FAIL_ERROR:Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;

.field private static final MATERIAL_TYPE_IS_NOT_VIDEO:I = 0xbc4

.field public static final MATERIAL_TYPE_IS_NOT_VIDEO_ERROR:Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;

.field private static final NO_ADSDTO_FAIL:I = 0xbc2

.field public static final NO_ADSDTO_FAIL_ERROR:Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;

.field private static final NO_MAIN_VIDEO_DATA:I = 0xbc0

.field public static final NO_MAIN_VIDEO_DATA_ERROR:Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;

.field public static final VAST_VIDEO_PLAY_ERROR:I = 0xbc3


# instance fields
.field private final errorCode:I

.field private final errorMessage:Ljava/lang/String;


# direct methods
.method static constructor <clinit>()V
    .locals 4

    new-instance v0, Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;

    const/4 v1, -0x1

    const-string v2, "unknown error"

    invoke-direct {v0, v1, v2}, Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;-><init>(ILjava/lang/String;)V

    sput-object v0, Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;->ERROR_UNKNOWN:Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;

    new-instance v0, Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;

    const/16 v1, 0x7d1

    const-string v2, "response parse failed"

    invoke-direct {v0, v1, v2}, Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;-><init>(ILjava/lang/String;)V

    sput-object v0, Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;->ERROR_RESPONSE_PARSING_FAILED:Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;

    new-instance v0, Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;

    const/16 v1, 0x7d2

    const-string v2, "response is null"

    invoke-direct {v0, v1, v2}, Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;-><init>(ILjava/lang/String;)V

    sput-object v0, Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;->ERROR_RESPONSE_IS_NULL:Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;

    new-instance v0, Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;

    const/16 v1, 0x7d3

    const-string v2, "network request timeout"

    invoke-direct {v0, v1, v2}, Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;-><init>(ILjava/lang/String;)V

    sput-object v0, Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;->ERROR_NETWORK_REQUEST_TIME_OUT:Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;

    new-instance v0, Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;

    const/16 v1, 0x7d4

    const-string v2, "network is not connected"

    invoke-direct {v0, v1, v2}, Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;-><init>(ILjava/lang/String;)V

    sput-object v0, Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;->ERROR_NETWORK_NOT_CONNECTED:Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;

    new-instance v0, Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;

    const/16 v1, 0xbba

    const-string v2, "creative url is empty or download failed"

    invoke-direct {v0, v1, v2}, Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;-><init>(ILjava/lang/String;)V

    sput-object v0, Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;->ERROR_AD_MATERIAL_TAKE_FAILED:Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;

    new-instance v0, Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;

    const/16 v1, 0xbbb

    const-string v2, "store material url is empty or download failed"

    invoke-direct {v0, v1, v2}, Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;-><init>(ILjava/lang/String;)V

    sput-object v0, Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;->ERROR_STORE_MATERIAL_TAKE_FAILED:Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;

    new-instance v0, Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;

    const/16 v1, 0xbbc

    const-string v2, "material zip load failed"

    invoke-direct {v0, v1, v2}, Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;-><init>(ILjava/lang/String;)V

    sput-object v0, Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;->ERROR_ZIP_MATERIAL_TAKE_FAILED:Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;

    new-instance v0, Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;

    const/16 v1, 0xbbd

    const-string v2, "material zip decompress failed"

    invoke-direct {v0, v1, v2}, Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;-><init>(ILjava/lang/String;)V

    sput-object v0, Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;->ERROR_ZIP_MATERIAL_DECOMPRESS_FAILED:Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;

    new-instance v0, Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;

    const/16 v1, 0xbbe

    const-string v2, "adm scales is not fit"

    invoke-direct {v0, v1, v2}, Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;-><init>(ILjava/lang/String;)V

    sput-object v0, Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;->ERROR_ADM_SCALE_TAKE_FAILED:Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;

    new-instance v0, Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;

    const/16 v1, 0xbbf

    const-string v2, "image acquisition failed"

    invoke-direct {v0, v1, v2}, Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;-><init>(ILjava/lang/String;)V

    sput-object v0, Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;->ERROR_BITMAP_TAKE_FAILED:Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;

    new-instance v0, Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;

    const/16 v1, 0xbc0

    const-string v2, "video resource is empty"

    invoke-direct {v0, v1, v2}, Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;-><init>(ILjava/lang/String;)V

    sput-object v0, Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;->NO_MAIN_VIDEO_DATA_ERROR:Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;

    new-instance v0, Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;

    const/16 v1, 0xbc1

    invoke-direct {v0, v1, v2}, Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;-><init>(ILjava/lang/String;)V

    sput-object v0, Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;->MAIN_VIDEO_DOWNLOAD_FAIL_ERROR:Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;

    new-instance v0, Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;

    const/16 v1, 0xbc2

    const-string v2, "ads is null"

    invoke-direct {v0, v1, v2}, Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;-><init>(ILjava/lang/String;)V

    sput-object v0, Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;->NO_ADSDTO_FAIL_ERROR:Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;

    new-instance v0, Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;

    const/16 v1, 0xbc4

    const-string v2, "material type is not video"

    invoke-direct {v0, v1, v2}, Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;-><init>(ILjava/lang/String;)V

    sput-object v0, Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;->MATERIAL_TYPE_IS_NOT_VIDEO_ERROR:Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;

    new-instance v0, Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;

    const/16 v1, 0xfa1

    const-string v2, "Media load ad timeout"

    invoke-direct {v0, v1, v2}, Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;-><init>(ILjava/lang/String;)V

    sput-object v0, Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;->ERROR_MEDIA_LOAD_TIME_OUT:Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;

    new-instance v0, Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;

    const/16 v1, 0xfa2

    const-string v2, "ad data is null"

    invoke-direct {v0, v1, v2}, Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;-><init>(ILjava/lang/String;)V

    sput-object v0, Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;->ERROR_AD_DATA_IS_NULL:Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;

    new-instance v0, Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;

    const/16 v1, 0xfa3

    const-string v2, "ad slot id does not match"

    invoke-direct {v0, v1, v2}, Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;-><init>(ILjava/lang/String;)V

    sput-object v0, Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;->ERROR_PLACEMENT_ID_MISMATCH:Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;

    new-instance v0, Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;

    const/16 v1, 0xfa4

    const-string v2, "ad type does not match"

    invoke-direct {v0, v1, v2}, Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;-><init>(ILjava/lang/String;)V

    sput-object v0, Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;->ERROR_AD_TYPE_MISMATCH:Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;

    new-instance v0, Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;

    const/16 v1, 0xfa5

    const-string v2, "returned ads are filtered"

    invoke-direct {v0, v1, v2}, Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;-><init>(ILjava/lang/String;)V

    sput-object v0, Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;->ERROR_ADS_ARE_FILTERED:Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;

    new-instance v0, Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;

    const/16 v1, 0xfa6

    const-string v2, "no ads available"

    invoke-direct {v0, v1, v2}, Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;-><init>(ILjava/lang/String;)V

    sput-object v0, Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;->ERROR_TAKE_AD_FAILED:Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;

    new-instance v0, Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;

    const-string v1, "unable to obtain valid ad code seat id"

    const/16 v2, 0xfab

    invoke-direct {v0, v2, v1}, Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;-><init>(ILjava/lang/String;)V

    sput-object v0, Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;->ERROR_AD_UNIT_CONFIG_IS_EMPTY:Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;

    new-instance v0, Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;

    const/16 v1, 0xfa8

    const-string v3, "ad impression limit"

    invoke-direct {v0, v1, v3}, Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;-><init>(ILjava/lang/String;)V

    sput-object v0, Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;->ERROR_AD_IMPRESSION_LIMIT:Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;

    new-instance v0, Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;

    const/16 v1, 0xfa9

    const-string v3, "ad show not in interval"

    invoke-direct {v0, v1, v3}, Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;-><init>(ILjava/lang/String;)V

    sput-object v0, Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;->ERROR_AD_SHOW_NOT_IN_INTERVAL:Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;

    new-instance v0, Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;

    const/16 v1, 0xfaa

    const-string v3, "destroyed before filling"

    invoke-direct {v0, v1, v3}, Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;-><init>(ILjava/lang/String;)V

    sput-object v0, Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;->ERROR_DESTROY_BEFORE_FILLING:Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;

    new-instance v0, Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;

    const-string v1, "load ad failed\uff0cbecause loading"

    invoke-direct {v0, v2, v1}, Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;-><init>(ILjava/lang/String;)V

    sput-object v0, Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;->ERROR_LOAD_FAILED_CAUSE_LOADING:Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;

    new-instance v0, Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;

    const/16 v1, 0xfac

    const-string v2, "load ad failed\uff0cbecause destroyed"

    invoke-direct {v0, v1, v2}, Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;-><init>(ILjava/lang/String;)V

    sput-object v0, Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;->ERROR_LOAD_FAILED_CAUSE_DESTROY:Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;

    return-void
.end method

.method public constructor <init>(ILjava/lang/String;)V
    .locals 1

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    invoke-static {p2}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    if-eqz v0, :cond_0

    const-string p2, "empty msg"

    :cond_0
    iput p1, p0, Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;->errorCode:I

    iput-object p2, p0, Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;->errorMessage:Ljava/lang/String;

    return-void
.end method


# virtual methods
.method public getErrorCode()I
    .locals 1

    iget v0, p0, Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;->errorCode:I

    return v0
.end method

.method public getErrorMessage()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;->errorMessage:Ljava/lang/String;

    return-object v0
.end method

.method public toString()Ljava/lang/String;
    .locals 2

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "TaErrorCode{errorCode="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget v1, p0, Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;->errorCode:I

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v1, ", errorMessage=\'"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v1, p0, Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;->errorMessage:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const/16 v1, 0x27

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    const/16 v1, 0x7d

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
