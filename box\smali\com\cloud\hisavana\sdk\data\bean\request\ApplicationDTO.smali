.class public Lcom/cloud/hisavana/sdk/data/bean/request/ApplicationDTO;
.super Ljava/lang/Object;


# instance fields
.field private installTime:Ljava/lang/String;

.field private packageName:Ljava/lang/String;

.field private psApiVersion:Ljava/lang/String;

.field private psChannel:Ljava/lang/String;

.field private psVersion:J

.field private sdkVersion:Ljava/lang/String;

.field private sdkVersionCode:I

.field private userAgent:Ljava/lang/String;

.field private version:Ljava/lang/String;

.field private versionInt:Ljava/lang/String;


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public getInstallTime()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/cloud/hisavana/sdk/data/bean/request/ApplicationDTO;->installTime:Ljava/lang/String;

    return-object v0
.end method

.method public getPackageName()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/cloud/hisavana/sdk/data/bean/request/ApplicationDTO;->packageName:Ljava/lang/String;

    return-object v0
.end method

.method public getPsApiVersion()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/cloud/hisavana/sdk/data/bean/request/ApplicationDTO;->psApiVersion:Ljava/lang/String;

    return-object v0
.end method

.method public getPsChannel()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/cloud/hisavana/sdk/data/bean/request/ApplicationDTO;->psChannel:Ljava/lang/String;

    return-object v0
.end method

.method public getPsVersion()J
    .locals 2

    iget-wide v0, p0, Lcom/cloud/hisavana/sdk/data/bean/request/ApplicationDTO;->psVersion:J

    return-wide v0
.end method

.method public getSdkVersion()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/cloud/hisavana/sdk/data/bean/request/ApplicationDTO;->sdkVersion:Ljava/lang/String;

    return-object v0
.end method

.method public getSdkVersionCode()I
    .locals 1

    iget v0, p0, Lcom/cloud/hisavana/sdk/data/bean/request/ApplicationDTO;->sdkVersionCode:I

    return v0
.end method

.method public getUserAgent()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/cloud/hisavana/sdk/data/bean/request/ApplicationDTO;->userAgent:Ljava/lang/String;

    return-object v0
.end method

.method public getVersion()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/cloud/hisavana/sdk/data/bean/request/ApplicationDTO;->version:Ljava/lang/String;

    return-object v0
.end method

.method public getVersionInt()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/cloud/hisavana/sdk/data/bean/request/ApplicationDTO;->versionInt:Ljava/lang/String;

    return-object v0
.end method

.method public setInstallTime(Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/cloud/hisavana/sdk/data/bean/request/ApplicationDTO;->installTime:Ljava/lang/String;

    return-void
.end method

.method public setPackageName(Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/cloud/hisavana/sdk/data/bean/request/ApplicationDTO;->packageName:Ljava/lang/String;

    return-void
.end method

.method public setPsApiVersion(Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/cloud/hisavana/sdk/data/bean/request/ApplicationDTO;->psApiVersion:Ljava/lang/String;

    return-void
.end method

.method public setPsChannel(Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/cloud/hisavana/sdk/data/bean/request/ApplicationDTO;->psChannel:Ljava/lang/String;

    return-void
.end method

.method public setPsVersion(J)V
    .locals 0

    iput-wide p1, p0, Lcom/cloud/hisavana/sdk/data/bean/request/ApplicationDTO;->psVersion:J

    return-void
.end method

.method public setSdkVersion(Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/cloud/hisavana/sdk/data/bean/request/ApplicationDTO;->sdkVersion:Ljava/lang/String;

    return-void
.end method

.method public setSdkVersionCode(I)V
    .locals 0

    iput p1, p0, Lcom/cloud/hisavana/sdk/data/bean/request/ApplicationDTO;->sdkVersionCode:I

    return-void
.end method

.method public setUserAgent(Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/cloud/hisavana/sdk/data/bean/request/ApplicationDTO;->userAgent:Ljava/lang/String;

    return-void
.end method

.method public setVersion(Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/cloud/hisavana/sdk/data/bean/request/ApplicationDTO;->version:Ljava/lang/String;

    return-void
.end method

.method public setVersionInt(Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/cloud/hisavana/sdk/data/bean/request/ApplicationDTO;->versionInt:Ljava/lang/String;

    return-void
.end method
