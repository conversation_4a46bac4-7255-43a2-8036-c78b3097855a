.class public Lcom/cloud/hisavana/sdk/data/bean/request/SdkVideo;
.super Ljava/lang/Object;


# instance fields
.field private mimes:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>()V
    .locals 2

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lcom/cloud/hisavana/sdk/data/bean/request/SdkVideo;->mimes:Ljava/util/List;

    const-string v1, "video/mp4"

    invoke-interface {v0, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    iget-object v0, p0, Lcom/cloud/hisavana/sdk/data/bean/request/SdkVideo;->mimes:Ljava/util/List;

    const-string v1, "video/m4a"

    invoke-interface {v0, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    iget-object v0, p0, Lcom/cloud/hisavana/sdk/data/bean/request/SdkVideo;->mimes:Ljava/util/List;

    const-string v1, "video/fmp4"

    invoke-interface {v0, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    iget-object v0, p0, Lcom/cloud/hisavana/sdk/data/bean/request/SdkVideo;->mimes:Ljava/util/List;

    const-string v1, "video/webm"

    invoke-interface {v0, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    iget-object v0, p0, Lcom/cloud/hisavana/sdk/data/bean/request/SdkVideo;->mimes:Ljava/util/List;

    const-string v1, "video/mkv"

    invoke-interface {v0, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    iget-object v0, p0, Lcom/cloud/hisavana/sdk/data/bean/request/SdkVideo;->mimes:Ljava/util/List;

    const-string v1, "video/ogg"

    invoke-interface {v0, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    iget-object v0, p0, Lcom/cloud/hisavana/sdk/data/bean/request/SdkVideo;->mimes:Ljava/util/List;

    const-string v1, "video/wav"

    invoke-interface {v0, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    iget-object v0, p0, Lcom/cloud/hisavana/sdk/data/bean/request/SdkVideo;->mimes:Ljava/util/List;

    const-string v1, "video/flv"

    invoke-interface {v0, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    iget-object v0, p0, Lcom/cloud/hisavana/sdk/data/bean/request/SdkVideo;->mimes:Ljava/util/List;

    const-string v1, "video/adts"

    invoke-interface {v0, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    iget-object v0, p0, Lcom/cloud/hisavana/sdk/data/bean/request/SdkVideo;->mimes:Ljava/util/List;

    const-string v1, "video/aac"

    invoke-interface {v0, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method


# virtual methods
.method public getMimes()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    iget-object v0, p0, Lcom/cloud/hisavana/sdk/data/bean/request/SdkVideo;->mimes:Ljava/util/List;

    return-object v0
.end method

.method public setMimes(Ljava/util/List;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lcom/cloud/hisavana/sdk/data/bean/request/SdkVideo;->mimes:Ljava/util/List;

    return-void
.end method
