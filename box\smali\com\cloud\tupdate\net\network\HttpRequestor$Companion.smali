.class public final Lcom/cloud/tupdate/net/network/HttpRequestor$Companion;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/cloud/tupdate/net/network/HttpRequestor;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "Companion"
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public synthetic constructor <init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    invoke-direct {p0}, Lcom/cloud/tupdate/net/network/HttpRequestor$Companion;-><init>()V

    return-void
.end method


# virtual methods
.method public final a()Lcom/cloud/tupdate/net/network/HttpRequestor;
    .locals 1

    sget-object v0, Lcom/cloud/tupdate/net/network/HttpRequestor$HttpRequestorHolder;->a:Lcom/cloud/tupdate/net/network/HttpRequestor$HttpRequestorHolder;

    invoke-virtual {v0}, Lcom/cloud/tupdate/net/network/HttpRequestor$HttpRequestorHolder;->a()Lcom/cloud/tupdate/net/network/HttpRequestor;

    move-result-object v0

    return-object v0
.end method
