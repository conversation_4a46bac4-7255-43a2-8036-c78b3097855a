.class final Lcom/cloud/tupdate/net/network/HttpRequestor$HttpRequestorHolder;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/cloud/tupdate/net/network/HttpRequestor;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "HttpRequestorHolder"
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation


# static fields
.field public static final a:Lcom/cloud/tupdate/net/network/HttpRequestor$HttpRequestorHolder;

.field public static final b:Lcom/cloud/tupdate/net/network/HttpRequestor;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    new-instance v0, Lcom/cloud/tupdate/net/network/HttpRequestor$HttpRequestorHolder;

    invoke-direct {v0}, Lcom/cloud/tupdate/net/network/HttpRequestor$HttpRequestorHolder;-><init>()V

    sput-object v0, Lcom/cloud/tupdate/net/network/HttpRequestor$HttpRequestorHolder;->a:Lcom/cloud/tupdate/net/network/HttpRequestor$HttpRequestorHolder;

    new-instance v0, Lcom/cloud/tupdate/net/network/HttpRequestor;

    invoke-direct {v0}, Lcom/cloud/tupdate/net/network/HttpRequestor;-><init>()V

    sput-object v0, Lcom/cloud/tupdate/net/network/HttpRequestor$HttpRequestorHolder;->b:Lcom/cloud/tupdate/net/network/HttpRequestor;

    return-void
.end method

.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public a()Lcom/cloud/tupdate/net/network/HttpRequestor;
    .locals 1

    sget-object v0, Lcom/cloud/tupdate/net/network/HttpRequestor$HttpRequestorHolder;->b:Lcom/cloud/tupdate/net/network/HttpRequestor;

    return-object v0
.end method
