.class public interface abstract Lcom/facebook/ads/AudienceNetworkAds$InitListener;
.super Ljava/lang/Object;


# annotations
.annotation build Landroidx/annotation/Keep;
.end annotation

.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/facebook/ads/AudienceNetworkAds;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "InitListener"
.end annotation


# virtual methods
.method public abstract onInitialized(Lcom/facebook/ads/AudienceNetworkAds$InitResult;)V
.end method
