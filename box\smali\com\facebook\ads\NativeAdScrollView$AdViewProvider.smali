.class public interface abstract Lcom/facebook/ads/NativeAdScrollView$AdViewProvider;
.super Ljava/lang/Object;


# annotations
.annotation build Landroidx/annotation/Keep;
.end annotation

.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/facebook/ads/NativeAdScrollView;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "AdViewProvider"
.end annotation


# virtual methods
.method public abstract createView(Lcom/facebook/ads/NativeAd;I)Landroid/view/View;
.end method

.method public abstract destroyView(Lcom/facebook/ads/NativeAd;Landroid/view/View;)V
.end method
