.class public interface abstract Lcom/facebook/ads/internal/api/AdComponentView;
.super Ljava/lang/Object;


# annotations
.annotation build Landroidx/annotation/Keep;
.end annotation


# virtual methods
.method public abstract addView(Landroid/view/View;)V
.end method

.method public abstract addView(Landroid/view/View;I)V
.end method

.method public abstract addView(Landroid/view/View;II)V
.end method

.method public abstract addView(Landroid/view/View;ILandroid/view/ViewGroup$LayoutParams;)V
.end method

.method public abstract addView(Landroid/view/View;Landroid/view/ViewGroup$LayoutParams;)V
.end method

.method public abstract onWindowFocusChanged(Z)V
.end method

.method public abstract setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V
.end method
