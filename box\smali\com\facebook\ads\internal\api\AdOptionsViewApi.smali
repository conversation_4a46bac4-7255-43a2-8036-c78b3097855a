.class public interface abstract Lcom/facebook/ads/internal/api/AdOptionsViewApi;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/facebook/ads/internal/api/AdComponentViewApiProvider;


# annotations
.annotation build Landroidx/annotation/Keep;
.end annotation


# virtual methods
.method public abstract setIconColor(I)V
.end method

.method public abstract setIconSizeDp(I)V
.end method

.method public abstract setSingleIcon(Z)V
.end method
