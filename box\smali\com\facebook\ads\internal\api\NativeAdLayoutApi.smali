.class public interface abstract Lcom/facebook/ads/internal/api/NativeAdLayoutApi;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/facebook/ads/internal/api/AdComponentViewApiProvider;


# annotations
.annotation build Landroidx/annotation/Keep;
.end annotation


# virtual methods
.method public abstract initialize(Lcom/facebook/ads/NativeAdLayout;)V
.end method

.method public abstract setMaxWidth(I)V
.end method

.method public abstract setMinWidth(I)V
.end method
