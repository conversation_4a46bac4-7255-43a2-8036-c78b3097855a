.class public Lcom/google/android/datatransport/cct/CctBackendFactory;
.super Ljava/lang/Object;

# interfaces
.implements Ly8/c;


# annotations
.annotation build Landroidx/annotation/Keep;
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public create(Ly8/f;)Ly8/k;
    .locals 3

    new-instance v0, Lw8/d;

    invoke-virtual {p1}, Ly8/f;->b()Landroid/content/Context;

    move-result-object v1

    invoke-virtual {p1}, Ly8/f;->e()Lh9/a;

    move-result-object v2

    invoke-virtual {p1}, Ly8/f;->d()Lh9/a;

    move-result-object p1

    invoke-direct {v0, v1, v2, p1}, Lw8/d;-><init>(Landroid/content/Context;Lh9/a;Lh9/a;)V

    return-object v0
.end method
