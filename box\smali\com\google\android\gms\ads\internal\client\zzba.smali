.class public final Lcom/google/android/gms/ads/internal/client/zzba;
.super Ljava/lang/Object;


# static fields
.field public static final d:Lcom/google/android/gms/ads/internal/client/zzba;


# instance fields
.field public final a:Lcom/google/android/gms/internal/ads/ws;

.field public final b:Lcom/google/android/gms/internal/ads/xs;

.field public final c:Lcom/google/android/gms/internal/ads/ct;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    new-instance v0, Lcom/google/android/gms/ads/internal/client/zzba;

    invoke-direct {v0}, Lcom/google/android/gms/ads/internal/client/zzba;-><init>()V

    sput-object v0, Lcom/google/android/gms/ads/internal/client/zzba;->d:Lcom/google/android/gms/ads/internal/client/zzba;

    return-void
.end method

.method public constructor <init>()V
    .locals 3

    new-instance v0, Lcom/google/android/gms/internal/ads/ws;

    invoke-direct {v0}, Lcom/google/android/gms/internal/ads/ws;-><init>()V

    new-instance v1, Lcom/google/android/gms/internal/ads/xs;

    invoke-direct {v1}, Lcom/google/android/gms/internal/ads/xs;-><init>()V

    new-instance v2, Lcom/google/android/gms/internal/ads/ct;

    invoke-direct {v2}, Lcom/google/android/gms/internal/ads/ct;-><init>()V

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object v0, p0, Lcom/google/android/gms/ads/internal/client/zzba;->a:Lcom/google/android/gms/internal/ads/ws;

    iput-object v1, p0, Lcom/google/android/gms/ads/internal/client/zzba;->b:Lcom/google/android/gms/internal/ads/xs;

    iput-object v2, p0, Lcom/google/android/gms/ads/internal/client/zzba;->c:Lcom/google/android/gms/internal/ads/ct;

    return-void
.end method

.method public static zza()Lcom/google/android/gms/internal/ads/ws;
    .locals 1

    sget-object v0, Lcom/google/android/gms/ads/internal/client/zzba;->d:Lcom/google/android/gms/ads/internal/client/zzba;

    iget-object v0, v0, Lcom/google/android/gms/ads/internal/client/zzba;->a:Lcom/google/android/gms/internal/ads/ws;

    return-object v0
.end method

.method public static zzb()Lcom/google/android/gms/internal/ads/xs;
    .locals 1

    sget-object v0, Lcom/google/android/gms/ads/internal/client/zzba;->d:Lcom/google/android/gms/ads/internal/client/zzba;

    iget-object v0, v0, Lcom/google/android/gms/ads/internal/client/zzba;->b:Lcom/google/android/gms/internal/ads/xs;

    return-object v0
.end method

.method public static zzc()Lcom/google/android/gms/internal/ads/ct;
    .locals 1

    sget-object v0, Lcom/google/android/gms/ads/internal/client/zzba;->d:Lcom/google/android/gms/ads/internal/client/zzba;

    iget-object v0, v0, Lcom/google/android/gms/ads/internal/client/zzba;->c:Lcom/google/android/gms/internal/ads/ct;

    return-object v0
.end method
