.class final Lcom/google/android/gms/internal/measurement/zzeb;
.super Lcom/google/android/gms/internal/measurement/zzdu;


# instance fields
.field final synthetic zza:Landroid/app/Activity;

.field final synthetic zzb:Lcom/google/android/gms/internal/measurement/r1;


# direct methods
.method public constructor <init>(Lcom/google/android/gms/internal/measurement/r1;Landroid/app/Activity;)V
    .locals 0

    iput-object p1, p0, Lcom/google/android/gms/internal/measurement/zzeb;->zzb:Lcom/google/android/gms/internal/measurement/r1;

    iput-object p2, p0, Lcom/google/android/gms/internal/measurement/zzeb;->zza:Landroid/app/Activity;

    iget-object p1, p1, Lcom/google/android/gms/internal/measurement/r1;->a:Lcom/google/android/gms/internal/measurement/s1;

    const/4 p2, 0x1

    invoke-direct {p0, p1, p2}, Lcom/google/android/gms/internal/measurement/zzdu;-><init>(Lcom/google/android/gms/internal/measurement/s1;Z)V

    return-void
.end method


# virtual methods
.method public final zza()V
    .locals 4
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroid/os/RemoteException;
        }
    .end annotation

    iget-object v0, p0, Lcom/google/android/gms/internal/measurement/zzeb;->zzb:Lcom/google/android/gms/internal/measurement/r1;

    iget-object v0, v0, Lcom/google/android/gms/internal/measurement/r1;->a:Lcom/google/android/gms/internal/measurement/s1;

    invoke-static {v0}, Lcom/google/android/gms/internal/measurement/s1;->y(Lcom/google/android/gms/internal/measurement/s1;)Lcom/google/android/gms/internal/measurement/d1;

    move-result-object v0

    invoke-static {v0}, Lpb/m;->l(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/google/android/gms/internal/measurement/d1;

    iget-object v1, p0, Lcom/google/android/gms/internal/measurement/zzeb;->zza:Landroid/app/Activity;

    invoke-static {v1}, Lbc/d;->P3(Ljava/lang/Object;)Lbc/b;

    move-result-object v1

    iget-wide v2, p0, Lcom/google/android/gms/internal/measurement/zzdu;->zzi:J

    invoke-interface {v0, v1, v2, v3}, Lcom/google/android/gms/internal/measurement/d1;->onActivityStopped(Lbc/b;J)V

    return-void
.end method
