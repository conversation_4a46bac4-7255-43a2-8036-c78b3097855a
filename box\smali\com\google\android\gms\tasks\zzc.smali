.class final Lcom/google/android/gms/tasks/zzc;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/lang/Runnable;


# instance fields
.field final synthetic zza:Lcom/google/android/gms/tasks/j;

.field final synthetic zzb:Lcom/google/android/gms/tasks/u;


# direct methods
.method public constructor <init>(Lcom/google/android/gms/tasks/u;Lcom/google/android/gms/tasks/j;)V
    .locals 0

    iput-object p1, p0, Lcom/google/android/gms/tasks/zzc;->zzb:Lcom/google/android/gms/tasks/u;

    iput-object p2, p0, Lcom/google/android/gms/tasks/zzc;->zza:Lcom/google/android/gms/tasks/j;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final run()V
    .locals 2

    iget-object v0, p0, Lcom/google/android/gms/tasks/zzc;->zza:Lcom/google/android/gms/tasks/j;

    invoke-virtual {v0}, Lcom/google/android/gms/tasks/j;->p()Z

    move-result v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/google/android/gms/tasks/zzc;->zzb:Lcom/google/android/gms/tasks/u;

    invoke-static {v0}, Lcom/google/android/gms/tasks/u;->c(Lcom/google/android/gms/tasks/u;)Lcom/google/android/gms/tasks/h0;

    move-result-object v0

    invoke-virtual {v0}, Lcom/google/android/gms/tasks/h0;->w()Z

    return-void

    :cond_0
    :try_start_0
    iget-object v0, p0, Lcom/google/android/gms/tasks/zzc;->zzb:Lcom/google/android/gms/tasks/u;

    invoke-static {v0}, Lcom/google/android/gms/tasks/u;->b(Lcom/google/android/gms/tasks/u;)Lcom/google/android/gms/tasks/c;

    move-result-object v0

    iget-object v1, p0, Lcom/google/android/gms/tasks/zzc;->zza:Lcom/google/android/gms/tasks/j;

    invoke-interface {v0, v1}, Lcom/google/android/gms/tasks/c;->then(Lcom/google/android/gms/tasks/j;)Ljava/lang/Object;

    move-result-object v0
    :try_end_0
    .catch Lcom/google/android/gms/tasks/RuntimeExecutionException; {:try_start_0 .. :try_end_0} :catch_1
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    iget-object v1, p0, Lcom/google/android/gms/tasks/zzc;->zzb:Lcom/google/android/gms/tasks/u;

    invoke-static {v1}, Lcom/google/android/gms/tasks/u;->c(Lcom/google/android/gms/tasks/u;)Lcom/google/android/gms/tasks/h0;

    move-result-object v1

    invoke-virtual {v1, v0}, Lcom/google/android/gms/tasks/h0;->v(Ljava/lang/Object;)V

    return-void

    :catch_0
    move-exception v0

    goto :goto_0

    :catch_1
    move-exception v0

    goto :goto_1

    :goto_0
    iget-object v1, p0, Lcom/google/android/gms/tasks/zzc;->zzb:Lcom/google/android/gms/tasks/u;

    invoke-static {v1}, Lcom/google/android/gms/tasks/u;->c(Lcom/google/android/gms/tasks/u;)Lcom/google/android/gms/tasks/h0;

    move-result-object v1

    invoke-virtual {v1, v0}, Lcom/google/android/gms/tasks/h0;->u(Ljava/lang/Exception;)V

    return-void

    :goto_1
    invoke-virtual {v0}, Ljava/lang/Throwable;->getCause()Ljava/lang/Throwable;

    move-result-object v1

    instance-of v1, v1, Ljava/lang/Exception;

    if-eqz v1, :cond_1

    iget-object v1, p0, Lcom/google/android/gms/tasks/zzc;->zzb:Lcom/google/android/gms/tasks/u;

    invoke-static {v1}, Lcom/google/android/gms/tasks/u;->c(Lcom/google/android/gms/tasks/u;)Lcom/google/android/gms/tasks/h0;

    move-result-object v1

    invoke-virtual {v0}, Ljava/lang/Throwable;->getCause()Ljava/lang/Throwable;

    move-result-object v0

    check-cast v0, Ljava/lang/Exception;

    invoke-virtual {v1, v0}, Lcom/google/android/gms/tasks/h0;->u(Ljava/lang/Exception;)V

    return-void

    :cond_1
    iget-object v1, p0, Lcom/google/android/gms/tasks/zzc;->zzb:Lcom/google/android/gms/tasks/u;

    invoke-static {v1}, Lcom/google/android/gms/tasks/u;->c(Lcom/google/android/gms/tasks/u;)Lcom/google/android/gms/tasks/h0;

    move-result-object v1

    invoke-virtual {v1, v0}, Lcom/google/android/gms/tasks/h0;->u(Ljava/lang/Exception;)V

    return-void
.end method
