.class final Lcom/google/android/play/core/appupdate/internal/zzq;
.super Lcom/google/android/play/core/appupdate/internal/zzn;


# instance fields
.field final synthetic zza:Lcom/google/android/gms/tasks/k;

.field final synthetic zzb:Lcom/google/android/play/core/appupdate/internal/zzn;

.field final synthetic zzc:Lcom/google/android/play/core/appupdate/internal/y;


# direct methods
.method public constructor <init>(Lcom/google/android/play/core/appupdate/internal/y;Lcom/google/android/gms/tasks/k;Lcom/google/android/gms/tasks/k;Lcom/google/android/play/core/appupdate/internal/zzn;)V
    .locals 0

    iput-object p1, p0, Lcom/google/android/play/core/appupdate/internal/zzq;->zzc:Lcom/google/android/play/core/appupdate/internal/y;

    iput-object p3, p0, Lcom/google/android/play/core/appupdate/internal/zzq;->zza:Lcom/google/android/gms/tasks/k;

    iput-object p4, p0, Lcom/google/android/play/core/appupdate/internal/zzq;->zzb:Lcom/google/android/play/core/appupdate/internal/zzn;

    invoke-direct {p0, p2}, Lcom/google/android/play/core/appupdate/internal/zzn;-><init>(Lcom/google/android/gms/tasks/k;)V

    return-void
.end method


# virtual methods
.method public final zza()V
    .locals 4

    iget-object v0, p0, Lcom/google/android/play/core/appupdate/internal/zzq;->zzc:Lcom/google/android/play/core/appupdate/internal/y;

    invoke-static {v0}, Lcom/google/android/play/core/appupdate/internal/y;->g(Lcom/google/android/play/core/appupdate/internal/y;)Ljava/lang/Object;

    move-result-object v0

    monitor-enter v0

    :try_start_0
    iget-object v1, p0, Lcom/google/android/play/core/appupdate/internal/zzq;->zzc:Lcom/google/android/play/core/appupdate/internal/y;

    iget-object v2, p0, Lcom/google/android/play/core/appupdate/internal/zzq;->zza:Lcom/google/android/gms/tasks/k;

    invoke-static {v1, v2}, Lcom/google/android/play/core/appupdate/internal/y;->n(Lcom/google/android/play/core/appupdate/internal/y;Lcom/google/android/gms/tasks/k;)V

    iget-object v1, p0, Lcom/google/android/play/core/appupdate/internal/zzq;->zzc:Lcom/google/android/play/core/appupdate/internal/y;

    invoke-static {v1}, Lcom/google/android/play/core/appupdate/internal/y;->i(Lcom/google/android/play/core/appupdate/internal/y;)Ljava/util/concurrent/atomic/AtomicInteger;

    move-result-object v1

    invoke-virtual {v1}, Ljava/util/concurrent/atomic/AtomicInteger;->getAndIncrement()I

    move-result v1

    if-lez v1, :cond_0

    iget-object v1, p0, Lcom/google/android/play/core/appupdate/internal/zzq;->zzc:Lcom/google/android/play/core/appupdate/internal/y;

    invoke-static {v1}, Lcom/google/android/play/core/appupdate/internal/y;->f(Lcom/google/android/play/core/appupdate/internal/y;)Lcom/google/android/play/core/appupdate/internal/s;

    move-result-object v1

    const-string v2, "Already connected to the service."

    const/4 v3, 0x0

    new-array v3, v3, [Ljava/lang/Object;

    invoke-virtual {v1, v2, v3}, Lcom/google/android/play/core/appupdate/internal/s;->d(Ljava/lang/String;[Ljava/lang/Object;)I

    goto :goto_0

    :catchall_0
    move-exception v1

    goto :goto_1

    :cond_0
    :goto_0
    iget-object v1, p0, Lcom/google/android/play/core/appupdate/internal/zzq;->zzc:Lcom/google/android/play/core/appupdate/internal/y;

    iget-object v2, p0, Lcom/google/android/play/core/appupdate/internal/zzq;->zzb:Lcom/google/android/play/core/appupdate/internal/zzn;

    invoke-static {v1, v2}, Lcom/google/android/play/core/appupdate/internal/y;->p(Lcom/google/android/play/core/appupdate/internal/y;Lcom/google/android/play/core/appupdate/internal/zzn;)V

    monitor-exit v0

    return-void

    :goto_1
    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    throw v1
.end method
