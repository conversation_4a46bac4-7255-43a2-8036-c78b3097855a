.class final Lcom/google/android/play/core/appupdate/internal/zzr;
.super Lcom/google/android/play/core/appupdate/internal/zzn;


# instance fields
.field final synthetic zza:Lcom/google/android/play/core/appupdate/internal/y;


# direct methods
.method public constructor <init>(Lcom/google/android/play/core/appupdate/internal/y;)V
    .locals 0

    iput-object p1, p0, Lcom/google/android/play/core/appupdate/internal/zzr;->zza:Lcom/google/android/play/core/appupdate/internal/y;

    invoke-direct {p0}, Lcom/google/android/play/core/appupdate/internal/zzn;-><init>()V

    return-void
.end method


# virtual methods
.method public final zza()V
    .locals 5

    iget-object v0, p0, Lcom/google/android/play/core/appupdate/internal/zzr;->zza:Lcom/google/android/play/core/appupdate/internal/y;

    invoke-static {v0}, Lcom/google/android/play/core/appupdate/internal/y;->g(Lcom/google/android/play/core/appupdate/internal/y;)Ljava/lang/Object;

    move-result-object v0

    monitor-enter v0

    :try_start_0
    iget-object v1, p0, Lcom/google/android/play/core/appupdate/internal/zzr;->zza:Lcom/google/android/play/core/appupdate/internal/y;

    invoke-static {v1}, Lcom/google/android/play/core/appupdate/internal/y;->i(Lcom/google/android/play/core/appupdate/internal/y;)Ljava/util/concurrent/atomic/AtomicInteger;

    move-result-object v1

    invoke-virtual {v1}, Ljava/util/concurrent/atomic/AtomicInteger;->get()I

    move-result v1

    const/4 v2, 0x0

    if-lez v1, :cond_1

    iget-object v1, p0, Lcom/google/android/play/core/appupdate/internal/zzr;->zza:Lcom/google/android/play/core/appupdate/internal/y;

    invoke-static {v1}, Lcom/google/android/play/core/appupdate/internal/y;->i(Lcom/google/android/play/core/appupdate/internal/y;)Ljava/util/concurrent/atomic/AtomicInteger;

    move-result-object v1

    invoke-virtual {v1}, Ljava/util/concurrent/atomic/AtomicInteger;->decrementAndGet()I

    move-result v1

    if-gtz v1, :cond_0

    goto :goto_0

    :cond_0
    iget-object v1, p0, Lcom/google/android/play/core/appupdate/internal/zzr;->zza:Lcom/google/android/play/core/appupdate/internal/y;

    invoke-static {v1}, Lcom/google/android/play/core/appupdate/internal/y;->f(Lcom/google/android/play/core/appupdate/internal/y;)Lcom/google/android/play/core/appupdate/internal/s;

    move-result-object v1

    const-string v3, "Leaving the connection open for other ongoing calls."

    new-array v2, v2, [Ljava/lang/Object;

    invoke-virtual {v1, v3, v2}, Lcom/google/android/play/core/appupdate/internal/s;->d(Ljava/lang/String;[Ljava/lang/Object;)I

    monitor-exit v0

    return-void

    :catchall_0
    move-exception v1

    goto :goto_1

    :cond_1
    :goto_0
    iget-object v1, p0, Lcom/google/android/play/core/appupdate/internal/zzr;->zza:Lcom/google/android/play/core/appupdate/internal/y;

    invoke-static {v1}, Lcom/google/android/play/core/appupdate/internal/y;->d(Lcom/google/android/play/core/appupdate/internal/y;)Landroid/os/IInterface;

    move-result-object v3

    if-eqz v3, :cond_2

    invoke-static {v1}, Lcom/google/android/play/core/appupdate/internal/y;->f(Lcom/google/android/play/core/appupdate/internal/y;)Lcom/google/android/play/core/appupdate/internal/s;

    move-result-object v1

    const-string v3, "Unbind from service."

    new-array v4, v2, [Ljava/lang/Object;

    invoke-virtual {v1, v3, v4}, Lcom/google/android/play/core/appupdate/internal/s;->d(Ljava/lang/String;[Ljava/lang/Object;)I

    iget-object v1, p0, Lcom/google/android/play/core/appupdate/internal/zzr;->zza:Lcom/google/android/play/core/appupdate/internal/y;

    invoke-static {v1}, Lcom/google/android/play/core/appupdate/internal/y;->a(Lcom/google/android/play/core/appupdate/internal/y;)Landroid/content/Context;

    move-result-object v3

    invoke-static {v1}, Lcom/google/android/play/core/appupdate/internal/y;->b(Lcom/google/android/play/core/appupdate/internal/y;)Landroid/content/ServiceConnection;

    move-result-object v1

    invoke-virtual {v3, v1}, Landroid/content/Context;->unbindService(Landroid/content/ServiceConnection;)V

    iget-object v1, p0, Lcom/google/android/play/core/appupdate/internal/zzr;->zza:Lcom/google/android/play/core/appupdate/internal/y;

    invoke-static {v1, v2}, Lcom/google/android/play/core/appupdate/internal/y;->l(Lcom/google/android/play/core/appupdate/internal/y;Z)V

    iget-object v1, p0, Lcom/google/android/play/core/appupdate/internal/zzr;->zza:Lcom/google/android/play/core/appupdate/internal/y;

    const/4 v2, 0x0

    invoke-static {v1, v2}, Lcom/google/android/play/core/appupdate/internal/y;->m(Lcom/google/android/play/core/appupdate/internal/y;Landroid/os/IInterface;)V

    iget-object v1, p0, Lcom/google/android/play/core/appupdate/internal/zzr;->zza:Lcom/google/android/play/core/appupdate/internal/y;

    invoke-static {v1, v2}, Lcom/google/android/play/core/appupdate/internal/y;->k(Lcom/google/android/play/core/appupdate/internal/y;Landroid/content/ServiceConnection;)V

    :cond_2
    iget-object v1, p0, Lcom/google/android/play/core/appupdate/internal/zzr;->zza:Lcom/google/android/play/core/appupdate/internal/y;

    invoke-static {v1}, Lcom/google/android/play/core/appupdate/internal/y;->o(Lcom/google/android/play/core/appupdate/internal/y;)V

    monitor-exit v0

    return-void

    :goto_1
    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    throw v1
.end method
