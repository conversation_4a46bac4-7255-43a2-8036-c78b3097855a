.class final Lcom/google/android/play/core/appupdate/internal/zzt;
.super Lcom/google/android/play/core/appupdate/internal/zzn;


# instance fields
.field final synthetic zza:Landroid/os/IBinder;

.field final synthetic zzb:Lcom/google/android/play/core/appupdate/internal/x;


# direct methods
.method public constructor <init>(Lcom/google/android/play/core/appupdate/internal/x;Landroid/os/IBinder;)V
    .locals 0

    iput-object p1, p0, Lcom/google/android/play/core/appupdate/internal/zzt;->zzb:Lcom/google/android/play/core/appupdate/internal/x;

    iput-object p2, p0, Lcom/google/android/play/core/appupdate/internal/zzt;->zza:Landroid/os/IBinder;

    invoke-direct {p0}, Lcom/google/android/play/core/appupdate/internal/zzn;-><init>()V

    return-void
.end method


# virtual methods
.method public final zza()V
    .locals 2

    iget-object v0, p0, Lcom/google/android/play/core/appupdate/internal/zzt;->zzb:Lcom/google/android/play/core/appupdate/internal/x;

    iget-object v0, v0, Lcom/google/android/play/core/appupdate/internal/x;->a:Lcom/google/android/play/core/appupdate/internal/y;

    iget-object v1, p0, Lcom/google/android/play/core/appupdate/internal/zzt;->zza:Landroid/os/IBinder;

    invoke-static {v1}, Lcom/google/android/play/core/appupdate/internal/k;->D(Landroid/os/IBinder;)Lcom/google/android/play/core/appupdate/internal/l;

    move-result-object v1

    invoke-static {v0, v1}, Lcom/google/android/play/core/appupdate/internal/y;->m(Lcom/google/android/play/core/appupdate/internal/y;Landroid/os/IInterface;)V

    iget-object v0, p0, Lcom/google/android/play/core/appupdate/internal/zzt;->zzb:Lcom/google/android/play/core/appupdate/internal/x;

    iget-object v0, v0, Lcom/google/android/play/core/appupdate/internal/x;->a:Lcom/google/android/play/core/appupdate/internal/y;

    invoke-static {v0}, Lcom/google/android/play/core/appupdate/internal/y;->q(Lcom/google/android/play/core/appupdate/internal/y;)V

    iget-object v0, p0, Lcom/google/android/play/core/appupdate/internal/zzt;->zzb:Lcom/google/android/play/core/appupdate/internal/x;

    iget-object v0, v0, Lcom/google/android/play/core/appupdate/internal/x;->a:Lcom/google/android/play/core/appupdate/internal/y;

    const/4 v1, 0x0

    invoke-static {v0, v1}, Lcom/google/android/play/core/appupdate/internal/y;->l(Lcom/google/android/play/core/appupdate/internal/y;Z)V

    iget-object v0, p0, Lcom/google/android/play/core/appupdate/internal/zzt;->zzb:Lcom/google/android/play/core/appupdate/internal/x;

    iget-object v0, v0, Lcom/google/android/play/core/appupdate/internal/x;->a:Lcom/google/android/play/core/appupdate/internal/y;

    invoke-static {v0}, Lcom/google/android/play/core/appupdate/internal/y;->h(Lcom/google/android/play/core/appupdate/internal/y;)Ljava/util/List;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_0

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/lang/Runnable;

    invoke-interface {v1}, Ljava/lang/Runnable;->run()V

    goto :goto_0

    :cond_0
    iget-object v0, p0, Lcom/google/android/play/core/appupdate/internal/zzt;->zzb:Lcom/google/android/play/core/appupdate/internal/x;

    iget-object v0, v0, Lcom/google/android/play/core/appupdate/internal/x;->a:Lcom/google/android/play/core/appupdate/internal/y;

    invoke-static {v0}, Lcom/google/android/play/core/appupdate/internal/y;->h(Lcom/google/android/play/core/appupdate/internal/y;)Ljava/util/List;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/List;->clear()V

    return-void
.end method
