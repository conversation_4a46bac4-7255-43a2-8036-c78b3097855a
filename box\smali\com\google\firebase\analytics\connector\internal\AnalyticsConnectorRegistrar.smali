.class public Lcom/google/firebase/analytics/connector/internal/AnalyticsConnectorRegistrar;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/google/firebase/components/ComponentRegistrar;


# annotations
.annotation build Landroidx/annotation/Keep;
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static synthetic lambda$getComponents$0(Lge/e;)Lce/a;
    .locals 3

    const-class v0, Lyd/e;

    invoke-interface {p0, v0}, Lge/e;->a(Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lyd/e;

    const-class v1, Landroid/content/Context;

    invoke-interface {p0, v1}, Lge/e;->a(Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Landroid/content/Context;

    const-class v2, Lnf/d;

    invoke-interface {p0, v2}, Lge/e;->a(Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p0

    check-cast p0, Lnf/d;

    invoke-static {v0, v1, p0}, Lce/b;->h(Lyd/e;Landroid/content/Context;Lnf/d;)Lce/a;

    move-result-object p0

    return-object p0
.end method


# virtual methods
.method public getComponents()Ljava/util/List;
    .locals 3
    .annotation build Landroid/annotation/SuppressLint;
        value = {
            "MissingPermission"
        }
    .end annotation

    .annotation build Landroidx/annotation/Keep;
    .end annotation

    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lge/c<",
            "*>;>;"
        }
    .end annotation

    const/4 v0, 0x2

    new-array v0, v0, [Lge/c;

    const-class v1, Lce/a;

    invoke-static {v1}, Lge/c;->e(Ljava/lang/Class;)Lge/c$b;

    move-result-object v1

    const-class v2, Lyd/e;

    invoke-static {v2}, Lge/r;->k(Ljava/lang/Class;)Lge/r;

    move-result-object v2

    invoke-virtual {v1, v2}, Lge/c$b;->b(Lge/r;)Lge/c$b;

    move-result-object v1

    const-class v2, Landroid/content/Context;

    invoke-static {v2}, Lge/r;->k(Ljava/lang/Class;)Lge/r;

    move-result-object v2

    invoke-virtual {v1, v2}, Lge/c$b;->b(Lge/r;)Lge/c$b;

    move-result-object v1

    const-class v2, Lnf/d;

    invoke-static {v2}, Lge/r;->k(Ljava/lang/Class;)Lge/r;

    move-result-object v2

    invoke-virtual {v1, v2}, Lge/c$b;->b(Lge/r;)Lge/c$b;

    move-result-object v1

    sget-object v2, Lde/a;->a:Lde/a;

    invoke-virtual {v1, v2}, Lge/c$b;->f(Lge/h;)Lge/c$b;

    move-result-object v1

    invoke-virtual {v1}, Lge/c$b;->e()Lge/c$b;

    move-result-object v1

    invoke-virtual {v1}, Lge/c$b;->d()Lge/c;

    move-result-object v1

    const/4 v2, 0x0

    aput-object v1, v0, v2

    const-string v1, "fire-analytics"

    const-string v2, "21.2.0"

    invoke-static {v1, v2}, Lng/h;->b(Ljava/lang/String;Ljava/lang/String;)Lge/c;

    move-result-object v1

    const/4 v2, 0x1

    aput-object v1, v0, v2

    invoke-static {v0}, Ljava/util/Arrays;->asList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v0

    return-object v0
.end method
