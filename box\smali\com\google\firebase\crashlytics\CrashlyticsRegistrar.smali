.class public Lcom/google/firebase/crashlytics/CrashlyticsRegistrar;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/google/firebase/components/ComponentRegistrar;


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static synthetic a(Lcom/google/firebase/crashlytics/CrashlyticsRegistrar;Lge/e;)Lcom/google/firebase/crashlytics/FirebaseCrashlytics;
    .locals 0

    invoke-virtual {p0, p1}, Lcom/google/firebase/crashlytics/CrashlyticsRegistrar;->b(Lge/e;)Lcom/google/firebase/crashlytics/FirebaseCrashlytics;

    move-result-object p0

    return-object p0
.end method


# virtual methods
.method public final b(Lge/e;)Lcom/google/firebase/crashlytics/FirebaseCrashlytics;
    .locals 4

    const-class v0, Lyd/e;

    invoke-interface {p1, v0}, Lge/e;->a(<PERSON>java/lang/Class;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lyd/e;

    const-class v1, Lve/a;

    invoke-interface {p1, v1}, Lge/e;->i(Ljava/lang/Class;)Lqf/a;

    move-result-object v1

    const-class v2, Lce/a;

    invoke-interface {p1, v2}, Lge/e;->i(Ljava/lang/Class;)Lqf/a;

    move-result-object v2

    const-class v3, Lrf/g;

    invoke-interface {p1, v3}, Lge/e;->a(Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lrf/g;

    invoke-static {v0, p1, v1, v2}, Lcom/google/firebase/crashlytics/FirebaseCrashlytics;->a(Lyd/e;Lrf/g;Lqf/a;Lqf/a;)Lcom/google/firebase/crashlytics/FirebaseCrashlytics;

    move-result-object p1

    return-object p1
.end method

.method public getComponents()Ljava/util/List;
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lge/c<",
            "*>;>;"
        }
    .end annotation

    const/4 v0, 0x2

    new-array v0, v0, [Lge/c;

    const-class v1, Lcom/google/firebase/crashlytics/FirebaseCrashlytics;

    invoke-static {v1}, Lge/c;->e(Ljava/lang/Class;)Lge/c$b;

    move-result-object v1

    const-string v2, "fire-cls"

    invoke-virtual {v1, v2}, Lge/c$b;->h(Ljava/lang/String;)Lge/c$b;

    move-result-object v1

    const-class v3, Lyd/e;

    invoke-static {v3}, Lge/r;->k(Ljava/lang/Class;)Lge/r;

    move-result-object v3

    invoke-virtual {v1, v3}, Lge/c$b;->b(Lge/r;)Lge/c$b;

    move-result-object v1

    const-class v3, Lrf/g;

    invoke-static {v3}, Lge/r;->k(Ljava/lang/Class;)Lge/r;

    move-result-object v3

    invoke-virtual {v1, v3}, Lge/c$b;->b(Lge/r;)Lge/c$b;

    move-result-object v1

    const-class v3, Lve/a;

    invoke-static {v3}, Lge/r;->a(Ljava/lang/Class;)Lge/r;

    move-result-object v3

    invoke-virtual {v1, v3}, Lge/c$b;->b(Lge/r;)Lge/c$b;

    move-result-object v1

    const-class v3, Lce/a;

    invoke-static {v3}, Lge/r;->a(Ljava/lang/Class;)Lge/r;

    move-result-object v3

    invoke-virtual {v1, v3}, Lge/c$b;->b(Lge/r;)Lge/c$b;

    move-result-object v1

    new-instance v3, Lhe/f;

    invoke-direct {v3, p0}, Lhe/f;-><init>(Lcom/google/firebase/crashlytics/CrashlyticsRegistrar;)V

    invoke-virtual {v1, v3}, Lge/c$b;->f(Lge/h;)Lge/c$b;

    move-result-object v1

    invoke-virtual {v1}, Lge/c$b;->e()Lge/c$b;

    move-result-object v1

    invoke-virtual {v1}, Lge/c$b;->d()Lge/c;

    move-result-object v1

    const/4 v3, 0x0

    aput-object v1, v0, v3

    const-string v1, "18.3.2"

    invoke-static {v2, v1}, Lng/h;->b(Ljava/lang/String;Ljava/lang/String;)Lge/c;

    move-result-object v1

    const/4 v2, 0x1

    aput-object v1, v0, v2

    invoke-static {v0}, Ljava/util/Arrays;->asList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v0

    return-object v0
.end method
