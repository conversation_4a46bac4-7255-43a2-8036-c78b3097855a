.class public final Lcom/google/firebase/iid/Registrar;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/google/firebase/components/ComponentRegistrar;


# annotations
.annotation build Landroidx/annotation/Keep;
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/google/firebase/iid/Registrar$a;
    }
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static final synthetic lambda$getComponents$0$Registrar(Lge/e;)Lcom/google/firebase/iid/FirebaseInstanceId;
    .locals 5

    new-instance v0, Lcom/google/firebase/iid/FirebaseInstanceId;

    const-class v1, Lyd/e;

    invoke-interface {p0, v1}, Lge/e;->a(Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lyd/e;

    const-class v2, Lng/i;

    invoke-interface {p0, v2}, Lge/e;->g(Ljava/lang/Class;)Lqf/b;

    move-result-object v2

    const-class v3, Lcom/google/firebase/heartbeatinfo/HeartBeatInfo;

    invoke-interface {p0, v3}, Lge/e;->g(Ljava/lang/Class;)Lqf/b;

    move-result-object v3

    const-class v4, Lrf/g;

    invoke-interface {p0, v4}, Lge/e;->a(Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p0

    check-cast p0, Lrf/g;

    invoke-direct {v0, v1, v2, v3, p0}, Lcom/google/firebase/iid/FirebaseInstanceId;-><init>(Lyd/e;Lqf/b;Lqf/b;Lrf/g;)V

    return-object v0
.end method

.method public static final synthetic lambda$getComponents$1$Registrar(Lge/e;)Lpf/a;
    .locals 2

    new-instance v0, Lcom/google/firebase/iid/Registrar$a;

    const-class v1, Lcom/google/firebase/iid/FirebaseInstanceId;

    invoke-interface {p0, v1}, Lge/e;->a(Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p0

    check-cast p0, Lcom/google/firebase/iid/FirebaseInstanceId;

    invoke-direct {v0, p0}, Lcom/google/firebase/iid/Registrar$a;-><init>(Lcom/google/firebase/iid/FirebaseInstanceId;)V

    return-object v0
.end method


# virtual methods
.method public getComponents()Ljava/util/List;
    .locals 5
    .annotation build Landroidx/annotation/Keep;
    .end annotation

    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lge/c<",
            "*>;>;"
        }
    .end annotation

    const-class v0, Lcom/google/firebase/iid/FirebaseInstanceId;

    invoke-static {v0}, Lge/c;->e(Ljava/lang/Class;)Lge/c$b;

    move-result-object v1

    const-class v2, Lyd/e;

    invoke-static {v2}, Lge/r;->k(Ljava/lang/Class;)Lge/r;

    move-result-object v2

    invoke-virtual {v1, v2}, Lge/c$b;->b(Lge/r;)Lge/c$b;

    move-result-object v1

    const-class v2, Lng/i;

    invoke-static {v2}, Lge/r;->i(Ljava/lang/Class;)Lge/r;

    move-result-object v2

    invoke-virtual {v1, v2}, Lge/c$b;->b(Lge/r;)Lge/c$b;

    move-result-object v1

    const-class v2, Lcom/google/firebase/heartbeatinfo/HeartBeatInfo;

    invoke-static {v2}, Lge/r;->i(Ljava/lang/Class;)Lge/r;

    move-result-object v2

    invoke-virtual {v1, v2}, Lge/c$b;->b(Lge/r;)Lge/c$b;

    move-result-object v1

    const-class v2, Lrf/g;

    invoke-static {v2}, Lge/r;->k(Ljava/lang/Class;)Lge/r;

    move-result-object v2

    invoke-virtual {v1, v2}, Lge/c$b;->b(Lge/r;)Lge/c$b;

    move-result-object v1

    sget-object v2, Lcom/google/firebase/iid/o;->a:Lge/h;

    invoke-virtual {v1, v2}, Lge/c$b;->f(Lge/h;)Lge/c$b;

    move-result-object v1

    invoke-virtual {v1}, Lge/c$b;->c()Lge/c$b;

    move-result-object v1

    invoke-virtual {v1}, Lge/c$b;->d()Lge/c;

    move-result-object v1

    const-class v2, Lpf/a;

    invoke-static {v2}, Lge/c;->e(Ljava/lang/Class;)Lge/c$b;

    move-result-object v2

    invoke-static {v0}, Lge/r;->k(Ljava/lang/Class;)Lge/r;

    move-result-object v0

    invoke-virtual {v2, v0}, Lge/c$b;->b(Lge/r;)Lge/c$b;

    move-result-object v0

    sget-object v2, Lcom/google/firebase/iid/p;->a:Lge/h;

    invoke-virtual {v0, v2}, Lge/c$b;->f(Lge/h;)Lge/c$b;

    move-result-object v0

    invoke-virtual {v0}, Lge/c$b;->d()Lge/c;

    move-result-object v0

    const-string v2, "fire-iid"

    const-string v3, "21.1.0"

    invoke-static {v2, v3}, Lng/h;->b(Ljava/lang/String;Ljava/lang/String;)Lge/c;

    move-result-object v2

    const/4 v3, 0x3

    new-array v3, v3, [Lge/c;

    const/4 v4, 0x0

    aput-object v1, v3, v4

    const/4 v1, 0x1

    aput-object v0, v3, v1

    const/4 v0, 0x2

    aput-object v2, v3, v0

    invoke-static {v3}, Ljava/util/Arrays;->asList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v0

    return-object v0
.end method
