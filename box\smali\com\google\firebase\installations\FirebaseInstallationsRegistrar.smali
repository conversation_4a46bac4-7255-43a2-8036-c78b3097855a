.class public Lcom/google/firebase/installations/FirebaseInstallationsRegistrar;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/google/firebase/components/ComponentRegistrar;


# annotations
.annotation build Landroidx/annotation/Keep;
.end annotation


# static fields
.field private static final LIBRARY_NAME:Ljava/lang/String; = "fire-installations"


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static synthetic a(Lge/e;)Lrf/g;
    .locals 0

    invoke-static {p0}, Lcom/google/firebase/installations/FirebaseInstallationsRegistrar;->lambda$getComponents$0(Lge/e;)Lrf/g;

    move-result-object p0

    return-object p0
.end method

.method private static synthetic lambda$getComponents$0(Lge/e;)Lrf/g;
    .locals 6

    new-instance v0, Lcom/google/firebase/installations/a;

    const-class v1, Lyd/e;

    invoke-interface {p0, v1}, Lge/e;->a(Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lyd/e;

    const-class v2, Lof/h;

    invoke-interface {p0, v2}, Lge/e;->g(Ljava/lang/Class;)Lqf/b;

    move-result-object v2

    const-class v3, Lfe/a;

    const-class v4, Ljava/util/concurrent/ExecutorService;

    invoke-static {v3, v4}, Lge/b0;->a(Ljava/lang/Class;Ljava/lang/Class;)Lge/b0;

    move-result-object v3

    invoke-interface {p0, v3}, Lge/e;->f(Lge/b0;)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Ljava/util/concurrent/ExecutorService;

    const-class v4, Lfe/b;

    const-class v5, Ljava/util/concurrent/Executor;

    invoke-static {v4, v5}, Lge/b0;->a(Ljava/lang/Class;Ljava/lang/Class;)Lge/b0;

    move-result-object v4

    invoke-interface {p0, v4}, Lge/e;->f(Lge/b0;)Ljava/lang/Object;

    move-result-object p0

    check-cast p0, Ljava/util/concurrent/Executor;

    invoke-static {p0}, Lcom/google/firebase/concurrent/FirebaseExecutors;->b(Ljava/util/concurrent/Executor;)Ljava/util/concurrent/Executor;

    move-result-object p0

    invoke-direct {v0, v1, v2, v3, p0}, Lcom/google/firebase/installations/a;-><init>(Lyd/e;Lqf/b;Ljava/util/concurrent/ExecutorService;Ljava/util/concurrent/Executor;)V

    return-object v0
.end method


# virtual methods
.method public getComponents()Ljava/util/List;
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lge/c<",
            "*>;>;"
        }
    .end annotation

    const/4 v0, 0x3

    new-array v0, v0, [Lge/c;

    const-class v1, Lrf/g;

    invoke-static {v1}, Lge/c;->e(Ljava/lang/Class;)Lge/c$b;

    move-result-object v1

    const-string v2, "fire-installations"

    invoke-virtual {v1, v2}, Lge/c$b;->h(Ljava/lang/String;)Lge/c$b;

    move-result-object v1

    const-class v3, Lyd/e;

    invoke-static {v3}, Lge/r;->k(Ljava/lang/Class;)Lge/r;

    move-result-object v3

    invoke-virtual {v1, v3}, Lge/c$b;->b(Lge/r;)Lge/c$b;

    move-result-object v1

    const-class v3, Lof/h;

    invoke-static {v3}, Lge/r;->i(Ljava/lang/Class;)Lge/r;

    move-result-object v3

    invoke-virtual {v1, v3}, Lge/c$b;->b(Lge/r;)Lge/c$b;

    move-result-object v1

    const-class v3, Lfe/a;

    const-class v4, Ljava/util/concurrent/ExecutorService;

    invoke-static {v3, v4}, Lge/b0;->a(Ljava/lang/Class;Ljava/lang/Class;)Lge/b0;

    move-result-object v3

    invoke-static {v3}, Lge/r;->j(Lge/b0;)Lge/r;

    move-result-object v3

    invoke-virtual {v1, v3}, Lge/c$b;->b(Lge/r;)Lge/c$b;

    move-result-object v1

    const-class v3, Lfe/b;

    const-class v4, Ljava/util/concurrent/Executor;

    invoke-static {v3, v4}, Lge/b0;->a(Ljava/lang/Class;Ljava/lang/Class;)Lge/b0;

    move-result-object v3

    invoke-static {v3}, Lge/r;->j(Lge/b0;)Lge/r;

    move-result-object v3

    invoke-virtual {v1, v3}, Lge/c$b;->b(Lge/r;)Lge/c$b;

    move-result-object v1

    new-instance v3, Lrf/h;

    invoke-direct {v3}, Lrf/h;-><init>()V

    invoke-virtual {v1, v3}, Lge/c$b;->f(Lge/h;)Lge/c$b;

    move-result-object v1

    invoke-virtual {v1}, Lge/c$b;->d()Lge/c;

    move-result-object v1

    const/4 v3, 0x0

    aput-object v1, v0, v3

    const/4 v1, 0x1

    invoke-static {}, Lof/g;->a()Lge/c;

    move-result-object v3

    aput-object v3, v0, v1

    const-string v1, "17.2.0"

    invoke-static {v2, v1}, Lng/h;->b(Ljava/lang/String;Ljava/lang/String;)Lge/c;

    move-result-object v1

    const/4 v2, 0x2

    aput-object v1, v0, v2

    invoke-static {v0}, Ljava/util/Arrays;->asList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v0

    return-object v0
.end method
