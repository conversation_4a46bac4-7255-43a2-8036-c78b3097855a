.class public Lcom/google/firebase/messaging/FirebaseMessagingRegistrar;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/google/firebase/components/ComponentRegistrar;


# annotations
.annotation build Landroidx/annotation/Keep;
.end annotation


# static fields
.field private static final LIBRARY_NAME:Ljava/lang/String; = "fire-fcm"


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static synthetic a(Lge/e;)Lcom/google/firebase/messaging/FirebaseMessaging;
    .locals 0

    invoke-static {p0}, Lcom/google/firebase/messaging/FirebaseMessagingRegistrar;->lambda$getComponents$0(Lge/e;)Lcom/google/firebase/messaging/FirebaseMessaging;

    move-result-object p0

    return-object p0
.end method

.method private static synthetic lambda$getComponents$0(Lge/e;)Lcom/google/firebase/messaging/FirebaseMessaging;
    .locals 9

    new-instance v8, Lcom/google/firebase/messaging/FirebaseMessaging;

    const-class v0, Lyd/e;

    invoke-interface {p0, v0}, Lge/e;->a(Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v0

    move-object v1, v0

    check-cast v1, Lyd/e;

    const-class v0, Lpf/a;

    invoke-interface {p0, v0}, Lge/e;->a(Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v0

    move-object v2, v0

    check-cast v2, Lpf/a;

    const-class v0, Lng/i;

    invoke-interface {p0, v0}, Lge/e;->g(Ljava/lang/Class;)Lqf/b;

    move-result-object v3

    const-class v0, Lcom/google/firebase/heartbeatinfo/HeartBeatInfo;

    invoke-interface {p0, v0}, Lge/e;->g(Ljava/lang/Class;)Lqf/b;

    move-result-object v4

    const-class v0, Lrf/g;

    invoke-interface {p0, v0}, Lge/e;->a(Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v0

    move-object v5, v0

    check-cast v5, Lrf/g;

    const-class v0, Lv8/h;

    invoke-interface {p0, v0}, Lge/e;->a(Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v0

    move-object v6, v0

    check-cast v6, Lv8/h;

    const-class v0, Lnf/d;

    invoke-interface {p0, v0}, Lge/e;->a(Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p0

    move-object v7, p0

    check-cast v7, Lnf/d;

    move-object v0, v8

    invoke-direct/range {v0 .. v7}, Lcom/google/firebase/messaging/FirebaseMessaging;-><init>(Lyd/e;Lpf/a;Lqf/b;Lqf/b;Lrf/g;Lv8/h;Lnf/d;)V

    return-object v8
.end method


# virtual methods
.method public getComponents()Ljava/util/List;
    .locals 4
    .annotation build Landroidx/annotation/Keep;
    .end annotation

    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lge/c<",
            "*>;>;"
        }
    .end annotation

    const/4 v0, 0x2

    new-array v0, v0, [Lge/c;

    const-class v1, Lcom/google/firebase/messaging/FirebaseMessaging;

    invoke-static {v1}, Lge/c;->e(Ljava/lang/Class;)Lge/c$b;

    move-result-object v1

    const-string v2, "fire-fcm"

    invoke-virtual {v1, v2}, Lge/c$b;->h(Ljava/lang/String;)Lge/c$b;

    move-result-object v1

    const-class v3, Lyd/e;

    invoke-static {v3}, Lge/r;->k(Ljava/lang/Class;)Lge/r;

    move-result-object v3

    invoke-virtual {v1, v3}, Lge/c$b;->b(Lge/r;)Lge/c$b;

    move-result-object v1

    const-class v3, Lpf/a;

    invoke-static {v3}, Lge/r;->h(Ljava/lang/Class;)Lge/r;

    move-result-object v3

    invoke-virtual {v1, v3}, Lge/c$b;->b(Lge/r;)Lge/c$b;

    move-result-object v1

    const-class v3, Lng/i;

    invoke-static {v3}, Lge/r;->i(Ljava/lang/Class;)Lge/r;

    move-result-object v3

    invoke-virtual {v1, v3}, Lge/c$b;->b(Lge/r;)Lge/c$b;

    move-result-object v1

    const-class v3, Lcom/google/firebase/heartbeatinfo/HeartBeatInfo;

    invoke-static {v3}, Lge/r;->i(Ljava/lang/Class;)Lge/r;

    move-result-object v3

    invoke-virtual {v1, v3}, Lge/c$b;->b(Lge/r;)Lge/c$b;

    move-result-object v1

    const-class v3, Lv8/h;

    invoke-static {v3}, Lge/r;->h(Ljava/lang/Class;)Lge/r;

    move-result-object v3

    invoke-virtual {v1, v3}, Lge/c$b;->b(Lge/r;)Lge/c$b;

    move-result-object v1

    const-class v3, Lrf/g;

    invoke-static {v3}, Lge/r;->k(Ljava/lang/Class;)Lge/r;

    move-result-object v3

    invoke-virtual {v1, v3}, Lge/c$b;->b(Lge/r;)Lge/c$b;

    move-result-object v1

    const-class v3, Lnf/d;

    invoke-static {v3}, Lge/r;->k(Ljava/lang/Class;)Lge/r;

    move-result-object v3

    invoke-virtual {v1, v3}, Lge/c$b;->b(Lge/r;)Lge/c$b;

    move-result-object v1

    new-instance v3, Lcom/google/firebase/messaging/y;

    invoke-direct {v3}, Lcom/google/firebase/messaging/y;-><init>()V

    invoke-virtual {v1, v3}, Lge/c$b;->f(Lge/h;)Lge/c$b;

    move-result-object v1

    invoke-virtual {v1}, Lge/c$b;->c()Lge/c$b;

    move-result-object v1

    invoke-virtual {v1}, Lge/c$b;->d()Lge/c;

    move-result-object v1

    const/4 v3, 0x0

    aput-object v1, v0, v3

    const-string v1, "23.3.1"

    invoke-static {v2, v1}, Lng/h;->b(Ljava/lang/String;Ljava/lang/String;)Lge/c;

    move-result-object v1

    const/4 v2, 0x1

    aput-object v1, v0, v2

    invoke-static {v0}, Ljava/util/Arrays;->asList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v0

    return-object v0
.end method
