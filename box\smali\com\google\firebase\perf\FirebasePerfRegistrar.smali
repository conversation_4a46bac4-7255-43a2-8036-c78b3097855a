.class public Lcom/google/firebase/perf/FirebasePerfRegistrar;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/google/firebase/components/ComponentRegistrar;


# annotations
.annotation build Landroidx/annotation/Keep;
.end annotation


# static fields
.field private static final LIBRARY_NAME:Ljava/lang/String; = "fire-perf"


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static synthetic a(Lge/e;)Lag/c;
    .locals 0

    invoke-static {p0}, Lcom/google/firebase/perf/FirebasePerfRegistrar;->providesFirebasePerformance(Lge/e;)Lag/c;

    move-result-object p0

    return-object p0
.end method

.method private static providesFirebasePerformance(Lge/e;)Lag/c;
    .locals 6

    invoke-static {}, Ldg/a;->b()Ldg/a$b;

    move-result-object v0

    new-instance v1, Leg/a;

    const-class v2, Lyd/e;

    invoke-interface {p0, v2}, Lge/e;->a(Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lyd/e;

    const-class v3, Lrf/g;

    invoke-interface {p0, v3}, Lge/e;->a(Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lrf/g;

    const-class v4, Log/m;

    invoke-interface {p0, v4}, Lge/e;->g(Ljava/lang/Class;)Lqf/b;

    move-result-object v4

    const-class v5, Lv8/h;

    invoke-interface {p0, v5}, Lge/e;->g(Ljava/lang/Class;)Lqf/b;

    move-result-object p0

    invoke-direct {v1, v2, v3, v4, p0}, Leg/a;-><init>(Lyd/e;Lrf/g;Lqf/b;Lqf/b;)V

    invoke-virtual {v0, v1}, Ldg/a$b;->b(Leg/a;)Ldg/a$b;

    move-result-object p0

    invoke-virtual {p0}, Ldg/a$b;->a()Ldg/b;

    move-result-object p0

    invoke-interface {p0}, Ldg/b;->a()Lag/c;

    move-result-object p0

    return-object p0
.end method


# virtual methods
.method public getComponents()Ljava/util/List;
    .locals 4
    .annotation build Landroidx/annotation/Keep;
    .end annotation

    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lge/c<",
            "*>;>;"
        }
    .end annotation

    const/4 v0, 0x2

    new-array v0, v0, [Lge/c;

    const-class v1, Lag/c;

    invoke-static {v1}, Lge/c;->e(Ljava/lang/Class;)Lge/c$b;

    move-result-object v1

    const-string v2, "fire-perf"

    invoke-virtual {v1, v2}, Lge/c$b;->h(Ljava/lang/String;)Lge/c$b;

    move-result-object v1

    const-class v3, Lyd/e;

    invoke-static {v3}, Lge/r;->k(Ljava/lang/Class;)Lge/r;

    move-result-object v3

    invoke-virtual {v1, v3}, Lge/c$b;->b(Lge/r;)Lge/c$b;

    move-result-object v1

    const-class v3, Log/m;

    invoke-static {v3}, Lge/r;->l(Ljava/lang/Class;)Lge/r;

    move-result-object v3

    invoke-virtual {v1, v3}, Lge/c$b;->b(Lge/r;)Lge/c$b;

    move-result-object v1

    const-class v3, Lrf/g;

    invoke-static {v3}, Lge/r;->k(Ljava/lang/Class;)Lge/r;

    move-result-object v3

    invoke-virtual {v1, v3}, Lge/c$b;->b(Lge/r;)Lge/c$b;

    move-result-object v1

    const-class v3, Lv8/h;

    invoke-static {v3}, Lge/r;->l(Ljava/lang/Class;)Lge/r;

    move-result-object v3

    invoke-virtual {v1, v3}, Lge/c$b;->b(Lge/r;)Lge/c$b;

    move-result-object v1

    new-instance v3, Lag/b;

    invoke-direct {v3}, Lag/b;-><init>()V

    invoke-virtual {v1, v3}, Lge/c$b;->f(Lge/h;)Lge/c$b;

    move-result-object v1

    invoke-virtual {v1}, Lge/c$b;->d()Lge/c;

    move-result-object v1

    const/4 v3, 0x0

    aput-object v1, v0, v3

    const-string v1, "20.3.0"

    invoke-static {v2, v1}, Lng/h;->b(Ljava/lang/String;Ljava/lang/String;)Lge/c;

    move-result-object v1

    const/4 v2, 0x1

    aput-object v1, v0, v2

    invoke-static {v0}, Ljava/util/Arrays;->asList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v0

    return-object v0
.end method
