.class public Lcom/google/firebase/perf/network/FirebasePerfOkHttpClient;
.super Ljava/lang/Object;


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static a(Lokhttp3/y;Lgg/j;JJ)V
    .locals 6
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    invoke-virtual {p0}, Lokhttp3/y;->u()Lokhttp3/w;

    move-result-object v0

    if-nez v0, :cond_0

    return-void

    :cond_0
    invoke-virtual {v0}, Lokhttp3/w;->k()Lokhttp3/s;

    move-result-object v1

    invoke-virtual {v1}, Lokhttp3/s;->u()Ljava/net/URL;

    move-result-object v1

    invoke-virtual {v1}, Ljava/net/URL;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p1, v1}, Lgg/j;->G(Ljava/lang/String;)Lgg/j;

    invoke-virtual {v0}, Lokhttp3/w;->h()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p1, v1}, Lgg/j;->o(Ljava/lang/String;)Lgg/j;

    invoke-virtual {v0}, Lokhttp3/w;->a()Lokhttp3/x;

    move-result-object v1

    const-wide/16 v2, -0x1

    if-eqz v1, :cond_1

    invoke-virtual {v0}, Lokhttp3/w;->a()Lokhttp3/x;

    move-result-object v0

    invoke-virtual {v0}, Lokhttp3/x;->contentLength()J

    move-result-wide v0

    cmp-long v4, v0, v2

    if-eqz v4, :cond_1

    invoke-virtual {p1, v0, v1}, Lgg/j;->z(J)Lgg/j;

    :cond_1
    invoke-virtual {p0}, Lokhttp3/y;->a()Lokhttp3/z;

    move-result-object v0

    if-eqz v0, :cond_3

    invoke-virtual {v0}, Lokhttp3/z;->contentLength()J

    move-result-wide v4

    cmp-long v1, v4, v2

    if-eqz v1, :cond_2

    invoke-virtual {p1, v4, v5}, Lgg/j;->C(J)Lgg/j;

    :cond_2
    invoke-virtual {v0}, Lokhttp3/z;->contentType()Lokhttp3/u;

    move-result-object v0

    if-eqz v0, :cond_3

    invoke-virtual {v0}, Lokhttp3/u;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p1, v0}, Lgg/j;->B(Ljava/lang/String;)Lgg/j;

    :cond_3
    invoke-virtual {p0}, Lokhttp3/y;->g()I

    move-result p0

    invoke-virtual {p1, p0}, Lgg/j;->r(I)Lgg/j;

    invoke-virtual {p1, p2, p3}, Lgg/j;->A(J)Lgg/j;

    invoke-virtual {p1, p4, p5}, Lgg/j;->E(J)Lgg/j;

    invoke-virtual {p1}, Lgg/j;->c()Lcom/google/firebase/perf/v1/NetworkRequestMetric;

    return-void
.end method

.method public static enqueue(Lokhttp3/e;Lokhttp3/f;)V
    .locals 7
    .annotation build Landroidx/annotation/Keep;
    .end annotation

    new-instance v3, Lcom/google/firebase/perf/util/Timer;

    invoke-direct {v3}, Lcom/google/firebase/perf/util/Timer;-><init>()V

    invoke-virtual {v3}, Lcom/google/firebase/perf/util/Timer;->h()J

    move-result-wide v4

    new-instance v6, Lig/i;

    invoke-static {}, Llg/k;->k()Llg/k;

    move-result-object v2

    move-object v0, v6

    move-object v1, p1

    invoke-direct/range {v0 .. v5}, Lig/i;-><init>(Lokhttp3/f;Llg/k;Lcom/google/firebase/perf/util/Timer;J)V

    invoke-interface {p0, v6}, Lokhttp3/e;->f0(Lokhttp3/f;)V

    return-void
.end method

.method public static execute(Lokhttp3/e;)Lokhttp3/y;
    .locals 11
    .annotation build Landroidx/annotation/Keep;
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    invoke-static {}, Llg/k;->k()Llg/k;

    move-result-object v0

    invoke-static {v0}, Lgg/j;->d(Llg/k;)Lgg/j;

    move-result-object v0

    new-instance v7, Lcom/google/firebase/perf/util/Timer;

    invoke-direct {v7}, Lcom/google/firebase/perf/util/Timer;-><init>()V

    invoke-virtual {v7}, Lcom/google/firebase/perf/util/Timer;->h()J

    move-result-wide v8

    :try_start_0
    invoke-interface {p0}, Lokhttp3/e;->execute()Lokhttp3/y;

    move-result-object v10

    invoke-virtual {v7}, Lcom/google/firebase/perf/util/Timer;->d()J

    move-result-wide v5

    move-object v1, v10

    move-object v2, v0

    move-wide v3, v8

    invoke-static/range {v1 .. v6}, Lcom/google/firebase/perf/network/FirebasePerfOkHttpClient;->a(Lokhttp3/y;Lgg/j;JJ)V
    :try_end_0
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_0

    return-object v10

    :catch_0
    move-exception v1

    invoke-interface {p0}, Lokhttp3/e;->request()Lokhttp3/w;

    move-result-object p0

    if-eqz p0, :cond_1

    invoke-virtual {p0}, Lokhttp3/w;->k()Lokhttp3/s;

    move-result-object v2

    if-eqz v2, :cond_0

    invoke-virtual {v2}, Lokhttp3/s;->u()Ljava/net/URL;

    move-result-object v2

    invoke-virtual {v2}, Ljava/net/URL;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v0, v2}, Lgg/j;->G(Ljava/lang/String;)Lgg/j;

    :cond_0
    invoke-virtual {p0}, Lokhttp3/w;->h()Ljava/lang/String;

    move-result-object v2

    if-eqz v2, :cond_1

    invoke-virtual {p0}, Lokhttp3/w;->h()Ljava/lang/String;

    move-result-object p0

    invoke-virtual {v0, p0}, Lgg/j;->o(Ljava/lang/String;)Lgg/j;

    :cond_1
    invoke-virtual {v0, v8, v9}, Lgg/j;->A(J)Lgg/j;

    invoke-virtual {v7}, Lcom/google/firebase/perf/util/Timer;->d()J

    move-result-wide v2

    invoke-virtual {v0, v2, v3}, Lgg/j;->E(J)Lgg/j;

    invoke-static {v0}, Lig/j;->d(Lgg/j;)V

    throw v1
.end method
