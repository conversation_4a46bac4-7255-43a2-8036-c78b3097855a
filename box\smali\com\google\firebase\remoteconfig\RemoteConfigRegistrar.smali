.class public Lcom/google/firebase/remoteconfig/RemoteConfigRegistrar;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/google/firebase/components/ComponentRegistrar;


# annotations
.annotation build Landroidx/annotation/Keep;
.end annotation


# static fields
.field private static final LIBRARY_NAME:Ljava/lang/String; = "fire-rc"


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static synthetic a(Lge/b0;Lge/e;)Log/m;
    .locals 0

    invoke-static {p0, p1}, Lcom/google/firebase/remoteconfig/RemoteConfigRegistrar;->lambda$getComponents$0(Lge/b0;Lge/e;)Log/m;

    move-result-object p0

    return-object p0
.end method

.method private static synthetic lambda$getComponents$0(Lge/b0;Lge/e;)Log/m;
    .locals 8

    new-instance v7, Log/m;

    const-class v0, Landroid/content/Context;

    invoke-interface {p1, v0}, Lge/e;->a(Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v0

    move-object v1, v0

    check-cast v1, Landroid/content/Context;

    invoke-interface {p1, p0}, Lge/e;->f(Lge/b0;)Ljava/lang/Object;

    move-result-object p0

    move-object v2, p0

    check-cast v2, Ljava/util/concurrent/Executor;

    const-class p0, Lyd/e;

    invoke-interface {p1, p0}, Lge/e;->a(Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p0

    move-object v3, p0

    check-cast v3, Lyd/e;

    const-class p0, Lrf/g;

    invoke-interface {p1, p0}, Lge/e;->a(Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p0

    move-object v4, p0

    check-cast v4, Lrf/g;

    const-class p0, Lae/a;

    invoke-interface {p1, p0}, Lge/e;->a(Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p0

    check-cast p0, Lae/a;

    const-string v0, "frc"

    invoke-virtual {p0, v0}, Lae/a;->b(Ljava/lang/String;)Lzd/b;

    move-result-object v5

    const-class p0, Lce/a;

    invoke-interface {p1, p0}, Lge/e;->g(Ljava/lang/Class;)Lqf/b;

    move-result-object v6

    move-object v0, v7

    invoke-direct/range {v0 .. v6}, Log/m;-><init>(Landroid/content/Context;Ljava/util/concurrent/Executor;Lyd/e;Lrf/g;Lzd/b;Lqf/b;)V

    return-object v7
.end method


# virtual methods
.method public getComponents()Ljava/util/List;
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lge/c<",
            "*>;>;"
        }
    .end annotation

    const-class v0, Lfe/b;

    const-class v1, Ljava/util/concurrent/Executor;

    invoke-static {v0, v1}, Lge/b0;->a(Ljava/lang/Class;Ljava/lang/Class;)Lge/b0;

    move-result-object v0

    const/4 v1, 0x2

    new-array v1, v1, [Lge/c;

    const-class v2, Log/m;

    invoke-static {v2}, Lge/c;->e(Ljava/lang/Class;)Lge/c$b;

    move-result-object v2

    const-string v3, "fire-rc"

    invoke-virtual {v2, v3}, Lge/c$b;->h(Ljava/lang/String;)Lge/c$b;

    move-result-object v2

    const-class v4, Landroid/content/Context;

    invoke-static {v4}, Lge/r;->k(Ljava/lang/Class;)Lge/r;

    move-result-object v4

    invoke-virtual {v2, v4}, Lge/c$b;->b(Lge/r;)Lge/c$b;

    move-result-object v2

    invoke-static {v0}, Lge/r;->j(Lge/b0;)Lge/r;

    move-result-object v4

    invoke-virtual {v2, v4}, Lge/c$b;->b(Lge/r;)Lge/c$b;

    move-result-object v2

    const-class v4, Lyd/e;

    invoke-static {v4}, Lge/r;->k(Ljava/lang/Class;)Lge/r;

    move-result-object v4

    invoke-virtual {v2, v4}, Lge/c$b;->b(Lge/r;)Lge/c$b;

    move-result-object v2

    const-class v4, Lrf/g;

    invoke-static {v4}, Lge/r;->k(Ljava/lang/Class;)Lge/r;

    move-result-object v4

    invoke-virtual {v2, v4}, Lge/c$b;->b(Lge/r;)Lge/c$b;

    move-result-object v2

    const-class v4, Lae/a;

    invoke-static {v4}, Lge/r;->k(Ljava/lang/Class;)Lge/r;

    move-result-object v4

    invoke-virtual {v2, v4}, Lge/c$b;->b(Lge/r;)Lge/c$b;

    move-result-object v2

    const-class v4, Lce/a;

    invoke-static {v4}, Lge/r;->i(Ljava/lang/Class;)Lge/r;

    move-result-object v4

    invoke-virtual {v2, v4}, Lge/c$b;->b(Lge/r;)Lge/c$b;

    move-result-object v2

    new-instance v4, Log/n;

    invoke-direct {v4, v0}, Log/n;-><init>(Lge/b0;)V

    invoke-virtual {v2, v4}, Lge/c$b;->f(Lge/h;)Lge/c$b;

    move-result-object v0

    invoke-virtual {v0}, Lge/c$b;->e()Lge/c$b;

    move-result-object v0

    invoke-virtual {v0}, Lge/c$b;->d()Lge/c;

    move-result-object v0

    const/4 v2, 0x0

    aput-object v0, v1, v2

    const-string v0, "21.2.1"

    invoke-static {v3, v0}, Lng/h;->b(Ljava/lang/String;Ljava/lang/String;)Lge/c;

    move-result-object v0

    const/4 v2, 0x1

    aput-object v0, v1, v2

    invoke-static {v1}, Ljava/util/Arrays;->asList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v0

    return-object v0
.end method
