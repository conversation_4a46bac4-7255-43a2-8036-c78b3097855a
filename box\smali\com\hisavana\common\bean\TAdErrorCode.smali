.class public Lcom/hisavana/common/bean/TAdErrorCode;
.super Ljava/lang/Object;


# static fields
.field public static final CODE_AD_HANDLER_IS_NULL:I = 0xea64

.field public static final CODE_AD_IS_SHOWING:I = 0xea66

.field public static final CODE_AD_REQUEST_FAILED:I = 0x7533

.field public static final CODE_AD_REQUEST_TIME_OUT:I = 0x7532

.field public static final CODE_AD_SHOW_NOT_IN_INTERVAL:I = 0x1117a

.field public static final CODE_AD_SHOW_TIMES_OUT_OF_DAY:I = 0x11178

.field public static final CODE_AD_SHOW_TIMES_OUT_OF_HOUR:I = 0x11179

.field public static final CODE_AD_SOURCE_INIT_FAILED:I = 0x7531

.field public static final CODE_AD_SOURCE_LIST_IS_EMPTY:I = 0x1117b

.field public static final CODE_AD_TYPE_LOAD_INCONSISTENCY:I = 0x1117c

.field public static final CODE_AD_UNIT_CONFIG_EMPTY:I = 0x11177

.field public static final CODE_APPLICATION_IS_CLOSED:I = 0x11176

.field public static final CODE_APPLICATION_IS_NOT_EXIST:I = 0x11173

.field public static final CODE_CLOUD_AD_SEAT_IS_CLOSED:I = 0x1117d

.field public static final CODE_CURRENT_OBJECT_IS_DESTROYED:I = 0xea62

.field public static final CODE_FILL_FAILED_WITH_BIDDING_READY:I = 0x4e25

.field public static final CODE_FILL_FAILED_WITH_MEDIATION_TIME_OUT:I = 0x4e23

.field public static final CODE_FILL_FAILED_WITH_NETWORK_EXCEPTION:I = 0x4e24

.field public static final CODE_FILL_FAILED_WITH_NO_AD:I = 0x4e21

.field public static final CODE_FILL_FAILED_WiTH_EXPIRED:I = 0x4e22

.field public static final CODE_INVALID_APP_ID:I = 0xea61

.field public static final CODE_MATERIEL_OBTAIN_FAILED:I = 0x7534

.field public static final CODE_NATIVE_PARMER_IS_NULL:I = 0xea65

.field public static final CODE_OPERATING_SYSTEM_INCONSISTENCY:I = 0x11175

.field public static final CODE_PACKAGE_NAME_IS_INCONSISTENT:I = 0x11174

.field public static final CODE_REQUEST_BIDDING_FAILED:I = 0x7536

.field public static final CODE_REQUEST_CLOUD_PARMER_EXCEPTION:I = 0x11172

.field public static final CODE_REQUEST_CLOUD_TIME_OUT:I = 0x11171

.field public static final CODE_REQUEST_OBJECT_IS_DESTROYED:I = 0x7535

.field public static final CODE_SHOW_EXCEPTION:I = 0xc351

.field public static final CODE_SHOW_VULGAR:I = 0xea67

.field public static final CODE_SPLASH_PARMER_NULL:I = 0xea63

.field public static final CODE_SUCCESS:I = 0x0

.field public static final CODE_TRIGGER_SHOW_OFFLINE_NO_AD:I = 0x9c42

.field public static final CODE_TRIGGER_SHOW_ONLINE_NO_AD:I = 0x9c41

.field public static final CODE_UNKNOWN:I = -0x2710

.field public static final ERROR_AD_HANDLER_IS_NULL:Lcom/hisavana/common/bean/TAdErrorCode;

.field public static final ERROR_AD_REQUEST_FAILED:Lcom/hisavana/common/bean/TAdErrorCode;

.field public static final ERROR_AD_REQUEST_TIME_OUT:Lcom/hisavana/common/bean/TAdErrorCode;

.field public static final ERROR_AD_SHOW_NOT_IN_INTERVAL:Lcom/hisavana/common/bean/TAdErrorCode;

.field public static final ERROR_AD_SHOW_TIMES_OUT_OF_DAY:Lcom/hisavana/common/bean/TAdErrorCode;

.field public static final ERROR_AD_SHOW_TIMES_OUT_OF_HOUR:Lcom/hisavana/common/bean/TAdErrorCode;

.field public static final ERROR_AD_SOURCE_INIT_FAILED:Lcom/hisavana/common/bean/TAdErrorCode;

.field public static final ERROR_AD_SOURCE_LIST_IS_EMPTY:Lcom/hisavana/common/bean/TAdErrorCode;

.field public static final ERROR_AD_TYPE_LOAD_INCONSISTENCY:Lcom/hisavana/common/bean/TAdErrorCode;

.field public static final ERROR_AD_UNIT_CONFIG_EMPTY:Lcom/hisavana/common/bean/TAdErrorCode;

.field public static final ERROR_APPLICATION_IS_CLOSED:Lcom/hisavana/common/bean/TAdErrorCode;

.field public static final ERROR_APPLICATION_IS_NOT_EXIST:Lcom/hisavana/common/bean/TAdErrorCode;

.field public static final ERROR_CLOUD_AD_SEAT_IS_CLOSED:Lcom/hisavana/common/bean/TAdErrorCode;

.field public static final ERROR_CODE_AD_IS_SHOWING:Lcom/hisavana/common/bean/TAdErrorCode;

.field public static final ERROR_CODE_SHOW_VULGAR:Lcom/hisavana/common/bean/TAdErrorCode;

.field public static final ERROR_CURRENT_OBJECT_IS_DESTROYED:Lcom/hisavana/common/bean/TAdErrorCode;

.field public static final ERROR_FILL_FAILED_WITH_BIDDING_READY:Lcom/hisavana/common/bean/TAdErrorCode;

.field public static final ERROR_FILL_FAILED_WITH_MEDIATION_TIME_OUT:Lcom/hisavana/common/bean/TAdErrorCode;

.field public static final ERROR_FILL_FAILED_WITH_NETWORK_EXCEPTION:Lcom/hisavana/common/bean/TAdErrorCode;

.field public static final ERROR_FILL_FAILED_WITH_NO_AD:Lcom/hisavana/common/bean/TAdErrorCode;

.field public static final ERROR_FILL_FAILED_WiTH_EXPIRED:Lcom/hisavana/common/bean/TAdErrorCode;

.field public static final ERROR_INVALID_APP_ID:Lcom/hisavana/common/bean/TAdErrorCode;

.field public static final ERROR_NATIVE_PARMER_IS_NULL:Lcom/hisavana/common/bean/TAdErrorCode;

.field public static final ERROR_OPERATING_SYSTEM_INCONSISTENCY:Lcom/hisavana/common/bean/TAdErrorCode;

.field public static final ERROR_PACKAGE_NAME_IS_INCONSISTENT:Lcom/hisavana/common/bean/TAdErrorCode;

.field public static final ERROR_PREPARE_REQUEST_FAILED:Lcom/hisavana/common/bean/TAdErrorCode;

.field public static final ERROR_REQUEST_CLOUD_PARMER_EXCEPTION:Lcom/hisavana/common/bean/TAdErrorCode;

.field public static final ERROR_REQUEST_CLOUD_TIME_OUT:Lcom/hisavana/common/bean/TAdErrorCode;

.field public static final ERROR_REQUEST_OBJECT_IS_DESTROYED:Lcom/hisavana/common/bean/TAdErrorCode;

.field public static final ERROR_RIGGER_SHOW_OFFLINE_NO_AD:Lcom/hisavana/common/bean/TAdErrorCode;

.field public static final ERROR_SHOW_EXCEPTION:Lcom/hisavana/common/bean/TAdErrorCode;

.field public static final ERROR_SPLASH_PARMER_NULL:Lcom/hisavana/common/bean/TAdErrorCode;

.field public static final ERROR_TRIGGER_SHOW_ONLINE_NO_AD:Lcom/hisavana/common/bean/TAdErrorCode;

.field public static final ERROR_UNKNOWN:Lcom/hisavana/common/bean/TAdErrorCode;

.field public static final PREPARE_REQUEST_FAILED:I = 0x1117e

.field public static final SUCCESS_MESSAGE:Lcom/hisavana/common/bean/TAdErrorCode;


# instance fields
.field private final errorCode:I

.field private final errorMessage:Ljava/lang/String;


# direct methods
.method static constructor <clinit>()V
    .locals 3

    new-instance v0, Lcom/hisavana/common/bean/TAdErrorCode;

    const/4 v1, 0x0

    const-string v2, "success"

    invoke-direct {v0, v1, v2}, Lcom/hisavana/common/bean/TAdErrorCode;-><init>(ILjava/lang/String;)V

    sput-object v0, Lcom/hisavana/common/bean/TAdErrorCode;->SUCCESS_MESSAGE:Lcom/hisavana/common/bean/TAdErrorCode;

    new-instance v0, Lcom/hisavana/common/bean/TAdErrorCode;

    const/16 v1, -0x2710

    const-string v2, "unknown error"

    invoke-direct {v0, v1, v2}, Lcom/hisavana/common/bean/TAdErrorCode;-><init>(ILjava/lang/String;)V

    sput-object v0, Lcom/hisavana/common/bean/TAdErrorCode;->ERROR_UNKNOWN:Lcom/hisavana/common/bean/TAdErrorCode;

    new-instance v0, Lcom/hisavana/common/bean/TAdErrorCode;

    const/16 v1, 0x4e21

    const-string v2, "No ads available, fill failed"

    invoke-direct {v0, v1, v2}, Lcom/hisavana/common/bean/TAdErrorCode;-><init>(ILjava/lang/String;)V

    sput-object v0, Lcom/hisavana/common/bean/TAdErrorCode;->ERROR_FILL_FAILED_WITH_NO_AD:Lcom/hisavana/common/bean/TAdErrorCode;

    new-instance v0, Lcom/hisavana/common/bean/TAdErrorCode;

    const/16 v1, 0x4e22

    const-string v2, "no ad or ad is expired"

    invoke-direct {v0, v1, v2}, Lcom/hisavana/common/bean/TAdErrorCode;-><init>(ILjava/lang/String;)V

    sput-object v0, Lcom/hisavana/common/bean/TAdErrorCode;->ERROR_FILL_FAILED_WiTH_EXPIRED:Lcom/hisavana/common/bean/TAdErrorCode;

    new-instance v0, Lcom/hisavana/common/bean/TAdErrorCode;

    const/16 v1, 0x4e23

    const-string v2, "The request duration configured by the cloud control has timed out."

    invoke-direct {v0, v1, v2}, Lcom/hisavana/common/bean/TAdErrorCode;-><init>(ILjava/lang/String;)V

    sput-object v0, Lcom/hisavana/common/bean/TAdErrorCode;->ERROR_FILL_FAILED_WITH_MEDIATION_TIME_OUT:Lcom/hisavana/common/bean/TAdErrorCode;

    new-instance v0, Lcom/hisavana/common/bean/TAdErrorCode;

    const/16 v1, 0x4e24

    const-string v2, "Network exception, filling failed"

    invoke-direct {v0, v1, v2}, Lcom/hisavana/common/bean/TAdErrorCode;-><init>(ILjava/lang/String;)V

    sput-object v0, Lcom/hisavana/common/bean/TAdErrorCode;->ERROR_FILL_FAILED_WITH_NETWORK_EXCEPTION:Lcom/hisavana/common/bean/TAdErrorCode;

    new-instance v0, Lcom/hisavana/common/bean/TAdErrorCode;

    const/16 v1, 0x4e25

    const-string v2, "Bidding time is ready,but no ad fill"

    invoke-direct {v0, v1, v2}, Lcom/hisavana/common/bean/TAdErrorCode;-><init>(ILjava/lang/String;)V

    sput-object v0, Lcom/hisavana/common/bean/TAdErrorCode;->ERROR_FILL_FAILED_WITH_BIDDING_READY:Lcom/hisavana/common/bean/TAdErrorCode;

    new-instance v0, Lcom/hisavana/common/bean/TAdErrorCode;

    const/16 v1, 0x7531

    const-string v2, "Ad source initialization failed"

    invoke-direct {v0, v1, v2}, Lcom/hisavana/common/bean/TAdErrorCode;-><init>(ILjava/lang/String;)V

    sput-object v0, Lcom/hisavana/common/bean/TAdErrorCode;->ERROR_AD_SOURCE_INIT_FAILED:Lcom/hisavana/common/bean/TAdErrorCode;

    new-instance v0, Lcom/hisavana/common/bean/TAdErrorCode;

    const/16 v1, 0x7532

    const-string v2, "Ad request timeout"

    invoke-direct {v0, v1, v2}, Lcom/hisavana/common/bean/TAdErrorCode;-><init>(ILjava/lang/String;)V

    sput-object v0, Lcom/hisavana/common/bean/TAdErrorCode;->ERROR_AD_REQUEST_TIME_OUT:Lcom/hisavana/common/bean/TAdErrorCode;

    new-instance v0, Lcom/hisavana/common/bean/TAdErrorCode;

    const/16 v1, 0x7533

    const-string v2, "Ad request failed"

    invoke-direct {v0, v1, v2}, Lcom/hisavana/common/bean/TAdErrorCode;-><init>(ILjava/lang/String;)V

    sput-object v0, Lcom/hisavana/common/bean/TAdErrorCode;->ERROR_AD_REQUEST_FAILED:Lcom/hisavana/common/bean/TAdErrorCode;

    new-instance v0, Lcom/hisavana/common/bean/TAdErrorCode;

    const/16 v1, 0x7535

    const-string v2, "The requesting object is destroyed"

    invoke-direct {v0, v1, v2}, Lcom/hisavana/common/bean/TAdErrorCode;-><init>(ILjava/lang/String;)V

    sput-object v0, Lcom/hisavana/common/bean/TAdErrorCode;->ERROR_REQUEST_OBJECT_IS_DESTROYED:Lcom/hisavana/common/bean/TAdErrorCode;

    new-instance v0, Lcom/hisavana/common/bean/TAdErrorCode;

    const v1, 0x9c41

    const-string v2, "Media calls to show, but no ads are currently available"

    invoke-direct {v0, v1, v2}, Lcom/hisavana/common/bean/TAdErrorCode;-><init>(ILjava/lang/String;)V

    sput-object v0, Lcom/hisavana/common/bean/TAdErrorCode;->ERROR_TRIGGER_SHOW_ONLINE_NO_AD:Lcom/hisavana/common/bean/TAdErrorCode;

    new-instance v0, Lcom/hisavana/common/bean/TAdErrorCode;

    const v1, 0x9c42

    const-string v2, "The media calls for display in a network-free environment, but there are currently no offline ads available."

    invoke-direct {v0, v1, v2}, Lcom/hisavana/common/bean/TAdErrorCode;-><init>(ILjava/lang/String;)V

    sput-object v0, Lcom/hisavana/common/bean/TAdErrorCode;->ERROR_RIGGER_SHOW_OFFLINE_NO_AD:Lcom/hisavana/common/bean/TAdErrorCode;

    new-instance v0, Lcom/hisavana/common/bean/TAdErrorCode;

    const v1, 0xc351

    const-string v2, "Ad display failed"

    invoke-direct {v0, v1, v2}, Lcom/hisavana/common/bean/TAdErrorCode;-><init>(ILjava/lang/String;)V

    sput-object v0, Lcom/hisavana/common/bean/TAdErrorCode;->ERROR_SHOW_EXCEPTION:Lcom/hisavana/common/bean/TAdErrorCode;

    new-instance v0, Lcom/hisavana/common/bean/TAdErrorCode;

    const v1, 0xea61

    const-string v2, "Invalid app id"

    invoke-direct {v0, v1, v2}, Lcom/hisavana/common/bean/TAdErrorCode;-><init>(ILjava/lang/String;)V

    sput-object v0, Lcom/hisavana/common/bean/TAdErrorCode;->ERROR_INVALID_APP_ID:Lcom/hisavana/common/bean/TAdErrorCode;

    new-instance v0, Lcom/hisavana/common/bean/TAdErrorCode;

    const v1, 0xea62

    const-string v2, "Operation using an object that has been destroyed"

    invoke-direct {v0, v1, v2}, Lcom/hisavana/common/bean/TAdErrorCode;-><init>(ILjava/lang/String;)V

    sput-object v0, Lcom/hisavana/common/bean/TAdErrorCode;->ERROR_CURRENT_OBJECT_IS_DESTROYED:Lcom/hisavana/common/bean/TAdErrorCode;

    new-instance v0, Lcom/hisavana/common/bean/TAdErrorCode;

    const v1, 0xea63

    const-string v2, "When displaying splash ads, the parameters are abnormal"

    invoke-direct {v0, v1, v2}, Lcom/hisavana/common/bean/TAdErrorCode;-><init>(ILjava/lang/String;)V

    sput-object v0, Lcom/hisavana/common/bean/TAdErrorCode;->ERROR_SPLASH_PARMER_NULL:Lcom/hisavana/common/bean/TAdErrorCode;

    new-instance v0, Lcom/hisavana/common/bean/TAdErrorCode;

    const v1, 0xea64

    const-string v2, "SDK internal logic problem, cacheHandler cannot be obtained currently"

    invoke-direct {v0, v1, v2}, Lcom/hisavana/common/bean/TAdErrorCode;-><init>(ILjava/lang/String;)V

    sput-object v0, Lcom/hisavana/common/bean/TAdErrorCode;->ERROR_AD_HANDLER_IS_NULL:Lcom/hisavana/common/bean/TAdErrorCode;

    new-instance v0, Lcom/hisavana/common/bean/TAdErrorCode;

    const v1, 0xea65

    const-string v2, "When displaying native or icon ads, the parameters are abnormal"

    invoke-direct {v0, v1, v2}, Lcom/hisavana/common/bean/TAdErrorCode;-><init>(ILjava/lang/String;)V

    sput-object v0, Lcom/hisavana/common/bean/TAdErrorCode;->ERROR_NATIVE_PARMER_IS_NULL:Lcom/hisavana/common/bean/TAdErrorCode;

    new-instance v0, Lcom/hisavana/common/bean/TAdErrorCode;

    const v1, 0xea66

    const-string v2, "ad is showing, please wait for the ad to close"

    invoke-direct {v0, v1, v2}, Lcom/hisavana/common/bean/TAdErrorCode;-><init>(ILjava/lang/String;)V

    sput-object v0, Lcom/hisavana/common/bean/TAdErrorCode;->ERROR_CODE_AD_IS_SHOWING:Lcom/hisavana/common/bean/TAdErrorCode;

    new-instance v0, Lcom/hisavana/common/bean/TAdErrorCode;

    const v1, 0xea67

    const-string v2, "The current page contains sensitive content and advertising brands are restricted"

    invoke-direct {v0, v1, v2}, Lcom/hisavana/common/bean/TAdErrorCode;-><init>(ILjava/lang/String;)V

    sput-object v0, Lcom/hisavana/common/bean/TAdErrorCode;->ERROR_CODE_SHOW_VULGAR:Lcom/hisavana/common/bean/TAdErrorCode;

    new-instance v0, Lcom/hisavana/common/bean/TAdErrorCode;

    const v1, 0x11171

    const-string v2, "Configuration information request timed out"

    invoke-direct {v0, v1, v2}, Lcom/hisavana/common/bean/TAdErrorCode;-><init>(ILjava/lang/String;)V

    sput-object v0, Lcom/hisavana/common/bean/TAdErrorCode;->ERROR_REQUEST_CLOUD_TIME_OUT:Lcom/hisavana/common/bean/TAdErrorCode;

    new-instance v0, Lcom/hisavana/common/bean/TAdErrorCode;

    const v1, 0x11172

    const-string v2, "Cloud control information request parameter abnormality"

    invoke-direct {v0, v1, v2}, Lcom/hisavana/common/bean/TAdErrorCode;-><init>(ILjava/lang/String;)V

    sput-object v0, Lcom/hisavana/common/bean/TAdErrorCode;->ERROR_REQUEST_CLOUD_PARMER_EXCEPTION:Lcom/hisavana/common/bean/TAdErrorCode;

    new-instance v0, Lcom/hisavana/common/bean/TAdErrorCode;

    const v1, 0x11173

    const-string v2, "Cloud control information request, app id verification failed"

    invoke-direct {v0, v1, v2}, Lcom/hisavana/common/bean/TAdErrorCode;-><init>(ILjava/lang/String;)V

    sput-object v0, Lcom/hisavana/common/bean/TAdErrorCode;->ERROR_APPLICATION_IS_NOT_EXIST:Lcom/hisavana/common/bean/TAdErrorCode;

    new-instance v0, Lcom/hisavana/common/bean/TAdErrorCode;

    const v1, 0x11174

    const-string v2, "Cloud control information request, app id and package name do not match"

    invoke-direct {v0, v1, v2}, Lcom/hisavana/common/bean/TAdErrorCode;-><init>(ILjava/lang/String;)V

    sput-object v0, Lcom/hisavana/common/bean/TAdErrorCode;->ERROR_PACKAGE_NAME_IS_INCONSISTENT:Lcom/hisavana/common/bean/TAdErrorCode;

    new-instance v0, Lcom/hisavana/common/bean/TAdErrorCode;

    const v1, 0x11175

    const-string v2, "Cloud control information request, system inconsistency"

    invoke-direct {v0, v1, v2}, Lcom/hisavana/common/bean/TAdErrorCode;-><init>(ILjava/lang/String;)V

    sput-object v0, Lcom/hisavana/common/bean/TAdErrorCode;->ERROR_OPERATING_SYSTEM_INCONSISTENCY:Lcom/hisavana/common/bean/TAdErrorCode;

    new-instance v0, Lcom/hisavana/common/bean/TAdErrorCode;

    const v1, 0x11176

    const-string v2, "Cloud control information request, the application has been closed"

    invoke-direct {v0, v1, v2}, Lcom/hisavana/common/bean/TAdErrorCode;-><init>(ILjava/lang/String;)V

    sput-object v0, Lcom/hisavana/common/bean/TAdErrorCode;->ERROR_APPLICATION_IS_CLOSED:Lcom/hisavana/common/bean/TAdErrorCode;

    new-instance v0, Lcom/hisavana/common/bean/TAdErrorCode;

    const v1, 0x11177

    const-string v2, "No matching code seat information"

    invoke-direct {v0, v1, v2}, Lcom/hisavana/common/bean/TAdErrorCode;-><init>(ILjava/lang/String;)V

    sput-object v0, Lcom/hisavana/common/bean/TAdErrorCode;->ERROR_AD_UNIT_CONFIG_EMPTY:Lcom/hisavana/common/bean/TAdErrorCode;

    new-instance v0, Lcom/hisavana/common/bean/TAdErrorCode;

    const v1, 0x11178

    const-string v2, "The number of impressions in a single day for cloud control configuration has reached"

    invoke-direct {v0, v1, v2}, Lcom/hisavana/common/bean/TAdErrorCode;-><init>(ILjava/lang/String;)V

    sput-object v0, Lcom/hisavana/common/bean/TAdErrorCode;->ERROR_AD_SHOW_TIMES_OUT_OF_DAY:Lcom/hisavana/common/bean/TAdErrorCode;

    new-instance v0, Lcom/hisavana/common/bean/TAdErrorCode;

    const v1, 0x11179

    const-string v2, "The number of impressions in a single hour for cloud control configuration has reached"

    invoke-direct {v0, v1, v2}, Lcom/hisavana/common/bean/TAdErrorCode;-><init>(ILjava/lang/String;)V

    sput-object v0, Lcom/hisavana/common/bean/TAdErrorCode;->ERROR_AD_SHOW_TIMES_OUT_OF_HOUR:Lcom/hisavana/common/bean/TAdErrorCode;

    new-instance v0, Lcom/hisavana/common/bean/TAdErrorCode;

    const v1, 0x1117a

    const-string v2, "The display interval of cloud control configuration has not been reached yet"

    invoke-direct {v0, v1, v2}, Lcom/hisavana/common/bean/TAdErrorCode;-><init>(ILjava/lang/String;)V

    sput-object v0, Lcom/hisavana/common/bean/TAdErrorCode;->ERROR_AD_SHOW_NOT_IN_INTERVAL:Lcom/hisavana/common/bean/TAdErrorCode;

    new-instance v0, Lcom/hisavana/common/bean/TAdErrorCode;

    const v1, 0x1117b

    const-string v2, "There is no advertising source configured under the advertising slot"

    invoke-direct {v0, v1, v2}, Lcom/hisavana/common/bean/TAdErrorCode;-><init>(ILjava/lang/String;)V

    sput-object v0, Lcom/hisavana/common/bean/TAdErrorCode;->ERROR_AD_SOURCE_LIST_IS_EMPTY:Lcom/hisavana/common/bean/TAdErrorCode;

    new-instance v0, Lcom/hisavana/common/bean/TAdErrorCode;

    const v1, 0x1117c

    const-string v2, "The API used by the media does not match the current ad slot"

    invoke-direct {v0, v1, v2}, Lcom/hisavana/common/bean/TAdErrorCode;-><init>(ILjava/lang/String;)V

    sput-object v0, Lcom/hisavana/common/bean/TAdErrorCode;->ERROR_AD_TYPE_LOAD_INCONSISTENCY:Lcom/hisavana/common/bean/TAdErrorCode;

    new-instance v0, Lcom/hisavana/common/bean/TAdErrorCode;

    const v1, 0x1117d

    const-string v2, "The advertising function has been turned off for this ad slot"

    invoke-direct {v0, v1, v2}, Lcom/hisavana/common/bean/TAdErrorCode;-><init>(ILjava/lang/String;)V

    sput-object v0, Lcom/hisavana/common/bean/TAdErrorCode;->ERROR_CLOUD_AD_SEAT_IS_CLOSED:Lcom/hisavana/common/bean/TAdErrorCode;

    new-instance v0, Lcom/hisavana/common/bean/TAdErrorCode;

    const v1, 0x1117e

    const-string v2, "prepare request failed"

    invoke-direct {v0, v1, v2}, Lcom/hisavana/common/bean/TAdErrorCode;-><init>(ILjava/lang/String;)V

    sput-object v0, Lcom/hisavana/common/bean/TAdErrorCode;->ERROR_PREPARE_REQUEST_FAILED:Lcom/hisavana/common/bean/TAdErrorCode;

    return-void
.end method

.method public constructor <init>(ILjava/lang/String;)V
    .locals 1

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    invoke-static {p2}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    if-eqz v0, :cond_0

    const-string p2, "empty msg"

    :cond_0
    iput p1, p0, Lcom/hisavana/common/bean/TAdErrorCode;->errorCode:I

    iput-object p2, p0, Lcom/hisavana/common/bean/TAdErrorCode;->errorMessage:Ljava/lang/String;

    return-void
.end method


# virtual methods
.method public getErrorCode()I
    .locals 1

    iget v0, p0, Lcom/hisavana/common/bean/TAdErrorCode;->errorCode:I

    return v0
.end method

.method public getErrorMessage()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/hisavana/common/bean/TAdErrorCode;->errorMessage:Ljava/lang/String;

    return-object v0
.end method

.method public toString()Ljava/lang/String;
    .locals 2

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "TAdErrorCode{errorCode="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget v1, p0, Lcom/hisavana/common/bean/TAdErrorCode;->errorCode:I

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v1, ", errorMessage=\'"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v1, p0, Lcom/hisavana/common/bean/TAdErrorCode;->errorMessage:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const/16 v1, 0x27

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    const/16 v1, 0x7d

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
