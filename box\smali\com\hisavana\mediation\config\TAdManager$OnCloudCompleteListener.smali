.class public interface abstract Lcom/hisavana/mediation/config/TAdManager$OnCloudCompleteListener;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/hisavana/mediation/config/TAdManager;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "OnCloudCompleteListener"
.end annotation


# virtual methods
.method public abstract onCloudComplete(ILjava/lang/String;)V
.end method
