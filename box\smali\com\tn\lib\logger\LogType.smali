.class public final enum Lcom/tn/lib/logger/LogType;
.super Ljava/lang/Enum;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Enum<",
        "Lcom/tn/lib/logger/LogType;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation


# static fields
.field private static final synthetic $VALUES:[Lcom/tn/lib/logger/LogType;

.field public static final enum TYPE_LOGCAT:Lcom/tn/lib/logger/LogType;

.field public static final enum TYPE_XLOG:Lcom/tn/lib/logger/LogType;


# direct methods
.method private static final synthetic $values()[Lcom/tn/lib/logger/LogType;
    .locals 3

    const/4 v0, 0x2

    new-array v0, v0, [Lcom/tn/lib/logger/LogType;

    const/4 v1, 0x0

    sget-object v2, Lcom/tn/lib/logger/LogType;->TYPE_XLOG:Lcom/tn/lib/logger/LogType;

    aput-object v2, v0, v1

    const/4 v1, 0x1

    sget-object v2, Lcom/tn/lib/logger/LogType;->TYPE_LOGCAT:Lcom/tn/lib/logger/LogType;

    aput-object v2, v0, v1

    return-object v0
.end method

.method static constructor <clinit>()V
    .locals 3

    new-instance v0, Lcom/tn/lib/logger/LogType;

    const-string v1, "TYPE_XLOG"

    const/4 v2, 0x0

    invoke-direct {v0, v1, v2}, Lcom/tn/lib/logger/LogType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lcom/tn/lib/logger/LogType;->TYPE_XLOG:Lcom/tn/lib/logger/LogType;

    new-instance v0, Lcom/tn/lib/logger/LogType;

    const-string v1, "TYPE_LOGCAT"

    const/4 v2, 0x1

    invoke-direct {v0, v1, v2}, Lcom/tn/lib/logger/LogType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lcom/tn/lib/logger/LogType;->TYPE_LOGCAT:Lcom/tn/lib/logger/LogType;

    invoke-static {}, Lcom/tn/lib/logger/LogType;->$values()[Lcom/tn/lib/logger/LogType;

    move-result-object v0

    sput-object v0, Lcom/tn/lib/logger/LogType;->$VALUES:[Lcom/tn/lib/logger/LogType;

    return-void
.end method

.method private constructor <init>(Ljava/lang/String;I)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation

    invoke-direct {p0, p1, p2}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    return-void
.end method

.method public static valueOf(Ljava/lang/String;)Lcom/tn/lib/logger/LogType;
    .locals 1

    const-class v0, Lcom/tn/lib/logger/LogType;

    invoke-static {v0, p0}, Ljava/lang/Enum;->valueOf(Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Enum;

    move-result-object p0

    check-cast p0, Lcom/tn/lib/logger/LogType;

    return-object p0
.end method

.method public static values()[Lcom/tn/lib/logger/LogType;
    .locals 1

    sget-object v0, Lcom/tn/lib/logger/LogType;->$VALUES:[Lcom/tn/lib/logger/LogType;

    invoke-virtual {v0}, [Ljava/lang/Object;->clone()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [Lcom/tn/lib/logger/LogType;

    return-object v0
.end method
