.class public final enum Lcom/tn/lib/net/cons/HeaderType;
.super Ljava/lang/Enum;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Enum<",
        "Lcom/tn/lib/net/cons/HeaderType;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation


# static fields
.field private static final synthetic $VALUES:[Lcom/tn/lib/net/cons/HeaderType;

.field public static final enum ONE_ROOM:Lcom/tn/lib/net/cons/HeaderType;


# direct methods
.method private static final synthetic $values()[Lcom/tn/lib/net/cons/HeaderType;
    .locals 3

    const/4 v0, 0x1

    new-array v0, v0, [Lcom/tn/lib/net/cons/HeaderType;

    const/4 v1, 0x0

    sget-object v2, Lcom/tn/lib/net/cons/HeaderType;->ONE_ROOM:Lcom/tn/lib/net/cons/HeaderType;

    aput-object v2, v0, v1

    return-object v0
.end method

.method static constructor <clinit>()V
    .locals 3

    new-instance v0, Lcom/tn/lib/net/cons/HeaderType;

    const-string v1, "ONE_ROOM"

    const/4 v2, 0x0

    invoke-direct {v0, v1, v2}, Lcom/tn/lib/net/cons/HeaderType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lcom/tn/lib/net/cons/HeaderType;->ONE_ROOM:Lcom/tn/lib/net/cons/HeaderType;

    invoke-static {}, Lcom/tn/lib/net/cons/HeaderType;->$values()[Lcom/tn/lib/net/cons/HeaderType;

    move-result-object v0

    sput-object v0, Lcom/tn/lib/net/cons/HeaderType;->$VALUES:[Lcom/tn/lib/net/cons/HeaderType;

    return-void
.end method

.method private constructor <init>(Ljava/lang/String;I)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation

    invoke-direct {p0, p1, p2}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    return-void
.end method

.method public static valueOf(Ljava/lang/String;)Lcom/tn/lib/net/cons/HeaderType;
    .locals 1

    const-class v0, Lcom/tn/lib/net/cons/HeaderType;

    invoke-static {v0, p0}, Ljava/lang/Enum;->valueOf(Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Enum;

    move-result-object p0

    check-cast p0, Lcom/tn/lib/net/cons/HeaderType;

    return-object p0
.end method

.method public static values()[Lcom/tn/lib/net/cons/HeaderType;
    .locals 1

    sget-object v0, Lcom/tn/lib/net/cons/HeaderType;->$VALUES:[Lcom/tn/lib/net/cons/HeaderType;

    invoke-virtual {v0}, [Ljava/lang/Object;->clone()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [Lcom/tn/lib/net/cons/HeaderType;

    return-object v0
.end method
