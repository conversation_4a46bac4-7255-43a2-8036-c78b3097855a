.class public final Lcom/tn/lib/net/interceptor/DynamicHostInterceptor;
.super Ljava/lang/Object;

# interfaces
.implements Lokhttp3/t;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/tn/lib/net/interceptor/DynamicHostInterceptor$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation


# static fields
.field public static final b:Lcom/tn/lib/net/interceptor/DynamicHostInterceptor$a;


# instance fields
.field public final a:Lkotlin/Lazy;


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lcom/tn/lib/net/interceptor/DynamicHostInterceptor$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lcom/tn/lib/net/interceptor/DynamicHostInterceptor$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lcom/tn/lib/net/interceptor/DynamicHostInterceptor;->b:Lcom/tn/lib/net/interceptor/DynamicHostInterceptor$a;

    return-void
.end method

.method public constructor <init>()V
    .locals 1

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    sget-object v0, Lcom/tn/lib/net/interceptor/DynamicHostInterceptor$handler$2;->INSTANCE:Lcom/tn/lib/net/interceptor/DynamicHostInterceptor$handler$2;

    invoke-static {v0}, Lkotlin/LazyKt;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/Lazy;

    move-result-object v0

    iput-object v0, p0, Lcom/tn/lib/net/interceptor/DynamicHostInterceptor;->a:Lkotlin/Lazy;

    return-void
.end method


# virtual methods
.method public intercept(Lokhttp3/t$a;)Lokhttp3/y;
    .locals 6

    const-string v0, "chain"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-interface {p1}, Lokhttp3/t$a;->request()Lokhttp3/w;

    move-result-object v0

    invoke-virtual {v0}, Lokhttp3/w;->k()Lokhttp3/s;

    move-result-object v1

    invoke-virtual {v1}, Lokhttp3/s;->k()Lokhttp3/s$a;

    move-result-object v1

    invoke-virtual {v0}, Lokhttp3/w;->k()Lokhttp3/s;

    move-result-object v2

    invoke-virtual {v2}, Lokhttp3/s;->i()Ljava/lang/String;

    move-result-object v2

    sget-object v3, Lcom/tn/lib/net/dns/or/CacheIpPool;->a:Lcom/tn/lib/net/dns/or/CacheIpPool;

    invoke-virtual {v3}, Lcom/tn/lib/net/dns/or/CacheIpPool;->g()Ljava/lang/String;

    move-result-object v4

    invoke-static {v2, v4}, Landroid/text/TextUtils;->equals(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Z

    move-result v2

    const-string v4, "host"

    if-eqz v2, :cond_1

    invoke-virtual {v0}, Lokhttp3/w;->k()Lokhttp3/s;

    move-result-object v2

    invoke-virtual {v2}, Lokhttp3/s;->s()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v3}, Lcom/tn/lib/net/dns/or/CacheIpPool;->h()Ljava/lang/String;

    move-result-object v5

    invoke-static {v2, v5}, Landroid/text/TextUtils;->equals(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Z

    move-result v2

    if-nez v2, :cond_0

    goto :goto_0

    :cond_0
    invoke-virtual {v1, v4}, Lokhttp3/s$a;->s(Ljava/lang/String;)Lokhttp3/s$a;

    goto :goto_1

    :cond_1
    :goto_0
    invoke-virtual {v3}, Lcom/tn/lib/net/dns/or/CacheIpPool;->h()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v2}, Lokhttp3/s$a;->u(Ljava/lang/String;)Lokhttp3/s$a;

    move-result-object v2

    invoke-virtual {v3}, Lcom/tn/lib/net/dns/or/CacheIpPool;->g()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v2, v3}, Lokhttp3/s$a;->h(Ljava/lang/String;)Lokhttp3/s$a;

    move-result-object v2

    invoke-virtual {v2, v4}, Lokhttp3/s$a;->s(Ljava/lang/String;)Lokhttp3/s$a;

    :goto_1
    invoke-virtual {v0}, Lokhttp3/w;->i()Lokhttp3/w$a;

    move-result-object v0

    invoke-virtual {v1}, Lokhttp3/s$a;->c()Lokhttp3/s;

    move-result-object v1

    invoke-virtual {v0, v1}, Lokhttp3/w$a;->o(Lokhttp3/s;)Lokhttp3/w$a;

    move-result-object v0

    invoke-virtual {v0}, Lokhttp3/w$a;->b()Lokhttp3/w;

    move-result-object v0

    invoke-interface {p1, v0}, Lokhttp3/t$a;->a(Lokhttp3/w;)Lokhttp3/y;

    move-result-object p1

    sget-object v1, Lxi/b;->a:Lxi/b$a;

    invoke-virtual {p1}, Lokhttp3/y;->g()I

    move-result v2

    invoke-virtual {v0}, Lokhttp3/w;->k()Lokhttp3/s;

    move-result-object v0

    invoke-virtual {p1}, Lokhttp3/y;->s()Lokhttp3/Protocol;

    move-result-object v3

    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    const-string v5, "DynamicHostInterceptor response "

    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4, v2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v2, " : "

    invoke-virtual {v4, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v0, " protocol: "

    invoke-virtual {v4, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v0, " "

    invoke-virtual {v4, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    filled-new-array {v0}, [Ljava/lang/String;

    move-result-object v0

    const/4 v2, 0x1

    const-string v3, "HttpTag"

    invoke-virtual {v1, v3, v0, v2}, Lxi/b$a;->n(Ljava/lang/String;[Ljava/lang/String;Z)V

    return-object p1
.end method
