.class public final Lcom/tn/lib/widget/R$color;
.super Ljava/lang/Object;


# static fields
.field public static association_bg_grad_1:I = 0x7f060027

.field public static association_bg_grad_2:I = 0x7f060028

.field public static base_color_884DFF:I = 0x7f060036

.field public static base_color_999999:I = 0x7f060038

.field public static bg_01:I = 0x7f060046

.field public static bg_01_0:I = 0x7f060047

.field public static bg_01_20:I = 0x7f060048

.field public static bg_01_70:I = 0x7f060049

.field public static bg_01_70_reverse:I = 0x7f06004a

.field public static bg_02:I = 0x7f06004b

.field public static bg_03:I = 0x7f06004c

.field public static bg_04:I = 0x7f06004d

.field public static bg_05:I = 0x7f06004e

.field public static bg_06:I = 0x7f06004f

.field public static bg_07:I = 0x7f060050

.field public static bg_08:I = 0x7f060051

.field public static bg_09:I = 0x7f060052

.field public static bg_search_bg_01:I = 0x7f060053

.field public static black:I = 0x7f060054

.field public static black_0:I = 0x7f060055

.field public static black_10:I = 0x7f060056

.field public static black_20:I = 0x7f060057

.field public static black_25:I = 0x7f060058

.field public static black_30:I = 0x7f060059

.field public static black_40:I = 0x7f06005a

.field public static black_50:I = 0x7f06005b

.field public static black_60:I = 0x7f06005c

.field public static black_70:I = 0x7f06005d

.field public static black_75:I = 0x7f06005e

.field public static black_80:I = 0x7f06005f

.field public static black_90:I = 0x7f060060

.field public static border:I = 0x7f060061

.field public static border_2:I = 0x7f060062

.field public static border_3:I = 0x7f060063

.field public static border_4:I = 0x7f060064

.field public static brand:I = 0x7f060065

.field public static brand_0:I = 0x7f060066

.field public static brand_00:I = 0x7f060067

.field public static brand_10:I = 0x7f060069

.field public static brand_30:I = 0x7f06006a

.field public static brand_40:I = 0x7f06006b

.field public static brand_50:I = 0x7f06006c

.field public static brand_60:I = 0x7f06006d

.field public static brand_70:I = 0x7f06006e

.field public static brand_80:I = 0x7f06006f

.field public static brand_90:I = 0x7f060070

.field public static brand_dark_00:I = 0x7f060071

.field public static brand_dark_10:I = 0x7f060072

.field public static brand_dark_20:I = 0x7f060073

.field public static brand_dark_30:I = 0x7f060074

.field public static brand_dark_40:I = 0x7f060075

.field public static brand_dark_50:I = 0x7f060076

.field public static brand_dark_60:I = 0x7f060077

.field public static brand_dark_70:I = 0x7f060078

.field public static brand_dark_80:I = 0x7f060079

.field public static brand_dark_90:I = 0x7f06007a

.field public static brand_gradient_center:I = 0x7f06007b

.field public static brand_gradient_center_40:I = 0x7f06007c

.field public static brand_gradient_center_60:I = 0x7f06007d

.field public static brand_gradient_center_80:I = 0x7f06007e

.field public static brand_gradient_center_color:I = 0x7f06007f

.field public static brand_gradient_end:I = 0x7f060080

.field public static brand_gradient_end_40:I = 0x7f060081

.field public static brand_gradient_end_60:I = 0x7f060082

.field public static brand_gradient_end_80:I = 0x7f060083

.field public static brand_gradient_start:I = 0x7f060084

.field public static brand_gradient_start_40:I = 0x7f060085

.field public static brand_gradient_start_60:I = 0x7f060086

.field public static brand_gradient_start_80:I = 0x7f060087

.field public static brand_light_00:I = 0x7f060088

.field public static brand_light_10:I = 0x7f060089

.field public static brand_light_20:I = 0x7f06008a

.field public static brand_light_30:I = 0x7f06008b

.field public static brand_light_40:I = 0x7f06008c

.field public static brand_light_50:I = 0x7f06008d

.field public static brand_light_60:I = 0x7f06008e

.field public static brand_light_70:I = 0x7f06008f

.field public static brand_light_80:I = 0x7f060090

.field public static brand_light_90:I = 0x7f060091

.field public static brand_new_50:I = 0x7f060092

.field public static brand_new_gradient_center:I = 0x7f060093

.field public static brand_new_gradient_center_50:I = 0x7f060094

.field public static brand_new_gradient_end:I = 0x7f060095

.field public static brand_new_gradient_end_50:I = 0x7f060096

.field public static brand_new_gradient_start:I = 0x7f060097

.field public static brand_new_gradient_start_50:I = 0x7f060098

.field public static brand_trans_10:I = 0x7f060099

.field public static brand_trans_16:I = 0x7f06009a

.field public static brand_trans_20:I = 0x7f06009b

.field public static btn_back:I = 0x7f0600a6

.field public static btn_bg_01:I = 0x7f0600a7

.field public static category_gradient_end:I = 0x7f0600b0

.field public static category_gradient_start:I = 0x7f0600b1

.field public static cl01:I = 0x7f0600ba

.field public static cl01_80:I = 0x7f0600bb

.field public static cl01_99:I = 0x7f0600bc

.field public static cl02:I = 0x7f0600be

.field public static cl03:I = 0x7f0600bf

.field public static cl05:I = 0x7f0600c0

.field public static cl11:I = 0x7f0600c1

.field public static cl12:I = 0x7f0600c2

.field public static cl17:I = 0x7f0600c3

.field public static cl31:I = 0x7f0600c4

.field public static cl31_25_p:I = 0x7f0600c5

.field public static cl31_30_p:I = 0x7f0600c6

.field public static cl31_50_p:I = 0x7f0600c7

.field public static cl31_70_p:I = 0x7f0600c8

.field public static cl31_86_p:I = 0x7f0600c9

.field public static cl32:I = 0x7f0600ca

.field public static cl32_ff_p:I = 0x7f0600cb

.field public static cl33:I = 0x7f0600cc

.field public static cl34:I = 0x7f0600cd

.field public static cl34_ff:I = 0x7f0600ce

.field public static cl35:I = 0x7f0600cf

.field public static cl35_40:I = 0x7f0600d0

.field public static cl36:I = 0x7f0600d1

.field public static cl37:I = 0x7f0600d2

.field public static cl38:I = 0x7f0600d3

.field public static cl38_16_p:I = 0x7f0600d4

.field public static cl38_20_p:I = 0x7f0600d5

.field public static cl38_30_p:I = 0x7f0600d6

.field public static cl38_40_p:I = 0x7f0600d7

.field public static cl38_50_p:I = 0x7f0600d8

.field public static cl38_60_p:I = 0x7f0600d9

.field public static cl38_80_p:I = 0x7f0600da

.field public static cl41:I = 0x7f0600db

.field public static cl42:I = 0x7f0600dc

.field public static cl43:I = 0x7f0600dd

.field public static cl44_95:I = 0x7f0600de

.field public static cl45:I = 0x7f0600df

.field public static cl45_30_p:I = 0x7f0600e0

.field public static cl45_50_p:I = 0x7f0600e1

.field public static cl_ic_download_home:I = 0x7f0600e2

.field public static cl_ic_search:I = 0x7f0600e3

.field public static color_00101114:I = 0x7f0600e6

.field public static color_07B84E:I = 0x7f0600e8

.field public static color_0ba7ff:I = 0x7f0600ea

.field public static color_111214:I = 0x7f0600eb

.field public static color_111214_0:I = 0x7f0600ec

.field public static color_171F2C:I = 0x7f0600ed

.field public static color_1fbdff_5p:I = 0x7f0600ef

.field public static color_28292E:I = 0x7f0600f0

.field public static color_2FF58B:I = 0x7f0600f1

.field public static color_2b3645:I = 0x7f0600f2

.field public static color_2e3b4b:I = 0x7f0600f3

.field public static color_31353D:I = 0x7f0600f4

.field public static color_31baf5:I = 0x7f0600f5

.field public static color_324042:I = 0x7f0600f6

.field public static color_333333:I = 0x7f0600f7

.field public static color_333333_40:I = 0x7f0600f8

.field public static color_383A40:I = 0x7f0600f9

.field public static color_3D3D3D:I = 0x7f0600fa

.field public static color_4c9ffe:I = 0x7f0600fb

.field public static color_4e5158:I = 0x7f0600fc

.field public static color_53f357:I = 0x7f0600fd

.field public static color_5f6167:I = 0x7f0600fe

.field public static color_610c08:I = 0x7f0600ff

.field public static color_664D01:I = 0x7f060100

.field public static color_666666:I = 0x7f060101

.field public static color_666972:I = 0x7f060102

.field public static color_7d7b81:I = 0x7f060104

.field public static color_92969E:I = 0x7f060105

.field public static color_999999:I = 0x7f060106

.field public static color_B2B2B2:I = 0x7f060108

.field public static color_DFF7E4:I = 0x7f06010a

.field public static color_EBFCFF:I = 0x7f06010c

.field public static color_EDF0F5:I = 0x7f06010d

.field public static color_F2F5FA:I = 0x7f06010e

.field public static color_F7F7F7:I = 0x7f06010f

.field public static color_FFEB2669:I = 0x7f060110

.field public static color_FFEB7AB7:I = 0x7f060111

.field public static color_FFFF3077:I = 0x7f060112

.field public static color_FFFF94CE:I = 0x7f060113

.field public static color_d6101114:I = 0x7f060114

.field public static color_e9f3ff:I = 0x7f060119

.field public static color_e9f9ff:I = 0x7f06011a

.field public static color_eeeeee:I = 0x7f06011b

.field public static color_f2774e:I = 0x7f06011c

.field public static color_f3f5fb:I = 0x7f06011d

.field public static color_fafafa:I = 0x7f06011e

.field public static color_ff6633:I = 0x7f060120

.field public static color_ffaa0f:I = 0x7f060122

.field public static color_ffaa33:I = 0x7f060123

.field public static color_ffd956_5p:I = 0x7f060124

.field public static color_fff42c5e:I = 0x7f060125

.field public static color_fffe8000:I = 0x7f060126

.field public static color_ffffbf00:I = 0x7f060127

.field public static color_score:I = 0x7f06012a

.field public static color_switch_close:I = 0x7f06012b

.field public static color_video_detail_episode_bg_normal:I = 0x7f06012e

.field public static color_video_detail_episode_bg_sel:I = 0x7f06012f

.field public static comment_input_bg:I = 0x7f060134

.field public static comment_like:I = 0x7f060136

.field public static comment_like_icon_end:I = 0x7f060137

.field public static comment_like_icon_start:I = 0x7f060138

.field public static common_white:I = 0x7f060145

.field public static dialog_line:I = 0x7f06016c

.field public static error_10:I = 0x7f060183

.field public static error_30:I = 0x7f060184

.field public static error_50:I = 0x7f060185

.field public static error_module:I = 0x7f060188

.field public static error_red:I = 0x7f060189

.field public static gray_0:I = 0x7f060194

.field public static gray_0_0:I = 0x7f060195

.field public static gray_0_1:I = 0x7f060196

.field public static gray_0_20:I = 0x7f060197

.field public static gray_0_60:I = 0x7f060198

.field public static gray_0_70:I = 0x7f060199

.field public static gray_0_80:I = 0x7f06019a

.field public static gray_10:I = 0x7f06019b

.field public static gray_20:I = 0x7f06019c

.field public static gray_30:I = 0x7f06019d

.field public static gray_40:I = 0x7f06019e

.field public static gray_50:I = 0x7f06019f

.field public static gray_60:I = 0x7f0601a0

.field public static gray_70:I = 0x7f0601a1

.field public static gray_80:I = 0x7f0601a2

.field public static gray_90:I = 0x7f0601a3

.field public static gray_dark_00:I = 0x7f0601a4

.field public static gray_dark_10:I = 0x7f0601a5

.field public static gray_dark_20:I = 0x7f0601a6

.field public static gray_dark_30:I = 0x7f0601a7

.field public static gray_dark_30_90:I = 0x7f0601a8

.field public static gray_dark_40:I = 0x7f0601a9

.field public static gray_dark_50:I = 0x7f0601aa

.field public static gray_dark_60:I = 0x7f0601ab

.field public static gray_dark_70:I = 0x7f0601ac

.field public static gray_dark_80:I = 0x7f0601ad

.field public static gray_dark_90:I = 0x7f0601ae

.field public static gray_light_00:I = 0x7f0601af

.field public static gray_light_10:I = 0x7f0601b0

.field public static gray_light_20:I = 0x7f0601b1

.field public static gray_light_30:I = 0x7f0601b2

.field public static gray_light_40:I = 0x7f0601b3

.field public static gray_light_50:I = 0x7f0601b4

.field public static gray_light_60:I = 0x7f0601b5

.field public static gray_light_70:I = 0x7f0601b6

.field public static gray_light_70_10:I = 0x7f0601b7

.field public static gray_light_80:I = 0x7f0601b8

.field public static gray_light_90:I = 0x7f0601b9

.field public static icon_play_bg:I = 0x7f0601c0

.field public static indicator_normal:I = 0x7f0601c3

.field public static indicator_selected:I = 0x7f0601c4

.field public static indicator_unselected:I = 0x7f0601c5

.field public static libui_main_btn_text_color_selector:I = 0x7f0601ca

.field public static libui_sub_btn2_text_color_selector:I = 0x7f0601cb

.field public static line_01:I = 0x7f0601cc

.field public static line_02:I = 0x7f0601cd

.field public static main:I = 0x7f06038d

.field public static main_10:I = 0x7f06038e

.field public static main_20:I = 0x7f06038f

.field public static main_gradient_center:I = 0x7f060390

.field public static main_gradient_center_40:I = 0x7f060391

.field public static main_gradient_center_color:I = 0x7f060392

.field public static main_gradient_end:I = 0x7f060393

.field public static main_gradient_end_40:I = 0x7f060394

.field public static main_gradient_start:I = 0x7f060395

.field public static main_gradient_start_40:I = 0x7f060396

.field public static module_01:I = 0x7f060478

.field public static module_02:I = 0x7f060479

.field public static module_03:I = 0x7f06047a

.field public static module_04:I = 0x7f06047b

.field public static module_05:I = 0x7f06047c

.field public static module_06:I = 0x7f06047d

.field public static module_07:I = 0x7f06047e

.field public static module_08:I = 0x7f06047f

.field public static module_09:I = 0x7f060480

.field public static module_10:I = 0x7f060481

.field public static module_dark_80:I = 0x7f060482

.field public static movie_detail_reviews_end:I = 0x7f060483

.field public static movie_detail_reviews_start:I = 0x7f060484

.field public static new_text_05:I = 0x7f0604bf

.field public static notice_40:I = 0x7f0604c3

.field public static pink_50:I = 0x7f0604d9

.field public static ps_link_star:I = 0x7f0604f5

.field public static rank_tag_color:I = 0x7f060653

.field public static skeleton:I = 0x7f06066b

.field public static text_01:I = 0x7f060682

.field public static text_02:I = 0x7f060683

.field public static text_03:I = 0x7f060684

.field public static text_04:I = 0x7f060685

.field public static text_05:I = 0x7f060686

.field public static text_06:I = 0x7f060687

.field public static text_07:I = 0x7f060688

.field public static text_08:I = 0x7f060689

.field public static text_09:I = 0x7f06068a

.field public static text_10:I = 0x7f06068b

.field public static text_11:I = 0x7f06068c

.field public static text_12:I = 0x7f06068d

.field public static transparent:I = 0x7f06069f

.field public static warn_no_network:I = 0x7f0606e6

.field public static warn_yellow:I = 0x7f0606e7

.field public static warning_50:I = 0x7f0606e8

.field public static whit_black_not_transform:I = 0x7f0606ea

.field public static whit_black_transform:I = 0x7f0606eb

.field public static whit_gary80_transform:I = 0x7f0606ec

.field public static white:I = 0x7f0606ed

.field public static white_0:I = 0x7f0606ee

.field public static white_10:I = 0x7f0606ef

.field public static white_100:I = 0x7f0606f0

.field public static white_16:I = 0x7f0606f1

.field public static white_20:I = 0x7f0606f2

.field public static white_30:I = 0x7f0606f3

.field public static white_40:I = 0x7f0606f4

.field public static white_50:I = 0x7f0606f5

.field public static white_6:I = 0x7f0606f6

.field public static white_60:I = 0x7f0606f7

.field public static white_70:I = 0x7f0606f8

.field public static white_80:I = 0x7f0606f9

.field public static white_90:I = 0x7f0606fa

.field public static white_ff_e:I = 0x7f0606fb

.field public static yellow_00:I = 0x7f0606fc

.field public static yellow_10:I = 0x7f0606fd

.field public static yellow_20:I = 0x7f0606fe

.field public static yellow_30:I = 0x7f0606ff

.field public static yellow_40:I = 0x7f060700

.field public static yellow_50:I = 0x7f060701

.field public static yellow_60:I = 0x7f060702

.field public static yellow_70:I = 0x7f060703

.field public static yellow_80:I = 0x7f060704

.field public static yellow_90:I = 0x7f060705

.field public static yellow_dark_00:I = 0x7f060706

.field public static yellow_dark_10:I = 0x7f060707

.field public static yellow_dark_20:I = 0x7f060708

.field public static yellow_dark_30:I = 0x7f060709

.field public static yellow_dark_40:I = 0x7f06070a

.field public static yellow_dark_50:I = 0x7f06070b

.field public static yellow_dark_60:I = 0x7f06070c

.field public static yellow_dark_70:I = 0x7f06070d

.field public static yellow_dark_80:I = 0x7f06070e

.field public static yellow_dark_90:I = 0x7f06070f

.field public static yellow_light_00:I = 0x7f060710

.field public static yellow_light_10:I = 0x7f060711

.field public static yellow_light_20:I = 0x7f060712

.field public static yellow_light_30:I = 0x7f060713

.field public static yellow_light_40:I = 0x7f060714

.field public static yellow_light_50:I = 0x7f060715

.field public static yellow_light_50_00:I = 0x7f060716

.field public static yellow_light_50_15:I = 0x7f060717

.field public static yellow_light_50_50:I = 0x7f060718

.field public static yellow_light_60:I = 0x7f060719

.field public static yellow_light_70:I = 0x7f06071a

.field public static yellow_light_80:I = 0x7f06071b

.field public static yellow_light_90:I = 0x7f06071c


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
