.class public final Lcom/tn/lib/widget/R$drawable;
.super Ljava/lang/Object;


# static fields
.field public static bg_blur:I = 0x7f0800c8

.field public static bg_brand_corner_4:I = 0x7f0800ca

.field public static bg_brand_linear_r6:I = 0x7f0800cb

.field public static bg_corner_tips_4:I = 0x7f0800e4

.field public static bg_corner_tips_rectangle_2:I = 0x7f0800e5

.field public static bg_go_to_setting:I = 0x7f0800f3

.field public static bg_music_corner_tips_4:I = 0x7f080120

.field public static bg_no_connection_restore_tip:I = 0x7f080122

.field public static bg_search_bg_01:I = 0x7f080159

.field public static bg_search_bg_02:I = 0x7f08015a

.field public static bg_toast:I = 0x7f080179

.field public static btn_gray:I = 0x7f080191

.field public static btn_state_view_bg:I = 0x7f080196

.field public static divider_flexbox:I = 0x7f0801c3

.field public static home_mask_ranking:I = 0x7f080241

.field public static ic_category_star:I = 0x7f08025c

.field public static ic_hot_white:I = 0x7f080287

.field public static ic_image_download:I = 0x7f080289

.field public static ic_location:I = 0x7f080291

.field public static ic_location_permission_tips:I = 0x7f080292

.field public static ic_mine_feedback:I = 0x7f080299

.field public static ic_mine_history:I = 0x7f08029a

.field public static ic_mine_mylist:I = 0x7f08029c

.field public static ic_mine_post:I = 0x7f08029d

.field public static ic_movie_download:I = 0x7f0802a2

.field public static ic_movie_like:I = 0x7f0802a3

.field public static ic_movie_share:I = 0x7f0802a4

.field public static ic_player_language:I = 0x7f0802af

.field public static ic_post_like:I = 0x7f0802b6

.field public static ic_post_share:I = 0x7f0802b7

.field public static ic_room_member:I = 0x7f0802c1

.field public static libui_bottom_dialog_bg:I = 0x7f0802ff

.field public static libui_bottom_dialog_bg_12dp:I = 0x7f080300

.field public static libui_bottom_dialog_bg_grey:I = 0x7f080301

.field public static libui_bottom_dialog_bg_shadow:I = 0x7f080302

.field public static libui_circle_grey_4dp:I = 0x7f080303

.field public static libui_circle_red_4dp:I = 0x7f080304

.field public static libui_common_bottom_dialog_bg:I = 0x7f080305

.field public static libui_common_dialog_bg:I = 0x7f080306

.field public static libui_ic_base_left:I = 0x7f080307

.field public static libui_ic_check_red_selector:I = 0x7f080308

.field public static libui_ic_check_selector:I = 0x7f080309

.field public static libui_ic_medium_arrow_right:I = 0x7f08030a

.field public static libui_join_1:I = 0x7f08030b

.field public static libui_join_2:I = 0x7f08030c

.field public static libui_join_3:I = 0x7f08030d

.field public static libui_join_4:I = 0x7f08030e

.field public static libui_main_btn_disable:I = 0x7f08030f

.field public static libui_main_btn_normal:I = 0x7f080310

.field public static libui_main_btn_pressed:I = 0x7f080311

.field public static libui_main_btn_selector:I = 0x7f080312

.field public static libui_mask_cl45_0p_to_30p:I = 0x7f080313

.field public static libui_mask_cl45_30p_to_0p:I = 0x7f080314

.field public static libui_mask_cl45_50p_to_0p:I = 0x7f080315

.field public static libui_message_tip:I = 0x7f080316

.field public static libui_shape_round_corner_black:I = 0x7f080317

.field public static libui_sub_btn2_disable:I = 0x7f080318

.field public static libui_sub_btn2_normal:I = 0x7f080319

.field public static libui_sub_btn2_pressed:I = 0x7f08031a

.field public static libui_sub_btn2_selector:I = 0x7f08031b

.field public static libui_sub_btn3_normal:I = 0x7f08031c

.field public static libui_sub_btn4_bg:I = 0x7f08031d

.field public static libui_sub_join_normal:I = 0x7f08031e

.field public static libui_sub_join_normal_tran:I = 0x7f08031f

.field public static liui_main_gradient_bg:I = 0x7f080322

.field public static liui_main_gradient_bg_40:I = 0x7f080323

.field public static liui_main_gradient_bg_50:I = 0x7f080324

.field public static liui_main_gradient_bg_8dp:I = 0x7f080325

.field public static liui_main_gradient_bg_8dp_40:I = 0x7f080326

.field public static opertaion_vertical_dialog_bg:I = 0x7f08043d

.field public static push_small_logo:I = 0x7f0804bb

.field public static room_ic_add:I = 0x7f0804c4


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
