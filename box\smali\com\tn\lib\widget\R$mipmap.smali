.class public final Lcom/tn/lib/widget/R$mipmap;
.super Ljava/lang/Object;


# static fields
.field public static bg_network_view:I = 0x7f0f001e

.field public static bg_no_network:I = 0x7f0f001f

.field public static guide_gif:I = 0x7f0f0039

.field public static ic_arrow_right:I = 0x7f0f004d

.field public static ic_arrow_right_white:I = 0x7f0f004e

.field public static ic_arrow_up_white:I = 0x7f0f0050

.field public static ic_btn_arrow_transparent:I = 0x7f0f0062

.field public static ic_history_clear:I = 0x7f0f0095

.field public static ic_home_join:I = 0x7f0f0096

.field public static ic_img_placeholder:I = 0x7f0f009a

.field public static ic_member:I = 0x7f0f00a4

.field public static ic_no_content:I = 0x7f0f00b4

.field public static ic_no_content_dark:I = 0x7f0f00b5

.field public static ic_no_error:I = 0x7f0f00b6

.field public static ic_no_network:I = 0x7f0f00b7

.field public static ic_no_network_tips_1:I = 0x7f0f00b8

.field public static ic_no_network_tips_2:I = 0x7f0f00b9

.field public static ic_no_network_tips_3:I = 0x7f0f00ba

.field public static ic_no_permission:I = 0x7f0f00bb

.field public static ic_play_round:I = 0x7f0f00d3

.field public static ic_play_transparent:I = 0x7f0f00d5

.field public static ic_poins_new:I = 0x7f0f00d9

.field public static ic_point_purchase:I = 0x7f0f00da

.field public static ic_post_like_selected:I = 0x7f0f00e0

.field public static ic_right_black:I = 0x7f0f00f8

.field public static ic_room_join_def:I = 0x7f0f00f9

.field public static ic_room_join_def_white:I = 0x7f0f00fa

.field public static ic_room_join_loading:I = 0x7f0f00fb

.field public static ic_room_join_view:I = 0x7f0f00fc

.field public static ic_transfer_tips_hint:I = 0x7f0f0137

.field public static ic_vip_20:I = 0x7f0f0141

.field public static icon_play_white:I = 0x7f0f0148

.field public static icon_white_back:I = 0x7f0f014f

.field public static info_player_ic_collapse:I = 0x7f0f015c

.field public static info_player_ic_expand:I = 0x7f0f015d

.field public static join_right:I = 0x7f0f0160

.field public static libui_ic_back_black:I = 0x7f0f0167

.field public static libui_ic_base_black_left:I = 0x7f0f0168

.field public static libui_ic_base_black_publish:I = 0x7f0f0169

.field public static libui_ic_base_black_share:I = 0x7f0f016a

.field public static libui_ic_base_whit_left:I = 0x7f0f016b

.field public static libui_ic_base_whit_publish:I = 0x7f0f016c

.field public static libui_ic_base_whit_share:I = 0x7f0f016d

.field public static libui_ic_basic_close_white:I = 0x7f0f016e

.field public static libui_ic_checked:I = 0x7f0f016f

.field public static libui_ic_close_dialog:I = 0x7f0f0170

.field public static libui_ic_down_more:I = 0x7f0f0171

.field public static libui_ic_edit:I = 0x7f0f0172

.field public static libui_ic_more:I = 0x7f0f0173

.field public static libui_ic_more_small_base_color:I = 0x7f0f0174

.field public static libui_ic_unchecked:I = 0x7f0f0175

.field public static libui_ic_up_hide:I = 0x7f0f0176

.field public static libui_iv_close_circle:I = 0x7f0f0177

.field public static libui_upload_error_image:I = 0x7f0f0178

.field public static loading_1_whit:I = 0x7f0f0179

.field public static loading_2_red:I = 0x7f0f017a

.field public static loading_3_gray:I = 0x7f0f017b

.field public static loading_green_dark:I = 0x7f0f017c

.field public static loading_green_light:I = 0x7f0f017d

.field public static login_arrow_down:I = 0x7f0f017e

.field public static logo_about_us:I = 0x7f0f0194

.field public static no_wifi_tip:I = 0x7f0f01cb

.field public static no_wifi_tip_big:I = 0x7f0f01cc

.field public static profile_camera:I = 0x7f0f01f4

.field public static profile_default_avatar:I = 0x7f0f01f5

.field public static profile_et_clear_fill:I = 0x7f0f01f7

.field public static profile_setting_right:I = 0x7f0f0200


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
