.class public final Lcom/tn/lib/widget/R$string;
.super Ljava/lang/Object;


# static fields
.field public static Enable:I = 0x7f120001

.field public static Join:I = 0x7f120002

.field public static Joined:I = 0x7f120003

.field public static Leave:I = 0x7f120004

.field public static View:I = 0x7f12000b

.field public static ad_no_network:I = 0x7f120033

.field public static all:I = 0x7f120040

.field public static also_known_as:I = 0x7f120043

.field public static common_failed:I = 0x7f1200c9

.field public static download_now:I = 0x7f120162

.field public static error_load_failed:I = 0x7f1201ec

.field public static failed_toast:I = 0x7f120229

.field public static game_center:I = 0x7f120274

.field public static go_to_setting:I = 0x7f12027a

.field public static home_fail_try_again:I = 0x7f120297

.field public static home_no_network_content:I = 0x7f120298

.field public static home_retry_text:I = 0x7f12029a

.field public static load_failed:I = 0x7f1202d4

.field public static location_permission_dialog_tips:I = 0x7f1202d6

.field public static location_permission_tips:I = 0x7f1202d7

.field public static loding:I = 0x7f1202d8

.field public static members:I = 0x7f1203cd

.field public static my_likes:I = 0x7f12044a

.field public static my_posts:I = 0x7f12044b

.field public static no_connection:I = 0x7f120464

.field public static no_connection_restore_content_1:I = 0x7f120465

.field public static no_connection_restore_content_2:I = 0x7f120466

.field public static no_connection_restore_content_3:I = 0x7f120467

.field public static no_connection_restore_title:I = 0x7f120468

.field public static no_content:I = 0x7f120469

.field public static no_error_content:I = 0x7f12046a

.field public static no_network:I = 0x7f12046c

.field public static no_network_tips:I = 0x7f12046d

.field public static no_network_title:I = 0x7f12046e

.field public static no_network_toast:I = 0x7f12046f

.field public static no_result_default:I = 0x7f120470

.field public static on_line_now:I = 0x7f120493

.field public static player_copy_link:I = 0x7f1204df

.field public static player_hide:I = 0x7f1204e4

.field public static player_more:I = 0x7f1204e5

.field public static player_share_friends:I = 0x7f1204e9

.field public static sources:I = 0x7f1205ee

.field public static submit:I = 0x7f120640

.field public static tab_downloads:I = 0x7f12066e

.field public static tab_home:I = 0x7f12066f

.field public static tab_me:I = 0x7f120670

.field public static tab_member:I = 0x7f120671

.field public static tab_short_tv:I = 0x7f120672

.field public static tab_video:I = 0x7f120673

.field public static try_again:I = 0x7f1206f0

.field public static watch_later:I = 0x7f1207d8


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
