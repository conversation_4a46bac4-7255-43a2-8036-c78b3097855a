.class public final Lcom/tn/lib/widget/R$styleable;
.super Ljava/lang/Object;


# static fields
.field public static AVLoadingIndicatorView:[I = null

.field public static AVLoadingIndicatorView_indicator:I = 0x0

.field public static AVLoadingIndicatorView_indicator_color:I = 0x1

.field public static AfRecyclerView:[I = null

.field public static AfRecyclerView_arv_connect_tips:I = 0x0

.field public static AfRecyclerView_isChatting:I = 0x1

.field public static AppEmptyView:[I = null

.field public static AppEmptyView_empty_icon:I = 0x0

.field public static AppEmptyView_empty_icon_margin_bottom:I = 0x1

.field public static AppEmptyView_empty_icon_margin_top:I = 0x2

.field public static AppEmptyView_empty_refresh_height:I = 0x3

.field public static AppEmptyView_empty_refresh_icon:I = 0x4

.field public static AppEmptyView_empty_refresh_icon_margin_end:I = 0x5

.field public static AppEmptyView_empty_refresh_text:I = 0x6

.field public static AppEmptyView_empty_refresh_text_color:I = 0x7

.field public static AppEmptyView_empty_refresh_text_size:I = 0x8

.field public static AppEmptyView_empty_refresh_width:I = 0x9

.field public static AppEmptyView_empty_text:I = 0xa

.field public static AppEmptyView_empty_text_color:I = 0xb

.field public static AppEmptyView_empty_text_margin_bottom:I = 0xc

.field public static AppEmptyView_empty_text_margin_top:I = 0xd

.field public static AppEmptyView_empty_text_size:I = 0xe

.field public static AppEmptyView_empty_type:I = 0xf

.field public static Base_YuanProgressBar:[I = null

.field public static Base_YuanProgressBar_base_circleColor:I = 0x0

.field public static Base_YuanProgressBar_base_insideColor:I = 0x1

.field public static Base_YuanProgressBar_base_yuanCircleStrokeWidth:I = 0x2

.field public static Base_YuanProgressBar_base_yuanProgressStrokeWidth:I = 0x3

.field public static Base_YuanProgressBar_base_yuan_progressColor:I = 0x4

.field public static BubbleView:[I = null

.field public static BubbleView_angle:I = 0x0

.field public static BubbleView_arrowCenter:I = 0x1

.field public static BubbleView_arrowHeight:I = 0x2

.field public static BubbleView_arrowLocation:I = 0x3

.field public static BubbleView_arrowPosition:I = 0x4

.field public static BubbleView_arrowWidth:I = 0x5

.field public static BubbleView_bubbleAlpha:I = 0x6

.field public static BubbleView_bubbleColor:I = 0x7

.field public static BubbleView_gradientCenterColor:I = 0x8

.field public static BubbleView_gradientEndColor:I = 0x9

.field public static BubbleView_gradientStartColor:I = 0xa

.field public static BubbleView_gradient_orientation:I = 0xb

.field public static BubbleView_isOpenAnimator:I = 0xc

.field public static BubbleView_shadowColor:I = 0xd

.field public static BubbleView_shadowOffsetX:I = 0xe

.field public static BubbleView_shadowOffsetY:I = 0xf

.field public static BubbleView_shadowRadius:I = 0x10

.field public static CircleProgressbar:[I = null

.field public static CircleProgressbar_progressBgColor:I = 0x0

.field public static CircleProgressbar_progressCurrent:I = 0x1

.field public static CircleProgressbar_progressMax:I = 0x2

.field public static CircleProgressbar_progressRadius:I = 0x3

.field public static CircleProgressbar_progressRingsColor:I = 0x4

.field public static CircleProgressbar_progressStrokesWidth:I = 0x5

.field public static CircleProgressbar_progressTextsColor:I = 0x6

.field public static CircleProgressbar_ra:I = 0x7

.field public static CircleProgressbar_ringsColor:I = 0x8

.field public static CircleProgressbar_strokesWidth:I = 0x9

.field public static CircleProgressbar_textsColor:I = 0xa

.field public static CornerTextView:[I = null

.field public static CornerTextView_rectangleShape:I = 0x0

.field public static DrawableIndicator:[I = null

.field public static DrawableIndicator_normal_drawable:I = 0x0

.field public static DrawableIndicator_selected_drawable:I = 0x1

.field public static ExpandView:[I = null

.field public static ExpandView_pop_Init_state:I = 0x0

.field public static ExpandView_pop_collapse_gap:I = 0x1

.field public static ExpandView_pop_ellipsis:I = 0x2

.field public static ExpandView_pop_expand_bg_Color:I = 0x3

.field public static ExpandView_pop_expand_gap:I = 0x4

.field public static ExpandView_pop_expand_hint:I = 0x5

.field public static ExpandView_pop_expand_hint_color:I = 0x6

.field public static ExpandView_pop_hash_tag_color:I = 0x7

.field public static ExpandView_pop_hint_text_size:I = 0x8

.field public static ExpandView_pop_reverse_Lines:I = 0x9

.field public static ExpandView_pop_show_expand_hint:I = 0xa

.field public static ExpandView_pop_show_hint_icon:I = 0xb

.field public static ExpandView_pop_show_shrink_hint:I = 0xc

.field public static ExpandView_pop_shrink_bg_color:I = 0xd

.field public static ExpandView_pop_shrink_hint:I = 0xe

.field public static ExpandView_pop_shrink_hint_color:I = 0xf

.field public static ExpandView_pop_toggle_enabled:I = 0x10

.field public static FlowLayout:[I = null

.field public static FlowLayout_android_gravity:I = 0x0

.field public static FlowLayout_flChildSpacing:I = 0x1

.field public static FlowLayout_flChildSpacingForLastRow:I = 0x2

.field public static FlowLayout_flFlow:I = 0x3

.field public static FlowLayout_flMaxRows:I = 0x4

.field public static FlowLayout_flMinChildSpacing:I = 0x5

.field public static FlowLayout_flRowSpacing:I = 0x6

.field public static FlowLayout_flRowVerticalGravity:I = 0x7

.field public static FlowLayout_flRtl:I = 0x8

.field public static FlowLayout_itemSpacing:I = 0x9

.field public static FlowLayout_lineSpacing:I = 0xa

.field public static MaxHeightNestedScrollView:[I = null

.field public static MaxHeightNestedScrollView_scroll_maxHeight:I = 0x0

.field public static RoomJoinAnimationView:[I = null

.field public static RoomJoinAnimationView_jv_style:I = 0x0

.field public static RoomJoinAnimationView_jv_text_join_bg:I = 0x1

.field public static RoomJoinAnimationView_jv_text_join_text_color:I = 0x2

.field public static RoomJoinAnimationView_jv_text_joined_bg:I = 0x3

.field public static RoomJoinAnimationView_jv_text_joined_text_color:I = 0x4

.field public static RoomJoinAnimationView_jv_text_loading_bg:I = 0x5

.field public static RoomJoinAnimationView_jv_text_loading_color:I = 0x6

.field public static RoomJoinAnimationView_jv_text_loading_size:I = 0x7

.field public static RoomJoinAnimationView_jv_text_text_size:I = 0x8

.field public static RoundedArrowImageView:[I = null

.field public static RoundedArrowImageView_arrowIVHeight:I = 0x0

.field public static RoundedArrowImageView_arrowIVPositionOffset:I = 0x1

.field public static RoundedArrowImageView_arrowIVWidth:I = 0x2

.field public static RoundedArrowImageView_cornerRadius:I = 0x3

.field public static SecondariesSeekBar:[I = null

.field public static SecondariesSeekBar_ssb_bar_center_color:I = 0x0

.field public static SecondariesSeekBar_ssb_bar_end_color:I = 0x1

.field public static SecondariesSeekBar_ssb_bar_start_color:I = 0x2

.field public static SecondariesSeekBar_ssb_bg_color:I = 0x3

.field public static SecondariesSeekBar_ssb_max:I = 0x4

.field public static SecondariesSeekBar_ssb_progress:I = 0x5

.field public static SecondariesSeekBar_ssb_progress_size:I = 0x6

.field public static SecondariesSeekBar_ssb_secondaries_color:I = 0x7

.field public static SecondariesSeekBar_ssb_seek_enable:I = 0x8

.field public static SecondariesSeekBar_ssb_thumb_color:I = 0x9

.field public static SecondariesSeekBar_ssb_thumb_enlarge:I = 0xa

.field public static SecondariesSeekBar_ssb_thumb_size:I = 0xb

.field public static StateView:[I = null

.field public static StateView_screen_type:I = 0x0

.field public static SwitchView:[I = null

.field public static SwitchView_switch_ball_color:I = 0x0

.field public static SwitchView_switch_bg_color:I = 0x1

.field public static SwitchView_switch_checked_bg_color:I = 0x2

.field public static SwitchView_switch_gradient_end_color:I = 0x3

.field public static SwitchView_switch_gradient_start_color:I = 0x4

.field public static SwitchView_switch_radius:I = 0x5

.field public static TitleLayout:[I = null

.field public static TitleLayout_backIconRes:I = 0x0

.field public static TitleLayout_isShowBack:I = 0x1

.field public static TitleLayout_menuRes:I = 0x2

.field public static TitleLayout_showLine:I = 0x3

.field public static TitleLayout_titleBackgroundColor:I = 0x4

.field public static TitleLayout_titleGravity:I = 0x5

.field public static TitleLayout_titleText:I = 0x6

.field public static TitleLayout_titleTextColor:I = 0x7

.field public static TitleLayout_titleTextSize:I = 0x8

.field public static advrecyclerview:[I = null

.field public static advrecyclerview_adv_layout_empty:I = 0x0

.field public static advrecyclerview_adv_layout_error:I = 0x1

.field public static advrecyclerview_adv_layout_progress:I = 0x2

.field public static advstateview:[I = null

.field public static advstateview_layout_empty:I = 0x0

.field public static advstateview_layout_error:I = 0x1

.field public static advstateview_layout_progress:I = 0x2

.field public static download_view:[I = null

.field public static download_view_iconSrc:I = 0x0

.field public static download_view_tips_textColor:I = 0x1

.field public static download_view_tips_textSize:I = 0x2

.field public static download_view_tips_textVisibility:I = 0x3


# direct methods
.method public static constructor <clinit>()V
    .locals 5

    const v0, 0x7f0403e5

    const v1, 0x7f0403ec

    filled-new-array {v0, v1}, [I

    move-result-object v0

    sput-object v0, Lcom/tn/lib/widget/R$styleable;->AVLoadingIndicatorView:[I

    const v0, 0x7f04005b

    const v1, 0x7f0403f8

    filled-new-array {v0, v1}, [I

    move-result-object v0

    sput-object v0, Lcom/tn/lib/widget/R$styleable;->AfRecyclerView:[I

    const/16 v0, 0x10

    new-array v0, v0, [I

    fill-array-data v0, :array_0

    sput-object v0, Lcom/tn/lib/widget/R$styleable;->AppEmptyView:[I

    const v0, 0x7f0400a6

    const v1, 0x7f0400a7

    const v2, 0x7f0400a3

    const v3, 0x7f0400a4

    const v4, 0x7f0400a5

    filled-new-array {v2, v3, v4, v0, v1}, [I

    move-result-object v0

    sput-object v0, Lcom/tn/lib/widget/R$styleable;->Base_YuanProgressBar:[I

    const/16 v0, 0x11

    new-array v1, v0, [I

    fill-array-data v1, :array_1

    sput-object v1, Lcom/tn/lib/widget/R$styleable;->BubbleView:[I

    const/16 v1, 0xb

    new-array v2, v1, [I

    fill-array-data v2, :array_2

    sput-object v2, Lcom/tn/lib/widget/R$styleable;->CircleProgressbar:[I

    const v2, 0x7f040601

    filled-new-array {v2}, [I

    move-result-object v2

    sput-object v2, Lcom/tn/lib/widget/R$styleable;->CornerTextView:[I

    const v2, 0x7f040588

    const v3, 0x7f040634

    filled-new-array {v2, v3}, [I

    move-result-object v2

    sput-object v2, Lcom/tn/lib/widget/R$styleable;->DrawableIndicator:[I

    new-array v0, v0, [I

    fill-array-data v0, :array_3

    sput-object v0, Lcom/tn/lib/widget/R$styleable;->ExpandView:[I

    new-array v0, v1, [I

    fill-array-data v0, :array_4

    sput-object v0, Lcom/tn/lib/widget/R$styleable;->FlowLayout:[I

    const v0, 0x7f040625

    filled-new-array {v0}, [I

    move-result-object v0

    sput-object v0, Lcom/tn/lib/widget/R$styleable;->MaxHeightNestedScrollView:[I

    const/16 v0, 0x9

    new-array v1, v0, [I

    fill-array-data v1, :array_5

    sput-object v1, Lcom/tn/lib/widget/R$styleable;->RoomJoinAnimationView:[I

    const v1, 0x7f040055

    const v2, 0x7f0402be

    const v3, 0x7f040053

    const v4, 0x7f040054

    filled-new-array {v3, v4, v1, v2}, [I

    move-result-object v1

    sput-object v1, Lcom/tn/lib/widget/R$styleable;->RoundedArrowImageView:[I

    const/16 v1, 0xc

    new-array v1, v1, [I

    fill-array-data v1, :array_6

    sput-object v1, Lcom/tn/lib/widget/R$styleable;->SecondariesSeekBar:[I

    const v1, 0x7f040621

    filled-new-array {v1}, [I

    move-result-object v1

    sput-object v1, Lcom/tn/lib/widget/R$styleable;->StateView:[I

    const/4 v1, 0x6

    new-array v1, v1, [I

    fill-array-data v1, :array_7

    sput-object v1, Lcom/tn/lib/widget/R$styleable;->SwitchView:[I

    new-array v0, v0, [I

    fill-array-data v0, :array_8

    sput-object v0, Lcom/tn/lib/widget/R$styleable;->TitleLayout:[I

    const v0, 0x7f040037

    const v1, 0x7f040038

    const v2, 0x7f040036

    filled-new-array {v2, v0, v1}, [I

    move-result-object v0

    sput-object v0, Lcom/tn/lib/widget/R$styleable;->advrecyclerview:[I

    const v0, 0x7f0404a2

    const v1, 0x7f0404b6

    const v2, 0x7f0404a1

    filled-new-array {v2, v0, v1}, [I

    move-result-object v0

    sput-object v0, Lcom/tn/lib/widget/R$styleable;->advstateview:[I

    const v0, 0x7f04075e

    const v1, 0x7f04075f

    const v2, 0x7f0403d5

    const v3, 0x7f04075d

    filled-new-array {v2, v3, v0, v1}, [I

    move-result-object v0

    sput-object v0, Lcom/tn/lib/widget/R$styleable;->download_view:[I

    return-void

    nop

    :array_0
    .array-data 4
        0x7f04031d
        0x7f04031e
        0x7f04031f
        0x7f040320
        0x7f040321
        0x7f040322
        0x7f040323
        0x7f040324
        0x7f040325
        0x7f040326
        0x7f040327
        0x7f040328
        0x7f040329
        0x7f04032a
        0x7f04032b
        0x7f04032c
    .end array-data

    :array_1
    .array-data 4
        0x7f040045
        0x7f040050
        0x7f040052
        0x7f040056
        0x7f040057
        0x7f040059
        0x7f0401db
        0x7f0401dc
        0x7f0403ae
        0x7f0403af
        0x7f0403b1
        0x7f0403b5
        0x7f0403fe
        0x7f040638
        0x7f040639
        0x7f04063a
        0x7f04063b
    .end array-data

    :array_2
    .array-data 4
        0x7f0405df
        0x7f0405e0
        0x7f0405e1
        0x7f0405e2
        0x7f0405e3
        0x7f0405e4
        0x7f0405e5
        0x7f0405ed
        0x7f040614
        0x7f0406be
        0x7f04073a
    .end array-data

    :array_3
    .array-data 4
        0x7f0405c0
        0x7f0405c1
        0x7f0405c2
        0x7f0405c3
        0x7f0405c4
        0x7f0405c5
        0x7f0405c6
        0x7f0405c7
        0x7f0405c8
        0x7f0405c9
        0x7f0405ca
        0x7f0405cb
        0x7f0405cc
        0x7f0405cd
        0x7f0405ce
        0x7f0405cf
        0x7f0405d0
    .end array-data

    :array_4
    .array-data 4
        0x10100af
        0x7f04036d
        0x7f04036e
        0x7f04036f
        0x7f040370
        0x7f040371
        0x7f040372
        0x7f040373
        0x7f040374
        0x7f04043c
        0x7f0404c4
    .end array-data

    :array_5
    .array-data 4
        0x7f04044d
        0x7f04044e
        0x7f04044f
        0x7f040450
        0x7f040451
        0x7f040452
        0x7f040453
        0x7f040454
        0x7f040455
    .end array-data

    :array_6
    .array-data 4
        0x7f040695
        0x7f040696
        0x7f040697
        0x7f040698
        0x7f040699
        0x7f04069a
        0x7f04069b
        0x7f04069c
        0x7f04069d
        0x7f04069e
        0x7f04069f
        0x7f0406a0
    .end array-data

    :array_7
    .array-data 4
        0x7f0406d4
        0x7f0406d5
        0x7f0406d6
        0x7f0406d7
        0x7f0406d8
        0x7f0406d9
    .end array-data

    :array_8
    .array-data 4
        0x7f040069
        0x7f0403ff
        0x7f04053c
        0x7f040665
        0x7f040761
        0x7f040765
        0x7f04076d
        0x7f04076f
        0x7f040771
    .end array-data
.end method

.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
