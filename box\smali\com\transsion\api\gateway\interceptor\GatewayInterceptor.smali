.class public Lcom/transsion/api/gateway/interceptor/GatewayInterceptor;
.super Ljava/lang/Object;

# interfaces
.implements Lokhttp3/t;


# instance fields
.field public isReadResponse:Z

.field public mResponse:Lokhttp3/y;

.field public startNs:J


# direct methods
.method public constructor <init>()V
    .locals 1

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, 0x0

    iput-boolean v0, p0, Lcom/transsion/api/gateway/interceptor/GatewayInterceptor;->isReadResponse:Z

    const/4 v0, 0x0

    iput-object v0, p0, Lcom/transsion/api/gateway/interceptor/GatewayInterceptor;->mResponse:Lokhttp3/y;

    return-void
.end method

.method private changeRequestUrl(Lokhttp3/t$a;Lcom/transsion/api/gateway/bean/GatewayStrategy;Lcom/transsion/api/gateway/analytics/a;)Lokhttp3/w;
    .locals 2

    invoke-interface {p1}, Lokhttp3/t$a;->request()Lokhttp3/w;

    move-result-object p1

    if-nez p2, :cond_0

    return-object p1

    :cond_0
    invoke-virtual {p1}, Lokhttp3/w;->k()Lokhttp3/s;

    move-result-object v0

    invoke-virtual {v0}, Lokhttp3/s;->k()Lokhttp3/s$a;

    move-result-object v0

    invoke-static {}, Lcom/transsion/api/gateway/GateWaySdk;->getHost()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Lokhttp3/s$a;->h(Ljava/lang/String;)Lokhttp3/s$a;

    move-result-object v0

    invoke-virtual {v0}, Lokhttp3/s$a;->c()Lokhttp3/s;

    move-result-object v0

    invoke-virtual {p2}, Lcom/transsion/api/gateway/bean/GatewayStrategy;->isUseOriginHost()Z

    move-result v1

    if-eqz v1, :cond_1

    iget-object v1, p3, Lcom/transsion/api/gateway/analytics/a;->i:Ljava/lang/String;

    invoke-static {v1}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v1

    if-nez v1, :cond_1

    invoke-virtual {v0}, Lokhttp3/s;->k()Lokhttp3/s$a;

    move-result-object v0

    iget-object v1, p3, Lcom/transsion/api/gateway/analytics/a;->i:Ljava/lang/String;

    invoke-virtual {v0, v1}, Lokhttp3/s$a;->h(Ljava/lang/String;)Lokhttp3/s$a;

    move-result-object v0

    invoke-virtual {v0}, Lokhttp3/s$a;->c()Lokhttp3/s;

    move-result-object v0

    :cond_1
    invoke-virtual {p2}, Lcom/transsion/api/gateway/bean/GatewayStrategy;->isNoDns()Z

    move-result v1

    iput-boolean v1, p3, Lcom/transsion/api/gateway/analytics/a;->c:Z

    invoke-virtual {p1}, Lokhttp3/w;->i()Lokhttp3/w$a;

    move-result-object p1

    invoke-virtual {p1, v0}, Lokhttp3/w$a;->o(Lokhttp3/s;)Lokhttp3/w$a;

    move-result-object p1

    invoke-virtual {p1}, Lokhttp3/w$a;->b()Lokhttp3/w;

    move-result-object p1

    invoke-virtual {p1}, Lokhttp3/w;->i()Lokhttp3/w$a;

    move-result-object p1

    invoke-virtual {p2}, Lcom/transsion/api/gateway/bean/GatewayStrategy;->isUseOriginHost()Z

    move-result v0

    if-nez v0, :cond_2

    iget-object v0, p3, Lcom/transsion/api/gateway/analytics/a;->i:Ljava/lang/String;

    invoke-static {v0}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    if-nez v0, :cond_2

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    iget-object p3, p3, Lcom/transsion/api/gateway/analytics/a;->i:Ljava/lang/String;

    invoke-virtual {v0, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string p3, ":443"

    invoke-virtual {v0, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p3

    const-string v0, "Host"

    invoke-virtual {p1, v0, p3}, Lokhttp3/w$a;->a(Ljava/lang/String;Ljava/lang/String;)Lokhttp3/w$a;

    invoke-static {}, Lcom/transsion/api/gateway/dns/GateWayDns;->getInstance()Lcom/transsion/api/gateway/dns/GateWayDns;

    move-result-object p3

    invoke-virtual {p2}, Lcom/transsion/api/gateway/bean/GatewayStrategy;->isNoDns()Z

    move-result p2

    invoke-virtual {p3, p2}, Lcom/transsion/api/gateway/dns/GateWayDns;->setIsUseGatewayDns(Z)V

    :cond_2
    invoke-virtual {p1}, Lokhttp3/w$a;->b()Lokhttp3/w;

    move-result-object p1

    return-object p1
.end method

.method private doGatewayJob(Lokhttp3/t$a;Lcom/transsion/api/gateway/analytics/a;)Lokhttp3/y;
    .locals 13
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    sget-object v0, Lcom/transsion/api/gateway/utils/GatewayUtils;->L:Lcom/transsion/api/gateway/utils/ObjectLogUtils;

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, " gateway request\uff1a"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-wide v2, p0, Lcom/transsion/api/gateway/interceptor/GatewayInterceptor;->startNs:J

    invoke-virtual {v1, v2, v3}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Lcom/transsion/api/gateway/utils/ObjectLogUtils;->d(Ljava/lang/Object;)V

    invoke-static {}, Lcom/transsion/api/gateway/config/b;->b()Lcom/transsion/api/gateway/config/b;

    move-result-object v0

    iget-object v0, v0, Lcom/transsion/api/gateway/config/b;->c:Lcom/transsion/api/gateway/config/a;

    iget-object v0, v0, Lcom/transsion/api/gateway/config/a;->k:Ljava/util/List;

    const/4 v1, 0x0

    invoke-interface {v0, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/transsion/api/gateway/bean/GatewayStrategy;

    invoke-direct {p0, p1, v2, p2}, Lcom/transsion/api/gateway/interceptor/GatewayInterceptor;->changeRequestUrl(Lokhttp3/t$a;Lcom/transsion/api/gateway/bean/GatewayStrategy;Lcom/transsion/api/gateway/analytics/a;)Lokhttp3/w;

    move-result-object v2

    invoke-virtual {v2}, Lokhttp3/w;->k()Lokhttp3/s;

    move-result-object v2

    invoke-virtual {v2}, Lokhttp3/s;->i()Ljava/lang/String;

    move-result-object v2

    iput-object v2, p2, Lcom/transsion/api/gateway/analytics/a;->f:Ljava/lang/String;

    const/4 v2, 0x1

    :try_start_0
    invoke-interface {p1}, Lokhttp3/t$a;->request()Lokhttp3/w;

    move-result-object v3

    invoke-direct {p0, v3}, Lcom/transsion/api/gateway/interceptor/GatewayInterceptor;->doGzipOrSign(Lokhttp3/w;)Lokhttp3/w;

    move-result-object v3

    invoke-interface {p1, v3}, Lokhttp3/t$a;->a(Lokhttp3/w;)Lokhttp3/y;

    move-result-object v3
    :try_end_0
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception v3

    sget-object v4, Lcom/transsion/api/gateway/utils/GatewayUtils;->L:Lcom/transsion/api/gateway/utils/ObjectLogUtils;

    new-instance v5, Ljava/lang/StringBuilder;

    invoke-direct {v5}, Ljava/lang/StringBuilder;-><init>()V

    const-string v6, "request fail, reason:"

    invoke-virtual {v5, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v5, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    invoke-virtual {v5}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v5

    invoke-virtual {v4, v5}, Lcom/transsion/api/gateway/utils/ObjectLogUtils;->e(Ljava/lang/Object;)V

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v4

    if-le v4, v2, :cond_4

    const/4 v3, 0x0

    :goto_0
    invoke-direct {p0, v3}, Lcom/transsion/api/gateway/interceptor/GatewayInterceptor;->getTimeOffset(Lokhttp3/y;)J

    move-result-wide v4

    const-wide/16 v6, 0x0

    cmp-long v8, v4, v6

    if-lez v8, :cond_1

    invoke-static {}, Lcom/transsion/api/gateway/utils/ContextUtils;->getContext()Landroid/content/Context;

    move-result-object v6

    invoke-static {v6}, Lcom/transsion/api/gateway/utils/SafeStringUtils;->getInstance(Landroid/content/Context;)Lcom/transsion/api/gateway/utils/SafeStringUtils;

    move-result-object v6

    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v7

    sub-long/2addr v4, v7

    const-string v7, "time_offset"

    invoke-virtual {v6, v7, v4, v5}, Lcom/transsion/api/gateway/utils/SafeStringUtils;->saveLong(Ljava/lang/String;J)V

    :try_start_1
    invoke-interface {p1}, Lokhttp3/t$a;->request()Lokhttp3/w;

    move-result-object v4

    invoke-direct {p0, v4}, Lcom/transsion/api/gateway/interceptor/GatewayInterceptor;->doGzipOrSign(Lokhttp3/w;)Lokhttp3/w;

    move-result-object v4

    invoke-interface {p1, v4}, Lokhttp3/t$a;->a(Lokhttp3/w;)Lokhttp3/y;

    move-result-object v3
    :try_end_1
    .catch Ljava/lang/Exception; {:try_start_1 .. :try_end_1} :catch_1

    goto :goto_1

    :catch_1
    move-exception v4

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v5

    if-le v5, v2, :cond_0

    goto :goto_1

    :cond_0
    invoke-virtual {v4}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-direct {p0, p1, p2}, Lcom/transsion/api/gateway/interceptor/GatewayInterceptor;->setRequestResult(Ljava/lang/String;Lcom/transsion/api/gateway/analytics/a;)V

    throw v4

    :cond_1
    :goto_1
    move-object v4, p0

    :goto_2
    if-nez v3, :cond_3

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v5

    sub-int/2addr v5, v2

    if-ge v1, v5, :cond_3

    add-int/lit8 v1, v1, 0x1

    invoke-interface {v0, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Lcom/transsion/api/gateway/bean/GatewayStrategy;

    invoke-direct {v4, p1, v5, p2}, Lcom/transsion/api/gateway/interceptor/GatewayInterceptor;->changeRequestUrl(Lokhttp3/t$a;Lcom/transsion/api/gateway/bean/GatewayStrategy;Lcom/transsion/api/gateway/analytics/a;)Lokhttp3/w;

    move-result-object v5

    iput v1, p2, Lcom/transsion/api/gateway/analytics/a;->j:I

    :try_start_2
    invoke-direct {v4, v5}, Lcom/transsion/api/gateway/interceptor/GatewayInterceptor;->doGzipOrSign(Lokhttp3/w;)Lokhttp3/w;

    move-result-object v5

    invoke-interface {p1, v5}, Lokhttp3/t$a;->a(Lokhttp3/w;)Lokhttp3/y;

    move-result-object v3
    :try_end_2
    .catch Ljava/io/IOException; {:try_start_2 .. :try_end_2} :catch_2

    goto :goto_2

    :catch_2
    move-exception v5

    sget-object v6, Lcom/transsion/api/gateway/utils/GatewayUtils;->L:Lcom/transsion/api/gateway/utils/ObjectLogUtils;

    const-string v7, "gateway request fail  "

    invoke-virtual {v6, v7}, Lcom/transsion/api/gateway/utils/ObjectLogUtils;->d(Ljava/lang/Object;)V

    sget-object v6, Lcom/transsion/api/gateway/utils/GatewayUtils;->L:Lcom/transsion/api/gateway/utils/ObjectLogUtils;

    new-instance v7, Ljava/lang/StringBuilder;

    invoke-direct {v7}, Ljava/lang/StringBuilder;-><init>()V

    const-string v8, "gateway request count: "

    invoke-virtual {v7, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v7, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v8, "reason"

    invoke-virtual {v7, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v5}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v9

    invoke-virtual {v7, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v9, "duration"

    invoke-virtual {v7, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v9

    iget-wide v11, v4, Lcom/transsion/api/gateway/interceptor/GatewayInterceptor;->startNs:J

    sub-long/2addr v9, v11

    invoke-virtual {v7, v9, v10}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    invoke-virtual {v7}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v7

    invoke-virtual {v6, v7}, Lcom/transsion/api/gateway/utils/ObjectLogUtils;->d(Ljava/lang/Object;)V

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v6

    sub-int/2addr v6, v2

    if-ge v1, v6, :cond_2

    goto :goto_2

    :cond_2
    sget-object p1, Lcom/transsion/api/gateway/utils/GatewayUtils;->L:Lcom/transsion/api/gateway/utils/ObjectLogUtils;

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "gateway request count: : "

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v0, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v5}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p1, v0}, Lcom/transsion/api/gateway/utils/ObjectLogUtils;->d(Ljava/lang/Object;)V

    invoke-virtual {v5}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-direct {v4, p1, p2}, Lcom/transsion/api/gateway/interceptor/GatewayInterceptor;->setRequestResult(Ljava/lang/String;Lcom/transsion/api/gateway/analytics/a;)V

    throw v5

    :cond_3
    invoke-direct {v4, v3, p2}, Lcom/transsion/api/gateway/interceptor/GatewayInterceptor;->recordResponse(Lokhttp3/y;Lcom/transsion/api/gateway/analytics/a;)V

    sget-object p1, Lcom/transsion/api/gateway/utils/GatewayUtils;->L:Lcom/transsion/api/gateway/utils/ObjectLogUtils;

    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v0, " gateway request success, duration\uff1a"

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v5

    iget-wide v7, v4, Lcom/transsion/api/gateway/interceptor/GatewayInterceptor;->startNs:J

    sub-long/2addr v5, v7

    invoke-virtual {p2, v5, v6}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    const-string v0, "gateway retry count"

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p2, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p1, p2}, Lcom/transsion/api/gateway/utils/ObjectLogUtils;->d(Ljava/lang/Object;)V

    return-object v3

    :cond_4
    invoke-virtual {v3}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-direct {p0, p1, p2}, Lcom/transsion/api/gateway/interceptor/GatewayInterceptor;->setRequestResult(Ljava/lang/String;Lcom/transsion/api/gateway/analytics/a;)V

    throw v3
.end method

.method private doGzipOrSign(Lokhttp3/w;)Lokhttp3/w;
    .locals 19

    sget-object v0, Lcom/transsion/api/gateway/utils/GatewayUtils;->L:Lcom/transsion/api/gateway/utils/ObjectLogUtils;

    const-string v1, "do sign"

    invoke-virtual {v0, v1}, Lcom/transsion/api/gateway/utils/ObjectLogUtils;->d(Ljava/lang/Object;)V

    invoke-virtual/range {p1 .. p1}, Lokhttp3/w;->i()Lokhttp3/w$a;

    move-result-object v1

    invoke-static {}, Lcom/transsion/api/gateway/utils/ContextUtils;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-static {v0}, Lcom/transsion/api/gateway/utils/SafeStringUtils;->getInstance(Landroid/content/Context;)Lcom/transsion/api/gateway/utils/SafeStringUtils;

    move-result-object v0

    const-string v2, "time_offset"

    invoke-virtual {v0, v2}, Lcom/transsion/api/gateway/utils/SafeStringUtils;->getLong(Ljava/lang/String;)J

    move-result-wide v2

    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v4

    add-long/2addr v4, v2

    invoke-static {}, Lcom/transsion/api/gateway/GateWaySdk;->getSecret()Ljava/lang/String;

    move-result-object v0

    new-instance v2, Lcom/transsion/api/gateway/sercurity/a;

    invoke-direct {v2, v0}, Lcom/transsion/api/gateway/sercurity/a;-><init>(Ljava/lang/String;)V

    new-instance v3, Lcom/transsion/api/gateway/sercurity/c;

    invoke-direct {v3, v2}, Lcom/transsion/api/gateway/sercurity/c;-><init>(Lcom/transsion/api/gateway/sercurity/a;)V

    invoke-virtual {v1}, Lokhttp3/w$a;->b()Lokhttp3/w;

    move-result-object v2

    new-instance v6, Ljava/lang/StringBuilder;

    invoke-direct {v6}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v2}, Lokhttp3/w;->f()Lokhttp3/r;

    move-result-object v0

    const-string v7, "accept"

    invoke-virtual {v0, v7}, Lokhttp3/r;->a(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v7

    invoke-virtual {v2}, Lokhttp3/w;->f()Lokhttp3/r;

    move-result-object v0

    const-string v8, "content-type"

    invoke-virtual {v0, v8}, Lokhttp3/r;->a(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v2}, Lokhttp3/w;->f()Lokhttp3/r;

    move-result-object v8

    const-string v9, "content-length"

    invoke-virtual {v8, v9}, Lokhttp3/r;->a(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v8

    :try_start_0
    invoke-virtual {v2}, Lokhttp3/w;->a()Lokhttp3/x;

    move-result-object v9

    if-eqz v9, :cond_0

    invoke-static {v0}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v9

    if-eqz v9, :cond_0

    invoke-virtual {v2}, Lokhttp3/w;->a()Lokhttp3/x;

    move-result-object v9

    invoke-virtual {v9}, Lokhttp3/x;->contentType()Lokhttp3/u;

    move-result-object v9

    invoke-virtual {v9}, Lokhttp3/u;->toString()Ljava/lang/String;

    move-result-object v0
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    :catch_0
    :cond_0
    move-object v9, v0

    :try_start_1
    invoke-virtual {v2}, Lokhttp3/w;->a()Lokhttp3/x;

    move-result-object v0

    if-eqz v0, :cond_1

    invoke-static {v8}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    if-eqz v0, :cond_1

    invoke-virtual {v2}, Lokhttp3/w;->a()Lokhttp3/x;

    move-result-object v0

    invoke-virtual {v0}, Lokhttp3/x;->contentLength()J

    move-result-wide v10

    invoke-static {v10, v11}, Ljava/lang/String;->valueOf(J)Ljava/lang/String;

    move-result-object v8
    :try_end_1
    .catch Ljava/lang/Exception; {:try_start_1 .. :try_end_1} :catch_1

    goto :goto_0

    :catch_1
    nop

    :cond_1
    :goto_0
    invoke-virtual {v2}, Lokhttp3/w;->a()Lokhttp3/x;

    move-result-object v0

    const-string v10, ""

    if-eqz v0, :cond_4

    sget-object v0, Lcom/transsion/api/gateway/utils/GatewayUtils;->L:Lcom/transsion/api/gateway/utils/ObjectLogUtils;

    const-string v11, "request body is not null"

    invoke-virtual {v0, v11}, Lcom/transsion/api/gateway/utils/ObjectLogUtils;->d(Ljava/lang/Object;)V

    :try_start_2
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v11

    invoke-virtual {v2}, Lokhttp3/w;->i()Lokhttp3/w$a;

    move-result-object v0

    invoke-virtual {v0}, Lokhttp3/w$a;->b()Lokhttp3/w;

    move-result-object v0

    new-instance v13, Lokio/Buffer;

    invoke-direct {v13}, Lokio/Buffer;-><init>()V

    invoke-virtual {v0}, Lokhttp3/w;->a()Lokhttp3/x;

    move-result-object v14

    if-eqz v14, :cond_2

    invoke-virtual {v0}, Lokhttp3/w;->a()Lokhttp3/x;

    move-result-object v0

    invoke-virtual {v0, v13}, Lokhttp3/x;->writeTo(Lokio/BufferedSink;)V

    goto :goto_1

    :catch_2
    move-exception v0

    move-object v13, v10

    goto :goto_4

    :cond_2
    :goto_1
    invoke-static {v8}, Ljava/lang/Integer;->parseInt(Ljava/lang/String;)I

    move-result v0

    const v14, 0x19000

    if-le v0, v14, :cond_3

    new-instance v0, Lokio/Buffer;

    invoke-direct {v0}, Lokio/Buffer;-><init>()V

    const-wide/16 v15, 0x0

    const-wide/32 v17, 0x19000

    move-object v14, v0

    invoke-virtual/range {v13 .. v18}, Lokio/Buffer;->copyTo(Lokio/Buffer;JJ)Lokio/Buffer;

    invoke-virtual {v0}, Lokio/Buffer;->md5()Lokio/ByteString;

    move-result-object v0

    invoke-virtual {v0}, Lokio/ByteString;->hex()Ljava/lang/String;

    move-result-object v0

    :goto_2
    move-object v13, v0

    goto :goto_3

    :cond_3
    invoke-virtual {v13}, Lokio/Buffer;->md5()Lokio/ByteString;

    move-result-object v0

    invoke-virtual {v0}, Lokio/ByteString;->hex()Ljava/lang/String;

    move-result-object v0
    :try_end_2
    .catch Ljava/lang/Exception; {:try_start_2 .. :try_end_2} :catch_2

    goto :goto_2

    :goto_3
    :try_start_3
    sget-object v0, Lcom/transsion/api/gateway/utils/GatewayUtils;->L:Lcom/transsion/api/gateway/utils/ObjectLogUtils;

    new-instance v14, Ljava/lang/StringBuilder;

    invoke-direct {v14}, Ljava/lang/StringBuilder;-><init>()V

    const-string v15, "md5 duration"

    invoke-virtual {v14, v15}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v15

    sub-long v11, v15, v11

    invoke-virtual {v14, v11, v12}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    invoke-virtual {v14}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v11

    invoke-virtual {v0, v11}, Lcom/transsion/api/gateway/utils/ObjectLogUtils;->d(Ljava/lang/Object;)V
    :try_end_3
    .catch Ljava/lang/Exception; {:try_start_3 .. :try_end_3} :catch_3

    goto :goto_5

    :catch_3
    move-exception v0

    :goto_4
    sget-object v11, Lcom/transsion/api/gateway/utils/GatewayUtils;->L:Lcom/transsion/api/gateway/utils/ObjectLogUtils;

    new-instance v12, Ljava/lang/StringBuilder;

    invoke-direct {v12}, Ljava/lang/StringBuilder;-><init>()V

    const-string v14, "generateContent exception"

    invoke-virtual {v12, v14}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v12, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v12}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v11, v0}, Lcom/transsion/api/gateway/utils/ObjectLogUtils;->e(Ljava/lang/Object;)V

    goto :goto_5

    :cond_4
    sget-object v0, Lcom/transsion/api/gateway/utils/GatewayUtils;->L:Lcom/transsion/api/gateway/utils/ObjectLogUtils;

    const-string v11, "request body is null"

    invoke-virtual {v0, v11}, Lcom/transsion/api/gateway/utils/ObjectLogUtils;->d(Ljava/lang/Object;)V

    move-object v13, v10

    :goto_5
    invoke-virtual {v2}, Lokhttp3/w;->h()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/String;->toUpperCase()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v6, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v0, "\n"

    invoke-virtual {v6, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    if-nez v7, :cond_5

    move-object v7, v10

    :cond_5
    invoke-virtual {v6, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v6, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    if-nez v9, :cond_6

    move-object v9, v10

    :cond_6
    invoke-virtual {v6, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v6, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    if-nez v8, :cond_7

    move-object v8, v10

    :cond_7
    invoke-virtual {v6, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v6, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v6, v4, v5}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    invoke-virtual {v6, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-static {v13}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v7

    if-eqz v7, :cond_8

    goto :goto_6

    :cond_8
    move-object v10, v13

    :goto_6
    invoke-virtual {v6, v10}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v6, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v2}, Lokhttp3/w;->k()Lokhttp3/s;

    move-result-object v7

    invoke-virtual {v7}, Lokhttp3/s;->t()Ljava/net/URI;

    move-result-object v7

    invoke-virtual {v7}, Ljava/net/URI;->getPath()Ljava/lang/String;

    move-result-object v7

    invoke-virtual {v0, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Lokhttp3/w;->k()Lokhttp3/s;

    move-result-object v7

    invoke-virtual {v7}, Lokhttp3/s;->t()Ljava/net/URI;

    move-result-object v7

    invoke-virtual {v7}, Ljava/net/URI;->getQuery()Ljava/lang/String;

    move-result-object v7

    invoke-static {v7}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v7

    if-nez v7, :cond_9

    invoke-virtual {v2}, Lokhttp3/w;->k()Lokhttp3/s;

    move-result-object v7

    invoke-virtual {v7}, Lokhttp3/s;->t()Ljava/net/URI;

    move-result-object v7

    invoke-virtual {v7}, Ljava/net/URI;->getQuery()Ljava/lang/String;

    move-result-object v7

    invoke-virtual {v3, v7}, Lcom/transsion/api/gateway/sercurity/c;->a(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v7

    invoke-static {v7}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v8

    if-nez v8, :cond_9

    sget-object v8, Lcom/transsion/api/gateway/utils/GatewayUtils;->L:Lcom/transsion/api/gateway/utils/ObjectLogUtils;

    new-instance v9, Ljava/lang/StringBuilder;

    invoke-direct {v9}, Ljava/lang/StringBuilder;-><init>()V

    const-string v10, "formatedUrl"

    invoke-virtual {v9, v10}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v9, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v9}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v9

    invoke-virtual {v8, v9}, Lcom/transsion/api/gateway/utils/ObjectLogUtils;->d(Ljava/lang/Object;)V

    const-string v8, "?"

    invoke-virtual {v0, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    :cond_9
    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v6, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    sget-object v0, Lcom/transsion/api/gateway/utils/GatewayUtils;->L:Lcom/transsion/api/gateway/utils/ObjectLogUtils;

    invoke-virtual {v6}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v7

    invoke-virtual {v0, v7}, Lcom/transsion/api/gateway/utils/ObjectLogUtils;->d(Ljava/lang/Object;)V

    invoke-virtual {v6}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v2}, Lokhttp3/w;->f()Lokhttp3/r;

    move-result-object v2

    const-string v6, "x-tr-signature-method"

    invoke-virtual {v2, v6}, Lokhttp3/r;->a(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v2

    if-eqz v2, :cond_a

    invoke-virtual {v2}, Ljava/lang/String;->isEmpty()Z

    move-result v6

    if-nez v6, :cond_a

    invoke-static {v2}, Lcom/transsion/api/gateway/sercurity/d;->valueOf(Ljava/lang/String;)Lcom/transsion/api/gateway/sercurity/d;

    move-result-object v2

    goto :goto_7

    :cond_a
    sget-object v2, Lcom/transsion/api/gateway/sercurity/d;->c:Lcom/transsion/api/gateway/sercurity/d;

    :goto_7
    iget-object v3, v3, Lcom/transsion/api/gateway/sercurity/c;->a:Lcom/transsion/api/gateway/sercurity/a;

    invoke-virtual {v3, v2, v0}, Lcom/transsion/api/gateway/sercurity/a;->a(Lcom/transsion/api/gateway/sercurity/d;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    const-string v2, "x-tr-signature"

    invoke-virtual {v1, v2}, Lokhttp3/w$a;->l(Ljava/lang/String;)Lokhttp3/w$a;

    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v3, v4, v5}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    const-string v6, "|2|"

    invoke-virtual {v3, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v1, v2, v3}, Lokhttp3/w$a;->a(Ljava/lang/String;Ljava/lang/String;)Lokhttp3/w$a;

    sget-object v2, Lcom/transsion/api/gateway/utils/GatewayUtils;->L:Lcom/transsion/api/gateway/utils/ObjectLogUtils;

    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v7, "x-tr-signature: "

    invoke-virtual {v3, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3, v4, v5}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    invoke-virtual {v3, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v2, v0}, Lcom/transsion/api/gateway/utils/ObjectLogUtils;->d(Ljava/lang/Object;)V

    invoke-virtual {v1}, Lokhttp3/w$a;->b()Lokhttp3/w;

    move-result-object v0

    invoke-virtual/range {p1 .. p1}, Lokhttp3/w;->a()Lokhttp3/x;

    move-result-object v1

    if-eqz v1, :cond_b

    const-string v1, "Content-Encoding"

    move-object/from16 v2, p1

    invoke-virtual {v2, v1}, Lokhttp3/w;->d(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v3

    if-eqz v3, :cond_c

    :cond_b
    move-object/from16 v3, p0

    goto :goto_8

    :cond_c
    invoke-virtual/range {p1 .. p1}, Lokhttp3/w;->k()Lokhttp3/s;

    move-result-object v3

    invoke-virtual {v3}, Lokhttp3/s;->t()Ljava/net/URI;

    move-result-object v3

    invoke-virtual {v3}, Ljava/net/URI;->getPath()Ljava/lang/String;

    move-result-object v3

    const-string v4, "/gateway/metric/add"

    invoke-virtual {v3, v4}, Ljava/lang/String;->contains(Ljava/lang/CharSequence;)Z

    move-result v3

    if-nez v3, :cond_d

    return-object v0

    :cond_d
    invoke-virtual {v0}, Lokhttp3/w;->i()Lokhttp3/w$a;

    move-result-object v0

    const-string v3, "gzip"

    invoke-virtual {v0, v1, v3}, Lokhttp3/w$a;->h(Ljava/lang/String;Ljava/lang/String;)Lokhttp3/w$a;

    move-result-object v0

    invoke-virtual/range {p1 .. p1}, Lokhttp3/w;->h()Ljava/lang/String;

    move-result-object v1

    invoke-virtual/range {p1 .. p1}, Lokhttp3/w;->a()Lokhttp3/x;

    move-result-object v2

    move-object/from16 v3, p0

    invoke-direct {v3, v2}, Lcom/transsion/api/gateway/interceptor/GatewayInterceptor;->gzip(Lokhttp3/x;)Lokhttp3/x;

    move-result-object v2

    invoke-virtual {v0, v1, v2}, Lokhttp3/w$a;->j(Ljava/lang/String;Lokhttp3/x;)Lokhttp3/w$a;

    move-result-object v0

    invoke-virtual {v0}, Lokhttp3/w$a;->b()Lokhttp3/w;

    move-result-object v0

    :goto_8
    return-object v0
.end method

.method private getTimeOffset(Lokhttp3/y;)J
    .locals 5

    const-wide/16 v0, 0x0

    if-eqz p1, :cond_1

    :try_start_0
    invoke-virtual {p1}, Lokhttp3/y;->isSuccessful()Z

    move-result v2

    if-nez v2, :cond_1

    invoke-virtual {p1}, Lokhttp3/y;->a()Lokhttp3/z;

    move-result-object v2

    if-eqz v2, :cond_1

    invoke-virtual {p1}, Lokhttp3/y;->g()I

    move-result v2

    const/16 v3, 0x1f4

    if-eq v2, v3, :cond_0

    goto :goto_0

    :cond_0
    const/4 v2, 0x1

    iput-boolean v2, p0, Lcom/transsion/api/gateway/interceptor/GatewayInterceptor;->isReadResponse:Z

    invoke-virtual {p1}, Lokhttp3/y;->a()Lokhttp3/z;

    move-result-object v2

    invoke-virtual {v2}, Lokhttp3/z;->string()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {p1}, Lokhttp3/y;->a()Lokhttp3/z;

    move-result-object v3

    invoke-virtual {v3}, Lokhttp3/z;->contentType()Lokhttp3/u;

    move-result-object v3

    invoke-virtual {p1}, Lokhttp3/y;->q()Lokhttp3/y$a;

    move-result-object p1

    invoke-static {v3, v2}, Lokhttp3/z;->create(Lokhttp3/u;Ljava/lang/String;)Lokhttp3/z;

    move-result-object v3

    invoke-virtual {p1, v3}, Lokhttp3/y$a;->b(Lokhttp3/z;)Lokhttp3/y$a;

    move-result-object p1

    invoke-virtual {p1}, Lokhttp3/y$a;->c()Lokhttp3/y;

    move-result-object p1

    iput-object p1, p0, Lcom/transsion/api/gateway/interceptor/GatewayInterceptor;->mResponse:Lokhttp3/y;

    sget-object p1, Lcom/transsion/api/gateway/utils/GatewayUtils;->L:Lcom/transsion/api/gateway/utils/ObjectLogUtils;

    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v4, "responseString:"

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {p1, v3}, Lcom/transsion/api/gateway/utils/ObjectLogUtils;->d(Ljava/lang/Object;)V

    const-class p1, Lcom/transsion/api/gateway/bean/GatewayResponse;

    invoke-static {v2, p1}, Lcom/transsion/json/b;->a(Ljava/lang/String;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/transsion/api/gateway/bean/GatewayResponse;
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    const-string v2, "GW.4410"

    :try_start_1
    iget-object v3, p1, Lcom/transsion/api/gateway/bean/GatewayResponse;->errorCode:Ljava/lang/String;

    invoke-virtual {v2, v3}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_1

    sget-object v2, Lcom/transsion/api/gateway/utils/GatewayUtils;->L:Lcom/transsion/api/gateway/utils/ObjectLogUtils;

    const-string v3, "verify sign failed, retrying update time"

    invoke-virtual {v2, v3}, Lcom/transsion/api/gateway/utils/ObjectLogUtils;->d(Ljava/lang/Object;)V

    iget-object p1, p1, Lcom/transsion/api/gateway/bean/GatewayResponse;->errorMsg:Ljava/lang/String;

    invoke-static {p1}, Lcom/transsion/api/gateway/dns/a;->a(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    const-class v2, Lcom/transsion/api/gateway/bean/TimeBean;

    invoke-static {p1, v2}, Lcom/transsion/json/b;->a(Ljava/lang/String;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/transsion/api/gateway/bean/TimeBean;

    iget-wide v0, p1, Lcom/transsion/api/gateway/bean/TimeBean;->time:J
    :try_end_1
    .catch Ljava/lang/Exception; {:try_start_1 .. :try_end_1} :catch_0

    return-wide v0

    :catch_0
    move-exception p1

    sget-object v2, Lcom/transsion/api/gateway/utils/GatewayUtils;->L:Lcom/transsion/api/gateway/utils/ObjectLogUtils;

    invoke-virtual {v2, p1}, Lcom/transsion/api/gateway/utils/ObjectLogUtils;->e(Ljava/lang/Object;)V

    :cond_1
    :goto_0
    return-wide v0
.end method

.method private gzip(Lokhttp3/x;)Lokhttp3/x;
    .locals 1

    new-instance v0, Lcom/transsion/api/gateway/interceptor/GatewayInterceptor$1;

    invoke-direct {v0, p0, p1}, Lcom/transsion/api/gateway/interceptor/GatewayInterceptor$1;-><init>(Lcom/transsion/api/gateway/interceptor/GatewayInterceptor;Lokhttp3/x;)V

    return-object v0
.end method

.method private recordRequest(Lokhttp3/w;Lcom/transsion/api/gateway/analytics/a;)V
    .locals 8

    if-eqz p1, :cond_3

    invoke-virtual {p1}, Lokhttp3/w;->k()Lokhttp3/s;

    move-result-object v0

    invoke-virtual {v0}, Lokhttp3/s;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    if-eqz v0, :cond_0

    goto/16 :goto_2

    :cond_0
    const/4 v0, 0x1

    iput-boolean v0, p2, Lcom/transsion/api/gateway/analytics/a;->d:Z

    invoke-virtual {p1}, Lokhttp3/w;->k()Lokhttp3/s;

    move-result-object v0

    invoke-virtual {v0}, Lokhttp3/s;->i()Ljava/lang/String;

    move-result-object v0

    iput-object v0, p2, Lcom/transsion/api/gateway/analytics/a;->f:Ljava/lang/String;

    invoke-virtual {p1}, Lokhttp3/w;->k()Lokhttp3/s;

    move-result-object v0

    invoke-virtual {v0}, Lokhttp3/s;->toString()Ljava/lang/String;

    move-result-object v0

    iput-object v0, p2, Lcom/transsion/api/gateway/analytics/a;->h:Ljava/lang/String;

    invoke-virtual {p1}, Lokhttp3/w;->k()Lokhttp3/s;

    move-result-object v0

    invoke-virtual {v0}, Lokhttp3/s;->i()Ljava/lang/String;

    move-result-object v0

    iput-object v0, p2, Lcom/transsion/api/gateway/analytics/a;->i:Ljava/lang/String;

    invoke-virtual {p1}, Lokhttp3/w;->k()Lokhttp3/s;

    move-result-object v0

    invoke-virtual {v0}, Lokhttp3/s;->t()Ljava/net/URI;

    move-result-object v0

    invoke-virtual {v0}, Ljava/net/URI;->getPath()Ljava/lang/String;

    move-result-object v0

    iput-object v0, p2, Lcom/transsion/api/gateway/analytics/a;->g:Ljava/lang/String;

    invoke-virtual {p1}, Lokhttp3/w;->a()Lokhttp3/x;

    move-result-object v0

    if-nez v0, :cond_1

    sget-object v0, Lcom/transsion/api/gateway/utils/GatewayUtils;->L:Lcom/transsion/api/gateway/utils/ObjectLogUtils;

    const-string v1, "recordRequest, request body is null"

    invoke-virtual {v0, v1}, Lcom/transsion/api/gateway/utils/ObjectLogUtils;->d(Ljava/lang/Object;)V

    invoke-virtual {p1}, Lokhttp3/w;->k()Lokhttp3/s;

    move-result-object p1

    invoke-virtual {p1}, Lokhttp3/s;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/String;->getBytes()[B

    move-result-object p1

    array-length p1, p1

    int-to-long v0, p1

    iput-wide v0, p2, Lcom/transsion/api/gateway/analytics/a;->e:J

    return-void

    :cond_1
    const-wide/16 v1, 0x0

    :try_start_0
    invoke-virtual {v0}, Lokhttp3/x;->contentLength()J

    move-result-wide v3
    :try_end_0
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_1

    :try_start_1
    sget-object v0, Lcom/transsion/api/gateway/utils/GatewayUtils;->L:Lcom/transsion/api/gateway/utils/ObjectLogUtils;

    new-instance v5, Ljava/lang/StringBuilder;

    invoke-direct {v5}, Ljava/lang/StringBuilder;-><init>()V

    const-string v6, "recordRequest, request length is "

    invoke-virtual {v5, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v5, v3, v4}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    invoke-virtual {v5}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v5

    invoke-virtual {v0, v5}, Lcom/transsion/api/gateway/utils/ObjectLogUtils;->d(Ljava/lang/Object;)V
    :try_end_1
    .catch Ljava/io/IOException; {:try_start_1 .. :try_end_1} :catch_0

    goto :goto_1

    :catch_0
    move-exception v0

    goto :goto_0

    :catch_1
    move-exception v0

    move-wide v3, v1

    :goto_0
    sget-object v5, Lcom/transsion/api/gateway/utils/GatewayUtils;->L:Lcom/transsion/api/gateway/utils/ObjectLogUtils;

    new-instance v6, Ljava/lang/StringBuilder;

    invoke-direct {v6}, Ljava/lang/StringBuilder;-><init>()V

    const-string v7, "recordRequest exception: "

    invoke-virtual {v6, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v6, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v6}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v5, v0}, Lcom/transsion/api/gateway/utils/ObjectLogUtils;->d(Ljava/lang/Object;)V

    :goto_1
    cmp-long v0, v3, v1

    if-lez v0, :cond_2

    iput-wide v3, p2, Lcom/transsion/api/gateway/analytics/a;->e:J

    goto :goto_2

    :cond_2
    invoke-virtual {p1}, Lokhttp3/w;->k()Lokhttp3/s;

    move-result-object p1

    invoke-virtual {p1}, Lokhttp3/s;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/String;->getBytes()[B

    move-result-object p1

    array-length p1, p1

    int-to-long v0, p1

    iput-wide v0, p2, Lcom/transsion/api/gateway/analytics/a;->e:J

    :cond_3
    :goto_2
    return-void
.end method

.method private recordResponse(Lokhttp3/y;Lcom/transsion/api/gateway/analytics/a;)V
    .locals 1

    if-nez p1, :cond_0

    return-void

    :cond_0
    invoke-virtual {p1}, Lokhttp3/y;->g()I

    move-result v0

    iput v0, p2, Lcom/transsion/api/gateway/analytics/a;->k:I

    invoke-virtual {p1}, Lokhttp3/y;->isSuccessful()Z

    move-result p1

    if-nez p1, :cond_1

    return-void

    :cond_1
    const-string p1, "success"

    invoke-direct {p0, p1, p2}, Lcom/transsion/api/gateway/interceptor/GatewayInterceptor;->setRequestResult(Ljava/lang/String;Lcom/transsion/api/gateway/analytics/a;)V

    return-void
.end method

.method private setRequestResult(Ljava/lang/String;Lcom/transsion/api/gateway/analytics/a;)V
    .locals 0

    return-void
.end method


# virtual methods
.method public intercept(Lokhttp3/t$a;)Lokhttp3/y;
    .locals 11
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    const-string v0, "request fail, duration\uff1a"

    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v1

    iput-wide v1, p0, Lcom/transsion/api/gateway/interceptor/GatewayInterceptor;->startNs:J

    const/4 v1, 0x0

    iput-boolean v1, p0, Lcom/transsion/api/gateway/interceptor/GatewayInterceptor;->isReadResponse:Z

    sget-object v2, Lcom/transsion/api/gateway/utils/GatewayUtils;->L:Lcom/transsion/api/gateway/utils/ObjectLogUtils;

    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v4, " request start time\uff1a"

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-wide v4, p0, Lcom/transsion/api/gateway/interceptor/GatewayInterceptor;->startNs:J

    invoke-virtual {v3, v4, v5}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v2, v3}, Lcom/transsion/api/gateway/utils/ObjectLogUtils;->d(Ljava/lang/Object;)V

    new-instance v2, Lcom/transsion/api/gateway/analytics/a;

    invoke-direct {v2}, Lcom/transsion/api/gateway/analytics/a;-><init>()V

    invoke-interface {p1}, Lokhttp3/t$a;->request()Lokhttp3/w;

    move-result-object v3

    invoke-direct {p0, v3, v2}, Lcom/transsion/api/gateway/interceptor/GatewayInterceptor;->recordRequest(Lokhttp3/w;Lcom/transsion/api/gateway/analytics/a;)V

    iget-object v3, v2, Lcom/transsion/api/gateway/analytics/a;->f:Ljava/lang/String;

    iget-object v4, v2, Lcom/transsion/api/gateway/analytics/a;->g:Ljava/lang/String;

    invoke-static {v3, v4}, Lcom/transsion/api/gateway/utils/GatewayUtils;->canUseGateWay(Ljava/lang/String;Ljava/lang/String;)Z

    move-result v3

    if-nez v3, :cond_3

    sget-object v3, Lcom/transsion/api/gateway/utils/GatewayUtils;->L:Lcom/transsion/api/gateway/utils/ObjectLogUtils;

    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    const-string v5, "transparent request\uff1a"

    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-wide v5, p0, Lcom/transsion/api/gateway/interceptor/GatewayInterceptor;->startNs:J

    invoke-virtual {v4, v5, v6}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v3, v4}, Lcom/transsion/api/gateway/utils/ObjectLogUtils;->d(Ljava/lang/Object;)V

    :try_start_0
    invoke-interface {p1}, Lokhttp3/t$a;->request()Lokhttp3/w;

    move-result-object v3

    invoke-direct {p0, v3}, Lcom/transsion/api/gateway/interceptor/GatewayInterceptor;->doGzipOrSign(Lokhttp3/w;)Lokhttp3/w;

    move-result-object v3

    invoke-interface {p1, v3}, Lokhttp3/t$a;->a(Lokhttp3/w;)Lokhttp3/y;

    move-result-object v3
    :try_end_0
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_1

    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v4

    invoke-direct {p0, v3}, Lcom/transsion/api/gateway/interceptor/GatewayInterceptor;->getTimeOffset(Lokhttp3/y;)J

    move-result-wide v6

    const-wide/16 v8, 0x0

    cmp-long v10, v6, v8

    if-lez v10, :cond_0

    iput-boolean v1, p0, Lcom/transsion/api/gateway/interceptor/GatewayInterceptor;->isReadResponse:Z

    const/4 v1, 0x0

    iput-object v1, p0, Lcom/transsion/api/gateway/interceptor/GatewayInterceptor;->mResponse:Lokhttp3/y;

    invoke-static {}, Lcom/transsion/api/gateway/utils/ContextUtils;->getContext()Landroid/content/Context;

    move-result-object v1

    invoke-static {v1}, Lcom/transsion/api/gateway/utils/SafeStringUtils;->getInstance(Landroid/content/Context;)Lcom/transsion/api/gateway/utils/SafeStringUtils;

    move-result-object v1

    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v8

    sub-long/2addr v6, v8

    const-string v3, "time_offset"

    invoke-virtual {v1, v3, v6, v7}, Lcom/transsion/api/gateway/utils/SafeStringUtils;->saveLong(Ljava/lang/String;J)V

    :try_start_1
    invoke-interface {p1}, Lokhttp3/t$a;->request()Lokhttp3/w;

    move-result-object v1

    invoke-direct {p0, v1}, Lcom/transsion/api/gateway/interceptor/GatewayInterceptor;->doGzipOrSign(Lokhttp3/w;)Lokhttp3/w;

    move-result-object v1

    invoke-interface {p1, v1}, Lokhttp3/t$a;->a(Lokhttp3/w;)Lokhttp3/y;

    move-result-object v3
    :try_end_1
    .catch Ljava/io/IOException; {:try_start_1 .. :try_end_1} :catch_0

    sget-object p1, Lcom/transsion/api/gateway/utils/GatewayUtils;->L:Lcom/transsion/api/gateway/utils/ObjectLogUtils;

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "retry for timeoffset duration\uff1a"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v6

    sub-long/2addr v6, v4

    invoke-virtual {v0, v6, v7}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p1, v0}, Lcom/transsion/api/gateway/utils/ObjectLogUtils;->d(Ljava/lang/Object;)V

    goto :goto_0

    :catch_0
    move-exception p1

    sget-object v1, Lcom/transsion/api/gateway/utils/GatewayUtils;->L:Lcom/transsion/api/gateway/utils/ObjectLogUtils;

    invoke-virtual {v1, p1}, Lcom/transsion/api/gateway/utils/ObjectLogUtils;->e(Ljava/lang/Object;)V

    invoke-virtual {p1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-direct {p0, v1, v2}, Lcom/transsion/api/gateway/interceptor/GatewayInterceptor;->setRequestResult(Ljava/lang/String;Lcom/transsion/api/gateway/analytics/a;)V

    sget-object v1, Lcom/transsion/api/gateway/utils/GatewayUtils;->L:Lcom/transsion/api/gateway/utils/ObjectLogUtils;

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v3

    iget-wide v5, p0, Lcom/transsion/api/gateway/interceptor/GatewayInterceptor;->startNs:J

    sub-long/2addr v3, v5

    invoke-virtual {v2, v3, v4}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v1, v0}, Lcom/transsion/api/gateway/utils/ObjectLogUtils;->d(Ljava/lang/Object;)V

    throw p1

    :cond_0
    :goto_0
    invoke-direct {p0, v3, v2}, Lcom/transsion/api/gateway/interceptor/GatewayInterceptor;->recordResponse(Lokhttp3/y;Lcom/transsion/api/gateway/analytics/a;)V

    iget p1, v2, Lcom/transsion/api/gateway/analytics/a;->k:I

    const/16 v0, 0xc8

    if-ne p1, v0, :cond_1

    sget-object p1, Lcom/transsion/api/gateway/utils/GatewayUtils;->L:Lcom/transsion/api/gateway/utils/ObjectLogUtils;

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, " request success, duration\uff1a"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v1

    iget-wide v4, p0, Lcom/transsion/api/gateway/interceptor/GatewayInterceptor;->startNs:J

    sub-long/2addr v1, v4

    invoke-virtual {v0, v1, v2}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p1, v0}, Lcom/transsion/api/gateway/utils/ObjectLogUtils;->d(Ljava/lang/Object;)V

    goto :goto_1

    :cond_1
    sget-object p1, Lcom/transsion/api/gateway/utils/GatewayUtils;->L:Lcom/transsion/api/gateway/utils/ObjectLogUtils;

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, " request fail, code:  "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget v1, v2, Lcom/transsion/api/gateway/analytics/a;->k:I

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p1, v0}, Lcom/transsion/api/gateway/utils/ObjectLogUtils;->d(Ljava/lang/Object;)V

    :goto_1
    iget-boolean p1, p0, Lcom/transsion/api/gateway/interceptor/GatewayInterceptor;->isReadResponse:Z

    if-eqz p1, :cond_2

    iget-object p1, p0, Lcom/transsion/api/gateway/interceptor/GatewayInterceptor;->mResponse:Lokhttp3/y;

    if-eqz p1, :cond_2

    return-object p1

    :cond_2
    return-object v3

    :catch_1
    move-exception p1

    sget-object v1, Lcom/transsion/api/gateway/utils/GatewayUtils;->L:Lcom/transsion/api/gateway/utils/ObjectLogUtils;

    invoke-virtual {v1, p1}, Lcom/transsion/api/gateway/utils/ObjectLogUtils;->e(Ljava/lang/Object;)V

    invoke-virtual {p1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-direct {p0, v1, v2}, Lcom/transsion/api/gateway/interceptor/GatewayInterceptor;->setRequestResult(Ljava/lang/String;Lcom/transsion/api/gateway/analytics/a;)V

    sget-object v1, Lcom/transsion/api/gateway/utils/GatewayUtils;->L:Lcom/transsion/api/gateway/utils/ObjectLogUtils;

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v3

    iget-wide v5, p0, Lcom/transsion/api/gateway/interceptor/GatewayInterceptor;->startNs:J

    sub-long/2addr v3, v5

    invoke-virtual {v2, v3, v4}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v1, v0}, Lcom/transsion/api/gateway/utils/ObjectLogUtils;->d(Ljava/lang/Object;)V

    throw p1

    :cond_3
    invoke-direct {p0, p1, v2}, Lcom/transsion/api/gateway/interceptor/GatewayInterceptor;->doGatewayJob(Lokhttp3/t$a;Lcom/transsion/api/gateway/analytics/a;)Lokhttp3/y;

    move-result-object p1

    return-object p1
.end method
