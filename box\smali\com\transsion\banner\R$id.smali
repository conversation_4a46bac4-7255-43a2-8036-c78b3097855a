.class public final Lcom/transsion/banner/R$id;
.super Ljava/lang/Object;


# static fields
.field public static banner:I = 0x7f0a00b6

.field public static bannerContainer:I = 0x7f0a00b7

.field public static bannerViewPager:I = 0x7f0a00b8

.field public static banner_data_key:I = 0x7f0a00b9

.field public static banner_mask_id:I = 0x7f0a00ba

.field public static banner_pos_key:I = 0x7f0a00bb

.field public static bg_color:I = 0x7f0a00c9

.field public static center:I = 0x7f0a0134

.field public static circleIndicator:I = 0x7f0a0143

.field public static cover:I = 0x7f0a01a3

.field public static download:I = 0x7f0a01da

.field public static horizontal:I = 0x7f0a0308

.field public static image:I = 0x7f0a0323

.field public static item_root:I = 0x7f0a0377

.field public static iv_cover:I = 0x7f0a03f3

.field public static iv_tag:I = 0x7f0a048a

.field public static joinAnimationView:I = 0x7f0a04a3

.field public static left:I = 0x7f0a04dc

.field public static ll_content:I = 0x7f0a050a

.field public static ll_download:I = 0x7f0a050d

.field public static native_layout:I = 0x7f0a0692

.field public static op_gradient:I = 0x7f0a06d1

.field public static recycler_view:I = 0x7f0a0780

.field public static right:I = 0x7f0a0791

.field public static shadow:I = 0x7f0a082e

.field public static title:I = 0x7f0a0908

.field public static tv_desc:I = 0x7f0a0a25

.field public static tv_download_size:I = 0x7f0a0a31

.field public static tv_ops_title:I = 0x7f0a0a9c

.field public static tv_playlist_corner:I = 0x7f0a0aaa

.field public static tv_post_count:I = 0x7f0a0ab1

.field public static tv_score:I = 0x7f0a0ada

.field public static tv_time:I = 0x7f0a0b10

.field public static tv_title:I = 0x7f0a0b1d

.field public static tv_view_more:I = 0x7f0a0b32

.field public static vertical:I = 0x7f0a0bd0

.field public static view_more:I = 0x7f0a0c09


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
