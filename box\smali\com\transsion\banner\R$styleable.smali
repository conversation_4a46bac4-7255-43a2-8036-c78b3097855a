.class public final Lcom/transsion/banner/R$styleable;
.super Ljava/lang/Object;


# static fields
.field public static Banner:[I = null

.field public static Banner_banner_auto_loop:I = 0x0

.field public static Banner_banner_indicator_gravity:I = 0x1

.field public static Banner_banner_indicator_height:I = 0x2

.field public static Banner_banner_indicator_margin:I = 0x3

.field public static Banner_banner_indicator_marginBottom:I = 0x4

.field public static Banner_banner_indicator_marginLeft:I = 0x5

.field public static Banner_banner_indicator_marginRight:I = 0x6

.field public static Banner_banner_indicator_marginTop:I = 0x7

.field public static Banner_banner_indicator_normal_color:I = 0x8

.field public static Banner_banner_indicator_normal_width:I = 0x9

.field public static Banner_banner_indicator_radius:I = 0xa

.field public static Banner_banner_indicator_selected_color:I = 0xb

.field public static Banner_banner_indicator_selected_width:I = 0xc

.field public static Banner_banner_indicator_space:I = 0xd

.field public static Banner_banner_infinite_loop:I = 0xe

.field public static Banner_banner_loop_time:I = 0xf

.field public static Banner_banner_orientation:I = 0x10

.field public static Banner_banner_radius:I = 0x11

.field public static Banner_banner_round_bottom_left:I = 0x12

.field public static Banner_banner_round_bottom_right:I = 0x13

.field public static Banner_banner_round_top_left:I = 0x14

.field public static Banner_banner_round_top_right:I = 0x15

.field public static Banner_banner_viewpager_height:I = 0x16

.field public static opt_banner:[I = null

.field public static opt_banner_indicator_drawable_selected:I = 0x0

.field public static opt_banner_indicator_drawable_unselected:I = 0x1

.field public static opt_banner_indicator_height:I = 0x2

.field public static opt_banner_indicator_margin:I = 0x3

.field public static opt_banner_indicator_selected_height:I = 0x4

.field public static opt_banner_indicator_selected_width:I = 0x5

.field public static opt_banner_indicator_width:I = 0x6

.field public static opt_banner_is_auto_play:I = 0x7


# direct methods
.method public static constructor <clinit>()V
    .locals 1

    const/16 v0, 0x17

    new-array v0, v0, [I

    fill-array-data v0, :array_0

    sput-object v0, Lcom/transsion/banner/R$styleable;->Banner:[I

    const/16 v0, 0x8

    new-array v0, v0, [I

    fill-array-data v0, :array_1

    sput-object v0, Lcom/transsion/banner/R$styleable;->opt_banner:[I

    return-void

    nop

    :array_0
    .array-data 4
        0x7f040086
        0x7f040087
        0x7f040088
        0x7f040089
        0x7f04008a
        0x7f04008b
        0x7f04008c
        0x7f04008d
        0x7f04008e
        0x7f04008f
        0x7f040090
        0x7f040091
        0x7f040092
        0x7f040093
        0x7f040094
        0x7f040095
        0x7f040096
        0x7f040097
        0x7f040098
        0x7f040099
        0x7f04009a
        0x7f04009b
        0x7f04009c
    .end array-data

    :array_1
    .array-data 4
        0x7f0403ed
        0x7f0403ee
        0x7f0403ef
        0x7f0403f0
        0x7f0403f1
        0x7f0403f2
        0x7f0403f3
        0x7f040400
    .end array-data
.end method

.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
