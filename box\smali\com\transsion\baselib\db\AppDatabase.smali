.class public abstract Lcom/transsion/baselib/db/AppDatabase;
.super Landroidx/room/RoomDatabase;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/transsion/baselib/db/AppDatabase$t0;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation


# static fields
.field public static final A:Lcom/transsion/baselib/db/AppDatabase$a;

.field public static final B:Lcom/transsion/baselib/db/AppDatabase$b;

.field public static final C:Lcom/transsion/baselib/db/AppDatabase$c;

.field public static final D:Lcom/transsion/baselib/db/AppDatabase$d;

.field public static final E:Lcom/transsion/baselib/db/AppDatabase$e;

.field public static final F:Lcom/transsion/baselib/db/AppDatabase$f;

.field public static final G:Lcom/transsion/baselib/db/AppDatabase$g;

.field public static final H:Lcom/transsion/baselib/db/AppDatabase$h;

.field public static final I:Lcom/transsion/baselib/db/AppDatabase$i;

.field public static final J:Lcom/transsion/baselib/db/AppDatabase$j;

.field public static final K:Lcom/transsion/baselib/db/AppDatabase$l;

.field public static final L:Lcom/transsion/baselib/db/AppDatabase$m;

.field public static final M:Lcom/transsion/baselib/db/AppDatabase$n;

.field public static final N:Lcom/transsion/baselib/db/AppDatabase$o;

.field public static final O:Lcom/transsion/baselib/db/AppDatabase$p;

.field public static final P:Lcom/transsion/baselib/db/AppDatabase$q;

.field public static final Q:Lcom/transsion/baselib/db/AppDatabase$r;

.field public static final R:Lcom/transsion/baselib/db/AppDatabase$s;

.field public static final S:Lcom/transsion/baselib/db/AppDatabase$t;

.field public static final T:Lcom/transsion/baselib/db/AppDatabase$u;

.field public static final U:Lcom/transsion/baselib/db/AppDatabase$w;

.field public static final V:Lcom/transsion/baselib/db/AppDatabase$x;

.field public static final W:Lcom/transsion/baselib/db/AppDatabase$y;

.field public static final X:Lcom/transsion/baselib/db/AppDatabase$z;

.field public static final Y:Lcom/transsion/baselib/db/AppDatabase$a0;

.field public static final Z:Lcom/transsion/baselib/db/AppDatabase$b0;

.field public static final a0:Lcom/transsion/baselib/db/AppDatabase$c0;

.field public static final b0:Lcom/transsion/baselib/db/AppDatabase$d0;

.field public static final c0:Lcom/transsion/baselib/db/AppDatabase$e0;

.field public static final d0:Lcom/transsion/baselib/db/AppDatabase$f0;

.field public static final e0:Lcom/transsion/baselib/db/AppDatabase$h0;

.field public static final f0:Lcom/transsion/baselib/db/AppDatabase$i0;

.field public static final g0:Lcom/transsion/baselib/db/AppDatabase$j0;

.field public static final h0:Lcom/transsion/baselib/db/AppDatabase$k0;

.field public static final i0:Lcom/transsion/baselib/db/AppDatabase$l0;

.field public static final j0:Lcom/transsion/baselib/db/AppDatabase$m0;

.field public static final p:Lcom/transsion/baselib/db/AppDatabase$t0;

.field public static volatile q:Lcom/transsion/baselib/db/AppDatabase;

.field public static final r:Lcom/transsion/baselib/db/AppDatabase$k;

.field public static final s:Lcom/transsion/baselib/db/AppDatabase$v;

.field public static final t:Lcom/transsion/baselib/db/AppDatabase$g0;

.field public static final u:Lcom/transsion/baselib/db/AppDatabase$n0;

.field public static final v:Lcom/transsion/baselib/db/AppDatabase$o0;

.field public static final w:Lcom/transsion/baselib/db/AppDatabase$p0;

.field public static final x:Lcom/transsion/baselib/db/AppDatabase$q0;

.field public static final y:Lcom/transsion/baselib/db/AppDatabase$r0;

.field public static final z:Lcom/transsion/baselib/db/AppDatabase$s0;


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lcom/transsion/baselib/db/AppDatabase$t0;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lcom/transsion/baselib/db/AppDatabase$t0;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lcom/transsion/baselib/db/AppDatabase;->p:Lcom/transsion/baselib/db/AppDatabase$t0;

    new-instance v0, Lcom/transsion/baselib/db/AppDatabase$k;

    invoke-direct {v0}, Lcom/transsion/baselib/db/AppDatabase$k;-><init>()V

    sput-object v0, Lcom/transsion/baselib/db/AppDatabase;->r:Lcom/transsion/baselib/db/AppDatabase$k;

    new-instance v0, Lcom/transsion/baselib/db/AppDatabase$v;

    invoke-direct {v0}, Lcom/transsion/baselib/db/AppDatabase$v;-><init>()V

    sput-object v0, Lcom/transsion/baselib/db/AppDatabase;->s:Lcom/transsion/baselib/db/AppDatabase$v;

    new-instance v0, Lcom/transsion/baselib/db/AppDatabase$g0;

    invoke-direct {v0}, Lcom/transsion/baselib/db/AppDatabase$g0;-><init>()V

    sput-object v0, Lcom/transsion/baselib/db/AppDatabase;->t:Lcom/transsion/baselib/db/AppDatabase$g0;

    new-instance v0, Lcom/transsion/baselib/db/AppDatabase$n0;

    invoke-direct {v0}, Lcom/transsion/baselib/db/AppDatabase$n0;-><init>()V

    sput-object v0, Lcom/transsion/baselib/db/AppDatabase;->u:Lcom/transsion/baselib/db/AppDatabase$n0;

    new-instance v0, Lcom/transsion/baselib/db/AppDatabase$o0;

    invoke-direct {v0}, Lcom/transsion/baselib/db/AppDatabase$o0;-><init>()V

    sput-object v0, Lcom/transsion/baselib/db/AppDatabase;->v:Lcom/transsion/baselib/db/AppDatabase$o0;

    new-instance v0, Lcom/transsion/baselib/db/AppDatabase$p0;

    invoke-direct {v0}, Lcom/transsion/baselib/db/AppDatabase$p0;-><init>()V

    sput-object v0, Lcom/transsion/baselib/db/AppDatabase;->w:Lcom/transsion/baselib/db/AppDatabase$p0;

    new-instance v0, Lcom/transsion/baselib/db/AppDatabase$q0;

    invoke-direct {v0}, Lcom/transsion/baselib/db/AppDatabase$q0;-><init>()V

    sput-object v0, Lcom/transsion/baselib/db/AppDatabase;->x:Lcom/transsion/baselib/db/AppDatabase$q0;

    new-instance v0, Lcom/transsion/baselib/db/AppDatabase$r0;

    invoke-direct {v0}, Lcom/transsion/baselib/db/AppDatabase$r0;-><init>()V

    sput-object v0, Lcom/transsion/baselib/db/AppDatabase;->y:Lcom/transsion/baselib/db/AppDatabase$r0;

    new-instance v0, Lcom/transsion/baselib/db/AppDatabase$s0;

    invoke-direct {v0}, Lcom/transsion/baselib/db/AppDatabase$s0;-><init>()V

    sput-object v0, Lcom/transsion/baselib/db/AppDatabase;->z:Lcom/transsion/baselib/db/AppDatabase$s0;

    new-instance v0, Lcom/transsion/baselib/db/AppDatabase$a;

    invoke-direct {v0}, Lcom/transsion/baselib/db/AppDatabase$a;-><init>()V

    sput-object v0, Lcom/transsion/baselib/db/AppDatabase;->A:Lcom/transsion/baselib/db/AppDatabase$a;

    new-instance v0, Lcom/transsion/baselib/db/AppDatabase$b;

    invoke-direct {v0}, Lcom/transsion/baselib/db/AppDatabase$b;-><init>()V

    sput-object v0, Lcom/transsion/baselib/db/AppDatabase;->B:Lcom/transsion/baselib/db/AppDatabase$b;

    new-instance v0, Lcom/transsion/baselib/db/AppDatabase$c;

    invoke-direct {v0}, Lcom/transsion/baselib/db/AppDatabase$c;-><init>()V

    sput-object v0, Lcom/transsion/baselib/db/AppDatabase;->C:Lcom/transsion/baselib/db/AppDatabase$c;

    new-instance v0, Lcom/transsion/baselib/db/AppDatabase$d;

    invoke-direct {v0}, Lcom/transsion/baselib/db/AppDatabase$d;-><init>()V

    sput-object v0, Lcom/transsion/baselib/db/AppDatabase;->D:Lcom/transsion/baselib/db/AppDatabase$d;

    new-instance v0, Lcom/transsion/baselib/db/AppDatabase$e;

    invoke-direct {v0}, Lcom/transsion/baselib/db/AppDatabase$e;-><init>()V

    sput-object v0, Lcom/transsion/baselib/db/AppDatabase;->E:Lcom/transsion/baselib/db/AppDatabase$e;

    new-instance v0, Lcom/transsion/baselib/db/AppDatabase$f;

    invoke-direct {v0}, Lcom/transsion/baselib/db/AppDatabase$f;-><init>()V

    sput-object v0, Lcom/transsion/baselib/db/AppDatabase;->F:Lcom/transsion/baselib/db/AppDatabase$f;

    new-instance v0, Lcom/transsion/baselib/db/AppDatabase$g;

    invoke-direct {v0}, Lcom/transsion/baselib/db/AppDatabase$g;-><init>()V

    sput-object v0, Lcom/transsion/baselib/db/AppDatabase;->G:Lcom/transsion/baselib/db/AppDatabase$g;

    new-instance v0, Lcom/transsion/baselib/db/AppDatabase$h;

    invoke-direct {v0}, Lcom/transsion/baselib/db/AppDatabase$h;-><init>()V

    sput-object v0, Lcom/transsion/baselib/db/AppDatabase;->H:Lcom/transsion/baselib/db/AppDatabase$h;

    new-instance v0, Lcom/transsion/baselib/db/AppDatabase$i;

    invoke-direct {v0}, Lcom/transsion/baselib/db/AppDatabase$i;-><init>()V

    sput-object v0, Lcom/transsion/baselib/db/AppDatabase;->I:Lcom/transsion/baselib/db/AppDatabase$i;

    new-instance v0, Lcom/transsion/baselib/db/AppDatabase$j;

    invoke-direct {v0}, Lcom/transsion/baselib/db/AppDatabase$j;-><init>()V

    sput-object v0, Lcom/transsion/baselib/db/AppDatabase;->J:Lcom/transsion/baselib/db/AppDatabase$j;

    new-instance v0, Lcom/transsion/baselib/db/AppDatabase$l;

    invoke-direct {v0}, Lcom/transsion/baselib/db/AppDatabase$l;-><init>()V

    sput-object v0, Lcom/transsion/baselib/db/AppDatabase;->K:Lcom/transsion/baselib/db/AppDatabase$l;

    new-instance v0, Lcom/transsion/baselib/db/AppDatabase$m;

    invoke-direct {v0}, Lcom/transsion/baselib/db/AppDatabase$m;-><init>()V

    sput-object v0, Lcom/transsion/baselib/db/AppDatabase;->L:Lcom/transsion/baselib/db/AppDatabase$m;

    new-instance v0, Lcom/transsion/baselib/db/AppDatabase$n;

    invoke-direct {v0}, Lcom/transsion/baselib/db/AppDatabase$n;-><init>()V

    sput-object v0, Lcom/transsion/baselib/db/AppDatabase;->M:Lcom/transsion/baselib/db/AppDatabase$n;

    new-instance v0, Lcom/transsion/baselib/db/AppDatabase$o;

    invoke-direct {v0}, Lcom/transsion/baselib/db/AppDatabase$o;-><init>()V

    sput-object v0, Lcom/transsion/baselib/db/AppDatabase;->N:Lcom/transsion/baselib/db/AppDatabase$o;

    new-instance v0, Lcom/transsion/baselib/db/AppDatabase$p;

    invoke-direct {v0}, Lcom/transsion/baselib/db/AppDatabase$p;-><init>()V

    sput-object v0, Lcom/transsion/baselib/db/AppDatabase;->O:Lcom/transsion/baselib/db/AppDatabase$p;

    new-instance v0, Lcom/transsion/baselib/db/AppDatabase$q;

    invoke-direct {v0}, Lcom/transsion/baselib/db/AppDatabase$q;-><init>()V

    sput-object v0, Lcom/transsion/baselib/db/AppDatabase;->P:Lcom/transsion/baselib/db/AppDatabase$q;

    new-instance v0, Lcom/transsion/baselib/db/AppDatabase$r;

    invoke-direct {v0}, Lcom/transsion/baselib/db/AppDatabase$r;-><init>()V

    sput-object v0, Lcom/transsion/baselib/db/AppDatabase;->Q:Lcom/transsion/baselib/db/AppDatabase$r;

    new-instance v0, Lcom/transsion/baselib/db/AppDatabase$s;

    invoke-direct {v0}, Lcom/transsion/baselib/db/AppDatabase$s;-><init>()V

    sput-object v0, Lcom/transsion/baselib/db/AppDatabase;->R:Lcom/transsion/baselib/db/AppDatabase$s;

    new-instance v0, Lcom/transsion/baselib/db/AppDatabase$t;

    invoke-direct {v0}, Lcom/transsion/baselib/db/AppDatabase$t;-><init>()V

    sput-object v0, Lcom/transsion/baselib/db/AppDatabase;->S:Lcom/transsion/baselib/db/AppDatabase$t;

    new-instance v0, Lcom/transsion/baselib/db/AppDatabase$u;

    invoke-direct {v0}, Lcom/transsion/baselib/db/AppDatabase$u;-><init>()V

    sput-object v0, Lcom/transsion/baselib/db/AppDatabase;->T:Lcom/transsion/baselib/db/AppDatabase$u;

    new-instance v0, Lcom/transsion/baselib/db/AppDatabase$w;

    invoke-direct {v0}, Lcom/transsion/baselib/db/AppDatabase$w;-><init>()V

    sput-object v0, Lcom/transsion/baselib/db/AppDatabase;->U:Lcom/transsion/baselib/db/AppDatabase$w;

    new-instance v0, Lcom/transsion/baselib/db/AppDatabase$x;

    invoke-direct {v0}, Lcom/transsion/baselib/db/AppDatabase$x;-><init>()V

    sput-object v0, Lcom/transsion/baselib/db/AppDatabase;->V:Lcom/transsion/baselib/db/AppDatabase$x;

    new-instance v0, Lcom/transsion/baselib/db/AppDatabase$y;

    invoke-direct {v0}, Lcom/transsion/baselib/db/AppDatabase$y;-><init>()V

    sput-object v0, Lcom/transsion/baselib/db/AppDatabase;->W:Lcom/transsion/baselib/db/AppDatabase$y;

    new-instance v0, Lcom/transsion/baselib/db/AppDatabase$z;

    invoke-direct {v0}, Lcom/transsion/baselib/db/AppDatabase$z;-><init>()V

    sput-object v0, Lcom/transsion/baselib/db/AppDatabase;->X:Lcom/transsion/baselib/db/AppDatabase$z;

    new-instance v0, Lcom/transsion/baselib/db/AppDatabase$a0;

    invoke-direct {v0}, Lcom/transsion/baselib/db/AppDatabase$a0;-><init>()V

    sput-object v0, Lcom/transsion/baselib/db/AppDatabase;->Y:Lcom/transsion/baselib/db/AppDatabase$a0;

    new-instance v0, Lcom/transsion/baselib/db/AppDatabase$b0;

    invoke-direct {v0}, Lcom/transsion/baselib/db/AppDatabase$b0;-><init>()V

    sput-object v0, Lcom/transsion/baselib/db/AppDatabase;->Z:Lcom/transsion/baselib/db/AppDatabase$b0;

    new-instance v0, Lcom/transsion/baselib/db/AppDatabase$c0;

    invoke-direct {v0}, Lcom/transsion/baselib/db/AppDatabase$c0;-><init>()V

    sput-object v0, Lcom/transsion/baselib/db/AppDatabase;->a0:Lcom/transsion/baselib/db/AppDatabase$c0;

    new-instance v0, Lcom/transsion/baselib/db/AppDatabase$d0;

    invoke-direct {v0}, Lcom/transsion/baselib/db/AppDatabase$d0;-><init>()V

    sput-object v0, Lcom/transsion/baselib/db/AppDatabase;->b0:Lcom/transsion/baselib/db/AppDatabase$d0;

    new-instance v0, Lcom/transsion/baselib/db/AppDatabase$e0;

    invoke-direct {v0}, Lcom/transsion/baselib/db/AppDatabase$e0;-><init>()V

    sput-object v0, Lcom/transsion/baselib/db/AppDatabase;->c0:Lcom/transsion/baselib/db/AppDatabase$e0;

    new-instance v0, Lcom/transsion/baselib/db/AppDatabase$f0;

    invoke-direct {v0}, Lcom/transsion/baselib/db/AppDatabase$f0;-><init>()V

    sput-object v0, Lcom/transsion/baselib/db/AppDatabase;->d0:Lcom/transsion/baselib/db/AppDatabase$f0;

    new-instance v0, Lcom/transsion/baselib/db/AppDatabase$h0;

    invoke-direct {v0}, Lcom/transsion/baselib/db/AppDatabase$h0;-><init>()V

    sput-object v0, Lcom/transsion/baselib/db/AppDatabase;->e0:Lcom/transsion/baselib/db/AppDatabase$h0;

    new-instance v0, Lcom/transsion/baselib/db/AppDatabase$i0;

    invoke-direct {v0}, Lcom/transsion/baselib/db/AppDatabase$i0;-><init>()V

    sput-object v0, Lcom/transsion/baselib/db/AppDatabase;->f0:Lcom/transsion/baselib/db/AppDatabase$i0;

    new-instance v0, Lcom/transsion/baselib/db/AppDatabase$j0;

    invoke-direct {v0}, Lcom/transsion/baselib/db/AppDatabase$j0;-><init>()V

    sput-object v0, Lcom/transsion/baselib/db/AppDatabase;->g0:Lcom/transsion/baselib/db/AppDatabase$j0;

    new-instance v0, Lcom/transsion/baselib/db/AppDatabase$k0;

    invoke-direct {v0}, Lcom/transsion/baselib/db/AppDatabase$k0;-><init>()V

    sput-object v0, Lcom/transsion/baselib/db/AppDatabase;->h0:Lcom/transsion/baselib/db/AppDatabase$k0;

    new-instance v0, Lcom/transsion/baselib/db/AppDatabase$l0;

    invoke-direct {v0}, Lcom/transsion/baselib/db/AppDatabase$l0;-><init>()V

    sput-object v0, Lcom/transsion/baselib/db/AppDatabase;->i0:Lcom/transsion/baselib/db/AppDatabase$l0;

    new-instance v0, Lcom/transsion/baselib/db/AppDatabase$m0;

    invoke-direct {v0}, Lcom/transsion/baselib/db/AppDatabase$m0;-><init>()V

    sput-object v0, Lcom/transsion/baselib/db/AppDatabase;->j0:Lcom/transsion/baselib/db/AppDatabase$m0;

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Landroidx/room/RoomDatabase;-><init>()V

    return-void
.end method

.method public static final synthetic A0(Lcom/transsion/baselib/db/AppDatabase;)V
    .locals 0

    sput-object p0, Lcom/transsion/baselib/db/AppDatabase;->q:Lcom/transsion/baselib/db/AppDatabase;

    return-void
.end method

.method public static final synthetic G()Lcom/transsion/baselib/db/AppDatabase;
    .locals 1

    sget-object v0, Lcom/transsion/baselib/db/AppDatabase;->q:Lcom/transsion/baselib/db/AppDatabase;

    return-object v0
.end method

.method public static final synthetic H()Lcom/transsion/baselib/db/AppDatabase$a;
    .locals 1

    sget-object v0, Lcom/transsion/baselib/db/AppDatabase;->A:Lcom/transsion/baselib/db/AppDatabase$a;

    return-object v0
.end method

.method public static final synthetic I()Lcom/transsion/baselib/db/AppDatabase$b;
    .locals 1

    sget-object v0, Lcom/transsion/baselib/db/AppDatabase;->B:Lcom/transsion/baselib/db/AppDatabase$b;

    return-object v0
.end method

.method public static final synthetic J()Lcom/transsion/baselib/db/AppDatabase$c;
    .locals 1

    sget-object v0, Lcom/transsion/baselib/db/AppDatabase;->C:Lcom/transsion/baselib/db/AppDatabase$c;

    return-object v0
.end method

.method public static final synthetic K()Lcom/transsion/baselib/db/AppDatabase$d;
    .locals 1

    sget-object v0, Lcom/transsion/baselib/db/AppDatabase;->D:Lcom/transsion/baselib/db/AppDatabase$d;

    return-object v0
.end method

.method public static final synthetic L()Lcom/transsion/baselib/db/AppDatabase$e;
    .locals 1

    sget-object v0, Lcom/transsion/baselib/db/AppDatabase;->E:Lcom/transsion/baselib/db/AppDatabase$e;

    return-object v0
.end method

.method public static final synthetic M()Lcom/transsion/baselib/db/AppDatabase$f;
    .locals 1

    sget-object v0, Lcom/transsion/baselib/db/AppDatabase;->F:Lcom/transsion/baselib/db/AppDatabase$f;

    return-object v0
.end method

.method public static final synthetic N()Lcom/transsion/baselib/db/AppDatabase$g;
    .locals 1

    sget-object v0, Lcom/transsion/baselib/db/AppDatabase;->G:Lcom/transsion/baselib/db/AppDatabase$g;

    return-object v0
.end method

.method public static final synthetic O()Lcom/transsion/baselib/db/AppDatabase$h;
    .locals 1

    sget-object v0, Lcom/transsion/baselib/db/AppDatabase;->H:Lcom/transsion/baselib/db/AppDatabase$h;

    return-object v0
.end method

.method public static final synthetic P()Lcom/transsion/baselib/db/AppDatabase$i;
    .locals 1

    sget-object v0, Lcom/transsion/baselib/db/AppDatabase;->I:Lcom/transsion/baselib/db/AppDatabase$i;

    return-object v0
.end method

.method public static final synthetic Q()Lcom/transsion/baselib/db/AppDatabase$j;
    .locals 1

    sget-object v0, Lcom/transsion/baselib/db/AppDatabase;->J:Lcom/transsion/baselib/db/AppDatabase$j;

    return-object v0
.end method

.method public static final synthetic R()Lcom/transsion/baselib/db/AppDatabase$k;
    .locals 1

    sget-object v0, Lcom/transsion/baselib/db/AppDatabase;->r:Lcom/transsion/baselib/db/AppDatabase$k;

    return-object v0
.end method

.method public static final synthetic S()Lcom/transsion/baselib/db/AppDatabase$l;
    .locals 1

    sget-object v0, Lcom/transsion/baselib/db/AppDatabase;->K:Lcom/transsion/baselib/db/AppDatabase$l;

    return-object v0
.end method

.method public static final synthetic T()Lcom/transsion/baselib/db/AppDatabase$m;
    .locals 1

    sget-object v0, Lcom/transsion/baselib/db/AppDatabase;->L:Lcom/transsion/baselib/db/AppDatabase$m;

    return-object v0
.end method

.method public static final synthetic U()Lcom/transsion/baselib/db/AppDatabase$n;
    .locals 1

    sget-object v0, Lcom/transsion/baselib/db/AppDatabase;->M:Lcom/transsion/baselib/db/AppDatabase$n;

    return-object v0
.end method

.method public static final synthetic V()Lcom/transsion/baselib/db/AppDatabase$o;
    .locals 1

    sget-object v0, Lcom/transsion/baselib/db/AppDatabase;->N:Lcom/transsion/baselib/db/AppDatabase$o;

    return-object v0
.end method

.method public static final synthetic W()Lcom/transsion/baselib/db/AppDatabase$p;
    .locals 1

    sget-object v0, Lcom/transsion/baselib/db/AppDatabase;->O:Lcom/transsion/baselib/db/AppDatabase$p;

    return-object v0
.end method

.method public static final synthetic X()Lcom/transsion/baselib/db/AppDatabase$q;
    .locals 1

    sget-object v0, Lcom/transsion/baselib/db/AppDatabase;->P:Lcom/transsion/baselib/db/AppDatabase$q;

    return-object v0
.end method

.method public static final synthetic Y()Lcom/transsion/baselib/db/AppDatabase$r;
    .locals 1

    sget-object v0, Lcom/transsion/baselib/db/AppDatabase;->Q:Lcom/transsion/baselib/db/AppDatabase$r;

    return-object v0
.end method

.method public static final synthetic Z()Lcom/transsion/baselib/db/AppDatabase$s;
    .locals 1

    sget-object v0, Lcom/transsion/baselib/db/AppDatabase;->R:Lcom/transsion/baselib/db/AppDatabase$s;

    return-object v0
.end method

.method public static final synthetic a0()Lcom/transsion/baselib/db/AppDatabase$t;
    .locals 1

    sget-object v0, Lcom/transsion/baselib/db/AppDatabase;->S:Lcom/transsion/baselib/db/AppDatabase$t;

    return-object v0
.end method

.method public static final synthetic b0()Lcom/transsion/baselib/db/AppDatabase$u;
    .locals 1

    sget-object v0, Lcom/transsion/baselib/db/AppDatabase;->T:Lcom/transsion/baselib/db/AppDatabase$u;

    return-object v0
.end method

.method public static final synthetic c0()Lcom/transsion/baselib/db/AppDatabase$v;
    .locals 1

    sget-object v0, Lcom/transsion/baselib/db/AppDatabase;->s:Lcom/transsion/baselib/db/AppDatabase$v;

    return-object v0
.end method

.method public static final synthetic d0()Lcom/transsion/baselib/db/AppDatabase$w;
    .locals 1

    sget-object v0, Lcom/transsion/baselib/db/AppDatabase;->U:Lcom/transsion/baselib/db/AppDatabase$w;

    return-object v0
.end method

.method public static final synthetic e0()Lcom/transsion/baselib/db/AppDatabase$x;
    .locals 1

    sget-object v0, Lcom/transsion/baselib/db/AppDatabase;->V:Lcom/transsion/baselib/db/AppDatabase$x;

    return-object v0
.end method

.method public static final synthetic f0()Lcom/transsion/baselib/db/AppDatabase$y;
    .locals 1

    sget-object v0, Lcom/transsion/baselib/db/AppDatabase;->W:Lcom/transsion/baselib/db/AppDatabase$y;

    return-object v0
.end method

.method public static final synthetic g0()Lcom/transsion/baselib/db/AppDatabase$z;
    .locals 1

    sget-object v0, Lcom/transsion/baselib/db/AppDatabase;->X:Lcom/transsion/baselib/db/AppDatabase$z;

    return-object v0
.end method

.method public static final synthetic h0()Lcom/transsion/baselib/db/AppDatabase$a0;
    .locals 1

    sget-object v0, Lcom/transsion/baselib/db/AppDatabase;->Y:Lcom/transsion/baselib/db/AppDatabase$a0;

    return-object v0
.end method

.method public static final synthetic i0()Lcom/transsion/baselib/db/AppDatabase$b0;
    .locals 1

    sget-object v0, Lcom/transsion/baselib/db/AppDatabase;->Z:Lcom/transsion/baselib/db/AppDatabase$b0;

    return-object v0
.end method

.method public static final synthetic j0()Lcom/transsion/baselib/db/AppDatabase$c0;
    .locals 1

    sget-object v0, Lcom/transsion/baselib/db/AppDatabase;->a0:Lcom/transsion/baselib/db/AppDatabase$c0;

    return-object v0
.end method

.method public static final synthetic k0()Lcom/transsion/baselib/db/AppDatabase$d0;
    .locals 1

    sget-object v0, Lcom/transsion/baselib/db/AppDatabase;->b0:Lcom/transsion/baselib/db/AppDatabase$d0;

    return-object v0
.end method

.method public static final synthetic l0()Lcom/transsion/baselib/db/AppDatabase$e0;
    .locals 1

    sget-object v0, Lcom/transsion/baselib/db/AppDatabase;->c0:Lcom/transsion/baselib/db/AppDatabase$e0;

    return-object v0
.end method

.method public static final synthetic m0()Lcom/transsion/baselib/db/AppDatabase$f0;
    .locals 1

    sget-object v0, Lcom/transsion/baselib/db/AppDatabase;->d0:Lcom/transsion/baselib/db/AppDatabase$f0;

    return-object v0
.end method

.method public static final synthetic n0()Lcom/transsion/baselib/db/AppDatabase$g0;
    .locals 1

    sget-object v0, Lcom/transsion/baselib/db/AppDatabase;->t:Lcom/transsion/baselib/db/AppDatabase$g0;

    return-object v0
.end method

.method public static final synthetic o0()Lcom/transsion/baselib/db/AppDatabase$h0;
    .locals 1

    sget-object v0, Lcom/transsion/baselib/db/AppDatabase;->e0:Lcom/transsion/baselib/db/AppDatabase$h0;

    return-object v0
.end method

.method public static final synthetic p0()Lcom/transsion/baselib/db/AppDatabase$i0;
    .locals 1

    sget-object v0, Lcom/transsion/baselib/db/AppDatabase;->f0:Lcom/transsion/baselib/db/AppDatabase$i0;

    return-object v0
.end method

.method public static final synthetic q0()Lcom/transsion/baselib/db/AppDatabase$j0;
    .locals 1

    sget-object v0, Lcom/transsion/baselib/db/AppDatabase;->g0:Lcom/transsion/baselib/db/AppDatabase$j0;

    return-object v0
.end method

.method public static final synthetic r0()Lcom/transsion/baselib/db/AppDatabase$k0;
    .locals 1

    sget-object v0, Lcom/transsion/baselib/db/AppDatabase;->h0:Lcom/transsion/baselib/db/AppDatabase$k0;

    return-object v0
.end method

.method public static final synthetic s0()Lcom/transsion/baselib/db/AppDatabase$l0;
    .locals 1

    sget-object v0, Lcom/transsion/baselib/db/AppDatabase;->i0:Lcom/transsion/baselib/db/AppDatabase$l0;

    return-object v0
.end method

.method public static final synthetic t0()Lcom/transsion/baselib/db/AppDatabase$m0;
    .locals 1

    sget-object v0, Lcom/transsion/baselib/db/AppDatabase;->j0:Lcom/transsion/baselib/db/AppDatabase$m0;

    return-object v0
.end method

.method public static final synthetic u0()Lcom/transsion/baselib/db/AppDatabase$n0;
    .locals 1

    sget-object v0, Lcom/transsion/baselib/db/AppDatabase;->u:Lcom/transsion/baselib/db/AppDatabase$n0;

    return-object v0
.end method

.method public static final synthetic v0()Lcom/transsion/baselib/db/AppDatabase$o0;
    .locals 1

    sget-object v0, Lcom/transsion/baselib/db/AppDatabase;->v:Lcom/transsion/baselib/db/AppDatabase$o0;

    return-object v0
.end method

.method public static final synthetic w0()Lcom/transsion/baselib/db/AppDatabase$p0;
    .locals 1

    sget-object v0, Lcom/transsion/baselib/db/AppDatabase;->w:Lcom/transsion/baselib/db/AppDatabase$p0;

    return-object v0
.end method

.method public static final synthetic x0()Lcom/transsion/baselib/db/AppDatabase$q0;
    .locals 1

    sget-object v0, Lcom/transsion/baselib/db/AppDatabase;->x:Lcom/transsion/baselib/db/AppDatabase$q0;

    return-object v0
.end method

.method public static final synthetic y0()Lcom/transsion/baselib/db/AppDatabase$r0;
    .locals 1

    sget-object v0, Lcom/transsion/baselib/db/AppDatabase;->y:Lcom/transsion/baselib/db/AppDatabase$r0;

    return-object v0
.end method

.method public static final synthetic z0()Lcom/transsion/baselib/db/AppDatabase$s0;
    .locals 1

    sget-object v0, Lcom/transsion/baselib/db/AppDatabase;->z:Lcom/transsion/baselib/db/AppDatabase$s0;

    return-object v0
.end method


# virtual methods
.method public abstract B0()Lcl/a;
.end method

.method public abstract C0()Lel/c;
.end method

.method public abstract D0()Lel/h;
.end method

.method public abstract E0()Lil/a;
.end method

.method public abstract F0()Lgl/a;
.end method

.method public abstract G0()Lcom/transsion/baselib/db/place/PlaceDao;
.end method

.method public abstract H0()Ljl/a;
.end method

.method public abstract I0()Lcom/transsion/baselib/db/video/ShortTVPlayDao;
.end method

.method public abstract J0()Lcom/transsion/baselib/db/video/IShortTvFavoriteStateDao;
.end method

.method public abstract K0()Lel/m;
.end method

.method public abstract L0()Lel/o;
.end method

.method public abstract M0()Lcom/transsion/baselib/db/video/VideoDetailPlayDao;
.end method

.method public abstract N0()Lcom/transsion/baselib/db/video/f;
.end method
