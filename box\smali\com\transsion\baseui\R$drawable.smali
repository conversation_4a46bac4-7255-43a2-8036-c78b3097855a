.class public final Lcom/transsion/baseui/R$drawable;
.super Ljava/lang/Object;


# static fields
.field public static bg_btn_01:I = 0x7f0800cc

.field public static bg_btn_01_new:I = 0x7f0800cd

.field public static bg_btn_01_radius_20:I = 0x7f0800ce

.field public static bg_btn_01_radius_4:I = 0x7f0800cf

.field public static bg_btn_module_07:I = 0x7f0800d3

.field public static bg_btn_radius_20:I = 0x7f0800d5

.field public static bg_clear_dialog:I = 0x7f0800df

.field public static bg_comfirm:I = 0x7f0800e0

.field public static bg_days_left:I = 0x7f0800e7

.field public static bg_detector_btn_8dp:I = 0x7f0800e8

.field public static bg_feed_item_edu_add:I = 0x7f0800f1

.field public static bg_movie_search:I = 0x7f08011d

.field public static bg_music_floating0_4_4_0:I = 0x7f080121

.field public static bg_num_text_6dp_bottom_left:I = 0x7f080123

.field public static bg_num_text_6dp_bottom_right:I = 0x7f080124

.field public static bg_progress_bar_music:I = 0x7f080136

.field public static bg_r12_color_linear:I = 0x7f08013f

.field public static bg_radius2_white20:I = 0x7f080140

.field public static bg_radius4_black60:I = 0x7f080141

.field public static bg_radius_12_color_white_50:I = 0x7f080142

.field public static bg_radius_12_f03930:I = 0x7f080143

.field public static bg_radius_14_color_f7f7f7:I = 0x7f080144

.field public static bg_radius_20_color_brand_stroke:I = 0x7f080145

.field public static bg_radius_20_color_module10:I = 0x7f080146

.field public static bg_radius_4_color_f7f7f7:I = 0x7f080147

.field public static bg_radius_4_color_white:I = 0x7f080148

.field public static bg_radius_4_color_white_10p:I = 0x7f080149

.field public static bg_radius_5_color_f7f7f7:I = 0x7f08014a

.field public static bg_radius_6_color_white:I = 0x7f08014b

.field public static bg_radius_6_color_white_6p:I = 0x7f08014c

.field public static bg_radius_8_color_white_10p:I = 0x7f08014d

.field public static bg_radius_bottom_6_color_white_6p:I = 0x7f08014e

.field public static bg_radius_top_6_color_white_6p:I = 0x7f08014f

.field public static bg_red_notice:I = 0x7f080151

.field public static bg_red_r7:I = 0x7f080152

.field public static bg_shape_newcomer_guide_button:I = 0x7f080165

.field public static bg_shape_newcomer_guide_target:I = 0x7f080167

.field public static bg_shape_newcomer_guide_target_anima:I = 0x7f080168

.field public static bg_white_top_16dp_light:I = 0x7f080184

.field public static comment_input_bg:I = 0x7f0801a2

.field public static divider_4_grey:I = 0x7f0801c2

.field public static download_bg:I = 0x7f0801c7

.field public static download_bg_white:I = 0x7f0801c9

.field public static ic_add_text01:I = 0x7f080245

.field public static ic_add_white:I = 0x7f080246

.field public static ic_arrow_down:I = 0x7f080248

.field public static ic_arrow_up:I = 0x7f08024a

.field public static ic_backward_10:I = 0x7f08024f

.field public static ic_close_text_01:I = 0x7f080261

.field public static ic_forward_10:I = 0x7f08027b

.field public static ic_group_def_bg:I = 0x7f08027c

.field public static ic_history:I = 0x7f080283

.field public static ic_movie_comment:I = 0x7f0802a1

.field public static ic_ok_text01:I = 0x7f0802ab

.field public static ic_play_next:I = 0x7f0802ad

.field public static ic_player_pause:I = 0x7f0802b0

.field public static ic_player_play:I = 0x7f0802b1

.field public static ic_player_play_24:I = 0x7f0802b2

.field public static ic_post_comment:I = 0x7f0802b4

.field public static ic_premium_tab:I = 0x7f0802b8

.field public static ic_preview:I = 0x7f0802b9

.field public static ic_quick_speed:I = 0x7f0802bb

.field public static ic_reset_text_01:I = 0x7f0802bc

.field public static ic_search:I = 0x7f0802c3

.field public static ic_search_fit_dark:I = 0x7f0802c6

.field public static ic_square_checked:I = 0x7f0802d0

.field public static ic_square_unchecked:I = 0x7f0802d1

.field public static ic_subject_favorite:I = 0x7f0802d4

.field public static ic_subject_favorite_selected:I = 0x7f0802d5

.field public static ic_tag_audio:I = 0x7f0802d6

.field public static ic_tag_edu:I = 0x7f0802d7

.field public static ic_tag_local_file:I = 0x7f0802d8

.field public static ic_tag_movie:I = 0x7f0802d9

.field public static ic_tag_music:I = 0x7f0802da

.field public static ic_tag_tv:I = 0x7f0802db

.field public static iv_music_floating_icon:I = 0x7f0802fa

.field public static link_input_edit_bg:I = 0x7f080321

.field public static member_bg_8:I = 0x7f0803ee

.field public static movie_detail_room_bg:I = 0x7f0803f4

.field public static movie_detail_subject_bg:I = 0x7f0803f5

.field public static music_float_close:I = 0x7f08041f

.field public static music_float_pause:I = 0x7f080420

.field public static music_float_play:I = 0x7f080421

.field public static post_detail_bg_0_0:I = 0x7f080451

.field public static post_detail_layer_horizontal_progress:I = 0x7f08045b

.field public static selector_arrow_expand_ic:I = 0x7f0804c9

.field public static selector_btn_interest_bg:I = 0x7f0804ca

.field public static selector_check_square:I = 0x7f0804cb

.field public static selector_ic_course_add_subject:I = 0x7f0804d3

.field public static selector_subject_favorite_icon:I = 0x7f0804d8

.field public static update_bottom_bg:I = 0x7f080609

.field public static update_button_shape:I = 0x7f08060b

.field public static update_ic_checked:I = 0x7f08060e

.field public static update_ic_unchecked:I = 0x7f08060f


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
