.class public final Lcom/transsion/baseui/R$id;
.super Ljava/lang/Object;


# static fields
.field public static b1:I = 0x7f0a00b2

.field public static b2:I = 0x7f0a00b3

.field public static b3:I = 0x7f0a00b4

.field public static bottom_layout:I = 0x7f0a00da

.field public static bottom_to_top:I = 0x7f0a00e4

.field public static card_detail:I = 0x7f0a012b

.field public static card_tip_gray:I = 0x7f0a012d

.field public static card_tip_white:I = 0x7f0a012e

.field public static card_title:I = 0x7f0a012f

.field public static centerControlLayout:I = 0x7f0a0136

.field public static clRoot:I = 0x7f0a0154

.field public static contentScrollView:I = 0x7f0a0196

.field public static contentScrollViewBottom:I = 0x7f0a0197

.field public static contentScrollViewTop:I = 0x7f0a0198

.field public static currentVersionTv:I = 0x7f0a01aa

.field public static divider1:I = 0x7f0a01d0

.field public static divider2:I = 0x7f0a01d1

.field public static divider3:I = 0x7f0a01d2

.field public static divider4:I = 0x7f0a01d3

.field public static divider5:I = 0x7f0a01d4

.field public static divider6:I = 0x7f0a01d5

.field public static divider7:I = 0x7f0a01d6

.field public static divider8:I = 0x7f0a01d7

.field public static divider9:I = 0x7f0a01d8

.field public static fl:I = 0x7f0a026c

.field public static flContent:I = 0x7f0a0273

.field public static flStateView:I = 0x7f0a0281

.field public static guideline:I = 0x7f0a02eb

.field public static header_d:I = 0x7f0a02f1

.field public static horizontal:I = 0x7f0a0308

.field public static ignoreUpdateTips:I = 0x7f0a031c

.field public static ivAdLoading:I = 0x7f0a037f

.field public static ivBack:I = 0x7f0a0385

.field public static ivBackward:I = 0x7f0a0386

.field public static ivCenterPause:I = 0x7f0a038c

.field public static ivClose:I = 0x7f0a038e

.field public static ivCover:I = 0x7f0a0393

.field public static ivCoverBg:I = 0x7f0a0394

.field public static ivForward:I = 0x7f0a039f

.field public static ivPlayPause:I = 0x7f0a03b8

.field public static ivSubjectIcon:I = 0x7f0a03c8

.field public static iv_centerView:I = 0x7f0a03e8

.field public static iv_guide_line:I = 0x7f0a0413

.field public static iv_guide_target:I = 0x7f0a0414

.field public static iv_room_cover:I = 0x7f0a046b

.field public static iv_subject_cover:I = 0x7f0a0484

.field public static left_to_right:I = 0x7f0a04e2

.field public static linear:I = 0x7f0a04eb

.field public static llResource:I = 0x7f0a04fa

.field public static llRootView:I = 0x7f0a04fc

.field public static ll_ignore:I = 0x7f0a0516

.field public static ll_link:I = 0x7f0a051b

.field public static ll_room:I = 0x7f0a0526

.field public static ll_subject:I = 0x7f0a052a

.field public static load_view:I = 0x7f0a0539

.field public static mRefreshLayout:I = 0x7f0a054e

.field public static mRv:I = 0x7f0a054f

.field public static mTitleLayout:I = 0x7f0a0550

.field public static numText:I = 0x7f0a06c7

.field public static post_detail_loading:I = 0x7f0a073b

.field public static progress:I = 0x7f0a0741

.field public static progressBar:I = 0x7f0a0742

.field public static radial:I = 0x7f0a0756

.field public static restart:I = 0x7f0a078a

.field public static reverse:I = 0x7f0a078d

.field public static right_to_left:I = 0x7f0a079a

.field public static roundExpand:I = 0x7f0a07b0

.field public static roundFold:I = 0x7f0a07b1

.field public static space:I = 0x7f0a0849

.field public static toolbar_d:I = 0x7f0a091a

.field public static top_to_bottom:I = 0x7f0a0928

.field public static tvDescription:I = 0x7f0a097e

.field public static tvLoading:I = 0x7f0a099d

.field public static tvSubjectDownload:I = 0x7f0a09db

.field public static tvSubjectName:I = 0x7f0a09dc

.field public static tvTitle:I = 0x7f0a09ea

.field public static tv_complete:I = 0x7f0a0a1b

.field public static tv_err:I = 0x7f0a0a3e

.field public static tv_guide_button:I = 0x7f0a0a57

.field public static tv_guide_tips:I = 0x7f0a0a58

.field public static tv_no_more:I = 0x7f0a0a96

.field public static tv_room:I = 0x7f0a0ad3

.field public static tv_subject:I = 0x7f0a0b02

.field public static tv_tips:I = 0x7f0a0b15

.field public static updateBottomDivider:I = 0x7f0a0b45

.field public static updateContentTv:I = 0x7f0a0b46

.field public static updateIgnoreCb:I = 0x7f0a0b47

.field public static updateNegativeBtn:I = 0x7f0a0b48

.field public static updatePositiveBtn:I = 0x7f0a0b49

.field public static updateTitleTv:I = 0x7f0a0b4a

.field public static updateTop:I = 0x7f0a0b4b

.field public static updateVersionTv:I = 0x7f0a0b4c

.field public static v_guide_anima_bg:I = 0x7f0a0b75

.field public static v_guide_bg:I = 0x7f0a0b76

.field public static vertical:I = 0x7f0a0bd0

.field public static view1:I = 0x7f0a0bd9

.field public static view2:I = 0x7f0a0bdc

.field public static view3:I = 0x7f0a0bdd

.field public static view4:I = 0x7f0a0bde

.field public static view5:I = 0x7f0a0bdf

.field public static viewBg:I = 0x7f0a0be6

.field public static viewLoad:I = 0x7f0a0bf4


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
