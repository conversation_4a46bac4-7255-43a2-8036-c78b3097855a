.class public final Lcom/transsion/baseui/R$layout;
.super Ljava/lang/Object;


# static fields
.field public static base_dialog_loading:I = 0x7f0d00a3

.field public static base_item_load_more:I = 0x7f0d00a5

.field public static base_layout_with_no_network:I = 0x7f0d00a6

.field public static base_list_fragment_layout:I = 0x7f0d00a7

.field public static default_base_layout:I = 0x7f0d00b0

.field public static default_base_loading_layout:I = 0x7f0d00b1

.field public static default_item_empty_layout:I = 0x7f0d00b2

.field public static default_list_loading_view:I = 0x7f0d00b3

.field public static default_rv_loading_layout:I = 0x7f0d00b6

.field public static fragment_default_base_layout:I = 0x7f0d0137

.field public static layout_newcomer_guide:I = 0x7f0d0295

.field public static layout_play_center_control:I = 0x7f0d029a

.field public static member_dialog_loading:I = 0x7f0d032d

.field public static movie_detail_item_resource:I = 0x7f0d033a

.field public static movie_detail_item_room:I = 0x7f0d033b

.field public static movie_detail_item_subject:I = 0x7f0d033d

.field public static music_float_layout:I = 0x7f0d0364

.field public static resources_request:I = 0x7f0d03f8

.field public static tupdate_layout_upgrade_dialog:I = 0x7f0d041a

.field public static view_request_resource:I = 0x7f0d044f


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
