.class public final Lcom/transsion/baseui/R$string;
.super Ljava/lang/Object;


# static fields
.field public static Newest:I = 0x7f120007

.field public static Popular:I = 0x7f120008

.field public static back:I = 0x7f12005a

.field public static base_app_name:I = 0x7f12005c

.field public static base_just_now:I = 0x7f12005d

.field public static base_load_err:I = 0x7f12005e

.field public static base_load_no_more:I = 0x7f12005f

.field public static base_loading:I = 0x7f120060

.field public static base_net_err:I = 0x7f120061

.field public static base_network_fail:I = 0x7f120062

.field public static base_select_grant_permission:I = 0x7f120063

.field public static base_select_no_permission:I = 0x7f120064

.field public static base_ui_connect_to_internet:I = 0x7f120065

.field public static base_ui_date:I = 0x7f120066

.field public static base_ui_size:I = 0x7f120067

.field public static base_ui_source:I = 0x7f120068

.field public static base_ui_uploaded_by:I = 0x7f120069

.field public static delete_post_failed:I = 0x7f120125

.field public static download_in_background:I = 0x7f12014d

.field public static download_movie:I = 0x7f120155

.field public static education_add_course:I = 0x7f1201e6

.field public static education_added:I = 0x7f1201e7

.field public static explore:I = 0x7f120225

.field public static fit:I = 0x7f12026f

.field public static for_you:I = 0x7f120272

.field public static free_download_tips:I = 0x7f120273

.field public static give_me_it:I = 0x7f120279

.field public static guide_no_network_tip:I = 0x7f120285

.field public static insufficient_storage_available:I = 0x7f1202b8

.field public static language_x:I = 0x7f1202c5

.field public static limit_des:I = 0x7f1202d2

.field public static mem_loading:I = 0x7f120377

.field public static movie_title:I = 0x7f1203fe

.field public static movie_uploaded_by:I = 0x7f1203ff

.field public static my_comments:I = 0x7f120449

.field public static name_comments:I = 0x7f12044d

.field public static nearby:I = 0x7f120455

.field public static not_show_again:I = 0x7f12047b

.field public static notification:I = 0x7f12047c

.field public static pip:I = 0x7f1204d0

.field public static play:I = 0x7f1204d9

.field public static play_next:I = 0x7f1204db

.field public static play_tap_lock:I = 0x7f1204dc

.field public static play_tap_unlock:I = 0x7f1204dd

.field public static profile_my_offline:I = 0x7f120530

.field public static resource:I = 0x7f120576

.field public static resource_request:I = 0x7f120578

.field public static resource_request_detail:I = 0x7f120579

.field public static resource_request_tip:I = 0x7f12057a

.field public static retry_text:I = 0x7f12057c

.field public static reviews:I = 0x7f12057d

.field public static show_notification_widget:I = 0x7f1205e3

.field public static show_notification_widget_tips:I = 0x7f1205e4

.field public static skip_ad:I = 0x7f1205ea

.field public static submission_fail:I = 0x7f12063e

.field public static submission_successful:I = 0x7f12063f

.field public static tap_again_to_exit:I = 0x7f120674

.field public static want_to_see_share:I = 0x7f1207d7


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
