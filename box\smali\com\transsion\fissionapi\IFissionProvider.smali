.class public interface abstract Lcom/transsion/fissionapi/IFissionProvider;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/alibaba/android/arouter/facade/template/IProvider;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/transsion/fissionapi/IFissionProvider$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation


# virtual methods
.method public abstract A0(Landroid/content/Context;)V
.end method

.method public abstract D0(I)V
.end method

.method public abstract F(Lkotlin/jvm/functions/Function2;)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function2<",
            "-",
            "Ljava/lang/<PERSON>olean;",
            "-",
            "Ljava/lang/Boolean;",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation
.end method

.method public abstract G(Ljava/lang/String;)V
.end method

.method public abstract I0()Z
.end method

.method public abstract K0()Ljava/lang/String;
.end method

.method public abstract W(Z)V
.end method

.method public abstract enable()Z
.end method

.method public abstract n0(Z)V
.end method

.method public abstract u1(Z)V
.end method

.method public abstract y0()Ljava/lang/String;
.end method
