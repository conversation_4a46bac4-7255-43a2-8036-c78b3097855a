.class public final synthetic Lcom/transsion/ga/i;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/transsion/ga/AthenaAnalytics$f;


# direct methods
.method public synthetic constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final a(Ljava/lang/String;)Ljava/lang/String;
    .locals 0

    invoke-static {p1}, Lcom/transsion/ga/AthenaAnalytics;->a(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method
