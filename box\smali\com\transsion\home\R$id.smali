.class public final Lcom/transsion/home/<USER>
.super Ljava/lang/Object;


# static fields
.field public static adContainer:I = 0x7f0a0070

.field public static ad_container:I = 0x7f0a007e

.field public static app_bar:I = 0x7f0a00a1

.field public static banner:I = 0x7f0a00b6

.field public static bg:I = 0x7f0a00c6

.field public static bg_gradient:I = 0x7f0a00cb

.field public static bottom_op_container:I = 0x7f0a00dd

.field public static bottom_op_mb_logo:I = 0x7f0a00de

.field public static bottom_op_search_appstore:I = 0x7f0a00df

.field public static bottom_op_search_gamestore:I = 0x7f0a00e0

.field public static bottom_op_search_liner:I = 0x7f0a00e1

.field public static bottom_op_search_text:I = 0x7f0a00e2

.field public static btnClaim:I = 0x7f0a00f0

.field public static bubble_container:I = 0x7f0a011d

.field public static cardPager:I = 0x7f0a0128

.field public static center:I = 0x7f0a0134

.field public static channelExpand:I = 0x7f0a013e

.field public static check_list:I = 0x7f0a013f

.field public static clRoot:I = 0x7f0a0154

.field public static cl_cover:I = 0x7f0a0160

.field public static container:I = 0x7f0a0190

.field public static contentIV:I = 0x7f0a0192

.field public static contentIVContainer:I = 0x7f0a0193

.field public static desTV:I = 0x7f0a01b8

.field public static desc:I = 0x7f0a01ba

.field public static divider:I = 0x7f0a01cf

.field public static filterExpand:I = 0x7f0a025e

.field public static filter_item_name:I = 0x7f0a025f

.field public static filter_name_1:I = 0x7f0a0260

.field public static flGameCenter:I = 0x7f0a0276

.field public static flPhoneCenter:I = 0x7f0a027b

.field public static flWidget:I = 0x7f0a0287

.field public static fl_limit:I = 0x7f0a029a

.field public static flow_layout:I = 0x7f0a02b5

.field public static game_list:I = 0x7f0a02be

.field public static group:I = 0x7f0a02cc

.field public static home_sub_pager_item_image:I = 0x7f0a0302

.field public static home_sub_pager_item_mute:I = 0x7f0a0303

.field public static home_sub_pager_item_texture:I = 0x7f0a0304

.field public static home_sub_pager_items_status:I = 0x7f0a0305

.field public static home_sub_pager_items_top_mask:I = 0x7f0a0306

.field public static ic_player:I = 0x7f0a030d

.field public static image_red_tips:I = 0x7f0a0325

.field public static image_tab_icon:I = 0x7f0a0326

.field public static interestLayout:I = 0x7f0a0344

.field public static item1:I = 0x7f0a034c

.field public static item2:I = 0x7f0a034d

.field public static item3:I = 0x7f0a034e

.field public static itemClaimLayout:I = 0x7f0a034f

.field public static item_honor_background:I = 0x7f0a036d

.field public static item_honor_content:I = 0x7f0a036e

.field public static item_honor_dot_end:I = 0x7f0a036f

.field public static item_honor_dot_start:I = 0x7f0a0370

.field public static item_honor_level_icon:I = 0x7f0a0371

.field public static item_honor_level_subtitle:I = 0x7f0a0372

.field public static item_honor_level_title:I = 0x7f0a0373

.field public static item_root:I = 0x7f0a0377

.field public static ivBack:I = 0x7f0a0385

.field public static ivCover:I = 0x7f0a0393

.field public static ivIcon:I = 0x7f0a03a4

.field public static ivUpdateApp:I = 0x7f0a03cd

.field public static iv_arrow:I = 0x7f0a03d6

.field public static iv_cast:I = 0x7f0a03e7

.field public static iv_close:I = 0x7f0a03ec

.field public static iv_cover:I = 0x7f0a03f3

.field public static iv_download_fail:I = 0x7f0a0400

.field public static iv_ges:I = 0x7f0a0411

.field public static iv_icon:I = 0x7f0a0419

.field public static iv_limit_cover:I = 0x7f0a0424

.field public static iv_mb_logo:I = 0x7f0a042b

.field public static iv_playlist_page_corner:I = 0x7f0a0447

.field public static iv_rounded:I = 0x7f0a046c

.field public static iv_search_container:I = 0x7f0a0472

.field public static iv_stills:I = 0x7f0a0482

.field public static iv_tag:I = 0x7f0a048a

.field public static iv_top_cover:I = 0x7f0a0499

.field public static l1:I = 0x7f0a04a7

.field public static l2:I = 0x7f0a04a8

.field public static l3:I = 0x7f0a04a9

.field public static l4:I = 0x7f0a04aa

.field public static l5:I = 0x7f0a04ab

.field public static l6:I = 0x7f0a04ac

.field public static ll_download:I = 0x7f0a050d

.field public static ll_subject:I = 0x7f0a052a

.field public static ll_tab:I = 0x7f0a052c

.field public static ll_tab_filter:I = 0x7f0a052d

.field public static ll_tab_movie:I = 0x7f0a052e

.field public static loading_bg:I = 0x7f0a053d

.field public static loading_pb:I = 0x7f0a053f

.field public static loading_stub:I = 0x7f0a0541

.field public static magicIndicator:I = 0x7f0a0551

.field public static magic_indicator:I = 0x7f0a0552

.field public static main_op_honor_image:I = 0x7f0a0557

.field public static main_op_honor_more_mask:I = 0x7f0a0558

.field public static main_op_honor_more_text:I = 0x7f0a0559

.field public static main_op_movie_rank_corner:I = 0x7f0a055a

.field public static main_op_movie_rank_image:I = 0x7f0a055b

.field public static main_op_movie_rank_more_mask:I = 0x7f0a055c

.field public static main_op_movie_rank_title:I = 0x7f0a055d

.field public static main_op_sport_live_image:I = 0x7f0a055e

.field public static main_op_sport_live_status_text:I = 0x7f0a055f

.field public static main_op_sport_live_team1_container:I = 0x7f0a0560

.field public static main_op_sport_live_team1_image:I = 0x7f0a0561

.field public static main_op_sport_live_team2_image:I = 0x7f0a0562

.field public static main_op_sport_live_time_text:I = 0x7f0a0563

.field public static main_op_sport_live_title_text:I = 0x7f0a0564

.field public static main_op_sport_live_vs:I = 0x7f0a0565

.field public static main_operation_movie_more_text:I = 0x7f0a0566

.field public static main_operation_movie_rank_recycler:I = 0x7f0a0567

.field public static main_operation_movie_rank_title:I = 0x7f0a0568

.field public static main_operation_sport_live_recycler:I = 0x7f0a0569

.field public static main_operation_sport_live_title:I = 0x7f0a056a

.field public static main_operation_sport_more_text:I = 0x7f0a056b

.field public static movie_list:I = 0x7f0a0667

.field public static name:I = 0x7f0a0686

.field public static no_network:I = 0x7f0a06a8

.field public static no_network_stub:I = 0x7f0a06a9

.field public static no_result_stub:I = 0x7f0a06ac

.field public static op_item_honor_gradient_bg:I = 0x7f0a06d2

.field public static op_item_honor_recycler:I = 0x7f0a06d3

.field public static op_item_honor_space:I = 0x7f0a06d4

.field public static op_item_number_rank_corner:I = 0x7f0a06d5

.field public static op_item_number_rank_index_image:I = 0x7f0a06d6

.field public static op_item_number_rank_poster:I = 0x7f0a06d7

.field public static op_item_number_rank_title:I = 0x7f0a06d8

.field public static open_network_tip:I = 0x7f0a06d9

.field public static operate_page_container:I = 0x7f0a06eb

.field public static operate_page_toolbar:I = 0x7f0a06ec

.field public static playIV:I = 0x7f0a0728

.field public static play_list_title:I = 0x7f0a0729

.field public static play_list_view:I = 0x7f0a072a

.field public static popup_filter_view:I = 0x7f0a072e

.field public static popup_filter_view_linear:I = 0x7f0a072f

.field public static postTitle:I = 0x7f0a0738

.field public static post_item_room_container:I = 0x7f0a073c

.field public static post_title_container:I = 0x7f0a073e

.field public static rank_all_category_container:I = 0x7f0a075e

.field public static rank_all_category_item_fragment:I = 0x7f0a075f

.field public static rank_all_error:I = 0x7f0a0760

.field public static rank_all_list_recycler:I = 0x7f0a0761

.field public static rank_all_loading_frame:I = 0x7f0a0762

.field public static rank_all_title:I = 0x7f0a0763

.field public static rank_item_corner:I = 0x7f0a0764

.field public static rank_item_des:I = 0x7f0a0765

.field public static rank_item_download:I = 0x7f0a0766

.field public static rank_item_image:I = 0x7f0a0767

.field public static rank_item_imdb:I = 0x7f0a0768

.field public static rank_item_rank_duration:I = 0x7f0a0769

.field public static rank_item_rank_tag:I = 0x7f0a076a

.field public static rank_item_tag_rank:I = 0x7f0a076b

.field public static rank_item_title:I = 0x7f0a076c

.field public static rank_list_loading_frame:I = 0x7f0a076d

.field public static ranking_view:I = 0x7f0a076e

.field public static recycleView:I = 0x7f0a077e

.field public static recycler_view:I = 0x7f0a0780

.field public static root:I = 0x7f0a07ad

.field public static rvTabs:I = 0x7f0a07c1

.field public static rv_list:I = 0x7f0a07c5

.field public static search_left_container:I = 0x7f0a07f5

.field public static state_view:I = 0x7f0a0862

.field public static sub_movie_header_bg:I = 0x7f0a086c

.field public static sub_operation_appointment_booked:I = 0x7f0a086d

.field public static sub_operation_appointment_checked:I = 0x7f0a086e

.field public static sub_operation_appointment_checked_icon:I = 0x7f0a086f

.field public static sub_operation_appointment_corner:I = 0x7f0a0870

.field public static sub_operation_appointment_image:I = 0x7f0a0871

.field public static sub_operation_appointment_item_title:I = 0x7f0a0872

.field public static sub_operation_appointment_recycle:I = 0x7f0a0873

.field public static sub_operation_appointment_title:I = 0x7f0a0874

.field public static sub_operation_appointment_unchecked:I = 0x7f0a0875

.field public static sub_operation_appointment_unchecked_icon:I = 0x7f0a0876

.field public static sub_operation_appointment_unchecked_text:I = 0x7f0a0877

.field public static sub_operation_banner_bg:I = 0x7f0a0878

.field public static sub_operation_banner_download:I = 0x7f0a0879

.field public static sub_operation_banner_space:I = 0x7f0a087a

.field public static sub_operation_banner_title:I = 0x7f0a087b

.field public static sub_operation_card_helper:I = 0x7f0a087c

.field public static sub_operation_course_explore:I = 0x7f0a087d

.field public static sub_operation_course_item_image:I = 0x7f0a087e

.field public static sub_operation_course_item_liner:I = 0x7f0a087f

.field public static sub_operation_course_item_percent_bg:I = 0x7f0a0880

.field public static sub_operation_course_item_percent_text:I = 0x7f0a0881

.field public static sub_operation_course_item_percent_view:I = 0x7f0a0882

.field public static sub_operation_course_item_title_text:I = 0x7f0a0883

.field public static sub_operation_course_learn:I = 0x7f0a0884

.field public static sub_operation_course_start_liner:I = 0x7f0a0885

.field public static sub_operation_course_title:I = 0x7f0a0886

.field public static sub_operation_filter_add_icon:I = 0x7f0a0887

.field public static sub_operation_filter_icon:I = 0x7f0a0888

.field public static sub_operation_filter_recycler:I = 0x7f0a0889

.field public static sub_operation_filter_title:I = 0x7f0a088a

.field public static sub_operation_header_bg:I = 0x7f0a088b

.field public static sub_operation_horizontal_view_pager:I = 0x7f0a088c

.field public static sub_operation_loading:I = 0x7f0a088d

.field public static sub_operation_main_guide:I = 0x7f0a088e

.field public static sub_operation_main_recycler:I = 0x7f0a088f

.field public static sub_operation_main_refresh:I = 0x7f0a0890

.field public static sub_operation_ranking_education_add_icon:I = 0x7f0a0891

.field public static sub_operation_ranking_education_cover:I = 0x7f0a0892

.field public static sub_operation_ranking_education_tag:I = 0x7f0a0893

.field public static sub_operation_ranking_education_title:I = 0x7f0a0894

.field public static sub_operation_ranking_recycler:I = 0x7f0a0895

.field public static sub_operation_ranking_title:I = 0x7f0a0896

.field public static sub_operation_rankinglist_add_icon:I = 0x7f0a0897

.field public static sub_operation_rankinglist_cover:I = 0x7f0a0898

.field public static sub_operation_rankinglist_rank:I = 0x7f0a0899

.field public static sub_operation_rankinglist_root:I = 0x7f0a089a

.field public static sub_operation_rankinglist_tab_text:I = 0x7f0a089b

.field public static sub_operation_rankinglist_tag:I = 0x7f0a089c

.field public static sub_operation_rankinglist_text:I = 0x7f0a089d

.field public static sub_operation_rankinglist_title:I = 0x7f0a089e

.field public static sub_operation_ranklist_recycler:I = 0x7f0a089f

.field public static sub_operation_ranklist_title_linear:I = 0x7f0a08a0

.field public static sub_operation_ranklist_title_tab_layout:I = 0x7f0a08a1

.field public static sub_operation_ranklist_title_tab_recycler:I = 0x7f0a08a2

.field public static sub_operation_ranklist_title_text:I = 0x7f0a08a3

.field public static sub_operation_title_text:I = 0x7f0a08a4

.field public static sub_operation_variable_image:I = 0x7f0a08a5

.field public static sub_operation_variable_item_title:I = 0x7f0a08a6

.field public static sub_operation_variable_recycle:I = 0x7f0a08a7

.field public static sub_operation_variable_title:I = 0x7f0a08a8

.field public static sub_operation_view_pager:I = 0x7f0a08a9

.field public static sub_operation_view_scroll_helper:I = 0x7f0a08aa

.field public static sub_shor_tv_container:I = 0x7f0a08ac

.field public static sub_shor_tv_header_bg:I = 0x7f0a08ad

.field public static swipe_refresh:I = 0x7f0a08c1

.field public static tab_movie:I = 0x7f0a08ce

.field public static tag_divider:I = 0x7f0a08d8

.field public static tag_group:I = 0x7f0a08d9

.field public static tag_icon:I = 0x7f0a08da

.field public static tag_list:I = 0x7f0a08dc

.field public static tag_name:I = 0x7f0a08dd

.field public static text_switcher:I = 0x7f0a08f7

.field public static title:I = 0x7f0a0908

.field public static titleTV:I = 0x7f0a090e

.field public static tool_bar:I = 0x7f0a0916

.field public static toolbar:I = 0x7f0a0917

.field public static topView:I = 0x7f0a0922

.field public static trending_bottom_bg:I = 0x7f0a0944

.field public static trending_header_bg:I = 0x7f0a0945

.field public static tv:I = 0x7f0a0949

.field public static tvExpand:I = 0x7f0a0985

.field public static tvRankNum:I = 0x7f0a09be

.field public static tvSubTitle:I = 0x7f0a09d9

.field public static tvTitle:I = 0x7f0a09ea

.field public static tvTitle_expand:I = 0x7f0a09ec

.field public static tv_desc:I = 0x7f0a0a25

.field public static tv_discover:I = 0x7f0a0a27

.field public static tv_download_status:I = 0x7f0a0a32

.field public static tv_duration:I = 0x7f0a0a36

.field public static tv_game_center:I = 0x7f0a0a4f

.field public static tv_game_title:I = 0x7f0a0a50

.field public static tv_limit_tips:I = 0x7f0a0a7f

.field public static tv_name:I = 0x7f0a0a91

.field public static tv_no_result:I = 0x7f0a0a98

.field public static tv_rank:I = 0x7f0a0ac1

.field public static tv_red_tips:I = 0x7f0a0ac5

.field public static tv_reset:I = 0x7f0a0acb

.field public static tv_score:I = 0x7f0a0ada

.field public static tv_search:I = 0x7f0a0adf

.field public static tv_search_text:I = 0x7f0a0ae0

.field public static tv_subject:I = 0x7f0a0b02

.field public static tv_subject_num:I = 0x7f0a0b06

.field public static tv_subject_year:I = 0x7f0a0b07

.field public static tv_tab:I = 0x7f0a0b0c

.field public static tv_tabs:I = 0x7f0a0b0d

.field public static tv_tag:I = 0x7f0a0b0e

.field public static tv_tips:I = 0x7f0a0b15

.field public static tv_title:I = 0x7f0a0b1d

.field public static v_download_tips:I = 0x7f0a0b71

.field public static view:I = 0x7f0a0bd8

.field public static viewLine:I = 0x7f0a0bed

.field public static viewPager:I = 0x7f0a0bf7

.field public static view_pager:I = 0x7f0a0c0b


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
