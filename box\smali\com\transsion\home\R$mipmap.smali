.class public final Lcom/transsion/home/<USER>
.super Ljava/lang/Object;


# static fields
.field public static bg_appointment_corner:I = 0x7f0f0017

.field public static bg_one_click:I = 0x7f0f0020

.field public static bg_op_filter_all:I = 0x7f0f0021

.field public static ic_cast:I = 0x7f0f0064

.field public static ic_circle_close:I = 0x7f0f0065

.field public static ic_game_center:I = 0x7f0f008b

.field public static ic_gradient_common_bg:I = 0x7f0f008d

.field public static ic_guide_tip:I = 0x7f0f0094

.field public static ic_home_mb_logo:I = 0x7f0f0097

.field public static ic_honor_default:I = 0x7f0f0098

.field public static ic_my_course_add:I = 0x7f0f00b2

.field public static ic_op_live_default:I = 0x7f0f00c2

.field public static ic_op_rank_number_1:I = 0x7f0f00c3

.field public static ic_op_rank_number_10:I = 0x7f0f00c4

.field public static ic_op_rank_number_2:I = 0x7f0f00c5

.field public static ic_op_rank_number_3:I = 0x7f0f00c6

.field public static ic_op_rank_number_4:I = 0x7f0f00c7

.field public static ic_op_rank_number_5:I = 0x7f0f00c8

.field public static ic_op_rank_number_6:I = 0x7f0f00c9

.field public static ic_op_rank_number_7:I = 0x7f0f00ca

.field public static ic_op_rank_number_8:I = 0x7f0f00cb

.field public static ic_op_rank_number_9:I = 0x7f0f00cc

.field public static ic_op_sport_live:I = 0x7f0f00cd

.field public static ic_op_sport_live_status:I = 0x7f0f00ce

.field public static ic_op_sport_vs:I = 0x7f0f00cf

.field public static ic_open_network_warning:I = 0x7f0f00d0

.field public static ic_play_store:I = 0x7f0f00d4

.field public static ic_search_movie:I = 0x7f0f0100

.field public static ic_sub_operation_appointment:I = 0x7f0f0109

.field public static ic_sub_operation_appointment_checked:I = 0x7f0f010a

.field public static ic_sub_operation_filter:I = 0x7f0f010b

.field public static ic_tab_downloads_active:I = 0x7f0f0121

.field public static ic_tab_downloads_unactive_dark:I = 0x7f0f0122

.field public static ic_tab_home_active:I = 0x7f0f0124

.field public static ic_tab_home_unactive_dark:I = 0x7f0f0125

.field public static ic_tab_me_active:I = 0x7f0f0126

.field public static ic_tab_me_unactive_dark:I = 0x7f0f0127

.field public static ic_tab_music_active:I = 0x7f0f0128

.field public static ic_tab_music_unactive_dark:I = 0x7f0f0129

.field public static ic_tab_premium_activit:I = 0x7f0f012a

.field public static ic_tab_premium_unactive:I = 0x7f0f012b

.field public static ic_tab_short_tv_active:I = 0x7f0f012c

.field public static ic_tab_short_tv_unactive:I = 0x7f0f012d


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
