.class public final Lcom/transsion/home/<USER>
.super Ljava/lang/Object;


# static fields
.field public static appointment_add_success:I = 0x7f12004b

.field public static appointment_delete_success:I = 0x7f12004c

.field public static appointment_episode_alert_title:I = 0x7f12004d

.field public static appointment_movie_alert_description:I = 0x7f12004e

.field public static appointment_movie_alert_title:I = 0x7f12004f

.field public static appointment_series_alert_description:I = 0x7f120050

.field public static apps_navigation:I = 0x7f120051

.field public static course_explore:I = 0x7f1200e9

.field public static course_learn:I = 0x7f1200ee

.field public static course_start_add:I = 0x7f1200f3

.field public static discover_more_movies:I = 0x7f12012a

.field public static double_quotes:I = 0x7f12012b

.field public static filter:I = 0x7f120255

.field public static filter_confirm:I = 0x7f120256

.field public static filter_more_title:I = 0x7f120257

.field public static filter_reset:I = 0x7f120258

.field public static home_button_fail:I = 0x7f120295

.field public static home_claim_now:I = 0x7f120296

.field public static home_fail_try_again:I = 0x7f120297

.field public static home_no_network_content:I = 0x7f120298

.field public static home_reset_text:I = 0x7f120299

.field public static home_retry_text:I = 0x7f12029a

.field public static home_tab_education_title:I = 0x7f12029c

.field public static home_tab_movie_title:I = 0x7f12029d

.field public static home_tab_music_title:I = 0x7f12029e

.field public static home_tab_name_animation:I = 0x7f12029f

.field public static home_tab_name_education:I = 0x7f1202a0

.field public static home_tab_name_movie:I = 0x7f1202a1

.field public static home_tab_name_music:I = 0x7f1202a2

.field public static home_tab_name_shorttv:I = 0x7f1202a3

.field public static home_tab_name_trending:I = 0x7f1202a4

.field public static home_tab_name_tv_series:I = 0x7f1202a5

.field public static home_tab_trend_title:I = 0x7f1202a6

.field public static movie_info_desc:I = 0x7f1203f7

.field public static music_tab_discover:I = 0x7f120445

.field public static music_tab_liked_music:I = 0x7f120446

.field public static must_play_games:I = 0x7f120448

.field public static newcomer_guide_tips_step_1:I = 0x7f12045e

.field public static newcomer_guide_tips_step_2:I = 0x7f12045f

.field public static newcomer_guide_tips_step_3:I = 0x7f120460

.field public static no_calender_permission:I = 0x7f120462

.field public static no_filter_result:I = 0x7f12046b

.field public static one_click_download_title:I = 0x7f120494

.field public static open_network_tip:I = 0x7f120499

.field public static remind_me:I = 0x7f120563

.field public static reminder_set:I = 0x7f120564

.field public static room_title:I = 0x7f12057e

.field public static score:I = 0x7f120589

.field public static search:I = 0x7f12058b

.field public static search_all:I = 0x7f12058c

.field public static search_guide_tip:I = 0x7f120593

.field public static search_guide_title:I = 0x7f120594

.field public static single_quotes:I = 0x7f1205e8

.field public static sport_live_living:I = 0x7f1205f0

.field public static sport_live_upcoming:I = 0x7f1205f1

.field public static str_checkin:I = 0x7f12060e

.field public static str_ranking:I = 0x7f12062f

.field public static subject_num:I = 0x7f12063b

.field public static video_built_in_tips:I = 0x7f1207b9

.field public static want_to_see:I = 0x7f1207d6


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
