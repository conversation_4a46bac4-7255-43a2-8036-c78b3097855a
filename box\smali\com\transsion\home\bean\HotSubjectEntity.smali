.class public final Lcom/transsion/home/<USER>/HotSubjectEntity;
.super Ljava/lang/Object;

# interfaces
.implements Landroid/os/Parcelable;


# annotations
.annotation build Landroidx/annotation/Keep;
.end annotation

.annotation runtime L<PERSON>lin/Metadata;
.end annotation


# static fields
.field public static final $stable:I = 0x8

.field public static final CREATOR:Landroid/os/Parcelable$Creator;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Landroid/os/Parcelable$Creator<",
            "Lcom/transsion/home/<USER>/HotSubjectEntity;",
            ">;"
        }
    .end annotation
.end field


# instance fields
.field private everyoneSearch:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lcom/transsion/home/<USER>/HotSearchKeyWord;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 1

    new-instance v0, Lcom/transsion/home/<USER>/HotSubjectEntity$a;

    invoke-direct {v0}, Lcom/transsion/home/<USER>/HotSubjectEntity$a;-><init>()V

    sput-object v0, Lcom/transsion/home/<USER>/HotSubjectEntity;->CREATOR:Landroid/os/Parcelable$Creator;

    return-void
.end method

.method public constructor <init>(Ljava/util/List;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lcom/transsion/home/<USER>/HotSearchKeyWord;",
            ">;)V"
        }
    .end annotation

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/transsion/home/<USER>/HotSubjectEntity;->everyoneSearch:Ljava/util/List;

    return-void
.end method

.method public static synthetic copy$default(Lcom/transsion/home/<USER>/HotSubjectEntity;Ljava/util/List;ILjava/lang/Object;)Lcom/transsion/home/<USER>/HotSubjectEntity;
    .locals 0

    and-int/lit8 p2, p2, 0x1

    if-eqz p2, :cond_0

    iget-object p1, p0, Lcom/transsion/home/<USER>/HotSubjectEntity;->everyoneSearch:Ljava/util/List;

    :cond_0
    invoke-virtual {p0, p1}, Lcom/transsion/home/<USER>/HotSubjectEntity;->copy(Ljava/util/List;)Lcom/transsion/home/<USER>/HotSubjectEntity;

    move-result-object p0

    return-object p0
.end method


# virtual methods
.method public final component1()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lcom/transsion/home/<USER>/HotSearchKeyWord;",
            ">;"
        }
    .end annotation

    iget-object v0, p0, Lcom/transsion/home/<USER>/HotSubjectEntity;->everyoneSearch:Ljava/util/List;

    return-object v0
.end method

.method public final copy(Ljava/util/List;)Lcom/transsion/home/<USER>/HotSubjectEntity;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lcom/transsion/home/<USER>/HotSearchKeyWord;",
            ">;)",
            "Lcom/transsion/home/<USER>/HotSubjectEntity;"
        }
    .end annotation

    new-instance v0, Lcom/transsion/home/<USER>/HotSubjectEntity;

    invoke-direct {v0, p1}, Lcom/transsion/home/<USER>/HotSubjectEntity;-><init>(Ljava/util/List;)V

    return-object v0
.end method

.method public describeContents()I
    .locals 1

    const/4 v0, 0x0

    return v0
.end method

.method public equals(Ljava/lang/Object;)Z
    .locals 3

    const/4 v0, 0x1

    if-ne p0, p1, :cond_0

    return v0

    :cond_0
    instance-of v1, p1, Lcom/transsion/home/<USER>/HotSubjectEntity;

    const/4 v2, 0x0

    if-nez v1, :cond_1

    return v2

    :cond_1
    check-cast p1, Lcom/transsion/home/<USER>/HotSubjectEntity;

    iget-object v1, p0, Lcom/transsion/home/<USER>/HotSubjectEntity;->everyoneSearch:Ljava/util/List;

    iget-object p1, p1, Lcom/transsion/home/<USER>/HotSubjectEntity;->everyoneSearch:Ljava/util/List;

    invoke-static {v1, p1}, Lkotlin/jvm/internal/Intrinsics;->b(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result p1

    if-nez p1, :cond_2

    return v2

    :cond_2
    return v0
.end method

.method public final getEveryoneSearch()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lcom/transsion/home/<USER>/HotSearchKeyWord;",
            ">;"
        }
    .end annotation

    iget-object v0, p0, Lcom/transsion/home/<USER>/HotSubjectEntity;->everyoneSearch:Ljava/util/List;

    return-object v0
.end method

.method public hashCode()I
    .locals 1

    iget-object v0, p0, Lcom/transsion/home/<USER>/HotSubjectEntity;->everyoneSearch:Ljava/util/List;

    if-nez v0, :cond_0

    const/4 v0, 0x0

    goto :goto_0

    :cond_0
    invoke-virtual {v0}, Ljava/lang/Object;->hashCode()I

    move-result v0

    :goto_0
    return v0
.end method

.method public final setEveryoneSearch(Ljava/util/List;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lcom/transsion/home/<USER>/HotSearchKeyWord;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lcom/transsion/home/<USER>/HotSubjectEntity;->everyoneSearch:Ljava/util/List;

    return-void
.end method

.method public toString()Ljava/lang/String;
    .locals 3

    iget-object v0, p0, Lcom/transsion/home/<USER>/HotSubjectEntity;->everyoneSearch:Ljava/util/List;

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "HotSubjectEntity(everyoneSearch="

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v0, ")"

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public writeToParcel(Landroid/os/Parcel;I)V
    .locals 2

    const-string v0, "out"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    iget-object v0, p0, Lcom/transsion/home/<USER>/HotSubjectEntity;->everyoneSearch:Ljava/util/List;

    if-nez v0, :cond_0

    const/4 p2, 0x0

    invoke-virtual {p1, p2}, Landroid/os/Parcel;->writeInt(I)V

    goto :goto_1

    :cond_0
    const/4 v1, 0x1

    invoke-virtual {p1, v1}, Landroid/os/Parcel;->writeInt(I)V

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v1

    invoke-virtual {p1, v1}, Landroid/os/Parcel;->writeInt(I)V

    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_1

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/transsion/home/<USER>/HotSearchKeyWord;

    invoke-virtual {v1, p1, p2}, Lcom/transsion/home/<USER>/HotSearchKeyWord;->writeToParcel(Landroid/os/Parcel;I)V

    goto :goto_0

    :cond_1
    :goto_1
    return-void
.end method
