.class public final Lcom/transsion/home/<USER>/HomeFragment;
.super Lcom/transsion/baseui/fragment/BaseFragment;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/transsion/home/<USER>/HomeFragment$a;,
        Lcom/transsion/home/<USER>/HomeFragment$b;
    }
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lcom/transsion/baseui/fragment/BaseFragment<",
        "Lum/j;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation

.annotation build Lkotlin/jvm/internal/SourceDebugExtension;
.end annotation


# static fields
.field public static final i:Lcom/transsion/home/<USER>/HomeFragment$a;

.field public static final j:I

.field public static k:I


# instance fields
.field public a:Lcom/transsion/home/<USER>/AppTab;

.field public b:I

.field public c:Lcom/transsion/home/<USER>/tab/TrendingFragment;

.field public d:Ljava/lang/String;

.field public e:Ljava/lang/String;

.field public f:Lcom/transsion/home/<USER>/HomeSearchViewManager;

.field public g:Landroid/os/Bundle;

.field public h:J


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lcom/transsion/home/<USER>/HomeFragment$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lcom/transsion/home/<USER>/HomeFragment$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lcom/transsion/home/<USER>/HomeFragment;->i:Lcom/transsion/home/<USER>/HomeFragment$a;

    const/16 v0, 0x8

    sput v0, Lcom/transsion/home/<USER>/HomeFragment;->j:I

    return-void
.end method

.method public constructor <init>()V
    .locals 1

    invoke-direct {p0}, Lcom/transsion/baseui/fragment/BaseFragment;-><init>()V

    sget v0, Lcom/transsion/home/<USER>/HomeFragment;->k:I

    iput v0, p0, Lcom/transsion/home/<USER>/HomeFragment;->b:I

    const-string v0, "{\"country\":\"All\",\"year\":\"All\",\"genre\":\"All\",\"sort\":\"ForYou\"}"

    iput-object v0, p0, Lcom/transsion/home/<USER>/HomeFragment;->e:Ljava/lang/String;

    return-void
.end method

.method public static final synthetic Y(Lcom/transsion/home/<USER>/HomeFragment;)Lcom/transsion/home/<USER>/AppTab;
    .locals 0

    iget-object p0, p0, Lcom/transsion/home/<USER>/HomeFragment;->a:Lcom/transsion/home/<USER>/AppTab;

    return-object p0
.end method

.method public static final synthetic Z(Lcom/transsion/home/<USER>/HomeFragment;I)Landroidx/fragment/app/Fragment;
    .locals 0

    invoke-virtual {p0, p1}, Lcom/transsion/home/<USER>/HomeFragment;->h0(I)Landroidx/fragment/app/Fragment;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic a0(Lcom/transsion/home/<USER>/HomeFragment;)Lcom/transsion/home/<USER>/HomeSearchViewManager;
    .locals 0

    iget-object p0, p0, Lcom/transsion/home/<USER>/HomeFragment;->f:Lcom/transsion/home/<USER>/HomeSearchViewManager;

    return-object p0
.end method

.method public static final synthetic b0(Lcom/transsion/home/<USER>/HomeFragment;)I
    .locals 0

    iget p0, p0, Lcom/transsion/home/<USER>/HomeFragment;->b:I

    return p0
.end method

.method public static final synthetic c0(Lcom/transsion/home/<USER>/HomeFragment;I)V
    .locals 0

    iput p1, p0, Lcom/transsion/home/<USER>/HomeFragment;->b:I

    return-void
.end method

.method public static final synthetic d0(Lcom/transsion/home/<USER>/HomeFragment;)V
    .locals 0

    invoke-virtual {p0}, Lcom/transsion/home/<USER>/HomeFragment;->w0()V

    return-void
.end method

.method public static final synthetic e0(Lcom/transsion/home/<USER>/HomeFragment;Lcom/transsion/home/<USER>/AppTab;)V
    .locals 0

    invoke-virtual {p0, p1}, Lcom/transsion/home/<USER>/HomeFragment;->z0(Lcom/transsion/home/<USER>/AppTab;)V

    return-void
.end method

.method private final f0()V
    .locals 7

    invoke-virtual {p0}, Lcom/transsion/baseui/fragment/BaseFragment;->getMViewBinding()Ls4/a;

    move-result-object v0

    check-cast v0, Lum/j;

    if-eqz v0, :cond_0

    iget-object v0, v0, Lum/j;->l:Landroidx/viewpager2/widget/ViewPager2;

    if-eqz v0, :cond_0

    new-instance v1, Lcom/transsion/home/<USER>/HomeFragment$c;

    invoke-direct {v1, p0}, Lcom/transsion/home/<USER>/HomeFragment$c;-><init>(Lcom/transsion/home/<USER>/HomeFragment;)V

    invoke-virtual {v0, v1}, Landroidx/viewpager2/widget/ViewPager2;->registerOnPageChangeCallback(Landroidx/viewpager2/widget/ViewPager2$OnPageChangeCallback;)V

    :cond_0
    iget-object v0, p0, Lcom/transsion/home/<USER>/HomeFragment;->a:Lcom/transsion/home/<USER>/AppTab;

    const/4 v1, 0x0

    const/4 v2, 0x0

    if-eqz v0, :cond_4

    invoke-virtual {v0}, Lcom/transsion/home/<USER>/AppTab;->getHomeTabs()Ljava/util/List;

    move-result-object v0

    if-eqz v0, :cond_4

    check-cast v0, Ljava/lang/Iterable;

    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    move-result-object v0

    const/4 v3, 0x0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v4

    if-eqz v4, :cond_4

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v4

    add-int/lit8 v5, v3, 0x1

    if-gez v3, :cond_1

    invoke-static {}, Lkotlin/collections/CollectionsKt;->u()V

    :cond_1
    check-cast v4, Lcom/transsion/home/<USER>/HomeTabItem;

    if-eqz v4, :cond_2

    invoke-virtual {v4}, Lcom/transsion/home/<USER>/HomeTabItem;->getTabCode()Ljava/lang/String;

    move-result-object v4

    goto :goto_1

    :cond_2
    move-object v4, v1

    :goto_1
    const-string v6, "Trending"

    invoke-static {v4, v6}, Lkotlin/jvm/internal/Intrinsics;->b(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v4

    if-eqz v4, :cond_3

    sput v3, Lcom/transsion/home/<USER>/HomeFragment;->k:I

    iput v3, p0, Lcom/transsion/home/<USER>/HomeFragment;->b:I

    :cond_3
    move v3, v5

    goto :goto_0

    :cond_4
    invoke-virtual {p0}, Lcom/transsion/baseui/fragment/BaseFragment;->getMViewBinding()Ls4/a;

    move-result-object v0

    check-cast v0, Lum/j;

    if-eqz v0, :cond_5

    iget-object v0, v0, Lum/j;->l:Landroidx/viewpager2/widget/ViewPager2;

    if-eqz v0, :cond_5

    iget v3, p0, Lcom/transsion/home/<USER>/HomeFragment;->b:I

    invoke-virtual {v0, v3, v2}, Landroidx/viewpager2/widget/ViewPager2;->setCurrentItem(IZ)V

    :cond_5
    invoke-virtual {p0}, Lcom/transsion/baseui/fragment/BaseFragment;->getMViewBinding()Ls4/a;

    move-result-object v0

    check-cast v0, Lum/j;

    if-eqz v0, :cond_6

    iget-object v1, v0, Lum/j;->l:Landroidx/viewpager2/widget/ViewPager2;

    :cond_6
    if-nez v1, :cond_7

    goto :goto_2

    :cond_7
    const/4 v0, 0x1

    invoke-virtual {v1, v0}, Landroidx/viewpager2/widget/ViewPager2;->setOffscreenPageLimit(I)V

    :goto_2
    return-void
.end method

.method private final p0()V
    .locals 9

    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->isAdded()Z

    move-result v0

    if-nez v0, :cond_0

    return-void

    :cond_0
    sget-object v0, Lxi/b;->a:Lxi/b$a;

    const-string v2, "HomeFragment"

    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getChildFragmentManager()Landroidx/fragment/app/FragmentManager;

    move-result-object v1

    invoke-virtual {v1}, Landroidx/fragment/app/FragmentManager;->getFragments()Ljava/util/List;

    move-result-object v1

    invoke-interface {v1}, Ljava/util/List;->size()I

    move-result v1

    invoke-virtual {p0}, Lcom/transsion/home/<USER>/HomeFragment;->m0()I

    move-result v3

    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    const-string v7, "fragment count "

    invoke-virtual {v4, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v1, " tabs size "

    invoke-virtual {v4, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4, v3}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    const/4 v4, 0x0

    const/4 v5, 0x4

    const/4 v6, 0x0

    move-object v1, v0

    invoke-static/range {v1 .. v6}, Lxi/b$a;->f(Lxi/b$a;Ljava/lang/String;Ljava/lang/String;ZILjava/lang/Object;)V

    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getChildFragmentManager()Landroidx/fragment/app/FragmentManager;

    move-result-object v1

    invoke-virtual {v1}, Landroidx/fragment/app/FragmentManager;->getFragments()Ljava/util/List;

    move-result-object v1

    invoke-interface {v1}, Ljava/util/List;->size()I

    move-result v1

    const/4 v8, 0x0

    if-lez v1, :cond_3

    invoke-virtual {p0}, Lcom/transsion/home/<USER>/HomeFragment;->m0()I

    move-result v2

    if-eq v1, v2, :cond_3

    invoke-virtual {p0}, Lcom/transsion/baseui/fragment/BaseFragment;->getMViewBinding()Ls4/a;

    move-result-object v1

    check-cast v1, Lum/j;

    if-eqz v1, :cond_1

    iget-object v1, v1, Lum/j;->l:Landroidx/viewpager2/widget/ViewPager2;

    goto :goto_0

    :cond_1
    move-object v1, v8

    :goto_0
    if-nez v1, :cond_2

    goto :goto_1

    :cond_2
    new-instance v2, Lcom/transsion/home/<USER>/HomeFragment$d;

    invoke-direct {v2, p0}, Lcom/transsion/home/<USER>/HomeFragment$d;-><init>(Lcom/transsion/home/<USER>/HomeFragment;)V

    invoke-virtual {v1, v2}, Landroidx/viewpager2/widget/ViewPager2;->setAdapter(Landroidx/recyclerview/widget/RecyclerView$Adapter;)V

    :cond_3
    :goto_1
    const-string v2, "HomeFragment"

    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getChildFragmentManager()Landroidx/fragment/app/FragmentManager;

    move-result-object v1

    invoke-virtual {v1}, Landroidx/fragment/app/FragmentManager;->getFragments()Ljava/util/List;

    move-result-object v1

    invoke-interface {v1}, Ljava/util/List;->size()I

    move-result v1

    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v3, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    const/4 v4, 0x0

    const/4 v5, 0x4

    const/4 v6, 0x0

    move-object v1, v0

    invoke-static/range {v1 .. v6}, Lxi/b$a;->f(Lxi/b$a;Ljava/lang/String;Ljava/lang/String;ZILjava/lang/Object;)V

    invoke-virtual {p0}, Lcom/transsion/baseui/fragment/BaseFragment;->getMViewBinding()Ls4/a;

    move-result-object v0

    check-cast v0, Lum/j;

    if-eqz v0, :cond_4

    iget-object v8, v0, Lum/j;->l:Landroidx/viewpager2/widget/ViewPager2;

    :cond_4
    if-nez v8, :cond_5

    goto :goto_2

    :cond_5
    new-instance v0, Lcom/transsion/home/<USER>/HomeFragment$e;

    invoke-direct {v0, p0}, Lcom/transsion/home/<USER>/HomeFragment$e;-><init>(Lcom/transsion/home/<USER>/HomeFragment;)V

    invoke-virtual {v8, v0}, Landroidx/viewpager2/widget/ViewPager2;->setAdapter(Landroidx/recyclerview/widget/RecyclerView$Adapter;)V

    :goto_2
    invoke-direct {p0}, Lcom/transsion/home/<USER>/HomeFragment;->f0()V

    return-void
.end method

.method private final t0()V
    .locals 0

    invoke-virtual {p0}, Lcom/transsion/home/<USER>/HomeFragment;->q0()V

    invoke-virtual {p0}, Lcom/transsion/home/<USER>/HomeFragment;->u0()V

    return-void
.end method


# virtual methods
.method public final g0(Z)V
    .locals 2

    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getActivity()Landroidx/fragment/app/FragmentActivity;

    move-result-object v0

    instance-of v1, v0, Lcom/transsion/baseui/activity/BaseActivity;

    if-eqz v1, :cond_0

    check-cast v0, Lcom/transsion/baseui/activity/BaseActivity;

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    if-eqz v0, :cond_1

    const/4 v1, 0x1

    invoke-virtual {v0, p1, v1}, Lcom/transsion/baseui/activity/BaseActivity;->changeStatusFontColor(ZZ)V

    :cond_1
    iget-object v0, p0, Lcom/transsion/home/<USER>/HomeFragment;->f:Lcom/transsion/home/<USER>/HomeSearchViewManager;

    if-eqz v0, :cond_2

    invoke-virtual {v0, p1}, Lcom/transsion/home/<USER>/HomeSearchViewManager;->l(Z)V

    :cond_2
    return-void
.end method

.method public bridge synthetic getViewBinding(Landroid/view/LayoutInflater;)Ls4/a;
    .locals 0

    invoke-virtual {p0, p1}, Lcom/transsion/home/<USER>/HomeFragment;->n0(Landroid/view/LayoutInflater;)Lum/j;

    move-result-object p1

    return-object p1
.end method

.method public final h0(I)Landroidx/fragment/app/Fragment;
    .locals 9

    iget-object v0, p0, Lcom/transsion/home/<USER>/HomeFragment;->a:Lcom/transsion/home/<USER>/AppTab;

    const/4 v1, 0x0

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Lcom/transsion/home/<USER>/AppTab;->getHomeTabs()Ljava/util/List;

    move-result-object v0

    if-eqz v0, :cond_0

    invoke-interface {v0, p1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/transsion/home/<USER>/HomeTabItem;

    goto :goto_0

    :cond_0
    move-object v0, v1

    :goto_0
    sget-object v2, Lxi/b;->a:Lxi/b$a;

    if-eqz v0, :cond_1

    invoke-virtual {v0}, Lcom/transsion/home/<USER>/HomeTabItem;->getType()Ljava/lang/String;

    move-result-object v3

    goto :goto_1

    :cond_1
    move-object v3, v1

    :goto_1
    if-eqz v0, :cond_2

    invoke-virtual {v0}, Lcom/transsion/home/<USER>/HomeTabItem;->getTabCode()Ljava/lang/String;

    move-result-object v4

    goto :goto_2

    :cond_2
    move-object v4, v1

    :goto_2
    new-instance v5, Ljava/lang/StringBuilder;

    invoke-direct {v5}, Ljava/lang/StringBuilder;-><init>()V

    const-string v6, "getFragment:"

    invoke-virtual {v5, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v5, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v3, "&position:"

    invoke-virtual {v5, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v5, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v3, "&tabCode:"

    invoke-virtual {v5, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v5, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v5}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    const-string v4, "HomeFragment"

    const/4 v5, 0x1

    invoke-virtual {v2, v4, v3, v5}, Lxi/b$a;->c(Ljava/lang/String;Ljava/lang/String;Z)V

    sget-object v2, Lcom/transsion/home/<USER>/HomeTabType;->Companion:Lcom/transsion/home/<USER>/HomeTabType$a;

    if-eqz v0, :cond_3

    invoke-virtual {v0}, Lcom/transsion/home/<USER>/HomeTabItem;->getType()Ljava/lang/String;

    move-result-object v3

    goto :goto_3

    :cond_3
    move-object v3, v1

    :goto_3
    invoke-virtual {v2, v3}, Lcom/transsion/home/<USER>/HomeTabType$a;->a(Ljava/lang/String;)Lcom/transsion/home/<USER>/HomeTabType;

    move-result-object v2

    if-nez v2, :cond_4

    const/4 v2, -0x1

    goto :goto_4

    :cond_4
    sget-object v3, Lcom/transsion/home/<USER>/HomeFragment$b;->a:[I

    invoke-virtual {v2}, Ljava/lang/Enum;->ordinal()I

    move-result v2

    aget v2, v3, v2

    :goto_4
    if-eq v2, v5, :cond_11

    const/4 v3, 0x2

    if-eq v2, v3, :cond_e

    const/4 v4, 0x5

    const/4 v6, 0x4

    const/4 v7, 0x3

    if-eq v2, v7, :cond_b

    if-eq v2, v6, :cond_6

    if-eq v2, v4, :cond_5

    invoke-virtual {p0, p1}, Lcom/transsion/home/<USER>/HomeFragment;->i0(I)Landroidx/fragment/app/Fragment;

    move-result-object p1

    instance-of v0, p1, Lcom/transsion/home/<USER>/tab/MovieFragment;

    if-eqz v0, :cond_14

    iget-object v0, p0, Lcom/transsion/home/<USER>/HomeFragment;->d:Ljava/lang/String;

    if-eqz v0, :cond_14

    invoke-interface {v0}, Ljava/lang/CharSequence;->length()I

    move-result v0

    if-lez v0, :cond_14

    move-object v0, p1

    check-cast v0, Lcom/transsion/home/<USER>/tab/MovieFragment;

    iget-object v1, p0, Lcom/transsion/home/<USER>/HomeFragment;->d:Ljava/lang/String;

    iget-object v2, p0, Lcom/transsion/home/<USER>/HomeFragment;->e:Ljava/lang/String;

    invoke-virtual {v0, v1, v2}, Lcom/transsion/home/<USER>/tab/MovieFragment;->G1(Ljava/lang/String;Ljava/lang/String;)V

    goto/16 :goto_7

    :cond_5
    new-instance p1, Lcom/transsion/home/<USER>/tab/SubShortTvFragment;

    invoke-direct {p1}, Lcom/transsion/home/<USER>/tab/SubShortTvFragment;-><init>()V

    goto/16 :goto_7

    :cond_6
    iget-object p1, p0, Lcom/transsion/home/<USER>/HomeFragment;->c:Lcom/transsion/home/<USER>/tab/TrendingFragment;

    if-eqz p1, :cond_7

    invoke-static {p1}, Lkotlin/jvm/internal/Intrinsics;->d(Ljava/lang/Object;)V

    return-object p1

    :cond_7
    sget-object p1, Lcom/transsion/home/<USER>/tab/TrendingFragment;->x:Lcom/transsion/home/<USER>/tab/TrendingFragment$a;

    if-eqz v0, :cond_8

    invoke-virtual {v0}, Lcom/transsion/home/<USER>/HomeTabItem;->getTabId()Ljava/lang/Integer;

    move-result-object v2

    if-eqz v2, :cond_8

    invoke-virtual {v2}, Ljava/lang/Integer;->intValue()I

    move-result v5

    :cond_8
    if-eqz v0, :cond_9

    invoke-virtual {v0}, Lcom/transsion/home/<USER>/HomeTabItem;->getTabCode()Ljava/lang/String;

    move-result-object v1

    :cond_9
    invoke-virtual {p1, v5, v1}, Lcom/transsion/home/<USER>/tab/TrendingFragment$a;->a(ILjava/lang/String;)Lcom/transsion/home/<USER>/tab/TrendingFragment;

    move-result-object p1

    iput-object p1, p0, Lcom/transsion/home/<USER>/HomeFragment;->c:Lcom/transsion/home/<USER>/tab/TrendingFragment;

    if-eqz p1, :cond_a

    new-instance v0, Lcom/transsion/home/<USER>/HomeFragment$getFragmentByType$2;

    invoke-direct {v0, p0}, Lcom/transsion/home/<USER>/HomeFragment$getFragmentByType$2;-><init>(Lcom/transsion/home/<USER>/HomeFragment;)V

    invoke-virtual {p1, v0}, Lcom/transsion/home/<USER>/tab/TrendingFragment;->K1(Lkotlin/jvm/functions/Function0;)V

    :cond_a
    iget-object p1, p0, Lcom/transsion/home/<USER>/HomeFragment;->c:Lcom/transsion/home/<USER>/tab/TrendingFragment;

    invoke-static {p1}, Lkotlin/jvm/internal/Intrinsics;->d(Ljava/lang/Object;)V

    goto/16 :goto_7

    :cond_b
    sget-object p1, Lcom/transsion/web/fragment/WebFragment;->t:Lcom/transsion/web/fragment/WebFragment$a;

    invoke-virtual {p1}, Lcom/transsion/web/fragment/WebFragment$a;->b()Lcom/transsion/web/fragment/WebFragment;

    move-result-object p1

    new-array v2, v4, [Lkotlin/Pair;

    if-eqz v0, :cond_c

    invoke-virtual {v0}, Lcom/transsion/home/<USER>/HomeTabItem;->getUrl()Ljava/lang/String;

    move-result-object v4

    goto :goto_5

    :cond_c
    move-object v4, v1

    :goto_5
    const-string v8, "url"

    invoke-static {v8, v4}, Lkotlin/TuplesKt;->a(Ljava/lang/Object;Ljava/lang/Object;)Lkotlin/Pair;

    move-result-object v4

    const/4 v8, 0x0

    aput-object v4, v2, v8

    sget-object v4, Ljava/lang/Boolean;->TRUE:Ljava/lang/Boolean;

    const-string v8, "tool_bar_hidden"

    invoke-static {v8, v4}, Lkotlin/TuplesKt;->a(Ljava/lang/Object;Ljava/lang/Object;)Lkotlin/Pair;

    move-result-object v8

    aput-object v8, v2, v5

    if-eqz v0, :cond_d

    invoke-virtual {v0}, Lcom/transsion/home/<USER>/HomeTabItem;->getTabCode()Ljava/lang/String;

    move-result-object v1

    :cond_d
    const-string v0, "tab_code"

    invoke-static {v0, v1}, Lkotlin/TuplesKt;->a(Ljava/lang/Object;Ljava/lang/Object;)Lkotlin/Pair;

    move-result-object v0

    aput-object v0, v2, v3

    const-string v0, "need_header"

    invoke-static {v0, v4}, Lkotlin/TuplesKt;->a(Ljava/lang/Object;Ljava/lang/Object;)Lkotlin/Pair;

    move-result-object v0

    aput-object v0, v2, v7

    sget v0, Lcom/transsion/home/<USER>

    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v0

    const-string v1, "header_GB"

    invoke-static {v1, v0}, Lkotlin/TuplesKt;->a(Ljava/lang/Object;Ljava/lang/Object;)Lkotlin/Pair;

    move-result-object v0

    aput-object v0, v2, v6

    invoke-static {v2}, Landroidx/core/os/b;->b([Lkotlin/Pair;)Landroid/os/Bundle;

    move-result-object v0

    invoke-virtual {p1, v0}, Landroidx/fragment/app/Fragment;->setArguments(Landroid/os/Bundle;)V

    goto :goto_7

    :cond_e
    sget-object p1, Lcom/transsion/home/<USER>/tab/MovieFragment;->z:Lcom/transsion/home/<USER>/tab/MovieFragment$a;

    if-eqz v0, :cond_f

    invoke-virtual {v0}, Lcom/transsion/home/<USER>/HomeTabItem;->getTabId()Ljava/lang/Integer;

    move-result-object v2

    if-eqz v2, :cond_f

    invoke-virtual {v2}, Ljava/lang/Integer;->intValue()I

    move-result v5

    move v2, v5

    goto :goto_6

    :cond_f
    const/4 v2, 0x1

    :goto_6
    const/4 v3, 0x0

    const/4 v4, 0x0

    if-eqz v0, :cond_10

    invoke-virtual {v0}, Lcom/transsion/home/<USER>/HomeTabItem;->getTabCode()Ljava/lang/String;

    move-result-object v1

    :cond_10
    move-object v5, v1

    const/4 v6, 0x6

    const/4 v7, 0x0

    move-object v1, p1

    invoke-static/range {v1 .. v7}, Lcom/transsion/home/<USER>/tab/MovieFragment$a;->b(Lcom/transsion/home/<USER>/tab/MovieFragment$a;IZLjava/lang/String;Ljava/lang/String;ILjava/lang/Object;)Lcom/transsion/home/<USER>/tab/MovieFragment;

    move-result-object p1

    goto :goto_7

    :cond_11
    sget-object p1, Lcom/transsion/home/<USER>/tab/SubTabFragment;->q:Lcom/transsion/home/<USER>/tab/SubTabFragment$a;

    if-eqz v0, :cond_12

    invoke-virtual {v0}, Lcom/transsion/home/<USER>/HomeTabItem;->getTabId()Ljava/lang/Integer;

    move-result-object v2

    if-eqz v2, :cond_12

    invoke-virtual {v2}, Ljava/lang/Integer;->intValue()I

    move-result v5

    :cond_12
    if-eqz v0, :cond_13

    invoke-virtual {v0}, Lcom/transsion/home/<USER>/HomeTabItem;->getTabCode()Ljava/lang/String;

    move-result-object v1

    :cond_13
    invoke-virtual {p1, v5, v1}, Lcom/transsion/home/<USER>/tab/SubTabFragment$a;->b(ILjava/lang/String;)Lcom/transsion/home/<USER>/tab/SubTabFragment;

    move-result-object p1

    :cond_14
    :goto_7
    return-object p1
.end method

.method public final i0(I)Landroidx/fragment/app/Fragment;
    .locals 9

    iget-object v0, p0, Lcom/transsion/home/<USER>/HomeFragment;->a:Lcom/transsion/home/<USER>/AppTab;

    const/4 v1, 0x0

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Lcom/transsion/home/<USER>/AppTab;->getHomeTabs()Ljava/util/List;

    move-result-object v0

    if-eqz v0, :cond_0

    invoke-interface {v0, p1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/transsion/home/<USER>/HomeTabItem;

    goto :goto_0

    :cond_0
    move-object p1, v1

    :goto_0
    sget-object v2, Lcom/transsion/home/<USER>/tab/MovieFragment;->z:Lcom/transsion/home/<USER>/tab/MovieFragment$a;

    if-eqz p1, :cond_1

    invoke-virtual {p1}, Lcom/transsion/home/<USER>/HomeTabItem;->getTabId()Ljava/lang/Integer;

    move-result-object v0

    if-eqz v0, :cond_1

    invoke-virtual {v0}, Ljava/lang/Integer;->intValue()I

    move-result v0

    move v3, v0

    goto :goto_1

    :cond_1
    const/4 v0, 0x1

    const/4 v3, 0x1

    :goto_1
    const/4 v4, 0x0

    const/4 v5, 0x0

    if-eqz p1, :cond_2

    invoke-virtual {p1}, Lcom/transsion/home/<USER>/HomeTabItem;->getTabCode()Ljava/lang/String;

    move-result-object v1

    :cond_2
    move-object v6, v1

    const/4 v7, 0x6

    const/4 v8, 0x0

    invoke-static/range {v2 .. v8}, Lcom/transsion/home/<USER>/tab/MovieFragment$a;->b(Lcom/transsion/home/<USER>/tab/MovieFragment$a;IZLjava/lang/String;Ljava/lang/String;ILjava/lang/Object;)Lcom/transsion/home/<USER>/tab/MovieFragment;

    move-result-object p1

    return-object p1
.end method

.method public initData(Landroid/view/View;Landroid/os/Bundle;)V
    .locals 0

    const-string p2, "view"

    invoke-static {p1, p2}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    sget-object p1, Lcom/transsnet/downloader/util/DownloadSDCardUtil;->a:Lcom/transsnet/downloader/util/DownloadSDCardUtil;

    invoke-virtual {p1}, Lcom/transsnet/downloader/util/DownloadSDCardUtil;->c()V

    sget-object p1, Lcom/transsion/videofloat/VideoFloatManager;->a:Lcom/transsion/videofloat/VideoFloatManager$Companion;

    invoke-virtual {p1}, Lcom/transsion/videofloat/VideoFloatManager$Companion;->b()Lcom/transsion/videofloat/VideoFloatManager;

    move-result-object p1

    invoke-interface {p1}, Lcom/transsion/videofloat/VideoFloatManager;->d()V

    return-void
.end method

.method public initView(Landroid/view/View;Landroid/os/Bundle;)V
    .locals 1

    const-string v0, "view"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-static {p0}, Landroidx/lifecycle/v;->a(Landroidx/lifecycle/u;)Landroidx/lifecycle/LifecycleCoroutineScope;

    iput-object p2, p0, Lcom/transsion/home/<USER>/HomeFragment;->g:Landroid/os/Bundle;

    if-eqz p2, :cond_0

    const-string p1, "current_tab"

    sget v0, Lcom/transsion/home/<USER>/HomeFragment;->k:I

    invoke-virtual {p2, p1, v0}, Landroid/os/BaseBundle;->getInt(Ljava/lang/String;I)I

    move-result p1

    goto :goto_0

    :cond_0
    iget p1, p0, Lcom/transsion/home/<USER>/HomeFragment;->b:I

    :goto_0
    iput p1, p0, Lcom/transsion/home/<USER>/HomeFragment;->b:I

    invoke-virtual {p0}, Lcom/transsion/home/<USER>/HomeFragment;->o0()V

    invoke-direct {p0}, Lcom/transsion/home/<USER>/HomeFragment;->t0()V

    invoke-virtual {p0}, Lcom/transsion/baseui/fragment/BaseFragment;->getMViewBinding()Ls4/a;

    move-result-object p1

    check-cast p1, Lum/j;

    if-eqz p1, :cond_1

    new-instance p2, Lcom/transsion/home/<USER>/HomeSearchViewManager;

    invoke-direct {p2, p1, p0}, Lcom/transsion/home/<USER>/HomeSearchViewManager;-><init>(Lum/j;Lcom/transsion/home/<USER>/HomeFragment;)V

    iput-object p2, p0, Lcom/transsion/home/<USER>/HomeFragment;->f:Lcom/transsion/home/<USER>/HomeSearchViewManager;

    iget-object p1, p0, Lcom/transsion/home/<USER>/HomeFragment;->a:Lcom/transsion/home/<USER>/AppTab;

    invoke-virtual {p2, p1}, Lcom/transsion/home/<USER>/HomeSearchViewManager;->A(Lcom/transsion/home/<USER>/AppTab;)V

    :cond_1
    sget p1, Landroid/os/Build$VERSION;->SDK_INT:I

    const/16 p2, 0x17

    if-gt p1, p2, :cond_4

    invoke-virtual {p0}, Lcom/transsion/baseui/fragment/BaseFragment;->getMViewBinding()Ls4/a;

    move-result-object p1

    check-cast p1, Lum/j;

    if-eqz p1, :cond_2

    iget-object p1, p1, Lum/j;->j:Lcom/transsion/home/<USER>/BlurredSectorView;

    goto :goto_1

    :cond_2
    const/4 p1, 0x0

    :goto_1
    if-nez p1, :cond_3

    goto :goto_2

    :cond_3
    const/16 p2, 0x8

    invoke-virtual {p1, p2}, Landroid/view/View;->setVisibility(I)V

    :cond_4
    :goto_2
    return-void
.end method

.method public final j0(Ljava/lang/String;I)I
    .locals 5

    const/4 v0, 0x0

    if-ltz p2, :cond_3

    iget-object v1, p0, Lcom/transsion/home/<USER>/HomeFragment;->a:Lcom/transsion/home/<USER>/AppTab;

    if-eqz v1, :cond_3

    invoke-virtual {v1}, Lcom/transsion/home/<USER>/AppTab;->getHomeTabs()Ljava/util/List;

    move-result-object v1

    if-eqz v1, :cond_3

    check-cast v1, Ljava/lang/Iterable;

    invoke-interface {v1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    move-result-object v1

    const/4 v2, 0x0

    :goto_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v3

    if-eqz v3, :cond_3

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v3

    add-int/lit8 v4, v2, 0x1

    if-gez v2, :cond_0

    invoke-static {}, Lkotlin/collections/CollectionsKt;->u()V

    :cond_0
    check-cast v3, Lcom/transsion/home/<USER>/HomeTabItem;

    if-eqz v3, :cond_2

    invoke-virtual {v3}, Lcom/transsion/home/<USER>/HomeTabItem;->getTabId()Ljava/lang/Integer;

    move-result-object v3

    if-nez v3, :cond_1

    goto :goto_1

    :cond_1
    invoke-virtual {v3}, Ljava/lang/Integer;->intValue()I

    move-result v3

    if-ne v3, p2, :cond_2

    return v2

    :cond_2
    :goto_1
    move v2, v4

    goto :goto_0

    :cond_3
    iget-object p2, p0, Lcom/transsion/home/<USER>/HomeFragment;->a:Lcom/transsion/home/<USER>/AppTab;

    if-eqz p2, :cond_7

    invoke-virtual {p2}, Lcom/transsion/home/<USER>/AppTab;->getHomeTabs()Ljava/util/List;

    move-result-object p2

    if-eqz p2, :cond_7

    check-cast p2, Ljava/lang/Iterable;

    invoke-interface {p2}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    move-result-object p2

    :goto_2
    invoke-interface {p2}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_7

    invoke-interface {p2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    add-int/lit8 v2, v0, 0x1

    if-gez v0, :cond_4

    invoke-static {}, Lkotlin/collections/CollectionsKt;->u()V

    :cond_4
    check-cast v1, Lcom/transsion/home/<USER>/HomeTabItem;

    if-eqz v1, :cond_5

    invoke-virtual {v1}, Lcom/transsion/home/<USER>/HomeTabItem;->getTabCode()Ljava/lang/String;

    move-result-object v1

    goto :goto_3

    :cond_5
    const/4 v1, 0x0

    :goto_3
    invoke-static {v1, p1}, Lkotlin/jvm/internal/Intrinsics;->b(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_6

    return v0

    :cond_6
    move v0, v2

    goto :goto_2

    :cond_7
    sget p1, Lcom/transsion/home/<USER>/HomeFragment;->k:I

    return p1
.end method

.method public final k0()I
    .locals 1

    iget v0, p0, Lcom/transsion/home/<USER>/HomeFragment;->b:I

    return v0
.end method

.method public final l0()Ljava/lang/String;
    .locals 2

    iget-object v0, p0, Lcom/transsion/home/<USER>/HomeFragment;->a:Lcom/transsion/home/<USER>/AppTab;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Lcom/transsion/home/<USER>/AppTab;->getHomeTabs()Ljava/util/List;

    move-result-object v0

    if-eqz v0, :cond_0

    iget v1, p0, Lcom/transsion/home/<USER>/HomeFragment;->b:I

    invoke-interface {v0, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/transsion/home/<USER>/HomeTabItem;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Lcom/transsion/home/<USER>/HomeTabItem;->getTabCode()Ljava/lang/String;

    move-result-object v0

    if-nez v0, :cond_1

    :cond_0
    const-string v0, ""

    :cond_1
    return-object v0
.end method

.method public lazyLoadData()V
    .locals 0

    return-void
.end method

.method public logPause()V
    .locals 5

    invoke-super {p0}, Lcom/transsion/baseui/fragment/BaseFragment;->logPause()V

    iget-wide v0, p0, Lcom/transsion/home/<USER>/HomeFragment;->h:J

    const-wide/16 v2, 0x0

    cmp-long v4, v0, v2

    if-eqz v4, :cond_0

    invoke-static {}, Landroid/os/SystemClock;->elapsedRealtime()J

    move-result-wide v0

    iget-wide v2, p0, Lcom/transsion/home/<USER>/HomeFragment;->h:J

    sub-long/2addr v0, v2

    sget-object v2, Lcom/transsion/baselib/report/FirebaseAnalyticsManager;->a:Lcom/transsion/baselib/report/FirebaseAnalyticsManager;

    invoke-static {v0, v1}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    move-result-object v0

    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getContext()Landroid/content/Context;

    move-result-object v1

    const-string v3, "home"

    invoke-virtual {v2, v3, v0, v1}, Lcom/transsion/baselib/report/FirebaseAnalyticsManager;->o(Ljava/lang/String;Ljava/lang/Long;Landroid/content/Context;)V

    :cond_0
    return-void
.end method

.method public logResume()V
    .locals 2

    invoke-super {p0}, Lcom/transsion/baseui/fragment/BaseFragment;->logResume()V

    invoke-static {}, Landroid/os/SystemClock;->elapsedRealtime()J

    move-result-wide v0

    iput-wide v0, p0, Lcom/transsion/home/<USER>/HomeFragment;->h:J

    return-void
.end method

.method public final m0()I
    .locals 1

    iget-object v0, p0, Lcom/transsion/home/<USER>/HomeFragment;->a:Lcom/transsion/home/<USER>/AppTab;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Lcom/transsion/home/<USER>/AppTab;->getHomeTabs()Ljava/util/List;

    move-result-object v0

    if-eqz v0, :cond_0

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    goto :goto_0

    :cond_0
    const/4 v0, 0x1

    :goto_0
    return v0
.end method

.method public n0(Landroid/view/LayoutInflater;)Lum/j;
    .locals 1

    const-string v0, "inflater"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-static {p1}, Lum/j;->c(Landroid/view/LayoutInflater;)Lum/j;

    move-result-object p1

    const-string v0, "inflate(inflater)"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    return-object p1
.end method

.method public newIntent(Landroid/content/Intent;)V
    .locals 6

    invoke-super {p0, p1}, Lcom/transsion/baseui/fragment/BaseFragment;->newIntent(Landroid/content/Intent;)V

    :try_start_0
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->isAdded()Z

    move-result v0

    const/4 v1, 0x0

    if-eqz v0, :cond_5

    if-eqz p1, :cond_0

    const-string v0, "topTab"

    invoke-virtual {p1, v0}, Landroid/content/Intent;->getStringExtra(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    if-nez v0, :cond_1

    goto :goto_0

    :catch_0
    move-exception p1

    goto :goto_1

    :cond_0
    :goto_0
    const-string v0, "Trending"

    :cond_1
    const/4 v2, -0x1

    if-eqz p1, :cond_2

    const-string v3, "secondTabIndex"

    invoke-virtual {p1, v3, v2}, Landroid/content/Intent;->getIntExtra(Ljava/lang/String;I)I

    move-result v2

    :cond_2
    invoke-virtual {p0, v0, v2}, Lcom/transsion/home/<USER>/HomeFragment;->j0(Ljava/lang/String;I)I

    move-result p1

    iput p1, p0, Lcom/transsion/home/<USER>/HomeFragment;->b:I

    invoke-virtual {p0}, Lcom/transsion/baseui/fragment/BaseFragment;->getMViewBinding()Ls4/a;

    move-result-object p1

    check-cast p1, Lum/j;

    if-eqz p1, :cond_3

    iget-object v1, p1, Lum/j;->l:Landroidx/viewpager2/widget/ViewPager2;

    :cond_3
    if-nez v1, :cond_4

    goto :goto_2

    :cond_4
    iget p1, p0, Lcom/transsion/home/<USER>/HomeFragment;->b:I

    invoke-virtual {v1, p1}, Landroidx/viewpager2/widget/ViewPager2;->setCurrentItem(I)V

    goto :goto_2

    :cond_5
    if-eqz p1, :cond_6

    invoke-virtual {p1}, Landroid/content/Intent;->getExtras()Landroid/os/Bundle;

    move-result-object v1

    :cond_6
    invoke-virtual {p0, v1}, Landroidx/fragment/app/Fragment;->setArguments(Landroid/os/Bundle;)V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_2

    :goto_1
    sget-object v0, Lxi/b;->a:Lxi/b$a;

    const-string v1, "HomeFragment"

    invoke-virtual {p1}, Ljava/lang/Throwable;->getMessage()Ljava/lang/String;

    move-result-object p1

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "error= "

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    const/4 v3, 0x0

    const/4 v4, 0x4

    const/4 v5, 0x0

    invoke-static/range {v0 .. v5}, Lxi/b$a;->f(Lxi/b$a;Ljava/lang/String;Ljava/lang/String;ZILjava/lang/Object;)V

    :goto_2
    return-void
.end method

.method public final o0()V
    .locals 8

    sget-object v0, Lcom/transsion/home/<USER>/preload/PreloadTrendingData;->o:Lcom/transsion/home/<USER>/preload/PreloadTrendingData$a;

    invoke-virtual {v0}, Lcom/transsion/home/<USER>/preload/PreloadTrendingData$a;->a()Lcom/transsion/home/<USER>/preload/PreloadTrendingData;

    move-result-object v1

    invoke-virtual {v1}, Lcom/transsion/home/<USER>/preload/PreloadTrendingData;->v()Landroidx/lifecycle/c0;

    move-result-object v1

    invoke-virtual {v1}, Landroidx/lifecycle/LiveData;->f()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/transsion/home/<USER>/AppTab;

    iput-object v1, p0, Lcom/transsion/home/<USER>/HomeFragment;->a:Lcom/transsion/home/<USER>/AppTab;

    sget-object v2, Lxi/b;->a:Lxi/b$a;

    const-string v3, "HomeFragment"

    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    const-string v5, "homeTabFromCache = "

    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v4

    const/4 v5, 0x0

    const/4 v6, 0x4

    const/4 v7, 0x0

    invoke-static/range {v2 .. v7}, Lxi/b$a;->f(Lxi/b$a;Ljava/lang/String;Ljava/lang/String;ZILjava/lang/Object;)V

    iget-object v1, p0, Lcom/transsion/home/<USER>/HomeFragment;->a:Lcom/transsion/home/<USER>/AppTab;

    if-nez v1, :cond_0

    invoke-virtual {v0}, Lcom/transsion/home/<USER>/preload/PreloadTrendingData$a;->a()Lcom/transsion/home/<USER>/preload/PreloadTrendingData;

    move-result-object v1

    invoke-virtual {v1}, Lcom/transsion/home/<USER>/preload/PreloadTrendingData;->v()Landroidx/lifecycle/c0;

    move-result-object v1

    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getViewLifecycleOwner()Landroidx/lifecycle/u;

    move-result-object v2

    new-instance v3, Lcom/transsion/home/<USER>/HomeFragment$initHomeTab$1;

    invoke-direct {v3, p0}, Lcom/transsion/home/<USER>/HomeFragment$initHomeTab$1;-><init>(Lcom/transsion/home/<USER>/HomeFragment;)V

    new-instance v4, Lcom/transsion/home/<USER>/HomeFragment$f;

    invoke-direct {v4, v3}, Lcom/transsion/home/<USER>/HomeFragment$f;-><init>(Lkotlin/jvm/functions/Function1;)V

    invoke-virtual {v1, v2, v4}, Landroidx/lifecycle/LiveData;->j(Landroidx/lifecycle/u;Landroidx/lifecycle/d0;)V

    :cond_0
    invoke-virtual {v0}, Lcom/transsion/home/<USER>/preload/PreloadTrendingData$a;->a()Lcom/transsion/home/<USER>/preload/PreloadTrendingData;

    move-result-object v1

    invoke-virtual {v1}, Lcom/transsion/home/<USER>/preload/PreloadTrendingData;->w()Landroidx/lifecycle/c0;

    move-result-object v1

    invoke-virtual {v1, p0}, Landroidx/lifecycle/LiveData;->p(Landroidx/lifecycle/u;)V

    invoke-virtual {v0}, Lcom/transsion/home/<USER>/preload/PreloadTrendingData$a;->a()Lcom/transsion/home/<USER>/preload/PreloadTrendingData;

    move-result-object v0

    invoke-virtual {v0}, Lcom/transsion/home/<USER>/preload/PreloadTrendingData;->w()Landroidx/lifecycle/c0;

    move-result-object v0

    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getViewLifecycleOwner()Landroidx/lifecycle/u;

    move-result-object v1

    new-instance v2, Lcom/transsion/home/<USER>/HomeFragment$initHomeTab$2;

    invoke-direct {v2, p0}, Lcom/transsion/home/<USER>/HomeFragment$initHomeTab$2;-><init>(Lcom/transsion/home/<USER>/HomeFragment;)V

    new-instance v3, Lcom/transsion/home/<USER>/HomeFragment$f;

    invoke-direct {v3, v2}, Lcom/transsion/home/<USER>/HomeFragment$f;-><init>(Lkotlin/jvm/functions/Function1;)V

    invoke-virtual {v0, v1, v3}, Landroidx/lifecycle/LiveData;->j(Landroidx/lifecycle/u;Landroidx/lifecycle/d0;)V

    iget-object v0, p0, Lcom/transsion/home/<USER>/HomeFragment;->a:Lcom/transsion/home/<USER>/AppTab;

    if-eqz v0, :cond_1

    invoke-virtual {v0}, Lcom/transsion/home/<USER>/AppTab;->getHomeTabs()Ljava/util/List;

    move-result-object v0

    if-eqz v0, :cond_1

    check-cast v0, Ljava/util/Collection;

    invoke-interface {v0}, Ljava/util/Collection;->isEmpty()Z

    move-result v0

    const/4 v1, 0x1

    xor-int/2addr v0, v1

    if-ne v0, v1, :cond_1

    invoke-direct {p0}, Lcom/transsion/home/<USER>/HomeFragment;->p0()V

    :cond_1
    return-void
.end method

.method public onDestroy()V
    .locals 1

    invoke-super {p0}, Landroidx/fragment/app/Fragment;->onDestroy()V

    sget-object v0, Lcom/transsnet/downloader/util/DownloadSDCardUtil;->a:Lcom/transsnet/downloader/util/DownloadSDCardUtil;

    invoke-virtual {v0}, Lcom/transsnet/downloader/util/DownloadSDCardUtil;->e()V

    sget-object v0, Lcom/transsion/home/<USER>/preload/PreloadTrendingData;->o:Lcom/transsion/home/<USER>/preload/PreloadTrendingData$a;

    invoke-virtual {v0}, Lcom/transsion/home/<USER>/preload/PreloadTrendingData$a;->a()Lcom/transsion/home/<USER>/preload/PreloadTrendingData;

    move-result-object v0

    invoke-virtual {v0}, Lcom/transsion/home/<USER>/preload/PreloadTrendingData;->w()Landroidx/lifecycle/c0;

    move-result-object v0

    invoke-virtual {v0, p0}, Landroidx/lifecycle/LiveData;->p(Landroidx/lifecycle/u;)V

    iget-object v0, p0, Lcom/transsion/home/<USER>/HomeFragment;->f:Lcom/transsion/home/<USER>/HomeSearchViewManager;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Lcom/transsion/home/<USER>/HomeSearchViewManager;->H()V

    :cond_0
    iget-object v0, p0, Lcom/transsion/home/<USER>/HomeFragment;->f:Lcom/transsion/home/<USER>/HomeSearchViewManager;

    if-eqz v0, :cond_1

    invoke-virtual {v0}, Lcom/transsion/home/<USER>/HomeSearchViewManager;->n()V

    :cond_1
    return-void
.end method

.method public onHiddenChanged(Z)V
    .locals 7

    invoke-super {p0, p1}, Landroidx/fragment/app/Fragment;->onHiddenChanged(Z)V

    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->isResumed()Z

    move-result v0

    if-eqz v0, :cond_1

    if-eqz p1, :cond_0

    invoke-virtual {p0}, Lcom/transsion/home/<USER>/HomeFragment;->logPause()V

    iget-object v0, p0, Lcom/transsion/home/<USER>/HomeFragment;->f:Lcom/transsion/home/<USER>/HomeSearchViewManager;

    if-eqz v0, :cond_1

    invoke-virtual {v0}, Lcom/transsion/home/<USER>/HomeSearchViewManager;->H()V

    goto :goto_0

    :cond_0
    invoke-virtual {p0}, Lcom/transsion/home/<USER>/HomeFragment;->logResume()V

    iget-object v0, p0, Lcom/transsion/home/<USER>/HomeFragment;->f:Lcom/transsion/home/<USER>/HomeSearchViewManager;

    if-eqz v0, :cond_1

    invoke-virtual {v0}, Lcom/transsion/home/<USER>/HomeSearchViewManager;->I()V

    :cond_1
    :goto_0
    const/4 v1, 0x0

    const/4 v4, 0x0

    const/16 v5, 0x9

    const/4 v6, 0x0

    move-object v2, p0

    move v3, p1

    invoke-static/range {v1 .. v6}, Lcom/transsion/baseui/activity/d;->h(Ljava/lang/String;Landroidx/fragment/app/Fragment;ZLjava/lang/String;ILjava/lang/Object;)V

    return-void
.end method

.method public onPause()V
    .locals 4

    invoke-super {p0}, Lcom/transsion/baseui/fragment/BaseFragment;->onPause()V

    const/4 v0, 0x0

    const/4 v1, 0x5

    invoke-static {v0, p0, v0, v1, v0}, Lcom/transsion/baseui/activity/d;->l(Ljava/lang/String;Landroidx/fragment/app/Fragment;Ljava/lang/String;ILjava/lang/Object;)V

    sget-object v0, Lxi/b;->a:Lxi/b$a;

    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getChildFragmentManager()Landroidx/fragment/app/FragmentManager;

    move-result-object v1

    invoke-virtual {v1}, Landroidx/fragment/app/FragmentManager;->getFragments()Ljava/util/List;

    move-result-object v1

    invoke-interface {v1}, Ljava/util/List;->size()I

    move-result v1

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "onPause fragment count "

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    const/4 v2, 0x1

    invoke-virtual {v0, v1, v2}, Lxi/b$a;->d(Ljava/lang/String;Z)V

    iget-object v0, p0, Lcom/transsion/home/<USER>/HomeFragment;->f:Lcom/transsion/home/<USER>/HomeSearchViewManager;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Lcom/transsion/home/<USER>/HomeSearchViewManager;->H()V

    :cond_0
    return-void
.end method

.method public onResume()V
    .locals 7

    invoke-super {p0}, Lcom/transsion/baseui/fragment/BaseFragment;->onResume()V

    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->isVisible()Z

    move-result v0

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "visible="

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    const-string v0, " fragment:"

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    const/4 v1, 0x0

    const/4 v2, 0x1

    invoke-static {v1, p0, v0, v2, v1}, Lcom/transsion/baseui/activity/d;->p(Ljava/lang/String;Landroidx/fragment/app/Fragment;Ljava/lang/String;ILjava/lang/Object;)V

    iget-object v0, p0, Lcom/transsion/home/<USER>/HomeFragment;->f:Lcom/transsion/home/<USER>/HomeSearchViewManager;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Lcom/transsion/home/<USER>/HomeSearchViewManager;->I()V

    :cond_0
    iget-object v0, p0, Lcom/transsion/home/<USER>/HomeFragment;->g:Landroid/os/Bundle;

    if-eqz v0, :cond_1

    const-string v3, "saved_search_color"

    const/4 v4, 0x2

    invoke-virtual {v0, v3, v4}, Landroid/os/BaseBundle;->getInt(Ljava/lang/String;I)I

    move-result v0

    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v0

    goto :goto_0

    :cond_1
    move-object v0, v1

    :goto_0
    if-nez v0, :cond_2

    goto :goto_1

    :cond_2
    invoke-virtual {v0}, Ljava/lang/Integer;->intValue()I

    move-result v3

    if-eqz v3, :cond_4

    :goto_1
    if-nez v0, :cond_3

    goto :goto_7

    :cond_3
    invoke-virtual {v0}, Ljava/lang/Integer;->intValue()I

    move-result v3

    if-ne v3, v2, :cond_a

    :cond_4
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getChildFragmentManager()Landroidx/fragment/app/FragmentManager;

    move-result-object v3

    iget v4, p0, Lcom/transsion/home/<USER>/HomeFragment;->b:I

    new-instance v5, Ljava/lang/StringBuilder;

    invoke-direct {v5}, Ljava/lang/StringBuilder;-><init>()V

    const-string v6, "f"

    invoke-virtual {v5, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v5, v4}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v5}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v3, v4}, Landroidx/fragment/app/FragmentManager;->findFragmentByTag(Ljava/lang/String;)Landroidx/fragment/app/Fragment;

    move-result-object v3

    instance-of v4, v3, Lcom/transsion/home/<USER>/tab/BaseHomeSubFragment;

    const/4 v5, 0x0

    if-eqz v4, :cond_7

    check-cast v3, Lcom/transsion/home/<USER>/tab/BaseHomeSubFragment;

    if-nez v0, :cond_5

    goto :goto_2

    :cond_5
    invoke-virtual {v0}, Ljava/lang/Integer;->intValue()I

    move-result v0

    if-ne v0, v2, :cond_6

    goto :goto_3

    :cond_6
    :goto_2
    const/4 v2, 0x0

    :goto_3
    invoke-virtual {v3, v2}, Lcom/transsion/home/<USER>/tab/BaseHomeSubFragment;->Y(Z)V

    goto :goto_6

    :cond_7
    if-nez v0, :cond_8

    goto :goto_4

    :cond_8
    invoke-virtual {v0}, Ljava/lang/Integer;->intValue()I

    move-result v0

    if-ne v0, v2, :cond_9

    goto :goto_5

    :cond_9
    :goto_4
    const/4 v2, 0x0

    :goto_5
    invoke-virtual {p0, v2}, Lcom/transsion/home/<USER>/HomeFragment;->g0(Z)V

    :goto_6
    iput-object v1, p0, Lcom/transsion/home/<USER>/HomeFragment;->g:Landroid/os/Bundle;

    :cond_a
    :goto_7
    return-void
.end method

.method public onSaveInstanceState(Landroid/os/Bundle;)V
    .locals 3

    const-string v0, "outState"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-super {p0, p1}, Landroidx/fragment/app/Fragment;->onSaveInstanceState(Landroid/os/Bundle;)V

    const-string v0, "current_tab"

    iget v1, p0, Lcom/transsion/home/<USER>/HomeFragment;->b:I

    invoke-virtual {p1, v0, v1}, Landroid/os/BaseBundle;->putInt(Ljava/lang/String;I)V

    iget-object v0, p0, Lcom/transsion/home/<USER>/HomeFragment;->f:Lcom/transsion/home/<USER>/HomeSearchViewManager;

    const/4 v1, 0x0

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Lcom/transsion/home/<USER>/HomeSearchViewManager;->B()Z

    move-result v0

    const/4 v2, 0x1

    if-ne v0, v2, :cond_0

    const/4 v1, 0x1

    :cond_0
    const-string v0, "saved_search_color"

    invoke-virtual {p1, v0, v1}, Landroid/os/BaseBundle;->putInt(Ljava/lang/String;I)V

    return-void
.end method

.method public final q0()V
    .locals 9

    :try_start_0
    const-class v0, Landroidx/viewpager2/widget/ViewPager2;

    const-string v1, "mRecyclerView"

    invoke-virtual {v0, v1}, Ljava/lang/Class;->getDeclaredField(Ljava/lang/String;)Ljava/lang/reflect/Field;

    move-result-object v0

    const-string v1, "ViewPager2::class.java.g\u2026redField(\"mRecyclerView\")"

    invoke-static {v0, v1}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    const/4 v1, 0x1

    invoke-virtual {v0, v1}, Ljava/lang/reflect/AccessibleObject;->setAccessible(Z)V

    invoke-virtual {p0}, Lcom/transsion/baseui/fragment/BaseFragment;->getMViewBinding()Ls4/a;

    move-result-object v2

    check-cast v2, Lum/j;

    if-eqz v2, :cond_0

    iget-object v2, v2, Lum/j;->l:Landroidx/viewpager2/widget/ViewPager2;

    goto :goto_0

    :catch_0
    move-exception v0

    goto :goto_1

    :cond_0
    const/4 v2, 0x0

    :goto_0
    invoke-virtual {v0, v2}, Ljava/lang/reflect/Field;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    const-string v2, "null cannot be cast to non-null type androidx.recyclerview.widget.RecyclerView"

    invoke-static {v0, v2}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/String;)V

    check-cast v0, Landroidx/recyclerview/widget/RecyclerView;

    const-class v2, Landroidx/recyclerview/widget/RecyclerView;

    const-string v3, "mTouchSlop"

    invoke-virtual {v2, v3}, Ljava/lang/Class;->getDeclaredField(Ljava/lang/String;)Ljava/lang/reflect/Field;

    move-result-object v2

    const-string v3, "RecyclerView::class.java\u2026claredField(\"mTouchSlop\")"

    invoke-static {v2, v3}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-virtual {v2, v1}, Ljava/lang/reflect/AccessibleObject;->setAccessible(Z)V

    invoke-virtual {v2, v0}, Ljava/lang/reflect/Field;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    const-string v3, "null cannot be cast to non-null type kotlin.Int"

    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/String;)V

    check-cast v1, Ljava/lang/Integer;

    invoke-virtual {v1}, Ljava/lang/Integer;->intValue()I

    move-result v1

    mul-int/lit8 v1, v1, 0x2

    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    invoke-virtual {v2, v0, v1}, Ljava/lang/reflect/Field;->set(Ljava/lang/Object;Ljava/lang/Object;)V

    sget-object v3, Lxi/b;->a:Lxi/b$a;

    const-string v4, "invoke"

    const-string v5, "testOver"

    const/4 v6, 0x0

    const/4 v7, 0x4

    const/4 v8, 0x0

    invoke-static/range {v3 .. v8}, Lxi/b$a;->f(Lxi/b$a;Ljava/lang/String;Ljava/lang/String;ZILjava/lang/Object;)V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_2

    :goto_1
    sget-object v1, Lxi/b;->a:Lxi/b$a;

    const-string v2, "invoke"

    invoke-virtual {v0}, Ljava/lang/Throwable;->getMessage()Ljava/lang/String;

    move-result-object v0

    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v4, "Exception "

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    const/4 v4, 0x0

    const/4 v5, 0x4

    const/4 v6, 0x0

    invoke-static/range {v1 .. v6}, Lxi/b$a;->f(Lxi/b$a;Ljava/lang/String;Ljava/lang/String;ZILjava/lang/Object;)V

    :goto_2
    return-void
.end method

.method public final r0()Z
    .locals 4

    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getChildFragmentManager()Landroidx/fragment/app/FragmentManager;

    move-result-object v0

    iget v1, p0, Lcom/transsion/home/<USER>/HomeFragment;->b:I

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "f"

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Landroidx/fragment/app/FragmentManager;->findFragmentByTag(Ljava/lang/String;)Landroidx/fragment/app/Fragment;

    move-result-object v0

    instance-of v1, v0, Lcom/transsion/web/fragment/WebFragment;

    if-eqz v1, :cond_0

    check-cast v0, Lcom/transsion/web/fragment/WebFragment;

    invoke-virtual {v0}, Lcom/transsion/web/fragment/WebFragment;->Q0()Z

    move-result v0

    if-eqz v0, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method public final s0()V
    .locals 4

    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getChildFragmentManager()Landroidx/fragment/app/FragmentManager;

    move-result-object v0

    iget v1, p0, Lcom/transsion/home/<USER>/HomeFragment;->b:I

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "f"

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Landroidx/fragment/app/FragmentManager;->findFragmentByTag(Ljava/lang/String;)Landroidx/fragment/app/Fragment;

    move-result-object v0

    instance-of v1, v0, Lcom/transsion/home/<USER>/tab/BaseHomeSubFragment;

    if-eqz v1, :cond_0

    check-cast v0, Lcom/transsion/home/<USER>/tab/BaseHomeSubFragment;

    invoke-virtual {v0}, Lcom/transsion/home/<USER>/tab/BaseHomeSubFragment;->m0()V

    :cond_0
    return-void
.end method

.method public final u0()V
    .locals 1

    new-instance v0, Lcom/transsion/home/<USER>/HomeFragment$g;

    invoke-direct {v0, p0}, Lcom/transsion/home/<USER>/HomeFragment$g;-><init>(Lcom/transsion/home/<USER>/HomeFragment;)V

    invoke-virtual {p0, v0}, Lcom/transsion/baseui/fragment/BaseFragment;->setNetListener(Lcom/tn/lib/util/networkinfo/g;)V

    return-void
.end method

.method public final v0(I)V
    .locals 0

    iput p1, p0, Lcom/transsion/home/<USER>/HomeFragment;->b:I

    return-void
.end method

.method public final w0()V
    .locals 8

    sget-object v0, Lcom/transsion/home/<USER>/g;->a:Lcom/transsion/home/<USER>/g;

    invoke-virtual {v0}, Lcom/transsion/home/<USER>/g;->a()Z

    move-result v0

    if-nez v0, :cond_0

    return-void

    :cond_0
    sget-object v0, Lcom/transsion/home/<USER>/SearchGuideDialog;->d:Lcom/transsion/home/<USER>/SearchGuideDialog$a;

    invoke-virtual {v0}, Lcom/transsion/home/<USER>/SearchGuideDialog$a;->b()Lcom/transsion/home/<USER>/SearchGuideDialog;

    move-result-object v0

    new-instance v1, Lcom/transsion/home/<USER>/HomeFragment$showSearchGuideDialogIfNeed$1;

    invoke-direct {v1, p0}, Lcom/transsion/home/<USER>/HomeFragment$showSearchGuideDialogIfNeed$1;-><init>(Lcom/transsion/home/<USER>/HomeFragment;)V

    invoke-virtual {v0, v1}, Lcom/transsion/home/<USER>/SearchGuideDialog;->b0(Lkotlin/jvm/functions/Function0;)V

    iget-object v1, p0, Lcom/transsion/home/<USER>/HomeFragment;->f:Lcom/transsion/home/<USER>/HomeSearchViewManager;

    if-eqz v1, :cond_1

    invoke-virtual {v1}, Lcom/transsion/home/<USER>/HomeSearchViewManager;->o()Landroidx/constraintlayout/widget/ConstraintLayout$b;

    move-result-object v1

    goto :goto_0

    :cond_1
    const/4 v1, 0x0

    :goto_0
    invoke-virtual {v0, v1}, Lcom/transsion/home/<USER>/SearchGuideDialog;->e0(Landroidx/constraintlayout/widget/ConstraintLayout$b;)V

    const-string v1, "search_guide"

    invoke-virtual {v0, p0, v1}, Lcom/transsion/baseui/dialog/BaseDialog;->showDialog(Landroidx/fragment/app/Fragment;Ljava/lang/String;)V

    sget-object v2, Lxi/b;->a:Lxi/b$a;

    const-string v3, "HomeFragment"

    const-string v4, "show dialog for search guide"

    const/4 v5, 0x0

    const/4 v6, 0x4

    const/4 v7, 0x0

    invoke-static/range {v2 .. v7}, Lxi/b$a;->f(Lxi/b$a;Ljava/lang/String;Ljava/lang/String;ZILjava/lang/Object;)V

    return-void
.end method

.method public final x0(Ljava/lang/String;I)V
    .locals 1

    const-string v0, "tabCode"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-virtual {p0, p1, p2}, Lcom/transsion/home/<USER>/HomeFragment;->j0(Ljava/lang/String;I)I

    move-result p1

    invoke-virtual {p0}, Lcom/transsion/baseui/fragment/BaseFragment;->getMViewBinding()Ls4/a;

    move-result-object p2

    check-cast p2, Lum/j;

    if-eqz p2, :cond_0

    iget-object p2, p2, Lum/j;->l:Landroidx/viewpager2/widget/ViewPager2;

    if-eqz p2, :cond_0

    const/4 v0, 0x0

    invoke-virtual {p2, p1, v0}, Landroidx/viewpager2/widget/ViewPager2;->setCurrentItem(IZ)V

    :cond_0
    return-void
.end method

.method public final y0()V
    .locals 3

    iget-object v0, p0, Lcom/transsion/home/<USER>/HomeFragment;->c:Lcom/transsion/home/<USER>/tab/TrendingFragment;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Lcom/transsion/home/<USER>/tab/TrendingFragment;->F1()V

    :cond_0
    invoke-virtual {p0}, Lcom/transsion/baseui/fragment/BaseFragment;->getMViewBinding()Ls4/a;

    move-result-object v0

    check-cast v0, Lum/j;

    if-eqz v0, :cond_1

    iget-object v0, v0, Lum/j;->l:Landroidx/viewpager2/widget/ViewPager2;

    if-eqz v0, :cond_1

    sget v1, Lcom/transsion/home/<USER>/HomeFragment;->k:I

    const/4 v2, 0x0

    invoke-virtual {v0, v1, v2}, Landroidx/viewpager2/widget/ViewPager2;->setCurrentItem(IZ)V

    :cond_1
    return-void
.end method

.method public final z0(Lcom/transsion/home/<USER>/AppTab;)V
    .locals 4

    if-eqz p1, :cond_6

    invoke-virtual {p1}, Lcom/transsion/home/<USER>/AppTab;->getHomeTabs()Ljava/util/List;

    move-result-object v0

    if-eqz v0, :cond_6

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    if-lez v0, :cond_6

    invoke-virtual {p1}, Lcom/transsion/home/<USER>/AppTab;->getVersion()Ljava/lang/String;

    move-result-object v0

    iget-object v1, p0, Lcom/transsion/home/<USER>/HomeFragment;->a:Lcom/transsion/home/<USER>/AppTab;

    const/4 v2, 0x0

    if-eqz v1, :cond_0

    invoke-virtual {v1}, Lcom/transsion/home/<USER>/AppTab;->getVersion()Ljava/lang/String;

    move-result-object v1

    goto :goto_0

    :cond_0
    move-object v1, v2

    :goto_0
    invoke-static {v0, v1}, Lkotlin/jvm/internal/Intrinsics;->b(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_5

    invoke-virtual {p1}, Lcom/transsion/home/<USER>/AppTab;->getHomeTabs()Ljava/util/List;

    move-result-object v0

    const/4 v1, 0x0

    if-eqz v0, :cond_1

    invoke-interface {v0, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/transsion/home/<USER>/HomeTabItem;

    if-eqz v0, :cond_1

    invoke-virtual {v0}, Lcom/transsion/home/<USER>/HomeTabItem;->getName()Ljava/lang/String;

    move-result-object v0

    goto :goto_1

    :cond_1
    move-object v0, v2

    :goto_1
    iget-object v3, p0, Lcom/transsion/home/<USER>/HomeFragment;->a:Lcom/transsion/home/<USER>/AppTab;

    if-eqz v3, :cond_2

    invoke-virtual {v3}, Lcom/transsion/home/<USER>/AppTab;->getHomeTabs()Ljava/util/List;

    move-result-object v3

    if-eqz v3, :cond_2

    invoke-interface {v3, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/transsion/home/<USER>/HomeTabItem;

    if-eqz v1, :cond_2

    invoke-virtual {v1}, Lcom/transsion/home/<USER>/HomeTabItem;->getName()Ljava/lang/String;

    move-result-object v1

    goto :goto_2

    :cond_2
    move-object v1, v2

    :goto_2
    invoke-static {v0, v1}, Lkotlin/jvm/internal/Intrinsics;->b(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_5

    invoke-virtual {p1}, Lcom/transsion/home/<USER>/AppTab;->getHomeTabs()Ljava/util/List;

    move-result-object v0

    if-eqz v0, :cond_3

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v0

    goto :goto_3

    :cond_3
    move-object v0, v2

    :goto_3
    iget-object v1, p0, Lcom/transsion/home/<USER>/HomeFragment;->a:Lcom/transsion/home/<USER>/AppTab;

    if-eqz v1, :cond_4

    invoke-virtual {v1}, Lcom/transsion/home/<USER>/AppTab;->getHomeTabs()Ljava/util/List;

    move-result-object v1

    if-eqz v1, :cond_4

    invoke-interface {v1}, Ljava/util/List;->size()I

    move-result v1

    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    goto :goto_4

    :cond_4
    move-object v1, v2

    :goto_4
    invoke-static {v0, v1}, Lkotlin/jvm/internal/Intrinsics;->b(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_6

    :cond_5
    iput-object p1, p0, Lcom/transsion/home/<USER>/HomeFragment;->a:Lcom/transsion/home/<USER>/AppTab;

    iput-object v2, p0, Lcom/transsion/home/<USER>/HomeFragment;->c:Lcom/transsion/home/<USER>/tab/TrendingFragment;

    invoke-direct {p0}, Lcom/transsion/home/<USER>/HomeFragment;->p0()V

    iget-object v0, p0, Lcom/transsion/home/<USER>/HomeFragment;->f:Lcom/transsion/home/<USER>/HomeSearchViewManager;

    if-eqz v0, :cond_6

    invoke-virtual {v0, p1}, Lcom/transsion/home/<USER>/HomeSearchViewManager;->J(Lcom/transsion/home/<USER>/AppTab;)V

    :cond_6
    return-void
.end method
