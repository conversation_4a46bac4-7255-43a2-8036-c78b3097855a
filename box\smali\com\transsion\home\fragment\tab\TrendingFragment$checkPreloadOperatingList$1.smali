.class final Lcom/transsion/home/<USER>/tab/TrendingFragment$checkPreloadOperatingList$1;
.super Lkotlin/jvm/internal/Lambda;

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/transsion/home/<USER>/tab/TrendingFragment;->Y0()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/jvm/internal/Lambda;",
        "Lkotlin/jvm/functions/Function1<",
        "Lcom/transsion/moviedetailapi/bean/MainOperateData;",
        "Lkotlin/Unit;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation


# instance fields
.field final synthetic $startRender:Lkotlin/jvm/functions/Function1;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlin/jvm/functions/Function1<",
            "Ljava/lang/<PERSON>;",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation
.end field

.field final synthetic $useCacheRender:Z

.field final synthetic this$0:Lcom/transsion/home/<USER>/tab/TrendingFragment;


# direct methods
.method public constructor <init>(Lcom/transsion/home/<USER>/tab/TrendingFragment;ZLkotlin/jvm/functions/Function1;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/transsion/home/<USER>/tab/TrendingFragment;",
            "Z",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Ljava/lang/Boolean;",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lcom/transsion/home/<USER>/tab/TrendingFragment$checkPreloadOperatingList$1;->this$0:Lcom/transsion/home/<USER>/tab/TrendingFragment;

    iput-boolean p2, p0, Lcom/transsion/home/<USER>/tab/TrendingFragment$checkPreloadOperatingList$1;->$useCacheRender:Z

    iput-object p3, p0, Lcom/transsion/home/<USER>/tab/TrendingFragment$checkPreloadOperatingList$1;->$startRender:Lkotlin/jvm/functions/Function1;

    const/4 p1, 0x1

    invoke-direct {p0, p1}, Lkotlin/jvm/internal/Lambda;-><init>(I)V

    return-void
.end method


# virtual methods
.method public bridge synthetic invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    check-cast p1, Lcom/transsion/moviedetailapi/bean/MainOperateData;

    invoke-virtual {p0, p1}, Lcom/transsion/home/<USER>/tab/TrendingFragment$checkPreloadOperatingList$1;->invoke(Lcom/transsion/moviedetailapi/bean/MainOperateData;)V

    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    return-object p1
.end method

.method public final invoke(Lcom/transsion/moviedetailapi/bean/MainOperateData;)V
    .locals 4

    iget-object v0, p0, Lcom/transsion/home/<USER>/tab/TrendingFragment$checkPreloadOperatingList$1;->this$0:Lcom/transsion/home/<USER>/tab/TrendingFragment;

    invoke-virtual {v0}, Lcom/transsion/baseui/fragment/BaseFragment;->getLogViewConfig()Lcom/transsion/baselib/report/h;

    move-result-object v0

    const/4 v1, 0x0

    if-eqz v0, :cond_1

    invoke-virtual {v0}, Lcom/transsion/baselib/report/h;->g()Ljava/util/HashMap;

    move-result-object v0

    if-eqz v0, :cond_1

    if-eqz p1, :cond_0

    invoke-virtual {p1}, Lcom/transsion/moviedetailapi/bean/MainOperateData;->getItems()Ljava/util/List;

    move-result-object v2

    if-eqz v2, :cond_0

    invoke-interface {v2}, Ljava/util/List;->size()I

    move-result v2

    invoke-static {v2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v2

    goto :goto_0

    :cond_0
    move-object v2, v1

    :goto_0
    invoke-static {v2}, Ljava/lang/String;->valueOf(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v2

    const-string v3, "enterCache"

    invoke-interface {v0, v3, v2}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    :cond_1
    iget-boolean v0, p0, Lcom/transsion/home/<USER>/tab/TrendingFragment$checkPreloadOperatingList$1;->$useCacheRender:Z

    if-eqz v0, :cond_5

    iget-object v0, p0, Lcom/transsion/home/<USER>/tab/TrendingFragment$checkPreloadOperatingList$1;->this$0:Lcom/transsion/home/<USER>/tab/TrendingFragment;

    invoke-static {v0}, Lcom/transsion/home/<USER>/tab/TrendingFragment;->B0(Lcom/transsion/home/<USER>/tab/TrendingFragment;)Lcom/transsion/home/<USER>/trending/TrendingAdapter;

    move-result-object v0

    if-nez v0, :cond_2

    const-string v0, "mAdapter"

    invoke-static {v0}, Lkotlin/jvm/internal/Intrinsics;->y(Ljava/lang/String;)V

    move-object v0, v1

    :cond_2
    invoke-virtual {v0}, Lcom/chad/library/adapter/base/BaseQuickAdapter;->E()Ljava/util/List;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    if-nez v0, :cond_5

    if-eqz p1, :cond_3

    invoke-virtual {p1}, Lcom/transsion/moviedetailapi/bean/MainOperateData;->getItems()Ljava/util/List;

    move-result-object v1

    :cond_3
    check-cast v1, Ljava/util/Collection;

    if-eqz v1, :cond_5

    invoke-interface {v1}, Ljava/util/Collection;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_4

    goto :goto_1

    :cond_4
    iget-object v0, p0, Lcom/transsion/home/<USER>/tab/TrendingFragment$checkPreloadOperatingList$1;->this$0:Lcom/transsion/home/<USER>/tab/TrendingFragment;

    invoke-static {v0, p1}, Lcom/transsion/home/<USER>/tab/TrendingFragment;->T0(Lcom/transsion/home/<USER>/tab/TrendingFragment;Lcom/transsion/moviedetailapi/bean/MainOperateData;)V

    iget-object p1, p0, Lcom/transsion/home/<USER>/tab/TrendingFragment$checkPreloadOperatingList$1;->$startRender:Lkotlin/jvm/functions/Function1;

    sget-object v0, Ljava/lang/Boolean;->TRUE:Ljava/lang/Boolean;

    invoke-interface {p1, v0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    :cond_5
    :goto_1
    return-void
.end method
