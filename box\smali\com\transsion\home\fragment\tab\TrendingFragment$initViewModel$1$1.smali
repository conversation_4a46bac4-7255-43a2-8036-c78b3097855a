.class final Lcom/transsion/home/<USER>/tab/TrendingFragment$initViewModel$1$1;
.super Lkotlin/jvm/internal/Lambda;

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/transsion/home/<USER>/tab/TrendingFragment;->z1()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/jvm/internal/Lambda;",
        "Lkotlin/jvm/functions/Function1<",
        "Lcom/tn/lib/net/bean/BaseDto<",
        "Lcom/transsion/home/<USER>/TrendingRespData;",
        ">;",
        "Lkotlin/Unit;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation


# instance fields
.field final synthetic this$0:Lcom/transsion/home/<USER>/tab/TrendingFragment;


# direct methods
.method public constructor <init>(Lcom/transsion/home/<USER>/tab/TrendingFragment;)V
    .locals 0

    iput-object p1, p0, Lcom/transsion/home/<USER>/tab/TrendingFragment$initViewModel$1$1;->this$0:Lcom/transsion/home/<USER>/tab/TrendingFragment;

    const/4 p1, 0x1

    invoke-direct {p0, p1}, Lkotlin/jvm/internal/Lambda;-><init>(I)V

    return-void
.end method


# virtual methods
.method public bridge synthetic invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    check-cast p1, Lcom/tn/lib/net/bean/BaseDto;

    invoke-virtual {p0, p1}, Lcom/transsion/home/<USER>/tab/TrendingFragment$initViewModel$1$1;->invoke(Lcom/tn/lib/net/bean/BaseDto;)V

    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    return-object p1
.end method

.method public final invoke(Lcom/tn/lib/net/bean/BaseDto;)V
    .locals 8
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/tn/lib/net/bean/BaseDto<",
            "Lcom/transsion/home/<USER>/TrendingRespData;",
            ">;)V"
        }
    .end annotation

    iget-object v0, p0, Lcom/transsion/home/<USER>/tab/TrendingFragment$initViewModel$1$1;->this$0:Lcom/transsion/home/<USER>/tab/TrendingFragment;

    invoke-static {v0}, Lcom/transsion/home/<USER>/tab/TrendingFragment;->H0(Lcom/transsion/home/<USER>/tab/TrendingFragment;)Lcom/transsion/moviedetailapi/bean/MainOperateData;

    move-result-object v0

    const/4 v1, 0x0

    const/4 v2, 0x1

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Lcom/transsion/moviedetailapi/bean/MainOperateData;->getItems()Ljava/util/List;

    move-result-object v0

    if-eqz v0, :cond_0

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    if-lez v0, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    if-nez v0, :cond_2

    iget-object v3, p0, Lcom/transsion/home/<USER>/tab/TrendingFragment$initViewModel$1$1;->this$0:Lcom/transsion/home/<USER>/tab/TrendingFragment;

    invoke-static {v3}, Lcom/transsion/home/<USER>/tab/TrendingFragment;->B0(Lcom/transsion/home/<USER>/tab/TrendingFragment;)Lcom/transsion/home/<USER>/trending/TrendingAdapter;

    move-result-object v3

    if-nez v3, :cond_1

    const-string v3, "mAdapter"

    invoke-static {v3}, Lkotlin/jvm/internal/Intrinsics;->y(Ljava/lang/String;)V

    const/4 v3, 0x0

    :cond_1
    invoke-virtual {v3}, Lcom/chad/library/adapter/base/BaseQuickAdapter;->E()Ljava/util/List;

    move-result-object v3

    invoke-interface {v3}, Ljava/util/List;->size()I

    move-result v3

    if-lez v3, :cond_2

    iget-object v3, p0, Lcom/transsion/home/<USER>/tab/TrendingFragment$initViewModel$1$1;->this$0:Lcom/transsion/home/<USER>/tab/TrendingFragment;

    invoke-static {v3}, Lcom/transsion/home/<USER>/tab/TrendingFragment;->G0(Lcom/transsion/home/<USER>/tab/TrendingFragment;)Ljava/lang/String;

    move-result-object v3

    const-string v4, "1"

    invoke-static {v3, v4}, Lkotlin/jvm/internal/Intrinsics;->b(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v3

    if-eqz v3, :cond_2

    const/4 v1, 0x1

    :cond_2
    sget-object v2, Lxi/b;->a:Lxi/b$a;

    const-string v3, "PreloadTrending"

    invoke-virtual {p1}, Lcom/tn/lib/net/bean/BaseDto;->getCode()Ljava/lang/String;

    move-result-object v4

    new-instance v5, Ljava/lang/StringBuilder;

    invoke-direct {v5}, Ljava/lang/StringBuilder;-><init>()V

    const-string v6, "load code"

    invoke-virtual {v5, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v5, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v4, " isFresh "

    invoke-virtual {v5, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v5, v1}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    const-string v4, " hasOperating "

    invoke-virtual {v5, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v5, v0}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    invoke-virtual {v5}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v4

    const/4 v5, 0x0

    const/4 v6, 0x4

    const/4 v7, 0x0

    invoke-static/range {v2 .. v7}, Lxi/b$a;->f(Lxi/b$a;Ljava/lang/String;Ljava/lang/String;ZILjava/lang/Object;)V

    iget-object v0, p0, Lcom/transsion/home/<USER>/tab/TrendingFragment$initViewModel$1$1;->this$0:Lcom/transsion/home/<USER>/tab/TrendingFragment;

    invoke-static {v0, p1, v1}, Lcom/transsion/home/<USER>/tab/TrendingFragment;->U0(Lcom/transsion/home/<USER>/tab/TrendingFragment;Lcom/tn/lib/net/bean/BaseDto;Z)V

    return-void
.end method
