.class public final synthetic Lcom/transsion/home/<USER>/tab/a0;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/lang/Runnable;


# instance fields
.field public final synthetic a:Lcom/transsion/home/<USER>/tab/TrendingFragment;


# direct methods
.method public synthetic constructor <init>(Lcom/transsion/home/<USER>/tab/TrendingFragment;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/transsion/home/<USER>/tab/a0;->a:Lcom/transsion/home/<USER>/tab/TrendingFragment;

    return-void
.end method


# virtual methods
.method public final run()V
    .locals 1

    iget-object v0, p0, Lcom/transsion/home/<USER>/tab/a0;->a:Lcom/transsion/home/<USER>/tab/TrendingFragment;

    invoke-static {v0}, Lcom/transsion/home/<USER>/tab/TrendingFragment;->w0(Lcom/transsion/home/<USER>/tab/TrendingFragment;)V

    return-void
.end method
