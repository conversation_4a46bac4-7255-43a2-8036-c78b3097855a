.class public abstract Lcom/transsion/json/b/a;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/transsion/json/b/n;
.implements Lcom/transsion/json/b/g;


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public a()Ljava/lang/<PERSON>;
    .locals 1

    sget-object v0, Ljava/lang/<PERSON>;->FALSE:Ljava/lang/Bo<PERSON>an;

    return-object v0
.end method

.method public b()Lcom/transsion/json/i;
    .locals 1

    invoke-static {}, Lcom/transsion/json/i;->q()Lcom/transsion/json/i;

    move-result-object v0

    return-object v0
.end method
