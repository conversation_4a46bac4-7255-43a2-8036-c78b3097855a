.class final Lcom/transsion/json/v;
.super Lcom/transsion/json/b/p;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/transsion/json/w;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = null
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 3

    invoke-direct {p0}, Lcom/transsion/json/b/p;-><init>()V

    new-instance v0, Lcom/transsion/json/b/j;

    invoke-direct {v0}, Lcom/transsion/json/b/j;-><init>()V

    sget-object v1, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    new-instance v2, Lcom/transsion/json/b/o;

    invoke-direct {v2, v0}, Lcom/transsion/json/b/o;-><init>(Lcom/transsion/json/b/n;)V

    invoke-virtual {p0, v1, v2}, Lcom/transsion/json/b/p;->a(Ljava/lang/Class;Lcom/transsion/json/b/n;)Lcom/transsion/json/b/n;

    new-instance v0, Lcom/transsion/json/b/l;

    invoke-direct {v0}, Lcom/transsion/json/b/l;-><init>()V

    new-instance v1, Lcom/transsion/json/b/o;

    invoke-direct {v1, v0}, Lcom/transsion/json/b/o;-><init>(Lcom/transsion/json/b/n;)V

    const-class v0, Ljava/lang/Object;

    invoke-virtual {p0, v0, v1}, Lcom/transsion/json/b/p;->a(Ljava/lang/Class;Lcom/transsion/json/b/n;)Lcom/transsion/json/b/n;

    new-instance v0, Lcom/transsion/json/b/e;

    invoke-direct {v0}, Lcom/transsion/json/b/e;-><init>()V

    new-instance v1, Lcom/transsion/json/b/o;

    invoke-direct {v1, v0}, Lcom/transsion/json/b/o;-><init>(Lcom/transsion/json/b/n;)V

    const-class v0, Ljava/lang/Class;

    invoke-virtual {p0, v0, v1}, Lcom/transsion/json/b/p;->a(Ljava/lang/Class;Lcom/transsion/json/b/n;)Lcom/transsion/json/b/n;

    new-instance v0, Lcom/transsion/json/b/c;

    invoke-direct {v0}, Lcom/transsion/json/b/c;-><init>()V

    sget-object v1, Ljava/lang/Boolean;->TYPE:Ljava/lang/Class;

    new-instance v2, Lcom/transsion/json/b/o;

    invoke-direct {v2, v0}, Lcom/transsion/json/b/o;-><init>(Lcom/transsion/json/b/n;)V

    invoke-virtual {p0, v1, v2}, Lcom/transsion/json/b/p;->a(Ljava/lang/Class;Lcom/transsion/json/b/n;)Lcom/transsion/json/b/n;

    new-instance v1, Lcom/transsion/json/b/o;

    invoke-direct {v1, v0}, Lcom/transsion/json/b/o;-><init>(Lcom/transsion/json/b/n;)V

    const-class v0, Ljava/lang/Boolean;

    invoke-virtual {p0, v0, v1}, Lcom/transsion/json/b/p;->a(Ljava/lang/Class;Lcom/transsion/json/b/n;)Lcom/transsion/json/b/n;

    new-instance v0, Lcom/transsion/json/b/k;

    invoke-direct {v0}, Lcom/transsion/json/b/k;-><init>()V

    new-instance v1, Lcom/transsion/json/b/o;

    invoke-direct {v1, v0}, Lcom/transsion/json/b/o;-><init>(Lcom/transsion/json/b/n;)V

    const-class v2, Ljava/lang/Number;

    invoke-virtual {p0, v2, v1}, Lcom/transsion/json/b/p;->a(Ljava/lang/Class;Lcom/transsion/json/b/n;)Lcom/transsion/json/b/n;

    new-instance v1, Lcom/transsion/json/b/o;

    invoke-direct {v1, v0}, Lcom/transsion/json/b/o;-><init>(Lcom/transsion/json/b/n;)V

    const-class v2, Ljava/lang/Integer;

    invoke-virtual {p0, v2, v1}, Lcom/transsion/json/b/p;->a(Ljava/lang/Class;Lcom/transsion/json/b/n;)Lcom/transsion/json/b/n;

    sget-object v1, Ljava/lang/Integer;->TYPE:Ljava/lang/Class;

    new-instance v2, Lcom/transsion/json/b/o;

    invoke-direct {v2, v0}, Lcom/transsion/json/b/o;-><init>(Lcom/transsion/json/b/n;)V

    invoke-virtual {p0, v1, v2}, Lcom/transsion/json/b/p;->a(Ljava/lang/Class;Lcom/transsion/json/b/n;)Lcom/transsion/json/b/n;

    new-instance v1, Lcom/transsion/json/b/o;

    invoke-direct {v1, v0}, Lcom/transsion/json/b/o;-><init>(Lcom/transsion/json/b/n;)V

    const-class v2, Ljava/lang/Long;

    invoke-virtual {p0, v2, v1}, Lcom/transsion/json/b/p;->a(Ljava/lang/Class;Lcom/transsion/json/b/n;)Lcom/transsion/json/b/n;

    sget-object v1, Ljava/lang/Long;->TYPE:Ljava/lang/Class;

    new-instance v2, Lcom/transsion/json/b/o;

    invoke-direct {v2, v0}, Lcom/transsion/json/b/o;-><init>(Lcom/transsion/json/b/n;)V

    invoke-virtual {p0, v1, v2}, Lcom/transsion/json/b/p;->a(Ljava/lang/Class;Lcom/transsion/json/b/n;)Lcom/transsion/json/b/n;

    new-instance v1, Lcom/transsion/json/b/o;

    invoke-direct {v1, v0}, Lcom/transsion/json/b/o;-><init>(Lcom/transsion/json/b/n;)V

    const-class v2, Ljava/lang/Double;

    invoke-virtual {p0, v2, v1}, Lcom/transsion/json/b/p;->a(Ljava/lang/Class;Lcom/transsion/json/b/n;)Lcom/transsion/json/b/n;

    sget-object v1, Ljava/lang/Double;->TYPE:Ljava/lang/Class;

    new-instance v2, Lcom/transsion/json/b/o;

    invoke-direct {v2, v0}, Lcom/transsion/json/b/o;-><init>(Lcom/transsion/json/b/n;)V

    invoke-virtual {p0, v1, v2}, Lcom/transsion/json/b/p;->a(Ljava/lang/Class;Lcom/transsion/json/b/n;)Lcom/transsion/json/b/n;

    new-instance v1, Lcom/transsion/json/b/o;

    invoke-direct {v1, v0}, Lcom/transsion/json/b/o;-><init>(Lcom/transsion/json/b/n;)V

    const-class v2, Ljava/lang/Float;

    invoke-virtual {p0, v2, v1}, Lcom/transsion/json/b/p;->a(Ljava/lang/Class;Lcom/transsion/json/b/n;)Lcom/transsion/json/b/n;

    sget-object v1, Ljava/lang/Float;->TYPE:Ljava/lang/Class;

    new-instance v2, Lcom/transsion/json/b/o;

    invoke-direct {v2, v0}, Lcom/transsion/json/b/o;-><init>(Lcom/transsion/json/b/n;)V

    invoke-virtual {p0, v1, v2}, Lcom/transsion/json/b/p;->a(Ljava/lang/Class;Lcom/transsion/json/b/n;)Lcom/transsion/json/b/n;

    new-instance v0, Lcom/transsion/json/b/m;

    invoke-direct {v0}, Lcom/transsion/json/b/m;-><init>()V

    new-instance v1, Lcom/transsion/json/b/o;

    invoke-direct {v1, v0}, Lcom/transsion/json/b/o;-><init>(Lcom/transsion/json/b/n;)V

    const-class v0, Ljava/lang/String;

    invoke-virtual {p0, v0, v1}, Lcom/transsion/json/b/p;->a(Ljava/lang/Class;Lcom/transsion/json/b/n;)Lcom/transsion/json/b/n;

    new-instance v0, Lcom/transsion/json/b/d;

    invoke-direct {v0}, Lcom/transsion/json/b/d;-><init>()V

    new-instance v1, Lcom/transsion/json/b/o;

    invoke-direct {v1, v0}, Lcom/transsion/json/b/o;-><init>(Lcom/transsion/json/b/n;)V

    const-class v2, Ljava/lang/Character;

    invoke-virtual {p0, v2, v1}, Lcom/transsion/json/b/p;->a(Ljava/lang/Class;Lcom/transsion/json/b/n;)Lcom/transsion/json/b/n;

    sget-object v1, Ljava/lang/Character;->TYPE:Ljava/lang/Class;

    new-instance v2, Lcom/transsion/json/b/o;

    invoke-direct {v2, v0}, Lcom/transsion/json/b/o;-><init>(Lcom/transsion/json/b/n;)V

    invoke-virtual {p0, v1, v2}, Lcom/transsion/json/b/p;->a(Ljava/lang/Class;Lcom/transsion/json/b/n;)Lcom/transsion/json/b/n;

    new-instance v0, Lcom/transsion/json/b/f;

    invoke-direct {v0}, Lcom/transsion/json/b/f;-><init>()V

    new-instance v1, Lcom/transsion/json/b/o;

    invoke-direct {v1, v0}, Lcom/transsion/json/b/o;-><init>(Lcom/transsion/json/b/n;)V

    const-class v0, Ljava/lang/Enum;

    invoke-virtual {p0, v0, v1}, Lcom/transsion/json/b/p;->a(Ljava/lang/Class;Lcom/transsion/json/b/n;)Lcom/transsion/json/b/n;

    new-instance v0, Lcom/transsion/json/b/h;

    invoke-direct {v0}, Lcom/transsion/json/b/h;-><init>()V

    new-instance v1, Lcom/transsion/json/b/o;

    invoke-direct {v1, v0}, Lcom/transsion/json/b/o;-><init>(Lcom/transsion/json/b/n;)V

    const-class v0, Ljava/lang/Iterable;

    invoke-virtual {p0, v0, v1}, Lcom/transsion/json/b/p;->a(Ljava/lang/Class;Lcom/transsion/json/b/n;)Lcom/transsion/json/b/n;

    new-instance v0, Lcom/transsion/json/b/i;

    invoke-direct {v0}, Lcom/transsion/json/b/i;-><init>()V

    new-instance v1, Lcom/transsion/json/b/o;

    invoke-direct {v1, v0}, Lcom/transsion/json/b/o;-><init>(Lcom/transsion/json/b/n;)V

    const-class v0, Ljava/util/Map;

    invoke-virtual {p0, v0, v1}, Lcom/transsion/json/b/p;->a(Ljava/lang/Class;Lcom/transsion/json/b/n;)Lcom/transsion/json/b/n;

    new-instance v0, Lcom/transsion/json/b/b;

    invoke-direct {v0}, Lcom/transsion/json/b/b;-><init>()V

    new-instance v1, Lcom/transsion/json/b/o;

    invoke-direct {v1, v0}, Lcom/transsion/json/b/o;-><init>(Lcom/transsion/json/b/n;)V

    const-class v0, Ljava/util/Arrays;

    invoke-virtual {p0, v0, v1}, Lcom/transsion/json/b/p;->a(Ljava/lang/Class;Lcom/transsion/json/b/n;)Lcom/transsion/json/b/n;

    const/4 v0, 0x1

    iput-boolean v0, p0, Lcom/transsion/json/b/p;->b:Z

    return-void
.end method
