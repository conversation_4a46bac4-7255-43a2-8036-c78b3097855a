.class public Lcom/transsion/json/w;
.super Ljava/lang/Object;


# static fields
.field public static final a:Lcom/transsion/json/b/p;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    new-instance v0, Lcom/transsion/json/v;

    invoke-direct {v0}, Lcom/transsion/json/v;-><init>()V

    sput-object v0, Lcom/transsion/json/w;->a:Lcom/transsion/json/b/p;

    return-void
.end method

.method public static a()Lcom/transsion/json/b/p;
    .locals 1

    sget-object v0, Lcom/transsion/json/w;->a:Lcom/transsion/json/b/p;

    return-object v0
.end method
