.class public interface abstract Lcom/transsion/memberapi/IMemberApi;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/alibaba/android/arouter/facade/template/IProvider;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/transsion/memberapi/IMemberApi$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation


# virtual methods
.method public abstract A(Lcom/transsion/memberapi/MemberDetail;)V
.end method

.method public abstract B0()V
.end method

.method public abstract B1()V
.end method

.method public abstract C1()Ljava/lang/String;
.end method

.method public abstract H0(Lwn/e;)V
.end method

.method public abstract M()Lcom/transsion/memberapi/MemberDetail;
.end method

.method public abstract O()Z
.end method

.method public abstract P(Landroid/app/Activity;Lcom/transsion/memberapi/MemberSource;Lcom/transsion/memberapi/MemberCheckResult;Lwn/b;)V
.end method

.method public abstract P0(F)V
.end method

.method public abstract S(Lcom/transsion/memberapi/OpType;Lkotlin/jvm/functions/Function1;)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/transsion/memberapi/OpType;",
            "Lkotlin/jvm/functions/Function1<",
            "Ljava/lang/Object;",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation
.end method

.method public abstract U0(Landroid/app/Activity;Ljava/lang/String;Ljava/lang/String;Lwn/g;)V
.end method

.method public abstract Y(ILkotlin/jvm/functions/Function2;)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I",
            "Lkotlin/jvm/functions/Function2<",
            "-",
            "Ljava/lang/Boolean;",
            "-",
            "Ljava/lang/Boolean;",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation
.end method

.method public abstract a0()V
.end method

.method public abstract a1(Lkotlin/jvm/functions/Function2;Lkotlin/jvm/functions/Function0;)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function2<",
            "-",
            "Ljava/lang/String;",
            "-",
            "Ljava/lang/String;",
            "Lkotlin/Unit;",
            ">;",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation
.end method

.method public abstract e0(Landroid/app/Activity;Lcom/transsion/memberapi/MemberSource;Lwn/b;Z)V
.end method

.method public abstract f()Lkotlinx/coroutines/flow/f1;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/f1<",
            "Ljava/lang/Boolean;",
            ">;"
        }
    .end annotation
.end method

.method public abstract g1(Lwn/e;)V
.end method

.method public abstract h(Lcom/transsion/memberapi/MemberSceneType;Ljava/lang/Integer;Lkotlin/jvm/functions/Function1;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/transsion/memberapi/MemberSceneType;",
            "Ljava/lang/Integer;",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Lcom/transsion/memberapi/MemberCheckResult;",
            "Lkotlin/Unit;",
            ">;",
            "Lkotlin/coroutines/Continuation<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation
.end method

.method public abstract h0()Lkotlinx/coroutines/flow/f1;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/f1<",
            "Lcom/transsion/memberapi/MemberTaskItem;",
            ">;"
        }
    .end annotation
.end method

.method public abstract h1()Z
.end method

.method public abstract i()Z
.end method

.method public abstract k1(Lkotlin/jvm/functions/Function0;)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation
.end method

.method public abstract m1(Lcom/transsion/memberapi/MemberSceneType;Ljava/lang/Integer;Lwn/a;)V
.end method

.method public abstract n()Z
.end method

.method public abstract n1()I
.end method

.method public abstract o()V
.end method

.method public abstract o0()Landroidx/fragment/app/Fragment;
.end method

.method public abstract v(Ljava/lang/Integer;Lwn/a;)V
.end method

.method public abstract y()V
.end method

.method public abstract z(Landroidx/fragment/app/Fragment;)Z
.end method

.method public abstract z1(Lwn/c;)V
.end method
