.class public final enum Lcom/transsion/moviedetailapi/bean/PostItemType;
.super Ljava/lang/Enum;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/transsion/moviedetailapi/bean/PostItemType$a;
    }
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Enum<",
        "Lcom/transsion/moviedetailapi/bean/PostItemType;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation


# static fields
.field private static final synthetic $VALUES:[Lcom/transsion/moviedetailapi/bean/PostItemType;

.field public static final enum APPOINTMENT_LIST:Lcom/transsion/moviedetailapi/bean/PostItemType;

.field public static final enum BANNER:Lcom/transsion/moviedetailapi/bean/PostItemType;

.field public static final enum CUSTOM_DATA:Lcom/transsion/moviedetailapi/bean/PostItemType;

.field public static final Companion:Lcom/transsion/moviedetailapi/bean/PostItemType$a;

.field public static final enum EDUCATION_SUBJECT:Lcom/transsion/moviedetailapi/bean/PostItemType;

.field public static final enum FEEDS_TITLE:Lcom/transsion/moviedetailapi/bean/PostItemType;

.field public static final enum FILTER:Lcom/transsion/moviedetailapi/bean/PostItemType;

.field public static final enum GAME_LIST:Lcom/transsion/moviedetailapi/bean/PostItemType;

.field public static final enum GRID_SUBJECT:Lcom/transsion/moviedetailapi/bean/PostItemType;

.field public static final enum HONOR:Lcom/transsion/moviedetailapi/bean/PostItemType;

.field public static final enum HORIZONTAL_BANNER:Lcom/transsion/moviedetailapi/bean/PostItemType;

.field public static final enum MY_COURSE:Lcom/transsion/moviedetailapi/bean/PostItemType;

.field public static final enum NO_NETWORK:Lcom/transsion/moviedetailapi/bean/PostItemType;

.field public static final enum OP_RANKING:Lcom/transsion/moviedetailapi/bean/PostItemType;

.field public static final enum OP_SUBJECTS_MOVIE:Lcom/transsion/moviedetailapi/bean/PostItemType;

.field public static final enum PLAY_LIST:Lcom/transsion/moviedetailapi/bean/PostItemType;

.field public static final enum RANKING_LIST:Lcom/transsion/moviedetailapi/bean/PostItemType;

.field public static final enum RANKING_LIST_MUSIC:Lcom/transsion/moviedetailapi/bean/PostItemType;

.field public static final enum RANKING_LIST_NUMBER:Lcom/transsion/moviedetailapi/bean/PostItemType;

.field public static final enum RANKING_MOVIE_HORIZONTAL:Lcom/transsion/moviedetailapi/bean/PostItemType;

.field public static final enum ROOM:Lcom/transsion/moviedetailapi/bean/PostItemType;

.field public static final enum SINGLE_SUBJECT:Lcom/transsion/moviedetailapi/bean/PostItemType;

.field public static final enum SPORT_LIVE:Lcom/transsion/moviedetailapi/bean/PostItemType;

.field public static final enum SUBJECT:Lcom/transsion/moviedetailapi/bean/PostItemType;

.field public static final enum TRENDING_NATIVE_AD:Lcom/transsion/moviedetailapi/bean/PostItemType;


# instance fields
.field private final value:Ljava/lang/String;


# direct methods
.method private static final synthetic $values()[Lcom/transsion/moviedetailapi/bean/PostItemType;
    .locals 3

    const/16 v0, 0x18

    new-array v0, v0, [Lcom/transsion/moviedetailapi/bean/PostItemType;

    const/4 v1, 0x0

    sget-object v2, Lcom/transsion/moviedetailapi/bean/PostItemType;->SUBJECT:Lcom/transsion/moviedetailapi/bean/PostItemType;

    aput-object v2, v0, v1

    const/4 v1, 0x1

    sget-object v2, Lcom/transsion/moviedetailapi/bean/PostItemType;->OP_RANKING:Lcom/transsion/moviedetailapi/bean/PostItemType;

    aput-object v2, v0, v1

    const/4 v1, 0x2

    sget-object v2, Lcom/transsion/moviedetailapi/bean/PostItemType;->FILTER:Lcom/transsion/moviedetailapi/bean/PostItemType;

    aput-object v2, v0, v1

    const/4 v1, 0x3

    sget-object v2, Lcom/transsion/moviedetailapi/bean/PostItemType;->BANNER:Lcom/transsion/moviedetailapi/bean/PostItemType;

    aput-object v2, v0, v1

    const/4 v1, 0x4

    sget-object v2, Lcom/transsion/moviedetailapi/bean/PostItemType;->CUSTOM_DATA:Lcom/transsion/moviedetailapi/bean/PostItemType;

    aput-object v2, v0, v1

    const/4 v1, 0x5

    sget-object v2, Lcom/transsion/moviedetailapi/bean/PostItemType;->APPOINTMENT_LIST:Lcom/transsion/moviedetailapi/bean/PostItemType;

    aput-object v2, v0, v1

    const/4 v1, 0x6

    sget-object v2, Lcom/transsion/moviedetailapi/bean/PostItemType;->OP_SUBJECTS_MOVIE:Lcom/transsion/moviedetailapi/bean/PostItemType;

    aput-object v2, v0, v1

    const/4 v1, 0x7

    sget-object v2, Lcom/transsion/moviedetailapi/bean/PostItemType;->RANKING_MOVIE_HORIZONTAL:Lcom/transsion/moviedetailapi/bean/PostItemType;

    aput-object v2, v0, v1

    const/16 v1, 0x8

    sget-object v2, Lcom/transsion/moviedetailapi/bean/PostItemType;->RANKING_LIST:Lcom/transsion/moviedetailapi/bean/PostItemType;

    aput-object v2, v0, v1

    const/16 v1, 0x9

    sget-object v2, Lcom/transsion/moviedetailapi/bean/PostItemType;->RANKING_LIST_MUSIC:Lcom/transsion/moviedetailapi/bean/PostItemType;

    aput-object v2, v0, v1

    const/16 v1, 0xa

    sget-object v2, Lcom/transsion/moviedetailapi/bean/PostItemType;->SPORT_LIVE:Lcom/transsion/moviedetailapi/bean/PostItemType;

    aput-object v2, v0, v1

    const/16 v1, 0xb

    sget-object v2, Lcom/transsion/moviedetailapi/bean/PostItemType;->PLAY_LIST:Lcom/transsion/moviedetailapi/bean/PostItemType;

    aput-object v2, v0, v1

    const/16 v1, 0xc

    sget-object v2, Lcom/transsion/moviedetailapi/bean/PostItemType;->RANKING_LIST_NUMBER:Lcom/transsion/moviedetailapi/bean/PostItemType;

    aput-object v2, v0, v1

    const/16 v1, 0xd

    sget-object v2, Lcom/transsion/moviedetailapi/bean/PostItemType;->HORIZONTAL_BANNER:Lcom/transsion/moviedetailapi/bean/PostItemType;

    aput-object v2, v0, v1

    const/16 v1, 0xe

    sget-object v2, Lcom/transsion/moviedetailapi/bean/PostItemType;->SINGLE_SUBJECT:Lcom/transsion/moviedetailapi/bean/PostItemType;

    aput-object v2, v0, v1

    const/16 v1, 0xf

    sget-object v2, Lcom/transsion/moviedetailapi/bean/PostItemType;->EDUCATION_SUBJECT:Lcom/transsion/moviedetailapi/bean/PostItemType;

    aput-object v2, v0, v1

    const/16 v1, 0x10

    sget-object v2, Lcom/transsion/moviedetailapi/bean/PostItemType;->GRID_SUBJECT:Lcom/transsion/moviedetailapi/bean/PostItemType;

    aput-object v2, v0, v1

    const/16 v1, 0x11

    sget-object v2, Lcom/transsion/moviedetailapi/bean/PostItemType;->GAME_LIST:Lcom/transsion/moviedetailapi/bean/PostItemType;

    aput-object v2, v0, v1

    const/16 v1, 0x12

    sget-object v2, Lcom/transsion/moviedetailapi/bean/PostItemType;->NO_NETWORK:Lcom/transsion/moviedetailapi/bean/PostItemType;

    aput-object v2, v0, v1

    const/16 v1, 0x13

    sget-object v2, Lcom/transsion/moviedetailapi/bean/PostItemType;->TRENDING_NATIVE_AD:Lcom/transsion/moviedetailapi/bean/PostItemType;

    aput-object v2, v0, v1

    const/16 v1, 0x14

    sget-object v2, Lcom/transsion/moviedetailapi/bean/PostItemType;->HONOR:Lcom/transsion/moviedetailapi/bean/PostItemType;

    aput-object v2, v0, v1

    const/16 v1, 0x15

    sget-object v2, Lcom/transsion/moviedetailapi/bean/PostItemType;->FEEDS_TITLE:Lcom/transsion/moviedetailapi/bean/PostItemType;

    aput-object v2, v0, v1

    const/16 v1, 0x16

    sget-object v2, Lcom/transsion/moviedetailapi/bean/PostItemType;->MY_COURSE:Lcom/transsion/moviedetailapi/bean/PostItemType;

    aput-object v2, v0, v1

    const/16 v1, 0x17

    sget-object v2, Lcom/transsion/moviedetailapi/bean/PostItemType;->ROOM:Lcom/transsion/moviedetailapi/bean/PostItemType;

    aput-object v2, v0, v1

    return-object v0
.end method

.method static constructor <clinit>()V
    .locals 4

    new-instance v0, Lcom/transsion/moviedetailapi/bean/PostItemType;

    const-string v1, "SUBJECT"

    const/4 v2, 0x0

    invoke-direct {v0, v1, v2, v1}, Lcom/transsion/moviedetailapi/bean/PostItemType;-><init>(Ljava/lang/String;ILjava/lang/String;)V

    sput-object v0, Lcom/transsion/moviedetailapi/bean/PostItemType;->SUBJECT:Lcom/transsion/moviedetailapi/bean/PostItemType;

    new-instance v0, Lcom/transsion/moviedetailapi/bean/PostItemType;

    const/4 v1, 0x1

    const-string v2, "RANKING"

    const-string v3, "OP_RANKING"

    invoke-direct {v0, v3, v1, v2}, Lcom/transsion/moviedetailapi/bean/PostItemType;-><init>(Ljava/lang/String;ILjava/lang/String;)V

    sput-object v0, Lcom/transsion/moviedetailapi/bean/PostItemType;->OP_RANKING:Lcom/transsion/moviedetailapi/bean/PostItemType;

    new-instance v0, Lcom/transsion/moviedetailapi/bean/PostItemType;

    const-string v1, "FILTER"

    const/4 v2, 0x2

    invoke-direct {v0, v1, v2, v1}, Lcom/transsion/moviedetailapi/bean/PostItemType;-><init>(Ljava/lang/String;ILjava/lang/String;)V

    sput-object v0, Lcom/transsion/moviedetailapi/bean/PostItemType;->FILTER:Lcom/transsion/moviedetailapi/bean/PostItemType;

    new-instance v0, Lcom/transsion/moviedetailapi/bean/PostItemType;

    const-string v1, "BANNER"

    const/4 v2, 0x3

    invoke-direct {v0, v1, v2, v1}, Lcom/transsion/moviedetailapi/bean/PostItemType;-><init>(Ljava/lang/String;ILjava/lang/String;)V

    sput-object v0, Lcom/transsion/moviedetailapi/bean/PostItemType;->BANNER:Lcom/transsion/moviedetailapi/bean/PostItemType;

    new-instance v0, Lcom/transsion/moviedetailapi/bean/PostItemType;

    const/4 v1, 0x4

    const-string v2, "CUSTOM"

    const-string v3, "CUSTOM_DATA"

    invoke-direct {v0, v3, v1, v2}, Lcom/transsion/moviedetailapi/bean/PostItemType;-><init>(Ljava/lang/String;ILjava/lang/String;)V

    sput-object v0, Lcom/transsion/moviedetailapi/bean/PostItemType;->CUSTOM_DATA:Lcom/transsion/moviedetailapi/bean/PostItemType;

    new-instance v0, Lcom/transsion/moviedetailapi/bean/PostItemType;

    const-string v1, "APPOINTMENT_LIST"

    const/4 v2, 0x5

    invoke-direct {v0, v1, v2, v1}, Lcom/transsion/moviedetailapi/bean/PostItemType;-><init>(Ljava/lang/String;ILjava/lang/String;)V

    sput-object v0, Lcom/transsion/moviedetailapi/bean/PostItemType;->APPOINTMENT_LIST:Lcom/transsion/moviedetailapi/bean/PostItemType;

    new-instance v0, Lcom/transsion/moviedetailapi/bean/PostItemType;

    const/4 v1, 0x6

    const-string v2, "SUBJECTS_MOVIE"

    const-string v3, "OP_SUBJECTS_MOVIE"

    invoke-direct {v0, v3, v1, v2}, Lcom/transsion/moviedetailapi/bean/PostItemType;-><init>(Ljava/lang/String;ILjava/lang/String;)V

    sput-object v0, Lcom/transsion/moviedetailapi/bean/PostItemType;->OP_SUBJECTS_MOVIE:Lcom/transsion/moviedetailapi/bean/PostItemType;

    new-instance v0, Lcom/transsion/moviedetailapi/bean/PostItemType;

    const/4 v1, 0x7

    const-string v2, "SUBJ_MOVIE_HORIZ"

    const-string v3, "RANKING_MOVIE_HORIZONTAL"

    invoke-direct {v0, v3, v1, v2}, Lcom/transsion/moviedetailapi/bean/PostItemType;-><init>(Ljava/lang/String;ILjava/lang/String;)V

    sput-object v0, Lcom/transsion/moviedetailapi/bean/PostItemType;->RANKING_MOVIE_HORIZONTAL:Lcom/transsion/moviedetailapi/bean/PostItemType;

    new-instance v0, Lcom/transsion/moviedetailapi/bean/PostItemType;

    const-string v1, "RANKING_LIST"

    const/16 v2, 0x8

    invoke-direct {v0, v1, v2, v1}, Lcom/transsion/moviedetailapi/bean/PostItemType;-><init>(Ljava/lang/String;ILjava/lang/String;)V

    sput-object v0, Lcom/transsion/moviedetailapi/bean/PostItemType;->RANKING_LIST:Lcom/transsion/moviedetailapi/bean/PostItemType;

    new-instance v0, Lcom/transsion/moviedetailapi/bean/PostItemType;

    const/16 v1, 0x9

    const-string v2, "MUSIC_CHARTS"

    const-string v3, "RANKING_LIST_MUSIC"

    invoke-direct {v0, v3, v1, v2}, Lcom/transsion/moviedetailapi/bean/PostItemType;-><init>(Ljava/lang/String;ILjava/lang/String;)V

    sput-object v0, Lcom/transsion/moviedetailapi/bean/PostItemType;->RANKING_LIST_MUSIC:Lcom/transsion/moviedetailapi/bean/PostItemType;

    new-instance v0, Lcom/transsion/moviedetailapi/bean/PostItemType;

    const-string v1, "SPORT_LIVE"

    const/16 v2, 0xa

    invoke-direct {v0, v1, v2, v1}, Lcom/transsion/moviedetailapi/bean/PostItemType;-><init>(Ljava/lang/String;ILjava/lang/String;)V

    sput-object v0, Lcom/transsion/moviedetailapi/bean/PostItemType;->SPORT_LIVE:Lcom/transsion/moviedetailapi/bean/PostItemType;

    new-instance v0, Lcom/transsion/moviedetailapi/bean/PostItemType;

    const-string v1, "PLAY_LIST"

    const/16 v2, 0xb

    invoke-direct {v0, v1, v2, v1}, Lcom/transsion/moviedetailapi/bean/PostItemType;-><init>(Ljava/lang/String;ILjava/lang/String;)V

    sput-object v0, Lcom/transsion/moviedetailapi/bean/PostItemType;->PLAY_LIST:Lcom/transsion/moviedetailapi/bean/PostItemType;

    new-instance v0, Lcom/transsion/moviedetailapi/bean/PostItemType;

    const/16 v1, 0xc

    const-string v2, "RANKING_HORIZ"

    const-string v3, "RANKING_LIST_NUMBER"

    invoke-direct {v0, v3, v1, v2}, Lcom/transsion/moviedetailapi/bean/PostItemType;-><init>(Ljava/lang/String;ILjava/lang/String;)V

    sput-object v0, Lcom/transsion/moviedetailapi/bean/PostItemType;->RANKING_LIST_NUMBER:Lcom/transsion/moviedetailapi/bean/PostItemType;

    new-instance v0, Lcom/transsion/moviedetailapi/bean/PostItemType;

    const-string v1, "HORIZONTAL_BANNER"

    const/16 v2, 0xd

    invoke-direct {v0, v1, v2, v1}, Lcom/transsion/moviedetailapi/bean/PostItemType;-><init>(Ljava/lang/String;ILjava/lang/String;)V

    sput-object v0, Lcom/transsion/moviedetailapi/bean/PostItemType;->HORIZONTAL_BANNER:Lcom/transsion/moviedetailapi/bean/PostItemType;

    new-instance v0, Lcom/transsion/moviedetailapi/bean/PostItemType;

    const-string v1, "SINGLE_SUBJECT"

    const/16 v2, 0xe

    invoke-direct {v0, v1, v2, v1}, Lcom/transsion/moviedetailapi/bean/PostItemType;-><init>(Ljava/lang/String;ILjava/lang/String;)V

    sput-object v0, Lcom/transsion/moviedetailapi/bean/PostItemType;->SINGLE_SUBJECT:Lcom/transsion/moviedetailapi/bean/PostItemType;

    new-instance v0, Lcom/transsion/moviedetailapi/bean/PostItemType;

    const-string v1, "EDUCATION_SUBJECT"

    const/16 v2, 0xf

    invoke-direct {v0, v1, v2, v1}, Lcom/transsion/moviedetailapi/bean/PostItemType;-><init>(Ljava/lang/String;ILjava/lang/String;)V

    sput-object v0, Lcom/transsion/moviedetailapi/bean/PostItemType;->EDUCATION_SUBJECT:Lcom/transsion/moviedetailapi/bean/PostItemType;

    new-instance v0, Lcom/transsion/moviedetailapi/bean/PostItemType;

    const-string v1, "GRID_SUBJECT"

    const/16 v2, 0x10

    invoke-direct {v0, v1, v2, v1}, Lcom/transsion/moviedetailapi/bean/PostItemType;-><init>(Ljava/lang/String;ILjava/lang/String;)V

    sput-object v0, Lcom/transsion/moviedetailapi/bean/PostItemType;->GRID_SUBJECT:Lcom/transsion/moviedetailapi/bean/PostItemType;

    new-instance v0, Lcom/transsion/moviedetailapi/bean/PostItemType;

    const-string v1, "GAME_LIST"

    const/16 v2, 0x11

    invoke-direct {v0, v1, v2, v1}, Lcom/transsion/moviedetailapi/bean/PostItemType;-><init>(Ljava/lang/String;ILjava/lang/String;)V

    sput-object v0, Lcom/transsion/moviedetailapi/bean/PostItemType;->GAME_LIST:Lcom/transsion/moviedetailapi/bean/PostItemType;

    new-instance v0, Lcom/transsion/moviedetailapi/bean/PostItemType;

    const/16 v1, 0x12

    const-string v2, "no_network"

    const-string v3, "NO_NETWORK"

    invoke-direct {v0, v3, v1, v2}, Lcom/transsion/moviedetailapi/bean/PostItemType;-><init>(Ljava/lang/String;ILjava/lang/String;)V

    sput-object v0, Lcom/transsion/moviedetailapi/bean/PostItemType;->NO_NETWORK:Lcom/transsion/moviedetailapi/bean/PostItemType;

    new-instance v0, Lcom/transsion/moviedetailapi/bean/PostItemType;

    const-string v1, "TRENDING_NATIVE_AD"

    const/16 v2, 0x13

    invoke-direct {v0, v1, v2, v1}, Lcom/transsion/moviedetailapi/bean/PostItemType;-><init>(Ljava/lang/String;ILjava/lang/String;)V

    sput-object v0, Lcom/transsion/moviedetailapi/bean/PostItemType;->TRENDING_NATIVE_AD:Lcom/transsion/moviedetailapi/bean/PostItemType;

    new-instance v0, Lcom/transsion/moviedetailapi/bean/PostItemType;

    const-string v1, "HONOR"

    const/16 v2, 0x14

    invoke-direct {v0, v1, v2, v1}, Lcom/transsion/moviedetailapi/bean/PostItemType;-><init>(Ljava/lang/String;ILjava/lang/String;)V

    sput-object v0, Lcom/transsion/moviedetailapi/bean/PostItemType;->HONOR:Lcom/transsion/moviedetailapi/bean/PostItemType;

    new-instance v0, Lcom/transsion/moviedetailapi/bean/PostItemType;

    const-string v1, "FEEDS_TITLE"

    const/16 v2, 0x15

    invoke-direct {v0, v1, v2, v1}, Lcom/transsion/moviedetailapi/bean/PostItemType;-><init>(Ljava/lang/String;ILjava/lang/String;)V

    sput-object v0, Lcom/transsion/moviedetailapi/bean/PostItemType;->FEEDS_TITLE:Lcom/transsion/moviedetailapi/bean/PostItemType;

    new-instance v0, Lcom/transsion/moviedetailapi/bean/PostItemType;

    const-string v1, "MY_COURSE"

    const/16 v2, 0x16

    invoke-direct {v0, v1, v2, v1}, Lcom/transsion/moviedetailapi/bean/PostItemType;-><init>(Ljava/lang/String;ILjava/lang/String;)V

    sput-object v0, Lcom/transsion/moviedetailapi/bean/PostItemType;->MY_COURSE:Lcom/transsion/moviedetailapi/bean/PostItemType;

    new-instance v0, Lcom/transsion/moviedetailapi/bean/PostItemType;

    const-string v1, "ROOM"

    const/16 v2, 0x17

    invoke-direct {v0, v1, v2, v1}, Lcom/transsion/moviedetailapi/bean/PostItemType;-><init>(Ljava/lang/String;ILjava/lang/String;)V

    sput-object v0, Lcom/transsion/moviedetailapi/bean/PostItemType;->ROOM:Lcom/transsion/moviedetailapi/bean/PostItemType;

    invoke-static {}, Lcom/transsion/moviedetailapi/bean/PostItemType;->$values()[Lcom/transsion/moviedetailapi/bean/PostItemType;

    move-result-object v0

    sput-object v0, Lcom/transsion/moviedetailapi/bean/PostItemType;->$VALUES:[Lcom/transsion/moviedetailapi/bean/PostItemType;

    new-instance v0, Lcom/transsion/moviedetailapi/bean/PostItemType$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lcom/transsion/moviedetailapi/bean/PostItemType$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lcom/transsion/moviedetailapi/bean/PostItemType;->Companion:Lcom/transsion/moviedetailapi/bean/PostItemType$a;

    return-void
.end method

.method private constructor <init>(Ljava/lang/String;ILjava/lang/String;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            ")V"
        }
    .end annotation

    invoke-direct {p0, p1, p2}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    iput-object p3, p0, Lcom/transsion/moviedetailapi/bean/PostItemType;->value:Ljava/lang/String;

    return-void
.end method

.method public static valueOf(Ljava/lang/String;)Lcom/transsion/moviedetailapi/bean/PostItemType;
    .locals 1

    const-class v0, Lcom/transsion/moviedetailapi/bean/PostItemType;

    invoke-static {v0, p0}, Ljava/lang/Enum;->valueOf(Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Enum;

    move-result-object p0

    check-cast p0, Lcom/transsion/moviedetailapi/bean/PostItemType;

    return-object p0
.end method

.method public static values()[Lcom/transsion/moviedetailapi/bean/PostItemType;
    .locals 1

    sget-object v0, Lcom/transsion/moviedetailapi/bean/PostItemType;->$VALUES:[Lcom/transsion/moviedetailapi/bean/PostItemType;

    invoke-virtual {v0}, [Ljava/lang/Object;->clone()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [Lcom/transsion/moviedetailapi/bean/PostItemType;

    return-object v0
.end method


# virtual methods
.method public final getValue()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/transsion/moviedetailapi/bean/PostItemType;->value:Ljava/lang/String;

    return-object v0
.end method
