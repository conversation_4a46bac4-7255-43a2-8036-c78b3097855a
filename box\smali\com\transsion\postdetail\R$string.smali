.class public final Lcom/transsion/postdetail/R$string;
.super Ljava/lang/Object;


# static fields
.field public static Nearby_Communities:I = 0x7f120005

.field public static analysing_from:I = 0x7f120044

.field public static base_net_err:I = 0x7f120061

.field public static btn_ok:I = 0x7f120081

.field public static check_error:I = 0x7f120091

.field public static choose_subtitle:I = 0x7f120097

.field public static close:I = 0x7f1200a0

.field public static comment_copy_success:I = 0x7f1200b2

.field public static comment_copy_tips:I = 0x7f1200b3

.field public static comment_delete_cancel:I = 0x7f1200b4

.field public static comment_delete_failed:I = 0x7f1200b5

.field public static comment_delete_success:I = 0x7f1200b6

.field public static comment_delete_tips:I = 0x7f1200b7

.field public static comment_empty:I = 0x7f1200b9

.field public static comment_empty_tips:I = 0x7f1200ba

.field public static comment_failed:I = 0x7f1200bb

.field public static comment_hint_add:I = 0x7f1200bc

.field public static comment_just_now:I = 0x7f1200be

.field public static comment_login_title:I = 0x7f1200bf

.field public static comment_max_tips:I = 0x7f1200c0

.field public static comment_post_time:I = 0x7f1200c1

.field public static comment_reply:I = 0x7f1200c2

.field public static comment_reply_to:I = 0x7f1200c3

.field public static comment_report_tips:I = 0x7f1200c4

.field public static comment_retry:I = 0x7f1200c5

.field public static comment_sub_more_tip:I = 0x7f1200c6

.field public static comment_success:I = 0x7f1200c7

.field public static comments:I = 0x7f1200c8

.field public static delete_post_failed:I = 0x7f120125

.field public static discover:I = 0x7f120129

.field public static download_playing_downloading_tips:I = 0x7f120170

.field public static download_playing_pause:I = 0x7f120171

.field public static download_playing_pause_tips:I = 0x7f120172

.field public static download_playing_resume:I = 0x7f120173

.field public static downloaded:I = 0x7f1201ca

.field public static downloading_play_load_failed:I = 0x7f1201cd

.field public static downloading_play_timeout:I = 0x7f1201ce

.field public static downloading_play_timeout_reload:I = 0x7f1201cf

.field public static downloading_play_wait_msg:I = 0x7f1201d0

.field public static favorite:I = 0x7f12022d

.field public static font_color:I = 0x7f120270

.field public static font_size:I = 0x7f120271

.field public static for_you:I = 0x7f120272

.field public static language_title:I = 0x7f1202c4

.field public static last_played_time:I = 0x7f1202c6

.field public static loding:I = 0x7f1202d8

.field public static more_episodes:I = 0x7f1203e2

.field public static no_comment_yet:I = 0x7f120463

.field public static no_result_found:I = 0x7f120471

.field public static none:I = 0x7f120476

.field public static open_from:I = 0x7f120498

.field public static options:I = 0x7f12049a

.field public static play_loading:I = 0x7f1204da

.field public static player_delete_video_cancel:I = 0x7f1204e2

.field public static player_no_network_tip2:I = 0x7f1204e6

.field public static post:I = 0x7f1204f1

.field public static post_at:I = 0x7f1204f2

.field public static post_cancel:I = 0x7f1204f3

.field public static post_confirm:I = 0x7f1204f4

.field public static post_confirm_title:I = 0x7f1204f5

.field public static post_count_down_get_ad_free:I = 0x7f1204f6

.field public static post_delete_tips:I = 0x7f1204f7

.field public static post_failed_load:I = 0x7f1204f9

.field public static post_group_count:I = 0x7f1204fa

.field public static post_loading:I = 0x7f1204fc

.field public static post_progress_tx_style:I = 0x7f1204fe

.field public static post_retry:I = 0x7f1204ff

.field public static post_video_guide:I = 0x7f120503

.field public static rating:I = 0x7f120561

.field public static replay:I = 0x7f120565

.field public static reset:I = 0x7f120575

.field public static resource_detector:I = 0x7f120577

.field public static rotate:I = 0x7f12057f

.field public static save_video:I = 0x7f120588

.field public static score:I = 0x7f120589

.field public static search_result:I = 0x7f12059d

.field public static series_next_play_tips:I = 0x7f1205b8

.field public static short_tv_all:I = 0x7f1205c7

.field public static short_tv_category_all:I = 0x7f1205cc

.field public static short_tv_category_hottest:I = 0x7f1205cd

.field public static short_tv_category_latest:I = 0x7f1205ce

.field public static short_tv_category_view_all:I = 0x7f1205cf

.field public static short_tv_favorite:I = 0x7f1205d0

.field public static short_tv_favorited:I = 0x7f1205d3

.field public static short_tv_guide_tips:I = 0x7f1205d4

.field public static short_tv_history_title:I = 0x7f1205d5

.field public static short_tv_list:I = 0x7f1205d6

.field public static short_tv_most_trending:I = 0x7f1205d7

.field public static short_tv_my_list:I = 0x7f1205d8

.field public static short_tv_new_release:I = 0x7f1205d9

.field public static short_tv_play_all:I = 0x7f1205da

.field public static short_tv_trailer:I = 0x7f1205db

.field public static short_tv_unlock:I = 0x7f1205dc

.field public static short_tv_unlock_in_order:I = 0x7f1205dd

.field public static short_tv_unlock_success:I = 0x7f1205de

.field public static short_tv_watch_ad:I = 0x7f1205df

.field public static short_tv_watch_ad_tips:I = 0x7f1205e0

.field public static short_tv_watching_online:I = 0x7f1205e1

.field public static subtitle:I = 0x7f120641

.field public static subtitle_result:I = 0x7f120659

.field public static sync_adjust:I = 0x7f120669

.field public static tip:I = 0x7f1206a4

.field public static tip_post:I = 0x7f1206a8

.field public static tips_new_capital:I = 0x7f1206ae

.field public static title_background:I = 0x7f1206b0

.field public static title_background_opacity:I = 0x7f1206b1

.field public static title_for_you:I = 0x7f1206b2

.field public static title_help:I = 0x7f1206b3

.field public static title_position:I = 0x7f1206b4

.field public static title_shadow:I = 0x7f1206b5

.field public static turn_off:I = 0x7f120788

.field public static turn_off_short:I = 0x7f120789

.field public static turn_on:I = 0x7f12078a

.field public static turn_on_network:I = 0x7f12078b

.field public static turn_on_short:I = 0x7f12078c

.field public static tv_adjustment_tips:I = 0x7f12078d

.field public static video_crop:I = 0x7f1207ba

.field public static video_double_tap:I = 0x7f1207bc

.field public static video_double_tap_forward:I = 0x7f1207bd

.field public static video_double_tap_pause:I = 0x7f1207be

.field public static video_double_tap_rewind:I = 0x7f1207bf

.field public static video_double_tap_time:I = 0x7f1207c0

.field public static video_episode:I = 0x7f1207c1

.field public static video_fit_screen:I = 0x7f1207c2

.field public static video_has_delete_tip:I = 0x7f1207cb

.field public static video_play_use_mobile_data:I = 0x7f1207d0

.field public static video_player:I = 0x7f1207d1

.field public static video_stretch:I = 0x7f1207d3


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
