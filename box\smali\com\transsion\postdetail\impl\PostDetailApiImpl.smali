.class public final Lcom/transsion/postdetail/impl/PostDetailApiImpl;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/transsion/postdetailapi/IPostDetailApi;


# annotations
.annotation build Lcom/alibaba/android/arouter/facade/annotation/Route;
    path = "/post/detailapi"
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public D(Ljava/lang/String;)Landroidx/fragment/app/Fragment;
    .locals 1

    sget-object v0, Lcom/transsion/postdetail/ui/fragment/RoomPostNewestFragment;->C:Lcom/transsion/postdetail/ui/fragment/RoomPostNewestFragment$a;

    invoke-virtual {v0, p1}, Lcom/transsion/postdetail/ui/fragment/RoomPostNewestFragment$a;->a(Ljava/lang/String;)Lcom/transsion/postdetail/ui/fragment/RoomPostNewestFragment;

    move-result-object p1

    return-object p1
.end method

.method public K(Landroidx/recyclerview/widget/RecyclerView$s;)Lcom/chad/library/adapter/base/provider/BaseItemProvider;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroidx/recyclerview/widget/RecyclerView$s;",
            ")",
            "Lcom/chad/library/adapter/base/provider/BaseItemProvider<",
            "Lcom/transsion/moviedetailapi/bean/PostSubjectItem;",
            ">;"
        }
    .end annotation

    const-string v0, "pool"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    new-instance v0, Lcom/transsion/postdetail/ui/adapter/provider/i;

    invoke-direct {v0, p1}, Lcom/transsion/postdetail/ui/adapter/provider/i;-><init>(Landroidx/recyclerview/widget/RecyclerView$s;)V

    return-object v0
.end method

.method public T(Landroid/content/Context;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;IZLcom/transsion/moviedetailapi/bean/PostSubjectItem;Ljava/lang/String;Z)Landroid/content/Intent;
    .locals 0

    const-string p8, "context"

    invoke-static {p1, p8}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    const-string p8, "postId"

    invoke-static {p2, p8}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    const-string p8, "itemType"

    invoke-static {p3, p8}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    new-instance p8, Landroid/content/Intent;

    sget-object p9, Lcom/transsion/moviedetailapi/bean/MediaType;->VIDEO:Lcom/transsion/moviedetailapi/bean/MediaType;

    invoke-virtual {p9}, Lcom/transsion/moviedetailapi/bean/MediaType;->getValue()Ljava/lang/String;

    move-result-object p9

    invoke-static {p4, p9}, Lkotlin/jvm/internal/Intrinsics;->b(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result p9

    if-eqz p9, :cond_0

    const-class p9, Lcom/transsion/postdetail/ui/activity/PostDetailVideoActivity;

    goto :goto_0

    :cond_0
    const-class p9, Lcom/transsion/postdetail/ui/activity/PostDetailActivity;

    :goto_0
    invoke-direct {p8, p1, p9}, Landroid/content/Intent;-><init>(Landroid/content/Context;Ljava/lang/Class;)V

    const-string p1, "id"

    invoke-virtual {p8, p1, p2}, Landroid/content/Intent;->putExtra(Ljava/lang/String;Ljava/lang/String;)Landroid/content/Intent;

    const-string p1, "item_type"

    invoke-virtual {p8, p1, p3}, Landroid/content/Intent;->putExtra(Ljava/lang/String;Ljava/lang/String;)Landroid/content/Intent;

    const-string p1, "media_type"

    invoke-virtual {p8, p1, p4}, Landroid/content/Intent;->putExtra(Ljava/lang/String;Ljava/lang/String;)Landroid/content/Intent;

    const-string p1, "rec_ops"

    invoke-virtual {p8, p1, p5}, Landroid/content/Intent;->putExtra(Ljava/lang/String;Ljava/lang/String;)Landroid/content/Intent;

    const-string p1, "tab_id"

    invoke-virtual {p8, p1, p6}, Landroid/content/Intent;->putExtra(Ljava/lang/String;I)Landroid/content/Intent;

    const-string p1, "video_load_more"

    invoke-virtual {p8, p1, p7}, Landroid/content/Intent;->putExtra(Ljava/lang/String;Z)Landroid/content/Intent;

    const-string p1, "is_build_in"

    invoke-virtual {p8, p1, p10}, Landroid/content/Intent;->putExtra(Ljava/lang/String;Z)Landroid/content/Intent;

    return-object p8
.end method

.method public Y0(Ljava/lang/String;)Landroidx/fragment/app/Fragment;
    .locals 1

    sget-object v0, Lcom/transsion/postdetail/ui/fragment/RoomPostPopularFragment;->C:Lcom/transsion/postdetail/ui/fragment/RoomPostPopularFragment$a;

    invoke-virtual {v0, p1}, Lcom/transsion/postdetail/ui/fragment/RoomPostPopularFragment$a;->a(Ljava/lang/String;)Lcom/transsion/postdetail/ui/fragment/RoomPostPopularFragment;

    move-result-object p1

    return-object p1
.end method

.method public b1()Landroidx/fragment/app/Fragment;
    .locals 1

    sget-object v0, Lcom/transsion/postdetail/ui/fragment/RoomPostExploreFragment;->C:Lcom/transsion/postdetail/ui/fragment/RoomPostExploreFragment$a;

    invoke-virtual {v0}, Lcom/transsion/postdetail/ui/fragment/RoomPostExploreFragment$a;->a()Lcom/transsion/postdetail/ui/fragment/RoomPostExploreFragment;

    move-result-object v0

    return-object v0
.end method

.method public g0(Lcom/transsion/moviedetailapi/bean/PostSubjectItem;)V
    .locals 1

    const-string v0, "data"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    sget-object v0, Lcom/transsion/postdetail/helper/ImmVideoHelper;->g:Lcom/transsion/postdetail/helper/ImmVideoHelper$a;

    invoke-virtual {v0}, Lcom/transsion/postdetail/helper/ImmVideoHelper$a;->a()Lcom/transsion/postdetail/helper/ImmVideoHelper;

    move-result-object v0

    invoke-virtual {v0, p1}, Lcom/transsion/postdetail/helper/ImmVideoHelper;->o(Lcom/transsion/moviedetailapi/bean/PostSubjectItem;)V

    return-void
.end method

.method public init(Landroid/content/Context;)V
    .locals 0

    return-void
.end method

.method public o1(Ljava/lang/String;)V
    .locals 8

    const-string v0, "tag"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    sget-object v0, Lcom/transsion/videofloat/a;->a:Lcom/transsion/videofloat/a;

    invoke-virtual {v0, p1}, Lcom/transsion/videofloat/a;->b(Ljava/lang/String;)Z

    move-result v1

    if-nez v1, :cond_0

    return-void

    :cond_0
    sget-object v2, Lxi/b;->a:Lxi/b$a;

    const-string v3, "VideoFloat"

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v4, "\u8d44\u6e90\u88ab\u5220\u9664\uff0c\u79fb\u9664pip,tag:"

    invoke-virtual {v1, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v4

    const/4 v5, 0x0

    const/4 v6, 0x4

    const/4 v7, 0x0

    invoke-static/range {v2 .. v7}, Lxi/b$a;->f(Lxi/b$a;Ljava/lang/String;Ljava/lang/String;ZILjava/lang/Object;)V

    invoke-virtual {v0}, Lcom/transsion/videofloat/a;->a()V

    return-void
.end method

.method public q1()Landroidx/fragment/app/Fragment;
    .locals 1

    sget-object v0, Lcom/transsion/postdetail/ui/fragment/RoomPostNearbyFragment;->M:Lcom/transsion/postdetail/ui/fragment/RoomPostNearbyFragment$a;

    invoke-virtual {v0}, Lcom/transsion/postdetail/ui/fragment/RoomPostNearbyFragment$a;->a()Lcom/transsion/postdetail/ui/fragment/RoomPostNearbyFragment;

    move-result-object v0

    return-object v0
.end method

.method public t(Landroidx/recyclerview/widget/RecyclerView$s;)Lcom/chad/library/adapter/base/provider/BaseItemProvider;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroidx/recyclerview/widget/RecyclerView$s;",
            ")",
            "Lcom/chad/library/adapter/base/provider/BaseItemProvider<",
            "Lcom/transsion/moviedetailapi/bean/PostSubjectItem;",
            ">;"
        }
    .end annotation

    const-string v0, "pool"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    new-instance v0, Lcom/transsion/postdetail/ui/adapter/provider/o;

    invoke-direct {v0, p1}, Lcom/transsion/postdetail/ui/adapter/provider/o;-><init>(Landroidx/recyclerview/widget/RecyclerView$s;)V

    return-object v0
.end method
