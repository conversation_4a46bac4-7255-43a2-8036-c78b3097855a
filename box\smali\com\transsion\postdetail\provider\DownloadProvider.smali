.class public final Lcom/transsion/postdetail/provider/DownloadProvider;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/transsnet/downloader/provider/IDownloadProvider;


# annotations
.annotation build Lcom/alibaba/android/arouter/facade/annotation/Route;
    path = "/video/download"
.end annotation

.annotation runtime L<PERSON>lin/Metadata;
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public init(Landroid/content/Context;)V
    .locals 0

    return-void
.end method
