.class public interface abstract Lcom/transsion/postdetailapi/IPostDetailApi;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/alibaba/android/arouter/facade/template/IProvider;


# annotations
.annotation runtime L<PERSON>lin/Metadata;
.end annotation


# virtual methods
.method public abstract D(Ljava/lang/String;)Landroidx/fragment/app/Fragment;
.end method

.method public abstract K(Landroidx/recyclerview/widget/RecyclerView$s;)Lcom/chad/library/adapter/base/provider/BaseItemProvider;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroidx/recyclerview/widget/RecyclerView$s;",
            ")",
            "Lcom/chad/library/adapter/base/provider/BaseItemProvider<",
            "Lcom/transsion/moviedetailapi/bean/PostSubjectItem;",
            ">;"
        }
    .end annotation
.end method

.method public abstract T(Landroid/content/Context;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;IZLcom/transsion/moviedetailapi/bean/PostSubjectItem;Ljava/lang/String;Z)Landroid/content/Intent;
.end method

.method public abstract Y0(Ljava/lang/String;)Landroidx/fragment/app/Fragment;
.end method

.method public abstract b1()Landroidx/fragment/app/Fragment;
.end method

.method public abstract g0(Lcom/transsion/moviedetailapi/bean/PostSubjectItem;)V
.end method

.method public abstract o1(Ljava/lang/String;)V
.end method

.method public abstract q1()Landroidx/fragment/app/Fragment;
.end method

.method public abstract t(Landroidx/recyclerview/widget/RecyclerView$s;)Lcom/chad/library/adapter/base/provider/BaseItemProvider;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroidx/recyclerview/widget/RecyclerView$s;",
            ")",
            "Lcom/chad/library/adapter/base/provider/BaseItemProvider<",
            "Lcom/transsion/moviedetailapi/bean/PostSubjectItem;",
            ">;"
        }
    .end annotation
.end method
