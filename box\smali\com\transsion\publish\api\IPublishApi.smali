.class public interface abstract Lcom/transsion/publish/api/IPublishApi;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/alibaba/android/arouter/facade/template/IProvider;


# annotations
.annotation runtime <PERSON><PERSON><PERSON>/Metadata;
.end annotation


# virtual methods
.method public abstract E0(Landroid/content/Context;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;I)V
.end method

.method public abstract J(Landroid/content/Context;)V
.end method

.method public abstract R()V
.end method

.method public abstract c0()Z
.end method

.method public abstract m0(Landroid/content/Context;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;I)V
.end method

.method public abstract p0()Z
.end method

.method public abstract w(Landroid/content/Context;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V
.end method

.method public abstract x1(Landroid/content/Context;)Landroid/content/Intent;
.end method
