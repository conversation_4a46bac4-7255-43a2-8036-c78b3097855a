.class public final enum Lcom/transsion/push/bean/ShowOrder;
.super Ljava/lang/Enum;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Enum<",
        "Lcom/transsion/push/bean/ShowOrder;",
        ">;"
    }
.end annotation

.annotation runtime L<PERSON>lin/Metadata;
.end annotation


# static fields
.field private static final synthetic $VALUES:[Lcom/transsion/push/bean/ShowOrder;

.field public static final enum CURRENT:Lcom/transsion/push/bean/ShowOrder;

.field public static final enum LAST:Lcom/transsion/push/bean/ShowOrder;

.field public static final enum NEXT:Lcom/transsion/push/bean/ShowOrder;


# direct methods
.method private static final synthetic $values()[Lcom/transsion/push/bean/ShowOrder;
    .locals 3

    const/4 v0, 0x3

    new-array v0, v0, [Lcom/transsion/push/bean/ShowOrder;

    const/4 v1, 0x0

    sget-object v2, Lcom/transsion/push/bean/ShowOrder;->CURRENT:Lcom/transsion/push/bean/ShowOrder;

    aput-object v2, v0, v1

    const/4 v1, 0x1

    sget-object v2, Lcom/transsion/push/bean/ShowOrder;->LAST:Lcom/transsion/push/bean/ShowOrder;

    aput-object v2, v0, v1

    const/4 v1, 0x2

    sget-object v2, Lcom/transsion/push/bean/ShowOrder;->NEXT:Lcom/transsion/push/bean/ShowOrder;

    aput-object v2, v0, v1

    return-object v0
.end method

.method static constructor <clinit>()V
    .locals 3

    new-instance v0, Lcom/transsion/push/bean/ShowOrder;

    const-string v1, "CURRENT"

    const/4 v2, 0x0

    invoke-direct {v0, v1, v2}, Lcom/transsion/push/bean/ShowOrder;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lcom/transsion/push/bean/ShowOrder;->CURRENT:Lcom/transsion/push/bean/ShowOrder;

    new-instance v0, Lcom/transsion/push/bean/ShowOrder;

    const-string v1, "LAST"

    const/4 v2, 0x1

    invoke-direct {v0, v1, v2}, Lcom/transsion/push/bean/ShowOrder;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lcom/transsion/push/bean/ShowOrder;->LAST:Lcom/transsion/push/bean/ShowOrder;

    new-instance v0, Lcom/transsion/push/bean/ShowOrder;

    const-string v1, "NEXT"

    const/4 v2, 0x2

    invoke-direct {v0, v1, v2}, Lcom/transsion/push/bean/ShowOrder;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lcom/transsion/push/bean/ShowOrder;->NEXT:Lcom/transsion/push/bean/ShowOrder;

    invoke-static {}, Lcom/transsion/push/bean/ShowOrder;->$values()[Lcom/transsion/push/bean/ShowOrder;

    move-result-object v0

    sput-object v0, Lcom/transsion/push/bean/ShowOrder;->$VALUES:[Lcom/transsion/push/bean/ShowOrder;

    return-void
.end method

.method private constructor <init>(Ljava/lang/String;I)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation

    invoke-direct {p0, p1, p2}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    return-void
.end method

.method public static valueOf(Ljava/lang/String;)Lcom/transsion/push/bean/ShowOrder;
    .locals 1

    const-class v0, Lcom/transsion/push/bean/ShowOrder;

    invoke-static {v0, p0}, Ljava/lang/Enum;->valueOf(Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Enum;

    move-result-object p0

    check-cast p0, Lcom/transsion/push/bean/ShowOrder;

    return-object p0
.end method

.method public static values()[Lcom/transsion/push/bean/ShowOrder;
    .locals 1

    sget-object v0, Lcom/transsion/push/bean/ShowOrder;->$VALUES:[Lcom/transsion/push/bean/ShowOrder;

    invoke-virtual {v0}, [Ljava/lang/Object;->clone()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [Lcom/transsion/push/bean/ShowOrder;

    return-object v0
.end method
