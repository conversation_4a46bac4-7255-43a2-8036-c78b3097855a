.class public interface abstract Lcom/transsion/push/service/JobIntentService$b;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/transsion/push/service/JobIntentService;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "b"
.end annotation


# virtual methods
.method public abstract a()Lcom/transsion/push/service/JobIntentService$e;
.end method

.method public abstract b()Landroid/os/IBinder;
.end method
