.class public final Lcom/transsion/push/utils/PushApiImpl;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/transsion/pushapi/IPushServiceApi;


# annotations
.annotation build Lcom/alibaba/android/arouter/facade/annotation/Route;
    path = "/push/service"
.end annotation

.annotation runtime L<PERSON><PERSON>/Metadata;
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public init(Landroid/content/Context;)V
    .locals 0

    return-void
.end method
