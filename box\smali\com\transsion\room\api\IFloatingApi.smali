.class public interface abstract Lcom/transsion/room/api/IFloatingApi;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/alibaba/android/arouter/facade/template/IProvider;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/transsion/room/api/IFloatingApi$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation


# virtual methods
.method public abstract N0()V
.end method

.method public abstract X0(Ljava/lang/ref/WeakReference;)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/ref/WeakReference<",
            "Landroid/app/Activity;",
            ">;)V"
        }
    .end annotation
.end method

.method public abstract Z()V
.end method

.method public abstract b()V
.end method

.method public abstract f0()V
.end method

.method public abstract i0(Lcom/transsion/room/api/IFloatingApi$a;)V
.end method

.method public abstract l0()V
.end method

.method public abstract p1(Lcom/transsion/room/api/IFloatingApi$a;)V
.end method

.method public abstract r0(Ljava/lang/String;)V
.end method

.method public abstract show()V
.end method

.method public abstract t0(Ljava/lang/ref/WeakReference;)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/ref/WeakReference<",
            "Landroid/app/Activity;",
            ">;)V"
        }
    .end annotation
.end method

.method public abstract x(Ljava/lang/ref/WeakReference;Lcom/transsion/baselib/db/audio/AudioBean;)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/ref/WeakReference<",
            "Landroid/app/Activity;",
            ">;",
            "Lcom/transsion/baselib/db/audio/AudioBean;",
            ")V"
        }
    .end annotation
.end method
