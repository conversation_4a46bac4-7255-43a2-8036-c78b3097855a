.class public interface abstract Lcom/transsion/spwaitkiller/ProxySWork$AboveAndroid12Processor;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/transsion/spwaitkiller/ProxySWork;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "AboveAndroid12Processor"
.end annotation

.annotation runtime L<PERSON>lin/Metadata;
.end annotation


# virtual methods
.method public abstract reProxySWork()V
.end method
