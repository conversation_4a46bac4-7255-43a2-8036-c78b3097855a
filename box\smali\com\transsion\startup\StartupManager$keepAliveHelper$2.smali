.class final Lcom/transsion/startup/StartupManager$keepAliveHelper$2;
.super Lkotlin/jvm/internal/Lambda;

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/transsion/startup/StartupManager;-><init>()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/jvm/internal/Lambda;",
        "Lkotlin/jvm/functions/Function0<",
        "Lcom/transsion/startup/pref/al/a;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation


# static fields
.field public static final INSTANCE:Lcom/transsion/startup/StartupManager$keepAliveHelper$2;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    new-instance v0, Lcom/transsion/startup/StartupManager$keepAliveHelper$2;

    invoke-direct {v0}, Lcom/transsion/startup/StartupManager$keepAliveHelper$2;-><init>()V

    sput-object v0, Lcom/transsion/startup/StartupManager$keepAliveHelper$2;->INSTANCE:Lcom/transsion/startup/StartupManager$keepAliveHelper$2;

    return-void
.end method

.method public constructor <init>()V
    .locals 1

    const/4 v0, 0x0

    invoke-direct {p0, v0}, Lkotlin/jvm/internal/Lambda;-><init>(I)V

    return-void
.end method


# virtual methods
.method public final invoke()Lcom/transsion/startup/pref/al/a;
    .locals 1

    new-instance v0, Lcom/transsion/startup/pref/al/a;

    invoke-direct {v0}, Lcom/transsion/startup/pref/al/a;-><init>()V

    return-object v0
.end method

.method public bridge synthetic invoke()Ljava/lang/Object;
    .locals 1

    invoke-virtual {p0}, Lcom/transsion/startup/StartupManager$keepAliveHelper$2;->invoke()Lcom/transsion/startup/pref/al/a;

    move-result-object v0

    return-object v0
.end method
