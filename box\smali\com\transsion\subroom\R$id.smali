.class public final Lcom/transsion/subroom/R$id;
.super Ljava/lang/Object;


# static fields
.field public static bg_transparent:I = 0x7f0a00ce

.field public static btn_continue:I = 0x7f0a0102

.field public static btn_skip:I = 0x7f0a0119

.field public static centerBg:I = 0x7f0a0135

.field public static container:I = 0x7f0a0190

.field public static fl_content:I = 0x7f0a028f

.field public static guide_desc:I = 0x7f0a02e7

.field public static guide_image:I = 0x7f0a02e8

.field public static guide_title:I = 0x7f0a02ea

.field public static image_tab_icon:I = 0x7f0a0326

.field public static indicator:I = 0x7f0a0334

.field public static largeBottomBg:I = 0x7f0a04b7

.field public static leftBg:I = 0x7f0a04de

.field public static leftTwoBg:I = 0x7f0a04e0

.field public static ll_no_network:I = 0x7f0a0522

.field public static load_view:I = 0x7f0a0539

.field public static no_network_tip:I = 0x7f0a06aa

.field public static pb_skip:I = 0x7f0a070c

.field public static recycler_view:I = 0x7f0a0780

.field public static rightBg:I = 0x7f0a0793

.field public static rightTwoBg:I = 0x7f0a0795

.field public static tabBg:I = 0x7f0a08ca

.field public static tab_bottom:I = 0x7f0a08cc

.field public static tv_call:I = 0x7f0a0a0e

.field public static tv_perfer:I = 0x7f0a0a9f

.field public static tv_setting:I = 0x7f0a0aee

.field public static tv_skip:I = 0x7f0a0af7

.field public static tv_sub_title:I = 0x7f0a0b01

.field public static tv_submit:I = 0x7f0a0b08

.field public static tv_title:I = 0x7f0a0b1d

.field public static view_pager:I = 0x7f0a0c0b


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
