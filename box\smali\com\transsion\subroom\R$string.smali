.class public final Lcom/transsion/subroom/R$string;
.super Ljava/lang/Object;


# static fields
.field public static bottom_tab_group:I = 0x7f120072

.field public static bottom_tab_home:I = 0x7f120073

.field public static bottom_tab_me:I = 0x7f120074

.field public static bottom_tab_tips_hot:I = 0x7f120075

.field public static choose_at_least_one:I = 0x7f120096

.field public static choose_up_to_5:I = 0x7f120098

.field public static com_crashlytics_android_build_id:I = 0x7f1200b0

.field public static default_web_client_id:I = 0x7f120121

.field public static gcm_defaultSenderId:I = 0x7f120276

.field public static get_started:I = 0x7f120278

.field public static google_api_key:I = 0x7f12027b

.field public static google_app_id:I = 0x7f12027c

.field public static google_crash_reporting_api_key:I = 0x7f12027e

.field public static google_storage_bucket:I = 0x7f12027f

.field public static guide_desc1:I = 0x7f120283

.field public static guide_desc2:I = 0x7f120284

.field public static guide_title1:I = 0x7f120286

.field public static guide_title2:I = 0x7f120287

.field public static in_current_region:I = 0x7f1202b0

.field public static install_tip:I = 0x7f1202b6

.field public static install_update:I = 0x7f1202b7

.field public static not_available:I = 0x7f120477

.field public static project_id:I = 0x7f120552

.field public static skip:I = 0x7f1205e9

.field public static to_main:I = 0x7f1206b6

.field public static user_perfer_skip:I = 0x7f12079e

.field public static user_perfer_sub_title:I = 0x7f12079f

.field public static user_perfer_submit:I = 0x7f1207a0

.field public static user_perfer_title:I = 0x7f1207a1


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
