.class public final Lcom/transsion/subroom/R$styleable;
.super Ljava/lang/Object;


# static fields
.field public static Indicator:[I = null

.field public static Indicator_CustomHorizontalMargin:I = 0x0

.field public static Indicator_CustomIndicatorSize:I = 0x1

.field public static Indicator_CustomSelectedIndex:I = 0x2


# direct methods
.method public static constructor <clinit>()V
    .locals 3

    const v0, 0x7f040001

    const v1, 0x7f040002

    const/high16 v2, 0x7f040000

    filled-new-array {v2, v0, v1}, [I

    move-result-object v0

    sput-object v0, Lcom/transsion/subroom/R$styleable;->Indicator:[I

    return-void
.end method

.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
