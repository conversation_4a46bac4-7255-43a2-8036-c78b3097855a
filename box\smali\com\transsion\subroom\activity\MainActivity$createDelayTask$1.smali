.class final Lcom/transsion/subroom/activity/MainActivity$createDelayTask$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/transsion/subroom/activity/MainActivity;->S()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/k0;",
        "Lkotlin/coroutines/Continuation<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation

.annotation runtime Lkotlin/coroutines/jvm/internal/DebugMetadata;
    c = "com.transsion.subroom.activity.MainActivity$createDelayTask$1"
    f = "MainActivity.kt"
    l = {
        0x3a7,
        0x3b7
    }
    m = "invokeSuspend"
.end annotation

.annotation build Lkotlin/jvm/internal/SourceDebugExtension;
.end annotation


# instance fields
.field label:I

.field final synthetic this$0:Lcom/transsion/subroom/activity/MainActivity;


# direct methods
.method public constructor <init>(Lcom/transsion/subroom/activity/MainActivity;Lkotlin/coroutines/Continuation;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/transsion/subroom/activity/MainActivity;",
            "Lkotlin/coroutines/Continuation<",
            "-",
            "Lcom/transsion/subroom/activity/MainActivity$createDelayTask$1;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lcom/transsion/subroom/activity/MainActivity$createDelayTask$1;->this$0:Lcom/transsion/subroom/activity/MainActivity;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/Continuation;)V

    return-void
.end method

.method public static synthetic a(Lcom/transsion/subroom/activity/MainActivity;)V
    .locals 0

    invoke-static {p0}, Lcom/transsion/subroom/activity/MainActivity$createDelayTask$1;->i(Lcom/transsion/subroom/activity/MainActivity;)V

    return-void
.end method

.method public static synthetic d()V
    .locals 0

    invoke-static {}, Lcom/transsion/subroom/activity/MainActivity$createDelayTask$1;->k()V

    return-void
.end method

.method public static synthetic g(Lcom/transsion/subroom/activity/MainActivity;)V
    .locals 0

    invoke-static {p0}, Lcom/transsion/subroom/activity/MainActivity$createDelayTask$1;->l(Lcom/transsion/subroom/activity/MainActivity;)V

    return-void
.end method

.method public static synthetic h()V
    .locals 0

    invoke-static {}, Lcom/transsion/subroom/activity/MainActivity$createDelayTask$1;->j()V

    return-void
.end method

.method public static final i(Lcom/transsion/subroom/activity/MainActivity;)V
    .locals 1

    invoke-static {p0}, Lcom/transsion/subroom/activity/MainActivity;->B(Lcom/transsion/subroom/activity/MainActivity;)Lcom/transsion/subroom/update/GPUpdateManager;

    move-result-object v0

    invoke-virtual {v0, p0}, Lcom/transsion/subroom/update/GPUpdateManager;->j(Landroid/app/Activity;)V

    return-void
.end method

.method public static final j()V
    .locals 1

    sget-object v0, Lcom/transsnet/downloader/manager/DownloadInterceptManager;->a:Lcom/transsnet/downloader/manager/DownloadInterceptManager$a;

    invoke-virtual {v0}, Lcom/transsnet/downloader/manager/DownloadInterceptManager$a;->a()Lcom/transsnet/downloader/manager/DownloadInterceptManager;

    move-result-object v0

    invoke-virtual {v0}, Lcom/transsnet/downloader/manager/DownloadInterceptManager;->g()V

    return-void
.end method

.method public static final k()V
    .locals 1

    sget-object v0, Lcom/transsnet/downloader/notification/DownloadNotificationUtils;->a:Lcom/transsnet/downloader/notification/DownloadNotificationUtils;

    invoke-virtual {v0}, Lcom/transsnet/downloader/notification/DownloadNotificationUtils;->C()V

    return-void
.end method

.method public static final l(Lcom/transsion/subroom/activity/MainActivity;)V
    .locals 7

    sget-object v0, Lcom/transsion/push/utils/NotificationUtil;->a:Lcom/transsion/push/utils/NotificationUtil;

    const/4 v2, 0x0

    const/4 v3, 0x0

    const/4 v4, 0x0

    const/16 v5, 0xe

    const/4 v6, 0x0

    move-object v1, p0

    invoke-static/range {v0 .. v6}, Lcom/transsion/push/utils/NotificationUtil;->v(Lcom/transsion/push/utils/NotificationUtil;Landroid/content/Context;Lcom/transsion/push/bean/ShowOrder;Ljava/lang/String;ZILjava/lang/Object;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/Continuation;)Lkotlin/coroutines/Continuation;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/Continuation<",
            "*>;)",
            "Lkotlin/coroutines/Continuation<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance p1, Lcom/transsion/subroom/activity/MainActivity$createDelayTask$1;

    iget-object v0, p0, Lcom/transsion/subroom/activity/MainActivity$createDelayTask$1;->this$0:Lcom/transsion/subroom/activity/MainActivity;

    invoke-direct {p1, v0, p2}, Lcom/transsion/subroom/activity/MainActivity$createDelayTask$1;-><init>(Lcom/transsion/subroom/activity/MainActivity;Lkotlin/coroutines/Continuation;)V

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    check-cast p1, Lkotlinx/coroutines/k0;

    check-cast p2, Lkotlin/coroutines/Continuation;

    invoke-virtual {p0, p1, p2}, Lcom/transsion/subroom/activity/MainActivity$createDelayTask$1;->invoke(Lkotlinx/coroutines/k0;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/k0;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/k0;",
            "Lkotlin/coroutines/Continuation<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    invoke-virtual {p0, p1, p2}, Lcom/transsion/subroom/activity/MainActivity$createDelayTask$1;->create(Ljava/lang/Object;Lkotlin/coroutines/Continuation;)Lkotlin/coroutines/Continuation;

    move-result-object p1

    check-cast p1, Lcom/transsion/subroom/activity/MainActivity$createDelayTask$1;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lcom/transsion/subroom/activity/MainActivity$createDelayTask$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 12

    invoke-static {}, Lkotlin/coroutines/intrinsics/IntrinsicsKt;->e()Ljava/lang/Object;

    move-result-object v0

    iget v1, p0, Lcom/transsion/subroom/activity/MainActivity$createDelayTask$1;->label:I

    const/4 v2, 0x2

    const/4 v3, 0x1

    if-eqz v1, :cond_2

    if-eq v1, v3, :cond_1

    if-ne v1, v2, :cond_0

    invoke-static {p1}, Lkotlin/ResultKt;->b(Ljava/lang/Object;)V

    goto/16 :goto_2

    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw p1

    :cond_1
    invoke-static {p1}, Lkotlin/ResultKt;->b(Ljava/lang/Object;)V

    goto :goto_0

    :cond_2
    invoke-static {p1}, Lkotlin/ResultKt;->b(Ljava/lang/Object;)V

    sget-object p1, Lcom/tn/lib/thread/d;->a:Lcom/tn/lib/thread/d;

    iget-object v1, p0, Lcom/transsion/subroom/activity/MainActivity$createDelayTask$1;->this$0:Lcom/transsion/subroom/activity/MainActivity;

    new-instance v4, Lcom/transsion/subroom/activity/h;

    invoke-direct {v4, v1}, Lcom/transsion/subroom/activity/h;-><init>(Lcom/transsion/subroom/activity/MainActivity;)V

    invoke-virtual {p1, v4}, Lcom/tn/lib/thread/d;->b(Ljava/lang/Runnable;)V

    iput v3, p0, Lcom/transsion/subroom/activity/MainActivity$createDelayTask$1;->label:I

    const-wide/16 v4, 0x7d0

    invoke-static {v4, v5, p0}, Lkotlinx/coroutines/r0;->a(JLkotlin/coroutines/Continuation;)Ljava/lang/Object;

    move-result-object p1

    if-ne p1, v0, :cond_3

    return-object v0

    :cond_3
    :goto_0
    sget-object p1, Lcom/transsion/startup/pref/al/UpdateUtils;->a:Lcom/transsion/startup/pref/al/UpdateUtils;

    invoke-virtual {p1}, Lcom/transsion/startup/pref/al/UpdateUtils;->c()Z

    move-result v1

    if-eqz v1, :cond_4

    const-string v1, "main_page"

    const/4 v4, 0x0

    invoke-static {p1, v1, v4, v2, v4}, Lcom/transsion/startup/pref/al/UpdateUtils;->g(Lcom/transsion/startup/pref/al/UpdateUtils;Ljava/lang/String;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)V

    iget-object v6, p0, Lcom/transsion/subroom/activity/MainActivity$createDelayTask$1;->this$0:Lcom/transsion/subroom/activity/MainActivity;

    new-instance v11, Lcom/transsion/subroom/activity/MainActivity$createDelayTask$1$2;

    invoke-direct {v11, v6}, Lcom/transsion/subroom/activity/MainActivity$createDelayTask$1$2;-><init>(Lcom/transsion/subroom/activity/MainActivity;)V

    sget-object p1, Lcom/transsnet/flow/event/AppScopeVMlProvider;->INSTANCE:Lcom/transsnet/flow/event/AppScopeVMlProvider;

    const-class v1, Lcom/transsnet/flow/event/FlowEventBus;

    invoke-virtual {p1, v1}, Lcom/transsnet/flow/event/AppScopeVMlProvider;->getApplicationScopeViewModel(Ljava/lang/Class;)Landroidx/lifecycle/u0;

    move-result-object p1

    move-object v5, p1

    check-cast v5, Lcom/transsnet/flow/event/FlowEventBus;

    const-class p1, Lcom/transsnet/flow/event/sync/event/DiffUpdateResultEvent;

    invoke-virtual {p1}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object v7

    const-string p1, "T::class.java.name"

    invoke-static {v7, p1}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    sget-object v8, Landroidx/lifecycle/Lifecycle$State;->CREATED:Landroidx/lifecycle/Lifecycle$State;

    invoke-static {}, Lkotlinx/coroutines/w0;->c()Lkotlinx/coroutines/a2;

    move-result-object p1

    invoke-virtual {p1}, Lkotlinx/coroutines/a2;->t()Lkotlinx/coroutines/a2;

    move-result-object v9

    const/4 v10, 0x0

    invoke-virtual/range {v5 .. v11}, Lcom/transsnet/flow/event/FlowEventBus;->observeEvent(Landroidx/lifecycle/u;Ljava/lang/String;Landroidx/lifecycle/Lifecycle$State;Lkotlinx/coroutines/CoroutineDispatcher;ZLkotlin/jvm/functions/Function1;)Lkotlinx/coroutines/q1;

    goto :goto_1

    :cond_4
    sget-object p1, Lcom/transsion/mb/config/manager/ConfigManager;->c:Lcom/transsion/mb/config/manager/ConfigManager$a;

    invoke-virtual {p1}, Lcom/transsion/mb/config/manager/ConfigManager$a;->a()Lcom/transsion/mb/config/manager/ConfigManager;

    move-result-object p1

    const-string v1, "key_auto_update_dialog"

    invoke-virtual {p1, v1, v3}, Lcom/transsion/mb/config/manager/ConfigManager;->b(Ljava/lang/String;Z)Lcom/transsion/mb/config/manager/ConfigBean;

    move-result-object p1

    if-eqz p1, :cond_5

    invoke-virtual {p1}, Lcom/transsion/mb/config/manager/ConfigBean;->e()Ljava/lang/String;

    move-result-object p1

    if-eqz p1, :cond_5

    invoke-static {p1}, Lkotlin/text/StringsKt;->V0(Ljava/lang/String;)Ljava/lang/Boolean;

    move-result-object p1

    if-eqz p1, :cond_5

    invoke-virtual {p1}, Ljava/lang/Boolean;->booleanValue()Z

    move-result p1

    if-eqz p1, :cond_5

    sget-object p1, Lcom/cloud/tupdate/TUpdate;->c:Lcom/cloud/tupdate/TUpdate$a;

    iget-object v1, p0, Lcom/transsion/subroom/activity/MainActivity$createDelayTask$1;->this$0:Lcom/transsion/subroom/activity/MainActivity;

    invoke-virtual {p1, v1}, Lcom/cloud/tupdate/TUpdate$a;->c(Landroid/content/Context;)Li7/b$a;

    move-result-object p1

    sget-object v1, Lcom/tn/lib/util/device/TNDeviceHelper;->a:Lcom/tn/lib/util/device/TNDeviceHelper;

    invoke-virtual {v1}, Lcom/tn/lib/util/device/TNDeviceHelper;->g()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p1, v1}, Li7/b$a;->f(Ljava/lang/String;)Li7/b$a;

    move-result-object p1

    invoke-virtual {p1}, Li7/b$a;->g()V

    :cond_5
    :goto_1
    iput v2, p0, Lcom/transsion/subroom/activity/MainActivity$createDelayTask$1;->label:I

    const-wide/16 v1, 0xbb8

    invoke-static {v1, v2, p0}, Lkotlinx/coroutines/r0;->a(JLkotlin/coroutines/Continuation;)Ljava/lang/Object;

    move-result-object p1

    if-ne p1, v0, :cond_6

    return-object v0

    :cond_6
    :goto_2
    sget-object p1, Lcom/tn/lib/thread/d;->a:Lcom/tn/lib/thread/d;

    new-instance v0, Lcom/transsion/subroom/activity/i;

    invoke-direct {v0}, Lcom/transsion/subroom/activity/i;-><init>()V

    invoke-virtual {p1, v0}, Lcom/tn/lib/thread/d;->b(Ljava/lang/Runnable;)V

    new-instance v0, Lcom/transsion/subroom/activity/j;

    invoke-direct {v0}, Lcom/transsion/subroom/activity/j;-><init>()V

    invoke-virtual {p1, v0}, Lcom/tn/lib/thread/d;->b(Ljava/lang/Runnable;)V

    iget-object v0, p0, Lcom/transsion/subroom/activity/MainActivity$createDelayTask$1;->this$0:Lcom/transsion/subroom/activity/MainActivity;

    new-instance v1, Lcom/transsion/subroom/activity/k;

    invoke-direct {v1, v0}, Lcom/transsion/subroom/activity/k;-><init>(Lcom/transsion/subroom/activity/MainActivity;)V

    invoke-virtual {p1, v1}, Lcom/tn/lib/thread/d;->b(Ljava/lang/Runnable;)V

    sget-object p1, Lcom/transsion/usercenter/setting/labelsfeedback/sender/FBSender;->a:Lcom/transsion/usercenter/setting/labelsfeedback/sender/FBSender;

    iget-object v0, p0, Lcom/transsion/subroom/activity/MainActivity$createDelayTask$1;->this$0:Lcom/transsion/subroom/activity/MainActivity;

    invoke-virtual {v0}, Landroid/app/Activity;->getApplication()Landroid/app/Application;

    move-result-object v0

    const-string v1, "application"

    invoke-static {v0, v1}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-virtual {p1, v0}, Lcom/transsion/usercenter/setting/labelsfeedback/sender/FBSender;->f(Landroid/app/Application;)V

    iget-object p1, p0, Lcom/transsion/subroom/activity/MainActivity$createDelayTask$1;->this$0:Lcom/transsion/subroom/activity/MainActivity;

    invoke-static {p1, v3}, Lcom/transsion/subroom/activity/MainActivity;->L(Lcom/transsion/subroom/activity/MainActivity;Z)V

    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    return-object p1
.end method
