.class public final Lcom/transsion/subroom/activity/MainActivity;
.super Lcom/transsion/baseui/activity/BaseActivity;

# interfaces
.implements Lcom/transsion/home/<USER>/a;
.implements Lcn/a;


# annotations
.annotation build Lcom/alibaba/android/arouter/facade/annotation/Route;
    path = "/main/tab"
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/transsion/subroom/activity/MainActivity$a;
    }
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lcom/transsion/baseui/activity/BaseActivity<",
        "Luq/b;",
        ">;",
        "Lcom/transsion/home/<USER>/a;",
        "Lcn/a;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation

.annotation build Lkotlin/jvm/internal/SourceDebugExtension;
.end annotation


# static fields
.field public static final x:Lcom/transsion/subroom/activity/MainActivity$a;


# instance fields
.field public a:Ltt/e;

.field public b:Lcom/transsion/home/<USER>/HomeFragment;

.field public c:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Landroidx/fragment/app/Fragment;",
            ">;"
        }
    .end annotation
.end field

.field public d:Landroidx/fragment/app/Fragment;

.field public e:I

.field public f:I

.field public g:Z

.field public h:Ljava/lang/String;
    .annotation build Lcom/alibaba/android/arouter/facade/annotation/Autowired;
        name = "topTab"
    .end annotation

    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public i:Ljava/lang/String;
    .annotation build Lcom/alibaba/android/arouter/facade/annotation/Autowired;
        name = "bottomTab"
    .end annotation

    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public j:I
    .annotation build Lcom/alibaba/android/arouter/facade/annotation/Autowired;
        name = "tabIndex"
    .end annotation

    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public k:I
    .annotation build Lcom/alibaba/android/arouter/facade/annotation/Autowired;
        name = "secondTabIndex"
    .end annotation

    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public l:J

.field public m:Lcom/transsion/home/<USER>/b;

.field public n:Lcom/transsion/home/<USER>/AppTab;

.field public o:Z

.field public p:Z

.field public q:Ljava/lang/String;

.field public final r:Lkotlin/Lazy;

.field public s:Z

.field public t:Landroid/view/GestureDetector;

.field public u:Z

.field public v:Z

.field public w:J


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lcom/transsion/subroom/activity/MainActivity$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lcom/transsion/subroom/activity/MainActivity$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lcom/transsion/subroom/activity/MainActivity;->x:Lcom/transsion/subroom/activity/MainActivity$a;

    return-void
.end method

.method public constructor <init>()V
    .locals 2

    invoke-direct {p0}, Lcom/transsion/baseui/activity/BaseActivity;-><init>()V

    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lcom/transsion/subroom/activity/MainActivity;->c:Ljava/util/List;

    iget v0, p0, Lcom/transsion/subroom/activity/MainActivity;->e:I

    iput v0, p0, Lcom/transsion/subroom/activity/MainActivity;->f:I

    const-string v0, "Trending"

    iput-object v0, p0, Lcom/transsion/subroom/activity/MainActivity;->h:Ljava/lang/String;

    const-string v0, "HOME"

    iput-object v0, p0, Lcom/transsion/subroom/activity/MainActivity;->i:Ljava/lang/String;

    const/4 v0, -0x1

    iput v0, p0, Lcom/transsion/subroom/activity/MainActivity;->j:I

    iput v0, p0, Lcom/transsion/subroom/activity/MainActivity;->k:I

    const/4 v0, 0x1

    iput-boolean v0, p0, Lcom/transsion/subroom/activity/MainActivity;->p:Z

    const-string v1, ""

    iput-object v1, p0, Lcom/transsion/subroom/activity/MainActivity;->q:Ljava/lang/String;

    sget-object v1, Lcom/transsion/subroom/activity/MainActivity$mbUpdateManager$2;->INSTANCE:Lcom/transsion/subroom/activity/MainActivity$mbUpdateManager$2;

    invoke-static {v1}, Lkotlin/LazyKt;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/Lazy;

    move-result-object v1

    iput-object v1, p0, Lcom/transsion/subroom/activity/MainActivity;->r:Lkotlin/Lazy;

    iput-boolean v0, p0, Lcom/transsion/subroom/activity/MainActivity;->s:Z

    iput-boolean v0, p0, Lcom/transsion/subroom/activity/MainActivity;->u:Z

    return-void
.end method

.method public static final synthetic A(Lcom/transsion/subroom/activity/MainActivity;)Lcom/transsion/home/<USER>/AppTab;
    .locals 0

    iget-object p0, p0, Lcom/transsion/subroom/activity/MainActivity;->n:Lcom/transsion/home/<USER>/AppTab;

    return-object p0
.end method

.method public static final synthetic B(Lcom/transsion/subroom/activity/MainActivity;)Lcom/transsion/subroom/update/GPUpdateManager;
    .locals 0

    invoke-virtual {p0}, Lcom/transsion/subroom/activity/MainActivity;->T()Lcom/transsion/subroom/update/GPUpdateManager;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic C(Lcom/transsion/subroom/activity/MainActivity;Ljava/lang/String;I)I
    .locals 0

    invoke-virtual {p0, p1, p2}, Lcom/transsion/subroom/activity/MainActivity;->U(Ljava/lang/String;I)I

    move-result p0

    return p0
.end method

.method public static final synthetic D(Lcom/transsion/subroom/activity/MainActivity;Lcom/google/android/material/tabs/TabLayout$Tab;)V
    .locals 0

    invoke-virtual {p0, p1}, Lcom/transsion/subroom/activity/MainActivity;->f0(Lcom/google/android/material/tabs/TabLayout$Tab;)V

    return-void
.end method

.method public static final synthetic E(Lcom/transsion/subroom/activity/MainActivity;I)V
    .locals 0

    invoke-virtual {p0, p1}, Lcom/transsion/subroom/activity/MainActivity;->j0(I)V

    return-void
.end method

.method public static final synthetic F(Lcom/transsion/subroom/activity/MainActivity;Landroid/view/GestureDetector;)V
    .locals 0

    iput-object p1, p0, Lcom/transsion/subroom/activity/MainActivity;->t:Landroid/view/GestureDetector;

    return-void
.end method

.method public static final synthetic G(Lcom/transsion/subroom/activity/MainActivity;Lcom/transsion/home/<USER>/AppTab;)V
    .locals 0

    iput-object p1, p0, Lcom/transsion/subroom/activity/MainActivity;->n:Lcom/transsion/home/<USER>/AppTab;

    return-void
.end method

.method public static final synthetic J(Lcom/transsion/subroom/activity/MainActivity;Z)V
    .locals 0

    invoke-virtual {p0, p1}, Lcom/transsion/subroom/activity/MainActivity;->m0(Z)V

    return-void
.end method

.method public static final synthetic L(Lcom/transsion/subroom/activity/MainActivity;Z)V
    .locals 0

    iput-boolean p1, p0, Lcom/transsion/subroom/activity/MainActivity;->o:Z

    return-void
.end method

.method public static final synthetic M(Lcom/transsion/subroom/activity/MainActivity;Z)V
    .locals 0

    iput-boolean p1, p0, Lcom/transsion/subroom/activity/MainActivity;->u:Z

    return-void
.end method

.method public static final synthetic N(Lcom/transsion/subroom/activity/MainActivity;II)V
    .locals 0

    invoke-virtual {p0, p1, p2}, Lcom/transsion/subroom/activity/MainActivity;->p0(II)V

    return-void
.end method

.method public static synthetic R(Lcom/transsion/subroom/activity/MainActivity;FILjava/lang/Object;)V
    .locals 0

    and-int/lit8 p2, p2, 0x1

    if-eqz p2, :cond_0

    const/high16 p1, 0x42480000    # 50.0f

    :cond_0
    invoke-virtual {p0, p1}, Lcom/transsion/subroom/activity/MainActivity;->Q(F)V

    return-void
.end method

.method private final Z()V
    .locals 4

    new-instance v0, Lcom/transsion/subroom/activity/MainActivity$c;

    invoke-direct {v0, p0}, Lcom/transsion/subroom/activity/MainActivity$c;-><init>(Lcom/transsion/subroom/activity/MainActivity;)V

    iput-object v0, p0, Lcom/transsion/subroom/activity/MainActivity;->a:Ltt/e;

    sget-object v0, Lcom/transsnet/downloader/manager/DownloadStatusIconManager;->h:Lcom/transsnet/downloader/manager/DownloadStatusIconManager$a;

    invoke-virtual {v0}, Lcom/transsnet/downloader/manager/DownloadStatusIconManager$a;->a()Lcom/transsnet/downloader/manager/DownloadStatusIconManager;

    move-result-object v1

    iget-object v2, p0, Lcom/transsion/subroom/activity/MainActivity;->a:Ltt/e;

    const-string v3, "null cannot be cast to non-null type com.transsnet.downloader.callback.OnDownloadIconStatusListener"

    invoke-static {v2, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-virtual {v1, v2}, Lcom/transsnet/downloader/manager/DownloadStatusIconManager;->m(Ltt/e;)V

    invoke-virtual {v0}, Lcom/transsnet/downloader/manager/DownloadStatusIconManager$a;->a()Lcom/transsnet/downloader/manager/DownloadStatusIconManager;

    move-result-object v0

    invoke-virtual {v0}, Lcom/transsnet/downloader/manager/DownloadStatusIconManager;->o()V

    return-void
.end method

.method public static final d0(Lcom/transsion/subroom/activity/MainActivity;Lcom/google/android/material/tabs/TabLayout$TabView;)V
    .locals 3

    const-string v0, "this$0"

    invoke-static {p0, v0}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    new-instance v0, Landroidx/lifecycle/w0;

    invoke-direct {v0, p0}, Landroidx/lifecycle/w0;-><init>(Landroidx/lifecycle/z0;)V

    const-class v1, Lcom/transsion/usercenter/profile/NoticeMessageViewModel;

    invoke-virtual {v0, v1}, Landroidx/lifecycle/w0;->a(Ljava/lang/Class;)Landroidx/lifecycle/u0;

    move-result-object v0

    check-cast v0, Lcom/transsion/usercenter/profile/NoticeMessageViewModel;

    invoke-virtual {v0}, Lcom/transsion/usercenter/profile/NoticeMessageViewModel;->g()Landroidx/lifecycle/c0;

    move-result-object v1

    new-instance v2, Lcom/transsion/subroom/activity/MainActivity$loadNoticeCount$1$1;

    invoke-direct {v2, p1}, Lcom/transsion/subroom/activity/MainActivity$loadNoticeCount$1$1;-><init>(Lcom/google/android/material/tabs/TabLayout$TabView;)V

    new-instance p1, Lcom/transsion/subroom/activity/MainActivity$f;

    invoke-direct {p1, v2}, Lcom/transsion/subroom/activity/MainActivity$f;-><init>(Lkotlin/jvm/functions/Function1;)V

    invoke-virtual {v1, p0, p1}, Landroidx/lifecycle/LiveData;->j(Landroidx/lifecycle/u;Landroidx/lifecycle/d0;)V

    invoke-virtual {v0}, Lcom/transsion/usercenter/profile/NoticeMessageViewModel;->c()V

    return-void
.end method

.method private final initPlayer()V
    .locals 23

    sget-object v0, Llo/b;->a:Llo/b;

    new-instance v15, Llo/d;

    move-object v1, v15

    sget-object v2, Lcom/transsion/player/config/RenderType;->SURFACE_VIEW:Lcom/transsion/player/config/RenderType;

    const/4 v3, 0x0

    const/4 v4, 0x0

    const/4 v5, 0x0

    const/4 v6, 0x0

    const/4 v7, 0x0

    const/4 v8, 0x0

    const/4 v9, 0x0

    const-wide/16 v10, 0x0

    const/4 v12, 0x0

    const/4 v13, 0x0

    const/4 v14, 0x0

    const/16 v16, 0x0

    move-object/from16 v22, v15

    move/from16 v15, v16

    const/16 v17, 0x0

    const/16 v18, 0x0

    const/16 v19, 0x0

    const v20, 0x1fffe

    const/16 v21, 0x0

    invoke-direct/range {v1 .. v21}, Llo/d;-><init>(Lcom/transsion/player/config/RenderType;ZIIIIIIJIIZZZZZLjava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    move-object/from16 v1, v22

    invoke-virtual {v0, v1}, Llo/b;->d(Llo/d;)V

    return-void
.end method

.method public static final l0(Lcom/transsion/subroom/activity/MainActivity;ILandroid/view/View;)V
    .locals 3

    const-string p2, "this$0"

    invoke-static {p0, p2}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    iget p2, p0, Lcom/transsion/subroom/activity/MainActivity;->f:I

    if-eq p2, p1, :cond_0

    return-void

    :cond_0
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide p1

    iget-wide v0, p0, Lcom/transsion/subroom/activity/MainActivity;->w:J

    sub-long/2addr p1, v0

    const-wide/16 v0, 0x1f4

    cmp-long v2, p1, v0

    if-gez v2, :cond_2

    iget-object p1, p0, Lcom/transsion/subroom/activity/MainActivity;->b:Lcom/transsion/home/<USER>/HomeFragment;

    if-eqz p1, :cond_1

    invoke-virtual {p1}, Lcom/transsion/home/<USER>/HomeFragment;->s0()V

    :cond_1
    const-wide/16 p1, 0x0

    iput-wide p1, p0, Lcom/transsion/subroom/activity/MainActivity;->w:J

    goto :goto_0

    :cond_2
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide p1

    iput-wide p1, p0, Lcom/transsion/subroom/activity/MainActivity;->w:J

    :goto_0
    return-void
.end method

.method public static final n0(Lcom/google/android/material/tabs/TabLayout$TabView;Z)V
    .locals 1

    sget v0, Lcom/transsion/home/<USER>

    invoke-virtual {p0, v0}, Landroid/view/View;->findViewById(I)Landroid/view/View;

    move-result-object p0

    check-cast p0, Landroid/widget/ImageView;

    if-eqz p1, :cond_0

    if-eqz p0, :cond_1

    invoke-static {p0}, Lvi/c;->k(Landroid/view/View;)V

    goto :goto_0

    :cond_0
    if-eqz p0, :cond_1

    invoke-static {p0}, Lvi/c;->g(Landroid/view/View;)V

    :cond_1
    :goto_0
    return-void
.end method

.method public static synthetic w(Lcom/transsion/subroom/activity/MainActivity;ILandroid/view/View;)V
    .locals 0

    invoke-static {p0, p1, p2}, Lcom/transsion/subroom/activity/MainActivity;->l0(Lcom/transsion/subroom/activity/MainActivity;ILandroid/view/View;)V

    return-void
.end method

.method public static synthetic x(Lcom/transsion/subroom/activity/MainActivity;Lcom/google/android/material/tabs/TabLayout$TabView;)V
    .locals 0

    invoke-static {p0, p1}, Lcom/transsion/subroom/activity/MainActivity;->d0(Lcom/transsion/subroom/activity/MainActivity;Lcom/google/android/material/tabs/TabLayout$TabView;)V

    return-void
.end method

.method public static synthetic y(Lcom/google/android/material/tabs/TabLayout$TabView;Z)V
    .locals 0

    invoke-static {p0, p1}, Lcom/transsion/subroom/activity/MainActivity;->n0(Lcom/google/android/material/tabs/TabLayout$TabView;Z)V

    return-void
.end method

.method public static final synthetic z(Lcom/transsion/subroom/activity/MainActivity;Ljava/util/List;)V
    .locals 0

    invoke-virtual {p0, p1}, Lcom/transsion/subroom/activity/MainActivity;->O(Ljava/util/List;)V

    return-void
.end method


# virtual methods
.method public final O(Ljava/util/List;)V
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lcom/transsion/home/<USER>/BottomTabItem;",
            ">;)V"
        }
    .end annotation

    const/4 v0, 0x0

    if-eqz p1, :cond_0

    move-object v1, p1

    check-cast v1, Ljava/util/Collection;

    invoke-static {v1}, Lkotlin/collections/CollectionsKt;->F0(Ljava/util/Collection;)Ljava/util/List;

    move-result-object v1

    goto :goto_0

    :cond_0
    move-object v1, v0

    :goto_0
    check-cast p1, Ljava/util/Collection;

    if-eqz p1, :cond_1

    invoke-interface {p1}, Ljava/util/Collection;->isEmpty()Z

    move-result p1

    if-eqz p1, :cond_3

    :cond_1
    sget-object p1, Lxi/b;->a:Lxi/b$a;

    const-string v1, "bottom tabs \u5f02\u5e38\u515c\u5e95"

    const/4 v2, 0x1

    const-string v3, "MainActivity"

    invoke-virtual {p1, v3, v1, v2}, Lxi/b$a;->c(Ljava/lang/String;Ljava/lang/String;Z)V

    sget-object p1, Lcom/transsion/home/<USER>/preload/PreloadTrendingData;->o:Lcom/transsion/home/<USER>/preload/PreloadTrendingData$a;

    invoke-virtual {p1}, Lcom/transsion/home/<USER>/preload/PreloadTrendingData$a;->a()Lcom/transsion/home/<USER>/preload/PreloadTrendingData;

    move-result-object p1

    invoke-virtual {p1, p0}, Lcom/transsion/home/<USER>/preload/PreloadTrendingData;->t(Landroid/content/Context;)Lcom/transsion/home/<USER>/AppTab;

    move-result-object p1

    iput-object p1, p0, Lcom/transsion/subroom/activity/MainActivity;->n:Lcom/transsion/home/<USER>/AppTab;

    if-eqz p1, :cond_2

    invoke-virtual {p1}, Lcom/transsion/home/<USER>/AppTab;->getBottomTabs()Ljava/util/List;

    move-result-object v0

    :cond_2
    move-object v1, v0

    :cond_3
    invoke-virtual {p0, v1}, Lcom/transsion/subroom/activity/MainActivity;->a0(Ljava/util/List;)V

    invoke-virtual {p0, v1}, Lcom/transsion/subroom/activity/MainActivity;->Y(Ljava/util/List;)V

    invoke-direct {p0}, Lcom/transsion/subroom/activity/MainActivity;->Z()V

    invoke-virtual {p0}, Lcom/transsion/subroom/activity/MainActivity;->k0()V

    invoke-virtual {p0}, Lcom/transsion/subroom/activity/MainActivity;->c0()V

    return-void
.end method

.method public final P(Ljava/util/List;)Z
    .locals 9
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lcom/transsion/home/<USER>/BottomTabItem;",
            ">;)Z"
        }
    .end annotation

    const/4 v0, 0x0

    if-eqz p1, :cond_0

    invoke-interface {p1}, Ljava/util/List;->size()I

    move-result v1

    goto :goto_0

    :cond_0
    const/4 v1, 0x0

    :goto_0
    const/4 v2, 0x0

    if-eqz p1, :cond_7

    invoke-interface {p1}, Ljava/util/List;->size()I

    move-result v3

    div-int/lit8 v3, v3, 0x2

    check-cast p1, Ljava/lang/Iterable;

    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    move-result-object p1

    move-object v4, v2

    const/4 v5, 0x0

    :goto_1
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v6

    if-eqz v6, :cond_6

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v6

    add-int/lit8 v7, v5, 0x1

    if-gez v5, :cond_1

    invoke-static {}, Lkotlin/collections/CollectionsKt;->u()V

    :cond_1
    check-cast v6, Lcom/transsion/home/<USER>/BottomTabItem;

    invoke-virtual {v6}, Lcom/transsion/home/<USER>/BottomTabItem;->isLargeUiType()Z

    move-result v8

    if-eqz v8, :cond_5

    if-eq v5, v3, :cond_4

    invoke-virtual {v6}, Lcom/transsion/home/<USER>/BottomTabItem;->getIcon()Lcom/transsion/home/<USER>/Icon;

    move-result-object v5

    if-nez v5, :cond_2

    goto :goto_2

    :cond_2
    invoke-virtual {v5, v2}, Lcom/transsion/home/<USER>/Icon;->setDefaultBigIcon(Ljava/lang/String;)V

    :goto_2
    invoke-virtual {v6}, Lcom/transsion/home/<USER>/BottomTabItem;->getIcon()Lcom/transsion/home/<USER>/Icon;

    move-result-object v5

    if-nez v5, :cond_3

    goto :goto_3

    :cond_3
    invoke-virtual {v5, v2}, Lcom/transsion/home/<USER>/Icon;->setSelectBigIcon(Ljava/lang/String;)V

    goto :goto_3

    :cond_4
    move-object v4, v6

    :cond_5
    :goto_3
    move v5, v7

    goto :goto_1

    :cond_6
    move-object v2, v4

    :cond_7
    const/4 p1, 0x1

    if-eqz v2, :cond_8

    rem-int/lit8 v1, v1, 0x2

    if-ne v1, p1, :cond_8

    const/4 v1, 0x1

    goto :goto_4

    :cond_8
    const/4 v1, 0x0

    :goto_4
    iput-boolean v1, p0, Lcom/transsion/subroom/activity/MainActivity;->v:Z

    invoke-virtual {p0}, Lcom/transsion/baseui/activity/BaseCommonActivity;->getMViewBinding()Ls4/a;

    move-result-object v1

    check-cast v1, Luq/b;

    iget-object v1, v1, Luq/b;->d:Landroidx/constraintlayout/widget/Group;

    const-string v3, "mViewBinding.largeBottomBg"

    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    iget-boolean v3, p0, Lcom/transsion/subroom/activity/MainActivity;->v:Z

    const/16 v4, 0x8

    if-eqz v3, :cond_9

    const/4 v3, 0x0

    goto :goto_5

    :cond_9
    const/16 v3, 0x8

    :goto_5
    invoke-virtual {v1, v3}, Landroid/view/View;->setVisibility(I)V

    invoke-virtual {p0}, Lcom/transsion/baseui/activity/BaseCommonActivity;->getMViewBinding()Ls4/a;

    move-result-object v1

    check-cast v1, Luq/b;

    iget-object v1, v1, Luq/b;->i:Landroid/widget/ImageView;

    const-string v3, "mViewBinding.tabBg"

    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    iget-boolean v3, p0, Lcom/transsion/subroom/activity/MainActivity;->v:Z

    xor-int/2addr v3, p1

    if-eqz v3, :cond_a

    const/4 v4, 0x0

    :cond_a
    invoke-virtual {v1, v4}, Landroid/view/View;->setVisibility(I)V

    if-eqz v2, :cond_b

    const/4 v0, 0x1

    :cond_b
    return v0
.end method

.method public final Q(F)V
    .locals 2

    invoke-static {}, Lcom/alibaba/android/arouter/launcher/a;->d()Lcom/alibaba/android/arouter/launcher/a;

    move-result-object v0

    const-class v1, Lcom/transsion/memberapi/IMemberApi;

    invoke-virtual {v0, v1}, Lcom/alibaba/android/arouter/launcher/a;->h(Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/transsion/memberapi/IMemberApi;

    invoke-interface {v0, p1}, Lcom/transsion/memberapi/IMemberApi;->P0(F)V

    return-void
.end method

.method public final S()V
    .locals 7

    iget-boolean v0, p0, Lcom/transsion/subroom/activity/MainActivity;->o:Z

    if-eqz v0, :cond_0

    return-void

    :cond_0
    sget-object v0, Lkotlinx/coroutines/g0;->C0:Lkotlinx/coroutines/g0$a;

    new-instance v2, Lcom/transsion/subroom/activity/MainActivity$b;

    invoke-direct {v2, v0}, Lcom/transsion/subroom/activity/MainActivity$b;-><init>(Lkotlinx/coroutines/g0$a;)V

    invoke-static {p0}, Landroidx/lifecycle/v;->a(Landroidx/lifecycle/u;)Landroidx/lifecycle/LifecycleCoroutineScope;

    move-result-object v1

    const/4 v3, 0x0

    new-instance v4, Lcom/transsion/subroom/activity/MainActivity$createDelayTask$1;

    const/4 v0, 0x0

    invoke-direct {v4, p0, v0}, Lcom/transsion/subroom/activity/MainActivity$createDelayTask$1;-><init>(Lcom/transsion/subroom/activity/MainActivity;Lkotlin/coroutines/Continuation;)V

    const/4 v5, 0x2

    const/4 v6, 0x0

    invoke-static/range {v1 .. v6}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/k0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/q1;

    return-void
.end method

.method public final T()Lcom/transsion/subroom/update/GPUpdateManager;
    .locals 1

    iget-object v0, p0, Lcom/transsion/subroom/activity/MainActivity;->r:Lkotlin/Lazy;

    invoke-interface {v0}, Lkotlin/Lazy;->getValue()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/transsion/subroom/update/GPUpdateManager;

    return-object v0
.end method

.method public final U(Ljava/lang/String;I)I
    .locals 4

    if-ltz p2, :cond_4

    const/4 v0, 0x5

    if-ge p2, v0, :cond_4

    const-string p1, "HOME"

    if-eqz p2, :cond_4

    const/4 v1, 0x1

    if-eq p2, v1, :cond_4

    const/4 v1, 0x2

    if-eq p2, v1, :cond_3

    const/4 v1, 0x3

    if-eq p2, v1, :cond_2

    const/4 v1, 0x4

    if-eq p2, v1, :cond_1

    if-eq p2, v0, :cond_0

    goto :goto_0

    :cond_0
    const-string p1, "MUSIC"

    goto :goto_0

    :cond_1
    const-string p1, "PREMIUM"

    goto :goto_0

    :cond_2
    const-string p1, "ME"

    goto :goto_0

    :cond_3
    const-string p1, "SHORTTV"

    :cond_4
    :goto_0
    iget-object p2, p0, Lcom/transsion/subroom/activity/MainActivity;->n:Lcom/transsion/home/<USER>/AppTab;

    const/4 v0, 0x0

    if-eqz p2, :cond_7

    invoke-virtual {p2}, Lcom/transsion/home/<USER>/AppTab;->getBottomTabs()Ljava/util/List;

    move-result-object p2

    if-eqz p2, :cond_7

    check-cast p2, Ljava/lang/Iterable;

    invoke-interface {p2}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    move-result-object p2

    const/4 v1, 0x0

    :goto_1
    invoke-interface {p2}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_7

    invoke-interface {p2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    add-int/lit8 v3, v1, 0x1

    if-gez v1, :cond_5

    invoke-static {}, Lkotlin/collections/CollectionsKt;->u()V

    :cond_5
    check-cast v2, Lcom/transsion/home/<USER>/BottomTabItem;

    invoke-virtual {v2}, Lcom/transsion/home/<USER>/BottomTabItem;->getBtTabCode()Ljava/lang/String;

    move-result-object v2

    invoke-static {v2, p1}, Lkotlin/jvm/internal/Intrinsics;->b(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_6

    move v0, v1

    :cond_6
    move v1, v3

    goto :goto_1

    :cond_7
    return v0
.end method

.method public final V(Ljava/lang/String;)I
    .locals 5

    iget-object v0, p0, Lcom/transsion/subroom/activity/MainActivity;->n:Lcom/transsion/home/<USER>/AppTab;

    const/4 v1, -0x1

    if-eqz v0, :cond_2

    invoke-virtual {v0}, Lcom/transsion/home/<USER>/AppTab;->getBottomTabs()Ljava/util/List;

    move-result-object v0

    if-eqz v0, :cond_2

    check-cast v0, Ljava/lang/Iterable;

    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    move-result-object v0

    const/4 v2, 0x0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v3

    if-eqz v3, :cond_2

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v3

    add-int/lit8 v4, v2, 0x1

    if-gez v2, :cond_0

    invoke-static {}, Lkotlin/collections/CollectionsKt;->u()V

    :cond_0
    check-cast v3, Lcom/transsion/home/<USER>/BottomTabItem;

    invoke-virtual {v3}, Lcom/transsion/home/<USER>/BottomTabItem;->getBtTabCode()Ljava/lang/String;

    move-result-object v3

    invoke-static {v3, p1}, Lkotlin/jvm/internal/Intrinsics;->b(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v3

    if-eqz v3, :cond_1

    move v1, v2

    :cond_1
    move v2, v4

    goto :goto_0

    :cond_2
    return v1
.end method

.method public final W(Ljava/lang/String;)I
    .locals 5

    iget-object v0, p0, Lcom/transsion/subroom/activity/MainActivity;->n:Lcom/transsion/home/<USER>/AppTab;

    const/4 v1, 0x0

    if-eqz v0, :cond_2

    invoke-virtual {v0}, Lcom/transsion/home/<USER>/AppTab;->getBottomTabs()Ljava/util/List;

    move-result-object v0

    if-eqz v0, :cond_2

    check-cast v0, Ljava/lang/Iterable;

    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    move-result-object v0

    const/4 v2, 0x0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v3

    if-eqz v3, :cond_2

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v3

    add-int/lit8 v4, v2, 0x1

    if-gez v2, :cond_0

    invoke-static {}, Lkotlin/collections/CollectionsKt;->u()V

    :cond_0
    check-cast v3, Lcom/transsion/home/<USER>/BottomTabItem;

    invoke-virtual {v3}, Lcom/transsion/home/<USER>/BottomTabItem;->getBtTabType()Ljava/lang/String;

    move-result-object v3

    invoke-static {v3, p1}, Lkotlin/jvm/internal/Intrinsics;->b(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v3

    if-eqz v3, :cond_1

    move v1, v2

    :cond_1
    move v2, v4

    goto :goto_0

    :cond_2
    return v1
.end method

.method public X()Luq/b;
    .locals 2

    invoke-virtual {p0}, Landroid/app/Activity;->getLayoutInflater()Landroid/view/LayoutInflater;

    move-result-object v0

    invoke-static {v0}, Luq/b;->c(Landroid/view/LayoutInflater;)Luq/b;

    move-result-object v0

    const-string v1, "inflate(layoutInflater)"

    invoke-static {v0, v1}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    return-object v0
.end method

.method public final Y(Ljava/util/List;)V
    .locals 9
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lcom/transsion/home/<USER>/BottomTabItem;",
            ">;)V"
        }
    .end annotation

    invoke-virtual {p0}, Lcom/transsion/baseui/activity/BaseCommonActivity;->getMViewBinding()Ls4/a;

    move-result-object v0

    check-cast v0, Luq/b;

    iget-object v0, v0, Luq/b;->j:Lcom/google/android/material/tabs/TabLayout;

    invoke-virtual {v0}, Lcom/google/android/material/tabs/TabLayout;->removeAllTabs()V

    sget-object v0, Lcom/transsion/baselib/report/launch/RoomAppMMKV;->a:Lcom/transsion/baselib/report/launch/RoomAppMMKV;

    invoke-virtual {v0}, Lcom/transsion/baselib/report/launch/RoomAppMMKV;->a()Lcom/tencent/mmkv/MMKV;

    move-result-object v1

    const-string v2, "tab_hot_tips_version"

    const-string v3, ""

    invoke-virtual {v1, v2, v3}, Lcom/tencent/mmkv/MMKV;->getString(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v1

    iget-object v2, p0, Lcom/transsion/subroom/activity/MainActivity;->n:Lcom/transsion/home/<USER>/AppTab;

    const/4 v3, 0x0

    if-eqz v2, :cond_0

    invoke-virtual {v2}, Lcom/transsion/home/<USER>/AppTab;->getBadgeVer()Ljava/lang/String;

    move-result-object v2

    invoke-static {v1, v2}, Landroid/text/TextUtils;->equals(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Z

    move-result v1

    if-nez v1, :cond_0

    invoke-virtual {v0}, Lcom/transsion/baselib/report/launch/RoomAppMMKV;->a()Lcom/tencent/mmkv/MMKV;

    move-result-object v0

    const-string v1, "tab_hot_tips_is_show"

    invoke-virtual {v0, v1, v3}, Lcom/tencent/mmkv/MMKV;->putBoolean(Ljava/lang/String;Z)Landroid/content/SharedPreferences$Editor;

    :cond_0
    invoke-virtual {p0, p1}, Lcom/transsion/subroom/activity/MainActivity;->P(Ljava/util/List;)Z

    if-eqz p1, :cond_3

    move-object v0, p1

    check-cast v0, Ljava/lang/Iterable;

    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    move-result-object v0

    const/4 v1, 0x0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_3

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    add-int/lit8 v4, v1, 0x1

    if-gez v1, :cond_1

    invoke-static {}, Lkotlin/collections/CollectionsKt;->u()V

    :cond_1
    check-cast v2, Lcom/transsion/home/<USER>/BottomTabItem;

    invoke-virtual {p0}, Lcom/transsion/baseui/activity/BaseCommonActivity;->getMViewBinding()Ls4/a;

    move-result-object v5

    check-cast v5, Luq/b;

    iget-object v5, v5, Luq/b;->j:Lcom/google/android/material/tabs/TabLayout;

    invoke-virtual {p0}, Lcom/transsion/baseui/activity/BaseCommonActivity;->getMViewBinding()Ls4/a;

    move-result-object v6

    check-cast v6, Luq/b;

    iget-object v6, v6, Luq/b;->j:Lcom/google/android/material/tabs/TabLayout;

    invoke-virtual {v6}, Lcom/google/android/material/tabs/TabLayout;->newTab()Lcom/google/android/material/tabs/TabLayout$Tab;

    move-result-object v6

    invoke-virtual {v2}, Lcom/transsion/home/<USER>/BottomTabItem;->getBtTabCode()Ljava/lang/String;

    move-result-object v7

    invoke-virtual {v6, v7}, Lcom/google/android/material/tabs/TabLayout$Tab;->setTag(Ljava/lang/Object;)Lcom/google/android/material/tabs/TabLayout$Tab;

    iget-object v7, v6, Lcom/google/android/material/tabs/TabLayout$Tab;->view:Lcom/google/android/material/tabs/TabLayout$TabView;

    const-string v8, "view"

    invoke-static {v7, v8}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    iget v8, p0, Lcom/transsion/subroom/activity/MainActivity;->f:I

    invoke-interface {p1, v8}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v8

    check-cast v8, Lcom/transsion/home/<USER>/BottomTabItem;

    invoke-virtual {p0, v2, v7, v8}, Lcom/transsion/subroom/activity/MainActivity;->t0(Lcom/transsion/home/<USER>/BottomTabItem;Lcom/google/android/material/tabs/TabLayout$TabView;Lcom/transsion/home/<USER>/BottomTabItem;)Landroid/view/View;

    move-result-object v2

    invoke-virtual {v6, v2}, Lcom/google/android/material/tabs/TabLayout$Tab;->setCustomView(Landroid/view/View;)Lcom/google/android/material/tabs/TabLayout$Tab;

    iget v2, p0, Lcom/transsion/subroom/activity/MainActivity;->f:I

    if-ne v2, v1, :cond_2

    const/4 v1, 0x1

    goto :goto_1

    :cond_2
    const/4 v1, 0x0

    :goto_1
    invoke-virtual {v5, v6, v1}, Lcom/google/android/material/tabs/TabLayout;->addTab(Lcom/google/android/material/tabs/TabLayout$Tab;Z)V

    move v1, v4

    goto :goto_0

    :cond_3
    return-void
.end method

.method public final a0(Ljava/util/List;)V
    .locals 17
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lcom/transsion/home/<USER>/BottomTabItem;",
            ">;)V"
        }
    .end annotation

    move-object/from16 v0, p0

    move-object/from16 v1, p1

    sget-object v2, Lxi/b;->a:Lxi/b$a;

    const-string v3, "MainActivity"

    const-string v4, "initFragment() --> \u5f00\u59cb\u521b\u5efaFragment\u4e86"

    const/4 v5, 0x0

    const/4 v6, 0x4

    const/4 v7, 0x0

    invoke-static/range {v2 .. v7}, Lxi/b$a;->f(Lxi/b$a;Ljava/lang/String;Ljava/lang/String;ZILjava/lang/Object;)V

    invoke-virtual/range {p0 .. p0}, Landroidx/fragment/app/FragmentActivity;->getSupportFragmentManager()Landroidx/fragment/app/FragmentManager;

    move-result-object v2

    const/4 v3, 0x0

    const/4 v4, 0x1

    invoke-virtual {v2, v3, v4}, Landroidx/fragment/app/FragmentManager;->popBackStackImmediate(Ljava/lang/String;I)Z

    iget-object v2, v0, Lcom/transsion/subroom/activity/MainActivity;->c:Ljava/util/List;

    invoke-interface {v2}, Ljava/util/List;->clear()V

    if-eqz v1, :cond_10

    move-object v2, v1

    check-cast v2, Ljava/lang/Iterable;

    invoke-interface {v2}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    move-result-object v2

    :goto_0
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    move-result v3

    if-eqz v3, :cond_10

    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lcom/transsion/home/<USER>/BottomTabItem;

    invoke-virtual {v3}, Lcom/transsion/home/<USER>/BottomTabItem;->getBtTabType()Ljava/lang/String;

    move-result-object v5

    sget-object v6, Lcom/transsion/home/<USER>/BottomTabType;->HOME:Lcom/transsion/home/<USER>/BottomTabType;

    invoke-virtual {v6}, Lcom/transsion/home/<USER>/BottomTabType;->getValue()Ljava/lang/String;

    move-result-object v6

    invoke-static {v5, v6}, Lkotlin/jvm/internal/Intrinsics;->b(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v6

    if-eqz v6, :cond_2

    sget-object v7, Lxi/b;->a:Lxi/b$a;

    const-string v8, "MainActivity"

    const-string v9, "initFragment() --> \u5f00\u59cb\u521b\u5efaFragment\u4e86 --> homeFragment"

    const/4 v10, 0x0

    const/4 v11, 0x4

    const/4 v12, 0x0

    invoke-static/range {v7 .. v12}, Lxi/b$a;->f(Lxi/b$a;Ljava/lang/String;Ljava/lang/String;ZILjava/lang/Object;)V

    iget-object v5, v0, Lcom/transsion/subroom/activity/MainActivity;->b:Lcom/transsion/home/<USER>/HomeFragment;

    if-eqz v5, :cond_0

    invoke-static {v5}, Lkotlin/jvm/internal/Intrinsics;->d(Ljava/lang/Object;)V

    goto/16 :goto_5

    :cond_0
    invoke-virtual/range {p0 .. p0}, Landroidx/fragment/app/FragmentActivity;->getSupportFragmentManager()Landroidx/fragment/app/FragmentManager;

    move-result-object v5

    invoke-virtual {v3}, Lcom/transsion/home/<USER>/BottomTabItem;->getBtTabCode()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v5, v3}, Landroidx/fragment/app/FragmentManager;->findFragmentByTag(Ljava/lang/String;)Landroidx/fragment/app/Fragment;

    move-result-object v3

    instance-of v5, v3, Lcom/transsion/home/<USER>/HomeFragment;

    if-eqz v5, :cond_1

    check-cast v3, Lcom/transsion/home/<USER>/HomeFragment;

    :goto_1
    move-object v5, v3

    goto :goto_2

    :cond_1
    new-instance v3, Lcom/transsion/home/<USER>/HomeFragment;

    invoke-direct {v3}, Lcom/transsion/home/<USER>/HomeFragment;-><init>()V

    goto :goto_1

    :goto_2
    iput-object v5, v0, Lcom/transsion/subroom/activity/MainActivity;->b:Lcom/transsion/home/<USER>/HomeFragment;

    invoke-static {v5}, Lkotlin/jvm/internal/Intrinsics;->d(Ljava/lang/Object;)V

    goto/16 :goto_5

    :cond_2
    sget-object v6, Lcom/transsion/home/<USER>/BottomTabType;->SHORT_TV:Lcom/transsion/home/<USER>/BottomTabType;

    invoke-virtual {v6}, Lcom/transsion/home/<USER>/BottomTabType;->getValue()Ljava/lang/String;

    move-result-object v6

    invoke-static {v5, v6}, Lkotlin/jvm/internal/Intrinsics;->b(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v6

    if-eqz v6, :cond_4

    sget-object v7, Lxi/b;->a:Lxi/b$a;

    const-string v8, "MainActivity"

    const-string v9, "initFragment() --> \u5f00\u59cb\u521b\u5efaFragment\u4e86 --> immersionVideoFragment"

    const/4 v10, 0x0

    const/4 v11, 0x4

    const/4 v12, 0x0

    invoke-static/range {v7 .. v12}, Lxi/b$a;->f(Lxi/b$a;Ljava/lang/String;Ljava/lang/String;ZILjava/lang/Object;)V

    invoke-virtual/range {p0 .. p0}, Landroidx/fragment/app/FragmentActivity;->getSupportFragmentManager()Landroidx/fragment/app/FragmentManager;

    move-result-object v5

    invoke-virtual {v3}, Lcom/transsion/home/<USER>/BottomTabItem;->getBtTabCode()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v5, v3}, Landroidx/fragment/app/FragmentManager;->findFragmentByTag(Ljava/lang/String;)Landroidx/fragment/app/Fragment;

    move-result-object v3

    instance-of v5, v3, Lcom/transsion/postdetail/shorttv/ShortTVHomeFragment;

    if-eqz v5, :cond_3

    check-cast v3, Lcom/transsion/postdetail/shorttv/ShortTVHomeFragment;

    :goto_3
    move-object v5, v3

    goto/16 :goto_5

    :cond_3
    new-instance v3, Lcom/transsion/postdetail/shorttv/ShortTVHomeFragment;

    invoke-direct {v3}, Lcom/transsion/postdetail/shorttv/ShortTVHomeFragment;-><init>()V

    goto :goto_3

    :cond_4
    sget-object v6, Lcom/transsion/home/<USER>/BottomTabType;->OPERATION:Lcom/transsion/home/<USER>/BottomTabType;

    invoke-virtual {v6}, Lcom/transsion/home/<USER>/BottomTabType;->getValue()Ljava/lang/String;

    move-result-object v6

    invoke-static {v5, v6}, Lkotlin/jvm/internal/Intrinsics;->b(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v6

    if-eqz v6, :cond_7

    sget-object v7, Lxi/b;->a:Lxi/b$a;

    const-string v8, "MainActivity"

    invoke-virtual {v3}, Lcom/transsion/home/<USER>/BottomTabItem;->getOperateTabId()Ljava/lang/Integer;

    move-result-object v5

    new-instance v6, Ljava/lang/StringBuilder;

    invoke-direct {v6}, Ljava/lang/StringBuilder;-><init>()V

    const-string v9, "initFragment() --> \u5f00\u59cb\u521b\u5efaFragment\u4e86 --> operateTab:"

    invoke-virtual {v6, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v6, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    invoke-virtual {v6}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v9

    const/4 v10, 0x0

    const/4 v11, 0x4

    const/4 v12, 0x0

    invoke-static/range {v7 .. v12}, Lxi/b$a;->f(Lxi/b$a;Ljava/lang/String;Ljava/lang/String;ZILjava/lang/Object;)V

    invoke-virtual/range {p0 .. p0}, Landroidx/fragment/app/FragmentActivity;->getSupportFragmentManager()Landroidx/fragment/app/FragmentManager;

    move-result-object v5

    invoke-virtual {v3}, Lcom/transsion/home/<USER>/BottomTabItem;->getBtTabCode()Ljava/lang/String;

    move-result-object v6

    invoke-virtual {v5, v6}, Landroidx/fragment/app/FragmentManager;->findFragmentByTag(Ljava/lang/String;)Landroidx/fragment/app/Fragment;

    move-result-object v5

    instance-of v6, v5, Lcom/transsion/home/<USER>/tab/BottomOpFragment;

    if-eqz v6, :cond_5

    check-cast v5, Lcom/transsion/home/<USER>/tab/BottomOpFragment;

    goto/16 :goto_5

    :cond_5
    sget-object v5, Lcom/transsion/home/<USER>/tab/BottomOpFragment;->c:Lcom/transsion/home/<USER>/tab/BottomOpFragment$a;

    invoke-virtual {v3}, Lcom/transsion/home/<USER>/BottomTabItem;->getOperateTabId()Ljava/lang/Integer;

    move-result-object v6

    if-eqz v6, :cond_6

    invoke-virtual {v6}, Ljava/lang/Integer;->intValue()I

    move-result v6

    goto :goto_4

    :cond_6
    sget-object v6, Lcom/transsion/home/<USER>/HomeTabId;->MusicOperate:Lcom/transsion/home/<USER>/HomeTabId;

    invoke-virtual {v6}, Lcom/transsion/home/<USER>/HomeTabId;->getValue()I

    move-result v6

    :goto_4
    invoke-virtual {v3}, Lcom/transsion/home/<USER>/BottomTabItem;->getBtTabCode()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v5, v6, v3}, Lcom/transsion/home/<USER>/tab/BottomOpFragment$a;->a(ILjava/lang/String;)Lcom/transsion/home/<USER>/tab/BottomOpFragment;

    move-result-object v3

    goto :goto_3

    :cond_7
    sget-object v6, Lcom/transsion/home/<USER>/BottomTabType;->PREMIUM:Lcom/transsion/home/<USER>/BottomTabType;

    invoke-virtual {v6}, Lcom/transsion/home/<USER>/BottomTabType;->getValue()Ljava/lang/String;

    move-result-object v6

    invoke-static {v5, v6}, Lkotlin/jvm/internal/Intrinsics;->b(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v6

    if-eqz v6, :cond_9

    sget-object v7, Lxi/b;->a:Lxi/b$a;

    const-string v8, "MainActivity"

    const-string v9, "initFragment() --> \u5f00\u59cb\u521b\u5efaFragment\u4e86 --> memberFragment"

    const/4 v10, 0x0

    const/4 v11, 0x4

    const/4 v12, 0x0

    invoke-static/range {v7 .. v12}, Lxi/b$a;->f(Lxi/b$a;Ljava/lang/String;Ljava/lang/String;ZILjava/lang/Object;)V

    invoke-virtual/range {p0 .. p0}, Landroidx/fragment/app/FragmentActivity;->getSupportFragmentManager()Landroidx/fragment/app/FragmentManager;

    move-result-object v5

    invoke-virtual {v3}, Lcom/transsion/home/<USER>/BottomTabItem;->getBtTabCode()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v5, v3}, Landroidx/fragment/app/FragmentManager;->findFragmentByTag(Ljava/lang/String;)Landroidx/fragment/app/Fragment;

    move-result-object v3

    const-class v5, Lcom/transsion/memberapi/IMemberApi;

    if-eqz v3, :cond_8

    invoke-static {}, Lcom/alibaba/android/arouter/launcher/a;->d()Lcom/alibaba/android/arouter/launcher/a;

    move-result-object v6

    invoke-virtual {v6, v5}, Lcom/alibaba/android/arouter/launcher/a;->h(Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v6

    check-cast v6, Lcom/transsion/memberapi/IMemberApi;

    invoke-interface {v6, v3}, Lcom/transsion/memberapi/IMemberApi;->z(Landroidx/fragment/app/Fragment;)Z

    move-result v6

    if-eqz v6, :cond_8

    goto/16 :goto_3

    :cond_8
    invoke-static {}, Lcom/alibaba/android/arouter/launcher/a;->d()Lcom/alibaba/android/arouter/launcher/a;

    move-result-object v3

    invoke-virtual {v3, v5}, Lcom/alibaba/android/arouter/launcher/a;->h(Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lcom/transsion/memberapi/IMemberApi;

    invoke-interface {v3}, Lcom/transsion/memberapi/IMemberApi;->o0()Landroidx/fragment/app/Fragment;

    move-result-object v3

    goto/16 :goto_3

    :cond_9
    sget-object v6, Lcom/transsion/home/<USER>/BottomTabType;->DOWNLOAD:Lcom/transsion/home/<USER>/BottomTabType;

    invoke-virtual {v6}, Lcom/transsion/home/<USER>/BottomTabType;->getValue()Ljava/lang/String;

    move-result-object v6

    invoke-static {v5, v6}, Lkotlin/jvm/internal/Intrinsics;->b(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v6

    const/4 v7, 0x0

    if-eqz v6, :cond_b

    sget-object v8, Lxi/b;->a:Lxi/b$a;

    const-string v9, "MainActivity"

    const-string v10, "initFragment() --> \u5f00\u59cb\u521b\u5efaFragment\u4e86 --> fileManagerFragment"

    const/4 v11, 0x0

    const/4 v12, 0x4

    const/4 v13, 0x0

    invoke-static/range {v8 .. v13}, Lxi/b$a;->f(Lxi/b$a;Ljava/lang/String;Ljava/lang/String;ZILjava/lang/Object;)V

    invoke-virtual/range {p0 .. p0}, Landroidx/fragment/app/FragmentActivity;->getSupportFragmentManager()Landroidx/fragment/app/FragmentManager;

    move-result-object v5

    invoke-virtual {v3}, Lcom/transsion/home/<USER>/BottomTabItem;->getBtTabCode()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v5, v3}, Landroidx/fragment/app/FragmentManager;->findFragmentByTag(Ljava/lang/String;)Landroidx/fragment/app/Fragment;

    move-result-object v3

    instance-of v5, v3, Lcom/transsnet/downloader/fragment/DownloadMainFragment;

    if-eqz v5, :cond_a

    check-cast v3, Lcom/transsnet/downloader/fragment/DownloadMainFragment;

    goto/16 :goto_3

    :cond_a
    sget-object v3, Lcom/transsnet/downloader/fragment/DownloadMainFragment;->e:Lcom/transsnet/downloader/fragment/DownloadMainFragment$a;

    invoke-virtual {v3, v7}, Lcom/transsnet/downloader/fragment/DownloadMainFragment$a;->a(I)Lcom/transsnet/downloader/fragment/DownloadMainFragment;

    move-result-object v3

    goto/16 :goto_3

    :cond_b
    sget-object v6, Lcom/transsion/home/<USER>/BottomTabType;->H5TAB:Lcom/transsion/home/<USER>/BottomTabType;

    invoke-virtual {v6}, Lcom/transsion/home/<USER>/BottomTabType;->getValue()Ljava/lang/String;

    move-result-object v6

    invoke-static {v5, v6}, Lkotlin/jvm/internal/Intrinsics;->b(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v6

    const/4 v8, 0x2

    const-string v9, "tool_bar_hidden"

    const-string v10, "url"

    if-eqz v6, :cond_d

    sget-object v11, Lxi/b;->a:Lxi/b$a;

    const-string v12, "MainActivity"

    invoke-virtual {v3}, Lcom/transsion/home/<USER>/BottomTabItem;->getUrl()Ljava/lang/String;

    move-result-object v5

    new-instance v6, Ljava/lang/StringBuilder;

    invoke-direct {v6}, Ljava/lang/StringBuilder;-><init>()V

    const-string v13, "initFragment() --> \u5f00\u59cb\u521b\u5efaFragment\u4e86 --> h5fragment --> "

    invoke-virtual {v6, v13}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v6, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v6}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v13

    const/4 v14, 0x0

    const/4 v15, 0x4

    const/16 v16, 0x0

    invoke-static/range {v11 .. v16}, Lxi/b$a;->f(Lxi/b$a;Ljava/lang/String;Ljava/lang/String;ZILjava/lang/Object;)V

    invoke-virtual/range {p0 .. p0}, Landroidx/fragment/app/FragmentActivity;->getSupportFragmentManager()Landroidx/fragment/app/FragmentManager;

    move-result-object v5

    invoke-virtual {v3}, Lcom/transsion/home/<USER>/BottomTabItem;->getBtTabCode()Ljava/lang/String;

    move-result-object v6

    invoke-virtual {v5, v6}, Landroidx/fragment/app/FragmentManager;->findFragmentByTag(Ljava/lang/String;)Landroidx/fragment/app/Fragment;

    move-result-object v5

    instance-of v6, v5, Lcom/transsion/web/fragment/WebFragment;

    if-eqz v6, :cond_c

    check-cast v5, Lcom/transsion/web/fragment/WebFragment;

    goto/16 :goto_5

    :cond_c
    sget-object v5, Lcom/transsion/web/fragment/WebFragment;->t:Lcom/transsion/web/fragment/WebFragment$a;

    invoke-virtual {v5}, Lcom/transsion/web/fragment/WebFragment$a;->b()Lcom/transsion/web/fragment/WebFragment;

    move-result-object v5

    const/4 v6, 0x4

    new-array v6, v6, [Lkotlin/Pair;

    invoke-virtual {v3}, Lcom/transsion/home/<USER>/BottomTabItem;->getUrl()Ljava/lang/String;

    move-result-object v11

    invoke-static {v10, v11}, Lkotlin/TuplesKt;->a(Ljava/lang/Object;Ljava/lang/Object;)Lkotlin/Pair;

    move-result-object v10

    aput-object v10, v6, v7

    sget-object v7, Ljava/lang/Boolean;->TRUE:Ljava/lang/Boolean;

    invoke-static {v9, v7}, Lkotlin/TuplesKt;->a(Ljava/lang/Object;Ljava/lang/Object;)Lkotlin/Pair;

    move-result-object v7

    aput-object v7, v6, v4

    const-string v7, "tab_code"

    invoke-virtual {v3}, Lcom/transsion/home/<USER>/BottomTabItem;->getBtTabCode()Ljava/lang/String;

    move-result-object v3

    invoke-static {v7, v3}, Lkotlin/TuplesKt;->a(Ljava/lang/Object;Ljava/lang/Object;)Lkotlin/Pair;

    move-result-object v3

    aput-object v3, v6, v8

    invoke-virtual/range {p0 .. p0}, Landroidx/appcompat/app/AppCompatActivity;->getResources()Landroid/content/res/Resources;

    move-result-object v3

    sget v7, Lcom/tn/lib/widget/R$dimen;->tab_bottom_show_height:I

    invoke-virtual {v3, v7}, Landroid/content/res/Resources;->getDimension(I)F

    move-result v3

    float-to-int v3, v3

    invoke-static {v3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v3

    const-string v7, "bottom_margin"

    invoke-static {v7, v3}, Lkotlin/TuplesKt;->a(Ljava/lang/Object;Ljava/lang/Object;)Lkotlin/Pair;

    move-result-object v3

    const/4 v7, 0x3

    aput-object v3, v6, v7

    invoke-static {v6}, Landroidx/core/os/b;->b([Lkotlin/Pair;)Landroid/os/Bundle;

    move-result-object v3

    invoke-virtual {v5, v3}, Landroidx/fragment/app/Fragment;->setArguments(Landroid/os/Bundle;)V

    goto :goto_5

    :cond_d
    sget-object v6, Lcom/transsion/home/<USER>/BottomTabType;->ME:Lcom/transsion/home/<USER>/BottomTabType;

    invoke-virtual {v6}, Lcom/transsion/home/<USER>/BottomTabType;->getValue()Ljava/lang/String;

    move-result-object v6

    invoke-static {v5, v6}, Lkotlin/jvm/internal/Intrinsics;->b(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v5

    if-eqz v5, :cond_f

    sget-object v11, Lxi/b;->a:Lxi/b$a;

    const-string v12, "MainActivity"

    const-string v13, "initFragment() --> \u5f00\u59cb\u521b\u5efaFragment\u4e86 --> profileFragment"

    const/4 v14, 0x0

    const/4 v15, 0x4

    const/16 v16, 0x0

    invoke-static/range {v11 .. v16}, Lxi/b$a;->f(Lxi/b$a;Ljava/lang/String;Ljava/lang/String;ZILjava/lang/Object;)V

    invoke-virtual/range {p0 .. p0}, Landroidx/fragment/app/FragmentActivity;->getSupportFragmentManager()Landroidx/fragment/app/FragmentManager;

    move-result-object v5

    invoke-virtual {v3}, Lcom/transsion/home/<USER>/BottomTabItem;->getBtTabCode()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v5, v3}, Landroidx/fragment/app/FragmentManager;->findFragmentByTag(Ljava/lang/String;)Landroidx/fragment/app/Fragment;

    move-result-object v3

    instance-of v5, v3, Lcom/transsion/usercenter/me/MeFragment;

    if-eqz v5, :cond_e

    check-cast v3, Lcom/transsion/usercenter/me/MeFragment;

    goto/16 :goto_3

    :cond_e
    sget-object v3, Lcom/transsion/usercenter/me/MeFragment;->e:Lcom/transsion/usercenter/me/MeFragment$a;

    invoke-virtual {v3}, Lcom/transsion/usercenter/me/MeFragment$a;->a()Lcom/transsion/usercenter/me/MeFragment;

    move-result-object v3

    goto/16 :goto_3

    :cond_f
    sget-object v3, Lcom/transsion/web/fragment/WebFragment;->t:Lcom/transsion/web/fragment/WebFragment$a;

    invoke-virtual {v3}, Lcom/transsion/web/fragment/WebFragment$a;->b()Lcom/transsion/web/fragment/WebFragment;

    move-result-object v5

    new-array v3, v8, [Lkotlin/Pair;

    const-string v6, "https://moviebox.ng/home"

    invoke-static {v10, v6}, Lkotlin/TuplesKt;->a(Ljava/lang/Object;Ljava/lang/Object;)Lkotlin/Pair;

    move-result-object v6

    aput-object v6, v3, v7

    sget-object v6, Ljava/lang/Boolean;->TRUE:Ljava/lang/Boolean;

    invoke-static {v9, v6}, Lkotlin/TuplesKt;->a(Ljava/lang/Object;Ljava/lang/Object;)Lkotlin/Pair;

    move-result-object v6

    aput-object v6, v3, v4

    invoke-static {v3}, Landroidx/core/os/b;->b([Lkotlin/Pair;)Landroid/os/Bundle;

    move-result-object v3

    invoke-virtual {v5, v3}, Landroidx/fragment/app/Fragment;->setArguments(Landroid/os/Bundle;)V

    :goto_5
    const-string v3, "when (bottomTabItem.btTa\u2026          }\n            }"

    invoke-static {v5, v3}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    iget-object v3, v0, Lcom/transsion/subroom/activity/MainActivity;->c:Ljava/util/List;

    invoke-interface {v3, v5}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto/16 :goto_0

    :cond_10
    iget-object v2, v0, Lcom/transsion/subroom/activity/MainActivity;->c:Ljava/util/List;

    iget v3, v0, Lcom/transsion/subroom/activity/MainActivity;->f:I

    invoke-interface {v2, v3}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Landroidx/fragment/app/Fragment;

    if-eqz v1, :cond_11

    iget v3, v0, Lcom/transsion/subroom/activity/MainActivity;->f:I

    invoke-interface {v1, v3}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/transsion/home/<USER>/BottomTabItem;

    if-eqz v1, :cond_11

    invoke-virtual {v1}, Lcom/transsion/home/<USER>/BottomTabItem;->getBtTabCode()Ljava/lang/String;

    move-result-object v1

    if-nez v1, :cond_12

    :cond_11
    const-string v1, ""

    :cond_12
    invoke-virtual {v0, v2, v1}, Lcom/transsion/subroom/activity/MainActivity;->q0(Landroidx/fragment/app/Fragment;Ljava/lang/String;)V

    return-void
.end method

.method public final b0()V
    .locals 6

    sget-object v0, Lxi/b;->a:Lxi/b$a;

    const-string v1, "MainActivity"

    const-string v2, "initTabs() --> \u5f00\u59cb\u521b\u5efaTab\u4e86"

    const/4 v3, 0x0

    const/4 v4, 0x4

    const/4 v5, 0x0

    invoke-static/range {v0 .. v5}, Lxi/b$a;->f(Lxi/b$a;Ljava/lang/String;Ljava/lang/String;ZILjava/lang/Object;)V

    invoke-virtual {p0}, Lcom/transsion/baseui/activity/BaseCommonActivity;->getMViewBinding()Ls4/a;

    move-result-object v0

    check-cast v0, Luq/b;

    iget-object v0, v0, Luq/b;->j:Lcom/google/android/material/tabs/TabLayout;

    invoke-static {p0}, Lcom/transsion/baseui/util/b;->a(Landroid/content/Context;)Z

    move-result v1

    if-eqz v1, :cond_0

    const/4 v1, 0x2

    goto :goto_0

    :cond_0
    const/4 v1, 0x1

    :goto_0
    invoke-virtual {v0, v1}, Lcom/google/android/material/tabs/TabLayout;->setTabMode(I)V

    invoke-virtual {p0}, Lcom/transsion/baseui/activity/BaseCommonActivity;->getMViewBinding()Ls4/a;

    move-result-object v0

    check-cast v0, Luq/b;

    iget-object v0, v0, Luq/b;->j:Lcom/google/android/material/tabs/TabLayout;

    new-instance v1, Lcom/transsion/subroom/activity/MainActivity$d;

    invoke-direct {v1, p0}, Lcom/transsion/subroom/activity/MainActivity$d;-><init>(Lcom/transsion/subroom/activity/MainActivity;)V

    invoke-virtual {v0, v1}, Lcom/google/android/material/tabs/TabLayout;->addOnTabSelectedListener(Lcom/google/android/material/tabs/TabLayout$OnTabSelectedListener;)V

    invoke-virtual {p0}, Lcom/transsion/subroom/activity/MainActivity;->g0()V

    sget-object v0, Lcom/transsion/home/<USER>/preload/PreloadTrendingData;->o:Lcom/transsion/home/<USER>/preload/PreloadTrendingData$a;

    invoke-virtual {v0}, Lcom/transsion/home/<USER>/preload/PreloadTrendingData$a;->a()Lcom/transsion/home/<USER>/preload/PreloadTrendingData;

    move-result-object v1

    invoke-virtual {v1}, Lcom/transsion/home/<USER>/preload/PreloadTrendingData;->v()Landroidx/lifecycle/c0;

    move-result-object v1

    new-instance v2, Lcom/transsion/subroom/activity/MainActivity$initTabs$2;

    invoke-direct {v2, p0}, Lcom/transsion/subroom/activity/MainActivity$initTabs$2;-><init>(Lcom/transsion/subroom/activity/MainActivity;)V

    new-instance v3, Lcom/transsion/subroom/activity/MainActivity$f;

    invoke-direct {v3, v2}, Lcom/transsion/subroom/activity/MainActivity$f;-><init>(Lkotlin/jvm/functions/Function1;)V

    invoke-virtual {v1, p0, v3}, Landroidx/lifecycle/LiveData;->j(Landroidx/lifecycle/u;Landroidx/lifecycle/d0;)V

    invoke-virtual {v0}, Lcom/transsion/home/<USER>/preload/PreloadTrendingData$a;->a()Lcom/transsion/home/<USER>/preload/PreloadTrendingData;

    move-result-object v1

    invoke-virtual {v1}, Lcom/transsion/home/<USER>/preload/PreloadTrendingData;->w()Landroidx/lifecycle/c0;

    move-result-object v1

    new-instance v2, Lcom/transsion/subroom/activity/MainActivity$initTabs$3;

    invoke-direct {v2, p0}, Lcom/transsion/subroom/activity/MainActivity$initTabs$3;-><init>(Lcom/transsion/subroom/activity/MainActivity;)V

    new-instance v3, Lcom/transsion/subroom/activity/MainActivity$f;

    invoke-direct {v3, v2}, Lcom/transsion/subroom/activity/MainActivity$f;-><init>(Lkotlin/jvm/functions/Function1;)V

    invoke-virtual {v1, p0, v3}, Landroidx/lifecycle/LiveData;->j(Landroidx/lifecycle/u;Landroidx/lifecycle/d0;)V

    invoke-virtual {v0}, Lcom/transsion/home/<USER>/preload/PreloadTrendingData$a;->a()Lcom/transsion/home/<USER>/preload/PreloadTrendingData;

    move-result-object v1

    invoke-virtual {v1}, Lcom/transsion/home/<USER>/preload/PreloadTrendingData;->H()Z

    move-result v1

    if-nez v1, :cond_1

    invoke-virtual {v0}, Lcom/transsion/home/<USER>/preload/PreloadTrendingData$a;->a()Lcom/transsion/home/<USER>/preload/PreloadTrendingData;

    move-result-object v0

    invoke-virtual {v0}, Lcom/transsion/home/<USER>/preload/PreloadTrendingData;->s()V

    :cond_1
    return-void
.end method

.method public final c0()V
    .locals 2

    sget-object v0, Lcom/transsion/home/<USER>/BottomTabType;->ME:Lcom/transsion/home/<USER>/BottomTabType;

    invoke-virtual {v0}, Lcom/transsion/home/<USER>/BottomTabType;->getValue()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p0, v0}, Lcom/transsion/subroom/activity/MainActivity;->W(Ljava/lang/String;)I

    move-result v0

    invoke-virtual {p0}, Lcom/transsion/baseui/activity/BaseCommonActivity;->getMViewBinding()Ls4/a;

    move-result-object v1

    check-cast v1, Luq/b;

    iget-object v1, v1, Luq/b;->j:Lcom/google/android/material/tabs/TabLayout;

    invoke-virtual {v1, v0}, Lcom/google/android/material/tabs/TabLayout;->getTabAt(I)Lcom/google/android/material/tabs/TabLayout$Tab;

    move-result-object v0

    if-eqz v0, :cond_0

    iget-object v0, v0, Lcom/google/android/material/tabs/TabLayout$Tab;->view:Lcom/google/android/material/tabs/TabLayout$TabView;

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    if-eqz v0, :cond_1

    new-instance v1, Lcom/transsion/subroom/activity/g;

    invoke-direct {v1, p0, v0}, Lcom/transsion/subroom/activity/g;-><init>(Lcom/transsion/subroom/activity/MainActivity;Lcom/google/android/material/tabs/TabLayout$TabView;)V

    invoke-virtual {v0, v1}, Landroid/view/View;->post(Ljava/lang/Runnable;)Z

    :cond_1
    return-void
.end method

.method public changeStatusFontColor(ZZ)V
    .locals 0

    if-eqz p2, :cond_0

    iput-boolean p1, p0, Lcom/transsion/subroom/activity/MainActivity;->s:Z

    :cond_0
    invoke-super {p0, p1, p2}, Lcom/transsion/baseui/activity/BaseActivity;->changeStatusFontColor(ZZ)V

    return-void
.end method

.method public d()Lcom/transsion/home/<USER>/b;
    .locals 1

    iget-object v0, p0, Lcom/transsion/subroom/activity/MainActivity;->m:Lcom/transsion/home/<USER>/b;

    return-object v0
.end method

.method public dispatchTouchEvent(Landroid/view/MotionEvent;)Z
    .locals 1

    if-eqz p1, :cond_0

    iget-boolean v0, p0, Lcom/transsion/subroom/activity/MainActivity;->u:Z

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/transsion/subroom/activity/MainActivity;->t:Landroid/view/GestureDetector;

    if-eqz v0, :cond_0

    invoke-virtual {v0, p1}, Landroid/view/GestureDetector;->onTouchEvent(Landroid/view/MotionEvent;)Z

    :cond_0
    invoke-super {p0, p1}, Lcom/transsion/baseui/activity/BaseActivity;->dispatchTouchEvent(Landroid/view/MotionEvent;)Z

    move-result p1

    return p1
.end method

.method public final e0()V
    .locals 6

    sget-object v0, Lxi/b;->a:Lxi/b$a;

    const-string v1, "MainActivity"

    const-string v2, "onCreateNext() --> \u9996\u9875\u5f00\u59cb\u521d\u59cb\u5316\u4e86"

    const/4 v3, 0x0

    const/4 v4, 0x4

    const/4 v5, 0x0

    invoke-static/range {v0 .. v5}, Lxi/b$a;->f(Lxi/b$a;Ljava/lang/String;Ljava/lang/String;ZILjava/lang/Object;)V

    invoke-virtual {p0}, Lcom/transsion/subroom/activity/MainActivity;->b0()V

    invoke-direct {p0}, Lcom/transsion/subroom/activity/MainActivity;->initPlayer()V

    invoke-virtual {p0}, Lcom/transsion/baseui/activity/BaseCommonActivity;->getMViewBinding()Ls4/a;

    move-result-object v0

    check-cast v0, Luq/b;

    iget-object v0, v0, Luq/b;->c:Landroid/widget/FrameLayout;

    sget v1, Lcom/tn/lib/widget/R$color;->bg_04:I

    invoke-virtual {v0, v1}, Landroid/view/View;->setBackgroundResource(I)V

    invoke-virtual {p0}, Lcom/transsion/subroom/activity/MainActivity;->i0()V

    sget-object v0, Lpp/c;->a:Lpp/c;

    sget-object v1, Lcom/transsion/push/notification/permission/NoticePermissionFrom;->APP_START:Lcom/transsion/push/notification/permission/NoticePermissionFrom;

    invoke-virtual {v0, p0, v1}, Lpp/c;->d(Landroidx/fragment/app/FragmentActivity;Lcom/transsion/push/notification/permission/NoticePermissionFrom;)Z

    invoke-static {}, Lcom/alibaba/android/arouter/launcher/a;->d()Lcom/alibaba/android/arouter/launcher/a;

    move-result-object v0

    const-class v1, Lcom/transsion/memberapi/IMemberApi;

    invoke-virtual {v0, v1}, Lcom/alibaba/android/arouter/launcher/a;->h(Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/transsion/memberapi/IMemberApi;

    invoke-interface {v0}, Lcom/transsion/memberapi/IMemberApi;->o()V

    invoke-static {}, Lcom/alibaba/android/arouter/launcher/a;->d()Lcom/alibaba/android/arouter/launcher/a;

    move-result-object v0

    const-class v1, Lcom/transsion/commercializationapi/ICommonDialogApi;

    invoke-virtual {v0, v1}, Lcom/alibaba/android/arouter/launcher/a;->h(Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/transsion/commercializationapi/ICommonDialogApi;

    invoke-interface {v0}, Lcom/transsion/commercializationapi/ICommonDialogApi;->w0()V

    sget-object v0, Lcom/transsion/videofloat/VideoPipManager;->a:Lcom/transsion/videofloat/VideoPipManager$Companion;

    invoke-virtual {v0}, Lcom/transsion/videofloat/VideoPipManager$Companion;->a()Lcom/transsion/videofloat/VideoPipManager;

    move-result-object v0

    invoke-interface {v0}, Lcom/transsion/videofloat/VideoPipManager;->e()V

    return-void
.end method

.method public final f0(Lcom/google/android/material/tabs/TabLayout$Tab;)V
    .locals 8

    if-nez p1, :cond_0

    return-void

    :cond_0
    invoke-static {}, Lcom/alibaba/android/arouter/launcher/a;->d()Lcom/alibaba/android/arouter/launcher/a;

    move-result-object v0

    const-class v1, Lcom/transsion/memberapi/IMemberApi;

    invoke-virtual {v0, v1}, Lcom/alibaba/android/arouter/launcher/a;->h(Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/transsion/memberapi/IMemberApi;

    invoke-interface {v0}, Lcom/transsion/memberapi/IMemberApi;->B1()V

    iget-object v0, p0, Lcom/transsion/subroom/activity/MainActivity;->c:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    invoke-virtual {p1}, Lcom/google/android/material/tabs/TabLayout$Tab;->getPosition()I

    move-result v1

    if-ltz v1, :cond_e

    if-ge v1, v0, :cond_e

    iget-object v0, p0, Lcom/transsion/subroom/activity/MainActivity;->n:Lcom/transsion/home/<USER>/AppTab;

    const/4 v1, 0x0

    if-eqz v0, :cond_1

    invoke-virtual {v0}, Lcom/transsion/home/<USER>/AppTab;->getBottomTabs()Ljava/util/List;

    move-result-object v0

    if-eqz v0, :cond_1

    invoke-virtual {p1}, Lcom/google/android/material/tabs/TabLayout$Tab;->getPosition()I

    move-result v2

    invoke-interface {v0, v2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/transsion/home/<USER>/BottomTabItem;

    goto :goto_0

    :cond_1
    move-object v0, v1

    :goto_0
    if-eqz v0, :cond_2

    invoke-virtual {v0}, Lcom/transsion/home/<USER>/BottomTabItem;->getBtTabCode()Ljava/lang/String;

    move-result-object v2

    goto :goto_1

    :cond_2
    move-object v2, v1

    :goto_1
    iget-object v3, p0, Lcom/transsion/subroom/activity/MainActivity;->q:Ljava/lang/String;

    invoke-static {v2, v3}, Lkotlin/jvm/internal/Intrinsics;->b(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v2

    const/4 v3, 0x1

    const/4 v4, 0x0

    if-eqz v2, :cond_4

    iput-boolean v4, p0, Lcom/transsion/subroom/activity/MainActivity;->p:Z

    sget-object v2, Lcom/transsion/baselib/report/launch/RoomAppMMKV;->a:Lcom/transsion/baselib/report/launch/RoomAppMMKV;

    invoke-virtual {v2}, Lcom/transsion/baselib/report/launch/RoomAppMMKV;->a()Lcom/tencent/mmkv/MMKV;

    move-result-object v5

    iget-object v6, p0, Lcom/transsion/subroom/activity/MainActivity;->n:Lcom/transsion/home/<USER>/AppTab;

    if-eqz v6, :cond_3

    invoke-virtual {v6}, Lcom/transsion/home/<USER>/AppTab;->getBadgeVer()Ljava/lang/String;

    move-result-object v6

    goto :goto_2

    :cond_3
    move-object v6, v1

    :goto_2
    const-string v7, "tab_hot_tips_version"

    invoke-virtual {v5, v7, v6}, Lcom/tencent/mmkv/MMKV;->putString(Ljava/lang/String;Ljava/lang/String;)Landroid/content/SharedPreferences$Editor;

    invoke-virtual {v2}, Lcom/transsion/baselib/report/launch/RoomAppMMKV;->a()Lcom/tencent/mmkv/MMKV;

    move-result-object v2

    const-string v5, "tab_hot_tips_is_show"

    invoke-virtual {v2, v5, v3}, Lcom/tencent/mmkv/MMKV;->putBoolean(Ljava/lang/String;Z)Landroid/content/SharedPreferences$Editor;

    :cond_4
    invoke-virtual {p0, v0}, Lcom/transsion/subroom/activity/MainActivity;->h0(Lcom/transsion/home/<USER>/BottomTabItem;)V

    invoke-virtual {p1}, Lcom/google/android/material/tabs/TabLayout$Tab;->getPosition()I

    move-result v2

    iput v2, p0, Lcom/transsion/subroom/activity/MainActivity;->f:I

    iget-object v2, p0, Lcom/transsion/subroom/activity/MainActivity;->c:Ljava/util/List;

    invoke-virtual {p1}, Lcom/google/android/material/tabs/TabLayout$Tab;->getPosition()I

    move-result p1

    invoke-interface {v2, p1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Landroidx/fragment/app/Fragment;

    if-eqz v0, :cond_5

    invoke-virtual {v0}, Lcom/transsion/home/<USER>/BottomTabItem;->getBtTabCode()Ljava/lang/String;

    move-result-object v2

    if-nez v2, :cond_6

    :cond_5
    const-string v2, ""

    :cond_6
    invoke-virtual {p0, p1, v2}, Lcom/transsion/subroom/activity/MainActivity;->q0(Landroidx/fragment/app/Fragment;Ljava/lang/String;)V

    if-eqz v0, :cond_7

    invoke-virtual {v0}, Lcom/transsion/home/<USER>/BottomTabItem;->getBtTabType()Ljava/lang/String;

    move-result-object p1

    goto :goto_3

    :cond_7
    move-object p1, v1

    :goto_3
    sget-object v2, Lcom/transsion/home/<USER>/BottomTabType;->HOME:Lcom/transsion/home/<USER>/BottomTabType;

    invoke-virtual {v2}, Lcom/transsion/home/<USER>/BottomTabType;->getValue()Ljava/lang/String;

    move-result-object v5

    invoke-static {p1, v5}, Lkotlin/jvm/internal/Intrinsics;->b(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result p1

    const/4 v5, 0x2

    if-eqz p1, :cond_8

    iget-boolean p1, p0, Lcom/transsion/subroom/activity/MainActivity;->s:Z

    invoke-static {p0, p1, v4, v5, v1}, Lcom/transsion/baseui/activity/BaseActivity;->changeStatusFontColor$default(Lcom/transsion/baseui/activity/BaseActivity;ZZILjava/lang/Object;)V

    goto :goto_5

    :cond_8
    if-eqz v0, :cond_9

    invoke-virtual {v0}, Lcom/transsion/home/<USER>/BottomTabItem;->getStatusWhite()Z

    move-result p1

    if-ne p1, v3, :cond_9

    const/4 p1, 0x1

    goto :goto_4

    :cond_9
    const/4 p1, 0x0

    :goto_4
    invoke-static {p0, p1, v4, v5, v1}, Lcom/transsion/baseui/activity/BaseActivity;->changeStatusFontColor$default(Lcom/transsion/baseui/activity/BaseActivity;ZZILjava/lang/Object;)V

    :goto_5
    if-eqz v0, :cond_a

    invoke-virtual {v0}, Lcom/transsion/home/<USER>/BottomTabItem;->getBtTabType()Ljava/lang/String;

    move-result-object p1

    goto :goto_6

    :cond_a
    move-object p1, v1

    :goto_6
    invoke-virtual {v2}, Lcom/transsion/home/<USER>/BottomTabType;->getValue()Ljava/lang/String;

    move-result-object v2

    invoke-static {p1, v2}, Lkotlin/jvm/internal/Intrinsics;->b(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result p1

    if-nez p1, :cond_c

    if-eqz v0, :cond_b

    invoke-virtual {v0}, Lcom/transsion/home/<USER>/BottomTabItem;->getBtTabType()Ljava/lang/String;

    move-result-object p1

    goto :goto_7

    :cond_b
    move-object p1, v1

    :goto_7
    sget-object v2, Lcom/transsion/home/<USER>/BottomTabType;->PREMIUM:Lcom/transsion/home/<USER>/BottomTabType;

    invoke-virtual {v2}, Lcom/transsion/home/<USER>/BottomTabType;->getValue()Ljava/lang/String;

    move-result-object v2

    invoke-static {p1, v2}, Lkotlin/jvm/internal/Intrinsics;->b(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result p1

    if-nez p1, :cond_c

    const/4 p1, 0x0

    invoke-static {p0, p1, v3, v1}, Lcom/transsion/subroom/activity/MainActivity;->R(Lcom/transsion/subroom/activity/MainActivity;FILjava/lang/Object;)V

    :cond_c
    if-eqz v0, :cond_d

    invoke-virtual {v0}, Lcom/transsion/home/<USER>/BottomTabItem;->getBtTabType()Ljava/lang/String;

    move-result-object v1

    :cond_d
    sget-object p1, Lcom/transsion/home/<USER>/BottomTabType;->DOWNLOAD:Lcom/transsion/home/<USER>/BottomTabType;

    invoke-virtual {p1}, Lcom/transsion/home/<USER>/BottomTabType;->getValue()Ljava/lang/String;

    move-result-object p1

    invoke-static {v1, p1}, Lkotlin/jvm/internal/Intrinsics;->b(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_e

    const/4 p1, 0x6

    invoke-virtual {p0, p1, v4}, Lcom/transsion/subroom/activity/MainActivity;->p0(II)V

    :cond_e
    return-void
.end method

.method public g()Z
    .locals 1

    iget-boolean v0, p0, Lcom/transsion/subroom/activity/MainActivity;->v:Z

    return v0
.end method

.method public final g0()V
    .locals 7

    sget-object v0, Lcom/transsion/home/<USER>/preload/PreloadTrendingData;->o:Lcom/transsion/home/<USER>/preload/PreloadTrendingData$a;

    invoke-virtual {v0}, Lcom/transsion/home/<USER>/preload/PreloadTrendingData$a;->a()Lcom/transsion/home/<USER>/preload/PreloadTrendingData;

    move-result-object v0

    invoke-virtual {v0}, Lcom/transsion/home/<USER>/preload/PreloadTrendingData;->A()Lcom/transsion/home/<USER>/AppTab;

    move-result-object v0

    iput-object v0, p0, Lcom/transsion/subroom/activity/MainActivity;->n:Lcom/transsion/home/<USER>/AppTab;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Lcom/transsion/home/<USER>/AppTab;->getBottomTabs()Ljava/util/List;

    move-result-object v0

    if-eqz v0, :cond_0

    sget-object v1, Lxi/b;->a:Lxi/b$a;

    const-string v2, "PreloadTrending"

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v3

    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    const-string v5, "bottomTab build from cache 1 "

    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4, v3}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    const/4 v4, 0x0

    const/4 v5, 0x4

    const/4 v6, 0x0

    invoke-static/range {v1 .. v6}, Lxi/b$a;->f(Lxi/b$a;Ljava/lang/String;Ljava/lang/String;ZILjava/lang/Object;)V

    invoke-virtual {p0, v0}, Lcom/transsion/subroom/activity/MainActivity;->O(Ljava/util/List;)V

    :cond_0
    return-void
.end method

.method public bridge synthetic getViewBinding()Ls4/a;
    .locals 1

    invoke-virtual {p0}, Lcom/transsion/subroom/activity/MainActivity;->X()Luq/b;

    move-result-object v0

    return-object v0
.end method

.method public final h0(Lcom/transsion/home/<USER>/BottomTabItem;)V
    .locals 6

    invoke-virtual {p0}, Lcom/transsion/baseui/activity/BaseCommonActivity;->getMViewBinding()Ls4/a;

    move-result-object v0

    check-cast v0, Luq/b;

    iget-object v0, v0, Luq/b;->j:Lcom/google/android/material/tabs/TabLayout;

    invoke-virtual {v0}, Lcom/google/android/material/tabs/TabLayout;->getTabCount()I

    move-result v1

    const/4 v2, 0x0

    :goto_0
    if-ge v2, v1, :cond_2

    iget-object v3, p0, Lcom/transsion/subroom/activity/MainActivity;->n:Lcom/transsion/home/<USER>/AppTab;

    const/4 v4, 0x0

    if-eqz v3, :cond_0

    invoke-virtual {v3}, Lcom/transsion/home/<USER>/AppTab;->getBottomTabs()Ljava/util/List;

    move-result-object v3

    if-eqz v3, :cond_0

    invoke-interface {v3, v2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lcom/transsion/home/<USER>/BottomTabItem;

    goto :goto_1

    :cond_0
    move-object v3, v4

    :goto_1
    invoke-virtual {v0, v2}, Lcom/google/android/material/tabs/TabLayout;->getTabAt(I)Lcom/google/android/material/tabs/TabLayout$Tab;

    move-result-object v5

    if-eqz v5, :cond_1

    iget-object v4, v5, Lcom/google/android/material/tabs/TabLayout$Tab;->view:Lcom/google/android/material/tabs/TabLayout$TabView;

    :cond_1
    invoke-virtual {p0, v3, v4, p1}, Lcom/transsion/subroom/activity/MainActivity;->o0(Lcom/transsion/home/<USER>/BottomTabItem;Landroid/view/View;Lcom/transsion/home/<USER>/BottomTabItem;)V

    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_2
    return-void
.end method

.method public final i0()V
    .locals 7

    sget-object v0, Lcom/tn/lib/util/networkinfo/f;->a:Lcom/tn/lib/util/networkinfo/f;

    invoke-virtual {v0}, Lcom/tn/lib/util/networkinfo/f;->e()Z

    move-result v0

    if-eqz v0, :cond_0

    return-void

    :cond_0
    invoke-static {}, Lkotlinx/coroutines/w0;->c()Lkotlinx/coroutines/a2;

    move-result-object v0

    invoke-static {v0}, Lkotlinx/coroutines/l0;->a(Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/k0;

    move-result-object v1

    const/4 v2, 0x0

    const/4 v3, 0x0

    new-instance v4, Lcom/transsion/subroom/activity/MainActivity$routerToDownloadsFragment$1;

    const/4 v0, 0x0

    invoke-direct {v4, p0, v0}, Lcom/transsion/subroom/activity/MainActivity$routerToDownloadsFragment$1;-><init>(Lcom/transsion/subroom/activity/MainActivity;Lkotlin/coroutines/Continuation;)V

    const/4 v5, 0x3

    const/4 v6, 0x0

    invoke-static/range {v1 .. v6}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/k0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/q1;

    return-void
.end method

.method public isChangeStatusBar()Z
    .locals 1

    const/4 v0, 0x0

    return v0
.end method

.method public isStatusDark()Z
    .locals 1

    const/4 v0, 0x0

    return v0
.end method

.method public isTranslucent()Z
    .locals 1

    const/4 v0, 0x1

    return v0
.end method

.method public final j0(I)V
    .locals 1

    invoke-virtual {p0}, Lcom/transsion/baseui/activity/BaseCommonActivity;->getMViewBinding()Ls4/a;

    move-result-object v0

    check-cast v0, Luq/b;

    iget-object v0, v0, Luq/b;->j:Lcom/google/android/material/tabs/TabLayout;

    invoke-virtual {v0, p1}, Lcom/google/android/material/tabs/TabLayout;->getTabAt(I)Lcom/google/android/material/tabs/TabLayout$Tab;

    move-result-object p1

    invoke-virtual {v0, p1}, Lcom/google/android/material/tabs/TabLayout;->selectTab(Lcom/google/android/material/tabs/TabLayout$Tab;)V

    return-void
.end method

.method public final k0()V
    .locals 3

    const-string v0, "HOME"

    invoke-virtual {p0, v0}, Lcom/transsion/subroom/activity/MainActivity;->V(Ljava/lang/String;)I

    move-result v0

    invoke-virtual {p0}, Lcom/transsion/baseui/activity/BaseCommonActivity;->getMViewBinding()Ls4/a;

    move-result-object v1

    check-cast v1, Luq/b;

    iget-object v1, v1, Luq/b;->j:Lcom/google/android/material/tabs/TabLayout;

    invoke-virtual {v1, v0}, Lcom/google/android/material/tabs/TabLayout;->getTabAt(I)Lcom/google/android/material/tabs/TabLayout$Tab;

    move-result-object v1

    if-eqz v1, :cond_0

    iget-object v1, v1, Lcom/google/android/material/tabs/TabLayout$Tab;->view:Lcom/google/android/material/tabs/TabLayout$TabView;

    if-eqz v1, :cond_0

    new-instance v2, Lcom/transsion/subroom/activity/f;

    invoke-direct {v2, p0, v0}, Lcom/transsion/subroom/activity/f;-><init>(Lcom/transsion/subroom/activity/MainActivity;I)V

    invoke-virtual {v1, v2}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    :cond_0
    return-void
.end method

.method public final m0(Z)V
    .locals 2

    sget-object v0, Lcom/transsion/home/<USER>/BottomTabType;->ME:Lcom/transsion/home/<USER>/BottomTabType;

    invoke-virtual {v0}, Lcom/transsion/home/<USER>/BottomTabType;->getValue()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p0, v0}, Lcom/transsion/subroom/activity/MainActivity;->W(Ljava/lang/String;)I

    move-result v0

    invoke-virtual {p0}, Lcom/transsion/baseui/activity/BaseCommonActivity;->getMViewBinding()Ls4/a;

    move-result-object v1

    check-cast v1, Luq/b;

    iget-object v1, v1, Luq/b;->j:Lcom/google/android/material/tabs/TabLayout;

    invoke-virtual {v1, v0}, Lcom/google/android/material/tabs/TabLayout;->getTabAt(I)Lcom/google/android/material/tabs/TabLayout$Tab;

    move-result-object v0

    if-eqz v0, :cond_0

    iget-object v0, v0, Lcom/google/android/material/tabs/TabLayout$Tab;->view:Lcom/google/android/material/tabs/TabLayout$TabView;

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    if-eqz v0, :cond_1

    new-instance v1, Lcom/transsion/subroom/activity/e;

    invoke-direct {v1, v0, p1}, Lcom/transsion/subroom/activity/e;-><init>(Lcom/google/android/material/tabs/TabLayout$TabView;Z)V

    invoke-virtual {v0, v1}, Landroid/view/View;->post(Ljava/lang/Runnable;)Z

    :cond_1
    return-void
.end method

.method public final o0(Lcom/transsion/home/<USER>/BottomTabItem;Landroid/view/View;Lcom/transsion/home/<USER>/BottomTabItem;)V
    .locals 27

    move-object/from16 v1, p0

    move-object/from16 v2, p1

    move-object/from16 v0, p2

    if-eqz v0, :cond_1c

    sget v3, Lcom/transsion/home/<USER>

    invoke-virtual {v0, v3}, Landroid/view/View;->findViewById(I)Landroid/view/View;

    move-result-object v3

    check-cast v3, Lcom/tn/lib/widget/TnTextView;

    const/4 v4, 0x0

    if-eqz v3, :cond_1

    const-string v5, "findViewById<TnTextView>\u2026anssion.home.R.id.tv_tab)"

    invoke-static {v3, v5}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    if-eqz v2, :cond_0

    invoke-virtual/range {p1 .. p1}, Lcom/transsion/home/<USER>/BottomTabItem;->getName()Ljava/lang/String;

    move-result-object v5

    goto :goto_0

    :cond_0
    move-object v5, v4

    :goto_0
    invoke-virtual {v3, v5}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    :cond_1
    move-object/from16 v3, p3

    invoke-static {v3, v2}, Lkotlin/jvm/internal/Intrinsics;->b(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v3

    const-string v5, "context"

    const-string v6, ""

    if-eqz v3, :cond_8

    if-eqz v2, :cond_3

    invoke-virtual/range {p1 .. p1}, Lcom/transsion/home/<USER>/BottomTabItem;->getSelectIcon()Ljava/lang/String;

    move-result-object v3

    if-nez v3, :cond_2

    goto :goto_1

    :cond_2
    move-object v6, v3

    :cond_3
    :goto_1
    sget-object v3, Lcom/transsion/subroom/activity/a;->a:Lcom/transsion/subroom/activity/a;

    invoke-virtual {v3, v2}, Lcom/transsion/subroom/activity/a;->b(Lcom/transsion/home/<USER>/BottomTabItem;)I

    move-result v3

    invoke-virtual/range {p2 .. p2}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v7

    const-string v8, "this.context"

    invoke-static {v7, v8}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-static {v7}, Lvi/a;->b(Landroid/content/Context;)Landroid/graphics/Typeface;

    move-result-object v7

    const-string v8, "getMediumTypeFace(this.context)"

    invoke-static {v7, v8}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    if-eqz v2, :cond_4

    invoke-virtual/range {p1 .. p1}, Lcom/transsion/home/<USER>/BottomTabItem;->getText()Lcom/transsion/home/<USER>/Text;

    move-result-object v8

    if-eqz v8, :cond_4

    invoke-virtual {v8}, Lcom/transsion/home/<USER>/Text;->getSelectColor()Ljava/lang/String;

    move-result-object v8

    goto :goto_2

    :cond_4
    move-object v8, v4

    :goto_2
    if-eqz v8, :cond_7

    invoke-interface {v8}, Ljava/lang/CharSequence;->length()I

    move-result v8

    if-nez v8, :cond_5

    goto :goto_4

    :cond_5
    if-eqz v2, :cond_6

    invoke-virtual/range {p1 .. p1}, Lcom/transsion/home/<USER>/BottomTabItem;->getText()Lcom/transsion/home/<USER>/Text;

    move-result-object v8

    if-eqz v8, :cond_6

    invoke-virtual {v8}, Lcom/transsion/home/<USER>/Text;->getSelectColor()Ljava/lang/String;

    move-result-object v8

    goto :goto_3

    :cond_6
    move-object v8, v4

    :goto_3
    invoke-static {v8}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v8

    goto :goto_5

    :cond_7
    :goto_4
    sget v8, Lcom/tn/lib/widget/R$color;->text_01:I

    invoke-static {v1, v8}, Le1/a;->c(Landroid/content/Context;I)I

    move-result v8

    :goto_5
    move v13, v3

    move-object v12, v6

    goto :goto_a

    :cond_8
    if-eqz v2, :cond_a

    invoke-virtual/range {p1 .. p1}, Lcom/transsion/home/<USER>/BottomTabItem;->getDefaultIcon()Ljava/lang/String;

    move-result-object v3

    if-nez v3, :cond_9

    goto :goto_6

    :cond_9
    move-object v6, v3

    :cond_a
    :goto_6
    sget-object v3, Lcom/transsion/subroom/activity/a;->a:Lcom/transsion/subroom/activity/a;

    invoke-virtual {v3, v2}, Lcom/transsion/subroom/activity/a;->a(Lcom/transsion/home/<USER>/BottomTabItem;)I

    move-result v3

    invoke-virtual/range {p2 .. p2}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v7

    invoke-static {v7, v5}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-static {v7}, Lvi/a;->c(Landroid/content/Context;)Landroid/graphics/Typeface;

    move-result-object v7

    const-string v8, "getRegularTypeFace(context)"

    invoke-static {v7, v8}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    if-eqz v2, :cond_b

    invoke-virtual/range {p1 .. p1}, Lcom/transsion/home/<USER>/BottomTabItem;->getText()Lcom/transsion/home/<USER>/Text;

    move-result-object v8

    if-eqz v8, :cond_b

    invoke-virtual {v8}, Lcom/transsion/home/<USER>/Text;->getDefaultColor()Ljava/lang/String;

    move-result-object v8

    goto :goto_7

    :cond_b
    move-object v8, v4

    :goto_7
    if-eqz v8, :cond_e

    invoke-interface {v8}, Ljava/lang/CharSequence;->length()I

    move-result v8

    if-nez v8, :cond_c

    goto :goto_9

    :cond_c
    if-eqz v2, :cond_d

    invoke-virtual/range {p1 .. p1}, Lcom/transsion/home/<USER>/BottomTabItem;->getText()Lcom/transsion/home/<USER>/Text;

    move-result-object v8

    if-eqz v8, :cond_d

    invoke-virtual {v8}, Lcom/transsion/home/<USER>/Text;->getDefaultColor()Ljava/lang/String;

    move-result-object v8

    goto :goto_8

    :cond_d
    move-object v8, v4

    :goto_8
    invoke-static {v8}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v8

    goto :goto_5

    :cond_e
    :goto_9
    sget v8, Lcom/tn/lib/widget/R$color;->text_02:I

    invoke-static {v1, v8}, Le1/a;->c(Landroid/content/Context;I)I

    move-result v8

    goto :goto_5

    :goto_a
    sget v3, Lcom/transsion/home/<USER>

    invoke-virtual {v0, v3}, Landroid/view/View;->findViewById(I)Landroid/view/View;

    move-result-object v3

    check-cast v3, Landroidx/appcompat/widget/AppCompatTextView;

    if-eqz v3, :cond_f

    const-string v6, "findViewById<AppCompatTe\u2026anssion.home.R.id.tv_tab)"

    invoke-static {v3, v6}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-virtual {v3, v8}, Landroid/widget/TextView;->setTextColor(I)V

    invoke-virtual {v3, v7}, Landroid/widget/TextView;->setTypeface(Landroid/graphics/Typeface;)V

    :cond_f
    sget v3, Lcom/transsion/home/<USER>

    invoke-virtual {v0, v3}, Landroid/view/View;->findViewById(I)Landroid/view/View;

    move-result-object v3

    move-object v11, v3

    check-cast v11, Landroidx/appcompat/widget/AppCompatImageView;

    if-eqz v11, :cond_11

    const-string v3, "findViewById<AppCompatIm\u2026home.R.id.image_tab_icon)"

    invoke-static {v11, v3}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    if-eqz v2, :cond_10

    invoke-virtual/range {p1 .. p1}, Lcom/transsion/home/<USER>/BottomTabItem;->isLargeUiType()Z

    move-result v3

    const/4 v6, 0x1

    if-ne v3, v6, :cond_10

    const/high16 v3, 0x42900000    # 72.0f

    goto :goto_b

    :cond_10
    const/high16 v3, 0x41c00000    # 24.0f

    :goto_b
    sget-object v9, Lcom/transsion/baseui/image/ImageHelper;->a:Lcom/transsion/baseui/image/ImageHelper$Companion;

    invoke-virtual {v11}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v6

    move-object v10, v6

    invoke-static {v3}, Lcom/blankj/utilcode/util/f0;->a(F)I

    move-result v14

    invoke-static {v6, v5}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    const/4 v15, 0x0

    const/16 v16, 0x0

    const/16 v17, 0x0

    const/16 v18, 0x0

    const/16 v19, 0x0

    const/16 v20, 0x0

    const/16 v21, 0x0

    const/16 v22, 0x0

    const/16 v23, 0x0

    const/16 v24, 0x0

    const/16 v25, 0x7fe0

    const/16 v26, 0x0

    invoke-static/range {v9 .. v26}, Lcom/transsion/baseui/image/ImageHelper$Companion;->r(Lcom/transsion/baseui/image/ImageHelper$Companion;Landroid/content/Context;Landroid/widget/ImageView;Ljava/lang/String;IIIIZLjava/lang/String;ZZZZZIILjava/lang/Object;)V

    :cond_11
    sget v3, Lcom/transsion/home/<USER>

    invoke-virtual {v0, v3}, Landroid/view/View;->findViewById(I)Landroid/view/View;

    move-result-object v3

    check-cast v3, Landroidx/appcompat/widget/AppCompatImageView;

    if-eqz v3, :cond_12

    sget v5, Lcom/transsnet/downloader/R$mipmap;->ic_download_status_fail_dark:I

    invoke-virtual {v3, v5}, Landroidx/appcompat/widget/AppCompatImageView;->setImageResource(I)V

    :cond_12
    sget v3, Lcom/transsion/home/<USER>

    invoke-virtual {v0, v3}, Landroid/view/View;->findViewById(I)Landroid/view/View;

    move-result-object v3

    check-cast v3, Landroidx/appcompat/widget/AppCompatTextView;

    if-eqz v3, :cond_13

    sget v5, Lcom/transsnet/downloader/R$drawable;->shape_download_icon_status_dark:I

    invoke-virtual {v3, v5}, Landroidx/appcompat/widget/AppCompatTextView;->setBackgroundResource(I)V

    :cond_13
    sget v3, Lcom/transsion/home/<USER>

    invoke-virtual {v0, v3}, Landroid/view/View;->findViewById(I)Landroid/view/View;

    move-result-object v0

    check-cast v0, Landroidx/appcompat/widget/AppCompatTextView;

    if-eqz v2, :cond_14

    invoke-virtual/range {p1 .. p1}, Lcom/transsion/home/<USER>/BottomTabItem;->getBadge()Lcom/transsion/home/<USER>/Badge;

    move-result-object v3

    goto :goto_c

    :cond_14
    move-object v3, v4

    :goto_c
    const-string v5, "tvRedTips"

    if-eqz v3, :cond_1b

    iget-boolean v3, v1, Lcom/transsion/subroom/activity/MainActivity;->p:Z

    if-eqz v3, :cond_1b

    sget-object v3, Lcom/transsion/baselib/report/launch/RoomAppMMKV;->a:Lcom/transsion/baselib/report/launch/RoomAppMMKV;

    invoke-virtual {v3}, Lcom/transsion/baselib/report/launch/RoomAppMMKV;->a()Lcom/tencent/mmkv/MMKV;

    move-result-object v3

    const-string v6, "tab_hot_tips_is_show"

    const/4 v7, 0x0

    invoke-virtual {v3, v6, v7}, Lcom/tencent/mmkv/MMKV;->getBoolean(Ljava/lang/String;Z)Z

    move-result v3

    if-nez v3, :cond_1b

    if-eqz v0, :cond_15

    invoke-static {v0, v5}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-static {v0}, Lvi/c;->k(Landroid/view/View;)V

    :cond_15
    if-nez v0, :cond_16

    goto :goto_d

    :cond_16
    invoke-static {}, Lcom/blankj/utilcode/util/Utils;->a()Landroid/app/Application;

    move-result-object v3

    sget v5, Lcom/transsion/subroom/R$string;->bottom_tab_tips_hot:I

    invoke-virtual {v3, v5}, Landroid/content/Context;->getString(I)Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v0, v3}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    :goto_d
    :try_start_0
    sget-object v3, Lkotlin/Result;->Companion:Lkotlin/Result$Companion;

    if-nez v0, :cond_17

    goto :goto_f

    :cond_17
    invoke-virtual/range {p1 .. p1}, Lcom/transsion/home/<USER>/BottomTabItem;->getBadge()Lcom/transsion/home/<USER>/Badge;

    move-result-object v3

    if-eqz v3, :cond_18

    invoke-virtual {v3}, Lcom/transsion/home/<USER>/Badge;->getText()Ljava/lang/String;

    move-result-object v3

    if-eqz v3, :cond_18

    goto :goto_e

    :catchall_0
    move-exception v0

    goto :goto_10

    :cond_18
    const-string v3, "Hot"

    :goto_e
    invoke-virtual {v0, v3}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    :goto_f
    if-eqz v0, :cond_1a

    invoke-virtual/range {p1 .. p1}, Lcom/transsion/home/<USER>/BottomTabItem;->getBadge()Lcom/transsion/home/<USER>/Badge;

    move-result-object v3

    if-eqz v3, :cond_19

    invoke-virtual {v3}, Lcom/transsion/home/<USER>/Badge;->getBgHue()Ljava/lang/String;

    move-result-object v4

    :cond_19
    invoke-static {v4}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v3

    invoke-virtual {v0, v3}, Landroid/view/View;->setBackgroundColor(I)V

    sget-object v4, Lkotlin/Unit;->a:Lkotlin/Unit;

    :cond_1a
    invoke-static {v4}, Lkotlin/Result;->constructor-impl(Ljava/lang/Object;)Ljava/lang/Object;
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    goto :goto_11

    :goto_10
    sget-object v3, Lkotlin/Result;->Companion:Lkotlin/Result$Companion;

    invoke-static {v0}, Lkotlin/ResultKt;->a(Ljava/lang/Throwable;)Ljava/lang/Object;

    move-result-object v0

    invoke-static {v0}, Lkotlin/Result;->constructor-impl(Ljava/lang/Object;)Ljava/lang/Object;

    :goto_11
    invoke-virtual/range {p1 .. p1}, Lcom/transsion/home/<USER>/BottomTabItem;->getBtTabCode()Ljava/lang/String;

    move-result-object v0

    iput-object v0, v1, Lcom/transsion/subroom/activity/MainActivity;->q:Ljava/lang/String;

    goto :goto_12

    :cond_1b
    if-eqz v0, :cond_1c

    invoke-static {v0, v5}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-static {v0}, Lvi/c;->g(Landroid/view/View;)V

    :cond_1c
    :goto_12
    return-void
.end method

.method public onActivityResult(IILandroid/content/Intent;)V
    .locals 1

    invoke-super {p0, p1, p2, p3}, Landroidx/fragment/app/FragmentActivity;->onActivityResult(IILandroid/content/Intent;)V

    invoke-virtual {p0}, Lcom/transsion/subroom/activity/MainActivity;->T()Lcom/transsion/subroom/update/GPUpdateManager;

    move-result-object v0

    invoke-virtual {v0, p1, p2, p3}, Lcom/transsion/subroom/update/GPUpdateManager;->n(IILandroid/content/Intent;)V

    return-void
.end method

.method public onBackPressed()V
    .locals 7

    iget-object v0, p0, Lcom/transsion/subroom/activity/MainActivity;->d:Landroidx/fragment/app/Fragment;

    instance-of v1, v0, Lcom/transsion/web/fragment/WebFragment;

    if-eqz v1, :cond_0

    const-string v1, "null cannot be cast to non-null type com.transsion.web.fragment.WebFragment"

    invoke-static {v0, v1}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/String;)V

    check-cast v0, Lcom/transsion/web/fragment/WebFragment;

    invoke-virtual {v0}, Lcom/transsion/web/fragment/WebFragment;->Q0()Z

    move-result v0

    if-eqz v0, :cond_0

    return-void

    :cond_0
    iget-object v0, p0, Lcom/transsion/subroom/activity/MainActivity;->d:Landroidx/fragment/app/Fragment;

    instance-of v1, v0, Lcom/transsion/home/<USER>/HomeFragment;

    if-eqz v1, :cond_1

    const-string v1, "null cannot be cast to non-null type com.transsion.home.fragment.HomeFragment"

    invoke-static {v0, v1}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/String;)V

    check-cast v0, Lcom/transsion/home/<USER>/HomeFragment;

    invoke-virtual {v0}, Lcom/transsion/home/<USER>/HomeFragment;->r0()Z

    move-result v0

    if-eqz v0, :cond_1

    return-void

    :cond_1
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v0

    iget-wide v2, p0, Lcom/transsion/subroom/activity/MainActivity;->l:J

    sub-long v2, v0, v2

    sget-object v4, Ljava/util/concurrent/TimeUnit;->SECONDS:Ljava/util/concurrent/TimeUnit;

    const-wide/16 v5, 0x3

    invoke-virtual {v4, v5, v6}, Ljava/util/concurrent/TimeUnit;->toMillis(J)J

    move-result-wide v4

    cmp-long v6, v2, v4

    if-lez v6, :cond_3

    iput-wide v0, p0, Lcom/transsion/subroom/activity/MainActivity;->l:J

    sget-object v0, Lbk/b;->a:Lbk/b$a;

    sget v1, Lcom/transsion/baseui/R$string;->tap_again_to_exit:I

    invoke-virtual {v0, v1}, Lbk/b$a;->d(I)V

    sget-object v0, Lcom/tn/lib/util/networkinfo/f;->a:Lcom/tn/lib/util/networkinfo/f;

    invoke-virtual {v0}, Lcom/tn/lib/util/networkinfo/f;->e()Z

    move-result v0

    if-eqz v0, :cond_2

    iget-object v0, p0, Lcom/transsion/subroom/activity/MainActivity;->b:Lcom/transsion/home/<USER>/HomeFragment;

    if-eqz v0, :cond_2

    invoke-virtual {v0}, Landroidx/fragment/app/Fragment;->isVisible()Z

    move-result v0

    const/4 v1, 0x1

    if-ne v0, v1, :cond_2

    iget-object v0, p0, Lcom/transsion/subroom/activity/MainActivity;->b:Lcom/transsion/home/<USER>/HomeFragment;

    if-eqz v0, :cond_2

    invoke-virtual {v0}, Lcom/transsion/home/<USER>/HomeFragment;->y0()V

    :cond_2
    return-void

    :cond_3
    invoke-super {p0}, Landroidx/activity/ComponentActivity;->onBackPressed()V

    return-void
.end method

.method public onConfigurationChanged(Landroid/content/res/Configuration;)V
    .locals 1

    const-string v0, "newConfig"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-super {p0, p1}, Landroidx/appcompat/app/AppCompatActivity;->onConfigurationChanged(Landroid/content/res/Configuration;)V

    iget-object v0, p0, Lcom/transsion/subroom/activity/MainActivity;->b:Lcom/transsion/home/<USER>/HomeFragment;

    if-eqz v0, :cond_0

    invoke-virtual {v0, p1}, Landroidx/fragment/app/Fragment;->onConfigurationChanged(Landroid/content/res/Configuration;)V

    :cond_0
    return-void
.end method

.method public native onCreate(Landroid/os/Bundle;)V
.end method

.method public onDestroy()V
    .locals 2

    invoke-super {p0}, Lcom/transsion/baseui/activity/BaseActivity;->onDestroy()V

    iget-object v0, p0, Lcom/transsion/subroom/activity/MainActivity;->m:Lcom/transsion/home/<USER>/b;

    if-eqz v0, :cond_0

    invoke-interface {v0}, Lcom/transsion/home/<USER>/b;->reset()V

    :cond_0
    const/4 v0, 0x0

    iput-boolean v0, p0, Lcom/transsion/subroom/activity/MainActivity;->o:Z

    sget-object v0, Lcom/transsion/startup/pref/consume/AppStartReport;->a:Lcom/transsion/startup/pref/consume/AppStartReport;

    invoke-virtual {v0}, Lcom/transsion/startup/pref/consume/AppStartReport;->h()V

    invoke-virtual {p0}, Lcom/transsion/subroom/activity/MainActivity;->T()Lcom/transsion/subroom/update/GPUpdateManager;

    move-result-object v0

    invoke-virtual {v0}, Lcom/transsion/subroom/update/GPUpdateManager;->s()V

    iget-object v0, p0, Lcom/transsion/subroom/activity/MainActivity;->a:Ltt/e;

    if-eqz v0, :cond_1

    sget-object v1, Lcom/transsnet/downloader/manager/DownloadStatusIconManager;->h:Lcom/transsnet/downloader/manager/DownloadStatusIconManager$a;

    invoke-virtual {v1}, Lcom/transsnet/downloader/manager/DownloadStatusIconManager$a;->a()Lcom/transsnet/downloader/manager/DownloadStatusIconManager;

    move-result-object v1

    invoke-virtual {v1, v0}, Lcom/transsnet/downloader/manager/DownloadStatusIconManager;->r(Ltt/e;)V

    :cond_1
    sget-object v0, Lcom/transsion/subtitle/VideoSubtitleManager;->a:Lcom/transsion/subtitle/VideoSubtitleManager$Companion;

    invoke-virtual {v0}, Lcom/transsion/subtitle/VideoSubtitleManager$Companion;->a()Lcom/transsion/subtitle/VideoSubtitleManager;

    move-result-object v0

    invoke-interface {v0}, Lcom/transsion/subtitle/VideoSubtitleManager;->destroy()V

    sget-object v0, Lcom/transsion/postdetail/helper/ShortTvImmVideoHelper;->k:Lcom/transsion/postdetail/helper/ShortTvImmVideoHelper$a;

    invoke-virtual {v0}, Lcom/transsion/postdetail/helper/ShortTvImmVideoHelper$a;->a()Lcom/transsion/postdetail/helper/ShortTvImmVideoHelper;

    move-result-object v0

    invoke-virtual {v0}, Lcom/transsion/postdetail/helper/ShortTvImmVideoHelper;->v()V

    sget-object v0, Lcom/transsion/home/<USER>/g;->a:Lcom/transsion/home/<USER>/g;

    invoke-virtual {v0}, Lcom/transsion/home/<USER>/g;->f()V

    sget-object v0, Lli/c;->a:Lli/c;

    invoke-virtual {v0}, Lli/c;->a()V

    invoke-static {}, Lcom/alibaba/android/arouter/launcher/a;->d()Lcom/alibaba/android/arouter/launcher/a;

    move-result-object v0

    const-class v1, Lcom/transsion/commercializationapi/ICommonDialogApi;

    invoke-virtual {v0, v1}, Lcom/alibaba/android/arouter/launcher/a;->h(Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/transsion/commercializationapi/ICommonDialogApi;

    invoke-interface {v0}, Lcom/transsion/commercializationapi/ICommonDialogApi;->onDestroy()V

    return-void
.end method

.method public onNewIntent(Landroid/content/Intent;)V
    .locals 0

    invoke-super {p0, p1}, Landroidx/activity/ComponentActivity;->onNewIntent(Landroid/content/Intent;)V

    :try_start_0
    invoke-virtual {p0, p1}, Landroid/app/Activity;->setIntent(Landroid/content/Intent;)V

    invoke-static {}, Lcom/alibaba/android/arouter/launcher/a;->d()Lcom/alibaba/android/arouter/launcher/a;

    move-result-object p1

    invoke-virtual {p1, p0}, Lcom/alibaba/android/arouter/launcher/a;->f(Ljava/lang/Object;)V

    invoke-virtual {p0}, Lcom/transsion/subroom/activity/MainActivity;->s0()V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    :catch_0
    return-void
.end method

.method public onRequestPermissionsResult(I[Ljava/lang/String;[I)V
    .locals 1

    const-string v0, "permissions"

    invoke-static {p2, v0}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    const-string v0, "grantResults"

    invoke-static {p3, v0}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-super {p0, p1, p2, p3}, Landroidx/fragment/app/FragmentActivity;->onRequestPermissionsResult(I[Ljava/lang/String;[I)V

    return-void
.end method

.method public onResume()V
    .locals 0

    invoke-super {p0}, Lcom/transsion/baseui/activity/BaseActivity;->onResume()V

    invoke-virtual {p0}, Lcom/transsion/subroom/activity/MainActivity;->S()V

    return-void
.end method

.method public onSaveInstanceState(Landroid/os/Bundle;)V
    .locals 2

    const-string v0, "outState"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-super {p0, p1}, Landroidx/activity/ComponentActivity;->onSaveInstanceState(Landroid/os/Bundle;)V

    const-string v0, "current_fragment_index"

    iget v1, p0, Lcom/transsion/subroom/activity/MainActivity;->f:I

    invoke-virtual {p1, v0, v1}, Landroid/os/BaseBundle;->putInt(Ljava/lang/String;I)V

    const-string v0, "video_tab_is_white"

    iget-boolean v1, p0, Lcom/transsion/subroom/activity/MainActivity;->g:Z

    invoke-virtual {p1, v0, v1}, Landroid/os/Bundle;->putBoolean(Ljava/lang/String;Z)V

    return-void
.end method

.method public final p0(II)V
    .locals 5

    sget-object v0, Lcom/transsion/home/<USER>/BottomTabType;->DOWNLOAD:Lcom/transsion/home/<USER>/BottomTabType;

    invoke-virtual {v0}, Lcom/transsion/home/<USER>/BottomTabType;->getValue()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p0, v0}, Lcom/transsion/subroom/activity/MainActivity;->W(Ljava/lang/String;)I

    move-result v0

    invoke-virtual {p0}, Lcom/transsion/baseui/activity/BaseCommonActivity;->getMViewBinding()Ls4/a;

    move-result-object v1

    check-cast v1, Luq/b;

    iget-object v1, v1, Luq/b;->j:Lcom/google/android/material/tabs/TabLayout;

    invoke-virtual {v1, v0}, Lcom/google/android/material/tabs/TabLayout;->getTabAt(I)Lcom/google/android/material/tabs/TabLayout$Tab;

    move-result-object v0

    const/4 v1, 0x0

    if-eqz v0, :cond_0

    iget-object v2, v0, Lcom/google/android/material/tabs/TabLayout$Tab;->view:Lcom/google/android/material/tabs/TabLayout$TabView;

    if-eqz v2, :cond_0

    sget v3, Lcom/transsion/home/<USER>

    invoke-virtual {v2, v3}, Landroid/view/View;->findViewById(I)Landroid/view/View;

    move-result-object v2

    goto :goto_0

    :cond_0
    move-object v2, v1

    :goto_0
    if-eqz v0, :cond_1

    iget-object v3, v0, Lcom/google/android/material/tabs/TabLayout$Tab;->view:Lcom/google/android/material/tabs/TabLayout$TabView;

    if-eqz v3, :cond_1

    sget v4, Lcom/transsion/home/<USER>

    invoke-virtual {v3, v4}, Landroid/view/View;->findViewById(I)Landroid/view/View;

    move-result-object v3

    check-cast v3, Landroidx/appcompat/widget/AppCompatTextView;

    goto :goto_1

    :cond_1
    move-object v3, v1

    :goto_1
    if-eqz v0, :cond_2

    iget-object v0, v0, Lcom/google/android/material/tabs/TabLayout$Tab;->view:Lcom/google/android/material/tabs/TabLayout$TabView;

    if-eqz v0, :cond_2

    sget v1, Lcom/transsion/home/<USER>

    invoke-virtual {v0, v1}, Landroid/view/View;->findViewById(I)Landroid/view/View;

    move-result-object v0

    move-object v1, v0

    check-cast v1, Landroidx/appcompat/widget/AppCompatImageView;

    :cond_2
    if-eqz p1, :cond_c

    const/4 v0, 0x1

    if-eq p1, v0, :cond_9

    const/4 v4, 0x2

    if-eq p1, v4, :cond_6

    const/4 v4, 0x4

    if-eq p1, v4, :cond_5

    const/4 v4, 0x5

    if-eq p1, v4, :cond_4

    const/4 p2, 0x6

    if-eq p1, p2, :cond_3

    goto :goto_2

    :cond_3
    if-eqz v2, :cond_f

    invoke-static {v2}, Lvi/c;->i(Landroid/view/View;)Z

    move-result p1

    if-ne p1, v0, :cond_f

    invoke-static {v2}, Lvi/c;->g(Landroid/view/View;)V

    goto :goto_2

    :cond_4
    invoke-virtual {p0, v2, v3, v1, p2}, Lcom/transsion/subroom/activity/MainActivity;->r0(Landroid/view/View;Landroidx/appcompat/widget/AppCompatTextView;Landroidx/appcompat/widget/AppCompatImageView;I)V

    goto :goto_2

    :cond_5
    invoke-virtual {p0, v2, v3, v1, p2}, Lcom/transsion/subroom/activity/MainActivity;->r0(Landroid/view/View;Landroidx/appcompat/widget/AppCompatTextView;Landroidx/appcompat/widget/AppCompatImageView;I)V

    goto :goto_2

    :cond_6
    if-eqz v1, :cond_7

    invoke-static {v1}, Lvi/c;->k(Landroid/view/View;)V

    :cond_7
    if-eqz v2, :cond_8

    invoke-static {v2}, Lvi/c;->g(Landroid/view/View;)V

    :cond_8
    if-eqz v3, :cond_f

    invoke-static {v3}, Lvi/c;->g(Landroid/view/View;)V

    goto :goto_2

    :cond_9
    if-eqz v2, :cond_a

    invoke-static {v2}, Lvi/c;->k(Landroid/view/View;)V

    :cond_a
    if-eqz v3, :cond_b

    invoke-static {v3}, Lvi/c;->g(Landroid/view/View;)V

    :cond_b
    if-eqz v1, :cond_f

    invoke-static {v1}, Lvi/c;->g(Landroid/view/View;)V

    goto :goto_2

    :cond_c
    if-eqz v2, :cond_d

    invoke-static {v2}, Lvi/c;->g(Landroid/view/View;)V

    :cond_d
    if-eqz v3, :cond_e

    invoke-static {v3}, Lvi/c;->g(Landroid/view/View;)V

    :cond_e
    if-eqz v1, :cond_f

    invoke-static {v1}, Lvi/c;->g(Landroid/view/View;)V

    :cond_f
    :goto_2
    return-void
.end method

.method public final q0(Landroidx/fragment/app/Fragment;Ljava/lang/String;)V
    .locals 4

    iget-object v0, p0, Lcom/transsion/subroom/activity/MainActivity;->d:Landroidx/fragment/app/Fragment;

    invoke-static {v0, p1}, Lkotlin/jvm/internal/Intrinsics;->b(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_2

    sget-object v0, Lxi/b;->a:Lxi/b$a;

    invoke-virtual {p1}, Landroidx/fragment/app/Fragment;->isAdded()Z

    move-result v1

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "current add fragment:"

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v3, " isAdded: "

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    const-string v1, " fragment:"

    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    const/4 v2, 0x1

    const-string v3, "MainActivity"

    invoke-virtual {v0, v3, v1, v2}, Lxi/b$a;->c(Ljava/lang/String;Ljava/lang/String;Z)V

    invoke-virtual {p0}, Landroidx/fragment/app/FragmentActivity;->getSupportFragmentManager()Landroidx/fragment/app/FragmentManager;

    move-result-object v0

    invoke-virtual {v0}, Landroidx/fragment/app/FragmentManager;->beginTransaction()Landroidx/fragment/app/FragmentTransaction;

    move-result-object v0

    iget-object v1, p0, Lcom/transsion/subroom/activity/MainActivity;->d:Landroidx/fragment/app/Fragment;

    if-eqz v1, :cond_0

    invoke-virtual {v0, v1}, Landroidx/fragment/app/FragmentTransaction;->hide(Landroidx/fragment/app/Fragment;)Landroidx/fragment/app/FragmentTransaction;

    :cond_0
    iput-object p1, p0, Lcom/transsion/subroom/activity/MainActivity;->d:Landroidx/fragment/app/Fragment;

    invoke-virtual {p1}, Landroidx/fragment/app/Fragment;->isAdded()Z

    move-result v1

    if-nez v1, :cond_1

    sget v1, Lcom/transsion/subroom/R$id;->container:I

    invoke-virtual {v0, v1, p1, p2}, Landroidx/fragment/app/FragmentTransaction;->add(ILandroidx/fragment/app/Fragment;Ljava/lang/String;)Landroidx/fragment/app/FragmentTransaction;

    invoke-virtual {v0, p1}, Landroidx/fragment/app/FragmentTransaction;->show(Landroidx/fragment/app/Fragment;)Landroidx/fragment/app/FragmentTransaction;

    invoke-virtual {v0}, Landroidx/fragment/app/FragmentTransaction;->commitNowAllowingStateLoss()V

    goto :goto_0

    :cond_1
    invoke-virtual {v0, p1}, Landroidx/fragment/app/FragmentTransaction;->show(Landroidx/fragment/app/Fragment;)Landroidx/fragment/app/FragmentTransaction;

    invoke-virtual {v0}, Landroidx/fragment/app/FragmentTransaction;->commitNowAllowingStateLoss()V

    :cond_2
    :goto_0
    return-void
.end method

.method public final r0(Landroid/view/View;Landroidx/appcompat/widget/AppCompatTextView;Landroidx/appcompat/widget/AppCompatImageView;I)V
    .locals 0

    if-eqz p1, :cond_0

    invoke-static {p1}, Lvi/c;->g(Landroid/view/View;)V

    :cond_0
    if-eqz p3, :cond_1

    invoke-static {p3}, Lvi/c;->g(Landroid/view/View;)V

    :cond_1
    if-gtz p4, :cond_3

    if-eqz p2, :cond_2

    invoke-static {p2}, Lvi/c;->g(Landroid/view/View;)V

    :cond_2
    return-void

    :cond_3
    const/16 p1, 0x63

    if-le p4, p1, :cond_4

    const-string p1, "99+"

    goto :goto_0

    :cond_4
    invoke-static {p4}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    move-result-object p1

    :goto_0
    if-eqz p2, :cond_5

    invoke-static {p2}, Lvi/c;->k(Landroid/view/View;)V

    :cond_5
    if-nez p2, :cond_6

    goto :goto_1

    :cond_6
    invoke-virtual {p2, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    :goto_1
    return-void
.end method

.method public final s0()V
    .locals 3

    iget-object v0, p0, Lcom/transsion/subroom/activity/MainActivity;->i:Ljava/lang/String;

    iget v1, p0, Lcom/transsion/subroom/activity/MainActivity;->j:I

    invoke-virtual {p0, v0, v1}, Lcom/transsion/subroom/activity/MainActivity;->U(Ljava/lang/String;I)I

    move-result v0

    iget-object v1, p0, Lcom/transsion/subroom/activity/MainActivity;->c:Ljava/util/List;

    invoke-interface {v1}, Ljava/util/List;->size()I

    move-result v1

    if-ge v0, v1, :cond_3

    if-gez v0, :cond_0

    goto :goto_1

    :cond_0
    iget v1, p0, Lcom/transsion/subroom/activity/MainActivity;->f:I

    if-eq v0, v1, :cond_1

    invoke-virtual {p0, v0}, Lcom/transsion/subroom/activity/MainActivity;->j0(I)V

    :cond_1
    iget-object v1, p0, Lcom/transsion/subroom/activity/MainActivity;->n:Lcom/transsion/home/<USER>/AppTab;

    if-eqz v1, :cond_2

    invoke-virtual {v1}, Lcom/transsion/home/<USER>/AppTab;->getBottomTabs()Ljava/util/List;

    move-result-object v1

    if-eqz v1, :cond_2

    invoke-interface {v1, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/transsion/home/<USER>/BottomTabItem;

    if-eqz v0, :cond_2

    invoke-virtual {v0}, Lcom/transsion/home/<USER>/BottomTabItem;->getBtTabCode()Ljava/lang/String;

    move-result-object v0

    goto :goto_0

    :cond_2
    const/4 v0, 0x0

    :goto_0
    const-string v1, "HOME"

    invoke-static {v0, v1}, Lkotlin/jvm/internal/Intrinsics;->b(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_3

    iget-object v0, p0, Lcom/transsion/subroom/activity/MainActivity;->b:Lcom/transsion/home/<USER>/HomeFragment;

    if-eqz v0, :cond_3

    iget-object v1, p0, Lcom/transsion/subroom/activity/MainActivity;->h:Ljava/lang/String;

    iget v2, p0, Lcom/transsion/subroom/activity/MainActivity;->k:I

    invoke-virtual {v0, v1, v2}, Lcom/transsion/home/<USER>/HomeFragment;->x0(Ljava/lang/String;I)V

    :cond_3
    :goto_1
    return-void
.end method

.method public statusColor()I
    .locals 1

    sget v0, Lcom/tn/lib/widget/R$color;->text_01:I

    return v0
.end method

.method public final t0(Lcom/transsion/home/<USER>/BottomTabItem;Lcom/google/android/material/tabs/TabLayout$TabView;Lcom/transsion/home/<USER>/BottomTabItem;)Landroid/view/View;
    .locals 3

    invoke-static {p0}, Landroid/view/LayoutInflater;->from(Landroid/content/Context;)Landroid/view/LayoutInflater;

    move-result-object v0

    invoke-virtual {p1}, Lcom/transsion/home/<USER>/BottomTabItem;->isLargeUiType()Z

    move-result v1

    if-eqz v1, :cond_0

    sget v1, Lcom/transsion/subroom/R$layout;->layout_center_bottom_tab:I

    goto :goto_0

    :cond_0
    sget v1, Lcom/transsion/home/<USER>

    :goto_0
    const/4 v2, 0x0

    invoke-virtual {v0, v1, p2, v2}, Landroid/view/LayoutInflater;->inflate(ILandroid/view/ViewGroup;Z)Landroid/view/View;

    move-result-object p2

    invoke-virtual {p0, p1, p2, p3}, Lcom/transsion/subroom/activity/MainActivity;->o0(Lcom/transsion/home/<USER>/BottomTabItem;Landroid/view/View;Lcom/transsion/home/<USER>/BottomTabItem;)V

    const-string p1, "from(this).inflate(\n    \u2026electedTabItem)\n        }"

    invoke-static {p2, p1}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    return-object p2
.end method
