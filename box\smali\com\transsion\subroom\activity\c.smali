.class public final synthetic Lcom/transsion/subroom/activity/c;
.super Ljava/lang/Object;

# interfaces
.implements Landroid/view/View$OnClickListener;


# instance fields
.field public final synthetic a:Lcom/transsion/subroom/activity/GuideActivity;


# direct methods
.method public synthetic constructor <init>(Lcom/transsion/subroom/activity/GuideActivity;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/transsion/subroom/activity/c;->a:Lcom/transsion/subroom/activity/GuideActivity;

    return-void
.end method


# virtual methods
.method public final onClick(Landroid/view/View;)V
    .locals 1

    iget-object v0, p0, Lcom/transsion/subroom/activity/c;->a:Lcom/transsion/subroom/activity/GuideActivity;

    invoke-static {v0, p1}, Lcom/transsion/subroom/activity/GuideActivity;->w(Lcom/transsion/subroom/activity/GuideActivity;Landroid/view/View;)V

    return-void
.end method
