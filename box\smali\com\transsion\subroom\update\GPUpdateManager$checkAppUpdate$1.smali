.class final Lcom/transsion/subroom/update/GPUpdateManager$checkAppUpdate$1;
.super Lkotlin/jvm/internal/Lambda;

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/transsion/subroom/update/GPUpdateManager;->j(Landroid/app/Activity;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/jvm/internal/Lambda;",
        "Lkotlin/jvm/functions/Function1<",
        "Lcom/google/android/play/core/appupdate/a;",
        "Lkotlin/Unit;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation


# instance fields
.field final synthetic $activity:Landroid/app/Activity;

.field final synthetic this$0:Lcom/transsion/subroom/update/GPUpdateManager;


# direct methods
.method public constructor <init>(Lcom/transsion/subroom/update/GPUpdateManager;Landroid/app/Activity;)V
    .locals 0

    iput-object p1, p0, Lcom/transsion/subroom/update/GPUpdateManager$checkAppUpdate$1;->this$0:Lcom/transsion/subroom/update/GPUpdateManager;

    iput-object p2, p0, Lcom/transsion/subroom/update/GPUpdateManager$checkAppUpdate$1;->$activity:Landroid/app/Activity;

    const/4 p1, 0x1

    invoke-direct {p0, p1}, Lkotlin/jvm/internal/Lambda;-><init>(I)V

    return-void
.end method


# virtual methods
.method public bridge synthetic invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    check-cast p1, Lcom/google/android/play/core/appupdate/a;

    invoke-virtual {p0, p1}, Lcom/transsion/subroom/update/GPUpdateManager$checkAppUpdate$1;->invoke(Lcom/google/android/play/core/appupdate/a;)V

    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    return-object p1
.end method

.method public final invoke(Lcom/google/android/play/core/appupdate/a;)V
    .locals 8

    sget-object v6, Lxi/b;->a:Lxi/b$a;

    const-string v1, "GPUpdateManager"

    invoke-virtual {p1}, Lcom/google/android/play/core/appupdate/a;->a()I

    move-result v0

    invoke-virtual {p1}, Lcom/google/android/play/core/appupdate/a;->d()I

    move-result v2

    const/4 v7, 0x0

    invoke-virtual {p1, v7}, Lcom/google/android/play/core/appupdate/a;->b(I)Z

    move-result v3

    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    const-string v5, "start installStatus="

    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v0, " available="

    invoke-virtual {v4, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4, v2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v0, " allow="

    invoke-virtual {v4, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4, v3}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    const/4 v3, 0x0

    const/4 v4, 0x4

    const/4 v5, 0x0

    move-object v0, v6

    invoke-static/range {v0 .. v5}, Lxi/b$a;->f(Lxi/b$a;Ljava/lang/String;Ljava/lang/String;ZILjava/lang/Object;)V

    invoke-virtual {p1}, Lcom/google/android/play/core/appupdate/a;->a()I

    move-result v0

    const/16 v1, 0xb

    if-ne v0, v1, :cond_0

    iget-object p1, p0, Lcom/transsion/subroom/update/GPUpdateManager$checkAppUpdate$1;->this$0:Lcom/transsion/subroom/update/GPUpdateManager;

    invoke-static {p1}, Lcom/transsion/subroom/update/GPUpdateManager;->h(Lcom/transsion/subroom/update/GPUpdateManager;)V

    goto :goto_0

    :cond_0
    invoke-virtual {p1}, Lcom/google/android/play/core/appupdate/a;->d()I

    move-result v0

    const/4 v1, 0x2

    if-ne v0, v1, :cond_2

    invoke-virtual {p1, v7}, Lcom/google/android/play/core/appupdate/a;->b(I)Z

    move-result v0

    if-eqz v0, :cond_2

    iget-object v0, p0, Lcom/transsion/subroom/update/GPUpdateManager$checkAppUpdate$1;->this$0:Lcom/transsion/subroom/update/GPUpdateManager;

    invoke-static {v0}, Lcom/transsion/subroom/update/GPUpdateManager;->f(Lcom/transsion/subroom/update/GPUpdateManager;)Z

    move-result v0

    if-eqz v0, :cond_2

    const-string v1, "GPUpdateManager"

    const-string v2, "canShow"

    const/4 v3, 0x0

    const/4 v4, 0x4

    const/4 v5, 0x0

    move-object v0, v6

    invoke-static/range {v0 .. v5}, Lxi/b$a;->f(Lxi/b$a;Ljava/lang/String;Ljava/lang/String;ZILjava/lang/Object;)V

    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    sget-object v1, Lcom/transsion/baselib/report/l;->a:Lcom/transsion/baselib/report/l;

    const-string v2, "gp_update_dialog"

    const-string v3, "browse"

    invoke-virtual {v1, v2, v3, v0}, Lcom/transsion/baselib/report/l;->q(Ljava/lang/String;Ljava/lang/String;Ljava/util/Map;)V

    iget-object v0, p0, Lcom/transsion/subroom/update/GPUpdateManager$checkAppUpdate$1;->this$0:Lcom/transsion/subroom/update/GPUpdateManager;

    invoke-static {v0}, Lcom/transsion/subroom/update/GPUpdateManager;->g(Lcom/transsion/subroom/update/GPUpdateManager;)Lcom/google/android/play/core/appupdate/b;

    move-result-object v0

    if-eqz v0, :cond_1

    iget-object v1, p0, Lcom/transsion/subroom/update/GPUpdateManager$checkAppUpdate$1;->$activity:Landroid/app/Activity;

    const v2, 0xff01

    invoke-interface {v0, p1, v7, v1, v2}, Lcom/google/android/play/core/appupdate/b;->e(Lcom/google/android/play/core/appupdate/a;ILandroid/app/Activity;I)Z

    :cond_1
    sget-object p1, Lcom/transsion/baselib/report/launch/RoomAppMMKV;->a:Lcom/transsion/baselib/report/launch/RoomAppMMKV;

    invoke-virtual {p1}, Lcom/transsion/baselib/report/launch/RoomAppMMKV;->a()Lcom/tencent/mmkv/MMKV;

    move-result-object p1

    const-string v0, "app_update_show_time"

    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v1

    invoke-virtual {p1, v0, v1, v2}, Lcom/tencent/mmkv/MMKV;->putLong(Ljava/lang/String;J)Landroid/content/SharedPreferences$Editor;

    :cond_2
    :goto_0
    return-void
.end method
