.class public final Lcom/transsion/subroom/update/GPUpdateManager;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/transsion/subroom/update/GPUpdateManager$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation


# static fields
.field public static final c:Lcom/transsion/subroom/update/GPUpdateManager$a;


# instance fields
.field public a:Lcom/google/android/play/core/appupdate/b;

.field public final b:Lqd/a;


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lcom/transsion/subroom/update/GPUpdateManager$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lcom/transsion/subroom/update/GPUpdateManager$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lcom/transsion/subroom/update/GPUpdateManager;->c:Lcom/transsion/subroom/update/GPUpdateManager$a;

    return-void
.end method

.method public constructor <init>()V
    .locals 1

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    new-instance v0, Lcom/transsion/subroom/update/a;

    invoke-direct {v0, p0}, Lcom/transsion/subroom/update/a;-><init>(Lcom/transsion/subroom/update/GPUpdateManager;)V

    iput-object v0, p0, Lcom/transsion/subroom/update/GPUpdateManager;->b:Lqd/a;

    return-void
.end method

.method public static synthetic a(Lkotlin/jvm/functions/Function1;Ljava/lang/Object;)V
    .locals 0

    invoke-static {p0, p1}, Lcom/transsion/subroom/update/GPUpdateManager;->k(Lkotlin/jvm/functions/Function1;Ljava/lang/Object;)V

    return-void
.end method

.method public static synthetic b(Lcom/transsion/subroom/update/GPUpdateManager;Lcom/google/android/play/core/install/InstallState;)V
    .locals 0

    invoke-static {p0, p1}, Lcom/transsion/subroom/update/GPUpdateManager;->m(Lcom/transsion/subroom/update/GPUpdateManager;Lcom/google/android/play/core/install/InstallState;)V

    return-void
.end method

.method public static synthetic c(Lcom/transsion/subroom/update/GPUpdateManager;Landroid/view/View;)V
    .locals 0

    invoke-static {p0, p1}, Lcom/transsion/subroom/update/GPUpdateManager;->p(Lcom/transsion/subroom/update/GPUpdateManager;Landroid/view/View;)V

    return-void
.end method

.method public static synthetic d(Ljava/lang/Exception;)V
    .locals 0

    invoke-static {p0}, Lcom/transsion/subroom/update/GPUpdateManager;->l(Ljava/lang/Exception;)V

    return-void
.end method

.method public static synthetic e(Lcom/transsion/subroom/update/GPUpdateManager;Landroid/view/View;)V
    .locals 0

    invoke-static {p0, p1}, Lcom/transsion/subroom/update/GPUpdateManager;->q(Lcom/transsion/subroom/update/GPUpdateManager;Landroid/view/View;)V

    return-void
.end method

.method public static final synthetic f(Lcom/transsion/subroom/update/GPUpdateManager;)Z
    .locals 0

    invoke-virtual {p0}, Lcom/transsion/subroom/update/GPUpdateManager;->i()Z

    move-result p0

    return p0
.end method

.method public static final synthetic g(Lcom/transsion/subroom/update/GPUpdateManager;)Lcom/google/android/play/core/appupdate/b;
    .locals 0

    iget-object p0, p0, Lcom/transsion/subroom/update/GPUpdateManager;->a:Lcom/google/android/play/core/appupdate/b;

    return-object p0
.end method

.method public static final synthetic h(Lcom/transsion/subroom/update/GPUpdateManager;)V
    .locals 0

    invoke-virtual {p0}, Lcom/transsion/subroom/update/GPUpdateManager;->o()V

    return-void
.end method

.method public static final k(Lkotlin/jvm/functions/Function1;Ljava/lang/Object;)V
    .locals 1

    const-string v0, "$tmp0"

    invoke-static {p0, v0}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-interface {p0, p1}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    return-void
.end method

.method public static final l(Ljava/lang/Exception;)V
    .locals 7

    const-string v0, "it"

    invoke-static {p0, v0}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    sget-object v1, Lxi/b;->a:Lxi/b$a;

    const-string v2, "GPUpdateManager"

    invoke-virtual {p0}, Ljava/lang/Throwable;->getMessage()Ljava/lang/String;

    move-result-object p0

    invoke-static {p0}, Ljava/lang/String;->valueOf(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v3

    const/4 v4, 0x0

    const/4 v5, 0x4

    const/4 v6, 0x0

    invoke-static/range {v1 .. v6}, Lxi/b$a;->f(Lxi/b$a;Ljava/lang/String;Ljava/lang/String;ZILjava/lang/Object;)V

    return-void
.end method

.method public static final m(Lcom/transsion/subroom/update/GPUpdateManager;Lcom/google/android/play/core/install/InstallState;)V
    .locals 1

    const-string v0, "this$0"

    invoke-static {p0, v0}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    const-string v0, "state"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-virtual {p1}, Lcom/google/android/play/core/install/InstallState;->c()I

    move-result p1

    const/16 v0, 0xb

    if-ne p1, v0, :cond_0

    invoke-virtual {p0}, Lcom/transsion/subroom/update/GPUpdateManager;->o()V

    :cond_0
    return-void
.end method

.method public static final p(Lcom/transsion/subroom/update/GPUpdateManager;Landroid/view/View;)V
    .locals 0

    const-string p1, "this$0"

    invoke-static {p0, p1}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    iget-object p0, p0, Lcom/transsion/subroom/update/GPUpdateManager;->a:Lcom/google/android/play/core/appupdate/b;

    if-eqz p0, :cond_0

    invoke-interface {p0}, Lcom/google/android/play/core/appupdate/b;->c()Lcom/google/android/gms/tasks/j;

    :cond_0
    return-void
.end method

.method public static final q(Lcom/transsion/subroom/update/GPUpdateManager;Landroid/view/View;)V
    .locals 3

    const-string p1, "this$0"

    invoke-static {p0, p1}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    new-instance p1, Ljava/util/HashMap;

    invoke-direct {p1}, Ljava/util/HashMap;-><init>()V

    sget-object v0, Lcom/transsion/baselib/report/l;->a:Lcom/transsion/baselib/report/l;

    const-string v1, "gp_update_dialog"

    const-string v2, "click"

    invoke-virtual {v0, v1, v2, p1}, Lcom/transsion/baselib/report/l;->l(Ljava/lang/String;Ljava/lang/String;Ljava/util/Map;)V

    iget-object p0, p0, Lcom/transsion/subroom/update/GPUpdateManager;->a:Lcom/google/android/play/core/appupdate/b;

    if-eqz p0, :cond_0

    invoke-interface {p0}, Lcom/google/android/play/core/appupdate/b;->c()Lcom/google/android/gms/tasks/j;

    :cond_0
    return-void
.end method


# virtual methods
.method public final i()Z
    .locals 17

    sget-object v0, Lcom/transsion/mb/config/manager/ConfigManager;->c:Lcom/transsion/mb/config/manager/ConfigManager$a;

    invoke-virtual {v0}, Lcom/transsion/mb/config/manager/ConfigManager$a;->a()Lcom/transsion/mb/config/manager/ConfigManager;

    move-result-object v1

    const-string v2, "app_update_switch"

    const/4 v3, 0x0

    const/4 v4, 0x2

    const/4 v5, 0x0

    invoke-static {v1, v2, v3, v4, v5}, Lcom/transsion/mb/config/manager/ConfigManager;->c(Lcom/transsion/mb/config/manager/ConfigManager;Ljava/lang/String;ZILjava/lang/Object;)Lcom/transsion/mb/config/manager/ConfigBean;

    move-result-object v1

    if-eqz v1, :cond_0

    invoke-virtual {v1}, Lcom/transsion/mb/config/manager/ConfigBean;->e()Ljava/lang/String;

    move-result-object v1

    if-eqz v1, :cond_0

    invoke-static {v1}, Lkotlin/text/StringsKt;->V0(Ljava/lang/String;)Ljava/lang/Boolean;

    move-result-object v1

    if-eqz v1, :cond_0

    invoke-virtual {v1}, Ljava/lang/Boolean;->booleanValue()Z

    move-result v1

    goto :goto_0

    :cond_0
    const/4 v1, 0x0

    :goto_0
    sget-object v2, Lxi/b;->a:Lxi/b$a;

    const-string v7, "GPUpdateManager"

    new-instance v6, Ljava/lang/StringBuilder;

    invoke-direct {v6}, Ljava/lang/StringBuilder;-><init>()V

    const-string v8, "appUpdateSwitch="

    invoke-virtual {v6, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v6, v1}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    invoke-virtual {v6}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v8

    const/4 v9, 0x0

    const/4 v10, 0x4

    const/4 v11, 0x0

    move-object v6, v2

    invoke-static/range {v6 .. v11}, Lxi/b$a;->f(Lxi/b$a;Ljava/lang/String;Ljava/lang/String;ZILjava/lang/Object;)V

    if-nez v1, :cond_1

    return v3

    :cond_1
    sget-object v1, Lcom/transsion/baselib/report/launch/RoomAppMMKV;->a:Lcom/transsion/baselib/report/launch/RoomAppMMKV;

    invoke-virtual {v1}, Lcom/transsion/baselib/report/launch/RoomAppMMKV;->a()Lcom/tencent/mmkv/MMKV;

    move-result-object v6

    const-string v7, "k_first_started"

    const/4 v12, 0x1

    invoke-virtual {v6, v7, v12}, Lcom/tencent/mmkv/MMKV;->getBoolean(Ljava/lang/String;Z)Z

    move-result v6

    if-eqz v6, :cond_2

    invoke-virtual {v1}, Lcom/transsion/baselib/report/launch/RoomAppMMKV;->a()Lcom/tencent/mmkv/MMKV;

    move-result-object v0

    invoke-virtual {v0, v7, v3}, Lcom/tencent/mmkv/MMKV;->putBoolean(Ljava/lang/String;Z)Landroid/content/SharedPreferences$Editor;

    return v3

    :cond_2
    invoke-virtual {v1}, Lcom/transsion/baselib/report/launch/RoomAppMMKV;->a()Lcom/tencent/mmkv/MMKV;

    move-result-object v1

    const-string v6, "app_update_show_time"

    const-wide/16 v7, 0x0

    invoke-virtual {v1, v6, v7, v8}, Lcom/tencent/mmkv/MMKV;->getLong(Ljava/lang/String;J)J

    move-result-wide v9

    const v1, 0x5265c00

    int-to-long v13, v1

    div-long v15, v9, v13

    invoke-virtual {v0}, Lcom/transsion/mb/config/manager/ConfigManager$a;->a()Lcom/transsion/mb/config/manager/ConfigManager;

    move-result-object v0

    const-string v1, "app_update_interval_days"

    invoke-static {v0, v1, v3, v4, v5}, Lcom/transsion/mb/config/manager/ConfigManager;->c(Lcom/transsion/mb/config/manager/ConfigManager;Ljava/lang/String;ZILjava/lang/Object;)Lcom/transsion/mb/config/manager/ConfigBean;

    move-result-object v0

    if-eqz v0, :cond_3

    invoke-virtual {v0}, Lcom/transsion/mb/config/manager/ConfigBean;->e()Ljava/lang/String;

    move-result-object v0

    if-eqz v0, :cond_3

    invoke-static {v0}, Lkotlin/text/StringsKt;->m(Ljava/lang/String;)Ljava/lang/Long;

    move-result-object v0

    if-eqz v0, :cond_3

    invoke-virtual {v0}, Ljava/lang/Long;->longValue()J

    move-result-wide v7

    :cond_3
    move-wide v0, v7

    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v4

    div-long/2addr v4, v13

    const-string v7, "GPUpdateManager"

    new-instance v6, Ljava/lang/StringBuilder;

    invoke-direct {v6}, Ljava/lang/StringBuilder;-><init>()V

    const-string v8, "appUpdateIntervalDays="

    invoke-virtual {v6, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v6, v0, v1}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    invoke-virtual {v6}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v8

    const/4 v9, 0x0

    const/4 v10, 0x4

    const/4 v11, 0x0

    move-object v6, v2

    invoke-static/range {v6 .. v11}, Lxi/b$a;->f(Lxi/b$a;Ljava/lang/String;Ljava/lang/String;ZILjava/lang/Object;)V

    sub-long/2addr v4, v15

    cmp-long v2, v4, v0

    if-lez v2, :cond_4

    const/4 v3, 0x1

    :cond_4
    return v3
.end method

.method public final j(Landroid/app/Activity;)V
    .locals 2

    const-string v0, "activity"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-static {p1}, Lcom/google/android/play/core/appupdate/c;->a(Landroid/content/Context;)Lcom/google/android/play/core/appupdate/b;

    move-result-object v0

    iput-object v0, p0, Lcom/transsion/subroom/update/GPUpdateManager;->a:Lcom/google/android/play/core/appupdate/b;

    if-eqz v0, :cond_0

    invoke-interface {v0}, Lcom/google/android/play/core/appupdate/b;->d()Lcom/google/android/gms/tasks/j;

    move-result-object v0

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    if-eqz v0, :cond_1

    new-instance v1, Lcom/transsion/subroom/update/GPUpdateManager$checkAppUpdate$1;

    invoke-direct {v1, p0, p1}, Lcom/transsion/subroom/update/GPUpdateManager$checkAppUpdate$1;-><init>(Lcom/transsion/subroom/update/GPUpdateManager;Landroid/app/Activity;)V

    new-instance p1, Lcom/transsion/subroom/update/b;

    invoke-direct {p1, v1}, Lcom/transsion/subroom/update/b;-><init>(Lkotlin/jvm/functions/Function1;)V

    invoke-virtual {v0, p1}, Lcom/google/android/gms/tasks/j;->g(Lcom/google/android/gms/tasks/g;)Lcom/google/android/gms/tasks/j;

    :cond_1
    if-eqz v0, :cond_2

    new-instance p1, Lcom/transsion/subroom/update/c;

    invoke-direct {p1}, Lcom/transsion/subroom/update/c;-><init>()V

    invoke-virtual {v0, p1}, Lcom/google/android/gms/tasks/j;->e(Lcom/google/android/gms/tasks/f;)Lcom/google/android/gms/tasks/j;

    :cond_2
    invoke-virtual {p0}, Lcom/transsion/subroom/update/GPUpdateManager;->r()V

    return-void
.end method

.method public final n(IILandroid/content/Intent;)V
    .locals 0

    return-void
.end method

.method public final o()V
    .locals 3

    sget-object v0, Lcom/transsion/baselib/report/RoomActivityLifecycleCallbacks;->a:Lcom/transsion/baselib/report/RoomActivityLifecycleCallbacks;

    invoke-virtual {v0}, Lcom/transsion/baselib/report/RoomActivityLifecycleCallbacks;->g()Landroid/app/Activity;

    move-result-object v0

    if-eqz v0, :cond_0

    instance-of v1, v0, Lcom/transsion/baseui/activity/BaseCommonActivity;

    if-eqz v1, :cond_0

    check-cast v0, Lcom/transsion/baseui/activity/BaseCommonActivity;

    invoke-virtual {v0}, Lcom/transsion/baseui/activity/BaseCommonActivity;->getMViewBinding()Ls4/a;

    move-result-object v0

    invoke-interface {v0}, Ls4/a;->getRoot()Landroid/view/View;

    move-result-object v0

    invoke-virtual {v0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v1

    sget v2, Lcom/transsion/subroom/R$string;->install_tip:I

    invoke-virtual {v1, v2}, Landroid/content/Context;->getString(I)Ljava/lang/String;

    move-result-object v1

    const/4 v2, -0x2

    invoke-static {v0, v1, v2}, Lcom/google/android/material/snackbar/Snackbar;->n0(Landroid/view/View;Ljava/lang/CharSequence;I)Lcom/google/android/material/snackbar/Snackbar;

    move-result-object v1

    invoke-virtual {v0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    sget v2, Lcom/transsion/subroom/R$string;->install_update:I

    invoke-virtual {v0, v2}, Landroid/content/Context;->getString(I)Ljava/lang/String;

    move-result-object v0

    new-instance v2, Lcom/transsion/subroom/update/d;

    invoke-direct {v2, p0}, Lcom/transsion/subroom/update/d;-><init>(Lcom/transsion/subroom/update/GPUpdateManager;)V

    invoke-virtual {v1, v0, v2}, Lcom/google/android/material/snackbar/Snackbar;->q0(Ljava/lang/CharSequence;Landroid/view/View$OnClickListener;)Lcom/google/android/material/snackbar/Snackbar;

    invoke-virtual {v1}, Lcom/google/android/material/snackbar/BaseTransientBottomBar;->H()Landroid/view/View;

    move-result-object v0

    new-instance v2, Lcom/transsion/subroom/update/e;

    invoke-direct {v2, p0}, Lcom/transsion/subroom/update/e;-><init>(Lcom/transsion/subroom/update/GPUpdateManager;)V

    invoke-virtual {v0, v2}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    invoke-virtual {v1}, Lcom/google/android/material/snackbar/Snackbar;->X()V

    :cond_0
    return-void
.end method

.method public final r()V
    .locals 2

    iget-object v0, p0, Lcom/transsion/subroom/update/GPUpdateManager;->a:Lcom/google/android/play/core/appupdate/b;

    if-eqz v0, :cond_0

    iget-object v1, p0, Lcom/transsion/subroom/update/GPUpdateManager;->b:Lqd/a;

    invoke-interface {v0, v1}, Lcom/google/android/play/core/appupdate/b;->a(Lqd/a;)V

    :cond_0
    return-void
.end method

.method public final s()V
    .locals 2

    iget-object v0, p0, Lcom/transsion/subroom/update/GPUpdateManager;->a:Lcom/google/android/play/core/appupdate/b;

    if-eqz v0, :cond_0

    iget-object v1, p0, Lcom/transsion/subroom/update/GPUpdateManager;->b:Lqd/a;

    invoke-interface {v0, v1}, Lcom/google/android/play/core/appupdate/b;->b(Lqd/a;)V

    :cond_0
    return-void
.end method
