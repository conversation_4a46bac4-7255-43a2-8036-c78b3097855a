.class public final synthetic Lcom/transsion/subroom/update/a;
.super Ljava/lang/Object;

# interfaces
.implements Lqd/a;


# instance fields
.field public final synthetic a:Lcom/transsion/subroom/update/GPUpdateManager;


# direct methods
.method public synthetic constructor <init>(Lcom/transsion/subroom/update/GPUpdateManager;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/transsion/subroom/update/a;->a:Lcom/transsion/subroom/update/GPUpdateManager;

    return-void
.end method


# virtual methods
.method public final a(Ljava/lang/Object;)V
    .locals 1

    iget-object v0, p0, Lcom/transsion/subroom/update/a;->a:Lcom/transsion/subroom/update/GPUpdateManager;

    check-cast p1, Lcom/google/android/play/core/install/InstallState;

    invoke-static {v0, p1}, Lcom/transsion/subroom/update/GPUpdateManager;->b(Lcom/transsion/subroom/update/GPUpdateManager;Lcom/google/android/play/core/install/InstallState;)V

    return-void
.end method
