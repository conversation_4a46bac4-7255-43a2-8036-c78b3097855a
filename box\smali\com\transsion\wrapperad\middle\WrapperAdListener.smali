.class public abstract Lcom/transsion/wrapperad/middle/WrapperAdListener;
.super Lcom/hisavana/common/interfacz/TAdListener;


# annotations
.annotation runtime Lkotlin/Metadata;
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Lcom/hisavana/common/interfacz/TAdListener;-><init>()V

    return-void
.end method


# virtual methods
.method public onBannerViewReady(Landroid/view/View;)V
    .locals 0

    return-void
.end method

.method public onClicked(I)V
    .locals 0

    return-void
.end method

.method public onClosed(I)V
    .locals 0

    return-void
.end method

.method public onClosed(Lcom/hisavana/common/bean/TAdNativeInfo;)V
    .locals 0

    invoke-super {p0, p1}, Lcom/hisavana/common/interfacz/TAdListener;->onClosed(Lcom/hisavana/common/bean/TAdNativeInfo;)V

    return-void
.end method

.method public onDestroy()V
    .locals 0

    return-void
.end method

.method public onError(Lcom/hisavana/common/bean/TAdErrorCode;)V
    .locals 0

    return-void
.end method

.method public onIconAdReady(Ljava/util/List;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lcom/hisavana/common/bean/TAdNativeInfo;",
            ">;)V"
        }
    .end annotation

    const-string v0, "tAdNativeInfos"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    return-void
.end method

.method public onLoad()V
    .locals 0

    return-void

    invoke-super {p0}, Lcom/hisavana/common/interfacz/TAdListener;->onLoad()V

    return-void
.end method

.method public onNativeInfoReady(Lcom/hisavana/common/bean/TAdNativeInfo;)V
    .locals 0

    return-void
.end method

.method public onPlanAdShowError(Lcom/hisavana/common/bean/TAdErrorCode;)V
    .locals 0

    return-void
.end method

.method public onRewarded()V
    .locals 0

    invoke-super {p0}, Lcom/hisavana/common/interfacz/TAdListener;->onRewarded()V

    return-void
.end method

.method public onShow(I)V
    .locals 0

    return-void
.end method
