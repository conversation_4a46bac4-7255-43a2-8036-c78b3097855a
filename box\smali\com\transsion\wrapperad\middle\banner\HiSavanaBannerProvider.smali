.class public final Lcom/transsion/wrapperad/middle/banner/HiSavanaBannerProvider;
.super Lcom/hisavana/common/interfacz/TAdListener;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/transsion/wrapperad/middle/banner/HiSavanaBannerProvider$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation


# static fields
.field public static final AD_BANNER_STATE_ERROR:I = 0x2

.field public static final AD_BANNER_STATE_LOADED:I = 0x3

.field public static final AD_BANNER_STATE_LOADING:I = 0x1

.field public static final AD_BANNER_STATE_NONE:I = 0x0

.field public static final AD_BANNER_STATE_SHOWED:I = 0x4

.field public static final Companion:Lcom/transsion/wrapperad/middle/banner/HiSavanaBannerProvider$a;


# instance fields
.field private adview:Lcom/hisavana/mediation/ad/TBannerView;

.field private currentState:I

.field private mHiSavanaId:Ljava/lang/String;

.field private mListener:Lcom/transsion/wrapperad/middle/WrapperAdListener;

.field private final mSceneId:Ljava/lang/String;


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lcom/transsion/wrapperad/middle/banner/HiSavanaBannerProvider$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lcom/transsion/wrapperad/middle/banner/HiSavanaBannerProvider$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lcom/transsion/wrapperad/middle/banner/HiSavanaBannerProvider;->Companion:Lcom/transsion/wrapperad/middle/banner/HiSavanaBannerProvider$a;

    return-void
.end method

.method public constructor <init>(Ljava/lang/String;)V
    .locals 0

    invoke-direct {p0}, Lcom/hisavana/common/interfacz/TAdListener;-><init>()V

    iput-object p1, p0, Lcom/transsion/wrapperad/middle/banner/HiSavanaBannerProvider;->mSceneId:Ljava/lang/String;

    return-void
.end method


# virtual methods
.method public final destroy()V
    .locals 5

    sget-object v0, Lqt/a;->a:Lqt/a;

    iget-object v1, p0, Lcom/transsion/wrapperad/middle/banner/HiSavanaBannerProvider;->mSceneId:Ljava/lang/String;

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "TrendingBannerProvider --> destroy() -- sceneId = "

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    const/4 v2, 0x0

    const/4 v3, 0x2

    const/4 v4, 0x0

    invoke-static {v0, v1, v2, v3, v4}, Lqt/a;->t(Lqt/a;Ljava/lang/String;ZILjava/lang/Object;)V

    iput-object v4, p0, Lcom/transsion/wrapperad/middle/banner/HiSavanaBannerProvider;->mListener:Lcom/transsion/wrapperad/middle/WrapperAdListener;

    iget-object v0, p0, Lcom/transsion/wrapperad/middle/banner/HiSavanaBannerProvider;->adview:Lcom/hisavana/mediation/ad/TBannerView;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Landroid/view/View;->getParent()Landroid/view/ViewParent;

    move-result-object v0

    goto :goto_0

    :cond_0
    move-object v0, v4

    :goto_0
    instance-of v1, v0, Landroid/view/ViewGroup;

    if-eqz v1, :cond_1

    check-cast v0, Landroid/view/ViewGroup;

    goto :goto_1

    :cond_1
    move-object v0, v4

    :goto_1
    if-eqz v0, :cond_2

    iget-object v1, p0, Lcom/transsion/wrapperad/middle/banner/HiSavanaBannerProvider;->adview:Lcom/hisavana/mediation/ad/TBannerView;

    invoke-virtual {v0, v1}, Landroid/view/ViewGroup;->removeView(Landroid/view/View;)V

    :cond_2
    iget-object v0, p0, Lcom/transsion/wrapperad/middle/banner/HiSavanaBannerProvider;->adview:Lcom/hisavana/mediation/ad/TBannerView;

    if-eqz v0, :cond_3

    invoke-virtual {v0}, Lcom/hisavana/mediation/ad/TBannerView;->destroy()V

    :cond_3
    iput-object v4, p0, Lcom/transsion/wrapperad/middle/banner/HiSavanaBannerProvider;->adview:Lcom/hisavana/mediation/ad/TBannerView;

    return-void
.end method

.method public final getMSceneId()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/transsion/wrapperad/middle/banner/HiSavanaBannerProvider;->mSceneId:Ljava/lang/String;

    return-object v0
.end method

.method public final loadAd(Lcom/transsion/wrapperad/middle/WrapperAdListener;Ljava/util/Map;)V
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/transsion/wrapperad/middle/WrapperAdListener;",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "+",
            "Ljava/lang/Object;",
            ">;)V"
        }
    .end annotation

    const-string v0, "listener"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    const-string v0, "ctxMap"

    invoke-static {p2, v0}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    iput-object p1, p0, Lcom/transsion/wrapperad/middle/banner/HiSavanaBannerProvider;->mListener:Lcom/transsion/wrapperad/middle/WrapperAdListener;

    sget-object p1, Lpt/b;->a:Lpt/b;

    iget-object v0, p0, Lcom/transsion/wrapperad/middle/banner/HiSavanaBannerProvider;->mSceneId:Ljava/lang/String;

    invoke-virtual {p1, v0}, Lpt/b;->a(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    iput-object p1, p0, Lcom/transsion/wrapperad/middle/banner/HiSavanaBannerProvider;->mHiSavanaId:Ljava/lang/String;

    const/4 p1, 0x1

    iput p1, p0, Lcom/transsion/wrapperad/middle/banner/HiSavanaBannerProvider;->currentState:I

    sget-object v0, Lcom/transsion/wrapperad/hi/HiSavanaAdManager;->a:Lcom/transsion/wrapperad/hi/HiSavanaAdManager;

    invoke-virtual {v0}, Lcom/transsion/wrapperad/hi/HiSavanaAdManager;->f()Z

    move-result v0

    const/4 v1, 0x2

    if-nez v0, :cond_1

    sget-object p1, Lqt/a;->a:Lqt/a;

    const-class p2, Lcom/transsion/wrapperad/middle/banner/HiSavanaBannerProvider;

    invoke-virtual {p2}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    move-result-object p2

    iget-object v0, p0, Lcom/transsion/wrapperad/middle/banner/HiSavanaBannerProvider;->mSceneId:Ljava/lang/String;

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v2, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string p2, " --> loadAd() --> HiSavana \u5e7f\u544aSDK\u672a\u521d\u59cb\u5316 -- sceneId = "

    invoke-virtual {v2, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    const/4 v0, 0x0

    const/4 v2, 0x0

    invoke-static {p1, p2, v0, v1, v2}, Lqt/a;->v(Lqt/a;Ljava/lang/String;ZILjava/lang/Object;)V

    iget-object p1, p0, Lcom/transsion/wrapperad/middle/banner/HiSavanaBannerProvider;->mListener:Lcom/transsion/wrapperad/middle/WrapperAdListener;

    if-eqz p1, :cond_0

    new-instance p2, Lcom/hisavana/common/bean/TAdErrorCode;

    iget-object v0, p0, Lcom/transsion/wrapperad/middle/banner/HiSavanaBannerProvider;->mSceneId:Ljava/lang/String;

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "HiSavana \u5e7f\u544aSDK\u672a\u521d\u59cb\u5316 -- sceneId = "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    const/16 v1, 0x67

    invoke-direct {p2, v1, v0}, Lcom/hisavana/common/bean/TAdErrorCode;-><init>(ILjava/lang/String;)V

    invoke-virtual {p1, p2}, Lcom/transsion/wrapperad/middle/WrapperAdListener;->onError(Lcom/hisavana/common/bean/TAdErrorCode;)V

    :cond_0
    return-void

    :cond_1
    iget-object v0, p0, Lcom/transsion/wrapperad/middle/banner/HiSavanaBannerProvider;->adview:Lcom/hisavana/mediation/ad/TBannerView;

    if-nez v0, :cond_5

    new-instance v0, Lcom/hisavana/mediation/ad/TBannerView;

    invoke-static {}, Lcom/blankj/utilcode/util/Utils;->a()Landroid/app/Application;

    move-result-object v2

    invoke-direct {v0, v2}, Lcom/hisavana/mediation/ad/TBannerView;-><init>(Landroid/content/Context;)V

    iput-object v0, p0, Lcom/transsion/wrapperad/middle/banner/HiSavanaBannerProvider;->adview:Lcom/hisavana/mediation/ad/TBannerView;

    sget-object v0, Lcom/transsion/wrapperad/strategy/b;->a:Lcom/transsion/wrapperad/strategy/b;

    invoke-virtual {v0, p2}, Lcom/transsion/wrapperad/strategy/b;->d(Ljava/util/Map;)Z

    move-result p2

    if-eqz p2, :cond_2

    iget-object p2, p0, Lcom/transsion/wrapperad/middle/banner/HiSavanaBannerProvider;->adview:Lcom/hisavana/mediation/ad/TBannerView;

    if-eqz p2, :cond_2

    invoke-virtual {p2, p1}, Lcom/hisavana/mediation/ad/TBannerView;->setContainVulgarContent(Z)V

    :cond_2
    iget-object p1, p0, Lcom/transsion/wrapperad/middle/banner/HiSavanaBannerProvider;->adview:Lcom/hisavana/mediation/ad/TBannerView;

    if-eqz p1, :cond_3

    invoke-virtual {p1, v1}, Lcom/hisavana/mediation/ad/TBannerView;->setAdSize(I)V

    :cond_3
    iget-object p1, p0, Lcom/transsion/wrapperad/middle/banner/HiSavanaBannerProvider;->adview:Lcom/hisavana/mediation/ad/TBannerView;

    if-eqz p1, :cond_4

    iget-object p2, p0, Lcom/transsion/wrapperad/middle/banner/HiSavanaBannerProvider;->mHiSavanaId:Ljava/lang/String;

    invoke-virtual {p1, p2}, Lcom/hisavana/mediation/ad/TBannerView;->setAdUnitId(Ljava/lang/String;)V

    :cond_4
    new-instance p1, Lcom/hisavana/common/bean/TAdRequestBody$AdRequestBodyBuild;

    invoke-direct {p1}, Lcom/hisavana/common/bean/TAdRequestBody$AdRequestBodyBuild;-><init>()V

    invoke-virtual {p1, p0}, Lcom/hisavana/common/bean/TAdRequestBody$AdRequestBodyBuild;->setAdListener(Lcom/hisavana/common/interfacz/TAdListener;)Lcom/hisavana/common/bean/TAdRequestBody$AdRequestBodyBuild;

    move-result-object p1

    invoke-virtual {p1}, Lcom/hisavana/common/bean/TAdRequestBody$AdRequestBodyBuild;->build()Lcom/hisavana/common/bean/TAdRequestBody;

    move-result-object p1

    iget-object p2, p0, Lcom/transsion/wrapperad/middle/banner/HiSavanaBannerProvider;->adview:Lcom/hisavana/mediation/ad/TBannerView;

    if-eqz p2, :cond_5

    invoke-virtual {p2, p1}, Lcom/hisavana/mediation/ad/TBannerView;->setRequestBody(Lcom/hisavana/common/bean/TAdRequestBody;)V

    :cond_5
    iget-object p1, p0, Lcom/transsion/wrapperad/middle/banner/HiSavanaBannerProvider;->adview:Lcom/hisavana/mediation/ad/TBannerView;

    if-eqz p1, :cond_6

    invoke-virtual {p1}, Lcom/hisavana/mediation/ad/TBannerView;->loadAd()V

    :cond_6
    return-void
.end method

.method public onClicked(I)V
    .locals 17

    move-object/from16 v0, p0

    sget-object v1, Lqt/a;->a:Lqt/a;

    const-class v2, Lcom/transsion/wrapperad/middle/banner/HiSavanaBannerProvider;

    invoke-virtual {v2}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    move-result-object v2

    iget-object v3, v0, Lcom/transsion/wrapperad/middle/banner/HiSavanaBannerProvider;->mHiSavanaId:Ljava/lang/String;

    iget-object v4, v0, Lcom/transsion/wrapperad/middle/banner/HiSavanaBannerProvider;->mSceneId:Ljava/lang/String;

    new-instance v5, Ljava/lang/StringBuilder;

    invoke-direct {v5}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v5, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v2, " --> onClicked() -- mHiSavanaId = "

    invoke-virtual {v5, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v5, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v2, " -- sceneId = "

    invoke-virtual {v5, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v5, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v5}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    const/4 v3, 0x2

    const/4 v4, 0x0

    const/4 v5, 0x0

    invoke-static {v1, v2, v5, v3, v4}, Lqt/a;->t(Lqt/a;Ljava/lang/String;ZILjava/lang/Object;)V

    sget-object v6, Lcom/transsion/wrapperad/a;->a:Lcom/transsion/wrapperad/a;

    const/4 v7, 0x0

    iget-object v8, v0, Lcom/transsion/wrapperad/middle/banner/HiSavanaBannerProvider;->mSceneId:Ljava/lang/String;

    const-string v9, ""

    iget-object v11, v0, Lcom/transsion/wrapperad/middle/banner/HiSavanaBannerProvider;->mHiSavanaId:Ljava/lang/String;

    const/4 v12, 0x1

    const/4 v13, 0x0

    const/4 v14, 0x0

    const/16 v15, 0x81

    const/16 v16, 0x0

    move/from16 v10, p1

    invoke-static/range {v6 .. v16}, Lcom/transsion/wrapperad/a;->b(Lcom/transsion/wrapperad/a;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;ILjava/lang/String;IZLjava/util/Map;ILjava/lang/Object;)V

    return-void
.end method

.method public onClosed(I)V
    .locals 0

    return-void
.end method

.method public onError(Lcom/hisavana/common/bean/TAdErrorCode;)V
    .locals 5

    sget-object v0, Lqt/a;->a:Lqt/a;

    const-class v1, Lcom/transsion/wrapperad/middle/banner/HiSavanaBannerProvider;

    invoke-virtual {v1}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    move-result-object v1

    iget-object v2, p0, Lcom/transsion/wrapperad/middle/banner/HiSavanaBannerProvider;->mHiSavanaId:Ljava/lang/String;

    iget-object v3, p0, Lcom/transsion/wrapperad/middle/banner/HiSavanaBannerProvider;->mSceneId:Ljava/lang/String;

    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v4, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v1, " --> onError() -- mHiSavanaId = "

    invoke-virtual {v4, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v1, " -- p0 = "

    invoke-virtual {v4, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v1, " -- sceneId = "

    invoke-virtual {v4, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    const/4 v2, 0x0

    const/4 v3, 0x0

    const/4 v4, 0x2

    invoke-static {v0, v1, v3, v4, v2}, Lqt/a;->v(Lqt/a;Ljava/lang/String;ZILjava/lang/Object;)V

    iput v4, p0, Lcom/transsion/wrapperad/middle/banner/HiSavanaBannerProvider;->currentState:I

    iget-object v0, p0, Lcom/transsion/wrapperad/middle/banner/HiSavanaBannerProvider;->mListener:Lcom/transsion/wrapperad/middle/WrapperAdListener;

    if-eqz v0, :cond_0

    invoke-virtual {v0, p1}, Lcom/transsion/wrapperad/middle/WrapperAdListener;->onError(Lcom/hisavana/common/bean/TAdErrorCode;)V

    :cond_0
    return-void
.end method

.method public onLoad()V
    .locals 5

    invoke-super {p0}, Lcom/hisavana/common/interfacz/TAdListener;->onLoad()V

    sget-object v0, Lqt/a;->a:Lqt/a;

    const-class v1, Lcom/transsion/wrapperad/middle/banner/HiSavanaBannerProvider;

    invoke-virtual {v1}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    move-result-object v1

    iget-object v2, p0, Lcom/transsion/wrapperad/middle/banner/HiSavanaBannerProvider;->mHiSavanaId:Ljava/lang/String;

    iget-object v3, p0, Lcom/transsion/wrapperad/middle/banner/HiSavanaBannerProvider;->mSceneId:Ljava/lang/String;

    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v4, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v1, " --> onLoad() -- mHiSavanaId = "

    invoke-virtual {v4, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v1, " -- sceneId = "

    invoke-virtual {v4, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    const/4 v2, 0x0

    invoke-virtual {v0, v1, v2}, Lqt/a;->s(Ljava/lang/String;Z)V

    const/4 v0, 0x3

    iput v0, p0, Lcom/transsion/wrapperad/middle/banner/HiSavanaBannerProvider;->currentState:I

    iget-object v0, p0, Lcom/transsion/wrapperad/middle/banner/HiSavanaBannerProvider;->mListener:Lcom/transsion/wrapperad/middle/WrapperAdListener;

    if-eqz v0, :cond_0

    iget-object v1, p0, Lcom/transsion/wrapperad/middle/banner/HiSavanaBannerProvider;->adview:Lcom/hisavana/mediation/ad/TBannerView;

    invoke-virtual {v0, v1}, Lcom/transsion/wrapperad/middle/WrapperAdListener;->onBannerViewReady(Landroid/view/View;)V

    :cond_0
    return-void
.end method

.method public onShow(I)V
    .locals 12

    sget-object v0, Lqt/a;->a:Lqt/a;

    const-class v1, Lcom/transsion/wrapperad/middle/banner/HiSavanaBannerProvider;

    invoke-virtual {v1}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    move-result-object v1

    iget-object v2, p0, Lcom/transsion/wrapperad/middle/banner/HiSavanaBannerProvider;->mHiSavanaId:Ljava/lang/String;

    iget-object v3, p0, Lcom/transsion/wrapperad/middle/banner/HiSavanaBannerProvider;->mSceneId:Ljava/lang/String;

    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v4, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v1, " --> onShow() -- mHiSavanaId = "

    invoke-virtual {v4, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v1, " -- sceneId = "

    invoke-virtual {v4, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    const/4 v2, 0x0

    invoke-virtual {v0, v1, v2}, Lqt/a;->s(Ljava/lang/String;Z)V

    const/4 v0, 0x4

    iput v0, p0, Lcom/transsion/wrapperad/middle/banner/HiSavanaBannerProvider;->currentState:I

    sget-object v1, Lcom/transsion/wrapperad/a;->a:Lcom/transsion/wrapperad/a;

    const/4 v2, 0x0

    iget-object v3, p0, Lcom/transsion/wrapperad/middle/banner/HiSavanaBannerProvider;->mSceneId:Ljava/lang/String;

    const-string v4, ""

    iget-object v6, p0, Lcom/transsion/wrapperad/middle/banner/HiSavanaBannerProvider;->mHiSavanaId:Ljava/lang/String;

    const/4 v7, 0x1

    const/4 v8, 0x0

    const/4 v9, 0x0

    const/16 v10, 0x81

    const/4 v11, 0x0

    move v5, p1

    invoke-static/range {v1 .. v11}, Lcom/transsion/wrapperad/a;->f(Lcom/transsion/wrapperad/a;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;ILjava/lang/String;IZLjava/util/Map;ILjava/lang/Object;)V

    return-void
.end method
