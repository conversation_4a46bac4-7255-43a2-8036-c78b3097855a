.class public final Lcom/transsion/wrapperad/middle/banner/WrapperBanner;
.super Lcom/transsion/wrapperad/middle/WrapperAdListener;


# annotations
.annotation runtime Lkotlin/Metadata;
.end annotation


# instance fields
.field private hiSavanaBanner:Lcom/transsion/wrapperad/middle/banner/HiSavanaBannerProvider;

.field private mAdPlans:Lcom/transsion/wrapperad/monopoly/model/AdPlans;

.field private mAdShowFinalPlan:Lcom/transsion/wrapperad/monopoly/model/AdPlans;

.field private mContext:Landroid/content/Context;

.field private mListener:Lcom/transsion/wrapperad/middle/WrapperAdListener;

.field private mSceneId:Ljava/lang/String;

.field private nonBannerView:Lcom/transsion/wrapperad/middle/banner/NonBannerView;


# direct methods
.method public constructor <init>()V
    .locals 1

    invoke-direct {p0}, Lcom/transsion/wrapperad/middle/WrapperAdListener;-><init>()V

    const-string v0, ""

    iput-object v0, p0, Lcom/transsion/wrapperad/middle/banner/WrapperBanner;->mSceneId:Ljava/lang/String;

    return-void
.end method

.method public static final synthetic access$innerLoadAd(Lcom/transsion/wrapperad/middle/banner/WrapperBanner;Ljava/lang/String;Landroid/content/Context;Lcom/transsion/wrapperad/middle/WrapperAdListener;Ljava/util/Map;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;
    .locals 0

    invoke-direct/range {p0 .. p5}, Lcom/transsion/wrapperad/middle/banner/WrapperBanner;->innerLoadAd(Ljava/lang/String;Landroid/content/Context;Lcom/transsion/wrapperad/middle/WrapperAdListener;Ljava/util/Map;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;

    move-result-object p0

    return-object p0
.end method

.method private final innerLoadAd(Ljava/lang/String;Landroid/content/Context;Lcom/transsion/wrapperad/middle/WrapperAdListener;Ljava/util/Map;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;
    .locals 18
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Landroid/content/Context;",
            "Lcom/transsion/wrapperad/middle/WrapperAdListener;",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "+",
            "Ljava/lang/Object;",
            ">;",
            "Lkotlin/coroutines/Continuation<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    move-object/from16 v0, p0

    move-object/from16 v1, p1

    move-object/from16 v2, p4

    move-object/from16 v3, p5

    instance-of v4, v3, Lcom/transsion/wrapperad/middle/banner/WrapperBanner$innerLoadAd$1;

    if-eqz v4, :cond_0

    move-object v4, v3

    check-cast v4, Lcom/transsion/wrapperad/middle/banner/WrapperBanner$innerLoadAd$1;

    iget v5, v4, Lcom/transsion/wrapperad/middle/banner/WrapperBanner$innerLoadAd$1;->label:I

    const/high16 v6, -0x80000000

    and-int v7, v5, v6

    if-eqz v7, :cond_0

    sub-int/2addr v5, v6

    iput v5, v4, Lcom/transsion/wrapperad/middle/banner/WrapperBanner$innerLoadAd$1;->label:I

    goto :goto_0

    :cond_0
    new-instance v4, Lcom/transsion/wrapperad/middle/banner/WrapperBanner$innerLoadAd$1;

    invoke-direct {v4, v0, v3}, Lcom/transsion/wrapperad/middle/banner/WrapperBanner$innerLoadAd$1;-><init>(Lcom/transsion/wrapperad/middle/banner/WrapperBanner;Lkotlin/coroutines/Continuation;)V

    :goto_0
    iget-object v3, v4, Lcom/transsion/wrapperad/middle/banner/WrapperBanner$innerLoadAd$1;->result:Ljava/lang/Object;

    invoke-static {}, Lkotlin/coroutines/intrinsics/IntrinsicsKt;->e()Ljava/lang/Object;

    move-result-object v5

    iget v6, v4, Lcom/transsion/wrapperad/middle/banner/WrapperBanner$innerLoadAd$1;->label:I

    const/4 v7, 0x0

    const/4 v8, 0x2

    const/4 v9, 0x1

    if-eqz v6, :cond_3

    if-eq v6, v9, :cond_2

    if-ne v6, v8, :cond_1

    iget-object v1, v4, Lcom/transsion/wrapperad/middle/banner/WrapperBanner$innerLoadAd$1;->L$3:Ljava/lang/Object;

    check-cast v1, Lcom/transsion/wrapperad/middle/banner/WrapperBanner;

    iget-object v2, v4, Lcom/transsion/wrapperad/middle/banner/WrapperBanner$innerLoadAd$1;->L$2:Ljava/lang/Object;

    check-cast v2, Ljava/util/Map;

    iget-object v5, v4, Lcom/transsion/wrapperad/middle/banner/WrapperBanner$innerLoadAd$1;->L$1:Ljava/lang/Object;

    check-cast v5, Ljava/lang/String;

    iget-object v4, v4, Lcom/transsion/wrapperad/middle/banner/WrapperBanner$innerLoadAd$1;->L$0:Ljava/lang/Object;

    check-cast v4, Lcom/transsion/wrapperad/middle/banner/WrapperBanner;

    invoke-static {v3}, Lkotlin/ResultKt;->b(Ljava/lang/Object;)V

    goto/16 :goto_2

    :cond_1
    new-instance v1, Ljava/lang/IllegalStateException;

    const-string v2, "call to \'resume\' before \'invoke\' with coroutine"

    invoke-direct {v1, v2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw v1

    :cond_2
    iget-object v1, v4, Lcom/transsion/wrapperad/middle/banner/WrapperBanner$innerLoadAd$1;->L$3:Ljava/lang/Object;

    check-cast v1, Lcom/transsion/wrapperad/middle/banner/WrapperBanner;

    iget-object v2, v4, Lcom/transsion/wrapperad/middle/banner/WrapperBanner$innerLoadAd$1;->L$2:Ljava/lang/Object;

    check-cast v2, Ljava/util/Map;

    iget-object v6, v4, Lcom/transsion/wrapperad/middle/banner/WrapperBanner$innerLoadAd$1;->L$1:Ljava/lang/Object;

    check-cast v6, Ljava/lang/String;

    iget-object v10, v4, Lcom/transsion/wrapperad/middle/banner/WrapperBanner$innerLoadAd$1;->L$0:Ljava/lang/Object;

    check-cast v10, Lcom/transsion/wrapperad/middle/banner/WrapperBanner;

    invoke-static {v3}, Lkotlin/ResultKt;->b(Ljava/lang/Object;)V

    move-object/from16 v17, v3

    move-object v3, v1

    move-object v1, v6

    move-object/from16 v6, v17

    goto :goto_1

    :cond_3
    invoke-static {v3}, Lkotlin/ResultKt;->b(Ljava/lang/Object;)V

    iput-object v1, v0, Lcom/transsion/wrapperad/middle/banner/WrapperBanner;->mSceneId:Ljava/lang/String;

    move-object/from16 v3, p3

    iput-object v3, v0, Lcom/transsion/wrapperad/middle/banner/WrapperBanner;->mListener:Lcom/transsion/wrapperad/middle/WrapperAdListener;

    move-object/from16 v3, p2

    iput-object v3, v0, Lcom/transsion/wrapperad/middle/banner/WrapperBanner;->mContext:Landroid/content/Context;

    invoke-static {}, Lkotlinx/coroutines/w0;->b()Lkotlinx/coroutines/CoroutineDispatcher;

    move-result-object v3

    new-instance v6, Lcom/transsion/wrapperad/middle/banner/WrapperBanner$innerLoadAd$2;

    invoke-direct {v6, v1, v2, v7}, Lcom/transsion/wrapperad/middle/banner/WrapperBanner$innerLoadAd$2;-><init>(Ljava/lang/String;Ljava/util/Map;Lkotlin/coroutines/Continuation;)V

    iput-object v0, v4, Lcom/transsion/wrapperad/middle/banner/WrapperBanner$innerLoadAd$1;->L$0:Ljava/lang/Object;

    iput-object v1, v4, Lcom/transsion/wrapperad/middle/banner/WrapperBanner$innerLoadAd$1;->L$1:Ljava/lang/Object;

    iput-object v2, v4, Lcom/transsion/wrapperad/middle/banner/WrapperBanner$innerLoadAd$1;->L$2:Ljava/lang/Object;

    iput-object v0, v4, Lcom/transsion/wrapperad/middle/banner/WrapperBanner$innerLoadAd$1;->L$3:Ljava/lang/Object;

    iput v9, v4, Lcom/transsion/wrapperad/middle/banner/WrapperBanner$innerLoadAd$1;->label:I

    invoke-static {v3, v6, v4}, Lkotlinx/coroutines/h;->g(Lkotlin/coroutines/CoroutineContext;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;

    move-result-object v3

    if-ne v3, v5, :cond_4

    return-object v5

    :cond_4
    move-object v10, v0

    move-object v6, v3

    move-object v3, v10

    :goto_1
    check-cast v6, Lcom/transsion/wrapperad/monopoly/model/AdPlans;

    iput-object v6, v3, Lcom/transsion/wrapperad/middle/banner/WrapperBanner;->mAdPlans:Lcom/transsion/wrapperad/monopoly/model/AdPlans;

    invoke-static {}, Lkotlinx/coroutines/w0;->b()Lkotlinx/coroutines/CoroutineDispatcher;

    move-result-object v3

    new-instance v6, Lcom/transsion/wrapperad/middle/banner/WrapperBanner$innerLoadAd$3;

    invoke-direct {v6, v1, v2, v7}, Lcom/transsion/wrapperad/middle/banner/WrapperBanner$innerLoadAd$3;-><init>(Ljava/lang/String;Ljava/util/Map;Lkotlin/coroutines/Continuation;)V

    iput-object v10, v4, Lcom/transsion/wrapperad/middle/banner/WrapperBanner$innerLoadAd$1;->L$0:Ljava/lang/Object;

    iput-object v1, v4, Lcom/transsion/wrapperad/middle/banner/WrapperBanner$innerLoadAd$1;->L$1:Ljava/lang/Object;

    iput-object v2, v4, Lcom/transsion/wrapperad/middle/banner/WrapperBanner$innerLoadAd$1;->L$2:Ljava/lang/Object;

    iput-object v10, v4, Lcom/transsion/wrapperad/middle/banner/WrapperBanner$innerLoadAd$1;->L$3:Ljava/lang/Object;

    iput v8, v4, Lcom/transsion/wrapperad/middle/banner/WrapperBanner$innerLoadAd$1;->label:I

    invoke-static {v3, v6, v4}, Lkotlinx/coroutines/h;->g(Lkotlin/coroutines/CoroutineContext;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;

    move-result-object v3

    if-ne v3, v5, :cond_5

    return-object v5

    :cond_5
    move-object v5, v1

    move-object v1, v10

    move-object v4, v1

    :goto_2
    check-cast v3, Lcom/transsion/wrapperad/monopoly/model/AdPlans;

    iput-object v3, v1, Lcom/transsion/wrapperad/middle/banner/WrapperBanner;->mAdShowFinalPlan:Lcom/transsion/wrapperad/monopoly/model/AdPlans;

    sget-object v10, Lcom/transsion/wrapperad/a;->a:Lcom/transsion/wrapperad/a;

    const/4 v11, 0x0

    const/4 v13, 0x1

    iget-object v1, v4, Lcom/transsion/wrapperad/middle/banner/WrapperBanner;->mAdPlans:Lcom/transsion/wrapperad/monopoly/model/AdPlans;

    if-nez v1, :cond_6

    const/4 v14, 0x2

    goto :goto_3

    :cond_6
    const/4 v14, 0x1

    :goto_3
    const/4 v15, 0x1

    const/16 v16, 0x0

    move-object v12, v5

    invoke-static/range {v10 .. v16}, Lcom/transsion/wrapperad/a;->j(Lcom/transsion/wrapperad/a;Ljava/lang/String;Ljava/lang/String;IIILjava/lang/Object;)V

    iget-object v1, v4, Lcom/transsion/wrapperad/middle/banner/WrapperBanner;->mAdPlans:Lcom/transsion/wrapperad/monopoly/model/AdPlans;

    if-nez v1, :cond_7

    invoke-direct {v4, v5, v2}, Lcom/transsion/wrapperad/middle/banner/WrapperBanner;->innerLoadHiSavanaAd(Ljava/lang/String;Ljava/util/Map;)V

    goto :goto_4

    :cond_7
    sget-object v1, Lpt/d;->a:Lpt/d;

    invoke-virtual {v1, v5}, Lpt/d;->b(Ljava/lang/String;)Z

    move-result v1

    if-nez v1, :cond_8

    iget-object v1, v4, Lcom/transsion/wrapperad/middle/banner/WrapperBanner;->mAdPlans:Lcom/transsion/wrapperad/monopoly/model/AdPlans;

    invoke-direct {v4, v1, v5}, Lcom/transsion/wrapperad/middle/banner/WrapperBanner;->loadNonBannerAd(Lcom/transsion/wrapperad/monopoly/model/AdPlans;Ljava/lang/String;)V

    goto :goto_4

    :cond_8
    invoke-direct {v4, v5, v2}, Lcom/transsion/wrapperad/middle/banner/WrapperBanner;->innerLoadHiSavanaAd(Ljava/lang/String;Ljava/util/Map;)V

    :goto_4
    sget-object v1, Lkotlin/Unit;->a:Lkotlin/Unit;

    return-object v1
.end method

.method private final innerLoadAdShowFinal()V
    .locals 5

    iget-object v0, p0, Lcom/transsion/wrapperad/middle/banner/WrapperBanner;->mAdShowFinalPlan:Lcom/transsion/wrapperad/monopoly/model/AdPlans;

    const/4 v1, 0x0

    const-class v2, Lcom/transsion/wrapperad/middle/banner/WrapperBanner;

    if-nez v0, :cond_0

    sget-object v0, Lqt/a;->a:Lqt/a;

    invoke-virtual {v2}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    move-result-object v2

    iget-object v3, p0, Lcom/transsion/wrapperad/middle/banner/WrapperBanner;->mSceneId:Ljava/lang/String;

    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v4, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v2, " --> innerLoadAdShowFinal() --> \u5f53\u524d\u6ca1\u6709\u515c\u5e95\u5e7f\u544a --> sceneId = "

    invoke-virtual {v4, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v0, v2, v1}, Lqt/a;->s(Ljava/lang/String;Z)V

    iget-object v0, p0, Lcom/transsion/wrapperad/middle/banner/WrapperBanner;->mListener:Lcom/transsion/wrapperad/middle/WrapperAdListener;

    if-eqz v0, :cond_1

    new-instance v1, Lcom/hisavana/common/bean/TAdErrorCode;

    iget-object v2, p0, Lcom/transsion/wrapperad/middle/banner/WrapperBanner;->mSceneId:Ljava/lang/String;

    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v4, "\u6ca1\u6709\u515c\u5e95\u5e7f\u544a --> sceneId = "

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    const/16 v3, 0x65

    invoke-direct {v1, v3, v2}, Lcom/hisavana/common/bean/TAdErrorCode;-><init>(ILjava/lang/String;)V

    invoke-virtual {v0, v1}, Lcom/transsion/wrapperad/middle/WrapperAdListener;->onError(Lcom/hisavana/common/bean/TAdErrorCode;)V

    goto :goto_0

    :cond_0
    sget-object v0, Lqt/a;->a:Lqt/a;

    invoke-virtual {v2}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    move-result-object v2

    iget-object v3, p0, Lcom/transsion/wrapperad/middle/banner/WrapperBanner;->mSceneId:Ljava/lang/String;

    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v4, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v2, " --> innerLoadAdShowFinal() --> \u5f53\u524d\u6709\u515c\u5e95\u5e7f\u544a\u53ef\u7528 --> sceneId = "

    invoke-virtual {v4, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v0, v2, v1}, Lqt/a;->s(Ljava/lang/String;Z)V

    iget-object v0, p0, Lcom/transsion/wrapperad/middle/banner/WrapperBanner;->mAdShowFinalPlan:Lcom/transsion/wrapperad/monopoly/model/AdPlans;

    iget-object v1, p0, Lcom/transsion/wrapperad/middle/banner/WrapperBanner;->mSceneId:Ljava/lang/String;

    invoke-direct {p0, v0, v1}, Lcom/transsion/wrapperad/middle/banner/WrapperBanner;->loadNonBannerAd(Lcom/transsion/wrapperad/monopoly/model/AdPlans;Ljava/lang/String;)V

    :cond_1
    :goto_0
    return-void
.end method

.method private final innerLoadHiSavanaAd(Ljava/lang/String;Ljava/util/Map;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "+",
            "Ljava/lang/Object;",
            ">;)V"
        }
    .end annotation

    sget-object v0, Lpt/d;->a:Lpt/d;

    invoke-virtual {v0, p1}, Lpt/d;->a(Ljava/lang/String;)Z

    move-result v0

    if-nez v0, :cond_1

    iget-object v0, p0, Lcom/transsion/wrapperad/middle/banner/WrapperBanner;->hiSavanaBanner:Lcom/transsion/wrapperad/middle/banner/HiSavanaBannerProvider;

    if-nez v0, :cond_0

    new-instance v0, Lcom/transsion/wrapperad/middle/banner/HiSavanaBannerProvider;

    invoke-direct {v0, p1}, Lcom/transsion/wrapperad/middle/banner/HiSavanaBannerProvider;-><init>(Ljava/lang/String;)V

    iput-object v0, p0, Lcom/transsion/wrapperad/middle/banner/WrapperBanner;->hiSavanaBanner:Lcom/transsion/wrapperad/middle/banner/HiSavanaBannerProvider;

    :cond_0
    iget-object p1, p0, Lcom/transsion/wrapperad/middle/banner/WrapperBanner;->hiSavanaBanner:Lcom/transsion/wrapperad/middle/banner/HiSavanaBannerProvider;

    if-eqz p1, :cond_2

    invoke-virtual {p1, p0, p2}, Lcom/transsion/wrapperad/middle/banner/HiSavanaBannerProvider;->loadAd(Lcom/transsion/wrapperad/middle/WrapperAdListener;Ljava/util/Map;)V

    goto :goto_0

    :cond_1
    invoke-direct {p0}, Lcom/transsion/wrapperad/middle/banner/WrapperBanner;->innerLoadAdShowFinal()V

    :cond_2
    :goto_0
    return-void
.end method

.method public static synthetic loadBannerAd$default(Lcom/transsion/wrapperad/middle/banner/WrapperBanner;Ljava/lang/String;Landroid/content/Context;Lcom/transsion/wrapperad/middle/WrapperAdListener;Ljava/util/Map;Lkotlin/coroutines/Continuation;ILjava/lang/Object;)Ljava/lang/Object;
    .locals 6

    and-int/lit8 p6, p6, 0x8

    if-eqz p6, :cond_0

    invoke-static {}, Lkotlin/collections/MapsKt;->h()Ljava/util/Map;

    move-result-object p4

    :cond_0
    move-object v4, p4

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    move-object v3, p3

    move-object v5, p5

    invoke-virtual/range {v0 .. v5}, Lcom/transsion/wrapperad/middle/banner/WrapperBanner;->loadBannerAd(Ljava/lang/String;Landroid/content/Context;Lcom/transsion/wrapperad/middle/WrapperAdListener;Ljava/util/Map;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;

    move-result-object p0

    return-object p0
.end method

.method private final loadNonBannerAd(Lcom/transsion/wrapperad/monopoly/model/AdPlans;Ljava/lang/String;)V
    .locals 2

    iget-object v0, p0, Lcom/transsion/wrapperad/middle/banner/WrapperBanner;->mContext:Landroid/content/Context;

    if-eqz v0, :cond_0

    new-instance v1, Lcom/transsion/wrapperad/middle/banner/NonBannerView;

    invoke-direct {v1, v0}, Lcom/transsion/wrapperad/middle/banner/NonBannerView;-><init>(Landroid/content/Context;)V

    iput-object v1, p0, Lcom/transsion/wrapperad/middle/banner/WrapperBanner;->nonBannerView:Lcom/transsion/wrapperad/middle/banner/NonBannerView;

    invoke-virtual {v1, p2, p1, p0}, Lcom/transsion/wrapperad/middle/banner/NonBannerView;->loadAd(Ljava/lang/String;Lcom/transsion/wrapperad/monopoly/model/AdPlans;Lcom/transsion/wrapperad/middle/WrapperAdListener;)V

    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    if-nez p1, :cond_1

    :cond_0
    iget-object p1, p0, Lcom/transsion/wrapperad/middle/banner/WrapperBanner;->mListener:Lcom/transsion/wrapperad/middle/WrapperAdListener;

    if-eqz p1, :cond_1

    new-instance p2, Lcom/hisavana/common/bean/TAdErrorCode;

    const/16 v0, 0x65

    const-string v1, "mContext == null"

    invoke-direct {p2, v0, v1}, Lcom/hisavana/common/bean/TAdErrorCode;-><init>(ILjava/lang/String;)V

    invoke-virtual {p1, p2}, Lcom/transsion/wrapperad/middle/WrapperAdListener;->onError(Lcom/hisavana/common/bean/TAdErrorCode;)V

    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    :cond_1
    return-void
.end method


# virtual methods
.method public final destroy()V
    .locals 4

    const/4 v0, 0x0

    iput-object v0, p0, Lcom/transsion/wrapperad/middle/banner/WrapperBanner;->mListener:Lcom/transsion/wrapperad/middle/WrapperAdListener;

    iget-object v1, p0, Lcom/transsion/wrapperad/middle/banner/WrapperBanner;->hiSavanaBanner:Lcom/transsion/wrapperad/middle/banner/HiSavanaBannerProvider;

    if-eqz v1, :cond_0

    invoke-virtual {v1}, Lcom/transsion/wrapperad/middle/banner/HiSavanaBannerProvider;->destroy()V

    :cond_0
    iput-object v0, p0, Lcom/transsion/wrapperad/middle/banner/WrapperBanner;->hiSavanaBanner:Lcom/transsion/wrapperad/middle/banner/HiSavanaBannerProvider;

    iget-object v1, p0, Lcom/transsion/wrapperad/middle/banner/WrapperBanner;->nonBannerView:Lcom/transsion/wrapperad/middle/banner/NonBannerView;

    if-eqz v1, :cond_1

    invoke-virtual {v1}, Lcom/transsion/wrapperad/middle/banner/NonBannerView;->destroy()V

    :cond_1
    iput-object v0, p0, Lcom/transsion/wrapperad/middle/banner/WrapperBanner;->nonBannerView:Lcom/transsion/wrapperad/middle/banner/NonBannerView;

    iput-object v0, p0, Lcom/transsion/wrapperad/middle/banner/WrapperBanner;->mContext:Landroid/content/Context;

    sget-object v0, Lqt/a;->a:Lqt/a;

    const-class v1, Lcom/transsion/wrapperad/middle/banner/WrapperBanner;

    invoke-virtual {v1}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    move-result-object v1

    iget-object v2, p0, Lcom/transsion/wrapperad/middle/banner/WrapperBanner;->mSceneId:Ljava/lang/String;

    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v3, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v1, " --> destroy() --> sceneId = "

    invoke-virtual {v3, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    const/4 v2, 0x0

    invoke-virtual {v0, v1, v2}, Lqt/a;->s(Ljava/lang/String;Z)V

    return-void
.end method

.method public final loadBannerAd(Ljava/lang/String;Landroid/content/Context;Lcom/transsion/wrapperad/middle/WrapperAdListener;Ljava/util/Map;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Landroid/content/Context;",
            "Lcom/transsion/wrapperad/middle/WrapperAdListener;",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "+",
            "Ljava/lang/Object;",
            ">;",
            "Lkotlin/coroutines/Continuation<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    sget-object v0, Lpt/d;->a:Lpt/d;

    invoke-virtual {v0, p1, p3}, Lpt/d;->d(Ljava/lang/String;Lcom/transsion/wrapperad/middle/WrapperAdListener;)Z

    move-result v0

    if-eqz v0, :cond_0

    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    return-object p1

    :cond_0
    invoke-direct/range {p0 .. p5}, Lcom/transsion/wrapperad/middle/banner/WrapperBanner;->innerLoadAd(Ljava/lang/String;Landroid/content/Context;Lcom/transsion/wrapperad/middle/WrapperAdListener;Ljava/util/Map;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;

    move-result-object p1

    invoke-static {}, Lkotlin/coroutines/intrinsics/IntrinsicsKt;->e()Ljava/lang/Object;

    move-result-object p2

    if-ne p1, p2, :cond_1

    return-object p1

    :cond_1
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    return-object p1
.end method

.method public onBannerViewReady(Landroid/view/View;)V
    .locals 1

    invoke-super {p0, p1}, Lcom/transsion/wrapperad/middle/WrapperAdListener;->onBannerViewReady(Landroid/view/View;)V

    iget-object v0, p0, Lcom/transsion/wrapperad/middle/banner/WrapperBanner;->mListener:Lcom/transsion/wrapperad/middle/WrapperAdListener;

    if-eqz v0, :cond_0

    invoke-virtual {v0, p1}, Lcom/transsion/wrapperad/middle/WrapperAdListener;->onBannerViewReady(Landroid/view/View;)V

    :cond_0
    return-void
.end method

.method public onError(Lcom/hisavana/common/bean/TAdErrorCode;)V
    .locals 0

    invoke-super {p0, p1}, Lcom/transsion/wrapperad/middle/WrapperAdListener;->onError(Lcom/hisavana/common/bean/TAdErrorCode;)V

    invoke-direct {p0}, Lcom/transsion/wrapperad/middle/banner/WrapperBanner;->innerLoadAdShowFinal()V

    return-void
.end method
