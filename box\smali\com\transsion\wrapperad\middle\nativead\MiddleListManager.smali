.class public final Lcom/transsion/wrapperad/middle/nativead/MiddleListManager;
.super Ljava/lang/Object;


# annotations
.annotation runtime Lkotlin/Metadata;
.end annotation

.annotation build Lkotlin/jvm/internal/SourceDebugExtension;
.end annotation


# instance fields
.field public a:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/Object;",
            ">;"
        }
    .end annotation
.end field

.field public b:Lkotlinx/coroutines/k0;

.field public c:Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;

.field public d:I

.field public e:I

.field public f:Ljava/lang/ref/WeakReference;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/lang/ref/WeakReference<",
            "Landroidx/recyclerview/widget/RecyclerView;",
            ">;"
        }
    .end annotation
.end field

.field public g:Lkotlin/jvm/functions/Function2;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlin/jvm/functions/Function2<",
            "-",
            "Ljava/lang/Integer;",
            "-",
            "Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation
.end field

.field public final h:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;",
            ">;"
        }
    .end annotation
.end field

.field public i:Z

.field public j:Ljava/lang/String;

.field public k:Z

.field public final l:J

.field public final m:Landroid/os/Handler;

.field public n:Ljava/lang/Runnable;


# direct methods
.method public constructor <init>()V
    .locals 2

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    new-instance v0, Ljava/util/LinkedHashMap;

    invoke-direct {v0}, Ljava/util/LinkedHashMap;-><init>()V

    iput-object v0, p0, Lcom/transsion/wrapperad/middle/nativead/MiddleListManager;->a:Ljava/util/Map;

    const/4 v0, -0x1

    iput v0, p0, Lcom/transsion/wrapperad/middle/nativead/MiddleListManager;->d:I

    iput v0, p0, Lcom/transsion/wrapperad/middle/nativead/MiddleListManager;->e:I

    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lcom/transsion/wrapperad/middle/nativead/MiddleListManager;->h:Ljava/util/List;

    const-string v0, ""

    iput-object v0, p0, Lcom/transsion/wrapperad/middle/nativead/MiddleListManager;->j:Ljava/lang/String;

    const/4 v0, 0x1

    iput-boolean v0, p0, Lcom/transsion/wrapperad/middle/nativead/MiddleListManager;->k:Z

    const-wide/16 v0, 0x12c

    iput-wide v0, p0, Lcom/transsion/wrapperad/middle/nativead/MiddleListManager;->l:J

    new-instance v0, Landroid/os/Handler;

    invoke-static {}, Landroid/os/Looper;->getMainLooper()Landroid/os/Looper;

    move-result-object v1

    invoke-direct {v0, v1}, Landroid/os/Handler;-><init>(Landroid/os/Looper;)V

    iput-object v0, p0, Lcom/transsion/wrapperad/middle/nativead/MiddleListManager;->m:Landroid/os/Handler;

    new-instance v0, Lcom/transsion/wrapperad/middle/nativead/e;

    invoke-direct {v0, p0}, Lcom/transsion/wrapperad/middle/nativead/e;-><init>(Lcom/transsion/wrapperad/middle/nativead/MiddleListManager;)V

    iput-object v0, p0, Lcom/transsion/wrapperad/middle/nativead/MiddleListManager;->n:Ljava/lang/Runnable;

    return-void
.end method

.method public static synthetic a(Lcom/transsion/wrapperad/middle/nativead/MiddleListManager;)V
    .locals 0

    invoke-static {p0}, Lcom/transsion/wrapperad/middle/nativead/MiddleListManager;->m(Lcom/transsion/wrapperad/middle/nativead/MiddleListManager;)V

    return-void
.end method

.method public static final synthetic b(Lcom/transsion/wrapperad/middle/nativead/MiddleListManager;)Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;
    .locals 0

    iget-object p0, p0, Lcom/transsion/wrapperad/middle/nativead/MiddleListManager;->c:Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;

    return-object p0
.end method

.method public static final synthetic c(Lcom/transsion/wrapperad/middle/nativead/MiddleListManager;)Ljava/util/Map;
    .locals 0

    iget-object p0, p0, Lcom/transsion/wrapperad/middle/nativead/MiddleListManager;->a:Ljava/util/Map;

    return-object p0
.end method

.method public static final synthetic d(Lcom/transsion/wrapperad/middle/nativead/MiddleListManager;)Ljava/lang/String;
    .locals 0

    iget-object p0, p0, Lcom/transsion/wrapperad/middle/nativead/MiddleListManager;->j:Ljava/lang/String;

    return-object p0
.end method

.method public static final synthetic e(Lcom/transsion/wrapperad/middle/nativead/MiddleListManager;)I
    .locals 0

    invoke-virtual {p0}, Lcom/transsion/wrapperad/middle/nativead/MiddleListManager;->s()I

    move-result p0

    return p0
.end method

.method public static final synthetic f(Lcom/transsion/wrapperad/middle/nativead/MiddleListManager;)I
    .locals 0

    invoke-virtual {p0}, Lcom/transsion/wrapperad/middle/nativead/MiddleListManager;->t()I

    move-result p0

    return p0
.end method

.method public static final synthetic g(Lcom/transsion/wrapperad/middle/nativead/MiddleListManager;)V
    .locals 0

    invoke-virtual {p0}, Lcom/transsion/wrapperad/middle/nativead/MiddleListManager;->u()V

    return-void
.end method

.method public static final synthetic h(Lcom/transsion/wrapperad/middle/nativead/MiddleListManager;)Z
    .locals 0

    iget-boolean p0, p0, Lcom/transsion/wrapperad/middle/nativead/MiddleListManager;->k:Z

    return p0
.end method

.method public static final synthetic i(Lcom/transsion/wrapperad/middle/nativead/MiddleListManager;)Z
    .locals 0

    iget-boolean p0, p0, Lcom/transsion/wrapperad/middle/nativead/MiddleListManager;->i:Z

    return p0
.end method

.method public static final synthetic j(Lcom/transsion/wrapperad/middle/nativead/MiddleListManager;)V
    .locals 0

    invoke-virtual {p0}, Lcom/transsion/wrapperad/middle/nativead/MiddleListManager;->v()V

    return-void
.end method

.method public static final synthetic k(Lcom/transsion/wrapperad/middle/nativead/MiddleListManager;Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;)V
    .locals 0

    iput-object p1, p0, Lcom/transsion/wrapperad/middle/nativead/MiddleListManager;->c:Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;

    return-void
.end method

.method public static final synthetic l(Lcom/transsion/wrapperad/middle/nativead/MiddleListManager;Z)V
    .locals 0

    iput-boolean p1, p0, Lcom/transsion/wrapperad/middle/nativead/MiddleListManager;->i:Z

    return-void
.end method

.method public static final m(Lcom/transsion/wrapperad/middle/nativead/MiddleListManager;)V
    .locals 1

    const-string v0, "this$0"

    invoke-static {p0, v0}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-virtual {p0}, Lcom/transsion/wrapperad/middle/nativead/MiddleListManager;->o()V

    return-void
.end method


# virtual methods
.method public final A(Z)V
    .locals 0

    iput-boolean p1, p0, Lcom/transsion/wrapperad/middle/nativead/MiddleListManager;->k:Z

    return-void
.end method

.method public final B(Lkotlin/jvm/functions/Function2;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function2<",
            "-",
            "Ljava/lang/Integer;",
            "-",
            "Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lcom/transsion/wrapperad/middle/nativead/MiddleListManager;->g:Lkotlin/jvm/functions/Function2;

    return-void
.end method

.method public final C(I)V
    .locals 0

    iput p1, p0, Lcom/transsion/wrapperad/middle/nativead/MiddleListManager;->d:I

    return-void
.end method

.method public final D(Landroidx/recyclerview/widget/RecyclerView;)V
    .locals 1

    new-instance v0, Ljava/lang/ref/WeakReference;

    invoke-direct {v0, p1}, Ljava/lang/ref/WeakReference;-><init>(Ljava/lang/Object;)V

    iput-object v0, p0, Lcom/transsion/wrapperad/middle/nativead/MiddleListManager;->f:Ljava/lang/ref/WeakReference;

    if-eqz p1, :cond_0

    new-instance v0, Lcom/transsion/wrapperad/middle/nativead/MiddleListManager$a;

    invoke-direct {v0, p0}, Lcom/transsion/wrapperad/middle/nativead/MiddleListManager$a;-><init>(Lcom/transsion/wrapperad/middle/nativead/MiddleListManager;)V

    invoke-virtual {p1, v0}, Landroidx/recyclerview/widget/RecyclerView;->addOnScrollListener(Landroidx/recyclerview/widget/RecyclerView$r;)V

    :cond_0
    return-void
.end method

.method public final E(Ljava/lang/String;)V
    .locals 1

    const-string v0, "sceneId"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    iput-object p1, p0, Lcom/transsion/wrapperad/middle/nativead/MiddleListManager;->j:Ljava/lang/String;

    return-void
.end method

.method public final n()V
    .locals 1

    invoke-virtual {p0}, Lcom/transsion/wrapperad/middle/nativead/MiddleListManager;->w()V

    iget-object v0, p0, Lcom/transsion/wrapperad/middle/nativead/MiddleListManager;->f:Ljava/lang/ref/WeakReference;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Ljava/lang/ref/Reference;->clear()V

    :cond_0
    const/4 v0, 0x0

    iput-object v0, p0, Lcom/transsion/wrapperad/middle/nativead/MiddleListManager;->g:Lkotlin/jvm/functions/Function2;

    return-void
.end method

.method public final o()V
    .locals 8

    sget-object v0, Lqt/a;->a:Lqt/a;

    const-class v1, Lcom/transsion/wrapperad/middle/nativead/MiddleListManager;

    invoke-virtual {v1}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    move-result-object v2

    iget-object v3, p0, Lcom/transsion/wrapperad/middle/nativead/MiddleListManager;->j:Ljava/lang/String;

    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v4, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v2, " --> getDelegate() --> \u6eda\u52a8\u7ed3\u675f\u4e86 --> \u63d2\u5165\u5e7f\u544a -- sceneId = "

    invoke-virtual {v4, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    const/4 v3, 0x0

    invoke-virtual {v0, v2, v3}, Lqt/a;->E(Ljava/lang/String;Z)V

    iget-object v2, p0, Lcom/transsion/wrapperad/middle/nativead/MiddleListManager;->c:Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;

    if-eqz v2, :cond_0

    invoke-virtual {v2}, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;->isReady()Z

    move-result v2

    const/4 v4, 0x1

    if-ne v2, v4, :cond_0

    invoke-virtual {p0}, Lcom/transsion/wrapperad/middle/nativead/MiddleListManager;->u()V

    return-void

    :cond_0
    iget-object v2, p0, Lcom/transsion/wrapperad/middle/nativead/MiddleListManager;->c:Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;

    if-eqz v2, :cond_1

    invoke-virtual {v1}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    move-result-object v1

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v1, " --> null != delegate"

    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1, v3}, Lqt/a;->E(Ljava/lang/String;Z)V

    return-void

    :cond_1
    new-instance v0, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;

    invoke-direct {v0}, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;-><init>()V

    iput-object v0, p0, Lcom/transsion/wrapperad/middle/nativead/MiddleListManager;->c:Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;

    iget-object v1, p0, Lcom/transsion/wrapperad/middle/nativead/MiddleListManager;->h:Ljava/util/List;

    invoke-interface {v1, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    iget-object v2, p0, Lcom/transsion/wrapperad/middle/nativead/MiddleListManager;->b:Lkotlinx/coroutines/k0;

    if-eqz v2, :cond_2

    const/4 v3, 0x0

    const/4 v4, 0x0

    new-instance v5, Lcom/transsion/wrapperad/middle/nativead/MiddleListManager$getDelegate$1$1;

    const/4 v1, 0x0

    invoke-direct {v5, v0, p0, v1}, Lcom/transsion/wrapperad/middle/nativead/MiddleListManager$getDelegate$1$1;-><init>(Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;Lcom/transsion/wrapperad/middle/nativead/MiddleListManager;Lkotlin/coroutines/Continuation;)V

    const/4 v6, 0x3

    const/4 v7, 0x0

    invoke-static/range {v2 .. v7}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/k0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/q1;

    :cond_2
    return-void
.end method

.method public final p()Lkotlin/jvm/functions/Function2;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlin/jvm/functions/Function2<",
            "Ljava/lang/Integer;",
            "Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    iget-object v0, p0, Lcom/transsion/wrapperad/middle/nativead/MiddleListManager;->g:Lkotlin/jvm/functions/Function2;

    return-object v0
.end method

.method public final q()I
    .locals 1

    iget v0, p0, Lcom/transsion/wrapperad/middle/nativead/MiddleListManager;->e:I

    return v0
.end method

.method public final r()I
    .locals 1

    iget v0, p0, Lcom/transsion/wrapperad/middle/nativead/MiddleListManager;->d:I

    return v0
.end method

.method public final s()I
    .locals 5

    sget-object v0, Lpt/b;->a:Lpt/b;

    iget-object v1, p0, Lcom/transsion/wrapperad/middle/nativead/MiddleListManager;->j:Ljava/lang/String;

    const/4 v2, 0x2

    const/4 v3, 0x0

    const/4 v4, 0x0

    invoke-static {v0, v1, v4, v2, v3}, Lpt/b;->f(Lpt/b;Ljava/lang/String;IILjava/lang/Object;)I

    move-result v0

    return v0
.end method

.method public final t()I
    .locals 5

    sget-object v0, Lpt/b;->a:Lpt/b;

    iget-object v1, p0, Lcom/transsion/wrapperad/middle/nativead/MiddleListManager;->j:Ljava/lang/String;

    const/4 v2, 0x2

    const/4 v3, 0x0

    const/4 v4, 0x0

    invoke-static {v0, v1, v4, v2, v3}, Lpt/b;->h(Lpt/b;Ljava/lang/String;IILjava/lang/Object;)I

    move-result v0

    return v0
.end method

.method public final u()V
    .locals 5

    iget-object v0, p0, Lcom/transsion/wrapperad/middle/nativead/MiddleListManager;->c:Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;

    if-eqz v0, :cond_2

    iget v1, p0, Lcom/transsion/wrapperad/middle/nativead/MiddleListManager;->d:I

    add-int/lit8 v1, v1, 0x1

    iget v2, p0, Lcom/transsion/wrapperad/middle/nativead/MiddleListManager;->e:I

    sub-int v2, v1, v2

    invoke-virtual {p0}, Lcom/transsion/wrapperad/middle/nativead/MiddleListManager;->t()I

    move-result v3

    if-ge v2, v3, :cond_0

    return-void

    :cond_0
    iput v1, p0, Lcom/transsion/wrapperad/middle/nativead/MiddleListManager;->e:I

    iget-object v2, p0, Lcom/transsion/wrapperad/middle/nativead/MiddleListManager;->g:Lkotlin/jvm/functions/Function2;

    if-eqz v2, :cond_1

    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    invoke-interface {v2, v1, v0}, Lkotlin/jvm/functions/Function2;->invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lkotlin/Unit;

    :cond_1
    sget-object v0, Lqt/a;->a:Lqt/a;

    const-class v1, Lcom/transsion/wrapperad/middle/nativead/MiddleListManager;

    invoke-virtual {v1}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    move-result-object v1

    iget v2, p0, Lcom/transsion/wrapperad/middle/nativead/MiddleListManager;->d:I

    iget v3, p0, Lcom/transsion/wrapperad/middle/nativead/MiddleListManager;->e:I

    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v4, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v1, " --> insert() --> \u89e6\u53d1\u63d2\u5165\u5e7f\u544a --- mLastItemPosition = "

    invoke-virtual {v4, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4, v2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v1, " -- mLastAdPosition = "

    invoke-virtual {v4, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4, v3}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    const/4 v2, 0x0

    const/4 v3, 0x2

    const/4 v4, 0x0

    invoke-static {v0, v1, v2, v3, v4}, Lqt/a;->F(Lqt/a;Ljava/lang/String;ZILjava/lang/Object;)V

    iput-object v4, p0, Lcom/transsion/wrapperad/middle/nativead/MiddleListManager;->c:Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;

    :cond_2
    return-void
.end method

.method public final v()V
    .locals 6

    sget-object v0, Lqt/a;->a:Lqt/a;

    const-class v1, Lcom/transsion/wrapperad/middle/nativead/MiddleListManager;

    invoke-virtual {v1}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    move-result-object v1

    iget v2, p0, Lcom/transsion/wrapperad/middle/nativead/MiddleListManager;->d:I

    iget v3, p0, Lcom/transsion/wrapperad/middle/nativead/MiddleListManager;->e:I

    invoke-virtual {p0}, Ljava/lang/Object;->hashCode()I

    move-result v4

    new-instance v5, Ljava/lang/StringBuilder;

    invoke-direct {v5}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v5, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v1, " --> mLastItemPosition = "

    invoke-virtual {v5, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v5, v2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v1, " --> mLastAdPosition = "

    invoke-virtual {v5, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v5, v3}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v1, " --> hashCode = "

    invoke-virtual {v5, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v5, v4}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v5}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    const/4 v2, 0x2

    const/4 v3, 0x0

    const/4 v4, 0x0

    invoke-static {v0, v1, v4, v2, v3}, Lqt/a;->F(Lqt/a;Ljava/lang/String;ZILjava/lang/Object;)V

    iget-object v0, p0, Lcom/transsion/wrapperad/middle/nativead/MiddleListManager;->m:Landroid/os/Handler;

    iget-object v1, p0, Lcom/transsion/wrapperad/middle/nativead/MiddleListManager;->n:Ljava/lang/Runnable;

    invoke-virtual {v0, v1}, Landroid/os/Handler;->removeCallbacks(Ljava/lang/Runnable;)V

    iget-object v0, p0, Lcom/transsion/wrapperad/middle/nativead/MiddleListManager;->m:Landroid/os/Handler;

    iget-object v1, p0, Lcom/transsion/wrapperad/middle/nativead/MiddleListManager;->n:Ljava/lang/Runnable;

    iget-wide v2, p0, Lcom/transsion/wrapperad/middle/nativead/MiddleListManager;->l:J

    invoke-virtual {v0, v1, v2, v3}, Landroid/os/Handler;->postDelayed(Ljava/lang/Runnable;J)Z

    return-void
.end method

.method public final w()V
    .locals 4

    sget-object v0, Lqt/a;->a:Lqt/a;

    const-class v1, Lcom/transsion/wrapperad/middle/nativead/MiddleListManager;

    invoke-virtual {v1}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    move-result-object v1

    iget-object v2, p0, Lcom/transsion/wrapperad/middle/nativead/MiddleListManager;->j:Ljava/lang/String;

    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v3, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v1, " --> refresh() --> \u8d44\u6e90\u56de\u6536 --> delegateList.forEach{it.destroy()} -- sceneId = "

    invoke-virtual {v3, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    const/4 v2, 0x0

    invoke-virtual {v0, v1, v2}, Lqt/a;->E(Ljava/lang/String;Z)V

    iget-object v0, p0, Lcom/transsion/wrapperad/middle/nativead/MiddleListManager;->h:Ljava/util/List;

    check-cast v0, Ljava/lang/Iterable;

    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_0

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;

    invoke-virtual {v1}, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;->destroy()V

    goto :goto_0

    :cond_0
    const/4 v0, -0x1

    iput v0, p0, Lcom/transsion/wrapperad/middle/nativead/MiddleListManager;->d:I

    iput v0, p0, Lcom/transsion/wrapperad/middle/nativead/MiddleListManager;->e:I

    return-void
.end method

.method public final x(Lkotlin/jvm/functions/Function2;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function2<",
            "-",
            "Ljava/lang/Integer;",
            "-",
            "Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lcom/transsion/wrapperad/middle/nativead/MiddleListManager;->g:Lkotlin/jvm/functions/Function2;

    return-void
.end method

.method public final y(Lkotlinx/coroutines/k0;)V
    .locals 0

    iput-object p1, p0, Lcom/transsion/wrapperad/middle/nativead/MiddleListManager;->b:Lkotlinx/coroutines/k0;

    return-void
.end method

.method public final z(Ljava/util/Map;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/Object;",
            ">;)V"
        }
    .end annotation

    const-string v0, "ctxMap"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    iput-object p1, p0, Lcom/transsion/wrapperad/middle/nativead/MiddleListManager;->a:Ljava/util/Map;

    return-void
.end method
