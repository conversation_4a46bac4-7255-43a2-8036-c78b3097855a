.class public final Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;
.super Lcom/transsion/wrapperad/middle/WrapperAdListener;

# interfaces
.implements Landroid/os/Parcelable;


# annotations
.annotation runtime Lkotlin/Metadata;
.end annotation


# static fields
.field public static final CREATOR:Landroid/os/Parcelable$Creator;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Landroid/os/Parcelable$Creator<",
            "Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;",
            ">;"
        }
    .end annotation
.end field


# instance fields
.field private buyOutNativeView:Lcom/transsion/wrapperad/middle/nativead/BuyOutNativeView;

.field private ctxMap:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "+",
            "Ljava/lang/Object;",
            ">;"
        }
    .end annotation
.end field

.field private enableBgBlur:Ljava/lang/Boolean;

.field private mAdContainer:Landroid/widget/FrameLayout;

.field private mAdPlans:Lcom/transsion/wrapperad/monopoly/model/AdPlans;

.field private mAdShowFinalPlan:Lcom/transsion/wrapperad/monopoly/model/AdPlans;

.field private mCallback:Lcom/transsion/wrapperad/middle/WrapperAdListener;

.field private mNativeInfo:Lcom/hisavana/common/bean/TAdNativeInfo;

.field private mNonAdPlans:Lcom/transsion/wrapperad/monopoly/model/AdPlans;

.field private mSceneId:Ljava/lang/String;

.field private mTAdNativeView:Lcom/hisavana/mediation/ad/TAdNativeView;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    new-instance v0, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager$a;

    invoke-direct {v0}, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager$a;-><init>()V

    sput-object v0, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;->CREATOR:Landroid/os/Parcelable$Creator;

    return-void
.end method

.method public constructor <init>()V
    .locals 1

    invoke-direct {p0}, Lcom/transsion/wrapperad/middle/WrapperAdListener;-><init>()V

    new-instance v0, Ljava/util/LinkedHashMap;

    invoke-direct {v0}, Ljava/util/LinkedHashMap;-><init>()V

    iput-object v0, p0, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;->ctxMap:Ljava/util/Map;

    return-void
.end method

.method public static final synthetic access$innerLoadAd(Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;Ljava/lang/String;Lcom/transsion/wrapperad/middle/WrapperAdListener;Ljava/util/Map;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;
    .locals 0

    invoke-direct {p0, p1, p2, p3, p4}, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;->innerLoadAd(Ljava/lang/String;Lcom/transsion/wrapperad/middle/WrapperAdListener;Ljava/util/Map;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;

    move-result-object p0

    return-object p0
.end method

.method private static synthetic getBuyOutNativeView$annotations()V
    .locals 0

    return-void
.end method

.method private static synthetic getCtxMap$annotations()V
    .locals 0

    return-void
.end method

.method private static synthetic getEnableBgBlur$annotations()V
    .locals 0

    return-void
.end method

.method private static synthetic getMAdContainer$annotations()V
    .locals 0

    return-void
.end method

.method private static synthetic getMAdPlans$annotations()V
    .locals 0

    return-void
.end method

.method private static synthetic getMAdShowFinalPlan$annotations()V
    .locals 0

    return-void
.end method

.method private static synthetic getMCallback$annotations()V
    .locals 0

    return-void
.end method

.method private static synthetic getMNativeInfo$annotations()V
    .locals 0

    return-void
.end method

.method private static synthetic getMNonAdPlans$annotations()V
    .locals 0

    return-void
.end method

.method private static synthetic getMSceneId$annotations()V
    .locals 0

    return-void
.end method

.method private static synthetic getMTAdNativeView$annotations()V
    .locals 0

    return-void
.end method

.method private final innerAddViewShowAd(Landroid/view/View;Landroid/widget/FrameLayout;)V
    .locals 3

    if-eqz p2, :cond_0

    invoke-virtual {p2}, Landroid/view/ViewGroup;->removeAllViews()V

    const/4 v0, 0x0

    invoke-virtual {p2, v0}, Landroid/view/View;->setVisibility(I)V

    new-instance v0, Landroid/widget/FrameLayout$LayoutParams;

    const/4 v1, -0x1

    const/4 v2, -0x2

    invoke-direct {v0, v1, v2}, Landroid/widget/FrameLayout$LayoutParams;-><init>(II)V

    invoke-virtual {p2, p1, v0}, Landroid/view/ViewGroup;->addView(Landroid/view/View;Landroid/view/ViewGroup$LayoutParams;)V

    :cond_0
    return-void
.end method

.method private final innerAddViewShowAd(Landroid/widget/FrameLayout;ZZ)V
    .locals 1

    iput-object p1, p0, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;->mAdContainer:Landroid/widget/FrameLayout;

    iget-object v0, p0, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;->mNativeInfo:Lcom/hisavana/common/bean/TAdNativeInfo;

    if-nez v0, :cond_0

    invoke-direct {p0, p1, p2, p3}, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;->innerShowNonNativeAd(Landroid/widget/FrameLayout;ZZ)V

    goto :goto_0

    :cond_0
    invoke-direct {p0, p1}, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;->innerShowHiSavanaAd(Landroid/widget/FrameLayout;)V

    :goto_0
    return-void
.end method

.method private final innerDestroy()V
    .locals 5

    sget-object v0, Lqt/a;->a:Lqt/a;

    const-class v1, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;

    invoke-virtual {v1}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    move-result-object v1

    iget-object v2, p0, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;->mSceneId:Ljava/lang/String;

    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v3, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v1, " --> destroy() --> sceneId = "

    invoke-virtual {v3, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v1, " \u573a\u666f\u8d44\u6e90\u56de\u6536\u4e86"

    invoke-virtual {v3, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    const/4 v2, 0x0

    invoke-virtual {v0, v1, v2}, Lqt/a;->E(Ljava/lang/String;Z)V

    sget-object v0, Lcom/transsion/wrapperad/middle/nativead/HiSavanaNativeAdManager;->Companion:Lcom/transsion/wrapperad/middle/nativead/HiSavanaNativeAdManager$a;

    sget-object v1, Lpt/b;->a:Lpt/b;

    iget-object v2, p0, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;->mSceneId:Ljava/lang/String;

    invoke-virtual {v1, v2}, Lpt/b;->a(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v0, v2}, Lcom/transsion/wrapperad/middle/nativead/HiSavanaNativeAdManager$a;->b(Ljava/lang/String;)Lcom/transsion/wrapperad/middle/nativead/HiSavanaNativeAdManager;

    move-result-object v2

    if-eqz v2, :cond_0

    invoke-virtual {v2, p0}, Lcom/transsion/wrapperad/middle/nativead/HiSavanaNativeAdManager;->removerCallback(Lcom/transsion/wrapperad/middle/WrapperAdListener;)V

    :cond_0
    iget-object v2, p0, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;->mNativeInfo:Lcom/hisavana/common/bean/TAdNativeInfo;

    const/4 v3, 0x0

    if-eqz v2, :cond_3

    if-eqz v2, :cond_1

    invoke-interface {v2}, Lcom/hisavana/common/interfacz/ICacheAd;->getExt()Ljava/lang/String;

    move-result-object v4

    goto :goto_0

    :cond_1
    move-object v4, v3

    :goto_0
    invoke-static {v4}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v4

    if-eqz v4, :cond_2

    invoke-virtual {v2}, Lcom/hisavana/common/bean/TAdNativeInfo;->release()V

    goto :goto_1

    :cond_2
    iget-object v4, p0, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;->mSceneId:Ljava/lang/String;

    invoke-virtual {v1, v4}, Lpt/b;->a(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Lcom/transsion/wrapperad/middle/nativead/HiSavanaNativeAdManager$a;->b(Ljava/lang/String;)Lcom/transsion/wrapperad/middle/nativead/HiSavanaNativeAdManager;

    move-result-object v0

    if-eqz v0, :cond_3

    invoke-virtual {v0, v2}, Lcom/transsion/wrapperad/middle/nativead/HiSavanaNativeAdManager;->addUnusedAdToPool(Lcom/hisavana/common/bean/TAdNativeInfo;)V

    :cond_3
    :goto_1
    iget-object v0, p0, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;->buyOutNativeView:Lcom/transsion/wrapperad/middle/nativead/BuyOutNativeView;

    if-eqz v0, :cond_4

    invoke-virtual {v0}, Lcom/transsion/wrapperad/middle/nativead/BuyOutNativeView;->destroy()V

    :cond_4
    iput-object v3, p0, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;->buyOutNativeView:Lcom/transsion/wrapperad/middle/nativead/BuyOutNativeView;

    iget-object v0, p0, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;->mTAdNativeView:Lcom/hisavana/mediation/ad/TAdNativeView;

    if-eqz v0, :cond_5

    invoke-virtual {v0}, Lcom/hisavana/mediation/ad/TAdNativeView;->release()V

    :cond_5
    iput-object v3, p0, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;->mTAdNativeView:Lcom/hisavana/mediation/ad/TAdNativeView;

    iget-object v0, p0, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;->mAdContainer:Landroid/widget/FrameLayout;

    if-eqz v0, :cond_6

    invoke-virtual {v0}, Landroid/view/ViewGroup;->removeAllViews()V

    :cond_6
    iput-object v3, p0, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;->mAdContainer:Landroid/widget/FrameLayout;

    iput-object v3, p0, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;->mCallback:Lcom/transsion/wrapperad/middle/WrapperAdListener;

    iput-object v3, p0, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;->mNativeInfo:Lcom/hisavana/common/bean/TAdNativeInfo;

    iput-object v3, p0, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;->mAdShowFinalPlan:Lcom/transsion/wrapperad/monopoly/model/AdPlans;

    iput-object v3, p0, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;->mAdPlans:Lcom/transsion/wrapperad/monopoly/model/AdPlans;

    iput-object v3, p0, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;->mNonAdPlans:Lcom/transsion/wrapperad/monopoly/model/AdPlans;

    return-void
.end method

.method private final innerLoadAd(Ljava/lang/String;Lcom/transsion/wrapperad/middle/WrapperAdListener;Ljava/util/Map;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;
    .locals 18
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Lcom/transsion/wrapperad/middle/WrapperAdListener;",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "+",
            "Ljava/lang/Object;",
            ">;",
            "Lkotlin/coroutines/Continuation<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    move-object/from16 v0, p0

    move-object/from16 v1, p1

    move-object/from16 v2, p3

    move-object/from16 v3, p4

    instance-of v4, v3, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager$innerLoadAd$1;

    if-eqz v4, :cond_0

    move-object v4, v3

    check-cast v4, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager$innerLoadAd$1;

    iget v5, v4, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager$innerLoadAd$1;->label:I

    const/high16 v6, -0x80000000

    and-int v7, v5, v6

    if-eqz v7, :cond_0

    sub-int/2addr v5, v6

    iput v5, v4, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager$innerLoadAd$1;->label:I

    goto :goto_0

    :cond_0
    new-instance v4, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager$innerLoadAd$1;

    invoke-direct {v4, v0, v3}, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager$innerLoadAd$1;-><init>(Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;Lkotlin/coroutines/Continuation;)V

    :goto_0
    iget-object v3, v4, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager$innerLoadAd$1;->result:Ljava/lang/Object;

    invoke-static {}, Lkotlin/coroutines/intrinsics/IntrinsicsKt;->e()Ljava/lang/Object;

    move-result-object v5

    iget v6, v4, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager$innerLoadAd$1;->label:I

    const/4 v7, 0x0

    const/4 v8, 0x2

    const/4 v9, 0x1

    if-eqz v6, :cond_3

    if-eq v6, v9, :cond_2

    if-ne v6, v8, :cond_1

    iget-object v1, v4, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager$innerLoadAd$1;->L$2:Ljava/lang/Object;

    check-cast v1, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;

    iget-object v2, v4, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager$innerLoadAd$1;->L$1:Ljava/lang/Object;

    check-cast v2, Ljava/lang/String;

    iget-object v4, v4, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager$innerLoadAd$1;->L$0:Ljava/lang/Object;

    check-cast v4, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;

    invoke-static {v3}, Lkotlin/ResultKt;->b(Ljava/lang/Object;)V

    goto/16 :goto_2

    :cond_1
    new-instance v1, Ljava/lang/IllegalStateException;

    const-string v2, "call to \'resume\' before \'invoke\' with coroutine"

    invoke-direct {v1, v2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw v1

    :cond_2
    iget-object v1, v4, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager$innerLoadAd$1;->L$3:Ljava/lang/Object;

    check-cast v1, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;

    iget-object v2, v4, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager$innerLoadAd$1;->L$2:Ljava/lang/Object;

    check-cast v2, Ljava/util/Map;

    iget-object v6, v4, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager$innerLoadAd$1;->L$1:Ljava/lang/Object;

    check-cast v6, Ljava/lang/String;

    iget-object v10, v4, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager$innerLoadAd$1;->L$0:Ljava/lang/Object;

    check-cast v10, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;

    invoke-static {v3}, Lkotlin/ResultKt;->b(Ljava/lang/Object;)V

    move-object/from16 v17, v3

    move-object v3, v1

    move-object v1, v6

    move-object/from16 v6, v17

    goto :goto_1

    :cond_3
    invoke-static {v3}, Lkotlin/ResultKt;->b(Ljava/lang/Object;)V

    sget-object v3, Lqt/a;->a:Lqt/a;

    const-class v6, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;

    invoke-virtual {v6}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    move-result-object v6

    new-instance v10, Ljava/lang/StringBuilder;

    invoke-direct {v10}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v10, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v6, " --> loadNativeAd() --> \u5f00\u59cb\u52a0\u8f7d\u5e7f\u544a --> sceneId = "

    invoke-virtual {v10, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v10, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v6, " --> ctxMap = "

    invoke-virtual {v10, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v10, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    invoke-virtual {v10}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v6

    const/4 v10, 0x0

    invoke-virtual {v3, v6, v10}, Lqt/a;->E(Ljava/lang/String;Z)V

    move-object/from16 v3, p2

    iput-object v3, v0, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;->mCallback:Lcom/transsion/wrapperad/middle/WrapperAdListener;

    iput-object v1, v0, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;->mSceneId:Ljava/lang/String;

    invoke-static {}, Lkotlinx/coroutines/w0;->b()Lkotlinx/coroutines/CoroutineDispatcher;

    move-result-object v3

    new-instance v6, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager$innerLoadAd$2;

    invoke-direct {v6, v1, v2, v7}, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager$innerLoadAd$2;-><init>(Ljava/lang/String;Ljava/util/Map;Lkotlin/coroutines/Continuation;)V

    iput-object v0, v4, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager$innerLoadAd$1;->L$0:Ljava/lang/Object;

    iput-object v1, v4, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager$innerLoadAd$1;->L$1:Ljava/lang/Object;

    iput-object v2, v4, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager$innerLoadAd$1;->L$2:Ljava/lang/Object;

    iput-object v0, v4, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager$innerLoadAd$1;->L$3:Ljava/lang/Object;

    iput v9, v4, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager$innerLoadAd$1;->label:I

    invoke-static {v3, v6, v4}, Lkotlinx/coroutines/h;->g(Lkotlin/coroutines/CoroutineContext;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;

    move-result-object v3

    if-ne v3, v5, :cond_4

    return-object v5

    :cond_4
    move-object v10, v0

    move-object v6, v3

    move-object v3, v10

    :goto_1
    check-cast v6, Lcom/transsion/wrapperad/monopoly/model/AdPlans;

    iput-object v6, v3, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;->mAdPlans:Lcom/transsion/wrapperad/monopoly/model/AdPlans;

    invoke-static {}, Lkotlinx/coroutines/w0;->b()Lkotlinx/coroutines/CoroutineDispatcher;

    move-result-object v3

    new-instance v6, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager$innerLoadAd$3;

    invoke-direct {v6, v1, v2, v7}, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager$innerLoadAd$3;-><init>(Ljava/lang/String;Ljava/util/Map;Lkotlin/coroutines/Continuation;)V

    iput-object v10, v4, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager$innerLoadAd$1;->L$0:Ljava/lang/Object;

    iput-object v1, v4, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager$innerLoadAd$1;->L$1:Ljava/lang/Object;

    iput-object v10, v4, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager$innerLoadAd$1;->L$2:Ljava/lang/Object;

    iput-object v7, v4, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager$innerLoadAd$1;->L$3:Ljava/lang/Object;

    iput v8, v4, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager$innerLoadAd$1;->label:I

    invoke-static {v3, v6, v4}, Lkotlinx/coroutines/h;->g(Lkotlin/coroutines/CoroutineContext;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;

    move-result-object v3

    if-ne v3, v5, :cond_5

    return-object v5

    :cond_5
    move-object v2, v1

    move-object v1, v10

    move-object v4, v1

    :goto_2
    check-cast v3, Lcom/transsion/wrapperad/monopoly/model/AdPlans;

    iput-object v3, v1, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;->mAdShowFinalPlan:Lcom/transsion/wrapperad/monopoly/model/AdPlans;

    sget-object v10, Lcom/transsion/wrapperad/a;->a:Lcom/transsion/wrapperad/a;

    const/4 v11, 0x0

    const/4 v13, 0x2

    iget-object v1, v4, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;->mAdPlans:Lcom/transsion/wrapperad/monopoly/model/AdPlans;

    if-nez v1, :cond_6

    const/4 v14, 0x2

    goto :goto_3

    :cond_6
    const/4 v14, 0x1

    :goto_3
    const/4 v15, 0x1

    const/16 v16, 0x0

    move-object v12, v2

    invoke-static/range {v10 .. v16}, Lcom/transsion/wrapperad/a;->j(Lcom/transsion/wrapperad/a;Ljava/lang/String;Ljava/lang/String;IIILjava/lang/Object;)V

    iget-object v1, v4, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;->mAdPlans:Lcom/transsion/wrapperad/monopoly/model/AdPlans;

    if-nez v1, :cond_7

    invoke-direct {v4, v2}, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;->innerLoadHiSavanaAd(Ljava/lang/String;)V

    goto :goto_4

    :cond_7
    sget-object v1, Lpt/d;->a:Lpt/d;

    invoke-virtual {v1, v2}, Lpt/d;->b(Ljava/lang/String;)Z

    move-result v1

    if-nez v1, :cond_8

    invoke-direct {v4}, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;->innerLoadAdPlan()V

    goto :goto_4

    :cond_8
    invoke-direct {v4, v2}, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;->innerLoadHiSavanaAd(Ljava/lang/String;)V

    :goto_4
    sget-object v1, Lkotlin/Unit;->a:Lkotlin/Unit;

    return-object v1
.end method

.method private final innerLoadAdPlan()V
    .locals 4

    sget-object v0, Lqt/a;->a:Lqt/a;

    const-class v1, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;

    invoke-virtual {v1}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    move-result-object v1

    iget-object v2, p0, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;->mSceneId:Ljava/lang/String;

    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v3, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v1, " --> innerLoadAdPlan() --> \u5305\u65ad\u5e7f\u544a\u53ef\u4ee5\u4f7f\u7528 --> sceneId = "

    invoke-virtual {v3, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    const/4 v2, 0x0

    invoke-virtual {v0, v1, v2}, Lqt/a;->E(Ljava/lang/String;Z)V

    iget-object v0, p0, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;->mAdPlans:Lcom/transsion/wrapperad/monopoly/model/AdPlans;

    invoke-direct {p0, v0}, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;->onNonNativeReady(Lcom/transsion/wrapperad/monopoly/model/AdPlans;)V

    return-void
.end method

.method private final innerLoadAdShowFinal()V
    .locals 5

    iget-object v0, p0, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;->mAdShowFinalPlan:Lcom/transsion/wrapperad/monopoly/model/AdPlans;

    const/4 v1, 0x0

    const-class v2, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;

    if-nez v0, :cond_0

    sget-object v0, Lqt/a;->a:Lqt/a;

    invoke-virtual {v2}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    move-result-object v2

    iget-object v3, p0, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;->mSceneId:Ljava/lang/String;

    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v4, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v2, " --> innerLoadAdShowFinal() --> \u5f53\u524d\u6ca1\u6709\u515c\u5e95\u5e7f\u544a --> sceneId = "

    invoke-virtual {v4, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v0, v2, v1}, Lqt/a;->E(Ljava/lang/String;Z)V

    iget-object v0, p0, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;->mCallback:Lcom/transsion/wrapperad/middle/WrapperAdListener;

    if-eqz v0, :cond_1

    new-instance v1, Lcom/hisavana/common/bean/TAdErrorCode;

    iget-object v2, p0, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;->mSceneId:Ljava/lang/String;

    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v4, "\u6ca1\u6709\u515c\u5e95\u5e7f\u544a --> sceneId = "

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    const/16 v3, 0x65

    invoke-direct {v1, v3, v2}, Lcom/hisavana/common/bean/TAdErrorCode;-><init>(ILjava/lang/String;)V

    invoke-virtual {v0, v1}, Lcom/transsion/wrapperad/middle/WrapperAdListener;->onError(Lcom/hisavana/common/bean/TAdErrorCode;)V

    goto :goto_0

    :cond_0
    sget-object v0, Lqt/a;->a:Lqt/a;

    invoke-virtual {v2}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    move-result-object v2

    iget-object v3, p0, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;->mSceneId:Ljava/lang/String;

    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v4, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v2, " --> innerLoadAdShowFinal() --> \u5f53\u524d\u6709\u515c\u5e95\u5e7f\u544a\u53ef\u7528 --> sceneId = "

    invoke-virtual {v4, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v0, v2, v1}, Lqt/a;->E(Ljava/lang/String;Z)V

    iget-object v0, p0, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;->mAdShowFinalPlan:Lcom/transsion/wrapperad/monopoly/model/AdPlans;

    invoke-direct {p0, v0}, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;->onNonNativeReady(Lcom/transsion/wrapperad/monopoly/model/AdPlans;)V

    :cond_1
    :goto_0
    return-void
.end method

.method private final innerLoadHiSavanaAd(Ljava/lang/String;)V
    .locals 2

    sget-object v0, Lpt/d;->a:Lpt/d;

    invoke-virtual {v0, p1}, Lpt/d;->a(Ljava/lang/String;)Z

    move-result p1

    if-nez p1, :cond_1

    sget-object p1, Lcom/transsion/wrapperad/middle/nativead/HiSavanaNativeAdManager;->Companion:Lcom/transsion/wrapperad/middle/nativead/HiSavanaNativeAdManager$a;

    sget-object v0, Lpt/b;->a:Lpt/b;

    iget-object v1, p0, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;->mSceneId:Ljava/lang/String;

    invoke-virtual {v0, v1}, Lpt/b;->a(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p1, v0}, Lcom/transsion/wrapperad/middle/nativead/HiSavanaNativeAdManager$a;->b(Ljava/lang/String;)Lcom/transsion/wrapperad/middle/nativead/HiSavanaNativeAdManager;

    move-result-object p1

    if-eqz p1, :cond_0

    iget-object v0, p0, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;->mSceneId:Ljava/lang/String;

    invoke-virtual {p1, p0, v0}, Lcom/transsion/wrapperad/middle/nativead/HiSavanaNativeAdManager;->addCallback(Lcom/transsion/wrapperad/middle/WrapperAdListener;Ljava/lang/String;)V

    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    :goto_0
    if-nez p1, :cond_2

    invoke-direct {p0}, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;->innerLoadAdShowFinal()V

    goto :goto_1

    :cond_1
    invoke-direct {p0}, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;->innerLoadAdShowFinal()V

    :cond_2
    :goto_1
    return-void
.end method

.method private final innerShowHiSavanaAd(Landroid/widget/FrameLayout;)V
    .locals 8

    sget-object v0, Lcom/transsion/wrapperad/middle/nativead/HiSavanaNativeAdManager;->Companion:Lcom/transsion/wrapperad/middle/nativead/HiSavanaNativeAdManager$a;

    sget-object v1, Lpt/b;->a:Lpt/b;

    iget-object v2, p0, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;->mSceneId:Ljava/lang/String;

    invoke-virtual {v1, v2}, Lpt/b;->a(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Lcom/transsion/wrapperad/middle/nativead/HiSavanaNativeAdManager$a;->b(Ljava/lang/String;)Lcom/transsion/wrapperad/middle/nativead/HiSavanaNativeAdManager;

    move-result-object v0

    if-eqz v0, :cond_5

    invoke-virtual {v0}, Lcom/transsion/wrapperad/middle/nativead/HiSavanaNativeAdManager;->getNativeAd()Lcom/hisavana/mediation/ad/TNativeAd;

    move-result-object v0

    if-eqz v0, :cond_5

    iget-object v1, p0, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;->mNativeInfo:Lcom/hisavana/common/bean/TAdNativeInfo;

    const/4 v2, 0x0

    const/4 v3, 0x0

    if-eqz v1, :cond_4

    sget-object v4, Lcom/transsion/wrapperad/middle/nativead/ViewBinderManager;->a:Lcom/transsion/wrapperad/middle/nativead/ViewBinderManager;

    iget-object v5, p0, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;->mSceneId:Ljava/lang/String;

    if-eqz p1, :cond_0

    invoke-virtual {p1}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v6

    goto :goto_0

    :cond_0
    move-object v6, v3

    :goto_0
    invoke-virtual {v4, v5, v2, v1, v6}, Lcom/transsion/wrapperad/middle/nativead/ViewBinderManager;->o(Ljava/lang/String;ZLcom/hisavana/common/bean/TAdNativeInfo;Landroid/content/Context;)Lcom/hisavana/mediation/ad/ViewBinder;

    move-result-object v4

    iget-object v5, p0, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;->mTAdNativeView:Lcom/hisavana/mediation/ad/TAdNativeView;

    if-nez v5, :cond_1

    new-instance v5, Lcom/hisavana/mediation/ad/TAdNativeView;

    invoke-static {}, Lcom/blankj/utilcode/util/Utils;->a()Landroid/app/Application;

    move-result-object v6

    invoke-direct {v5, v6}, Lcom/hisavana/mediation/ad/TAdNativeView;-><init>(Landroid/content/Context;)V

    iput-object v5, p0, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;->mTAdNativeView:Lcom/hisavana/mediation/ad/TAdNativeView;

    goto :goto_1

    :cond_1
    if-eqz v5, :cond_2

    invoke-virtual {v5}, Landroid/view/ViewGroup;->removeAllViews()V

    :cond_2
    sget-object v5, Lqt/j;->a:Lqt/j;

    iget-object v6, p0, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;->mTAdNativeView:Lcom/hisavana/mediation/ad/TAdNativeView;

    invoke-virtual {v5, v6}, Lqt/j;->a(Landroid/view/View;)V

    :goto_1
    iget-object v5, p0, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;->mTAdNativeView:Lcom/hisavana/mediation/ad/TAdNativeView;

    if-eqz v5, :cond_4

    iget-object v6, p0, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;->mSceneId:Ljava/lang/String;

    const/4 v7, 0x1

    invoke-virtual {v0, v6, v7}, Lyh/b;->enterScene(Ljava/lang/String;I)Ljava/lang/String;

    move-result-object v6

    invoke-virtual {v0, v5, v1, v4, v6}, Lcom/hisavana/mediation/ad/TNativeAd;->bindNativeView(Lcom/hisavana/mediation/ad/TAdNativeView;Lcom/hisavana/common/bean/TAdNativeInfo;Lcom/hisavana/mediation/ad/ViewBinder;Ljava/lang/String;)V

    iget-object v0, p0, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;->mSceneId:Ljava/lang/String;

    invoke-static {v0}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    if-eqz v0, :cond_3

    const-string v0, "tag_mSceneId"

    goto :goto_2

    :cond_3
    iget-object v0, p0, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;->mSceneId:Ljava/lang/String;

    :goto_2
    invoke-virtual {v1, v0}, Lcom/hisavana/common/bean/TAdNativeInfo;->setSceneId(Ljava/lang/String;)V

    invoke-direct {p0, v5, p1}, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;->innerAddViewShowAd(Landroid/view/View;Landroid/widget/FrameLayout;)V

    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    goto :goto_3

    :cond_4
    move-object p1, v3

    :goto_3
    if-nez p1, :cond_5

    sget-object p1, Lqt/a;->a:Lqt/a;

    const-class v0, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;

    invoke-virtual {v0}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    move-result-object v0

    iget-object v1, p0, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;->mSceneId:Ljava/lang/String;

    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v4, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v0, " --> bindNativeView() --> sceneId = "

    invoke-virtual {v4, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v0, " \u573a\u666f\u63a5\u53d7\u5230\u56de\u8c03\u4e86 --> null == mNativeInfo"

    invoke-virtual {v4, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    const/4 v1, 0x2

    invoke-static {p1, v0, v2, v1, v3}, Lqt/a;->F(Lqt/a;Ljava/lang/String;ZILjava/lang/Object;)V

    :cond_5
    return-void
.end method

.method private final innerShowNonNativeAd(Landroid/widget/FrameLayout;ZZ)V
    .locals 8

    iget-object v0, p0, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;->buyOutNativeView:Lcom/transsion/wrapperad/middle/nativead/BuyOutNativeView;

    if-nez v0, :cond_0

    new-instance v0, Lcom/transsion/wrapperad/middle/nativead/BuyOutNativeView;

    invoke-static {}, Lcom/blankj/utilcode/util/Utils;->a()Landroid/app/Application;

    move-result-object v1

    const-string v2, "getApp()"

    invoke-static {v1, v2}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-direct {v0, v1}, Lcom/transsion/wrapperad/middle/nativead/BuyOutNativeView;-><init>(Landroid/content/Context;)V

    iput-object v0, p0, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;->buyOutNativeView:Lcom/transsion/wrapperad/middle/nativead/BuyOutNativeView;

    iget-object v1, p0, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;->enableBgBlur:Ljava/lang/Boolean;

    invoke-virtual {v0, v1}, Lcom/transsion/wrapperad/middle/nativead/BuyOutNativeView;->enableBgBlur(Ljava/lang/Boolean;)V

    iget-object v0, p0, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;->buyOutNativeView:Lcom/transsion/wrapperad/middle/nativead/BuyOutNativeView;

    if-eqz v0, :cond_2

    invoke-virtual {v0, p0}, Lcom/transsion/wrapperad/middle/nativead/BuyOutNativeView;->setCallback(Lcom/transsion/wrapperad/middle/WrapperAdListener;)V

    goto :goto_0

    :cond_0
    if-eqz v0, :cond_1

    invoke-virtual {v0}, Landroid/view/ViewGroup;->removeAllViews()V

    :cond_1
    sget-object v0, Lqt/j;->a:Lqt/j;

    iget-object v1, p0, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;->buyOutNativeView:Lcom/transsion/wrapperad/middle/nativead/BuyOutNativeView;

    invoke-virtual {v0, v1}, Lqt/j;->a(Landroid/view/View;)V

    :cond_2
    :goto_0
    iget-object v2, p0, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;->buyOutNativeView:Lcom/transsion/wrapperad/middle/nativead/BuyOutNativeView;

    if-eqz v2, :cond_4

    iget-object v3, p0, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;->mSceneId:Ljava/lang/String;

    iget-object v4, p0, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;->mNonAdPlans:Lcom/transsion/wrapperad/monopoly/model/AdPlans;

    sget-object v0, Lcom/transsion/wrapperad/middle/nativead/ViewBinderManager;->a:Lcom/transsion/wrapperad/middle/nativead/ViewBinderManager;

    const/4 v1, 0x0

    if-eqz p1, :cond_3

    invoke-virtual {p1}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v5

    goto :goto_1

    :cond_3
    move-object v5, v1

    :goto_1
    const/4 v6, 0x1

    invoke-virtual {v0, v3, v6, v1, v5}, Lcom/transsion/wrapperad/middle/nativead/ViewBinderManager;->o(Ljava/lang/String;ZLcom/hisavana/common/bean/TAdNativeInfo;Landroid/content/Context;)Lcom/hisavana/mediation/ad/ViewBinder;

    move-result-object v5

    move v6, p2

    move v7, p3

    invoke-virtual/range {v2 .. v7}, Lcom/transsion/wrapperad/middle/nativead/BuyOutNativeView;->bindNativeView(Ljava/lang/String;Lcom/transsion/wrapperad/monopoly/model/AdPlans;Lcom/hisavana/mediation/ad/ViewBinder;ZZ)V

    :cond_4
    iget-object p2, p0, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;->buyOutNativeView:Lcom/transsion/wrapperad/middle/nativead/BuyOutNativeView;

    invoke-direct {p0, p2, p1}, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;->innerAddViewShowAd(Landroid/view/View;Landroid/widget/FrameLayout;)V

    return-void
.end method

.method public static synthetic loadNativeAd$default(Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;Ljava/lang/String;Lcom/transsion/wrapperad/middle/WrapperAdListener;Ljava/util/Map;Lkotlin/coroutines/Continuation;ILjava/lang/Object;)Ljava/lang/Object;
    .locals 0

    and-int/lit8 p5, p5, 0x4

    if-eqz p5, :cond_0

    invoke-static {}, Lkotlin/collections/MapsKt;->h()Ljava/util/Map;

    move-result-object p3

    :cond_0
    invoke-virtual {p0, p1, p2, p3, p4}, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;->loadNativeAd(Ljava/lang/String;Lcom/transsion/wrapperad/middle/WrapperAdListener;Ljava/util/Map;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;

    move-result-object p0

    return-object p0
.end method

.method private final onNonNativeReady(Lcom/transsion/wrapperad/monopoly/model/AdPlans;)V
    .locals 0

    iput-object p1, p0, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;->mNonAdPlans:Lcom/transsion/wrapperad/monopoly/model/AdPlans;

    iget-object p1, p0, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;->mCallback:Lcom/transsion/wrapperad/middle/WrapperAdListener;

    if-eqz p1, :cond_0

    invoke-virtual {p1}, Lcom/transsion/wrapperad/middle/WrapperAdListener;->onLoad()V

    :cond_0
    return-void
.end method

.method public static synthetic showNativeAd$default(Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;Landroid/widget/FrameLayout;ZZILjava/lang/Object;)V
    .locals 1

    and-int/lit8 p5, p4, 0x2

    const/4 v0, 0x1

    if-eqz p5, :cond_0

    const/4 p2, 0x1

    :cond_0
    and-int/lit8 p4, p4, 0x4

    if-eqz p4, :cond_1

    const/4 p3, 0x1

    :cond_1
    invoke-virtual {p0, p1, p2, p3}, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;->showNativeAd(Landroid/widget/FrameLayout;ZZ)V

    return-void
.end method


# virtual methods
.method public describeContents()I
    .locals 1

    const/4 v0, 0x0

    return v0
.end method

.method public final destroy()V
    .locals 0

    invoke-direct {p0}, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;->innerDestroy()V

    return-void
.end method

.method public final enableBgBlur(Z)V
    .locals 0

    invoke-static {p1}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object p1

    iput-object p1, p0, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;->enableBgBlur:Ljava/lang/Boolean;

    return-void
.end method

.method public final isReady()Z
    .locals 1

    iget-object v0, p0, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;->mAdPlans:Lcom/transsion/wrapperad/monopoly/model/AdPlans;

    if-nez v0, :cond_1

    iget-object v0, p0, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;->mNativeInfo:Lcom/hisavana/common/bean/TAdNativeInfo;

    if-eqz v0, :cond_0

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 v0, 0x1

    :goto_1
    return v0
.end method

.method public final loadNativeAd(Ljava/lang/String;Lcom/transsion/wrapperad/middle/WrapperAdListener;Ljava/util/Map;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Lcom/transsion/wrapperad/middle/WrapperAdListener;",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "+",
            "Ljava/lang/Object;",
            ">;",
            "Lkotlin/coroutines/Continuation<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    sget-object v0, Lpt/d;->a:Lpt/d;

    invoke-virtual {v0, p1, p2}, Lpt/d;->d(Ljava/lang/String;Lcom/transsion/wrapperad/middle/WrapperAdListener;)Z

    move-result v0

    if-eqz v0, :cond_0

    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    return-object p1

    :cond_0
    iput-object p3, p0, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;->ctxMap:Ljava/util/Map;

    invoke-direct {p0, p1, p2, p3, p4}, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;->innerLoadAd(Ljava/lang/String;Lcom/transsion/wrapperad/middle/WrapperAdListener;Ljava/util/Map;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;

    move-result-object p1

    invoke-static {}, Lkotlin/coroutines/intrinsics/IntrinsicsKt;->e()Ljava/lang/Object;

    move-result-object p2

    if-ne p1, p2, :cond_1

    return-object p1

    :cond_1
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    return-object p1
.end method

.method public onClicked(I)V
    .locals 1

    invoke-super {p0, p1}, Lcom/transsion/wrapperad/middle/WrapperAdListener;->onClicked(I)V

    iget-object v0, p0, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;->mCallback:Lcom/transsion/wrapperad/middle/WrapperAdListener;

    if-eqz v0, :cond_0

    invoke-virtual {v0, p1}, Lcom/transsion/wrapperad/middle/WrapperAdListener;->onClicked(I)V

    :cond_0
    return-void
.end method

.method public onError(Lcom/hisavana/common/bean/TAdErrorCode;)V
    .locals 0

    invoke-super {p0, p1}, Lcom/transsion/wrapperad/middle/WrapperAdListener;->onError(Lcom/hisavana/common/bean/TAdErrorCode;)V

    invoke-direct {p0}, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;->innerLoadAdShowFinal()V

    return-void
.end method

.method public onNativeInfoReady(Lcom/hisavana/common/bean/TAdNativeInfo;)V
    .locals 10

    invoke-super {p0, p1}, Lcom/transsion/wrapperad/middle/WrapperAdListener;->onNativeInfoReady(Lcom/hisavana/common/bean/TAdNativeInfo;)V

    sget-object v0, Lcom/transsion/wrapperad/strategy/b;->a:Lcom/transsion/wrapperad/strategy/b;

    invoke-virtual {v0}, Lcom/transsion/wrapperad/strategy/b;->b()Ljava/lang/String;

    move-result-object v1

    sget-object v2, Lqt/a;->a:Lqt/a;

    const-class v3, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;

    invoke-virtual {v3}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    move-result-object v4

    const/4 v5, 0x1

    const/4 v6, 0x0

    if-eqz p1, :cond_0

    invoke-interface {p1}, Lcom/hisavana/common/interfacz/ICacheAd;->isMatchVulgarBrand()Z

    move-result v7

    if-ne v7, v5, :cond_0

    const/4 v7, 0x1

    goto :goto_0

    :cond_0
    const/4 v7, 0x0

    :goto_0
    iget-object v8, p0, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;->ctxMap:Ljava/util/Map;

    new-instance v9, Ljava/lang/StringBuilder;

    invoke-direct {v9}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v9, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v4, " --> onNativeInfoReady() --> HiSavana \u5e7f\u544a\u51c6\u5907\u5b8c\u6210 --> isMatchVulgarBrand = "

    invoke-virtual {v9, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v9, v7}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    const-string v4, " --> genre = "

    invoke-virtual {v9, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v9, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v4, " -- ctxMap = "

    invoke-virtual {v9, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v9, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    invoke-virtual {v9}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v7

    invoke-virtual {v2, v7, v6}, Lqt/a;->E(Ljava/lang/String;Z)V

    if-eqz p1, :cond_2

    invoke-interface {p1}, Lcom/hisavana/common/interfacz/ICacheAd;->isMatchVulgarBrand()Z

    move-result v7

    if-ne v7, v5, :cond_2

    iget-object v5, p0, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;->ctxMap:Ljava/util/Map;

    invoke-virtual {v0, v5}, Lcom/transsion/wrapperad/strategy/b;->d(Ljava/util/Map;)Z

    move-result v0

    if-eqz v0, :cond_2

    const/4 v0, 0x0

    invoke-virtual {p0, v0}, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;->onError(Lcom/hisavana/common/bean/TAdErrorCode;)V

    sget-object v5, Lcom/transsion/wrapperad/middle/nativead/HiSavanaNativeAdManager;->Companion:Lcom/transsion/wrapperad/middle/nativead/HiSavanaNativeAdManager$a;

    sget-object v7, Lpt/b;->a:Lpt/b;

    iget-object v8, p0, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;->mSceneId:Ljava/lang/String;

    invoke-virtual {v7, v8}, Lpt/b;->a(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v7

    invoke-virtual {v5, v7}, Lcom/transsion/wrapperad/middle/nativead/HiSavanaNativeAdManager$a;->b(Ljava/lang/String;)Lcom/transsion/wrapperad/middle/nativead/HiSavanaNativeAdManager;

    move-result-object v5

    if-eqz v5, :cond_1

    invoke-virtual {v5, p1}, Lcom/transsion/wrapperad/middle/nativead/HiSavanaNativeAdManager;->addUnusedAdToPoolLast(Lcom/hisavana/common/bean/TAdNativeInfo;)V

    :cond_1
    invoke-virtual {v3}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    move-result-object p1

    iget-object v3, p0, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;->ctxMap:Ljava/util/Map;

    new-instance v5, Ljava/lang/StringBuilder;

    invoke-direct {v5}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v5, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string p1, " --> \u5f53\u524d\u662f\u54c1\u724c\u5e7f\u544a -- \u5f53\u524d\u662f\u9650\u5236\u573a\u666f --> \u9700\u8981\u5c4f\u853d --> \u91cd\u65b0\u6dfb\u52a0\u5230\u7f13\u5b58\u6c60\u4e2d --> genre = "

    invoke-virtual {v5, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v5, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v5, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v5, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    invoke-virtual {v5}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    const/4 v1, 0x2

    invoke-static {v2, p1, v6, v1, v0}, Lqt/a;->H(Lqt/a;Ljava/lang/String;ZILjava/lang/Object;)V

    return-void

    :cond_2
    if-nez p1, :cond_3

    goto :goto_1

    :cond_3
    iget-object v0, p0, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;->mSceneId:Ljava/lang/String;

    invoke-interface {p1, v0}, Lcom/hisavana/common/interfacz/ICacheAd;->setExt(Ljava/lang/String;)V

    :goto_1
    iput-object p1, p0, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;->mNativeInfo:Lcom/hisavana/common/bean/TAdNativeInfo;

    iget-object p1, p0, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;->mCallback:Lcom/transsion/wrapperad/middle/WrapperAdListener;

    if-eqz p1, :cond_4

    invoke-virtual {p1}, Lcom/transsion/wrapperad/middle/WrapperAdListener;->onLoad()V

    :cond_4
    return-void
.end method

.method public final showNativeAd(Landroid/widget/FrameLayout;ZZ)V
    .locals 2

    :try_start_0
    sget-object v0, Lkotlin/Result;->Companion:Lkotlin/Result$Companion;

    invoke-direct {p0, p1, p2, p3}, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;->innerAddViewShowAd(Landroid/widget/FrameLayout;ZZ)V

    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-static {p1}, Lkotlin/Result;->constructor-impl(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    goto :goto_0

    :catchall_0
    move-exception p1

    sget-object p2, Lkotlin/Result;->Companion:Lkotlin/Result$Companion;

    invoke-static {p1}, Lkotlin/ResultKt;->a(Ljava/lang/Throwable;)Ljava/lang/Object;

    move-result-object p1

    invoke-static {p1}, Lkotlin/Result;->constructor-impl(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    :goto_0
    invoke-static {p1}, Lkotlin/Result;->exceptionOrNull-impl(Ljava/lang/Object;)Ljava/lang/Throwable;

    move-result-object p1

    if-nez p1, :cond_0

    goto :goto_1

    :cond_0
    sget-object p2, Lqt/a;->a:Lqt/a;

    const-class p3, Lcom/transsion/wrapperad/middle/nativead/WrapperNativeManager;

    invoke-virtual {p3}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    move-result-object p3

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v0, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string p3, " --> showNativeAd() --> \u5e7f\u544a\u52a0\u8f7d\u5931\u8d25 --> it = "

    invoke-virtual {v0, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    const/4 p3, 0x2

    const/4 v0, 0x0

    const/4 v1, 0x0

    invoke-static {p2, p1, v1, p3, v0}, Lqt/a;->F(Lqt/a;Ljava/lang/String;ZILjava/lang/Object;)V

    :goto_1
    return-void
.end method

.method public writeToParcel(Landroid/os/Parcel;I)V
    .locals 0

    const-string p2, "out"

    invoke-static {p1, p2}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    const/4 p2, 0x1

    invoke-virtual {p1, p2}, Landroid/os/Parcel;->writeInt(I)V

    return-void
.end method
