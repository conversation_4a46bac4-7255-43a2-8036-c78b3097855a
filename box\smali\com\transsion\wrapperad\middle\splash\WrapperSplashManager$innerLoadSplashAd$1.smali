.class final Lcom/transsion/wrapperad/middle/splash/WrapperSplashManager$innerLoadSplashAd$1;
.super Lkotlin/coroutines/jvm/internal/ContinuationImpl;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/transsion/wrapperad/middle/splash/WrapperSplashManager;->d(Ljava/lang/String;Lcom/transsion/wrapperad/middle/WrapperAdListener;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation

.annotation runtime Lkotlin/coroutines/jvm/internal/DebugMetadata;
    c = "com.transsion.wrapperad.middle.splash.WrapperSplashManager"
    f = "WrapperSplashManager.kt"
    l = {
        0x55,
        0x5b
    }
    m = "innerLoadSplashAd"
.end annotation


# instance fields
.field L$0:Ljava/lang/Object;

.field L$1:Ljava/lang/Object;

.field L$2:Ljava/lang/Object;

.field label:I

.field synthetic result:Ljava/lang/Object;

.field final synthetic this$0:Lcom/transsion/wrapperad/middle/splash/WrapperSplashManager;


# direct methods
.method public constructor <init>(Lcom/transsion/wrapperad/middle/splash/WrapperSplashManager;Lkotlin/coroutines/Continuation;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/transsion/wrapperad/middle/splash/WrapperSplashManager;",
            "Lkotlin/coroutines/Continuation<",
            "-",
            "Lcom/transsion/wrapperad/middle/splash/WrapperSplashManager$innerLoadSplashAd$1;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lcom/transsion/wrapperad/middle/splash/WrapperSplashManager$innerLoadSplashAd$1;->this$0:Lcom/transsion/wrapperad/middle/splash/WrapperSplashManager;

    invoke-direct {p0, p2}, Lkotlin/coroutines/jvm/internal/ContinuationImpl;-><init>(Lkotlin/coroutines/Continuation;)V

    return-void
.end method


# virtual methods
.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    iput-object p1, p0, Lcom/transsion/wrapperad/middle/splash/WrapperSplashManager$innerLoadSplashAd$1;->result:Ljava/lang/Object;

    iget p1, p0, Lcom/transsion/wrapperad/middle/splash/WrapperSplashManager$innerLoadSplashAd$1;->label:I

    const/high16 v0, -0x80000000

    or-int/2addr p1, v0

    iput p1, p0, Lcom/transsion/wrapperad/middle/splash/WrapperSplashManager$innerLoadSplashAd$1;->label:I

    iget-object p1, p0, Lcom/transsion/wrapperad/middle/splash/WrapperSplashManager$innerLoadSplashAd$1;->this$0:Lcom/transsion/wrapperad/middle/splash/WrapperSplashManager;

    const/4 v0, 0x0

    invoke-static {p1, v0, v0, p0}, Lcom/transsion/wrapperad/middle/splash/WrapperSplashManager;->a(Lcom/transsion/wrapperad/middle/splash/WrapperSplashManager;Ljava/lang/String;Lcom/transsion/wrapperad/middle/WrapperAdListener;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method
