.class public final Lcom/transsion/wrapperad/middle/splash/WrapperSplashManager;
.super Ljava/lang/Object;


# annotations
.annotation runtime Lkotlin/Metadata;
.end annotation


# static fields
.field public static final a:Lcom/transsion/wrapperad/middle/splash/WrapperSplashManager;

.field public static b:Lcom/transsion/wrapperad/monopoly/model/AdPlans;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    new-instance v0, Lcom/transsion/wrapperad/middle/splash/WrapperSplashManager;

    invoke-direct {v0}, Lcom/transsion/wrapperad/middle/splash/WrapperSplashManager;-><init>()V

    sput-object v0, Lcom/transsion/wrapperad/middle/splash/WrapperSplashManager;->a:Lcom/transsion/wrapperad/middle/splash/WrapperSplashManager;

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static final synthetic a(Lcom/transsion/wrapperad/middle/splash/WrapperSplashManager;Ljava/lang/String;Lcom/transsion/wrapperad/middle/WrapperAdListener;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;
    .locals 0

    invoke-virtual {p0, p1, p2, p3}, Lcom/transsion/wrapperad/middle/splash/WrapperSplashManager;->d(Ljava/lang/String;Lcom/transsion/wrapperad/middle/WrapperAdListener;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;

    move-result-object p0

    return-object p0
.end method


# virtual methods
.method public final b()V
    .locals 1

    const/4 v0, 0x0

    sput-object v0, Lcom/transsion/wrapperad/middle/splash/WrapperSplashManager;->b:Lcom/transsion/wrapperad/monopoly/model/AdPlans;

    return-void
.end method

.method public final c()Lcom/transsion/wrapperad/monopoly/model/AdPlans;
    .locals 1

    sget-object v0, Lcom/transsion/wrapperad/middle/splash/WrapperSplashManager;->b:Lcom/transsion/wrapperad/monopoly/model/AdPlans;

    return-object v0
.end method

.method public final d(Ljava/lang/String;Lcom/transsion/wrapperad/middle/WrapperAdListener;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;
    .locals 14
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Lcom/transsion/wrapperad/middle/WrapperAdListener;",
            "Lkotlin/coroutines/Continuation<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    move-object v0, p0

    move-object v8, p1

    move-object/from16 v1, p3

    instance-of v2, v1, Lcom/transsion/wrapperad/middle/splash/WrapperSplashManager$innerLoadSplashAd$1;

    if-eqz v2, :cond_0

    move-object v2, v1

    check-cast v2, Lcom/transsion/wrapperad/middle/splash/WrapperSplashManager$innerLoadSplashAd$1;

    iget v3, v2, Lcom/transsion/wrapperad/middle/splash/WrapperSplashManager$innerLoadSplashAd$1;->label:I

    const/high16 v4, -0x80000000

    and-int v5, v3, v4

    if-eqz v5, :cond_0

    sub-int/2addr v3, v4

    iput v3, v2, Lcom/transsion/wrapperad/middle/splash/WrapperSplashManager$innerLoadSplashAd$1;->label:I

    :goto_0
    move-object v9, v2

    goto :goto_1

    :cond_0
    new-instance v2, Lcom/transsion/wrapperad/middle/splash/WrapperSplashManager$innerLoadSplashAd$1;

    invoke-direct {v2, p0, v1}, Lcom/transsion/wrapperad/middle/splash/WrapperSplashManager$innerLoadSplashAd$1;-><init>(Lcom/transsion/wrapperad/middle/splash/WrapperSplashManager;Lkotlin/coroutines/Continuation;)V

    goto :goto_0

    :goto_1
    iget-object v1, v9, Lcom/transsion/wrapperad/middle/splash/WrapperSplashManager$innerLoadSplashAd$1;->result:Ljava/lang/Object;

    invoke-static {}, Lkotlin/coroutines/intrinsics/IntrinsicsKt;->e()Ljava/lang/Object;

    move-result-object v10

    iget v2, v9, Lcom/transsion/wrapperad/middle/splash/WrapperSplashManager$innerLoadSplashAd$1;->label:I

    const/4 v11, 0x1

    const/4 v12, 0x0

    const/4 v13, 0x2

    if-eqz v2, :cond_3

    if-eq v2, v11, :cond_2

    if-ne v2, v13, :cond_1

    iget-object v2, v9, Lcom/transsion/wrapperad/middle/splash/WrapperSplashManager$innerLoadSplashAd$1;->L$2:Ljava/lang/Object;

    check-cast v2, Lcom/transsion/wrapperad/middle/WrapperAdListener;

    iget-object v3, v9, Lcom/transsion/wrapperad/middle/splash/WrapperSplashManager$innerLoadSplashAd$1;->L$1:Ljava/lang/Object;

    check-cast v3, Ljava/lang/String;

    iget-object v4, v9, Lcom/transsion/wrapperad/middle/splash/WrapperSplashManager$innerLoadSplashAd$1;->L$0:Ljava/lang/Object;

    check-cast v4, Lcom/transsion/wrapperad/middle/splash/WrapperSplashManager;

    invoke-static {v1}, Lkotlin/ResultKt;->b(Ljava/lang/Object;)V

    goto :goto_3

    :cond_1
    new-instance v1, Ljava/lang/IllegalStateException;

    const-string v2, "call to \'resume\' before \'invoke\' with coroutine"

    invoke-direct {v1, v2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw v1

    :cond_2
    iget-object v2, v9, Lcom/transsion/wrapperad/middle/splash/WrapperSplashManager$innerLoadSplashAd$1;->L$2:Ljava/lang/Object;

    check-cast v2, Lcom/transsion/wrapperad/middle/WrapperAdListener;

    iget-object v3, v9, Lcom/transsion/wrapperad/middle/splash/WrapperSplashManager$innerLoadSplashAd$1;->L$1:Ljava/lang/Object;

    check-cast v3, Ljava/lang/String;

    iget-object v4, v9, Lcom/transsion/wrapperad/middle/splash/WrapperSplashManager$innerLoadSplashAd$1;->L$0:Ljava/lang/Object;

    check-cast v4, Lcom/transsion/wrapperad/middle/splash/WrapperSplashManager;

    invoke-static {v1}, Lkotlin/ResultKt;->b(Ljava/lang/Object;)V

    goto :goto_2

    :cond_3
    invoke-static {v1}, Lkotlin/ResultKt;->b(Ljava/lang/Object;)V

    sget-object v1, Lcom/transsion/wrapperad/a;->a:Lcom/transsion/wrapperad/a;

    const/4 v2, 0x0

    const/4 v4, 0x5

    const/4 v5, 0x1

    const/4 v6, 0x1

    const/4 v7, 0x0

    move-object v3, p1

    invoke-static/range {v1 .. v7}, Lcom/transsion/wrapperad/a;->j(Lcom/transsion/wrapperad/a;Ljava/lang/String;Ljava/lang/String;IIILjava/lang/Object;)V

    invoke-static {}, Lkotlinx/coroutines/w0;->b()Lkotlinx/coroutines/CoroutineDispatcher;

    move-result-object v1

    new-instance v2, Lcom/transsion/wrapperad/middle/splash/WrapperSplashManager$innerLoadSplashAd$mAdPlans$1;

    invoke-direct {v2, p1, v12}, Lcom/transsion/wrapperad/middle/splash/WrapperSplashManager$innerLoadSplashAd$mAdPlans$1;-><init>(Ljava/lang/String;Lkotlin/coroutines/Continuation;)V

    iput-object v0, v9, Lcom/transsion/wrapperad/middle/splash/WrapperSplashManager$innerLoadSplashAd$1;->L$0:Ljava/lang/Object;

    iput-object v8, v9, Lcom/transsion/wrapperad/middle/splash/WrapperSplashManager$innerLoadSplashAd$1;->L$1:Ljava/lang/Object;

    move-object/from16 v3, p2

    iput-object v3, v9, Lcom/transsion/wrapperad/middle/splash/WrapperSplashManager$innerLoadSplashAd$1;->L$2:Ljava/lang/Object;

    iput v11, v9, Lcom/transsion/wrapperad/middle/splash/WrapperSplashManager$innerLoadSplashAd$1;->label:I

    invoke-static {v1, v2, v9}, Lkotlinx/coroutines/h;->g(Lkotlin/coroutines/CoroutineContext;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;

    move-result-object v1

    if-ne v1, v10, :cond_4

    return-object v10

    :cond_4
    move-object v4, v0

    move-object v2, v3

    move-object v3, v8

    :goto_2
    check-cast v1, Lcom/transsion/wrapperad/monopoly/model/AdPlans;

    if-nez v1, :cond_6

    invoke-static {}, Lkotlinx/coroutines/w0;->b()Lkotlinx/coroutines/CoroutineDispatcher;

    move-result-object v1

    new-instance v5, Lcom/transsion/wrapperad/middle/splash/WrapperSplashManager$innerLoadSplashAd$2;

    invoke-direct {v5, v3, v12}, Lcom/transsion/wrapperad/middle/splash/WrapperSplashManager$innerLoadSplashAd$2;-><init>(Ljava/lang/String;Lkotlin/coroutines/Continuation;)V

    iput-object v4, v9, Lcom/transsion/wrapperad/middle/splash/WrapperSplashManager$innerLoadSplashAd$1;->L$0:Ljava/lang/Object;

    iput-object v3, v9, Lcom/transsion/wrapperad/middle/splash/WrapperSplashManager$innerLoadSplashAd$1;->L$1:Ljava/lang/Object;

    iput-object v2, v9, Lcom/transsion/wrapperad/middle/splash/WrapperSplashManager$innerLoadSplashAd$1;->L$2:Ljava/lang/Object;

    iput v13, v9, Lcom/transsion/wrapperad/middle/splash/WrapperSplashManager$innerLoadSplashAd$1;->label:I

    invoke-static {v1, v5, v9}, Lkotlinx/coroutines/h;->g(Lkotlin/coroutines/CoroutineContext;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;

    move-result-object v1

    if-ne v1, v10, :cond_5

    return-object v10

    :cond_5
    :goto_3
    check-cast v1, Lcom/transsion/wrapperad/monopoly/model/AdPlans;

    :cond_6
    const/16 v5, 0x65

    const/4 v6, 0x0

    if-nez v1, :cond_7

    sget-object v1, Lqt/a;->a:Lqt/a;

    invoke-virtual {v4}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v4

    invoke-virtual {v4}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    move-result-object v4

    new-instance v7, Ljava/lang/StringBuilder;

    invoke-direct {v7}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v7, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v4, " --> innerLoadSplashAd() --> \u5f53\u524d\u6ca1\u6709\u914d\u7f6e\u975e\u6807\u5e7f\u544a --> sceneId = "

    invoke-virtual {v7, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v7, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v7}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v4

    invoke-static {v1, v4, v6, v13, v12}, Lqt/a;->L(Lqt/a;Ljava/lang/String;ZILjava/lang/Object;)V

    if-eqz v2, :cond_9

    new-instance v1, Lcom/hisavana/common/bean/TAdErrorCode;

    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    const-string v6, "\u5f53\u524d\u6ca1\u6709\u914d\u7f6e\u975e\u6807\u5e7f\u544a --> sceneId = "

    invoke-virtual {v4, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    invoke-direct {v1, v5, v3}, Lcom/hisavana/common/bean/TAdErrorCode;-><init>(ILjava/lang/String;)V

    invoke-virtual {v2, v1}, Lcom/transsion/wrapperad/middle/WrapperAdListener;->onError(Lcom/hisavana/common/bean/TAdErrorCode;)V

    goto :goto_4

    :cond_7
    sget-object v7, Lpt/d;->a:Lpt/d;

    invoke-virtual {v7, v3}, Lpt/d;->b(Ljava/lang/String;)Z

    move-result v7

    if-nez v7, :cond_8

    sput-object v1, Lcom/transsion/wrapperad/middle/splash/WrapperSplashManager;->b:Lcom/transsion/wrapperad/monopoly/model/AdPlans;

    if-eqz v2, :cond_9

    invoke-virtual {v2}, Lcom/transsion/wrapperad/middle/WrapperAdListener;->onLoad()V

    goto :goto_4

    :cond_8
    sget-object v1, Lqt/a;->a:Lqt/a;

    invoke-virtual {v4}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v4

    invoke-virtual {v4}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    move-result-object v4

    new-instance v7, Ljava/lang/StringBuilder;

    invoke-direct {v7}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v7, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v4, " --> innerLoadSplashAd() --> \u975e\u6807\u5e7f\u544a\u573a\u666f\u5173\u95ed --> sceneId = "

    invoke-virtual {v7, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v7, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v7}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v4

    invoke-static {v1, v4, v6, v13, v12}, Lqt/a;->L(Lqt/a;Ljava/lang/String;ZILjava/lang/Object;)V

    if-eqz v2, :cond_9

    new-instance v1, Lcom/hisavana/common/bean/TAdErrorCode;

    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    const-string v6, "\u975e\u6807\u5e7f\u544a\u573a\u666f\u5173\u95ed --> sceneId = "

    invoke-virtual {v4, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    invoke-direct {v1, v5, v3}, Lcom/hisavana/common/bean/TAdErrorCode;-><init>(ILjava/lang/String;)V

    invoke-virtual {v2, v1}, Lcom/transsion/wrapperad/middle/WrapperAdListener;->onError(Lcom/hisavana/common/bean/TAdErrorCode;)V

    :cond_9
    :goto_4
    sget-object v1, Lkotlin/Unit;->a:Lkotlin/Unit;

    return-object v1
.end method

.method public final e(Ljava/lang/String;Lcom/transsion/wrapperad/middle/WrapperAdListener;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Lcom/transsion/wrapperad/middle/WrapperAdListener;",
            "Lkotlin/coroutines/Continuation<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    sget-object v0, Lpt/d;->a:Lpt/d;

    invoke-virtual {v0, p1, p2}, Lpt/d;->d(Ljava/lang/String;Lcom/transsion/wrapperad/middle/WrapperAdListener;)Z

    move-result v0

    if-eqz v0, :cond_0

    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    return-object p1

    :cond_0
    invoke-virtual {p0, p1, p2, p3}, Lcom/transsion/wrapperad/middle/splash/WrapperSplashManager;->d(Ljava/lang/String;Lcom/transsion/wrapperad/middle/WrapperAdListener;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;

    move-result-object p1

    invoke-static {}, Lkotlin/coroutines/intrinsics/IntrinsicsKt;->e()Ljava/lang/Object;

    move-result-object p2

    if-ne p1, p2, :cond_1

    return-object p1

    :cond_1
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    return-object p1
.end method

.method public final f(Landroid/content/Context;)V
    .locals 2

    invoke-virtual {p0}, Lcom/transsion/wrapperad/middle/splash/WrapperSplashManager;->c()Lcom/transsion/wrapperad/monopoly/model/AdPlans;

    move-result-object v0

    if-eqz v0, :cond_0

    if-eqz p1, :cond_0

    new-instance v0, Landroid/content/Intent;

    const-class v1, Lcom/transsion/wrapperad/middle/splash/NonSplashActivity;

    invoke-direct {v0, p1, v1}, Landroid/content/Intent;-><init>(Landroid/content/Context;Ljava/lang/Class;)V

    invoke-virtual {p1, v0}, Landroid/content/Context;->startActivity(Landroid/content/Intent;)V

    :cond_0
    return-void
.end method
