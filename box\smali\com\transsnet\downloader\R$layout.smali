.class public final Lcom/transsnet/downloader/R$layout;
.super Ljava/lang/Object;


# static fields
.field public static activity_all_historical_play_record:I = 0x7f0d001f

.field public static activity_download_panel:I = 0x7f0d0028

.field public static activity_download_series_list:I = 0x7f0d0029

.field public static activity_transfer:I = 0x7f0d0067

.field public static adapter_download_item_ad:I = 0x7f0d0077

.field public static adapter_download_item_all_ep:I = 0x7f0d0078

.field public static adapter_download_item_for_you:I = 0x7f0d0079

.field public static adapter_download_item_header:I = 0x7f0d007a

.field public static adapter_download_item_hide_more:I = 0x7f0d007b

.field public static adapter_download_more_dialog_item:I = 0x7f0d007c

.field public static adapter_downloaded_item:I = 0x7f0d007d

.field public static adapter_downloading_item:I = 0x7f0d007e

.field public static adapter_empty_local_file_layout:I = 0x7f0d007f

.field public static adapter_historical_play_record_layout:I = 0x7f0d0082

.field public static adapter_transfer_tips_dialog_item:I = 0x7f0d0094

.field public static dialog_changename_input_edit:I = 0x7f0d00cc

.field public static dialog_download_confirm:I = 0x7f0d00d2

.field public static dialog_download_intercept:I = 0x7f0d00d3

.field public static dialog_download_more:I = 0x7f0d00d5

.field public static dialog_download_permission_tips:I = 0x7f0d00d6

.field public static dialog_download_res_main:I = 0x7f0d00d7

.field public static dialog_download_res_path_save_allow_access:I = 0x7f0d00d8

.field public static dialog_download_save_loading:I = 0x7f0d00d9

.field public static dialog_download_task_control_manager_layout:I = 0x7f0d00da

.field public static dialog_local_file_error:I = 0x7f0d00f1

.field public static dialog_request_authorization:I = 0x7f0d00fd

.field public static dialog_transfer_tips_layout:I = 0x7f0d010d

.field public static download_clear_history_dialog_layout:I = 0x7f0d0111

.field public static download_empty_default:I = 0x7f0d0112

.field public static download_view_type_list:I = 0x7f0d0114

.field public static downloading_tips_dialog_fragment_layout:I = 0x7f0d0115

.field public static fragment_all_historical_play_record:I = 0x7f0d012f

.field public static fragment_app_download_ad_layout:I = 0x7f0d0130

.field public static fragment_download_audio_track:I = 0x7f0d0138

.field public static fragment_download_list:I = 0x7f0d0139

.field public static fragment_download_main:I = 0x7f0d013a

.field public static fragment_download_panel:I = 0x7f0d013b

.field public static fragment_download_res_ana:I = 0x7f0d013c

.field public static fragment_download_res_group:I = 0x7f0d013d

.field public static fragment_download_res_group_main:I = 0x7f0d013e

.field public static fragment_download_res_multi:I = 0x7f0d013f

.field public static fragment_download_res_path_save:I = 0x7f0d0140

.field public static fragment_download_res_path_setting:I = 0x7f0d0141

.field public static fragment_download_res_short_tv:I = 0x7f0d0142

.field public static fragment_download_res_short_tv_ad:I = 0x7f0d0143

.field public static fragment_download_res_single_res:I = 0x7f0d0144

.field public static fragment_file_manager:I = 0x7f0d0148

.field public static fragment_history_play_record_layout:I = 0x7f0d0150

.field public static fragment_no_permission_layout:I = 0x7f0d0162

.field public static fragment_seasons:I = 0x7f0d0187

.field public static fragment_top_card_ad_layout:I = 0x7f0d019c

.field public static fragment_transfer_main:I = 0x7f0d019e

.field public static fragment_transfer_select_list:I = 0x7f0d01a0

.field public static item_app_download_ad_hi_layout:I = 0x7f0d01c2

.field public static item_app_download_ad_ps_layout:I = 0x7f0d01c3

.field public static item_bottom_dialog_download_ana_group:I = 0x7f0d01c6

.field public static item_download_re_audio:I = 0x7f0d01d0

.field public static item_download_re_resolution:I = 0x7f0d01d1

.field public static item_download_res_path_error_tips:I = 0x7f0d01d2

.field public static item_download_res_path_phone_storage_album:I = 0x7f0d01d3

.field public static item_download_res_path_phone_storage_album_save:I = 0x7f0d01d4

.field public static item_download_res_path_phone_storage_mb:I = 0x7f0d01d5

.field public static item_download_res_path_sdcard:I = 0x7f0d01d6

.field public static item_download_sel_dialog:I = 0x7f0d01d7

.field public static item_download_short_tv_tag:I = 0x7f0d01d8

.field public static item_historical_section_ad_layout:I = 0x7f0d01df

.field public static item_historical_section_body_layout:I = 0x7f0d01e0

.field public static item_historical_section_header_layout:I = 0x7f0d01e1

.field public static item_movie_rec:I = 0x7f0d01f0

.field public static item_movie_rec_footview:I = 0x7f0d01f1

.field public static item_transfer_select_header:I = 0x7f0d0253

.field public static item_transfer_select_item:I = 0x7f0d0254

.field public static layout_download_dialog_transfer_guide:I = 0x7f0d0265

.field public static layout_download_empty_default:I = 0x7f0d0266

.field public static layout_download_for_you_footview:I = 0x7f0d0267

.field public static layout_download_item_cover:I = 0x7f0d0268

.field public static layout_download_load_end_view:I = 0x7f0d0269

.field public static layout_download_premium:I = 0x7f0d026a

.field public static layout_download_re_path_entrance:I = 0x7f0d026b

.field public static layout_download_re_resolution_tab:I = 0x7f0d026c

.field public static layout_download_res_path_setting_content:I = 0x7f0d026d

.field public static layout_download_short_tv_loading:I = 0x7f0d026e

.field public static layout_download_transfer_later_tips:I = 0x7f0d026f

.field public static layout_downloading_play_guide:I = 0x7f0d0270

.field public static layout_downloading_title:I = 0x7f0d0271

.field public static layout_transfer_download_empty:I = 0x7f0d02bc

.field public static layout_transfer_tips:I = 0x7f0d02bd

.field public static notification_download_complete:I = 0x7f0d0394

.field public static notification_download_complete_v12:I = 0x7f0d0395

.field public static notification_downloading:I = 0x7f0d0396

.field public static notification_downloading_v12:I = 0x7f0d0397

.field public static notification_other_status:I = 0x7f0d039a

.field public static notification_other_status_v12:I = 0x7f0d039b

.field public static view_download_info_extend_layout:I = 0x7f0d0431

.field public static view_download_res_load_more:I = 0x7f0d0432

.field public static view_download_status_icon:I = 0x7f0d0433

.field public static view_download_status_icon_lav:I = 0x7f0d0434

.field public static view_download_top_tab:I = 0x7f0d0435

.field public static view_stub_local_file_delete_layout:I = 0x7f0d0456

.field public static view_transfer_bottom_tools:I = 0x7f0d0460

.field public static view_transfer_series_list:I = 0x7f0d0461


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
