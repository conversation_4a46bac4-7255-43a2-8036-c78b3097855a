.class public final Lcom/transsnet/downloader/R$mipmap;
.super Ljava/lang/Object;


# static fields
.field public static ic_ad:I = 0x7f0f0046

.field public static ic_arrow_down:I = 0x7f0f004c

.field public static ic_arrow_top_green:I = 0x7f0f004f

.field public static ic_audio_download_historical:I = 0x7f0f0055

.field public static ic_audiol_switch:I = 0x7f0f005b

.field public static ic_delete_local_file:I = 0x7f0f006c

.field public static ic_download_allow_access:I = 0x7f0f006e

.field public static ic_download_dialog_close:I = 0x7f0f0070

.field public static ic_download_downloading:I = 0x7f0f0071

.field public static ic_download_empty_transfer:I = 0x7f0f0072

.field public static ic_download_error_tips:I = 0x7f0f0073

.field public static ic_download_file:I = 0x7f0f0074

.field public static ic_download_item_pause:I = 0x7f0f0075

.field public static ic_download_path:I = 0x7f0f0077

.field public static ic_download_pause:I = 0x7f0f0078

.field public static ic_download_play:I = 0x7f0f0079

.field public static ic_download_save_file_icon:I = 0x7f0f007b

.field public static ic_download_selected_check:I = 0x7f0f007c

.field public static ic_download_series:I = 0x7f0f007d

.field public static ic_download_short_tv_ad:I = 0x7f0f007e

.field public static ic_download_short_tv_favorite:I = 0x7f0f007f

.field public static ic_download_short_tv_favorite_select:I = 0x7f0f0080

.field public static ic_download_short_tv_share:I = 0x7f0f0081

.field public static ic_download_status_fail:I = 0x7f0f0082

.field public static ic_download_status_fail_dark:I = 0x7f0f0083

.field public static ic_download_status_fail_home:I = 0x7f0f0084

.field public static ic_download_white:I = 0x7f0f0086

.field public static ic_guide_close:I = 0x7f0f0093

.field public static ic_newcomer_guide_download_tips:I = 0x7f0f00b3

.field public static ic_play_dark:I = 0x7f0f00d1

.field public static ic_short_tv_download_sub:I = 0x7f0f0104

.field public static ic_short_tv_lock:I = 0x7f0f0108

.field public static ic_transfer_btn_receive:I = 0x7f0f0130

.field public static ic_transfer_btn_send:I = 0x7f0f0131

.field public static ic_transfer_enter:I = 0x7f0f0132

.field public static ic_transfer_send_ing:I = 0x7f0f0133

.field public static ic_transfer_send_ing_dark:I = 0x7f0f0134

.field public static ic_transfer_send_ing_error:I = 0x7f0f0135

.field public static ic_transfer_tips_dialog_close:I = 0x7f0f0136

.field public static icon_download_light_close:I = 0x7f0f0147

.field public static image_transfer_tips_step1:I = 0x7f0f0151

.field public static image_transfer_tips_step2:I = 0x7f0f0152

.field public static image_transfer_tips_step3:I = 0x7f0f0153

.field public static img_download_transfer_no_permission:I = 0x7f0f0154

.field public static img_newcomer_guide_download_0:I = 0x7f0f0156

.field public static img_newcomer_guide_download_1:I = 0x7f0f0157

.field public static img_newcomer_guide_download_2:I = 0x7f0f0158

.field public static img_newcomer_guide_download_3:I = 0x7f0f0159

.field public static img_newcomer_guide_download_4:I = 0x7f0f015a

.field public static left_top_shadow:I = 0x7f0f0165

.field public static movie_source_info:I = 0x7f0f01c2


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
