.class public final Lcom/transsnet/downloader/R$string;
.super Ljava/lang/Object;


# static fields
.field public static already_in_downloads:I = 0x7f120042

.field public static available:I = 0x7f120059

.field public static cancel:I = 0x7f12008c

.field public static change:I = 0x7f12008d

.field public static clear:I = 0x7f120099

.field public static clear_all_history:I = 0x7f12009a

.field public static clear_all_history_tips:I = 0x7f12009b

.field public static clear_history_tips:I = 0x7f12009c

.field public static clear_tips:I = 0x7f12009f

.field public static comment_max_tips:I = 0x7f1200c0

.field public static confirm:I = 0x7f1200df

.field public static confirm_delete:I = 0x7f1200e0

.field public static continue_watch:I = 0x7f1200e2

.field public static delete:I = 0x7f120124

.field public static download_again:I = 0x7f12012c

.field public static download_allow_access_to_move_tips:I = 0x7f12012d

.field public static download_allow_access_to_save_tips:I = 0x7f12012e

.field public static download_analyzing:I = 0x7f12012f

.field public static download_apps_to_be_intalled:I = 0x7f120130

.field public static download_apps_to_be_intalled_txt:I = 0x7f120131

.field public static download_apps_to_be_open_txt:I = 0x7f120132

.field public static download_audio_original_tag:I = 0x7f120133

.field public static download_audio_title:I = 0x7f120134

.field public static download_authorization:I = 0x7f120135

.field public static download_available:I = 0x7f120136

.field public static download_btn_refresh:I = 0x7f120137

.field public static download_cache:I = 0x7f12013a

.field public static download_cancel:I = 0x7f12013b

.field public static download_delete_success:I = 0x7f12013c

.field public static download_delete_tips:I = 0x7f12013d

.field public static download_details:I = 0x7f12013e

.field public static download_dialog_audio_playlist:I = 0x7f12013f

.field public static download_dialog_save:I = 0x7f120140

.field public static download_downloaded_to_do:I = 0x7f120141

.field public static download_empty_find_free_source:I = 0x7f120142

.field public static download_empty_transfer_tips:I = 0x7f120143

.field public static download_enables_now:I = 0x7f120144

.field public static download_ep_selected_count:I = 0x7f120145

.field public static download_episodes:I = 0x7f120146

.field public static download_file_not_exist_tips:I = 0x7f120148

.field public static download_file_permission_tips:I = 0x7f120149

.field public static download_files:I = 0x7f12014a

.field public static download_finished:I = 0x7f12014b

.field public static download_for_free_tip:I = 0x7f12014c

.field public static download_in_progress:I = 0x7f12014f

.field public static download_listened:I = 0x7f120150

.field public static download_more:I = 0x7f120151

.field public static download_move_failed:I = 0x7f120152

.field public static download_move_successful:I = 0x7f120153

.field public static download_move_to:I = 0x7f120154

.field public static download_moving:I = 0x7f120156

.field public static download_no_files_yet:I = 0x7f120157

.field public static download_no_historical_tips:I = 0x7f120158

.field public static download_no_local_file_tips:I = 0x7f120159

.field public static download_no_local_file_tips_2:I = 0x7f12015a

.field public static download_no_options_toast:I = 0x7f12015b

.field public static download_no_permission_btn:I = 0x7f12015c

.field public static download_no_permission_new_tips:I = 0x7f12015d

.field public static download_no_permission_tips:I = 0x7f12015e

.field public static download_not_net:I = 0x7f12015f

.field public static download_not_open:I = 0x7f120160

.field public static download_notifications_name:I = 0x7f120161

.field public static download_open_file_no_permission:I = 0x7f120163

.field public static download_path_guide_tips:I = 0x7f120164

.field public static download_path_guide_title:I = 0x7f120165

.field public static download_path_title_prefix:I = 0x7f120166

.field public static download_pause_all:I = 0x7f120167

.field public static download_permission_android_10_tips:I = 0x7f120168

.field public static download_permission_btn:I = 0x7f120169

.field public static download_permission_denied:I = 0x7f12016a

.field public static download_permission_denied_tips:I = 0x7f12016b

.field public static download_permission_tips:I = 0x7f12016c

.field public static download_permission_title:I = 0x7f12016d

.field public static download_play_guide:I = 0x7f12016e

.field public static download_play_now:I = 0x7f12016f

.field public static download_premium:I = 0x7f120174

.field public static download_quality_per_episode_size:I = 0x7f120175

.field public static download_quality_title:I = 0x7f120176

.field public static download_redownload_tips:I = 0x7f120177

.field public static download_resume_all:I = 0x7f120178

.field public static download_save:I = 0x7f120179

.field public static download_save_failed:I = 0x7f12017a

.field public static download_save_ing:I = 0x7f12017b

.field public static download_save_subtitle_tips:I = 0x7f12017c

.field public static download_save_successful:I = 0x7f12017d

.field public static download_save_to:I = 0x7f12017e

.field public static download_save_to_dot:I = 0x7f12017f

.field public static download_saving:I = 0x7f120180

.field public static download_saving_to_album:I = 0x7f120181

.field public static download_select_all:I = 0x7f120182

.field public static download_series_all_chapters:I = 0x7f120183

.field public static download_series_all_episodes:I = 0x7f120184

.field public static download_series_all_lessons:I = 0x7f120185

.field public static download_short_tv_unlock_success_toast:I = 0x7f120186

.field public static download_short_tv_unselected_toast:I = 0x7f120187

.field public static download_short_tv_watch_ad_all:I = 0x7f120188

.field public static download_short_tv_watch_ad_ep:I = 0x7f120189

.field public static download_short_tv_watch_ad_tips:I = 0x7f12018a

.field public static download_status_failed:I = 0x7f12018b

.field public static download_status_no_net:I = 0x7f12018c

.field public static download_status_paused:I = 0x7f12018d

.field public static download_status_waiting:I = 0x7f12018e

.field public static download_success_notifications_name:I = 0x7f120191

.field public static download_tab_name_all:I = 0x7f120192

.field public static download_tab_name_file_manager:I = 0x7f120193

.field public static download_tab_name_transfer:I = 0x7f120194

.field public static download_tab_transfer_tips:I = 0x7f120195

.field public static download_tab_transfer_tips_desc_1:I = 0x7f120196

.field public static download_tab_transfer_tips_desc_2:I = 0x7f120197

.field public static download_tab_transfer_tips_desc_3:I = 0x7f120198

.field public static download_tab_transfer_tips_get:I = 0x7f120199

.field public static download_tab_transfer_tips_next:I = 0x7f12019a

.field public static download_task_control_manager_start:I = 0x7f12019b

.field public static download_task_control_manager_start_mul:I = 0x7f12019c

.field public static download_tips0:I = 0x7f12019d

.field public static download_tips1:I = 0x7f12019e

.field public static download_tips2:I = 0x7f12019f

.field public static download_tips_desc0:I = 0x7f1201a0

.field public static download_tips_desc1:I = 0x7f1201a1

.field public static download_tips_desc2:I = 0x7f1201a2

.field public static download_tips_desc3:I = 0x7f1201a3

.field public static download_title_for_you:I = 0x7f1201a4

.field public static download_title_manager:I = 0x7f1201a5

.field public static download_today:I = 0x7f1201a6

.field public static download_transfer_btn_close:I = 0x7f1201a7

.field public static download_transfer_btn_disconnect:I = 0x7f1201a8

.field public static download_transfer_btn_list:I = 0x7f1201a9

.field public static download_transfer_btn_receive:I = 0x7f1201aa

.field public static download_transfer_btn_reset:I = 0x7f1201ab

.field public static download_transfer_btn_send:I = 0x7f1201ac

.field public static download_transfer_connect_to:I = 0x7f1201ad

.field public static download_transfer_disconnect_tips:I = 0x7f1201ae

.field public static download_transfer_disconnect_title:I = 0x7f1201af

.field public static download_transfer_disconnect_to:I = 0x7f1201b0

.field public static download_transfer_later_tips:I = 0x7f1201b1

.field public static download_transfer_path_title:I = 0x7f1201b2

.field public static download_transfer_received_empty_tips:I = 0x7f1201b3

.field public static download_transfer_retry_btn:I = 0x7f1201b4

.field public static download_transfer_retry_tips:I = 0x7f1201b5

.field public static download_transfer_sending:I = 0x7f1201b6

.field public static download_transfer_tab_received:I = 0x7f1201b7

.field public static download_transfer_tips:I = 0x7f1201b8

.field public static download_transfer_title:I = 0x7f1201b9

.field public static download_unselected_tips:I = 0x7f1201ba

.field public static download_uploaded_by:I = 0x7f1201bb

.field public static download_video_detail_resources:I = 0x7f1201bc

.field public static download_video_detail_season_index:I = 0x7f1201bd

.field public static download_video_detail_seasons:I = 0x7f1201be

.field public static download_video_detail_select_resources:I = 0x7f1201bf

.field public static download_video_detail_source_info:I = 0x7f1201c0

.field public static download_video_detail_unit_index:I = 0x7f1201c1

.field public static download_video_detail_units:I = 0x7f1201c2

.field public static download_video_detail_upload_by:I = 0x7f1201c3

.field public static download_watch_history:I = 0x7f1201c4

.field public static download_watch_history_all:I = 0x7f1201c5

.field public static download_watch_history_cleared:I = 0x7f1201c6

.field public static download_watched:I = 0x7f1201c7

.field public static download_xx_size_at_once:I = 0x7f1201c8

.field public static download_yesterday:I = 0x7f1201c9

.field public static downloading_Local_files:I = 0x7f1201cb

.field public static downloading_play:I = 0x7f1201cc

.field public static downloading_time_left:I = 0x7f1201d1

.field public static downloading_title_count:I = 0x7f1201d2

.field public static file_manager:I = 0x7f12024b

.field public static help:I = 0x7f120289

.field public static intercept_dialog_tips:I = 0x7f1202b9

.field public static newcomer_guide_download_tips:I = 0x7f120459

.field public static newcomer_guide_download_tips_1:I = 0x7f12045a

.field public static newcomer_guide_download_tips_2:I = 0x7f12045b

.field public static newcomer_guide_download_tips_3:I = 0x7f12045c

.field public static newcomer_guide_download_tips_title:I = 0x7f12045d

.field public static notification_continue_watching:I = 0x7f12047d

.field public static notification_download_complete:I = 0x7f12047e

.field public static notification_download_continue_tip:I = 0x7f12047f

.field public static notification_download_failed:I = 0x7f120480

.field public static notification_download_paused:I = 0x7f120481

.field public static notification_download_protection:I = 0x7f120482

.field public static notification_download_success:I = 0x7f120483

.field public static notification_download_waiting:I = 0x7f120484

.field public static notification_download_watch_tip:I = 0x7f120485

.field public static permission_es_allow:I = 0x7f1204b4

.field public static permission_es_deny_des:I = 0x7f1204b5

.field public static permission_es_des:I = 0x7f1204b6

.field public static permission_es_title:I = 0x7f1204b7

.field public static request_authorization_tips:I = 0x7f120571

.field public static sdcard_name:I = 0x7f12058a

.field public static select_and_download:I = 0x7f1205b1

.field public static set_as_default:I = 0x7f1205b9

.field public static short_tv_favorite_remove_toast:I = 0x7f1205d1

.field public static short_tv_favorite_toast:I = 0x7f1205d2

.field public static str_ad_video_error_tips:I = 0x7f120608

.field public static str_add_a_name:I = 0x7f120609

.field public static str_count:I = 0x7f120615

.field public static str_download:I = 0x7f120616

.field public static str_download_dialog_path_albums:I = 0x7f120617

.field public static str_download_dialog_path_moviebox_folder:I = 0x7f120618

.field public static str_download_dialog_path_phone_storage:I = 0x7f120619

.field public static str_download_dialog_path_title:I = 0x7f12061a

.field public static str_download_dialog_title:I = 0x7f12061b

.field public static str_downloaded_title:I = 0x7f12061c

.field public static str_downloading_file_size:I = 0x7f12061d

.field public static str_downloading_tips:I = 0x7f12061e

.field public static str_downloading_tips_new:I = 0x7f12061f

.field public static str_downloading_title:I = 0x7f120620

.field public static str_downloads:I = 0x7f120621

.field public static str_empty_name_tips:I = 0x7f120623

.field public static str_hide:I = 0x7f120624

.field public static str_info:I = 0x7f120627

.field public static str_invite_whatsapp_friends:I = 0x7f120628

.field public static str_local_files_title:I = 0x7f12062a

.field public static str_more:I = 0x7f12062b

.field public static str_waiting:I = 0x7f120636

.field public static str_watch_a_video:I = 0x7f120637

.field public static tips_01:I = 0x7f1206aa

.field public static tips_02:I = 0x7f1206ab

.field public static tips_03:I = 0x7f1206ac

.field public static unlock:I = 0x7f12078f

.field public static unlock_in_order:I = 0x7f120790

.field public static view_downloads:I = 0x7f1207d4

.field public static watch_now:I = 0x7f1207d9


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
