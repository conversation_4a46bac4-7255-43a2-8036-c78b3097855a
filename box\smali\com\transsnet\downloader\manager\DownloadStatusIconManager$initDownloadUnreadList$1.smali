.class final Lcom/transsnet/downloader/manager/DownloadStatusIconManager$initDownloadUnreadList$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/transsnet/downloader/manager/DownloadStatusIconManager;->o()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/k0;",
        "Lkotlin/coroutines/Continuation<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation

.annotation runtime Lkotlin/coroutines/jvm/internal/DebugMetadata;
    c = "com.transsnet.downloader.manager.DownloadStatusIconManager$initDownloadUnreadList$1"
    f = "DownloadStatusIconManager.kt"
    l = {}
    m = "invokeSuspend"
.end annotation


# instance fields
.field label:I

.field final synthetic this$0:Lcom/transsnet/downloader/manager/DownloadStatusIconManager;


# direct methods
.method public constructor <init>(Lcom/transsnet/downloader/manager/DownloadStatusIconManager;Lkotlin/coroutines/Continuation;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/transsnet/downloader/manager/DownloadStatusIconManager;",
            "Lkotlin/coroutines/Continuation<",
            "-",
            "Lcom/transsnet/downloader/manager/DownloadStatusIconManager$initDownloadUnreadList$1;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lcom/transsnet/downloader/manager/DownloadStatusIconManager$initDownloadUnreadList$1;->this$0:Lcom/transsnet/downloader/manager/DownloadStatusIconManager;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/Continuation;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/Continuation;)Lkotlin/coroutines/Continuation;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/Continuation<",
            "*>;)",
            "Lkotlin/coroutines/Continuation<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance p1, Lcom/transsnet/downloader/manager/DownloadStatusIconManager$initDownloadUnreadList$1;

    iget-object v0, p0, Lcom/transsnet/downloader/manager/DownloadStatusIconManager$initDownloadUnreadList$1;->this$0:Lcom/transsnet/downloader/manager/DownloadStatusIconManager;

    invoke-direct {p1, v0, p2}, Lcom/transsnet/downloader/manager/DownloadStatusIconManager$initDownloadUnreadList$1;-><init>(Lcom/transsnet/downloader/manager/DownloadStatusIconManager;Lkotlin/coroutines/Continuation;)V

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    check-cast p1, Lkotlinx/coroutines/k0;

    check-cast p2, Lkotlin/coroutines/Continuation;

    invoke-virtual {p0, p1, p2}, Lcom/transsnet/downloader/manager/DownloadStatusIconManager$initDownloadUnreadList$1;->invoke(Lkotlinx/coroutines/k0;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/k0;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/k0;",
            "Lkotlin/coroutines/Continuation<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    invoke-virtual {p0, p1, p2}, Lcom/transsnet/downloader/manager/DownloadStatusIconManager$initDownloadUnreadList$1;->create(Ljava/lang/Object;Lkotlin/coroutines/Continuation;)Lkotlin/coroutines/Continuation;

    move-result-object p1

    check-cast p1, Lcom/transsnet/downloader/manager/DownloadStatusIconManager$initDownloadUnreadList$1;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lcom/transsnet/downloader/manager/DownloadStatusIconManager$initDownloadUnreadList$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 4

    invoke-static {}, Lkotlin/coroutines/intrinsics/IntrinsicsKt;->e()Ljava/lang/Object;

    iget v0, p0, Lcom/transsnet/downloader/manager/DownloadStatusIconManager$initDownloadUnreadList$1;->label:I

    if-nez v0, :cond_2

    invoke-static {p1}, Lkotlin/ResultKt;->b(Ljava/lang/Object;)V

    iget-object p1, p0, Lcom/transsnet/downloader/manager/DownloadStatusIconManager$initDownloadUnreadList$1;->this$0:Lcom/transsnet/downloader/manager/DownloadStatusIconManager;

    invoke-static {p1}, Lcom/transsnet/downloader/manager/DownloadStatusIconManager;->c(Lcom/transsnet/downloader/manager/DownloadStatusIconManager;)I

    move-result p1

    if-gtz p1, :cond_1

    iget-object p1, p0, Lcom/transsnet/downloader/manager/DownloadStatusIconManager$initDownloadUnreadList$1;->this$0:Lcom/transsnet/downloader/manager/DownloadStatusIconManager;

    invoke-static {p1}, Lcom/transsnet/downloader/manager/DownloadStatusIconManager;->e(Lcom/transsnet/downloader/manager/DownloadStatusIconManager;)I

    move-result p1

    if-gtz p1, :cond_1

    iget-object p1, p0, Lcom/transsnet/downloader/manager/DownloadStatusIconManager$initDownloadUnreadList$1;->this$0:Lcom/transsnet/downloader/manager/DownloadStatusIconManager;

    invoke-static {p1}, Lcom/transsnet/downloader/manager/DownloadStatusIconManager;->f(Lcom/transsnet/downloader/manager/DownloadStatusIconManager;)I

    move-result p1

    if-lez p1, :cond_0

    goto :goto_0

    :cond_0
    iget-object p1, p0, Lcom/transsnet/downloader/manager/DownloadStatusIconManager$initDownloadUnreadList$1;->this$0:Lcom/transsnet/downloader/manager/DownloadStatusIconManager;

    invoke-static {p1}, Lcom/transsnet/downloader/manager/DownloadStatusIconManager;->g(Lcom/transsnet/downloader/manager/DownloadStatusIconManager;)Z

    move-result v0

    xor-int/lit8 v0, v0, 0x1

    const/4 v1, 0x2

    const/4 v2, 0x0

    const/4 v3, 0x0

    invoke-static {p1, v0, v3, v1, v2}, Lcom/transsnet/downloader/manager/DownloadStatusIconManager;->q(Lcom/transsnet/downloader/manager/DownloadStatusIconManager;IIILjava/lang/Object;)V

    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    return-object p1

    :cond_1
    :goto_0
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    return-object p1

    :cond_2
    new-instance p1, Ljava/lang/IllegalStateException;

    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw p1
.end method
