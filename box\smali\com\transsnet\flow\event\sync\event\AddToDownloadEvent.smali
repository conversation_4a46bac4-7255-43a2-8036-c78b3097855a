.class public final Lcom/transsnet/flow/event/sync/event/AddToDownloadEvent;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/io/Serializable;


# annotations
.annotation runtime L<PERSON><PERSON>/Metadata;
.end annotation


# instance fields
.field private final add:Z

.field private final isSeries:Z

.field private final resourceId:Ljava/lang/String;

.field private final subjectId:Ljava/lang/String;


# direct methods
.method public constructor <init>(Ljava/lang/String;Ljava/lang/String;ZZ)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/transsnet/flow/event/sync/event/AddToDownloadEvent;->subjectId:Ljava/lang/String;

    iput-object p2, p0, Lcom/transsnet/flow/event/sync/event/AddToDownloadEvent;->resourceId:Ljava/lang/String;

    iput-boolean p3, p0, Lcom/transsnet/flow/event/sync/event/AddToDownloadEvent;->add:Z

    iput-boolean p4, p0, Lcom/transsnet/flow/event/sync/event/AddToDownloadEvent;->isSeries:Z

    return-void
.end method

.method public synthetic constructor <init>(Ljava/lang/String;Ljava/lang/String;ZZILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 1

    and-int/lit8 p6, p5, 0x4

    const/4 v0, 0x0

    if-eqz p6, :cond_0

    const/4 p3, 0x0

    :cond_0
    and-int/lit8 p5, p5, 0x8

    if-eqz p5, :cond_1

    const/4 p4, 0x0

    :cond_1
    invoke-direct {p0, p1, p2, p3, p4}, Lcom/transsnet/flow/event/sync/event/AddToDownloadEvent;-><init>(Ljava/lang/String;Ljava/lang/String;ZZ)V

    return-void
.end method

.method public static synthetic copy$default(Lcom/transsnet/flow/event/sync/event/AddToDownloadEvent;Ljava/lang/String;Ljava/lang/String;ZZILjava/lang/Object;)Lcom/transsnet/flow/event/sync/event/AddToDownloadEvent;
    .locals 0

    and-int/lit8 p6, p5, 0x1

    if-eqz p6, :cond_0

    iget-object p1, p0, Lcom/transsnet/flow/event/sync/event/AddToDownloadEvent;->subjectId:Ljava/lang/String;

    :cond_0
    and-int/lit8 p6, p5, 0x2

    if-eqz p6, :cond_1

    iget-object p2, p0, Lcom/transsnet/flow/event/sync/event/AddToDownloadEvent;->resourceId:Ljava/lang/String;

    :cond_1
    and-int/lit8 p6, p5, 0x4

    if-eqz p6, :cond_2

    iget-boolean p3, p0, Lcom/transsnet/flow/event/sync/event/AddToDownloadEvent;->add:Z

    :cond_2
    and-int/lit8 p5, p5, 0x8

    if-eqz p5, :cond_3

    iget-boolean p4, p0, Lcom/transsnet/flow/event/sync/event/AddToDownloadEvent;->isSeries:Z

    :cond_3
    invoke-virtual {p0, p1, p2, p3, p4}, Lcom/transsnet/flow/event/sync/event/AddToDownloadEvent;->copy(Ljava/lang/String;Ljava/lang/String;ZZ)Lcom/transsnet/flow/event/sync/event/AddToDownloadEvent;

    move-result-object p0

    return-object p0
.end method


# virtual methods
.method public final component1()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/transsnet/flow/event/sync/event/AddToDownloadEvent;->subjectId:Ljava/lang/String;

    return-object v0
.end method

.method public final component2()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/transsnet/flow/event/sync/event/AddToDownloadEvent;->resourceId:Ljava/lang/String;

    return-object v0
.end method

.method public final component3()Z
    .locals 1

    iget-boolean v0, p0, Lcom/transsnet/flow/event/sync/event/AddToDownloadEvent;->add:Z

    return v0
.end method

.method public final component4()Z
    .locals 1

    iget-boolean v0, p0, Lcom/transsnet/flow/event/sync/event/AddToDownloadEvent;->isSeries:Z

    return v0
.end method

.method public final copy(Ljava/lang/String;Ljava/lang/String;ZZ)Lcom/transsnet/flow/event/sync/event/AddToDownloadEvent;
    .locals 1

    new-instance v0, Lcom/transsnet/flow/event/sync/event/AddToDownloadEvent;

    invoke-direct {v0, p1, p2, p3, p4}, Lcom/transsnet/flow/event/sync/event/AddToDownloadEvent;-><init>(Ljava/lang/String;Ljava/lang/String;ZZ)V

    return-object v0
.end method

.method public equals(Ljava/lang/Object;)Z
    .locals 4

    const/4 v0, 0x1

    if-ne p0, p1, :cond_0

    return v0

    :cond_0
    instance-of v1, p1, Lcom/transsnet/flow/event/sync/event/AddToDownloadEvent;

    const/4 v2, 0x0

    if-nez v1, :cond_1

    return v2

    :cond_1
    check-cast p1, Lcom/transsnet/flow/event/sync/event/AddToDownloadEvent;

    iget-object v1, p0, Lcom/transsnet/flow/event/sync/event/AddToDownloadEvent;->subjectId:Ljava/lang/String;

    iget-object v3, p1, Lcom/transsnet/flow/event/sync/event/AddToDownloadEvent;->subjectId:Ljava/lang/String;

    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->b(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_2

    return v2

    :cond_2
    iget-object v1, p0, Lcom/transsnet/flow/event/sync/event/AddToDownloadEvent;->resourceId:Ljava/lang/String;

    iget-object v3, p1, Lcom/transsnet/flow/event/sync/event/AddToDownloadEvent;->resourceId:Ljava/lang/String;

    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->b(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_3

    return v2

    :cond_3
    iget-boolean v1, p0, Lcom/transsnet/flow/event/sync/event/AddToDownloadEvent;->add:Z

    iget-boolean v3, p1, Lcom/transsnet/flow/event/sync/event/AddToDownloadEvent;->add:Z

    if-eq v1, v3, :cond_4

    return v2

    :cond_4
    iget-boolean v1, p0, Lcom/transsnet/flow/event/sync/event/AddToDownloadEvent;->isSeries:Z

    iget-boolean p1, p1, Lcom/transsnet/flow/event/sync/event/AddToDownloadEvent;->isSeries:Z

    if-eq v1, p1, :cond_5

    return v2

    :cond_5
    return v0
.end method

.method public final getAdd()Z
    .locals 1

    iget-boolean v0, p0, Lcom/transsnet/flow/event/sync/event/AddToDownloadEvent;->add:Z

    return v0
.end method

.method public final getResourceId()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/transsnet/flow/event/sync/event/AddToDownloadEvent;->resourceId:Ljava/lang/String;

    return-object v0
.end method

.method public final getSubjectId()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/transsnet/flow/event/sync/event/AddToDownloadEvent;->subjectId:Ljava/lang/String;

    return-object v0
.end method

.method public hashCode()I
    .locals 3

    iget-object v0, p0, Lcom/transsnet/flow/event/sync/event/AddToDownloadEvent;->subjectId:Ljava/lang/String;

    const/4 v1, 0x0

    if-nez v0, :cond_0

    const/4 v0, 0x0

    goto :goto_0

    :cond_0
    invoke-virtual {v0}, Ljava/lang/String;->hashCode()I

    move-result v0

    :goto_0
    mul-int/lit8 v0, v0, 0x1f

    iget-object v2, p0, Lcom/transsnet/flow/event/sync/event/AddToDownloadEvent;->resourceId:Ljava/lang/String;

    if-nez v2, :cond_1

    goto :goto_1

    :cond_1
    invoke-virtual {v2}, Ljava/lang/String;->hashCode()I

    move-result v1

    :goto_1
    add-int/2addr v0, v1

    mul-int/lit8 v0, v0, 0x1f

    iget-boolean v1, p0, Lcom/transsnet/flow/event/sync/event/AddToDownloadEvent;->add:Z

    const/4 v2, 0x1

    if-eqz v1, :cond_2

    const/4 v1, 0x1

    :cond_2
    add-int/2addr v0, v1

    mul-int/lit8 v0, v0, 0x1f

    iget-boolean v1, p0, Lcom/transsnet/flow/event/sync/event/AddToDownloadEvent;->isSeries:Z

    if-eqz v1, :cond_3

    goto :goto_2

    :cond_3
    move v2, v1

    :goto_2
    add-int/2addr v0, v2

    return v0
.end method

.method public final isSeries()Z
    .locals 1

    iget-boolean v0, p0, Lcom/transsnet/flow/event/sync/event/AddToDownloadEvent;->isSeries:Z

    return v0
.end method

.method public toString()Ljava/lang/String;
    .locals 6

    iget-object v0, p0, Lcom/transsnet/flow/event/sync/event/AddToDownloadEvent;->subjectId:Ljava/lang/String;

    iget-object v1, p0, Lcom/transsnet/flow/event/sync/event/AddToDownloadEvent;->resourceId:Ljava/lang/String;

    iget-boolean v2, p0, Lcom/transsnet/flow/event/sync/event/AddToDownloadEvent;->add:Z

    iget-boolean v3, p0, Lcom/transsnet/flow/event/sync/event/AddToDownloadEvent;->isSeries:Z

    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    const-string v5, "AddToDownloadEvent(subjectId="

    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v0, ", resourceId="

    invoke-virtual {v4, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v0, ", add="

    invoke-virtual {v4, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4, v2}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    const-string v0, ", isSeries="

    invoke-virtual {v4, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4, v3}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    const-string v0, ")"

    invoke-virtual {v4, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
