.class public final Lcom/transsnet/login/LoginProvider;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/transsnet/loginapi/ILoginApi;


# annotations
.annotation build Lcom/alibaba/android/arouter/facade/annotation/Route;
    path = "/Login/Api"
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/transsnet/login/LoginProvider$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation

.annotation build Lkotlin/jvm/internal/SourceDebugExtension;
.end annotation


# static fields
.field public static final f:Lcom/transsnet/login/LoginProvider$a;

.field public static final g:Lkotlin/Lazy;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlin/Lazy<",
            "Lcom/transsnet/loginapi/ILoginApi;",
            ">;"
        }
    .end annotation
.end field


# instance fields
.field public a:Landroid/content/Context;

.field public volatile b:Lcom/transsnet/loginapi/bean/UserInfo;

.field public c:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Ljava/lang/ref/WeakReference<",
            "Leu/a;",
            ">;>;"
        }
    .end annotation
.end field

.field public final d:Lkotlin/Lazy;

.field public final e:Ljava/lang/String;


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lcom/transsnet/login/LoginProvider$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lcom/transsnet/login/LoginProvider$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lcom/transsnet/login/LoginProvider;->f:Lcom/transsnet/login/LoginProvider$a;

    sget-object v0, Lcom/transsnet/login/LoginProvider$Companion$instance$2;->INSTANCE:Lcom/transsnet/login/LoginProvider$Companion$instance$2;

    invoke-static {v0}, Lkotlin/LazyKt;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/Lazy;

    move-result-object v0

    sput-object v0, Lcom/transsnet/login/LoginProvider;->g:Lkotlin/Lazy;

    return-void
.end method

.method public constructor <init>()V
    .locals 1

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    new-instance v0, Ljava/util/concurrent/CopyOnWriteArrayList;

    invoke-direct {v0}, Ljava/util/concurrent/CopyOnWriteArrayList;-><init>()V

    iput-object v0, p0, Lcom/transsnet/login/LoginProvider;->c:Ljava/util/List;

    sget-object v0, Lcom/transsnet/login/LoginProvider$iVodApi$2;->INSTANCE:Lcom/transsnet/login/LoginProvider$iVodApi$2;

    invoke-static {v0}, Lkotlin/LazyKt;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/Lazy;

    move-result-object v0

    iput-object v0, p0, Lcom/transsnet/login/LoginProvider;->d:Lkotlin/Lazy;

    const-string v0, "X-User"

    iput-object v0, p0, Lcom/transsnet/login/LoginProvider;->e:Ljava/lang/String;

    return-void
.end method

.method public static synthetic D1(Lcom/transsnet/login/LoginProvider;Lcom/transsnet/loginapi/bean/UserInfo;)V
    .locals 0

    invoke-static {p0, p1}, Lcom/transsnet/login/LoginProvider;->T1(Lcom/transsnet/login/LoginProvider;Lcom/transsnet/loginapi/bean/UserInfo;)V

    return-void
.end method

.method public static synthetic E1(Lcom/transsnet/login/LoginProvider;Lcom/transsnet/loginapi/bean/UserInfo;)V
    .locals 0

    invoke-static {p0, p1}, Lcom/transsnet/login/LoginProvider;->W1(Lcom/transsnet/login/LoginProvider;Lcom/transsnet/loginapi/bean/UserInfo;)V

    return-void
.end method

.method public static synthetic F1(Lcom/transsnet/login/LoginProvider;)V
    .locals 0

    invoke-static {p0}, Lcom/transsnet/login/LoginProvider;->Q1(Lcom/transsnet/login/LoginProvider;)V

    return-void
.end method

.method public static final synthetic G1(Lcom/transsnet/login/LoginProvider;)Lbu/a;
    .locals 0

    invoke-virtual {p0}, Lcom/transsnet/login/LoginProvider;->O1()Lbu/a;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic H1()Lkotlin/Lazy;
    .locals 1

    sget-object v0, Lcom/transsnet/login/LoginProvider;->g:Lkotlin/Lazy;

    return-object v0
.end method

.method public static final synthetic I1(Lcom/transsnet/login/LoginProvider;)Lcom/transsnet/loginapi/bean/UserInfo;
    .locals 0

    iget-object p0, p0, Lcom/transsnet/login/LoginProvider;->b:Lcom/transsnet/loginapi/bean/UserInfo;

    return-object p0
.end method

.method public static final synthetic J1(Lcom/transsnet/login/LoginProvider;)V
    .locals 0

    invoke-direct {p0}, Lcom/transsnet/login/LoginProvider;->R()V

    return-void
.end method

.method public static final synthetic K1(Lcom/transsnet/login/LoginProvider;Lcom/transsnet/loginapi/bean/UserInfo;)V
    .locals 0

    invoke-virtual {p0, p1}, Lcom/transsnet/login/LoginProvider;->S1(Lcom/transsnet/loginapi/bean/UserInfo;)V

    return-void
.end method

.method public static final synthetic L1(Lcom/transsnet/login/LoginProvider;Lcom/transsnet/loginapi/bean/UserInfo;)V
    .locals 0

    invoke-virtual {p0, p1}, Lcom/transsnet/login/LoginProvider;->V1(Lcom/transsnet/loginapi/bean/UserInfo;)V

    return-void
.end method

.method public static final Q1(Lcom/transsnet/login/LoginProvider;)V
    .locals 1

    const-string v0, "this$0"

    invoke-static {p0, v0}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    iget-object p0, p0, Lcom/transsnet/login/LoginProvider;->c:Ljava/util/List;

    check-cast p0, Ljava/lang/Iterable;

    invoke-interface {p0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    move-result-object p0

    :cond_0
    :goto_0
    invoke-interface {p0}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_1

    invoke-interface {p0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/lang/ref/WeakReference;

    invoke-virtual {v0}, Ljava/lang/ref/Reference;->get()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Leu/a;

    if-eqz v0, :cond_0

    invoke-interface {v0}, Leu/a;->onLogout()V

    goto :goto_0

    :cond_1
    return-void
.end method

.method private final R()V
    .locals 3

    const/4 v0, 0x0

    iput-object v0, p0, Lcom/transsnet/login/LoginProvider;->b:Lcom/transsnet/loginapi/bean/UserInfo;

    sget-object v0, Lcom/transsnet/login/constant/LoginMmkvUtil;->a:Lcom/transsnet/login/constant/LoginMmkvUtil;

    invoke-virtual {v0}, Lcom/transsnet/login/constant/LoginMmkvUtil;->a()Lcom/tencent/mmkv/MMKV;

    move-result-object v0

    const-string v1, "login_user"

    const-string v2, ""

    invoke-virtual {v0, v1, v2}, Lcom/tencent/mmkv/MMKV;->putString(Ljava/lang/String;Ljava/lang/String;)Landroid/content/SharedPreferences$Editor;

    sget-object v0, Llj/a;->a:Llj/a$a;

    invoke-virtual {v0, v2}, Llj/a$a;->h(Ljava/lang/String;)V

    new-instance v0, Lcom/transsnet/login/i;

    invoke-direct {v0, p0}, Lcom/transsnet/login/i;-><init>(Lcom/transsnet/login/LoginProvider;)V

    invoke-static {v0}, Lcom/blankj/utilcode/util/ThreadUtils;->j(Ljava/lang/Runnable;)V

    return-void
.end method

.method public static final T1(Lcom/transsnet/login/LoginProvider;Lcom/transsnet/loginapi/bean/UserInfo;)V
    .locals 1

    const-string v0, "this$0"

    invoke-static {p0, v0}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    const-string v0, "$userInfo"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    iget-object p0, p0, Lcom/transsnet/login/LoginProvider;->c:Ljava/util/List;

    check-cast p0, Ljava/lang/Iterable;

    invoke-interface {p0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    move-result-object p0

    :cond_0
    :goto_0
    invoke-interface {p0}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_1

    invoke-interface {p0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/lang/ref/WeakReference;

    invoke-virtual {v0}, Ljava/lang/ref/Reference;->get()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Leu/a;

    if-eqz v0, :cond_0

    invoke-interface {v0, p1}, Leu/a;->onLogin(Lcom/transsnet/loginapi/bean/UserInfo;)V

    goto :goto_0

    :cond_1
    return-void
.end method

.method public static final W1(Lcom/transsnet/login/LoginProvider;Lcom/transsnet/loginapi/bean/UserInfo;)V
    .locals 1

    const-string v0, "this$0"

    invoke-static {p0, v0}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    const-string v0, "$userInfo"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    iget-object p0, p0, Lcom/transsnet/login/LoginProvider;->c:Ljava/util/List;

    check-cast p0, Ljava/lang/Iterable;

    invoke-interface {p0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    move-result-object p0

    :cond_0
    :goto_0
    invoke-interface {p0}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_1

    invoke-interface {p0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/lang/ref/WeakReference;

    invoke-virtual {v0}, Ljava/lang/ref/Reference;->get()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Leu/a;

    if-eqz v0, :cond_0

    invoke-interface {v0, p1}, Leu/a;->onUpdateUserInfo(Lcom/transsnet/loginapi/bean/UserInfo;)V

    goto :goto_0

    :cond_1
    return-void
.end method


# virtual methods
.method public B()Lcom/transsnet/loginapi/bean/Country;
    .locals 1

    sget-object v0, Lzt/a;->a:Lzt/a;

    invoke-virtual {v0}, Lzt/a;->b()Lcom/transsnet/loginapi/bean/Country;

    move-result-object v0

    return-object v0
.end method

.method public H(Lokhttp3/y;)V
    .locals 2

    const-string v0, "response"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    :try_start_0
    invoke-virtual {p1}, Lokhttp3/y;->g()I

    move-result v0

    const/16 v1, 0xc8

    if-ne v0, v1, :cond_4

    invoke-virtual {p1}, Lokhttp3/y;->n()Lokhttp3/r;

    move-result-object p1

    iget-object v0, p0, Lcom/transsnet/login/LoginProvider;->e:Ljava/lang/String;

    invoke-virtual {p1, v0}, Lokhttp3/r;->a(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    invoke-static {p1}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    if-eqz v0, :cond_0

    return-void

    :cond_0
    const-class v0, Lcom/transsnet/loginapi/bean/UserInfo;

    invoke-static {p1, v0}, Lcom/blankj/utilcode/util/n;->d(Ljava/lang/String;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/transsnet/loginapi/bean/UserInfo;

    iget-object v0, p0, Lcom/transsnet/login/LoginProvider;->b:Lcom/transsnet/loginapi/bean/UserInfo;

    if-nez v0, :cond_1

    const-string v0, "user"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-virtual {p0, p1}, Lcom/transsnet/login/LoginProvider;->S1(Lcom/transsnet/loginapi/bean/UserInfo;)V

    goto :goto_3

    :catch_0
    move-exception p1

    goto :goto_2

    :cond_1
    iget-object v0, p0, Lcom/transsnet/login/LoginProvider;->b:Lcom/transsnet/loginapi/bean/UserInfo;

    if-eqz v0, :cond_2

    invoke-virtual {v0}, Lcom/transsnet/loginapi/bean/UserInfo;->getToken()Ljava/lang/String;

    move-result-object v0

    goto :goto_0

    :cond_2
    const/4 v0, 0x0

    :goto_0
    invoke-virtual {p1}, Lcom/transsnet/loginapi/bean/UserInfo;->getToken()Ljava/lang/String;

    move-result-object v1

    invoke-static {v0, v1}, Landroid/text/TextUtils;->equals(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Z

    move-result v0

    if-nez v0, :cond_5

    iget-object v0, p0, Lcom/transsnet/login/LoginProvider;->b:Lcom/transsnet/loginapi/bean/UserInfo;

    if-nez v0, :cond_3

    goto :goto_1

    :cond_3
    invoke-virtual {p1}, Lcom/transsnet/loginapi/bean/UserInfo;->getToken()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v0, p1}, Lcom/transsnet/loginapi/bean/UserInfo;->setToken(Ljava/lang/String;)V

    :goto_1
    iget-object p1, p0, Lcom/transsnet/login/LoginProvider;->b:Lcom/transsnet/loginapi/bean/UserInfo;

    invoke-virtual {p0, p1}, Lcom/transsnet/login/LoginProvider;->R1(Lcom/transsnet/loginapi/bean/UserInfo;)V

    goto :goto_3

    :cond_4
    invoke-virtual {p1}, Lokhttp3/y;->g()I

    move-result p1

    const/16 v0, 0x191

    if-ne p1, v0, :cond_5

    invoke-virtual {p0}, Lcom/transsnet/login/LoginProvider;->U1()V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_3

    :goto_2
    invoke-virtual {p1}, Ljava/lang/Throwable;->printStackTrace()V

    :cond_5
    :goto_3
    return-void
.end method

.method public J0(Landroid/content/Context;)V
    .locals 2

    const-string v0, "context"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    iput-object p1, p0, Lcom/transsnet/login/LoginProvider;->a:Landroid/content/Context;

    sget-object p1, Lcom/transsnet/login/constant/LoginMmkvUtil;->a:Lcom/transsnet/login/constant/LoginMmkvUtil;

    invoke-virtual {p1}, Lcom/transsnet/login/constant/LoginMmkvUtil;->a()Lcom/tencent/mmkv/MMKV;

    move-result-object p1

    const-string v0, "login_user"

    const-string v1, ""

    invoke-virtual {p1, v0, v1}, Lcom/tencent/mmkv/MMKV;->getString(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    if-nez p1, :cond_0

    goto :goto_0

    :cond_0
    move-object v1, p1

    :goto_0
    invoke-static {v1}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result p1

    if-nez p1, :cond_1

    const-class p1, Lcom/transsnet/loginapi/bean/UserInfo;

    invoke-static {v1, p1}, Lcom/blankj/utilcode/util/n;->d(Ljava/lang/String;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/transsnet/loginapi/bean/UserInfo;

    goto :goto_1

    :cond_1
    const/4 p1, 0x0

    :goto_1
    iget-object v0, p0, Lcom/transsnet/login/LoginProvider;->b:Lcom/transsnet/loginapi/bean/UserInfo;

    if-nez v0, :cond_2

    iput-object p1, p0, Lcom/transsnet/login/LoginProvider;->b:Lcom/transsnet/loginapi/bean/UserInfo;

    if-eqz p1, :cond_2

    invoke-virtual {p1}, Lcom/transsnet/loginapi/bean/UserInfo;->getUserId()Ljava/lang/String;

    move-result-object p1

    if-eqz p1, :cond_2

    sget-object v0, Llj/a;->a:Llj/a$a;

    invoke-virtual {v0, p1}, Llj/a$a;->h(Ljava/lang/String;)V

    :cond_2
    return-void
.end method

.method public M1(Ljava/lang/String;)V
    .locals 3

    invoke-virtual {p0}, Lcom/transsnet/login/LoginProvider;->O1()Lbu/a;

    move-result-object v0

    if-nez p1, :cond_0

    const-string p1, ""

    :cond_0
    const/4 v1, 0x2

    const/4 v2, 0x0

    invoke-static {v0, p1, v2, v1, v2}, Lbu/a$a;->c(Lbu/a;Ljava/lang/String;Ljava/lang/String;ILjava/lang/Object;)Lio/reactivex/rxjava3/core/j;

    move-result-object p1

    invoke-static {}, Lru/a;->b()Lio/reactivex/rxjava3/core/Scheduler;

    move-result-object v0

    invoke-virtual {p1, v0}, Lio/reactivex/rxjava3/core/j;->r(Lio/reactivex/rxjava3/core/Scheduler;)Lio/reactivex/rxjava3/core/j;

    move-result-object p1

    new-instance v0, Lcom/transsnet/login/LoginProvider$b;

    invoke-direct {v0, p0}, Lcom/transsnet/login/LoginProvider$b;-><init>(Lcom/transsnet/login/LoginProvider;)V

    invoke-virtual {p1, v0}, Lio/reactivex/rxjava3/core/j;->q(Llu/h;)Lio/reactivex/rxjava3/core/j;

    move-result-object p1

    new-instance v0, Lcom/transsnet/login/LoginProvider$c;

    invoke-direct {v0, p0}, Lcom/transsnet/login/LoginProvider$c;-><init>(Lcom/transsnet/login/LoginProvider;)V

    invoke-virtual {p1, v0}, Lio/reactivex/rxjava3/core/j;->g(Llu/f;)Lio/reactivex/rxjava3/core/j;

    move-result-object p1

    sget-object v0, Lcj/d;->a:Lcj/d;

    invoke-virtual {v0}, Lcj/d;->c()Lio/reactivex/rxjava3/core/n;

    move-result-object v0

    invoke-virtual {p1, v0}, Lio/reactivex/rxjava3/core/j;->e(Lio/reactivex/rxjava3/core/n;)Lio/reactivex/rxjava3/core/j;

    move-result-object p1

    new-instance v0, Lcom/transsnet/login/LoginProvider$d;

    invoke-direct {v0, p0}, Lcom/transsnet/login/LoginProvider$d;-><init>(Lcom/transsnet/login/LoginProvider;)V

    invoke-virtual {p1, v0}, Lio/reactivex/rxjava3/core/j;->subscribe(Lio/reactivex/rxjava3/core/o;)V

    return-void
.end method

.method public N()Z
    .locals 2

    iget-object v0, p0, Lcom/transsnet/login/LoginProvider;->b:Lcom/transsnet/loginapi/bean/UserInfo;

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/transsnet/login/LoginProvider;->b:Lcom/transsnet/loginapi/bean/UserInfo;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Lcom/transsnet/loginapi/bean/UserInfo;->getUserType()I

    move-result v0

    const/4 v1, 0x1

    if-ne v0, v1, :cond_0

    return v1

    :cond_0
    const/4 v0, 0x0

    return v0
.end method

.method public final N1(Leu/a;)Ljava/lang/ref/WeakReference;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Leu/a;",
            ")",
            "Ljava/lang/ref/WeakReference<",
            "Leu/a;",
            ">;"
        }
    .end annotation

    iget-object v0, p0, Lcom/transsnet/login/LoginProvider;->c:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :cond_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_1

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/lang/ref/WeakReference;

    invoke-virtual {v1}, Ljava/lang/ref/Reference;->get()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Leu/a;

    if-eqz v2, :cond_0

    if-ne v2, p1, :cond_0

    return-object v1

    :cond_1
    const/4 p1, 0x0

    return-object p1
.end method

.method public final O1()Lbu/a;
    .locals 1

    iget-object v0, p0, Lcom/transsnet/login/LoginProvider;->d:Lkotlin/Lazy;

    invoke-interface {v0}, Lkotlin/Lazy;->getValue()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lbu/a;

    return-object v0
.end method

.method public final P1()V
    .locals 2

    const-string v0, ""

    invoke-static {v0}, Lio/reactivex/rxjava3/core/j;->p(Ljava/lang/Object;)Lio/reactivex/rxjava3/core/j;

    move-result-object v0

    sget-object v1, Lcom/transsnet/login/LoginProvider$e;->a:Lcom/transsnet/login/LoginProvider$e;

    invoke-virtual {v0, v1}, Lio/reactivex/rxjava3/core/j;->q(Llu/h;)Lio/reactivex/rxjava3/core/j;

    move-result-object v0

    new-instance v1, Lcom/transsnet/login/LoginProvider$f;

    invoke-direct {v1, p0}, Lcom/transsnet/login/LoginProvider$f;-><init>(Lcom/transsnet/login/LoginProvider;)V

    invoke-virtual {v0, v1}, Lio/reactivex/rxjava3/core/j;->k(Llu/h;)Lio/reactivex/rxjava3/core/j;

    move-result-object v0

    sget-object v1, Lcj/d;->a:Lcj/d;

    invoke-virtual {v1}, Lcj/d;->c()Lio/reactivex/rxjava3/core/n;

    move-result-object v1

    invoke-virtual {v0, v1}, Lio/reactivex/rxjava3/core/j;->e(Lio/reactivex/rxjava3/core/n;)Lio/reactivex/rxjava3/core/j;

    move-result-object v0

    new-instance v1, Lcom/transsnet/login/LoginProvider$g;

    invoke-direct {v1, p0}, Lcom/transsnet/login/LoginProvider$g;-><init>(Lcom/transsnet/login/LoginProvider;)V

    invoke-virtual {v0, v1}, Lio/reactivex/rxjava3/core/j;->subscribe(Lio/reactivex/rxjava3/core/o;)V

    return-void
.end method

.method public Q()Lcom/transsnet/loginapi/bean/UserInfo;
    .locals 1

    iget-object v0, p0, Lcom/transsnet/login/LoginProvider;->b:Lcom/transsnet/loginapi/bean/UserInfo;

    return-object v0
.end method

.method public final declared-synchronized R1(Lcom/transsnet/loginapi/bean/UserInfo;)V
    .locals 7

    monitor-enter p0

    :try_start_0
    iput-object p1, p0, Lcom/transsnet/login/LoginProvider;->b:Lcom/transsnet/loginapi/bean/UserInfo;

    sget-object v0, Lxi/b;->a:Lxi/b$a;

    const-string v1, "login"

    if-eqz p1, :cond_0

    invoke-virtual {p1}, Lcom/transsnet/loginapi/bean/UserInfo;->getUserId()Ljava/lang/String;

    move-result-object v2

    goto :goto_0

    :catchall_0
    move-exception p1

    goto :goto_1

    :cond_0
    const/4 v2, 0x0

    :goto_0
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v4, "saveUserInfo "

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    new-instance v3, Ljava/lang/Throwable;

    const-string v4, "info"

    invoke-direct {v3, v4}, Ljava/lang/Throwable;-><init>(Ljava/lang/String;)V

    const/4 v4, 0x0

    const/16 v5, 0x8

    const/4 v6, 0x0

    invoke-static/range {v0 .. v6}, Lxi/b$a;->e(Lxi/b$a;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Throwable;ZILjava/lang/Object;)V

    if-eqz p1, :cond_1

    invoke-virtual {p1}, Lcom/transsnet/loginapi/bean/UserInfo;->getUserId()Ljava/lang/String;

    move-result-object v0

    if-eqz v0, :cond_1

    sget-object v1, Llj/a;->a:Llj/a$a;

    invoke-virtual {v1, v0}, Llj/a$a;->h(Ljava/lang/String;)V

    :cond_1
    sget-object v0, Lcom/transsnet/login/constant/LoginMmkvUtil;->a:Lcom/transsnet/login/constant/LoginMmkvUtil;

    invoke-virtual {v0}, Lcom/transsnet/login/constant/LoginMmkvUtil;->a()Lcom/tencent/mmkv/MMKV;

    move-result-object v0

    const-string v1, "login_user"

    invoke-static {p1}, Lcom/blankj/utilcode/util/n;->j(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v0, v1, p1}, Lcom/tencent/mmkv/MMKV;->putString(Ljava/lang/String;Ljava/lang/String;)Landroid/content/SharedPreferences$Editor;
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    monitor-exit p0

    return-void

    :goto_1
    monitor-exit p0

    throw p1
.end method

.method public final S1(Lcom/transsnet/loginapi/bean/UserInfo;)V
    .locals 1

    invoke-virtual {p0, p1}, Lcom/transsnet/login/LoginProvider;->R1(Lcom/transsnet/loginapi/bean/UserInfo;)V

    new-instance v0, Lcom/transsnet/login/j;

    invoke-direct {v0, p0, p1}, Lcom/transsnet/login/j;-><init>(Lcom/transsnet/login/LoginProvider;Lcom/transsnet/loginapi/bean/UserInfo;)V

    invoke-static {v0}, Lcom/blankj/utilcode/util/ThreadUtils;->j(Ljava/lang/Runnable;)V

    return-void
.end method

.method public U1()V
    .locals 0

    invoke-direct {p0}, Lcom/transsnet/login/LoginProvider;->R()V

    return-void
.end method

.method public final V1(Lcom/transsnet/loginapi/bean/UserInfo;)V
    .locals 1

    iput-object p1, p0, Lcom/transsnet/login/LoginProvider;->b:Lcom/transsnet/loginapi/bean/UserInfo;

    new-instance v0, Lcom/transsnet/login/k;

    invoke-direct {v0, p0, p1}, Lcom/transsnet/login/k;-><init>(Lcom/transsnet/login/LoginProvider;Lcom/transsnet/loginapi/bean/UserInfo;)V

    invoke-static {v0}, Lcom/blankj/utilcode/util/ThreadUtils;->j(Ljava/lang/Runnable;)V

    return-void
.end method

.method public Z0(Lcom/transsnet/loginapi/bean/UserInfo;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/transsnet/loginapi/bean/UserInfo;",
            "Lkotlin/coroutines/Continuation<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    invoke-virtual {p0, p1}, Lcom/transsnet/login/LoginProvider;->S1(Lcom/transsnet/loginapi/bean/UserInfo;)V

    invoke-virtual {p1}, Lcom/transsnet/loginapi/bean/UserInfo;->getUserId()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1}, Lcom/transsnet/login/LoginProvider;->M1(Ljava/lang/String;)V

    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    return-object p1
.end method

.method public c()Ljava/lang/String;
    .locals 2

    invoke-static {}, Lcom/blankj/utilcode/util/Utils;->a()Landroid/app/Application;

    move-result-object v0

    sget v1, Lcom/transsnet/login/R$string;->login_success:I

    invoke-virtual {v0, v1}, Landroid/content/Context;->getString(I)Ljava/lang/String;

    move-result-object v0

    const-string v1, "getApp().getString(R.string.login_success)"

    invoke-static {v0, v1}, Lkotlin/jvm/internal/Intrinsics;->f(Ljava/lang/Object;Ljava/lang/String;)V

    return-object v0
.end method

.method public c1(Leu/a;)V
    .locals 1

    const-string v0, "listener"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    iget-object v0, p0, Lcom/transsnet/login/LoginProvider;->c:Ljava/util/List;

    check-cast v0, Ljava/util/Collection;

    invoke-virtual {p0, p1}, Lcom/transsnet/login/LoginProvider;->N1(Leu/a;)Ljava/lang/ref/WeakReference;

    move-result-object p1

    invoke-static {v0}, Lkotlin/jvm/internal/TypeIntrinsics;->a(Ljava/lang/Object;)Ljava/util/Collection;

    move-result-object v0

    invoke-interface {v0, p1}, Ljava/util/Collection;->remove(Ljava/lang/Object;)Z

    return-void
.end method

.method public e1(J)V
    .locals 2

    sget-object v0, Lcom/transsnet/login/constant/LoginMmkvUtil;->a:Lcom/transsnet/login/constant/LoginMmkvUtil;

    invoke-virtual {v0}, Lcom/transsnet/login/constant/LoginMmkvUtil;->a()Lcom/tencent/mmkv/MMKV;

    move-result-object v0

    const-string v1, "login_launch_first_state"

    invoke-virtual {v0, v1, p1, p2}, Lcom/tencent/mmkv/MMKV;->putLong(Ljava/lang/String;J)Landroid/content/SharedPreferences$Editor;

    return-void
.end method

.method public g()V
    .locals 1

    sget-object v0, Lcom/tn/lib/util/networkinfo/f;->a:Lcom/tn/lib/util/networkinfo/f;

    invoke-virtual {v0}, Lcom/tn/lib/util/networkinfo/f;->e()Z

    move-result v0

    if-nez v0, :cond_0

    invoke-direct {p0}, Lcom/transsnet/login/LoginProvider;->R()V

    return-void

    :cond_0
    invoke-virtual {p0}, Lcom/transsnet/login/LoginProvider;->P1()V

    return-void
.end method

.method public init(Landroid/content/Context;)V
    .locals 1

    const-string v0, "context"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    return-void
.end method

.method public j1()J
    .locals 4

    sget-object v0, Lcom/transsnet/login/constant/LoginMmkvUtil;->a:Lcom/transsnet/login/constant/LoginMmkvUtil;

    invoke-virtual {v0}, Lcom/transsnet/login/constant/LoginMmkvUtil;->a()Lcom/tencent/mmkv/MMKV;

    move-result-object v0

    const-string v1, "login_launch_first_state"

    const-wide/16 v2, 0x0

    invoke-virtual {v0, v1, v2, v3}, Lcom/tencent/mmkv/MMKV;->getLong(Ljava/lang/String;J)J

    move-result-wide v0

    return-wide v0
.end method

.method public k0(Leu/a;)V
    .locals 2

    const-string v0, "listener"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-virtual {p0, p1}, Lcom/transsnet/login/LoginProvider;->N1(Leu/a;)Ljava/lang/ref/WeakReference;

    move-result-object v0

    if-nez v0, :cond_0

    iget-object v0, p0, Lcom/transsnet/login/LoginProvider;->c:Ljava/util/List;

    new-instance v1, Ljava/lang/ref/WeakReference;

    invoke-direct {v1, p1}, Ljava/lang/ref/WeakReference;-><init>(Ljava/lang/Object;)V

    invoke-interface {v0, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    :cond_0
    return-void
.end method

.method public r1(Landroid/content/Context;)Landroid/content/Intent;
    .locals 2

    const-string v0, "context"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    new-instance v0, Landroid/content/Intent;

    const-class v1, Lcom/transsnet/login/LoginLikeActivity;

    invoke-direct {v0, p1, v1}, Landroid/content/Intent;-><init>(Landroid/content/Context;Ljava/lang/Class;)V

    return-object v0
.end method

.method public x0(Landroid/content/Context;)V
    .locals 2

    const-string v0, "context"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    new-instance v0, Landroid/content/Intent;

    const-class v1, Lcom/transsnet/login/LoginLikeActivity;

    invoke-direct {v0, p1, v1}, Landroid/content/Intent;-><init>(Landroid/content/Context;Ljava/lang/Class;)V

    const/high16 v1, 0x10000000

    invoke-virtual {v0, v1}, Landroid/content/Intent;->setFlags(I)Landroid/content/Intent;

    invoke-virtual {p1, v0}, Landroid/content/Context;->startActivity(Landroid/content/Intent;)V

    return-void
.end method
