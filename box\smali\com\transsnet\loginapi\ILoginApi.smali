.class public interface abstract Lcom/transsnet/loginapi/ILoginApi;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/alibaba/android/arouter/facade/template/IProvider;


# annotations
.annotation runtime L<PERSON><PERSON>/Metadata;
.end annotation


# virtual methods
.method public abstract B()Lcom/transsnet/loginapi/bean/Country;
.end method

.method public abstract H(Lokhttp3/y;)V
.end method

.method public abstract J0(Landroid/content/Context;)V
.end method

.method public abstract N()Z
.end method

.method public abstract Q()Lcom/transsnet/loginapi/bean/UserInfo;
.end method

.method public abstract Z0(Lcom/transsnet/loginapi/bean/UserInfo;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/transsnet/loginapi/bean/UserInfo;",
            "Lkotlin/coroutines/Continuation<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation
.end method

.method public abstract c()Ljava/lang/String;
.end method

.method public abstract c1(Leu/a;)V
.end method

.method public abstract e1(J)V
.end method

.method public abstract g()V
.end method

.method public abstract j1()J
.end method

.method public abstract k0(Leu/a;)V
.end method

.method public abstract r1(Landroid/content/Context;)Landroid/content/Intent;
.end method

.method public abstract x0(Landroid/content/Context;)V
.end method
