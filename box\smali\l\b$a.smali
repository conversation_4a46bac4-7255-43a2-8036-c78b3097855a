.class public interface abstract Ll/b$a;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Ll/b;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "a"
.end annotation


# virtual methods
.method public abstract a(Ll/b;Landroid/view/MenuItem;)Z
.end method

.method public abstract b(Ll/b;Landroid/view/Menu;)Z
.end method

.method public abstract c(Ll/b;Landroid/view/Menu;)Z
.end method

.method public abstract d(Ll/b;)V
.end method
