.class public interface abstract Lo1/b$a;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lo1/b;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "a"
.end annotation


# virtual methods
.method public abstract a(Landroid/database/Cursor;)V
.end method

.method public abstract b(Landroid/database/Cursor;)Ljava/lang/CharSequence;
.end method

.method public abstract c(Ljava/lang/CharSequence;)Landroid/database/Cursor;
.end method

.method public abstract d()Landroid/database/Cursor;
.end method
