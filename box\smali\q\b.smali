.class public interface abstract Lq/b;
.super Ljava/lang/Object;


# virtual methods
.method public abstract a(IIII)V
.end method

.method public abstract b(Landroid/graphics/drawable/Drawable;)V
.end method

.method public abstract c()Z
.end method

.method public abstract d()Landroid/graphics/drawable/Drawable;
.end method

.method public abstract e()Z
.end method

.method public abstract f()Landroid/view/View;
.end method
