.class public interface abstract Lv0/e;
.super Ljava/lang/Object;

# interfaces
.implements Lv0/n;


# annotations
.annotation runtime L<PERSON>lin/Metadata;
.end annotation

.annotation build Lkotlin/jvm/internal/SourceDebugExtension;
.end annotation


# virtual methods
.method public abstract H0(F)F
.end method

.method public abstract O0(F)F
.end method

.method public abstract W(F)J
.end method

.method public abstract Y0(J)J
.end method

.method public abstract getDensity()F
.end method

.method public abstract j0(F)I
.end method

.method public abstract p0(J)F
.end method
