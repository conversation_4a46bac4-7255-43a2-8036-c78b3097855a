.class public Ly/d;
.super Lkotlin/collections/AbstractMap;

# interfaces
.implements Lx/g;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Ly/d$a;
    }
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "<K:",
        "Ljava/lang/Object;",
        "V:",
        "Ljava/lang/Object;",
        ">",
        "Lkotlin/collections/AbstractMap<",
        "TK;TV;>;",
        "Lx/g<",
        "TK;TV;>;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation

.annotation build Lkotlin/jvm/internal/SourceDebugExtension;
.end annotation


# static fields
.field public static final f:Ly/d$a;

.field public static final g:I

.field public static final h:Ly/d;


# instance fields
.field public final d:Ly/t;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ly/t<",
            "TK;TV;>;"
        }
    .end annotation
.end field

.field public final e:I


# direct methods
.method static constructor <clinit>()V
    .locals 3

    new-instance v0, Ly/d$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Ly/d$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Ly/d;->f:Ly/d$a;

    const/16 v0, 0x8

    sput v0, Ly/d;->g:I

    new-instance v0, Ly/d;

    sget-object v1, Ly/t;->e:Ly/t$a;

    invoke-virtual {v1}, Ly/t$a;->a()Ly/t;

    move-result-object v1

    const/4 v2, 0x0

    invoke-direct {v0, v1, v2}, Ly/d;-><init>(Ly/t;I)V

    sput-object v0, Ly/d;->h:Ly/d;

    return-void
.end method

.method public constructor <init>(Ly/t;I)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ly/t<",
            "TK;TV;>;I)V"
        }
    .end annotation

    invoke-direct {p0}, Lkotlin/collections/AbstractMap;-><init>()V

    iput-object p1, p0, Ly/d;->d:Ly/t;

    iput p2, p0, Ly/d;->e:I

    return-void
.end method

.method public static final synthetic o()Ly/d;
    .locals 1

    sget-object v0, Ly/d;->h:Ly/d;

    return-object v0
.end method


# virtual methods
.method public bridge synthetic builder()Lx/g$a;
    .locals 1

    invoke-virtual {p0}, Ly/d;->p()Ly/f;

    move-result-object v0

    return-object v0
.end method

.method public containsKey(Ljava/lang/Object;)Z
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(TK;)Z"
        }
    .end annotation

    iget-object v0, p0, Ly/d;->d:Ly/t;

    const/4 v1, 0x0

    if-eqz p1, :cond_0

    invoke-virtual {p1}, Ljava/lang/Object;->hashCode()I

    move-result v2

    goto :goto_0

    :cond_0
    const/4 v2, 0x0

    :goto_0
    invoke-virtual {v0, v2, p1, v1}, Ly/t;->k(ILjava/lang/Object;I)Z

    move-result p1

    return p1
.end method

.method public final e()Ljava/util/Set;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Set<",
            "Ljava/util/Map$Entry<",
            "TK;TV;>;>;"
        }
    .end annotation

    .annotation build Lkotlin/PublishedApi;
    .end annotation

    invoke-virtual {p0}, Ly/d;->q()Lx/e;

    move-result-object v0

    return-object v0
.end method

.method public bridge synthetic g()Ljava/util/Set;
    .locals 1

    invoke-virtual {p0}, Ly/d;->r()Lx/e;

    move-result-object v0

    return-object v0
.end method

.method public get(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(TK;)TV;"
        }
    .end annotation

    iget-object v0, p0, Ly/d;->d:Ly/t;

    const/4 v1, 0x0

    if-eqz p1, :cond_0

    invoke-virtual {p1}, Ljava/lang/Object;->hashCode()I

    move-result v2

    goto :goto_0

    :cond_0
    const/4 v2, 0x0

    :goto_0
    invoke-virtual {v0, v2, p1, v1}, Ly/t;->o(ILjava/lang/Object;I)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public h()I
    .locals 1

    iget v0, p0, Ly/d;->e:I

    return v0
.end method

.method public bridge synthetic i()Ljava/util/Collection;
    .locals 1

    invoke-virtual {p0}, Ly/d;->t()Lx/b;

    move-result-object v0

    return-object v0
.end method

.method public p()Ly/f;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ly/f<",
            "TK;TV;>;"
        }
    .end annotation

    new-instance v0, Ly/f;

    invoke-direct {v0, p0}, Ly/f;-><init>(Ly/d;)V

    return-object v0
.end method

.method public final q()Lx/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lx/e<",
            "Ljava/util/Map$Entry<",
            "TK;TV;>;>;"
        }
    .end annotation

    new-instance v0, Ly/n;

    invoke-direct {v0, p0}, Ly/n;-><init>(Ly/d;)V

    return-object v0
.end method

.method public r()Lx/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lx/e<",
            "TK;>;"
        }
    .end annotation

    new-instance v0, Ly/p;

    invoke-direct {v0, p0}, Ly/p;-><init>(Ly/d;)V

    return-object v0
.end method

.method public final s()Ly/t;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ly/t<",
            "TK;TV;>;"
        }
    .end annotation

    iget-object v0, p0, Ly/d;->d:Ly/t;

    return-object v0
.end method

.method public t()Lx/b;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lx/b<",
            "TV;>;"
        }
    .end annotation

    new-instance v0, Ly/r;

    invoke-direct {v0, p0}, Ly/r;-><init>(Ly/d;)V

    return-object v0
.end method

.method public u(Ljava/lang/Object;Ljava/lang/Object;)Ly/d;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(TK;TV;)",
            "Ly/d<",
            "TK;TV;>;"
        }
    .end annotation

    iget-object v0, p0, Ly/d;->d:Ly/t;

    const/4 v1, 0x0

    if-eqz p1, :cond_0

    invoke-virtual {p1}, Ljava/lang/Object;->hashCode()I

    move-result v2

    goto :goto_0

    :cond_0
    const/4 v2, 0x0

    :goto_0
    invoke-virtual {v0, v2, p1, p2, v1}, Ly/t;->P(ILjava/lang/Object;Ljava/lang/Object;I)Ly/t$b;

    move-result-object p1

    if-nez p1, :cond_1

    return-object p0

    :cond_1
    new-instance p2, Ly/d;

    invoke-virtual {p1}, Ly/t$b;->a()Ly/t;

    move-result-object v0

    invoke-virtual {p0}, Lkotlin/collections/AbstractMap;->size()I

    move-result v1

    invoke-virtual {p1}, Ly/t$b;->b()I

    move-result p1

    add-int/2addr v1, p1

    invoke-direct {p2, v0, v1}, Ly/d;-><init>(Ly/t;I)V

    return-object p2
.end method

.method public v(Ljava/lang/Object;)Ly/d;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(TK;)",
            "Ly/d<",
            "TK;TV;>;"
        }
    .end annotation

    iget-object v0, p0, Ly/d;->d:Ly/t;

    const/4 v1, 0x0

    if-eqz p1, :cond_0

    invoke-virtual {p1}, Ljava/lang/Object;->hashCode()I

    move-result v2

    goto :goto_0

    :cond_0
    const/4 v2, 0x0

    :goto_0
    invoke-virtual {v0, v2, p1, v1}, Ly/t;->Q(ILjava/lang/Object;I)Ly/t;

    move-result-object p1

    iget-object v0, p0, Ly/d;->d:Ly/t;

    if-ne v0, p1, :cond_1

    return-object p0

    :cond_1
    if-nez p1, :cond_2

    sget-object p1, Ly/d;->f:Ly/d$a;

    invoke-virtual {p1}, Ly/d$a;->a()Ly/d;

    move-result-object p1

    return-object p1

    :cond_2
    new-instance v0, Ly/d;

    invoke-virtual {p0}, Lkotlin/collections/AbstractMap;->size()I

    move-result v1

    add-int/lit8 v1, v1, -0x1

    invoke-direct {v0, p1, v1}, Ly/d;-><init>(Ly/t;I)V

    return-object v0
.end method
