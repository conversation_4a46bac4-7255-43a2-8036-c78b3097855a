.class public interface abstract annotation Lcom/facebook/ads/internal/checkerframework/checker/nullness/qual/NonNull;
.super Ljava/lang/Object;
.source ""

# interfaces
.implements Ljava/lang/annotation/Annotation;


# annotations
.annotation runtime Lcom/facebook/ads/internal/checkerframework/framework/qual/DefaultFor;
    value = {
        .enum Lcom/facebook/ads/redexgen/X/7Y;->A05:Lcom/facebook/ads/redexgen/X/7Y;
    }
.end annotation

.annotation runtime Lcom/facebook/ads/internal/checkerframework/framework/qual/DefaultInUncheckedCodeFor;
    value = {
        .enum Lcom/facebook/ads/redexgen/X/7Y;->A0E:Lcom/facebook/ads/redexgen/X/7Y;,
        .enum Lcom/facebook/ads/redexgen/X/7Y;->A0C:Lcom/facebook/ads/redexgen/X/7Y;
    }
.end annotation

.annotation runtime Lcom/facebook/ads/internal/checkerframework/framework/qual/DefaultQualifierInHierarchy;
.end annotation

.annotation runtime Lcom/facebook/ads/internal/checkerframework/framework/qual/QualifierForLiterals;
    value = {
        .enum Lcom/facebook/ads/redexgen/X/7J;->A0B:Lcom/facebook/ads/redexgen/X/7J;
    }
.end annotation

.annotation runtime Lcom/facebook/ads/internal/checkerframework/framework/qual/SubtypeOf;
    value = {
        Lcom/facebook/ads/internal/checkerframework/checker/nullness/qual/MonotonicNonNull;
    }
.end annotation

.annotation runtime Lcom/facebook/ads/internal/checkerframework/framework/qual/UpperBoundFor;
    typeKinds = {
        .enum Lcom/facebook/ads/redexgen/X/7X;->A0I:Lcom/facebook/ads/redexgen/X/7X;,
        .enum Lcom/facebook/ads/redexgen/X/7X;->A0C:Lcom/facebook/ads/redexgen/X/7X;,
        .enum Lcom/facebook/ads/redexgen/X/7X;->A04:Lcom/facebook/ads/redexgen/X/7X;,
        .enum Lcom/facebook/ads/redexgen/X/7X;->A06:Lcom/facebook/ads/redexgen/X/7X;,
        .enum Lcom/facebook/ads/redexgen/X/7X;->A08:Lcom/facebook/ads/redexgen/X/7X;,
        .enum Lcom/facebook/ads/redexgen/X/7X;->A0B:Lcom/facebook/ads/redexgen/X/7X;,
        .enum Lcom/facebook/ads/redexgen/X/7X;->A0E:Lcom/facebook/ads/redexgen/X/7X;,
        .enum Lcom/facebook/ads/redexgen/X/7X;->A0J:Lcom/facebook/ads/redexgen/X/7X;,
        .enum Lcom/facebook/ads/redexgen/X/7X;->A05:Lcom/facebook/ads/redexgen/X/7X;
    }
.end annotation

.annotation runtime Ljava/lang/annotation/Documented;
.end annotation

.annotation runtime Ljava/lang/annotation/Retention;
    value = .enum Ljava/lang/annotation/RetentionPolicy;->RUNTIME:Ljava/lang/annotation/RetentionPolicy;
.end annotation

.annotation runtime Ljava/lang/annotation/Target;
    value = {
        .enum Ljava/lang/annotation/ElementType;->TYPE_USE:Ljava/lang/annotation/ElementType;,
        .enum Ljava/lang/annotation/ElementType;->TYPE_PARAMETER:Ljava/lang/annotation/ElementType;
    }
.end annotation
