.class public interface abstract Lcom/facebook/ads/redexgen/X/00;
.super Ljava/lang/Object;
.source ""


# virtual methods
.method public abstract AFH(Lcom/facebook/ads/redexgen/X/06;Z)Lcom/facebook/ads/redexgen/X/02;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/facebook/ads/redexgen/X/06;",
            "Z)",
            "Lcom/facebook/ads/redexgen/X/02<",
            "Landroid/graphics/Bitmap;",
            ">;"
        }
    .end annotation
.end method

.method public abstract AFI(Lcom/facebook/ads/redexgen/X/06;)Ljava/io/File;
.end method

.method public abstract AFJ(Lcom/facebook/ads/redexgen/X/06;)Ljava/lang/String;
.end method

.method public abstract AFK(Lcom/facebook/ads/redexgen/X/06;)Ljava/lang/String;
.end method
