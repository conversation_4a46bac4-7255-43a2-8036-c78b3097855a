.class public final Lcom/facebook/ads/redexgen/X/08;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/facebook/ads/redexgen/X/07;
    }
.end annotation


# instance fields
.field public final A00:I

.field public final A01:I

.field public final A02:Z

.field public final A03:Z

.field public final A04:Z


# direct methods
.method public constructor <init>(IZIZZ)V
    .locals 0

    .line 2801
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2802
    iput p1, p0, Lcom/facebook/ads/redexgen/X/08;->A00:I

    .line 2803
    iput-boolean p2, p0, Lcom/facebook/ads/redexgen/X/08;->A02:Z

    .line 2804
    iput p3, p0, Lcom/facebook/ads/redexgen/X/08;->A01:I

    .line 2805
    iput-boolean p4, p0, Lcom/facebook/ads/redexgen/X/08;->A03:Z

    .line 2806
    iput-boolean p5, p0, Lcom/facebook/ads/redexgen/X/08;->A04:Z

    .line 2807
    return-void
.end method


# virtual methods
.method public final A00()I
    .locals 1

    .line 2808
    iget v0, p0, Lcom/facebook/ads/redexgen/X/08;->A00:I

    return v0
.end method

.method public final A01()I
    .locals 1

    .line 2809
    iget v0, p0, Lcom/facebook/ads/redexgen/X/08;->A01:I

    return v0
.end method

.method public final A02()Z
    .locals 1

    .line 2810
    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/08;->A02:Z

    return v0
.end method

.method public final A03()Z
    .locals 1

    .line 2811
    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/08;->A03:Z

    return v0
.end method

.method public final A04()Z
    .locals 1

    .line 2812
    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/08;->A04:Z

    return v0
.end method
