.class public interface abstract Lcom/facebook/ads/redexgen/X/0K;
.super Ljava/lang/Object;
.source ""


# virtual methods
.method public abstract ADm(I)V
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lcom/facebook/ads/redexgen/X/bk;
        }
    .end annotation
.end method

.method public abstract close()V
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lcom/facebook/ads/redexgen/X/bk;
        }
    .end annotation
.end method

.method public abstract length()I
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lcom/facebook/ads/redexgen/X/bk;
        }
    .end annotation
.end method

.method public abstract read([B)I
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lcom/facebook/ads/redexgen/X/bk;
        }
    .end annotation
.end method
