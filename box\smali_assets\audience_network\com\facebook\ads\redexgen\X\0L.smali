.class public interface abstract Lcom/facebook/ads/redexgen/X/0L;
.super Ljava/lang/Object;
.source ""


# virtual methods
.method public abstract A9U(Ljava/lang/Throwable;)V
.end method

.method public abstract A9f(Ljava/lang/Throwable;)V
.end method

.method public abstract AEP(Ljava/lang/String;ILjava/lang/String;Ljava/lang/Long;Ljava/lang/Long;Lcom/facebook/ads/redexgen/X/06;)V
.end method

.method public abstract AEQ(Ljava/lang/String;ZLcom/facebook/ads/redexgen/X/06;)V
.end method
