.class public abstract Lcom/facebook/ads/redexgen/X/0X;
.super Ljava/lang/Object;
.source ""


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 3017
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static A00()Lcom/facebook/ads/redexgen/X/be;
    .locals 1

    .line 3018
    new-instance v0, Lcom/facebook/ads/redexgen/X/be;

    invoke-direct {v0}, Lcom/facebook/ads/redexgen/X/be;-><init>()V

    return-object v0
.end method
