.class public final enum Lcom/facebook/ads/redexgen/X/0g;
.super Ljava/lang/Enum;
.source ""


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Enum<",
        "Lcom/facebook/ads/redexgen/X/0g;",
        ">;"
    }
.end annotation


# static fields
.field public static A00:[B

.field public static final synthetic A01:[Lcom/facebook/ads/redexgen/X/0g;

.field public static final enum A02:Lcom/facebook/ads/redexgen/X/0g;

.field public static final enum A03:Lcom/facebook/ads/redexgen/X/0g;

.field public static final enum A04:Lcom/facebook/ads/redexgen/X/0g;

.field public static final enum A05:Lcom/facebook/ads/redexgen/X/0g;

.field public static final enum A06:Lcom/facebook/ads/redexgen/X/0g;

.field public static final enum A07:Lcom/facebook/ads/redexgen/X/0g;

.field public static final enum A08:Lcom/facebook/ads/redexgen/X/0g;

.field public static final enum A09:Lcom/facebook/ads/redexgen/X/0g;


# direct methods
.method public static constructor <clinit>()V
    .locals 16

    .line 282
    invoke-static {}, Lcom/facebook/ads/redexgen/X/0g;->A01()V

    const/16 v2, 0x3a

    const/4 v1, 0x4

    const/16 v0, 0x52

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/0g;->A00(III)Ljava/lang/String;

    move-result-object v0

    const/4 v15, 0x0

    new-instance v14, Lcom/facebook/ads/redexgen/X/0g;

    invoke-direct {v14, v0, v15}, Lcom/facebook/ads/redexgen/X/0g;-><init>(Ljava/lang/String;I)V

    sput-object v14, Lcom/facebook/ads/redexgen/X/0g;->A08:Lcom/facebook/ads/redexgen/X/0g;

    .line 283
    const/16 v2, 0x9

    const/16 v1, 0xb

    const/16 v0, 0x52

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/0g;->A00(III)Ljava/lang/String;

    move-result-object v0

    const/4 v13, 0x1

    new-instance v12, Lcom/facebook/ads/redexgen/X/0g;

    invoke-direct {v12, v0, v13}, Lcom/facebook/ads/redexgen/X/0g;-><init>(Ljava/lang/String;I)V

    sput-object v12, Lcom/facebook/ads/redexgen/X/0g;->A03:Lcom/facebook/ads/redexgen/X/0g;

    .line 284
    const/16 v2, 0x14

    const/16 v1, 0xc

    const/16 v0, 0x51

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/0g;->A00(III)Ljava/lang/String;

    move-result-object v0

    const/4 v11, 0x2

    new-instance v10, Lcom/facebook/ads/redexgen/X/0g;

    invoke-direct {v10, v0, v11}, Lcom/facebook/ads/redexgen/X/0g;-><init>(Ljava/lang/String;I)V

    sput-object v10, Lcom/facebook/ads/redexgen/X/0g;->A04:Lcom/facebook/ads/redexgen/X/0g;

    .line 285
    const/16 v2, 0x3e

    const/16 v1, 0xf

    const/16 v0, 0x11

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/0g;->A00(III)Ljava/lang/String;

    move-result-object v0

    const/4 v9, 0x3

    new-instance v8, Lcom/facebook/ads/redexgen/X/0g;

    invoke-direct {v8, v0, v9}, Lcom/facebook/ads/redexgen/X/0g;-><init>(Ljava/lang/String;I)V

    sput-object v8, Lcom/facebook/ads/redexgen/X/0g;->A09:Lcom/facebook/ads/redexgen/X/0g;

    .line 286
    const/4 v2, 0x0

    const/16 v1, 0x9

    const/16 v0, 0x3f

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/0g;->A00(III)Ljava/lang/String;

    move-result-object v0

    const/4 v7, 0x4

    new-instance v6, Lcom/facebook/ads/redexgen/X/0g;

    invoke-direct {v6, v0, v7}, Lcom/facebook/ads/redexgen/X/0g;-><init>(Ljava/lang/String;I)V

    sput-object v6, Lcom/facebook/ads/redexgen/X/0g;->A02:Lcom/facebook/ads/redexgen/X/0g;

    .line 287
    const/16 v2, 0x37

    const/4 v1, 0x3

    const/16 v0, 0x62

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/0g;->A00(III)Ljava/lang/String;

    move-result-object v1

    const/4 v5, 0x5

    new-instance v0, Lcom/facebook/ads/redexgen/X/0g;

    invoke-direct {v0, v1, v5}, Lcom/facebook/ads/redexgen/X/0g;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lcom/facebook/ads/redexgen/X/0g;->A07:Lcom/facebook/ads/redexgen/X/0g;

    .line 288
    const/16 v3, 0x27

    const/16 v2, 0x10

    const/16 v1, 0x5d

    invoke-static {v3, v2, v1}, Lcom/facebook/ads/redexgen/X/0g;->A00(III)Ljava/lang/String;

    move-result-object v2

    const/4 v1, 0x6

    new-instance v4, Lcom/facebook/ads/redexgen/X/0g;

    invoke-direct {v4, v2, v1}, Lcom/facebook/ads/redexgen/X/0g;-><init>(Ljava/lang/String;I)V

    sput-object v4, Lcom/facebook/ads/redexgen/X/0g;->A06:Lcom/facebook/ads/redexgen/X/0g;

    .line 289
    const/16 v3, 0x20

    const/4 v2, 0x7

    const/16 v1, 0x1b

    invoke-static {v3, v2, v1}, Lcom/facebook/ads/redexgen/X/0g;->A00(III)Ljava/lang/String;

    move-result-object v1

    const/4 v3, 0x7

    new-instance v2, Lcom/facebook/ads/redexgen/X/0g;

    invoke-direct {v2, v1, v3}, Lcom/facebook/ads/redexgen/X/0g;-><init>(Ljava/lang/String;I)V

    sput-object v2, Lcom/facebook/ads/redexgen/X/0g;->A05:Lcom/facebook/ads/redexgen/X/0g;

    .line 290
    const/16 v1, 0x8

    new-array v1, v1, [Lcom/facebook/ads/redexgen/X/0g;

    aput-object v14, v1, v15

    aput-object v12, v1, v13

    aput-object v10, v1, v11

    aput-object v8, v1, v9

    aput-object v6, v1, v7

    aput-object v0, v1, v5

    const/4 v0, 0x6

    aput-object v4, v1, v0

    aput-object v2, v1, v3

    sput-object v1, Lcom/facebook/ads/redexgen/X/0g;->A01:[Lcom/facebook/ads/redexgen/X/0g;

    return-void
.end method

.method public constructor <init>(Ljava/lang/String;I)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation

    .line 3033
    invoke-direct {p0, p1, p2}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    return-void
.end method

.method public static A00(III)Ljava/lang/String;
    .locals 2

    sget-object v1, Lcom/facebook/ads/redexgen/X/0g;->A00:[B

    add-int v0, p0, p1

    invoke-static {v1, p0, v0}, Ljava/util/Arrays;->copyOfRange([BII)[B

    move-result-object p0

    const/4 v1, 0x0

    :goto_0
    array-length v0, p0

    if-ge v1, v0, :cond_0

    aget-byte v0, p0, v1

    sub-int/2addr v0, p2

    add-int/lit8 v0, v0, -0x65

    int-to-byte v0, v0

    aput-byte v0, p0, v1

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_0
    new-instance v0, Ljava/lang/String;

    invoke-direct {v0, p0}, Ljava/lang/String;-><init>([B)V

    return-object v0
.end method

.method public static A01()V
    .locals 1

    const/16 v0, 0x4d

    new-array v0, v0, [B

    fill-array-data v0, :array_0

    sput-object v0, Lcom/facebook/ads/redexgen/X/0g;->A00:[B

    return-void

    :array_0
    .array-data 1
        -0x1bt
        -0xct
        -0xct
        0x3t
        -0x9t
        -0x8t
        -0xdt
        -0xat
        -0x17t
        -0x6t
        -0x8t
        0x5t
        0x5t
        0x6t
        0xbt
        0x16t
        0x6t
        0x7t
        -0x4t
        0x5t
        -0x7t
        -0x9t
        0x4t
        0x4t
        0x5t
        0xat
        0x15t
        0xat
        0x8t
        -0x9t
        -0x7t
        0x1t
        -0x3ct
        -0x2et
        -0x31t
        -0x30t
        -0x30t
        -0x3bt
        -0x3ct
        0x7t
        0x1at
        0x16t
        0x7t
        0x14t
        0x10t
        0x3t
        0xet
        0x21t
        0x4t
        0x14t
        0x11t
        0x19t
        0x15t
        0x7t
        0x14t
        0x10t
        0x8t
        0x9t
        0x5t
        0x6t
        0x5t
        -0x4t
        -0x38t
        -0x45t
        -0x46t
        -0x41t
        -0x38t
        -0x45t
        -0x47t
        -0x36t
        -0x2bt
        -0x36t
        -0x3bt
        -0x2bt
        -0x49t
        -0x3at
        -0x3at
    .end array-data
.end method

.method public static A02(Lcom/facebook/ads/redexgen/X/0g;)Z
    .locals 1

    .line 3034
    sget-object v0, Lcom/facebook/ads/redexgen/X/0g;->A03:Lcom/facebook/ads/redexgen/X/0g;

    invoke-virtual {v0, p0}, Lcom/facebook/ads/redexgen/X/0g;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_0

    sget-object v0, Lcom/facebook/ads/redexgen/X/0g;->A04:Lcom/facebook/ads/redexgen/X/0g;

    invoke-virtual {v0, p0}, Lcom/facebook/ads/redexgen/X/0g;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_1

    :cond_0
    const/4 v0, 0x1

    :goto_0
    return v0

    :cond_1
    const/4 v0, 0x0

    goto :goto_0
.end method

.method public static valueOf(Ljava/lang/String;)Lcom/facebook/ads/redexgen/X/0g;
    .locals 1

    .line 3035
    const-class v0, Lcom/facebook/ads/redexgen/X/0g;

    invoke-static {v0, p0}, Ljava/lang/Enum;->valueOf(Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Enum;

    move-result-object v0

    check-cast v0, Lcom/facebook/ads/redexgen/X/0g;

    return-object v0
.end method

.method public static values()[Lcom/facebook/ads/redexgen/X/0g;
    .locals 1

    .line 3036
    sget-object v0, Lcom/facebook/ads/redexgen/X/0g;->A01:[Lcom/facebook/ads/redexgen/X/0g;

    invoke-virtual {v0}, [Ljava/lang/Object;->clone()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [Lcom/facebook/ads/redexgen/X/0g;

    return-object v0
.end method
