.class public abstract Lcom/facebook/ads/redexgen/X/0h;
.super Ljava/lang/Object;
.source ""


# instance fields
.field public final A00:Lcom/facebook/ads/redexgen/X/Yn;

.field public final A01:Lcom/facebook/ads/redexgen/X/J2;

.field public final A02:Ljava/lang/String;


# direct methods
.method public constructor <init>(Lcom/facebook/ads/redexgen/X/Yn;Lcom/facebook/ads/redexgen/X/J2;Ljava/lang/String;)V
    .locals 0

    .line 3037
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 3038
    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/0h;->A00:Lcom/facebook/ads/redexgen/X/Yn;

    .line 3039
    iput-object p2, p0, Lcom/facebook/ads/redexgen/X/0h;->A01:Lcom/facebook/ads/redexgen/X/J2;

    .line 3040
    iput-object p3, p0, Lcom/facebook/ads/redexgen/X/0h;->A02:Ljava/lang/String;

    .line 3041
    return-void
.end method


# virtual methods
.method public abstract A0C()Lcom/facebook/ads/redexgen/X/0g;
.end method
