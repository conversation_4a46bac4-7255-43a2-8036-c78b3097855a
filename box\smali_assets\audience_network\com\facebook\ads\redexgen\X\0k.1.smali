.class public interface abstract Lcom/facebook/ads/redexgen/X/0k;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/facebook/ads/redexgen/X/0l;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "Invalidateable"
.end annotation


# virtual methods
.method public abstract A6T()Ljava/lang/String;
.end method

.method public abstract A6n()Ljava/util/Collection;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Collection<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end method

.method public abstract A7E()Lcom/facebook/ads/redexgen/X/0j;
.end method
