.class public interface abstract Lcom/facebook/ads/redexgen/X/0x;
.super Ljava/lang/Object;
.source ""


# virtual methods
.method public abstract AAx(Lcom/facebook/ads/redexgen/X/bN;)V
.end method

.method public abstract AAy(Lcom/facebook/ads/redexgen/X/bN;Landroid/view/View;)V
.end method

.method public abstract AAz(Lcom/facebook/ads/redexgen/X/bN;)V
.end method

.method public abstract ABw(Lcom/facebook/ads/redexgen/X/bN;Lcom/facebook/ads/redexgen/X/Jb;)V
.end method
