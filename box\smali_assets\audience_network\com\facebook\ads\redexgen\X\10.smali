.class public interface abstract Lcom/facebook/ads/redexgen/X/10;
.super Ljava/lang/Object;
.source ""


# virtual methods
.method public abstract ABl(Lcom/facebook/ads/redexgen/X/bM;Ljava/lang/String;Z)V
.end method

.method public abstract ABm(Lcom/facebook/ads/redexgen/X/bM;)V
.end method

.method public abstract ABn(Lcom/facebook/ads/redexgen/X/bM;)V
.end method

.method public abstract ABo(Lcom/facebook/ads/redexgen/X/bM;)V
.end method

.method public abstract ABp(Lcom/facebook/ads/redexgen/X/bM;Lcom/facebook/ads/AdError;)V
.end method

.method public abstract ABq(Lcom/facebook/ads/redexgen/X/bM;)V
.end method

.method public abstract ABr()V
.end method

.method public abstract ABs()V
.end method

.method public abstract ABt()V
.end method

.method public abstract onInterstitialActivityDestroyed()V
.end method
