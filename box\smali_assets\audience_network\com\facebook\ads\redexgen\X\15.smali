.class public interface abstract Lcom/facebook/ads/redexgen/X/15;
.super Ljava/lang/Object;
.source ""


# virtual methods
.method public abstract ACM(Lcom/facebook/ads/redexgen/X/bK;)V
.end method

.method public abstract ACN(Lcom/facebook/ads/redexgen/X/bK;)V
.end method

.method public abstract ACO(Lcom/facebook/ads/redexgen/X/bK;)V
.end method

.method public abstract ACQ(Lcom/facebook/ads/redexgen/X/bK;Lcom/facebook/ads/redexgen/X/Jb;)V
.end method
