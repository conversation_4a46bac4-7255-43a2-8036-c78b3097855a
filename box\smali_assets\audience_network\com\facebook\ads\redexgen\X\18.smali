.class public interface abstract Lcom/facebook/ads/redexgen/X/18;
.super Ljava/lang/Object;
.source ""


# virtual methods
.method public abstract ACv(Lcom/facebook/ads/redexgen/X/b9;)V
.end method

.method public abstract ACw(Lcom/facebook/ads/redexgen/X/b9;)V
.end method

.method public abstract ACx(Lcom/facebook/ads/redexgen/X/b9;)V
.end method

.method public abstract ACy(Lcom/facebook/ads/redexgen/X/b9;)V
.end method

.method public abstract ACz(Lcom/facebook/ads/redexgen/X/b9;)V
.end method

.method public abstract AD0(Lcom/facebook/ads/redexgen/X/b9;)V
.end method

.method public abstract AD1(Lcom/facebook/ads/redexgen/X/b9;Lcom/facebook/ads/AdError;)V
.end method

.method public abstract onRewardedVideoActivityDestroyed()V
.end method

.method public abstract onRewardedVideoClosed()V
.end method
