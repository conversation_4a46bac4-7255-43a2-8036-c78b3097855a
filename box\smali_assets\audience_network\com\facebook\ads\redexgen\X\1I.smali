.class public final Lcom/facebook/ads/redexgen/X/1I;
.super Ljava/lang/Object;
.source ""

# interfaces
.implements Ljava/io/Serializable;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/facebook/ads/redexgen/X/1J;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "Builder"
.end annotation


# static fields
.field public static final serialVersionUID:J = -0x4a480b6eb5993653L


# instance fields
.field public A00:I

.field public A01:I

.field public A02:I

.field public A03:I

.field public A04:I

.field public A05:J

.field public A06:Lcom/facebook/ads/redexgen/X/1c;

.field public A07:Ljava/lang/String;

.field public A08:Ljava/lang/String;

.field public A09:Z

.field public A0A:Z

.field public A0B:Z


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 4122
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static synthetic A00(Lcom/facebook/ads/redexgen/X/1I;)I
    .locals 0

    .line 4123
    iget p0, p0, Lcom/facebook/ads/redexgen/X/1I;->A02:I

    return p0
.end method

.method public static synthetic A01(Lcom/facebook/ads/redexgen/X/1I;)I
    .locals 0

    .line 4124
    iget p0, p0, Lcom/facebook/ads/redexgen/X/1I;->A03:I

    return p0
.end method

.method public static synthetic A02(Lcom/facebook/ads/redexgen/X/1I;)I
    .locals 0

    .line 4125
    iget p0, p0, Lcom/facebook/ads/redexgen/X/1I;->A04:I

    return p0
.end method

.method public static synthetic A03(Lcom/facebook/ads/redexgen/X/1I;)I
    .locals 0

    .line 4126
    iget p0, p0, Lcom/facebook/ads/redexgen/X/1I;->A01:I

    return p0
.end method

.method public static synthetic A04(Lcom/facebook/ads/redexgen/X/1I;)I
    .locals 0

    .line 4127
    iget p0, p0, Lcom/facebook/ads/redexgen/X/1I;->A00:I

    return p0
.end method

.method public static synthetic A05(Lcom/facebook/ads/redexgen/X/1I;)J
    .locals 1

    .line 4128
    iget-wide v0, p0, Lcom/facebook/ads/redexgen/X/1I;->A05:J

    return-wide v0
.end method

.method public static synthetic A06(Lcom/facebook/ads/redexgen/X/1I;)Lcom/facebook/ads/redexgen/X/1c;
    .locals 0

    .line 4129
    iget-object p0, p0, Lcom/facebook/ads/redexgen/X/1I;->A06:Lcom/facebook/ads/redexgen/X/1c;

    return-object p0
.end method

.method public static synthetic A07(Lcom/facebook/ads/redexgen/X/1I;)Ljava/lang/String;
    .locals 0

    .line 4130
    iget-object p0, p0, Lcom/facebook/ads/redexgen/X/1I;->A08:Ljava/lang/String;

    return-object p0
.end method

.method public static synthetic A08(Lcom/facebook/ads/redexgen/X/1I;)Ljava/lang/String;
    .locals 0

    .line 4131
    iget-object p0, p0, Lcom/facebook/ads/redexgen/X/1I;->A07:Ljava/lang/String;

    return-object p0
.end method

.method public static synthetic A09(Lcom/facebook/ads/redexgen/X/1I;)Z
    .locals 0

    .line 4132
    iget-boolean p0, p0, Lcom/facebook/ads/redexgen/X/1I;->A0B:Z

    return p0
.end method

.method public static synthetic A0A(Lcom/facebook/ads/redexgen/X/1I;)Z
    .locals 0

    .line 4133
    iget-boolean p0, p0, Lcom/facebook/ads/redexgen/X/1I;->A0A:Z

    return p0
.end method

.method public static synthetic A0B(Lcom/facebook/ads/redexgen/X/1I;)Z
    .locals 0

    .line 4134
    iget-boolean p0, p0, Lcom/facebook/ads/redexgen/X/1I;->A09:Z

    return p0
.end method


# virtual methods
.method public final A0C(I)Lcom/facebook/ads/redexgen/X/1I;
    .locals 0

    .line 4135
    iput p1, p0, Lcom/facebook/ads/redexgen/X/1I;->A02:I

    .line 4136
    return-object p0
.end method

.method public final A0D(I)Lcom/facebook/ads/redexgen/X/1I;
    .locals 0

    .line 4137
    iput p1, p0, Lcom/facebook/ads/redexgen/X/1I;->A03:I

    .line 4138
    return-object p0
.end method

.method public final A0E(I)Lcom/facebook/ads/redexgen/X/1I;
    .locals 0

    .line 4139
    iput p1, p0, Lcom/facebook/ads/redexgen/X/1I;->A04:I

    .line 4140
    return-object p0
.end method

.method public final A0F(I)Lcom/facebook/ads/redexgen/X/1I;
    .locals 0

    .line 4141
    iput p1, p0, Lcom/facebook/ads/redexgen/X/1I;->A00:I

    .line 4142
    return-object p0
.end method

.method public final A0G(I)Lcom/facebook/ads/redexgen/X/1I;
    .locals 0

    .line 4143
    iput p1, p0, Lcom/facebook/ads/redexgen/X/1I;->A01:I

    .line 4144
    return-object p0
.end method

.method public final A0H(J)Lcom/facebook/ads/redexgen/X/1I;
    .locals 0

    .line 4145
    iput-wide p1, p0, Lcom/facebook/ads/redexgen/X/1I;->A05:J

    .line 4146
    return-object p0
.end method

.method public final A0I(Lcom/facebook/ads/redexgen/X/1c;)Lcom/facebook/ads/redexgen/X/1I;
    .locals 0

    .line 4147
    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/1I;->A06:Lcom/facebook/ads/redexgen/X/1c;

    .line 4148
    return-object p0
.end method

.method public final A0J(Ljava/lang/String;)Lcom/facebook/ads/redexgen/X/1I;
    .locals 0

    .line 4149
    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/1I;->A08:Ljava/lang/String;

    .line 4150
    return-object p0
.end method

.method public final A0K(Ljava/lang/String;)Lcom/facebook/ads/redexgen/X/1I;
    .locals 0

    .line 4151
    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/1I;->A07:Ljava/lang/String;

    .line 4152
    return-object p0
.end method

.method public final A0L(Z)Lcom/facebook/ads/redexgen/X/1I;
    .locals 0

    .line 4153
    iput-boolean p1, p0, Lcom/facebook/ads/redexgen/X/1I;->A09:Z

    .line 4154
    return-object p0
.end method

.method public final A0M(Z)Lcom/facebook/ads/redexgen/X/1I;
    .locals 0

    .line 4155
    iput-boolean p1, p0, Lcom/facebook/ads/redexgen/X/1I;->A0A:Z

    .line 4156
    return-object p0
.end method

.method public final A0N(Z)Lcom/facebook/ads/redexgen/X/1I;
    .locals 0

    .line 4157
    iput-boolean p1, p0, Lcom/facebook/ads/redexgen/X/1I;->A0B:Z

    .line 4158
    return-object p0
.end method

.method public final A0O()Lcom/facebook/ads/redexgen/X/1J;
    .locals 2

    .line 4159
    const/4 v1, 0x0

    new-instance v0, Lcom/facebook/ads/redexgen/X/1J;

    invoke-direct {v0, p0, v1}, Lcom/facebook/ads/redexgen/X/1J;-><init>(Lcom/facebook/ads/redexgen/X/1I;Lcom/facebook/ads/redexgen/X/1H;)V

    return-object v0
.end method
