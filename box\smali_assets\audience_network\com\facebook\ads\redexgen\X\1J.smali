.class public final Lcom/facebook/ads/redexgen/X/1J;
.super Ljava/lang/Object;
.source ""

# interfaces
.implements Ljava/io/Serializable;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/facebook/ads/redexgen/X/1I;
    }
.end annotation


# static fields
.field public static final serialVersionUID:J = -0x3ba6bcbd8943c62L


# instance fields
.field public final A00:I

.field public final A01:I

.field public final A02:I

.field public final A03:I

.field public final A04:I

.field public final A05:J

.field public final A06:Lcom/facebook/ads/redexgen/X/1c;

.field public final A07:Ljava/lang/String;

.field public final A08:Ljava/lang/String;

.field public final A09:Z

.field public final A0A:Z

.field public final A0B:Z


# direct methods
.method public constructor <init>(Lcom/facebook/ads/redexgen/X/1I;)V
    .locals 2

    .line 4160
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 4161
    invoke-static {p1}, Lcom/facebook/ads/redexgen/X/1I;->A07(Lcom/facebook/ads/redexgen/X/1I;)Ljava/lang/String;

    move-result-object v0

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/1J;->A08:Ljava/lang/String;

    .line 4162
    invoke-static {p1}, Lcom/facebook/ads/redexgen/X/1I;->A05(Lcom/facebook/ads/redexgen/X/1I;)J

    move-result-wide v0

    iput-wide v0, p0, Lcom/facebook/ads/redexgen/X/1J;->A05:J

    .line 4163
    invoke-static {p1}, Lcom/facebook/ads/redexgen/X/1I;->A01(Lcom/facebook/ads/redexgen/X/1I;)I

    move-result v0

    iput v0, p0, Lcom/facebook/ads/redexgen/X/1J;->A03:I

    .line 4164
    invoke-static {p1}, Lcom/facebook/ads/redexgen/X/1I;->A02(Lcom/facebook/ads/redexgen/X/1I;)I

    move-result v0

    iput v0, p0, Lcom/facebook/ads/redexgen/X/1J;->A04:I

    .line 4165
    invoke-static {p1}, Lcom/facebook/ads/redexgen/X/1I;->A0A(Lcom/facebook/ads/redexgen/X/1I;)Z

    move-result v0

    iput-boolean v0, p0, Lcom/facebook/ads/redexgen/X/1J;->A0A:Z

    .line 4166
    invoke-static {p1}, Lcom/facebook/ads/redexgen/X/1I;->A0B(Lcom/facebook/ads/redexgen/X/1I;)Z

    move-result v0

    iput-boolean v0, p0, Lcom/facebook/ads/redexgen/X/1J;->A09:Z

    .line 4167
    invoke-static {p1}, Lcom/facebook/ads/redexgen/X/1I;->A08(Lcom/facebook/ads/redexgen/X/1I;)Ljava/lang/String;

    move-result-object v0

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/1J;->A07:Ljava/lang/String;

    .line 4168
    invoke-static {p1}, Lcom/facebook/ads/redexgen/X/1I;->A03(Lcom/facebook/ads/redexgen/X/1I;)I

    move-result v0

    iput v0, p0, Lcom/facebook/ads/redexgen/X/1J;->A01:I

    .line 4169
    invoke-static {p1}, Lcom/facebook/ads/redexgen/X/1I;->A04(Lcom/facebook/ads/redexgen/X/1I;)I

    move-result v0

    iput v0, p0, Lcom/facebook/ads/redexgen/X/1J;->A00:I

    .line 4170
    invoke-static {p1}, Lcom/facebook/ads/redexgen/X/1I;->A06(Lcom/facebook/ads/redexgen/X/1I;)Lcom/facebook/ads/redexgen/X/1c;

    move-result-object v0

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/1J;->A06:Lcom/facebook/ads/redexgen/X/1c;

    .line 4171
    invoke-static {p1}, Lcom/facebook/ads/redexgen/X/1I;->A00(Lcom/facebook/ads/redexgen/X/1I;)I

    move-result v0

    iput v0, p0, Lcom/facebook/ads/redexgen/X/1J;->A02:I

    .line 4172
    invoke-static {p1}, Lcom/facebook/ads/redexgen/X/1I;->A09(Lcom/facebook/ads/redexgen/X/1I;)Z

    move-result v0

    iput-boolean v0, p0, Lcom/facebook/ads/redexgen/X/1J;->A0B:Z

    .line 4173
    return-void
.end method

.method public synthetic constructor <init>(Lcom/facebook/ads/redexgen/X/1I;Lcom/facebook/ads/redexgen/X/1H;)V
    .locals 0

    .line 4174
    invoke-direct {p0, p1}, Lcom/facebook/ads/redexgen/X/1J;-><init>(Lcom/facebook/ads/redexgen/X/1I;)V

    return-void
.end method


# virtual methods
.method public final A00()I
    .locals 1

    .line 4175
    iget v0, p0, Lcom/facebook/ads/redexgen/X/1J;->A00:I

    return v0
.end method

.method public final A01()I
    .locals 1

    .line 4176
    iget v0, p0, Lcom/facebook/ads/redexgen/X/1J;->A01:I

    return v0
.end method

.method public final A02()I
    .locals 1

    .line 4177
    iget v0, p0, Lcom/facebook/ads/redexgen/X/1J;->A02:I

    return v0
.end method

.method public final A03()I
    .locals 1

    .line 4178
    iget v0, p0, Lcom/facebook/ads/redexgen/X/1J;->A03:I

    return v0
.end method

.method public final A04()I
    .locals 1

    .line 4179
    iget v0, p0, Lcom/facebook/ads/redexgen/X/1J;->A04:I

    return v0
.end method

.method public final A05()J
    .locals 2

    .line 4180
    iget-wide v0, p0, Lcom/facebook/ads/redexgen/X/1J;->A05:J

    return-wide v0
.end method

.method public final A06()Lcom/facebook/ads/redexgen/X/1c;
    .locals 1

    .line 4181
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/1J;->A06:Lcom/facebook/ads/redexgen/X/1c;

    return-object v0
.end method

.method public final A07()Ljava/lang/String;
    .locals 1

    .line 4182
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/1J;->A07:Ljava/lang/String;

    return-object v0
.end method

.method public final A08()Ljava/lang/String;
    .locals 1

    .line 4183
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/1J;->A08:Ljava/lang/String;

    return-object v0
.end method

.method public final A09()Z
    .locals 1

    .line 4184
    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/1J;->A09:Z

    return v0
.end method

.method public final A0A()Z
    .locals 1

    .line 4185
    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/1J;->A0B:Z

    return v0
.end method
