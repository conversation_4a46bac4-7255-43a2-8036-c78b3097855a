.class public final Lcom/facebook/ads/redexgen/X/1M;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/facebook/ads/redexgen/X/1N;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "Builder"
.end annotation


# instance fields
.field public A00:Lcom/facebook/ads/redexgen/X/1L;

.field public A01:Ljava/lang/String;

.field public A02:Ljava/lang/String;

.field public A03:Ljava/lang/String;

.field public A04:Ljava/lang/String;

.field public A05:Ljava/lang/String;

.field public A06:Ljava/lang/String;

.field public A07:Ljava/lang/String;

.field public A08:Ljava/lang/String;


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 4195
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static synthetic A00(Lcom/facebook/ads/redexgen/X/1M;)Lcom/facebook/ads/redexgen/X/1L;
    .locals 0

    .line 4196
    iget-object p0, p0, Lcom/facebook/ads/redexgen/X/1M;->A00:Lcom/facebook/ads/redexgen/X/1L;

    return-object p0
.end method

.method public static synthetic A01(Lcom/facebook/ads/redexgen/X/1M;)Ljava/lang/String;
    .locals 0

    .line 4197
    iget-object p0, p0, Lcom/facebook/ads/redexgen/X/1M;->A08:Ljava/lang/String;

    return-object p0
.end method

.method public static synthetic A02(Lcom/facebook/ads/redexgen/X/1M;)Ljava/lang/String;
    .locals 0

    .line 4198
    iget-object p0, p0, Lcom/facebook/ads/redexgen/X/1M;->A07:Ljava/lang/String;

    return-object p0
.end method

.method public static synthetic A03(Lcom/facebook/ads/redexgen/X/1M;)Ljava/lang/String;
    .locals 0

    .line 4199
    iget-object p0, p0, Lcom/facebook/ads/redexgen/X/1M;->A02:Ljava/lang/String;

    return-object p0
.end method

.method public static synthetic A04(Lcom/facebook/ads/redexgen/X/1M;)Ljava/lang/String;
    .locals 0

    .line 4200
    iget-object p0, p0, Lcom/facebook/ads/redexgen/X/1M;->A05:Ljava/lang/String;

    return-object p0
.end method

.method public static synthetic A05(Lcom/facebook/ads/redexgen/X/1M;)Ljava/lang/String;
    .locals 0

    .line 4201
    iget-object p0, p0, Lcom/facebook/ads/redexgen/X/1M;->A04:Ljava/lang/String;

    return-object p0
.end method

.method public static synthetic A06(Lcom/facebook/ads/redexgen/X/1M;)Ljava/lang/String;
    .locals 0

    .line 4202
    iget-object p0, p0, Lcom/facebook/ads/redexgen/X/1M;->A01:Ljava/lang/String;

    return-object p0
.end method

.method public static synthetic A07(Lcom/facebook/ads/redexgen/X/1M;)Ljava/lang/String;
    .locals 0

    .line 4203
    iget-object p0, p0, Lcom/facebook/ads/redexgen/X/1M;->A03:Ljava/lang/String;

    return-object p0
.end method

.method public static synthetic A08(Lcom/facebook/ads/redexgen/X/1M;)Ljava/lang/String;
    .locals 0

    .line 4204
    iget-object p0, p0, Lcom/facebook/ads/redexgen/X/1M;->A06:Ljava/lang/String;

    return-object p0
.end method


# virtual methods
.method public final A09(Ljava/lang/String;)Lcom/facebook/ads/redexgen/X/1M;
    .locals 1

    .line 4205
    invoke-static {p1}, Lcom/facebook/ads/redexgen/X/1L;->A00(Ljava/lang/String;)Lcom/facebook/ads/redexgen/X/1L;

    move-result-object v0

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/1M;->A00:Lcom/facebook/ads/redexgen/X/1L;

    .line 4206
    return-object p0
.end method

.method public final A0A(Ljava/lang/String;)Lcom/facebook/ads/redexgen/X/1M;
    .locals 0

    .line 4207
    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/1M;->A01:Ljava/lang/String;

    .line 4208
    return-object p0
.end method

.method public final A0B(Ljava/lang/String;)Lcom/facebook/ads/redexgen/X/1M;
    .locals 0

    .line 4209
    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/1M;->A02:Ljava/lang/String;

    .line 4210
    return-object p0
.end method

.method public final A0C(Ljava/lang/String;)Lcom/facebook/ads/redexgen/X/1M;
    .locals 0

    .line 4211
    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/1M;->A03:Ljava/lang/String;

    .line 4212
    return-object p0
.end method

.method public final A0D(Ljava/lang/String;)Lcom/facebook/ads/redexgen/X/1M;
    .locals 0

    .line 4213
    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/1M;->A04:Ljava/lang/String;

    .line 4214
    return-object p0
.end method

.method public final A0E(Ljava/lang/String;)Lcom/facebook/ads/redexgen/X/1M;
    .locals 0

    .line 4215
    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/1M;->A05:Ljava/lang/String;

    .line 4216
    return-object p0
.end method

.method public final A0F(Ljava/lang/String;)Lcom/facebook/ads/redexgen/X/1M;
    .locals 0

    .line 4217
    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/1M;->A06:Ljava/lang/String;

    .line 4218
    return-object p0
.end method

.method public final A0G(Ljava/lang/String;)Lcom/facebook/ads/redexgen/X/1M;
    .locals 0

    .line 4219
    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/1M;->A07:Ljava/lang/String;

    .line 4220
    return-object p0
.end method

.method public final A0H(Ljava/lang/String;)Lcom/facebook/ads/redexgen/X/1M;
    .locals 0

    .line 4221
    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/1M;->A08:Ljava/lang/String;

    .line 4222
    return-object p0
.end method

.method public final A0I()Lcom/facebook/ads/redexgen/X/1N;
    .locals 2

    .line 4223
    const/4 v1, 0x0

    new-instance v0, Lcom/facebook/ads/redexgen/X/1N;

    invoke-direct {v0, p0, v1}, Lcom/facebook/ads/redexgen/X/1N;-><init>(Lcom/facebook/ads/redexgen/X/1M;Lcom/facebook/ads/redexgen/X/1K;)V

    return-object v0
.end method
