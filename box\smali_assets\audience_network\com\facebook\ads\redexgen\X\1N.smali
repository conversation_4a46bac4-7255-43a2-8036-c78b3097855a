.class public final Lcom/facebook/ads/redexgen/X/1N;
.super Ljava/lang/Object;
.source ""

# interfaces
.implements Ljava/io/Serializable;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/facebook/ads/redexgen/X/1M;,
        Lcom/facebook/ads/redexgen/X/1L;
    }
.end annotation


# static fields
.field public static final serialVersionUID:J = 0x49a3265cc5b0bddaL


# instance fields
.field public final A00:Lcom/facebook/ads/redexgen/X/1L;

.field public final A01:Ljava/lang/String;

.field public final A02:Ljava/lang/String;

.field public final A03:Ljava/lang/String;

.field public final A04:Ljava/lang/String;

.field public final A05:Ljava/lang/String;

.field public final A06:Ljava/lang/String;

.field public final A07:Ljava/lang/String;

.field public final A08:Ljava/lang/String;


# direct methods
.method public constructor <init>(Lcom/facebook/ads/redexgen/X/1M;)V
    .locals 1

    .line 4224
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 4225
    invoke-static {p1}, Lcom/facebook/ads/redexgen/X/1M;->A01(Lcom/facebook/ads/redexgen/X/1M;)Ljava/lang/String;

    move-result-object v0

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/1N;->A08:Ljava/lang/String;

    .line 4226
    invoke-static {p1}, Lcom/facebook/ads/redexgen/X/1M;->A02(Lcom/facebook/ads/redexgen/X/1M;)Ljava/lang/String;

    move-result-object v0

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/1N;->A07:Ljava/lang/String;

    .line 4227
    invoke-static {p1}, Lcom/facebook/ads/redexgen/X/1M;->A03(Lcom/facebook/ads/redexgen/X/1M;)Ljava/lang/String;

    move-result-object v0

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/1N;->A02:Ljava/lang/String;

    .line 4228
    invoke-static {p1}, Lcom/facebook/ads/redexgen/X/1M;->A04(Lcom/facebook/ads/redexgen/X/1M;)Ljava/lang/String;

    move-result-object v0

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/1N;->A05:Ljava/lang/String;

    .line 4229
    invoke-static {p1}, Lcom/facebook/ads/redexgen/X/1M;->A05(Lcom/facebook/ads/redexgen/X/1M;)Ljava/lang/String;

    move-result-object v0

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/1N;->A04:Ljava/lang/String;

    .line 4230
    invoke-static {p1}, Lcom/facebook/ads/redexgen/X/1M;->A06(Lcom/facebook/ads/redexgen/X/1M;)Ljava/lang/String;

    move-result-object v0

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/1N;->A01:Ljava/lang/String;

    .line 4231
    invoke-static {p1}, Lcom/facebook/ads/redexgen/X/1M;->A07(Lcom/facebook/ads/redexgen/X/1M;)Ljava/lang/String;

    move-result-object v0

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/1N;->A03:Ljava/lang/String;

    .line 4232
    invoke-static {p1}, Lcom/facebook/ads/redexgen/X/1M;->A08(Lcom/facebook/ads/redexgen/X/1M;)Ljava/lang/String;

    move-result-object v0

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/1N;->A06:Ljava/lang/String;

    .line 4233
    invoke-static {p1}, Lcom/facebook/ads/redexgen/X/1M;->A00(Lcom/facebook/ads/redexgen/X/1M;)Lcom/facebook/ads/redexgen/X/1L;

    move-result-object v0

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/1N;->A00:Lcom/facebook/ads/redexgen/X/1L;

    .line 4234
    return-void
.end method

.method public synthetic constructor <init>(Lcom/facebook/ads/redexgen/X/1M;Lcom/facebook/ads/redexgen/X/1K;)V
    .locals 0

    .line 4235
    invoke-direct {p0, p1}, Lcom/facebook/ads/redexgen/X/1N;-><init>(Lcom/facebook/ads/redexgen/X/1M;)V

    return-void
.end method


# virtual methods
.method public final A00()Lcom/facebook/ads/redexgen/X/1L;
    .locals 1

    .line 4236
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/1N;->A00:Lcom/facebook/ads/redexgen/X/1L;

    return-object v0
.end method

.method public final A01()Ljava/lang/String;
    .locals 1

    .line 4237
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/1N;->A02:Ljava/lang/String;

    return-object v0
.end method

.method public final A02()Ljava/lang/String;
    .locals 1

    .line 4238
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/1N;->A04:Ljava/lang/String;

    return-object v0
.end method

.method public final A03()Ljava/lang/String;
    .locals 1

    .line 4239
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/1N;->A05:Ljava/lang/String;

    return-object v0
.end method

.method public final A04()Ljava/lang/String;
    .locals 1

    .line 4240
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/1N;->A06:Ljava/lang/String;

    return-object v0
.end method

.method public final A05()Ljava/lang/String;
    .locals 1

    .line 4241
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/1N;->A07:Ljava/lang/String;

    return-object v0
.end method

.method public final A06()Ljava/lang/String;
    .locals 1

    .line 4242
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/1N;->A08:Ljava/lang/String;

    return-object v0
.end method
