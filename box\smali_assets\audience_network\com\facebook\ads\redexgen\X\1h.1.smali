.class public interface abstract Lcom/facebook/ads/redexgen/X/1h;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/facebook/ads/redexgen/X/1i;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "InterstitialAdapterDelegateListener"
.end annotation


# virtual methods
.method public abstract AAe(Lcom/facebook/ads/AdError;)V
.end method

.method public abstract AAf()V
.end method

.method public abstract AES()V
.end method

.method public abstract AGm()V
.end method
