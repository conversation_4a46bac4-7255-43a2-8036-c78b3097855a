.class public interface abstract Lcom/facebook/ads/redexgen/X/1q;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/facebook/ads/redexgen/X/1s;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "PlayablePreCacheListener"
.end annotation


# virtual methods
.method public abstract ACZ(Lcom/facebook/ads/AdError;)V
.end method

.method public abstract ACa()V
.end method
