.class public interface abstract Lcom/facebook/ads/redexgen/X/1y;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/facebook/ads/redexgen/X/ag;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "Listener"
.end annotation


# virtual methods
.method public abstract ABR(Lcom/facebook/ads/redexgen/X/Jb;)V
.end method

.method public abstract ACP(Ljava/util/List;)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lcom/facebook/ads/redexgen/X/bK;",
            ">;)V"
        }
    .end annotation
.end method
