.class public final enum Lcom/facebook/ads/redexgen/X/20;
.super Ljava/lang/Enum;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/facebook/ads/redexgen/X/21;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x4019
    name = "AdState"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Enum<",
        "Lcom/facebook/ads/redexgen/X/20;",
        ">;"
    }
.end annotation


# static fields
.field public static A00:[B

.field public static A01:[Ljava/lang/String;

.field public static final synthetic A02:[Lcom/facebook/ads/redexgen/X/20;

.field public static final enum A03:Lcom/facebook/ads/redexgen/X/20;

.field public static final enum A04:Lcom/facebook/ads/redexgen/X/20;

.field public static final enum A05:Lcom/facebook/ads/redexgen/X/20;

.field public static final enum A06:Lcom/facebook/ads/redexgen/X/20;

.field public static final enum A07:Lcom/facebook/ads/redexgen/X/20;

.field public static final enum A08:Lcom/facebook/ads/redexgen/X/20;

.field public static final enum A09:Lcom/facebook/ads/redexgen/X/20;


# direct methods
.method public static constructor <clinit>()V
    .locals 15

    .line 342
    const/16 v0, 0x8

    new-array v2, v0, [Ljava/lang/String;

    const/4 v1, 0x0

    const-string v0, "qS0jzzy053U49Su1PxhqxbWBiRBDhrlz"

    aput-object v0, v2, v1

    const/4 v1, 0x1

    const-string v0, "Xt2DRLlKFdB95NsINvyqeyDnMxOOuDLx"

    aput-object v0, v2, v1

    const/4 v1, 0x2

    const-string v0, "yf08nX9dIZfcKypKRld4zDkH94BCZAU7"

    aput-object v0, v2, v1

    const/4 v1, 0x3

    const-string v0, "tfbakkmOh5dxCJghzZjKfCWWzPpI"

    aput-object v0, v2, v1

    const/4 v1, 0x4

    const-string v0, "7IyHNSQ2TJBMgpMVEgH3C5YFRcP1ce7U"

    aput-object v0, v2, v1

    const/4 v1, 0x5

    const-string v0, "xEQSxTjWMbA9c974QplA5JGviijwmXcM"

    aput-object v0, v2, v1

    const/4 v1, 0x6

    const-string v0, "x69Rlf5MxzDhXyXzxEdLcnVcx3iMgH2Y"

    aput-object v0, v2, v1

    const/4 v1, 0x7

    const-string v0, "aVnA9Ux0owpYjW5hlcDkLBIQBKS6Kuco"

    aput-object v0, v2, v1

    sput-object v2, Lcom/facebook/ads/redexgen/X/20;->A01:[Ljava/lang/String;

    invoke-static {}, Lcom/facebook/ads/redexgen/X/20;->A01()V

    const/4 v2, 0x0

    const/4 v1, 0x7

    const/16 v0, 0x55

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/20;->A00(III)Ljava/lang/String;

    move-result-object v0

    const/4 v14, 0x0

    new-instance v13, Lcom/facebook/ads/redexgen/X/20;

    invoke-direct {v13, v0, v14}, Lcom/facebook/ads/redexgen/X/20;-><init>(Ljava/lang/String;I)V

    sput-object v13, Lcom/facebook/ads/redexgen/X/20;->A03:Lcom/facebook/ads/redexgen/X/20;

    .line 343
    const/16 v2, 0x1b

    const/4 v1, 0x7

    const/16 v0, 0x75

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/20;->A00(III)Ljava/lang/String;

    move-result-object v0

    const/4 v12, 0x1

    new-instance v11, Lcom/facebook/ads/redexgen/X/20;

    invoke-direct {v11, v0, v12}, Lcom/facebook/ads/redexgen/X/20;-><init>(Ljava/lang/String;I)V

    sput-object v11, Lcom/facebook/ads/redexgen/X/20;->A07:Lcom/facebook/ads/redexgen/X/20;

    .line 344
    const/16 v2, 0x15

    const/4 v1, 0x6

    const/16 v0, 0x38

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/20;->A00(III)Ljava/lang/String;

    move-result-object v0

    const/4 v10, 0x2

    new-instance v9, Lcom/facebook/ads/redexgen/X/20;

    invoke-direct {v9, v0, v10}, Lcom/facebook/ads/redexgen/X/20;-><init>(Ljava/lang/String;I)V

    sput-object v9, Lcom/facebook/ads/redexgen/X/20;->A06:Lcom/facebook/ads/redexgen/X/20;

    .line 345
    const/16 v2, 0x22

    const/4 v1, 0x7

    const/16 v0, 0xe

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/20;->A00(III)Ljava/lang/String;

    move-result-object v0

    const/4 v8, 0x3

    new-instance v7, Lcom/facebook/ads/redexgen/X/20;

    invoke-direct {v7, v0, v8}, Lcom/facebook/ads/redexgen/X/20;-><init>(Ljava/lang/String;I)V

    sput-object v7, Lcom/facebook/ads/redexgen/X/20;->A08:Lcom/facebook/ads/redexgen/X/20;

    .line 346
    const/16 v2, 0x29

    const/4 v1, 0x5

    const/16 v0, 0x78

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/20;->A00(III)Ljava/lang/String;

    move-result-object v0

    const/4 v6, 0x4

    new-instance v5, Lcom/facebook/ads/redexgen/X/20;

    invoke-direct {v5, v0, v6}, Lcom/facebook/ads/redexgen/X/20;-><init>(Ljava/lang/String;I)V

    sput-object v5, Lcom/facebook/ads/redexgen/X/20;->A09:Lcom/facebook/ads/redexgen/X/20;

    .line 347
    const/4 v2, 0x7

    const/16 v1, 0x9

    const/16 v0, 0x76

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/20;->A00(III)Ljava/lang/String;

    move-result-object v0

    const/4 v4, 0x5

    new-instance v3, Lcom/facebook/ads/redexgen/X/20;

    invoke-direct {v3, v0, v4}, Lcom/facebook/ads/redexgen/X/20;-><init>(Ljava/lang/String;I)V

    sput-object v3, Lcom/facebook/ads/redexgen/X/20;->A04:Lcom/facebook/ads/redexgen/X/20;

    .line 348
    const/16 v2, 0x10

    const/4 v1, 0x5

    const/16 v0, 0x23

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/20;->A00(III)Ljava/lang/String;

    move-result-object v0

    const/4 v2, 0x6

    new-instance v1, Lcom/facebook/ads/redexgen/X/20;

    invoke-direct {v1, v0, v2}, Lcom/facebook/ads/redexgen/X/20;-><init>(Ljava/lang/String;I)V

    sput-object v1, Lcom/facebook/ads/redexgen/X/20;->A05:Lcom/facebook/ads/redexgen/X/20;

    .line 349
    const/4 v0, 0x7

    new-array v0, v0, [Lcom/facebook/ads/redexgen/X/20;

    aput-object v13, v0, v14

    aput-object v11, v0, v12

    aput-object v9, v0, v10

    aput-object v7, v0, v8

    aput-object v5, v0, v6

    aput-object v3, v0, v4

    aput-object v1, v0, v2

    sput-object v0, Lcom/facebook/ads/redexgen/X/20;->A02:[Lcom/facebook/ads/redexgen/X/20;

    return-void
.end method

.method public constructor <init>(Ljava/lang/String;I)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation

    .line 4983
    invoke-direct {p0, p1, p2}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    return-void
.end method

.method public static A00(III)Ljava/lang/String;
    .locals 2

    sget-object v1, Lcom/facebook/ads/redexgen/X/20;->A00:[B

    add-int v0, p0, p1

    invoke-static {v1, p0, v0}, Ljava/util/Arrays;->copyOfRange([BII)[B

    move-result-object p0

    const/4 v1, 0x0

    :goto_0
    array-length v0, p0

    if-ge v1, v0, :cond_0

    aget-byte v0, p0, v1

    sub-int/2addr v0, p2

    add-int/lit8 v0, v0, -0xd

    int-to-byte v0, v0

    aput-byte v0, p0, v1

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_0
    new-instance v0, Ljava/lang/String;

    invoke-direct {v0, p0}, Ljava/lang/String;-><init>([B)V

    return-object v0
.end method

.method public static A01()V
    .locals 3

    const/16 v0, 0x2e

    new-array v0, v0, [B

    fill-array-data v0, :array_0

    sput-object v0, Lcom/facebook/ads/redexgen/X/20;->A00:[B

    sget-object v1, Lcom/facebook/ads/redexgen/X/20;->A01:[Ljava/lang/String;

    const/4 v0, 0x1

    aget-object v1, v1, v0

    const/16 v0, 0x19

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    const/16 v0, 0x4d

    if-eq v1, v0, :cond_0

    sget-object v2, Lcom/facebook/ads/redexgen/X/20;->A01:[Ljava/lang/String;

    const-string v1, "ORHYqq2J4PQNu3tNdzrtPOmYFobQNh7q"

    const/4 v0, 0x1

    aput-object v1, v2, v0

    return-void

    :cond_0
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    :array_0
    .array-data 1
        -0x5bt
        -0x4ct
        -0x59t
        -0x5dt
        -0x4at
        -0x59t
        -0x5at
        -0x39t
        -0x38t
        -0x2at
        -0x29t
        -0x2bt
        -0x2et
        -0x24t
        -0x38t
        -0x39t
        0x75t
        -0x7et
        -0x7et
        0x7ft
        -0x7et
        -0x6ft
        -0x6ct
        -0x7at
        -0x77t
        -0x76t
        -0x77t
        -0x32t
        -0x2ft
        -0x3dt
        -0x3at
        -0x35t
        -0x30t
        -0x37t
        0x6et
        0x63t
        0x6at
        0x72t
        0x64t
        0x69t
        0x62t
        -0x28t
        -0x33t
        -0x2ct
        -0x24t
        -0x2dt
    .end array-data
.end method

.method public static valueOf(Ljava/lang/String;)Lcom/facebook/ads/redexgen/X/20;
    .locals 1

    .line 4984
    const-class v0, Lcom/facebook/ads/redexgen/X/20;

    invoke-static {v0, p0}, Ljava/lang/Enum;->valueOf(Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Enum;

    move-result-object v0

    check-cast v0, Lcom/facebook/ads/redexgen/X/20;

    return-object v0
.end method

.method public static values()[Lcom/facebook/ads/redexgen/X/20;
    .locals 1

    .line 4985
    sget-object v0, Lcom/facebook/ads/redexgen/X/20;->A02:[Lcom/facebook/ads/redexgen/X/20;

    invoke-virtual {v0}, [Ljava/lang/Object;->clone()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [Lcom/facebook/ads/redexgen/X/20;

    return-object v0
.end method
