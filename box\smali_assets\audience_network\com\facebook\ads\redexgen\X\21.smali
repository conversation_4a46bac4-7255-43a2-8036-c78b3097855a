.class public interface abstract Lcom/facebook/ads/redexgen/X/21;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/facebook/ads/redexgen/X/20;
    }
.end annotation


# virtual methods
.method public abstract A5d()Z
.end method

.method public abstract A5e()Z
.end method

.method public abstract A5v()Lcom/facebook/ads/redexgen/X/20;
.end method

.method public abstract A5w()Lcom/facebook/ads/redexgen/X/20;
.end method

.method public abstract AFw(Lcom/facebook/ads/redexgen/X/20;)V
.end method

.method public abstract AG0(Lcom/facebook/ads/AdError;)V
.end method

.method public abstract AG5()V
.end method

.method public abstract AGF()V
.end method
