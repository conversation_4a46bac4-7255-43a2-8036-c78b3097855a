.class public interface abstract Lcom/facebook/ads/redexgen/X/27;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/facebook/ads/redexgen/X/FF;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "MessageHelperFactoryFactory"
.end annotation


# virtual methods
.method public abstract A4h(Lcom/facebook/ads/redexgen/X/26;)Lcom/facebook/ads/redexgen/X/aP;
.end method
