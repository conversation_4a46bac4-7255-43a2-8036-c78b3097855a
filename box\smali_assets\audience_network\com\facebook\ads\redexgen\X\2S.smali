.class public final Lcom/facebook/ads/redexgen/X/2S;
.super Ljava/lang/Object;
.source ""


# static fields
.field public static A01:[B

.field public static A02:[Ljava/lang/String;

.field public static final A03:[Ljava/lang/String;


# instance fields
.field public final A00:Lcom/facebook/ads/redexgen/X/2Y;


# direct methods
.method public static constructor <clinit>()V
    .locals 5

    .line 363
    const/16 v0, 0x8

    new-array v2, v0, [Ljava/lang/String;

    const/4 v1, 0x0

    const-string v0, "9okEwBsJnxm4XP2uMdCs1TTs0ipxbCrx"

    aput-object v0, v2, v1

    const/4 v1, 0x1

    const-string v0, "KwR5WWC5sufK43WzM862skA54ooPALBv"

    aput-object v0, v2, v1

    const/4 v1, 0x2

    const-string v0, "gy93sI55ZIYYvh5J8OUT9qm2p9"

    aput-object v0, v2, v1

    const/4 v1, 0x3

    const-string v0, "dxYBmhUa98SUIpbIHX6MiqZ5bWUPty4B"

    aput-object v0, v2, v1

    const/4 v1, 0x4

    const-string v0, "67xZp2aOxtSOd7RgrTL8HL2Ne7"

    aput-object v0, v2, v1

    const/4 v1, 0x5

    const-string v0, "07dtCbgrw0W8dRsWClXlOnRD4KzojNVd"

    aput-object v0, v2, v1

    const/4 v1, 0x6

    const-string v0, "fNc2y58JlCy0XZUWB5ryiRvO0DobwJiD"

    aput-object v0, v2, v1

    const/4 v1, 0x7

    const-string v0, "gLrzPSgUGRY8V9c6gRfYWq9AMfEa3UYZ"

    aput-object v0, v2, v1

    sput-object v2, Lcom/facebook/ads/redexgen/X/2S;->A02:[Ljava/lang/String;

    invoke-static {}, Lcom/facebook/ads/redexgen/X/2S;->A06()V

    const/16 v0, 0xf

    new-array v4, v0, [Ljava/lang/String;

    const/4 v3, 0x0

    const/16 v2, 0x1c4

    const/4 v1, 0x7

    const/16 v0, 0x14

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/2S;->A02(III)Ljava/lang/String;

    move-result-object v0

    aput-object v0, v4, v3

    const/4 v3, 0x1

    const/16 v2, 0x1cb

    const/16 v1, 0x13

    const/16 v0, 0x69

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/2S;->A02(III)Ljava/lang/String;

    move-result-object v0

    aput-object v0, v4, v3

    const/4 v3, 0x2

    const/16 v2, 0x1de

    const/16 v1, 0x19

    const/16 v0, 0x60

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/2S;->A02(III)Ljava/lang/String;

    move-result-object v0

    aput-object v0, v4, v3

    const/4 v3, 0x3

    const/16 v2, 0x1f7

    const/16 v1, 0xf

    const/16 v0, 0x1e

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/2S;->A02(III)Ljava/lang/String;

    move-result-object v0

    aput-object v0, v4, v3

    const/4 v3, 0x4

    const/16 v2, 0x265

    const/16 v1, 0x9

    const/16 v0, 0x63

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/2S;->A02(III)Ljava/lang/String;

    move-result-object v0

    aput-object v0, v4, v3

    const/4 v3, 0x5

    const/16 v2, 0x26e

    const/16 v1, 0x15

    const/16 v0, 0x8

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/2S;->A02(III)Ljava/lang/String;

    move-result-object v0

    aput-object v0, v4, v3

    const/4 v3, 0x6

    const/16 v2, 0x283

    const/16 v1, 0x1b

    const/16 v0, 0x44

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/2S;->A02(III)Ljava/lang/String;

    move-result-object v0

    aput-object v0, v4, v3

    const/4 v3, 0x7

    const/16 v2, 0x29e

    const/16 v1, 0x11

    const/16 v0, 0x35

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/2S;->A02(III)Ljava/lang/String;

    move-result-object v0

    aput-object v0, v4, v3

    const/16 v3, 0x8

    const/16 v2, 0x21c

    const/16 v1, 0x15

    const/16 v0, 0x17

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/2S;->A02(III)Ljava/lang/String;

    move-result-object v0

    aput-object v0, v4, v3

    const/16 v3, 0x9

    const/16 v2, 0x1a2

    const/16 v1, 0x10

    const/16 v0, 0x53

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/2S;->A02(III)Ljava/lang/String;

    move-result-object v0

    aput-object v0, v4, v3

    const/16 v3, 0xa

    const/16 v2, 0x1b2

    const/16 v1, 0x12

    const/16 v0, 0x5b

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/2S;->A02(III)Ljava/lang/String;

    move-result-object v0

    aput-object v0, v4, v3

    const/16 v3, 0xb

    const/16 v2, 0x18e

    const/16 v1, 0x14

    const/16 v0, 0x4e

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/2S;->A02(III)Ljava/lang/String;

    move-result-object v0

    aput-object v0, v4, v3

    const/16 v3, 0xc

    const/16 v2, 0x2b8

    const/16 v1, 0x14

    const/16 v0, 0x74

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/2S;->A02(III)Ljava/lang/String;

    move-result-object v0

    aput-object v0, v4, v3

    const/16 v3, 0xd

    const/16 v2, 0x160

    const/16 v1, 0xe

    const/16 v0, 0x2e

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/2S;->A02(III)Ljava/lang/String;

    move-result-object v0

    aput-object v0, v4, v3

    const/16 v3, 0xe

    const/16 v2, 0x231

    const/16 v1, 0x19

    const/16 v0, 0x70

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/2S;->A02(III)Ljava/lang/String;

    move-result-object v0

    aput-object v0, v4, v3

    sput-object v4, Lcom/facebook/ads/redexgen/X/2S;->A03:[Ljava/lang/String;

    return-void
.end method

.method public constructor <init>(Lcom/facebook/ads/redexgen/X/2Y;)V
    .locals 0

    .line 5497
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 5498
    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/2S;->A00:Lcom/facebook/ads/redexgen/X/2Y;

    .line 5499
    return-void
.end method

.method private A00()Ljava/lang/String;
    .locals 4

    .line 5500
    const/16 v2, 0x1de

    const/16 v1, 0x19

    const/16 v0, 0x60

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/2S;->A02(III)Ljava/lang/String;

    move-result-object v3

    const/16 v2, 0x3c

    const/16 v1, 0x45

    const/16 v0, 0x79

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/2S;->A02(III)Ljava/lang/String;

    move-result-object v0

    invoke-direct {p0, v3, v0}, Lcom/facebook/ads/redexgen/X/2S;->A03(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method private A01()Ljava/lang/String;
    .locals 4

    .line 5501
    const/16 v2, 0x283

    const/16 v1, 0x1b

    const/16 v0, 0x44

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/2S;->A02(III)Ljava/lang/String;

    move-result-object v3

    const/16 v2, 0x81

    const/16 v1, 0x40

    const/16 v0, 0x22

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/2S;->A02(III)Ljava/lang/String;

    move-result-object v0

    invoke-direct {p0, v3, v0}, Lcom/facebook/ads/redexgen/X/2S;->A03(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public static A02(III)Ljava/lang/String;
    .locals 2

    sget-object v1, Lcom/facebook/ads/redexgen/X/2S;->A01:[B

    add-int v0, p0, p1

    invoke-static {v1, p0, v0}, Ljava/util/Arrays;->copyOfRange([BII)[B

    move-result-object p0

    const/4 v1, 0x0

    :goto_0
    array-length v0, p0

    if-ge v1, v0, :cond_0

    aget-byte v0, p0, v1

    sub-int/2addr v0, p2

    add-int/lit8 v0, v0, -0x60

    int-to-byte v0, v0

    aput-byte v0, p0, v1

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_0
    new-instance v0, Ljava/lang/String;

    invoke-direct {v0, p0}, Ljava/lang/String;-><init>([B)V

    return-object v0
.end method

.method private A03(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;
    .locals 4

    .line 5502
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/2S;->A00:Lcom/facebook/ads/redexgen/X/2Y;

    invoke-interface {v0, p1, p2}, Lcom/facebook/ads/redexgen/X/2Y;->A85(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v3

    .line 5503
    .local v0, "value":Ljava/lang/String;
    if-eqz v3, :cond_0

    const/16 v2, 0x24a

    const/4 v1, 0x4

    const/16 v0, 0x27

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/2S;->A02(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v3, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_1

    :cond_0
    :goto_0
    return-object p2

    :cond_1
    move-object p2, v3

    goto :goto_0
.end method

.method public static A04(Ljava/lang/String;)Ljava/util/List;
    .locals 9
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            ")",
            "Ljava/util/List<",
            "Lcom/facebook/ads/redexgen/X/2W;",
            ">;"
        }
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lorg/json/JSONException;
        }
    .end annotation

    .line 5504
    invoke-static {p0}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    if-nez v0, :cond_0

    const/16 v2, 0x24a

    const/4 v1, 0x4

    const/16 v0, 0x27

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/2S;->A02(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p0, v0}, Ljava/lang/String;->equalsIgnoreCase(Ljava/lang/String;)Z

    move-result v0

    if-eqz v0, :cond_1

    .line 5505
    .end local v0
    .end local v1
    :cond_0
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    return-object v0

    .line 5506
    :cond_1
    new-instance v4, Lorg/json/JSONArray;

    invoke-direct {v4, p0}, Lorg/json/JSONArray;-><init>(Ljava/lang/String;)V

    .line 5507
    .local v0, "optionsArray":Lorg/json/JSONArray;
    new-instance v5, Ljava/util/ArrayList;

    invoke-direct {v5}, Ljava/util/ArrayList;-><init>()V

    .line 5508
    .local v1, "reasonsList":Ljava/util/List;, "Ljava/util/List<Lcom/facebook/ads/internal/adreportingconfig/AdReportingReason;>;"
    const/4 v3, 0x0

    .local v2, "i":I
    :goto_0
    invoke-virtual {v4}, Lorg/json/JSONArray;->length()I

    move-result v0

    if-ge v3, v0, :cond_4

    .line 5509
    invoke-virtual {v4, v3}, Lorg/json/JSONArray;->get(I)Ljava/lang/Object;

    move-result-object v8

    check-cast v8, Lorg/json/JSONObject;

    .line 5510
    .local v3, "option":Lorg/json/JSONObject;
    const/16 v2, 0x259

    const/16 v1, 0xc

    const/16 v0, 0x7b

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/2S;->A02(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v8, v0}, Lorg/json/JSONObject;->getInt(Ljava/lang/String;)I

    move-result p0

    .line 5511
    const/16 v2, 0x24e

    const/16 v1, 0xb

    const/16 v0, 0x21

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/2S;->A02(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v8, v0}, Lorg/json/JSONObject;->getString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v7

    .line 5512
    const/16 v2, 0x16e

    const/16 v1, 0x10

    const/16 v0, 0x11

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/2S;->A02(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v8, v0}, Lorg/json/JSONObject;->optString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    new-instance v6, Lcom/facebook/ads/redexgen/X/2W;

    invoke-direct {v6, p0, v7, v0}, Lcom/facebook/ads/redexgen/X/2W;-><init>(ILjava/lang/String;Ljava/lang/String;)V

    .line 5513
    .local v4, "reason":Lcom/facebook/ads/redexgen/X/2W;
    const/16 v2, 0x17e

    const/16 v1, 0x10

    const/16 v0, 0x64

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/2S;->A02(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v8, v0}, Lorg/json/JSONObject;->optString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/2S;->A04(Ljava/lang/String;)Ljava/util/List;

    move-result-object v0

    .line 5514
    .local v5, "children":Ljava/util/List;, "Ljava/util/List<Lcom/facebook/ads/internal/adreportingconfig/AdReportingReason;>;"
    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :goto_1
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_2

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/facebook/ads/redexgen/X/2W;

    .line 5515
    .local v7, "child":Lcom/facebook/ads/redexgen/X/2W;
    invoke-virtual {v6, v0}, Lcom/facebook/ads/redexgen/X/2W;->A06(Lcom/facebook/ads/redexgen/X/2W;)V

    .line 5516
    .end local v7    # "child":Lcom/facebook/ads/redexgen/X/2W;
    goto :goto_1

    .line 5517
    :cond_2
    invoke-interface {v5, v6}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    sget-object v1, Lcom/facebook/ads/redexgen/X/2S;->A02:[Ljava/lang/String;

    const/4 v0, 0x0

    aget-object v1, v1, v0

    const/16 v0, 0x8

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    const/16 v0, 0x44

    if-eq v1, v0, :cond_3

    .line 5518
    .end local v3    # "option":Lorg/json/JSONObject;
    .end local v4    # "reason":Lcom/facebook/ads/redexgen/X/2W;
    .end local v5    # "children":Ljava/util/List;, "Ljava/util/List<Lcom/facebook/ads/internal/adreportingconfig/AdReportingReason;>;"
    sget-object v2, Lcom/facebook/ads/redexgen/X/2S;->A02:[Ljava/lang/String;

    const-string v1, "UoOnH9Hv65m0hYQCpY1Dh"

    const/4 v0, 0x1

    aput-object v1, v2, v0

    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    :cond_3
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    .line 5519
    .end local v2    # "i":I
    :cond_4
    return-object v5
.end method

.method public static A05(Ljava/lang/String;)Ljava/util/Map;
    .locals 8
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            ")",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    .line 5520
    const/4 v7, 0x0

    if-eqz p0, :cond_0

    invoke-virtual {p0}, Ljava/lang/String;->isEmpty()Z

    move-result v0

    if-nez v0, :cond_0

    const/16 v2, 0x15e

    const/4 v1, 0x2

    const/16 v0, 0x41

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/2S;->A02(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p0, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_1

    .line 5521
    .end local v1
    .end local v2
    :cond_0
    return-object v7

    .line 5522
    :cond_1
    new-instance v3, Ljava/util/HashMap;

    invoke-direct {v3}, Ljava/util/HashMap;-><init>()V

    .line 5523
    .local v1, "sanitisedInput":Ljava/util/Map;, "Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"
    :try_start_0
    new-instance v4, Lorg/json/JSONObject;

    invoke-direct {v4, p0}, Lorg/json/JSONObject;-><init>(Ljava/lang/String;)V

    .line 5524
    .local v2, "json":Lorg/json/JSONObject;
    sget-object v6, Lcom/facebook/ads/redexgen/X/2S;->A03:[Ljava/lang/String;

    array-length v5, v6

    const/4 v2, 0x0

    :goto_0
    if-ge v2, v5, :cond_3

    aget-object v1, v6, v2

    .line 5525
    .local v6, "key":Ljava/lang/String;
    invoke-virtual {v4, v1}, Lorg/json/JSONObject;->has(Ljava/lang/String;)Z

    move-result v0

    if-eqz v0, :cond_2

    .line 5526
    invoke-virtual {v4, v1}, Lorg/json/JSONObject;->getString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    invoke-interface {v3, v1, v0}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 5527
    .end local v6    # "key":Ljava/lang/String;
    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    .line 5528
    .restart local v6    # "key":Ljava/lang/String;
    :cond_2
    return-object v7

    .line 5529
    .end local v6    # "key":Ljava/lang/String;
    :cond_3
    const/16 v2, 0x29e

    const/16 v1, 0x11

    const/16 v0, 0x35

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/2S;->A02(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v4, v0}, Lorg/json/JSONObject;->getString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/2S;->A04(Ljava/lang/String;)Ljava/util/List;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    if-nez v0, :cond_4

    .line 5530
    sget v3, Lcom/facebook/ads/redexgen/X/8A;->A28:I

    const/16 v2, 0xef

    const/16 v1, 0x14

    const/16 v0, 0x65

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/2S;->A02(III)Ljava/lang/String;

    move-result-object v0

    new-instance v1, Lcom/facebook/ads/redexgen/X/8B;

    invoke-direct {v1, v0}, Lcom/facebook/ads/redexgen/X/8B;-><init>(Ljava/lang/String;)V

    invoke-static {v3, v1}, Lcom/facebook/ads/redexgen/X/2S;->A08(ILcom/facebook/ads/redexgen/X/8B;)V

    .line 5531
    return-object v7

    .line 5532
    :cond_4
    const/16 v2, 0x1f7

    const/16 v1, 0xf

    const/16 v0, 0x1e

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/2S;->A02(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v4, v0}, Lorg/json/JSONObject;->getString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/2S;->A04(Ljava/lang/String;)Ljava/util/List;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    if-nez v0, :cond_5

    .line 5533
    sget v3, Lcom/facebook/ads/redexgen/X/8A;->A25:I

    const/16 v2, 0xdd

    const/16 v1, 0x12

    const/16 v0, 0xa

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/2S;->A02(III)Ljava/lang/String;

    move-result-object v0

    new-instance v1, Lcom/facebook/ads/redexgen/X/8B;

    invoke-direct {v1, v0}, Lcom/facebook/ads/redexgen/X/8B;-><init>(Ljava/lang/String;)V

    invoke-static {v3, v1}, Lcom/facebook/ads/redexgen/X/2S;->A08(ILcom/facebook/ads/redexgen/X/8B;)V

    .line 5534
    return-object v7

    .line 5535
    :cond_5
    return-object v3
    :try_end_0
    .catch Lorg/json/JSONException; {:try_start_0 .. :try_end_0} :catch_0

    .line 5536
    .end local v2    # "json":Lorg/json/JSONObject;
    :catch_0
    move-exception v2

    .line 5537
    .local v2, "e":Lorg/json/JSONException;
    sget v1, Lcom/facebook/ads/redexgen/X/8A;->A27:I

    new-instance v0, Lcom/facebook/ads/redexgen/X/8B;

    invoke-direct {v0, v2}, Lcom/facebook/ads/redexgen/X/8B;-><init>(Ljava/lang/Throwable;)V

    invoke-static {v1, v0}, Lcom/facebook/ads/redexgen/X/2S;->A08(ILcom/facebook/ads/redexgen/X/8B;)V

    .line 5538
    return-object v7
.end method

.method public static A06()V
    .locals 1

    const/16 v0, 0x2cc

    new-array v0, v0, [B

    fill-array-data v0, :array_0

    sput-object v0, Lcom/facebook/ads/redexgen/X/2S;->A01:[B

    return-void

    :array_0
    .array-data 1
        -0x5at
        -0x2dt
        -0x19t
        -0x8t
        -0xft
        -0x5at
        -0x19t
        -0x16t
        -0x5at
        -0x19t
        -0x7t
        -0x5at
        -0xbt
        -0x14t
        -0x14t
        -0x15t
        -0xct
        -0x7t
        -0x11t
        -0x4t
        -0x15t
        -0x5at
        -0xbt
        -0x8t
        -0x5at
        -0x11t
        -0xct
        -0x19t
        -0xat
        -0xat
        -0x8t
        -0xbt
        -0xat
        -0x8t
        -0x11t
        -0x19t
        -0x6t
        -0x15t
        -0x45t
        -0x22t
        -0x66t
        -0x1et
        -0x1dt
        -0x22t
        -0x22t
        -0x21t
        -0x18t
        -0x58t
        -0x56t
        -0x33t
        -0x77t
        -0x25t
        -0x32t
        -0x27t
        -0x28t
        -0x25t
        -0x23t
        -0x32t
        -0x33t
        -0x69t
        0x21t
        0x3et
        0x45t
        0x49t
        -0x7t
        0x4et
        0x4ct
        -0x7t
        0x4et
        0x47t
        0x3dt
        0x3et
        0x4bt
        0x4ct
        0x4dt
        0x3at
        0x47t
        0x3dt
        -0x7t
        0x50t
        0x41t
        0x3at
        0x4dt
        -0x7t
        0x42t
        0x4ct
        -0x7t
        0x41t
        0x3at
        0x49t
        0x49t
        0x3et
        0x47t
        0x42t
        0x47t
        0x40t
        0x7t
        -0x7t
        0x30t
        0x41t
        0x52t
        -0x7t
        0x3dt
        0x48t
        0x47t
        0x0t
        0x4dt
        -0x7t
        0x52t
        0x48t
        0x4et
        -0x7t
        0x50t
        0x3at
        0x47t
        0x4dt
        -0x7t
        0x4dt
        0x48t
        -0x7t
        0x4ct
        0x3et
        0x3et
        -0x7t
        0x4dt
        0x41t
        0x42t
        0x4ct
        0x18t
        -0x36t
        -0x19t
        -0x12t
        -0xet
        -0x5et
        -0x9t
        -0xbt
        -0x5et
        -0x9t
        -0x10t
        -0x1at
        -0x19t
        -0xct
        -0xbt
        -0xat
        -0x1dt
        -0x10t
        -0x1at
        -0x5et
        -0x7t
        -0x16t
        -0x1dt
        -0xat
        -0x5et
        -0x15t
        -0xbt
        -0x5et
        -0x16t
        -0x1dt
        -0xet
        -0xet
        -0x19t
        -0x10t
        -0x15t
        -0x10t
        -0x17t
        -0x50t
        -0x5et
        -0x27t
        -0x16t
        -0x5t
        -0x5et
        -0x15t
        -0xbt
        -0x5et
        -0xat
        -0x16t
        -0x15t
        -0xbt
        -0x5et
        -0x15t
        -0x10t
        -0x1dt
        -0xet
        -0xet
        -0xct
        -0xft
        -0xet
        -0xct
        -0x15t
        -0x1dt
        -0xat
        -0x19t
        -0x3ft
        -0x3dt
        -0x1ct
        -0x21t
        -0x20t
        -0x65t
        -0x44t
        -0x21t
        0x8t
        0x1ct
        0x29t
        0x1ct
        0x22t
        0x20t
        -0x25t
        0x1ct
        0x1ft
        -0x25t
        0x2bt
        0x2dt
        0x20t
        0x21t
        0x20t
        0x2dt
        0x20t
        0x29t
        0x1et
        0x20t
        0x2et
        -0x48t
        -0x27t
        -0x76t
        -0x2et
        -0x2dt
        -0x32t
        -0x31t
        -0x76t
        -0x35t
        -0x32t
        -0x76t
        -0x27t
        -0x26t
        -0x22t
        -0x2dt
        -0x27t
        -0x28t
        -0x23t
        0x13t
        0x34t
        -0x1bt
        0x37t
        0x2at
        0x35t
        0x34t
        0x37t
        0x39t
        -0x1bt
        0x26t
        0x29t
        -0x1bt
        0x34t
        0x35t
        0x39t
        0x2et
        0x34t
        0x33t
        0x38t
        -0x25t
        -0x12t
        -0x7t
        -0x8t
        -0x5t
        -0x3t
        -0x57t
        -0x36t
        -0x13t
        -0x12t
        0x0t
        0x0t
        -0x45t
        0x1t
        0x0t
        0x12t
        0x0t
        0xdt
        -0x45t
        -0x4t
        -0x1t
        0xet
        -0x45t
        0x7t
        0x4t
        0x6t
        0x0t
        -0x45t
        0xft
        0x3t
        0x4t
        0xet
        -0x38t
        -0x27t
        -0x16t
        -0x6ft
        -0x2et
        -0x22t
        -0x6ft
        -0x46t
        -0x6ft
        -0x1ct
        -0x2at
        -0x2at
        -0x26t
        -0x21t
        -0x28t
        -0x6ft
        -0x1bt
        -0x27t
        -0x26t
        -0x1ct
        -0x50t
        0x0t
        0x16t
        0x1ct
        0x19t
        -0x39t
        0x1at
        0x1ct
        0x9t
        0x14t
        0x10t
        0x1at
        0x1at
        0x10t
        0x16t
        0x15t
        -0x39t
        0x10t
        0x1at
        -0x39t
        0x15t
        0x16t
        0x1et
        -0x39t
        0x9t
        0xct
        0x10t
        0x15t
        0xet
        -0x39t
        0x19t
        0xct
        0x1dt
        0x10t
        0xct
        0x1et
        0xct
        0xbt
        -0x2bt
        -0x4t
        -0x2t
        -0x11t
        -0xet
        -0x13t
        -0xft
        -0xat
        -0x3t
        -0x9t
        -0xft
        -0xdt
        0x1t
        -0x13t
        0x3t
        0x0t
        -0x9t
        -0x2ct
        -0x27t
        -0x26t
        -0x23t
        -0x2bt
        -0x1dt
        -0x2at
        -0x21t
        -0x30t
        -0x27t
        -0x2at
        -0x2et
        -0x2bt
        -0x26t
        -0x21t
        -0x28t
        0x27t
        0x2ct
        0x2dt
        0x30t
        0x28t
        0x36t
        0x29t
        0x32t
        0x23t
        0x33t
        0x34t
        0x38t
        0x2dt
        0x33t
        0x32t
        0x37t
        0x14t
        0x17t
        0x1ct
        0x17t
        0x21t
        0x16t
        0x13t
        0x12t
        0xdt
        0x12t
        0x13t
        0x21t
        0x11t
        0x20t
        0x17t
        0x1et
        0x22t
        0x17t
        0x1dt
        0x1ct
        0x19t
        0x1ct
        0x21t
        0x1ct
        0x26t
        0x1bt
        0x18t
        0x17t
        0x12t
        0x1bt
        0x1ct
        0x17t
        0x18t
        0x12t
        0x14t
        0x17t
        0x21t
        0x24t
        0x29t
        0x24t
        0x2et
        0x23t
        0x20t
        0x1ft
        0x1at
        0x2dt
        0x20t
        0x2bt
        0x2at
        0x2dt
        0x2ft
        0x1at
        0x1ct
        0x1ft
        -0x24t
        -0x23t
        -0x28t
        -0x27t
        -0x2dt
        -0x2bt
        -0x28t
        0x31t
        0x32t
        0x2dt
        0x2et
        0x28t
        0x2at
        0x2dt
        0x28t
        0x2dt
        0x2et
        0x3ct
        0x2ct
        0x3bt
        0x32t
        0x39t
        0x3dt
        0x32t
        0x38t
        0x37t
        0x28t
        0x29t
        0x24t
        0x25t
        0x1ft
        0x21t
        0x24t
        0x1ft
        0x26t
        0x2ft
        0x2ct
        0x2ct
        0x2ft
        0x37t
        0x1ft
        0x35t
        0x30t
        0x1ft
        0x28t
        0x25t
        0x21t
        0x24t
        0x29t
        0x2et
        0x27t
        -0x1at
        -0x19t
        -0x1et
        -0x1dt
        -0x23t
        -0x21t
        -0x1et
        -0x23t
        -0x13t
        -0x12t
        -0xet
        -0x19t
        -0x13t
        -0x14t
        -0xft
        0x44t
        0x39t
        0x4bt
        0x4ct
        0x37t
        0x4dt
        0x48t
        0x3ct
        0x39t
        0x4ct
        0x3dt
        0x3ct
        0x37t
        0x4ct
        0x41t
        0x45t
        0x3dt
        0x4bt
        0x4ct
        0x39t
        0x45t
        0x48t
        -0x1ct
        -0x28t
        -0x1bt
        -0x28t
        -0x22t
        -0x24t
        -0x2at
        -0x28t
        -0x25t
        -0x2at
        -0x19t
        -0x17t
        -0x24t
        -0x23t
        -0x24t
        -0x17t
        -0x24t
        -0x1bt
        -0x26t
        -0x24t
        -0x16t
        0x3dt
        0x31t
        0x3et
        0x31t
        0x37t
        0x35t
        0x2ft
        0x31t
        0x34t
        0x2ft
        0x40t
        0x42t
        0x35t
        0x36t
        0x35t
        0x42t
        0x35t
        0x3et
        0x33t
        0x35t
        0x43t
        0x2ft
        0x45t
        0x42t
        0x39t
        -0xbt
        -0x4t
        -0xdt
        -0xdt
        -0x10t
        -0xft
        -0xbt
        -0x16t
        -0x10t
        -0x11t
        -0x20t
        -0xbt
        -0x1at
        -0x7t
        -0xbt
        0x4at
        0x4bt
        0x4ft
        0x44t
        0x4at
        0x49t
        0x3at
        0x51t
        0x3ct
        0x47t
        0x50t
        0x40t
        0x35t
        0x28t
        0x33t
        0x32t
        0x35t
        0x37t
        0x22t
        0x24t
        0x27t
        -0x26t
        -0x33t
        -0x28t
        -0x29t
        -0x26t
        -0x24t
        -0x39t
        -0x37t
        -0x34t
        -0x39t
        -0x34t
        -0x33t
        -0x25t
        -0x35t
        -0x26t
        -0x2ft
        -0x28t
        -0x24t
        -0x2ft
        -0x29t
        -0x2at
        0x16t
        0x9t
        0x14t
        0x13t
        0x16t
        0x18t
        0x3t
        0x5t
        0x8t
        0x3t
        0xat
        0x13t
        0x10t
        0x10t
        0x13t
        0x1bt
        0x3t
        0x19t
        0x14t
        0x3t
        0xct
        0x9t
        0x5t
        0x8t
        0xdt
        0x12t
        0xbt
        0x7t
        -0x6t
        0x5t
        0x4t
        0x7t
        0x9t
        -0xct
        -0xat
        -0x7t
        -0xct
        0x4t
        0x5t
        0x9t
        -0x2t
        0x4t
        0x3t
        0x8t
        -0x29t
        -0x36t
        -0x2bt
        -0x2ct
        -0x29t
        -0x27t
        -0x32t
        -0x2dt
        -0x34t
        0x4bt
        0x3ct
        0x4dt
        0x33t
        0x35t
        0x41t
        0x33t
        0x3dt
        0x33t
        0x47t
        0x39t
        0x39t
        0x3dt
        0x42t
        0x3bt
        0x33t
        0x48t
        0x3ct
        0x3dt
        0x47t
    .end array-data
.end method

.method private final A07()V
    .locals 4

    .line 5539
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/2S;->A00:Lcom/facebook/ads/redexgen/X/2Y;

    invoke-interface {v0}, Lcom/facebook/ads/redexgen/X/2Y;->A5S()Lcom/facebook/ads/redexgen/X/aD;

    move-result-object v3

    .line 5540
    .local v0, "editor":Lcom/facebook/ads/redexgen/X/2X;
    const/16 v2, 0x206

    const/16 v1, 0x16

    const/16 v0, 0x78

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/2S;->A02(III)Ljava/lang/String;

    move-result-object v2

    const-wide/16 v0, 0x0

    invoke-virtual {v3, v2, v0, v1}, Lcom/facebook/ads/redexgen/X/aD;->A00(Ljava/lang/String;J)Lcom/facebook/ads/redexgen/X/aD;

    .line 5541
    invoke-virtual {v3}, Lcom/facebook/ads/redexgen/X/aD;->A02()V

    .line 5542
    return-void
.end method

.method public static A08(ILcom/facebook/ads/redexgen/X/8B;)V
    .locals 4

    .line 5543
    invoke-static {}, Lcom/facebook/ads/redexgen/X/7e;->A00()Lcom/facebook/ads/redexgen/X/Ym;

    move-result-object v0

    .line 5544
    .local v0, "sdkContext":Lcom/facebook/ads/redexgen/X/Ym;
    if-eqz v0, :cond_0

    .line 5545
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/7f;->A07()Lcom/facebook/ads/redexgen/X/89;

    move-result-object v3

    const/16 v2, 0x2af

    const/16 v1, 0x9

    const/4 v0, 0x5

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/2S;->A02(III)Ljava/lang/String;

    move-result-object v0

    invoke-interface {v3, v0, p0, p1}, Lcom/facebook/ads/redexgen/X/89;->A9a(Ljava/lang/String;ILcom/facebook/ads/redexgen/X/8B;)V

    .line 5546
    :cond_0
    return-void
.end method


# virtual methods
.method public final A09()J
    .locals 4

    .line 5547
    iget-object v3, p0, Lcom/facebook/ads/redexgen/X/2S;->A00:Lcom/facebook/ads/redexgen/X/2Y;

    const/16 v2, 0x206

    const/16 v1, 0x16

    const/16 v0, 0x78

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/2S;->A02(III)Ljava/lang/String;

    move-result-object v2

    const-wide/16 v0, 0x0

    invoke-interface {v3, v2, v0, v1}, Lcom/facebook/ads/redexgen/X/2Y;->A7K(Ljava/lang/String;J)J

    move-result-wide v0

    return-wide v0
.end method

.method public final A0A()Lcom/facebook/ads/redexgen/X/2W;
    .locals 5

    .line 5548
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/2S;->A00()Ljava/lang/String;

    move-result-object v0

    new-instance v4, Lcom/facebook/ads/redexgen/X/2W;

    invoke-direct {v4, v0}, Lcom/facebook/ads/redexgen/X/2W;-><init>(Ljava/lang/String;)V

    .line 5549
    .local v0, "hideAdParent":Lcom/facebook/ads/redexgen/X/2W;
    :try_start_0
    const/16 v2, 0x1f7

    const/16 v1, 0xf

    const/16 v0, 0x1e

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/2S;->A02(III)Ljava/lang/String;

    move-result-object v1

    const/4 v3, 0x0

    const/4 v2, 0x0

    const/16 v0, 0x5e

    invoke-static {v3, v2, v0}, Lcom/facebook/ads/redexgen/X/2S;->A02(III)Ljava/lang/String;

    move-result-object v0

    invoke-direct {p0, v1, v0}, Lcom/facebook/ads/redexgen/X/2S;->A03(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/2S;->A04(Ljava/lang/String;)Ljava/util/List;

    move-result-object v0

    .line 5550
    .local v1, "children":Ljava/util/List;, "Ljava/util/List<Lcom/facebook/ads/internal/adreportingconfig/AdReportingReason;>;"
    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :goto_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_0

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/facebook/ads/redexgen/X/2W;

    .line 5551
    .local v3, "child":Lcom/facebook/ads/redexgen/X/2W;
    invoke-virtual {v4, v0}, Lcom/facebook/ads/redexgen/X/2W;->A06(Lcom/facebook/ads/redexgen/X/2W;)V

    goto :goto_0
    :try_end_0
    .catch Lorg/json/JSONException; {:try_start_0 .. :try_end_0} :catch_0

    .line 5552
    .end local v1    # "children":Ljava/util/List;, "Ljava/util/List<Lcom/facebook/ads/internal/adreportingconfig/AdReportingReason;>;"
    :catch_0
    move-exception v2

    .line 5553
    .local v1, "e":Lorg/json/JSONException;
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/2S;->A07()V

    .line 5554
    sget v1, Lcom/facebook/ads/redexgen/X/8A;->A26:I

    new-instance v0, Lcom/facebook/ads/redexgen/X/8B;

    invoke-direct {v0, v2}, Lcom/facebook/ads/redexgen/X/8B;-><init>(Ljava/lang/Throwable;)V

    invoke-static {v1, v0}, Lcom/facebook/ads/redexgen/X/2S;->A08(ILcom/facebook/ads/redexgen/X/8B;)V

    .line 5555
    .end local v1    # "e":Lorg/json/JSONException;
    :cond_0
    return-object v4
.end method

.method public final A0B()Lcom/facebook/ads/redexgen/X/2W;
    .locals 5

    .line 5556
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/2S;->A01()Ljava/lang/String;

    move-result-object v0

    new-instance v4, Lcom/facebook/ads/redexgen/X/2W;

    invoke-direct {v4, v0}, Lcom/facebook/ads/redexgen/X/2W;-><init>(Ljava/lang/String;)V

    .line 5557
    .local v0, "reportAdParent":Lcom/facebook/ads/redexgen/X/2W;
    :try_start_0
    const/16 v2, 0x29e

    const/16 v1, 0x11

    const/16 v0, 0x35

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/2S;->A02(III)Ljava/lang/String;

    move-result-object v1

    const/4 v3, 0x0

    const/4 v2, 0x0

    const/16 v0, 0x5e

    invoke-static {v3, v2, v0}, Lcom/facebook/ads/redexgen/X/2S;->A02(III)Ljava/lang/String;

    move-result-object v0

    invoke-direct {p0, v1, v0}, Lcom/facebook/ads/redexgen/X/2S;->A03(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/2S;->A04(Ljava/lang/String;)Ljava/util/List;

    move-result-object v0

    .line 5558
    .local v1, "children":Ljava/util/List;, "Ljava/util/List<Lcom/facebook/ads/internal/adreportingconfig/AdReportingReason;>;"
    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :goto_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_0

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/facebook/ads/redexgen/X/2W;

    .line 5559
    .local v3, "child":Lcom/facebook/ads/redexgen/X/2W;
    invoke-virtual {v4, v0}, Lcom/facebook/ads/redexgen/X/2W;->A06(Lcom/facebook/ads/redexgen/X/2W;)V

    goto :goto_0
    :try_end_0
    .catch Lorg/json/JSONException; {:try_start_0 .. :try_end_0} :catch_0

    .line 5560
    .end local v1    # "children":Ljava/util/List;, "Ljava/util/List<Lcom/facebook/ads/internal/adreportingconfig/AdReportingReason;>;"
    :catch_0
    move-exception v2

    .line 5561
    .local v1, "e":Lorg/json/JSONException;
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/2S;->A07()V

    .line 5562
    sget v1, Lcom/facebook/ads/redexgen/X/8A;->A29:I

    new-instance v0, Lcom/facebook/ads/redexgen/X/8B;

    invoke-direct {v0, v2}, Lcom/facebook/ads/redexgen/X/8B;-><init>(Ljava/lang/Throwable;)V

    invoke-static {v1, v0}, Lcom/facebook/ads/redexgen/X/2S;->A08(ILcom/facebook/ads/redexgen/X/8B;)V

    .line 5563
    .end local v1    # "e":Lorg/json/JSONException;
    :cond_0
    return-object v4
.end method

.method public final A0C()Ljava/lang/String;
    .locals 4

    .line 5564
    const/16 v2, 0x160

    const/16 v1, 0xe

    const/16 v0, 0x2e

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/2S;->A02(III)Ljava/lang/String;

    move-result-object v3

    const/4 v2, 0x0

    const/4 v1, 0x0

    const/16 v0, 0x5e

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/2S;->A02(III)Ljava/lang/String;

    move-result-object v0

    invoke-direct {p0, v3, v0}, Lcom/facebook/ads/redexgen/X/2S;->A03(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public final A0D()Ljava/lang/String;
    .locals 4

    .line 5565
    const/16 v2, 0x18e

    const/16 v1, 0x14

    const/16 v0, 0x4e

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/2S;->A02(III)Ljava/lang/String;

    move-result-object v3

    const/16 v2, 0x138

    const/16 v1, 0x26

    const/16 v0, 0x47

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/2S;->A02(III)Ljava/lang/String;

    move-result-object v0

    invoke-direct {p0, v3, v0}, Lcom/facebook/ads/redexgen/X/2S;->A03(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public final A0E()Ljava/lang/String;
    .locals 4

    .line 5566
    const/16 v2, 0x1a2

    const/16 v1, 0x10

    const/16 v0, 0x53

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/2S;->A02(III)Ljava/lang/String;

    move-result-object v3

    const/16 v2, 0x26

    const/16 v1, 0xa

    const/16 v0, 0x1a

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/2S;->A02(III)Ljava/lang/String;

    move-result-object v0

    invoke-direct {p0, v3, v0}, Lcom/facebook/ads/redexgen/X/2S;->A03(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public final A0F()Ljava/lang/String;
    .locals 4

    .line 5567
    const/16 v2, 0x1b2

    const/16 v1, 0x12

    const/16 v0, 0x5b

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/2S;->A02(III)Ljava/lang/String;

    move-result-object v3

    const/16 v2, 0x30

    const/16 v1, 0xc

    const/16 v0, 0x9

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/2S;->A02(III)Ljava/lang/String;

    move-result-object v0

    invoke-direct {p0, v3, v0}, Lcom/facebook/ads/redexgen/X/2S;->A03(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public final A0G()Ljava/lang/String;
    .locals 4

    .line 5568
    const/16 v2, 0x1cb

    const/16 v1, 0x13

    const/16 v0, 0x69

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/2S;->A02(III)Ljava/lang/String;

    move-result-object v3

    const/16 v2, 0x10c

    const/16 v1, 0x17

    const/16 v0, 0x3b

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/2S;->A02(III)Ljava/lang/String;

    move-result-object v0

    invoke-direct {p0, v3, v0}, Lcom/facebook/ads/redexgen/X/2S;->A03(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public final A0H()Ljava/lang/String;
    .locals 4

    .line 5569
    const/16 v2, 0x1c4

    const/4 v1, 0x7

    const/16 v0, 0x14

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/2S;->A02(III)Ljava/lang/String;

    move-result-object v3

    const/16 v2, 0xc1

    const/4 v1, 0x7

    const/16 v0, 0x1b

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/2S;->A02(III)Ljava/lang/String;

    move-result-object v0

    invoke-direct {p0, v3, v0}, Lcom/facebook/ads/redexgen/X/2S;->A03(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public final A0I()Ljava/lang/String;
    .locals 4

    .line 5570
    const/16 v2, 0x231

    const/16 v1, 0x19

    const/16 v0, 0x70

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/2S;->A02(III)Ljava/lang/String;

    move-result-object v3

    const/4 v2, 0x0

    const/4 v1, 0x0

    const/16 v0, 0x5e

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/2S;->A02(III)Ljava/lang/String;

    move-result-object v0

    invoke-direct {p0, v3, v0}, Lcom/facebook/ads/redexgen/X/2S;->A03(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public final A0J()Ljava/lang/String;
    .locals 4

    .line 5571
    const/16 v2, 0x21c

    const/16 v1, 0x15

    const/16 v0, 0x17

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/2S;->A02(III)Ljava/lang/String;

    move-result-object v3

    const/16 v2, 0xc8

    const/16 v1, 0x15

    const/16 v0, 0x5b

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/2S;->A02(III)Ljava/lang/String;

    move-result-object v0

    invoke-direct {p0, v3, v0}, Lcom/facebook/ads/redexgen/X/2S;->A03(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public final A0K()Ljava/lang/String;
    .locals 4

    .line 5572
    const/16 v2, 0x26e

    const/16 v1, 0x15

    const/16 v0, 0x8

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/2S;->A02(III)Ljava/lang/String;

    move-result-object v3

    const/4 v2, 0x0

    const/16 v1, 0x26

    const/16 v0, 0x26

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/2S;->A02(III)Ljava/lang/String;

    move-result-object v0

    invoke-direct {p0, v3, v0}, Lcom/facebook/ads/redexgen/X/2S;->A03(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public final A0L()Ljava/lang/String;
    .locals 4

    .line 5573
    const/16 v2, 0x265

    const/16 v1, 0x9

    const/16 v0, 0x63

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/2S;->A02(III)Ljava/lang/String;

    move-result-object v3

    const/16 v2, 0x103

    const/16 v1, 0x9

    const/16 v0, 0x29

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/2S;->A02(III)Ljava/lang/String;

    move-result-object v0

    invoke-direct {p0, v3, v0}, Lcom/facebook/ads/redexgen/X/2S;->A03(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public final A0M()Ljava/lang/String;
    .locals 4

    .line 5574
    const/16 v2, 0x2b8

    const/16 v1, 0x14

    const/16 v0, 0x74

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/2S;->A02(III)Ljava/lang/String;

    move-result-object v3

    const/16 v2, 0x123

    const/16 v1, 0x15

    const/16 v0, 0x11

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/2S;->A02(III)Ljava/lang/String;

    move-result-object v0

    invoke-direct {p0, v3, v0}, Lcom/facebook/ads/redexgen/X/2S;->A03(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public final A0N(Ljava/lang/String;)V
    .locals 7

    .line 5575
    invoke-static {p1}, Lcom/facebook/ads/redexgen/X/2S;->A05(Ljava/lang/String;)Ljava/util/Map;

    move-result-object v6

    .line 5576
    .local v0, "jsonValues":Ljava/util/Map;, "Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"
    if-eqz v6, :cond_0

    invoke-interface {v6}, Ljava/util/Map;->size()I

    move-result v1

    sget-object v5, Lcom/facebook/ads/redexgen/X/2S;->A03:[Ljava/lang/String;

    array-length v0, v5

    if-eq v1, v0, :cond_1

    .line 5577
    .end local v1
    :cond_0
    return-void

    .line 5578
    :cond_1
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/2S;->A00:Lcom/facebook/ads/redexgen/X/2Y;

    invoke-interface {v0}, Lcom/facebook/ads/redexgen/X/2Y;->A5S()Lcom/facebook/ads/redexgen/X/aD;

    move-result-object v4

    .line 5579
    .local v1, "editor":Lcom/facebook/ads/redexgen/X/2X;
    array-length v3, v5

    const/4 v2, 0x0

    :goto_0
    if-ge v2, v3, :cond_2

    aget-object v1, v5, v2

    .line 5580
    .local v5, "key":Ljava/lang/String;
    invoke-interface {v6, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/lang/String;

    invoke-virtual {v4, v1, v0}, Lcom/facebook/ads/redexgen/X/aD;->A01(Ljava/lang/String;Ljava/lang/String;)Lcom/facebook/ads/redexgen/X/aD;

    .line 5581
    .end local v5    # "key":Ljava/lang/String;
    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    .line 5582
    :cond_2
    const/16 v2, 0x206

    const/16 v1, 0x16

    const/16 v0, 0x78

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/2S;->A02(III)Ljava/lang/String;

    move-result-object v2

    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v0

    invoke-virtual {v4, v2, v0, v1}, Lcom/facebook/ads/redexgen/X/aD;->A00(Ljava/lang/String;J)Lcom/facebook/ads/redexgen/X/aD;

    .line 5583
    invoke-virtual {v4}, Lcom/facebook/ads/redexgen/X/aD;->A02()V

    .line 5584
    return-void
.end method

.method public final A0O(Landroid/content/Context;Z)Z
    .locals 7

    .line 5585
    const/4 v6, 0x0

    if-nez p2, :cond_0

    .line 5586
    invoke-static {p1}, Lcom/facebook/ads/redexgen/X/Ih;->A29(Landroid/content/Context;)Z

    move-result v0

    if-eqz v0, :cond_3

    :cond_0
    const/4 v5, 0x1

    .line 5587
    .local v2, "isNativeReportingEnabled":Z
    :goto_0
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/2S;->A09()J

    move-result-wide v3

    const-wide/16 v1, 0x0

    cmp-long v0, v3, v1

    if-lez v0, :cond_2

    const/4 v0, 0x1

    .line 5588
    .local v3, "isNativeReportingAvailable":Z
    :goto_1
    if-eqz v5, :cond_1

    if-eqz v0, :cond_1

    const/4 v6, 0x1

    :cond_1
    return v6

    .line 5589
    :cond_2
    const/4 v0, 0x0

    goto :goto_1

    .line 5590
    :cond_3
    const/4 v5, 0x0

    goto :goto_0
.end method
