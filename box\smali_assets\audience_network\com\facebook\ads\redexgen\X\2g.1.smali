.class public interface abstract Lcom/facebook/ads/redexgen/X/2g;
.super Ljava/lang/Object;
.source ""


# virtual methods
.method public abstract A66(Lcom/facebook/ads/redexgen/X/2f;)Landroid/content/res/ColorStateList;
.end method

.method public abstract A6t(Lcom/facebook/ads/redexgen/X/2f;)F
.end method

.method public abstract A7M(Lcom/facebook/ads/redexgen/X/2f;)F
.end method

.method public abstract A7R(Lcom/facebook/ads/redexgen/X/2f;)F
.end method

.method public abstract A7S(Lcom/facebook/ads/redexgen/X/2f;)F
.end method

.method public abstract A7m(Lcom/facebook/ads/redexgen/X/2f;)F
.end method

.method public abstract A8r()V
.end method

.method public abstract A8s(Lcom/facebook/ads/redexgen/X/2f;Landroid/content/Context;Landroid/content/res/ColorStateList;FFF)V
.end method

.method public abstract AB7(Lcom/facebook/ads/redexgen/X/2f;)V
.end method

.method public abstract ACk(Lcom/facebook/ads/redexgen/X/2f;)V
.end method

.method public abstract AFt(Lcom/facebook/ads/redexgen/X/2f;Landroid/content/res/ColorStateList;)V
.end method

.method public abstract AFz(Lcom/facebook/ads/redexgen/X/2f;F)V
.end method

.method public abstract AG6(Lcom/facebook/ads/redexgen/X/2f;F)V
.end method

.method public abstract AGC(Lcom/facebook/ads/redexgen/X/2f;F)V
.end method
