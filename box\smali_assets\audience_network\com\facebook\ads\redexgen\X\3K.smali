.class public final Lcom/facebook/ads/redexgen/X/3K;
.super Lcom/facebook/ads/redexgen/X/6u;
.source ""

# interfaces
.implements Lcom/facebook/ads/redexgen/X/Ga;
.implements Ljava/io/Serializable;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "<T:",
        "Ljava/lang/Enum<",
        "TT;>;>",
        "Lcom/facebook/ads/redexgen/X/6u<",
        "TT;>;",
        "Lcom/facebook/ads/redexgen/X/Ga<",
        "TT;>;",
        "Ljava/io/Serializable;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000:\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0010\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0011\n\u0002\u0008\u0003\n\u0002\u0010\u0008\n\u0002\u0008\u0003\n\u0002\u0010\u000b\n\u0002\u0008\t\n\u0002\u0010\u0000\n\u0000\u0008\u0003\u0018\u0000*\u000e\u0008\u0000\u0010\u0001*\u0008\u0012\u0004\u0012\u0002H\u00010\u00022\u0008\u0012\u0004\u0012\u0002H\u00010\u00032\u0008\u0012\u0004\u0012\u0002H\u00010\u00042\u00060\u0005j\u0002`\u0006B\u0013\u0012\u000c\u0010\u0007\u001a\u0008\u0012\u0004\u0012\u00028\u00000\u0008\u00a2\u0006\u0002\u0010\tJ\u0016\u0010\u000f\u001a\u00020\u00102\u0006\u0010\u0011\u001a\u00028\u0000H\u0096\u0002\u00a2\u0006\u0002\u0010\u0012J\u0016\u0010\u0013\u001a\u00028\u00002\u0006\u0010\u0014\u001a\u00020\u000cH\u0096\u0002\u00a2\u0006\u0002\u0010\u0015J\u0015\u0010\u0016\u001a\u00020\u000c2\u0006\u0010\u0011\u001a\u00028\u0000H\u0016\u00a2\u0006\u0002\u0010\u0017J\u0015\u0010\u0018\u001a\u00020\u000c2\u0006\u0010\u0011\u001a\u00028\u0000H\u0016\u00a2\u0006\u0002\u0010\u0017J\u0008\u0010\u0019\u001a\u00020\u001aH\u0002R\u0016\u0010\u0007\u001a\u0008\u0012\u0004\u0012\u00028\u00000\u0008X\u0082\u0004\u00a2\u0006\u0004\n\u0002\u0010\nR\u0014\u0010\u000b\u001a\u00020\u000c8VX\u0096\u0004\u00a2\u0006\u0006\u001a\u0004\u0008\r\u0010\u000e\u00a8\u0006\u001b"
    }
    d2 = {
        "Lkotlin/enums/EnumEntriesList;",
        "T",
        "",
        "Lkotlin/enums/EnumEntries;",
        "Lkotlin/collections/AbstractList;",
        "Ljava/io/Serializable;",
        "Lkotlin/io/Serializable;",
        "entries",
        "",
        "([Ljava/lang/Enum;)V",
        "[Ljava/lang/Enum;",
        "size",
        "",
        "getSize",
        "()I",
        "contains",
        "",
        "element",
        "(Ljava/lang/Enum;)Z",
        "get",
        "index",
        "(I)Ljava/lang/Enum;",
        "indexOf",
        "(Ljava/lang/Enum;)I",
        "lastIndexOf",
        "writeReplace",
        "",
        "kotlin-stdlib"
    }
    k = 0x1
    mv = {
        0x1,
        0x9,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static A01:[B

.field public static A02:[Ljava/lang/String;


# instance fields
.field public final A00:[Ljava/lang/Enum;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "[TT;"
        }
    .end annotation
.end field


# direct methods
.method public static constructor <clinit>()V
    .locals 3

    .line 404
    const/16 v0, 0x8

    new-array v2, v0, [Ljava/lang/String;

    const/4 v1, 0x0

    const-string v0, "g51k1HhHpYkj9VHfWZ"

    aput-object v0, v2, v1

    const/4 v1, 0x1

    const-string v0, "EWWj4NKKEIgPKrjUtv88K0X8SPv0XOKR"

    aput-object v0, v2, v1

    const/4 v1, 0x2

    const-string v0, "MYYix4VerB0hQ5oDwMWCCvMrCMkMejVr"

    aput-object v0, v2, v1

    const/4 v1, 0x3

    const-string v0, "KXmmOsGIW251fyBFhdS6O0hRIquxh5d4"

    aput-object v0, v2, v1

    const/4 v1, 0x4

    const-string v0, "FqRmXM6RsSdQrEmx7rTZy8IkaG5JdbxF"

    aput-object v0, v2, v1

    const/4 v1, 0x5

    const-string v0, "xb9XSYIqlsKB21aN6AlwK9AyDPFMpn92"

    aput-object v0, v2, v1

    const/4 v1, 0x6

    const-string v0, "G5eIpQ2zAIls9BeUM771c0Xq"

    aput-object v0, v2, v1

    const/4 v1, 0x7

    const-string v0, "UoGRLNIkpvi"

    aput-object v0, v2, v1

    sput-object v2, Lcom/facebook/ads/redexgen/X/3K;->A02:[Ljava/lang/String;

    invoke-static {}, Lcom/facebook/ads/redexgen/X/3K;->A04()V

    return-void
.end method

.method public constructor <init>([Ljava/lang/Enum;)V
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "([TT;)V"
        }
    .end annotation

    const/4 v2, 0x7

    const/4 v1, 0x7

    const/16 v0, 0x3c

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/3K;->A03(III)Ljava/lang/String;

    move-result-object v0

    invoke-static {p1, v0}, Lcom/facebook/ads/redexgen/X/bu;->A07(Ljava/lang/Object;Ljava/lang/String;)V

    .line 7844
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/6u;-><init>()V

    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/3K;->A00:[Ljava/lang/Enum;

    return-void
.end method

.method private final A00(Ljava/lang/Enum;)I
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(TT;)I"
        }
    .end annotation

    const/4 v2, 0x0

    const/4 v1, 0x7

    const/16 v0, 0x71

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/3K;->A03(III)Ljava/lang/String;

    move-result-object v0

    invoke-static {p1, v0}, Lcom/facebook/ads/redexgen/X/bu;->A07(Ljava/lang/Object;Ljava/lang/String;)V

    .line 7845
    invoke-virtual {p1}, Ljava/lang/Enum;->ordinal()I

    move-result v1

    .line 7846
    .local v0, "ordinal":I
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3K;->A00:[Ljava/lang/Enum;

    invoke-static {v0, v1}, Lcom/facebook/ads/redexgen/X/3M;->A01([Ljava/lang/Object;I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/lang/Enum;

    .line 7847
    .local v1, "target":Ljava/lang/Enum;
    if-ne v0, p1, :cond_0

    :goto_0
    return v1

    :cond_0
    const/4 v1, -0x1

    goto :goto_0
.end method

.method private final A01(Ljava/lang/Enum;)I
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(TT;)I"
        }
    .end annotation

    const/4 v2, 0x0

    const/4 v1, 0x7

    const/16 v0, 0x71

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/3K;->A03(III)Ljava/lang/String;

    move-result-object v0

    invoke-static {p1, v0}, Lcom/facebook/ads/redexgen/X/bu;->A07(Ljava/lang/Object;Ljava/lang/String;)V

    .line 7848
    invoke-virtual {p0, p1}, Lcom/facebook/ads/redexgen/X/3K;->indexOf(Ljava/lang/Object;)I

    move-result v0

    return v0
.end method

.method private final A02(I)Ljava/lang/Enum;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I)TT;"
        }
    .end annotation

    .line 7849
    sget-object v1, Lcom/facebook/ads/redexgen/X/6u;->A02:Lcom/facebook/ads/redexgen/X/c1;

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3K;->A00:[Ljava/lang/Enum;

    array-length v0, v0

    invoke-virtual {v1, p1, v0}, Lcom/facebook/ads/redexgen/X/c1;->A03(II)V

    .line 7850
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3K;->A00:[Ljava/lang/Enum;

    aget-object v0, v0, p1

    return-object v0
.end method

.method public static A03(III)Ljava/lang/String;
    .locals 2

    sget-object v1, Lcom/facebook/ads/redexgen/X/3K;->A01:[B

    add-int v0, p0, p1

    invoke-static {v1, p0, v0}, Ljava/util/Arrays;->copyOfRange([BII)[B

    move-result-object p0

    const/4 v1, 0x0

    :goto_0
    array-length v0, p0

    if-ge v1, v0, :cond_0

    aget-byte v0, p0, v1

    xor-int/2addr v0, p2

    xor-int/lit8 v0, v0, 0x1b

    int-to-byte v0, v0

    aput-byte v0, p0, v1

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_0
    new-instance v0, Ljava/lang/String;

    invoke-direct {v0, p0}, Ljava/lang/String;-><init>([B)V

    return-object v0
.end method

.method public static A04()V
    .locals 1

    const/16 v0, 0xe

    new-array v0, v0, [B

    fill-array-data v0, :array_0

    sput-object v0, Lcom/facebook/ads/redexgen/X/3K;->A01:[B

    return-void

    :array_0
    .array-data 1
        0xft
        0x6t
        0xft
        0x7t
        0xft
        0x4t
        0x1et
        0x42t
        0x49t
        0x53t
        0x55t
        0x4et
        0x42t
        0x54t
    .end array-data
.end method

.method private final A05(Ljava/lang/Enum;)Z
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(TT;)Z"
        }
    .end annotation

    const/4 v2, 0x0

    const/4 v1, 0x7

    const/16 v0, 0x71

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/3K;->A03(III)Ljava/lang/String;

    move-result-object v0

    invoke-static {p1, v0}, Lcom/facebook/ads/redexgen/X/bu;->A07(Ljava/lang/Object;Ljava/lang/String;)V

    .line 7851
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/3K;->A00:[Ljava/lang/Enum;

    invoke-virtual {p1}, Ljava/lang/Enum;->ordinal()I

    move-result v0

    invoke-static {v1, v0}, Lcom/facebook/ads/redexgen/X/3M;->A01([Ljava/lang/Object;I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/lang/Enum;

    .line 7852
    .local v0, "target":Ljava/lang/Enum;
    if-ne v0, p1, :cond_0

    const/4 v0, 0x1

    :goto_0
    return v0

    :cond_0
    const/4 v0, 0x0

    goto :goto_0
.end method


# virtual methods
.method public final A0A()I
    .locals 1

    .line 7853
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3K;->A00:[Ljava/lang/Enum;

    array-length v0, v0

    return v0
.end method

.method public final bridge contains(Ljava/lang/Object;)Z
    .locals 1

    .line 7854
    instance-of v0, p1, Ljava/lang/Enum;

    if-nez v0, :cond_0

    const/4 v0, 0x0

    return v0

    :cond_0
    check-cast p1, Ljava/lang/Enum;

    invoke-direct {p0, p1}, Lcom/facebook/ads/redexgen/X/3K;->A05(Ljava/lang/Enum;)Z

    move-result v0

    return v0
.end method

.method public final bridge synthetic get(I)Ljava/lang/Object;
    .locals 1

    .line 7855
    invoke-direct {p0, p1}, Lcom/facebook/ads/redexgen/X/3K;->A02(I)Ljava/lang/Enum;

    move-result-object v0

    return-object v0
.end method

.method public final bridge indexOf(Ljava/lang/Object;)I
    .locals 1

    .line 7856
    instance-of v0, p1, Ljava/lang/Enum;

    if-nez v0, :cond_0

    const/4 v0, -0x1

    return v0

    :cond_0
    check-cast p1, Ljava/lang/Enum;

    invoke-direct {p0, p1}, Lcom/facebook/ads/redexgen/X/3K;->A00(Ljava/lang/Enum;)I

    move-result v0

    return v0
.end method

.method public final bridge lastIndexOf(Ljava/lang/Object;)I
    .locals 3

    .line 7857
    instance-of v0, p1, Ljava/lang/Enum;

    if-nez v0, :cond_0

    const/4 v0, -0x1

    return v0

    :cond_0
    check-cast p1, Ljava/lang/Enum;

    sget-object v1, Lcom/facebook/ads/redexgen/X/3K;->A02:[Ljava/lang/String;

    const/4 v0, 0x1

    aget-object v1, v1, v0

    const/16 v0, 0x15

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    const/16 v0, 0x30

    if-eq v1, v0, :cond_1

    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    :cond_1
    sget-object v2, Lcom/facebook/ads/redexgen/X/3K;->A02:[Ljava/lang/String;

    const-string v1, "ilUmfbZBv6Y2f9PUf49wlGdNYrlD4EkR"

    const/4 v0, 0x4

    aput-object v1, v2, v0

    const-string v1, "0YjmfpCo2P8nf50lJrNZDFbnB4C9VoXf"

    const/4 v0, 0x3

    aput-object v1, v2, v0

    invoke-direct {p0, p1}, Lcom/facebook/ads/redexgen/X/3K;->A01(Ljava/lang/Enum;)I

    move-result v0

    return v0
.end method
