.class public Lcom/facebook/ads/redexgen/X/3h;
.super Landroid/view/ViewGroup;
.source ""


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/facebook/ads/redexgen/X/3g;,
        Lcom/facebook/ads/redexgen/X/3a;,
        Lcom/facebook/ads/redexgen/X/3e;,
        Lcom/facebook/ads/redexgen/X/Zz;,
        Lcom/facebook/ads/internal/androidx/support/v4/view/ViewPager$SavedState;,
        Lcom/facebook/ads/internal/androidx/support/v4/view/ViewPager$DecorView;,
        Lcom/facebook/ads/internal/androidx/support/v4/view/ViewPager$OnAdapterChangeListener;,
        Lcom/facebook/ads/internal/androidx/support/v4/view/ViewPager$PageTransformer;,
        Lcom/facebook/ads/internal/androidx/support/v4/view/ViewPager$SimpleOnPageChangeListener;,
        Lcom/facebook/ads/redexgen/X/3c;,
        Lcom/facebook/ads/redexgen/X/3Z;
    }
.end annotation


# static fields
.field public static A0s:[B

.field public static A0t:[Ljava/lang/String;

.field public static final A0u:[I

.field public static final A0v:Landroid/view/animation/Interpolator;

.field public static final A0w:Lcom/facebook/ads/redexgen/X/3g;

.field public static final A0x:Ljava/util/Comparator;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Comparator<",
            "Lcom/facebook/ads/redexgen/X/3Z;",
            ">;"
        }
    .end annotation
.end field


# instance fields
.field public A00:I

.field public A01:Lcom/facebook/ads/redexgen/X/3E;

.field public A02:F

.field public A03:F

.field public A04:F

.field public A05:F

.field public A06:F

.field public A07:F

.field public A08:I

.field public A09:I

.field public A0A:I

.field public A0B:I

.field public A0C:I

.field public A0D:I

.field public A0E:I

.field public A0F:I

.field public A0G:I

.field public A0H:I

.field public A0I:I

.field public A0J:I

.field public A0K:I

.field public A0L:I

.field public A0M:I

.field public A0N:I

.field public A0O:I

.field public A0P:I

.field public A0Q:I

.field public A0R:I

.field public A0S:Landroid/graphics/drawable/Drawable;

.field public A0T:Landroid/os/Parcelable;

.field public A0U:Landroid/view/VelocityTracker;

.field public A0V:Landroid/widget/EdgeEffect;

.field public A0W:Landroid/widget/EdgeEffect;

.field public A0X:Landroid/widget/Scroller;

.field public A0Y:Lcom/facebook/ads/redexgen/X/3c;

.field public A0Z:Lcom/facebook/ads/redexgen/X/3e;

.field public A0a:Ljava/lang/ClassLoader;

.field public A0b:Ljava/util/ArrayList;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/ArrayList<",
            "Landroid/view/View;",
            ">;"
        }
    .end annotation
.end field

.field public A0c:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lcom/facebook/ads/internal/androidx/support/v4/view/ViewPager$OnAdapterChangeListener;",
            ">;"
        }
    .end annotation
.end field

.field public A0d:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lcom/facebook/ads/redexgen/X/3c;",
            ">;"
        }
    .end annotation
.end field

.field public A0e:Z

.field public A0f:Z

.field public A0g:Z

.field public A0h:Z

.field public A0i:Z

.field public A0j:Z

.field public A0k:Z

.field public A0l:Z

.field public A0m:Z

.field public A0n:Z

.field public final A0o:Landroid/graphics/Rect;

.field public final A0p:Lcom/facebook/ads/redexgen/X/3Z;

.field public final A0q:Ljava/lang/Runnable;

.field public final A0r:Ljava/util/ArrayList;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/ArrayList<",
            "Lcom/facebook/ads/redexgen/X/3Z;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public static constructor <clinit>()V
    .locals 3

    .line 438
    const/16 v0, 0x8

    new-array v2, v0, [Ljava/lang/String;

    const/4 v1, 0x0

    const-string v0, "Pb5dMBpLoD8olSqqN3M1wt71s6WfJWWx"

    aput-object v0, v2, v1

    const/4 v1, 0x1

    const-string v0, "mawes8eGWKEfT5kg0DwbKE"

    aput-object v0, v2, v1

    const/4 v1, 0x2

    const-string v0, "yCX0UAcI"

    aput-object v0, v2, v1

    const/4 v1, 0x3

    const-string v0, "nBGVAWTalDrx8Q3Q0HTp4hPpqgaJoZ5h"

    aput-object v0, v2, v1

    const/4 v1, 0x4

    const-string v0, ""

    aput-object v0, v2, v1

    const/4 v1, 0x5

    const-string v0, ""

    aput-object v0, v2, v1

    const/4 v1, 0x6

    const-string v0, "gKpLqM1fc4CgqUT7jB7tIrf4xsKdVFyY"

    aput-object v0, v2, v1

    const/4 v1, 0x7

    const-string v0, "wKHjR3FqXT8StRIGs0zAAfuJmbbhub4H"

    aput-object v0, v2, v1

    sput-object v2, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    invoke-static {}, Lcom/facebook/ads/redexgen/X/3h;->A0C()V

    const v0, 0x10100b3

    filled-new-array {v0}, [I

    move-result-object v0

    sput-object v0, Lcom/facebook/ads/redexgen/X/3h;->A0u:[I

    .line 439
    new-instance v0, Lcom/facebook/ads/redexgen/X/3V;

    invoke-direct {v0}, Lcom/facebook/ads/redexgen/X/3V;-><init>()V

    sput-object v0, Lcom/facebook/ads/redexgen/X/3h;->A0x:Ljava/util/Comparator;

    .line 440
    new-instance v0, Lcom/facebook/ads/redexgen/X/3W;

    invoke-direct {v0}, Lcom/facebook/ads/redexgen/X/3W;-><init>()V

    sput-object v0, Lcom/facebook/ads/redexgen/X/3h;->A0v:Landroid/view/animation/Interpolator;

    .line 441
    new-instance v0, Lcom/facebook/ads/redexgen/X/3g;

    invoke-direct {v0}, Lcom/facebook/ads/redexgen/X/3g;-><init>()V

    sput-object v0, Lcom/facebook/ads/redexgen/X/3h;->A0w:Lcom/facebook/ads/redexgen/X/3g;

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 2

    .line 8110
    invoke-direct {p0, p1}, Landroid/view/ViewGroup;-><init>(Landroid/content/Context;)V

    .line 8111
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0r:Ljava/util/ArrayList;

    .line 8112
    new-instance v0, Lcom/facebook/ads/redexgen/X/3Z;

    invoke-direct {v0}, Lcom/facebook/ads/redexgen/X/3Z;-><init>()V

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0p:Lcom/facebook/ads/redexgen/X/3Z;

    .line 8113
    new-instance v0, Landroid/graphics/Rect;

    invoke-direct {v0}, Landroid/graphics/Rect;-><init>()V

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0o:Landroid/graphics/Rect;

    .line 8114
    const/4 v1, -0x1

    iput v1, p0, Lcom/facebook/ads/redexgen/X/3h;->A0O:I

    .line 8115
    const/4 v0, 0x0

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0T:Landroid/os/Parcelable;

    .line 8116
    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0a:Ljava/lang/ClassLoader;

    .line 8117
    const v0, -0x800001

    iput v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A02:F

    .line 8118
    const v0, 0x7f7fffff    # Float.MAX_VALUE

    iput v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A07:F

    .line 8119
    const/4 v0, 0x1

    iput v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0L:I

    .line 8120
    iput v1, p0, Lcom/facebook/ads/redexgen/X/3h;->A08:I

    .line 8121
    iput-boolean v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0g:Z

    .line 8122
    const/4 v1, 0x0

    iput-boolean v1, p0, Lcom/facebook/ads/redexgen/X/3h;->A0l:Z

    .line 8123
    new-instance v0, Lcom/facebook/ads/redexgen/X/3X;

    invoke-direct {v0, p0}, Lcom/facebook/ads/redexgen/X/3X;-><init>(Lcom/facebook/ads/redexgen/X/3h;)V

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0q:Ljava/lang/Runnable;

    .line 8124
    iput v1, p0, Lcom/facebook/ads/redexgen/X/3h;->A0P:I

    .line 8125
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/3h;->A0D()V

    .line 8126
    return-void
.end method

.method private final A00(F)F
    .locals 3

    .line 8127
    const/high16 v0, 0x3f000000    # 0.5f

    sub-float/2addr p1, v0

    .line 8128
    const v0, 0x3ef1463b

    mul-float/2addr p1, v0

    .line 8129
    float-to-double v0, p1

    invoke-static {v0, v1}, Ljava/lang/Math;->sin(D)D

    move-result-wide v1

    double-to-float v0, v1

    return v0
.end method

.method private A01(IFII)I
    .locals 3

    .line 8130
    invoke-static {p4}, Ljava/lang/Math;->abs(I)I

    move-result v1

    iget v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0H:I

    if-le v1, v0, :cond_2

    invoke-static {p3}, Ljava/lang/Math;->abs(I)I

    move-result v1

    iget v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0K:I

    if-le v1, v0, :cond_2

    .line 8131
    if-lez p3, :cond_1

    .line 8132
    .local v0, "targetPage":I
    :goto_0
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0r:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->size()I

    move-result v0

    if-lez v0, :cond_0

    .line 8133
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/3h;->A0r:Ljava/util/ArrayList;

    const/4 v0, 0x0

    invoke-virtual {v1, v0}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/facebook/ads/redexgen/X/3Z;

    .line 8134
    .local v1, "firstItem":Lcom/facebook/ads/redexgen/X/3Z;
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/3h;->A0r:Ljava/util/ArrayList;

    invoke-virtual {v1}, Ljava/util/ArrayList;->size()I

    move-result v0

    add-int/lit8 v0, v0, -0x1

    invoke-virtual {v1, v0}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/facebook/ads/redexgen/X/3Z;

    .line 8135
    .local v2, "lastItem":Lcom/facebook/ads/redexgen/X/3Z;
    iget v1, v2, Lcom/facebook/ads/redexgen/X/3Z;->A02:I

    iget v0, v0, Lcom/facebook/ads/redexgen/X/3Z;->A02:I

    invoke-static {p1, v0}, Ljava/lang/Math;->min(II)I

    move-result v0

    invoke-static {v1, v0}, Ljava/lang/Math;->max(II)I

    move-result p1

    .line 8136
    .end local v1    # "firstItem":Lcom/facebook/ads/redexgen/X/3Z;
    .end local v2    # "lastItem":Lcom/facebook/ads/redexgen/X/3Z;
    :cond_0
    return p1

    .line 8137
    :cond_1
    add-int/lit8 p1, p1, 0x1

    goto :goto_0

    .line 8138
    .end local v0    # "targetPage":I
    :cond_2
    iget v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A00:I

    if-lt p1, v0, :cond_3

    const v0, 0x3ecccccd    # 0.4f

    .line 8139
    .local v0, "truncator":F
    :goto_1
    add-float/2addr p2, v0

    float-to-int v0, p2

    add-int/2addr v0, p1

    move p1, v0

    goto :goto_0

    .line 8140
    :cond_3
    const v0, 0x3f19999a    # 0.6f

    goto :goto_1
.end method

.method private A02(Landroid/graphics/Rect;Landroid/view/View;)Landroid/graphics/Rect;
    .locals 3

    .line 8141
    if-nez p1, :cond_0

    .line 8142
    new-instance p1, Landroid/graphics/Rect;

    invoke-direct {p1}, Landroid/graphics/Rect;-><init>()V

    .line 8143
    :cond_0
    if-nez p2, :cond_1

    .line 8144
    const/4 v0, 0x0

    invoke-virtual {p1, v0, v0, v0, v0}, Landroid/graphics/Rect;->set(IIII)V

    .line 8145
    return-object p1

    .line 8146
    :cond_1
    invoke-virtual {p2}, Landroid/view/View;->getLeft()I

    move-result v0

    iput v0, p1, Landroid/graphics/Rect;->left:I

    .line 8147
    invoke-virtual {p2}, Landroid/view/View;->getRight()I

    move-result v0

    iput v0, p1, Landroid/graphics/Rect;->right:I

    .line 8148
    invoke-virtual {p2}, Landroid/view/View;->getTop()I

    move-result v0

    iput v0, p1, Landroid/graphics/Rect;->top:I

    .line 8149
    invoke-virtual {p2}, Landroid/view/View;->getBottom()I

    move-result v0

    iput v0, p1, Landroid/graphics/Rect;->bottom:I

    .line 8150
    invoke-virtual {p2}, Landroid/view/View;->getParent()Landroid/view/ViewParent;

    move-result-object v2

    .line 8151
    .local v0, "parent":Landroid/view/ViewParent;
    :goto_0
    instance-of v0, v2, Landroid/view/ViewGroup;

    if-eqz v0, :cond_2

    if-eq v2, p0, :cond_2

    .line 8152
    check-cast v2, Landroid/view/ViewGroup;

    .line 8153
    .local v1, "group":Landroid/view/ViewGroup;
    iget v1, p1, Landroid/graphics/Rect;->left:I

    invoke-virtual {v2}, Landroid/view/ViewGroup;->getLeft()I

    move-result v0

    add-int/2addr v1, v0

    iput v1, p1, Landroid/graphics/Rect;->left:I

    .line 8154
    iget v1, p1, Landroid/graphics/Rect;->right:I

    invoke-virtual {v2}, Landroid/view/ViewGroup;->getRight()I

    move-result v0

    add-int/2addr v1, v0

    iput v1, p1, Landroid/graphics/Rect;->right:I

    .line 8155
    iget v1, p1, Landroid/graphics/Rect;->top:I

    invoke-virtual {v2}, Landroid/view/ViewGroup;->getTop()I

    move-result v0

    add-int/2addr v1, v0

    iput v1, p1, Landroid/graphics/Rect;->top:I

    .line 8156
    iget v1, p1, Landroid/graphics/Rect;->bottom:I

    invoke-virtual {v2}, Landroid/view/ViewGroup;->getBottom()I

    move-result v0

    add-int/2addr v1, v0

    iput v1, p1, Landroid/graphics/Rect;->bottom:I

    .line 8157
    invoke-virtual {v2}, Landroid/view/ViewGroup;->getParent()Landroid/view/ViewParent;

    move-result-object v2

    .line 8158
    .end local v1    # "group":Landroid/view/ViewGroup;
    goto :goto_0

    .line 8159
    :cond_2
    return-object p1
.end method

.method private A03()Lcom/facebook/ads/redexgen/X/3Z;
    .locals 11

    .line 8160
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/3h;->getClientWidth()I

    move-result v1

    .line 8161
    .local v0, "width":I
    const/4 v6, 0x0

    if-lez v1, :cond_7

    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/3h;->getScrollX()I

    move-result v0

    int-to-float v7, v0

    int-to-float v0, v1

    div-float/2addr v7, v0

    .line 8162
    .local v2, "scrollOffset":F
    :goto_0
    if-lez v1, :cond_0

    iget v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0M:I

    int-to-float v6, v0

    int-to-float v0, v1

    div-float/2addr v6, v0

    .line 8163
    .local v1, "marginOffset":F
    :cond_0
    const/4 v9, -0x1

    .line 8164
    .local v3, "lastPos":I
    const/4 v3, 0x0

    .line 8165
    .local v4, "lastOffset":F
    const/4 v8, 0x0

    .line 8166
    .local v5, "lastWidth":F
    const/4 v2, 0x1

    .line 8167
    .local v6, "first":Z
    const/4 v10, 0x0

    .line 8168
    .local v7, "lastItem":Lcom/facebook/ads/redexgen/X/3Z;
    const/4 v5, 0x0

    .local v8, "i":I
    :goto_1
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0r:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->size()I

    move-result v0

    if-ge v5, v0, :cond_9

    .line 8169
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0r:Ljava/util/ArrayList;

    invoke-virtual {v0, v5}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lcom/facebook/ads/redexgen/X/3Z;

    .line 8170
    .local v9, "ii":Lcom/facebook/ads/redexgen/X/3Z;
    if-nez v2, :cond_1

    iget v1, v4, Lcom/facebook/ads/redexgen/X/3Z;->A02:I

    add-int/lit8 v0, v9, 0x1

    if-eq v1, v0, :cond_1

    .line 8171
    iget-object v4, p0, Lcom/facebook/ads/redexgen/X/3h;->A0p:Lcom/facebook/ads/redexgen/X/3Z;

    .line 8172
    add-float/2addr v3, v8

    add-float/2addr v3, v6

    iput v3, v4, Lcom/facebook/ads/redexgen/X/3Z;->A00:F

    .line 8173
    add-int/lit8 v0, v9, 0x1

    iput v0, v4, Lcom/facebook/ads/redexgen/X/3Z;->A02:I

    .line 8174
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/3h;->A01:Lcom/facebook/ads/redexgen/X/3E;

    iget v0, v4, Lcom/facebook/ads/redexgen/X/3Z;->A02:I

    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/3E;->A00(I)F

    move-result v0

    iput v0, v4, Lcom/facebook/ads/redexgen/X/3Z;->A01:F

    .line 8175
    add-int/lit8 v5, v5, -0x1

    .line 8176
    :cond_1
    iget v3, v4, Lcom/facebook/ads/redexgen/X/3Z;->A00:F

    .line 8177
    .local v10, "offset":F
    .local p0, "leftBound":F
    iget v8, v4, Lcom/facebook/ads/redexgen/X/3Z;->A01:F

    add-float/2addr v8, v3

    add-float/2addr v8, v6

    .line 8178
    .local p1, "rightBound":F
    if-nez v2, :cond_3

    cmpl-float v9, v7, v3

    sget-object v2, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const/4 v0, 0x4

    aget-object v1, v2, v0

    const/4 v0, 0x5

    aget-object v0, v2, v0

    invoke-virtual {v1}, Ljava/lang/String;->length()I

    move-result v1

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v0

    if-eq v1, v0, :cond_2

    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    :cond_2
    sget-object v2, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const-string v1, "VrUwTTzuyHD2SkMMCenWDHvarMqbXCCI"

    const/4 v0, 0x3

    aput-object v1, v2, v0

    const-string v1, "LX2NnyEsDXWEx3dFbPH8p7fzI1U1dTZ8"

    const/4 v0, 0x6

    aput-object v1, v2, v0

    if-ltz v9, :cond_8

    .line 8179
    :cond_3
    cmpg-float v0, v7, v8

    if-ltz v0, :cond_4

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0r:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->size()I

    move-result v0

    add-int/lit8 v8, v0, -0x1

    sget-object v2, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const/4 v0, 0x3

    aget-object v1, v2, v0

    const/4 v0, 0x6

    aget-object v2, v2, v0

    const/16 v0, 0xf

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    invoke-virtual {v2, v0}, Ljava/lang/String;->charAt(I)C

    move-result v0

    if-eq v1, v0, :cond_5

    sget-object v2, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const-string v1, "sbP3P6ChvdnfvWI"

    const/4 v0, 0x2

    aput-object v1, v2, v0

    if-ne v5, v8, :cond_6

    .line 8180
    .restart local v9    # "ii":Lcom/facebook/ads/redexgen/X/3Z;
    .restart local v10    # "offset":F
    .restart local p0    # "leftBound":F
    .restart local p1
    :cond_4
    :goto_2
    return-object v4

    :cond_5
    sget-object v2, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const-string v1, "kvGf4Zg53U8bz7GLeUNnCLzy4o"

    const/4 v0, 0x1

    aput-object v1, v2, v0

    if-ne v5, v8, :cond_6

    goto :goto_2

    .line 8181
    :cond_6
    const/4 v2, 0x0

    .line 8182
    iget v9, v4, Lcom/facebook/ads/redexgen/X/3Z;->A02:I

    .line 8183
    iget v8, v4, Lcom/facebook/ads/redexgen/X/3Z;->A01:F

    .line 8184
    move-object v10, v4

    .line 8185
    .end local v9    # "ii":Lcom/facebook/ads/redexgen/X/3Z;
    .end local v10    # "offset":F
    .end local p0    # "leftBound":F
    .end local p1
    add-int/lit8 v5, v5, 0x1

    goto/16 :goto_1

    .line 8186
    :cond_7
    const/4 v7, 0x0

    goto/16 :goto_0

    .line 8187
    :cond_8
    return-object v10

    .line 8188
    .end local v8    # "i":I
    .end local v9
    .end local v10
    .end local p0
    .end local p1
    :cond_9
    return-object v10
.end method

.method private final A04(I)Lcom/facebook/ads/redexgen/X/3Z;
    .locals 3

    .line 8189
    const/4 v2, 0x0

    .local v0, "i":I
    :goto_0
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0r:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->size()I

    move-result v0

    if-ge v2, v0, :cond_1

    .line 8190
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0r:Ljava/util/ArrayList;

    invoke-virtual {v0, v2}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/facebook/ads/redexgen/X/3Z;

    .line 8191
    .local v1, "ii":Lcom/facebook/ads/redexgen/X/3Z;
    iget v0, v1, Lcom/facebook/ads/redexgen/X/3Z;->A02:I

    if-ne v0, p1, :cond_0

    .line 8192
    return-object v1

    .line 8193
    .end local v1    # "ii":Lcom/facebook/ads/redexgen/X/3Z;
    :cond_0
    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    .line 8194
    .end local v0    # "i":I
    :cond_1
    const/4 v0, 0x0

    return-object v0
.end method

.method private final A05(II)Lcom/facebook/ads/redexgen/X/3Z;
    .locals 2

    .line 8195
    new-instance v1, Lcom/facebook/ads/redexgen/X/3Z;

    invoke-direct {v1}, Lcom/facebook/ads/redexgen/X/3Z;-><init>()V

    .line 8196
    .local v0, "ii":Lcom/facebook/ads/redexgen/X/3Z;
    iput p1, v1, Lcom/facebook/ads/redexgen/X/3Z;->A02:I

    .line 8197
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A01:Lcom/facebook/ads/redexgen/X/3E;

    invoke-virtual {v0, p0, p1}, Lcom/facebook/ads/redexgen/X/3E;->A04(Landroid/view/ViewGroup;I)Ljava/lang/Object;

    move-result-object v0

    iput-object v0, v1, Lcom/facebook/ads/redexgen/X/3Z;->A03:Ljava/lang/Object;

    .line 8198
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A01:Lcom/facebook/ads/redexgen/X/3E;

    invoke-virtual {v0, p1}, Lcom/facebook/ads/redexgen/X/3E;->A00(I)F

    move-result v0

    iput v0, v1, Lcom/facebook/ads/redexgen/X/3Z;->A01:F

    .line 8199
    if-ltz p2, :cond_0

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0r:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->size()I

    move-result v0

    if-lt p2, v0, :cond_1

    .line 8200
    :cond_0
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0r:Ljava/util/ArrayList;

    invoke-virtual {v0, v1}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 8201
    :goto_0
    return-object v1

    .line 8202
    :cond_1
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0r:Ljava/util/ArrayList;

    invoke-virtual {v0, p2, v1}, Ljava/util/ArrayList;->add(ILjava/lang/Object;)V

    goto :goto_0
.end method

.method private final A06(Landroid/view/View;)Lcom/facebook/ads/redexgen/X/3Z;
    .locals 2

    .line 8203
    :goto_0
    invoke-virtual {p1}, Landroid/view/View;->getParent()Landroid/view/ViewParent;

    move-result-object v1

    .local v1, "parent":Landroid/view/ViewParent;
    if-eq v1, p0, :cond_2

    .line 8204
    if-eqz v1, :cond_0

    instance-of v0, v1, Landroid/view/View;

    if-nez v0, :cond_1

    .line 8205
    :cond_0
    const/4 v0, 0x0

    return-object v0

    .line 8206
    :cond_1
    move-object p1, v1

    check-cast p1, Landroid/view/View;

    goto :goto_0

    .line 8207
    :cond_2
    invoke-direct {p0, p1}, Lcom/facebook/ads/redexgen/X/3h;->A07(Landroid/view/View;)Lcom/facebook/ads/redexgen/X/3Z;

    move-result-object v0

    return-object v0
.end method

.method private final A07(Landroid/view/View;)Lcom/facebook/ads/redexgen/X/3Z;
    .locals 4

    .line 8208
    const/4 v3, 0x0

    .local v0, "i":I
    :goto_0
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0r:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->size()I

    move-result v0

    if-ge v3, v0, :cond_1

    .line 8209
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0r:Ljava/util/ArrayList;

    invoke-virtual {v0, v3}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/facebook/ads/redexgen/X/3Z;

    .line 8210
    .local v1, "ii":Lcom/facebook/ads/redexgen/X/3Z;
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/3h;->A01:Lcom/facebook/ads/redexgen/X/3E;

    iget-object v0, v2, Lcom/facebook/ads/redexgen/X/3Z;->A03:Ljava/lang/Object;

    invoke-virtual {v1, p1, v0}, Lcom/facebook/ads/redexgen/X/3E;->A08(Landroid/view/View;Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_0

    .line 8211
    return-object v2

    .line 8212
    .end local v1    # "ii":Lcom/facebook/ads/redexgen/X/3Z;
    :cond_0
    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    .line 8213
    .end local v0    # "i":I
    :cond_1
    const/4 v3, 0x0

    sget-object v2, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const/4 v0, 0x4

    aget-object v1, v2, v0

    const/4 v0, 0x5

    aget-object v0, v2, v0

    invoke-virtual {v1}, Ljava/lang/String;->length()I

    move-result v1

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v0

    if-eq v1, v0, :cond_2

    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    :cond_2
    sget-object v2, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const-string v1, "T6ty"

    const/4 v0, 0x1

    aput-object v1, v2, v0

    return-object v3
.end method

.method public static A08(III)Ljava/lang/String;
    .locals 2

    sget-object v1, Lcom/facebook/ads/redexgen/X/3h;->A0s:[B

    add-int v0, p0, p1

    invoke-static {v1, p0, v0}, Ljava/util/Arrays;->copyOfRange([BII)[B

    move-result-object p0

    const/4 v1, 0x0

    :goto_0
    array-length v0, p0

    if-ge v1, v0, :cond_0

    aget-byte v0, p0, v1

    sub-int/2addr v0, p2

    add-int/lit8 v0, v0, -0x68

    int-to-byte v0, v0

    aput-byte v0, p0, v1

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_0
    new-instance v0, Ljava/lang/String;

    invoke-direct {v0, p0}, Ljava/lang/String;-><init>([B)V

    return-object v0
.end method

.method private A09()V
    .locals 4

    .line 8214
    const/4 v0, 0x0

    iput-boolean v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0i:Z

    .line 8215
    iput-boolean v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0k:Z

    .line 8216
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0U:Landroid/view/VelocityTracker;

    if-eqz v0, :cond_1

    .line 8217
    invoke-virtual {v0}, Landroid/view/VelocityTracker;->recycle()V

    .line 8218
    const/4 v3, 0x0

    sget-object v2, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const/4 v0, 0x7

    aget-object v1, v2, v0

    const/4 v0, 0x0

    aget-object v2, v2, v0

    const/16 v0, 0xa

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    invoke-virtual {v2, v0}, Ljava/lang/String;->charAt(I)C

    move-result v0

    if-eq v1, v0, :cond_0

    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    :cond_0
    sget-object v2, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const-string v1, "yoRarqzFlsm2scdQtgnfRphmjo"

    const/4 v0, 0x1

    aput-object v1, v2, v0

    iput-object v3, p0, Lcom/facebook/ads/redexgen/X/3h;->A0U:Landroid/view/VelocityTracker;

    .line 8219
    :cond_1
    return-void
.end method

.method private A0A()V
    .locals 2

    .line 8220
    const/4 v1, 0x0

    .local v0, "i":I
    :goto_0
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/3h;->getChildCount()I

    move-result v0

    if-ge v1, v0, :cond_1

    .line 8221
    invoke-virtual {p0, v1}, Lcom/facebook/ads/redexgen/X/3h;->getChildAt(I)Landroid/view/View;

    move-result-object v0

    .line 8222
    .local v1, "child":Landroid/view/View;
    invoke-virtual {v0}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    move-result-object v0

    check-cast v0, Lcom/facebook/ads/redexgen/X/3a;

    .line 8223
    .local p0, "lp":Lcom/facebook/ads/redexgen/X/3a;
    iget-boolean v0, v0, Lcom/facebook/ads/redexgen/X/3a;->A05:Z

    if-nez v0, :cond_0

    .line 8224
    invoke-virtual {p0, v1}, Lcom/facebook/ads/redexgen/X/3h;->removeViewAt(I)V

    .line 8225
    add-int/lit8 v1, v1, -0x1

    .line 8226
    .end local v1    # "child":Landroid/view/View;
    .end local p0    # "lp":Lcom/facebook/ads/redexgen/X/3a;
    :cond_0
    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    .line 8227
    .end local v0    # "i":I
    :cond_1
    return-void
.end method

.method private A0B()V
    .locals 4

    .line 8228
    iget v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0F:I

    if-eqz v0, :cond_2

    .line 8229
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0b:Ljava/util/ArrayList;

    if-nez v0, :cond_0

    .line 8230
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0b:Ljava/util/ArrayList;

    .line 8231
    :goto_0
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/3h;->getChildCount()I

    move-result v3

    .line 8232
    .local v0, "childCount":I
    const/4 v2, 0x0

    .local v1, "i":I
    :goto_1
    if-ge v2, v3, :cond_1

    .line 8233
    invoke-virtual {p0, v2}, Lcom/facebook/ads/redexgen/X/3h;->getChildAt(I)Landroid/view/View;

    move-result-object v1

    .line 8234
    .local v2, "child":Landroid/view/View;
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0b:Ljava/util/ArrayList;

    invoke-virtual {v0, v1}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 8235
    .end local v2    # "child":Landroid/view/View;
    add-int/lit8 v2, v2, 0x1

    goto :goto_1

    .line 8236
    :cond_0
    invoke-virtual {v0}, Ljava/util/ArrayList;->clear()V

    goto :goto_0

    .line 8237
    .end local v1    # "i":I
    :cond_1
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/3h;->A0b:Ljava/util/ArrayList;

    sget-object v0, Lcom/facebook/ads/redexgen/X/3h;->A0w:Lcom/facebook/ads/redexgen/X/3g;

    invoke-static {v1, v0}, Ljava/util/Collections;->sort(Ljava/util/List;Ljava/util/Comparator;)V

    .line 8238
    .end local v0    # "childCount":I
    :cond_2
    return-void
.end method

.method public static A0C()V
    .locals 1

    const/16 v0, 0x1cf

    new-array v0, v0, [B

    fill-array-data v0, :array_0

    sput-object v0, Lcom/facebook/ads/redexgen/X/3h;->A0s:[B

    return-void

    :array_0
    .array-data 1
        -0x63t
        -0x46t
        -0x45t
        -0x63t
        -0x3et
        -0xet
        0x3t
        0x9t
        0x7t
        0x14t
        -0x3et
        0x5t
        0xet
        0x3t
        0x15t
        0x15t
        -0x24t
        -0x3et
        -0x30t
        0x0t
        0x11t
        0x17t
        0x15t
        0x22t
        -0x30t
        0x19t
        0x14t
        -0x16t
        -0x30t
        -0x22t
        0xet
        0x30t
        0x2dt
        0x20t
        0x2at
        0x23t
        0x2bt
        0x1ft
        0x32t
        0x27t
        0x21t
        -0x22t
        0x1ft
        0x22t
        0x1ft
        0x2et
        0x32t
        0x23t
        0x30t
        -0x8t
        -0x22t
        -0x1et
        0x36t
        0x31t
        0x31t
        -0x1et
        0x35t
        0x2ft
        0x23t
        0x2et
        0x2et
        -0x3t
        -0x1et
        0x26t
        0x27t
        0x28t
        0x23t
        0x37t
        0x2et
        0x36t
        0x2bt
        0x30t
        0x29t
        -0x1et
        0x36t
        0x31t
        -0x1et
        -0x42t
        -0x4et
        -0x8t
        0x1t
        0x7t
        0x0t
        -0xat
        -0x34t
        -0x4et
        -0x45t
        -0x27t
        -0x1at
        -0x1at
        -0x19t
        -0x14t
        -0x68t
        -0x27t
        -0x24t
        -0x24t
        -0x68t
        -0x18t
        -0x27t
        -0x21t
        -0x23t
        -0x16t
        -0x68t
        -0x24t
        -0x23t
        -0x25t
        -0x19t
        -0x16t
        -0x68t
        -0x12t
        -0x1ft
        -0x23t
        -0x11t
        -0x68t
        -0x24t
        -0x13t
        -0x16t
        -0x1ft
        -0x1at
        -0x21t
        -0x68t
        -0x1ct
        -0x27t
        -0xft
        -0x19t
        -0x13t
        -0x14t
        -0xat
        0x9t
        0x15t
        0x19t
        0x9t
        0x17t
        0x18t
        0x9t
        0x8t
        -0x3ct
        0x13t
        0xat
        0xat
        0x17t
        0x7t
        0x16t
        0x9t
        0x9t
        0x12t
        -0x3ct
        0x14t
        0x5t
        0xbt
        0x9t
        -0x3ct
        0x10t
        0xdt
        0x11t
        0xdt
        0x18t
        -0x3ct
        -0xat
        0xat
        0x7t
        -0x3et
        0x3t
        0x12t
        0x12t
        0xet
        0xbt
        0x5t
        0x3t
        0x16t
        0xbt
        0x11t
        0x10t
        -0x37t
        0x15t
        -0x3et
        -0xet
        0x3t
        0x9t
        0x7t
        0x14t
        -0x1dt
        0x6t
        0x3t
        0x12t
        0x16t
        0x7t
        0x14t
        -0x3et
        0x5t
        0xat
        0x3t
        0x10t
        0x9t
        0x7t
        0x6t
        -0x3et
        0x16t
        0xat
        0x7t
        -0x3et
        0x3t
        0x6t
        0x3t
        0x12t
        0x16t
        0x7t
        0x14t
        -0x37t
        0x15t
        -0x3et
        0x5t
        0x11t
        0x10t
        0x16t
        0x7t
        0x10t
        0x16t
        0x15t
        -0x3et
        0x19t
        0xbt
        0x16t
        0xat
        0x11t
        0x17t
        0x16t
        -0x3et
        0x5t
        0x3t
        0xet
        0xet
        0xbt
        0x10t
        0x9t
        -0x3et
        -0xet
        0x3t
        0x9t
        0x7t
        0x14t
        -0x1dt
        0x6t
        0x3t
        0x12t
        0x16t
        0x7t
        0x14t
        -0x3bt
        0x10t
        0x11t
        0x16t
        0xbt
        0x8t
        0x1bt
        -0x1at
        0x3t
        0x16t
        0x3t
        -0xbt
        0x7t
        0x16t
        -0x1bt
        0xat
        0x3t
        0x10t
        0x9t
        0x7t
        0x6t
        -0x3dt
        -0x3et
        -0x19t
        0x1at
        0x12t
        0x7t
        0x5t
        0x16t
        0x7t
        0x6t
        -0x3et
        0x3t
        0x6t
        0x3t
        0x12t
        0x16t
        0x7t
        0x14t
        -0x3et
        0xbt
        0x16t
        0x7t
        0xft
        -0x3et
        0x5t
        0x11t
        0x17t
        0x10t
        0x16t
        -0x24t
        -0x3et
        -0x13t
        0x0t
        -0x4t
        0xet
        -0x19t
        -0x8t
        -0x2t
        -0x4t
        0x9t
        0x19t
        0x2at
        0x2at
        0x27t
        0x2ft
        0xbt
        0x1bt
        0x2at
        0x27t
        0x24t
        0x24t
        -0x28t
        0x2ct
        0x2at
        0x21t
        0x1dt
        0x1ct
        -0x28t
        0x2ct
        0x27t
        -0x28t
        0x1et
        0x21t
        0x26t
        0x1ct
        -0x28t
        0x1et
        0x27t
        0x1bt
        0x2dt
        0x2bt
        -0x28t
        0x1at
        0x19t
        0x2bt
        0x1dt
        0x1ct
        -0x28t
        0x27t
        0x26t
        -0x28t
        0x26t
        0x27t
        0x26t
        -0x1bt
        0x1bt
        0x20t
        0x21t
        0x24t
        0x1ct
        -0x28t
        0x1bt
        0x2dt
        0x2at
        0x2at
        0x1dt
        0x26t
        0x2ct
        -0x28t
        0x1et
        0x27t
        0x1bt
        0x2dt
        0x2bt
        0x1dt
        0x1ct
        -0x28t
        0x2et
        0x21t
        0x1dt
        0x2ft
        -0x28t
        0x4ct
        0x4bt
        0x1et
        0x41t
        0x3et
        0x4dt
        0x51t
        0x42t
        0x4ft
        0x20t
        0x45t
        0x3et
        0x4bt
        0x44t
        0x42t
        0x41t
        0x13t
        0x12t
        -0xct
        0x5t
        0xbt
        0x9t
        -0x9t
        0x7t
        0x16t
        0x13t
        0x10t
        0x10t
        0x9t
        0x8t
        -0x3ct
        0x8t
        0xdt
        0x8t
        -0x3ct
        0x12t
        0x13t
        0x18t
        -0x3ct
        0x7t
        0x5t
        0x10t
        0x10t
        -0x3ct
        0x17t
        0x19t
        0x14t
        0x9t
        0x16t
        0x7t
        0x10t
        0x5t
        0x17t
        0x17t
        -0x3ct
        0xdt
        0x11t
        0x14t
        0x10t
        0x9t
        0x11t
        0x9t
        0x12t
        0x18t
        0x5t
        0x18t
        0xdt
        0x13t
        0x12t
        0x55t
        0x53t
        0x42t
        0x4ft
        0x54t
        0x47t
        0x50t
        0x53t
        0x4et
        0x31t
        0x42t
        0x48t
        0x46t
    .end array-data
.end method

.method private final A0D()V
    .locals 5

    .line 8239
    const/4 v0, 0x0

    invoke-virtual {p0, v0}, Lcom/facebook/ads/redexgen/X/3h;->setWillNotDraw(Z)V

    .line 8240
    const/high16 v0, 0x40000

    invoke-virtual {p0, v0}, Lcom/facebook/ads/redexgen/X/3h;->setDescendantFocusability(I)V

    .line 8241
    const/4 v1, 0x1

    invoke-virtual {p0, v1}, Lcom/facebook/ads/redexgen/X/3h;->setFocusable(Z)V

    .line 8242
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/3h;->getContext()Landroid/content/Context;

    move-result-object v4

    .line 8243
    .local v1, "context":Landroid/content/Context;
    sget-object v2, Lcom/facebook/ads/redexgen/X/3h;->A0v:Landroid/view/animation/Interpolator;

    new-instance v0, Landroid/widget/Scroller;

    invoke-direct {v0, v4, v2}, Landroid/widget/Scroller;-><init>(Landroid/content/Context;Landroid/view/animation/Interpolator;)V

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0X:Landroid/widget/Scroller;

    .line 8244
    invoke-static {v4}, Landroid/view/ViewConfiguration;->get(Landroid/content/Context;)Landroid/view/ViewConfiguration;

    move-result-object v3

    .line 8245
    .local v2, "configuration":Landroid/view/ViewConfiguration;
    invoke-virtual {v4}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    move-result-object v0

    invoke-virtual {v0}, Landroid/content/res/Resources;->getDisplayMetrics()Landroid/util/DisplayMetrics;

    move-result-object v0

    iget v2, v0, Landroid/util/DisplayMetrics;->density:F

    .line 8246
    .local v3, "density":F
    invoke-virtual {v3}, Landroid/view/ViewConfiguration;->getScaledPagingTouchSlop()I

    move-result v0

    iput v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0R:I

    .line 8247
    const/high16 v0, 0x43c80000    # 400.0f

    mul-float/2addr v0, v2

    float-to-int v0, v0

    iput v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0K:I

    .line 8248
    invoke-virtual {v3}, Landroid/view/ViewConfiguration;->getScaledMaximumFlingVelocity()I

    move-result v0

    iput v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0J:I

    .line 8249
    new-instance v0, Landroid/widget/EdgeEffect;

    invoke-direct {v0, v4}, Landroid/widget/EdgeEffect;-><init>(Landroid/content/Context;)V

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0V:Landroid/widget/EdgeEffect;

    .line 8250
    new-instance v0, Landroid/widget/EdgeEffect;

    invoke-direct {v0, v4}, Landroid/widget/EdgeEffect;-><init>(Landroid/content/Context;)V

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0W:Landroid/widget/EdgeEffect;

    .line 8251
    const/high16 v0, 0x41c80000    # 25.0f

    mul-float/2addr v0, v2

    float-to-int v0, v0

    iput v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0H:I

    .line 8252
    const/high16 v0, 0x40000000    # 2.0f

    mul-float/2addr v0, v2

    float-to-int v0, v0

    iput v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0C:I

    .line 8253
    const/high16 v0, 0x41800000    # 16.0f

    mul-float/2addr v0, v2

    float-to-int v0, v0

    iput v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0E:I

    .line 8254
    new-instance v0, Lcom/facebook/ads/redexgen/X/Zz;

    invoke-direct {v0, p0}, Lcom/facebook/ads/redexgen/X/Zz;-><init>(Lcom/facebook/ads/redexgen/X/3h;)V

    invoke-static {p0, v0}, Lcom/facebook/ads/redexgen/X/3T;->A0B(Landroid/view/View;Lcom/facebook/ads/redexgen/X/37;)V

    .line 8255
    invoke-static {p0}, Lcom/facebook/ads/redexgen/X/3T;->A00(Landroid/view/View;)I

    move-result v0

    if-nez v0, :cond_0

    .line 8256
    invoke-static {p0, v1}, Lcom/facebook/ads/redexgen/X/3T;->A09(Landroid/view/View;I)V

    .line 8257
    :cond_0
    new-instance v0, Lcom/facebook/ads/redexgen/X/a0;

    invoke-direct {v0, p0}, Lcom/facebook/ads/redexgen/X/a0;-><init>(Lcom/facebook/ads/redexgen/X/3h;)V

    invoke-static {p0, v0}, Lcom/facebook/ads/redexgen/X/3T;->A0C(Landroid/view/View;Lcom/facebook/ads/redexgen/X/3D;)V

    .line 8258
    return-void
.end method

.method private A0E(I)V
    .locals 3

    .line 8259
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0d:Ljava/util/List;

    if-eqz v0, :cond_0

    .line 8260
    const/4 v2, 0x0

    .local v1, "i":I
    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v1

    .local v0, "z":I
    :goto_0
    if-ge v2, v1, :cond_0

    .line 8261
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0d:Ljava/util/List;

    invoke-interface {v0, v2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 8262
    .local v2, "listener":Lcom/facebook/ads/redexgen/X/3c;
    .end local v2    # "listener":Lcom/facebook/ads/redexgen/X/3c;
    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    .line 8263
    :cond_0
    return-void
.end method

.method private A0F(I)V
    .locals 3

    .line 8264
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0d:Ljava/util/List;

    if-eqz v0, :cond_0

    .line 8265
    const/4 v2, 0x0

    .local v1, "i":I
    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v1

    .local v0, "z":I
    :goto_0
    if-ge v2, v1, :cond_0

    .line 8266
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0d:Ljava/util/List;

    invoke-interface {v0, v2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 8267
    .local v2, "listener":Lcom/facebook/ads/redexgen/X/3c;
    .end local v2    # "listener":Lcom/facebook/ads/redexgen/X/3c;
    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    .line 8268
    :cond_0
    return-void
.end method

.method private final A0G(I)V
    .locals 16

    .line 8269
    move-object/from16 v3, p0

    const/4 v8, 0x0

    .line 8270
    .local v0, "oldCurInfo":Lcom/facebook/ads/redexgen/X/3Z;
    iget v0, v3, Lcom/facebook/ads/redexgen/X/3h;->A00:I

    move/from16 v1, p1

    if-eq v0, v1, :cond_0

    .line 8271
    invoke-direct {v3, v0}, Lcom/facebook/ads/redexgen/X/3h;->A04(I)Lcom/facebook/ads/redexgen/X/3Z;

    move-result-object v8

    .line 8272
    iput v1, v3, Lcom/facebook/ads/redexgen/X/3h;->A00:I

    .line 8273
    .end local v0    # "oldCurInfo":Lcom/facebook/ads/redexgen/X/3Z;
    .local v3, "oldCurInfo":Lcom/facebook/ads/redexgen/X/3Z;
    :cond_0
    iget-object v0, v3, Lcom/facebook/ads/redexgen/X/3h;->A01:Lcom/facebook/ads/redexgen/X/3E;

    if-nez v0, :cond_1

    .line 8274
    invoke-direct/range {p0 .. p0}, Lcom/facebook/ads/redexgen/X/3h;->A0B()V

    .line 8275
    return-void

    .line 8276
    :cond_1
    iget-boolean v0, v3, Lcom/facebook/ads/redexgen/X/3h;->A0m:Z

    if-eqz v0, :cond_2

    .line 8277
    invoke-direct/range {p0 .. p0}, Lcom/facebook/ads/redexgen/X/3h;->A0B()V

    .line 8278
    return-void

    .line 8279
    :cond_2
    invoke-virtual/range {p0 .. p0}, Lcom/facebook/ads/redexgen/X/3h;->getWindowToken()Landroid/os/IBinder;

    move-result-object v0

    if-nez v0, :cond_3

    .line 8280
    return-void

    .line 8281
    :cond_3
    iget v2, v3, Lcom/facebook/ads/redexgen/X/3h;->A0L:I

    .line 8282
    .local v4, "pageLimit":I
    iget v1, v3, Lcom/facebook/ads/redexgen/X/3h;->A00:I

    sub-int/2addr v1, v2

    const/4 v0, 0x0

    invoke-static {v0, v1}, Ljava/lang/Math;->max(II)I

    move-result v10

    .line 8283
    .local v5, "startPos":I
    iget-object v0, v3, Lcom/facebook/ads/redexgen/X/3h;->A01:Lcom/facebook/ads/redexgen/X/3E;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/3E;->A01()I

    move-result v5

    .line 8284
    .local v6, "N":I
    add-int/lit8 v1, v5, -0x1

    iget v0, v3, Lcom/facebook/ads/redexgen/X/3h;->A00:I

    add-int/2addr v0, v2

    invoke-static {v1, v0}, Ljava/lang/Math;->min(II)I

    move-result v7

    .line 8285
    .local v7, "endPos":I
    iget v0, v3, Lcom/facebook/ads/redexgen/X/3h;->A0G:I

    if-ne v5, v0, :cond_1b

    .line 8286
    .local v0, "curIndex":I
    const/4 v6, 0x0

    sget-object v1, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const/4 v0, 0x2

    aget-object v0, v1, v0

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v1

    const/16 v0, 0xe

    if-eq v1, v0, :cond_1d

    .line 8287
    .local v8, "curItem":Lcom/facebook/ads/redexgen/X/3Z;
    sget-object v2, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const-string v1, "Tot0RAE"

    const/4 v0, 0x1

    aput-object v1, v2, v0

    const/4 v4, 0x0

    :goto_0
    iget-object v0, v3, Lcom/facebook/ads/redexgen/X/3h;->A0r:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->size()I

    move-result v0

    if-ge v4, v0, :cond_4

    .line 8288
    iget-object v0, v3, Lcom/facebook/ads/redexgen/X/3h;->A0r:Ljava/util/ArrayList;

    invoke-virtual {v0, v4}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/facebook/ads/redexgen/X/3Z;

    .line 8289
    .local v9, "ii":Lcom/facebook/ads/redexgen/X/3Z;
    iget v1, v2, Lcom/facebook/ads/redexgen/X/3Z;->A02:I

    iget v0, v3, Lcom/facebook/ads/redexgen/X/3h;->A00:I

    if-lt v1, v0, :cond_1a

    .line 8290
    iget v1, v2, Lcom/facebook/ads/redexgen/X/3Z;->A02:I

    iget v0, v3, Lcom/facebook/ads/redexgen/X/3h;->A00:I

    if-ne v1, v0, :cond_4

    move-object v6, v2

    .line 8291
    :cond_4
    if-nez v6, :cond_5

    if-lez v5, :cond_5

    .line 8292
    iget v6, v3, Lcom/facebook/ads/redexgen/X/3h;->A00:I

    sget-object v1, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const/4 v0, 0x1

    aget-object v0, v1, v0

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v1

    const/16 v0, 0xc

    if-eq v1, v0, :cond_19

    sget-object v2, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const-string v1, "0"

    const/4 v0, 0x2

    aput-object v1, v2, v0

    invoke-direct {v3, v6, v4}, Lcom/facebook/ads/redexgen/X/3h;->A05(II)Lcom/facebook/ads/redexgen/X/3Z;

    move-result-object v6

    .line 8293
    :cond_5
    :goto_1
    if-eqz v6, :cond_8

    .line 8294
    const/4 v15, 0x0

    .line 8295
    .local v11, "extraWidthLeft":F
    add-int/lit8 v9, v4, -0x1

    .line 8296
    .local v12, "itemIndex":I
    if-ltz v9, :cond_18

    iget-object v0, v3, Lcom/facebook/ads/redexgen/X/3h;->A0r:Ljava/util/ArrayList;

    invoke-virtual {v0, v9}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v11

    sget-object v2, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const/4 v0, 0x3

    aget-object v1, v2, v0

    const/4 v0, 0x6

    aget-object v2, v2, v0

    const/16 v0, 0xf

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    invoke-virtual {v2, v0}, Ljava/lang/String;->charAt(I)C

    move-result v0

    if-eq v1, v0, :cond_1d

    sget-object v2, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const-string v1, "VNMCyWdkMCax4jTa2KYBa9F64T8dlp"

    const/4 v0, 0x1

    aput-object v1, v2, v0

    check-cast v11, Lcom/facebook/ads/redexgen/X/3Z;

    .line 8297
    .local v13, "ii":Lcom/facebook/ads/redexgen/X/3Z;
    :goto_2
    invoke-direct/range {p0 .. p0}, Lcom/facebook/ads/redexgen/X/3h;->getClientWidth()I

    move-result v13

    .line 8298
    .local v14, "clientWidth":I
    const/high16 v14, 0x40000000    # 2.0f

    if-gtz v13, :cond_17

    const/4 v14, 0x0

    .line 8299
    .local v9, "leftWidthNeeded":F
    :goto_3
    iget v0, v3, Lcom/facebook/ads/redexgen/X/3h;->A00:I

    add-int/lit8 v12, v0, -0x1

    .local v10, "pos":I
    :goto_4
    if-ltz v12, :cond_6

    .line 8300
    cmpl-float v0, v15, v14

    if-ltz v0, :cond_1c

    if-ge v12, v10, :cond_1c

    .line 8301
    if-nez v11, :cond_15

    .line 8302
    .end local v10    # "pos":I
    :cond_6
    iget v12, v6, Lcom/facebook/ads/redexgen/X/3Z;->A01:F

    .line 8303
    .local v2, "extraWidthRight":F
    add-int/lit8 v11, v4, 0x1

    .line 8304
    .end local v12    # "itemIndex":I
    .local v10, "itemIndex":I
    const/high16 v0, 0x40000000    # 2.0f

    cmpg-float v0, v12, v0

    if-gez v0, :cond_7

    .line 8305
    iget-object v0, v3, Lcom/facebook/ads/redexgen/X/3h;->A0r:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->size()I

    move-result v0

    if-ge v11, v0, :cond_14

    iget-object v0, v3, Lcom/facebook/ads/redexgen/X/3h;->A0r:Ljava/util/ArrayList;

    invoke-virtual {v0, v11}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v10

    check-cast v10, Lcom/facebook/ads/redexgen/X/3Z;

    .line 8306
    .end local v13    # "ii":Lcom/facebook/ads/redexgen/X/3Z;
    .local v12, "ii":Lcom/facebook/ads/redexgen/X/3Z;
    :goto_5
    if-gtz v13, :cond_12

    const/4 v9, 0x0

    .line 8307
    .local v13, "rightWidthNeeded":F
    :goto_6
    iget v0, v3, Lcom/facebook/ads/redexgen/X/3h;->A00:I

    add-int/lit8 v1, v0, 0x1

    .local v15, "pos":I
    :goto_7
    if-ge v1, v5, :cond_7

    .line 8308
    cmpl-float v0, v12, v9

    if-ltz v0, :cond_d

    if-le v1, v7, :cond_d

    .line 8309
    if-nez v10, :cond_b

    .line 8310
    .end local v4    # "pageLimit":I
    .end local v5    # "startPos":I
    .restart local p0    # "this":Lcom/facebook/ads/redexgen/X/3h;
    .restart local p1    # null:I
    :cond_7
    invoke-direct {v3, v6, v4, v8}, Lcom/facebook/ads/redexgen/X/3h;->A0Q(Lcom/facebook/ads/redexgen/X/3Z;ILcom/facebook/ads/redexgen/X/3Z;)V

    .line 8311
    .end local v4
    .end local v5
    .restart local p0    # "this":Lcom/facebook/ads/redexgen/X/3h;
    .restart local p1    # null:I
    :cond_8
    invoke-virtual/range {p0 .. p0}, Lcom/facebook/ads/redexgen/X/3h;->getChildCount()I

    move-result v6

    .line 8312
    .local v2, "childCount":I
    const/4 v5, 0x0

    .local v4, "i":I
    :goto_8
    if-ge v5, v6, :cond_23

    .line 8313
    invoke-virtual {v3, v5}, Lcom/facebook/ads/redexgen/X/3h;->getChildAt(I)Landroid/view/View;

    move-result-object v2

    .line 8314
    .local v5, "child":Landroid/view/View;
    invoke-virtual {v2}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    move-result-object v4

    check-cast v4, Lcom/facebook/ads/redexgen/X/3a;

    .line 8315
    .local v9, "lp":Lcom/facebook/ads/redexgen/X/3a;
    iput v5, v4, Lcom/facebook/ads/redexgen/X/3a;->A01:I

    .line 8316
    iget-boolean v0, v4, Lcom/facebook/ads/redexgen/X/3a;->A05:Z

    if-nez v0, :cond_9

    iget v1, v4, Lcom/facebook/ads/redexgen/X/3a;->A00:F

    const/4 v0, 0x0

    cmpl-float v0, v1, v0

    if-nez v0, :cond_9

    .line 8317
    invoke-direct {v3, v2}, Lcom/facebook/ads/redexgen/X/3h;->A07(Landroid/view/View;)Lcom/facebook/ads/redexgen/X/3Z;

    move-result-object v7

    sget-object v2, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const/4 v0, 0x7

    aget-object v1, v2, v0

    const/4 v0, 0x0

    aget-object v2, v2, v0

    const/16 v0, 0xa

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    invoke-virtual {v2, v0}, Ljava/lang/String;->charAt(I)C

    move-result v0

    if-eq v1, v0, :cond_a

    .line 8318
    .local v10, "ii":Lcom/facebook/ads/redexgen/X/3Z;
    if-eqz v7, :cond_9

    .line 8319
    :goto_9
    iget v0, v7, Lcom/facebook/ads/redexgen/X/3Z;->A01:F

    iput v0, v4, Lcom/facebook/ads/redexgen/X/3a;->A00:F

    .line 8320
    iget v0, v7, Lcom/facebook/ads/redexgen/X/3Z;->A02:I

    iput v0, v4, Lcom/facebook/ads/redexgen/X/3a;->A02:I

    .line 8321
    .end local v5    # "child":Landroid/view/View;
    .end local v9    # "lp":Lcom/facebook/ads/redexgen/X/3a;
    :cond_9
    add-int/lit8 v5, v5, 0x1

    goto :goto_8

    .line 8322
    .local v10, "ii":Lcom/facebook/ads/redexgen/X/3Z;
    :cond_a
    sget-object v2, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const-string v1, "EEa7ILjnbFiKaMBJBW0FEjvS9loAIL"

    const/4 v0, 0x1

    aput-object v1, v2, v0

    if-eqz v7, :cond_9

    goto :goto_9

    .line 8323
    .end local v4    # "i":I
    .local p0, "pageLimit":I
    :cond_b
    iget v0, v10, Lcom/facebook/ads/redexgen/X/3Z;->A02:I

    if-ne v1, v0, :cond_e

    iget-boolean v0, v10, Lcom/facebook/ads/redexgen/X/3Z;->A04:Z

    if-nez v0, :cond_e

    .line 8324
    iget-object v0, v3, Lcom/facebook/ads/redexgen/X/3h;->A0r:Ljava/util/ArrayList;

    invoke-virtual {v0, v11}, Ljava/util/ArrayList;->remove(I)Ljava/lang/Object;

    .line 8325
    iget-object v2, v3, Lcom/facebook/ads/redexgen/X/3h;->A01:Lcom/facebook/ads/redexgen/X/3E;

    .end local v5
    .local p1, "startPos":I
    iget-object v0, v10, Lcom/facebook/ads/redexgen/X/3Z;->A03:Ljava/lang/Object;

    invoke-virtual {v2, v3, v1, v0}, Lcom/facebook/ads/redexgen/X/3E;->A07(Landroid/view/ViewGroup;ILjava/lang/Object;)V

    .line 8326
    iget-object v0, v3, Lcom/facebook/ads/redexgen/X/3h;->A0r:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->size()I

    move-result v0

    if-ge v11, v0, :cond_c

    iget-object v0, v3, Lcom/facebook/ads/redexgen/X/3h;->A0r:Ljava/util/ArrayList;

    invoke-virtual {v0, v11}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v10

    check-cast v10, Lcom/facebook/ads/redexgen/X/3Z;

    goto :goto_a

    :cond_c
    const/4 v10, 0x0

    goto :goto_a

    .line 8327
    .end local p0    # "pageLimit":I
    .end local p1    # "startPos":I
    .restart local v4    # "i":I
    .restart local v5    # "child":Landroid/view/View;
    .end local v4    # "i":I
    .end local v5    # "child":Landroid/view/View;
    .restart local p0    # "pageLimit":I
    .restart local p1    # "startPos":I
    :cond_d
    if-eqz v10, :cond_10

    iget v0, v10, Lcom/facebook/ads/redexgen/X/3Z;->A02:I

    if-ne v1, v0, :cond_10

    .line 8328
    iget v0, v10, Lcom/facebook/ads/redexgen/X/3Z;->A01:F

    add-float/2addr v12, v0

    .line 8329
    add-int/lit8 v11, v11, 0x1

    .line 8330
    iget-object v0, v3, Lcom/facebook/ads/redexgen/X/3h;->A0r:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->size()I

    move-result v0

    if-ge v11, v0, :cond_f

    iget-object v0, v3, Lcom/facebook/ads/redexgen/X/3h;->A0r:Ljava/util/ArrayList;

    invoke-virtual {v0, v11}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v10

    check-cast v10, Lcom/facebook/ads/redexgen/X/3Z;

    .line 8331
    .end local v4
    .restart local v12    # "ii":Lcom/facebook/ads/redexgen/X/3Z;
    :cond_e
    :goto_a
    add-int/lit8 v1, v1, 0x1

    goto/16 :goto_7

    .line 8332
    :cond_f
    const/4 v10, 0x0

    goto :goto_a

    .line 8333
    :cond_10
    invoke-direct {v3, v1, v11}, Lcom/facebook/ads/redexgen/X/3h;->A05(II)Lcom/facebook/ads/redexgen/X/3Z;

    move-result-object v0

    .line 8334
    .end local v12    # "ii":Lcom/facebook/ads/redexgen/X/3Z;
    .local v4, "ii":Lcom/facebook/ads/redexgen/X/3Z;
    add-int/lit8 v11, v11, 0x1

    .line 8335
    iget v0, v0, Lcom/facebook/ads/redexgen/X/3Z;->A01:F

    add-float/2addr v12, v0

    .line 8336
    iget-object v0, v3, Lcom/facebook/ads/redexgen/X/3h;->A0r:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->size()I

    move-result v0

    if-ge v11, v0, :cond_11

    iget-object v0, v3, Lcom/facebook/ads/redexgen/X/3h;->A0r:Ljava/util/ArrayList;

    invoke-virtual {v0, v11}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v10

    check-cast v10, Lcom/facebook/ads/redexgen/X/3Z;

    goto :goto_a

    :cond_11
    const/4 v10, 0x0

    goto :goto_a

    .line 8337
    :cond_12
    invoke-virtual/range {p0 .. p0}, Lcom/facebook/ads/redexgen/X/3h;->getPaddingRight()I

    move-result v0

    int-to-float v9, v0

    int-to-float v13, v13

    sget-object v2, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const/4 v0, 0x4

    aget-object v1, v2, v0

    const/4 v0, 0x5

    aget-object v0, v2, v0

    invoke-virtual {v1}, Ljava/lang/String;->length()I

    move-result v1

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v0

    if-eq v1, v0, :cond_13

    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    :cond_13
    sget-object v2, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const-string v1, ""

    const/4 v0, 0x4

    aput-object v1, v2, v0

    const-string v1, ""

    const/4 v0, 0x5

    aput-object v1, v2, v0

    div-float/2addr v9, v13

    const/high16 v0, 0x40000000    # 2.0f

    add-float/2addr v9, v0

    goto/16 :goto_6

    .line 8338
    :cond_14
    const/4 v10, 0x0

    goto/16 :goto_5

    .line 8339
    :cond_15
    iget v0, v11, Lcom/facebook/ads/redexgen/X/3Z;->A02:I

    if-ne v12, v0, :cond_21

    iget-boolean v0, v11, Lcom/facebook/ads/redexgen/X/3Z;->A04:Z

    if-nez v0, :cond_21

    .line 8340
    iget-object v0, v3, Lcom/facebook/ads/redexgen/X/3h;->A0r:Ljava/util/ArrayList;

    invoke-virtual {v0, v9}, Ljava/util/ArrayList;->remove(I)Ljava/lang/Object;

    .line 8341
    iget-object v1, v3, Lcom/facebook/ads/redexgen/X/3h;->A01:Lcom/facebook/ads/redexgen/X/3E;

    iget-object v0, v11, Lcom/facebook/ads/redexgen/X/3Z;->A03:Ljava/lang/Object;

    invoke-virtual {v1, v3, v12, v0}, Lcom/facebook/ads/redexgen/X/3E;->A07(Landroid/view/ViewGroup;ILjava/lang/Object;)V

    .line 8342
    add-int/lit8 v9, v9, -0x1

    .line 8343
    add-int/lit8 v4, v4, -0x1

    .line 8344
    if-ltz v9, :cond_16

    iget-object v0, v3, Lcom/facebook/ads/redexgen/X/3h;->A0r:Ljava/util/ArrayList;

    invoke-virtual {v0, v9}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v11

    check-cast v11, Lcom/facebook/ads/redexgen/X/3Z;

    goto/16 :goto_c

    :cond_16
    const/4 v11, 0x0

    goto/16 :goto_c

    .line 8345
    :cond_17
    iget v0, v6, Lcom/facebook/ads/redexgen/X/3Z;->A01:F

    sub-float/2addr v14, v0

    invoke-virtual/range {p0 .. p0}, Lcom/facebook/ads/redexgen/X/3h;->getPaddingLeft()I

    move-result v0

    int-to-float v1, v0

    int-to-float v0, v13

    div-float/2addr v1, v0

    add-float/2addr v14, v1

    goto/16 :goto_3

    .line 8346
    :cond_18
    const/4 v11, 0x0

    goto/16 :goto_2

    :cond_19
    sget-object v2, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const-string v1, ""

    const/4 v0, 0x4

    aput-object v1, v2, v0

    const-string v1, ""

    const/4 v0, 0x5

    aput-object v1, v2, v0

    invoke-direct {v3, v6, v4}, Lcom/facebook/ads/redexgen/X/3h;->A05(II)Lcom/facebook/ads/redexgen/X/3Z;

    move-result-object v6

    goto/16 :goto_1

    .line 8347
    .end local v9
    :cond_1a
    add-int/lit8 v4, v4, 0x1

    goto/16 :goto_0

    .line 8348
    .end local v4    # "ii":Lcom/facebook/ads/redexgen/X/3Z;
    .end local v5
    .restart local p0    # "pageLimit":I
    .restart local p1    # "startPos":I
    :cond_1b
    :try_start_0
    invoke-virtual/range {p0 .. p0}, Lcom/facebook/ads/redexgen/X/3h;->getResources()Landroid/content/res/Resources;

    move-result-object v1

    invoke-virtual/range {p0 .. p0}, Lcom/facebook/ads/redexgen/X/3h;->getId()I

    move-result v0

    invoke-virtual {v1, v0}, Landroid/content/res/Resources;->getResourceName(I)Ljava/lang/String;

    move-result-object v6

    goto :goto_b
    :try_end_0
    .catch Landroid/content/res/Resources$NotFoundException; {:try_start_0 .. :try_end_0} :catch_0

    .line 8349
    .end local v0    # "curIndex":I
    .local v0, "e":Landroid/content/res/Resources$NotFoundException;
    :catch_0
    invoke-virtual/range {p0 .. p0}, Lcom/facebook/ads/redexgen/X/3h;->getId()I

    move-result v0

    invoke-static {v0}, Ljava/lang/Integer;->toHexString(I)Ljava/lang/String;

    move-result-object v6

    sget-object v1, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const/4 v0, 0x2

    aget-object v0, v1, v0

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v1

    const/16 v0, 0xe

    if-eq v1, v0, :cond_1d

    sget-object v2, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const-string v1, "3cJ8G"

    const/4 v0, 0x2

    aput-object v1, v2, v0

    .line 8350
    .local v0, "resName":Ljava/lang/String;
    :goto_b
    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    const/16 v2, 0x9e

    const/16 v1, 0x8e

    const/16 v0, 0x3a

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/3h;->A08(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v4, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    iget v0, v3, Lcom/facebook/ads/redexgen/X/3h;->A0G:I

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v4

    const/16 v2, 0x4d

    const/16 v1, 0x9

    const/16 v0, 0x2a

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/3h;->A08(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v4, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, v5}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v4

    const/16 v2, 0x12

    const/16 v1, 0xb

    const/16 v0, 0x48

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/3h;->A08(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v4, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v4

    const/4 v2, 0x4

    const/16 v1, 0xe

    const/16 v0, 0x3a

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/3h;->A08(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v4, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    .line 8351
    invoke-virtual/range {p0 .. p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v4

    const/16 v2, 0x1d

    const/16 v1, 0x16

    const/16 v0, 0x56

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/3h;->A08(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v4, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    iget-object v0, v3, Lcom/facebook/ads/redexgen/X/3h;->A01:Lcom/facebook/ads/redexgen/X/3E;

    .line 8352
    invoke-virtual {v0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    new-instance v0, Ljava/lang/IllegalStateException;

    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw v0

    .line 8353
    .end local v2    # "childCount":I
    .restart local v13    # "rightWidthNeeded":F
    :cond_1c
    if-eqz v11, :cond_1e

    iget v0, v11, Lcom/facebook/ads/redexgen/X/3Z;->A02:I

    if-ne v12, v0, :cond_1e

    .line 8354
    iget v0, v11, Lcom/facebook/ads/redexgen/X/3Z;->A01:F

    add-float/2addr v15, v0

    sget-object v2, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const/4 v0, 0x7

    aget-object v1, v2, v0

    const/4 v0, 0x0

    aget-object v2, v2, v0

    const/16 v0, 0xa

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    invoke-virtual {v2, v0}, Ljava/lang/String;->charAt(I)C

    move-result v0

    if-eq v1, v0, :cond_20

    :cond_1d
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    .line 8355
    .end local v2
    .restart local v13    # "rightWidthNeeded":F
    :cond_1e
    add-int/lit8 v0, v9, 0x1

    invoke-direct {v3, v12, v0}, Lcom/facebook/ads/redexgen/X/3h;->A05(II)Lcom/facebook/ads/redexgen/X/3Z;

    move-result-object v0

    .line 8356
    .end local v13    # "rightWidthNeeded":F
    .restart local v2    # "childCount":I
    iget v0, v0, Lcom/facebook/ads/redexgen/X/3Z;->A01:F

    add-float/2addr v15, v0

    .line 8357
    add-int/lit8 v4, v4, 0x1

    .line 8358
    if-ltz v9, :cond_1f

    iget-object v0, v3, Lcom/facebook/ads/redexgen/X/3h;->A0r:Ljava/util/ArrayList;

    invoke-virtual {v0, v9}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v11

    check-cast v11, Lcom/facebook/ads/redexgen/X/3Z;

    goto :goto_c

    :cond_1f
    const/4 v11, 0x0

    goto :goto_c

    .line 8359
    :cond_20
    sget-object v2, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const-string v1, ""

    const/4 v0, 0x4

    aput-object v1, v2, v0

    const-string v1, ""

    const/4 v0, 0x5

    aput-object v1, v2, v0

    add-int/lit8 v9, v9, -0x1

    .line 8360
    if-ltz v9, :cond_22

    iget-object v0, v3, Lcom/facebook/ads/redexgen/X/3h;->A0r:Ljava/util/ArrayList;

    invoke-virtual {v0, v9}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v11

    check-cast v11, Lcom/facebook/ads/redexgen/X/3Z;

    .line 8361
    .end local v2    # "childCount":I
    .restart local v13    # "rightWidthNeeded":F
    :cond_21
    :goto_c
    add-int/lit8 v12, v12, -0x1

    goto/16 :goto_4

    .line 8362
    :cond_22
    const/4 v11, 0x0

    goto :goto_c

    .line 8363
    .end local v4
    :cond_23
    invoke-direct/range {p0 .. p0}, Lcom/facebook/ads/redexgen/X/3h;->A0B()V

    sget-object v2, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const/4 v0, 0x4

    aget-object v1, v2, v0

    const/4 v0, 0x5

    aget-object v0, v2, v0

    invoke-virtual {v1}, Ljava/lang/String;->length()I

    move-result v1

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v0

    if-eq v1, v0, :cond_24

    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    .line 8364
    :cond_24
    sget-object v2, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const-string v1, "fAvJTe45YQ8jwRLJzle6w88GhXZAW5fe"

    const/4 v0, 0x7

    aput-object v1, v2, v0

    const-string v1, "XHTmtVqtTM8ykl9XiQ4ZuPNQzfhQsr72"

    const/4 v0, 0x0

    aput-object v1, v2, v0

    invoke-virtual/range {p0 .. p0}, Lcom/facebook/ads/redexgen/X/3h;->hasFocus()Z

    move-result v0

    if-eqz v0, :cond_26

    .line 8365
    invoke-virtual/range {p0 .. p0}, Lcom/facebook/ads/redexgen/X/3h;->findFocus()Landroid/view/View;

    move-result-object v4

    sget-object v2, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const/4 v0, 0x3

    aget-object v1, v2, v0

    const/4 v0, 0x6

    aget-object v2, v2, v0

    const/16 v0, 0xf

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    invoke-virtual {v2, v0}, Ljava/lang/String;->charAt(I)C

    move-result v0

    if-eq v1, v0, :cond_29

    .line 8366
    .local v4, "currentFocused":Landroid/view/View;
    sget-object v2, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const-string v1, "98nQxxcA6vj5924MyChW7mkeiNJnWKv2"

    const/4 v0, 0x3

    aput-object v1, v2, v0

    const-string v1, "ju5RZqhAYuaQhiOPGNIJcLOaoEeV3OP8"

    const/4 v0, 0x6

    aput-object v1, v2, v0

    if-eqz v4, :cond_28

    invoke-direct {v3, v4}, Lcom/facebook/ads/redexgen/X/3h;->A06(Landroid/view/View;)Lcom/facebook/ads/redexgen/X/3Z;

    move-result-object v0

    .line 8367
    .local v5, "ii":Lcom/facebook/ads/redexgen/X/3Z;
    :goto_d
    if-eqz v0, :cond_25

    iget v1, v0, Lcom/facebook/ads/redexgen/X/3Z;->A02:I

    iget v0, v3, Lcom/facebook/ads/redexgen/X/3h;->A00:I

    if-eq v1, v0, :cond_26

    .line 8368
    :cond_25
    const/4 v4, 0x0

    .local v9, "i":I
    :goto_e
    invoke-virtual/range {p0 .. p0}, Lcom/facebook/ads/redexgen/X/3h;->getChildCount()I

    move-result v0

    if-ge v4, v0, :cond_26

    .line 8369
    invoke-virtual {v3, v4}, Lcom/facebook/ads/redexgen/X/3h;->getChildAt(I)Landroid/view/View;

    move-result-object v2

    .line 8370
    .local v10, "child":Landroid/view/View;
    invoke-direct {v3, v2}, Lcom/facebook/ads/redexgen/X/3h;->A07(Landroid/view/View;)Lcom/facebook/ads/redexgen/X/3Z;

    move-result-object v0

    .line 8371
    if-eqz v0, :cond_27

    iget v1, v0, Lcom/facebook/ads/redexgen/X/3Z;->A02:I

    iget v0, v3, Lcom/facebook/ads/redexgen/X/3h;->A00:I

    if-ne v1, v0, :cond_27

    .line 8372
    const/4 v0, 0x2

    invoke-virtual {v2, v0}, Landroid/view/View;->requestFocus(I)Z

    move-result v0

    if-eqz v0, :cond_27

    .line 8373
    .end local v4    # "currentFocused":Landroid/view/View;
    .end local v5    # "ii":Lcom/facebook/ads/redexgen/X/3Z;
    .end local v9    # "i":I
    :cond_26
    return-void

    .line 8374
    .end local v10    # "child":Landroid/view/View;
    :cond_27
    add-int/lit8 v4, v4, 0x1

    goto :goto_e

    .line 8375
    :cond_28
    const/4 v0, 0x0

    goto :goto_d

    :cond_29
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0
.end method

.method private A0H(IFI)V
    .locals 3

    .line 8376
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0d:Ljava/util/List;

    if-eqz v0, :cond_0

    .line 8377
    const/4 v2, 0x0

    .local v1, "i":I
    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v1

    .local v0, "z":I
    :goto_0
    if-ge v2, v1, :cond_0

    .line 8378
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0d:Ljava/util/List;

    invoke-interface {v0, v2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 8379
    .local v2, "listener":Lcom/facebook/ads/redexgen/X/3c;
    .end local v2    # "listener":Lcom/facebook/ads/redexgen/X/3c;
    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    .line 8380
    :cond_0
    return-void
.end method

.method private final A0I(IFI)V
    .locals 9

    .line 8381
    iget v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0D:I

    if-lez v0, :cond_3

    .line 8382
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/3h;->getScrollX()I

    move-result v8

    .line 8383
    .local v0, "scrollX":I
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/3h;->getPaddingLeft()I

    move-result v3

    .line 8384
    .local v1, "paddingLeft":I
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/3h;->getPaddingRight()I

    move-result v7

    sget-object v1, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const/4 v0, 0x1

    aget-object v0, v1, v0

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v1

    const/16 v0, 0xc

    if-eq v1, v0, :cond_2

    .line 8385
    .local v2, "paddingRight":I
    sget-object v2, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const-string v1, "hSwONVQ9BwzgmNPK4tziO3X6uj4eQvsQ"

    const/4 v0, 0x3

    aput-object v1, v2, v0

    const-string v1, "gKhZoL4mhYlFRC9fFMXnjqdiW6EOuKfZ"

    const/4 v0, 0x6

    aput-object v1, v2, v0

    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/3h;->getWidth()I

    move-result v1

    .line 8386
    .local v3, "width":I
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/3h;->getChildCount()I

    move-result v0

    .line 8387
    .local v4, "childCount":I
    const/4 v4, 0x0

    .local v5, "i":I
    :goto_0
    if-ge v4, v0, :cond_3

    .line 8388
    invoke-virtual {p0, v4}, Lcom/facebook/ads/redexgen/X/3h;->getChildAt(I)Landroid/view/View;

    move-result-object v5

    .line 8389
    .local v6, "child":Landroid/view/View;
    invoke-virtual {v5}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    move-result-object v6

    check-cast v6, Lcom/facebook/ads/redexgen/X/3a;

    .line 8390
    .local v7, "lp":Lcom/facebook/ads/redexgen/X/3a;
    iget-boolean v2, v6, Lcom/facebook/ads/redexgen/X/3a;->A05:Z

    if-nez v2, :cond_1

    .line 8391
    .end local v6    # "child":Landroid/view/View;
    .end local v7    # "lp":Lcom/facebook/ads/redexgen/X/3a;
    .end local v8
    .end local p0    # "this":Lcom/facebook/ads/redexgen/X/3h;
    .end local p1    # null:I
    :cond_0
    :goto_1
    add-int/lit8 v4, v4, 0x1

    goto :goto_0

    .line 8392
    :cond_1
    iget v2, v6, Lcom/facebook/ads/redexgen/X/3a;->A04:I

    and-int/lit8 v2, v2, 0x7

    .line 8393
    .local v8, "hgrav":I
    .local p0, "childLeft":I
    packed-switch v2, :pswitch_data_0

    .line 8394
    :pswitch_0
    move v6, v3

    .line 8395
    .end local p0    # "childLeft":I
    .local p1, "childLeft":I
    :goto_2
    add-int/2addr v6, v8

    .line 8396
    invoke-virtual {v5}, Landroid/view/View;->getLeft()I

    move-result v2

    sub-int/2addr v6, v2

    .line 8397
    .local p0, "childOffset":I
    if-eqz v6, :cond_0

    .line 8398
    invoke-virtual {v5, v6}, Landroid/view/View;->offsetLeftAndRight(I)V

    goto :goto_1

    .line 8399
    .end local p1    # "childLeft":I
    .restart local p0    # "childOffset":I
    :pswitch_1
    sub-int v6, v1, v7

    invoke-virtual {v5}, Landroid/view/View;->getMeasuredWidth()I

    move-result v2

    sub-int/2addr v6, v2

    .line 8400
    .end local p0    # "childOffset":I
    .restart local p1    # "childLeft":I
    invoke-virtual {v5}, Landroid/view/View;->getMeasuredWidth()I

    move-result v2

    add-int/2addr v7, v2

    goto :goto_2

    .line 8401
    .end local p1    # "childLeft":I
    .restart local p0    # "childOffset":I
    :pswitch_2
    move v6, v3

    .line 8402
    .end local p0    # "childOffset":I
    .restart local p1    # "childLeft":I
    invoke-virtual {v5}, Landroid/view/View;->getWidth()I

    move-result v2

    add-int/2addr v3, v2

    .line 8403
    goto :goto_2

    .line 8404
    .end local p1    # "childLeft":I
    .restart local p0    # "childOffset":I
    :pswitch_3
    invoke-virtual {v5}, Landroid/view/View;->getMeasuredWidth()I

    move-result v2

    sub-int v2, v1, v2

    div-int/lit8 v2, v2, 0x2

    invoke-static {v2, v3}, Ljava/lang/Math;->max(II)I

    move-result v6

    .line 8405
    .end local p0    # "childOffset":I
    .restart local p1    # "childLeft":I
    goto :goto_2

    .line 8406
    :cond_2
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    .line 8407
    .end local v0    # "scrollX":I
    .end local v1    # "paddingLeft":I
    .end local v2    # "paddingRight":I
    .end local v3    # "width":I
    .end local v4    # "childCount":I
    .end local v5    # "i":I
    :cond_3
    invoke-direct {p0, p1, p2, p3}, Lcom/facebook/ads/redexgen/X/3h;->A0H(IFI)V

    .line 8408
    const/4 v0, 0x0

    if-eqz v0, :cond_5

    .line 8409
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/3h;->getScrollX()I

    .line 8410
    .restart local v0    # "scrollX":I
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/3h;->getChildCount()I

    move-result v3

    .line 8411
    .local v1, "childCount":I
    const/4 v2, 0x0

    .local v2, "i":I
    :goto_3
    if-ge v2, v3, :cond_5

    .line 8412
    invoke-virtual {p0, v2}, Lcom/facebook/ads/redexgen/X/3h;->getChildAt(I)Landroid/view/View;

    move-result-object v1

    .line 8413
    .local v3, "child":Landroid/view/View;
    invoke-virtual {v1}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    move-result-object v0

    check-cast v0, Lcom/facebook/ads/redexgen/X/3a;

    .line 8414
    .local v4, "lp":Lcom/facebook/ads/redexgen/X/3a;
    iget-boolean v0, v0, Lcom/facebook/ads/redexgen/X/3a;->A05:Z

    if-eqz v0, :cond_4

    .line 8415
    .end local v3    # "child":Landroid/view/View;
    .end local v4    # "lp":Lcom/facebook/ads/redexgen/X/3a;
    .end local v5
    add-int/lit8 v2, v2, 0x1

    goto :goto_3

    .line 8416
    :cond_4
    invoke-virtual {v1}, Landroid/view/View;->getLeft()I

    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/3h;->getClientWidth()I

    .line 8417
    .local v5, "transformPos":F
    const/16 v2, 0x1c2

    const/16 v1, 0xd

    const/16 v0, 0x79

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/3h;->A08(III)Ljava/lang/String;

    move-result-object v1

    new-instance v0, Ljava/lang/NullPointerException;

    invoke-direct {v0, v1}, Ljava/lang/NullPointerException;-><init>(Ljava/lang/String;)V

    throw v0

    .line 8418
    .end local v0    # "scrollX":I
    .end local v1    # "childCount":I
    .end local v2    # "i":I
    :cond_5
    const/4 v0, 0x1

    iput-boolean v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0e:Z

    .line 8419
    return-void

    nop

    :pswitch_data_0
    .packed-switch 0x1
        :pswitch_3
        :pswitch_0
        :pswitch_2
        :pswitch_0
        :pswitch_1
    .end packed-switch
.end method

.method private final A0J(III)V
    .locals 13

    .line 8420
    move v11, p2

    move v10, p1

    move-object v4, p0

    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/3h;->getChildCount()I

    move-result v0

    const/4 v3, 0x0

    if-nez v0, :cond_0

    .line 8421
    invoke-direct {v4, v3}, Lcom/facebook/ads/redexgen/X/3h;->setScrollingCacheEnabled(Z)V

    .line 8422
    return-void

    .line 8423
    :cond_0
    iget-object v0, v4, Lcom/facebook/ads/redexgen/X/3h;->A0X:Landroid/widget/Scroller;

    const/4 v1, 0x1

    if-eqz v0, :cond_3

    invoke-virtual {v0}, Landroid/widget/Scroller;->isFinished()Z

    move-result v0

    if-nez v0, :cond_3

    const/4 v0, 0x1

    .line 8424
    .local v1, "wasScrolling":Z
    :goto_0
    if-eqz v0, :cond_2

    .line 8425
    iget-boolean v0, v4, Lcom/facebook/ads/redexgen/X/3h;->A0j:Z

    if-eqz v0, :cond_1

    iget-object v0, v4, Lcom/facebook/ads/redexgen/X/3h;->A0X:Landroid/widget/Scroller;

    invoke-virtual {v0}, Landroid/widget/Scroller;->getCurrX()I

    move-result v8

    .line 8426
    .local v4, "sx":I
    :goto_1
    iget-object v0, v4, Lcom/facebook/ads/redexgen/X/3h;->A0X:Landroid/widget/Scroller;

    invoke-virtual {v0}, Landroid/widget/Scroller;->abortAnimation()V

    .line 8427
    invoke-direct {v4, v3}, Lcom/facebook/ads/redexgen/X/3h;->setScrollingCacheEnabled(Z)V

    .line 8428
    .restart local v4    # "sx":I
    :goto_2
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/3h;->getScrollY()I

    move-result v9

    .line 8429
    .local v11, "sy":I
    sub-int/2addr v10, v8

    .line 8430
    .local v12, "dx":I
    sub-int/2addr v11, v9

    .line 8431
    .local p0, "dy":I
    if-nez v10, :cond_4

    if-nez v11, :cond_4

    .line 8432
    invoke-direct {v4, v3}, Lcom/facebook/ads/redexgen/X/3h;->A0R(Z)V

    .line 8433
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/3h;->A0f()V

    .line 8434
    invoke-virtual {v4, v3}, Lcom/facebook/ads/redexgen/X/3h;->setScrollState(I)V

    .line 8435
    return-void

    .line 8436
    :cond_1
    iget-object v0, v4, Lcom/facebook/ads/redexgen/X/3h;->A0X:Landroid/widget/Scroller;

    invoke-virtual {v0}, Landroid/widget/Scroller;->getStartX()I

    move-result v8

    goto :goto_1

    .line 8437
    .end local v4    # "sx":I
    :cond_2
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/3h;->getScrollX()I

    move-result v8

    goto :goto_2

    .line 8438
    :cond_3
    const/4 v0, 0x0

    goto :goto_0

    .line 8439
    :cond_4
    invoke-direct {v4, v1}, Lcom/facebook/ads/redexgen/X/3h;->setScrollingCacheEnabled(Z)V

    .line 8440
    const/4 v0, 0x2

    invoke-virtual {v4, v0}, Lcom/facebook/ads/redexgen/X/3h;->setScrollState(I)V

    .line 8441
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/3h;->getClientWidth()I

    move-result v2

    .line 8442
    .local v3, "width":I
    div-int/lit8 v7, v2, 0x2

    .line 8443
    .local p1, "halfWidth":I
    invoke-static {v10}, Ljava/lang/Math;->abs(I)I

    move-result v0

    int-to-float v1, v0

    const/high16 v5, 0x3f800000    # 1.0f

    mul-float/2addr v1, v5

    int-to-float v0, v2

    div-float/2addr v1, v0

    invoke-static {v5, v1}, Ljava/lang/Math;->min(FF)F

    move-result v0

    .line 8444
    .local p2, "distanceRatio":F
    int-to-float v6, v7

    int-to-float v1, v7

    .line 8445
    invoke-direct {v4, v0}, Lcom/facebook/ads/redexgen/X/3h;->A00(F)F

    move-result v0

    mul-float/2addr v1, v0

    add-float/2addr v6, v1

    .line 8446
    .local p3, "distance":F
    invoke-static/range {p3 .. p3}, Ljava/lang/Math;->abs(I)I

    move-result v0

    .line 8447
    .end local p8
    .local v10, "velocity":I
    if-lez v0, :cond_5

    .line 8448
    int-to-float v0, v0

    div-float/2addr v6, v0

    sget-object v1, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const/4 v0, 0x1

    aget-object v0, v1, v0

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v1

    const/16 v0, 0xc

    if-eq v1, v0, :cond_6

    sget-object v2, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const-string v1, ""

    const/4 v0, 0x4

    aput-object v1, v2, v0

    const-string v1, ""

    const/4 v0, 0x5

    aput-object v1, v2, v0

    invoke-static {v6}, Ljava/lang/Math;->abs(F)F

    move-result v1

    const/high16 v0, 0x447a0000    # 1000.0f

    mul-float/2addr v1, v0

    invoke-static {v1}, Ljava/lang/Math;->round(F)I

    move-result v0

    mul-int/lit8 v0, v0, 0x4

    .line 8449
    .local v5, "duration":I
    .end local v7
    .local v5, "duration":I
    :goto_3
    const/16 v1, 0x258

    invoke-static {v0, v1}, Ljava/lang/Math;->min(II)I

    move-result v12

    .line 8450
    .end local v5    # "duration":I
    .local p4, "duration":I
    iput-boolean v3, v4, Lcom/facebook/ads/redexgen/X/3h;->A0j:Z

    .line 8451
    iget-object v7, v4, Lcom/facebook/ads/redexgen/X/3h;->A0X:Landroid/widget/Scroller;

    .end local v10    # "velocity":I
    .local v2, "velocity":I
    invoke-virtual/range {v7 .. v12}, Landroid/widget/Scroller;->startScroll(IIIII)V

    .line 8452
    invoke-static {p0}, Lcom/facebook/ads/redexgen/X/3T;->A07(Landroid/view/View;)V

    .line 8453
    return-void

    .line 8454
    .end local v5
    :cond_5
    int-to-float v2, v2

    iget-object v1, v4, Lcom/facebook/ads/redexgen/X/3h;->A01:Lcom/facebook/ads/redexgen/X/3E;

    iget v0, v4, Lcom/facebook/ads/redexgen/X/3h;->A00:I

    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/3E;->A00(I)F

    move-result v0

    mul-float/2addr v2, v0

    .line 8455
    .local v5, "pageWidth":F
    invoke-static {v10}, Ljava/lang/Math;->abs(I)I

    move-result v0

    int-to-float v1, v0

    iget v0, v4, Lcom/facebook/ads/redexgen/X/3h;->A0M:I

    int-to-float v0, v0

    add-float/2addr v0, v2

    div-float/2addr v1, v0

    .line 8456
    .local v7, "pageDelta":F
    add-float/2addr v5, v1

    const/high16 v0, 0x42c80000    # 100.0f

    mul-float/2addr v5, v0

    float-to-int v0, v5

    goto :goto_3

    :cond_6
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0
.end method

.method private A0K(IIII)V
    .locals 4

    .line 8457
    if-lez p2, :cond_0

    iget-object v3, p0, Lcom/facebook/ads/redexgen/X/3h;->A0r:Ljava/util/ArrayList;

    sget-object v2, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const/4 v0, 0x7

    aget-object v1, v2, v0

    const/4 v0, 0x0

    aget-object v2, v2, v0

    const/16 v0, 0xa

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    invoke-virtual {v2, v0}, Ljava/lang/String;->charAt(I)C

    move-result v0

    if-eq v1, v0, :cond_1

    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    .line 8458
    :cond_0
    iget v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A00:I

    invoke-direct {p0, v0}, Lcom/facebook/ads/redexgen/X/3h;->A04(I)Lcom/facebook/ads/redexgen/X/3Z;

    move-result-object v0

    .line 8459
    .local v0, "ii":Lcom/facebook/ads/redexgen/X/3Z;
    if-eqz v0, :cond_4

    iget v1, v0, Lcom/facebook/ads/redexgen/X/3Z;->A00:F

    iget v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A07:F

    invoke-static {v1, v0}, Ljava/lang/Math;->min(FF)F

    move-result v1

    .line 8460
    .local v1, "scrollOffset":F
    :goto_0
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/3h;->getPaddingLeft()I

    move-result v0

    sub-int/2addr p1, v0

    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/3h;->getPaddingRight()I

    move-result v0

    sub-int/2addr p1, v0

    int-to-float v0, p1

    mul-float/2addr v0, v1

    float-to-int v1, v0

    .line 8461
    .local v2, "scrollPos":I
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/3h;->getScrollX()I

    move-result v0

    if-eq v1, v0, :cond_2

    .line 8462
    const/4 v0, 0x0

    invoke-direct {p0, v0}, Lcom/facebook/ads/redexgen/X/3h;->A0R(Z)V

    .line 8463
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/3h;->getScrollY()I

    move-result v0

    invoke-virtual {p0, v1, v0}, Lcom/facebook/ads/redexgen/X/3h;->scrollTo(II)V

    goto :goto_1

    .line 8464
    :cond_1
    sget-object v2, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const-string v1, "xS6wP2kv8Ukjp2mlI4OFB7EI"

    const/4 v0, 0x1

    aput-object v1, v2, v0

    invoke-virtual {v3}, Ljava/util/ArrayList;->isEmpty()Z

    move-result v0

    if-nez v0, :cond_0

    .line 8465
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0X:Landroid/widget/Scroller;

    invoke-virtual {v0}, Landroid/widget/Scroller;->isFinished()Z

    move-result v3

    sget-object v1, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const/4 v0, 0x1

    aget-object v0, v1, v0

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v1

    const/16 v0, 0xc

    if-eq v1, v0, :cond_5

    sget-object v2, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const-string v1, "M3IvNYMGgF0qb538wmgDMK0nNkgqAt"

    const/4 v0, 0x2

    aput-object v1, v2, v0

    if-nez v3, :cond_3

    .line 8466
    iget-object v2, p0, Lcom/facebook/ads/redexgen/X/3h;->A0X:Landroid/widget/Scroller;

    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/3h;->getCurrentItem()I

    move-result v1

    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/3h;->getClientWidth()I

    move-result v0

    mul-int/2addr v1, v0

    invoke-virtual {v2, v1}, Landroid/widget/Scroller;->setFinalX(I)V

    .line 8467
    .end local v0    # "ii":Lcom/facebook/ads/redexgen/X/3Z;
    .end local v1    # "scrollOffset":F
    .end local v2    # "scrollPos":I
    :cond_2
    :goto_1
    return-void

    .line 8468
    :cond_3
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/3h;->getPaddingLeft()I

    move-result v0

    sub-int/2addr p1, v0

    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/3h;->getPaddingRight()I

    move-result v0

    sub-int/2addr p1, v0

    add-int/2addr p1, p3

    .line 8469
    .local v0, "widthWithMargin":I
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/3h;->getPaddingLeft()I

    move-result v0

    sub-int/2addr p2, v0

    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/3h;->getPaddingRight()I

    move-result v0

    sub-int/2addr p2, v0

    add-int/2addr p2, p4

    .line 8470
    .local v1, "oldWidthWithMargin":I
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/3h;->getScrollX()I

    move-result v0

    .line 8471
    .local v2, "xpos":I
    int-to-float v1, v0

    int-to-float v0, p2

    div-float/2addr v1, v0

    .line 8472
    .local v3, "pageOffset":F
    int-to-float v0, p1

    mul-float/2addr v0, v1

    float-to-int v1, v0

    .line 8473
    .local p0, "newOffsetPixels":I
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/3h;->getScrollY()I

    move-result v0

    invoke-virtual {p0, v1, v0}, Lcom/facebook/ads/redexgen/X/3h;->scrollTo(II)V

    .line 8474
    .end local v0    # "widthWithMargin":I
    .end local v1    # "oldWidthWithMargin":I
    .end local v2    # "xpos":I
    .end local v3    # "pageOffset":F
    .end local p0    # "newOffsetPixels":I
    goto :goto_1

    .line 8475
    :cond_4
    const/4 v1, 0x0

    goto :goto_0

    .line 8476
    :cond_5
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0
.end method

.method private final A0L(IZ)V
    .locals 1

    .line 8477
    const/4 v0, 0x0

    iput-boolean v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0m:Z

    .line 8478
    invoke-direct {p0, p1, p2, v0}, Lcom/facebook/ads/redexgen/X/3h;->A0N(IZZ)V

    .line 8479
    return-void
.end method

.method private A0M(IZIZ)V
    .locals 5

    .line 8480
    invoke-direct {p0, p1}, Lcom/facebook/ads/redexgen/X/3h;->A04(I)Lcom/facebook/ads/redexgen/X/3Z;

    move-result-object v1

    .line 8481
    .local v0, "curInfo":Lcom/facebook/ads/redexgen/X/3Z;
    const/4 v4, 0x0

    .line 8482
    .local v1, "destX":I
    if-eqz v1, :cond_0

    .line 8483
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/3h;->getClientWidth()I

    move-result v0

    .line 8484
    .local v2, "width":I
    int-to-float v3, v0

    iget v2, p0, Lcom/facebook/ads/redexgen/X/3h;->A02:F

    iget v1, v1, Lcom/facebook/ads/redexgen/X/3Z;->A00:F

    iget v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A07:F

    .line 8485
    invoke-static {v1, v0}, Ljava/lang/Math;->min(FF)F

    move-result v0

    .line 8486
    invoke-static {v2, v0}, Ljava/lang/Math;->max(FF)F

    move-result v0

    mul-float/2addr v3, v0

    float-to-int v4, v3

    .line 8487
    .end local v2    # "width":I
    :cond_0
    const/4 v3, 0x0

    sget-object v1, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const/4 v0, 0x2

    aget-object v0, v1, v0

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v1

    const/16 v0, 0xe

    if-eq v1, v0, :cond_4

    sget-object v2, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const-string v1, "qWyJthdIKprwmupmlWytZXkwKuTz6nwm"

    const/4 v0, 0x3

    aput-object v1, v2, v0

    const-string v1, "fDmSwMvEmt93O90vXMKXMmFP8biUMO95"

    const/4 v0, 0x6

    aput-object v1, v2, v0

    if-eqz p2, :cond_2

    .line 8488
    invoke-direct {p0, v4, v3, p3}, Lcom/facebook/ads/redexgen/X/3h;->A0J(III)V

    .line 8489
    if-eqz p4, :cond_1

    .line 8490
    invoke-direct {p0, p1}, Lcom/facebook/ads/redexgen/X/3h;->A0E(I)V

    .line 8491
    :cond_1
    :goto_0
    return-void

    .line 8492
    :cond_2
    if-eqz p4, :cond_3

    .line 8493
    invoke-direct {p0, p1}, Lcom/facebook/ads/redexgen/X/3h;->A0E(I)V

    .line 8494
    :cond_3
    invoke-direct {p0, v3}, Lcom/facebook/ads/redexgen/X/3h;->A0R(Z)V

    .line 8495
    invoke-virtual {p0, v4, v3}, Lcom/facebook/ads/redexgen/X/3h;->scrollTo(II)V

    .line 8496
    invoke-direct {p0, v4}, Lcom/facebook/ads/redexgen/X/3h;->A0Z(I)Z

    goto :goto_0

    :cond_4
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0
.end method

.method private final A0N(IZZ)V
    .locals 1

    .line 8497
    const/4 v0, 0x0

    invoke-direct {p0, p1, p2, p3, v0}, Lcom/facebook/ads/redexgen/X/3h;->A0O(IZZI)V

    .line 8498
    return-void
.end method

.method private final A0O(IZZI)V
    .locals 6

    .line 8499
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A01:Lcom/facebook/ads/redexgen/X/3E;

    const/4 v3, 0x0

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/3E;->A01()I

    move-result v0

    if-gtz v0, :cond_1

    .line 8500
    .end local v0
    .end local v2
    :cond_0
    invoke-direct {p0, v3}, Lcom/facebook/ads/redexgen/X/3h;->setScrollingCacheEnabled(Z)V

    .line 8501
    return-void

    .line 8502
    :cond_1
    if-nez p3, :cond_2

    iget v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A00:I

    if-ne v0, p1, :cond_2

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0r:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->size()I

    move-result v0

    if-eqz v0, :cond_2

    .line 8503
    invoke-direct {p0, v3}, Lcom/facebook/ads/redexgen/X/3h;->setScrollingCacheEnabled(Z)V

    .line 8504
    return-void

    .line 8505
    :cond_2
    const/4 v4, 0x1

    if-gez p1, :cond_4

    .line 8506
    const/4 p1, 0x0

    .line 8507
    :cond_3
    :goto_0
    iget v1, p0, Lcom/facebook/ads/redexgen/X/3h;->A0L:I

    .line 8508
    .local v2, "pageLimit":I
    iget v5, p0, Lcom/facebook/ads/redexgen/X/3h;->A00:I

    add-int v0, v5, v1

    if-gt p1, v0, :cond_6

    sub-int/2addr v5, v1

    sget-object v2, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const/4 v0, 0x7

    aget-object v1, v2, v0

    const/4 v0, 0x0

    aget-object v2, v2, v0

    const/16 v0, 0xa

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    invoke-virtual {v2, v0}, Ljava/lang/String;->charAt(I)C

    move-result v0

    if-eq v1, v0, :cond_5

    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    .line 8509
    :cond_4
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A01:Lcom/facebook/ads/redexgen/X/3E;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/3E;->A01()I

    move-result v0

    if-lt p1, v0, :cond_3

    .line 8510
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A01:Lcom/facebook/ads/redexgen/X/3E;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/3E;->A01()I

    move-result v0

    add-int/lit8 p1, v0, -0x1

    goto :goto_0

    :cond_5
    sget-object v2, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const-string v1, "HdD8kb8A1NTv0LjA3C8pNf8B8Uhf5Rjf"

    const/4 v0, 0x3

    aput-object v1, v2, v0

    const-string v1, "hEPxY7plOgAB5ujpSYVhyDABeqG0KedF"

    const/4 v0, 0x6

    aput-object v1, v2, v0

    if-ge p1, v5, :cond_7

    .line 8511
    :cond_6
    const/4 v1, 0x0

    .local v3, "i":I
    :goto_1
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0r:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->size()I

    move-result v0

    if-ge v1, v0, :cond_7

    .line 8512
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0r:Ljava/util/ArrayList;

    invoke-virtual {v0, v1}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/facebook/ads/redexgen/X/3Z;

    iput-boolean v4, v0, Lcom/facebook/ads/redexgen/X/3Z;->A04:Z

    .line 8513
    add-int/lit8 v1, v1, 0x1

    goto :goto_1

    .line 8514
    .end local v3    # "i":I
    :cond_7
    iget v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A00:I

    if-eq v0, p1, :cond_8

    const/4 v3, 0x1

    .line 8515
    .local v0, "dispatchSelected":Z
    :cond_8
    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0g:Z

    if-eqz v0, :cond_a

    .line 8516
    iput p1, p0, Lcom/facebook/ads/redexgen/X/3h;->A00:I

    .line 8517
    if-eqz v3, :cond_9

    .line 8518
    invoke-direct {p0, p1}, Lcom/facebook/ads/redexgen/X/3h;->A0E(I)V

    .line 8519
    :cond_9
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/3h;->requestLayout()V

    .line 8520
    :goto_2
    return-void

    .line 8521
    :cond_a
    invoke-direct {p0, p1}, Lcom/facebook/ads/redexgen/X/3h;->A0G(I)V

    .line 8522
    invoke-direct {p0, p1, p2, p4, v3}, Lcom/facebook/ads/redexgen/X/3h;->A0M(IZIZ)V

    goto :goto_2
.end method

.method private A0P(Landroid/view/MotionEvent;)V
    .locals 4

    .line 8523
    invoke-virtual {p1}, Landroid/view/MotionEvent;->getActionIndex()I

    move-result v2

    .line 8524
    .local v0, "pointerIndex":I
    invoke-virtual {p1, v2}, Landroid/view/MotionEvent;->getPointerId(I)I

    move-result v1

    .line 8525
    .local v1, "pointerId":I
    iget v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A08:I

    if-ne v1, v0, :cond_2

    .line 8526
    if-nez v2, :cond_0

    const/4 v3, 0x1

    .line 8527
    .local v2, "newPointerIndex":I
    :goto_0
    invoke-virtual {p1, v3}, Landroid/view/MotionEvent;->getX(I)F

    move-result v0

    iput v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A05:F

    sget-object v2, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const/4 v0, 0x4

    aget-object v1, v2, v0

    const/4 v0, 0x5

    aget-object v0, v2, v0

    invoke-virtual {v1}, Ljava/lang/String;->length()I

    move-result v1

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v0

    if-eq v1, v0, :cond_1

    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    .line 8528
    :cond_0
    const/4 v3, 0x0

    goto :goto_0

    .line 8529
    :cond_1
    sget-object v2, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const-string v1, ""

    const/4 v0, 0x4

    aput-object v1, v2, v0

    const-string v1, ""

    const/4 v0, 0x5

    aput-object v1, v2, v0

    invoke-virtual {p1, v3}, Landroid/view/MotionEvent;->getPointerId(I)I

    move-result v0

    iput v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A08:I

    .line 8530
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0U:Landroid/view/VelocityTracker;

    if-eqz v0, :cond_2

    .line 8531
    invoke-virtual {v0}, Landroid/view/VelocityTracker;->clear()V

    .line 8532
    .end local v2    # "newPointerIndex":I
    :cond_2
    return-void
.end method

.method private A0Q(Lcom/facebook/ads/redexgen/X/3Z;ILcom/facebook/ads/redexgen/X/3Z;)V
    .locals 12

    .line 8533
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A01:Lcom/facebook/ads/redexgen/X/3E;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/3E;->A01()I

    move-result v11

    .line 8534
    .local v0, "N":I
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/3h;->getClientWidth()I

    move-result v1

    .line 8535
    .local v1, "width":I
    if-lez v1, :cond_2

    iget v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0M:I

    int-to-float v3, v0

    int-to-float v0, v1

    div-float/2addr v3, v0

    .line 8536
    .local v2, "marginOffset":F
    :goto_0
    if-eqz p3, :cond_8

    .line 8537
    iget v1, p3, Lcom/facebook/ads/redexgen/X/3Z;->A02:I

    .line 8538
    .local v3, "oldCurPosition":I
    iget v0, p1, Lcom/facebook/ads/redexgen/X/3Z;->A02:I

    if-ge v1, v0, :cond_4

    .line 8539
    const/4 v5, 0x0

    .line 8540
    .local v4, "itemIndex":I
    .local v5, "ii":Lcom/facebook/ads/redexgen/X/3Z;
    iget v4, p3, Lcom/facebook/ads/redexgen/X/3Z;->A00:F

    iget v0, p3, Lcom/facebook/ads/redexgen/X/3Z;->A01:F

    add-float/2addr v4, v0

    add-float/2addr v4, v3

    .line 8541
    .local v6, "offset":F
    add-int/lit8 v6, v1, 0x1

    .line 8542
    .local v7, "pos":I
    :goto_1
    iget v0, p1, Lcom/facebook/ads/redexgen/X/3Z;->A02:I

    if-gt v6, v0, :cond_8

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0r:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->size()I

    move-result v0

    if-ge v5, v0, :cond_8

    .line 8543
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0r:Ljava/util/ArrayList;

    invoke-virtual {v0, v5}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v7

    check-cast v7, Lcom/facebook/ads/redexgen/X/3Z;

    .line 8544
    :goto_2
    iget v0, v7, Lcom/facebook/ads/redexgen/X/3Z;->A02:I

    if-le v6, v0, :cond_0

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0r:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->size()I

    move-result v0

    add-int/lit8 v0, v0, -0x1

    if-ge v5, v0, :cond_0

    .line 8545
    add-int/lit8 v5, v5, 0x1

    .line 8546
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0r:Ljava/util/ArrayList;

    invoke-virtual {v0, v5}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v7

    check-cast v7, Lcom/facebook/ads/redexgen/X/3Z;

    goto :goto_2

    .line 8547
    :cond_0
    :goto_3
    iget v0, v7, Lcom/facebook/ads/redexgen/X/3Z;->A02:I

    if-ge v6, v0, :cond_1

    .line 8548
    iget-object v8, p0, Lcom/facebook/ads/redexgen/X/3h;->A01:Lcom/facebook/ads/redexgen/X/3E;

    sget-object v1, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const/4 v0, 0x2

    aget-object v0, v1, v0

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v1

    const/16 v0, 0xe

    if-eq v1, v0, :cond_3

    sget-object v2, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const-string v1, "unjnr1JPHveRA3A"

    const/4 v0, 0x2

    aput-object v1, v2, v0

    invoke-virtual {v8, v6}, Lcom/facebook/ads/redexgen/X/3E;->A00(I)F

    move-result v0

    add-float/2addr v0, v3

    add-float/2addr v4, v0

    .line 8549
    add-int/lit8 v6, v6, 0x1

    goto :goto_3

    .line 8550
    :cond_1
    iput v4, v7, Lcom/facebook/ads/redexgen/X/3Z;->A00:F

    .line 8551
    iget v0, v7, Lcom/facebook/ads/redexgen/X/3Z;->A01:F

    add-float/2addr v0, v3

    add-float/2addr v4, v0

    .line 8552
    add-int/lit8 v6, v6, 0x1

    goto :goto_1

    .line 8553
    :cond_2
    const/4 v3, 0x0

    goto :goto_0

    :cond_3
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    .line 8554
    .end local v4    # "itemIndex":I
    .end local v5    # "ii":Lcom/facebook/ads/redexgen/X/3Z;
    .end local v6    # "offset":F
    .end local v7    # "pos":I
    :cond_4
    iget v0, p1, Lcom/facebook/ads/redexgen/X/3Z;->A02:I

    if-le v1, v0, :cond_8

    .line 8555
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0r:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->size()I

    move-result v0

    add-int/lit8 v6, v0, -0x1

    .line 8556
    .restart local v4    # "itemIndex":I
    .restart local v5    # "ii":Lcom/facebook/ads/redexgen/X/3Z;
    iget v7, p3, Lcom/facebook/ads/redexgen/X/3Z;->A00:F

    .line 8557
    .restart local v6    # "offset":F
    add-int/lit8 v5, v1, -0x1

    .line 8558
    .restart local v7    # "pos":I
    :goto_4
    iget v0, p1, Lcom/facebook/ads/redexgen/X/3Z;->A02:I

    if-lt v5, v0, :cond_8

    if-ltz v6, :cond_8

    .line 8559
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0r:Ljava/util/ArrayList;

    invoke-virtual {v0, v6}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lcom/facebook/ads/redexgen/X/3Z;

    .line 8560
    :goto_5
    iget v0, v4, Lcom/facebook/ads/redexgen/X/3Z;->A02:I

    if-ge v5, v0, :cond_5

    if-lez v6, :cond_5

    .line 8561
    add-int/lit8 v6, v6, -0x1

    .line 8562
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0r:Ljava/util/ArrayList;

    invoke-virtual {v0, v6}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lcom/facebook/ads/redexgen/X/3Z;

    goto :goto_5

    .line 8563
    :cond_5
    :goto_6
    iget v8, v4, Lcom/facebook/ads/redexgen/X/3Z;->A02:I

    sget-object v2, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const/4 v0, 0x4

    aget-object v1, v2, v0

    const/4 v0, 0x5

    aget-object v0, v2, v0

    invoke-virtual {v1}, Ljava/lang/String;->length()I

    move-result v1

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v0

    if-eq v1, v0, :cond_6

    sget-object v2, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const-string v1, "tUoZSKhLB59JJ"

    const/4 v0, 0x1

    aput-object v1, v2, v0

    if-le v5, v8, :cond_7

    .line 8564
    :goto_7
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A01:Lcom/facebook/ads/redexgen/X/3E;

    invoke-virtual {v0, v5}, Lcom/facebook/ads/redexgen/X/3E;->A00(I)F

    move-result v0

    add-float/2addr v0, v3

    sub-float/2addr v7, v0

    .line 8565
    add-int/lit8 v5, v5, -0x1

    goto :goto_6

    :cond_6
    sget-object v2, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const-string v1, "eK1wjFwqyv9CJUk"

    const/4 v0, 0x2

    aput-object v1, v2, v0

    if-le v5, v8, :cond_7

    goto :goto_7

    .line 8566
    :cond_7
    iget v0, v4, Lcom/facebook/ads/redexgen/X/3Z;->A01:F

    add-float/2addr v0, v3

    sub-float/2addr v7, v0

    .line 8567
    iput v7, v4, Lcom/facebook/ads/redexgen/X/3Z;->A00:F

    .line 8568
    add-int/lit8 v5, v5, -0x1

    goto :goto_4

    .line 8569
    .end local v3    # "oldCurPosition":I
    :cond_8
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0r:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->size()I

    move-result v5

    .line 8570
    .local v3, "itemCount":I
    iget v6, p1, Lcom/facebook/ads/redexgen/X/3Z;->A00:F

    .line 8571
    .local v4, "offset":F
    iget v0, p1, Lcom/facebook/ads/redexgen/X/3Z;->A02:I

    add-int/lit8 v7, v0, -0x1

    .line 8572
    .local v5, "pos":I
    iget v0, p1, Lcom/facebook/ads/redexgen/X/3Z;->A02:I

    if-nez v0, :cond_c

    iget v0, p1, Lcom/facebook/ads/redexgen/X/3Z;->A00:F

    :goto_8
    iput v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A02:F

    .line 8573
    iget v1, p1, Lcom/facebook/ads/redexgen/X/3Z;->A02:I

    add-int/lit8 v0, v11, -0x1

    const/high16 v10, 0x3f800000    # 1.0f

    if-ne v1, v0, :cond_b

    .line 8574
    iget v1, p1, Lcom/facebook/ads/redexgen/X/3Z;->A00:F

    iget v0, p1, Lcom/facebook/ads/redexgen/X/3Z;->A01:F

    add-float/2addr v1, v0

    sub-float/2addr v1, v10

    :goto_9
    iput v1, p0, Lcom/facebook/ads/redexgen/X/3h;->A07:F

    .line 8575
    add-int/lit8 v4, p2, -0x1

    .local v6, "i":I
    :goto_a
    if-ltz v4, :cond_d

    .line 8576
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0r:Ljava/util/ArrayList;

    invoke-virtual {v0, v4}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/facebook/ads/redexgen/X/3Z;

    .line 8577
    .local v7, "ii":Lcom/facebook/ads/redexgen/X/3Z;
    :goto_b
    iget v0, v2, Lcom/facebook/ads/redexgen/X/3Z;->A02:I

    if-le v7, v0, :cond_9

    .line 8578
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A01:Lcom/facebook/ads/redexgen/X/3E;

    add-int/lit8 v1, v7, -0x1

    .end local v5    # "pos":I
    .local v10, "pos":I
    invoke-virtual {v0, v7}, Lcom/facebook/ads/redexgen/X/3E;->A00(I)F

    move-result v0

    add-float/2addr v0, v3

    sub-float/2addr v6, v0

    move v7, v1

    goto :goto_b

    .line 8579
    .end local v10    # "pos":I
    .restart local v5    # "pos":I
    :cond_9
    iget v0, v2, Lcom/facebook/ads/redexgen/X/3Z;->A01:F

    add-float/2addr v0, v3

    sub-float/2addr v6, v0

    .line 8580
    iput v6, v2, Lcom/facebook/ads/redexgen/X/3Z;->A00:F

    .line 8581
    iget v0, v2, Lcom/facebook/ads/redexgen/X/3Z;->A02:I

    if-nez v0, :cond_a

    iput v6, p0, Lcom/facebook/ads/redexgen/X/3h;->A02:F

    .line 8582
    .end local v7    # "ii":Lcom/facebook/ads/redexgen/X/3Z;
    :cond_a
    add-int/lit8 v4, v4, -0x1

    add-int/lit8 v7, v7, -0x1

    goto :goto_a

    .line 8583
    :cond_b
    const v1, 0x7f7fffff    # Float.MAX_VALUE

    goto :goto_9

    .line 8584
    :cond_c
    const v0, -0x800001

    goto :goto_8

    .line 8585
    .end local v6    # "i":I
    :cond_d
    iget v4, p1, Lcom/facebook/ads/redexgen/X/3Z;->A00:F

    iget v0, p1, Lcom/facebook/ads/redexgen/X/3Z;->A01:F

    add-float/2addr v4, v0

    add-float/2addr v4, v3

    .line 8586
    .end local v4    # "offset":F
    .local v6, "offset":F
    iget v0, p1, Lcom/facebook/ads/redexgen/X/3Z;->A02:I

    add-int/lit8 v6, v0, 0x1

    .line 8587
    .end local v5    # "pos":I
    .local v4, "pos":I
    add-int/lit8 v7, p2, 0x1

    .local v5, "i":I
    :goto_c
    if-ge v7, v5, :cond_11

    .line 8588
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0r:Ljava/util/ArrayList;

    invoke-virtual {v0, v7}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v8

    check-cast v8, Lcom/facebook/ads/redexgen/X/3Z;

    .line 8589
    .restart local v7    # "ii":Lcom/facebook/ads/redexgen/X/3Z;
    :goto_d
    iget v0, v8, Lcom/facebook/ads/redexgen/X/3Z;->A02:I

    if-ge v6, v0, :cond_e

    .line 8590
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A01:Lcom/facebook/ads/redexgen/X/3E;

    add-int/lit8 v1, v6, 0x1

    .end local v4    # "pos":I
    .restart local v10    # "pos":I
    invoke-virtual {v0, v6}, Lcom/facebook/ads/redexgen/X/3E;->A00(I)F

    move-result v0

    add-float/2addr v0, v3

    add-float/2addr v4, v0

    move v6, v1

    goto :goto_d

    .line 8591
    .end local v10    # "pos":I
    .restart local v4    # "pos":I
    :cond_e
    iget v9, v8, Lcom/facebook/ads/redexgen/X/3Z;->A02:I

    sget-object v1, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const/4 v0, 0x2

    aget-object v0, v1, v0

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v1

    const/16 v0, 0xe

    if-eq v1, v0, :cond_10

    sget-object v2, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const-string v1, "LKMrFShf3NXE5XfaxsCGHUW3hns0S8Wh"

    const/4 v0, 0x3

    aput-object v1, v2, v0

    const-string v1, "xptjAJKkekqR5TDnJjimQZvUd22mQJJ9"

    const/4 v0, 0x6

    aput-object v1, v2, v0

    add-int/lit8 v0, v11, -0x1

    if-ne v9, v0, :cond_f

    .line 8592
    :goto_e
    iget v0, v8, Lcom/facebook/ads/redexgen/X/3Z;->A01:F

    add-float/2addr v0, v4

    sub-float/2addr v0, v10

    iput v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A07:F

    .line 8593
    :cond_f
    iput v4, v8, Lcom/facebook/ads/redexgen/X/3Z;->A00:F

    .line 8594
    iget v0, v8, Lcom/facebook/ads/redexgen/X/3Z;->A01:F

    add-float/2addr v0, v3

    add-float/2addr v4, v0

    .line 8595
    .end local v7    # "ii":Lcom/facebook/ads/redexgen/X/3Z;
    add-int/lit8 v7, v7, 0x1

    add-int/lit8 v6, v6, 0x1

    goto :goto_c

    :cond_10
    add-int/lit8 v0, v11, -0x1

    if-ne v9, v0, :cond_f

    goto :goto_e

    .line 8596
    .end local v5    # "i":I
    :cond_11
    const/4 v0, 0x0

    iput-boolean v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0l:Z

    .line 8597
    return-void
.end method

.method private A0R(Z)V
    .locals 6

    .line 8598
    iget v2, p0, Lcom/facebook/ads/redexgen/X/3h;->A0P:I

    const/4 v0, 0x2

    const/4 v1, 0x1

    const/4 v4, 0x0

    if-ne v2, v0, :cond_3

    const/4 v5, 0x1

    .line 8599
    .local v0, "needPopulate":Z
    :goto_0
    if-eqz v5, :cond_1

    .line 8600
    invoke-direct {p0, v4}, Lcom/facebook/ads/redexgen/X/3h;->setScrollingCacheEnabled(Z)V

    .line 8601
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0X:Landroid/widget/Scroller;

    invoke-virtual {v0}, Landroid/widget/Scroller;->isFinished()Z

    move-result v0

    xor-int/2addr v0, v1

    .line 8602
    .local v1, "wasScrolling":Z
    if-eqz v0, :cond_1

    .line 8603
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0X:Landroid/widget/Scroller;

    invoke-virtual {v0}, Landroid/widget/Scroller;->abortAnimation()V

    sget-object v1, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const/4 v0, 0x1

    aget-object v0, v1, v0

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v1

    const/16 v0, 0xc

    if-eq v1, v0, :cond_7

    .line 8604
    sget-object v2, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const-string v1, "La7"

    const/4 v0, 0x1

    aput-object v1, v2, v0

    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/3h;->getScrollX()I

    move-result v3

    .line 8605
    .local v2, "oldX":I
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/3h;->getScrollY()I

    move-result v2

    .line 8606
    .local v4, "oldY":I
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0X:Landroid/widget/Scroller;

    invoke-virtual {v0}, Landroid/widget/Scroller;->getCurrX()I

    move-result v1

    .line 8607
    .local v5, "x":I
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0X:Landroid/widget/Scroller;

    invoke-virtual {v0}, Landroid/widget/Scroller;->getCurrY()I

    move-result v0

    .line 8608
    .local p0, "y":I
    if-ne v3, v1, :cond_0

    if-eq v2, v0, :cond_1

    .line 8609
    :cond_0
    invoke-virtual {p0, v1, v0}, Lcom/facebook/ads/redexgen/X/3h;->scrollTo(II)V

    .line 8610
    if-eq v1, v3, :cond_1

    .line 8611
    invoke-direct {p0, v1}, Lcom/facebook/ads/redexgen/X/3h;->A0Z(I)Z

    .line 8612
    .end local v1    # "wasScrolling":Z
    .end local v2    # "oldX":I
    .end local v4    # "oldY":I
    .end local v5    # "x":I
    .end local p0    # "y":I
    :cond_1
    iput-boolean v4, p0, Lcom/facebook/ads/redexgen/X/3h;->A0m:Z

    .line 8613
    const/4 v2, 0x0

    .local v1, "i":I
    :goto_1
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0r:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->size()I

    move-result v0

    if-ge v2, v0, :cond_4

    .line 8614
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0r:Ljava/util/ArrayList;

    invoke-virtual {v0, v2}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/facebook/ads/redexgen/X/3Z;

    .line 8615
    .local v2, "ii":Lcom/facebook/ads/redexgen/X/3Z;
    iget-boolean v0, v1, Lcom/facebook/ads/redexgen/X/3Z;->A04:Z

    if-eqz v0, :cond_2

    .line 8616
    const/4 v5, 0x1

    .line 8617
    iput-boolean v4, v1, Lcom/facebook/ads/redexgen/X/3Z;->A04:Z

    .line 8618
    .end local v2    # "ii":Lcom/facebook/ads/redexgen/X/3Z;
    :cond_2
    add-int/lit8 v2, v2, 0x1

    goto :goto_1

    .line 8619
    :cond_3
    const/4 v5, 0x0

    goto :goto_0

    .line 8620
    .end local v1    # "i":I
    :cond_4
    if-eqz v5, :cond_5

    .line 8621
    if-eqz p1, :cond_6

    .line 8622
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0q:Ljava/lang/Runnable;

    invoke-static {p0, v0}, Lcom/facebook/ads/redexgen/X/3T;->A0D(Landroid/view/View;Ljava/lang/Runnable;)V

    .line 8623
    :cond_5
    :goto_2
    return-void

    .line 8624
    :cond_6
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0q:Ljava/lang/Runnable;

    invoke-interface {v0}, Ljava/lang/Runnable;->run()V

    goto :goto_2

    :cond_7
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0
.end method

.method private A0S(Z)V
    .locals 5

    .line 8625
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/3h;->getChildCount()I

    move-result v4

    .line 8626
    .local v0, "childCount":I
    const/4 v3, 0x0

    .local v1, "i":I
    :goto_0
    if-ge v3, v4, :cond_1

    .line 8627
    if-eqz p1, :cond_0

    .line 8628
    iget v2, p0, Lcom/facebook/ads/redexgen/X/3h;->A0N:I

    .line 8629
    .local v2, "layerType":I
    :goto_1
    invoke-virtual {p0, v3}, Lcom/facebook/ads/redexgen/X/3h;->getChildAt(I)Landroid/view/View;

    move-result-object v1

    const/4 v0, 0x0

    invoke-virtual {v1, v2, v0}, Landroid/view/View;->setLayerType(ILandroid/graphics/Paint;)V

    .line 8630
    .end local v2    # "layerType":I
    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    .line 8631
    :cond_0
    const/4 v2, 0x0

    goto :goto_1

    .line 8632
    .end local v1    # "i":I
    :cond_1
    return-void
.end method

.method private A0T(Z)V
    .locals 1

    .line 8633
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/3h;->getParent()Landroid/view/ViewParent;

    move-result-object v0

    .line 8634
    .local v0, "parent":Landroid/view/ViewParent;
    if-eqz v0, :cond_0

    .line 8635
    invoke-interface {v0, p1}, Landroid/view/ViewParent;->requestDisallowInterceptTouchEvent(Z)V

    .line 8636
    :cond_0
    return-void
.end method

.method private A0U()Z
    .locals 1

    .line 8637
    const/4 v0, -0x1

    iput v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A08:I

    .line 8638
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/3h;->A09()V

    .line 8639
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0V:Landroid/widget/EdgeEffect;

    invoke-virtual {v0}, Landroid/widget/EdgeEffect;->onRelease()V

    .line 8640
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0W:Landroid/widget/EdgeEffect;

    invoke-virtual {v0}, Landroid/widget/EdgeEffect;->onRelease()V

    .line 8641
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0V:Landroid/widget/EdgeEffect;

    invoke-virtual {v0}, Landroid/widget/EdgeEffect;->isFinished()Z

    move-result v0

    if-nez v0, :cond_0

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0W:Landroid/widget/EdgeEffect;

    invoke-virtual {v0}, Landroid/widget/EdgeEffect;->isFinished()Z

    move-result v0

    if-eqz v0, :cond_1

    :cond_0
    const/4 v0, 0x1

    .line 8642
    .local v0, "needsInvalidate":Z
    :goto_0
    return v0

    .line 8643
    :cond_1
    const/4 v0, 0x0

    goto :goto_0
.end method

.method private final A0V()Z
    .locals 2

    .line 8644
    iget v1, p0, Lcom/facebook/ads/redexgen/X/3h;->A00:I

    if-lez v1, :cond_0

    .line 8645
    const/4 v0, 0x1

    sub-int/2addr v1, v0

    invoke-direct {p0, v1, v0}, Lcom/facebook/ads/redexgen/X/3h;->A0L(IZ)V

    .line 8646
    return v0

    .line 8647
    :cond_0
    const/4 v0, 0x0

    return v0
.end method

.method private final A0W()Z
    .locals 4

    .line 8648
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A01:Lcom/facebook/ads/redexgen/X/3E;

    if-eqz v0, :cond_0

    iget v2, p0, Lcom/facebook/ads/redexgen/X/3h;->A00:I

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/3E;->A01()I

    move-result v0

    const/4 v1, 0x1

    sub-int/2addr v0, v1

    if-ge v2, v0, :cond_0

    .line 8649
    iget v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A00:I

    add-int/2addr v0, v1

    invoke-direct {p0, v0, v1}, Lcom/facebook/ads/redexgen/X/3h;->A0L(IZ)V

    .line 8650
    return v1

    .line 8651
    :cond_0
    const/4 v3, 0x0

    sget-object v1, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const/4 v0, 0x2

    aget-object v0, v1, v0

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v1

    const/16 v0, 0xe

    if-eq v1, v0, :cond_1

    sget-object v2, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const-string v1, "AXA5EDUbYu85ntxkUdYhY3hIIlQIUQar"

    const/4 v0, 0x7

    aput-object v1, v2, v0

    const-string v1, "7HhQN1X94o8BEMOAnE9vO9t08zapWTmy"

    const/4 v0, 0x0

    aput-object v1, v2, v0

    return v3

    :cond_1
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0
.end method

.method private A0X(F)Z
    .locals 12

    .line 8652
    move-object v4, p0

    const/4 v11, 0x0

    .line 8653
    .local v2, "needsInvalidate":Z
    iget v1, v4, Lcom/facebook/ads/redexgen/X/3h;->A05:F

    sub-float/2addr v1, p1

    .line 8654
    .local v3, "deltaX":F
    iput p1, v4, Lcom/facebook/ads/redexgen/X/3h;->A05:F

    .line 8655
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/3h;->getScrollX()I

    move-result v0

    int-to-float v5, v0

    .line 8656
    .local v4, "oldScrollX":F
    add-float/2addr v5, v1

    .line 8657
    .local v5, "scrollX":F
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/3h;->getClientWidth()I

    move-result v8

    .line 8658
    .local v6, "width":I
    int-to-float v6, v8

    iget v0, v4, Lcom/facebook/ads/redexgen/X/3h;->A02:F

    mul-float/2addr v6, v0

    .line 8659
    .local v7, "leftBound":F
    int-to-float v3, v8

    iget v0, v4, Lcom/facebook/ads/redexgen/X/3h;->A07:F

    mul-float/2addr v3, v0

    .line 8660
    .local v8, "rightBound":F
    const/4 v10, 0x1

    .line 8661
    .local v9, "leftAbsolute":Z
    const/4 v9, 0x1

    .line 8662
    .local v10, "rightAbsolute":Z
    iget-object v1, v4, Lcom/facebook/ads/redexgen/X/3h;->A0r:Ljava/util/ArrayList;

    const/4 v0, 0x0

    invoke-virtual {v1, v0}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/facebook/ads/redexgen/X/3Z;

    .line 8663
    .local v11, "firstItem":Lcom/facebook/ads/redexgen/X/3Z;
    iget-object v1, v4, Lcom/facebook/ads/redexgen/X/3h;->A0r:Ljava/util/ArrayList;

    invoke-virtual {v1}, Ljava/util/ArrayList;->size()I

    move-result v0

    add-int/lit8 v0, v0, -0x1

    invoke-virtual {v1, v0}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v7

    check-cast v7, Lcom/facebook/ads/redexgen/X/3Z;

    .line 8664
    .local p0, "lastItem":Lcom/facebook/ads/redexgen/X/3Z;
    iget v0, v2, Lcom/facebook/ads/redexgen/X/3Z;->A02:I

    if-eqz v0, :cond_0

    .line 8665
    const/4 v10, 0x0

    .line 8666
    iget v6, v2, Lcom/facebook/ads/redexgen/X/3Z;->A00:F

    sget-object v1, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const/4 v0, 0x1

    aget-object v0, v1, v0

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v1

    const/16 v0, 0xc

    if-eq v1, v0, :cond_7

    sget-object v2, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const-string v1, "a"

    const/4 v0, 0x1

    aput-object v1, v2, v0

    int-to-float v0, v8

    mul-float/2addr v6, v0

    .line 8667
    :cond_0
    iget v1, v7, Lcom/facebook/ads/redexgen/X/3Z;->A02:I

    iget-object v0, v4, Lcom/facebook/ads/redexgen/X/3h;->A01:Lcom/facebook/ads/redexgen/X/3E;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/3E;->A01()I

    move-result v0

    add-int/lit8 v0, v0, -0x1

    if-eq v1, v0, :cond_1

    .line 8668
    const/4 v9, 0x0

    .line 8669
    iget v3, v7, Lcom/facebook/ads/redexgen/X/3Z;->A00:F

    int-to-float v0, v8

    mul-float/2addr v3, v0

    .line 8670
    :cond_1
    cmpg-float v0, v5, v6

    if-gez v0, :cond_4

    .line 8671
    if-eqz v10, :cond_2

    .line 8672
    sub-float v0, v6, v5

    .line 8673
    .local p1, "over":F
    iget-object v2, v4, Lcom/facebook/ads/redexgen/X/3h;->A0V:Landroid/widget/EdgeEffect;

    invoke-static {v0}, Ljava/lang/Math;->abs(F)F

    move-result v1

    int-to-float v0, v8

    div-float/2addr v1, v0

    invoke-virtual {v2, v1}, Landroid/widget/EdgeEffect;->onPull(F)V

    .line 8674
    const/4 v11, 0x1

    .line 8675
    .end local p1    # "over":F
    :cond_2
    move v5, v6

    .line 8676
    :cond_3
    :goto_0
    iget v1, v4, Lcom/facebook/ads/redexgen/X/3h;->A05:F

    float-to-int v0, v5

    int-to-float v0, v0

    sub-float v0, v5, v0

    add-float/2addr v1, v0

    iput v1, v4, Lcom/facebook/ads/redexgen/X/3h;->A05:F

    .line 8677
    float-to-int v1, v5

    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/3h;->getScrollY()I

    move-result v0

    invoke-virtual {v4, v1, v0}, Lcom/facebook/ads/redexgen/X/3h;->scrollTo(II)V

    .line 8678
    float-to-int v0, v5

    invoke-direct {v4, v0}, Lcom/facebook/ads/redexgen/X/3h;->A0Z(I)Z

    .line 8679
    return v11

    .line 8680
    :cond_4
    cmpl-float v0, v5, v3

    if-lez v0, :cond_3

    .line 8681
    if-eqz v9, :cond_5

    .line 8682
    sub-float/2addr v5, v3

    .line 8683
    .local v1, "over":F
    iget-object v7, v4, Lcom/facebook/ads/redexgen/X/3h;->A0W:Landroid/widget/EdgeEffect;

    invoke-static {v5}, Ljava/lang/Math;->abs(F)F

    move-result v6

    int-to-float v5, v8

    sget-object v2, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const/4 v0, 0x4

    aget-object v1, v2, v0

    const/4 v0, 0x5

    aget-object v0, v2, v0

    invoke-virtual {v1}, Ljava/lang/String;->length()I

    move-result v1

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v0

    if-eq v1, v0, :cond_6

    div-float/2addr v6, v5

    invoke-virtual {v7, v6}, Landroid/widget/EdgeEffect;->onPull(F)V

    .line 8684
    const/4 v11, 0x1

    .line 8685
    .end local v1    # "over":F
    :cond_5
    :goto_1
    move v5, v3

    goto :goto_0

    :cond_6
    sget-object v2, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const-string v1, "TwKB2lBmR"

    const/4 v0, 0x1

    aput-object v1, v2, v0

    div-float/2addr v6, v5

    invoke-virtual {v7, v6}, Landroid/widget/EdgeEffect;->onPull(F)V

    .line 8686
    const/4 v11, 0x1

    goto :goto_1

    :cond_7
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0
.end method

.method private final A0Y(FF)Z
    .locals 4

    .line 8687
    iget v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0I:I

    int-to-float v0, v0

    const/4 v2, 0x0

    cmpg-float v0, p1, v0

    if-gez v0, :cond_0

    cmpl-float v0, p2, v2

    if-gtz v0, :cond_1

    :cond_0
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/3h;->getWidth()I

    move-result v1

    iget v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0I:I

    sub-int/2addr v1, v0

    int-to-float v0, v1

    cmpl-float v0, p1, v0

    if-lez v0, :cond_2

    cmpg-float v3, p2, v2

    sget-object v1, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const/4 v0, 0x2

    aget-object v0, v1, v0

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v1

    const/16 v0, 0xe

    if-eq v1, v0, :cond_3

    sget-object v2, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const-string v1, "LoLbwLodEQ0RZhoADzk4YxqyeMxGbPNe"

    const/4 v0, 0x3

    aput-object v1, v2, v0

    const-string v1, "uXFtlpuRRgamX4n9Zkql1HjWorEkSQ7b"

    const/4 v0, 0x6

    aput-object v1, v2, v0

    if-gez v3, :cond_2

    :cond_1
    const/4 v0, 0x1

    :goto_0
    return v0

    :cond_2
    const/4 v0, 0x0

    goto :goto_0

    :cond_3
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0
.end method

.method private A0Z(I)Z
    .locals 9

    .line 8688
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0r:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->size()I

    move-result v5

    const/16 v2, 0x18d

    const/16 v1, 0x35

    const/16 v0, 0x3c

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/3h;->A08(III)Ljava/lang/String;

    move-result-object v3

    const/4 v4, 0x0

    if-nez v5, :cond_3

    .line 8689
    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0g:Z

    if-eqz v0, :cond_0

    .line 8690
    return v4

    .line 8691
    :cond_0
    iput-boolean v4, p0, Lcom/facebook/ads/redexgen/X/3h;->A0e:Z

    .line 8692
    const/4 v0, 0x0

    invoke-direct {p0, v4, v0, v4}, Lcom/facebook/ads/redexgen/X/3h;->A0I(IFI)V

    sget-object v2, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const/4 v0, 0x4

    aget-object v1, v2, v0

    const/4 v0, 0x5

    aget-object v0, v2, v0

    invoke-virtual {v1}, Ljava/lang/String;->length()I

    move-result v1

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v0

    if-eq v1, v0, :cond_1

    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    .line 8693
    :cond_1
    sget-object v2, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const-string v1, "c3ramapljB1reDSufYjA"

    const/4 v0, 0x2

    aput-object v1, v2, v0

    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0e:Z

    if-eqz v0, :cond_2

    .line 8694
    return v4

    .line 8695
    :cond_2
    new-instance v0, Ljava/lang/IllegalStateException;

    invoke-direct {v0, v3}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw v0

    .line 8696
    :cond_3
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/3h;->A03()Lcom/facebook/ads/redexgen/X/3Z;

    move-result-object v8

    .line 8697
    .local v0, "ii":Lcom/facebook/ads/redexgen/X/3Z;
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/3h;->getClientWidth()I

    move-result v7

    .line 8698
    .local v3, "width":I
    iget v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0M:I

    add-int v6, v7, v0

    .line 8699
    .local v5, "widthWithMargin":I
    int-to-float v5, v0

    int-to-float v0, v7

    div-float/2addr v5, v0

    .line 8700
    .local v4, "marginOffset":F
    iget v2, v8, Lcom/facebook/ads/redexgen/X/3Z;->A02:I

    .line 8701
    .local v6, "currentPage":I
    int-to-float v1, p1

    int-to-float v0, v7

    div-float/2addr v1, v0

    iget v0, v8, Lcom/facebook/ads/redexgen/X/3Z;->A00:F

    sub-float/2addr v1, v0

    iget v0, v8, Lcom/facebook/ads/redexgen/X/3Z;->A01:F

    add-float/2addr v0, v5

    div-float/2addr v1, v0

    .line 8702
    .local v7, "pageOffset":F
    int-to-float v0, v6

    mul-float/2addr v0, v1

    float-to-int v0, v0

    .line 8703
    .local v8, "offsetPixels":I
    iput-boolean v4, p0, Lcom/facebook/ads/redexgen/X/3h;->A0e:Z

    .line 8704
    invoke-direct {p0, v2, v1, v0}, Lcom/facebook/ads/redexgen/X/3h;->A0I(IFI)V

    .line 8705
    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0e:Z

    if-eqz v0, :cond_4

    .line 8706
    const/4 v0, 0x1

    return v0

    .line 8707
    :cond_4
    new-instance v0, Ljava/lang/IllegalStateException;

    invoke-direct {v0, v3}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw v0
.end method

.method private final A0a(I)Z
    .locals 5

    .line 8708
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/3h;->findFocus()Landroid/view/View;

    move-result-object v3

    .line 8709
    .local v0, "currentFocused":Landroid/view/View;
    if-ne v3, p0, :cond_a

    .line 8710
    const/4 v3, 0x0

    .line 8711
    .end local v1
    .end local v2
    :cond_0
    :goto_0
    const/4 v4, 0x0

    .line 8712
    .local v1, "handled":Z
    invoke-static {}, Landroid/view/FocusFinder;->getInstance()Landroid/view/FocusFinder;

    move-result-object v0

    invoke-virtual {v0, p0, v3, p1}, Landroid/view/FocusFinder;->findNextFocus(Landroid/view/ViewGroup;Landroid/view/View;I)Landroid/view/View;

    move-result-object v2

    .line 8713
    .local v2, "nextFocused":Landroid/view/View;
    const/16 v1, 0x42

    const/16 v0, 0x11

    if-eqz v2, :cond_6

    if-eq v2, v3, :cond_6

    .line 8714
    if-ne p1, v0, :cond_4

    .line 8715
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0o:Landroid/graphics/Rect;

    invoke-direct {p0, v0, v2}, Lcom/facebook/ads/redexgen/X/3h;->A02(Landroid/graphics/Rect;Landroid/view/View;)Landroid/graphics/Rect;

    move-result-object v0

    iget v1, v0, Landroid/graphics/Rect;->left:I

    .line 8716
    .local v3, "nextLeft":I
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0o:Landroid/graphics/Rect;

    invoke-direct {p0, v0, v3}, Lcom/facebook/ads/redexgen/X/3h;->A02(Landroid/graphics/Rect;Landroid/view/View;)Landroid/graphics/Rect;

    move-result-object v0

    iget v0, v0, Landroid/graphics/Rect;->left:I

    .line 8717
    .local v4, "currLeft":I
    if-eqz v3, :cond_3

    if-lt v1, v0, :cond_3

    .line 8718
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/3h;->A0V()Z

    move-result v4

    .line 8719
    :cond_1
    :goto_1
    if-eqz v4, :cond_2

    .line 8720
    invoke-static {p1}, Landroid/view/SoundEffectConstants;->getContantForFocusDirection(I)I

    move-result v0

    invoke-virtual {p0, v0}, Lcom/facebook/ads/redexgen/X/3h;->playSoundEffect(I)V

    .line 8721
    :cond_2
    return v4

    .line 8722
    :cond_3
    invoke-virtual {v2}, Landroid/view/View;->requestFocus()Z

    move-result v4

    goto :goto_1

    .line 8723
    :cond_4
    if-ne p1, v1, :cond_1

    .line 8724
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0o:Landroid/graphics/Rect;

    invoke-direct {p0, v0, v2}, Lcom/facebook/ads/redexgen/X/3h;->A02(Landroid/graphics/Rect;Landroid/view/View;)Landroid/graphics/Rect;

    move-result-object v0

    iget v1, v0, Landroid/graphics/Rect;->left:I

    .line 8725
    .restart local v3    # "nextLeft":I
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0o:Landroid/graphics/Rect;

    invoke-direct {p0, v0, v3}, Lcom/facebook/ads/redexgen/X/3h;->A02(Landroid/graphics/Rect;Landroid/view/View;)Landroid/graphics/Rect;

    move-result-object v0

    iget v0, v0, Landroid/graphics/Rect;->left:I

    .line 8726
    .restart local v4    # "currLeft":I
    if-eqz v3, :cond_5

    if-gt v1, v0, :cond_5

    .line 8727
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/3h;->A0W()Z

    move-result v4

    goto :goto_1

    .line 8728
    :cond_5
    invoke-virtual {v2}, Landroid/view/View;->requestFocus()Z

    move-result v4

    goto :goto_1

    .line 8729
    :cond_6
    if-eq p1, v0, :cond_7

    const/4 v0, 0x1

    if-ne p1, v0, :cond_8

    .line 8730
    :cond_7
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/3h;->A0V()Z

    move-result v4

    goto :goto_1

    .line 8731
    :cond_8
    if-eq p1, v1, :cond_9

    const/4 v0, 0x2

    if-ne p1, v0, :cond_1

    .line 8732
    :cond_9
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/3h;->A0W()Z

    move-result v4

    goto :goto_1

    .line 8733
    :cond_a
    if-eqz v3, :cond_0

    .line 8734
    const/4 v2, 0x0

    .line 8735
    .local v1, "isChild":Z
    invoke-virtual {v3}, Landroid/view/View;->getParent()Landroid/view/ViewParent;

    move-result-object v1

    .local v2, "parent":Landroid/view/ViewParent;
    :goto_2
    instance-of v0, v1, Landroid/view/ViewGroup;

    if-eqz v0, :cond_b

    .line 8736
    if-ne v1, p0, :cond_c

    .line 8737
    const/4 v2, 0x1

    .line 8738
    .end local v2    # "parent":Landroid/view/ViewParent;
    :cond_b
    if-nez v2, :cond_0

    .line 8739
    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    .line 8740
    .local v2, "sb":Ljava/lang/StringBuilder;
    invoke-virtual {v3}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v4, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 8741
    invoke-virtual {v3}, Landroid/view/View;->getParent()Landroid/view/ViewParent;

    move-result-object v3

    .local v3, "parent":Landroid/view/ViewParent;
    :goto_3
    instance-of v0, v3, Landroid/view/ViewGroup;

    if-eqz v0, :cond_d

    .line 8742
    const/4 v2, 0x0

    const/4 v1, 0x4

    const/16 v0, 0x15

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/3h;->A08(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v4, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v3}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 8743
    invoke-interface {v3}, Landroid/view/ViewParent;->getParent()Landroid/view/ViewParent;

    move-result-object v3

    goto :goto_3

    .line 8744
    :cond_c
    invoke-interface {v1}, Landroid/view/ViewParent;->getParent()Landroid/view/ViewParent;

    move-result-object v1

    goto :goto_2

    .line 8745
    .end local v3    # "parent":Landroid/view/ViewParent;
    :cond_d
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const/16 v2, 0x135

    const/16 v1, 0x48

    const/16 v0, 0x50

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/3h;->A08(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    .line 8746
    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    .line 8747
    const/16 v2, 0x12c

    const/16 v1, 0x9

    const/16 v0, 0x2f

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/3h;->A08(III)Ljava/lang/String;

    move-result-object v0

    invoke-static {v0, v3}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    .line 8748
    const/4 v3, 0x0

    goto/16 :goto_0
.end method

.method private final A0b(Landroid/view/KeyEvent;)Z
    .locals 4

    .line 8749
    const/4 v2, 0x0

    .line 8750
    .local v0, "handled":Z
    invoke-virtual {p1}, Landroid/view/KeyEvent;->getAction()I

    move-result v0

    if-nez v0, :cond_0

    .line 8751
    invoke-virtual {p1}, Landroid/view/KeyEvent;->getKeyCode()I

    move-result v0

    const/4 v1, 0x2

    sparse-switch v0, :sswitch_data_0

    .line 8752
    :cond_0
    :goto_0
    return v2

    .line 8753
    :sswitch_0
    invoke-virtual {p1}, Landroid/view/KeyEvent;->hasNoModifiers()Z

    move-result v0

    if-eqz v0, :cond_1

    .line 8754
    invoke-direct {p0, v1}, Lcom/facebook/ads/redexgen/X/3h;->A0a(I)Z

    move-result v2

    goto :goto_0

    .line 8755
    :cond_1
    const/4 v1, 0x1

    invoke-virtual {p1, v1}, Landroid/view/KeyEvent;->hasModifiers(I)Z

    move-result v0

    if-eqz v0, :cond_0

    .line 8756
    invoke-direct {p0, v1}, Lcom/facebook/ads/redexgen/X/3h;->A0a(I)Z

    move-result v2

    goto :goto_0

    .line 8757
    :sswitch_1
    invoke-virtual {p1, v1}, Landroid/view/KeyEvent;->hasModifiers(I)Z

    move-result v3

    sget-object v2, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const/4 v0, 0x4

    aget-object v1, v2, v0

    const/4 v0, 0x5

    aget-object v0, v2, v0

    invoke-virtual {v1}, Ljava/lang/String;->length()I

    move-result v1

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v0

    if-eq v1, v0, :cond_2

    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    :cond_2
    sget-object v2, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const-string v1, "lC1XoLB2bpdljamdt6Vm"

    const/4 v0, 0x1

    aput-object v1, v2, v0

    if-eqz v3, :cond_3

    .line 8758
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/3h;->A0W()Z

    move-result v2

    goto :goto_0

    .line 8759
    :cond_3
    const/16 v0, 0x42

    invoke-direct {p0, v0}, Lcom/facebook/ads/redexgen/X/3h;->A0a(I)Z

    move-result v2

    .line 8760
    goto :goto_0

    .line 8761
    :sswitch_2
    invoke-virtual {p1, v1}, Landroid/view/KeyEvent;->hasModifiers(I)Z

    move-result v0

    if-eqz v0, :cond_4

    .line 8762
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/3h;->A0V()Z

    move-result v2

    goto :goto_0

    .line 8763
    :cond_4
    const/16 v0, 0x11

    invoke-direct {p0, v0}, Lcom/facebook/ads/redexgen/X/3h;->A0a(I)Z

    move-result v2

    .line 8764
    goto :goto_0

    :sswitch_data_0
    .sparse-switch
        0x15 -> :sswitch_2
        0x16 -> :sswitch_1
        0x3d -> :sswitch_0
    .end sparse-switch
.end method

.method public static A0c(Landroid/view/View;)Z
    .locals 1

    .line 8765
    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object p0

    .line 8766
    .local v0, "clazz":Ljava/lang/Class;, "Ljava/lang/Class<*>;"
    const-class v0, Lcom/facebook/ads/internal/androidx/support/v4/view/ViewPager$DecorView;

    invoke-virtual {p0, v0}, Ljava/lang/Class;->getAnnotation(Ljava/lang/Class;)Ljava/lang/annotation/Annotation;

    move-result-object v0

    if-eqz v0, :cond_0

    const/4 v0, 0x1

    :goto_0
    return v0

    :cond_0
    const/4 v0, 0x0

    goto :goto_0
.end method

.method private final A0d(Landroid/view/View;ZIII)Z
    .locals 14

    .line 8767
    instance-of v0, p1, Landroid/view/ViewGroup;

    const/4 v7, 0x1

    move/from16 v11, p3

    if-eqz v0, :cond_2

    .line 8768
    move-object v3, p1

    check-cast v3, Landroid/view/ViewGroup;

    .line 8769
    .local v1, "group":Landroid/view/ViewGroup;
    invoke-virtual {p1}, Landroid/view/View;->getScrollX()I

    move-result v6

    .line 8770
    .local v3, "scrollX":I
    invoke-virtual {p1}, Landroid/view/View;->getScrollY()I

    move-result v5

    .line 8771
    .local v4, "scrollY":I
    invoke-virtual {v3}, Landroid/view/ViewGroup;->getChildCount()I

    move-result v0

    .line 8772
    .local v5, "count":I
    add-int/lit8 v2, v0, -0x1

    .local v6, "i":I
    :goto_0
    if-ltz v2, :cond_2

    .line 8773
    invoke-virtual {v3, v2}, Landroid/view/ViewGroup;->getChildAt(I)Landroid/view/View;

    move-result-object v9

    .line 8774
    .local v13, "child":Landroid/view/View;
    add-int v1, p4, v6

    invoke-virtual {v9}, Landroid/view/View;->getLeft()I

    move-result v0

    if-lt v1, v0, :cond_0

    add-int v1, p4, v6

    invoke-virtual {v9}, Landroid/view/View;->getRight()I

    move-result v0

    if-ge v1, v0, :cond_0

    add-int v1, p5, v5

    .line 8775
    invoke-virtual {v9}, Landroid/view/View;->getTop()I

    move-result v0

    if-lt v1, v0, :cond_0

    add-int v1, p5, v5

    invoke-virtual {v9}, Landroid/view/View;->getBottom()I

    move-result v0

    if-ge v1, v0, :cond_0

    const/4 v10, 0x1

    add-int v12, p4, v6

    .line 8776
    invoke-virtual {v9}, Landroid/view/View;->getLeft()I

    move-result v0

    sub-int/2addr v12, v0

    add-int v13, p5, v5

    sget-object v1, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const/4 v0, 0x1

    aget-object v0, v1, v0

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v1

    const/16 v0, 0xc

    if-eq v1, v0, :cond_1

    .line 8777
    sget-object v4, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const-string v1, ""

    const/4 v0, 0x4

    aput-object v1, v4, v0

    const-string v1, ""

    const/4 v0, 0x5

    aput-object v1, v4, v0

    invoke-virtual {v9}, Landroid/view/View;->getTop()I

    move-result v0

    sub-int/2addr v13, v0

    .line 8778
    move-object v8, p0

    invoke-direct/range {v8 .. v13}, Lcom/facebook/ads/redexgen/X/3h;->A0d(Landroid/view/View;ZIII)Z

    move-result v0

    if-eqz v0, :cond_0

    .line 8779
    return v7

    .line 8780
    .end local v13    # "child":Landroid/view/View;
    :cond_0
    add-int/lit8 v2, v2, -0x1

    goto :goto_0

    :cond_1
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    .line 8781
    .end local v1    # "group":Landroid/view/ViewGroup;
    .end local v3    # "scrollX":I
    .end local v4    # "scrollY":I
    .end local v5    # "count":I
    .end local v6    # "i":I
    :cond_2
    if-eqz p2, :cond_3

    neg-int v0, v11

    invoke-virtual {p1, v0}, Landroid/view/View;->canScrollHorizontally(I)Z

    move-result v0

    if-eqz v0, :cond_3

    :goto_1
    return v7

    :cond_3
    const/4 v7, 0x0

    goto :goto_1
.end method

.method private getClientWidth()I
    .locals 2

    .line 8942
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/3h;->getMeasuredWidth()I

    move-result v1

    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/3h;->getPaddingLeft()I

    move-result v0

    sub-int/2addr v1, v0

    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/3h;->getPaddingRight()I

    move-result v0

    sub-int/2addr v1, v0

    return v1
.end method

.method private setScrollingCacheEnabled(Z)V
    .locals 1

    .line 9360
    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0n:Z

    if-eq v0, p1, :cond_0

    .line 9361
    iput-boolean p1, p0, Lcom/facebook/ads/redexgen/X/3h;->A0n:Z

    .line 9362
    :cond_0
    return-void
.end method


# virtual methods
.method public final A0e()V
    .locals 11

    .line 8782
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A01:Lcom/facebook/ads/redexgen/X/3E;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/3E;->A01()I

    move-result v3

    .line 8783
    .local v0, "adapterCount":I
    iput v3, p0, Lcom/facebook/ads/redexgen/X/3h;->A0G:I

    .line 8784
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0r:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->size()I

    move-result v1

    iget v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0L:I

    mul-int/lit8 v0, v0, 0x2

    const/4 v5, 0x1

    add-int/2addr v0, v5

    const/4 v4, 0x0

    if-ge v1, v0, :cond_5

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0r:Ljava/util/ArrayList;

    .line 8785
    invoke-virtual {v0}, Ljava/util/ArrayList;->size()I

    move-result v0

    if-ge v0, v3, :cond_5

    const/4 v10, 0x1

    .line 8786
    .local v1, "needPopulate":Z
    :goto_0
    iget v6, p0, Lcom/facebook/ads/redexgen/X/3h;->A00:I

    .line 8787
    .local v2, "newCurrItem":I
    const/4 v9, 0x0

    .line 8788
    .local v5, "isUpdating":Z
    const/4 v2, 0x0

    .local v6, "i":I
    :goto_1
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0r:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->size()I

    move-result v0

    if-ge v2, v0, :cond_6

    .line 8789
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0r:Ljava/util/ArrayList;

    invoke-virtual {v0, v2}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v8

    check-cast v8, Lcom/facebook/ads/redexgen/X/3Z;

    .line 8790
    .local v7, "ii":Lcom/facebook/ads/redexgen/X/3Z;
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/3h;->A01:Lcom/facebook/ads/redexgen/X/3E;

    iget-object v0, v8, Lcom/facebook/ads/redexgen/X/3Z;->A03:Ljava/lang/Object;

    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/3E;->A02(Ljava/lang/Object;)I

    move-result v7

    .line 8791
    .local v8, "newPos":I
    const/4 v0, -0x1

    if-ne v7, v0, :cond_1

    .line 8792
    .end local v7    # "ii":Lcom/facebook/ads/redexgen/X/3Z;
    .end local v8    # "newPos":I
    :cond_0
    :goto_2
    add-int/2addr v2, v5

    goto :goto_1

    .line 8793
    :cond_1
    const/4 v0, -0x2

    if-ne v7, v0, :cond_3

    .line 8794
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0r:Ljava/util/ArrayList;

    invoke-virtual {v0, v2}, Ljava/util/ArrayList;->remove(I)Ljava/lang/Object;

    .line 8795
    add-int/lit8 v2, v2, -0x1

    .line 8796
    if-nez v9, :cond_2

    .line 8797
    const/4 v9, 0x1

    .line 8798
    :cond_2
    iget-object v7, p0, Lcom/facebook/ads/redexgen/X/3h;->A01:Lcom/facebook/ads/redexgen/X/3E;

    iget v1, v8, Lcom/facebook/ads/redexgen/X/3Z;->A02:I

    iget-object v0, v8, Lcom/facebook/ads/redexgen/X/3Z;->A03:Ljava/lang/Object;

    invoke-virtual {v7, p0, v1, v0}, Lcom/facebook/ads/redexgen/X/3E;->A07(Landroid/view/ViewGroup;ILjava/lang/Object;)V

    .line 8799
    const/4 v10, 0x1

    .line 8800
    iget v1, p0, Lcom/facebook/ads/redexgen/X/3h;->A00:I

    iget v0, v8, Lcom/facebook/ads/redexgen/X/3Z;->A02:I

    if-ne v1, v0, :cond_0

    .line 8801
    iget v1, p0, Lcom/facebook/ads/redexgen/X/3h;->A00:I

    add-int/lit8 v0, v3, -0x1

    invoke-static {v1, v0}, Ljava/lang/Math;->min(II)I

    move-result v0

    invoke-static {v4, v0}, Ljava/lang/Math;->max(II)I

    move-result v6

    .line 8802
    const/4 v10, 0x1

    goto :goto_2

    .line 8803
    :cond_3
    iget v0, v8, Lcom/facebook/ads/redexgen/X/3Z;->A02:I

    if-eq v0, v7, :cond_0

    .line 8804
    iget v1, v8, Lcom/facebook/ads/redexgen/X/3Z;->A02:I

    iget v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A00:I

    if-ne v1, v0, :cond_4

    .line 8805
    move v6, v7

    .line 8806
    :cond_4
    iput v7, v8, Lcom/facebook/ads/redexgen/X/3Z;->A02:I

    .line 8807
    const/4 v10, 0x1

    goto :goto_2

    .line 8808
    :cond_5
    const/4 v10, 0x0

    goto :goto_0

    .line 8809
    .end local v6    # "i":I
    :cond_6
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/3h;->A0r:Ljava/util/ArrayList;

    sget-object v0, Lcom/facebook/ads/redexgen/X/3h;->A0x:Ljava/util/Comparator;

    invoke-static {v1, v0}, Ljava/util/Collections;->sort(Ljava/util/List;Ljava/util/Comparator;)V

    .line 8810
    if-eqz v10, :cond_9

    .line 8811
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/3h;->getChildCount()I

    move-result v3

    .line 8812
    .local v6, "childCount":I
    const/4 v2, 0x0

    .local v7, "i":I
    :goto_3
    if-ge v2, v3, :cond_8

    .line 8813
    invoke-virtual {p0, v2}, Lcom/facebook/ads/redexgen/X/3h;->getChildAt(I)Landroid/view/View;

    move-result-object v0

    .line 8814
    .local v8, "child":Landroid/view/View;
    invoke-virtual {v0}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    move-result-object v1

    check-cast v1, Lcom/facebook/ads/redexgen/X/3a;

    .line 8815
    .local v9, "lp":Lcom/facebook/ads/redexgen/X/3a;
    iget-boolean v0, v1, Lcom/facebook/ads/redexgen/X/3a;->A05:Z

    if-nez v0, :cond_7

    .line 8816
    const/4 v0, 0x0

    iput v0, v1, Lcom/facebook/ads/redexgen/X/3a;->A00:F

    .line 8817
    .end local v8    # "child":Landroid/view/View;
    .end local v9    # "lp":Lcom/facebook/ads/redexgen/X/3a;
    :cond_7
    add-int/lit8 v2, v2, 0x1

    goto :goto_3

    .line 8818
    .end local v7    # "i":I
    :cond_8
    invoke-direct {p0, v6, v4, v5}, Lcom/facebook/ads/redexgen/X/3h;->A0N(IZZ)V

    sget-object v1, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const/4 v0, 0x2

    aget-object v0, v1, v0

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v1

    const/16 v0, 0xe

    if-eq v1, v0, :cond_a

    .line 8819
    sget-object v2, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const-string v1, "iBz2AIJ6C"

    const/4 v0, 0x2

    aput-object v1, v2, v0

    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/3h;->requestLayout()V

    .line 8820
    .end local v6    # "childCount":I
    :cond_9
    return-void

    :cond_a
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0
.end method

.method public final A0f()V
    .locals 1

    .line 8821
    iget v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A00:I

    invoke-direct {p0, v0}, Lcom/facebook/ads/redexgen/X/3h;->A0G(I)V

    .line 8822
    return-void
.end method

.method public final addFocusables(Ljava/util/ArrayList;II)V
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/ArrayList<",
            "Landroid/view/View;",
            ">;II)V"
        }
    .end annotation

    .line 8823
    .local p2, "views":Ljava/util/ArrayList;, "Ljava/util/ArrayList<Landroid/view/View;>;"
    invoke-virtual {p1}, Ljava/util/ArrayList;->size()I

    move-result v5

    .line 8824
    .local v0, "focusableCount":I
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/3h;->getDescendantFocusability()I

    move-result v4

    .line 8825
    .local v1, "descendantFocusability":I
    const/high16 v0, 0x60000

    if-eq v4, v0, :cond_1

    .line 8826
    const/4 v3, 0x0

    .local v2, "i":I
    :goto_0
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/3h;->getChildCount()I

    move-result v0

    if-ge v3, v0, :cond_1

    .line 8827
    invoke-virtual {p0, v3}, Lcom/facebook/ads/redexgen/X/3h;->getChildAt(I)Landroid/view/View;

    move-result-object v2

    .line 8828
    .local v3, "child":Landroid/view/View;
    invoke-virtual {v2}, Landroid/view/View;->getVisibility()I

    move-result v0

    if-nez v0, :cond_0

    .line 8829
    invoke-direct {p0, v2}, Lcom/facebook/ads/redexgen/X/3h;->A07(Landroid/view/View;)Lcom/facebook/ads/redexgen/X/3Z;

    move-result-object v0

    .line 8830
    .local v4, "ii":Lcom/facebook/ads/redexgen/X/3Z;
    if-eqz v0, :cond_0

    iget v1, v0, Lcom/facebook/ads/redexgen/X/3Z;->A02:I

    iget v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A00:I

    if-ne v1, v0, :cond_0

    .line 8831
    invoke-virtual {v2, p1, p2, p3}, Landroid/view/View;->addFocusables(Ljava/util/ArrayList;II)V

    .line 8832
    .end local v3    # "child":Landroid/view/View;
    .end local v4    # "ii":Lcom/facebook/ads/redexgen/X/3Z;
    :cond_0
    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    .line 8833
    .end local v2    # "i":I
    :cond_1
    const/high16 v0, 0x40000

    if-ne v4, v0, :cond_2

    .line 8834
    invoke-virtual {p1}, Ljava/util/ArrayList;->size()I

    move-result v0

    if-ne v5, v0, :cond_5

    .line 8835
    :cond_2
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/3h;->isFocusable()Z

    move-result v0

    if-nez v0, :cond_3

    .line 8836
    return-void

    .line 8837
    :cond_3
    and-int/lit8 v1, p3, 0x1

    const/4 v0, 0x1

    if-ne v1, v0, :cond_4

    .line 8838
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/3h;->isInTouchMode()Z

    move-result v0

    if-eqz v0, :cond_4

    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/3h;->isFocusableInTouchMode()Z

    move-result v0

    if-nez v0, :cond_4

    .line 8839
    return-void

    .line 8840
    :cond_4
    if-eqz p1, :cond_5

    .line 8841
    invoke-virtual {p1, p0}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 8842
    :cond_5
    return-void
.end method

.method public final addTouchables(Ljava/util/ArrayList;)V
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/ArrayList<",
            "Landroid/view/View;",
            ">;)V"
        }
    .end annotation

    .line 8843
    .local p2, "views":Ljava/util/ArrayList;, "Ljava/util/ArrayList<Landroid/view/View;>;"
    const/4 v3, 0x0

    .local v0, "i":I
    :goto_0
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/3h;->getChildCount()I

    move-result v0

    if-ge v3, v0, :cond_1

    .line 8844
    invoke-virtual {p0, v3}, Lcom/facebook/ads/redexgen/X/3h;->getChildAt(I)Landroid/view/View;

    move-result-object v2

    .line 8845
    .local v1, "child":Landroid/view/View;
    invoke-virtual {v2}, Landroid/view/View;->getVisibility()I

    move-result v0

    if-nez v0, :cond_0

    .line 8846
    invoke-direct {p0, v2}, Lcom/facebook/ads/redexgen/X/3h;->A07(Landroid/view/View;)Lcom/facebook/ads/redexgen/X/3Z;

    move-result-object v0

    .line 8847
    .local v2, "ii":Lcom/facebook/ads/redexgen/X/3Z;
    if-eqz v0, :cond_0

    iget v1, v0, Lcom/facebook/ads/redexgen/X/3Z;->A02:I

    iget v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A00:I

    if-ne v1, v0, :cond_0

    .line 8848
    invoke-virtual {v2, p1}, Landroid/view/View;->addTouchables(Ljava/util/ArrayList;)V

    .line 8849
    .end local v1    # "child":Landroid/view/View;
    .end local v2    # "ii":Lcom/facebook/ads/redexgen/X/3Z;
    :cond_0
    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    .line 8850
    .end local v0    # "i":I
    :cond_1
    return-void
.end method

.method public final addView(Landroid/view/View;ILandroid/view/ViewGroup$LayoutParams;)V
    .locals 3

    .line 8851
    invoke-virtual {p0, p3}, Lcom/facebook/ads/redexgen/X/3h;->checkLayoutParams(Landroid/view/ViewGroup$LayoutParams;)Z

    move-result v0

    if-nez v0, :cond_0

    .line 8852
    invoke-virtual {p0, p3}, Lcom/facebook/ads/redexgen/X/3h;->generateLayoutParams(Landroid/view/ViewGroup$LayoutParams;)Landroid/view/ViewGroup$LayoutParams;

    move-result-object p3

    .line 8853
    :cond_0
    move-object v2, p3

    check-cast v2, Lcom/facebook/ads/redexgen/X/3a;

    .line 8854
    .local v0, "lp":Lcom/facebook/ads/redexgen/X/3a;
    iget-boolean v1, v2, Lcom/facebook/ads/redexgen/X/3a;->A05:Z

    invoke-static {p1}, Lcom/facebook/ads/redexgen/X/3h;->A0c(Landroid/view/View;)Z

    move-result v0

    or-int/2addr v1, v0

    iput-boolean v1, v2, Lcom/facebook/ads/redexgen/X/3a;->A05:Z

    .line 8855
    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0h:Z

    if-eqz v0, :cond_2

    .line 8856
    if-eqz v2, :cond_1

    iget-boolean v0, v2, Lcom/facebook/ads/redexgen/X/3a;->A05:Z

    if-nez v0, :cond_4

    .line 8857
    :cond_1
    const/4 v0, 0x1

    iput-boolean v0, v2, Lcom/facebook/ads/redexgen/X/3a;->A03:Z

    sget-object v1, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const/4 v0, 0x2

    aget-object v0, v1, v0

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v1

    const/16 v0, 0xe

    if-eq v1, v0, :cond_3

    .line 8858
    sget-object v2, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const-string v1, "XL6oa3uLuKs2IOpHyjfCXdHiJnTalh"

    const/4 v0, 0x1

    aput-object v1, v2, v0

    invoke-virtual {p0, p1, p2, p3}, Lcom/facebook/ads/redexgen/X/3h;->addViewInLayout(Landroid/view/View;ILandroid/view/ViewGroup$LayoutParams;)Z

    .line 8859
    :goto_0
    return-void

    .line 8860
    :cond_2
    invoke-super {p0, p1, p2, p3}, Landroid/view/ViewGroup;->addView(Landroid/view/View;ILandroid/view/ViewGroup$LayoutParams;)V

    goto :goto_0

    :cond_3
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    .line 8861
    :cond_4
    const/16 v2, 0x56

    const/16 v1, 0x29

    const/16 v0, 0x10

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/3h;->A08(III)Ljava/lang/String;

    move-result-object v1

    new-instance v0, Ljava/lang/IllegalStateException;

    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw v0
.end method

.method public final canScrollHorizontally(I)Z
    .locals 4

    .line 8862
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A01:Lcom/facebook/ads/redexgen/X/3E;

    const/4 v3, 0x0

    if-nez v0, :cond_0

    .line 8863
    return v3

    .line 8864
    :cond_0
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/3h;->getClientWidth()I

    move-result v0

    .line 8865
    .local v0, "width":I
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/3h;->getScrollX()I

    move-result v2

    .line 8866
    .local v2, "scrollX":I
    if-gez p1, :cond_2

    .line 8867
    int-to-float v1, v0

    iget v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A02:F

    mul-float/2addr v1, v0

    float-to-int v0, v1

    if-le v2, v0, :cond_1

    const/4 v3, 0x1

    :cond_1
    return v3

    .line 8868
    :cond_2
    if-lez p1, :cond_4

    .line 8869
    int-to-float v1, v0

    iget v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A07:F

    mul-float/2addr v1, v0

    float-to-int v0, v1

    if-ge v2, v0, :cond_3

    const/4 v3, 0x1

    :cond_3
    return v3

    .line 8870
    :cond_4
    return v3
.end method

.method public final checkLayoutParams(Landroid/view/ViewGroup$LayoutParams;)Z
    .locals 1

    .line 8871
    instance-of v0, p1, Lcom/facebook/ads/redexgen/X/3a;

    if-eqz v0, :cond_0

    invoke-super {p0, p1}, Landroid/view/ViewGroup;->checkLayoutParams(Landroid/view/ViewGroup$LayoutParams;)Z

    move-result v0

    if-eqz v0, :cond_0

    const/4 v0, 0x1

    :goto_0
    return v0

    :cond_0
    const/4 v0, 0x0

    goto :goto_0
.end method

.method public final computeScroll()V
    .locals 5

    .line 8872
    const/4 v1, 0x1

    iput-boolean v1, p0, Lcom/facebook/ads/redexgen/X/3h;->A0j:Z

    .line 8873
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0X:Landroid/widget/Scroller;

    invoke-virtual {v0}, Landroid/widget/Scroller;->isFinished()Z

    move-result v0

    if-nez v0, :cond_2

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0X:Landroid/widget/Scroller;

    invoke-virtual {v0}, Landroid/widget/Scroller;->computeScrollOffset()Z

    move-result v0

    if-eqz v0, :cond_2

    .line 8874
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/3h;->getScrollX()I

    move-result v4

    .line 8875
    .local v0, "oldX":I
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/3h;->getScrollY()I

    move-result v3

    .line 8876
    .local v1, "oldY":I
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0X:Landroid/widget/Scroller;

    invoke-virtual {v0}, Landroid/widget/Scroller;->getCurrX()I

    move-result v2

    .line 8877
    .local v2, "x":I
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0X:Landroid/widget/Scroller;

    invoke-virtual {v0}, Landroid/widget/Scroller;->getCurrY()I

    move-result v1

    .line 8878
    .local v3, "y":I
    if-ne v4, v2, :cond_0

    if-eq v3, v1, :cond_1

    .line 8879
    :cond_0
    invoke-virtual {p0, v2, v1}, Lcom/facebook/ads/redexgen/X/3h;->scrollTo(II)V

    .line 8880
    invoke-direct {p0, v2}, Lcom/facebook/ads/redexgen/X/3h;->A0Z(I)Z

    move-result v0

    if-nez v0, :cond_1

    .line 8881
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0X:Landroid/widget/Scroller;

    invoke-virtual {v0}, Landroid/widget/Scroller;->abortAnimation()V

    .line 8882
    const/4 v0, 0x0

    invoke-virtual {p0, v0, v1}, Lcom/facebook/ads/redexgen/X/3h;->scrollTo(II)V

    .line 8883
    :cond_1
    invoke-static {p0}, Lcom/facebook/ads/redexgen/X/3T;->A07(Landroid/view/View;)V

    .line 8884
    return-void

    .line 8885
    .end local v0    # "oldX":I
    .end local v1    # "oldY":I
    .end local v2    # "x":I
    .end local v3    # "y":I
    :cond_2
    invoke-direct {p0, v1}, Lcom/facebook/ads/redexgen/X/3h;->A0R(Z)V

    .line 8886
    return-void
.end method

.method public final dispatchKeyEvent(Landroid/view/KeyEvent;)Z
    .locals 1

    .line 8887
    invoke-super {p0, p1}, Landroid/view/ViewGroup;->dispatchKeyEvent(Landroid/view/KeyEvent;)Z

    move-result v0

    if-nez v0, :cond_0

    invoke-direct {p0, p1}, Lcom/facebook/ads/redexgen/X/3h;->A0b(Landroid/view/KeyEvent;)Z

    move-result v0

    if-eqz v0, :cond_1

    :cond_0
    const/4 v0, 0x1

    :goto_0
    return v0

    :cond_1
    const/4 v0, 0x0

    goto :goto_0
.end method

.method public final dispatchPopulateAccessibilityEvent(Landroid/view/accessibility/AccessibilityEvent;)Z
    .locals 7

    .line 8888
    invoke-virtual {p1}, Landroid/view/accessibility/AccessibilityEvent;->getEventType()I

    move-result v1

    const/16 v0, 0x1000

    if-ne v1, v0, :cond_0

    .line 8889
    invoke-super {p0, p1}, Landroid/view/ViewGroup;->dispatchPopulateAccessibilityEvent(Landroid/view/accessibility/AccessibilityEvent;)Z

    move-result v0

    return v0

    .line 8890
    :cond_0
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/3h;->getChildCount()I

    move-result v5

    .line 8891
    .local v0, "childCount":I
    const/4 v4, 0x0

    .local v1, "i":I
    :goto_0
    if-ge v4, v5, :cond_3

    .line 8892
    invoke-virtual {p0, v4}, Lcom/facebook/ads/redexgen/X/3h;->getChildAt(I)Landroid/view/View;

    move-result-object v6

    .line 8893
    .local v2, "child":Landroid/view/View;
    invoke-virtual {v6}, Landroid/view/View;->getVisibility()I

    move-result v0

    if-nez v0, :cond_2

    .line 8894
    invoke-direct {p0, v6}, Lcom/facebook/ads/redexgen/X/3h;->A07(Landroid/view/View;)Lcom/facebook/ads/redexgen/X/3Z;

    move-result-object v0

    .line 8895
    .local v3, "ii":Lcom/facebook/ads/redexgen/X/3Z;
    if-eqz v0, :cond_2

    iget v3, v0, Lcom/facebook/ads/redexgen/X/3Z;->A02:I

    sget-object v2, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const/4 v0, 0x7

    aget-object v1, v2, v0

    const/4 v0, 0x0

    aget-object v2, v2, v0

    const/16 v0, 0xa

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    invoke-virtual {v2, v0}, Ljava/lang/String;->charAt(I)C

    move-result v0

    if-eq v1, v0, :cond_1

    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    :cond_1
    sget-object v2, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const-string v1, ""

    const/4 v0, 0x4

    aput-object v1, v2, v0

    const-string v1, ""

    const/4 v0, 0x5

    aput-object v1, v2, v0

    iget v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A00:I

    if-ne v3, v0, :cond_2

    .line 8896
    invoke-virtual {v6, p1}, Landroid/view/View;->dispatchPopulateAccessibilityEvent(Landroid/view/accessibility/AccessibilityEvent;)Z

    move-result v0

    if-eqz v0, :cond_2

    .line 8897
    const/4 v0, 0x1

    return v0

    .line 8898
    .end local v2    # "child":Landroid/view/View;
    .end local v3    # "ii":Lcom/facebook/ads/redexgen/X/3Z;
    :cond_2
    add-int/lit8 v4, v4, 0x1

    goto :goto_0

    .line 8899
    .end local v1    # "i":I
    :cond_3
    const/4 v0, 0x0

    return v0
.end method

.method public final draw(Landroid/graphics/Canvas;)V
    .locals 7

    .line 8900
    invoke-super {p0, p1}, Landroid/view/ViewGroup;->draw(Landroid/graphics/Canvas;)V

    .line 8901
    const/4 v6, 0x0

    .line 8902
    .local v0, "needsInvalidate":Z
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/3h;->getOverScrollMode()I

    move-result v0

    .line 8903
    .local v1, "overScrollMode":I
    if-eqz v0, :cond_1

    const/4 v4, 0x1

    if-ne v0, v4, :cond_6

    iget-object v3, p0, Lcom/facebook/ads/redexgen/X/3h;->A01:Lcom/facebook/ads/redexgen/X/3E;

    sget-object v2, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const/4 v0, 0x7

    aget-object v1, v2, v0

    const/4 v0, 0x0

    aget-object v2, v2, v0

    const/16 v0, 0xa

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    invoke-virtual {v2, v0}, Ljava/lang/String;->charAt(I)C

    move-result v0

    if-eq v1, v0, :cond_0

    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    :cond_0
    sget-object v2, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const-string v1, "uCo1AgFx4bPu4bwpitwpleYcuNfeFds"

    const/4 v0, 0x1

    aput-object v1, v2, v0

    if-eqz v3, :cond_6

    .line 8904
    invoke-virtual {v3}, Lcom/facebook/ads/redexgen/X/3E;->A01()I

    move-result v0

    if-le v0, v4, :cond_6

    .line 8905
    :cond_1
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0V:Landroid/widget/EdgeEffect;

    invoke-virtual {v0}, Landroid/widget/EdgeEffect;->isFinished()Z

    move-result v3

    sget-object v1, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const/4 v0, 0x1

    aget-object v0, v1, v0

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v1

    const/16 v0, 0xc

    if-eq v1, v0, :cond_5

    sget-object v2, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const-string v1, "sR19g1oBfMrUqPxbyG4ArbsnA"

    const/4 v0, 0x1

    aput-object v1, v2, v0

    if-nez v3, :cond_2

    .line 8906
    :goto_0
    invoke-virtual {p1}, Landroid/graphics/Canvas;->save()I

    move-result v5

    .line 8907
    .local v2, "restoreCount":I
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/3h;->getHeight()I

    move-result v4

    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/3h;->getPaddingTop()I

    move-result v0

    sub-int/2addr v4, v0

    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/3h;->getPaddingBottom()I

    move-result v0

    sub-int/2addr v4, v0

    .line 8908
    .local v3, "height":I
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/3h;->getWidth()I

    move-result v3

    .line 8909
    .local v4, "width":I
    const/high16 v0, 0x43870000    # 270.0f

    invoke-virtual {p1, v0}, Landroid/graphics/Canvas;->rotate(F)V

    .line 8910
    neg-int v1, v4

    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/3h;->getPaddingTop()I

    move-result v0

    add-int/2addr v1, v0

    int-to-float v2, v1

    iget v1, p0, Lcom/facebook/ads/redexgen/X/3h;->A02:F

    int-to-float v0, v3

    mul-float/2addr v1, v0

    invoke-virtual {p1, v2, v1}, Landroid/graphics/Canvas;->translate(FF)V

    .line 8911
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0V:Landroid/widget/EdgeEffect;

    invoke-virtual {v0, v4, v3}, Landroid/widget/EdgeEffect;->setSize(II)V

    .line 8912
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0V:Landroid/widget/EdgeEffect;

    invoke-virtual {v0, p1}, Landroid/widget/EdgeEffect;->draw(Landroid/graphics/Canvas;)Z

    move-result v0

    or-int/2addr v6, v0

    .line 8913
    invoke-virtual {p1, v5}, Landroid/graphics/Canvas;->restoreToCount(I)V

    .line 8914
    .end local v2    # "restoreCount":I
    .end local v3    # "height":I
    .end local v4    # "width":I
    :cond_2
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0W:Landroid/widget/EdgeEffect;

    invoke-virtual {v0}, Landroid/widget/EdgeEffect;->isFinished()Z

    move-result v0

    if-nez v0, :cond_3

    .line 8915
    invoke-virtual {p1}, Landroid/graphics/Canvas;->save()I

    move-result v3

    .line 8916
    .restart local v2    # "restoreCount":I
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/3h;->getWidth()I

    move-result v5

    .line 8917
    .local v3, "width":I
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/3h;->getHeight()I

    move-result v4

    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/3h;->getPaddingTop()I

    move-result v0

    sub-int/2addr v4, v0

    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/3h;->getPaddingBottom()I

    move-result v0

    sub-int/2addr v4, v0

    .line 8918
    .local v4, "height":I
    const/high16 v0, 0x42b40000    # 90.0f

    invoke-virtual {p1, v0}, Landroid/graphics/Canvas;->rotate(F)V

    .line 8919
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/3h;->getPaddingTop()I

    move-result v0

    neg-int v0, v0

    int-to-float v2, v0

    iget v1, p0, Lcom/facebook/ads/redexgen/X/3h;->A07:F

    const/high16 v0, 0x3f800000    # 1.0f

    add-float/2addr v1, v0

    neg-float v1, v1

    int-to-float v0, v5

    mul-float/2addr v1, v0

    invoke-virtual {p1, v2, v1}, Landroid/graphics/Canvas;->translate(FF)V

    .line 8920
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0W:Landroid/widget/EdgeEffect;

    invoke-virtual {v0, v4, v5}, Landroid/widget/EdgeEffect;->setSize(II)V

    .line 8921
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0W:Landroid/widget/EdgeEffect;

    invoke-virtual {v0, p1}, Landroid/widget/EdgeEffect;->draw(Landroid/graphics/Canvas;)Z

    move-result v0

    or-int/2addr v6, v0

    .line 8922
    invoke-virtual {p1, v3}, Landroid/graphics/Canvas;->restoreToCount(I)V

    .line 8923
    .end local v2    # "restoreCount":I
    .end local v3    # "width":I
    .end local v4    # "height":I
    :cond_3
    :goto_1
    if-eqz v6, :cond_4

    .line 8924
    invoke-static {p0}, Lcom/facebook/ads/redexgen/X/3T;->A07(Landroid/view/View;)V

    .line 8925
    :cond_4
    return-void

    :cond_5
    sget-object v2, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const-string v1, ""

    const/4 v0, 0x4

    aput-object v1, v2, v0

    const-string v1, ""

    const/4 v0, 0x5

    aput-object v1, v2, v0

    if-nez v3, :cond_2

    goto/16 :goto_0

    .line 8926
    :cond_6
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0V:Landroid/widget/EdgeEffect;

    invoke-virtual {v0}, Landroid/widget/EdgeEffect;->finish()V

    .line 8927
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0W:Landroid/widget/EdgeEffect;

    invoke-virtual {v0}, Landroid/widget/EdgeEffect;->finish()V

    goto :goto_1
.end method

.method public final drawableStateChanged()V
    .locals 2

    .line 8928
    invoke-super {p0}, Landroid/view/ViewGroup;->drawableStateChanged()V

    .line 8929
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/3h;->A0S:Landroid/graphics/drawable/Drawable;

    .line 8930
    .local v0, "d":Landroid/graphics/drawable/Drawable;
    if-eqz v1, :cond_0

    invoke-virtual {v1}, Landroid/graphics/drawable/Drawable;->isStateful()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 8931
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/3h;->getDrawableState()[I

    move-result-object v0

    invoke-virtual {v1, v0}, Landroid/graphics/drawable/Drawable;->setState([I)Z

    .line 8932
    :cond_0
    return-void
.end method

.method public final generateDefaultLayoutParams()Landroid/view/ViewGroup$LayoutParams;
    .locals 1

    .line 8933
    new-instance v0, Lcom/facebook/ads/redexgen/X/3a;

    invoke-direct {v0}, Lcom/facebook/ads/redexgen/X/3a;-><init>()V

    return-object v0
.end method

.method public final generateLayoutParams(Landroid/util/AttributeSet;)Landroid/view/ViewGroup$LayoutParams;
    .locals 2

    .line 8934
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/3h;->getContext()Landroid/content/Context;

    move-result-object v1

    new-instance v0, Lcom/facebook/ads/redexgen/X/3a;

    invoke-direct {v0, v1, p1}, Lcom/facebook/ads/redexgen/X/3a;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;)V

    return-object v0
.end method

.method public final generateLayoutParams(Landroid/view/ViewGroup$LayoutParams;)Landroid/view/ViewGroup$LayoutParams;
    .locals 1

    .line 8935
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/3h;->generateDefaultLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    move-result-object v0

    return-object v0
.end method

.method public getAdapter()Lcom/facebook/ads/redexgen/X/3E;
    .locals 1

    .line 8936
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A01:Lcom/facebook/ads/redexgen/X/3E;

    return-object v0
.end method

.method public final getChildDrawingOrder(II)I
    .locals 2

    .line 8937
    iget v1, p0, Lcom/facebook/ads/redexgen/X/3h;->A0F:I

    const/4 v0, 0x2

    if-ne v1, v0, :cond_0

    add-int/lit8 v1, p1, -0x1

    sub-int/2addr v1, p2

    .line 8938
    .local v0, "index":I
    :goto_0
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0b:Ljava/util/ArrayList;

    .line 8939
    invoke-virtual {v0, v1}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroid/view/View;

    invoke-virtual {v0}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    move-result-object v0

    check-cast v0, Lcom/facebook/ads/redexgen/X/3a;

    iget v0, v0, Lcom/facebook/ads/redexgen/X/3a;->A01:I

    .line 8940
    .local v1, "result":I
    return v0

    .line 8941
    :cond_0
    move v1, p2

    goto :goto_0
.end method

.method public getCurrentItem()I
    .locals 1

    .line 8943
    iget v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A00:I

    return v0
.end method

.method public getOffscreenPageLimit()I
    .locals 1

    .line 8944
    iget v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0L:I

    return v0
.end method

.method public getPageMargin()I
    .locals 1

    .line 8945
    iget v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0M:I

    return v0
.end method

.method public final onAttachedToWindow()V
    .locals 1

    .line 8946
    invoke-super {p0}, Landroid/view/ViewGroup;->onAttachedToWindow()V

    .line 8947
    const/4 v0, 0x1

    iput-boolean v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0g:Z

    .line 8948
    return-void
.end method

.method public final onDetachedFromWindow()V
    .locals 1

    .line 8949
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0q:Ljava/lang/Runnable;

    invoke-virtual {p0, v0}, Lcom/facebook/ads/redexgen/X/3h;->removeCallbacks(Ljava/lang/Runnable;)Z

    .line 8950
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0X:Landroid/widget/Scroller;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Landroid/widget/Scroller;->isFinished()Z

    move-result v0

    if-nez v0, :cond_0

    .line 8951
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0X:Landroid/widget/Scroller;

    invoke-virtual {v0}, Landroid/widget/Scroller;->abortAnimation()V

    .line 8952
    :cond_0
    invoke-super {p0}, Landroid/view/ViewGroup;->onDetachedFromWindow()V

    .line 8953
    return-void
.end method

.method public final onDraw(Landroid/graphics/Canvas;)V
    .locals 17

    .line 8954
    move-object/from16 v1, p0

    move-object v10, v1

    move-object/from16 v11, p1

    invoke-super {v1, v11}, Landroid/view/ViewGroup;->onDraw(Landroid/graphics/Canvas;)V

    .line 8955
    iget v0, v10, Lcom/facebook/ads/redexgen/X/3h;->A0M:I

    if-lez v0, :cond_4

    iget-object v0, v10, Lcom/facebook/ads/redexgen/X/3h;->A0S:Landroid/graphics/drawable/Drawable;

    if-eqz v0, :cond_4

    iget-object v0, v10, Lcom/facebook/ads/redexgen/X/3h;->A0r:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->size()I

    move-result v0

    if-lez v0, :cond_4

    iget-object v0, v10, Lcom/facebook/ads/redexgen/X/3h;->A01:Lcom/facebook/ads/redexgen/X/3E;

    if-eqz v0, :cond_4

    .line 8956
    invoke-virtual {v1}, Lcom/facebook/ads/redexgen/X/3h;->getScrollX()I

    move-result v9

    .line 8957
    .local v1, "scrollX":I
    invoke-virtual {v1}, Lcom/facebook/ads/redexgen/X/3h;->getWidth()I

    move-result v8

    .line 8958
    .local v2, "width":I
    iget v0, v10, Lcom/facebook/ads/redexgen/X/3h;->A0M:I

    int-to-float v7, v0

    int-to-float v0, v8

    div-float/2addr v7, v0

    .line 8959
    .local v3, "marginOffset":F
    const/4 v6, 0x0

    .line 8960
    .local v4, "itemIndex":I
    iget-object v1, v10, Lcom/facebook/ads/redexgen/X/3h;->A0r:Ljava/util/ArrayList;

    const/4 v0, 0x0

    invoke-virtual {v1, v0}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lcom/facebook/ads/redexgen/X/3Z;

    .line 8961
    .local v5, "ii":Lcom/facebook/ads/redexgen/X/3Z;
    iget v2, v3, Lcom/facebook/ads/redexgen/X/3Z;->A00:F

    .line 8962
    .local v6, "offset":F
    iget-object v0, v10, Lcom/facebook/ads/redexgen/X/3h;->A0r:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->size()I

    move-result v5

    .line 8963
    .local v7, "itemCount":I
    iget v4, v3, Lcom/facebook/ads/redexgen/X/3Z;->A02:I

    .line 8964
    .local v8, "firstPos":I
    iget-object v1, v10, Lcom/facebook/ads/redexgen/X/3h;->A0r:Ljava/util/ArrayList;

    add-int/lit8 v0, v5, -0x1

    invoke-virtual {v1, v0}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/facebook/ads/redexgen/X/3Z;

    iget v0, v0, Lcom/facebook/ads/redexgen/X/3Z;->A02:I

    move/from16 v16, v0

    .line 8965
    .local v9, "lastPos":I
    .local v10, "pos":I
    :goto_0
    move/from16 v0, v16

    if-ge v4, v0, :cond_4

    .line 8966
    :goto_1
    iget v0, v3, Lcom/facebook/ads/redexgen/X/3Z;->A02:I

    if-le v4, v0, :cond_2

    if-ge v6, v5, :cond_2

    .line 8967
    iget-object v0, v10, Lcom/facebook/ads/redexgen/X/3h;->A0r:Ljava/util/ArrayList;

    add-int/lit8 v6, v6, 0x1

    invoke-virtual {v0, v6}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v3

    sget-object v12, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const/4 v0, 0x7

    aget-object v1, v12, v0

    const/4 v0, 0x0

    aget-object v12, v12, v0

    const/16 v0, 0xa

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    invoke-virtual {v12, v0}, Ljava/lang/String;->charAt(I)C

    move-result v0

    if-eq v1, v0, :cond_1

    :cond_0
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    :cond_1
    sget-object v12, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const-string v1, "Qpfq"

    const/4 v0, 0x2

    aput-object v1, v12, v0

    check-cast v3, Lcom/facebook/ads/redexgen/X/3Z;

    goto :goto_1

    .line 8968
    :cond_2
    iget v0, v3, Lcom/facebook/ads/redexgen/X/3Z;->A02:I

    if-ne v4, v0, :cond_6

    .line 8969
    iget v13, v3, Lcom/facebook/ads/redexgen/X/3Z;->A00:F

    iget v0, v3, Lcom/facebook/ads/redexgen/X/3Z;->A01:F

    add-float/2addr v13, v0

    int-to-float v0, v8

    mul-float/2addr v13, v0

    .line 8970
    .local v11, "drawAt":F
    iget v2, v3, Lcom/facebook/ads/redexgen/X/3Z;->A00:F

    iget v0, v3, Lcom/facebook/ads/redexgen/X/3Z;->A01:F

    add-float/2addr v2, v0

    add-float/2addr v2, v7

    .line 8971
    .end local v6    # "offset":F
    .local v12, "offset":F
    .end local v12    # "offset":F
    .local v11, "drawAt":F
    :goto_2
    iget v0, v10, Lcom/facebook/ads/redexgen/X/3h;->A0M:I

    int-to-float v1, v0

    add-float/2addr v1, v13

    int-to-float v0, v9

    cmpl-float v0, v1, v0

    if-lez v0, :cond_3

    .line 8972
    iget-object v15, v10, Lcom/facebook/ads/redexgen/X/3h;->A0S:Landroid/graphics/drawable/Drawable;

    invoke-static {v13}, Ljava/lang/Math;->round(F)I

    move-result v14

    iget v12, v10, Lcom/facebook/ads/redexgen/X/3h;->A0Q:I

    iget v0, v10, Lcom/facebook/ads/redexgen/X/3h;->A0M:I

    int-to-float v0, v0

    add-float/2addr v0, v13

    .line 8973
    invoke-static {v0}, Ljava/lang/Math;->round(F)I

    move-result v1

    .end local v3    # "marginOffset":F
    .local v16, "marginOffset":F
    iget v0, v10, Lcom/facebook/ads/redexgen/X/3h;->A09:I

    .line 8974
    invoke-virtual {v15, v14, v12, v1, v0}, Landroid/graphics/drawable/Drawable;->setBounds(IIII)V

    .line 8975
    iget-object v14, v10, Lcom/facebook/ads/redexgen/X/3h;->A0S:Landroid/graphics/drawable/Drawable;

    sget-object v12, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const/4 v0, 0x7

    aget-object v1, v12, v0

    const/4 v0, 0x0

    aget-object v12, v12, v0

    const/16 v0, 0xa

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    invoke-virtual {v12, v0}, Ljava/lang/String;->charAt(I)C

    move-result v0

    if-eq v1, v0, :cond_5

    sget-object v12, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const-string v1, ""

    const/4 v0, 0x1

    aput-object v1, v12, v0

    invoke-virtual {v14, v11}, Landroid/graphics/drawable/Drawable;->draw(Landroid/graphics/Canvas;)V

    .line 8976
    .end local v3
    .restart local v16    # "marginOffset":F
    :cond_3
    :goto_3
    add-int v14, v9, v8

    sget-object v1, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const/4 v0, 0x1

    aget-object v0, v1, v0

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v1

    const/16 v0, 0xc

    if-eq v1, v0, :cond_0

    sget-object v12, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const-string v1, "DrXdt"

    const/4 v0, 0x1

    aput-object v1, v12, v0

    int-to-float v0, v14

    cmpl-float v0, v13, v0

    if-lez v0, :cond_7

    .line 8977
    :cond_4
    return-void

    :cond_5
    sget-object v12, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const-string v1, ""

    const/4 v0, 0x4

    aput-object v1, v12, v0

    const-string v1, ""

    const/4 v0, 0x5

    aput-object v1, v12, v0

    invoke-virtual {v14, v11}, Landroid/graphics/drawable/Drawable;->draw(Landroid/graphics/Canvas;)V

    goto :goto_3

    .line 8978
    .end local v11    # "drawAt":F
    .end local v12
    .restart local v6    # "offset":F
    :cond_6
    iget-object v0, v10, Lcom/facebook/ads/redexgen/X/3h;->A01:Lcom/facebook/ads/redexgen/X/3E;

    invoke-virtual {v0, v4}, Lcom/facebook/ads/redexgen/X/3E;->A00(I)F

    move-result v1

    .line 8979
    .local v11, "widthFactor":F
    add-float v13, v2, v1

    int-to-float v0, v8

    mul-float/2addr v13, v0

    .line 8980
    .local v12, "drawAt":F
    add-float/2addr v1, v7

    add-float/2addr v2, v1

    goto :goto_2

    .line 8981
    .end local v11    # "widthFactor":F
    :cond_7
    add-int/lit8 v4, v4, 0x1

    goto/16 :goto_0
.end method

.method public final onInterceptTouchEvent(Landroid/view/MotionEvent;)Z
    .locals 17

    .line 8982
    move-object/from16 v3, p0

    move-object v3, v3

    move-object/from16 v4, p1

    invoke-virtual {v4}, Landroid/view/MotionEvent;->getAction()I

    move-result v0

    and-int/lit16 v2, v0, 0xff

    .line 8983
    .local v8, "action":I
    const/4 v0, 0x3

    const/4 v1, 0x0

    if-eq v2, v0, :cond_0

    const/4 v5, 0x1

    if-ne v2, v5, :cond_2

    .line 8984
    :cond_0
    invoke-direct {v3}, Lcom/facebook/ads/redexgen/X/3h;->A0U()Z

    .line 8985
    const/4 v3, 0x0

    sget-object v2, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const/4 v0, 0x4

    aget-object v1, v2, v0

    const/4 v0, 0x5

    aget-object v0, v2, v0

    invoke-virtual {v1}, Ljava/lang/String;->length()I

    move-result v1

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v0

    if-eq v1, v0, :cond_1

    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    :cond_1
    sget-object v2, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const-string v1, "iW5W"

    const/4 v0, 0x2

    aput-object v1, v2, v0

    return v3

    .line 8986
    :cond_2
    if-eqz v2, :cond_4

    .line 8987
    iget-boolean v0, v3, Lcom/facebook/ads/redexgen/X/3h;->A0i:Z

    if-eqz v0, :cond_3

    .line 8988
    return v5

    .line 8989
    :cond_3
    iget-boolean v0, v3, Lcom/facebook/ads/redexgen/X/3h;->A0k:Z

    if-eqz v0, :cond_4

    .line 8990
    return v1

    .line 8991
    :cond_4
    packed-switch v2, :pswitch_data_0

    .line 8992
    :cond_5
    :goto_0
    :pswitch_0
    iget-object v5, v3, Lcom/facebook/ads/redexgen/X/3h;->A0U:Landroid/view/VelocityTracker;

    sget-object v1, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const/4 v0, 0x2

    aget-object v0, v1, v0

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v1

    const/16 v0, 0xe

    if-eq v1, v0, :cond_e

    sget-object v2, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const-string v1, "qZEBvpK5WM51NnTYZj9Wv9V85vPNkfCF"

    const/4 v0, 0x3

    aput-object v1, v2, v0

    const-string v1, "8DoAZte0LwUXg2aXlHvSBWFBy3vUeqDy"

    const/4 v0, 0x6

    aput-object v1, v2, v0

    if-nez v5, :cond_6

    .line 8993
    invoke-static {}, Landroid/view/VelocityTracker;->obtain()Landroid/view/VelocityTracker;

    move-result-object v0

    iput-object v0, v3, Lcom/facebook/ads/redexgen/X/3h;->A0U:Landroid/view/VelocityTracker;

    .line 8994
    :cond_6
    iget-object v0, v3, Lcom/facebook/ads/redexgen/X/3h;->A0U:Landroid/view/VelocityTracker;

    invoke-virtual {v0, v4}, Landroid/view/VelocityTracker;->addMovement(Landroid/view/MotionEvent;)V

    .line 8995
    iget-boolean v0, v3, Lcom/facebook/ads/redexgen/X/3h;->A0i:Z

    return v0

    .line 8996
    :pswitch_1
    invoke-direct {v3, v4}, Lcom/facebook/ads/redexgen/X/3h;->A0P(Landroid/view/MotionEvent;)V

    goto :goto_0

    .line 8997
    :pswitch_2
    iget v1, v3, Lcom/facebook/ads/redexgen/X/3h;->A08:I

    .line 8998
    .local v11, "activePointerId":I
    const/4 v0, -0x1

    if-ne v1, v0, :cond_7

    goto :goto_0

    .line 8999
    :cond_7
    invoke-virtual {v4, v1}, Landroid/view/MotionEvent;->findPointerIndex(I)I

    move-result v1

    .line 9000
    .local v12, "pointerIndex":I
    invoke-virtual {v4, v1}, Landroid/view/MotionEvent;->getX(I)F

    move-result v6

    .line 9001
    .local v13, "x":F
    iget v0, v3, Lcom/facebook/ads/redexgen/X/3h;->A05:F

    sub-float v2, v6, v0

    .line 9002
    .local v14, "dx":F
    invoke-static {v2}, Ljava/lang/Math;->abs(F)F

    move-result v10

    .line 9003
    .local v15, "xDiff":F
    invoke-virtual {v4, v1}, Landroid/view/MotionEvent;->getY(I)F

    move-result v7

    .line 9004
    .local v5, "y":F
    iget v0, v3, Lcom/facebook/ads/redexgen/X/3h;->A04:F

    sub-float v0, v7, v0

    invoke-static {v0}, Ljava/lang/Math;->abs(F)F

    move-result v9

    .line 9005
    .local v16, "yDiff":F
    const/4 v8, 0x0

    cmpl-float v0, v2, v8

    if-eqz v0, :cond_8

    iget v0, v3, Lcom/facebook/ads/redexgen/X/3h;->A05:F

    invoke-direct {v3, v0, v2}, Lcom/facebook/ads/redexgen/X/3h;->A0Y(FF)Z

    move-result v0

    if-nez v0, :cond_8

    const/4 v13, 0x0

    float-to-int v14, v2

    float-to-int v15, v6

    float-to-int v0, v7

    .line 9006
    move-object v11, v3

    move-object v12, v3

    .end local v5    # "y":F
    .local v9, "y":F
    move/from16 v16, v0

    invoke-direct/range {v11 .. v16}, Lcom/facebook/ads/redexgen/X/3h;->A0d(Landroid/view/View;ZIII)Z

    move-result v0

    if-eqz v0, :cond_8

    .line 9007
    iput v6, v3, Lcom/facebook/ads/redexgen/X/3h;->A05:F

    .line 9008
    iput v7, v3, Lcom/facebook/ads/redexgen/X/3h;->A06:F

    .line 9009
    iput-boolean v5, v3, Lcom/facebook/ads/redexgen/X/3h;->A0k:Z

    .line 9010
    const/4 v0, 0x0

    return v0

    .line 9011
    .end local v5
    .restart local v9    # "y":F
    :cond_8
    iget v1, v3, Lcom/facebook/ads/redexgen/X/3h;->A0R:I

    int-to-float v0, v1

    cmpl-float v0, v10, v0

    if-lez v0, :cond_c

    const/high16 v0, 0x3f000000    # 0.5f

    mul-float/2addr v0, v10

    cmpl-float v0, v0, v9

    if-lez v0, :cond_c

    .line 9012
    iput-boolean v5, v3, Lcom/facebook/ads/redexgen/X/3h;->A0i:Z

    .line 9013
    invoke-direct {v3, v5}, Lcom/facebook/ads/redexgen/X/3h;->A0T(Z)V

    .line 9014
    invoke-virtual {v3, v5}, Lcom/facebook/ads/redexgen/X/3h;->setScrollState(I)V

    .line 9015
    cmpl-float v0, v2, v8

    if-lez v0, :cond_b

    .line 9016
    iget v8, v3, Lcom/facebook/ads/redexgen/X/3h;->A03:F

    iget v9, v3, Lcom/facebook/ads/redexgen/X/3h;->A0R:I

    sget-object v2, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const/4 v0, 0x7

    aget-object v1, v2, v0

    const/4 v0, 0x0

    aget-object v2, v2, v0

    const/16 v0, 0xa

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    invoke-virtual {v2, v0}, Ljava/lang/String;->charAt(I)C

    move-result v0

    if-eq v1, v0, :cond_a

    int-to-float v0, v9

    add-float/2addr v8, v0

    :goto_1
    iput v8, v3, Lcom/facebook/ads/redexgen/X/3h;->A05:F

    .line 9017
    iput v7, v3, Lcom/facebook/ads/redexgen/X/3h;->A06:F

    .line 9018
    invoke-direct {v3, v5}, Lcom/facebook/ads/redexgen/X/3h;->setScrollingCacheEnabled(Z)V

    .line 9019
    :cond_9
    :goto_2
    iget-boolean v0, v3, Lcom/facebook/ads/redexgen/X/3h;->A0i:Z

    if-eqz v0, :cond_5

    .line 9020
    invoke-direct {v3, v6}, Lcom/facebook/ads/redexgen/X/3h;->A0X(F)Z

    move-result v0

    if-eqz v0, :cond_5

    .line 9021
    invoke-static {v3}, Lcom/facebook/ads/redexgen/X/3T;->A07(Landroid/view/View;)V

    goto/16 :goto_0

    :cond_a
    sget-object v2, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const-string v1, "WpQimEPBWZ8vG05HKhTxshBMY3NNEur0"

    const/4 v0, 0x7

    aput-object v1, v2, v0

    const-string v1, "BEtp4ufX6D8x3OZlDxdpMoFh1oKQqP0t"

    const/4 v0, 0x0

    aput-object v1, v2, v0

    int-to-float v0, v9

    add-float/2addr v8, v0

    goto :goto_1

    .line 9022
    :cond_b
    iget v8, v3, Lcom/facebook/ads/redexgen/X/3h;->A03:F

    iget v0, v3, Lcom/facebook/ads/redexgen/X/3h;->A0R:I

    int-to-float v0, v0

    sub-float/2addr v8, v0

    goto :goto_1

    .line 9023
    :cond_c
    int-to-float v0, v1

    cmpl-float v0, v9, v0

    if-lez v0, :cond_9

    .line 9024
    iput-boolean v5, v3, Lcom/facebook/ads/redexgen/X/3h;->A0k:Z

    goto :goto_2

    .line 9025
    .end local v9    # "y":F
    .end local v11    # "activePointerId":I
    .end local v12    # "pointerIndex":I
    .end local v13    # "x":F
    .end local v14    # "dx":F
    .end local v15    # "xDiff":F
    .end local v16    # "yDiff":F
    :pswitch_3
    invoke-virtual {v4}, Landroid/view/MotionEvent;->getX()F

    move-result v0

    iput v0, v3, Lcom/facebook/ads/redexgen/X/3h;->A03:F

    iput v0, v3, Lcom/facebook/ads/redexgen/X/3h;->A05:F

    .line 9026
    invoke-virtual {v4}, Landroid/view/MotionEvent;->getY()F

    move-result v0

    iput v0, v3, Lcom/facebook/ads/redexgen/X/3h;->A04:F

    iput v0, v3, Lcom/facebook/ads/redexgen/X/3h;->A06:F

    .line 9027
    const/4 v1, 0x0

    invoke-virtual {v4, v1}, Landroid/view/MotionEvent;->getPointerId(I)I

    move-result v0

    iput v0, v3, Lcom/facebook/ads/redexgen/X/3h;->A08:I

    .line 9028
    iput-boolean v1, v3, Lcom/facebook/ads/redexgen/X/3h;->A0k:Z

    .line 9029
    iput-boolean v5, v3, Lcom/facebook/ads/redexgen/X/3h;->A0j:Z

    .line 9030
    iget-object v0, v3, Lcom/facebook/ads/redexgen/X/3h;->A0X:Landroid/widget/Scroller;

    invoke-virtual {v0}, Landroid/widget/Scroller;->computeScrollOffset()Z

    .line 9031
    iget v1, v3, Lcom/facebook/ads/redexgen/X/3h;->A0P:I

    const/4 v0, 0x2

    if-ne v1, v0, :cond_d

    iget-object v0, v3, Lcom/facebook/ads/redexgen/X/3h;->A0X:Landroid/widget/Scroller;

    .line 9032
    invoke-virtual {v0}, Landroid/widget/Scroller;->getFinalX()I

    move-result v1

    iget-object v0, v3, Lcom/facebook/ads/redexgen/X/3h;->A0X:Landroid/widget/Scroller;

    invoke-virtual {v0}, Landroid/widget/Scroller;->getCurrX()I

    move-result v0

    sub-int/2addr v1, v0

    invoke-static {v1}, Ljava/lang/Math;->abs(I)I

    move-result v1

    iget v0, v3, Lcom/facebook/ads/redexgen/X/3h;->A0C:I

    if-le v1, v0, :cond_d

    .line 9033
    iget-object v0, v3, Lcom/facebook/ads/redexgen/X/3h;->A0X:Landroid/widget/Scroller;

    invoke-virtual {v0}, Landroid/widget/Scroller;->abortAnimation()V

    .line 9034
    const/4 v0, 0x0

    iput-boolean v0, v3, Lcom/facebook/ads/redexgen/X/3h;->A0m:Z

    .line 9035
    invoke-virtual {v3}, Lcom/facebook/ads/redexgen/X/3h;->A0f()V

    .line 9036
    iput-boolean v5, v3, Lcom/facebook/ads/redexgen/X/3h;->A0i:Z

    .line 9037
    invoke-direct {v3, v5}, Lcom/facebook/ads/redexgen/X/3h;->A0T(Z)V

    .line 9038
    invoke-virtual {v3, v5}, Lcom/facebook/ads/redexgen/X/3h;->setScrollState(I)V

    goto/16 :goto_0

    .line 9039
    :cond_d
    const/4 v0, 0x0

    invoke-direct {v3, v0}, Lcom/facebook/ads/redexgen/X/3h;->A0R(Z)V

    .line 9040
    iput-boolean v0, v3, Lcom/facebook/ads/redexgen/X/3h;->A0i:Z

    .line 9041
    goto/16 :goto_0

    .line 9042
    :cond_e
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    :pswitch_data_0
    .packed-switch 0x0
        :pswitch_3
        :pswitch_0
        :pswitch_2
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_1
    .end packed-switch
.end method

.method public final onLayout(ZIIII)V
    .locals 18

    .line 9043
    move/from16 v5, p5

    move/from16 v6, p4

    move-object/from16 v3, p0

    invoke-virtual/range {p0 .. p0}, Lcom/facebook/ads/redexgen/X/3h;->getChildCount()I

    move-result v4

    .line 9044
    .local v1, "count":I
    sub-int v6, v6, p2

    .line 9045
    .local v2, "width":I
    sub-int v5, v5, p3

    .line 9046
    .local v3, "height":I
    invoke-virtual/range {p0 .. p0}, Lcom/facebook/ads/redexgen/X/3h;->getPaddingLeft()I

    move-result v8

    .line 9047
    .local v4, "paddingLeft":I
    invoke-virtual/range {p0 .. p0}, Lcom/facebook/ads/redexgen/X/3h;->getPaddingTop()I

    move-result v9

    .line 9048
    .local v5, "paddingTop":I
    invoke-virtual/range {p0 .. p0}, Lcom/facebook/ads/redexgen/X/3h;->getPaddingRight()I

    move-result v17

    .line 9049
    .local v6, "paddingRight":I
    invoke-virtual/range {p0 .. p0}, Lcom/facebook/ads/redexgen/X/3h;->getPaddingBottom()I

    move-result v16

    .line 9050
    .local v7, "paddingBottom":I
    invoke-virtual/range {p0 .. p0}, Lcom/facebook/ads/redexgen/X/3h;->getScrollX()I

    move-result v15

    .line 9051
    .local v8, "scrollX":I
    const/4 v7, 0x0

    .line 9052
    .local v9, "decorCount":I
    const/4 v10, 0x0

    .local v10, "i":I
    :goto_0
    const/16 v13, 0x8

    sget-object v2, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const/4 v0, 0x4

    aget-object v1, v2, v0

    const/4 v0, 0x5

    aget-object v0, v2, v0

    invoke-virtual {v1}, Ljava/lang/String;->length()I

    move-result v1

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v0

    if-eq v1, v0, :cond_0

    :goto_1
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    :cond_0
    sget-object v2, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const-string v1, ""

    const/4 v0, 0x1

    aput-object v1, v2, v0

    if-ge v10, v4, :cond_3

    .line 9053
    invoke-virtual {v3, v10}, Lcom/facebook/ads/redexgen/X/3h;->getChildAt(I)Landroid/view/View;

    move-result-object v11

    .line 9054
    .local v12, "child":Landroid/view/View;
    invoke-virtual {v11}, Landroid/view/View;->getVisibility()I

    move-result v0

    if-eq v0, v13, :cond_1

    .line 9055
    invoke-virtual {v11}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    move-result-object v2

    check-cast v2, Lcom/facebook/ads/redexgen/X/3a;

    .line 9056
    .local v11, "lp":Lcom/facebook/ads/redexgen/X/3a;
    .local v13, "childLeft":I
    .local v14, "childTop":I
    iget-boolean v0, v2, Lcom/facebook/ads/redexgen/X/3a;->A05:Z

    if-eqz v0, :cond_1

    .line 9057
    iget v0, v2, Lcom/facebook/ads/redexgen/X/3a;->A04:I

    and-int/lit8 v1, v0, 0x7

    .line 9058
    .local v15, "hgrav":I
    .end local v13    # "childLeft":I
    .local v16, "childLeft":I
    iget v0, v2, Lcom/facebook/ads/redexgen/X/3a;->A04:I

    and-int/lit8 v14, v0, 0x70

    .line 9059
    .local v13, "vgrav":I
    packed-switch v1, :pswitch_data_0

    .line 9060
    .end local v11    # "lp":Lcom/facebook/ads/redexgen/X/3a;
    .local p0, "lp":Lcom/facebook/ads/redexgen/X/3a;
    :pswitch_0
    move v12, v8

    .line 9061
    .end local v16    # "childLeft":I
    .local v17, "childLeft":I
    :goto_2
    sparse-switch v14, :sswitch_data_0

    .line 9062
    move v1, v9

    .line 9063
    .end local v14    # "childTop":I
    .local v11, "childTop":I
    :goto_3
    add-int/2addr v12, v15

    .line 9064
    .end local v17    # "childLeft":I
    .local v14, "childLeft":I
    invoke-virtual {v11}, Landroid/view/View;->getMeasuredWidth()I

    move-result v0

    .end local v4    # "paddingLeft":I
    .local v17, "paddingLeft":I
    add-int v2, v12, v0

    .line 9065
    invoke-virtual {v11}, Landroid/view/View;->getMeasuredHeight()I

    move-result v0

    .end local v5    # "paddingTop":I
    .local p1, "paddingTop":I
    add-int/2addr v0, v1

    .line 9066
    invoke-virtual {v11, v12, v1, v2, v0}, Landroid/view/View;->layout(IIII)V

    .line 9067
    add-int/lit8 v7, v7, 0x1

    .line 9068
    .end local v11    # "childTop":I
    .end local v12    # "child":Landroid/view/View;
    .end local v13    # "vgrav":I
    .end local v14    # "childLeft":I
    :cond_1
    add-int/lit8 v10, v10, 0x1

    goto :goto_0

    .line 9069
    .end local v11
    .restart local v14    # "childLeft":I
    :sswitch_0
    sub-int v1, v5, v16

    invoke-virtual {v11}, Landroid/view/View;->getMeasuredHeight()I

    move-result v0

    sub-int/2addr v1, v0

    .line 9070
    .end local v14    # "childLeft":I
    .restart local v11    # "childTop":I
    invoke-virtual {v11}, Landroid/view/View;->getMeasuredHeight()I

    move-result v0

    add-int v16, v16, v0

    goto :goto_3

    .line 9071
    .end local v11    # "childTop":I
    .restart local v14    # "childLeft":I
    :sswitch_1
    move v1, v9

    .line 9072
    .end local v14    # "childLeft":I
    .restart local v11    # "childTop":I
    invoke-virtual {v11}, Landroid/view/View;->getMeasuredHeight()I

    move-result v0

    add-int/2addr v9, v0

    .line 9073
    goto :goto_3

    .line 9074
    .end local v11    # "childTop":I
    .restart local v14    # "childLeft":I
    :sswitch_2
    invoke-virtual {v11}, Landroid/view/View;->getMeasuredHeight()I

    move-result v0

    sub-int v0, v5, v0

    div-int/lit8 v0, v0, 0x2

    invoke-static {v0, v9}, Ljava/lang/Math;->max(II)I

    move-result v1

    .line 9075
    .end local v14    # "childLeft":I
    .restart local v11    # "childTop":I
    goto :goto_3

    .line 9076
    .end local v17    # "paddingLeft":I
    .end local p0    # "lp":Lcom/facebook/ads/redexgen/X/3a;
    .restart local v11    # "childTop":I
    .restart local v16    # "childLeft":I
    :pswitch_1
    sub-int v12, v6, v17

    invoke-virtual {v11}, Landroid/view/View;->getMeasuredWidth()I

    move-result v13

    sget-object v2, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const/4 v0, 0x7

    aget-object v1, v2, v0

    const/4 v0, 0x0

    aget-object v2, v2, v0

    const/16 v0, 0xa

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    invoke-virtual {v2, v0}, Ljava/lang/String;->charAt(I)C

    move-result v0

    if-eq v1, v0, :cond_2

    goto/16 :goto_1

    :cond_2
    sget-object v2, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const-string v1, "51Hkg"

    const/4 v0, 0x1

    aput-object v1, v2, v0

    sub-int/2addr v12, v13

    .line 9077
    .end local v16    # "childLeft":I
    .restart local v17    # "paddingLeft":I
    invoke-virtual {v11}, Landroid/view/View;->getMeasuredWidth()I

    move-result v0

    add-int v17, v17, v0

    goto :goto_2

    .line 9078
    .end local v17    # "paddingLeft":I
    .restart local v16    # "childLeft":I
    :pswitch_2
    move v12, v8

    .line 9079
    .end local v16    # "childLeft":I
    .restart local v17    # "paddingLeft":I
    invoke-virtual {v11}, Landroid/view/View;->getMeasuredWidth()I

    move-result v0

    add-int/2addr v8, v0

    .line 9080
    goto :goto_2

    .line 9081
    .end local v17    # "paddingLeft":I
    .restart local v16    # "childLeft":I
    :pswitch_3
    invoke-virtual {v11}, Landroid/view/View;->getMeasuredWidth()I

    move-result v0

    sub-int v0, v6, v0

    .end local v11    # "childTop":I
    .restart local p0    # "lp":Lcom/facebook/ads/redexgen/X/3a;
    div-int/lit8 v0, v0, 0x2

    invoke-static {v0, v8}, Ljava/lang/Math;->max(II)I

    move-result v12

    .line 9082
    .end local v16    # "childLeft":I
    .restart local v17    # "paddingLeft":I
    goto :goto_2

    .line 9083
    .end local v10    # "i":I
    :cond_3
    sub-int/2addr v6, v8

    sub-int v6, v6, v17

    .line 9084
    .local v10, "childWidth":I
    const/4 v12, 0x0

    .local v12, "i":I
    :goto_4
    if-ge v12, v4, :cond_6

    .line 9085
    invoke-virtual {v3, v12}, Lcom/facebook/ads/redexgen/X/3h;->getChildAt(I)Landroid/view/View;

    move-result-object v11

    .line 9086
    .local v14, "child":Landroid/view/View;
    invoke-virtual {v11}, Landroid/view/View;->getVisibility()I

    move-result v0

    if-eq v0, v13, :cond_5

    .line 9087
    invoke-virtual {v11}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    move-result-object v2

    check-cast v2, Lcom/facebook/ads/redexgen/X/3a;

    .line 9088
    .local v15, "lp":Lcom/facebook/ads/redexgen/X/3a;
    iget-boolean v0, v2, Lcom/facebook/ads/redexgen/X/3a;->A05:Z

    if-nez v0, :cond_5

    invoke-direct {v3, v11}, Lcom/facebook/ads/redexgen/X/3h;->A07(Landroid/view/View;)Lcom/facebook/ads/redexgen/X/3Z;

    move-result-object v0

    .local v17, "ii":Lcom/facebook/ads/redexgen/X/3Z;
    if-eqz v0, :cond_5

    .line 9089
    int-to-float v1, v6

    .end local v1    # "count":I
    .local v13, "ii":Lcom/facebook/ads/redexgen/X/3Z;
    .local v17, "count":I
    iget v0, v0, Lcom/facebook/ads/redexgen/X/3Z;->A00:F

    mul-float/2addr v1, v0

    float-to-int v0, v1

    .line 9090
    .local v1, "loff":I
    add-int v10, v8, v0

    .line 9091
    .local v11, "childLeft":I
    .local p1, "childTop":I
    .end local v1    # "loff":I
    .local p2, "loff":I
    iget-boolean v0, v2, Lcom/facebook/ads/redexgen/X/3a;->A03:Z

    if-eqz v0, :cond_4

    .line 9092
    const/4 v0, 0x0

    iput-boolean v0, v2, Lcom/facebook/ads/redexgen/X/3a;->A03:Z

    .line 9093
    int-to-float v1, v6

    .end local v2    # "width":I
    .local p3, "width":I
    iget v0, v2, Lcom/facebook/ads/redexgen/X/3a;->A00:F

    mul-float/2addr v1, v0

    float-to-int v0, v1

    const/high16 v2, 0x40000000    # 2.0f

    invoke-static {v0, v2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    move-result v1

    .line 9094
    .local v1, "widthSpec":I
    sub-int v0, v5, v9

    .end local v4
    .local p4, "paddingLeft":I
    sub-int v0, v0, v16

    invoke-static {v0, v2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    move-result v0

    .line 9095
    .local v2, "heightSpec":I
    invoke-virtual {v11, v1, v0}, Landroid/view/View;->measure(II)V

    .line 9096
    .end local v2    # "heightSpec":I
    .end local v4
    .restart local p3    # "width":I
    .restart local p4    # "paddingLeft":I
    :cond_4
    invoke-virtual {v11}, Landroid/view/View;->getMeasuredWidth()I

    move-result v2

    add-int/2addr v2, v10

    .line 9097
    invoke-virtual {v11}, Landroid/view/View;->getMeasuredHeight()I

    move-result v1

    move v0, v9

    .end local p1    # "childTop":I
    .local v4, "childTop":I
    add-int/2addr v1, v0

    .line 9098
    invoke-virtual {v11, v10, v0, v2, v1}, Landroid/view/View;->layout(IIII)V

    .line 9099
    .end local v1    # "widthSpec":I
    .end local v2
    .end local v4    # "childTop":I
    .end local v14    # "child":Landroid/view/View;
    .restart local v17    # "count":I
    .restart local p3    # "width":I
    .restart local p4    # "paddingLeft":I
    :cond_5
    add-int/lit8 v12, v12, 0x1

    const/16 v13, 0x8

    goto :goto_4

    .line 9100
    .end local v17    # "count":I
    .end local p3    # "width":I
    .end local p4    # "paddingLeft":I
    .restart local v1    # "widthSpec":I
    .restart local v2    # "heightSpec":I
    .restart local v4    # "childTop":I
    .end local v1    # "widthSpec":I
    .end local v2    # "heightSpec":I
    .end local v4    # "childTop":I
    .end local v12    # "i":I
    .restart local v17    # "count":I
    .restart local p3    # "width":I
    .restart local p4    # "paddingLeft":I
    :cond_6
    iput v9, v3, Lcom/facebook/ads/redexgen/X/3h;->A0Q:I

    .line 9101
    sub-int v5, v5, v16

    iput v5, v3, Lcom/facebook/ads/redexgen/X/3h;->A09:I

    .line 9102
    iput v7, v3, Lcom/facebook/ads/redexgen/X/3h;->A0D:I

    .line 9103
    iget-boolean v0, v3, Lcom/facebook/ads/redexgen/X/3h;->A0g:Z

    if-eqz v0, :cond_7

    .line 9104
    iget v5, v3, Lcom/facebook/ads/redexgen/X/3h;->A00:I

    const/4 v4, 0x0

    sget-object v2, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const/4 v0, 0x4

    aget-object v1, v2, v0

    const/4 v0, 0x5

    aget-object v0, v2, v0

    invoke-virtual {v1}, Ljava/lang/String;->length()I

    move-result v1

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v0

    if-eq v1, v0, :cond_8

    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    .line 9105
    :cond_7
    const/4 v4, 0x0

    goto :goto_5

    .line 9106
    :cond_8
    sget-object v2, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const-string v1, ""

    const/4 v0, 0x4

    aput-object v1, v2, v0

    const-string v1, ""

    const/4 v0, 0x5

    aput-object v1, v2, v0

    invoke-direct {v3, v5, v4, v4, v4}, Lcom/facebook/ads/redexgen/X/3h;->A0M(IZIZ)V

    .line 9107
    :goto_5
    iput-boolean v4, v3, Lcom/facebook/ads/redexgen/X/3h;->A0g:Z

    .line 9108
    return-void

    :pswitch_data_0
    .packed-switch 0x1
        :pswitch_3
        :pswitch_0
        :pswitch_2
        :pswitch_0
        :pswitch_1
    .end packed-switch

    :sswitch_data_0
    .sparse-switch
        0x10 -> :sswitch_2
        0x30 -> :sswitch_1
        0x50 -> :sswitch_0
    .end sparse-switch
.end method

.method public onMeasure(II)V
    .locals 15

    .line 9109
    move-object v4, p0

    const/4 v0, 0x0

    move/from16 v1, p1

    invoke-static {v0, v1}, Lcom/facebook/ads/redexgen/X/3h;->getDefaultSize(II)I

    move-result v1

    .line 9110
    move/from16 v2, p2

    invoke-static {v0, v2}, Lcom/facebook/ads/redexgen/X/3h;->getDefaultSize(II)I

    move-result v0

    .line 9111
    invoke-virtual {v4, v1, v0}, Lcom/facebook/ads/redexgen/X/3h;->setMeasuredDimension(II)V

    .line 9112
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/3h;->getMeasuredWidth()I

    move-result v3

    .line 9113
    .local v3, "measuredWidth":I
    div-int/lit8 v1, v3, 0xa

    .line 9114
    .local v5, "maxGutterSize":I
    iget v0, v4, Lcom/facebook/ads/redexgen/X/3h;->A0E:I

    invoke-static {v1, v0}, Ljava/lang/Math;->min(II)I

    move-result v0

    iput v0, v4, Lcom/facebook/ads/redexgen/X/3h;->A0I:I

    .line 9115
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/3h;->getPaddingLeft()I

    move-result v0

    sub-int/2addr v3, v0

    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/3h;->getPaddingRight()I

    move-result v0

    sub-int/2addr v3, v0

    .line 9116
    .local v6, "childWidthSize":I
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/3h;->getMeasuredHeight()I

    move-result v5

    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/3h;->getPaddingTop()I

    move-result v0

    sub-int/2addr v5, v0

    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/3h;->getPaddingBottom()I

    move-result v0

    sub-int/2addr v5, v0

    .line 9117
    .local v7, "childHeightSize":I
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/3h;->getChildCount()I

    move-result v7

    .line 9118
    .local v8, "size":I
    const/4 v6, 0x0

    .local v9, "i":I
    :goto_0
    const/16 v9, 0x8

    if-ge v6, v7, :cond_d

    .line 9119
    invoke-virtual {v4, v6}, Lcom/facebook/ads/redexgen/X/3h;->getChildAt(I)Landroid/view/View;

    move-result-object v8

    .line 9120
    .local v12, "child":Landroid/view/View;
    invoke-virtual {v8}, Landroid/view/View;->getVisibility()I

    move-result v0

    if-eq v0, v9, :cond_4

    .line 9121
    invoke-virtual {v8}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    move-result-object v9

    check-cast v9, Lcom/facebook/ads/redexgen/X/3a;

    sget-object v1, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const/4 v0, 0x1

    aget-object v0, v1, v0

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v1

    const/16 v0, 0xc

    if-eq v1, v0, :cond_f

    .line 9122
    .local v10, "lp":Lcom/facebook/ads/redexgen/X/3a;
    sget-object v2, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const-string v1, ""

    const/4 v0, 0x4

    aput-object v1, v2, v0

    const-string v1, ""

    const/4 v0, 0x5

    aput-object v1, v2, v0

    if-eqz v9, :cond_4

    iget-boolean v0, v9, Lcom/facebook/ads/redexgen/X/3a;->A05:Z

    if-eqz v0, :cond_4

    .line 9123
    iget v0, v9, Lcom/facebook/ads/redexgen/X/3a;->A04:I

    and-int/lit8 v2, v0, 0x7

    .line 9124
    .local v13, "hgrav":I
    iget v0, v9, Lcom/facebook/ads/redexgen/X/3a;->A04:I

    and-int/lit8 v1, v0, 0x70

    .line 9125
    .local v14, "vgrav":I
    const/high16 v12, -0x80000000

    .line 9126
    .local p0, "widthMode":I
    const/high16 v10, -0x80000000

    .line 9127
    .local p1, "heightMode":I
    const/16 v0, 0x30

    if-eq v1, v0, :cond_0

    const/16 v0, 0x50

    if-ne v1, v0, :cond_c

    :cond_0
    const/4 v14, 0x1

    .line 9128
    .local v1, "consumeVertical":Z
    :goto_1
    const/4 v0, 0x3

    if-eq v2, v0, :cond_1

    const/4 v0, 0x5

    if-ne v2, v0, :cond_b

    :cond_1
    const/4 v13, 0x1

    .line 9129
    .local v11, "consumeHorizontal":Z
    :goto_2
    if-eqz v14, :cond_a

    .line 9130
    const/high16 v12, 0x40000000    # 2.0f

    .line 9131
    .local p2, "widthSize":I
    .local p3, "heightSize":I
    :cond_2
    :goto_3
    iget v1, v9, Lcom/facebook/ads/redexgen/X/3a;->width:I

    .end local v3    # "measuredWidth":I
    .local p4, "measuredWidth":I
    const/4 v0, -0x2

    if-eq v1, v0, :cond_9

    .line 9132
    const/high16 v12, 0x40000000    # 2.0f

    .line 9133
    iget v1, v9, Lcom/facebook/ads/redexgen/X/3a;->width:I

    const/4 v0, -0x1

    if-eq v1, v0, :cond_8

    .line 9134
    iget v11, v9, Lcom/facebook/ads/redexgen/X/3a;->width:I

    .line 9135
    .end local p2    # "widthSize":I
    .local v2, "widthSize":I
    .end local p2
    .restart local v2    # "widthSize":I
    :goto_4
    iget v1, v9, Lcom/facebook/ads/redexgen/X/3a;->height:I

    const/4 v0, -0x2

    if-eq v1, v0, :cond_7

    .line 9136
    const/high16 v10, 0x40000000    # 2.0f

    .line 9137
    iget v1, v9, Lcom/facebook/ads/redexgen/X/3a;->height:I

    const/4 v0, -0x1

    if-eq v1, v0, :cond_6

    .line 9138
    iget v9, v9, Lcom/facebook/ads/redexgen/X/3a;->height:I

    sget-object v1, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const/4 v0, 0x1

    aget-object v0, v1, v0

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v1

    const/16 v0, 0xc

    if-eq v1, v0, :cond_3

    sget-object v2, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const-string v1, "VKsSzAx6pk9J5wmYrW5ACp"

    const/4 v0, 0x1

    aput-object v1, v2, v0

    .line 9139
    .end local p3
    .local v3, "heightSize":I
    .end local p1    # "heightMode":I
    .end local p3
    .local v3, "heightMode":I
    .local v4, "heightSize":I
    .end local v5    # "maxGutterSize":I
    .local p1, "maxGutterSize":I
    :cond_3
    :goto_5
    invoke-static {v11, v12}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    move-result v1

    .line 9140
    .local v5, "widthSpec":I
    .end local v2    # "widthSize":I
    .restart local p2    # "widthSize":I
    invoke-static {v9, v10}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    move-result v0

    .line 9141
    .local v2, "heightSpec":I
    invoke-virtual {v8, v1, v0}, Landroid/view/View;->measure(II)V

    .line 9142
    if-eqz v14, :cond_5

    .line 9143
    invoke-virtual {v8}, Landroid/view/View;->getMeasuredHeight()I

    move-result v0

    sub-int/2addr v5, v0

    .line 9144
    .end local v3    # "heightMode":I
    .end local v5    # "widthSpec":I
    .end local v12    # "child":Landroid/view/View;
    .restart local p1    # "maxGutterSize":I
    .restart local p4
    :cond_4
    :goto_6
    add-int/lit8 v6, v6, 0x1

    goto/16 :goto_0

    .line 9145
    :cond_5
    if-eqz v13, :cond_4

    .line 9146
    invoke-virtual {v8}, Landroid/view/View;->getMeasuredWidth()I

    move-result v0

    sub-int/2addr v3, v0

    goto :goto_6

    .line 9147
    .end local v3
    .restart local p3
    :cond_6
    move v9, v5

    goto :goto_5

    .line 9148
    :cond_7
    move v9, v5

    goto :goto_5

    .line 9149
    .end local v2    # "heightSpec":I
    .restart local p2    # "widthSize":I
    :cond_8
    move v11, v3

    goto :goto_4

    .line 9150
    :cond_9
    move v11, v3

    goto :goto_4

    .line 9151
    :cond_a
    if-eqz v13, :cond_2

    .line 9152
    const/high16 v10, 0x40000000    # 2.0f

    goto :goto_3

    .line 9153
    :cond_b
    const/4 v13, 0x0

    goto :goto_2

    .line 9154
    :cond_c
    const/4 v14, 0x0

    goto :goto_1

    .line 9155
    .end local p1    # "maxGutterSize":I
    .end local p4
    .restart local v3    # "heightMode":I
    .restart local v5    # "widthSpec":I
    .end local v3    # "heightMode":I
    .end local v5    # "widthSpec":I
    .end local v9    # "i":I
    .restart local p1    # "maxGutterSize":I
    .restart local p4
    :cond_d
    const/high16 v8, 0x40000000    # 2.0f

    invoke-static {v3, v8}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    move-result v0

    iput v0, v4, Lcom/facebook/ads/redexgen/X/3h;->A0B:I

    .line 9156
    invoke-static {v5, v8}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    move-result v0

    iput v0, v4, Lcom/facebook/ads/redexgen/X/3h;->A0A:I

    .line 9157
    const/4 v0, 0x1

    iput-boolean v0, v4, Lcom/facebook/ads/redexgen/X/3h;->A0h:Z

    .line 9158
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/3h;->A0f()V

    .line 9159
    const/4 v0, 0x0

    iput-boolean v0, v4, Lcom/facebook/ads/redexgen/X/3h;->A0h:Z

    .line 9160
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/3h;->getChildCount()I

    move-result v7

    .line 9161
    .end local v8    # "size":I
    .local v2, "size":I
    const/4 v6, 0x0

    .local v3, "i":I
    :goto_7
    if-ge v6, v7, :cond_12

    .line 9162
    invoke-virtual {v4, v6}, Lcom/facebook/ads/redexgen/X/3h;->getChildAt(I)Landroid/view/View;

    move-result-object v5

    .line 9163
    .local v4, "child":Landroid/view/View;
    invoke-virtual {v5}, Landroid/view/View;->getVisibility()I

    move-result v0

    if-eq v0, v9, :cond_11

    .line 9164
    invoke-virtual {v5}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    move-result-object v11

    check-cast v11, Lcom/facebook/ads/redexgen/X/3a;

    .line 9165
    .local v5, "lp":Lcom/facebook/ads/redexgen/X/3a;
    if-eqz v11, :cond_e

    iget-boolean v0, v11, Lcom/facebook/ads/redexgen/X/3a;->A05:Z

    if-nez v0, :cond_11

    .line 9166
    :cond_e
    int-to-float v10, v3

    sget-object v2, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const/4 v0, 0x4

    aget-object v1, v2, v0

    const/4 v0, 0x5

    aget-object v0, v2, v0

    invoke-virtual {v1}, Ljava/lang/String;->length()I

    move-result v1

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v0

    if-eq v1, v0, :cond_10

    :cond_f
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    :cond_10
    sget-object v2, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const-string v1, "11cm9STM7FlxGR3"

    const/4 v0, 0x1

    aput-object v1, v2, v0

    iget v0, v11, Lcom/facebook/ads/redexgen/X/3a;->A00:F

    mul-float/2addr v10, v0

    float-to-int v0, v10

    invoke-static {v0, v8}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    move-result v1

    .line 9167
    .local v8, "widthSpec":I
    iget v0, v4, Lcom/facebook/ads/redexgen/X/3h;->A0A:I

    invoke-virtual {v5, v1, v0}, Landroid/view/View;->measure(II)V

    .line 9168
    .end local v4    # "child":Landroid/view/View;
    .end local v5    # "lp":Lcom/facebook/ads/redexgen/X/3a;
    .end local v8    # "widthSpec":I
    :cond_11
    add-int/lit8 v6, v6, 0x1

    goto :goto_7

    .line 9169
    .end local v3    # "i":I
    :cond_12
    return-void
.end method

.method public final onRequestFocusInDescendants(ILandroid/graphics/Rect;)Z
    .locals 7

    .line 9170
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/3h;->getChildCount()I

    move-result v4

    .line 9171
    .local v0, "count":I
    and-int/lit8 v0, p1, 0x2

    if-eqz v0, :cond_1

    .line 9172
    const/4 v3, 0x0

    .line 9173
    .local v1, "index":I
    const/4 v6, 0x1

    .line 9174
    .local v2, "increment":I
    .local v3, "end":I
    .local v4, "i":I
    :goto_0
    if-eq v3, v4, :cond_3

    .line 9175
    invoke-virtual {p0, v3}, Lcom/facebook/ads/redexgen/X/3h;->getChildAt(I)Landroid/view/View;

    move-result-object v5

    sget-object v1, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const/4 v0, 0x2

    aget-object v0, v1, v0

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v1

    const/16 v0, 0xe

    if-eq v1, v0, :cond_2

    .line 9176
    .local v5, "child":Landroid/view/View;
    sget-object v2, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const-string v1, "wvs0VrXDMEHK1ZrgMliCmQkmYIqFRPYZ"

    const/4 v0, 0x3

    aput-object v1, v2, v0

    const-string v1, "yeuyy9yC0QSIfk9tyj8D1No5khczL5F7"

    const/4 v0, 0x6

    aput-object v1, v2, v0

    invoke-virtual {v5}, Landroid/view/View;->getVisibility()I

    move-result v0

    if-nez v0, :cond_0

    .line 9177
    invoke-direct {p0, v5}, Lcom/facebook/ads/redexgen/X/3h;->A07(Landroid/view/View;)Lcom/facebook/ads/redexgen/X/3Z;

    move-result-object v0

    .line 9178
    .local v6, "ii":Lcom/facebook/ads/redexgen/X/3Z;
    if-eqz v0, :cond_0

    iget v1, v0, Lcom/facebook/ads/redexgen/X/3Z;->A02:I

    iget v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A00:I

    if-ne v1, v0, :cond_0

    .line 9179
    invoke-virtual {v5, p1, p2}, Landroid/view/View;->requestFocus(ILandroid/graphics/Rect;)Z

    move-result v0

    if-eqz v0, :cond_0

    .line 9180
    const/4 v0, 0x1

    return v0

    .line 9181
    .end local v5    # "child":Landroid/view/View;
    .end local v6    # "ii":Lcom/facebook/ads/redexgen/X/3Z;
    :cond_0
    add-int/2addr v3, v6

    goto :goto_0

    .line 9182
    .end local v1    # "index":I
    .end local v2    # "increment":I
    .end local v3    # "end":I
    :cond_1
    add-int/lit8 v3, v4, -0x1

    .line 9183
    .restart local v1    # "index":I
    const/4 v6, -0x1

    .line 9184
    .restart local v2    # "increment":I
    const/4 v4, -0x1

    goto :goto_0

    :cond_2
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    .line 9185
    .end local v4    # "i":I
    :cond_3
    const/4 v0, 0x0

    return v0
.end method

.method public final onRestoreInstanceState(Landroid/os/Parcelable;)V
    .locals 4

    .line 9186
    instance-of v0, p1, Lcom/facebook/ads/internal/util/parcelable/WrappedParcelable;

    if-nez v0, :cond_0

    .line 9187
    invoke-super {p0, p1}, Landroid/view/ViewGroup;->onRestoreInstanceState(Landroid/os/Parcelable;)V

    .line 9188
    return-void

    .line 9189
    :cond_0
    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/Class;->getClassLoader()Ljava/lang/ClassLoader;

    move-result-object v0

    .line 9190
    .local v0, "classLoader":Ljava/lang/ClassLoader;
    if-nez v0, :cond_1

    .line 9191
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/3h;->getContext()Landroid/content/Context;

    move-result-object v3

    sget-object v2, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const/4 v0, 0x3

    aget-object v1, v2, v0

    const/4 v0, 0x6

    aget-object v2, v2, v0

    const/16 v0, 0xf

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    invoke-virtual {v2, v0}, Ljava/lang/String;->charAt(I)C

    move-result v0

    if-eq v1, v0, :cond_4

    sget-object v2, Lcom/facebook/ads/redexgen/X/3h;->A0t:[Ljava/lang/String;

    const-string v1, "Ly8LvMv3iU8o2fiJAVqvd6K7Aqz3gZKY"

    const/4 v0, 0x7

    aput-object v1, v2, v0

    const-string v1, "HcNmsPXina8Ul250FP3kUGj1pgK53N1r"

    const/4 v0, 0x0

    aput-object v1, v2, v0

    invoke-virtual {v3}, Landroid/content/Context;->getClassLoader()Ljava/lang/ClassLoader;

    move-result-object v0

    .line 9192
    :cond_1
    check-cast p1, Lcom/facebook/ads/internal/util/parcelable/WrappedParcelable;

    invoke-virtual {p1, v0}, Lcom/facebook/ads/internal/util/parcelable/WrappedParcelable;->unwrap(Ljava/lang/ClassLoader;)Landroid/os/Parcelable;

    move-result-object v1

    .line 9193
    .local v1, "state":Landroid/os/Parcelable;
    instance-of v0, v1, Lcom/facebook/ads/internal/androidx/support/v4/view/ViewPager$SavedState;

    if-nez v0, :cond_2

    .line 9194
    invoke-super {p0, v1}, Landroid/view/ViewGroup;->onRestoreInstanceState(Landroid/os/Parcelable;)V

    .line 9195
    return-void

    .line 9196
    :cond_2
    check-cast v1, Lcom/facebook/ads/internal/androidx/support/v4/view/ViewPager$SavedState;

    .line 9197
    .local v2, "ss":Lcom/facebook/ads/internal/androidx/support/v4/view/ViewPager$SavedState;
    invoke-virtual {v1}, Lcom/facebook/ads/internal/androidx/support/v4/view/AbsSavedState;->A02()Landroid/os/Parcelable;

    move-result-object v0

    invoke-super {p0, v0}, Landroid/view/ViewGroup;->onRestoreInstanceState(Landroid/os/Parcelable;)V

    .line 9198
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A01:Lcom/facebook/ads/redexgen/X/3E;

    if-eqz v0, :cond_3

    .line 9199
    iget v2, v1, Lcom/facebook/ads/internal/androidx/support/v4/view/ViewPager$SavedState;->A00:I

    const/4 v1, 0x0

    const/4 v0, 0x1

    invoke-direct {p0, v2, v1, v0}, Lcom/facebook/ads/redexgen/X/3h;->A0N(IZZ)V

    .line 9200
    :goto_0
    return-void

    .line 9201
    :cond_3
    iget v0, v1, Lcom/facebook/ads/internal/androidx/support/v4/view/ViewPager$SavedState;->A00:I

    iput v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0O:I

    .line 9202
    iget-object v0, v1, Lcom/facebook/ads/internal/androidx/support/v4/view/ViewPager$SavedState;->A01:Landroid/os/Parcelable;

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0T:Landroid/os/Parcelable;

    .line 9203
    iget-object v0, v1, Lcom/facebook/ads/internal/androidx/support/v4/view/ViewPager$SavedState;->A02:Ljava/lang/ClassLoader;

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0a:Ljava/lang/ClassLoader;

    goto :goto_0

    :cond_4
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0
.end method

.method public final onSaveInstanceState()Landroid/os/Parcelable;
    .locals 2

    .line 9204
    invoke-super {p0}, Landroid/view/ViewGroup;->onSaveInstanceState()Landroid/os/Parcelable;

    move-result-object v0

    .line 9205
    .local v0, "superState":Landroid/os/Parcelable;
    new-instance v1, Lcom/facebook/ads/internal/androidx/support/v4/view/ViewPager$SavedState;

    invoke-direct {v1, v0}, Lcom/facebook/ads/internal/androidx/support/v4/view/ViewPager$SavedState;-><init>(Landroid/os/Parcelable;)V

    .line 9206
    .local v1, "ss":Lcom/facebook/ads/internal/androidx/support/v4/view/ViewPager$SavedState;
    iget v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A00:I

    iput v0, v1, Lcom/facebook/ads/internal/androidx/support/v4/view/ViewPager$SavedState;->A00:I

    .line 9207
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A01:Lcom/facebook/ads/redexgen/X/3E;

    if-eqz v0, :cond_0

    .line 9208
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/3E;->A03()Landroid/os/Parcelable;

    move-result-object v0

    iput-object v0, v1, Lcom/facebook/ads/internal/androidx/support/v4/view/ViewPager$SavedState;->A01:Landroid/os/Parcelable;

    .line 9209
    :cond_0
    new-instance v0, Lcom/facebook/ads/internal/util/parcelable/WrappedParcelable;

    invoke-direct {v0, v1}, Lcom/facebook/ads/internal/util/parcelable/WrappedParcelable;-><init>(Landroid/os/Parcelable;)V

    return-object v0
.end method

.method public final onSizeChanged(IIII)V
    .locals 1

    .line 9210
    invoke-super {p0, p1, p2, p3, p4}, Landroid/view/ViewGroup;->onSizeChanged(IIII)V

    .line 9211
    if-eq p1, p3, :cond_0

    .line 9212
    iget v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0M:I

    invoke-direct {p0, p1, p3, v0, v0}, Lcom/facebook/ads/redexgen/X/3h;->A0K(IIII)V

    .line 9213
    :cond_0
    return-void
.end method

.method public final onTouchEvent(Landroid/view/MotionEvent;)Z
    .locals 9

    .line 9214
    move-object v3, p0

    iget-boolean v0, v3, Lcom/facebook/ads/redexgen/X/3h;->A0f:Z

    const/4 v2, 0x1

    if-eqz v0, :cond_0

    .line 9215
    return v2

    .line 9216
    :cond_0
    invoke-virtual {p1}, Landroid/view/MotionEvent;->getAction()I

    move-result v0

    const/4 v1, 0x0

    if-nez v0, :cond_1

    invoke-virtual {p1}, Landroid/view/MotionEvent;->getEdgeFlags()I

    move-result v0

    if-eqz v0, :cond_1

    .line 9217
    return v1

    .line 9218
    :cond_1
    iget-object v0, v3, Lcom/facebook/ads/redexgen/X/3h;->A01:Lcom/facebook/ads/redexgen/X/3E;

    if-eqz v0, :cond_2

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/3E;->A01()I

    move-result v0

    if-nez v0, :cond_3

    .line 9219
    .end local v5
    .end local p7
    :cond_2
    return v1

    .line 9220
    :cond_3
    iget-object v0, v3, Lcom/facebook/ads/redexgen/X/3h;->A0U:Landroid/view/VelocityTracker;

    if-nez v0, :cond_4

    .line 9221
    invoke-static {}, Landroid/view/VelocityTracker;->obtain()Landroid/view/VelocityTracker;

    move-result-object v0

    iput-object v0, v3, Lcom/facebook/ads/redexgen/X/3h;->A0U:Landroid/view/VelocityTracker;

    .line 9222
    :cond_4
    iget-object v0, v3, Lcom/facebook/ads/redexgen/X/3h;->A0U:Landroid/view/VelocityTracker;

    invoke-virtual {v0, p1}, Landroid/view/VelocityTracker;->addMovement(Landroid/view/MotionEvent;)V

    .line 9223
    invoke-virtual {p1}, Landroid/view/MotionEvent;->getAction()I

    move-result v0

    .line 9224
    .local v2, "action":I
    const/4 v7, 0x0

    .line 9225
    .local v5, "needsInvalidate":Z
    and-int/lit16 v0, v0, 0xff

    packed-switch v0, :pswitch_data_0

    .line 9226
    :cond_5
    :goto_0
    :pswitch_0
    if-eqz v7, :cond_6

    .line 9227
    invoke-static {p0}, Lcom/facebook/ads/redexgen/X/3T;->A07(Landroid/view/View;)V

    .line 9228
    :cond_6
    return v2

    .line 9229
    .end local p7
    .restart local v2    # "action":I
    :pswitch_1
    invoke-direct {p0, p1}, Lcom/facebook/ads/redexgen/X/3h;->A0P(Landroid/view/MotionEvent;)V

    .line 9230
    iget v0, v3, Lcom/facebook/ads/redexgen/X/3h;->A08:I

    invoke-virtual {p1, v0}, Landroid/view/MotionEvent;->findPointerIndex(I)I

    move-result v0

    invoke-virtual {p1, v0}, Landroid/view/MotionEvent;->getX(I)F

    move-result v0

    iput v0, v3, Lcom/facebook/ads/redexgen/X/3h;->A05:F

    goto :goto_0

    .line 9231
    :pswitch_2
    invoke-virtual {p1}, Landroid/view/MotionEvent;->getActionIndex()I

    move-result v1

    .line 9232
    .local v4, "index":I
    invoke-virtual {p1, v1}, Landroid/view/MotionEvent;->getX(I)F

    move-result v0

    .line 9233
    .local v6, "x":F
    iput v0, v3, Lcom/facebook/ads/redexgen/X/3h;->A05:F

    .line 9234
    invoke-virtual {p1, v1}, Landroid/view/MotionEvent;->getPointerId(I)I

    move-result v0

    iput v0, v3, Lcom/facebook/ads/redexgen/X/3h;->A08:I

    .line 9235
    goto :goto_0

    .line 9236
    .end local v4    # "index":I
    .end local v6    # "x":F
    :pswitch_3
    iget-boolean v0, v3, Lcom/facebook/ads/redexgen/X/3h;->A0i:Z

    if-eqz v0, :cond_5

    .line 9237
    iget v0, v3, Lcom/facebook/ads/redexgen/X/3h;->A00:I

    invoke-direct {v3, v0, v2, v1, v1}, Lcom/facebook/ads/redexgen/X/3h;->A0M(IZIZ)V

    .line 9238
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/3h;->A0U()Z

    move-result v7

    goto :goto_0

    .line 9239
    :pswitch_4
    iget-boolean v0, v3, Lcom/facebook/ads/redexgen/X/3h;->A0i:Z

    if-nez v0, :cond_8

    .line 9240
    iget v0, v3, Lcom/facebook/ads/redexgen/X/3h;->A08:I

    invoke-virtual {p1, v0}, Landroid/view/MotionEvent;->findPointerIndex(I)I

    move-result v1

    .line 9241
    .local v4, "pointerIndex":I
    const/4 v0, -0x1

    if-ne v1, v0, :cond_7

    .line 9242
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/3h;->A0U()Z

    move-result v7

    .line 9243
    goto :goto_0

    .line 9244
    :cond_7
    invoke-virtual {p1, v1}, Landroid/view/MotionEvent;->getX(I)F

    move-result v6

    .line 9245
    .restart local v6    # "x":F
    iget v0, v3, Lcom/facebook/ads/redexgen/X/3h;->A05:F

    sub-float v0, v6, v0

    invoke-static {v0}, Ljava/lang/Math;->abs(F)F

    move-result v5

    .line 9246
    .local v7, "xDiff":F
    invoke-virtual {p1, v1}, Landroid/view/MotionEvent;->getY(I)F

    move-result v4

    .line 9247
    .local v8, "y":F
    iget v0, v3, Lcom/facebook/ads/redexgen/X/3h;->A06:F

    sub-float v0, v4, v0

    invoke-static {v0}, Ljava/lang/Math;->abs(F)F

    move-result v1

    .line 9248
    .local p0, "yDiff":F
    iget v0, v3, Lcom/facebook/ads/redexgen/X/3h;->A0R:I

    int-to-float v0, v0

    cmpl-float v0, v5, v0

    if-lez v0, :cond_8

    cmpl-float v0, v5, v1

    if-lez v0, :cond_8

    .line 9249
    iput-boolean v2, v3, Lcom/facebook/ads/redexgen/X/3h;->A0i:Z

    .line 9250
    invoke-direct {v3, v2}, Lcom/facebook/ads/redexgen/X/3h;->A0T(Z)V

    .line 9251
    iget v1, v3, Lcom/facebook/ads/redexgen/X/3h;->A03:F

    sub-float/2addr v6, v1

    const/4 v0, 0x0

    cmpl-float v0, v6, v0

    if-lez v0, :cond_9

    iget v0, v3, Lcom/facebook/ads/redexgen/X/3h;->A0R:I

    int-to-float v0, v0

    add-float/2addr v1, v0

    .line 9252
    :goto_1
    iput v1, v3, Lcom/facebook/ads/redexgen/X/3h;->A05:F

    .line 9253
    iput v4, v3, Lcom/facebook/ads/redexgen/X/3h;->A06:F

    .line 9254
    invoke-virtual {v3, v2}, Lcom/facebook/ads/redexgen/X/3h;->setScrollState(I)V

    .line 9255
    invoke-direct {v3, v2}, Lcom/facebook/ads/redexgen/X/3h;->setScrollingCacheEnabled(Z)V

    .line 9256
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/3h;->getParent()Landroid/view/ViewParent;

    move-result-object v0

    .line 9257
    .local p1, "parent":Landroid/view/ViewParent;
    if-eqz v0, :cond_8

    .line 9258
    invoke-interface {v0, v2}, Landroid/view/ViewParent;->requestDisallowInterceptTouchEvent(Z)V

    .line 9259
    .end local v4    # "pointerIndex":I
    .end local v6    # "x":F
    .end local v7    # "xDiff":F
    .end local v8    # "y":F
    .end local p0    # "yDiff":F
    .end local p1    # "parent":Landroid/view/ViewParent;
    :cond_8
    iget-boolean v0, v3, Lcom/facebook/ads/redexgen/X/3h;->A0i:Z

    if-eqz v0, :cond_5

    .line 9260
    iget v0, v3, Lcom/facebook/ads/redexgen/X/3h;->A08:I

    invoke-virtual {p1, v0}, Landroid/view/MotionEvent;->findPointerIndex(I)I

    move-result v0

    .line 9261
    .local v4, "activePointerIndex":I
    invoke-virtual {p1, v0}, Landroid/view/MotionEvent;->getX(I)F

    move-result v0

    .line 9262
    .restart local v6    # "x":F
    invoke-direct {v3, v0}, Lcom/facebook/ads/redexgen/X/3h;->A0X(F)Z

    move-result v0

    or-int/2addr v7, v0

    .line 9263
    .end local v4    # "activePointerIndex":I
    .end local v6    # "x":F
    goto/16 :goto_0

    .line 9264
    :cond_9
    iget v0, v3, Lcom/facebook/ads/redexgen/X/3h;->A0R:I

    int-to-float v0, v0

    sub-float/2addr v1, v0

    goto :goto_1

    .line 9265
    :pswitch_5
    iget-boolean v0, v3, Lcom/facebook/ads/redexgen/X/3h;->A0i:Z

    if-eqz v0, :cond_5

    .line 9266
    iget-object v4, v3, Lcom/facebook/ads/redexgen/X/3h;->A0U:Landroid/view/VelocityTracker;

    .line 9267
    .local v4, "velocityTracker":Landroid/view/VelocityTracker;
    iget v0, v3, Lcom/facebook/ads/redexgen/X/3h;->A0J:I

    int-to-float v1, v0

    const/16 v0, 0x3e8

    invoke-virtual {v4, v0, v1}, Landroid/view/VelocityTracker;->computeCurrentVelocity(IF)V

    .line 9268
    iget v0, v3, Lcom/facebook/ads/redexgen/X/3h;->A08:I

    invoke-virtual {v4, v0}, Landroid/view/VelocityTracker;->getXVelocity(I)F

    move-result v0

    float-to-int v6, v0

    .line 9269
    .local v6, "initialVelocity":I
    iput-boolean v2, v3, Lcom/facebook/ads/redexgen/X/3h;->A0m:Z

    .line 9270
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/3h;->getClientWidth()I

    move-result v8

    .line 9271
    .local v7, "width":I
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/3h;->getScrollX()I

    move-result v4

    .line 9272
    .local v8, "scrollX":I
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/3h;->A03()Lcom/facebook/ads/redexgen/X/3Z;

    move-result-object v7

    .line 9273
    .local p0, "ii":Lcom/facebook/ads/redexgen/X/3Z;
    iget v0, v3, Lcom/facebook/ads/redexgen/X/3h;->A0M:I

    int-to-float v1, v0

    int-to-float v0, v8

    div-float/2addr v1, v0

    .line 9274
    .local p1, "marginOffset":F
    iget v5, v7, Lcom/facebook/ads/redexgen/X/3Z;->A02:I

    .line 9275
    .local p2, "currentPage":I
    int-to-float v4, v4

    int-to-float v0, v8

    div-float/2addr v4, v0

    iget v0, v7, Lcom/facebook/ads/redexgen/X/3Z;->A00:F

    sub-float/2addr v4, v0

    iget v0, v7, Lcom/facebook/ads/redexgen/X/3Z;->A01:F

    add-float/2addr v0, v1

    div-float/2addr v4, v0

    .line 9276
    .local p3, "pageOffset":F
    iget v0, v3, Lcom/facebook/ads/redexgen/X/3h;->A08:I

    invoke-virtual {p1, v0}, Landroid/view/MotionEvent;->findPointerIndex(I)I

    move-result v0

    .line 9277
    .local p4, "activePointerIndex":I
    invoke-virtual {p1, v0}, Landroid/view/MotionEvent;->getX(I)F

    move-result v1

    .line 9278
    .local p5, "x":F
    iget v0, v3, Lcom/facebook/ads/redexgen/X/3h;->A03:F

    sub-float/2addr v1, v0

    float-to-int v0, v1

    .line 9279
    .local p6, "totalDelta":I
    .end local v2    # "action":I
    .restart local p7
    invoke-direct {v3, v5, v4, v6, v0}, Lcom/facebook/ads/redexgen/X/3h;->A01(IFII)I

    move-result v0

    .line 9280
    .local v2, "nextPage":I
    invoke-direct {v3, v0, v2, v2, v6}, Lcom/facebook/ads/redexgen/X/3h;->A0O(IZZI)V

    .line 9281
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/3h;->A0U()Z

    move-result v7

    .line 9282
    .end local v2    # "nextPage":I
    .end local v4    # "velocityTracker":Landroid/view/VelocityTracker;
    .end local v6    # "initialVelocity":I
    .end local v7    # "width":I
    .end local v8    # "scrollX":I
    .end local p0    # "ii":Lcom/facebook/ads/redexgen/X/3Z;
    .end local p1    # "marginOffset":F
    .end local p2
    .end local p3
    .end local p4
    .end local p5
    .end local p6
    goto/16 :goto_0

    .line 9283
    .end local p7
    .restart local v2    # "nextPage":I
    .end local v2    # "nextPage":I
    .restart local p7
    :pswitch_6
    iget-object v0, v3, Lcom/facebook/ads/redexgen/X/3h;->A0X:Landroid/widget/Scroller;

    invoke-virtual {v0}, Landroid/widget/Scroller;->abortAnimation()V

    .line 9284
    iput-boolean v1, v3, Lcom/facebook/ads/redexgen/X/3h;->A0m:Z

    .line 9285
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/3h;->A0f()V

    .line 9286
    invoke-virtual {p1}, Landroid/view/MotionEvent;->getX()F

    move-result v0

    iput v0, v3, Lcom/facebook/ads/redexgen/X/3h;->A03:F

    iput v0, v3, Lcom/facebook/ads/redexgen/X/3h;->A05:F

    .line 9287
    invoke-virtual {p1}, Landroid/view/MotionEvent;->getY()F

    move-result v0

    iput v0, v3, Lcom/facebook/ads/redexgen/X/3h;->A04:F

    iput v0, v3, Lcom/facebook/ads/redexgen/X/3h;->A06:F

    .line 9288
    invoke-virtual {p1, v1}, Landroid/view/MotionEvent;->getPointerId(I)I

    move-result v0

    iput v0, v3, Lcom/facebook/ads/redexgen/X/3h;->A08:I

    .line 9289
    goto/16 :goto_0

    :pswitch_data_0
    .packed-switch 0x0
        :pswitch_6
        :pswitch_5
        :pswitch_4
        :pswitch_3
        :pswitch_0
        :pswitch_2
        :pswitch_1
    .end packed-switch
.end method

.method public final removeView(Landroid/view/View;)V
    .locals 1

    .line 9290
    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0h:Z

    if-eqz v0, :cond_0

    .line 9291
    invoke-virtual {p0, p1}, Lcom/facebook/ads/redexgen/X/3h;->removeViewInLayout(Landroid/view/View;)V

    .line 9292
    :goto_0
    return-void

    .line 9293
    :cond_0
    invoke-super {p0, p1}, Landroid/view/ViewGroup;->removeView(Landroid/view/View;)V

    goto :goto_0
.end method

.method public setAdapter(Lcom/facebook/ads/redexgen/X/3E;)V
    .locals 6

    .line 9294
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A01:Lcom/facebook/ads/redexgen/X/3E;

    const/4 v3, 0x0

    const/4 v4, 0x0

    if-eqz v0, :cond_1

    .line 9295
    invoke-virtual {v0, v3}, Lcom/facebook/ads/redexgen/X/3E;->A06(Landroid/database/DataSetObserver;)V

    .line 9296
    const/4 v5, 0x0

    .local v0, "i":I
    :goto_0
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0r:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->size()I

    move-result v0

    if-ge v5, v0, :cond_0

    .line 9297
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0r:Ljava/util/ArrayList;

    invoke-virtual {v0, v5}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/facebook/ads/redexgen/X/3Z;

    .line 9298
    .local v3, "ii":Lcom/facebook/ads/redexgen/X/3Z;
    iget-object v2, p0, Lcom/facebook/ads/redexgen/X/3h;->A01:Lcom/facebook/ads/redexgen/X/3E;

    iget v1, v0, Lcom/facebook/ads/redexgen/X/3Z;->A02:I

    iget-object v0, v0, Lcom/facebook/ads/redexgen/X/3Z;->A03:Ljava/lang/Object;

    invoke-virtual {v2, p0, v1, v0}, Lcom/facebook/ads/redexgen/X/3E;->A07(Landroid/view/ViewGroup;ILjava/lang/Object;)V

    .line 9299
    .end local v3    # "ii":Lcom/facebook/ads/redexgen/X/3Z;
    add-int/lit8 v5, v5, 0x1

    goto :goto_0

    .line 9300
    .end local v0    # "i":I
    :cond_0
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0r:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->clear()V

    .line 9301
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/3h;->A0A()V

    .line 9302
    iput v4, p0, Lcom/facebook/ads/redexgen/X/3h;->A00:I

    .line 9303
    invoke-virtual {p0, v4, v4}, Lcom/facebook/ads/redexgen/X/3h;->scrollTo(II)V

    .line 9304
    .local v0, "oldAdapter":Lcom/facebook/ads/redexgen/X/3E;
    :cond_1
    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/3h;->A01:Lcom/facebook/ads/redexgen/X/3E;

    .line 9305
    iput v4, p0, Lcom/facebook/ads/redexgen/X/3h;->A0G:I

    .line 9306
    if-eqz p1, :cond_3

    .line 9307
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0Z:Lcom/facebook/ads/redexgen/X/3e;

    if-nez v0, :cond_2

    .line 9308
    new-instance v0, Lcom/facebook/ads/redexgen/X/3e;

    invoke-direct {v0, p0}, Lcom/facebook/ads/redexgen/X/3e;-><init>(Lcom/facebook/ads/redexgen/X/3h;)V

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0Z:Lcom/facebook/ads/redexgen/X/3e;

    .line 9309
    :cond_2
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/3h;->A01:Lcom/facebook/ads/redexgen/X/3E;

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0Z:Lcom/facebook/ads/redexgen/X/3e;

    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/3E;->A06(Landroid/database/DataSetObserver;)V

    .line 9310
    iput-boolean v4, p0, Lcom/facebook/ads/redexgen/X/3h;->A0m:Z

    .line 9311
    iget-boolean v2, p0, Lcom/facebook/ads/redexgen/X/3h;->A0g:Z

    .line 9312
    .local v3, "wasFirstLayout":Z
    const/4 v1, 0x1

    iput-boolean v1, p0, Lcom/facebook/ads/redexgen/X/3h;->A0g:Z

    .line 9313
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A01:Lcom/facebook/ads/redexgen/X/3E;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/3E;->A01()I

    move-result v0

    iput v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0G:I

    .line 9314
    iget v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0O:I

    if-ltz v0, :cond_4

    .line 9315
    iget v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0O:I

    invoke-direct {p0, v0, v4, v1}, Lcom/facebook/ads/redexgen/X/3h;->A0N(IZZ)V

    .line 9316
    const/4 v0, -0x1

    iput v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0O:I

    .line 9317
    iput-object v3, p0, Lcom/facebook/ads/redexgen/X/3h;->A0T:Landroid/os/Parcelable;

    .line 9318
    iput-object v3, p0, Lcom/facebook/ads/redexgen/X/3h;->A0a:Ljava/lang/ClassLoader;

    .line 9319
    .end local v3    # "wasFirstLayout":Z
    :cond_3
    :goto_1
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0c:Ljava/util/List;

    if-eqz v0, :cond_6

    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    move-result v0

    if-nez v0, :cond_6

    .line 9320
    const/4 v1, 0x0

    .local v1, "i":I
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0c:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    .local v2, "count":I
    if-ge v1, v0, :cond_6

    .line 9321
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0c:Ljava/util/List;

    invoke-interface {v0, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    const/16 v2, 0x17d

    const/16 v1, 0x10

    const/16 v0, 0x75

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/3h;->A08(III)Ljava/lang/String;

    move-result-object v1

    new-instance v0, Ljava/lang/NullPointerException;

    invoke-direct {v0, v1}, Ljava/lang/NullPointerException;-><init>(Ljava/lang/String;)V

    throw v0

    .line 9322
    :cond_4
    if-nez v2, :cond_5

    .line 9323
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/3h;->A0f()V

    goto :goto_1

    .line 9324
    :cond_5
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/3h;->requestLayout()V

    goto :goto_1

    .line 9325
    .end local v1    # "i":I
    .end local v2    # "count":I
    :cond_6
    return-void
.end method

.method public setCurrentItem(I)V
    .locals 2

    .line 9326
    const/4 v1, 0x0

    iput-boolean v1, p0, Lcom/facebook/ads/redexgen/X/3h;->A0m:Z

    .line 9327
    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0g:Z

    xor-int/lit8 v0, v0, 0x1

    invoke-direct {p0, p1, v0, v1}, Lcom/facebook/ads/redexgen/X/3h;->A0N(IZZ)V

    .line 9328
    return-void
.end method

.method public setOffscreenPageLimit(I)V
    .locals 5

    .line 9329
    const/4 v4, 0x1

    if-ge p1, v4, :cond_0

    .line 9330
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const/16 v2, 0x7f

    const/16 v1, 0x1f

    const/16 v0, 0x3c

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/3h;->A08(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v3

    const/16 v2, 0x33

    const/16 v1, 0x1a

    const/16 v0, 0x5a

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/3h;->A08(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, v4}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    const/16 v2, 0x12c

    const/16 v1, 0x9

    const/16 v0, 0x2f

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/3h;->A08(III)Ljava/lang/String;

    move-result-object v0

    invoke-static {v0, v3}, Landroid/util/Log;->w(Ljava/lang/String;Ljava/lang/String;)I

    .line 9331
    const/4 p1, 0x1

    .line 9332
    :cond_0
    iget v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0L:I

    if-eq p1, v0, :cond_1

    .line 9333
    iput p1, p0, Lcom/facebook/ads/redexgen/X/3h;->A0L:I

    .line 9334
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/3h;->A0f()V

    .line 9335
    :cond_1
    return-void
.end method

.method public setOnPageChangeListener(Lcom/facebook/ads/redexgen/X/3c;)V
    .locals 0
    .annotation runtime Ljava/lang/Deprecated;
    .end annotation

    .line 9336
    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/3h;->A0Y:Lcom/facebook/ads/redexgen/X/3c;

    .line 9337
    return-void
.end method

.method public setPageMargin(I)V
    .locals 2

    .line 9338
    iget v1, p0, Lcom/facebook/ads/redexgen/X/3h;->A0M:I

    .line 9339
    .local v0, "oldMargin":I
    iput p1, p0, Lcom/facebook/ads/redexgen/X/3h;->A0M:I

    .line 9340
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/3h;->getWidth()I

    move-result v0

    .line 9341
    .local v1, "width":I
    invoke-direct {p0, v0, v0, p1, v1}, Lcom/facebook/ads/redexgen/X/3h;->A0K(IIII)V

    .line 9342
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/3h;->requestLayout()V

    .line 9343
    return-void
.end method

.method public setPageMarginDrawable(I)V
    .locals 1

    .line 9344
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/3h;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-static {v0, p1}, Lcom/facebook/ads/redexgen/X/2k;->A00(Landroid/content/Context;I)Landroid/graphics/drawable/Drawable;

    move-result-object v0

    invoke-virtual {p0, v0}, Lcom/facebook/ads/redexgen/X/3h;->setPageMarginDrawable(Landroid/graphics/drawable/Drawable;)V

    .line 9345
    return-void
.end method

.method public setPageMarginDrawable(Landroid/graphics/drawable/Drawable;)V
    .locals 1

    .line 9346
    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/3h;->A0S:Landroid/graphics/drawable/Drawable;

    .line 9347
    if-eqz p1, :cond_0

    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/3h;->refreshDrawableState()V

    .line 9348
    :cond_0
    if-nez p1, :cond_1

    const/4 v0, 0x1

    :goto_0
    invoke-virtual {p0, v0}, Lcom/facebook/ads/redexgen/X/3h;->setWillNotDraw(Z)V

    .line 9349
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/3h;->invalidate()V

    .line 9350
    return-void

    .line 9351
    :cond_1
    const/4 v0, 0x0

    goto :goto_0
.end method

.method public setScrollState(I)V
    .locals 1

    .line 9352
    iget v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0P:I

    if-ne v0, p1, :cond_0

    .line 9353
    return-void

    .line 9354
    :cond_0
    iput p1, p0, Lcom/facebook/ads/redexgen/X/3h;->A0P:I

    .line 9355
    const/4 v0, 0x0

    if-eqz v0, :cond_1

    .line 9356
    if-eqz p1, :cond_2

    const/4 v0, 0x1

    :goto_0
    invoke-direct {p0, v0}, Lcom/facebook/ads/redexgen/X/3h;->A0S(Z)V

    .line 9357
    :cond_1
    invoke-direct {p0, p1}, Lcom/facebook/ads/redexgen/X/3h;->A0F(I)V

    .line 9358
    return-void

    .line 9359
    :cond_2
    const/4 v0, 0x0

    goto :goto_0
.end method

.method public final verifyDrawable(Landroid/graphics/drawable/Drawable;)Z
    .locals 1

    .line 9363
    invoke-super {p0, p1}, Landroid/view/ViewGroup;->verifyDrawable(Landroid/graphics/drawable/Drawable;)Z

    move-result v0

    if-nez v0, :cond_0

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/3h;->A0S:Landroid/graphics/drawable/Drawable;

    if-ne p1, v0, :cond_1

    :cond_0
    const/4 v0, 0x1

    :goto_0
    return v0

    :cond_1
    const/4 v0, 0x0

    goto :goto_0
.end method
