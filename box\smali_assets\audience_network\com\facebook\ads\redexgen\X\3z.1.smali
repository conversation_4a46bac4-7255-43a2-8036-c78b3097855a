.class public interface abstract Lcom/facebook/ads/redexgen/X/3z;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/facebook/ads/redexgen/X/40;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "AccessibilityNodeInfoBridge"
.end annotation


# virtual methods
.method public abstract A4W(I)Ljava/lang/Object;
.end method

.method public abstract A5h(Ljava/lang/String;I)Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "I)",
            "Ljava/util/List<",
            "Ljava/lang/Object;",
            ">;"
        }
    .end annotation
.end method

.method public abstract A5i(I)Ljava/lang/Object;
.end method

.method public abstract ADx(IILandroid/os/Bundle;)Z
.end method
