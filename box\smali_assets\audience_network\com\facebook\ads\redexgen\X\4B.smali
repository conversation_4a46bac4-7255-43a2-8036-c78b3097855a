.class public interface abstract Lcom/facebook/ads/redexgen/X/4B;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/facebook/ads/redexgen/X/4C;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "Callback"
.end annotation


# virtual methods
.method public abstract A3e(Landroid/view/View;ILandroid/view/ViewGroup$LayoutParams;)V
.end method

.method public abstract A57(I)V
.end method

.method public abstract A6M(I)Landroid/view/View;
.end method

.method public abstract A6N()I
.end method

.method public abstract A6Q(Landroid/view/View;)Lcom/facebook/ads/redexgen/X/56;
.end method

.method public abstract A8n(Landroid/view/View;)I
.end method

.method public abstract ABQ(Landroid/view/View;)V
.end method

.method public abstract ABx(Landroid/view/View;)V
.end method

.method public abstract AF6()V
.end method

.method public abstract AFA(I)V
.end method

.method public abstract addView(Landroid/view/View;I)V
.end method
