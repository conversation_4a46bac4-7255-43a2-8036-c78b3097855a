.class public final Lcom/facebook/ads/redexgen/X/4n;
.super Lcom/facebook/ads/redexgen/X/Br;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/facebook/ads/redexgen/X/Bq;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x11
    name = "CeaOutputBuffer"
.end annotation


# instance fields
.field public final synthetic A00:Lcom/facebook/ads/redexgen/X/Bq;


# direct methods
.method public constructor <init>(Lcom/facebook/ads/redexgen/X/Bq;)V
    .locals 0

    .line 11029
    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/4n;->A00:Lcom/facebook/ads/redexgen/X/Bq;

    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/Br;-><init>()V

    return-void
.end method

.method public synthetic constructor <init>(Lcom/facebook/ads/redexgen/X/Bq;Lcom/facebook/ads/redexgen/X/Fx;)V
    .locals 0

    .line 11030
    invoke-direct {p0, p1}, Lcom/facebook/ads/redexgen/X/4n;-><init>(Lcom/facebook/ads/redexgen/X/Bq;)V

    return-void
.end method


# virtual methods
.method public final A08()V
    .locals 1

    .line 11031
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/4n;->A00:Lcom/facebook/ads/redexgen/X/Bq;

    invoke-virtual {v0, p0}, Lcom/facebook/ads/redexgen/X/Bq;->A0Q(Lcom/facebook/ads/redexgen/X/Br;)V

    .line 11032
    return-void
.end method
