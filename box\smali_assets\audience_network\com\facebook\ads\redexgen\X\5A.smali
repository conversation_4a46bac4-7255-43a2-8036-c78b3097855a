.class public final Lcom/facebook/ads/redexgen/X/5A;
.super Lcom/facebook/ads/redexgen/X/Bq;
.source ""


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/facebook/ads/redexgen/X/Fu;
    }
.end annotation


# static fields
.field public static A0C:[B

.field public static A0D:[Ljava/lang/String;

.field public static final A0E:[I

.field public static final A0F:[I

.field public static final A0G:[I

.field public static final A0H:[I

.field public static final A0I:[I

.field public static final A0J:[I

.field public static final A0K:[I


# instance fields
.field public A00:B

.field public A01:B

.field public A02:I

.field public A03:I

.field public A04:Lcom/facebook/ads/redexgen/X/Fu;

.field public A05:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lcom/facebook/ads/redexgen/X/Fn;",
            ">;"
        }
    .end annotation
.end field

.field public A06:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lcom/facebook/ads/redexgen/X/Fn;",
            ">;"
        }
    .end annotation
.end field

.field public A07:Z

.field public final A08:I

.field public final A09:I

.field public final A0A:Lcom/facebook/ads/redexgen/X/Hz;

.field public final A0B:Ljava/util/ArrayList;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/ArrayList<",
            "Lcom/facebook/ads/redexgen/X/Fu;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public static constructor <clinit>()V
    .locals 3

    .line 525
    const/16 v0, 0x8

    new-array v2, v0, [Ljava/lang/String;

    const/4 v1, 0x0

    const-string v0, ""

    aput-object v0, v2, v1

    const/4 v1, 0x1

    const-string v0, "UOqHcTLtEs7RiTmy1QusyMeFEB23VRO4"

    aput-object v0, v2, v1

    const/4 v1, 0x2

    const-string v0, "RTk1kBX5PmCwF3vSKDHM5RpxSToEVllh"

    aput-object v0, v2, v1

    const/4 v1, 0x3

    const-string v0, "JV0f3puRHBuDT0TwlUq0wFp"

    aput-object v0, v2, v1

    const/4 v1, 0x4

    const-string v0, "x2su64Hvd39hRMCT3aa5nJ2s0reV8jFq"

    aput-object v0, v2, v1

    const/4 v1, 0x5

    const-string v0, "Uq5EuILG8kpYquwld9Hd2RiG7wrTKtDW"

    aput-object v0, v2, v1

    const/4 v1, 0x6

    const-string v0, "n7d5UKOeFpSokQbO2p0B5"

    aput-object v0, v2, v1

    const/4 v1, 0x7

    const-string v0, "QmvZ7EzKQdyrN87iPZyE0qt0G2nn9i3V"

    aput-object v0, v2, v1

    sput-object v2, Lcom/facebook/ads/redexgen/X/5A;->A0D:[Ljava/lang/String;

    invoke-static {}, Lcom/facebook/ads/redexgen/X/5A;->A07()V

    const/16 v1, 0x8

    new-array v0, v1, [I

    fill-array-data v0, :array_0

    sput-object v0, Lcom/facebook/ads/redexgen/X/5A;->A0G:[I

    .line 526
    new-array v0, v1, [I

    fill-array-data v0, :array_1

    sput-object v0, Lcom/facebook/ads/redexgen/X/5A;->A0F:[I

    .line 527
    const/4 v0, 0x7

    new-array v0, v0, [I

    fill-array-data v0, :array_2

    sput-object v0, Lcom/facebook/ads/redexgen/X/5A;->A0K:[I

    .line 528
    const/16 v0, 0x60

    new-array v0, v0, [I

    fill-array-data v0, :array_3

    sput-object v0, Lcom/facebook/ads/redexgen/X/5A;->A0E:[I

    .line 529
    const/16 v0, 0x10

    new-array v0, v0, [I

    fill-array-data v0, :array_4

    sput-object v0, Lcom/facebook/ads/redexgen/X/5A;->A0H:[I

    .line 530
    const/16 v1, 0x20

    new-array v0, v1, [I

    fill-array-data v0, :array_5

    sput-object v0, Lcom/facebook/ads/redexgen/X/5A;->A0I:[I

    .line 531
    new-array v0, v1, [I

    fill-array-data v0, :array_6

    sput-object v0, Lcom/facebook/ads/redexgen/X/5A;->A0J:[I

    return-void

    :array_0
    .array-data 4
        0xb
        0x1
        0x3
        0xc
        0xe
        0x5
        0x7
        0x9
    .end array-data

    :array_1
    .array-data 4
        0x0
        0x4
        0x8
        0xc
        0x10
        0x14
        0x18
        0x1c
    .end array-data

    :array_2
    .array-data 4
        -0x1
        -0xff0100
        -0xffff01
        -0xff0001
        -0x10000
        -0x100
        -0xff01
    .end array-data

    :array_3
    .array-data 4
        0x20
        0x21
        0x22
        0x23
        0x24
        0x25
        0x26
        0x27
        0x28
        0x29
        0xe1
        0x2b
        0x2c
        0x2d
        0x2e
        0x2f
        0x30
        0x31
        0x32
        0x33
        0x34
        0x35
        0x36
        0x37
        0x38
        0x39
        0x3a
        0x3b
        0x3c
        0x3d
        0x3e
        0x3f
        0x40
        0x41
        0x42
        0x43
        0x44
        0x45
        0x46
        0x47
        0x48
        0x49
        0x4a
        0x4b
        0x4c
        0x4d
        0x4e
        0x4f
        0x50
        0x51
        0x52
        0x53
        0x54
        0x55
        0x56
        0x57
        0x58
        0x59
        0x5a
        0x5b
        0xe9
        0x5d
        0xed
        0xf3
        0xfa
        0x61
        0x62
        0x63
        0x64
        0x65
        0x66
        0x67
        0x68
        0x69
        0x6a
        0x6b
        0x6c
        0x6d
        0x6e
        0x6f
        0x70
        0x71
        0x72
        0x73
        0x74
        0x75
        0x76
        0x77
        0x78
        0x79
        0x7a
        0xe7
        0xf7
        0xd1
        0xf1
        0x25a0
    .end array-data

    :array_4
    .array-data 4
        0xae
        0xb0
        0xbd
        0xbf
        0x2122
        0xa2
        0xa3
        0x266a
        0xe0
        0x20
        0xe8
        0xe2
        0xea
        0xee
        0xf4
        0xfb
    .end array-data

    :array_5
    .array-data 4
        0xc1
        0xc9
        0xd3
        0xda
        0xdc
        0xfc
        0x2018
        0xa1
        0x2a
        0x27
        0x2014
        0xa9
        0x2120
        0x2022
        0x201c
        0x201d
        0xc0
        0xc2
        0xc7
        0xc8
        0xca
        0xcb
        0xeb
        0xce
        0xcf
        0xef
        0xd4
        0xd9
        0xf9
        0xdb
        0xab
        0xbb
    .end array-data

    :array_6
    .array-data 4
        0xc3
        0xe3
        0xcd
        0xcc
        0xec
        0xd2
        0xf2
        0xd5
        0xf5
        0x7b
        0x7d
        0x5c
        0x5e
        0x5f
        0x7c
        0x7e
        0xc4
        0xe4
        0xd6
        0xf6
        0xdf
        0xa5
        0xa4
        0x2502
        0xc5
        0xe5
        0xd8
        0xf8
        0x250c
        0x2510
        0x2514
        0x2518
    .end array-data
.end method

.method public constructor <init>(Ljava/lang/String;I)V
    .locals 4

    .line 12860
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/Bq;-><init>()V

    .line 12861
    new-instance v0, Lcom/facebook/ads/redexgen/X/Hz;

    invoke-direct {v0}, Lcom/facebook/ads/redexgen/X/Hz;-><init>()V

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/5A;->A0A:Lcom/facebook/ads/redexgen/X/Hz;

    .line 12862
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/5A;->A0B:Ljava/util/ArrayList;

    .line 12863
    const/4 v1, 0x4

    const/4 v3, 0x0

    new-instance v0, Lcom/facebook/ads/redexgen/X/Fu;

    invoke-direct {v0, v3, v1}, Lcom/facebook/ads/redexgen/X/Fu;-><init>(II)V

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/5A;->A04:Lcom/facebook/ads/redexgen/X/Fu;

    .line 12864
    const/4 v2, 0x0

    const/16 v1, 0x19

    const/16 v0, 0x56

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/5A;->A04(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    const/4 v1, 0x2

    if-eqz v0, :cond_0

    const/4 v0, 0x2

    :goto_0
    iput v0, p0, Lcom/facebook/ads/redexgen/X/5A;->A08:I

    .line 12865
    packed-switch p2, :pswitch_data_0

    .line 12866
    const/4 v0, 0x1

    iput v0, p0, Lcom/facebook/ads/redexgen/X/5A;->A09:I

    .line 12867
    :goto_1
    invoke-direct {p0, v3}, Lcom/facebook/ads/redexgen/X/5A;->A0B(I)V

    .line 12868
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/5A;->A06()V

    .line 12869
    return-void

    .line 12870
    :pswitch_0
    iput v1, p0, Lcom/facebook/ads/redexgen/X/5A;->A09:I

    .line 12871
    goto :goto_1

    .line 12872
    :cond_0
    const/4 v0, 0x3

    goto :goto_0

    nop

    :pswitch_data_0
    .packed-switch 0x3
        :pswitch_0
        :pswitch_0
    .end packed-switch
.end method

.method public static A00(B)C
    .locals 1

    .line 12873
    and-int/lit8 v0, p0, 0x7f

    add-int/lit8 p0, v0, -0x20

    .line 12874
    .local v0, "index":I
    sget-object v0, Lcom/facebook/ads/redexgen/X/5A;->A0E:[I

    aget v0, v0, p0

    int-to-char v0, v0

    return v0
.end method

.method public static A01(B)C
    .locals 1

    .line 12875
    and-int/lit8 p0, p0, 0x1f

    .line 12876
    .local v0, "index":I
    sget-object v0, Lcom/facebook/ads/redexgen/X/5A;->A0I:[I

    aget v0, v0, p0

    int-to-char v0, v0

    return v0
.end method

.method public static A02(B)C
    .locals 1

    .line 12877
    and-int/lit8 p0, p0, 0x1f

    .line 12878
    .local v0, "index":I
    sget-object v0, Lcom/facebook/ads/redexgen/X/5A;->A0J:[I

    aget v0, v0, p0

    int-to-char v0, v0

    return v0
.end method

.method public static A03(B)C
    .locals 1

    .line 12879
    and-int/lit8 p0, p0, 0xf

    .line 12880
    .local v0, "index":I
    sget-object v0, Lcom/facebook/ads/redexgen/X/5A;->A0H:[I

    aget v0, v0, p0

    int-to-char v0, v0

    return v0
.end method

.method public static A04(III)Ljava/lang/String;
    .locals 2

    sget-object v1, Lcom/facebook/ads/redexgen/X/5A;->A0C:[B

    add-int v0, p0, p1

    invoke-static {v1, p0, v0}, Ljava/util/Arrays;->copyOfRange([BII)[B

    move-result-object p0

    const/4 v1, 0x0

    :goto_0
    array-length v0, p0

    if-ge v1, v0, :cond_0

    aget-byte v0, p0, v1

    sub-int/2addr v0, p2

    add-int/lit8 v0, v0, -0x37

    int-to-byte v0, v0

    aput-byte v0, p0, v1

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_0
    new-instance v0, Ljava/lang/String;

    invoke-direct {v0, p0}, Ljava/lang/String;-><init>([B)V

    return-object v0
.end method

.method private A05()Ljava/util/List;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lcom/facebook/ads/redexgen/X/Fn;",
            ">;"
        }
    .end annotation

    .line 12881
    new-instance v2, Ljava/util/ArrayList;

    invoke-direct {v2}, Ljava/util/ArrayList;-><init>()V

    .line 12882
    .local v0, "displayCues":Ljava/util/List;, "Ljava/util/List<Lcom/facebook/ads/internal/exoplayer2/thirdparty/text/Cue;>;"
    const/4 v1, 0x0

    .local v1, "i":I
    :goto_0
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/5A;->A0B:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->size()I

    move-result v0

    if-ge v1, v0, :cond_1

    .line 12883
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/5A;->A0B:Ljava/util/ArrayList;

    invoke-virtual {v0, v1}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/facebook/ads/redexgen/X/Fu;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/Fu;->A05()Lcom/facebook/ads/redexgen/X/Fn;

    move-result-object v0

    .line 12884
    .local v2, "cue":Lcom/facebook/ads/redexgen/X/Fn;
    if-eqz v0, :cond_0

    .line 12885
    invoke-interface {v2, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 12886
    .end local v2    # "cue":Lcom/facebook/ads/redexgen/X/Fn;
    :cond_0
    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    .line 12887
    .end local v1    # "i":I
    :cond_1
    return-object v2
.end method

.method private A06()V
    .locals 2

    .line 12888
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/5A;->A04:Lcom/facebook/ads/redexgen/X/Fu;

    iget v0, p0, Lcom/facebook/ads/redexgen/X/5A;->A02:I

    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/Fu;->A09(I)V

    .line 12889
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/5A;->A0B:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->clear()V

    .line 12890
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/5A;->A0B:Ljava/util/ArrayList;

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/5A;->A04:Lcom/facebook/ads/redexgen/X/Fu;

    invoke-virtual {v1, v0}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 12891
    return-void
.end method

.method public static A07()V
    .locals 4

    const/16 v0, 0x19

    new-array v3, v0, [B

    sget-object v1, Lcom/facebook/ads/redexgen/X/5A;->A0D:[Ljava/lang/String;

    const/4 v0, 0x3

    aget-object v0, v1, v0

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v1

    const/16 v0, 0x15

    if-eq v1, v0, :cond_0

    sget-object v2, Lcom/facebook/ads/redexgen/X/5A;->A0D:[Ljava/lang/String;

    const-string v1, "Dii1DZOZQfkEzXA6fYCCVz7kQ8ZnR11r"

    const/4 v0, 0x5

    aput-object v1, v2, v0

    fill-array-data v3, :array_0

    sput-object v3, Lcom/facebook/ads/redexgen/X/5A;->A0C:[B

    return-void

    :cond_0
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    :array_0
    .array-data 1
        -0x12t
        -0x3t
        -0x3t
        -0x7t
        -0xat
        -0x10t
        -0x12t
        0x1t
        -0xat
        -0x4t
        -0x5t
        -0x44t
        0x5t
        -0x46t
        -0x6t
        -0x3t
        -0x3ft
        -0x46t
        -0x10t
        -0xet
        -0x12t
        -0x46t
        -0x3dt
        -0x43t
        -0x3bt
    .end array-data
.end method

.method private A08(B)V
    .locals 3

    .line 12892
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/5A;->A04:Lcom/facebook/ads/redexgen/X/Fu;

    const/16 v0, 0x20

    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/Fu;->A08(C)V

    .line 12893
    and-int/lit8 v0, p1, 0x1

    const/4 v2, 0x1

    if-ne v0, v2, :cond_0

    .line 12894
    .local v0, "underline":Z
    :goto_0
    shr-int/lit8 v0, p1, 0x1

    and-int/lit8 v1, v0, 0x7

    .line 12895
    .local v1, "style":I
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/5A;->A04:Lcom/facebook/ads/redexgen/X/Fu;

    invoke-virtual {v0, v1, v2}, Lcom/facebook/ads/redexgen/X/Fu;->A0E(IZ)V

    .line 12896
    return-void

    .line 12897
    :cond_0
    const/4 v2, 0x0

    goto :goto_0
.end method

.method private A09(B)V
    .locals 4

    .line 12898
    const/4 v0, 0x2

    const/4 v3, 0x3

    const/4 v2, 0x1

    packed-switch p1, :pswitch_data_0

    .line 12899
    :pswitch_0
    iget v1, p0, Lcom/facebook/ads/redexgen/X/5A;->A02:I

    if-nez v1, :cond_0

    .line 12900
    return-void

    .line 12901
    :cond_0
    sparse-switch p1, :sswitch_data_0

    .line 12902
    :cond_1
    :goto_0
    :sswitch_0
    return-void

    .line 12903
    :sswitch_1
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/5A;->A05()Ljava/util/List;

    move-result-object v0

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/5A;->A05:Ljava/util/List;

    .line 12904
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/5A;->A06()V

    .line 12905
    goto :goto_0

    .line 12906
    :sswitch_2
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/5A;->A06()V

    .line 12907
    goto :goto_0

    .line 12908
    :sswitch_3
    if-ne v1, v2, :cond_1

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/5A;->A04:Lcom/facebook/ads/redexgen/X/Fu;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/Fu;->A0F()Z

    move-result v0

    if-nez v0, :cond_1

    .line 12909
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/5A;->A04:Lcom/facebook/ads/redexgen/X/Fu;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/Fu;->A07()V

    goto :goto_0

    .line 12910
    :sswitch_4
    const/4 v0, 0x0

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/5A;->A05:Ljava/util/List;

    .line 12911
    if-eq v1, v2, :cond_2

    if-ne v1, v3, :cond_1

    .line 12912
    :cond_2
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/5A;->A06()V

    goto :goto_0

    .line 12913
    :sswitch_5
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/5A;->A04:Lcom/facebook/ads/redexgen/X/Fu;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/Fu;->A06()V

    .line 12914
    goto :goto_0

    .line 12915
    :pswitch_1
    invoke-direct {p0, v3}, Lcom/facebook/ads/redexgen/X/5A;->A0B(I)V

    sget-object v1, Lcom/facebook/ads/redexgen/X/5A;->A0D:[Ljava/lang/String;

    const/4 v0, 0x5

    aget-object v1, v1, v0

    const/16 v0, 0x15

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    const/16 v0, 0x6b

    if-eq v1, v0, :cond_3

    .line 12916
    sget-object v2, Lcom/facebook/ads/redexgen/X/5A;->A0D:[Ljava/lang/String;

    const-string v1, "pIRkBqgh"

    const/4 v0, 0x3

    aput-object v1, v2, v0

    return-void

    :cond_3
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    .line 12917
    :pswitch_2
    invoke-direct {p0, v2}, Lcom/facebook/ads/redexgen/X/5A;->A0B(I)V

    .line 12918
    const/4 v0, 0x4

    invoke-direct {p0, v0}, Lcom/facebook/ads/redexgen/X/5A;->A0C(I)V

    .line 12919
    return-void

    .line 12920
    :pswitch_3
    invoke-direct {p0, v2}, Lcom/facebook/ads/redexgen/X/5A;->A0B(I)V

    sget-object v2, Lcom/facebook/ads/redexgen/X/5A;->A0D:[Ljava/lang/String;

    const/4 v0, 0x7

    aget-object v1, v2, v0

    const/4 v0, 0x1

    aget-object v2, v2, v0

    const/16 v0, 0x17

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    invoke-virtual {v2, v0}, Ljava/lang/String;->charAt(I)C

    move-result v0

    if-eq v1, v0, :cond_4

    .line 12921
    sget-object v2, Lcom/facebook/ads/redexgen/X/5A;->A0D:[Ljava/lang/String;

    const-string v1, "7AFdC54jnBMcb1fQALYn1FmxfK"

    const/4 v0, 0x3

    aput-object v1, v2, v0

    invoke-direct {p0, v3}, Lcom/facebook/ads/redexgen/X/5A;->A0C(I)V

    .line 12922
    return-void

    .line 12923
    :cond_4
    sget-object v2, Lcom/facebook/ads/redexgen/X/5A;->A0D:[Ljava/lang/String;

    const-string v1, "j0BFBVzEby1MIwgBQMEQk"

    const/4 v0, 0x6

    aput-object v1, v2, v0

    const-string v1, ""

    const/4 v0, 0x0

    aput-object v1, v2, v0

    invoke-direct {p0, v3}, Lcom/facebook/ads/redexgen/X/5A;->A0C(I)V

    .line 12924
    return-void

    .line 12925
    :pswitch_4
    invoke-direct {p0, v2}, Lcom/facebook/ads/redexgen/X/5A;->A0B(I)V

    .line 12926
    invoke-direct {p0, v0}, Lcom/facebook/ads/redexgen/X/5A;->A0C(I)V

    .line 12927
    return-void

    .line 12928
    :pswitch_5
    invoke-direct {p0, v0}, Lcom/facebook/ads/redexgen/X/5A;->A0B(I)V

    .line 12929
    return-void

    nop

    :pswitch_data_0
    .packed-switch 0x20
        :pswitch_5
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_4
        :pswitch_3
        :pswitch_2
        :pswitch_0
        :pswitch_1
    .end packed-switch

    :sswitch_data_0
    .sparse-switch
        0x21 -> :sswitch_5
        0x24 -> :sswitch_0
        0x2c -> :sswitch_4
        0x2d -> :sswitch_3
        0x2e -> :sswitch_2
        0x2f -> :sswitch_1
    .end sparse-switch
.end method

.method private A0A(BB)V
    .locals 6

    .line 12930
    sget-object v1, Lcom/facebook/ads/redexgen/X/5A;->A0G:[I

    and-int/lit8 v0, p1, 0x7

    aget v3, v1, v0

    .line 12931
    .local v0, "row":I
    and-int/lit8 v0, p2, 0x20

    const/4 v4, 0x0

    const/4 v5, 0x1

    if-eqz v0, :cond_7

    const/4 v0, 0x1

    .line 12932
    .local v1, "nextRowDown":Z
    :goto_0
    if-eqz v0, :cond_0

    .line 12933
    add-int/lit8 v3, v3, 0x1

    .line 12934
    :cond_0
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/5A;->A04:Lcom/facebook/ads/redexgen/X/Fu;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/Fu;->A04()I

    move-result v0

    if-eq v3, v0, :cond_2

    .line 12935
    iget v0, p0, Lcom/facebook/ads/redexgen/X/5A;->A02:I

    if-eq v0, v5, :cond_1

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/5A;->A04:Lcom/facebook/ads/redexgen/X/Fu;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/Fu;->A0F()Z

    move-result v0

    if-nez v0, :cond_1

    .line 12936
    iget v2, p0, Lcom/facebook/ads/redexgen/X/5A;->A02:I

    iget v0, p0, Lcom/facebook/ads/redexgen/X/5A;->A03:I

    new-instance v1, Lcom/facebook/ads/redexgen/X/Fu;

    invoke-direct {v1, v2, v0}, Lcom/facebook/ads/redexgen/X/Fu;-><init>(II)V

    iput-object v1, p0, Lcom/facebook/ads/redexgen/X/5A;->A04:Lcom/facebook/ads/redexgen/X/Fu;

    .line 12937
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/5A;->A0B:Ljava/util/ArrayList;

    invoke-virtual {v0, v1}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 12938
    :cond_1
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/5A;->A04:Lcom/facebook/ads/redexgen/X/Fu;

    invoke-virtual {v0, v3}, Lcom/facebook/ads/redexgen/X/Fu;->A0C(I)V

    .line 12939
    :cond_2
    and-int/lit8 v1, p2, 0x10

    const/16 v0, 0x10

    if-ne v1, v0, :cond_6

    const/4 v3, 0x1

    .line 12940
    .local v4, "isCursor":Z
    :goto_1
    and-int/lit8 v0, p2, 0x1

    if-ne v0, v5, :cond_3

    const/4 v4, 0x1

    .line 12941
    .local v2, "underline":Z
    :cond_3
    shr-int/lit8 v0, p2, 0x1

    and-int/lit8 v2, v0, 0x7

    .line 12942
    .local v3, "cursorOrStyle":I
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/5A;->A04:Lcom/facebook/ads/redexgen/X/Fu;

    if-eqz v3, :cond_5

    const/16 v0, 0x8

    :goto_2
    invoke-virtual {v1, v0, v4}, Lcom/facebook/ads/redexgen/X/Fu;->A0E(IZ)V

    .line 12943
    if-eqz v3, :cond_4

    .line 12944
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/5A;->A04:Lcom/facebook/ads/redexgen/X/Fu;

    sget-object v0, Lcom/facebook/ads/redexgen/X/5A;->A0F:[I

    aget v0, v0, v2

    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/Fu;->A0B(I)V

    .line 12945
    :cond_4
    return-void

    .line 12946
    :cond_5
    move v0, v2

    goto :goto_2

    .line 12947
    :cond_6
    const/4 v3, 0x0

    goto :goto_1

    .line 12948
    :cond_7
    const/4 v0, 0x0

    goto :goto_0
.end method

.method private A0B(I)V
    .locals 5

    .line 12949
    iget v0, p0, Lcom/facebook/ads/redexgen/X/5A;->A02:I

    if-ne v0, p1, :cond_0

    .line 12950
    return-void

    .line 12951
    :cond_0
    iget v4, p0, Lcom/facebook/ads/redexgen/X/5A;->A02:I

    .line 12952
    .local v0, "oldCaptionMode":I
    iput p1, p0, Lcom/facebook/ads/redexgen/X/5A;->A02:I

    .line 12953
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/5A;->A06()V

    .line 12954
    const/4 v3, 0x3

    sget-object v2, Lcom/facebook/ads/redexgen/X/5A;->A0D:[Ljava/lang/String;

    const/4 v0, 0x6

    aget-object v1, v2, v0

    const/4 v0, 0x0

    aget-object v0, v2, v0

    invoke-virtual {v1}, Ljava/lang/String;->length()I

    move-result v1

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v0

    if-eq v1, v0, :cond_3

    sget-object v2, Lcom/facebook/ads/redexgen/X/5A;->A0D:[Ljava/lang/String;

    const-string v1, "ABJBOr9tpuOfw3WTn1dkaeDCVhgUjFFd"

    const/4 v0, 0x7

    aput-object v1, v2, v0

    const-string v1, "d4G6TzBMILcpXDpqIyaB8VlXWHVbvB7r"

    const/4 v0, 0x1

    aput-object v1, v2, v0

    if-eq v4, v3, :cond_1

    const/4 v0, 0x1

    if-eq p1, v0, :cond_1

    if-nez p1, :cond_2

    .line 12955
    :cond_1
    const/4 v0, 0x0

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/5A;->A05:Ljava/util/List;

    .line 12956
    :cond_2
    return-void

    :cond_3
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0
.end method

.method private A0C(I)V
    .locals 1

    .line 12957
    iput p1, p0, Lcom/facebook/ads/redexgen/X/5A;->A03:I

    .line 12958
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/5A;->A04:Lcom/facebook/ads/redexgen/X/Fu;

    invoke-virtual {v0, p1}, Lcom/facebook/ads/redexgen/X/Fu;->A0A(I)V

    .line 12959
    return-void
.end method

.method public static A0D(B)Z
    .locals 1

    .line 12960
    and-int/lit16 p0, p0, 0xf0

    const/16 v0, 0x10

    if-ne p0, v0, :cond_0

    const/4 v0, 0x1

    :goto_0
    return v0

    :cond_0
    const/4 v0, 0x0

    goto :goto_0
.end method

.method private A0E(BB)Z
    .locals 5

    .line 12961
    invoke-static {p1}, Lcom/facebook/ads/redexgen/X/5A;->A0D(B)Z

    move-result v4

    .line 12962
    .local v0, "isRepeatableControl":Z
    if-eqz v4, :cond_2

    .line 12963
    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/5A;->A07:Z

    const/4 v3, 0x1

    if-eqz v0, :cond_1

    iget-byte v0, p0, Lcom/facebook/ads/redexgen/X/5A;->A00:B

    if-ne v0, p1, :cond_1

    iget-byte v0, p0, Lcom/facebook/ads/redexgen/X/5A;->A01:B

    if-ne v0, p2, :cond_1

    .line 12964
    const/4 v0, 0x0

    iput-boolean v0, p0, Lcom/facebook/ads/redexgen/X/5A;->A07:Z

    sget-object v1, Lcom/facebook/ads/redexgen/X/5A;->A0D:[Ljava/lang/String;

    const/4 v0, 0x3

    aget-object v0, v1, v0

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v1

    const/16 v0, 0x15

    if-eq v1, v0, :cond_0

    .line 12965
    sget-object v2, Lcom/facebook/ads/redexgen/X/5A;->A0D:[Ljava/lang/String;

    const-string v1, "tLHt8UhnxX3deh7O9w8vVVmAo0sQxALm"

    const/4 v0, 0x7

    aput-object v1, v2, v0

    const-string v1, "EbP4Ke60ENtBy2kbmKo88ltRhFojrrj9"

    const/4 v0, 0x1

    aput-object v1, v2, v0

    return v3

    :cond_0
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    .line 12966
    :cond_1
    iput-boolean v3, p0, Lcom/facebook/ads/redexgen/X/5A;->A07:Z

    .line 12967
    iput-byte p1, p0, Lcom/facebook/ads/redexgen/X/5A;->A00:B

    .line 12968
    iput-byte p2, p0, Lcom/facebook/ads/redexgen/X/5A;->A01:B

    .line 12969
    :cond_2
    invoke-static {p1, p2}, Lcom/facebook/ads/redexgen/X/5A;->A0F(BB)Z

    move-result v0

    if-eqz v0, :cond_4

    .line 12970
    invoke-direct {p0, p2}, Lcom/facebook/ads/redexgen/X/5A;->A08(B)V

    .line 12971
    :cond_3
    :goto_0
    return v4

    .line 12972
    :cond_4
    invoke-static {p1, p2}, Lcom/facebook/ads/redexgen/X/5A;->A0H(BB)Z

    move-result v0

    if-eqz v0, :cond_5

    .line 12973
    invoke-direct {p0, p1, p2}, Lcom/facebook/ads/redexgen/X/5A;->A0A(BB)V

    goto :goto_0

    .line 12974
    :cond_5
    invoke-static {p1, p2}, Lcom/facebook/ads/redexgen/X/5A;->A0I(BB)Z

    move-result v0

    if-eqz v0, :cond_6

    .line 12975
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/5A;->A04:Lcom/facebook/ads/redexgen/X/Fu;

    add-int/lit8 v0, p2, -0x20

    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/Fu;->A0D(I)V

    goto :goto_0

    .line 12976
    :cond_6
    invoke-static {p1, p2}, Lcom/facebook/ads/redexgen/X/5A;->A0G(BB)Z

    move-result v3

    sget-object v2, Lcom/facebook/ads/redexgen/X/5A;->A0D:[Ljava/lang/String;

    const/4 v0, 0x6

    aget-object v1, v2, v0

    const/4 v0, 0x0

    aget-object v0, v2, v0

    invoke-virtual {v1}, Ljava/lang/String;->length()I

    move-result v1

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v0

    if-eq v1, v0, :cond_7

    sget-object v2, Lcom/facebook/ads/redexgen/X/5A;->A0D:[Ljava/lang/String;

    const-string v1, "swbvKOmcGgaLmVTPPwnrE4ARixZyKCFo"

    const/4 v0, 0x5

    aput-object v1, v2, v0

    if-eqz v3, :cond_3

    .line 12977
    :goto_1
    invoke-direct {p0, p2}, Lcom/facebook/ads/redexgen/X/5A;->A09(B)V

    goto :goto_0

    :cond_7
    sget-object v2, Lcom/facebook/ads/redexgen/X/5A;->A0D:[Ljava/lang/String;

    const-string v1, "bB00fVvIMETGp28QmAgSrtQi1elcPeSe"

    const/4 v0, 0x7

    aput-object v1, v2, v0

    const-string v1, "LRx4WR8VImivhrBFvPxZGhVFm5yp8QMB"

    const/4 v0, 0x1

    aput-object v1, v2, v0

    if-eqz v3, :cond_3

    goto :goto_1
.end method

.method public static A0F(BB)Z
    .locals 1

    .line 12978
    and-int/lit16 p0, p0, 0xf7

    const/16 v0, 0x11

    if-ne p0, v0, :cond_0

    and-int/lit16 p0, p1, 0xf0

    const/16 v0, 0x20

    if-ne p0, v0, :cond_0

    const/4 v0, 0x1

    :goto_0
    return v0

    :cond_0
    const/4 v0, 0x0

    goto :goto_0
.end method

.method public static A0G(BB)Z
    .locals 1

    .line 12979
    and-int/lit16 p0, p0, 0xf7

    const/16 v0, 0x14

    if-ne p0, v0, :cond_0

    and-int/lit16 p0, p1, 0xf0

    const/16 v0, 0x20

    if-ne p0, v0, :cond_0

    const/4 v0, 0x1

    :goto_0
    return v0

    :cond_0
    const/4 v0, 0x0

    goto :goto_0
.end method

.method public static A0H(BB)Z
    .locals 1

    .line 12980
    and-int/lit16 p0, p0, 0xf0

    const/16 v0, 0x10

    if-ne p0, v0, :cond_0

    and-int/lit16 p0, p1, 0xc0

    const/16 v0, 0x40

    if-ne p0, v0, :cond_0

    const/4 v0, 0x1

    :goto_0
    return v0

    :cond_0
    const/4 v0, 0x0

    goto :goto_0
.end method

.method public static A0I(BB)Z
    .locals 1

    .line 12981
    and-int/lit16 p0, p0, 0xf7

    const/16 v0, 0x17

    if-ne p0, v0, :cond_0

    const/16 v0, 0x21

    if-lt p1, v0, :cond_0

    const/16 v0, 0x23

    if-gt p1, v0, :cond_0

    const/4 v0, 0x1

    :goto_0
    return v0

    :cond_0
    const/4 v0, 0x0

    goto :goto_0
.end method

.method public static synthetic A0J()[I
    .locals 1

    .line 12982
    sget-object v0, Lcom/facebook/ads/redexgen/X/5A;->A0K:[I

    return-object v0
.end method


# virtual methods
.method public final bridge synthetic A0L()Lcom/facebook/ads/redexgen/X/C5;
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lcom/facebook/ads/redexgen/X/Fp;
        }
    .end annotation

    .line 12983
    invoke-super {p0}, Lcom/facebook/ads/redexgen/X/Bq;->A0L()Lcom/facebook/ads/redexgen/X/C5;

    move-result-object v0

    return-object v0
.end method

.method public final bridge synthetic A0M()Lcom/facebook/ads/redexgen/X/Br;
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lcom/facebook/ads/redexgen/X/Fp;
        }
    .end annotation

    .line 12984
    invoke-super {p0}, Lcom/facebook/ads/redexgen/X/Bq;->A0M()Lcom/facebook/ads/redexgen/X/Br;

    move-result-object v0

    return-object v0
.end method

.method public final A0N()Lcom/facebook/ads/redexgen/X/WI;
    .locals 2

    .line 12985
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/5A;->A05:Ljava/util/List;

    iput-object v1, p0, Lcom/facebook/ads/redexgen/X/5A;->A06:Ljava/util/List;

    .line 12986
    new-instance v0, Lcom/facebook/ads/redexgen/X/WI;

    invoke-direct {v0, v1}, Lcom/facebook/ads/redexgen/X/WI;-><init>(Ljava/util/List;)V

    return-object v0
.end method

.method public final bridge synthetic A0O(Lcom/facebook/ads/redexgen/X/C5;)V
    .locals 0
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lcom/facebook/ads/redexgen/X/Fp;
        }
    .end annotation

    .line 12987
    invoke-super {p0, p1}, Lcom/facebook/ads/redexgen/X/Bq;->A0O(Lcom/facebook/ads/redexgen/X/C5;)V

    return-void
.end method

.method public final A0P(Lcom/facebook/ads/redexgen/X/C5;)V
    .locals 9

    .line 12988
    iget-object v2, p0, Lcom/facebook/ads/redexgen/X/5A;->A0A:Lcom/facebook/ads/redexgen/X/Hz;

    iget-object v0, p1, Lcom/facebook/ads/redexgen/X/Xr;->A01:Ljava/nio/ByteBuffer;

    invoke-virtual {v0}, Ljava/nio/ByteBuffer;->array()[B

    move-result-object v1

    iget-object v0, p1, Lcom/facebook/ads/redexgen/X/Xr;->A01:Ljava/nio/ByteBuffer;

    invoke-virtual {v0}, Ljava/nio/ByteBuffer;->limit()I

    move-result v0

    invoke-virtual {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Hz;->A0b([BI)V

    .line 12989
    const/4 v8, 0x0

    .line 12990
    .local v0, "captionDataProcessed":Z
    const/4 v7, 0x0

    .line 12991
    .local v1, "isRepeatableControl":Z
    :cond_0
    :goto_0
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/5A;->A0A:Lcom/facebook/ads/redexgen/X/Hz;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/Hz;->A04()I

    move-result v1

    iget v0, p0, Lcom/facebook/ads/redexgen/X/5A;->A08:I

    const/4 v2, 0x1

    if-lt v1, v0, :cond_b

    .line 12992
    const/4 v5, 0x2

    if-ne v0, v5, :cond_9

    const/4 v6, -0x4

    .line 12993
    .local v3, "ccDataHeader":B
    :goto_1
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/5A;->A0A:Lcom/facebook/ads/redexgen/X/Hz;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/Hz;->A0E()I

    move-result v0

    and-int/lit8 v0, v0, 0x7f

    int-to-byte v4, v0

    .line 12994
    .local v5, "ccData1":B
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/5A;->A0A:Lcom/facebook/ads/redexgen/X/Hz;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/Hz;->A0E()I

    move-result v0

    and-int/lit8 v0, v0, 0x7f

    int-to-byte v3, v0

    .line 12995
    .local v6, "ccData2":B
    and-int/lit8 v1, v6, 0x6

    const/4 v0, 0x4

    if-eq v1, v0, :cond_1

    goto :goto_0

    .line 12996
    :cond_1
    iget v1, p0, Lcom/facebook/ads/redexgen/X/5A;->A09:I

    if-ne v1, v2, :cond_2

    and-int/lit8 v0, v6, 0x1

    if-nez v0, :cond_0

    :cond_2
    if-ne v1, v5, :cond_3

    and-int/lit8 v0, v6, 0x1

    if-eq v0, v2, :cond_3

    goto :goto_0

    .line 12997
    :cond_3
    if-nez v4, :cond_4

    if-nez v3, :cond_4

    goto :goto_0

    .line 12998
    :cond_4
    const/4 v8, 0x1

    .line 12999
    and-int/lit16 v1, v4, 0xf7

    const/16 v0, 0x11

    if-ne v1, v0, :cond_5

    and-int/lit16 v5, v3, 0xf0

    sget-object v1, Lcom/facebook/ads/redexgen/X/5A;->A0D:[Ljava/lang/String;

    const/4 v0, 0x3

    aget-object v0, v1, v0

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v1

    const/16 v0, 0x15

    if-eq v1, v0, :cond_a

    sget-object v2, Lcom/facebook/ads/redexgen/X/5A;->A0D:[Ljava/lang/String;

    const-string v1, "eaZSvvtKQ377g5qz65V0guECH3aHDc35"

    const/4 v0, 0x5

    aput-object v1, v2, v0

    const/16 v0, 0x30

    if-ne v5, v0, :cond_5

    .line 13000
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/5A;->A04:Lcom/facebook/ads/redexgen/X/Fu;

    invoke-static {v3}, Lcom/facebook/ads/redexgen/X/5A;->A03(B)C

    move-result v0

    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/Fu;->A08(C)V

    .line 13001
    goto :goto_0

    .line 13002
    :cond_5
    and-int/lit16 v1, v4, 0xf6

    const/16 v0, 0x12

    if-ne v1, v0, :cond_7

    and-int/lit16 v1, v3, 0xe0

    const/16 v0, 0x20

    if-ne v1, v0, :cond_7

    .line 13003
    iget-object v5, p0, Lcom/facebook/ads/redexgen/X/5A;->A04:Lcom/facebook/ads/redexgen/X/Fu;

    sget-object v1, Lcom/facebook/ads/redexgen/X/5A;->A0D:[Ljava/lang/String;

    const/4 v0, 0x3

    aget-object v0, v1, v0

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v1

    const/16 v0, 0x15

    if-eq v1, v0, :cond_a

    sget-object v2, Lcom/facebook/ads/redexgen/X/5A;->A0D:[Ljava/lang/String;

    const-string v1, "seUeLqtTdSBoap5Vmsj4c"

    const/4 v0, 0x6

    aput-object v1, v2, v0

    const-string v1, ""

    const/4 v0, 0x0

    aput-object v1, v2, v0

    invoke-virtual {v5}, Lcom/facebook/ads/redexgen/X/Fu;->A06()V

    .line 13004
    and-int/lit8 v0, v4, 0x1

    if-nez v0, :cond_6

    .line 13005
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/5A;->A04:Lcom/facebook/ads/redexgen/X/Fu;

    invoke-static {v3}, Lcom/facebook/ads/redexgen/X/5A;->A01(B)C

    move-result v0

    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/Fu;->A08(C)V

    goto/16 :goto_0

    .line 13006
    :cond_6
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/5A;->A04:Lcom/facebook/ads/redexgen/X/Fu;

    invoke-static {v3}, Lcom/facebook/ads/redexgen/X/5A;->A02(B)C

    move-result v0

    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/Fu;->A08(C)V

    .line 13007
    goto/16 :goto_0

    .line 13008
    :cond_7
    and-int/lit16 v0, v4, 0xe0

    if-nez v0, :cond_8

    .line 13009
    invoke-direct {p0, v4, v3}, Lcom/facebook/ads/redexgen/X/5A;->A0E(BB)Z

    move-result v7

    .line 13010
    goto/16 :goto_0

    .line 13011
    :cond_8
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/5A;->A04:Lcom/facebook/ads/redexgen/X/Fu;

    invoke-static {v4}, Lcom/facebook/ads/redexgen/X/5A;->A00(B)C

    move-result v0

    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/Fu;->A08(C)V

    .line 13012
    and-int/lit16 v0, v3, 0xe0

    if-eqz v0, :cond_0

    .line 13013
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/5A;->A04:Lcom/facebook/ads/redexgen/X/Fu;

    invoke-static {v3}, Lcom/facebook/ads/redexgen/X/5A;->A00(B)C

    move-result v0

    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/Fu;->A08(C)V

    goto/16 :goto_0

    .line 13014
    :cond_9
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/5A;->A0A:Lcom/facebook/ads/redexgen/X/Hz;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/Hz;->A0E()I

    move-result v0

    int-to-byte v6, v0

    goto/16 :goto_1

    :cond_a
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    .line 13015
    :cond_b
    if-eqz v8, :cond_e

    .line 13016
    if-nez v7, :cond_c

    .line 13017
    const/4 v0, 0x0

    iput-boolean v0, p0, Lcom/facebook/ads/redexgen/X/5A;->A07:Z

    .line 13018
    :cond_c
    iget v1, p0, Lcom/facebook/ads/redexgen/X/5A;->A02:I

    if-eq v1, v2, :cond_d

    const/4 v0, 0x3

    if-ne v1, v0, :cond_e

    .line 13019
    :cond_d
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/5A;->A05()Ljava/util/List;

    move-result-object v0

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/5A;->A05:Ljava/util/List;

    .line 13020
    :cond_e
    return-void
.end method

.method public final A0R()Z
    .locals 2

    .line 13021
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/5A;->A05:Ljava/util/List;

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/5A;->A06:Ljava/util/List;

    if-eq v1, v0, :cond_0

    const/4 v0, 0x1

    :goto_0
    return v0

    :cond_0
    const/4 v0, 0x0

    goto :goto_0
.end method

.method public final AEV()V
    .locals 0

    .line 13022
    return-void
.end method

.method public final bridge synthetic AGB(J)V
    .locals 0

    .line 13023
    invoke-super {p0, p1, p2}, Lcom/facebook/ads/redexgen/X/Bq;->AGB(J)V

    return-void
.end method

.method public final flush()V
    .locals 2

    .line 13024
    invoke-super {p0}, Lcom/facebook/ads/redexgen/X/Bq;->flush()V

    .line 13025
    const/4 v0, 0x0

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/5A;->A05:Ljava/util/List;

    .line 13026
    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/5A;->A06:Ljava/util/List;

    .line 13027
    const/4 v1, 0x0

    invoke-direct {p0, v1}, Lcom/facebook/ads/redexgen/X/5A;->A0B(I)V

    .line 13028
    const/4 v0, 0x4

    invoke-direct {p0, v0}, Lcom/facebook/ads/redexgen/X/5A;->A0C(I)V

    .line 13029
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/5A;->A06()V

    .line 13030
    iput-boolean v1, p0, Lcom/facebook/ads/redexgen/X/5A;->A07:Z

    .line 13031
    iput-byte v1, p0, Lcom/facebook/ads/redexgen/X/5A;->A00:B

    .line 13032
    iput-byte v1, p0, Lcom/facebook/ads/redexgen/X/5A;->A01:B

    .line 13033
    return-void
.end method
