.class public interface abstract Lcom/facebook/ads/redexgen/X/5T;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/facebook/ads/redexgen/X/5V;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "BackButtonInterceptor"
.end annotation


# virtual methods
.method public abstract A8u()Z
.end method
