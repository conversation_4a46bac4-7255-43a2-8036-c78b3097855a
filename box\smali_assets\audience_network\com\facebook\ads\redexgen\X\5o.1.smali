.class public interface abstract Lcom/facebook/ads/redexgen/X/5o;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/facebook/ads/redexgen/X/5q;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "NativeBannerImageLoadTaskListener"
.end annotation


# virtual methods
.method public abstract ABi(Landroid/graphics/drawable/Drawable;)V
.end method
