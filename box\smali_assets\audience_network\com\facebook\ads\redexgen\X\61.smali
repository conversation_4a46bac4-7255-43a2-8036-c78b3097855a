.class public interface abstract Lcom/facebook/ads/redexgen/X/61;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/facebook/ads/redexgen/X/62;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "AssetsLoadListener"
.end annotation


# virtual methods
.method public abstract AAl()V
.end method

.method public abstract AAm()V
.end method
