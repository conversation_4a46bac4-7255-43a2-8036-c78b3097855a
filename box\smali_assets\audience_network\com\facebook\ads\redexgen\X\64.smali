.class public abstract Lcom/facebook/ads/redexgen/X/64;
.super Ljava/lang/Object;
.source ""


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 14844
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static A00()Lcom/facebook/ads/redexgen/X/Z4;
    .locals 1

    .line 14845
    new-instance v0, Lcom/facebook/ads/redexgen/X/Z4;

    invoke-direct {v0}, Lcom/facebook/ads/redexgen/X/Z4;-><init>()V

    return-object v0
.end method
