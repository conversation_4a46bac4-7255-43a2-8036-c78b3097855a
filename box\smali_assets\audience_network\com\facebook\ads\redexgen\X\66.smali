.class public interface abstract Lcom/facebook/ads/redexgen/X/66;
.super Ljava/lang/Object;
.source ""


# static fields
.field public static final A00:Ljava/lang/String;


# direct methods
.method public static constructor <clinit>()V
    .locals 1

    .line 562
    const-class v0, Lcom/facebook/ads/redexgen/X/66;

    invoke-virtual {v0}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object v0

    sput-object v0, Lcom/facebook/ads/redexgen/X/66;->A00:Ljava/lang/String;

    return-void
.end method


# virtual methods
.method public abstract A3P(Lcom/facebook/ads/redexgen/X/68;)V
.end method

.method public abstract A62()Ljava/util/Set;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Set<",
            "Lcom/facebook/ads/redexgen/X/69;",
            ">;"
        }
    .end annotation
.end method
