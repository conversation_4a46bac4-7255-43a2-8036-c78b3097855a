.class public final Lcom/facebook/ads/redexgen/X/6Q;
.super Lcom/facebook/ads/redexgen/X/Br;
.source ""


# instance fields
.field public final A00:Lcom/facebook/ads/redexgen/X/C8;


# direct methods
.method public constructor <init>(Lcom/facebook/ads/redexgen/X/C8;)V
    .locals 0

    .line 15114
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/Br;-><init>()V

    .line 15115
    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/6Q;->A00:Lcom/facebook/ads/redexgen/X/C8;

    .line 15116
    return-void
.end method


# virtual methods
.method public final A08()V
    .locals 1

    .line 15117
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/6Q;->A00:Lcom/facebook/ads/redexgen/X/C8;

    invoke-virtual {v0, p0}, Lcom/facebook/ads/redexgen/X/C8;->A0c(Lcom/facebook/ads/redexgen/X/Br;)V

    .line 15118
    return-void
.end method
