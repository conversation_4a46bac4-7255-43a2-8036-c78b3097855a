.class public final Lcom/facebook/ads/redexgen/X/6z;
.super Lcom/facebook/ads/redexgen/X/HN;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/facebook/ads/redexgen/X/6x;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "KeepServerBundleResponse"
.end annotation


# direct methods
.method public constructor <init>(Lcom/facebook/ads/redexgen/X/Hw;)V
    .locals 1

    .line 16628
    sget-object v0, Lcom/facebook/ads/redexgen/X/S6;->A04:Lcom/facebook/ads/redexgen/X/S6;

    invoke-direct {p0, p1, v0}, Lcom/facebook/ads/redexgen/X/HN;-><init>(Lcom/facebook/ads/redexgen/X/Hw;Lcom/facebook/ads/redexgen/X/S6;)V

    .line 16629
    return-void
.end method
