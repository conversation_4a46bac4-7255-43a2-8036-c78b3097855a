.class public final Lcom/facebook/ads/redexgen/X/76;
.super Lcom/facebook/ads/redexgen/X/Jr;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/facebook/ads/redexgen/X/Jh;-><init>(Lcom/facebook/ads/redexgen/X/RP;Lcom/facebook/ads/redexgen/X/89;Ljava/util/concurrent/Executor;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field public final synthetic A00:Lcom/facebook/ads/redexgen/X/Jh;


# direct methods
.method public constructor <init>(Lcom/facebook/ads/redexgen/X/Jh;Lcom/facebook/ads/redexgen/X/Ra;)V
    .locals 0

    .line 16639
    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/76;->A00:Lcom/facebook/ads/redexgen/X/Jh;

    invoke-direct {p0, p2}, Lcom/facebook/ads/redexgen/X/Jr;-><init>(Lcom/facebook/ads/redexgen/X/Ra;)V

    return-void
.end method
