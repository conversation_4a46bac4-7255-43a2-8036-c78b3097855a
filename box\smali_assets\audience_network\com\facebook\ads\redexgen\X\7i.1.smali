.class public interface abstract Lcom/facebook/ads/redexgen/X/7i;
.super Ljava/lang/Object;
.source ""


# virtual methods
.method public abstract A4n()Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end method

.method public abstract A61()Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end method

.method public abstract A7F()Ljava/lang/String;
.end method

.method public abstract A7H()Ljava/lang/String;
.end method

.method public abstract A8Y()Z
.end method

.method public abstract A94()Z
.end method

.method public abstract AAL()V
.end method

.method public abstract AAd()V
.end method
