.class public final enum Lcom/facebook/ads/redexgen/X/7q;
.super Ljava/lang/Enum;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/facebook/ads/redexgen/X/7r;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x4019
    name = "QueryError"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Enum<",
        "Lcom/facebook/ads/redexgen/X/7q;",
        ">;"
    }
.end annotation


# static fields
.field public static A02:[B

.field public static A03:[Ljava/lang/String;

.field public static final synthetic A04:[Lcom/facebook/ads/redexgen/X/7q;

.field public static final enum A05:Lcom/facebook/ads/redexgen/X/7q;

.field public static final enum A06:Lcom/facebook/ads/redexgen/X/7q;

.field public static final enum A07:Lcom/facebook/ads/redexgen/X/7q;

.field public static final enum A08:Lcom/facebook/ads/redexgen/X/7q;

.field public static final enum A09:Lcom/facebook/ads/redexgen/X/7q;


# instance fields
.field public final A00:I

.field public final A01:Ljava/lang/String;


# direct methods
.method public static constructor <clinit>()V
    .locals 13

    .line 673
    const/16 v0, 0x8

    new-array v2, v0, [Ljava/lang/String;

    const/4 v1, 0x0

    const-string v0, "dUypCp5ZH75"

    aput-object v0, v2, v1

    const/4 v1, 0x1

    const-string v0, "x5Qm8f9lc"

    aput-object v0, v2, v1

    const/4 v1, 0x2

    const-string v0, "sq2W39eKqgwqqOWlIAV"

    aput-object v0, v2, v1

    const/4 v1, 0x3

    const-string v0, "igK"

    aput-object v0, v2, v1

    const/4 v1, 0x4

    const-string v0, "bv2fGbu4PZe4QDTt94R09bPmX6"

    aput-object v0, v2, v1

    const/4 v1, 0x5

    const-string v0, "Gs0WF5WWQbMJ1GPXWCa"

    aput-object v0, v2, v1

    const/4 v1, 0x6

    const-string v0, "E1e7Uw5ci0LxHvCHKGZ7MMTImO0krz0e"

    aput-object v0, v2, v1

    const/4 v1, 0x7

    const-string v0, "59L0PM8TZSDfga3AwgwmMzwLrxdyp45T"

    aput-object v0, v2, v1

    sput-object v2, Lcom/facebook/ads/redexgen/X/7q;->A03:[Ljava/lang/String;

    invoke-static {}, Lcom/facebook/ads/redexgen/X/7q;->A01()V

    const/16 v4, 0x2328

    const/4 v2, 0x0

    const/16 v1, 0x1e

    const/16 v0, 0x74

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/7q;->A00(III)Ljava/lang/String;

    move-result-object v3

    const/16 v2, 0xde

    const/4 v1, 0x7

    const/16 v0, 0x5e

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/7q;->A00(III)Ljava/lang/String;

    move-result-object v0

    const/4 v10, 0x0

    new-instance v9, Lcom/facebook/ads/redexgen/X/7q;

    invoke-direct {v9, v0, v10, v4, v3}, Lcom/facebook/ads/redexgen/X/7q;-><init>(Ljava/lang/String;IILjava/lang/String;)V

    sput-object v9, Lcom/facebook/ads/redexgen/X/7q;->A09:Lcom/facebook/ads/redexgen/X/7q;

    .line 674
    const/16 v4, 0xbb9

    const/16 v2, 0xa0

    const/16 v1, 0x1d

    const/16 v0, 0x38

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/7q;->A00(III)Ljava/lang/String;

    move-result-object v3

    const/16 v2, 0x3c

    const/16 v1, 0xf

    const/16 v0, 0xa

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/7q;->A00(III)Ljava/lang/String;

    move-result-object v0

    const/4 v8, 0x1

    new-instance v7, Lcom/facebook/ads/redexgen/X/7q;

    invoke-direct {v7, v0, v8, v4, v3}, Lcom/facebook/ads/redexgen/X/7q;-><init>(Ljava/lang/String;IILjava/lang/String;)V

    sput-object v7, Lcom/facebook/ads/redexgen/X/7q;->A07:Lcom/facebook/ads/redexgen/X/7q;

    .line 675
    const/16 v4, 0xbba

    const/16 v2, 0x7d

    const/16 v1, 0x23

    const/4 v0, 0x6

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/7q;->A00(III)Ljava/lang/String;

    move-result-object v3

    const/16 v2, 0x2d

    const/16 v1, 0xf

    const/16 v0, 0x15

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/7q;->A00(III)Ljava/lang/String;

    move-result-object v0

    const/4 v6, 0x2

    new-instance v5, Lcom/facebook/ads/redexgen/X/7q;

    invoke-direct {v5, v0, v6, v4, v3}, Lcom/facebook/ads/redexgen/X/7q;-><init>(Ljava/lang/String;IILjava/lang/String;)V

    sput-object v5, Lcom/facebook/ads/redexgen/X/7q;->A06:Lcom/facebook/ads/redexgen/X/7q;

    .line 676
    const/16 v12, 0xbbb

    const/16 v2, 0xbd

    const/16 v1, 0x21

    const/16 v0, 0x24

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/7q;->A00(III)Ljava/lang/String;

    move-result-object v11

    const/16 v2, 0x4b

    const/16 v1, 0xf

    const/16 v0, 0x71

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/7q;->A00(III)Ljava/lang/String;

    move-result-object v0

    const/4 v4, 0x3

    new-instance v3, Lcom/facebook/ads/redexgen/X/7q;

    invoke-direct {v3, v0, v4, v12, v11}, Lcom/facebook/ads/redexgen/X/7q;-><init>(Ljava/lang/String;IILjava/lang/String;)V

    sput-object v3, Lcom/facebook/ads/redexgen/X/7q;->A08:Lcom/facebook/ads/redexgen/X/7q;

    .line 677
    const/16 v12, 0xbbc

    const/16 v2, 0x5a

    const/16 v1, 0x23

    const/16 v0, 0x47

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/7q;->A00(III)Ljava/lang/String;

    move-result-object v11

    const/16 v2, 0x1e

    const/16 v1, 0xf

    const/16 v0, 0x59

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/7q;->A00(III)Ljava/lang/String;

    move-result-object v0

    const/4 v2, 0x4

    new-instance v1, Lcom/facebook/ads/redexgen/X/7q;

    invoke-direct {v1, v0, v2, v12, v11}, Lcom/facebook/ads/redexgen/X/7q;-><init>(Ljava/lang/String;IILjava/lang/String;)V

    sput-object v1, Lcom/facebook/ads/redexgen/X/7q;->A05:Lcom/facebook/ads/redexgen/X/7q;

    .line 678
    const/4 v0, 0x5

    new-array v0, v0, [Lcom/facebook/ads/redexgen/X/7q;

    aput-object v9, v0, v10

    aput-object v7, v0, v8

    aput-object v5, v0, v6

    aput-object v3, v0, v4

    aput-object v1, v0, v2

    sput-object v0, Lcom/facebook/ads/redexgen/X/7q;->A04:[Lcom/facebook/ads/redexgen/X/7q;

    return-void
.end method

.method public constructor <init>(Ljava/lang/String;IILjava/lang/String;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I",
            "Ljava/lang/String;",
            ")V"
        }
    .end annotation

    .line 17155
    invoke-direct {p0, p1, p2}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    .line 17156
    iput p3, p0, Lcom/facebook/ads/redexgen/X/7q;->A00:I

    .line 17157
    iput-object p4, p0, Lcom/facebook/ads/redexgen/X/7q;->A01:Ljava/lang/String;

    .line 17158
    return-void
.end method

.method public static A00(III)Ljava/lang/String;
    .locals 4

    sget-object v1, Lcom/facebook/ads/redexgen/X/7q;->A02:[B

    add-int v0, p0, p1

    invoke-static {v1, p0, v0}, Ljava/util/Arrays;->copyOfRange([BII)[B

    move-result-object v3

    const/4 p0, 0x0

    :goto_0
    array-length p1, v3

    sget-object v1, Lcom/facebook/ads/redexgen/X/7q;->A03:[Ljava/lang/String;

    const/4 v0, 0x4

    aget-object v0, v1, v0

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v1

    const/16 v0, 0x1a

    if-eq v1, v0, :cond_0

    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    :cond_0
    sget-object v2, Lcom/facebook/ads/redexgen/X/7q;->A03:[Ljava/lang/String;

    const-string v1, "61vyGPsquycEeLbb0w4"

    const/4 v0, 0x5

    aput-object v1, v2, v0

    if-ge p0, p1, :cond_2

    aget-byte p1, v3, p0

    sget-object v1, Lcom/facebook/ads/redexgen/X/7q;->A03:[Ljava/lang/String;

    const/4 v0, 0x6

    aget-object v1, v1, v0

    const/16 v0, 0x11

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    const/16 v0, 0x33

    if-eq v1, v0, :cond_1

    sget-object v2, Lcom/facebook/ads/redexgen/X/7q;->A03:[Ljava/lang/String;

    const-string v1, "XBx"

    const/4 v0, 0x3

    aput-object v1, v2, v0

    xor-int/2addr p1, p2

    xor-int/lit8 v0, p1, 0x69

    int-to-byte v0, v0

    aput-byte v0, v3, p0

    add-int/lit8 p0, p0, 0x1

    goto :goto_0

    :cond_1
    sget-object v2, Lcom/facebook/ads/redexgen/X/7q;->A03:[Ljava/lang/String;

    const-string v1, "7Eu"

    const/4 v0, 0x3

    aput-object v1, v2, v0

    xor-int/2addr p1, p2

    xor-int/lit8 v0, p1, 0x69

    int-to-byte v0, v0

    aput-byte v0, v3, p0

    add-int/lit8 p0, p0, 0x0

    goto :goto_0

    :cond_2
    new-instance v0, Ljava/lang/String;

    invoke-direct {v0, v3}, Ljava/lang/String;-><init>([B)V

    return-object v0
.end method

.method public static A01()V
    .locals 1

    const/16 v0, 0xe5

    new-array v0, v0, [B

    fill-array-data v0, :array_0

    sput-object v0, Lcom/facebook/ads/redexgen/X/7q;->A02:[B

    return-void

    :array_0
    .array-data 1
        0x5ct
        0x73t
        0x3dt
        0x68t
        0x73t
        0x76t
        0x73t
        0x72t
        0x6at
        0x73t
        0x3dt
        0x78t
        0x6ft
        0x6ft
        0x72t
        0x6ft
        0x3dt
        0x75t
        0x7ct
        0x6et
        0x3dt
        0x72t
        0x7et
        0x7et
        0x68t
        0x6ft
        0x6ft
        0x78t
        0x79t
        0x33t
        0x74t
        0x71t
        0x64t
        0x71t
        0x72t
        0x71t
        0x63t
        0x75t
        0x6ft
        0x74t
        0x75t
        0x7ct
        0x75t
        0x64t
        0x75t
        0x38t
        0x3dt
        0x28t
        0x3dt
        0x3et
        0x3dt
        0x2ft
        0x39t
        0x23t
        0x35t
        0x32t
        0x2ft
        0x39t
        0x2et
        0x28t
        0x27t
        0x22t
        0x37t
        0x22t
        0x21t
        0x22t
        0x30t
        0x26t
        0x3ct
        0x30t
        0x26t
        0x2ft
        0x26t
        0x20t
        0x37t
        0x5ct
        0x59t
        0x4ct
        0x59t
        0x5at
        0x59t
        0x4bt
        0x5dt
        0x47t
        0x4dt
        0x48t
        0x5ct
        0x59t
        0x4ct
        0x5dt
        0x68t
        0x4ft
        0x47t
        0x42t
        0x4bt
        0x4at
        0xet
        0x5at
        0x41t
        0xet
        0x4at
        0x4bt
        0x42t
        0x4bt
        0x5at
        0x4bt
        0xet
        0x5ct
        0x41t
        0x59t
        0xet
        0x48t
        0x5ct
        0x41t
        0x43t
        0xet
        0x4at
        0x4ft
        0x5at
        0x4ft
        0x4ct
        0x4ft
        0x5dt
        0x4bt
        0x0t
        0x29t
        0xet
        0x6t
        0x3t
        0xat
        0xbt
        0x4ft
        0x1bt
        0x0t
        0x4ft
        0x6t
        0x1t
        0x1ct
        0xat
        0x1dt
        0x1bt
        0x4ft
        0x1dt
        0x0t
        0x18t
        0x4ft
        0x6t
        0x1t
        0x1bt
        0x0t
        0x4ft
        0xbt
        0xet
        0x1bt
        0xet
        0xdt
        0xet
        0x1ct
        0xat
        0x41t
        0x17t
        0x30t
        0x38t
        0x3dt
        0x34t
        0x35t
        0x71t
        0x25t
        0x3et
        0x71t
        0x23t
        0x34t
        0x30t
        0x35t
        0x71t
        0x37t
        0x23t
        0x3et
        0x3ct
        0x71t
        0x35t
        0x30t
        0x25t
        0x30t
        0x33t
        0x30t
        0x22t
        0x34t
        0x7ft
        0xbt
        0x2ct
        0x24t
        0x21t
        0x28t
        0x29t
        0x6dt
        0x39t
        0x22t
        0x6dt
        0x38t
        0x3dt
        0x29t
        0x2ct
        0x39t
        0x28t
        0x6dt
        0x3ft
        0x22t
        0x3at
        0x6dt
        0x24t
        0x23t
        0x6dt
        0x29t
        0x2ct
        0x39t
        0x2ct
        0x2ft
        0x2ct
        0x3et
        0x28t
        0x63t
        0x62t
        0x79t
        0x7ct
        0x79t
        0x78t
        0x60t
        0x79t
    .end array-data
.end method

.method public static valueOf(Ljava/lang/String;)Lcom/facebook/ads/redexgen/X/7q;
    .locals 1

    .line 17161
    const-class v0, Lcom/facebook/ads/redexgen/X/7q;

    invoke-static {v0, p0}, Ljava/lang/Enum;->valueOf(Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Enum;

    move-result-object v0

    check-cast v0, Lcom/facebook/ads/redexgen/X/7q;

    return-object v0
.end method

.method public static values()[Lcom/facebook/ads/redexgen/X/7q;
    .locals 1

    .line 17162
    sget-object v0, Lcom/facebook/ads/redexgen/X/7q;->A04:[Lcom/facebook/ads/redexgen/X/7q;

    invoke-virtual {v0}, [Ljava/lang/Object;->clone()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [Lcom/facebook/ads/redexgen/X/7q;

    return-object v0
.end method


# virtual methods
.method public final A02()I
    .locals 1

    .line 17159
    iget v0, p0, Lcom/facebook/ads/redexgen/X/7q;->A00:I

    return v0
.end method

.method public final A03()Ljava/lang/String;
    .locals 1

    .line 17160
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/7q;->A01:Ljava/lang/String;

    return-object v0
.end method
