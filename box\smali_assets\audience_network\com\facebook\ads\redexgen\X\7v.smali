.class public interface abstract Lcom/facebook/ads/redexgen/X/7v;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/facebook/ads/redexgen/X/7w;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "SettingsStorage"
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/facebook/ads/redexgen/X/7u;
    }
.end annotation


# virtual methods
.method public abstract AAQ(Lcom/facebook/ads/redexgen/X/7u;)V
.end method
