.class public abstract Lcom/facebook/ads/redexgen/X/7x;
.super Ljava/lang/Object;
.source ""


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 17269
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static A00()Lcom/facebook/ads/redexgen/X/Yi;
    .locals 1

    .line 17270
    new-instance v0, Lcom/facebook/ads/redexgen/X/Yi;

    invoke-direct {v0}, Lcom/facebook/ads/redexgen/X/Yi;-><init>()V

    return-object v0
.end method
