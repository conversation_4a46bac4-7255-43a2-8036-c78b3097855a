.class public interface abstract Lcom/facebook/ads/redexgen/X/82;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/facebook/ads/redexgen/X/84;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "DebugEventStorage"
.end annotation


# virtual methods
.method public abstract AHD(Ljava/lang/String;Ljava/util/Map;Lcom/facebook/ads/redexgen/X/7f;)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ">;",
            "Lcom/facebook/ads/redexgen/X/7f;",
            ")V"
        }
    .end annotation
.end method
