.class public final Lcom/facebook/ads/redexgen/X/85;
.super Ljava/lang/Object;
.source ""


# instance fields
.field public final A00:I

.field public final A01:Lcom/facebook/ads/redexgen/X/8B;

.field public final A02:Ljava/lang/String;


# direct methods
.method public constructor <init>(Ljava/lang/String;ILcom/facebook/ads/redexgen/X/8B;)V
    .locals 0

    .line 17528
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 17529
    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/85;->A02:Ljava/lang/String;

    .line 17530
    iput p2, p0, Lcom/facebook/ads/redexgen/X/85;->A00:I

    .line 17531
    iput-object p3, p0, Lcom/facebook/ads/redexgen/X/85;->A01:Lcom/facebook/ads/redexgen/X/8B;

    .line 17532
    return-void
.end method


# virtual methods
.method public final A00()I
    .locals 1

    .line 17533
    iget v0, p0, Lcom/facebook/ads/redexgen/X/85;->A00:I

    return v0
.end method

.method public final A01()Lcom/facebook/ads/redexgen/X/8B;
    .locals 1

    .line 17534
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/85;->A01:Lcom/facebook/ads/redexgen/X/8B;

    return-object v0
.end method

.method public final A02()Ljava/lang/String;
    .locals 1

    .line 17535
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/85;->A02:Ljava/lang/String;

    return-object v0
.end method
