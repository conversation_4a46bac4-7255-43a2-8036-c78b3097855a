.class public interface abstract Lcom/facebook/ads/redexgen/X/87;
.super Ljava/lang/Object;
.source ""


# virtual methods
.method public abstract AHD(Ljava/lang/String;Ljava/util/Map;Lcom/facebook/ads/redexgen/X/7f;)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ">;",
            "Lcom/facebook/ads/redexgen/X/7f;",
            ")V"
        }
    .end annotation
.end method

.method public abstract AHF(Lcom/facebook/ads/redexgen/X/8E;Lcom/facebook/ads/redexgen/X/Ym;)V
.end method
