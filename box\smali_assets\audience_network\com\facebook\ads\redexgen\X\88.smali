.class public abstract Lcom/facebook/ads/redexgen/X/88;
.super Ljava/lang/Object;
.source ""


# static fields
.field public static final A00:Lcom/facebook/ads/redexgen/X/87;


# direct methods
.method public static constructor <clinit>()V
    .locals 1

    .line 694
    new-instance v0, Lcom/facebook/ads/redexgen/X/Yc;

    invoke-direct {v0}, Lcom/facebook/ads/redexgen/X/Yc;-><init>()V

    sput-object v0, Lcom/facebook/ads/redexgen/X/88;->A00:Lcom/facebook/ads/redexgen/X/87;

    return-void
.end method

.method public static A00()Lcom/facebook/ads/redexgen/X/87;
    .locals 1

    .line 17563
    sget-object v0, Lcom/facebook/ads/redexgen/X/88;->A00:Lcom/facebook/ads/redexgen/X/87;

    return-object v0
.end method
