.class public interface abstract Lcom/facebook/ads/redexgen/X/89;
.super Ljava/lang/Object;
.source ""


# virtual methods
.method public abstract A3c(Ljava/lang/Throwable;)V
.end method

.method public abstract A8l(Ljava/lang/String;)V
.end method

.method public abstract A9a(Ljava/lang/String;ILcom/facebook/ads/redexgen/X/8B;)V
.end method

.method public abstract A9b(Ljava/lang/String;ILcom/facebook/ads/redexgen/X/8B;)V
.end method

.method public abstract A9t(JJJJILjava/lang/Exception;)V
.end method

.method public abstract AA2(Ljava/lang/String;ILcom/facebook/ads/redexgen/X/8B;)V
.end method

.method public abstract AAA(Ljava/lang/String;ILcom/facebook/ads/redexgen/X/8B;)V
.end method

.method public abstract AAL()V
.end method
