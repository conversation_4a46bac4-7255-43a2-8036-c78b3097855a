.class public interface abstract Lcom/facebook/ads/redexgen/X/8Z;
.super Ljava/lang/Object;
.source ""


# virtual methods
.method public abstract A6E()Ljava/lang/String;
.end method

.method public abstract A6S()Ljava/lang/String;
.end method

.method public abstract A6k(Lcom/facebook/ads/redexgen/X/7f;)Lcom/facebook/ads/redexgen/X/7t;
.end method

.method public abstract A7Q()Ljava/lang/String;
.end method

.method public abstract A87()Ljava/lang/String;
.end method

.method public abstract A99()Z
.end method

.method public abstract A9E()Z
.end method

.method public abstract A9J()Ljava/lang/Boolean;
.end method

.method public abstract isTestMode(Landroid/content/Context;)Z
.end method
