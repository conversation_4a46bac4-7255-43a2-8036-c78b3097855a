.class public interface abstract Lcom/facebook/ads/redexgen/X/8k;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/facebook/ads/redexgen/X/8j;,
        Lcom/facebook/ads/redexgen/X/8i;
    }
.end annotation


# virtual methods
.method public abstract A6w(Lcom/facebook/ads/redexgen/X/8Z;)Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/facebook/ads/redexgen/X/8Z;",
            ")",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end method
