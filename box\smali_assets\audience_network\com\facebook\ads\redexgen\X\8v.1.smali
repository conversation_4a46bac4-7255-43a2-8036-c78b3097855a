.class public interface abstract Lcom/facebook/ads/redexgen/X/8v;
.super Ljava/lang/Object;
.source ""


# virtual methods
.method public abstract A4I(I)I
.end method

.method public abstract A53(Ljava/lang/String;)Z
.end method

.method public abstract A5y()Lorg/json/JSONArray;
.end method

.method public abstract A5z()Lorg/json/JSONObject;
.end method

.method public abstract A6z()I
.end method

.method public abstract A72(Ljava/lang/String;)Ljava/lang/String;
.end method

.method public abstract A89(I)Landroid/util/Pair;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I)",
            "Landroid/util/Pair<",
            "Lorg/json/JSONObject;",
            "Lorg/json/JSONArray;",
            ">;"
        }
    .end annotation
.end method

.method public abstract A8m(Ljava/lang/String;)Z
.end method
