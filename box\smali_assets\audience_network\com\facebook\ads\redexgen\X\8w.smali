.class public interface abstract Lcom/facebook/ads/redexgen/X/8w;
.super Ljava/lang/Object;
.source ""


# virtual methods
.method public abstract A4J()V
.end method

.method public abstract AHE(Lcom/facebook/ads/redexgen/X/Iz;Lcom/facebook/ads/redexgen/X/8t;)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/facebook/ads/redexgen/X/Iz;",
            "Lcom/facebook/ads/redexgen/X/8t<",
            "Ljava/lang/String;",
            ">;)V"
        }
    .end annotation
.end method
