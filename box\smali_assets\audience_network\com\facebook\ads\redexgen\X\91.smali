.class public final Lcom/facebook/ads/redexgen/X/91;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/facebook/ads/redexgen/X/90;
    }
.end annotation


# instance fields
.field public final A00:I

.field public final A01:I

.field public final A02:I

.field public final A03:Lcom/facebook/ads/redexgen/X/90;


# direct methods
.method public constructor <init>(Lcom/facebook/ads/redexgen/X/90;III)V
    .locals 0

    .line 19004
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 19005
    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/91;->A03:Lcom/facebook/ads/redexgen/X/90;

    .line 19006
    iput p2, p0, Lcom/facebook/ads/redexgen/X/91;->A01:I

    .line 19007
    iput p3, p0, Lcom/facebook/ads/redexgen/X/91;->A00:I

    .line 19008
    iput p4, p0, Lcom/facebook/ads/redexgen/X/91;->A02:I

    .line 19009
    return-void
.end method


# virtual methods
.method public final A00()I
    .locals 1

    .line 19010
    iget v0, p0, Lcom/facebook/ads/redexgen/X/91;->A00:I

    return v0
.end method

.method public final A01()I
    .locals 1

    .line 19011
    iget v0, p0, Lcom/facebook/ads/redexgen/X/91;->A01:I

    return v0
.end method

.method public final A02()I
    .locals 1

    .line 19012
    iget v0, p0, Lcom/facebook/ads/redexgen/X/91;->A02:I

    return v0
.end method

.method public final A03()Lcom/facebook/ads/redexgen/X/90;
    .locals 1

    .line 19013
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/91;->A03:Lcom/facebook/ads/redexgen/X/90;

    return-object v0
.end method
