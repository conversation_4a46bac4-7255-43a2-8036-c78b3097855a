.class public final Lcom/facebook/ads/redexgen/X/92;
.super Ljava/lang/Object;
.source ""


# instance fields
.field public final A00:I

.field public final A01:Lcom/facebook/ads/redexgen/X/91;


# direct methods
.method public constructor <init>(ILcom/facebook/ads/redexgen/X/91;)V
    .locals 0

    .line 19014
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 19015
    iput p1, p0, Lcom/facebook/ads/redexgen/X/92;->A00:I

    .line 19016
    iput-object p2, p0, Lcom/facebook/ads/redexgen/X/92;->A01:Lcom/facebook/ads/redexgen/X/91;

    .line 19017
    return-void
.end method


# virtual methods
.method public final A00()I
    .locals 1

    .line 19018
    iget v0, p0, Lcom/facebook/ads/redexgen/X/92;->A00:I

    return v0
.end method

.method public final A01()Lcom/facebook/ads/redexgen/X/91;
    .locals 1

    .line 19019
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/92;->A01:Lcom/facebook/ads/redexgen/X/91;

    return-object v0
.end method
