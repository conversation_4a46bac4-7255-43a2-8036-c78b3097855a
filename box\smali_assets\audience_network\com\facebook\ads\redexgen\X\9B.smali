.class public final Lcom/facebook/ads/redexgen/X/9B;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/facebook/ads/redexgen/X/YA;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "FetchLocation"
.end annotation


# instance fields
.field public final A00:I

.field public final A01:I

.field public final A02:Lcom/facebook/ads/redexgen/X/8z;


# direct methods
.method public constructor <init>(Lcom/facebook/ads/redexgen/X/8z;II)V
    .locals 0

    .line 19216
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 19217
    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/9B;->A02:Lcom/facebook/ads/redexgen/X/8z;

    .line 19218
    iput p2, p0, Lcom/facebook/ads/redexgen/X/9B;->A01:I

    .line 19219
    iput p3, p0, Lcom/facebook/ads/redexgen/X/9B;->A00:I

    .line 19220
    return-void
.end method
