.class public final Lcom/facebook/ads/redexgen/X/9I;
.super Lcom/facebook/ads/redexgen/X/QS;
.source ""


# instance fields
.field public A00:I

.field public A01:Z

.field public final A02:Lcom/facebook/ads/redexgen/X/8s;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/facebook/ads/redexgen/X/8s<",
            "Lcom/facebook/ads/redexgen/X/9H;",
            ">;"
        }
    .end annotation
.end field

.field public final A03:Lcom/facebook/ads/redexgen/X/8s;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/facebook/ads/redexgen/X/8s<",
            "Lcom/facebook/ads/redexgen/X/98;",
            ">;"
        }
    .end annotation
.end field

.field public final A04:Lcom/facebook/ads/redexgen/X/8s;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/facebook/ads/redexgen/X/8s<",
            "Lcom/facebook/ads/redexgen/X/O8;",
            ">;"
        }
    .end annotation
.end field

.field public final A05:Lcom/facebook/ads/redexgen/X/8s;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/facebook/ads/redexgen/X/8s<",
            "Lcom/facebook/ads/redexgen/X/93;",
            ">;"
        }
    .end annotation
.end field

.field public final A06:Lcom/facebook/ads/redexgen/X/8s;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/facebook/ads/redexgen/X/8s<",
            "Lcom/facebook/ads/redexgen/X/Mr;",
            ">;"
        }
    .end annotation
.end field

.field public final A07:Lcom/facebook/ads/redexgen/X/8s;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/facebook/ads/redexgen/X/8s<",
            "Lcom/facebook/ads/redexgen/X/Mf;",
            ">;"
        }
    .end annotation
.end field

.field public final A08:Lcom/facebook/ads/redexgen/X/8s;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/facebook/ads/redexgen/X/8s<",
            "Lcom/facebook/ads/redexgen/X/Ma;",
            ">;"
        }
    .end annotation
.end field

.field public final A09:Lcom/facebook/ads/redexgen/X/8s;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/facebook/ads/redexgen/X/8s<",
            "Lcom/facebook/ads/redexgen/X/MU;",
            ">;"
        }
    .end annotation
.end field

.field public final A0A:Lcom/facebook/ads/redexgen/X/8s;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/facebook/ads/redexgen/X/8s<",
            "Lcom/facebook/ads/redexgen/X/Lu;",
            ">;"
        }
    .end annotation
.end field

.field public final A0B:Lcom/facebook/ads/redexgen/X/SA;

.field public final A0C:Lcom/facebook/ads/redexgen/X/NX;

.field public final A0D:Lcom/facebook/ads/redexgen/X/MV;


# direct methods
.method public constructor <init>(Lcom/facebook/ads/redexgen/X/Yn;Lcom/facebook/ads/redexgen/X/J2;Lcom/facebook/ads/redexgen/X/SA;Ljava/lang/String;)V
    .locals 6

    .line 19376
    new-instance v4, Ljava/util/ArrayList;

    invoke-direct {v4}, Ljava/util/ArrayList;-><init>()V

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    move-object v3, p3

    move-object v5, p4

    invoke-direct/range {v0 .. v5}, Lcom/facebook/ads/redexgen/X/9I;-><init>(Lcom/facebook/ads/redexgen/X/Yn;Lcom/facebook/ads/redexgen/X/J2;Lcom/facebook/ads/redexgen/X/SA;Ljava/util/List;Ljava/lang/String;)V

    .line 19377
    return-void
.end method

.method public constructor <init>(Lcom/facebook/ads/redexgen/X/Yn;Lcom/facebook/ads/redexgen/X/J2;Lcom/facebook/ads/redexgen/X/SA;Ljava/lang/String;Landroid/os/Bundle;)V
    .locals 8

    .line 19378
    new-instance v4, Ljava/util/ArrayList;

    invoke-direct {v4}, Ljava/util/ArrayList;-><init>()V

    const/4 v7, 0x0

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    move-object v3, p3

    move-object v5, p4

    move-object v6, p5

    invoke-direct/range {v0 .. v7}, Lcom/facebook/ads/redexgen/X/9I;-><init>(Lcom/facebook/ads/redexgen/X/Yn;Lcom/facebook/ads/redexgen/X/J2;Lcom/facebook/ads/redexgen/X/SA;Ljava/util/List;Ljava/lang/String;Landroid/os/Bundle;Ljava/util/Map;)V

    .line 19379
    return-void
.end method

.method public constructor <init>(Lcom/facebook/ads/redexgen/X/Yn;Lcom/facebook/ads/redexgen/X/J2;Lcom/facebook/ads/redexgen/X/SA;Ljava/lang/String;Ljava/util/Map;)V
    .locals 8
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/facebook/ads/redexgen/X/Yn;",
            "Lcom/facebook/ads/redexgen/X/J2;",
            "Lcom/facebook/ads/redexgen/X/SA;",
            "Ljava/lang/String;",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ">;)V"
        }
    .end annotation

    .line 19380
    .local p5, "extraParams":Ljava/util/Map;, "Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"
    new-instance v4, Ljava/util/ArrayList;

    invoke-direct {v4}, Ljava/util/ArrayList;-><init>()V

    const/4 v6, 0x0

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    move-object v3, p3

    move-object v5, p4

    move-object v7, p5

    invoke-direct/range {v0 .. v7}, Lcom/facebook/ads/redexgen/X/9I;-><init>(Lcom/facebook/ads/redexgen/X/Yn;Lcom/facebook/ads/redexgen/X/J2;Lcom/facebook/ads/redexgen/X/SA;Ljava/util/List;Ljava/lang/String;Landroid/os/Bundle;Ljava/util/Map;)V

    .line 19381
    return-void
.end method

.method public constructor <init>(Lcom/facebook/ads/redexgen/X/Yn;Lcom/facebook/ads/redexgen/X/J2;Lcom/facebook/ads/redexgen/X/SA;Ljava/util/List;Ljava/lang/String;)V
    .locals 14
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/facebook/ads/redexgen/X/Yn;",
            "Lcom/facebook/ads/redexgen/X/J2;",
            "Lcom/facebook/ads/redexgen/X/SA;",
            "Ljava/util/List<",
            "Lcom/facebook/ads/redexgen/X/2P;",
            ">;",
            "Ljava/lang/String;",
            ")V"
        }
    .end annotation

    .line 19382
    .local p6, "adQualityRules":Ljava/util/List;, "Ljava/util/List<Lcom/facebook/ads/internal/adquality/AdQualityRule;>;"
    move-object v1, p0

    .line 19383
    move-object/from16 v2, p3

    invoke-virtual {v2}, Lcom/facebook/ads/redexgen/X/SA;->A0h()Z

    move-result v0

    xor-int/lit8 v9, v0, 0x1

    new-instance v10, Lcom/facebook/ads/redexgen/X/QR;

    move-object v4, p1

    invoke-direct {v10, v4, v2}, Lcom/facebook/ads/redexgen/X/QR;-><init>(Lcom/facebook/ads/redexgen/X/Yn;Landroid/view/View;)V

    .line 19384
    move-object v3, p0

    move-object v6, v2

    move-object/from16 v5, p2

    move-object/from16 v7, p4

    move-object/from16 v8, p5

    invoke-direct/range {v3 .. v10}, Lcom/facebook/ads/redexgen/X/QS;-><init>(Lcom/facebook/ads/redexgen/X/Yn;Lcom/facebook/ads/redexgen/X/J2;Lcom/facebook/ads/redexgen/X/QE;Ljava/util/List;Ljava/lang/String;ZLcom/facebook/ads/redexgen/X/QK;)V

    .line 19385
    new-instance v13, Lcom/facebook/ads/redexgen/X/9J;

    invoke-direct {v13, v1}, Lcom/facebook/ads/redexgen/X/9J;-><init>(Lcom/facebook/ads/redexgen/X/9I;)V

    iput-object v13, v1, Lcom/facebook/ads/redexgen/X/9I;->A0D:Lcom/facebook/ads/redexgen/X/MV;

    .line 19386
    new-instance v12, Lcom/facebook/ads/redexgen/X/S7;

    invoke-direct {v12, v1}, Lcom/facebook/ads/redexgen/X/S7;-><init>(Lcom/facebook/ads/redexgen/X/9I;)V

    iput-object v12, v1, Lcom/facebook/ads/redexgen/X/9I;->A07:Lcom/facebook/ads/redexgen/X/8s;

    .line 19387
    new-instance v11, Lcom/facebook/ads/redexgen/X/S3;

    invoke-direct {v11, v1}, Lcom/facebook/ads/redexgen/X/S3;-><init>(Lcom/facebook/ads/redexgen/X/9I;)V

    iput-object v11, v1, Lcom/facebook/ads/redexgen/X/9I;->A03:Lcom/facebook/ads/redexgen/X/8s;

    .line 19388
    new-instance v10, Lcom/facebook/ads/redexgen/X/RH;

    invoke-direct {v10, v1}, Lcom/facebook/ads/redexgen/X/RH;-><init>(Lcom/facebook/ads/redexgen/X/9I;)V

    iput-object v10, v1, Lcom/facebook/ads/redexgen/X/9I;->A04:Lcom/facebook/ads/redexgen/X/8s;

    .line 19389
    new-instance v9, Lcom/facebook/ads/redexgen/X/RA;

    invoke-direct {v9, v1}, Lcom/facebook/ads/redexgen/X/RA;-><init>(Lcom/facebook/ads/redexgen/X/9I;)V

    iput-object v9, v1, Lcom/facebook/ads/redexgen/X/9I;->A05:Lcom/facebook/ads/redexgen/X/8s;

    .line 19390
    new-instance v8, Lcom/facebook/ads/redexgen/X/Qd;

    invoke-direct {v8, v1}, Lcom/facebook/ads/redexgen/X/Qd;-><init>(Lcom/facebook/ads/redexgen/X/9I;)V

    iput-object v8, v1, Lcom/facebook/ads/redexgen/X/9I;->A02:Lcom/facebook/ads/redexgen/X/8s;

    .line 19391
    new-instance v7, Lcom/facebook/ads/redexgen/X/Qc;

    invoke-direct {v7, v1}, Lcom/facebook/ads/redexgen/X/Qc;-><init>(Lcom/facebook/ads/redexgen/X/9I;)V

    iput-object v7, v1, Lcom/facebook/ads/redexgen/X/9I;->A06:Lcom/facebook/ads/redexgen/X/8s;

    .line 19392
    new-instance v6, Lcom/facebook/ads/redexgen/X/Qa;

    invoke-direct {v6, v1}, Lcom/facebook/ads/redexgen/X/Qa;-><init>(Lcom/facebook/ads/redexgen/X/9I;)V

    iput-object v6, v1, Lcom/facebook/ads/redexgen/X/9I;->A09:Lcom/facebook/ads/redexgen/X/8s;

    .line 19393
    new-instance v5, Lcom/facebook/ads/redexgen/X/QV;

    invoke-direct {v5, v1}, Lcom/facebook/ads/redexgen/X/QV;-><init>(Lcom/facebook/ads/redexgen/X/9I;)V

    iput-object v5, v1, Lcom/facebook/ads/redexgen/X/9I;->A0A:Lcom/facebook/ads/redexgen/X/8s;

    .line 19394
    new-instance v4, Lcom/facebook/ads/redexgen/X/S9;

    invoke-direct {v4, v1}, Lcom/facebook/ads/redexgen/X/S9;-><init>(Lcom/facebook/ads/redexgen/X/9I;)V

    iput-object v4, v1, Lcom/facebook/ads/redexgen/X/9I;->A08:Lcom/facebook/ads/redexgen/X/8s;

    .line 19395
    new-instance v3, Lcom/facebook/ads/redexgen/X/9K;

    invoke-direct {v3, v1}, Lcom/facebook/ads/redexgen/X/9K;-><init>(Lcom/facebook/ads/redexgen/X/9I;)V

    iput-object v3, v1, Lcom/facebook/ads/redexgen/X/9I;->A0C:Lcom/facebook/ads/redexgen/X/NX;

    .line 19396
    const/4 v0, 0x0

    iput-boolean v0, v1, Lcom/facebook/ads/redexgen/X/9I;->A01:Z

    .line 19397
    iput-object v2, v1, Lcom/facebook/ads/redexgen/X/9I;->A0B:Lcom/facebook/ads/redexgen/X/SA;

    .line 19398
    invoke-virtual {v2}, Lcom/facebook/ads/redexgen/X/SA;->getEventBus()Lcom/facebook/ads/redexgen/X/8r;

    move-result-object v2

    const/16 v1, 0xb

    new-array v1, v1, [Lcom/facebook/ads/redexgen/X/8s;

    aput-object v13, v1, v0

    const/4 v0, 0x1

    aput-object v9, v1, v0

    const/4 v0, 0x2

    aput-object v12, v1, v0

    const/4 v0, 0x3

    aput-object v10, v1, v0

    const/4 v0, 0x4

    aput-object v11, v1, v0

    const/4 v0, 0x5

    aput-object v8, v1, v0

    const/4 v0, 0x6

    aput-object v7, v1, v0

    const/4 v0, 0x7

    aput-object v6, v1, v0

    const/16 v0, 0x8

    aput-object v5, v1, v0

    const/16 v0, 0x9

    aput-object v3, v1, v0

    const/16 v0, 0xa

    aput-object v4, v1, v0

    .line 19399
    invoke-virtual {v2, v1}, Lcom/facebook/ads/redexgen/X/8r;->A03([Lcom/facebook/ads/redexgen/X/8s;)V

    .line 19400
    return-void
.end method

.method public constructor <init>(Lcom/facebook/ads/redexgen/X/Yn;Lcom/facebook/ads/redexgen/X/J2;Lcom/facebook/ads/redexgen/X/SA;Ljava/util/List;Ljava/lang/String;Landroid/os/Bundle;Ljava/util/Map;)V
    .locals 14
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/facebook/ads/redexgen/X/Yn;",
            "Lcom/facebook/ads/redexgen/X/J2;",
            "Lcom/facebook/ads/redexgen/X/SA;",
            "Ljava/util/List<",
            "Lcom/facebook/ads/redexgen/X/2P;",
            ">;",
            "Ljava/lang/String;",
            "Landroid/os/Bundle;",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ">;)V"
        }
    .end annotation

    .line 19401
    .local p6, "adQualityRules":Ljava/util/List;, "Ljava/util/List<Lcom/facebook/ads/internal/adquality/AdQualityRule;>;"
    .local p9, "extraParams":Ljava/util/Map;, "Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"
    move-object v0, p0

    .line 19402
    move-object/from16 v1, p3

    invoke-virtual {v1}, Lcom/facebook/ads/redexgen/X/SA;->A0h()Z

    move-result v2

    xor-int/lit8 v8, v2, 0x1

    new-instance v11, Lcom/facebook/ads/redexgen/X/QR;

    move-object v3, p1

    invoke-direct {v11, v3, v1}, Lcom/facebook/ads/redexgen/X/QR;-><init>(Lcom/facebook/ads/redexgen/X/Yn;Landroid/view/View;)V

    .line 19403
    move-object v2, p0

    move-object v5, v1

    move-object/from16 v4, p2

    move-object/from16 v6, p4

    move-object/from16 v7, p5

    move-object/from16 v9, p6

    move-object/from16 v10, p7

    invoke-direct/range {v2 .. v11}, Lcom/facebook/ads/redexgen/X/QS;-><init>(Lcom/facebook/ads/redexgen/X/Yn;Lcom/facebook/ads/redexgen/X/J2;Lcom/facebook/ads/redexgen/X/QE;Ljava/util/List;Ljava/lang/String;ZLandroid/os/Bundle;Ljava/util/Map;Lcom/facebook/ads/redexgen/X/QK;)V

    .line 19404
    new-instance v13, Lcom/facebook/ads/redexgen/X/9J;

    invoke-direct {v13, v0}, Lcom/facebook/ads/redexgen/X/9J;-><init>(Lcom/facebook/ads/redexgen/X/9I;)V

    iput-object v13, v0, Lcom/facebook/ads/redexgen/X/9I;->A0D:Lcom/facebook/ads/redexgen/X/MV;

    .line 19405
    new-instance v12, Lcom/facebook/ads/redexgen/X/S7;

    invoke-direct {v12, v0}, Lcom/facebook/ads/redexgen/X/S7;-><init>(Lcom/facebook/ads/redexgen/X/9I;)V

    iput-object v12, v0, Lcom/facebook/ads/redexgen/X/9I;->A07:Lcom/facebook/ads/redexgen/X/8s;

    .line 19406
    new-instance v11, Lcom/facebook/ads/redexgen/X/S3;

    invoke-direct {v11, v0}, Lcom/facebook/ads/redexgen/X/S3;-><init>(Lcom/facebook/ads/redexgen/X/9I;)V

    iput-object v11, v0, Lcom/facebook/ads/redexgen/X/9I;->A03:Lcom/facebook/ads/redexgen/X/8s;

    .line 19407
    new-instance v10, Lcom/facebook/ads/redexgen/X/RH;

    invoke-direct {v10, v0}, Lcom/facebook/ads/redexgen/X/RH;-><init>(Lcom/facebook/ads/redexgen/X/9I;)V

    iput-object v10, v0, Lcom/facebook/ads/redexgen/X/9I;->A04:Lcom/facebook/ads/redexgen/X/8s;

    .line 19408
    new-instance v9, Lcom/facebook/ads/redexgen/X/RA;

    invoke-direct {v9, v0}, Lcom/facebook/ads/redexgen/X/RA;-><init>(Lcom/facebook/ads/redexgen/X/9I;)V

    iput-object v9, v0, Lcom/facebook/ads/redexgen/X/9I;->A05:Lcom/facebook/ads/redexgen/X/8s;

    .line 19409
    new-instance v8, Lcom/facebook/ads/redexgen/X/Qd;

    invoke-direct {v8, v0}, Lcom/facebook/ads/redexgen/X/Qd;-><init>(Lcom/facebook/ads/redexgen/X/9I;)V

    iput-object v8, v0, Lcom/facebook/ads/redexgen/X/9I;->A02:Lcom/facebook/ads/redexgen/X/8s;

    .line 19410
    new-instance v7, Lcom/facebook/ads/redexgen/X/Qc;

    invoke-direct {v7, v0}, Lcom/facebook/ads/redexgen/X/Qc;-><init>(Lcom/facebook/ads/redexgen/X/9I;)V

    iput-object v7, v0, Lcom/facebook/ads/redexgen/X/9I;->A06:Lcom/facebook/ads/redexgen/X/8s;

    .line 19411
    new-instance v6, Lcom/facebook/ads/redexgen/X/Qa;

    invoke-direct {v6, v0}, Lcom/facebook/ads/redexgen/X/Qa;-><init>(Lcom/facebook/ads/redexgen/X/9I;)V

    iput-object v6, v0, Lcom/facebook/ads/redexgen/X/9I;->A09:Lcom/facebook/ads/redexgen/X/8s;

    .line 19412
    new-instance v5, Lcom/facebook/ads/redexgen/X/QV;

    invoke-direct {v5, v0}, Lcom/facebook/ads/redexgen/X/QV;-><init>(Lcom/facebook/ads/redexgen/X/9I;)V

    iput-object v5, v0, Lcom/facebook/ads/redexgen/X/9I;->A0A:Lcom/facebook/ads/redexgen/X/8s;

    .line 19413
    new-instance v4, Lcom/facebook/ads/redexgen/X/S9;

    invoke-direct {v4, v0}, Lcom/facebook/ads/redexgen/X/S9;-><init>(Lcom/facebook/ads/redexgen/X/9I;)V

    iput-object v4, v0, Lcom/facebook/ads/redexgen/X/9I;->A08:Lcom/facebook/ads/redexgen/X/8s;

    .line 19414
    new-instance v2, Lcom/facebook/ads/redexgen/X/9K;

    invoke-direct {v2, v0}, Lcom/facebook/ads/redexgen/X/9K;-><init>(Lcom/facebook/ads/redexgen/X/9I;)V

    iput-object v2, v0, Lcom/facebook/ads/redexgen/X/9I;->A0C:Lcom/facebook/ads/redexgen/X/NX;

    .line 19415
    const/4 v3, 0x0

    iput-boolean v3, v0, Lcom/facebook/ads/redexgen/X/9I;->A01:Z

    .line 19416
    iput-object v1, v0, Lcom/facebook/ads/redexgen/X/9I;->A0B:Lcom/facebook/ads/redexgen/X/SA;

    .line 19417
    invoke-virtual {v1}, Lcom/facebook/ads/redexgen/X/SA;->getEventBus()Lcom/facebook/ads/redexgen/X/8r;

    move-result-object v2

    const/16 v0, 0xa

    new-array v1, v0, [Lcom/facebook/ads/redexgen/X/8s;

    aput-object v13, v1, v3

    const/4 v0, 0x1

    aput-object v9, v1, v0

    const/4 v0, 0x2

    aput-object v12, v1, v0

    const/4 v0, 0x3

    aput-object v10, v1, v0

    const/4 v0, 0x4

    aput-object v11, v1, v0

    const/4 v0, 0x5

    aput-object v8, v1, v0

    const/4 v0, 0x6

    aput-object v7, v1, v0

    const/4 v0, 0x7

    aput-object v6, v1, v0

    const/16 v0, 0x8

    aput-object v5, v1, v0

    const/16 v0, 0x9

    aput-object v4, v1, v0

    .line 19418
    invoke-virtual {v2, v1}, Lcom/facebook/ads/redexgen/X/8r;->A03([Lcom/facebook/ads/redexgen/X/8s;)V

    .line 19419
    return-void
.end method

.method public static synthetic A00(Lcom/facebook/ads/redexgen/X/9I;)Lcom/facebook/ads/redexgen/X/8s;
    .locals 0

    .line 19420
    iget-object p0, p0, Lcom/facebook/ads/redexgen/X/9I;->A0A:Lcom/facebook/ads/redexgen/X/8s;

    return-object p0
.end method

.method public static synthetic A01(Lcom/facebook/ads/redexgen/X/9I;)Lcom/facebook/ads/redexgen/X/8s;
    .locals 0

    .line 19421
    iget-object p0, p0, Lcom/facebook/ads/redexgen/X/9I;->A08:Lcom/facebook/ads/redexgen/X/8s;

    return-object p0
.end method

.method public static synthetic A02(Lcom/facebook/ads/redexgen/X/9I;)Lcom/facebook/ads/redexgen/X/8s;
    .locals 0

    .line 19422
    iget-object p0, p0, Lcom/facebook/ads/redexgen/X/9I;->A05:Lcom/facebook/ads/redexgen/X/8s;

    return-object p0
.end method

.method public static synthetic A03(Lcom/facebook/ads/redexgen/X/9I;)Lcom/facebook/ads/redexgen/X/8s;
    .locals 0

    .line 19423
    iget-object p0, p0, Lcom/facebook/ads/redexgen/X/9I;->A07:Lcom/facebook/ads/redexgen/X/8s;

    return-object p0
.end method

.method public static synthetic A04(Lcom/facebook/ads/redexgen/X/9I;)Lcom/facebook/ads/redexgen/X/8s;
    .locals 0

    .line 19424
    iget-object p0, p0, Lcom/facebook/ads/redexgen/X/9I;->A04:Lcom/facebook/ads/redexgen/X/8s;

    return-object p0
.end method

.method public static synthetic A05(Lcom/facebook/ads/redexgen/X/9I;)Lcom/facebook/ads/redexgen/X/8s;
    .locals 0

    .line 19425
    iget-object p0, p0, Lcom/facebook/ads/redexgen/X/9I;->A03:Lcom/facebook/ads/redexgen/X/8s;

    return-object p0
.end method

.method public static synthetic A06(Lcom/facebook/ads/redexgen/X/9I;)Lcom/facebook/ads/redexgen/X/8s;
    .locals 0

    .line 19426
    iget-object p0, p0, Lcom/facebook/ads/redexgen/X/9I;->A02:Lcom/facebook/ads/redexgen/X/8s;

    return-object p0
.end method

.method public static synthetic A07(Lcom/facebook/ads/redexgen/X/9I;)Lcom/facebook/ads/redexgen/X/8s;
    .locals 0

    .line 19427
    iget-object p0, p0, Lcom/facebook/ads/redexgen/X/9I;->A06:Lcom/facebook/ads/redexgen/X/8s;

    return-object p0
.end method

.method public static synthetic A08(Lcom/facebook/ads/redexgen/X/9I;)Lcom/facebook/ads/redexgen/X/8s;
    .locals 0

    .line 19428
    iget-object p0, p0, Lcom/facebook/ads/redexgen/X/9I;->A09:Lcom/facebook/ads/redexgen/X/8s;

    return-object p0
.end method

.method public static synthetic A09(Lcom/facebook/ads/redexgen/X/9I;)Lcom/facebook/ads/redexgen/X/SA;
    .locals 0

    .line 19429
    iget-object p0, p0, Lcom/facebook/ads/redexgen/X/9I;->A0B:Lcom/facebook/ads/redexgen/X/SA;

    return-object p0
.end method

.method public static synthetic A0A(Lcom/facebook/ads/redexgen/X/9I;)Lcom/facebook/ads/redexgen/X/NX;
    .locals 0

    .line 19430
    iget-object p0, p0, Lcom/facebook/ads/redexgen/X/9I;->A0C:Lcom/facebook/ads/redexgen/X/NX;

    return-object p0
.end method

.method public static synthetic A0B(Lcom/facebook/ads/redexgen/X/9I;)Lcom/facebook/ads/redexgen/X/MV;
    .locals 0

    .line 19431
    iget-object p0, p0, Lcom/facebook/ads/redexgen/X/9I;->A0D:Lcom/facebook/ads/redexgen/X/MV;

    return-object p0
.end method

.method public static synthetic A0C(Lcom/facebook/ads/redexgen/X/9I;)Z
    .locals 0

    .line 19432
    iget-boolean p0, p0, Lcom/facebook/ads/redexgen/X/9I;->A01:Z

    return p0
.end method

.method public static synthetic A0D(Lcom/facebook/ads/redexgen/X/9I;Z)Z
    .locals 0

    .line 19433
    iput-boolean p1, p0, Lcom/facebook/ads/redexgen/X/9I;->A01:Z

    return p1
.end method


# virtual methods
.method public final A0i()V
    .locals 2

    .line 19434
    new-instance v1, Lcom/facebook/ads/redexgen/X/S8;

    invoke-direct {v1, p0}, Lcom/facebook/ads/redexgen/X/S8;-><init>(Lcom/facebook/ads/redexgen/X/9I;)V

    .line 19435
    .local v0, "unregisterRunnable":Lcom/facebook/ads/redexgen/X/KT;
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/9I;->A0B:Lcom/facebook/ads/redexgen/X/SA;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/SA;->A0l()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 19436
    invoke-static {v1}, Lcom/facebook/ads/redexgen/X/Li;->A00(Ljava/lang/Runnable;)V

    .line 19437
    :goto_0
    return-void

    .line 19438
    :cond_0
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/9I;->A0B:Lcom/facebook/ads/redexgen/X/SA;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/SA;->getStateHandler()Landroid/os/Handler;

    move-result-object v0

    invoke-virtual {v0, v1}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    goto :goto_0
.end method
