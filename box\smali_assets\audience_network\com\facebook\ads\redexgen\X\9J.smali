.class public final Lcom/facebook/ads/redexgen/X/9J;
.super Lcom/facebook/ads/redexgen/X/MV;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/facebook/ads/redexgen/X/9I;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field public final synthetic A00:Lcom/facebook/ads/redexgen/X/9I;


# direct methods
.method public constructor <init>(Lcom/facebook/ads/redexgen/X/9I;)V
    .locals 0

    .line 19439
    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/9J;->A00:Lcom/facebook/ads/redexgen/X/9I;

    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/MV;-><init>()V

    return-void
.end method

.method private final A00(Lcom/facebook/ads/redexgen/X/MW;)V
    .locals 1

    .line 19440
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/9J;->A00:Lcom/facebook/ads/redexgen/X/9I;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/QS;->A0a()V

    .line 19441
    return-void
.end method


# virtual methods
.method public final bridge synthetic A03(Lcom/facebook/ads/redexgen/X/8q;)V
    .locals 0

    .line 19442
    check-cast p1, Lcom/facebook/ads/redexgen/X/MW;

    invoke-direct {p0, p1}, Lcom/facebook/ads/redexgen/X/9J;->A00(Lcom/facebook/ads/redexgen/X/MW;)V

    return-void
.end method
