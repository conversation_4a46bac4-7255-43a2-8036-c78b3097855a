.class public final Lcom/facebook/ads/redexgen/X/9M;
.super Lcom/facebook/ads/redexgen/X/On;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/facebook/ads/redexgen/X/SF;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field public final synthetic A00:Lcom/facebook/ads/redexgen/X/SF;


# direct methods
.method public constructor <init>(Lcom/facebook/ads/redexgen/X/SF;)V
    .locals 0

    .line 19452
    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/9M;->A00:Lcom/facebook/ads/redexgen/X/SF;

    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/On;-><init>()V

    return-void
.end method

.method private final A00(Lcom/facebook/ads/redexgen/X/98;)V
    .locals 1

    .line 19453
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/9M;->A00:Lcom/facebook/ads/redexgen/X/SF;

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/SF;->A00(Lcom/facebook/ads/redexgen/X/SF;)Lcom/facebook/ads/redexgen/X/Yn;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/Yn;->A0E()Lcom/facebook/ads/redexgen/X/0S;

    move-result-object v0

    invoke-interface {v0}, Lcom/facebook/ads/redexgen/X/0S;->AFb()V

    .line 19454
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/9M;->A00:Lcom/facebook/ads/redexgen/X/SF;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/SF;->A0D()V

    .line 19455
    return-void
.end method


# virtual methods
.method public final bridge synthetic A03(Lcom/facebook/ads/redexgen/X/8q;)V
    .locals 0

    .line 19456
    check-cast p1, Lcom/facebook/ads/redexgen/X/98;

    invoke-direct {p0, p1}, Lcom/facebook/ads/redexgen/X/9M;->A00(Lcom/facebook/ads/redexgen/X/98;)V

    return-void
.end method
