.class public final Lcom/facebook/ads/redexgen/X/9P;
.super Lcom/facebook/ads/redexgen/X/UL;
.source ""


# static fields
.field public static A0F:[B

.field public static A0G:[Ljava/lang/String;

.field public static final A0H:I

.field public static final A0I:I

.field public static final A0J:I

.field public static final A0K:I

.field public static final A0L:I


# instance fields
.field public A00:I

.field public A01:I

.field public A02:Landroid/widget/LinearLayout;

.field public A03:Lcom/facebook/ads/redexgen/X/b5;

.field public A04:Lcom/facebook/ads/redexgen/X/6c;

.field public A05:Lcom/facebook/ads/redexgen/X/3r;

.field public A06:Lcom/facebook/ads/redexgen/X/O2;

.field public A07:Lcom/facebook/ads/redexgen/X/Sb;

.field public A08:Lcom/facebook/ads/redexgen/X/RD;

.field public A09:Lcom/facebook/ads/redexgen/X/RE;

.field public A0A:Ljava/lang/String;

.field public A0B:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lcom/facebook/ads/redexgen/X/Pp;",
            ">;"
        }
    .end annotation
.end field

.field public A0C:Z

.field public final A0D:Lcom/facebook/ads/redexgen/X/Yn;

.field public final A0E:Lcom/facebook/ads/redexgen/X/Lg;


# direct methods
.method public static constructor <clinit>()V
    .locals 3

    .line 913
    const/16 v0, 0x8

    new-array v2, v0, [Ljava/lang/String;

    const/4 v1, 0x0

    const-string v0, "hEO5MspmmQwWZDjfO9hYkGtE8t8ljCUN"

    aput-object v0, v2, v1

    const/4 v1, 0x1

    const-string v0, "895GOd6GqHS2SDNwtQvAqIE"

    aput-object v0, v2, v1

    const/4 v1, 0x2

    const-string v0, "zBXblOu1CdgXx49mdyV8flA"

    aput-object v0, v2, v1

    const/4 v1, 0x3

    const-string v0, "GxqIbNjcrUQMSJiuMkyJv1mwkudVYaGP"

    aput-object v0, v2, v1

    const/4 v1, 0x4

    const-string v0, "be3ZgXhlaup3yXbKLr41yKjcTusIQNjx"

    aput-object v0, v2, v1

    const/4 v1, 0x5

    const-string v0, "HLi8FTj59ZkYUe8FmXrpO0CEflvtIm0o"

    aput-object v0, v2, v1

    const/4 v1, 0x6

    const-string v0, "7bereGXxKpIS6kk2UNHV0kt11i4QSfeP"

    aput-object v0, v2, v1

    const/4 v1, 0x7

    const-string v0, "LRvrjmvhZNKPrr4qRukG9JYrfPQqD7wd"

    aput-object v0, v2, v1

    sput-object v2, Lcom/facebook/ads/redexgen/X/9P;->A0G:[Ljava/lang/String;

    invoke-static {}, Lcom/facebook/ads/redexgen/X/9P;->A0B()V

    const/high16 v1, 0x42400000    # 48.0f

    sget v0, Lcom/facebook/ads/redexgen/X/LD;->A02:F

    mul-float/2addr v0, v1

    float-to-int v0, v0

    sput v0, Lcom/facebook/ads/redexgen/X/9P;->A0H:I

    .line 914
    sget v0, Lcom/facebook/ads/redexgen/X/LD;->A02:F

    const/high16 v1, 0x41000000    # 8.0f

    mul-float/2addr v0, v1

    float-to-int v0, v0

    sput v0, Lcom/facebook/ads/redexgen/X/9P;->A0I:I

    .line 915
    sget v0, Lcom/facebook/ads/redexgen/X/LD;->A02:F

    mul-float/2addr v0, v1

    float-to-int v0, v0

    sput v0, Lcom/facebook/ads/redexgen/X/9P;->A0J:I

    .line 916
    const/high16 v1, 0x42600000    # 56.0f

    sget v0, Lcom/facebook/ads/redexgen/X/LD;->A02:F

    mul-float/2addr v0, v1

    float-to-int v0, v0

    sput v0, Lcom/facebook/ads/redexgen/X/9P;->A0L:I

    .line 917
    const/high16 v1, 0x41400000    # 12.0f

    sget v0, Lcom/facebook/ads/redexgen/X/LD;->A02:F

    mul-float/2addr v0, v1

    float-to-int v0, v0

    sput v0, Lcom/facebook/ads/redexgen/X/9P;->A0K:I

    return-void
.end method

.method public constructor <init>(Lcom/facebook/ads/redexgen/X/Yn;Lcom/facebook/ads/redexgen/X/J2;Lcom/facebook/ads/redexgen/X/6c;Lcom/facebook/ads/redexgen/X/MC;Lcom/facebook/ads/redexgen/X/FS;)V
    .locals 3

    .line 19481
    invoke-direct {p0, p1, p2, p4, p5}, Lcom/facebook/ads/redexgen/X/UL;-><init>(Lcom/facebook/ads/redexgen/X/Yn;Lcom/facebook/ads/redexgen/X/J2;Lcom/facebook/ads/redexgen/X/MC;Lcom/facebook/ads/redexgen/X/b5;)V

    .line 19482
    new-instance v0, Lcom/facebook/ads/redexgen/X/Lg;

    invoke-direct {v0}, Lcom/facebook/ads/redexgen/X/Lg;-><init>()V

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/9P;->A0E:Lcom/facebook/ads/redexgen/X/Lg;

    .line 19483
    const/4 v0, 0x0

    iput-boolean v0, p0, Lcom/facebook/ads/redexgen/X/9P;->A0C:Z

    .line 19484
    const/4 v0, 0x1

    iput-boolean v0, p0, Lcom/facebook/ads/redexgen/X/UL;->A07:Z

    .line 19485
    new-instance v2, Lcom/facebook/ads/redexgen/X/U8;

    invoke-direct {v2}, Lcom/facebook/ads/redexgen/X/U8;-><init>()V

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/UL;->A0A:Lcom/facebook/ads/redexgen/X/b5;

    .line 19486
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/1F;->A0T()Ljava/lang/String;

    move-result-object v1

    new-instance v0, Lcom/facebook/ads/redexgen/X/Q7;

    invoke-direct {v0, p1, v2, v1, p4}, Lcom/facebook/ads/redexgen/X/Q7;-><init>(Lcom/facebook/ads/redexgen/X/Yn;Lcom/facebook/ads/redexgen/X/Mj;Ljava/lang/String;Lcom/facebook/ads/redexgen/X/MC;)V

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/UL;->A06:Lcom/facebook/ads/redexgen/X/Q7;

    .line 19487
    iput-object p3, p0, Lcom/facebook/ads/redexgen/X/9P;->A04:Lcom/facebook/ads/redexgen/X/6c;

    .line 19488
    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/9P;->A0D:Lcom/facebook/ads/redexgen/X/Yn;

    .line 19489
    return-void
.end method

.method public constructor <init>(Lcom/facebook/ads/redexgen/X/Yn;Lcom/facebook/ads/redexgen/X/J2;Lcom/facebook/ads/redexgen/X/6c;Lcom/facebook/ads/redexgen/X/MC;Lcom/facebook/ads/redexgen/X/FU;)V
    .locals 1

    .line 19490
    invoke-direct {p0, p1, p2, p4, p5}, Lcom/facebook/ads/redexgen/X/UL;-><init>(Lcom/facebook/ads/redexgen/X/Yn;Lcom/facebook/ads/redexgen/X/J2;Lcom/facebook/ads/redexgen/X/MC;Lcom/facebook/ads/redexgen/X/b5;)V

    .line 19491
    new-instance v0, Lcom/facebook/ads/redexgen/X/Lg;

    invoke-direct {v0}, Lcom/facebook/ads/redexgen/X/Lg;-><init>()V

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/9P;->A0E:Lcom/facebook/ads/redexgen/X/Lg;

    .line 19492
    const/4 v0, 0x0

    iput-boolean v0, p0, Lcom/facebook/ads/redexgen/X/9P;->A0C:Z

    .line 19493
    iput-object p3, p0, Lcom/facebook/ads/redexgen/X/9P;->A04:Lcom/facebook/ads/redexgen/X/6c;

    .line 19494
    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/9P;->A0D:Lcom/facebook/ads/redexgen/X/Yn;

    .line 19495
    return-void
.end method

.method private A00(Landroid/content/Intent;)Lcom/facebook/ads/redexgen/X/b5;
    .locals 3

    .line 19496
    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/UL;->A07:Z

    if-eqz v0, :cond_0

    .line 19497
    const/16 v2, 0xe

    const/16 v1, 0x19

    const/16 v0, 0x4d

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/9P;->A09(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p1, v0}, Landroid/content/Intent;->getSerializableExtra(Ljava/lang/String;)Ljava/io/Serializable;

    move-result-object v0

    check-cast v0, Lcom/facebook/ads/redexgen/X/b5;

    .line 19498
    return-object v0

    .line 19499
    :cond_0
    const/4 v2, 0x0

    const/16 v1, 0xe

    const/16 v0, 0x5d

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/9P;->A09(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p1, v0}, Landroid/content/Intent;->getSerializableExtra(Ljava/lang/String;)Ljava/io/Serializable;

    move-result-object v0

    check-cast v0, Lcom/facebook/ads/redexgen/X/b5;

    .line 19500
    return-object v0
.end method

.method public static synthetic A01(Lcom/facebook/ads/redexgen/X/9P;)Lcom/facebook/ads/redexgen/X/b5;
    .locals 0

    .line 19501
    iget-object p0, p0, Lcom/facebook/ads/redexgen/X/UL;->A0A:Lcom/facebook/ads/redexgen/X/b5;

    return-object p0
.end method

.method public static synthetic A02(Lcom/facebook/ads/redexgen/X/9P;)Lcom/facebook/ads/redexgen/X/b5;
    .locals 0

    .line 19502
    iget-object p0, p0, Lcom/facebook/ads/redexgen/X/UL;->A0A:Lcom/facebook/ads/redexgen/X/b5;

    return-object p0
.end method

.method public static synthetic A03(Lcom/facebook/ads/redexgen/X/9P;)Lcom/facebook/ads/redexgen/X/b5;
    .locals 0

    .line 19503
    iget-object p0, p0, Lcom/facebook/ads/redexgen/X/9P;->A03:Lcom/facebook/ads/redexgen/X/b5;

    return-object p0
.end method

.method public static synthetic A04(Lcom/facebook/ads/redexgen/X/9P;)Lcom/facebook/ads/redexgen/X/Yn;
    .locals 0

    .line 19504
    iget-object p0, p0, Lcom/facebook/ads/redexgen/X/9P;->A0D:Lcom/facebook/ads/redexgen/X/Yn;

    return-object p0
.end method

.method public static synthetic A05(Lcom/facebook/ads/redexgen/X/9P;)Lcom/facebook/ads/redexgen/X/J2;
    .locals 0

    .line 19505
    iget-object p0, p0, Lcom/facebook/ads/redexgen/X/UL;->A0C:Lcom/facebook/ads/redexgen/X/J2;

    return-object p0
.end method

.method public static synthetic A06(Lcom/facebook/ads/redexgen/X/9P;)Lcom/facebook/ads/redexgen/X/Lg;
    .locals 0

    .line 19506
    iget-object p0, p0, Lcom/facebook/ads/redexgen/X/9P;->A0E:Lcom/facebook/ads/redexgen/X/Lg;

    return-object p0
.end method

.method public static synthetic A07(Lcom/facebook/ads/redexgen/X/9P;)Lcom/facebook/ads/redexgen/X/O2;
    .locals 0

    .line 19507
    iget-object p0, p0, Lcom/facebook/ads/redexgen/X/9P;->A06:Lcom/facebook/ads/redexgen/X/O2;

    return-object p0
.end method

.method public static synthetic A08(Lcom/facebook/ads/redexgen/X/9P;)Lcom/facebook/ads/redexgen/X/RE;
    .locals 0

    .line 19508
    iget-object p0, p0, Lcom/facebook/ads/redexgen/X/9P;->A09:Lcom/facebook/ads/redexgen/X/RE;

    return-object p0
.end method

.method public static A09(III)Ljava/lang/String;
    .locals 4

    sget-object v1, Lcom/facebook/ads/redexgen/X/9P;->A0F:[B

    add-int v0, p0, p1

    invoke-static {v1, p0, v0}, Ljava/util/Arrays;->copyOfRange([BII)[B

    move-result-object v3

    const/4 p0, 0x0

    :goto_0
    array-length p1, v3

    sget-object v2, Lcom/facebook/ads/redexgen/X/9P;->A0G:[Ljava/lang/String;

    const/4 v0, 0x0

    aget-object v1, v2, v0

    const/4 v0, 0x6

    aget-object v2, v2, v0

    const/16 v0, 0x16

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    invoke-virtual {v2, v0}, Ljava/lang/String;->charAt(I)C

    move-result v0

    if-eq v1, v0, :cond_0

    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    :cond_0
    sget-object v2, Lcom/facebook/ads/redexgen/X/9P;->A0G:[Ljava/lang/String;

    const-string v1, "I427OJiZERIl1pgFYs7VIJxgJMuHqTWY"

    const/4 v0, 0x5

    aput-object v1, v2, v0

    const-string v1, "m1VK8a3APoHjuOOKlTc7gi9yCeUy6Tbh"

    const/4 v0, 0x4

    aput-object v1, v2, v0

    if-ge p0, p1, :cond_1

    aget-byte v0, v3, p0

    xor-int/2addr v0, p2

    xor-int/lit8 v0, v0, 0x4a

    int-to-byte v0, v0

    aput-byte v0, v3, p0

    add-int/lit8 p0, p0, 0x1

    goto :goto_0

    :cond_1
    new-instance v0, Ljava/lang/String;

    invoke-direct {v0, v3}, Ljava/lang/String;-><init>([B)V

    return-object v0
.end method

.method public static synthetic A0A(Lcom/facebook/ads/redexgen/X/9P;)Ljava/lang/String;
    .locals 0

    .line 19509
    iget-object p0, p0, Lcom/facebook/ads/redexgen/X/9P;->A0A:Ljava/lang/String;

    return-object p0
.end method

.method public static A0B()V
    .locals 1

    const/16 v0, 0x27

    new-array v0, v0, [B

    fill-array-data v0, :array_0

    sput-object v0, Lcom/facebook/ads/redexgen/X/9P;->A0F:[B

    return-void

    :array_0
    .array-data 1
        0x76t
        0x73t
        0x48t
        0x73t
        0x76t
        0x63t
        0x76t
        0x48t
        0x75t
        0x62t
        0x79t
        0x73t
        0x7bt
        0x72t
        0x75t
        0x62t
        0x70t
        0x66t
        0x75t
        0x63t
        0x62t
        0x63t
        0x51t
        0x6et
        0x63t
        0x62t
        0x68t
        0x46t
        0x63t
        0x43t
        0x66t
        0x73t
        0x66t
        0x45t
        0x72t
        0x69t
        0x63t
        0x6bt
        0x62t
    .end array-data
.end method

.method private final A0C()V
    .locals 2

    .line 19510
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/9P;->A02:Landroid/widget/LinearLayout;

    const/4 v1, 0x0

    if-eqz v0, :cond_0

    .line 19511
    invoke-virtual {v0}, Landroid/widget/LinearLayout;->removeAllViews()V

    .line 19512
    iput-object v1, p0, Lcom/facebook/ads/redexgen/X/9P;->A02:Landroid/widget/LinearLayout;

    .line 19513
    :cond_0
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/9P;->A05:Lcom/facebook/ads/redexgen/X/3r;

    if-eqz v0, :cond_1

    .line 19514
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/3r;->removeAllViews()V

    .line 19515
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/9P;->A05:Lcom/facebook/ads/redexgen/X/3r;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/Eb;->A1Q()V

    .line 19516
    iput-object v1, p0, Lcom/facebook/ads/redexgen/X/9P;->A05:Lcom/facebook/ads/redexgen/X/3r;

    .line 19517
    :cond_1
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/9P;->A06:Lcom/facebook/ads/redexgen/X/O2;

    if-eqz v0, :cond_2

    .line 19518
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/O2;->removeAllViews()V

    .line 19519
    iput-object v1, p0, Lcom/facebook/ads/redexgen/X/9P;->A06:Lcom/facebook/ads/redexgen/X/O2;

    .line 19520
    :cond_2
    return-void
.end method

.method private final A0D(ILandroid/os/Bundle;)V
    .locals 24

    .line 19521
    move-object/from16 v0, p0

    invoke-virtual/range {p0 .. p0}, Lcom/facebook/ads/redexgen/X/9P;->getContext()Landroid/content/Context;

    move-result-object v1

    new-instance v2, Landroid/widget/LinearLayout;

    invoke-direct {v2, v1}, Landroid/widget/LinearLayout;-><init>(Landroid/content/Context;)V

    iput-object v2, v0, Lcom/facebook/ads/redexgen/X/9P;->A02:Landroid/widget/LinearLayout;

    .line 19522
    const/4 v8, 0x1

    move/from16 v5, p1

    if-ne v5, v8, :cond_5

    .line 19523
    const/16 v1, 0x11

    invoke-virtual {v2, v1}, Landroid/widget/LinearLayout;->setGravity(I)V

    .line 19524
    :goto_0
    iget-object v2, v0, Lcom/facebook/ads/redexgen/X/9P;->A02:Landroid/widget/LinearLayout;

    const/4 v7, -0x1

    new-instance v1, Landroid/widget/RelativeLayout$LayoutParams;

    invoke-direct {v1, v7, v7}, Landroid/widget/RelativeLayout$LayoutParams;-><init>(II)V

    invoke-virtual {v2, v1}, Landroid/widget/LinearLayout;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 19525
    iget-object v1, v0, Lcom/facebook/ads/redexgen/X/9P;->A02:Landroid/widget/LinearLayout;

    invoke-virtual {v1, v8}, Landroid/widget/LinearLayout;->setOrientation(I)V

    .line 19526
    sget-object v1, Lcom/facebook/ads/redexgen/X/LD;->A03:Landroid/util/DisplayMetrics;

    iget v3, v1, Landroid/util/DisplayMetrics;->widthPixels:I

    .line 19527
    .local v13, "width":I
    sget-object v1, Lcom/facebook/ads/redexgen/X/LD;->A03:Landroid/util/DisplayMetrics;

    iget v4, v1, Landroid/util/DisplayMetrics;->heightPixels:I

    .line 19528
    .local v12, "height":I
    if-ne v5, v8, :cond_4

    .line 19529
    sget v1, Lcom/facebook/ads/redexgen/X/9P;->A0I:I

    mul-int/lit8 v1, v1, 0x4

    sub-int v2, v3, v1

    div-int/lit8 v1, v4, 0x2

    invoke-static {v2, v1}, Ljava/lang/Math;->min(II)I

    move-result v4

    .line 19530
    .local v1, "childWidth":I
    sub-int/2addr v3, v4

    div-int/lit8 v3, v3, 0x8

    .line 19531
    .local v2, "childSpacing":I
    mul-int/lit8 v20, v3, 0x4

    .line 19532
    .local v4, "extraSpacing":I
    .end local v1    # "childWidth":I
    .end local v2    # "childSpacing":I
    .local v17, "childWidth":I
    .local v18, "childSpacing":I
    .local v19, "extraSpacing":I
    :goto_1
    new-instance v1, Lcom/facebook/ads/redexgen/X/SS;

    invoke-direct {v1, v0}, Lcom/facebook/ads/redexgen/X/SS;-><init>(Lcom/facebook/ads/redexgen/X/9P;)V

    iput-object v1, v0, Lcom/facebook/ads/redexgen/X/9P;->A08:Lcom/facebook/ads/redexgen/X/RD;

    .line 19533
    iget-object v1, v0, Lcom/facebook/ads/redexgen/X/9P;->A08:Lcom/facebook/ads/redexgen/X/RD;

    new-instance v6, Ljava/lang/ref/WeakReference;

    invoke-direct {v6, v1}, Ljava/lang/ref/WeakReference;-><init>(Ljava/lang/Object;)V

    iget-object v1, v0, Lcom/facebook/ads/redexgen/X/9P;->A0D:Lcom/facebook/ads/redexgen/X/Yn;

    new-instance v2, Lcom/facebook/ads/redexgen/X/RE;

    invoke-direct {v2, v0, v8, v6, v1}, Lcom/facebook/ads/redexgen/X/RE;-><init>(Landroid/view/View;ILjava/lang/ref/WeakReference;Lcom/facebook/ads/redexgen/X/Yn;)V

    iput-object v2, v0, Lcom/facebook/ads/redexgen/X/9P;->A09:Lcom/facebook/ads/redexgen/X/RE;

    .line 19534
    iget v1, v0, Lcom/facebook/ads/redexgen/X/9P;->A00:I

    invoke-virtual {v2, v1}, Lcom/facebook/ads/redexgen/X/RE;->A0W(I)V

    .line 19535
    iget-object v2, v0, Lcom/facebook/ads/redexgen/X/9P;->A09:Lcom/facebook/ads/redexgen/X/RE;

    iget v1, v0, Lcom/facebook/ads/redexgen/X/9P;->A01:I

    invoke-virtual {v2, v1}, Lcom/facebook/ads/redexgen/X/RE;->A0X(I)V

    .line 19536
    iget-object v1, v0, Lcom/facebook/ads/redexgen/X/9P;->A0D:Lcom/facebook/ads/redexgen/X/Yn;

    new-instance v6, Lcom/facebook/ads/redexgen/X/3r;

    invoke-direct {v6, v1}, Lcom/facebook/ads/redexgen/X/3r;-><init>(Lcom/facebook/ads/redexgen/X/Yn;)V

    iput-object v6, v0, Lcom/facebook/ads/redexgen/X/9P;->A05:Lcom/facebook/ads/redexgen/X/3r;

    .line 19537
    const/4 v2, -0x2

    new-instance v1, Landroid/widget/RelativeLayout$LayoutParams;

    invoke-direct {v1, v7, v2}, Landroid/widget/RelativeLayout$LayoutParams;-><init>(II)V

    invoke-virtual {v6, v1}, Lcom/facebook/ads/redexgen/X/3r;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 19538
    new-instance v7, Lcom/facebook/ads/redexgen/X/Sb;

    iget-object v6, v0, Lcom/facebook/ads/redexgen/X/9P;->A05:Lcom/facebook/ads/redexgen/X/3r;

    iget-object v2, v0, Lcom/facebook/ads/redexgen/X/9P;->A0B:Ljava/util/List;

    iget-object v1, v0, Lcom/facebook/ads/redexgen/X/9P;->A09:Lcom/facebook/ads/redexgen/X/RE;

    move-object v7, v7

    move-object v8, v6

    move v9, v5

    move-object v10, v2

    move-object v11, v1

    move-object/from16 v12, p2

    invoke-direct/range {v7 .. v12}, Lcom/facebook/ads/redexgen/X/Sb;-><init>(Lcom/facebook/ads/redexgen/X/3r;ILjava/util/List;Lcom/facebook/ads/redexgen/X/RE;Landroid/os/Bundle;)V

    iput-object v7, v0, Lcom/facebook/ads/redexgen/X/9P;->A07:Lcom/facebook/ads/redexgen/X/Sb;

    .line 19539
    iget-object v2, v0, Lcom/facebook/ads/redexgen/X/9P;->A05:Lcom/facebook/ads/redexgen/X/3r;

    new-instance v8, Lcom/facebook/ads/redexgen/X/ST;

    iget-object v9, v0, Lcom/facebook/ads/redexgen/X/9P;->A0D:Lcom/facebook/ads/redexgen/X/Yn;

    iget-object v10, v0, Lcom/facebook/ads/redexgen/X/9P;->A0B:Ljava/util/List;

    iget-object v11, v0, Lcom/facebook/ads/redexgen/X/9P;->A03:Lcom/facebook/ads/redexgen/X/b5;

    iget-object v12, v0, Lcom/facebook/ads/redexgen/X/UL;->A0C:Lcom/facebook/ads/redexgen/X/J2;

    iget-object v13, v0, Lcom/facebook/ads/redexgen/X/9P;->A04:Lcom/facebook/ads/redexgen/X/6c;

    iget-object v14, v0, Lcom/facebook/ads/redexgen/X/9P;->A09:Lcom/facebook/ads/redexgen/X/RE;

    iget-object v15, v0, Lcom/facebook/ads/redexgen/X/9P;->A0E:Lcom/facebook/ads/redexgen/X/Lg;

    .line 19540
    invoke-virtual/range {p0 .. p0}, Lcom/facebook/ads/redexgen/X/UL;->getAudienceNetworkListener()Lcom/facebook/ads/redexgen/X/MC;

    move-result-object v16

    iget-object v7, v0, Lcom/facebook/ads/redexgen/X/9P;->A0A:Ljava/lang/String;

    iget-object v1, v0, Lcom/facebook/ads/redexgen/X/9P;->A07:Lcom/facebook/ads/redexgen/X/Sb;

    iget-object v0, v0, Lcom/facebook/ads/redexgen/X/UL;->A0E:Lcom/facebook/ads/redexgen/X/MB;

    move-object v6, v8

    .end local v12    # "height":I
    .local v20, "height":I
    .end local v13    # "width":I
    .local v22, "width":I
    move-object/from16 v22, v1

    move-object/from16 v23, v0

    move/from16 v21, v5

    move/from16 v19, v3

    move/from16 v18, v4

    move-object/from16 v17, v7

    invoke-direct/range {v8 .. v23}, Lcom/facebook/ads/redexgen/X/ST;-><init>(Lcom/facebook/ads/redexgen/X/Yn;Ljava/util/List;Lcom/facebook/ads/redexgen/X/b5;Lcom/facebook/ads/redexgen/X/J2;Lcom/facebook/ads/redexgen/X/6c;Lcom/facebook/ads/redexgen/X/RE;Lcom/facebook/ads/redexgen/X/Lg;Lcom/facebook/ads/redexgen/X/MC;Ljava/lang/String;IIIILcom/facebook/ads/redexgen/X/Sb;Lcom/facebook/ads/redexgen/X/MB;)V

    .line 19541
    invoke-virtual {v2, v6}, Lcom/facebook/ads/redexgen/X/Eb;->setAdapter(Lcom/facebook/ads/redexgen/X/4c;)V

    .line 19542
    move-object/from16 v4, p0

    iget-object v1, v4, Lcom/facebook/ads/redexgen/X/9P;->A05:Lcom/facebook/ads/redexgen/X/3r;

    invoke-virtual {v1}, Lcom/facebook/ads/redexgen/X/3r;->getOnScrollListener()Lcom/facebook/ads/redexgen/X/4t;

    move-result-object v0

    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/Eb;->A1f(Lcom/facebook/ads/redexgen/X/4t;)V

    .line 19543
    const/4 v0, 0x1

    if-ne v5, v0, :cond_0

    .line 19544
    iget-object v0, v4, Lcom/facebook/ads/redexgen/X/9P;->A07:Lcom/facebook/ads/redexgen/X/Sb;

    invoke-direct {v4, v0}, Lcom/facebook/ads/redexgen/X/9P;->A0F(Lcom/facebook/ads/redexgen/X/Sb;)V

    .line 19545
    :cond_0
    iget-object v6, v4, Lcom/facebook/ads/redexgen/X/9P;->A02:Landroid/widget/LinearLayout;

    iget-object v3, v4, Lcom/facebook/ads/redexgen/X/9P;->A05:Lcom/facebook/ads/redexgen/X/3r;

    sget-object v1, Lcom/facebook/ads/redexgen/X/9P;->A0G:[Ljava/lang/String;

    const/4 v0, 0x7

    aget-object v1, v1, v0

    const/16 v0, 0x1a

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    const/16 v0, 0x61

    if-eq v1, v0, :cond_7

    sget-object v2, Lcom/facebook/ads/redexgen/X/9P;->A0G:[Ljava/lang/String;

    const-string v1, "pS0yYjaDPNGuTrfnUmeGhxtdEtLadoEJ"

    const/4 v0, 0x0

    aput-object v1, v2, v0

    const-string v1, "mwC7eMAbLdHre0yQkkMBVztsNAF33a9m"

    const/4 v0, 0x6

    aput-object v1, v2, v0

    invoke-virtual {v6, v3}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 19546
    iget-object v3, v4, Lcom/facebook/ads/redexgen/X/9P;->A06:Lcom/facebook/ads/redexgen/X/O2;

    if-eqz v3, :cond_1

    .line 19547
    iget-object v6, v4, Lcom/facebook/ads/redexgen/X/9P;->A02:Landroid/widget/LinearLayout;

    sget-object v2, Lcom/facebook/ads/redexgen/X/9P;->A0G:[Ljava/lang/String;

    const/4 v0, 0x5

    aget-object v1, v2, v0

    const/4 v0, 0x4

    aget-object v2, v2, v0

    const/16 v0, 0x10

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    invoke-virtual {v2, v0}, Ljava/lang/String;->charAt(I)C

    move-result v0

    if-eq v1, v0, :cond_6

    sget-object v2, Lcom/facebook/ads/redexgen/X/9P;->A0G:[Ljava/lang/String;

    const-string v1, "7ZjVpxQcOo2BFfYt51qxyVC8jdlns0bj"

    const/4 v0, 0x3

    aput-object v1, v2, v0

    invoke-virtual {v6, v3}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 19548
    :cond_1
    iget-object v0, v4, Lcom/facebook/ads/redexgen/X/9P;->A0D:Lcom/facebook/ads/redexgen/X/Yn;

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/Ih;->A1W(Landroid/content/Context;)Z

    move-result v6

    const/4 v3, 0x0

    sget-object v2, Lcom/facebook/ads/redexgen/X/9P;->A0G:[Ljava/lang/String;

    const/4 v0, 0x0

    aget-object v1, v2, v0

    const/4 v0, 0x6

    aget-object v2, v2, v0

    const/16 v0, 0x16

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    invoke-virtual {v2, v0}, Ljava/lang/String;->charAt(I)C

    move-result v0

    if-eq v1, v0, :cond_3

    if-eqz v6, :cond_2

    .line 19549
    :goto_2
    iget-object v0, v4, Lcom/facebook/ads/redexgen/X/9P;->A0D:Lcom/facebook/ads/redexgen/X/Yn;

    .line 19550
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/7f;->A0A()Lcom/facebook/ads/redexgen/X/JE;

    move-result-object v2

    iget-object v1, v4, Lcom/facebook/ads/redexgen/X/9P;->A05:Lcom/facebook/ads/redexgen/X/3r;

    iget-object v0, v4, Lcom/facebook/ads/redexgen/X/UL;->A0A:Lcom/facebook/ads/redexgen/X/b5;

    .line 19551
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/b5;->A12()Ljava/lang/String;

    move-result-object v0

    invoke-interface {v2, v1, v0, v3}, Lcom/facebook/ads/redexgen/X/JE;->AGv(Landroid/view/View;Ljava/lang/String;Z)V

    .line 19552
    :cond_2
    iget-object v0, v4, Lcom/facebook/ads/redexgen/X/9P;->A02:Landroid/widget/LinearLayout;

    invoke-virtual {v4, v0, v3, v5}, Lcom/facebook/ads/redexgen/X/UL;->A0U(Landroid/view/View;ZI)V

    .line 19553
    return-void

    :cond_3
    sget-object v2, Lcom/facebook/ads/redexgen/X/9P;->A0G:[Ljava/lang/String;

    const-string v1, "nKvQ299cRrmi8SltfoUYXILZ1CbfqCSq"

    const/4 v0, 0x3

    aput-object v1, v2, v0

    if-eqz v6, :cond_2

    goto :goto_2

    .line 19554
    .end local v1
    .end local v2
    .end local v4    # "extraSpacing":I
    :cond_4
    sget v2, Lcom/facebook/ads/redexgen/X/9P;->A0L:I

    sget v1, Lcom/facebook/ads/redexgen/X/9P;->A0H:I

    add-int/2addr v2, v1

    sget v1, Lcom/facebook/ads/redexgen/X/9P;->A0I:I

    mul-int/lit8 v1, v1, 0x4

    add-int/2addr v2, v1

    sub-int/2addr v4, v2

    .line 19555
    .restart local v1    # "childWidth":I
    sget v3, Lcom/facebook/ads/redexgen/X/9P;->A0I:I

    .line 19556
    .restart local v2    # "childSpacing":I
    mul-int/lit8 v20, v3, 0x2

    goto/16 :goto_1

    .line 19557
    :cond_5
    const/16 v1, 0x30

    invoke-virtual {v2, v1}, Landroid/widget/LinearLayout;->setGravity(I)V

    goto/16 :goto_0

    :cond_6
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    :cond_7
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0
.end method

.method private A0E(Lcom/facebook/ads/redexgen/X/b5;)V
    .locals 6

    .line 19558
    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/9P;->A03:Lcom/facebook/ads/redexgen/X/b5;

    .line 19559
    invoke-virtual {p1}, Lcom/facebook/ads/redexgen/X/b5;->A12()Ljava/lang/String;

    move-result-object v0

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/9P;->A0A:Ljava/lang/String;

    .line 19560
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/9P;->A03:Lcom/facebook/ads/redexgen/X/b5;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/1F;->A0C()I

    move-result v0

    iput v0, p0, Lcom/facebook/ads/redexgen/X/9P;->A00:I

    .line 19561
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/9P;->A03:Lcom/facebook/ads/redexgen/X/b5;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/1F;->A0D()I

    move-result v0

    iput v0, p0, Lcom/facebook/ads/redexgen/X/9P;->A01:I

    .line 19562
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/9P;->A03:Lcom/facebook/ads/redexgen/X/b5;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/b5;->A14()Ljava/util/List;

    move-result-object v5

    .line 19563
    .local v0, "adInfoList":Ljava/util/List;, "Ljava/util/List<Lcom/facebook/ads/internal/adapters/datamodels/AdInfo;>;"
    invoke-interface {v5}, Ljava/util/List;->size()I

    move-result v1

    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0, v1}, Ljava/util/ArrayList;-><init>(I)V

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/9P;->A0B:Ljava/util/List;

    .line 19564
    const/4 v4, 0x0

    .local v1, "i":I
    :goto_0
    invoke-interface {v5}, Ljava/util/List;->size()I

    move-result v3

    sget-object v2, Lcom/facebook/ads/redexgen/X/9P;->A0G:[Ljava/lang/String;

    const/4 v0, 0x5

    aget-object v1, v2, v0

    const/4 v0, 0x4

    aget-object v2, v2, v0

    const/16 v0, 0x10

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    invoke-virtual {v2, v0}, Ljava/lang/String;->charAt(I)C

    move-result v0

    if-eq v1, v0, :cond_1

    sget-object v2, Lcom/facebook/ads/redexgen/X/9P;->A0G:[Ljava/lang/String;

    const-string v1, "1AHb04hcylshFNuVFPVd0IA840Fx90m0"

    const/4 v0, 0x3

    aput-object v1, v2, v0

    if-ge v4, v3, :cond_0

    .line 19565
    invoke-interface {v5, v4}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lcom/facebook/ads/redexgen/X/1G;

    .line 19566
    .local v2, "adInfo":Lcom/facebook/ads/redexgen/X/1G;
    iget-object v2, p0, Lcom/facebook/ads/redexgen/X/9P;->A0B:Ljava/util/List;

    invoke-interface {v5}, Ljava/util/List;->size()I

    move-result v1

    new-instance v0, Lcom/facebook/ads/redexgen/X/Pp;

    invoke-direct {v0, v4, v1, v3}, Lcom/facebook/ads/redexgen/X/Pp;-><init>(IILcom/facebook/ads/redexgen/X/1G;)V

    invoke-interface {v2, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 19567
    .end local v2    # "adInfo":Lcom/facebook/ads/redexgen/X/1G;
    add-int/lit8 v4, v4, 0x1

    goto :goto_0

    .line 19568
    .end local v1    # "i":I
    :cond_0
    return-void

    :cond_1
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0
.end method

.method private A0F(Lcom/facebook/ads/redexgen/X/Sb;)V
    .locals 4

    .line 19569
    new-instance v1, Lcom/facebook/ads/redexgen/X/Ec;

    invoke-direct {v1}, Lcom/facebook/ads/redexgen/X/Ec;-><init>()V

    .line 19570
    .local v0, "mSnapHelper":Lcom/facebook/ads/redexgen/X/ZY;
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/9P;->A05:Lcom/facebook/ads/redexgen/X/3r;

    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/ZY;->A0G(Lcom/facebook/ads/redexgen/X/Eb;)V

    .line 19571
    new-instance v0, Lcom/facebook/ads/redexgen/X/SR;

    invoke-direct {v0, p0}, Lcom/facebook/ads/redexgen/X/SR;-><init>(Lcom/facebook/ads/redexgen/X/9P;)V

    invoke-virtual {p1, v0}, Lcom/facebook/ads/redexgen/X/Sb;->A0Y(Lcom/facebook/ads/redexgen/X/Op;)V

    .line 19572
    iget-object v3, p0, Lcom/facebook/ads/redexgen/X/9P;->A0D:Lcom/facebook/ads/redexgen/X/Yn;

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/UL;->A05:Lcom/facebook/ads/redexgen/X/1C;

    .line 19573
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/1C;->A01()Lcom/facebook/ads/redexgen/X/1P;

    move-result-object v2

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/9P;->A0B:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v1

    new-instance v0, Lcom/facebook/ads/redexgen/X/O2;

    invoke-direct {v0, v3, v2, v1}, Lcom/facebook/ads/redexgen/X/O2;-><init>(Lcom/facebook/ads/redexgen/X/Yn;Lcom/facebook/ads/redexgen/X/1P;I)V

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/9P;->A06:Lcom/facebook/ads/redexgen/X/O2;

    .line 19574
    const/4 v1, -0x1

    sget v0, Lcom/facebook/ads/redexgen/X/9P;->A0J:I

    new-instance v2, Landroid/widget/LinearLayout$LayoutParams;

    invoke-direct {v2, v1, v0}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    .line 19575
    .local v1, "positionDotsLayoutParams":Landroid/widget/LinearLayout$LayoutParams;
    const/4 v1, 0x0

    sget v0, Lcom/facebook/ads/redexgen/X/9P;->A0K:I

    invoke-virtual {v2, v1, v0, v1, v1}, Landroid/widget/LinearLayout$LayoutParams;->setMargins(IIII)V

    .line 19576
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/9P;->A06:Lcom/facebook/ads/redexgen/X/O2;

    invoke-virtual {v0, v2}, Lcom/facebook/ads/redexgen/X/O2;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 19577
    return-void
.end method

.method public static synthetic A0G(Lcom/facebook/ads/redexgen/X/9P;Lcom/facebook/ads/redexgen/X/Lg;)V
    .locals 0

    .line 19578
    invoke-virtual {p0, p1}, Lcom/facebook/ads/redexgen/X/UL;->setImpressionRecordingFlag(Lcom/facebook/ads/redexgen/X/Lg;)V

    return-void
.end method

.method public static synthetic A0H(Lcom/facebook/ads/redexgen/X/9P;)Z
    .locals 0

    .line 19579
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/UL;->A0Y()Z

    move-result p0

    return p0
.end method

.method public static synthetic A0I(Lcom/facebook/ads/redexgen/X/9P;)Z
    .locals 0

    .line 19580
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/UL;->A0Z()Z

    move-result p0

    return p0
.end method


# virtual methods
.method public final A0a()Z
    .locals 1

    .line 19581
    const/4 v0, 0x0

    return v0
.end method

.method public final A9Q(Landroid/content/Intent;Landroid/os/Bundle;Lcom/facebook/ads/redexgen/X/5V;)V
    .locals 2

    .line 19582
    invoke-direct {p0, p1}, Lcom/facebook/ads/redexgen/X/9P;->A00(Landroid/content/Intent;)Lcom/facebook/ads/redexgen/X/b5;

    move-result-object v0

    .line 19583
    .local v0, "dataBundle":Lcom/facebook/ads/redexgen/X/b5;
    invoke-virtual {p0, p3}, Lcom/facebook/ads/redexgen/X/UL;->A0V(Lcom/facebook/ads/redexgen/X/5V;)V

    .line 19584
    invoke-direct {p0, v0}, Lcom/facebook/ads/redexgen/X/9P;->A0E(Lcom/facebook/ads/redexgen/X/b5;)V

    .line 19585
    invoke-virtual {p3}, Lcom/facebook/ads/redexgen/X/5V;->A0J()Lcom/facebook/ads/AudienceNetworkActivity;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/AudienceNetworkActivity;->getResources()Landroid/content/res/Resources;

    move-result-object v0

    invoke-virtual {v0}, Landroid/content/res/Resources;->getConfiguration()Landroid/content/res/Configuration;

    move-result-object v0

    iget v0, v0, Landroid/content/res/Configuration;->orientation:I

    .line 19586
    invoke-direct {p0, v0, p2}, Lcom/facebook/ads/redexgen/X/9P;->A0D(ILandroid/os/Bundle;)V

    .line 19587
    new-instance v0, Lcom/facebook/ads/redexgen/X/SQ;

    invoke-direct {v0, p0, p3}, Lcom/facebook/ads/redexgen/X/SQ;-><init>(Lcom/facebook/ads/redexgen/X/9P;Lcom/facebook/ads/redexgen/X/5V;)V

    invoke-virtual {p3, v0}, Lcom/facebook/ads/redexgen/X/5V;->A0N(Lcom/facebook/ads/redexgen/X/5T;)V

    .line 19588
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/UL;->A0A:Lcom/facebook/ads/redexgen/X/b5;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/b5;->A0x()Lcom/facebook/ads/redexgen/X/1G;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/1G;->A0D()Lcom/facebook/ads/redexgen/X/1J;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/1J;->A03()I

    move-result v1

    .line 19589
    .local v1, "unskippableSec":I
    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/UL;->A07:Z

    if-eqz v0, :cond_0

    .line 19590
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/UL;->A0A:Lcom/facebook/ads/redexgen/X/b5;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/b5;->A0x()Lcom/facebook/ads/redexgen/X/1G;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/1G;->A0D()Lcom/facebook/ads/redexgen/X/1J;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/1J;->A02()I

    move-result v1

    .line 19591
    :cond_0
    if-lez v1, :cond_1

    .line 19592
    invoke-virtual {p0, v1}, Lcom/facebook/ads/redexgen/X/UL;->A0T(I)V

    .line 19593
    :cond_1
    return-void
.end method

.method public final ACW(Z)V
    .locals 1

    .line 19594
    invoke-super {p0, p1}, Lcom/facebook/ads/redexgen/X/UL;->ACW(Z)V

    .line 19595
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/9P;->A07:Lcom/facebook/ads/redexgen/X/Sb;

    if-eqz v0, :cond_0

    .line 19596
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/Sb;->A0Q()V

    .line 19597
    :cond_0
    return-void
.end method

.method public final ACu(Z)V
    .locals 1

    .line 19598
    invoke-super {p0, p1}, Lcom/facebook/ads/redexgen/X/UL;->ACu(Z)V

    .line 19599
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/9P;->A07:Lcom/facebook/ads/redexgen/X/Sb;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/Sb;->A0R()V

    .line 19600
    return-void
.end method

.method public final AFT(Landroid/os/Bundle;)V
    .locals 1

    .line 19601
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/9P;->A07:Lcom/facebook/ads/redexgen/X/Sb;

    if-eqz v0, :cond_0

    .line 19602
    invoke-virtual {v0, p1}, Lcom/facebook/ads/redexgen/X/Sb;->A0W(Landroid/os/Bundle;)V

    .line 19603
    :cond_0
    return-void
.end method

.method public getCloseButtonStyle()I
    .locals 1

    .line 19604
    const/4 v0, 0x0

    return v0
.end method

.method public final onConfigurationChanged(Landroid/content/res/Configuration;)V
    .locals 2

    .line 19605
    new-instance v1, Landroid/os/Bundle;

    invoke-direct {v1}, Landroid/os/Bundle;-><init>()V

    .line 19606
    .local v0, "savedInstanceState":Landroid/os/Bundle;
    invoke-virtual {p0, v1}, Lcom/facebook/ads/redexgen/X/9P;->AFT(Landroid/os/Bundle;)V

    .line 19607
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/9P;->A0C()V

    .line 19608
    iget v0, p1, Landroid/content/res/Configuration;->orientation:I

    invoke-direct {p0, v0, v1}, Lcom/facebook/ads/redexgen/X/9P;->A0D(ILandroid/os/Bundle;)V

    .line 19609
    invoke-super {p0, p1}, Lcom/facebook/ads/redexgen/X/UL;->onConfigurationChanged(Landroid/content/res/Configuration;)V

    .line 19610
    return-void
.end method

.method public final onDestroy()V
    .locals 4

    .line 19611
    invoke-super {p0}, Lcom/facebook/ads/redexgen/X/UL;->onDestroy()V

    .line 19612
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/9P;->A0D:Lcom/facebook/ads/redexgen/X/Yn;

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/Ih;->A1W(Landroid/content/Context;)Z

    move-result v0

    if-eqz v0, :cond_0

    .line 19613
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/9P;->A0D:Lcom/facebook/ads/redexgen/X/Yn;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/7f;->A0A()Lcom/facebook/ads/redexgen/X/JE;

    move-result-object v1

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/9P;->A05:Lcom/facebook/ads/redexgen/X/3r;

    invoke-interface {v1, v0}, Lcom/facebook/ads/redexgen/X/JE;->AGk(Landroid/view/View;)V

    .line 19614
    :cond_0
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/9P;->A0A:Ljava/lang/String;

    invoke-static {v0}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    if-nez v0, :cond_1

    .line 19615
    iget-object v3, p0, Lcom/facebook/ads/redexgen/X/UL;->A0C:Lcom/facebook/ads/redexgen/X/J2;

    iget-object v2, p0, Lcom/facebook/ads/redexgen/X/9P;->A0A:Ljava/lang/String;

    new-instance v1, Lcom/facebook/ads/redexgen/X/Ni;

    invoke-direct {v1}, Lcom/facebook/ads/redexgen/X/Ni;-><init>()V

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/9P;->A09:Lcom/facebook/ads/redexgen/X/RE;

    .line 19616
    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/Ni;->A03(Lcom/facebook/ads/redexgen/X/RE;)Lcom/facebook/ads/redexgen/X/Ni;

    move-result-object v1

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/9P;->A0E:Lcom/facebook/ads/redexgen/X/Lg;

    .line 19617
    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/Ni;->A02(Lcom/facebook/ads/redexgen/X/Lg;)Lcom/facebook/ads/redexgen/X/Ni;

    move-result-object v0

    .line 19618
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/Ni;->A05()Ljava/util/Map;

    move-result-object v0

    .line 19619
    invoke-interface {v3, v2, v0}, Lcom/facebook/ads/redexgen/X/J2;->A9X(Ljava/lang/String;Ljava/util/Map;)V

    .line 19620
    :cond_1
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/9P;->A0C()V

    .line 19621
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/9P;->A09:Lcom/facebook/ads/redexgen/X/RE;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/RE;->A0V()V

    .line 19622
    const/4 v0, 0x0

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/9P;->A09:Lcom/facebook/ads/redexgen/X/RE;

    .line 19623
    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/9P;->A08:Lcom/facebook/ads/redexgen/X/RD;

    .line 19624
    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/9P;->A0B:Ljava/util/List;

    .line 19625
    return-void
.end method

.method public final onInterceptTouchEvent(Landroid/view/MotionEvent;)Z
    .locals 2

    .line 19626
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/9P;->A0E:Lcom/facebook/ads/redexgen/X/Lg;

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/9P;->A0D:Lcom/facebook/ads/redexgen/X/Yn;

    invoke-virtual {v1, v0, p1, p0, p0}, Lcom/facebook/ads/redexgen/X/Lg;->A06(Lcom/facebook/ads/redexgen/X/Yn;Landroid/view/MotionEvent;Landroid/view/View;Landroid/view/View;)V

    .line 19627
    invoke-super {p0, p1}, Lcom/facebook/ads/redexgen/X/UL;->onInterceptTouchEvent(Landroid/view/MotionEvent;)Z

    move-result v0

    return v0
.end method
