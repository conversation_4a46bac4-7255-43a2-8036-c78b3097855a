.class public final Lcom/facebook/ads/redexgen/X/9Q;
.super Lcom/facebook/ads/redexgen/X/Ss;
.source ""


# static fields
.field public static A0L:[B

.field public static A0M:[Ljava/lang/String;


# instance fields
.field public A00:Lcom/facebook/ads/redexgen/X/OL;

.field public A01:Lcom/facebook/ads/redexgen/X/Q7;

.field public A02:Lcom/facebook/ads/redexgen/X/QM;

.field public A03:Z

.field public A04:Z

.field public A05:Z

.field public A06:Z

.field public A07:Z

.field public A08:Z

.field public final A09:Lcom/facebook/ads/redexgen/X/5T;

.field public final A0A:Lcom/facebook/ads/redexgen/X/SF;

.field public final A0B:Lcom/facebook/ads/redexgen/X/SA;

.field public final A0C:Lcom/facebook/ads/redexgen/X/9I;

.field public final A0D:Lcom/facebook/ads/redexgen/X/Pu;

.field public final A0E:Lcom/facebook/ads/redexgen/X/Ps;

.field public final A0F:Lcom/facebook/ads/redexgen/X/On;

.field public final A0G:Lcom/facebook/ads/redexgen/X/O7;

.field public final A0H:Lcom/facebook/ads/redexgen/X/NX;

.field public final A0I:Lcom/facebook/ads/redexgen/X/NQ;

.field public final A0J:Lcom/facebook/ads/redexgen/X/Kw;

.field public final A0K:Lcom/facebook/ads/redexgen/X/KP;


# direct methods
.method public static constructor <clinit>()V
    .locals 3

    .line 918
    const/16 v0, 0x8

    new-array v2, v0, [Ljava/lang/String;

    const/4 v1, 0x0

    const-string v0, "Mriq29lS8vjEPztqvg4mnlhqPa1pf4jw"

    aput-object v0, v2, v1

    const/4 v1, 0x1

    const-string v0, "J0ebGdEZD5oKohgNzUxMA3JiHaSF4bRJ"

    aput-object v0, v2, v1

    const/4 v1, 0x2

    const-string v0, "IT9tvlYp44pYwsfxgtsksFS"

    aput-object v0, v2, v1

    const/4 v1, 0x3

    const-string v0, "El43mrOkb3qqMjbYlNbneDDC3riKbt8u"

    aput-object v0, v2, v1

    const/4 v1, 0x4

    const-string v0, "SsyQgLhJr8BA6K4Jeu0226NIXlotTSUK"

    aput-object v0, v2, v1

    const/4 v1, 0x5

    const-string v0, "5pKSfcHcmxRzGJ6MCKbxSHPk9j0pnkgf"

    aput-object v0, v2, v1

    const/4 v1, 0x6

    const-string v0, "vZ9Xs81YGHnjRjGsoTeSsIOHxP"

    aput-object v0, v2, v1

    const/4 v1, 0x7

    const-string v0, "Y9Sammkz"

    aput-object v0, v2, v1

    sput-object v2, Lcom/facebook/ads/redexgen/X/9Q;->A0M:[Ljava/lang/String;

    invoke-static {}, Lcom/facebook/ads/redexgen/X/9Q;->A08()V

    return-void
.end method

.method public constructor <init>(Lcom/facebook/ads/redexgen/X/Yn;Lcom/facebook/ads/redexgen/X/Mj;Lcom/facebook/ads/redexgen/X/J2;Lcom/facebook/ads/redexgen/X/b5;Lcom/facebook/ads/redexgen/X/6c;Lcom/facebook/ads/redexgen/X/MC;)V
    .locals 16

    .line 19628
    move-object/from16 v2, p0

    move-object v2, v2

    move-object/from16 v3, p1

    move-object/from16 v4, p2

    move-object/from16 v5, p3

    move-object/from16 v6, p4

    move-object/from16 v7, p5

    move-object/from16 v8, p6

    move-object v8, v8

    invoke-direct/range {v2 .. v8}, Lcom/facebook/ads/redexgen/X/Ss;-><init>(Lcom/facebook/ads/redexgen/X/Yn;Lcom/facebook/ads/redexgen/X/Mj;Lcom/facebook/ads/redexgen/X/J2;Lcom/facebook/ads/redexgen/X/b5;Lcom/facebook/ads/redexgen/X/6c;Lcom/facebook/ads/redexgen/X/MC;)V

    .line 19629
    new-instance v0, Lcom/facebook/ads/redexgen/X/Sh;

    invoke-direct {v0, v2}, Lcom/facebook/ads/redexgen/X/Sh;-><init>(Lcom/facebook/ads/redexgen/X/9Q;)V

    iput-object v0, v2, Lcom/facebook/ads/redexgen/X/9Q;->A09:Lcom/facebook/ads/redexgen/X/5T;

    .line 19630
    new-instance v1, Lcom/facebook/ads/redexgen/X/9X;

    invoke-direct {v1, v2}, Lcom/facebook/ads/redexgen/X/9X;-><init>(Lcom/facebook/ads/redexgen/X/9Q;)V

    iput-object v1, v2, Lcom/facebook/ads/redexgen/X/9Q;->A0H:Lcom/facebook/ads/redexgen/X/NX;

    .line 19631
    new-instance v12, Lcom/facebook/ads/redexgen/X/9V;

    invoke-direct {v12, v2}, Lcom/facebook/ads/redexgen/X/9V;-><init>(Lcom/facebook/ads/redexgen/X/9Q;)V

    iput-object v12, v2, Lcom/facebook/ads/redexgen/X/9Q;->A0G:Lcom/facebook/ads/redexgen/X/O7;

    .line 19632
    new-instance v11, Lcom/facebook/ads/redexgen/X/9U;

    invoke-direct {v11, v2}, Lcom/facebook/ads/redexgen/X/9U;-><init>(Lcom/facebook/ads/redexgen/X/9Q;)V

    iput-object v11, v2, Lcom/facebook/ads/redexgen/X/9Q;->A0F:Lcom/facebook/ads/redexgen/X/On;

    .line 19633
    new-instance v10, Lcom/facebook/ads/redexgen/X/9T;

    invoke-direct {v10, v2}, Lcom/facebook/ads/redexgen/X/9T;-><init>(Lcom/facebook/ads/redexgen/X/9Q;)V

    iput-object v10, v2, Lcom/facebook/ads/redexgen/X/9Q;->A0I:Lcom/facebook/ads/redexgen/X/NQ;

    .line 19634
    new-instance v9, Lcom/facebook/ads/redexgen/X/9S;

    invoke-direct {v9, v2}, Lcom/facebook/ads/redexgen/X/9S;-><init>(Lcom/facebook/ads/redexgen/X/9Q;)V

    iput-object v9, v2, Lcom/facebook/ads/redexgen/X/9Q;->A0D:Lcom/facebook/ads/redexgen/X/Pu;

    .line 19635
    new-instance v7, Lcom/facebook/ads/redexgen/X/9R;

    invoke-direct {v7, v2}, Lcom/facebook/ads/redexgen/X/9R;-><init>(Lcom/facebook/ads/redexgen/X/9Q;)V

    iput-object v7, v2, Lcom/facebook/ads/redexgen/X/9Q;->A0E:Lcom/facebook/ads/redexgen/X/Ps;

    .line 19636
    const/4 v3, 0x0

    iput-boolean v3, v2, Lcom/facebook/ads/redexgen/X/9Q;->A07:Z

    .line 19637
    iput-boolean v3, v2, Lcom/facebook/ads/redexgen/X/9Q;->A08:Z

    .line 19638
    iput-boolean v3, v2, Lcom/facebook/ads/redexgen/X/9Q;->A06:Z

    .line 19639
    iput-boolean v3, v2, Lcom/facebook/ads/redexgen/X/9Q;->A05:Z

    .line 19640
    iput-boolean v3, v2, Lcom/facebook/ads/redexgen/X/9Q;->A04:Z

    .line 19641
    iget-object v0, v2, Lcom/facebook/ads/redexgen/X/Ss;->A03:Lcom/facebook/ads/redexgen/X/Yn;

    new-instance v5, Lcom/facebook/ads/redexgen/X/SA;

    invoke-direct {v5, v0}, Lcom/facebook/ads/redexgen/X/SA;-><init>(Lcom/facebook/ads/redexgen/X/Yn;)V

    iput-object v5, v2, Lcom/facebook/ads/redexgen/X/9Q;->A0B:Lcom/facebook/ads/redexgen/X/SA;

    .line 19642
    iget-object v0, v2, Lcom/facebook/ads/redexgen/X/Ss;->A05:Lcom/facebook/ads/redexgen/X/JA;

    invoke-virtual {v5, v0}, Lcom/facebook/ads/redexgen/X/SA;->setFunnelLoggingHandler(Lcom/facebook/ads/redexgen/X/JA;)V

    .line 19643
    invoke-virtual {v5}, Lcom/facebook/ads/redexgen/X/SA;->getEventBus()Lcom/facebook/ads/redexgen/X/8r;

    move-result-object v6

    const/4 v0, 0x6

    new-array v4, v0, [Lcom/facebook/ads/redexgen/X/8s;

    aput-object v1, v4, v3

    const/4 v1, 0x1

    aput-object v12, v4, v1

    const/4 v0, 0x2

    aput-object v11, v4, v0

    const/4 v0, 0x3

    aput-object v10, v4, v0

    const/4 v0, 0x4

    aput-object v9, v4, v0

    const/4 v0, 0x5

    aput-object v7, v4, v0

    .line 19644
    invoke-virtual {v6, v4}, Lcom/facebook/ads/redexgen/X/8r;->A03([Lcom/facebook/ads/redexgen/X/8s;)V

    .line 19645
    iget-object v7, v2, Lcom/facebook/ads/redexgen/X/Ss;->A03:Lcom/facebook/ads/redexgen/X/Yn;

    iget-object v6, v2, Lcom/facebook/ads/redexgen/X/Ss;->A04:Lcom/facebook/ads/redexgen/X/J2;

    iget-object v0, v2, Lcom/facebook/ads/redexgen/X/Ss;->A01:Lcom/facebook/ads/redexgen/X/b5;

    .line 19646
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/b5;->A12()Ljava/lang/String;

    move-result-object v0

    new-instance v4, Lcom/facebook/ads/redexgen/X/9I;

    invoke-direct {v4, v7, v6, v5, v0}, Lcom/facebook/ads/redexgen/X/9I;-><init>(Lcom/facebook/ads/redexgen/X/Yn;Lcom/facebook/ads/redexgen/X/J2;Lcom/facebook/ads/redexgen/X/SA;Ljava/lang/String;)V

    iput-object v4, v2, Lcom/facebook/ads/redexgen/X/9Q;->A0C:Lcom/facebook/ads/redexgen/X/9I;

    .line 19647
    iget-object v0, v2, Lcom/facebook/ads/redexgen/X/Ss;->A03:Lcom/facebook/ads/redexgen/X/Yn;

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/Ih;->A1X(Landroid/content/Context;)Z

    move-result v0

    if-eqz v0, :cond_5

    .line 19648
    new-instance v9, Lcom/facebook/ads/redexgen/X/SF;

    iget-object v10, v2, Lcom/facebook/ads/redexgen/X/Ss;->A03:Lcom/facebook/ads/redexgen/X/Yn;

    iget-object v11, v2, Lcom/facebook/ads/redexgen/X/Ss;->A04:Lcom/facebook/ads/redexgen/X/J2;

    iget-object v0, v2, Lcom/facebook/ads/redexgen/X/Ss;->A01:Lcom/facebook/ads/redexgen/X/b5;

    .line 19649
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/b5;->A12()Ljava/lang/String;

    move-result-object v13

    const/4 v15, 0x0

    move-object v14, v4

    move-object v12, v5

    invoke-direct/range {v9 .. v15}, Lcom/facebook/ads/redexgen/X/SF;-><init>(Lcom/facebook/ads/redexgen/X/Yn;Lcom/facebook/ads/redexgen/X/J2;Lcom/facebook/ads/redexgen/X/SA;Ljava/lang/String;Lcom/facebook/ads/redexgen/X/QS;Ljava/util/Map;)V

    iput-object v9, v2, Lcom/facebook/ads/redexgen/X/9Q;->A0A:Lcom/facebook/ads/redexgen/X/SF;

    .line 19650
    :goto_0
    iget-object v7, v2, Lcom/facebook/ads/redexgen/X/Ss;->A03:Lcom/facebook/ads/redexgen/X/Yn;

    iget-object v6, v2, Lcom/facebook/ads/redexgen/X/Ss;->A09:Lcom/facebook/ads/redexgen/X/Mj;

    iget-object v0, v2, Lcom/facebook/ads/redexgen/X/Ss;->A01:Lcom/facebook/ads/redexgen/X/b5;

    .line 19651
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/1F;->A0T()Ljava/lang/String;

    move-result-object v4

    new-instance v0, Lcom/facebook/ads/redexgen/X/Q7;

    invoke-direct {v0, v7, v6, v4, v8}, Lcom/facebook/ads/redexgen/X/Q7;-><init>(Lcom/facebook/ads/redexgen/X/Yn;Lcom/facebook/ads/redexgen/X/Mj;Ljava/lang/String;Lcom/facebook/ads/redexgen/X/MC;)V

    iput-object v0, v2, Lcom/facebook/ads/redexgen/X/9Q;->A01:Lcom/facebook/ads/redexgen/X/Q7;

    .line 19652
    iget-object v6, v2, Lcom/facebook/ads/redexgen/X/Ss;->A03:Lcom/facebook/ads/redexgen/X/Yn;

    iget-object v4, v2, Lcom/facebook/ads/redexgen/X/Ss;->A05:Lcom/facebook/ads/redexgen/X/JA;

    new-instance v0, Lcom/facebook/ads/redexgen/X/Kw;

    invoke-direct {v0, v6, v4}, Lcom/facebook/ads/redexgen/X/Kw;-><init>(Lcom/facebook/ads/redexgen/X/Yn;Lcom/facebook/ads/redexgen/X/JA;)V

    iput-object v0, v2, Lcom/facebook/ads/redexgen/X/9Q;->A0J:Lcom/facebook/ads/redexgen/X/Kw;

    .line 19653
    iget-object v4, v2, Lcom/facebook/ads/redexgen/X/Ss;->A03:Lcom/facebook/ads/redexgen/X/Yn;

    new-instance v0, Lcom/facebook/ads/redexgen/X/KP;

    invoke-direct {v0, v4}, Lcom/facebook/ads/redexgen/X/KP;-><init>(Lcom/facebook/ads/redexgen/X/Yn;)V

    iput-object v0, v2, Lcom/facebook/ads/redexgen/X/9Q;->A0K:Lcom/facebook/ads/redexgen/X/KP;

    .line 19654
    invoke-direct {v2}, Lcom/facebook/ads/redexgen/X/9Q;->A07()V

    .line 19655
    iget-object v0, v2, Lcom/facebook/ads/redexgen/X/Ss;->A01:Lcom/facebook/ads/redexgen/X/b5;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/b5;->A0x()Lcom/facebook/ads/redexgen/X/1G;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/1G;->A0D()Lcom/facebook/ads/redexgen/X/1J;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/1J;->A08()Ljava/lang/String;

    move-result-object v4

    .line 19656
    .local v2, "videoUrl":Ljava/lang/String;
    iget-object v0, v2, Lcom/facebook/ads/redexgen/X/Ss;->A02:Lcom/facebook/ads/redexgen/X/6c;

    invoke-virtual {v0, v4}, Lcom/facebook/ads/redexgen/X/6c;->A0S(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v5, v0}, Lcom/facebook/ads/redexgen/X/SA;->setVideoURI(Ljava/lang/String;)V

    .line 19657
    invoke-direct {v2}, Lcom/facebook/ads/redexgen/X/9Q;->A05()V

    .line 19658
    iget-object v0, v2, Lcom/facebook/ads/redexgen/X/Ss;->A01:Lcom/facebook/ads/redexgen/X/b5;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/b5;->A0x()Lcom/facebook/ads/redexgen/X/1G;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/1G;->A0D()Lcom/facebook/ads/redexgen/X/1J;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/1J;->A03()I

    move-result v4

    .line 19659
    .local v3, "unskippableSeconds":I
    iget-object v0, v2, Lcom/facebook/ads/redexgen/X/Ss;->A03:Lcom/facebook/ads/redexgen/X/Yn;

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/Ih;->A1w(Landroid/content/Context;)Z

    move-result v0

    if-eqz v0, :cond_3

    .line 19660
    if-nez v4, :cond_2

    const/4 v0, 0x1

    :goto_1
    iput-boolean v0, v2, Lcom/facebook/ads/redexgen/X/9Q;->A05:Z

    .line 19661
    :goto_2
    iget-object v0, v2, Lcom/facebook/ads/redexgen/X/Ss;->A01:Lcom/facebook/ads/redexgen/X/b5;

    .line 19662
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/b5;->A0x()Lcom/facebook/ads/redexgen/X/1G;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/1G;->A0M()Z

    move-result v0

    if-eqz v0, :cond_0

    iget-object v0, v2, Lcom/facebook/ads/redexgen/X/Ss;->A01:Lcom/facebook/ads/redexgen/X/b5;

    .line 19663
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/b5;->A0x()Lcom/facebook/ads/redexgen/X/1G;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/1G;->A0D()Lcom/facebook/ads/redexgen/X/1J;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/1J;->A02()I

    move-result v0

    if-lez v0, :cond_0

    const/4 v3, 0x1

    :cond_0
    iput-boolean v3, v2, Lcom/facebook/ads/redexgen/X/9Q;->A03:Z

    .line 19664
    iget-object v0, v2, Lcom/facebook/ads/redexgen/X/Ss;->A01:Lcom/facebook/ads/redexgen/X/b5;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/b5;->A0w()Lcom/facebook/ads/redexgen/X/1C;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/1C;->A01()Lcom/facebook/ads/redexgen/X/1P;

    move-result-object v0

    .line 19665
    .local v4, "colors":Lcom/facebook/ads/redexgen/X/1P;
    invoke-virtual {v0, v1}, Lcom/facebook/ads/redexgen/X/1P;->A07(Z)I

    move-result v0

    invoke-static {v2, v0}, Lcom/facebook/ads/redexgen/X/Lo;->A0M(Landroid/view/View;I)V

    .line 19666
    iget-object v0, v2, Lcom/facebook/ads/redexgen/X/Ss;->A03:Lcom/facebook/ads/redexgen/X/Yn;

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/Ih;->A1q(Landroid/content/Context;)Z

    move-result v0

    if-eqz v0, :cond_1

    .line 19667
    iget-object v0, v2, Lcom/facebook/ads/redexgen/X/Ss;->A07:Lcom/facebook/ads/redexgen/X/MB;

    invoke-virtual {v0, v1}, Lcom/facebook/ads/redexgen/X/MB;->setProgressSpinnerInvisible(Z)V

    .line 19668
    :cond_1
    return-void

    .line 19669
    :cond_2
    const/4 v0, 0x0

    goto :goto_1

    .line 19670
    :cond_3
    if-gtz v4, :cond_4

    const/4 v0, 0x1

    :goto_3
    iput-boolean v0, v2, Lcom/facebook/ads/redexgen/X/9Q;->A05:Z

    goto :goto_2

    :cond_4
    const/4 v0, 0x0

    goto :goto_3

    .line 19671
    :cond_5
    const/4 v0, 0x0

    iput-object v0, v2, Lcom/facebook/ads/redexgen/X/9Q;->A0A:Lcom/facebook/ads/redexgen/X/SF;

    goto/16 :goto_0
.end method

.method private A00(I)Lcom/facebook/ads/redexgen/X/OL;
    .locals 9

    .line 19672
    new-instance v1, Lcom/facebook/ads/redexgen/X/OO;

    iget-object v2, p0, Lcom/facebook/ads/redexgen/X/Ss;->A03:Lcom/facebook/ads/redexgen/X/Yn;

    iget-object v3, p0, Lcom/facebook/ads/redexgen/X/Ss;->A04:Lcom/facebook/ads/redexgen/X/J2;

    iget-object v4, p0, Lcom/facebook/ads/redexgen/X/Ss;->A08:Lcom/facebook/ads/redexgen/X/MC;

    iget-object v5, p0, Lcom/facebook/ads/redexgen/X/Ss;->A01:Lcom/facebook/ads/redexgen/X/b5;

    iget-object v6, p0, Lcom/facebook/ads/redexgen/X/9Q;->A0B:Lcom/facebook/ads/redexgen/X/SA;

    iget-object v7, p0, Lcom/facebook/ads/redexgen/X/Ss;->A0A:Lcom/facebook/ads/redexgen/X/RE;

    iget-object v8, p0, Lcom/facebook/ads/redexgen/X/Ss;->A06:Lcom/facebook/ads/redexgen/X/Lg;

    invoke-direct/range {v1 .. v8}, Lcom/facebook/ads/redexgen/X/OO;-><init>(Lcom/facebook/ads/redexgen/X/Yn;Lcom/facebook/ads/redexgen/X/J2;Lcom/facebook/ads/redexgen/X/MC;Lcom/facebook/ads/redexgen/X/b5;Landroid/view/View;Lcom/facebook/ads/redexgen/X/RE;Lcom/facebook/ads/redexgen/X/Lg;)V

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ss;->A07:Lcom/facebook/ads/redexgen/X/MB;

    .line 19673
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/MB;->getToolbarHeight()I

    move-result v0

    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/OO;->A0E(I)Lcom/facebook/ads/redexgen/X/OO;

    move-result-object v1

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ss;->A07:Lcom/facebook/ads/redexgen/X/MB;

    .line 19674
    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/OO;->A0H(Lcom/facebook/ads/redexgen/X/MB;)Lcom/facebook/ads/redexgen/X/OO;

    move-result-object v0

    .line 19675
    invoke-virtual {v0, p1}, Lcom/facebook/ads/redexgen/X/OO;->A0D(I)Lcom/facebook/ads/redexgen/X/OO;

    move-result-object v1

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/9Q;->A0J:Lcom/facebook/ads/redexgen/X/Kw;

    .line 19676
    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/OO;->A0F(Landroid/view/View;)Lcom/facebook/ads/redexgen/X/OO;

    move-result-object v1

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/9Q;->A0K:Lcom/facebook/ads/redexgen/X/KP;

    .line 19677
    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/OO;->A0I(Lcom/facebook/ads/redexgen/X/KP;)Lcom/facebook/ads/redexgen/X/OO;

    move-result-object v0

    .line 19678
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/OO;->A0K()Lcom/facebook/ads/redexgen/X/OP;

    move-result-object v2

    .line 19679
    .local v0, "params":Lcom/facebook/ads/redexgen/X/OP;
    const/4 v1, 0x0

    const/4 v0, 0x1

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/OM;->A00(Lcom/facebook/ads/redexgen/X/OP;Landroid/os/Bundle;Z)Lcom/facebook/ads/redexgen/X/OL;

    move-result-object v0

    return-object v0
.end method

.method public static synthetic A01(Lcom/facebook/ads/redexgen/X/9Q;)Lcom/facebook/ads/redexgen/X/OL;
    .locals 0

    .line 19680
    iget-object p0, p0, Lcom/facebook/ads/redexgen/X/9Q;->A00:Lcom/facebook/ads/redexgen/X/OL;

    return-object p0
.end method

.method public static synthetic A02(Lcom/facebook/ads/redexgen/X/9Q;)Lcom/facebook/ads/redexgen/X/SA;
    .locals 0

    .line 19681
    iget-object p0, p0, Lcom/facebook/ads/redexgen/X/9Q;->A0B:Lcom/facebook/ads/redexgen/X/SA;

    return-object p0
.end method

.method public static A03(III)Ljava/lang/String;
    .locals 2

    sget-object v1, Lcom/facebook/ads/redexgen/X/9Q;->A0L:[B

    add-int v0, p0, p1

    invoke-static {v1, p0, v0}, Ljava/util/Arrays;->copyOfRange([BII)[B

    move-result-object p0

    const/4 v1, 0x0

    :goto_0
    array-length v0, p0

    if-ge v1, v0, :cond_0

    aget-byte v0, p0, v1

    sub-int/2addr v0, p2

    add-int/lit8 v0, v0, -0x58

    int-to-byte v0, v0

    aput-byte v0, p0, v1

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_0
    new-instance v0, Ljava/lang/String;

    invoke-direct {v0, p0}, Ljava/lang/String;-><init>([B)V

    return-object v0
.end method

.method private A04()V
    .locals 4

    .line 19682
    iget-object v3, p0, Lcom/facebook/ads/redexgen/X/9Q;->A0B:Lcom/facebook/ads/redexgen/X/SA;

    new-instance v2, Lcom/facebook/ads/redexgen/X/Sg;

    invoke-direct {v2, p0}, Lcom/facebook/ads/redexgen/X/Sg;-><init>(Lcom/facebook/ads/redexgen/X/9Q;)V

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ss;->A03:Lcom/facebook/ads/redexgen/X/Yn;

    .line 19683
    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/Ih;->A0K(Landroid/content/Context;)I

    move-result v0

    int-to-long v0, v0

    .line 19684
    invoke-virtual {v3, v2, v0, v1}, Lcom/facebook/ads/redexgen/X/SA;->postDelayed(Ljava/lang/Runnable;J)Z

    .line 19685
    return-void
.end method

.method private A05()V
    .locals 4

    .line 19686
    iget-object v3, p0, Lcom/facebook/ads/redexgen/X/9Q;->A0B:Lcom/facebook/ads/redexgen/X/SA;

    new-instance v2, Lcom/facebook/ads/redexgen/X/Sf;

    invoke-direct {v2, p0}, Lcom/facebook/ads/redexgen/X/Sf;-><init>(Lcom/facebook/ads/redexgen/X/9Q;)V

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ss;->A03:Lcom/facebook/ads/redexgen/X/Yn;

    .line 19687
    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/Ih;->A0L(Landroid/content/Context;)I

    move-result v0

    int-to-long v0, v0

    .line 19688
    invoke-virtual {v3, v2, v0, v1}, Lcom/facebook/ads/redexgen/X/SA;->postDelayed(Ljava/lang/Runnable;J)Z

    .line 19689
    return-void
.end method

.method private A06()V
    .locals 1

    .line 19690
    const/4 v0, 0x1

    iput-boolean v0, p0, Lcom/facebook/ads/redexgen/X/9Q;->A05:Z

    .line 19691
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ss;->A07:Lcom/facebook/ads/redexgen/X/MB;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/MB;->A04()V

    .line 19692
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/9Q;->A00:Lcom/facebook/ads/redexgen/X/OL;

    if-eqz v0, :cond_0

    .line 19693
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/OL;->A0y()V

    .line 19694
    :cond_0
    return-void
.end method

.method private A07()V
    .locals 5

    .line 19695
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/9Q;->A0B:Lcom/facebook/ads/redexgen/X/SA;

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/9Q;->A0K:Lcom/facebook/ads/redexgen/X/KP;

    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/SA;->A0c(Lcom/facebook/ads/redexgen/X/QN;)V

    .line 19696
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/9Q;->A0B:Lcom/facebook/ads/redexgen/X/SA;

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/9Q;->A0J:Lcom/facebook/ads/redexgen/X/Kw;

    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/SA;->A0c(Lcom/facebook/ads/redexgen/X/QN;)V

    .line 19697
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ss;->A01:Lcom/facebook/ads/redexgen/X/b5;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/b5;->A0x()Lcom/facebook/ads/redexgen/X/1G;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/1G;->A0D()Lcom/facebook/ads/redexgen/X/1J;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/1J;->A07()Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    if-nez v0, :cond_0

    .line 19698
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ss;->A03:Lcom/facebook/ads/redexgen/X/Yn;

    new-instance v1, Lcom/facebook/ads/redexgen/X/7R;

    invoke-direct {v1, v0}, Lcom/facebook/ads/redexgen/X/7R;-><init>(Lcom/facebook/ads/redexgen/X/Yn;)V

    .line 19699
    .local v0, "placeholderImagePlugin":Lcom/facebook/ads/redexgen/X/7R;
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/9Q;->A0B:Lcom/facebook/ads/redexgen/X/SA;

    invoke-virtual {v0, v1}, Lcom/facebook/ads/redexgen/X/SA;->A0c(Lcom/facebook/ads/redexgen/X/QN;)V

    .line 19700
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ss;->A01:Lcom/facebook/ads/redexgen/X/b5;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/b5;->A0x()Lcom/facebook/ads/redexgen/X/1G;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/1G;->A0D()Lcom/facebook/ads/redexgen/X/1J;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/1J;->A07()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/7R;->setImage(Ljava/lang/String;)V

    .line 19701
    .end local v0    # "placeholderImagePlugin":Lcom/facebook/ads/redexgen/X/7R;
    :cond_0
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/Ss;->A03:Lcom/facebook/ads/redexgen/X/Yn;

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ss;->A05:Lcom/facebook/ads/redexgen/X/JA;

    const/4 v4, 0x1

    new-instance v3, Lcom/facebook/ads/redexgen/X/7C;

    invoke-direct {v3, v1, v4, v0}, Lcom/facebook/ads/redexgen/X/7C;-><init>(Lcom/facebook/ads/redexgen/X/Yn;ZLcom/facebook/ads/redexgen/X/JA;)V

    .line 19702
    .local v0, "touchPlayPausePlugin":Lcom/facebook/ads/redexgen/X/7C;
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/9Q;->A0B:Lcom/facebook/ads/redexgen/X/SA;

    invoke-virtual {v0, v3}, Lcom/facebook/ads/redexgen/X/SA;->A0c(Lcom/facebook/ads/redexgen/X/QN;)V

    .line 19703
    iget-object v2, p0, Lcom/facebook/ads/redexgen/X/9Q;->A0B:Lcom/facebook/ads/redexgen/X/SA;

    sget-object v1, Lcom/facebook/ads/redexgen/X/QZ;->A03:Lcom/facebook/ads/redexgen/X/QZ;

    new-instance v0, Lcom/facebook/ads/redexgen/X/L7;

    invoke-direct {v0, v3, v1, v4}, Lcom/facebook/ads/redexgen/X/L7;-><init>(Landroid/view/View;Lcom/facebook/ads/redexgen/X/QZ;Z)V

    invoke-virtual {v2, v0}, Lcom/facebook/ads/redexgen/X/SA;->A0c(Lcom/facebook/ads/redexgen/X/QN;)V

    .line 19704
    iget-object v2, p0, Lcom/facebook/ads/redexgen/X/9Q;->A0B:Lcom/facebook/ads/redexgen/X/SA;

    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/Ss;->A03:Lcom/facebook/ads/redexgen/X/Yn;

    new-instance v0, Lcom/facebook/ads/redexgen/X/7H;

    invoke-direct {v0, v1}, Lcom/facebook/ads/redexgen/X/7H;-><init>(Lcom/facebook/ads/redexgen/X/Yn;)V

    invoke-virtual {v2, v0}, Lcom/facebook/ads/redexgen/X/SA;->A0c(Lcom/facebook/ads/redexgen/X/QN;)V

    .line 19705
    return-void
.end method

.method public static A08()V
    .locals 1

    const/4 v0, 0x6

    new-array v0, v0, [B

    fill-array-data v0, :array_0

    sput-object v0, Lcom/facebook/ads/redexgen/X/9Q;->A0L:[B

    return-void

    nop

    :array_0
    .array-data 1
        -0x3t
        0x15t
        0x7t
        0x5t
        0x15t
        -0x1t
    .end array-data
.end method

.method public static synthetic A09(Lcom/facebook/ads/redexgen/X/9Q;Lcom/facebook/ads/redexgen/X/9H;ZZ)V
    .locals 0

    .line 19706
    invoke-direct {p0, p1, p2, p3}, Lcom/facebook/ads/redexgen/X/9Q;->A0D(Lcom/facebook/ads/redexgen/X/9H;ZZ)V

    return-void
.end method

.method public static synthetic A0A(Lcom/facebook/ads/redexgen/X/9Q;Lcom/facebook/ads/redexgen/X/93;)V
    .locals 0

    .line 19707
    invoke-direct {p0, p1}, Lcom/facebook/ads/redexgen/X/9Q;->A0F(Lcom/facebook/ads/redexgen/X/93;)V

    return-void
.end method

.method public static synthetic A0B(Lcom/facebook/ads/redexgen/X/9Q;Lcom/facebook/ads/redexgen/X/93;)V
    .locals 0

    .line 19708
    invoke-direct {p0, p1}, Lcom/facebook/ads/redexgen/X/9Q;->A0E(Lcom/facebook/ads/redexgen/X/93;)V

    return-void
.end method

.method public static synthetic A0C(Lcom/facebook/ads/redexgen/X/9Q;Ljava/lang/String;)V
    .locals 0

    .line 19709
    invoke-direct {p0, p1}, Lcom/facebook/ads/redexgen/X/9Q;->A0G(Ljava/lang/String;)V

    return-void
.end method

.method private A0D(Lcom/facebook/ads/redexgen/X/9H;ZZ)V
    .locals 4

    .line 19710
    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/9Q;->A06:Z

    if-eqz v0, :cond_0

    .line 19711
    return-void

    .line 19712
    :cond_0
    const/4 v3, 0x1

    sget-object v1, Lcom/facebook/ads/redexgen/X/9Q;->A0M:[Ljava/lang/String;

    const/4 v0, 0x6

    aget-object v0, v1, v0

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v1

    const/16 v0, 0x1a

    if-eq v1, v0, :cond_1

    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    :cond_1
    sget-object v2, Lcom/facebook/ads/redexgen/X/9Q;->A0M:[Ljava/lang/String;

    const-string v1, "N5ZIPH8mvSXK1CHoPTlSEsF"

    const/4 v0, 0x2

    aput-object v1, v2, v0

    const-string v1, "PVGkj9Ta"

    const/4 v0, 0x7

    aput-object v1, v2, v0

    iput-boolean v3, p0, Lcom/facebook/ads/redexgen/X/9Q;->A06:Z

    .line 19713
    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/9Q;->A05:Z

    if-nez v0, :cond_2

    .line 19714
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/9Q;->A06()V

    .line 19715
    :cond_2
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/9Q;->A00:Lcom/facebook/ads/redexgen/X/OL;

    if-eqz v0, :cond_3

    .line 19716
    invoke-virtual {v0, p1}, Lcom/facebook/ads/redexgen/X/OL;->A0B(Lcom/facebook/ads/redexgen/X/9H;)V

    .line 19717
    :cond_3
    iget-object v3, p0, Lcom/facebook/ads/redexgen/X/Ss;->A07:Lcom/facebook/ads/redexgen/X/MB;

    const/4 v2, 0x0

    const/4 v1, 0x0

    const/16 v0, 0x19

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/9Q;->A03(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v3, v0}, Lcom/facebook/ads/redexgen/X/MB;->setToolbarActionMessage(Ljava/lang/String;)V

    .line 19718
    invoke-direct {p0, p2, p3}, Lcom/facebook/ads/redexgen/X/9Q;->A0H(ZZ)V

    .line 19719
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/Ss;->A07:Lcom/facebook/ads/redexgen/X/MB;

    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/9Q;->getCloseButtonStyle()I

    move-result v0

    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/MB;->setToolbarActionMode(I)V

    .line 19720
    return-void
.end method

.method private A0E(Lcom/facebook/ads/redexgen/X/93;)V
    .locals 4

    .line 19721
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/9Q;->A0B:Lcom/facebook/ads/redexgen/X/SA;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/SA;->getState()Lcom/facebook/ads/redexgen/X/RB;

    move-result-object v1

    sget-object v0, Lcom/facebook/ads/redexgen/X/RB;->A02:Lcom/facebook/ads/redexgen/X/RB;

    if-eq v1, v0, :cond_0

    .line 19722
    return-void

    .line 19723
    :cond_0
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ss;->A03:Lcom/facebook/ads/redexgen/X/Yn;

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/Ih;->A1D(Landroid/content/Context;)Z

    move-result v0

    if-eqz v0, :cond_1

    .line 19724
    iget-object v3, p0, Lcom/facebook/ads/redexgen/X/9Q;->A0B:Lcom/facebook/ads/redexgen/X/SA;

    new-instance v2, Lcom/facebook/ads/redexgen/X/Si;

    invoke-direct {v2, p0, p1}, Lcom/facebook/ads/redexgen/X/Si;-><init>(Lcom/facebook/ads/redexgen/X/9Q;Lcom/facebook/ads/redexgen/X/93;)V

    const-wide/16 v0, 0x1388

    invoke-virtual {v3, v2, v0, v1}, Lcom/facebook/ads/redexgen/X/SA;->postDelayed(Ljava/lang/Runnable;J)Z

    .line 19725
    :cond_1
    return-void
.end method

.method private A0F(Lcom/facebook/ads/redexgen/X/93;)V
    .locals 7

    .line 19726
    invoke-virtual {p1}, Lcom/facebook/ads/redexgen/X/Pv;->A00()I

    move-result v4

    .line 19727
    .local v0, "currentPosMs":I
    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/9Q;->A03:Z

    if-eqz v0, :cond_1

    .line 19728
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ss;->A01:Lcom/facebook/ads/redexgen/X/b5;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/b5;->A0x()Lcom/facebook/ads/redexgen/X/1G;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/1G;->A0D()Lcom/facebook/ads/redexgen/X/1J;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/1J;->A02()I

    move-result v6

    .line 19729
    .local v1, "totalSecondsForReward":I
    div-int/lit16 v0, v4, 0x3e8

    sub-int/2addr v6, v0

    .line 19730
    .local v2, "remainingSecondsForReward":I
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ss;->A01:Lcom/facebook/ads/redexgen/X/b5;

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/Tl;->A08(Lcom/facebook/ads/redexgen/X/b5;)Z

    move-result v0

    if-nez v0, :cond_0

    .line 19731
    if-lez v6, :cond_3

    .line 19732
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ss;->A01:Lcom/facebook/ads/redexgen/X/b5;

    .line 19733
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/b5;->A11()Lcom/facebook/ads/redexgen/X/1g;

    move-result-object v0

    .line 19734
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/1g;->A02()Ljava/lang/String;

    move-result-object v5

    .line 19735
    invoke-static {v6}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    move-result-object v3

    const/4 v2, 0x0

    const/4 v1, 0x6

    const/16 v0, 0x4a

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/9Q;->A03(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v5, v0, v3}, Ljava/lang/String;->replace(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Ljava/lang/String;

    move-result-object v1

    .line 19736
    .local v3, "rewardMessage":Ljava/lang/String;
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ss;->A07:Lcom/facebook/ads/redexgen/X/MB;

    invoke-virtual {v0, v1}, Lcom/facebook/ads/redexgen/X/MB;->setToolbarActionMessage(Ljava/lang/String;)V

    .line 19737
    .end local v3    # "rewardMessage":Ljava/lang/String;
    :cond_0
    :goto_0
    if-gtz v6, :cond_1

    .line 19738
    const/4 v3, 0x0

    sget-object v2, Lcom/facebook/ads/redexgen/X/9Q;->A0M:[Ljava/lang/String;

    const/4 v0, 0x2

    aget-object v1, v2, v0

    const/4 v0, 0x7

    aget-object v0, v2, v0

    invoke-virtual {v1}, Ljava/lang/String;->length()I

    move-result v1

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v0

    if-eq v1, v0, :cond_4

    sget-object v2, Lcom/facebook/ads/redexgen/X/9Q;->A0M:[Ljava/lang/String;

    const-string v1, "PXM0w4DTcyhv6xVVxlaJPbpxowOdlGPl"

    const/4 v0, 0x0

    aput-object v1, v2, v0

    const-string v1, "ZagmJHoPVKqTZKI94Zh80WtjAbW4VeCE"

    const/4 v0, 0x4

    aput-object v1, v2, v0

    invoke-direct {p0, v3, v3}, Lcom/facebook/ads/redexgen/X/9Q;->A0H(ZZ)V

    .line 19739
    .end local v1    # "totalSecondsForReward":I
    .end local v2    # "remainingSecondsForReward":I
    :cond_1
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ss;->A01:Lcom/facebook/ads/redexgen/X/b5;

    .line 19740
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/b5;->A0x()Lcom/facebook/ads/redexgen/X/1G;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/1G;->A0D()Lcom/facebook/ads/redexgen/X/1J;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/1J;->A03()I

    move-result v0

    int-to-float v1, v0

    const/high16 v0, 0x447a0000    # 1000.0f

    mul-float/2addr v1, v0

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/9Q;->A0B:Lcom/facebook/ads/redexgen/X/SA;

    .line 19741
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/SA;->getDuration()I

    move-result v0

    int-to-float v0, v0

    .line 19742
    invoke-static {v1, v0}, Ljava/lang/Math;->min(FF)F

    move-result v0

    .line 19743
    .local v1, "unskippableSeconds":F
    int-to-float v2, v4

    div-float/2addr v2, v0

    .line 19744
    .local v2, "seenPercentage":F
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/Ss;->A07:Lcom/facebook/ads/redexgen/X/MB;

    const/high16 v0, 0x42c80000    # 100.0f

    mul-float/2addr v0, v2

    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/MB;->setProgress(F)V

    .line 19745
    const/high16 v0, 0x3f800000    # 1.0f

    cmpl-float v0, v2, v0

    if-ltz v0, :cond_2

    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/9Q;->A05:Z

    if-nez v0, :cond_2

    .line 19746
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/9Q;->A06()V

    .line 19747
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/Ss;->A07:Lcom/facebook/ads/redexgen/X/MB;

    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/9Q;->getCloseButtonStyle()I

    move-result v0

    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/MB;->setToolbarActionMode(I)V

    .line 19748
    :cond_2
    return-void

    .line 19749
    :cond_3
    iget-object v3, p0, Lcom/facebook/ads/redexgen/X/Ss;->A07:Lcom/facebook/ads/redexgen/X/MB;

    const/4 v2, 0x0

    const/4 v1, 0x0

    const/16 v0, 0x19

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/9Q;->A03(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v3, v0}, Lcom/facebook/ads/redexgen/X/MB;->setToolbarActionMessage(Ljava/lang/String;)V

    goto :goto_0

    :cond_4
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0
.end method

.method private A0G(Ljava/lang/String;)V
    .locals 5

    .line 19750
    const/4 v4, 0x0

    .line 19751
    .local v0, "currentPositionMS":I
    const/4 v3, 0x0

    .line 19752
    .local v1, "duration":I
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/9Q;->A0B:Lcom/facebook/ads/redexgen/X/SA;

    if-eqz v0, :cond_0

    .line 19753
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/SA;->getCurrentPositionInMillis()I

    move-result v4

    .line 19754
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/9Q;->A0B:Lcom/facebook/ads/redexgen/X/SA;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/SA;->getDuration()I

    move-result v3

    .line 19755
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/9Q;->A0B:Lcom/facebook/ads/redexgen/X/SA;

    const/4 v0, 0x3

    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/SA;->A0Z(I)V

    .line 19756
    :cond_0
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ss;->A03:Lcom/facebook/ads/redexgen/X/Yn;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/Yn;->A0E()Lcom/facebook/ads/redexgen/X/0S;

    move-result-object v0

    invoke-interface {v0, p1}, Lcom/facebook/ads/redexgen/X/0S;->A2z(Ljava/lang/String;)V

    .line 19757
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ss;->A03:Lcom/facebook/ads/redexgen/X/Yn;

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/Ih;->A1E(Landroid/content/Context;)Z

    move-result v0

    if-eqz v0, :cond_1

    .line 19758
    new-instance v2, Lcom/facebook/ads/redexgen/X/9H;

    invoke-direct {v2, v4, v3}, Lcom/facebook/ads/redexgen/X/9H;-><init>(II)V

    const/4 v1, 0x0

    const/4 v0, 0x1

    invoke-direct {p0, v2, v1, v0}, Lcom/facebook/ads/redexgen/X/9Q;->A0D(Lcom/facebook/ads/redexgen/X/9H;ZZ)V

    .line 19759
    :goto_0
    return-void

    .line 19760
    :cond_1
    iget-object v3, p0, Lcom/facebook/ads/redexgen/X/Ss;->A08:Lcom/facebook/ads/redexgen/X/MC;

    sget-object v1, Lcom/facebook/ads/redexgen/X/9Q;->A0M:[Ljava/lang/String;

    const/4 v0, 0x3

    aget-object v1, v1, v0

    const/4 v0, 0x0

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    const/16 v0, 0x7a

    if-eq v1, v0, :cond_2

    sget-object v2, Lcom/facebook/ads/redexgen/X/9Q;->A0M:[Ljava/lang/String;

    const-string v1, "uhCmlX27t5rzSWT78KYVdoY12DutI5Be"

    const/4 v0, 0x3

    aput-object v1, v2, v0

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ss;->A09:Lcom/facebook/ads/redexgen/X/Mj;

    invoke-interface {v0}, Lcom/facebook/ads/redexgen/X/Mj;->A6y()Ljava/lang/String;

    move-result-object v0

    invoke-interface {v3, v0}, Lcom/facebook/ads/redexgen/X/MC;->A43(Ljava/lang/String;)V

    .line 19761
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/Ss;->A08:Lcom/facebook/ads/redexgen/X/MC;

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ss;->A09:Lcom/facebook/ads/redexgen/X/Mj;

    invoke-interface {v0}, Lcom/facebook/ads/redexgen/X/Mj;->A6u()Ljava/lang/String;

    move-result-object v0

    invoke-interface {v1, v0}, Lcom/facebook/ads/redexgen/X/MC;->A43(Ljava/lang/String;)V

    goto :goto_0

    :cond_2
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0
.end method

.method private declared-synchronized A0H(ZZ)V
    .locals 3

    monitor-enter p0

    .line 19762
    :try_start_0
    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/9Q;->A03:Z

    const/4 v2, 0x0

    if-eqz v0, :cond_2

    if-nez p1, :cond_2

    if-eqz p2, :cond_0

    goto :goto_0

    .line 19763
    :cond_0
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/Ss;->A08:Lcom/facebook/ads/redexgen/X/MC;

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ss;->A09:Lcom/facebook/ads/redexgen/X/Mj;

    invoke-interface {v0}, Lcom/facebook/ads/redexgen/X/Mj;->A5s()Ljava/lang/String;

    move-result-object v0

    invoke-interface {v1, v0}, Lcom/facebook/ads/redexgen/X/MC;->A43(Ljava/lang/String;)V

    .line 19764
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/9Q;->A01:Lcom/facebook/ads/redexgen/X/Q7;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/Q7;->A05()V

    .line 19765
    iput-boolean v2, p0, Lcom/facebook/ads/redexgen/X/9Q;->A03:Z

    .line 19766
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/9Q;->A00:Lcom/facebook/ads/redexgen/X/OL;

    if-eqz v0, :cond_1

    .line 19767
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/OL;->A0w()V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 19768
    .end local p0    # "this":Lcom/facebook/ads/redexgen/X/9Q;
    :cond_1
    monitor-exit p0

    return-void

    .line 19769
    :cond_2
    :goto_0
    :try_start_1
    iput-boolean v2, p0, Lcom/facebook/ads/redexgen/X/9Q;->A03:Z
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    .line 19770
    monitor-exit p0

    return-void

    .line 19771
    .end local p1    # null:Z
    .end local p2    # null:Z
    :catchall_0
    move-exception v0

    monitor-exit p0

    throw v0
.end method

.method public static synthetic A0I(Lcom/facebook/ads/redexgen/X/9Q;)Z
    .locals 0

    .line 19772
    iget-boolean p0, p0, Lcom/facebook/ads/redexgen/X/9Q;->A05:Z

    return p0
.end method

.method public static synthetic A0J(Lcom/facebook/ads/redexgen/X/9Q;)Z
    .locals 0

    .line 19773
    iget-boolean p0, p0, Lcom/facebook/ads/redexgen/X/9Q;->A07:Z

    return p0
.end method

.method public static synthetic A0K(Lcom/facebook/ads/redexgen/X/9Q;)Z
    .locals 0

    .line 19774
    iget-boolean p0, p0, Lcom/facebook/ads/redexgen/X/9Q;->A08:Z

    return p0
.end method

.method public static synthetic A0L(Lcom/facebook/ads/redexgen/X/9Q;Z)Z
    .locals 0

    .line 19775
    iput-boolean p1, p0, Lcom/facebook/ads/redexgen/X/9Q;->A07:Z

    return p1
.end method

.method public static synthetic A0M(Lcom/facebook/ads/redexgen/X/9Q;Z)Z
    .locals 0

    .line 19776
    iput-boolean p1, p0, Lcom/facebook/ads/redexgen/X/9Q;->A08:Z

    return p1
.end method

.method private getCloseButtonStyle()I
    .locals 1

    .line 19813
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/9Q;->A00:Lcom/facebook/ads/redexgen/X/OL;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/OL;->getCloseButtonStyle()I

    move-result v0

    :goto_0
    return v0

    :cond_0
    const/4 v0, 0x0

    goto :goto_0
.end method


# virtual methods
.method public final A0Q()V
    .locals 3

    .line 19777
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/9Q;->A00:Lcom/facebook/ads/redexgen/X/OL;

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/Lo;->A0L(Landroid/view/View;)V

    .line 19778
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ss;->A07:Lcom/facebook/ads/redexgen/X/MB;

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/Lo;->A0L(Landroid/view/View;)V

    .line 19779
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/9Q;->A0B:Lcom/facebook/ads/redexgen/X/SA;

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ss;->A01:Lcom/facebook/ads/redexgen/X/b5;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/b5;->A0x()Lcom/facebook/ads/redexgen/X/1G;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/1G;->A0D()Lcom/facebook/ads/redexgen/X/1J;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/1J;->A09()Z

    move-result v0

    if-eqz v0, :cond_2

    const/4 v0, 0x0

    :goto_0
    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/SA;->setVolume(F)V

    .line 19780
    iget-object v2, p0, Lcom/facebook/ads/redexgen/X/9Q;->A0B:Lcom/facebook/ads/redexgen/X/SA;

    sget-object v1, Lcom/facebook/ads/redexgen/X/QM;->A02:Lcom/facebook/ads/redexgen/X/QM;

    const/16 v0, 0x14

    invoke-virtual {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/SA;->A0b(Lcom/facebook/ads/redexgen/X/QM;I)V

    .line 19781
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/9Q;->A00:Lcom/facebook/ads/redexgen/X/OL;

    if-eqz v1, :cond_0

    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/9Q;->A03:Z

    if-eqz v0, :cond_0

    .line 19782
    invoke-virtual {v1}, Lcom/facebook/ads/redexgen/X/OL;->A0x()V

    .line 19783
    :cond_0
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/9Q;->A00:Lcom/facebook/ads/redexgen/X/OL;

    if-eqz v1, :cond_1

    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/9Q;->A05:Z

    if-nez v0, :cond_1

    .line 19784
    invoke-virtual {v1}, Lcom/facebook/ads/redexgen/X/OL;->A0z()V

    .line 19785
    :cond_1
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/9Q;->A04()V

    .line 19786
    return-void

    .line 19787
    :cond_2
    const/high16 v0, 0x3f800000    # 1.0f

    goto :goto_0
.end method

.method public final A0S(Lcom/facebook/ads/redexgen/X/5V;)V
    .locals 4

    .line 19788
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/9Q;->A09:Lcom/facebook/ads/redexgen/X/5T;

    invoke-virtual {p1, v0}, Lcom/facebook/ads/redexgen/X/5V;->A0N(Lcom/facebook/ads/redexgen/X/5T;)V

    .line 19789
    invoke-virtual {p1}, Lcom/facebook/ads/redexgen/X/5V;->A0J()Lcom/facebook/ads/AudienceNetworkActivity;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/AudienceNetworkActivity;->getResources()Landroid/content/res/Resources;

    move-result-object v0

    invoke-virtual {v0}, Landroid/content/res/Resources;->getConfiguration()Landroid/content/res/Configuration;

    move-result-object v0

    iget v0, v0, Landroid/content/res/Configuration;->orientation:I

    .line 19790
    .local v0, "orientation":I
    invoke-direct {p0, v0}, Lcom/facebook/ads/redexgen/X/9Q;->A00(I)Lcom/facebook/ads/redexgen/X/OL;

    move-result-object v1

    iput-object v1, p0, Lcom/facebook/ads/redexgen/X/9Q;->A00:Lcom/facebook/ads/redexgen/X/OL;

    .line 19791
    sget-object v0, Lcom/facebook/ads/redexgen/X/Ss;->A0E:Landroid/widget/RelativeLayout$LayoutParams;

    invoke-virtual {p0, v1, v0}, Lcom/facebook/ads/redexgen/X/9Q;->addView(Landroid/view/View;Landroid/view/ViewGroup$LayoutParams;)V

    .line 19792
    iget-object v3, p0, Lcom/facebook/ads/redexgen/X/Ss;->A07:Lcom/facebook/ads/redexgen/X/MB;

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ss;->A07:Lcom/facebook/ads/redexgen/X/MB;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/MB;->getToolbarHeight()I

    move-result v2

    const/4 v1, -0x1

    new-instance v0, Landroid/widget/FrameLayout$LayoutParams;

    invoke-direct {v0, v1, v2}, Landroid/widget/FrameLayout$LayoutParams;-><init>(II)V

    invoke-virtual {p0, v3, v0}, Lcom/facebook/ads/redexgen/X/9Q;->addView(Landroid/view/View;Landroid/view/ViewGroup$LayoutParams;)V

    .line 19793
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/9Q;->A00:Lcom/facebook/ads/redexgen/X/OL;

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/Lo;->A0H(Landroid/view/View;)V

    .line 19794
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ss;->A07:Lcom/facebook/ads/redexgen/X/MB;

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/Lo;->A0H(Landroid/view/View;)V

    .line 19795
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/9Q;->A00:Lcom/facebook/ads/redexgen/X/OL;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/OL;->A11()Z

    move-result v0

    if-eqz v0, :cond_0

    const/4 v0, 0x1

    :goto_0
    invoke-virtual {p0, v0}, Lcom/facebook/ads/redexgen/X/Ss;->setUpFullscreenMode(Z)V

    .line 19796
    return-void

    .line 19797
    :cond_0
    const/4 v0, 0x0

    goto :goto_0
.end method

.method public final A0T()Z
    .locals 2

    .line 19798
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/9Q;->A00:Lcom/facebook/ads/redexgen/X/OL;

    if-eqz v1, :cond_0

    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/9Q;->A03:Z

    .line 19799
    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/OL;->A12(Z)Z

    move-result v0

    if-eqz v0, :cond_0

    const/4 v0, 0x1

    .line 19800
    :goto_0
    return v0

    .line 19801
    :cond_0
    const/4 v0, 0x0

    goto :goto_0
.end method

.method public final ACW(Z)V
    .locals 3

    .line 19802
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/9Q;->A0B:Lcom/facebook/ads/redexgen/X/SA;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/SA;->A0i()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 19803
    return-void

    .line 19804
    :cond_0
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/9Q;->A0B:Lcom/facebook/ads/redexgen/X/SA;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/SA;->getVideoStartReason()Lcom/facebook/ads/redexgen/X/QM;

    move-result-object v0

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/9Q;->A02:Lcom/facebook/ads/redexgen/X/QM;

    .line 19805
    iput-boolean p1, p0, Lcom/facebook/ads/redexgen/X/9Q;->A04:Z

    .line 19806
    iget-object v2, p0, Lcom/facebook/ads/redexgen/X/9Q;->A0B:Lcom/facebook/ads/redexgen/X/SA;

    const/4 v1, 0x0

    const/16 v0, 0xd

    invoke-virtual {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/SA;->A0e(ZI)V

    .line 19807
    return-void
.end method

.method public final ACu(Z)V
    .locals 6

    .line 19808
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/9Q;->A0B:Lcom/facebook/ads/redexgen/X/SA;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/SA;->A0j()Z

    move-result v0

    if-nez v0, :cond_0

    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/9Q;->A06:Z

    if-eqz v0, :cond_1

    .line 19809
    :cond_0
    return-void

    .line 19810
    :cond_1
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/9Q;->A0B:Lcom/facebook/ads/redexgen/X/SA;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/SA;->getState()Lcom/facebook/ads/redexgen/X/RB;

    move-result-object v1

    sget-object v0, Lcom/facebook/ads/redexgen/X/RB;->A06:Lcom/facebook/ads/redexgen/X/RB;

    if-eq v1, v0, :cond_3

    iget-object v5, p0, Lcom/facebook/ads/redexgen/X/9Q;->A02:Lcom/facebook/ads/redexgen/X/QM;

    if-eqz v5, :cond_3

    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/9Q;->A04:Z

    if-eqz v0, :cond_2

    if-eqz p1, :cond_3

    .line 19811
    :cond_2
    iget-object v4, p0, Lcom/facebook/ads/redexgen/X/9Q;->A0B:Lcom/facebook/ads/redexgen/X/SA;

    const/16 v3, 0x13

    sget-object v2, Lcom/facebook/ads/redexgen/X/9Q;->A0M:[Ljava/lang/String;

    const/4 v0, 0x0

    aget-object v1, v2, v0

    const/4 v0, 0x4

    aget-object v2, v2, v0

    const/16 v0, 0x8

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    invoke-virtual {v2, v0}, Ljava/lang/String;->charAt(I)C

    move-result v0

    if-eq v1, v0, :cond_4

    sget-object v2, Lcom/facebook/ads/redexgen/X/9Q;->A0M:[Ljava/lang/String;

    const-string v1, "5vrQw9TAnlgebYwA7tbvLzoGHglPMjBr"

    const/4 v0, 0x1

    aput-object v1, v2, v0

    invoke-virtual {v4, v5, v3}, Lcom/facebook/ads/redexgen/X/SA;->A0b(Lcom/facebook/ads/redexgen/X/QM;I)V

    .line 19812
    :cond_3
    return-void

    :cond_4
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0
.end method

.method public final onDestroy()V
    .locals 4

    .line 19814
    invoke-super {p0}, Lcom/facebook/ads/redexgen/X/Ss;->onDestroy()V

    .line 19815
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ss;->A03:Lcom/facebook/ads/redexgen/X/Yn;

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/Ih;->A1W(Landroid/content/Context;)Z

    move-result v0

    if-eqz v0, :cond_0

    .line 19816
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ss;->A03:Lcom/facebook/ads/redexgen/X/Yn;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/7f;->A0A()Lcom/facebook/ads/redexgen/X/JE;

    move-result-object v1

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/9Q;->A0B:Lcom/facebook/ads/redexgen/X/SA;

    invoke-interface {v1, v0}, Lcom/facebook/ads/redexgen/X/JE;->AGk(Landroid/view/View;)V

    .line 19817
    :cond_0
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/9Q;->A00:Lcom/facebook/ads/redexgen/X/OL;

    if-eqz v0, :cond_1

    .line 19818
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/OL;->A0v()V

    .line 19819
    :cond_1
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/9Q;->A0B:Lcom/facebook/ads/redexgen/X/SA;

    if-eqz v1, :cond_3

    .line 19820
    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/9Q;->A06:Z

    if-nez v0, :cond_2

    .line 19821
    sget-object v0, Lcom/facebook/ads/redexgen/X/QH;->A05:Lcom/facebook/ads/redexgen/X/QH;

    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/SA;->A0a(Lcom/facebook/ads/redexgen/X/QH;)V

    .line 19822
    :cond_2
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/9Q;->A0B:Lcom/facebook/ads/redexgen/X/SA;

    .line 19823
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/SA;->getEventBus()Lcom/facebook/ads/redexgen/X/8r;

    move-result-object v3

    const/4 v0, 0x6

    new-array v2, v0, [Lcom/facebook/ads/redexgen/X/8s;

    const/4 v1, 0x0

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/9Q;->A0H:Lcom/facebook/ads/redexgen/X/NX;

    aput-object v0, v2, v1

    const/4 v1, 0x1

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/9Q;->A0G:Lcom/facebook/ads/redexgen/X/O7;

    aput-object v0, v2, v1

    const/4 v1, 0x2

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/9Q;->A0F:Lcom/facebook/ads/redexgen/X/On;

    aput-object v0, v2, v1

    const/4 v1, 0x3

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/9Q;->A0I:Lcom/facebook/ads/redexgen/X/NQ;

    aput-object v0, v2, v1

    const/4 v1, 0x4

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/9Q;->A0D:Lcom/facebook/ads/redexgen/X/Pu;

    aput-object v0, v2, v1

    const/4 v1, 0x5

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/9Q;->A0E:Lcom/facebook/ads/redexgen/X/Ps;

    aput-object v0, v2, v1

    .line 19824
    invoke-virtual {v3, v2}, Lcom/facebook/ads/redexgen/X/8r;->A04([Lcom/facebook/ads/redexgen/X/8s;)V

    .line 19825
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/9Q;->A0B:Lcom/facebook/ads/redexgen/X/SA;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/SA;->A0V()V

    .line 19826
    :cond_3
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/9Q;->A0A:Lcom/facebook/ads/redexgen/X/SF;

    if-eqz v0, :cond_4

    .line 19827
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/SF;->A0C()V

    .line 19828
    :cond_4
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/9Q;->A0C:Lcom/facebook/ads/redexgen/X/9I;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/9I;->A0i()V

    .line 19829
    return-void
.end method

.method public setServerSideRewardHandler(Lcom/facebook/ads/redexgen/X/Q7;)V
    .locals 0

    .line 19830
    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/9Q;->A01:Lcom/facebook/ads/redexgen/X/Q7;

    .line 19831
    return-void
.end method
