.class public final Lcom/facebook/ads/redexgen/X/9T;
.super Lcom/facebook/ads/redexgen/X/NQ;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/facebook/ads/redexgen/X/9Q;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field public final synthetic A00:Lcom/facebook/ads/redexgen/X/9Q;


# direct methods
.method public constructor <init>(Lcom/facebook/ads/redexgen/X/9Q;)V
    .locals 0

    .line 19842
    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/9T;->A00:Lcom/facebook/ads/redexgen/X/9Q;

    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/NQ;-><init>()V

    return-void
.end method

.method private final A00(Lcom/facebook/ads/redexgen/X/93;)V
    .locals 1

    .line 19843
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/9T;->A00:Lcom/facebook/ads/redexgen/X/9Q;

    invoke-static {v0, p1}, Lcom/facebook/ads/redexgen/X/9Q;->A0A(Lcom/facebook/ads/redexgen/X/9Q;Lcom/facebook/ads/redexgen/X/93;)V

    .line 19844
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/9T;->A00:Lcom/facebook/ads/redexgen/X/9Q;

    invoke-static {v0, p1}, Lcom/facebook/ads/redexgen/X/9Q;->A0B(Lcom/facebook/ads/redexgen/X/9Q;Lcom/facebook/ads/redexgen/X/93;)V

    .line 19845
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/9T;->A00:Lcom/facebook/ads/redexgen/X/9Q;

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/9Q;->A01(Lcom/facebook/ads/redexgen/X/9Q;)Lcom/facebook/ads/redexgen/X/OL;

    move-result-object v0

    invoke-virtual {v0, p1}, Lcom/facebook/ads/redexgen/X/OL;->A0C(Lcom/facebook/ads/redexgen/X/93;)V

    .line 19846
    return-void
.end method


# virtual methods
.method public final bridge synthetic A03(Lcom/facebook/ads/redexgen/X/8q;)V
    .locals 0

    .line 19847
    check-cast p1, Lcom/facebook/ads/redexgen/X/93;

    invoke-direct {p0, p1}, Lcom/facebook/ads/redexgen/X/9T;->A00(Lcom/facebook/ads/redexgen/X/93;)V

    return-void
.end method
