.class public final Lcom/facebook/ads/redexgen/X/9l;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/facebook/ads/redexgen/X/EI;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "SeekPosition"
.end annotation


# instance fields
.field public final A00:I

.field public final A01:J

.field public final A02:Lcom/facebook/ads/redexgen/X/AH;


# direct methods
.method public constructor <init>(Lcom/facebook/ads/redexgen/X/AH;IJ)V
    .locals 0

    .line 20060
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 20061
    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/9l;->A02:Lcom/facebook/ads/redexgen/X/AH;

    .line 20062
    iput p2, p0, Lcom/facebook/ads/redexgen/X/9l;->A00:I

    .line 20063
    iput-wide p3, p0, Lcom/facebook/ads/redexgen/X/9l;->A01:J

    .line 20064
    return-void
.end method
