.class public final Lcom/facebook/ads/redexgen/X/9q;
.super Ljava/lang/IllegalStateException;
.source ""


# instance fields
.field public final A00:I

.field public final A01:J

.field public final A02:Lcom/facebook/ads/redexgen/X/AH;


# direct methods
.method public constructor <init>(Lcom/facebook/ads/redexgen/X/AH;IJ)V
    .locals 0

    .line 20245
    invoke-direct {p0}, Ljava/lang/IllegalStateException;-><init>()V

    .line 20246
    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/9q;->A02:Lcom/facebook/ads/redexgen/X/AH;

    .line 20247
    iput p2, p0, Lcom/facebook/ads/redexgen/X/9q;->A00:I

    .line 20248
    iput-wide p3, p0, Lcom/facebook/ads/redexgen/X/9q;->A01:J

    .line 20249
    return-void
.end method
