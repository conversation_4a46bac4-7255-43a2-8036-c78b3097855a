.class public interface abstract Lcom/facebook/ads/redexgen/X/A5;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/facebook/ads/internal/exoplayer2/thirdparty/Player$TimelineChangeReason;,
        Lcom/facebook/ads/internal/exoplayer2/thirdparty/Player$DiscontinuityReason;,
        Lcom/facebook/ads/internal/exoplayer2/thirdparty/Player$RepeatMode;,
        Lcom/facebook/ads/internal/exoplayer2/thirdparty/Player$DefaultEventListener;,
        Lcom/facebook/ads/redexgen/X/A0;,
        Lcom/facebook/ads/internal/exoplayer2/thirdparty/Player$TextComponent;,
        Lcom/facebook/ads/internal/exoplayer2/thirdparty/Player$VideoComponent;
    }
.end annotation


# virtual methods
.method public abstract A3Q(Lcom/facebook/ads/redexgen/X/A0;)V
.end method

.method public abstract A6B()I
.end method

.method public abstract A6C()J
.end method

.method public abstract A6Y()J
.end method

.method public abstract A6a()I
.end method

.method public abstract A6b()I
.end method

.method public abstract A6d()J
.end method

.method public abstract A6f()Lcom/facebook/ads/redexgen/X/AH;
.end method

.method public abstract A6g()I
.end method

.method public abstract A6q()J
.end method

.method public abstract A7g()Z
.end method

.method public abstract AEV()V
.end method

.method public abstract AFj(J)V
.end method

.method public abstract AFk()V
.end method

.method public abstract AG9(Z)V
.end method

.method public abstract AGa(Z)V
.end method
