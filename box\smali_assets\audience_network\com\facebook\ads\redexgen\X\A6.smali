.class public interface abstract Lcom/facebook/ads/redexgen/X/A6;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/facebook/ads/redexgen/X/A8;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "Sender"
.end annotation


# virtual methods
.method public abstract AFp(Lcom/facebook/ads/redexgen/X/A8;)V
.end method
