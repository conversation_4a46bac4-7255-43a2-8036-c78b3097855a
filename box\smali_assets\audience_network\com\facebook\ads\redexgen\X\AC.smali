.class public interface abstract Lcom/facebook/ads/redexgen/X/AC;
.super Ljava/lang/Object;
.source ""


# virtual methods
.method public abstract A4l(Landroid/os/Handler;Lcom/facebook/ads/redexgen/X/Id;Lcom/facebook/ads/redexgen/X/Aj;Lcom/facebook/ads/redexgen/X/Fr;Lcom/facebook/ads/redexgen/X/DZ;Lcom/facebook/ads/redexgen/X/Bc;)[Lcom/facebook/ads/redexgen/X/Y5;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/os/Handler;",
            "Lcom/facebook/ads/redexgen/X/Id;",
            "Lcom/facebook/ads/redexgen/X/Aj;",
            "Lcom/facebook/ads/redexgen/X/Fr;",
            "Lcom/facebook/ads/redexgen/X/DZ;",
            "Lcom/facebook/ads/redexgen/X/Bc<",
            "Lcom/facebook/ads/internal/exoplayer2/thirdparty/drm/FrameworkMediaCrypto;",
            ">;)[",
            "Lcom/facebook/ads/redexgen/X/Y5;"
        }
    .end annotation
.end method
