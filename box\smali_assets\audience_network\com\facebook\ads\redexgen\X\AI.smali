.class public final Lcom/facebook/ads/redexgen/X/AI;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/facebook/ads/redexgen/X/Y2;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "Factory"
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 20886
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final A00(Lcom/facebook/ads/redexgen/X/A5;Lcom/facebook/ads/redexgen/X/Hd;)Lcom/facebook/ads/redexgen/X/Y2;
    .locals 1

    .line 20887
    new-instance v0, Lcom/facebook/ads/redexgen/X/Y2;

    invoke-direct {v0, p1, p2}, Lcom/facebook/ads/redexgen/X/Y2;-><init>(Lcom/facebook/ads/redexgen/X/A5;Lcom/facebook/ads/redexgen/X/Hd;)V

    return-object v0
.end method
