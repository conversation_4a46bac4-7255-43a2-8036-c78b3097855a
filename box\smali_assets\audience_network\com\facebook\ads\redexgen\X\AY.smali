.class public final Lcom/facebook/ads/redexgen/X/AY;
.super Lcom/facebook/ads/redexgen/X/NQ;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/facebook/ads/redexgen/X/P3;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field public final synthetic A00:Lcom/facebook/ads/redexgen/X/P3;


# direct methods
.method public constructor <init>(Lcom/facebook/ads/redexgen/X/P3;)V
    .locals 0

    .line 21283
    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/AY;->A00:Lcom/facebook/ads/redexgen/X/P3;

    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/NQ;-><init>()V

    return-void
.end method

.method private final A00(Lcom/facebook/ads/redexgen/X/93;)V
    .locals 1

    .line 21284
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/AY;->A00:Lcom/facebook/ads/redexgen/X/P3;

    invoke-static {v0, p1}, Lcom/facebook/ads/redexgen/X/P3;->A0F(Lcom/facebook/ads/redexgen/X/P3;Lcom/facebook/ads/redexgen/X/93;)V

    .line 21285
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/AY;->A00:Lcom/facebook/ads/redexgen/X/P3;

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/P3;->A0D(Lcom/facebook/ads/redexgen/X/P3;)V

    .line 21286
    return-void
.end method


# virtual methods
.method public final bridge synthetic A03(Lcom/facebook/ads/redexgen/X/8q;)V
    .locals 0

    .line 21287
    check-cast p1, Lcom/facebook/ads/redexgen/X/93;

    invoke-direct {p0, p1}, Lcom/facebook/ads/redexgen/X/AY;->A00(Lcom/facebook/ads/redexgen/X/93;)V

    return-void
.end method
