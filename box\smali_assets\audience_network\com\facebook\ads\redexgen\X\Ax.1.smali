.class public interface abstract Lcom/facebook/ads/redexgen/X/Ax;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/facebook/ads/redexgen/X/Xy;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "AudioProcessorChain"
.end annotation


# virtual methods
.method public abstract A3b(Lcom/facebook/ads/redexgen/X/9x;)Lcom/facebook/ads/redexgen/X/9x;
.end method

.method public abstract A64()[Lcom/facebook/ads/redexgen/X/Ab;
.end method

.method public abstract A7O(J)J
.end method

.method public abstract A80()J
.end method
