.class public final Lcom/facebook/ads/redexgen/X/Ay;
.super Ljava/lang/RuntimeException;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/facebook/ads/redexgen/X/Xy;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "InvalidAudioTrackTimestampException"
.end annotation


# direct methods
.method public constructor <init>(Ljava/lang/String;)V
    .locals 0

    .line 21589
    invoke-direct {p0, p1}, Ljava/lang/RuntimeException;-><init>(Ljava/lang/String;)V

    .line 21590
    return-void
.end method

.method public synthetic constructor <init>(Ljava/lang/String;Lcom/facebook/ads/redexgen/X/Av;)V
    .locals 0

    .line 21591
    invoke-direct {p0, p1}, Lcom/facebook/ads/redexgen/X/Ay;-><init>(Ljava/lang/String;)V

    return-void
.end method
