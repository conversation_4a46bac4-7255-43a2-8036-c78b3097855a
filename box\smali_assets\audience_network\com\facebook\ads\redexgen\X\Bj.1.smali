.class public final Lcom/facebook/ads/redexgen/X/Bj;
.super Ljava/lang/Object;
.source ""

# interfaces
.implements Lcom/facebook/ads/redexgen/X/V1;


# instance fields
.field public A00:Lcom/facebook/ads/NativeAdBase;

.field public A01:Lcom/facebook/ads/NativeAdListener;


# direct methods
.method public constructor <init>(Lcom/facebook/ads/NativeAdListener;Lcom/facebook/ads/NativeAdBase;)V
    .locals 0

    .line 23291
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 23292
    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/Bj;->A01:Lcom/facebook/ads/NativeAdListener;

    .line 23293
    iput-object p2, p0, Lcom/facebook/ads/redexgen/X/Bj;->A00:Lcom/facebook/ads/NativeAdBase;

    .line 23294
    return-void
.end method


# virtual methods
.method public final AAc()V
    .locals 1

    .line 23295
    new-instance v0, Lcom/facebook/ads/redexgen/X/Ux;

    invoke-direct {v0, p0}, Lcom/facebook/ads/redexgen/X/Ux;-><init>(Lcom/facebook/ads/redexgen/X/Bj;)V

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/KK;->A00(Lcom/facebook/ads/redexgen/X/KG;)V

    .line 23296
    return-void
.end method

.method public final AAg()V
    .locals 1

    .line 23297
    new-instance v0, Lcom/facebook/ads/redexgen/X/Uy;

    invoke-direct {v0, p0}, Lcom/facebook/ads/redexgen/X/Uy;-><init>(Lcom/facebook/ads/redexgen/X/Bj;)V

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/KK;->A00(Lcom/facebook/ads/redexgen/X/KG;)V

    .line 23298
    return-void
.end method

.method public final ABR(Lcom/facebook/ads/redexgen/X/Jb;)V
    .locals 1

    .line 23299
    new-instance v0, Lcom/facebook/ads/redexgen/X/V0;

    invoke-direct {v0, p0, p1}, Lcom/facebook/ads/redexgen/X/V0;-><init>(Lcom/facebook/ads/redexgen/X/Bj;Lcom/facebook/ads/redexgen/X/Jb;)V

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/KK;->A00(Lcom/facebook/ads/redexgen/X/KG;)V

    .line 23300
    return-void
.end method

.method public final ACA()V
    .locals 1

    .line 23301
    new-instance v0, Lcom/facebook/ads/redexgen/X/Uw;

    invoke-direct {v0, p0}, Lcom/facebook/ads/redexgen/X/Uw;-><init>(Lcom/facebook/ads/redexgen/X/Bj;)V

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/KK;->A00(Lcom/facebook/ads/redexgen/X/KG;)V

    .line 23302
    return-void
.end method

.method public final ACF()V
    .locals 1

    .line 23303
    new-instance v0, Lcom/facebook/ads/redexgen/X/Uz;

    invoke-direct {v0, p0}, Lcom/facebook/ads/redexgen/X/Uz;-><init>(Lcom/facebook/ads/redexgen/X/Bj;)V

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/KK;->A00(Lcom/facebook/ads/redexgen/X/KG;)V

    .line 23304
    return-void
.end method
