.class public interface abstract Lcom/facebook/ads/redexgen/X/Bs;
.super Ljava/lang/Object;
.source ""


# virtual methods
.method public abstract A8o(Lcom/facebook/ads/redexgen/X/Bu;)V
.end method

.method public abstract AEH(Lcom/facebook/ads/redexgen/X/Bt;Lcom/facebook/ads/redexgen/X/Bz;)I
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;,
            Ljava/lang/InterruptedException;
        }
    .end annotation
.end method

.method public abstract AFh(JJ)V
.end method

.method public abstract AGR(Lcom/facebook/ads/redexgen/X/Bt;)Z
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;,
            Ljava/lang/InterruptedException;
        }
    .end annotation
.end method
