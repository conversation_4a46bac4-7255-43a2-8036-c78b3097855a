.class public final Lcom/facebook/ads/redexgen/X/By;
.super Ljava/lang/Object;
.source ""


# static fields
.field public static A07:[B

.field public static A08:[Ljava/lang/String;

.field public static final A09:[I

.field public static final A0A:[I

.field public static final A0B:[I

.field public static final A0C:[I

.field public static final A0D:[I

.field public static final A0E:[I

.field public static final A0F:[Ljava/lang/String;


# instance fields
.field public A00:I

.field public A01:I

.field public A02:I

.field public A03:I

.field public A04:I

.field public A05:I

.field public A06:Ljava/lang/String;


# direct methods
.method public static constructor <clinit>()V
    .locals 5

    .line 995
    const/16 v0, 0x8

    new-array v2, v0, [Ljava/lang/String;

    const/4 v1, 0x0

    const-string v0, "FJwhSbR0Dqb1wEJXVBdpfUEyE2PwmXT5"

    aput-object v0, v2, v1

    const/4 v1, 0x1

    const-string v0, "QXyUQtNV4ztNvs"

    aput-object v0, v2, v1

    const/4 v1, 0x2

    const-string v0, "u680PItdmSCtrwUArgQMKOt5dGhFKyFy"

    aput-object v0, v2, v1

    const/4 v1, 0x3

    const-string v0, "M6QX6bpZXsa0EpVfLqUIYL0ok3oIyD"

    aput-object v0, v2, v1

    const/4 v1, 0x4

    const-string v0, "4ddrBoC1HSiu89JLzfXMV59B74qIm2xJ"

    aput-object v0, v2, v1

    const/4 v1, 0x5

    const-string v0, "MUUz2dKYtP7sZF6Tuk5kEGRgHEx452fJ"

    aput-object v0, v2, v1

    const/4 v1, 0x6

    const-string v0, "i1sqGOju2LE8Q92QQNgFTaNu0rUo1z4f"

    aput-object v0, v2, v1

    const/4 v1, 0x7

    const-string v0, "5HWyWxmgcaxBLo1LpD3PpaOIjtbRMHJC"

    aput-object v0, v2, v1

    sput-object v2, Lcom/facebook/ads/redexgen/X/By;->A08:[Ljava/lang/String;

    invoke-static {}, Lcom/facebook/ads/redexgen/X/By;->A02()V

    const/4 v0, 0x3

    new-array v4, v0, [Ljava/lang/String;

    const/4 v3, 0x0

    const/16 v2, 0xa

    const/16 v1, 0xd

    const/16 v0, 0x5e

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/By;->A01(III)Ljava/lang/String;

    move-result-object v0

    aput-object v0, v4, v3

    const/4 v3, 0x1

    const/16 v2, 0x17

    const/16 v1, 0xd

    const/16 v0, 0x73

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/By;->A01(III)Ljava/lang/String;

    move-result-object v0

    aput-object v0, v4, v3

    const/4 v3, 0x2

    const/4 v2, 0x0

    const/16 v1, 0xa

    const/4 v0, 0x1

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/By;->A01(III)Ljava/lang/String;

    move-result-object v0

    aput-object v0, v4, v3

    sput-object v4, Lcom/facebook/ads/redexgen/X/By;->A0F:[Ljava/lang/String;

    .line 996
    const v2, 0xbb80

    const/16 v1, 0x7d00

    const v0, 0xac44

    filled-new-array {v0, v2, v1}, [I

    move-result-object v0

    sput-object v0, Lcom/facebook/ads/redexgen/X/By;->A0E:[I

    .line 997
    const/16 v1, 0xe

    new-array v0, v1, [I

    fill-array-data v0, :array_0

    sput-object v0, Lcom/facebook/ads/redexgen/X/By;->A09:[I

    .line 998
    new-array v0, v1, [I

    fill-array-data v0, :array_1

    sput-object v0, Lcom/facebook/ads/redexgen/X/By;->A0D:[I

    .line 999
    new-array v0, v1, [I

    fill-array-data v0, :array_2

    sput-object v0, Lcom/facebook/ads/redexgen/X/By;->A0A:[I

    .line 1000
    new-array v0, v1, [I

    fill-array-data v0, :array_3

    sput-object v0, Lcom/facebook/ads/redexgen/X/By;->A0B:[I

    .line 1001
    new-array v0, v1, [I

    fill-array-data v0, :array_4

    sput-object v0, Lcom/facebook/ads/redexgen/X/By;->A0C:[I

    return-void

    nop

    :array_0
    .array-data 4
        0x20
        0x40
        0x60
        0x80
        0xa0
        0xc0
        0xe0
        0x100
        0x120
        0x140
        0x160
        0x180
        0x1a0
        0x1c0
    .end array-data

    :array_1
    .array-data 4
        0x20
        0x30
        0x38
        0x40
        0x50
        0x60
        0x70
        0x80
        0x90
        0xa0
        0xb0
        0xc0
        0xe0
        0x100
    .end array-data

    :array_2
    .array-data 4
        0x20
        0x30
        0x38
        0x40
        0x50
        0x60
        0x70
        0x80
        0xa0
        0xc0
        0xe0
        0x100
        0x140
        0x180
    .end array-data

    :array_3
    .array-data 4
        0x20
        0x28
        0x30
        0x38
        0x40
        0x50
        0x60
        0x70
        0x80
        0xa0
        0xc0
        0xe0
        0x100
        0x140
    .end array-data

    :array_4
    .array-data 4
        0x8
        0x10
        0x18
        0x20
        0x28
        0x30
        0x38
        0x40
        0x50
        0x60
        0x70
        0x80
        0x90
        0xa0
    .end array-data
.end method

.method public constructor <init>()V
    .locals 0

    .line 24094
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static A00(I)I
    .locals 10

    .line 24095
    const/high16 v1, -0x200000

    and-int v0, p0, v1

    const/4 v2, -0x1

    if-eq v0, v1, :cond_0

    .line 24096
    return v2

    .line 24097
    :cond_0
    ushr-int/lit8 v7, p0, 0x13

    const/4 v6, 0x3

    and-int/2addr v7, v6

    .line 24098
    .local v0, "version":I
    const/4 v5, 0x1

    if-ne v7, v5, :cond_1

    .line 24099
    return v2

    .line 24100
    :cond_1
    ushr-int/lit8 v4, p0, 0x11

    and-int/2addr v4, v6

    .line 24101
    .local v4, "layer":I
    if-nez v4, :cond_2

    .line 24102
    return v2

    .line 24103
    :cond_2
    ushr-int/lit8 v8, p0, 0xc

    const/16 v0, 0xf

    and-int/2addr v8, v0

    .line 24104
    .local v5, "bitrateIndex":I
    if-eqz v8, :cond_3

    if-ne v8, v0, :cond_4

    .line 24105
    .end local v2
    .end local v6
    .end local v7
    .end local v8
    :cond_3
    return v2

    .line 24106
    :cond_4
    ushr-int/lit8 v1, p0, 0xa

    and-int/2addr v1, v6

    .line 24107
    .local v6, "samplingRateIndex":I
    if-ne v1, v6, :cond_5

    .line 24108
    return v2

    .line 24109
    :cond_5
    sget-object v0, Lcom/facebook/ads/redexgen/X/By;->A0E:[I

    aget v3, v0, v1

    .line 24110
    .local v2, "samplingRate":I
    const/4 v9, 0x2

    if-ne v7, v9, :cond_8

    .line 24111
    div-int/lit8 v3, v3, 0x2

    .line 24112
    :cond_6
    :goto_0
    ushr-int/lit8 p0, p0, 0x9

    and-int/2addr p0, v5

    sget-object v2, Lcom/facebook/ads/redexgen/X/By;->A08:[Ljava/lang/String;

    const/4 v0, 0x5

    aget-object v1, v2, v0

    const/4 v0, 0x4

    aget-object v2, v2, v0

    const/16 v0, 0x14

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    invoke-virtual {v2, v0}, Ljava/lang/String;->charAt(I)C

    move-result v0

    if-eq v1, v0, :cond_e

    .line 24113
    .local v8, "padding":I
    sget-object v2, Lcom/facebook/ads/redexgen/X/By;->A08:[Ljava/lang/String;

    const-string v1, "wCPzQVRdLc9fuoZbbzdyTQMs65DUcW8k"

    const/4 v0, 0x0

    aput-object v1, v2, v0

    if-ne v4, v6, :cond_9

    .line 24114
    if-ne v7, v6, :cond_7

    sget-object v1, Lcom/facebook/ads/redexgen/X/By;->A09:[I

    add-int/lit8 v0, v8, -0x1

    aget v0, v1, v0

    .line 24115
    .local v1, "bitrate":I
    :goto_1
    mul-int/lit16 v0, v0, 0x2ee0

    div-int/2addr v0, v3

    add-int/2addr v0, p0

    mul-int/lit8 v0, v0, 0x4

    return v0

    .line 24116
    :cond_7
    sget-object v1, Lcom/facebook/ads/redexgen/X/By;->A0D:[I

    add-int/lit8 v0, v8, -0x1

    aget v0, v1, v0

    goto :goto_1

    .line 24117
    :cond_8
    if-nez v7, :cond_6

    .line 24118
    div-int/lit8 v3, v3, 0x4

    goto :goto_0

    .line 24119
    .end local v1    # "bitrate":I
    :cond_9
    if-ne v7, v6, :cond_b

    .line 24120
    if-ne v4, v9, :cond_a

    sget-object v1, Lcom/facebook/ads/redexgen/X/By;->A0A:[I

    add-int/lit8 v0, v8, -0x1

    aget v1, v1, v0

    .line 24121
    .restart local v7
    :goto_2
    const v0, 0x23280

    if-ne v7, v6, :cond_c

    .line 24122
    mul-int/2addr v0, v1

    div-int/2addr v0, v3

    add-int/2addr v0, p0

    return v0

    .line 24123
    :cond_a
    sget-object v1, Lcom/facebook/ads/redexgen/X/By;->A0B:[I

    add-int/lit8 v0, v8, -0x1

    aget v1, v1, v0

    goto :goto_2

    .line 24124
    .end local v7
    :cond_b
    sget-object v1, Lcom/facebook/ads/redexgen/X/By;->A0C:[I

    add-int/lit8 v0, v8, -0x1

    aget v1, v1, v0

    goto :goto_2

    .line 24125
    :cond_c
    if-ne v4, v5, :cond_d

    const v0, 0x11940

    :cond_d
    mul-int/2addr v0, v1

    div-int/2addr v0, v3

    add-int/2addr v0, p0

    return v0

    :cond_e
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0
.end method

.method public static A01(III)Ljava/lang/String;
    .locals 4

    sget-object v1, Lcom/facebook/ads/redexgen/X/By;->A07:[B

    add-int v0, p0, p1

    invoke-static {v1, p0, v0}, Ljava/util/Arrays;->copyOfRange([BII)[B

    move-result-object v3

    const/4 p0, 0x0

    :goto_0
    array-length v0, v3

    if-ge p0, v0, :cond_1

    aget-byte p1, v3, p0

    sub-int/2addr p1, p2

    sget-object v2, Lcom/facebook/ads/redexgen/X/By;->A08:[Ljava/lang/String;

    const/4 v0, 0x6

    aget-object v1, v2, v0

    const/4 v0, 0x2

    aget-object v2, v2, v0

    const/16 v0, 0x1a

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    invoke-virtual {v2, v0}, Ljava/lang/String;->charAt(I)C

    move-result v0

    if-eq v1, v0, :cond_0

    sget-object v2, Lcom/facebook/ads/redexgen/X/By;->A08:[Ljava/lang/String;

    const-string v1, "mZfo4fIlXHQscYR0BkWPKUfaL8c0bByk"

    const/4 v0, 0x6

    aput-object v1, v2, v0

    const-string v1, "AkiufmiMzZJvhyS77AiglOXb5e3YRP2V"

    const/4 v0, 0x2

    aput-object v1, v2, v0

    add-int/lit8 v0, p1, -0x5e

    int-to-byte v0, v0

    aput-byte v0, v3, p0

    add-int/lit8 p0, p0, 0x1

    goto :goto_0

    :cond_0
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    :cond_1
    new-instance v0, Ljava/lang/String;

    invoke-direct {v0, v3}, Ljava/lang/String;-><init>([B)V

    return-object v0
.end method

.method public static A02()V
    .locals 1

    const/16 v0, 0x24

    new-array v0, v0, [B

    fill-array-data v0, :array_0

    sput-object v0, Lcom/facebook/ads/redexgen/X/By;->A07:[B

    return-void

    :array_0
    .array-data 1
        -0x40t
        -0x2ct
        -0x3dt
        -0x38t
        -0x32t
        -0x72t
        -0x34t
        -0x31t
        -0x3ct
        -0x3at
        0x1dt
        0x31t
        0x20t
        0x25t
        0x2bt
        -0x15t
        0x29t
        0x2ct
        0x21t
        0x23t
        -0x17t
        0x8t
        -0x13t
        0x32t
        0x46t
        0x35t
        0x3at
        0x40t
        0x0t
        0x3et
        0x41t
        0x36t
        0x38t
        -0x2t
        0x1dt
        0x3t
    .end array-data
.end method

.method private A03(ILjava/lang/String;IIIII)V
    .locals 0

    .line 24126
    iput p1, p0, Lcom/facebook/ads/redexgen/X/By;->A05:I

    .line 24127
    iput-object p2, p0, Lcom/facebook/ads/redexgen/X/By;->A06:Ljava/lang/String;

    .line 24128
    iput p3, p0, Lcom/facebook/ads/redexgen/X/By;->A02:I

    .line 24129
    iput p4, p0, Lcom/facebook/ads/redexgen/X/By;->A03:I

    .line 24130
    iput p5, p0, Lcom/facebook/ads/redexgen/X/By;->A01:I

    .line 24131
    iput p6, p0, Lcom/facebook/ads/redexgen/X/By;->A00:I

    .line 24132
    iput p7, p0, Lcom/facebook/ads/redexgen/X/By;->A04:I

    .line 24133
    return-void
.end method

.method public static A04(ILcom/facebook/ads/redexgen/X/By;)Z
    .locals 14

    .line 24134
    const/high16 v1, -0x200000

    and-int v0, p0, v1

    const/4 v6, 0x0

    if-eq v0, v1, :cond_0

    .line 24135
    return v6

    .line 24136
    :cond_0
    ushr-int/lit8 v7, p0, 0x13

    const/4 v3, 0x3

    and-int/2addr v7, v3

    .line 24137
    .local v0, "version":I
    const/4 v2, 0x1

    if-ne v7, v2, :cond_1

    .line 24138
    return v6

    .line 24139
    :cond_1
    ushr-int/lit8 v0, p0, 0x11

    and-int/lit8 v4, v0, 0x3

    .line 24140
    .local v12, "layer":I
    if-nez v4, :cond_2

    .line 24141
    return v6

    .line 24142
    :cond_2
    ushr-int/lit8 v1, p0, 0xc

    const/16 v0, 0xf

    and-int/lit8 v5, v1, 0xf

    .line 24143
    .local v13, "bitrateIndex":I
    if-eqz v5, :cond_3

    if-ne v5, v0, :cond_4

    .line 24144
    .end local v1
    .end local v2
    .end local v8
    .end local p0    # null:I
    .end local p1    # null:Lcom/facebook/ads/redexgen/X/By;
    .end local p2
    .end local p3
    .end local p4
    :cond_3
    return v6

    .line 24145
    :cond_4
    ushr-int/lit8 v0, p0, 0xa

    and-int/lit8 v1, v0, 0x3

    .line 24146
    .local p0, "samplingRateIndex":I
    if-ne v1, v3, :cond_5

    .line 24147
    return v6

    .line 24148
    :cond_5
    sget-object v0, Lcom/facebook/ads/redexgen/X/By;->A0E:[I

    aget v10, v0, v1

    .line 24149
    .local v2, "sampleRate":I
    const/4 v1, 0x2

    if-ne v7, v1, :cond_e

    .line 24150
    div-int/lit8 v10, v10, 0x2

    .line 24151
    :cond_6
    :goto_0
    ushr-int/lit8 v0, p0, 0x9

    and-int/lit8 v6, v0, 0x1

    .line 24152
    .local p1, "padding":I
    if-ne v4, v3, :cond_9

    .line 24153
    if-ne v7, v3, :cond_8

    sget-object v1, Lcom/facebook/ads/redexgen/X/By;->A09:[I

    add-int/lit8 v0, v5, -0x1

    aget v5, v1, v0

    .line 24154
    .local v4, "bitrate":I
    :goto_1
    mul-int/lit16 v0, v5, 0x2ee0

    div-int/2addr v0, v10

    add-int/2addr v0, v6

    mul-int/lit8 v9, v0, 0x4

    .line 24155
    .local v5, "frameSize":I
    const/16 v13, 0x180

    .line 24156
    .local v6, "samplesPerFrame":I
    .end local v5    # "frameSize":I
    .end local v6    # "samplesPerFrame":I
    .local v10, "bitrate":I
    .local p2, "frameSize":I
    .local p3, "samplesPerFrame":I
    :goto_2
    sget-object v1, Lcom/facebook/ads/redexgen/X/By;->A0F:[Ljava/lang/String;

    rsub-int/lit8 v0, v4, 0x3

    aget-object v8, v1, v0

    .line 24157
    .local p4, "mimeType":Ljava/lang/String;
    shr-int/lit8 v0, p0, 0x6

    and-int/2addr v0, v3

    if-ne v0, v3, :cond_7

    const/4 v11, 0x1

    .line 24158
    .local v8, "channels":I
    :goto_3
    mul-int/lit16 v12, v5, 0x3e8

    .end local v10    # "bitrate":I
    .local v1, "bitrate":I
    move-object v6, p1

    invoke-direct/range {v6 .. v13}, Lcom/facebook/ads/redexgen/X/By;->A03(ILjava/lang/String;IIIII)V

    .line 24159
    return v2

    .line 24160
    :cond_7
    const/4 v11, 0x2

    goto :goto_3

    .line 24161
    :cond_8
    sget-object v1, Lcom/facebook/ads/redexgen/X/By;->A0D:[I

    add-int/lit8 v0, v5, -0x1

    aget v5, v1, v0

    goto :goto_1

    .line 24162
    .end local v4    # "bitrate":I
    .end local v5
    .end local v6
    :cond_9
    const v9, 0x23280

    if-ne v7, v3, :cond_b

    .line 24163
    if-ne v4, v1, :cond_a

    sget-object v1, Lcom/facebook/ads/redexgen/X/By;->A0A:[I

    add-int/lit8 v0, v5, -0x1

    aget v5, v1, v0

    .line 24164
    .local v5, "bitrate":I
    :goto_4
    const/16 v13, 0x480

    .line 24165
    .restart local v6    # "samplesPerFrame":I
    mul-int/2addr v9, v5

    div-int/2addr v9, v10

    add-int/2addr v9, v6

    .local v4, "frameSize":I
    goto :goto_2

    .line 24166
    :cond_a
    sget-object v1, Lcom/facebook/ads/redexgen/X/By;->A0B:[I

    add-int/lit8 v0, v5, -0x1

    aget v5, v1, v0

    goto :goto_4

    .line 24167
    .end local v4    # "frameSize":I
    .end local v5    # "bitrate":I
    .end local v6    # "samplesPerFrame":I
    :cond_b
    sget-object v1, Lcom/facebook/ads/redexgen/X/By;->A0C:[I

    add-int/lit8 v0, v5, -0x1

    aget v5, v1, v0

    .line 24168
    .restart local v5    # "bitrate":I
    if-ne v4, v2, :cond_d

    const/16 v13, 0x240

    .line 24169
    .restart local v6    # "samplesPerFrame":I
    :goto_5
    if-ne v4, v2, :cond_c

    const v9, 0x11940

    :cond_c
    mul-int/2addr v9, v5

    div-int/2addr v9, v10

    add-int/2addr v9, v6

    goto :goto_2

    .line 24170
    :cond_d
    const/16 v13, 0x480

    goto :goto_5

    .line 24171
    :cond_e
    if-nez v7, :cond_6

    .line 24172
    div-int/lit8 v10, v10, 0x4

    goto :goto_0
.end method
