.class public interface abstract Lcom/facebook/ads/redexgen/X/C4;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/facebook/ads/redexgen/X/C3;
    }
.end annotation


# virtual methods
.method public abstract A5n(Lcom/facebook/ads/internal/exoplayer2/thirdparty/Format;)V
.end method

.method public abstract AFQ(Lcom/facebook/ads/redexgen/X/Bt;IZ)I
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;,
            Ljava/lang/InterruptedException;
        }
    .end annotation
.end method

.method public abstract AFR(Lcom/facebook/ads/redexgen/X/Hz;I)V
.end method

.method public abstract AFS(JIIILcom/facebook/ads/redexgen/X/C3;)V
.end method
