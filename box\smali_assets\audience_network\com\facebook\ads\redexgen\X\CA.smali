.class public interface abstract Lcom/facebook/ads/redexgen/X/CA;
.super Ljava/lang/Object;
.source ""


# virtual methods
.method public abstract A8p(Lcom/facebook/ads/redexgen/X/CC;)V
.end method

.method public abstract AEJ(Lcom/facebook/ads/redexgen/X/Bt;)Z
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;,
            Ljava/lang/InterruptedException;
        }
    .end annotation
.end method

.method public abstract reset()V
.end method
