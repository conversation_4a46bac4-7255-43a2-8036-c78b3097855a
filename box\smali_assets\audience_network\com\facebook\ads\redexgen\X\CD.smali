.class public final Lcom/facebook/ads/redexgen/X/CD;
.super Ljava/lang/Object;
.source ""

# interfaces
.implements Lcom/facebook/ads/redexgen/X/WP;
.implements Lcom/facebook/ads/redexgen/X/Bu;
.implements Lcom/facebook/ads/redexgen/X/H6;
.implements Lcom/facebook/ads/redexgen/X/H9;
.implements Lcom/facebook/ads/redexgen/X/FA;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/facebook/ads/redexgen/X/Ek;,
        Lcom/facebook/ads/redexgen/X/WT;,
        Lcom/facebook/ads/redexgen/X/WS;,
        Lcom/facebook/ads/redexgen/X/El;
    }
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Lcom/facebook/ads/redexgen/X/WP;",
        "Lcom/facebook/ads/redexgen/X/Bu;",
        "Lcom/facebook/ads/redexgen/X/H6<",
        "Lcom/facebook/ads/redexgen/X/WT;",
        ">;",
        "Lcom/facebook/ads/redexgen/X/H9;",
        "Lcom/facebook/ads/redexgen/X/FA;"
    }
.end annotation


# static fields
.field public static A0c:[B

.field public static A0d:[Ljava/lang/String;


# instance fields
.field public A00:I

.field public A01:I

.field public A02:I

.field public A03:J

.field public A04:J

.field public A05:J

.field public A06:J

.field public A07:Lcom/facebook/ads/redexgen/X/C1;

.field public A08:Lcom/facebook/ads/redexgen/X/WQ;

.field public A09:Lcom/facebook/ads/internal/exoplayer2/thirdparty/source/TrackGroupArray;

.field public A0A:Z

.field public A0B:Z

.field public A0C:Z

.field public A0D:Z

.field public A0E:Z

.field public A0F:Z

.field public A0G:Z

.field public A0H:Z

.field public A0I:Z

.field public A0J:[I

.field public A0K:[Lcom/facebook/ads/redexgen/X/WO;

.field public A0L:[Z

.field public A0M:[Z

.field public A0N:[Z

.field public final A0O:I

.field public final A0P:J

.field public final A0Q:Landroid/net/Uri;

.field public final A0R:Landroid/os/Handler;

.field public final A0S:Lcom/facebook/ads/redexgen/X/Ek;

.field public final A0T:Lcom/facebook/ads/redexgen/X/El;

.field public final A0U:Lcom/facebook/ads/redexgen/X/F1;

.field public final A0V:Lcom/facebook/ads/redexgen/X/Gm;

.field public final A0W:Lcom/facebook/ads/redexgen/X/Gu;

.field public final A0X:Lcom/facebook/ads/redexgen/X/Vq;

.field public final A0Y:Lcom/facebook/ads/redexgen/X/Hg;

.field public final A0Z:Ljava/lang/Runnable;

.field public final A0a:Ljava/lang/Runnable;

.field public final A0b:Ljava/lang/String;


# direct methods
.method public static constructor <clinit>()V
    .locals 3

    .line 1005
    const/16 v0, 0x8

    new-array v2, v0, [Ljava/lang/String;

    const/4 v1, 0x0

    const-string v0, "uM3"

    aput-object v0, v2, v1

    const/4 v1, 0x1

    const-string v0, "HhtEoeszujhq2hqW803X6wAUFNr2yIvk"

    aput-object v0, v2, v1

    const/4 v1, 0x2

    const-string v0, "llgRlYE2HHrDKVr9Tlv1gg4sawaKL"

    aput-object v0, v2, v1

    const/4 v1, 0x3

    const-string v0, "gd26Zevc0BeMQVnsPvgEWj4kMHitbDqE"

    aput-object v0, v2, v1

    const/4 v1, 0x4

    const-string v0, "B56DgxYfwXnRdFUWoxgY8ZwWxUmrPQlR"

    aput-object v0, v2, v1

    const/4 v1, 0x5

    const-string v0, "o6X9hbG4Io42IRbcZylLcsRNIhbJFPRA"

    aput-object v0, v2, v1

    const/4 v1, 0x6

    const-string v0, "SBfu1SOz9uKNnyoy8"

    aput-object v0, v2, v1

    const/4 v1, 0x7

    const-string v0, "5PtJb97AhuhjIJJCziWFyl83Esr9CMYA"

    aput-object v0, v2, v1

    sput-object v2, Lcom/facebook/ads/redexgen/X/CD;->A0d:[Ljava/lang/String;

    invoke-static {}, Lcom/facebook/ads/redexgen/X/CD;->A0B()V

    return-void
.end method

.method public constructor <init>(Landroid/net/Uri;Lcom/facebook/ads/redexgen/X/Gu;[Lcom/facebook/ads/redexgen/X/Bs;ILcom/facebook/ads/redexgen/X/F1;Lcom/facebook/ads/redexgen/X/El;Lcom/facebook/ads/redexgen/X/Gm;Ljava/lang/String;I)V
    .locals 4

    .line 24291
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 24292
    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/CD;->A0Q:Landroid/net/Uri;

    .line 24293
    iput-object p2, p0, Lcom/facebook/ads/redexgen/X/CD;->A0W:Lcom/facebook/ads/redexgen/X/Gu;

    .line 24294
    iput p4, p0, Lcom/facebook/ads/redexgen/X/CD;->A0O:I

    .line 24295
    iput-object p5, p0, Lcom/facebook/ads/redexgen/X/CD;->A0U:Lcom/facebook/ads/redexgen/X/F1;

    .line 24296
    iput-object p6, p0, Lcom/facebook/ads/redexgen/X/CD;->A0T:Lcom/facebook/ads/redexgen/X/El;

    .line 24297
    iput-object p7, p0, Lcom/facebook/ads/redexgen/X/CD;->A0V:Lcom/facebook/ads/redexgen/X/Gm;

    .line 24298
    iput-object p8, p0, Lcom/facebook/ads/redexgen/X/CD;->A0b:Ljava/lang/String;

    .line 24299
    int-to-long v0, p9

    iput-wide v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A0P:J

    .line 24300
    const/4 v2, 0x0

    const/16 v1, 0x1b

    const/16 v0, 0x1b

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/CD;->A07(III)Ljava/lang/String;

    move-result-object v1

    new-instance v0, Lcom/facebook/ads/redexgen/X/Vq;

    invoke-direct {v0, v1}, Lcom/facebook/ads/redexgen/X/Vq;-><init>(Ljava/lang/String;)V

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A0X:Lcom/facebook/ads/redexgen/X/Vq;

    .line 24301
    new-instance v0, Lcom/facebook/ads/redexgen/X/Ek;

    invoke-direct {v0, p3, p0}, Lcom/facebook/ads/redexgen/X/Ek;-><init>([Lcom/facebook/ads/redexgen/X/Bs;Lcom/facebook/ads/redexgen/X/Bu;)V

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A0S:Lcom/facebook/ads/redexgen/X/Ek;

    .line 24302
    new-instance v0, Lcom/facebook/ads/redexgen/X/Hg;

    invoke-direct {v0}, Lcom/facebook/ads/redexgen/X/Hg;-><init>()V

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A0Y:Lcom/facebook/ads/redexgen/X/Hg;

    .line 24303
    new-instance v0, Lcom/facebook/ads/redexgen/X/Ei;

    invoke-direct {v0, p0}, Lcom/facebook/ads/redexgen/X/Ei;-><init>(Lcom/facebook/ads/redexgen/X/CD;)V

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A0Z:Ljava/lang/Runnable;

    .line 24304
    new-instance v0, Lcom/facebook/ads/redexgen/X/Ej;

    invoke-direct {v0, p0}, Lcom/facebook/ads/redexgen/X/Ej;-><init>(Lcom/facebook/ads/redexgen/X/CD;)V

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A0a:Ljava/lang/Runnable;

    .line 24305
    new-instance v0, Landroid/os/Handler;

    invoke-direct {v0}, Landroid/os/Handler;-><init>()V

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A0R:Landroid/os/Handler;

    .line 24306
    const/4 v1, 0x0

    new-array v0, v1, [I

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A0J:[I

    .line 24307
    new-array v0, v1, [Lcom/facebook/ads/redexgen/X/WO;

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A0K:[Lcom/facebook/ads/redexgen/X/WO;

    .line 24308
    const-wide v2, -0x7fffffffffffffffL    # -4.9E-324

    iput-wide v2, p0, Lcom/facebook/ads/redexgen/X/CD;->A06:J

    .line 24309
    const-wide/16 v0, -0x1

    iput-wide v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A05:J

    .line 24310
    iput-wide v2, p0, Lcom/facebook/ads/redexgen/X/CD;->A03:J

    .line 24311
    const/4 v0, -0x1

    if-ne p4, v0, :cond_0

    .line 24312
    const/4 p4, 0x3

    .line 24313
    :cond_0
    iput p4, p0, Lcom/facebook/ads/redexgen/X/CD;->A00:I

    .line 24314
    invoke-virtual {p5}, Lcom/facebook/ads/redexgen/X/F1;->A03()V

    .line 24315
    return-void
.end method

.method private A00()I
    .locals 5

    .line 24316
    const/4 v4, 0x0

    .line 24317
    .local v0, "extractedSamplesCount":I
    iget-object v3, p0, Lcom/facebook/ads/redexgen/X/CD;->A0K:[Lcom/facebook/ads/redexgen/X/WO;

    array-length v2, v3

    const/4 v1, 0x0

    :goto_0
    if-ge v1, v2, :cond_0

    aget-object v0, v3, v1

    .line 24318
    .local v4, "sampleQueue":Lcom/facebook/ads/redexgen/X/WO;
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/WO;->A0C()I

    move-result v0

    add-int/2addr v4, v0

    .line 24319
    .end local v4    # "sampleQueue":Lcom/facebook/ads/redexgen/X/WO;
    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    .line 24320
    :cond_0
    return v4
.end method

.method private final A01(Lcom/facebook/ads/redexgen/X/WT;JJLjava/io/IOException;)I
    .locals 21

    move-object/from16 v0, p0

    move-object v0, v0

    .line 24321
    move-object/from16 v19, p6

    invoke-static/range {v19 .. v19}, Lcom/facebook/ads/redexgen/X/CD;->A0N(Ljava/io/IOException;)Z

    move-result v20

    .line 24322
    .local v20, "isErrorFatal":Z
    iget-object v2, v0, Lcom/facebook/ads/redexgen/X/CD;->A0U:Lcom/facebook/ads/redexgen/X/F1;

    .line 24323
    move-object/from16 v1, p1

    invoke-static {v1}, Lcom/facebook/ads/redexgen/X/WT;->A03(Lcom/facebook/ads/redexgen/X/WT;)Lcom/facebook/ads/redexgen/X/Gy;

    move-result-object v3

    const/4 v4, 0x1

    const/4 v5, -0x1

    const/4 v6, 0x0

    const/4 v7, 0x0

    const/4 v8, 0x0

    .line 24324
    invoke-static {v1}, Lcom/facebook/ads/redexgen/X/WT;->A00(Lcom/facebook/ads/redexgen/X/WT;)J

    move-result-wide v9

    iget-wide v11, v0, Lcom/facebook/ads/redexgen/X/CD;->A03:J

    .line 24325
    invoke-static {v1}, Lcom/facebook/ads/redexgen/X/WT;->A01(Lcom/facebook/ads/redexgen/X/WT;)J

    move-result-wide v17

    .line 24326
    move-wide/from16 v13, p2

    move-wide/from16 v15, p4

    invoke-virtual/range {v2 .. v20}, Lcom/facebook/ads/redexgen/X/F1;->A0H(Lcom/facebook/ads/redexgen/X/Gy;IILcom/facebook/ads/internal/exoplayer2/thirdparty/Format;ILjava/lang/Object;JJJJJLjava/io/IOException;Z)V

    .line 24327
    invoke-direct {v0, v1}, Lcom/facebook/ads/redexgen/X/CD;->A0E(Lcom/facebook/ads/redexgen/X/WT;)V

    .line 24328
    if-eqz v20, :cond_0

    .line 24329
    const/4 v0, 0x3

    return v0

    .line 24330
    :cond_0
    invoke-direct {v0}, Lcom/facebook/ads/redexgen/X/CD;->A00()I

    move-result v4

    .line 24331
    .local v1, "extractedSamplesCount":I
    iget v2, v0, Lcom/facebook/ads/redexgen/X/CD;->A02:I

    const/4 v3, 0x1

    if-le v4, v2, :cond_3

    const/4 v2, 0x1

    .line 24332
    .local v2, "madeProgress":Z
    :goto_0
    invoke-direct {v0, v1, v4}, Lcom/facebook/ads/redexgen/X/CD;->A0L(Lcom/facebook/ads/redexgen/X/WT;I)Z

    move-result v0

    if-eqz v0, :cond_2

    .line 24333
    if-eqz v2, :cond_1

    .line 24334
    :goto_1
    return v3

    .line 24335
    :cond_1
    const/4 v3, 0x0

    goto :goto_1

    .line 24336
    :cond_2
    const/4 v3, 0x2

    goto :goto_1

    .line 24337
    :cond_3
    const/4 v2, 0x0

    goto :goto_0
.end method

.method private A02()J
    .locals 7

    .line 24338
    const-wide/high16 v3, -0x8000000000000000L

    .line 24339
    .local v0, "largestQueuedTimestampUs":J
    iget-object v6, p0, Lcom/facebook/ads/redexgen/X/CD;->A0K:[Lcom/facebook/ads/redexgen/X/WO;

    array-length v5, v6

    const/4 v2, 0x0

    :goto_0
    if-ge v2, v5, :cond_0

    aget-object v0, v6, v2

    .line 24340
    .local v5, "sampleQueue":Lcom/facebook/ads/redexgen/X/WO;
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/WO;->A0F()J

    move-result-wide v0

    invoke-static {v3, v4, v0, v1}, Ljava/lang/Math;->max(JJ)J

    move-result-wide v3

    .line 24341
    .end local v5    # "sampleQueue":Lcom/facebook/ads/redexgen/X/WO;
    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    .line 24342
    :cond_0
    return-wide v3
.end method

.method public static synthetic A03(Lcom/facebook/ads/redexgen/X/CD;)J
    .locals 1

    .line 24343
    iget-wide v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A0P:J

    return-wide v0
.end method

.method public static synthetic A04(Lcom/facebook/ads/redexgen/X/CD;)Landroid/os/Handler;
    .locals 0

    .line 24344
    iget-object p0, p0, Lcom/facebook/ads/redexgen/X/CD;->A0R:Landroid/os/Handler;

    return-object p0
.end method

.method public static synthetic A05(Lcom/facebook/ads/redexgen/X/CD;)Lcom/facebook/ads/redexgen/X/WQ;
    .locals 0

    .line 24345
    iget-object p0, p0, Lcom/facebook/ads/redexgen/X/CD;->A08:Lcom/facebook/ads/redexgen/X/WQ;

    return-object p0
.end method

.method public static synthetic A06(Lcom/facebook/ads/redexgen/X/CD;)Ljava/lang/Runnable;
    .locals 0

    .line 24346
    iget-object p0, p0, Lcom/facebook/ads/redexgen/X/CD;->A0a:Ljava/lang/Runnable;

    return-object p0
.end method

.method public static A07(III)Ljava/lang/String;
    .locals 2

    sget-object v1, Lcom/facebook/ads/redexgen/X/CD;->A0c:[B

    add-int v0, p0, p1

    invoke-static {v1, p0, v0}, Ljava/util/Arrays;->copyOfRange([BII)[B

    move-result-object p0

    const/4 v1, 0x0

    :goto_0
    array-length v0, p0

    if-ge v1, v0, :cond_0

    aget-byte v0, p0, v1

    xor-int/2addr v0, p2

    xor-int/lit8 v0, v0, 0x19

    int-to-byte v0, v0

    aput-byte v0, p0, v1

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_0
    new-instance v0, Ljava/lang/String;

    invoke-direct {v0, p0}, Ljava/lang/String;-><init>([B)V

    return-object v0
.end method

.method public static synthetic A08(Lcom/facebook/ads/redexgen/X/CD;)Ljava/lang/String;
    .locals 0

    .line 24347
    iget-object p0, p0, Lcom/facebook/ads/redexgen/X/CD;->A0b:Ljava/lang/String;

    return-object p0
.end method

.method private A09()V
    .locals 8

    .line 24348
    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A0G:Z

    if-nez v0, :cond_0

    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A0F:Z

    if-nez v0, :cond_0

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A07:Lcom/facebook/ads/redexgen/X/C1;

    if-eqz v0, :cond_0

    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A0H:Z

    if-nez v0, :cond_1

    .line 24349
    .end local v0
    .end local v1
    :cond_0
    return-void

    .line 24350
    :cond_1
    iget-object v3, p0, Lcom/facebook/ads/redexgen/X/CD;->A0K:[Lcom/facebook/ads/redexgen/X/WO;

    array-length v2, v3

    const/4 v7, 0x0

    const/4 v1, 0x0

    :goto_0
    if-ge v1, v2, :cond_3

    aget-object v0, v3, v1

    .line 24351
    .local v4, "sampleQueue":Lcom/facebook/ads/redexgen/X/WO;
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/WO;->A0G()Lcom/facebook/ads/internal/exoplayer2/thirdparty/Format;

    move-result-object v0

    if-nez v0, :cond_2

    .line 24352
    return-void

    .line 24353
    .end local v4    # "sampleQueue":Lcom/facebook/ads/redexgen/X/WO;
    :cond_2
    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    .line 24354
    :cond_3
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A0Y:Lcom/facebook/ads/redexgen/X/Hg;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/Hg;->A01()Z

    .line 24355
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A0K:[Lcom/facebook/ads/redexgen/X/WO;

    array-length v6, v0

    .line 24356
    .local v0, "trackCount":I
    new-array v5, v6, [Lcom/facebook/ads/internal/exoplayer2/thirdparty/source/TrackGroup;

    .line 24357
    .local v1, "trackArray":[Lcom/facebook/ads/internal/exoplayer2/thirdparty/source/TrackGroup;
    new-array v0, v6, [Z

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A0N:[Z

    .line 24358
    new-array v0, v6, [Z

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A0L:[Z

    .line 24359
    new-array v0, v6, [Z

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A0M:[Z

    .line 24360
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A07:Lcom/facebook/ads/redexgen/X/C1;

    invoke-interface {v0}, Lcom/facebook/ads/redexgen/X/C1;->A6r()J

    move-result-wide v0

    iput-wide v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A03:J

    .line 24361
    const/4 v3, 0x0

    .local v3, "i":I
    :goto_1
    const/4 v4, 0x1

    if-ge v3, v6, :cond_6

    .line 24362
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A0K:[Lcom/facebook/ads/redexgen/X/WO;

    aget-object v0, v0, v3

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/WO;->A0G()Lcom/facebook/ads/internal/exoplayer2/thirdparty/Format;

    move-result-object v2

    .line 24363
    .local v5, "trackFormat":Lcom/facebook/ads/internal/exoplayer2/thirdparty/Format;
    new-array v1, v4, [Lcom/facebook/ads/internal/exoplayer2/thirdparty/Format;

    aput-object v2, v1, v7

    new-instance v0, Lcom/facebook/ads/internal/exoplayer2/thirdparty/source/TrackGroup;

    invoke-direct {v0, v1}, Lcom/facebook/ads/internal/exoplayer2/thirdparty/source/TrackGroup;-><init>([Lcom/facebook/ads/internal/exoplayer2/thirdparty/Format;)V

    aput-object v0, v5, v3

    .line 24364
    iget-object v1, v2, Lcom/facebook/ads/internal/exoplayer2/thirdparty/Format;->A0O:Ljava/lang/String;

    .line 24365
    .local v6, "mimeType":Ljava/lang/String;
    invoke-static {v1}, Lcom/facebook/ads/redexgen/X/Hs;->A0B(Ljava/lang/String;)Z

    move-result v0

    if-nez v0, :cond_4

    invoke-static {v1}, Lcom/facebook/ads/redexgen/X/Hs;->A09(Ljava/lang/String;)Z

    move-result v0

    if-eqz v0, :cond_5

    .line 24366
    .local v4, "isAudioVideo":Z
    :cond_4
    :goto_2
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A0N:[Z

    aput-boolean v4, v0, v3

    .line 24367
    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A0A:Z

    or-int/2addr v0, v4

    iput-boolean v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A0A:Z

    .line 24368
    .end local v4    # "isAudioVideo":Z
    .end local v5    # "trackFormat":Lcom/facebook/ads/internal/exoplayer2/thirdparty/Format;
    .end local v6    # "mimeType":Ljava/lang/String;
    add-int/lit8 v3, v3, 0x1

    goto :goto_1

    .line 24369
    :cond_5
    const/4 v4, 0x0

    goto :goto_2

    .line 24370
    .end local v3    # "i":I
    :cond_6
    new-instance v0, Lcom/facebook/ads/internal/exoplayer2/thirdparty/source/TrackGroupArray;

    invoke-direct {v0, v5}, Lcom/facebook/ads/internal/exoplayer2/thirdparty/source/TrackGroupArray;-><init>([Lcom/facebook/ads/internal/exoplayer2/thirdparty/source/TrackGroup;)V

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A09:Lcom/facebook/ads/internal/exoplayer2/thirdparty/source/TrackGroupArray;

    .line 24371
    iget v1, p0, Lcom/facebook/ads/redexgen/X/CD;->A0O:I

    const/4 v0, -0x1

    if-ne v1, v0, :cond_7

    iget-wide v5, p0, Lcom/facebook/ads/redexgen/X/CD;->A05:J

    const-wide/16 v1, -0x1

    cmp-long v0, v5, v1

    if-nez v0, :cond_7

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A07:Lcom/facebook/ads/redexgen/X/C1;

    .line 24372
    invoke-interface {v0}, Lcom/facebook/ads/redexgen/X/C1;->A6r()J

    move-result-wide v5

    const-wide v1, -0x7fffffffffffffffL    # -4.9E-324

    cmp-long v0, v5, v1

    if-nez v0, :cond_7

    .line 24373
    const/4 v3, 0x6

    sget-object v2, Lcom/facebook/ads/redexgen/X/CD;->A0d:[Ljava/lang/String;

    const/4 v0, 0x4

    aget-object v1, v2, v0

    const/4 v0, 0x5

    aget-object v2, v2, v0

    const/16 v0, 0x13

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    invoke-virtual {v2, v0}, Ljava/lang/String;->charAt(I)C

    move-result v0

    if-eq v1, v0, :cond_8

    sget-object v2, Lcom/facebook/ads/redexgen/X/CD;->A0d:[Ljava/lang/String;

    const-string v1, "7a3gC0RfnKvRSexgB6r2d4KLSEZ"

    const/4 v0, 0x2

    aput-object v1, v2, v0

    iput v3, p0, Lcom/facebook/ads/redexgen/X/CD;->A00:I

    .line 24374
    :cond_7
    iput-boolean v4, p0, Lcom/facebook/ads/redexgen/X/CD;->A0F:Z

    .line 24375
    iget-object v3, p0, Lcom/facebook/ads/redexgen/X/CD;->A0T:Lcom/facebook/ads/redexgen/X/El;

    iget-wide v1, p0, Lcom/facebook/ads/redexgen/X/CD;->A03:J

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A07:Lcom/facebook/ads/redexgen/X/C1;

    invoke-interface {v0}, Lcom/facebook/ads/redexgen/X/C1;->A9I()Z

    move-result v0

    invoke-interface {v3, v1, v2, v0}, Lcom/facebook/ads/redexgen/X/El;->AD9(JZ)V

    .line 24376
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A08:Lcom/facebook/ads/redexgen/X/WQ;

    invoke-interface {v0, p0}, Lcom/facebook/ads/redexgen/X/WQ;->ACj(Lcom/facebook/ads/redexgen/X/WP;)V

    .line 24377
    return-void

    :cond_8
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0
.end method

.method private A0A()V
    .locals 15

    .line 24378
    move-object v0, p0

    new-instance v9, Lcom/facebook/ads/redexgen/X/WT;

    iget-object v11, v0, Lcom/facebook/ads/redexgen/X/CD;->A0Q:Landroid/net/Uri;

    iget-object v12, v0, Lcom/facebook/ads/redexgen/X/CD;->A0W:Lcom/facebook/ads/redexgen/X/Gu;

    iget-object v13, v0, Lcom/facebook/ads/redexgen/X/CD;->A0S:Lcom/facebook/ads/redexgen/X/Ek;

    iget-object v14, v0, Lcom/facebook/ads/redexgen/X/CD;->A0Y:Lcom/facebook/ads/redexgen/X/Hg;

    move-object v10, p0

    invoke-direct/range {v9 .. v14}, Lcom/facebook/ads/redexgen/X/WT;-><init>(Lcom/facebook/ads/redexgen/X/CD;Landroid/net/Uri;Lcom/facebook/ads/redexgen/X/Gu;Lcom/facebook/ads/redexgen/X/Ek;Lcom/facebook/ads/redexgen/X/Hg;)V

    .line 24379
    .local v0, "loadable":Lcom/facebook/ads/redexgen/X/WT;
    iget-boolean v1, v0, Lcom/facebook/ads/redexgen/X/CD;->A0F:Z

    if-eqz v1, :cond_1

    .line 24380
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/CD;->A0I()Z

    move-result v1

    invoke-static {v1}, Lcom/facebook/ads/redexgen/X/Ha;->A04(Z)V

    .line 24381
    iget-wide v7, v0, Lcom/facebook/ads/redexgen/X/CD;->A03:J

    const-wide v5, -0x7fffffffffffffffL    # -4.9E-324

    cmp-long v1, v7, v5

    if-eqz v1, :cond_0

    iget-wide v2, v0, Lcom/facebook/ads/redexgen/X/CD;->A06:J

    cmp-long v1, v2, v7

    if-ltz v1, :cond_0

    .line 24382
    const/4 v1, 0x1

    iput-boolean v1, v0, Lcom/facebook/ads/redexgen/X/CD;->A0B:Z

    .line 24383
    iput-wide v5, v0, Lcom/facebook/ads/redexgen/X/CD;->A06:J

    .line 24384
    return-void

    .line 24385
    :cond_0
    iget-object v3, v0, Lcom/facebook/ads/redexgen/X/CD;->A07:Lcom/facebook/ads/redexgen/X/C1;

    iget-wide v1, v0, Lcom/facebook/ads/redexgen/X/CD;->A06:J

    .line 24386
    invoke-interface {v3, v1, v2}, Lcom/facebook/ads/redexgen/X/C1;->A7t(J)Lcom/facebook/ads/redexgen/X/C0;

    move-result-object v1

    iget-object v1, v1, Lcom/facebook/ads/redexgen/X/C0;->A00:Lcom/facebook/ads/redexgen/X/C2;

    iget-wide v3, v1, Lcom/facebook/ads/redexgen/X/C2;->A00:J

    iget-wide v1, v0, Lcom/facebook/ads/redexgen/X/CD;->A06:J

    .line 24387
    invoke-virtual {v9, v3, v4, v1, v2}, Lcom/facebook/ads/redexgen/X/WT;->A04(JJ)V

    .line 24388
    iput-wide v5, v0, Lcom/facebook/ads/redexgen/X/CD;->A06:J

    .line 24389
    :cond_1
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/CD;->A00()I

    move-result v1

    iput v1, v0, Lcom/facebook/ads/redexgen/X/CD;->A02:I

    .line 24390
    iget-object v2, v0, Lcom/facebook/ads/redexgen/X/CD;->A0X:Lcom/facebook/ads/redexgen/X/Vq;

    iget v1, v0, Lcom/facebook/ads/redexgen/X/CD;->A00:I

    invoke-virtual {v2, v9, v0, v1}, Lcom/facebook/ads/redexgen/X/Vq;->A04(Lcom/facebook/ads/redexgen/X/H8;Lcom/facebook/ads/redexgen/X/H6;I)J

    move-result-wide v12

    .line 24391
    .local v1, "elapsedRealtimeMs":J
    iget-object v1, v0, Lcom/facebook/ads/redexgen/X/CD;->A0U:Lcom/facebook/ads/redexgen/X/F1;

    .line 24392
    invoke-static {v9}, Lcom/facebook/ads/redexgen/X/WT;->A03(Lcom/facebook/ads/redexgen/X/WT;)Lcom/facebook/ads/redexgen/X/Gy;

    move-result-object v2

    const/4 v3, 0x1

    const/4 v4, -0x1

    const/4 v5, 0x0

    const/4 v6, 0x0

    const/4 v7, 0x0

    .line 24393
    invoke-static {v9}, Lcom/facebook/ads/redexgen/X/WT;->A00(Lcom/facebook/ads/redexgen/X/WT;)J

    move-result-wide v8

    iget-wide v10, v0, Lcom/facebook/ads/redexgen/X/CD;->A03:J

    .line 24394
    invoke-virtual/range {v1 .. v13}, Lcom/facebook/ads/redexgen/X/F1;->A0E(Lcom/facebook/ads/redexgen/X/Gy;IILcom/facebook/ads/internal/exoplayer2/thirdparty/Format;ILjava/lang/Object;JJJ)V

    .line 24395
    return-void
.end method

.method public static A0B()V
    .locals 1

    const/16 v0, 0x1b

    new-array v0, v0, [B

    fill-array-data v0, :array_0

    sput-object v0, Lcom/facebook/ads/redexgen/X/CD;->A0c:[B

    return-void

    :array_0
    .array-data 1
        0x4et
        0x6dt
        0x63t
        0x66t
        0x67t
        0x70t
        0x38t
        0x47t
        0x7at
        0x76t
        0x70t
        0x63t
        0x61t
        0x76t
        0x6dt
        0x70t
        0x4ft
        0x67t
        0x66t
        0x6bt
        0x63t
        0x52t
        0x67t
        0x70t
        0x6bt
        0x6dt
        0x66t
    .end array-data
.end method

.method private A0C(I)V
    .locals 8

    .line 24396
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A0M:[Z

    aget-boolean v0, v0, p1

    if-nez v0, :cond_0

    .line 24397
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A09:Lcom/facebook/ads/internal/exoplayer2/thirdparty/source/TrackGroupArray;

    invoke-virtual {v0, p1}, Lcom/facebook/ads/internal/exoplayer2/thirdparty/source/TrackGroupArray;->A01(I)Lcom/facebook/ads/internal/exoplayer2/thirdparty/source/TrackGroup;

    move-result-object v1

    const/4 v0, 0x0

    invoke-virtual {v1, v0}, Lcom/facebook/ads/internal/exoplayer2/thirdparty/source/TrackGroup;->A01(I)Lcom/facebook/ads/internal/exoplayer2/thirdparty/Format;

    move-result-object v3

    .line 24398
    .local v0, "trackFormat":Lcom/facebook/ads/internal/exoplayer2/thirdparty/Format;
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/CD;->A0U:Lcom/facebook/ads/redexgen/X/F1;

    iget-object v0, v3, Lcom/facebook/ads/internal/exoplayer2/thirdparty/Format;->A0O:Ljava/lang/String;

    .line 24399
    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/Hs;->A01(Ljava/lang/String;)I

    move-result v2

    const/4 v4, 0x0

    const/4 v5, 0x0

    iget-wide v6, p0, Lcom/facebook/ads/redexgen/X/CD;->A04:J

    .line 24400
    invoke-virtual/range {v1 .. v7}, Lcom/facebook/ads/redexgen/X/F1;->A06(ILcom/facebook/ads/internal/exoplayer2/thirdparty/Format;ILjava/lang/Object;J)V

    .line 24401
    iget-object v3, p0, Lcom/facebook/ads/redexgen/X/CD;->A0M:[Z

    sget-object v1, Lcom/facebook/ads/redexgen/X/CD;->A0d:[Ljava/lang/String;

    const/4 v0, 0x2

    aget-object v0, v1, v0

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v1

    const/16 v0, 0x1f

    if-eq v1, v0, :cond_1

    sget-object v2, Lcom/facebook/ads/redexgen/X/CD;->A0d:[Ljava/lang/String;

    const-string v1, "eh27oOHcwVOmHzYfi8kDTS"

    const/4 v0, 0x2

    aput-object v1, v2, v0

    const/4 v0, 0x1

    aput-boolean v0, v3, p1

    .line 24402
    .end local v0    # "trackFormat":Lcom/facebook/ads/internal/exoplayer2/thirdparty/Format;
    :cond_0
    return-void

    :cond_1
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0
.end method

.method private A0D(I)V
    .locals 5

    .line 24403
    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A0E:Z

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A0N:[Z

    aget-boolean v0, v0, p1

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A0K:[Lcom/facebook/ads/redexgen/X/WO;

    aget-object v0, v0, p1

    .line 24404
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/WO;->A0M()Z

    move-result v0

    if-eqz v0, :cond_1

    .line 24405
    :cond_0
    return-void

    .line 24406
    :cond_1
    const-wide/16 v2, 0x0

    sget-object v4, Lcom/facebook/ads/redexgen/X/CD;->A0d:[Ljava/lang/String;

    const/4 v0, 0x7

    aget-object v1, v4, v0

    const/4 v0, 0x1

    aget-object v4, v4, v0

    const/16 v0, 0x1a

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    invoke-virtual {v4, v0}, Ljava/lang/String;->charAt(I)C

    move-result v0

    if-eq v1, v0, :cond_2

    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    :cond_2
    sget-object v4, Lcom/facebook/ads/redexgen/X/CD;->A0d:[Ljava/lang/String;

    const-string v1, "sgNFiq5UDquEcD4Be"

    const/4 v0, 0x6

    aput-object v1, v4, v0

    const-string v1, "0a9"

    const/4 v0, 0x0

    aput-object v1, v4, v0

    iput-wide v2, p0, Lcom/facebook/ads/redexgen/X/CD;->A06:J

    .line 24407
    const/4 v4, 0x0

    iput-boolean v4, p0, Lcom/facebook/ads/redexgen/X/CD;->A0E:Z

    .line 24408
    const/4 v0, 0x1

    iput-boolean v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A0D:Z

    .line 24409
    iput-wide v2, p0, Lcom/facebook/ads/redexgen/X/CD;->A04:J

    .line 24410
    iput v4, p0, Lcom/facebook/ads/redexgen/X/CD;->A02:I

    .line 24411
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/CD;->A0K:[Lcom/facebook/ads/redexgen/X/WO;

    array-length v0, v1

    :goto_0
    if-ge v4, v0, :cond_3

    aget-object v2, v1, v4

    .line 24412
    .local v3, "sampleQueue":Lcom/facebook/ads/redexgen/X/WO;
    invoke-virtual {v2}, Lcom/facebook/ads/redexgen/X/WO;->A0I()V

    .line 24413
    .end local v3    # "sampleQueue":Lcom/facebook/ads/redexgen/X/WO;
    add-int/lit8 v4, v4, 0x1

    goto :goto_0

    .line 24414
    :cond_3
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A08:Lcom/facebook/ads/redexgen/X/WQ;

    invoke-interface {v0, p0}, Lcom/facebook/ads/redexgen/X/FC;->ABC(Lcom/facebook/ads/redexgen/X/FD;)V

    .line 24415
    return-void
.end method

.method private A0E(Lcom/facebook/ads/redexgen/X/WT;)V
    .locals 5

    .line 24416
    iget-wide v3, p0, Lcom/facebook/ads/redexgen/X/CD;->A05:J

    const-wide/16 v1, -0x1

    cmp-long v0, v3, v1

    if-nez v0, :cond_0

    .line 24417
    invoke-static {p1}, Lcom/facebook/ads/redexgen/X/WT;->A02(Lcom/facebook/ads/redexgen/X/WT;)J

    move-result-wide v0

    iput-wide v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A05:J

    .line 24418
    :cond_0
    return-void
.end method

.method private final A0F(Lcom/facebook/ads/redexgen/X/WT;JJ)V
    .locals 20

    .line 24419
    move-object/from16 v2, p0

    move-object v2, v2

    iget-wide v5, v2, Lcom/facebook/ads/redexgen/X/CD;->A03:J

    const-wide v3, -0x7fffffffffffffffL    # -4.9E-324

    cmp-long v0, v5, v3

    if-nez v0, :cond_0

    .line 24420
    invoke-direct {v2}, Lcom/facebook/ads/redexgen/X/CD;->A02()J

    move-result-wide v5

    .line 24421
    .local v1, "largestQueuedTimestampUs":J
    const-wide/high16 v3, -0x8000000000000000L

    cmp-long v0, v5, v3

    if-nez v0, :cond_1

    .line 24422
    const-wide/16 v0, 0x0

    .line 24423
    :goto_0
    iput-wide v0, v2, Lcom/facebook/ads/redexgen/X/CD;->A03:J

    .line 24424
    iget-object v4, v2, Lcom/facebook/ads/redexgen/X/CD;->A0T:Lcom/facebook/ads/redexgen/X/El;

    iget-object v3, v2, Lcom/facebook/ads/redexgen/X/CD;->A07:Lcom/facebook/ads/redexgen/X/C1;

    invoke-interface {v3}, Lcom/facebook/ads/redexgen/X/C1;->A9I()Z

    move-result v3

    invoke-interface {v4, v0, v1, v3}, Lcom/facebook/ads/redexgen/X/El;->AD9(JZ)V

    .line 24425
    .end local v1    # "largestQueuedTimestampUs":J
    :cond_0
    iget-object v3, v2, Lcom/facebook/ads/redexgen/X/CD;->A0U:Lcom/facebook/ads/redexgen/X/F1;

    .line 24426
    move-object/from16 v0, p1

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/WT;->A03(Lcom/facebook/ads/redexgen/X/WT;)Lcom/facebook/ads/redexgen/X/Gy;

    move-result-object v4

    const/4 v5, 0x1

    const/4 v6, -0x1

    const/4 v7, 0x0

    const/4 v8, 0x0

    const/4 v9, 0x0

    .line 24427
    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/WT;->A00(Lcom/facebook/ads/redexgen/X/WT;)J

    move-result-wide v10

    iget-wide v12, v2, Lcom/facebook/ads/redexgen/X/CD;->A03:J

    .line 24428
    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/WT;->A01(Lcom/facebook/ads/redexgen/X/WT;)J

    move-result-wide v18

    .line 24429
    move-wide/from16 v14, p2

    move-wide/from16 v16, p4

    invoke-virtual/range {v3 .. v19}, Lcom/facebook/ads/redexgen/X/F1;->A0G(Lcom/facebook/ads/redexgen/X/Gy;IILcom/facebook/ads/internal/exoplayer2/thirdparty/Format;ILjava/lang/Object;JJJJJ)V

    .line 24430
    invoke-direct {v2, v0}, Lcom/facebook/ads/redexgen/X/CD;->A0E(Lcom/facebook/ads/redexgen/X/WT;)V

    .line 24431
    const/4 v0, 0x1

    iput-boolean v0, v2, Lcom/facebook/ads/redexgen/X/CD;->A0B:Z

    .line 24432
    iget-object v0, v2, Lcom/facebook/ads/redexgen/X/CD;->A08:Lcom/facebook/ads/redexgen/X/WQ;

    invoke-interface {v0, v2}, Lcom/facebook/ads/redexgen/X/FC;->ABC(Lcom/facebook/ads/redexgen/X/FD;)V

    .line 24433
    return-void

    .line 24434
    :cond_1
    const-wide/16 v0, 0x2710

    add-long/2addr v0, v5

    goto :goto_0
.end method

.method private final A0G(Lcom/facebook/ads/redexgen/X/WT;JJZ)V
    .locals 19

    move-object/from16 v1, p0

    move-object v1, v1

    .line 24435
    iget-object v2, v1, Lcom/facebook/ads/redexgen/X/CD;->A0U:Lcom/facebook/ads/redexgen/X/F1;

    .line 24436
    move-object/from16 v0, p1

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/WT;->A03(Lcom/facebook/ads/redexgen/X/WT;)Lcom/facebook/ads/redexgen/X/Gy;

    move-result-object v3

    const/4 v4, 0x1

    const/4 v5, -0x1

    const/4 v6, 0x0

    const/4 v7, 0x0

    const/4 v8, 0x0

    .line 24437
    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/WT;->A00(Lcom/facebook/ads/redexgen/X/WT;)J

    move-result-wide v9

    iget-wide v11, v1, Lcom/facebook/ads/redexgen/X/CD;->A03:J

    .line 24438
    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/WT;->A01(Lcom/facebook/ads/redexgen/X/WT;)J

    move-result-wide v17

    .line 24439
    move-wide/from16 v13, p2

    move-wide/from16 v15, p4

    invoke-virtual/range {v2 .. v18}, Lcom/facebook/ads/redexgen/X/F1;->A0F(Lcom/facebook/ads/redexgen/X/Gy;IILcom/facebook/ads/internal/exoplayer2/thirdparty/Format;ILjava/lang/Object;JJJJJ)V

    .line 24440
    if-nez p6, :cond_1

    .line 24441
    invoke-direct {v1, v0}, Lcom/facebook/ads/redexgen/X/CD;->A0E(Lcom/facebook/ads/redexgen/X/WT;)V

    .line 24442
    iget-object v4, v1, Lcom/facebook/ads/redexgen/X/CD;->A0K:[Lcom/facebook/ads/redexgen/X/WO;

    array-length v3, v4

    const/4 v2, 0x0

    :goto_0
    if-ge v2, v3, :cond_0

    aget-object v0, v4, v2

    .line 24443
    .local v4, "sampleQueue":Lcom/facebook/ads/redexgen/X/WO;
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/WO;->A0I()V

    .line 24444
    .end local v4    # "sampleQueue":Lcom/facebook/ads/redexgen/X/WO;
    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    .line 24445
    :cond_0
    iget v0, v1, Lcom/facebook/ads/redexgen/X/CD;->A01:I

    if-lez v0, :cond_1

    .line 24446
    iget-object v0, v1, Lcom/facebook/ads/redexgen/X/CD;->A08:Lcom/facebook/ads/redexgen/X/WQ;

    invoke-interface {v0, v1}, Lcom/facebook/ads/redexgen/X/FC;->ABC(Lcom/facebook/ads/redexgen/X/FD;)V

    .line 24447
    :cond_1
    return-void
.end method

.method public static synthetic A0H(Lcom/facebook/ads/redexgen/X/CD;)V
    .locals 0

    .line 24448
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/CD;->A09()V

    return-void
.end method

.method private A0I()Z
    .locals 5

    .line 24449
    iget-wide v3, p0, Lcom/facebook/ads/redexgen/X/CD;->A06:J

    const-wide v1, -0x7fffffffffffffffL    # -4.9E-324

    cmp-long v0, v3, v1

    if-eqz v0, :cond_0

    const/4 v0, 0x1

    :goto_0
    return v0

    :cond_0
    const/4 v0, 0x0

    goto :goto_0
.end method

.method private A0J()Z
    .locals 1

    .line 24450
    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A0D:Z

    if-nez v0, :cond_0

    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/CD;->A0I()Z

    move-result v0

    if-eqz v0, :cond_1

    :cond_0
    const/4 v0, 0x1

    :goto_0
    return v0

    :cond_1
    const/4 v0, 0x0

    goto :goto_0
.end method

.method private A0K(J)Z
    .locals 7

    .line 24451
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A0K:[Lcom/facebook/ads/redexgen/X/WO;

    array-length v6, v0

    .line 24452
    .local v0, "trackCount":I
    const/4 v5, 0x0

    .local v1, "i":I
    :goto_0
    const/4 v2, 0x1

    if-ge v5, v6, :cond_4

    .line 24453
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A0K:[Lcom/facebook/ads/redexgen/X/WO;

    aget-object v0, v0, v5

    .line 24454
    .local v3, "sampleQueue":Lcom/facebook/ads/redexgen/X/WO;
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/WO;->A0J()V

    .line 24455
    const/4 v4, 0x0

    invoke-virtual {v0, p1, p2, v2, v4}, Lcom/facebook/ads/redexgen/X/WO;->A0D(JZZ)I

    move-result v1

    const/4 v0, -0x1

    if-eq v1, v0, :cond_2

    .line 24456
    .local v2, "seekInsideQueue":Z
    :goto_1
    if-nez v2, :cond_1

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A0N:[Z

    aget-boolean v0, v0, v5

    if-nez v0, :cond_0

    iget-boolean v3, p0, Lcom/facebook/ads/redexgen/X/CD;->A0A:Z

    sget-object v2, Lcom/facebook/ads/redexgen/X/CD;->A0d:[Ljava/lang/String;

    const/4 v0, 0x4

    aget-object v1, v2, v0

    const/4 v0, 0x5

    aget-object v2, v2, v0

    const/16 v0, 0x13

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    invoke-virtual {v2, v0}, Ljava/lang/String;->charAt(I)C

    move-result v0

    if-eq v1, v0, :cond_3

    sget-object v2, Lcom/facebook/ads/redexgen/X/CD;->A0d:[Ljava/lang/String;

    const-string v1, "iCn5X7g6PoYVWCe4GwpQYWYt9drfTGBT"

    const/4 v0, 0x7

    aput-object v1, v2, v0

    const-string v1, "IDd3eJ2NBG0wgz78gdspZyWtrcrofueQ"

    const/4 v0, 0x1

    aput-object v1, v2, v0

    if-nez v3, :cond_1

    .line 24457
    :cond_0
    return v4

    .line 24458
    .end local v2    # "seekInsideQueue":Z
    .end local v3    # "sampleQueue":Lcom/facebook/ads/redexgen/X/WO;
    :cond_1
    add-int/lit8 v5, v5, 0x1

    goto :goto_0

    .line 24459
    :cond_2
    const/4 v2, 0x0

    goto :goto_1

    :cond_3
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    .line 24460
    .end local v1    # "i":I
    :cond_4
    return v2
.end method

.method private A0L(Lcom/facebook/ads/redexgen/X/WT;I)Z
    .locals 7

    .line 24461
    iget-wide v1, p0, Lcom/facebook/ads/redexgen/X/CD;->A05:J

    const-wide/16 v3, -0x1

    const/4 v5, 0x1

    cmp-long v0, v1, v3

    if-nez v0, :cond_0

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A07:Lcom/facebook/ads/redexgen/X/C1;

    if-eqz v0, :cond_2

    invoke-interface {v0}, Lcom/facebook/ads/redexgen/X/C1;->A6r()J

    move-result-wide v3

    const-wide v1, -0x7fffffffffffffffL    # -4.9E-324

    cmp-long v0, v3, v1

    if-eqz v0, :cond_2

    .line 24462
    :cond_0
    iput p2, p0, Lcom/facebook/ads/redexgen/X/CD;->A02:I

    sget-object v1, Lcom/facebook/ads/redexgen/X/CD;->A0d:[Ljava/lang/String;

    const/4 v0, 0x2

    aget-object v0, v1, v0

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v1

    const/16 v0, 0x1f

    if-eq v1, v0, :cond_1

    .line 24463
    sget-object v2, Lcom/facebook/ads/redexgen/X/CD;->A0d:[Ljava/lang/String;

    const-string v1, "F1SGTXFlfQdsRtCv2"

    const/4 v0, 0x6

    aput-object v1, v2, v0

    const-string v1, "c29"

    const/4 v0, 0x0

    aput-object v1, v2, v0

    return v5

    :cond_1
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    .line 24464
    :cond_2
    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A0F:Z

    const/4 v6, 0x0

    if-eqz v0, :cond_3

    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/CD;->A0J()Z

    move-result v0

    if-nez v0, :cond_3

    .line 24465
    iput-boolean v5, p0, Lcom/facebook/ads/redexgen/X/CD;->A0E:Z

    .line 24466
    return v6

    .line 24467
    :cond_3
    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A0F:Z

    iput-boolean v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A0D:Z

    .line 24468
    const-wide/16 v2, 0x0

    iput-wide v2, p0, Lcom/facebook/ads/redexgen/X/CD;->A04:J

    .line 24469
    iput v6, p0, Lcom/facebook/ads/redexgen/X/CD;->A02:I

    .line 24470
    iget-object v4, p0, Lcom/facebook/ads/redexgen/X/CD;->A0K:[Lcom/facebook/ads/redexgen/X/WO;

    array-length v1, v4

    :goto_0
    if-ge v6, v1, :cond_4

    aget-object v0, v4, v6

    .line 24471
    .local v6, "sampleQueue":Lcom/facebook/ads/redexgen/X/WO;
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/WO;->A0I()V

    .line 24472
    .end local v6    # "sampleQueue":Lcom/facebook/ads/redexgen/X/WO;
    add-int/lit8 v6, v6, 0x1

    goto :goto_0

    .line 24473
    :cond_4
    invoke-virtual {p1, v2, v3, v2, v3}, Lcom/facebook/ads/redexgen/X/WT;->A04(JJ)V

    .line 24474
    return v5
.end method

.method public static synthetic A0M(Lcom/facebook/ads/redexgen/X/CD;)Z
    .locals 0

    .line 24475
    iget-boolean p0, p0, Lcom/facebook/ads/redexgen/X/CD;->A0G:Z

    return p0
.end method

.method public static A0N(Ljava/io/IOException;)Z
    .locals 0

    .line 24476
    instance-of p0, p0, Lcom/facebook/ads/redexgen/X/WM;

    return p0
.end method


# virtual methods
.method public final A0O(IJ)I
    .locals 5

    .line 24477
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/CD;->A0J()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 24478
    const/4 v0, 0x0

    return v0

    .line 24479
    :cond_0
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A0K:[Lcom/facebook/ads/redexgen/X/WO;

    aget-object v3, v0, p1

    .line 24480
    .local v0, "sampleQueue":Lcom/facebook/ads/redexgen/X/WO;
    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A0B:Z

    if-eqz v0, :cond_3

    invoke-virtual {v3}, Lcom/facebook/ads/redexgen/X/WO;->A0F()J

    move-result-wide v1

    cmp-long v0, p2, v1

    if-lez v0, :cond_3

    .line 24481
    invoke-virtual {v3}, Lcom/facebook/ads/redexgen/X/WO;->A0A()I

    move-result v4

    .line 24482
    .local v1, "skipCount":I
    :cond_1
    :goto_0
    if-lez v4, :cond_2

    .line 24483
    invoke-direct {p0, p1}, Lcom/facebook/ads/redexgen/X/CD;->A0C(I)V

    .line 24484
    :goto_1
    return v4

    .line 24485
    :cond_2
    invoke-direct {p0, p1}, Lcom/facebook/ads/redexgen/X/CD;->A0D(I)V

    goto :goto_1

    .line 24486
    .end local v1    # "skipCount":I
    :cond_3
    const/4 v0, 0x1

    invoke-virtual {v3, p2, p3, v0, v0}, Lcom/facebook/ads/redexgen/X/WO;->A0D(JZZ)I

    move-result v4

    .line 24487
    .restart local v1    # "skipCount":I
    const/4 v3, -0x1

    sget-object v2, Lcom/facebook/ads/redexgen/X/CD;->A0d:[Ljava/lang/String;

    const/4 v0, 0x4

    aget-object v1, v2, v0

    const/4 v0, 0x5

    aget-object v2, v2, v0

    const/16 v0, 0x13

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    invoke-virtual {v2, v0}, Ljava/lang/String;->charAt(I)C

    move-result v0

    if-eq v1, v0, :cond_4

    sget-object v2, Lcom/facebook/ads/redexgen/X/CD;->A0d:[Ljava/lang/String;

    const-string v1, "NIYG9FQCzFZSe10IE"

    const/4 v0, 0x6

    aput-object v1, v2, v0

    const-string v1, "6cN"

    const/4 v0, 0x0

    aput-object v1, v2, v0

    if-ne v4, v3, :cond_1

    .line 24488
    const/4 v4, 0x0

    goto :goto_0

    :cond_4
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0
.end method

.method public final A0P(ILcom/facebook/ads/redexgen/X/9p;Lcom/facebook/ads/redexgen/X/Xr;Z)I
    .locals 10

    .line 24489
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/CD;->A0J()Z

    move-result v0

    const/4 v2, -0x3

    if-eqz v0, :cond_0

    .line 24490
    return v2

    .line 24491
    :cond_0
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A0K:[Lcom/facebook/ads/redexgen/X/WO;

    aget-object v3, v0, p1

    iget-boolean v7, p0, Lcom/facebook/ads/redexgen/X/CD;->A0B:Z

    iget-wide v8, p0, Lcom/facebook/ads/redexgen/X/CD;->A04:J

    .line 24492
    move-object v4, p2

    move-object v5, p3

    move v6, p4

    invoke-virtual/range {v3 .. v9}, Lcom/facebook/ads/redexgen/X/WO;->A0E(Lcom/facebook/ads/redexgen/X/9p;Lcom/facebook/ads/redexgen/X/Xr;ZZJ)I

    move-result v1

    .line 24493
    .local v0, "result":I
    const/4 v0, -0x4

    if-ne v1, v0, :cond_2

    .line 24494
    invoke-direct {p0, p1}, Lcom/facebook/ads/redexgen/X/CD;->A0C(I)V

    .line 24495
    :cond_1
    :goto_0
    return v1

    .line 24496
    :cond_2
    if-ne v1, v2, :cond_1

    .line 24497
    invoke-direct {p0, p1}, Lcom/facebook/ads/redexgen/X/CD;->A0D(I)V

    goto :goto_0
.end method

.method public final A0Q()V
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 24498
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/CD;->A0X:Lcom/facebook/ads/redexgen/X/Vq;

    iget v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A00:I

    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/Vq;->A06(I)V

    .line 24499
    return-void
.end method

.method public final A0R()V
    .locals 4

    .line 24500
    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A0F:Z

    if-eqz v0, :cond_0

    .line 24501
    iget-object v3, p0, Lcom/facebook/ads/redexgen/X/CD;->A0K:[Lcom/facebook/ads/redexgen/X/WO;

    array-length v2, v3

    const/4 v1, 0x0

    :goto_0
    if-ge v1, v2, :cond_0

    aget-object v0, v3, v1

    .line 24502
    .local v3, "sampleQueue":Lcom/facebook/ads/redexgen/X/WO;
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/WO;->A0H()V

    .line 24503
    .end local v3    # "sampleQueue":Lcom/facebook/ads/redexgen/X/WO;
    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    .line 24504
    :cond_0
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A0X:Lcom/facebook/ads/redexgen/X/Vq;

    invoke-virtual {v0, p0}, Lcom/facebook/ads/redexgen/X/Vq;->A07(Lcom/facebook/ads/redexgen/X/H9;)V

    .line 24505
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/CD;->A0R:Landroid/os/Handler;

    const/4 v0, 0x0

    invoke-virtual {v1, v0}, Landroid/os/Handler;->removeCallbacksAndMessages(Ljava/lang/Object;)V

    .line 24506
    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A08:Lcom/facebook/ads/redexgen/X/WQ;

    .line 24507
    const/4 v0, 0x1

    iput-boolean v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A0G:Z

    .line 24508
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A0U:Lcom/facebook/ads/redexgen/X/F1;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/F1;->A04()V

    .line 24509
    return-void
.end method

.method public final A0S(I)Z
    .locals 1

    .line 24510
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/CD;->A0J()Z

    move-result v0

    if-nez v0, :cond_1

    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A0B:Z

    if-nez v0, :cond_0

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A0K:[Lcom/facebook/ads/redexgen/X/WO;

    aget-object v0, v0, p1

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/WO;->A0M()Z

    move-result v0

    if-eqz v0, :cond_1

    :cond_0
    const/4 v0, 0x1

    :goto_0
    return v0

    :cond_1
    const/4 v0, 0x0

    goto :goto_0
.end method

.method public final A4T(J)Z
    .locals 2

    .line 24511
    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A0B:Z

    if-nez v0, :cond_0

    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A0E:Z

    if-nez v0, :cond_0

    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A0F:Z

    if-eqz v0, :cond_1

    iget v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A01:I

    if-nez v0, :cond_1

    .line 24512
    .end local v0
    :cond_0
    const/4 v0, 0x0

    return v0

    .line 24513
    :cond_1
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A0Y:Lcom/facebook/ads/redexgen/X/Hg;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/Hg;->A02()Z

    move-result v1

    .line 24514
    .local v0, "continuedLoading":Z
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A0X:Lcom/facebook/ads/redexgen/X/Vq;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/Vq;->A08()Z

    move-result v0

    if-nez v0, :cond_2

    .line 24515
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/CD;->A0A()V

    .line 24516
    const/4 v1, 0x1

    .line 24517
    :cond_2
    return v1
.end method

.method public final A5A(JZ)V
    .locals 4

    .line 24518
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A0K:[Lcom/facebook/ads/redexgen/X/WO;

    array-length v3, v0

    .line 24519
    .local v0, "trackCount":I
    const/4 v2, 0x0

    .local v1, "i":I
    :goto_0
    if-ge v2, v3, :cond_0

    .line 24520
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A0K:[Lcom/facebook/ads/redexgen/X/WO;

    aget-object v1, v0, v2

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A0L:[Z

    aget-boolean v0, v0, v2

    invoke-virtual {v1, p1, p2, p3, v0}, Lcom/facebook/ads/redexgen/X/WO;->A0K(JZZ)V

    .line 24521
    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    .line 24522
    .end local v1    # "i":I
    :cond_0
    return-void
.end method

.method public final A5Y()V
    .locals 2

    .line 24523
    const/4 v0, 0x1

    iput-boolean v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A0H:Z

    .line 24524
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/CD;->A0R:Landroid/os/Handler;

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A0Z:Ljava/lang/Runnable;

    invoke-virtual {v1, v0}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    .line 24525
    return-void
.end method

.method public final A5x(JLcom/facebook/ads/redexgen/X/AD;)J
    .locals 9

    .line 24526
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A07:Lcom/facebook/ads/redexgen/X/C1;

    invoke-interface {v0}, Lcom/facebook/ads/redexgen/X/C1;->A9I()Z

    move-result v0

    if-nez v0, :cond_0

    .line 24527
    const-wide/16 v0, 0x0

    return-wide v0

    .line 24528
    :cond_0
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A07:Lcom/facebook/ads/redexgen/X/C1;

    move-wide v2, p1

    invoke-interface {v0, v2, v3}, Lcom/facebook/ads/redexgen/X/C1;->A7t(J)Lcom/facebook/ads/redexgen/X/C0;

    move-result-object v1

    .line 24529
    .local v0, "seekPoints":Lcom/facebook/ads/redexgen/X/C0;
    iget-object v0, v1, Lcom/facebook/ads/redexgen/X/C0;->A00:Lcom/facebook/ads/redexgen/X/C2;

    iget-wide v5, v0, Lcom/facebook/ads/redexgen/X/C2;->A01:J

    iget-object v0, v1, Lcom/facebook/ads/redexgen/X/C0;->A01:Lcom/facebook/ads/redexgen/X/C2;

    iget-wide v7, v0, Lcom/facebook/ads/redexgen/X/C2;->A01:J

    move-object v4, p3

    invoke-static/range {v2 .. v8}, Lcom/facebook/ads/redexgen/X/IF;->A0I(JLcom/facebook/ads/redexgen/X/AD;JJ)J

    move-result-wide v0

    return-wide v0
.end method

.method public final A6D()J
    .locals 8

    .line 24530
    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A0B:Z

    const-wide/high16 v6, -0x8000000000000000L

    if-eqz v0, :cond_0

    .line 24531
    return-wide v6

    .line 24532
    :cond_0
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/CD;->A0I()Z

    move-result v0

    if-eqz v0, :cond_2

    .line 24533
    iget-wide v3, p0, Lcom/facebook/ads/redexgen/X/CD;->A06:J

    sget-object v2, Lcom/facebook/ads/redexgen/X/CD;->A0d:[Ljava/lang/String;

    const/4 v0, 0x7

    aget-object v1, v2, v0

    const/4 v0, 0x1

    aget-object v2, v2, v0

    const/16 v0, 0x1a

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    invoke-virtual {v2, v0}, Ljava/lang/String;->charAt(I)C

    move-result v0

    if-eq v1, v0, :cond_1

    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    :cond_1
    sget-object v2, Lcom/facebook/ads/redexgen/X/CD;->A0d:[Ljava/lang/String;

    const-string v1, "ld4wWqv9tK9nTvU7QEEl8vpde5wWqGSi"

    const/4 v0, 0x3

    aput-object v1, v2, v0

    return-wide v3

    .line 24534
    :cond_2
    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A0A:Z

    if-eqz v0, :cond_4

    .line 24535
    const-wide v2, 0x7fffffffffffffffL

    .line 24536
    .local v3, "largestQueuedTimestampUs":J
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A0K:[Lcom/facebook/ads/redexgen/X/WO;

    array-length v5, v0

    .line 24537
    .local v0, "trackCount":I
    const/4 v4, 0x0

    .local v5, "i":I
    :goto_0
    if-ge v4, v5, :cond_5

    .line 24538
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A0N:[Z

    aget-boolean v0, v0, v4

    if-eqz v0, :cond_3

    .line 24539
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A0K:[Lcom/facebook/ads/redexgen/X/WO;

    aget-object v0, v0, v4

    .line 24540
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/WO;->A0F()J

    move-result-wide v0

    invoke-static {v2, v3, v0, v1}, Ljava/lang/Math;->min(JJ)J

    move-result-wide v2

    .line 24541
    :cond_3
    add-int/lit8 v4, v4, 0x1

    goto :goto_0

    .line 24542
    .end local v3    # "largestQueuedTimestampUs":J
    :cond_4
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/CD;->A02()J

    move-result-wide v2

    .line 24543
    .restart local v3    # "largestQueuedTimestampUs":J
    :cond_5
    cmp-long v0, v2, v6

    if-nez v0, :cond_6

    .line 24544
    iget-wide v2, p0, Lcom/facebook/ads/redexgen/X/CD;->A04:J

    .line 24545
    :cond_6
    return-wide v2
.end method

.method public final A7U()J
    .locals 2

    .line 24546
    iget v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A01:I

    if-nez v0, :cond_0

    const-wide/high16 v0, -0x8000000000000000L

    :goto_0
    return-wide v0

    :cond_0
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/CD;->A6D()J

    move-result-wide v0

    goto :goto_0
.end method

.method public final A8B()Lcom/facebook/ads/internal/exoplayer2/thirdparty/source/TrackGroupArray;
    .locals 1

    .line 24547
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A09:Lcom/facebook/ads/internal/exoplayer2/thirdparty/source/TrackGroupArray;

    return-object v0
.end method

.method public final AAN()V
    .locals 0
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 24548
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/CD;->A0Q()V

    .line 24549
    return-void
.end method

.method public final bridge synthetic ABz(Lcom/facebook/ads/redexgen/X/H8;JJZ)V
    .locals 7

    .line 24550
    move-object v1, p1

    check-cast v1, Lcom/facebook/ads/redexgen/X/WT;

    move-object v0, p0

    move-wide v2, p2

    move-wide v4, p4

    move v6, p6

    invoke-direct/range {v0 .. v6}, Lcom/facebook/ads/redexgen/X/CD;->A0G(Lcom/facebook/ads/redexgen/X/WT;JJZ)V

    return-void
.end method

.method public final bridge synthetic AC1(Lcom/facebook/ads/redexgen/X/H8;JJ)V
    .locals 6

    .line 24551
    move-object v1, p1

    check-cast v1, Lcom/facebook/ads/redexgen/X/WT;

    move-object v0, p0

    move-wide v2, p2

    move-wide v4, p4

    invoke-direct/range {v0 .. v5}, Lcom/facebook/ads/redexgen/X/CD;->A0F(Lcom/facebook/ads/redexgen/X/WT;JJ)V

    return-void
.end method

.method public final bridge synthetic AC2(Lcom/facebook/ads/redexgen/X/H8;JJLjava/io/IOException;)I
    .locals 7

    .line 24552
    move-object v1, p1

    check-cast v1, Lcom/facebook/ads/redexgen/X/WT;

    move-object v0, p0

    move-wide v2, p2

    move-wide v4, p4

    move-object v6, p6

    invoke-direct/range {v0 .. v6}, Lcom/facebook/ads/redexgen/X/CD;->A01(Lcom/facebook/ads/redexgen/X/WT;JJLjava/io/IOException;)I

    move-result v0

    return v0
.end method

.method public final AC6()V
    .locals 4

    .line 24553
    iget-object v3, p0, Lcom/facebook/ads/redexgen/X/CD;->A0K:[Lcom/facebook/ads/redexgen/X/WO;

    array-length v2, v3

    const/4 v1, 0x0

    :goto_0
    if-ge v1, v2, :cond_0

    aget-object v0, v3, v1

    .line 24554
    .local v3, "sampleQueue":Lcom/facebook/ads/redexgen/X/WO;
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/WO;->A0I()V

    .line 24555
    .end local v3    # "sampleQueue":Lcom/facebook/ads/redexgen/X/WO;
    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    .line 24556
    :cond_0
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A0S:Lcom/facebook/ads/redexgen/X/Ek;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/Ek;->A03()V

    .line 24557
    return-void
.end method

.method public final ADS(Lcom/facebook/ads/internal/exoplayer2/thirdparty/Format;)V
    .locals 2

    .line 24558
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/CD;->A0R:Landroid/os/Handler;

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A0Z:Ljava/lang/Runnable;

    invoke-virtual {v1, v0}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    .line 24559
    return-void
.end method

.method public final AE4(Lcom/facebook/ads/redexgen/X/WQ;J)V
    .locals 1

    .line 24560
    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/CD;->A08:Lcom/facebook/ads/redexgen/X/WQ;

    .line 24561
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A0Y:Lcom/facebook/ads/redexgen/X/Hg;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/Hg;->A02()Z

    .line 24562
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/CD;->A0A()V

    .line 24563
    return-void
.end method

.method public final AEL()J
    .locals 5

    .line 24564
    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A0C:Z

    if-nez v0, :cond_0

    .line 24565
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A0U:Lcom/facebook/ads/redexgen/X/F1;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/F1;->A05()V

    .line 24566
    const/4 v0, 0x1

    iput-boolean v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A0C:Z

    .line 24567
    :cond_0
    iget-boolean v3, p0, Lcom/facebook/ads/redexgen/X/CD;->A0D:Z

    sget-object v2, Lcom/facebook/ads/redexgen/X/CD;->A0d:[Ljava/lang/String;

    const/4 v0, 0x6

    aget-object v1, v2, v0

    const/4 v0, 0x0

    aget-object v0, v2, v0

    invoke-virtual {v1}, Ljava/lang/String;->length()I

    move-result v1

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v0

    if-eq v1, v0, :cond_3

    sget-object v2, Lcom/facebook/ads/redexgen/X/CD;->A0d:[Ljava/lang/String;

    const-string v1, ""

    const/4 v0, 0x2

    aput-object v1, v2, v0

    if-eqz v3, :cond_2

    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A0B:Z

    if-nez v0, :cond_1

    .line 24568
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/CD;->A00()I

    move-result v1

    iget v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A02:I

    if-le v1, v0, :cond_2

    .line 24569
    :cond_1
    const/4 v0, 0x0

    iput-boolean v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A0D:Z

    .line 24570
    iget-wide v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A04:J

    return-wide v0

    .line 24571
    :cond_2
    const-wide v3, -0x7fffffffffffffffL    # -4.9E-324

    sget-object v2, Lcom/facebook/ads/redexgen/X/CD;->A0d:[Ljava/lang/String;

    const/4 v0, 0x7

    aget-object v1, v2, v0

    const/4 v0, 0x1

    aget-object v2, v2, v0

    const/16 v0, 0x1a

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    invoke-virtual {v2, v0}, Ljava/lang/String;->charAt(I)C

    move-result v0

    if-eq v1, v0, :cond_4

    :cond_3
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    :cond_4
    sget-object v2, Lcom/facebook/ads/redexgen/X/CD;->A0d:[Ljava/lang/String;

    const-string v1, "a8TiTKBNhx8jXxPRrcAgh1BUcIw96G2f"

    const/4 v0, 0x4

    aput-object v1, v2, v0

    const-string v1, "NHpS1UqcJuZi4hHtySMRiLK65X8yfSYb"

    const/4 v0, 0x5

    aput-object v1, v2, v0

    return-wide v3
.end method

.method public final AER(J)V
    .locals 0

    .line 24572
    return-void
.end method

.method public final AFi(Lcom/facebook/ads/redexgen/X/C1;)V
    .locals 2

    .line 24573
    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/CD;->A07:Lcom/facebook/ads/redexgen/X/C1;

    .line 24574
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/CD;->A0R:Landroid/os/Handler;

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A0Z:Ljava/lang/Runnable;

    invoke-virtual {v1, v0}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    .line 24575
    return-void
.end method

.method public final AFl(J)J
    .locals 5

    .line 24576
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A07:Lcom/facebook/ads/redexgen/X/C1;

    invoke-interface {v0}, Lcom/facebook/ads/redexgen/X/C1;->A9I()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 24577
    :goto_0
    iput-wide p1, p0, Lcom/facebook/ads/redexgen/X/CD;->A04:J

    .line 24578
    const/4 v3, 0x0

    iput-boolean v3, p0, Lcom/facebook/ads/redexgen/X/CD;->A0D:Z

    .line 24579
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/CD;->A0I()Z

    move-result v0

    if-nez v0, :cond_2

    invoke-direct {p0, p1, p2}, Lcom/facebook/ads/redexgen/X/CD;->A0K(J)Z

    move-result v4

    sget-object v2, Lcom/facebook/ads/redexgen/X/CD;->A0d:[Ljava/lang/String;

    const/4 v0, 0x4

    aget-object v1, v2, v0

    const/4 v0, 0x5

    aget-object v2, v2, v0

    const/16 v0, 0x13

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    invoke-virtual {v2, v0}, Ljava/lang/String;->charAt(I)C

    move-result v0

    if-eq v1, v0, :cond_1

    sget-object v2, Lcom/facebook/ads/redexgen/X/CD;->A0d:[Ljava/lang/String;

    const-string v1, "4t4h1X84Q9aXzP6i3O1IFqP61fZwJyic"

    const/4 v0, 0x4

    aput-object v1, v2, v0

    const-string v1, "YchhDxV7HDCpyjRy2lVbypcIuIxdt4SR"

    const/4 v0, 0x5

    aput-object v1, v2, v0

    if-eqz v4, :cond_2

    .line 24580
    return-wide p1

    .line 24581
    :cond_0
    const-wide/16 p1, 0x0

    goto :goto_0

    :cond_1
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    .line 24582
    :cond_2
    iput-boolean v3, p0, Lcom/facebook/ads/redexgen/X/CD;->A0E:Z

    .line 24583
    iput-wide p1, p0, Lcom/facebook/ads/redexgen/X/CD;->A06:J

    .line 24584
    iput-boolean v3, p0, Lcom/facebook/ads/redexgen/X/CD;->A0B:Z

    .line 24585
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A0X:Lcom/facebook/ads/redexgen/X/Vq;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/Vq;->A08()Z

    move-result v0

    if-eqz v0, :cond_4

    .line 24586
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A0X:Lcom/facebook/ads/redexgen/X/Vq;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/Vq;->A05()V

    .line 24587
    :cond_3
    return-wide p1

    .line 24588
    :cond_4
    iget-object v2, p0, Lcom/facebook/ads/redexgen/X/CD;->A0K:[Lcom/facebook/ads/redexgen/X/WO;

    array-length v1, v2

    :goto_1
    if-ge v3, v1, :cond_3

    aget-object v0, v2, v3

    .line 24589
    .local v3, "sampleQueue":Lcom/facebook/ads/redexgen/X/WO;
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/WO;->A0I()V

    .line 24590
    .end local v3    # "sampleQueue":Lcom/facebook/ads/redexgen/X/WO;
    add-int/lit8 v3, v3, 0x1

    goto :goto_1
.end method

.method public final AFm([Lcom/facebook/ads/redexgen/X/Gg;[Z[Lcom/facebook/ads/redexgen/X/FB;[ZJ)J
    .locals 9

    .line 24591
    move-object v5, p0

    iget-boolean v0, v5, Lcom/facebook/ads/redexgen/X/CD;->A0F:Z

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/Ha;->A04(Z)V

    .line 24592
    iget v6, v5, Lcom/facebook/ads/redexgen/X/CD;->A01:I

    .line 24593
    .local v5, "oldEnabledTrackCount":I
    const/4 v7, 0x0

    .local v6, "i":I
    :goto_0
    array-length v0, p1

    const/4 v4, 0x0

    const/4 v3, 0x1

    if-ge v7, v0, :cond_3

    .line 24594
    aget-object v8, p3, v7

    sget-object v2, Lcom/facebook/ads/redexgen/X/CD;->A0d:[Ljava/lang/String;

    const/4 v0, 0x4

    aget-object v1, v2, v0

    const/4 v0, 0x5

    aget-object v2, v2, v0

    const/16 v0, 0x13

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    invoke-virtual {v2, v0}, Ljava/lang/String;->charAt(I)C

    move-result v0

    if-eq v1, v0, :cond_2

    sget-object v2, Lcom/facebook/ads/redexgen/X/CD;->A0d:[Ljava/lang/String;

    const-string v1, "mg4ta7ka0nUwB4FGZ9fOP"

    const/4 v0, 0x2

    aput-object v1, v2, v0

    if-eqz v8, :cond_1

    aget-object v0, p1, v7

    if-eqz v0, :cond_0

    aget-boolean v0, p2, v7

    if-nez v0, :cond_1

    .line 24595
    :cond_0
    aget-object v0, p3, v7

    check-cast v0, Lcom/facebook/ads/redexgen/X/WS;

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/WS;->A00(Lcom/facebook/ads/redexgen/X/WS;)I

    move-result v1

    .line 24596
    .local v7, "track":I
    iget-object v0, v5, Lcom/facebook/ads/redexgen/X/CD;->A0L:[Z

    aget-boolean v0, v0, v1

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/Ha;->A04(Z)V

    .line 24597
    iget v0, v5, Lcom/facebook/ads/redexgen/X/CD;->A01:I

    sub-int/2addr v0, v3

    iput v0, v5, Lcom/facebook/ads/redexgen/X/CD;->A01:I

    .line 24598
    iget-object v0, v5, Lcom/facebook/ads/redexgen/X/CD;->A0L:[Z

    aput-boolean v4, v0, v1

    .line 24599
    const/4 v0, 0x0

    aput-object v0, p3, v7

    .line 24600
    .end local v7    # "track":I
    :cond_1
    add-int/lit8 v7, v7, 0x1

    goto :goto_0

    :cond_2
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    .line 24601
    .end local v6    # "i":I
    :cond_3
    iget-boolean v0, v5, Lcom/facebook/ads/redexgen/X/CD;->A0I:Z

    if-eqz v0, :cond_8

    if-nez v6, :cond_9

    :goto_1
    const/4 v1, 0x1

    .line 24602
    .local v6, "seekRequired":Z
    :goto_2
    const/4 v6, 0x0

    .local v7, "i":I
    :goto_3
    array-length v0, p1

    if-ge v6, v0, :cond_a

    .line 24603
    aget-object v0, p3, v6

    if-nez v0, :cond_4

    aget-object v0, p1, v6

    if-eqz v0, :cond_4

    .line 24604
    aget-object v7, p1, v6

    .line 24605
    .local p1, "selection":Lcom/facebook/ads/redexgen/X/Gg;
    invoke-interface {v7}, Lcom/facebook/ads/redexgen/X/Gg;->length()I

    move-result v0

    if-ne v0, v3, :cond_7

    const/4 v0, 0x1

    :goto_4
    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/Ha;->A04(Z)V

    .line 24606
    invoke-interface {v7, v4}, Lcom/facebook/ads/redexgen/X/Gg;->A7C(I)I

    move-result v0

    if-nez v0, :cond_6

    const/4 v0, 0x1

    :goto_5
    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/Ha;->A04(Z)V

    .line 24607
    iget-object v2, v5, Lcom/facebook/ads/redexgen/X/CD;->A09:Lcom/facebook/ads/internal/exoplayer2/thirdparty/source/TrackGroupArray;

    invoke-interface {v7}, Lcom/facebook/ads/redexgen/X/Gg;->A8A()Lcom/facebook/ads/internal/exoplayer2/thirdparty/source/TrackGroup;

    move-result-object v0

    invoke-virtual {v2, v0}, Lcom/facebook/ads/internal/exoplayer2/thirdparty/source/TrackGroupArray;->A00(Lcom/facebook/ads/internal/exoplayer2/thirdparty/source/TrackGroup;)I

    move-result v2

    .line 24608
    .local p2, "track":I
    iget-object v0, v5, Lcom/facebook/ads/redexgen/X/CD;->A0L:[Z

    aget-boolean v0, v0, v2

    xor-int/2addr v0, v3

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/Ha;->A04(Z)V

    .line 24609
    iget v0, v5, Lcom/facebook/ads/redexgen/X/CD;->A01:I

    add-int/2addr v0, v3

    iput v0, v5, Lcom/facebook/ads/redexgen/X/CD;->A01:I

    .line 24610
    iget-object v0, v5, Lcom/facebook/ads/redexgen/X/CD;->A0L:[Z

    aput-boolean v3, v0, v2

    .line 24611
    new-instance v0, Lcom/facebook/ads/redexgen/X/WS;

    invoke-direct {v0, p0, v2}, Lcom/facebook/ads/redexgen/X/WS;-><init>(Lcom/facebook/ads/redexgen/X/CD;I)V

    aput-object v0, p3, v6

    .line 24612
    aput-boolean v3, p4, v6

    .line 24613
    if-nez v1, :cond_4

    .line 24614
    iget-object v0, v5, Lcom/facebook/ads/redexgen/X/CD;->A0K:[Lcom/facebook/ads/redexgen/X/WO;

    aget-object v2, v0, v2

    .line 24615
    .local p3, "sampleQueue":Lcom/facebook/ads/redexgen/X/WO;
    invoke-virtual {v2}, Lcom/facebook/ads/redexgen/X/WO;->A0J()V

    .line 24616
    invoke-virtual {v2, p5, p6, v3, v3}, Lcom/facebook/ads/redexgen/X/WO;->A0D(JZZ)I

    move-result v1

    const/4 v0, -0x1

    if-ne v1, v0, :cond_5

    .line 24617
    invoke-virtual {v2}, Lcom/facebook/ads/redexgen/X/WO;->A0B()I

    move-result v0

    if-eqz v0, :cond_5

    const/4 v1, 0x1

    .line 24618
    .end local p1    # "selection":Lcom/facebook/ads/redexgen/X/Gg;
    .end local p2    # "track":I
    .end local p3    # "sampleQueue":Lcom/facebook/ads/redexgen/X/WO;
    :cond_4
    :goto_6
    add-int/lit8 v6, v6, 0x1

    goto :goto_3

    .line 24619
    :cond_5
    const/4 v1, 0x0

    goto :goto_6

    .line 24620
    :cond_6
    const/4 v0, 0x0

    goto :goto_5

    .line 24621
    :cond_7
    const/4 v0, 0x0

    goto :goto_4

    .line 24622
    :cond_8
    const-wide/16 v1, 0x0

    cmp-long v0, p5, v1

    if-eqz v0, :cond_9

    goto :goto_1

    :cond_9
    const/4 v1, 0x0

    goto :goto_2

    .line 24623
    .end local v7    # "i":I
    :cond_a
    iget v0, v5, Lcom/facebook/ads/redexgen/X/CD;->A01:I

    if-nez v0, :cond_c

    .line 24624
    iput-boolean v4, v5, Lcom/facebook/ads/redexgen/X/CD;->A0E:Z

    .line 24625
    iput-boolean v4, v5, Lcom/facebook/ads/redexgen/X/CD;->A0D:Z

    .line 24626
    iget-object v0, v5, Lcom/facebook/ads/redexgen/X/CD;->A0X:Lcom/facebook/ads/redexgen/X/Vq;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/Vq;->A08()Z

    move-result v0

    if-eqz v0, :cond_b

    .line 24627
    iget-object v2, v5, Lcom/facebook/ads/redexgen/X/CD;->A0K:[Lcom/facebook/ads/redexgen/X/WO;

    array-length v1, v2

    :goto_7
    if-ge v4, v1, :cond_e

    aget-object v0, v2, v4

    .line 24628
    .local p2, "sampleQueue":Lcom/facebook/ads/redexgen/X/WO;
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/WO;->A0H()V

    .line 24629
    .end local p2    # "sampleQueue":Lcom/facebook/ads/redexgen/X/WO;
    add-int/lit8 v4, v4, 0x1

    goto :goto_7

    .line 24630
    :cond_b
    iget-object v2, v5, Lcom/facebook/ads/redexgen/X/CD;->A0K:[Lcom/facebook/ads/redexgen/X/WO;

    array-length v1, v2

    :goto_8
    if-ge v4, v1, :cond_f

    aget-object v0, v2, v4

    .line 24631
    .restart local p2    # "sampleQueue":Lcom/facebook/ads/redexgen/X/WO;
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/WO;->A0I()V

    .line 24632
    .end local p2    # "sampleQueue":Lcom/facebook/ads/redexgen/X/WO;
    add-int/lit8 v4, v4, 0x1

    goto :goto_8

    .line 24633
    :cond_c
    if-eqz v1, :cond_f

    .line 24634
    invoke-virtual {p0, p5, p6}, Lcom/facebook/ads/redexgen/X/CD;->AFl(J)J

    move-result-wide p5

    .line 24635
    .end local p11
    .local v3, "positionUs":J
    const/4 v1, 0x0

    .restart local v7    # "i":I
    :goto_9
    array-length v0, p3

    if-ge v1, v0, :cond_f

    .line 24636
    aget-object v0, p3, v1

    if-eqz v0, :cond_d

    .line 24637
    aput-boolean v3, p4, v1

    .line 24638
    :cond_d
    add-int/lit8 v1, v1, 0x1

    goto :goto_9

    .line 24639
    :cond_e
    iget-object v0, v5, Lcom/facebook/ads/redexgen/X/CD;->A0X:Lcom/facebook/ads/redexgen/X/Vq;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/Vq;->A05()V

    .line 24640
    .end local v7    # "i":I
    :cond_f
    iput-boolean v3, v5, Lcom/facebook/ads/redexgen/X/CD;->A0I:Z

    .line 24641
    return-wide p5
.end method

.method public final AGi(II)Lcom/facebook/ads/redexgen/X/C4;
    .locals 4

    .line 24642
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A0K:[Lcom/facebook/ads/redexgen/X/WO;

    array-length v3, v0

    .line 24643
    .local v0, "trackCount":I
    const/4 v1, 0x0

    .local v1, "i":I
    :goto_0
    if-ge v1, v3, :cond_1

    .line 24644
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A0J:[I

    aget v0, v0, v1

    if-ne v0, p1, :cond_0

    .line 24645
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A0K:[Lcom/facebook/ads/redexgen/X/WO;

    aget-object v0, v0, v1

    return-object v0

    .line 24646
    :cond_0
    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    .line 24647
    .end local v1    # "i":I
    :cond_1
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A0V:Lcom/facebook/ads/redexgen/X/Gm;

    new-instance v2, Lcom/facebook/ads/redexgen/X/WO;

    invoke-direct {v2, v0}, Lcom/facebook/ads/redexgen/X/WO;-><init>(Lcom/facebook/ads/redexgen/X/Gm;)V

    .line 24648
    .local v1, "trackOutput":Lcom/facebook/ads/redexgen/X/WO;
    invoke-virtual {v2, p0}, Lcom/facebook/ads/redexgen/X/WO;->A0L(Lcom/facebook/ads/redexgen/X/FA;)V

    .line 24649
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/CD;->A0J:[I

    add-int/lit8 v0, v3, 0x1

    invoke-static {v1, v0}, Ljava/util/Arrays;->copyOf([II)[I

    move-result-object v0

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A0J:[I

    .line 24650
    aput p1, v0, v3

    .line 24651
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/CD;->A0K:[Lcom/facebook/ads/redexgen/X/WO;

    add-int/lit8 v0, v3, 0x1

    invoke-static {v1, v0}, Ljava/util/Arrays;->copyOf([Ljava/lang/Object;I)[Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [Lcom/facebook/ads/redexgen/X/WO;

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/CD;->A0K:[Lcom/facebook/ads/redexgen/X/WO;

    .line 24652
    aput-object v2, v0, v3

    .line 24653
    return-object v2
.end method
