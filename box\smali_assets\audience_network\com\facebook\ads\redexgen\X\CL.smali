.class public interface abstract Lcom/facebook/ads/redexgen/X/CL;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/facebook/ads/redexgen/X/CO;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "SampleSizeBox"
.end annotation


# virtual methods
.method public abstract A7o()I
.end method

.method public abstract A93()Z
.end method

.method public abstract AEN()I
.end method
