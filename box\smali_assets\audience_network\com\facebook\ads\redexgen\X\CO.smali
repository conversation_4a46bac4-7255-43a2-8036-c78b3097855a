.class public abstract Lcom/facebook/ads/redexgen/X/CO;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/facebook/ads/redexgen/X/XQ;,
        Lcom/facebook/ads/redexgen/X/XR;,
        Lcom/facebook/ads/redexgen/X/CL;,
        Lcom/facebook/ads/redexgen/X/CM;,
        Lcom/facebook/ads/redexgen/X/CN;,
        Lcom/facebook/ads/redexgen/X/CK;,
        Lcom/facebook/ads/redexgen/X/XP;
    }
.end annotation


# static fields
.field public static A00:[B

.field public static A01:[Ljava/lang/String;

.field public static final A02:I

.field public static final A03:I

.field public static final A04:I

.field public static final A05:I

.field public static final A06:I

.field public static final A07:I

.field public static final A08:I


# direct methods
.method public static constructor <clinit>()V
    .locals 3

    .line 1104
    const/16 v0, 0x8

    new-array v2, v0, [Ljava/lang/String;

    const/4 v1, 0x0

    const-string v0, "b"

    aput-object v0, v2, v1

    const/4 v1, 0x1

    const-string v0, "1fRxwJ5XE31BEFto"

    aput-object v0, v2, v1

    const/4 v1, 0x2

    const-string v0, "uScK6DjhwaakoKLKywWqQqJcG3f6AUTY"

    aput-object v0, v2, v1

    const/4 v1, 0x3

    const-string v0, "4Hlq2lRki9cJUEzjlqXKmdMa0dcUUFBD"

    aput-object v0, v2, v1

    const/4 v1, 0x4

    const-string v0, "g78w6vvkNWOU54nDIB5PtPh0iS7AkCQe"

    aput-object v0, v2, v1

    const/4 v1, 0x5

    const-string v0, "b"

    aput-object v0, v2, v1

    const/4 v1, 0x6

    const-string v0, "Krct3XGjvdQG30dTRHyGL58gGhCYlC87"

    aput-object v0, v2, v1

    const/4 v1, 0x7

    const-string v0, "LauCO"

    aput-object v0, v2, v1

    sput-object v2, Lcom/facebook/ads/redexgen/X/CO;->A01:[Ljava/lang/String;

    invoke-static {}, Lcom/facebook/ads/redexgen/X/CO;->A0J()V

    const/16 v2, 0x2b8

    const/4 v1, 0x4

    const/16 v0, 0x31

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/CO;->A0I(III)Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/IF;->A08(Ljava/lang/String;)I

    move-result v0

    sput v0, Lcom/facebook/ads/redexgen/X/CO;->A08:I

    .line 1105
    const/16 v2, 0x296

    const/4 v1, 0x4

    const/16 v0, 0x76

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/CO;->A0I(III)Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/IF;->A08(Ljava/lang/String;)I

    move-result v0

    sput v0, Lcom/facebook/ads/redexgen/X/CO;->A05:I

    .line 1106
    const/16 v2, 0x2b4

    const/4 v1, 0x4

    const/16 v0, 0x43

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/CO;->A0I(III)Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/IF;->A08(Ljava/lang/String;)I

    move-result v0

    sput v0, Lcom/facebook/ads/redexgen/X/CO;->A07:I

    .line 1107
    const/16 v2, 0x27c

    const/4 v1, 0x4

    const/16 v0, 0xc

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/CO;->A0I(III)Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/IF;->A08(Ljava/lang/String;)I

    move-result v0

    sput v0, Lcom/facebook/ads/redexgen/X/CO;->A04:I

    .line 1108
    const/16 v2, 0x29a

    const/4 v1, 0x4

    const/16 v0, 0x5f

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/CO;->A0I(III)Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/IF;->A08(Ljava/lang/String;)I

    move-result v0

    sput v0, Lcom/facebook/ads/redexgen/X/CO;->A06:I

    .line 1109
    const/16 v2, 0x25e

    const/4 v1, 0x4

    const/16 v0, 0xf

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/CO;->A0I(III)Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/IF;->A08(Ljava/lang/String;)I

    move-result v0

    sput v0, Lcom/facebook/ads/redexgen/X/CO;->A02:I

    .line 1110
    const/16 v2, 0x278

    const/4 v1, 0x4

    const/16 v0, 0x78

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/CO;->A0I(III)Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/IF;->A08(Ljava/lang/String;)I

    move-result v0

    sput v0, Lcom/facebook/ads/redexgen/X/CO;->A03:I

    return-void
.end method

.method public static A00(Lcom/facebook/ads/redexgen/X/Hz;I)F
    .locals 2

    .line 25171
    add-int/lit8 v0, p1, 0x8

    invoke-virtual {p0, v0}, Lcom/facebook/ads/redexgen/X/Hz;->A0Y(I)V

    .line 25172
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Hz;->A0H()I

    move-result v1

    .line 25173
    .local v0, "hSpacing":I
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Hz;->A0H()I

    move-result v0

    .line 25174
    .local v1, "vSpacing":I
    int-to-float v1, v1

    int-to-float v0, v0

    div-float/2addr v1, v0

    return v1
.end method

.method public static A01(Lcom/facebook/ads/redexgen/X/Hz;)I
    .locals 3

    .line 25175
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Hz;->A0E()I

    move-result v2

    .line 25176
    .local v0, "currentByte":I
    and-int/lit8 v1, v2, 0x7f

    .line 25177
    .local v1, "size":I
    :goto_0
    and-int/lit16 v2, v2, 0x80

    const/16 v0, 0x80

    if-ne v2, v0, :cond_0

    .line 25178
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Hz;->A0E()I

    move-result v2

    .line 25179
    shl-int/lit8 v1, v1, 0x7

    and-int/lit8 v0, v2, 0x7f

    or-int/2addr v1, v0

    goto :goto_0

    .line 25180
    :cond_0
    return v1
.end method

.method public static A02(Lcom/facebook/ads/redexgen/X/Hz;)I
    .locals 1

    .line 25181
    const/16 v0, 0x10

    invoke-virtual {p0, v0}, Lcom/facebook/ads/redexgen/X/Hz;->A0Y(I)V

    .line 25182
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Hz;->A08()I

    move-result p0

    .line 25183
    .local v0, "trackType":I
    sget v0, Lcom/facebook/ads/redexgen/X/CO;->A05:I

    if-ne p0, v0, :cond_0

    .line 25184
    const/4 v0, 0x1

    return v0

    .line 25185
    :cond_0
    sget v0, Lcom/facebook/ads/redexgen/X/CO;->A08:I

    if-ne p0, v0, :cond_1

    .line 25186
    const/4 v0, 0x2

    return v0

    .line 25187
    :cond_1
    sget v0, Lcom/facebook/ads/redexgen/X/CO;->A07:I

    if-eq p0, v0, :cond_2

    sget v0, Lcom/facebook/ads/redexgen/X/CO;->A04:I

    if-eq p0, v0, :cond_2

    sget v0, Lcom/facebook/ads/redexgen/X/CO;->A06:I

    if-eq p0, v0, :cond_2

    sget v0, Lcom/facebook/ads/redexgen/X/CO;->A02:I

    if-ne p0, v0, :cond_3

    .line 25188
    :cond_2
    const/4 v0, 0x3

    return v0

    .line 25189
    :cond_3
    sget v0, Lcom/facebook/ads/redexgen/X/CO;->A03:I

    if-ne p0, v0, :cond_4

    .line 25190
    const/4 v0, 0x4

    return v0

    .line 25191
    :cond_4
    const/4 v0, -0x1

    return v0
.end method

.method public static A03(Lcom/facebook/ads/redexgen/X/Hz;II)I
    .locals 7

    .line 25192
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Hz;->A06()I

    move-result v3

    .line 25193
    .local v0, "childAtomPosition":I
    :goto_0
    sub-int v0, v3, p1

    if-ge v0, p2, :cond_3

    .line 25194
    invoke-virtual {p0, v3}, Lcom/facebook/ads/redexgen/X/Hz;->A0Y(I)V

    .line 25195
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Hz;->A08()I

    move-result v6

    .line 25196
    .local v1, "childAtomSize":I
    if-lez v6, :cond_2

    const/4 v4, 0x1

    :goto_1
    const/16 v5, 0x23e

    sget-object v1, Lcom/facebook/ads/redexgen/X/CO;->A01:[Ljava/lang/String;

    const/4 v0, 0x4

    aget-object v1, v1, v0

    const/16 v0, 0x18

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    const/16 v0, 0x69

    if-eq v1, v0, :cond_0

    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    :cond_0
    sget-object v2, Lcom/facebook/ads/redexgen/X/CO;->A01:[Ljava/lang/String;

    const-string v1, "NygnrBLA11LVP9MJCR7n8rNii2RIBIyr"

    const/4 v0, 0x4

    aput-object v1, v2, v0

    const/16 v1, 0x20

    const/16 v0, 0x57

    invoke-static {v5, v1, v0}, Lcom/facebook/ads/redexgen/X/CO;->A0I(III)Ljava/lang/String;

    move-result-object v0

    invoke-static {v4, v0}, Lcom/facebook/ads/redexgen/X/Ha;->A05(ZLjava/lang/Object;)V

    .line 25197
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Hz;->A08()I

    move-result v1

    .line 25198
    .local v2, "childType":I
    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A0T:I

    if-ne v1, v0, :cond_1

    .line 25199
    return v3

    .line 25200
    :cond_1
    add-int/2addr v3, v6

    .line 25201
    .end local v1    # "childAtomSize":I
    .end local v2    # "childType":I
    goto :goto_0

    .line 25202
    :cond_2
    const/4 v4, 0x0

    goto :goto_1

    .line 25203
    :cond_3
    const/4 v0, -0x1

    return v0
.end method

.method public static A04(Lcom/facebook/ads/redexgen/X/Hz;)J
    .locals 3

    .line 25204
    const/16 v1, 0x8

    invoke-virtual {p0, v1}, Lcom/facebook/ads/redexgen/X/Hz;->A0Y(I)V

    .line 25205
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Hz;->A08()I

    move-result v0

    .line 25206
    .local v1, "fullAtom":I
    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/CJ;->A01(I)I

    move-result v0

    .line 25207
    .local v2, "version":I
    if-nez v0, :cond_0

    :goto_0
    invoke-virtual {p0, v1}, Lcom/facebook/ads/redexgen/X/Hz;->A0Z(I)V

    sget-object v1, Lcom/facebook/ads/redexgen/X/CO;->A01:[Ljava/lang/String;

    const/4 v0, 0x1

    aget-object v0, v1, v0

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v1

    const/16 v0, 0xc

    if-eq v1, v0, :cond_1

    .line 25208
    sget-object v2, Lcom/facebook/ads/redexgen/X/CO;->A01:[Ljava/lang/String;

    const-string v1, "3W9G8I03JPUdsev"

    const/4 v0, 0x7

    aput-object v1, v2, v0

    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Hz;->A0M()J

    move-result-wide v0

    return-wide v0

    .line 25209
    :cond_0
    const/16 v1, 0x10

    goto :goto_0

    :cond_1
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0
.end method

.method public static A05(Lcom/facebook/ads/redexgen/X/XT;)Landroid/util/Pair;
    .locals 8
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/facebook/ads/redexgen/X/XT;",
            ")",
            "Landroid/util/Pair<",
            "[J[J>;"
        }
    .end annotation

    .line 25210
    if-eqz p0, :cond_0

    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A0P:I

    invoke-virtual {p0, v0}, Lcom/facebook/ads/redexgen/X/XT;->A07(I)Lcom/facebook/ads/redexgen/X/XS;

    move-result-object v0

    .local v1, "elst":Lcom/facebook/ads/redexgen/X/XS;
    if-nez v0, :cond_1

    .line 25211
    .end local v0
    .end local v1    # "elst":Lcom/facebook/ads/redexgen/X/XS;
    .end local v2
    .end local v3
    .end local v4
    .end local v5
    .end local v6
    :cond_0
    const/4 v0, 0x0

    invoke-static {v0, v0}, Landroid/util/Pair;->create(Ljava/lang/Object;Ljava/lang/Object;)Landroid/util/Pair;

    move-result-object v0

    return-object v0

    .line 25212
    :cond_1
    iget-object p0, v0, Lcom/facebook/ads/redexgen/X/XS;->A00:Lcom/facebook/ads/redexgen/X/Hz;

    .line 25213
    .local v0, "elstData":Lcom/facebook/ads/redexgen/X/Hz;
    const/16 v0, 0x8

    invoke-virtual {p0, v0}, Lcom/facebook/ads/redexgen/X/Hz;->A0Y(I)V

    .line 25214
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Hz;->A08()I

    move-result v0

    .line 25215
    .local v2, "fullAtom":I
    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/CJ;->A01(I)I

    move-result v7

    .line 25216
    .local v3, "version":I
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Hz;->A0H()I

    move-result v6

    .line 25217
    .local v4, "entryCount":I
    new-array v5, v6, [J

    .line 25218
    .local v5, "editListDurations":[J
    new-array v4, v6, [J

    .line 25219
    .local v6, "editListMediaTimes":[J
    const/4 v3, 0x0

    .local v7, "i":I
    :goto_0
    if-ge v3, v6, :cond_5

    .line 25220
    const/4 v2, 0x1

    if-ne v7, v2, :cond_3

    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Hz;->A0N()J

    move-result-wide v0

    :goto_1
    aput-wide v0, v5, v3

    .line 25221
    if-ne v7, v2, :cond_2

    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Hz;->A0L()J

    move-result-wide v0

    :goto_2
    aput-wide v0, v4, v3

    .line 25222
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Hz;->A0U()S

    move-result v0

    .line 25223
    .local p1, "mediaRateInteger":I
    if-ne v0, v2, :cond_4

    .line 25224
    const/4 v0, 0x2

    invoke-virtual {p0, v0}, Lcom/facebook/ads/redexgen/X/Hz;->A0Z(I)V

    .line 25225
    .end local p1
    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    .line 25226
    :cond_2
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Hz;->A08()I

    move-result v0

    int-to-long v0, v0

    goto :goto_2

    .line 25227
    :cond_3
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Hz;->A0M()J

    move-result-wide v0

    goto :goto_1

    .line 25228
    .restart local p1
    :cond_4
    const/16 v2, 0x110

    const/16 v1, 0x17

    const/16 v0, 0x5c

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/CO;->A0I(III)Ljava/lang/String;

    move-result-object v1

    new-instance v0, Ljava/lang/IllegalArgumentException;

    invoke-direct {v0, v1}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw v0

    .line 25229
    .end local v7    # "i":I
    .end local p1
    :cond_5
    invoke-static {v5, v4}, Landroid/util/Pair;->create(Ljava/lang/Object;Ljava/lang/Object;)Landroid/util/Pair;

    move-result-object v0

    return-object v0
.end method

.method public static A06(Lcom/facebook/ads/redexgen/X/Hz;)Landroid/util/Pair;
    .locals 7
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/facebook/ads/redexgen/X/Hz;",
            ")",
            "Landroid/util/Pair<",
            "Ljava/lang/Long;",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    .line 25230
    const/16 v2, 0x8

    invoke-virtual {p0, v2}, Lcom/facebook/ads/redexgen/X/Hz;->A0Y(I)V

    .line 25231
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Hz;->A08()I

    move-result v0

    .line 25232
    .local v1, "fullAtom":I
    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/CJ;->A01(I)I

    move-result v1

    .line 25233
    .local v2, "version":I
    if-nez v1, :cond_1

    const/16 v0, 0x8

    :goto_0
    invoke-virtual {p0, v0}, Lcom/facebook/ads/redexgen/X/Hz;->A0Z(I)V

    .line 25234
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Hz;->A0M()J

    move-result-wide v5

    .line 25235
    .local v3, "timescale":J
    if-nez v1, :cond_0

    const/4 v2, 0x4

    :cond_0
    invoke-virtual {p0, v2}, Lcom/facebook/ads/redexgen/X/Hz;->A0Z(I)V

    .line 25236
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Hz;->A0I()I

    move-result v4

    .line 25237
    .local v0, "languageCode":I
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const/4 v2, 0x0

    const/4 v1, 0x0

    const/16 v0, 0x55

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/CO;->A0I(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    shr-int/lit8 v0, v4, 0xa

    and-int/lit8 v0, v0, 0x1f

    add-int/lit8 v0, v0, 0x60

    int-to-char v0, v0

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    move-result-object v1

    shr-int/lit8 v0, v4, 0x5

    and-int/lit8 v0, v0, 0x1f

    add-int/lit8 v0, v0, 0x60

    int-to-char v0, v0

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    move-result-object v1

    and-int/lit8 v0, v4, 0x1f

    add-int/lit8 v0, v0, 0x60

    int-to-char v0, v0

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    .line 25238
    .local v5, "language":Ljava/lang/String;
    invoke-static {v5, v6}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    move-result-object v0

    invoke-static {v0, v1}, Landroid/util/Pair;->create(Ljava/lang/Object;Ljava/lang/Object;)Landroid/util/Pair;

    move-result-object v0

    return-object v0

    .line 25239
    :cond_1
    const/16 v0, 0x10

    goto :goto_0
.end method

.method public static A07(Lcom/facebook/ads/redexgen/X/Hz;I)Landroid/util/Pair;
    .locals 7
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/facebook/ads/redexgen/X/Hz;",
            "I)",
            "Landroid/util/Pair<",
            "Ljava/lang/String;",
            "[B>;"
        }
    .end annotation

    .line 25240
    add-int/lit8 v0, p1, 0x8

    add-int/lit8 v0, v0, 0x4

    invoke-virtual {p0, v0}, Lcom/facebook/ads/redexgen/X/Hz;->A0Y(I)V

    .line 25241
    const/4 v4, 0x1

    invoke-virtual {p0, v4}, Lcom/facebook/ads/redexgen/X/Hz;->A0Z(I)V

    .line 25242
    invoke-static {p0}, Lcom/facebook/ads/redexgen/X/CO;->A01(Lcom/facebook/ads/redexgen/X/Hz;)I

    .line 25243
    const/4 v5, 0x2

    invoke-virtual {p0, v5}, Lcom/facebook/ads/redexgen/X/Hz;->A0Z(I)V

    .line 25244
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Hz;->A0E()I

    move-result v6

    .line 25245
    .local v2, "flags":I
    and-int/lit16 v0, v6, 0x80

    if-eqz v0, :cond_0

    .line 25246
    invoke-virtual {p0, v5}, Lcom/facebook/ads/redexgen/X/Hz;->A0Z(I)V

    .line 25247
    :cond_0
    and-int/lit8 v3, v6, 0x40

    sget-object v1, Lcom/facebook/ads/redexgen/X/CO;->A01:[Ljava/lang/String;

    const/4 v0, 0x6

    aget-object v1, v1, v0

    const/16 v0, 0x12

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    const/16 v0, 0x79

    if-eq v1, v0, :cond_1

    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    :cond_1
    sget-object v2, Lcom/facebook/ads/redexgen/X/CO;->A01:[Ljava/lang/String;

    const-string v1, "xKeFRQRG14ulCThjCTH6nGG5i69swr7b"

    const/4 v0, 0x4

    aput-object v1, v2, v0

    if-eqz v3, :cond_2

    .line 25248
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Hz;->A0I()I

    move-result v0

    invoke-virtual {p0, v0}, Lcom/facebook/ads/redexgen/X/Hz;->A0Z(I)V

    .line 25249
    :cond_2
    and-int/lit8 v0, v6, 0x20

    if-eqz v0, :cond_3

    .line 25250
    invoke-virtual {p0, v5}, Lcom/facebook/ads/redexgen/X/Hz;->A0Z(I)V

    .line 25251
    :cond_3
    invoke-virtual {p0, v4}, Lcom/facebook/ads/redexgen/X/Hz;->A0Z(I)V

    .line 25252
    invoke-static {p0}, Lcom/facebook/ads/redexgen/X/CO;->A01(Lcom/facebook/ads/redexgen/X/Hz;)I

    .line 25253
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Hz;->A0E()I

    move-result v0

    .line 25254
    .local v1, "objectTypeIndication":I
    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/Hs;->A03(I)Ljava/lang/String;

    move-result-object v3

    .line 25255
    .local v3, "mimeType":Ljava/lang/String;
    const/16 v2, 0x1e2

    const/16 v1, 0xa

    const/16 v0, 0x5f

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/CO;->A0I(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v0, v3}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_4

    .line 25256
    const/16 v2, 0x1f5

    const/16 v1, 0xd

    const/16 v0, 0x7b

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/CO;->A0I(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v0, v3}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_4

    .line 25257
    const/16 v2, 0x202

    const/16 v1, 0x10

    const/16 v0, 0x30

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/CO;->A0I(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v0, v3}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_6

    .line 25258
    .end local v0
    .end local v4
    :cond_4
    const/4 v0, 0x0

    invoke-static {v3, v0}, Landroid/util/Pair;->create(Ljava/lang/Object;Ljava/lang/Object;)Landroid/util/Pair;

    move-result-object v3

    sget-object v1, Lcom/facebook/ads/redexgen/X/CO;->A01:[Ljava/lang/String;

    const/4 v0, 0x6

    aget-object v1, v1, v0

    const/16 v0, 0x12

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    const/16 v0, 0x79

    if-eq v1, v0, :cond_5

    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    :cond_5
    sget-object v2, Lcom/facebook/ads/redexgen/X/CO;->A01:[Ljava/lang/String;

    const-string v1, "4"

    const/4 v0, 0x0

    aput-object v1, v2, v0

    const-string v1, "i"

    const/4 v0, 0x5

    aput-object v1, v2, v0

    return-object v3

    .line 25259
    :cond_6
    const/16 v0, 0xc

    invoke-virtual {p0, v0}, Lcom/facebook/ads/redexgen/X/Hz;->A0Z(I)V

    .line 25260
    invoke-virtual {p0, v4}, Lcom/facebook/ads/redexgen/X/Hz;->A0Z(I)V

    .line 25261
    invoke-static {p0}, Lcom/facebook/ads/redexgen/X/CO;->A01(Lcom/facebook/ads/redexgen/X/Hz;)I

    move-result v2

    .line 25262
    .local v0, "initializationDataSize":I
    new-array v1, v2, [B

    .line 25263
    .local v4, "initializationData":[B
    const/4 v0, 0x0

    invoke-virtual {p0, v1, v0, v2}, Lcom/facebook/ads/redexgen/X/Hz;->A0c([BII)V

    .line 25264
    invoke-static {v3, v1}, Landroid/util/Pair;->create(Ljava/lang/Object;Ljava/lang/Object;)Landroid/util/Pair;

    move-result-object v0

    return-object v0
.end method

.method public static A08(Lcom/facebook/ads/redexgen/X/Hz;II)Landroid/util/Pair;
    .locals 10
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/facebook/ads/redexgen/X/Hz;",
            "II)",
            "Landroid/util/Pair<",
            "Ljava/lang/Integer;",
            "Lcom/facebook/ads/redexgen/X/Cf;",
            ">;"
        }
    .end annotation

    .line 25265
    add-int/lit8 v7, p1, 0x8

    .line 25266
    .local v0, "childPosition":I
    const/4 v6, -0x1

    .line 25267
    .local v1, "schemeInformationBoxPosition":I
    const/4 v5, 0x0

    .line 25268
    .local v2, "schemeInformationBoxSize":I
    const/4 v4, 0x0

    .line 25269
    .local v3, "schemeType":Ljava/lang/String;
    const/4 v3, 0x0

    .line 25270
    .local v4, "dataFormat":Ljava/lang/Integer;
    :goto_0
    sub-int v0, v7, p1

    if-ge v0, p2, :cond_4

    .line 25271
    invoke-virtual {p0, v7}, Lcom/facebook/ads/redexgen/X/Hz;->A0Y(I)V

    .line 25272
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Hz;->A08()I

    move-result v8

    .line 25273
    .local v5, "childAtomSize":I
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Hz;->A08()I

    move-result v1

    .line 25274
    .local v6, "childAtomType":I
    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A0U:I

    if-ne v1, v0, :cond_1

    .line 25275
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Hz;->A08()I

    move-result v0

    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v3

    .line 25276
    :cond_0
    :goto_1
    add-int/2addr v7, v8

    .line 25277
    .end local v5    # "childAtomSize":I
    .end local v6    # "childAtomType":I
    goto :goto_0

    .line 25278
    :cond_1
    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A10:I

    if-ne v1, v0, :cond_2

    .line 25279
    const/4 v0, 0x4

    invoke-virtual {p0, v0}, Lcom/facebook/ads/redexgen/X/Hz;->A0Z(I)V

    .line 25280
    invoke-virtual {p0, v0}, Lcom/facebook/ads/redexgen/X/Hz;->A0S(I)Ljava/lang/String;

    move-result-object v4

    goto :goto_1

    .line 25281
    :cond_2
    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A0z:I

    if-ne v1, v0, :cond_0

    .line 25282
    move v6, v7

    sget-object v1, Lcom/facebook/ads/redexgen/X/CO;->A01:[Ljava/lang/String;

    const/4 v0, 0x1

    aget-object v0, v1, v0

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v1

    const/16 v0, 0xc

    if-eq v1, v0, :cond_3

    .line 25283
    sget-object v2, Lcom/facebook/ads/redexgen/X/CO;->A01:[Ljava/lang/String;

    const-string v1, "1Dp8zcpsjMOU2LphvBo6Szkhi7qi1syu"

    const/4 v0, 0x4

    aput-object v1, v2, v0

    move v5, v8

    goto :goto_1

    :cond_3
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    .line 25284
    :cond_4
    const/16 v9, 0x236

    const/4 v8, 0x4

    const/16 v7, 0x4d

    sget-object v1, Lcom/facebook/ads/redexgen/X/CO;->A01:[Ljava/lang/String;

    const/4 v0, 0x4

    aget-object v1, v1, v0

    const/16 v0, 0x18

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    const/16 v0, 0x69

    if-eq v1, v0, :cond_9

    sget-object v2, Lcom/facebook/ads/redexgen/X/CO;->A01:[Ljava/lang/String;

    const-string v1, "sI3X7CYNkWTnNUBuJepPqwY6PzAeX7x4"

    const/4 v0, 0x2

    aput-object v1, v2, v0

    invoke-static {v9, v8, v7}, Lcom/facebook/ads/redexgen/X/CO;->A0I(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v0, v4}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_5

    .line 25285
    :goto_2
    const/16 v2, 0x22e

    const/4 v1, 0x4

    const/16 v0, 0x49

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/CO;->A0I(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v0, v4}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_5

    .line 25286
    const/16 v2, 0x23a

    const/4 v1, 0x4

    const/16 v0, 0x4d

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/CO;->A0I(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v0, v4}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_5

    .line 25287
    const/16 v2, 0x232

    const/4 v1, 0x4

    const/16 v0, 0x7d

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/CO;->A0I(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v0, v4}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_b

    .line 25288
    :cond_5
    const/4 v7, 0x1

    sget-object v1, Lcom/facebook/ads/redexgen/X/CO;->A01:[Ljava/lang/String;

    const/4 v0, 0x3

    aget-object v1, v1, v0

    const/16 v0, 0x12

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    const/16 v0, 0x79

    if-eq v1, v0, :cond_a

    sget-object v2, Lcom/facebook/ads/redexgen/X/CO;->A01:[Ljava/lang/String;

    const-string v1, "c3g2qilHanrF8nzgjzcjaWK2icn944It"

    const/4 v0, 0x4

    aput-object v1, v2, v0

    if-eqz v3, :cond_8

    const/4 v8, 0x1

    :goto_3
    const/16 v2, 0x262

    const/16 v1, 0x16

    const/16 v0, 0x10

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/CO;->A0I(III)Ljava/lang/String;

    move-result-object v0

    invoke-static {v8, v0}, Lcom/facebook/ads/redexgen/X/Ha;->A05(ZLjava/lang/Object;)V

    .line 25289
    const/4 v0, -0x1

    if-eq v6, v0, :cond_7

    const/4 v8, 0x1

    :goto_4
    const/16 v2, 0x280

    const/16 v1, 0x16

    const/16 v0, 0x76

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/CO;->A0I(III)Ljava/lang/String;

    move-result-object v0

    invoke-static {v8, v0}, Lcom/facebook/ads/redexgen/X/Ha;->A05(ZLjava/lang/Object;)V

    .line 25290
    invoke-static {p0, v6, v5, v4}, Lcom/facebook/ads/redexgen/X/CO;->A0D(Lcom/facebook/ads/redexgen/X/Hz;IILjava/lang/String;)Lcom/facebook/ads/redexgen/X/Cf;

    move-result-object v4

    .line 25291
    .local v7, "encryptionBox":Lcom/facebook/ads/redexgen/X/Cf;
    if-eqz v4, :cond_6

    :goto_5
    const/16 v2, 0x29e

    const/16 v1, 0x16

    const/16 v0, 0x31

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/CO;->A0I(III)Ljava/lang/String;

    move-result-object v0

    invoke-static {v7, v0}, Lcom/facebook/ads/redexgen/X/Ha;->A05(ZLjava/lang/Object;)V

    .line 25292
    invoke-static {v3, v4}, Landroid/util/Pair;->create(Ljava/lang/Object;Ljava/lang/Object;)Landroid/util/Pair;

    move-result-object v0

    return-object v0

    .line 25293
    :cond_6
    const/4 v7, 0x0

    goto :goto_5

    .line 25294
    :cond_7
    const/4 v8, 0x0

    goto :goto_4

    .line 25295
    :cond_8
    const/4 v8, 0x0

    goto :goto_3

    :cond_9
    sget-object v2, Lcom/facebook/ads/redexgen/X/CO;->A01:[Ljava/lang/String;

    const-string v1, "yPyTXv33PIzdo"

    const/4 v0, 0x1

    aput-object v1, v2, v0

    invoke-static {v9, v8, v7}, Lcom/facebook/ads/redexgen/X/CO;->A0I(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v0, v4}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_5

    goto/16 :goto_2

    :cond_a
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    .line 25296
    :cond_b
    const/4 v0, 0x0

    return-object v0
.end method

.method public static A09(Lcom/facebook/ads/redexgen/X/Hz;II)Landroid/util/Pair;
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/facebook/ads/redexgen/X/Hz;",
            "II)",
            "Landroid/util/Pair<",
            "Ljava/lang/Integer;",
            "Lcom/facebook/ads/redexgen/X/Cf;",
            ">;"
        }
    .end annotation

    .line 25297
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Hz;->A06()I

    move-result v5

    .line 25298
    .local v0, "childPosition":I
    :goto_0
    sub-int v3, v5, p1

    sget-object v1, Lcom/facebook/ads/redexgen/X/CO;->A01:[Ljava/lang/String;

    const/4 v0, 0x3

    aget-object v1, v1, v0

    const/16 v0, 0x12

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    const/16 v0, 0x79

    if-eq v1, v0, :cond_3

    sget-object v2, Lcom/facebook/ads/redexgen/X/CO;->A01:[Ljava/lang/String;

    const-string v1, "VuSqoU71mDFleviScFynwgGpV5GSmBmr"

    const/4 v0, 0x6

    aput-object v1, v2, v0

    if-ge v3, p2, :cond_2

    .line 25299
    invoke-virtual {p0, v5}, Lcom/facebook/ads/redexgen/X/Hz;->A0Y(I)V

    .line 25300
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Hz;->A08()I

    move-result v4

    .line 25301
    .local v1, "childAtomSize":I
    if-lez v4, :cond_1

    const/4 v3, 0x1

    :goto_1
    const/16 v2, 0x23e

    const/16 v1, 0x20

    const/16 v0, 0x57

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/CO;->A0I(III)Ljava/lang/String;

    move-result-object v0

    invoke-static {v3, v0}, Lcom/facebook/ads/redexgen/X/Ha;->A05(ZLjava/lang/Object;)V

    .line 25302
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Hz;->A08()I

    move-result v1

    .line 25303
    .local v2, "childAtomType":I
    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A14:I

    if-ne v1, v0, :cond_0

    .line 25304
    invoke-static {p0, v5, v4}, Lcom/facebook/ads/redexgen/X/CO;->A08(Lcom/facebook/ads/redexgen/X/Hz;II)Landroid/util/Pair;

    move-result-object v0

    .line 25305
    .local v3, "result":Landroid/util/Pair;, "Landroid/util/Pair<Ljava/lang/Integer;Lcom/facebook/ads/internal/exoplayer2/thirdparty/extractor/mp4/TrackEncryptionBox;>;"
    if-eqz v0, :cond_0

    .line 25306
    return-object v0

    .line 25307
    .end local v3    # "result":Landroid/util/Pair;, "Landroid/util/Pair<Ljava/lang/Integer;Lcom/facebook/ads/internal/exoplayer2/thirdparty/extractor/mp4/TrackEncryptionBox;>;"
    :cond_0
    add-int/2addr v5, v4

    .line 25308
    .end local v1    # "childAtomSize":I
    .end local v2    # "childAtomType":I
    goto :goto_0

    .line 25309
    :cond_1
    const/4 v3, 0x0

    goto :goto_1

    .line 25310
    :cond_2
    const/4 v0, 0x0

    return-object v0

    :cond_3
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0
.end method

.method public static A0A(Lcom/facebook/ads/redexgen/X/Hz;IILjava/lang/String;Lcom/facebook/ads/internal/exoplayer2/thirdparty/drm/DrmInitData;Z)Lcom/facebook/ads/redexgen/X/CM;
    .locals 23
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lcom/facebook/ads/redexgen/X/9v;
        }
    .end annotation

    .line 25311
    const/16 v0, 0xc

    move-object/from16 v4, p0

    invoke-virtual {v4, v0}, Lcom/facebook/ads/redexgen/X/Hz;->A0Y(I)V

    .line 25312
    invoke-virtual {v4}, Lcom/facebook/ads/redexgen/X/Hz;->A08()I

    move-result v3

    .line 25313
    .local v11, "numberOfEntries":I
    new-instance v13, Lcom/facebook/ads/redexgen/X/CM;

    invoke-direct {v13, v3}, Lcom/facebook/ads/redexgen/X/CM;-><init>(I)V

    .line 25314
    .local v12, "out":Lcom/facebook/ads/redexgen/X/CM;
    const/4 v14, 0x0

    .local v13, "i":I
    :goto_0
    if-ge v14, v3, :cond_c

    .line 25315
    invoke-virtual {v4}, Lcom/facebook/ads/redexgen/X/Hz;->A06()I

    move-result v7

    .line 25316
    .local v14, "childStartPosition":I
    invoke-virtual {v4}, Lcom/facebook/ads/redexgen/X/Hz;->A08()I

    move-result v8

    .line 25317
    .local v15, "childAtomSize":I
    if-lez v8, :cond_b

    const/4 v5, 0x1

    :goto_1
    const/16 v2, 0x23e

    const/16 v1, 0x20

    const/16 v0, 0x57

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/CO;->A0I(III)Ljava/lang/String;

    move-result-object v0

    invoke-static {v5, v0}, Lcom/facebook/ads/redexgen/X/Ha;->A05(ZLjava/lang/Object;)V

    .line 25318
    invoke-virtual {v4}, Lcom/facebook/ads/redexgen/X/Hz;->A08()I

    move-result v6

    .line 25319
    .local v9, "childAtomType":I
    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A07:I

    move/from16 v9, p1

    move-object/from16 v12, p4

    if-eq v6, v0, :cond_2

    sget v5, Lcom/facebook/ads/redexgen/X/CJ;->A08:I

    sget-object v1, Lcom/facebook/ads/redexgen/X/CO;->A01:[Ljava/lang/String;

    const/4 v0, 0x4

    aget-object v1, v1, v0

    const/16 v0, 0x18

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    const/16 v0, 0x69

    if-eq v1, v0, :cond_1

    :cond_0
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    :cond_1
    sget-object v2, Lcom/facebook/ads/redexgen/X/CO;->A01:[Ljava/lang/String;

    const-string v1, "XfWsLr5EskpQolouThrY0L7T3EnzbtXq"

    const/4 v0, 0x2

    aput-object v1, v2, v0

    if-eq v6, v5, :cond_2

    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A0S:I

    if-eq v6, v0, :cond_2

    sget v5, Lcom/facebook/ads/redexgen/X/CJ;->A0m:I

    sget-object v1, Lcom/facebook/ads/redexgen/X/CO;->A01:[Ljava/lang/String;

    const/4 v0, 0x7

    aget-object v0, v1, v0

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v1

    const/16 v0, 0xe

    if-eq v1, v0, :cond_0

    sget-object v2, Lcom/facebook/ads/redexgen/X/CO;->A01:[Ljava/lang/String;

    const-string v1, "z"

    const/4 v0, 0x0

    aput-object v1, v2, v0

    const-string v1, "6"

    const/4 v0, 0x5

    aput-object v1, v2, v0

    if-eq v6, v5, :cond_2

    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A0Y:I

    if-eq v6, v0, :cond_2

    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A0X:I

    if-eq v6, v0, :cond_2

    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A0t:I

    if-eq v6, v0, :cond_2

    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A1T:I

    if-eq v6, v0, :cond_2

    sget v5, Lcom/facebook/ads/redexgen/X/CJ;->A1U:I

    sget-object v1, Lcom/facebook/ads/redexgen/X/CO;->A01:[Ljava/lang/String;

    const/4 v0, 0x4

    aget-object v1, v1, v0

    const/16 v0, 0x18

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    const/16 v0, 0x69

    if-eq v1, v0, :cond_4

    if-ne v6, v5, :cond_5

    .line 25320
    .end local v9    # "childAtomType":I
    .restart local v16
    :cond_2
    :goto_2
    move-object v15, v4

    move/from16 v20, p2

    move/from16 v16, v6

    move/from16 v17, v7

    move/from16 v18, v8

    move/from16 v19, v9

    move-object/from16 v21, v12

    move-object/from16 v22, v13

    move/from16 p0, v14

    invoke-static/range {v15 .. v23}, Lcom/facebook/ads/redexgen/X/CO;->A0K(Lcom/facebook/ads/redexgen/X/Hz;IIIIILcom/facebook/ads/internal/exoplayer2/thirdparty/drm/DrmInitData;Lcom/facebook/ads/redexgen/X/CM;I)V

    .line 25321
    :cond_3
    :goto_3
    add-int/2addr v7, v8

    invoke-virtual {v4, v7}, Lcom/facebook/ads/redexgen/X/Hz;->A0Y(I)V

    .line 25322
    .end local v14    # "childStartPosition":I
    .end local v15    # "childAtomSize":I
    .end local v16
    add-int/lit8 v14, v14, 0x1

    goto/16 :goto_0

    :cond_4
    sget-object v2, Lcom/facebook/ads/redexgen/X/CO;->A01:[Ljava/lang/String;

    const-string v1, "2YqAUbM6XE3am7qEi8y8ENpTVaXzAM3G"

    const/4 v0, 0x6

    aput-object v1, v2, v0

    if-ne v6, v5, :cond_5

    goto :goto_2

    .line 25323
    :cond_5
    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A0l:I

    move-object/from16 v10, p3

    if-eq v6, v0, :cond_6

    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A0R:I

    if-eq v6, v0, :cond_6

    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A05:I

    if-eq v6, v0, :cond_6

    sget v5, Lcom/facebook/ads/redexgen/X/CJ;->A0N:I

    sget-object v1, Lcom/facebook/ads/redexgen/X/CO;->A01:[Ljava/lang/String;

    const/4 v0, 0x6

    aget-object v1, v1, v0

    const/16 v0, 0x12

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    const/16 v0, 0x79

    if-eq v1, v0, :cond_7

    sget-object v2, Lcom/facebook/ads/redexgen/X/CO;->A01:[Ljava/lang/String;

    const-string v1, "sstJHJE2SSSA0gl"

    const/4 v0, 0x7

    aput-object v1, v2, v0

    if-eq v6, v5, :cond_6

    :goto_4
    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A0J:I

    if-eq v6, v0, :cond_6

    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A0K:I

    if-eq v6, v0, :cond_6

    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A0L:I

    if-eq v6, v0, :cond_6

    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A0M:I

    if-eq v6, v0, :cond_6

    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A0w:I

    if-eq v6, v0, :cond_6

    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A0x:I

    if-eq v6, v0, :cond_6

    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A0b:I

    if-eq v6, v0, :cond_6

    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A15:I

    if-eq v6, v0, :cond_6

    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A04:I

    if-eq v6, v0, :cond_6

    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A06:I

    if-ne v6, v0, :cond_8

    .line 25324
    :cond_6
    move-object v5, v4

    .end local v9
    .local v16, "childAtomType":I
    move/from16 v11, p5

    invoke-static/range {v5 .. v14}, Lcom/facebook/ads/redexgen/X/CO;->A0M(Lcom/facebook/ads/redexgen/X/Hz;IIIILjava/lang/String;ZLcom/facebook/ads/internal/exoplayer2/thirdparty/drm/DrmInitData;Lcom/facebook/ads/redexgen/X/CM;I)V

    goto :goto_3

    :cond_7
    sget-object v2, Lcom/facebook/ads/redexgen/X/CO;->A01:[Ljava/lang/String;

    const-string v1, "NowU1j8TXSe0umpE5Ib758xlieALevMk"

    const/4 v0, 0x4

    aput-object v1, v2, v0

    if-eq v6, v5, :cond_6

    goto :goto_4

    .line 25325
    :cond_8
    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A03:I

    if-eq v6, v0, :cond_9

    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A1P:I

    if-eq v6, v0, :cond_9

    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A1X:I

    if-eq v6, v0, :cond_9

    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A19:I

    if-eq v6, v0, :cond_9

    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A0A:I

    if-ne v6, v0, :cond_a

    .line 25326
    :cond_9
    move-object v5, v4

    move v6, v6

    move v7, v7

    move v8, v8

    move v9, v9

    move-object v10, v10

    move-object v11, v13

    invoke-static/range {v5 .. v11}, Lcom/facebook/ads/redexgen/X/CO;->A0L(Lcom/facebook/ads/redexgen/X/Hz;IIIILjava/lang/String;Lcom/facebook/ads/redexgen/X/CM;)V

    goto/16 :goto_3

    .line 25327
    :cond_a
    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A0B:I

    if-ne v6, v0, :cond_3

    .line 25328
    invoke-static {v9}, Ljava/lang/Integer;->toString(I)Ljava/lang/String;

    move-result-object v5

    .line 25329
    const/16 v2, 0x13b

    const/16 v1, 0x1b

    const/16 v0, 0x2b

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/CO;->A0I(III)Ljava/lang/String;

    move-result-object v2

    const/4 v1, 0x0

    const/4 v0, -0x1

    invoke-static {v5, v2, v1, v0, v1}, Lcom/facebook/ads/internal/exoplayer2/thirdparty/Format;->A0B(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;ILcom/facebook/ads/internal/exoplayer2/thirdparty/drm/DrmInitData;)Lcom/facebook/ads/internal/exoplayer2/thirdparty/Format;

    move-result-object v0

    iput-object v0, v13, Lcom/facebook/ads/redexgen/X/CM;->A02:Lcom/facebook/ads/internal/exoplayer2/thirdparty/Format;

    goto/16 :goto_3

    .line 25330
    :cond_b
    const/4 v5, 0x0

    goto/16 :goto_1

    .line 25331
    .end local v13    # "i":I
    :cond_c
    return-object v13
.end method

.method public static A0B(Lcom/facebook/ads/redexgen/X/Hz;)Lcom/facebook/ads/redexgen/X/CN;
    .locals 10

    .line 25332
    const/16 v4, 0x8

    invoke-virtual {p0, v4}, Lcom/facebook/ads/redexgen/X/Hz;->A0Y(I)V

    .line 25333
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Hz;->A08()I

    move-result v0

    .line 25334
    .local v1, "fullAtom":I
    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/CJ;->A01(I)I

    move-result v9

    .line 25335
    .local v2, "version":I
    const/16 v7, 0x10

    if-nez v9, :cond_0

    const/16 v0, 0x8

    :goto_0
    invoke-virtual {p0, v0}, Lcom/facebook/ads/redexgen/X/Hz;->A0Z(I)V

    .line 25336
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Hz;->A08()I

    move-result v5

    .line 25337
    .local v4, "trackId":I
    const/4 v6, 0x4

    invoke-virtual {p0, v6}, Lcom/facebook/ads/redexgen/X/Hz;->A0Z(I)V

    .line 25338
    const/4 v8, 0x1

    sget-object v2, Lcom/facebook/ads/redexgen/X/CO;->A01:[Ljava/lang/String;

    const/4 v0, 0x0

    aget-object v1, v2, v0

    const/4 v0, 0x5

    aget-object v0, v2, v0

    invoke-virtual {v1}, Ljava/lang/String;->length()I

    move-result v1

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v0

    if-eq v1, v0, :cond_1

    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    .line 25339
    :cond_0
    const/16 v0, 0x10

    goto :goto_0

    .line 25340
    .local v6, "durationUnknown":Z
    :cond_1
    sget-object v2, Lcom/facebook/ads/redexgen/X/CO;->A01:[Ljava/lang/String;

    const-string v1, "r"

    const/4 v0, 0x0

    aput-object v1, v2, v0

    const-string v1, "s"

    const/4 v0, 0x5

    aput-object v1, v2, v0

    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Hz;->A06()I

    move-result v0

    .line 25341
    .local v7, "durationPosition":I
    if-nez v9, :cond_2

    const/4 v4, 0x4

    .line 25342
    .local v0, "durationByteCount":I
    :cond_2
    const/4 v3, 0x0

    .local v8, "i":I
    :goto_1
    if-ge v3, v4, :cond_3

    .line 25343
    iget-object v2, p0, Lcom/facebook/ads/redexgen/X/Hz;->A00:[B

    add-int v1, v0, v3

    aget-byte v2, v2, v1

    const/4 v1, -0x1

    if-eq v2, v1, :cond_a

    .line 25344
    const/4 v8, 0x0

    .line 25345
    .end local v8    # "i":I
    :cond_3
    if-eqz v8, :cond_8

    .line 25346
    invoke-virtual {p0, v4}, Lcom/facebook/ads/redexgen/X/Hz;->A0Z(I)V

    .line 25347
    const-wide v3, -0x7fffffffffffffffL    # -4.9E-324

    .line 25348
    .local v8, "duration":J
    :cond_4
    :goto_2
    invoke-virtual {p0, v7}, Lcom/facebook/ads/redexgen/X/Hz;->A0Z(I)V

    .line 25349
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Hz;->A08()I

    move-result v8

    .line 25350
    .local v3, "a00":I
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Hz;->A08()I

    move-result v7

    .line 25351
    .local p0, "a01":I
    invoke-virtual {p0, v6}, Lcom/facebook/ads/redexgen/X/Hz;->A0Z(I)V

    .line 25352
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Hz;->A08()I

    move-result v6

    .line 25353
    .local v5, "a10":I
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Hz;->A08()I

    move-result v2

    .line 25354
    .local p1, "a11":I
    const/high16 v1, 0x10000

    .line 25355
    .local p2, "fixedOne":I
    if-nez v8, :cond_5

    if-ne v7, v1, :cond_5

    neg-int v0, v1

    if-ne v6, v0, :cond_5

    if-nez v2, :cond_5

    .line 25356
    const/16 v1, 0x5a

    .line 25357
    .local p3, "rotationDegrees":I
    .restart local p3
    :goto_3
    new-instance v0, Lcom/facebook/ads/redexgen/X/CN;

    invoke-direct {v0, v5, v3, v4, v1}, Lcom/facebook/ads/redexgen/X/CN;-><init>(IJI)V

    return-object v0

    .line 25358
    .end local p3
    :cond_5
    if-nez v8, :cond_6

    neg-int v0, v1

    if-ne v7, v0, :cond_6

    if-ne v6, v1, :cond_6

    if-nez v2, :cond_6

    .line 25359
    const/16 v1, 0x10e

    .restart local p3
    goto :goto_3

    .line 25360
    .end local p3
    :cond_6
    neg-int v0, v1

    if-ne v8, v0, :cond_7

    if-nez v7, :cond_7

    if-nez v6, :cond_7

    neg-int v0, v1

    if-ne v2, v0, :cond_7

    .line 25361
    const/16 v1, 0xb4

    .restart local p3
    goto :goto_3

    .line 25362
    .end local p3
    :cond_7
    const/4 v1, 0x0

    goto :goto_3

    .line 25363
    .end local v8    # "duration":J
    :cond_8
    if-nez v9, :cond_9

    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Hz;->A0M()J

    move-result-wide v3

    .line 25364
    .restart local v8    # "duration":J
    :goto_4
    const-wide/16 v1, 0x0

    cmp-long v0, v3, v1

    if-nez v0, :cond_4

    .line 25365
    const-wide v3, -0x7fffffffffffffffL    # -4.9E-324

    goto :goto_2

    .line 25366
    :cond_9
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Hz;->A0N()J

    move-result-wide v3

    goto :goto_4

    .line 25367
    :cond_a
    add-int/lit8 v3, v3, 0x1

    goto :goto_1
.end method

.method public static A0C(Lcom/facebook/ads/redexgen/X/XT;Lcom/facebook/ads/redexgen/X/XS;JLcom/facebook/ads/internal/exoplayer2/thirdparty/drm/DrmInitData;ZZ)Lcom/facebook/ads/redexgen/X/Ce;
    .locals 18
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lcom/facebook/ads/redexgen/X/9v;
        }
    .end annotation

    .line 25368
    move-wide/from16 v15, p2

    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A0e:I

    move-object/from16 v1, p0

    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/XT;->A06(I)Lcom/facebook/ads/redexgen/X/XT;

    move-result-object v3

    .line 25369
    .local v1, "mdia":Lcom/facebook/ads/redexgen/X/XT;
    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A0W:I

    invoke-virtual {v3, v0}, Lcom/facebook/ads/redexgen/X/XT;->A07(I)Lcom/facebook/ads/redexgen/X/XS;

    move-result-object v0

    iget-object v0, v0, Lcom/facebook/ads/redexgen/X/XS;->A00:Lcom/facebook/ads/redexgen/X/Hz;

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/CO;->A02(Lcom/facebook/ads/redexgen/X/Hz;)I

    move-result v10

    .line 25370
    .local v2, "trackType":I
    const/4 v0, -0x1

    const/4 v8, 0x0

    if-ne v10, v0, :cond_0

    .line 25371
    return-object v8

    .line 25372
    :cond_0
    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A1K:I

    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/XT;->A07(I)Lcom/facebook/ads/redexgen/X/XS;

    move-result-object v0

    iget-object v0, v0, Lcom/facebook/ads/redexgen/X/XS;->A00:Lcom/facebook/ads/redexgen/X/Hz;

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/CO;->A0B(Lcom/facebook/ads/redexgen/X/Hz;)Lcom/facebook/ads/redexgen/X/CN;

    move-result-object v7

    .line 25373
    .local p0, "tkhdData":Lcom/facebook/ads/redexgen/X/CN;
    const-wide v5, -0x7fffffffffffffffL    # -4.9E-324

    cmp-long v0, v15, v5

    if-nez v0, :cond_1

    .line 25374
    invoke-static {v7}, Lcom/facebook/ads/redexgen/X/CN;->A02(Lcom/facebook/ads/redexgen/X/CN;)J

    move-result-wide v15

    .line 25375
    .end local p15
    .local v7, "duration":J
    .end local p15
    .local p1, "duration":J
    :cond_1
    move-object/from16 v0, p1

    iget-object v0, v0, Lcom/facebook/ads/redexgen/X/XS;->A00:Lcom/facebook/ads/redexgen/X/Hz;

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/CO;->A04(Lcom/facebook/ads/redexgen/X/Hz;)J

    move-result-wide v13

    sget-object v2, Lcom/facebook/ads/redexgen/X/CO;->A01:[Ljava/lang/String;

    const/4 v0, 0x6

    aget-object v2, v2, v0

    const/16 v0, 0x12

    invoke-virtual {v2, v0}, Ljava/lang/String;->charAt(I)C

    move-result v2

    const/16 v0, 0x79

    if-eq v2, v0, :cond_2

    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    .line 25376
    .local p3, "movieTimescale":J
    :cond_2
    sget-object v4, Lcom/facebook/ads/redexgen/X/CO;->A01:[Ljava/lang/String;

    const-string v2, "VRI"

    const/4 v0, 0x7

    aput-object v2, v4, v0

    cmp-long v0, v15, v5

    if-nez v0, :cond_5

    .line 25377
    const-wide v15, -0x7fffffffffffffffL    # -4.9E-324

    .line 25378
    .local v5, "durationUs":J
    .local p5, "durationUs":J
    :goto_0
    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A0i:I

    .line 25379
    invoke-virtual {v3, v0}, Lcom/facebook/ads/redexgen/X/XT;->A06(I)Lcom/facebook/ads/redexgen/X/XT;

    move-result-object v2

    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A17:I

    invoke-virtual {v2, v0}, Lcom/facebook/ads/redexgen/X/XT;->A06(I)Lcom/facebook/ads/redexgen/X/XT;

    move-result-object v4

    .line 25380
    .local v14, "stbl":Lcom/facebook/ads/redexgen/X/XT;
    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A0d:I

    invoke-virtual {v3, v0}, Lcom/facebook/ads/redexgen/X/XT;->A07(I)Lcom/facebook/ads/redexgen/X/XS;

    move-result-object v0

    iget-object v0, v0, Lcom/facebook/ads/redexgen/X/XS;->A00:Lcom/facebook/ads/redexgen/X/Hz;

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/CO;->A06(Lcom/facebook/ads/redexgen/X/Hz;)Landroid/util/Pair;

    move-result-object v2

    .line 25381
    .local v13, "mdhdData":Landroid/util/Pair;, "Landroid/util/Pair<Ljava/lang/Long;Ljava/lang/String;>;"
    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A1B:I

    .line 25382
    invoke-virtual {v4, v0}, Lcom/facebook/ads/redexgen/X/XT;->A07(I)Lcom/facebook/ads/redexgen/X/XS;

    move-result-object v0

    iget-object v3, v0, Lcom/facebook/ads/redexgen/X/XS;->A00:Lcom/facebook/ads/redexgen/X/Hz;

    .line 25383
    invoke-static {v7}, Lcom/facebook/ads/redexgen/X/CN;->A00(Lcom/facebook/ads/redexgen/X/CN;)I

    move-result p0

    .line 25384
    invoke-static {v7}, Lcom/facebook/ads/redexgen/X/CN;->A01(Lcom/facebook/ads/redexgen/X/CN;)I

    move-result p1

    iget-object v0, v2, Landroid/util/Pair;->second:Ljava/lang/Object;

    check-cast v0, Ljava/lang/String;

    .line 25385
    move-object/from16 p3, p4

    move/from16 p4, p6

    move-object/from16 v17, v3

    move-object/from16 p2, v0

    invoke-static/range {v17 .. v22}, Lcom/facebook/ads/redexgen/X/CO;->A0A(Lcom/facebook/ads/redexgen/X/Hz;IILjava/lang/String;Lcom/facebook/ads/internal/exoplayer2/thirdparty/drm/DrmInitData;Z)Lcom/facebook/ads/redexgen/X/CM;

    move-result-object v0

    .line 25386
    .local v12, "stsdData":Lcom/facebook/ads/redexgen/X/CM;
    const/4 v4, 0x0

    .line 25387
    .local v3, "editListDurations":[J
    const/4 v3, 0x0

    .line 25388
    .local v5, "editListMediaTimes":[J
    if-nez p5, :cond_3

    .line 25389
    sget v3, Lcom/facebook/ads/redexgen/X/CJ;->A0O:I

    invoke-virtual {v1, v3}, Lcom/facebook/ads/redexgen/X/XT;->A06(I)Lcom/facebook/ads/redexgen/X/XT;

    move-result-object v1

    invoke-static {v1}, Lcom/facebook/ads/redexgen/X/CO;->A05(Lcom/facebook/ads/redexgen/X/XT;)Landroid/util/Pair;

    move-result-object v1

    .line 25390
    .local v6, "edtsData":Landroid/util/Pair;, "Landroid/util/Pair<[J[J>;"
    iget-object v4, v1, Landroid/util/Pair;->first:Ljava/lang/Object;

    check-cast v4, [J

    .line 25391
    iget-object v3, v1, Landroid/util/Pair;->second:Ljava/lang/Object;

    check-cast v3, [J

    .line 25392
    .end local v3    # "editListDurations":[J
    .end local v5    # "editListMediaTimes":[J
    .local p7, "editListDurations":[J
    .local p8, "editListMediaTimes":[J
    :cond_3
    iget-object v1, v0, Lcom/facebook/ads/redexgen/X/CM;->A02:Lcom/facebook/ads/internal/exoplayer2/thirdparty/Format;

    if-nez v1, :cond_4

    :goto_1
    return-object v8

    .line 25393
    :cond_4
    new-instance v8, Lcom/facebook/ads/redexgen/X/Ce;

    .line 25394
    invoke-static {v7}, Lcom/facebook/ads/redexgen/X/CN;->A00(Lcom/facebook/ads/redexgen/X/CN;)I

    move-result v9

    iget-object v1, v2, Landroid/util/Pair;->first:Ljava/lang/Object;

    check-cast v1, Ljava/lang/Long;

    .line 25395
    invoke-virtual {v1}, Ljava/lang/Long;->longValue()J

    move-result-wide v11

    iget-object v5, v0, Lcom/facebook/ads/redexgen/X/CM;->A02:Lcom/facebook/ads/internal/exoplayer2/thirdparty/Format;

    iget v2, v0, Lcom/facebook/ads/redexgen/X/CM;->A01:I

    iget-object v1, v0, Lcom/facebook/ads/redexgen/X/CM;->A03:[Lcom/facebook/ads/redexgen/X/Cf;

    iget v0, v0, Lcom/facebook/ads/redexgen/X/CM;->A00:I

    .end local v12    # "stsdData":Lcom/facebook/ads/redexgen/X/CM;
    .local p12, "stsdData":Lcom/facebook/ads/redexgen/X/CM;
    .end local v13    # "mdhdData":Landroid/util/Pair;, "Landroid/util/Pair<Ljava/lang/Long;Ljava/lang/String;>;"
    .local p10, "mdhdData":Landroid/util/Pair;, "Landroid/util/Pair<Ljava/lang/Long;Ljava/lang/String;>;"
    .end local v14    # "stbl":Lcom/facebook/ads/redexgen/X/XT;
    .local p11, "stbl":Lcom/facebook/ads/redexgen/X/XT;
    move-object/from16 v17, v5

    move/from16 p0, v2

    move-object/from16 p1, v1

    move/from16 p2, v0

    move-object/from16 p3, v4

    move-object/from16 p4, v3

    invoke-direct/range {v8 .. v22}, Lcom/facebook/ads/redexgen/X/Ce;-><init>(IIJJJLcom/facebook/ads/internal/exoplayer2/thirdparty/Format;I[Lcom/facebook/ads/redexgen/X/Cf;I[J[J)V

    goto :goto_1

    .line 25396
    .end local v5
    :cond_5
    const-wide/32 v17, 0xf4240

    move-wide/from16 p1, v13

    invoke-static/range {v15 .. v20}, Lcom/facebook/ads/redexgen/X/IF;->A0F(JJJ)J

    move-result-wide v15

    goto/16 :goto_0
.end method

.method public static A0D(Lcom/facebook/ads/redexgen/X/Hz;IILjava/lang/String;)Lcom/facebook/ads/redexgen/X/Cf;
    .locals 12

    .line 25397
    add-int/lit8 v3, p1, 0x8

    .line 25398
    .local v1, "childPosition":I
    :goto_0
    sub-int v0, v3, p1

    if-ge v0, p2, :cond_5

    .line 25399
    invoke-virtual {p0, v3}, Lcom/facebook/ads/redexgen/X/Hz;->A0Y(I)V

    .line 25400
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Hz;->A08()I

    move-result v2

    .line 25401
    .local v2, "childAtomSize":I
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Hz;->A08()I

    move-result v1

    .line 25402
    .local v4, "childAtomType":I
    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A1H:I

    if-ne v1, v0, :cond_3

    .line 25403
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Hz;->A08()I

    move-result v0

    .line 25404
    .local v5, "fullAtom":I
    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/CJ;->A01(I)I

    move-result v0

    .line 25405
    .local v6, "version":I
    const/4 v5, 0x1

    invoke-virtual {p0, v5}, Lcom/facebook/ads/redexgen/X/Hz;->A0Z(I)V

    .line 25406
    const/4 v9, 0x0

    .line 25407
    .local v8, "defaultCryptByteBlock":I
    const/4 v10, 0x0

    .line 25408
    .local v9, "defaultSkipByteBlock":I
    if-nez v0, :cond_2

    .line 25409
    invoke-virtual {p0, v5}, Lcom/facebook/ads/redexgen/X/Hz;->A0Z(I)V

    .line 25410
    .end local v10
    :goto_1
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Hz;->A0E()I

    move-result v0

    const/4 v4, 0x0

    if-ne v0, v5, :cond_1

    .line 25411
    .local v7, "defaultIsProtected":Z
    :goto_2
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Hz;->A0E()I

    move-result v7

    .line 25412
    .local v10, "defaultPerSampleIvSize":I
    const/16 v0, 0x10

    new-array v8, v0, [B

    .line 25413
    .local p3, "defaultKeyId":[B
    array-length v0, v8

    invoke-virtual {p0, v8, v4, v0}, Lcom/facebook/ads/redexgen/X/Hz;->A0c([BII)V

    .line 25414
    const/4 v11, 0x0

    .line 25415
    .local p0, "constantIv":[B
    if-eqz v5, :cond_0

    if-nez v7, :cond_0

    .line 25416
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Hz;->A0E()I

    move-result v3

    sget-object v1, Lcom/facebook/ads/redexgen/X/CO;->A01:[Ljava/lang/String;

    const/4 v0, 0x3

    aget-object v1, v1, v0

    const/16 v0, 0x12

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    const/16 v0, 0x79

    if-eq v1, v0, :cond_4

    .line 25417
    .local p1, "constantIvSize":I
    sget-object v2, Lcom/facebook/ads/redexgen/X/CO;->A01:[Ljava/lang/String;

    const-string v1, "4wz2pNpdOED2I1F7uwyPx7ja9AIbtNj6"

    const/4 v0, 0x6

    aput-object v1, v2, v0

    new-array v11, v3, [B

    .line 25418
    invoke-virtual {p0, v11, v4, v3}, Lcom/facebook/ads/redexgen/X/Hz;->A0c([BII)V

    .line 25419
    .end local p0    # "constantIv":[B
    .local p7, "constantIv":[B
    :cond_0
    new-instance v4, Lcom/facebook/ads/redexgen/X/Cf;

    .end local p3    # "defaultKeyId":[B
    .local p9, "defaultKeyId":[B
    move-object v6, p3

    invoke-direct/range {v4 .. v11}, Lcom/facebook/ads/redexgen/X/Cf;-><init>(ZLjava/lang/String;I[BII[B)V

    return-object v4

    .line 25420
    :cond_1
    const/4 v5, 0x0

    goto :goto_2

    .line 25421
    :cond_2
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Hz;->A0E()I

    move-result v1

    .line 25422
    .local v10, "patternByte":I
    and-int/lit16 v0, v1, 0xf0

    shr-int/lit8 v9, v0, 0x4

    .line 25423
    and-int/lit8 v10, v1, 0xf

    goto :goto_1

    .line 25424
    .end local v5    # "fullAtom":I
    .end local v6    # "version":I
    .end local v7    # "defaultIsProtected":Z
    .end local v8    # "defaultCryptByteBlock":I
    .end local v9    # "defaultSkipByteBlock":I
    .end local v10    # "patternByte":I
    .end local p7
    .end local p9
    :cond_3
    add-int/2addr v3, v2

    .line 25425
    .end local v2    # "childAtomSize":I
    .end local v4    # "childAtomType":I
    goto :goto_0

    :cond_4
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    .line 25426
    :cond_5
    const/4 v0, 0x0

    return-object v0
.end method

.method public static A0E(Lcom/facebook/ads/redexgen/X/Ce;Lcom/facebook/ads/redexgen/X/XT;Lcom/facebook/ads/redexgen/X/Bw;)Lcom/facebook/ads/redexgen/X/Ch;
    .locals 35
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lcom/facebook/ads/redexgen/X/9v;
        }
    .end annotation

    .line 25427
    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A1D:I

    move-object/from16 v6, p1

    invoke-virtual {v6, v0}, Lcom/facebook/ads/redexgen/X/XT;->A07(I)Lcom/facebook/ads/redexgen/X/XS;

    move-result-object v1

    .line 25428
    .local v12, "stszAtom":Lcom/facebook/ads/redexgen/X/XS;
    if-eqz v1, :cond_0

    .line 25429
    new-instance v29, Lcom/facebook/ads/redexgen/X/XR;

    move-object/from16 v0, v29

    invoke-direct {v0, v1}, Lcom/facebook/ads/redexgen/X/XR;-><init>(Lcom/facebook/ads/redexgen/X/XS;)V

    .line 25430
    .local v0, "sampleSizeBox":Lcom/facebook/ads/redexgen/X/CL;
    .end local v0    # "sampleSizeBox":Lcom/facebook/ads/redexgen/X/CL;
    .local v13, "sampleSizeBox":Lcom/facebook/ads/redexgen/X/CL;
    :goto_0
    invoke-interface/range {v29 .. v29}, Lcom/facebook/ads/redexgen/X/CL;->A7o()I

    move-result v16

    .line 25431
    .local v14, "sampleCount":I
    const/4 v0, 0x0

    move-object/from16 v2, p0

    if-nez v16, :cond_1

    .line 25432
    new-instance v5, Lcom/facebook/ads/redexgen/X/Ch;

    new-array v4, v0, [J

    new-array v3, v0, [I

    const/4 v9, 0x0

    new-array v1, v0, [J

    new-array v0, v0, [I

    const-wide v12, -0x7fffffffffffffffL    # -4.9E-324

    move-object v6, v2

    move-object v7, v4

    move-object v8, v3

    move-object v10, v1

    move-object v11, v0

    invoke-direct/range {v5 .. v13}, Lcom/facebook/ads/redexgen/X/Ch;-><init>(Lcom/facebook/ads/redexgen/X/Ce;[J[II[J[IJ)V

    return-object v5

    .line 25433
    .end local v0
    :cond_0
    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A1F:I

    invoke-virtual {v6, v0}, Lcom/facebook/ads/redexgen/X/XT;->A07(I)Lcom/facebook/ads/redexgen/X/XS;

    move-result-object v1

    .line 25434
    .local v0, "stz2Atom":Lcom/facebook/ads/redexgen/X/XS;
    if-eqz v1, :cond_34

    .line 25435
    new-instance v29, Lcom/facebook/ads/redexgen/X/XQ;

    move-object/from16 v0, v29

    invoke-direct {v0, v1}, Lcom/facebook/ads/redexgen/X/XQ;-><init>(Lcom/facebook/ads/redexgen/X/XS;)V

    goto :goto_0

    .line 25436
    :cond_1
    const/4 v7, 0x0

    .line 25437
    .local v1, "chunkOffsetsAreLongs":Z
    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A18:I

    invoke-virtual {v6, v0}, Lcom/facebook/ads/redexgen/X/XT;->A07(I)Lcom/facebook/ads/redexgen/X/XS;

    move-result-object v0

    .line 25438
    .local v2, "chunkOffsetsAtom":Lcom/facebook/ads/redexgen/X/XS;
    if-nez v0, :cond_2

    .line 25439
    const/4 v7, 0x1

    .line 25440
    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A0C:I

    invoke-virtual {v6, v0}, Lcom/facebook/ads/redexgen/X/XT;->A07(I)Lcom/facebook/ads/redexgen/X/XS;

    move-result-object v0

    .line 25441
    .end local v1    # "chunkOffsetsAreLongs":Z
    .end local v2    # "chunkOffsetsAtom":Lcom/facebook/ads/redexgen/X/XS;
    .local v7, "chunkOffsetsAtom":Lcom/facebook/ads/redexgen/X/XS;
    .local v15, "chunkOffsetsAreLongs":Z
    :cond_2
    iget-object v5, v0, Lcom/facebook/ads/redexgen/X/XS;->A00:Lcom/facebook/ads/redexgen/X/Hz;

    .line 25442
    .local v8, "chunkOffsets":Lcom/facebook/ads/redexgen/X/Hz;
    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A1A:I

    invoke-virtual {v6, v0}, Lcom/facebook/ads/redexgen/X/XT;->A07(I)Lcom/facebook/ads/redexgen/X/XS;

    move-result-object v0

    iget-object v4, v0, Lcom/facebook/ads/redexgen/X/XS;->A00:Lcom/facebook/ads/redexgen/X/Hz;

    .line 25443
    .local v6, "stsc":Lcom/facebook/ads/redexgen/X/Hz;
    sget v8, Lcom/facebook/ads/redexgen/X/CJ;->A1E:I

    sget-object v1, Lcom/facebook/ads/redexgen/X/CO;->A01:[Ljava/lang/String;

    const/4 v0, 0x4

    aget-object v1, v1, v0

    const/16 v0, 0x18

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    const/16 v0, 0x69

    if-eq v1, v0, :cond_4

    :cond_3
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    :cond_4
    sget-object v3, Lcom/facebook/ads/redexgen/X/CO;->A01:[Ljava/lang/String;

    const-string v1, "VC45zkewNZzpqmaBl1H7tUMq9GmKccOG"

    const/4 v0, 0x3

    aput-object v1, v3, v0

    invoke-virtual {v6, v8}, Lcom/facebook/ads/redexgen/X/XT;->A07(I)Lcom/facebook/ads/redexgen/X/XS;

    move-result-object v0

    iget-object v0, v0, Lcom/facebook/ads/redexgen/X/XS;->A00:Lcom/facebook/ads/redexgen/X/Hz;

    move-object/from16 v30, v0

    .line 25444
    .local v5, "stts":Lcom/facebook/ads/redexgen/X/Hz;
    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A1C:I

    invoke-virtual {v6, v0}, Lcom/facebook/ads/redexgen/X/XT;->A07(I)Lcom/facebook/ads/redexgen/X/XS;

    move-result-object v0

    .line 25445
    .local v4, "stssAtom":Lcom/facebook/ads/redexgen/X/XS;
    const/4 v15, 0x0

    if-eqz v0, :cond_12

    iget-object v14, v0, Lcom/facebook/ads/redexgen/X/XS;->A00:Lcom/facebook/ads/redexgen/X/Hz;

    .line 25446
    .local v2, "stss":Lcom/facebook/ads/redexgen/X/Hz;
    :goto_1
    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A0D:I

    invoke-virtual {v6, v0}, Lcom/facebook/ads/redexgen/X/XT;->A07(I)Lcom/facebook/ads/redexgen/X/XS;

    move-result-object v0

    .line 25447
    .local v3, "cttsAtom":Lcom/facebook/ads/redexgen/X/XS;
    if-eqz v0, :cond_5

    iget-object v15, v0, Lcom/facebook/ads/redexgen/X/XS;->A00:Lcom/facebook/ads/redexgen/X/Hz;

    .line 25448
    .local v1, "ctts":Lcom/facebook/ads/redexgen/X/Hz;
    :cond_5
    new-instance v11, Lcom/facebook/ads/redexgen/X/CK;

    invoke-direct {v11, v4, v5, v7}, Lcom/facebook/ads/redexgen/X/CK;-><init>(Lcom/facebook/ads/redexgen/X/Hz;Lcom/facebook/ads/redexgen/X/Hz;Z)V

    .line 25449
    .local v0, "chunkIterator":Lcom/facebook/ads/redexgen/X/CK;
    .end local v3    # "cttsAtom":Lcom/facebook/ads/redexgen/X/XS;
    .local v17, "cttsAtom":Lcom/facebook/ads/redexgen/X/XS;
    const/16 v1, 0xc

    move-object/from16 v0, v30

    invoke-virtual {v0, v1}, Lcom/facebook/ads/redexgen/X/Hz;->A0Y(I)V

    .line 25450
    invoke-virtual/range {v30 .. v30}, Lcom/facebook/ads/redexgen/X/Hz;->A0H()I

    move-result v0

    add-int/lit8 v7, v0, -0x1

    .line 25451
    .local v18, "remainingTimestampDeltaChanges":I
    invoke-virtual/range {v30 .. v30}, Lcom/facebook/ads/redexgen/X/Hz;->A0H()I

    move-result v12

    .line 25452
    .local v20, "remainingSamplesAtTimestampDelta":I
    invoke-virtual/range {v30 .. v30}, Lcom/facebook/ads/redexgen/X/Hz;->A0H()I

    move-result v13

    .line 25453
    .local v3, "timestampDeltaInTimeUnits":I
    const/16 v28, 0x0

    .line 25454
    .local v22, "remainingSamplesAtTimestampOffset":I
    const/16 v27, 0x0

    .line 25455
    .local v23, "remainingTimestampOffsetChanges":I
    const/16 v26, 0x0

    .line 25456
    .local v24, "timestampOffset":I
    if-eqz v15, :cond_6

    .line 25457
    const/16 v0, 0xc

    .end local v4    # "stssAtom":Lcom/facebook/ads/redexgen/X/XS;
    .local v25, "stssAtom":Lcom/facebook/ads/redexgen/X/XS;
    invoke-virtual {v15, v0}, Lcom/facebook/ads/redexgen/X/Hz;->A0Y(I)V

    .line 25458
    invoke-virtual {v15}, Lcom/facebook/ads/redexgen/X/Hz;->A0H()I

    move-result v27

    .line 25459
    .end local v4
    .restart local v25    # "stssAtom":Lcom/facebook/ads/redexgen/X/XS;
    :cond_6
    const/16 v17, -0x1

    .line 25460
    .local v4, "nextSynchronizationSampleIndex":I
    const/4 v10, 0x0

    .line 25461
    .local v26, "remainingSynchronizationSamples":I
    if-eqz v14, :cond_7

    .line 25462
    const/16 v0, 0xc

    .end local v4    # "nextSynchronizationSampleIndex":I
    .local v27, "nextSynchronizationSampleIndex":I
    invoke-virtual {v14, v0}, Lcom/facebook/ads/redexgen/X/Hz;->A0Y(I)V

    .line 25463
    invoke-virtual {v14}, Lcom/facebook/ads/redexgen/X/Hz;->A0H()I

    move-result v10

    .line 25464
    if-lez v10, :cond_11

    .line 25465
    invoke-virtual {v14}, Lcom/facebook/ads/redexgen/X/Hz;->A0H()I

    move-result v0

    add-int/lit8 v17, v0, -0x1

    .line 25466
    .end local v27    # "nextSynchronizationSampleIndex":I
    .restart local v4    # "nextSynchronizationSampleIndex":I
    .end local v2    # "stss":Lcom/facebook/ads/redexgen/X/Hz;
    .end local v27
    .restart local v4    # "nextSynchronizationSampleIndex":I
    .local v19, "stss":Lcom/facebook/ads/redexgen/X/Hz;
    :cond_7
    :goto_2
    invoke-interface/range {v29 .. v29}, Lcom/facebook/ads/redexgen/X/CL;->A93()Z

    move-result v4

    sget-object v1, Lcom/facebook/ads/redexgen/X/CO;->A01:[Ljava/lang/String;

    const/4 v0, 0x7

    aget-object v0, v1, v0

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v1

    const/16 v0, 0xe

    if-eq v1, v0, :cond_f

    sget-object v3, Lcom/facebook/ads/redexgen/X/CO;->A01:[Ljava/lang/String;

    const-string v1, "hEJdTsd1BrdgtnhVHuIKNZxoiMcvZFQc"

    const/4 v0, 0x4

    aput-object v1, v3, v0

    if-eqz v4, :cond_10

    :goto_3
    iget-object v0, v2, Lcom/facebook/ads/redexgen/X/Ce;->A07:Lcom/facebook/ads/internal/exoplayer2/thirdparty/Format;

    iget-object v4, v0, Lcom/facebook/ads/internal/exoplayer2/thirdparty/Format;->A0O:Ljava/lang/String;

    .line 25467
    .end local v4    # "nextSynchronizationSampleIndex":I
    .restart local v27    # "nextSynchronizationSampleIndex":I
    const/16 v3, 0x1ec

    const/16 v1, 0x9

    const/16 v0, 0x49

    invoke-static {v3, v1, v0}, Lcom/facebook/ads/redexgen/X/CO;->A0I(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v0, v4}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_10

    if-nez v7, :cond_10

    if-nez v27, :cond_10

    if-nez v10, :cond_10

    const/4 v6, 0x1

    .line 25468
    .local v28, "isFixedSampleSizeRawAudio":Z
    :goto_4
    const/16 v18, 0x0

    .line 25469
    .local v2, "maximumSize":I
    const-wide/16 v0, 0x0

    .line 25470
    .local v29, "timestampTimeUnits":J
    const/16 v5, 0x80

    const/16 v4, 0xb

    const/4 v3, 0x7

    invoke-static {v5, v4, v3}, Lcom/facebook/ads/redexgen/X/CO;->A0I(III)Ljava/lang/String;

    move-result-object v21

    if-nez v6, :cond_18

    .line 25471
    .end local v2    # "maximumSize":I
    .local v31, "maximumSize":I
    move/from16 v3, v16

    new-array v3, v3, [J

    move-object/from16 v19, v3

    .line 25472
    .local v2, "offsets":[J
    .end local v6    # "stsc":Lcom/facebook/ads/redexgen/X/Hz;
    .local v32, "stsc":Lcom/facebook/ads/redexgen/X/Hz;
    move/from16 v3, v16

    new-array v3, v3, [I

    move-object/from16 v20, v3

    .line 25473
    .local v6, "sizes":[I
    .end local v7    # "chunkOffsetsAtom":Lcom/facebook/ads/redexgen/X/XS;
    .local v33, "chunkOffsetsAtom":Lcom/facebook/ads/redexgen/X/XS;
    move/from16 v3, v16

    new-array v3, v3, [J

    move-object/from16 v23, v3

    .line 25474
    .local v7, "timestamps":[J
    .end local v8    # "chunkOffsets":Lcom/facebook/ads/redexgen/X/Hz;
    .local v34, "chunkOffsets":Lcom/facebook/ads/redexgen/X/Hz;
    move/from16 v3, v16

    new-array v3, v3, [I

    move-object/from16 v22, v3

    .line 25475
    .local v8, "flags":[I
    const-wide/16 v5, 0x0

    .line 25476
    .local p0, "offset":J
    const/4 v9, 0x0

    .line 25477
    .local p2, "remainingSamplesInChunk":I
    const/4 v8, 0x0

    .end local v20    # "remainingSamplesAtTimestampDelta":I
    .end local v24    # "timestampOffset":I
    .end local v27    # "nextSynchronizationSampleIndex":I
    .end local v31    # "maximumSize":I
    .end local p2    # "remainingSamplesInChunk":I
    .local v3, "maximumSize":I
    .local v4, "i":I
    .local v9, "remainingSynchronizationSamples":I
    .local v10, "timestampDeltaInTimeUnits":I
    .local v11, "remainingSamplesAtTimestampDelta":I
    .local v12, "nextSynchronizationSampleIndex":I
    .local v15, "remainingTimestampDeltaChanges":I
    .local v18, "timestampOffset":I
    .local v26, "offset":J
    .local p0, "stszAtom":Lcom/facebook/ads/redexgen/X/XS;
    .local p1, "chunkOffsetsAreLongs":Z
    .local p4, "remainingSamplesInChunk":I
    :goto_5
    move/from16 v3, v16

    if-ge v8, v3, :cond_13

    .line 25478
    :goto_6
    if-nez v9, :cond_8

    .line 25479
    invoke-virtual {v11}, Lcom/facebook/ads/redexgen/X/CK;->A02()Z

    move-result v3

    invoke-static {v3}, Lcom/facebook/ads/redexgen/X/Ha;->A04(Z)V

    .line 25480
    sget-object v4, Lcom/facebook/ads/redexgen/X/CO;->A01:[Ljava/lang/String;

    const/4 v3, 0x2

    aget-object v4, v4, v3

    const/4 v3, 0x2

    invoke-virtual {v4, v3}, Ljava/lang/String;->charAt(I)C

    move-result v4

    const/16 v3, 0x74

    if-eq v4, v3, :cond_3

    sget-object v5, Lcom/facebook/ads/redexgen/X/CO;->A01:[Ljava/lang/String;

    const-string v4, "3XDIuarfTH54Gh2sDFB8u"

    const/4 v3, 0x7

    aput-object v4, v5, v3

    .end local v14    # "sampleCount":I
    .end local v15    # "remainingTimestampDeltaChanges":I
    .local v20, "remainingTimestampDeltaChanges":I
    .local p3, "sampleCount":I
    iget-wide v5, v11, Lcom/facebook/ads/redexgen/X/CK;->A02:J

    .line 25481
    .end local v26    # "offset":J
    .local v14, "offset":J
    .end local v14    # "offset":J
    .restart local v26    # "offset":J
    iget v9, v11, Lcom/facebook/ads/redexgen/X/CK;->A01:I

    .end local p4
    .local v14, "remainingSamplesInChunk":I
    goto :goto_6

    .line 25482
    .end local v20    # "remainingTimestampDeltaChanges":I
    .end local p3
    .local v14, "sampleCount":I
    .restart local v15    # "remainingTimestampDeltaChanges":I
    .restart local p4
    .end local v14    # "sampleCount":I
    .end local v15    # "remainingTimestampDeltaChanges":I
    .restart local v20    # "remainingTimestampDeltaChanges":I
    .restart local p3
    :cond_8
    if-eqz v15, :cond_a

    .line 25483
    :goto_7
    if-nez v28, :cond_9

    if-lez v27, :cond_9

    .line 25484
    invoke-virtual {v15}, Lcom/facebook/ads/redexgen/X/Hz;->A0H()I

    move-result v28

    .line 25485
    invoke-virtual {v15}, Lcom/facebook/ads/redexgen/X/Hz;->A08()I

    move-result v26

    .line 25486
    add-int/lit8 v27, v27, -0x1

    goto :goto_7

    .line 25487
    :cond_9
    add-int/lit8 v28, v28, -0x1

    .line 25488
    .end local v18    # "timestampOffset":I
    .local v14, "timestampOffset":I
    :cond_a
    aput-wide v5, v19, v8

    .line 25489
    invoke-interface/range {v29 .. v29}, Lcom/facebook/ads/redexgen/X/CL;->AEN()I

    move-result v3

    aput v3, v20, v8

    .line 25490
    aget v3, v20, v8

    move v4, v3

    move/from16 v3, v18

    if-le v4, v3, :cond_b

    .line 25491
    aget v18, v20, v8

    .line 25492
    .end local v2    # "offsets":[J
    .end local v3    # "maximumSize":I
    .local v15, "offsets":[J
    .local v18, "maximumSize":I
    :cond_b
    move/from16 v3, v26

    int-to-long v3, v3

    move-wide/from16 v24, v3

    add-long v3, v0, v24

    aput-wide v3, v23, v8

    .line 25493
    if-nez v14, :cond_e

    const/4 v3, 0x1

    :goto_8
    aput v3, v22, v8

    .line 25494
    move/from16 v3, v17

    if-ne v8, v3, :cond_c

    .line 25495
    const/4 v3, 0x1

    aput v3, v22, v8

    .line 25496
    add-int/lit8 v10, v10, -0x1

    .line 25497
    if-lez v10, :cond_c

    .line 25498
    invoke-virtual {v14}, Lcom/facebook/ads/redexgen/X/Hz;->A0H()I

    move-result v3

    add-int/lit8 v17, v3, -0x1

    .line 25499
    :cond_c
    int-to-long v3, v13

    move-wide/from16 v24, v3

    add-long v0, v0, v24

    .line 25500
    add-int/lit8 v12, v12, -0x1

    .line 25501
    if-nez v12, :cond_d

    if-lez v7, :cond_d

    .line 25502
    invoke-virtual/range {v30 .. v30}, Lcom/facebook/ads/redexgen/X/Hz;->A0H()I

    move-result v12

    .line 25503
    .end local v11    # "remainingSamplesAtTimestampDelta":I
    .local v2, "remainingSamplesAtTimestampDelta":I
    invoke-virtual/range {v30 .. v30}, Lcom/facebook/ads/redexgen/X/Hz;->A08()I

    move-result v13

    .line 25504
    add-int/lit8 v7, v7, -0x1

    .line 25505
    .end local v20    # "remainingTimestampDeltaChanges":I
    .local v3, "remainingTimestampDeltaChanges":I
    .end local v20
    .restart local v3    # "remainingTimestampDeltaChanges":I
    :cond_d
    aget v3, v20, v8

    .end local v3    # "remainingTimestampDeltaChanges":I
    .restart local v20    # "remainingTimestampDeltaChanges":I
    move v3, v3

    int-to-long v3, v3

    move-wide/from16 v24, v3

    add-long v5, v5, v24

    .line 25506
    add-int/lit8 v9, v9, -0x1

    .line 25507
    add-int/lit8 v8, v8, 0x1

    goto/16 :goto_5

    .line 25508
    :cond_e
    const/4 v3, 0x0

    goto :goto_8

    :cond_f
    sget-object v3, Lcom/facebook/ads/redexgen/X/CO;->A01:[Ljava/lang/String;

    const-string v1, "JmCEQgBO3I7S12cR5vZQ3WUXD6vvV57N"

    const/4 v0, 0x3

    aput-object v1, v3, v0

    if-eqz v4, :cond_10

    goto/16 :goto_3

    .line 25509
    .end local v4    # "i":I
    .restart local v27    # "nextSynchronizationSampleIndex":I
    :cond_10
    const/4 v6, 0x0

    goto/16 :goto_4

    .line 25510
    .end local v4
    .restart local v27    # "nextSynchronizationSampleIndex":I
    :cond_11
    const/4 v14, 0x0

    goto/16 :goto_2

    .line 25511
    :cond_12
    move-object v14, v15

    goto/16 :goto_1

    .line 25512
    .end local v20    # "remainingTimestampDeltaChanges":I
    .end local p3
    .local v2, "offsets":[J
    .local v3, "maximumSize":I
    .local v14, "sampleCount":I
    .local v15, "remainingTimestampDeltaChanges":I
    .local v18, "timestampOffset":I
    .end local v2    # "offsets":[J
    .end local v4
    .end local v14    # "sampleCount":I
    .local v15, "offsets":[J
    .restart local v20    # "remainingTimestampDeltaChanges":I
    .restart local p3
    .end local v3    # "maximumSize":I
    .end local v18    # "timestampOffset":I
    .local v4, "maximumSize":I
    .local v14, "timestampOffset":I
    :cond_13
    move/from16 v3, v26

    int-to-long v3, v3

    add-long/2addr v0, v3

    .line 25513
    .local v2, "duration":J
    if-nez v28, :cond_15

    const/4 v3, 0x1

    :goto_9
    invoke-static {v3}, Lcom/facebook/ads/redexgen/X/Ha;->A03(Z)V

    .line 25514
    :goto_a
    if-lez v27, :cond_16

    .line 25515
    invoke-virtual {v15}, Lcom/facebook/ads/redexgen/X/Hz;->A0H()I

    move-result v3

    if-nez v3, :cond_14

    const/4 v3, 0x1

    :goto_b
    invoke-static {v3}, Lcom/facebook/ads/redexgen/X/Ha;->A03(Z)V

    .line 25516
    invoke-virtual {v15}, Lcom/facebook/ads/redexgen/X/Hz;->A08()I

    .line 25517
    add-int/lit8 v27, v27, -0x1

    goto :goto_a

    .line 25518
    :cond_14
    const/4 v3, 0x0

    goto :goto_b

    .line 25519
    :cond_15
    const/4 v3, 0x0

    goto :goto_9

    .line 25520
    :cond_16
    if-nez v10, :cond_17

    if-nez v12, :cond_17

    .end local p4
    .local v1, "remainingSamplesInChunk":I
    .local p5, "ctts":Lcom/facebook/ads/redexgen/X/Hz;
    if-nez v9, :cond_17

    if-eqz v7, :cond_1a

    .line 25521
    .end local p4
    .local v1, "remainingSamplesInChunk":I
    .restart local p5
    .end local v2    # "duration":J
    .local p6, "duration":J
    :cond_17
    new-instance v6, Ljava/lang/StringBuilder;

    invoke-direct {v6}, Ljava/lang/StringBuilder;-><init>()V

    const/16 v5, 0xc6

    const/16 v4, 0x20

    const/16 v3, 0x42

    invoke-static {v5, v4, v3}, Lcom/facebook/ads/redexgen/X/CO;->A0I(III)Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v6, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v4

    .end local v4    # "maximumSize":I
    .end local v9    # "remainingSynchronizationSamples":I
    .local v3, "remainingSynchronizationSamples":I
    .local v18, "maximumSize":I
    iget v3, v2, Lcom/facebook/ads/redexgen/X/Ce;->A00:I

    invoke-virtual {v4, v3}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v6

    const/16 v5, 0x5e

    const/16 v4, 0x22

    const/16 v3, 0x1d

    invoke-static {v5, v4, v3}, Lcom/facebook/ads/redexgen/X/CO;->A0I(III)Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v6, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-virtual {v3, v10}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v6

    const/4 v5, 0x0

    const/16 v4, 0x23

    const/16 v3, 0x21

    invoke-static {v5, v4, v3}, Lcom/facebook/ads/redexgen/X/CO;->A0I(III)Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v6, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-virtual {v3, v12}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v6

    const/16 v5, 0x23

    const/16 v4, 0x1a

    const/16 v3, 0x1b

    invoke-static {v5, v4, v3}, Lcom/facebook/ads/redexgen/X/CO;->A0I(III)Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v6, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-virtual {v3, v9}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v6

    const/16 v5, 0x3d

    const/16 v4, 0x21

    const/16 v3, 0x65

    invoke-static {v5, v4, v3}, Lcom/facebook/ads/redexgen/X/CO;->A0I(III)Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v6, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    .end local v20    # "remainingTimestampDeltaChanges":I
    .local v4, "remainingTimestampDeltaChanges":I
    invoke-virtual {v3, v7}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v4

    .end local v1    # "remainingSamplesInChunk":I
    .local v20, "remainingSamplesInChunk":I
    move-object/from16 v3, v21

    invoke-static {v3, v4}, Landroid/util/Log;->w(Ljava/lang/String;Ljava/lang/String;)I

    goto :goto_d

    .line 25522
    .end local v4    # "remainingTimestampDeltaChanges":I
    .end local v10    # "timestampDeltaInTimeUnits":I
    .end local v11
    .end local v32    # "stsc":Lcom/facebook/ads/redexgen/X/Hz;
    .end local v33    # "chunkOffsetsAtom":Lcom/facebook/ads/redexgen/X/XS;
    .end local v34    # "chunkOffsets":Lcom/facebook/ads/redexgen/X/Hz;
    .end local p0    # "stszAtom":Lcom/facebook/ads/redexgen/X/XS;
    .end local p1    # "chunkOffsetsAreLongs":Z
    .end local p3
    .end local p5
    .end local p6
    .local v1, "ctts":Lcom/facebook/ads/redexgen/X/Hz;
    .local v2, "maximumSize":I
    .local v3, "timestampDeltaInTimeUnits":I
    .local v6, "stsc":Lcom/facebook/ads/redexgen/X/Hz;
    .local v7, "chunkOffsetsAtom":Lcom/facebook/ads/redexgen/X/XS;
    .local v8, "chunkOffsets":Lcom/facebook/ads/redexgen/X/Hz;
    .local v12, "stszAtom":Lcom/facebook/ads/redexgen/X/XS;
    .local v14, "sampleCount":I
    .local v15, "chunkOffsetsAreLongs":Z
    .local v18, "remainingTimestampDeltaChanges":I
    .local v20, "remainingSamplesAtTimestampDelta":I
    .restart local v24    # "timestampOffset":I
    .local v26, "remainingSynchronizationSamples":I
    .restart local v27    # "nextSynchronizationSampleIndex":I
    .end local v1    # "ctts":Lcom/facebook/ads/redexgen/X/Hz;
    .end local v2    # "maximumSize":I
    .end local v6    # "stsc":Lcom/facebook/ads/redexgen/X/Hz;
    .end local v7    # "chunkOffsetsAtom":Lcom/facebook/ads/redexgen/X/XS;
    .end local v8    # "chunkOffsets":Lcom/facebook/ads/redexgen/X/Hz;
    .end local v12    # "stszAtom":Lcom/facebook/ads/redexgen/X/XS;
    .end local v14    # "sampleCount":I
    .end local v15    # "chunkOffsetsAreLongs":Z
    .restart local v31    # "maximumSize":I
    .restart local v32    # "stsc":Lcom/facebook/ads/redexgen/X/Hz;
    .restart local v33    # "chunkOffsetsAtom":Lcom/facebook/ads/redexgen/X/XS;
    .restart local v34    # "chunkOffsets":Lcom/facebook/ads/redexgen/X/Hz;
    .restart local p0    # "stszAtom":Lcom/facebook/ads/redexgen/X/XS;
    .restart local p1    # "chunkOffsetsAreLongs":Z
    .restart local p3
    .restart local p5
    :cond_18
    iget v0, v11, Lcom/facebook/ads/redexgen/X/CK;->A05:I

    new-array v5, v0, [J

    .line 25523
    .local v2, "chunkOffsetsBytes":[J
    iget v0, v11, Lcom/facebook/ads/redexgen/X/CK;->A05:I

    new-array v4, v0, [I

    .line 25524
    .local v4, "chunkSampleCounts":[I
    :goto_c
    invoke-virtual {v11}, Lcom/facebook/ads/redexgen/X/CK;->A02()Z

    move-result v0

    if-eqz v0, :cond_19

    .line 25525
    iget v3, v11, Lcom/facebook/ads/redexgen/X/CK;->A00:I

    iget-wide v0, v11, Lcom/facebook/ads/redexgen/X/CK;->A02:J

    aput-wide v0, v5, v3

    .line 25526
    iget v1, v11, Lcom/facebook/ads/redexgen/X/CK;->A00:I

    iget v0, v11, Lcom/facebook/ads/redexgen/X/CK;->A01:I

    aput v0, v4, v1

    goto :goto_c

    .line 25527
    :cond_19
    iget-object v6, v2, Lcom/facebook/ads/redexgen/X/Ce;->A07:Lcom/facebook/ads/internal/exoplayer2/thirdparty/Format;

    sget-object v1, Lcom/facebook/ads/redexgen/X/CO;->A01:[Ljava/lang/String;

    const/4 v0, 0x6

    aget-object v1, v1, v0

    const/16 v0, 0x12

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    const/16 v0, 0x79

    if-eq v1, v0, :cond_1c

    iget v1, v6, Lcom/facebook/ads/internal/exoplayer2/thirdparty/Format;->A0A:I

    iget-object v0, v2, Lcom/facebook/ads/redexgen/X/Ce;->A07:Lcom/facebook/ads/internal/exoplayer2/thirdparty/Format;

    iget v0, v0, Lcom/facebook/ads/internal/exoplayer2/thirdparty/Format;->A05:I

    .line 25528
    invoke-static {v1, v0}, Lcom/facebook/ads/redexgen/X/IF;->A05(II)I

    move-result v3

    .line 25529
    .local v6, "fixedSampleSize":I
    int-to-long v0, v13

    .line 25530
    invoke-static {v3, v5, v4, v0, v1}, Lcom/facebook/ads/redexgen/X/CS;->A00(I[J[IJ)Lcom/facebook/ads/redexgen/X/CR;

    move-result-object v1

    .line 25531
    .local v7, "rechunkedResults":Lcom/facebook/ads/redexgen/X/CR;
    iget-object v0, v1, Lcom/facebook/ads/redexgen/X/CR;->A04:[J

    move-object/from16 v19, v0

    .line 25532
    .local v8, "offsets":[J
    iget-object v0, v1, Lcom/facebook/ads/redexgen/X/CR;->A03:[I

    move-object/from16 v20, v0

    .line 25533
    .local v10, "sizes":[I
    iget v0, v1, Lcom/facebook/ads/redexgen/X/CR;->A00:I

    move/from16 v18, v0

    .line 25534
    .end local v31    # "maximumSize":I
    .local v11, "maximumSize":I
    iget-object v0, v1, Lcom/facebook/ads/redexgen/X/CR;->A05:[J

    move-object/from16 v23, v0

    .line 25535
    .local v12, "timestamps":[J
    iget-object v0, v1, Lcom/facebook/ads/redexgen/X/CR;->A02:[I

    move-object/from16 v22, v0

    .line 25536
    .local v14, "flags":[I
    .end local v2    # "chunkOffsetsBytes":[J
    .end local v3    # "timestampDeltaInTimeUnits":I
    .local v15, "timestampDeltaInTimeUnits":I
    .local v31, "chunkOffsetsBytes":[J
    iget-wide v0, v1, Lcom/facebook/ads/redexgen/X/CR;->A01:J

    .line 25537
    .end local v4    # "chunkSampleCounts":[I
    .end local v6    # "fixedSampleSize":I
    .end local v11    # "maximumSize":I
    .end local v31    # "chunkOffsetsBytes":[J
    .local v7, "timestamps":[J
    .local v8, "flags":[I
    .local v10, "duration":J
    .local v12, "timestampDeltaInTimeUnits":I
    .local v14, "sizes":[I
    .local v15, "offsets":[J
    .local v18, "maximumSize":I
    .local v20, "remainingTimestampDeltaChanges":I
    .local v22, "remainingSamplesAtTimestampDelta":I
    .local v23, "remainingSamplesAtTimestampOffset":I
    .local v24, "remainingTimestampOffsetChanges":I
    .local v26, "timestampOffset":I
    .local v29, "remainingSynchronizationSamples":I
    .local v30, "timestampTimeUnits":J
    :cond_1a
    :goto_d
    const-wide/32 v7, 0xf4240

    iget-wide v3, v2, Lcom/facebook/ads/redexgen/X/Ce;->A06:J

    move-wide v5, v0

    move-wide v9, v3

    invoke-static/range {v5 .. v10}, Lcom/facebook/ads/redexgen/X/IF;->A0F(JJJ)J

    move-result-wide v14

    .line 25538
    .local p13, "durationUs":J
    iget-object v3, v2, Lcom/facebook/ads/redexgen/X/Ce;->A08:[J

    if-eqz v3, :cond_1b

    move-object/from16 v9, p2

    invoke-virtual {v9}, Lcom/facebook/ads/redexgen/X/Bw;->A03()Z

    move-result v3

    if-eqz v3, :cond_1d

    .line 25539
    .end local v0    # "chunkIterator":Lcom/facebook/ads/redexgen/X/CK;
    .end local v5    # "stts":Lcom/facebook/ads/redexgen/X/Hz;
    .end local v7    # "timestamps":[J
    .end local v8    # "flags":[I
    .end local v13    # "sampleSizeBox":Lcom/facebook/ads/redexgen/X/CL;
    .end local v14    # "sizes":[I
    .end local v15    # "offsets":[J
    .end local p3
    .local v10, "flags":[I
    .local v12, "timestamps":[J
    .restart local p2    # "remainingSamplesInChunk":I
    .restart local p4
    .restart local p6
    .restart local p7
    .restart local p11
    .restart local p12
    .restart local p29
    .restart local p31
    :cond_1b
    iget-wide v5, v2, Lcom/facebook/ads/redexgen/X/Ce;->A06:J

    const-wide/32 v3, 0xf4240

    move-object/from16 v0, v23

    invoke-static {v0, v3, v4, v5, v6}, Lcom/facebook/ads/redexgen/X/IF;->A0a([JJJ)V

    .line 25540
    new-instance v7, Lcom/facebook/ads/redexgen/X/Ch;

    move-object v8, v2

    move-object/from16 v9, v19

    move-object/from16 v10, v20

    move/from16 v11, v18

    move-object/from16 v12, v23

    move-object/from16 v13, v22

    invoke-direct/range {v7 .. v15}, Lcom/facebook/ads/redexgen/X/Ch;-><init>(Lcom/facebook/ads/redexgen/X/Ce;[J[II[J[IJ)V

    return-object v7

    .line 25541
    :cond_1c
    sget-object v3, Lcom/facebook/ads/redexgen/X/CO;->A01:[Ljava/lang/String;

    const-string v1, "5"

    const/4 v0, 0x0

    aput-object v1, v3, v0

    const-string v1, "P"

    const/4 v0, 0x5

    aput-object v1, v3, v0

    iget v1, v6, Lcom/facebook/ads/internal/exoplayer2/thirdparty/Format;->A0A:I

    iget-object v0, v2, Lcom/facebook/ads/redexgen/X/Ce;->A07:Lcom/facebook/ads/internal/exoplayer2/thirdparty/Format;

    iget v0, v0, Lcom/facebook/ads/internal/exoplayer2/thirdparty/Format;->A05:I

    .line 25542
    invoke-static {v1, v0}, Lcom/facebook/ads/redexgen/X/IF;->A05(II)I

    move-result v3

    .line 25543
    .local v6, "fixedSampleSize":I
    int-to-long v0, v13

    .line 25544
    invoke-static {v3, v5, v4, v0, v1}, Lcom/facebook/ads/redexgen/X/CS;->A00(I[J[IJ)Lcom/facebook/ads/redexgen/X/CR;

    move-result-object v1

    .line 25545
    .local v7, "rechunkedResults":Lcom/facebook/ads/redexgen/X/CR;
    iget-object v0, v1, Lcom/facebook/ads/redexgen/X/CR;->A04:[J

    move-object/from16 v19, v0

    .line 25546
    .local v8, "offsets":[J
    iget-object v0, v1, Lcom/facebook/ads/redexgen/X/CR;->A03:[I

    move-object/from16 v20, v0

    .line 25547
    .local v10, "sizes":[I
    iget v0, v1, Lcom/facebook/ads/redexgen/X/CR;->A00:I

    move/from16 v18, v0

    .line 25548
    .end local v31
    .local v11, "maximumSize":I
    iget-object v0, v1, Lcom/facebook/ads/redexgen/X/CR;->A05:[J

    move-object/from16 v23, v0

    .line 25549
    .local v12, "timestamps":[J
    iget-object v0, v1, Lcom/facebook/ads/redexgen/X/CR;->A02:[I

    move-object/from16 v22, v0

    .line 25550
    .local v14, "flags":[I
    .end local v2
    .end local v3
    .local v15, "timestampDeltaInTimeUnits":I
    .local v31, "chunkOffsetsBytes":[J
    iget-wide v0, v1, Lcom/facebook/ads/redexgen/X/CR;->A01:J

    goto :goto_d

    .line 25551
    :cond_1d
    iget-object v3, v2, Lcom/facebook/ads/redexgen/X/Ce;->A08:[J

    array-length v3, v3

    const-wide/16 v12, 0x0

    const/4 v4, 0x1

    if-ne v3, v4, :cond_1f

    iget v3, v2, Lcom/facebook/ads/redexgen/X/Ce;->A03:I

    if-ne v3, v4, :cond_1f

    move-object/from16 v3, v23

    array-length v4, v3

    const/4 v3, 0x2

    if-lt v4, v3, :cond_1f

    .line 25552
    iget-object v3, v2, Lcom/facebook/ads/redexgen/X/Ce;->A09:[J

    const/4 v4, 0x0

    aget-wide v26, v3, v4

    .line 25553
    .local p17, "editStartTime":J
    iget-object v3, v2, Lcom/facebook/ads/redexgen/X/Ce;->A08:[J

    aget-wide v3, v3, v4

    iget-wide v5, v2, Lcom/facebook/ads/redexgen/X/Ce;->A06:J

    .end local v5
    .local p2, "stts":Lcom/facebook/ads/redexgen/X/Hz;
    iget-wide v7, v2, Lcom/facebook/ads/redexgen/X/Ce;->A05:J

    .line 25554
    invoke-static/range {v3 .. v8}, Lcom/facebook/ads/redexgen/X/IF;->A0F(JJJ)J

    move-result-wide v3

    add-long v28, v26, v3

    .line 25555
    .local p21, "editEndTime":J
    move-object/from16 v23, v23

    move-wide/from16 v24, v0

    invoke-static/range {v23 .. v29}, Lcom/facebook/ads/redexgen/X/CO;->A0N([JJJJ)Z

    move-result v3

    if-eqz v3, :cond_1f

    .line 25556
    sub-long v24, v0, v28

    .line 25557
    .local p23, "paddingTimeUnits":J
    const/4 v3, 0x0

    aget-wide v3, v23, v3

    sub-long v26, v26, v3

    iget-object v3, v2, Lcom/facebook/ads/redexgen/X/Ce;->A07:Lcom/facebook/ads/internal/exoplayer2/thirdparty/Format;

    iget v3, v3, Lcom/facebook/ads/internal/exoplayer2/thirdparty/Format;->A0C:I

    int-to-long v5, v3

    iget-wide v3, v2, Lcom/facebook/ads/redexgen/X/Ce;->A06:J

    .line 25558
    move-wide/from16 v28, v5

    move-wide/from16 v30, v3

    invoke-static/range {v26 .. v31}, Lcom/facebook/ads/redexgen/X/IF;->A0F(JJJ)J

    move-result-wide v5

    .line 25559
    .local v5, "encoderDelay":J
    iget-object v3, v2, Lcom/facebook/ads/redexgen/X/Ce;->A07:Lcom/facebook/ads/internal/exoplayer2/thirdparty/Format;

    iget v3, v3, Lcom/facebook/ads/internal/exoplayer2/thirdparty/Format;->A0C:I

    int-to-long v7, v3

    .end local v12    # "timestamps":[J
    .end local v13
    .local p4, "sampleSizeBox":Lcom/facebook/ads/redexgen/X/CL;
    .local p12, "timestampDeltaInTimeUnits":I
    iget-wide v3, v2, Lcom/facebook/ads/redexgen/X/Ce;->A06:J

    .line 25560
    move-wide/from16 v26, v7

    move-wide/from16 v28, v3

    invoke-static/range {v24 .. v29}, Lcom/facebook/ads/redexgen/X/IF;->A0F(JJJ)J

    move-result-wide v3

    .line 25561
    .local v12, "encoderPadding":J
    cmp-long v7, v5, v12

    if-nez v7, :cond_1e

    cmp-long v7, v3, v12

    if-eqz v7, :cond_1f

    :cond_1e
    const-wide/32 v10, 0x7fffffff

    cmp-long v7, v5, v10

    if-gtz v7, :cond_1f

    cmp-long v7, v3, v10

    if-gtz v7, :cond_1f

    .line 25562
    long-to-int v0, v5

    iput v0, v9, Lcom/facebook/ads/redexgen/X/Bw;->A00:I

    .line 25563
    long-to-int v0, v3

    iput v0, v9, Lcom/facebook/ads/redexgen/X/Bw;->A01:I

    .line 25564
    iget-wide v5, v2, Lcom/facebook/ads/redexgen/X/Ce;->A06:J

    const-wide/32 v3, 0xf4240

    move-object/from16 v0, v23

    invoke-static {v0, v3, v4, v5, v6}, Lcom/facebook/ads/redexgen/X/IF;->A0a([JJJ)V

    .line 25565
    new-instance v7, Lcom/facebook/ads/redexgen/X/Ch;

    .end local v0
    .local p6, "chunkIterator":Lcom/facebook/ads/redexgen/X/CK;
    .end local v5    # "encoderDelay":J
    .local p7, "encoderDelay":J
    .end local v7    # "rechunkedResults":Lcom/facebook/ads/redexgen/X/CR;
    .end local v8    # "offsets":[J
    .local v12, "timestamps":[J
    .local p9, "flags":[I
    .local p10, "encoderPadding":J
    move-object v8, v2

    move-object/from16 v9, v19

    move-object/from16 v10, v20

    move/from16 v11, v18

    move-object/from16 v12, v23

    move-object/from16 v13, v22

    invoke-direct/range {v7 .. v15}, Lcom/facebook/ads/redexgen/X/Ch;-><init>(Lcom/facebook/ads/redexgen/X/Ce;[J[II[J[IJ)V

    return-object v7

    .line 25566
    .end local v0
    .end local v5
    .end local v7
    .end local v8
    .end local v13
    .local v12, "timestamps":[J
    .restart local p2    # "stts":Lcom/facebook/ads/redexgen/X/Hz;
    .restart local p4
    .restart local p6
    .restart local p9
    .restart local p12
    :cond_1f
    iget-object v3, v2, Lcom/facebook/ads/redexgen/X/Ce;->A08:[J

    array-length v4, v3

    const/4 v3, 0x1

    if-ne v4, v3, :cond_20

    iget-object v7, v2, Lcom/facebook/ads/redexgen/X/Ce;->A08:[J

    sget-object v4, Lcom/facebook/ads/redexgen/X/CO;->A01:[Ljava/lang/String;

    const/4 v3, 0x7

    aget-object v3, v4, v3

    invoke-virtual {v3}, Ljava/lang/String;->length()I

    move-result v4

    const/16 v3, 0xe

    if-eq v4, v3, :cond_31

    sget-object v5, Lcom/facebook/ads/redexgen/X/CO;->A01:[Ljava/lang/String;

    const-string v4, "2"

    const/4 v3, 0x0

    aput-object v4, v5, v3

    const-string v4, "c"

    const/4 v3, 0x5

    aput-object v4, v5, v3

    const/4 v6, 0x0

    aget-wide v4, v7, v6

    cmp-long v3, v4, v12

    if-nez v3, :cond_20

    .line 25567
    iget-object v3, v2, Lcom/facebook/ads/redexgen/X/Ce;->A09:[J

    aget-wide v6, v3, v6

    .line 25568
    .local p7, "editStartTime":J
    const/4 v5, 0x0

    .local v0, "i":I
    :goto_e
    move-object/from16 v3, v23

    array-length v3, v3

    if-ge v5, v3, :cond_30

    .line 25569
    aget-wide v8, v23, v5

    sub-long/2addr v8, v6

    const-wide/32 v10, 0xf4240

    iget-wide v12, v2, Lcom/facebook/ads/redexgen/X/Ce;->A06:J

    .line 25570
    invoke-static/range {v8 .. v13}, Lcom/facebook/ads/redexgen/X/IF;->A0F(JJJ)J

    move-result-wide v3

    aput-wide v3, v23, v5

    .line 25571
    add-int/lit8 v5, v5, 0x1

    goto :goto_e

    .line 25572
    .end local p7
    .end local p10
    .restart local p13
    :cond_20
    iget v1, v2, Lcom/facebook/ads/redexgen/X/Ce;->A03:I

    const/4 v0, 0x1

    if-ne v1, v0, :cond_23

    const/4 v8, 0x1

    .line 25573
    .local v13, "omitClippedSample":Z
    :goto_f
    const/4 v7, 0x0

    .line 25574
    .local v0, "editedSampleCount":I
    const/4 v10, 0x0

    .line 25575
    .local v2, "nextSampleIndex":I
    const/16 v17, 0x0

    .line 25576
    .local v3, "copyMetadata":Z
    const/4 v9, 0x0

    .end local v0    # "editedSampleCount":I
    .end local v2    # "nextSampleIndex":I
    .local v4, "i":I
    .local v7, "editedSampleCount":I
    .local v8, "nextSampleIndex":I
    :goto_10
    iget-object v0, v2, Lcom/facebook/ads/redexgen/X/Ce;->A08:[J

    array-length v0, v0

    const-wide/16 v5, -0x1

    if-ge v9, v0, :cond_24

    .line 25577
    iget-object v0, v2, Lcom/facebook/ads/redexgen/X/Ce;->A09:[J

    .end local v10    # "sizes":[I
    .local p7, "duration":J
    aget-wide v3, v0, v9

    .line 25578
    .local v10, "editMediaTime":J
    cmp-long v0, v3, v5

    if-eqz v0, :cond_21

    .line 25579
    iget-object v0, v2, Lcom/facebook/ads/redexgen/X/Ce;->A08:[J

    aget-wide v24, v0, v9

    iget-wide v5, v2, Lcom/facebook/ads/redexgen/X/Ce;->A06:J

    iget-wide v0, v2, Lcom/facebook/ads/redexgen/X/Ce;->A05:J

    .line 25580
    move-wide/from16 v26, v5

    move-wide/from16 v28, v0

    invoke-static/range {v24 .. v29}, Lcom/facebook/ads/redexgen/X/IF;->A0F(JJJ)J

    move-result-wide v11

    .line 25581
    .local v0, "editDuration":J
    const/4 v1, 0x1

    move-object/from16 v0, v23

    invoke-static {v0, v3, v4, v1, v1}, Lcom/facebook/ads/redexgen/X/IF;->A0A([JJZZ)I

    move-result v5

    .line 25582
    .local v6, "startIndex":I
    .end local v14    # "flags":[I
    .end local v15    # "timestampDeltaInTimeUnits":I
    .local p10, "offsets":[J
    .local p11, "sizes":[I
    add-long/2addr v3, v11

    .line 25583
    const/4 v1, 0x0

    move-object/from16 v0, v23

    invoke-static {v0, v3, v4, v8, v1}, Lcom/facebook/ads/redexgen/X/IF;->A0A([JJZZ)I

    move-result v1

    .line 25584
    .local v14, "endIndex":I
    sub-int v0, v1, v5

    add-int/2addr v7, v0

    .line 25585
    if-eq v10, v5, :cond_22

    const/4 v0, 0x1

    :goto_11
    or-int v17, v17, v0

    .line 25586
    move v10, v1

    .line 25587
    .end local v8    # "nextSampleIndex":I
    .local v5, "nextSampleIndex":I
    .end local v10    # "editMediaTime":J
    .end local v14    # "endIndex":I
    .end local v15
    .restart local p10
    .restart local p11
    :cond_21
    add-int/lit8 v9, v9, 0x1

    goto :goto_10

    .line 25588
    :cond_22
    const/4 v0, 0x0

    goto :goto_11

    .line 25589
    :cond_23
    const/4 v8, 0x0

    goto :goto_f

    .line 25590
    .end local p7
    .end local p10
    .end local p11
    .local v10, "duration":J
    .restart local v14    # "endIndex":I
    .restart local v15    # "timestampDeltaInTimeUnits":I
    :cond_24
    sget-object v1, Lcom/facebook/ads/redexgen/X/CO;->A01:[Ljava/lang/String;

    const/4 v0, 0x1

    aget-object v0, v1, v0

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v1

    const/16 v0, 0xc

    if-eq v1, v0, :cond_2e

    .line 25591
    .end local v4    # "i":I
    .end local v10    # "duration":J
    .end local v14    # "endIndex":I
    .end local v15    # "timestampDeltaInTimeUnits":I
    .restart local p7
    .restart local p10
    .restart local p11
    sget-object v3, Lcom/facebook/ads/redexgen/X/CO;->A01:[Ljava/lang/String;

    const-string v1, "kwnW70E81yVLGqt4h2ynIc7oXyulLjyz"

    const/4 v0, 0x6

    aput-object v1, v3, v0

    .end local p3
    .local v10, "sampleCount":I
    move/from16 v0, v16

    if-eq v7, v0, :cond_2f

    :goto_12
    const/4 v0, 0x1

    :goto_13
    or-int v17, v17, v0

    .line 25592
    .end local v3    # "copyMetadata":Z
    .local v11, "copyMetadata":Z
    if-eqz v17, :cond_2d

    new-array v14, v7, [J

    .line 25593
    .local v14, "editedOffsets":[J
    :goto_14
    if-eqz v17, :cond_2c

    new-array v12, v7, [I

    .line 25594
    .local v15, "editedSizes":[I
    :goto_15
    if-eqz v17, :cond_25

    const/16 v18, 0x0

    .line 25595
    .local v0, "editedMaximumSize":I
    :cond_25
    if-eqz v17, :cond_2b

    new-array v11, v7, [I

    .line 25596
    .local v4, "editedFlags":[I
    :goto_16
    new-array v13, v7, [J

    .line 25597
    .local v3, "editedTimestamps":[J
    const-wide/16 v30, 0x0

    .line 25598
    .local p15, "pts":J
    const/4 v1, 0x0

    .line 25599
    .local v1, "sampleIndex":I
    const/4 v7, 0x0

    .end local p15
    .local v0, "i":I
    .local p3, "editedMaximumSize":I
    .local p21, "pts":J
    :goto_17
    iget-object v0, v2, Lcom/facebook/ads/redexgen/X/Ce;->A08:[J

    array-length v0, v0

    if-ge v7, v0, :cond_33

    .line 25600
    iget-object v0, v2, Lcom/facebook/ads/redexgen/X/Ce;->A09:[J

    .end local v7    # "editedSampleCount":I
    .local p25, "editedSampleCount":I
    aget-wide v5, v0, v7

    .line 25601
    .local v6, "editMediaTime":J
    iget-object v0, v2, Lcom/facebook/ads/redexgen/X/Ce;->A08:[J

    aget-wide v24, v0, v7

    .line 25602
    .local p26, "editDuration":J
    const-wide/16 v3, -0x1

    cmp-long v0, v5, v3

    if-eqz v0, :cond_2a

    .line 25603
    .end local v3    # "editedTimestamps":[J
    .local p28, "editedTimestamps":[J
    iget-wide v9, v2, Lcom/facebook/ads/redexgen/X/Ce;->A06:J

    .end local v4    # "editedFlags":[I
    .local p29, "editedFlags":[I
    iget-wide v3, v2, Lcom/facebook/ads/redexgen/X/Ce;->A05:J

    .line 25604
    move-wide/from16 v26, v9

    move-wide/from16 v28, v3

    invoke-static/range {v24 .. v29}, Lcom/facebook/ads/redexgen/X/IF;->A0F(JJJ)J

    move-result-wide v3

    add-long/2addr v3, v5

    .line 25605
    .local v2, "endMediaTime":J
    const/4 v9, 0x1

    move-object/from16 v0, v23

    invoke-static {v0, v5, v6, v9, v9}, Lcom/facebook/ads/redexgen/X/IF;->A0A([JJZZ)I

    move-result v0

    .line 25606
    .local v5, "startIndex":I
    const/4 v10, 0x0

    .end local v8
    .local v16, "nextSampleIndex":I
    move-object/from16 v9, v23

    invoke-static {v9, v3, v4, v8, v10}, Lcom/facebook/ads/redexgen/X/IF;->A0A([JJZZ)I

    move-result v3

    .line 25607
    .local v8, "endIndex":I
    if-eqz v17, :cond_26

    .line 25608
    sub-int v9, v3, v0

    .line 25609
    .local v4, "count":I
    .end local p10
    .local v10, "offsets":[J
    .local p31, "sampleCount":I
    move-object/from16 v4, v19

    invoke-static {v4, v0, v14, v1, v9}, Ljava/lang/System;->arraycopy(Ljava/lang/Object;ILjava/lang/Object;II)V

    .line 25610
    .end local p11
    .local v13, "sizes":[I
    .local p10, "omitClippedSample":Z
    move-object/from16 v4, v20

    invoke-static {v4, v0, v12, v1, v9}, Ljava/lang/System;->arraycopy(Ljava/lang/Object;ILjava/lang/Object;II)V

    .line 25611
    .end local v2    # "endMediaTime":J
    .end local p9
    .end local p29
    .local v3, "editedFlags":[I
    .local v10, "flags":[I
    .local p11, "offsets":[J
    .local p32, "endMediaTime":J
    move-object/from16 v4, v22

    invoke-static {v4, v0, v11, v1, v9}, Ljava/lang/System;->arraycopy(Ljava/lang/Object;ILjava/lang/Object;II)V

    .line 25612
    .end local v2
    .end local p9
    .end local p29
    .restart local v3    # "editedFlags":[I
    .local v10, "flags":[I
    .local v13, "sizes":[I
    .local p10, "omitClippedSample":Z
    .local p11, "offsets":[J
    .restart local p31
    .restart local p32
    :cond_26
    if-ge v0, v3, :cond_27

    aget v9, v11, v1

    const/4 v4, 0x1

    and-int/2addr v9, v4

    if-eqz v9, :cond_32

    .line 25613
    .end local p3
    .local v2, "editedMaximumSize":I
    .local v4, "j":I
    :cond_27
    :goto_18
    if-ge v0, v3, :cond_29

    .line 25614
    const-wide/32 v32, 0xf4240

    .end local v13    # "sizes":[I
    .end local v14    # "editedOffsets":[J
    .local p9, "editedOffsets":[J
    .local p29, "sizes":[I
    iget-wide v9, v2, Lcom/facebook/ads/redexgen/X/Ce;->A05:J

    move-wide/from16 v34, v9

    invoke-static/range {v30 .. v35}, Lcom/facebook/ads/redexgen/X/IF;->A0F(JJJ)J

    move-result-wide v15

    .line 25615
    .local v13, "ptsUs":J
    aget-wide v32, v23, v0

    sub-long v32, v32, v5

    const-wide/32 v34, 0xf4240

    .end local v5    # "startIndex":I
    .end local v6    # "editMediaTime":J
    .local v7, "startIndex":I
    .local p15, "editMediaTime":J
    iget-wide v9, v2, Lcom/facebook/ads/redexgen/X/Ce;->A06:J

    .line 25616
    move-wide/from16 p1, v9

    invoke-static/range {v32 .. v37}, Lcom/facebook/ads/redexgen/X/IF;->A0F(JJJ)J

    move-result-wide v9

    .line 25617
    .local v5, "timeInSegmentUs":J
    add-long/2addr v15, v9

    aput-wide v15, v13, v1

    .line 25618
    if-eqz v17, :cond_28

    .end local v3    # "editedFlags":[I
    .local p34, "editedFlags":[I
    aget v9, v12, v1

    move/from16 v4, v18

    if-le v9, v4, :cond_28

    .line 25619
    aget v18, v20, v0

    .line 25620
    .end local v3
    .restart local p34
    .end local v5    # "timeInSegmentUs":J
    .end local v13    # "ptsUs":J
    :cond_28
    add-int/lit8 v1, v1, 0x1

    .line 25621
    add-int/lit8 v0, v0, 0x1

    goto :goto_18

    .end local v7    # "startIndex":I
    .end local p9
    .end local p15
    .end local p29
    .end local p34
    .restart local v3    # "editedFlags":[I
    .local v5, "startIndex":I
    .restart local v6    # "editMediaTime":J
    .local v13, "sizes":[I
    .restart local v14    # "editedOffsets":[J
    :cond_29
    sget-object v3, Lcom/facebook/ads/redexgen/X/CO;->A01:[Ljava/lang/String;

    const/4 v0, 0x7

    aget-object v0, v3, v0

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v3

    const/16 v0, 0xe

    if-eq v3, v0, :cond_3

    sget-object v4, Lcom/facebook/ads/redexgen/X/CO;->A01:[Ljava/lang/String;

    const-string v3, "ss"

    const/4 v0, 0x7

    aput-object v3, v4, v0

    .line 25622
    .end local v3    # "editedFlags":[I
    .end local v5    # "startIndex":I
    .end local v6    # "editMediaTime":J
    .end local v13    # "sizes":[I
    .end local v14    # "editedOffsets":[J
    .restart local v7    # "startIndex":I
    .restart local p9
    .restart local p15
    .restart local p29
    .restart local p34
    .end local v3
    .end local v4    # "j":I
    .end local v6
    .end local v8    # "endIndex":I
    .end local v13
    .end local v14
    .local v10, "flags":[I
    .restart local v16    # "nextSampleIndex":I
    .local p9, "editedOffsets":[J
    .local p10, "omitClippedSample":Z
    .local p11, "offsets":[J
    .restart local p15
    .restart local p28
    .restart local p29
    .restart local p31
    .restart local p34
    :cond_2a
    add-long v30, v30, v24

    .line 25623
    .end local p15
    .end local p26
    add-int/lit8 v7, v7, 0x1

    goto/16 :goto_17

    .line 25624
    :cond_2b
    move-object/from16 v11, v22

    goto/16 :goto_16

    .line 25625
    :cond_2c
    move-object/from16 v12, v20

    goto/16 :goto_15

    .line 25626
    :cond_2d
    move-object/from16 v14, v19

    goto/16 :goto_14

    .line 25627
    .end local v4
    .end local v10    # "flags":[I
    .end local v14
    .end local v15    # "editedSizes":[I
    .restart local p7
    .restart local p10
    .restart local p11
    :cond_2e
    sget-object v3, Lcom/facebook/ads/redexgen/X/CO;->A01:[Ljava/lang/String;

    const-string v1, "IBcqblQzdyIwYrCXVHJIvpyFiJoNYMYn"

    const/4 v0, 0x4

    aput-object v1, v3, v0

    .end local p3
    .local v10, "sampleCount":I
    move/from16 v0, v16

    if-eq v7, v0, :cond_2f

    goto/16 :goto_12

    :cond_2f
    const/4 v0, 0x0

    goto/16 :goto_13

    .line 25628
    .end local v0    # "i":I
    :cond_30
    sub-long/2addr v0, v6

    const-wide/32 v7, 0xf4240

    iget-wide v3, v2, Lcom/facebook/ads/redexgen/X/Ce;->A06:J

    .line 25629
    move-wide v5, v0

    move-wide v9, v3

    invoke-static/range {v5 .. v10}, Lcom/facebook/ads/redexgen/X/IF;->A0F(JJJ)J

    move-result-wide v7

    .line 25630
    .end local p13
    .local p10, "durationUs":J
    new-instance v0, Lcom/facebook/ads/redexgen/X/Ch;

    move-object v1, v2

    move-object/from16 v2, v19

    move-object/from16 v3, v20

    move/from16 v4, v18

    move-object/from16 v5, v23

    move-object/from16 v6, v22

    invoke-direct/range {v0 .. v8}, Lcom/facebook/ads/redexgen/X/Ch;-><init>(Lcom/facebook/ads/redexgen/X/Ce;[J[II[J[IJ)V

    return-object v0

    :cond_31
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    .line 25631
    :cond_32
    const/16 v2, 0x8b

    const/16 v1, 0x3b

    const/16 v0, 0x27

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/CO;->A0I(III)Ljava/lang/String;

    move-result-object v1

    move-object/from16 v0, v21

    invoke-static {v0, v1}, Landroid/util/Log;->w(Ljava/lang/String;Ljava/lang/String;)I

    .line 25632
    new-instance v0, Lcom/facebook/ads/redexgen/X/XP;

    invoke-direct {v0}, Lcom/facebook/ads/redexgen/X/XP;-><init>()V

    throw v0

    .line 25633
    .end local v16    # "nextSampleIndex":I
    .end local p25
    .end local p28
    .end local p29
    .end local p31
    .end local p34
    .restart local v3    # "editedFlags":[I
    .restart local v4    # "j":I
    .local v7, "editedSampleCount":I
    .restart local v8    # "endIndex":I
    .local v10, "sampleCount":I
    .restart local v13    # "sizes":[I
    .restart local v14    # "editedOffsets":[J
    .local p9, "flags":[I
    .local p10, "offsets":[J
    .local p11, "sizes":[I
    .end local v0
    .end local v3    # "editedFlags":[I
    .end local v4    # "j":I
    .end local v7    # "editedSampleCount":I
    .end local v8    # "endIndex":I
    .end local v13    # "sizes":[I
    .end local v14    # "editedOffsets":[J
    .local v10, "flags":[I
    .restart local v16    # "nextSampleIndex":I
    .local p9, "editedOffsets":[J
    .local p10, "omitClippedSample":Z
    .local p11, "offsets":[J
    .restart local p25
    .restart local p28
    .restart local p29
    .restart local p31
    .restart local p34
    :cond_33
    const-wide/32 v32, 0xf4240

    iget-wide v0, v2, Lcom/facebook/ads/redexgen/X/Ce;->A06:J

    move-wide/from16 v34, v0

    invoke-static/range {v30 .. v35}, Lcom/facebook/ads/redexgen/X/IF;->A0F(JJJ)J

    move-result-wide v7

    .line 25634
    .local v13, "editedDurationUs":J
    new-instance v0, Lcom/facebook/ads/redexgen/X/Ch;

    .end local v1    # "sampleIndex":I
    .local p15, "sampleIndex":I
    .end local p28
    .end local p34
    .restart local v4    # "j":I
    .local p16, "editedTimestamps":[J
    .end local v4    # "j":I
    .local p17, "editedFlags":[I
    .end local p25
    .local v16, "editedSampleCount":I
    .local p18, "nextSampleIndex":I
    move-object v1, v2

    move-object v2, v14

    move-object v3, v12

    move/from16 v4, v18

    move-object v5, v13

    move-object v6, v11

    invoke-direct/range {v0 .. v8}, Lcom/facebook/ads/redexgen/X/Ch;-><init>(Lcom/facebook/ads/redexgen/X/Ce;[J[II[J[IJ)V

    return-object v0

    .line 25635
    .end local v10    # "flags":[I
    .end local v17    # "cttsAtom":Lcom/facebook/ads/redexgen/X/XS;
    .end local v18    # "maximumSize":I
    .end local v19    # "stss":Lcom/facebook/ads/redexgen/X/Hz;
    .end local v20    # "remainingTimestampDeltaChanges":I
    .end local v22    # "remainingSamplesAtTimestampDelta":I
    .end local v23    # "remainingSamplesAtTimestampOffset":I
    .end local v24    # "remainingTimestampOffsetChanges":I
    .end local v25    # "stssAtom":Lcom/facebook/ads/redexgen/X/XS;
    .end local v26    # "timestampOffset":I
    .end local v27    # "nextSynchronizationSampleIndex":I
    .end local v28    # "isFixedSampleSizeRawAudio":Z
    .end local v29    # "remainingSynchronizationSamples":I
    .end local v30    # "timestampTimeUnits":J
    .end local v32    # "stsc":Lcom/facebook/ads/redexgen/X/Hz;
    .end local v33    # "chunkOffsetsAtom":Lcom/facebook/ads/redexgen/X/XS;
    .end local v34    # "chunkOffsets":Lcom/facebook/ads/redexgen/X/Hz;
    .end local p0    # "stszAtom":Lcom/facebook/ads/redexgen/X/XS;
    .end local p1    # "chunkOffsetsAreLongs":Z
    .end local p2    # "stts":Lcom/facebook/ads/redexgen/X/Hz;
    .end local p4
    .end local p5
    .end local p6
    .end local p7
    .end local p11
    .end local p12
    .end local p13
    .end local p29
    .end local p31
    .local v0, "stz2Atom":Lcom/facebook/ads/redexgen/X/XS;
    .local v12, "stszAtom":Lcom/facebook/ads/redexgen/X/XS;
    :cond_34
    const/16 v2, 0xe6

    const/16 v1, 0x2a

    const/16 v0, 0x60

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/CO;->A0I(III)Ljava/lang/String;

    move-result-object v1

    new-instance v0, Lcom/facebook/ads/redexgen/X/9v;

    invoke-direct {v0, v1}, Lcom/facebook/ads/redexgen/X/9v;-><init>(Ljava/lang/String;)V

    throw v0
.end method

.method public static A0F(Lcom/facebook/ads/redexgen/X/XS;Z)Lcom/facebook/ads/internal/exoplayer2/thirdparty/metadata/Metadata;
    .locals 7

    .line 25636
    const/4 v6, 0x0

    if-eqz p1, :cond_0

    .line 25637
    return-object v6

    .line 25638
    :cond_0
    iget-object v5, p0, Lcom/facebook/ads/redexgen/X/XS;->A00:Lcom/facebook/ads/redexgen/X/Hz;

    .line 25639
    .local v1, "udtaData":Lcom/facebook/ads/redexgen/X/Hz;
    const/16 v4, 0x8

    invoke-virtual {v5, v4}, Lcom/facebook/ads/redexgen/X/Hz;->A0Y(I)V

    .line 25640
    :goto_0
    invoke-virtual {v5}, Lcom/facebook/ads/redexgen/X/Hz;->A04()I

    move-result v0

    if-lt v0, v4, :cond_2

    .line 25641
    invoke-virtual {v5}, Lcom/facebook/ads/redexgen/X/Hz;->A06()I

    move-result v3

    .line 25642
    .local v3, "atomPosition":I
    invoke-virtual {v5}, Lcom/facebook/ads/redexgen/X/Hz;->A08()I

    move-result v2

    .line 25643
    .local v4, "atomSize":I
    invoke-virtual {v5}, Lcom/facebook/ads/redexgen/X/Hz;->A08()I

    move-result v1

    .line 25644
    .local v5, "atomType":I
    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A0h:I

    if-ne v1, v0, :cond_1

    .line 25645
    invoke-virtual {v5, v3}, Lcom/facebook/ads/redexgen/X/Hz;->A0Y(I)V

    .line 25646
    add-int/2addr v3, v2

    invoke-static {v5, v3}, Lcom/facebook/ads/redexgen/X/CO;->A0H(Lcom/facebook/ads/redexgen/X/Hz;I)Lcom/facebook/ads/internal/exoplayer2/thirdparty/metadata/Metadata;

    move-result-object v0

    return-object v0

    .line 25647
    :cond_1
    add-int/lit8 v0, v2, -0x8

    invoke-virtual {v5, v0}, Lcom/facebook/ads/redexgen/X/Hz;->A0Z(I)V

    .line 25648
    .end local v3    # "atomPosition":I
    .end local v4    # "atomSize":I
    .end local v5    # "atomType":I
    goto :goto_0

    .line 25649
    :cond_2
    return-object v6
.end method

.method public static A0G(Lcom/facebook/ads/redexgen/X/Hz;I)Lcom/facebook/ads/internal/exoplayer2/thirdparty/metadata/Metadata;
    .locals 2

    .line 25650
    const/16 v0, 0x8

    invoke-virtual {p0, v0}, Lcom/facebook/ads/redexgen/X/Hz;->A0Z(I)V

    .line 25651
    new-instance v1, Ljava/util/ArrayList;

    invoke-direct {v1}, Ljava/util/ArrayList;-><init>()V

    .line 25652
    .local v0, "entries":Ljava/util/ArrayList;, "Ljava/util/ArrayList<Lcom/facebook/ads/internal/exoplayer2/thirdparty/metadata/Metadata$Entry;>;"
    :cond_0
    :goto_0
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Hz;->A06()I

    move-result v0

    if-ge v0, p1, :cond_1

    .line 25653
    invoke-static {p0}, Lcom/facebook/ads/redexgen/X/CW;->A04(Lcom/facebook/ads/redexgen/X/Hz;)Lcom/facebook/ads/internal/exoplayer2/thirdparty/metadata/id3/Id3Frame;

    move-result-object v0

    .line 25654
    .local v1, "entry":Lcom/facebook/ads/internal/exoplayer2/thirdparty/metadata/Metadata$Entry;
    if-eqz v0, :cond_0

    .line 25655
    invoke-virtual {v1, v0}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    goto :goto_0

    .line 25656
    :cond_1
    invoke-virtual {v1}, Ljava/util/ArrayList;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_2

    const/4 v0, 0x0

    :goto_1
    return-object v0

    :cond_2
    new-instance v0, Lcom/facebook/ads/internal/exoplayer2/thirdparty/metadata/Metadata;

    invoke-direct {v0, v1}, Lcom/facebook/ads/internal/exoplayer2/thirdparty/metadata/Metadata;-><init>(Ljava/util/List;)V

    goto :goto_1
.end method

.method public static A0H(Lcom/facebook/ads/redexgen/X/Hz;I)Lcom/facebook/ads/internal/exoplayer2/thirdparty/metadata/Metadata;
    .locals 4

    .line 25657
    const/16 v0, 0xc

    invoke-virtual {p0, v0}, Lcom/facebook/ads/redexgen/X/Hz;->A0Z(I)V

    .line 25658
    :goto_0
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Hz;->A06()I

    move-result v3

    sget-object v1, Lcom/facebook/ads/redexgen/X/CO;->A01:[Ljava/lang/String;

    const/4 v0, 0x2

    aget-object v1, v1, v0

    const/4 v0, 0x2

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    const/16 v0, 0x74

    if-eq v1, v0, :cond_2

    sget-object v2, Lcom/facebook/ads/redexgen/X/CO;->A01:[Ljava/lang/String;

    const-string v1, "B"

    const/4 v0, 0x0

    aput-object v1, v2, v0

    const-string v1, "e"

    const/4 v0, 0x5

    aput-object v1, v2, v0

    if-ge v3, p1, :cond_1

    .line 25659
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Hz;->A06()I

    move-result v3

    .line 25660
    .local v0, "atomPosition":I
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Hz;->A08()I

    move-result v2

    .line 25661
    .local v1, "atomSize":I
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Hz;->A08()I

    move-result v1

    .line 25662
    .local v2, "atomType":I
    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A0a:I

    if-ne v1, v0, :cond_0

    .line 25663
    invoke-virtual {p0, v3}, Lcom/facebook/ads/redexgen/X/Hz;->A0Y(I)V

    .line 25664
    add-int/2addr v3, v2

    invoke-static {p0, v3}, Lcom/facebook/ads/redexgen/X/CO;->A0G(Lcom/facebook/ads/redexgen/X/Hz;I)Lcom/facebook/ads/internal/exoplayer2/thirdparty/metadata/Metadata;

    move-result-object v0

    return-object v0

    .line 25665
    :cond_0
    add-int/lit8 v0, v2, -0x8

    invoke-virtual {p0, v0}, Lcom/facebook/ads/redexgen/X/Hz;->A0Z(I)V

    .line 25666
    .end local v0    # "atomPosition":I
    .end local v1    # "atomSize":I
    .end local v2    # "atomType":I
    goto :goto_0

    .line 25667
    :cond_1
    const/4 v0, 0x0

    return-object v0

    :cond_2
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0
.end method

.method public static A0I(III)Ljava/lang/String;
    .locals 2

    sget-object v1, Lcom/facebook/ads/redexgen/X/CO;->A00:[B

    add-int v0, p0, p1

    invoke-static {v1, p0, v0}, Ljava/util/Arrays;->copyOfRange([BII)[B

    move-result-object p0

    const/4 v1, 0x0

    :goto_0
    array-length v0, p0

    if-ge v1, v0, :cond_0

    aget-byte v0, p0, v1

    sub-int/2addr v0, p2

    add-int/lit8 v0, v0, -0xf

    int-to-byte v0, v0

    aput-byte v0, p0, v1

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_0
    new-instance v0, Ljava/lang/String;

    invoke-direct {v0, p0}, Ljava/lang/String;-><init>([B)V

    return-object v0
.end method

.method public static A0J()V
    .locals 1

    const/16 v0, 0x2ff

    new-array v0, v0, [B

    fill-array-data v0, :array_0

    sput-object v0, Lcom/facebook/ads/redexgen/X/CO;->A00:[B

    return-void

    :array_0
    .array-data 1
        0x5ct
        0x50t
        -0x5et
        -0x6bt
        -0x63t
        -0x6ft
        -0x67t
        -0x62t
        -0x67t
        -0x62t
        -0x69t
        -0x7dt
        -0x6ft
        -0x63t
        -0x60t
        -0x64t
        -0x6bt
        -0x5dt
        0x71t
        -0x5ct
        -0x7ct
        -0x67t
        -0x63t
        -0x6bt
        -0x5dt
        -0x5ct
        -0x6ft
        -0x63t
        -0x60t
        0x74t
        -0x6bt
        -0x64t
        -0x5ct
        -0x6ft
        0x50t
        0x56t
        0x4at
        -0x64t
        -0x71t
        -0x69t
        -0x75t
        -0x6dt
        -0x68t
        -0x6dt
        -0x68t
        -0x6ft
        0x7dt
        -0x75t
        -0x69t
        -0x66t
        -0x6at
        -0x71t
        -0x63t
        0x73t
        -0x68t
        0x6dt
        -0x6et
        -0x61t
        -0x68t
        -0x6bt
        0x4at
        -0x60t
        -0x6ct
        -0x1at
        -0x27t
        -0x1ft
        -0x2bt
        -0x23t
        -0x1et
        -0x23t
        -0x1et
        -0x25t
        -0x38t
        -0x23t
        -0x1ft
        -0x27t
        -0x19t
        -0x18t
        -0x2bt
        -0x1ft
        -0x1ct
        -0x48t
        -0x27t
        -0x20t
        -0x18t
        -0x2bt
        -0x49t
        -0x24t
        -0x2bt
        -0x1et
        -0x25t
        -0x27t
        -0x19t
        -0x6ct
        0x66t
        0x4ct
        -0x62t
        -0x6ft
        -0x67t
        -0x73t
        -0x6bt
        -0x66t
        -0x6bt
        -0x66t
        -0x6dt
        0x7ft
        -0x5bt
        -0x66t
        -0x71t
        -0x6ct
        -0x62t
        -0x65t
        -0x66t
        -0x6bt
        -0x5at
        -0x73t
        -0x60t
        -0x6bt
        -0x65t
        -0x66t
        0x7ft
        -0x73t
        -0x67t
        -0x64t
        -0x68t
        -0x6ft
        -0x61t
        0x4ct
        0x57t
        -0x76t
        -0x7bt
        -0x7dt
        0x66t
        0x77t
        -0x78t
        -0x77t
        0x7bt
        -0x78t
        -0x77t
        0x7ft
        -0x63t
        -0x5ct
        -0x5bt
        -0x58t
        -0x61t
        -0x5ct
        -0x63t
        0x56t
        -0x65t
        -0x66t
        -0x61t
        -0x56t
        0x56t
        -0x5et
        -0x61t
        -0x57t
        -0x56t
        0x70t
        0x56t
        -0x65t
        -0x66t
        -0x61t
        -0x56t
        0x56t
        -0x66t
        -0x5bt
        -0x65t
        -0x57t
        0x56t
        -0x5ct
        -0x5bt
        -0x56t
        0x56t
        -0x57t
        -0x56t
        -0x69t
        -0x58t
        -0x56t
        0x56t
        -0x53t
        -0x61t
        -0x56t
        -0x62t
        0x56t
        -0x69t
        0x56t
        -0x57t
        -0x51t
        -0x5ct
        -0x67t
        0x56t
        -0x57t
        -0x69t
        -0x5dt
        -0x5at
        -0x5et
        -0x65t
        0x64t
        -0x66t
        -0x41t
        -0x4ct
        -0x40t
        -0x41t
        -0x3ct
        -0x46t
        -0x3ct
        -0x3bt
        -0x4at
        -0x41t
        -0x3bt
        0x71t
        -0x3ct
        -0x3bt
        -0x4dt
        -0x43t
        0x71t
        -0x4dt
        -0x40t
        -0x37t
        0x71t
        -0x49t
        -0x40t
        -0x3dt
        0x71t
        -0x3bt
        -0x3dt
        -0x4et
        -0x4ct
        -0x44t
        0x71t
        -0x3dt
        -0x1ft
        -0x30t
        -0x2et
        -0x26t
        -0x71t
        -0x29t
        -0x30t
        -0x1et
        -0x71t
        -0x23t
        -0x22t
        -0x71t
        -0x1et
        -0x30t
        -0x24t
        -0x21t
        -0x25t
        -0x2ct
        -0x71t
        -0x1dt
        -0x30t
        -0x2ft
        -0x25t
        -0x2ct
        -0x71t
        -0x1et
        -0x28t
        -0x17t
        -0x2ct
        -0x71t
        -0x28t
        -0x23t
        -0x2bt
        -0x22t
        -0x1ft
        -0x24t
        -0x30t
        -0x1dt
        -0x28t
        -0x22t
        -0x23t
        -0x40t
        -0x27t
        -0x22t
        -0x20t
        -0x25t
        -0x25t
        -0x26t
        -0x23t
        -0x21t
        -0x30t
        -0x31t
        -0x75t
        -0x28t
        -0x30t
        -0x31t
        -0x2ct
        -0x34t
        -0x75t
        -0x23t
        -0x34t
        -0x21t
        -0x30t
        -0x67t
        -0x7et
        -0x6ft
        -0x6ft
        -0x73t
        -0x76t
        -0x7ct
        -0x7et
        -0x6bt
        -0x76t
        -0x70t
        -0x71t
        0x50t
        -0x6bt
        -0x6bt
        -0x72t
        -0x73t
        0x4ct
        -0x67t
        -0x72t
        -0x73t
        -0x65t
        -0x56t
        -0x56t
        -0x5at
        -0x5dt
        -0x63t
        -0x65t
        -0x52t
        -0x5dt
        -0x57t
        -0x58t
        0x69t
        -0x4et
        0x67t
        -0x63t
        -0x65t
        -0x59t
        -0x61t
        -0x54t
        -0x65t
        0x67t
        -0x59t
        -0x57t
        -0x52t
        -0x5dt
        -0x57t
        -0x58t
        -0x58t
        -0x49t
        -0x49t
        -0x4dt
        -0x50t
        -0x56t
        -0x58t
        -0x45t
        -0x50t
        -0x4at
        -0x4bt
        0x76t
        -0x41t
        0x74t
        -0x4ct
        -0x49t
        0x7bt
        0x74t
        -0x56t
        -0x54t
        -0x58t
        0x74t
        0x7dt
        0x77t
        0x7ft
        0x79t
        -0x78t
        -0x78t
        -0x7ct
        -0x7ft
        0x7bt
        0x79t
        -0x74t
        -0x7ft
        -0x79t
        -0x7at
        0x47t
        -0x70t
        0x45t
        -0x7bt
        -0x78t
        0x4ct
        0x45t
        -0x72t
        -0x74t
        -0x74t
        -0x59t
        -0x4at
        -0x4at
        -0x4et
        -0x51t
        -0x57t
        -0x59t
        -0x46t
        -0x51t
        -0x4bt
        -0x4ct
        0x75t
        -0x42t
        0x73t
        -0x49t
        -0x45t
        -0x51t
        -0x57t
        -0x4ft
        -0x46t
        -0x51t
        -0x4dt
        -0x55t
        0x73t
        -0x46t
        -0x42t
        0x79t
        -0x53t
        -0x60t
        -0x4ct
        -0x5dt
        -0x58t
        -0x52t
        0x6et
        0x72t
        -0x5at
        -0x51t
        -0x51t
        0x7et
        -0x6et
        -0x7ft
        -0x7at
        -0x74t
        0x4ct
        0x7et
        -0x80t
        0x50t
        -0x27t
        -0x13t
        -0x24t
        -0x1ft
        -0x19t
        -0x59t
        -0x27t
        -0x1ct
        -0x27t
        -0x25t
        0x78t
        -0x74t
        0x7bt
        -0x80t
        -0x7at
        0x46t
        0x78t
        -0x7ct
        -0x77t
        0x44t
        -0x72t
        0x79t
        -0x1et
        -0xat
        -0x1bt
        -0x16t
        -0x10t
        -0x50t
        -0x1at
        -0x1et
        -0x1ct
        -0x4ct
        -0x7ft
        -0x6bt
        -0x7ct
        -0x77t
        -0x71t
        0x4ft
        -0x73t
        -0x70t
        0x54t
        -0x7ft
        0x4dt
        -0x74t
        -0x7ft
        -0x6ct
        -0x73t
        -0x31t
        -0x1dt
        -0x2et
        -0x29t
        -0x23t
        -0x63t
        -0x25t
        -0x22t
        -0x2dt
        -0x2bt
        -0x47t
        -0x33t
        -0x44t
        -0x3ft
        -0x39t
        -0x79t
        -0x36t
        -0x47t
        -0x31t
        -0x15t
        -0x1t
        -0x12t
        -0xdt
        -0x7t
        -0x47t
        0x0t
        -0x8t
        -0x12t
        -0x48t
        -0x12t
        -0x2t
        -0x3t
        -0x60t
        -0x4ct
        -0x5dt
        -0x58t
        -0x52t
        0x6et
        -0x4bt
        -0x53t
        -0x5dt
        0x6dt
        -0x5dt
        -0x4dt
        -0x4et
        0x6dt
        -0x59t
        -0x5dt
        -0x24t
        -0x10t
        -0x21t
        -0x1ct
        -0x16t
        -0x56t
        -0xft
        -0x17t
        -0x21t
        -0x57t
        -0x21t
        -0x11t
        -0x12t
        -0x57t
        -0x1dt
        -0x21t
        -0x4at
        -0x15t
        -0x13t
        -0x16t
        -0x1ft
        -0x1ct
        -0x19t
        -0x20t
        -0x48t
        -0x19t
        -0x23t
        -0x13t
        -0x45t
        -0x46t
        -0x45t
        -0x77t
        -0x11t
        -0x12t
        -0x11t
        -0x1t
        -0x41t
        -0x3ft
        -0x36t
        -0x41t
        -0x41t
        -0x3ft
        -0x36t
        -0x31t
        -0x37t
        -0x32t
        -0x31t
        -0x2et
        -0x36t
        -0x59t
        -0x26t
        -0x2bt
        -0x2dt
        -0x47t
        -0x31t
        -0x20t
        -0x35t
        -0x7at
        -0x27t
        -0x32t
        -0x2bt
        -0x25t
        -0x2et
        -0x36t
        -0x7at
        -0x38t
        -0x35t
        -0x7at
        -0x2at
        -0x2bt
        -0x27t
        -0x31t
        -0x26t
        -0x31t
        -0x24t
        -0x35t
        -0x7ft
        -0x76t
        -0x7ft
        -0x72t
        -0x7bt
        -0x6ft
        -0x74t
        -0x80t
        0x3ft
        -0x80t
        -0x6dt
        -0x72t
        -0x74t
        0x3ft
        -0x78t
        -0x6et
        0x3ft
        -0x74t
        -0x80t
        -0x73t
        -0x7dt
        -0x80t
        -0x6dt
        -0x72t
        -0x6ft
        -0x68t
        -0xct
        -0x14t
        -0x5t
        -0x18t
        -0x72t
        0x7dt
        -0x71t
        -0x79t
        -0x8t
        -0x18t
        -0x13t
        -0x12t
        -0x5bt
        -0x1at
        -0x7t
        -0xct
        -0xet
        -0x5bt
        -0x12t
        -0x8t
        -0x5bt
        -0xet
        -0x1at
        -0xdt
        -0x17t
        -0x1at
        -0x7t
        -0xct
        -0x9t
        -0x2t
        -0x8t
        -0xct
        -0x6t
        -0xdt
        -0x1ft
        -0x1dt
        -0x30t
        -0x1et
        -0x4ct
        -0x5bt
        -0x52t
        -0x5dt
        0x60t
        -0x5ft
        -0x4ct
        -0x51t
        -0x53t
        0x60t
        -0x57t
        -0x4dt
        0x60t
        -0x53t
        -0x5ft
        -0x52t
        -0x5ct
        -0x5ft
        -0x4ct
        -0x51t
        -0x4et
        -0x47t
        -0x3at
        -0x49t
        -0x36t
        -0x3at
        -0x4at
        -0x57t
        -0x5ct
        -0x5bt
        -0x3dt
        -0x4at
        -0x4ft
        -0x4et
        -0x44t
        0x7ct
        -0x80t
        -0x4ct
        -0x43t
        -0x43t
        -0x70t
        -0x7dt
        0x7et
        0x7ft
        -0x77t
        0x49t
        0x7bt
        -0x70t
        0x7dt
        -0x72t
        -0x7ft
        0x7ct
        0x7dt
        -0x79t
        0x47t
        -0x80t
        0x7dt
        -0x72t
        0x7bt
        -0x15t
        -0x22t
        -0x27t
        -0x26t
        -0x1ct
        -0x5ct
        -0x13t
        -0x5et
        -0x15t
        -0x1dt
        -0x27t
        -0x5dt
        -0x1ct
        -0x1dt
        -0x59t
        -0x5dt
        -0x15t
        -0x1bt
        -0x53t
        -0x5ft
        -0x6ct
        -0x71t
        -0x70t
        -0x66t
        0x5at
        -0x5dt
        0x58t
        -0x5ft
        -0x67t
        -0x71t
        0x59t
        -0x66t
        -0x67t
        0x5dt
        0x59t
        -0x5ft
        -0x65t
        0x64t
    .end array-data
.end method

.method public static A0K(Lcom/facebook/ads/redexgen/X/Hz;IIIIILcom/facebook/ads/internal/exoplayer2/thirdparty/drm/DrmInitData;Lcom/facebook/ads/redexgen/X/CM;I)V
    .locals 23
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lcom/facebook/ads/redexgen/X/9v;
        }
    .end annotation

    .line 25668
    move/from16 v9, p2

    move-object/from16 v12, p6

    move/from16 v14, p1

    add-int/lit8 v0, v9, 0x8

    add-int/lit8 v0, v0, 0x8

    move-object/from16 v10, p0

    invoke-virtual {v10, v0}, Lcom/facebook/ads/redexgen/X/Hz;->A0Y(I)V

    .line 25669
    const/16 v0, 0x10

    invoke-virtual {v10, v0}, Lcom/facebook/ads/redexgen/X/Hz;->A0Z(I)V

    .line 25670
    invoke-virtual {v10}, Lcom/facebook/ads/redexgen/X/Hz;->A0I()I

    move-result v19

    .line 25671
    .local v5, "width":I
    invoke-virtual {v10}, Lcom/facebook/ads/redexgen/X/Hz;->A0I()I

    move-result v20

    .line 25672
    .local v21, "height":I
    const/16 v18, 0x0

    .line 25673
    .local v6, "pixelWidthHeightRatioFromPasp":Z
    const/high16 v15, 0x3f800000    # 1.0f

    .line 25674
    .local v7, "pixelWidthHeightRatio":F
    const/16 v0, 0x32

    invoke-virtual {v10, v0}, Lcom/facebook/ads/redexgen/X/Hz;->A0Z(I)V

    .line 25675
    invoke-virtual {v10}, Lcom/facebook/ads/redexgen/X/Hz;->A06()I

    move-result v8

    .line 25676
    .local v8, "childPosition":I
    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A0S:I

    move/from16 v13, p3

    move-object/from16 v4, p7

    if-ne v14, v0, :cond_17

    .line 25677
    invoke-static {v10, v9, v13}, Lcom/facebook/ads/redexgen/X/CO;->A09(Lcom/facebook/ads/redexgen/X/Hz;II)Landroid/util/Pair;

    move-result-object v2

    .line 25678
    .local v9, "sampleEntryEncryptionData":Landroid/util/Pair;, "Landroid/util/Pair<Ljava/lang/Integer;Lcom/facebook/ads/internal/exoplayer2/thirdparty/extractor/mp4/TrackEncryptionBox;>;"
    if-eqz v2, :cond_0

    .line 25679
    iget-object v0, v2, Landroid/util/Pair;->first:Ljava/lang/Object;

    check-cast v0, Ljava/lang/Integer;

    invoke-virtual {v0}, Ljava/lang/Integer;->intValue()I

    move-result v14

    .line 25680
    .end local p8    # null:I
    .local v10, "atomType":I
    if-nez v12, :cond_16

    .line 25681
    const/4 v12, 0x0

    .line 25682
    .end local p13
    .local v3, "drmInitData":Lcom/facebook/ads/internal/exoplayer2/thirdparty/drm/DrmInitData;
    :goto_0
    iget-object v1, v4, Lcom/facebook/ads/redexgen/X/CM;->A03:[Lcom/facebook/ads/redexgen/X/Cf;

    iget-object v0, v2, Landroid/util/Pair;->second:Ljava/lang/Object;

    check-cast v0, Lcom/facebook/ads/redexgen/X/Cf;

    aput-object v0, v1, p8

    .line 25683
    :cond_0
    invoke-virtual {v10, v8}, Lcom/facebook/ads/redexgen/X/Hz;->A0Y(I)V

    .line 25684
    .end local p8
    .end local p13
    .local v3, "atomType":I
    .local v22, "drmInitData":Lcom/facebook/ads/internal/exoplayer2/thirdparty/drm/DrmInitData;
    :goto_1
    const/4 v5, 0x0

    .line 25685
    .local v9, "initializationData":Ljava/util/List;, "Ljava/util/List<[B>;"
    const/4 v0, 0x0

    .line 25686
    .local v10, "mimeType":Ljava/lang/String;
    const/16 p2, 0x0

    .line 25687
    .local v11, "projectionData":[B
    const/16 p3, -0x1

    sget-object v3, Lcom/facebook/ads/redexgen/X/CO;->A01:[Ljava/lang/String;

    const/4 v1, 0x0

    aget-object v2, v3, v1

    const/4 v1, 0x5

    aget-object v1, v3, v1

    invoke-virtual {v2}, Ljava/lang/String;->length()I

    move-result v2

    invoke-virtual {v1}, Ljava/lang/String;->length()I

    move-result v1

    if-eq v2, v1, :cond_2

    :cond_1
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    :cond_2
    sget-object v3, Lcom/facebook/ads/redexgen/X/CO;->A01:[Ljava/lang/String;

    const-string v2, "jNZq0IwPPShJWAAKA9azFLLmiQKjz3bf"

    const/4 v1, 0x4

    aput-object v2, v3, v1

    .line 25688
    .end local v6    # "pixelWidthHeightRatioFromPasp":Z
    .end local v7    # "pixelWidthHeightRatio":F
    .end local v8    # "childPosition":I
    .end local v9    # "initializationData":Ljava/util/List;, "Ljava/util/List<[B>;"
    .end local v10    # "mimeType":Ljava/lang/String;
    .end local v11    # "projectionData":[B
    .local v15, "childPosition":I
    .local p0, "pixelWidthHeightRatioFromPasp":Z
    .local p1, "pixelWidthHeightRatio":F
    .local p2, "initializationData":Ljava/util/List;, "Ljava/util/List<[B>;"
    .local p3, "mimeType":Ljava/lang/String;
    .local p4, "projectionData":[B
    .local p5, "stereoMode":I
    :goto_2
    sub-int v1, v8, v9

    if-ge v1, v13, :cond_3

    .line 25689
    invoke-virtual {v10, v8}, Lcom/facebook/ads/redexgen/X/Hz;->A0Y(I)V

    .line 25690
    invoke-virtual {v10}, Lcom/facebook/ads/redexgen/X/Hz;->A06()I

    move-result v11

    .line 25691
    .local v6, "childStartPosition":I
    invoke-virtual {v10}, Lcom/facebook/ads/redexgen/X/Hz;->A08()I

    move-result v7

    .line 25692
    .local v7, "childAtomSize":I
    if-nez v7, :cond_4

    invoke-virtual {v10}, Lcom/facebook/ads/redexgen/X/Hz;->A06()I

    move-result v1

    sub-int/2addr v1, v9

    if-ne v1, v13, :cond_4

    .line 25693
    :cond_3
    if-nez v0, :cond_18

    .line 25694
    return-void

    .line 25695
    :cond_4
    const/16 v17, 0x1

    if-lez v7, :cond_15

    const/4 v6, 0x1

    :goto_3
    const/16 v3, 0x23e

    const/16 v2, 0x20

    const/16 v1, 0x57

    invoke-static {v3, v2, v1}, Lcom/facebook/ads/redexgen/X/CO;->A0I(III)Ljava/lang/String;

    move-result-object v1

    invoke-static {v6, v1}, Lcom/facebook/ads/redexgen/X/Ha;->A05(ZLjava/lang/Object;)V

    .line 25696
    invoke-virtual {v10}, Lcom/facebook/ads/redexgen/X/Hz;->A08()I

    move-result v6

    .line 25697
    .local v10, "childAtomType":I
    sget v1, Lcom/facebook/ads/redexgen/X/CJ;->A09:I

    if-ne v6, v1, :cond_7

    .line 25698
    if-nez v0, :cond_6

    :goto_4
    invoke-static/range {v17 .. v17}, Lcom/facebook/ads/redexgen/X/Ha;->A04(Z)V

    .line 25699
    const/16 v2, 0x2c6

    const/16 v1, 0x9

    const/16 v0, 0xb

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/CO;->A0I(III)Ljava/lang/String;

    move-result-object v0

    .line 25700
    .end local p3    # "mimeType":Ljava/lang/String;
    .local v8, "mimeType":Ljava/lang/String;
    add-int/lit8 v1, v11, 0x8

    invoke-virtual {v10, v1}, Lcom/facebook/ads/redexgen/X/Hz;->A0Y(I)V

    .line 25701
    invoke-static {v10}, Lcom/facebook/ads/redexgen/X/IH;->A00(Lcom/facebook/ads/redexgen/X/Hz;)Lcom/facebook/ads/redexgen/X/IH;

    move-result-object v2

    .line 25702
    .local v9, "avcConfig":Lcom/facebook/ads/redexgen/X/IH;
    iget-object v5, v2, Lcom/facebook/ads/redexgen/X/IH;->A04:Ljava/util/List;

    .line 25703
    .end local p2    # "initializationData":Ljava/util/List;, "Ljava/util/List<[B>;"
    .local v11, "initializationData":Ljava/util/List;, "Ljava/util/List<[B>;"
    iget v1, v2, Lcom/facebook/ads/redexgen/X/IH;->A02:I

    iput v1, v4, Lcom/facebook/ads/redexgen/X/CM;->A00:I

    .line 25704
    if-nez v18, :cond_5

    .line 25705
    iget v15, v2, Lcom/facebook/ads/redexgen/X/IH;->A00:F

    .line 25706
    .end local v8    # "mimeType":Ljava/lang/String;
    .end local v9    # "avcConfig":Lcom/facebook/ads/redexgen/X/IH;
    .end local v11    # "initializationData":Ljava/util/List;, "Ljava/util/List<[B>;"
    .restart local p5    # "stereoMode":I
    :cond_5
    :goto_5
    add-int/2addr v8, v7

    .line 25707
    .end local v6    # "childStartPosition":I
    .end local v7    # "childAtomSize":I
    .end local v10    # "childAtomType":I
    goto :goto_2

    .line 25708
    :cond_6
    const/16 v17, 0x0

    goto :goto_4

    .line 25709
    .end local v8
    .end local v11
    .restart local p2    # "initializationData":Ljava/util/List;, "Ljava/util/List<[B>;"
    .restart local p3    # "mimeType":Ljava/lang/String;
    :cond_7
    sget v3, Lcom/facebook/ads/redexgen/X/CJ;->A0Z:I

    sget-object v2, Lcom/facebook/ads/redexgen/X/CO;->A01:[Ljava/lang/String;

    const/4 v1, 0x1

    aget-object v1, v2, v1

    invoke-virtual {v1}, Ljava/lang/String;->length()I

    move-result v2

    const/16 v1, 0xc

    if-eq v2, v1, :cond_1

    sget-object v16, Lcom/facebook/ads/redexgen/X/CO;->A01:[Ljava/lang/String;

    const-string v2, "trr"

    const/4 v1, 0x7

    aput-object v2, v16, v1

    if-ne v6, v3, :cond_9

    .line 25710
    if-nez v0, :cond_8

    :goto_6
    invoke-static/range {v17 .. v17}, Lcom/facebook/ads/redexgen/X/Ha;->A04(Z)V

    .line 25711
    const/16 v2, 0x2cf

    const/16 v1, 0xa

    const/16 v0, 0x9

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/CO;->A0I(III)Ljava/lang/String;

    move-result-object v0

    .line 25712
    .end local p3    # "mimeType":Ljava/lang/String;
    .restart local v8    # "mimeType":Ljava/lang/String;
    add-int/lit8 v1, v11, 0x8

    invoke-virtual {v10, v1}, Lcom/facebook/ads/redexgen/X/Hz;->A0Y(I)V

    .line 25713
    invoke-static {v10}, Lcom/facebook/ads/redexgen/X/IN;->A00(Lcom/facebook/ads/redexgen/X/Hz;)Lcom/facebook/ads/redexgen/X/IN;

    move-result-object v1

    .line 25714
    .local v9, "hevcConfig":Lcom/facebook/ads/redexgen/X/IN;
    iget-object v5, v1, Lcom/facebook/ads/redexgen/X/IN;->A01:Ljava/util/List;

    .line 25715
    .end local p2    # "initializationData":Ljava/util/List;, "Ljava/util/List<[B>;"
    .restart local v11    # "initializationData":Ljava/util/List;, "Ljava/util/List<[B>;"
    iget v1, v1, Lcom/facebook/ads/redexgen/X/IN;->A00:I

    iput v1, v4, Lcom/facebook/ads/redexgen/X/CM;->A00:I

    .line 25716
    .end local v9    # "hevcConfig":Lcom/facebook/ads/redexgen/X/IN;
    goto :goto_5

    .line 25717
    :cond_8
    const/16 v17, 0x0

    goto :goto_6

    .line 25718
    .end local v8    # "mimeType":Ljava/lang/String;
    .end local v11    # "initializationData":Ljava/util/List;, "Ljava/util/List<[B>;"
    .restart local p2    # "initializationData":Ljava/util/List;, "Ljava/util/List<[B>;"
    .restart local p3    # "mimeType":Ljava/lang/String;
    :cond_9
    sget v3, Lcom/facebook/ads/redexgen/X/CJ;->A1V:I

    sget-object v2, Lcom/facebook/ads/redexgen/X/CO;->A01:[Ljava/lang/String;

    const/4 v1, 0x4

    aget-object v2, v2, v1

    const/16 v1, 0x18

    invoke-virtual {v2, v1}, Ljava/lang/String;->charAt(I)C

    move-result v2

    const/16 v1, 0x69

    if-eq v2, v1, :cond_b

    sget-object v16, Lcom/facebook/ads/redexgen/X/CO;->A01:[Ljava/lang/String;

    const-string v2, "BNWfK92Afgdig3u125odHn9NiRsm91NE"

    const/4 v1, 0x4

    aput-object v2, v16, v1

    if-ne v6, v3, :cond_d

    .line 25719
    :goto_7
    if-nez v0, :cond_a

    :goto_8
    invoke-static/range {v17 .. v17}, Lcom/facebook/ads/redexgen/X/Ha;->A04(Z)V

    .line 25720
    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A1T:I

    if-ne v14, v0, :cond_c

    const/16 v2, 0x2d9

    const/16 v1, 0x13

    const/16 v0, 0x66

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/CO;->A0I(III)Ljava/lang/String;

    move-result-object v0

    goto :goto_5

    .line 25721
    :cond_a
    const/16 v17, 0x0

    goto :goto_8

    :cond_b
    sget-object v16, Lcom/facebook/ads/redexgen/X/CO;->A01:[Ljava/lang/String;

    const-string v2, "R"

    const/4 v1, 0x0

    aput-object v2, v16, v1

    const-string v2, "z"

    const/4 v1, 0x5

    aput-object v2, v16, v1

    if-ne v6, v3, :cond_d

    goto :goto_7

    .line 25722
    :cond_c
    const/16 v2, 0x2ec

    const/16 v1, 0x13

    const/16 v0, 0x1c

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/CO;->A0I(III)Ljava/lang/String;

    move-result-object v0

    goto/16 :goto_5

    .line 25723
    .end local v8
    .restart local p3    # "mimeType":Ljava/lang/String;
    :cond_d
    sget v1, Lcom/facebook/ads/redexgen/X/CJ;->A0E:I

    if-ne v6, v1, :cond_f

    .line 25724
    if-nez v0, :cond_e

    :goto_9
    invoke-static/range {v17 .. v17}, Lcom/facebook/ads/redexgen/X/Ha;->A04(Z)V

    .line 25725
    const/16 v2, 0x2bc

    const/16 v1, 0xa

    const/16 v0, 0x3e

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/CO;->A0I(III)Ljava/lang/String;

    move-result-object v0

    .end local p3    # "mimeType":Ljava/lang/String;
    .restart local v8    # "mimeType":Ljava/lang/String;
    goto/16 :goto_5

    .line 25726
    :cond_e
    const/16 v17, 0x0

    goto :goto_9

    .line 25727
    .end local v8    # "mimeType":Ljava/lang/String;
    .restart local p3    # "mimeType":Ljava/lang/String;
    :cond_f
    sget v1, Lcom/facebook/ads/redexgen/X/CJ;->A0T:I

    if-ne v6, v1, :cond_11

    .line 25728
    if-nez v0, :cond_10

    :goto_a
    invoke-static/range {v17 .. v17}, Lcom/facebook/ads/redexgen/X/Ha;->A04(Z)V

    .line 25729
    invoke-static {v10, v11}, Lcom/facebook/ads/redexgen/X/CO;->A07(Lcom/facebook/ads/redexgen/X/Hz;I)Landroid/util/Pair;

    move-result-object v1

    .line 25730
    .local v8, "mimeTypeAndInitializationData":Landroid/util/Pair;, "Landroid/util/Pair<Ljava/lang/String;[B>;"
    iget-object v0, v1, Landroid/util/Pair;->first:Ljava/lang/Object;

    check-cast v0, Ljava/lang/String;

    .line 25731
    .end local p3    # "mimeType":Ljava/lang/String;
    .local v9, "mimeType":Ljava/lang/String;
    iget-object v1, v1, Landroid/util/Pair;->second:Ljava/lang/Object;

    check-cast v1, [B

    invoke-static {v1}, Ljava/util/Collections;->singletonList(Ljava/lang/Object;)Ljava/util/List;

    move-result-object v5

    .line 25732
    .end local p2    # "initializationData":Ljava/util/List;, "Ljava/util/List<[B>;"
    .local v8, "initializationData":Ljava/util/List;, "Ljava/util/List<[B>;"
    goto/16 :goto_5

    .line 25733
    :cond_10
    const/16 v17, 0x0

    goto :goto_a

    .line 25734
    .end local v8    # "initializationData":Ljava/util/List;, "Ljava/util/List<[B>;"
    .end local v9    # "mimeType":Ljava/lang/String;
    .restart local p2    # "initializationData":Ljava/util/List;, "Ljava/util/List<[B>;"
    .restart local p3    # "mimeType":Ljava/lang/String;
    :cond_11
    sget v1, Lcom/facebook/ads/redexgen/X/CJ;->A0q:I

    if-ne v6, v1, :cond_12

    .line 25735
    invoke-static {v10, v11}, Lcom/facebook/ads/redexgen/X/CO;->A00(Lcom/facebook/ads/redexgen/X/Hz;I)F

    move-result v15

    .line 25736
    .end local p1    # "pixelWidthHeightRatio":F
    .local v8, "pixelWidthHeightRatio":F
    const/16 v18, 0x1

    .end local p0    # "pixelWidthHeightRatioFromPasp":Z
    .local v9, "pixelWidthHeightRatioFromPasp":Z
    goto/16 :goto_5

    .line 25737
    .end local v8    # "pixelWidthHeightRatio":F
    .end local v9    # "pixelWidthHeightRatioFromPasp":Z
    .restart local p0    # "pixelWidthHeightRatioFromPasp":Z
    .restart local p1    # "pixelWidthHeightRatio":F
    :cond_12
    sget v1, Lcom/facebook/ads/redexgen/X/CJ;->A1G:I

    if-ne v6, v1, :cond_13

    .line 25738
    invoke-static {v10, v11, v7}, Lcom/facebook/ads/redexgen/X/CO;->A0O(Lcom/facebook/ads/redexgen/X/Hz;II)[B

    move-result-object p2

    .end local p4    # "projectionData":[B
    .local v8, "projectionData":[B
    goto/16 :goto_5

    .line 25739
    .end local v8    # "projectionData":[B
    .restart local p4    # "projectionData":[B
    :cond_13
    sget v1, Lcom/facebook/ads/redexgen/X/CJ;->A16:I

    if-ne v6, v1, :cond_5

    .line 25740
    invoke-virtual {v10}, Lcom/facebook/ads/redexgen/X/Hz;->A0E()I

    move-result v2

    .line 25741
    .local v8, "version":I
    const/4 v1, 0x3

    invoke-virtual {v10, v1}, Lcom/facebook/ads/redexgen/X/Hz;->A0Z(I)V

    .line 25742
    if-nez v2, :cond_5

    .line 25743
    invoke-virtual {v10}, Lcom/facebook/ads/redexgen/X/Hz;->A0E()I

    move-result v1

    .line 25744
    .local v9, "layout":I
    packed-switch v1, :pswitch_data_0

    goto/16 :goto_5

    .line 25745
    :pswitch_0
    const/16 p3, 0x3

    sget-object v2, Lcom/facebook/ads/redexgen/X/CO;->A01:[Ljava/lang/String;

    const/4 v1, 0x1

    aget-object v1, v2, v1

    invoke-virtual {v1}, Ljava/lang/String;->length()I

    move-result v2

    const/16 v1, 0xc

    if-eq v2, v1, :cond_14

    .line 25746
    .end local p5    # "stereoMode":I
    .local v11, "stereoMode":I
    sget-object v3, Lcom/facebook/ads/redexgen/X/CO;->A01:[Ljava/lang/String;

    const-string v2, "ncqjdIo8Q7C9TEhGWLQwCcGeWNY5S9h6"

    const/4 v1, 0x2

    aput-object v2, v3, v1

    goto/16 :goto_5

    .end local p5
    .local v11, "stereoMode":I
    :cond_14
    sget-object v3, Lcom/facebook/ads/redexgen/X/CO;->A01:[Ljava/lang/String;

    const-string v2, "dnuTGkt9wQcTbo6J7tdoKURT12LZQ83u"

    const/4 v1, 0x2

    aput-object v2, v3, v1

    goto/16 :goto_5

    .line 25747
    .end local v11    # "stereoMode":I
    .restart local p5    # "stereoMode":I
    :pswitch_1
    const/16 p3, 0x2

    .line 25748
    .end local p5    # "stereoMode":I
    .restart local v11    # "stereoMode":I
    goto/16 :goto_5

    .line 25749
    .end local v11    # "stereoMode":I
    .restart local p5    # "stereoMode":I
    :pswitch_2
    const/16 p3, 0x1

    .line 25750
    .end local p5    # "stereoMode":I
    .restart local v11    # "stereoMode":I
    goto/16 :goto_5

    .line 25751
    .end local v11    # "stereoMode":I
    .restart local p5    # "stereoMode":I
    :pswitch_3
    const/16 p3, 0x0

    .line 25752
    .end local p5    # "stereoMode":I
    .restart local v11    # "stereoMode":I
    goto/16 :goto_5

    .line 25753
    :cond_15
    const/4 v6, 0x0

    goto/16 :goto_3

    .line 25754
    :cond_16
    iget-object v0, v2, Landroid/util/Pair;->second:Ljava/lang/Object;

    check-cast v0, Lcom/facebook/ads/redexgen/X/Cf;

    iget-object v0, v0, Lcom/facebook/ads/redexgen/X/Cf;->A02:Ljava/lang/String;

    invoke-virtual {v12, v0}, Lcom/facebook/ads/internal/exoplayer2/thirdparty/drm/DrmInitData;->A02(Ljava/lang/String;)Lcom/facebook/ads/internal/exoplayer2/thirdparty/drm/DrmInitData;

    move-result-object v12

    goto/16 :goto_0

    .line 25755
    .end local v3    # "atomType":I
    .end local v9    # "layout":I
    .end local v10
    .restart local p8    # null:I
    .restart local p13
    :cond_17
    sget-object v1, Lcom/facebook/ads/redexgen/X/CO;->A01:[Ljava/lang/String;

    const/4 v0, 0x7

    aget-object v0, v1, v0

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v1

    const/16 v0, 0xe

    if-eq v1, v0, :cond_1

    sget-object v2, Lcom/facebook/ads/redexgen/X/CO;->A01:[Ljava/lang/String;

    const-string v1, "PeJuctV1gZmOLx5oH2gSZJGQBpKTFEo9"

    const/4 v0, 0x2

    aput-object v1, v2, v0

    goto/16 :goto_1

    .line 25756
    :cond_18
    invoke-static/range {p4 .. p4}, Ljava/lang/Integer;->toString(I)Ljava/lang/String;

    move-result-object v14

    const/16 v16, 0x0

    const/16 v17, -0x1

    const/16 v18, -0x1

    const/high16 v21, -0x40800000    # -1.0f

    const/16 p4, 0x0

    .line 25757
    .end local v15    # "childPosition":I
    .local p6, "childPosition":I
    move/from16 p0, p5

    move-object/from16 v22, v5

    move/from16 p1, v15

    move-object/from16 p5, v12

    move-object v15, v0

    invoke-static/range {v14 .. v28}, Lcom/facebook/ads/internal/exoplayer2/thirdparty/Format;->A04(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;IIIIFLjava/util/List;IF[BILcom/facebook/ads/internal/exoplayer2/thirdparty/video/ColorInfo;Lcom/facebook/ads/internal/exoplayer2/thirdparty/drm/DrmInitData;)Lcom/facebook/ads/internal/exoplayer2/thirdparty/Format;

    move-result-object v0

    iput-object v0, v4, Lcom/facebook/ads/redexgen/X/CM;->A02:Lcom/facebook/ads/internal/exoplayer2/thirdparty/Format;

    .line 25758
    return-void

    nop

    :pswitch_data_0
    .packed-switch 0x0
        :pswitch_3
        :pswitch_2
        :pswitch_1
        :pswitch_0
    .end packed-switch
.end method

.method public static A0L(Lcom/facebook/ads/redexgen/X/Hz;IIIILjava/lang/String;Lcom/facebook/ads/redexgen/X/CM;)V
    .locals 14
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lcom/facebook/ads/redexgen/X/9v;
        }
    .end annotation

    .line 25759
    add-int/lit8 v0, p2, 0x8

    add-int/lit8 v0, v0, 0x8

    invoke-virtual {p0, v0}, Lcom/facebook/ads/redexgen/X/Hz;->A0Y(I)V

    .line 25760
    const/4 v13, 0x0

    .line 25761
    .local v3, "initializationData":Ljava/util/List;, "Ljava/util/List<[B>;"
    const-wide v11, 0x7fffffffffffffffL

    .line 25762
    .local v4, "subsampleOffsetUs":J
    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A03:I

    move-object/from16 v1, p6

    if-ne p1, v0, :cond_0

    .line 25763
    const/16 v3, 0x127

    const/16 v2, 0x14

    const/16 v0, 0x12

    invoke-static {v3, v2, v0}, Lcom/facebook/ads/redexgen/X/CO;->A0I(III)Ljava/lang/String;

    move-result-object v4

    .line 25764
    .local v6, "mimeType":Ljava/lang/String;
    :goto_0
    invoke-static/range {p4 .. p4}, Ljava/lang/Integer;->toString(I)Ljava/lang/String;

    move-result-object v3

    const/4 v5, 0x0

    const/4 v6, -0x1

    const/4 v7, 0x0

    const/4 v9, -0x1

    const/4 v10, 0x0

    .line 25765
    move-object/from16 v8, p5

    invoke-static/range {v3 .. v13}, Lcom/facebook/ads/internal/exoplayer2/thirdparty/Format;->A09(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;IILjava/lang/String;ILcom/facebook/ads/internal/exoplayer2/thirdparty/drm/DrmInitData;JLjava/util/List;)Lcom/facebook/ads/internal/exoplayer2/thirdparty/Format;

    move-result-object v0

    iput-object v0, v1, Lcom/facebook/ads/redexgen/X/CM;->A02:Lcom/facebook/ads/internal/exoplayer2/thirdparty/Format;

    .line 25766
    return-void

    .line 25767
    .end local v6    # "mimeType":Ljava/lang/String;
    :cond_0
    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A1P:I

    if-ne p1, v0, :cond_1

    .line 25768
    const/16 v3, 0x184

    const/16 v2, 0x1c

    const/16 v0, 0x37

    invoke-static {v3, v2, v0}, Lcom/facebook/ads/redexgen/X/CO;->A0I(III)Ljava/lang/String;

    move-result-object v4

    .line 25769
    .restart local v6    # "mimeType":Ljava/lang/String;
    add-int/lit8 v0, p3, -0x8

    add-int/lit8 v3, v0, -0x8

    .line 25770
    .local v7, "sampleDescriptionLength":I
    new-array v2, v3, [B

    .line 25771
    .local v8, "sampleDescriptionData":[B
    const/4 v0, 0x0

    invoke-virtual {p0, v2, v0, v3}, Lcom/facebook/ads/redexgen/X/Hz;->A0c([BII)V

    .line 25772
    invoke-static {v2}, Ljava/util/Collections;->singletonList(Ljava/lang/Object;)Ljava/util/List;

    move-result-object v13

    .line 25773
    .end local v7    # "sampleDescriptionLength":I
    .end local v8    # "sampleDescriptionData":[B
    goto :goto_0

    .end local v6    # "mimeType":Ljava/lang/String;
    :cond_1
    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A1X:I

    if-ne p1, v0, :cond_2

    .line 25774
    const/16 v3, 0x16f

    const/16 v2, 0x15

    const/16 v0, 0x9

    invoke-static {v3, v2, v0}, Lcom/facebook/ads/redexgen/X/CO;->A0I(III)Ljava/lang/String;

    move-result-object v4

    .restart local v6    # "mimeType":Ljava/lang/String;
    goto :goto_0

    .line 25775
    .end local v6    # "mimeType":Ljava/lang/String;
    :cond_2
    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A19:I

    if-ne p1, v0, :cond_3

    .line 25776
    const/16 v3, 0x127

    const/16 v2, 0x14

    const/16 v0, 0x12

    invoke-static {v3, v2, v0}, Lcom/facebook/ads/redexgen/X/CO;->A0I(III)Ljava/lang/String;

    move-result-object v4

    .line 25777
    .restart local v6    # "mimeType":Ljava/lang/String;
    const-wide/16 v11, 0x0

    goto :goto_0

    .line 25778
    .end local v6    # "mimeType":Ljava/lang/String;
    :cond_3
    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A0A:I

    if-ne p1, v0, :cond_4

    .line 25779
    const/16 v3, 0x156

    const/16 v2, 0x19

    const/16 v0, 0x38

    invoke-static {v3, v2, v0}, Lcom/facebook/ads/redexgen/X/CO;->A0I(III)Ljava/lang/String;

    move-result-object v4

    .line 25780
    .restart local v6    # "mimeType":Ljava/lang/String;
    const/4 v0, 0x1

    iput v0, v1, Lcom/facebook/ads/redexgen/X/CM;->A01:I

    goto :goto_0

    .line 25781
    .end local v6    # "mimeType":Ljava/lang/String;
    :cond_4
    new-instance v0, Ljava/lang/IllegalStateException;

    invoke-direct {v0}, Ljava/lang/IllegalStateException;-><init>()V

    throw v0
.end method

.method public static A0M(Lcom/facebook/ads/redexgen/X/Hz;IIIILjava/lang/String;ZLcom/facebook/ads/internal/exoplayer2/thirdparty/drm/DrmInitData;Lcom/facebook/ads/redexgen/X/CM;I)V
    .locals 31
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lcom/facebook/ads/redexgen/X/9v;
        }
    .end annotation

    .line 25782
    move/from16 v7, p2

    move-object/from16 v3, p7

    move/from16 v6, p1

    add-int/lit8 v1, v7, 0x8

    const/16 v0, 0x8

    add-int/2addr v1, v0

    move-object/from16 v5, p0

    invoke-virtual {v5, v1}, Lcom/facebook/ads/redexgen/X/Hz;->A0Y(I)V

    .line 25783
    const/4 v4, 0x0

    .line 25784
    .local v4, "quickTimeSoundDescriptionVersion":I
    const/4 v8, 0x6

    if-eqz p6, :cond_12

    .line 25785
    invoke-virtual {v5}, Lcom/facebook/ads/redexgen/X/Hz;->A0I()I

    move-result v4

    .line 25786
    invoke-virtual {v5, v8}, Lcom/facebook/ads/redexgen/X/Hz;->A0Z(I)V

    .line 25787
    .end local v4    # "quickTimeSoundDescriptionVersion":I
    .local v13, "quickTimeSoundDescriptionVersion":I
    :goto_0
    const/4 v2, 0x2

    const/16 v1, 0x10

    const/4 v0, 0x1

    if-eqz v4, :cond_0

    if-ne v4, v0, :cond_11

    .line 25788
    :cond_0
    invoke-virtual {v5}, Lcom/facebook/ads/redexgen/X/Hz;->A0I()I

    move-result v20

    .line 25789
    .local v5, "channelCount":I
    invoke-virtual {v5, v8}, Lcom/facebook/ads/redexgen/X/Hz;->A0Z(I)V

    .line 25790
    invoke-virtual {v5}, Lcom/facebook/ads/redexgen/X/Hz;->A0F()I

    move-result v14

    .line 25791
    .local v6, "sampleRate":I
    if-ne v4, v0, :cond_1

    .line 25792
    invoke-virtual {v5, v1}, Lcom/facebook/ads/redexgen/X/Hz;->A0Z(I)V

    .line 25793
    .end local v6    # "sampleRate":I
    .restart local v4    # "quickTimeSoundDescriptionVersion":I
    .local v5, "sampleRate":I
    :cond_1
    :goto_1
    invoke-virtual {v5}, Lcom/facebook/ads/redexgen/X/Hz;->A06()I

    move-result v4

    .line 25794
    .local v6, "childPosition":I
    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A0R:I

    const/16 v23, 0x0

    move/from16 v15, p3

    move-object/from16 v2, p8

    if-ne v6, v0, :cond_3

    .line 25795
    invoke-static {v5, v7, v15}, Lcom/facebook/ads/redexgen/X/CO;->A09(Lcom/facebook/ads/redexgen/X/Hz;II)Landroid/util/Pair;

    move-result-object v9

    sget-object v1, Lcom/facebook/ads/redexgen/X/CO;->A01:[Ljava/lang/String;

    const/4 v0, 0x2

    aget-object v1, v1, v0

    const/4 v0, 0x2

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    const/16 v0, 0x74

    if-eq v1, v0, :cond_22

    .line 25796
    .local v7, "sampleEntryEncryptionData":Landroid/util/Pair;, "Landroid/util/Pair<Ljava/lang/Integer;Lcom/facebook/ads/internal/exoplayer2/thirdparty/extractor/mp4/TrackEncryptionBox;>;"
    sget-object v8, Lcom/facebook/ads/redexgen/X/CO;->A01:[Ljava/lang/String;

    const-string v1, "rXJd0uQKTdLTB"

    const/4 v0, 0x1

    aput-object v1, v8, v0

    if-eqz v9, :cond_2

    .line 25797
    iget-object v0, v9, Landroid/util/Pair;->first:Ljava/lang/Object;

    check-cast v0, Ljava/lang/Integer;

    invoke-virtual {v0}, Ljava/lang/Integer;->intValue()I

    move-result v6

    .line 25798
    .end local v30
    .local v8, "atomType":I
    if-nez v3, :cond_10

    .line 25799
    move-object/from16 v3, v23

    .line 25800
    .end local p5    # null:Ljava/lang/String;
    .local v3, "drmInitData":Lcom/facebook/ads/internal/exoplayer2/thirdparty/drm/DrmInitData;
    :goto_2
    iget-object v1, v2, Lcom/facebook/ads/redexgen/X/CM;->A03:[Lcom/facebook/ads/redexgen/X/Cf;

    iget-object v0, v9, Landroid/util/Pair;->second:Ljava/lang/Object;

    check-cast v0, Lcom/facebook/ads/redexgen/X/Cf;

    aput-object v0, v1, p9

    .line 25801
    :cond_2
    invoke-virtual {v5, v4}, Lcom/facebook/ads/redexgen/X/Hz;->A0Y(I)V

    .line 25802
    .end local v30
    .end local p5
    .local v9, "drmInitData":Lcom/facebook/ads/internal/exoplayer2/thirdparty/drm/DrmInitData;
    .local v10, "atomType":I
    :cond_3
    const/4 v0, 0x0

    .line 25803
    .local v3, "mimeType":Ljava/lang/String;
    sget v1, Lcom/facebook/ads/redexgen/X/CJ;->A05:I

    if-ne v6, v1, :cond_5

    .line 25804
    const/16 v6, 0x1aa

    const/16 v1, 0x9

    const/16 v0, 0xe

    invoke-static {v6, v1, v0}, Lcom/facebook/ads/redexgen/X/CO;->A0I(III)Ljava/lang/String;

    move-result-object v0

    .line 25805
    :cond_4
    :goto_3
    const/4 v1, 0x0

    sget-object v8, Lcom/facebook/ads/redexgen/X/CO;->A01:[Ljava/lang/String;

    const/4 v6, 0x4

    aget-object v8, v8, v6

    const/16 v6, 0x18

    invoke-virtual {v8, v6}, Ljava/lang/String;->charAt(I)C

    move-result v8

    const/16 v6, 0x69

    if-eq v8, v6, :cond_13

    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    .line 25806
    :cond_5
    sget v1, Lcom/facebook/ads/redexgen/X/CJ;->A0N:I

    if-ne v6, v1, :cond_6

    .line 25807
    const/16 v6, 0x1c9

    const/16 v1, 0xa

    const/16 v0, 0x72

    invoke-static {v6, v1, v0}, Lcom/facebook/ads/redexgen/X/CO;->A0I(III)Ljava/lang/String;

    move-result-object v0

    goto :goto_3

    .line 25808
    :cond_6
    sget v1, Lcom/facebook/ads/redexgen/X/CJ;->A0J:I

    if-ne v6, v1, :cond_7

    .line 25809
    const/16 v6, 0x1f5

    const/16 v1, 0xd

    const/16 v0, 0x7b

    invoke-static {v6, v1, v0}, Lcom/facebook/ads/redexgen/X/CO;->A0I(III)Ljava/lang/String;

    move-result-object v0

    goto :goto_3

    .line 25810
    :cond_7
    sget v1, Lcom/facebook/ads/redexgen/X/CJ;->A0L:I

    if-eq v6, v1, :cond_8

    sget v1, Lcom/facebook/ads/redexgen/X/CJ;->A0M:I

    if-ne v6, v1, :cond_9

    .line 25811
    :cond_8
    const/16 v6, 0x202

    const/16 v1, 0x10

    const/16 v0, 0x30

    invoke-static {v6, v1, v0}, Lcom/facebook/ads/redexgen/X/CO;->A0I(III)Ljava/lang/String;

    move-result-object v0

    goto :goto_3

    .line 25812
    :cond_9
    sget v1, Lcom/facebook/ads/redexgen/X/CJ;->A0K:I

    if-ne v6, v1, :cond_a

    .line 25813
    const/16 v6, 0x212

    const/16 v1, 0x1c

    const/16 v0, 0x6c

    invoke-static {v6, v1, v0}, Lcom/facebook/ads/redexgen/X/CO;->A0I(III)Ljava/lang/String;

    move-result-object v0

    goto :goto_3

    .line 25814
    :cond_a
    sget v1, Lcom/facebook/ads/redexgen/X/CJ;->A0w:I

    if-ne v6, v1, :cond_b

    .line 25815
    const/16 v6, 0x1a0

    const/16 v1, 0xa

    const/16 v0, 0x30

    invoke-static {v6, v1, v0}, Lcom/facebook/ads/redexgen/X/CO;->A0I(III)Ljava/lang/String;

    move-result-object v0

    goto :goto_3

    .line 25816
    :cond_b
    sget v1, Lcom/facebook/ads/redexgen/X/CJ;->A0x:I

    if-ne v6, v1, :cond_c

    .line 25817
    const/16 v6, 0x1bd

    const/16 v1, 0xc

    const/16 v0, 0x8

    invoke-static {v6, v1, v0}, Lcom/facebook/ads/redexgen/X/CO;->A0I(III)Ljava/lang/String;

    move-result-object v0

    goto :goto_3

    .line 25818
    :cond_c
    sget v1, Lcom/facebook/ads/redexgen/X/CJ;->A0b:I

    if-eq v6, v1, :cond_d

    sget v1, Lcom/facebook/ads/redexgen/X/CJ;->A15:I

    if-ne v6, v1, :cond_e

    .line 25819
    :cond_d
    const/16 v6, 0x1ec

    const/16 v1, 0x9

    const/16 v0, 0x49

    invoke-static {v6, v1, v0}, Lcom/facebook/ads/redexgen/X/CO;->A0I(III)Ljava/lang/String;

    move-result-object v0

    goto/16 :goto_3

    .line 25820
    :cond_e
    sget v1, Lcom/facebook/ads/redexgen/X/CJ;->A04:I

    if-ne v6, v1, :cond_f

    .line 25821
    const/16 v6, 0x1e2

    const/16 v1, 0xa

    const/16 v0, 0x5f

    invoke-static {v6, v1, v0}, Lcom/facebook/ads/redexgen/X/CO;->A0I(III)Ljava/lang/String;

    move-result-object v0

    goto/16 :goto_3

    .line 25822
    :cond_f
    sget v10, Lcom/facebook/ads/redexgen/X/CJ;->A06:I

    sget-object v8, Lcom/facebook/ads/redexgen/X/CO;->A01:[Ljava/lang/String;

    const/4 v1, 0x7

    aget-object v1, v8, v1

    invoke-virtual {v1}, Ljava/lang/String;->length()I

    move-result v8

    const/16 v1, 0xe

    if-eq v8, v1, :cond_21

    sget-object v9, Lcom/facebook/ads/redexgen/X/CO;->A01:[Ljava/lang/String;

    const-string v8, "HdbKXAaE1rDpPSyLEE9ktT"

    const/4 v1, 0x1

    aput-object v8, v9, v1

    if-ne v6, v10, :cond_4

    .line 25823
    const/16 v6, 0x1b3

    const/16 v1, 0xa

    const/16 v0, 0x69

    invoke-static {v6, v1, v0}, Lcom/facebook/ads/redexgen/X/CO;->A0I(III)Ljava/lang/String;

    move-result-object v0

    goto/16 :goto_3

    .line 25824
    :cond_10
    iget-object v0, v9, Landroid/util/Pair;->second:Ljava/lang/Object;

    check-cast v0, Lcom/facebook/ads/redexgen/X/Cf;

    iget-object v0, v0, Lcom/facebook/ads/redexgen/X/Cf;->A02:Ljava/lang/String;

    invoke-virtual {v3, v0}, Lcom/facebook/ads/internal/exoplayer2/thirdparty/drm/DrmInitData;->A02(Ljava/lang/String;)Lcom/facebook/ads/internal/exoplayer2/thirdparty/drm/DrmInitData;

    move-result-object v3

    goto/16 :goto_2

    .line 25825
    :cond_11
    if-ne v4, v2, :cond_23

    .line 25826
    invoke-virtual {v5, v1}, Lcom/facebook/ads/redexgen/X/Hz;->A0Z(I)V

    .line 25827
    invoke-virtual {v5}, Lcom/facebook/ads/redexgen/X/Hz;->A03()D

    move-result-wide v0

    invoke-static {v0, v1}, Ljava/lang/Math;->round(D)J

    move-result-wide v0

    long-to-int v14, v0

    .line 25828
    .local v5, "sampleRate":I
    invoke-virtual {v5}, Lcom/facebook/ads/redexgen/X/Hz;->A0H()I

    move-result v20

    .line 25829
    .local v4, "channelCount":I
    const/16 v0, 0x14

    invoke-virtual {v5, v0}, Lcom/facebook/ads/redexgen/X/Hz;->A0Z(I)V

    goto/16 :goto_1

    .line 25830
    :cond_12
    invoke-virtual {v5, v0}, Lcom/facebook/ads/redexgen/X/Hz;->A0Z(I)V

    goto/16 :goto_0

    :cond_13
    sget-object v9, Lcom/facebook/ads/redexgen/X/CO;->A01:[Ljava/lang/String;

    const-string v8, "QyfbMcdO26pfDQU6rlymYH0CLTjMRKBW"

    const/4 v6, 0x6

    aput-object v8, v9, v6

    .line 25831
    .end local v3    # "mimeType":Ljava/lang/String;
    .end local v4    # "channelCount":I
    .end local v5    # "sampleRate":I
    .end local v6    # "childPosition":I
    .local v7, "childPosition":I
    .local v8, "mimeType":Ljava/lang/String;
    .local v17, "channelCount":I
    .local v18, "sampleRate":I
    .local v19, "initializationData":[B
    :goto_4
    sub-int v6, v4, v7

    const/4 v13, -0x1

    move-object/from16 v16, p5

    if-ge v6, v15, :cond_1d

    .line 25832
    invoke-virtual {v5, v4}, Lcom/facebook/ads/redexgen/X/Hz;->A0Y(I)V

    .line 25833
    invoke-virtual {v5}, Lcom/facebook/ads/redexgen/X/Hz;->A08()I

    move-result v6

    .line 25834
    .local v6, "childAtomSize":I
    const/4 v12, 0x0

    if-lez v6, :cond_1b

    const/4 v11, 0x1

    :goto_5
    const/16 v10, 0x23e

    const/16 v9, 0x20

    const/16 v8, 0x57

    invoke-static {v10, v9, v8}, Lcom/facebook/ads/redexgen/X/CO;->A0I(III)Ljava/lang/String;

    move-result-object v8

    invoke-static {v11, v8}, Lcom/facebook/ads/redexgen/X/Ha;->A05(ZLjava/lang/Object;)V

    .line 25835
    invoke-virtual {v5}, Lcom/facebook/ads/redexgen/X/Hz;->A08()I

    move-result v10

    .line 25836
    .local v11, "childAtomType":I
    sget v8, Lcom/facebook/ads/redexgen/X/CJ;->A0T:I

    if-eq v10, v8, :cond_14

    if-eqz p6, :cond_17

    sget v8, Lcom/facebook/ads/redexgen/X/CJ;->A1W:I

    if-ne v10, v8, :cond_17

    .line 25837
    .end local v7    # "childPosition":I
    .end local v8    # "mimeType":Ljava/lang/String;
    .end local v9    # "drmInitData":Lcom/facebook/ads/internal/exoplayer2/thirdparty/drm/DrmInitData;
    .end local v10    # "atomType":I
    .end local v11    # "childAtomType":I
    .restart local v5    # "sampleRate":I
    .local v13, "childPosition":I
    .restart local v21
    .restart local v22
    .restart local v24
    .restart local v27
    :cond_14
    sget v8, Lcom/facebook/ads/redexgen/X/CJ;->A0T:I

    if-ne v10, v8, :cond_16

    .line 25838
    move v8, v4

    .line 25839
    .local v3, "esdsAtomPosition":I
    :goto_6
    if-eq v8, v13, :cond_15

    .line 25840
    invoke-static {v5, v8}, Lcom/facebook/ads/redexgen/X/CO;->A07(Lcom/facebook/ads/redexgen/X/Hz;I)Landroid/util/Pair;

    move-result-object v1

    .line 25841
    .local v4, "mimeTypeAndInitializationData":Landroid/util/Pair;, "Landroid/util/Pair<Ljava/lang/String;[B>;"
    iget-object v0, v1, Landroid/util/Pair;->first:Ljava/lang/Object;

    check-cast v0, Ljava/lang/String;

    .line 25842
    .end local v27
    .restart local v8    # "mimeType":Ljava/lang/String;
    iget-object v1, v1, Landroid/util/Pair;->second:Ljava/lang/Object;

    check-cast v1, [B

    .line 25843
    const/16 v10, 0x1d3

    const/16 v9, 0xf

    const/16 v8, 0x11

    invoke-static {v10, v9, v8}, Lcom/facebook/ads/redexgen/X/CO;->A0I(III)Ljava/lang/String;

    move-result-object v8

    invoke-virtual {v8, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v8

    if-eqz v8, :cond_15

    .line 25844
    invoke-static {v1}, Lcom/facebook/ads/redexgen/X/He;->A03([B)Landroid/util/Pair;

    move-result-object v9

    .line 25845
    .local v7, "audioSpecificConfig":Landroid/util/Pair;, "Landroid/util/Pair<Ljava/lang/Integer;Ljava/lang/Integer;>;"
    iget-object v8, v9, Landroid/util/Pair;->first:Ljava/lang/Object;

    check-cast v8, Ljava/lang/Integer;

    invoke-virtual {v8}, Ljava/lang/Integer;->intValue()I

    move-result v14

    .line 25846
    iget-object v8, v9, Landroid/util/Pair;->second:Ljava/lang/Object;

    check-cast v8, Ljava/lang/Integer;

    invoke-virtual {v8}, Ljava/lang/Integer;->intValue()I

    move-result v20

    .line 25847
    :cond_15
    :goto_7
    add-int/2addr v4, v6

    .line 25848
    .end local v5    # "sampleRate":I
    .end local v6    # "childAtomSize":I
    .end local v13    # "childPosition":I
    .local v7, "childPosition":I
    goto :goto_4

    .line 25849
    :cond_16
    invoke-static {v5, v4, v6}, Lcom/facebook/ads/redexgen/X/CO;->A03(Lcom/facebook/ads/redexgen/X/Hz;II)I

    move-result v8

    goto :goto_6

    .line 25850
    :cond_17
    sget v11, Lcom/facebook/ads/redexgen/X/CJ;->A0F:I

    sget-object v9, Lcom/facebook/ads/redexgen/X/CO;->A01:[Ljava/lang/String;

    const/4 v8, 0x2

    aget-object v9, v9, v8

    const/4 v8, 0x2

    invoke-virtual {v9, v8}, Ljava/lang/String;->charAt(I)C

    move-result v9

    const/16 v8, 0x74

    if-eq v9, v8, :cond_1c

    sget-object v13, Lcom/facebook/ads/redexgen/X/CO;->A01:[Ljava/lang/String;

    const-string v9, "Q"

    const/4 v8, 0x0

    aput-object v9, v13, v8

    const-string v9, "M"

    const/4 v8, 0x5

    aput-object v9, v13, v8

    if-ne v10, v11, :cond_18

    .line 25851
    add-int/lit8 v8, v4, 0x8

    invoke-virtual {v5, v8}, Lcom/facebook/ads/redexgen/X/Hz;->A0Y(I)V

    .line 25852
    invoke-static/range {p4 .. p4}, Ljava/lang/Integer;->toString(I)Ljava/lang/String;

    move-result-object v9

    move-object/from16 v8, v16

    invoke-static {v5, v9, v8, v3}, Lcom/facebook/ads/redexgen/X/AQ;->A07(Lcom/facebook/ads/redexgen/X/Hz;Ljava/lang/String;Ljava/lang/String;Lcom/facebook/ads/internal/exoplayer2/thirdparty/drm/DrmInitData;)Lcom/facebook/ads/internal/exoplayer2/thirdparty/Format;

    move-result-object v8

    iput-object v8, v2, Lcom/facebook/ads/redexgen/X/CM;->A02:Lcom/facebook/ads/internal/exoplayer2/thirdparty/Format;

    goto :goto_7

    .line 25853
    :cond_18
    sget v8, Lcom/facebook/ads/redexgen/X/CJ;->A0I:I

    if-ne v10, v8, :cond_19

    .line 25854
    add-int/lit8 v8, v4, 0x8

    invoke-virtual {v5, v8}, Lcom/facebook/ads/redexgen/X/Hz;->A0Y(I)V

    .line 25855
    invoke-static/range {p4 .. p4}, Ljava/lang/Integer;->toString(I)Ljava/lang/String;

    move-result-object v9

    move-object/from16 v8, v16

    invoke-static {v5, v9, v8, v3}, Lcom/facebook/ads/redexgen/X/AQ;->A08(Lcom/facebook/ads/redexgen/X/Hz;Ljava/lang/String;Ljava/lang/String;Lcom/facebook/ads/internal/exoplayer2/thirdparty/drm/DrmInitData;)Lcom/facebook/ads/internal/exoplayer2/thirdparty/Format;

    move-result-object v8

    iput-object v8, v2, Lcom/facebook/ads/redexgen/X/CM;->A02:Lcom/facebook/ads/internal/exoplayer2/thirdparty/Format;

    goto :goto_7

    .line 25856
    :cond_19
    sget v8, Lcom/facebook/ads/redexgen/X/CJ;->A0H:I

    if-ne v10, v8, :cond_1a

    .line 25857
    invoke-static/range {p4 .. p4}, Ljava/lang/Integer;->toString(I)Ljava/lang/String;

    move-result-object v24

    const/16 v26, 0x0

    const/16 v27, -0x1

    const/16 v28, -0x1

    const/16 p0, 0x0

    const/16 p2, 0x0

    .line 25858
    move-object/from16 v25, v0

    .end local v6
    .local v25, "childAtomSize":I
    .end local v7    # "childPosition":I
    .local v26, "childPosition":I
    .end local v8    # "mimeType":Ljava/lang/String;
    .local v27, "mimeType":Ljava/lang/String;
    .end local v9
    .local v21, "drmInitData":Lcom/facebook/ads/internal/exoplayer2/thirdparty/drm/DrmInitData;
    .end local v10
    .local v22, "atomType":I
    .end local v11
    .local v28, "childAtomType":I
    .end local v13
    .local v24, "quickTimeSoundDescriptionVersion":I
    move-object/from16 p3, v16

    move/from16 v29, v20

    move/from16 v30, v14

    move-object/from16 p1, v3

    invoke-static/range {v24 .. v34}, Lcom/facebook/ads/internal/exoplayer2/thirdparty/Format;->A07(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;IIIILjava/util/List;Lcom/facebook/ads/internal/exoplayer2/thirdparty/drm/DrmInitData;ILjava/lang/String;)Lcom/facebook/ads/internal/exoplayer2/thirdparty/Format;

    move-result-object v8

    iput-object v8, v2, Lcom/facebook/ads/redexgen/X/CM;->A02:Lcom/facebook/ads/internal/exoplayer2/thirdparty/Format;

    goto :goto_7

    .line 25859
    .end local v21    # "drmInitData":Lcom/facebook/ads/internal/exoplayer2/thirdparty/drm/DrmInitData;
    .end local v22    # "atomType":I
    .end local v24    # "quickTimeSoundDescriptionVersion":I
    .end local v25    # "childAtomSize":I
    .end local v26    # "childPosition":I
    .end local v27    # "mimeType":Ljava/lang/String;
    .end local v28    # "childAtomType":I
    .restart local v6    # "childAtomSize":I
    .restart local v7    # "childPosition":I
    .restart local v8    # "mimeType":Ljava/lang/String;
    .restart local v9    # "drmInitData":Lcom/facebook/ads/internal/exoplayer2/thirdparty/drm/DrmInitData;
    .restart local v10    # "atomType":I
    .restart local v11    # "childAtomType":I
    .restart local v13    # "childPosition":I
    .end local v6    # "childAtomSize":I
    .end local v7    # "childPosition":I
    .end local v8    # "mimeType":Ljava/lang/String;
    .end local v9    # "drmInitData":Lcom/facebook/ads/internal/exoplayer2/thirdparty/drm/DrmInitData;
    .end local v10    # "atomType":I
    .end local v11    # "childAtomType":I
    .end local v13    # "childPosition":I
    .restart local v21    # "drmInitData":Lcom/facebook/ads/internal/exoplayer2/thirdparty/drm/DrmInitData;
    .restart local v22    # "atomType":I
    .restart local v24    # "quickTimeSoundDescriptionVersion":I
    .restart local v25    # "childAtomSize":I
    .restart local v26    # "childPosition":I
    .restart local v27    # "mimeType":Ljava/lang/String;
    .restart local v28    # "childAtomType":I
    :cond_1a
    sget v8, Lcom/facebook/ads/redexgen/X/CJ;->A06:I

    .end local v28    # "childAtomType":I
    .local v5, "childAtomType":I
    if-ne v10, v8, :cond_15

    .line 25860
    .end local v25    # "childAtomSize":I
    .restart local v6    # "childAtomSize":I
    new-array v1, v6, [B

    .line 25861
    .end local v19    # "initializationData":[B
    .local v4, "initializationData":[B
    .end local v26    # "childPosition":I
    .local v13, "childPosition":I
    invoke-virtual {v5, v4}, Lcom/facebook/ads/redexgen/X/Hz;->A0Y(I)V

    .line 25862
    invoke-virtual {v5, v1, v12, v6}, Lcom/facebook/ads/redexgen/X/Hz;->A0c([BII)V

    goto :goto_7

    .line 25863
    :cond_1b
    const/4 v11, 0x0

    goto/16 :goto_5

    :cond_1c
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    .line 25864
    .end local v21    # "drmInitData":Lcom/facebook/ads/internal/exoplayer2/thirdparty/drm/DrmInitData;
    .end local v22    # "atomType":I
    .end local v24    # "quickTimeSoundDescriptionVersion":I
    .restart local v9    # "drmInitData":Lcom/facebook/ads/internal/exoplayer2/thirdparty/drm/DrmInitData;
    .restart local v10    # "atomType":I
    .local v13, "quickTimeSoundDescriptionVersion":I
    .end local v7
    .end local v8
    .end local v9    # "drmInitData":Lcom/facebook/ads/internal/exoplayer2/thirdparty/drm/DrmInitData;
    .end local v10    # "atomType":I
    .local v13, "childPosition":I
    .restart local v21    # "drmInitData":Lcom/facebook/ads/internal/exoplayer2/thirdparty/drm/DrmInitData;
    .restart local v22    # "atomType":I
    .restart local v24    # "quickTimeSoundDescriptionVersion":I
    .restart local v27    # "mimeType":Ljava/lang/String;
    :cond_1d
    iget-object v4, v2, Lcom/facebook/ads/redexgen/X/CM;->A02:Lcom/facebook/ads/internal/exoplayer2/thirdparty/Format;

    if-nez v4, :cond_1e

    .end local v27    # "mimeType":Ljava/lang/String;
    .local v12, "mimeType":Ljava/lang/String;
    if-eqz v0, :cond_1e

    .line 25865
    const/16 v6, 0x1ec

    const/16 v5, 0x9

    const/16 v4, 0x49

    invoke-static {v6, v5, v4}, Lcom/facebook/ads/redexgen/X/CO;->A0I(III)Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v4, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v4

    if-eqz v4, :cond_20

    const/16 v22, 0x2

    .line 25866
    .local v10, "pcmEncoding":I
    :goto_8
    invoke-static/range {p4 .. p4}, Ljava/lang/Integer;->toString(I)Ljava/lang/String;

    move-result-object v15

    const/16 v17, 0x0

    const/16 v18, -0x1

    const/16 v19, -0x1

    .line 25867
    if-nez v1, :cond_1f

    :goto_9
    const/16 v25, 0x0

    .line 25868
    .end local v12    # "mimeType":Ljava/lang/String;
    .local v20, "mimeType":Ljava/lang/String;
    .end local v13    # "childPosition":I
    .local v23, "childPosition":I
    move-object/from16 v24, v3

    move-object/from16 v26, v16

    move-object/from16 v16, v0

    move/from16 v21, v14

    invoke-static/range {v15 .. v26}, Lcom/facebook/ads/internal/exoplayer2/thirdparty/Format;->A06(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;IIIIILjava/util/List;Lcom/facebook/ads/internal/exoplayer2/thirdparty/drm/DrmInitData;ILjava/lang/String;)Lcom/facebook/ads/internal/exoplayer2/thirdparty/Format;

    move-result-object v0

    iput-object v0, v2, Lcom/facebook/ads/redexgen/X/CM;->A02:Lcom/facebook/ads/internal/exoplayer2/thirdparty/Format;

    .line 25869
    .end local v13
    .end local v27
    .restart local v20    # "mimeType":Ljava/lang/String;
    .restart local v23    # "childPosition":I
    :cond_1e
    return-void

    .line 25870
    :cond_1f
    invoke-static {v1}, Ljava/util/Collections;->singletonList(Ljava/lang/Object;)Ljava/util/List;

    move-result-object v23

    goto :goto_9

    .line 25871
    :cond_20
    const/16 v22, -0x1

    goto :goto_8

    :cond_21
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    :cond_22
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    .line 25872
    .end local v4    # "initializationData":[B
    .end local v5    # "childAtomType":I
    :cond_23
    return-void
.end method

.method public static A0N([JJJJ)Z
    .locals 6

    .line 25873
    array-length v3, p0

    const/4 v5, 0x1

    sub-int/2addr v3, v5

    .line 25874
    .local v0, "lastIndex":I
    const/4 v2, 0x3

    const/4 v1, 0x0

    invoke-static {v2, v1, v3}, Lcom/facebook/ads/redexgen/X/IF;->A06(III)I

    move-result v4

    .line 25875
    .local v4, "latestDelayIndex":I
    array-length v0, p0

    sub-int/2addr v0, v2

    .line 25876
    invoke-static {v0, v1, v3}, Lcom/facebook/ads/redexgen/X/IF;->A06(III)I

    move-result v3

    .line 25877
    .local v2, "earliestPaddingIndex":I
    aget-wide v1, p0, v1

    cmp-long v0, v1, p3

    if-gtz v0, :cond_0

    aget-wide v1, p0, v4

    cmp-long v0, p3, v1

    if-gez v0, :cond_0

    aget-wide v1, p0, v3

    cmp-long v0, v1, p5

    if-gez v0, :cond_0

    cmp-long v0, p5, p1

    if-gtz v0, :cond_0

    :goto_0
    return v5

    :cond_0
    const/4 v5, 0x0

    goto :goto_0
.end method

.method public static A0O(Lcom/facebook/ads/redexgen/X/Hz;II)[B
    .locals 7

    .line 25878
    add-int/lit8 v3, p1, 0x8

    .line 25879
    .local v0, "childPosition":I
    :goto_0
    sub-int v4, v3, p1

    sget-object v2, Lcom/facebook/ads/redexgen/X/CO;->A01:[Ljava/lang/String;

    const/4 v0, 0x0

    aget-object v1, v2, v0

    const/4 v0, 0x5

    aget-object v0, v2, v0

    invoke-virtual {v1}, Ljava/lang/String;->length()I

    move-result v1

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v0

    if-eq v1, v0, :cond_1

    :cond_0
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    :cond_1
    sget-object v2, Lcom/facebook/ads/redexgen/X/CO;->A01:[Ljava/lang/String;

    const-string v1, "hiyhCkw83qioTZht4ycME2aPtqNlSya0"

    const/4 v0, 0x2

    aput-object v1, v2, v0

    if-ge v4, p2, :cond_3

    .line 25880
    invoke-virtual {p0, v3}, Lcom/facebook/ads/redexgen/X/Hz;->A0Y(I)V

    .line 25881
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Hz;->A08()I

    move-result v6

    .line 25882
    .local v1, "childAtomSize":I
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Hz;->A08()I

    move-result v5

    .line 25883
    .local v2, "childAtomType":I
    sget v4, Lcom/facebook/ads/redexgen/X/CJ;->A0r:I

    sget-object v1, Lcom/facebook/ads/redexgen/X/CO;->A01:[Ljava/lang/String;

    const/4 v0, 0x2

    aget-object v1, v1, v0

    const/4 v0, 0x2

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    const/16 v0, 0x74

    if-eq v1, v0, :cond_0

    sget-object v2, Lcom/facebook/ads/redexgen/X/CO;->A01:[Ljava/lang/String;

    const-string v1, "K"

    const/4 v0, 0x0

    aput-object v1, v2, v0

    const-string v1, "V"

    const/4 v0, 0x5

    aput-object v1, v2, v0

    if-ne v5, v4, :cond_2

    .line 25884
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/Hz;->A00:[B

    add-int v0, v3, v6

    invoke-static {v1, v3, v0}, Ljava/util/Arrays;->copyOfRange([BII)[B

    move-result-object v0

    return-object v0

    .line 25885
    :cond_2
    add-int/2addr v3, v6

    .line 25886
    .end local v1    # "childAtomSize":I
    .end local v2    # "childAtomType":I
    goto :goto_0

    .line 25887
    :cond_3
    const/4 v3, 0x0

    sget-object v1, Lcom/facebook/ads/redexgen/X/CO;->A01:[Ljava/lang/String;

    const/4 v0, 0x4

    aget-object v1, v1, v0

    const/16 v0, 0x18

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    const/16 v0, 0x69

    if-eq v1, v0, :cond_4

    return-object v3

    :cond_4
    sget-object v2, Lcom/facebook/ads/redexgen/X/CO;->A01:[Ljava/lang/String;

    const-string v1, "op0Xll27WP2wTMdM3NyEU8fL7oyu7ROp"

    const/4 v0, 0x6

    aput-object v1, v2, v0

    return-object v3
.end method
