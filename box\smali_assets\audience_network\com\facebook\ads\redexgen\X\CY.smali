.class public final Lcom/facebook/ads/redexgen/X/CY;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/facebook/ads/redexgen/X/XL;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "Mp4Track"
.end annotation


# instance fields
.field public A00:I

.field public final A01:Lcom/facebook/ads/redexgen/X/C4;

.field public final A02:Lcom/facebook/ads/redexgen/X/Ce;

.field public final A03:Lcom/facebook/ads/redexgen/X/Ch;


# direct methods
.method public constructor <init>(Lcom/facebook/ads/redexgen/X/Ce;Lcom/facebook/ads/redexgen/X/Ch;Lcom/facebook/ads/redexgen/X/C4;)V
    .locals 0

    .line 26268
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 26269
    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/CY;->A02:Lcom/facebook/ads/redexgen/X/Ce;

    .line 26270
    iput-object p2, p0, Lcom/facebook/ads/redexgen/X/CY;->A03:Lcom/facebook/ads/redexgen/X/Ch;

    .line 26271
    iput-object p3, p0, Lcom/facebook/ads/redexgen/X/CY;->A01:Lcom/facebook/ads/redexgen/X/C4;

    .line 26272
    return-void
.end method
