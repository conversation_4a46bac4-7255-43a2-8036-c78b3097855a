.class public abstract Lcom/facebook/ads/redexgen/X/Cc;
.super Ljava/lang/Object;
.source ""


# static fields
.field public static A00:[B

.field public static A01:[Ljava/lang/String;

.field public static final A02:[I


# direct methods
.method public static constructor <clinit>()V
    .locals 4

    .line 1146
    const/16 v0, 0x8

    new-array v2, v0, [Ljava/lang/String;

    const/4 v1, 0x0

    const-string v0, "bdaFL9zMZ"

    aput-object v0, v2, v1

    const/4 v1, 0x1

    const-string v0, "I63pCoSBBbO7fDlHhIa7BrZ6Ava1cIF3"

    aput-object v0, v2, v1

    const/4 v1, 0x2

    const-string v0, "x5nJfB3W8HSDeHix"

    aput-object v0, v2, v1

    const/4 v1, 0x3

    const-string v0, "yyLZDQNtNFC811gZWNvs8tiPSoT"

    aput-object v0, v2, v1

    const/4 v1, 0x4

    const-string v0, "kE4kWLsRx96QKHczqOKboI9lp"

    aput-object v0, v2, v1

    const/4 v1, 0x5

    const-string v0, "rJUZcTDeN"

    aput-object v0, v2, v1

    const/4 v1, 0x6

    const-string v0, "z735Hr0viTdtcy9jRTmcNapxWooAX9zU"

    aput-object v0, v2, v1

    const/4 v1, 0x7

    const-string v0, "rl5X7GmnpjWHRCYLou0FtooQfFV"

    aput-object v0, v2, v1

    sput-object v2, Lcom/facebook/ads/redexgen/X/Cc;->A01:[Ljava/lang/String;

    invoke-static {}, Lcom/facebook/ads/redexgen/X/Cc;->A01()V

    const/16 v0, 0x18

    new-array v3, v0, [I

    .line 1147
    const/16 v2, 0x4f

    const/4 v1, 0x4

    const/16 v0, 0x45

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Cc;->A00(III)Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/IF;->A08(Ljava/lang/String;)I

    move-result v1

    const/4 v0, 0x0

    aput v1, v3, v0

    .line 1148
    const/16 v2, 0x3b

    const/4 v1, 0x4

    const/16 v0, 0x34

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Cc;->A00(III)Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/IF;->A08(Ljava/lang/String;)I

    move-result v1

    const/4 v0, 0x1

    aput v1, v3, v0

    .line 1149
    const/16 v2, 0x3f

    const/4 v1, 0x4

    const/16 v0, 0x54

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Cc;->A00(III)Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/IF;->A08(Ljava/lang/String;)I

    move-result v1

    const/4 v0, 0x2

    aput v1, v3, v0

    .line 1150
    const/16 v2, 0x43

    const/4 v1, 0x4

    const/16 v0, 0x5b

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Cc;->A00(III)Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/IF;->A08(Ljava/lang/String;)I

    move-result v1

    const/4 v0, 0x3

    aput v1, v3, v0

    .line 1151
    const/16 v2, 0x47

    const/4 v1, 0x4

    const/16 v0, 0x14

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Cc;->A00(III)Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/IF;->A08(Ljava/lang/String;)I

    move-result v1

    const/4 v0, 0x4

    aput v1, v3, v0

    .line 1152
    const/16 v2, 0x4b

    const/4 v1, 0x4

    const/16 v0, 0x26

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Cc;->A00(III)Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/IF;->A08(Ljava/lang/String;)I

    move-result v1

    const/4 v0, 0x5

    aput v1, v3, v0

    .line 1153
    const/16 v2, 0x2b

    const/4 v1, 0x4

    const/16 v0, 0x29

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Cc;->A00(III)Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/IF;->A08(Ljava/lang/String;)I

    move-result v1

    const/4 v0, 0x6

    aput v1, v3, v0

    .line 1154
    const/16 v2, 0x37

    const/4 v1, 0x4

    const/16 v0, 0x2a

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Cc;->A00(III)Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/IF;->A08(Ljava/lang/String;)I

    move-result v1

    const/4 v0, 0x7

    aput v1, v3, v0

    .line 1155
    const/16 v2, 0x33

    const/4 v1, 0x4

    const/16 v0, 0x39

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Cc;->A00(III)Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/IF;->A08(Ljava/lang/String;)I

    move-result v1

    const/16 v0, 0x8

    aput v1, v3, v0

    .line 1156
    const/16 v2, 0x57

    const/4 v1, 0x4

    const/16 v0, 0x31

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Cc;->A00(III)Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/IF;->A08(Ljava/lang/String;)I

    move-result v1

    const/16 v0, 0x9

    aput v1, v3, v0

    .line 1157
    const/16 v2, 0x5b

    const/4 v1, 0x4

    const/16 v0, 0x7a

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Cc;->A00(III)Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/IF;->A08(Ljava/lang/String;)I

    move-result v1

    const/16 v0, 0xa

    aput v1, v3, v0

    .line 1158
    const/4 v2, 0x0

    const/4 v1, 0x4

    const/16 v0, 0x27

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Cc;->A00(III)Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/IF;->A08(Ljava/lang/String;)I

    move-result v1

    const/16 v0, 0xb

    aput v1, v3, v0

    .line 1159
    const/4 v2, 0x4

    const/4 v1, 0x4

    const/16 v0, 0x6c

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Cc;->A00(III)Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/IF;->A08(Ljava/lang/String;)I

    move-result v1

    const/16 v0, 0xc

    aput v1, v3, v0

    .line 1160
    const/16 v2, 0x13

    const/4 v1, 0x4

    const/16 v0, 0x3f

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Cc;->A00(III)Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/IF;->A08(Ljava/lang/String;)I

    move-result v1

    const/16 v0, 0xd

    aput v1, v3, v0

    .line 1161
    const/16 v2, 0x17

    const/4 v1, 0x4

    const/16 v0, 0x61

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Cc;->A00(III)Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/IF;->A08(Ljava/lang/String;)I

    move-result v1

    const/16 v0, 0xe

    aput v1, v3, v0

    .line 1162
    const/16 v2, 0x8

    const/4 v1, 0x4

    const/16 v0, 0x4b

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Cc;->A00(III)Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/IF;->A08(Ljava/lang/String;)I

    move-result v1

    const/16 v0, 0xf

    aput v1, v3, v0

    .line 1163
    const/16 v2, 0xc

    const/4 v1, 0x4

    const/16 v0, 0x69

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Cc;->A00(III)Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/IF;->A08(Ljava/lang/String;)I

    move-result v1

    const/16 v0, 0x10

    aput v1, v3, v0

    .line 1164
    const/16 v2, 0x1f

    const/4 v1, 0x4

    const/16 v0, 0x78

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Cc;->A00(III)Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/IF;->A08(Ljava/lang/String;)I

    move-result v1

    const/16 v0, 0x11

    aput v1, v3, v0

    .line 1165
    const/16 v2, 0x1b

    const/4 v1, 0x4

    const/16 v0, 0xf

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Cc;->A00(III)Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/IF;->A08(Ljava/lang/String;)I

    move-result v1

    const/16 v0, 0x12

    aput v1, v3, v0

    .line 1166
    const/16 v2, 0x2f

    const/4 v1, 0x4

    const/16 v0, 0x33

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Cc;->A00(III)Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/IF;->A08(Ljava/lang/String;)I

    move-result v1

    const/16 v0, 0x13

    aput v1, v3, v0

    .line 1167
    const/16 v2, 0x53

    const/4 v1, 0x4

    const/16 v0, 0x29

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Cc;->A00(III)Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/IF;->A08(Ljava/lang/String;)I

    move-result v1

    const/16 v0, 0x14

    aput v1, v3, v0

    .line 1168
    const/16 v2, 0x23

    const/4 v1, 0x4

    const/16 v0, 0x78

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Cc;->A00(III)Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/IF;->A08(Ljava/lang/String;)I

    move-result v1

    const/16 v0, 0x15

    aput v1, v3, v0

    .line 1169
    const/16 v2, 0x5f

    const/4 v1, 0x4

    const/16 v0, 0xb

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Cc;->A00(III)Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/IF;->A08(Ljava/lang/String;)I

    move-result v1

    const/16 v0, 0x16

    aput v1, v3, v0

    .line 1170
    const/16 v2, 0x27

    const/4 v1, 0x4

    const/16 v0, 0x10

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Cc;->A00(III)Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/IF;->A08(Ljava/lang/String;)I

    move-result v1

    const/16 v0, 0x17

    aput v1, v3, v0

    sput-object v3, Lcom/facebook/ads/redexgen/X/Cc;->A02:[I

    .line 1171
    return-void
.end method

.method public static A00(III)Ljava/lang/String;
    .locals 4

    sget-object v1, Lcom/facebook/ads/redexgen/X/Cc;->A00:[B

    add-int v0, p0, p1

    invoke-static {v1, p0, v0}, Ljava/util/Arrays;->copyOfRange([BII)[B

    move-result-object v3

    const/4 p0, 0x0

    :goto_0
    array-length p1, v3

    sget-object v2, Lcom/facebook/ads/redexgen/X/Cc;->A01:[Ljava/lang/String;

    const/4 v0, 0x0

    aget-object v1, v2, v0

    const/4 v0, 0x5

    aget-object v0, v2, v0

    invoke-virtual {v1}, Ljava/lang/String;->length()I

    move-result v1

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v0

    if-eq v1, v0, :cond_0

    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    :cond_0
    sget-object v2, Lcom/facebook/ads/redexgen/X/Cc;->A01:[Ljava/lang/String;

    const-string v1, "MWn"

    const/4 v0, 0x3

    aput-object v1, v2, v0

    if-ge p0, p1, :cond_2

    aget-byte v0, v3, p0

    sub-int/2addr v0, p2

    add-int/lit8 v0, v0, -0x7a

    int-to-byte v0, v0

    aput-byte v0, v3, p0

    sget-object v1, Lcom/facebook/ads/redexgen/X/Cc;->A01:[Ljava/lang/String;

    const/4 v0, 0x7

    aget-object v0, v1, v0

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v1

    const/16 v0, 0xc

    if-eq v1, v0, :cond_1

    sget-object v2, Lcom/facebook/ads/redexgen/X/Cc;->A01:[Ljava/lang/String;

    const-string v1, "KMAr4"

    const/4 v0, 0x7

    aput-object v1, v2, v0

    add-int/lit8 p0, p0, 0x1

    goto :goto_0

    :cond_1
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    :cond_2
    new-instance v0, Ljava/lang/String;

    invoke-direct {v0, v3}, Ljava/lang/String;-><init>([B)V

    return-object v0
.end method

.method public static A01()V
    .locals 1

    const/16 v0, 0x63

    new-array v0, v0, [B

    fill-array-data v0, :array_0

    sput-object v0, Lcom/facebook/ads/redexgen/X/Cc;->A00:[B

    return-void

    :array_0
    .array-data 1
        -0x2ct
        0x8t
        -0x2dt
        0x2t
        0x19t
        0x4dt
        0x18t
        0x48t
        -0x8t
        0x2ct
        0x2at
        -0x5t
        0x16t
        0x4at
        0x4at
        0x19t
        -0x31t
        0x3t
        0xct
        -0x14t
        0x20t
        0x2bt
        -0x11t
        0xet
        0x42t
        0x4et
        0x11t
        -0x2at
        -0x43t
        -0x36t
        -0x57t
        0x3ft
        0x26t
        0x48t
        0x12t
        0x3ft
        0x26t
        0x48t
        0x42t
        -0x29t
        -0x23t
        -0x28t
        -0x20t
        0x4t
        0x19t
        0x6t
        -0x2ct
        0x13t
        -0x1ft
        0x23t
        -0x33t
        0x1bt
        0x18t
        0x29t
        -0x1ct
        0xct
        0x1at
        0x7t
        -0x2bt
        0x17t
        0x21t
        0x1dt
        -0x20t
        0x37t
        0x41t
        0x3dt
        0x1t
        0x3et
        0x48t
        0x44t
        0x9t
        -0x9t
        0x1t
        -0x3t
        -0x3dt
        0x9t
        0x13t
        0xft
        -0x2at
        0x28t
        0x32t
        0x2et
        0x2ct
        0xet
        0x7t
        0x7t
        0xct
        0x18t
        0x1bt
        -0x21t
        -0x24t
        0x61t
        0x64t
        0x28t
        0x26t
        -0xat
        -0x7t
        -0x5bt
        -0x5bt
    .end array-data
.end method

.method public static A02(I)Z
    .locals 6

    .line 26309
    ushr-int/lit8 v3, p0, 0x8

    const/16 v2, 0x10

    const/4 v1, 0x3

    const/16 v0, 0x22

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Cc;->A00(III)Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/IF;->A08(Ljava/lang/String;)I

    move-result v0

    const/4 v5, 0x1

    if-ne v3, v0, :cond_0

    .line 26310
    return v5

    .line 26311
    :cond_0
    sget-object v4, Lcom/facebook/ads/redexgen/X/Cc;->A02:[I

    array-length v3, v4

    const/4 v2, 0x0

    const/4 v1, 0x0

    :goto_0
    if-ge v1, v3, :cond_2

    aget v0, v4, v1

    .line 26312
    .local v5, "compatibleBrand":I
    if-ne v0, p0, :cond_1

    .line 26313
    return v5

    .line 26314
    .end local v5    # "compatibleBrand":I
    :cond_1
    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    .line 26315
    :cond_2
    return v2
.end method

.method public static A03(Lcom/facebook/ads/redexgen/X/Bt;)Z
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;,
            Ljava/lang/InterruptedException;
        }
    .end annotation

    .line 26316
    const/4 v0, 0x1

    invoke-static {p0, v0}, Lcom/facebook/ads/redexgen/X/Cc;->A05(Lcom/facebook/ads/redexgen/X/Bt;Z)Z

    move-result v0

    return v0
.end method

.method public static A04(Lcom/facebook/ads/redexgen/X/Bt;)Z
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;,
            Ljava/lang/InterruptedException;
        }
    .end annotation

    .line 26317
    const/4 v0, 0x0

    invoke-static {p0, v0}, Lcom/facebook/ads/redexgen/X/Cc;->A05(Lcom/facebook/ads/redexgen/X/Bt;Z)Z

    move-result v0

    return v0
.end method

.method public static A05(Lcom/facebook/ads/redexgen/X/Bt;Z)Z
    .locals 16
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;,
            Ljava/lang/InterruptedException;
        }
    .end annotation

    .line 26318
    move-object/from16 v10, p0

    invoke-interface {v10}, Lcom/facebook/ads/redexgen/X/Bt;->A7I()J

    move-result-wide v3

    .line 26319
    .local v1, "inputLength":J
    const-wide/16 v1, 0x1000

    const-wide/16 v15, -0x1

    cmp-long v0, v3, v15

    if-eqz v0, :cond_0

    cmp-long v0, v3, v1

    if-lez v0, :cond_f

    .line 26320
    :cond_0
    :goto_0
    long-to-int v12, v1

    .line 26321
    .local v4, "bytesToSearch":I
    const/16 v0, 0x40

    new-instance v9, Lcom/facebook/ads/redexgen/X/Hz;

    invoke-direct {v9, v0}, Lcom/facebook/ads/redexgen/X/Hz;-><init>(I)V

    .line 26322
    .local v3, "buffer":Lcom/facebook/ads/redexgen/X/Hz;
    const/4 v8, 0x0

    .line 26323
    .local v7, "bytesSearched":I
    const/4 v14, 0x0

    .line 26324
    .local v8, "foundGoodFileType":Z
    const/4 v11, 0x0

    .line 26325
    .local v9, "isFragmented":Z
    :goto_1
    const/4 v1, 0x0

    if-ge v8, v12, :cond_13

    .line 26326
    const/16 v7, 0x8

    .line 26327
    .local v12, "headerSize":I
    invoke-virtual {v9, v7}, Lcom/facebook/ads/redexgen/X/Hz;->A0W(I)V

    .line 26328
    iget-object v0, v9, Lcom/facebook/ads/redexgen/X/Hz;->A00:[B

    invoke-interface {v10, v0, v1, v7}, Lcom/facebook/ads/redexgen/X/Bt;->ADv([BII)V

    .line 26329
    invoke-virtual {v9}, Lcom/facebook/ads/redexgen/X/Hz;->A0M()J

    move-result-wide v4

    .line 26330
    .local v13, "atomSize":J
    invoke-virtual {v9}, Lcom/facebook/ads/redexgen/X/Hz;->A08()I

    move-result v6

    .line 26331
    .local v15, "atomType":I
    const-wide/16 v2, 0x1

    const/16 v1, 0x8

    cmp-long v0, v4, v2

    if-nez v0, :cond_e

    .line 26332
    const/16 v7, 0x10

    .line 26333
    iget-object v0, v9, Lcom/facebook/ads/redexgen/X/Hz;->A00:[B

    invoke-interface {v10, v0, v1, v1}, Lcom/facebook/ads/redexgen/X/Bt;->ADv([BII)V

    .line 26334
    const/16 v3, 0x10

    sget-object v1, Lcom/facebook/ads/redexgen/X/Cc;->A01:[Ljava/lang/String;

    const/4 v0, 0x3

    aget-object v0, v1, v0

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v1

    const/16 v0, 0x15

    if-eq v1, v0, :cond_12

    sget-object v2, Lcom/facebook/ads/redexgen/X/Cc;->A01:[Ljava/lang/String;

    const-string v1, "x"

    const/4 v0, 0x3

    aput-object v1, v2, v0

    invoke-virtual {v9, v3}, Lcom/facebook/ads/redexgen/X/Hz;->A0X(I)V

    .line 26335
    invoke-virtual {v9}, Lcom/facebook/ads/redexgen/X/Hz;->A0N()J

    move-result-wide v4

    .line 26336
    .end local p2
    :cond_1
    :goto_2
    int-to-long v0, v7

    cmp-long v2, v4, v0

    if-gez v2, :cond_2

    .line 26337
    const/4 v3, 0x0

    sget-object v1, Lcom/facebook/ads/redexgen/X/Cc;->A01:[Ljava/lang/String;

    const/4 v0, 0x6

    aget-object v0, v1, v0

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v1

    const/16 v0, 0xe

    if-eq v1, v0, :cond_10

    sget-object v2, Lcom/facebook/ads/redexgen/X/Cc;->A01:[Ljava/lang/String;

    const-string v1, "xNdZT7PF3cfE5U51qI7qEUy5Bn2I1n4"

    const/4 v0, 0x6

    aput-object v1, v2, v0

    return v3

    .line 26338
    :cond_2
    add-int/2addr v8, v7

    .line 26339
    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A0k:I

    if-ne v6, v0, :cond_3

    .line 26340
    const-wide/16 v15, -0x1

    goto :goto_1

    .line 26341
    :cond_3
    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A0j:I

    if-eq v6, v0, :cond_4

    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A0n:I

    if-ne v6, v0, :cond_5

    const/4 v0, 0x0

    .line 26342
    :goto_3
    const/4 v11, 0x1

    .line 26343
    goto/16 :goto_6

    .line 26344
    .restart local v12    # "headerSize":I
    .restart local v13    # "atomSize":J
    .restart local v15    # "atomType":I
    :cond_4
    const/4 v0, 0x0

    goto :goto_3

    .line 26345
    :cond_5
    int-to-long v2, v8

    add-long/2addr v2, v4

    int-to-long v0, v7

    sub-long/2addr v2, v0

    int-to-long v0, v12

    cmp-long v13, v2, v0

    if-ltz v13, :cond_6

    .line 26346
    const/4 v0, 0x0

    goto/16 :goto_6

    .line 26347
    :cond_6
    int-to-long v0, v7

    sub-long/2addr v4, v0

    long-to-int v2, v4

    .line 26348
    .local v6, "atomDataSize":I
    add-int/2addr v8, v2

    .line 26349
    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A0V:I

    if-ne v6, v0, :cond_c

    .line 26350
    const/16 v0, 0x8

    if-ge v2, v0, :cond_7

    .line 26351
    const/4 v0, 0x0

    return v0

    .line 26352
    :cond_7
    const/4 v1, 0x0

    invoke-virtual {v9, v2}, Lcom/facebook/ads/redexgen/X/Hz;->A0W(I)V

    .line 26353
    iget-object v0, v9, Lcom/facebook/ads/redexgen/X/Hz;->A00:[B

    invoke-interface {v10, v0, v1, v2}, Lcom/facebook/ads/redexgen/X/Bt;->ADv([BII)V

    .line 26354
    div-int/lit8 v4, v2, 0x4

    .line 26355
    .local v5, "brandsCount":I
    const/4 v3, 0x0

    .local v10, "i":I
    :goto_4
    if-ge v3, v4, :cond_b

    .line 26356
    const/4 v5, 0x1

    sget-object v1, Lcom/facebook/ads/redexgen/X/Cc;->A01:[Ljava/lang/String;

    const/4 v0, 0x3

    aget-object v0, v1, v0

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v1

    const/16 v0, 0x15

    if-eq v1, v0, :cond_9

    sget-object v2, Lcom/facebook/ads/redexgen/X/Cc;->A01:[Ljava/lang/String;

    const-string v1, "gohG7oAKJXd788dq"

    const/4 v0, 0x2

    aput-object v1, v2, v0

    const-string v1, "s0tItg43zYN2kAKL8Ko7hXkhv"

    const/4 v0, 0x4

    aput-object v1, v2, v0

    if-ne v3, v5, :cond_a

    .line 26357
    :goto_5
    const/4 v5, 0x4

    sget-object v1, Lcom/facebook/ads/redexgen/X/Cc;->A01:[Ljava/lang/String;

    const/4 v0, 0x7

    aget-object v0, v1, v0

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v1

    const/16 v0, 0xc

    if-eq v1, v0, :cond_11

    sget-object v2, Lcom/facebook/ads/redexgen/X/Cc;->A01:[Ljava/lang/String;

    const-string v1, "X5YHVW4uSpKOduvkf9RISQBbBxib"

    const/4 v0, 0x7

    aput-object v1, v2, v0

    invoke-virtual {v9, v5}, Lcom/facebook/ads/redexgen/X/Hz;->A0Z(I)V

    .line 26358
    :cond_8
    add-int/lit8 v3, v3, 0x1

    goto :goto_4

    :cond_9
    if-ne v3, v5, :cond_a

    goto :goto_5

    .line 26359
    :cond_a
    invoke-virtual {v9}, Lcom/facebook/ads/redexgen/X/Hz;->A08()I

    move-result v0

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/Cc;->A02(I)Z

    move-result v0

    if-eqz v0, :cond_8

    .line 26360
    const/4 v14, 0x1

    .line 26361
    .end local v10    # "i":I
    :cond_b
    if-nez v14, :cond_d

    .line 26362
    const/4 v0, 0x0

    return v0

    .line 26363
    :cond_c
    if-eqz v2, :cond_d

    .line 26364
    invoke-interface {v10, v2}, Lcom/facebook/ads/redexgen/X/Bt;->A3W(I)V

    .line 26365
    .end local v6    # "atomDataSize":I
    .end local v12    # "headerSize":I
    .end local v13    # "atomSize":J
    .end local v15    # "atomType":I
    :cond_d
    const-wide/16 v15, -0x1

    goto/16 :goto_1

    .line 26366
    :cond_e
    const-wide/16 v1, 0x0

    cmp-long v0, v4, v1

    if-nez v0, :cond_1

    .line 26367
    invoke-interface {v10}, Lcom/facebook/ads/redexgen/X/Bt;->A7I()J

    move-result-wide v2

    .line 26368
    .local p2, "endPosition":J
    cmp-long v0, v2, v15

    if-eqz v0, :cond_1

    .line 26369
    invoke-interface {v10}, Lcom/facebook/ads/redexgen/X/Bt;->A7i()J

    move-result-wide v0

    sub-long/2addr v2, v0

    int-to-long v0, v7

    add-long v4, v2, v0

    goto/16 :goto_2

    .line 26370
    :cond_f
    move-wide v1, v3

    goto/16 :goto_0

    :cond_10
    sget-object v2, Lcom/facebook/ads/redexgen/X/Cc;->A01:[Ljava/lang/String;

    const-string v1, "uC"

    const/4 v0, 0x6

    aput-object v1, v2, v0

    return v3

    :cond_11
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    :cond_12
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    .line 26371
    .end local v12
    .end local v13
    .end local v15
    :cond_13
    const/4 v0, 0x0

    .line 26372
    :goto_6
    if-eqz v14, :cond_14

    move/from16 v1, p1

    if-ne v1, v11, :cond_14

    const/4 v0, 0x1

    :cond_14
    return v0
.end method
