.class public interface abstract Lcom/facebook/ads/redexgen/X/Cl;
.super Ljava/lang/Object;
.source ""


# virtual methods
.method public abstract A4m()Lcom/facebook/ads/redexgen/X/C1;
.end method

.method public abstract AEI(Lcom/facebook/ads/redexgen/X/Bt;)J
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;,
            Ljava/lang/InterruptedException;
        }
    .end annotation
.end method

.method public abstract AGX(J)J
.end method
