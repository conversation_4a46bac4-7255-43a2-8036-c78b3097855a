.class public final Lcom/facebook/ads/redexgen/X/Cq;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/facebook/ads/redexgen/X/XC;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "VorbisSetup"
.end annotation


# instance fields
.field public final A00:I

.field public final A01:Lcom/facebook/ads/redexgen/X/Cs;

.field public final A02:Lcom/facebook/ads/redexgen/X/Cu;

.field public final A03:[B

.field public final A04:[Lcom/facebook/ads/redexgen/X/Ct;


# direct methods
.method public constructor <init>(Lcom/facebook/ads/redexgen/X/Cu;Lcom/facebook/ads/redexgen/X/Cs;[B[Lcom/facebook/ads/redexgen/X/Ct;I)V
    .locals 0

    .line 26786
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 26787
    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/Cq;->A02:Lcom/facebook/ads/redexgen/X/Cu;

    .line 26788
    iput-object p2, p0, Lcom/facebook/ads/redexgen/X/Cq;->A01:Lcom/facebook/ads/redexgen/X/Cs;

    .line 26789
    iput-object p3, p0, Lcom/facebook/ads/redexgen/X/Cq;->A03:[B

    .line 26790
    iput-object p4, p0, Lcom/facebook/ads/redexgen/X/Cq;->A04:[Lcom/facebook/ads/redexgen/X/Ct;

    .line 26791
    iput p5, p0, Lcom/facebook/ads/redexgen/X/Cq;->A00:I

    .line 26792
    return-void
.end method
