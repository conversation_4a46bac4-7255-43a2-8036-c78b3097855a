.class public final Lcom/facebook/ads/redexgen/X/Cu;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/facebook/ads/redexgen/X/Cv;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "VorbisIdHeader"
.end annotation


# instance fields
.field public final A00:I

.field public final A01:I

.field public final A02:I

.field public final A03:I

.field public final A04:I

.field public final A05:I

.field public final A06:J

.field public final A07:J

.field public final A08:Z

.field public final A09:[B


# direct methods
.method public constructor <init>(JIJIIIIIZ[B)V
    .locals 0

    .line 26811
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 26812
    iput-wide p1, p0, Lcom/facebook/ads/redexgen/X/Cu;->A07:J

    .line 26813
    iput p3, p0, Lcom/facebook/ads/redexgen/X/Cu;->A05:I

    .line 26814
    iput-wide p4, p0, Lcom/facebook/ads/redexgen/X/Cu;->A06:J

    .line 26815
    iput p6, p0, Lcom/facebook/ads/redexgen/X/Cu;->A00:I

    .line 26816
    iput p7, p0, Lcom/facebook/ads/redexgen/X/Cu;->A02:I

    .line 26817
    iput p8, p0, Lcom/facebook/ads/redexgen/X/Cu;->A01:I

    .line 26818
    iput p9, p0, Lcom/facebook/ads/redexgen/X/Cu;->A03:I

    .line 26819
    iput p10, p0, Lcom/facebook/ads/redexgen/X/Cu;->A04:I

    .line 26820
    iput-boolean p11, p0, Lcom/facebook/ads/redexgen/X/Cu;->A08:Z

    .line 26821
    iput-object p12, p0, Lcom/facebook/ads/redexgen/X/Cu;->A09:[B

    .line 26822
    return-void
.end method
