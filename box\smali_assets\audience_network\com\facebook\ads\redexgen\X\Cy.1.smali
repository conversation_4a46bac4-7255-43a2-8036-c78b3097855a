.class public interface abstract Lcom/facebook/ads/redexgen/X/Cy;
.super Ljava/lang/Object;
.source ""


# virtual methods
.method public abstract A4R(Lcom/facebook/ads/redexgen/X/Hz;)V
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lcom/facebook/ads/redexgen/X/9v;
        }
    .end annotation
.end method

.method public abstract A4p(Lcom/facebook/ads/redexgen/X/Bu;Lcom/facebook/ads/redexgen/X/DC;)V
.end method

.method public abstract ADs()V
.end method

.method public abstract ADt(JZ)V
.end method

.method public abstract AFg()V
.end method
