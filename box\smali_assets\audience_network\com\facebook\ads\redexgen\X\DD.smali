.class public interface abstract Lcom/facebook/ads/redexgen/X/DD;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/facebook/ads/redexgen/X/DC;,
        Lcom/facebook/ads/redexgen/X/D9;,
        Lcom/facebook/ads/redexgen/X/DA;,
        Lcom/facebook/ads/redexgen/X/DB;
    }
.end annotation


# virtual methods
.method public abstract A4S(Lcom/facebook/ads/redexgen/X/Hz;Z)V
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lcom/facebook/ads/redexgen/X/9v;
        }
    .end annotation
.end method

.method public abstract A8q(Lcom/facebook/ads/redexgen/X/IB;Lcom/facebook/ads/redexgen/X/Bu;Lcom/facebook/ads/redexgen/X/DC;)V
.end method

.method public abstract AFg()V
.end method
