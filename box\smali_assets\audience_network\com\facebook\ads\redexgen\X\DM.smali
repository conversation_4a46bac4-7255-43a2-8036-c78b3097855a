.class public interface abstract Lcom/facebook/ads/redexgen/X/DM;
.super Ljava/lang/Object;
.source ""


# static fields
.field public static final A00:Lcom/facebook/ads/redexgen/X/DM;


# direct methods
.method public static constructor <clinit>()V
    .locals 1

    .line 1193
    new-instance v0, Lcom/facebook/ads/redexgen/X/Wk;

    invoke-direct {v0}, Lcom/facebook/ads/redexgen/X/Wk;-><init>()V

    sput-object v0, Lcom/facebook/ads/redexgen/X/DM;->A00:Lcom/facebook/ads/redexgen/X/DM;

    return-void
.end method


# virtual methods
.method public abstract A6m(Ljava/lang/String;Z)Lcom/facebook/ads/redexgen/X/DG;
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lcom/facebook/ads/redexgen/X/DP;
        }
    .end annotation
.end method

.method public abstract A7c()Lcom/facebook/ads/redexgen/X/DG;
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lcom/facebook/ads/redexgen/X/DP;
        }
    .end annotation
.end method
