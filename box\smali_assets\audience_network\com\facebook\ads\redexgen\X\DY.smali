.class public interface abstract Lcom/facebook/ads/redexgen/X/DY;
.super Ljava/lang/Object;
.source ""


# static fields
.field public static final A00:Lcom/facebook/ads/redexgen/X/DY;


# direct methods
.method public static constructor <clinit>()V
    .locals 1

    .line 1250
    new-instance v0, Lcom/facebook/ads/redexgen/X/Wh;

    invoke-direct {v0}, Lcom/facebook/ads/redexgen/X/Wh;-><init>()V

    sput-object v0, Lcom/facebook/ads/redexgen/X/DY;->A00:Lcom/facebook/ads/redexgen/X/DY;

    return-void
.end method


# virtual methods
.method public abstract A4Y(Lcom/facebook/ads/internal/exoplayer2/thirdparty/Format;)Lcom/facebook/ads/redexgen/X/DW;
.end method

.method public abstract AGf(Lcom/facebook/ads/internal/exoplayer2/thirdparty/Format;)Z
.end method
