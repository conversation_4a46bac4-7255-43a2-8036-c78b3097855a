.class public interface abstract Lcom/facebook/ads/redexgen/X/EK;
.super Ljava/lang/Object;
.source ""


# virtual methods
.method public abstract A5D()V
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/InterruptedException;,
            Ljava/io/IOException;
        }
    .end annotation
.end method

.method public abstract A6o()F
.end method

.method public abstract A6p()J
.end method

.method public abstract cancel()V
.end method

.method public abstract remove()V
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/InterruptedException;
        }
    .end annotation
.end method
