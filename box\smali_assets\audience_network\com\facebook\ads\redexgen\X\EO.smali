.class public final Lcom/facebook/ads/redexgen/X/EO;
.super Lcom/facebook/ads/redexgen/X/YR;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/facebook/ads/redexgen/X/YL;->A6v(Lcom/facebook/ads/redexgen/X/7f;)Lcom/facebook/ads/redexgen/X/7i;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field public final synthetic A00:Lcom/facebook/ads/redexgen/X/YL;


# direct methods
.method public constructor <init>(Lcom/facebook/ads/redexgen/X/YL;Lcom/facebook/ads/redexgen/X/7f;)V
    .locals 0

    .line 30426
    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/EO;->A00:Lcom/facebook/ads/redexgen/X/YL;

    invoke-direct {p0, p2}, Lcom/facebook/ads/redexgen/X/YR;-><init>(Lcom/facebook/ads/redexgen/X/7f;)V

    return-void
.end method


# virtual methods
.method public final A8Y()Z
    .locals 1

    .line 30427
    invoke-static {}, Lcom/facebook/ads/redexgen/X/Jv;->A09()Z

    move-result v0

    return v0
.end method

.method public final AAL()V
    .locals 1

    .line 30428
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/YR;->A00:Lcom/facebook/ads/redexgen/X/7f;

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/5c;->A09(Landroid/content/Context;)Lcom/facebook/ads/redexgen/X/Ym;

    move-result-object v0

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/8Y;->A06(Lcom/facebook/ads/redexgen/X/Ym;)V

    .line 30429
    return-void
.end method

.method public final AAd()V
    .locals 1

    .line 30430
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/YR;->A00:Lcom/facebook/ads/redexgen/X/7f;

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/5c;->A09(Landroid/content/Context;)Lcom/facebook/ads/redexgen/X/Ym;

    move-result-object v0

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/8Y;->A07(Lcom/facebook/ads/redexgen/X/Ym;)V

    .line 30431
    return-void
.end method
