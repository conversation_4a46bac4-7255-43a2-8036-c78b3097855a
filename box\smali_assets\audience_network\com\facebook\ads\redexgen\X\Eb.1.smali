.class public Lcom/facebook/ads/redexgen/X/Eb;
.super Landroid/view/ViewGroup;
.source ""

# interfaces
.implements Lcom/facebook/ads/redexgen/X/3A;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/facebook/ads/redexgen/X/4f;,
        Lcom/facebook/ads/redexgen/X/4k;,
        Lcom/facebook/ads/redexgen/X/Zh;,
        Lcom/facebook/ads/redexgen/X/4r;,
        Lcom/facebook/ads/redexgen/X/53;,
        Lcom/facebook/ads/internal/androidx/support/v7/widget/RecyclerView$SavedState;,
        Lcom/facebook/ads/redexgen/X/4d;,
        Lcom/facebook/ads/redexgen/X/51;,
        Lcom/facebook/ads/redexgen/X/4e;,
        Lcom/facebook/ads/redexgen/X/4p;,
        Lcom/facebook/ads/redexgen/X/56;,
        Lcom/facebook/ads/internal/androidx/support/v7/widget/RecyclerView$OnChildAttachStateChangeListener;,
        Lcom/facebook/ads/redexgen/X/4x;,
        Lcom/facebook/ads/redexgen/X/4t;,
        Lcom/facebook/ads/internal/androidx/support/v7/widget/RecyclerView$SimpleOnItemTouchListener;,
        Lcom/facebook/ads/redexgen/X/4s;,
        Lcom/facebook/ads/internal/androidx/support/v7/widget/RecyclerView$ItemDecoration;,
        Lcom/facebook/ads/redexgen/X/4o;,
        Lcom/facebook/ads/redexgen/X/4c;,
        Lcom/facebook/ads/redexgen/X/54;,
        Lcom/facebook/ads/redexgen/X/4w;,
        Lcom/facebook/ads/redexgen/X/4v;,
        Lcom/facebook/ads/redexgen/X/Ze;,
        Lcom/facebook/ads/redexgen/X/55;
    }
.end annotation


# static fields
.field public static A18:[B

.field public static A19:[Ljava/lang/String;

.field public static final A1A:Landroid/view/animation/Interpolator;

.field public static final A1B:Z

.field public static final A1C:Z

.field public static final A1D:Z

.field public static final A1E:Z

.field public static final A1F:Z

.field public static final A1G:Z

.field public static final A1H:[I

.field public static final A1I:[I

.field public static final A1J:[Ljava/lang/Class;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "[",
            "Ljava/lang/Class<",
            "*>;"
        }
    .end annotation
.end field


# instance fields
.field public A00:Lcom/facebook/ads/redexgen/X/Zq;

.field public A01:Lcom/facebook/ads/redexgen/X/4C;

.field public A02:Lcom/facebook/ads/redexgen/X/Zp;

.field public A03:Lcom/facebook/ads/redexgen/X/4P;

.field public A04:Lcom/facebook/ads/redexgen/X/4c;

.field public A05:Lcom/facebook/ads/redexgen/X/4k;

.field public A06:Lcom/facebook/ads/redexgen/X/4o;

.field public A07:Lcom/facebook/ads/redexgen/X/4x;

.field public A08:Lcom/facebook/ads/redexgen/X/55;

.field public A09:Lcom/facebook/ads/redexgen/X/Zb;

.field public A0A:Z

.field public A0B:Z

.field public A0C:Z

.field public A0D:Z

.field public A0E:Z

.field public A0F:Z

.field public A0G:Z

.field public A0H:Z

.field public A0I:Z

.field public A0J:Z

.field public A0K:Z

.field public A0L:F

.field public A0M:F

.field public A0N:I

.field public A0O:I

.field public A0P:I

.field public A0Q:I

.field public A0R:I

.field public A0S:I

.field public A0T:I

.field public A0U:I

.field public A0V:I

.field public A0W:I

.field public A0X:I

.field public A0Y:Landroid/view/VelocityTracker;

.field public A0Z:Landroid/widget/EdgeEffect;

.field public A0a:Landroid/widget/EdgeEffect;

.field public A0b:Landroid/widget/EdgeEffect;

.field public A0c:Landroid/widget/EdgeEffect;

.field public A0d:Lcom/facebook/ads/redexgen/X/3B;

.field public A0e:Lcom/facebook/ads/redexgen/X/4f;

.field public A0f:Lcom/facebook/ads/redexgen/X/4i;

.field public A0g:Lcom/facebook/ads/redexgen/X/4r;

.field public A0h:Lcom/facebook/ads/redexgen/X/4s;

.field public A0i:Lcom/facebook/ads/redexgen/X/4t;

.field public A0j:Lcom/facebook/ads/internal/androidx/support/v7/widget/RecyclerView$SavedState;

.field public A0k:Ljava/lang/Runnable;

.field public A0l:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lcom/facebook/ads/internal/androidx/support/v7/widget/RecyclerView$OnChildAttachStateChangeListener;",
            ">;"
        }
    .end annotation
.end field

.field public A0m:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lcom/facebook/ads/redexgen/X/4t;",
            ">;"
        }
    .end annotation
.end field

.field public A0n:Z

.field public A0o:Z

.field public final A0p:Landroid/graphics/Rect;

.field public final A0q:Landroid/graphics/RectF;

.field public final A0r:Lcom/facebook/ads/redexgen/X/4w;

.field public final A0s:Lcom/facebook/ads/redexgen/X/53;

.field public final A0t:Lcom/facebook/ads/redexgen/X/5E;

.field public final A0u:Ljava/lang/Runnable;

.field public final A0v:Ljava/util/ArrayList;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/ArrayList<",
            "Lcom/facebook/ads/internal/androidx/support/v7/widget/RecyclerView$ItemDecoration;",
            ">;"
        }
    .end annotation
.end field

.field public final A0w:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lcom/facebook/ads/redexgen/X/56;",
            ">;"
        }
    .end annotation
.end field

.field public final A0x:I

.field public final A0y:I

.field public final A0z:Landroid/graphics/Rect;

.field public final A10:Landroid/view/accessibility/AccessibilityManager;

.field public final A11:Lcom/facebook/ads/redexgen/X/Ze;

.field public final A12:Lcom/facebook/ads/redexgen/X/5D;

.field public final A13:Ljava/util/ArrayList;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/ArrayList<",
            "Lcom/facebook/ads/redexgen/X/4s;",
            ">;"
        }
    .end annotation
.end field

.field public final A14:[I

.field public final A15:[I

.field public final A16:[I

.field public final A17:[I


# direct methods
.method public static constructor <clinit>()V
    .locals 4

    .line 1270
    const/16 v0, 0x8

    new-array v2, v0, [Ljava/lang/String;

    const/4 v1, 0x0

    const-string v0, "EzQ8zede2M"

    aput-object v0, v2, v1

    const/4 v1, 0x1

    const-string v0, "Jhjgeach8ubRdn89CHbftPIFCkhy8Cmz"

    aput-object v0, v2, v1

    const/4 v1, 0x2

    const-string v0, "AI0RoC"

    aput-object v0, v2, v1

    const/4 v1, 0x3

    const-string v0, "LwFgY3t3Qwi2PJUU36Pvm2"

    aput-object v0, v2, v1

    const/4 v1, 0x4

    const-string v0, "gOCwMU1oDfpLVyXTNaTi7eQdHjis00sx"

    aput-object v0, v2, v1

    const/4 v1, 0x5

    const-string v0, "GZYggCcl1OWg4VOzBYKk2kBvqjxmktta"

    aput-object v0, v2, v1

    const/4 v1, 0x6

    const-string v0, "Te26Sy6xLG0KtVPzJaOHws0Kts3F9NRb"

    aput-object v0, v2, v1

    const/4 v1, 0x7

    const-string v0, "K773CdyIAwEU"

    aput-object v0, v2, v1

    sput-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    invoke-static {}, Lcom/facebook/ads/redexgen/X/Eb;->A0W()V

    const v0, 0x1010436

    filled-new-array {v0}, [I

    move-result-object v0

    sput-object v0, Lcom/facebook/ads/redexgen/X/Eb;->A1I:[I

    .line 1271
    const v0, 0x10100eb

    filled-new-array {v0}, [I

    move-result-object v0

    sput-object v0, Lcom/facebook/ads/redexgen/X/Eb;->A1H:[I

    .line 1272
    sget v3, Landroid/os/Build$VERSION;->SDK_INT:I

    const/16 v2, 0x12

    const/4 v1, 0x0

    const/4 v0, 0x1

    if-eq v3, v2, :cond_0

    sget v3, Landroid/os/Build$VERSION;->SDK_INT:I

    const/16 v2, 0x13

    if-eq v3, v2, :cond_0

    sget v3, Landroid/os/Build$VERSION;->SDK_INT:I

    const/16 v2, 0x14

    if-ne v3, v2, :cond_6

    :cond_0
    const/4 v2, 0x1

    :goto_0
    sput-boolean v2, Lcom/facebook/ads/redexgen/X/Eb;->A1C:Z

    .line 1273
    sget v3, Landroid/os/Build$VERSION;->SDK_INT:I

    const/16 v2, 0x17

    if-lt v3, v2, :cond_5

    const/4 v2, 0x1

    :goto_1
    sput-boolean v2, Lcom/facebook/ads/redexgen/X/Eb;->A1B:Z

    .line 1274
    sget v3, Landroid/os/Build$VERSION;->SDK_INT:I

    const/16 v2, 0x10

    if-lt v3, v2, :cond_4

    const/4 v2, 0x1

    :goto_2
    sput-boolean v2, Lcom/facebook/ads/redexgen/X/Eb;->A1D:Z

    .line 1275
    sget v3, Landroid/os/Build$VERSION;->SDK_INT:I

    const/16 v2, 0x15

    if-lt v3, v2, :cond_3

    const/4 v2, 0x1

    :goto_3
    sput-boolean v2, Lcom/facebook/ads/redexgen/X/Eb;->A1E:Z

    .line 1276
    sget v2, Landroid/os/Build$VERSION;->SDK_INT:I

    const/16 v3, 0xf

    if-gt v2, v3, :cond_2

    const/4 v2, 0x1

    :goto_4
    sput-boolean v2, Lcom/facebook/ads/redexgen/X/Eb;->A1F:Z

    .line 1277
    sget v2, Landroid/os/Build$VERSION;->SDK_INT:I

    if-gt v2, v3, :cond_1

    const/4 v2, 0x1

    :goto_5
    sput-boolean v2, Lcom/facebook/ads/redexgen/X/Eb;->A1G:Z

    .line 1278
    const/4 v2, 0x4

    new-array v3, v2, [Ljava/lang/Class;

    const-class v2, Landroid/content/Context;

    aput-object v2, v3, v1

    const-class v1, Landroid/util/AttributeSet;

    aput-object v1, v3, v0

    const/4 v1, 0x2

    sget-object v0, Ljava/lang/Integer;->TYPE:Ljava/lang/Class;

    aput-object v0, v3, v1

    const/4 v1, 0x3

    sget-object v0, Ljava/lang/Integer;->TYPE:Ljava/lang/Class;

    aput-object v0, v3, v1

    sput-object v3, Lcom/facebook/ads/redexgen/X/Eb;->A1J:[Ljava/lang/Class;

    .line 1279
    new-instance v0, Lcom/facebook/ads/redexgen/X/4b;

    invoke-direct {v0}, Lcom/facebook/ads/redexgen/X/4b;-><init>()V

    sput-object v0, Lcom/facebook/ads/redexgen/X/Eb;->A1A:Landroid/view/animation/Interpolator;

    return-void

    .line 1280
    :cond_1
    const/4 v2, 0x0

    goto :goto_5

    .line 1281
    :cond_2
    const/4 v2, 0x0

    goto :goto_4

    .line 1282
    :cond_3
    const/4 v2, 0x0

    goto :goto_3

    .line 1283
    :cond_4
    const/4 v2, 0x0

    goto :goto_2

    .line 1284
    :cond_5
    const/4 v2, 0x0

    goto :goto_1

    .line 1285
    :cond_6
    const/4 v2, 0x0

    goto :goto_0
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 1

    .line 30575
    const/4 v0, 0x0

    invoke-direct {p0, p1, v0}, Lcom/facebook/ads/redexgen/X/Eb;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;)V

    .line 30576
    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 1

    .line 30577
    const/4 v0, 0x0

    invoke-direct {p0, p1, p2, v0}, Lcom/facebook/ads/redexgen/X/Eb;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    .line 30578
    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V
    .locals 5

    .line 30579
    invoke-direct {p0, p1, p2, p3}, Landroid/view/ViewGroup;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    .line 30580
    new-instance v0, Lcom/facebook/ads/redexgen/X/Ze;

    invoke-direct {v0, p0}, Lcom/facebook/ads/redexgen/X/Ze;-><init>(Lcom/facebook/ads/redexgen/X/Eb;)V

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A11:Lcom/facebook/ads/redexgen/X/Ze;

    .line 30581
    new-instance v0, Lcom/facebook/ads/redexgen/X/4w;

    invoke-direct {v0, p0}, Lcom/facebook/ads/redexgen/X/4w;-><init>(Lcom/facebook/ads/redexgen/X/Eb;)V

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0r:Lcom/facebook/ads/redexgen/X/4w;

    .line 30582
    new-instance v0, Lcom/facebook/ads/redexgen/X/5E;

    invoke-direct {v0}, Lcom/facebook/ads/redexgen/X/5E;-><init>()V

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0t:Lcom/facebook/ads/redexgen/X/5E;

    .line 30583
    new-instance v0, Lcom/facebook/ads/redexgen/X/4Z;

    invoke-direct {v0, p0}, Lcom/facebook/ads/redexgen/X/4Z;-><init>(Lcom/facebook/ads/redexgen/X/Eb;)V

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0u:Ljava/lang/Runnable;

    .line 30584
    new-instance v0, Landroid/graphics/Rect;

    invoke-direct {v0}, Landroid/graphics/Rect;-><init>()V

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0p:Landroid/graphics/Rect;

    .line 30585
    new-instance v0, Landroid/graphics/Rect;

    invoke-direct {v0}, Landroid/graphics/Rect;-><init>()V

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0z:Landroid/graphics/Rect;

    .line 30586
    new-instance v0, Landroid/graphics/RectF;

    invoke-direct {v0}, Landroid/graphics/RectF;-><init>()V

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0q:Landroid/graphics/RectF;

    .line 30587
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0v:Ljava/util/ArrayList;

    .line 30588
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A13:Ljava/util/ArrayList;

    .line 30589
    const/4 v4, 0x0

    iput v4, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0O:I

    .line 30590
    iput-boolean v4, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0C:Z

    .line 30591
    iput v4, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0U:I

    .line 30592
    iput v4, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0N:I

    .line 30593
    new-instance v0, Lcom/facebook/ads/redexgen/X/Ee;

    invoke-direct {v0}, Lcom/facebook/ads/redexgen/X/Ee;-><init>()V

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A05:Lcom/facebook/ads/redexgen/X/4k;

    .line 30594
    iput v4, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0W:I

    .line 30595
    const/4 v0, -0x1

    iput v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0V:I

    .line 30596
    const/4 v0, 0x1

    iput v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0L:F

    .line 30597
    iput v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0M:F

    .line 30598
    const/4 v2, 0x1

    iput-boolean v2, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0o:Z

    .line 30599
    new-instance v0, Lcom/facebook/ads/redexgen/X/55;

    invoke-direct {v0, p0}, Lcom/facebook/ads/redexgen/X/55;-><init>(Lcom/facebook/ads/redexgen/X/Eb;)V

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A08:Lcom/facebook/ads/redexgen/X/55;

    .line 30600
    sget-boolean v0, Lcom/facebook/ads/redexgen/X/Eb;->A1E:Z

    if-eqz v0, :cond_3

    new-instance v0, Lcom/facebook/ads/redexgen/X/Zp;

    invoke-direct {v0}, Lcom/facebook/ads/redexgen/X/Zp;-><init>()V

    :goto_0
    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A02:Lcom/facebook/ads/redexgen/X/Zp;

    .line 30601
    new-instance v0, Lcom/facebook/ads/redexgen/X/53;

    invoke-direct {v0}, Lcom/facebook/ads/redexgen/X/53;-><init>()V

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0s:Lcom/facebook/ads/redexgen/X/53;

    .line 30602
    iput-boolean v4, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0G:Z

    .line 30603
    iput-boolean v4, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0H:Z

    .line 30604
    new-instance v0, Lcom/facebook/ads/redexgen/X/Zh;

    invoke-direct {v0, p0}, Lcom/facebook/ads/redexgen/X/Zh;-><init>(Lcom/facebook/ads/redexgen/X/Eb;)V

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0f:Lcom/facebook/ads/redexgen/X/4i;

    .line 30605
    iput-boolean v4, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0K:Z

    .line 30606
    const/4 v3, 0x2

    new-array v0, v3, [I

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A14:[I

    .line 30607
    new-array v0, v3, [I

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A17:[I

    .line 30608
    new-array v0, v3, [I

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A16:[I

    .line 30609
    new-array v0, v3, [I

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A15:[I

    .line 30610
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0w:Ljava/util/List;

    .line 30611
    new-instance v0, Lcom/facebook/ads/redexgen/X/4a;

    invoke-direct {v0, p0}, Lcom/facebook/ads/redexgen/X/4a;-><init>(Lcom/facebook/ads/redexgen/X/Eb;)V

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0k:Ljava/lang/Runnable;

    .line 30612
    new-instance v0, Lcom/facebook/ads/redexgen/X/Zk;

    invoke-direct {v0, p0}, Lcom/facebook/ads/redexgen/X/Zk;-><init>(Lcom/facebook/ads/redexgen/X/Eb;)V

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A12:Lcom/facebook/ads/redexgen/X/5D;

    .line 30613
    if-eqz p2, :cond_2

    .line 30614
    sget-object v0, Lcom/facebook/ads/redexgen/X/Eb;->A1H:[I

    invoke-virtual {p1, p2, v0, p3, v4}, Landroid/content/Context;->obtainStyledAttributes(Landroid/util/AttributeSet;[III)Landroid/content/res/TypedArray;

    move-result-object v1

    .line 30615
    .local v3, "a":Landroid/content/res/TypedArray;
    invoke-virtual {v1, v4, v2}, Landroid/content/res/TypedArray;->getBoolean(IZ)Z

    move-result v0

    iput-boolean v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0B:Z

    .line 30616
    invoke-virtual {v1}, Landroid/content/res/TypedArray;->recycle()V

    .line 30617
    .end local v3    # "a":Landroid/content/res/TypedArray;
    :goto_1
    invoke-virtual {p0, v2}, Lcom/facebook/ads/redexgen/X/Eb;->setScrollContainer(Z)V

    .line 30618
    invoke-virtual {p0, v2}, Lcom/facebook/ads/redexgen/X/Eb;->setFocusableInTouchMode(Z)V

    .line 30619
    invoke-static {p1}, Landroid/view/ViewConfiguration;->get(Landroid/content/Context;)Landroid/view/ViewConfiguration;

    move-result-object v1

    .line 30620
    .local v3, "vc":Landroid/view/ViewConfiguration;
    invoke-virtual {v1}, Landroid/view/ViewConfiguration;->getScaledTouchSlop()I

    move-result v0

    iput v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0X:I

    .line 30621
    invoke-static {v1, p1}, Lcom/facebook/ads/redexgen/X/3U;->A00(Landroid/view/ViewConfiguration;Landroid/content/Context;)F

    move-result v0

    iput v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0L:F

    .line 30622
    invoke-static {v1, p1}, Lcom/facebook/ads/redexgen/X/3U;->A01(Landroid/view/ViewConfiguration;Landroid/content/Context;)F

    move-result v0

    iput v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0M:F

    .line 30623
    invoke-virtual {v1}, Landroid/view/ViewConfiguration;->getScaledMinimumFlingVelocity()I

    move-result v0

    iput v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0y:I

    .line 30624
    invoke-virtual {v1}, Landroid/view/ViewConfiguration;->getScaledMaximumFlingVelocity()I

    move-result v0

    iput v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0x:I

    .line 30625
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->getOverScrollMode()I

    move-result v0

    if-ne v0, v3, :cond_0

    const/4 v4, 0x1

    :cond_0
    invoke-virtual {p0, v4}, Lcom/facebook/ads/redexgen/X/Eb;->setWillNotDraw(Z)V

    .line 30626
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/Eb;->A05:Lcom/facebook/ads/redexgen/X/4k;

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0f:Lcom/facebook/ads/redexgen/X/4i;

    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/4k;->A0B(Lcom/facebook/ads/redexgen/X/4i;)V

    .line 30627
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/Eb;->A0e()V

    .line 30628
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/Eb;->A0O()V

    .line 30629
    invoke-static {p0}, Lcom/facebook/ads/redexgen/X/3T;->A00(Landroid/view/View;)I

    move-result v0

    if-nez v0, :cond_1

    .line 30630
    invoke-static {p0, v2}, Lcom/facebook/ads/redexgen/X/3T;->A09(Landroid/view/View;I)V

    .line 30631
    :cond_1
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->getContext()Landroid/content/Context;

    move-result-object v3

    .line 30632
    const/16 v2, 0x727

    const/16 v1, 0xd

    const/16 v0, 0x51

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Eb;->A0I(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v3, v0}, Landroid/content/Context;->getSystemService(Ljava/lang/String;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroid/view/accessibility/AccessibilityManager;

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A10:Landroid/view/accessibility/AccessibilityManager;

    .line 30633
    new-instance v0, Lcom/facebook/ads/redexgen/X/Zb;

    invoke-direct {v0, p0}, Lcom/facebook/ads/redexgen/X/Zb;-><init>(Lcom/facebook/ads/redexgen/X/Eb;)V

    invoke-virtual {p0, v0}, Lcom/facebook/ads/redexgen/X/Eb;->setAccessibilityDelegateCompat(Lcom/facebook/ads/redexgen/X/Zb;)V

    .line 30634
    const/4 v1, 0x1

    .line 30635
    .local v0, "nestedScrollingEnabled":Z
    const/high16 v0, 0x40000

    invoke-virtual {p0, v0}, Lcom/facebook/ads/redexgen/X/Eb;->setDescendantFocusability(I)V

    .line 30636
    invoke-virtual {p0, v1}, Lcom/facebook/ads/redexgen/X/Eb;->setNestedScrollingEnabled(Z)V

    .line 30637
    return-void

    .line 30638
    :cond_2
    iput-boolean v2, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0B:Z

    goto :goto_1

    .line 30639
    :cond_3
    const/4 v0, 0x0

    goto/16 :goto_0
.end method

.method private A0B(Landroid/view/View;)I
    .locals 3

    .line 30640
    invoke-virtual {p1}, Landroid/view/View;->getId()I

    move-result v2

    .line 30641
    .local v0, "lastKnownId":I
    :cond_0
    :goto_0
    invoke-virtual {p1}, Landroid/view/View;->isFocused()Z

    move-result v0

    if-nez v0, :cond_1

    instance-of v0, p1, Landroid/view/ViewGroup;

    if-eqz v0, :cond_1

    invoke-virtual {p1}, Landroid/view/View;->hasFocus()Z

    move-result v0

    if-eqz v0, :cond_1

    .line 30642
    check-cast p1, Landroid/view/ViewGroup;

    invoke-virtual {p1}, Landroid/view/ViewGroup;->getFocusedChild()Landroid/view/View;

    move-result-object p1

    .line 30643
    invoke-virtual {p1}, Landroid/view/View;->getId()I

    move-result v1

    .line 30644
    .local v1, "id":I
    const/4 v0, -0x1

    if-eq v1, v0, :cond_0

    .line 30645
    invoke-virtual {p1}, Landroid/view/View;->getId()I

    move-result v2

    goto :goto_0

    .line 30646
    :cond_1
    return v2
.end method

.method private final A0C(Lcom/facebook/ads/redexgen/X/56;)J
    .locals 2

    .line 30647
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A04:Lcom/facebook/ads/redexgen/X/4c;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/4c;->A0A()Z

    move-result v0

    if-eqz v0, :cond_0

    invoke-virtual {p1}, Lcom/facebook/ads/redexgen/X/56;->A0K()J

    move-result-wide v0

    :goto_0
    return-wide v0

    :cond_0
    iget v0, p1, Lcom/facebook/ads/redexgen/X/56;->A03:I

    int-to-long v0, v0

    goto :goto_0
.end method

.method private A0D()Landroid/view/View;
    .locals 5

    .line 30648
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0s:Lcom/facebook/ads/redexgen/X/53;

    iget v1, v0, Lcom/facebook/ads/redexgen/X/53;->A01:I

    const/4 v0, -0x1

    if-eq v1, v0, :cond_5

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0s:Lcom/facebook/ads/redexgen/X/53;

    iget v4, v0, Lcom/facebook/ads/redexgen/X/53;->A01:I

    .line 30649
    .local v0, "startFocusSearchIndex":I
    :goto_0
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0s:Lcom/facebook/ads/redexgen/X/53;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/53;->A03()I

    move-result v3

    .line 30650
    .local v1, "itemCount":I
    move v2, v4

    .local v2, "i":I
    :goto_1
    if-ge v2, v3, :cond_0

    .line 30651
    invoke-virtual {p0, v2}, Lcom/facebook/ads/redexgen/X/Eb;->A1F(I)Lcom/facebook/ads/redexgen/X/56;

    move-result-object v1

    .line 30652
    .local v3, "nextFocus":Lcom/facebook/ads/redexgen/X/56;
    if-nez v1, :cond_3

    .line 30653
    .end local v2    # "i":I
    .end local v3    # "nextFocus":Lcom/facebook/ads/redexgen/X/56;
    :cond_0
    invoke-static {v3, v4}, Ljava/lang/Math;->min(II)I

    move-result v0

    .line 30654
    .local v2, "limit":I
    add-int/lit8 v2, v0, -0x1

    .local v3, "i":I
    :goto_2
    const/4 v0, 0x0

    if-ltz v2, :cond_6

    .line 30655
    invoke-virtual {p0, v2}, Lcom/facebook/ads/redexgen/X/Eb;->A1F(I)Lcom/facebook/ads/redexgen/X/56;

    move-result-object v1

    .line 30656
    .local p0, "nextFocus":Lcom/facebook/ads/redexgen/X/56;
    if-nez v1, :cond_1

    .line 30657
    return-object v0

    .line 30658
    :cond_1
    iget-object v0, v1, Lcom/facebook/ads/redexgen/X/56;->A0H:Landroid/view/View;

    invoke-virtual {v0}, Landroid/view/View;->hasFocusable()Z

    move-result v0

    if-eqz v0, :cond_2

    .line 30659
    iget-object v0, v1, Lcom/facebook/ads/redexgen/X/56;->A0H:Landroid/view/View;

    return-object v0

    .line 30660
    :cond_2
    add-int/lit8 v2, v2, -0x1

    goto :goto_2

    .line 30661
    :cond_3
    iget-object v0, v1, Lcom/facebook/ads/redexgen/X/56;->A0H:Landroid/view/View;

    invoke-virtual {v0}, Landroid/view/View;->hasFocusable()Z

    move-result v0

    if-eqz v0, :cond_4

    .line 30662
    iget-object v0, v1, Lcom/facebook/ads/redexgen/X/56;->A0H:Landroid/view/View;

    return-object v0

    .line 30663
    :cond_4
    add-int/lit8 v2, v2, 0x1

    goto :goto_1

    .line 30664
    :cond_5
    const/4 v4, 0x0

    goto :goto_0

    .line 30665
    .end local v3    # "i":I
    .end local p0    # "nextFocus":Lcom/facebook/ads/redexgen/X/56;
    :cond_6
    return-object v0
.end method

.method private final A0E(J)Lcom/facebook/ads/redexgen/X/56;
    .locals 9

    .line 30666
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A04:Lcom/facebook/ads/redexgen/X/4c;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/4c;->A0A()Z

    move-result v0

    if-nez v0, :cond_1

    .line 30667
    .end local v0
    .end local v1
    :cond_0
    const/4 v0, 0x0

    return-object v0

    .line 30668
    :cond_1
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A01:Lcom/facebook/ads/redexgen/X/4C;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/4C;->A06()I

    move-result v5

    .line 30669
    .local v0, "childCount":I
    const/4 v8, 0x0

    .line 30670
    .local v1, "hidden":Lcom/facebook/ads/redexgen/X/56;
    const/4 v4, 0x0

    .local v2, "i":I
    :goto_0
    if-ge v4, v5, :cond_5

    .line 30671
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A01:Lcom/facebook/ads/redexgen/X/4C;

    invoke-virtual {v0, v4}, Lcom/facebook/ads/redexgen/X/4C;->A0A(I)Landroid/view/View;

    move-result-object v0

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/Eb;->A0F(Landroid/view/View;)Lcom/facebook/ads/redexgen/X/56;

    move-result-object v3

    .line 30672
    .local v3, "holder":Lcom/facebook/ads/redexgen/X/56;
    if-eqz v3, :cond_2

    invoke-virtual {v3}, Lcom/facebook/ads/redexgen/X/56;->A0a()Z

    move-result v0

    if-nez v0, :cond_2

    invoke-virtual {v3}, Lcom/facebook/ads/redexgen/X/56;->A0K()J

    move-result-wide v6

    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const/4 v0, 0x3

    aget-object v1, v2, v0

    const/4 v0, 0x0

    aget-object v0, v2, v0

    invoke-virtual {v1}, Ljava/lang/String;->length()I

    move-result v1

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v0

    if-eq v1, v0, :cond_4

    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const-string v1, "zZZE93"

    const/4 v0, 0x2

    aput-object v1, v2, v0

    const-string v1, "VhF552wVYFMR"

    const/4 v0, 0x7

    aput-object v1, v2, v0

    cmp-long v0, v6, p1

    if-nez v0, :cond_2

    .line 30673
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/Eb;->A01:Lcom/facebook/ads/redexgen/X/4C;

    iget-object v0, v3, Lcom/facebook/ads/redexgen/X/56;->A0H:Landroid/view/View;

    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/4C;->A0K(Landroid/view/View;)Z

    move-result v0

    if-eqz v0, :cond_3

    .line 30674
    move-object v8, v3

    .line 30675
    .end local v3    # "holder":Lcom/facebook/ads/redexgen/X/56;
    :cond_2
    add-int/lit8 v4, v4, 0x1

    goto :goto_0

    .line 30676
    :cond_3
    return-object v3

    :cond_4
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    .line 30677
    .end local v2    # "i":I
    :cond_5
    return-object v8
.end method

.method public static A0F(Landroid/view/View;)Lcom/facebook/ads/redexgen/X/56;
    .locals 3

    .line 30678
    if-nez p0, :cond_0

    .line 30679
    const/4 v0, 0x0

    return-object v0

    .line 30680
    :cond_0
    invoke-virtual {p0}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    move-result-object p0

    sget-object v1, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const/4 v0, 0x1

    aget-object v0, v1, v0

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v1

    const/16 v0, 0x20

    if-eq v1, v0, :cond_1

    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    :cond_1
    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const-string v1, "BAOGG7B6SgQWpjs0haC4kmkQlPV7A4k9"

    const/4 v0, 0x6

    aput-object v1, v2, v0

    const-string v1, "PAhaNRNZ3VZ1U75Bha3wwWQKZTmx8jYI"

    const/4 v0, 0x4

    aput-object v1, v2, v0

    check-cast p0, Lcom/facebook/ads/redexgen/X/4p;

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/4p;->A00:Lcom/facebook/ads/redexgen/X/56;

    return-object v0
.end method

.method private final A0G(Landroid/view/View;)Lcom/facebook/ads/redexgen/X/56;
    .locals 1

    .line 30681
    invoke-virtual {p0, p1}, Lcom/facebook/ads/redexgen/X/Eb;->A1E(Landroid/view/View;)Landroid/view/View;

    move-result-object v0

    .line 30682
    .local v0, "itemView":Landroid/view/View;
    if-nez v0, :cond_0

    const/4 v0, 0x0

    :goto_0
    return-object v0

    :cond_0
    invoke-virtual {p0, v0}, Lcom/facebook/ads/redexgen/X/Eb;->A1G(Landroid/view/View;)Lcom/facebook/ads/redexgen/X/56;

    move-result-object v0

    goto :goto_0
.end method

.method public static A0H(Landroid/view/View;)Lcom/facebook/ads/redexgen/X/Eb;
    .locals 5

    .line 30683
    instance-of v0, p0, Landroid/view/ViewGroup;

    const/4 v4, 0x0

    if-nez v0, :cond_0

    .line 30684
    return-object v4

    .line 30685
    :cond_0
    instance-of v3, p0, Lcom/facebook/ads/redexgen/X/Eb;

    sget-object v1, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const/4 v0, 0x5

    aget-object v1, v1, v0

    const/4 v0, 0x2

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    const/16 v0, 0x59

    if-eq v1, v0, :cond_1

    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    :cond_1
    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const-string v1, "St3OXKQiThZ0lO5vTnfaG6NXMltPJG5W"

    const/4 v0, 0x1

    aput-object v1, v2, v0

    if-eqz v3, :cond_2

    .line 30686
    check-cast p0, Lcom/facebook/ads/redexgen/X/Eb;

    return-object p0

    .line 30687
    :cond_2
    check-cast p0, Landroid/view/ViewGroup;

    .line 30688
    .local v0, "parent":Landroid/view/ViewGroup;
    invoke-virtual {p0}, Landroid/view/ViewGroup;->getChildCount()I

    move-result v2

    .line 30689
    .local v2, "count":I
    const/4 v1, 0x0

    .local v3, "i":I
    :goto_0
    if-ge v1, v2, :cond_4

    .line 30690
    invoke-virtual {p0, v1}, Landroid/view/ViewGroup;->getChildAt(I)Landroid/view/View;

    move-result-object v0

    .line 30691
    .local v4, "child":Landroid/view/View;
    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/Eb;->A0H(Landroid/view/View;)Lcom/facebook/ads/redexgen/X/Eb;

    move-result-object v0

    .line 30692
    .local p0, "descendant":Lcom/facebook/ads/redexgen/X/Eb;
    if-eqz v0, :cond_3

    .line 30693
    return-object v0

    .line 30694
    .end local v4    # "child":Landroid/view/View;
    .end local p0    # "descendant":Lcom/facebook/ads/redexgen/X/Eb;
    :cond_3
    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    .line 30695
    .end local v3    # "i":I
    :cond_4
    return-object v4
.end method

.method public static A0I(III)Ljava/lang/String;
    .locals 2

    sget-object v1, Lcom/facebook/ads/redexgen/X/Eb;->A18:[B

    add-int v0, p0, p1

    invoke-static {v1, p0, v0}, Ljava/util/Arrays;->copyOfRange([BII)[B

    move-result-object p0

    const/4 v1, 0x0

    :goto_0
    array-length v0, p0

    if-ge v1, v0, :cond_0

    aget-byte v0, p0, v1

    sub-int/2addr v0, p2

    add-int/lit8 v0, v0, -0x55

    int-to-byte v0, v0

    aput-byte v0, p0, v1

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_0
    new-instance v0, Ljava/lang/String;

    invoke-direct {v0, p0}, Ljava/lang/String;-><init>([B)V

    return-object v0
.end method

.method private A0J()V
    .locals 1

    .line 30696
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/Eb;->A0T()V

    .line 30697
    const/4 v0, 0x0

    invoke-virtual {p0, v0}, Lcom/facebook/ads/redexgen/X/Eb;->setScrollState(I)V

    .line 30698
    return-void
.end method

.method private A0K()V
    .locals 5

    .line 30699
    iget v3, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0P:I

    .line 30700
    .local v0, "flags":I
    const/4 v0, 0x0

    iput v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0P:I

    .line 30701
    if-eqz v3, :cond_0

    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->A1o()Z

    move-result v4

    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const/4 v0, 0x2

    aget-object v1, v2, v0

    const/4 v0, 0x7

    aget-object v0, v2, v0

    invoke-virtual {v1}, Ljava/lang/String;->length()I

    move-result v1

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v0

    if-eq v1, v0, :cond_1

    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const-string v1, "eU2cwXbbXfV7oR8qPBCYPw"

    const/4 v0, 0x3

    aput-object v1, v2, v0

    const-string v1, "cj1lHsGs8Y"

    const/4 v0, 0x0

    aput-object v1, v2, v0

    if-eqz v4, :cond_0

    .line 30702
    invoke-static {}, Landroid/view/accessibility/AccessibilityEvent;->obtain()Landroid/view/accessibility/AccessibilityEvent;

    move-result-object v1

    .line 30703
    .local v1, "event":Landroid/view/accessibility/AccessibilityEvent;
    const/16 v0, 0x800

    invoke-virtual {v1, v0}, Landroid/view/accessibility/AccessibilityEvent;->setEventType(I)V

    .line 30704
    invoke-static {v1, v3}, Lcom/facebook/ads/redexgen/X/3m;->A01(Landroid/view/accessibility/AccessibilityEvent;I)V

    .line 30705
    invoke-virtual {p0, v1}, Lcom/facebook/ads/redexgen/X/Eb;->sendAccessibilityEventUnchecked(Landroid/view/accessibility/AccessibilityEvent;)V

    .line 30706
    .end local v1    # "event":Landroid/view/accessibility/AccessibilityEvent;
    :cond_0
    return-void

    :cond_1
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0
.end method

.method private A0L()V
    .locals 10

    .line 30707
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0s:Lcom/facebook/ads/redexgen/X/53;

    const/4 v2, 0x1

    invoke-virtual {v0, v2}, Lcom/facebook/ads/redexgen/X/53;->A04(I)V

    .line 30708
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0s:Lcom/facebook/ads/redexgen/X/53;

    invoke-virtual {p0, v0}, Lcom/facebook/ads/redexgen/X/Eb;->A1h(Lcom/facebook/ads/redexgen/X/53;)V

    .line 30709
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0s:Lcom/facebook/ads/redexgen/X/53;

    const/4 v3, 0x0

    iput-boolean v3, v0, Lcom/facebook/ads/redexgen/X/53;->A0A:Z

    .line 30710
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->A1J()V

    .line 30711
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0t:Lcom/facebook/ads/redexgen/X/5E;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/5E;->A06()V

    .line 30712
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->A1K()V

    .line 30713
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/Eb;->A0P()V

    .line 30714
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/Eb;->A0U()V

    .line 30715
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0s:Lcom/facebook/ads/redexgen/X/53;

    iget-boolean v0, v1, Lcom/facebook/ads/redexgen/X/53;->A0C:Z

    if-eqz v0, :cond_5

    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0H:Z

    if-eqz v0, :cond_5

    :goto_0
    iput-boolean v2, v1, Lcom/facebook/ads/redexgen/X/53;->A0E:Z

    .line 30716
    iput-boolean v3, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0H:Z

    iput-boolean v3, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0G:Z

    .line 30717
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0s:Lcom/facebook/ads/redexgen/X/53;

    iget-boolean v0, v1, Lcom/facebook/ads/redexgen/X/53;->A0B:Z

    iput-boolean v0, v1, Lcom/facebook/ads/redexgen/X/53;->A09:Z

    .line 30718
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0s:Lcom/facebook/ads/redexgen/X/53;

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A04:Lcom/facebook/ads/redexgen/X/4c;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/4c;->A0E()I

    move-result v0

    iput v0, v1, Lcom/facebook/ads/redexgen/X/53;->A03:I

    .line 30719
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A14:[I

    invoke-direct {p0, v0}, Lcom/facebook/ads/redexgen/X/Eb;->A0x([I)V

    .line 30720
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0s:Lcom/facebook/ads/redexgen/X/53;

    iget-boolean v0, v0, Lcom/facebook/ads/redexgen/X/53;->A0C:Z

    if-eqz v0, :cond_6

    .line 30721
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A01:Lcom/facebook/ads/redexgen/X/4C;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/4C;->A05()I

    move-result v8

    .line 30722
    .local v0, "count":I
    const/4 v7, 0x0

    .local v1, "i":I
    :goto_1
    if-ge v7, v8, :cond_6

    .line 30723
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A01:Lcom/facebook/ads/redexgen/X/4C;

    invoke-virtual {v0, v7}, Lcom/facebook/ads/redexgen/X/4C;->A09(I)Landroid/view/View;

    move-result-object v0

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/Eb;->A0F(Landroid/view/View;)Lcom/facebook/ads/redexgen/X/56;

    move-result-object v6

    .line 30724
    .local v3, "holder":Lcom/facebook/ads/redexgen/X/56;
    invoke-virtual {v6}, Lcom/facebook/ads/redexgen/X/56;->A0f()Z

    move-result v4

    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const/4 v0, 0x2

    aget-object v1, v2, v0

    const/4 v0, 0x7

    aget-object v0, v2, v0

    invoke-virtual {v1}, Ljava/lang/String;->length()I

    move-result v1

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v0

    if-eq v1, v0, :cond_3

    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const-string v1, "UNYHKTM9Pgc2Q8zoabZHHBhpghLQMYt4"

    const/4 v0, 0x5

    aput-object v1, v2, v0

    if-nez v4, :cond_0

    invoke-virtual {v6}, Lcom/facebook/ads/redexgen/X/56;->A0Z()Z

    move-result v4

    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const/4 v0, 0x2

    aget-object v1, v2, v0

    const/4 v0, 0x7

    aget-object v0, v2, v0

    invoke-virtual {v1}, Ljava/lang/String;->length()I

    move-result v1

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v0

    if-eq v1, v0, :cond_3

    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const-string v1, "yAYMfY7gQPsOXL0bw1Y8FO5LYSWRNpHN"

    const/4 v0, 0x5

    aput-object v1, v2, v0

    if-eqz v4, :cond_2

    iget-object v4, p0, Lcom/facebook/ads/redexgen/X/Eb;->A04:Lcom/facebook/ads/redexgen/X/4c;

    sget-object v1, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const/4 v0, 0x5

    aget-object v1, v1, v0

    const/4 v0, 0x2

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    const/16 v0, 0x59

    if-eq v1, v0, :cond_1

    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const-string v1, "S1msfb"

    const/4 v0, 0x2

    aput-object v1, v2, v0

    const-string v1, "DqMDNTpz3t0S"

    const/4 v0, 0x7

    aput-object v1, v2, v0

    invoke-virtual {v4}, Lcom/facebook/ads/redexgen/X/4c;->A0A()Z

    move-result v0

    if-nez v0, :cond_2

    .line 30725
    .end local v3    # "holder":Lcom/facebook/ads/redexgen/X/56;
    .end local v4
    .end local v5
    :cond_0
    :goto_2
    add-int/lit8 v7, v7, 0x1

    goto :goto_1

    :cond_1
    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const-string v1, "3GcQR0gwPdJ7qG9NsafqkCoBzG49WZbm"

    const/4 v0, 0x6

    aput-object v1, v2, v0

    const-string v1, "xwZbJMHEvrrw2fWE8anB3sW9hLiJiwUw"

    const/4 v0, 0x4

    aput-object v1, v2, v0

    invoke-virtual {v4}, Lcom/facebook/ads/redexgen/X/4c;->A0A()Z

    move-result v0

    if-nez v0, :cond_2

    goto :goto_2

    .line 30726
    :cond_2
    iget-object v4, p0, Lcom/facebook/ads/redexgen/X/Eb;->A05:Lcom/facebook/ads/redexgen/X/4k;

    iget-object v2, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0s:Lcom/facebook/ads/redexgen/X/53;

    .line 30727
    invoke-static {v6}, Lcom/facebook/ads/redexgen/X/4k;->A00(Lcom/facebook/ads/redexgen/X/56;)I

    move-result v1

    .line 30728
    invoke-virtual {v6}, Lcom/facebook/ads/redexgen/X/56;->A0L()Ljava/util/List;

    move-result-object v0

    .line 30729
    invoke-virtual {v4, v2, v6, v1, v0}, Lcom/facebook/ads/redexgen/X/4k;->A09(Lcom/facebook/ads/redexgen/X/53;Lcom/facebook/ads/redexgen/X/56;ILjava/util/List;)Lcom/facebook/ads/redexgen/X/4j;

    move-result-object v1

    .line 30730
    .local v4, "animationInfo":Lcom/facebook/ads/redexgen/X/4j;
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0t:Lcom/facebook/ads/redexgen/X/5E;

    invoke-virtual {v0, v6, v1}, Lcom/facebook/ads/redexgen/X/5E;->A0F(Lcom/facebook/ads/redexgen/X/56;Lcom/facebook/ads/redexgen/X/4j;)V

    .line 30731
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0s:Lcom/facebook/ads/redexgen/X/53;

    iget-boolean v0, v0, Lcom/facebook/ads/redexgen/X/53;->A0E:Z

    if-eqz v0, :cond_0

    invoke-virtual {v6}, Lcom/facebook/ads/redexgen/X/56;->A0d()Z

    move-result v0

    if-eqz v0, :cond_0

    invoke-virtual {v6}, Lcom/facebook/ads/redexgen/X/56;->A0a()Z

    move-result v0

    if-nez v0, :cond_0

    .line 30732
    invoke-virtual {v6}, Lcom/facebook/ads/redexgen/X/56;->A0f()Z

    move-result v0

    if-nez v0, :cond_0

    invoke-virtual {v6}, Lcom/facebook/ads/redexgen/X/56;->A0Z()Z

    move-result v0

    if-nez v0, :cond_0

    .line 30733
    invoke-direct {p0, v6}, Lcom/facebook/ads/redexgen/X/Eb;->A0C(Lcom/facebook/ads/redexgen/X/56;)J

    move-result-wide v4

    .line 30734
    .local v5, "key":J
    iget-object v9, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0t:Lcom/facebook/ads/redexgen/X/5E;

    sget-object v1, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const/4 v0, 0x1

    aget-object v0, v1, v0

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v1

    const/16 v0, 0x20

    if-eq v1, v0, :cond_4

    :cond_3
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    :cond_4
    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const-string v1, "VKmUie50Iu1ZqeQWLORu5w"

    const/4 v0, 0x3

    aput-object v1, v2, v0

    const-string v1, "PYciOOvrvR"

    const/4 v0, 0x0

    aput-object v1, v2, v0

    invoke-virtual {v9, v4, v5, v6}, Lcom/facebook/ads/redexgen/X/5E;->A08(JLcom/facebook/ads/redexgen/X/56;)V

    goto :goto_2

    .line 30735
    :cond_5
    const/4 v2, 0x0

    goto/16 :goto_0

    .line 30736
    .end local v0    # "count":I
    .end local v1    # "i":I
    :cond_6
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0s:Lcom/facebook/ads/redexgen/X/53;

    iget-boolean v0, v0, Lcom/facebook/ads/redexgen/X/53;->A0B:Z

    if-eqz v0, :cond_d

    .line 30737
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/Eb;->A0i()V

    .line 30738
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0s:Lcom/facebook/ads/redexgen/X/53;

    iget-boolean v4, v0, Lcom/facebook/ads/redexgen/X/53;->A0D:Z

    .line 30739
    .local v0, "didStructureChange":Z
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0s:Lcom/facebook/ads/redexgen/X/53;

    iput-boolean v3, v0, Lcom/facebook/ads/redexgen/X/53;->A0D:Z

    .line 30740
    iget-object v2, p0, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0r:Lcom/facebook/ads/redexgen/X/4w;

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0s:Lcom/facebook/ads/redexgen/X/53;

    invoke-virtual {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/4o;->A1u(Lcom/facebook/ads/redexgen/X/4w;Lcom/facebook/ads/redexgen/X/53;)V

    .line 30741
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0s:Lcom/facebook/ads/redexgen/X/53;

    iput-boolean v4, v0, Lcom/facebook/ads/redexgen/X/53;->A0D:Z

    .line 30742
    const/4 v4, 0x0

    .restart local v1    # "i":I
    :goto_3
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A01:Lcom/facebook/ads/redexgen/X/4C;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/4C;->A05()I

    move-result v0

    if-ge v4, v0, :cond_c

    .line 30743
    iget-object v5, p0, Lcom/facebook/ads/redexgen/X/Eb;->A01:Lcom/facebook/ads/redexgen/X/4C;

    sget-object v1, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const/4 v0, 0x1

    aget-object v0, v1, v0

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v1

    const/16 v0, 0x20

    if-eq v1, v0, :cond_8

    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const-string v1, "4lQmW8"

    const/4 v0, 0x2

    aput-object v1, v2, v0

    const-string v1, "2Uq8uzi7rIEU"

    const/4 v0, 0x7

    aput-object v1, v2, v0

    invoke-virtual {v5, v4}, Lcom/facebook/ads/redexgen/X/4C;->A09(I)Landroid/view/View;

    move-result-object v0

    .line 30744
    .local v3, "child":Landroid/view/View;
    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/Eb;->A0F(Landroid/view/View;)Lcom/facebook/ads/redexgen/X/56;

    move-result-object v1

    .line 30745
    .local v4, "viewHolder":Lcom/facebook/ads/redexgen/X/56;
    invoke-virtual {v1}, Lcom/facebook/ads/redexgen/X/56;->A0f()Z

    move-result v0

    if-eqz v0, :cond_9

    .line 30746
    .end local v3    # "child":Landroid/view/View;
    .end local v4    # "viewHolder":Lcom/facebook/ads/redexgen/X/56;
    .end local v5    # "key":J
    .end local v6
    .end local v7
    :cond_7
    :goto_4
    add-int/lit8 v4, v4, 0x1

    goto :goto_3

    :cond_8
    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const-string v1, "EzPEwBDGhSnYyJadD6vqsk9WPl8azgQD"

    const/4 v0, 0x1

    aput-object v1, v2, v0

    invoke-virtual {v5, v4}, Lcom/facebook/ads/redexgen/X/4C;->A09(I)Landroid/view/View;

    move-result-object v0

    .line 30747
    .local v3, "child":Landroid/view/View;
    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/Eb;->A0F(Landroid/view/View;)Lcom/facebook/ads/redexgen/X/56;

    move-result-object v1

    .line 30748
    .local v4, "viewHolder":Lcom/facebook/ads/redexgen/X/56;
    invoke-virtual {v1}, Lcom/facebook/ads/redexgen/X/56;->A0f()Z

    move-result v0

    if-eqz v0, :cond_9

    goto :goto_4

    .line 30749
    :cond_9
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0t:Lcom/facebook/ads/redexgen/X/5E;

    invoke-virtual {v0, v1}, Lcom/facebook/ads/redexgen/X/5E;->A0I(Lcom/facebook/ads/redexgen/X/56;)Z

    move-result v0

    if-nez v0, :cond_7

    .line 30750
    invoke-static {v1}, Lcom/facebook/ads/redexgen/X/4k;->A00(Lcom/facebook/ads/redexgen/X/56;)I

    move-result v7

    .line 30751
    .local v5, "flags":I
    const/16 v0, 0x2000

    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/56;->A0i(I)Z

    move-result v6

    .line 30752
    .local v6, "wasHidden":Z
    if-nez v6, :cond_a

    .line 30753
    or-int/lit16 v7, v7, 0x1000

    .line 30754
    :cond_a
    iget-object v5, p0, Lcom/facebook/ads/redexgen/X/Eb;->A05:Lcom/facebook/ads/redexgen/X/4k;

    iget-object v2, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0s:Lcom/facebook/ads/redexgen/X/53;

    .line 30755
    invoke-virtual {v1}, Lcom/facebook/ads/redexgen/X/56;->A0L()Ljava/util/List;

    move-result-object v0

    .line 30756
    invoke-virtual {v5, v2, v1, v7, v0}, Lcom/facebook/ads/redexgen/X/4k;->A09(Lcom/facebook/ads/redexgen/X/53;Lcom/facebook/ads/redexgen/X/56;ILjava/util/List;)Lcom/facebook/ads/redexgen/X/4j;

    move-result-object v2

    .line 30757
    .local v7, "animationInfo":Lcom/facebook/ads/redexgen/X/4j;
    if-eqz v6, :cond_b

    .line 30758
    invoke-virtual {p0, v1, v2}, Lcom/facebook/ads/redexgen/X/Eb;->A1i(Lcom/facebook/ads/redexgen/X/56;Lcom/facebook/ads/redexgen/X/4j;)V

    goto :goto_4

    .line 30759
    :cond_b
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0t:Lcom/facebook/ads/redexgen/X/5E;

    invoke-virtual {v0, v1, v2}, Lcom/facebook/ads/redexgen/X/5E;->A0D(Lcom/facebook/ads/redexgen/X/56;Lcom/facebook/ads/redexgen/X/4j;)V

    goto :goto_4

    .line 30760
    .end local v1    # "i":I
    :cond_c
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/Eb;->A0X()V

    .line 30761
    .end local v0    # "didStructureChange":Z
    goto :goto_5

    .line 30762
    :cond_d
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/Eb;->A0X()V

    .line 30763
    :goto_5
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->A1L()V

    .line 30764
    invoke-virtual {p0, v3}, Lcom/facebook/ads/redexgen/X/Eb;->A1n(Z)V

    .line 30765
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0s:Lcom/facebook/ads/redexgen/X/53;

    const/4 v0, 0x2

    iput v0, v1, Lcom/facebook/ads/redexgen/X/53;->A04:I

    .line 30766
    return-void
.end method

.method private A0M()V
    .locals 6

    .line 30767
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->A1J()V

    .line 30768
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->A1K()V

    .line 30769
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0s:Lcom/facebook/ads/redexgen/X/53;

    const/4 v0, 0x6

    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/53;->A04(I)V

    .line 30770
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A00:Lcom/facebook/ads/redexgen/X/Zq;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/Zq;->A07()V

    .line 30771
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0s:Lcom/facebook/ads/redexgen/X/53;

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A04:Lcom/facebook/ads/redexgen/X/4c;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/4c;->A0E()I

    move-result v0

    iput v0, v1, Lcom/facebook/ads/redexgen/X/53;->A03:I

    .line 30772
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0s:Lcom/facebook/ads/redexgen/X/53;

    const/4 v3, 0x0

    iput v3, v0, Lcom/facebook/ads/redexgen/X/53;->A00:I

    .line 30773
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0s:Lcom/facebook/ads/redexgen/X/53;

    iput-boolean v3, v0, Lcom/facebook/ads/redexgen/X/53;->A09:Z

    .line 30774
    iget-object v2, p0, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0r:Lcom/facebook/ads/redexgen/X/4w;

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0s:Lcom/facebook/ads/redexgen/X/53;

    invoke-virtual {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/4o;->A1u(Lcom/facebook/ads/redexgen/X/4w;Lcom/facebook/ads/redexgen/X/53;)V

    .line 30775
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0s:Lcom/facebook/ads/redexgen/X/53;

    iput-boolean v3, v0, Lcom/facebook/ads/redexgen/X/53;->A0D:Z

    .line 30776
    const/4 v0, 0x0

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0j:Lcom/facebook/ads/internal/androidx/support/v7/widget/RecyclerView$SavedState;

    .line 30777
    iget-object v4, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0s:Lcom/facebook/ads/redexgen/X/53;

    iget-boolean v0, v4, Lcom/facebook/ads/redexgen/X/53;->A0C:Z

    if-eqz v0, :cond_0

    iget-object v5, p0, Lcom/facebook/ads/redexgen/X/Eb;->A05:Lcom/facebook/ads/redexgen/X/4k;

    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const/4 v0, 0x3

    aget-object v1, v2, v0

    const/4 v0, 0x0

    aget-object v0, v2, v0

    invoke-virtual {v1}, Ljava/lang/String;->length()I

    move-result v1

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v0

    if-eq v1, v0, :cond_2

    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const-string v1, "QyMu0uKDVMcIUQ2WaaKkjpUJvINNzfF4"

    const/4 v0, 0x6

    aput-object v1, v2, v0

    const-string v1, "jnZHkwnP1Z9tC9uQsacmbdRp3FaoK01F"

    const/4 v0, 0x4

    aput-object v1, v2, v0

    if-eqz v5, :cond_0

    const/4 v0, 0x1

    :goto_0
    iput-boolean v0, v4, Lcom/facebook/ads/redexgen/X/53;->A0C:Z

    .line 30778
    iget-object v4, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0s:Lcom/facebook/ads/redexgen/X/53;

    sget-object v1, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const/4 v0, 0x1

    aget-object v0, v1, v0

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v1

    const/16 v0, 0x20

    if-eq v1, v0, :cond_1

    const/4 v0, 0x4

    iput v0, v4, Lcom/facebook/ads/redexgen/X/53;->A04:I

    .line 30779
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->A1L()V

    .line 30780
    invoke-virtual {p0, v3}, Lcom/facebook/ads/redexgen/X/Eb;->A1n(Z)V

    .line 30781
    return-void

    .line 30782
    :cond_0
    const/4 v0, 0x0

    goto :goto_0

    :cond_1
    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const-string v1, "3IsVIAoReRfPbfS1EeUcjIwVgXkj1JVv"

    const/4 v0, 0x1

    aput-object v1, v2, v0

    const/4 v0, 0x4

    iput v0, v4, Lcom/facebook/ads/redexgen/X/53;->A04:I

    .line 30783
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->A1L()V

    .line 30784
    invoke-virtual {p0, v3}, Lcom/facebook/ads/redexgen/X/Eb;->A1n(Z)V

    .line 30785
    return-void

    :cond_2
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0
.end method

.method private A0N()V
    .locals 16

    .line 30786
    move-object/from16 v5, p0

    iget-object v1, v5, Lcom/facebook/ads/redexgen/X/Eb;->A0s:Lcom/facebook/ads/redexgen/X/53;

    const/4 v0, 0x4

    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/53;->A04(I)V

    .line 30787
    invoke-virtual/range {p0 .. p0}, Lcom/facebook/ads/redexgen/X/Eb;->A1J()V

    .line 30788
    invoke-virtual/range {p0 .. p0}, Lcom/facebook/ads/redexgen/X/Eb;->A1K()V

    .line 30789
    iget-object v0, v5, Lcom/facebook/ads/redexgen/X/Eb;->A0s:Lcom/facebook/ads/redexgen/X/53;

    const/4 v6, 0x1

    iput v6, v0, Lcom/facebook/ads/redexgen/X/53;->A04:I

    .line 30790
    iget-object v0, v5, Lcom/facebook/ads/redexgen/X/Eb;->A0s:Lcom/facebook/ads/redexgen/X/53;

    iget-boolean v0, v0, Lcom/facebook/ads/redexgen/X/53;->A0C:Z

    if-eqz v0, :cond_1

    .line 30791
    iget-object v0, v5, Lcom/facebook/ads/redexgen/X/Eb;->A01:Lcom/facebook/ads/redexgen/X/4C;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/4C;->A05()I

    move-result v7

    sub-int/2addr v7, v6

    .local v9, "i":I
    :goto_0
    if-ltz v7, :cond_0

    .line 30792
    iget-object v0, v5, Lcom/facebook/ads/redexgen/X/Eb;->A01:Lcom/facebook/ads/redexgen/X/4C;

    invoke-virtual {v0, v7}, Lcom/facebook/ads/redexgen/X/4C;->A09(I)Landroid/view/View;

    move-result-object v0

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/Eb;->A0F(Landroid/view/View;)Lcom/facebook/ads/redexgen/X/56;

    move-result-object v11

    .line 30793
    .local v10, "holder":Lcom/facebook/ads/redexgen/X/56;
    invoke-virtual {v11}, Lcom/facebook/ads/redexgen/X/56;->A0f()Z

    move-result v0

    if-eqz v0, :cond_5

    .line 30794
    .end local v10    # "holder":Lcom/facebook/ads/redexgen/X/56;
    .end local v11
    .end local v13
    .end local v14
    :goto_1
    add-int/lit8 v7, v7, -0x1

    goto :goto_0

    .line 30795
    .end local v9    # "i":I
    :cond_0
    iget-object v1, v5, Lcom/facebook/ads/redexgen/X/Eb;->A0t:Lcom/facebook/ads/redexgen/X/5E;

    iget-object v0, v5, Lcom/facebook/ads/redexgen/X/Eb;->A12:Lcom/facebook/ads/redexgen/X/5D;

    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/5E;->A0G(Lcom/facebook/ads/redexgen/X/5D;)V

    .line 30796
    :cond_1
    iget-object v1, v5, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    iget-object v0, v5, Lcom/facebook/ads/redexgen/X/Eb;->A0r:Lcom/facebook/ads/redexgen/X/4w;

    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/4o;->A1G(Lcom/facebook/ads/redexgen/X/4w;)V

    .line 30797
    iget-object v1, v5, Lcom/facebook/ads/redexgen/X/Eb;->A0s:Lcom/facebook/ads/redexgen/X/53;

    iget v0, v1, Lcom/facebook/ads/redexgen/X/53;->A03:I

    iput v0, v1, Lcom/facebook/ads/redexgen/X/53;->A05:I

    .line 30798
    const/4 v3, 0x0

    iput-boolean v3, v5, Lcom/facebook/ads/redexgen/X/Eb;->A0C:Z

    .line 30799
    iget-object v0, v5, Lcom/facebook/ads/redexgen/X/Eb;->A0s:Lcom/facebook/ads/redexgen/X/53;

    iput-boolean v3, v0, Lcom/facebook/ads/redexgen/X/53;->A0C:Z

    .line 30800
    iget-object v0, v5, Lcom/facebook/ads/redexgen/X/Eb;->A0s:Lcom/facebook/ads/redexgen/X/53;

    iput-boolean v3, v0, Lcom/facebook/ads/redexgen/X/53;->A0B:Z

    .line 30801
    iget-object v0, v5, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    iput-boolean v3, v0, Lcom/facebook/ads/redexgen/X/4o;->A09:Z

    .line 30802
    iget-object v0, v5, Lcom/facebook/ads/redexgen/X/Eb;->A0r:Lcom/facebook/ads/redexgen/X/4w;

    iget-object v0, v0, Lcom/facebook/ads/redexgen/X/4w;->A02:Ljava/util/ArrayList;

    if-eqz v0, :cond_2

    .line 30803
    iget-object v0, v5, Lcom/facebook/ads/redexgen/X/Eb;->A0r:Lcom/facebook/ads/redexgen/X/4w;

    iget-object v4, v0, Lcom/facebook/ads/redexgen/X/4w;->A02:Ljava/util/ArrayList;

    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const/4 v0, 0x3

    aget-object v1, v2, v0

    const/4 v0, 0x0

    aget-object v0, v2, v0

    invoke-virtual {v1}, Ljava/lang/String;->length()I

    move-result v1

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v0

    if-eq v1, v0, :cond_6

    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const-string v1, "DxhhwSm2KO29M0snTVs4N8QFyNW6c8YZ"

    const/4 v0, 0x1

    aput-object v1, v2, v0

    invoke-virtual {v4}, Ljava/util/ArrayList;->clear()V

    .line 30804
    :cond_2
    iget-object v0, v5, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    iget-boolean v0, v0, Lcom/facebook/ads/redexgen/X/4o;->A08:Z

    if-eqz v0, :cond_3

    .line 30805
    iget-object v0, v5, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    iput v3, v0, Lcom/facebook/ads/redexgen/X/4o;->A00:I

    .line 30806
    iget-object v0, v5, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    iput-boolean v3, v0, Lcom/facebook/ads/redexgen/X/4o;->A08:Z

    .line 30807
    iget-object v0, v5, Lcom/facebook/ads/redexgen/X/Eb;->A0r:Lcom/facebook/ads/redexgen/X/4w;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/4w;->A0O()V

    .line 30808
    :cond_3
    iget-object v1, v5, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    iget-object v0, v5, Lcom/facebook/ads/redexgen/X/Eb;->A0s:Lcom/facebook/ads/redexgen/X/53;

    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/4o;->A1v(Lcom/facebook/ads/redexgen/X/53;)V

    .line 30809
    invoke-virtual/range {p0 .. p0}, Lcom/facebook/ads/redexgen/X/Eb;->A1L()V

    .line 30810
    invoke-virtual {v5, v3}, Lcom/facebook/ads/redexgen/X/Eb;->A1n(Z)V

    .line 30811
    iget-object v0, v5, Lcom/facebook/ads/redexgen/X/Eb;->A0t:Lcom/facebook/ads/redexgen/X/5E;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/5E;->A06()V

    .line 30812
    iget-object v0, v5, Lcom/facebook/ads/redexgen/X/Eb;->A14:[I

    aget v1, v0, v3

    aget v0, v0, v6

    invoke-direct {v5, v1, v0}, Lcom/facebook/ads/redexgen/X/Eb;->A11(II)Z

    move-result v0

    if-eqz v0, :cond_4

    .line 30813
    invoke-virtual {v5, v3, v3}, Lcom/facebook/ads/redexgen/X/Eb;->A1b(II)V

    .line 30814
    :cond_4
    invoke-direct/range {p0 .. p0}, Lcom/facebook/ads/redexgen/X/Eb;->A0Q()V

    .line 30815
    invoke-direct/range {p0 .. p0}, Lcom/facebook/ads/redexgen/X/Eb;->A0S()V

    .line 30816
    return-void

    .line 30817
    :cond_5
    invoke-direct {v5, v11}, Lcom/facebook/ads/redexgen/X/Eb;->A0C(Lcom/facebook/ads/redexgen/X/56;)J

    move-result-wide v0

    .line 30818
    .local v11, "key":J
    iget-object v3, v5, Lcom/facebook/ads/redexgen/X/Eb;->A05:Lcom/facebook/ads/redexgen/X/4k;

    iget-object v2, v5, Lcom/facebook/ads/redexgen/X/Eb;->A0s:Lcom/facebook/ads/redexgen/X/53;

    .line 30819
    invoke-virtual {v3, v2, v11}, Lcom/facebook/ads/redexgen/X/4k;->A08(Lcom/facebook/ads/redexgen/X/53;Lcom/facebook/ads/redexgen/X/56;)Lcom/facebook/ads/redexgen/X/4j;

    move-result-object v8

    .line 30820
    .local v13, "animationInfo":Lcom/facebook/ads/redexgen/X/4j;
    iget-object v2, v5, Lcom/facebook/ads/redexgen/X/Eb;->A0t:Lcom/facebook/ads/redexgen/X/5E;

    invoke-virtual {v2, v0, v1}, Lcom/facebook/ads/redexgen/X/5E;->A05(J)Lcom/facebook/ads/redexgen/X/56;

    move-result-object v10

    .line 30821
    .local v14, "oldChangeViewHolder":Lcom/facebook/ads/redexgen/X/56;
    if-eqz v10, :cond_a

    invoke-virtual {v10}, Lcom/facebook/ads/redexgen/X/56;->A0f()Z

    move-result v2

    if-nez v2, :cond_a

    .line 30822
    iget-object v2, v5, Lcom/facebook/ads/redexgen/X/Eb;->A0t:Lcom/facebook/ads/redexgen/X/5E;

    invoke-virtual {v2, v10}, Lcom/facebook/ads/redexgen/X/5E;->A0H(Lcom/facebook/ads/redexgen/X/56;)Z

    move-result v14

    .line 30823
    .local v15, "oldDisappearing":Z
    iget-object v9, v5, Lcom/facebook/ads/redexgen/X/Eb;->A0t:Lcom/facebook/ads/redexgen/X/5E;

    sget-object v3, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const/4 v2, 0x5

    aget-object v3, v3, v2

    const/4 v2, 0x2

    invoke-virtual {v3, v2}, Ljava/lang/String;->charAt(I)C

    move-result v3

    const/16 v2, 0x59

    if-eq v3, v2, :cond_7

    :cond_6
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    :cond_7
    sget-object v4, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const-string v3, "oIS3LUWJBkP0D1bim3OID749wsN9UxnV"

    const/4 v2, 0x1

    aput-object v3, v4, v2

    invoke-virtual {v9, v11}, Lcom/facebook/ads/redexgen/X/5E;->A0H(Lcom/facebook/ads/redexgen/X/56;)Z

    move-result v15

    .line 30824
    .local p0, "newDisappearing":Z
    if-eqz v14, :cond_8

    if-ne v10, v11, :cond_8

    .line 30825
    iget-object v0, v5, Lcom/facebook/ads/redexgen/X/Eb;->A0t:Lcom/facebook/ads/redexgen/X/5E;

    invoke-virtual {v0, v11, v8}, Lcom/facebook/ads/redexgen/X/5E;->A0E(Lcom/facebook/ads/redexgen/X/56;Lcom/facebook/ads/redexgen/X/4j;)V

    goto/16 :goto_1

    .line 30826
    :cond_8
    iget-object v2, v5, Lcom/facebook/ads/redexgen/X/Eb;->A0t:Lcom/facebook/ads/redexgen/X/5E;

    invoke-virtual {v2, v10}, Lcom/facebook/ads/redexgen/X/5E;->A04(Lcom/facebook/ads/redexgen/X/56;)Lcom/facebook/ads/redexgen/X/4j;

    move-result-object v12

    .line 30827
    .local p1, "preInfo":Lcom/facebook/ads/redexgen/X/4j;
    iget-object v2, v5, Lcom/facebook/ads/redexgen/X/Eb;->A0t:Lcom/facebook/ads/redexgen/X/5E;

    invoke-virtual {v2, v11, v8}, Lcom/facebook/ads/redexgen/X/5E;->A0E(Lcom/facebook/ads/redexgen/X/56;Lcom/facebook/ads/redexgen/X/4j;)V

    .line 30828
    iget-object v2, v5, Lcom/facebook/ads/redexgen/X/Eb;->A0t:Lcom/facebook/ads/redexgen/X/5E;

    invoke-virtual {v2, v11}, Lcom/facebook/ads/redexgen/X/5E;->A03(Lcom/facebook/ads/redexgen/X/56;)Lcom/facebook/ads/redexgen/X/4j;

    move-result-object v13

    .line 30829
    .local p2, "postInfo":Lcom/facebook/ads/redexgen/X/4j;
    if-nez v12, :cond_9

    .line 30830
    invoke-direct {v5, v0, v1, v11, v10}, Lcom/facebook/ads/redexgen/X/Eb;->A0m(JLcom/facebook/ads/redexgen/X/56;Lcom/facebook/ads/redexgen/X/56;)V

    goto/16 :goto_1

    .line 30831
    :cond_9
    move-object/from16 v9, p0

    invoke-direct/range {v9 .. v15}, Lcom/facebook/ads/redexgen/X/Eb;->A0t(Lcom/facebook/ads/redexgen/X/56;Lcom/facebook/ads/redexgen/X/56;Lcom/facebook/ads/redexgen/X/4j;Lcom/facebook/ads/redexgen/X/4j;ZZ)V

    goto/16 :goto_1

    .line 30832
    :cond_a
    iget-object v0, v5, Lcom/facebook/ads/redexgen/X/Eb;->A0t:Lcom/facebook/ads/redexgen/X/5E;

    invoke-virtual {v0, v11, v8}, Lcom/facebook/ads/redexgen/X/5E;->A0E(Lcom/facebook/ads/redexgen/X/56;Lcom/facebook/ads/redexgen/X/4j;)V

    goto/16 :goto_1
.end method

.method private A0O()V
    .locals 2

    .line 30833
    new-instance v1, Lcom/facebook/ads/redexgen/X/Zj;

    invoke-direct {v1, p0}, Lcom/facebook/ads/redexgen/X/Zj;-><init>(Lcom/facebook/ads/redexgen/X/Eb;)V

    new-instance v0, Lcom/facebook/ads/redexgen/X/4C;

    invoke-direct {v0, v1}, Lcom/facebook/ads/redexgen/X/4C;-><init>(Lcom/facebook/ads/redexgen/X/4B;)V

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A01:Lcom/facebook/ads/redexgen/X/4C;

    .line 30834
    return-void
.end method

.method private A0P()V
    .locals 7

    .line 30835
    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0C:Z

    if-eqz v0, :cond_0

    .line 30836
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A00:Lcom/facebook/ads/redexgen/X/Zq;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/Zq;->A09()V

    .line 30837
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    invoke-virtual {v0, p0}, Lcom/facebook/ads/redexgen/X/4o;->A1M(Lcom/facebook/ads/redexgen/X/Eb;)V

    .line 30838
    :cond_0
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/Eb;->A0z()Z

    move-result v0

    if-eqz v0, :cond_4

    .line 30839
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A00:Lcom/facebook/ads/redexgen/X/Zq;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/Zq;->A08()V

    .line 30840
    :goto_0
    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0G:Z

    const/4 v3, 0x0

    if-nez v0, :cond_1

    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0H:Z

    if-eqz v0, :cond_3

    :cond_1
    const/4 v6, 0x1

    .line 30841
    .local v0, "animationTypeSupported":Z
    :goto_1
    iget-object v4, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0s:Lcom/facebook/ads/redexgen/X/53;

    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0D:Z

    if-eqz v0, :cond_5

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A05:Lcom/facebook/ads/redexgen/X/4k;

    if-eqz v0, :cond_5

    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0C:Z

    if-nez v0, :cond_2

    if-nez v6, :cond_2

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    iget-boolean v0, v0, Lcom/facebook/ads/redexgen/X/4o;->A09:Z

    if-eqz v0, :cond_5

    :cond_2
    iget-boolean v5, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0C:Z

    sget-object v1, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const/4 v0, 0x5

    aget-object v1, v1, v0

    const/4 v0, 0x2

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    const/16 v0, 0x59

    if-eq v1, v0, :cond_6

    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    .line 30842
    :cond_3
    const/4 v6, 0x0

    goto :goto_1

    .line 30843
    :cond_4
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A00:Lcom/facebook/ads/redexgen/X/Zq;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/Zq;->A07()V

    goto :goto_0

    .line 30844
    :cond_5
    const/4 v0, 0x0

    goto :goto_2

    .line 30845
    :cond_6
    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const-string v1, "q3lLGfTGOOnJqHZydaTklLew08TAvgTY"

    const/4 v0, 0x6

    aput-object v1, v2, v0

    const-string v1, "cayAc0YyiA2wrQyaXaOul4VyYlz5tKdZ"

    const/4 v0, 0x4

    aput-object v1, v2, v0

    if-eqz v5, :cond_7

    .line 30846
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A04:Lcom/facebook/ads/redexgen/X/4c;

    .line 30847
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/4c;->A0A()Z

    move-result v0

    if-eqz v0, :cond_5

    :cond_7
    const/4 v0, 0x1

    :goto_2
    iput-boolean v0, v4, Lcom/facebook/ads/redexgen/X/53;->A0C:Z

    .line 30848
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0s:Lcom/facebook/ads/redexgen/X/53;

    iget-boolean v0, v1, Lcom/facebook/ads/redexgen/X/53;->A0C:Z

    if-eqz v0, :cond_8

    if-eqz v6, :cond_8

    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0C:Z

    if-nez v0, :cond_8

    .line 30849
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/Eb;->A0z()Z

    move-result v0

    if-eqz v0, :cond_8

    const/4 v3, 0x1

    :cond_8
    iput-boolean v3, v1, Lcom/facebook/ads/redexgen/X/53;->A0B:Z

    sget-object v1, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const/4 v0, 0x1

    aget-object v0, v1, v0

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v1

    const/16 v0, 0x20

    if-eq v1, v0, :cond_9

    .line 30850
    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const-string v1, "gWU7AH"

    const/4 v0, 0x2

    aput-object v1, v2, v0

    const-string v1, "O2IXTNL1mXfE"

    const/4 v0, 0x7

    aput-object v1, v2, v0

    return-void

    :cond_9
    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const-string v1, "vAY5NMbknnGVxibfJ54Fdf4KbFDo9MxQ"

    const/4 v0, 0x5

    aput-object v1, v2, v0

    return-void
.end method

.method private A0Q()V
    .locals 7

    .line 30851
    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0o:Z

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A04:Lcom/facebook/ads/redexgen/X/4c;

    if-eqz v0, :cond_0

    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->hasFocus()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 30852
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->getDescendantFocusability()I

    move-result v1

    const/high16 v0, 0x60000

    if-eq v1, v0, :cond_0

    .line 30853
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->getDescendantFocusability()I

    move-result v1

    const/high16 v0, 0x20000

    if-ne v1, v0, :cond_1

    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->isFocused()Z

    move-result v0

    if-eqz v0, :cond_1

    .line 30854
    .end local v0
    .end local v1
    :cond_0
    return-void

    .line 30855
    :cond_1
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->isFocused()Z

    move-result v3

    sget-object v1, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const/4 v0, 0x5

    aget-object v1, v1, v0

    const/4 v0, 0x2

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    const/16 v0, 0x59

    if-eq v1, v0, :cond_2

    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    :cond_2
    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const-string v1, "GSYhD7ljhsNq0QcPBhSV8ATJlhol3xt1"

    const/4 v0, 0x5

    aput-object v1, v2, v0

    if-nez v3, :cond_5

    .line 30856
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->getFocusedChild()Landroid/view/View;

    move-result-object v1

    .line 30857
    .local v0, "focusedChild":Landroid/view/View;
    sget-boolean v0, Lcom/facebook/ads/redexgen/X/Eb;->A1G:Z

    if-eqz v0, :cond_4

    .line 30858
    invoke-virtual {v1}, Landroid/view/View;->getParent()Landroid/view/ViewParent;

    move-result-object v0

    if-eqz v0, :cond_3

    invoke-virtual {v1}, Landroid/view/View;->hasFocus()Z

    move-result v0

    if-nez v0, :cond_4

    .line 30859
    :cond_3
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A01:Lcom/facebook/ads/redexgen/X/4C;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/4C;->A05()I

    move-result v0

    if-nez v0, :cond_5

    .line 30860
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->requestFocus()Z

    .line 30861
    return-void

    .line 30862
    :cond_4
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A01:Lcom/facebook/ads/redexgen/X/4C;

    invoke-virtual {v0, v1}, Lcom/facebook/ads/redexgen/X/4C;->A0K(Landroid/view/View;)Z

    move-result v0

    if-nez v0, :cond_5

    .line 30863
    return-void

    .line 30864
    .end local v0    # "focusedChild":Landroid/view/View;
    :cond_5
    const/4 v6, 0x0

    .line 30865
    .local v0, "focusTarget":Lcom/facebook/ads/redexgen/X/56;
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0s:Lcom/facebook/ads/redexgen/X/53;

    iget-wide v1, v0, Lcom/facebook/ads/redexgen/X/53;->A08:J

    const-wide/16 v4, -0x1

    cmp-long v0, v1, v4

    if-eqz v0, :cond_6

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A04:Lcom/facebook/ads/redexgen/X/4c;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/4c;->A0A()Z

    move-result v0

    if-eqz v0, :cond_6

    .line 30866
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0s:Lcom/facebook/ads/redexgen/X/53;

    iget-wide v0, v0, Lcom/facebook/ads/redexgen/X/53;->A08:J

    invoke-direct {p0, v0, v1}, Lcom/facebook/ads/redexgen/X/Eb;->A0E(J)Lcom/facebook/ads/redexgen/X/56;

    move-result-object v6

    .line 30867
    :cond_6
    const/4 v3, 0x0

    .line 30868
    .local v1, "viewToFocus":Landroid/view/View;
    if-eqz v6, :cond_7

    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/Eb;->A01:Lcom/facebook/ads/redexgen/X/4C;

    iget-object v0, v6, Lcom/facebook/ads/redexgen/X/56;->A0H:Landroid/view/View;

    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/4C;->A0K(Landroid/view/View;)Z

    move-result v0

    if-nez v0, :cond_7

    iget-object v0, v6, Lcom/facebook/ads/redexgen/X/56;->A0H:Landroid/view/View;

    .line 30869
    invoke-virtual {v0}, Landroid/view/View;->hasFocusable()Z

    move-result v0

    if-nez v0, :cond_b

    .line 30870
    :cond_7
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A01:Lcom/facebook/ads/redexgen/X/4C;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/4C;->A05()I

    move-result v0

    if-lez v0, :cond_8

    .line 30871
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/Eb;->A0D()Landroid/view/View;

    move-result-object v3

    .line 30872
    :cond_8
    :goto_0
    if-eqz v3, :cond_a

    .line 30873
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0s:Lcom/facebook/ads/redexgen/X/53;

    iget v0, v0, Lcom/facebook/ads/redexgen/X/53;->A02:I

    int-to-long v1, v0

    cmp-long v0, v1, v4

    if-eqz v0, :cond_9

    .line 30874
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0s:Lcom/facebook/ads/redexgen/X/53;

    iget v0, v0, Lcom/facebook/ads/redexgen/X/53;->A02:I

    invoke-virtual {v3, v0}, Landroid/view/View;->findViewById(I)Landroid/view/View;

    move-result-object v1

    .line 30875
    .local v2, "child":Landroid/view/View;
    if-eqz v1, :cond_9

    invoke-virtual {v1}, Landroid/view/View;->isFocusable()Z

    move-result v0

    if-eqz v0, :cond_9

    .line 30876
    move-object v3, v1

    .line 30877
    .end local v2    # "child":Landroid/view/View;
    :cond_9
    invoke-virtual {v3}, Landroid/view/View;->requestFocus()Z

    .line 30878
    :cond_a
    return-void

    .line 30879
    :cond_b
    iget-object v3, v6, Lcom/facebook/ads/redexgen/X/56;->A0H:Landroid/view/View;

    goto :goto_0
.end method

.method private A0R()V
    .locals 2

    .line 30880
    const/4 v1, 0x0

    .line 30881
    .local v0, "needsInvalidate":Z
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0a:Landroid/widget/EdgeEffect;

    if-eqz v0, :cond_0

    .line 30882
    invoke-virtual {v0}, Landroid/widget/EdgeEffect;->onRelease()V

    .line 30883
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0a:Landroid/widget/EdgeEffect;

    invoke-virtual {v0}, Landroid/widget/EdgeEffect;->isFinished()Z

    move-result v1

    .line 30884
    :cond_0
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0c:Landroid/widget/EdgeEffect;

    if-eqz v0, :cond_1

    .line 30885
    invoke-virtual {v0}, Landroid/widget/EdgeEffect;->onRelease()V

    .line 30886
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0c:Landroid/widget/EdgeEffect;

    invoke-virtual {v0}, Landroid/widget/EdgeEffect;->isFinished()Z

    move-result v0

    or-int/2addr v1, v0

    .line 30887
    :cond_1
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0b:Landroid/widget/EdgeEffect;

    if-eqz v0, :cond_2

    .line 30888
    invoke-virtual {v0}, Landroid/widget/EdgeEffect;->onRelease()V

    .line 30889
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0b:Landroid/widget/EdgeEffect;

    invoke-virtual {v0}, Landroid/widget/EdgeEffect;->isFinished()Z

    move-result v0

    or-int/2addr v1, v0

    .line 30890
    :cond_2
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0Z:Landroid/widget/EdgeEffect;

    if-eqz v0, :cond_3

    .line 30891
    invoke-virtual {v0}, Landroid/widget/EdgeEffect;->onRelease()V

    .line 30892
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0Z:Landroid/widget/EdgeEffect;

    invoke-virtual {v0}, Landroid/widget/EdgeEffect;->isFinished()Z

    move-result v0

    or-int/2addr v1, v0

    .line 30893
    :cond_3
    if-eqz v1, :cond_4

    .line 30894
    invoke-static {p0}, Lcom/facebook/ads/redexgen/X/3T;->A07(Landroid/view/View;)V

    .line 30895
    :cond_4
    return-void
.end method

.method private A0S()V
    .locals 3

    .line 30896
    iget-object v2, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0s:Lcom/facebook/ads/redexgen/X/53;

    const-wide/16 v0, -0x1

    iput-wide v0, v2, Lcom/facebook/ads/redexgen/X/53;->A08:J

    .line 30897
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0s:Lcom/facebook/ads/redexgen/X/53;

    const/4 v1, -0x1

    iput v1, v0, Lcom/facebook/ads/redexgen/X/53;->A01:I

    .line 30898
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0s:Lcom/facebook/ads/redexgen/X/53;

    iput v1, v0, Lcom/facebook/ads/redexgen/X/53;->A02:I

    .line 30899
    return-void
.end method

.method private A0T()V
    .locals 1

    .line 30900
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0Y:Landroid/view/VelocityTracker;

    if-eqz v0, :cond_0

    .line 30901
    invoke-virtual {v0}, Landroid/view/VelocityTracker;->clear()V

    .line 30902
    :cond_0
    const/4 v0, 0x0

    invoke-virtual {p0, v0}, Lcom/facebook/ads/redexgen/X/Eb;->A1X(I)V

    .line 30903
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/Eb;->A0R()V

    .line 30904
    return-void
.end method

.method private A0U()V
    .locals 4

    .line 30905
    const/4 v1, 0x0

    .line 30906
    .local v0, "child":Landroid/view/View;
    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0o:Z

    if-eqz v0, :cond_0

    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->hasFocus()Z

    move-result v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A04:Lcom/facebook/ads/redexgen/X/4c;

    if-eqz v0, :cond_0

    .line 30907
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->getFocusedChild()Landroid/view/View;

    move-result-object v1

    .line 30908
    :cond_0
    if-nez v1, :cond_5

    const/4 v3, 0x0

    .line 30909
    .local v1, "focusedVh":Lcom/facebook/ads/redexgen/X/56;
    :goto_0
    if-nez v3, :cond_1

    .line 30910
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/Eb;->A0S()V

    .line 30911
    :goto_1
    return-void

    .line 30912
    :cond_1
    iget-object v2, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0s:Lcom/facebook/ads/redexgen/X/53;

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A04:Lcom/facebook/ads/redexgen/X/4c;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/4c;->A0A()Z

    move-result v0

    if-eqz v0, :cond_4

    invoke-virtual {v3}, Lcom/facebook/ads/redexgen/X/56;->A0K()J

    move-result-wide v0

    :goto_2
    iput-wide v0, v2, Lcom/facebook/ads/redexgen/X/53;->A08:J

    .line 30913
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0s:Lcom/facebook/ads/redexgen/X/53;

    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0C:Z

    if-eqz v0, :cond_2

    const/4 v0, -0x1

    .line 30914
    :goto_3
    iput v0, v1, Lcom/facebook/ads/redexgen/X/53;->A01:I

    .line 30915
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0s:Lcom/facebook/ads/redexgen/X/53;

    iget-object v0, v3, Lcom/facebook/ads/redexgen/X/56;->A0H:Landroid/view/View;

    invoke-direct {p0, v0}, Lcom/facebook/ads/redexgen/X/Eb;->A0B(Landroid/view/View;)I

    move-result v0

    iput v0, v1, Lcom/facebook/ads/redexgen/X/53;->A02:I

    goto :goto_1

    .line 30916
    :cond_2
    invoke-virtual {v3}, Lcom/facebook/ads/redexgen/X/56;->A0a()Z

    move-result v0

    if-eqz v0, :cond_3

    iget v0, v3, Lcom/facebook/ads/redexgen/X/56;->A01:I

    goto :goto_3

    .line 30917
    :cond_3
    invoke-virtual {v3}, Lcom/facebook/ads/redexgen/X/56;->A0G()I

    move-result v0

    goto :goto_3

    .line 30918
    :cond_4
    const-wide/16 v0, -0x1

    goto :goto_2

    .line 30919
    :cond_5
    invoke-direct {p0, v1}, Lcom/facebook/ads/redexgen/X/Eb;->A0G(Landroid/view/View;)Lcom/facebook/ads/redexgen/X/56;

    move-result-object v3

    goto :goto_0
.end method

.method private A0V()V
    .locals 1

    .line 30920
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A08:Lcom/facebook/ads/redexgen/X/55;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/55;->A08()V

    .line 30921
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    if-eqz v0, :cond_0

    .line 30922
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/4o;->A0x()V

    .line 30923
    :cond_0
    return-void
.end method

.method public static A0W()V
    .locals 1

    const/16 v0, 0x7f9

    new-array v0, v0, [B

    fill-array-data v0, :array_0

    sput-object v0, Lcom/facebook/ads/redexgen/X/Eb;->A18:[B

    return-void

    :array_0
    .array-data 1
        -0x4at
        -0x4at
        -0x60t
        -0x4at
        -0x14t
        -0x1t
        -0x5t
        0xdt
        -0x4at
        -0x22t
        0x5t
        0x2t
        -0x6t
        -0x5t
        0x8t
        -0x4at
        -0x38t
        -0x30t
        -0x64t
        -0x21t
        -0x23t
        -0x16t
        -0x16t
        -0x15t
        -0x10t
        -0x64t
        -0x22t
        -0x1ft
        -0x64t
        -0x1et
        -0x15t
        -0xft
        -0x16t
        -0x20t
        -0x64t
        -0x22t
        -0xft
        -0x10t
        -0x64t
        -0x1bt
        -0x10t
        -0x64t
        -0x1bt
        -0x11t
        -0x64t
        -0x16t
        -0x1ft
        -0x21t
        -0x1ft
        -0x11t
        -0x11t
        -0x23t
        -0x12t
        -0xbt
        -0x64t
        -0x1et
        -0x15t
        -0x12t
        -0x64t
        0x7et
        -0x39t
        -0x2ft
        0x7et
        -0x41t
        -0x36t
        -0x30t
        -0x3dt
        -0x41t
        -0x3et
        -0x29t
        0x7et
        -0x41t
        -0x2et
        -0x2et
        -0x41t
        -0x3ft
        -0x3at
        -0x3dt
        -0x3et
        0x7et
        -0x2et
        -0x33t
        0x7et
        -0x41t
        0x7et
        -0x50t
        -0x3dt
        -0x3ft
        -0x29t
        -0x3ft
        -0x36t
        -0x3dt
        -0x30t
        -0x4ct
        -0x39t
        -0x3dt
        -0x2bt
        -0x68t
        -0xct
        0x3dt
        0x47t
        -0xct
        0x42t
        0x43t
        0x48t
        -0xct
        0x35t
        -0xct
        0x38t
        0x3dt
        0x46t
        0x39t
        0x37t
        0x48t
        -0xct
        0x37t
        0x3ct
        0x3dt
        0x40t
        0x38t
        -0xct
        0x43t
        0x3at
        -0xct
        -0x5dt
        -0xft
        -0xet
        -0x9t
        -0x5dt
        -0x17t
        -0xet
        -0x8t
        -0xft
        -0x19t
        -0x4ft
        -0x5dt
        -0x39t
        -0x14t
        -0x19t
        -0x5dt
        -0x1ct
        -0xft
        -0x4t
        -0x5dt
        -0x30t
        -0xet
        -0x9t
        -0x14t
        -0xet
        -0xft
        -0x38t
        -0x7t
        -0x18t
        -0xft
        -0x9t
        -0xat
        -0x5dt
        -0x16t
        -0x18t
        -0x9t
        -0x5dt
        -0xat
        -0x12t
        -0x14t
        -0xdt
        -0xdt
        -0x18t
        -0x19t
        -0x3et
        -0x2dt
        -0x39t
        0x8t
        0xbt
        0x8t
        0x17t
        0x1bt
        0xct
        0x19t
        -0x1ft
        -0x75t
        0x7ft
        -0x3et
        -0x32t
        -0x33t
        -0x2dt
        -0x3ct
        -0x29t
        -0x2dt
        -0x67t
        -0x38t
        -0x44t
        0x8t
        -0x3t
        0x15t
        0xbt
        0x11t
        0x10t
        -0x2at
        0x9t
        -0x12t
        0x43t
        0x41t
        0x37t
        0x3ct
        0x35t
        -0x12t
        0x32t
        0x33t
        0x34t
        0x2ft
        0x43t
        0x3at
        0x42t
        -0x12t
        0x44t
        0x2ft
        0x3at
        0x43t
        0x33t
        -0x43t
        -0x25t
        -0x1at
        -0x1at
        -0x21t
        -0x22t
        -0x66t
        -0x14t
        -0x21t
        -0x19t
        -0x17t
        -0x10t
        -0x21t
        -0x42t
        -0x21t
        -0x12t
        -0x25t
        -0x23t
        -0x1et
        -0x21t
        -0x22t
        -0x30t
        -0x1dt
        -0x21t
        -0xft
        -0x66t
        -0xft
        -0x1dt
        -0x12t
        -0x1et
        -0x66t
        -0x25t
        -0x66t
        -0x10t
        -0x1dt
        -0x21t
        -0xft
        -0x66t
        -0xft
        -0x1et
        -0x1dt
        -0x23t
        -0x1et
        -0x66t
        -0x1dt
        -0x13t
        -0x66t
        -0x18t
        -0x17t
        -0x12t
        -0x66t
        -0x20t
        -0x1at
        -0x25t
        -0x1ft
        -0x1ft
        -0x21t
        -0x22t
        -0x66t
        -0x25t
        -0x13t
        -0x66t
        -0x12t
        -0x19t
        -0x16t
        -0x66t
        -0x22t
        -0x21t
        -0x12t
        -0x25t
        -0x23t
        -0x1et
        -0x21t
        -0x22t
        -0x58t
        -0x50t
        -0x32t
        -0x25t
        -0x25t
        -0x24t
        -0x1ft
        -0x73t
        -0x30t
        -0x32t
        -0x27t
        -0x27t
        -0x73t
        -0x1ft
        -0x2bt
        -0x2at
        -0x20t
        -0x73t
        -0x26t
        -0x2et
        -0x1ft
        -0x2bt
        -0x24t
        -0x2ft
        -0x73t
        -0x2at
        -0x25t
        -0x73t
        -0x32t
        -0x73t
        -0x20t
        -0x30t
        -0x21t
        -0x24t
        -0x27t
        -0x27t
        -0x73t
        -0x30t
        -0x32t
        -0x27t
        -0x27t
        -0x31t
        -0x32t
        -0x30t
        -0x28t
        -0x65t
        -0x73t
        -0x40t
        -0x30t
        -0x21t
        -0x24t
        -0x27t
        -0x27t
        -0x73t
        -0x30t
        -0x32t
        -0x27t
        -0x27t
        -0x31t
        -0x32t
        -0x30t
        -0x28t
        -0x20t
        -0x73t
        -0x26t
        -0x2at
        -0x2ct
        -0x2bt
        -0x1ft
        -0x31t
        -0x2et
        -0x73t
        -0x21t
        -0x1et
        -0x25t
        -0x73t
        -0x2ft
        -0x1et
        -0x21t
        -0x2at
        -0x25t
        -0x2ct
        -0x73t
        -0x32t
        -0x73t
        -0x26t
        -0x2et
        -0x32t
        -0x20t
        -0x1et
        -0x21t
        -0x2et
        -0x73t
        -0x6dt
        -0x73t
        -0x27t
        -0x32t
        -0x1at
        -0x24t
        -0x1et
        -0x1ft
        -0x73t
        -0x23t
        -0x32t
        -0x20t
        -0x20t
        -0x73t
        -0x1ct
        -0x2bt
        -0x2et
        -0x21t
        -0x2et
        -0x73t
        -0x1at
        -0x24t
        -0x1et
        -0x73t
        -0x30t
        -0x32t
        -0x25t
        -0x25t
        -0x24t
        -0x1ft
        -0x73t
        -0x30t
        -0x2bt
        -0x32t
        -0x25t
        -0x2ct
        -0x2et
        -0x73t
        -0x1ft
        -0x2bt
        -0x2et
        -0x41t
        -0x2et
        -0x30t
        -0x1at
        -0x30t
        -0x27t
        -0x2et
        -0x21t
        -0x3dt
        -0x2at
        -0x2et
        -0x1ct
        -0x73t
        -0x2ft
        -0x32t
        -0x1ft
        -0x32t
        -0x65t
        -0x73t
        -0x52t
        -0x25t
        -0x1at
        -0x73t
        -0x26t
        -0x2et
        -0x1ft
        -0x2bt
        -0x24t
        -0x2ft
        -0x73t
        -0x30t
        -0x32t
        -0x27t
        -0x27t
        -0x73t
        -0x1ft
        -0x2bt
        -0x32t
        -0x1ft
        -0x73t
        -0x26t
        -0x2at
        -0x2ct
        -0x2bt
        -0x1ft
        -0x73t
        -0x30t
        -0x2bt
        -0x32t
        -0x25t
        -0x2ct
        -0x2et
        -0x73t
        -0x1ft
        -0x2bt
        -0x2et
        -0x73t
        -0x20t
        -0x1ft
        -0x21t
        -0x1et
        -0x30t
        -0x1ft
        -0x1et
        -0x21t
        -0x2et
        -0x24t
        -0x2dt
        -0x73t
        -0x1ft
        -0x2bt
        -0x2et
        -0x73t
        -0x41t
        -0x2et
        -0x30t
        -0x1at
        -0x30t
        -0x27t
        -0x2et
        -0x21t
        -0x3dt
        -0x2at
        -0x2et
        -0x1ct
        -0x73t
        -0x24t
        -0x21t
        -0x73t
        -0x1ft
        -0x2bt
        -0x2et
        -0x73t
        -0x32t
        -0x2ft
        -0x32t
        -0x23t
        -0x1ft
        -0x2et
        -0x21t
        -0x73t
        -0x30t
        -0x24t
        -0x25t
        -0x1ft
        -0x2et
        -0x25t
        -0x1ft
        -0x20t
        -0x73t
        -0x20t
        -0x2bt
        -0x24t
        -0x1et
        -0x27t
        -0x2ft
        -0x73t
        -0x31t
        -0x2et
        -0x73t
        -0x23t
        -0x24t
        -0x20t
        -0x1ft
        -0x23t
        -0x24t
        -0x25t
        -0x2et
        -0x2ft
        -0x73t
        -0x1ft
        -0x24t
        -0x1ft
        -0x2bt
        -0x2et
        -0x73t
        -0x25t
        -0x2et
        -0x1bt
        -0x1ft
        -0x73t
        -0x2dt
        -0x21t
        -0x32t
        -0x26t
        -0x2et
        -0x65t
        -0x37t
        -0x19t
        -0xct
        -0xct
        -0xbt
        -0x6t
        -0x5at
        -0x17t
        -0x19t
        -0xet
        -0xet
        -0x5at
        -0x6t
        -0x12t
        -0x11t
        -0x7t
        -0x5at
        -0xdt
        -0x15t
        -0x6t
        -0x12t
        -0xbt
        -0x16t
        -0x5at
        -0x3t
        -0x12t
        -0x11t
        -0xet
        -0x15t
        -0x5at
        -0x28t
        -0x15t
        -0x17t
        -0x1t
        -0x17t
        -0xet
        -0x15t
        -0x8t
        -0x24t
        -0x11t
        -0x15t
        -0x3t
        -0x5at
        -0x11t
        -0x7t
        -0x5at
        -0x17t
        -0xbt
        -0xdt
        -0xat
        -0x5t
        -0x6t
        -0x11t
        -0xct
        -0x13t
        -0x5at
        -0x19t
        -0x5at
        -0xet
        -0x19t
        -0x1t
        -0xbt
        -0x5t
        -0x6t
        -0x5at
        -0xbt
        -0x8t
        -0x5at
        -0x7t
        -0x17t
        -0x8t
        -0xbt
        -0xet
        -0xet
        -0x11t
        -0xct
        -0x13t
        -0x1ct
        0x2t
        0xft
        0xft
        0x10t
        0x15t
        -0x3ft
        0x7t
        0xdt
        0xat
        0xft
        0x8t
        -0x3ft
        0x18t
        0xat
        0x15t
        0x9t
        0x10t
        0x16t
        0x15t
        -0x3ft
        0x2t
        -0x3ft
        -0x13t
        0x2t
        0x1at
        0x10t
        0x16t
        0x15t
        -0x12t
        0x2t
        0xft
        0x2t
        0x8t
        0x6t
        0x13t
        -0x3ft
        0x14t
        0x6t
        0x15t
        -0x31t
        -0x3ft
        -0x1ct
        0x2t
        0xdt
        0xdt
        -0x3ft
        0x14t
        0x6t
        0x15t
        -0x13t
        0x2t
        0x1at
        0x10t
        0x16t
        0x15t
        -0x12t
        0x2t
        0xft
        0x2t
        0x8t
        0x6t
        0x13t
        -0x3ft
        0x18t
        0xat
        0x15t
        0x9t
        -0x3ft
        0x2t
        -0x3ft
        0xft
        0x10t
        0xft
        -0x32t
        0xft
        0x16t
        0xdt
        0xdt
        -0x3ft
        0x2t
        0x13t
        0x8t
        0x16t
        0xet
        0x6t
        0xft
        0x15t
        -0x31t
        -0x23t
        -0x5t
        0x8t
        0x8t
        0x9t
        0xet
        -0x46t
        0xdt
        -0x3t
        0xct
        0x9t
        0x6t
        0x6t
        -0x46t
        0xet
        0x9t
        -0x46t
        0xat
        0x9t
        0xdt
        0x3t
        0xet
        0x3t
        0x9t
        0x8t
        -0x46t
        -0x5t
        -0x46t
        -0x1at
        -0x5t
        0x13t
        0x9t
        0xft
        0xet
        -0x19t
        -0x5t
        0x8t
        -0x5t
        0x1t
        -0x1t
        0xct
        -0x46t
        0xdt
        -0x1t
        0xet
        -0x38t
        -0x46t
        -0x23t
        -0x5t
        0x6t
        0x6t
        -0x46t
        0xdt
        -0x1t
        0xet
        -0x1at
        -0x5t
        0x13t
        0x9t
        0xft
        0xet
        -0x19t
        -0x5t
        0x8t
        -0x5t
        0x1t
        -0x1t
        0xct
        -0x46t
        0x11t
        0x3t
        0xet
        0x2t
        -0x46t
        -0x5t
        -0x46t
        0x8t
        0x9t
        0x8t
        -0x39t
        0x8t
        0xft
        0x6t
        0x6t
        -0x46t
        -0x5t
        0xct
        0x1t
        0xft
        0x7t
        -0x1t
        0x8t
        0xet
        -0x38t
        -0x21t
        -0x3t
        0xat
        0xat
        0xbt
        0x10t
        -0x44t
        0xft
        -0x1t
        0xet
        0xbt
        0x8t
        0x8t
        -0x44t
        0x13t
        0x5t
        0x10t
        0x4t
        0xbt
        0x11t
        0x10t
        -0x44t
        -0x3t
        -0x44t
        -0x18t
        -0x3t
        0x15t
        0xbt
        0x11t
        0x10t
        -0x17t
        -0x3t
        0xat
        -0x3t
        0x3t
        0x1t
        0xet
        -0x44t
        0xft
        0x1t
        0x10t
        -0x36t
        -0x44t
        -0x21t
        -0x3t
        0x8t
        0x8t
        -0x44t
        0xft
        0x1t
        0x10t
        -0x18t
        -0x3t
        0x15t
        0xbt
        0x11t
        0x10t
        -0x17t
        -0x3t
        0xat
        -0x3t
        0x3t
        0x1t
        0xet
        -0x44t
        0x13t
        0x5t
        0x10t
        0x4t
        -0x44t
        -0x3t
        -0x44t
        0xat
        0xbt
        0xat
        -0x37t
        0xat
        0x11t
        0x8t
        0x8t
        -0x44t
        -0x3t
        0xet
        0x3t
        0x11t
        0x9t
        0x1t
        0xat
        0x10t
        -0x36t
        -0x1dt
        0x1t
        0xet
        0xet
        0xft
        0x14t
        -0x40t
        0x13t
        0xdt
        0xft
        0xft
        0x14t
        0x8t
        -0x40t
        0x13t
        0x3t
        0x12t
        0xft
        0xct
        0xct
        -0x40t
        0x17t
        0x9t
        0x14t
        0x8t
        0xft
        0x15t
        0x14t
        -0x40t
        0x1t
        -0x40t
        -0x14t
        0x1t
        0x19t
        0xft
        0x15t
        0x14t
        -0x13t
        0x1t
        0xet
        0x1t
        0x7t
        0x5t
        0x12t
        -0x40t
        0x13t
        0x5t
        0x14t
        -0x32t
        -0x40t
        -0x1dt
        0x1t
        0xct
        0xct
        -0x40t
        0x13t
        0x5t
        0x14t
        -0x14t
        0x1t
        0x19t
        0xft
        0x15t
        0x14t
        -0x13t
        0x1t
        0xet
        0x1t
        0x7t
        0x5t
        0x12t
        -0x40t
        0x17t
        0x9t
        0x14t
        0x8t
        -0x40t
        0x1t
        -0x40t
        0xet
        0xft
        0xet
        -0x33t
        0xet
        0x15t
        0xct
        0xct
        -0x40t
        0x1t
        0x12t
        0x7t
        0x15t
        0xdt
        0x5t
        0xet
        0x14t
        -0x32t
        -0x12t
        0x19t
        -0x36t
        0x18t
        0x19t
        0x1et
        -0x36t
        0x1dt
        0xft
        0x1et
        -0xat
        0xbt
        0x23t
        0x19t
        0x1ft
        0x1et
        -0x10t
        0x1ct
        0x19t
        0x24t
        0xft
        0x18t
        -0x36t
        0x13t
        0x18t
        -0x36t
        0x16t
        0xbt
        0x23t
        0x19t
        0x1ft
        0x1et
        -0x36t
        0x19t
        0x1ct
        -0x36t
        0x1dt
        0xdt
        0x1ct
        0x19t
        0x16t
        0x16t
        -0x34t
        -0x7t
        -0x7t
        -0xat
        -0x7t
        -0x59t
        -0x9t
        -0x7t
        -0xat
        -0x16t
        -0x14t
        -0x6t
        -0x6t
        -0x10t
        -0xbt
        -0x12t
        -0x59t
        -0x6t
        -0x16t
        -0x7t
        -0xat
        -0xdt
        -0xdt
        -0x3et
        -0x59t
        -0x9t
        -0xat
        -0x10t
        -0xbt
        -0x5t
        -0x14t
        -0x7t
        -0x59t
        -0x10t
        -0xbt
        -0x15t
        -0x14t
        -0x1t
        -0x59t
        -0x13t
        -0xat
        -0x7t
        -0x59t
        -0x10t
        -0x15t
        -0x59t
        -0x7t
        0xet
        0x26t
        0x1ct
        0x22t
        0x21t
        -0x6t
        0xet
        0x1bt
        0xet
        0x14t
        0x12t
        0x1ft
        -0x33t
        -0x35t
        -0x14t
        -0x63t
        -0x22t
        -0x1ft
        -0x22t
        -0x13t
        -0xft
        -0x1et
        -0x11t
        -0x63t
        -0x22t
        -0xft
        -0xft
        -0x22t
        -0x20t
        -0x1bt
        -0x1et
        -0x1ft
        -0x48t
        -0x63t
        -0x10t
        -0x18t
        -0x1at
        -0x13t
        -0x13t
        -0x1at
        -0x15t
        -0x1ct
        -0x63t
        -0x17t
        -0x22t
        -0xat
        -0x14t
        -0xet
        -0xft
        -0x34t
        -0x13t
        -0x62t
        -0x16t
        -0x21t
        -0x9t
        -0x13t
        -0xdt
        -0xet
        -0x62t
        -0x15t
        -0x21t
        -0x14t
        -0x21t
        -0x1bt
        -0x1dt
        -0x10t
        -0x62t
        -0x21t
        -0xet
        -0xet
        -0x21t
        -0x1ft
        -0x1at
        -0x1dt
        -0x1et
        -0x47t
        -0x62t
        -0xft
        -0x17t
        -0x19t
        -0x12t
        -0x12t
        -0x19t
        -0x14t
        -0x1bt
        -0x62t
        -0x16t
        -0x21t
        -0x9t
        -0x13t
        -0xdt
        -0xet
        -0x1t
        0x21t
        0x1et
        0x11t
        0x1bt
        0x14t
        0x1ct
        -0x31t
        0x26t
        0x17t
        0x18t
        0x1bt
        0x14t
        -0x31t
        0x1ct
        0x10t
        0x23t
        0x12t
        0x17t
        0x18t
        0x1dt
        0x16t
        -0x31t
        0x12t
        0x17t
        0x10t
        0x1dt
        0x16t
        0x14t
        0x13t
        -0x31t
        0x25t
        0x18t
        0x14t
        0x26t
        -0x31t
        0x17t
        0x1et
        0x1bt
        0x13t
        0x14t
        0x21t
        0x22t
        -0x31t
        0x26t
        0x18t
        0x23t
        0x17t
        -0x31t
        0x23t
        0x17t
        0x14t
        -0x31t
        0x1dt
        0x14t
        0x26t
        0x1et
        0x1dt
        0x14t
        0x22t
        -0x23t
        -0x31t
        0x3t
        0x17t
        0x14t
        -0x31t
        0x1ft
        0x21t
        0x14t
        -0x24t
        0x1bt
        0x10t
        0x28t
        0x1et
        0x24t
        0x23t
        -0x31t
        0x18t
        0x1dt
        0x15t
        0x1et
        0x21t
        0x1ct
        0x10t
        0x23t
        0x18t
        0x1et
        0x1dt
        -0x31t
        0x15t
        0x1et
        0x21t
        -0x31t
        0x23t
        0x17t
        0x14t
        -0x31t
        0x12t
        0x17t
        0x10t
        0x1dt
        0x16t
        0x14t
        -0x31t
        0x17t
        0x1et
        0x1bt
        0x13t
        0x14t
        0x21t
        -0x31t
        0x5t
        0x9t
        -0x2dt
        -0x7t
        0x28t
        0x1ft
        0x1ft
        -0x4t
        0x21t
        0x29t
        0x14t
        0x1ft
        0x1ct
        0x17t
        0x14t
        0x27t
        0x18t
        -0x3t
        0x1t
        -0x35t
        -0x6t
        0x19t
        -0x9t
        0xct
        0x24t
        0x1at
        0x20t
        0x1ft
        -0x7t
        -0x3t
        -0x39t
        -0x9t
        0x8t
        0x19t
        0x1bt
        0x10t
        0x8t
        0x13t
        -0x10t
        0x15t
        0x1dt
        0x8t
        0x13t
        0x10t
        0xbt
        0x8t
        0x1bt
        0xct
        -0xet
        -0xat
        -0x40t
        -0xdt
        0x3t
        0x12t
        0xft
        0xct
        0xct
        -0xat
        0x9t
        0x7t
        0x1dt
        0x7t
        0x10t
        0x9t
        0x16t
        -0x6t
        0xdt
        0x9t
        0x1bt
        0x1t
        0x14t
        0x12t
        0x28t
        0x12t
        0x1bt
        0x14t
        0x21t
        0x5t
        0x18t
        0x14t
        0x26t
        -0x31t
        0x13t
        0x1et
        0x14t
        0x22t
        -0x31t
        0x1dt
        0x1et
        0x23t
        -0x31t
        0x22t
        0x24t
        0x1ft
        0x1ft
        0x1et
        0x21t
        0x23t
        -0x31t
        0x22t
        0x12t
        0x21t
        0x1et
        0x1bt
        0x1bt
        0x18t
        0x1dt
        0x16t
        -0x31t
        0x23t
        0x1et
        -0x31t
        0x10t
        0x1dt
        -0x31t
        0x10t
        0x11t
        0x22t
        0x1et
        0x1bt
        0x24t
        0x23t
        0x14t
        -0x31t
        0x1ft
        0x1et
        0x22t
        0x18t
        0x23t
        0x18t
        0x1et
        0x1dt
        -0x23t
        -0x31t
        0x4t
        0x22t
        0x14t
        -0x31t
        0x22t
        0x12t
        0x21t
        0x1et
        0x1bt
        0x1bt
        0x3t
        0x1et
        -0x1t
        0x1et
        0x22t
        0x18t
        0x23t
        0x18t
        0x1et
        0x1dt
        -0x31t
        0x18t
        0x1dt
        0x22t
        0x23t
        0x14t
        0x10t
        0x13t
        -0x48t
        -0x35t
        -0x37t
        -0x21t
        -0x37t
        -0x2et
        -0x35t
        -0x28t
        -0x44t
        -0x31t
        -0x35t
        -0x23t
        -0x7at
        -0x32t
        -0x39t
        -0x27t
        -0x7at
        -0x2ct
        -0x2bt
        -0x7at
        -0x4et
        -0x39t
        -0x21t
        -0x2bt
        -0x25t
        -0x26t
        -0x4dt
        -0x39t
        -0x2ct
        -0x39t
        -0x33t
        -0x35t
        -0x28t
        0x1dt
        0x40t
        0x38t
        -0x17t
        0x2dt
        0x32t
        0x2ft
        0x2ft
        0x2et
        0x3bt
        0x2et
        0x37t
        0x3dt
        -0x17t
        0x1ft
        0x32t
        0x2et
        0x40t
        0x11t
        0x38t
        0x35t
        0x2dt
        0x2et
        0x3bt
        0x3ct
        -0x17t
        0x31t
        0x2at
        0x3ft
        0x2et
        -0x17t
        0x3dt
        0x31t
        0x2et
        -0x17t
        0x3ct
        0x2at
        0x36t
        0x2et
        -0x17t
        0x2ct
        0x31t
        0x2at
        0x37t
        0x30t
        0x2et
        -0x17t
        0x12t
        0xdt
        -0x9t
        -0x17t
        0x1dt
        0x31t
        0x32t
        0x3ct
        -0x17t
        0x36t
        0x32t
        0x30t
        0x31t
        0x3dt
        -0x17t
        0x31t
        0x2at
        0x39t
        0x39t
        0x2et
        0x37t
        -0x17t
        0x2dt
        0x3et
        0x2et
        -0x17t
        0x3dt
        0x38t
        -0x17t
        0x32t
        0x37t
        0x2ct
        0x38t
        0x37t
        0x3ct
        0x32t
        0x3ct
        0x3dt
        0x2et
        0x37t
        0x3dt
        -0x17t
        0xat
        0x2dt
        0x2at
        0x39t
        0x3dt
        0x2et
        0x3bt
        -0x17t
        0x3et
        0x39t
        0x2dt
        0x2at
        0x3dt
        0x2et
        -0x17t
        0x2et
        0x3ft
        0x2et
        0x37t
        0x3dt
        0x3ct
        -0x17t
        0x38t
        0x3bt
        -0x17t
        0x32t
        0x2ft
        -0x17t
        0x3dt
        0x31t
        0x2et
        -0x17t
        0x15t
        0x2at
        0x42t
        0x38t
        0x3et
        0x3dt
        0x16t
        0x2at
        0x37t
        0x2at
        0x30t
        0x2et
        0x3bt
        -0x17t
        0x35t
        0x2at
        0x42t
        0x3ct
        -0x17t
        0x38t
        0x3et
        0x3dt
        -0x17t
        0x3dt
        0x31t
        0x2et
        -0x17t
        0x3ct
        0x2at
        0x36t
        0x2et
        -0x17t
        0x1ft
        0x32t
        0x2et
        0x40t
        -0x17t
        0x36t
        0x3et
        0x35t
        0x3dt
        0x32t
        0x39t
        0x35t
        0x2et
        -0x17t
        0x3dt
        0x32t
        0x36t
        0x2et
        0x3ct
        -0x9t
        -0x2dt
        -0x17t
        0x1ft
        0x32t
        0x2et
        0x40t
        0x11t
        0x38t
        0x35t
        0x2dt
        0x2et
        0x3bt
        -0x17t
        -0x6t
        0x3t
        -0xat
        0x19t
        0x11t
        -0x3et
        0x6t
        0xbt
        0x8t
        0x8t
        0x7t
        0x14t
        0x7t
        0x10t
        0x16t
        -0x3et
        -0x8t
        0xbt
        0x7t
        0x19t
        -0x16t
        0x11t
        0xet
        0x6t
        0x7t
        0x14t
        0x15t
        -0x3et
        0xat
        0x3t
        0x18t
        0x7t
        -0x3et
        0x16t
        0xat
        0x7t
        -0x3et
        0x15t
        0x3t
        0xft
        0x7t
        -0x3et
        0x15t
        0x16t
        0x3t
        0x4t
        0xet
        0x7t
        -0x3et
        -0x15t
        -0x1at
        -0x30t
        -0x3et
        -0xbt
        0x16t
        0x3t
        0x4t
        0xet
        0x7t
        -0x3et
        -0x15t
        -0x1at
        0x15t
        -0x3et
        0xbt
        0x10t
        -0x3et
        0x1bt
        0x11t
        0x17t
        0x14t
        -0x3et
        0x3t
        0x6t
        0x3t
        0x12t
        0x16t
        0x7t
        0x14t
        -0x3et
        -0x11t
        -0x9t
        -0xbt
        -0xat
        -0x3et
        -0x1ct
        -0x19t
        -0x3et
        0x17t
        0x10t
        0xbt
        0x13t
        0x17t
        0x7t
        -0x3et
        0x3t
        0x10t
        0x6t
        -0x3et
        -0xbt
        -0x16t
        -0xft
        -0x9t
        -0x12t
        -0x1at
        -0x3et
        -0x10t
        -0xft
        -0xat
        -0x3et
        0x5t
        0xat
        0x3t
        0x10t
        0x9t
        0x7t
        -0x30t
        -0x54t
        -0x3et
        -0x8t
        0xbt
        0x7t
        0x19t
        -0x16t
        0x11t
        0xet
        0x6t
        0x7t
        0x14t
        -0x3et
        -0x2dt
        -0x24t
        -0x1dt
        -0xat
        -0xet
        0x4t
        -0x53t
        0x7t
        0x9t
        0x9t
        0xbt
        0x19t
        0x19t
        0xft
        0x8t
        0xft
        0x12t
        0xft
        0x1at
        0x1ft
        0x1ft
        0x24t
        0x2dt
        0x20t
        0x1et
        0x2ft
        0x24t
        0x2at
        0x29t
        -0x25t
        0x28t
        0x30t
        0x2et
        0x2ft
        -0x25t
        0x1dt
        0x20t
        -0x25t
        0x1ct
        0x1dt
        0x2et
        0x2at
        0x27t
        0x30t
        0x2ft
        0x20t
        -0x17t
        -0x25t
        0x2dt
        0x20t
        0x1et
        0x20t
        0x24t
        0x31t
        0x20t
        0x1ft
        -0xbt
        -0x32t
        -0x34t
        -0x25t
        -0x50t
        -0x25t
        -0x34t
        -0x2ct
        -0x4at
        -0x33t
        -0x33t
        -0x26t
        -0x34t
        -0x25t
        -0x26t
        -0x28t
        -0x29t
        -0x54t
        -0x2ft
        -0x2et
        -0x2bt
        -0x33t
        -0x41t
        -0x2et
        -0x32t
        -0x20t
        -0x56t
        -0x23t
        -0x23t
        -0x36t
        -0x34t
        -0x2ft
        -0x32t
        -0x33t
        -0x43t
        -0x28t
        -0x40t
        -0x2et
        -0x29t
        -0x33t
        -0x28t
        -0x20t
        -0x22t
        -0x23t
        -0x4et
        -0x29t
        -0x28t
        -0x25t
        -0x2dt
        -0x3bt
        -0x28t
        -0x2ct
        -0x1at
        -0x4dt
        -0x2ct
        -0x1dt
        -0x30t
        -0x2et
        -0x29t
        -0x2ct
        -0x2dt
        -0x4bt
        -0x1ft
        -0x22t
        -0x24t
        -0x3at
        -0x28t
        -0x23t
        -0x2dt
        -0x22t
        -0x1at
        -0x19t
        -0x1at
        -0x41t
        -0x23t
        -0x14t
        -0x45t
        -0x20t
        -0x1ft
        -0x1ct
        -0x24t
        -0x44t
        -0x16t
        -0x27t
        -0x11t
        -0x1ft
        -0x1at
        -0x21t
        -0x39t
        -0x16t
        -0x24t
        -0x23t
        -0x16t
        0x12t
        0x11t
        -0x14t
        0x11t
        0x17t
        0x8t
        0x15t
        0x6t
        0x8t
        0x13t
        0x17t
        -0x9t
        0x12t
        0x18t
        0x6t
        0xbt
        -0x18t
        0x19t
        0x8t
        0x11t
        0x17t
        0x1dt
        0xft
        0x1et
        -0x3t
        0xdt
        0x1ct
        0x19t
        0x16t
        0x16t
        0x13t
        0x18t
        0x11t
        -0x2t
        0x19t
        0x1ft
        0xdt
        0x12t
        -0x3t
        0x16t
        0x19t
        0x1at
        -0x2et
        -0x2dt
        -0x1ct
        -0x36t
        0xct
        0xbt
        0xet
        -0x36t
        0xbt
        0x1ct
        0x11t
        0x1ft
        0x17t
        0xft
        0x18t
        0x1et
        -0x36t
        0xdt
        0x19t
        0x18t
        0x1dt
        0x1et
        0xbt
        0x18t
        0x1et
        -0x36t
    .end array-data
.end method

.method private final A0X()V
    .locals 6

    .line 30924
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A01:Lcom/facebook/ads/redexgen/X/4C;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/4C;->A06()I

    move-result v4

    .line 30925
    .local v0, "childCount":I
    const/4 v3, 0x0

    .local v1, "i":I
    :goto_0
    if-ge v3, v4, :cond_2

    .line 30926
    iget-object v5, p0, Lcom/facebook/ads/redexgen/X/Eb;->A01:Lcom/facebook/ads/redexgen/X/4C;

    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const/4 v0, 0x3

    aget-object v1, v2, v0

    const/4 v0, 0x0

    aget-object v0, v2, v0

    invoke-virtual {v1}, Ljava/lang/String;->length()I

    move-result v1

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v0

    if-eq v1, v0, :cond_1

    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const-string v1, "VFyFJGthyduvDNyVUqu48D"

    const/4 v0, 0x3

    aput-object v1, v2, v0

    const-string v1, "uBjdUWzzez"

    const/4 v0, 0x0

    aput-object v1, v2, v0

    invoke-virtual {v5, v3}, Lcom/facebook/ads/redexgen/X/4C;->A0A(I)Landroid/view/View;

    move-result-object v0

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/Eb;->A0F(Landroid/view/View;)Lcom/facebook/ads/redexgen/X/56;

    move-result-object v1

    .line 30927
    .local v2, "holder":Lcom/facebook/ads/redexgen/X/56;
    invoke-virtual {v1}, Lcom/facebook/ads/redexgen/X/56;->A0f()Z

    move-result v0

    if-nez v0, :cond_0

    .line 30928
    invoke-virtual {v1}, Lcom/facebook/ads/redexgen/X/56;->A0M()V

    .line 30929
    .end local v2    # "holder":Lcom/facebook/ads/redexgen/X/56;
    :cond_0
    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    :cond_1
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    .line 30930
    .end local v1    # "i":I
    :cond_2
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0r:Lcom/facebook/ads/redexgen/X/4w;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/4w;->A0K()V

    .line 30931
    return-void
.end method

.method private final A0Y()V
    .locals 5

    .line 30932
    iget-object v4, p0, Lcom/facebook/ads/redexgen/X/Eb;->A04:Lcom/facebook/ads/redexgen/X/4c;

    const/16 v2, 0x55a

    const/16 v1, 0xc

    const/16 v0, 0x4f

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Eb;->A0I(III)Ljava/lang/String;

    move-result-object v3

    if-nez v4, :cond_0

    .line 30933
    const/16 v2, 0x463

    const/16 v1, 0x24

    const/16 v0, 0x28

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Eb;->A0I(III)Ljava/lang/String;

    move-result-object v0

    invoke-static {v3, v0}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    .line 30934
    return-void

    .line 30935
    :cond_0
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    if-nez v0, :cond_1

    .line 30936
    const/16 v2, 0x487

    const/16 v1, 0x2b

    const/16 v0, 0x29

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Eb;->A0I(III)Ljava/lang/String;

    move-result-object v0

    invoke-static {v3, v0}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    .line 30937
    return-void

    .line 30938
    :cond_1
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0s:Lcom/facebook/ads/redexgen/X/53;

    const/4 v0, 0x0

    iput-boolean v0, v1, Lcom/facebook/ads/redexgen/X/53;->A0A:Z

    .line 30939
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0s:Lcom/facebook/ads/redexgen/X/53;

    iget v1, v0, Lcom/facebook/ads/redexgen/X/53;->A04:I

    const/4 v0, 0x1

    if-ne v1, v0, :cond_2

    .line 30940
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/Eb;->A0L()V

    .line 30941
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    invoke-virtual {v0, p0}, Lcom/facebook/ads/redexgen/X/4o;->A1O(Lcom/facebook/ads/redexgen/X/Eb;)V

    .line 30942
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/Eb;->A0M()V

    .line 30943
    :goto_0
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/Eb;->A0N()V

    .line 30944
    return-void

    .line 30945
    :cond_2
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A00:Lcom/facebook/ads/redexgen/X/Zq;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/Zq;->A0C()Z

    move-result v0

    if-nez v0, :cond_3

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/4o;->A0h()I

    move-result v1

    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->getWidth()I

    move-result v0

    if-ne v1, v0, :cond_3

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    .line 30946
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/4o;->A0X()I

    move-result v1

    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->getHeight()I

    move-result v0

    if-eq v1, v0, :cond_4

    .line 30947
    :cond_3
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    invoke-virtual {v0, p0}, Lcom/facebook/ads/redexgen/X/4o;->A1O(Lcom/facebook/ads/redexgen/X/Eb;)V

    .line 30948
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/Eb;->A0M()V

    goto :goto_0

    .line 30949
    :cond_4
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    invoke-virtual {v0, p0}, Lcom/facebook/ads/redexgen/X/4o;->A1O(Lcom/facebook/ads/redexgen/X/Eb;)V

    goto :goto_0
.end method

.method private final A0Z()V
    .locals 5

    .line 30950
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0w:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    add-int/lit8 v4, v0, -0x1

    .local v0, "i":I
    :goto_0
    if-ltz v4, :cond_2

    .line 30951
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0w:Ljava/util/List;

    invoke-interface {v0, v4}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lcom/facebook/ads/redexgen/X/56;

    .line 30952
    .local v1, "viewHolder":Lcom/facebook/ads/redexgen/X/56;
    iget-object v0, v3, Lcom/facebook/ads/redexgen/X/56;->A0H:Landroid/view/View;

    invoke-virtual {v0}, Landroid/view/View;->getParent()Landroid/view/ViewParent;

    move-result-object v0

    if-ne v0, p0, :cond_0

    invoke-virtual {v3}, Lcom/facebook/ads/redexgen/X/56;->A0f()Z

    move-result v0

    if-eqz v0, :cond_1

    .line 30953
    .end local v1    # "viewHolder":Lcom/facebook/ads/redexgen/X/56;
    .end local v2
    :cond_0
    :goto_1
    add-int/lit8 v4, v4, -0x1

    goto :goto_0

    .line 30954
    :cond_1
    iget v2, v3, Lcom/facebook/ads/redexgen/X/56;->A02:I

    .line 30955
    .local v2, "state":I
    const/4 v1, -0x1

    if-eq v2, v1, :cond_0

    .line 30956
    iget-object v0, v3, Lcom/facebook/ads/redexgen/X/56;->A0H:Landroid/view/View;

    invoke-static {v0, v2}, Lcom/facebook/ads/redexgen/X/3T;->A09(Landroid/view/View;I)V

    .line 30957
    iput v1, v3, Lcom/facebook/ads/redexgen/X/56;->A02:I

    goto :goto_1

    .line 30958
    .end local v0    # "i":I
    :cond_2
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0w:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->clear()V

    .line 30959
    return-void
.end method

.method private final A0a()V
    .locals 4

    .line 30960
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0Z:Landroid/widget/EdgeEffect;

    if-eqz v0, :cond_0

    .line 30961
    return-void

    .line 30962
    :cond_0
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->getContext()Landroid/content/Context;

    move-result-object v0

    new-instance v3, Landroid/widget/EdgeEffect;

    invoke-direct {v3, v0}, Landroid/widget/EdgeEffect;-><init>(Landroid/content/Context;)V

    iput-object v3, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0Z:Landroid/widget/EdgeEffect;

    .line 30963
    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0B:Z

    if-eqz v0, :cond_1

    .line 30964
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->getMeasuredWidth()I

    move-result v2

    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->getPaddingLeft()I

    move-result v0

    sub-int/2addr v2, v0

    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->getPaddingRight()I

    move-result v0

    sub-int/2addr v2, v0

    .line 30965
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->getMeasuredHeight()I

    move-result v1

    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->getPaddingTop()I

    move-result v0

    sub-int/2addr v1, v0

    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->getPaddingBottom()I

    move-result v0

    sub-int/2addr v1, v0

    .line 30966
    invoke-virtual {v3, v2, v1}, Landroid/widget/EdgeEffect;->setSize(II)V

    .line 30967
    :goto_0
    return-void

    .line 30968
    :cond_1
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->getMeasuredWidth()I

    move-result v1

    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->getMeasuredHeight()I

    move-result v0

    invoke-virtual {v3, v1, v0}, Landroid/widget/EdgeEffect;->setSize(II)V

    goto :goto_0
.end method

.method private final A0b()V
    .locals 7

    .line 30969
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0a:Landroid/widget/EdgeEffect;

    if-eqz v0, :cond_0

    .line 30970
    return-void

    .line 30971
    :cond_0
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->getContext()Landroid/content/Context;

    move-result-object v0

    new-instance v3, Landroid/widget/EdgeEffect;

    invoke-direct {v3, v0}, Landroid/widget/EdgeEffect;-><init>(Landroid/content/Context;)V

    iput-object v3, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0a:Landroid/widget/EdgeEffect;

    .line 30972
    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0B:Z

    if-eqz v0, :cond_1

    .line 30973
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->getMeasuredHeight()I

    move-result v5

    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->getPaddingTop()I

    move-result v0

    sub-int/2addr v5, v0

    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->getPaddingBottom()I

    move-result v0

    sub-int/2addr v5, v0

    .line 30974
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->getMeasuredWidth()I

    move-result v4

    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->getPaddingLeft()I

    move-result v0

    sub-int/2addr v4, v0

    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->getPaddingRight()I

    move-result v6

    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const/4 v0, 0x2

    aget-object v1, v2, v0

    const/4 v0, 0x7

    aget-object v0, v2, v0

    invoke-virtual {v1}, Ljava/lang/String;->length()I

    move-result v1

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v0

    if-eq v1, v0, :cond_2

    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const-string v1, "GMHrLIeJxdXYoHZyaa7kSwSfzExGbrBL"

    const/4 v0, 0x6

    aput-object v1, v2, v0

    const-string v1, "0YT1HYXH3vPYSlLI8aOL6WZeFeHH6VHe"

    const/4 v0, 0x4

    aput-object v1, v2, v0

    sub-int/2addr v4, v6

    .line 30975
    invoke-virtual {v3, v5, v4}, Landroid/widget/EdgeEffect;->setSize(II)V

    .line 30976
    :goto_0
    return-void

    .line 30977
    :cond_1
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->getMeasuredHeight()I

    move-result v1

    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->getMeasuredWidth()I

    move-result v0

    invoke-virtual {v3, v1, v0}, Landroid/widget/EdgeEffect;->setSize(II)V

    goto :goto_0

    :cond_2
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0
.end method

.method private final A0c()V
    .locals 4

    .line 30978
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0b:Landroid/widget/EdgeEffect;

    if-eqz v0, :cond_0

    .line 30979
    return-void

    .line 30980
    :cond_0
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->getContext()Landroid/content/Context;

    move-result-object v0

    new-instance v3, Landroid/widget/EdgeEffect;

    invoke-direct {v3, v0}, Landroid/widget/EdgeEffect;-><init>(Landroid/content/Context;)V

    iput-object v3, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0b:Landroid/widget/EdgeEffect;

    .line 30981
    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0B:Z

    if-eqz v0, :cond_1

    .line 30982
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->getMeasuredHeight()I

    move-result v2

    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->getPaddingTop()I

    move-result v0

    sub-int/2addr v2, v0

    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->getPaddingBottom()I

    move-result v0

    sub-int/2addr v2, v0

    .line 30983
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->getMeasuredWidth()I

    move-result v1

    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->getPaddingLeft()I

    move-result v0

    sub-int/2addr v1, v0

    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->getPaddingRight()I

    move-result v0

    sub-int/2addr v1, v0

    .line 30984
    invoke-virtual {v3, v2, v1}, Landroid/widget/EdgeEffect;->setSize(II)V

    .line 30985
    :goto_0
    return-void

    .line 30986
    :cond_1
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->getMeasuredHeight()I

    move-result v1

    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->getMeasuredWidth()I

    move-result v0

    invoke-virtual {v3, v1, v0}, Landroid/widget/EdgeEffect;->setSize(II)V

    goto :goto_0
.end method

.method private final A0d()V
    .locals 5

    .line 30987
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0c:Landroid/widget/EdgeEffect;

    if-eqz v0, :cond_0

    .line 30988
    return-void

    .line 30989
    :cond_0
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->getContext()Landroid/content/Context;

    move-result-object v0

    new-instance v4, Landroid/widget/EdgeEffect;

    invoke-direct {v4, v0}, Landroid/widget/EdgeEffect;-><init>(Landroid/content/Context;)V

    iput-object v4, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0c:Landroid/widget/EdgeEffect;

    .line 30990
    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0B:Z

    if-eqz v0, :cond_1

    .line 30991
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->getMeasuredWidth()I

    move-result v2

    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->getPaddingLeft()I

    move-result v0

    sub-int/2addr v2, v0

    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->getPaddingRight()I

    move-result v0

    sub-int/2addr v2, v0

    .line 30992
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->getMeasuredHeight()I

    move-result v1

    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->getPaddingTop()I

    move-result v0

    sub-int/2addr v1, v0

    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->getPaddingBottom()I

    move-result v0

    sub-int/2addr v1, v0

    .line 30993
    invoke-virtual {v4, v2, v1}, Landroid/widget/EdgeEffect;->setSize(II)V

    .line 30994
    :goto_0
    return-void

    .line 30995
    :cond_1
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->getMeasuredWidth()I

    move-result v3

    sget-object v1, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const/4 v0, 0x1

    aget-object v0, v1, v0

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v1

    const/16 v0, 0x20

    if-eq v1, v0, :cond_2

    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    :cond_2
    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const-string v1, "10JS50"

    const/4 v0, 0x2

    aput-object v1, v2, v0

    const-string v1, "NY78MD5eAiV9"

    const/4 v0, 0x7

    aput-object v1, v2, v0

    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->getMeasuredHeight()I

    move-result v0

    invoke-virtual {v4, v3, v0}, Landroid/widget/EdgeEffect;->setSize(II)V

    goto :goto_0
.end method

.method private final A0e()V
    .locals 2

    .line 30996
    new-instance v1, Lcom/facebook/ads/redexgen/X/Zi;

    invoke-direct {v1, p0}, Lcom/facebook/ads/redexgen/X/Zi;-><init>(Lcom/facebook/ads/redexgen/X/Eb;)V

    new-instance v0, Lcom/facebook/ads/redexgen/X/Zq;

    invoke-direct {v0, v1}, Lcom/facebook/ads/redexgen/X/Zq;-><init>(Lcom/facebook/ads/redexgen/X/48;)V

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A00:Lcom/facebook/ads/redexgen/X/Zq;

    .line 30997
    return-void
.end method

.method private final A0f()V
    .locals 1

    .line 30998
    const/4 v0, 0x0

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0Z:Landroid/widget/EdgeEffect;

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0c:Landroid/widget/EdgeEffect;

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0b:Landroid/widget/EdgeEffect;

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0a:Landroid/widget/EdgeEffect;

    .line 30999
    return-void
.end method

.method private final A0g()V
    .locals 4

    .line 31000
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A01:Lcom/facebook/ads/redexgen/X/4C;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/4C;->A06()I

    move-result v3

    .line 31001
    .local v0, "childCount":I
    const/4 v2, 0x0

    .local v1, "i":I
    :goto_0
    if-ge v2, v3, :cond_0

    .line 31002
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A01:Lcom/facebook/ads/redexgen/X/4C;

    invoke-virtual {v0, v2}, Lcom/facebook/ads/redexgen/X/4C;->A0A(I)Landroid/view/View;

    move-result-object v0

    .line 31003
    .local v2, "child":Landroid/view/View;
    invoke-virtual {v0}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    move-result-object v1

    check-cast v1, Lcom/facebook/ads/redexgen/X/4p;

    const/4 v0, 0x1

    iput-boolean v0, v1, Lcom/facebook/ads/redexgen/X/4p;->A01:Z

    .line 31004
    .end local v2    # "child":Landroid/view/View;
    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    .line 31005
    .end local v1    # "i":I
    :cond_0
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0r:Lcom/facebook/ads/redexgen/X/4w;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/4w;->A0M()V

    .line 31006
    return-void
.end method

.method private final A0h()V
    .locals 4

    .line 31007
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A01:Lcom/facebook/ads/redexgen/X/4C;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/4C;->A06()I

    move-result v3

    .line 31008
    .local v0, "childCount":I
    const/4 v2, 0x0

    .local v1, "i":I
    :goto_0
    if-ge v2, v3, :cond_1

    .line 31009
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A01:Lcom/facebook/ads/redexgen/X/4C;

    invoke-virtual {v0, v2}, Lcom/facebook/ads/redexgen/X/4C;->A0A(I)Landroid/view/View;

    move-result-object v0

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/Eb;->A0F(Landroid/view/View;)Lcom/facebook/ads/redexgen/X/56;

    move-result-object v1

    .line 31010
    .local v2, "holder":Lcom/facebook/ads/redexgen/X/56;
    if-eqz v1, :cond_0

    invoke-virtual {v1}, Lcom/facebook/ads/redexgen/X/56;->A0f()Z

    move-result v0

    if-nez v0, :cond_0

    .line 31011
    const/4 v0, 0x6

    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/56;->A0T(I)V

    .line 31012
    .end local v2    # "holder":Lcom/facebook/ads/redexgen/X/56;
    :cond_0
    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    .line 31013
    .end local v1    # "i":I
    :cond_1
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/Eb;->A0g()V

    .line 31014
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0r:Lcom/facebook/ads/redexgen/X/4w;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/4w;->A0N()V

    .line 31015
    return-void
.end method

.method private final A0i()V
    .locals 4

    .line 31016
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A01:Lcom/facebook/ads/redexgen/X/4C;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/4C;->A06()I

    move-result v3

    .line 31017
    .local v0, "childCount":I
    const/4 v2, 0x0

    .local v1, "i":I
    :goto_0
    if-ge v2, v3, :cond_1

    .line 31018
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A01:Lcom/facebook/ads/redexgen/X/4C;

    invoke-virtual {v0, v2}, Lcom/facebook/ads/redexgen/X/4C;->A0A(I)Landroid/view/View;

    move-result-object v0

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/Eb;->A0F(Landroid/view/View;)Lcom/facebook/ads/redexgen/X/56;

    move-result-object v1

    .line 31019
    .local v2, "holder":Lcom/facebook/ads/redexgen/X/56;
    invoke-virtual {v1}, Lcom/facebook/ads/redexgen/X/56;->A0f()Z

    move-result v0

    if-nez v0, :cond_0

    .line 31020
    invoke-virtual {v1}, Lcom/facebook/ads/redexgen/X/56;->A0R()V

    .line 31021
    .end local v2    # "holder":Lcom/facebook/ads/redexgen/X/56;
    :cond_0
    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    .line 31022
    .end local v1    # "i":I
    :cond_1
    return-void
.end method

.method private A0j(FFFF)V
    .locals 5

    .line 31023
    const/4 v1, 0x0

    .line 31024
    .local v0, "invalidate":Z
    const/high16 v3, 0x3f800000    # 1.0f

    const/4 v4, 0x0

    cmpg-float v0, p2, v4

    if-gez v0, :cond_5

    .line 31025
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/Eb;->A0b()V

    .line 31026
    iget-object v2, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0a:Landroid/widget/EdgeEffect;

    neg-float v1, p2

    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->getWidth()I

    move-result v0

    int-to-float v0, v0

    div-float/2addr v1, v0

    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->getHeight()I

    move-result v0

    int-to-float v0, v0

    div-float/2addr p3, v0

    sub-float v0, v3, p3

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/43;->A00(Landroid/widget/EdgeEffect;FF)V

    .line 31027
    const/4 v1, 0x1

    .line 31028
    :cond_0
    :goto_0
    cmpg-float v0, p4, v4

    if-gez v0, :cond_4

    .line 31029
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/Eb;->A0d()V

    .line 31030
    iget-object v2, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0c:Landroid/widget/EdgeEffect;

    neg-float v1, p4

    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->getHeight()I

    move-result v0

    int-to-float v0, v0

    div-float/2addr v1, v0

    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->getWidth()I

    move-result v0

    int-to-float v0, v0

    div-float/2addr p1, v0

    invoke-static {v2, v1, p1}, Lcom/facebook/ads/redexgen/X/43;->A00(Landroid/widget/EdgeEffect;FF)V

    .line 31031
    const/4 v1, 0x1

    .line 31032
    :cond_1
    :goto_1
    if-nez v1, :cond_2

    cmpl-float v0, p2, v4

    if-nez v0, :cond_2

    cmpl-float v0, p4, v4

    if-eqz v0, :cond_3

    .line 31033
    :cond_2
    invoke-static {p0}, Lcom/facebook/ads/redexgen/X/3T;->A07(Landroid/view/View;)V

    .line 31034
    :cond_3
    return-void

    .line 31035
    :cond_4
    cmpl-float v0, p4, v4

    if-lez v0, :cond_1

    .line 31036
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/Eb;->A0a()V

    .line 31037
    iget-object v2, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0Z:Landroid/widget/EdgeEffect;

    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->getHeight()I

    move-result v0

    int-to-float v0, v0

    div-float v1, p4, v0

    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->getWidth()I

    move-result v0

    int-to-float v0, v0

    div-float/2addr p1, v0

    sub-float/2addr v3, p1

    invoke-static {v2, v1, v3}, Lcom/facebook/ads/redexgen/X/43;->A00(Landroid/widget/EdgeEffect;FF)V

    .line 31038
    const/4 v1, 0x1

    goto :goto_1

    .line 31039
    :cond_5
    cmpl-float v0, p2, v4

    if-lez v0, :cond_0

    .line 31040
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/Eb;->A0c()V

    .line 31041
    iget-object v2, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0b:Landroid/widget/EdgeEffect;

    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->getWidth()I

    move-result v0

    int-to-float v0, v0

    div-float v1, p2, v0

    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->getHeight()I

    move-result v0

    int-to-float v0, v0

    div-float/2addr p3, v0

    invoke-static {v2, v1, p3}, Lcom/facebook/ads/redexgen/X/43;->A00(Landroid/widget/EdgeEffect;FF)V

    .line 31042
    const/4 v1, 0x1

    goto :goto_0
.end method

.method private final A0k(I)V
    .locals 4

    .line 31043
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    .line 31044
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0i:Lcom/facebook/ads/redexgen/X/4t;

    if-eqz v0, :cond_0

    .line 31045
    invoke-virtual {v0, p0, p1}, Lcom/facebook/ads/redexgen/X/4t;->A0L(Lcom/facebook/ads/redexgen/X/Eb;I)V

    .line 31046
    :cond_0
    iget-object v3, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0m:Ljava/util/List;

    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const/4 v0, 0x2

    aget-object v1, v2, v0

    const/4 v0, 0x7

    aget-object v0, v2, v0

    invoke-virtual {v1}, Ljava/lang/String;->length()I

    move-result v1

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v0

    if-eq v1, v0, :cond_2

    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const-string v1, "mB2qLC"

    const/4 v0, 0x2

    aput-object v1, v2, v0

    const-string v1, "mQXIqdAaLius"

    const/4 v0, 0x7

    aput-object v1, v2, v0

    if-eqz v3, :cond_1

    .line 31047
    invoke-interface {v3}, Ljava/util/List;->size()I

    move-result v0

    add-int/lit8 v1, v0, -0x1

    .local v0, "i":I
    :goto_0
    if-ltz v1, :cond_1

    .line 31048
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0m:Ljava/util/List;

    invoke-interface {v0, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/facebook/ads/redexgen/X/4t;

    invoke-virtual {v0, p0, p1}, Lcom/facebook/ads/redexgen/X/4t;->A0L(Lcom/facebook/ads/redexgen/X/Eb;I)V

    .line 31049
    add-int/lit8 v1, v1, -0x1

    goto :goto_0

    .line 31050
    .end local v0    # "i":I
    :cond_1
    return-void

    :cond_2
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0
.end method

.method private final A0l(IILandroid/view/animation/Interpolator;)V
    .locals 5

    .line 31051
    iget-object v4, p0, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    if-nez v4, :cond_0

    .line 31052
    const/16 v2, 0x55a

    const/16 v1, 0xc

    const/16 v0, 0x4f

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Eb;->A0I(III)Ljava/lang/String;

    move-result-object v3

    const/16 v2, 0x39c

    const/16 v1, 0x61

    const/16 v0, 0x4b

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Eb;->A0I(III)Ljava/lang/String;

    move-result-object v0

    invoke-static {v3, v0}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    .line 31053
    return-void

    .line 31054
    :cond_0
    iget-boolean v3, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0I:Z

    sget-object v1, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const/4 v0, 0x1

    aget-object v0, v1, v0

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v1

    const/16 v0, 0x20

    if-eq v1, v0, :cond_1

    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    :cond_1
    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const-string v1, "pqK8Y9"

    const/4 v0, 0x2

    aput-object v1, v2, v0

    const-string v1, "UiASdGbGWO9v"

    const/4 v0, 0x7

    aput-object v1, v2, v0

    if-eqz v3, :cond_2

    .line 31055
    return-void

    .line 31056
    :cond_2
    invoke-virtual {v4}, Lcom/facebook/ads/redexgen/X/4o;->A20()Z

    move-result v0

    if-nez v0, :cond_3

    .line 31057
    const/4 p1, 0x0

    .line 31058
    :cond_3
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/4o;->A21()Z

    move-result v0

    if-nez v0, :cond_4

    .line 31059
    const/4 p2, 0x0

    .line 31060
    :cond_4
    if-nez p1, :cond_5

    if-eqz p2, :cond_6

    .line 31061
    :cond_5
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A08:Lcom/facebook/ads/redexgen/X/55;

    invoke-virtual {v0, p1, p2, p3}, Lcom/facebook/ads/redexgen/X/55;->A0D(IILandroid/view/animation/Interpolator;)V

    .line 31062
    :cond_6
    return-void
.end method

.method private A0m(JLcom/facebook/ads/redexgen/X/56;Lcom/facebook/ads/redexgen/X/56;)V
    .locals 6

    .line 31063
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A01:Lcom/facebook/ads/redexgen/X/4C;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/4C;->A05()I

    move-result v5

    .line 31064
    .local v0, "childCount":I
    const/4 v3, 0x0

    .local v1, "i":I
    :goto_0
    if-ge v3, v5, :cond_3

    .line 31065
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A01:Lcom/facebook/ads/redexgen/X/4C;

    invoke-virtual {v0, v3}, Lcom/facebook/ads/redexgen/X/4C;->A09(I)Landroid/view/View;

    move-result-object v0

    .line 31066
    .local v2, "view":Landroid/view/View;
    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/Eb;->A0F(Landroid/view/View;)Lcom/facebook/ads/redexgen/X/56;

    move-result-object v4

    .line 31067
    .local v3, "other":Lcom/facebook/ads/redexgen/X/56;
    if-ne v4, p3, :cond_1

    .line 31068
    .end local v2    # "view":Landroid/view/View;
    .end local v3    # "other":Lcom/facebook/ads/redexgen/X/56;
    .end local v4
    :cond_0
    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    .line 31069
    :cond_1
    invoke-direct {p0, v4}, Lcom/facebook/ads/redexgen/X/Eb;->A0C(Lcom/facebook/ads/redexgen/X/56;)J

    move-result-wide v1

    .line 31070
    .local v4, "otherKey":J
    cmp-long v0, v1, p1

    if-nez v0, :cond_0

    .line 31071
    iget-object v3, p0, Lcom/facebook/ads/redexgen/X/Eb;->A04:Lcom/facebook/ads/redexgen/X/4c;

    const/4 v2, 0x1

    const/16 v1, 0x11

    const/16 v0, 0x41

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Eb;->A0I(III)Ljava/lang/String;

    move-result-object v5

    if-eqz v3, :cond_2

    invoke-virtual {v3}, Lcom/facebook/ads/redexgen/X/4c;->A0A()Z

    move-result v0

    if-eqz v0, :cond_2

    .line 31072
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const/16 v2, 0x6a0

    const/16 v1, 0x82

    const/16 v0, 0x4d

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Eb;->A0I(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v1

    .line 31073
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->A1H()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    new-instance v0, Ljava/lang/IllegalStateException;

    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw v0

    .line 31074
    :cond_2
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const/16 v2, 0x5e4

    const/16 v1, 0xbc

    const/16 v0, 0x74

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Eb;->A0I(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v1

    .line 31075
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->A1H()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    new-instance v0, Ljava/lang/IllegalStateException;

    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw v0

    .line 31076
    .end local v1    # "i":I
    :cond_3
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const/16 v2, 0x4b2

    const/16 v1, 0x6f

    const/16 v0, 0x5a

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Eb;->A0I(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p4}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v3

    const/16 v2, 0x12

    const/16 v1, 0x29

    const/16 v0, 0x27

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Eb;->A0I(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v1

    .line 31077
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->A1H()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    .line 31078
    const/16 v2, 0x55a

    const/16 v1, 0xc

    const/16 v0, 0x4f

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Eb;->A0I(III)Ljava/lang/String;

    move-result-object v0

    invoke-static {v0, v3}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    .line 31079
    return-void
.end method

.method private A0n(Landroid/view/MotionEvent;)V
    .locals 3

    .line 31080
    invoke-virtual {p1}, Landroid/view/MotionEvent;->getActionIndex()I

    move-result v2

    .line 31081
    .local v0, "actionIndex":I
    invoke-virtual {p1, v2}, Landroid/view/MotionEvent;->getPointerId(I)I

    move-result v1

    iget v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0V:I

    if-ne v1, v0, :cond_0

    .line 31082
    if-nez v2, :cond_1

    const/4 v2, 0x1

    .line 31083
    .local v1, "newIndex":I
    :goto_0
    invoke-virtual {p1, v2}, Landroid/view/MotionEvent;->getPointerId(I)I

    move-result v0

    iput v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0V:I

    .line 31084
    invoke-virtual {p1, v2}, Landroid/view/MotionEvent;->getX(I)F

    move-result v0

    const/high16 v1, 0x3f000000    # 0.5f

    add-float/2addr v0, v1

    float-to-int v0, v0

    iput v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0S:I

    iput v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0Q:I

    .line 31085
    invoke-virtual {p1, v2}, Landroid/view/MotionEvent;->getY(I)F

    move-result v0

    add-float/2addr v0, v1

    float-to-int v0, v0

    iput v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0T:I

    iput v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0R:I

    .line 31086
    .end local v1    # "newIndex":I
    :cond_0
    return-void

    .line 31087
    :cond_1
    const/4 v2, 0x0

    goto :goto_0
.end method

.method public static A0o(Landroid/view/View;Landroid/graphics/Rect;)V
    .locals 7

    .line 31088
    invoke-virtual {p0}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    move-result-object v6

    check-cast v6, Lcom/facebook/ads/redexgen/X/4p;

    .line 31089
    .local v0, "lp":Lcom/facebook/ads/redexgen/X/4p;
    iget-object v5, v6, Lcom/facebook/ads/redexgen/X/4p;->A03:Landroid/graphics/Rect;

    .line 31090
    .local v1, "insets":Landroid/graphics/Rect;
    invoke-virtual {p0}, Landroid/view/View;->getLeft()I

    move-result v4

    iget v0, v5, Landroid/graphics/Rect;->left:I

    sub-int/2addr v4, v0

    iget v0, v6, Lcom/facebook/ads/redexgen/X/4p;->leftMargin:I

    sub-int/2addr v4, v0

    .line 31091
    invoke-virtual {p0}, Landroid/view/View;->getTop()I

    move-result v3

    iget v0, v5, Landroid/graphics/Rect;->top:I

    sub-int/2addr v3, v0

    iget v0, v6, Lcom/facebook/ads/redexgen/X/4p;->topMargin:I

    sub-int/2addr v3, v0

    .line 31092
    invoke-virtual {p0}, Landroid/view/View;->getRight()I

    move-result v2

    iget v0, v5, Landroid/graphics/Rect;->right:I

    add-int/2addr v2, v0

    iget v0, v6, Lcom/facebook/ads/redexgen/X/4p;->rightMargin:I

    add-int/2addr v2, v0

    .line 31093
    invoke-virtual {p0}, Landroid/view/View;->getBottom()I

    move-result v1

    iget v0, v5, Landroid/graphics/Rect;->bottom:I

    add-int/2addr v1, v0

    iget v0, v6, Lcom/facebook/ads/redexgen/X/4p;->bottomMargin:I

    add-int/2addr v1, v0

    .line 31094
    invoke-virtual {p1, v4, v3, v2, v1}, Landroid/graphics/Rect;->set(IIII)V

    .line 31095
    return-void
.end method

.method private A0p(Landroid/view/View;Landroid/view/View;)V
    .locals 8

    .line 31096
    move-object v4, p1

    if-eqz p2, :cond_3

    move-object v5, p2

    .line 31097
    .local v0, "rectView":Landroid/view/View;
    :goto_0
    iget-object v3, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0p:Landroid/graphics/Rect;

    invoke-virtual {v5}, Landroid/view/View;->getWidth()I

    move-result v2

    invoke-virtual {v5}, Landroid/view/View;->getHeight()I

    move-result v1

    const/4 v0, 0x0

    invoke-virtual {v3, v0, v0, v2, v1}, Landroid/graphics/Rect;->set(IIII)V

    .line 31098
    invoke-virtual {v5}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    move-result-object v1

    .line 31099
    .local v1, "focusedLayoutParams":Landroid/view/ViewGroup$LayoutParams;
    instance-of v0, v1, Lcom/facebook/ads/redexgen/X/4p;

    if-eqz v0, :cond_0

    .line 31100
    check-cast v1, Lcom/facebook/ads/redexgen/X/4p;

    .line 31101
    .local v2, "lp":Lcom/facebook/ads/redexgen/X/4p;
    iget-boolean v0, v1, Lcom/facebook/ads/redexgen/X/4p;->A01:Z

    if-nez v0, :cond_0

    .line 31102
    iget-object v3, v1, Lcom/facebook/ads/redexgen/X/4p;->A03:Landroid/graphics/Rect;

    .line 31103
    .local v3, "insets":Landroid/graphics/Rect;
    iget-object v2, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0p:Landroid/graphics/Rect;

    iget v1, v2, Landroid/graphics/Rect;->left:I

    iget v0, v3, Landroid/graphics/Rect;->left:I

    sub-int/2addr v1, v0

    iput v1, v2, Landroid/graphics/Rect;->left:I

    .line 31104
    iget-object v2, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0p:Landroid/graphics/Rect;

    iget v1, v2, Landroid/graphics/Rect;->right:I

    iget v0, v3, Landroid/graphics/Rect;->right:I

    add-int/2addr v1, v0

    iput v1, v2, Landroid/graphics/Rect;->right:I

    .line 31105
    iget-object v2, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0p:Landroid/graphics/Rect;

    iget v1, v2, Landroid/graphics/Rect;->top:I

    iget v0, v3, Landroid/graphics/Rect;->top:I

    sub-int/2addr v1, v0

    iput v1, v2, Landroid/graphics/Rect;->top:I

    .line 31106
    iget-object v2, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0p:Landroid/graphics/Rect;

    iget v1, v2, Landroid/graphics/Rect;->bottom:I

    iget v0, v3, Landroid/graphics/Rect;->bottom:I

    add-int/2addr v1, v0

    iput v1, v2, Landroid/graphics/Rect;->bottom:I

    .line 31107
    .end local v2    # "lp":Lcom/facebook/ads/redexgen/X/4p;
    .end local v3    # "insets":Landroid/graphics/Rect;
    :cond_0
    if-eqz p2, :cond_1

    .line 31108
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0p:Landroid/graphics/Rect;

    invoke-virtual {p0, p2, v0}, Lcom/facebook/ads/redexgen/X/Eb;->offsetDescendantRectToMyCoords(Landroid/view/View;Landroid/graphics/Rect;)V

    .line 31109
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0p:Landroid/graphics/Rect;

    invoke-virtual {p0, v4, v0}, Lcom/facebook/ads/redexgen/X/Eb;->offsetRectIntoDescendantCoords(Landroid/view/View;Landroid/graphics/Rect;)V

    .line 31110
    :cond_1
    iget-object v2, p0, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    iget-object v5, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0p:Landroid/graphics/Rect;

    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0D:Z

    xor-int/lit8 v6, v0, 0x1

    if-nez p2, :cond_2

    const/4 v7, 0x1

    :goto_1
    move-object v3, p0

    invoke-virtual/range {v2 .. v7}, Lcom/facebook/ads/redexgen/X/4o;->A1a(Lcom/facebook/ads/redexgen/X/Eb;Landroid/view/View;Landroid/graphics/Rect;ZZ)Z

    .line 31111
    return-void

    .line 31112
    :cond_2
    const/4 v7, 0x0

    goto :goto_1

    .line 31113
    :cond_3
    move-object v5, v4

    goto :goto_0
.end method

.method private A0q(Lcom/facebook/ads/redexgen/X/4c;ZZ)V
    .locals 5

    .line 31114
    iget-object v4, p0, Lcom/facebook/ads/redexgen/X/Eb;->A04:Lcom/facebook/ads/redexgen/X/4c;

    if-eqz v4, :cond_0

    .line 31115
    iget-object v3, p0, Lcom/facebook/ads/redexgen/X/Eb;->A11:Lcom/facebook/ads/redexgen/X/Ze;

    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const/4 v0, 0x2

    aget-object v1, v2, v0

    const/4 v0, 0x7

    aget-object v0, v2, v0

    invoke-virtual {v1}, Ljava/lang/String;->length()I

    move-result v1

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v0

    if-eq v1, v0, :cond_4

    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const-string v1, "W0We85FJJqvSsXa60a6gd4EA7hDdiGkp"

    const/4 v0, 0x6

    aput-object v1, v2, v0

    const-string v1, "7sp9VzUFxAlBM78ItapDbGKr6NkpOVCZ"

    const/4 v0, 0x4

    aput-object v1, v2, v0

    invoke-virtual {v4, v3}, Lcom/facebook/ads/redexgen/X/4c;->A08(Lcom/facebook/ads/redexgen/X/4e;)V

    .line 31116
    :cond_0
    if-eqz p2, :cond_1

    if-eqz p3, :cond_2

    .line 31117
    :cond_1
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->A1N()V

    .line 31118
    :cond_2
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A00:Lcom/facebook/ads/redexgen/X/Zq;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/Zq;->A09()V

    .line 31119
    iget-object v2, p0, Lcom/facebook/ads/redexgen/X/Eb;->A04:Lcom/facebook/ads/redexgen/X/4c;

    .line 31120
    .local v0, "oldAdapter":Lcom/facebook/ads/redexgen/X/4c;
    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/Eb;->A04:Lcom/facebook/ads/redexgen/X/4c;

    .line 31121
    if-eqz p1, :cond_3

    .line 31122
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A11:Lcom/facebook/ads/redexgen/X/Ze;

    invoke-virtual {p1, v0}, Lcom/facebook/ads/redexgen/X/4c;->A07(Lcom/facebook/ads/redexgen/X/4e;)V

    .line 31123
    :cond_3
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    .line 31124
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0r:Lcom/facebook/ads/redexgen/X/4w;

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A04:Lcom/facebook/ads/redexgen/X/4c;

    invoke-virtual {v1, v2, v0, p2}, Lcom/facebook/ads/redexgen/X/4w;->A0U(Lcom/facebook/ads/redexgen/X/4c;Lcom/facebook/ads/redexgen/X/4c;Z)V

    .line 31125
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0s:Lcom/facebook/ads/redexgen/X/53;

    const/4 v0, 0x1

    iput-boolean v0, v1, Lcom/facebook/ads/redexgen/X/53;->A0D:Z

    .line 31126
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->A1P()V

    .line 31127
    return-void

    :cond_4
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0
.end method

.method private A0r(Lcom/facebook/ads/redexgen/X/56;)V
    .locals 6

    .line 31128
    iget-object v3, p1, Lcom/facebook/ads/redexgen/X/56;->A0H:Landroid/view/View;

    .line 31129
    .local v0, "view":Landroid/view/View;
    invoke-virtual {v3}, Landroid/view/View;->getParent()Landroid/view/ViewParent;

    move-result-object v0

    const/4 v4, 0x1

    if-ne v0, p0, :cond_2

    const/4 v2, 0x1

    .line 31130
    .local v1, "alreadyParented":Z
    :goto_0
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0r:Lcom/facebook/ads/redexgen/X/4w;

    invoke-virtual {p0, v3}, Lcom/facebook/ads/redexgen/X/Eb;->A1G(Landroid/view/View;)Lcom/facebook/ads/redexgen/X/56;

    move-result-object v0

    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/4w;->A0Y(Lcom/facebook/ads/redexgen/X/56;)V

    .line 31131
    invoke-virtual {p1}, Lcom/facebook/ads/redexgen/X/56;->A0c()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 31132
    iget-object v5, p0, Lcom/facebook/ads/redexgen/X/Eb;->A01:Lcom/facebook/ads/redexgen/X/4C;

    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const/4 v0, 0x3

    aget-object v1, v2, v0

    const/4 v0, 0x0

    aget-object v0, v2, v0

    invoke-virtual {v1}, Ljava/lang/String;->length()I

    move-result v1

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v0

    if-eq v1, v0, :cond_3

    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const-string v1, "yAMck033yUmsKAwrNaBuaPdrYuAtD8a4"

    const/4 v0, 0x6

    aput-object v1, v2, v0

    const-string v1, "2F1g5meBaFaT8Ughfaz4Efd5BOFwzFG2"

    const/4 v0, 0x4

    aput-object v1, v2, v0

    const/4 v1, -0x1

    invoke-virtual {v3}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    move-result-object v0

    invoke-virtual {v5, v3, v1, v0, v4}, Lcom/facebook/ads/redexgen/X/4C;->A0H(Landroid/view/View;ILandroid/view/ViewGroup$LayoutParams;Z)V

    .line 31133
    :goto_1
    return-void

    .line 31134
    :cond_0
    if-nez v2, :cond_1

    .line 31135
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A01:Lcom/facebook/ads/redexgen/X/4C;

    invoke-virtual {v0, v3, v4}, Lcom/facebook/ads/redexgen/X/4C;->A0J(Landroid/view/View;Z)V

    goto :goto_1

    .line 31136
    :cond_1
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A01:Lcom/facebook/ads/redexgen/X/4C;

    invoke-virtual {v0, v3}, Lcom/facebook/ads/redexgen/X/4C;->A0E(Landroid/view/View;)V

    goto :goto_1

    .line 31137
    :cond_2
    const/4 v2, 0x0

    goto :goto_0

    :cond_3
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0
.end method

.method public static A0s(Lcom/facebook/ads/redexgen/X/56;)V
    .locals 4

    .line 31138
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/56;->A09:Ljava/lang/ref/WeakReference;

    if-eqz v0, :cond_4

    .line 31139
    iget-object v3, p0, Lcom/facebook/ads/redexgen/X/56;->A09:Ljava/lang/ref/WeakReference;

    sget-object v1, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const/4 v0, 0x1

    aget-object v0, v1, v0

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v1

    const/16 v0, 0x20

    if-eq v1, v0, :cond_0

    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    :cond_0
    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const-string v1, "Lp0Vudgnjyr9htx58aJuOXSG6zR0Rms0"

    const/4 v0, 0x6

    aput-object v1, v2, v0

    const-string v1, "QnOoNSP4qihOpTYseackLt73SA9jbFjz"

    const/4 v0, 0x4

    aput-object v1, v2, v0

    invoke-virtual {v3}, Ljava/lang/ref/WeakReference;->get()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Landroid/view/View;

    .line 31140
    .local v0, "item":Landroid/view/View;
    :goto_0
    if-eqz v1, :cond_3

    .line 31141
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/56;->A0H:Landroid/view/View;

    if-ne v1, v0, :cond_1

    .line 31142
    return-void

    .line 31143
    :cond_1
    invoke-virtual {v1}, Landroid/view/View;->getParent()Landroid/view/ViewParent;

    move-result-object v1

    .line 31144
    .local v1, "parent":Landroid/view/ViewParent;
    instance-of v0, v1, Landroid/view/View;

    if-eqz v0, :cond_2

    .line 31145
    check-cast v1, Landroid/view/View;

    goto :goto_0

    .line 31146
    :cond_2
    const/4 v1, 0x0

    goto :goto_0

    .line 31147
    :cond_3
    const/4 v0, 0x0

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/56;->A09:Ljava/lang/ref/WeakReference;

    .line 31148
    .end local v0    # "item":Landroid/view/View;
    :cond_4
    return-void
.end method

.method private A0t(Lcom/facebook/ads/redexgen/X/56;Lcom/facebook/ads/redexgen/X/56;Lcom/facebook/ads/redexgen/X/4j;Lcom/facebook/ads/redexgen/X/4j;ZZ)V
    .locals 2

    .line 31149
    const/4 v1, 0x0

    invoke-virtual {p1, v1}, Lcom/facebook/ads/redexgen/X/56;->A0X(Z)V

    .line 31150
    if-eqz p5, :cond_0

    .line 31151
    invoke-direct {p0, p1}, Lcom/facebook/ads/redexgen/X/Eb;->A0r(Lcom/facebook/ads/redexgen/X/56;)V

    .line 31152
    :cond_0
    if-eq p1, p2, :cond_2

    .line 31153
    if-eqz p6, :cond_1

    .line 31154
    invoke-direct {p0, p2}, Lcom/facebook/ads/redexgen/X/Eb;->A0r(Lcom/facebook/ads/redexgen/X/56;)V

    .line 31155
    :cond_1
    iput-object p2, p1, Lcom/facebook/ads/redexgen/X/56;->A06:Lcom/facebook/ads/redexgen/X/56;

    .line 31156
    invoke-direct {p0, p1}, Lcom/facebook/ads/redexgen/X/Eb;->A0r(Lcom/facebook/ads/redexgen/X/56;)V

    .line 31157
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0r:Lcom/facebook/ads/redexgen/X/4w;

    invoke-virtual {v0, p1}, Lcom/facebook/ads/redexgen/X/4w;->A0Y(Lcom/facebook/ads/redexgen/X/56;)V

    .line 31158
    invoke-virtual {p2, v1}, Lcom/facebook/ads/redexgen/X/56;->A0X(Z)V

    .line 31159
    iput-object p1, p2, Lcom/facebook/ads/redexgen/X/56;->A07:Lcom/facebook/ads/redexgen/X/56;

    .line 31160
    :cond_2
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A05:Lcom/facebook/ads/redexgen/X/4k;

    invoke-virtual {v0, p1, p2, p3, p4}, Lcom/facebook/ads/redexgen/X/4k;->A0H(Lcom/facebook/ads/redexgen/X/56;Lcom/facebook/ads/redexgen/X/56;Lcom/facebook/ads/redexgen/X/4j;Lcom/facebook/ads/redexgen/X/4j;)Z

    move-result v0

    if-eqz v0, :cond_3

    .line 31161
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->A1M()V

    .line 31162
    :cond_3
    return-void
.end method

.method public static synthetic A0u(Lcom/facebook/ads/redexgen/X/Eb;I)V
    .locals 0

    .line 31163
    invoke-virtual {p0, p1}, Lcom/facebook/ads/redexgen/X/Eb;->detachViewFromParent(I)V

    return-void
.end method

.method public static synthetic A0v(Lcom/facebook/ads/redexgen/X/Eb;II)V
    .locals 0

    .line 31164
    invoke-virtual {p0, p1, p2}, Lcom/facebook/ads/redexgen/X/Eb;->setMeasuredDimension(II)V

    return-void
.end method

.method public static synthetic A0w(Lcom/facebook/ads/redexgen/X/Eb;Landroid/view/View;ILandroid/view/ViewGroup$LayoutParams;)V
    .locals 0

    .line 31165
    invoke-virtual {p0, p1, p2, p3}, Lcom/facebook/ads/redexgen/X/Eb;->attachViewToParent(Landroid/view/View;ILandroid/view/ViewGroup$LayoutParams;)V

    return-void
.end method

.method private A0x([I)V
    .locals 8

    .line 31166
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A01:Lcom/facebook/ads/redexgen/X/4C;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/4C;->A05()I

    move-result v7

    .line 31167
    .local v0, "count":I
    const/4 v6, 0x0

    const/4 v5, 0x1

    if-nez v7, :cond_0

    .line 31168
    const/4 v0, -0x1

    aput v0, p1, v6

    .line 31169
    aput v0, p1, v5

    .line 31170
    return-void

    .line 31171
    :cond_0
    const v4, 0x7fffffff

    .line 31172
    .local v3, "minPositionPreLayout":I
    const/high16 v3, -0x80000000

    .line 31173
    .local v4, "maxPositionPreLayout":I
    const/4 v2, 0x0

    .local v5, "i":I
    :goto_0
    if-ge v2, v7, :cond_4

    .line 31174
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A01:Lcom/facebook/ads/redexgen/X/4C;

    invoke-virtual {v0, v2}, Lcom/facebook/ads/redexgen/X/4C;->A09(I)Landroid/view/View;

    move-result-object v0

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/Eb;->A0F(Landroid/view/View;)Lcom/facebook/ads/redexgen/X/56;

    move-result-object v1

    .line 31175
    .local v6, "holder":Lcom/facebook/ads/redexgen/X/56;
    invoke-virtual {v1}, Lcom/facebook/ads/redexgen/X/56;->A0f()Z

    move-result v0

    if-eqz v0, :cond_2

    .line 31176
    .end local v6    # "holder":Lcom/facebook/ads/redexgen/X/56;
    .end local v7
    :cond_1
    :goto_1
    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    .line 31177
    :cond_2
    invoke-virtual {v1}, Lcom/facebook/ads/redexgen/X/56;->A0I()I

    move-result v0

    .line 31178
    .local v7, "pos":I
    if-ge v0, v4, :cond_3

    .line 31179
    move v4, v0

    .line 31180
    :cond_3
    if-le v0, v3, :cond_1

    .line 31181
    move v3, v0

    goto :goto_1

    .line 31182
    .end local v5    # "i":I
    :cond_4
    aput v4, p1, v6

    .line 31183
    aput v3, p1, v5

    .line 31184
    return-void
.end method

.method private A0y()Z
    .locals 4

    .line 31185
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A01:Lcom/facebook/ads/redexgen/X/4C;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/4C;->A05()I

    move-result v3

    .line 31186
    .local v0, "childCount":I
    const/4 v2, 0x0

    .local v1, "i":I
    :goto_0
    if-ge v2, v3, :cond_2

    .line 31187
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A01:Lcom/facebook/ads/redexgen/X/4C;

    invoke-virtual {v0, v2}, Lcom/facebook/ads/redexgen/X/4C;->A09(I)Landroid/view/View;

    move-result-object v0

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/Eb;->A0F(Landroid/view/View;)Lcom/facebook/ads/redexgen/X/56;

    move-result-object v1

    .line 31188
    .local v2, "holder":Lcom/facebook/ads/redexgen/X/56;
    if-eqz v1, :cond_0

    invoke-virtual {v1}, Lcom/facebook/ads/redexgen/X/56;->A0f()Z

    move-result v0

    if-eqz v0, :cond_1

    .line 31189
    .end local v2    # "holder":Lcom/facebook/ads/redexgen/X/56;
    :cond_0
    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    .line 31190
    :cond_1
    invoke-virtual {v1}, Lcom/facebook/ads/redexgen/X/56;->A0d()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 31191
    const/4 v0, 0x1

    return v0

    .line 31192
    .end local v1    # "i":I
    :cond_2
    const/4 v0, 0x0

    return v0
.end method

.method private A0z()Z
    .locals 1

    .line 31193
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A05:Lcom/facebook/ads/redexgen/X/4k;

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/4o;->A22()Z

    move-result v0

    if-eqz v0, :cond_0

    const/4 v0, 0x1

    :goto_0
    return v0

    :cond_0
    const/4 v0, 0x0

    goto :goto_0
.end method

.method public static synthetic A10()Z
    .locals 1

    .line 31194
    sget-boolean v0, Lcom/facebook/ads/redexgen/X/Eb;->A1E:Z

    return v0
.end method

.method private A11(II)Z
    .locals 4

    .line 31195
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A14:[I

    invoke-direct {p0, v0}, Lcom/facebook/ads/redexgen/X/Eb;->A0x([I)V

    .line 31196
    iget-object v3, p0, Lcom/facebook/ads/redexgen/X/Eb;->A14:[I

    const/4 v2, 0x0

    aget v1, v3, v2

    const/4 v0, 0x1

    if-ne v1, p1, :cond_0

    aget v0, v3, v0

    if-eq v0, p2, :cond_1

    :cond_0
    const/4 v2, 0x1

    :cond_1
    return v2
.end method

.method private final A12(II)Z
    .locals 7

    .line 31197
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    const/4 v6, 0x0

    if-nez v1, :cond_0

    .line 31198
    const/16 v2, 0x55a

    const/16 v1, 0xc

    const/16 v0, 0x4f

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Eb;->A0I(III)Ljava/lang/String;

    move-result-object v3

    const/16 v2, 0x28b

    const/16 v1, 0x59

    const/16 v0, 0x4c

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Eb;->A0I(III)Ljava/lang/String;

    move-result-object v0

    invoke-static {v3, v0}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    .line 31199
    return v6

    .line 31200
    :cond_0
    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0I:Z

    if-eqz v0, :cond_1

    .line 31201
    return v6

    .line 31202
    :cond_1
    invoke-virtual {v1}, Lcom/facebook/ads/redexgen/X/4o;->A20()Z

    move-result v5

    .line 31203
    .local v0, "canScrollHorizontal":Z
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/4o;->A21()Z

    move-result v4

    .line 31204
    .local v2, "canScrollVertical":Z
    if-eqz v5, :cond_2

    invoke-static {p1}, Ljava/lang/Math;->abs(I)I

    move-result v1

    iget v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0y:I

    if-ge v1, v0, :cond_3

    .line 31205
    :cond_2
    const/4 p1, 0x0

    .line 31206
    :cond_3
    if-eqz v4, :cond_4

    invoke-static {p2}, Ljava/lang/Math;->abs(I)I

    move-result v1

    iget v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0y:I

    if-ge v1, v0, :cond_5

    .line 31207
    :cond_4
    const/4 p2, 0x0

    .line 31208
    :cond_5
    if-nez p1, :cond_6

    if-nez p2, :cond_6

    .line 31209
    return v6

    .line 31210
    :cond_6
    int-to-float v1, p1

    int-to-float v0, p2

    invoke-virtual {p0, v1, v0}, Lcom/facebook/ads/redexgen/X/Eb;->dispatchNestedPreFling(FF)Z

    move-result v0

    if-nez v0, :cond_c

    .line 31211
    const/4 v3, 0x1

    if-nez v5, :cond_7

    if-eqz v4, :cond_8

    :cond_7
    const/4 v2, 0x1

    .line 31212
    .local v4, "canScroll":Z
    :goto_0
    int-to-float v1, p1

    int-to-float v0, p2

    invoke-virtual {p0, v1, v0, v2}, Lcom/facebook/ads/redexgen/X/Eb;->dispatchNestedFling(FFZ)Z

    .line 31213
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0g:Lcom/facebook/ads/redexgen/X/4r;

    if-eqz v0, :cond_9

    invoke-virtual {v0, p1, p2}, Lcom/facebook/ads/redexgen/X/4r;->A0B(II)Z

    move-result v0

    if-eqz v0, :cond_9

    .line 31214
    return v3

    .line 31215
    :cond_8
    const/4 v2, 0x0

    goto :goto_0

    .line 31216
    :cond_9
    if-eqz v2, :cond_c

    .line 31217
    const/4 v0, 0x0

    .line 31218
    .local v1, "nestedScrollAxis":I
    if-eqz v5, :cond_a

    .line 31219
    or-int/lit8 v0, v0, 0x1

    .line 31220
    :cond_a
    if-eqz v4, :cond_b

    .line 31221
    or-int/lit8 v0, v0, 0x2

    .line 31222
    :cond_b
    invoke-virtual {p0, v0, v3}, Lcom/facebook/ads/redexgen/X/Eb;->A1s(II)Z

    .line 31223
    iget v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0x:I

    neg-int v1, v0

    invoke-static {p1, v0}, Ljava/lang/Math;->min(II)I

    move-result v0

    invoke-static {v1, v0}, Ljava/lang/Math;->max(II)I

    move-result v2

    .line 31224
    iget v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0x:I

    neg-int v1, v0

    invoke-static {p2, v0}, Ljava/lang/Math;->min(II)I

    move-result v0

    invoke-static {v1, v0}, Ljava/lang/Math;->max(II)I

    move-result v1

    .line 31225
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A08:Lcom/facebook/ads/redexgen/X/55;

    invoke-virtual {v0, v2, v1}, Lcom/facebook/ads/redexgen/X/55;->A09(II)V

    .line 31226
    return v3

    .line 31227
    .end local v1    # "nestedScrollAxis":I
    .end local v4    # "canScroll":Z
    :cond_c
    return v6
.end method

.method private final A13(IILandroid/view/MotionEvent;)Z
    .locals 17

    .line 31228
    move-object/from16 v4, p0

    move-object v4, v4

    const/4 v13, 0x0

    .local v0, "unconsumedX":I
    const/4 v14, 0x0

    .line 31229
    .local v1, "unconsumedY":I
    const/4 v11, 0x0

    .local v2, "consumedX":I
    const/4 v12, 0x0

    .line 31230
    .local v3, "consumedY":I
    invoke-virtual {v4}, Lcom/facebook/ads/redexgen/X/Eb;->A1I()V

    .line 31231
    iget-object v0, v4, Lcom/facebook/ads/redexgen/X/Eb;->A04:Lcom/facebook/ads/redexgen/X/4c;

    const/4 v3, 0x0

    move/from16 v6, p1

    move/from16 v5, p2

    if-eqz v0, :cond_2

    .line 31232
    invoke-virtual {v4}, Lcom/facebook/ads/redexgen/X/Eb;->A1J()V

    .line 31233
    invoke-virtual {v4}, Lcom/facebook/ads/redexgen/X/Eb;->A1K()V

    .line 31234
    const/16 v2, 0x551

    const/16 v1, 0x9

    const/16 v0, 0x4b

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Eb;->A0I(III)Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/2q;->A01(Ljava/lang/String;)V

    .line 31235
    iget-object v0, v4, Lcom/facebook/ads/redexgen/X/Eb;->A0s:Lcom/facebook/ads/redexgen/X/53;

    invoke-virtual {v4, v0}, Lcom/facebook/ads/redexgen/X/Eb;->A1h(Lcom/facebook/ads/redexgen/X/53;)V

    .line 31236
    if-eqz v6, :cond_0

    .line 31237
    iget-object v2, v4, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    iget-object v1, v4, Lcom/facebook/ads/redexgen/X/Eb;->A0r:Lcom/facebook/ads/redexgen/X/4w;

    iget-object v0, v4, Lcom/facebook/ads/redexgen/X/Eb;->A0s:Lcom/facebook/ads/redexgen/X/53;

    invoke-virtual {v2, v6, v1, v0}, Lcom/facebook/ads/redexgen/X/4o;->A1d(ILcom/facebook/ads/redexgen/X/4w;Lcom/facebook/ads/redexgen/X/53;)I

    move-result v11

    .line 31238
    sub-int v13, v6, v11

    .line 31239
    :cond_0
    if-eqz v5, :cond_1

    .line 31240
    iget-object v2, v4, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    iget-object v1, v4, Lcom/facebook/ads/redexgen/X/Eb;->A0r:Lcom/facebook/ads/redexgen/X/4w;

    iget-object v0, v4, Lcom/facebook/ads/redexgen/X/Eb;->A0s:Lcom/facebook/ads/redexgen/X/53;

    invoke-virtual {v2, v5, v1, v0}, Lcom/facebook/ads/redexgen/X/4o;->A1e(ILcom/facebook/ads/redexgen/X/4w;Lcom/facebook/ads/redexgen/X/53;)I

    move-result v12

    .line 31241
    sub-int v14, v5, v12

    .line 31242
    :cond_1
    invoke-static {}, Lcom/facebook/ads/redexgen/X/2q;->A00()V

    .line 31243
    invoke-virtual {v4}, Lcom/facebook/ads/redexgen/X/Eb;->A1O()V

    .line 31244
    invoke-virtual {v4}, Lcom/facebook/ads/redexgen/X/Eb;->A1L()V

    .line 31245
    invoke-virtual {v4, v3}, Lcom/facebook/ads/redexgen/X/Eb;->A1n(Z)V

    .line 31246
    .end local v0    # "unconsumedX":I
    .end local v1    # "unconsumedY":I
    .end local v2    # "consumedX":I
    .end local v3    # "consumedY":I
    .local v12, "unconsumedX":I
    .local v13, "unconsumedY":I
    .local v14, "consumedX":I
    .local v15, "consumedY":I
    :cond_2
    iget-object v0, v4, Lcom/facebook/ads/redexgen/X/Eb;->A0v:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->isEmpty()Z

    move-result v0

    if-nez v0, :cond_3

    .line 31247
    invoke-virtual {v4}, Lcom/facebook/ads/redexgen/X/Eb;->invalidate()V

    .line 31248
    :cond_3
    iget-object v15, v4, Lcom/facebook/ads/redexgen/X/Eb;->A17:[I

    const/16 v16, 0x0

    move-object v10, v4

    invoke-virtual/range {v10 .. v16}, Lcom/facebook/ads/redexgen/X/Eb;->A1t(IIII[II)Z

    move-result v0

    const/4 v8, 0x1

    move-object/from16 v2, p3

    if-eqz v0, :cond_5

    .line 31249
    iget v1, v4, Lcom/facebook/ads/redexgen/X/Eb;->A0S:I

    iget-object v0, v4, Lcom/facebook/ads/redexgen/X/Eb;->A17:[I

    aget v5, v0, v3

    sub-int/2addr v1, v5

    iput v1, v4, Lcom/facebook/ads/redexgen/X/Eb;->A0S:I

    .line 31250
    iget v1, v4, Lcom/facebook/ads/redexgen/X/Eb;->A0T:I

    aget v0, v0, v8

    sub-int/2addr v1, v0

    iput v1, v4, Lcom/facebook/ads/redexgen/X/Eb;->A0T:I

    .line 31251
    if-eqz v2, :cond_4

    .line 31252
    int-to-float v1, v5

    int-to-float v0, v0

    invoke-virtual {v2, v1, v0}, Landroid/view/MotionEvent;->offsetLocation(FF)V

    .line 31253
    :cond_4
    iget-object v5, v4, Lcom/facebook/ads/redexgen/X/Eb;->A15:[I

    aget v7, v5, v3

    iget-object v6, v4, Lcom/facebook/ads/redexgen/X/Eb;->A17:[I

    aget v0, v6, v3

    add-int/2addr v7, v0

    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const/4 v0, 0x6

    aget-object v1, v2, v0

    const/4 v0, 0x4

    aget-object v2, v2, v0

    const/16 v0, 0x11

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    invoke-virtual {v2, v0}, Ljava/lang/String;->charAt(I)C

    move-result v0

    if-eq v1, v0, :cond_8

    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    .line 31254
    :cond_5
    invoke-virtual {v4}, Lcom/facebook/ads/redexgen/X/Eb;->getOverScrollMode()I

    move-result v1

    const/4 v0, 0x2

    if-eq v1, v0, :cond_9

    .line 31255
    if-eqz v2, :cond_7

    const/16 v0, 0x2002

    invoke-static {v2, v0}, Lcom/facebook/ads/redexgen/X/39;->A00(Landroid/view/MotionEvent;I)Z

    move-result v0

    if-nez v0, :cond_7

    .line 31256
    invoke-virtual {v2}, Landroid/view/MotionEvent;->getX()F

    move-result v8

    int-to-float v9, v13

    invoke-virtual {v2}, Landroid/view/MotionEvent;->getY()F

    move-result v7

    int-to-float v10, v14

    sget-object v1, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const/4 v0, 0x1

    aget-object v0, v1, v0

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v1

    const/16 v0, 0x20

    if-eq v1, v0, :cond_6

    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    :cond_6
    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const-string v1, "Rb7Wd2rB1Bk4FJnk1H23iqiZozoKDKpI"

    const/4 v0, 0x1

    aput-object v1, v2, v0

    invoke-direct {v4, v8, v9, v7, v10}, Lcom/facebook/ads/redexgen/X/Eb;->A0j(FFFF)V

    .line 31257
    :cond_7
    invoke-virtual {v4, v6, v5}, Lcom/facebook/ads/redexgen/X/Eb;->A1Z(II)V

    goto :goto_0

    .line 31258
    :cond_8
    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const-string v1, "TzjapWGLjdNwrlMHhQTvw0KTjubTnl9R"

    const/4 v0, 0x1

    aput-object v1, v2, v0

    aput v7, v5, v3

    .line 31259
    aget v1, v5, v8

    aget v0, v6, v8

    add-int/2addr v1, v0

    aput v1, v5, v8

    .line 31260
    :cond_9
    :goto_0
    if-nez v11, :cond_a

    if-eqz v12, :cond_b

    .line 31261
    :cond_a
    invoke-virtual {v4, v11, v12}, Lcom/facebook/ads/redexgen/X/Eb;->A1b(II)V

    .line 31262
    :cond_b
    invoke-virtual {v4}, Lcom/facebook/ads/redexgen/X/Eb;->awakenScrollBars()Z

    move-result v0

    if-nez v0, :cond_c

    .line 31263
    invoke-virtual {v4}, Lcom/facebook/ads/redexgen/X/Eb;->invalidate()V

    .line 31264
    :cond_c
    if-nez v11, :cond_d

    if-eqz v12, :cond_e

    :cond_d
    const/4 v3, 0x1

    :cond_e
    return v3
.end method

.method private A14(Landroid/view/MotionEvent;)Z
    .locals 4

    .line 31265
    invoke-virtual {p1}, Landroid/view/MotionEvent;->getAction()I

    move-result v3

    .line 31266
    .local v0, "action":I
    const/4 v0, 0x0

    const/4 v2, 0x1

    if-eqz v0, :cond_0

    .line 31267
    const/4 v1, 0x0

    if-nez v3, :cond_2

    .line 31268
    iput-object v1, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0h:Lcom/facebook/ads/redexgen/X/4s;

    .line 31269
    :cond_0
    if-eqz v3, :cond_1

    .line 31270
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A13:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->size()I

    move-result v0

    .line 31271
    .local v1, "listenerCount":I
    const/4 v1, 0x0

    .local v3, "i":I
    if-ge v1, v0, :cond_1

    .line 31272
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A13:Ljava/util/ArrayList;

    invoke-virtual {v0, v1}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    .line 31273
    .local p0, "listener":Lcom/facebook/ads/redexgen/X/4s;
    const/16 v2, 0x7b5

    const/16 v1, 0x15

    const/16 v0, 0x4e

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Eb;->A0I(III)Ljava/lang/String;

    move-result-object v1

    new-instance v0, Ljava/lang/NullPointerException;

    invoke-direct {v0, v1}, Ljava/lang/NullPointerException;-><init>(Ljava/lang/String;)V

    throw v0

    .line 31274
    .end local v1    # "listenerCount":I
    .end local v3    # "i":I
    :cond_1
    const/4 v0, 0x0

    return v0

    .line 31275
    :cond_2
    const/4 v0, 0x3

    if-eq v3, v0, :cond_3

    if-ne v3, v2, :cond_4

    .line 31276
    :cond_3
    iput-object v1, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0h:Lcom/facebook/ads/redexgen/X/4s;

    .line 31277
    :cond_4
    return v2
.end method

.method private A15(Landroid/view/MotionEvent;)Z
    .locals 3

    .line 31278
    invoke-virtual {p1}, Landroid/view/MotionEvent;->getAction()I

    move-result v1

    .line 31279
    .local v0, "action":I
    const/4 v0, 0x3

    if-eq v1, v0, :cond_0

    if-nez v1, :cond_1

    .line 31280
    :cond_0
    const/4 v0, 0x0

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0h:Lcom/facebook/ads/redexgen/X/4s;

    .line 31281
    :cond_1
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A13:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->size()I

    move-result v0

    .line 31282
    .local v2, "listenerCount":I
    const/4 v1, 0x0

    .local p0, "i":I
    if-ge v1, v0, :cond_2

    .line 31283
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A13:Ljava/util/ArrayList;

    invoke-virtual {v0, v1}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    .line 31284
    .local p1, "listener":Lcom/facebook/ads/redexgen/X/4s;
    const/16 v2, 0x7b5

    const/16 v1, 0x15

    const/16 v0, 0x4e

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Eb;->A0I(III)Ljava/lang/String;

    move-result-object v1

    new-instance v0, Ljava/lang/NullPointerException;

    invoke-direct {v0, v1}, Ljava/lang/NullPointerException;-><init>(Ljava/lang/String;)V

    throw v0

    .line 31285
    .end local p0    # "i":I
    :cond_2
    const/4 v0, 0x0

    return v0
.end method

.method private A16(Landroid/view/View;Landroid/view/View;I)Z
    .locals 4

    .line 31286
    const/4 v3, 0x0

    if-eqz p2, :cond_0

    if-ne p2, p0, :cond_1

    .line 31287
    .end local v0
    .end local v3
    :cond_0
    return v3

    .line 31288
    :cond_1
    const/4 v2, 0x1

    if-nez p1, :cond_2

    .line 31289
    return v2

    .line 31290
    :cond_2
    const/4 v1, 0x2

    if-eq p3, v1, :cond_3

    if-ne p3, v2, :cond_9

    .line 31291
    :cond_3
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/4o;->A0a()I

    move-result v0

    if-ne v0, v2, :cond_6

    const/4 v0, 0x1

    .line 31292
    .local v3, "rtl":Z
    :goto_0
    if-ne p3, v1, :cond_4

    const/4 v3, 0x1

    :cond_4
    xor-int/2addr v3, v0

    if-eqz v3, :cond_5

    .line 31293
    const/16 v0, 0x42

    .line 31294
    .local v0, "absHorizontal":I
    :goto_1
    invoke-direct {p0, p1, p2, v0}, Lcom/facebook/ads/redexgen/X/Eb;->A17(Landroid/view/View;Landroid/view/View;I)Z

    move-result v0

    if-eqz v0, :cond_7

    .line 31295
    return v2

    .line 31296
    :cond_5
    const/16 v0, 0x11

    goto :goto_1

    .line 31297
    :cond_6
    const/4 v0, 0x0

    goto :goto_0

    .line 31298
    :cond_7
    if-ne p3, v1, :cond_8

    .line 31299
    const/16 v0, 0x82

    invoke-direct {p0, p1, p2, v0}, Lcom/facebook/ads/redexgen/X/Eb;->A17(Landroid/view/View;Landroid/view/View;I)Z

    move-result v0

    return v0

    .line 31300
    :cond_8
    const/16 v0, 0x21

    invoke-direct {p0, p1, p2, v0}, Lcom/facebook/ads/redexgen/X/Eb;->A17(Landroid/view/View;Landroid/view/View;I)Z

    move-result v0

    return v0

    .line 31301
    :cond_9
    invoke-direct {p0, p1, p2, p3}, Lcom/facebook/ads/redexgen/X/Eb;->A17(Landroid/view/View;Landroid/view/View;I)Z

    move-result v0

    return v0
.end method

.method private A17(Landroid/view/View;Landroid/view/View;I)Z
    .locals 5

    .line 31302
    iget-object v2, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0p:Landroid/graphics/Rect;

    invoke-virtual {p1}, Landroid/view/View;->getWidth()I

    move-result v1

    invoke-virtual {p1}, Landroid/view/View;->getHeight()I

    move-result v0

    const/4 v3, 0x0

    invoke-virtual {v2, v3, v3, v1, v0}, Landroid/graphics/Rect;->set(IIII)V

    .line 31303
    iget-object v2, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0z:Landroid/graphics/Rect;

    invoke-virtual {p2}, Landroid/view/View;->getWidth()I

    move-result v1

    invoke-virtual {p2}, Landroid/view/View;->getHeight()I

    move-result v0

    invoke-virtual {v2, v3, v3, v1, v0}, Landroid/graphics/Rect;->set(IIII)V

    .line 31304
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0p:Landroid/graphics/Rect;

    invoke-virtual {p0, p1, v0}, Lcom/facebook/ads/redexgen/X/Eb;->offsetDescendantRectToMyCoords(Landroid/view/View;Landroid/graphics/Rect;)V

    .line 31305
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0z:Landroid/graphics/Rect;

    invoke-virtual {p0, p2, v0}, Lcom/facebook/ads/redexgen/X/Eb;->offsetDescendantRectToMyCoords(Landroid/view/View;Landroid/graphics/Rect;)V

    .line 31306
    sparse-switch p3, :sswitch_data_0

    .line 31307
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const/16 v2, 0x734

    const/16 v1, 0x25

    const/16 v0, 0x66

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Eb;->A0I(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p3}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v1

    .line 31308
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->A1H()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    new-instance v0, Ljava/lang/IllegalArgumentException;

    invoke-direct {v0, v1}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw v0

    .line 31309
    :sswitch_0
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0p:Landroid/graphics/Rect;

    iget v1, v0, Landroid/graphics/Rect;->top:I

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0z:Landroid/graphics/Rect;

    iget v0, v0, Landroid/graphics/Rect;->top:I

    if-lt v1, v0, :cond_0

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0p:Landroid/graphics/Rect;

    iget v1, v0, Landroid/graphics/Rect;->bottom:I

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0z:Landroid/graphics/Rect;

    iget v0, v0, Landroid/graphics/Rect;->top:I

    if-gt v1, v0, :cond_1

    :cond_0
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0p:Landroid/graphics/Rect;

    iget v1, v0, Landroid/graphics/Rect;->bottom:I

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0z:Landroid/graphics/Rect;

    iget v0, v0, Landroid/graphics/Rect;->bottom:I

    if-ge v1, v0, :cond_1

    const/4 v3, 0x1

    :cond_1
    return v3

    .line 31310
    :sswitch_1
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0p:Landroid/graphics/Rect;

    iget v4, v0, Landroid/graphics/Rect;->left:I

    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const/4 v0, 0x2

    aget-object v1, v2, v0

    const/4 v0, 0x7

    aget-object v0, v2, v0

    invoke-virtual {v1}, Ljava/lang/String;->length()I

    move-result v1

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v0

    if-eq v1, v0, :cond_4

    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const-string v1, "H3chOpt4vkJfacRirYduge"

    const/4 v0, 0x3

    aput-object v1, v2, v0

    const-string v1, "3wUmDedSMI"

    const/4 v0, 0x0

    aput-object v1, v2, v0

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0z:Landroid/graphics/Rect;

    iget v0, v0, Landroid/graphics/Rect;->left:I

    if-lt v4, v0, :cond_2

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0p:Landroid/graphics/Rect;

    iget v1, v0, Landroid/graphics/Rect;->right:I

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0z:Landroid/graphics/Rect;

    iget v0, v0, Landroid/graphics/Rect;->left:I

    if-gt v1, v0, :cond_3

    :cond_2
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0p:Landroid/graphics/Rect;

    iget v1, v0, Landroid/graphics/Rect;->right:I

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0z:Landroid/graphics/Rect;

    iget v0, v0, Landroid/graphics/Rect;->right:I

    if-ge v1, v0, :cond_3

    const/4 v3, 0x1

    :cond_3
    return v3

    :cond_4
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    .line 31311
    :sswitch_2
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0p:Landroid/graphics/Rect;

    iget v1, v0, Landroid/graphics/Rect;->bottom:I

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0z:Landroid/graphics/Rect;

    iget v0, v0, Landroid/graphics/Rect;->bottom:I

    if-gt v1, v0, :cond_5

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0p:Landroid/graphics/Rect;

    iget v1, v0, Landroid/graphics/Rect;->top:I

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0z:Landroid/graphics/Rect;

    iget v0, v0, Landroid/graphics/Rect;->bottom:I

    if-lt v1, v0, :cond_6

    :cond_5
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0p:Landroid/graphics/Rect;

    iget v1, v0, Landroid/graphics/Rect;->top:I

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0z:Landroid/graphics/Rect;

    iget v0, v0, Landroid/graphics/Rect;->top:I

    if-le v1, v0, :cond_6

    const/4 v3, 0x1

    :cond_6
    return v3

    .line 31312
    :sswitch_3
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0p:Landroid/graphics/Rect;

    iget v1, v0, Landroid/graphics/Rect;->right:I

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0z:Landroid/graphics/Rect;

    iget v0, v0, Landroid/graphics/Rect;->right:I

    if-gt v1, v0, :cond_7

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0p:Landroid/graphics/Rect;

    iget v1, v0, Landroid/graphics/Rect;->left:I

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0z:Landroid/graphics/Rect;

    iget v0, v0, Landroid/graphics/Rect;->right:I

    if-lt v1, v0, :cond_8

    :cond_7
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0p:Landroid/graphics/Rect;

    iget v1, v0, Landroid/graphics/Rect;->left:I

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0z:Landroid/graphics/Rect;

    iget v0, v0, Landroid/graphics/Rect;->left:I

    if-le v1, v0, :cond_8

    const/4 v3, 0x1

    :cond_8
    return v3

    nop

    :sswitch_data_0
    .sparse-switch
        0x11 -> :sswitch_3
        0x21 -> :sswitch_2
        0x42 -> :sswitch_1
        0x82 -> :sswitch_0
    .end sparse-switch
.end method

.method private final A18(Landroid/view/accessibility/AccessibilityEvent;)Z
    .locals 2

    .line 31313
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->A1q()Z

    move-result v0

    if-eqz v0, :cond_2

    .line 31314
    const/4 v1, 0x0

    .line 31315
    .local v0, "type":I
    if-eqz p1, :cond_0

    .line 31316
    invoke-static {p1}, Lcom/facebook/ads/redexgen/X/3m;->A00(Landroid/view/accessibility/AccessibilityEvent;)I

    move-result v1

    .line 31317
    :cond_0
    if-nez v1, :cond_1

    .line 31318
    const/4 v1, 0x0

    .line 31319
    :cond_1
    iget v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0P:I

    or-int/2addr v0, v1

    iput v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0P:I

    .line 31320
    const/4 v0, 0x1

    return v0

    .line 31321
    .end local v0    # "type":I
    :cond_2
    const/4 v0, 0x0

    return v0
.end method

.method public static synthetic A19(Lcom/facebook/ads/redexgen/X/Eb;)Z
    .locals 0

    .line 31322
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->awakenScrollBars()Z

    move-result p0

    return p0
.end method

.method public static synthetic A1A(Lcom/facebook/ads/redexgen/X/Eb;)[I
    .locals 0

    .line 31323
    iget-object p0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A16:[I

    return-object p0
.end method

.method private getScrollingChildHelper()Lcom/facebook/ads/redexgen/X/3B;
    .locals 1

    .line 31784
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0d:Lcom/facebook/ads/redexgen/X/3B;

    if-nez v0, :cond_0

    .line 31785
    new-instance v0, Lcom/facebook/ads/redexgen/X/3B;

    invoke-direct {v0, p0}, Lcom/facebook/ads/redexgen/X/3B;-><init>(Landroid/view/View;)V

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0d:Lcom/facebook/ads/redexgen/X/3B;

    .line 31786
    :cond_0
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0d:Lcom/facebook/ads/redexgen/X/3B;

    return-object v0
.end method


# virtual methods
.method public final A1B(Landroid/view/View;)I
    .locals 1

    .line 31324
    invoke-static {p1}, Lcom/facebook/ads/redexgen/X/Eb;->A0F(Landroid/view/View;)Lcom/facebook/ads/redexgen/X/56;

    move-result-object v0

    .line 31325
    .local v0, "holder":Lcom/facebook/ads/redexgen/X/56;
    if-eqz v0, :cond_0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/56;->A0I()I

    move-result v0

    :goto_0
    return v0

    :cond_0
    const/4 v0, -0x1

    goto :goto_0
.end method

.method public final A1C(Lcom/facebook/ads/redexgen/X/56;)I
    .locals 4

    .line 31326
    const/16 v0, 0x20c

    invoke-virtual {p1, v0}, Lcom/facebook/ads/redexgen/X/56;->A0i(I)Z

    move-result v0

    if-nez v0, :cond_0

    .line 31327
    invoke-virtual {p1}, Lcom/facebook/ads/redexgen/X/56;->A0Y()Z

    move-result v0

    if-nez v0, :cond_2

    .line 31328
    :cond_0
    const/4 v3, -0x1

    sget-object v1, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const/4 v0, 0x5

    aget-object v1, v1, v0

    const/4 v0, 0x2

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    const/16 v0, 0x59

    if-eq v1, v0, :cond_1

    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    :cond_1
    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const-string v1, "S0wUd1"

    const/4 v0, 0x2

    aput-object v1, v2, v0

    const-string v1, "zF1SY7WsjbFG"

    const/4 v0, 0x7

    aput-object v1, v2, v0

    return v3

    .line 31329
    :cond_2
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/Eb;->A00:Lcom/facebook/ads/redexgen/X/Zq;

    iget v0, p1, Lcom/facebook/ads/redexgen/X/56;->A03:I

    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/Zq;->A05(I)I

    move-result v0

    return v0
.end method

.method public final A1D(Landroid/view/View;)Landroid/graphics/Rect;
    .locals 5

    .line 31330
    invoke-virtual {p1}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    move-result-object v4

    check-cast v4, Lcom/facebook/ads/redexgen/X/4p;

    .line 31331
    .local v0, "lp":Lcom/facebook/ads/redexgen/X/4p;
    iget-boolean v0, v4, Lcom/facebook/ads/redexgen/X/4p;->A01:Z

    if-nez v0, :cond_0

    .line 31332
    iget-object v0, v4, Lcom/facebook/ads/redexgen/X/4p;->A03:Landroid/graphics/Rect;

    return-object v0

    .line 31333
    :cond_0
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0s:Lcom/facebook/ads/redexgen/X/53;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/53;->A07()Z

    move-result v0

    if-eqz v0, :cond_3

    invoke-virtual {v4}, Lcom/facebook/ads/redexgen/X/4p;->A01()Z

    move-result v0

    if-nez v0, :cond_2

    invoke-virtual {v4}, Lcom/facebook/ads/redexgen/X/4p;->A03()Z

    move-result v3

    sget-object v1, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const/4 v0, 0x5

    aget-object v1, v1, v0

    const/4 v0, 0x2

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    const/16 v0, 0x59

    if-eq v1, v0, :cond_1

    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    :cond_1
    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const-string v1, "R8RZJpxNRYJLmE7KdaIYiTLeL7r3Y7bS"

    const/4 v0, 0x6

    aput-object v1, v2, v0

    const-string v1, "BRrnvNJfpVwihHaR5a5gEdBxu0vp26qH"

    const/4 v0, 0x4

    aput-object v1, v2, v0

    if-eqz v3, :cond_3

    .line 31334
    :cond_2
    iget-object v0, v4, Lcom/facebook/ads/redexgen/X/4p;->A03:Landroid/graphics/Rect;

    return-object v0

    .line 31335
    :cond_3
    iget-object v3, v4, Lcom/facebook/ads/redexgen/X/4p;->A03:Landroid/graphics/Rect;

    .line 31336
    .local v1, "insets":Landroid/graphics/Rect;
    const/4 v2, 0x0

    invoke-virtual {v3, v2, v2, v2, v2}, Landroid/graphics/Rect;->set(IIII)V

    .line 31337
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0v:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->size()I

    move-result v0

    .line 31338
    .local v3, "decorCount":I
    const/4 v1, 0x0

    .local v4, "i":I
    if-ge v1, v0, :cond_4

    .line 31339
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0p:Landroid/graphics/Rect;

    invoke-virtual {v0, v2, v2, v2, v2}, Landroid/graphics/Rect;->set(IIII)V

    .line 31340
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0v:Ljava/util/ArrayList;

    invoke-virtual {v0, v1}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    const/16 v2, 0x759

    const/16 v1, 0xe

    const/16 v0, 0x12

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Eb;->A0I(III)Ljava/lang/String;

    move-result-object v1

    new-instance v0, Ljava/lang/NullPointerException;

    invoke-direct {v0, v1}, Ljava/lang/NullPointerException;-><init>(Ljava/lang/String;)V

    throw v0

    .line 31341
    .end local v4    # "i":I
    :cond_4
    iput-boolean v2, v4, Lcom/facebook/ads/redexgen/X/4p;->A01:Z

    .line 31342
    return-object v3
.end method

.method public final A1E(Landroid/view/View;)Landroid/view/View;
    .locals 2

    .line 31343
    invoke-virtual {p1}, Landroid/view/View;->getParent()Landroid/view/ViewParent;

    move-result-object v1

    .line 31344
    .local v0, "parent":Landroid/view/ViewParent;
    :goto_0
    if-eqz v1, :cond_0

    if-eq v1, p0, :cond_0

    instance-of v0, v1, Landroid/view/View;

    if-eqz v0, :cond_0

    .line 31345
    move-object p1, v1

    check-cast p1, Landroid/view/View;

    .line 31346
    invoke-virtual {p1}, Landroid/view/View;->getParent()Landroid/view/ViewParent;

    move-result-object v1

    goto :goto_0

    .line 31347
    :cond_0
    if-ne v1, p0, :cond_1

    :goto_1
    return-object p1

    :cond_1
    const/4 p1, 0x0

    goto :goto_1
.end method

.method public final A1F(I)Lcom/facebook/ads/redexgen/X/56;
    .locals 5

    .line 31348
    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0C:Z

    if-eqz v0, :cond_0

    .line 31349
    const/4 v0, 0x0

    return-object v0

    .line 31350
    :cond_0
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A01:Lcom/facebook/ads/redexgen/X/4C;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/4C;->A06()I

    move-result v4

    .line 31351
    .local v0, "childCount":I
    const/4 v1, 0x0

    .line 31352
    .local v1, "hidden":Lcom/facebook/ads/redexgen/X/56;
    const/4 v3, 0x0

    .local v2, "i":I
    :goto_0
    if-ge v3, v4, :cond_3

    .line 31353
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A01:Lcom/facebook/ads/redexgen/X/4C;

    invoke-virtual {v0, v3}, Lcom/facebook/ads/redexgen/X/4C;->A0A(I)Landroid/view/View;

    move-result-object v0

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/Eb;->A0F(Landroid/view/View;)Lcom/facebook/ads/redexgen/X/56;

    move-result-object v2

    .line 31354
    .local v3, "holder":Lcom/facebook/ads/redexgen/X/56;
    if-eqz v2, :cond_1

    invoke-virtual {v2}, Lcom/facebook/ads/redexgen/X/56;->A0a()Z

    move-result v0

    if-nez v0, :cond_1

    .line 31355
    invoke-virtual {p0, v2}, Lcom/facebook/ads/redexgen/X/Eb;->A1C(Lcom/facebook/ads/redexgen/X/56;)I

    move-result v0

    if-ne v0, p1, :cond_1

    .line 31356
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/Eb;->A01:Lcom/facebook/ads/redexgen/X/4C;

    iget-object v0, v2, Lcom/facebook/ads/redexgen/X/56;->A0H:Landroid/view/View;

    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/4C;->A0K(Landroid/view/View;)Z

    move-result v0

    if-eqz v0, :cond_2

    .line 31357
    move-object v1, v2

    .line 31358
    .end local v3    # "holder":Lcom/facebook/ads/redexgen/X/56;
    :cond_1
    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    .line 31359
    :cond_2
    return-object v2

    .line 31360
    .end local v2    # "i":I
    :cond_3
    return-object v1
.end method

.method public final A1G(Landroid/view/View;)Lcom/facebook/ads/redexgen/X/56;
    .locals 4

    .line 31361
    invoke-virtual {p1}, Landroid/view/View;->getParent()Landroid/view/ViewParent;

    move-result-object v0

    .line 31362
    .local v0, "parent":Landroid/view/ViewParent;
    if-eqz v0, :cond_0

    if-ne v0, p0, :cond_1

    .line 31363
    :cond_0
    invoke-static {p1}, Lcom/facebook/ads/redexgen/X/Eb;->A0F(Landroid/view/View;)Lcom/facebook/ads/redexgen/X/56;

    move-result-object v0

    return-object v0

    .line 31364
    :cond_1
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const/16 v2, 0x722

    const/4 v1, 0x5

    const/16 v0, 0x38

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Eb;->A0I(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v3

    const/16 v2, 0x62

    const/16 v1, 0x1a

    const/16 v0, 0x7f

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Eb;->A0I(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    new-instance v0, Ljava/lang/IllegalArgumentException;

    invoke-direct {v0, v1}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw v0
.end method

.method public final A1H()Ljava/lang/String;
    .locals 4

    .line 31365
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const/4 v2, 0x0

    const/4 v1, 0x1

    const/16 v0, 0x41

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Eb;->A0I(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-super {p0}, Landroid/view/ViewGroup;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    const/16 v2, 0xa9

    const/16 v1, 0xa

    const/16 v0, 0x52

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Eb;->A0I(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A04:Lcom/facebook/ads/redexgen/X/4c;

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v3

    const/16 v2, 0xbd

    const/16 v1, 0x9

    const/16 v0, 0x47

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Eb;->A0I(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v3

    const/16 v2, 0xb3

    const/16 v1, 0xa

    const/16 v0, 0xa

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Eb;->A0I(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    .line 31366
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    .line 31367
    return-object v0
.end method

.method public final A1I()V
    .locals 5

    .line 31368
    iget-boolean v3, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0D:Z

    const/16 v2, 0x521

    const/16 v1, 0x11

    const/16 v0, 0x5e

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Eb;->A0I(III)Ljava/lang/String;

    move-result-object v4

    if-eqz v3, :cond_0

    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0C:Z

    if-eqz v0, :cond_1

    .line 31369
    :cond_0
    invoke-static {v4}, Lcom/facebook/ads/redexgen/X/2q;->A01(Ljava/lang/String;)V

    .line 31370
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/Eb;->A0Y()V

    .line 31371
    invoke-static {}, Lcom/facebook/ads/redexgen/X/2q;->A00()V

    .line 31372
    return-void

    .line 31373
    :cond_1
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A00:Lcom/facebook/ads/redexgen/X/Zq;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/Zq;->A0B()Z

    move-result v0

    if-nez v0, :cond_2

    .line 31374
    return-void

    .line 31375
    :cond_2
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/Eb;->A00:Lcom/facebook/ads/redexgen/X/Zq;

    const/4 v0, 0x4

    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/Zq;->A0D(I)Z

    move-result v0

    if-eqz v0, :cond_3

    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/Eb;->A00:Lcom/facebook/ads/redexgen/X/Zq;

    .line 31376
    const/16 v0, 0xb

    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/Zq;->A0D(I)Z

    move-result v3

    sget-object v1, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const/4 v0, 0x5

    aget-object v1, v1, v0

    const/4 v0, 0x2

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    const/16 v0, 0x59

    if-eq v1, v0, :cond_4

    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    .line 31377
    :cond_3
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A00:Lcom/facebook/ads/redexgen/X/Zq;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/Zq;->A0B()Z

    move-result v0

    if-eqz v0, :cond_6

    .line 31378
    invoke-static {v4}, Lcom/facebook/ads/redexgen/X/2q;->A01(Ljava/lang/String;)V

    .line 31379
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/Eb;->A0Y()V

    .line 31380
    invoke-static {}, Lcom/facebook/ads/redexgen/X/2q;->A00()V

    goto :goto_1

    .line 31381
    :cond_4
    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const-string v1, "e84NaKOnn7I9zpRn8aasqpcOMtbsg9mo"

    const/4 v0, 0x6

    aput-object v1, v2, v0

    const-string v1, "iuEMZhfSQYSf3gJ7Ya7A7FZe771rQXKi"

    const/4 v0, 0x4

    aput-object v1, v2, v0

    if-nez v3, :cond_3

    .line 31382
    const/16 v2, 0x53d

    const/16 v1, 0x14

    const/16 v0, 0x52

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Eb;->A0I(III)Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/2q;->A01(Ljava/lang/String;)V

    .line 31383
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->A1J()V

    .line 31384
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->A1K()V

    .line 31385
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A00:Lcom/facebook/ads/redexgen/X/Zq;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/Zq;->A08()V

    .line 31386
    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0J:Z

    if-nez v0, :cond_5

    .line 31387
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/Eb;->A0y()Z

    move-result v0

    if-eqz v0, :cond_7

    .line 31388
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/Eb;->A0Y()V

    .line 31389
    :cond_5
    :goto_0
    const/4 v0, 0x1

    invoke-virtual {p0, v0}, Lcom/facebook/ads/redexgen/X/Eb;->A1n(Z)V

    .line 31390
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->A1L()V

    .line 31391
    invoke-static {}, Lcom/facebook/ads/redexgen/X/2q;->A00()V

    .line 31392
    :cond_6
    :goto_1
    return-void

    .line 31393
    :cond_7
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A00:Lcom/facebook/ads/redexgen/X/Zq;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/Zq;->A06()V

    goto :goto_0
.end method

.method public final A1J()V
    .locals 2

    .line 31394
    iget v1, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0O:I

    const/4 v0, 0x1

    add-int/2addr v1, v0

    iput v1, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0O:I

    .line 31395
    if-ne v1, v0, :cond_0

    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0I:Z

    if-nez v0, :cond_0

    .line 31396
    const/4 v0, 0x0

    iput-boolean v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0J:Z

    .line 31397
    :cond_0
    return-void
.end method

.method public final A1K()V
    .locals 1

    .line 31398
    iget v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0U:I

    add-int/lit8 v0, v0, 0x1

    iput v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0U:I

    .line 31399
    return-void
.end method

.method public final A1L()V
    .locals 1

    .line 31400
    const/4 v0, 0x1

    invoke-virtual {p0, v0}, Lcom/facebook/ads/redexgen/X/Eb;->A1m(Z)V

    .line 31401
    return-void
.end method

.method public final A1M()V
    .locals 1

    .line 31402
    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0K:Z

    if-nez v0, :cond_0

    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0F:Z

    if-eqz v0, :cond_0

    .line 31403
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0k:Ljava/lang/Runnable;

    invoke-static {p0, v0}, Lcom/facebook/ads/redexgen/X/3T;->A0D(Landroid/view/View;Ljava/lang/Runnable;)V

    .line 31404
    const/4 v0, 0x1

    iput-boolean v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0K:Z

    .line 31405
    :cond_0
    return-void
.end method

.method public final A1N()V
    .locals 2

    .line 31406
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A05:Lcom/facebook/ads/redexgen/X/4k;

    if-eqz v0, :cond_0

    .line 31407
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/4k;->A0I()V

    .line 31408
    :cond_0
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    if-eqz v1, :cond_1

    .line 31409
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0r:Lcom/facebook/ads/redexgen/X/4w;

    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/4o;->A1I(Lcom/facebook/ads/redexgen/X/4w;)V

    .line 31410
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0r:Lcom/facebook/ads/redexgen/X/4w;

    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/4o;->A1G(Lcom/facebook/ads/redexgen/X/4w;)V

    .line 31411
    :cond_1
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0r:Lcom/facebook/ads/redexgen/X/4w;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/4w;->A0P()V

    .line 31412
    return-void
.end method

.method public final A1O()V
    .locals 9

    .line 31413
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A01:Lcom/facebook/ads/redexgen/X/4C;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/4C;->A05()I

    move-result v8

    .line 31414
    .local v0, "count":I
    const/4 v7, 0x0

    .local v1, "i":I
    :goto_0
    if-ge v7, v8, :cond_3

    .line 31415
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A01:Lcom/facebook/ads/redexgen/X/4C;

    invoke-virtual {v0, v7}, Lcom/facebook/ads/redexgen/X/4C;->A09(I)Landroid/view/View;

    move-result-object v2

    .line 31416
    .local v2, "view":Landroid/view/View;
    invoke-virtual {p0, v2}, Lcom/facebook/ads/redexgen/X/Eb;->A1G(Landroid/view/View;)Lcom/facebook/ads/redexgen/X/56;

    move-result-object v1

    .line 31417
    .local v3, "holder":Lcom/facebook/ads/redexgen/X/56;
    if-eqz v1, :cond_1

    iget-object v0, v1, Lcom/facebook/ads/redexgen/X/56;->A07:Lcom/facebook/ads/redexgen/X/56;

    if-eqz v0, :cond_1

    .line 31418
    iget-object v0, v1, Lcom/facebook/ads/redexgen/X/56;->A07:Lcom/facebook/ads/redexgen/X/56;

    iget-object v6, v0, Lcom/facebook/ads/redexgen/X/56;->A0H:Landroid/view/View;

    .line 31419
    .local v4, "shadowingView":Landroid/view/View;
    invoke-virtual {v2}, Landroid/view/View;->getLeft()I

    move-result v5

    .line 31420
    .local v5, "left":I
    invoke-virtual {v2}, Landroid/view/View;->getTop()I

    move-result v4

    .line 31421
    .local v6, "top":I
    invoke-virtual {v6}, Landroid/view/View;->getLeft()I

    move-result v0

    if-ne v5, v0, :cond_0

    invoke-virtual {v6}, Landroid/view/View;->getTop()I

    move-result v0

    if-eq v4, v0, :cond_1

    .line 31422
    :cond_0
    invoke-virtual {v6}, Landroid/view/View;->getWidth()I

    move-result v3

    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const/4 v0, 0x2

    aget-object v1, v2, v0

    const/4 v0, 0x7

    aget-object v0, v2, v0

    invoke-virtual {v1}, Ljava/lang/String;->length()I

    move-result v1

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v0

    if-eq v1, v0, :cond_2

    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const-string v1, "Uubm7UlJXcXPQblsDCbwus"

    const/4 v0, 0x3

    aput-object v1, v2, v0

    const-string v1, "kvvVMTFBun"

    const/4 v0, 0x0

    aput-object v1, v2, v0

    add-int/2addr v3, v5

    .line 31423
    invoke-virtual {v6}, Landroid/view/View;->getHeight()I

    move-result v0

    add-int/2addr v0, v4

    .line 31424
    invoke-virtual {v6, v5, v4, v3, v0}, Landroid/view/View;->layout(IIII)V

    .line 31425
    .end local v2    # "view":Landroid/view/View;
    .end local v3    # "holder":Lcom/facebook/ads/redexgen/X/56;
    .end local v4    # "shadowingView":Landroid/view/View;
    .end local v5    # "left":I
    .end local v6    # "top":I
    :cond_1
    add-int/lit8 v7, v7, 0x1

    goto :goto_0

    :cond_2
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    .line 31426
    .end local v1    # "i":I
    :cond_3
    return-void
.end method

.method public final A1P()V
    .locals 1

    .line 31427
    const/4 v0, 0x1

    iput-boolean v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0C:Z

    .line 31428
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/Eb;->A0h()V

    .line 31429
    return-void
.end method

.method public final A1Q()V
    .locals 1

    .line 31430
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0m:Ljava/util/List;

    if-eqz v0, :cond_0

    .line 31431
    invoke-interface {v0}, Ljava/util/List;->clear()V

    .line 31432
    :cond_0
    return-void
.end method

.method public final A1R()V
    .locals 1

    .line 31433
    const/4 v0, 0x0

    invoke-virtual {p0, v0}, Lcom/facebook/ads/redexgen/X/Eb;->setScrollState(I)V

    .line 31434
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/Eb;->A0V()V

    .line 31435
    return-void
.end method

.method public final A1S(I)V
    .locals 3

    .line 31436
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    if-nez v0, :cond_0

    .line 31437
    return-void

    .line 31438
    :cond_0
    invoke-virtual {v0, p1}, Lcom/facebook/ads/redexgen/X/4o;->A1p(I)V

    .line 31439
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->awakenScrollBars()Z

    sget-object v1, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const/4 v0, 0x5

    aget-object v1, v1, v0

    const/4 v0, 0x2

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    const/16 v0, 0x59

    if-eq v1, v0, :cond_1

    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    .line 31440
    :cond_1
    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const-string v1, "0QEBPr"

    const/4 v0, 0x2

    aput-object v1, v2, v0

    const-string v1, "MYIDRbA0xlXg"

    const/4 v0, 0x7

    aput-object v1, v2, v0

    return-void
.end method

.method public final A1T(I)V
    .locals 3

    .line 31441
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A01:Lcom/facebook/ads/redexgen/X/4C;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/4C;->A05()I

    move-result v2

    .line 31442
    .local v0, "childCount":I
    const/4 v1, 0x0

    .local v1, "i":I
    :goto_0
    if-ge v1, v2, :cond_0

    .line 31443
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A01:Lcom/facebook/ads/redexgen/X/4C;

    invoke-virtual {v0, v1}, Lcom/facebook/ads/redexgen/X/4C;->A09(I)Landroid/view/View;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/view/View;->offsetLeftAndRight(I)V

    .line 31444
    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    .line 31445
    .end local v1    # "i":I
    :cond_0
    return-void
.end method

.method public final A1U(I)V
    .locals 3

    .line 31446
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A01:Lcom/facebook/ads/redexgen/X/4C;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/4C;->A05()I

    move-result v2

    .line 31447
    .local v0, "childCount":I
    const/4 v1, 0x0

    .local v1, "i":I
    :goto_0
    if-ge v1, v2, :cond_0

    .line 31448
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A01:Lcom/facebook/ads/redexgen/X/4C;

    invoke-virtual {v0, v1}, Lcom/facebook/ads/redexgen/X/4C;->A09(I)Landroid/view/View;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/view/View;->offsetTopAndBottom(I)V

    .line 31449
    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    .line 31450
    .end local v1    # "i":I
    :cond_0
    return-void
.end method

.method public final A1V(I)V
    .locals 5

    .line 31451
    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0I:Z

    if-eqz v0, :cond_0

    .line 31452
    return-void

    .line 31453
    :cond_0
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->A1R()V

    .line 31454
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    if-nez v0, :cond_2

    .line 31455
    const/16 v2, 0x55a

    const/16 v1, 0xc

    const/16 v0, 0x4f

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Eb;->A0I(III)Ljava/lang/String;

    move-result-object v3

    const/16 v4, 0x2e4

    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const/4 v0, 0x6

    aget-object v1, v2, v0

    const/4 v0, 0x4

    aget-object v2, v2, v0

    const/16 v0, 0x11

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    invoke-virtual {v2, v0}, Ljava/lang/String;->charAt(I)C

    move-result v0

    if-eq v1, v0, :cond_1

    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    :cond_1
    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const-string v1, "bKq2dxkRCeugdKx9Xa6GvrcP2g1fh08B"

    const/4 v0, 0x6

    aput-object v1, v2, v0

    const-string v1, "kU2aPHyT0THRQATybaGwdbwAtHBBu9qc"

    const/4 v0, 0x4

    aput-object v1, v2, v0

    const/16 v1, 0x5e

    const/16 v0, 0x45

    invoke-static {v4, v1, v0}, Lcom/facebook/ads/redexgen/X/Eb;->A0I(III)Ljava/lang/String;

    move-result-object v0

    invoke-static {v3, v0}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    .line 31456
    return-void

    .line 31457
    :cond_2
    invoke-virtual {v0, p1}, Lcom/facebook/ads/redexgen/X/4o;->A1p(I)V

    .line 31458
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->awakenScrollBars()Z

    .line 31459
    return-void
.end method

.method public final A1W(I)V
    .locals 4

    .line 31460
    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0I:Z

    if-eqz v0, :cond_0

    .line 31461
    return-void

    .line 31462
    :cond_0
    iget-object v3, p0, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const/4 v0, 0x3

    aget-object v1, v2, v0

    const/4 v0, 0x0

    aget-object v0, v2, v0

    invoke-virtual {v1}, Ljava/lang/String;->length()I

    move-result v1

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v0

    if-eq v1, v0, :cond_3

    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const-string v1, "WdHf0lV4QkHUB6jMNa4hJVyYAF4qnaeC"

    const/4 v0, 0x6

    aput-object v1, v2, v0

    const-string v1, "5gGhKJie0DBDDFfkhapEv4fZOrT4upMj"

    const/4 v0, 0x4

    aput-object v1, v2, v0

    if-nez v3, :cond_2

    .line 31463
    const/16 v3, 0x55a

    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const/4 v0, 0x6

    aget-object v1, v2, v0

    const/4 v0, 0x4

    aget-object v2, v2, v0

    const/16 v0, 0x11

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    invoke-virtual {v2, v0}, Ljava/lang/String;->charAt(I)C

    move-result v0

    if-eq v1, v0, :cond_1

    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const-string v1, "tnW8WJDd0hyAXA7zkanproBVQy0r1bMC"

    const/4 v0, 0x6

    aput-object v1, v2, v0

    const-string v1, "4Fp0w8uFB0Hn5lDXLaIQvmhL07bdVyXH"

    const/4 v0, 0x4

    aput-object v1, v2, v0

    const/16 v1, 0xa

    const/16 v0, 0xd

    invoke-static {v3, v1, v0}, Lcom/facebook/ads/redexgen/X/Eb;->A0I(III)Ljava/lang/String;

    move-result-object v3

    const/16 v2, 0x35a

    const/16 v1, 0x2e

    const/16 v0, 0x10

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Eb;->A0I(III)Ljava/lang/String;

    move-result-object v0

    invoke-static {v3, v0}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    .line 31464
    return-void

    :cond_1
    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const-string v1, "z3A7CLBHtBtrAEYqxn9jPo"

    const/4 v0, 0x3

    aput-object v1, v2, v0

    const-string v1, "loKczzo2gT"

    const/4 v0, 0x0

    aput-object v1, v2, v0

    const/16 v1, 0xc

    const/16 v0, 0x4f

    invoke-static {v3, v1, v0}, Lcom/facebook/ads/redexgen/X/Eb;->A0I(III)Ljava/lang/String;

    move-result-object v3

    const/16 v2, 0x39c

    const/16 v1, 0x61

    const/16 v0, 0x4b

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Eb;->A0I(III)Ljava/lang/String;

    move-result-object v0

    invoke-static {v3, v0}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    return-void

    .line 31465
    :cond_2
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0s:Lcom/facebook/ads/redexgen/X/53;

    invoke-virtual {v3, p0, v0, p1}, Lcom/facebook/ads/redexgen/X/4o;->A1x(Lcom/facebook/ads/redexgen/X/Eb;Lcom/facebook/ads/redexgen/X/53;I)V

    .line 31466
    return-void

    :cond_3
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0
.end method

.method public final A1X(I)V
    .locals 1

    .line 31467
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/Eb;->getScrollingChildHelper()Lcom/facebook/ads/redexgen/X/3B;

    move-result-object v0

    invoke-virtual {v0, p1}, Lcom/facebook/ads/redexgen/X/3B;->A03(I)V

    .line 31468
    return-void
.end method

.method public final A1Y(II)V
    .locals 3

    .line 31469
    if-gez p1, :cond_1

    .line 31470
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/Eb;->A0b()V

    .line 31471
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0a:Landroid/widget/EdgeEffect;

    neg-int v0, p1

    invoke-virtual {v1, v0}, Landroid/widget/EdgeEffect;->onAbsorb(I)V

    .line 31472
    :cond_0
    :goto_0
    if-gez p2, :cond_2

    .line 31473
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/Eb;->A0d()V

    sget-object v1, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const/4 v0, 0x5

    aget-object v1, v1, v0

    const/4 v0, 0x2

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    const/16 v0, 0x59

    if-eq v1, v0, :cond_3

    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    .line 31474
    :cond_1
    if-lez p1, :cond_0

    .line 31475
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/Eb;->A0c()V

    .line 31476
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0b:Landroid/widget/EdgeEffect;

    invoke-virtual {v0, p1}, Landroid/widget/EdgeEffect;->onAbsorb(I)V

    goto :goto_0

    .line 31477
    :cond_2
    if-lez p2, :cond_4

    .line 31478
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/Eb;->A0a()V

    .line 31479
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0Z:Landroid/widget/EdgeEffect;

    invoke-virtual {v0, p2}, Landroid/widget/EdgeEffect;->onAbsorb(I)V

    goto :goto_1

    .line 31480
    :cond_3
    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const-string v1, "gyhAwDIMjNJoK3bJ2ki6wxmL7XFPannX"

    const/4 v0, 0x1

    aput-object v1, v2, v0

    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0c:Landroid/widget/EdgeEffect;

    neg-int v0, p2

    invoke-virtual {v1, v0}, Landroid/widget/EdgeEffect;->onAbsorb(I)V

    .line 31481
    :cond_4
    :goto_1
    if-nez p1, :cond_5

    if-eqz p2, :cond_6

    .line 31482
    :cond_5
    invoke-static {p0}, Lcom/facebook/ads/redexgen/X/3T;->A07(Landroid/view/View;)V

    .line 31483
    :cond_6
    return-void
.end method

.method public final A1Z(II)V
    .locals 5

    .line 31484
    const/4 v4, 0x0

    .line 31485
    .local v0, "needsInvalidate":Z
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0a:Landroid/widget/EdgeEffect;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Landroid/widget/EdgeEffect;->isFinished()Z

    move-result v0

    if-nez v0, :cond_0

    if-lez p1, :cond_0

    .line 31486
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0a:Landroid/widget/EdgeEffect;

    invoke-virtual {v0}, Landroid/widget/EdgeEffect;->onRelease()V

    .line 31487
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0a:Landroid/widget/EdgeEffect;

    invoke-virtual {v0}, Landroid/widget/EdgeEffect;->isFinished()Z

    move-result v4

    .line 31488
    :cond_0
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0b:Landroid/widget/EdgeEffect;

    if-eqz v0, :cond_1

    invoke-virtual {v0}, Landroid/widget/EdgeEffect;->isFinished()Z

    move-result v0

    if-nez v0, :cond_1

    if-gez p1, :cond_1

    .line 31489
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0b:Landroid/widget/EdgeEffect;

    invoke-virtual {v0}, Landroid/widget/EdgeEffect;->onRelease()V

    .line 31490
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0b:Landroid/widget/EdgeEffect;

    invoke-virtual {v0}, Landroid/widget/EdgeEffect;->isFinished()Z

    move-result v0

    or-int/2addr v4, v0

    .line 31491
    :cond_1
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0c:Landroid/widget/EdgeEffect;

    if-eqz v0, :cond_3

    invoke-virtual {v0}, Landroid/widget/EdgeEffect;->isFinished()Z

    move-result v0

    if-nez v0, :cond_3

    if-lez p2, :cond_3

    .line 31492
    iget-object v3, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0c:Landroid/widget/EdgeEffect;

    sget-object v1, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const/4 v0, 0x1

    aget-object v0, v1, v0

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v1

    const/16 v0, 0x20

    if-eq v1, v0, :cond_2

    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    :cond_2
    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const-string v1, "CkvbraPTkuNHVjG6c5toq1"

    const/4 v0, 0x3

    aput-object v1, v2, v0

    const-string v1, "anwNoWvWLV"

    const/4 v0, 0x0

    aput-object v1, v2, v0

    invoke-virtual {v3}, Landroid/widget/EdgeEffect;->onRelease()V

    .line 31493
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0c:Landroid/widget/EdgeEffect;

    invoke-virtual {v0}, Landroid/widget/EdgeEffect;->isFinished()Z

    move-result v0

    or-int/2addr v4, v0

    .line 31494
    :cond_3
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0Z:Landroid/widget/EdgeEffect;

    if-eqz v0, :cond_4

    invoke-virtual {v0}, Landroid/widget/EdgeEffect;->isFinished()Z

    move-result v0

    if-nez v0, :cond_4

    if-gez p2, :cond_4

    .line 31495
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0Z:Landroid/widget/EdgeEffect;

    invoke-virtual {v0}, Landroid/widget/EdgeEffect;->onRelease()V

    .line 31496
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0Z:Landroid/widget/EdgeEffect;

    invoke-virtual {v0}, Landroid/widget/EdgeEffect;->isFinished()Z

    move-result v0

    or-int/2addr v4, v0

    .line 31497
    :cond_4
    if-eqz v4, :cond_5

    .line 31498
    invoke-static {p0}, Lcom/facebook/ads/redexgen/X/3T;->A07(Landroid/view/View;)V

    .line 31499
    :cond_5
    return-void
.end method

.method public final A1a(II)V
    .locals 3

    .line 31500
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->getPaddingLeft()I

    move-result v1

    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->getPaddingRight()I

    move-result v0

    add-int/2addr v1, v0

    .line 31501
    invoke-static {p0}, Lcom/facebook/ads/redexgen/X/3T;->A03(Landroid/view/View;)I

    move-result v0

    .line 31502
    invoke-static {p1, v1, v0}, Lcom/facebook/ads/redexgen/X/4o;->A00(III)I

    move-result v2

    .line 31503
    .local v0, "width":I
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->getPaddingTop()I

    move-result v1

    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->getPaddingBottom()I

    move-result v0

    add-int/2addr v1, v0

    .line 31504
    invoke-static {p0}, Lcom/facebook/ads/redexgen/X/3T;->A02(Landroid/view/View;)I

    move-result v0

    .line 31505
    invoke-static {p2, v1, v0}, Lcom/facebook/ads/redexgen/X/4o;->A00(III)I

    move-result v0

    .line 31506
    .local v1, "height":I
    invoke-virtual {p0, v2, v0}, Lcom/facebook/ads/redexgen/X/Eb;->setMeasuredDimension(II)V

    .line 31507
    return-void
.end method

.method public final A1b(II)V
    .locals 2

    .line 31508
    iget v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0N:I

    add-int/lit8 v0, v0, 0x1

    iput v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0N:I

    .line 31509
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->getScrollX()I

    move-result v1

    .line 31510
    .local v0, "scrollX":I
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->getScrollY()I

    move-result v0

    .line 31511
    .local v1, "scrollY":I
    invoke-virtual {p0, v1, v0, v1, v0}, Lcom/facebook/ads/redexgen/X/Eb;->onScrollChanged(IIII)V

    .line 31512
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0i:Lcom/facebook/ads/redexgen/X/4t;

    if-eqz v0, :cond_0

    .line 31513
    invoke-virtual {v0, p0, p1, p2}, Lcom/facebook/ads/redexgen/X/4t;->A0M(Lcom/facebook/ads/redexgen/X/Eb;II)V

    .line 31514
    :cond_0
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0m:Ljava/util/List;

    if-eqz v0, :cond_1

    .line 31515
    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    add-int/lit8 v1, v0, -0x1

    .local p0, "i":I
    :goto_0
    if-ltz v1, :cond_1

    .line 31516
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0m:Ljava/util/List;

    invoke-interface {v0, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/facebook/ads/redexgen/X/4t;

    invoke-virtual {v0, p0, p1, p2}, Lcom/facebook/ads/redexgen/X/4t;->A0M(Lcom/facebook/ads/redexgen/X/Eb;II)V

    .line 31517
    add-int/lit8 v1, v1, -0x1

    goto :goto_0

    .line 31518
    .end local p0    # "i":I
    :cond_1
    iget v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0N:I

    add-int/lit8 v0, v0, -0x1

    iput v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0N:I

    .line 31519
    return-void
.end method

.method public final A1c(II)V
    .locals 1

    .line 31520
    const/4 v0, 0x0

    invoke-direct {p0, p1, p2, v0}, Lcom/facebook/ads/redexgen/X/Eb;->A0l(IILandroid/view/animation/Interpolator;)V

    .line 31521
    return-void
.end method

.method public final A1d(Landroid/view/View;)V
    .locals 4

    .line 31522
    invoke-static {p1}, Lcom/facebook/ads/redexgen/X/Eb;->A0F(Landroid/view/View;)Lcom/facebook/ads/redexgen/X/56;

    move-result-object v1

    .line 31523
    .local v0, "viewHolder":Lcom/facebook/ads/redexgen/X/56;
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A04:Lcom/facebook/ads/redexgen/X/4c;

    .line 31524
    iget-object v3, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0l:Ljava/util/List;

    sget-object v1, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const/4 v0, 0x5

    aget-object v1, v1, v0

    const/4 v0, 0x2

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    const/16 v0, 0x59

    if-eq v1, v0, :cond_0

    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    :cond_0
    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const-string v1, "qDYnzfveQxKkK3O0opuqDthRtcgsED9a"

    const/4 v0, 0x5

    aput-object v1, v2, v0

    if-eqz v3, :cond_1

    .line 31525
    invoke-interface {v3}, Ljava/util/List;->size()I

    move-result v0

    .line 31526
    .local v1, "cnt":I
    add-int/lit8 v1, v0, -0x1

    .local v2, "i":I
    if-ltz v1, :cond_1

    .line 31527
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0l:Ljava/util/List;

    invoke-interface {v0, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    const/16 v2, 0x767

    const/16 v1, 0x1b

    const/16 v0, 0x14

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Eb;->A0I(III)Ljava/lang/String;

    move-result-object v1

    new-instance v0, Ljava/lang/NullPointerException;

    invoke-direct {v0, v1}, Ljava/lang/NullPointerException;-><init>(Ljava/lang/String;)V

    throw v0

    .line 31528
    .end local v1    # "cnt":I
    .end local v2    # "i":I
    :cond_1
    return-void
.end method

.method public final A1e(Landroid/view/View;)V
    .locals 3

    .line 31529
    invoke-static {p1}, Lcom/facebook/ads/redexgen/X/Eb;->A0F(Landroid/view/View;)Lcom/facebook/ads/redexgen/X/56;

    move-result-object v1

    .line 31530
    .local v0, "viewHolder":Lcom/facebook/ads/redexgen/X/56;
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A04:Lcom/facebook/ads/redexgen/X/4c;

    .line 31531
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0l:Ljava/util/List;

    if-eqz v0, :cond_0

    .line 31532
    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    .line 31533
    .local v1, "cnt":I
    add-int/lit8 v1, v0, -0x1

    .local v2, "i":I
    if-ltz v1, :cond_0

    .line 31534
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0l:Ljava/util/List;

    invoke-interface {v0, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    const/16 v2, 0x782

    const/16 v1, 0x1d

    const/16 v0, 0x1a

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Eb;->A0I(III)Ljava/lang/String;

    move-result-object v1

    new-instance v0, Ljava/lang/NullPointerException;

    invoke-direct {v0, v1}, Ljava/lang/NullPointerException;-><init>(Ljava/lang/String;)V

    throw v0

    .line 31535
    .end local v1    # "cnt":I
    .end local v2    # "i":I
    :cond_0
    return-void
.end method

.method public final A1f(Lcom/facebook/ads/redexgen/X/4t;)V
    .locals 1

    .line 31536
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0m:Ljava/util/List;

    if-nez v0, :cond_0

    .line 31537
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0m:Ljava/util/List;

    .line 31538
    :cond_0
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0m:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 31539
    return-void
.end method

.method public final A1g(Lcom/facebook/ads/redexgen/X/4t;)V
    .locals 1

    .line 31540
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0m:Ljava/util/List;

    if-eqz v0, :cond_0

    .line 31541
    invoke-interface {v0, p1}, Ljava/util/List;->remove(Ljava/lang/Object;)Z

    .line 31542
    :cond_0
    return-void
.end method

.method public final A1h(Lcom/facebook/ads/redexgen/X/53;)V
    .locals 3

    .line 31543
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->getScrollState()I

    move-result v1

    const/4 v0, 0x2

    if-ne v1, v0, :cond_0

    .line 31544
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A08:Lcom/facebook/ads/redexgen/X/55;

    iget-object v2, v0, Lcom/facebook/ads/redexgen/X/55;->A01:Landroid/widget/OverScroller;

    .line 31545
    .local v0, "scroller":Landroid/widget/OverScroller;
    invoke-virtual {v2}, Landroid/widget/OverScroller;->getFinalX()I

    move-result v1

    invoke-virtual {v2}, Landroid/widget/OverScroller;->getCurrX()I

    move-result v0

    sub-int/2addr v1, v0

    iput v1, p1, Lcom/facebook/ads/redexgen/X/53;->A06:I

    .line 31546
    invoke-virtual {v2}, Landroid/widget/OverScroller;->getFinalY()I

    move-result v1

    invoke-virtual {v2}, Landroid/widget/OverScroller;->getCurrY()I

    move-result v0

    sub-int/2addr v1, v0

    iput v1, p1, Lcom/facebook/ads/redexgen/X/53;->A07:I

    .line 31547
    .end local v0    # "scroller":Landroid/widget/OverScroller;
    :goto_0
    return-void

    .line 31548
    :cond_0
    const/4 v0, 0x0

    iput v0, p1, Lcom/facebook/ads/redexgen/X/53;->A06:I

    .line 31549
    iput v0, p1, Lcom/facebook/ads/redexgen/X/53;->A07:I

    goto :goto_0
.end method

.method public final A1i(Lcom/facebook/ads/redexgen/X/56;Lcom/facebook/ads/redexgen/X/4j;)V
    .locals 3

    .line 31550
    const/4 v1, 0x0

    const/16 v0, 0x2000

    invoke-virtual {p1, v1, v0}, Lcom/facebook/ads/redexgen/X/56;->A0U(II)V

    .line 31551
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0s:Lcom/facebook/ads/redexgen/X/53;

    iget-boolean v0, v0, Lcom/facebook/ads/redexgen/X/53;->A0E:Z

    if-eqz v0, :cond_0

    invoke-virtual {p1}, Lcom/facebook/ads/redexgen/X/56;->A0d()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 31552
    invoke-virtual {p1}, Lcom/facebook/ads/redexgen/X/56;->A0a()Z

    move-result v0

    if-nez v0, :cond_0

    invoke-virtual {p1}, Lcom/facebook/ads/redexgen/X/56;->A0f()Z

    move-result v0

    if-nez v0, :cond_0

    .line 31553
    invoke-direct {p0, p1}, Lcom/facebook/ads/redexgen/X/Eb;->A0C(Lcom/facebook/ads/redexgen/X/56;)J

    move-result-wide v1

    .line 31554
    .local v0, "key":J
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0t:Lcom/facebook/ads/redexgen/X/5E;

    invoke-virtual {v0, v1, v2, p1}, Lcom/facebook/ads/redexgen/X/5E;->A08(JLcom/facebook/ads/redexgen/X/56;)V

    .line 31555
    .end local v0    # "key":J
    :cond_0
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0t:Lcom/facebook/ads/redexgen/X/5E;

    invoke-virtual {v0, p1, p2}, Lcom/facebook/ads/redexgen/X/5E;->A0F(Lcom/facebook/ads/redexgen/X/56;Lcom/facebook/ads/redexgen/X/4j;)V

    .line 31556
    return-void
.end method

.method public final A1j(Lcom/facebook/ads/redexgen/X/56;Lcom/facebook/ads/redexgen/X/4j;Lcom/facebook/ads/redexgen/X/4j;)V
    .locals 1

    .line 31557
    const/4 v0, 0x0

    invoke-virtual {p1, v0}, Lcom/facebook/ads/redexgen/X/56;->A0X(Z)V

    .line 31558
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A05:Lcom/facebook/ads/redexgen/X/4k;

    invoke-virtual {v0, p1, p2, p3}, Lcom/facebook/ads/redexgen/X/4k;->A0E(Lcom/facebook/ads/redexgen/X/56;Lcom/facebook/ads/redexgen/X/4j;Lcom/facebook/ads/redexgen/X/4j;)Z

    move-result v0

    if-eqz v0, :cond_0

    .line 31559
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->A1M()V

    .line 31560
    :cond_0
    return-void
.end method

.method public final A1k(Lcom/facebook/ads/redexgen/X/56;Lcom/facebook/ads/redexgen/X/4j;Lcom/facebook/ads/redexgen/X/4j;)V
    .locals 1

    .line 31561
    invoke-direct {p0, p1}, Lcom/facebook/ads/redexgen/X/Eb;->A0r(Lcom/facebook/ads/redexgen/X/56;)V

    .line 31562
    const/4 v0, 0x0

    invoke-virtual {p1, v0}, Lcom/facebook/ads/redexgen/X/56;->A0X(Z)V

    .line 31563
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A05:Lcom/facebook/ads/redexgen/X/4k;

    invoke-virtual {v0, p1, p2, p3}, Lcom/facebook/ads/redexgen/X/4k;->A0F(Lcom/facebook/ads/redexgen/X/56;Lcom/facebook/ads/redexgen/X/4j;Lcom/facebook/ads/redexgen/X/4j;)Z

    move-result v0

    if-eqz v0, :cond_0

    .line 31564
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->A1M()V

    .line 31565
    :cond_0
    return-void
.end method

.method public final A1l(Ljava/lang/String;)V
    .locals 5

    .line 31566
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->A1q()Z

    move-result v0

    if-eqz v0, :cond_1

    .line 31567
    if-nez p1, :cond_0

    .line 31568
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const/16 v2, 0x23e

    const/16 v1, 0x4d

    const/16 v0, 0x31

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Eb;->A0I(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    .line 31569
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->A1H()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    new-instance v0, Ljava/lang/IllegalStateException;

    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw v0

    .line 31570
    :cond_0
    new-instance v0, Ljava/lang/IllegalStateException;

    invoke-direct {v0, p1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw v0

    .line 31571
    :cond_1
    iget v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0N:I

    if-lez v0, :cond_2

    .line 31572
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const/4 v2, 0x0

    const/4 v1, 0x0

    const/16 v0, 0x3e

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Eb;->A0I(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    .line 31573
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->A1H()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    new-instance v4, Ljava/lang/IllegalStateException;

    invoke-direct {v4, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 31574
    const/16 v2, 0x55a

    const/16 v1, 0xc

    const/16 v0, 0x4f

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Eb;->A0I(III)Ljava/lang/String;

    move-result-object v3

    const/16 v2, 0x126

    const/16 v1, 0x118

    const/16 v0, 0x18

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Eb;->A0I(III)Ljava/lang/String;

    move-result-object v0

    invoke-static {v3, v0, v4}, Landroid/util/Log;->w(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Throwable;)I

    .line 31575
    :cond_2
    return-void
.end method

.method public final A1m(Z)V
    .locals 3

    .line 31576
    iget v1, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0U:I

    const/4 v0, 0x1

    sub-int/2addr v1, v0

    iput v1, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0U:I

    .line 31577
    if-ge v1, v0, :cond_1

    .line 31578
    const/4 v0, 0x0

    iput v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0U:I

    .line 31579
    if-eqz p1, :cond_1

    .line 31580
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/Eb;->A0K()V

    sget-object v1, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const/4 v0, 0x1

    aget-object v0, v1, v0

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v1

    const/16 v0, 0x20

    if-eq v1, v0, :cond_0

    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    .line 31581
    :cond_0
    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const-string v1, "clcdgLago0QzUCUQna9RqUvMnx3zufId"

    const/4 v0, 0x6

    aput-object v1, v2, v0

    const-string v1, "dGTmnJIudyc2xKqsUaVYnInX4VXDIHXZ"

    const/4 v0, 0x4

    aput-object v1, v2, v0

    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/Eb;->A0Z()V

    .line 31582
    :cond_1
    return-void
.end method

.method public final A1n(Z)V
    .locals 3

    .line 31583
    iget v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0O:I

    const/4 v2, 0x1

    if-ge v0, v2, :cond_0

    .line 31584
    iput v2, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0O:I

    .line 31585
    :cond_0
    const/4 v1, 0x0

    if-nez p1, :cond_1

    .line 31586
    iput-boolean v1, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0J:Z

    .line 31587
    :cond_1
    iget v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0O:I

    if-ne v0, v2, :cond_3

    .line 31588
    if-eqz p1, :cond_2

    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0J:Z

    if-eqz v0, :cond_2

    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0I:Z

    if-nez v0, :cond_2

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    if-eqz v0, :cond_2

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A04:Lcom/facebook/ads/redexgen/X/4c;

    if-eqz v0, :cond_2

    .line 31589
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/Eb;->A0Y()V

    .line 31590
    :cond_2
    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0I:Z

    if-nez v0, :cond_3

    .line 31591
    iput-boolean v1, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0J:Z

    .line 31592
    :cond_3
    iget v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0O:I

    sub-int/2addr v0, v2

    iput v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0O:I

    .line 31593
    return-void
.end method

.method public final A1o()Z
    .locals 4

    .line 31594
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A10:Landroid/view/accessibility/AccessibilityManager;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Landroid/view/accessibility/AccessibilityManager;->isEnabled()Z

    move-result v3

    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const/4 v0, 0x6

    aget-object v1, v2, v0

    const/4 v0, 0x4

    aget-object v2, v2, v0

    const/16 v0, 0x11

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    invoke-virtual {v2, v0}, Ljava/lang/String;->charAt(I)C

    move-result v0

    if-eq v1, v0, :cond_1

    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    :cond_0
    const/4 v0, 0x0

    goto :goto_0

    :cond_1
    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const-string v1, "qqYNnQooq1iBe19qNxONRBmcR9jcXf5d"

    const/4 v0, 0x5

    aput-object v1, v2, v0

    if-eqz v3, :cond_0

    const/4 v0, 0x1

    :goto_0
    return v0
.end method

.method public final A1p()Z
    .locals 1

    .line 31595
    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0D:Z

    if-eqz v0, :cond_0

    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0C:Z

    if-nez v0, :cond_0

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A00:Lcom/facebook/ads/redexgen/X/Zq;

    .line 31596
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/Zq;->A0B()Z

    move-result v0

    if-eqz v0, :cond_1

    :cond_0
    const/4 v0, 0x1

    .line 31597
    :goto_0
    return v0

    .line 31598
    :cond_1
    const/4 v0, 0x0

    goto :goto_0
.end method

.method public final A1q()Z
    .locals 1

    .line 31599
    iget v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0U:I

    if-lez v0, :cond_0

    const/4 v0, 0x1

    :goto_0
    return v0

    :cond_0
    const/4 v0, 0x0

    goto :goto_0
.end method

.method public final A1r(I)Z
    .locals 1

    .line 31600
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/Eb;->getScrollingChildHelper()Lcom/facebook/ads/redexgen/X/3B;

    move-result-object v0

    invoke-virtual {v0, p1}, Lcom/facebook/ads/redexgen/X/3B;->A09(I)Z

    move-result v0

    return v0
.end method

.method public final A1s(II)Z
    .locals 1

    .line 31601
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/Eb;->getScrollingChildHelper()Lcom/facebook/ads/redexgen/X/3B;

    move-result-object v0

    invoke-virtual {v0, p1, p2}, Lcom/facebook/ads/redexgen/X/3B;->A0B(II)Z

    move-result v0

    return v0
.end method

.method public final A1t(IIII[II)Z
    .locals 7

    .line 31602
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/Eb;->getScrollingChildHelper()Lcom/facebook/ads/redexgen/X/3B;

    move-result-object v0

    move v1, p1

    move v2, p2

    move v3, p3

    move v4, p4

    move-object v5, p5

    move v6, p6

    invoke-virtual/range {v0 .. v6}, Lcom/facebook/ads/redexgen/X/3B;->A0D(IIII[II)Z

    move-result v0

    return v0
.end method

.method public final A1u(II[I[II)Z
    .locals 6

    .line 31603
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/Eb;->getScrollingChildHelper()Lcom/facebook/ads/redexgen/X/3B;

    move-result-object v0

    move v1, p1

    move v2, p2

    move-object v3, p3

    move-object v4, p4

    move v5, p5

    invoke-virtual/range {v0 .. v5}, Lcom/facebook/ads/redexgen/X/3B;->A0F(II[I[II)Z

    move-result v0

    return v0
.end method

.method public final A1v(Landroid/view/View;)Z
    .locals 4

    .line 31604
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->A1J()V

    .line 31605
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A01:Lcom/facebook/ads/redexgen/X/4C;

    invoke-virtual {v0, p1}, Lcom/facebook/ads/redexgen/X/4C;->A0L(Landroid/view/View;)Z

    move-result v3

    .line 31606
    .local v0, "removed":Z
    if-eqz v3, :cond_0

    .line 31607
    invoke-static {p1}, Lcom/facebook/ads/redexgen/X/Eb;->A0F(Landroid/view/View;)Lcom/facebook/ads/redexgen/X/56;

    move-result-object v1

    .line 31608
    .local v1, "viewHolder":Lcom/facebook/ads/redexgen/X/56;
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0r:Lcom/facebook/ads/redexgen/X/4w;

    invoke-virtual {v0, v1}, Lcom/facebook/ads/redexgen/X/4w;->A0Y(Lcom/facebook/ads/redexgen/X/56;)V

    .line 31609
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0r:Lcom/facebook/ads/redexgen/X/4w;

    invoke-virtual {v0, v1}, Lcom/facebook/ads/redexgen/X/4w;->A0X(Lcom/facebook/ads/redexgen/X/56;)V

    .line 31610
    .end local v1    # "viewHolder":Lcom/facebook/ads/redexgen/X/56;
    :cond_0
    xor-int/lit8 v0, v3, 0x1

    invoke-virtual {p0, v0}, Lcom/facebook/ads/redexgen/X/Eb;->A1n(Z)V

    sget-object v1, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const/4 v0, 0x1

    aget-object v0, v1, v0

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v1

    const/16 v0, 0x20

    if-eq v1, v0, :cond_1

    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    .line 31611
    :cond_1
    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const-string v1, "2BT72D7B0XXfHPWJs3DbS8"

    const/4 v0, 0x3

    aput-object v1, v2, v0

    const-string v1, "RgGvPpmRJD"

    const/4 v0, 0x0

    aput-object v1, v2, v0

    return v3
.end method

.method public final A1w(Lcom/facebook/ads/redexgen/X/56;)Z
    .locals 2

    .line 31612
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/Eb;->A05:Lcom/facebook/ads/redexgen/X/4k;

    if-eqz v1, :cond_0

    .line 31613
    invoke-virtual {p1}, Lcom/facebook/ads/redexgen/X/56;->A0L()Ljava/util/List;

    move-result-object v0

    .line 31614
    invoke-virtual {v1, p1, v0}, Lcom/facebook/ads/redexgen/X/4k;->A0M(Lcom/facebook/ads/redexgen/X/56;Ljava/util/List;)Z

    move-result v0

    if-eqz v0, :cond_1

    :cond_0
    const/4 v0, 0x1

    :goto_0
    return v0

    :cond_1
    const/4 v0, 0x0

    goto :goto_0
.end method

.method public final A1x(Lcom/facebook/ads/redexgen/X/56;I)Z
    .locals 1

    .line 31615
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->A1q()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 31616
    iput p2, p1, Lcom/facebook/ads/redexgen/X/56;->A02:I

    .line 31617
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0w:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 31618
    const/4 v0, 0x0

    return v0

    .line 31619
    :cond_0
    iget-object v0, p1, Lcom/facebook/ads/redexgen/X/56;->A0H:Landroid/view/View;

    invoke-static {v0, p2}, Lcom/facebook/ads/redexgen/X/3T;->A09(Landroid/view/View;I)V

    .line 31620
    const/4 v0, 0x1

    return v0
.end method

.method public final addFocusables(Ljava/util/ArrayList;II)V
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/ArrayList<",
            "Landroid/view/View;",
            ">;II)V"
        }
    .end annotation

    .line 31621
    .local v2, "views":Ljava/util/ArrayList;, "Ljava/util/ArrayList<Landroid/view/View;>;"
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    if-eqz v0, :cond_1

    invoke-virtual {v0, p0, p1, p2, p3}, Lcom/facebook/ads/redexgen/X/4o;->A1c(Lcom/facebook/ads/redexgen/X/Eb;Ljava/util/ArrayList;II)Z

    move-result v3

    sget-object v1, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const/4 v0, 0x5

    aget-object v1, v1, v0

    const/4 v0, 0x2

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    const/16 v0, 0x59

    if-eq v1, v0, :cond_0

    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    :cond_0
    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const-string v1, "qzNrCd"

    const/4 v0, 0x2

    aput-object v1, v2, v0

    const-string v1, "bMAkXN6Kp1Dd"

    const/4 v0, 0x7

    aput-object v1, v2, v0

    if-nez v3, :cond_2

    .line 31622
    :cond_1
    invoke-super {p0, p1, p2, p3}, Landroid/view/ViewGroup;->addFocusables(Ljava/util/ArrayList;II)V

    .line 31623
    :cond_2
    return-void
.end method

.method public final checkLayoutParams(Landroid/view/ViewGroup$LayoutParams;)Z
    .locals 1

    .line 31624
    instance-of v0, p1, Lcom/facebook/ads/redexgen/X/4p;

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    check-cast p1, Lcom/facebook/ads/redexgen/X/4p;

    invoke-virtual {v0, p1}, Lcom/facebook/ads/redexgen/X/4o;->A1Y(Lcom/facebook/ads/redexgen/X/4p;)Z

    move-result v0

    if-eqz v0, :cond_0

    const/4 v0, 0x1

    :goto_0
    return v0

    :cond_0
    const/4 v0, 0x0

    goto :goto_0
.end method

.method public final computeHorizontalScrollExtent()I
    .locals 2

    .line 31625
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    const/4 v1, 0x0

    if-nez v0, :cond_0

    .line 31626
    return v1

    .line 31627
    :cond_0
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/4o;->A20()Z

    move-result v0

    if-eqz v0, :cond_1

    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0s:Lcom/facebook/ads/redexgen/X/53;

    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/4o;->A1f(Lcom/facebook/ads/redexgen/X/53;)I

    move-result v1

    :cond_1
    return v1
.end method

.method public final computeHorizontalScrollOffset()I
    .locals 2

    .line 31628
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    const/4 v1, 0x0

    if-nez v0, :cond_0

    .line 31629
    return v1

    .line 31630
    :cond_0
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/4o;->A20()Z

    move-result v0

    if-eqz v0, :cond_1

    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0s:Lcom/facebook/ads/redexgen/X/53;

    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/4o;->A1g(Lcom/facebook/ads/redexgen/X/53;)I

    move-result v1

    :cond_1
    return v1
.end method

.method public final computeHorizontalScrollRange()I
    .locals 5

    .line 31631
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    const/4 v4, 0x0

    if-nez v0, :cond_0

    .line 31632
    return v4

    .line 31633
    :cond_0
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/4o;->A20()Z

    move-result v3

    sget-object v1, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const/4 v0, 0x5

    aget-object v1, v1, v0

    const/4 v0, 0x2

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    const/16 v0, 0x59

    if-eq v1, v0, :cond_1

    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    :cond_1
    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const-string v1, "GzwOa56ahdwGOmec9vc3XIggLcxaXOnz"

    const/4 v0, 0x1

    aput-object v1, v2, v0

    if-eqz v3, :cond_2

    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0s:Lcom/facebook/ads/redexgen/X/53;

    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/4o;->A1h(Lcom/facebook/ads/redexgen/X/53;)I

    move-result v4

    :cond_2
    return v4
.end method

.method public final computeVerticalScrollExtent()I
    .locals 5

    .line 31634
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    const/4 v4, 0x0

    if-nez v0, :cond_0

    .line 31635
    return v4

    .line 31636
    :cond_0
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/4o;->A21()Z

    move-result v3

    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const/4 v0, 0x3

    aget-object v1, v2, v0

    const/4 v0, 0x0

    aget-object v0, v2, v0

    invoke-virtual {v1}, Ljava/lang/String;->length()I

    move-result v1

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v0

    if-eq v1, v0, :cond_2

    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const-string v1, "Wsbuah"

    const/4 v0, 0x2

    aput-object v1, v2, v0

    const-string v1, "lDl9g1VSjrmk"

    const/4 v0, 0x7

    aput-object v1, v2, v0

    if-eqz v3, :cond_1

    iget-object v3, p0, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const/4 v0, 0x3

    aget-object v1, v2, v0

    const/4 v0, 0x0

    aget-object v0, v2, v0

    invoke-virtual {v1}, Ljava/lang/String;->length()I

    move-result v1

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v0

    if-eq v1, v0, :cond_2

    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const-string v1, "sfYwg45p3roym5legx720iFNlg07mmUN"

    const/4 v0, 0x5

    aput-object v1, v2, v0

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0s:Lcom/facebook/ads/redexgen/X/53;

    invoke-virtual {v3, v0}, Lcom/facebook/ads/redexgen/X/4o;->A1i(Lcom/facebook/ads/redexgen/X/53;)I

    move-result v4

    :cond_1
    return v4

    :cond_2
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0
.end method

.method public final computeVerticalScrollOffset()I
    .locals 2

    .line 31637
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    const/4 v1, 0x0

    if-nez v0, :cond_0

    .line 31638
    return v1

    .line 31639
    :cond_0
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/4o;->A21()Z

    move-result v0

    if-eqz v0, :cond_1

    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0s:Lcom/facebook/ads/redexgen/X/53;

    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/4o;->A1j(Lcom/facebook/ads/redexgen/X/53;)I

    move-result v1

    :cond_1
    return v1
.end method

.method public final computeVerticalScrollRange()I
    .locals 4

    .line 31640
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    const/4 v1, 0x0

    if-nez v0, :cond_0

    .line 31641
    return v1

    .line 31642
    :cond_0
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/4o;->A21()Z

    move-result v0

    if-eqz v0, :cond_2

    iget-object v3, p0, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    sget-object v1, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const/4 v0, 0x5

    aget-object v1, v1, v0

    const/4 v0, 0x2

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    const/16 v0, 0x59

    if-eq v1, v0, :cond_1

    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    :cond_1
    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const-string v1, "NPPquBudgt65Kfhzva46C14iIBYgw5ng"

    const/4 v0, 0x6

    aput-object v1, v2, v0

    const-string v1, "gzaMlk0wrsXS1BjbnaI8sjsGlzE0u55w"

    const/4 v0, 0x4

    aput-object v1, v2, v0

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0s:Lcom/facebook/ads/redexgen/X/53;

    invoke-virtual {v3, v0}, Lcom/facebook/ads/redexgen/X/4o;->A1k(Lcom/facebook/ads/redexgen/X/53;)I

    move-result v1

    :cond_2
    return v1
.end method

.method public final dispatchNestedFling(FFZ)Z
    .locals 1

    .line 31643
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/Eb;->getScrollingChildHelper()Lcom/facebook/ads/redexgen/X/3B;

    move-result-object v0

    invoke-virtual {v0, p1, p2, p3}, Lcom/facebook/ads/redexgen/X/3B;->A08(FFZ)Z

    move-result v0

    return v0
.end method

.method public final dispatchNestedPreFling(FF)Z
    .locals 1

    .line 31644
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/Eb;->getScrollingChildHelper()Lcom/facebook/ads/redexgen/X/3B;

    move-result-object v0

    invoke-virtual {v0, p1, p2}, Lcom/facebook/ads/redexgen/X/3B;->A07(FF)Z

    move-result v0

    return v0
.end method

.method public final dispatchNestedPreScroll(II[I[I)Z
    .locals 1

    .line 31645
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/Eb;->getScrollingChildHelper()Lcom/facebook/ads/redexgen/X/3B;

    move-result-object v0

    invoke-virtual {v0, p1, p2, p3, p4}, Lcom/facebook/ads/redexgen/X/3B;->A0E(II[I[I)Z

    move-result v0

    return v0
.end method

.method public final dispatchNestedScroll(IIII[I)Z
    .locals 6

    .line 31646
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/Eb;->getScrollingChildHelper()Lcom/facebook/ads/redexgen/X/3B;

    move-result-object v0

    move v1, p1

    move v2, p2

    move v3, p3

    move v4, p4

    move-object v5, p5

    invoke-virtual/range {v0 .. v5}, Lcom/facebook/ads/redexgen/X/3B;->A0C(IIII[I)Z

    move-result v0

    return v0
.end method

.method public final dispatchRestoreInstanceState(Landroid/util/SparseArray;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/util/SparseArray<",
            "Landroid/os/Parcelable;",
            ">;)V"
        }
    .end annotation

    .line 31647
    .local p1, "container":Landroid/util/SparseArray;, "Landroid/util/SparseArray<Landroid/os/Parcelable;>;"
    invoke-virtual {p0, p1}, Lcom/facebook/ads/redexgen/X/Eb;->dispatchThawSelfOnly(Landroid/util/SparseArray;)V

    .line 31648
    return-void
.end method

.method public final dispatchSaveInstanceState(Landroid/util/SparseArray;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/util/SparseArray<",
            "Landroid/os/Parcelable;",
            ">;)V"
        }
    .end annotation

    .line 31649
    .local p1, "container":Landroid/util/SparseArray;, "Landroid/util/SparseArray<Landroid/os/Parcelable;>;"
    invoke-virtual {p0, p1}, Lcom/facebook/ads/redexgen/X/Eb;->dispatchFreezeSelfOnly(Landroid/util/SparseArray;)V

    .line 31650
    return-void
.end method

.method public final draw(Landroid/graphics/Canvas;)V
    .locals 7

    .line 31651
    invoke-super {p0, p1}, Landroid/view/ViewGroup;->draw(Landroid/graphics/Canvas;)V

    .line 31652
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0v:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->size()I

    move-result v2

    .line 31653
    .local v0, "count":I
    const/4 v1, 0x0

    .local v1, "i":I
    :goto_0
    if-ge v1, v2, :cond_0

    .line 31654
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0v:Ljava/util/ArrayList;

    invoke-virtual {v0, v1}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    .line 31655
    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    .line 31656
    .end local v1    # "i":I
    :cond_0
    const/4 v5, 0x0

    .line 31657
    .local v1, "needsInvalidate":Z
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0a:Landroid/widget/EdgeEffect;

    const/4 v6, 0x1

    if-eqz v0, :cond_1

    invoke-virtual {v0}, Landroid/widget/EdgeEffect;->isFinished()Z

    move-result v0

    if-nez v0, :cond_1

    .line 31658
    invoke-virtual {p1}, Landroid/graphics/Canvas;->save()I

    move-result v2

    .line 31659
    .local v2, "restore":I
    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0B:Z

    if-eqz v0, :cond_f

    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->getPaddingBottom()I

    move-result v1

    .line 31660
    .local v5, "padding":I
    :goto_1
    const/high16 v0, 0x43870000    # 270.0f

    invoke-virtual {p1, v0}, Landroid/graphics/Canvas;->rotate(F)V

    .line 31661
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->getHeight()I

    move-result v0

    neg-int v0, v0

    add-int/2addr v0, v1

    int-to-float v1, v0

    const/4 v0, 0x0

    invoke-virtual {p1, v1, v0}, Landroid/graphics/Canvas;->translate(FF)V

    .line 31662
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0a:Landroid/widget/EdgeEffect;

    if-eqz v0, :cond_e

    invoke-virtual {v0, p1}, Landroid/widget/EdgeEffect;->draw(Landroid/graphics/Canvas;)Z

    move-result v0

    if-eqz v0, :cond_e

    const/4 v5, 0x1

    .line 31663
    :goto_2
    invoke-virtual {p1, v2}, Landroid/graphics/Canvas;->restoreToCount(I)V

    .line 31664
    .end local v2    # "restore":I
    .end local v5    # "padding":I
    :cond_1
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0c:Landroid/widget/EdgeEffect;

    if-eqz v0, :cond_3

    invoke-virtual {v0}, Landroid/widget/EdgeEffect;->isFinished()Z

    move-result v0

    if-nez v0, :cond_3

    .line 31665
    invoke-virtual {p1}, Landroid/graphics/Canvas;->save()I

    move-result v2

    .line 31666
    .restart local v2    # "restore":I
    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0B:Z

    if-eqz v0, :cond_2

    .line 31667
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->getPaddingLeft()I

    move-result v0

    int-to-float v1, v0

    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->getPaddingTop()I

    move-result v0

    int-to-float v0, v0

    invoke-virtual {p1, v1, v0}, Landroid/graphics/Canvas;->translate(FF)V

    .line 31668
    :cond_2
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0c:Landroid/widget/EdgeEffect;

    if-eqz v0, :cond_d

    invoke-virtual {v0, p1}, Landroid/widget/EdgeEffect;->draw(Landroid/graphics/Canvas;)Z

    move-result v0

    if-eqz v0, :cond_d

    const/4 v0, 0x1

    :goto_3
    or-int/2addr v5, v0

    .line 31669
    invoke-virtual {p1, v2}, Landroid/graphics/Canvas;->restoreToCount(I)V

    .line 31670
    .end local v2    # "restore":I
    :cond_3
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0b:Landroid/widget/EdgeEffect;

    if-eqz v0, :cond_4

    invoke-virtual {v0}, Landroid/widget/EdgeEffect;->isFinished()Z

    move-result v0

    if-nez v0, :cond_4

    .line 31671
    invoke-virtual {p1}, Landroid/graphics/Canvas;->save()I

    move-result v4

    .line 31672
    .restart local v2    # "restore":I
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->getWidth()I

    move-result v2

    .line 31673
    .local v5, "width":I
    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0B:Z

    if-eqz v0, :cond_c

    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->getPaddingTop()I

    move-result v1

    .line 31674
    .local v6, "padding":I
    :goto_4
    const/high16 v0, 0x42b40000    # 90.0f

    invoke-virtual {p1, v0}, Landroid/graphics/Canvas;->rotate(F)V

    .line 31675
    neg-int v0, v1

    int-to-float v1, v0

    neg-int v0, v2

    int-to-float v0, v0

    invoke-virtual {p1, v1, v0}, Landroid/graphics/Canvas;->translate(FF)V

    .line 31676
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0b:Landroid/widget/EdgeEffect;

    if-eqz v0, :cond_b

    invoke-virtual {v0, p1}, Landroid/widget/EdgeEffect;->draw(Landroid/graphics/Canvas;)Z

    move-result v3

    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const/4 v0, 0x2

    aget-object v1, v2, v0

    const/4 v0, 0x7

    aget-object v0, v2, v0

    invoke-virtual {v1}, Ljava/lang/String;->length()I

    move-result v1

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v0

    if-eq v1, v0, :cond_10

    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const-string v1, "ItYbouux6ivob9pa5cwYanZ1Qwg84bbh"

    const/4 v0, 0x5

    aput-object v1, v2, v0

    if-eqz v3, :cond_b

    const/4 v0, 0x1

    :goto_5
    or-int/2addr v5, v0

    .line 31677
    invoke-virtual {p1, v4}, Landroid/graphics/Canvas;->restoreToCount(I)V

    .line 31678
    .end local v2    # "restore":I
    .end local v5    # "width":I
    .end local v6    # "padding":I
    :cond_4
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0Z:Landroid/widget/EdgeEffect;

    if-eqz v0, :cond_5

    invoke-virtual {v0}, Landroid/widget/EdgeEffect;->isFinished()Z

    move-result v0

    if-nez v0, :cond_5

    .line 31679
    invoke-virtual {p1}, Landroid/graphics/Canvas;->save()I

    move-result v3

    .line 31680
    .restart local v2    # "restore":I
    const/high16 v0, 0x43340000    # 180.0f

    invoke-virtual {p1, v0}, Landroid/graphics/Canvas;->rotate(F)V

    .line 31681
    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0B:Z

    if-eqz v0, :cond_a

    .line 31682
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->getWidth()I

    move-result v0

    neg-int v1, v0

    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->getPaddingRight()I

    move-result v0

    add-int/2addr v1, v0

    int-to-float v2, v1

    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->getHeight()I

    move-result v0

    neg-int v1, v0

    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->getPaddingBottom()I

    move-result v0

    add-int/2addr v1, v0

    int-to-float v0, v1

    invoke-virtual {p1, v2, v0}, Landroid/graphics/Canvas;->translate(FF)V

    .line 31683
    :goto_6
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0Z:Landroid/widget/EdgeEffect;

    if-eqz v0, :cond_9

    invoke-virtual {v0, p1}, Landroid/widget/EdgeEffect;->draw(Landroid/graphics/Canvas;)Z

    move-result v0

    if-eqz v0, :cond_9

    :goto_7
    or-int/2addr v5, v6

    .line 31684
    invoke-virtual {p1, v3}, Landroid/graphics/Canvas;->restoreToCount(I)V

    .line 31685
    .end local v2    # "restore":I
    :cond_5
    if-nez v5, :cond_6

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A05:Lcom/facebook/ads/redexgen/X/4k;

    if-eqz v0, :cond_6

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0v:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->size()I

    move-result v0

    if-lez v0, :cond_6

    iget-object v3, p0, Lcom/facebook/ads/redexgen/X/Eb;->A05:Lcom/facebook/ads/redexgen/X/4k;

    sget-object v1, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const/4 v0, 0x5

    aget-object v1, v1, v0

    const/4 v0, 0x2

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    const/16 v0, 0x59

    if-eq v1, v0, :cond_8

    .line 31686
    invoke-virtual {v3}, Lcom/facebook/ads/redexgen/X/4k;->A0L()Z

    move-result v0

    if-eqz v0, :cond_6

    .line 31687
    :goto_8
    const/4 v5, 0x1

    .line 31688
    :cond_6
    if-eqz v5, :cond_7

    .line 31689
    invoke-static {p0}, Lcom/facebook/ads/redexgen/X/3T;->A07(Landroid/view/View;)V

    .line 31690
    :cond_7
    return-void

    .line 31691
    :cond_8
    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const-string v1, "slYCg9imVkQI2IB5irC0agqTwBjTAdyw"

    const/4 v0, 0x5

    aput-object v1, v2, v0

    invoke-virtual {v3}, Lcom/facebook/ads/redexgen/X/4k;->A0L()Z

    move-result v0

    if-eqz v0, :cond_6

    goto :goto_8

    .line 31692
    :cond_9
    const/4 v6, 0x0

    goto :goto_7

    .line 31693
    :cond_a
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->getWidth()I

    move-result v0

    neg-int v0, v0

    int-to-float v1, v0

    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->getHeight()I

    move-result v0

    neg-int v0, v0

    int-to-float v0, v0

    invoke-virtual {p1, v1, v0}, Landroid/graphics/Canvas;->translate(FF)V

    goto :goto_6

    .line 31694
    :cond_b
    const/4 v0, 0x0

    goto/16 :goto_5

    .line 31695
    :cond_c
    const/4 v1, 0x0

    goto/16 :goto_4

    .line 31696
    :cond_d
    const/4 v0, 0x0

    goto/16 :goto_3

    .line 31697
    :cond_e
    const/4 v5, 0x0

    goto/16 :goto_2

    .line 31698
    :cond_f
    const/4 v1, 0x0

    goto/16 :goto_1

    :cond_10
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0
.end method

.method public final drawChild(Landroid/graphics/Canvas;Landroid/view/View;J)Z
    .locals 1

    .line 31699
    invoke-super {p0, p1, p2, p3, p4}, Landroid/view/ViewGroup;->drawChild(Landroid/graphics/Canvas;Landroid/view/View;J)Z

    move-result v0

    return v0
.end method

.method public final focusSearch(Landroid/view/View;I)Landroid/view/View;
    .locals 8

    .line 31700
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    invoke-virtual {v0, p1, p2}, Lcom/facebook/ads/redexgen/X/4o;->A0u(Landroid/view/View;I)Landroid/view/View;

    move-result-object v0

    .line 31701
    .local v0, "result":Landroid/view/View;
    if-eqz v0, :cond_0

    .line 31702
    return-object v0

    .line 31703
    :cond_0
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A04:Lcom/facebook/ads/redexgen/X/4c;

    const/4 v6, 0x1

    const/4 v3, 0x0

    if-eqz v0, :cond_a

    iget-object v4, p0, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const/4 v0, 0x2

    aget-object v1, v2, v0

    const/4 v0, 0x7

    aget-object v0, v2, v0

    invoke-virtual {v1}, Ljava/lang/String;->length()I

    move-result v1

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v0

    if-eq v1, v0, :cond_13

    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const-string v1, "RhZxCClAx00iKsBYiNgalY"

    const/4 v0, 0x3

    aput-object v1, v2, v0

    const-string v1, "xpwAZ9hAV8"

    const/4 v0, 0x0

    aput-object v1, v2, v0

    if-eqz v4, :cond_a

    .line 31704
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->A1q()Z

    move-result v0

    if-nez v0, :cond_a

    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0I:Z

    if-nez v0, :cond_a

    const/4 v0, 0x1

    .line 31705
    .local v1, "canRunFocusFailure":Z
    :goto_0
    invoke-static {}, Landroid/view/FocusFinder;->getInstance()Landroid/view/FocusFinder;

    move-result-object v5

    .line 31706
    .local v4, "ff":Landroid/view/FocusFinder;
    const/4 v4, 0x0

    if-eqz v0, :cond_d

    const/4 v7, 0x2

    if-eq p2, v7, :cond_1

    if-ne p2, v6, :cond_d

    .line 31707
    :cond_1
    const/4 v2, 0x0

    .line 31708
    .local v7, "needsFocusFailureLayout":Z
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/4o;->A21()Z

    move-result v0

    if-eqz v0, :cond_2

    .line 31709
    if-ne p2, v7, :cond_9

    const/16 v1, 0x82

    .line 31710
    .local p0, "absDir":I
    :goto_1
    invoke-virtual {v5, p0, p1, v1}, Landroid/view/FocusFinder;->findNextFocus(Landroid/view/ViewGroup;Landroid/view/View;I)Landroid/view/View;

    move-result-object v0

    .line 31711
    .local p1, "found":Landroid/view/View;
    if-nez v0, :cond_8

    const/4 v2, 0x1

    .line 31712
    :goto_2
    sget-boolean v0, Lcom/facebook/ads/redexgen/X/Eb;->A1F:Z

    if-eqz v0, :cond_2

    .line 31713
    move p2, v1

    .line 31714
    .end local p0    # "absDir":I
    .end local p1    # "found":Landroid/view/View;
    :cond_2
    if-nez v2, :cond_3

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/4o;->A20()Z

    move-result v0

    if-eqz v0, :cond_3

    .line 31715
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/4o;->A0a()I

    move-result v0

    if-ne v0, v6, :cond_7

    const/4 v1, 0x1

    .line 31716
    .local p0, "rtl":Z
    :goto_3
    if-ne p2, v7, :cond_6

    const/4 v0, 0x1

    :goto_4
    xor-int/2addr v0, v1

    if-eqz v0, :cond_5

    .line 31717
    const/16 v1, 0x42

    .line 31718
    .local v6, "absDir":I
    :goto_5
    invoke-virtual {v5, p0, p1, v1}, Landroid/view/FocusFinder;->findNextFocus(Landroid/view/ViewGroup;Landroid/view/View;I)Landroid/view/View;

    move-result-object v0

    .line 31719
    .restart local p1    # "found":Landroid/view/View;
    if-nez v0, :cond_4

    :goto_6
    move v2, v6

    .line 31720
    sget-boolean v0, Lcom/facebook/ads/redexgen/X/Eb;->A1F:Z

    if-eqz v0, :cond_3

    .line 31721
    move p2, v1

    .line 31722
    .end local v6    # "absDir":I
    .end local p0    # "rtl":Z
    .end local p1    # "found":Landroid/view/View;
    :cond_3
    if-eqz v2, :cond_c

    .line 31723
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->A1I()V

    .line 31724
    invoke-virtual {p0, p1}, Lcom/facebook/ads/redexgen/X/Eb;->A1E(Landroid/view/View;)Landroid/view/View;

    move-result-object v0

    .line 31725
    .local v2, "focusedItemView":Landroid/view/View;
    if-nez v0, :cond_b

    .line 31726
    return-object v4

    .line 31727
    :cond_4
    const/4 v6, 0x0

    goto :goto_6

    .line 31728
    :cond_5
    const/16 v1, 0x11

    goto :goto_5

    .line 31729
    :cond_6
    const/4 v0, 0x0

    goto :goto_4

    .line 31730
    :cond_7
    const/4 v1, 0x0

    goto :goto_3

    .line 31731
    :cond_8
    const/4 v2, 0x0

    goto :goto_2

    .line 31732
    :cond_9
    const/16 v1, 0x21

    goto :goto_1

    .line 31733
    :cond_a
    const/4 v0, 0x0

    goto :goto_0

    .line 31734
    :cond_b
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->A1J()V

    .line 31735
    iget-object v2, p0, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0r:Lcom/facebook/ads/redexgen/X/4w;

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0s:Lcom/facebook/ads/redexgen/X/53;

    invoke-virtual {v2, p1, p2, v1, v0}, Lcom/facebook/ads/redexgen/X/4o;->A1n(Landroid/view/View;ILcom/facebook/ads/redexgen/X/4w;Lcom/facebook/ads/redexgen/X/53;)Landroid/view/View;

    .line 31736
    invoke-virtual {p0, v3}, Lcom/facebook/ads/redexgen/X/Eb;->A1n(Z)V

    .line 31737
    .end local v2    # "focusedItemView":Landroid/view/View;
    :cond_c
    invoke-virtual {v5, p0, p1, p2}, Landroid/view/FocusFinder;->findNextFocus(Landroid/view/ViewGroup;Landroid/view/View;I)Landroid/view/View;

    move-result-object v1

    .line 31738
    .end local v7    # "needsFocusFailureLayout":Z
    goto :goto_7

    .line 31739
    :cond_d
    invoke-virtual {v5, p0, p1, p2}, Landroid/view/FocusFinder;->findNextFocus(Landroid/view/ViewGroup;Landroid/view/View;I)Landroid/view/View;

    move-result-object v1

    .line 31740
    if-nez v1, :cond_f

    if-eqz v0, :cond_f

    .line 31741
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->A1I()V

    .line 31742
    invoke-virtual {p0, p1}, Lcom/facebook/ads/redexgen/X/Eb;->A1E(Landroid/view/View;)Landroid/view/View;

    move-result-object v0

    .line 31743
    .restart local v2    # "focusedItemView":Landroid/view/View;
    if-nez v0, :cond_e

    .line 31744
    return-object v4

    .line 31745
    :cond_e
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->A1J()V

    .line 31746
    iget-object v2, p0, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0r:Lcom/facebook/ads/redexgen/X/4w;

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0s:Lcom/facebook/ads/redexgen/X/53;

    invoke-virtual {v2, p1, p2, v1, v0}, Lcom/facebook/ads/redexgen/X/4o;->A1n(Landroid/view/View;ILcom/facebook/ads/redexgen/X/4w;Lcom/facebook/ads/redexgen/X/53;)Landroid/view/View;

    move-result-object v1

    .line 31747
    invoke-virtual {p0, v3}, Lcom/facebook/ads/redexgen/X/Eb;->A1n(Z)V

    .line 31748
    .end local v2    # "focusedItemView":Landroid/view/View;
    :cond_f
    :goto_7
    if-eqz v1, :cond_11

    invoke-virtual {v1}, Landroid/view/View;->hasFocusable()Z

    move-result v0

    if-nez v0, :cond_11

    .line 31749
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->getFocusedChild()Landroid/view/View;

    move-result-object v0

    if-nez v0, :cond_10

    .line 31750
    invoke-super {p0, p1, p2}, Landroid/view/ViewGroup;->focusSearch(Landroid/view/View;I)Landroid/view/View;

    move-result-object v0

    return-object v0

    .line 31751
    :cond_10
    invoke-direct {p0, v1, v4}, Lcom/facebook/ads/redexgen/X/Eb;->A0p(Landroid/view/View;Landroid/view/View;)V

    .line 31752
    return-object p1

    .line 31753
    :cond_11
    invoke-direct {p0, p1, v1, p2}, Lcom/facebook/ads/redexgen/X/Eb;->A16(Landroid/view/View;Landroid/view/View;I)Z

    move-result v0

    if-eqz v0, :cond_12

    :goto_8
    return-object v1

    .line 31754
    :cond_12
    invoke-super {p0, p1, p2}, Landroid/view/ViewGroup;->focusSearch(Landroid/view/View;I)Landroid/view/View;

    move-result-object v1

    goto :goto_8

    :cond_13
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0
.end method

.method public final generateDefaultLayoutParams()Landroid/view/ViewGroup$LayoutParams;
    .locals 4

    .line 31755
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    if-eqz v0, :cond_0

    .line 31756
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/4o;->A1o()Lcom/facebook/ads/redexgen/X/4p;

    move-result-object v0

    return-object v0

    .line 31757
    :cond_0
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const/16 v2, 0x5c3

    const/16 v1, 0x21

    const/16 v0, 0x11

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Eb;->A0I(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->A1H()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    new-instance v0, Ljava/lang/IllegalStateException;

    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw v0
.end method

.method public final generateLayoutParams(Landroid/util/AttributeSet;)Landroid/view/ViewGroup$LayoutParams;
    .locals 4

    .line 31758
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    if-eqz v1, :cond_0

    .line 31759
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {v1, v0, p1}, Lcom/facebook/ads/redexgen/X/4o;->A0v(Landroid/content/Context;Landroid/util/AttributeSet;)Lcom/facebook/ads/redexgen/X/4p;

    move-result-object v0

    return-object v0

    .line 31760
    :cond_0
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const/16 v2, 0x5c3

    const/16 v1, 0x21

    const/16 v0, 0x11

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Eb;->A0I(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->A1H()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    new-instance v0, Ljava/lang/IllegalStateException;

    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw v0
.end method

.method public final generateLayoutParams(Landroid/view/ViewGroup$LayoutParams;)Landroid/view/ViewGroup$LayoutParams;
    .locals 4

    .line 31761
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    if-eqz v0, :cond_1

    .line 31762
    invoke-virtual {v0, p1}, Lcom/facebook/ads/redexgen/X/4o;->A0w(Landroid/view/ViewGroup$LayoutParams;)Lcom/facebook/ads/redexgen/X/4p;

    move-result-object v3

    sget-object v1, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const/4 v0, 0x1

    aget-object v0, v1, v0

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v1

    const/16 v0, 0x20

    if-eq v1, v0, :cond_0

    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    :cond_0
    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const-string v1, "4uZ9QAUZcjEo06EXHPHPN1"

    const/4 v0, 0x3

    aput-object v1, v2, v0

    const-string v1, "ePNmh1lbEA"

    const/4 v0, 0x0

    aput-object v1, v2, v0

    return-object v3

    .line 31763
    :cond_1
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const/16 v2, 0x5c3

    const/16 v1, 0x21

    const/16 v0, 0x11

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Eb;->A0I(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->A1H()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    new-instance v0, Ljava/lang/IllegalStateException;

    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw v0
.end method

.method public getAdapter()Lcom/facebook/ads/redexgen/X/4c;
    .locals 1

    .line 31764
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A04:Lcom/facebook/ads/redexgen/X/4c;

    return-object v0
.end method

.method public getBaseline()I
    .locals 4

    .line 31765
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    if-eqz v0, :cond_1

    .line 31766
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/4o;->A0V()I

    move-result v3

    sget-object v1, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const/4 v0, 0x1

    aget-object v0, v1, v0

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v1

    const/16 v0, 0x20

    if-eq v1, v0, :cond_0

    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    :cond_0
    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const-string v1, "WUntfQVcja1qFzj09nDprclvV6sI7lKs"

    const/4 v0, 0x1

    aput-object v1, v2, v0

    return v3

    .line 31767
    :cond_1
    invoke-super {p0}, Landroid/view/ViewGroup;->getBaseline()I

    move-result v0

    return v0
.end method

.method public final getChildDrawingOrder(II)I
    .locals 3

    .line 31768
    const/4 v0, 0x0

    if-nez v0, :cond_0

    .line 31769
    invoke-super {p0, p1, p2}, Landroid/view/ViewGroup;->getChildDrawingOrder(II)I

    move-result v0

    return v0

    .line 31770
    :cond_0
    const/16 v2, 0x79f

    const/16 v1, 0x16

    const/16 v0, 0x23

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Eb;->A0I(III)Ljava/lang/String;

    move-result-object v1

    new-instance v0, Ljava/lang/NullPointerException;

    invoke-direct {v0, v1}, Ljava/lang/NullPointerException;-><init>(Ljava/lang/String;)V

    throw v0
.end method

.method public getClipToPadding()Z
    .locals 1

    .line 31771
    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0B:Z

    return v0
.end method

.method public getCompatAccessibilityDelegate()Lcom/facebook/ads/redexgen/X/Zb;
    .locals 1

    .line 31772
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A09:Lcom/facebook/ads/redexgen/X/Zb;

    return-object v0
.end method

.method public getItemAnimator()Lcom/facebook/ads/redexgen/X/4k;
    .locals 1

    .line 31773
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A05:Lcom/facebook/ads/redexgen/X/4k;

    return-object v0
.end method

.method public getLayoutManager()Lcom/facebook/ads/redexgen/X/4o;
    .locals 1

    .line 31774
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    return-object v0
.end method

.method public getMaxFlingVelocity()I
    .locals 1

    .line 31775
    iget v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0x:I

    return v0
.end method

.method public getMinFlingVelocity()I
    .locals 1

    .line 31776
    iget v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0y:I

    return v0
.end method

.method public getNanoTime()J
    .locals 2

    .line 31777
    sget-boolean v0, Lcom/facebook/ads/redexgen/X/Eb;->A1E:Z

    if-eqz v0, :cond_0

    .line 31778
    invoke-static {}, Ljava/lang/System;->nanoTime()J

    move-result-wide v0

    return-wide v0

    .line 31779
    :cond_0
    const-wide/16 v0, 0x0

    return-wide v0
.end method

.method public getOnFlingListener()Lcom/facebook/ads/redexgen/X/4r;
    .locals 1

    .line 31780
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0g:Lcom/facebook/ads/redexgen/X/4r;

    return-object v0
.end method

.method public getPreserveFocusAfterLayout()Z
    .locals 1

    .line 31781
    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0o:Z

    return v0
.end method

.method public getRecycledViewPool()Lcom/facebook/ads/redexgen/X/4v;
    .locals 1

    .line 31782
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0r:Lcom/facebook/ads/redexgen/X/4w;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/4w;->A0H()Lcom/facebook/ads/redexgen/X/4v;

    move-result-object v0

    return-object v0
.end method

.method public getScrollState()I
    .locals 1

    .line 31783
    iget v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0W:I

    return v0
.end method

.method public final hasNestedScrollingParent()Z
    .locals 1

    .line 31787
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/Eb;->getScrollingChildHelper()Lcom/facebook/ads/redexgen/X/3B;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/3B;->A05()Z

    move-result v0

    return v0
.end method

.method public final isAttachedToWindow()Z
    .locals 1

    .line 31788
    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0F:Z

    return v0
.end method

.method public final isNestedScrollingEnabled()Z
    .locals 1

    .line 31789
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/Eb;->getScrollingChildHelper()Lcom/facebook/ads/redexgen/X/3B;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/3B;->A06()Z

    move-result v0

    return v0
.end method

.method public final onAttachedToWindow()V
    .locals 6

    .line 31790
    invoke-super {p0}, Landroid/view/ViewGroup;->onAttachedToWindow()V

    .line 31791
    const/4 v2, 0x0

    iput v2, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0U:I

    .line 31792
    const/4 v1, 0x1

    iput-boolean v1, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0F:Z

    .line 31793
    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0D:Z

    if-eqz v0, :cond_4

    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->isLayoutRequested()Z

    move-result v0

    if-nez v0, :cond_4

    :goto_0
    iput-boolean v1, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0D:Z

    .line 31794
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    if-eqz v0, :cond_0

    .line 31795
    invoke-virtual {v0, p0}, Lcom/facebook/ads/redexgen/X/4o;->A1N(Lcom/facebook/ads/redexgen/X/Eb;)V

    .line 31796
    :cond_0
    iput-boolean v2, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0K:Z

    .line 31797
    sget-boolean v0, Lcom/facebook/ads/redexgen/X/Eb;->A1E:Z

    if-eqz v0, :cond_3

    .line 31798
    sget-object v0, Lcom/facebook/ads/redexgen/X/4P;->A07:Ljava/lang/ThreadLocal;

    invoke-virtual {v0}, Ljava/lang/ThreadLocal;->get()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/facebook/ads/redexgen/X/4P;

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A03:Lcom/facebook/ads/redexgen/X/4P;

    .line 31799
    if-nez v0, :cond_2

    .line 31800
    new-instance v0, Lcom/facebook/ads/redexgen/X/4P;

    invoke-direct {v0}, Lcom/facebook/ads/redexgen/X/4P;-><init>()V

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A03:Lcom/facebook/ads/redexgen/X/4P;

    .line 31801
    invoke-static {p0}, Lcom/facebook/ads/redexgen/X/3T;->A04(Landroid/view/View;)Landroid/view/Display;

    move-result-object v1

    .line 31802
    .local v0, "display":Landroid/view/Display;
    const/high16 v5, 0x42700000    # 60.0f

    .line 31803
    .local v1, "refreshRate":F
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->isInEditMode()Z

    move-result v0

    if-nez v0, :cond_1

    if-eqz v1, :cond_1

    .line 31804
    invoke-virtual {v1}, Landroid/view/Display;->getRefreshRate()F

    move-result v4

    .line 31805
    .local v2, "displayRefreshRate":F
    const/high16 v3, 0x41f00000    # 30.0f

    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const/4 v0, 0x2

    aget-object v1, v2, v0

    const/4 v0, 0x7

    aget-object v0, v2, v0

    invoke-virtual {v1}, Ljava/lang/String;->length()I

    move-result v1

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v0

    if-eq v1, v0, :cond_5

    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const-string v1, "HPKMSTrO6rgllPECADdom7"

    const/4 v0, 0x3

    aput-object v1, v2, v0

    const-string v1, "yPSN9KqlOS"

    const/4 v0, 0x0

    aput-object v1, v2, v0

    cmpl-float v0, v4, v3

    if-ltz v0, :cond_1

    .line 31806
    move v5, v4

    .line 31807
    .end local v2    # "displayRefreshRate":F
    :cond_1
    iget-object v2, p0, Lcom/facebook/ads/redexgen/X/Eb;->A03:Lcom/facebook/ads/redexgen/X/4P;

    const v0, 0x4e6e6b28    # 1.0E9f

    div-float/2addr v0, v5

    float-to-long v0, v0

    iput-wide v0, v2, Lcom/facebook/ads/redexgen/X/4P;->A00:J

    .line 31808
    sget-object v1, Lcom/facebook/ads/redexgen/X/4P;->A07:Ljava/lang/ThreadLocal;

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A03:Lcom/facebook/ads/redexgen/X/4P;

    invoke-virtual {v1, v0}, Ljava/lang/ThreadLocal;->set(Ljava/lang/Object;)V

    .line 31809
    .end local v0    # "display":Landroid/view/Display;
    .end local v1    # "refreshRate":F
    :cond_2
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A03:Lcom/facebook/ads/redexgen/X/4P;

    invoke-virtual {v0, p0}, Lcom/facebook/ads/redexgen/X/4P;->A09(Lcom/facebook/ads/redexgen/X/Eb;)V

    .line 31810
    :cond_3
    return-void

    .line 31811
    :cond_4
    const/4 v1, 0x0

    goto :goto_0

    :cond_5
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0
.end method

.method public final onDetachedFromWindow()V
    .locals 2

    .line 31812
    invoke-super {p0}, Landroid/view/ViewGroup;->onDetachedFromWindow()V

    .line 31813
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A05:Lcom/facebook/ads/redexgen/X/4k;

    if-eqz v0, :cond_0

    .line 31814
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/4k;->A0I()V

    .line 31815
    :cond_0
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->A1R()V

    .line 31816
    const/4 v0, 0x0

    iput-boolean v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0F:Z

    .line 31817
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    if-eqz v1, :cond_1

    .line 31818
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0r:Lcom/facebook/ads/redexgen/X/4w;

    invoke-virtual {v1, p0, v0}, Lcom/facebook/ads/redexgen/X/4o;->A1Q(Lcom/facebook/ads/redexgen/X/Eb;Lcom/facebook/ads/redexgen/X/4w;)V

    .line 31819
    :cond_1
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0w:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->clear()V

    .line 31820
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0k:Ljava/lang/Runnable;

    invoke-virtual {p0, v0}, Lcom/facebook/ads/redexgen/X/Eb;->removeCallbacks(Ljava/lang/Runnable;)Z

    .line 31821
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0t:Lcom/facebook/ads/redexgen/X/5E;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/5E;->A07()V

    .line 31822
    sget-boolean v0, Lcom/facebook/ads/redexgen/X/Eb;->A1E:Z

    if-eqz v0, :cond_2

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A03:Lcom/facebook/ads/redexgen/X/4P;

    if-eqz v0, :cond_2

    .line 31823
    invoke-virtual {v0, p0}, Lcom/facebook/ads/redexgen/X/4P;->A0A(Lcom/facebook/ads/redexgen/X/Eb;)V

    .line 31824
    const/4 v0, 0x0

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A03:Lcom/facebook/ads/redexgen/X/4P;

    .line 31825
    :cond_2
    return-void
.end method

.method public final onDraw(Landroid/graphics/Canvas;)V
    .locals 3

    .line 31826
    invoke-super {p0, p1}, Landroid/view/ViewGroup;->onDraw(Landroid/graphics/Canvas;)V

    .line 31827
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0v:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->size()I

    move-result v2

    .line 31828
    .local v0, "count":I
    const/4 v1, 0x0

    .local v1, "i":I
    :goto_0
    if-ge v1, v2, :cond_0

    .line 31829
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0v:Ljava/util/ArrayList;

    invoke-virtual {v0, v1}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    .line 31830
    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    .line 31831
    .end local v1    # "i":I
    :cond_0
    return-void
.end method

.method public final onGenericMotionEvent(Landroid/view/MotionEvent;)Z
    .locals 6

    .line 31832
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    const/4 v5, 0x0

    if-nez v0, :cond_0

    .line 31833
    return v5

    .line 31834
    :cond_0
    iget-boolean v3, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0I:Z

    sget-object v1, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const/4 v0, 0x1

    aget-object v0, v1, v0

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v1

    const/16 v0, 0x20

    if-eq v1, v0, :cond_1

    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    :cond_1
    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const-string v1, "hfCCVZ"

    const/4 v0, 0x2

    aput-object v1, v2, v0

    const-string v1, "6Rxn0wTGDCgs"

    const/4 v0, 0x7

    aput-object v1, v2, v0

    if-eqz v3, :cond_2

    .line 31835
    return v5

    .line 31836
    :cond_2
    invoke-virtual {p1}, Landroid/view/MotionEvent;->getAction()I

    move-result v1

    const/16 v0, 0x8

    if-ne v1, v0, :cond_4

    .line 31837
    invoke-virtual {p1}, Landroid/view/MotionEvent;->getSource()I

    move-result v0

    and-int/lit8 v0, v0, 0x2

    if-eqz v0, :cond_7

    .line 31838
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/4o;->A21()Z

    move-result v0

    if-eqz v0, :cond_6

    .line 31839
    const/16 v0, 0x9

    invoke-virtual {p1, v0}, Landroid/view/MotionEvent;->getAxisValue(I)F

    move-result v0

    neg-float v0, v0

    .line 31840
    .local v0, "vScroll":F
    .restart local v0    # "vScroll":F
    :goto_0
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    invoke-virtual {v1}, Lcom/facebook/ads/redexgen/X/4o;->A20()Z

    move-result v1

    if-eqz v1, :cond_5

    .line 31841
    const/16 v1, 0xa

    invoke-virtual {p1, v1}, Landroid/view/MotionEvent;->getAxisValue(I)F

    move-result v1

    .line 31842
    .local v2, "hScroll":F
    .restart local v2    # "hScroll":F
    :goto_1
    const/4 v3, 0x0

    cmpl-float v2, v0, v3

    if-nez v2, :cond_3

    cmpl-float v2, v1, v3

    if-eqz v2, :cond_4

    .line 31843
    :cond_3
    iget v2, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0L:F

    mul-float/2addr v2, v1

    float-to-int v2, v2

    iget v1, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0M:F

    mul-float/2addr v1, v0

    float-to-int v0, v1

    invoke-direct {p0, v2, v0, p1}, Lcom/facebook/ads/redexgen/X/Eb;->A13(IILandroid/view/MotionEvent;)Z

    .line 31844
    .end local v0    # "vScroll":F
    .end local v2    # "hScroll":F
    :cond_4
    return v5

    .line 31845
    .end local v2
    :cond_5
    const/4 v1, 0x0

    .restart local v2    # "hScroll":F
    goto :goto_1

    .line 31846
    .end local v0
    :cond_6
    const/4 v0, 0x0

    goto :goto_0

    .line 31847
    .end local v0
    .end local v2    # "hScroll":F
    :cond_7
    invoke-virtual {p1}, Landroid/view/MotionEvent;->getSource()I

    move-result v4

    const/high16 v3, 0x400000

    sget-object v1, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const/4 v0, 0x5

    aget-object v1, v1, v0

    const/4 v0, 0x2

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    const/16 v0, 0x59

    if-eq v1, v0, :cond_8

    and-int/2addr v4, v3

    if-eqz v4, :cond_b

    .line 31848
    :goto_2
    const/16 v0, 0x1a

    invoke-virtual {p1, v0}, Landroid/view/MotionEvent;->getAxisValue(I)F

    move-result v1

    .line 31849
    .local v0, "axisScroll":F
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/4o;->A21()Z

    move-result v0

    if-eqz v0, :cond_9

    .line 31850
    neg-float v0, v1

    .line 31851
    .local v2, "vScroll":F
    const/4 v1, 0x0

    .local v3, "hScroll":F
    goto :goto_1

    :cond_8
    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const-string v1, "SrYzqcFycO4E61WX0CCI79FBiQDWLxq8"

    const/4 v0, 0x5

    aput-object v1, v2, v0

    and-int/2addr v4, v3

    if-eqz v4, :cond_b

    goto :goto_2

    .line 31852
    .end local v2    # "vScroll":F
    .end local v3    # "hScroll":F
    :cond_9
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/4o;->A20()Z

    move-result v0

    if-eqz v0, :cond_a

    .line 31853
    const/4 v0, 0x0

    .line 31854
    .restart local v2    # "vScroll":F
    .restart local v3    # "hScroll":F
    goto :goto_1

    .line 31855
    .end local v2    # "vScroll":F
    .end local v3    # "hScroll":F
    :cond_a
    const/4 v0, 0x0

    .line 31856
    .restart local v2    # "vScroll":F
    const/4 v1, 0x0

    goto :goto_1

    .line 31857
    .end local v0    # "axisScroll":F
    .end local v2    # "vScroll":F
    :cond_b
    const/4 v0, 0x0

    .line 31858
    .restart local v0    # "axisScroll":F
    const/4 v1, 0x0

    goto :goto_1
.end method

.method public final onInterceptTouchEvent(Landroid/view/MotionEvent;)Z
    .locals 10

    .line 31859
    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0I:Z

    const/4 v4, 0x0

    if-eqz v0, :cond_0

    .line 31860
    return v4

    .line 31861
    :cond_0
    invoke-direct {p0, p1}, Lcom/facebook/ads/redexgen/X/Eb;->A15(Landroid/view/MotionEvent;)Z

    move-result v0

    const/4 v3, 0x1

    if-eqz v0, :cond_1

    .line 31862
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/Eb;->A0J()V

    .line 31863
    return v3

    .line 31864
    :cond_1
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    if-nez v0, :cond_2

    .line 31865
    return v4

    .line 31866
    :cond_2
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/4o;->A20()Z

    move-result v9

    .line 31867
    .local v0, "canScrollHorizontally":Z
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/4o;->A21()Z

    move-result v8

    .line 31868
    .local v3, "canScrollVertically":Z
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0Y:Landroid/view/VelocityTracker;

    if-nez v0, :cond_3

    .line 31869
    invoke-static {}, Landroid/view/VelocityTracker;->obtain()Landroid/view/VelocityTracker;

    move-result-object v0

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0Y:Landroid/view/VelocityTracker;

    .line 31870
    :cond_3
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0Y:Landroid/view/VelocityTracker;

    invoke-virtual {v0, p1}, Landroid/view/VelocityTracker;->addMovement(Landroid/view/MotionEvent;)V

    .line 31871
    invoke-virtual {p1}, Landroid/view/MotionEvent;->getActionMasked()I

    move-result v7

    .line 31872
    .local v4, "action":I
    invoke-virtual {p1}, Landroid/view/MotionEvent;->getActionIndex()I

    move-result v5

    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const/4 v0, 0x2

    aget-object v1, v2, v0

    const/4 v0, 0x7

    aget-object v0, v2, v0

    invoke-virtual {v1}, Ljava/lang/String;->length()I

    move-result v1

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v0

    if-eq v1, v0, :cond_f

    .line 31873
    .local v5, "actionIndex":I
    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const-string v1, "Oz3Y31ex0QABqFtFM6aXix"

    const/4 v0, 0x3

    aput-object v1, v2, v0

    const-string v1, "OpGO9pAa6R"

    const/4 v0, 0x0

    aput-object v1, v2, v0

    const/high16 v6, 0x3f000000    # 0.5f

    packed-switch v7, :pswitch_data_0

    .line 31874
    .end local v6
    :cond_4
    :goto_0
    :pswitch_0
    iget v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0W:I

    if-ne v0, v3, :cond_5

    const/4 v4, 0x1

    :cond_5
    return v4

    .line 31875
    :pswitch_1
    invoke-direct {p0, p1}, Lcom/facebook/ads/redexgen/X/Eb;->A0n(Landroid/view/MotionEvent;)V

    .line 31876
    goto :goto_0

    .line 31877
    :pswitch_2
    invoke-virtual {p1, v5}, Landroid/view/MotionEvent;->getPointerId(I)I

    move-result v0

    iput v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0V:I

    .line 31878
    invoke-virtual {p1, v5}, Landroid/view/MotionEvent;->getX(I)F

    move-result v0

    add-float/2addr v0, v6

    float-to-int v0, v0

    iput v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0S:I

    iput v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0Q:I

    .line 31879
    invoke-virtual {p1, v5}, Landroid/view/MotionEvent;->getY(I)F

    move-result v0

    add-float/2addr v0, v6

    float-to-int v0, v0

    iput v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0T:I

    iput v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0R:I

    .line 31880
    goto :goto_0

    .line 31881
    :pswitch_3
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/Eb;->A0J()V

    goto :goto_0

    .line 31882
    :pswitch_4
    iget v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0V:I

    invoke-virtual {p1, v0}, Landroid/view/MotionEvent;->findPointerIndex(I)I

    move-result v1

    .line 31883
    .local v7, "index":I
    if-gez v1, :cond_6

    .line 31884
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const/16 v2, 0x427

    const/16 v1, 0x2e

    const/16 v0, 0x32

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Eb;->A0I(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    iget v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0V:I

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v3

    const/16 v2, 0x7c

    const/16 v1, 0x2d

    const/16 v0, 0x2e

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Eb;->A0I(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    const/16 v2, 0x55a

    const/16 v1, 0xc

    const/16 v0, 0x4f

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Eb;->A0I(III)Ljava/lang/String;

    move-result-object v0

    invoke-static {v0, v3}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    .line 31885
    return v4

    .line 31886
    :cond_6
    invoke-virtual {p1, v1}, Landroid/view/MotionEvent;->getX(I)F

    move-result v0

    add-float/2addr v0, v6

    float-to-int v5, v0

    .line 31887
    .local v8, "x":I
    invoke-virtual {p1, v1}, Landroid/view/MotionEvent;->getY(I)F

    move-result v7

    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const/4 v0, 0x2

    aget-object v1, v2, v0

    const/4 v0, 0x7

    aget-object v0, v2, v0

    invoke-virtual {v1}, Ljava/lang/String;->length()I

    move-result v1

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v0

    if-eq v1, v0, :cond_9

    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const-string v1, "bk8L2DuoR6rlGazYt8IaCt"

    const/4 v0, 0x3

    aput-object v1, v2, v0

    const-string v1, "Sc1G85HZiT"

    const/4 v0, 0x0

    aput-object v1, v2, v0

    add-float/2addr v7, v6

    float-to-int v1, v7

    .line 31888
    .local v6, "y":I
    iget v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0W:I

    if-eq v0, v3, :cond_4

    .line 31889
    :goto_1
    iget v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0Q:I

    sub-int v2, v5, v0

    .line 31890
    .local v9, "dx":I
    iget v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0R:I

    sub-int v7, v1, v0

    .line 31891
    .local p0, "dy":I
    const/4 v6, 0x0

    .line 31892
    .local p1, "startScroll":Z
    if-eqz v9, :cond_7

    invoke-static {v2}, Ljava/lang/Math;->abs(I)I

    move-result v2

    iget v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0X:I

    if-le v2, v0, :cond_7

    .line 31893
    iput v5, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0S:I

    .line 31894
    const/4 v6, 0x1

    .line 31895
    :cond_7
    if-eqz v8, :cond_8

    invoke-static {v7}, Ljava/lang/Math;->abs(I)I

    move-result v2

    iget v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0X:I

    if-le v2, v0, :cond_8

    .line 31896
    iput v1, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0T:I

    .line 31897
    const/4 v6, 0x1

    .line 31898
    :cond_8
    if-eqz v6, :cond_4

    .line 31899
    invoke-virtual {p0, v3}, Lcom/facebook/ads/redexgen/X/Eb;->setScrollState(I)V

    goto/16 :goto_0

    :cond_9
    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const-string v1, "zyYUBzZDbxt5GKooJMB8Oo5URJ8cGTlk"

    const/4 v0, 0x5

    aput-object v1, v2, v0

    add-float/2addr v7, v6

    float-to-int v1, v7

    .line 31900
    .local v6, "y":I
    iget v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0W:I

    if-eq v0, v3, :cond_4

    goto :goto_1

    .line 31901
    :pswitch_5
    iget-object v5, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0Y:Landroid/view/VelocityTracker;

    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const/4 v0, 0x6

    aget-object v1, v2, v0

    const/4 v0, 0x4

    aget-object v2, v2, v0

    const/16 v0, 0x11

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    invoke-virtual {v2, v0}, Ljava/lang/String;->charAt(I)C

    move-result v0

    if-eq v1, v0, :cond_a

    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const-string v1, "RX1lNzZl9PHDMcdVrAJc1B"

    const/4 v0, 0x3

    aput-object v1, v2, v0

    const-string v1, "i2qb1YAFuc"

    const/4 v0, 0x0

    aput-object v1, v2, v0

    invoke-virtual {v5}, Landroid/view/VelocityTracker;->clear()V

    .line 31902
    invoke-virtual {p0, v4}, Lcom/facebook/ads/redexgen/X/Eb;->A1X(I)V

    .line 31903
    goto/16 :goto_0

    :cond_a
    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const-string v1, "VFVZbyoyfkMi6O34miIS2l4U7i7WEKsz"

    const/4 v0, 0x1

    aput-object v1, v2, v0

    invoke-virtual {v5}, Landroid/view/VelocityTracker;->clear()V

    .line 31904
    invoke-virtual {p0, v4}, Lcom/facebook/ads/redexgen/X/Eb;->A1X(I)V

    .line 31905
    goto/16 :goto_0

    .line 31906
    :pswitch_6
    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0n:Z

    if-eqz v0, :cond_b

    .line 31907
    iput-boolean v4, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0n:Z

    .line 31908
    :cond_b
    invoke-virtual {p1, v4}, Landroid/view/MotionEvent;->getPointerId(I)I

    move-result v0

    iput v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0V:I

    .line 31909
    invoke-virtual {p1}, Landroid/view/MotionEvent;->getX()F

    move-result v0

    add-float/2addr v0, v6

    float-to-int v0, v0

    iput v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0S:I

    iput v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0Q:I

    .line 31910
    invoke-virtual {p1}, Landroid/view/MotionEvent;->getY()F

    move-result v0

    add-float/2addr v0, v6

    float-to-int v0, v0

    iput v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0T:I

    iput v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0R:I

    .line 31911
    iget v1, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0W:I

    const/4 v0, 0x2

    if-ne v1, v0, :cond_c

    .line 31912
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->getParent()Landroid/view/ViewParent;

    move-result-object v0

    invoke-interface {v0, v3}, Landroid/view/ViewParent;->requestDisallowInterceptTouchEvent(Z)V

    .line 31913
    invoke-virtual {p0, v3}, Lcom/facebook/ads/redexgen/X/Eb;->setScrollState(I)V

    .line 31914
    :cond_c
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A15:[I

    aput v4, v0, v3

    aput v4, v0, v4

    .line 31915
    const/4 v0, 0x0

    .line 31916
    .local v6, "nestedScrollAxis":I
    if-eqz v9, :cond_d

    .line 31917
    or-int/lit8 v0, v0, 0x1

    .line 31918
    :cond_d
    if-eqz v8, :cond_e

    .line 31919
    or-int/lit8 v0, v0, 0x2

    .line 31920
    :cond_e
    invoke-virtual {p0, v0, v4}, Lcom/facebook/ads/redexgen/X/Eb;->A1s(II)Z

    .line 31921
    goto/16 :goto_0

    .line 31922
    :cond_f
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    :pswitch_data_0
    .packed-switch 0x0
        :pswitch_6
        :pswitch_5
        :pswitch_4
        :pswitch_3
        :pswitch_0
        :pswitch_2
        :pswitch_1
    .end packed-switch
.end method

.method public final onLayout(ZIIII)V
    .locals 3

    .line 31923
    const/16 v2, 0x532

    const/16 v1, 0xb

    const/16 v0, 0x56

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Eb;->A0I(III)Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/2q;->A01(Ljava/lang/String;)V

    .line 31924
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/Eb;->A0Y()V

    .line 31925
    invoke-static {}, Lcom/facebook/ads/redexgen/X/2q;->A00()V

    .line 31926
    const/4 v0, 0x1

    iput-boolean v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0D:Z

    .line 31927
    return-void
.end method

.method public onMeasure(II)V
    .locals 6

    .line 31928
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    if-nez v0, :cond_0

    .line 31929
    invoke-virtual {p0, p1, p2}, Lcom/facebook/ads/redexgen/X/Eb;->A1a(II)V

    .line 31930
    return-void

    .line 31931
    :cond_0
    iget-boolean v0, v0, Lcom/facebook/ads/redexgen/X/4o;->A06:Z

    const/4 v4, 0x1

    const/4 v3, 0x0

    if-eqz v0, :cond_5

    .line 31932
    invoke-static {p1}, Landroid/view/View$MeasureSpec;->getMode(I)I

    move-result v1

    .line 31933
    .local v0, "widthMode":I
    invoke-static {p2}, Landroid/view/View$MeasureSpec;->getMode(I)I

    move-result v0

    .line 31934
    .local v3, "heightMode":I
    const/high16 v5, 0x40000000    # 2.0f

    if-ne v1, v5, :cond_1

    if-ne v0, v5, :cond_1

    const/4 v3, 0x1

    .line 31935
    .local v2, "skipMeasure":Z
    :cond_1
    iget-object v2, p0, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0r:Lcom/facebook/ads/redexgen/X/4w;

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0s:Lcom/facebook/ads/redexgen/X/53;

    invoke-virtual {v2, v1, v0, p1, p2}, Lcom/facebook/ads/redexgen/X/4o;->A1J(Lcom/facebook/ads/redexgen/X/4w;Lcom/facebook/ads/redexgen/X/53;II)V

    .line 31936
    if-nez v3, :cond_2

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A04:Lcom/facebook/ads/redexgen/X/4c;

    if-nez v0, :cond_3

    .line 31937
    .restart local v0    # "widthMode":I
    .restart local v2    # "skipMeasure":Z
    .restart local v3    # "heightMode":I
    :cond_2
    return-void

    .line 31938
    :cond_3
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0s:Lcom/facebook/ads/redexgen/X/53;

    iget v0, v0, Lcom/facebook/ads/redexgen/X/53;->A04:I

    if-ne v0, v4, :cond_4

    .line 31939
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/Eb;->A0L()V

    .line 31940
    :cond_4
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    invoke-virtual {v0, p1, p2}, Lcom/facebook/ads/redexgen/X/4o;->A11(II)V

    .line 31941
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0s:Lcom/facebook/ads/redexgen/X/53;

    iput-boolean v4, v0, Lcom/facebook/ads/redexgen/X/53;->A0A:Z

    .line 31942
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/Eb;->A0M()V

    .line 31943
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    invoke-virtual {v0, p1, p2}, Lcom/facebook/ads/redexgen/X/4o;->A12(II)V

    .line 31944
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/4o;->A1z()Z

    move-result v0

    if-eqz v0, :cond_8

    .line 31945
    iget-object v2, p0, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    .line 31946
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->getMeasuredWidth()I

    move-result v0

    invoke-static {v0, v5}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    move-result v1

    .line 31947
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->getMeasuredHeight()I

    move-result v0

    invoke-static {v0, v5}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    move-result v0

    .line 31948
    invoke-virtual {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/4o;->A11(II)V

    .line 31949
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0s:Lcom/facebook/ads/redexgen/X/53;

    iput-boolean v4, v0, Lcom/facebook/ads/redexgen/X/53;->A0A:Z

    .line 31950
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/Eb;->A0M()V

    .line 31951
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    invoke-virtual {v0, p1, p2}, Lcom/facebook/ads/redexgen/X/4o;->A12(II)V

    goto :goto_2

    .line 31952
    .end local v0    # "widthMode":I
    .end local v2    # "skipMeasure":Z
    .end local v3    # "heightMode":I
    :cond_5
    iget-boolean v5, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0E:Z

    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const/4 v0, 0x3

    aget-object v1, v2, v0

    const/4 v0, 0x0

    aget-object v0, v2, v0

    invoke-virtual {v1}, Ljava/lang/String;->length()I

    move-result v1

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v0

    if-eq v1, v0, :cond_d

    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const-string v1, "KQlS07WJlXQIeKT2EaNKgCMREedmurGg"

    const/4 v0, 0x6

    aput-object v1, v2, v0

    const-string v1, "TSw2akNEB1AJctHCwaDYxiu1Uwr65JPJ"

    const/4 v0, 0x4

    aput-object v1, v2, v0

    if-eqz v5, :cond_6

    .line 31953
    iget-object v2, p0, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0r:Lcom/facebook/ads/redexgen/X/4w;

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0s:Lcom/facebook/ads/redexgen/X/53;

    invoke-virtual {v2, v1, v0, p1, p2}, Lcom/facebook/ads/redexgen/X/4o;->A1J(Lcom/facebook/ads/redexgen/X/4w;Lcom/facebook/ads/redexgen/X/53;II)V

    .line 31954
    return-void

    .line 31955
    :cond_6
    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0A:Z

    if-eqz v0, :cond_b

    .line 31956
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->A1J()V

    .line 31957
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->A1K()V

    .line 31958
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/Eb;->A0P()V

    .line 31959
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->A1L()V

    .line 31960
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0s:Lcom/facebook/ads/redexgen/X/53;

    iget-boolean v0, v0, Lcom/facebook/ads/redexgen/X/53;->A0B:Z

    if-eqz v0, :cond_a

    .line 31961
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0s:Lcom/facebook/ads/redexgen/X/53;

    iput-boolean v4, v0, Lcom/facebook/ads/redexgen/X/53;->A09:Z

    .line 31962
    :goto_0
    iput-boolean v3, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0A:Z

    .line 31963
    invoke-virtual {p0, v3}, Lcom/facebook/ads/redexgen/X/Eb;->A1n(Z)V

    .line 31964
    :cond_7
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A04:Lcom/facebook/ads/redexgen/X/4c;

    if-eqz v0, :cond_9

    .line 31965
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0s:Lcom/facebook/ads/redexgen/X/53;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/4c;->A0E()I

    move-result v0

    iput v0, v1, Lcom/facebook/ads/redexgen/X/53;->A03:I

    .line 31966
    :goto_1
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->A1J()V

    .line 31967
    iget-object v2, p0, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0r:Lcom/facebook/ads/redexgen/X/4w;

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0s:Lcom/facebook/ads/redexgen/X/53;

    invoke-virtual {v2, v1, v0, p1, p2}, Lcom/facebook/ads/redexgen/X/4o;->A1J(Lcom/facebook/ads/redexgen/X/4w;Lcom/facebook/ads/redexgen/X/53;II)V

    .line 31968
    invoke-virtual {p0, v3}, Lcom/facebook/ads/redexgen/X/Eb;->A1n(Z)V

    .line 31969
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0s:Lcom/facebook/ads/redexgen/X/53;

    iput-boolean v3, v0, Lcom/facebook/ads/redexgen/X/53;->A09:Z

    .line 31970
    :cond_8
    :goto_2
    return-void

    .line 31971
    :cond_9
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0s:Lcom/facebook/ads/redexgen/X/53;

    iput v3, v0, Lcom/facebook/ads/redexgen/X/53;->A03:I

    goto :goto_1

    .line 31972
    :cond_a
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A00:Lcom/facebook/ads/redexgen/X/Zq;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/Zq;->A07()V

    .line 31973
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0s:Lcom/facebook/ads/redexgen/X/53;

    iput-boolean v3, v0, Lcom/facebook/ads/redexgen/X/53;->A09:Z

    goto :goto_0

    .line 31974
    :cond_b
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0s:Lcom/facebook/ads/redexgen/X/53;

    iget-boolean v4, v0, Lcom/facebook/ads/redexgen/X/53;->A0B:Z

    sget-object v1, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const/4 v0, 0x1

    aget-object v0, v1, v0

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v1

    const/16 v0, 0x20

    if-eq v1, v0, :cond_c

    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const-string v1, "SNs0MIA5NFIkFiTpWaOkIZpcAnFttQ5E"

    const/4 v0, 0x6

    aput-object v1, v2, v0

    const-string v1, "fXihHPq3ps5k4WwfOaG7U1bhyi9cbGtD"

    const/4 v0, 0x4

    aput-object v1, v2, v0

    if-eqz v4, :cond_7

    .line 31975
    :goto_3
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->getMeasuredWidth()I

    move-result v1

    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->getMeasuredHeight()I

    move-result v0

    invoke-virtual {p0, v1, v0}, Lcom/facebook/ads/redexgen/X/Eb;->setMeasuredDimension(II)V

    .line 31976
    return-void

    :cond_c
    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const-string v1, "mxGjlxdGGYffeE2i6ToSoo"

    const/4 v0, 0x3

    aput-object v1, v2, v0

    const-string v1, "9Nm63BEsB7"

    const/4 v0, 0x0

    aput-object v1, v2, v0

    if-eqz v4, :cond_7

    goto :goto_3

    .line 31977
    :cond_d
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0
.end method

.method public final onRequestFocusInDescendants(ILandroid/graphics/Rect;)Z
    .locals 1

    .line 31978
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->A1q()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 31979
    const/4 v0, 0x0

    return v0

    .line 31980
    :cond_0
    invoke-super {p0, p1, p2}, Landroid/view/ViewGroup;->onRequestFocusInDescendants(ILandroid/graphics/Rect;)Z

    move-result v0

    return v0
.end method

.method public final onRestoreInstanceState(Landroid/os/Parcelable;)V
    .locals 2

    .line 31981
    instance-of v0, p1, Lcom/facebook/ads/internal/util/parcelable/WrappedParcelable;

    if-nez v0, :cond_0

    .line 31982
    invoke-super {p0, p1}, Landroid/view/ViewGroup;->onRestoreInstanceState(Landroid/os/Parcelable;)V

    .line 31983
    return-void

    .line 31984
    :cond_0
    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/Class;->getClassLoader()Ljava/lang/ClassLoader;

    move-result-object v0

    .line 31985
    .local v0, "classLoader":Ljava/lang/ClassLoader;
    if-nez v0, :cond_1

    .line 31986
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {v0}, Landroid/content/Context;->getClassLoader()Ljava/lang/ClassLoader;

    move-result-object v0

    .line 31987
    :cond_1
    check-cast p1, Lcom/facebook/ads/internal/util/parcelable/WrappedParcelable;

    invoke-virtual {p1, v0}, Lcom/facebook/ads/internal/util/parcelable/WrappedParcelable;->unwrap(Ljava/lang/ClassLoader;)Landroid/os/Parcelable;

    move-result-object v1

    .line 31988
    .local v1, "state":Landroid/os/Parcelable;
    instance-of v0, v1, Lcom/facebook/ads/internal/androidx/support/v7/widget/RecyclerView$SavedState;

    if-nez v0, :cond_2

    .line 31989
    invoke-super {p0, v1}, Landroid/view/ViewGroup;->onRestoreInstanceState(Landroid/os/Parcelable;)V

    .line 31990
    return-void

    .line 31991
    :cond_2
    check-cast v1, Lcom/facebook/ads/internal/androidx/support/v7/widget/RecyclerView$SavedState;

    iput-object v1, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0j:Lcom/facebook/ads/internal/androidx/support/v7/widget/RecyclerView$SavedState;

    .line 31992
    invoke-virtual {v1}, Lcom/facebook/ads/internal/androidx/support/v4/view/AbsSavedState;->A02()Landroid/os/Parcelable;

    move-result-object v0

    invoke-super {p0, v0}, Landroid/view/ViewGroup;->onRestoreInstanceState(Landroid/os/Parcelable;)V

    .line 31993
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    if-eqz v0, :cond_3

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0j:Lcom/facebook/ads/internal/androidx/support/v7/widget/RecyclerView$SavedState;

    iget-object v0, v0, Lcom/facebook/ads/internal/androidx/support/v7/widget/RecyclerView$SavedState;->A00:Landroid/os/Parcelable;

    if-eqz v0, :cond_3

    .line 31994
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0j:Lcom/facebook/ads/internal/androidx/support/v7/widget/RecyclerView$SavedState;

    iget-object v0, v0, Lcom/facebook/ads/internal/androidx/support/v7/widget/RecyclerView$SavedState;->A00:Landroid/os/Parcelable;

    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/4o;->A1s(Landroid/os/Parcelable;)V

    .line 31995
    :cond_3
    return-void
.end method

.method public final onSaveInstanceState()Landroid/os/Parcelable;
    .locals 2

    .line 31996
    nop

    invoke-super {p0}, Landroid/view/ViewGroup;->onSaveInstanceState()Landroid/os/Parcelable;

    move-result-object v0

    new-instance v1, Lcom/facebook/ads/internal/androidx/support/v7/widget/RecyclerView$SavedState;

    invoke-direct {v1, v0}, Lcom/facebook/ads/internal/androidx/support/v7/widget/RecyclerView$SavedState;-><init>(Landroid/os/Parcelable;)V

    .line 31997
    .local v0, "state":Lcom/facebook/ads/internal/androidx/support/v7/widget/RecyclerView$SavedState;
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0j:Lcom/facebook/ads/internal/androidx/support/v7/widget/RecyclerView$SavedState;

    if-eqz v0, :cond_0

    .line 31998
    invoke-virtual {v1, v0}, Lcom/facebook/ads/internal/androidx/support/v7/widget/RecyclerView$SavedState;->A03(Lcom/facebook/ads/internal/androidx/support/v7/widget/RecyclerView$SavedState;)V

    .line 31999
    :goto_0
    new-instance v0, Lcom/facebook/ads/internal/util/parcelable/WrappedParcelable;

    invoke-direct {v0, v1}, Lcom/facebook/ads/internal/util/parcelable/WrappedParcelable;-><init>(Landroid/os/Parcelable;)V

    return-object v0

    .line 32000
    :cond_0
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    if-eqz v0, :cond_1

    .line 32001
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/4o;->A1l()Landroid/os/Parcelable;

    move-result-object v0

    iput-object v0, v1, Lcom/facebook/ads/internal/androidx/support/v7/widget/RecyclerView$SavedState;->A00:Landroid/os/Parcelable;

    goto :goto_0

    .line 32002
    :cond_1
    const/4 v0, 0x0

    iput-object v0, v1, Lcom/facebook/ads/internal/androidx/support/v7/widget/RecyclerView$SavedState;->A00:Landroid/os/Parcelable;

    goto :goto_0
.end method

.method public final onSizeChanged(IIII)V
    .locals 0

    .line 32003
    invoke-super {p0, p1, p2, p3, p4}, Landroid/view/ViewGroup;->onSizeChanged(IIII)V

    .line 32004
    if-ne p1, p3, :cond_0

    if-eq p2, p4, :cond_1

    .line 32005
    :cond_0
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/Eb;->A0f()V

    .line 32006
    :cond_1
    return-void
.end method

.method public final onTouchEvent(Landroid/view/MotionEvent;)Z
    .locals 20

    .line 32007
    move-object/from16 v5, p0

    move-object v5, v5

    iget-boolean v0, v5, Lcom/facebook/ads/redexgen/X/Eb;->A0I:Z

    const/4 v6, 0x0

    if-nez v0, :cond_0

    iget-boolean v0, v5, Lcom/facebook/ads/redexgen/X/Eb;->A0n:Z

    if-eqz v0, :cond_1

    .line 32008
    .end local v10
    .end local v11
    .end local v12
    .end local v13
    .end local v14
    .end local v15
    :cond_0
    return v6

    .line 32009
    :cond_1
    move-object/from16 v7, p1

    invoke-direct {v5, v7}, Lcom/facebook/ads/redexgen/X/Eb;->A14(Landroid/view/MotionEvent;)Z

    move-result v3

    const/4 v4, 0x1

    sget-object v1, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const/4 v0, 0x5

    aget-object v1, v1, v0

    const/4 v0, 0x2

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    const/16 v0, 0x59

    if-eq v1, v0, :cond_2

    :goto_0
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    :cond_2
    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const-string v1, "8sYT0RnpvG2arPES2UJ12JYFoUL9IQDo"

    const/4 v0, 0x5

    aput-object v1, v2, v0

    if-eqz v3, :cond_3

    .line 32010
    invoke-direct {v5}, Lcom/facebook/ads/redexgen/X/Eb;->A0J()V

    .line 32011
    return v4

    .line 32012
    :cond_3
    iget-object v0, v5, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    if-nez v0, :cond_4

    .line 32013
    return v6

    .line 32014
    :cond_4
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/4o;->A20()Z

    move-result v13

    .line 32015
    .local v10, "canScrollHorizontally":Z
    iget-object v0, v5, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/4o;->A21()Z

    move-result v12

    .line 32016
    .local v11, "canScrollVertically":Z
    iget-object v0, v5, Lcom/facebook/ads/redexgen/X/Eb;->A0Y:Landroid/view/VelocityTracker;

    if-nez v0, :cond_5

    .line 32017
    invoke-static {}, Landroid/view/VelocityTracker;->obtain()Landroid/view/VelocityTracker;

    move-result-object v0

    iput-object v0, v5, Lcom/facebook/ads/redexgen/X/Eb;->A0Y:Landroid/view/VelocityTracker;

    .line 32018
    :cond_5
    const/4 v11, 0x0

    .line 32019
    .local v12, "eventAddedToVelocityTracker":Z
    invoke-static {v7}, Landroid/view/MotionEvent;->obtain(Landroid/view/MotionEvent;)Landroid/view/MotionEvent;

    move-result-object v3

    .line 32020
    .local v13, "vtev":Landroid/view/MotionEvent;
    invoke-virtual {v7}, Landroid/view/MotionEvent;->getActionMasked()I

    move-result v10

    .line 32021
    .local v14, "action":I
    invoke-virtual {v7}, Landroid/view/MotionEvent;->getActionIndex()I

    move-result v8

    .line 32022
    .local v15, "actionIndex":I
    if-nez v10, :cond_6

    .line 32023
    iget-object v9, v5, Lcom/facebook/ads/redexgen/X/Eb;->A15:[I

    aput v6, v9, v4

    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const/4 v0, 0x2

    aget-object v1, v2, v0

    const/4 v0, 0x7

    aget-object v0, v2, v0

    invoke-virtual {v1}, Ljava/lang/String;->length()I

    move-result v1

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v0

    if-eq v1, v0, :cond_1d

    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const-string v1, "w9Y7A1oKaVmj8Ov6eZqRiF6tVMdmRve8"

    const/4 v0, 0x5

    aput-object v1, v2, v0

    aput v6, v9, v6

    .line 32024
    :cond_6
    :goto_1
    iget-object v2, v5, Lcom/facebook/ads/redexgen/X/Eb;->A15:[I

    aget v0, v2, v6

    int-to-float v1, v0

    aget v0, v2, v4

    int-to-float v0, v0

    invoke-virtual {v3, v1, v0}, Landroid/view/MotionEvent;->offsetLocation(FF)V

    .line 32025
    const/high16 v2, 0x3f000000    # 0.5f

    packed-switch v10, :pswitch_data_0

    .line 32026
    :cond_7
    :goto_2
    :pswitch_0
    if-nez v11, :cond_8

    .line 32027
    iget-object v0, v5, Lcom/facebook/ads/redexgen/X/Eb;->A0Y:Landroid/view/VelocityTracker;

    invoke-virtual {v0, v3}, Landroid/view/VelocityTracker;->addMovement(Landroid/view/MotionEvent;)V

    .line 32028
    :cond_8
    invoke-virtual {v3}, Landroid/view/MotionEvent;->recycle()V

    .line 32029
    return v4

    .line 32030
    :pswitch_1
    invoke-direct {v5, v7}, Lcom/facebook/ads/redexgen/X/Eb;->A0n(Landroid/view/MotionEvent;)V

    .line 32031
    goto :goto_2

    .line 32032
    :pswitch_2
    invoke-virtual {v7, v8}, Landroid/view/MotionEvent;->getPointerId(I)I

    move-result v0

    iput v0, v5, Lcom/facebook/ads/redexgen/X/Eb;->A0V:I

    .line 32033
    invoke-virtual {v7, v8}, Landroid/view/MotionEvent;->getX(I)F

    move-result v0

    add-float/2addr v0, v2

    float-to-int v0, v0

    iput v0, v5, Lcom/facebook/ads/redexgen/X/Eb;->A0S:I

    iput v0, v5, Lcom/facebook/ads/redexgen/X/Eb;->A0Q:I

    .line 32034
    invoke-virtual {v7, v8}, Landroid/view/MotionEvent;->getY(I)F

    move-result v0

    add-float/2addr v0, v2

    float-to-int v0, v0

    iput v0, v5, Lcom/facebook/ads/redexgen/X/Eb;->A0T:I

    iput v0, v5, Lcom/facebook/ads/redexgen/X/Eb;->A0R:I

    .line 32035
    goto :goto_2

    .line 32036
    :pswitch_3
    invoke-direct {v5}, Lcom/facebook/ads/redexgen/X/Eb;->A0J()V

    goto :goto_2

    .line 32037
    :pswitch_4
    iget v0, v5, Lcom/facebook/ads/redexgen/X/Eb;->A0V:I

    invoke-virtual {v7, v0}, Landroid/view/MotionEvent;->findPointerIndex(I)I

    move-result v1

    .line 32038
    .local v5, "index":I
    if-gez v1, :cond_9

    .line 32039
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const/16 v2, 0x427

    const/16 v1, 0x2e

    const/16 v0, 0x32

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Eb;->A0I(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    iget v0, v5, Lcom/facebook/ads/redexgen/X/Eb;->A0V:I

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v3

    const/16 v2, 0x7c

    const/16 v1, 0x2d

    const/16 v0, 0x2e

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Eb;->A0I(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    const/16 v2, 0x55a

    const/16 v1, 0xc

    const/16 v0, 0x4f

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Eb;->A0I(III)Ljava/lang/String;

    move-result-object v0

    invoke-static {v0, v3}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    .line 32040
    return v6

    .line 32041
    :cond_9
    invoke-virtual {v7, v1}, Landroid/view/MotionEvent;->getX(I)F

    move-result v0

    add-float/2addr v0, v2

    float-to-int v9, v0

    .line 32042
    .local v4, "x":I
    invoke-virtual {v7, v1}, Landroid/view/MotionEvent;->getY(I)F

    move-result v0

    add-float/2addr v0, v2

    float-to-int v8, v0

    .line 32043
    .local v3, "y":I
    iget v15, v5, Lcom/facebook/ads/redexgen/X/Eb;->A0S:I

    sub-int/2addr v15, v9

    .line 32044
    .local v16, "dx":I
    iget v7, v5, Lcom/facebook/ads/redexgen/X/Eb;->A0T:I

    sub-int/2addr v7, v8

    .line 32045
    .local v17, "dy":I
    iget-object v1, v5, Lcom/facebook/ads/redexgen/X/Eb;->A16:[I

    iget-object v0, v5, Lcom/facebook/ads/redexgen/X/Eb;->A17:[I

    const/16 v19, 0x0

    move-object v14, v5

    .end local v3    # "y":I
    .local p1, "y":I
    .end local v4    # "x":I
    .local p0, "x":I
    .end local v5    # "index":I
    .local v19, "index":I
    move/from16 v16, v7

    move-object/from16 v17, v1

    move-object/from16 v18, v0

    invoke-virtual/range {v14 .. v19}, Lcom/facebook/ads/redexgen/X/Eb;->A1u(II[I[II)Z

    move-result v0

    if-eqz v0, :cond_a

    .line 32046
    iget-object v1, v5, Lcom/facebook/ads/redexgen/X/Eb;->A16:[I

    aget v0, v1, v6

    sub-int/2addr v15, v0

    .line 32047
    aget v0, v1, v4

    sub-int/2addr v7, v0

    .line 32048
    iget-object v2, v5, Lcom/facebook/ads/redexgen/X/Eb;->A17:[I

    aget v0, v2, v6

    int-to-float v1, v0

    aget v0, v2, v4

    int-to-float v0, v0

    invoke-virtual {v3, v1, v0}, Landroid/view/MotionEvent;->offsetLocation(FF)V

    .line 32049
    iget-object v10, v5, Lcom/facebook/ads/redexgen/X/Eb;->A15:[I

    aget v1, v10, v6

    iget-object v2, v5, Lcom/facebook/ads/redexgen/X/Eb;->A17:[I

    aget v0, v2, v6

    add-int/2addr v1, v0

    aput v1, v10, v6

    .line 32050
    aget v1, v10, v4

    aget v0, v2, v4

    add-int/2addr v1, v0

    aput v1, v10, v4

    .line 32051
    :cond_a
    iget v10, v5, Lcom/facebook/ads/redexgen/X/Eb;->A0W:I

    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const/4 v0, 0x3

    aget-object v1, v2, v0

    const/4 v0, 0x0

    aget-object v0, v2, v0

    invoke-virtual {v1}, Ljava/lang/String;->length()I

    move-result v1

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v0

    if-eq v1, v0, :cond_1e

    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const-string v1, "vPk2qMMC5wpKDUMEcolEIRuedMAFiD5T"

    const/4 v0, 0x1

    aput-object v1, v2, v0

    if-eq v10, v4, :cond_d

    .line 32052
    const/4 v2, 0x0

    .line 32053
    .local v0, "startScroll":Z
    if-eqz v13, :cond_b

    invoke-static {v15}, Ljava/lang/Math;->abs(I)I

    move-result v1

    iget v0, v5, Lcom/facebook/ads/redexgen/X/Eb;->A0X:I

    if-le v1, v0, :cond_b

    .line 32054
    if-lez v15, :cond_14

    .line 32055
    sub-int/2addr v15, v0

    .line 32056
    :goto_3
    const/4 v2, 0x1

    .line 32057
    :cond_b
    if-eqz v12, :cond_c

    invoke-static {v7}, Ljava/lang/Math;->abs(I)I

    move-result v1

    iget v0, v5, Lcom/facebook/ads/redexgen/X/Eb;->A0X:I

    if-le v1, v0, :cond_c

    .line 32058
    if-lez v7, :cond_13

    .line 32059
    sub-int/2addr v7, v0

    .line 32060
    :goto_4
    const/4 v2, 0x1

    .line 32061
    :cond_c
    if-eqz v2, :cond_d

    .line 32062
    invoke-virtual {v5, v4}, Lcom/facebook/ads/redexgen/X/Eb;->setScrollState(I)V

    .line 32063
    .end local v16    # "dx":I
    .end local v17    # "dy":I
    .local v0, "dx":I
    .local v1, "dy":I
    :cond_d
    iget v0, v5, Lcom/facebook/ads/redexgen/X/Eb;->A0W:I

    if-ne v0, v4, :cond_7

    .line 32064
    iget-object v1, v5, Lcom/facebook/ads/redexgen/X/Eb;->A17:[I

    aget v0, v1, v6

    sub-int/2addr v9, v0

    iput v9, v5, Lcom/facebook/ads/redexgen/X/Eb;->A0S:I

    .line 32065
    aget v0, v1, v4

    sub-int/2addr v8, v0

    iput v8, v5, Lcom/facebook/ads/redexgen/X/Eb;->A0T:I

    .line 32066
    if-eqz v13, :cond_12

    move v0, v15

    .line 32067
    :goto_5
    if-eqz v12, :cond_e

    move v6, v7

    .line 32068
    :cond_e
    invoke-direct {v5, v0, v6, v3}, Lcom/facebook/ads/redexgen/X/Eb;->A13(IILandroid/view/MotionEvent;)Z

    move-result v0

    if-eqz v0, :cond_f

    .line 32069
    invoke-virtual {v5}, Lcom/facebook/ads/redexgen/X/Eb;->getParent()Landroid/view/ViewParent;

    move-result-object v6

    sget-object v1, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const/4 v0, 0x5

    aget-object v1, v1, v0

    const/4 v0, 0x2

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    const/16 v0, 0x59

    if-eq v1, v0, :cond_11

    invoke-interface {v6, v4}, Landroid/view/ViewParent;->requestDisallowInterceptTouchEvent(Z)V

    .line 32070
    :cond_f
    :goto_6
    iget-object v0, v5, Lcom/facebook/ads/redexgen/X/Eb;->A03:Lcom/facebook/ads/redexgen/X/4P;

    if-eqz v0, :cond_7

    if-nez v15, :cond_10

    if-eqz v7, :cond_7

    .line 32071
    :cond_10
    invoke-virtual {v0, v5, v15, v7}, Lcom/facebook/ads/redexgen/X/4P;->A0B(Lcom/facebook/ads/redexgen/X/Eb;II)V

    goto/16 :goto_2

    :cond_11
    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const-string v1, "9Qb2BcK3cjm46xX51a5wS3"

    const/4 v0, 0x3

    aput-object v1, v2, v0

    const-string v1, "GdK3mOIcF6"

    const/4 v0, 0x0

    aput-object v1, v2, v0

    invoke-interface {v6, v4}, Landroid/view/ViewParent;->requestDisallowInterceptTouchEvent(Z)V

    goto :goto_6

    .line 32072
    :cond_12
    const/4 v0, 0x0

    goto :goto_5

    .line 32073
    :cond_13
    add-int/2addr v7, v0

    goto :goto_4

    .line 32074
    :cond_14
    add-int/2addr v15, v0

    goto :goto_3

    .line 32075
    :pswitch_5
    iget-object v0, v5, Lcom/facebook/ads/redexgen/X/Eb;->A0Y:Landroid/view/VelocityTracker;

    invoke-virtual {v0, v3}, Landroid/view/VelocityTracker;->addMovement(Landroid/view/MotionEvent;)V

    .line 32076
    const/4 v11, 0x1

    .line 32077
    iget-object v2, v5, Lcom/facebook/ads/redexgen/X/Eb;->A0Y:Landroid/view/VelocityTracker;

    iget v0, v5, Lcom/facebook/ads/redexgen/X/Eb;->A0x:I

    int-to-float v1, v0

    const/16 v0, 0x3e8

    invoke-virtual {v2, v0, v1}, Landroid/view/VelocityTracker;->computeCurrentVelocity(IF)V

    .line 32078
    const/4 v10, 0x0

    if-eqz v13, :cond_16

    .line 32079
    iget-object v1, v5, Lcom/facebook/ads/redexgen/X/Eb;->A0Y:Landroid/view/VelocityTracker;

    iget v0, v5, Lcom/facebook/ads/redexgen/X/Eb;->A0V:I

    invoke-virtual {v1, v0}, Landroid/view/VelocityTracker;->getXVelocity(I)F

    move-result v0

    neg-float v8, v0

    .line 32080
    .local v1, "xvel":F
    :goto_7
    if-eqz v12, :cond_15

    .line 32081
    iget-object v1, v5, Lcom/facebook/ads/redexgen/X/Eb;->A0Y:Landroid/view/VelocityTracker;

    iget v0, v5, Lcom/facebook/ads/redexgen/X/Eb;->A0V:I

    invoke-virtual {v1, v0}, Landroid/view/VelocityTracker;->getYVelocity(I)F

    move-result v0

    neg-float v7, v0

    .line 32082
    .local v2, "yvel":F
    :goto_8
    cmpl-float v9, v8, v10

    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const/4 v0, 0x6

    aget-object v1, v2, v0

    const/4 v0, 0x4

    aget-object v2, v2, v0

    const/16 v0, 0x11

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    invoke-virtual {v2, v0}, Ljava/lang/String;->charAt(I)C

    move-result v0

    if-eq v1, v0, :cond_17

    goto/16 :goto_0

    .line 32083
    :cond_15
    const/4 v7, 0x0

    goto :goto_8

    .line 32084
    :cond_16
    const/4 v8, 0x0

    goto :goto_7

    :cond_17
    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const-string v1, "SfYw0o2VnAou7QhBgLkBGelaMPLfL2ni"

    const/4 v0, 0x5

    aput-object v1, v2, v0

    if-nez v9, :cond_18

    .line 32085
    cmpl-float v0, v7, v10

    if-eqz v0, :cond_19

    :cond_18
    float-to-int v1, v8

    float-to-int v0, v7

    invoke-direct {v5, v1, v0}, Lcom/facebook/ads/redexgen/X/Eb;->A12(II)Z

    move-result v0

    if-nez v0, :cond_1a

    .line 32086
    :cond_19
    invoke-virtual {v5, v6}, Lcom/facebook/ads/redexgen/X/Eb;->setScrollState(I)V

    .line 32087
    :cond_1a
    invoke-direct {v5}, Lcom/facebook/ads/redexgen/X/Eb;->A0T()V

    .line 32088
    .end local v1    # "xvel":F
    .end local v2    # "yvel":F
    goto/16 :goto_2

    .line 32089
    :pswitch_6
    invoke-virtual {v7, v6}, Landroid/view/MotionEvent;->getPointerId(I)I

    move-result v0

    iput v0, v5, Lcom/facebook/ads/redexgen/X/Eb;->A0V:I

    .line 32090
    invoke-virtual {v7}, Landroid/view/MotionEvent;->getX()F

    move-result v0

    add-float/2addr v0, v2

    float-to-int v0, v0

    iput v0, v5, Lcom/facebook/ads/redexgen/X/Eb;->A0S:I

    iput v0, v5, Lcom/facebook/ads/redexgen/X/Eb;->A0Q:I

    .line 32091
    invoke-virtual {v7}, Landroid/view/MotionEvent;->getY()F

    move-result v0

    add-float/2addr v0, v2

    float-to-int v0, v0

    iput v0, v5, Lcom/facebook/ads/redexgen/X/Eb;->A0T:I

    iput v0, v5, Lcom/facebook/ads/redexgen/X/Eb;->A0R:I

    .line 32092
    const/4 v0, 0x0

    .line 32093
    .local v0, "nestedScrollAxis":I
    if-eqz v13, :cond_1b

    .line 32094
    or-int/lit8 v0, v0, 0x1

    .line 32095
    :cond_1b
    if-eqz v12, :cond_1c

    .line 32096
    or-int/lit8 v0, v0, 0x2

    .line 32097
    :cond_1c
    invoke-virtual {v5, v0, v6}, Lcom/facebook/ads/redexgen/X/Eb;->A1s(II)Z

    .line 32098
    .end local v0    # "nestedScrollAxis":I
    goto/16 :goto_2

    .line 32099
    :cond_1d
    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const-string v1, "DE62WFMw5Qbi4eBk1auhfUxeLGhnsE7e"

    const/4 v0, 0x6

    aput-object v1, v2, v0

    const-string v1, "A6rUAAhMCRWi1Exz9agrYLVLW1oNRabf"

    const/4 v0, 0x4

    aput-object v1, v2, v0

    aput v6, v9, v6

    goto/16 :goto_1

    .line 32100
    :cond_1e
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    :pswitch_data_0
    .packed-switch 0x0
        :pswitch_6
        :pswitch_5
        :pswitch_4
        :pswitch_3
        :pswitch_0
        :pswitch_2
        :pswitch_1
    .end packed-switch
.end method

.method public final removeDetachedView(Landroid/view/View;Z)V
    .locals 5

    .line 32101
    invoke-static {p1}, Lcom/facebook/ads/redexgen/X/Eb;->A0F(Landroid/view/View;)Lcom/facebook/ads/redexgen/X/56;

    move-result-object v4

    .line 32102
    .local v0, "vh":Lcom/facebook/ads/redexgen/X/56;
    if-eqz v4, :cond_0

    .line 32103
    invoke-virtual {v4}, Lcom/facebook/ads/redexgen/X/56;->A0c()Z

    move-result v0

    if-eqz v0, :cond_1

    .line 32104
    invoke-virtual {v4}, Lcom/facebook/ads/redexgen/X/56;->A0P()V

    .line 32105
    :cond_0
    :goto_0
    invoke-virtual {p1}, Landroid/view/View;->clearAnimation()V

    .line 32106
    invoke-virtual {p0, p1}, Lcom/facebook/ads/redexgen/X/Eb;->A1e(Landroid/view/View;)V

    .line 32107
    invoke-super {p0, p1, p2}, Landroid/view/ViewGroup;->removeDetachedView(Landroid/view/View;Z)V

    .line 32108
    return-void

    .line 32109
    :cond_1
    invoke-virtual {v4}, Lcom/facebook/ads/redexgen/X/56;->A0f()Z

    move-result v0

    if-eqz v0, :cond_2

    goto :goto_0

    .line 32110
    :cond_2
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const/16 v2, 0xdb

    const/16 v1, 0x4b

    const/16 v0, 0x25

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Eb;->A0I(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v1

    .line 32111
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->A1H()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    new-instance v0, Ljava/lang/IllegalArgumentException;

    invoke-direct {v0, v1}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw v0
.end method

.method public final requestChildFocus(Landroid/view/View;Landroid/view/View;)V
    .locals 2

    .line 32112
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0s:Lcom/facebook/ads/redexgen/X/53;

    invoke-virtual {v1, p0, v0, p1, p2}, Lcom/facebook/ads/redexgen/X/4o;->A1b(Lcom/facebook/ads/redexgen/X/Eb;Lcom/facebook/ads/redexgen/X/53;Landroid/view/View;Landroid/view/View;)Z

    move-result v0

    if-nez v0, :cond_0

    if-eqz p2, :cond_0

    .line 32113
    invoke-direct {p0, p1, p2}, Lcom/facebook/ads/redexgen/X/Eb;->A0p(Landroid/view/View;Landroid/view/View;)V

    .line 32114
    :cond_0
    invoke-super {p0, p1, p2}, Landroid/view/ViewGroup;->requestChildFocus(Landroid/view/View;Landroid/view/View;)V

    .line 32115
    return-void
.end method

.method public final requestChildRectangleOnScreen(Landroid/view/View;Landroid/graphics/Rect;Z)Z
    .locals 1

    .line 32116
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    invoke-virtual {v0, p0, p1, p2, p3}, Lcom/facebook/ads/redexgen/X/4o;->A1Z(Lcom/facebook/ads/redexgen/X/Eb;Landroid/view/View;Landroid/graphics/Rect;Z)Z

    move-result v0

    return v0
.end method

.method public final requestDisallowInterceptTouchEvent(Z)V
    .locals 3

    .line 32117
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A13:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->size()I

    move-result v2

    .line 32118
    .local v0, "listenerCount":I
    const/4 v1, 0x0

    .local v1, "i":I
    :goto_0
    if-ge v1, v2, :cond_0

    .line 32119
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A13:Ljava/util/ArrayList;

    invoke-virtual {v0, v1}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    .line 32120
    .local v2, "listener":Lcom/facebook/ads/redexgen/X/4s;
    .end local v2    # "listener":Lcom/facebook/ads/redexgen/X/4s;
    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    .line 32121
    .end local v1    # "i":I
    :cond_0
    invoke-super {p0, p1}, Landroid/view/ViewGroup;->requestDisallowInterceptTouchEvent(Z)V

    .line 32122
    return-void
.end method

.method public final requestLayout()V
    .locals 1

    .line 32123
    iget v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0O:I

    if-nez v0, :cond_0

    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0I:Z

    if-nez v0, :cond_0

    .line 32124
    invoke-super {p0}, Landroid/view/ViewGroup;->requestLayout()V

    .line 32125
    :goto_0
    return-void

    .line 32126
    :cond_0
    const/4 v0, 0x1

    iput-boolean v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0J:Z

    goto :goto_0
.end method

.method public final scrollBy(II)V
    .locals 4

    .line 32127
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    if-nez v1, :cond_0

    .line 32128
    const/16 v2, 0x55a

    const/16 v1, 0xc

    const/16 v0, 0x4f

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Eb;->A0I(III)Ljava/lang/String;

    move-result-object v3

    const/16 v2, 0x342

    const/16 v1, 0x5a

    const/16 v0, 0x47

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Eb;->A0I(III)Ljava/lang/String;

    move-result-object v0

    invoke-static {v3, v0}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    .line 32129
    return-void

    .line 32130
    :cond_0
    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0I:Z

    if-eqz v0, :cond_1

    .line 32131
    return-void

    .line 32132
    :cond_1
    invoke-virtual {v1}, Lcom/facebook/ads/redexgen/X/4o;->A20()Z

    move-result v2

    .line 32133
    .local v0, "canScrollHorizontal":Z
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/4o;->A21()Z

    move-result v0

    .line 32134
    .local v1, "canScrollVertical":Z
    if-nez v2, :cond_2

    if-eqz v0, :cond_4

    .line 32135
    :cond_2
    const/4 v1, 0x0

    if-eqz v2, :cond_5

    :goto_0
    if-eqz v0, :cond_3

    move v1, p2

    :cond_3
    const/4 v0, 0x0

    invoke-direct {p0, p1, v1, v0}, Lcom/facebook/ads/redexgen/X/Eb;->A13(IILandroid/view/MotionEvent;)Z

    .line 32136
    :cond_4
    return-void

    .line 32137
    :cond_5
    const/4 p1, 0x0

    goto :goto_0
.end method

.method public final scrollTo(II)V
    .locals 4

    .line 32138
    const/16 v2, 0x55a

    const/16 v1, 0xc

    const/16 v0, 0x4f

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Eb;->A0I(III)Ljava/lang/String;

    move-result-object v3

    const/16 v2, 0x566

    const/16 v1, 0x5d

    const/16 v0, 0x5a

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Eb;->A0I(III)Ljava/lang/String;

    move-result-object v0

    invoke-static {v3, v0}, Landroid/util/Log;->w(Ljava/lang/String;Ljava/lang/String;)I

    .line 32139
    return-void
.end method

.method public final sendAccessibilityEventUnchecked(Landroid/view/accessibility/AccessibilityEvent;)V
    .locals 1

    .line 32140
    invoke-direct {p0, p1}, Lcom/facebook/ads/redexgen/X/Eb;->A18(Landroid/view/accessibility/AccessibilityEvent;)Z

    move-result v0

    if-eqz v0, :cond_0

    .line 32141
    return-void

    .line 32142
    :cond_0
    invoke-super {p0, p1}, Landroid/view/ViewGroup;->sendAccessibilityEventUnchecked(Landroid/view/accessibility/AccessibilityEvent;)V

    .line 32143
    return-void
.end method

.method public setAccessibilityDelegateCompat(Lcom/facebook/ads/redexgen/X/Zb;)V
    .locals 0

    .line 32144
    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/Eb;->A09:Lcom/facebook/ads/redexgen/X/Zb;

    .line 32145
    invoke-static {p0, p1}, Lcom/facebook/ads/redexgen/X/3T;->A0B(Landroid/view/View;Lcom/facebook/ads/redexgen/X/37;)V

    .line 32146
    return-void
.end method

.method public setAdapter(Lcom/facebook/ads/redexgen/X/4c;)V
    .locals 2

    .line 32147
    const/4 v1, 0x0

    invoke-virtual {p0, v1}, Lcom/facebook/ads/redexgen/X/Eb;->setLayoutFrozen(Z)V

    .line 32148
    const/4 v0, 0x1

    invoke-direct {p0, p1, v1, v0}, Lcom/facebook/ads/redexgen/X/Eb;->A0q(Lcom/facebook/ads/redexgen/X/4c;ZZ)V

    .line 32149
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->requestLayout()V

    .line 32150
    return-void
.end method

.method public setChildDrawingOrderCallback(Lcom/facebook/ads/redexgen/X/4f;)V
    .locals 1

    .line 32151
    const/4 v0, 0x0

    if-ne p1, v0, :cond_0

    .line 32152
    return-void

    .line 32153
    :cond_0
    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0e:Lcom/facebook/ads/redexgen/X/4f;

    .line 32154
    if-eqz p1, :cond_1

    const/4 v0, 0x1

    :goto_0
    invoke-virtual {p0, v0}, Lcom/facebook/ads/redexgen/X/Eb;->setChildrenDrawingOrderEnabled(Z)V

    .line 32155
    return-void

    .line 32156
    :cond_1
    const/4 v0, 0x0

    goto :goto_0
.end method

.method public setClipToPadding(Z)V
    .locals 1

    .line 32157
    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0B:Z

    if-eq p1, v0, :cond_0

    .line 32158
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/Eb;->A0f()V

    .line 32159
    :cond_0
    iput-boolean p1, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0B:Z

    .line 32160
    invoke-super {p0, p1}, Landroid/view/ViewGroup;->setClipToPadding(Z)V

    .line 32161
    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0D:Z

    if-eqz v0, :cond_1

    .line 32162
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->requestLayout()V

    .line 32163
    :cond_1
    return-void
.end method

.method public setHasFixedSize(Z)V
    .locals 0

    .line 32164
    iput-boolean p1, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0E:Z

    .line 32165
    return-void
.end method

.method public setItemAnimator(Lcom/facebook/ads/redexgen/X/4k;)V
    .locals 4

    .line 32166
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A05:Lcom/facebook/ads/redexgen/X/4k;

    if-eqz v0, :cond_0

    .line 32167
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/4k;->A0I()V

    .line 32168
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/Eb;->A05:Lcom/facebook/ads/redexgen/X/4k;

    const/4 v0, 0x0

    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/4k;->A0B(Lcom/facebook/ads/redexgen/X/4i;)V

    .line 32169
    :cond_0
    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/Eb;->A05:Lcom/facebook/ads/redexgen/X/4k;

    .line 32170
    if-eqz p1, :cond_2

    .line 32171
    iget-object v3, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0f:Lcom/facebook/ads/redexgen/X/4i;

    sget-object v1, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const/4 v0, 0x1

    aget-object v0, v1, v0

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v1

    const/16 v0, 0x20

    if-eq v1, v0, :cond_1

    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    :cond_1
    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const-string v1, "lbql160khUomfa8gG92Y7q"

    const/4 v0, 0x3

    aput-object v1, v2, v0

    const-string v1, "AtGY6u7EZb"

    const/4 v0, 0x0

    aput-object v1, v2, v0

    invoke-virtual {p1, v3}, Lcom/facebook/ads/redexgen/X/4k;->A0B(Lcom/facebook/ads/redexgen/X/4i;)V

    .line 32172
    :cond_2
    return-void
.end method

.method public setItemViewCacheSize(I)V
    .locals 1

    .line 32173
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0r:Lcom/facebook/ads/redexgen/X/4w;

    invoke-virtual {v0, p1}, Lcom/facebook/ads/redexgen/X/4w;->A0Q(I)V

    .line 32174
    return-void
.end method

.method public setLayoutFrozen(Z)V
    .locals 8

    .line 32175
    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0I:Z

    if-eq p1, v0, :cond_1

    .line 32176
    const/16 v2, 0x3fd

    const/16 v1, 0x2a

    const/16 v0, 0x55

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Eb;->A0I(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p0, v0}, Lcom/facebook/ads/redexgen/X/Eb;->A1l(Ljava/lang/String;)V

    .line 32177
    if-nez p1, :cond_2

    .line 32178
    const/4 v1, 0x0

    iput-boolean v1, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0I:Z

    .line 32179
    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0J:Z

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A04:Lcom/facebook/ads/redexgen/X/4c;

    if-eqz v0, :cond_0

    .line 32180
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->requestLayout()V

    .line 32181
    :cond_0
    iput-boolean v1, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0J:Z

    .line 32182
    .end local v0
    .end local v2
    :cond_1
    :goto_0
    return-void

    .line 32183
    :cond_2
    invoke-static {}, Landroid/os/SystemClock;->uptimeMillis()J

    move-result-wide v0

    .line 32184
    .local v0, "now":J
    const/4 v4, 0x3

    const/4 v5, 0x0

    const/4 v6, 0x0

    const/4 v7, 0x0

    move-wide v2, v0

    invoke-static/range {v0 .. v7}, Landroid/view/MotionEvent;->obtain(JJIFFI)Landroid/view/MotionEvent;

    move-result-object v0

    .line 32185
    .local v2, "cancelEvent":Landroid/view/MotionEvent;
    invoke-virtual {p0, v0}, Lcom/facebook/ads/redexgen/X/Eb;->onTouchEvent(Landroid/view/MotionEvent;)Z

    .line 32186
    const/4 v0, 0x1

    iput-boolean v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0I:Z

    .line 32187
    iput-boolean v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0n:Z

    .line 32188
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->A1R()V

    goto :goto_0
.end method

.method public setLayoutManager(Lcom/facebook/ads/redexgen/X/4o;)V
    .locals 4

    .line 32189
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    if-ne p1, v0, :cond_0

    .line 32190
    return-void

    .line 32191
    :cond_0
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->A1R()V

    .line 32192
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    if-eqz v0, :cond_3

    .line 32193
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A05:Lcom/facebook/ads/redexgen/X/4k;

    if-eqz v0, :cond_1

    .line 32194
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/4k;->A0I()V

    .line 32195
    :cond_1
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0r:Lcom/facebook/ads/redexgen/X/4w;

    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/4o;->A1I(Lcom/facebook/ads/redexgen/X/4w;)V

    .line 32196
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0r:Lcom/facebook/ads/redexgen/X/4w;

    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/4o;->A1G(Lcom/facebook/ads/redexgen/X/4w;)V

    .line 32197
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0r:Lcom/facebook/ads/redexgen/X/4w;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/4w;->A0P()V

    .line 32198
    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0F:Z

    if-eqz v0, :cond_2

    .line 32199
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0r:Lcom/facebook/ads/redexgen/X/4w;

    invoke-virtual {v1, p0, v0}, Lcom/facebook/ads/redexgen/X/4o;->A1Q(Lcom/facebook/ads/redexgen/X/Eb;Lcom/facebook/ads/redexgen/X/4w;)V

    .line 32200
    :cond_2
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    const/4 v0, 0x0

    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/4o;->A1P(Lcom/facebook/ads/redexgen/X/Eb;)V

    .line 32201
    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    .line 32202
    :goto_0
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A01:Lcom/facebook/ads/redexgen/X/4C;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/4C;->A0B()V

    .line 32203
    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    .line 32204
    if-eqz p1, :cond_5

    .line 32205
    iget-object v3, p1, Lcom/facebook/ads/redexgen/X/4o;->A03:Lcom/facebook/ads/redexgen/X/Eb;

    sget-object v1, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const/4 v0, 0x1

    aget-object v0, v1, v0

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v1

    const/16 v0, 0x20

    if-eq v1, v0, :cond_4

    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    .line 32206
    :cond_3
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0r:Lcom/facebook/ads/redexgen/X/4w;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/4w;->A0P()V

    goto :goto_0

    :cond_4
    sget-object v2, Lcom/facebook/ads/redexgen/X/Eb;->A19:[Ljava/lang/String;

    const-string v1, "5zwSvF43vthb6lIFeaOoDRJ244Dq07FI"

    const/4 v0, 0x6

    aput-object v1, v2, v0

    const-string v1, "COyIsU8he0MLuT7wFa9x9SZ5A7LzgdfA"

    const/4 v0, 0x4

    aput-object v1, v2, v0

    if-nez v3, :cond_6

    .line 32207
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    invoke-virtual {v0, p0}, Lcom/facebook/ads/redexgen/X/4o;->A1P(Lcom/facebook/ads/redexgen/X/Eb;)V

    .line 32208
    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0F:Z

    if-eqz v0, :cond_5

    .line 32209
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A06:Lcom/facebook/ads/redexgen/X/4o;

    invoke-virtual {v0, p0}, Lcom/facebook/ads/redexgen/X/4o;->A1N(Lcom/facebook/ads/redexgen/X/Eb;)V

    .line 32210
    :cond_5
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0r:Lcom/facebook/ads/redexgen/X/4w;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/4w;->A0O()V

    .line 32211
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->requestLayout()V

    .line 32212
    return-void

    .line 32213
    :cond_6
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const/16 v2, 0x455

    const/16 v1, 0xe

    const/16 v0, 0x58

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Eb;->A0I(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v3

    const/16 v2, 0x3b

    const/16 v1, 0x27

    const/16 v0, 0x9

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Eb;->A0I(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    iget-object v0, p1, Lcom/facebook/ads/redexgen/X/4o;->A03:Lcom/facebook/ads/redexgen/X/Eb;

    .line 32214
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/Eb;->A1H()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    new-instance v0, Ljava/lang/IllegalArgumentException;

    invoke-direct {v0, v1}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw v0
.end method

.method public setNestedScrollingEnabled(Z)V
    .locals 1

    .line 32215
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/Eb;->getScrollingChildHelper()Lcom/facebook/ads/redexgen/X/3B;

    move-result-object v0

    invoke-virtual {v0, p1}, Lcom/facebook/ads/redexgen/X/3B;->A04(Z)V

    .line 32216
    return-void
.end method

.method public setOnFlingListener(Lcom/facebook/ads/redexgen/X/4r;)V
    .locals 0

    .line 32217
    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0g:Lcom/facebook/ads/redexgen/X/4r;

    .line 32218
    return-void
.end method

.method public setOnScrollListener(Lcom/facebook/ads/redexgen/X/4t;)V
    .locals 0
    .annotation runtime Ljava/lang/Deprecated;
    .end annotation

    .line 32219
    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0i:Lcom/facebook/ads/redexgen/X/4t;

    .line 32220
    return-void
.end method

.method public setPreserveFocusAfterLayout(Z)V
    .locals 0

    .line 32221
    iput-boolean p1, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0o:Z

    .line 32222
    return-void
.end method

.method public setRecycledViewPool(Lcom/facebook/ads/redexgen/X/4v;)V
    .locals 1

    .line 32223
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0r:Lcom/facebook/ads/redexgen/X/4w;

    invoke-virtual {v0, p1}, Lcom/facebook/ads/redexgen/X/4w;->A0V(Lcom/facebook/ads/redexgen/X/4v;)V

    .line 32224
    return-void
.end method

.method public setRecyclerListener(Lcom/facebook/ads/redexgen/X/4x;)V
    .locals 0

    .line 32225
    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/Eb;->A07:Lcom/facebook/ads/redexgen/X/4x;

    .line 32226
    return-void
.end method

.method public setScrollState(I)V
    .locals 1

    .line 32227
    iget v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0W:I

    if-ne p1, v0, :cond_0

    .line 32228
    return-void

    .line 32229
    :cond_0
    iput p1, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0W:I

    .line 32230
    const/4 v0, 0x2

    if-eq p1, v0, :cond_1

    .line 32231
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/Eb;->A0V()V

    .line 32232
    :cond_1
    invoke-direct {p0, p1}, Lcom/facebook/ads/redexgen/X/Eb;->A0k(I)V

    .line 32233
    return-void
.end method

.method public setScrollingTouchSlop(I)V
    .locals 5

    .line 32234
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Eb;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-static {v0}, Landroid/view/ViewConfiguration;->get(Landroid/content/Context;)Landroid/view/ViewConfiguration;

    move-result-object v4

    .line 32235
    .local v0, "vc":Landroid/view/ViewConfiguration;
    packed-switch p1, :pswitch_data_0

    .line 32236
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const/16 v2, 0x7ca

    const/16 v1, 0x2f

    const/16 v0, 0x55

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Eb;->A0I(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v3

    const/16 v2, 0xc6

    const/16 v1, 0x15

    const/16 v0, 0x79

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Eb;->A0I(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    const/16 v2, 0x55a

    const/16 v1, 0xc

    const/16 v0, 0x4f

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Eb;->A0I(III)Ljava/lang/String;

    move-result-object v0

    invoke-static {v0, v3}, Landroid/util/Log;->w(Ljava/lang/String;Ljava/lang/String;)I

    .line 32237
    :pswitch_0
    invoke-virtual {v4}, Landroid/view/ViewConfiguration;->getScaledTouchSlop()I

    move-result v0

    iput v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0X:I

    .line 32238
    :goto_0
    return-void

    .line 32239
    :pswitch_1
    invoke-virtual {v4}, Landroid/view/ViewConfiguration;->getScaledPagingTouchSlop()I

    move-result v0

    iput v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0X:I

    goto :goto_0

    nop

    :pswitch_data_0
    .packed-switch 0x0
        :pswitch_0
        :pswitch_1
    .end packed-switch
.end method

.method public setViewCacheExtension(Lcom/facebook/ads/redexgen/X/54;)V
    .locals 1

    .line 32240
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Eb;->A0r:Lcom/facebook/ads/redexgen/X/4w;

    invoke-virtual {v0, p1}, Lcom/facebook/ads/redexgen/X/4w;->A0W(Lcom/facebook/ads/redexgen/X/54;)V

    .line 32241
    return-void
.end method

.method public final startNestedScroll(I)Z
    .locals 1

    .line 32242
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/Eb;->getScrollingChildHelper()Lcom/facebook/ads/redexgen/X/3B;

    move-result-object v0

    invoke-virtual {v0, p1}, Lcom/facebook/ads/redexgen/X/3B;->A0A(I)Z

    move-result v0

    return v0
.end method

.method public final stopNestedScroll()V
    .locals 1

    .line 32243
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/Eb;->getScrollingChildHelper()Lcom/facebook/ads/redexgen/X/3B;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/3B;->A02()V

    .line 32244
    return-void
.end method
