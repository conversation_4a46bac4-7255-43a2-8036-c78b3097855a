.class public final Lcom/facebook/ads/redexgen/X/Ee;
.super Lcom/facebook/ads/redexgen/X/Za;
.source ""


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/facebook/ads/redexgen/X/4L;,
        Lcom/facebook/ads/redexgen/X/4M;
    }
.end annotation


# static fields
.field public static A0B:Landroid/animation/TimeInterpolator;

.field public static A0C:[Ljava/lang/String;


# instance fields
.field public A00:Ljava/util/ArrayList;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/ArrayList<",
            "Lcom/facebook/ads/redexgen/X/56;",
            ">;"
        }
    .end annotation
.end field

.field public A01:Ljava/util/ArrayList;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/ArrayList<",
            "Ljava/util/ArrayList<",
            "Lcom/facebook/ads/redexgen/X/56;",
            ">;>;"
        }
    .end annotation
.end field

.field public A02:Ljava/util/ArrayList;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/ArrayList<",
            "Lcom/facebook/ads/redexgen/X/56;",
            ">;"
        }
    .end annotation
.end field

.field public A03:Ljava/util/ArrayList;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/ArrayList<",
            "Ljava/util/ArrayList<",
            "Lcom/facebook/ads/redexgen/X/4L;",
            ">;>;"
        }
    .end annotation
.end field

.field public A04:Ljava/util/ArrayList;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/ArrayList<",
            "Lcom/facebook/ads/redexgen/X/56;",
            ">;"
        }
    .end annotation
.end field

.field public A05:Ljava/util/ArrayList;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/ArrayList<",
            "Ljava/util/ArrayList<",
            "Lcom/facebook/ads/redexgen/X/4M;",
            ">;>;"
        }
    .end annotation
.end field

.field public A06:Ljava/util/ArrayList;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/ArrayList<",
            "Lcom/facebook/ads/redexgen/X/56;",
            ">;"
        }
    .end annotation
.end field

.field public A07:Ljava/util/ArrayList;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/ArrayList<",
            "Lcom/facebook/ads/redexgen/X/56;",
            ">;"
        }
    .end annotation
.end field

.field public A08:Ljava/util/ArrayList;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/ArrayList<",
            "Lcom/facebook/ads/redexgen/X/4L;",
            ">;"
        }
    .end annotation
.end field

.field public A09:Ljava/util/ArrayList;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/ArrayList<",
            "Lcom/facebook/ads/redexgen/X/4M;",
            ">;"
        }
    .end annotation
.end field

.field public A0A:Ljava/util/ArrayList;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/ArrayList<",
            "Lcom/facebook/ads/redexgen/X/56;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public static constructor <clinit>()V
    .locals 3

    .line 1287
    const/16 v0, 0x8

    new-array v2, v0, [Ljava/lang/String;

    const/4 v1, 0x0

    const-string v0, "K3xavpXOVVnTPS8V8FmisIpuez1NdmFn"

    aput-object v0, v2, v1

    const/4 v1, 0x1

    const-string v0, "QjoCrUVtXYT15qNhmppxWCsnlOvXMRWn"

    aput-object v0, v2, v1

    const/4 v1, 0x2

    const-string v0, "HVYav4KnfeU3lAWSFDiSVAYelCZD7hW1"

    aput-object v0, v2, v1

    const/4 v1, 0x3

    const-string v0, "SKhwmHHGUoQzTLK2kw5WKB1efbL3jYLm"

    aput-object v0, v2, v1

    const/4 v1, 0x4

    const-string v0, "aZpNXGptJoOcjVlsPlgA0RFw1bcc4fI1"

    aput-object v0, v2, v1

    const/4 v1, 0x5

    const-string v0, "y8DDj9eTKGlZyxtiH83YwnquX80t2qUq"

    aput-object v0, v2, v1

    const/4 v1, 0x6

    const-string v0, "Eqht3urb"

    aput-object v0, v2, v1

    const/4 v1, 0x7

    const-string v0, "PkOCx2w9GPXTa0iGANdHxOJetOGxrMw0"

    aput-object v0, v2, v1

    sput-object v2, Lcom/facebook/ads/redexgen/X/Ee;->A0C:[Ljava/lang/String;

    return-void
.end method

.method public constructor <init>()V
    .locals 1

    .line 32346
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/Za;-><init>()V

    .line 32347
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/Ee;->A0A:Ljava/util/ArrayList;

    .line 32348
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/Ee;->A07:Ljava/util/ArrayList;

    .line 32349
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/Ee;->A09:Ljava/util/ArrayList;

    .line 32350
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/Ee;->A08:Ljava/util/ArrayList;

    .line 32351
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/Ee;->A01:Ljava/util/ArrayList;

    .line 32352
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/Ee;->A05:Ljava/util/ArrayList;

    .line 32353
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/Ee;->A03:Ljava/util/ArrayList;

    .line 32354
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/Ee;->A00:Ljava/util/ArrayList;

    .line 32355
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/Ee;->A04:Ljava/util/ArrayList;

    .line 32356
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/Ee;->A06:Ljava/util/ArrayList;

    .line 32357
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/Ee;->A02:Ljava/util/ArrayList;

    return-void
.end method

.method private A01(Lcom/facebook/ads/redexgen/X/4L;)V
    .locals 1

    .line 32358
    iget-object v0, p1, Lcom/facebook/ads/redexgen/X/4L;->A05:Lcom/facebook/ads/redexgen/X/56;

    if-eqz v0, :cond_0

    .line 32359
    iget-object v0, p1, Lcom/facebook/ads/redexgen/X/4L;->A05:Lcom/facebook/ads/redexgen/X/56;

    invoke-direct {p0, p1, v0}, Lcom/facebook/ads/redexgen/X/Ee;->A07(Lcom/facebook/ads/redexgen/X/4L;Lcom/facebook/ads/redexgen/X/56;)Z

    .line 32360
    :cond_0
    iget-object v0, p1, Lcom/facebook/ads/redexgen/X/4L;->A04:Lcom/facebook/ads/redexgen/X/56;

    if-eqz v0, :cond_1

    .line 32361
    iget-object v0, p1, Lcom/facebook/ads/redexgen/X/4L;->A04:Lcom/facebook/ads/redexgen/X/56;

    invoke-direct {p0, p1, v0}, Lcom/facebook/ads/redexgen/X/Ee;->A07(Lcom/facebook/ads/redexgen/X/4L;Lcom/facebook/ads/redexgen/X/56;)Z

    .line 32362
    :cond_1
    return-void
.end method

.method private A03(Lcom/facebook/ads/redexgen/X/56;)V
    .locals 4

    .line 32363
    iget-object v3, p1, Lcom/facebook/ads/redexgen/X/56;->A0H:Landroid/view/View;

    .line 32364
    .local v0, "view":Landroid/view/View;
    invoke-virtual {v3}, Landroid/view/View;->animate()Landroid/view/ViewPropertyAnimator;

    move-result-object v2

    .line 32365
    .local v1, "animation":Landroid/view/ViewPropertyAnimator;
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ee;->A06:Ljava/util/ArrayList;

    invoke-virtual {v0, p1}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 32366
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/4k;->A07()J

    move-result-wide v0

    invoke-virtual {v2, v0, v1}, Landroid/view/ViewPropertyAnimator;->setDuration(J)Landroid/view/ViewPropertyAnimator;

    move-result-object v1

    const/4 v0, 0x0

    invoke-virtual {v1, v0}, Landroid/view/ViewPropertyAnimator;->alpha(F)Landroid/view/ViewPropertyAnimator;

    move-result-object v1

    new-instance v0, Lcom/facebook/ads/redexgen/X/4G;

    invoke-direct {v0, p0, p1, v2, v3}, Lcom/facebook/ads/redexgen/X/4G;-><init>(Lcom/facebook/ads/redexgen/X/Ee;Lcom/facebook/ads/redexgen/X/56;Landroid/view/ViewPropertyAnimator;Landroid/view/View;)V

    invoke-virtual {v1, v0}, Landroid/view/ViewPropertyAnimator;->setListener(Landroid/animation/Animator$AnimatorListener;)Landroid/view/ViewPropertyAnimator;

    move-result-object v0

    .line 32367
    invoke-virtual {v0}, Landroid/view/ViewPropertyAnimator;->start()V

    .line 32368
    return-void
.end method

.method private A04(Lcom/facebook/ads/redexgen/X/56;)V
    .locals 2

    .line 32369
    sget-object v0, Lcom/facebook/ads/redexgen/X/Ee;->A0B:Landroid/animation/TimeInterpolator;

    if-nez v0, :cond_0

    .line 32370
    new-instance v0, Landroid/animation/ValueAnimator;

    invoke-direct {v0}, Landroid/animation/ValueAnimator;-><init>()V

    invoke-virtual {v0}, Landroid/animation/ValueAnimator;->getInterpolator()Landroid/animation/TimeInterpolator;

    move-result-object v0

    sput-object v0, Lcom/facebook/ads/redexgen/X/Ee;->A0B:Landroid/animation/TimeInterpolator;

    .line 32371
    :cond_0
    iget-object v0, p1, Lcom/facebook/ads/redexgen/X/56;->A0H:Landroid/view/View;

    invoke-virtual {v0}, Landroid/view/View;->animate()Landroid/view/ViewPropertyAnimator;

    move-result-object v1

    sget-object v0, Lcom/facebook/ads/redexgen/X/Ee;->A0B:Landroid/animation/TimeInterpolator;

    invoke-virtual {v1, v0}, Landroid/view/ViewPropertyAnimator;->setInterpolator(Landroid/animation/TimeInterpolator;)Landroid/view/ViewPropertyAnimator;

    .line 32372
    invoke-virtual {p0, p1}, Lcom/facebook/ads/redexgen/X/Ee;->A0K(Lcom/facebook/ads/redexgen/X/56;)V

    .line 32373
    return-void
.end method

.method private final A05(Ljava/util/List;)V
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lcom/facebook/ads/redexgen/X/56;",
            ">;)V"
        }
    .end annotation

    .line 32374
    .local p1, "viewHolders":Ljava/util/List;, "Ljava/util/List<Lcom/facebook/ads/internal/androidx/support/v7/widget/RecyclerView$ViewHolder;>;"
    invoke-interface {p1}, Ljava/util/List;->size()I

    move-result v0

    add-int/lit8 v1, v0, -0x1

    .local v0, "i":I
    :goto_0
    if-ltz v1, :cond_0

    .line 32375
    invoke-interface {p1, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/facebook/ads/redexgen/X/56;

    iget-object v0, v0, Lcom/facebook/ads/redexgen/X/56;->A0H:Landroid/view/View;

    invoke-virtual {v0}, Landroid/view/View;->animate()Landroid/view/ViewPropertyAnimator;

    move-result-object v0

    invoke-virtual {v0}, Landroid/view/ViewPropertyAnimator;->cancel()V

    .line 32376
    add-int/lit8 v1, v1, -0x1

    goto :goto_0

    .line 32377
    .end local v0    # "i":I
    :cond_0
    return-void
.end method

.method private A06(Ljava/util/List;Lcom/facebook/ads/redexgen/X/56;)V
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lcom/facebook/ads/redexgen/X/4L;",
            ">;",
            "Lcom/facebook/ads/redexgen/X/56;",
            ")V"
        }
    .end annotation

    .line 32378
    .local p1, "infoList":Ljava/util/List;, "Ljava/util/List<Lcom/facebook/ads/internal/androidx/support/v7/widget/DefaultItemAnimator$ChangeInfo;>;"
    invoke-interface {p1}, Ljava/util/List;->size()I

    move-result v0

    add-int/lit8 v2, v0, -0x1

    .local v0, "i":I
    :goto_0
    if-ltz v2, :cond_1

    .line 32379
    invoke-interface {p1, v2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/facebook/ads/redexgen/X/4L;

    .line 32380
    .local v1, "changeInfo":Lcom/facebook/ads/redexgen/X/4L;
    invoke-direct {p0, v1, p2}, Lcom/facebook/ads/redexgen/X/Ee;->A07(Lcom/facebook/ads/redexgen/X/4L;Lcom/facebook/ads/redexgen/X/56;)Z

    move-result v0

    if-eqz v0, :cond_0

    .line 32381
    iget-object v0, v1, Lcom/facebook/ads/redexgen/X/4L;->A05:Lcom/facebook/ads/redexgen/X/56;

    if-nez v0, :cond_0

    iget-object v0, v1, Lcom/facebook/ads/redexgen/X/4L;->A04:Lcom/facebook/ads/redexgen/X/56;

    if-nez v0, :cond_0

    .line 32382
    invoke-interface {p1, v1}, Ljava/util/List;->remove(Ljava/lang/Object;)Z

    .line 32383
    .end local v1    # "changeInfo":Lcom/facebook/ads/redexgen/X/4L;
    :cond_0
    add-int/lit8 v2, v2, -0x1

    goto :goto_0

    .line 32384
    .end local v0    # "i":I
    :cond_1
    return-void
.end method

.method private A07(Lcom/facebook/ads/redexgen/X/4L;Lcom/facebook/ads/redexgen/X/56;)Z
    .locals 3

    .line 32385
    const/4 v2, 0x0

    .line 32386
    .local v0, "oldItem":Z
    iget-object v0, p1, Lcom/facebook/ads/redexgen/X/4L;->A04:Lcom/facebook/ads/redexgen/X/56;

    const/4 v1, 0x0

    if-ne v0, p2, :cond_0

    .line 32387
    iput-object v1, p1, Lcom/facebook/ads/redexgen/X/4L;->A04:Lcom/facebook/ads/redexgen/X/56;

    .line 32388
    :goto_0
    iget-object v1, p2, Lcom/facebook/ads/redexgen/X/56;->A0H:Landroid/view/View;

    const/high16 v0, 0x3f800000    # 1.0f

    invoke-virtual {v1, v0}, Landroid/view/View;->setAlpha(F)V

    .line 32389
    iget-object v0, p2, Lcom/facebook/ads/redexgen/X/56;->A0H:Landroid/view/View;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Landroid/view/View;->setTranslationX(F)V

    .line 32390
    iget-object v0, p2, Lcom/facebook/ads/redexgen/X/56;->A0H:Landroid/view/View;

    invoke-virtual {v0, v1}, Landroid/view/View;->setTranslationY(F)V

    .line 32391
    invoke-virtual {p0, p2, v2}, Lcom/facebook/ads/redexgen/X/Za;->A0Q(Lcom/facebook/ads/redexgen/X/56;Z)V

    .line 32392
    const/4 v0, 0x1

    return v0

    .line 32393
    :cond_0
    iget-object v0, p1, Lcom/facebook/ads/redexgen/X/4L;->A05:Lcom/facebook/ads/redexgen/X/56;

    if-ne v0, p2, :cond_1

    .line 32394
    iput-object v1, p1, Lcom/facebook/ads/redexgen/X/4L;->A05:Lcom/facebook/ads/redexgen/X/56;

    .line 32395
    const/4 v2, 0x1

    goto :goto_0

    .line 32396
    :cond_1
    const/4 v0, 0x0

    return v0
.end method


# virtual methods
.method public final A0I()V
    .locals 7

    .line 32397
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ee;->A09:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->size()I

    move-result v0

    .line 32398
    .local v0, "count":I
    add-int/lit8 v2, v0, -0x1

    .local v1, "i":I
    :goto_0
    const/4 v6, 0x0

    if-ltz v2, :cond_0

    .line 32399
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ee;->A09:Ljava/util/ArrayList;

    invoke-virtual {v0, v2}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/facebook/ads/redexgen/X/4M;

    .line 32400
    .local v3, "item":Lcom/facebook/ads/redexgen/X/4M;
    iget-object v0, v1, Lcom/facebook/ads/redexgen/X/4M;->A04:Lcom/facebook/ads/redexgen/X/56;

    iget-object v0, v0, Lcom/facebook/ads/redexgen/X/56;->A0H:Landroid/view/View;

    .line 32401
    .local v4, "view":Landroid/view/View;
    invoke-virtual {v0, v6}, Landroid/view/View;->setTranslationY(F)V

    .line 32402
    invoke-virtual {v0, v6}, Landroid/view/View;->setTranslationX(F)V

    .line 32403
    iget-object v0, v1, Lcom/facebook/ads/redexgen/X/4M;->A04:Lcom/facebook/ads/redexgen/X/56;

    invoke-virtual {p0, v0}, Lcom/facebook/ads/redexgen/X/Za;->A0O(Lcom/facebook/ads/redexgen/X/56;)V

    .line 32404
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ee;->A09:Ljava/util/ArrayList;

    invoke-virtual {v0, v2}, Ljava/util/ArrayList;->remove(I)Ljava/lang/Object;

    .line 32405
    .end local v3    # "item":Lcom/facebook/ads/redexgen/X/4M;
    .end local v4    # "view":Landroid/view/View;
    add-int/lit8 v2, v2, -0x1

    goto :goto_0

    .line 32406
    .end local v1    # "i":I
    :cond_0
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ee;->A0A:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->size()I

    move-result v0

    .line 32407
    add-int/lit8 v1, v0, -0x1

    .restart local v1    # "i":I
    :goto_1
    if-ltz v1, :cond_1

    .line 32408
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ee;->A0A:Ljava/util/ArrayList;

    invoke-virtual {v0, v1}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/facebook/ads/redexgen/X/56;

    .line 32409
    .local v3, "item":Lcom/facebook/ads/redexgen/X/56;
    invoke-virtual {p0, v0}, Lcom/facebook/ads/redexgen/X/Za;->A0P(Lcom/facebook/ads/redexgen/X/56;)V

    .line 32410
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ee;->A0A:Ljava/util/ArrayList;

    invoke-virtual {v0, v1}, Ljava/util/ArrayList;->remove(I)Ljava/lang/Object;

    .line 32411
    .end local v3    # "item":Lcom/facebook/ads/redexgen/X/56;
    add-int/lit8 v1, v1, -0x1

    goto :goto_1

    .line 32412
    .end local v1    # "i":I
    :cond_1
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ee;->A07:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->size()I

    move-result v0

    .line 32413
    add-int/lit8 v2, v0, -0x1

    .restart local v1    # "i":I
    :goto_2
    const/high16 v5, 0x3f800000    # 1.0f

    if-ltz v2, :cond_2

    .line 32414
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ee;->A07:Ljava/util/ArrayList;

    invoke-virtual {v0, v2}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/facebook/ads/redexgen/X/56;

    .line 32415
    .local v4, "item":Lcom/facebook/ads/redexgen/X/56;
    iget-object v0, v1, Lcom/facebook/ads/redexgen/X/56;->A0H:Landroid/view/View;

    invoke-virtual {v0, v5}, Landroid/view/View;->setAlpha(F)V

    .line 32416
    invoke-virtual {p0, v1}, Lcom/facebook/ads/redexgen/X/Za;->A0N(Lcom/facebook/ads/redexgen/X/56;)V

    .line 32417
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ee;->A07:Ljava/util/ArrayList;

    invoke-virtual {v0, v2}, Ljava/util/ArrayList;->remove(I)Ljava/lang/Object;

    .line 32418
    .end local v4    # "item":Lcom/facebook/ads/redexgen/X/56;
    add-int/lit8 v2, v2, -0x1

    goto :goto_2

    .line 32419
    .end local v1    # "i":I
    :cond_2
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ee;->A08:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->size()I

    move-result v0

    .line 32420
    add-int/lit8 v1, v0, -0x1

    .restart local v1    # "i":I
    :goto_3
    if-ltz v1, :cond_3

    .line 32421
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ee;->A08:Ljava/util/ArrayList;

    invoke-virtual {v0, v1}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/facebook/ads/redexgen/X/4L;

    invoke-direct {p0, v0}, Lcom/facebook/ads/redexgen/X/Ee;->A01(Lcom/facebook/ads/redexgen/X/4L;)V

    .line 32422
    add-int/lit8 v1, v1, -0x1

    goto :goto_3

    .line 32423
    .end local v1    # "i":I
    :cond_3
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ee;->A08:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->clear()V

    .line 32424
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Ee;->A0L()Z

    move-result v0

    if-nez v0, :cond_4

    .line 32425
    return-void

    .line 32426
    :cond_4
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ee;->A05:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->size()I

    move-result v0

    .line 32427
    .local v1, "listCount":I
    add-int/lit8 v4, v0, -0x1

    .local v4, "i":I
    :goto_4
    if-ltz v4, :cond_7

    .line 32428
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ee;->A05:Ljava/util/ArrayList;

    invoke-virtual {v0, v4}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Ljava/util/ArrayList;

    .line 32429
    .local v5, "moves":Ljava/util/ArrayList;, "Ljava/util/ArrayList<Lcom/facebook/ads/internal/androidx/support/v7/widget/DefaultItemAnimator$MoveInfo;>;"
    invoke-virtual {v3}, Ljava/util/ArrayList;->size()I

    move-result v0

    .line 32430
    add-int/lit8 v2, v0, -0x1

    .local v6, "j":I
    :goto_5
    if-ltz v2, :cond_6

    .line 32431
    invoke-virtual {v3, v2}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/facebook/ads/redexgen/X/4M;

    .line 32432
    .local p0, "moveInfo":Lcom/facebook/ads/redexgen/X/4M;
    iget-object v0, v1, Lcom/facebook/ads/redexgen/X/4M;->A04:Lcom/facebook/ads/redexgen/X/56;

    .line 32433
    .local p1, "item":Lcom/facebook/ads/redexgen/X/56;
    iget-object v0, v0, Lcom/facebook/ads/redexgen/X/56;->A0H:Landroid/view/View;

    .line 32434
    .local p2, "view":Landroid/view/View;
    invoke-virtual {v0, v6}, Landroid/view/View;->setTranslationY(F)V

    .line 32435
    invoke-virtual {v0, v6}, Landroid/view/View;->setTranslationX(F)V

    .line 32436
    iget-object v0, v1, Lcom/facebook/ads/redexgen/X/4M;->A04:Lcom/facebook/ads/redexgen/X/56;

    invoke-virtual {p0, v0}, Lcom/facebook/ads/redexgen/X/Za;->A0O(Lcom/facebook/ads/redexgen/X/56;)V

    .line 32437
    invoke-virtual {v3, v2}, Ljava/util/ArrayList;->remove(I)Ljava/lang/Object;

    .line 32438
    invoke-virtual {v3}, Ljava/util/ArrayList;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_5

    .line 32439
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ee;->A05:Ljava/util/ArrayList;

    invoke-virtual {v0, v3}, Ljava/util/ArrayList;->remove(Ljava/lang/Object;)Z

    .line 32440
    .end local p0    # "moveInfo":Lcom/facebook/ads/redexgen/X/4M;
    .end local p1
    .end local p2
    :cond_5
    add-int/lit8 v2, v2, -0x1

    goto :goto_5

    .line 32441
    .end local v5    # "moves":Ljava/util/ArrayList;, "Ljava/util/ArrayList<Lcom/facebook/ads/internal/androidx/support/v7/widget/DefaultItemAnimator$MoveInfo;>;"
    .end local v6    # "j":I
    :cond_6
    add-int/lit8 v4, v4, -0x1

    goto :goto_4

    .line 32442
    .end local v4    # "i":I
    :cond_7
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ee;->A01:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->size()I

    move-result v0

    .line 32443
    add-int/lit8 v4, v0, -0x1

    .local v2, "i":I
    :goto_6
    if-ltz v4, :cond_a

    .line 32444
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ee;->A01:Ljava/util/ArrayList;

    invoke-virtual {v0, v4}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Ljava/util/ArrayList;

    .line 32445
    .local v4, "additions":Ljava/util/ArrayList;, "Ljava/util/ArrayList<Lcom/facebook/ads/internal/androidx/support/v7/widget/RecyclerView$ViewHolder;>;"
    invoke-virtual {v3}, Ljava/util/ArrayList;->size()I

    move-result v0

    .line 32446
    add-int/lit8 v2, v0, -0x1

    .local v5, "j":I
    :goto_7
    if-ltz v2, :cond_9

    .line 32447
    invoke-virtual {v3, v2}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/facebook/ads/redexgen/X/56;

    .line 32448
    .local v6, "item":Lcom/facebook/ads/redexgen/X/56;
    iget-object v0, v1, Lcom/facebook/ads/redexgen/X/56;->A0H:Landroid/view/View;

    .line 32449
    .local p0, "view":Landroid/view/View;
    invoke-virtual {v0, v5}, Landroid/view/View;->setAlpha(F)V

    .line 32450
    invoke-virtual {p0, v1}, Lcom/facebook/ads/redexgen/X/Za;->A0N(Lcom/facebook/ads/redexgen/X/56;)V

    .line 32451
    invoke-virtual {v3, v2}, Ljava/util/ArrayList;->remove(I)Ljava/lang/Object;

    .line 32452
    invoke-virtual {v3}, Ljava/util/ArrayList;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_8

    .line 32453
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ee;->A01:Ljava/util/ArrayList;

    invoke-virtual {v0, v3}, Ljava/util/ArrayList;->remove(Ljava/lang/Object;)Z

    .line 32454
    .end local v6    # "item":Lcom/facebook/ads/redexgen/X/56;
    .end local p0    # "view":Landroid/view/View;
    :cond_8
    add-int/lit8 v2, v2, -0x1

    goto :goto_7

    .line 32455
    .end local v4    # "additions":Ljava/util/ArrayList;, "Ljava/util/ArrayList<Lcom/facebook/ads/internal/androidx/support/v7/widget/RecyclerView$ViewHolder;>;"
    .end local v5    # "j":I
    :cond_9
    add-int/lit8 v4, v4, -0x1

    goto :goto_6

    .line 32456
    .end local v2    # "i":I
    :cond_a
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ee;->A03:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->size()I

    move-result v0

    .line 32457
    add-int/lit8 v3, v0, -0x1

    .restart local v2    # "i":I
    :goto_8
    if-ltz v3, :cond_d

    .line 32458
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ee;->A03:Ljava/util/ArrayList;

    invoke-virtual {v0, v3}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Ljava/util/ArrayList;

    .line 32459
    .local v3, "changes":Ljava/util/ArrayList;, "Ljava/util/ArrayList<Lcom/facebook/ads/internal/androidx/support/v7/widget/DefaultItemAnimator$ChangeInfo;>;"
    invoke-virtual {v2}, Ljava/util/ArrayList;->size()I

    move-result v0

    .line 32460
    add-int/lit8 v1, v0, -0x1

    .local v4, "j":I
    :goto_9
    if-ltz v1, :cond_c

    .line 32461
    invoke-virtual {v2, v1}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/facebook/ads/redexgen/X/4L;

    invoke-direct {p0, v0}, Lcom/facebook/ads/redexgen/X/Ee;->A01(Lcom/facebook/ads/redexgen/X/4L;)V

    .line 32462
    invoke-virtual {v2}, Ljava/util/ArrayList;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_b

    .line 32463
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ee;->A03:Ljava/util/ArrayList;

    invoke-virtual {v0, v2}, Ljava/util/ArrayList;->remove(Ljava/lang/Object;)Z

    .line 32464
    :cond_b
    add-int/lit8 v1, v1, -0x1

    goto :goto_9

    .line 32465
    .end local v3    # "changes":Ljava/util/ArrayList;, "Ljava/util/ArrayList<Lcom/facebook/ads/internal/androidx/support/v7/widget/DefaultItemAnimator$ChangeInfo;>;"
    .end local v4    # "j":I
    :cond_c
    add-int/lit8 v3, v3, -0x1

    goto :goto_8

    .line 32466
    .end local v2    # "i":I
    :cond_d
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ee;->A06:Ljava/util/ArrayList;

    invoke-direct {p0, v0}, Lcom/facebook/ads/redexgen/X/Ee;->A05(Ljava/util/List;)V

    .line 32467
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ee;->A04:Ljava/util/ArrayList;

    invoke-direct {p0, v0}, Lcom/facebook/ads/redexgen/X/Ee;->A05(Ljava/util/List;)V

    .line 32468
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ee;->A00:Ljava/util/ArrayList;

    invoke-direct {p0, v0}, Lcom/facebook/ads/redexgen/X/Ee;->A05(Ljava/util/List;)V

    .line 32469
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ee;->A02:Ljava/util/ArrayList;

    invoke-direct {p0, v0}, Lcom/facebook/ads/redexgen/X/Ee;->A05(Ljava/util/List;)V

    .line 32470
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/4k;->A0A()V

    .line 32471
    return-void
.end method

.method public final A0J()V
    .locals 12

    .line 32472
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ee;->A0A:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->isEmpty()Z

    move-result v0

    xor-int/lit8 v8, v0, 0x1

    .line 32473
    .local v0, "removalsPending":Z
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ee;->A09:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->isEmpty()Z

    move-result v0

    xor-int/lit8 v11, v0, 0x1

    .line 32474
    .local v1, "movesPending":Z
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ee;->A08:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->isEmpty()Z

    move-result v0

    xor-int/lit8 v10, v0, 0x1

    .line 32475
    .local v2, "changesPending":Z
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ee;->A07:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->isEmpty()Z

    move-result v0

    xor-int/lit8 v4, v0, 0x1

    .line 32476
    .local v3, "additionsPending":Z
    if-nez v8, :cond_0

    if-nez v11, :cond_0

    if-nez v4, :cond_0

    if-nez v10, :cond_0

    .line 32477
    return-void

    .line 32478
    :cond_0
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ee;->A0A:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :goto_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_1

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/facebook/ads/redexgen/X/56;

    .line 32479
    .local v5, "holder":Lcom/facebook/ads/redexgen/X/56;
    invoke-direct {p0, v0}, Lcom/facebook/ads/redexgen/X/Ee;->A03(Lcom/facebook/ads/redexgen/X/56;)V

    .line 32480
    .end local v5    # "holder":Lcom/facebook/ads/redexgen/X/56;
    goto :goto_0

    .line 32481
    :cond_1
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ee;->A0A:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->clear()V

    .line 32482
    const/4 v7, 0x0

    if-eqz v11, :cond_2

    .line 32483
    new-instance v1, Ljava/util/ArrayList;

    invoke-direct {v1}, Ljava/util/ArrayList;-><init>()V

    .line 32484
    .local v5, "moves":Ljava/util/ArrayList;, "Ljava/util/ArrayList<Lcom/facebook/ads/internal/androidx/support/v7/widget/DefaultItemAnimator$MoveInfo;>;"
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ee;->A09:Ljava/util/ArrayList;

    invoke-virtual {v1, v0}, Ljava/util/ArrayList;->addAll(Ljava/util/Collection;)Z

    .line 32485
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ee;->A05:Ljava/util/ArrayList;

    invoke-virtual {v0, v1}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 32486
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ee;->A09:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->clear()V

    .line 32487
    new-instance v3, Lcom/facebook/ads/redexgen/X/4D;

    invoke-direct {v3, p0, v1}, Lcom/facebook/ads/redexgen/X/4D;-><init>(Lcom/facebook/ads/redexgen/X/Ee;Ljava/util/ArrayList;)V

    .line 32488
    .local v6, "mover":Ljava/lang/Runnable;
    if-eqz v8, :cond_b

    .line 32489
    invoke-virtual {v1, v7}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/facebook/ads/redexgen/X/4M;

    iget-object v0, v0, Lcom/facebook/ads/redexgen/X/4M;->A04:Lcom/facebook/ads/redexgen/X/56;

    iget-object v2, v0, Lcom/facebook/ads/redexgen/X/56;->A0H:Landroid/view/View;

    .line 32490
    .local v7, "view":Landroid/view/View;
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/4k;->A07()J

    move-result-wide v0

    invoke-static {v2, v3, v0, v1}, Lcom/facebook/ads/redexgen/X/3T;->A0E(Landroid/view/View;Ljava/lang/Runnable;J)V

    .line 32491
    .end local v7    # "view":Landroid/view/View;
    .end local v5    # "moves":Ljava/util/ArrayList;, "Ljava/util/ArrayList<Lcom/facebook/ads/internal/androidx/support/v7/widget/DefaultItemAnimator$MoveInfo;>;"
    .end local v6    # "mover":Ljava/lang/Runnable;
    :cond_2
    :goto_1
    if-eqz v10, :cond_3

    .line 32492
    new-instance v1, Ljava/util/ArrayList;

    invoke-direct {v1}, Ljava/util/ArrayList;-><init>()V

    .line 32493
    .local v5, "changes":Ljava/util/ArrayList;, "Ljava/util/ArrayList<Lcom/facebook/ads/internal/androidx/support/v7/widget/DefaultItemAnimator$ChangeInfo;>;"
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ee;->A08:Ljava/util/ArrayList;

    invoke-virtual {v1, v0}, Ljava/util/ArrayList;->addAll(Ljava/util/Collection;)Z

    .line 32494
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ee;->A03:Ljava/util/ArrayList;

    invoke-virtual {v0, v1}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 32495
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ee;->A08:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->clear()V

    .line 32496
    new-instance v3, Lcom/facebook/ads/redexgen/X/4E;

    invoke-direct {v3, p0, v1}, Lcom/facebook/ads/redexgen/X/4E;-><init>(Lcom/facebook/ads/redexgen/X/Ee;Ljava/util/ArrayList;)V

    .line 32497
    .local v6, "changer":Ljava/lang/Runnable;
    if-eqz v8, :cond_a

    .line 32498
    invoke-virtual {v1, v7}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/facebook/ads/redexgen/X/4L;

    iget-object v0, v0, Lcom/facebook/ads/redexgen/X/4L;->A05:Lcom/facebook/ads/redexgen/X/56;

    .line 32499
    .local v7, "holder":Lcom/facebook/ads/redexgen/X/56;
    iget-object v2, v0, Lcom/facebook/ads/redexgen/X/56;->A0H:Landroid/view/View;

    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/4k;->A07()J

    move-result-wide v0

    invoke-static {v2, v3, v0, v1}, Lcom/facebook/ads/redexgen/X/3T;->A0E(Landroid/view/View;Ljava/lang/Runnable;J)V

    .line 32500
    .end local v7    # "holder":Lcom/facebook/ads/redexgen/X/56;
    .end local v5    # "changes":Ljava/util/ArrayList;, "Ljava/util/ArrayList<Lcom/facebook/ads/internal/androidx/support/v7/widget/DefaultItemAnimator$ChangeInfo;>;"
    .end local v6    # "changer":Ljava/lang/Runnable;
    :cond_3
    :goto_2
    if-eqz v4, :cond_6

    .line 32501
    new-instance v6, Ljava/util/ArrayList;

    invoke-direct {v6}, Ljava/util/ArrayList;-><init>()V

    .line 32502
    .local v5, "additions":Ljava/util/ArrayList;, "Ljava/util/ArrayList<Lcom/facebook/ads/internal/androidx/support/v7/widget/RecyclerView$ViewHolder;>;"
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ee;->A07:Ljava/util/ArrayList;

    invoke-virtual {v6, v0}, Ljava/util/ArrayList;->addAll(Ljava/util/Collection;)Z

    .line 32503
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ee;->A01:Ljava/util/ArrayList;

    invoke-virtual {v0, v6}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 32504
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ee;->A07:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->clear()V

    .line 32505
    new-instance v5, Lcom/facebook/ads/redexgen/X/4F;

    invoke-direct {v5, p0, v6}, Lcom/facebook/ads/redexgen/X/4F;-><init>(Lcom/facebook/ads/redexgen/X/Ee;Ljava/util/ArrayList;)V

    .line 32506
    .local v6, "adder":Ljava/lang/Runnable;
    if-nez v8, :cond_4

    if-nez v11, :cond_4

    if-eqz v10, :cond_9

    .line 32507
    :cond_4
    const-wide/16 v2, 0x0

    sget-object v1, Lcom/facebook/ads/redexgen/X/Ee;->A0C:[Ljava/lang/String;

    const/4 v0, 0x0

    aget-object v1, v1, v0

    const/4 v0, 0x3

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    const/16 v0, 0x53

    if-eq v1, v0, :cond_c

    sget-object v4, Lcom/facebook/ads/redexgen/X/Ee;->A0C:[Ljava/lang/String;

    const-string v1, "oGS3g0YSOogxL8fDiPQ9wWzcLRV8E0qu"

    const/4 v0, 0x4

    aput-object v1, v4, v0

    const-string v1, "UwBhVdK7HoWqzQqehBBKiWd5H5AJwN9O"

    const/4 v0, 0x3

    aput-object v1, v4, v0

    if-eqz v8, :cond_8

    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/4k;->A07()J

    move-result-wide v8

    .line 32508
    .local v9, "removeDuration":J
    :goto_3
    if-eqz v11, :cond_7

    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/4k;->A06()J

    move-result-wide v0

    .line 32509
    .local v11, "moveDuration":J
    :goto_4
    if-eqz v10, :cond_5

    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/4k;->A05()J

    move-result-wide v2

    .line 32510
    .local v7, "changeDuration":J
    :cond_5
    invoke-static {v0, v1, v2, v3}, Ljava/lang/Math;->max(JJ)J

    move-result-wide v1

    add-long/2addr v1, v8

    .line 32511
    .local p1, "totalDelay":J
    invoke-virtual {v6, v7}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/facebook/ads/redexgen/X/56;

    iget-object v0, v0, Lcom/facebook/ads/redexgen/X/56;->A0H:Landroid/view/View;

    .line 32512
    .local v4, "view":Landroid/view/View;
    invoke-static {v0, v5, v1, v2}, Lcom/facebook/ads/redexgen/X/3T;->A0E(Landroid/view/View;Ljava/lang/Runnable;J)V

    .line 32513
    .end local v4    # "view":Landroid/view/View;
    .end local v7    # "changeDuration":J
    .end local v9    # "removeDuration":J
    .end local v11    # "moveDuration":J
    .end local p1
    .end local v5    # "additions":Ljava/util/ArrayList;, "Ljava/util/ArrayList<Lcom/facebook/ads/internal/androidx/support/v7/widget/RecyclerView$ViewHolder;>;"
    .end local v6    # "adder":Ljava/lang/Runnable;
    :cond_6
    :goto_5
    return-void

    .line 32514
    :cond_7
    move-wide v0, v2

    goto :goto_4

    .line 32515
    :cond_8
    move-wide v8, v2

    goto :goto_3

    .line 32516
    :cond_9
    invoke-virtual {v5}, Lcom/facebook/ads/redexgen/X/4F;->run()V

    goto :goto_5

    .line 32517
    :cond_a
    invoke-virtual {v3}, Lcom/facebook/ads/redexgen/X/4E;->run()V

    goto :goto_2

    .line 32518
    :cond_b
    invoke-virtual {v3}, Lcom/facebook/ads/redexgen/X/4D;->run()V

    goto/16 :goto_1

    :cond_c
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0
.end method

.method public final A0K(Lcom/facebook/ads/redexgen/X/56;)V
    .locals 9

    .line 32519
    iget-object v4, p1, Lcom/facebook/ads/redexgen/X/56;->A0H:Landroid/view/View;

    .line 32520
    .local v0, "view":Landroid/view/View;
    invoke-virtual {v4}, Landroid/view/View;->animate()Landroid/view/ViewPropertyAnimator;

    move-result-object v0

    invoke-virtual {v0}, Landroid/view/ViewPropertyAnimator;->cancel()V

    .line 32521
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ee;->A09:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->size()I

    move-result v0

    add-int/lit8 v1, v0, -0x1

    .local v1, "i":I
    :goto_0
    const/4 v6, 0x0

    if-ltz v1, :cond_1

    .line 32522
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ee;->A09:Ljava/util/ArrayList;

    invoke-virtual {v0, v1}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/facebook/ads/redexgen/X/4M;

    .line 32523
    .local v3, "moveInfo":Lcom/facebook/ads/redexgen/X/4M;
    iget-object v0, v0, Lcom/facebook/ads/redexgen/X/4M;->A04:Lcom/facebook/ads/redexgen/X/56;

    if-ne v0, p1, :cond_0

    .line 32524
    invoke-virtual {v4, v6}, Landroid/view/View;->setTranslationY(F)V

    .line 32525
    invoke-virtual {v4, v6}, Landroid/view/View;->setTranslationX(F)V

    .line 32526
    invoke-virtual {p0, p1}, Lcom/facebook/ads/redexgen/X/Za;->A0O(Lcom/facebook/ads/redexgen/X/56;)V

    .line 32527
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ee;->A09:Ljava/util/ArrayList;

    invoke-virtual {v0, v1}, Ljava/util/ArrayList;->remove(I)Ljava/lang/Object;

    .line 32528
    .end local v3    # "moveInfo":Lcom/facebook/ads/redexgen/X/4M;
    :cond_0
    add-int/lit8 v1, v1, -0x1

    goto :goto_0

    .line 32529
    .end local v1    # "i":I
    :cond_1
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ee;->A08:Ljava/util/ArrayList;

    invoke-direct {p0, v0, p1}, Lcom/facebook/ads/redexgen/X/Ee;->A06(Ljava/util/List;Lcom/facebook/ads/redexgen/X/56;)V

    .line 32530
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ee;->A0A:Ljava/util/ArrayList;

    invoke-virtual {v0, p1}, Ljava/util/ArrayList;->remove(Ljava/lang/Object;)Z

    move-result v0

    const/high16 v3, 0x3f800000    # 1.0f

    if-eqz v0, :cond_2

    .line 32531
    invoke-virtual {v4, v3}, Landroid/view/View;->setAlpha(F)V

    .line 32532
    invoke-virtual {p0, p1}, Lcom/facebook/ads/redexgen/X/Za;->A0P(Lcom/facebook/ads/redexgen/X/56;)V

    .line 32533
    :cond_2
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ee;->A07:Ljava/util/ArrayList;

    invoke-virtual {v0, p1}, Ljava/util/ArrayList;->remove(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_3

    .line 32534
    invoke-virtual {v4, v3}, Landroid/view/View;->setAlpha(F)V

    .line 32535
    invoke-virtual {p0, p1}, Lcom/facebook/ads/redexgen/X/Za;->A0N(Lcom/facebook/ads/redexgen/X/56;)V

    .line 32536
    :cond_3
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ee;->A03:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->size()I

    move-result v0

    add-int/lit8 v1, v0, -0x1

    .restart local v1    # "i":I
    :goto_1
    if-ltz v1, :cond_5

    .line 32537
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ee;->A03:Ljava/util/ArrayList;

    invoke-virtual {v0, v1}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/util/ArrayList;

    .line 32538
    .local v4, "changes":Ljava/util/ArrayList;, "Ljava/util/ArrayList<Lcom/facebook/ads/internal/androidx/support/v7/widget/DefaultItemAnimator$ChangeInfo;>;"
    invoke-direct {p0, v0, p1}, Lcom/facebook/ads/redexgen/X/Ee;->A06(Ljava/util/List;Lcom/facebook/ads/redexgen/X/56;)V

    .line 32539
    invoke-virtual {v0}, Ljava/util/ArrayList;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_4

    .line 32540
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ee;->A03:Ljava/util/ArrayList;

    invoke-virtual {v0, v1}, Ljava/util/ArrayList;->remove(I)Ljava/lang/Object;

    .line 32541
    .end local v4    # "changes":Ljava/util/ArrayList;, "Ljava/util/ArrayList<Lcom/facebook/ads/internal/androidx/support/v7/widget/DefaultItemAnimator$ChangeInfo;>;"
    :cond_4
    add-int/lit8 v1, v1, -0x1

    goto :goto_1

    .line 32542
    .end local v1    # "i":I
    :cond_5
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ee;->A05:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->size()I

    move-result v0

    add-int/lit8 v5, v0, -0x1

    .restart local v1    # "i":I
    :goto_2
    if-ltz v5, :cond_9

    .line 32543
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ee;->A05:Ljava/util/ArrayList;

    invoke-virtual {v0, v5}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v8

    check-cast v8, Ljava/util/ArrayList;

    .line 32544
    .local v4, "moves":Ljava/util/ArrayList;, "Ljava/util/ArrayList<Lcom/facebook/ads/internal/androidx/support/v7/widget/DefaultItemAnimator$MoveInfo;>;"
    invoke-virtual {v8}, Ljava/util/ArrayList;->size()I

    move-result v0

    add-int/lit8 v7, v0, -0x1

    .local v5, "j":I
    :goto_3
    if-ltz v7, :cond_8

    .line 32545
    invoke-virtual {v8, v7}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/facebook/ads/redexgen/X/4M;

    .line 32546
    .local v6, "moveInfo":Lcom/facebook/ads/redexgen/X/4M;
    iget-object v0, v0, Lcom/facebook/ads/redexgen/X/4M;->A04:Lcom/facebook/ads/redexgen/X/56;

    if-ne v0, p1, :cond_6

    .line 32547
    invoke-virtual {v4, v6}, Landroid/view/View;->setTranslationY(F)V

    .line 32548
    invoke-virtual {v4, v6}, Landroid/view/View;->setTranslationX(F)V

    sget-object v2, Lcom/facebook/ads/redexgen/X/Ee;->A0C:[Ljava/lang/String;

    const/4 v0, 0x4

    aget-object v1, v2, v0

    const/4 v0, 0x3

    aget-object v2, v2, v0

    const/16 v0, 0x9

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    invoke-virtual {v2, v0}, Ljava/lang/String;->charAt(I)C

    move-result v0

    if-eq v1, v0, :cond_7

    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    .line 32549
    .end local v6    # "moveInfo":Lcom/facebook/ads/redexgen/X/4M;
    :cond_6
    add-int/lit8 v7, v7, -0x1

    goto :goto_3

    .line 32550
    :cond_7
    sget-object v2, Lcom/facebook/ads/redexgen/X/Ee;->A0C:[Ljava/lang/String;

    const-string v1, "ZnQkkSK02m4qlFQsfXGc8k1uWyp7aRL1"

    const/4 v0, 0x1

    aput-object v1, v2, v0

    invoke-virtual {p0, p1}, Lcom/facebook/ads/redexgen/X/Za;->A0O(Lcom/facebook/ads/redexgen/X/56;)V

    .line 32551
    invoke-virtual {v8, v7}, Ljava/util/ArrayList;->remove(I)Ljava/lang/Object;

    .line 32552
    invoke-virtual {v8}, Ljava/util/ArrayList;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_8

    .line 32553
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ee;->A05:Ljava/util/ArrayList;

    invoke-virtual {v0, v5}, Ljava/util/ArrayList;->remove(I)Ljava/lang/Object;

    .line 32554
    .end local v4    # "moves":Ljava/util/ArrayList;, "Ljava/util/ArrayList<Lcom/facebook/ads/internal/androidx/support/v7/widget/DefaultItemAnimator$MoveInfo;>;"
    .end local v5    # "j":I
    :cond_8
    add-int/lit8 v5, v5, -0x1

    goto :goto_2

    .line 32555
    .end local v1    # "i":I
    :cond_9
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ee;->A01:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->size()I

    move-result v5

    sget-object v1, Lcom/facebook/ads/redexgen/X/Ee;->A0C:[Ljava/lang/String;

    const/4 v0, 0x0

    aget-object v1, v1, v0

    const/4 v0, 0x3

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    const/16 v0, 0x53

    if-eq v1, v0, :cond_c

    sget-object v2, Lcom/facebook/ads/redexgen/X/Ee;->A0C:[Ljava/lang/String;

    const-string v1, "NHib0yCpDtVNZGbvtAXjIsv6RzrfBwwf"

    const/4 v0, 0x7

    aput-object v1, v2, v0

    add-int/lit8 v0, v5, -0x1

    .restart local v1    # "i":I
    :goto_4
    if-ltz v0, :cond_b

    .line 32556
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/Ee;->A01:Ljava/util/ArrayList;

    invoke-virtual {v1, v0}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Ljava/util/ArrayList;

    .line 32557
    .local v2, "additions":Ljava/util/ArrayList;, "Ljava/util/ArrayList<Lcom/facebook/ads/internal/androidx/support/v7/widget/RecyclerView$ViewHolder;>;"
    invoke-virtual {v2, p1}, Ljava/util/ArrayList;->remove(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_a

    .line 32558
    invoke-virtual {v4, v3}, Landroid/view/View;->setAlpha(F)V

    .line 32559
    invoke-virtual {p0, p1}, Lcom/facebook/ads/redexgen/X/Za;->A0N(Lcom/facebook/ads/redexgen/X/56;)V

    .line 32560
    invoke-virtual {v2}, Ljava/util/ArrayList;->isEmpty()Z

    move-result v1

    if-eqz v1, :cond_a

    .line 32561
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/Ee;->A01:Ljava/util/ArrayList;

    invoke-virtual {v1, v0}, Ljava/util/ArrayList;->remove(I)Ljava/lang/Object;

    .line 32562
    .end local v2    # "additions":Ljava/util/ArrayList;, "Ljava/util/ArrayList<Lcom/facebook/ads/internal/androidx/support/v7/widget/RecyclerView$ViewHolder;>;"
    :cond_a
    add-int/lit8 v0, v0, -0x1

    goto :goto_4

    .line 32563
    .end local v1    # "i":I
    :cond_b
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ee;->A06:Ljava/util/ArrayList;

    invoke-virtual {v0, p1}, Ljava/util/ArrayList;->remove(Ljava/lang/Object;)Z

    .line 32564
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ee;->A00:Ljava/util/ArrayList;

    invoke-virtual {v0, p1}, Ljava/util/ArrayList;->remove(Ljava/lang/Object;)Z

    .line 32565
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ee;->A02:Ljava/util/ArrayList;

    invoke-virtual {v0, p1}, Ljava/util/ArrayList;->remove(Ljava/lang/Object;)Z

    .line 32566
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ee;->A04:Ljava/util/ArrayList;

    invoke-virtual {v0, p1}, Ljava/util/ArrayList;->remove(Ljava/lang/Object;)Z

    .line 32567
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Ee;->A0V()V

    .line 32568
    return-void

    :cond_c
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0
.end method

.method public final A0L()Z
    .locals 4

    .line 32569
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ee;->A07:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_3

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ee;->A08:Ljava/util/ArrayList;

    .line 32570
    invoke-virtual {v0}, Ljava/util/ArrayList;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_3

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ee;->A09:Ljava/util/ArrayList;

    .line 32571
    invoke-virtual {v0}, Ljava/util/ArrayList;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_3

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ee;->A0A:Ljava/util/ArrayList;

    .line 32572
    invoke-virtual {v0}, Ljava/util/ArrayList;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_3

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ee;->A04:Ljava/util/ArrayList;

    .line 32573
    invoke-virtual {v0}, Ljava/util/ArrayList;->isEmpty()Z

    move-result v3

    sget-object v2, Lcom/facebook/ads/redexgen/X/Ee;->A0C:[Ljava/lang/String;

    const/4 v0, 0x2

    aget-object v1, v2, v0

    const/4 v0, 0x5

    aget-object v2, v2, v0

    const/16 v0, 0x12

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    invoke-virtual {v2, v0}, Ljava/lang/String;->charAt(I)C

    move-result v0

    if-eq v1, v0, :cond_1

    sget-object v2, Lcom/facebook/ads/redexgen/X/Ee;->A0C:[Ljava/lang/String;

    const-string v1, "HATKbuX1ioRbKjbJza4uRkbMkwnilInw"

    const/4 v0, 0x4

    aput-object v1, v2, v0

    const-string v1, "55W5G9gSwoJRpeM2nuMk9rdzPBhIsXI5"

    const/4 v0, 0x3

    aput-object v1, v2, v0

    if-eqz v3, :cond_3

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ee;->A06:Ljava/util/ArrayList;

    .line 32574
    invoke-virtual {v0}, Ljava/util/ArrayList;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_3

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ee;->A00:Ljava/util/ArrayList;

    .line 32575
    invoke-virtual {v0}, Ljava/util/ArrayList;->isEmpty()Z

    move-result v3

    sget-object v2, Lcom/facebook/ads/redexgen/X/Ee;->A0C:[Ljava/lang/String;

    const/4 v0, 0x2

    aget-object v1, v2, v0

    const/4 v0, 0x5

    aget-object v2, v2, v0

    const/16 v0, 0x12

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    invoke-virtual {v2, v0}, Ljava/lang/String;->charAt(I)C

    move-result v0

    if-eq v1, v0, :cond_0

    sget-object v2, Lcom/facebook/ads/redexgen/X/Ee;->A0C:[Ljava/lang/String;

    const-string v1, "PBqWGImhwZdo7IXQpaNFK9QlBHxMp4wk"

    const/4 v0, 0x7

    aput-object v1, v2, v0

    if-eqz v3, :cond_3

    :goto_0
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ee;->A02:Ljava/util/ArrayList;

    .line 32576
    invoke-virtual {v0}, Ljava/util/ArrayList;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_3

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ee;->A05:Ljava/util/ArrayList;

    .line 32577
    invoke-virtual {v0}, Ljava/util/ArrayList;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_3

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ee;->A01:Ljava/util/ArrayList;

    .line 32578
    invoke-virtual {v0}, Ljava/util/ArrayList;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_3

    iget-object v3, p0, Lcom/facebook/ads/redexgen/X/Ee;->A03:Ljava/util/ArrayList;

    sget-object v1, Lcom/facebook/ads/redexgen/X/Ee;->A0C:[Ljava/lang/String;

    const/4 v0, 0x7

    aget-object v1, v1, v0

    const/16 v0, 0x1e

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    const/16 v0, 0x77

    if-eq v1, v0, :cond_2

    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    :cond_0
    if-eqz v3, :cond_3

    goto :goto_0

    .line 32579
    :cond_1
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    :cond_2
    sget-object v2, Lcom/facebook/ads/redexgen/X/Ee;->A0C:[Ljava/lang/String;

    const-string v1, "80rjjAfjuSoUPBYOWBOUrC4mhnJBtDwK"

    const/4 v0, 0x7

    aput-object v1, v2, v0

    invoke-virtual {v3}, Ljava/util/ArrayList;->isEmpty()Z

    move-result v0

    if-nez v0, :cond_4

    :cond_3
    const/4 v0, 0x1

    .line 32580
    :goto_1
    return v0

    .line 32581
    :cond_4
    const/4 v0, 0x0

    goto :goto_1
.end method

.method public final A0M(Lcom/facebook/ads/redexgen/X/56;Ljava/util/List;)Z
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/facebook/ads/redexgen/X/56;",
            "Ljava/util/List<",
            "Ljava/lang/Object;",
            ">;)Z"
        }
    .end annotation

    .line 32582
    .local p2, "payloads":Ljava/util/List;, "Ljava/util/List<Ljava/lang/Object;>;"
    invoke-interface {p2}, Ljava/util/List;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_0

    invoke-super {p0, p1, p2}, Lcom/facebook/ads/redexgen/X/4k;->A0M(Lcom/facebook/ads/redexgen/X/56;Ljava/util/List;)Z

    move-result v0

    if-eqz v0, :cond_1

    :cond_0
    const/4 v0, 0x1

    :goto_0
    return v0

    :cond_1
    const/4 v0, 0x0

    goto :goto_0
.end method

.method public final A0R(Lcom/facebook/ads/redexgen/X/56;)Z
    .locals 2

    .line 32583
    invoke-direct {p0, p1}, Lcom/facebook/ads/redexgen/X/Ee;->A04(Lcom/facebook/ads/redexgen/X/56;)V

    .line 32584
    iget-object v1, p1, Lcom/facebook/ads/redexgen/X/56;->A0H:Landroid/view/View;

    const/4 v0, 0x0

    invoke-virtual {v1, v0}, Landroid/view/View;->setAlpha(F)V

    .line 32585
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ee;->A07:Ljava/util/ArrayList;

    invoke-virtual {v0, p1}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 32586
    const/4 v0, 0x1

    return v0
.end method

.method public final A0S(Lcom/facebook/ads/redexgen/X/56;)Z
    .locals 1

    .line 32587
    invoke-direct {p0, p1}, Lcom/facebook/ads/redexgen/X/Ee;->A04(Lcom/facebook/ads/redexgen/X/56;)V

    .line 32588
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ee;->A0A:Ljava/util/ArrayList;

    invoke-virtual {v0, p1}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 32589
    const/4 v0, 0x1

    return v0
.end method

.method public final A0T(Lcom/facebook/ads/redexgen/X/56;IIII)Z
    .locals 8

    .line 32590
    move-object v3, p1

    move v5, p3

    move v4, p2

    iget-object v2, v3, Lcom/facebook/ads/redexgen/X/56;->A0H:Landroid/view/View;

    .line 32591
    .local v7, "view":Landroid/view/View;
    iget-object v0, v3, Lcom/facebook/ads/redexgen/X/56;->A0H:Landroid/view/View;

    invoke-virtual {v0}, Landroid/view/View;->getTranslationX()F

    move-result v0

    float-to-int v0, v0

    add-int/2addr v4, v0

    .line 32592
    .end local p9
    .local p0, "fromX":I
    iget-object v0, v3, Lcom/facebook/ads/redexgen/X/56;->A0H:Landroid/view/View;

    invoke-virtual {v0}, Landroid/view/View;->getTranslationY()F

    move-result v0

    float-to-int v0, v0

    add-int/2addr v5, v0

    .line 32593
    .end local p10
    .local p1, "fromY":I
    invoke-direct {p0, v3}, Lcom/facebook/ads/redexgen/X/Ee;->A04(Lcom/facebook/ads/redexgen/X/56;)V

    .line 32594
    move v6, p4

    sub-int v0, v6, v4

    .line 32595
    .local p2, "deltaX":I
    move v7, p5

    sub-int v1, v7, v5

    .line 32596
    .local p3, "deltaY":I
    if-nez v0, :cond_0

    if-nez v1, :cond_0

    .line 32597
    invoke-virtual {p0, v3}, Lcom/facebook/ads/redexgen/X/Za;->A0O(Lcom/facebook/ads/redexgen/X/56;)V

    .line 32598
    const/4 v0, 0x0

    return v0

    .line 32599
    :cond_0
    if-eqz v0, :cond_1

    .line 32600
    neg-int v0, v0

    int-to-float v0, v0

    invoke-virtual {v2, v0}, Landroid/view/View;->setTranslationX(F)V

    .line 32601
    :cond_1
    if-eqz v1, :cond_2

    .line 32602
    neg-int v0, v1

    int-to-float v0, v0

    invoke-virtual {v2, v0}, Landroid/view/View;->setTranslationY(F)V

    .line 32603
    :cond_2
    move-object v0, p0

    iget-object v0, v0, Lcom/facebook/ads/redexgen/X/Ee;->A09:Ljava/util/ArrayList;

    new-instance v2, Lcom/facebook/ads/redexgen/X/4M;

    invoke-direct/range {v2 .. v7}, Lcom/facebook/ads/redexgen/X/4M;-><init>(Lcom/facebook/ads/redexgen/X/56;IIII)V

    invoke-virtual {v0, v2}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 32604
    const/4 v0, 0x1

    return v0
.end method

.method public final A0U(Lcom/facebook/ads/redexgen/X/56;Lcom/facebook/ads/redexgen/X/56;IIII)Z
    .locals 13

    .line 32605
    move-object v2, p0

    move-object v7, p1

    move-object v8, p2

    move/from16 v9, p3

    move/from16 v10, p4

    move/from16 v11, p5

    move/from16 v12, p6

    if-ne v7, v8, :cond_0

    .line 32606
    move-object v0, p0

    move-object v1, v7

    move v2, v9

    move v3, v10

    move v4, v11

    move v5, v12

    invoke-virtual/range {v0 .. v5}, Lcom/facebook/ads/redexgen/X/Ee;->A0T(Lcom/facebook/ads/redexgen/X/56;IIII)Z

    move-result v0

    return v0

    .line 32607
    :cond_0
    iget-object v0, v7, Lcom/facebook/ads/redexgen/X/56;->A0H:Landroid/view/View;

    invoke-virtual {v0}, Landroid/view/View;->getTranslationX()F

    move-result v6

    .line 32608
    .local v0, "prevTranslationX":F
    iget-object v0, v7, Lcom/facebook/ads/redexgen/X/56;->A0H:Landroid/view/View;

    invoke-virtual {v0}, Landroid/view/View;->getTranslationY()F

    move-result v5

    .line 32609
    .local v1, "prevTranslationY":F
    iget-object v0, v7, Lcom/facebook/ads/redexgen/X/56;->A0H:Landroid/view/View;

    invoke-virtual {v0}, Landroid/view/View;->getAlpha()F

    move-result v1

    .line 32610
    .local v2, "prevAlpha":F
    invoke-direct {p0, v7}, Lcom/facebook/ads/redexgen/X/Ee;->A04(Lcom/facebook/ads/redexgen/X/56;)V

    .line 32611
    sub-int v0, v11, v9

    int-to-float v0, v0

    sub-float/2addr v0, v6

    float-to-int v4, v0

    .line 32612
    .local v3, "deltaX":I
    sub-int v0, v12, v10

    int-to-float v0, v0

    sub-float/2addr v0, v5

    float-to-int v3, v0

    .line 32613
    .local v4, "deltaY":I
    iget-object v0, v7, Lcom/facebook/ads/redexgen/X/56;->A0H:Landroid/view/View;

    invoke-virtual {v0, v6}, Landroid/view/View;->setTranslationX(F)V

    .line 32614
    iget-object v0, v7, Lcom/facebook/ads/redexgen/X/56;->A0H:Landroid/view/View;

    invoke-virtual {v0, v5}, Landroid/view/View;->setTranslationY(F)V

    .line 32615
    iget-object v0, v7, Lcom/facebook/ads/redexgen/X/56;->A0H:Landroid/view/View;

    invoke-virtual {v0, v1}, Landroid/view/View;->setAlpha(F)V

    .line 32616
    if-eqz v8, :cond_1

    .line 32617
    invoke-direct {v2, v8}, Lcom/facebook/ads/redexgen/X/Ee;->A04(Lcom/facebook/ads/redexgen/X/56;)V

    .line 32618
    iget-object v1, v8, Lcom/facebook/ads/redexgen/X/56;->A0H:Landroid/view/View;

    neg-int v0, v4

    int-to-float v0, v0

    invoke-virtual {v1, v0}, Landroid/view/View;->setTranslationX(F)V

    .line 32619
    iget-object v1, v8, Lcom/facebook/ads/redexgen/X/56;->A0H:Landroid/view/View;

    neg-int v0, v3

    int-to-float v0, v0

    invoke-virtual {v1, v0}, Landroid/view/View;->setTranslationY(F)V

    .line 32620
    iget-object v1, v8, Lcom/facebook/ads/redexgen/X/56;->A0H:Landroid/view/View;

    const/4 v0, 0x0

    invoke-virtual {v1, v0}, Landroid/view/View;->setAlpha(F)V

    .line 32621
    :cond_1
    iget-object v1, v2, Lcom/facebook/ads/redexgen/X/Ee;->A08:Ljava/util/ArrayList;

    new-instance v6, Lcom/facebook/ads/redexgen/X/4L;

    move-object v0, v6

    .end local v0    # "prevTranslationX":F
    .local p3, "prevTranslationX":F
    invoke-direct/range {v6 .. v12}, Lcom/facebook/ads/redexgen/X/4L;-><init>(Lcom/facebook/ads/redexgen/X/56;Lcom/facebook/ads/redexgen/X/56;IIII)V

    invoke-virtual {v1, v0}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 32622
    const/4 v0, 0x1

    return v0
.end method

.method public final A0V()V
    .locals 1

    .line 32623
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Ee;->A0L()Z

    move-result v0

    if-nez v0, :cond_0

    .line 32624
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/4k;->A0A()V

    .line 32625
    :cond_0
    return-void
.end method

.method public final A0W(Lcom/facebook/ads/redexgen/X/4L;)V
    .locals 6

    .line 32626
    iget-object v0, p1, Lcom/facebook/ads/redexgen/X/4L;->A05:Lcom/facebook/ads/redexgen/X/56;

    .line 32627
    .local v0, "holder":Lcom/facebook/ads/redexgen/X/56;
    const/4 v4, 0x0

    if-nez v0, :cond_3

    move-object v3, v4

    .line 32628
    .local v2, "view":Landroid/view/View;
    :goto_0
    iget-object v0, p1, Lcom/facebook/ads/redexgen/X/4L;->A04:Lcom/facebook/ads/redexgen/X/56;

    .line 32629
    .local v3, "newHolder":Lcom/facebook/ads/redexgen/X/56;
    if-eqz v0, :cond_0

    iget-object v4, v0, Lcom/facebook/ads/redexgen/X/56;->A0H:Landroid/view/View;

    .line 32630
    .local v1, "newView":Landroid/view/View;
    :cond_0
    const/4 v2, 0x0

    if-eqz v3, :cond_1

    .line 32631
    invoke-virtual {v3}, Landroid/view/View;->animate()Landroid/view/ViewPropertyAnimator;

    move-result-object v5

    .line 32632
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/4k;->A05()J

    move-result-wide v0

    .line 32633
    invoke-virtual {v5, v0, v1}, Landroid/view/ViewPropertyAnimator;->setDuration(J)Landroid/view/ViewPropertyAnimator;

    move-result-object v5

    .line 32634
    .local v5, "oldViewAnim":Landroid/view/ViewPropertyAnimator;
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/Ee;->A02:Ljava/util/ArrayList;

    iget-object v0, p1, Lcom/facebook/ads/redexgen/X/4L;->A05:Lcom/facebook/ads/redexgen/X/56;

    invoke-virtual {v1, v0}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 32635
    iget v1, p1, Lcom/facebook/ads/redexgen/X/4L;->A02:I

    iget v0, p1, Lcom/facebook/ads/redexgen/X/4L;->A00:I

    sub-int/2addr v1, v0

    int-to-float v0, v1

    invoke-virtual {v5, v0}, Landroid/view/ViewPropertyAnimator;->translationX(F)Landroid/view/ViewPropertyAnimator;

    .line 32636
    iget v1, p1, Lcom/facebook/ads/redexgen/X/4L;->A03:I

    iget v0, p1, Lcom/facebook/ads/redexgen/X/4L;->A01:I

    sub-int/2addr v1, v0

    int-to-float v0, v1

    invoke-virtual {v5, v0}, Landroid/view/ViewPropertyAnimator;->translationY(F)Landroid/view/ViewPropertyAnimator;

    .line 32637
    invoke-virtual {v5, v2}, Landroid/view/ViewPropertyAnimator;->alpha(F)Landroid/view/ViewPropertyAnimator;

    move-result-object v1

    new-instance v0, Lcom/facebook/ads/redexgen/X/4J;

    invoke-direct {v0, p0, p1, v5, v3}, Lcom/facebook/ads/redexgen/X/4J;-><init>(Lcom/facebook/ads/redexgen/X/Ee;Lcom/facebook/ads/redexgen/X/4L;Landroid/view/ViewPropertyAnimator;Landroid/view/View;)V

    invoke-virtual {v1, v0}, Landroid/view/ViewPropertyAnimator;->setListener(Landroid/animation/Animator$AnimatorListener;)Landroid/view/ViewPropertyAnimator;

    move-result-object v0

    .line 32638
    invoke-virtual {v0}, Landroid/view/ViewPropertyAnimator;->start()V

    .line 32639
    .end local v5    # "oldViewAnim":Landroid/view/ViewPropertyAnimator;
    :cond_1
    if-eqz v4, :cond_2

    .line 32640
    invoke-virtual {v4}, Landroid/view/View;->animate()Landroid/view/ViewPropertyAnimator;

    move-result-object v3

    .line 32641
    .local v5, "newViewAnimation":Landroid/view/ViewPropertyAnimator;
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/Ee;->A02:Ljava/util/ArrayList;

    iget-object v0, p1, Lcom/facebook/ads/redexgen/X/4L;->A04:Lcom/facebook/ads/redexgen/X/56;

    invoke-virtual {v1, v0}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 32642
    invoke-virtual {v3, v2}, Landroid/view/ViewPropertyAnimator;->translationX(F)Landroid/view/ViewPropertyAnimator;

    move-result-object v0

    invoke-virtual {v0, v2}, Landroid/view/ViewPropertyAnimator;->translationY(F)Landroid/view/ViewPropertyAnimator;

    move-result-object v2

    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/4k;->A05()J

    move-result-wide v0

    invoke-virtual {v2, v0, v1}, Landroid/view/ViewPropertyAnimator;->setDuration(J)Landroid/view/ViewPropertyAnimator;

    move-result-object v1

    .line 32643
    const/high16 v0, 0x3f800000    # 1.0f

    invoke-virtual {v1, v0}, Landroid/view/ViewPropertyAnimator;->alpha(F)Landroid/view/ViewPropertyAnimator;

    move-result-object v1

    new-instance v0, Lcom/facebook/ads/redexgen/X/4K;

    invoke-direct {v0, p0, p1, v3, v4}, Lcom/facebook/ads/redexgen/X/4K;-><init>(Lcom/facebook/ads/redexgen/X/Ee;Lcom/facebook/ads/redexgen/X/4L;Landroid/view/ViewPropertyAnimator;Landroid/view/View;)V

    invoke-virtual {v1, v0}, Landroid/view/ViewPropertyAnimator;->setListener(Landroid/animation/Animator$AnimatorListener;)Landroid/view/ViewPropertyAnimator;

    move-result-object v0

    .line 32644
    invoke-virtual {v0}, Landroid/view/ViewPropertyAnimator;->start()V

    .line 32645
    .end local v5    # "newViewAnimation":Landroid/view/ViewPropertyAnimator;
    :cond_2
    return-void

    .line 32646
    :cond_3
    iget-object v3, v0, Lcom/facebook/ads/redexgen/X/56;->A0H:Landroid/view/View;

    goto :goto_0
.end method

.method public final A0X(Lcom/facebook/ads/redexgen/X/56;)V
    .locals 5

    .line 32647
    iget-object v4, p1, Lcom/facebook/ads/redexgen/X/56;->A0H:Landroid/view/View;

    .line 32648
    .local v0, "view":Landroid/view/View;
    invoke-virtual {v4}, Landroid/view/View;->animate()Landroid/view/ViewPropertyAnimator;

    move-result-object v3

    .line 32649
    .local v1, "animation":Landroid/view/ViewPropertyAnimator;
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ee;->A00:Ljava/util/ArrayList;

    invoke-virtual {v0, p1}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 32650
    const/high16 v0, 0x3f800000    # 1.0f

    invoke-virtual {v3, v0}, Landroid/view/ViewPropertyAnimator;->alpha(F)Landroid/view/ViewPropertyAnimator;

    move-result-object v2

    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/4k;->A04()J

    move-result-wide v0

    invoke-virtual {v2, v0, v1}, Landroid/view/ViewPropertyAnimator;->setDuration(J)Landroid/view/ViewPropertyAnimator;

    move-result-object v1

    new-instance v0, Lcom/facebook/ads/redexgen/X/4H;

    invoke-direct {v0, p0, p1, v4, v3}, Lcom/facebook/ads/redexgen/X/4H;-><init>(Lcom/facebook/ads/redexgen/X/Ee;Lcom/facebook/ads/redexgen/X/56;Landroid/view/View;Landroid/view/ViewPropertyAnimator;)V

    .line 32651
    invoke-virtual {v1, v0}, Landroid/view/ViewPropertyAnimator;->setListener(Landroid/animation/Animator$AnimatorListener;)Landroid/view/ViewPropertyAnimator;

    move-result-object v0

    .line 32652
    invoke-virtual {v0}, Landroid/view/ViewPropertyAnimator;->start()V

    .line 32653
    return-void
.end method

.method public final A0Y(Lcom/facebook/ads/redexgen/X/56;IIII)V
    .locals 8

    .line 32654
    move-object v3, p1

    move v6, p5

    move v4, p4

    iget-object v5, v3, Lcom/facebook/ads/redexgen/X/56;->A0H:Landroid/view/View;

    .line 32655
    .local p0, "view":Landroid/view/View;
    sub-int/2addr v4, p2

    .line 32656
    .local p1, "deltaX":I
    sub-int/2addr v6, p3

    .line 32657
    .local p2, "deltaY":I
    const/4 v1, 0x0

    if-eqz v4, :cond_0

    .line 32658
    invoke-virtual {v5}, Landroid/view/View;->animate()Landroid/view/ViewPropertyAnimator;

    move-result-object v0

    invoke-virtual {v0, v1}, Landroid/view/ViewPropertyAnimator;->translationX(F)Landroid/view/ViewPropertyAnimator;

    .line 32659
    :cond_0
    if-eqz v6, :cond_1

    .line 32660
    invoke-virtual {v5}, Landroid/view/View;->animate()Landroid/view/ViewPropertyAnimator;

    move-result-object v0

    invoke-virtual {v0, v1}, Landroid/view/ViewPropertyAnimator;->translationY(F)Landroid/view/ViewPropertyAnimator;

    .line 32661
    :cond_1
    invoke-virtual {v5}, Landroid/view/View;->animate()Landroid/view/ViewPropertyAnimator;

    move-result-object v7

    .line 32662
    .local p3, "animation":Landroid/view/ViewPropertyAnimator;
    move-object v0, p0

    iget-object v0, v0, Lcom/facebook/ads/redexgen/X/Ee;->A04:Ljava/util/ArrayList;

    invoke-virtual {v0, v3}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 32663
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/4k;->A06()J

    move-result-wide v0

    invoke-virtual {v7, v0, v1}, Landroid/view/ViewPropertyAnimator;->setDuration(J)Landroid/view/ViewPropertyAnimator;

    move-result-object v0

    new-instance v1, Lcom/facebook/ads/redexgen/X/4I;

    move-object v2, p0

    invoke-direct/range {v1 .. v7}, Lcom/facebook/ads/redexgen/X/4I;-><init>(Lcom/facebook/ads/redexgen/X/Ee;Lcom/facebook/ads/redexgen/X/56;ILandroid/view/View;ILandroid/view/ViewPropertyAnimator;)V

    invoke-virtual {v0, v1}, Landroid/view/ViewPropertyAnimator;->setListener(Landroid/animation/Animator$AnimatorListener;)Landroid/view/ViewPropertyAnimator;

    move-result-object v0

    .line 32664
    invoke-virtual {v0}, Landroid/view/ViewPropertyAnimator;->start()V

    .line 32665
    return-void
.end method
