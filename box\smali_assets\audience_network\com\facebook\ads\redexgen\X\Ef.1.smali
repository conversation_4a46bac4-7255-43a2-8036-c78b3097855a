.class public final Lcom/facebook/ads/redexgen/X/Ef;
.super Lcom/facebook/ads/redexgen/X/Zs;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/facebook/ads/redexgen/X/3u;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "AccessibilityNodeProviderKitKatImpl"
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 32666
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/Zs;-><init>()V

    .line 32667
    return-void
.end method


# virtual methods
.method public final AAW(Lcom/facebook/ads/redexgen/X/3u;)Ljava/lang/Object;
    .locals 1

    .line 32668
    new-instance v0, Lcom/facebook/ads/redexgen/X/Zt;

    invoke-direct {v0, p0, p1}, Lcom/facebook/ads/redexgen/X/Zt;-><init>(Lcom/facebook/ads/redexgen/X/Ef;Lcom/facebook/ads/redexgen/X/3u;)V

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/40;->A00(Lcom/facebook/ads/redexgen/X/3z;)Lcom/facebook/ads/redexgen/X/3y;

    move-result-object v0

    return-object v0
.end method
