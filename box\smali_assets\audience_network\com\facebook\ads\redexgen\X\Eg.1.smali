.class public final Lcom/facebook/ads/redexgen/X/Eg;
.super Lcom/facebook/ads/redexgen/X/Zs;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/facebook/ads/redexgen/X/3u;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "AccessibilityNodeProviderJellyBeanImpl"
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 32669
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/Zs;-><init>()V

    .line 32670
    return-void
.end method


# virtual methods
.method public final AAW(Lcom/facebook/ads/redexgen/X/3u;)Ljava/lang/Object;
    .locals 1

    .line 32671
    new-instance v0, Lcom/facebook/ads/redexgen/X/Zu;

    invoke-direct {v0, p0, p1}, Lcom/facebook/ads/redexgen/X/Zu;-><init>(Lcom/facebook/ads/redexgen/X/Eg;Lcom/facebook/ads/redexgen/X/3u;)V

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/3x;->A00(Lcom/facebook/ads/redexgen/X/3w;)Lcom/facebook/ads/redexgen/X/3v;

    move-result-object v0

    return-object v0
.end method
