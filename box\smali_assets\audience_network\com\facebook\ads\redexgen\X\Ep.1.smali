.class public interface abstract Lcom/facebook/ads/redexgen/X/Ep;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/facebook/ads/redexgen/X/Eq;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "SourceInfoRefreshListener"
.end annotation


# virtual methods
.method public abstract ADA(Lcom/facebook/ads/redexgen/X/Eq;Lcom/facebook/ads/redexgen/X/AH;Ljava/lang/Object;)V
.end method
