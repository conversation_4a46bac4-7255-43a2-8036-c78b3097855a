.class public interface abstract Lcom/facebook/ads/redexgen/X/Eq;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/facebook/ads/redexgen/X/Eo;,
        Lcom/facebook/ads/redexgen/X/Ep;
    }
.end annotation


# virtual methods
.method public abstract A3O(Landroid/os/Handler;Lcom/facebook/ads/redexgen/X/F4;)V
.end method

.method public abstract A4k(Lcom/facebook/ads/redexgen/X/Eo;Lcom/facebook/ads/redexgen/X/Gm;)Lcom/facebook/ads/redexgen/X/WP;
.end method

.method public abstract AAO()V
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation
.end method

.method public abstract AE7(Lcom/facebook/ads/redexgen/X/Y6;ZLcom/facebook/ads/redexgen/X/Ep;)V
.end method

.method public abstract AEa(Lcom/facebook/ads/redexgen/X/WP;)V
.end method

.method public abstract AEb(Lcom/facebook/ads/redexgen/X/Ep;)V
.end method

.method public abstract AF7(Lcom/facebook/ads/redexgen/X/F4;)V
.end method
