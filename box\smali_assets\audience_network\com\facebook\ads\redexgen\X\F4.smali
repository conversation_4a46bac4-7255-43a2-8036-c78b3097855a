.class public interface abstract Lcom/facebook/ads/redexgen/X/F4;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/facebook/ads/redexgen/X/F1;,
        Lcom/facebook/ads/redexgen/X/F3;,
        Lcom/facebook/ads/redexgen/X/F2;
    }
.end annotation


# virtual methods
.method public abstract ABM(ILcom/facebook/ads/redexgen/X/Eo;Lcom/facebook/ads/redexgen/X/F3;)V
.end method

.method public abstract ABy(ILcom/facebook/ads/redexgen/X/Eo;Lcom/facebook/ads/redexgen/X/F2;Lcom/facebook/ads/redexgen/X/F3;)V
.end method

.method public abstract AC0(ILcom/facebook/ads/redexgen/X/Eo;Lcom/facebook/ads/redexgen/X/F2;Lcom/facebook/ads/redexgen/X/F3;)V
.end method

.method public abstract AC3(ILcom/facebook/ads/redexgen/X/Eo;Lcom/facebook/ads/redexgen/X/F2;Lcom/facebook/ads/redexgen/X/F3;Ljava/io/IOException;Z)V
.end method

.method public abstract AC5(ILcom/facebook/ads/redexgen/X/Eo;Lcom/facebook/ads/redexgen/X/F2;Lcom/facebook/ads/redexgen/X/F3;)V
.end method

.method public abstract ACG(ILcom/facebook/ads/redexgen/X/Eo;)V
.end method

.method public abstract ACH(ILcom/facebook/ads/redexgen/X/Eo;)V
.end method

.method public abstract ACp(ILcom/facebook/ads/redexgen/X/Eo;)V
.end method
