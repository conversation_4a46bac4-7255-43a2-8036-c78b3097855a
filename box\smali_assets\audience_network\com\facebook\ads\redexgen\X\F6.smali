.class public final Lcom/facebook/ads/redexgen/X/F6;
.super Lcom/facebook/ads/redexgen/X/a7;
.source ""


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 32914
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/a7;-><init>()V

    return-void
.end method


# virtual methods
.method public final A8r()V
    .locals 1

    .line 32915
    new-instance v0, Lcom/facebook/ads/redexgen/X/aA;

    invoke-direct {v0, p0}, Lcom/facebook/ads/redexgen/X/aA;-><init>(Lcom/facebook/ads/redexgen/X/F6;)V

    sput-object v0, Lcom/facebook/ads/redexgen/X/2j;->A0G:Lcom/facebook/ads/redexgen/X/2i;

    .line 32916
    return-void
.end method
