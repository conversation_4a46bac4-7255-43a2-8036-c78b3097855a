.class public interface abstract Lcom/facebook/ads/redexgen/X/FA;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/facebook/ads/redexgen/X/WO;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "UpstreamFormatChangedListener"
.end annotation


# virtual methods
.method public abstract ADS(Lcom/facebook/ads/internal/exoplayer2/thirdparty/Format;)V
.end method
