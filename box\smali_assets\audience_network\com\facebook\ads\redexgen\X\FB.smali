.class public interface abstract Lcom/facebook/ads/redexgen/X/FB;
.super Ljava/lang/Object;
.source ""


# virtual methods
.method public abstract A9C()Z
.end method

.method public abstract AAM()V
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation
.end method

.method public abstract AEK(Lcom/facebook/ads/redexgen/X/9p;Lcom/facebook/ads/redexgen/X/Xr;Z)I
.end method

.method public abstract AGO(J)I
.end method
