.class public final Lcom/facebook/ads/redexgen/X/Fb;
.super Lcom/facebook/ads/redexgen/X/bK;
.source ""


# direct methods
.method public constructor <init>(Lcom/facebook/ads/redexgen/X/Yn;)V
    .locals 0

    .line 33830
    invoke-direct {p0, p1}, Lcom/facebook/ads/redexgen/X/bK;-><init>(Lcom/facebook/ads/redexgen/X/Yn;)V

    .line 33831
    return-void
.end method


# virtual methods
.method public final A7e()Lcom/facebook/ads/internal/protocol/AdPlacementType;
    .locals 1

    .line 33832
    sget-object v0, Lcom/facebook/ads/internal/protocol/AdPlacementType;->NATIVE_BANNER:Lcom/facebook/ads/internal/protocol/AdPlacementType;

    return-object v0
.end method
