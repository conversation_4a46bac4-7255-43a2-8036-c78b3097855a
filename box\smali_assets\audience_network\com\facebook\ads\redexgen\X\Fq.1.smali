.class public interface abstract Lcom/facebook/ads/redexgen/X/Fq;
.super Ljava/lang/Object;
.source ""


# static fields
.field public static final A00:Lcom/facebook/ads/redexgen/X/Fq;


# direct methods
.method public static constructor <clinit>()V
    .locals 1

    .line 1313
    new-instance v0, Lcom/facebook/ads/redexgen/X/WK;

    invoke-direct {v0}, Lcom/facebook/ads/redexgen/X/WK;-><init>()V

    sput-object v0, Lcom/facebook/ads/redexgen/X/Fq;->A00:Lcom/facebook/ads/redexgen/X/Fq;

    return-void
.end method


# virtual methods
.method public abstract A4Z(Lcom/facebook/ads/internal/exoplayer2/thirdparty/Format;)Lcom/facebook/ads/redexgen/X/WL;
.end method

.method public abstract AGf(Lcom/facebook/ads/internal/exoplayer2/thirdparty/Format;)Z
.end method
