.class public final Lcom/facebook/ads/redexgen/X/Fz;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/facebook/ads/redexgen/X/G7;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "ClutDefinition"
.end annotation


# instance fields
.field public final A00:I

.field public final A01:[I

.field public final A02:[I

.field public final A03:[I


# direct methods
.method public constructor <init>(I[I[I[I)V
    .locals 0

    .line 34486
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 34487
    iput p1, p0, Lcom/facebook/ads/redexgen/X/Fz;->A00:I

    .line 34488
    iput-object p2, p0, Lcom/facebook/ads/redexgen/X/Fz;->A01:[I

    .line 34489
    iput-object p3, p0, Lcom/facebook/ads/redexgen/X/Fz;->A02:[I

    .line 34490
    iput-object p4, p0, Lcom/facebook/ads/redexgen/X/Fz;->A03:[I

    .line 34491
    return-void
.end method
