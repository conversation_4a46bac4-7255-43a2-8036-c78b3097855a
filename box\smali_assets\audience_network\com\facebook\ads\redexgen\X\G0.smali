.class public final Lcom/facebook/ads/redexgen/X/G0;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/facebook/ads/redexgen/X/G7;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "DisplayDefinition"
.end annotation


# instance fields
.field public final A00:I

.field public final A01:I

.field public final A02:I

.field public final A03:I

.field public final A04:I

.field public final A05:I


# direct methods
.method public constructor <init>(IIIIII)V
    .locals 0

    .line 34492
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 34493
    iput p1, p0, Lcom/facebook/ads/redexgen/X/G0;->A05:I

    .line 34494
    iput p2, p0, Lcom/facebook/ads/redexgen/X/G0;->A00:I

    .line 34495
    iput p3, p0, Lcom/facebook/ads/redexgen/X/G0;->A02:I

    .line 34496
    iput p4, p0, Lcom/facebook/ads/redexgen/X/G0;->A01:I

    .line 34497
    iput p5, p0, Lcom/facebook/ads/redexgen/X/G0;->A04:I

    .line 34498
    iput p6, p0, Lcom/facebook/ads/redexgen/X/G0;->A03:I

    .line 34499
    return-void
.end method
