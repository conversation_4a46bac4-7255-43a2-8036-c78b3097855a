.class public final Lcom/facebook/ads/redexgen/X/G5;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/facebook/ads/redexgen/X/G7;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "RegionObject"
.end annotation


# instance fields
.field public final A00:I

.field public final A01:I

.field public final A02:I

.field public final A03:I

.field public final A04:I

.field public final A05:I


# direct methods
.method public constructor <init>(IIIIII)V
    .locals 0

    .line 34536
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 34537
    iput p1, p0, Lcom/facebook/ads/redexgen/X/G5;->A04:I

    .line 34538
    iput p2, p0, Lcom/facebook/ads/redexgen/X/G5;->A03:I

    .line 34539
    iput p3, p0, Lcom/facebook/ads/redexgen/X/G5;->A02:I

    .line 34540
    iput p4, p0, Lcom/facebook/ads/redexgen/X/G5;->A05:I

    .line 34541
    iput p5, p0, Lcom/facebook/ads/redexgen/X/G5;->A01:I

    .line 34542
    iput p6, p0, Lcom/facebook/ads/redexgen/X/G5;->A00:I

    .line 34543
    return-void
.end method
