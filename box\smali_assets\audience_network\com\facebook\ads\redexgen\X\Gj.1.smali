.class public abstract Lcom/facebook/ads/redexgen/X/Gj;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/facebook/ads/redexgen/X/Gi;
    }
.end annotation


# instance fields
.field public A00:Lcom/facebook/ads/redexgen/X/Gi;


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 36274
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final A00(Lcom/facebook/ads/redexgen/X/Gi;)V
    .locals 0

    .line 36275
    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/Gj;->A00:Lcom/facebook/ads/redexgen/X/Gi;

    .line 36276
    return-void
.end method

.method public abstract A0T([Lcom/facebook/ads/redexgen/X/AA;Lcom/facebook/ads/internal/exoplayer2/thirdparty/source/TrackGroupArray;)Lcom/facebook/ads/redexgen/X/Gk;
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lcom/facebook/ads/redexgen/X/9c;
        }
    .end annotation
.end method

.method public abstract A0U(Ljava/lang/Object;)V
.end method
