.class public interface abstract Lcom/facebook/ads/redexgen/X/HB;
.super Ljava/lang/Object;
.source ""

# interfaces
.implements Lcom/facebook/ads/redexgen/X/cP;


# virtual methods
.method public abstract A3V(Lcom/facebook/ads/redexgen/X/ca;Landroid/graphics/Rect;Landroid/graphics/Rect;)V
.end method

.method public abstract A41(JLjava/util/List;)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(J",
            "Ljava/util/List<",
            "Landroid/graphics/Rect;",
            ">;)V"
        }
    .end annotation
.end method

.method public abstract A5Z()V
.end method

.method public abstract AGH(Lcom/facebook/ads/redexgen/X/cX;)V
.end method
