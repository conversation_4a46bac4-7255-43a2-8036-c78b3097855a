.class public interface abstract Lcom/facebook/ads/redexgen/X/HJ;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/facebook/ads/redexgen/X/HK;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "Listener"
.end annotation


# virtual methods
.method public abstract ADB(Lcom/facebook/ads/redexgen/X/HK;Lcom/facebook/ads/redexgen/X/HO;)V
.end method

.method public abstract ADC(Lcom/facebook/ads/redexgen/X/HK;Lcom/facebook/ads/redexgen/X/HO;)V
.end method

.method public abstract ADD(Lcom/facebook/ads/redexgen/X/HK;Lcom/facebook/ads/redexgen/X/HO;Lcom/facebook/ads/redexgen/X/HO;)V
.end method
