.class public interface abstract Lcom/facebook/ads/redexgen/X/HK;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/facebook/ads/redexgen/X/HI;,
        Lcom/facebook/ads/redexgen/X/HJ;
    }
.end annotation


# virtual methods
.method public abstract A3a(Ljava/lang/String;Lcom/facebook/ads/redexgen/X/HW;)V
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lcom/facebook/ads/redexgen/X/HI;
        }
    .end annotation
.end method

.method public abstract A4N(Ljava/io/File;)V
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lcom/facebook/ads/redexgen/X/HI;
        }
    .end annotation
.end method

.method public abstract A6F()J
.end method

.method public abstract A6G(Ljava/lang/String;JJ)J
.end method

.method public abstract A6H(Ljava/lang/String;)Ljava/util/NavigableSet;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            ")",
            "Ljava/util/NavigableSet<",
            "Lcom/facebook/ads/redexgen/X/HO;",
            ">;"
        }
    .end annotation
.end method

.method public abstract A6W(Ljava/lang/String;)J
.end method

.method public abstract A6X(Ljava/lang/String;)Lcom/facebook/ads/redexgen/X/HU;
.end method

.method public abstract AEZ(Lcom/facebook/ads/redexgen/X/HO;)V
.end method

.method public abstract AF9(Lcom/facebook/ads/redexgen/X/HO;)V
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lcom/facebook/ads/redexgen/X/HI;
        }
    .end annotation
.end method

.method public abstract AFx(Ljava/lang/String;J)V
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lcom/facebook/ads/redexgen/X/HI;
        }
    .end annotation
.end method

.method public abstract AGT(Ljava/lang/String;JJ)Ljava/io/File;
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lcom/facebook/ads/redexgen/X/HI;
        }
    .end annotation
.end method

.method public abstract AGV(Ljava/lang/String;J)Lcom/facebook/ads/redexgen/X/HO;
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/InterruptedException;,
            Lcom/facebook/ads/redexgen/X/HI;
        }
    .end annotation
.end method

.method public abstract AGW(Ljava/lang/String;J)Lcom/facebook/ads/redexgen/X/HO;
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lcom/facebook/ads/redexgen/X/HI;
        }
    .end annotation
.end method
