.class public interface abstract Lcom/facebook/ads/redexgen/X/Hd;
.super Ljava/lang/Object;
.source ""


# static fields
.field public static final A00:Lcom/facebook/ads/redexgen/X/Hd;


# direct methods
.method public static constructor <clinit>()V
    .locals 1

    .line 1360
    new-instance v0, Lcom/facebook/ads/redexgen/X/Vd;

    invoke-direct {v0}, Lcom/facebook/ads/redexgen/X/Vd;-><init>()V

    sput-object v0, Lcom/facebook/ads/redexgen/X/Hd;->A00:Lcom/facebook/ads/redexgen/X/Hd;

    return-void
.end method


# virtual methods
.method public abstract A4c(Landroid/os/Looper;Landroid/os/Handler$Callback;)Lcom/facebook/ads/redexgen/X/Vc;
.end method

.method public abstract A5T()J
.end method

.method public abstract AGs()J
.end method
