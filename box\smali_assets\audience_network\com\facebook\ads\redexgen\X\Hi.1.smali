.class public final Lcom/facebook/ads/redexgen/X/Hi;
.super Ljava/lang/RuntimeException;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/facebook/ads/redexgen/X/Hk;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "GlException"
.end annotation


# direct methods
.method public constructor <init>(Ljava/lang/String;)V
    .locals 0

    .line 37132
    invoke-direct {p0, p1}, Ljava/lang/RuntimeException;-><init>(Ljava/lang/String;)V

    .line 37133
    return-void
.end method

.method public synthetic constructor <init>(Ljava/lang/String;Lcom/facebook/ads/redexgen/X/Hh;)V
    .locals 0

    .line 37134
    invoke-direct {p0, p1}, Lcom/facebook/ads/redexgen/X/Hi;-><init>(Ljava/lang/String;)V

    return-void
.end method
