.class public final Lcom/facebook/ads/redexgen/X/Hu;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/facebook/ads/redexgen/X/Hv;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "SpsData"
.end annotation


# instance fields
.field public final A00:F

.field public final A01:I

.field public final A02:I

.field public final A03:I

.field public final A04:I

.field public final A05:I

.field public final A06:I

.field public final A07:Z

.field public final A08:Z

.field public final A09:Z


# direct methods
.method public constructor <init>(IIIFZZIIIZ)V
    .locals 0

    .line 37413
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 37414
    iput p1, p0, Lcom/facebook/ads/redexgen/X/Hu;->A05:I

    .line 37415
    iput p2, p0, Lcom/facebook/ads/redexgen/X/Hu;->A06:I

    .line 37416
    iput p3, p0, Lcom/facebook/ads/redexgen/X/Hu;->A02:I

    .line 37417
    iput p4, p0, Lcom/facebook/ads/redexgen/X/Hu;->A00:F

    .line 37418
    iput-boolean p5, p0, Lcom/facebook/ads/redexgen/X/Hu;->A09:Z

    .line 37419
    iput-boolean p6, p0, Lcom/facebook/ads/redexgen/X/Hu;->A08:Z

    .line 37420
    iput p7, p0, Lcom/facebook/ads/redexgen/X/Hu;->A01:I

    .line 37421
    iput p8, p0, Lcom/facebook/ads/redexgen/X/Hu;->A04:I

    .line 37422
    iput p9, p0, Lcom/facebook/ads/redexgen/X/Hu;->A03:I

    .line 37423
    iput-boolean p10, p0, Lcom/facebook/ads/redexgen/X/Hu;->A07:Z

    .line 37424
    return-void
.end method
