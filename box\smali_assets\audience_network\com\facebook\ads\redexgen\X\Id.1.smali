.class public interface abstract Lcom/facebook/ads/redexgen/X/Id;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/facebook/ads/redexgen/X/Ic;
    }
.end annotation


# virtual methods
.method public abstract ABN(IJ)V
.end method

.method public abstract ACt(Landroid/view/Surface;)V
.end method

.method public abstract ADU(Ljava/lang/String;JJ)V
.end method

.method public abstract ADV(Lcom/facebook/ads/redexgen/X/BC;)V
.end method

.method public abstract ADW(Lcom/facebook/ads/redexgen/X/BC;)V
.end method

.method public abstract ADa(Lcom/facebook/ads/internal/exoplayer2/thirdparty/Format;)V
.end method

.method public abstract ADf(IIIF)V
.end method
