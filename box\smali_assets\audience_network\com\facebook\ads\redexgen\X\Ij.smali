.class public abstract Lcom/facebook/ads/redexgen/X/Ij;
.super Ljava/lang/Object;
.source ""


# static fields
.field public static A00:[B

.field public static A01:[Ljava/lang/String;


# direct methods
.method public static constructor <clinit>()V
    .locals 3

    .line 1556
    const/16 v0, 0x8

    new-array v2, v0, [Ljava/lang/String;

    const/4 v1, 0x0

    const-string v0, "UmMlfAJJysNoFzDVq3Qadmy32zpeb1XF"

    aput-object v0, v2, v1

    const/4 v1, 0x1

    const-string v0, "HllnJbGbN3"

    aput-object v0, v2, v1

    const/4 v1, 0x2

    const-string v0, "8LLGh47zYIGz9mlPEtFaTcZuh"

    aput-object v0, v2, v1

    const/4 v1, 0x3

    const-string v0, "UxHymo9qL"

    aput-object v0, v2, v1

    const/4 v1, 0x4

    const-string v0, "LEtC8FtnVg5ihxRCphfXt4mvpUeUrdN6"

    aput-object v0, v2, v1

    const/4 v1, 0x5

    const-string v0, "uWIYhcHXlNvGJXs3GUOJ1PEuERlV4x7"

    aput-object v0, v2, v1

    const/4 v1, 0x6

    const-string v0, "VZrIuYan0GxbimTZrmDwvEADARuUOj"

    aput-object v0, v2, v1

    const/4 v1, 0x7

    const-string v0, "38Z9W6n0Eu6i9ylO2bMusVthcISfAJzl"

    aput-object v0, v2, v1

    sput-object v2, Lcom/facebook/ads/redexgen/X/Ij;->A01:[Ljava/lang/String;

    invoke-static {}, Lcom/facebook/ads/redexgen/X/Ij;->A0N()V

    return-void
.end method

.method public static A00(Landroid/content/Context;)I
    .locals 3

    .line 39264
    invoke-static {p0}, Lcom/facebook/ads/redexgen/X/Ih;->A0R(Landroid/content/Context;)Lcom/facebook/ads/redexgen/X/Ih;

    move-result-object p0

    .line 39265
    const/16 v2, 0x2ee

    const/16 v1, 0x28

    const/16 v0, 0x64

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Ij;->A0L(III)Ljava/lang/String;

    move-result-object v1

    const/16 v0, 0xa

    invoke-virtual {p0, v1, v0}, Lcom/facebook/ads/redexgen/X/Ih;->A2T(Ljava/lang/String;I)I

    move-result v0

    .line 39266
    return v0
.end method

.method public static A01(Landroid/content/Context;)I
    .locals 3

    .line 39267
    invoke-static {p0}, Lcom/facebook/ads/redexgen/X/Ih;->A0R(Landroid/content/Context;)Lcom/facebook/ads/redexgen/X/Ih;

    move-result-object p0

    .line 39268
    const/16 v2, 0x316

    const/16 v1, 0x32

    const/16 v0, 0xf

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Ij;->A0L(III)Ljava/lang/String;

    move-result-object v1

    const/16 v0, 0x32

    invoke-virtual {p0, v1, v0}, Lcom/facebook/ads/redexgen/X/Ih;->A2T(Ljava/lang/String;I)I

    move-result v0

    .line 39269
    return v0
.end method

.method public static A02(Landroid/content/Context;)I
    .locals 3

    .line 39270
    invoke-static {p0}, Lcom/facebook/ads/redexgen/X/Ih;->A0R(Landroid/content/Context;)Lcom/facebook/ads/redexgen/X/Ih;

    move-result-object p0

    .line 39271
    const/16 v2, 0x95

    const/16 v1, 0x23

    const/16 v0, 0x5b

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Ij;->A0L(III)Ljava/lang/String;

    move-result-object v1

    const/4 v0, 0x1

    invoke-virtual {p0, v1, v0}, Lcom/facebook/ads/redexgen/X/Ih;->A2T(Ljava/lang/String;I)I

    move-result v0

    .line 39272
    return v0
.end method

.method public static A03(Landroid/content/Context;)I
    .locals 3

    .line 39273
    const/16 v2, 0x3b

    const/16 v1, 0x2e

    const/16 v0, 0x5c

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Ij;->A0L(III)Ljava/lang/String;

    move-result-object v1

    const/4 v0, 0x0

    invoke-static {p0, v1, v0}, Lcom/facebook/ads/redexgen/X/Ij;->A0H(Landroid/content/Context;Ljava/lang/String;I)I

    move-result v0

    return v0
.end method

.method public static A04(Landroid/content/Context;)I
    .locals 3

    .line 39274
    const/16 v2, 0x69

    const/16 v1, 0x2c

    const/16 v0, 0x2d

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Ij;->A0L(III)Ljava/lang/String;

    move-result-object v1

    const/4 v0, 0x0

    invoke-static {p0, v1, v0}, Lcom/facebook/ads/redexgen/X/Ij;->A0H(Landroid/content/Context;Ljava/lang/String;I)I

    move-result v0

    return v0
.end method

.method public static A05(Landroid/content/Context;)I
    .locals 3

    .line 39275
    invoke-static {p0}, Lcom/facebook/ads/redexgen/X/Ih;->A0R(Landroid/content/Context;)Lcom/facebook/ads/redexgen/X/Ih;

    move-result-object p0

    .line 39276
    const/16 v2, 0x3fe

    const/16 v1, 0x15

    const/16 v0, 0x13

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Ij;->A0L(III)Ljava/lang/String;

    move-result-object v1

    const/16 v0, 0xa

    invoke-virtual {p0, v1, v0}, Lcom/facebook/ads/redexgen/X/Ih;->A2T(Ljava/lang/String;I)I

    move-result v0

    .line 39277
    return v0
.end method

.method public static A06(Landroid/content/Context;)I
    .locals 3

    .line 39278
    invoke-static {p0}, Lcom/facebook/ads/redexgen/X/Ih;->A0R(Landroid/content/Context;)Lcom/facebook/ads/redexgen/X/Ih;

    move-result-object p0

    .line 39279
    const/16 v2, 0x113

    const/16 v1, 0x2d

    const/16 v0, 0x77

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Ij;->A0L(III)Ljava/lang/String;

    move-result-object v1

    const/4 v0, 0x0

    invoke-virtual {p0, v1, v0}, Lcom/facebook/ads/redexgen/X/Ih;->A2T(Ljava/lang/String;I)I

    move-result v0

    .line 39280
    return v0
.end method

.method public static A07(Landroid/content/Context;)I
    .locals 3

    .line 39281
    invoke-static {p0}, Lcom/facebook/ads/redexgen/X/Ih;->A0R(Landroid/content/Context;)Lcom/facebook/ads/redexgen/X/Ih;

    move-result-object p0

    .line 39282
    const/16 v2, 0x29a

    const/16 v1, 0x24

    const/16 v0, 0x21

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Ij;->A0L(III)Ljava/lang/String;

    move-result-object v1

    const/high16 v0, 0xa00000

    invoke-virtual {p0, v1, v0}, Lcom/facebook/ads/redexgen/X/Ih;->A2T(Ljava/lang/String;I)I

    move-result v0

    .line 39283
    return v0
.end method

.method public static A08(Landroid/content/Context;)I
    .locals 3

    .line 39284
    invoke-static {p0}, Lcom/facebook/ads/redexgen/X/Ih;->A0R(Landroid/content/Context;)Lcom/facebook/ads/redexgen/X/Ih;

    move-result-object p0

    .line 39285
    const/16 v2, 0x2d0

    const/16 v1, 0x1e

    const/16 v0, 0x47

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Ij;->A0L(III)Ljava/lang/String;

    move-result-object v1

    const/16 v0, 0x1e

    invoke-virtual {p0, v1, v0}, Lcom/facebook/ads/redexgen/X/Ih;->A2T(Ljava/lang/String;I)I

    move-result v0

    .line 39286
    return v0
.end method

.method public static A09(Landroid/content/Context;)I
    .locals 3

    .line 39287
    invoke-static {p0}, Lcom/facebook/ads/redexgen/X/Ih;->A0R(Landroid/content/Context;)Lcom/facebook/ads/redexgen/X/Ih;

    move-result-object p0

    .line 39288
    const/16 v2, 0x16e

    const/16 v1, 0x27

    const/16 v0, 0x1a

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Ij;->A0L(III)Ljava/lang/String;

    move-result-object v1

    const/4 v0, 0x5

    invoke-virtual {p0, v1, v0}, Lcom/facebook/ads/redexgen/X/Ih;->A2T(Ljava/lang/String;I)I

    move-result v0

    .line 39289
    return v0
.end method

.method public static A0A(Landroid/content/Context;)I
    .locals 3

    .line 39290
    invoke-static {p0}, Lcom/facebook/ads/redexgen/X/Ih;->A0R(Landroid/content/Context;)Lcom/facebook/ads/redexgen/X/Ih;

    move-result-object p0

    .line 39291
    const/16 v2, 0x195

    const/16 v1, 0x2e

    const/16 v0, 0x63

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Ij;->A0L(III)Ljava/lang/String;

    move-result-object v1

    const/4 v0, 0x0

    invoke-virtual {p0, v1, v0}, Lcom/facebook/ads/redexgen/X/Ih;->A2T(Ljava/lang/String;I)I

    move-result v0

    .line 39292
    return v0
.end method

.method public static A0B(Landroid/content/Context;)I
    .locals 3

    .line 39293
    invoke-static {p0}, Lcom/facebook/ads/redexgen/X/Ih;->A0R(Landroid/content/Context;)Lcom/facebook/ads/redexgen/X/Ih;

    move-result-object p0

    .line 39294
    const/16 v2, 0x1c3

    const/16 v1, 0x2b

    const/16 v0, 0x7b

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Ij;->A0L(III)Ljava/lang/String;

    move-result-object v1

    const/4 v0, 0x0

    invoke-virtual {p0, v1, v0}, Lcom/facebook/ads/redexgen/X/Ih;->A2T(Ljava/lang/String;I)I

    move-result v0

    .line 39295
    return v0
.end method

.method public static A0C(Landroid/content/Context;)I
    .locals 3

    .line 39296
    invoke-static {p0}, Lcom/facebook/ads/redexgen/X/Ih;->A0R(Landroid/content/Context;)Lcom/facebook/ads/redexgen/X/Ih;

    move-result-object p0

    .line 39297
    const/16 v2, 0x1ee

    const/16 v1, 0x29

    const/16 v0, 0x19

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Ij;->A0L(III)Ljava/lang/String;

    move-result-object v1

    const/4 v0, 0x0

    invoke-virtual {p0, v1, v0}, Lcom/facebook/ads/redexgen/X/Ih;->A2T(Ljava/lang/String;I)I

    move-result v0

    .line 39298
    return v0
.end method

.method public static A0D(Landroid/content/Context;)I
    .locals 3

    .line 39299
    invoke-static {p0}, Lcom/facebook/ads/redexgen/X/Ih;->A0R(Landroid/content/Context;)Lcom/facebook/ads/redexgen/X/Ih;

    move-result-object p0

    .line 39300
    const/16 v2, 0x217

    const/16 v1, 0x35

    const/16 v0, 0xb

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Ij;->A0L(III)Ljava/lang/String;

    move-result-object v1

    const/4 v0, 0x0

    invoke-virtual {p0, v1, v0}, Lcom/facebook/ads/redexgen/X/Ih;->A2T(Ljava/lang/String;I)I

    move-result v0

    .line 39301
    return v0
.end method

.method public static A0E(Landroid/content/Context;)I
    .locals 3

    .line 39302
    invoke-static {p0}, Lcom/facebook/ads/redexgen/X/Ih;->A0R(Landroid/content/Context;)Lcom/facebook/ads/redexgen/X/Ih;

    move-result-object p0

    .line 39303
    const/16 v2, 0x24c

    const/16 v1, 0x2f

    const/16 v0, 0x29

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Ij;->A0L(III)Ljava/lang/String;

    move-result-object v1

    const/4 v0, 0x0

    invoke-virtual {p0, v1, v0}, Lcom/facebook/ads/redexgen/X/Ih;->A2T(Ljava/lang/String;I)I

    move-result v0

    .line 39304
    return v0
.end method

.method public static A0F(Landroid/content/Context;)I
    .locals 3

    .line 39305
    invoke-static {p0}, Lcom/facebook/ads/redexgen/X/Ih;->A0R(Landroid/content/Context;)Lcom/facebook/ads/redexgen/X/Ih;

    move-result-object p0

    .line 39306
    const/16 v2, 0x43c

    const/16 v1, 0x1b

    const/16 v0, 0xd

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Ij;->A0L(III)Ljava/lang/String;

    move-result-object v1

    const/16 v0, 0x1e

    invoke-virtual {p0, v1, v0}, Lcom/facebook/ads/redexgen/X/Ih;->A2T(Ljava/lang/String;I)I

    move-result v0

    .line 39307
    return v0
.end method

.method public static A0G(Landroid/content/Context;)I
    .locals 3

    .line 39308
    invoke-static {p0}, Lcom/facebook/ads/redexgen/X/Ih;->A0R(Landroid/content/Context;)Lcom/facebook/ads/redexgen/X/Ih;

    move-result-object p0

    .line 39309
    const/16 v2, 0x479

    const/16 v1, 0x1b

    const/4 v0, 0x1

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Ij;->A0L(III)Ljava/lang/String;

    move-result-object v1

    const/16 v0, 0x14

    invoke-virtual {p0, v1, v0}, Lcom/facebook/ads/redexgen/X/Ih;->A2T(Ljava/lang/String;I)I

    move-result v0

    .line 39310
    return v0
.end method

.method public static A0H(Landroid/content/Context;Ljava/lang/String;I)I
    .locals 0

    .line 39311
    invoke-static {p0}, Lcom/facebook/ads/redexgen/X/Ih;->A0R(Landroid/content/Context;)Lcom/facebook/ads/redexgen/X/Ih;

    move-result-object p0

    invoke-virtual {p0, p1, p2}, Lcom/facebook/ads/redexgen/X/Ih;->A2T(Ljava/lang/String;I)I

    move-result p1

    .line 39312
    .local p0, "value":I
    if-ltz p1, :cond_0

    const/16 p0, 0x65

    if-ge p1, p0, :cond_0

    .line 39313
    return p1

    .line 39314
    :cond_0
    return p2
.end method

.method public static A0I(Landroid/content/Context;)J
    .locals 3

    .line 39315
    invoke-static {p0}, Lcom/facebook/ads/redexgen/X/Ih;->A0R(Landroid/content/Context;)Lcom/facebook/ads/redexgen/X/Ih;

    move-result-object p0

    .line 39316
    const/16 v2, 0x140

    const/16 v1, 0x2e

    const/16 v0, 0x7a

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Ij;->A0L(III)Ljava/lang/String;

    move-result-object v2

    const-wide/16 v0, 0x7d0

    invoke-virtual {p0, v2, v0, v1}, Lcom/facebook/ads/redexgen/X/Ih;->A2U(Ljava/lang/String;J)J

    move-result-wide v0

    .line 39317
    return-wide v0
.end method

.method public static A0J(Landroid/content/Context;)J
    .locals 3

    .line 39318
    invoke-static {p0}, Lcom/facebook/ads/redexgen/X/Ih;->A0R(Landroid/content/Context;)Lcom/facebook/ads/redexgen/X/Ih;

    move-result-object p0

    .line 39319
    const/16 v2, 0x413

    const/16 v1, 0x29

    const/16 v0, 0x42

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Ij;->A0L(III)Ljava/lang/String;

    move-result-object v1

    const/16 v0, 0x12c

    invoke-virtual {p0, v1, v0}, Lcom/facebook/ads/redexgen/X/Ih;->A2T(Ljava/lang/String;I)I

    move-result v0

    int-to-long v2, v0

    const-wide/16 v0, 0x3e8

    mul-long/2addr v2, v0

    .line 39320
    return-wide v2
.end method

.method public static A0K(Landroid/content/Context;)J
    .locals 3

    .line 39321
    invoke-static {p0}, Lcom/facebook/ads/redexgen/X/Ih;->A0R(Landroid/content/Context;)Lcom/facebook/ads/redexgen/X/Ih;

    move-result-object p0

    .line 39322
    const/16 v2, 0x457

    const/16 v1, 0x22

    const/16 v0, 0x66

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Ij;->A0L(III)Ljava/lang/String;

    move-result-object v2

    const-wide/16 v0, 0x1f4

    invoke-virtual {p0, v2, v0, v1}, Lcom/facebook/ads/redexgen/X/Ih;->A2U(Ljava/lang/String;J)J

    move-result-wide v0

    .line 39323
    return-wide v0
.end method

.method public static A0L(III)Ljava/lang/String;
    .locals 4

    sget-object v1, Lcom/facebook/ads/redexgen/X/Ij;->A00:[B

    add-int v0, p0, p1

    invoke-static {v1, p0, v0}, Ljava/util/Arrays;->copyOfRange([BII)[B

    move-result-object p1

    const/4 p0, 0x0

    :goto_0
    array-length v3, p1

    sget-object v1, Lcom/facebook/ads/redexgen/X/Ij;->A01:[Ljava/lang/String;

    const/4 v0, 0x2

    aget-object v0, v1, v0

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v1

    const/16 v0, 0x12

    if-eq v1, v0, :cond_1

    sget-object v2, Lcom/facebook/ads/redexgen/X/Ij;->A01:[Ljava/lang/String;

    const-string v1, "coY8s09zVAK7eBnMiZlV9B8LCQZOBRZX"

    const/4 v0, 0x0

    aput-object v1, v2, v0

    if-ge p0, v3, :cond_0

    aget-byte v0, p1, p0

    xor-int/2addr v0, p2

    xor-int/lit8 v0, v0, 0xa

    int-to-byte v0, v0

    aput-byte v0, p1, p0

    add-int/lit8 p0, p0, 0x1

    goto :goto_0

    :cond_0
    new-instance v0, Ljava/lang/String;

    invoke-direct {v0, p1}, Ljava/lang/String;-><init>([B)V

    return-object v0

    :cond_1
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0
.end method

.method public static A0M(Landroid/content/Context;)Ljava/util/HashMap;
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/content/Context;",
            ")",
            "Ljava/util/HashMap<",
            "Ljava/lang/String;",
            "Ljava/lang/Integer;",
            ">;"
        }
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lorg/json/JSONException;
        }
    .end annotation

    .line 39324
    invoke-static {p0}, Lcom/facebook/ads/redexgen/X/Ih;->A0R(Landroid/content/Context;)Lcom/facebook/ads/redexgen/X/Ih;

    move-result-object v4

    .line 39325
    const/16 v2, 0x18

    const/16 v1, 0x23

    const/16 v0, 0x7b

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Ij;->A0L(III)Ljava/lang/String;

    move-result-object v3

    const/16 v2, 0x16

    const/4 v1, 0x2

    const/16 v0, 0x6d

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Ij;->A0L(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v4, v3, v0}, Lcom/facebook/ads/redexgen/X/Ih;->A2V(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    .line 39326
    .local v0, "blackListString":Ljava/lang/String;
    new-instance v5, Ljava/util/HashMap;

    invoke-direct {v5}, Ljava/util/HashMap;-><init>()V

    .line 39327
    .local v1, "result":Ljava/util/HashMap;, "Ljava/util/HashMap<Ljava/lang/String;Ljava/lang/Integer;>;"
    new-instance v4, Lorg/json/JSONArray;

    invoke-direct {v4, v0}, Lorg/json/JSONArray;-><init>(Ljava/lang/String;)V

    .line 39328
    .local v2, "jsonArray":Lorg/json/JSONArray;
    const/4 v3, 0x0

    .local v3, "i":I
    :goto_0
    invoke-virtual {v4}, Lorg/json/JSONArray;->length()I

    move-result v0

    if-ge v3, v0, :cond_2

    .line 39329
    invoke-virtual {v4, v3}, Lorg/json/JSONArray;->optString(I)Ljava/lang/String;

    move-result-object p0

    .line 39330
    .local v4, "eventConfig":Ljava/lang/String;
    invoke-static {p0}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    if-nez v0, :cond_1

    .line 39331
    const/4 v2, 0x0

    const/4 v1, 0x1

    const/16 v0, 0x45

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Ij;->A0L(III)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p0, v1}, Ljava/lang/String;->contains(Ljava/lang/CharSequence;)Z

    move-result v0

    if-eqz v0, :cond_0

    .line 39332
    invoke-virtual {p0, v1}, Ljava/lang/String;->split(Ljava/lang/String;)[Ljava/lang/String;

    move-result-object v1

    .line 39333
    .local v5, "parts":[Ljava/lang/String;
    const/4 v0, 0x1

    aget-object v0, v1, v0

    .line 39334
    .local p0, "sampling":Ljava/lang/String;
    :try_start_0
    invoke-static {v0}, Ljava/lang/Integer;->parseInt(Ljava/lang/String;)I

    move-result v2
    :try_end_0
    .catch Ljava/lang/NumberFormatException; {:try_start_0 .. :try_end_0} :catch_0

    .line 39335
    .local p1, "samplingInt":I
    const/4 v0, 0x0

    aget-object v1, v1, v0

    invoke-static {v2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v0

    invoke-virtual {v5, v1, v0}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 39336
    .end local v5    # "parts":[Ljava/lang/String;
    .end local p0    # "sampling":Ljava/lang/String;
    .end local p1
    goto :goto_1

    .line 39337
    .end local v5
    .end local p0
    .end local p1
    :cond_0
    const/4 v0, -0x1

    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v0

    invoke-virtual {v5, p0, v0}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 39338
    .end local v4    # "eventConfig":Ljava/lang/String;
    :cond_1
    :goto_1
    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    .line 39339
    .restart local v5    # "parts":[Ljava/lang/String;
    .restart local p0    # "sampling":Ljava/lang/String;
    .local p1, "e":Ljava/lang/NumberFormatException;
    :catch_0
    const/4 v2, 0x1

    const/16 v1, 0x15

    const/16 v0, 0x79

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Ij;->A0L(III)Ljava/lang/String;

    move-result-object v1

    new-instance v0, Lorg/json/JSONException;

    invoke-direct {v0, v1}, Lorg/json/JSONException;-><init>(Ljava/lang/String;)V

    throw v0

    .line 39340
    .end local v3    # "i":I
    :cond_2
    return-object v5
.end method

.method public static A0N()V
    .locals 1

    const/16 v0, 0x494

    new-array v0, v0, [B

    fill-array-data v0, :array_0

    sput-object v0, Lcom/facebook/ads/redexgen/X/Ij;->A00:[B

    return-void

    :array_0
    .array-data 1
        0x71t
        0x30t
        0x12t
        0x1dt
        0x54t
        0x7t
        0x53t
        0x3t
        0x12t
        0x1t
        0x0t
        0x16t
        0x53t
        0x0t
        0x12t
        0x1et
        0x3t
        0x1ft
        0x1at
        0x1dt
        0x14t
        0x5dt
        0x3ct
        0x3at
        0x10t
        0x15t
        0x15t
        0x18t
        0x5t
        0x18t
        0x1et
        0x1ft
        0x10t
        0x1dt
        0x2et
        0x15t
        0x14t
        0x13t
        0x4t
        0x16t
        0x2et
        0x1dt
        0x1et
        0x16t
        0x16t
        0x18t
        0x1ft
        0x16t
        0x2et
        0x13t
        0x1dt
        0x10t
        0x12t
        0x1at
        0x2et
        0x1dt
        0x18t
        0x2t
        0x5t
        0x37t
        0x32t
        0x32t
        0x3ft
        0x22t
        0x3ft
        0x39t
        0x38t
        0x37t
        0x3at
        0x9t
        0x32t
        0x33t
        0x34t
        0x23t
        0x31t
        0x9t
        0x3at
        0x39t
        0x31t
        0x31t
        0x3ft
        0x38t
        0x31t
        0x9t
        0x34t
        0x3at
        0x37t
        0x35t
        0x3dt
        0x9t
        0x3at
        0x3ft
        0x25t
        0x22t
        0x9t
        0x26t
        0x33t
        0x24t
        0x35t
        0x33t
        0x38t
        0x22t
        0x37t
        0x31t
        0x33t
        0x46t
        0x43t
        0x43t
        0x4et
        0x53t
        0x4et
        0x48t
        0x49t
        0x46t
        0x4bt
        0x78t
        0x43t
        0x42t
        0x45t
        0x52t
        0x40t
        0x78t
        0x4bt
        0x48t
        0x40t
        0x40t
        0x4et
        0x49t
        0x40t
        0x78t
        0x54t
        0x46t
        0x4at
        0x57t
        0x4bt
        0x4et
        0x49t
        0x40t
        0x78t
        0x57t
        0x42t
        0x55t
        0x44t
        0x42t
        0x49t
        0x53t
        0x46t
        0x40t
        0x42t
        0x30t
        0x35t
        0x3ft
        0x26t
        0xet
        0x30t
        0x35t
        0x35t
        0x38t
        0x25t
        0x38t
        0x3et
        0x3ft
        0x30t
        0x3dt
        0xet
        0x3dt
        0x3et
        0x36t
        0x36t
        0x38t
        0x3ft
        0x36t
        0xet
        0x23t
        0x34t
        0x25t
        0x23t
        0x28t
        0xet
        0x3dt
        0x38t
        0x3ct
        0x38t
        0x25t
        0x9t
        0xct
        0x6t
        0x1ft
        0x37t
        0x9t
        0xct
        0xct
        0x1t
        0x1ct
        0x1t
        0x7t
        0x6t
        0x9t
        0x4t
        0x37t
        0x4t
        0x7t
        0xft
        0xft
        0x1t
        0x6t
        0xft
        0x37t
        0x1dt
        0x1bt
        0xdt
        0x37t
        0x1ct
        0xdt
        0x5t
        0x18t
        0x37t
        0xet
        0x1t
        0x4t
        0xdt
        0x37t
        0xet
        0x7t
        0x1at
        0x37t
        0x1at
        0xdt
        0x1ft
        0x1at
        0x1t
        0x1ct
        0xdt
        0x50t
        0x55t
        0x5ft
        0x46t
        0x6et
        0x50t
        0x5ft
        0x55t
        0x43t
        0x5et
        0x58t
        0x55t
        0x6et
        0x50t
        0x5dt
        0x46t
        0x50t
        0x48t
        0x42t
        0x6et
        0x5dt
        0x5et
        0x56t
        0x6et
        0x52t
        0x5et
        0x43t
        0x54t
        0x6et
        0x57t
        0x44t
        0x5ft
        0x5ft
        0x54t
        0x5dt
        0x6et
        0x54t
        0x47t
        0x54t
        0x5ft
        0x45t
        0x42t
        0x1ct
        0x19t
        0x13t
        0xat
        0x22t
        0x1ct
        0x13t
        0x19t
        0xft
        0x12t
        0x14t
        0x19t
        0x22t
        0x1et
        0x1ct
        0x1et
        0x15t
        0x18t
        0x22t
        0x19t
        0x18t
        0x1ft
        0x8t
        0x1at
        0x22t
        0x18t
        0xbt
        0x18t
        0x13t
        0x9t
        0xet
        0x22t
        0xet
        0x1ct
        0x10t
        0xdt
        0x11t
        0x14t
        0x13t
        0x1at
        0x22t
        0xft
        0x1ct
        0x9t
        0x18t
        0x11t
        0x14t
        0x1et
        0x7t
        0x2ft
        0x11t
        0x1et
        0x14t
        0x2t
        0x1ft
        0x19t
        0x14t
        0x2ft
        0x14t
        0x19t
        0x3t
        0x0t
        0x11t
        0x4t
        0x13t
        0x18t
        0x15t
        0x2t
        0x2ft
        0x19t
        0x1et
        0x19t
        0x4t
        0x19t
        0x11t
        0x1ct
        0x2ft
        0x2t
        0x15t
        0x4t
        0x2t
        0x9t
        0x2ft
        0x14t
        0x15t
        0x1ct
        0x11t
        0x9t
        0x2ft
        0x1dt
        0x3t
        0x71t
        0x74t
        0x7et
        0x67t
        0x4ft
        0x71t
        0x7et
        0x74t
        0x62t
        0x7ft
        0x79t
        0x74t
        0x4ft
        0x74t
        0x79t
        0x63t
        0x60t
        0x71t
        0x64t
        0x73t
        0x78t
        0x75t
        0x62t
        0x4ft
        0x7dt
        0x71t
        0x68t
        0x4ft
        0x62t
        0x75t
        0x64t
        0x62t
        0x69t
        0x4ft
        0x73t
        0x7ft
        0x65t
        0x7et
        0x64t
        0x8t
        0xdt
        0x7t
        0x1et
        0x36t
        0x8t
        0x7t
        0xdt
        0x1bt
        0x6t
        0x0t
        0xdt
        0x36t
        0xft
        0x1ct
        0x7t
        0x7t
        0xct
        0x5t
        0x36t
        0xdt
        0xct
        0xbt
        0x1ct
        0xet
        0x36t
        0xct
        0x1ft
        0xct
        0x7t
        0x1dt
        0x1at
        0x36t
        0x1at
        0x8t
        0x4t
        0x19t
        0x5t
        0x0t
        0x7t
        0xet
        0x36t
        0x1bt
        0x8t
        0x1dt
        0xct
        0x10t
        0x15t
        0x1ft
        0x6t
        0x2et
        0x10t
        0x1ft
        0x15t
        0x3t
        0x1et
        0x18t
        0x15t
        0x2et
        0x18t
        0x1t
        0x12t
        0x2et
        0x15t
        0x14t
        0x13t
        0x4t
        0x16t
        0x2et
        0x14t
        0x7t
        0x14t
        0x1ft
        0x5t
        0x2t
        0x2et
        0x2t
        0x10t
        0x1ct
        0x1t
        0x1dt
        0x18t
        0x1ft
        0x16t
        0x2et
        0x3t
        0x10t
        0x5t
        0x14t
        0x72t
        0x77t
        0x7dt
        0x64t
        0x4ct
        0x72t
        0x7dt
        0x77t
        0x61t
        0x7ct
        0x7at
        0x77t
        0x4ct
        0x7ft
        0x7ct
        0x70t
        0x72t
        0x7ft
        0x4ct
        0x70t
        0x7ct
        0x66t
        0x7dt
        0x67t
        0x76t
        0x61t
        0x60t
        0x4ct
        0x60t
        0x72t
        0x7et
        0x63t
        0x7ft
        0x7at
        0x7dt
        0x74t
        0x4ct
        0x61t
        0x72t
        0x67t
        0x76t
        0x60t
        0x65t
        0x6ft
        0x76t
        0x5et
        0x60t
        0x6ft
        0x65t
        0x73t
        0x6et
        0x68t
        0x65t
        0x5et
        0x6ft
        0x60t
        0x75t
        0x68t
        0x77t
        0x64t
        0x5et
        0x77t
        0x68t
        0x64t
        0x76t
        0x60t
        0x63t
        0x68t
        0x6dt
        0x68t
        0x75t
        0x78t
        0x5et
        0x69t
        0x68t
        0x72t
        0x75t
        0x6et
        0x73t
        0x78t
        0x5et
        0x72t
        0x60t
        0x6ct
        0x71t
        0x6dt
        0x68t
        0x6ft
        0x66t
        0x5et
        0x73t
        0x60t
        0x75t
        0x64t
        0x42t
        0x47t
        0x4dt
        0x54t
        0x7ct
        0x42t
        0x4dt
        0x47t
        0x51t
        0x4ct
        0x4at
        0x47t
        0x7ct
        0x4dt
        0x46t
        0x57t
        0x54t
        0x4ct
        0x51t
        0x48t
        0x7ct
        0x47t
        0x46t
        0x41t
        0x56t
        0x44t
        0x7ct
        0x46t
        0x55t
        0x46t
        0x4dt
        0x57t
        0x50t
        0x7ct
        0x50t
        0x42t
        0x4et
        0x53t
        0x4ft
        0x4at
        0x4dt
        0x44t
        0x7ct
        0x51t
        0x42t
        0x57t
        0x46t
        0x33t
        0x36t
        0x3ct
        0x25t
        0xdt
        0x33t
        0x3ct
        0x36t
        0x20t
        0x3dt
        0x3bt
        0x36t
        0xdt
        0x27t
        0x21t
        0x37t
        0xdt
        0x3et
        0x3dt
        0x31t
        0x33t
        0x3et
        0xdt
        0x31t
        0x3dt
        0x27t
        0x3ct
        0x26t
        0x37t
        0x20t
        0x21t
        0x4at
        0x4ft
        0x45t
        0x5ct
        0x74t
        0x4ft
        0x4et
        0x49t
        0x5et
        0x4ct
        0x74t
        0x47t
        0x44t
        0x4ct
        0x74t
        0x4dt
        0x42t
        0x47t
        0x4et
        0x74t
        0x58t
        0x42t
        0x51t
        0x4et
        0x74t
        0x47t
        0x42t
        0x46t
        0x42t
        0x5ft
        0x74t
        0x49t
        0x52t
        0x5ft
        0x4et
        0x58t
        0x5et
        0x5bt
        0x51t
        0x48t
        0x60t
        0x5bt
        0x5at
        0x5dt
        0x4at
        0x58t
        0x60t
        0x53t
        0x50t
        0x58t
        0x58t
        0x56t
        0x51t
        0x58t
        0x2ct
        0x29t
        0x23t
        0x3at
        0x12t
        0x29t
        0x28t
        0x2ft
        0x38t
        0x2at
        0x12t
        0x21t
        0x22t
        0x2at
        0x2at
        0x24t
        0x23t
        0x2at
        0x12t
        0x28t
        0x3bt
        0x28t
        0x23t
        0x39t
        0x12t
        0x21t
        0x24t
        0x20t
        0x24t
        0x39t
        0xft
        0xat
        0x0t
        0x19t
        0x31t
        0x2t
        0x1t
        0x9t
        0x31t
        0xft
        0xat
        0xat
        0x7t
        0x1at
        0x7t
        0x1t
        0x0t
        0xft
        0x2t
        0x31t
        0x2t
        0x1t
        0x9t
        0x9t
        0x7t
        0x0t
        0x9t
        0x31t
        0xbt
        0x18t
        0xbt
        0x0t
        0x1at
        0x1dt
        0x31t
        0x2t
        0x7t
        0x3t
        0x7t
        0x1at
        0x64t
        0x61t
        0x6bt
        0x72t
        0x5at
        0x69t
        0x6at
        0x62t
        0x5at
        0x64t
        0x61t
        0x61t
        0x6ct
        0x71t
        0x6ct
        0x6at
        0x6bt
        0x64t
        0x69t
        0x5at
        0x69t
        0x6at
        0x62t
        0x62t
        0x6ct
        0x6bt
        0x62t
        0x5at
        0x6ct
        0x6bt
        0x5at
        0x68t
        0x60t
        0x68t
        0x6at
        0x77t
        0x7ct
        0x5at
        0x60t
        0x73t
        0x60t
        0x6bt
        0x71t
        0x76t
        0x5at
        0x69t
        0x6ct
        0x68t
        0x6ct
        0x71t
        0x42t
        0x47t
        0x4dt
        0x54t
        0x7ct
        0x4ft
        0x4ct
        0x44t
        0x7ct
        0x42t
        0x47t
        0x47t
        0x4at
        0x57t
        0x4at
        0x4ct
        0x4dt
        0x42t
        0x4ft
        0x7ct
        0x4ft
        0x4ct
        0x44t
        0x44t
        0x4at
        0x4dt
        0x44t
        0x7ct
        0x50t
        0x46t
        0x50t
        0x50t
        0x4at
        0x4ct
        0x4dt
        0x7ct
        0x46t
        0x55t
        0x46t
        0x4dt
        0x57t
        0x50t
        0x7ct
        0x42t
        0x4ft
        0x54t
        0x42t
        0x5at
        0x50t
        0x63t
        0x66t
        0x6ct
        0x75t
        0x5dt
        0x71t
        0x67t
        0x6ct
        0x66t
        0x5dt
        0x63t
        0x66t
        0x66t
        0x6bt
        0x76t
        0x6bt
        0x6dt
        0x6ct
        0x63t
        0x6et
        0x5dt
        0x66t
        0x67t
        0x60t
        0x77t
        0x65t
        0x5dt
        0x6et
        0x6dt
        0x65t
        0x65t
        0x6bt
        0x6ct
        0x65t
        0x5dt
        0x71t
        0x76t
        0x63t
        0x61t
        0x69t
        0x76t
        0x70t
        0x63t
        0x61t
        0x67t
        0x7bt
        0x7et
        0x74t
        0x6dt
        0x45t
        0x69t
        0x72t
        0x75t
        0x6ft
        0x76t
        0x7et
        0x45t
        0x6ft
        0x69t
        0x7ft
        0x45t
        0x7ct
        0x73t
        0x76t
        0x7ft
        0x45t
        0x78t
        0x7bt
        0x69t
        0x7ft
        0x7et
        0x45t
        0x68t
        0x7ft
        0x79t
        0x75t
        0x68t
        0x7et
        0x45t
        0x7et
        0x7bt
        0x6et
        0x7bt
        0x78t
        0x7bt
        0x69t
        0x7ft
        0x23t
        0x26t
        0x2ct
        0x35t
        0x1dt
        0x37t
        0x31t
        0x27t
        0x1dt
        0x31t
        0x2at
        0x2dt
        0x30t
        0x36t
        0x1dt
        0x27t
        0x2ct
        0x34t
        0x1dt
        0x26t
        0x23t
        0x36t
        0x23t
        0x1dt
        0x24t
        0x2dt
        0x30t
        0x1dt
        0x23t
        0x26t
        0x26t
        0x2bt
        0x36t
        0x2bt
        0x2dt
        0x2ct
        0x23t
        0x2et
        0x1dt
        0x2et
        0x2dt
        0x25t
        0x25t
        0x2bt
        0x2ct
        0x25t
        0x7bt
        0x7dt
        0x46t
        0x7dt
        0x78t
        0x6dt
        0x78t
        0x46t
        0x75t
        0x76t
        0x7et
        0x7et
        0x70t
        0x77t
        0x7et
        0x46t
        0x75t
        0x70t
        0x74t
        0x70t
        0x6dt
        0x3dt
        0x26t
        0x21t
        0x2et
        0x21t
        0x2dt
        0x2ct
        0x17t
        0x24t
        0x27t
        0x2ft
        0x2ft
        0x21t
        0x26t
        0x2ft
        0x17t
        0x2ct
        0x21t
        0x3bt
        0x38t
        0x29t
        0x3ct
        0x2bt
        0x20t
        0x17t
        0x21t
        0x26t
        0x3ct
        0x2dt
        0x3at
        0x3et
        0x29t
        0x24t
        0x17t
        0x3bt
        0x2dt
        0x2bt
        0x27t
        0x26t
        0x2ct
        0x3bt
        0x72t
        0x69t
        0x6et
        0x61t
        0x6et
        0x62t
        0x63t
        0x58t
        0x6bt
        0x68t
        0x60t
        0x60t
        0x6et
        0x69t
        0x60t
        0x58t
        0x62t
        0x71t
        0x62t
        0x69t
        0x73t
        0x58t
        0x6bt
        0x6et
        0x6at
        0x6et
        0x73t
        0x19t
        0x2t
        0x5t
        0xat
        0x5t
        0x9t
        0x8t
        0x33t
        0x0t
        0x3t
        0xbt
        0xbt
        0x5t
        0x2t
        0xbt
        0x33t
        0x5t
        0x1t
        0x1t
        0x9t
        0x8t
        0x5t
        0xdt
        0x18t
        0x9t
        0x33t
        0x8t
        0x9t
        0x0t
        0xdt
        0x15t
        0x33t
        0x1t
        0x1ft
        0x7et
        0x65t
        0x62t
        0x6dt
        0x62t
        0x6et
        0x6ft
        0x54t
        0x67t
        0x64t
        0x6ct
        0x6ct
        0x62t
        0x65t
        0x6ct
        0x54t
        0x79t
        0x6et
        0x7ft
        0x79t
        0x72t
        0x54t
        0x67t
        0x62t
        0x66t
        0x62t
        0x7ft
    .end array-data
.end method

.method public static A0O(Landroid/content/Context;)Z
    .locals 3

    .line 39341
    invoke-static {p0}, Lcom/facebook/ads/redexgen/X/Ih;->A0R(Landroid/content/Context;)Lcom/facebook/ads/redexgen/X/Ih;

    move-result-object p0

    .line 39342
    const/16 v2, 0xb8

    const/16 v1, 0x31

    const/16 v0, 0x62

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Ij;->A0L(III)Ljava/lang/String;

    move-result-object v1

    const/4 v0, 0x1

    invoke-virtual {p0, v1, v0}, Lcom/facebook/ads/redexgen/X/Ih;->A2Z(Ljava/lang/String;Z)Z

    move-result v0

    .line 39343
    return v0
.end method

.method public static A0P(Landroid/content/Context;)Z
    .locals 3

    .line 39344
    invoke-static {p0}, Lcom/facebook/ads/redexgen/X/Ih;->A0R(Landroid/content/Context;)Lcom/facebook/ads/redexgen/X/Ih;

    move-result-object p0

    .line 39345
    const/16 v2, 0x2be

    const/16 v1, 0x12

    const/16 v0, 0x35

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Ij;->A0L(III)Ljava/lang/String;

    move-result-object v1

    const/4 v0, 0x0

    invoke-virtual {p0, v1, v0}, Lcom/facebook/ads/redexgen/X/Ih;->A2Z(Ljava/lang/String;Z)Z

    move-result v0

    .line 39346
    return v0
.end method

.method public static A0Q(Landroid/content/Context;)Z
    .locals 3

    .line 39347
    invoke-static {p0}, Lcom/facebook/ads/redexgen/X/Ih;->A0R(Landroid/content/Context;)Lcom/facebook/ads/redexgen/X/Ih;

    move-result-object p0

    .line 39348
    const/16 v2, 0x348

    const/16 v1, 0x31

    const/16 v0, 0x29

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Ij;->A0L(III)Ljava/lang/String;

    move-result-object v1

    const/4 v0, 0x1

    invoke-virtual {p0, v1, v0}, Lcom/facebook/ads/redexgen/X/Ih;->A2Z(Ljava/lang/String;Z)Z

    move-result v0

    .line 39349
    return v0
.end method

.method public static A0R(Landroid/content/Context;)Z
    .locals 3

    .line 39350
    invoke-static {p0}, Lcom/facebook/ads/redexgen/X/Ih;->A0R(Landroid/content/Context;)Lcom/facebook/ads/redexgen/X/Ih;

    move-result-object p0

    .line 39351
    const/16 v2, 0x379

    const/16 v1, 0x2d

    const/16 v0, 0x8

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Ij;->A0L(III)Ljava/lang/String;

    move-result-object v1

    const/4 v0, 0x0

    invoke-virtual {p0, v1, v0}, Lcom/facebook/ads/redexgen/X/Ih;->A2Z(Ljava/lang/String;Z)Z

    move-result v0

    .line 39352
    return v0
.end method

.method public static A0S(Landroid/content/Context;)Z
    .locals 3

    .line 39353
    invoke-static {p0}, Lcom/facebook/ads/redexgen/X/Ih;->A0R(Landroid/content/Context;)Lcom/facebook/ads/redexgen/X/Ih;

    move-result-object p0

    .line 39354
    const/16 v2, 0xe9

    const/16 v1, 0x2a

    const/16 v0, 0x3b

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Ij;->A0L(III)Ljava/lang/String;

    move-result-object v1

    const/4 v0, 0x1

    invoke-virtual {p0, v1, v0}, Lcom/facebook/ads/redexgen/X/Ih;->A2Z(Ljava/lang/String;Z)Z

    move-result v0

    .line 39355
    return v0
.end method

.method public static A0T(Landroid/content/Context;)Z
    .locals 3

    .line 39356
    invoke-static {p0}, Lcom/facebook/ads/redexgen/X/Ih;->A0R(Landroid/content/Context;)Lcom/facebook/ads/redexgen/X/Ih;

    move-result-object p0

    .line 39357
    const/16 v2, 0x3a6

    const/16 v1, 0x2a

    const/16 v0, 0x10

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Ij;->A0L(III)Ljava/lang/String;

    move-result-object v1

    const/4 v0, 0x0

    invoke-virtual {p0, v1, v0}, Lcom/facebook/ads/redexgen/X/Ih;->A2Z(Ljava/lang/String;Z)Z

    move-result v0

    .line 39358
    return v0
.end method

.method public static A0U(Landroid/content/Context;)Z
    .locals 3

    .line 39359
    invoke-static {p0}, Lcom/facebook/ads/redexgen/X/Ih;->A0R(Landroid/content/Context;)Lcom/facebook/ads/redexgen/X/Ih;

    move-result-object p0

    .line 39360
    const/16 v2, 0x27b

    const/16 v1, 0x1f

    const/16 v0, 0x58

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Ij;->A0L(III)Ljava/lang/String;

    move-result-object v1

    const/4 v0, 0x0

    invoke-virtual {p0, v1, v0}, Lcom/facebook/ads/redexgen/X/Ih;->A2Z(Ljava/lang/String;Z)Z

    move-result v0

    .line 39361
    return v0
.end method

.method public static A0V(Landroid/content/Context;)Z
    .locals 3

    .line 39362
    invoke-static {p0}, Lcom/facebook/ads/redexgen/X/Ih;->A0R(Landroid/content/Context;)Lcom/facebook/ads/redexgen/X/Ih;

    move-result-object p0

    .line 39363
    const/16 v2, 0x3d0

    const/16 v1, 0x2e

    const/16 v0, 0x48

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Ij;->A0L(III)Ljava/lang/String;

    move-result-object v1

    const/4 v0, 0x1

    invoke-virtual {p0, v1, v0}, Lcom/facebook/ads/redexgen/X/Ih;->A2Z(Ljava/lang/String;Z)Z

    move-result v0

    .line 39364
    return v0
.end method
