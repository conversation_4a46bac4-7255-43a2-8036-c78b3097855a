.class public final enum Lcom/facebook/ads/redexgen/X/Ir;
.super Ljava/lang/Enum;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/facebook/ads/redexgen/X/It;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x4019
    name = "LoggingStatus"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Enum<",
        "Lcom/facebook/ads/redexgen/X/Ir;",
        ">;"
    }
.end annotation


# static fields
.field public static A00:[B

.field public static A01:[Ljava/lang/String;

.field public static final synthetic A02:[Lcom/facebook/ads/redexgen/X/Ir;

.field public static final enum A03:Lcom/facebook/ads/redexgen/X/Ir;

.field public static final enum A04:Lcom/facebook/ads/redexgen/X/Ir;

.field public static final enum A05:Lcom/facebook/ads/redexgen/X/Ir;

.field public static final enum A06:Lcom/facebook/ads/redexgen/X/Ir;

.field public static final enum A07:Lcom/facebook/ads/redexgen/X/Ir;

.field public static final enum A08:Lcom/facebook/ads/redexgen/X/Ir;


# direct methods
.method public static constructor <clinit>()V
    .locals 13

    .line 1566
    const/16 v0, 0x8

    new-array v2, v0, [Ljava/lang/String;

    const/4 v1, 0x0

    const-string v0, "CoeI5qxpy1nzBU2HqPuBP2EjNKXl"

    aput-object v0, v2, v1

    const/4 v1, 0x1

    const-string v0, "2QVULSr2cnp6GdJqlDXRW8b5XXXh3hMX"

    aput-object v0, v2, v1

    const/4 v1, 0x2

    const-string v0, "813w1lpmsq"

    aput-object v0, v2, v1

    const/4 v1, 0x3

    const-string v0, "162t"

    aput-object v0, v2, v1

    const/4 v1, 0x4

    const-string v0, "yQveOSeoJgK3GG"

    aput-object v0, v2, v1

    const/4 v1, 0x5

    const-string v0, "9w5E8CzY6m1n4hygXrseg7VRB7EsZT51"

    aput-object v0, v2, v1

    const/4 v1, 0x6

    const-string v0, "L2DlJsKGhh650sdNsXzHdsi3nonkomMk"

    aput-object v0, v2, v1

    const/4 v1, 0x7

    const-string v0, "9e896LaDKrRwUGEGxpikTCFLA2EDc0Ve"

    aput-object v0, v2, v1

    sput-object v2, Lcom/facebook/ads/redexgen/X/Ir;->A01:[Ljava/lang/String;

    invoke-static {}, Lcom/facebook/ads/redexgen/X/Ir;->A01()V

    const/16 v2, 0x26

    const/16 v1, 0xa

    const/4 v0, 0x0

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Ir;->A00(III)Ljava/lang/String;

    move-result-object v0

    const/4 v12, 0x0

    new-instance v11, Lcom/facebook/ads/redexgen/X/Ir;

    invoke-direct {v11, v0, v12}, Lcom/facebook/ads/redexgen/X/Ir;-><init>(Ljava/lang/String;I)V

    sput-object v11, Lcom/facebook/ads/redexgen/X/Ir;->A07:Lcom/facebook/ads/redexgen/X/Ir;

    .line 1567
    const/16 v2, 0x30

    const/4 v1, 0x7

    const/16 v0, 0x11

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Ir;->A00(III)Ljava/lang/String;

    move-result-object v0

    const/4 v10, 0x1

    new-instance v9, Lcom/facebook/ads/redexgen/X/Ir;

    invoke-direct {v9, v0, v10}, Lcom/facebook/ads/redexgen/X/Ir;-><init>(Ljava/lang/String;I)V

    sput-object v9, Lcom/facebook/ads/redexgen/X/Ir;->A08:Lcom/facebook/ads/redexgen/X/Ir;

    .line 1568
    const/16 v2, 0xc

    const/4 v1, 0x7

    const/16 v0, 0x27

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Ir;->A00(III)Ljava/lang/String;

    move-result-object v0

    const/4 v8, 0x2

    new-instance v7, Lcom/facebook/ads/redexgen/X/Ir;

    invoke-direct {v7, v0, v8}, Lcom/facebook/ads/redexgen/X/Ir;-><init>(Ljava/lang/String;I)V

    sput-object v7, Lcom/facebook/ads/redexgen/X/Ir;->A05:Lcom/facebook/ads/redexgen/X/Ir;

    .line 1569
    const/16 v2, 0x13

    const/16 v1, 0x13

    const/16 v0, 0x18

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Ir;->A00(III)Ljava/lang/String;

    move-result-object v0

    const/4 v6, 0x3

    new-instance v5, Lcom/facebook/ads/redexgen/X/Ir;

    invoke-direct {v5, v0, v6}, Lcom/facebook/ads/redexgen/X/Ir;-><init>(Ljava/lang/String;I)V

    sput-object v5, Lcom/facebook/ads/redexgen/X/Ir;->A06:Lcom/facebook/ads/redexgen/X/Ir;

    .line 1570
    const/4 v2, 0x6

    const/4 v1, 0x6

    const/16 v0, 0x33

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Ir;->A00(III)Ljava/lang/String;

    move-result-object v0

    const/4 v4, 0x4

    new-instance v3, Lcom/facebook/ads/redexgen/X/Ir;

    invoke-direct {v3, v0, v4}, Lcom/facebook/ads/redexgen/X/Ir;-><init>(Ljava/lang/String;I)V

    sput-object v3, Lcom/facebook/ads/redexgen/X/Ir;->A04:Lcom/facebook/ads/redexgen/X/Ir;

    .line 1571
    const/4 v2, 0x0

    const/4 v1, 0x6

    const/16 v0, 0x64

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Ir;->A00(III)Ljava/lang/String;

    move-result-object v0

    const/4 v2, 0x5

    new-instance v1, Lcom/facebook/ads/redexgen/X/Ir;

    invoke-direct {v1, v0, v2}, Lcom/facebook/ads/redexgen/X/Ir;-><init>(Ljava/lang/String;I)V

    sput-object v1, Lcom/facebook/ads/redexgen/X/Ir;->A03:Lcom/facebook/ads/redexgen/X/Ir;

    .line 1572
    const/4 v0, 0x6

    new-array v0, v0, [Lcom/facebook/ads/redexgen/X/Ir;

    aput-object v11, v0, v12

    aput-object v9, v0, v10

    aput-object v7, v0, v8

    aput-object v5, v0, v6

    aput-object v3, v0, v4

    aput-object v1, v0, v2

    sput-object v0, Lcom/facebook/ads/redexgen/X/Ir;->A02:[Lcom/facebook/ads/redexgen/X/Ir;

    return-void
.end method

.method public constructor <init>(Ljava/lang/String;I)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation

    .line 39437
    invoke-direct {p0, p1, p2}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    return-void
.end method

.method public static A00(III)Ljava/lang/String;
    .locals 2

    sget-object v1, Lcom/facebook/ads/redexgen/X/Ir;->A00:[B

    add-int v0, p0, p1

    invoke-static {v1, p0, v0}, Ljava/util/Arrays;->copyOfRange([BII)[B

    move-result-object p0

    const/4 v1, 0x0

    :goto_0
    array-length v0, p0

    if-ge v1, v0, :cond_0

    aget-byte v0, p0, v1

    sub-int/2addr v0, p2

    add-int/lit8 v0, v0, -0x56

    int-to-byte v0, v0

    aput-byte v0, p0, v1

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_0
    new-instance v0, Ljava/lang/String;

    invoke-direct {v0, p0}, Ljava/lang/String;-><init>([B)V

    return-object v0
.end method

.method public static A01()V
    .locals 3

    const/16 v0, 0x37

    new-array v0, v0, [B

    fill-array-data v0, :array_0

    sput-object v0, Lcom/facebook/ads/redexgen/X/Ir;->A00:[B

    sget-object v1, Lcom/facebook/ads/redexgen/X/Ir;->A01:[Ljava/lang/String;

    const/4 v0, 0x2

    aget-object v0, v1, v0

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v1

    const/4 v0, 0x4

    if-eq v1, v0, :cond_0

    sget-object v2, Lcom/facebook/ads/redexgen/X/Ir;->A01:[Ljava/lang/String;

    const-string v1, "Y6chWCHiEHN0DdaKB8VVwvMQijKJ1yIC"

    const/4 v0, 0x1

    aput-object v1, v2, v0

    const-string v1, "jbJMvCo49VWYI4oJAoDoWItgvmTGViMF"

    const/4 v0, 0x6

    aput-object v1, v2, v0

    return-void

    :cond_0
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    :array_0
    .array-data 1
        0x0t
        -0x5t
        0x3t
        0x6t
        -0x1t
        -0x2t
        -0x2bt
        -0x28t
        -0x30t
        -0x30t
        -0x32t
        -0x33t
        -0x37t
        -0x34t
        -0x3ct
        -0x3ct
        -0x3at
        -0x35t
        -0x3ct
        -0x46t
        -0x43t
        -0x4bt
        -0x4bt
        -0x49t
        -0x44t
        -0x4bt
        -0x33t
        -0x46t
        -0x4dt
        -0x3ft
        -0x3ft
        -0x33t
        -0x3ft
        -0x3et
        -0x40t
        -0x49t
        -0x4ft
        -0x3et
        -0x5ct
        -0x5bt
        -0x56t
        -0x4bt
        -0x5et
        -0x5bt
        -0x63t
        -0x63t
        -0x65t
        -0x66t
        -0x49t
        -0x54t
        -0x4bt
        -0x55t
        -0x50t
        -0x4bt
        -0x52t
    .end array-data
.end method

.method public static valueOf(Ljava/lang/String;)Lcom/facebook/ads/redexgen/X/Ir;
    .locals 1

    .line 39438
    const-class v0, Lcom/facebook/ads/redexgen/X/Ir;

    invoke-static {v0, p0}, Ljava/lang/Enum;->valueOf(Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Enum;

    move-result-object v0

    check-cast v0, Lcom/facebook/ads/redexgen/X/Ir;

    return-object v0
.end method

.method public static values()[Lcom/facebook/ads/redexgen/X/Ir;
    .locals 1

    .line 39439
    sget-object v0, Lcom/facebook/ads/redexgen/X/Ir;->A02:[Lcom/facebook/ads/redexgen/X/Ir;

    invoke-virtual {v0}, [Ljava/lang/Object;->clone()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [Lcom/facebook/ads/redexgen/X/Ir;

    return-object v0
.end method
