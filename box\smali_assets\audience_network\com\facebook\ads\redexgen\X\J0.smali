.class public interface abstract Lcom/facebook/ads/redexgen/X/J0;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/facebook/ads/redexgen/X/J1;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "DispatchCallback"
.end annotation


# virtual methods
.method public abstract A4a()Lorg/json/JSONObject;
.end method

.method public abstract A9A()Z
.end method

.method public abstract AAi()V
.end method

.method public abstract ABI(Lorg/json/JSONArray;)V
.end method

.method public abstract ABJ(Lorg/json/JSONArray;)Z
.end method

.method public abstract ACo()V
.end method
