.class public abstract Lcom/facebook/ads/redexgen/X/J7;
.super Ljava/lang/Object;
.source ""


# direct methods
.method public static A00(Lcom/facebook/ads/redexgen/X/Ym;Lcom/facebook/ads/redexgen/X/8w;)Lcom/facebook/ads/redexgen/X/YC;
    .locals 1

    .line 39690
    check-cast p1, Lcom/facebook/ads/redexgen/X/YD;

    new-instance v0, Lcom/facebook/ads/redexgen/X/YC;

    invoke-direct {v0, p0, p1}, Lcom/facebook/ads/redexgen/X/YC;-><init>(Lcom/facebook/ads/redexgen/X/Ym;Lcom/facebook/ads/redexgen/X/YD;)V

    return-object v0
.end method

.method public static A01(Lcom/facebook/ads/redexgen/X/Ym;Lcom/facebook/ads/redexgen/X/YH;)Lcom/facebook/ads/redexgen/X/VP;
    .locals 1

    .line 39691
    new-instance v0, Lcom/facebook/ads/redexgen/X/VP;

    invoke-direct {v0, p0, p1}, Lcom/facebook/ads/redexgen/X/VP;-><init>(Lcom/facebook/ads/redexgen/X/Ym;Lcom/facebook/ads/redexgen/X/YH;)V

    return-object v0
.end method
