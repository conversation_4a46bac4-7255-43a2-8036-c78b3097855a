.class public interface abstract Lcom/facebook/ads/redexgen/X/JK;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/facebook/ads/redexgen/X/VE;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "PlaceHolderImageDownloadListener"
.end annotation


# virtual methods
.method public abstract ACX(Z)V
.end method
