.class public interface abstract Lcom/facebook/ads/redexgen/X/JO;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/facebook/ads/redexgen/X/V2;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "ViewTraversalPredicate"
.end annotation


# virtual methods
.method public abstract AGK(Landroid/view/View;)Z
.end method
