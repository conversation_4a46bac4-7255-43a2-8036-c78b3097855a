.class public final enum Lcom/facebook/ads/redexgen/X/JS;
.super Ljava/lang/Enum;
.source ""


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Enum<",
        "Lcom/facebook/ads/redexgen/X/JS;",
        ">;"
    }
.end annotation


# static fields
.field public static A04:[B

.field public static final synthetic A05:[Lcom/facebook/ads/redexgen/X/JS;

.field public static final enum A06:Lcom/facebook/ads/redexgen/X/JS;

.field public static final enum A07:Lcom/facebook/ads/redexgen/X/JS;

.field public static final enum A08:Lcom/facebook/ads/redexgen/X/JS;
    .annotation runtime Ljava/lang/Deprecated;
    .end annotation
.end field

.field public static final enum A09:Lcom/facebook/ads/redexgen/X/JS;
    .annotation runtime Ljava/lang/Deprecated;
    .end annotation
.end field

.field public static final enum A0A:Lcom/facebook/ads/redexgen/X/JS;

.field public static final enum A0B:Lcom/facebook/ads/redexgen/X/JS;


# instance fields
.field public final A00:I

.field public final A01:I

.field public final A02:I

.field public final A03:I


# direct methods
.method public static constructor <clinit>()V
    .locals 27

    .line 1680
    invoke-static {}, Lcom/facebook/ads/redexgen/X/JS;->A02()V

    new-instance v3, Lcom/facebook/ads/redexgen/X/JS;

    const/4 v2, 0x0

    const/16 v1, 0xa

    const/16 v0, 0x11

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/JS;->A01(III)Ljava/lang/String;

    move-result-object v4

    const/4 v5, 0x0

    const/4 v6, -0x1

    const/16 v7, 0x64

    const/4 v8, 0x1

    const/4 v9, 0x0

    invoke-direct/range {v3 .. v9}, Lcom/facebook/ads/redexgen/X/JS;-><init>(Ljava/lang/String;IIIII)V

    sput-object v3, Lcom/facebook/ads/redexgen/X/JS;->A06:Lcom/facebook/ads/redexgen/X/JS;

    .line 1681
    new-instance v10, Lcom/facebook/ads/redexgen/X/JS;

    const/16 v2, 0xa

    const/16 v1, 0xa

    const/16 v0, 0x40

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/JS;->A01(III)Ljava/lang/String;

    move-result-object v11

    const/4 v12, 0x1

    const/4 v13, -0x1

    const/16 v14, 0x78

    const/4 v15, 0x2

    const/16 v16, 0x1

    invoke-direct/range {v10 .. v16}, Lcom/facebook/ads/redexgen/X/JS;-><init>(Ljava/lang/String;IIIII)V

    sput-object v10, Lcom/facebook/ads/redexgen/X/JS;->A07:Lcom/facebook/ads/redexgen/X/JS;

    .line 1682
    new-instance v14, Lcom/facebook/ads/redexgen/X/JS;

    const/16 v2, 0x14

    const/16 v1, 0xa

    const/16 v0, 0x3e

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/JS;->A01(III)Ljava/lang/String;

    move-result-object v15

    const/16 v16, 0x2

    const/16 v17, -0x1

    const/16 v18, 0x12c

    const/16 v19, 0x3

    const/16 v20, 0x2

    invoke-direct/range {v14 .. v20}, Lcom/facebook/ads/redexgen/X/JS;-><init>(Ljava/lang/String;IIIII)V

    sput-object v14, Lcom/facebook/ads/redexgen/X/JS;->A08:Lcom/facebook/ads/redexgen/X/JS;

    .line 1683
    new-instance v18, Lcom/facebook/ads/redexgen/X/JS;

    const/16 v2, 0x1e

    const/16 v1, 0xa

    const/16 v0, 0x50

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/JS;->A01(III)Ljava/lang/String;

    move-result-object v19

    const/16 v20, 0x3

    const/16 v22, 0x190

    const/16 v23, 0x4

    const/16 v24, 0x3

    move/from16 v21, v13

    invoke-direct/range {v18 .. v24}, Lcom/facebook/ads/redexgen/X/JS;-><init>(Ljava/lang/String;IIIII)V

    sput-object v18, Lcom/facebook/ads/redexgen/X/JS;->A09:Lcom/facebook/ads/redexgen/X/JS;

    .line 1684
    new-instance v19, Lcom/facebook/ads/redexgen/X/JS;

    const/16 v2, 0x28

    const/16 v1, 0x9

    const/4 v0, 0x6

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/JS;->A01(III)Ljava/lang/String;

    move-result-object v20

    const/16 v21, 0x4

    const/16 v23, 0x32

    const/16 v24, 0x5

    const/16 v25, 0x4

    move/from16 v22, v17

    invoke-direct/range {v19 .. v25}, Lcom/facebook/ads/redexgen/X/JS;-><init>(Ljava/lang/String;IIIII)V

    sput-object v19, Lcom/facebook/ads/redexgen/X/JS;->A0A:Lcom/facebook/ads/redexgen/X/JS;

    .line 1685
    new-instance v20, Lcom/facebook/ads/redexgen/X/JS;

    const/16 v2, 0x31

    const/16 v1, 0xc

    const/16 v0, 0x2d

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/JS;->A01(III)Ljava/lang/String;

    move-result-object v21

    const/16 v22, 0x5

    const/16 v24, -0x1

    const/16 v25, 0x6

    const/16 v26, 0x5

    move/from16 v23, v13

    invoke-direct/range {v20 .. v26}, Lcom/facebook/ads/redexgen/X/JS;-><init>(Ljava/lang/String;IIIII)V

    sput-object v20, Lcom/facebook/ads/redexgen/X/JS;->A0B:Lcom/facebook/ads/redexgen/X/JS;

    .line 1686
    const/4 v0, 0x6

    new-array v1, v0, [Lcom/facebook/ads/redexgen/X/JS;

    aput-object v3, v1, v9

    const/4 v0, 0x1

    aput-object v10, v1, v0

    const/4 v0, 0x2

    aput-object v14, v1, v0

    const/4 v0, 0x3

    aput-object v18, v1, v0

    const/4 v0, 0x4

    aput-object v19, v1, v0

    const/4 v0, 0x5

    aput-object v20, v1, v0

    sput-object v1, Lcom/facebook/ads/redexgen/X/JS;->A05:[Lcom/facebook/ads/redexgen/X/JS;

    return-void
.end method

.method public constructor <init>(Ljava/lang/String;IIIII)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(IIII)V"
        }
    .end annotation

    .line 39994
    invoke-direct {p0, p1, p2}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    .line 39995
    iput p3, p0, Lcom/facebook/ads/redexgen/X/JS;->A03:I

    .line 39996
    iput p4, p0, Lcom/facebook/ads/redexgen/X/JS;->A01:I

    .line 39997
    iput p5, p0, Lcom/facebook/ads/redexgen/X/JS;->A02:I

    .line 39998
    iput p6, p0, Lcom/facebook/ads/redexgen/X/JS;->A00:I

    .line 39999
    return-void
.end method

.method public static A00(I)Lcom/facebook/ads/redexgen/X/JS;
    .locals 5

    .line 40000
    invoke-static {}, Lcom/facebook/ads/redexgen/X/JS;->values()[Lcom/facebook/ads/redexgen/X/JS;

    move-result-object v4

    array-length v3, v4

    const/4 v2, 0x0

    :goto_0
    if-ge v2, v3, :cond_1

    aget-object v1, v4, v2

    .line 40001
    .local v3, "value":Lcom/facebook/ads/redexgen/X/JS;
    iget v0, v1, Lcom/facebook/ads/redexgen/X/JS;->A00:I

    if-ne v0, p0, :cond_0

    .line 40002
    return-object v1

    .line 40003
    .end local v3    # "value":Lcom/facebook/ads/redexgen/X/JS;
    :cond_0
    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    .line 40004
    :cond_1
    const/4 v0, 0x0

    return-object v0
.end method

.method public static A01(III)Ljava/lang/String;
    .locals 2

    sget-object v1, Lcom/facebook/ads/redexgen/X/JS;->A04:[B

    add-int v0, p0, p1

    invoke-static {v1, p0, v0}, Ljava/util/Arrays;->copyOfRange([BII)[B

    move-result-object p0

    const/4 v1, 0x0

    :goto_0
    array-length v0, p0

    if-ge v1, v0, :cond_0

    aget-byte v0, p0, v1

    sub-int/2addr v0, p2

    add-int/lit8 v0, v0, -0x39

    int-to-byte v0, v0

    aput-byte v0, p0, v1

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_0
    new-instance v0, Ljava/lang/String;

    invoke-direct {v0, p0}, Ljava/lang/String;-><init>([B)V

    return-object v0
.end method

.method public static A02()V
    .locals 1

    const/16 v0, 0x3d

    new-array v0, v0, [B

    fill-array-data v0, :array_0

    sput-object v0, Lcom/facebook/ads/redexgen/X/JS;->A04:[B

    return-void

    :array_0
    .array-data 1
        -0x6et
        -0x71t
        -0x6dt
        -0x6ft
        -0x6et
        -0x62t
        -0x57t
        0x7bt
        0x7at
        0x7at
        -0x3ft
        -0x42t
        -0x3et
        -0x40t
        -0x3ft
        -0x33t
        -0x28t
        -0x56t
        -0x55t
        -0x57t
        -0x41t
        -0x44t
        -0x40t
        -0x42t
        -0x41t
        -0x35t
        -0x2at
        -0x56t
        -0x59t
        -0x59t
        -0x2ft
        -0x32t
        -0x2et
        -0x30t
        -0x2ft
        -0x23t
        -0x18t
        -0x43t
        -0x47t
        -0x47t
        -0x79t
        -0x7ct
        -0x78t
        -0x7at
        -0x79t
        -0x6dt
        -0x62t
        0x74t
        0x6ft
        -0x48t
        -0x55t
        -0x57t
        -0x46t
        -0x3bt
        -0x56t
        -0x41t
        -0x4ct
        -0x59t
        -0x4dt
        -0x51t
        -0x57t
    .end array-data
.end method

.method public static valueOf(Ljava/lang/String;)Lcom/facebook/ads/redexgen/X/JS;
    .locals 1

    .line 40008
    const-class v0, Lcom/facebook/ads/redexgen/X/JS;

    invoke-static {v0, p0}, Ljava/lang/Enum;->valueOf(Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Enum;

    move-result-object v0

    check-cast v0, Lcom/facebook/ads/redexgen/X/JS;

    return-object v0
.end method

.method public static values()[Lcom/facebook/ads/redexgen/X/JS;
    .locals 1

    .line 40009
    sget-object v0, Lcom/facebook/ads/redexgen/X/JS;->A05:[Lcom/facebook/ads/redexgen/X/JS;

    invoke-virtual {v0}, [Ljava/lang/Object;->clone()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [Lcom/facebook/ads/redexgen/X/JS;

    return-object v0
.end method


# virtual methods
.method public final A03()I
    .locals 1

    .line 40005
    iget v0, p0, Lcom/facebook/ads/redexgen/X/JS;->A01:I

    return v0
.end method

.method public final A04()I
    .locals 1

    .line 40006
    iget v0, p0, Lcom/facebook/ads/redexgen/X/JS;->A02:I

    return v0
.end method

.method public final A05()I
    .locals 1

    .line 40007
    iget v0, p0, Lcom/facebook/ads/redexgen/X/JS;->A03:I

    return v0
.end method
