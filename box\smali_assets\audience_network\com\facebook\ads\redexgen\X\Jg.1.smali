.class public final enum Lcom/facebook/ads/redexgen/X/Jg;
.super Ljava/lang/Enum;
.source ""


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Enum<",
        "Lcom/facebook/ads/redexgen/X/Jg;",
        ">;"
    }
.end annotation


# static fields
.field public static A01:[B

.field public static final synthetic A02:[Lcom/facebook/ads/redexgen/X/Jg;

.field public static final enum A03:Lcom/facebook/ads/redexgen/X/Jg;
    .annotation runtime Ljava/lang/Deprecated;
    .end annotation
.end field

.field public static final enum A04:Lcom/facebook/ads/redexgen/X/Jg;

.field public static final enum A05:Lcom/facebook/ads/redexgen/X/Jg;

.field public static final enum A06:Lcom/facebook/ads/redexgen/X/Jg;

.field public static final enum A07:Lcom/facebook/ads/redexgen/X/Jg;

.field public static final enum A08:Lcom/facebook/ads/redexgen/X/Jg;

.field public static final enum A09:Lcom/facebook/ads/redexgen/X/Jg;

.field public static final enum A0A:Lcom/facebook/ads/redexgen/X/Jg;

.field public static final enum A0B:Lcom/facebook/ads/redexgen/X/Jg;

.field public static final enum A0C:Lcom/facebook/ads/redexgen/X/Jg;

.field public static final enum A0D:Lcom/facebook/ads/redexgen/X/Jg;

.field public static final enum A0E:Lcom/facebook/ads/redexgen/X/Jg;

.field public static final enum A0F:Lcom/facebook/ads/redexgen/X/Jg;

.field public static final enum A0G:Lcom/facebook/ads/redexgen/X/Jg;

.field public static final enum A0H:Lcom/facebook/ads/redexgen/X/Jg;

.field public static final enum A0I:Lcom/facebook/ads/redexgen/X/Jg;

.field public static final enum A0J:Lcom/facebook/ads/redexgen/X/Jg;


# instance fields
.field public final A00:I


# direct methods
.method public static constructor <clinit>()V
    .locals 21

    .line 1748
    invoke-static {}, Lcom/facebook/ads/redexgen/X/Jg;->A02()V

    const/16 v2, 0x33

    const/4 v1, 0x7

    const/16 v0, 0x3b

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Jg;->A01(III)Ljava/lang/String;

    move-result-object v2

    const/4 v1, 0x0

    new-instance v20, Lcom/facebook/ads/redexgen/X/Jg;

    move-object/from16 v0, v20

    invoke-direct {v0, v2, v1, v1}, Lcom/facebook/ads/redexgen/X/Jg;-><init>(Ljava/lang/String;II)V

    sput-object v20, Lcom/facebook/ads/redexgen/X/Jg;->A07:Lcom/facebook/ads/redexgen/X/Jg;

    .line 1749
    const/16 v2, 0xc2

    const/16 v1, 0x15

    const/16 v0, 0x1d

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Jg;->A01(III)Ljava/lang/String;

    move-result-object v2

    const/4 v1, 0x1

    const/4 v6, 0x4

    new-instance v19, Lcom/facebook/ads/redexgen/X/Jg;

    move-object/from16 v0, v19

    invoke-direct {v0, v2, v1, v6}, Lcom/facebook/ads/redexgen/X/Jg;-><init>(Ljava/lang/String;II)V

    sput-object v19, Lcom/facebook/ads/redexgen/X/Jg;->A0F:Lcom/facebook/ads/redexgen/X/Jg;

    .line 1750
    const/16 v2, 0xa0

    const/16 v1, 0x11

    const/16 v0, 0x18

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Jg;->A01(III)Ljava/lang/String;

    move-result-object v2

    const/4 v1, 0x2

    const/4 v5, 0x5

    new-instance v18, Lcom/facebook/ads/redexgen/X/Jg;

    move-object/from16 v0, v18

    invoke-direct {v0, v2, v1, v5}, Lcom/facebook/ads/redexgen/X/Jg;-><init>(Ljava/lang/String;II)V

    sput-object v18, Lcom/facebook/ads/redexgen/X/Jg;->A0D:Lcom/facebook/ads/redexgen/X/Jg;

    .line 1751
    const/16 v2, 0xb1

    const/16 v1, 0x11

    const/16 v0, 0x28

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Jg;->A01(III)Ljava/lang/String;

    move-result-object v2

    const/4 v1, 0x3

    const/4 v4, 0x6

    new-instance v17, Lcom/facebook/ads/redexgen/X/Jg;

    move-object/from16 v0, v17

    invoke-direct {v0, v2, v1, v4}, Lcom/facebook/ads/redexgen/X/Jg;-><init>(Ljava/lang/String;II)V

    sput-object v17, Lcom/facebook/ads/redexgen/X/Jg;->A0E:Lcom/facebook/ads/redexgen/X/Jg;

    .line 1752
    const/16 v2, 0x8e

    const/16 v1, 0x12

    const/16 v0, 0x5a

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Jg;->A01(III)Ljava/lang/String;

    move-result-object v0

    const/4 v3, 0x7

    new-instance v14, Lcom/facebook/ads/redexgen/X/Jg;

    invoke-direct {v14, v0, v6, v3}, Lcom/facebook/ads/redexgen/X/Jg;-><init>(Ljava/lang/String;II)V

    sput-object v14, Lcom/facebook/ads/redexgen/X/Jg;->A0C:Lcom/facebook/ads/redexgen/X/Jg;

    .line 1753
    const/16 v2, 0x111

    const/16 v1, 0x1c

    const/16 v0, 0xd

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Jg;->A01(III)Ljava/lang/String;

    move-result-object v1

    const/16 v0, 0x64

    new-instance v13, Lcom/facebook/ads/redexgen/X/Jg;

    invoke-direct {v13, v1, v5, v0}, Lcom/facebook/ads/redexgen/X/Jg;-><init>(Ljava/lang/String;II)V

    sput-object v13, Lcom/facebook/ads/redexgen/X/Jg;->A0I:Lcom/facebook/ads/redexgen/X/Jg;

    .line 1754
    const/16 v2, 0xd7

    const/16 v1, 0x1f

    const/4 v0, 0x0

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Jg;->A01(III)Ljava/lang/String;

    move-result-object v1

    const/16 v0, 0x65

    new-instance v12, Lcom/facebook/ads/redexgen/X/Jg;

    invoke-direct {v12, v1, v4, v0}, Lcom/facebook/ads/redexgen/X/Jg;-><init>(Ljava/lang/String;II)V

    sput-object v12, Lcom/facebook/ads/redexgen/X/Jg;->A0G:Lcom/facebook/ads/redexgen/X/Jg;

    .line 1755
    const/16 v2, 0x12d

    const/16 v1, 0x1d

    const/16 v0, 0x32

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Jg;->A01(III)Ljava/lang/String;

    move-result-object v1

    const/16 v0, 0x66

    new-instance v11, Lcom/facebook/ads/redexgen/X/Jg;

    invoke-direct {v11, v1, v3, v0}, Lcom/facebook/ads/redexgen/X/Jg;-><init>(Ljava/lang/String;II)V

    sput-object v11, Lcom/facebook/ads/redexgen/X/Jg;->A0J:Lcom/facebook/ads/redexgen/X/Jg;

    .line 1756
    const/16 v3, 0x67

    const/16 v2, 0xf6

    const/16 v1, 0x1b

    const/16 v0, 0x51

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Jg;->A01(III)Ljava/lang/String;

    move-result-object v1

    const/16 v0, 0x8

    new-instance v10, Lcom/facebook/ads/redexgen/X/Jg;

    invoke-direct {v10, v1, v0, v3}, Lcom/facebook/ads/redexgen/X/Jg;-><init>(Ljava/lang/String;II)V

    sput-object v10, Lcom/facebook/ads/redexgen/X/Jg;->A0H:Lcom/facebook/ads/redexgen/X/Jg;

    .line 1757
    const/16 v3, 0xc8

    const/16 v2, 0x17

    const/16 v1, 0xe

    const/16 v0, 0x1a

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Jg;->A01(III)Ljava/lang/String;

    move-result-object v1

    const/16 v0, 0x9

    new-instance v9, Lcom/facebook/ads/redexgen/X/Jg;

    invoke-direct {v9, v1, v0, v3}, Lcom/facebook/ads/redexgen/X/Jg;-><init>(Ljava/lang/String;II)V

    sput-object v9, Lcom/facebook/ads/redexgen/X/Jg;->A05:Lcom/facebook/ads/redexgen/X/Jg;

    .line 1758
    const/16 v3, 0x1f4

    const/16 v2, 0xa

    const/16 v1, 0xd

    const/16 v0, 0x3f

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Jg;->A01(III)Ljava/lang/String;

    move-result-object v1

    const/16 v0, 0xa

    new-instance v8, Lcom/facebook/ads/redexgen/X/Jg;

    invoke-direct {v8, v1, v0, v3}, Lcom/facebook/ads/redexgen/X/Jg;-><init>(Ljava/lang/String;II)V

    sput-object v8, Lcom/facebook/ads/redexgen/X/Jg;->A04:Lcom/facebook/ads/redexgen/X/Jg;

    .line 1759
    const/16 v3, 0x320

    const/16 v2, 0x52

    const/16 v1, 0x17

    const/16 v0, 0x38

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Jg;->A01(III)Ljava/lang/String;

    move-result-object v1

    const/16 v0, 0xb

    new-instance v7, Lcom/facebook/ads/redexgen/X/Jg;

    invoke-direct {v7, v1, v0, v3}, Lcom/facebook/ads/redexgen/X/Jg;-><init>(Ljava/lang/String;II)V

    sput-object v7, Lcom/facebook/ads/redexgen/X/Jg;->A09:Lcom/facebook/ads/redexgen/X/Jg;

    .line 1760
    const/16 v3, 0x321

    const/16 v2, 0x3a

    const/16 v1, 0x18

    const/16 v0, 0x31

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Jg;->A01(III)Ljava/lang/String;

    move-result-object v1

    const/16 v0, 0xc

    new-instance v6, Lcom/facebook/ads/redexgen/X/Jg;

    invoke-direct {v6, v1, v0, v3}, Lcom/facebook/ads/redexgen/X/Jg;-><init>(Ljava/lang/String;II)V

    sput-object v6, Lcom/facebook/ads/redexgen/X/Jg;->A08:Lcom/facebook/ads/redexgen/X/Jg;

    .line 1761
    const/16 v3, 0x322

    const/16 v2, 0x69

    const/16 v1, 0x14

    const/16 v0, 0x55

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Jg;->A01(III)Ljava/lang/String;

    move-result-object v1

    const/16 v0, 0xd

    new-instance v5, Lcom/facebook/ads/redexgen/X/Jg;

    invoke-direct {v5, v1, v0, v3}, Lcom/facebook/ads/redexgen/X/Jg;-><init>(Ljava/lang/String;II)V

    sput-object v5, Lcom/facebook/ads/redexgen/X/Jg;->A0A:Lcom/facebook/ads/redexgen/X/Jg;

    .line 1762
    const/16 v3, 0x323

    const/16 v2, 0x7d

    const/16 v1, 0x11

    const/16 v0, 0x3b

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Jg;->A01(III)Ljava/lang/String;

    move-result-object v2

    const/16 v1, 0xe

    new-instance v4, Lcom/facebook/ads/redexgen/X/Jg;

    move v0, v3

    invoke-direct {v4, v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Jg;-><init>(Ljava/lang/String;II)V

    sput-object v4, Lcom/facebook/ads/redexgen/X/Jg;->A0B:Lcom/facebook/ads/redexgen/X/Jg;

    .line 1763
    const/16 v15, 0xc9

    const/4 v2, 0x0

    const/16 v1, 0xa

    const/16 v0, 0xe

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Jg;->A01(III)Ljava/lang/String;

    move-result-object v2

    const/16 v1, 0xf

    new-instance v3, Lcom/facebook/ads/redexgen/X/Jg;

    move v0, v15

    invoke-direct {v3, v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Jg;-><init>(Ljava/lang/String;II)V

    sput-object v3, Lcom/facebook/ads/redexgen/X/Jg;->A03:Lcom/facebook/ads/redexgen/X/Jg;

    .line 1764
    const/16 v16, 0x190

    const/16 v0, 0x25

    const/16 v2, 0xe

    const/16 v1, 0x45

    move v0, v0

    invoke-static {v0, v2, v1}, Lcom/facebook/ads/redexgen/X/Jg;->A01(III)Ljava/lang/String;

    move-result-object v0

    const/16 v2, 0x10

    new-instance v15, Lcom/facebook/ads/redexgen/X/Jg;

    move v2, v2

    move/from16 v1, v16

    move-object v0, v0

    invoke-direct {v15, v0, v2, v1}, Lcom/facebook/ads/redexgen/X/Jg;-><init>(Ljava/lang/String;II)V

    sput-object v15, Lcom/facebook/ads/redexgen/X/Jg;->A06:Lcom/facebook/ads/redexgen/X/Jg;

    .line 1765
    const/16 v0, 0x11

    new-array v1, v0, [Lcom/facebook/ads/redexgen/X/Jg;

    const/4 v0, 0x0

    aput-object v20, v1, v0

    const/4 v0, 0x1

    aput-object v19, v1, v0

    const/4 v0, 0x2

    aput-object v18, v1, v0

    const/4 v0, 0x3

    aput-object v17, v1, v0

    const/4 v0, 0x4

    aput-object v14, v1, v0

    const/4 v0, 0x5

    aput-object v13, v1, v0

    const/4 v0, 0x6

    aput-object v12, v1, v0

    const/4 v0, 0x7

    aput-object v11, v1, v0

    const/16 v0, 0x8

    aput-object v10, v1, v0

    const/16 v0, 0x9

    aput-object v9, v1, v0

    const/16 v0, 0xa

    aput-object v8, v1, v0

    const/16 v0, 0xb

    aput-object v7, v1, v0

    const/16 v0, 0xc

    aput-object v6, v1, v0

    const/16 v0, 0xd

    aput-object v5, v1, v0

    const/16 v0, 0xe

    aput-object v4, v1, v0

    const/16 v0, 0xf

    aput-object v3, v1, v0

    aput-object v15, v1, v2

    sput-object v1, Lcom/facebook/ads/redexgen/X/Jg;->A02:[Lcom/facebook/ads/redexgen/X/Jg;

    return-void
.end method

.method public constructor <init>(Ljava/lang/String;II)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I)V"
        }
    .end annotation

    .line 40201
    invoke-direct {p0, p1, p2}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    .line 40202
    iput p3, p0, Lcom/facebook/ads/redexgen/X/Jg;->A00:I

    .line 40203
    return-void
.end method

.method public static A00(I)Lcom/facebook/ads/redexgen/X/Jg;
    .locals 5

    .line 40204
    invoke-static {}, Lcom/facebook/ads/redexgen/X/Jg;->values()[Lcom/facebook/ads/redexgen/X/Jg;

    move-result-object v4

    array-length v3, v4

    const/4 v2, 0x0

    :goto_0
    if-ge v2, v3, :cond_1

    aget-object v1, v4, v2

    .line 40205
    .local v3, "template":Lcom/facebook/ads/redexgen/X/Jg;
    iget v0, v1, Lcom/facebook/ads/redexgen/X/Jg;->A00:I

    if-ne v0, p0, :cond_0

    .line 40206
    return-object v1

    .line 40207
    .end local v3    # "template":Lcom/facebook/ads/redexgen/X/Jg;
    :cond_0
    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    .line 40208
    :cond_1
    const/4 v0, 0x0

    return-object v0
.end method

.method public static A01(III)Ljava/lang/String;
    .locals 2

    sget-object v1, Lcom/facebook/ads/redexgen/X/Jg;->A01:[B

    add-int v0, p0, p1

    invoke-static {v1, p0, v0}, Ljava/util/Arrays;->copyOfRange([BII)[B

    move-result-object p0

    const/4 v1, 0x0

    :goto_0
    array-length v0, p0

    if-ge v1, v0, :cond_0

    aget-byte v0, p0, v1

    sub-int/2addr v0, p2

    add-int/lit8 v0, v0, -0x3c

    int-to-byte v0, v0

    aput-byte v0, p0, v1

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_0
    new-instance v0, Ljava/lang/String;

    invoke-direct {v0, p0}, Ljava/lang/String;-><init>([B)V

    return-object v0
.end method

.method public static A02()V
    .locals 1

    const/16 v0, 0x14a

    new-array v0, v0, [B

    fill-array-data v0, :array_0

    sput-object v0, Lcom/facebook/ads/redexgen/X/Jg;->A01:[B

    return-void

    :array_0
    .array-data 1
        -0x68t
        -0x75t
        -0x62t
        -0x6dt
        -0x60t
        -0x71t
        -0x57t
        0x7ct
        0x7ft
        0x7at
        -0x37t
        -0x44t
        -0x31t
        -0x3ct
        -0x2ft
        -0x40t
        -0x26t
        -0x43t
        -0x44t
        -0x37t
        -0x37t
        -0x40t
        -0x33t
        -0x5ct
        -0x69t
        -0x56t
        -0x61t
        -0x54t
        -0x65t
        -0x4bt
        -0x55t
        -0x5ct
        -0x5ft
        -0x5ct
        -0x5bt
        -0x53t
        -0x5ct
        -0x2dt
        -0x3at
        -0x28t
        -0x3et
        -0x2dt
        -0x3bt
        -0x3at
        -0x3bt
        -0x20t
        -0x29t
        -0x36t
        -0x3bt
        -0x3at
        -0x30t
        -0x34t
        -0x3bt
        -0x3et
        -0x3bt
        -0x3at
        -0x32t
        -0x3bt
        -0x3dt
        -0x41t
        -0x34t
        -0x45t
        -0x52t
        -0x3ft
        -0x4at
        -0x3dt
        -0x4et
        -0x34t
        -0x47t
        -0x52t
        -0x45t
        -0x4ft
        -0x40t
        -0x50t
        -0x52t
        -0x43t
        -0x4et
        -0x34t
        -0x62t
        -0x5dt
        -0x34t
        -0x5at
        -0x36t
        -0x3at
        -0x2dt
        -0x3et
        -0x4bt
        -0x38t
        -0x43t
        -0x36t
        -0x47t
        -0x2dt
        -0x3ct
        -0x3dt
        -0x3at
        -0x38t
        -0x3at
        -0x4bt
        -0x43t
        -0x38t
        -0x2dt
        -0x53t
        -0x2dt
        -0x5bt
        -0x56t
        -0x19t
        -0x1dt
        -0x10t
        -0x21t
        -0x2et
        -0x1bt
        -0x26t
        -0x19t
        -0x2at
        -0x10t
        -0x1ct
        -0x1et
        -0x1at
        -0x2et
        -0x1dt
        -0x2at
        -0x10t
        -0x3et
        -0x10t
        -0x3et
        -0x33t
        -0x37t
        -0x2at
        -0x37t
        -0x44t
        -0x32t
        -0x48t
        -0x37t
        -0x45t
        -0x44t
        -0x45t
        -0x2at
        -0x33t
        -0x40t
        -0x45t
        -0x44t
        -0x3at
        -0x13t
        -0x25t
        -0x28t
        -0x14t
        -0x21t
        -0x25t
        -0x13t
        -0xbt
        -0x28t
        -0x29t
        -0x1ct
        -0x1ct
        -0x25t
        -0x18t
        -0xbt
        -0x38t
        -0x35t
        -0x3at
        -0x55t
        -0x67t
        -0x6at
        -0x56t
        -0x63t
        -0x67t
        -0x55t
        -0x4dt
        -0x6at
        -0x6bt
        -0x5et
        -0x5et
        -0x67t
        -0x5at
        -0x4dt
        -0x77t
        -0x7ct
        -0x45t
        -0x57t
        -0x5at
        -0x46t
        -0x53t
        -0x57t
        -0x45t
        -0x3dt
        -0x5at
        -0x5bt
        -0x4et
        -0x4et
        -0x57t
        -0x4at
        -0x3dt
        -0x63t
        -0x6ct
        -0x50t
        -0x62t
        -0x65t
        -0x51t
        -0x5et
        -0x62t
        -0x50t
        -0x48t
        -0x65t
        -0x66t
        -0x59t
        -0x59t
        -0x62t
        -0x55t
        -0x48t
        -0x5bt
        -0x62t
        -0x60t
        -0x66t
        -0x64t
        -0x4et
        -0x6dt
        -0x7ft
        0x7et
        -0x6et
        -0x7bt
        -0x7ft
        -0x6dt
        -0x65t
        -0x7bt
        -0x76t
        -0x70t
        -0x7ft
        -0x72t
        -0x71t
        -0x70t
        -0x7bt
        -0x70t
        -0x7bt
        0x7dt
        -0x78t
        -0x65t
        -0x7ct
        -0x75t
        -0x72t
        -0x7bt
        -0x6at
        -0x75t
        -0x76t
        -0x70t
        0x7dt
        -0x78t
        -0x1ct
        -0x2et
        -0x31t
        -0x1dt
        -0x2at
        -0x2et
        -0x1ct
        -0x14t
        -0x2at
        -0x25t
        -0x1ft
        -0x2et
        -0x21t
        -0x20t
        -0x1ft
        -0x2at
        -0x1ft
        -0x2at
        -0x32t
        -0x27t
        -0x14t
        -0x1ft
        -0x32t
        -0x31t
        -0x27t
        -0x2et
        -0x1ft
        -0x60t
        -0x72t
        -0x75t
        -0x61t
        -0x6et
        -0x72t
        -0x60t
        -0x58t
        -0x6et
        -0x69t
        -0x63t
        -0x72t
        -0x65t
        -0x64t
        -0x63t
        -0x6et
        -0x63t
        -0x6et
        -0x76t
        -0x6bt
        -0x58t
        -0x62t
        -0x69t
        -0x6ct
        -0x69t
        -0x68t
        -0x60t
        -0x69t
        -0x3bt
        -0x4dt
        -0x50t
        -0x3ct
        -0x49t
        -0x4dt
        -0x3bt
        -0x33t
        -0x49t
        -0x44t
        -0x3et
        -0x4dt
        -0x40t
        -0x3ft
        -0x3et
        -0x49t
        -0x3et
        -0x49t
        -0x51t
        -0x46t
        -0x33t
        -0x3ct
        -0x4dt
        -0x40t
        -0x3et
        -0x49t
        -0x4ft
        -0x51t
        -0x46t
    .end array-data
.end method

.method public static valueOf(Ljava/lang/String;)Lcom/facebook/ads/redexgen/X/Jg;
    .locals 1

    .line 40216
    const-class v0, Lcom/facebook/ads/redexgen/X/Jg;

    invoke-static {v0, p0}, Ljava/lang/Enum;->valueOf(Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Enum;

    move-result-object v0

    check-cast v0, Lcom/facebook/ads/redexgen/X/Jg;

    return-object v0
.end method

.method public static values()[Lcom/facebook/ads/redexgen/X/Jg;
    .locals 1

    .line 40217
    sget-object v0, Lcom/facebook/ads/redexgen/X/Jg;->A02:[Lcom/facebook/ads/redexgen/X/Jg;

    invoke-virtual {v0}, [Ljava/lang/Object;->clone()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [Lcom/facebook/ads/redexgen/X/Jg;

    return-object v0
.end method


# virtual methods
.method public final A03()I
    .locals 1

    .line 40209
    iget v0, p0, Lcom/facebook/ads/redexgen/X/Jg;->A00:I

    return v0
.end method

.method public final A04()Lcom/facebook/ads/internal/protocol/AdPlacementType;
    .locals 2

    .line 40210
    sget-object v1, Lcom/facebook/ads/redexgen/X/Jf;->A00:[I

    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Jg;->ordinal()I

    move-result v0

    aget v0, v1, v0

    packed-switch v0, :pswitch_data_0

    .line 40211
    sget-object v0, Lcom/facebook/ads/internal/protocol/AdPlacementType;->UNKNOWN:Lcom/facebook/ads/internal/protocol/AdPlacementType;

    return-object v0

    .line 40212
    :pswitch_0
    sget-object v0, Lcom/facebook/ads/internal/protocol/AdPlacementType;->REWARDED_VIDEO:Lcom/facebook/ads/internal/protocol/AdPlacementType;

    return-object v0

    .line 40213
    :pswitch_1
    sget-object v0, Lcom/facebook/ads/internal/protocol/AdPlacementType;->INTERSTITIAL:Lcom/facebook/ads/internal/protocol/AdPlacementType;

    return-object v0

    .line 40214
    :pswitch_2
    sget-object v0, Lcom/facebook/ads/internal/protocol/AdPlacementType;->BANNER:Lcom/facebook/ads/internal/protocol/AdPlacementType;

    return-object v0

    .line 40215
    :pswitch_3
    sget-object v0, Lcom/facebook/ads/internal/protocol/AdPlacementType;->NATIVE:Lcom/facebook/ads/internal/protocol/AdPlacementType;

    return-object v0

    :pswitch_data_0
    .packed-switch 0x1
        :pswitch_3
        :pswitch_3
        :pswitch_3
        :pswitch_3
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_0
        :pswitch_0
    .end packed-switch
.end method
