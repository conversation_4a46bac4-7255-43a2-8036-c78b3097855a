.class public final Lcom/facebook/ads/redexgen/X/Jt;
.super Lcom/facebook/ads/redexgen/X/RM;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/facebook/ads/redexgen/X/RM;->A00()Lcom/facebook/ads/redexgen/X/Jt;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 40667
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/RM;-><init>()V

    return-void
.end method


# virtual methods
.method public final A00(Lcom/facebook/ads/redexgen/X/RP;Lcom/facebook/ads/redexgen/X/89;Ljava/util/concurrent/Executor;)Lcom/facebook/ads/redexgen/X/Jh;
    .locals 1

    .line 40668
    new-instance v0, Lcom/facebook/ads/redexgen/X/Jh;

    invoke-direct {v0, p1, p2, p3}, Lcom/facebook/ads/redexgen/X/Jh;-><init>(Lcom/facebook/ads/redexgen/X/RP;Lcom/facebook/ads/redexgen/X/89;Ljava/util/concurrent/Executor;)V

    return-object v0
.end method
