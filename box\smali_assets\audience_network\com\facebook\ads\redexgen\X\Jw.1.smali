.class public final Lcom/facebook/ads/redexgen/X/Jw;
.super Ljava/lang/Object;
.source ""

# interfaces
.implements Lcom/facebook/ads/redexgen/X/E9;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/facebook/ads/redexgen/X/R0;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field public final synthetic A00:Lcom/facebook/ads/redexgen/X/R0;


# direct methods
.method public constructor <init>(Lcom/facebook/ads/redexgen/X/R0;)V
    .locals 0

    .line 41360
    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/Jw;->A00:Lcom/facebook/ads/redexgen/X/R0;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final ABg(Lcom/facebook/ads/redexgen/X/EG;)V
    .locals 1

    .line 41361
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Jw;->A00:Lcom/facebook/ads/redexgen/X/R0;

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/R0;->A0E(Lcom/facebook/ads/redexgen/X/R0;)V

    .line 41362
    return-void
.end method

.method public final ABk(Lcom/facebook/ads/redexgen/X/EG;)V
    .locals 0

    .line 41363
    return-void
.end method

.method public final ADI(Lcom/facebook/ads/redexgen/X/EG;Lcom/facebook/ads/redexgen/X/EF;)V
    .locals 1

    .line 41364
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Jw;->A00:Lcom/facebook/ads/redexgen/X/R0;

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/R0;->A0D(Lcom/facebook/ads/redexgen/X/R0;)V

    .line 41365
    return-void
.end method
