.class public final Lcom/facebook/ads/redexgen/X/Jx;
.super Ljava/lang/Object;
.source ""

# interfaces
.implements Lcom/facebook/ads/redexgen/X/A0;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/facebook/ads/redexgen/X/Qw;->A0G(Lcom/facebook/ads/redexgen/X/Qt;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field public final synthetic A00:Lcom/facebook/ads/redexgen/X/Qt;

.field public final synthetic A01:Lcom/facebook/ads/redexgen/X/Qw;


# direct methods
.method public constructor <init>(Lcom/facebook/ads/redexgen/X/Qw;Lcom/facebook/ads/redexgen/X/Qt;)V
    .locals 0

    .line 41366
    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/Jx;->A01:Lcom/facebook/ads/redexgen/X/Qw;

    iput-object p2, p0, Lcom/facebook/ads/redexgen/X/Jx;->A00:Lcom/facebook/ads/redexgen/X/Qt;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final AC7(Z)V
    .locals 0

    .line 41367
    return-void
.end method

.method public final ACb(Lcom/facebook/ads/redexgen/X/9x;)V
    .locals 0

    .line 41368
    return-void
.end method

.method public final ACd(Lcom/facebook/ads/redexgen/X/9c;)V
    .locals 2

    .line 41369
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/Jx;->A00:Lcom/facebook/ads/redexgen/X/Qt;

    invoke-static {p1}, Lcom/facebook/ads/redexgen/X/Qw;->A01(Lcom/facebook/ads/redexgen/X/9c;)Ljava/lang/String;

    move-result-object v0

    invoke-interface {v1, v0, p1}, Lcom/facebook/ads/redexgen/X/Qt;->ACe(Ljava/lang/String;Ljava/lang/Exception;)V

    .line 41370
    return-void
.end method

.method public final ACf(ZI)V
    .locals 1

    .line 41371
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Jx;->A00:Lcom/facebook/ads/redexgen/X/Qt;

    invoke-interface {v0, p1, p2}, Lcom/facebook/ads/redexgen/X/Qt;->ACf(ZI)V

    .line 41372
    return-void
.end method

.method public final ACh(I)V
    .locals 0

    .line 41373
    return-void
.end method

.method public final AD4()V
    .locals 0

    .line 41374
    return-void
.end method

.method public final ADJ(Lcom/facebook/ads/redexgen/X/AH;Ljava/lang/Object;I)V
    .locals 0

    .line 41375
    return-void
.end method

.method public final ADM(Lcom/facebook/ads/internal/exoplayer2/thirdparty/source/TrackGroupArray;Lcom/facebook/ads/redexgen/X/Gh;)V
    .locals 0

    .line 41376
    return-void
.end method
