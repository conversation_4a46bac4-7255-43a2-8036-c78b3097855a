.class public final Lcom/facebook/ads/redexgen/X/Jy;
.super Ljava/lang/Object;
.source ""

# interfaces
.implements Lcom/facebook/ads/redexgen/X/IU;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/facebook/ads/redexgen/X/Qw;->A0H(Lcom/facebook/ads/redexgen/X/Qv;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field public final synthetic A00:Lcom/facebook/ads/redexgen/X/Qv;

.field public final synthetic A01:Lcom/facebook/ads/redexgen/X/Qw;


# direct methods
.method public constructor <init>(Lcom/facebook/ads/redexgen/X/Qw;Lcom/facebook/ads/redexgen/X/Qv;)V
    .locals 0

    .line 41377
    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/Jy;->A01:Lcom/facebook/ads/redexgen/X/Qw;

    iput-object p2, p0, Lcom/facebook/ads/redexgen/X/Jy;->A00:Lcom/facebook/ads/redexgen/X/Qv;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final ADf(IIIF)V
    .locals 1

    .line 41378
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Jy;->A00:Lcom/facebook/ads/redexgen/X/Qv;

    invoke-interface {v0, p1, p2, p3, p4}, Lcom/facebook/ads/redexgen/X/Qv;->ADf(IIIF)V

    .line 41379
    return-void
.end method
