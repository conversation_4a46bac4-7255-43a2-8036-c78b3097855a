.class public interface abstract Lcom/facebook/ads/redexgen/X/K3;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/facebook/ads/redexgen/X/K4;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "AdProviderListener"
.end annotation


# virtual methods
.method public abstract ABR(Lcom/facebook/ads/redexgen/X/Jb;)V
.end method

.method public abstract ADG(Lcom/facebook/ads/redexgen/X/Up;)V
.end method
