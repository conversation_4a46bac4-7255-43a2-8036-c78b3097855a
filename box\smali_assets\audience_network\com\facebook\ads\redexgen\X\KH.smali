.class public final Lcom/facebook/ads/redexgen/X/KH;
.super Ljava/util/concurrent/ConcurrentLinkedQueue;
.source ""


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/util/concurrent/ConcurrentLinkedQueue<",
        "Lcom/facebook/ads/redexgen/X/KX;",
        ">;"
    }
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 41859
    invoke-direct {p0}, Ljava/util/concurrent/ConcurrentLinkedQueue;-><init>()V

    return-void
.end method

.method public constructor <init>(Lcom/facebook/ads/redexgen/X/KH;)V
    .locals 0

    .line 41860
    invoke-direct {p0, p1}, Ljava/util/concurrent/ConcurrentLinkedQueue;-><init>(Ljava/util/Collection;)V

    .line 41861
    return-void
.end method
