.class public final enum Lcom/facebook/ads/redexgen/X/L2;
.super Ljava/lang/Enum;
.source ""


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Enum<",
        "Lcom/facebook/ads/redexgen/X/L2;",
        ">;"
    }
.end annotation


# static fields
.field public static A01:[B

.field public static final A02:[Lcom/facebook/ads/redexgen/X/L2;

.field public static final A03:Ljava/lang/String;

.field public static final synthetic A04:[Lcom/facebook/ads/redexgen/X/L2;

.field public static final enum A05:Lcom/facebook/ads/redexgen/X/L2;

.field public static final enum A06:Lcom/facebook/ads/redexgen/X/L2;

.field public static final enum A07:Lcom/facebook/ads/redexgen/X/L2;

.field public static final enum A08:Lcom/facebook/ads/redexgen/X/L2;

.field public static final enum A09:Lcom/facebook/ads/redexgen/X/L2;

.field public static final enum A0A:Lcom/facebook/ads/redexgen/X/L2;

.field public static final enum A0B:Lcom/facebook/ads/redexgen/X/L2;

.field public static final enum A0C:Lcom/facebook/ads/redexgen/X/L2;

.field public static final enum A0D:Lcom/facebook/ads/redexgen/X/L2;

.field public static final enum A0E:Lcom/facebook/ads/redexgen/X/L2;

.field public static final enum A0F:Lcom/facebook/ads/redexgen/X/L2;

.field public static final enum A0G:Lcom/facebook/ads/redexgen/X/L2;

.field public static final enum A0H:Lcom/facebook/ads/redexgen/X/L2;

.field public static final enum A0I:Lcom/facebook/ads/redexgen/X/L2;

.field public static final enum A0J:Lcom/facebook/ads/redexgen/X/L2;


# instance fields
.field public final A00:I


# direct methods
.method public static constructor <clinit>()V
    .locals 20

    .line 1873
    invoke-static {}, Lcom/facebook/ads/redexgen/X/L2;->A03()V

    const/16 v2, 0xa

    const/4 v1, 0x6

    const/16 v0, 0x3e

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/L2;->A02(III)Ljava/lang/String;

    move-result-object v2

    const/4 v1, 0x0

    new-instance v19, Lcom/facebook/ads/redexgen/X/L2;

    move-object/from16 v0, v19

    invoke-direct {v0, v2, v1, v1}, Lcom/facebook/ads/redexgen/X/L2;-><init>(Ljava/lang/String;II)V

    sput-object v19, Lcom/facebook/ads/redexgen/X/L2;->A06:Lcom/facebook/ads/redexgen/X/L2;

    .line 1874
    const/16 v2, 0x92

    const/4 v1, 0x7

    const/16 v0, 0x38

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/L2;->A02(III)Ljava/lang/String;

    move-result-object v2

    const/4 v1, 0x1

    new-instance v18, Lcom/facebook/ads/redexgen/X/L2;

    move-object/from16 v0, v18

    invoke-direct {v0, v2, v1, v1}, Lcom/facebook/ads/redexgen/X/L2;-><init>(Ljava/lang/String;II)V

    sput-object v18, Lcom/facebook/ads/redexgen/X/L2;->A0F:Lcom/facebook/ads/redexgen/X/L2;

    .line 1875
    const/16 v2, 0x10

    const/16 v1, 0x9

    const/16 v0, 0x13

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/L2;->A02(III)Ljava/lang/String;

    move-result-object v1

    const/4 v0, 0x2

    new-instance v14, Lcom/facebook/ads/redexgen/X/L2;

    invoke-direct {v14, v1, v0, v0}, Lcom/facebook/ads/redexgen/X/L2;-><init>(Ljava/lang/String;II)V

    sput-object v14, Lcom/facebook/ads/redexgen/X/L2;->A07:Lcom/facebook/ads/redexgen/X/L2;

    .line 1876
    const/16 v2, 0x99

    const/16 v1, 0xa

    const/16 v0, 0x7b

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/L2;->A02(III)Ljava/lang/String;

    move-result-object v1

    const/4 v0, 0x3

    new-instance v13, Lcom/facebook/ads/redexgen/X/L2;

    invoke-direct {v13, v1, v0, v0}, Lcom/facebook/ads/redexgen/X/L2;-><init>(Ljava/lang/String;II)V

    sput-object v13, Lcom/facebook/ads/redexgen/X/L2;->A0G:Lcom/facebook/ads/redexgen/X/L2;

    .line 1877
    const/16 v2, 0x19

    const/16 v1, 0x11

    const/16 v0, 0x39

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/L2;->A02(III)Ljava/lang/String;

    move-result-object v1

    const/4 v0, 0x4

    new-instance v12, Lcom/facebook/ads/redexgen/X/L2;

    invoke-direct {v12, v1, v0, v0}, Lcom/facebook/ads/redexgen/X/L2;-><init>(Ljava/lang/String;II)V

    sput-object v12, Lcom/facebook/ads/redexgen/X/L2;->A08:Lcom/facebook/ads/redexgen/X/L2;

    .line 1878
    const/4 v2, 0x0

    const/16 v1, 0xa

    const/16 v0, 0x64

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/L2;->A02(III)Ljava/lang/String;

    move-result-object v1

    const/4 v0, 0x5

    new-instance v11, Lcom/facebook/ads/redexgen/X/L2;

    invoke-direct {v11, v1, v0, v0}, Lcom/facebook/ads/redexgen/X/L2;-><init>(Ljava/lang/String;II)V

    sput-object v11, Lcom/facebook/ads/redexgen/X/L2;->A05:Lcom/facebook/ads/redexgen/X/L2;

    .line 1879
    const/16 v2, 0x6a

    const/16 v1, 0xa

    const/16 v0, 0x5f

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/L2;->A02(III)Ljava/lang/String;

    move-result-object v1

    const/4 v0, 0x6

    new-instance v10, Lcom/facebook/ads/redexgen/X/L2;

    invoke-direct {v10, v1, v0, v0}, Lcom/facebook/ads/redexgen/X/L2;-><init>(Ljava/lang/String;II)V

    sput-object v10, Lcom/facebook/ads/redexgen/X/L2;->A0D:Lcom/facebook/ads/redexgen/X/L2;

    .line 1880
    const/16 v2, 0x74

    const/16 v1, 0x1e

    const/16 v0, 0x2e

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/L2;->A02(III)Ljava/lang/String;

    move-result-object v1

    const/4 v0, 0x7

    new-instance v9, Lcom/facebook/ads/redexgen/X/L2;

    invoke-direct {v9, v1, v0, v0}, Lcom/facebook/ads/redexgen/X/L2;-><init>(Ljava/lang/String;II)V

    sput-object v9, Lcom/facebook/ads/redexgen/X/L2;->A0E:Lcom/facebook/ads/redexgen/X/L2;

    .line 1881
    const/16 v2, 0xc5

    const/16 v1, 0x8

    const/16 v0, 0x21

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/L2;->A02(III)Ljava/lang/String;

    move-result-object v1

    const/16 v0, 0x8

    new-instance v8, Lcom/facebook/ads/redexgen/X/L2;

    invoke-direct {v8, v1, v0, v0}, Lcom/facebook/ads/redexgen/X/L2;-><init>(Ljava/lang/String;II)V

    sput-object v8, Lcom/facebook/ads/redexgen/X/L2;->A0J:Lcom/facebook/ads/redexgen/X/L2;

    .line 1882
    const/16 v2, 0x5b

    const/16 v1, 0xf

    const/16 v0, 0x11

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/L2;->A02(III)Ljava/lang/String;

    move-result-object v1

    const/16 v0, 0x9

    new-instance v7, Lcom/facebook/ads/redexgen/X/L2;

    invoke-direct {v7, v1, v0, v0}, Lcom/facebook/ads/redexgen/X/L2;-><init>(Ljava/lang/String;II)V

    sput-object v7, Lcom/facebook/ads/redexgen/X/L2;->A0C:Lcom/facebook/ads/redexgen/X/L2;

    .line 1883
    const/16 v2, 0x2a

    const/16 v1, 0x16

    const/16 v0, 0x20

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/L2;->A02(III)Ljava/lang/String;

    move-result-object v1

    const/16 v0, 0xa

    new-instance v6, Lcom/facebook/ads/redexgen/X/L2;

    invoke-direct {v6, v1, v0, v0}, Lcom/facebook/ads/redexgen/X/L2;-><init>(Ljava/lang/String;II)V

    sput-object v6, Lcom/facebook/ads/redexgen/X/L2;->A09:Lcom/facebook/ads/redexgen/X/L2;

    .line 1884
    const/16 v2, 0xa3

    const/16 v1, 0x13

    const/16 v0, 0x75

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/L2;->A02(III)Ljava/lang/String;

    move-result-object v1

    const/16 v0, 0xb

    new-instance v5, Lcom/facebook/ads/redexgen/X/L2;

    invoke-direct {v5, v1, v0, v0}, Lcom/facebook/ads/redexgen/X/L2;-><init>(Ljava/lang/String;II)V

    sput-object v5, Lcom/facebook/ads/redexgen/X/L2;->A0H:Lcom/facebook/ads/redexgen/X/L2;

    .line 1885
    const/16 v2, 0x10

    const/16 v3, 0xb6

    const/16 v1, 0xf

    const/16 v0, 0x55

    invoke-static {v3, v1, v0}, Lcom/facebook/ads/redexgen/X/L2;->A02(III)Ljava/lang/String;

    move-result-object v1

    const/16 v0, 0xc

    new-instance v4, Lcom/facebook/ads/redexgen/X/L2;

    invoke-direct {v4, v1, v0, v2}, Lcom/facebook/ads/redexgen/X/L2;-><init>(Ljava/lang/String;II)V

    sput-object v4, Lcom/facebook/ads/redexgen/X/L2;->A0I:Lcom/facebook/ads/redexgen/X/L2;

    .line 1886
    const/16 v15, 0x11

    const/16 v2, 0x51

    const/16 v1, 0xa

    const/16 v0, 0xe

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/L2;->A02(III)Ljava/lang/String;

    move-result-object v2

    const/16 v1, 0xd

    new-instance v3, Lcom/facebook/ads/redexgen/X/L2;

    move v0, v15

    invoke-direct {v3, v2, v1, v0}, Lcom/facebook/ads/redexgen/X/L2;-><init>(Ljava/lang/String;II)V

    sput-object v3, Lcom/facebook/ads/redexgen/X/L2;->A0B:Lcom/facebook/ads/redexgen/X/L2;

    .line 1887
    const/16 v17, 0x12

    const/16 v0, 0x40

    const/16 v2, 0x11

    const/16 v1, 0x68

    move v0, v0

    invoke-static {v0, v2, v1}, Lcom/facebook/ads/redexgen/X/L2;->A02(III)Ljava/lang/String;

    move-result-object v0

    const/16 v16, 0xe

    new-instance v15, Lcom/facebook/ads/redexgen/X/L2;

    move/from16 v2, v16

    move/from16 v1, v17

    move-object v0, v0

    invoke-direct {v15, v0, v2, v1}, Lcom/facebook/ads/redexgen/X/L2;-><init>(Ljava/lang/String;II)V

    sput-object v15, Lcom/facebook/ads/redexgen/X/L2;->A0A:Lcom/facebook/ads/redexgen/X/L2;

    .line 1888
    const/16 v0, 0xf

    new-array v1, v0, [Lcom/facebook/ads/redexgen/X/L2;

    const/4 v0, 0x0

    aput-object v19, v1, v0

    const/4 v0, 0x1

    aput-object v18, v1, v0

    const/4 v0, 0x2

    aput-object v14, v1, v0

    const/4 v0, 0x3

    aput-object v13, v1, v0

    const/4 v0, 0x4

    aput-object v12, v1, v0

    const/4 v0, 0x5

    aput-object v11, v1, v0

    const/4 v0, 0x6

    aput-object v10, v1, v0

    const/4 v0, 0x7

    aput-object v9, v1, v0

    const/16 v2, 0x8

    aput-object v8, v1, v2

    const/16 v0, 0x9

    aput-object v7, v1, v0

    const/16 v0, 0xa

    aput-object v6, v1, v0

    const/16 v0, 0xb

    aput-object v5, v1, v0

    const/16 v0, 0xc

    aput-object v4, v1, v0

    const/16 v0, 0xd

    aput-object v3, v1, v0

    aput-object v15, v1, v16

    sput-object v1, Lcom/facebook/ads/redexgen/X/L2;->A04:[Lcom/facebook/ads/redexgen/X/L2;

    .line 1889
    new-array v7, v2, [Lcom/facebook/ads/redexgen/X/L2;

    const/4 v6, 0x0

    aput-object v13, v7, v6

    const/4 v0, 0x1

    aput-object v12, v7, v0

    const/4 v0, 0x2

    aput-object v11, v7, v0

    const/4 v0, 0x3

    aput-object v9, v7, v0

    const/4 v0, 0x4

    aput-object v5, v7, v0

    const/4 v0, 0x5

    aput-object v4, v7, v0

    const/4 v0, 0x6

    aput-object v3, v7, v0

    const/4 v0, 0x7

    aput-object v15, v7, v0

    sput-object v7, Lcom/facebook/ads/redexgen/X/L2;->A02:[Lcom/facebook/ads/redexgen/X/L2;

    .line 1890
    new-instance v2, Lorg/json/JSONArray;

    invoke-direct {v2}, Lorg/json/JSONArray;-><init>()V

    .line 1891
    .local v2, "array":Lorg/json/JSONArray;
    array-length v1, v7

    :goto_0
    if-ge v6, v1, :cond_0

    aget-object v0, v7, v6

    .line 1892
    .local v4, "supportedCapability":Lcom/facebook/ads/redexgen/X/L2;
    invoke-direct {v0}, Lcom/facebook/ads/redexgen/X/L2;->A00()I

    move-result v0

    invoke-virtual {v2, v0}, Lorg/json/JSONArray;->put(I)Lorg/json/JSONArray;

    .line 1893
    .end local v4    # "supportedCapability":Lcom/facebook/ads/redexgen/X/L2;
    add-int/lit8 v6, v6, 0x1

    goto :goto_0

    .line 1894
    :cond_0
    invoke-virtual {v2}, Lorg/json/JSONArray;->toString()Ljava/lang/String;

    move-result-object v0

    sput-object v0, Lcom/facebook/ads/redexgen/X/L2;->A03:Ljava/lang/String;

    .line 1895
    .end local v2    # "array":Lorg/json/JSONArray;
    return-void
.end method

.method public constructor <init>(Ljava/lang/String;II)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I)V"
        }
    .end annotation

    .line 42916
    invoke-direct {p0, p1, p2}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    .line 42917
    iput p3, p0, Lcom/facebook/ads/redexgen/X/L2;->A00:I

    .line 42918
    return-void
.end method

.method private final A00()I
    .locals 1

    .line 42919
    iget v0, p0, Lcom/facebook/ads/redexgen/X/L2;->A00:I

    return v0
.end method

.method public static A01()Ljava/lang/String;
    .locals 1

    .line 42920
    sget-object v0, Lcom/facebook/ads/redexgen/X/L2;->A03:Ljava/lang/String;

    return-object v0
.end method

.method public static A02(III)Ljava/lang/String;
    .locals 2

    sget-object v1, Lcom/facebook/ads/redexgen/X/L2;->A01:[B

    add-int v0, p0, p1

    invoke-static {v1, p0, v0}, Ljava/util/Arrays;->copyOfRange([BII)[B

    move-result-object p0

    const/4 v1, 0x0

    :goto_0
    array-length v0, p0

    if-ge v1, v0, :cond_0

    aget-byte v0, p0, v1

    sub-int/2addr v0, p2

    add-int/lit8 v0, v0, -0xe

    int-to-byte v0, v0

    aput-byte v0, p0, v1

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_0
    new-instance v0, Ljava/lang/String;

    invoke-direct {v0, p0}, Ljava/lang/String;-><init>([B)V

    return-object v0
.end method

.method public static A03()V
    .locals 1

    const/16 v0, 0xcd

    new-array v0, v0, [B

    fill-array-data v0, :array_0

    sput-object v0, Lcom/facebook/ads/redexgen/X/L2;->A01:[B

    return-void

    :array_0
    .array-data 1
        -0x4dt
        -0x4at
        -0x2ft
        -0x4bt
        -0x46t
        -0x3ft
        -0x45t
        -0x4bt
        -0x49t
        -0x3bt
        -0x73t
        -0x64t
        -0x64t
        -0x55t
        -0x73t
        -0x70t
        0x62t
        0x71t
        0x71t
        -0x80t
        0x62t
        0x65t
        -0x80t
        0x77t
        0x53t
        -0x78t
        -0x69t
        -0x69t
        -0x5at
        -0x74t
        -0x6bt
        -0x72t
        -0x78t
        -0x72t
        -0x74t
        -0x6ct
        -0x74t
        -0x6bt
        -0x65t
        -0x5at
        -0x78t
        -0x75t
        0x70t
        0x6ft
        0x7ct
        0x7ct
        0x73t
        -0x80t
        -0x73t
        -0x7et
        0x7dt
        -0x73t
        0x77t
        0x7ct
        -0x7et
        0x73t
        -0x80t
        -0x7ft
        -0x7et
        0x77t
        -0x7et
        0x77t
        0x6ft
        0x7at
        -0x44t
        -0x38t
        -0x45t
        -0x39t
        -0x35t
        -0x45t
        -0x3ct
        -0x47t
        -0x31t
        -0x2bt
        -0x47t
        -0x49t
        -0x3at
        -0x3at
        -0x41t
        -0x3ct
        -0x43t
        0x64t
        0x70t
        0x70t
        0x6ct
        0x7bt
        0x68t
        0x65t
        0x6at
        0x67t
        0x6ft
        0x68t
        0x6dt
        0x6bt
        0x68t
        0x6dt
        0x64t
        0x7et
        0x75t
        0x68t
        0x63t
        0x64t
        0x6et
        0x7et
        0x60t
        0x63t
        -0x49t
        -0x40t
        -0x34t
        -0x3ft
        -0x41t
        -0x4at
        -0x4ct
        -0x4ct
        -0x4et
        -0x41t
        -0x7at
        -0x71t
        -0x65t
        -0x70t
        -0x72t
        -0x7bt
        -0x7dt
        -0x7dt
        -0x7ft
        -0x72t
        -0x65t
        -0x76t
        -0x75t
        -0x65t
        0x7dt
        -0x6ft
        -0x70t
        -0x75t
        -0x65t
        -0x7bt
        -0x77t
        -0x74t
        -0x65t
        -0x78t
        -0x75t
        -0x7dt
        -0x7dt
        -0x7bt
        -0x76t
        -0x7dt
        -0x6et
        -0x71t
        -0x6ct
        -0x6ft
        -0x5bt
        -0x79t
        -0x76t
        -0x2bt
        -0x2et
        -0x29t
        -0x2ct
        -0x18t
        -0x36t
        -0x33t
        -0x18t
        -0x21t
        -0x45t
        -0x2ft
        -0x3ct
        -0x29t
        -0x34t
        -0x27t
        -0x38t
        -0x1et
        -0x3at
        -0x31t
        -0x2et
        -0x2at
        -0x38t
        -0x1et
        -0x3bt
        -0x28t
        -0x29t
        -0x29t
        -0x2et
        -0x2ft
        -0x48t
        -0x4ft
        -0x54t
        -0x57t
        -0x54t
        -0x58t
        -0x59t
        -0x3et
        -0x51t
        -0x4et
        -0x56t
        -0x56t
        -0x54t
        -0x4ft
        -0x56t
        -0x7bt
        0x78t
        0x73t
        0x74t
        0x7et
        -0x72t
        0x70t
        0x73t
    .end array-data
.end method

.method public static valueOf(Ljava/lang/String;)Lcom/facebook/ads/redexgen/X/L2;
    .locals 1

    .line 42922
    const-class v0, Lcom/facebook/ads/redexgen/X/L2;

    invoke-static {v0, p0}, Ljava/lang/Enum;->valueOf(Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Enum;

    move-result-object v0

    check-cast v0, Lcom/facebook/ads/redexgen/X/L2;

    return-object v0
.end method

.method public static values()[Lcom/facebook/ads/redexgen/X/L2;
    .locals 1

    .line 42923
    sget-object v0, Lcom/facebook/ads/redexgen/X/L2;->A04:[Lcom/facebook/ads/redexgen/X/L2;

    invoke-virtual {v0}, [Ljava/lang/Object;->clone()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [Lcom/facebook/ads/redexgen/X/L2;

    return-object v0
.end method


# virtual methods
.method public final toString()Ljava/lang/String;
    .locals 1

    .line 42921
    iget v0, p0, Lcom/facebook/ads/redexgen/X/L2;->A00:I

    invoke-static {v0}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
