.class public final enum Lcom/facebook/ads/redexgen/X/LI;
.super Ljava/lang/Enum;
.source ""


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Enum<",
        "Lcom/facebook/ads/redexgen/X/LI;",
        ">;"
    }
.end annotation


# static fields
.field public static A02:I

.field public static A03:[B

.field public static final synthetic A04:[Lcom/facebook/ads/redexgen/X/LI;

.field public static final enum A05:Lcom/facebook/ads/redexgen/X/LI;

.field public static final enum A06:Lcom/facebook/ads/redexgen/X/LI;

.field public static final enum A07:Lcom/facebook/ads/redexgen/X/LI;

.field public static final enum A08:Lcom/facebook/ads/redexgen/X/LI;

.field public static final enum A09:Lcom/facebook/ads/redexgen/X/LI;

.field public static final enum A0A:Lcom/facebook/ads/redexgen/X/LI;

.field public static final enum A0B:Lcom/facebook/ads/redexgen/X/LI;

.field public static final enum A0C:Lcom/facebook/ads/redexgen/X/LI;

.field public static final enum A0D:Lcom/facebook/ads/redexgen/X/LI;

.field public static final enum A0E:Lcom/facebook/ads/redexgen/X/LI;

.field public static final enum A0F:Lcom/facebook/ads/redexgen/X/LI;

.field public static final enum A0G:Lcom/facebook/ads/redexgen/X/LI;

.field public static final enum A0H:Lcom/facebook/ads/redexgen/X/LI;

.field public static final enum A0I:Lcom/facebook/ads/redexgen/X/LI;

.field public static final enum A0J:Lcom/facebook/ads/redexgen/X/LI;


# instance fields
.field public final A00:I

.field public final A01:Lcom/facebook/ads/NativeAdBase$NativeComponentTag;


# direct methods
.method public static constructor <clinit>()V
    .locals 20

    .line 1920
    invoke-static {}, Lcom/facebook/ads/redexgen/X/LI;->A02()V

    const/16 v2, 0xf6

    const/16 v1, 0xf

    const/16 v0, 0x34

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/LI;->A01(III)Ljava/lang/String;

    move-result-object v2

    const/4 v1, 0x0

    new-instance v19, Lcom/facebook/ads/redexgen/X/LI;

    move-object/from16 v0, v19

    invoke-direct {v0, v2, v1, v1}, Lcom/facebook/ads/redexgen/X/LI;-><init>(Ljava/lang/String;II)V

    sput-object v19, Lcom/facebook/ads/redexgen/X/LI;->A0H:Lcom/facebook/ads/redexgen/X/LI;

    .line 1921
    const/16 v2, 0xe5

    const/16 v1, 0x11

    const/16 v0, 0x64

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/LI;->A01(III)Ljava/lang/String;

    move-result-object v2

    const/4 v1, 0x1

    new-instance v18, Lcom/facebook/ads/redexgen/X/LI;

    move-object/from16 v0, v18

    invoke-direct {v0, v2, v1, v1}, Lcom/facebook/ads/redexgen/X/LI;-><init>(Ljava/lang/String;II)V

    sput-object v18, Lcom/facebook/ads/redexgen/X/LI;->A0G:Lcom/facebook/ads/redexgen/X/LI;

    .line 1922
    const/16 v2, 0xd1

    const/16 v1, 0x14

    const/16 v0, 0x1a

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/LI;->A01(III)Ljava/lang/String;

    move-result-object v2

    const/4 v1, 0x2

    new-instance v17, Lcom/facebook/ads/redexgen/X/LI;

    move-object/from16 v0, v17

    invoke-direct {v0, v2, v1, v1}, Lcom/facebook/ads/redexgen/X/LI;-><init>(Ljava/lang/String;II)V

    sput-object v17, Lcom/facebook/ads/redexgen/X/LI;->A0F:Lcom/facebook/ads/redexgen/X/LI;

    .line 1923
    const/16 v2, 0x117

    const/16 v1, 0x18

    const/16 v0, 0x4a

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/LI;->A01(III)Ljava/lang/String;

    move-result-object v1

    const/4 v0, 0x3

    new-instance v14, Lcom/facebook/ads/redexgen/X/LI;

    invoke-direct {v14, v1, v0, v0}, Lcom/facebook/ads/redexgen/X/LI;-><init>(Ljava/lang/String;II)V

    sput-object v14, Lcom/facebook/ads/redexgen/X/LI;->A0J:Lcom/facebook/ads/redexgen/X/LI;

    .line 1924
    const/16 v2, 0x105

    const/16 v1, 0x12

    const/16 v0, 0x75

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/LI;->A01(III)Ljava/lang/String;

    move-result-object v1

    const/4 v0, 0x4

    new-instance v13, Lcom/facebook/ads/redexgen/X/LI;

    invoke-direct {v13, v1, v0, v0}, Lcom/facebook/ads/redexgen/X/LI;-><init>(Ljava/lang/String;II)V

    sput-object v13, Lcom/facebook/ads/redexgen/X/LI;->A0I:Lcom/facebook/ads/redexgen/X/LI;

    .line 1925
    sget-object v3, Lcom/facebook/ads/NativeAdBase$NativeComponentTag;->AD_ICON:Lcom/facebook/ads/NativeAdBase$NativeComponentTag;

    const/16 v2, 0x59

    const/16 v1, 0x10

    const/16 v0, 0x45

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/LI;->A01(III)Ljava/lang/String;

    move-result-object v1

    const/4 v0, 0x5

    new-instance v12, Lcom/facebook/ads/redexgen/X/LI;

    invoke-direct {v12, v1, v0, v0, v3}, Lcom/facebook/ads/redexgen/X/LI;-><init>(Ljava/lang/String;IILcom/facebook/ads/NativeAdBase$NativeComponentTag;)V

    sput-object v12, Lcom/facebook/ads/redexgen/X/LI;->A09:Lcom/facebook/ads/redexgen/X/LI;

    .line 1926
    sget-object v3, Lcom/facebook/ads/NativeAdBase$NativeComponentTag;->AD_TITLE:Lcom/facebook/ads/NativeAdBase$NativeComponentTag;

    const/16 v2, 0xc0

    const/16 v1, 0x11

    const/16 v0, 0x72

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/LI;->A01(III)Ljava/lang/String;

    move-result-object v1

    const/4 v0, 0x6

    new-instance v11, Lcom/facebook/ads/redexgen/X/LI;

    invoke-direct {v11, v1, v0, v0, v3}, Lcom/facebook/ads/redexgen/X/LI;-><init>(Ljava/lang/String;IILcom/facebook/ads/NativeAdBase$NativeComponentTag;)V

    sput-object v11, Lcom/facebook/ads/redexgen/X/LI;->A0E:Lcom/facebook/ads/redexgen/X/LI;

    .line 1927
    sget-object v3, Lcom/facebook/ads/NativeAdBase$NativeComponentTag;->AD_COVER_IMAGE:Lcom/facebook/ads/NativeAdBase$NativeComponentTag;

    const/16 v2, 0x42

    const/16 v1, 0x17

    const/16 v0, 0x42

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/LI;->A01(III)Ljava/lang/String;

    move-result-object v1

    const/4 v0, 0x7

    new-instance v10, Lcom/facebook/ads/redexgen/X/LI;

    invoke-direct {v10, v1, v0, v0, v3}, Lcom/facebook/ads/redexgen/X/LI;-><init>(Ljava/lang/String;IILcom/facebook/ads/NativeAdBase$NativeComponentTag;)V

    sput-object v10, Lcom/facebook/ads/redexgen/X/LI;->A08:Lcom/facebook/ads/redexgen/X/LI;

    .line 1928
    sget-object v3, Lcom/facebook/ads/NativeAdBase$NativeComponentTag;->AD_SUBTITLE:Lcom/facebook/ads/NativeAdBase$NativeComponentTag;

    const/16 v2, 0xac

    const/16 v1, 0x14

    const/16 v0, 0x3e

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/LI;->A01(III)Ljava/lang/String;

    move-result-object v1

    const/16 v0, 0x8

    new-instance v9, Lcom/facebook/ads/redexgen/X/LI;

    invoke-direct {v9, v1, v0, v0, v3}, Lcom/facebook/ads/redexgen/X/LI;-><init>(Ljava/lang/String;IILcom/facebook/ads/NativeAdBase$NativeComponentTag;)V

    sput-object v9, Lcom/facebook/ads/redexgen/X/LI;->A0D:Lcom/facebook/ads/redexgen/X/LI;

    .line 1929
    sget-object v3, Lcom/facebook/ads/NativeAdBase$NativeComponentTag;->AD_BODY:Lcom/facebook/ads/NativeAdBase$NativeComponentTag;

    const/4 v2, 0x0

    const/16 v1, 0x10

    const/16 v0, 0x71

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/LI;->A01(III)Ljava/lang/String;

    move-result-object v1

    const/16 v0, 0x9

    new-instance v8, Lcom/facebook/ads/redexgen/X/LI;

    invoke-direct {v8, v1, v0, v0, v3}, Lcom/facebook/ads/redexgen/X/LI;-><init>(Ljava/lang/String;IILcom/facebook/ads/NativeAdBase$NativeComponentTag;)V

    sput-object v8, Lcom/facebook/ads/redexgen/X/LI;->A05:Lcom/facebook/ads/redexgen/X/LI;

    .line 1930
    sget-object v3, Lcom/facebook/ads/NativeAdBase$NativeComponentTag;->AD_CALL_TO_ACTION:Lcom/facebook/ads/NativeAdBase$NativeComponentTag;

    const/16 v2, 0x10

    const/16 v1, 0x1a

    const/16 v0, 0x29

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/LI;->A01(III)Ljava/lang/String;

    move-result-object v1

    const/16 v0, 0xa

    new-instance v7, Lcom/facebook/ads/redexgen/X/LI;

    invoke-direct {v7, v1, v0, v0, v3}, Lcom/facebook/ads/redexgen/X/LI;-><init>(Ljava/lang/String;IILcom/facebook/ads/NativeAdBase$NativeComponentTag;)V

    sput-object v7, Lcom/facebook/ads/redexgen/X/LI;->A06:Lcom/facebook/ads/redexgen/X/LI;

    .line 1931
    sget-object v3, Lcom/facebook/ads/NativeAdBase$NativeComponentTag;->AD_SOCIAL_CONTEXT:Lcom/facebook/ads/NativeAdBase$NativeComponentTag;

    const/16 v2, 0x92

    const/16 v1, 0x1a

    const/16 v0, 0x5e

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/LI;->A01(III)Ljava/lang/String;

    move-result-object v1

    const/16 v0, 0xb

    new-instance v6, Lcom/facebook/ads/redexgen/X/LI;

    invoke-direct {v6, v1, v0, v0, v3}, Lcom/facebook/ads/redexgen/X/LI;-><init>(Ljava/lang/String;IILcom/facebook/ads/NativeAdBase$NativeComponentTag;)V

    sput-object v6, Lcom/facebook/ads/redexgen/X/LI;->A0C:Lcom/facebook/ads/redexgen/X/LI;

    .line 1932
    sget-object v3, Lcom/facebook/ads/NativeAdBase$NativeComponentTag;->AD_CHOICES_ICON:Lcom/facebook/ads/NativeAdBase$NativeComponentTag;

    const/16 v2, 0x2a

    const/16 v1, 0x18

    const/4 v0, 0x6

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/LI;->A01(III)Ljava/lang/String;

    move-result-object v1

    const/16 v0, 0xc

    new-instance v5, Lcom/facebook/ads/redexgen/X/LI;

    invoke-direct {v5, v1, v0, v0, v3}, Lcom/facebook/ads/redexgen/X/LI;-><init>(Ljava/lang/String;IILcom/facebook/ads/NativeAdBase$NativeComponentTag;)V

    sput-object v5, Lcom/facebook/ads/redexgen/X/LI;->A07:Lcom/facebook/ads/redexgen/X/LI;

    .line 1933
    sget-object v3, Lcom/facebook/ads/NativeAdBase$NativeComponentTag;->AD_MEDIA:Lcom/facebook/ads/NativeAdBase$NativeComponentTag;

    const/16 v2, 0x69

    const/16 v1, 0x11

    const/16 v0, 0x40

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/LI;->A01(III)Ljava/lang/String;

    move-result-object v2

    const/16 v1, 0xd

    new-instance v4, Lcom/facebook/ads/redexgen/X/LI;

    move-object v0, v3

    invoke-direct {v4, v2, v1, v1, v0}, Lcom/facebook/ads/redexgen/X/LI;-><init>(Ljava/lang/String;IILcom/facebook/ads/NativeAdBase$NativeComponentTag;)V

    sput-object v4, Lcom/facebook/ads/redexgen/X/LI;->A0A:Lcom/facebook/ads/redexgen/X/LI;

    .line 1934
    sget-object v16, Lcom/facebook/ads/NativeAdBase$NativeComponentTag;->AD_OPTIONS_VIEW:Lcom/facebook/ads/NativeAdBase$NativeComponentTag;

    const/16 v0, 0x7a

    const/16 v2, 0x18

    const/16 v1, 0x11

    move v0, v0

    invoke-static {v0, v2, v1}, Lcom/facebook/ads/redexgen/X/LI;->A01(III)Ljava/lang/String;

    move-result-object v1

    const/16 v2, 0xe

    const/16 v0, 0xc

    new-instance v15, Lcom/facebook/ads/redexgen/X/LI;

    move-object v3, v1

    move v2, v2

    move-object/from16 v1, v16

    move v0, v0

    invoke-direct {v15, v3, v2, v0, v1}, Lcom/facebook/ads/redexgen/X/LI;-><init>(Ljava/lang/String;IILcom/facebook/ads/NativeAdBase$NativeComponentTag;)V

    sput-object v15, Lcom/facebook/ads/redexgen/X/LI;->A0B:Lcom/facebook/ads/redexgen/X/LI;

    .line 1935
    const/16 v0, 0xf

    new-array v1, v0, [Lcom/facebook/ads/redexgen/X/LI;

    const/4 v0, 0x0

    aput-object v19, v1, v0

    const/4 v0, 0x1

    aput-object v18, v1, v0

    const/4 v0, 0x2

    aput-object v17, v1, v0

    const/4 v0, 0x3

    aput-object v14, v1, v0

    const/4 v0, 0x4

    aput-object v13, v1, v0

    const/4 v0, 0x5

    aput-object v12, v1, v0

    const/4 v0, 0x6

    aput-object v11, v1, v0

    const/4 v0, 0x7

    aput-object v10, v1, v0

    const/16 v0, 0x8

    aput-object v9, v1, v0

    const/16 v0, 0x9

    aput-object v8, v1, v0

    const/16 v0, 0xa

    aput-object v7, v1, v0

    const/16 v0, 0xb

    aput-object v6, v1, v0

    const/16 v0, 0xc

    aput-object v5, v1, v0

    const/16 v0, 0xd

    aput-object v4, v1, v0

    aput-object v15, v1, v2

    sput-object v1, Lcom/facebook/ads/redexgen/X/LI;->A04:[Lcom/facebook/ads/redexgen/X/LI;

    .line 1936
    const v0, -0x5f000001

    sput v0, Lcom/facebook/ads/redexgen/X/LI;->A02:I

    return-void
.end method

.method public constructor <init>(Ljava/lang/String;II)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I)V"
        }
    .end annotation

    .line 43318
    invoke-direct {p0, p1, p2}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    .line 43319
    iput p3, p0, Lcom/facebook/ads/redexgen/X/LI;->A00:I

    .line 43320
    const/4 v0, 0x0

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/LI;->A01:Lcom/facebook/ads/NativeAdBase$NativeComponentTag;

    .line 43321
    return-void
.end method

.method public constructor <init>(Ljava/lang/String;IILcom/facebook/ads/NativeAdBase$NativeComponentTag;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I",
            "Lcom/facebook/ads/NativeAdBase$NativeComponentTag;",
            ")V"
        }
    .end annotation

    .line 43322
    invoke-direct {p0, p1, p2}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    .line 43323
    iput p3, p0, Lcom/facebook/ads/redexgen/X/LI;->A00:I

    .line 43324
    iput-object p4, p0, Lcom/facebook/ads/redexgen/X/LI;->A01:Lcom/facebook/ads/NativeAdBase$NativeComponentTag;

    .line 43325
    return-void
.end method

.method public static A00(Lcom/facebook/ads/NativeAdBase$NativeComponentTag;)Lcom/facebook/ads/redexgen/X/LI;
    .locals 5

    .line 43326
    invoke-static {}, Lcom/facebook/ads/redexgen/X/LI;->values()[Lcom/facebook/ads/redexgen/X/LI;

    move-result-object v4

    array-length v3, v4

    const/4 v2, 0x0

    :goto_0
    if-ge v2, v3, :cond_1

    aget-object v1, v4, v2

    .line 43327
    .local v3, "value":Lcom/facebook/ads/redexgen/X/LI;
    iget-object v0, v1, Lcom/facebook/ads/redexgen/X/LI;->A01:Lcom/facebook/ads/NativeAdBase$NativeComponentTag;

    if-ne v0, p0, :cond_0

    .line 43328
    return-object v1

    .line 43329
    .end local v3    # "value":Lcom/facebook/ads/redexgen/X/LI;
    :cond_0
    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    .line 43330
    :cond_1
    const/4 v0, 0x0

    return-object v0
.end method

.method public static A01(III)Ljava/lang/String;
    .locals 2

    sget-object v1, Lcom/facebook/ads/redexgen/X/LI;->A03:[B

    add-int v0, p0, p1

    invoke-static {v1, p0, v0}, Ljava/util/Arrays;->copyOfRange([BII)[B

    move-result-object p0

    const/4 v1, 0x0

    :goto_0
    array-length v0, p0

    if-ge v1, v0, :cond_0

    aget-byte v0, p0, v1

    sub-int/2addr v0, p2

    add-int/lit8 v0, v0, -0x3d

    int-to-byte v0, v0

    aput-byte v0, p0, v1

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_0
    new-instance v0, Ljava/lang/String;

    invoke-direct {v0, p0}, Ljava/lang/String;-><init>([B)V

    return-object v0
.end method

.method public static A02()V
    .locals 1

    const/16 v0, 0x12f

    new-array v0, v0, [B

    fill-array-data v0, :array_0

    sput-object v0, Lcom/facebook/ads/redexgen/X/LI;->A03:[B

    return-void

    :array_0
    .array-data 1
        -0x9t
        -0x4t
        0x2t
        -0xdt
        0x0t
        -0x4t
        -0x11t
        -0x6t
        0xdt
        -0x11t
        -0xet
        0xdt
        -0x10t
        -0x3t
        -0xet
        0x7t
        -0x51t
        -0x4ct
        -0x46t
        -0x55t
        -0x48t
        -0x4ct
        -0x59t
        -0x4et
        -0x3bt
        -0x59t
        -0x56t
        -0x3bt
        -0x57t
        -0x59t
        -0x4et
        -0x4et
        -0x3bt
        -0x46t
        -0x4bt
        -0x3bt
        -0x59t
        -0x57t
        -0x46t
        -0x51t
        -0x4bt
        -0x4ct
        -0x74t
        -0x6ft
        -0x69t
        -0x78t
        -0x6bt
        -0x6ft
        -0x7ct
        -0x71t
        -0x5et
        -0x7ct
        -0x79t
        -0x5et
        -0x7at
        -0x75t
        -0x6et
        -0x74t
        -0x7at
        -0x78t
        -0x6at
        -0x5et
        -0x74t
        -0x7at
        -0x6et
        -0x6ft
        -0x38t
        -0x33t
        -0x2dt
        -0x3ct
        -0x2ft
        -0x33t
        -0x40t
        -0x35t
        -0x22t
        -0x40t
        -0x3dt
        -0x22t
        -0x3et
        -0x32t
        -0x2bt
        -0x3ct
        -0x2ft
        -0x22t
        -0x38t
        -0x34t
        -0x40t
        -0x3at
        -0x3ct
        -0x35t
        -0x30t
        -0x2at
        -0x39t
        -0x2ct
        -0x30t
        -0x3dt
        -0x32t
        -0x1ft
        -0x3dt
        -0x3at
        -0x1ft
        -0x35t
        -0x3bt
        -0x2ft
        -0x30t
        -0x3at
        -0x35t
        -0x2ft
        -0x3et
        -0x31t
        -0x35t
        -0x42t
        -0x37t
        -0x24t
        -0x42t
        -0x3ft
        -0x24t
        -0x36t
        -0x3et
        -0x3ft
        -0x3at
        -0x42t
        -0x69t
        -0x64t
        -0x5et
        -0x6dt
        -0x60t
        -0x64t
        -0x71t
        -0x66t
        -0x53t
        -0x71t
        -0x6et
        -0x53t
        -0x63t
        -0x62t
        -0x5et
        -0x69t
        -0x63t
        -0x64t
        -0x5ft
        -0x53t
        -0x5ct
        -0x69t
        -0x6dt
        -0x5bt
        -0x1ct
        -0x17t
        -0x11t
        -0x20t
        -0x13t
        -0x17t
        -0x24t
        -0x19t
        -0x6t
        -0x24t
        -0x21t
        -0x6t
        -0x12t
        -0x16t
        -0x22t
        -0x1ct
        -0x24t
        -0x19t
        -0x6t
        -0x22t
        -0x16t
        -0x17t
        -0x11t
        -0x20t
        -0xdt
        -0x11t
        -0x3ct
        -0x37t
        -0x31t
        -0x40t
        -0x33t
        -0x37t
        -0x44t
        -0x39t
        -0x26t
        -0x44t
        -0x41t
        -0x26t
        -0x32t
        -0x30t
        -0x43t
        -0x31t
        -0x3ct
        -0x31t
        -0x39t
        -0x40t
        -0x8t
        -0x3t
        0x3t
        -0xct
        0x1t
        -0x3t
        -0x10t
        -0x5t
        0xet
        -0x10t
        -0xdt
        0xet
        0x3t
        -0x8t
        0x3t
        -0x5t
        -0xct
        -0x60t
        -0x5bt
        -0x55t
        -0x64t
        -0x57t
        -0x5bt
        -0x68t
        -0x5dt
        -0x4at
        -0x68t
        -0x59t
        -0x60t
        -0x4at
        -0x55t
        -0x5at
        -0x5at
        -0x4at
        -0x5dt
        -0x5at
        -0x52t
        -0x16t
        -0x11t
        -0xbt
        -0x1at
        -0xdt
        -0x11t
        -0x1et
        -0x13t
        0x0t
        -0x11t
        -0x10t
        0x0t
        -0x1ct
        -0x13t
        -0x16t
        -0x1ct
        -0x14t
        -0x46t
        -0x41t
        -0x3bt
        -0x4at
        -0x3dt
        -0x41t
        -0x4et
        -0x43t
        -0x30t
        -0x41t
        -0x40t
        -0x30t
        -0x3bt
        -0x4et
        -0x48t
        -0x5t
        0x0t
        0x6t
        -0x9t
        0x4t
        0x0t
        -0xdt
        -0x2t
        0x11t
        0x0t
        0x7t
        -0x2t
        -0x2t
        0x11t
        0x8t
        -0x5t
        -0x9t
        0x9t
        -0x30t
        -0x2bt
        -0x25t
        -0x34t
        -0x27t
        -0x2bt
        -0x38t
        -0x2dt
        -0x1at
        -0x22t
        -0x27t
        -0x2at
        -0x2bt
        -0x32t
        -0x1at
        -0x25t
        -0x38t
        -0x32t
        -0x1at
        -0x36t
        -0x2dt
        -0x38t
        -0x26t
        -0x26t
    .end array-data
.end method

.method public static A03(Landroid/view/View;Lcom/facebook/ads/NativeAdBase$NativeComponentTag;)V
    .locals 2

    .line 43331
    invoke-static {p1}, Lcom/facebook/ads/redexgen/X/LI;->A00(Lcom/facebook/ads/NativeAdBase$NativeComponentTag;)Lcom/facebook/ads/redexgen/X/LI;

    move-result-object v0

    .line 43332
    .local v0, "internalTag":Lcom/facebook/ads/redexgen/X/LI;
    if-eqz p0, :cond_0

    if-eqz v0, :cond_0

    sget v1, Landroid/os/Build$VERSION;->SDK_INT:I

    const/4 v0, 0x4

    if-le v1, v0, :cond_0

    .line 43333
    sget v0, Lcom/facebook/ads/redexgen/X/LI;->A02:I

    invoke-virtual {p0, v0, p1}, Landroid/view/View;->setTag(ILjava/lang/Object;)V

    .line 43334
    :cond_0
    return-void
.end method

.method public static A04(Landroid/view/View;Lcom/facebook/ads/redexgen/X/LI;)V
    .locals 2

    .line 43335
    if-eqz p0, :cond_0

    if-eqz p1, :cond_0

    sget v1, Landroid/os/Build$VERSION;->SDK_INT:I

    const/4 v0, 0x4

    if-le v1, v0, :cond_0

    .line 43336
    sget v0, Lcom/facebook/ads/redexgen/X/LI;->A02:I

    invoke-virtual {p0, v0, p1}, Landroid/view/View;->setTag(ILjava/lang/Object;)V

    .line 43337
    :cond_0
    return-void
.end method

.method public static valueOf(Ljava/lang/String;)Lcom/facebook/ads/redexgen/X/LI;
    .locals 1

    .line 43339
    const-class v0, Lcom/facebook/ads/redexgen/X/LI;

    invoke-static {v0, p0}, Ljava/lang/Enum;->valueOf(Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Enum;

    move-result-object v0

    check-cast v0, Lcom/facebook/ads/redexgen/X/LI;

    return-object v0
.end method

.method public static values()[Lcom/facebook/ads/redexgen/X/LI;
    .locals 1

    .line 43340
    sget-object v0, Lcom/facebook/ads/redexgen/X/LI;->A04:[Lcom/facebook/ads/redexgen/X/LI;

    invoke-virtual {v0}, [Ljava/lang/Object;->clone()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [Lcom/facebook/ads/redexgen/X/LI;

    return-object v0
.end method


# virtual methods
.method public final A05()I
    .locals 1

    .line 43338
    iget v0, p0, Lcom/facebook/ads/redexgen/X/LI;->A00:I

    return v0
.end method
