.class public final Lcom/facebook/ads/redexgen/X/Lh;
.super Lcom/facebook/ads/redexgen/X/KT;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/facebook/ads/redexgen/X/8N;->A00(Lcom/facebook/ads/redexgen/X/MY;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field public final synthetic A00:Lcom/facebook/ads/redexgen/X/8N;


# direct methods
.method public constructor <init>(Lcom/facebook/ads/redexgen/X/8N;)V
    .locals 0

    .line 43864
    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/Lh;->A00:Lcom/facebook/ads/redexgen/X/8N;

    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/KT;-><init>()V

    return-void
.end method


# virtual methods
.method public final A06()V
    .locals 2

    .line 43865
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Lh;->A00:Lcom/facebook/ads/redexgen/X/8N;

    iget-object v0, v0, Lcom/facebook/ads/redexgen/X/8N;->A00:Lcom/facebook/ads/redexgen/X/LX;

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/LX;->A04(Lcom/facebook/ads/redexgen/X/LX;)V

    .line 43866
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Lh;->A00:Lcom/facebook/ads/redexgen/X/8N;

    iget-object v1, v0, Lcom/facebook/ads/redexgen/X/8N;->A00:Lcom/facebook/ads/redexgen/X/LX;

    const/4 v0, 0x1

    invoke-static {v1, v0, v0}, Lcom/facebook/ads/redexgen/X/LX;->A05(Lcom/facebook/ads/redexgen/X/LX;ZZ)V

    .line 43867
    return-void
.end method
