.class public final Lcom/facebook/ads/redexgen/X/Lm;
.super Lcom/facebook/ads/redexgen/X/KT;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/facebook/ads/redexgen/X/8P;->A00(Lcom/facebook/ads/redexgen/X/O8;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field public final synthetic A00:Lcom/facebook/ads/redexgen/X/8P;


# direct methods
.method public constructor <init>(Lcom/facebook/ads/redexgen/X/8P;)V
    .locals 0

    .line 43895
    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/Lm;->A00:Lcom/facebook/ads/redexgen/X/8P;

    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/KT;-><init>()V

    return-void
.end method


# virtual methods
.method public final A06()V
    .locals 2

    .line 43896
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Lm;->A00:Lcom/facebook/ads/redexgen/X/8P;

    iget-object v0, v0, Lcom/facebook/ads/redexgen/X/8P;->A00:Lcom/facebook/ads/redexgen/X/LX;

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/LX;->A04(Lcom/facebook/ads/redexgen/X/LX;)V

    .line 43897
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Lm;->A00:Lcom/facebook/ads/redexgen/X/8P;

    iget-object v1, v0, Lcom/facebook/ads/redexgen/X/8P;->A00:Lcom/facebook/ads/redexgen/X/LX;

    const/4 v0, 0x1

    invoke-static {v1, v0, v0}, Lcom/facebook/ads/redexgen/X/LX;->A05(Lcom/facebook/ads/redexgen/X/LX;ZZ)V

    .line 43898
    return-void
.end method
