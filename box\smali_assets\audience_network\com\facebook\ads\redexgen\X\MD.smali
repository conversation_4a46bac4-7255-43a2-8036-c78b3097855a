.class public interface abstract Lcom/facebook/ads/redexgen/X/MD;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/facebook/ads/redexgen/X/MC;
    }
.end annotation


# virtual methods
.method public abstract A9Q(Landroid/content/Intent;Landroid/os/Bundle;Lcom/facebook/ads/redexgen/X/5V;)V
.end method

.method public abstract ACW(Z)V
.end method

.method public abstract ACu(Z)V
.end method

.method public abstract AFT(Landroid/os/Bundle;)V
.end method

.method public abstract getCurrentClientToken()Ljava/lang/String;
.end method

.method public abstract onActivityResult(IILandroid/content/Intent;)Z
.end method

.method public abstract onDestroy()V
.end method
