.class public interface abstract Lcom/facebook/ads/redexgen/X/Mj;
.super Ljava/lang/Object;
.source ""


# virtual methods
.method public abstract A5s()Ljava/lang/String;
.end method

.method public abstract A6R()Ljava/lang/String;
.end method

.method public abstract A6u()Ljava/lang/String;
.end method

.method public abstract A6y()Ljava/lang/String;
.end method

.method public abstract A7B()Ljava/lang/String;
.end method

.method public abstract A7f()Ljava/lang/String;
.end method

.method public abstract A7w()Ljava/lang/String;
.end method

.method public abstract A7x()Ljava/lang/String;
.end method
