.class public interface abstract Lcom/facebook/ads/redexgen/X/NR;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/facebook/ads/redexgen/X/U1;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "InterceptRedirectRequest"
.end annotation


# virtual methods
.method public abstract A9D(Ljava/lang/String;)Z
.end method
