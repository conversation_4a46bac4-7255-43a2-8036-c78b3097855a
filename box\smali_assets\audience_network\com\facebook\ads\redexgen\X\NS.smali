.class public interface abstract Lcom/facebook/ads/redexgen/X/NS;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/facebook/ads/redexgen/X/U1;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "Listener"
.end annotation


# virtual methods
.method public abstract ACT(Ljava/lang/String;)V
.end method

.method public abstract ACV(Ljava/lang/String;)V
.end method

.method public abstract ACn(I)V
.end method

.method public abstract ACq(Ljava/lang/String;)V
.end method

.method public abstract ACs()V
.end method
