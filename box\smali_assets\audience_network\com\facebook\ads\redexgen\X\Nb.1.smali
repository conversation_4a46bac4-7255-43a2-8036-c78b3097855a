.class public interface abstract Lcom/facebook/ads/redexgen/X/Nb;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/facebook/ads/redexgen/X/Ts;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "AdWebViewListener"
.end annotation


# virtual methods
.method public abstract AAn()V
.end method

.method public abstract AB4(Ljava/lang/String;Ljava/util/Map;)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ">;)V"
        }
    .end annotation
.end method

.method public abstract ABW(ILjava/lang/String;)V
.end method

.method public abstract ABj()V
.end method

.method public abstract ACU()V
.end method

.method public abstract ADj()V
.end method
