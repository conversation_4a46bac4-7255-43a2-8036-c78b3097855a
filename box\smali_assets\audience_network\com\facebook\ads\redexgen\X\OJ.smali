.class public final synthetic Lcom/facebook/ads/redexgen/X/OJ;
.super Ljava/lang/Object;
.source ""

# interfaces
.implements Landroid/view/View$OnClickListener;


# instance fields
.field public final synthetic A00:Lcom/facebook/ads/redexgen/X/Tf;


# direct methods
.method public synthetic constructor <init>(Lcom/facebook/ads/redexgen/X/Tf;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/OJ;->A00:Lcom/facebook/ads/redexgen/X/Tf;

    return-void
.end method


# virtual methods
.method public final onClick(Landroid/view/View;)V
    .locals 1

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/OJ;->A00:Lcom/facebook/ads/redexgen/X/Tf;

    invoke-virtual {v0, p1}, Lcom/facebook/ads/redexgen/X/Tf;->A13(Landroid/view/View;)V

    return-void
.end method
