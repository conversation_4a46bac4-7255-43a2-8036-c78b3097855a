.class public final Lcom/facebook/ads/redexgen/X/OO;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/facebook/ads/redexgen/X/OP;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "InterstitialLayoutParamsBuilder"
.end annotation


# instance fields
.field public A00:Z

.field public A01:I

.field public A02:I

.field public A03:Landroid/view/View;

.field public A04:Lcom/facebook/ads/redexgen/X/V2;

.field public A05:Lcom/facebook/ads/redexgen/X/MB;

.field public A06:Lcom/facebook/ads/redexgen/X/KP;

.field public final A07:Landroid/view/View;

.field public final A08:Lcom/facebook/ads/redexgen/X/b5;

.field public final A09:Lcom/facebook/ads/redexgen/X/Yn;

.field public final A0A:Lcom/facebook/ads/redexgen/X/J2;

.field public final A0B:Lcom/facebook/ads/redexgen/X/Lg;

.field public final A0C:Lcom/facebook/ads/redexgen/X/MC;

.field public final A0D:Lcom/facebook/ads/redexgen/X/RE;


# direct methods
.method public constructor <init>(Lcom/facebook/ads/redexgen/X/Yn;Lcom/facebook/ads/redexgen/X/J2;Lcom/facebook/ads/redexgen/X/MC;Lcom/facebook/ads/redexgen/X/b5;Landroid/view/View;Lcom/facebook/ads/redexgen/X/RE;Lcom/facebook/ads/redexgen/X/Lg;)V
    .locals 1

    .line 47115
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 47116
    const/4 v0, 0x0

    iput v0, p0, Lcom/facebook/ads/redexgen/X/OO;->A02:I

    .line 47117
    const/4 v0, 0x1

    iput v0, p0, Lcom/facebook/ads/redexgen/X/OO;->A01:I

    .line 47118
    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/OO;->A09:Lcom/facebook/ads/redexgen/X/Yn;

    .line 47119
    iput-object p2, p0, Lcom/facebook/ads/redexgen/X/OO;->A0A:Lcom/facebook/ads/redexgen/X/J2;

    .line 47120
    iput-object p3, p0, Lcom/facebook/ads/redexgen/X/OO;->A0C:Lcom/facebook/ads/redexgen/X/MC;

    .line 47121
    iput-object p4, p0, Lcom/facebook/ads/redexgen/X/OO;->A08:Lcom/facebook/ads/redexgen/X/b5;

    .line 47122
    iput-object p5, p0, Lcom/facebook/ads/redexgen/X/OO;->A07:Landroid/view/View;

    .line 47123
    iput-object p6, p0, Lcom/facebook/ads/redexgen/X/OO;->A0D:Lcom/facebook/ads/redexgen/X/RE;

    .line 47124
    iput-object p7, p0, Lcom/facebook/ads/redexgen/X/OO;->A0B:Lcom/facebook/ads/redexgen/X/Lg;

    .line 47125
    return-void
.end method

.method public static synthetic A00(Lcom/facebook/ads/redexgen/X/OO;)I
    .locals 0

    .line 47126
    iget p0, p0, Lcom/facebook/ads/redexgen/X/OO;->A02:I

    return p0
.end method

.method public static synthetic A01(Lcom/facebook/ads/redexgen/X/OO;)I
    .locals 0

    .line 47127
    iget p0, p0, Lcom/facebook/ads/redexgen/X/OO;->A01:I

    return p0
.end method

.method public static synthetic A02(Lcom/facebook/ads/redexgen/X/OO;)Landroid/view/View;
    .locals 0

    .line 47128
    iget-object p0, p0, Lcom/facebook/ads/redexgen/X/OO;->A03:Landroid/view/View;

    return-object p0
.end method

.method public static synthetic A03(Lcom/facebook/ads/redexgen/X/OO;)Landroid/view/View;
    .locals 0

    .line 47129
    iget-object p0, p0, Lcom/facebook/ads/redexgen/X/OO;->A07:Landroid/view/View;

    return-object p0
.end method

.method public static synthetic A04(Lcom/facebook/ads/redexgen/X/OO;)Lcom/facebook/ads/redexgen/X/b5;
    .locals 0

    .line 47130
    iget-object p0, p0, Lcom/facebook/ads/redexgen/X/OO;->A08:Lcom/facebook/ads/redexgen/X/b5;

    return-object p0
.end method

.method public static synthetic A05(Lcom/facebook/ads/redexgen/X/OO;)Lcom/facebook/ads/redexgen/X/Yn;
    .locals 0

    .line 47131
    iget-object p0, p0, Lcom/facebook/ads/redexgen/X/OO;->A09:Lcom/facebook/ads/redexgen/X/Yn;

    return-object p0
.end method

.method public static synthetic A06(Lcom/facebook/ads/redexgen/X/OO;)Lcom/facebook/ads/redexgen/X/J2;
    .locals 0

    .line 47132
    iget-object p0, p0, Lcom/facebook/ads/redexgen/X/OO;->A0A:Lcom/facebook/ads/redexgen/X/J2;

    return-object p0
.end method

.method public static synthetic A07(Lcom/facebook/ads/redexgen/X/OO;)Lcom/facebook/ads/redexgen/X/V2;
    .locals 0

    .line 47133
    iget-object p0, p0, Lcom/facebook/ads/redexgen/X/OO;->A04:Lcom/facebook/ads/redexgen/X/V2;

    return-object p0
.end method

.method public static synthetic A08(Lcom/facebook/ads/redexgen/X/OO;)Lcom/facebook/ads/redexgen/X/Lg;
    .locals 0

    .line 47134
    iget-object p0, p0, Lcom/facebook/ads/redexgen/X/OO;->A0B:Lcom/facebook/ads/redexgen/X/Lg;

    return-object p0
.end method

.method public static synthetic A09(Lcom/facebook/ads/redexgen/X/OO;)Lcom/facebook/ads/redexgen/X/MB;
    .locals 0

    .line 47135
    iget-object p0, p0, Lcom/facebook/ads/redexgen/X/OO;->A05:Lcom/facebook/ads/redexgen/X/MB;

    return-object p0
.end method

.method public static synthetic A0A(Lcom/facebook/ads/redexgen/X/OO;)Lcom/facebook/ads/redexgen/X/MC;
    .locals 0

    .line 47136
    iget-object p0, p0, Lcom/facebook/ads/redexgen/X/OO;->A0C:Lcom/facebook/ads/redexgen/X/MC;

    return-object p0
.end method

.method public static synthetic A0B(Lcom/facebook/ads/redexgen/X/OO;)Lcom/facebook/ads/redexgen/X/KP;
    .locals 0

    .line 47137
    iget-object p0, p0, Lcom/facebook/ads/redexgen/X/OO;->A06:Lcom/facebook/ads/redexgen/X/KP;

    return-object p0
.end method

.method public static synthetic A0C(Lcom/facebook/ads/redexgen/X/OO;)Lcom/facebook/ads/redexgen/X/RE;
    .locals 0

    .line 47138
    iget-object p0, p0, Lcom/facebook/ads/redexgen/X/OO;->A0D:Lcom/facebook/ads/redexgen/X/RE;

    return-object p0
.end method


# virtual methods
.method public final A0D(I)Lcom/facebook/ads/redexgen/X/OO;
    .locals 0

    .line 47139
    iput p1, p0, Lcom/facebook/ads/redexgen/X/OO;->A01:I

    .line 47140
    return-object p0
.end method

.method public final A0E(I)Lcom/facebook/ads/redexgen/X/OO;
    .locals 0

    .line 47141
    iput p1, p0, Lcom/facebook/ads/redexgen/X/OO;->A02:I

    .line 47142
    return-object p0
.end method

.method public final A0F(Landroid/view/View;)Lcom/facebook/ads/redexgen/X/OO;
    .locals 0

    .line 47143
    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/OO;->A03:Landroid/view/View;

    .line 47144
    return-object p0
.end method

.method public final A0G(Lcom/facebook/ads/redexgen/X/V2;)Lcom/facebook/ads/redexgen/X/OO;
    .locals 0

    .line 47145
    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/OO;->A04:Lcom/facebook/ads/redexgen/X/V2;

    .line 47146
    return-object p0
.end method

.method public final A0H(Lcom/facebook/ads/redexgen/X/MB;)Lcom/facebook/ads/redexgen/X/OO;
    .locals 0

    .line 47147
    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/OO;->A05:Lcom/facebook/ads/redexgen/X/MB;

    .line 47148
    return-object p0
.end method

.method public final A0I(Lcom/facebook/ads/redexgen/X/KP;)Lcom/facebook/ads/redexgen/X/OO;
    .locals 0

    .line 47149
    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/OO;->A06:Lcom/facebook/ads/redexgen/X/KP;

    .line 47150
    return-object p0
.end method

.method public final A0J(Z)Lcom/facebook/ads/redexgen/X/OO;
    .locals 0

    .line 47151
    iput-boolean p1, p0, Lcom/facebook/ads/redexgen/X/OO;->A00:Z

    .line 47152
    return-object p0
.end method

.method public final A0K()Lcom/facebook/ads/redexgen/X/OP;
    .locals 2

    .line 47153
    const/4 v1, 0x0

    new-instance v0, Lcom/facebook/ads/redexgen/X/OP;

    invoke-direct {v0, p0, v1}, Lcom/facebook/ads/redexgen/X/OP;-><init>(Lcom/facebook/ads/redexgen/X/OO;Lcom/facebook/ads/redexgen/X/ON;)V

    return-object v0
.end method
