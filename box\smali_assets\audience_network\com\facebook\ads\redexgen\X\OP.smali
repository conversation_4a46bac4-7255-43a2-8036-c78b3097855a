.class public final Lcom/facebook/ads/redexgen/X/OP;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/facebook/ads/redexgen/X/OO;
    }
.end annotation


# instance fields
.field public final A00:I

.field public final A01:I

.field public final A02:Landroid/view/View;

.field public final A03:Landroid/view/View;

.field public final A04:Lcom/facebook/ads/redexgen/X/b5;

.field public final A05:Lcom/facebook/ads/redexgen/X/Yn;

.field public final A06:Lcom/facebook/ads/redexgen/X/J2;

.field public final A07:Lcom/facebook/ads/redexgen/X/V2;

.field public final A08:Lcom/facebook/ads/redexgen/X/Lg;

.field public final A09:Lcom/facebook/ads/redexgen/X/MB;

.field public final A0A:Lcom/facebook/ads/redexgen/X/MC;

.field public final A0B:Lcom/facebook/ads/redexgen/X/KP;

.field public final A0C:Lcom/facebook/ads/redexgen/X/RE;

.field public final A0D:Z


# direct methods
.method public constructor <init>(Lcom/facebook/ads/redexgen/X/OO;)V
    .locals 1

    .line 47154
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 47155
    invoke-static {p1}, Lcom/facebook/ads/redexgen/X/OO;->A05(Lcom/facebook/ads/redexgen/X/OO;)Lcom/facebook/ads/redexgen/X/Yn;

    move-result-object v0

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/OP;->A05:Lcom/facebook/ads/redexgen/X/Yn;

    .line 47156
    invoke-static {p1}, Lcom/facebook/ads/redexgen/X/OO;->A06(Lcom/facebook/ads/redexgen/X/OO;)Lcom/facebook/ads/redexgen/X/J2;

    move-result-object v0

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/OP;->A06:Lcom/facebook/ads/redexgen/X/J2;

    .line 47157
    invoke-static {p1}, Lcom/facebook/ads/redexgen/X/OO;->A0A(Lcom/facebook/ads/redexgen/X/OO;)Lcom/facebook/ads/redexgen/X/MC;

    move-result-object v0

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/OP;->A0A:Lcom/facebook/ads/redexgen/X/MC;

    .line 47158
    invoke-static {p1}, Lcom/facebook/ads/redexgen/X/OO;->A04(Lcom/facebook/ads/redexgen/X/OO;)Lcom/facebook/ads/redexgen/X/b5;

    move-result-object v0

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/OP;->A04:Lcom/facebook/ads/redexgen/X/b5;

    .line 47159
    invoke-static {p1}, Lcom/facebook/ads/redexgen/X/OO;->A03(Lcom/facebook/ads/redexgen/X/OO;)Landroid/view/View;

    move-result-object v0

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/OP;->A02:Landroid/view/View;

    .line 47160
    invoke-static {p1}, Lcom/facebook/ads/redexgen/X/OO;->A0C(Lcom/facebook/ads/redexgen/X/OO;)Lcom/facebook/ads/redexgen/X/RE;

    move-result-object v0

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/OP;->A0C:Lcom/facebook/ads/redexgen/X/RE;

    .line 47161
    invoke-static {p1}, Lcom/facebook/ads/redexgen/X/OO;->A08(Lcom/facebook/ads/redexgen/X/OO;)Lcom/facebook/ads/redexgen/X/Lg;

    move-result-object v0

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/OP;->A08:Lcom/facebook/ads/redexgen/X/Lg;

    .line 47162
    invoke-static {p1}, Lcom/facebook/ads/redexgen/X/OO;->A00(Lcom/facebook/ads/redexgen/X/OO;)I

    move-result v0

    iput v0, p0, Lcom/facebook/ads/redexgen/X/OP;->A01:I

    .line 47163
    invoke-static {p1}, Lcom/facebook/ads/redexgen/X/OO;->A01(Lcom/facebook/ads/redexgen/X/OO;)I

    move-result v0

    iput v0, p0, Lcom/facebook/ads/redexgen/X/OP;->A00:I

    .line 47164
    invoke-static {p1}, Lcom/facebook/ads/redexgen/X/OO;->A0B(Lcom/facebook/ads/redexgen/X/OO;)Lcom/facebook/ads/redexgen/X/KP;

    move-result-object v0

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/OP;->A0B:Lcom/facebook/ads/redexgen/X/KP;

    .line 47165
    invoke-static {p1}, Lcom/facebook/ads/redexgen/X/OO;->A02(Lcom/facebook/ads/redexgen/X/OO;)Landroid/view/View;

    move-result-object v0

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/OP;->A03:Landroid/view/View;

    .line 47166
    invoke-static {p1}, Lcom/facebook/ads/redexgen/X/OO;->A09(Lcom/facebook/ads/redexgen/X/OO;)Lcom/facebook/ads/redexgen/X/MB;

    move-result-object v0

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/OP;->A09:Lcom/facebook/ads/redexgen/X/MB;

    .line 47167
    invoke-static {p1}, Lcom/facebook/ads/redexgen/X/OO;->A07(Lcom/facebook/ads/redexgen/X/OO;)Lcom/facebook/ads/redexgen/X/V2;

    move-result-object v0

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/OP;->A07:Lcom/facebook/ads/redexgen/X/V2;

    .line 47168
    iget-boolean v0, p1, Lcom/facebook/ads/redexgen/X/OO;->A00:Z

    iput-boolean v0, p0, Lcom/facebook/ads/redexgen/X/OP;->A0D:Z

    .line 47169
    return-void
.end method

.method public synthetic constructor <init>(Lcom/facebook/ads/redexgen/X/OO;Lcom/facebook/ads/redexgen/X/ON;)V
    .locals 0

    .line 47170
    invoke-direct {p0, p1}, Lcom/facebook/ads/redexgen/X/OP;-><init>(Lcom/facebook/ads/redexgen/X/OO;)V

    return-void
.end method


# virtual methods
.method public final A00()I
    .locals 1

    .line 47171
    iget v0, p0, Lcom/facebook/ads/redexgen/X/OP;->A00:I

    return v0
.end method

.method public final A01()I
    .locals 1

    .line 47172
    iget v0, p0, Lcom/facebook/ads/redexgen/X/OP;->A01:I

    return v0
.end method

.method public final A02()Landroid/view/View;
    .locals 1

    .line 47173
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/OP;->A02:Landroid/view/View;

    return-object v0
.end method

.method public final A03()Landroid/view/View;
    .locals 1

    .line 47174
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/OP;->A03:Landroid/view/View;

    return-object v0
.end method

.method public final A04()Lcom/facebook/ads/redexgen/X/b5;
    .locals 1

    .line 47175
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/OP;->A04:Lcom/facebook/ads/redexgen/X/b5;

    return-object v0
.end method

.method public final A05()Lcom/facebook/ads/redexgen/X/Yn;
    .locals 1

    .line 47176
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/OP;->A05:Lcom/facebook/ads/redexgen/X/Yn;

    return-object v0
.end method

.method public final A06()Lcom/facebook/ads/redexgen/X/J2;
    .locals 1

    .line 47177
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/OP;->A06:Lcom/facebook/ads/redexgen/X/J2;

    return-object v0
.end method

.method public final A07()Lcom/facebook/ads/redexgen/X/Lg;
    .locals 1

    .line 47178
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/OP;->A08:Lcom/facebook/ads/redexgen/X/Lg;

    return-object v0
.end method

.method public final A08()Lcom/facebook/ads/redexgen/X/MB;
    .locals 1

    .line 47179
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/OP;->A09:Lcom/facebook/ads/redexgen/X/MB;

    return-object v0
.end method

.method public final A09()Lcom/facebook/ads/redexgen/X/MC;
    .locals 1

    .line 47180
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/OP;->A0A:Lcom/facebook/ads/redexgen/X/MC;

    return-object v0
.end method

.method public final A0A()Lcom/facebook/ads/redexgen/X/KP;
    .locals 1

    .line 47181
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/OP;->A0B:Lcom/facebook/ads/redexgen/X/KP;

    return-object v0
.end method

.method public final A0B()Lcom/facebook/ads/redexgen/X/RE;
    .locals 1

    .line 47182
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/OP;->A0C:Lcom/facebook/ads/redexgen/X/RE;

    return-object v0
.end method

.method public final A0C()Z
    .locals 1

    .line 47183
    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/OP;->A0D:Z

    return v0
.end method
