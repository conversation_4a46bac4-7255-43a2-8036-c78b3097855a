.class public interface abstract Lcom/facebook/ads/redexgen/X/Op;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/facebook/ads/redexgen/X/TS;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "CardChangeLayoutListener"
.end annotation


# virtual methods
.method public abstract AGp(I)V
.end method
