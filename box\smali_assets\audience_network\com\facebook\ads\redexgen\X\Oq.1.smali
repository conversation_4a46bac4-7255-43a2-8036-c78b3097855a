.class public interface abstract Lcom/facebook/ads/redexgen/X/Oq;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/facebook/ads/redexgen/X/TS;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "OnAssetsLoadedListener"
.end annotation


# virtual methods
.method public abstract AAn()V
.end method
