.class public interface abstract Lcom/facebook/ads/redexgen/X/Ot;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/facebook/ads/redexgen/X/TS;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "VolumeController"
.end annotation


# virtual methods
.method public abstract getVolume()F
.end method

.method public abstract setVolume(F)V
.end method
