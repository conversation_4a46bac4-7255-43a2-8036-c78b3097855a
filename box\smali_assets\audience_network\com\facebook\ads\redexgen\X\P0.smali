.class public interface abstract Lcom/facebook/ads/redexgen/X/P0;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/facebook/ads/redexgen/X/B0;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "NativeDSLListener"
.end annotation


# virtual methods
.method public abstract A9L()V
.end method

.method public abstract ABO(Lcom/facebook/ads/redexgen/X/B0;)V
.end method

.method public abstract ABd()V
.end method

.method public abstract ADL(Landroid/view/View;Landroid/view/MotionEvent;)V
.end method
