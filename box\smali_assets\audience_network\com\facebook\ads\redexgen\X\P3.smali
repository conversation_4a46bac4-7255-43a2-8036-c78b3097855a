.class public final Lcom/facebook/ads/redexgen/X/P3;
.super Landroid/widget/FrameLayout;
.source ""


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/facebook/ads/redexgen/X/P2;
    }
.end annotation


# static fields
.field public static A0L:[B

.field public static A0M:[Ljava/lang/String;


# instance fields
.field public A00:Lcom/facebook/ads/redexgen/X/QM;

.field public A01:Z

.field public A02:Z

.field public A03:Z

.field public A04:Z

.field public final A05:Lcom/facebook/ads/redexgen/X/b5;

.field public final A06:Lcom/facebook/ads/redexgen/X/6c;

.field public final A07:Lcom/facebook/ads/redexgen/X/Yn;

.field public final A08:Lcom/facebook/ads/redexgen/X/JA;

.field public final A09:Lcom/facebook/ads/redexgen/X/MC;

.field public final A0A:Lcom/facebook/ads/redexgen/X/Mj;

.field public final A0B:Lcom/facebook/ads/redexgen/X/P2;

.field public final A0C:Lcom/facebook/ads/redexgen/X/SF;

.field public final A0D:Lcom/facebook/ads/redexgen/X/SA;

.field public final A0E:Lcom/facebook/ads/redexgen/X/9I;

.field public final A0F:Lcom/facebook/ads/redexgen/X/Pu;

.field public final A0G:Lcom/facebook/ads/redexgen/X/Ps;

.field public final A0H:Lcom/facebook/ads/redexgen/X/On;

.field public final A0I:Lcom/facebook/ads/redexgen/X/O7;

.field public final A0J:Lcom/facebook/ads/redexgen/X/NX;

.field public final A0K:Lcom/facebook/ads/redexgen/X/NQ;


# direct methods
.method public static constructor <clinit>()V
    .locals 3

    .line 2134
    const/16 v0, 0x8

    new-array v2, v0, [Ljava/lang/String;

    const/4 v1, 0x0

    const-string v0, "tg98T7YqorVxljyy5rC9rRviyjZQ3PWt"

    aput-object v0, v2, v1

    const/4 v1, 0x1

    const-string v0, "vfHBQfbeXMWpx0Lt9J2p4bolhXFdVoUD"

    aput-object v0, v2, v1

    const/4 v1, 0x2

    const-string v0, "nuD8gLvjahybAxP4K7AI7EnQPexhi40p"

    aput-object v0, v2, v1

    const/4 v1, 0x3

    const-string v0, "Mle9DIn2BhVQrDGkT0Ym6c4cXgC65WVc"

    aput-object v0, v2, v1

    const/4 v1, 0x4

    const-string v0, "K7R1lop0AG70QQAdOPTNlaEDkQeUTiOI"

    aput-object v0, v2, v1

    const/4 v1, 0x5

    const-string v0, "yRh0uKXr8zSd48KJrOgjFYKLFGGCRN1w"

    aput-object v0, v2, v1

    const/4 v1, 0x6

    const-string v0, "QQOq4rDYvkiUVU0ErOwgNbjJJCuazTiH"

    aput-object v0, v2, v1

    const/4 v1, 0x7

    const-string v0, "YmsKh0Z7MVbEzdUA2tq2qo4jdnYDOoVM"

    aput-object v0, v2, v1

    sput-object v2, Lcom/facebook/ads/redexgen/X/P3;->A0M:[Ljava/lang/String;

    invoke-static {}, Lcom/facebook/ads/redexgen/X/P3;->A0C()V

    return-void
.end method

.method public constructor <init>(Lcom/facebook/ads/redexgen/X/Yn;Lcom/facebook/ads/redexgen/X/J2;Lcom/facebook/ads/redexgen/X/b5;Lcom/facebook/ads/redexgen/X/6c;Lcom/facebook/ads/redexgen/X/JA;Lcom/facebook/ads/redexgen/X/MC;Lcom/facebook/ads/redexgen/X/Mj;Lcom/facebook/ads/redexgen/X/P2;)V
    .locals 17

    .line 47386
    move-object/from16 v2, p0

    move-object v2, v2

    move-object/from16 v3, p1

    invoke-direct {v2, v3}, Landroid/widget/FrameLayout;-><init>(Landroid/content/Context;)V

    .line 47387
    new-instance v12, Lcom/facebook/ads/redexgen/X/At;

    invoke-direct {v12, v2}, Lcom/facebook/ads/redexgen/X/At;-><init>(Lcom/facebook/ads/redexgen/X/P3;)V

    iput-object v12, v2, Lcom/facebook/ads/redexgen/X/P3;->A0J:Lcom/facebook/ads/redexgen/X/NX;

    .line 47388
    new-instance v11, Lcom/facebook/ads/redexgen/X/Aq;

    invoke-direct {v11, v2}, Lcom/facebook/ads/redexgen/X/Aq;-><init>(Lcom/facebook/ads/redexgen/X/P3;)V

    iput-object v11, v2, Lcom/facebook/ads/redexgen/X/P3;->A0I:Lcom/facebook/ads/redexgen/X/O7;

    .line 47389
    new-instance v10, Lcom/facebook/ads/redexgen/X/AZ;

    invoke-direct {v10, v2}, Lcom/facebook/ads/redexgen/X/AZ;-><init>(Lcom/facebook/ads/redexgen/X/P3;)V

    iput-object v10, v2, Lcom/facebook/ads/redexgen/X/P3;->A0H:Lcom/facebook/ads/redexgen/X/On;

    .line 47390
    new-instance v9, Lcom/facebook/ads/redexgen/X/AY;

    invoke-direct {v9, v2}, Lcom/facebook/ads/redexgen/X/AY;-><init>(Lcom/facebook/ads/redexgen/X/P3;)V

    iput-object v9, v2, Lcom/facebook/ads/redexgen/X/P3;->A0K:Lcom/facebook/ads/redexgen/X/NQ;

    .line 47391
    new-instance v8, Lcom/facebook/ads/redexgen/X/AX;

    invoke-direct {v8, v2}, Lcom/facebook/ads/redexgen/X/AX;-><init>(Lcom/facebook/ads/redexgen/X/P3;)V

    iput-object v8, v2, Lcom/facebook/ads/redexgen/X/P3;->A0F:Lcom/facebook/ads/redexgen/X/Pu;

    .line 47392
    new-instance v7, Lcom/facebook/ads/redexgen/X/AW;

    invoke-direct {v7, v2}, Lcom/facebook/ads/redexgen/X/AW;-><init>(Lcom/facebook/ads/redexgen/X/P3;)V

    iput-object v7, v2, Lcom/facebook/ads/redexgen/X/P3;->A0G:Lcom/facebook/ads/redexgen/X/Ps;

    .line 47393
    const/4 v0, 0x0

    iput-boolean v0, v2, Lcom/facebook/ads/redexgen/X/P3;->A03:Z

    .line 47394
    iput-boolean v0, v2, Lcom/facebook/ads/redexgen/X/P3;->A04:Z

    .line 47395
    iput-boolean v0, v2, Lcom/facebook/ads/redexgen/X/P3;->A02:Z

    .line 47396
    iput-boolean v0, v2, Lcom/facebook/ads/redexgen/X/P3;->A01:Z

    .line 47397
    iput-object v3, v2, Lcom/facebook/ads/redexgen/X/P3;->A07:Lcom/facebook/ads/redexgen/X/Yn;

    .line 47398
    move-object/from16 v4, p3

    iput-object v4, v2, Lcom/facebook/ads/redexgen/X/P3;->A05:Lcom/facebook/ads/redexgen/X/b5;

    .line 47399
    move-object/from16 v1, p4

    iput-object v1, v2, Lcom/facebook/ads/redexgen/X/P3;->A06:Lcom/facebook/ads/redexgen/X/6c;

    .line 47400
    move-object/from16 v5, p5

    iput-object v5, v2, Lcom/facebook/ads/redexgen/X/P3;->A08:Lcom/facebook/ads/redexgen/X/JA;

    .line 47401
    move-object/from16 v0, p6

    iput-object v0, v2, Lcom/facebook/ads/redexgen/X/P3;->A09:Lcom/facebook/ads/redexgen/X/MC;

    .line 47402
    move-object/from16 v0, p7

    iput-object v0, v2, Lcom/facebook/ads/redexgen/X/P3;->A0A:Lcom/facebook/ads/redexgen/X/Mj;

    .line 47403
    new-instance v13, Lcom/facebook/ads/redexgen/X/SA;

    invoke-direct {v13, v3}, Lcom/facebook/ads/redexgen/X/SA;-><init>(Lcom/facebook/ads/redexgen/X/Yn;)V

    iput-object v13, v2, Lcom/facebook/ads/redexgen/X/P3;->A0D:Lcom/facebook/ads/redexgen/X/SA;

    .line 47404
    move-object/from16 v0, p8

    iput-object v0, v2, Lcom/facebook/ads/redexgen/X/P3;->A0B:Lcom/facebook/ads/redexgen/X/P2;

    .line 47405
    invoke-virtual {v13, v5}, Lcom/facebook/ads/redexgen/X/SA;->setFunnelLoggingHandler(Lcom/facebook/ads/redexgen/X/JA;)V

    .line 47406
    invoke-virtual {v13}, Lcom/facebook/ads/redexgen/X/SA;->getEventBus()Lcom/facebook/ads/redexgen/X/8r;

    move-result-object v6

    const/4 v0, 0x6

    new-array v5, v0, [Lcom/facebook/ads/redexgen/X/8s;

    const/4 v0, 0x0

    aput-object v12, v5, v0

    const/4 v0, 0x1

    aput-object v11, v5, v0

    const/4 v0, 0x2

    aput-object v10, v5, v0

    const/4 v0, 0x3

    aput-object v9, v5, v0

    const/4 v0, 0x4

    aput-object v8, v5, v0

    const/4 v0, 0x5

    aput-object v7, v5, v0

    .line 47407
    invoke-virtual {v6, v5}, Lcom/facebook/ads/redexgen/X/8r;->A03([Lcom/facebook/ads/redexgen/X/8s;)V

    .line 47408
    invoke-virtual {v4}, Lcom/facebook/ads/redexgen/X/b5;->A12()Ljava/lang/String;

    move-result-object v0

    new-instance v15, Lcom/facebook/ads/redexgen/X/9I;

    move-object/from16 v12, p2

    invoke-direct {v15, v3, v12, v13, v0}, Lcom/facebook/ads/redexgen/X/9I;-><init>(Lcom/facebook/ads/redexgen/X/Yn;Lcom/facebook/ads/redexgen/X/J2;Lcom/facebook/ads/redexgen/X/SA;Ljava/lang/String;)V

    iput-object v15, v2, Lcom/facebook/ads/redexgen/X/P3;->A0E:Lcom/facebook/ads/redexgen/X/9I;

    .line 47409
    invoke-static {v3}, Lcom/facebook/ads/redexgen/X/Ih;->A1X(Landroid/content/Context;)Z

    move-result v0

    if-eqz v0, :cond_1

    .line 47410
    new-instance v10, Lcom/facebook/ads/redexgen/X/SF;

    .line 47411
    invoke-virtual {v4}, Lcom/facebook/ads/redexgen/X/b5;->A12()Ljava/lang/String;

    move-result-object v14

    const/16 v16, 0x0

    move-object v11, v3

    move-object v0, v10

    invoke-direct/range {v10 .. v16}, Lcom/facebook/ads/redexgen/X/SF;-><init>(Lcom/facebook/ads/redexgen/X/Yn;Lcom/facebook/ads/redexgen/X/J2;Lcom/facebook/ads/redexgen/X/SA;Ljava/lang/String;Lcom/facebook/ads/redexgen/X/QS;Ljava/util/Map;)V

    iput-object v0, v2, Lcom/facebook/ads/redexgen/X/P3;->A0C:Lcom/facebook/ads/redexgen/X/SF;

    .line 47412
    :goto_0
    invoke-direct {v2}, Lcom/facebook/ads/redexgen/X/P3;->A0B()V

    .line 47413
    invoke-virtual {v4}, Lcom/facebook/ads/redexgen/X/b5;->A0x()Lcom/facebook/ads/redexgen/X/1G;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/1G;->A0D()Lcom/facebook/ads/redexgen/X/1J;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/1J;->A08()Ljava/lang/String;

    move-result-object v0

    .line 47414
    .local v1, "videoUrl":Ljava/lang/String;
    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/6c;->A0S(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v13, v0}, Lcom/facebook/ads/redexgen/X/SA;->setVideoURI(Ljava/lang/String;)V

    .line 47415
    invoke-direct {v2}, Lcom/facebook/ads/redexgen/X/P3;->A09()V

    .line 47416
    invoke-virtual {v4}, Lcom/facebook/ads/redexgen/X/b5;->A0w()Lcom/facebook/ads/redexgen/X/1C;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/1C;->A01()Lcom/facebook/ads/redexgen/X/1P;

    move-result-object v1

    .line 47417
    .local v2, "colors":Lcom/facebook/ads/redexgen/X/1P;
    const/4 v0, 0x1

    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/1P;->A07(Z)I

    move-result v0

    invoke-static {v2, v0}, Lcom/facebook/ads/redexgen/X/Lo;->A0M(Landroid/view/View;I)V

    .line 47418
    invoke-virtual {v4}, Lcom/facebook/ads/redexgen/X/b5;->A0x()Lcom/facebook/ads/redexgen/X/1G;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/1G;->A0D()Lcom/facebook/ads/redexgen/X/1J;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/1J;->A07()Ljava/lang/String;

    move-result-object v1

    .line 47419
    .local v3, "imageUrl":Ljava/lang/String;
    invoke-static {v1}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    if-nez v0, :cond_0

    .line 47420
    invoke-static {v3, v2, v1}, Lcom/facebook/ads/redexgen/X/OA;->A00(Lcom/facebook/ads/redexgen/X/Yn;Landroid/view/ViewGroup;Ljava/lang/String;)V

    .line 47421
    :cond_0
    const/4 v1, -0x1

    new-instance v0, Landroid/widget/FrameLayout$LayoutParams;

    invoke-direct {v0, v1, v1}, Landroid/widget/FrameLayout$LayoutParams;-><init>(II)V

    invoke-virtual {v2, v13, v0}, Lcom/facebook/ads/redexgen/X/P3;->addView(Landroid/view/View;Landroid/view/ViewGroup$LayoutParams;)V

    .line 47422
    return-void

    .line 47423
    :cond_1
    const/4 v0, 0x0

    iput-object v0, v2, Lcom/facebook/ads/redexgen/X/P3;->A0C:Lcom/facebook/ads/redexgen/X/SF;

    goto :goto_0
.end method

.method public static synthetic A00(Lcom/facebook/ads/redexgen/X/P3;)Lcom/facebook/ads/redexgen/X/P2;
    .locals 0

    .line 47424
    iget-object p0, p0, Lcom/facebook/ads/redexgen/X/P3;->A0B:Lcom/facebook/ads/redexgen/X/P2;

    return-object p0
.end method

.method public static synthetic A01(Lcom/facebook/ads/redexgen/X/P3;)Lcom/facebook/ads/redexgen/X/SA;
    .locals 0

    .line 47425
    iget-object p0, p0, Lcom/facebook/ads/redexgen/X/P3;->A0D:Lcom/facebook/ads/redexgen/X/SA;

    return-object p0
.end method

.method public static A02(III)Ljava/lang/String;
    .locals 2

    sget-object v1, Lcom/facebook/ads/redexgen/X/P3;->A0L:[B

    add-int v0, p0, p1

    invoke-static {v1, p0, v0}, Ljava/util/Arrays;->copyOfRange([BII)[B

    move-result-object p0

    const/4 v1, 0x0

    :goto_0
    array-length v0, p0

    if-ge v1, v0, :cond_0

    aget-byte v0, p0, v1

    xor-int/2addr v0, p2

    xor-int/lit8 v0, v0, 0x26

    int-to-byte v0, v0

    aput-byte v0, p0, v1

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_0
    new-instance v0, Ljava/lang/String;

    invoke-direct {v0, p0}, Ljava/lang/String;-><init>([B)V

    return-object v0
.end method

.method private A03()Lorg/json/JSONObject;
    .locals 1

    .line 47426
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/P3;->A0D:Lcom/facebook/ads/redexgen/X/SA;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/SA;->getCurrentPositionInMillis()I

    move-result v0

    invoke-direct {p0, v0}, Lcom/facebook/ads/redexgen/X/P3;->A04(I)Lorg/json/JSONObject;

    move-result-object v0

    return-object v0
.end method

.method private A04(I)Lorg/json/JSONObject;
    .locals 4

    .line 47427
    new-instance v3, Lorg/json/JSONObject;

    invoke-direct {v3}, Lorg/json/JSONObject;-><init>()V

    .line 47428
    .local v0, "extras":Lorg/json/JSONObject;
    :try_start_0
    const/4 v2, 0x0

    const/16 v1, 0xb

    const/16 v0, 0x40

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/P3;->A02(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v3, v0, p1}, Lorg/json/JSONObject;->put(Ljava/lang/String;I)Lorg/json/JSONObject;

    .line 47429
    const/16 v2, 0xb

    const/16 v1, 0x8

    const/16 v0, 0xc

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/P3;->A02(III)Ljava/lang/String;

    move-result-object v0

    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/P3;->A0D:Lcom/facebook/ads/redexgen/X/SA;

    invoke-virtual {v1}, Lcom/facebook/ads/redexgen/X/SA;->getDuration()I

    move-result v1

    invoke-virtual {v3, v0, v1}, Lorg/json/JSONObject;->put(Ljava/lang/String;I)Lorg/json/JSONObject;

    .line 47430
    const/16 v2, 0x13

    const/4 v1, 0x5

    const/16 v0, 0x14

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/P3;->A02(III)Ljava/lang/String;

    move-result-object v0

    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/P3;->A0D:Lcom/facebook/ads/redexgen/X/SA;

    invoke-virtual {v1}, Lcom/facebook/ads/redexgen/X/SA;->A0h()Z

    move-result v1

    invoke-virtual {v3, v0, v1}, Lorg/json/JSONObject;->put(Ljava/lang/String;Z)Lorg/json/JSONObject;

    .line 47431
    const/16 v2, 0x18

    const/16 v1, 0xc

    const/16 v0, 0x34

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/P3;->A02(III)Ljava/lang/String;

    move-result-object v0

    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/P3;->A0D:Lcom/facebook/ads/redexgen/X/SA;

    invoke-virtual {v1}, Lcom/facebook/ads/redexgen/X/SA;->A0j()Z

    move-result v1

    invoke-virtual {v3, v0, v1}, Lorg/json/JSONObject;->put(Ljava/lang/String;Z)Lorg/json/JSONObject;

    .line 47432
    return-object v3
    :try_end_0
    .catch Lorg/json/JSONException; {:try_start_0 .. :try_end_0} :catch_0

    .line 47433
    :catch_0
    move-exception v1

    .line 47434
    .local v1, "e":Lorg/json/JSONException;
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0, v1}, Ljava/lang/RuntimeException;-><init>(Ljava/lang/Throwable;)V

    throw v0
.end method

.method public static synthetic A05(Lcom/facebook/ads/redexgen/X/P3;)Lorg/json/JSONObject;
    .locals 0

    .line 47435
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/P3;->A03()Lorg/json/JSONObject;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic A06(Lcom/facebook/ads/redexgen/X/P3;I)Lorg/json/JSONObject;
    .locals 0

    .line 47436
    invoke-direct {p0, p1}, Lcom/facebook/ads/redexgen/X/P3;->A04(I)Lorg/json/JSONObject;

    move-result-object p0

    return-object p0
.end method

.method private A07()V
    .locals 4

    .line 47437
    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/P3;->A02:Z

    if-eqz v0, :cond_0

    .line 47438
    return-void

    .line 47439
    :cond_0
    const/4 v3, 0x1

    sget-object v1, Lcom/facebook/ads/redexgen/X/P3;->A0M:[Ljava/lang/String;

    const/4 v0, 0x4

    aget-object v1, v1, v0

    const/4 v0, 0x2

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    const/16 v0, 0x6e

    if-eq v1, v0, :cond_1

    sget-object v2, Lcom/facebook/ads/redexgen/X/P3;->A0M:[Ljava/lang/String;

    const-string v1, "wYD5NcGpzh0isOAoVzlYli3P4mnoyMgF"

    const/4 v0, 0x2

    aput-object v1, v2, v0

    const-string v1, "lJUNZHbcIhPOnD8CvxzjDicpi9xwJBJa"

    const/4 v0, 0x3

    aput-object v1, v2, v0

    iput-boolean v3, p0, Lcom/facebook/ads/redexgen/X/P3;->A02:Z

    .line 47440
    return-void

    :cond_1
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0
.end method

.method private A08()V
    .locals 4

    .line 47441
    iget-object v3, p0, Lcom/facebook/ads/redexgen/X/P3;->A0D:Lcom/facebook/ads/redexgen/X/SA;

    new-instance v2, Lcom/facebook/ads/redexgen/X/TH;

    invoke-direct {v2, p0}, Lcom/facebook/ads/redexgen/X/TH;-><init>(Lcom/facebook/ads/redexgen/X/P3;)V

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/P3;->A07:Lcom/facebook/ads/redexgen/X/Yn;

    .line 47442
    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/Ih;->A0K(Landroid/content/Context;)I

    move-result v0

    int-to-long v0, v0

    .line 47443
    invoke-virtual {v3, v2, v0, v1}, Lcom/facebook/ads/redexgen/X/SA;->postDelayed(Ljava/lang/Runnable;J)Z

    .line 47444
    return-void
.end method

.method private A09()V
    .locals 4

    .line 47445
    iget-object v3, p0, Lcom/facebook/ads/redexgen/X/P3;->A0D:Lcom/facebook/ads/redexgen/X/SA;

    new-instance v2, Lcom/facebook/ads/redexgen/X/TG;

    invoke-direct {v2, p0}, Lcom/facebook/ads/redexgen/X/TG;-><init>(Lcom/facebook/ads/redexgen/X/P3;)V

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/P3;->A07:Lcom/facebook/ads/redexgen/X/Yn;

    .line 47446
    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/Ih;->A0L(Landroid/content/Context;)I

    move-result v0

    int-to-long v0, v0

    .line 47447
    invoke-virtual {v3, v2, v0, v1}, Lcom/facebook/ads/redexgen/X/SA;->postDelayed(Ljava/lang/Runnable;J)Z

    .line 47448
    return-void
.end method

.method private A0A()V
    .locals 4

    .line 47449
    iget-object v3, p0, Lcom/facebook/ads/redexgen/X/P3;->A0B:Lcom/facebook/ads/redexgen/X/P2;

    const/16 v2, 0x24

    const/16 v1, 0x8

    const/16 v0, 0x70

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/P3;->A02(III)Ljava/lang/String;

    move-result-object v1

    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/P3;->A03()Lorg/json/JSONObject;

    move-result-object v0

    invoke-interface {v3, v1, v0}, Lcom/facebook/ads/redexgen/X/P2;->ADY(Ljava/lang/String;Lorg/json/JSONObject;)V

    .line 47450
    return-void
.end method

.method private A0B()V
    .locals 5

    .line 47451
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/P3;->A05:Lcom/facebook/ads/redexgen/X/b5;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/b5;->A0x()Lcom/facebook/ads/redexgen/X/1G;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/1G;->A0D()Lcom/facebook/ads/redexgen/X/1J;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/1J;->A07()Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    if-nez v0, :cond_0

    .line 47452
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/P3;->A07:Lcom/facebook/ads/redexgen/X/Yn;

    new-instance v1, Lcom/facebook/ads/redexgen/X/7R;

    invoke-direct {v1, v0}, Lcom/facebook/ads/redexgen/X/7R;-><init>(Lcom/facebook/ads/redexgen/X/Yn;)V

    .line 47453
    .local v0, "placeholderImagePlugin":Lcom/facebook/ads/redexgen/X/7R;
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/P3;->A0D:Lcom/facebook/ads/redexgen/X/SA;

    invoke-virtual {v0, v1}, Lcom/facebook/ads/redexgen/X/SA;->A0c(Lcom/facebook/ads/redexgen/X/QN;)V

    .line 47454
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/P3;->A05:Lcom/facebook/ads/redexgen/X/b5;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/b5;->A0x()Lcom/facebook/ads/redexgen/X/1G;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/1G;->A0D()Lcom/facebook/ads/redexgen/X/1J;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/1J;->A07()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/7R;->setImage(Ljava/lang/String;)V

    .line 47455
    .end local v0    # "placeholderImagePlugin":Lcom/facebook/ads/redexgen/X/7R;
    :cond_0
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/P3;->A07:Lcom/facebook/ads/redexgen/X/Yn;

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/P3;->A08:Lcom/facebook/ads/redexgen/X/JA;

    const/4 v4, 0x1

    new-instance v3, Lcom/facebook/ads/redexgen/X/7C;

    invoke-direct {v3, v1, v4, v0}, Lcom/facebook/ads/redexgen/X/7C;-><init>(Lcom/facebook/ads/redexgen/X/Yn;ZLcom/facebook/ads/redexgen/X/JA;)V

    .line 47456
    .local v0, "touchPlayPausePlugin":Lcom/facebook/ads/redexgen/X/7C;
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/P3;->A0D:Lcom/facebook/ads/redexgen/X/SA;

    invoke-virtual {v0, v3}, Lcom/facebook/ads/redexgen/X/SA;->A0c(Lcom/facebook/ads/redexgen/X/QN;)V

    .line 47457
    iget-object v2, p0, Lcom/facebook/ads/redexgen/X/P3;->A0D:Lcom/facebook/ads/redexgen/X/SA;

    sget-object v1, Lcom/facebook/ads/redexgen/X/QZ;->A03:Lcom/facebook/ads/redexgen/X/QZ;

    new-instance v0, Lcom/facebook/ads/redexgen/X/L7;

    invoke-direct {v0, v3, v1, v4}, Lcom/facebook/ads/redexgen/X/L7;-><init>(Landroid/view/View;Lcom/facebook/ads/redexgen/X/QZ;Z)V

    invoke-virtual {v2, v0}, Lcom/facebook/ads/redexgen/X/SA;->A0c(Lcom/facebook/ads/redexgen/X/QN;)V

    .line 47458
    iget-object v2, p0, Lcom/facebook/ads/redexgen/X/P3;->A0D:Lcom/facebook/ads/redexgen/X/SA;

    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/P3;->A07:Lcom/facebook/ads/redexgen/X/Yn;

    new-instance v0, Lcom/facebook/ads/redexgen/X/7H;

    invoke-direct {v0, v1}, Lcom/facebook/ads/redexgen/X/7H;-><init>(Lcom/facebook/ads/redexgen/X/Yn;)V

    invoke-virtual {v2, v0}, Lcom/facebook/ads/redexgen/X/SA;->A0c(Lcom/facebook/ads/redexgen/X/QN;)V

    .line 47459
    return-void
.end method

.method public static A0C()V
    .locals 1

    const/16 v0, 0x2c

    new-array v0, v0, [B

    fill-array-data v0, :array_0

    sput-object v0, Lcom/facebook/ads/redexgen/X/P3;->A0L:[B

    return-void

    :array_0
    .array-data 1
        0x5t
        0x13t
        0x14t
        0x14t
        0x3t
        0x8t
        0x12t
        0x32t
        0xft
        0xbt
        0x3t
        0x4et
        0x5ft
        0x58t
        0x4bt
        0x5et
        0x43t
        0x45t
        0x44t
        0x5ft
        0x47t
        0x46t
        0x57t
        0x56t
        0x62t
        0x73t
        0x67t
        0x61t
        0x77t
        0x76t
        0x50t
        0x6bt
        0x47t
        0x61t
        0x77t
        0x60t
        0x26t
        0x24t
        0x39t
        0x31t
        0x24t
        0x33t
        0x25t
        0x25t
    .end array-data
.end method

.method public static synthetic A0D(Lcom/facebook/ads/redexgen/X/P3;)V
    .locals 0

    .line 47460
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/P3;->A0A()V

    return-void
.end method

.method public static synthetic A0E(Lcom/facebook/ads/redexgen/X/P3;)V
    .locals 0

    .line 47461
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/P3;->A07()V

    return-void
.end method

.method public static synthetic A0F(Lcom/facebook/ads/redexgen/X/P3;Lcom/facebook/ads/redexgen/X/93;)V
    .locals 0

    .line 47462
    invoke-direct {p0, p1}, Lcom/facebook/ads/redexgen/X/P3;->A0H(Lcom/facebook/ads/redexgen/X/93;)V

    return-void
.end method

.method public static synthetic A0G(Lcom/facebook/ads/redexgen/X/P3;Ljava/lang/String;)V
    .locals 0

    .line 47463
    invoke-direct {p0, p1}, Lcom/facebook/ads/redexgen/X/P3;->A0I(Ljava/lang/String;)V

    return-void
.end method

.method private A0H(Lcom/facebook/ads/redexgen/X/93;)V
    .locals 4

    .line 47464
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/P3;->A0D:Lcom/facebook/ads/redexgen/X/SA;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/SA;->getState()Lcom/facebook/ads/redexgen/X/RB;

    move-result-object v1

    sget-object v0, Lcom/facebook/ads/redexgen/X/RB;->A02:Lcom/facebook/ads/redexgen/X/RB;

    if-eq v1, v0, :cond_0

    .line 47465
    return-void

    .line 47466
    :cond_0
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/P3;->A07:Lcom/facebook/ads/redexgen/X/Yn;

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/Ih;->A1D(Landroid/content/Context;)Z

    move-result v0

    if-eqz v0, :cond_1

    .line 47467
    iget-object v3, p0, Lcom/facebook/ads/redexgen/X/P3;->A0D:Lcom/facebook/ads/redexgen/X/SA;

    new-instance v2, Lcom/facebook/ads/redexgen/X/TF;

    invoke-direct {v2, p0, p1}, Lcom/facebook/ads/redexgen/X/TF;-><init>(Lcom/facebook/ads/redexgen/X/P3;Lcom/facebook/ads/redexgen/X/93;)V

    const-wide/16 v0, 0x1388

    invoke-virtual {v3, v2, v0, v1}, Lcom/facebook/ads/redexgen/X/SA;->postDelayed(Ljava/lang/Runnable;J)Z

    .line 47468
    :cond_1
    return-void
.end method

.method private A0I(Ljava/lang/String;)V
    .locals 5

    .line 47469
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/P3;->A07:Lcom/facebook/ads/redexgen/X/Yn;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/Yn;->A0E()Lcom/facebook/ads/redexgen/X/0S;

    move-result-object v0

    invoke-interface {v0, p1}, Lcom/facebook/ads/redexgen/X/0S;->A2z(Ljava/lang/String;)V

    .line 47470
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/P3;->A07:Lcom/facebook/ads/redexgen/X/Yn;

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/Ih;->A1E(Landroid/content/Context;)Z

    move-result v0

    if-eqz v0, :cond_0

    .line 47471
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/P3;->A07()V

    .line 47472
    :goto_0
    return-void

    .line 47473
    :cond_0
    iget-object v3, p0, Lcom/facebook/ads/redexgen/X/P3;->A09:Lcom/facebook/ads/redexgen/X/MC;

    iget-object v4, p0, Lcom/facebook/ads/redexgen/X/P3;->A0A:Lcom/facebook/ads/redexgen/X/Mj;

    sget-object v2, Lcom/facebook/ads/redexgen/X/P3;->A0M:[Ljava/lang/String;

    const/4 v0, 0x5

    aget-object v1, v2, v0

    const/4 v0, 0x6

    aget-object v2, v2, v0

    const/16 v0, 0x10

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    invoke-virtual {v2, v0}, Ljava/lang/String;->charAt(I)C

    move-result v0

    if-eq v1, v0, :cond_1

    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    :cond_1
    sget-object v2, Lcom/facebook/ads/redexgen/X/P3;->A0M:[Ljava/lang/String;

    const-string v1, "EBONCaOY6he3EVwrjSVJVgPSsddIWnnf"

    const/4 v0, 0x2

    aput-object v1, v2, v0

    const-string v1, "quoo3J1Esh1kTLqYeLvwyhMYZuOues3Q"

    const/4 v0, 0x3

    aput-object v1, v2, v0

    invoke-interface {v4}, Lcom/facebook/ads/redexgen/X/Mj;->A6y()Ljava/lang/String;

    move-result-object v0

    invoke-interface {v3, v0}, Lcom/facebook/ads/redexgen/X/MC;->A43(Ljava/lang/String;)V

    .line 47474
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/P3;->A09:Lcom/facebook/ads/redexgen/X/MC;

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/P3;->A0A:Lcom/facebook/ads/redexgen/X/Mj;

    invoke-interface {v0}, Lcom/facebook/ads/redexgen/X/Mj;->A6u()Ljava/lang/String;

    move-result-object v0

    invoke-interface {v1, v0}, Lcom/facebook/ads/redexgen/X/MC;->A43(Ljava/lang/String;)V

    goto :goto_0
.end method

.method public static synthetic A0J(Lcom/facebook/ads/redexgen/X/P3;)Z
    .locals 0

    .line 47475
    iget-boolean p0, p0, Lcom/facebook/ads/redexgen/X/P3;->A03:Z

    return p0
.end method

.method public static synthetic A0K(Lcom/facebook/ads/redexgen/X/P3;)Z
    .locals 0

    .line 47476
    iget-boolean p0, p0, Lcom/facebook/ads/redexgen/X/P3;->A04:Z

    return p0
.end method

.method public static synthetic A0L(Lcom/facebook/ads/redexgen/X/P3;Z)Z
    .locals 0

    .line 47477
    iput-boolean p1, p0, Lcom/facebook/ads/redexgen/X/P3;->A03:Z

    return p1
.end method

.method public static synthetic A0M(Lcom/facebook/ads/redexgen/X/P3;Z)Z
    .locals 0

    .line 47478
    iput-boolean p1, p0, Lcom/facebook/ads/redexgen/X/P3;->A04:Z

    return p1
.end method


# virtual methods
.method public final A0N()V
    .locals 3

    .line 47479
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/P3;->A0D:Lcom/facebook/ads/redexgen/X/SA;

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/P3;->A05:Lcom/facebook/ads/redexgen/X/b5;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/b5;->A0x()Lcom/facebook/ads/redexgen/X/1G;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/1G;->A0D()Lcom/facebook/ads/redexgen/X/1J;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/1J;->A09()Z

    move-result v0

    if-eqz v0, :cond_0

    const/4 v0, 0x0

    :goto_0
    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/SA;->setVolume(F)V

    .line 47480
    iget-object v2, p0, Lcom/facebook/ads/redexgen/X/P3;->A0D:Lcom/facebook/ads/redexgen/X/SA;

    sget-object v1, Lcom/facebook/ads/redexgen/X/QM;->A02:Lcom/facebook/ads/redexgen/X/QM;

    const/16 v0, 0x1a

    invoke-virtual {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/SA;->A0b(Lcom/facebook/ads/redexgen/X/QM;I)V

    .line 47481
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/P3;->A08()V

    .line 47482
    return-void

    .line 47483
    :cond_0
    const/high16 v0, 0x3f800000    # 1.0f

    goto :goto_0
.end method

.method public final A0O()V
    .locals 4

    .line 47484
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/P3;->A0D:Lcom/facebook/ads/redexgen/X/SA;

    if-eqz v1, :cond_1

    .line 47485
    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/P3;->A02:Z

    if-nez v0, :cond_0

    .line 47486
    sget-object v0, Lcom/facebook/ads/redexgen/X/QH;->A03:Lcom/facebook/ads/redexgen/X/QH;

    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/SA;->A0a(Lcom/facebook/ads/redexgen/X/QH;)V

    .line 47487
    :cond_0
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/P3;->A0D:Lcom/facebook/ads/redexgen/X/SA;

    .line 47488
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/SA;->getEventBus()Lcom/facebook/ads/redexgen/X/8r;

    move-result-object v3

    const/4 v0, 0x6

    new-array v2, v0, [Lcom/facebook/ads/redexgen/X/8s;

    const/4 v1, 0x0

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/P3;->A0J:Lcom/facebook/ads/redexgen/X/NX;

    aput-object v0, v2, v1

    const/4 v1, 0x1

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/P3;->A0I:Lcom/facebook/ads/redexgen/X/O7;

    aput-object v0, v2, v1

    const/4 v1, 0x2

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/P3;->A0H:Lcom/facebook/ads/redexgen/X/On;

    aput-object v0, v2, v1

    const/4 v1, 0x3

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/P3;->A0K:Lcom/facebook/ads/redexgen/X/NQ;

    aput-object v0, v2, v1

    const/4 v1, 0x4

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/P3;->A0F:Lcom/facebook/ads/redexgen/X/Pu;

    aput-object v0, v2, v1

    const/4 v1, 0x5

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/P3;->A0G:Lcom/facebook/ads/redexgen/X/Ps;

    aput-object v0, v2, v1

    .line 47489
    invoke-virtual {v3, v2}, Lcom/facebook/ads/redexgen/X/8r;->A04([Lcom/facebook/ads/redexgen/X/8s;)V

    .line 47490
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/P3;->A0D:Lcom/facebook/ads/redexgen/X/SA;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/SA;->A0V()V

    .line 47491
    :cond_1
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/P3;->A0C:Lcom/facebook/ads/redexgen/X/SF;

    if-eqz v0, :cond_2

    .line 47492
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/SF;->A0C()V

    .line 47493
    :cond_2
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/P3;->A0E:Lcom/facebook/ads/redexgen/X/9I;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/9I;->A0i()V

    .line 47494
    return-void
.end method

.method public final A0P()V
    .locals 3

    .line 47495
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/P3;->A0D:Lcom/facebook/ads/redexgen/X/SA;

    const/16 v0, 0x9

    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/SA;->A0Z(I)V

    .line 47496
    invoke-static {p0}, Lcom/facebook/ads/redexgen/X/Lo;->A0T(Landroid/view/ViewGroup;)V

    .line 47497
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/P3;->A0D:Lcom/facebook/ads/redexgen/X/SA;

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/Lo;->A0H(Landroid/view/View;)V

    .line 47498
    const/4 v0, 0x1

    new-array v2, v0, [Landroid/view/View;

    const/4 v1, 0x0

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/P3;->A0D:Lcom/facebook/ads/redexgen/X/SA;

    aput-object v0, v2, v1

    invoke-static {v2}, Lcom/facebook/ads/redexgen/X/Lo;->A0a([Landroid/view/View;)V

    .line 47499
    return-void
.end method

.method public final A0Q()V
    .locals 2

    .line 47500
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/P3;->A0D:Lcom/facebook/ads/redexgen/X/SA;

    sget-object v0, Lcom/facebook/ads/redexgen/X/QH;->A04:Lcom/facebook/ads/redexgen/X/QH;

    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/SA;->A0a(Lcom/facebook/ads/redexgen/X/QH;)V

    .line 47501
    return-void
.end method

.method public final A0R(Z)V
    .locals 3

    .line 47502
    if-eqz p1, :cond_0

    .line 47503
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/P3;->A0D:Lcom/facebook/ads/redexgen/X/SA;

    const/4 v0, 0x0

    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/SA;->setVolume(F)V

    .line 47504
    :goto_0
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/P3;->A0A()V

    sget-object v1, Lcom/facebook/ads/redexgen/X/P3;->A0M:[Ljava/lang/String;

    const/4 v0, 0x7

    aget-object v1, v1, v0

    const/4 v0, 0x5

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    const/16 v0, 0x75

    if-eq v1, v0, :cond_1

    .line 47505
    sget-object v2, Lcom/facebook/ads/redexgen/X/P3;->A0M:[Ljava/lang/String;

    const-string v1, "yn69DofMHsLSVfdL7HK9OU5c1k1rZYJH"

    const/4 v0, 0x4

    aput-object v1, v2, v0

    return-void

    .line 47506
    :cond_0
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/P3;->A0D:Lcom/facebook/ads/redexgen/X/SA;

    const/high16 v0, 0x3f800000    # 1.0f

    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/SA;->setVolume(F)V

    goto :goto_0

    :cond_1
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0
.end method

.method public final A0S(Z)V
    .locals 3

    .line 47507
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/P3;->A0D:Lcom/facebook/ads/redexgen/X/SA;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/SA;->A0i()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 47508
    return-void

    .line 47509
    :cond_0
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/P3;->A0D:Lcom/facebook/ads/redexgen/X/SA;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/SA;->getVideoStartReason()Lcom/facebook/ads/redexgen/X/QM;

    move-result-object v0

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/P3;->A00:Lcom/facebook/ads/redexgen/X/QM;

    .line 47510
    iput-boolean p1, p0, Lcom/facebook/ads/redexgen/X/P3;->A01:Z

    .line 47511
    iget-object v2, p0, Lcom/facebook/ads/redexgen/X/P3;->A0D:Lcom/facebook/ads/redexgen/X/SA;

    const/4 v1, 0x0

    const/16 v0, 0x13

    invoke-virtual {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/SA;->A0e(ZI)V

    .line 47512
    return-void
.end method

.method public final A0T(Z)V
    .locals 5

    .line 47513
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/P3;->A0D:Lcom/facebook/ads/redexgen/X/SA;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/SA;->A0j()Z

    move-result v0

    if-nez v0, :cond_0

    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/P3;->A02:Z

    if-eqz v0, :cond_1

    .line 47514
    :cond_0
    return-void

    .line 47515
    :cond_1
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/P3;->A0D:Lcom/facebook/ads/redexgen/X/SA;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/SA;->getState()Lcom/facebook/ads/redexgen/X/RB;

    move-result-object v1

    sget-object v0, Lcom/facebook/ads/redexgen/X/RB;->A06:Lcom/facebook/ads/redexgen/X/RB;

    if-eq v1, v0, :cond_3

    iget-object v3, p0, Lcom/facebook/ads/redexgen/X/P3;->A00:Lcom/facebook/ads/redexgen/X/QM;

    if-eqz v3, :cond_3

    iget-boolean v4, p0, Lcom/facebook/ads/redexgen/X/P3;->A01:Z

    sget-object v1, Lcom/facebook/ads/redexgen/X/P3;->A0M:[Ljava/lang/String;

    const/4 v0, 0x7

    aget-object v1, v1, v0

    const/4 v0, 0x5

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    const/16 v0, 0x75

    if-eq v1, v0, :cond_4

    sget-object v2, Lcom/facebook/ads/redexgen/X/P3;->A0M:[Ljava/lang/String;

    const-string v1, "icjW3n8vZZxsaCmttUmjcTI7CQu2m51p"

    const/4 v0, 0x1

    aput-object v1, v2, v0

    const-string v1, "kS3lp82K2YolC4kFtdcvtTBENFWE8ibh"

    const/4 v0, 0x0

    aput-object v1, v2, v0

    if-eqz v4, :cond_2

    if-eqz p1, :cond_3

    .line 47516
    :cond_2
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/P3;->A0D:Lcom/facebook/ads/redexgen/X/SA;

    const/16 v0, 0x1b

    invoke-virtual {v1, v3, v0}, Lcom/facebook/ads/redexgen/X/SA;->A0b(Lcom/facebook/ads/redexgen/X/QM;I)V

    .line 47517
    :cond_3
    return-void

    :cond_4
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0
.end method

.method public final A0U(Z)V
    .locals 2

    .line 47518
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/P3;->A0D:Lcom/facebook/ads/redexgen/X/SA;

    const/16 v0, 0x12

    invoke-virtual {v1, p1, v0}, Lcom/facebook/ads/redexgen/X/SA;->A0e(ZI)V

    .line 47519
    return-void
.end method

.method public final A0V(Z)V
    .locals 3

    .line 47520
    iget-object v2, p0, Lcom/facebook/ads/redexgen/X/P3;->A0D:Lcom/facebook/ads/redexgen/X/SA;

    sget-object v1, Lcom/facebook/ads/redexgen/X/QM;->A04:Lcom/facebook/ads/redexgen/X/QM;

    const/16 v0, 0x19

    invoke-virtual {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/SA;->A0b(Lcom/facebook/ads/redexgen/X/QM;I)V

    .line 47521
    return-void
.end method
