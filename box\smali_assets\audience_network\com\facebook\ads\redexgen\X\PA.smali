.class public interface abstract Lcom/facebook/ads/redexgen/X/PA;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/facebook/ads/redexgen/X/PB;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "ProcessCrashListener"
.end annotation


# virtual methods
.method public abstract ACl()V
.end method
