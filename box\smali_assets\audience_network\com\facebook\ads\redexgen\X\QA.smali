.class public abstract Lcom/facebook/ads/redexgen/X/QA;
.super Lcom/facebook/ads/redexgen/X/8q;
.source ""


# instance fields
.field public A00:F

.field public A01:J

.field public A02:J

.field public A03:J


# direct methods
.method public constructor <init>(JJJF)V
    .locals 0

    .line 48942
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/8q;-><init>()V

    .line 48943
    iput-wide p1, p0, Lcom/facebook/ads/redexgen/X/QA;->A03:J

    .line 48944
    iput-wide p3, p0, Lcom/facebook/ads/redexgen/X/QA;->A01:J

    .line 48945
    iput-wide p5, p0, Lcom/facebook/ads/redexgen/X/QA;->A02:J

    .line 48946
    iput p7, p0, Lcom/facebook/ads/redexgen/X/QA;->A00:F

    .line 48947
    return-void
.end method


# virtual methods
.method public final A00()F
    .locals 1

    .line 48948
    iget v0, p0, Lcom/facebook/ads/redexgen/X/QA;->A00:F

    return v0
.end method

.method public final A01()J
    .locals 2

    .line 48949
    iget-wide v0, p0, Lcom/facebook/ads/redexgen/X/QA;->A01:J

    return-wide v0
.end method

.method public final A02()J
    .locals 2

    .line 48950
    iget-wide v0, p0, Lcom/facebook/ads/redexgen/X/QA;->A02:J

    return-wide v0
.end method

.method public final A03()J
    .locals 2

    .line 48951
    iget-wide v0, p0, Lcom/facebook/ads/redexgen/X/QA;->A03:J

    return-wide v0
.end method
