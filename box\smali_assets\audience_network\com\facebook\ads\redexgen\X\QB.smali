.class public interface abstract Lcom/facebook/ads/redexgen/X/QB;
.super Ljava/lang/Object;
.source ""


# virtual methods
.method public abstract getView()Landroid/view/View;
.end method

.method public abstract getViewsForInteraction()Ljava/util/ArrayList;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/ArrayList<",
            "Landroid/view/View;",
            ">;"
        }
    .end annotation
.end method

.method public abstract unregisterView()V
.end method
