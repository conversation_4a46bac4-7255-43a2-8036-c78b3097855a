.class public interface abstract Lcom/facebook/ads/redexgen/X/QE;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/facebook/ads/redexgen/X/QS;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "DataProvider"
.end annotation


# virtual methods
.method public abstract A92()Z
.end method

.method public abstract A95()Z
.end method

.method public abstract getCurrentPositionInMillis()I
.end method

.method public abstract getGlobalVisibleRect(Landroid/graphics/Rect;)Z
.end method

.method public abstract getInitialBufferTime()J
.end method

.method public abstract getMeasuredHeight()I
.end method

.method public abstract getMeasuredWidth()I
.end method

.method public abstract getVideoStartReason()Lcom/facebook/ads/redexgen/X/QM;
.end method

.method public abstract getVolume()F
.end method
