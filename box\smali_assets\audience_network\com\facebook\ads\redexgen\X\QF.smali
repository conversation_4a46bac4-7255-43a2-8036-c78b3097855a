.class public interface abstract Lcom/facebook/ads/redexgen/X/QF;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/facebook/ads/redexgen/X/QS;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "OnLogEventListener"
.end annotation


# virtual methods
.method public abstract AC8()V
.end method
