.class public final enum Lcom/facebook/ads/redexgen/X/QG;
.super Ljava/lang/Enum;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/facebook/ads/redexgen/X/QS;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x4019
    name = "ReportType"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Enum<",
        "Lcom/facebook/ads/redexgen/X/QG;",
        ">;"
    }
.end annotation


# static fields
.field public static A01:[B

.field public static final synthetic A02:[Lcom/facebook/ads/redexgen/X/QG;

.field public static final enum A03:Lcom/facebook/ads/redexgen/X/QG;

.field public static final enum A04:Lcom/facebook/ads/redexgen/X/QG;

.field public static final enum A05:Lcom/facebook/ads/redexgen/X/QG;

.field public static final enum A06:Lcom/facebook/ads/redexgen/X/QG;

.field public static final enum A07:Lcom/facebook/ads/redexgen/X/QG;

.field public static final enum A08:Lcom/facebook/ads/redexgen/X/QG;

.field public static final enum A09:Lcom/facebook/ads/redexgen/X/QG;

.field public static final enum A0A:Lcom/facebook/ads/redexgen/X/QG;

.field public static final enum A0B:Lcom/facebook/ads/redexgen/X/QG;


# instance fields
.field public final A00:I


# direct methods
.method public static constructor <clinit>()V
    .locals 16

    .line 2198
    invoke-static {}, Lcom/facebook/ads/redexgen/X/QG;->A01()V

    const/16 v2, 0xc

    const/4 v1, 0x4

    const/16 v0, 0x77

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/QG;->A00(III)Ljava/lang/String;

    move-result-object v0

    const/4 v15, 0x0

    new-instance v14, Lcom/facebook/ads/redexgen/X/QG;

    invoke-direct {v14, v0, v15, v15}, Lcom/facebook/ads/redexgen/X/QG;-><init>(Ljava/lang/String;II)V

    sput-object v14, Lcom/facebook/ads/redexgen/X/QG;->A06:Lcom/facebook/ads/redexgen/X/QG;

    .line 2199
    const/16 v2, 0x16

    const/4 v1, 0x4

    const/16 v0, 0x50

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/QG;->A00(III)Ljava/lang/String;

    move-result-object v0

    const/4 v13, 0x1

    new-instance v12, Lcom/facebook/ads/redexgen/X/QG;

    invoke-direct {v12, v0, v13, v13}, Lcom/facebook/ads/redexgen/X/QG;-><init>(Ljava/lang/String;II)V

    sput-object v12, Lcom/facebook/ads/redexgen/X/QG;->A08:Lcom/facebook/ads/redexgen/X/QG;

    .line 2200
    const/16 v2, 0x1a

    const/4 v1, 0x4

    const/16 v0, 0x62

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/QG;->A00(III)Ljava/lang/String;

    move-result-object v0

    const/4 v11, 0x2

    new-instance v10, Lcom/facebook/ads/redexgen/X/QG;

    invoke-direct {v10, v0, v11, v11}, Lcom/facebook/ads/redexgen/X/QG;-><init>(Ljava/lang/String;II)V

    sput-object v10, Lcom/facebook/ads/redexgen/X/QG;->A09:Lcom/facebook/ads/redexgen/X/QG;

    .line 2201
    const/4 v2, 0x0

    const/4 v1, 0x3

    const/16 v0, 0x53

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/QG;->A00(III)Ljava/lang/String;

    move-result-object v0

    const/4 v9, 0x3

    new-instance v8, Lcom/facebook/ads/redexgen/X/QG;

    invoke-direct {v8, v0, v9, v9}, Lcom/facebook/ads/redexgen/X/QG;-><init>(Ljava/lang/String;II)V

    sput-object v8, Lcom/facebook/ads/redexgen/X/QG;->A03:Lcom/facebook/ads/redexgen/X/QG;

    .line 2202
    const/4 v2, 0x7

    const/4 v1, 0x5

    const/16 v0, 0x76

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/QG;->A00(III)Ljava/lang/String;

    move-result-object v1

    const/4 v0, 0x4

    new-instance v7, Lcom/facebook/ads/redexgen/X/QG;

    invoke-direct {v7, v1, v0, v0}, Lcom/facebook/ads/redexgen/X/QG;-><init>(Ljava/lang/String;II)V

    sput-object v7, Lcom/facebook/ads/redexgen/X/QG;->A05:Lcom/facebook/ads/redexgen/X/QG;

    .line 2203
    const/16 v2, 0x10

    const/4 v1, 0x6

    const/16 v0, 0x1a

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/QG;->A00(III)Ljava/lang/String;

    move-result-object v1

    const/4 v0, 0x5

    new-instance v6, Lcom/facebook/ads/redexgen/X/QG;

    invoke-direct {v6, v1, v0, v0}, Lcom/facebook/ads/redexgen/X/QG;-><init>(Ljava/lang/String;II)V

    sput-object v6, Lcom/facebook/ads/redexgen/X/QG;->A07:Lcom/facebook/ads/redexgen/X/QG;

    .line 2204
    const/4 v2, 0x3

    const/4 v1, 0x4

    const/16 v0, 0x19

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/QG;->A00(III)Ljava/lang/String;

    move-result-object v1

    const/4 v0, 0x6

    new-instance v5, Lcom/facebook/ads/redexgen/X/QG;

    invoke-direct {v5, v1, v0, v0}, Lcom/facebook/ads/redexgen/X/QG;-><init>(Ljava/lang/String;II)V

    sput-object v5, Lcom/facebook/ads/redexgen/X/QG;->A04:Lcom/facebook/ads/redexgen/X/QG;

    .line 2205
    const/16 v2, 0x1e

    const/4 v1, 0x6

    const/16 v0, 0x43

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/QG;->A00(III)Ljava/lang/String;

    move-result-object v1

    const/4 v0, 0x7

    new-instance v4, Lcom/facebook/ads/redexgen/X/QG;

    invoke-direct {v4, v1, v0, v0}, Lcom/facebook/ads/redexgen/X/QG;-><init>(Ljava/lang/String;II)V

    sput-object v4, Lcom/facebook/ads/redexgen/X/QG;->A0A:Lcom/facebook/ads/redexgen/X/QG;

    .line 2206
    const/16 v1, 0xa

    const/16 v3, 0x24

    const/16 v2, 0x13

    const/16 v0, 0x60

    invoke-static {v3, v2, v0}, Lcom/facebook/ads/redexgen/X/QG;->A00(III)Ljava/lang/String;

    move-result-object v0

    const/16 v3, 0x8

    new-instance v2, Lcom/facebook/ads/redexgen/X/QG;

    invoke-direct {v2, v0, v3, v1}, Lcom/facebook/ads/redexgen/X/QG;-><init>(Ljava/lang/String;II)V

    sput-object v2, Lcom/facebook/ads/redexgen/X/QG;->A0B:Lcom/facebook/ads/redexgen/X/QG;

    .line 2207
    const/16 v0, 0x9

    new-array v1, v0, [Lcom/facebook/ads/redexgen/X/QG;

    aput-object v14, v1, v15

    aput-object v12, v1, v13

    aput-object v10, v1, v11

    aput-object v8, v1, v9

    const/4 v0, 0x4

    aput-object v7, v1, v0

    const/4 v0, 0x5

    aput-object v6, v1, v0

    const/4 v0, 0x6

    aput-object v5, v1, v0

    const/4 v0, 0x7

    aput-object v4, v1, v0

    aput-object v2, v1, v3

    sput-object v1, Lcom/facebook/ads/redexgen/X/QG;->A02:[Lcom/facebook/ads/redexgen/X/QG;

    return-void
.end method

.method public constructor <init>(Ljava/lang/String;II)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I)V"
        }
    .end annotation

    .line 48980
    invoke-direct {p0, p1, p2}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    .line 48981
    iput p3, p0, Lcom/facebook/ads/redexgen/X/QG;->A00:I

    .line 48982
    return-void
.end method

.method public static A00(III)Ljava/lang/String;
    .locals 2

    sget-object v1, Lcom/facebook/ads/redexgen/X/QG;->A01:[B

    add-int v0, p0, p1

    invoke-static {v1, p0, v0}, Ljava/util/Arrays;->copyOfRange([BII)[B

    move-result-object p0

    const/4 v1, 0x0

    :goto_0
    array-length v0, p0

    if-ge v1, v0, :cond_0

    aget-byte v0, p0, v1

    sub-int/2addr v0, p2

    add-int/lit8 v0, v0, -0xb

    int-to-byte v0, v0

    aput-byte v0, p0, v1

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_0
    new-instance v0, Ljava/lang/String;

    invoke-direct {v0, p0}, Ljava/lang/String;-><init>([B)V

    return-object v0
.end method

.method public static A01()V
    .locals 1

    const/16 v0, 0x37

    new-array v0, v0, [B

    fill-array-data v0, :array_0

    sput-object v0, Lcom/facebook/ads/redexgen/X/QG;->A01:[B

    return-void

    :array_0
    .array-data 1
        -0x55t
        -0x50t
        -0x5ft
        0x71t
        0x79t
        0x78t
        0x69t
        -0x2ft
        -0x3et
        -0x2at
        -0x2ct
        -0x3at
        -0x2et
        -0x32t
        -0x3dt
        -0x25t
        0x77t
        0x6at
        0x78t
        0x7at
        0x72t
        0x6at
        -0x52t
        -0x5at
        -0x5ct
        -0x55t
        -0x3ft
        -0x4at
        -0x46t
        -0x4et
        -0x5dt
        -0x64t
        -0x65t
        -0x5dt
        -0x5et
        -0x6dt
        -0x3ft
        -0x4ct
        -0x50t
        -0x3et
        -0x54t
        -0x53t
        -0x49t
        -0x50t
        -0x36t
        -0x4ct
        -0x48t
        -0x45t
        -0x43t
        -0x50t
        -0x42t
        -0x42t
        -0x4ct
        -0x46t
        -0x47t
    .end array-data
.end method

.method public static valueOf(Ljava/lang/String;)Lcom/facebook/ads/redexgen/X/QG;
    .locals 1

    .line 48983
    const-class v0, Lcom/facebook/ads/redexgen/X/QG;

    invoke-static {v0, p0}, Ljava/lang/Enum;->valueOf(Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Enum;

    move-result-object v0

    check-cast v0, Lcom/facebook/ads/redexgen/X/QG;

    return-object v0
.end method

.method public static values()[Lcom/facebook/ads/redexgen/X/QG;
    .locals 1

    .line 48984
    sget-object v0, Lcom/facebook/ads/redexgen/X/QG;->A02:[Lcom/facebook/ads/redexgen/X/QG;

    invoke-virtual {v0}, [Ljava/lang/Object;->clone()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [Lcom/facebook/ads/redexgen/X/QG;

    return-object v0
.end method
