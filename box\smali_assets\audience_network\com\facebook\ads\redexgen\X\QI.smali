.class public interface abstract Lcom/facebook/ads/redexgen/X/QI;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/facebook/ads/redexgen/X/QJ;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "ViewImplInflationListener"
.end annotation


# virtual methods
.method public abstract ADZ()V
.end method
