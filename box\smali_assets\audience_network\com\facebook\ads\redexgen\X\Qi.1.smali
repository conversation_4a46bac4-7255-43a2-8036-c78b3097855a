.class public final enum Lcom/facebook/ads/redexgen/X/Qi;
.super Ljava/lang/Enum;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/facebook/ads/redexgen/X/Qj;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x4019
    name = "AnimationState"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Enum<",
        "Lcom/facebook/ads/redexgen/X/Qi;",
        ">;"
    }
.end annotation


# static fields
.field public static A00:[B

.field public static A01:[Ljava/lang/String;

.field public static final synthetic A02:[Lcom/facebook/ads/redexgen/X/Qi;

.field public static final enum A03:Lcom/facebook/ads/redexgen/X/Qi;

.field public static final enum A04:Lcom/facebook/ads/redexgen/X/Qi;

.field public static final enum A05:Lcom/facebook/ads/redexgen/X/Qi;

.field public static final enum A06:Lcom/facebook/ads/redexgen/X/Qi;


# direct methods
.method public static constructor <clinit>()V
    .locals 9

    .line 2245
    const/16 v0, 0x8

    new-array v2, v0, [Ljava/lang/String;

    const/4 v1, 0x0

    const-string v0, "DteKWBudESjDtqSUyoTC5E"

    aput-object v0, v2, v1

    const/4 v1, 0x1

    const-string v0, "I5FSM6v9D3qkRXnarws6dVsmQqtQoJdF"

    aput-object v0, v2, v1

    const/4 v1, 0x2

    const-string v0, "2aIeIMdS22Kqsjw8nHnHH5KV6oj1373E"

    aput-object v0, v2, v1

    const/4 v1, 0x3

    const-string v0, "g"

    aput-object v0, v2, v1

    const/4 v1, 0x4

    const-string v0, "GOJ9oRRABJfcwjAA770tm42MgykplS5Q"

    aput-object v0, v2, v1

    const/4 v1, 0x5

    const-string v0, "ccCRkU4tJU1cPhlyS12s2rOBaxNg0sA2"

    aput-object v0, v2, v1

    const/4 v1, 0x6

    const-string v0, "PEuaDw0tTVC0zIT2D0mW6"

    aput-object v0, v2, v1

    const/4 v1, 0x7

    const-string v0, "B"

    aput-object v0, v2, v1

    sput-object v2, Lcom/facebook/ads/redexgen/X/Qi;->A01:[Ljava/lang/String;

    invoke-static {}, Lcom/facebook/ads/redexgen/X/Qi;->A01()V

    const/16 v2, 0x8

    const/16 v1, 0x9

    const/16 v0, 0x58

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Qi;->A00(III)Ljava/lang/String;

    move-result-object v0

    const/4 v8, 0x0

    new-instance v7, Lcom/facebook/ads/redexgen/X/Qi;

    invoke-direct {v7, v0, v8}, Lcom/facebook/ads/redexgen/X/Qi;-><init>(Ljava/lang/String;I)V

    sput-object v7, Lcom/facebook/ads/redexgen/X/Qi;->A04:Lcom/facebook/ads/redexgen/X/Qi;

    .line 2246
    const/4 v2, 0x0

    const/16 v1, 0x8

    const/16 v0, 0x24

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Qi;->A00(III)Ljava/lang/String;

    move-result-object v0

    const/4 v6, 0x1

    new-instance v5, Lcom/facebook/ads/redexgen/X/Qi;

    invoke-direct {v5, v0, v6}, Lcom/facebook/ads/redexgen/X/Qi;-><init>(Ljava/lang/String;I)V

    sput-object v5, Lcom/facebook/ads/redexgen/X/Qi;->A03:Lcom/facebook/ads/redexgen/X/Qi;

    .line 2247
    const/16 v2, 0x21

    const/16 v1, 0x11

    const/16 v0, 0x4f

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Qi;->A00(III)Ljava/lang/String;

    move-result-object v0

    const/4 v4, 0x2

    new-instance v3, Lcom/facebook/ads/redexgen/X/Qi;

    invoke-direct {v3, v0, v4}, Lcom/facebook/ads/redexgen/X/Qi;-><init>(Ljava/lang/String;I)V

    sput-object v3, Lcom/facebook/ads/redexgen/X/Qi;->A06:Lcom/facebook/ads/redexgen/X/Qi;

    .line 2248
    const/16 v2, 0x11

    const/16 v1, 0x10

    const/16 v0, 0x58

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Qi;->A00(III)Ljava/lang/String;

    move-result-object v0

    const/4 v2, 0x3

    new-instance v1, Lcom/facebook/ads/redexgen/X/Qi;

    invoke-direct {v1, v0, v2}, Lcom/facebook/ads/redexgen/X/Qi;-><init>(Ljava/lang/String;I)V

    sput-object v1, Lcom/facebook/ads/redexgen/X/Qi;->A05:Lcom/facebook/ads/redexgen/X/Qi;

    .line 2249
    const/4 v0, 0x4

    new-array v0, v0, [Lcom/facebook/ads/redexgen/X/Qi;

    aput-object v7, v0, v8

    aput-object v5, v0, v6

    aput-object v3, v0, v4

    aput-object v1, v0, v2

    sput-object v0, Lcom/facebook/ads/redexgen/X/Qi;->A02:[Lcom/facebook/ads/redexgen/X/Qi;

    return-void
.end method

.method public constructor <init>(Ljava/lang/String;I)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation

    .line 49390
    invoke-direct {p0, p1, p2}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    return-void
.end method

.method public static A00(III)Ljava/lang/String;
    .locals 2

    sget-object v1, Lcom/facebook/ads/redexgen/X/Qi;->A00:[B

    add-int v0, p0, p1

    invoke-static {v1, p0, v0}, Ljava/util/Arrays;->copyOfRange([BII)[B

    move-result-object p0

    const/4 v1, 0x0

    :goto_0
    array-length v0, p0

    if-ge v1, v0, :cond_0

    aget-byte v0, p0, v1

    xor-int/2addr v0, p2

    xor-int/lit8 v0, v0, 0x4c

    int-to-byte v0, v0

    aput-byte v0, p0, v1

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_0
    new-instance v0, Ljava/lang/String;

    invoke-direct {v0, p0}, Ljava/lang/String;-><init>([B)V

    return-object v0
.end method

.method public static A01()V
    .locals 4

    const/16 v0, 0x32

    new-array v3, v0, [B

    fill-array-data v3, :array_0

    sget-object v2, Lcom/facebook/ads/redexgen/X/Qi;->A01:[Ljava/lang/String;

    const/4 v0, 0x3

    aget-object v1, v2, v0

    const/4 v0, 0x7

    aget-object v0, v2, v0

    invoke-virtual {v1}, Ljava/lang/String;->length()I

    move-result v1

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v0

    if-eq v1, v0, :cond_0

    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    :cond_0
    sget-object v2, Lcom/facebook/ads/redexgen/X/Qi;->A01:[Ljava/lang/String;

    const-string v1, "Z"

    const/4 v0, 0x3

    aput-object v1, v2, v0

    const-string v1, "Z"

    const/4 v0, 0x7

    aput-object v1, v2, v0

    sput-object v3, Lcom/facebook/ads/redexgen/X/Qi;->A00:[B

    return-void

    :array_0
    .array-data 1
        0x29t
        0x26t
        0x21t
        0x25t
        0x29t
        0x3ct
        0x2dt
        0x2ct
        0x55t
        0x5at
        0x5dt
        0x59t
        0x55t
        0x40t
        0x5dt
        0x5at
        0x53t
        0x46t
        0x51t
        0x42t
        0x51t
        0x46t
        0x47t
        0x51t
        0x4bt
        0x55t
        0x5at
        0x5dt
        0x59t
        0x55t
        0x40t
        0x51t
        0x50t
        0x51t
        0x46t
        0x55t
        0x46t
        0x51t
        0x50t
        0x46t
        0x5ct
        0x42t
        0x4dt
        0x4at
        0x4et
        0x42t
        0x57t
        0x4at
        0x4dt
        0x44t
    .end array-data
.end method

.method public static valueOf(Ljava/lang/String;)Lcom/facebook/ads/redexgen/X/Qi;
    .locals 1

    .line 49391
    const-class v0, Lcom/facebook/ads/redexgen/X/Qi;

    invoke-static {v0, p0}, Ljava/lang/Enum;->valueOf(Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Enum;

    move-result-object v0

    check-cast v0, Lcom/facebook/ads/redexgen/X/Qi;

    return-object v0
.end method

.method public static values()[Lcom/facebook/ads/redexgen/X/Qi;
    .locals 1

    .line 49392
    sget-object v0, Lcom/facebook/ads/redexgen/X/Qi;->A02:[Lcom/facebook/ads/redexgen/X/Qi;

    invoke-virtual {v0}, [Ljava/lang/Object;->clone()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [Lcom/facebook/ads/redexgen/X/Qi;

    return-object v0
.end method
