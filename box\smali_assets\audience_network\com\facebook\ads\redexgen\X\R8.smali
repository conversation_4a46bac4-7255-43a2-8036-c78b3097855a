.class public final Lcom/facebook/ads/redexgen/X/R8;
.super Ljava/lang/Object;
.source ""

# interfaces
.implements Landroid/view/View$OnTouchListener;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/facebook/ads/redexgen/X/Ju;->setControlsAnchorView(Landroid/view/View;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# static fields
.field public static A01:[Ljava/lang/String;


# instance fields
.field public final synthetic A00:Lcom/facebook/ads/redexgen/X/Ju;


# direct methods
.method public static constructor <clinit>()V
    .locals 3

    .line 2255
    const/16 v0, 0x8

    new-array v2, v0, [Ljava/lang/String;

    const/4 v1, 0x0

    const-string v0, "mce3RnjQuKnQyZwY2MYCRKsU6qDPnRTv"

    aput-object v0, v2, v1

    const/4 v1, 0x1

    const-string v0, "6i"

    aput-object v0, v2, v1

    const/4 v1, 0x2

    const-string v0, "za"

    aput-object v0, v2, v1

    const/4 v1, 0x3

    const-string v0, "wlJBNttigYYlx2yteY8tG49BlED6WsVY"

    aput-object v0, v2, v1

    const/4 v1, 0x4

    const-string v0, "MVnjYKLYm17azeeTFPLJcrer3BwrjfHl"

    aput-object v0, v2, v1

    const/4 v1, 0x5

    const-string v0, "3Wee1YLSMyRTxC38n8Qm6eLBL9IfF0GN"

    aput-object v0, v2, v1

    const/4 v1, 0x6

    const-string v0, "3z6wLog4xxMob0RUOVoWJU4Ax22Nftgw"

    aput-object v0, v2, v1

    const/4 v1, 0x7

    const-string v0, "VSwWIM7jHdG3nIkp1OJVw48UFn88gUH7"

    aput-object v0, v2, v1

    sput-object v2, Lcom/facebook/ads/redexgen/X/R8;->A01:[Ljava/lang/String;

    return-void
.end method

.method public constructor <init>(Lcom/facebook/ads/redexgen/X/Ju;)V
    .locals 0

    .line 49701
    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/R8;->A00:Lcom/facebook/ads/redexgen/X/Ju;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final onTouch(Landroid/view/View;Landroid/view/MotionEvent;)Z
    .locals 5

    .line 49702
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/R8;->A00:Lcom/facebook/ads/redexgen/X/Ju;

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/Ju;->A0B(Lcom/facebook/ads/redexgen/X/Ju;)Z

    move-result v0

    const/4 v3, 0x1

    if-eqz v0, :cond_0

    .line 49703
    return v3

    .line 49704
    :cond_0
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/R8;->A00:Lcom/facebook/ads/redexgen/X/Ju;

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/Ju;->A01(Lcom/facebook/ads/redexgen/X/Ju;)Landroid/widget/MediaController;

    move-result-object v4

    sget-object v2, Lcom/facebook/ads/redexgen/X/R8;->A01:[Ljava/lang/String;

    const/4 v0, 0x4

    aget-object v1, v2, v0

    const/4 v0, 0x6

    aget-object v2, v2, v0

    const/4 v0, 0x0

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    invoke-virtual {v2, v0}, Ljava/lang/String;->charAt(I)C

    move-result v0

    if-eq v1, v0, :cond_4

    sget-object v2, Lcom/facebook/ads/redexgen/X/R8;->A01:[Ljava/lang/String;

    const-string v1, "HvEVRWVu4b3A6ir0KswDbL9zFlDBl7Vo"

    const/4 v0, 0x0

    aput-object v1, v2, v0

    const-string v1, "aSdZu49XGoInnb2I2ybBtqTd8GDfXNZF"

    const/4 v0, 0x3

    aput-object v1, v2, v0

    if-eqz v4, :cond_1

    invoke-virtual {p2}, Landroid/view/MotionEvent;->getAction()I

    move-result v0

    if-ne v0, v3, :cond_1

    .line 49705
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/R8;->A00:Lcom/facebook/ads/redexgen/X/Ju;

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/Ju;->A01(Lcom/facebook/ads/redexgen/X/Ju;)Landroid/widget/MediaController;

    move-result-object v4

    sget-object v1, Lcom/facebook/ads/redexgen/X/R8;->A01:[Ljava/lang/String;

    const/4 v0, 0x7

    aget-object v1, v1, v0

    const/16 v0, 0x9

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    const/16 v0, 0x6f

    if-eq v1, v0, :cond_2

    sget-object v2, Lcom/facebook/ads/redexgen/X/R8;->A01:[Ljava/lang/String;

    const-string v1, "PKAZCNEBQS248pKbgoN5vL2n8kWbkeSf"

    const/4 v0, 0x5

    aput-object v1, v2, v0

    invoke-virtual {v4}, Landroid/widget/MediaController;->isShowing()Z

    move-result v0

    if-eqz v0, :cond_3

    .line 49706
    :goto_0
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/R8;->A00:Lcom/facebook/ads/redexgen/X/Ju;

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/Ju;->A01(Lcom/facebook/ads/redexgen/X/Ju;)Landroid/widget/MediaController;

    move-result-object v0

    invoke-virtual {v0}, Landroid/widget/MediaController;->hide()V

    .line 49707
    :cond_1
    :goto_1
    return v3

    :cond_2
    sget-object v2, Lcom/facebook/ads/redexgen/X/R8;->A01:[Ljava/lang/String;

    const-string v1, "se9uNVwp2KR0SqcPH5E6zAp8mBDLV9kP"

    const/4 v0, 0x0

    aput-object v1, v2, v0

    const-string v1, "7jkgWI5wEJtN4OtVk5f4fU2wMaDr3zYP"

    const/4 v0, 0x3

    aput-object v1, v2, v0

    invoke-virtual {v4}, Landroid/widget/MediaController;->isShowing()Z

    move-result v0

    if-eqz v0, :cond_3

    goto :goto_0

    .line 49708
    :cond_3
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/R8;->A00:Lcom/facebook/ads/redexgen/X/Ju;

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/Ju;->A01(Lcom/facebook/ads/redexgen/X/Ju;)Landroid/widget/MediaController;

    move-result-object v0

    invoke-virtual {v0}, Landroid/widget/MediaController;->show()V

    goto :goto_1

    :cond_4
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0
.end method
