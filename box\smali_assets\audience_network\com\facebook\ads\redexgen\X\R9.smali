.class public interface abstract Lcom/facebook/ads/redexgen/X/R9;
.super Ljava/lang/Object;
.source ""


# virtual methods
.method public abstract A8Q()V
.end method

.method public abstract A8b()Z
.end method

.method public abstract A8c()Z
.end method

.method public abstract A9B()Z
.end method

.method public abstract ADu(ZI)V
.end method

.method public abstract AGN(I)V
.end method

.method public abstract AGS(Lcom/facebook/ads/redexgen/X/QM;I)V
.end method

.method public abstract AGZ(I)V
.end method

.method public abstract destroy()V
.end method

.method public abstract getCurrentPosition()I
.end method

.method public abstract getDuration()I
.end method

.method public abstract getInitialBufferTime()J
.end method

.method public abstract getStartReason()Lcom/facebook/ads/redexgen/X/QM;
.end method

.method public abstract getState()Lcom/facebook/ads/redexgen/X/RB;
.end method

.method public abstract getVideoHeight()I
.end method

.method public abstract getVideoWidth()I
.end method

.method public abstract getView()Landroid/view/View;
.end method

.method public abstract getVolume()F
.end method

.method public abstract seekTo(I)V
.end method

.method public abstract setBackgroundPlaybackEnabled(Z)V
.end method

.method public abstract setControlsAnchorView(Landroid/view/View;)V
.end method

.method public abstract setFullScreen(Z)V
.end method

.method public abstract setRequestedVolume(F)V
.end method

.method public abstract setVideoMPD(Ljava/lang/String;)V
.end method

.method public abstract setVideoStateChangeListener(Lcom/facebook/ads/redexgen/X/RC;)V
.end method

.method public abstract setup(Landroid/net/Uri;)V
.end method
