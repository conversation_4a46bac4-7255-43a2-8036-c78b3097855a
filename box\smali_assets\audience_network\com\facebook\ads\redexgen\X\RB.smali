.class public final enum Lcom/facebook/ads/redexgen/X/RB;
.super Ljava/lang/Enum;
.source ""


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Enum<",
        "Lcom/facebook/ads/redexgen/X/RB;",
        ">;"
    }
.end annotation


# static fields
.field public static A00:[B

.field public static final synthetic A01:[Lcom/facebook/ads/redexgen/X/RB;

.field public static final enum A02:Lcom/facebook/ads/redexgen/X/RB;

.field public static final enum A03:Lcom/facebook/ads/redexgen/X/RB;

.field public static final enum A04:Lcom/facebook/ads/redexgen/X/RB;

.field public static final enum A05:Lcom/facebook/ads/redexgen/X/RB;

.field public static final enum A06:Lcom/facebook/ads/redexgen/X/RB;

.field public static final enum A07:Lcom/facebook/ads/redexgen/X/RB;

.field public static final enum A08:Lcom/facebook/ads/redexgen/X/RB;

.field public static final enum A09:Lcom/facebook/ads/redexgen/X/RB;

.field public static final enum A0A:Lcom/facebook/ads/redexgen/X/RB;


# direct methods
.method public static constructor <clinit>()V
    .locals 16

    .line 2256
    invoke-static {}, Lcom/facebook/ads/redexgen/X/RB;->A01()V

    const/16 v2, 0xe

    const/4 v1, 0x4

    const/16 v0, 0x22

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/RB;->A00(III)Ljava/lang/String;

    move-result-object v0

    const/4 v15, 0x0

    new-instance v14, Lcom/facebook/ads/redexgen/X/RB;

    invoke-direct {v14, v0, v15}, Lcom/facebook/ads/redexgen/X/RB;-><init>(Ljava/lang/String;I)V

    sput-object v14, Lcom/facebook/ads/redexgen/X/RB;->A04:Lcom/facebook/ads/redexgen/X/RB;

    .line 2257
    const/16 v2, 0x32

    const/16 v1, 0x9

    const/16 v0, 0x64

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/RB;->A00(III)Ljava/lang/String;

    move-result-object v0

    const/4 v13, 0x1

    new-instance v12, Lcom/facebook/ads/redexgen/X/RB;

    invoke-direct {v12, v0, v13}, Lcom/facebook/ads/redexgen/X/RB;-><init>(Ljava/lang/String;I)V

    sput-object v12, Lcom/facebook/ads/redexgen/X/RB;->A08:Lcom/facebook/ads/redexgen/X/RB;

    .line 2258
    const/16 v2, 0x2a

    const/16 v1, 0x8

    const/16 v0, 0x42

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/RB;->A00(III)Ljava/lang/String;

    move-result-object v0

    const/4 v11, 0x2

    new-instance v10, Lcom/facebook/ads/redexgen/X/RB;

    invoke-direct {v10, v0, v11}, Lcom/facebook/ads/redexgen/X/RB;-><init>(Ljava/lang/String;I)V

    sput-object v10, Lcom/facebook/ads/redexgen/X/RB;->A07:Lcom/facebook/ads/redexgen/X/RB;

    .line 2259
    const/16 v2, 0x42

    const/4 v1, 0x7

    const/16 v0, 0xe

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/RB;->A00(III)Ljava/lang/String;

    move-result-object v0

    const/4 v9, 0x3

    new-instance v8, Lcom/facebook/ads/redexgen/X/RB;

    invoke-direct {v8, v0, v9}, Lcom/facebook/ads/redexgen/X/RB;-><init>(Ljava/lang/String;I)V

    sput-object v8, Lcom/facebook/ads/redexgen/X/RB;->A0A:Lcom/facebook/ads/redexgen/X/RB;

    .line 2260
    const/16 v2, 0x12

    const/4 v1, 0x6

    const/16 v0, 0x63

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/RB;->A00(III)Ljava/lang/String;

    move-result-object v1

    const/4 v7, 0x4

    new-instance v0, Lcom/facebook/ads/redexgen/X/RB;

    invoke-direct {v0, v1, v7}, Lcom/facebook/ads/redexgen/X/RB;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lcom/facebook/ads/redexgen/X/RB;->A05:Lcom/facebook/ads/redexgen/X/RB;

    .line 2261
    const/4 v3, 0x0

    const/16 v2, 0x9

    const/16 v1, 0x77

    invoke-static {v3, v2, v1}, Lcom/facebook/ads/redexgen/X/RB;->A00(III)Ljava/lang/String;

    move-result-object v2

    const/4 v1, 0x5

    new-instance v6, Lcom/facebook/ads/redexgen/X/RB;

    invoke-direct {v6, v2, v1}, Lcom/facebook/ads/redexgen/X/RB;-><init>(Ljava/lang/String;I)V

    sput-object v6, Lcom/facebook/ads/redexgen/X/RB;->A02:Lcom/facebook/ads/redexgen/X/RB;

    .line 2262
    const/16 v3, 0x18

    const/16 v2, 0x12

    const/16 v1, 0x4e

    invoke-static {v3, v2, v1}, Lcom/facebook/ads/redexgen/X/RB;->A00(III)Ljava/lang/String;

    move-result-object v2

    const/4 v1, 0x6

    new-instance v5, Lcom/facebook/ads/redexgen/X/RB;

    invoke-direct {v5, v2, v1}, Lcom/facebook/ads/redexgen/X/RB;-><init>(Ljava/lang/String;I)V

    sput-object v5, Lcom/facebook/ads/redexgen/X/RB;->A06:Lcom/facebook/ads/redexgen/X/RB;

    .line 2263
    const/16 v3, 0x9

    const/4 v2, 0x5

    const/16 v1, 0x77

    invoke-static {v3, v2, v1}, Lcom/facebook/ads/redexgen/X/RB;->A00(III)Ljava/lang/String;

    move-result-object v2

    const/4 v1, 0x7

    new-instance v4, Lcom/facebook/ads/redexgen/X/RB;

    invoke-direct {v4, v2, v1}, Lcom/facebook/ads/redexgen/X/RB;-><init>(Ljava/lang/String;I)V

    sput-object v4, Lcom/facebook/ads/redexgen/X/RB;->A03:Lcom/facebook/ads/redexgen/X/RB;

    .line 2264
    const/16 v3, 0x3b

    const/4 v2, 0x7

    const/16 v1, 0x2e

    invoke-static {v3, v2, v1}, Lcom/facebook/ads/redexgen/X/RB;->A00(III)Ljava/lang/String;

    move-result-object v1

    const/16 v3, 0x8

    new-instance v2, Lcom/facebook/ads/redexgen/X/RB;

    invoke-direct {v2, v1, v3}, Lcom/facebook/ads/redexgen/X/RB;-><init>(Ljava/lang/String;I)V

    sput-object v2, Lcom/facebook/ads/redexgen/X/RB;->A09:Lcom/facebook/ads/redexgen/X/RB;

    .line 2265
    const/16 v1, 0x9

    new-array v1, v1, [Lcom/facebook/ads/redexgen/X/RB;

    aput-object v14, v1, v15

    aput-object v12, v1, v13

    aput-object v10, v1, v11

    aput-object v8, v1, v9

    aput-object v0, v1, v7

    const/4 v0, 0x5

    aput-object v6, v1, v0

    const/4 v0, 0x6

    aput-object v5, v1, v0

    const/4 v0, 0x7

    aput-object v4, v1, v0

    aput-object v2, v1, v3

    sput-object v1, Lcom/facebook/ads/redexgen/X/RB;->A01:[Lcom/facebook/ads/redexgen/X/RB;

    return-void
.end method

.method public constructor <init>(Ljava/lang/String;I)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation

    .line 49719
    invoke-direct {p0, p1, p2}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    return-void
.end method

.method public static A00(III)Ljava/lang/String;
    .locals 2

    sget-object v1, Lcom/facebook/ads/redexgen/X/RB;->A00:[B

    add-int v0, p0, p1

    invoke-static {v1, p0, v0}, Ljava/util/Arrays;->copyOfRange([BII)[B

    move-result-object p0

    const/4 v1, 0x0

    :goto_0
    array-length v0, p0

    if-ge v1, v0, :cond_0

    aget-byte v0, p0, v1

    sub-int/2addr v0, p2

    add-int/lit8 v0, v0, -0x3

    int-to-byte v0, v0

    aput-byte v0, p0, v1

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_0
    new-instance v0, Ljava/lang/String;

    invoke-direct {v0, p0}, Ljava/lang/String;-><init>([B)V

    return-object v0
.end method

.method public static A01()V
    .locals 1

    const/16 v0, 0x49

    new-array v0, v0, [B

    fill-array-data v0, :array_0

    sput-object v0, Lcom/facebook/ads/redexgen/X/RB;->A00:[B

    return-void

    :array_0
    .array-data 1
        -0x44t
        -0x31t
        -0x40t
        -0x40t
        -0x41t
        -0x34t
        -0x3dt
        -0x38t
        -0x3ft
        -0x41t
        -0x34t
        -0x34t
        -0x37t
        -0x34t
        0x6et
        0x69t
        0x71t
        0x6at
        -0x4at
        -0x59t
        -0x45t
        -0x47t
        -0x55t
        -0x56t
        -0x5ft
        -0x63t
        -0x6et
        -0x56t
        -0x6dt
        -0x6et
        -0x6ct
        -0x64t
        -0x50t
        -0x6ct
        -0x60t
        -0x62t
        -0x5ft
        -0x63t
        -0x6at
        -0x5bt
        -0x6at
        -0x6bt
        -0x6bt
        -0x69t
        -0x76t
        -0x6bt
        -0x7at
        -0x69t
        -0x76t
        -0x77t
        -0x49t
        -0x47t
        -0x54t
        -0x49t
        -0x58t
        -0x47t
        -0x50t
        -0x4bt
        -0x52t
        -0x7ct
        0x7ct
        0x7at
        -0x7ft
        -0x7ft
        0x76t
        0x75t
        0x64t
        0x65t
        0x52t
        0x63t
        0x65t
        0x56t
        0x55t
    .end array-data
.end method

.method public static valueOf(Ljava/lang/String;)Lcom/facebook/ads/redexgen/X/RB;
    .locals 1

    .line 49720
    const-class v0, Lcom/facebook/ads/redexgen/X/RB;

    invoke-static {v0, p0}, Ljava/lang/Enum;->valueOf(Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Enum;

    move-result-object v0

    check-cast v0, Lcom/facebook/ads/redexgen/X/RB;

    return-object v0
.end method

.method public static values()[Lcom/facebook/ads/redexgen/X/RB;
    .locals 1

    .line 49721
    sget-object v0, Lcom/facebook/ads/redexgen/X/RB;->A01:[Lcom/facebook/ads/redexgen/X/RB;

    invoke-virtual {v0}, [Ljava/lang/Object;->clone()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [Lcom/facebook/ads/redexgen/X/RB;

    return-object v0
.end method
