.class public interface abstract Lcom/facebook/ads/redexgen/X/RJ;
.super Ljava/lang/Object;
.source ""


# virtual methods
.method public abstract A68()[B
.end method

.method public abstract A69()Ljava/lang/String;
.end method

.method public abstract A79()Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;>;"
        }
    .end annotation
.end method

.method public abstract A83()I
.end method

.method public abstract getUrl()Ljava/lang/String;
.end method
