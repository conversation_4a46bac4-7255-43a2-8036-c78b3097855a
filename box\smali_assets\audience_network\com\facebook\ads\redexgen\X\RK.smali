.class public interface abstract Lcom/facebook/ads/redexgen/X/RK;
.super Ljava/lang/Object;
.source ""


# virtual methods
.method public abstract ADy(Ljava/lang/String;Ljava/util/Map;)Lcom/facebook/ads/redexgen/X/RJ;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ">;)",
            "Lcom/facebook/ads/redexgen/X/RJ;"
        }
    .end annotation

    .annotation runtime Ljava/lang/Deprecated;
    .end annotation
.end method

.method public abstract ADz(Ljava/lang/String;[B)Lcom/facebook/ads/redexgen/X/RJ;
    .annotation runtime Ljava/lang/Deprecated;
    .end annotation
.end method

.method public abstract AE0(Ljava/lang/String;[BLcom/facebook/ads/redexgen/X/RL;)V
.end method
