.class public abstract Lcom/facebook/ads/redexgen/X/RM;
.super Ljava/lang/Object;
.source ""


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 50018
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static A00()Lcom/facebook/ads/redexgen/X/Jt;
    .locals 1

    .line 50019
    new-instance v0, Lcom/facebook/ads/redexgen/X/Jt;

    invoke-direct {v0}, Lcom/facebook/ads/redexgen/X/Jt;-><init>()V

    return-object v0
.end method
