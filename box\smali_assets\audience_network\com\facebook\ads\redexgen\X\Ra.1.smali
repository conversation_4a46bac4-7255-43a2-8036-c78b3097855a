.class public interface abstract Lcom/facebook/ads/redexgen/X/Ra;
.super Ljava/lang/Object;
.source ""


# virtual methods
.method public abstract A98()Z
.end method

.method public abstract A9y(Ljava/net/HttpURLConnection;Ljava/lang/Object;)V
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation
.end method

.method public abstract A9z(Lcom/facebook/ads/redexgen/X/RJ;)V
.end method
