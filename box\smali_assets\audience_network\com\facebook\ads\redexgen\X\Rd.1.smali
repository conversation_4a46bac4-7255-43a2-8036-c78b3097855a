.class public interface abstract Lcom/facebook/ads/redexgen/X/Rd;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/facebook/ads/redexgen/X/Re;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "OpenUrlPostProcessDelegate"
.end annotation


# virtual methods
.method public abstract AEA()V
.end method

.method public abstract AEC(Lcom/facebook/ads/redexgen/X/Rf;)V
.end method
