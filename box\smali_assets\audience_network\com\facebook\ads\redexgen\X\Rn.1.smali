.class public interface abstract Lcom/facebook/ads/redexgen/X/Rn;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/facebook/ads/redexgen/X/Rp;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "Factory"
.end annotation


# virtual methods
.method public abstract A4V(Lcom/facebook/ads/redexgen/X/Ro;)Lcom/facebook/ads/redexgen/X/Hl;
.end method
