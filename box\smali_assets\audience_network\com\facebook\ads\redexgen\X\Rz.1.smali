.class public abstract Lcom/facebook/ads/redexgen/X/Rz;
.super Ljava/lang/Object;
.source ""


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 50387
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static A00()Lcom/facebook/ads/redexgen/X/HZ;
    .locals 1

    .line 50388
    new-instance v0, Lcom/facebook/ads/redexgen/X/HZ;

    invoke-direct {v0}, Lcom/facebook/ads/redexgen/X/HZ;-><init>()V

    return-object v0
.end method
