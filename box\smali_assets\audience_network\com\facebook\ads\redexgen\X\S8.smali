.class public final Lcom/facebook/ads/redexgen/X/S8;
.super Lcom/facebook/ads/redexgen/X/KT;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/facebook/ads/redexgen/X/9I;->A0i()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field public final synthetic A00:Lcom/facebook/ads/redexgen/X/9I;


# direct methods
.method public constructor <init>(Lcom/facebook/ads/redexgen/X/9I;)V
    .locals 0

    .line 50417
    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/S8;->A00:Lcom/facebook/ads/redexgen/X/9I;

    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/KT;-><init>()V

    return-void
.end method


# virtual methods
.method public final A06()V
    .locals 4

    .line 50418
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/S8;->A00:Lcom/facebook/ads/redexgen/X/9I;

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/9I;->A09(Lcom/facebook/ads/redexgen/X/9I;)Lcom/facebook/ads/redexgen/X/SA;

    move-result-object v0

    .line 50419
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/SA;->getEventBus()Lcom/facebook/ads/redexgen/X/8r;

    move-result-object v3

    const/16 v0, 0xb

    new-array v2, v0, [Lcom/facebook/ads/redexgen/X/8s;

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/S8;->A00:Lcom/facebook/ads/redexgen/X/9I;

    .line 50420
    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/9I;->A0B(Lcom/facebook/ads/redexgen/X/9I;)Lcom/facebook/ads/redexgen/X/MV;

    move-result-object v1

    const/4 v0, 0x0

    aput-object v1, v2, v0

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/S8;->A00:Lcom/facebook/ads/redexgen/X/9I;

    .line 50421
    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/9I;->A02(Lcom/facebook/ads/redexgen/X/9I;)Lcom/facebook/ads/redexgen/X/8s;

    move-result-object v1

    const/4 v0, 0x1

    aput-object v1, v2, v0

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/S8;->A00:Lcom/facebook/ads/redexgen/X/9I;

    .line 50422
    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/9I;->A03(Lcom/facebook/ads/redexgen/X/9I;)Lcom/facebook/ads/redexgen/X/8s;

    move-result-object v1

    const/4 v0, 0x2

    aput-object v1, v2, v0

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/S8;->A00:Lcom/facebook/ads/redexgen/X/9I;

    .line 50423
    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/9I;->A04(Lcom/facebook/ads/redexgen/X/9I;)Lcom/facebook/ads/redexgen/X/8s;

    move-result-object v1

    const/4 v0, 0x3

    aput-object v1, v2, v0

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/S8;->A00:Lcom/facebook/ads/redexgen/X/9I;

    .line 50424
    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/9I;->A05(Lcom/facebook/ads/redexgen/X/9I;)Lcom/facebook/ads/redexgen/X/8s;

    move-result-object v1

    const/4 v0, 0x4

    aput-object v1, v2, v0

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/S8;->A00:Lcom/facebook/ads/redexgen/X/9I;

    .line 50425
    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/9I;->A06(Lcom/facebook/ads/redexgen/X/9I;)Lcom/facebook/ads/redexgen/X/8s;

    move-result-object v1

    const/4 v0, 0x5

    aput-object v1, v2, v0

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/S8;->A00:Lcom/facebook/ads/redexgen/X/9I;

    .line 50426
    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/9I;->A07(Lcom/facebook/ads/redexgen/X/9I;)Lcom/facebook/ads/redexgen/X/8s;

    move-result-object v1

    const/4 v0, 0x6

    aput-object v1, v2, v0

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/S8;->A00:Lcom/facebook/ads/redexgen/X/9I;

    .line 50427
    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/9I;->A08(Lcom/facebook/ads/redexgen/X/9I;)Lcom/facebook/ads/redexgen/X/8s;

    move-result-object v1

    const/4 v0, 0x7

    aput-object v1, v2, v0

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/S8;->A00:Lcom/facebook/ads/redexgen/X/9I;

    .line 50428
    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/9I;->A00(Lcom/facebook/ads/redexgen/X/9I;)Lcom/facebook/ads/redexgen/X/8s;

    move-result-object v1

    const/16 v0, 0x8

    aput-object v1, v2, v0

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/S8;->A00:Lcom/facebook/ads/redexgen/X/9I;

    .line 50429
    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/9I;->A0A(Lcom/facebook/ads/redexgen/X/9I;)Lcom/facebook/ads/redexgen/X/NX;

    move-result-object v1

    const/16 v0, 0x9

    aput-object v1, v2, v0

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/S8;->A00:Lcom/facebook/ads/redexgen/X/9I;

    .line 50430
    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/9I;->A01(Lcom/facebook/ads/redexgen/X/9I;)Lcom/facebook/ads/redexgen/X/8s;

    move-result-object v1

    const/16 v0, 0xa

    aput-object v1, v2, v0

    .line 50431
    invoke-virtual {v3, v2}, Lcom/facebook/ads/redexgen/X/8r;->A04([Lcom/facebook/ads/redexgen/X/8s;)V

    .line 50432
    return-void
.end method
