.class public final Lcom/facebook/ads/redexgen/X/SN;
.super Lcom/facebook/ads/redexgen/X/56;
.source ""

# interfaces
.implements Lcom/facebook/ads/redexgen/X/MJ;


# static fields
.field public static A0B:[Ljava/lang/String;


# instance fields
.field public A00:Lcom/facebook/ads/redexgen/X/1U;

.field public A01:Lcom/facebook/ads/redexgen/X/RD;

.field public A02:Lcom/facebook/ads/redexgen/X/RE;

.field public A03:Lcom/facebook/ads/redexgen/X/RE;

.field public final A04:I

.field public final A05:I

.field public final A06:I

.field public final A07:I

.field public final A08:Landroid/util/SparseBooleanArray;

.field public final A09:Lcom/facebook/ads/redexgen/X/Yn;

.field public final A0A:Lcom/facebook/ads/redexgen/X/B4;


# direct methods
.method public static constructor <clinit>()V
    .locals 3

    .line 2336
    const/16 v0, 0x8

    new-array v2, v0, [Ljava/lang/String;

    const/4 v1, 0x0

    const-string v0, "av2tITPhYKbVn20ukUfJR25ELbcgjHbj"

    aput-object v0, v2, v1

    const/4 v1, 0x1

    const-string v0, "MLeLe181lhP7dC21QqjxU1VDKV9lNkzJ"

    aput-object v0, v2, v1

    const/4 v1, 0x2

    const-string v0, "XUmY4VBJ2tlJWKlPJ7YkBdCsKtcC"

    aput-object v0, v2, v1

    const/4 v1, 0x3

    const-string v0, "bK6oHsXedskfaWsjnVeS6YyvP0sRA20b"

    aput-object v0, v2, v1

    const/4 v1, 0x4

    const-string v0, "BhCeFQN4dHt9z3MLE3p7bnIzhrf86Cw8"

    aput-object v0, v2, v1

    const/4 v1, 0x5

    const-string v0, "mLW9T0fP2Q5Hob07hRXsv0QUH1fN4"

    aput-object v0, v2, v1

    const/4 v1, 0x6

    const-string v0, "pNzPXZ5fcby2PvmlRsfUzns43oHDF5Mi"

    aput-object v0, v2, v1

    const/4 v1, 0x7

    const-string v0, "3YktW7BrcDIc4Xw2f2zdWv5xFETbHIAE"

    aput-object v0, v2, v1

    sput-object v2, Lcom/facebook/ads/redexgen/X/SN;->A0B:[Ljava/lang/String;

    return-void
.end method

.method public constructor <init>(Lcom/facebook/ads/redexgen/X/B4;Landroid/util/SparseBooleanArray;Lcom/facebook/ads/redexgen/X/RE;IIIILcom/facebook/ads/redexgen/X/Yn;Lcom/facebook/ads/redexgen/X/1U;)V
    .locals 0

    .line 51006
    invoke-direct {p0, p1}, Lcom/facebook/ads/redexgen/X/56;-><init>(Landroid/view/View;)V

    .line 51007
    iput-object p8, p0, Lcom/facebook/ads/redexgen/X/SN;->A09:Lcom/facebook/ads/redexgen/X/Yn;

    .line 51008
    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/SN;->A0A:Lcom/facebook/ads/redexgen/X/B4;

    .line 51009
    iput-object p2, p0, Lcom/facebook/ads/redexgen/X/SN;->A08:Landroid/util/SparseBooleanArray;

    .line 51010
    iput-object p3, p0, Lcom/facebook/ads/redexgen/X/SN;->A02:Lcom/facebook/ads/redexgen/X/RE;

    .line 51011
    iput p4, p0, Lcom/facebook/ads/redexgen/X/SN;->A04:I

    .line 51012
    iput p5, p0, Lcom/facebook/ads/redexgen/X/SN;->A05:I

    .line 51013
    iput p6, p0, Lcom/facebook/ads/redexgen/X/SN;->A06:I

    .line 51014
    iput p7, p0, Lcom/facebook/ads/redexgen/X/SN;->A07:I

    .line 51015
    iput-object p9, p0, Lcom/facebook/ads/redexgen/X/SN;->A00:Lcom/facebook/ads/redexgen/X/1U;

    .line 51016
    return-void
.end method

.method public static synthetic A03(Lcom/facebook/ads/redexgen/X/SN;)Landroid/util/SparseBooleanArray;
    .locals 0

    .line 51017
    iget-object p0, p0, Lcom/facebook/ads/redexgen/X/SN;->A08:Landroid/util/SparseBooleanArray;

    return-object p0
.end method

.method public static synthetic A04(Lcom/facebook/ads/redexgen/X/SN;)Lcom/facebook/ads/redexgen/X/1U;
    .locals 0

    .line 51018
    iget-object p0, p0, Lcom/facebook/ads/redexgen/X/SN;->A00:Lcom/facebook/ads/redexgen/X/1U;

    return-object p0
.end method

.method public static synthetic A07(Lcom/facebook/ads/redexgen/X/SN;)Lcom/facebook/ads/redexgen/X/Yn;
    .locals 0

    .line 51019
    iget-object p0, p0, Lcom/facebook/ads/redexgen/X/SN;->A09:Lcom/facebook/ads/redexgen/X/Yn;

    return-object p0
.end method

.method public static synthetic A08(Lcom/facebook/ads/redexgen/X/SN;)Lcom/facebook/ads/redexgen/X/RE;
    .locals 0

    .line 51020
    iget-object p0, p0, Lcom/facebook/ads/redexgen/X/SN;->A02:Lcom/facebook/ads/redexgen/X/RE;

    return-object p0
.end method

.method public static synthetic A09(Lcom/facebook/ads/redexgen/X/SN;)Lcom/facebook/ads/redexgen/X/RE;
    .locals 0

    .line 51021
    iget-object p0, p0, Lcom/facebook/ads/redexgen/X/SN;->A03:Lcom/facebook/ads/redexgen/X/RE;

    return-object p0
.end method

.method private A0A(Lcom/facebook/ads/redexgen/X/J2;Lcom/facebook/ads/redexgen/X/Lg;Ljava/lang/String;Lcom/facebook/ads/redexgen/X/Pp;)V
    .locals 9

    .line 51022
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/SN;->A08:Landroid/util/SparseBooleanArray;

    move-object v5, p4

    invoke-virtual {v5}, Lcom/facebook/ads/redexgen/X/Pp;->A02()I

    move-result v0

    invoke-virtual {v1, v0}, Landroid/util/SparseBooleanArray;->get(I)Z

    move-result v0

    if-eqz v0, :cond_0

    .line 51023
    return-void

    .line 51024
    :cond_0
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/SN;->A03:Lcom/facebook/ads/redexgen/X/RE;

    if-eqz v0, :cond_2

    .line 51025
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/RE;->A0V()V

    sget-object v1, Lcom/facebook/ads/redexgen/X/SN;->A0B:[Ljava/lang/String;

    const/4 v0, 0x4

    aget-object v1, v1, v0

    const/16 v0, 0x10

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    const/16 v0, 0x45

    if-eq v1, v0, :cond_1

    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    .line 51026
    :cond_1
    sget-object v2, Lcom/facebook/ads/redexgen/X/SN;->A0B:[Ljava/lang/String;

    const-string v1, "iteeE4SnfkkyQnEAEqI0za4snrHpWaXy"

    const/4 v0, 0x4

    aput-object v1, v2, v0

    const/4 v0, 0x0

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/SN;->A03:Lcom/facebook/ads/redexgen/X/RE;

    .line 51027
    :cond_2
    invoke-virtual {v5}, Lcom/facebook/ads/redexgen/X/Pp;->A04()Ljava/util/Map;

    move-result-object v7

    .line 51028
    .local v0, "urlParams":Ljava/util/Map;, "Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"
    new-instance v2, Lcom/facebook/ads/redexgen/X/SP;

    move-object v3, p0

    move-object v6, p1

    move-object v8, p2

    move-object v4, p3

    invoke-direct/range {v2 .. v8}, Lcom/facebook/ads/redexgen/X/SP;-><init>(Lcom/facebook/ads/redexgen/X/SN;Ljava/lang/String;Lcom/facebook/ads/redexgen/X/Pp;Lcom/facebook/ads/redexgen/X/J2;Ljava/util/Map;Lcom/facebook/ads/redexgen/X/Lg;)V

    iput-object v2, p0, Lcom/facebook/ads/redexgen/X/SN;->A01:Lcom/facebook/ads/redexgen/X/RD;

    .line 51029
    iget-object v4, p0, Lcom/facebook/ads/redexgen/X/SN;->A0A:Lcom/facebook/ads/redexgen/X/B4;

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/SN;->A01:Lcom/facebook/ads/redexgen/X/RD;

    new-instance v3, Ljava/lang/ref/WeakReference;

    invoke-direct {v3, v0}, Ljava/lang/ref/WeakReference;-><init>(Ljava/lang/Object;)V

    iget-object v2, p0, Lcom/facebook/ads/redexgen/X/SN;->A09:Lcom/facebook/ads/redexgen/X/Yn;

    const/16 v0, 0xa

    new-instance v1, Lcom/facebook/ads/redexgen/X/RE;

    invoke-direct {v1, v4, v0, v3, v2}, Lcom/facebook/ads/redexgen/X/RE;-><init>(Landroid/view/View;ILjava/lang/ref/WeakReference;Lcom/facebook/ads/redexgen/X/Yn;)V

    iput-object v1, p0, Lcom/facebook/ads/redexgen/X/SN;->A03:Lcom/facebook/ads/redexgen/X/RE;

    .line 51030
    const/4 v0, 0x0

    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/RE;->A0Y(Z)V

    .line 51031
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/SN;->A03:Lcom/facebook/ads/redexgen/X/RE;

    const/16 v1, 0x64

    invoke-virtual {v0, v1}, Lcom/facebook/ads/redexgen/X/RE;->A0W(I)V

    .line 51032
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/SN;->A03:Lcom/facebook/ads/redexgen/X/RE;

    invoke-virtual {v0, v1}, Lcom/facebook/ads/redexgen/X/RE;->A0X(I)V

    .line 51033
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/SN;->A0A:Lcom/facebook/ads/redexgen/X/B4;

    new-instance v0, Lcom/facebook/ads/redexgen/X/SO;

    invoke-direct {v0, p0, v5}, Lcom/facebook/ads/redexgen/X/SO;-><init>(Lcom/facebook/ads/redexgen/X/SN;Lcom/facebook/ads/redexgen/X/Pp;)V

    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/B4;->setOnAssetsLoadedListener(Lcom/facebook/ads/redexgen/X/Oq;)V

    .line 51034
    return-void
.end method


# virtual methods
.method public final A0j(Lcom/facebook/ads/redexgen/X/Pp;Lcom/facebook/ads/redexgen/X/J2;Lcom/facebook/ads/redexgen/X/6c;Lcom/facebook/ads/redexgen/X/Lg;Ljava/lang/String;)V
    .locals 5

    .line 51035
    invoke-virtual {p1}, Lcom/facebook/ads/redexgen/X/Pp;->A02()I

    move-result v4

    .line 51036
    .local v0, "position":I
    iget-object v2, p0, Lcom/facebook/ads/redexgen/X/SN;->A0A:Lcom/facebook/ads/redexgen/X/B4;

    const v1, -0x5f000010

    invoke-static {v4}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v0

    invoke-virtual {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/B4;->setTag(ILjava/lang/Object;)V

    .line 51037
    iget v1, p0, Lcom/facebook/ads/redexgen/X/SN;->A04:I

    const/4 v0, -0x2

    new-instance v2, Landroid/view/ViewGroup$MarginLayoutParams;

    invoke-direct {v2, v1, v0}, Landroid/view/ViewGroup$MarginLayoutParams;-><init>(II)V

    .line 51038
    .local v1, "params":Landroid/view/ViewGroup$MarginLayoutParams;
    if-nez v4, :cond_2

    iget v3, p0, Lcom/facebook/ads/redexgen/X/SN;->A05:I

    .line 51039
    .local v2, "leftMargin":I
    :goto_0
    iget v0, p0, Lcom/facebook/ads/redexgen/X/SN;->A07:I

    add-int/lit8 v0, v0, -0x1

    if-lt v4, v0, :cond_1

    iget v1, p0, Lcom/facebook/ads/redexgen/X/SN;->A05:I

    .line 51040
    .local v3, "rightMargin":I
    :goto_1
    const/4 v0, 0x0

    invoke-virtual {v2, v3, v0, v1, v0}, Landroid/view/ViewGroup$MarginLayoutParams;->setMargins(IIII)V

    .line 51041
    invoke-virtual {p1}, Lcom/facebook/ads/redexgen/X/Pp;->A03()Lcom/facebook/ads/redexgen/X/1G;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/1G;->A0D()Lcom/facebook/ads/redexgen/X/1J;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/1J;->A07()Ljava/lang/String;

    move-result-object v4

    .line 51042
    .local v4, "imageUrl":Ljava/lang/String;
    invoke-virtual {p1}, Lcom/facebook/ads/redexgen/X/Pp;->A03()Lcom/facebook/ads/redexgen/X/1G;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/1G;->A0D()Lcom/facebook/ads/redexgen/X/1J;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/1J;->A08()Ljava/lang/String;

    move-result-object v3

    .line 51043
    .local p0, "videoUrl":Ljava/lang/String;
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/SN;->A0A:Lcom/facebook/ads/redexgen/X/B4;

    invoke-static {v3}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    xor-int/lit8 v0, v0, 0x1

    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/B4;->setIsVideo(Z)V

    .line 51044
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/SN;->A0A:Lcom/facebook/ads/redexgen/X/B4;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/B4;->A18()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 51045
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/SN;->A0A:Lcom/facebook/ads/redexgen/X/B4;

    invoke-virtual {v0, v4}, Lcom/facebook/ads/redexgen/X/B4;->setVideoPlaceholderUrl(Ljava/lang/String;)V

    .line 51046
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/SN;->A0A:Lcom/facebook/ads/redexgen/X/B4;

    invoke-virtual {p3, v3}, Lcom/facebook/ads/redexgen/X/6c;->A0S(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/B4;->setVideoUrl(Ljava/lang/String;)V

    .line 51047
    :goto_2
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/SN;->A0A:Lcom/facebook/ads/redexgen/X/B4;

    invoke-virtual {v0, v2}, Lcom/facebook/ads/redexgen/X/B4;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 51048
    iget-object v2, p0, Lcom/facebook/ads/redexgen/X/SN;->A0A:Lcom/facebook/ads/redexgen/X/B4;

    .line 51049
    invoke-virtual {p1}, Lcom/facebook/ads/redexgen/X/Pp;->A03()Lcom/facebook/ads/redexgen/X/1G;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/1G;->A0E()Lcom/facebook/ads/redexgen/X/1N;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/1N;->A06()Ljava/lang/String;

    move-result-object v1

    .line 51050
    invoke-virtual {p1}, Lcom/facebook/ads/redexgen/X/Pp;->A03()Lcom/facebook/ads/redexgen/X/1G;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/1G;->A0E()Lcom/facebook/ads/redexgen/X/1N;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/1N;->A01()Ljava/lang/String;

    move-result-object v0

    .line 51051
    invoke-virtual {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/B4;->setAdTitleAndDescription(Ljava/lang/String;Ljava/lang/String;)V

    .line 51052
    iget-object v2, p0, Lcom/facebook/ads/redexgen/X/SN;->A0A:Lcom/facebook/ads/redexgen/X/B4;

    invoke-virtual {p1}, Lcom/facebook/ads/redexgen/X/Pp;->A03()Lcom/facebook/ads/redexgen/X/1G;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/1G;->A0F()Lcom/facebook/ads/redexgen/X/1Q;

    move-result-object v1

    invoke-virtual {p1}, Lcom/facebook/ads/redexgen/X/Pp;->A04()Ljava/util/Map;

    move-result-object v0

    invoke-virtual {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/B4;->setCTAInfo(Lcom/facebook/ads/redexgen/X/1Q;Ljava/util/Map;)V

    .line 51053
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/SN;->A0A:Lcom/facebook/ads/redexgen/X/B4;

    invoke-virtual {p1}, Lcom/facebook/ads/redexgen/X/Pp;->A04()Ljava/util/Map;

    move-result-object v0

    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/B4;->A1A(Ljava/util/Map;)V

    .line 51054
    invoke-direct {p0, p2, p4, p5, p1}, Lcom/facebook/ads/redexgen/X/SN;->A0A(Lcom/facebook/ads/redexgen/X/J2;Lcom/facebook/ads/redexgen/X/Lg;Ljava/lang/String;Lcom/facebook/ads/redexgen/X/Pp;)V

    .line 51055
    return-void

    .line 51056
    :cond_0
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/SN;->A0A:Lcom/facebook/ads/redexgen/X/B4;

    invoke-virtual {v0, v4}, Lcom/facebook/ads/redexgen/X/B4;->setImageUrl(Ljava/lang/String;)V

    goto :goto_2

    .line 51057
    :cond_1
    iget v1, p0, Lcom/facebook/ads/redexgen/X/SN;->A06:I

    goto/16 :goto_1

    .line 51058
    :cond_2
    iget v3, p0, Lcom/facebook/ads/redexgen/X/SN;->A06:I

    goto/16 :goto_0
.end method

.method public final AFr()V
    .locals 1

    .line 51059
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/SN;->A0A:Lcom/facebook/ads/redexgen/X/B4;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/TS;->A13()V

    .line 51060
    return-void
.end method
