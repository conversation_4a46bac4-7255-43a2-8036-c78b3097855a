.class public final Lcom/facebook/ads/redexgen/X/Sk;
.super Lcom/facebook/ads/redexgen/X/Ln;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/facebook/ads/redexgen/X/Sj;->A06()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field public final synthetic A00:Lcom/facebook/ads/redexgen/X/Sj;


# direct methods
.method public constructor <init>(Lcom/facebook/ads/redexgen/X/Sj;)V
    .locals 0

    .line 51599
    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/Sk;->A00:Lcom/facebook/ads/redexgen/X/Sj;

    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/Ln;-><init>()V

    return-void
.end method


# virtual methods
.method public final onAnimationEnd(Landroid/view/animation/Animation;)V
    .locals 1

    .line 51600
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Sk;->A00:Lcom/facebook/ads/redexgen/X/Sj;

    iget-object v0, v0, Lcom/facebook/ads/redexgen/X/Sj;->A01:Lcom/facebook/ads/redexgen/X/Pg;

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/Lo;->A0H(Landroid/view/View;)V

    .line 51601
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Sk;->A00:Lcom/facebook/ads/redexgen/X/Sj;

    iget-object v0, v0, Lcom/facebook/ads/redexgen/X/Sj;->A00:Lcom/facebook/ads/redexgen/X/Pf;

    invoke-interface {v0}, Lcom/facebook/ads/redexgen/X/Pf;->ABu()V

    .line 51602
    return-void
.end method
