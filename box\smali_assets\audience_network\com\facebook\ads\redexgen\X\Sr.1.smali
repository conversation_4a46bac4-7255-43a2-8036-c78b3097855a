.class public final Lcom/facebook/ads/redexgen/X/Sr;
.super Ljava/lang/Object;
.source ""

# interfaces
.implements Lcom/facebook/ads/redexgen/X/5T;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/facebook/ads/redexgen/X/So;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field public final synthetic A00:Lcom/facebook/ads/redexgen/X/So;


# direct methods
.method public constructor <init>(Lcom/facebook/ads/redexgen/X/So;)V
    .locals 0

    .line 51928
    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/Sr;->A00:Lcom/facebook/ads/redexgen/X/So;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final A8u()Z
    .locals 1

    .line 51929
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Sr;->A00:Lcom/facebook/ads/redexgen/X/So;

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/So;->A0L(Lcom/facebook/ads/redexgen/X/So;)V

    .line 51930
    const/4 v0, 0x1

    return v0
.end method
