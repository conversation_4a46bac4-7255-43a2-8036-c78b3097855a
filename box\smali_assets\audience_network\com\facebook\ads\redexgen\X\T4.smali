.class public final Lcom/facebook/ads/redexgen/X/T4;
.super Lcom/facebook/ads/redexgen/X/PQ;
.source ""


# static fields
.field public static A0W:[B

.field public static A0X:[Ljava/lang/String;

.field public static final A0Y:I

.field public static final A0Z:I

.field public static final A0a:I

.field public static final A0b:I

.field public static final A0c:I

.field public static final A0d:I

.field public static final A0e:I

.field public static final A0f:I

.field public static final A0g:I

.field public static final A0h:I


# instance fields
.field public A00:Lcom/facebook/ads/redexgen/X/1P;

.field public A01:Lcom/facebook/ads/redexgen/X/L9;

.field public A02:Lcom/facebook/ads/redexgen/X/QM;

.field public A03:Lcom/facebook/ads/redexgen/X/KI;

.field public A04:Z

.field public A05:Z

.field public A06:Z

.field public A07:Z

.field public final A08:I

.field public final A09:I

.field public final A0A:I

.field public final A0B:I

.field public final A0C:Lcom/facebook/ads/redexgen/X/Yn;

.field public final A0D:Lcom/facebook/ads/redexgen/X/No;

.field public final A0E:Lcom/facebook/ads/redexgen/X/Tl;

.field public final A0F:Lcom/facebook/ads/redexgen/X/Pd;

.field public final A0G:Lcom/facebook/ads/redexgen/X/Q5;

.field public final A0H:Lcom/facebook/ads/redexgen/X/SF;

.field public final A0I:Lcom/facebook/ads/redexgen/X/SA;

.field public final A0J:Lcom/facebook/ads/redexgen/X/9I;

.field public final A0K:Lcom/facebook/ads/redexgen/X/Pu;

.field public final A0L:Lcom/facebook/ads/redexgen/X/Ps;

.field public final A0M:Lcom/facebook/ads/redexgen/X/On;

.field public final A0N:Lcom/facebook/ads/redexgen/X/O7;

.field public final A0O:Lcom/facebook/ads/redexgen/X/NX;

.field public final A0P:Lcom/facebook/ads/redexgen/X/NQ;

.field public final A0Q:Lcom/facebook/ads/redexgen/X/LX;

.field public final A0R:Lcom/facebook/ads/redexgen/X/Kw;

.field public final A0S:Lcom/facebook/ads/redexgen/X/KP;

.field public final A0T:Lcom/facebook/ads/redexgen/X/KA;

.field public final A0U:Z

.field public final A0V:Z


# direct methods
.method public static constructor <clinit>()V
    .locals 3

    .line 2353
    const/16 v0, 0x8

    new-array v2, v0, [Ljava/lang/String;

    const/4 v1, 0x0

    const-string v0, "zF"

    aput-object v0, v2, v1

    const/4 v1, 0x1

    const-string v0, "YuhgBsbCJ4GENG72FYuTSQ3UxVCmrs0t"

    aput-object v0, v2, v1

    const/4 v1, 0x2

    const-string v0, "RSfzcHToFNO8jc1tX2m0S0AZ0vbf9kKl"

    aput-object v0, v2, v1

    const/4 v1, 0x3

    const-string v0, "p"

    aput-object v0, v2, v1

    const/4 v1, 0x4

    const-string v0, "lGjhRdALEILVIUsiuWCm6MXJK2R8L6Co"

    aput-object v0, v2, v1

    const/4 v1, 0x5

    const-string v0, "wXQvDYR5huLQKlTsBS5mG0rN7ygrQvR2"

    aput-object v0, v2, v1

    const/4 v1, 0x6

    const-string v0, "AYgBJnzxyMi9rrwHO3oB8yOhTSWVLxNu"

    aput-object v0, v2, v1

    const/4 v1, 0x7

    const-string v0, "********************************"

    aput-object v0, v2, v1

    sput-object v2, Lcom/facebook/ads/redexgen/X/T4;->A0X:[Ljava/lang/String;

    invoke-static {}, Lcom/facebook/ads/redexgen/X/T4;->A0D()V

    const/high16 v1, 0x42400000    # 48.0f

    sget v0, Lcom/facebook/ads/redexgen/X/LD;->A02:F

    mul-float/2addr v0, v1

    float-to-int v0, v0

    sput v0, Lcom/facebook/ads/redexgen/X/T4;->A0b:I

    .line 2354
    const/4 v1, -0x1

    const/16 v0, 0x4d

    invoke-static {v1, v0}, Lcom/facebook/ads/redexgen/X/2p;->A01(II)I

    move-result v2

    sput v2, Lcom/facebook/ads/redexgen/X/T4;->A0Z:I

    .line 2355
    const/high16 v1, 0x41d00000    # 26.0f

    sget v0, Lcom/facebook/ads/redexgen/X/LD;->A02:F

    mul-float/2addr v0, v1

    float-to-int v0, v0

    sput v0, Lcom/facebook/ads/redexgen/X/T4;->A0g:I

    .line 2356
    sget v0, Lcom/facebook/ads/redexgen/X/LD;->A02:F

    const/high16 v1, 0x41400000    # 12.0f

    mul-float/2addr v0, v1

    float-to-int v0, v0

    sput v0, Lcom/facebook/ads/redexgen/X/T4;->A0h:I

    .line 2357
    sget v0, Lcom/facebook/ads/redexgen/X/LD;->A02:F

    mul-float/2addr v0, v1

    float-to-int v0, v0

    sput v0, Lcom/facebook/ads/redexgen/X/T4;->A0d:I

    .line 2358
    const/high16 v1, 0x42300000    # 44.0f

    sget v0, Lcom/facebook/ads/redexgen/X/LD;->A02:F

    mul-float/2addr v0, v1

    float-to-int v0, v0

    sput v0, Lcom/facebook/ads/redexgen/X/T4;->A0e:I

    .line 2359
    const/high16 v1, 0x41000000    # 8.0f

    sget v0, Lcom/facebook/ads/redexgen/X/LD;->A02:F

    mul-float/2addr v0, v1

    float-to-int v0, v0

    sput v0, Lcom/facebook/ads/redexgen/X/T4;->A0c:I

    .line 2360
    const/high16 v1, 0x41800000    # 16.0f

    sget v0, Lcom/facebook/ads/redexgen/X/LD;->A02:F

    mul-float/2addr v0, v1

    float-to-int v0, v0

    sput v0, Lcom/facebook/ads/redexgen/X/T4;->A0f:I

    .line 2361
    const/16 v0, 0x5a

    invoke-static {v2, v0}, Lcom/facebook/ads/redexgen/X/2p;->A01(II)I

    move-result v0

    sput v0, Lcom/facebook/ads/redexgen/X/T4;->A0a:I

    .line 2362
    const/high16 v1, 0x40800000    # 4.0f

    sget v0, Lcom/facebook/ads/redexgen/X/LD;->A02:F

    mul-float/2addr v0, v1

    float-to-int v0, v0

    sput v0, Lcom/facebook/ads/redexgen/X/T4;->A0Y:I

    return-void
.end method

.method public constructor <init>(Lcom/facebook/ads/redexgen/X/Yn;Lcom/facebook/ads/redexgen/X/J2;Lcom/facebook/ads/redexgen/X/b5;Lcom/facebook/ads/redexgen/X/6c;Lcom/facebook/ads/redexgen/X/Mj;ILcom/facebook/ads/redexgen/X/MC;Lcom/facebook/ads/redexgen/X/JA;IIIZIZZZLcom/facebook/ads/redexgen/X/Pd;)V
    .locals 18

    .line 52489
    move-object/from16 v3, p0

    move-object/from16 v9, p0

    move-object/from16 v5, p1

    move-object v10, v5

    move-object/from16 v4, p2

    move-object v12, v4

    move/from16 v16, p16

    move-object/from16 v13, p3

    move/from16 v15, p15

    move-object/from16 v11, p5

    move/from16 v14, p6

    move-object/from16 v17, p7

    invoke-direct/range {v9 .. v17}, Lcom/facebook/ads/redexgen/X/PQ;-><init>(Lcom/facebook/ads/redexgen/X/Yn;Lcom/facebook/ads/redexgen/X/Mj;Lcom/facebook/ads/redexgen/X/J2;Lcom/facebook/ads/redexgen/X/b5;IZZLcom/facebook/ads/redexgen/X/MC;)V

    .line 52490
    const/4 v0, 0x0

    iput-boolean v0, v3, Lcom/facebook/ads/redexgen/X/T4;->A04:Z

    .line 52491
    iput-boolean v0, v3, Lcom/facebook/ads/redexgen/X/T4;->A05:Z

    .line 52492
    iput-boolean v0, v3, Lcom/facebook/ads/redexgen/X/T4;->A07:Z

    .line 52493
    new-instance v12, Lcom/facebook/ads/redexgen/X/AV;

    invoke-direct {v12, v3}, Lcom/facebook/ads/redexgen/X/AV;-><init>(Lcom/facebook/ads/redexgen/X/T4;)V

    iput-object v12, v3, Lcom/facebook/ads/redexgen/X/T4;->A0O:Lcom/facebook/ads/redexgen/X/NX;

    .line 52494
    new-instance v11, Lcom/facebook/ads/redexgen/X/AO;

    invoke-direct {v11, v3}, Lcom/facebook/ads/redexgen/X/AO;-><init>(Lcom/facebook/ads/redexgen/X/T4;)V

    iput-object v11, v3, Lcom/facebook/ads/redexgen/X/T4;->A0N:Lcom/facebook/ads/redexgen/X/O7;

    .line 52495
    new-instance v10, Lcom/facebook/ads/redexgen/X/A9;

    invoke-direct {v10, v3}, Lcom/facebook/ads/redexgen/X/A9;-><init>(Lcom/facebook/ads/redexgen/X/T4;)V

    iput-object v10, v3, Lcom/facebook/ads/redexgen/X/T4;->A0M:Lcom/facebook/ads/redexgen/X/On;

    .line 52496
    new-instance v9, Lcom/facebook/ads/redexgen/X/A4;

    invoke-direct {v9, v3}, Lcom/facebook/ads/redexgen/X/A4;-><init>(Lcom/facebook/ads/redexgen/X/T4;)V

    iput-object v9, v3, Lcom/facebook/ads/redexgen/X/T4;->A0P:Lcom/facebook/ads/redexgen/X/NQ;

    .line 52497
    new-instance v8, Lcom/facebook/ads/redexgen/X/A3;

    invoke-direct {v8, v3}, Lcom/facebook/ads/redexgen/X/A3;-><init>(Lcom/facebook/ads/redexgen/X/T4;)V

    iput-object v8, v3, Lcom/facebook/ads/redexgen/X/T4;->A0K:Lcom/facebook/ads/redexgen/X/Pu;

    .line 52498
    new-instance v6, Lcom/facebook/ads/redexgen/X/A2;

    invoke-direct {v6, v3}, Lcom/facebook/ads/redexgen/X/A2;-><init>(Lcom/facebook/ads/redexgen/X/T4;)V

    iput-object v6, v3, Lcom/facebook/ads/redexgen/X/T4;->A0L:Lcom/facebook/ads/redexgen/X/Ps;

    .line 52499
    iput-object v5, v3, Lcom/facebook/ads/redexgen/X/T4;->A0C:Lcom/facebook/ads/redexgen/X/Yn;

    .line 52500
    new-instance v0, Lcom/facebook/ads/redexgen/X/Kw;

    move-object/from16 v1, p8

    invoke-direct {v0, v5, v1}, Lcom/facebook/ads/redexgen/X/Kw;-><init>(Lcom/facebook/ads/redexgen/X/Yn;Lcom/facebook/ads/redexgen/X/JA;)V

    iput-object v0, v3, Lcom/facebook/ads/redexgen/X/T4;->A0R:Lcom/facebook/ads/redexgen/X/Kw;

    .line 52501
    new-instance v0, Lcom/facebook/ads/redexgen/X/KP;

    invoke-direct {v0, v5}, Lcom/facebook/ads/redexgen/X/KP;-><init>(Lcom/facebook/ads/redexgen/X/Yn;)V

    iput-object v0, v3, Lcom/facebook/ads/redexgen/X/T4;->A0S:Lcom/facebook/ads/redexgen/X/KP;

    .line 52502
    move/from16 v0, p13

    iput v0, v3, Lcom/facebook/ads/redexgen/X/T4;->A0A:I

    .line 52503
    move/from16 v0, p14

    iput-boolean v0, v3, Lcom/facebook/ads/redexgen/X/T4;->A0V:Z

    .line 52504
    move/from16 v0, p9

    iput v0, v3, Lcom/facebook/ads/redexgen/X/T4;->A09:I

    .line 52505
    move/from16 v2, p10

    iput v2, v3, Lcom/facebook/ads/redexgen/X/T4;->A0B:I

    .line 52506
    move-object/from16 v0, p17

    iput-object v0, v3, Lcom/facebook/ads/redexgen/X/T4;->A0F:Lcom/facebook/ads/redexgen/X/Pd;

    .line 52507
    move/from16 v0, p11

    iput v0, v3, Lcom/facebook/ads/redexgen/X/T4;->A08:I

    .line 52508
    move/from16 v0, p12

    iput-boolean v0, v3, Lcom/facebook/ads/redexgen/X/T4;->A0U:Z

    .line 52509
    iget-object v1, v3, Lcom/facebook/ads/redexgen/X/PQ;->A05:Lcom/facebook/ads/redexgen/X/b5;

    new-instance v0, Lcom/facebook/ads/redexgen/X/Q5;

    invoke-direct {v0, v5, v4, v1}, Lcom/facebook/ads/redexgen/X/Q5;-><init>(Lcom/facebook/ads/redexgen/X/Yn;Lcom/facebook/ads/redexgen/X/J2;Lcom/facebook/ads/redexgen/X/b5;)V

    iput-object v0, v3, Lcom/facebook/ads/redexgen/X/T4;->A0G:Lcom/facebook/ads/redexgen/X/Q5;

    .line 52510
    const/4 v0, 0x1

    if-ne v2, v0, :cond_2

    .line 52511
    iget-object v0, v3, Lcom/facebook/ads/redexgen/X/PQ;->A05:Lcom/facebook/ads/redexgen/X/b5;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/b5;->A0w()Lcom/facebook/ads/redexgen/X/1C;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/1C;->A01()Lcom/facebook/ads/redexgen/X/1P;

    move-result-object v0

    .line 52512
    :goto_0
    iput-object v0, v3, Lcom/facebook/ads/redexgen/X/T4;->A00:Lcom/facebook/ads/redexgen/X/1P;

    .line 52513
    new-instance v7, Lcom/facebook/ads/redexgen/X/SA;

    invoke-direct {v7, v5}, Lcom/facebook/ads/redexgen/X/SA;-><init>(Lcom/facebook/ads/redexgen/X/Yn;)V

    iput-object v7, v3, Lcom/facebook/ads/redexgen/X/T4;->A0I:Lcom/facebook/ads/redexgen/X/SA;

    .line 52514
    invoke-virtual {v7}, Lcom/facebook/ads/redexgen/X/SA;->getEventBus()Lcom/facebook/ads/redexgen/X/8r;

    move-result-object v2

    const/4 v0, 0x6

    new-array v1, v0, [Lcom/facebook/ads/redexgen/X/8s;

    const/4 v0, 0x0

    aput-object v12, v1, v0

    const/4 v0, 0x1

    aput-object v11, v1, v0

    const/4 v0, 0x2

    aput-object v10, v1, v0

    const/4 v0, 0x3

    aput-object v9, v1, v0

    const/4 v0, 0x4

    aput-object v8, v1, v0

    const/4 v0, 0x5

    aput-object v6, v1, v0

    .line 52515
    invoke-virtual {v2, v1}, Lcom/facebook/ads/redexgen/X/8r;->A03([Lcom/facebook/ads/redexgen/X/8s;)V

    .line 52516
    invoke-virtual {v13}, Lcom/facebook/ads/redexgen/X/b5;->A12()Ljava/lang/String;

    move-result-object v0

    new-instance v6, Lcom/facebook/ads/redexgen/X/9I;

    invoke-direct {v6, v5, v4, v7, v0}, Lcom/facebook/ads/redexgen/X/9I;-><init>(Lcom/facebook/ads/redexgen/X/Yn;Lcom/facebook/ads/redexgen/X/J2;Lcom/facebook/ads/redexgen/X/SA;Ljava/lang/String;)V

    iput-object v6, v3, Lcom/facebook/ads/redexgen/X/T4;->A0J:Lcom/facebook/ads/redexgen/X/9I;

    .line 52517
    invoke-direct/range {p0 .. p0}, Lcom/facebook/ads/redexgen/X/T4;->A0C()V

    .line 52518
    iget-object v0, v3, Lcom/facebook/ads/redexgen/X/PQ;->A05:Lcom/facebook/ads/redexgen/X/b5;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/b5;->A0x()Lcom/facebook/ads/redexgen/X/1G;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/1G;->A0D()Lcom/facebook/ads/redexgen/X/1J;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/1J;->A08()Ljava/lang/String;

    move-result-object v0

    .line 52519
    .local v8, "videoUrl":Ljava/lang/String;
    move-object/from16 v1, p4

    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/6c;->A0S(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v7, v0}, Lcom/facebook/ads/redexgen/X/SA;->setVideoURI(Ljava/lang/String;)V

    .line 52520
    invoke-direct/range {p0 .. p0}, Lcom/facebook/ads/redexgen/X/T4;->A0B()V

    .line 52521
    invoke-direct/range {p0 .. p0}, Lcom/facebook/ads/redexgen/X/T4;->A05()Lcom/facebook/ads/redexgen/X/Th;

    move-result-object v9

    iput-object v9, v3, Lcom/facebook/ads/redexgen/X/T4;->A0D:Lcom/facebook/ads/redexgen/X/No;

    .line 52522
    const/16 v8, 0x190

    const/16 v1, 0x64

    const/4 v2, 0x0

    new-instance v0, Lcom/facebook/ads/redexgen/X/KA;

    invoke-direct {v0, v9, v8, v1, v2}, Lcom/facebook/ads/redexgen/X/KA;-><init>(Landroid/view/View;III)V

    iput-object v0, v3, Lcom/facebook/ads/redexgen/X/T4;->A0T:Lcom/facebook/ads/redexgen/X/KA;

    .line 52523
    const/4 v1, 0x1

    invoke-virtual {v0, v1, v2}, Lcom/facebook/ads/redexgen/X/KA;->A3Y(ZZ)V

    .line 52524
    new-instance v0, Lcom/facebook/ads/redexgen/X/LX;

    invoke-direct {v0, v1}, Lcom/facebook/ads/redexgen/X/LX;-><init>(Z)V

    iput-object v0, v3, Lcom/facebook/ads/redexgen/X/T4;->A0Q:Lcom/facebook/ads/redexgen/X/LX;

    .line 52525
    invoke-direct/range {p0 .. p0}, Lcom/facebook/ads/redexgen/X/T4;->A0E()V

    .line 52526
    invoke-virtual {v9}, Lcom/facebook/ads/redexgen/X/No;->getCTAButton()Lcom/facebook/ads/redexgen/X/Tl;

    move-result-object v1

    iput-object v1, v3, Lcom/facebook/ads/redexgen/X/T4;->A0E:Lcom/facebook/ads/redexgen/X/Tl;

    .line 52527
    const/16 v0, 0x3e9

    invoke-static {v0, v1}, Lcom/facebook/ads/redexgen/X/Lo;->A0G(ILandroid/view/View;)V

    .line 52528
    invoke-direct/range {p0 .. p0}, Lcom/facebook/ads/redexgen/X/T4;->A0A()V

    .line 52529
    invoke-direct/range {p0 .. p0}, Lcom/facebook/ads/redexgen/X/T4;->A09()V

    .line 52530
    invoke-virtual {v9}, Lcom/facebook/ads/redexgen/X/No;->bringToFront()V

    .line 52531
    invoke-static {v5}, Lcom/facebook/ads/redexgen/X/Ih;->A1W(Landroid/content/Context;)Z

    move-result v0

    if-eqz v0, :cond_0

    .line 52532
    invoke-virtual {v5}, Lcom/facebook/ads/redexgen/X/7f;->A0A()Lcom/facebook/ads/redexgen/X/JE;

    move-result-object v2

    iget-object v0, v3, Lcom/facebook/ads/redexgen/X/PQ;->A05:Lcom/facebook/ads/redexgen/X/b5;

    .line 52533
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/b5;->A12()Ljava/lang/String;

    move-result-object v1

    const/4 v0, 0x1

    invoke-interface {v2, v7, v1, v0}, Lcom/facebook/ads/redexgen/X/JE;->AGv(Landroid/view/View;Ljava/lang/String;Z)V

    .line 52534
    :cond_0
    invoke-static {v5}, Lcom/facebook/ads/redexgen/X/Ih;->A1X(Landroid/content/Context;)Z

    move-result v0

    if-eqz v0, :cond_1

    .line 52535
    new-instance v8, Lcom/facebook/ads/redexgen/X/SF;

    iget-object v0, v3, Lcom/facebook/ads/redexgen/X/PQ;->A05:Lcom/facebook/ads/redexgen/X/b5;

    .line 52536
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/b5;->A12()Ljava/lang/String;

    move-result-object v12

    const/4 v14, 0x0

    move-object v0, v8

    move-object v10, v4

    move-object v11, v7

    move-object v13, v6

    move-object v9, v5

    invoke-direct/range {v8 .. v14}, Lcom/facebook/ads/redexgen/X/SF;-><init>(Lcom/facebook/ads/redexgen/X/Yn;Lcom/facebook/ads/redexgen/X/J2;Lcom/facebook/ads/redexgen/X/SA;Ljava/lang/String;Lcom/facebook/ads/redexgen/X/QS;Ljava/util/Map;)V

    iput-object v0, v3, Lcom/facebook/ads/redexgen/X/T4;->A0H:Lcom/facebook/ads/redexgen/X/SF;

    .line 52537
    :goto_1
    return-void

    .line 52538
    :cond_1
    const/4 v0, 0x0

    iput-object v0, v3, Lcom/facebook/ads/redexgen/X/T4;->A0H:Lcom/facebook/ads/redexgen/X/SF;

    goto :goto_1

    .line 52539
    :cond_2
    iget-object v0, v3, Lcom/facebook/ads/redexgen/X/PQ;->A05:Lcom/facebook/ads/redexgen/X/b5;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/b5;->A0w()Lcom/facebook/ads/redexgen/X/1C;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/1C;->A00()Lcom/facebook/ads/redexgen/X/1P;

    move-result-object v0

    goto/16 :goto_0
.end method

.method public static synthetic A03(Lcom/facebook/ads/redexgen/X/T4;)I
    .locals 0

    .line 52540
    iget p0, p0, Lcom/facebook/ads/redexgen/X/T4;->A0A:I

    return p0
.end method

.method public static synthetic A04(Lcom/facebook/ads/redexgen/X/T4;)I
    .locals 0

    .line 52541
    iget p0, p0, Lcom/facebook/ads/redexgen/X/T4;->A09:I

    return p0
.end method

.method private A05()Lcom/facebook/ads/redexgen/X/Th;
    .locals 16

    .line 52542
    move-object/from16 v0, p0

    const/4 v2, -0x1

    const/4 v1, -0x2

    new-instance v3, Landroid/widget/RelativeLayout$LayoutParams;

    invoke-direct {v3, v2, v1}, Landroid/widget/RelativeLayout$LayoutParams;-><init>(II)V

    .line 52543
    .local v1, "adDetailsParams":Landroid/widget/RelativeLayout$LayoutParams;
    const/16 v1, 0xc

    invoke-virtual {v3, v1}, Landroid/widget/RelativeLayout$LayoutParams;->addRule(I)V

    .line 52544
    iget-object v1, v0, Lcom/facebook/ads/redexgen/X/PQ;->A05:Lcom/facebook/ads/redexgen/X/b5;

    invoke-virtual {v1}, Lcom/facebook/ads/redexgen/X/1F;->A0P()Ljava/lang/String;

    move-result-object v5

    const/16 v4, 0x25

    const/16 v2, 0xe

    const/16 v1, 0x7d

    invoke-static {v4, v2, v1}, Lcom/facebook/ads/redexgen/X/T4;->A08(III)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v5, v1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_1

    .line 52545
    sget-object v1, Lcom/facebook/ads/redexgen/X/QP;->A04:Lcom/facebook/ads/redexgen/X/QP;

    invoke-virtual {v1}, Lcom/facebook/ads/redexgen/X/QP;->A02()Ljava/lang/String;

    move-result-object v10

    .line 52546
    .local v9, "clickEvent":Ljava/lang/String;
    :goto_0
    new-instance v4, Lcom/facebook/ads/redexgen/X/Th;

    iget-object v5, v0, Lcom/facebook/ads/redexgen/X/T4;->A0C:Lcom/facebook/ads/redexgen/X/Yn;

    sget v6, Lcom/facebook/ads/redexgen/X/T4;->A0b:I

    iget-object v1, v0, Lcom/facebook/ads/redexgen/X/PQ;->A05:Lcom/facebook/ads/redexgen/X/b5;

    .line 52547
    invoke-virtual {v1}, Lcom/facebook/ads/redexgen/X/b5;->A0x()Lcom/facebook/ads/redexgen/X/1G;

    move-result-object v1

    invoke-virtual {v1}, Lcom/facebook/ads/redexgen/X/1G;->A0E()Lcom/facebook/ads/redexgen/X/1N;

    move-result-object v1

    invoke-virtual {v1}, Lcom/facebook/ads/redexgen/X/1N;->A00()Lcom/facebook/ads/redexgen/X/1L;

    move-result-object v2

    sget-object v1, Lcom/facebook/ads/redexgen/X/1L;->A05:Lcom/facebook/ads/redexgen/X/1L;

    if-ne v2, v1, :cond_0

    const/4 v7, 0x1

    .line 52548
    :goto_1
    invoke-virtual/range {p0 .. p0}, Lcom/facebook/ads/redexgen/X/T4;->getColors()Lcom/facebook/ads/redexgen/X/1P;

    move-result-object v8

    iget-object v1, v0, Lcom/facebook/ads/redexgen/X/PQ;->A05:Lcom/facebook/ads/redexgen/X/b5;

    .line 52549
    invoke-virtual {v1}, Lcom/facebook/ads/redexgen/X/b5;->A0x()Lcom/facebook/ads/redexgen/X/1G;

    move-result-object v1

    invoke-virtual {v1}, Lcom/facebook/ads/redexgen/X/1G;->A0F()Lcom/facebook/ads/redexgen/X/1Q;

    move-result-object v1

    invoke-virtual {v1}, Lcom/facebook/ads/redexgen/X/1Q;->A06()Z

    move-result v9

    iget-object v11, v0, Lcom/facebook/ads/redexgen/X/PQ;->A07:Lcom/facebook/ads/redexgen/X/J2;

    iget-object v12, v0, Lcom/facebook/ads/redexgen/X/PQ;->A09:Lcom/facebook/ads/redexgen/X/MC;

    iget-object v13, v0, Lcom/facebook/ads/redexgen/X/PQ;->A0B:Lcom/facebook/ads/redexgen/X/RE;

    iget-object v14, v0, Lcom/facebook/ads/redexgen/X/PQ;->A08:Lcom/facebook/ads/redexgen/X/Lg;

    iget-object v1, v0, Lcom/facebook/ads/redexgen/X/PQ;->A05:Lcom/facebook/ads/redexgen/X/b5;

    .line 52550
    invoke-virtual {v1}, Lcom/facebook/ads/redexgen/X/b5;->A0y()Lcom/facebook/ads/redexgen/X/1U;

    move-result-object v15

    invoke-direct/range {v4 .. v15}, Lcom/facebook/ads/redexgen/X/Th;-><init>(Lcom/facebook/ads/redexgen/X/Yn;IZLcom/facebook/ads/redexgen/X/1P;ZLjava/lang/String;Lcom/facebook/ads/redexgen/X/J2;Lcom/facebook/ads/redexgen/X/MC;Lcom/facebook/ads/redexgen/X/RE;Lcom/facebook/ads/redexgen/X/Lg;Lcom/facebook/ads/redexgen/X/1U;)V

    .line 52551
    .local v2, "detailsView":Lcom/facebook/ads/redexgen/X/No;
    const/16 v1, 0x3f0

    invoke-static {v1, v4}, Lcom/facebook/ads/redexgen/X/Lo;->A0G(ILandroid/view/View;)V

    .line 52552
    iget v1, v0, Lcom/facebook/ads/redexgen/X/T4;->A0B:I

    invoke-virtual {v4, v1}, Lcom/facebook/ads/redexgen/X/Th;->A0A(I)V

    .line 52553
    invoke-virtual {v0, v4, v3}, Lcom/facebook/ads/redexgen/X/T4;->addView(Landroid/view/View;Landroid/view/ViewGroup$LayoutParams;)V

    .line 52554
    iget-object v1, v0, Lcom/facebook/ads/redexgen/X/PQ;->A05:Lcom/facebook/ads/redexgen/X/b5;

    .line 52555
    invoke-virtual {v1}, Lcom/facebook/ads/redexgen/X/b5;->A0x()Lcom/facebook/ads/redexgen/X/1G;

    move-result-object v1

    invoke-virtual {v1}, Lcom/facebook/ads/redexgen/X/1G;->A0E()Lcom/facebook/ads/redexgen/X/1N;

    move-result-object v5

    iget-object v1, v0, Lcom/facebook/ads/redexgen/X/PQ;->A05:Lcom/facebook/ads/redexgen/X/b5;

    .line 52556
    invoke-virtual {v1}, Lcom/facebook/ads/redexgen/X/b5;->A0x()Lcom/facebook/ads/redexgen/X/1G;

    move-result-object v1

    invoke-virtual {v1}, Lcom/facebook/ads/redexgen/X/1G;->A0F()Lcom/facebook/ads/redexgen/X/1Q;

    move-result-object v6

    iget-object v1, v0, Lcom/facebook/ads/redexgen/X/PQ;->A05:Lcom/facebook/ads/redexgen/X/b5;

    .line 52557
    invoke-virtual {v1}, Lcom/facebook/ads/redexgen/X/b5;->A12()Ljava/lang/String;

    move-result-object v7

    iget-object v0, v0, Lcom/facebook/ads/redexgen/X/PQ;->A05:Lcom/facebook/ads/redexgen/X/b5;

    .line 52558
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/b5;->A10()Lcom/facebook/ads/redexgen/X/1a;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/1a;->A01()Ljava/lang/String;

    move-result-object v8

    const/4 v9, 0x0

    .line 52559
    invoke-virtual/range {v4 .. v9}, Lcom/facebook/ads/redexgen/X/Th;->setInfo(Lcom/facebook/ads/redexgen/X/1N;Lcom/facebook/ads/redexgen/X/1Q;Ljava/lang/String;Ljava/lang/String;Lcom/facebook/ads/redexgen/X/Nq;)V

    .line 52560
    return-object v4

    .line 52561
    :cond_0
    const/4 v7, 0x0

    goto :goto_1

    .line 52562
    :cond_1
    const/4 v4, 0x0

    const/16 v2, 0x25

    const/16 v1, 0x3a

    invoke-static {v4, v2, v1}, Lcom/facebook/ads/redexgen/X/T4;->A08(III)Ljava/lang/String;

    move-result-object v10

    goto :goto_0
.end method

.method public static synthetic A06(Lcom/facebook/ads/redexgen/X/T4;)Lcom/facebook/ads/redexgen/X/Pd;
    .locals 0

    .line 52563
    iget-object p0, p0, Lcom/facebook/ads/redexgen/X/T4;->A0F:Lcom/facebook/ads/redexgen/X/Pd;

    return-object p0
.end method

.method public static synthetic A07(Lcom/facebook/ads/redexgen/X/T4;)Lcom/facebook/ads/redexgen/X/SA;
    .locals 0

    .line 52564
    iget-object p0, p0, Lcom/facebook/ads/redexgen/X/T4;->A0I:Lcom/facebook/ads/redexgen/X/SA;

    return-object p0
.end method

.method public static A08(III)Ljava/lang/String;
    .locals 2

    sget-object v1, Lcom/facebook/ads/redexgen/X/T4;->A0W:[B

    add-int v0, p0, p1

    invoke-static {v1, p0, v0}, Ljava/util/Arrays;->copyOfRange([BII)[B

    move-result-object p0

    const/4 v1, 0x0

    :goto_0
    array-length v0, p0

    if-ge v1, v0, :cond_0

    aget-byte v0, p0, v1

    sub-int/2addr v0, p2

    add-int/lit8 v0, v0, -0x2c

    int-to-byte v0, v0

    aput-byte v0, p0, v1

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_0
    new-instance v0, Ljava/lang/String;

    invoke-direct {v0, p0}, Ljava/lang/String;-><init>([B)V

    return-object v0
.end method

.method private A09()V
    .locals 3

    .line 52565
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/T4;->A0S:Lcom/facebook/ads/redexgen/X/KP;

    sget v0, Lcom/facebook/ads/redexgen/X/T4;->A0Z:I

    const/4 v2, -0x1

    invoke-virtual {v1, v2, v0}, Lcom/facebook/ads/redexgen/X/KP;->A06(II)V

    .line 52566
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/T4;->A0S:Lcom/facebook/ads/redexgen/X/KP;

    sget v0, Lcom/facebook/ads/redexgen/X/T4;->A0h:I

    invoke-virtual {v1, v0, v0, v0, v0}, Lcom/facebook/ads/redexgen/X/KP;->setPadding(IIII)V

    .line 52567
    sget v0, Lcom/facebook/ads/redexgen/X/T4;->A0g:I

    new-instance v1, Landroid/widget/RelativeLayout$LayoutParams;

    invoke-direct {v1, v2, v0}, Landroid/widget/RelativeLayout$LayoutParams;-><init>(II)V

    .line 52568
    .local v0, "progressBarLayoutParams":Landroid/widget/RelativeLayout$LayoutParams;
    const/16 v0, 0xc

    invoke-virtual {v1, v0}, Landroid/widget/RelativeLayout$LayoutParams;->addRule(I)V

    .line 52569
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/T4;->A0S:Lcom/facebook/ads/redexgen/X/KP;

    invoke-virtual {p0, v0, v1}, Lcom/facebook/ads/redexgen/X/T4;->addView(Landroid/view/View;Landroid/view/ViewGroup$LayoutParams;)V

    .line 52570
    return-void
.end method

.method private A0A()V
    .locals 4

    .line 52571
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/T4;->A0R:Lcom/facebook/ads/redexgen/X/Kw;

    sget v0, Lcom/facebook/ads/redexgen/X/T4;->A0d:I

    invoke-virtual {v1, v0, v0, v0, v0}, Lcom/facebook/ads/redexgen/X/Kw;->setPadding(IIII)V

    .line 52572
    sget v0, Lcom/facebook/ads/redexgen/X/T4;->A0e:I

    new-instance v3, Landroid/widget/RelativeLayout$LayoutParams;

    invoke-direct {v3, v0, v0}, Landroid/widget/RelativeLayout$LayoutParams;-><init>(II)V

    .line 52573
    .local v0, "muteButtonParams":Landroid/widget/RelativeLayout$LayoutParams;
    sget v2, Lcom/facebook/ads/redexgen/X/MB;->A00:I

    sget v1, Lcom/facebook/ads/redexgen/X/T4;->A0c:I

    const/4 v0, 0x0

    invoke-virtual {v3, v0, v2, v1, v0}, Landroid/widget/RelativeLayout$LayoutParams;->setMargins(IIII)V

    .line 52574
    const/16 v0, 0xb

    invoke-virtual {v3, v0}, Landroid/widget/RelativeLayout$LayoutParams;->addRule(I)V

    .line 52575
    const/16 v0, 0xa

    invoke-virtual {v3, v0}, Landroid/widget/RelativeLayout$LayoutParams;->addRule(I)V

    .line 52576
    const/4 v0, -0x1

    new-instance v1, Landroid/widget/RelativeLayout$LayoutParams;

    invoke-direct {v1, v0, v0}, Landroid/widget/RelativeLayout$LayoutParams;-><init>(II)V

    .line 52577
    .local v1, "videoViewParams":Landroid/widget/RelativeLayout$LayoutParams;
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/T4;->A0I:Lcom/facebook/ads/redexgen/X/SA;

    invoke-virtual {p0, v0, v1}, Lcom/facebook/ads/redexgen/X/T4;->addView(Landroid/view/View;Landroid/view/ViewGroup$LayoutParams;)V

    .line 52578
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/T4;->A0R:Lcom/facebook/ads/redexgen/X/Kw;

    invoke-virtual {p0, v0, v3}, Lcom/facebook/ads/redexgen/X/T4;->addView(Landroid/view/View;Landroid/view/ViewGroup$LayoutParams;)V

    .line 52579
    return-void
.end method

.method private A0B()V
    .locals 3

    .line 52580
    new-instance v2, Lcom/facebook/ads/redexgen/X/T7;

    invoke-direct {v2, p0}, Lcom/facebook/ads/redexgen/X/T7;-><init>(Lcom/facebook/ads/redexgen/X/T4;)V

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/T4;->A0C:Lcom/facebook/ads/redexgen/X/Yn;

    .line 52581
    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/Ih;->A0L(Landroid/content/Context;)I

    move-result v0

    int-to-long v0, v0

    .line 52582
    invoke-virtual {p0, v2, v0, v1}, Lcom/facebook/ads/redexgen/X/T4;->postDelayed(Ljava/lang/Runnable;J)Z

    .line 52583
    return-void
.end method

.method private A0C()V
    .locals 3

    .line 52584
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/T4;->A0I:Lcom/facebook/ads/redexgen/X/SA;

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/T4;->A0S:Lcom/facebook/ads/redexgen/X/KP;

    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/SA;->A0c(Lcom/facebook/ads/redexgen/X/QN;)V

    .line 52585
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/T4;->A0I:Lcom/facebook/ads/redexgen/X/SA;

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/T4;->A0R:Lcom/facebook/ads/redexgen/X/Kw;

    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/SA;->A0c(Lcom/facebook/ads/redexgen/X/QN;)V

    .line 52586
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/PQ;->A05:Lcom/facebook/ads/redexgen/X/b5;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/b5;->A0x()Lcom/facebook/ads/redexgen/X/1G;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/1G;->A0D()Lcom/facebook/ads/redexgen/X/1J;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/1J;->A07()Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    if-nez v0, :cond_0

    .line 52587
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/T4;->A0C:Lcom/facebook/ads/redexgen/X/Yn;

    new-instance v1, Lcom/facebook/ads/redexgen/X/7R;

    invoke-direct {v1, v0}, Lcom/facebook/ads/redexgen/X/7R;-><init>(Lcom/facebook/ads/redexgen/X/Yn;)V

    .line 52588
    .local v0, "placeholderImagePlugin":Lcom/facebook/ads/redexgen/X/7R;
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/T4;->A0I:Lcom/facebook/ads/redexgen/X/SA;

    invoke-virtual {v0, v1}, Lcom/facebook/ads/redexgen/X/SA;->A0c(Lcom/facebook/ads/redexgen/X/QN;)V

    .line 52589
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/PQ;->A05:Lcom/facebook/ads/redexgen/X/b5;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/b5;->A0x()Lcom/facebook/ads/redexgen/X/1G;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/1G;->A0D()Lcom/facebook/ads/redexgen/X/1J;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/1J;->A07()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/7R;->setImage(Ljava/lang/String;)V

    .line 52590
    .end local v0    # "placeholderImagePlugin":Lcom/facebook/ads/redexgen/X/7R;
    :cond_0
    iget-object v2, p0, Lcom/facebook/ads/redexgen/X/T4;->A0I:Lcom/facebook/ads/redexgen/X/SA;

    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/T4;->A0C:Lcom/facebook/ads/redexgen/X/Yn;

    new-instance v0, Lcom/facebook/ads/redexgen/X/7H;

    invoke-direct {v0, v1}, Lcom/facebook/ads/redexgen/X/7H;-><init>(Lcom/facebook/ads/redexgen/X/Yn;)V

    invoke-virtual {v2, v0}, Lcom/facebook/ads/redexgen/X/SA;->A0c(Lcom/facebook/ads/redexgen/X/QN;)V

    .line 52591
    return-void
.end method

.method public static A0D()V
    .locals 1

    const/16 v0, 0x33

    new-array v0, v0, [B

    fill-array-data v0, :array_0

    sput-object v0, Lcom/facebook/ads/redexgen/X/T4;->A0W:[B

    return-void

    :array_0
    .array-data 1
        -0x37t
        -0x2bt
        -0x2dt
        -0x6ct
        -0x34t
        -0x39t
        -0x37t
        -0x35t
        -0x38t
        -0x2bt
        -0x2bt
        -0x2ft
        -0x6ct
        -0x39t
        -0x36t
        -0x27t
        -0x6ct
        -0x31t
        -0x2ct
        -0x26t
        -0x35t
        -0x28t
        -0x27t
        -0x26t
        -0x31t
        -0x26t
        -0x31t
        -0x39t
        -0x2et
        -0x6ct
        -0x37t
        -0x2et
        -0x31t
        -0x37t
        -0x2ft
        -0x35t
        -0x36t
        0x1bt
        0xet
        0x20t
        0xat
        0x1bt
        0xdt
        0xet
        0xdt
        0x8t
        0x1ft
        0x12t
        0xdt
        0xet
        0x18t
    .end array-data
.end method

.method private final A0E()V
    .locals 9

    .line 52592
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/T4;->A0D:Lcom/facebook/ads/redexgen/X/No;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/No;->getExpandableLayout()Landroid/view/View;

    move-result-object v5

    .line 52593
    .local v0, "expandableLayout":Landroid/view/View;
    if-eqz v5, :cond_1

    .line 52594
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/T4;->A03:Lcom/facebook/ads/redexgen/X/KI;

    if-eqz v1, :cond_0

    .line 52595
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/T4;->A0Q:Lcom/facebook/ads/redexgen/X/LX;

    invoke-virtual {v0, v1}, Lcom/facebook/ads/redexgen/X/LX;->A0I(Lcom/facebook/ads/redexgen/X/Qj;)V

    .line 52596
    :cond_0
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/PQ;->A05:Lcom/facebook/ads/redexgen/X/b5;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/b5;->A0w()Lcom/facebook/ads/redexgen/X/1C;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/1C;->A01()Lcom/facebook/ads/redexgen/X/1P;

    move-result-object v8

    .line 52597
    .local v1, "colors":Lcom/facebook/ads/redexgen/X/1P;
    iget-object v7, p0, Lcom/facebook/ads/redexgen/X/T4;->A0Q:Lcom/facebook/ads/redexgen/X/LX;

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/T4;->A0D:Lcom/facebook/ads/redexgen/X/No;

    .line 52598
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/No;->getCTAButton()Lcom/facebook/ads/redexgen/X/Tl;

    move-result-object v4

    .line 52599
    const/4 v3, 0x1

    invoke-virtual {v8, v3}, Lcom/facebook/ads/redexgen/X/1P;->A09(Z)I

    move-result v2

    const/16 v6, 0x12c

    const/4 v1, -0x1

    new-instance v0, Lcom/facebook/ads/redexgen/X/KC;

    invoke-direct {v0, v4, v6, v1, v2}, Lcom/facebook/ads/redexgen/X/KC;-><init>(Landroid/view/View;III)V

    .line 52600
    invoke-virtual {v7, v0}, Lcom/facebook/ads/redexgen/X/LX;->A0I(Lcom/facebook/ads/redexgen/X/Qj;)V

    .line 52601
    sget v2, Lcom/facebook/ads/redexgen/X/T4;->A0Z:I

    sget v0, Lcom/facebook/ads/redexgen/X/T4;->A0a:I

    sget v1, Lcom/facebook/ads/redexgen/X/T4;->A0Y:I

    .line 52602
    invoke-static {v2, v0, v1}, Lcom/facebook/ads/redexgen/X/Lo;->A08(III)Landroid/graphics/drawable/Drawable;

    move-result-object v4

    .line 52603
    .local v2, "startDrawable":Landroid/graphics/drawable/Drawable;
    invoke-virtual {v8, v3}, Lcom/facebook/ads/redexgen/X/1P;->A08(Z)I

    move-result v0

    invoke-static {v0, v1}, Lcom/facebook/ads/redexgen/X/Lo;->A05(II)Landroid/graphics/drawable/Drawable;

    move-result-object v3

    .line 52604
    .local v3, "endDrawable":Landroid/graphics/drawable/Drawable;
    iget-object v2, p0, Lcom/facebook/ads/redexgen/X/T4;->A0Q:Lcom/facebook/ads/redexgen/X/LX;

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/T4;->A0D:Lcom/facebook/ads/redexgen/X/No;

    .line 52605
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/No;->getCTAButton()Lcom/facebook/ads/redexgen/X/Tl;

    move-result-object v1

    new-instance v0, Lcom/facebook/ads/redexgen/X/KM;

    invoke-direct {v0, v1, v6, v4, v3}, Lcom/facebook/ads/redexgen/X/KM;-><init>(Landroid/view/View;ILandroid/graphics/drawable/Drawable;Landroid/graphics/drawable/Drawable;)V

    .line 52606
    invoke-virtual {v2, v0}, Lcom/facebook/ads/redexgen/X/LX;->A0I(Lcom/facebook/ads/redexgen/X/Qj;)V

    .line 52607
    iget-object v3, p0, Lcom/facebook/ads/redexgen/X/T4;->A0Q:Lcom/facebook/ads/redexgen/X/LX;

    const/16 v2, 0x96

    const/4 v1, 0x0

    new-instance v0, Lcom/facebook/ads/redexgen/X/KF;

    invoke-direct {v0, v5, v2, v1}, Lcom/facebook/ads/redexgen/X/KF;-><init>(Landroid/view/View;IZ)V

    invoke-virtual {v3, v0}, Lcom/facebook/ads/redexgen/X/LX;->A0I(Lcom/facebook/ads/redexgen/X/Qj;)V

    .line 52608
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/T4;->A0Q:Lcom/facebook/ads/redexgen/X/LX;

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/T4;->A0I:Lcom/facebook/ads/redexgen/X/SA;

    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/LX;->A9R(Lcom/facebook/ads/redexgen/X/SA;)V

    .line 52609
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/T4;->A0Q:Lcom/facebook/ads/redexgen/X/LX;

    const/16 v0, 0x8fc

    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/LX;->A0H(I)V

    .line 52610
    .end local v1    # "colors":Lcom/facebook/ads/redexgen/X/1P;
    .end local v2    # "startDrawable":Landroid/graphics/drawable/Drawable;
    .end local v3    # "endDrawable":Landroid/graphics/drawable/Drawable;
    :cond_1
    return-void
.end method

.method public static synthetic A0F(Lcom/facebook/ads/redexgen/X/T4;Lcom/facebook/ads/redexgen/X/93;)V
    .locals 0

    .line 52611
    invoke-direct {p0, p1}, Lcom/facebook/ads/redexgen/X/T4;->A0G(Lcom/facebook/ads/redexgen/X/93;)V

    return-void
.end method

.method private A0G(Lcom/facebook/ads/redexgen/X/93;)V
    .locals 3

    .line 52612
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/T4;->A0I:Lcom/facebook/ads/redexgen/X/SA;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/SA;->getState()Lcom/facebook/ads/redexgen/X/RB;

    move-result-object v1

    sget-object v0, Lcom/facebook/ads/redexgen/X/RB;->A02:Lcom/facebook/ads/redexgen/X/RB;

    if-eq v1, v0, :cond_0

    .line 52613
    return-void

    .line 52614
    :cond_0
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/T4;->A0C:Lcom/facebook/ads/redexgen/X/Yn;

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/Ih;->A1D(Landroid/content/Context;)Z

    move-result v0

    if-eqz v0, :cond_1

    .line 52615
    new-instance v2, Lcom/facebook/ads/redexgen/X/T6;

    invoke-direct {v2, p0, p1}, Lcom/facebook/ads/redexgen/X/T6;-><init>(Lcom/facebook/ads/redexgen/X/T4;Lcom/facebook/ads/redexgen/X/93;)V

    const-wide/16 v0, 0x1388

    invoke-virtual {p0, v2, v0, v1}, Lcom/facebook/ads/redexgen/X/T4;->postDelayed(Ljava/lang/Runnable;J)Z

    .line 52616
    :cond_1
    return-void
.end method

.method public static synthetic A0H(Lcom/facebook/ads/redexgen/X/T4;)Z
    .locals 0

    .line 52617
    iget-boolean p0, p0, Lcom/facebook/ads/redexgen/X/T4;->A07:Z

    return p0
.end method

.method public static synthetic A0I(Lcom/facebook/ads/redexgen/X/T4;)Z
    .locals 0

    .line 52618
    iget-boolean p0, p0, Lcom/facebook/ads/redexgen/X/T4;->A0V:Z

    return p0
.end method

.method public static synthetic A0J(Lcom/facebook/ads/redexgen/X/T4;Z)Z
    .locals 0

    .line 52619
    iput-boolean p1, p0, Lcom/facebook/ads/redexgen/X/T4;->A07:Z

    return p1
.end method


# virtual methods
.method public final A0z()V
    .locals 8

    .line 52620
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/T4;->A01:Lcom/facebook/ads/redexgen/X/L9;

    if-eqz v0, :cond_0

    .line 52621
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/L9;->A06()Z

    .line 52622
    :cond_0
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/T4;->A0C:Lcom/facebook/ads/redexgen/X/Yn;

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/Ih;->A1W(Landroid/content/Context;)Z

    move-result v0

    if-eqz v0, :cond_1

    .line 52623
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/T4;->A0C:Lcom/facebook/ads/redexgen/X/Yn;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/7f;->A0A()Lcom/facebook/ads/redexgen/X/JE;

    move-result-object v1

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/T4;->A0I:Lcom/facebook/ads/redexgen/X/SA;

    invoke-interface {v1, v0}, Lcom/facebook/ads/redexgen/X/JE;->AGk(Landroid/view/View;)V

    .line 52624
    :cond_1
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/T4;->A0H:Lcom/facebook/ads/redexgen/X/SF;

    if-eqz v0, :cond_2

    .line 52625
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/SF;->A0C()V

    .line 52626
    :cond_2
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/T4;->A0I:Lcom/facebook/ads/redexgen/X/SA;

    const/4 v7, 0x2

    const/4 v6, 0x1

    const/4 v5, 0x0

    const/4 v4, 0x3

    if-eqz v0, :cond_3

    .line 52627
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/SA;->getEventBus()Lcom/facebook/ads/redexgen/X/8r;

    move-result-object v3

    const/4 v0, 0x6

    new-array v2, v0, [Lcom/facebook/ads/redexgen/X/8s;

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/T4;->A0O:Lcom/facebook/ads/redexgen/X/NX;

    aput-object v0, v2, v5

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/T4;->A0N:Lcom/facebook/ads/redexgen/X/O7;

    aput-object v0, v2, v6

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/T4;->A0M:Lcom/facebook/ads/redexgen/X/On;

    aput-object v0, v2, v7

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/T4;->A0P:Lcom/facebook/ads/redexgen/X/NQ;

    aput-object v0, v2, v4

    const/4 v1, 0x4

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/T4;->A0K:Lcom/facebook/ads/redexgen/X/Pu;

    aput-object v0, v2, v1

    const/4 v1, 0x5

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/T4;->A0L:Lcom/facebook/ads/redexgen/X/Ps;

    aput-object v0, v2, v1

    .line 52628
    invoke-virtual {v3, v2}, Lcom/facebook/ads/redexgen/X/8r;->A04([Lcom/facebook/ads/redexgen/X/8s;)V

    .line 52629
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/T4;->A0I:Lcom/facebook/ads/redexgen/X/SA;

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/Lo;->A0H(Landroid/view/View;)V

    .line 52630
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/T4;->A0I:Lcom/facebook/ads/redexgen/X/SA;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/SA;->A0V()V

    .line 52631
    :cond_3
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/T4;->A0J:Lcom/facebook/ads/redexgen/X/9I;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/9I;->A0i()V

    .line 52632
    new-array v4, v4, [Landroid/view/View;

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/T4;->A0I:Lcom/facebook/ads/redexgen/X/SA;

    aput-object v0, v4, v5

    iget-object v3, p0, Lcom/facebook/ads/redexgen/X/T4;->A0S:Lcom/facebook/ads/redexgen/X/KP;

    sget-object v2, Lcom/facebook/ads/redexgen/X/T4;->A0X:[Ljava/lang/String;

    const/4 v0, 0x0

    aget-object v1, v2, v0

    const/4 v0, 0x3

    aget-object v0, v2, v0

    invoke-virtual {v1}, Ljava/lang/String;->length()I

    move-result v1

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v0

    if-eq v1, v0, :cond_4

    sget-object v2, Lcom/facebook/ads/redexgen/X/T4;->A0X:[Ljava/lang/String;

    const-string v1, "4e1dr9g4n9kT22ZIdoKy0n8REVoGbgbF"

    const/4 v0, 0x2

    aput-object v1, v2, v0

    aput-object v3, v4, v6

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/T4;->A0R:Lcom/facebook/ads/redexgen/X/Kw;

    aput-object v0, v4, v7

    invoke-static {v4}, Lcom/facebook/ads/redexgen/X/Lo;->A0a([Landroid/view/View;)V

    .line 52633
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/PQ;->A0B:Lcom/facebook/ads/redexgen/X/RE;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/RE;->A0V()V

    .line 52634
    return-void

    :cond_4
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0
.end method

.method public final A10()V
    .locals 8

    .line 52635
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/T4;->A0C:Lcom/facebook/ads/redexgen/X/Yn;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/Yn;->A0E()Lcom/facebook/ads/redexgen/X/0S;

    move-result-object v0

    invoke-interface {v0}, Lcom/facebook/ads/redexgen/X/0S;->A4F()V

    .line 52636
    const/4 v3, 0x1

    iput-boolean v3, p0, Lcom/facebook/ads/redexgen/X/T4;->A04:Z

    .line 52637
    invoke-static {p0}, Lcom/facebook/ads/redexgen/X/Lo;->A0T(Landroid/view/ViewGroup;)V

    .line 52638
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/T4;->A0I:Lcom/facebook/ads/redexgen/X/SA;

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/Lo;->A0H(Landroid/view/View;)V

    .line 52639
    const/4 v0, 0x3

    new-array v1, v0, [Landroid/view/View;

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/T4;->A0I:Lcom/facebook/ads/redexgen/X/SA;

    const/4 v6, 0x0

    aput-object v0, v1, v6

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/T4;->A0S:Lcom/facebook/ads/redexgen/X/KP;

    aput-object v0, v1, v3

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/T4;->A0R:Lcom/facebook/ads/redexgen/X/Kw;

    const/4 v5, 0x2

    aput-object v0, v1, v5

    invoke-static {v1}, Lcom/facebook/ads/redexgen/X/Lo;->A0a([Landroid/view/View;)V

    .line 52640
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/T4;->A0G:Lcom/facebook/ads/redexgen/X/Q5;

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/T4;->A0E:Lcom/facebook/ads/redexgen/X/Tl;

    .line 52641
    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/Q5;->A03(Lcom/facebook/ads/redexgen/X/Tl;)Landroid/util/Pair;

    move-result-object v2

    .line 52642
    .local v1, "endCard":Landroid/util/Pair;, "Landroid/util/Pair<Lcom/facebook/ads/internal/view/rewardedvideo/EndCardController$EndCardViewType;Landroid/view/View;>;"
    iget-object v4, v2, Landroid/util/Pair;->second:Ljava/lang/Object;

    check-cast v4, Landroid/view/View;

    .line 52643
    .local v2, "endCardView":Landroid/view/View;
    const/16 v0, 0x44d

    invoke-static {v0, v4}, Lcom/facebook/ads/redexgen/X/Lo;->A0G(ILandroid/view/View;)V

    .line 52644
    sget-object v1, Lcom/facebook/ads/redexgen/X/PR;->A00:[I

    iget-object v0, v2, Landroid/util/Pair;->first:Ljava/lang/Object;

    check-cast v0, Lcom/facebook/ads/redexgen/X/Q4;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/Q4;->ordinal()I

    move-result v0

    aget v0, v1, v0

    const/4 v2, -0x1

    packed-switch v0, :pswitch_data_0

    .line 52645
    .end local v5
    :goto_0
    iget v0, p0, Lcom/facebook/ads/redexgen/X/T4;->A08:I

    if-nez v0, :cond_1

    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/T4;->A0U:Z

    if-eqz v0, :cond_1

    .line 52646
    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/T4;->A0V:Z

    if-eqz v0, :cond_0

    .line 52647
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/T4;->A0F:Lcom/facebook/ads/redexgen/X/Pd;

    invoke-interface {v0}, Lcom/facebook/ads/redexgen/X/Pd;->AGq()V

    .line 52648
    :cond_0
    new-instance v1, Lcom/facebook/ads/redexgen/X/L9;

    iget v2, p0, Lcom/facebook/ads/redexgen/X/T4;->A0A:I

    const/high16 v3, 0x42c80000    # 100.0f

    const-wide/16 v4, 0x64

    .line 52649
    invoke-static {}, Landroid/os/Looper;->getMainLooper()Landroid/os/Looper;

    move-result-object v0

    new-instance v6, Landroid/os/Handler;

    invoke-direct {v6, v0}, Landroid/os/Handler;-><init>(Landroid/os/Looper;)V

    new-instance v7, Lcom/facebook/ads/redexgen/X/T5;

    invoke-direct {v7, p0}, Lcom/facebook/ads/redexgen/X/T5;-><init>(Lcom/facebook/ads/redexgen/X/T4;)V

    invoke-direct/range {v1 .. v7}, Lcom/facebook/ads/redexgen/X/L9;-><init>(IFJLandroid/os/Handler;Lcom/facebook/ads/redexgen/X/L8;)V

    iput-object v1, p0, Lcom/facebook/ads/redexgen/X/T4;->A01:Lcom/facebook/ads/redexgen/X/L9;

    .line 52650
    invoke-virtual {v1}, Lcom/facebook/ads/redexgen/X/L9;->A07()Z

    .line 52651
    :goto_1
    return-void

    .line 52652
    :cond_1
    iput-boolean v3, p0, Lcom/facebook/ads/redexgen/X/T4;->A05:Z

    .line 52653
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/T4;->A0F:Lcom/facebook/ads/redexgen/X/Pd;

    invoke-interface {v0, v3}, Lcom/facebook/ads/redexgen/X/Pd;->AD7(Z)V

    goto :goto_1

    .line 52654
    :pswitch_0
    new-array v1, v3, [Landroid/view/View;

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/T4;->A0D:Lcom/facebook/ads/redexgen/X/No;

    aput-object v0, v1, v6

    invoke-static {v1}, Lcom/facebook/ads/redexgen/X/Lo;->A0a([Landroid/view/View;)V

    .line 52655
    new-instance v1, Landroid/widget/RelativeLayout$LayoutParams;

    invoke-direct {v1, v2, v2}, Landroid/widget/RelativeLayout$LayoutParams;-><init>(II)V

    .line 52656
    .local v3, "infoParams":Landroid/widget/RelativeLayout$LayoutParams;
    sget v0, Lcom/facebook/ads/redexgen/X/T4;->A0f:I

    invoke-virtual {v1, v0, v0, v0, v0}, Landroid/widget/RelativeLayout$LayoutParams;->setMargins(IIII)V

    .line 52657
    invoke-virtual {p0, v4, v1}, Lcom/facebook/ads/redexgen/X/T4;->addView(Landroid/view/View;Landroid/view/ViewGroup$LayoutParams;)V

    goto :goto_0

    .line 52658
    .end local v3    # "infoParams":Landroid/widget/RelativeLayout$LayoutParams;
    :pswitch_1
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/T4;->A0D:Lcom/facebook/ads/redexgen/X/No;

    invoke-virtual {v0, v6}, Lcom/facebook/ads/redexgen/X/No;->setVisibility(I)V

    .line 52659
    new-instance v1, Landroid/widget/RelativeLayout$LayoutParams;

    invoke-direct {v1, v2, v2}, Landroid/widget/RelativeLayout$LayoutParams;-><init>(II)V

    .line 52660
    .local v5, "screenshotParams":Landroid/widget/RelativeLayout$LayoutParams;
    sget v0, Lcom/facebook/ads/redexgen/X/MB;->A00:I

    invoke-virtual {v1, v6, v0, v6, v6}, Landroid/widget/RelativeLayout$LayoutParams;->setMargins(IIII)V

    .line 52661
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/T4;->A0D:Lcom/facebook/ads/redexgen/X/No;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/No;->getId()I

    move-result v0

    invoke-virtual {v1, v5, v0}, Landroid/widget/RelativeLayout$LayoutParams;->addRule(II)V

    .line 52662
    invoke-virtual {p0, v4, v1}, Lcom/facebook/ads/redexgen/X/T4;->addView(Landroid/view/View;Landroid/view/ViewGroup$LayoutParams;)V

    .line 52663
    goto :goto_0

    :pswitch_data_0
    .packed-switch 0x1
        :pswitch_1
        :pswitch_0
    .end packed-switch
.end method

.method public final A11()V
    .locals 3

    .line 52664
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/T4;->A0I:Lcom/facebook/ads/redexgen/X/SA;

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/PQ;->A05:Lcom/facebook/ads/redexgen/X/b5;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/b5;->A0x()Lcom/facebook/ads/redexgen/X/1G;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/1G;->A0D()Lcom/facebook/ads/redexgen/X/1J;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/1J;->A09()Z

    move-result v0

    if-eqz v0, :cond_0

    const/4 v0, 0x0

    :goto_0
    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/SA;->setVolume(F)V

    .line 52665
    iget-object v2, p0, Lcom/facebook/ads/redexgen/X/T4;->A0I:Lcom/facebook/ads/redexgen/X/SA;

    sget-object v1, Lcom/facebook/ads/redexgen/X/QM;->A02:Lcom/facebook/ads/redexgen/X/QM;

    const/16 v0, 0x14

    invoke-virtual {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/SA;->A0b(Lcom/facebook/ads/redexgen/X/QM;I)V

    .line 52666
    return-void

    .line 52667
    :cond_0
    const/high16 v0, 0x3f800000    # 1.0f

    goto :goto_0
.end method

.method public final A12(Z)V
    .locals 3

    .line 52668
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/T4;->A01:Lcom/facebook/ads/redexgen/X/L9;

    if-eqz v0, :cond_0

    .line 52669
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/L9;->A06()Z

    .line 52670
    :cond_0
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/T4;->A0I:Lcom/facebook/ads/redexgen/X/SA;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/SA;->A0i()Z

    move-result v0

    if-eqz v0, :cond_1

    .line 52671
    return-void

    .line 52672
    :cond_1
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/T4;->A0I:Lcom/facebook/ads/redexgen/X/SA;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/SA;->getVideoStartReason()Lcom/facebook/ads/redexgen/X/QM;

    move-result-object v0

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/T4;->A02:Lcom/facebook/ads/redexgen/X/QM;

    .line 52673
    iput-boolean p1, p0, Lcom/facebook/ads/redexgen/X/T4;->A06:Z

    .line 52674
    iget-object v2, p0, Lcom/facebook/ads/redexgen/X/T4;->A0I:Lcom/facebook/ads/redexgen/X/SA;

    const/4 v1, 0x0

    const/16 v0, 0xd

    invoke-virtual {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/SA;->A0e(ZI)V

    .line 52675
    return-void
.end method

.method public final A13(Z)V
    .locals 4

    .line 52676
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/T4;->A01:Lcom/facebook/ads/redexgen/X/L9;

    if-eqz v0, :cond_1

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/L9;->A05()Z

    move-result v0

    if-nez v0, :cond_1

    .line 52677
    iget-object v3, p0, Lcom/facebook/ads/redexgen/X/T4;->A01:Lcom/facebook/ads/redexgen/X/L9;

    sget-object v1, Lcom/facebook/ads/redexgen/X/T4;->A0X:[Ljava/lang/String;

    const/4 v0, 0x5

    aget-object v1, v1, v0

    const/16 v0, 0x11

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    const/16 v0, 0x53

    if-eq v1, v0, :cond_0

    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    :cond_0
    sget-object v2, Lcom/facebook/ads/redexgen/X/T4;->A0X:[Ljava/lang/String;

    const-string v1, "pObqmm4LdTsqTcUgL7kozPbufRCkwYZC"

    const/4 v0, 0x7

    aput-object v1, v2, v0

    const-string v1, "5szeP9lNqMFVM0rzjOQcuK8NUDwg7u3G"

    const/4 v0, 0x6

    aput-object v1, v2, v0

    invoke-virtual {v3}, Lcom/facebook/ads/redexgen/X/L9;->A07()Z

    .line 52678
    :cond_1
    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/T4;->A04:Z

    if-nez v0, :cond_3

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/T4;->A0I:Lcom/facebook/ads/redexgen/X/SA;

    .line 52679
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/SA;->getState()Lcom/facebook/ads/redexgen/X/RB;

    move-result-object v1

    sget-object v0, Lcom/facebook/ads/redexgen/X/RB;->A06:Lcom/facebook/ads/redexgen/X/RB;

    if-eq v1, v0, :cond_3

    iget-object v2, p0, Lcom/facebook/ads/redexgen/X/T4;->A02:Lcom/facebook/ads/redexgen/X/QM;

    if-eqz v2, :cond_3

    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/T4;->A06:Z

    if-eqz v0, :cond_2

    if-eqz p1, :cond_3

    .line 52680
    :cond_2
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/T4;->A0I:Lcom/facebook/ads/redexgen/X/SA;

    const/16 v0, 0x13

    invoke-virtual {v1, v2, v0}, Lcom/facebook/ads/redexgen/X/SA;->A0b(Lcom/facebook/ads/redexgen/X/QM;I)V

    .line 52681
    :cond_3
    return-void
.end method

.method public final A14()Z
    .locals 2

    .line 52682
    iget v1, p0, Lcom/facebook/ads/redexgen/X/T4;->A08:I

    const/4 v0, 0x2

    if-eq v1, v0, :cond_0

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/PQ;->A05:Lcom/facebook/ads/redexgen/X/b5;

    .line 52683
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/b5;->A0x()Lcom/facebook/ads/redexgen/X/1G;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/1G;->A0O()Z

    move-result v0

    if-eqz v0, :cond_0

    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/T4;->A04:Z

    if-nez v0, :cond_0

    .line 52684
    const/4 v0, 0x1

    return v0

    .line 52685
    :cond_0
    const/4 v0, 0x0

    return v0
.end method

.method public final A15()Z
    .locals 1

    .line 52686
    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/T4;->A04:Z

    return v0
.end method

.method public getColors()Lcom/facebook/ads/redexgen/X/1P;
    .locals 1

    .line 52687
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/T4;->A00:Lcom/facebook/ads/redexgen/X/1P;

    return-object v0
.end method

.method public getFullScreenAdStyle()Lcom/facebook/ads/redexgen/X/Pc;
    .locals 9

    .line 52688
    new-instance v2, Lcom/facebook/ads/redexgen/X/Pc;

    const/4 v3, 0x1

    sget v4, Lcom/facebook/ads/redexgen/X/Pc;->A06:I

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/PQ;->A05:Lcom/facebook/ads/redexgen/X/b5;

    .line 52689
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/b5;->A0w()Lcom/facebook/ads/redexgen/X/1C;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/1C;->A01()Lcom/facebook/ads/redexgen/X/1P;

    move-result-object v5

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/PQ;->A05:Lcom/facebook/ads/redexgen/X/b5;

    .line 52690
    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/Tl;->A08(Lcom/facebook/ads/redexgen/X/b5;)Z

    move-result v6

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/PQ;->A05:Lcom/facebook/ads/redexgen/X/b5;

    .line 52691
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/b5;->A0w()Lcom/facebook/ads/redexgen/X/1C;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/1C;->A01()Lcom/facebook/ads/redexgen/X/1P;

    move-result-object v1

    const/4 v0, 0x1

    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/1P;->A07(Z)I

    move-result v7

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/PQ;->A05:Lcom/facebook/ads/redexgen/X/b5;

    .line 52692
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/b5;->A0x()Lcom/facebook/ads/redexgen/X/1G;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/1G;->A0D()Lcom/facebook/ads/redexgen/X/1J;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/1J;->A07()Ljava/lang/String;

    move-result-object v8

    invoke-direct/range {v2 .. v8}, Lcom/facebook/ads/redexgen/X/Pc;-><init>(ZILcom/facebook/ads/redexgen/X/1P;ZILjava/lang/String;)V

    .line 52693
    return-object v2
.end method

.method public getTouchDataRecorder()Lcom/facebook/ads/redexgen/X/Lg;
    .locals 1

    .line 52694
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/PQ;->A08:Lcom/facebook/ads/redexgen/X/Lg;

    return-object v0
.end method

.method public final onConfigurationChanged(Landroid/content/res/Configuration;)V
    .locals 5

    .line 52695
    invoke-super {p0, p1}, Lcom/facebook/ads/redexgen/X/PQ;->onConfigurationChanged(Landroid/content/res/Configuration;)V

    .line 52696
    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/T4;->A04:Z

    if-nez v0, :cond_0

    .line 52697
    iget v0, p1, Landroid/content/res/Configuration;->orientation:I

    const/4 v4, 0x1

    if-ne v0, v4, :cond_1

    .line 52698
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/PQ;->A05:Lcom/facebook/ads/redexgen/X/b5;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/b5;->A0w()Lcom/facebook/ads/redexgen/X/1C;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/1C;->A01()Lcom/facebook/ads/redexgen/X/1P;

    move-result-object v0

    .line 52699
    :goto_0
    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/T4;->A00:Lcom/facebook/ads/redexgen/X/1P;

    .line 52700
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/T4;->A0E:Lcom/facebook/ads/redexgen/X/Tl;

    invoke-virtual {v0, v4}, Lcom/facebook/ads/redexgen/X/Nv;->setViewShowsOverMedia(Z)V

    .line 52701
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/T4;->A0E:Lcom/facebook/ads/redexgen/X/Tl;

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/T4;->A00:Lcom/facebook/ads/redexgen/X/1P;

    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/Nv;->setUpButtonColors(Lcom/facebook/ads/redexgen/X/1P;)V

    .line 52702
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/T4;->A0D:Lcom/facebook/ads/redexgen/X/No;

    iget v0, p1, Landroid/content/res/Configuration;->orientation:I

    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/No;->A0A(I)V

    .line 52703
    :cond_0
    return-void

    .line 52704
    :cond_1
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/PQ;->A05:Lcom/facebook/ads/redexgen/X/b5;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/b5;->A0w()Lcom/facebook/ads/redexgen/X/1C;

    move-result-object v3

    sget-object v1, Lcom/facebook/ads/redexgen/X/T4;->A0X:[Ljava/lang/String;

    const/4 v0, 0x4

    aget-object v1, v1, v0

    const/4 v0, 0x4

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    const/16 v0, 0x52

    if-eq v1, v0, :cond_2

    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    :cond_2
    sget-object v2, Lcom/facebook/ads/redexgen/X/T4;->A0X:[Ljava/lang/String;

    const-string v1, "uYIsd2DhsQ91Wp0xmXJ9348eeMLtSRMf"

    const/4 v0, 0x1

    aput-object v1, v2, v0

    invoke-virtual {v3}, Lcom/facebook/ads/redexgen/X/1C;->A00()Lcom/facebook/ads/redexgen/X/1P;

    move-result-object v0

    goto :goto_0
.end method

.method public final onLayout(ZIIII)V
    .locals 5

    .line 52705
    invoke-super/range {p0 .. p5}, Lcom/facebook/ads/redexgen/X/PQ;->onLayout(ZIIII)V

    .line 52706
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/T4;->A0D:Lcom/facebook/ads/redexgen/X/No;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/No;->getExpandableLayout()Landroid/view/View;

    move-result-object v4

    .line 52707
    .local v0, "expandableLayout":Landroid/view/View;
    if-eqz v4, :cond_0

    .line 52708
    if-eqz p1, :cond_0

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/T4;->A03:Lcom/facebook/ads/redexgen/X/KI;

    if-nez v0, :cond_0

    .line 52709
    invoke-virtual {v4}, Landroid/view/View;->getHeight()I

    move-result v3

    const/4 v2, 0x0

    const/16 v0, 0x12c

    new-instance v1, Lcom/facebook/ads/redexgen/X/KI;

    invoke-direct {v1, v4, v0, v3, v2}, Lcom/facebook/ads/redexgen/X/KI;-><init>(Landroid/view/View;III)V

    iput-object v1, p0, Lcom/facebook/ads/redexgen/X/T4;->A03:Lcom/facebook/ads/redexgen/X/KI;

    .line 52710
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/T4;->A0Q:Lcom/facebook/ads/redexgen/X/LX;

    invoke-virtual {v0, v1}, Lcom/facebook/ads/redexgen/X/LX;->A0I(Lcom/facebook/ads/redexgen/X/Qj;)V

    .line 52711
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/T4;->A0Q:Lcom/facebook/ads/redexgen/X/LX;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/LX;->A0G()V

    .line 52712
    :cond_0
    return-void
.end method

.method public final onWindowFocusChanged(Z)V
    .locals 1

    .line 52713
    invoke-super {p0, p1}, Lcom/facebook/ads/redexgen/X/PQ;->onWindowFocusChanged(Z)V

    .line 52714
    const/4 v0, 0x0

    if-eqz p1, :cond_0

    .line 52715
    invoke-virtual {p0, v0}, Lcom/facebook/ads/redexgen/X/T4;->A13(Z)V

    .line 52716
    :goto_0
    return-void

    .line 52717
    :cond_0
    invoke-virtual {p0, v0}, Lcom/facebook/ads/redexgen/X/T4;->A12(Z)V

    goto :goto_0
.end method
