.class public final synthetic Lcom/facebook/ads/redexgen/X/Tb;
.super Ljava/lang/Object;
.source ""

# interfaces
.implements Lcom/facebook/ads/redexgen/X/NR;


# instance fields
.field public final synthetic A00:Lcom/facebook/ads/redexgen/X/TT;


# direct methods
.method public synthetic constructor <init>(Lcom/facebook/ads/redexgen/X/TT;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/Tb;->A00:Lcom/facebook/ads/redexgen/X/TT;

    return-void
.end method


# virtual methods
.method public final A9D(Ljava/lang/String;)Z
    .locals 1

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Tb;->A00:Lcom/facebook/ads/redexgen/X/TT;

    invoke-virtual {v0, p1}, Lcom/facebook/ads/redexgen/X/TT;->A1A(Ljava/lang/String;)Z

    move-result v0

    return v0
.end method
