.class public final Lcom/facebook/ads/redexgen/X/Te;
.super Ljava/lang/Object;
.source ""

# interfaces
.implements Lcom/facebook/ads/redexgen/X/O0;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/facebook/ads/redexgen/X/BI;-><init>(Lcom/facebook/ads/redexgen/X/OP;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field public final synthetic A00:Lcom/facebook/ads/redexgen/X/BI;


# direct methods
.method public constructor <init>(Lcom/facebook/ads/redexgen/X/BI;)V
    .locals 0

    .line 53669
    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/Te;->A00:Lcom/facebook/ads/redexgen/X/BI;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final AAh()V
    .locals 0

    .line 53670
    return-void
.end method

.method public final ACB()V
    .locals 2

    .line 53671
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/Te;->A00:Lcom/facebook/ads/redexgen/X/BI;

    const/4 v0, 0x0

    invoke-static {v1, v0}, Lcom/facebook/ads/redexgen/X/BI;->A09(Lcom/facebook/ads/redexgen/X/BI;Z)Z

    .line 53672
    return-void
.end method

.method public final ACC()V
    .locals 2

    .line 53673
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/Te;->A00:Lcom/facebook/ads/redexgen/X/BI;

    const/4 v0, 0x1

    invoke-static {v1, v0}, Lcom/facebook/ads/redexgen/X/BI;->A09(Lcom/facebook/ads/redexgen/X/BI;Z)Z

    .line 53674
    return-void
.end method
