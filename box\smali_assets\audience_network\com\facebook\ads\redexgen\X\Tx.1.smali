.class public final Lcom/facebook/ads/redexgen/X/Tx;
.super Ljava/lang/Object;
.source ""

# interfaces
.implements Lcom/facebook/ads/redexgen/X/Op;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/facebook/ads/redexgen/X/NW;->setupDotsLayout(Lcom/facebook/ads/redexgen/X/V2;Ljava/util/ArrayList;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field public final synthetic A00:Lcom/facebook/ads/redexgen/X/NW;


# direct methods
.method public constructor <init>(Lcom/facebook/ads/redexgen/X/NW;)V
    .locals 0

    .line 54536
    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/Tx;->A00:Lcom/facebook/ads/redexgen/X/NW;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final AGp(I)V
    .locals 1

    .line 54537
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Tx;->A00:Lcom/facebook/ads/redexgen/X/NW;

    invoke-static {v0, p1}, Lcom/facebook/ads/redexgen/X/NW;->A03(Lcom/facebook/ads/redexgen/X/NW;I)V

    .line 54538
    return-void
.end method
