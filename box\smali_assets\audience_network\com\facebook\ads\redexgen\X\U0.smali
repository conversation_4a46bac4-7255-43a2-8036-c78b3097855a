.class public final Lcom/facebook/ads/redexgen/X/U0;
.super Ljava/lang/Object;
.source ""

# interfaces
.implements Lcom/facebook/ads/redexgen/X/Or;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/facebook/ads/redexgen/X/BQ;-><init>(Lcom/facebook/ads/redexgen/X/3r;ILjava/util/List;Lcom/facebook/ads/redexgen/X/RE;Landroid/os/Bundle;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field public final synthetic A00:Lcom/facebook/ads/redexgen/X/BQ;


# direct methods
.method public constructor <init>(Lcom/facebook/ads/redexgen/X/BQ;)V
    .locals 0

    .line 54588
    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/U0;->A00:Lcom/facebook/ads/redexgen/X/BQ;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final ADT(I)V
    .locals 1

    .line 54589
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/U0;->A00:Lcom/facebook/ads/redexgen/X/BQ;

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/BQ;->A02(Lcom/facebook/ads/redexgen/X/BQ;)V

    .line 54590
    return-void
.end method
