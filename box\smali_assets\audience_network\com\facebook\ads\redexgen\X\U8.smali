.class public final Lcom/facebook/ads/redexgen/X/U8;
.super Ljava/lang/Object;
.source ""

# interfaces
.implements Lcom/facebook/ads/redexgen/X/Mj;


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 55013
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final A5s()Ljava/lang/String;
    .locals 1

    .line 55014
    sget-object v0, Lcom/facebook/ads/redexgen/X/QP;->A06:Lcom/facebook/ads/redexgen/X/QP;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/QP;->A02()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public final A6R()Ljava/lang/String;
    .locals 1

    .line 55015
    sget-object v0, Lcom/facebook/ads/redexgen/X/QP;->A04:Lcom/facebook/ads/redexgen/X/QP;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/QP;->A02()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public final A6u()Ljava/lang/String;
    .locals 1

    .line 55016
    sget-object v0, Lcom/facebook/ads/redexgen/X/QP;->A08:Lcom/facebook/ads/redexgen/X/QP;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/QP;->A02()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public final A6y()Ljava/lang/String;
    .locals 1

    .line 55017
    sget-object v0, Lcom/facebook/ads/redexgen/X/QP;->A09:Lcom/facebook/ads/redexgen/X/QP;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/QP;->A02()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public final A7B()Ljava/lang/String;
    .locals 1

    .line 55018
    sget-object v0, Lcom/facebook/ads/redexgen/X/QP;->A0A:Lcom/facebook/ads/redexgen/X/QP;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/QP;->A02()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public final A7f()Ljava/lang/String;
    .locals 1

    .line 55019
    sget-object v0, Lcom/facebook/ads/internal/protocol/AdPlacementType;->REWARDED_VIDEO:Lcom/facebook/ads/internal/protocol/AdPlacementType;

    invoke-virtual {v0}, Lcom/facebook/ads/internal/protocol/AdPlacementType;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public final A7w()Ljava/lang/String;
    .locals 1

    .line 55020
    sget-object v0, Lcom/facebook/ads/redexgen/X/QP;->A0B:Lcom/facebook/ads/redexgen/X/QP;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/QP;->A02()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public final A7x()Ljava/lang/String;
    .locals 1

    .line 55021
    sget-object v0, Lcom/facebook/ads/redexgen/X/QP;->A0C:Lcom/facebook/ads/redexgen/X/QP;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/QP;->A02()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
