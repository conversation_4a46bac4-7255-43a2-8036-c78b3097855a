.class public final Lcom/facebook/ads/redexgen/X/UR;
.super Lcom/facebook/ads/redexgen/X/56;
.source ""


# instance fields
.field public final A00:Lcom/facebook/ads/internal/api/AdNativeComponentView;


# direct methods
.method public constructor <init>(Lcom/facebook/ads/internal/api/AdNativeComponentView;)V
    .locals 0

    .line 55867
    invoke-direct {p0, p1}, Lcom/facebook/ads/redexgen/X/56;-><init>(Landroid/view/View;)V

    .line 55868
    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/UR;->A00:Lcom/facebook/ads/internal/api/AdNativeComponentView;

    .line 55869
    return-void
.end method


# virtual methods
.method public final A0j()Lcom/facebook/ads/internal/api/AdNativeComponentView;
    .locals 1

    .line 55870
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/UR;->A00:Lcom/facebook/ads/internal/api/AdNativeComponentView;

    return-object v0
.end method
