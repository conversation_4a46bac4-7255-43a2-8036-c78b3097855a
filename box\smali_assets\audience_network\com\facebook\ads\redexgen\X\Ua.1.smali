.class public final Lcom/facebook/ads/redexgen/X/Ua;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/facebook/ads/redexgen/X/Ly;
    }
.end annotation


# static fields
.field public static A00:[Ljava/lang/String;

.field public static final A01:[B

.field public static final A02:[S


# direct methods
.method public static constructor <clinit>()V
    .locals 3

    .line 2447
    const/16 v0, 0x8

    new-array v2, v0, [Ljava/lang/String;

    const/4 v1, 0x0

    const-string v0, "hdmHMjdsCvnnIiAm68nbzST5FT4P5mLb"

    aput-object v0, v2, v1

    const/4 v1, 0x1

    const-string v0, "EH3LUmZxwOvThvXMHcswnn58jDASieiK"

    aput-object v0, v2, v1

    const/4 v1, 0x2

    const-string v0, "ljU4lnctUqCxo644HWpRzVkau739AG18"

    aput-object v0, v2, v1

    const/4 v1, 0x3

    const-string v0, "cf0wMMVzQ5eDMzw1G9WbfLTL5VaSUv"

    aput-object v0, v2, v1

    const/4 v1, 0x4

    const-string v0, "l50byEuqIAJWTFcEqiPP3cq5SFvrKOhf"

    aput-object v0, v2, v1

    const/4 v1, 0x5

    const-string v0, "BkuhnFfUhQO532vAuKLRONmm63PuLWNO"

    aput-object v0, v2, v1

    const/4 v1, 0x6

    const-string v0, "lueSUxwI7577CW"

    aput-object v0, v2, v1

    const/4 v1, 0x7

    const-string v0, "p49ZYnIXKwO4jtZLxoBrhqW2OVcn4ZYc"

    aput-object v0, v2, v1

    sput-object v2, Lcom/facebook/ads/redexgen/X/Ua;->A00:[Ljava/lang/String;

    const/16 v1, 0xff

    new-array v0, v1, [S

    fill-array-data v0, :array_0

    sput-object v0, Lcom/facebook/ads/redexgen/X/Ua;->A02:[S

    .line 2448
    new-array v0, v1, [B

    fill-array-data v0, :array_1

    sput-object v0, Lcom/facebook/ads/redexgen/X/Ua;->A01:[B

    return-void

    nop

    :array_0
    .array-data 2
        0x200s
        0x200s
        0x1c8s
        0x200s
        0x148s
        0x1c8s
        0x14fs
        0x200s
        0x195s
        0x148s
        0x10fs
        0x1c8s
        0x184s
        0x14fs
        0x124s
        0x200s
        0x1c6s
        0x195s
        0x16cs
        0x148s
        0x12as
        0x10fs
        0x1f0s
        0x1c8s
        0x1a4s
        0x184s
        0x168s
        0x14fs
        0x138s
        0x124s
        0x111s
        0x200s
        0x1e2s
        0x1c6s
        0x1acs
        0x195s
        0x17fs
        0x16cs
        0x159s
        0x148s
        0x138s
        0x12as
        0x11cs
        0x10fs
        0x103s
        0x1f0s
        0x1dbs
        0x1c8s
        0x1b5s
        0x1a4s
        0x194s
        0x184s
        0x176s
        0x168s
        0x15bs
        0x14fs
        0x143s
        0x138s
        0x12es
        0x124s
        0x11as
        0x111s
        0x109s
        0x200s
        0x1f1s
        0x1e2s
        0x1d4s
        0x1c6s
        0x1b9s
        0x1acs
        0x1a1s
        0x195s
        0x18as
        0x17fs
        0x175s
        0x16cs
        0x162s
        0x159s
        0x151s
        0x148s
        0x140s
        0x138s
        0x131s
        0x12as
        0x123s
        0x11cs
        0x116s
        0x10fs
        0x109s
        0x103s
        0x1fbs
        0x1f0s
        0x1e5s
        0x1dbs
        0x1d1s
        0x1c8s
        0x1bes
        0x1b5s
        0x1acs
        0x1a4s
        0x19cs
        0x194s
        0x18cs
        0x184s
        0x17ds
        0x176s
        0x16fs
        0x168s
        0x162s
        0x15bs
        0x155s
        0x14fs
        0x149s
        0x143s
        0x13es
        0x138s
        0x133s
        0x12es
        0x129s
        0x124s
        0x11fs
        0x11as
        0x116s
        0x111s
        0x10ds
        0x109s
        0x105s
        0x200s
        0x1f9s
        0x1f1s
        0x1e9s
        0x1e2s
        0x1dbs
        0x1d4s
        0x1cds
        0x1c6s
        0x1bfs
        0x1b9s
        0x1b3s
        0x1acs
        0x1a6s
        0x1a1s
        0x19bs
        0x195s
        0x18fs
        0x18as
        0x185s
        0x17fs
        0x17as
        0x175s
        0x170s
        0x16cs
        0x167s
        0x162s
        0x15es
        0x159s
        0x155s
        0x151s
        0x14cs
        0x148s
        0x144s
        0x140s
        0x13cs
        0x138s
        0x135s
        0x131s
        0x12ds
        0x12as
        0x126s
        0x123s
        0x11fs
        0x11cs
        0x119s
        0x116s
        0x112s
        0x10fs
        0x10cs
        0x109s
        0x106s
        0x103s
        0x101s
        0x1fbs
        0x1f5s
        0x1f0s
        0x1ebs
        0x1e5s
        0x1e0s
        0x1dbs
        0x1d6s
        0x1d1s
        0x1ccs
        0x1c8s
        0x1c3s
        0x1bes
        0x1bas
        0x1b5s
        0x1b1s
        0x1acs
        0x1a8s
        0x1a4s
        0x1a0s
        0x19cs
        0x198s
        0x194s
        0x190s
        0x18cs
        0x188s
        0x184s
        0x181s
        0x17ds
        0x179s
        0x176s
        0x172s
        0x16fs
        0x16bs
        0x168s
        0x165s
        0x162s
        0x15es
        0x15bs
        0x158s
        0x155s
        0x152s
        0x14fs
        0x14cs
        0x149s
        0x146s
        0x143s
        0x140s
        0x13es
        0x13bs
        0x138s
        0x136s
        0x133s
        0x130s
        0x12es
        0x12bs
        0x129s
        0x126s
        0x124s
        0x121s
        0x11fs
        0x11ds
        0x11as
        0x118s
        0x116s
        0x113s
        0x111s
        0x10fs
        0x10ds
        0x10bs
        0x109s
        0x107s
        0x105s
        0x103s
    .end array-data

    nop

    :array_1
    .array-data 1
        0x9t
        0xbt
        0xct
        0xdt
        0xdt
        0xet
        0xet
        0xft
        0xft
        0xft
        0xft
        0x10t
        0x10t
        0x10t
        0x10t
        0x11t
        0x11t
        0x11t
        0x11t
        0x11t
        0x11t
        0x11t
        0x12t
        0x12t
        0x12t
        0x12t
        0x12t
        0x12t
        0x12t
        0x12t
        0x12t
        0x13t
        0x13t
        0x13t
        0x13t
        0x13t
        0x13t
        0x13t
        0x13t
        0x13t
        0x13t
        0x13t
        0x13t
        0x13t
        0x13t
        0x14t
        0x14t
        0x14t
        0x14t
        0x14t
        0x14t
        0x14t
        0x14t
        0x14t
        0x14t
        0x14t
        0x14t
        0x14t
        0x14t
        0x14t
        0x14t
        0x14t
        0x14t
        0x15t
        0x15t
        0x15t
        0x15t
        0x15t
        0x15t
        0x15t
        0x15t
        0x15t
        0x15t
        0x15t
        0x15t
        0x15t
        0x15t
        0x15t
        0x15t
        0x15t
        0x15t
        0x15t
        0x15t
        0x15t
        0x15t
        0x15t
        0x15t
        0x15t
        0x15t
        0x15t
        0x16t
        0x16t
        0x16t
        0x16t
        0x16t
        0x16t
        0x16t
        0x16t
        0x16t
        0x16t
        0x16t
        0x16t
        0x16t
        0x16t
        0x16t
        0x16t
        0x16t
        0x16t
        0x16t
        0x16t
        0x16t
        0x16t
        0x16t
        0x16t
        0x16t
        0x16t
        0x16t
        0x16t
        0x16t
        0x16t
        0x16t
        0x16t
        0x16t
        0x16t
        0x16t
        0x16t
        0x16t
        0x17t
        0x17t
        0x17t
        0x17t
        0x17t
        0x17t
        0x17t
        0x17t
        0x17t
        0x17t
        0x17t
        0x17t
        0x17t
        0x17t
        0x17t
        0x17t
        0x17t
        0x17t
        0x17t
        0x17t
        0x17t
        0x17t
        0x17t
        0x17t
        0x17t
        0x17t
        0x17t
        0x17t
        0x17t
        0x17t
        0x17t
        0x17t
        0x17t
        0x17t
        0x17t
        0x17t
        0x17t
        0x17t
        0x17t
        0x17t
        0x17t
        0x17t
        0x17t
        0x17t
        0x17t
        0x17t
        0x17t
        0x17t
        0x17t
        0x17t
        0x17t
        0x17t
        0x17t
        0x17t
        0x18t
        0x18t
        0x18t
        0x18t
        0x18t
        0x18t
        0x18t
        0x18t
        0x18t
        0x18t
        0x18t
        0x18t
        0x18t
        0x18t
        0x18t
        0x18t
        0x18t
        0x18t
        0x18t
        0x18t
        0x18t
        0x18t
        0x18t
        0x18t
        0x18t
        0x18t
        0x18t
        0x18t
        0x18t
        0x18t
        0x18t
        0x18t
        0x18t
        0x18t
        0x18t
        0x18t
        0x18t
        0x18t
        0x18t
        0x18t
        0x18t
        0x18t
        0x18t
        0x18t
        0x18t
        0x18t
        0x18t
        0x18t
        0x18t
        0x18t
        0x18t
        0x18t
        0x18t
        0x18t
        0x18t
        0x18t
        0x18t
        0x18t
        0x18t
        0x18t
        0x18t
        0x18t
        0x18t
        0x18t
        0x18t
        0x18t
        0x18t
        0x18t
        0x18t
        0x18t
        0x18t
        0x18t
        0x18t
        0x18t
    .end array-data
.end method

.method public constructor <init>()V
    .locals 0

    .line 56068
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static A00([IIIIIII)V
    .locals 42

    .line 56069
    move/from16 v25, p1

    add-int/lit8 v40, v25, -0x1

    .line 56070
    .local v4, "wm":I
    move/from16 v41, p2

    add-int/lit8 v39, v41, -0x1

    .line 56071
    .local v5, "hm":I
    move/from16 v24, p3

    mul-int/lit8 v26, v24, 0x2

    const/4 v1, 0x1

    add-int v26, v26, v1

    .line 56072
    .local v6, "div":I
    sget-object v0, Lcom/facebook/ads/redexgen/X/Ua;->A02:[S

    aget-short v30, v0, v24

    .line 56073
    .local v8, "mul_sum":I
    sget-object v0, Lcom/facebook/ads/redexgen/X/Ua;->A01:[B

    aget-byte v27, v0, v24

    .line 56074
    .local v9, "shr_sum":B
    move/from16 v0, v26

    new-array v2, v0, [I

    .line 56075
    .local v10, "stack":[I
    const-wide/16 v11, 0x0

    const/16 v29, 0x8

    const/16 v28, 0x10

    move/from16 v3, p6

    if-ne v3, v1, :cond_8

    .line 56076
    mul-int v38, p5, v41

    div-int v38, v38, p4

    .line 56077
    .local v7, "minY":I
    add-int/lit8 v37, p5, 0x1

    mul-int v37, v37, v41

    div-int v37, v37, p4

    .line 56078
    .local v14, "maxY":I
    .local v15, "y":I
    :goto_0
    move/from16 v1, v38

    move/from16 v0, v37

    if-ge v1, v0, :cond_12

    .line 56079
    .local v21, "sum_out_b":J
    move-wide/from16 v21, v11

    .local v23, "sum_out_g":J
    move-wide/from16 v19, v11

    .local v25, "sum_out_r":J
    move-wide/from16 v17, v11

    .local v27, "sum_in_b":J
    move-wide v15, v11

    .local v29, "sum_in_g":J
    move-wide v13, v11

    .local v31, "sum_in_r":J
    move-wide v9, v11

    .local v33, "sum_b":J
    move-wide v7, v11

    .local v35, "sum_g":J
    move-wide v5, v11

    .line 56080
    .local v37, "sum_r":J
    mul-int v4, v25, v38

    .line 56081
    .local v18, "src_i":I
    const/4 v3, 0x0

    .local v12, "i":I
    :goto_1
    move/from16 v0, v24

    if-gt v3, v0, :cond_0

    .line 56082
    .local v13, "stack_i":I
    aget v0, p0, v4

    aput v0, v2, v3

    .line 56083
    aget v0, p0, v4

    ushr-int/lit8 v0, v0, 0x10

    and-int/lit16 v0, v0, 0xff

    add-int/lit8 v1, v3, 0x1

    mul-int/2addr v0, v1

    .end local v13    # "stack_i":I
    .end local v14    # "maxY":I
    .local v40, "maxY":I
    .local p0, "stack_i":I
    int-to-long v0, v0

    add-long/2addr v5, v0

    .line 56084
    aget v0, p0, v4

    ushr-int/lit8 v0, v0, 0x8

    and-int/lit16 v0, v0, 0xff

    add-int/lit8 v1, v3, 0x1

    mul-int/2addr v0, v1

    int-to-long v0, v0

    add-long/2addr v7, v0

    .line 56085
    aget v0, p0, v4

    and-int/lit16 v0, v0, 0xff

    add-int/lit8 v1, v3, 0x1

    mul-int/2addr v0, v1

    int-to-long v0, v0

    add-long/2addr v9, v0

    .line 56086
    aget v0, p0, v4

    ushr-int/lit8 v0, v0, 0x10

    and-int/lit16 v0, v0, 0xff

    int-to-long v0, v0

    add-long v19, v19, v0

    .line 56087
    aget v0, p0, v4

    ushr-int/lit8 v0, v0, 0x8

    and-int/lit16 v0, v0, 0xff

    int-to-long v0, v0

    add-long v21, v21, v0

    .line 56088
    aget v0, p0, v4

    and-int/lit16 v0, v0, 0xff

    int-to-long v0, v0

    add-long/2addr v11, v0

    .line 56089
    add-int/lit8 v3, v3, 0x1

    goto :goto_1

    .line 56090
    .end local v40    # "maxY":I
    .end local p0    # "stack_i":I
    .restart local v14    # "maxY":I
    .end local v14    # "maxY":I
    .restart local v40    # "maxY":I
    :cond_0
    const/4 v3, 0x1

    .end local v12    # "i":I
    .local v11, "i":I
    :goto_2
    move/from16 v0, v24

    if-gt v3, v0, :cond_2

    .line 56091
    move/from16 v0, v40

    if-gt v3, v0, :cond_1

    add-int/lit8 v4, v4, 0x1

    .line 56092
    :cond_1
    add-int v1, v3, v24

    .line 56093
    .local v12, "stack_i":I
    aget v0, p0, v4

    aput v0, v2, v1

    .line 56094
    aget v0, p0, v4

    ushr-int/lit8 v0, v0, 0x10

    and-int/lit16 v0, v0, 0xff

    add-int/lit8 v1, v24, 0x1

    sub-int/2addr v1, v3

    mul-int/2addr v0, v1

    int-to-long v0, v0

    add-long/2addr v5, v0

    .line 56095
    aget v0, p0, v4

    ushr-int/lit8 v0, v0, 0x8

    and-int/lit16 v0, v0, 0xff

    add-int/lit8 v1, v24, 0x1

    sub-int/2addr v1, v3

    mul-int/2addr v0, v1

    int-to-long v0, v0

    add-long/2addr v7, v0

    .line 56096
    aget v0, p0, v4

    and-int/lit16 v0, v0, 0xff

    add-int/lit8 v1, v24, 0x1

    sub-int/2addr v1, v3

    mul-int/2addr v0, v1

    int-to-long v0, v0

    add-long/2addr v9, v0

    .line 56097
    aget v0, p0, v4

    ushr-int/lit8 v0, v0, 0x10

    and-int/lit16 v0, v0, 0xff

    int-to-long v0, v0

    add-long/2addr v13, v0

    .line 56098
    aget v0, p0, v4

    ushr-int/lit8 v0, v0, 0x8

    and-int/lit16 v0, v0, 0xff

    int-to-long v0, v0

    add-long/2addr v15, v0

    .line 56099
    aget v0, p0, v4

    and-int/lit16 v0, v0, 0xff

    int-to-long v0, v0

    add-long v17, v17, v0

    .line 56100
    add-int/lit8 v3, v3, 0x1

    goto :goto_2

    .line 56101
    .end local v12    # "stack_i":I
    :cond_2
    move/from16 v23, v24

    .line 56102
    .local v12, "sp":I
    move/from16 v36, v24

    .line 56103
    .local v13, "xp":I
    move/from16 v1, v36

    move/from16 v0, v40

    if-le v1, v0, :cond_3

    move/from16 v36, v40

    .line 56104
    :cond_3
    mul-int v35, v38, v25

    add-int v35, v35, v36

    .line 56105
    .end local v18    # "src_i":I
    .local v14, "src_i":I
    mul-int v34, v38, v25

    .line 56106
    .local v18, "dst_i":I
    const/16 v33, 0x0

    .local v7, "x":I
    .local p0, "minY":I
    :goto_3
    move/from16 v1, v33

    move/from16 v0, v25

    if-ge v1, v0, :cond_7

    .line 56107
    aget v0, p0, v34

    const/high16 v1, -0x1000000

    .end local v11    # "i":I
    .local p2, "i":I
    and-int/2addr v0, v1

    int-to-long v3, v0

    .end local v14    # "src_i":I
    .end local v15    # "y":I
    .local v11, "y":I
    .local p1, "src_i":I
    move/from16 v0, v30

    int-to-long v0, v0

    mul-long/2addr v0, v5

    ushr-long v0, v0, v27

    const-wide/16 v31, 0xff

    and-long v0, v0, v31

    shl-long v0, v0, v28

    or-long/2addr v3, v0

    move/from16 v0, v30

    int-to-long v0, v0

    mul-long/2addr v0, v7

    ushr-long v0, v0, v27

    and-long v0, v0, v31

    shl-long v0, v0, v29

    or-long/2addr v3, v0

    move/from16 v0, v30

    int-to-long v0, v0

    mul-long/2addr v0, v9

    ushr-long v0, v0, v27

    and-long v0, v0, v31

    or-long/2addr v3, v0

    long-to-int v0, v3

    aput v0, p0, v34

    .line 56108
    add-int/lit8 v34, v34, 0x1

    .line 56109
    sub-long v5, v5, v19

    .line 56110
    sub-long v7, v7, v21

    .line 56111
    sub-long/2addr v9, v11

    .line 56112
    add-int v3, v23, v26

    sub-int v3, v3, v24

    .line 56113
    .local v0, "stack_start":I
    move/from16 v0, v26

    if-lt v3, v0, :cond_4

    sub-int v3, v3, v26

    .line 56114
    .local v1, "stack_i":I
    :cond_4
    aget v0, v2, v3

    ushr-int/lit8 v0, v0, 0x10

    and-int/lit16 v0, v0, 0xff

    int-to-long v0, v0

    sub-long v19, v19, v0

    .line 56115
    aget v0, v2, v3

    ushr-int/lit8 v0, v0, 0x8

    and-int/lit16 v0, v0, 0xff

    int-to-long v0, v0

    sub-long v21, v21, v0

    .line 56116
    aget v0, v2, v3

    and-int/lit16 v0, v0, 0xff

    int-to-long v0, v0

    sub-long/2addr v11, v0

    .line 56117
    move/from16 v1, v36

    move/from16 v0, v40

    if-ge v1, v0, :cond_5

    .line 56118
    add-int/lit8 v35, v35, 0x1

    .line 56119
    .end local p1    # "src_i":I
    .restart local v14    # "src_i":I
    add-int/lit8 v36, v36, 0x1

    .line 56120
    .end local p1
    .restart local v14    # "src_i":I
    :cond_5
    aget v0, p0, v35

    aput v0, v2, v3

    .line 56121
    aget v0, p0, v35

    ushr-int/lit8 v0, v0, 0x10

    and-int/lit16 v0, v0, 0xff

    .end local v0    # "stack_start":I
    .end local v1    # "stack_i":I
    .local p3, "stack_start":I
    .local p4, "stack_i":I
    int-to-long v0, v0

    add-long/2addr v13, v0

    .line 56122
    aget v0, p0, v35

    ushr-int/lit8 v0, v0, 0x8

    and-int/lit16 v0, v0, 0xff

    int-to-long v0, v0

    add-long/2addr v15, v0

    .line 56123
    aget v0, p0, v35

    and-int/lit16 v0, v0, 0xff

    int-to-long v0, v0

    add-long v17, v17, v0

    .line 56124
    add-long/2addr v5, v13

    .line 56125
    add-long/2addr v7, v15

    .line 56126
    add-long v9, v9, v17

    .line 56127
    add-int/lit8 v3, v23, 0x1

    .line 56128
    move/from16 v0, v26

    if-lt v3, v0, :cond_6

    const/4 v3, 0x0

    .line 56129
    .end local p4    # "stack_i":I
    .local v0, "stack_i":I
    :cond_6
    aget v0, v2, v3

    ushr-int/lit8 v0, v0, 0x10

    and-int/lit16 v0, v0, 0xff

    move/from16 v23, v3

    .end local v12    # "sp":I
    .end local v13    # "xp":I
    .local v15, "xp":I
    .local p1, "sp":I
    int-to-long v0, v0

    add-long v19, v19, v0

    .line 56130
    aget v0, v2, v3

    ushr-int/lit8 v0, v0, 0x8

    and-int/lit16 v0, v0, 0xff

    int-to-long v0, v0

    add-long v21, v21, v0

    .line 56131
    aget v0, v2, v3

    and-int/lit16 v0, v0, 0xff

    int-to-long v0, v0

    add-long/2addr v11, v0

    .line 56132
    aget v0, v2, v3

    ushr-int/lit8 v0, v0, 0x10

    and-int/lit16 v0, v0, 0xff

    int-to-long v0, v0

    sub-long/2addr v13, v0

    .line 56133
    aget v0, v2, v3

    ushr-int/lit8 v0, v0, 0x8

    and-int/lit16 v0, v0, 0xff

    int-to-long v0, v0

    sub-long/2addr v15, v0

    .line 56134
    aget v0, v2, v3

    and-int/lit16 v0, v0, 0xff

    int-to-long v0, v0

    sub-long v17, v17, v0

    .line 56135
    add-int/lit8 v33, v33, 0x1

    goto/16 :goto_3

    .line 56136
    .end local v0    # "stack_i":I
    .end local p1    # "sp":I
    .end local p2    # "i":I
    .end local p3    # "stack_start":I
    .local v11, "i":I
    .restart local v12    # "sp":I
    .restart local v13    # "xp":I
    .local v15, "y":I
    .end local v14    # "src_i":I
    .end local v15    # "y":I
    .local v11, "y":I
    .local p1, "src_i":I
    .restart local p2    # "i":I
    :cond_7
    add-int/lit8 v38, v38, 0x1

    const-wide/16 v11, 0x0

    .end local v11    # "y":I
    .restart local v15    # "y":I
    goto/16 :goto_0

    .line 56137
    .end local v11
    :cond_8
    const/4 v0, 0x2

    if-ne v3, v0, :cond_12

    .line 56138
    mul-int v38, p5, v25

    div-int v38, v38, p4

    .line 56139
    .local v0, "minX":I
    add-int/lit8 v37, p5, 0x1

    mul-int v37, v37, v25

    div-int v37, v37, p4

    .line 56140
    .local v1, "maxX":I
    .local v7, "x":I
    :goto_4
    move/from16 v1, v38

    move/from16 v0, v37

    if-ge v1, v0, :cond_12

    .line 56141
    const-wide/16 v16, 0x0

    .local v13, "sum_out_b":J
    move-wide/from16 v22, v16

    .local v21, "sum_out_g":J
    move-wide/from16 v20, v16

    .local v23, "sum_out_r":J
    move-wide/from16 v18, v16

    .local v25, "sum_in_b":J
    move-wide/from16 v14, v16

    .local v27, "sum_in_g":J
    move-wide/from16 v12, v16

    .local v29, "sum_in_r":J
    move-wide/from16 v10, v16

    .local v31, "sum_b":J
    move-wide/from16 v8, v16

    .local v33, "sum_g":J
    move-wide/from16 v6, v16

    .line 56142
    .local v35, "sum_r":J
    move/from16 v4, v38

    .line 56143
    .local v15, "src_i":I
    const/4 v3, 0x0

    .local v11, "i":I
    :goto_5
    move/from16 v0, v24

    if-gt v3, v0, :cond_9

    .line 56144
    .local v12, "stack_i":I
    aget v0, p0, v4

    aput v0, v2, v3

    .line 56145
    aget v0, p0, v4

    .end local v0    # "minX":I
    .local v37, "minX":I
    ushr-int/lit8 v0, v0, 0x10

    and-int/lit16 v0, v0, 0xff

    add-int/lit8 v1, v3, 0x1

    mul-int/2addr v0, v1

    .end local v1    # "maxX":I
    .local v18, "maxX":I
    int-to-long v0, v0

    add-long/2addr v6, v0

    .line 56146
    aget v0, p0, v4

    ushr-int/lit8 v0, v0, 0x8

    and-int/lit16 v0, v0, 0xff

    add-int/lit8 v1, v3, 0x1

    mul-int/2addr v0, v1

    int-to-long v0, v0

    add-long/2addr v8, v0

    .line 56147
    aget v0, p0, v4

    and-int/lit16 v0, v0, 0xff

    add-int/lit8 v1, v3, 0x1

    mul-int/2addr v0, v1

    int-to-long v0, v0

    add-long/2addr v10, v0

    .line 56148
    aget v0, p0, v4

    ushr-int/lit8 v0, v0, 0x10

    and-int/lit16 v0, v0, 0xff

    int-to-long v0, v0

    add-long v20, v20, v0

    .line 56149
    aget v0, p0, v4

    ushr-int/lit8 v0, v0, 0x8

    and-int/lit16 v0, v0, 0xff

    int-to-long v0, v0

    add-long v22, v22, v0

    .line 56150
    aget v0, p0, v4

    and-int/lit16 v0, v0, 0xff

    int-to-long v0, v0

    add-long v16, v16, v0

    .line 56151
    add-int/lit8 v3, v3, 0x1

    goto :goto_5

    .line 56152
    .end local v12    # "stack_i":I
    .end local v18    # "maxX":I
    .end local v37    # "minX":I
    .restart local v0    # "minX":I
    .restart local v1    # "maxX":I
    .end local v0    # "minX":I
    .end local v1    # "maxX":I
    .restart local v18    # "maxX":I
    .restart local v37    # "minX":I
    :cond_9
    const/4 v3, 0x1

    .end local v11    # "i":I
    .local v0, "i":I
    :goto_6
    move/from16 v0, v24

    if-gt v3, v0, :cond_b

    .line 56153
    move/from16 v0, v39

    if-gt v3, v0, :cond_a

    add-int v4, v4, v25

    .line 56154
    :cond_a
    add-int v1, v3, v24

    .line 56155
    .local v1, "stack_i":I
    aget v0, p0, v4

    aput v0, v2, v1

    .line 56156
    aget v0, p0, v4

    ushr-int/lit8 v0, v0, 0x10

    and-int/lit16 v0, v0, 0xff

    add-int/lit8 v1, v24, 0x1

    sub-int/2addr v1, v3

    mul-int/2addr v0, v1

    int-to-long v0, v0

    add-long/2addr v6, v0

    .line 56157
    aget v0, p0, v4

    ushr-int/lit8 v0, v0, 0x8

    and-int/lit16 v0, v0, 0xff

    add-int/lit8 v1, v24, 0x1

    sub-int/2addr v1, v3

    mul-int/2addr v0, v1

    int-to-long v0, v0

    add-long/2addr v8, v0

    .line 56158
    aget v0, p0, v4

    and-int/lit16 v0, v0, 0xff

    add-int/lit8 v1, v24, 0x1

    sub-int/2addr v1, v3

    mul-int/2addr v0, v1

    int-to-long v0, v0

    add-long/2addr v10, v0

    .line 56159
    aget v0, p0, v4

    ushr-int/lit8 v0, v0, 0x10

    and-int/lit16 v0, v0, 0xff

    int-to-long v0, v0

    add-long/2addr v12, v0

    .line 56160
    aget v0, p0, v4

    ushr-int/lit8 v0, v0, 0x8

    and-int/lit16 v0, v0, 0xff

    int-to-long v0, v0

    add-long/2addr v14, v0

    .line 56161
    aget v0, p0, v4

    and-int/lit16 v0, v0, 0xff

    int-to-long v0, v0

    add-long v18, v18, v0

    .line 56162
    add-int/lit8 v3, v3, 0x1

    goto :goto_6

    .line 56163
    .end local v1    # "stack_i":I
    :cond_b
    move/from16 v5, v24

    .line 56164
    .local v1, "sp":I
    move/from16 v36, v24

    .line 56165
    .local v11, "yp":I
    move/from16 v1, v36

    move/from16 v0, v39

    if-le v1, v0, :cond_c

    move/from16 v36, v39

    .line 56166
    :cond_c
    mul-int v35, v36, v25

    add-int v35, v35, v38

    .line 56167
    .end local v15    # "src_i":I
    .local v12, "src_i":I
    move/from16 v34, v38

    .line 56168
    .local v15, "dst_i":I
    const/16 v33, 0x0

    .local v0, "y":I
    .local v38, "i":I
    :goto_7
    move/from16 v1, v33

    move/from16 v0, v41

    if-ge v1, v0, :cond_10

    .line 56169
    aget v0, p0, v34

    const/high16 v1, -0x1000000

    and-int/2addr v0, v1

    .end local v4    # "wm":I
    .local v40, "wm":I
    int-to-long v3, v0

    .end local v11    # "yp":I
    .end local v12    # "src_i":I
    .local p0, "yp":I
    .restart local p1    # "src_i":I
    move/from16 v0, v30

    int-to-long v0, v0

    mul-long/2addr v0, v6

    ushr-long v0, v0, v27

    const-wide/16 v31, 0xff

    and-long v0, v0, v31

    shl-long v0, v0, v28

    or-long/2addr v3, v0

    move/from16 v0, v30

    int-to-long v0, v0

    mul-long/2addr v0, v8

    ushr-long v0, v0, v27

    and-long v0, v0, v31

    shl-long v0, v0, v29

    or-long/2addr v3, v0

    move/from16 v0, v30

    int-to-long v0, v0

    mul-long/2addr v0, v10

    ushr-long v0, v0, v27

    and-long v0, v0, v31

    or-long/2addr v3, v0

    long-to-int v0, v3

    aput v0, p0, v34

    .line 56170
    add-int v34, v34, v25

    .line 56171
    sub-long v6, v6, v20

    .line 56172
    sub-long v8, v8, v22

    .line 56173
    sub-long v10, v10, v16

    .line 56174
    add-int v3, v5, v26

    sub-int v3, v3, v24

    .line 56175
    .local v3, "stack_start":I
    move/from16 v0, v26

    if-lt v3, v0, :cond_d

    sub-int v3, v3, v26

    .line 56176
    .local v4, "stack_i":I
    :cond_d
    aget v0, v2, v3

    ushr-int/lit8 v0, v0, 0x10

    and-int/lit16 v0, v0, 0xff

    int-to-long v0, v0

    sub-long v20, v20, v0

    .line 56177
    aget v0, v2, v3

    ushr-int/lit8 v0, v0, 0x8

    and-int/lit16 v0, v0, 0xff

    int-to-long v0, v0

    sub-long v22, v22, v0

    .line 56178
    aget v0, v2, v3

    and-int/lit16 v0, v0, 0xff

    int-to-long v0, v0

    sub-long v16, v16, v0

    .line 56179
    .end local p0    # "yp":I
    .restart local v11    # "yp":I
    move/from16 v1, v36

    move/from16 v0, v39

    if-ge v1, v0, :cond_e

    .line 56180
    add-int v35, v35, v25

    .line 56181
    .end local p1    # "src_i":I
    .restart local v12    # "src_i":I
    add-int/lit8 v36, v36, 0x1

    .line 56182
    .end local p1
    .restart local v12    # "src_i":I
    :cond_e
    aget v0, p0, v35

    aput v0, v2, v3

    .line 56183
    aget v0, p0, v35

    ushr-int/lit8 v0, v0, 0x10

    and-int/lit16 v0, v0, 0xff

    .end local v3    # "stack_start":I
    .local p0, "stack_start":I
    int-to-long v0, v0

    add-long/2addr v12, v0

    .line 56184
    aget v0, p0, v35

    ushr-int/lit8 v0, v0, 0x8

    and-int/lit16 v0, v0, 0xff

    int-to-long v0, v0

    add-long/2addr v14, v0

    .line 56185
    aget v0, p0, v35

    and-int/lit16 v0, v0, 0xff

    int-to-long v0, v0

    add-long v18, v18, v0

    .line 56186
    add-long/2addr v6, v12

    .line 56187
    add-long/2addr v8, v14

    .line 56188
    add-long v10, v10, v18

    .line 56189
    add-int/lit8 v5, v5, 0x1

    .line 56190
    move/from16 v0, v26

    if-lt v5, v0, :cond_f

    const/4 v5, 0x0

    .line 56191
    .end local v4    # "stack_i":I
    .local v2, "stack_i":I
    :cond_f
    aget v0, v2, v5

    ushr-int/lit8 v0, v0, 0x10

    and-int/lit16 v0, v0, 0xff

    int-to-long v0, v0

    add-long v20, v20, v0

    .line 56192
    aget v0, v2, v5

    ushr-int/lit8 v0, v0, 0x8

    and-int/lit16 v0, v0, 0xff

    int-to-long v0, v0

    add-long v22, v22, v0

    .line 56193
    aget v0, v2, v5

    and-int/lit16 v0, v0, 0xff

    int-to-long v0, v0

    add-long v16, v16, v0

    .line 56194
    aget v0, v2, v5

    ushr-int/lit8 v0, v0, 0x10

    and-int/lit16 v0, v0, 0xff

    int-to-long v0, v0

    sub-long/2addr v12, v0

    .line 56195
    aget v0, v2, v5

    ushr-int/lit8 v0, v0, 0x8

    and-int/lit16 v0, v0, 0xff

    int-to-long v0, v0

    sub-long/2addr v14, v0

    .line 56196
    aget v0, v2, v5

    and-int/lit16 v0, v0, 0xff

    int-to-long v0, v0

    sub-long v18, v18, v0

    .line 56197
    add-int/lit8 v33, v33, 0x1

    goto/16 :goto_7

    .line 56198
    .end local v2    # "stack_i":I
    .end local v40    # "wm":I
    .end local p0    # "stack_start":I
    .local v4, "wm":I
    .end local v4    # "wm":I
    .end local v12    # "src_i":I
    .restart local v40    # "wm":I
    .restart local p1    # "src_i":I
    :cond_10
    add-int/lit8 v38, v38, 0x1

    sget-object v3, Lcom/facebook/ads/redexgen/X/Ua;->A00:[Ljava/lang/String;

    const/4 v0, 0x4

    aget-object v1, v3, v0

    const/4 v0, 0x0

    aget-object v3, v3, v0

    const/16 v0, 0x18

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    invoke-virtual {v3, v0}, Ljava/lang/String;->charAt(I)C

    move-result v0

    if-eq v1, v0, :cond_11

    sget-object v3, Lcom/facebook/ads/redexgen/X/Ua;->A00:[Ljava/lang/String;

    const-string v1, "dRrOf81uK3m7JlCVGnAp7HBN8Ud450kD"

    const/4 v0, 0x4

    aput-object v1, v3, v0

    const-string v1, "ERfsWS88AUosonNdTmqfcWCTdCaDCKBU"

    const/4 v0, 0x0

    aput-object v1, v3, v0

    goto/16 :goto_4

    :cond_11
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    .line 56199
    .end local v4
    .restart local v40    # "wm":I
    :cond_12
    return-void
.end method

.method public static synthetic A01([IIIIIII)V
    .locals 0

    .line 56200
    invoke-static/range {p0 .. p6}, Lcom/facebook/ads/redexgen/X/Ua;->A00([IIIIIII)V

    return-void
.end method


# virtual methods
.method public final A02(Landroid/graphics/Bitmap;F)Landroid/graphics/Bitmap;
    .locals 16

    .line 56201
    move-object/from16 v4, p1

    invoke-virtual {v4}, Landroid/graphics/Bitmap;->getWidth()I

    move-result v7

    .line 56202
    .local v10, "w":I
    invoke-virtual {v4}, Landroid/graphics/Bitmap;->getHeight()I

    move-result v11

    .line 56203
    .local v11, "h":I
    .local v2, "currentPixels":[I
    mul-int v0, v7, v11

    :try_start_0
    new-array v5, v0, [I
    :try_end_0
    .catch Ljava/lang/OutOfMemoryError; {:try_start_0 .. :try_end_0} :catch_3

    .line 56204
    .end local v2    # "currentPixels":[I
    .local v13, "currentPixels":[I
    const/4 v6, 0x0

    const/4 v8, 0x0

    const/4 v9, 0x0

    move v10, v7

    invoke-virtual/range {v4 .. v11}, Landroid/graphics/Bitmap;->getPixels([IIIIIII)V

    .line 56205
    sget v13, Lcom/facebook/ads/redexgen/X/Lz;->A00:I

    .line 56206
    .local v14, "cores":I
    new-instance v3, Ljava/util/ArrayList;

    invoke-direct {v3, v13}, Ljava/util/ArrayList;-><init>(I)V

    .line 56207
    .local v15, "horizontal":Ljava/util/ArrayList;, "Ljava/util/ArrayList<Lcom/facebook/ads/internal/util/image/thirdparty/JavaBlurProcess$BlurTask;>;"
    new-instance v2, Ljava/util/ArrayList;

    invoke-direct {v2, v13}, Ljava/util/ArrayList;-><init>(I)V

    .line 56208
    .local v9, "vertical":Ljava/util/ArrayList;, "Ljava/util/ArrayList<Lcom/facebook/ads/internal/util/image/thirdparty/JavaBlurProcess$BlurTask;>;"
    const/4 v1, 0x0

    .local v0, "i":I
    :goto_0
    if-ge v1, v13, :cond_0

    .line 56209
    new-instance v8, Lcom/facebook/ads/redexgen/X/Ly;

    move/from16 v4, p2

    float-to-int v12, v4

    const/4 v15, 0x1

    move-object v0, v8

    .end local v9    # "vertical":Ljava/util/ArrayList;, "Ljava/util/ArrayList<Lcom/facebook/ads/internal/util/image/thirdparty/JavaBlurProcess$BlurTask;>;"
    .local p1, "vertical":Ljava/util/ArrayList;, "Ljava/util/ArrayList<Lcom/facebook/ads/internal/util/image/thirdparty/JavaBlurProcess$BlurTask;>;"
    move-object v9, v5

    move v10, v7

    move v11, v11

    move v14, v1

    invoke-direct/range {v8 .. v15}, Lcom/facebook/ads/redexgen/X/Ly;-><init>([IIIIIII)V

    invoke-virtual {v3, v0}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 56210
    new-instance v8, Lcom/facebook/ads/redexgen/X/Ly;

    float-to-int v12, v4

    const/4 v15, 0x2

    move-object v9, v5

    move v10, v7

    move v11, v11

    move v14, v1

    invoke-direct/range {v8 .. v15}, Lcom/facebook/ads/redexgen/X/Ly;-><init>([IIIIIII)V

    .end local p1    # "vertical":Ljava/util/ArrayList;, "Ljava/util/ArrayList<Lcom/facebook/ads/internal/util/image/thirdparty/JavaBlurProcess$BlurTask;>;"
    .local v2, "vertical":Ljava/util/ArrayList;, "Ljava/util/ArrayList<Lcom/facebook/ads/internal/util/image/thirdparty/JavaBlurProcess$BlurTask;>;"
    invoke-virtual {v2, v8}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 56211
    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    .line 56212
    .end local v0    # "i":I
    .end local v9
    .restart local v2    # "vertical":Ljava/util/ArrayList;, "Ljava/util/ArrayList<Lcom/facebook/ads/internal/util/image/thirdparty/JavaBlurProcess$BlurTask;>;"
    :cond_0
    :try_start_1
    sget-object v0, Lcom/facebook/ads/redexgen/X/Lz;->A01:Ljava/util/concurrent/ExecutorService;

    invoke-interface {v0, v3}, Ljava/util/concurrent/ExecutorService;->invokeAll(Ljava/util/Collection;)Ljava/util/List;
    :try_end_1
    .catch Ljava/lang/InterruptedException; {:try_start_1 .. :try_end_1} :catch_2

    .line 56213
    :try_start_2
    sget-object v0, Lcom/facebook/ads/redexgen/X/Lz;->A01:Ljava/util/concurrent/ExecutorService;

    invoke-interface {v0, v2}, Ljava/util/concurrent/ExecutorService;->invokeAll(Ljava/util/Collection;)Ljava/util/List;
    :try_end_2
    .catch Ljava/lang/InterruptedException; {:try_start_2 .. :try_end_2} :catch_1

    .line 56214
    :try_start_3
    sget-object v0, Landroid/graphics/Bitmap$Config;->ARGB_8888:Landroid/graphics/Bitmap$Config;

    invoke-static {v5, v7, v11, v0}, Landroid/graphics/Bitmap;->createBitmap([IIILandroid/graphics/Bitmap$Config;)Landroid/graphics/Bitmap;

    move-result-object v0

    return-object v0
    :try_end_3
    .catch Ljava/lang/OutOfMemoryError; {:try_start_3 .. :try_end_3} :catch_0

    .line 56215
    .local v0, "e":Ljava/lang/OutOfMemoryError;
    :catch_0
    const/4 v0, 0x0

    return-object v0

    .line 56216
    .end local v0    # "e":Ljava/lang/OutOfMemoryError;
    :catch_1
    const/4 v0, 0x0

    .line 56217
    .local v0, "e":Ljava/lang/InterruptedException;
    return-object v0

    .line 56218
    .end local v0    # "e":Ljava/lang/InterruptedException;
    :catch_2
    const/4 v0, 0x0

    .line 56219
    .restart local v0    # "e":Ljava/lang/InterruptedException;
    return-object v0

    .line 56220
    .end local v0    # "e":Ljava/lang/InterruptedException;
    .end local v13    # "currentPixels":[I
    .end local v14    # "cores":I
    .end local v15    # "horizontal":Ljava/util/ArrayList;, "Ljava/util/ArrayList<Lcom/facebook/ads/internal/util/image/thirdparty/JavaBlurProcess$BlurTask;>;"
    .local v2, "currentPixels":[I
    :catch_3
    const/4 v0, 0x0

    .line 56221
    .local v0, "e":Ljava/lang/OutOfMemoryError;
    return-object v0
.end method
