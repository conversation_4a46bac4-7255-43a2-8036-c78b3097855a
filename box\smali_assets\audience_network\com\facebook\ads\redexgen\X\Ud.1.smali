.class public final Lcom/facebook/ads/redexgen/X/Ud;
.super Lcom/facebook/ads/redexgen/X/KT;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/facebook/ads/redexgen/X/Lb;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field public final synthetic A00:Lcom/facebook/ads/redexgen/X/Lb;


# direct methods
.method public constructor <init>(Lcom/facebook/ads/redexgen/X/Lb;)V
    .locals 0

    .line 56229
    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/Ud;->A00:Lcom/facebook/ads/redexgen/X/Lb;

    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/KT;-><init>()V

    return-void
.end method


# virtual methods
.method public final A06()V
    .locals 2

    .line 56230
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/Ud;->A00:Lcom/facebook/ads/redexgen/X/Lb;

    const/4 v0, 0x0

    invoke-static {v1, v0}, Lcom/facebook/ads/redexgen/X/Lb;->A01(Lcom/facebook/ads/redexgen/X/Lb;Z)V

    .line 56231
    return-void
.end method
