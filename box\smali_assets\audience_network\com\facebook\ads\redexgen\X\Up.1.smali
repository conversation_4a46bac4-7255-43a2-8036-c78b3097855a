.class public final Lcom/facebook/ads/redexgen/X/Up;
.super Lcom/facebook/ads/redexgen/X/K7;
.source ""


# direct methods
.method public constructor <init>(Lcom/facebook/ads/redexgen/X/8S;Ljava/lang/String;)V
    .locals 1

    .line 56338
    sget-object v0, Lcom/facebook/ads/redexgen/X/K6;->A02:Lcom/facebook/ads/redexgen/X/K6;

    invoke-direct {p0, v0, p1, p2}, Lcom/facebook/ads/redexgen/X/K7;-><init>(Lcom/facebook/ads/redexgen/X/K6;Lcom/facebook/ads/redexgen/X/8S;Ljava/lang/String;)V

    .line 56339
    return-void
.end method


# virtual methods
.method public final bridge synthetic A00()Lcom/facebook/ads/redexgen/X/8S;
    .locals 1

    .line 56340
    invoke-super {p0}, Lcom/facebook/ads/redexgen/X/K7;->A00()Lcom/facebook/ads/redexgen/X/8S;

    move-result-object v0

    return-object v0
.end method
