.class public final Lcom/facebook/ads/redexgen/X/Uq;
.super Lcom/facebook/ads/redexgen/X/KT;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/facebook/ads/redexgen/X/K4;->A0D(Lcom/facebook/ads/redexgen/X/Jb;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field public final synthetic A00:Lcom/facebook/ads/redexgen/X/Jb;

.field public final synthetic A01:Lcom/facebook/ads/redexgen/X/K4;


# direct methods
.method public constructor <init>(Lcom/facebook/ads/redexgen/X/K4;Lcom/facebook/ads/redexgen/X/Jb;)V
    .locals 0

    .line 56341
    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/Uq;->A01:Lcom/facebook/ads/redexgen/X/K4;

    iput-object p2, p0, Lcom/facebook/ads/redexgen/X/Uq;->A00:Lcom/facebook/ads/redexgen/X/Jb;

    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/KT;-><init>()V

    return-void
.end method


# virtual methods
.method public final A06()V
    .locals 2

    .line 56342
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/Uq;->A01:Lcom/facebook/ads/redexgen/X/K4;

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Uq;->A00:Lcom/facebook/ads/redexgen/X/Jb;

    invoke-static {v1, v0}, Lcom/facebook/ads/redexgen/X/K4;->A0F(Lcom/facebook/ads/redexgen/X/K4;Lcom/facebook/ads/redexgen/X/Jb;)V

    .line 56343
    return-void
.end method
