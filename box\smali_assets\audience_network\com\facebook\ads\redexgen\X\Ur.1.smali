.class public final Lcom/facebook/ads/redexgen/X/Ur;
.super Lcom/facebook/ads/redexgen/X/KT;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/facebook/ads/redexgen/X/K4;->A0L(Lcom/facebook/ads/redexgen/X/Up;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field public final synthetic A00:Lcom/facebook/ads/redexgen/X/K4;

.field public final synthetic A01:Lcom/facebook/ads/redexgen/X/Up;


# direct methods
.method public constructor <init>(Lcom/facebook/ads/redexgen/X/K4;Lcom/facebook/ads/redexgen/X/Up;)V
    .locals 0

    .line 56344
    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/Ur;->A00:Lcom/facebook/ads/redexgen/X/K4;

    iput-object p2, p0, Lcom/facebook/ads/redexgen/X/Ur;->A01:Lcom/facebook/ads/redexgen/X/Up;

    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/KT;-><init>()V

    return-void
.end method


# virtual methods
.method public final A06()V
    .locals 2

    .line 56345
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/Ur;->A00:Lcom/facebook/ads/redexgen/X/K4;

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Ur;->A01:Lcom/facebook/ads/redexgen/X/Up;

    invoke-static {v1, v0}, Lcom/facebook/ads/redexgen/X/K4;->A0H(Lcom/facebook/ads/redexgen/X/K4;Lcom/facebook/ads/redexgen/X/Up;)V

    .line 56346
    return-void
.end method
