.class public final Lcom/facebook/ads/redexgen/X/V2;
.super Ljava/lang/Object;
.source ""

# interfaces
.implements Lcom/facebook/ads/Ad;
.implements Lcom/facebook/ads/internal/api/NativeAdBaseApi;
.implements Lcom/facebook/ads/internal/context/Repairable;
.implements Lcom/facebook/ads/redexgen/X/JX;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/facebook/ads/redexgen/X/V4;,
        Lcom/facebook/ads/redexgen/X/V3;,
        Lcom/facebook/ads/internal/mirror/InternalNativeAd$NativeViewabilityCheckerListener;,
        Lcom/facebook/ads/redexgen/X/JO;
    }
.end annotation


# static fields
.field public static A0k:Lcom/facebook/ads/redexgen/X/6c;

.field public static A0l:[B

.field public static A0m:[Ljava/lang/String;

.field public static final A0n:Ljava/lang/String;

.field public static final A0o:Ljava/util/WeakHashMap;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/WeakHashMap<",
            "Landroid/view/View;",
            "Ljava/lang/ref/WeakReference<",
            "Lcom/facebook/ads/redexgen/X/V2;",
            ">;>;"
        }
    .end annotation
.end field


# instance fields
.field public A00:J

.field public A01:Landroid/graphics/drawable/Drawable;

.field public A02:Landroid/view/View$OnTouchListener;

.field public A03:Landroid/view/View;

.field public A04:Landroid/view/View;

.field public A05:Landroid/view/View;

.field public A06:Landroid/view/View;

.field public A07:Lcom/facebook/ads/NativeAdLayout;

.field public A08:Lcom/facebook/ads/redexgen/X/12;

.field public A09:Lcom/facebook/ads/redexgen/X/bA;

.field public A0A:Lcom/facebook/ads/redexgen/X/FO;

.field public A0B:Lcom/facebook/ads/redexgen/X/ag;

.field public A0C:Lcom/facebook/ads/redexgen/X/6i;

.field public A0D:Lcom/facebook/ads/redexgen/X/8T;

.field public A0E:Lcom/facebook/ads/redexgen/X/JL;

.field public A0F:Lcom/facebook/ads/redexgen/X/V4;

.field public A0G:Lcom/facebook/ads/redexgen/X/V1;

.field public A0H:Lcom/facebook/ads/redexgen/X/JR;

.field public A0I:Lcom/facebook/ads/redexgen/X/JS;

.field public A0J:Lcom/facebook/ads/redexgen/X/Jg;

.field public A0K:Lcom/facebook/ads/redexgen/X/Mi;

.field public A0L:Lcom/facebook/ads/redexgen/X/Ng;

.field public A0M:Lcom/facebook/ads/redexgen/X/PB;

.field public A0N:Lcom/facebook/ads/redexgen/X/Px;

.field public A0O:Lcom/facebook/ads/redexgen/X/RD;

.field public A0P:Lcom/facebook/ads/redexgen/X/RD;

.field public A0Q:Lcom/facebook/ads/redexgen/X/RE;

.field public A0R:Lcom/facebook/ads/redexgen/X/RE;

.field public A0S:Ljava/lang/String;

.field public A0T:Ljava/lang/String;

.field public A0U:Ljava/lang/ref/WeakReference;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/lang/ref/WeakReference<",
            "Lcom/facebook/ads/redexgen/X/ZX;",
            ">;"
        }
    .end annotation
.end field

.field public A0V:Ljava/lang/ref/WeakReference;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/lang/ref/WeakReference<",
            "Lcom/facebook/ads/redexgen/X/RD;",
            ">;"
        }
    .end annotation
.end field

.field public A0W:Z

.field public A0X:Z

.field public A0Y:Z

.field public A0Z:Z

.field public A0a:Lcom/facebook/ads/redexgen/X/bK;

.field public final A0b:Lcom/facebook/ads/redexgen/X/6c;

.field public final A0c:Lcom/facebook/ads/redexgen/X/Yn;

.field public final A0d:Lcom/facebook/ads/redexgen/X/JO;

.field public final A0e:Lcom/facebook/ads/redexgen/X/JZ;

.field public final A0f:Lcom/facebook/ads/redexgen/X/Lg;

.field public final A0g:Ljava/lang/String;

.field public final A0h:Ljava/lang/String;

.field public final A0i:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Landroid/view/View;",
            ">;"
        }
    .end annotation
.end field

.field public volatile A0j:Z


# direct methods
.method public static constructor <clinit>()V
    .locals 3

    .line 2453
    const/16 v0, 0x8

    new-array v2, v0, [Ljava/lang/String;

    const/4 v1, 0x0

    const-string v0, "Bl7Z7O36thNMsAQG86cT5hXAswukL2TU"

    aput-object v0, v2, v1

    const/4 v1, 0x1

    const-string v0, "w0l3kgVUfrf8f1EzcfbBJ9JqF1JIQyF1"

    aput-object v0, v2, v1

    const/4 v1, 0x2

    const-string v0, "3FYeVZ4P9Hva0brxRfDXnSutu97odoaf"

    aput-object v0, v2, v1

    const/4 v1, 0x3

    const-string v0, "TkemScCQ72wM3yeu7V5OcaIZTlgQuG0T"

    aput-object v0, v2, v1

    const/4 v1, 0x4

    const-string v0, "aV5nfdXEnoj7uvLi"

    aput-object v0, v2, v1

    const/4 v1, 0x5

    const-string v0, "brDFWAS69jf159nGVdOYVvf"

    aput-object v0, v2, v1

    const/4 v1, 0x6

    const-string v0, "WGIwYSb5Wjcv"

    aput-object v0, v2, v1

    const/4 v1, 0x7

    const-string v0, "W7skGFSknqdZoaqu210O1mF"

    aput-object v0, v2, v1

    sput-object v2, Lcom/facebook/ads/redexgen/X/V2;->A0m:[Ljava/lang/String;

    invoke-static {}, Lcom/facebook/ads/redexgen/X/V2;->A0d()V

    const-class v0, Lcom/facebook/ads/redexgen/X/V2;

    invoke-virtual {v0}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    move-result-object v0

    sput-object v0, Lcom/facebook/ads/redexgen/X/V2;->A0n:Ljava/lang/String;

    .line 2454
    new-instance v0, Ljava/util/WeakHashMap;

    invoke-direct {v0}, Ljava/util/WeakHashMap;-><init>()V

    sput-object v0, Lcom/facebook/ads/redexgen/X/V2;->A0o:Ljava/util/WeakHashMap;

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Ljava/lang/String;Lcom/facebook/ads/redexgen/X/JO;Z)V
    .locals 2

    .line 56484
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 56485
    invoke-static {}, Ljava/util/UUID;->randomUUID()Ljava/util/UUID;

    move-result-object v0

    invoke-virtual {v0}, Ljava/util/UUID;->toString()Ljava/lang/String;

    move-result-object v0

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0h:Ljava/lang/String;

    .line 56486
    sget-object v0, Lcom/facebook/ads/redexgen/X/Jg;->A05:Lcom/facebook/ads/redexgen/X/Jg;

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0J:Lcom/facebook/ads/redexgen/X/Jg;

    .line 56487
    sget-object v0, Lcom/facebook/ads/redexgen/X/JL;->A04:Lcom/facebook/ads/redexgen/X/JL;

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0E:Lcom/facebook/ads/redexgen/X/JL;

    .line 56488
    sget-object v0, Lcom/facebook/ads/redexgen/X/12;->A03:Lcom/facebook/ads/redexgen/X/12;

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A08:Lcom/facebook/ads/redexgen/X/12;

    .line 56489
    const/4 v1, 0x0

    new-instance v0, Ljava/lang/ref/WeakReference;

    invoke-direct {v0, v1}, Ljava/lang/ref/WeakReference;-><init>(Ljava/lang/Object;)V

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0U:Ljava/lang/ref/WeakReference;

    .line 56490
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0i:Ljava/util/List;

    .line 56491
    new-instance v0, Lcom/facebook/ads/redexgen/X/Lg;

    invoke-direct {v0}, Lcom/facebook/ads/redexgen/X/Lg;-><init>()V

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0f:Lcom/facebook/ads/redexgen/X/Lg;

    .line 56492
    const/4 v0, 0x0

    iput-boolean v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0Z:Z

    .line 56493
    iput-boolean v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0Y:Z

    .line 56494
    const-wide/16 v0, -0x1

    iput-wide v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A00:J

    .line 56495
    instance-of v0, p1, Lcom/facebook/ads/redexgen/X/Yn;

    if-eqz v0, :cond_1

    .line 56496
    move-object v0, p1

    check-cast v0, Lcom/facebook/ads/redexgen/X/Yn;

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0c:Lcom/facebook/ads/redexgen/X/Yn;

    .line 56497
    :goto_0
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0c:Lcom/facebook/ads/redexgen/X/Yn;

    invoke-virtual {v0, p0}, Lcom/facebook/ads/redexgen/X/Yn;->A0L(Lcom/facebook/ads/internal/context/Repairable;)V

    .line 56498
    iput-object p2, p0, Lcom/facebook/ads/redexgen/X/V2;->A0g:Ljava/lang/String;

    .line 56499
    iput-object p3, p0, Lcom/facebook/ads/redexgen/X/V2;->A0d:Lcom/facebook/ads/redexgen/X/JO;

    .line 56500
    sget-object v0, Lcom/facebook/ads/redexgen/X/V2;->A0k:Lcom/facebook/ads/redexgen/X/6c;

    if-eqz v0, :cond_0

    .line 56501
    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0b:Lcom/facebook/ads/redexgen/X/6c;

    .line 56502
    :goto_1
    new-instance v0, Landroid/view/View;

    invoke-direct {v0, p1}, Landroid/view/View;-><init>(Landroid/content/Context;)V

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A05:Landroid/view/View;

    .line 56503
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/V2;->A0c:Lcom/facebook/ads/redexgen/X/Yn;

    new-instance v0, Lcom/facebook/ads/redexgen/X/JZ;

    invoke-direct {v0, v1, p0}, Lcom/facebook/ads/redexgen/X/JZ;-><init>(Lcom/facebook/ads/redexgen/X/7f;Lcom/facebook/ads/redexgen/X/JX;)V

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0e:Lcom/facebook/ads/redexgen/X/JZ;

    .line 56504
    return-void

    .line 56505
    :cond_0
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/V2;->A0c:Lcom/facebook/ads/redexgen/X/Yn;

    new-instance v0, Lcom/facebook/ads/redexgen/X/6c;

    invoke-direct {v0, v1}, Lcom/facebook/ads/redexgen/X/6c;-><init>(Lcom/facebook/ads/redexgen/X/7f;)V

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0b:Lcom/facebook/ads/redexgen/X/6c;

    goto :goto_1

    .line 56506
    :cond_1
    if-nez p4, :cond_2

    .line 56507
    invoke-static {p1}, Lcom/facebook/ads/redexgen/X/5c;->A04(Landroid/content/Context;)Lcom/facebook/ads/redexgen/X/Yn;

    move-result-object v0

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0c:Lcom/facebook/ads/redexgen/X/Yn;

    goto :goto_0

    .line 56508
    :cond_2
    invoke-static {p1}, Lcom/facebook/ads/redexgen/X/5c;->A03(Landroid/content/Context;)Lcom/facebook/ads/redexgen/X/Yn;

    move-result-object v0

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0c:Lcom/facebook/ads/redexgen/X/Yn;

    goto :goto_0
.end method

.method public constructor <init>(Lcom/facebook/ads/redexgen/X/V2;)V
    .locals 4

    .line 56509
    iget-object v3, p1, Lcom/facebook/ads/redexgen/X/V2;->A0c:Lcom/facebook/ads/redexgen/X/Yn;

    iget-object v2, p1, Lcom/facebook/ads/redexgen/X/V2;->A0d:Lcom/facebook/ads/redexgen/X/JO;

    const/4 v0, 0x0

    const/4 v1, 0x1

    invoke-direct {p0, v3, v0, v2, v1}, Lcom/facebook/ads/redexgen/X/V2;-><init>(Landroid/content/Context;Ljava/lang/String;Lcom/facebook/ads/redexgen/X/JO;Z)V

    .line 56510
    iget-object v0, p1, Lcom/facebook/ads/redexgen/X/V2;->A0D:Lcom/facebook/ads/redexgen/X/8T;

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0D:Lcom/facebook/ads/redexgen/X/8T;

    .line 56511
    iget-object v0, p1, Lcom/facebook/ads/redexgen/X/V2;->A0a:Lcom/facebook/ads/redexgen/X/bK;

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0a:Lcom/facebook/ads/redexgen/X/bK;

    .line 56512
    iget-object v0, p1, Lcom/facebook/ads/redexgen/X/V2;->A0B:Lcom/facebook/ads/redexgen/X/ag;

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0B:Lcom/facebook/ads/redexgen/X/ag;

    .line 56513
    iput-boolean v1, p0, Lcom/facebook/ads/redexgen/X/V2;->A0j:Z

    .line 56514
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/V2;->A0c:Lcom/facebook/ads/redexgen/X/Yn;

    new-instance v0, Landroid/view/View;

    invoke-direct {v0, v1}, Landroid/view/View;-><init>(Landroid/content/Context;)V

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A05:Landroid/view/View;

    .line 56515
    return-void
.end method

.method public constructor <init>(Lcom/facebook/ads/redexgen/X/Yn;Lcom/facebook/ads/redexgen/X/bK;Lcom/facebook/ads/redexgen/X/8T;Lcom/facebook/ads/redexgen/X/JO;)V
    .locals 2

    .line 56516
    const/4 v1, 0x0

    const/4 v0, 0x1

    invoke-direct {p0, p1, v1, p4, v0}, Lcom/facebook/ads/redexgen/X/V2;-><init>(Landroid/content/Context;Ljava/lang/String;Lcom/facebook/ads/redexgen/X/JO;Z)V

    .line 56517
    iput-object p2, p0, Lcom/facebook/ads/redexgen/X/V2;->A0a:Lcom/facebook/ads/redexgen/X/bK;

    .line 56518
    iput-object p3, p0, Lcom/facebook/ads/redexgen/X/V2;->A0D:Lcom/facebook/ads/redexgen/X/8T;

    .line 56519
    iput-boolean v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0j:Z

    .line 56520
    new-instance v0, Landroid/view/View;

    invoke-direct {v0, p1}, Landroid/view/View;-><init>(Landroid/content/Context;)V

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A05:Landroid/view/View;

    .line 56521
    return-void
.end method

.method public constructor <init>(Lcom/facebook/ads/redexgen/X/Yn;Lcom/facebook/ads/redexgen/X/bK;Lcom/facebook/ads/redexgen/X/8T;Lcom/facebook/ads/redexgen/X/JO;Lcom/facebook/ads/redexgen/X/ag;)V
    .locals 0

    .line 56522
    invoke-direct {p0, p1, p2, p3, p4}, Lcom/facebook/ads/redexgen/X/V2;-><init>(Lcom/facebook/ads/redexgen/X/Yn;Lcom/facebook/ads/redexgen/X/bK;Lcom/facebook/ads/redexgen/X/8T;Lcom/facebook/ads/redexgen/X/JO;)V

    .line 56523
    iput-object p5, p0, Lcom/facebook/ads/redexgen/X/V2;->A0B:Lcom/facebook/ads/redexgen/X/ag;

    .line 56524
    return-void
.end method

.method private A00()I
    .locals 2

    .line 56525
    const/4 v1, 0x1

    .line 56526
    .local v0, "viewabilityThreshold":I
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0D:Lcom/facebook/ads/redexgen/X/8T;

    if-eqz v0, :cond_1

    .line 56527
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/8T;->A04()I

    move-result v1

    .line 56528
    :cond_0
    :goto_0
    return v1

    .line 56529
    :cond_1
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0A:Lcom/facebook/ads/redexgen/X/FO;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/am;->A0H()Lcom/facebook/ads/redexgen/X/8T;

    move-result-object v0

    if-eqz v0, :cond_0

    .line 56530
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0A:Lcom/facebook/ads/redexgen/X/FO;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/am;->A0H()Lcom/facebook/ads/redexgen/X/8T;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/8T;->A04()I

    move-result v1

    goto :goto_0
.end method

.method private A01()I
    .locals 4

    .line 56531
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0D:Lcom/facebook/ads/redexgen/X/8T;

    if-eqz v0, :cond_0

    .line 56532
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/8T;->A07()I

    move-result v0

    return v0

    .line 56533
    :cond_0
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0a:Lcom/facebook/ads/redexgen/X/bK;

    if-eqz v0, :cond_2

    .line 56534
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/bK;->A0C()I

    move-result v3

    sget-object v2, Lcom/facebook/ads/redexgen/X/V2;->A0m:[Ljava/lang/String;

    const/4 v0, 0x1

    aget-object v1, v2, v0

    const/4 v0, 0x3

    aget-object v2, v2, v0

    const/16 v0, 0x10

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    invoke-virtual {v2, v0}, Ljava/lang/String;->charAt(I)C

    move-result v0

    if-eq v1, v0, :cond_1

    sget-object v2, Lcom/facebook/ads/redexgen/X/V2;->A0m:[Ljava/lang/String;

    const-string v1, "EUscbuPuA5cZxxPx7y6ncGc"

    const/4 v0, 0x7

    aput-object v1, v2, v0

    const-string v1, "07NrZ6juCFttxvE5hiFlF8c"

    const/4 v0, 0x5

    aput-object v1, v2, v0

    return v3

    :cond_1
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    .line 56535
    :cond_2
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0A:Lcom/facebook/ads/redexgen/X/FO;

    if-eqz v0, :cond_3

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/am;->A0H()Lcom/facebook/ads/redexgen/X/8T;

    move-result-object v0

    if-eqz v0, :cond_3

    .line 56536
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0A:Lcom/facebook/ads/redexgen/X/FO;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/am;->A0H()Lcom/facebook/ads/redexgen/X/8T;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/8T;->A07()I

    move-result v0

    return v0

    .line 56537
    :cond_3
    const/4 v0, 0x0

    return v0
.end method

.method private A02()I
    .locals 4

    .line 56538
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0D:Lcom/facebook/ads/redexgen/X/8T;

    if-eqz v0, :cond_0

    .line 56539
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/8T;->A08()I

    move-result v0

    return v0

    .line 56540
    :cond_0
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0a:Lcom/facebook/ads/redexgen/X/bK;

    if-eqz v0, :cond_2

    .line 56541
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/bK;->A0D()I

    move-result v3

    sget-object v2, Lcom/facebook/ads/redexgen/X/V2;->A0m:[Ljava/lang/String;

    const/4 v0, 0x0

    aget-object v1, v2, v0

    const/4 v0, 0x2

    aget-object v2, v2, v0

    const/4 v0, 0x5

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    invoke-virtual {v2, v0}, Ljava/lang/String;->charAt(I)C

    move-result v0

    if-eq v1, v0, :cond_1

    sget-object v2, Lcom/facebook/ads/redexgen/X/V2;->A0m:[Ljava/lang/String;

    const-string v1, "wlXrbCoONy6aNPfJSLZ4JGG9g8lWOQWJ"

    const/4 v0, 0x1

    aput-object v1, v2, v0

    const-string v1, "zMDzXw8vWIk5v34E7N5RdIcOKoPIh5je"

    const/4 v0, 0x3

    aput-object v1, v2, v0

    return v3

    :cond_1
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    .line 56542
    :cond_2
    iget-object v3, p0, Lcom/facebook/ads/redexgen/X/V2;->A0A:Lcom/facebook/ads/redexgen/X/FO;

    sget-object v2, Lcom/facebook/ads/redexgen/X/V2;->A0m:[Ljava/lang/String;

    const/4 v0, 0x4

    aget-object v1, v2, v0

    const/4 v0, 0x6

    aget-object v0, v2, v0

    invoke-virtual {v1}, Ljava/lang/String;->length()I

    move-result v1

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v0

    if-eq v1, v0, :cond_3

    sget-object v2, Lcom/facebook/ads/redexgen/X/V2;->A0m:[Ljava/lang/String;

    const-string v1, "IJhE7OuQr9AUmHg0gZAU0NE"

    const/4 v0, 0x7

    aput-object v1, v2, v0

    const-string v1, "Tpbr9nnLmrgR8Cas3OKWnYo"

    const/4 v0, 0x5

    aput-object v1, v2, v0

    if-eqz v3, :cond_4

    :goto_0
    invoke-virtual {v3}, Lcom/facebook/ads/redexgen/X/am;->A0H()Lcom/facebook/ads/redexgen/X/8T;

    move-result-object v0

    if-eqz v0, :cond_4

    .line 56543
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0A:Lcom/facebook/ads/redexgen/X/FO;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/am;->A0H()Lcom/facebook/ads/redexgen/X/8T;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/8T;->A08()I

    move-result v0

    return v0

    :cond_3
    sget-object v2, Lcom/facebook/ads/redexgen/X/V2;->A0m:[Ljava/lang/String;

    const-string v1, "x7i9CQzVOJPdqJX1dHOWNRL80PPtBuUS"

    const/4 v0, 0x0

    aput-object v1, v2, v0

    const-string v1, "xh5QtfSCbDAINUU48h4MV8p0ljd4aaCa"

    const/4 v0, 0x2

    aput-object v1, v2, v0

    if-eqz v3, :cond_4

    goto :goto_0

    .line 56544
    :cond_4
    const/16 v0, 0x3e8

    return v0
.end method

.method private A03()I
    .locals 2

    .line 56545
    const/4 v1, 0x0

    .line 56546
    .local v0, "viewabilityCheckTicker":I
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0D:Lcom/facebook/ads/redexgen/X/8T;

    if-eqz v0, :cond_1

    .line 56547
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/8T;->A09()I

    move-result v1

    .line 56548
    :cond_0
    :goto_0
    return v1

    .line 56549
    :cond_1
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0A:Lcom/facebook/ads/redexgen/X/FO;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/am;->A0H()Lcom/facebook/ads/redexgen/X/8T;

    move-result-object v0

    if-eqz v0, :cond_0

    .line 56550
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0A:Lcom/facebook/ads/redexgen/X/FO;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/am;->A0H()Lcom/facebook/ads/redexgen/X/8T;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/8T;->A09()I

    move-result v1

    goto :goto_0
.end method

.method public static synthetic A04(Lcom/facebook/ads/redexgen/X/V2;)J
    .locals 1

    .line 56551
    iget-wide v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A00:J

    return-wide v0
.end method

.method public static A05(Lcom/facebook/ads/redexgen/X/Yn;Landroid/graphics/Bitmap;ZLjava/lang/String;)Landroid/graphics/drawable/Drawable;
    .locals 4

    .line 56552
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Yn;->getResources()Landroid/content/res/Resources;

    move-result-object v0

    new-instance v3, Landroid/graphics/drawable/BitmapDrawable;

    invoke-direct {v3, v0, p1}, Landroid/graphics/drawable/BitmapDrawable;-><init>(Landroid/content/res/Resources;Landroid/graphics/Bitmap;)V

    .line 56553
    .local v0, "iconViewDrawable":Landroid/graphics/drawable/Drawable;
    if-eqz p2, :cond_0

    .line 56554
    invoke-static {p0, p3}, Lcom/facebook/ads/redexgen/X/Pw;->A00(Lcom/facebook/ads/redexgen/X/Yn;Ljava/lang/String;)Landroid/graphics/drawable/BitmapDrawable;

    move-result-object v2

    .line 56555
    .local v1, "mediationDrawable":Landroid/graphics/drawable/Drawable;
    if-eqz v2, :cond_0

    .line 56556
    const/4 v0, 0x2

    new-array v1, v0, [Landroid/graphics/drawable/Drawable;

    const/4 v0, 0x0

    aput-object v3, v1, v0

    const/4 v0, 0x1

    aput-object v2, v1, v0

    new-instance v0, Landroid/graphics/drawable/LayerDrawable;

    invoke-direct {v0, v1}, Landroid/graphics/drawable/LayerDrawable;-><init>([Landroid/graphics/drawable/Drawable;)V

    return-object v0

    .line 56557
    .end local v1    # "mediationDrawable":Landroid/graphics/drawable/Drawable;
    :cond_0
    return-object v3
.end method

.method public static synthetic A06(Lcom/facebook/ads/redexgen/X/V2;)Landroid/graphics/drawable/Drawable;
    .locals 0

    .line 56558
    iget-object p0, p0, Lcom/facebook/ads/redexgen/X/V2;->A01:Landroid/graphics/drawable/Drawable;

    return-object p0
.end method

.method public static synthetic A07(Lcom/facebook/ads/redexgen/X/V2;)Landroid/view/View$OnTouchListener;
    .locals 0

    .line 56559
    iget-object p0, p0, Lcom/facebook/ads/redexgen/X/V2;->A02:Landroid/view/View$OnTouchListener;

    return-object p0
.end method

.method public static synthetic A08(Lcom/facebook/ads/redexgen/X/V2;)Landroid/view/View;
    .locals 0

    .line 56560
    iget-object p0, p0, Lcom/facebook/ads/redexgen/X/V2;->A04:Landroid/view/View;

    return-object p0
.end method

.method public static synthetic A09(Lcom/facebook/ads/redexgen/X/V2;)Landroid/view/View;
    .locals 0

    .line 56561
    iget-object p0, p0, Lcom/facebook/ads/redexgen/X/V2;->A06:Landroid/view/View;

    return-object p0
.end method

.method public static A0A(Landroid/content/Context;Ljava/lang/String;Ljava/lang/String;)Lcom/facebook/ads/NativeAdBase;
    .locals 8
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lcom/facebook/ads/redexgen/X/Jc;
        }
    .end annotation

    .line 56562
    invoke-static {p2}, Lcom/facebook/ads/redexgen/X/Jl;->A00(Ljava/lang/String;)Lcom/facebook/ads/redexgen/X/Jg;

    move-result-object v6

    .line 56563
    .local v0, "template":Lcom/facebook/ads/redexgen/X/Jg;
    const/4 v7, 0x0

    const/4 v3, 0x1

    if-eqz v6, :cond_3

    .line 56564
    sget-object v4, Lcom/facebook/ads/redexgen/X/Jg;->A04:Lcom/facebook/ads/redexgen/X/Jg;

    sget-object v2, Lcom/facebook/ads/redexgen/X/V2;->A0m:[Ljava/lang/String;

    const/4 v0, 0x7

    aget-object v1, v2, v0

    const/4 v0, 0x5

    aget-object v0, v2, v0

    invoke-virtual {v1}, Ljava/lang/String;->length()I

    move-result v1

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v0

    if-eq v1, v0, :cond_0

    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    :cond_0
    sget-object v2, Lcom/facebook/ads/redexgen/X/V2;->A0m:[Ljava/lang/String;

    const-string v1, "9sjWaS9CbZxjWOFrlYfgckD2h0ahWze6"

    const/4 v0, 0x0

    aput-object v1, v2, v0

    const-string v1, "CgLfFBC4KJYZcdRIWMiGUMQeYT9XpqO1"

    const/4 v0, 0x2

    aput-object v1, v2, v0

    if-ne v6, v4, :cond_1

    .line 56565
    new-instance v0, Lcom/facebook/ads/NativeBannerAd;

    invoke-direct {v0, p0, p1}, Lcom/facebook/ads/NativeBannerAd;-><init>(Landroid/content/Context;Ljava/lang/String;)V

    return-object v0

    .line 56566
    :cond_1
    sget-object v0, Lcom/facebook/ads/redexgen/X/Jg;->A05:Lcom/facebook/ads/redexgen/X/Jg;

    if-ne v6, v0, :cond_2

    .line 56567
    new-instance v0, Lcom/facebook/ads/NativeAd;

    invoke-direct {v0, p0, p1}, Lcom/facebook/ads/NativeAd;-><init>(Landroid/content/Context;Ljava/lang/String;)V

    return-object v0

    .line 56568
    :cond_2
    sget-object v5, Lcom/facebook/ads/internal/protocol/AdErrorType;->BID_PAYLOAD_ERROR:Lcom/facebook/ads/internal/protocol/AdErrorType;

    sget-object v4, Ljava/util/Locale;->US:Ljava/util/Locale;

    new-array v3, v3, [Ljava/lang/Object;

    aput-object v6, v3, v7

    .line 56569
    const/16 v2, 0x2a

    const/16 v1, 0x22

    const/16 v0, 0x54

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/V2;->A0W(III)Ljava/lang/String;

    move-result-object v0

    invoke-static {v4, v0, v3}, Ljava/lang/String;->format(Ljava/util/Locale;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v1

    new-instance v0, Lcom/facebook/ads/redexgen/X/Jc;

    invoke-direct {v0, v5, v1}, Lcom/facebook/ads/redexgen/X/Jc;-><init>(Lcom/facebook/ads/internal/protocol/AdErrorType;Ljava/lang/String;)V

    throw v0

    .line 56570
    :cond_3
    sget-object v5, Lcom/facebook/ads/internal/protocol/AdErrorType;->BID_PAYLOAD_ERROR:Lcom/facebook/ads/internal/protocol/AdErrorType;

    sget-object v4, Ljava/util/Locale;->US:Ljava/util/Locale;

    new-array v3, v3, [Ljava/lang/Object;

    aput-object p2, v3, v7

    .line 56571
    const/16 v2, 0x72

    const/16 v1, 0x32

    const/16 v0, 0x32

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/V2;->A0W(III)Ljava/lang/String;

    move-result-object v0

    invoke-static {v4, v0, v3}, Ljava/lang/String;->format(Ljava/util/Locale;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v1

    new-instance v0, Lcom/facebook/ads/redexgen/X/Jc;

    invoke-direct {v0, v5, v1}, Lcom/facebook/ads/redexgen/X/Jc;-><init>(Lcom/facebook/ads/internal/protocol/AdErrorType;Ljava/lang/String;)V

    throw v0
.end method

.method public static synthetic A0B(Lcom/facebook/ads/redexgen/X/V2;)Lcom/facebook/ads/NativeAdLayout;
    .locals 0

    .line 56572
    iget-object p0, p0, Lcom/facebook/ads/redexgen/X/V2;->A07:Lcom/facebook/ads/NativeAdLayout;

    return-object p0
.end method

.method private final A0C()Lcom/facebook/ads/redexgen/X/bK;
    .locals 2

    .line 56573
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/V2;->A0a:Lcom/facebook/ads/redexgen/X/bK;

    .line 56574
    .local v0, "adapter":Lcom/facebook/ads/redexgen/X/bK;
    if-eqz v1, :cond_0

    invoke-virtual {v1}, Lcom/facebook/ads/redexgen/X/bK;->A0R()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 56575
    return-object v1

    .line 56576
    :cond_0
    const/4 v0, 0x0

    return-object v0
.end method

.method public static synthetic A0D(Lcom/facebook/ads/redexgen/X/V2;)Lcom/facebook/ads/redexgen/X/12;
    .locals 0

    .line 56577
    iget-object p0, p0, Lcom/facebook/ads/redexgen/X/V2;->A08:Lcom/facebook/ads/redexgen/X/12;

    return-object p0
.end method

.method public static synthetic A0E(Lcom/facebook/ads/redexgen/X/V2;)Lcom/facebook/ads/redexgen/X/bA;
    .locals 0

    .line 56578
    iget-object p0, p0, Lcom/facebook/ads/redexgen/X/V2;->A09:Lcom/facebook/ads/redexgen/X/bA;

    return-object p0
.end method

.method private A0F()Lcom/facebook/ads/redexgen/X/14;
    .locals 1

    .line 56579
    const/4 v0, 0x0

    invoke-direct {p0, v0}, Lcom/facebook/ads/redexgen/X/V2;->A0G(Z)Lcom/facebook/ads/redexgen/X/14;

    move-result-object v0

    return-object v0
.end method

.method private A0G(Z)Lcom/facebook/ads/redexgen/X/14;
    .locals 4

    .line 56580
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0a:Lcom/facebook/ads/redexgen/X/bK;

    if-eqz v0, :cond_2

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/bK;->A0R()Z

    move-result v0

    if-eqz v0, :cond_2

    .line 56581
    if-eqz p1, :cond_0

    .line 56582
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0a:Lcom/facebook/ads/redexgen/X/bK;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/bK;->A0I()V

    .line 56583
    :cond_0
    iget-object v3, p0, Lcom/facebook/ads/redexgen/X/V2;->A0a:Lcom/facebook/ads/redexgen/X/bK;

    sget-object v2, Lcom/facebook/ads/redexgen/X/V2;->A0m:[Ljava/lang/String;

    const/4 v0, 0x0

    aget-object v1, v2, v0

    const/4 v0, 0x2

    aget-object v2, v2, v0

    const/4 v0, 0x5

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    invoke-virtual {v2, v0}, Ljava/lang/String;->charAt(I)C

    move-result v0

    if-eq v1, v0, :cond_1

    sget-object v2, Lcom/facebook/ads/redexgen/X/V2;->A0m:[Ljava/lang/String;

    const-string v1, "bGIdjP48JCmqHZjd8yMcvy0"

    const/4 v0, 0x7

    aput-object v1, v2, v0

    const-string v1, "TixE3qv3ULKos9kbBUakVg1"

    const/4 v0, 0x5

    aput-object v1, v2, v0

    invoke-virtual {v3}, Lcom/facebook/ads/redexgen/X/bK;->A0E()Lcom/facebook/ads/redexgen/X/14;

    move-result-object v0

    return-object v0

    :cond_1
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    .line 56584
    :cond_2
    new-instance v0, Lcom/facebook/ads/redexgen/X/14;

    invoke-direct {v0}, Lcom/facebook/ads/redexgen/X/14;-><init>()V

    return-object v0
.end method

.method public static synthetic A0H(Lcom/facebook/ads/redexgen/X/V2;)Lcom/facebook/ads/redexgen/X/FO;
    .locals 0

    .line 56585
    iget-object p0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0A:Lcom/facebook/ads/redexgen/X/FO;

    return-object p0
.end method

.method public static synthetic A0I(Lcom/facebook/ads/redexgen/X/V2;)Lcom/facebook/ads/redexgen/X/Yn;
    .locals 0

    .line 56586
    iget-object p0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0c:Lcom/facebook/ads/redexgen/X/Yn;

    return-object p0
.end method

.method public static synthetic A0J(Lcom/facebook/ads/redexgen/X/V2;)Lcom/facebook/ads/redexgen/X/JL;
    .locals 0

    .line 56587
    iget-object p0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0E:Lcom/facebook/ads/redexgen/X/JL;

    return-object p0
.end method

.method public static A0K()Lcom/facebook/ads/redexgen/X/V5;
    .locals 1

    .line 56588
    new-instance v0, Lcom/facebook/ads/redexgen/X/V5;

    invoke-direct {v0}, Lcom/facebook/ads/redexgen/X/V5;-><init>()V

    return-object v0
.end method

.method public static A0L(Lcom/facebook/ads/internal/api/NativeAdBaseApi;)Lcom/facebook/ads/redexgen/X/V2;
    .locals 1

    .line 56589
    instance-of v0, p0, Ljava/lang/reflect/Proxy;

    if-eqz v0, :cond_0

    .line 56590
    invoke-static {p0}, Ljava/lang/reflect/Proxy;->getInvocationHandler(Ljava/lang/Object;)Ljava/lang/reflect/InvocationHandler;

    move-result-object v0

    check-cast v0, Lcom/facebook/ads/redexgen/X/5L;

    .line 56591
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/5L;->A04()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/facebook/ads/redexgen/X/V2;

    .line 56592
    return-object v0

    .line 56593
    :cond_0
    check-cast p0, Lcom/facebook/ads/redexgen/X/V2;

    return-object p0
.end method

.method private final A0M()Lcom/facebook/ads/redexgen/X/JP;
    .locals 1

    .line 56594
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/V2;->A0F()Lcom/facebook/ads/redexgen/X/14;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/14;->A0E()Lcom/facebook/ads/redexgen/X/JP;

    move-result-object v0

    return-object v0
.end method

.method public static synthetic A0N(Lcom/facebook/ads/redexgen/X/V2;)Lcom/facebook/ads/redexgen/X/V1;
    .locals 0

    .line 56595
    iget-object p0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0G:Lcom/facebook/ads/redexgen/X/V1;

    return-object p0
.end method

.method private final A0O()Lcom/facebook/ads/redexgen/X/JQ;
    .locals 1

    .line 56596
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/V2;->A0F()Lcom/facebook/ads/redexgen/X/14;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/14;->A0H()Lcom/facebook/ads/redexgen/X/JQ;

    move-result-object v0

    return-object v0
.end method

.method public static synthetic A0P(Lcom/facebook/ads/redexgen/X/V2;)Lcom/facebook/ads/redexgen/X/JS;
    .locals 0

    .line 56597
    iget-object p0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0I:Lcom/facebook/ads/redexgen/X/JS;

    return-object p0
.end method

.method public static synthetic A0Q(Lcom/facebook/ads/redexgen/X/V2;)Lcom/facebook/ads/redexgen/X/JZ;
    .locals 0

    .line 56598
    iget-object p0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0e:Lcom/facebook/ads/redexgen/X/JZ;

    return-object p0
.end method

.method private A0R()Lcom/facebook/ads/internal/protocol/AdPlacementType;
    .locals 2

    .line 56599
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/V2;->A0J:Lcom/facebook/ads/redexgen/X/Jg;

    sget-object v0, Lcom/facebook/ads/redexgen/X/Jg;->A05:Lcom/facebook/ads/redexgen/X/Jg;

    if-ne v1, v0, :cond_0

    .line 56600
    sget-object v0, Lcom/facebook/ads/internal/protocol/AdPlacementType;->NATIVE:Lcom/facebook/ads/internal/protocol/AdPlacementType;

    .line 56601
    :goto_0
    return-object v0

    .line 56602
    :cond_0
    sget-object v0, Lcom/facebook/ads/internal/protocol/AdPlacementType;->NATIVE_BANNER:Lcom/facebook/ads/internal/protocol/AdPlacementType;

    goto :goto_0
.end method

.method public static synthetic A0S(Lcom/facebook/ads/redexgen/X/V2;)Lcom/facebook/ads/redexgen/X/Lg;
    .locals 0

    .line 56603
    iget-object p0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0f:Lcom/facebook/ads/redexgen/X/Lg;

    return-object p0
.end method

.method public static synthetic A0T(Lcom/facebook/ads/redexgen/X/V2;)Lcom/facebook/ads/redexgen/X/Ng;
    .locals 0

    .line 56604
    iget-object p0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0L:Lcom/facebook/ads/redexgen/X/Ng;

    return-object p0
.end method

.method public static synthetic A0U(Lcom/facebook/ads/redexgen/X/V2;Lcom/facebook/ads/redexgen/X/PB;)Lcom/facebook/ads/redexgen/X/PB;
    .locals 0

    .line 56605
    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/V2;->A0M:Lcom/facebook/ads/redexgen/X/PB;

    return-object p1
.end method

.method public static synthetic A0V(Lcom/facebook/ads/redexgen/X/V2;)Lcom/facebook/ads/redexgen/X/RE;
    .locals 0

    .line 56606
    iget-object p0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0R:Lcom/facebook/ads/redexgen/X/RE;

    return-object p0
.end method

.method public static A0W(III)Ljava/lang/String;
    .locals 2

    sget-object v1, Lcom/facebook/ads/redexgen/X/V2;->A0l:[B

    add-int v0, p0, p1

    invoke-static {v1, p0, v0}, Ljava/util/Arrays;->copyOfRange([BII)[B

    move-result-object p0

    const/4 v1, 0x0

    :goto_0
    array-length v0, p0

    if-ge v1, v0, :cond_0

    aget-byte v0, p0, v1

    sub-int/2addr v0, p2

    add-int/lit8 v0, v0, -0x28

    int-to-byte v0, v0

    aput-byte v0, p0, v1

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_0
    new-instance v0, Ljava/lang/String;

    invoke-direct {v0, p0}, Ljava/lang/String;-><init>([B)V

    return-object v0
.end method

.method public static synthetic A0X(Lcom/facebook/ads/redexgen/X/V2;)Ljava/lang/String;
    .locals 0

    .line 56607
    iget-object p0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0S:Ljava/lang/String;

    return-object p0
.end method

.method public static synthetic A0Y(Lcom/facebook/ads/redexgen/X/V2;)Ljava/lang/ref/WeakReference;
    .locals 0

    .line 56608
    iget-object p0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0V:Ljava/lang/ref/WeakReference;

    return-object p0
.end method

.method private A0Z()V
    .locals 3

    .line 56609
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0i:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v2

    :goto_0
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_0

    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Landroid/view/View;

    .line 56610
    .local v1, "v":Landroid/view/View;
    const/4 v0, 0x0

    invoke-virtual {v1, v0}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 56611
    invoke-virtual {v1, v0}, Landroid/view/View;->setOnTouchListener(Landroid/view/View$OnTouchListener;)V

    .line 56612
    invoke-virtual {v1, v0}, Landroid/view/View;->setOnLongClickListener(Landroid/view/View$OnLongClickListener;)V

    .line 56613
    .end local v1    # "v":Landroid/view/View;
    goto :goto_0

    .line 56614
    :cond_0
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0i:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->clear()V

    .line 56615
    return-void
.end method

.method private A0a()V
    .locals 4

    .line 56616
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/V2;->getAdChoicesLinkUrl()Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    if-nez v0, :cond_0

    .line 56617
    new-instance v3, Lcom/facebook/ads/redexgen/X/Kv;

    invoke-direct {v3}, Lcom/facebook/ads/redexgen/X/Kv;-><init>()V

    iget-object v2, p0, Lcom/facebook/ads/redexgen/X/V2;->A0c:Lcom/facebook/ads/redexgen/X/Yn;

    .line 56618
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/V2;->getAdChoicesLinkUrl()Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/Ky;->A00(Ljava/lang/String;)Landroid/net/Uri;

    move-result-object v1

    .line 56619
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/V2;->A1B()Ljava/lang/String;

    move-result-object v0

    .line 56620
    invoke-static {v3, v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Kv;->A0M(Lcom/facebook/ads/redexgen/X/Kv;Lcom/facebook/ads/redexgen/X/Yn;Landroid/net/Uri;Ljava/lang/String;)Z

    .line 56621
    :cond_0
    return-void
.end method

.method private A0b()V
    .locals 1

    .line 56622
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0f:Lcom/facebook/ads/redexgen/X/Lg;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/Lg;->A05()V

    .line 56623
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0e:Lcom/facebook/ads/redexgen/X/JZ;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/JZ;->A05()V

    .line 56624
    return-void
.end method

.method private A0c()V
    .locals 1

    .line 56625
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0Q:Lcom/facebook/ads/redexgen/X/RE;

    if-eqz v0, :cond_0

    .line 56626
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/RE;->A0V()V

    .line 56627
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0c:Lcom/facebook/ads/redexgen/X/Yn;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/Yn;->A0E()Lcom/facebook/ads/redexgen/X/0S;

    move-result-object v0

    invoke-interface {v0}, Lcom/facebook/ads/redexgen/X/0S;->AAU()V

    .line 56628
    const/4 v0, 0x0

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0Q:Lcom/facebook/ads/redexgen/X/RE;

    .line 56629
    :cond_0
    return-void
.end method

.method public static A0d()V
    .locals 1

    const/16 v0, 0x25a

    new-array v0, v0, [B

    fill-array-data v0, :array_0

    sput-object v0, Lcom/facebook/ads/redexgen/X/V2;->A0l:[B

    return-void

    :array_0
    .array-data 1
        0x56t
        -0x70t
        -0x64t
        -0x6bt
        -0x6ct
        -0x5bt
        -0x54t
        -0x27t
        -0x29t
        -0x27t
        -0x5ct
        -0x58t
        -0x29t
        -0x4bt
        -0x4dt
        -0x1et
        -0x4et
        -0x19t
        -0x4bt
        -0x1ct
        -0x1dt
        -0x24t
        0xct
        -0x25t
        -0x27t
        -0x21t
        0x9t
        0x7t
        0xct
        0x7dt
        -0x60t
        0x5ct
        -0x56t
        -0x55t
        -0x50t
        0x5ct
        -0x58t
        -0x55t
        -0x63t
        -0x60t
        -0x5ft
        -0x60t
        -0x43t
        -0x20t
        -0x30t
        -0x1ft
        -0x17t
        -0x14t
        -0x18t
        -0x23t
        -0x10t
        -0x1ft
        -0x64t
        -0x5dt
        -0x5ft
        -0x11t
        -0x5dt
        -0x64t
        -0x1bt
        -0x11t
        -0x64t
        -0x16t
        -0x15t
        -0x10t
        -0x64t
        -0x23t
        -0x64t
        -0x16t
        -0x23t
        -0x10t
        -0x1bt
        -0xet
        -0x1ft
        -0x64t
        -0x23t
        -0x20t
        -0x69t
        -0x6dt
        -0x6et
        -0x3at
        -0x4bt
        -0x46t
        -0x4at
        -0x41t
        -0x4ct
        -0x4at
        -0x61t
        -0x4at
        -0x3bt
        -0x38t
        -0x40t
        -0x3dt
        -0x44t
        -0x53t
        -0x38t
        -0x30t
        -0x2dt
        -0x34t
        -0x35t
        -0x79t
        -0x25t
        -0x2at
        -0x79t
        -0x2dt
        -0x2at
        -0x38t
        -0x35t
        -0x79t
        -0x4ct
        -0x34t
        -0x35t
        -0x30t
        -0x38t
        -0x6bt
        -0x60t
        -0x45t
        -0x3dt
        -0x3at
        -0x41t
        -0x42t
        0x7at
        -0x32t
        -0x37t
        0x7at
        -0x37t
        -0x44t
        -0x32t
        -0x45t
        -0x3dt
        -0x38t
        0x7at
        -0x32t
        -0x41t
        -0x39t
        -0x36t
        -0x3at
        -0x45t
        -0x32t
        -0x41t
        0x7at
        -0x5dt
        -0x62t
        0x7at
        -0x40t
        -0x34t
        -0x37t
        -0x39t
        0x7at
        -0x44t
        -0x3dt
        -0x42t
        0x7at
        -0x36t
        -0x45t
        -0x2dt
        -0x3at
        -0x37t
        -0x45t
        -0x42t
        0x7at
        -0x7ft
        0x7ft
        -0x33t
        -0x7ft
        -0x4ft
        -0x2at
        -0x24t
        -0x33t
        -0x26t
        -0x2at
        -0x37t
        -0x2ct
        -0x78t
        -0x33t
        -0x26t
        -0x26t
        -0x29t
        -0x26t
        -0x6at
        0x72t
        -0x2ft
        -0xat
        -0x2t
        -0x17t
        -0xct
        -0xft
        -0x14t
        -0x58t
        -0x5t
        -0x13t
        -0x4t
        -0x58t
        -0x9t
        -0x12t
        -0x58t
        -0x15t
        -0xct
        -0xft
        -0x15t
        -0xdt
        -0x17t
        -0x16t
        -0xct
        -0x13t
        -0x58t
        -0x2t
        -0xft
        -0x13t
        -0x1t
        -0x5t
        -0x49t
        -0x31t
        -0x32t
        -0x2dt
        -0x35t
        -0x40t
        -0x2dt
        -0x31t
        -0x1ft
        -0x76t
        -0x30t
        -0x27t
        -0x24t
        -0x76t
        -0x2dt
        -0x33t
        -0x27t
        -0x28t
        -0x76t
        -0x2dt
        -0x23t
        -0x76t
        -0x2dt
        -0x23t
        -0x76t
        -0x29t
        -0x2dt
        -0x23t
        -0x23t
        -0x2dt
        -0x28t
        -0x2ft
        -0x68t
        -0x47t
        -0x2ft
        -0x30t
        -0x2bt
        -0x33t
        -0x3et
        -0x2bt
        -0x2ft
        -0x1dt
        -0x74t
        -0x2bt
        -0x21t
        -0x74t
        -0x27t
        -0x2bt
        -0x21t
        -0x21t
        -0x2bt
        -0x26t
        -0x2dt
        -0x66t
        -0x75t
        -0x4dt
        -0x4ft
        -0x4et
        0x5et
        -0x52t
        -0x50t
        -0x53t
        -0x4ct
        -0x59t
        -0x5et
        -0x5dt
        0x5et
        -0x61t
        0x5et
        -0x6ct
        -0x59t
        -0x5dt
        -0x4bt
        -0x31t
        -0x1et
        -0xbt
        -0x16t
        -0x9t
        -0x1at
        -0x5ft
        -0x3et
        -0x1bt
        -0x5ft
        -0x8t
        -0x1et
        -0xct
        -0x5ft
        -0x1et
        -0x13t
        -0xdt
        -0x1at
        -0x1et
        -0x1bt
        -0x6t
        -0x5ft
        -0xdt
        -0x1at
        -0x18t
        -0x16t
        -0xct
        -0xbt
        -0x1at
        -0xdt
        -0x1at
        -0x1bt
        -0x5ft
        -0x8t
        -0x16t
        -0xbt
        -0x17t
        -0x5ft
        -0x1et
        -0x5ft
        -0x29t
        -0x16t
        -0x1at
        -0x8t
        -0x51t
        -0x5ft
        -0x3et
        -0xat
        -0xbt
        -0x10t
        -0x5ft
        -0xat
        -0x11t
        -0xdt
        -0x1at
        -0x18t
        -0x16t
        -0xct
        -0xbt
        -0x1at
        -0xdt
        -0x16t
        -0x11t
        -0x18t
        -0x5ft
        -0x1et
        -0x11t
        -0x1bt
        -0x5ft
        -0xft
        -0xdt
        -0x10t
        -0x1ct
        -0x1at
        -0x1at
        -0x1bt
        -0x16t
        -0x11t
        -0x18t
        -0x51t
        -0x73t
        -0x60t
        -0x4dt
        -0x58t
        -0x4bt
        -0x5ct
        0x5ft
        -0x60t
        -0x5dt
        0x5ft
        -0x5dt
        -0x5ct
        -0x4et
        -0x4dt
        -0x4ft
        -0x52t
        -0x48t
        -0x5ct
        -0x5dt
        -0x28t
        -0x15t
        -0x2t
        -0xdt
        0x0t
        -0x11t
        -0x56t
        -0x15t
        -0x12t
        -0x56t
        -0xat
        -0x7t
        -0x15t
        -0x12t
        -0x56t
        -0x4t
        -0x11t
        -0x5t
        -0x1t
        -0x11t
        -0x3t
        -0x2t
        -0x11t
        -0x12t
        -0x73t
        -0x60t
        -0x64t
        -0x52t
        0x57t
        -0x68t
        -0x5dt
        -0x57t
        -0x64t
        -0x68t
        -0x65t
        -0x50t
        0x57t
        -0x57t
        -0x64t
        -0x62t
        -0x60t
        -0x56t
        -0x55t
        -0x64t
        -0x57t
        -0x64t
        -0x65t
        0x57t
        -0x52t
        -0x60t
        -0x55t
        -0x61t
        0x57t
        -0x68t
        0x57t
        -0x7bt
        -0x68t
        -0x55t
        -0x60t
        -0x53t
        -0x64t
        0x78t
        -0x65t
        0x65t
        0x57t
        0x78t
        -0x54t
        -0x55t
        -0x5at
        0x57t
        -0x54t
        -0x5bt
        -0x57t
        -0x64t
        -0x62t
        -0x60t
        -0x56t
        -0x55t
        -0x64t
        -0x57t
        -0x60t
        -0x5bt
        -0x62t
        0x57t
        -0x68t
        -0x5bt
        -0x65t
        0x57t
        -0x59t
        -0x57t
        -0x5at
        -0x66t
        -0x64t
        -0x64t
        -0x65t
        -0x60t
        -0x5bt
        -0x62t
        0x65t
        -0x3at
        -0x27t
        -0x2bt
        -0x19t
        -0x70t
        -0x22t
        -0x21t
        -0x1ct
        -0x70t
        -0x1et
        -0x2bt
        -0x29t
        -0x27t
        -0x1dt
        -0x1ct
        -0x2bt
        -0x1et
        -0x2bt
        -0x2ct
        -0x70t
        -0x19t
        -0x27t
        -0x1ct
        -0x28t
        -0x70t
        -0x1ct
        -0x28t
        -0x27t
        -0x1dt
        -0x70t
        -0x42t
        -0x2ft
        -0x1ct
        -0x27t
        -0x1at
        -0x2bt
        -0x4ft
        -0x2ct
        -0x68t
        -0x65t
        0x57t
        -0x5ct
        -0x64t
        -0x65t
        -0x60t
        -0x68t
        0x57t
        -0x55t
        -0x50t
        -0x59t
        -0x64t
        0x57t
        -0x60t
        -0x56t
        0x57t
        -0x5bt
        -0x5at
        -0x55t
        0x57t
        -0x56t
        -0x54t
        -0x59t
        -0x59t
        -0x5at
        -0x57t
        -0x55t
        -0x64t
        -0x65t
        0x65t
        -0x74t
        -0x65t
        -0x6ct
        -0x6dt
        -0x6ct
        -0x5et
        -0x5dt
        -0x5ft
        -0x62t
        -0x58t
        -0x5et
        -0x5bt
        -0x69t
        -0x66t
        0x77t
        -0x66t
        0x7t
        0xat
        -0x4t
        -0x1t
        -0x24t
        -0x1t
        -0x3dt
        -0x3ct
        -0x45t
        -0x2t
        -0x4t
        0x7t
        0x7t
        0x0t
        -0x1t
        -0x45t
        0x8t
        0xat
        0xdt
        0x0t
        -0x45t
        0xft
        0x3t
        -0x4t
        0x9t
        -0x45t
        0xat
        0x9t
        -0x2t
        0x0t
        -0x11t
        -0x1et
        -0xbt
        -0x16t
        -0x9t
        -0x1at
    .end array-data
.end method

.method public static A0e(Landroid/graphics/drawable/Drawable;Landroid/widget/ImageView;)V
    .locals 2

    .line 56630
    sget-object v0, Landroid/widget/ImageView$ScaleType;->FIT_XY:Landroid/widget/ImageView$ScaleType;

    invoke-virtual {p1, v0}, Landroid/widget/ImageView;->setScaleType(Landroid/widget/ImageView$ScaleType;)V

    .line 56631
    if-eqz p0, :cond_0

    .line 56632
    invoke-virtual {p1, p0}, Landroid/widget/ImageView;->setImageDrawable(Landroid/graphics/drawable/Drawable;)V

    .line 56633
    :cond_0
    sget p0, Landroid/os/Build$VERSION;->SDK_INT:I

    const/16 v1, 0x10

    const/4 v0, 0x0

    if-lt p0, v1, :cond_1

    .line 56634
    invoke-virtual {p1, v0}, Landroid/widget/ImageView;->setBackground(Landroid/graphics/drawable/Drawable;)V

    .line 56635
    :goto_0
    return-void

    .line 56636
    :cond_1
    invoke-virtual {p1, v0}, Landroid/widget/ImageView;->setBackgroundDrawable(Landroid/graphics/drawable/Drawable;)V

    goto :goto_0
.end method

.method private A0f(Landroid/view/View;Landroid/view/View;Ljava/util/List;Z)V
    .locals 13
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/view/View;",
            "Landroid/view/View;",
            "Ljava/util/List<",
            "Landroid/view/View;",
            ">;Z)V"
        }
    .end annotation

    .line 56637
    .local p11, "clickableViews":Ljava/util/List;, "Ljava/util/List<Landroid/view/View;>;"
    move-object v2, p0

    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/V2;->A0q()Z

    move-result v0

    if-nez v0, :cond_0

    .line 56638
    iget-object v0, v2, Lcom/facebook/ads/redexgen/X/V2;->A0c:Lcom/facebook/ads/redexgen/X/Yn;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/Yn;->A0E()Lcom/facebook/ads/redexgen/X/0S;

    move-result-object v0

    invoke-interface {v0}, Lcom/facebook/ads/redexgen/X/0S;->AEU()V

    .line 56639
    :cond_0
    if-nez p1, :cond_2

    .line 56640
    const/16 v3, 0x108

    const/16 v1, 0x13

    const/16 v0, 0x16

    invoke-static {v3, v1, v0}, Lcom/facebook/ads/redexgen/X/V2;->A0W(III)Ljava/lang/String;

    move-result-object v1

    .line 56641
    .local v4, "mustProvideAView":Ljava/lang/String;
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/V2;->A0q()Z

    move-result v0

    if-nez v0, :cond_1

    .line 56642
    iget-object v0, v2, Lcom/facebook/ads/redexgen/X/V2;->A0c:Lcom/facebook/ads/redexgen/X/Yn;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/Yn;->A0E()Lcom/facebook/ads/redexgen/X/0S;

    move-result-object v0

    invoke-interface {v0, v1}, Lcom/facebook/ads/redexgen/X/0S;->AET(Ljava/lang/String;)V

    .line 56643
    :cond_1
    new-instance v0, Ljava/lang/IllegalArgumentException;

    invoke-direct {v0, v1}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw v0

    .line 56644
    .end local v4    # "mustProvideAView":Ljava/lang/String;
    :cond_2
    move-object/from16 v6, p3

    if-eqz v6, :cond_3

    invoke-interface {v6}, Ljava/util/List;->size()I

    move-result v0

    if-nez v0, :cond_5

    .line 56645
    .end local v4
    .end local v5
    .end local v6
    .end local v8
    .end local v9
    .end local v10
    .end local v11
    :cond_3
    const/16 v3, 0xb4

    const/16 v1, 0x1e

    const/16 v0, 0x60

    invoke-static {v3, v1, v0}, Lcom/facebook/ads/redexgen/X/V2;->A0W(III)Ljava/lang/String;

    move-result-object v1

    .line 56646
    .local v2, "invalidSetOfClickableViews":Ljava/lang/String;
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/V2;->A0q()Z

    move-result v0

    if-nez v0, :cond_4

    .line 56647
    iget-object v0, v2, Lcom/facebook/ads/redexgen/X/V2;->A0c:Lcom/facebook/ads/redexgen/X/Yn;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/Yn;->A0E()Lcom/facebook/ads/redexgen/X/0S;

    move-result-object v0

    invoke-interface {v0, v1}, Lcom/facebook/ads/redexgen/X/0S;->AET(Ljava/lang/String;)V

    .line 56648
    :cond_4
    new-instance v0, Ljava/lang/IllegalArgumentException;

    invoke-direct {v0, v1}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw v0

    .line 56649
    :cond_5
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/V2;->A0C()Lcom/facebook/ads/redexgen/X/bK;

    move-result-object v5

    .line 56650
    .local v4, "adapter":Lcom/facebook/ads/redexgen/X/bK;
    if-nez v5, :cond_8

    .line 56651
    const/16 v3, 0x1d

    const/16 v1, 0xd

    const/16 v0, 0x14

    invoke-static {v3, v1, v0}, Lcom/facebook/ads/redexgen/X/V2;->A0W(III)Ljava/lang/String;

    move-result-object v1

    .line 56652
    .local v5, "adNotLoadedError":Ljava/lang/String;
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/V2;->A0q()Z

    move-result v0

    if-nez v0, :cond_6

    .line 56653
    iget-object v0, v2, Lcom/facebook/ads/redexgen/X/V2;->A0c:Lcom/facebook/ads/redexgen/X/Yn;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/Yn;->A0E()Lcom/facebook/ads/redexgen/X/0S;

    move-result-object v0

    invoke-interface {v0, v1}, Lcom/facebook/ads/redexgen/X/0S;->AET(Ljava/lang/String;)V

    .line 56654
    :cond_6
    sget-object v0, Lcom/facebook/ads/redexgen/X/V2;->A0n:Ljava/lang/String;

    invoke-static {v0, v1}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    .line 56655
    sget-object v0, Lcom/facebook/ads/internal/protocol/AdErrorType;->NATIVE_AD_IS_NOT_LOADED:Lcom/facebook/ads/internal/protocol/AdErrorType;

    new-instance v6, Lcom/facebook/ads/redexgen/X/Jb;

    invoke-direct {v6, v0, v1}, Lcom/facebook/ads/redexgen/X/Jb;-><init>(Lcom/facebook/ads/internal/protocol/AdErrorType;Ljava/lang/String;)V

    .line 56656
    .local v6, "error":Lcom/facebook/ads/redexgen/X/Jb;
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/V2;->A11()Lcom/facebook/ads/redexgen/X/Yn;

    move-result-object v0

    .line 56657
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/Yn;->A0E()Lcom/facebook/ads/redexgen/X/0S;

    move-result-object v5

    iget-wide v0, v2, Lcom/facebook/ads/redexgen/X/V2;->A00:J

    .line 56658
    invoke-static {v0, v1}, Lcom/facebook/ads/redexgen/X/Lf;->A01(J)J

    move-result-wide v3

    .line 56659
    invoke-virtual {v6}, Lcom/facebook/ads/redexgen/X/Jb;->A03()Lcom/facebook/ads/internal/protocol/AdErrorType;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/internal/protocol/AdErrorType;->getErrorCode()I

    move-result v1

    .line 56660
    invoke-virtual {v6}, Lcom/facebook/ads/redexgen/X/Jb;->A04()Ljava/lang/String;

    move-result-object v0

    .line 56661
    invoke-interface {v5, v3, v4, v1, v0}, Lcom/facebook/ads/redexgen/X/0S;->A2m(JILjava/lang/String;)V

    .line 56662
    iget-object v0, v2, Lcom/facebook/ads/redexgen/X/V2;->A0c:Lcom/facebook/ads/redexgen/X/Yn;

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/Ih;->A2F(Landroid/content/Context;)Z

    move-result v0

    if-eqz v0, :cond_7

    iget-object v0, v2, Lcom/facebook/ads/redexgen/X/V2;->A0G:Lcom/facebook/ads/redexgen/X/V1;

    if-eqz v0, :cond_7

    .line 56663
    invoke-interface {v0, v6}, Lcom/facebook/ads/redexgen/X/JH;->ABR(Lcom/facebook/ads/redexgen/X/Jb;)V

    .line 56664
    :cond_7
    return-void

    .line 56665
    .end local v5    # "adNotLoadedError":Ljava/lang/String;
    .end local v6    # "error":Lcom/facebook/ads/redexgen/X/Jb;
    :cond_8
    iget-object v4, v2, Lcom/facebook/ads/redexgen/X/V2;->A0T:Ljava/lang/String;

    .line 56666
    .local v5, "mediationData":Ljava/lang/String;
    instance-of v7, p1, Landroid/widget/FrameLayout;

    sget-object v3, Lcom/facebook/ads/redexgen/X/V2;->A0m:[Ljava/lang/String;

    const/4 v0, 0x7

    aget-object v1, v3, v0

    const/4 v0, 0x5

    aget-object v0, v3, v0

    invoke-virtual {v1}, Ljava/lang/String;->length()I

    move-result v1

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v0

    if-eq v1, v0, :cond_9

    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    :cond_9
    sget-object v3, Lcom/facebook/ads/redexgen/X/V2;->A0m:[Ljava/lang/String;

    const-string v1, "EctJGKY0XxsuqG0UQd3u1zsk6i1LiKEB"

    const/4 v0, 0x0

    aput-object v1, v3, v0

    const-string v1, "xfmrtxxFdZMJNwdY4ICwqytBKlocc6OO"

    const/4 v0, 0x2

    aput-object v1, v3, v0

    if-eqz v7, :cond_a

    if-eqz v4, :cond_a

    .line 56667
    move-object v0, p1

    check-cast v0, Landroid/widget/FrameLayout;

    .line 56668
    .local v6, "adLayout":Landroid/widget/FrameLayout;
    invoke-direct {v2, v0, v4}, Lcom/facebook/ads/redexgen/X/V2;->A0g(Landroid/widget/FrameLayout;Ljava/lang/String;)V

    .line 56669
    .end local v6    # "adLayout":Landroid/widget/FrameLayout;
    :cond_a
    iget-object v0, v2, Lcom/facebook/ads/redexgen/X/V2;->A07:Lcom/facebook/ads/NativeAdLayout;

    if-eqz v0, :cond_b

    .line 56670
    invoke-virtual {v0}, Lcom/facebook/ads/NativeAdLayout;->getNativeAdLayoutApi()Lcom/facebook/ads/internal/api/NativeAdLayoutApi;

    move-result-object v0

    check-cast v0, Lcom/facebook/ads/redexgen/X/ZG;

    .line 56671
    .local v6, "nativeAdLayoutApiImpl":Lcom/facebook/ads/redexgen/X/ZG;
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/ZG;->A02()V

    .line 56672
    .end local v6    # "nativeAdLayoutApiImpl":Lcom/facebook/ads/redexgen/X/ZG;
    :cond_b
    iget-object v0, v2, Lcom/facebook/ads/redexgen/X/V2;->A0U:Ljava/lang/ref/WeakReference;

    invoke-virtual {v0}, Ljava/lang/ref/WeakReference;->get()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/facebook/ads/redexgen/X/ZX;

    .line 56673
    .local v6, "adOptionsViewApi":Lcom/facebook/ads/redexgen/X/ZX;
    const/4 v3, 0x1

    if-eqz v1, :cond_c

    invoke-virtual {v5}, Lcom/facebook/ads/redexgen/X/bK;->A08()I

    move-result v0

    if-ne v0, v3, :cond_c

    .line 56674
    sget-object v0, Lcom/facebook/ads/redexgen/X/Lw;->A08:Lcom/facebook/ads/redexgen/X/Lw;

    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/ZX;->A03(Lcom/facebook/ads/redexgen/X/Lw;)V

    .line 56675
    :cond_c
    if-nez p2, :cond_11

    .line 56676
    iget-object v1, v2, Lcom/facebook/ads/redexgen/X/V2;->A0J:Lcom/facebook/ads/redexgen/X/Jg;

    sget-object v0, Lcom/facebook/ads/redexgen/X/Jg;->A05:Lcom/facebook/ads/redexgen/X/Jg;

    if-ne v1, v0, :cond_f

    .line 56677
    sget-object v4, Lcom/facebook/ads/internal/protocol/AdErrorType;->NO_MEDIAVIEW_IN_NATIVEAD:Lcom/facebook/ads/internal/protocol/AdErrorType;

    const/16 v3, 0xf3

    const/16 v1, 0x15

    const/16 v0, 0x44

    invoke-static {v3, v1, v0}, Lcom/facebook/ads/redexgen/X/V2;->A0W(III)Ljava/lang/String;

    move-result-object v7

    new-instance v6, Lcom/facebook/ads/redexgen/X/Jb;

    invoke-direct {v6, v4, v7}, Lcom/facebook/ads/redexgen/X/Jb;-><init>(Lcom/facebook/ads/internal/protocol/AdErrorType;Ljava/lang/String;)V

    .line 56678
    .local v7, "error":Lcom/facebook/ads/redexgen/X/Jb;
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/V2;->A11()Lcom/facebook/ads/redexgen/X/Yn;

    move-result-object v0

    .line 56679
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/Yn;->A0E()Lcom/facebook/ads/redexgen/X/0S;

    move-result-object v5

    iget-wide v0, v2, Lcom/facebook/ads/redexgen/X/V2;->A00:J

    .line 56680
    invoke-static {v0, v1}, Lcom/facebook/ads/redexgen/X/Lf;->A01(J)J

    move-result-wide v3

    .line 56681
    invoke-virtual {v6}, Lcom/facebook/ads/redexgen/X/Jb;->A03()Lcom/facebook/ads/internal/protocol/AdErrorType;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/internal/protocol/AdErrorType;->getErrorCode()I

    move-result v1

    .line 56682
    invoke-virtual {v6}, Lcom/facebook/ads/redexgen/X/Jb;->A04()Ljava/lang/String;

    move-result-object v0

    .line 56683
    invoke-interface {v5, v3, v4, v1, v0}, Lcom/facebook/ads/redexgen/X/0S;->A2m(JILjava/lang/String;)V

    .line 56684
    iget-object v0, v2, Lcom/facebook/ads/redexgen/X/V2;->A0G:Lcom/facebook/ads/redexgen/X/V1;

    if-eqz v0, :cond_d

    .line 56685
    invoke-interface {v0, v6}, Lcom/facebook/ads/redexgen/X/JH;->ABR(Lcom/facebook/ads/redexgen/X/Jb;)V

    .line 56686
    :cond_d
    invoke-static {}, Lcom/facebook/ads/internal/settings/AdInternalSettings;->isDebugBuild()Z

    move-result v0

    if-eqz v0, :cond_e

    .line 56687
    sget-object v0, Lcom/facebook/ads/redexgen/X/V2;->A0n:Ljava/lang/String;

    invoke-static {v0, v7}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    .line 56688
    .end local v7    # "error":Lcom/facebook/ads/redexgen/X/Jb;
    :cond_e
    :goto_0
    return-void

    .line 56689
    :cond_f
    sget-object v4, Lcom/facebook/ads/internal/protocol/AdErrorType;->NO_MEDIAVIEW_IN_NATIVEBANNERAD:Lcom/facebook/ads/internal/protocol/AdErrorType;

    const/16 v3, 0xd2

    const/16 v1, 0x21

    const/16 v0, 0x42

    invoke-static {v3, v1, v0}, Lcom/facebook/ads/redexgen/X/V2;->A0W(III)Ljava/lang/String;

    move-result-object v7

    new-instance v6, Lcom/facebook/ads/redexgen/X/Jb;

    invoke-direct {v6, v4, v7}, Lcom/facebook/ads/redexgen/X/Jb;-><init>(Lcom/facebook/ads/internal/protocol/AdErrorType;Ljava/lang/String;)V

    .line 56690
    .restart local v7    # "error":Lcom/facebook/ads/redexgen/X/Jb;
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/V2;->A11()Lcom/facebook/ads/redexgen/X/Yn;

    move-result-object v0

    .line 56691
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/Yn;->A0E()Lcom/facebook/ads/redexgen/X/0S;

    move-result-object v5

    iget-wide v0, v2, Lcom/facebook/ads/redexgen/X/V2;->A00:J

    .line 56692
    invoke-static {v0, v1}, Lcom/facebook/ads/redexgen/X/Lf;->A01(J)J

    move-result-wide v3

    .line 56693
    invoke-virtual {v6}, Lcom/facebook/ads/redexgen/X/Jb;->A03()Lcom/facebook/ads/internal/protocol/AdErrorType;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/internal/protocol/AdErrorType;->getErrorCode()I

    move-result v1

    .line 56694
    invoke-virtual {v6}, Lcom/facebook/ads/redexgen/X/Jb;->A04()Ljava/lang/String;

    move-result-object v0

    .line 56695
    invoke-interface {v5, v3, v4, v1, v0}, Lcom/facebook/ads/redexgen/X/0S;->A2m(JILjava/lang/String;)V

    .line 56696
    iget-object v0, v2, Lcom/facebook/ads/redexgen/X/V2;->A0G:Lcom/facebook/ads/redexgen/X/V1;

    if-eqz v0, :cond_10

    .line 56697
    invoke-interface {v0, v6}, Lcom/facebook/ads/redexgen/X/JH;->ABR(Lcom/facebook/ads/redexgen/X/Jb;)V

    .line 56698
    :cond_10
    invoke-static {}, Lcom/facebook/ads/internal/settings/AdInternalSettings;->isDebugBuild()Z

    move-result v0

    if-eqz v0, :cond_e

    .line 56699
    sget-object v0, Lcom/facebook/ads/redexgen/X/V2;->A0n:Ljava/lang/String;

    invoke-static {v0, v7}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    goto :goto_0

    .line 56700
    :cond_11
    instance-of v0, p2, Lcom/facebook/ads/internal/api/AdNativeComponentView;

    const/4 v8, 0x0

    if-eqz v0, :cond_14

    move-object v0, p2

    check-cast v0, Lcom/facebook/ads/internal/api/AdNativeComponentView;

    .line 56701
    invoke-virtual {v0}, Lcom/facebook/ads/internal/api/AdNativeComponentView;->getAdContentsView()Landroid/view/View;

    move-result-object v0

    if-eqz v0, :cond_14

    const/4 v1, 0x1

    .line 56702
    .local v8, "nativeAdViewIsValidAdNativeComponentView":Z
    :goto_1
    if-eqz p4, :cond_13

    instance-of v0, p2, Landroid/widget/ImageView;

    if-eqz v0, :cond_13

    const/4 v7, 0x1

    .line 56703
    .local v10, "nativeAdBannerViewIsImageView":Z
    :goto_2
    if-nez v1, :cond_15

    if-nez v7, :cond_15

    .line 56704
    iget-object v0, v2, Lcom/facebook/ads/redexgen/X/V2;->A0G:Lcom/facebook/ads/redexgen/X/V1;

    if-eqz v0, :cond_12

    .line 56705
    sget-object v4, Lcom/facebook/ads/internal/protocol/AdErrorType;->UNSUPPORTED_AD_ASSET_NATIVEAD:Lcom/facebook/ads/internal/protocol/AdErrorType;

    const/16 v3, 0x207

    const/16 v1, 0x1f

    const/16 v0, 0xf

    invoke-static {v3, v1, v0}, Lcom/facebook/ads/redexgen/X/V2;->A0W(III)Ljava/lang/String;

    move-result-object v0

    new-instance v6, Lcom/facebook/ads/redexgen/X/Jb;

    invoke-direct {v6, v4, v0}, Lcom/facebook/ads/redexgen/X/Jb;-><init>(Lcom/facebook/ads/internal/protocol/AdErrorType;Ljava/lang/String;)V

    .line 56706
    .restart local v7    # "error":Lcom/facebook/ads/redexgen/X/Jb;
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/V2;->A11()Lcom/facebook/ads/redexgen/X/Yn;

    move-result-object v0

    .line 56707
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/Yn;->A0E()Lcom/facebook/ads/redexgen/X/0S;

    move-result-object v5

    iget-wide v0, v2, Lcom/facebook/ads/redexgen/X/V2;->A00:J

    .line 56708
    invoke-static {v0, v1}, Lcom/facebook/ads/redexgen/X/Lf;->A01(J)J

    move-result-wide v3

    .line 56709
    invoke-virtual {v6}, Lcom/facebook/ads/redexgen/X/Jb;->A03()Lcom/facebook/ads/internal/protocol/AdErrorType;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/internal/protocol/AdErrorType;->getErrorCode()I

    move-result v1

    .line 56710
    invoke-virtual {v6}, Lcom/facebook/ads/redexgen/X/Jb;->A04()Ljava/lang/String;

    move-result-object v0

    .line 56711
    invoke-interface {v5, v3, v4, v1, v0}, Lcom/facebook/ads/redexgen/X/0S;->A2m(JILjava/lang/String;)V

    .line 56712
    iget-object v0, v2, Lcom/facebook/ads/redexgen/X/V2;->A0G:Lcom/facebook/ads/redexgen/X/V1;

    invoke-interface {v0, v6}, Lcom/facebook/ads/redexgen/X/JH;->ABR(Lcom/facebook/ads/redexgen/X/Jb;)V

    .line 56713
    .end local v7    # "error":Lcom/facebook/ads/redexgen/X/Jb;
    :cond_12
    return-void

    .line 56714
    :cond_13
    const/4 v7, 0x0

    goto :goto_2

    .line 56715
    :cond_14
    const/4 v1, 0x0

    goto :goto_1

    .line 56716
    :cond_15
    iget-object v0, v2, Lcom/facebook/ads/redexgen/X/V2;->A04:Landroid/view/View;

    if-eqz v0, :cond_17

    .line 56717
    sget-object v9, Lcom/facebook/ads/redexgen/X/V2;->A0n:Ljava/lang/String;

    const/16 v4, 0x11b

    const/16 v1, 0x50

    const/16 v0, 0x59

    invoke-static {v4, v1, v0}, Lcom/facebook/ads/redexgen/X/V2;->A0W(III)Ljava/lang/String;

    move-result-object v0

    invoke-static {v9, v0}, Landroid/util/Log;->w(Ljava/lang/String;Ljava/lang/String;)I

    sget-object v4, Lcom/facebook/ads/redexgen/X/V2;->A0m:[Ljava/lang/String;

    const/4 v0, 0x7

    aget-object v1, v4, v0

    const/4 v0, 0x5

    aget-object v0, v4, v0

    invoke-virtual {v1}, Ljava/lang/String;->length()I

    move-result v1

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v0

    if-eq v1, v0, :cond_16

    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    .line 56718
    :cond_16
    sget-object v4, Lcom/facebook/ads/redexgen/X/V2;->A0m:[Ljava/lang/String;

    const-string v1, "Yitqb4l5O5wHhToyiUZG328"

    const/4 v0, 0x7

    aput-object v1, v4, v0

    const-string v1, "n4uNZUYEyoiQJdMJgH58rRy"

    const/4 v0, 0x5

    aput-object v1, v4, v0

    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/V2;->unregisterView()V

    .line 56719
    :cond_17
    sget-object v10, Lcom/facebook/ads/redexgen/X/V2;->A0o:Ljava/util/WeakHashMap;

    invoke-virtual {v10, p1}, Ljava/util/WeakHashMap;->containsKey(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_18

    invoke-virtual {v10, p1}, Ljava/util/WeakHashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/lang/ref/WeakReference;

    invoke-virtual {v0}, Ljava/lang/ref/WeakReference;->get()Ljava/lang/Object;

    move-result-object v0

    if-eqz v0, :cond_18

    .line 56720
    sget-object v9, Lcom/facebook/ads/redexgen/X/V2;->A0n:Ljava/lang/String;

    const/16 v4, 0x196

    const/16 v1, 0x4b

    const/16 v0, 0xf

    invoke-static {v4, v1, v0}, Lcom/facebook/ads/redexgen/X/V2;->A0W(III)Ljava/lang/String;

    move-result-object v0

    invoke-static {v9, v0}, Landroid/util/Log;->w(Ljava/lang/String;Ljava/lang/String;)I

    .line 56721
    invoke-virtual {v10, p1}, Ljava/util/WeakHashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/lang/ref/WeakReference;

    invoke-virtual {v0}, Ljava/lang/ref/WeakReference;->get()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/facebook/ads/redexgen/X/V2;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/V2;->unregisterView()V

    .line 56722
    :cond_18
    iget-object v4, v2, Lcom/facebook/ads/redexgen/X/V2;->A0c:Lcom/facebook/ads/redexgen/X/Yn;

    const/4 v1, 0x0

    new-instance v0, Lcom/facebook/ads/redexgen/X/V4;

    invoke-direct {v0, v2, v4, v1}, Lcom/facebook/ads/redexgen/X/V4;-><init>(Lcom/facebook/ads/redexgen/X/V2;Lcom/facebook/ads/redexgen/X/Yn;Lcom/facebook/ads/redexgen/X/VD;)V

    iput-object v0, v2, Lcom/facebook/ads/redexgen/X/V2;->A0F:Lcom/facebook/ads/redexgen/X/V4;

    .line 56723
    iput-object p1, v2, Lcom/facebook/ads/redexgen/X/V2;->A04:Landroid/view/View;

    .line 56724
    iput-object p2, v2, Lcom/facebook/ads/redexgen/X/V2;->A06:Landroid/view/View;

    .line 56725
    instance-of v0, p1, Landroid/view/ViewGroup;

    if-eqz v0, :cond_19

    .line 56726
    iget-object v4, v2, Lcom/facebook/ads/redexgen/X/V2;->A0c:Lcom/facebook/ads/redexgen/X/Yn;

    new-instance v0, Lcom/facebook/ads/redexgen/X/V8;

    invoke-direct {v0, v2}, Lcom/facebook/ads/redexgen/X/V8;-><init>(Lcom/facebook/ads/redexgen/X/V2;)V

    new-instance v1, Lcom/facebook/ads/redexgen/X/Mi;

    invoke-direct {v1, v4, v0}, Lcom/facebook/ads/redexgen/X/Mi;-><init>(Lcom/facebook/ads/redexgen/X/Yn;Lcom/facebook/ads/redexgen/X/Mh;)V

    iput-object v1, v2, Lcom/facebook/ads/redexgen/X/V2;->A0K:Lcom/facebook/ads/redexgen/X/Mi;

    .line 56727
    move-object v0, p1

    check-cast v0, Landroid/view/ViewGroup;

    invoke-virtual {v0, v1}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 56728
    :cond_19
    invoke-virtual {v5}, Lcom/facebook/ads/redexgen/X/bK;->A0S()Z

    move-result v0

    if-eqz v0, :cond_1a

    .line 56729
    new-instance v0, Lcom/facebook/ads/redexgen/X/V7;

    invoke-direct {v0, v2}, Lcom/facebook/ads/redexgen/X/V7;-><init>(Lcom/facebook/ads/redexgen/X/V2;)V

    iput-object v0, v2, Lcom/facebook/ads/redexgen/X/V2;->A0O:Lcom/facebook/ads/redexgen/X/RD;

    .line 56730
    iget-object v0, v2, Lcom/facebook/ads/redexgen/X/V2;->A0O:Lcom/facebook/ads/redexgen/X/RD;

    new-instance v4, Ljava/lang/ref/WeakReference;

    invoke-direct {v4, v0}, Ljava/lang/ref/WeakReference;-><init>(Ljava/lang/Object;)V

    iget-object v1, v2, Lcom/facebook/ads/redexgen/X/V2;->A0c:Lcom/facebook/ads/redexgen/X/Yn;

    new-instance v0, Lcom/facebook/ads/redexgen/X/RE;

    invoke-direct {v0, p1, v3, v4, v1}, Lcom/facebook/ads/redexgen/X/RE;-><init>(Landroid/view/View;ILjava/lang/ref/WeakReference;Lcom/facebook/ads/redexgen/X/Yn;)V

    iput-object v0, v2, Lcom/facebook/ads/redexgen/X/V2;->A0Q:Lcom/facebook/ads/redexgen/X/RE;

    .line 56731
    invoke-virtual {v0, v8}, Lcom/facebook/ads/redexgen/X/RE;->A0Y(Z)V

    .line 56732
    iget-object v1, v2, Lcom/facebook/ads/redexgen/X/V2;->A0Q:Lcom/facebook/ads/redexgen/X/RE;

    .line 56733
    invoke-virtual {v5}, Lcom/facebook/ads/redexgen/X/bK;->A09()I

    move-result v0

    .line 56734
    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/RE;->A0X(I)V

    .line 56735
    iget-object v0, v2, Lcom/facebook/ads/redexgen/X/V2;->A0Q:Lcom/facebook/ads/redexgen/X/RE;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/RE;->A0U()V

    .line 56736
    iget-object v0, v2, Lcom/facebook/ads/redexgen/X/V2;->A0c:Lcom/facebook/ads/redexgen/X/Yn;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/Yn;->A0E()Lcom/facebook/ads/redexgen/X/0S;

    move-result-object v0

    invoke-interface {v0}, Lcom/facebook/ads/redexgen/X/0S;->AAT()V

    .line 56737
    :cond_1a
    new-instance v4, Ljava/util/ArrayList;

    invoke-direct {v4, v6}, Ljava/util/ArrayList;-><init>(Ljava/util/Collection;)V

    .line 56738
    .local v9, "copyOfClickableViews":Ljava/util/List;, "Ljava/util/List<Landroid/view/View;>;"
    iget-object v0, v2, Lcom/facebook/ads/redexgen/X/V2;->A05:Landroid/view/View;

    if-eqz v0, :cond_1b

    .line 56739
    invoke-interface {v4, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 56740
    :cond_1b
    invoke-interface {v4}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :goto_3
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_1c

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroid/view/View;

    .line 56741
    .local v12, "v":Landroid/view/View;
    invoke-virtual {v2, v0}, Lcom/facebook/ads/redexgen/X/V2;->A1K(Landroid/view/View;)V

    .line 56742
    .end local v12    # "v":Landroid/view/View;
    goto :goto_3

    .line 56743
    :cond_1c
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/V2;->A00()I

    move-result v8

    .line 56744
    .local v11, "viewabilityThreshold":I
    new-instance v0, Lcom/facebook/ads/redexgen/X/V6;

    invoke-direct {v0, v2, p2, v7, v5}, Lcom/facebook/ads/redexgen/X/V6;-><init>(Lcom/facebook/ads/redexgen/X/V2;Landroid/view/View;ZLcom/facebook/ads/redexgen/X/bK;)V

    iput-object v0, v2, Lcom/facebook/ads/redexgen/X/V2;->A0P:Lcom/facebook/ads/redexgen/X/RD;

    .line 56745
    instance-of v0, p2, Lcom/facebook/ads/internal/api/AdNativeComponentView;

    if-eqz v0, :cond_25

    .line 56746
    check-cast p2, Lcom/facebook/ads/internal/api/AdNativeComponentView;

    invoke-virtual {p2}, Lcom/facebook/ads/internal/api/AdNativeComponentView;->getAdContentsView()Landroid/view/View;

    move-result-object v0

    iput-object v0, v2, Lcom/facebook/ads/redexgen/X/V2;->A03:Landroid/view/View;

    .line 56747
    :goto_4
    iget-object v0, v2, Lcom/facebook/ads/redexgen/X/V2;->A0c:Lcom/facebook/ads/redexgen/X/Yn;

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/Ih;->A1W(Landroid/content/Context;)Z

    move-result v0

    if-eqz v0, :cond_1e

    .line 56748
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/V2;->A1B()Ljava/lang/String;

    move-result-object v1

    .line 56749
    .local v12, "clientToken":Ljava/lang/String;
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/V2;->A11()Lcom/facebook/ads/redexgen/X/Yn;

    move-result-object v0

    .line 56750
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/7f;->A0A()Lcom/facebook/ads/redexgen/X/JE;

    move-result-object v7

    iget-object v6, v2, Lcom/facebook/ads/redexgen/X/V2;->A03:Landroid/view/View;

    .line 56751
    if-nez v1, :cond_1d

    const/4 v5, 0x0

    const/4 v1, 0x0

    const/16 v0, 0x15

    invoke-static {v5, v1, v0}, Lcom/facebook/ads/redexgen/X/V2;->A0W(III)Ljava/lang/String;

    move-result-object v1

    :cond_1d
    instance-of v0, v6, Lcom/facebook/ads/redexgen/X/QJ;

    .line 56752
    invoke-interface {v7, v6, v1, v0, v3}, Lcom/facebook/ads/redexgen/X/JE;->AGw(Landroid/view/View;Ljava/lang/String;ZZ)V

    .line 56753
    .end local v12    # "clientToken":Ljava/lang/String;
    :cond_1e
    new-instance v6, Lcom/facebook/ads/redexgen/X/RE;

    iget-object v7, v2, Lcom/facebook/ads/redexgen/X/V2;->A03:Landroid/view/View;

    .line 56754
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/V2;->A03()I

    move-result v9

    const/4 v10, 0x1

    iget-object v0, v2, Lcom/facebook/ads/redexgen/X/V2;->A0P:Lcom/facebook/ads/redexgen/X/RD;

    new-instance v11, Ljava/lang/ref/WeakReference;

    invoke-direct {v11, v0}, Ljava/lang/ref/WeakReference;-><init>(Ljava/lang/Object;)V

    iget-object v12, v2, Lcom/facebook/ads/redexgen/X/V2;->A0c:Lcom/facebook/ads/redexgen/X/Yn;

    invoke-direct/range {v6 .. v12}, Lcom/facebook/ads/redexgen/X/RE;-><init>(Landroid/view/View;IIZLjava/lang/ref/WeakReference;Lcom/facebook/ads/redexgen/X/Yn;)V

    iput-object v6, v2, Lcom/facebook/ads/redexgen/X/V2;->A0R:Lcom/facebook/ads/redexgen/X/RE;

    .line 56755
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/V2;->A0q()Z

    move-result v0

    xor-int/2addr v3, v0

    invoke-virtual {v6, v3}, Lcom/facebook/ads/redexgen/X/RE;->A0Y(Z)V

    .line 56756
    iget-object v1, v2, Lcom/facebook/ads/redexgen/X/V2;->A0R:Lcom/facebook/ads/redexgen/X/RE;

    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/V2;->A01()I

    move-result v0

    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/RE;->A0W(I)V

    .line 56757
    iget-object v1, v2, Lcom/facebook/ads/redexgen/X/V2;->A0R:Lcom/facebook/ads/redexgen/X/RE;

    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/V2;->A02()I

    move-result v0

    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/RE;->A0X(I)V

    .line 56758
    iget-object v1, v2, Lcom/facebook/ads/redexgen/X/V2;->A03:Landroid/view/View;

    instance-of v0, v1, Lcom/facebook/ads/redexgen/X/NW;

    if-eqz v0, :cond_1f

    .line 56759
    check-cast v1, Lcom/facebook/ads/redexgen/X/NW;

    iget-object v0, v2, Lcom/facebook/ads/redexgen/X/V2;->A0R:Lcom/facebook/ads/redexgen/X/RE;

    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/NW;->A06(Lcom/facebook/ads/redexgen/X/RE;)V

    .line 56760
    :cond_1f
    iget-object v6, v2, Lcom/facebook/ads/redexgen/X/V2;->A0c:Lcom/facebook/ads/redexgen/X/Yn;

    const/4 v0, 0x0

    new-instance v5, Lcom/facebook/ads/redexgen/X/V3;

    invoke-direct {v5, v2, v0}, Lcom/facebook/ads/redexgen/X/V3;-><init>(Lcom/facebook/ads/redexgen/X/V2;Lcom/facebook/ads/redexgen/X/VD;)V

    iget-object v3, v2, Lcom/facebook/ads/redexgen/X/V2;->A0R:Lcom/facebook/ads/redexgen/X/RE;

    iget-object v1, v2, Lcom/facebook/ads/redexgen/X/V2;->A0a:Lcom/facebook/ads/redexgen/X/bK;

    new-instance v0, Lcom/facebook/ads/redexgen/X/bA;

    invoke-direct {v0, v6, v5, v3, v1}, Lcom/facebook/ads/redexgen/X/bA;-><init>(Lcom/facebook/ads/redexgen/X/Yn;Lcom/facebook/ads/redexgen/X/0s;Lcom/facebook/ads/redexgen/X/RE;Lcom/facebook/ads/redexgen/X/bK;)V

    iput-object v0, v2, Lcom/facebook/ads/redexgen/X/V2;->A09:Lcom/facebook/ads/redexgen/X/bA;

    .line 56761
    invoke-virtual {v0, v4}, Lcom/facebook/ads/redexgen/X/bA;->A0D(Ljava/util/List;)V

    .line 56762
    sget-object v1, Lcom/facebook/ads/redexgen/X/V2;->A0o:Ljava/util/WeakHashMap;

    new-instance v0, Ljava/lang/ref/WeakReference;

    invoke-direct {v0, v2}, Ljava/lang/ref/WeakReference;-><init>(Ljava/lang/Object;)V

    invoke-virtual {v1, p1, v0}, Ljava/util/WeakHashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 56763
    sget v1, Landroid/os/Build$VERSION;->SDK_INT:I

    const/16 v0, 0x12

    if-lt v1, v0, :cond_22

    iget-object v0, v2, Lcom/facebook/ads/redexgen/X/V2;->A0c:Lcom/facebook/ads/redexgen/X/Yn;

    .line 56764
    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/Ih;->A11(Landroid/content/Context;)Z

    move-result v0

    if-eqz v0, :cond_22

    .line 56765
    new-instance v1, Lcom/facebook/ads/redexgen/X/Ng;

    invoke-direct {v1}, Lcom/facebook/ads/redexgen/X/Ng;-><init>()V

    iput-object v1, v2, Lcom/facebook/ads/redexgen/X/V2;->A0L:Lcom/facebook/ads/redexgen/X/Ng;

    .line 56766
    iget-object v0, v2, Lcom/facebook/ads/redexgen/X/V2;->A0g:Ljava/lang/String;

    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/Ng;->A0C(Ljava/lang/String;)V

    .line 56767
    iget-object v1, v2, Lcom/facebook/ads/redexgen/X/V2;->A0L:Lcom/facebook/ads/redexgen/X/Ng;

    iget-object v0, v2, Lcom/facebook/ads/redexgen/X/V2;->A0c:Lcom/facebook/ads/redexgen/X/Yn;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/Yn;->getPackageName()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/Ng;->A0B(Ljava/lang/String;)V

    .line 56768
    iget-object v1, v2, Lcom/facebook/ads/redexgen/X/V2;->A0L:Lcom/facebook/ads/redexgen/X/Ng;

    iget-object v0, v2, Lcom/facebook/ads/redexgen/X/V2;->A0R:Lcom/facebook/ads/redexgen/X/RE;

    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/Ng;->A0A(Lcom/facebook/ads/redexgen/X/RE;)V

    .line 56769
    iget-object v0, v2, Lcom/facebook/ads/redexgen/X/V2;->A0a:Lcom/facebook/ads/redexgen/X/bK;

    if-eqz v0, :cond_20

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/bK;->A0E()Lcom/facebook/ads/redexgen/X/14;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/14;->A03()I

    move-result v0

    if-lez v0, :cond_20

    .line 56770
    iget-object v0, v2, Lcom/facebook/ads/redexgen/X/V2;->A0a:Lcom/facebook/ads/redexgen/X/bK;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/bK;->A0E()Lcom/facebook/ads/redexgen/X/14;

    move-result-object v0

    .line 56771
    .local v2, "nativeAdModel":Lcom/facebook/ads/redexgen/X/14;
    iget-object v3, v2, Lcom/facebook/ads/redexgen/X/V2;->A0L:Lcom/facebook/ads/redexgen/X/Ng;

    .line 56772
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/14;->A03()I

    move-result v1

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/14;->A04()I

    move-result v0

    .line 56773
    invoke-virtual {v3, v1, v0}, Lcom/facebook/ads/redexgen/X/Ng;->A08(II)V

    .line 56774
    .end local v2    # "nativeAdModel":Lcom/facebook/ads/redexgen/X/14;
    :cond_20
    iget-object v0, v2, Lcom/facebook/ads/redexgen/X/V2;->A0D:Lcom/facebook/ads/redexgen/X/8T;

    if-eqz v0, :cond_24

    .line 56775
    iget-object v3, v2, Lcom/facebook/ads/redexgen/X/V2;->A0L:Lcom/facebook/ads/redexgen/X/Ng;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/8T;->A0C()J

    move-result-wide v0

    invoke-virtual {v3, v0, v1}, Lcom/facebook/ads/redexgen/X/Ng;->A09(J)V

    .line 56776
    :cond_21
    :goto_5
    iget-object v4, v2, Lcom/facebook/ads/redexgen/X/V2;->A04:Landroid/view/View;

    sget-object v3, Lcom/facebook/ads/redexgen/X/V2;->A0m:[Ljava/lang/String;

    const/4 v0, 0x4

    aget-object v1, v3, v0

    const/4 v0, 0x6

    aget-object v0, v3, v0

    invoke-virtual {v1}, Ljava/lang/String;->length()I

    move-result v1

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v0

    if-eq v1, v0, :cond_23

    sget-object v3, Lcom/facebook/ads/redexgen/X/V2;->A0m:[Ljava/lang/String;

    const-string v1, "DFdj5aRKmThGOYGM"

    const/4 v0, 0x4

    aput-object v1, v3, v0

    const-string v1, "4rU7VADNdisg"

    const/4 v0, 0x6

    aput-object v1, v3, v0

    invoke-virtual {v4}, Landroid/view/View;->getOverlay()Landroid/view/ViewOverlay;

    move-result-object v1

    iget-object v0, v2, Lcom/facebook/ads/redexgen/X/V2;->A0L:Lcom/facebook/ads/redexgen/X/Ng;

    invoke-virtual {v1, v0}, Landroid/view/ViewOverlay;->add(Landroid/graphics/drawable/Drawable;)V

    .line 56777
    :cond_22
    :goto_6
    return-void

    :cond_23
    sget-object v3, Lcom/facebook/ads/redexgen/X/V2;->A0m:[Ljava/lang/String;

    const-string v1, "HovrQjOKGOU1glve9p8eAJs"

    const/4 v0, 0x7

    aput-object v1, v3, v0

    const-string v1, "lnqkcQfVF9X2NaZpGo2NogE"

    const/4 v0, 0x5

    aput-object v1, v3, v0

    invoke-virtual {v4}, Landroid/view/View;->getOverlay()Landroid/view/ViewOverlay;

    move-result-object v1

    iget-object v0, v2, Lcom/facebook/ads/redexgen/X/V2;->A0L:Lcom/facebook/ads/redexgen/X/Ng;

    invoke-virtual {v1, v0}, Landroid/view/ViewOverlay;->add(Landroid/graphics/drawable/Drawable;)V

    goto :goto_6

    .line 56778
    :cond_24
    iget-object v0, v2, Lcom/facebook/ads/redexgen/X/V2;->A0A:Lcom/facebook/ads/redexgen/X/FO;

    if-eqz v0, :cond_21

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/am;->A0H()Lcom/facebook/ads/redexgen/X/8T;

    move-result-object v0

    if-eqz v0, :cond_21

    .line 56779
    iget-object v3, v2, Lcom/facebook/ads/redexgen/X/V2;->A0L:Lcom/facebook/ads/redexgen/X/Ng;

    iget-object v0, v2, Lcom/facebook/ads/redexgen/X/V2;->A0A:Lcom/facebook/ads/redexgen/X/FO;

    .line 56780
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/am;->A0H()Lcom/facebook/ads/redexgen/X/8T;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/8T;->A0C()J

    move-result-wide v0

    .line 56781
    invoke-virtual {v3, v0, v1}, Lcom/facebook/ads/redexgen/X/Ng;->A09(J)V

    goto :goto_5

    .line 56782
    :cond_25
    iput-object p2, v2, Lcom/facebook/ads/redexgen/X/V2;->A03:Landroid/view/View;

    goto/16 :goto_4
.end method

.method private A0g(Landroid/widget/FrameLayout;Ljava/lang/String;)V
    .locals 3

    .line 56783
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0N:Lcom/facebook/ads/redexgen/X/Px;

    if-eqz v0, :cond_0

    .line 56784
    invoke-virtual {p1, v0}, Landroid/widget/FrameLayout;->removeView(Landroid/view/View;)V

    .line 56785
    :cond_0
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0c:Lcom/facebook/ads/redexgen/X/Yn;

    .line 56786
    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/5c;->A03(Landroid/content/Context;)Lcom/facebook/ads/redexgen/X/Yn;

    move-result-object v0

    .line 56787
    invoke-static {v0, p2}, Lcom/facebook/ads/redexgen/X/Pw;->A01(Lcom/facebook/ads/redexgen/X/Yn;Ljava/lang/String;)Lcom/facebook/ads/redexgen/X/Px;

    move-result-object v2

    iput-object v2, p0, Lcom/facebook/ads/redexgen/X/V2;->A0N:Lcom/facebook/ads/redexgen/X/Px;

    .line 56788
    if-eqz v2, :cond_1

    .line 56789
    const/4 v1, -0x1

    new-instance v0, Landroid/widget/FrameLayout$LayoutParams;

    invoke-direct {v0, v1, v1}, Landroid/widget/FrameLayout$LayoutParams;-><init>(II)V

    invoke-virtual {p1, v2, v0}, Landroid/widget/FrameLayout;->addView(Landroid/view/View;Landroid/view/ViewGroup$LayoutParams;)V

    .line 56790
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0N:Lcom/facebook/ads/redexgen/X/Px;

    invoke-virtual {p1, v0}, Landroid/widget/FrameLayout;->bringChildToFront(Landroid/view/View;)V

    .line 56791
    :cond_1
    return-void
.end method

.method private A0h(Lcom/facebook/ads/redexgen/X/bK;Z)V
    .locals 14

    .line 56792
    move-object v0, p0

    if-nez p1, :cond_0

    .line 56793
    return-void

    .line 56794
    :cond_0
    iget-object v2, v0, Lcom/facebook/ads/redexgen/X/V2;->A0E:Lcom/facebook/ads/redexgen/X/JL;

    sget-object v1, Lcom/facebook/ads/redexgen/X/JL;->A04:Lcom/facebook/ads/redexgen/X/JL;

    invoke-virtual {v2, v1}, Lcom/facebook/ads/redexgen/X/JL;->equals(Ljava/lang/Object;)Z

    move-result v4

    const/16 v3, 0x254

    const/4 v2, 0x6

    const/16 v1, 0x59

    invoke-static {v3, v2, v1}, Lcom/facebook/ads/redexgen/X/V2;->A0W(III)Ljava/lang/String;

    move-result-object v1

    if-eqz v4, :cond_8

    .line 56795
    invoke-virtual {p1}, Lcom/facebook/ads/redexgen/X/bK;->A0E()Lcom/facebook/ads/redexgen/X/14;

    move-result-object v7

    .line 56796
    .local v2, "nativeAdModel":Lcom/facebook/ads/redexgen/X/14;
    invoke-virtual {p1}, Lcom/facebook/ads/redexgen/X/bK;->A6T()Ljava/lang/String;

    move-result-object v4

    .line 56797
    .local v4, "clientToken":Ljava/lang/String;
    invoke-static {v4}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v2

    if-nez v2, :cond_1

    .line 56798
    iget-object v2, v0, Lcom/facebook/ads/redexgen/X/V2;->A0c:Lcom/facebook/ads/redexgen/X/Yn;

    .line 56799
    invoke-virtual {v2}, Lcom/facebook/ads/redexgen/X/7f;->A09()Lcom/facebook/ads/redexgen/X/J2;

    move-result-object v2

    new-instance v3, Lcom/facebook/ads/redexgen/X/JA;

    invoke-direct {v3, v4, v2}, Lcom/facebook/ads/redexgen/X/JA;-><init>(Ljava/lang/String;Lcom/facebook/ads/redexgen/X/J2;)V

    .line 56800
    .local v5, "funnelLoggingHandler":Lcom/facebook/ads/redexgen/X/JA;
    iget-object v2, v0, Lcom/facebook/ads/redexgen/X/V2;->A0b:Lcom/facebook/ads/redexgen/X/6c;

    invoke-virtual {v2, v3}, Lcom/facebook/ads/redexgen/X/6c;->A0d(Lcom/facebook/ads/redexgen/X/JA;)V

    .line 56801
    .end local v5    # "funnelLoggingHandler":Lcom/facebook/ads/redexgen/X/JA;
    :cond_1
    invoke-virtual {v7}, Lcom/facebook/ads/redexgen/X/14;->A0G()Lcom/facebook/ads/redexgen/X/JP;

    move-result-object v2

    if-eqz v2, :cond_2

    .line 56802
    new-instance v8, Lcom/facebook/ads/redexgen/X/6a;

    .line 56803
    invoke-virtual {v7}, Lcom/facebook/ads/redexgen/X/14;->A0G()Lcom/facebook/ads/redexgen/X/JP;

    move-result-object v2

    invoke-virtual {v2}, Lcom/facebook/ads/redexgen/X/JP;->getUrl()Ljava/lang/String;

    move-result-object v9

    .line 56804
    invoke-virtual {v7}, Lcom/facebook/ads/redexgen/X/14;->A0G()Lcom/facebook/ads/redexgen/X/JP;

    move-result-object v2

    invoke-virtual {v2}, Lcom/facebook/ads/redexgen/X/JP;->getHeight()I

    move-result v10

    .line 56805
    invoke-virtual {v7}, Lcom/facebook/ads/redexgen/X/14;->A0G()Lcom/facebook/ads/redexgen/X/JP;

    move-result-object v2

    invoke-virtual {v2}, Lcom/facebook/ads/redexgen/X/JP;->getWidth()I

    move-result v11

    .line 56806
    invoke-virtual {p1}, Lcom/facebook/ads/redexgen/X/bK;->A0G()Ljava/lang/String;

    move-result-object v12

    const/16 v4, 0x254

    const/4 v3, 0x6

    const/16 v2, 0x59

    invoke-static {v4, v3, v2}, Lcom/facebook/ads/redexgen/X/V2;->A0W(III)Ljava/lang/String;

    move-result-object v13

    invoke-direct/range {v8 .. v13}, Lcom/facebook/ads/redexgen/X/6a;-><init>(Ljava/lang/String;IILjava/lang/String;Ljava/lang/String;)V

    .line 56807
    .local v5, "adIconImageData":Lcom/facebook/ads/redexgen/X/6a;
    iget-object v2, v0, Lcom/facebook/ads/redexgen/X/V2;->A0C:Lcom/facebook/ads/redexgen/X/6i;

    iput-object v2, v8, Lcom/facebook/ads/redexgen/X/6a;->A00:Lcom/facebook/ads/redexgen/X/6i;

    .line 56808
    iget-object v2, v0, Lcom/facebook/ads/redexgen/X/V2;->A0b:Lcom/facebook/ads/redexgen/X/6c;

    invoke-virtual {v2}, Lcom/facebook/ads/redexgen/X/6c;->A0V()V

    .line 56809
    iget-object v2, v0, Lcom/facebook/ads/redexgen/X/V2;->A0b:Lcom/facebook/ads/redexgen/X/6c;

    invoke-virtual {v2, v8}, Lcom/facebook/ads/redexgen/X/6c;->A0b(Lcom/facebook/ads/redexgen/X/6a;)V

    .line 56810
    .end local v5    # "adIconImageData":Lcom/facebook/ads/redexgen/X/6a;
    :cond_2
    iget-object v3, v0, Lcom/facebook/ads/redexgen/X/V2;->A0J:Lcom/facebook/ads/redexgen/X/Jg;

    sget-object v2, Lcom/facebook/ads/redexgen/X/Jg;->A04:Lcom/facebook/ads/redexgen/X/Jg;

    invoke-virtual {v3, v2}, Lcom/facebook/ads/redexgen/X/Jg;->equals(Ljava/lang/Object;)Z

    move-result v2

    if-nez v2, :cond_7

    .line 56811
    invoke-virtual {v7}, Lcom/facebook/ads/redexgen/X/14;->A0F()Lcom/facebook/ads/redexgen/X/JP;

    move-result-object v2

    if-eqz v2, :cond_3

    .line 56812
    iget-object v5, v0, Lcom/facebook/ads/redexgen/X/V2;->A0b:Lcom/facebook/ads/redexgen/X/6c;

    new-instance v8, Lcom/facebook/ads/redexgen/X/6a;

    .line 56813
    invoke-virtual {v7}, Lcom/facebook/ads/redexgen/X/14;->A0F()Lcom/facebook/ads/redexgen/X/JP;

    move-result-object v2

    invoke-virtual {v2}, Lcom/facebook/ads/redexgen/X/JP;->getUrl()Ljava/lang/String;

    move-result-object v9

    .line 56814
    invoke-virtual {v7}, Lcom/facebook/ads/redexgen/X/14;->A0F()Lcom/facebook/ads/redexgen/X/JP;

    move-result-object v2

    invoke-virtual {v2}, Lcom/facebook/ads/redexgen/X/JP;->getHeight()I

    move-result v10

    .line 56815
    invoke-virtual {v7}, Lcom/facebook/ads/redexgen/X/14;->A0F()Lcom/facebook/ads/redexgen/X/JP;

    move-result-object v2

    invoke-virtual {v2}, Lcom/facebook/ads/redexgen/X/JP;->getWidth()I

    move-result v11

    .line 56816
    invoke-virtual {p1}, Lcom/facebook/ads/redexgen/X/bK;->A0G()Ljava/lang/String;

    move-result-object v12

    const/16 v4, 0x254

    const/4 v3, 0x6

    const/16 v2, 0x59

    invoke-static {v4, v3, v2}, Lcom/facebook/ads/redexgen/X/V2;->A0W(III)Ljava/lang/String;

    move-result-object v13

    invoke-direct/range {v8 .. v13}, Lcom/facebook/ads/redexgen/X/6a;-><init>(Ljava/lang/String;IILjava/lang/String;Ljava/lang/String;)V

    .line 56817
    invoke-virtual {v5, v8}, Lcom/facebook/ads/redexgen/X/6c;->A0b(Lcom/facebook/ads/redexgen/X/6a;)V

    .line 56818
    :cond_3
    invoke-virtual {p1}, Lcom/facebook/ads/redexgen/X/bK;->A0H()Ljava/util/List;

    move-result-object v5

    sget-object v4, Lcom/facebook/ads/redexgen/X/V2;->A0m:[Ljava/lang/String;

    const/4 v2, 0x1

    aget-object v3, v4, v2

    const/4 v2, 0x3

    aget-object v4, v4, v2

    const/16 v2, 0x10

    invoke-virtual {v3, v2}, Ljava/lang/String;->charAt(I)C

    move-result v3

    invoke-virtual {v4, v2}, Ljava/lang/String;->charAt(I)C

    move-result v2

    if-eq v3, v2, :cond_5

    sget-object v4, Lcom/facebook/ads/redexgen/X/V2;->A0m:[Ljava/lang/String;

    const-string v3, "BiVeVWtUTguxpSjeMqAkCwQXeKWOVTz4"

    const/4 v2, 0x1

    aput-object v3, v4, v2

    const-string v3, "8sdR9nWPZ664Gt9BvBocaDJi5It1hBkX"

    const/4 v2, 0x3

    aput-object v3, v4, v2

    if-eqz v5, :cond_6

    .line 56819
    invoke-virtual {p1}, Lcom/facebook/ads/redexgen/X/bK;->A0H()Ljava/util/List;

    move-result-object v2

    invoke-interface {v2}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v6

    :cond_4
    :goto_0
    invoke-interface {v6}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_6

    invoke-interface {v6}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lcom/facebook/ads/redexgen/X/V2;

    .line 56820
    .local v6, "carouselAd":Lcom/facebook/ads/redexgen/X/V2;
    invoke-virtual {v3}, Lcom/facebook/ads/redexgen/X/V2;->A13()Lcom/facebook/ads/redexgen/X/JP;

    move-result-object v2

    if-eqz v2, :cond_4

    .line 56821
    iget-object v5, v0, Lcom/facebook/ads/redexgen/X/V2;->A0b:Lcom/facebook/ads/redexgen/X/6c;

    new-instance v8, Lcom/facebook/ads/redexgen/X/6a;

    .line 56822
    invoke-virtual {v3}, Lcom/facebook/ads/redexgen/X/V2;->A13()Lcom/facebook/ads/redexgen/X/JP;

    move-result-object v2

    invoke-virtual {v2}, Lcom/facebook/ads/redexgen/X/JP;->getUrl()Ljava/lang/String;

    move-result-object v9

    .line 56823
    invoke-virtual {v3}, Lcom/facebook/ads/redexgen/X/V2;->A13()Lcom/facebook/ads/redexgen/X/JP;

    move-result-object v2

    invoke-virtual {v2}, Lcom/facebook/ads/redexgen/X/JP;->getHeight()I

    move-result v10

    .line 56824
    invoke-virtual {v3}, Lcom/facebook/ads/redexgen/X/V2;->A13()Lcom/facebook/ads/redexgen/X/JP;

    move-result-object v2

    invoke-virtual {v2}, Lcom/facebook/ads/redexgen/X/JP;->getWidth()I

    move-result v11

    .line 56825
    invoke-virtual {p1}, Lcom/facebook/ads/redexgen/X/bK;->A0G()Ljava/lang/String;

    move-result-object v12

    const/16 v4, 0x254

    const/4 v3, 0x6

    const/16 v2, 0x59

    invoke-static {v4, v3, v2}, Lcom/facebook/ads/redexgen/X/V2;->A0W(III)Ljava/lang/String;

    move-result-object v13

    invoke-direct/range {v8 .. v13}, Lcom/facebook/ads/redexgen/X/6a;-><init>(Ljava/lang/String;IILjava/lang/String;Ljava/lang/String;)V

    .line 56826
    invoke-virtual {v5, v8}, Lcom/facebook/ads/redexgen/X/6c;->A0b(Lcom/facebook/ads/redexgen/X/6a;)V

    goto :goto_0

    :cond_5
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    .line 56827
    :cond_6
    invoke-virtual {v7}, Lcom/facebook/ads/redexgen/X/14;->A0d()Ljava/lang/String;

    move-result-object v9

    .line 56828
    .local v5, "videoUrl":Ljava/lang/String;
    invoke-static {v9}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v2

    if-nez v2, :cond_7

    .line 56829
    iget-object v5, v0, Lcom/facebook/ads/redexgen/X/V2;->A0b:Lcom/facebook/ads/redexgen/X/6c;

    new-instance v8, Lcom/facebook/ads/redexgen/X/6Y;

    .line 56830
    invoke-virtual {p1}, Lcom/facebook/ads/redexgen/X/bK;->A0G()Ljava/lang/String;

    move-result-object v10

    const/16 v4, 0x254

    const/4 v3, 0x6

    const/16 v2, 0x59

    invoke-static {v4, v3, v2}, Lcom/facebook/ads/redexgen/X/V2;->A0W(III)Ljava/lang/String;

    move-result-object v11

    .line 56831
    invoke-virtual {v7}, Lcom/facebook/ads/redexgen/X/14;->A0A()J

    move-result-wide v12

    invoke-direct/range {v8 .. v13}, Lcom/facebook/ads/redexgen/X/6Y;-><init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;J)V

    .line 56832
    invoke-virtual {v5, v8}, Lcom/facebook/ads/redexgen/X/6c;->A0a(Lcom/facebook/ads/redexgen/X/6Y;)V

    .line 56833
    .end local v5    # "videoUrl":Ljava/lang/String;
    :cond_7
    invoke-virtual {v7}, Lcom/facebook/ads/redexgen/X/14;->A0D()Lcom/facebook/ads/redexgen/X/b5;

    move-result-object v4

    .line 56834
    .local v5, "adDataBundle":Lcom/facebook/ads/redexgen/X/b5;
    if-eqz v4, :cond_8

    .line 56835
    invoke-virtual {v4}, Lcom/facebook/ads/redexgen/X/1F;->A0m()Z

    move-result v2

    if-eqz v2, :cond_8

    .line 56836
    invoke-virtual {v4}, Lcom/facebook/ads/redexgen/X/1F;->A0M()Ljava/lang/String;

    move-result-object v3

    .line 56837
    invoke-virtual {v4}, Lcom/facebook/ads/redexgen/X/1F;->A0S()Ljava/lang/String;

    move-result-object v2

    new-instance v5, Lcom/facebook/ads/redexgen/X/6Y;

    invoke-direct {v5, v3, v2, v1}, Lcom/facebook/ads/redexgen/X/6Y;-><init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    .line 56838
    .local v6, "cacheFileData":Lcom/facebook/ads/redexgen/X/6Y;
    const/4 v2, 0x1

    iput-boolean v2, v5, Lcom/facebook/ads/redexgen/X/6Y;->A04:Z

    .line 56839
    const/4 v4, 0x0

    const/4 v3, 0x5

    const/4 v2, 0x0

    invoke-static {v4, v3, v2}, Lcom/facebook/ads/redexgen/X/V2;->A0W(III)Ljava/lang/String;

    move-result-object v2

    iput-object v2, v5, Lcom/facebook/ads/redexgen/X/6Y;->A03:Ljava/lang/String;

    .line 56840
    iget-object v2, v0, Lcom/facebook/ads/redexgen/X/V2;->A0b:Lcom/facebook/ads/redexgen/X/6c;

    invoke-virtual {v2, v5}, Lcom/facebook/ads/redexgen/X/6c;->A0X(Lcom/facebook/ads/redexgen/X/6Y;)V

    .line 56841
    .end local v2    # "nativeAdModel":Lcom/facebook/ads/redexgen/X/14;
    .end local v4    # "clientToken":Ljava/lang/String;
    .end local v5    # "adDataBundle":Lcom/facebook/ads/redexgen/X/b5;
    .end local v6    # "cacheFileData":Lcom/facebook/ads/redexgen/X/6Y;
    :cond_8
    iget-object v4, v0, Lcom/facebook/ads/redexgen/X/V2;->A0b:Lcom/facebook/ads/redexgen/X/6c;

    new-instance v3, Lcom/facebook/ads/redexgen/X/V9;

    move/from16 v2, p2

    invoke-direct {v3, v0, p1, v2}, Lcom/facebook/ads/redexgen/X/V9;-><init>(Lcom/facebook/ads/redexgen/X/V2;Lcom/facebook/ads/redexgen/X/bK;Z)V

    .line 56842
    invoke-virtual {p1}, Lcom/facebook/ads/redexgen/X/bK;->A0G()Ljava/lang/String;

    move-result-object v2

    new-instance v0, Lcom/facebook/ads/redexgen/X/6V;

    invoke-direct {v0, v2, v1}, Lcom/facebook/ads/redexgen/X/6V;-><init>(Ljava/lang/String;Ljava/lang/String;)V

    .line 56843
    invoke-virtual {v4, v3, v0}, Lcom/facebook/ads/redexgen/X/6c;->A0W(Lcom/facebook/ads/redexgen/X/6U;Lcom/facebook/ads/redexgen/X/6V;)V

    .line 56844
    return-void
.end method

.method private A0i(Lcom/facebook/ads/redexgen/X/15;)V
    .locals 1

    .line 56845
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0a:Lcom/facebook/ads/redexgen/X/bK;

    if-nez v0, :cond_0

    .line 56846
    return-void

    .line 56847
    :cond_0
    invoke-virtual {v0, p1}, Lcom/facebook/ads/redexgen/X/bK;->A0K(Lcom/facebook/ads/redexgen/X/15;)V

    .line 56848
    return-void
.end method

.method public static A0j(Lcom/facebook/ads/internal/api/NativeAdImageApi;Landroid/widget/ImageView;Lcom/facebook/ads/redexgen/X/Yn;)V
    .locals 3

    .line 56849
    if-eqz p0, :cond_0

    if-eqz p1, :cond_0

    .line 56850
    new-instance v2, Lcom/facebook/ads/redexgen/X/Tq;

    invoke-direct {v2, p1, p2}, Lcom/facebook/ads/redexgen/X/Tq;-><init>(Landroid/widget/ImageView;Lcom/facebook/ads/redexgen/X/Yn;)V

    .line 56851
    invoke-interface {p0}, Lcom/facebook/ads/internal/api/NativeAdImageApi;->getHeight()I

    move-result v1

    invoke-interface {p0}, Lcom/facebook/ads/internal/api/NativeAdImageApi;->getWidth()I

    move-result v0

    invoke-virtual {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Tq;->A05(II)Lcom/facebook/ads/redexgen/X/Tq;

    move-result-object v1

    .line 56852
    invoke-interface {p0}, Lcom/facebook/ads/internal/api/NativeAdImageApi;->getUrl()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/Tq;->A07(Ljava/lang/String;)V

    .line 56853
    :cond_0
    return-void
.end method

.method public static synthetic A0k(Lcom/facebook/ads/redexgen/X/V2;)V
    .locals 0

    .line 56854
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/V2;->A0b()V

    return-void
.end method

.method public static synthetic A0l(Lcom/facebook/ads/redexgen/X/V2;)V
    .locals 0

    .line 56855
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/V2;->A0c()V

    return-void
.end method

.method private final A0m(Lcom/facebook/ads/redexgen/X/V1;)V
    .locals 0

    .line 56856
    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/V2;->A0G:Lcom/facebook/ads/redexgen/X/V1;

    .line 56857
    return-void
.end method

.method private final A0n(Ljava/lang/String;)V
    .locals 0

    .line 56858
    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/V2;->A0S:Ljava/lang/String;

    .line 56859
    return-void
.end method

.method private A0o(Ljava/util/List;Landroid/view/View;)V
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Landroid/view/View;",
            ">;",
            "Landroid/view/View;",
            ")V"
        }
    .end annotation

    .line 56860
    .local v4, "subviews":Ljava/util/List;, "Ljava/util/List<Landroid/view/View;>;"
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0d:Lcom/facebook/ads/redexgen/X/JO;

    if-eqz v0, :cond_0

    invoke-interface {v0, p2}, Lcom/facebook/ads/redexgen/X/JO;->AGK(Landroid/view/View;)Z

    move-result v0

    if-eqz v0, :cond_0

    .line 56861
    return-void

    .line 56862
    :cond_0
    instance-of v0, p2, Landroid/view/ViewGroup;

    if-eqz v0, :cond_2

    .line 56863
    check-cast p2, Landroid/view/ViewGroup;

    .line 56864
    .local v0, "vg":Landroid/view/ViewGroup;
    const/4 v4, 0x0

    .local v1, "i":I
    :goto_0
    invoke-virtual {p2}, Landroid/view/ViewGroup;->getChildCount()I

    move-result v0

    if-ge v4, v0, :cond_3

    .line 56865
    invoke-virtual {p2, v4}, Landroid/view/ViewGroup;->getChildAt(I)Landroid/view/View;

    move-result-object v3

    sget-object v2, Lcom/facebook/ads/redexgen/X/V2;->A0m:[Ljava/lang/String;

    const/4 v0, 0x7

    aget-object v1, v2, v0

    const/4 v0, 0x5

    aget-object v0, v2, v0

    invoke-virtual {v1}, Ljava/lang/String;->length()I

    move-result v1

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v0

    if-eq v1, v0, :cond_1

    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    :cond_1
    sget-object v2, Lcom/facebook/ads/redexgen/X/V2;->A0m:[Ljava/lang/String;

    const-string v1, "SQxDsveS3BVeMl0nUnJayKxDlwKiSRwM"

    const/4 v0, 0x1

    aput-object v1, v2, v0

    const-string v1, "CPUPH8uU8pe6dd8URnD7AGDEQ4lKFlLs"

    const/4 v0, 0x3

    aput-object v1, v2, v0

    invoke-direct {p0, p1, v3}, Lcom/facebook/ads/redexgen/X/V2;->A0o(Ljava/util/List;Landroid/view/View;)V

    .line 56866
    add-int/lit8 v4, v4, 0x1

    goto :goto_0

    .line 56867
    :cond_2
    invoke-interface {p1, p2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 56868
    :cond_3
    return-void
.end method

.method private A0p()Z
    .locals 2

    .line 56869
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/V2;->A18()Lcom/facebook/ads/redexgen/X/JU;

    move-result-object v1

    sget-object v0, Lcom/facebook/ads/redexgen/X/JU;->A05:Lcom/facebook/ads/redexgen/X/JU;

    if-eq v1, v0, :cond_0

    .line 56870
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/V2;->A18()Lcom/facebook/ads/redexgen/X/JU;

    move-result-object v1

    sget-object v0, Lcom/facebook/ads/redexgen/X/JU;->A03:Lcom/facebook/ads/redexgen/X/JU;

    if-ne v1, v0, :cond_1

    :cond_0
    const/4 v0, 0x1

    .line 56871
    :goto_0
    return v0

    .line 56872
    :cond_1
    const/4 v0, 0x0

    goto :goto_0
.end method

.method private A0q()Z
    .locals 1

    .line 56873
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/V2;->A0F()Lcom/facebook/ads/redexgen/X/14;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/14;->A0h()Z

    move-result v0

    return v0
.end method

.method public static synthetic A0r(Lcom/facebook/ads/redexgen/X/V2;)Z
    .locals 0

    .line 56874
    iget-boolean p0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0W:Z

    return p0
.end method

.method public static synthetic A0s(Lcom/facebook/ads/redexgen/X/V2;)Z
    .locals 0

    .line 56875
    iget-boolean p0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0Z:Z

    return p0
.end method

.method public static synthetic A0t(Lcom/facebook/ads/redexgen/X/V2;)Z
    .locals 0

    .line 56876
    iget-boolean p0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0Y:Z

    return p0
.end method

.method public static synthetic A0u(Lcom/facebook/ads/redexgen/X/V2;)Z
    .locals 0

    .line 56877
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/V2;->A0p()Z

    move-result p0

    return p0
.end method

.method public static synthetic A0v(Lcom/facebook/ads/redexgen/X/V2;)Z
    .locals 0

    .line 56878
    iget-boolean p0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0X:Z

    return p0
.end method

.method public static synthetic A0w(Lcom/facebook/ads/redexgen/X/V2;)Z
    .locals 0

    .line 56879
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/V2;->A0q()Z

    move-result p0

    return p0
.end method


# virtual methods
.method public final A0x()J
    .locals 2

    .line 56880
    iget-wide v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A00:J

    return-wide v0
.end method

.method public final A0y()Lcom/facebook/ads/redexgen/X/bK;
    .locals 1

    .line 56881
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0a:Lcom/facebook/ads/redexgen/X/bK;

    return-object v0
.end method

.method public final A0z()Lcom/facebook/ads/redexgen/X/b5;
    .locals 1

    .line 56882
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/V2;->A0F()Lcom/facebook/ads/redexgen/X/14;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/14;->A0D()Lcom/facebook/ads/redexgen/X/b5;

    move-result-object v0

    return-object v0
.end method

.method public final A10()Lcom/facebook/ads/redexgen/X/6c;
    .locals 1

    .line 56883
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0b:Lcom/facebook/ads/redexgen/X/6c;

    return-object v0
.end method

.method public final A11()Lcom/facebook/ads/redexgen/X/Yn;
    .locals 1

    .line 56884
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0c:Lcom/facebook/ads/redexgen/X/Yn;

    return-object v0
.end method

.method public final A12()Lcom/facebook/ads/redexgen/X/V4;
    .locals 1

    .line 56885
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0F:Lcom/facebook/ads/redexgen/X/V4;

    return-object v0
.end method

.method public final A13()Lcom/facebook/ads/redexgen/X/JP;
    .locals 1

    .line 56886
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/V2;->A0F()Lcom/facebook/ads/redexgen/X/14;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/14;->A0F()Lcom/facebook/ads/redexgen/X/JP;

    move-result-object v0

    return-object v0
.end method

.method public final A14()Lcom/facebook/ads/redexgen/X/JP;
    .locals 1

    .line 56887
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/V2;->A0F()Lcom/facebook/ads/redexgen/X/14;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/14;->A0G()Lcom/facebook/ads/redexgen/X/JP;

    move-result-object v0

    return-object v0
.end method

.method public final A15()Lcom/facebook/ads/redexgen/X/V1;
    .locals 1

    .line 56888
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0G:Lcom/facebook/ads/redexgen/X/V1;

    return-object v0
.end method

.method public final A16()Lcom/facebook/ads/redexgen/X/JR;
    .locals 1

    .line 56889
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0H:Lcom/facebook/ads/redexgen/X/JR;

    return-object v0
.end method

.method public final A17()Lcom/facebook/ads/redexgen/X/JS;
    .locals 1

    .line 56890
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0I:Lcom/facebook/ads/redexgen/X/JS;

    return-object v0
.end method

.method public final A18()Lcom/facebook/ads/redexgen/X/JU;
    .locals 1

    .line 56891
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/V2;->A0F()Lcom/facebook/ads/redexgen/X/14;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/14;->A0I()Lcom/facebook/ads/redexgen/X/JU;

    move-result-object v0

    return-object v0
.end method

.method public final A19()Lcom/facebook/ads/redexgen/X/Lg;
    .locals 1

    .line 56892
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0f:Lcom/facebook/ads/redexgen/X/Lg;

    return-object v0
.end method

.method public final A1A()Lcom/facebook/ads/redexgen/X/RE;
    .locals 1

    .line 56893
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0R:Lcom/facebook/ads/redexgen/X/RE;

    return-object v0
.end method

.method public final A1B()Ljava/lang/String;
    .locals 1

    .line 56894
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0a:Lcom/facebook/ads/redexgen/X/bK;

    if-eqz v0, :cond_0

    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/V2;->isAdLoaded()Z

    move-result v0

    if-nez v0, :cond_1

    .line 56895
    :cond_0
    const/4 v0, 0x0

    return-object v0

    .line 56896
    :cond_1
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0a:Lcom/facebook/ads/redexgen/X/bK;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/bK;->A6T()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public final A1C()Ljava/lang/String;
    .locals 1

    .line 56897
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0T:Ljava/lang/String;

    return-object v0
.end method

.method public final A1D()Ljava/lang/String;
    .locals 1

    .line 56898
    const/4 v0, 0x1

    invoke-direct {p0, v0}, Lcom/facebook/ads/redexgen/X/V2;->A0G(Z)Lcom/facebook/ads/redexgen/X/14;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/14;->A0O()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public final A1E()Ljava/lang/String;
    .locals 1

    .line 56899
    const/4 v0, 0x1

    invoke-direct {p0, v0}, Lcom/facebook/ads/redexgen/X/V2;->A0G(Z)Lcom/facebook/ads/redexgen/X/14;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/14;->A0P()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public final A1F()Ljava/lang/String;
    .locals 1

    .line 56900
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/V2;->A0F()Lcom/facebook/ads/redexgen/X/14;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/14;->A0c()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public final A1G()Ljava/lang/String;
    .locals 2

    .line 56901
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0a:Lcom/facebook/ads/redexgen/X/bK;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/bK;->A0E()Lcom/facebook/ads/redexgen/X/14;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/14;->A0d()Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    if-eqz v0, :cond_1

    .line 56902
    :cond_0
    const/4 v0, 0x0

    return-object v0

    .line 56903
    :cond_1
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/V2;->A0b:Lcom/facebook/ads/redexgen/X/6c;

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0a:Lcom/facebook/ads/redexgen/X/bK;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/bK;->A0E()Lcom/facebook/ads/redexgen/X/14;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/14;->A0d()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/6c;->A0S(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public final A1H()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lcom/facebook/ads/redexgen/X/V2;",
            ">;"
        }
    .end annotation

    .line 56904
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0a:Lcom/facebook/ads/redexgen/X/bK;

    if-eqz v0, :cond_0

    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/V2;->isAdLoaded()Z

    move-result v0

    if-nez v0, :cond_1

    .line 56905
    :cond_0
    const/4 v0, 0x0

    return-object v0

    .line 56906
    :cond_1
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0a:Lcom/facebook/ads/redexgen/X/bK;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/bK;->A0H()Ljava/util/List;

    move-result-object v0

    return-object v0
.end method

.method public final A1I()V
    .locals 4

    .line 56907
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0c:Lcom/facebook/ads/redexgen/X/Yn;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/7f;->A01()Lcom/facebook/ads/redexgen/X/Ym;

    move-result-object v0

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/2T;->A00(Lcom/facebook/ads/redexgen/X/Ym;)Lcom/facebook/ads/redexgen/X/2S;

    move-result-object v2

    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/V2;->A0c:Lcom/facebook/ads/redexgen/X/Yn;

    .line 56908
    const/4 v0, 0x0

    invoke-virtual {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/2S;->A0O(Landroid/content/Context;Z)Z

    move-result v0

    if-nez v0, :cond_0

    .line 56909
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/V2;->A0a()V

    .line 56910
    return-void

    .line 56911
    :cond_0
    iget-object v3, p0, Lcom/facebook/ads/redexgen/X/V2;->A0c:Lcom/facebook/ads/redexgen/X/Yn;

    .line 56912
    invoke-virtual {v3}, Lcom/facebook/ads/redexgen/X/7f;->A09()Lcom/facebook/ads/redexgen/X/J2;

    move-result-object v2

    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/V2;->A1B()Ljava/lang/String;

    move-result-object v1

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A07:Lcom/facebook/ads/NativeAdLayout;

    .line 56913
    invoke-static {v3, v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Mp;->A01(Lcom/facebook/ads/redexgen/X/Yn;Lcom/facebook/ads/redexgen/X/J2;Ljava/lang/String;Lcom/facebook/ads/NativeAdLayout;)Lcom/facebook/ads/redexgen/X/Mo;

    move-result-object v1

    .line 56914
    .local v0, "adReportingLayout":Lcom/facebook/ads/redexgen/X/Mo;
    if-nez v1, :cond_1

    .line 56915
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/V2;->A0a()V

    .line 56916
    return-void

    .line 56917
    :cond_1
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A07:Lcom/facebook/ads/NativeAdLayout;

    invoke-virtual {v0}, Lcom/facebook/ads/NativeAdLayout;->getNativeAdLayoutApi()Lcom/facebook/ads/internal/api/NativeAdLayoutApi;

    move-result-object v0

    check-cast v0, Lcom/facebook/ads/redexgen/X/ZG;

    .line 56918
    invoke-virtual {v0, v1}, Lcom/facebook/ads/redexgen/X/ZG;->A03(Lcom/facebook/ads/redexgen/X/Mo;)V

    .line 56919
    invoke-virtual {v1}, Lcom/facebook/ads/redexgen/X/Mo;->A0K()V

    .line 56920
    return-void
.end method

.method public final A1J(Landroid/graphics/drawable/Drawable;)V
    .locals 3

    .line 56921
    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/V2;->A01:Landroid/graphics/drawable/Drawable;

    .line 56922
    const/4 v1, 0x1

    if-eqz p1, :cond_0

    const/4 v0, 0x1

    :goto_0
    invoke-virtual {p0, v0, v1}, Lcom/facebook/ads/redexgen/X/V2;->A1c(ZZ)V

    sget-object v2, Lcom/facebook/ads/redexgen/X/V2;->A0m:[Ljava/lang/String;

    const/4 v0, 0x4

    aget-object v1, v2, v0

    const/4 v0, 0x6

    aget-object v0, v2, v0

    invoke-virtual {v1}, Ljava/lang/String;->length()I

    move-result v1

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v0

    if-eq v1, v0, :cond_1

    .line 56923
    sget-object v2, Lcom/facebook/ads/redexgen/X/V2;->A0m:[Ljava/lang/String;

    const-string v1, "Q96JEqttX0oIb74l"

    const/4 v0, 0x4

    aput-object v1, v2, v0

    const-string v1, "mqYIcYUMJzEb"

    const/4 v0, 0x6

    aput-object v1, v2, v0

    return-void

    .line 56924
    :cond_0
    const/4 v0, 0x0

    goto :goto_0

    :cond_1
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0
.end method

.method public final A1K(Landroid/view/View;)V
    .locals 4

    .line 56925
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0i:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 56926
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0F:Lcom/facebook/ads/redexgen/X/V4;

    invoke-virtual {p1, v0}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 56927
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0F:Lcom/facebook/ads/redexgen/X/V4;

    invoke-virtual {p1, v0}, Landroid/view/View;->setOnTouchListener(Landroid/view/View$OnTouchListener;)V

    .line 56928
    sget v1, Landroid/os/Build$VERSION;->SDK_INT:I

    const/16 v0, 0x12

    if-lt v1, v0, :cond_0

    .line 56929
    invoke-virtual {p1}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/Ih;->A11(Landroid/content/Context;)Z

    move-result v3

    sget-object v2, Lcom/facebook/ads/redexgen/X/V2;->A0m:[Ljava/lang/String;

    const/4 v0, 0x1

    aget-object v1, v2, v0

    const/4 v0, 0x3

    aget-object v2, v2, v0

    const/16 v0, 0x10

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    invoke-virtual {v2, v0}, Ljava/lang/String;->charAt(I)C

    move-result v0

    if-eq v1, v0, :cond_1

    sget-object v2, Lcom/facebook/ads/redexgen/X/V2;->A0m:[Ljava/lang/String;

    const-string v1, "GeTsVetQiODT9yUx"

    const/4 v0, 0x4

    aput-object v1, v2, v0

    const-string v1, "UPeG8QvAHkrx"

    const/4 v0, 0x6

    aput-object v1, v2, v0

    if-eqz v3, :cond_0

    .line 56930
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0F:Lcom/facebook/ads/redexgen/X/V4;

    invoke-virtual {p1, v0}, Landroid/view/View;->setOnLongClickListener(Landroid/view/View$OnLongClickListener;)V

    .line 56931
    :cond_0
    return-void

    :cond_1
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0
.end method

.method public final A1L(Landroid/view/View;Landroid/widget/ImageView;)V
    .locals 2

    .line 56932
    new-instance v1, Ljava/util/ArrayList;

    invoke-direct {v1}, Ljava/util/ArrayList;-><init>()V

    .line 56933
    .local v0, "clickableViews":Ljava/util/List;, "Ljava/util/List<Landroid/view/View;>;"
    invoke-direct {p0, v1, p1}, Lcom/facebook/ads/redexgen/X/V2;->A0o(Ljava/util/List;Landroid/view/View;)V

    .line 56934
    const/4 v0, 0x1

    invoke-direct {p0, p1, p2, v1, v0}, Lcom/facebook/ads/redexgen/X/V2;->A0f(Landroid/view/View;Landroid/view/View;Ljava/util/List;Z)V

    .line 56935
    return-void
.end method

.method public final A1M(Landroid/view/View;Landroid/widget/ImageView;Ljava/util/List;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/view/View;",
            "Landroid/widget/ImageView;",
            "Ljava/util/List<",
            "Landroid/view/View;",
            ">;)V"
        }
    .end annotation

    .line 56936
    .local p3, "clickableViews":Ljava/util/List;, "Ljava/util/List<Landroid/view/View;>;"
    const/4 v0, 0x1

    invoke-direct {p0, p1, p2, p3, v0}, Lcom/facebook/ads/redexgen/X/V2;->A0f(Landroid/view/View;Landroid/view/View;Ljava/util/List;Z)V

    .line 56937
    return-void
.end method

.method public final A1N(Landroid/view/View;Lcom/facebook/ads/internal/api/AdNativeComponentView;)V
    .locals 2

    .line 56938
    new-instance v1, Ljava/util/ArrayList;

    invoke-direct {v1}, Ljava/util/ArrayList;-><init>()V

    .line 56939
    .local v0, "clickableViews":Ljava/util/List;, "Ljava/util/List<Landroid/view/View;>;"
    invoke-direct {p0, v1, p1}, Lcom/facebook/ads/redexgen/X/V2;->A0o(Ljava/util/List;Landroid/view/View;)V

    .line 56940
    const/4 v0, 0x0

    invoke-direct {p0, p1, p2, v1, v0}, Lcom/facebook/ads/redexgen/X/V2;->A0f(Landroid/view/View;Landroid/view/View;Ljava/util/List;Z)V

    .line 56941
    return-void
.end method

.method public final A1O(Landroid/view/View;Lcom/facebook/ads/internal/api/AdNativeComponentView;Ljava/util/List;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/view/View;",
            "Lcom/facebook/ads/internal/api/AdNativeComponentView;",
            "Ljava/util/List<",
            "Landroid/view/View;",
            ">;)V"
        }
    .end annotation

    .line 56942
    .local p3, "clickableViews":Ljava/util/List;, "Ljava/util/List<Landroid/view/View;>;"
    const/4 v0, 0x0

    invoke-direct {p0, p1, p2, p3, v0}, Lcom/facebook/ads/redexgen/X/V2;->A0f(Landroid/view/View;Landroid/view/View;Ljava/util/List;Z)V

    .line 56943
    return-void
.end method

.method public final A1P(Lcom/facebook/ads/MediaView;)V
    .locals 1

    .line 56944
    if-eqz p1, :cond_0

    .line 56945
    const/4 v0, 0x1

    iput-boolean v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0Y:Z

    .line 56946
    :cond_0
    return-void
.end method

.method public final A1Q(Lcom/facebook/ads/MediaView;)V
    .locals 1

    .line 56947
    if-eqz p1, :cond_0

    .line 56948
    const/4 v0, 0x1

    iput-boolean v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0Z:Z

    .line 56949
    :cond_0
    return-void
.end method

.method public final A1R(Lcom/facebook/ads/NativeAdBase;Lcom/facebook/ads/NativeAdListener;)V
    .locals 1

    .line 56950
    if-nez p2, :cond_0

    .line 56951
    return-void

    .line 56952
    :cond_0
    new-instance v0, Lcom/facebook/ads/redexgen/X/Bj;

    invoke-direct {v0, p2, p1}, Lcom/facebook/ads/redexgen/X/Bj;-><init>(Lcom/facebook/ads/NativeAdListener;Lcom/facebook/ads/NativeAdBase;)V

    invoke-direct {p0, v0}, Lcom/facebook/ads/redexgen/X/V2;->A0m(Lcom/facebook/ads/redexgen/X/V1;)V

    .line 56953
    return-void
.end method

.method public final A1S(Lcom/facebook/ads/NativeAdLayout;)V
    .locals 0

    .line 56954
    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/V2;->A07:Lcom/facebook/ads/NativeAdLayout;

    .line 56955
    return-void
.end method

.method public final A1T(Lcom/facebook/ads/redexgen/X/bK;)V
    .locals 4

    .line 56956
    const/4 v0, 0x1

    invoke-direct {p0, p1, v0}, Lcom/facebook/ads/redexgen/X/V2;->A0h(Lcom/facebook/ads/redexgen/X/bK;Z)V

    .line 56957
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0G:Lcom/facebook/ads/redexgen/X/V1;

    if-eqz v0, :cond_1

    invoke-virtual {p1}, Lcom/facebook/ads/redexgen/X/bK;->A0H()Ljava/util/List;

    move-result-object v3

    sget-object v2, Lcom/facebook/ads/redexgen/X/V2;->A0m:[Ljava/lang/String;

    const/4 v0, 0x7

    aget-object v1, v2, v0

    const/4 v0, 0x5

    aget-object v0, v2, v0

    invoke-virtual {v1}, Ljava/lang/String;->length()I

    move-result v1

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v0

    if-eq v1, v0, :cond_0

    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    :cond_0
    sget-object v2, Lcom/facebook/ads/redexgen/X/V2;->A0m:[Ljava/lang/String;

    const-string v1, "OfB2JsewzcYgCxMvn97l0AOaUV28WOmd"

    const/4 v0, 0x0

    aput-object v1, v2, v0

    const-string v1, "fN7XzjPm8qHCAEGwTiZWrbsVWAwnjwvJ"

    const/4 v0, 0x2

    aput-object v1, v2, v0

    if-eqz v3, :cond_1

    .line 56958
    new-instance v2, Lcom/facebook/ads/redexgen/X/VB;

    invoke-direct {v2, p0}, Lcom/facebook/ads/redexgen/X/VB;-><init>(Lcom/facebook/ads/redexgen/X/V2;)V

    .line 56959
    .local v0, "carouselChildNativeAdapterListener":Lcom/facebook/ads/redexgen/X/15;
    invoke-virtual {p1}, Lcom/facebook/ads/redexgen/X/bK;->A0H()Ljava/util/List;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :goto_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_1

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/facebook/ads/redexgen/X/V2;

    .line 56960
    .local v2, "childAd":Lcom/facebook/ads/redexgen/X/V2;
    invoke-direct {v0, v2}, Lcom/facebook/ads/redexgen/X/V2;->A0i(Lcom/facebook/ads/redexgen/X/15;)V

    .line 56961
    .end local v2    # "childAd":Lcom/facebook/ads/redexgen/X/V2;
    goto :goto_0

    .line 56962
    .end local v0    # "carouselChildNativeAdapterListener":Lcom/facebook/ads/redexgen/X/15;
    :cond_1
    return-void
.end method

.method public final A1U(Lcom/facebook/ads/redexgen/X/ZX;)V
    .locals 1

    .line 56963
    new-instance v0, Ljava/lang/ref/WeakReference;

    invoke-direct {v0, p1}, Ljava/lang/ref/WeakReference;-><init>(Ljava/lang/Object;)V

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0U:Ljava/lang/ref/WeakReference;

    .line 56964
    return-void
.end method

.method public final A1V(Lcom/facebook/ads/redexgen/X/JL;Ljava/lang/String;Lcom/facebook/ads/redexgen/X/6i;)V
    .locals 9

    .line 56965
    if-nez p2, :cond_3

    .line 56966
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0c:Lcom/facebook/ads/redexgen/X/Yn;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/Yn;->A0E()Lcom/facebook/ads/redexgen/X/0S;

    move-result-object v0

    invoke-interface {v0}, Lcom/facebook/ads/redexgen/X/0S;->A2p()V

    .line 56967
    :goto_0
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v0

    iput-wide v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A00:J

    .line 56968
    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0j:Z

    if-eqz v0, :cond_0

    .line 56969
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0c:Lcom/facebook/ads/redexgen/X/Yn;

    .line 56970
    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/1x;->A00(Lcom/facebook/ads/redexgen/X/Yn;)Lcom/facebook/ads/AdSettings$IntegrationErrorMode;

    move-result-object v3

    .line 56971
    .local v0, "integrationErrorMode":Lcom/facebook/ads/AdSettings$IntegrationErrorMode;
    const/16 v2, 0x236

    const/16 v1, 0x1e

    const/16 v0, 0x73

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/V2;->A0W(III)Ljava/lang/String;

    move-result-object v4

    .line 56972
    .local v1, "errorMessage":Ljava/lang/String;
    sget-object v0, Lcom/facebook/ads/AdSettings$IntegrationErrorMode;->INTEGRATION_ERROR_CRASH_DEBUG_MODE:Lcom/facebook/ads/AdSettings$IntegrationErrorMode;

    invoke-virtual {v0, v3}, Lcom/facebook/ads/AdSettings$IntegrationErrorMode;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_4

    .line 56973
    sget-object v3, Lcom/facebook/ads/internal/protocol/AdErrorType;->LOAD_AD_CALLED_MORE_THAN_ONCE:Lcom/facebook/ads/internal/protocol/AdErrorType;

    const/16 v2, 0x236

    const/16 v1, 0x1e

    const/16 v0, 0x73

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/V2;->A0W(III)Ljava/lang/String;

    move-result-object v0

    new-instance v6, Lcom/facebook/ads/redexgen/X/Jb;

    invoke-direct {v6, v3, v0}, Lcom/facebook/ads/redexgen/X/Jb;-><init>(Lcom/facebook/ads/internal/protocol/AdErrorType;Ljava/lang/String;)V

    .line 56974
    .local v2, "error":Lcom/facebook/ads/redexgen/X/Jb;
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/V2;->A11()Lcom/facebook/ads/redexgen/X/Yn;

    move-result-object v0

    .line 56975
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/Yn;->A0E()Lcom/facebook/ads/redexgen/X/0S;

    move-result-object v5

    iget-wide v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A00:J

    .line 56976
    invoke-static {v0, v1}, Lcom/facebook/ads/redexgen/X/Lf;->A01(J)J

    move-result-wide v0

    .line 56977
    invoke-virtual {v6}, Lcom/facebook/ads/redexgen/X/Jb;->A03()Lcom/facebook/ads/internal/protocol/AdErrorType;

    move-result-object v2

    invoke-virtual {v2}, Lcom/facebook/ads/internal/protocol/AdErrorType;->getErrorCode()I

    move-result v3

    .line 56978
    invoke-virtual {v6}, Lcom/facebook/ads/redexgen/X/Jb;->A04()Ljava/lang/String;

    move-result-object v2

    .line 56979
    invoke-interface {v5, v0, v1, v3, v2}, Lcom/facebook/ads/redexgen/X/0S;->A2m(JILjava/lang/String;)V

    .line 56980
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0G:Lcom/facebook/ads/redexgen/X/V1;

    if-eqz v0, :cond_2

    .line 56981
    invoke-interface {v0, v6}, Lcom/facebook/ads/redexgen/X/JH;->ABR(Lcom/facebook/ads/redexgen/X/Jb;)V

    .line 56982
    :goto_1
    new-instance v5, Lcom/facebook/ads/redexgen/X/8B;

    invoke-direct {v5, v4}, Lcom/facebook/ads/redexgen/X/8B;-><init>(Ljava/lang/String;)V

    .line 56983
    .local v3, "deException":Lcom/facebook/ads/redexgen/X/8B;
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/V2;->A11()Lcom/facebook/ads/redexgen/X/Yn;

    move-result-object v0

    .line 56984
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/7f;->A07()Lcom/facebook/ads/redexgen/X/89;

    move-result-object v4

    sget v3, Lcom/facebook/ads/redexgen/X/8A;->A0c:I

    .line 56985
    const/16 v2, 0x226

    const/4 v1, 0x3

    const/4 v0, 0x3

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/V2;->A0W(III)Ljava/lang/String;

    move-result-object v0

    invoke-interface {v4, v0, v3, v5}, Lcom/facebook/ads/redexgen/X/89;->A9a(Ljava/lang/String;ILcom/facebook/ads/redexgen/X/8B;)V

    .line 56986
    .end local v0    # "integrationErrorMode":Lcom/facebook/ads/AdSettings$IntegrationErrorMode;
    .end local v1    # "errorMessage":Ljava/lang/String;
    :cond_0
    const/4 v0, 0x1

    iput-boolean v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0j:Z

    .line 56987
    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/V2;->A0E:Lcom/facebook/ads/redexgen/X/JL;

    .line 56988
    sget-object v0, Lcom/facebook/ads/redexgen/X/JL;->A05:Lcom/facebook/ads/redexgen/X/JL;

    invoke-virtual {p1, v0}, Lcom/facebook/ads/redexgen/X/JL;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_1

    .line 56989
    sget-object v0, Lcom/facebook/ads/redexgen/X/12;->A05:Lcom/facebook/ads/redexgen/X/12;

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A08:Lcom/facebook/ads/redexgen/X/12;

    .line 56990
    :cond_1
    iput-object p3, p0, Lcom/facebook/ads/redexgen/X/V2;->A0C:Lcom/facebook/ads/redexgen/X/6i;

    .line 56991
    new-instance v2, Lcom/facebook/ads/redexgen/X/1u;

    iget-object v3, p0, Lcom/facebook/ads/redexgen/X/V2;->A0g:Ljava/lang/String;

    iget-object v4, p0, Lcom/facebook/ads/redexgen/X/V2;->A0J:Lcom/facebook/ads/redexgen/X/Jg;

    .line 56992
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/V2;->A0R()Lcom/facebook/ads/internal/protocol/AdPlacementType;

    move-result-object v5

    const/4 v6, 0x0

    const/4 v7, 0x1

    new-instance v8, Lcom/facebook/ads/redexgen/X/aZ;

    invoke-direct {v8}, Lcom/facebook/ads/redexgen/X/aZ;-><init>()V

    invoke-direct/range {v2 .. v8}, Lcom/facebook/ads/redexgen/X/1u;-><init>(Ljava/lang/String;Lcom/facebook/ads/redexgen/X/Jg;Lcom/facebook/ads/internal/protocol/AdPlacementType;Lcom/facebook/ads/redexgen/X/Je;ILcom/facebook/ads/redexgen/X/8Z;)V

    .line 56993
    .local v0, "adControllerConfig":Lcom/facebook/ads/redexgen/X/1u;
    invoke-virtual {v2, p1}, Lcom/facebook/ads/redexgen/X/1u;->A05(Lcom/facebook/ads/redexgen/X/JL;)V

    .line 56994
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0S:Ljava/lang/String;

    invoke-virtual {v2, v0}, Lcom/facebook/ads/redexgen/X/1u;->A06(Ljava/lang/String;)V

    .line 56995
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0T:Ljava/lang/String;

    invoke-virtual {v2, v0}, Lcom/facebook/ads/redexgen/X/1u;->A07(Ljava/lang/String;)V

    .line 56996
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0c:Lcom/facebook/ads/redexgen/X/Yn;

    new-instance v1, Lcom/facebook/ads/redexgen/X/FO;

    invoke-direct {v1, v0, v2}, Lcom/facebook/ads/redexgen/X/FO;-><init>(Lcom/facebook/ads/redexgen/X/Yn;Lcom/facebook/ads/redexgen/X/1u;)V

    iput-object v1, p0, Lcom/facebook/ads/redexgen/X/V2;->A0A:Lcom/facebook/ads/redexgen/X/FO;

    .line 56997
    new-instance v0, Lcom/facebook/ads/redexgen/X/VC;

    invoke-direct {v0, p0}, Lcom/facebook/ads/redexgen/X/VC;-><init>(Lcom/facebook/ads/redexgen/X/V2;)V

    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/am;->A0P(Lcom/facebook/ads/redexgen/X/0q;)V

    .line 56998
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0A:Lcom/facebook/ads/redexgen/X/FO;

    invoke-virtual {v0, p2}, Lcom/facebook/ads/redexgen/X/am;->A0T(Ljava/lang/String;)V

    .line 56999
    return-void

    .line 57000
    :cond_2
    const/16 v2, 0x4c

    const/16 v1, 0x11

    const/16 v0, 0x29

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/V2;->A0W(III)Ljava/lang/String;

    move-result-object v0

    invoke-static {v0, v4}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    goto :goto_1

    .line 57001
    :cond_3
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0c:Lcom/facebook/ads/redexgen/X/Yn;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/Yn;->A0E()Lcom/facebook/ads/redexgen/X/0S;

    move-result-object v0

    invoke-interface {v0}, Lcom/facebook/ads/redexgen/X/0S;->A2o()V

    goto/16 :goto_0

    .line 57002
    .end local v2    # "error":Lcom/facebook/ads/redexgen/X/Jb;
    .end local v3    # "deException":Lcom/facebook/ads/redexgen/X/8B;
    :cond_4
    new-instance v0, Lcom/facebook/ads/redexgen/X/22;

    invoke-direct {v0, v4}, Lcom/facebook/ads/redexgen/X/22;-><init>(Ljava/lang/String;)V

    throw v0
.end method

.method public final A1W(Lcom/facebook/ads/redexgen/X/JR;)V
    .locals 0

    .line 57003
    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/V2;->A0H:Lcom/facebook/ads/redexgen/X/JR;

    .line 57004
    return-void
.end method

.method public final A1X(Lcom/facebook/ads/redexgen/X/JS;)V
    .locals 0

    .line 57005
    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/V2;->A0I:Lcom/facebook/ads/redexgen/X/JS;

    .line 57006
    return-void
.end method

.method public final A1Y(Lcom/facebook/ads/redexgen/X/Jg;)V
    .locals 4

    .line 57007
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/V2;->A0q()Z

    move-result v0

    if-nez v0, :cond_2

    .line 57008
    sget-object v0, Lcom/facebook/ads/redexgen/X/Jg;->A04:Lcom/facebook/ads/redexgen/X/Jg;

    invoke-virtual {v0, p1}, Lcom/facebook/ads/redexgen/X/Jg;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_0

    .line 57009
    iget-object v3, p0, Lcom/facebook/ads/redexgen/X/V2;->A0c:Lcom/facebook/ads/redexgen/X/Yn;

    sget-object v2, Lcom/facebook/ads/redexgen/X/V2;->A0m:[Ljava/lang/String;

    const/4 v0, 0x7

    aget-object v1, v2, v0

    const/4 v0, 0x5

    aget-object v0, v2, v0

    invoke-virtual {v1}, Ljava/lang/String;->length()I

    move-result v1

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v0

    if-eq v1, v0, :cond_1

    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    .line 57010
    :cond_0
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0c:Lcom/facebook/ads/redexgen/X/Yn;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/Yn;->A0E()Lcom/facebook/ads/redexgen/X/0S;

    move-result-object v2

    sget-object v0, Lcom/facebook/ads/internal/protocol/AdPlacementType;->NATIVE:Lcom/facebook/ads/internal/protocol/AdPlacementType;

    invoke-virtual {v0}, Lcom/facebook/ads/internal/protocol/AdPlacementType;->toString()Ljava/lang/String;

    move-result-object v1

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0g:Ljava/lang/String;

    invoke-interface {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/0S;->A2s(Ljava/lang/String;Ljava/lang/String;)V

    goto :goto_0

    .line 57011
    :cond_1
    sget-object v2, Lcom/facebook/ads/redexgen/X/V2;->A0m:[Ljava/lang/String;

    const-string v1, "er71GFUENUkH6mVEmd80tsw"

    const/4 v0, 0x7

    aput-object v1, v2, v0

    const-string v1, "eRFgtOOg0hnVr16Te1PNaNs"

    const/4 v0, 0x5

    aput-object v1, v2, v0

    invoke-virtual {v3}, Lcom/facebook/ads/redexgen/X/Yn;->A0E()Lcom/facebook/ads/redexgen/X/0S;

    move-result-object v2

    sget-object v0, Lcom/facebook/ads/internal/protocol/AdPlacementType;->NATIVE_BANNER:Lcom/facebook/ads/internal/protocol/AdPlacementType;

    .line 57012
    invoke-virtual {v0}, Lcom/facebook/ads/internal/protocol/AdPlacementType;->toString()Ljava/lang/String;

    move-result-object v1

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0g:Ljava/lang/String;

    invoke-interface {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/0S;->A2s(Ljava/lang/String;Ljava/lang/String;)V

    .line 57013
    :cond_2
    :goto_0
    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/V2;->A0J:Lcom/facebook/ads/redexgen/X/Jg;

    .line 57014
    return-void
.end method

.method public final A1Z(Lcom/facebook/ads/redexgen/X/RD;)V
    .locals 1

    .line 57015
    new-instance v0, Ljava/lang/ref/WeakReference;

    invoke-direct {v0, p1}, Ljava/lang/ref/WeakReference;-><init>(Ljava/lang/Object;)V

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0V:Ljava/lang/ref/WeakReference;

    .line 57016
    return-void
.end method

.method public final A1a(Z)V
    .locals 0

    .line 57017
    iput-boolean p1, p0, Lcom/facebook/ads/redexgen/X/V2;->A0W:Z

    .line 57018
    return-void
.end method

.method public final A1b(Z)V
    .locals 0

    .line 57019
    iput-boolean p1, p0, Lcom/facebook/ads/redexgen/X/V2;->A0X:Z

    .line 57020
    return-void
.end method

.method public final A1c(ZZ)V
    .locals 6

    .line 57021
    if-eqz p1, :cond_2

    .line 57022
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/V2;->A0E:Lcom/facebook/ads/redexgen/X/JL;

    sget-object v0, Lcom/facebook/ads/redexgen/X/JL;->A05:Lcom/facebook/ads/redexgen/X/JL;

    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/JL;->equals(Ljava/lang/Object;)Z

    move-result v3

    sget-object v2, Lcom/facebook/ads/redexgen/X/V2;->A0m:[Ljava/lang/String;

    const/4 v0, 0x0

    aget-object v1, v2, v0

    const/4 v0, 0x2

    aget-object v2, v2, v0

    const/4 v0, 0x5

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    invoke-virtual {v2, v0}, Ljava/lang/String;->charAt(I)C

    move-result v0

    if-eq v1, v0, :cond_7

    sget-object v2, Lcom/facebook/ads/redexgen/X/V2;->A0m:[Ljava/lang/String;

    const-string v1, "JLY2qeoxINM9b6rKgxvdoo5"

    const/4 v0, 0x7

    aput-object v1, v2, v0

    const-string v1, "SO3TgjdXtMDnoN5cYw5MldE"

    const/4 v0, 0x5

    aput-object v1, v2, v0

    if-eqz v3, :cond_0

    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/V2;->A0q()Z

    move-result v0

    if-nez v0, :cond_0

    .line 57023
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0G:Lcom/facebook/ads/redexgen/X/V1;

    if-eqz v0, :cond_0

    .line 57024
    invoke-interface {v0}, Lcom/facebook/ads/redexgen/X/V1;->ACF()V

    .line 57025
    :cond_0
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0R:Lcom/facebook/ads/redexgen/X/RE;

    if-eqz v0, :cond_1

    .line 57026
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/RE;->A0U()V

    .line 57027
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0e:Lcom/facebook/ads/redexgen/X/JZ;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/JZ;->A09()V

    .line 57028
    .end local v0
    :cond_1
    :goto_0
    return-void

    .line 57029
    :cond_2
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0R:Lcom/facebook/ads/redexgen/X/RE;

    if-eqz v0, :cond_3

    .line 57030
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/V2;->A0y()Lcom/facebook/ads/redexgen/X/bK;

    move-result-object v3

    sget-object v2, Lcom/facebook/ads/redexgen/X/V2;->A0m:[Ljava/lang/String;

    const/4 v0, 0x7

    aget-object v1, v2, v0

    const/4 v0, 0x5

    aget-object v0, v2, v0

    invoke-virtual {v1}, Ljava/lang/String;->length()I

    move-result v1

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v0

    if-eq v1, v0, :cond_5

    .line 57031
    .local v0, "adapter":Lcom/facebook/ads/redexgen/X/bK;
    sget-object v2, Lcom/facebook/ads/redexgen/X/V2;->A0m:[Ljava/lang/String;

    const-string v1, "USSBWMStI3ylD2K7nffeYBb"

    const/4 v0, 0x7

    aput-object v1, v2, v0

    const-string v1, "raKwme25MGMDEpiUbEFBbTJ"

    const/4 v0, 0x5

    aput-object v1, v2, v0

    if-eqz v3, :cond_6

    .line 57032
    :goto_1
    invoke-virtual {v3}, Lcom/facebook/ads/redexgen/X/bK;->A0G()Ljava/lang/String;

    move-result-object v2

    .line 57033
    .local v1, "requestId":Ljava/lang/String;
    .restart local v1    # "requestId":Ljava/lang/String;
    :goto_2
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/V2;->A0e:Lcom/facebook/ads/redexgen/X/JZ;

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0c:Lcom/facebook/ads/redexgen/X/Yn;

    invoke-virtual {v1, v0, v2}, Lcom/facebook/ads/redexgen/X/JZ;->A0C(Lcom/facebook/ads/redexgen/X/7f;Ljava/lang/String;)V

    .line 57034
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0R:Lcom/facebook/ads/redexgen/X/RE;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/RE;->A0V()V

    .line 57035
    .end local v0    # "adapter":Lcom/facebook/ads/redexgen/X/bK;
    .end local v1    # "requestId":Ljava/lang/String;
    :cond_3
    iget-object v3, p0, Lcom/facebook/ads/redexgen/X/V2;->A0G:Lcom/facebook/ads/redexgen/X/V1;

    sget-object v2, Lcom/facebook/ads/redexgen/X/V2;->A0m:[Ljava/lang/String;

    const/4 v0, 0x4

    aget-object v1, v2, v0

    const/4 v0, 0x6

    aget-object v0, v2, v0

    invoke-virtual {v1}, Ljava/lang/String;->length()I

    move-result v1

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v0

    if-eq v1, v0, :cond_4

    sget-object v2, Lcom/facebook/ads/redexgen/X/V2;->A0m:[Ljava/lang/String;

    const-string v1, "FypWWsEn8bqXdPqUiIsc7hG8qhiMqauF"

    const/4 v0, 0x0

    aput-object v1, v2, v0

    const-string v1, "IXlSBRmtDoTZUwhzIwgkgymkT50eT72S"

    const/4 v0, 0x2

    aput-object v1, v2, v0

    if-eqz v3, :cond_1

    :goto_3
    if-eqz p2, :cond_1

    .line 57036
    sget-object v3, Lcom/facebook/ads/internal/protocol/AdErrorType;->BROKEN_MEDIA_ERROR:Lcom/facebook/ads/internal/protocol/AdErrorType;

    .line 57037
    const/16 v2, 0x5d

    const/16 v1, 0x15

    const/16 v0, 0x3f

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/V2;->A0W(III)Ljava/lang/String;

    move-result-object v0

    invoke-static {v3, v0}, Lcom/facebook/ads/redexgen/X/Jb;->A01(Lcom/facebook/ads/internal/protocol/AdErrorType;Ljava/lang/String;)Lcom/facebook/ads/redexgen/X/Jb;

    move-result-object v5

    .line 57038
    .local v0, "error":Lcom/facebook/ads/redexgen/X/Jb;
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/V2;->A11()Lcom/facebook/ads/redexgen/X/Yn;

    move-result-object v0

    .line 57039
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/Yn;->A0E()Lcom/facebook/ads/redexgen/X/0S;

    move-result-object v4

    iget-wide v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A00:J

    .line 57040
    invoke-static {v0, v1}, Lcom/facebook/ads/redexgen/X/Lf;->A01(J)J

    move-result-wide v2

    .line 57041
    invoke-virtual {v5}, Lcom/facebook/ads/redexgen/X/Jb;->A03()Lcom/facebook/ads/internal/protocol/AdErrorType;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/internal/protocol/AdErrorType;->getErrorCode()I

    move-result v1

    .line 57042
    invoke-virtual {v5}, Lcom/facebook/ads/redexgen/X/Jb;->A04()Ljava/lang/String;

    move-result-object v0

    .line 57043
    invoke-interface {v4, v2, v3, v1, v0}, Lcom/facebook/ads/redexgen/X/0S;->A2m(JILjava/lang/String;)V

    .line 57044
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0G:Lcom/facebook/ads/redexgen/X/V1;

    invoke-interface {v0, v5}, Lcom/facebook/ads/redexgen/X/JH;->ABR(Lcom/facebook/ads/redexgen/X/Jb;)V

    goto/16 :goto_0

    :cond_4
    if-eqz v3, :cond_1

    goto :goto_3

    .line 57045
    .local v0, "adapter":Lcom/facebook/ads/redexgen/X/bK;
    :cond_5
    sget-object v2, Lcom/facebook/ads/redexgen/X/V2;->A0m:[Ljava/lang/String;

    const-string v1, "bG4PdUDVMluUNyK8pZyXt2dq2VQBZyfQ"

    const/4 v0, 0x0

    aput-object v1, v2, v0

    const-string v1, "sHMyAJzOe9OYyidAIXRm3TyTiII6Suie"

    const/4 v0, 0x2

    aput-object v1, v2, v0

    if-eqz v3, :cond_6

    goto :goto_1

    .line 57046
    .end local v1
    :cond_6
    const/4 v2, 0x0

    const/4 v1, 0x0

    const/16 v0, 0x15

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/V2;->A0W(III)Ljava/lang/String;

    move-result-object v2

    goto :goto_2

    :cond_7
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0
.end method

.method public final A1d()Z
    .locals 1

    .line 57047
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A07:Lcom/facebook/ads/NativeAdLayout;

    if-nez v0, :cond_0

    const/4 v0, 0x1

    :goto_0
    return v0

    :cond_0
    const/4 v0, 0x0

    goto :goto_0
.end method

.method public final A6c()I
    .locals 2

    .line 57048
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/V2;->A06:Landroid/view/View;

    .line 57049
    .local v0, "nativeAdView":Landroid/view/View;
    instance-of v0, v1, Lcom/facebook/ads/internal/api/AdNativeComponentView;

    if-eqz v0, :cond_0

    .line 57050
    check-cast v1, Lcom/facebook/ads/internal/api/AdNativeComponentView;

    invoke-virtual {v1}, Lcom/facebook/ads/internal/api/AdNativeComponentView;->getAdContentsView()Landroid/view/View;

    move-result-object v1

    .line 57051
    .local v1, "videoView":Landroid/view/View;
    instance-of v0, v1, Lcom/facebook/ads/redexgen/X/QJ;

    if-eqz v0, :cond_0

    .line 57052
    check-cast v1, Lcom/facebook/ads/redexgen/X/QJ;

    invoke-virtual {v1}, Lcom/facebook/ads/redexgen/X/QJ;->getCurrentPosition()I

    move-result v0

    return v0

    .line 57053
    .end local v1    # "videoView":Landroid/view/View;
    :cond_0
    const/4 v0, -0x1

    return v0
.end method

.method public final buildLoadAdConfig(Lcom/facebook/ads/NativeAdBase;)Lcom/facebook/ads/NativeAdBase$NativeAdLoadConfigBuilder;
    .locals 1

    .line 57054
    new-instance v0, Lcom/facebook/ads/redexgen/X/JV;

    invoke-direct {v0, p0, p1}, Lcom/facebook/ads/redexgen/X/JV;-><init>(Lcom/facebook/ads/redexgen/X/V2;Lcom/facebook/ads/NativeAdBase;)V

    return-object v0
.end method

.method public final destroy()V
    .locals 5

    const/16 v2, 0x16b

    const/16 v1, 0x13

    const/16 v0, 0x17

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/V2;->A0W(III)Ljava/lang/String;

    move-result-object v4

    const/4 v2, 0x5

    const/16 v1, 0x8

    const/16 v0, 0x4c

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/V2;->A0W(III)Ljava/lang/String;

    move-result-object v3

    const/16 v2, 0x229

    const/4 v1, 0x7

    const/4 v0, 0x7

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/V2;->A0W(III)Ljava/lang/String;

    move-result-object v0

    invoke-static {v0, v4, v3}, Lcom/facebook/ads/redexgen/X/Jp;->A05(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    .line 57055
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0c:Lcom/facebook/ads/redexgen/X/Yn;

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/Ih;->A1W(Landroid/content/Context;)Z

    move-result v0

    if-eqz v0, :cond_0

    .line 57056
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/V2;->A11()Lcom/facebook/ads/redexgen/X/Yn;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/7f;->A0A()Lcom/facebook/ads/redexgen/X/JE;

    move-result-object v1

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A03:Landroid/view/View;

    invoke-interface {v1, v0}, Lcom/facebook/ads/redexgen/X/JE;->AGk(Landroid/view/View;)V

    .line 57057
    :cond_0
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/V2;->A0q()Z

    move-result v0

    if-nez v0, :cond_1

    .line 57058
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0c:Lcom/facebook/ads/redexgen/X/Yn;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/Yn;->A0E()Lcom/facebook/ads/redexgen/X/0S;

    move-result-object v0

    invoke-interface {v0}, Lcom/facebook/ads/redexgen/X/0S;->A2t()V

    .line 57059
    :cond_1
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/V2;->A0A:Lcom/facebook/ads/redexgen/X/FO;

    if-eqz v1, :cond_2

    .line 57060
    const/4 v0, 0x1

    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/am;->A0V(Z)V

    .line 57061
    const/4 v0, 0x0

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0A:Lcom/facebook/ads/redexgen/X/FO;

    .line 57062
    :cond_2
    return-void
.end method

.method public final downloadMedia()V
    .locals 2

    .line 57063
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/V2;->A0E:Lcom/facebook/ads/redexgen/X/JL;

    sget-object v0, Lcom/facebook/ads/redexgen/X/JL;->A05:Lcom/facebook/ads/redexgen/X/JL;

    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/JL;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_0

    .line 57064
    sget-object v0, Lcom/facebook/ads/redexgen/X/12;->A04:Lcom/facebook/ads/redexgen/X/12;

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A08:Lcom/facebook/ads/redexgen/X/12;

    .line 57065
    :cond_0
    sget-object v0, Lcom/facebook/ads/redexgen/X/JL;->A04:Lcom/facebook/ads/redexgen/X/JL;

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0E:Lcom/facebook/ads/redexgen/X/JL;

    .line 57066
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/V2;->A0a:Lcom/facebook/ads/redexgen/X/bK;

    const/4 v0, 0x0

    invoke-direct {p0, v1, v0}, Lcom/facebook/ads/redexgen/X/V2;->A0h(Lcom/facebook/ads/redexgen/X/bK;Z)V

    .line 57067
    return-void
.end method

.method public final getAdBodyText()Ljava/lang/String;
    .locals 1

    .line 57068
    const/4 v0, 0x1

    invoke-direct {p0, v0}, Lcom/facebook/ads/redexgen/X/V2;->A0G(Z)Lcom/facebook/ads/redexgen/X/14;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/14;->A0J()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public final getAdCallToAction()Ljava/lang/String;
    .locals 1

    .line 57069
    const/4 v0, 0x1

    invoke-direct {p0, v0}, Lcom/facebook/ads/redexgen/X/V2;->A0G(Z)Lcom/facebook/ads/redexgen/X/14;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/14;->A0X()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public final bridge synthetic getAdChoicesIcon()Lcom/facebook/ads/internal/api/NativeAdImageApi;
    .locals 1

    .line 57070
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/V2;->A0M()Lcom/facebook/ads/redexgen/X/JP;

    move-result-object v0

    return-object v0
.end method

.method public final getAdChoicesImageUrl()Ljava/lang/String;
    .locals 4

    .line 57071
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/V2;->A0M()Lcom/facebook/ads/redexgen/X/JP;

    move-result-object v0

    if-nez v0, :cond_0

    const/4 v0, 0x0

    :goto_0
    return-object v0

    :cond_0
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/V2;->A0M()Lcom/facebook/ads/redexgen/X/JP;

    move-result-object v3

    sget-object v2, Lcom/facebook/ads/redexgen/X/V2;->A0m:[Ljava/lang/String;

    const/4 v0, 0x0

    aget-object v1, v2, v0

    const/4 v0, 0x2

    aget-object v2, v2, v0

    const/4 v0, 0x5

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    invoke-virtual {v2, v0}, Ljava/lang/String;->charAt(I)C

    move-result v0

    if-eq v1, v0, :cond_1

    sget-object v2, Lcom/facebook/ads/redexgen/X/V2;->A0m:[Ljava/lang/String;

    const-string v1, "MRTmAh7YnfwjJHGdoro5GGF"

    const/4 v0, 0x7

    aput-object v1, v2, v0

    const-string v1, "5NAjexdbITBZf7E7VHPXfXw"

    const/4 v0, 0x5

    aput-object v1, v2, v0

    invoke-virtual {v3}, Lcom/facebook/ads/redexgen/X/JP;->getUrl()Ljava/lang/String;

    move-result-object v0

    goto :goto_0

    :cond_1
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0
.end method

.method public final getAdChoicesLinkUrl()Ljava/lang/String;
    .locals 1

    .line 57072
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/V2;->A0F()Lcom/facebook/ads/redexgen/X/14;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/14;->A0K()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public final getAdChoicesText()Ljava/lang/String;
    .locals 1

    .line 57073
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/V2;->A0F()Lcom/facebook/ads/redexgen/X/14;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/14;->A0L()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public final bridge synthetic getAdCoverImage()Lcom/facebook/ads/internal/api/NativeAdImageApi;
    .locals 1

    .line 57074
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/V2;->A13()Lcom/facebook/ads/redexgen/X/JP;

    move-result-object v0

    return-object v0
.end method

.method public final getAdHeadline()Ljava/lang/String;
    .locals 1

    .line 57075
    const/4 v0, 0x1

    invoke-direct {p0, v0}, Lcom/facebook/ads/redexgen/X/V2;->A0G(Z)Lcom/facebook/ads/redexgen/X/14;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/14;->A0M()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public final bridge synthetic getAdIcon()Lcom/facebook/ads/internal/api/NativeAdImageApi;
    .locals 1

    .line 57076
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/V2;->A14()Lcom/facebook/ads/redexgen/X/JP;

    move-result-object v0

    return-object v0
.end method

.method public final getAdLinkDescription()Ljava/lang/String;
    .locals 1

    .line 57077
    const/4 v0, 0x1

    invoke-direct {p0, v0}, Lcom/facebook/ads/redexgen/X/V2;->A0G(Z)Lcom/facebook/ads/redexgen/X/14;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/14;->A0N()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public final getAdSocialContext()Ljava/lang/String;
    .locals 1

    .line 57078
    const/4 v0, 0x1

    invoke-direct {p0, v0}, Lcom/facebook/ads/redexgen/X/V2;->A0G(Z)Lcom/facebook/ads/redexgen/X/14;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/14;->A0R()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public final bridge synthetic getAdStarRating()Lcom/facebook/ads/internal/api/NativeAdRatingApi;
    .locals 1

    .line 57079
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/V2;->A0O()Lcom/facebook/ads/redexgen/X/JQ;

    move-result-object v0

    return-object v0
.end method

.method public final getAdTranslation()Ljava/lang/String;
    .locals 1

    .line 57080
    const/4 v0, 0x1

    invoke-direct {p0, v0}, Lcom/facebook/ads/redexgen/X/V2;->A0G(Z)Lcom/facebook/ads/redexgen/X/14;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/14;->A0U()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public final getAdUntrimmedBodyText()Ljava/lang/String;
    .locals 1

    .line 57081
    const/4 v0, 0x1

    invoke-direct {p0, v0}, Lcom/facebook/ads/redexgen/X/V2;->A0G(Z)Lcom/facebook/ads/redexgen/X/14;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/14;->A0V()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public final getAdvertiserName()Ljava/lang/String;
    .locals 1

    .line 57082
    const/4 v0, 0x1

    invoke-direct {p0, v0}, Lcom/facebook/ads/redexgen/X/V2;->A0G(Z)Lcom/facebook/ads/redexgen/X/14;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/14;->A0W()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public final getAspectRatio()F
    .locals 5

    .line 57083
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0a:Lcom/facebook/ads/redexgen/X/bK;

    const/4 v3, 0x0

    if-eqz v0, :cond_2

    .line 57084
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/bK;->A0E()Lcom/facebook/ads/redexgen/X/14;

    move-result-object v4

    sget-object v2, Lcom/facebook/ads/redexgen/X/V2;->A0m:[Ljava/lang/String;

    const/4 v0, 0x7

    aget-object v1, v2, v0

    const/4 v0, 0x5

    aget-object v0, v2, v0

    invoke-virtual {v1}, Ljava/lang/String;->length()I

    move-result v1

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v0

    if-eq v1, v0, :cond_0

    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    :cond_0
    sget-object v2, Lcom/facebook/ads/redexgen/X/V2;->A0m:[Ljava/lang/String;

    const-string v1, "Gk0JtmCCXYpL8dPDyrRdSPBqQO7pSw2M"

    const/4 v0, 0x1

    aput-object v1, v2, v0

    const-string v1, "hZFl2ZrFl9W40ZKmdwMt57qcTxbjzHrP"

    const/4 v0, 0x3

    aput-object v1, v2, v0

    invoke-virtual {v4}, Lcom/facebook/ads/redexgen/X/14;->A0F()Lcom/facebook/ads/redexgen/X/JP;

    move-result-object v0

    .line 57085
    .local v0, "nativeAdImage":Lcom/facebook/ads/redexgen/X/JP;
    if-eqz v0, :cond_2

    .line 57086
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/JP;->getWidth()I

    move-result v1

    .line 57087
    .local v2, "width":I
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/JP;->getHeight()I

    move-result v0

    .line 57088
    .local v3, "height":I
    if-lez v0, :cond_1

    int-to-float v3, v1

    int-to-float v0, v0

    div-float/2addr v3, v0

    :cond_1
    return v3

    .line 57089
    .end local v0    # "nativeAdImage":Lcom/facebook/ads/redexgen/X/JP;
    .end local v2    # "width":I
    .end local v3    # "height":I
    :cond_2
    return v3
.end method

.method public final getId()Ljava/lang/String;
    .locals 4

    .line 57090
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/V2;->isAdLoaded()Z

    move-result v0

    if-nez v0, :cond_0

    .line 57091
    const/4 v0, 0x0

    return-object v0

    .line 57092
    :cond_0
    iget-object v3, p0, Lcom/facebook/ads/redexgen/X/V2;->A0h:Ljava/lang/String;

    sget-object v2, Lcom/facebook/ads/redexgen/X/V2;->A0m:[Ljava/lang/String;

    const/4 v0, 0x4

    aget-object v1, v2, v0

    const/4 v0, 0x6

    aget-object v0, v2, v0

    invoke-virtual {v1}, Ljava/lang/String;->length()I

    move-result v1

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v0

    if-eq v1, v0, :cond_1

    sget-object v2, Lcom/facebook/ads/redexgen/X/V2;->A0m:[Ljava/lang/String;

    const-string v1, "iawtn5ImZtHDfKUJHK1ePg9"

    const/4 v0, 0x7

    aput-object v1, v2, v0

    const-string v1, "61vZsTsWUXSHOR043CjOsZn"

    const/4 v0, 0x5

    aput-object v1, v2, v0

    return-object v3

    :cond_1
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0
.end method

.method public final getPlacementId()Ljava/lang/String;
    .locals 1

    .line 57093
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0g:Ljava/lang/String;

    return-object v0
.end method

.method public final getPreloadedIconViewDrawable()Landroid/graphics/drawable/Drawable;
    .locals 6

    .line 57094
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0a:Lcom/facebook/ads/redexgen/X/bK;

    .line 57095
    .local v0, "adapter":Lcom/facebook/ads/redexgen/X/bK;
    if-eqz v0, :cond_1

    .line 57096
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/V2;->A0F()Lcom/facebook/ads/redexgen/X/14;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/14;->A0G()Lcom/facebook/ads/redexgen/X/JP;

    move-result-object v0

    .line 57097
    .local v1, "adIcon":Lcom/facebook/ads/redexgen/X/JP;
    if-eqz v0, :cond_1

    .line 57098
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/V2;->A0b:Lcom/facebook/ads/redexgen/X/6c;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/JP;->getUrl()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/6c;->A0M(Ljava/lang/String;)Landroid/graphics/Bitmap;

    move-result-object v5

    .line 57099
    .local v2, "preloadedBitmap":Landroid/graphics/Bitmap;
    if-eqz v5, :cond_1

    .line 57100
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/V2;->A11()Lcom/facebook/ads/redexgen/X/Yn;

    move-result-object v4

    .line 57101
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/V2;->A1d()Z

    move-result v3

    sget-object v2, Lcom/facebook/ads/redexgen/X/V2;->A0m:[Ljava/lang/String;

    const/4 v0, 0x0

    aget-object v1, v2, v0

    const/4 v0, 0x2

    aget-object v2, v2, v0

    const/4 v0, 0x5

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    invoke-virtual {v2, v0}, Ljava/lang/String;->charAt(I)C

    move-result v0

    if-eq v1, v0, :cond_0

    .line 57102
    sget-object v2, Lcom/facebook/ads/redexgen/X/V2;->A0m:[Ljava/lang/String;

    const-string v1, "5rXv1LFjMRKkpcU7"

    const/4 v0, 0x4

    aput-object v1, v2, v0

    const-string v1, "SFgDKFOuseBk"

    const/4 v0, 0x6

    aput-object v1, v2, v0

    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/V2;->A1C()Ljava/lang/String;

    move-result-object v0

    .line 57103
    invoke-static {v4, v5, v3, v0}, Lcom/facebook/ads/redexgen/X/V2;->A05(Lcom/facebook/ads/redexgen/X/Yn;Landroid/graphics/Bitmap;ZLjava/lang/String;)Landroid/graphics/drawable/Drawable;

    move-result-object v0

    return-object v0

    :cond_0
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    .line 57104
    .end local v1    # "adIcon":Lcom/facebook/ads/redexgen/X/JP;
    .end local v2    # "preloadedBitmap":Landroid/graphics/Bitmap;
    :cond_1
    const/4 v0, 0x0

    return-object v0
.end method

.method public final getPromotedTranslation()Ljava/lang/String;
    .locals 1

    .line 57105
    const/4 v0, 0x1

    invoke-direct {p0, v0}, Lcom/facebook/ads/redexgen/X/V2;->A0G(Z)Lcom/facebook/ads/redexgen/X/14;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/14;->A0Q()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public final getSponsoredTranslation()Ljava/lang/String;
    .locals 1

    .line 57106
    const/4 v0, 0x1

    invoke-direct {p0, v0}, Lcom/facebook/ads/redexgen/X/V2;->A0G(Z)Lcom/facebook/ads/redexgen/X/14;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/14;->A0S()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public final hasCallToAction()Z
    .locals 1

    .line 57107
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0a:Lcom/facebook/ads/redexgen/X/bK;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/bK;->A0Q()Z

    move-result v0

    if-eqz v0, :cond_0

    const/4 v0, 0x1

    :goto_0
    return v0

    :cond_0
    const/4 v0, 0x0

    goto :goto_0
.end method

.method public final isAdInvalidated()Z
    .locals 5

    .line 57108
    const/4 v4, 0x1

    .line 57109
    .local v0, "result":Z
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0A:Lcom/facebook/ads/redexgen/X/FO;

    if-eqz v0, :cond_1

    .line 57110
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/am;->A0W()Z

    move-result v4

    .line 57111
    :cond_0
    :goto_0
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0c:Lcom/facebook/ads/redexgen/X/Yn;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/Yn;->A0E()Lcom/facebook/ads/redexgen/X/0S;

    move-result-object v0

    invoke-interface {v0, v4}, Lcom/facebook/ads/redexgen/X/0S;->A4s(Z)V

    .line 57112
    return v4

    .line 57113
    :cond_1
    iget-object v3, p0, Lcom/facebook/ads/redexgen/X/V2;->A0B:Lcom/facebook/ads/redexgen/X/ag;

    sget-object v2, Lcom/facebook/ads/redexgen/X/V2;->A0m:[Ljava/lang/String;

    const/4 v0, 0x1

    aget-object v1, v2, v0

    const/4 v0, 0x3

    aget-object v2, v2, v0

    const/16 v0, 0x10

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    invoke-virtual {v2, v0}, Ljava/lang/String;->charAt(I)C

    move-result v0

    if-eq v1, v0, :cond_2

    sget-object v2, Lcom/facebook/ads/redexgen/X/V2;->A0m:[Ljava/lang/String;

    const-string v1, "v5uFzvuQ32LyebguaenlI2Y"

    const/4 v0, 0x7

    aput-object v1, v2, v0

    const-string v1, "nsJGpwd0QPZOfVeZMyNRGWR"

    const/4 v0, 0x5

    aput-object v1, v2, v0

    if-eqz v3, :cond_0

    .line 57114
    invoke-virtual {v3}, Lcom/facebook/ads/redexgen/X/ag;->A0A()Z

    move-result v4

    goto :goto_0

    :cond_2
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0
.end method

.method public final isAdLoaded()Z
    .locals 1

    .line 57115
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0a:Lcom/facebook/ads/redexgen/X/bK;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/bK;->A0R()Z

    move-result v0

    if-eqz v0, :cond_0

    const/4 v0, 0x1

    :goto_0
    return v0

    :cond_0
    const/4 v0, 0x0

    goto :goto_0
.end method

.method public final loadAd()V
    .locals 5

    const/16 v2, 0x17e

    const/16 v1, 0x18

    const/16 v0, 0x62

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/V2;->A0W(III)Ljava/lang/String;

    move-result-object v4

    const/16 v2, 0xd

    const/16 v1, 0x8

    const/16 v0, 0x59

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/V2;->A0W(III)Ljava/lang/String;

    move-result-object v3

    const/16 v2, 0x230

    const/4 v1, 0x6

    const/16 v0, 0xe

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/V2;->A0W(III)Ljava/lang/String;

    move-result-object v0

    invoke-static {v0, v4, v3}, Lcom/facebook/ads/redexgen/X/Jp;->A05(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    .line 57116
    sget-object v0, Lcom/facebook/ads/NativeAdBase$MediaCacheFlag;->ALL:Lcom/facebook/ads/NativeAdBase$MediaCacheFlag;

    .line 57117
    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/JL;->A00(Lcom/facebook/ads/NativeAdBase$MediaCacheFlag;)Lcom/facebook/ads/redexgen/X/JL;

    move-result-object v3

    const/4 v2, 0x0

    const/4 v0, -0x1

    new-instance v1, Lcom/facebook/ads/redexgen/X/6i;

    invoke-direct {v1, v2, v0, v0}, Lcom/facebook/ads/redexgen/X/6i;-><init>(ZII)V

    .line 57118
    const/4 v0, 0x0

    invoke-virtual {p0, v3, v0, v1}, Lcom/facebook/ads/redexgen/X/V2;->A1V(Lcom/facebook/ads/redexgen/X/JL;Ljava/lang/String;Lcom/facebook/ads/redexgen/X/6i;)V

    .line 57119
    return-void
.end method

.method public final loadAd(Lcom/facebook/ads/NativeAdBase$NativeLoadAdConfig;)V
    .locals 5

    const/16 v2, 0x17e

    const/16 v1, 0x18

    const/16 v0, 0x62

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/V2;->A0W(III)Ljava/lang/String;

    move-result-object v4

    const/16 v2, 0x15

    const/16 v1, 0x8

    const/16 v0, 0x7e

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/V2;->A0W(III)Ljava/lang/String;

    move-result-object v3

    const/16 v2, 0x230

    const/4 v1, 0x6

    const/16 v0, 0xe

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/V2;->A0W(III)Ljava/lang/String;

    move-result-object v0

    invoke-static {v0, v4, v3}, Lcom/facebook/ads/redexgen/X/Jp;->A05(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    .line 57120
    check-cast p1, Lcom/facebook/ads/redexgen/X/JV;

    invoke-virtual {p1}, Lcom/facebook/ads/redexgen/X/JV;->A00()V

    .line 57121
    return-void
.end method

.method public final onCtaBroadcast()V
    .locals 1

    .line 57122
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A05:Landroid/view/View;

    if-eqz v0, :cond_0

    .line 57123
    invoke-virtual {v0}, Landroid/view/View;->performClick()Z

    .line 57124
    :cond_0
    return-void
.end method

.method public final repair(Ljava/lang/Throwable;)V
    .locals 5

    .line 57125
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/V2;->A04:Landroid/view/View;

    if-eqz v1, :cond_0

    .line 57126
    new-instance v0, Lcom/facebook/ads/redexgen/X/VD;

    invoke-direct {v0, p0}, Lcom/facebook/ads/redexgen/X/VD;-><init>(Lcom/facebook/ads/redexgen/X/V2;)V

    invoke-virtual {v1, v0}, Landroid/view/View;->post(Ljava/lang/Runnable;)Z

    .line 57127
    :cond_0
    const/16 v4, 0x7d1

    .line 57128
    .local v0, "errorCode":I
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const/16 v2, 0xa4

    const/16 v1, 0x10

    const/16 v0, 0x40

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/V2;->A0W(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0c:Lcom/facebook/ads/redexgen/X/Yn;

    .line 57129
    invoke-static {v0, p1}, Lcom/facebook/ads/redexgen/X/LW;->A03(Landroid/content/Context;Ljava/lang/Throwable;)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    .line 57130
    .local v1, "errorMessage":Ljava/lang/String;
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/V2;->A11()Lcom/facebook/ads/redexgen/X/Yn;

    move-result-object v0

    .line 57131
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/Yn;->A0E()Lcom/facebook/ads/redexgen/X/0S;

    move-result-object v2

    iget-wide v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A00:J

    .line 57132
    invoke-static {v0, v1}, Lcom/facebook/ads/redexgen/X/Lf;->A01(J)J

    move-result-wide v0

    invoke-interface {v2, v0, v1, v4, v3}, Lcom/facebook/ads/redexgen/X/0S;->A2m(JILjava/lang/String;)V

    .line 57133
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/V2;->A0G:Lcom/facebook/ads/redexgen/X/V1;

    if-eqz v1, :cond_1

    .line 57134
    new-instance v0, Lcom/facebook/ads/redexgen/X/Jb;

    invoke-direct {v0, v4, v3}, Lcom/facebook/ads/redexgen/X/Jb;-><init>(ILjava/lang/String;)V

    invoke-interface {v1, v0}, Lcom/facebook/ads/redexgen/X/JH;->ABR(Lcom/facebook/ads/redexgen/X/Jb;)V

    .line 57135
    :cond_1
    return-void
.end method

.method public final setExtraHints(Lcom/facebook/ads/ExtraHints;)V
    .locals 1

    .line 57136
    if-nez p1, :cond_0

    .line 57137
    return-void

    .line 57138
    :cond_0
    invoke-virtual {p1}, Lcom/facebook/ads/ExtraHints;->getHints()Ljava/lang/String;

    move-result-object v0

    invoke-direct {p0, v0}, Lcom/facebook/ads/redexgen/X/V2;->A0n(Ljava/lang/String;)V

    .line 57139
    invoke-virtual {p1}, Lcom/facebook/ads/ExtraHints;->getMediationData()Ljava/lang/String;

    move-result-object v0

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0T:Ljava/lang/String;

    .line 57140
    return-void
.end method

.method public final setOnTouchListener(Landroid/view/View$OnTouchListener;)V
    .locals 0

    .line 57141
    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/V2;->A02:Landroid/view/View$OnTouchListener;

    .line 57142
    return-void
.end method

.method public final unregisterView()V
    .locals 7

    .line 57143
    iget-object v2, p0, Lcom/facebook/ads/redexgen/X/V2;->A0N:Lcom/facebook/ads/redexgen/X/Px;

    .line 57144
    .local v0, "overlayView":Lcom/facebook/ads/redexgen/X/Px;
    const/4 v3, 0x0

    if-eqz v2, :cond_1

    .line 57145
    invoke-virtual {v2}, Lcom/facebook/ads/redexgen/X/Px;->getParent()Landroid/view/ViewParent;

    move-result-object v1

    .line 57146
    .local v2, "parent":Landroid/view/ViewParent;
    instance-of v0, v1, Landroid/view/ViewGroup;

    if-eqz v0, :cond_0

    .line 57147
    check-cast v1, Landroid/view/ViewGroup;

    invoke-virtual {v1, v2}, Landroid/view/ViewGroup;->removeView(Landroid/view/View;)V

    .line 57148
    :cond_0
    iput-object v3, p0, Lcom/facebook/ads/redexgen/X/V2;->A0N:Lcom/facebook/ads/redexgen/X/Px;

    .line 57149
    .end local v2    # "parent":Landroid/view/ViewParent;
    :cond_1
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A04:Landroid/view/View;

    if-eqz v0, :cond_2

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A06:Landroid/view/View;

    if-nez v0, :cond_3

    .line 57150
    :cond_2
    return-void

    .line 57151
    :cond_3
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/V2;->A0q()Z

    move-result v0

    if-nez v0, :cond_4

    .line 57152
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0c:Lcom/facebook/ads/redexgen/X/Yn;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/Yn;->A0E()Lcom/facebook/ads/redexgen/X/0S;

    move-result-object v4

    sget-object v2, Lcom/facebook/ads/redexgen/X/V2;->A0m:[Ljava/lang/String;

    const/4 v0, 0x4

    aget-object v1, v2, v0

    const/4 v0, 0x6

    aget-object v0, v2, v0

    invoke-virtual {v1}, Ljava/lang/String;->length()I

    move-result v1

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v0

    if-eq v1, v0, :cond_d

    sget-object v2, Lcom/facebook/ads/redexgen/X/V2;->A0m:[Ljava/lang/String;

    const-string v1, "cYEHlIHF7Qe4IYNxEGisZavvdYqfT1NQ"

    const/4 v0, 0x1

    aput-object v1, v2, v0

    const-string v1, "IvkWoeS5LcA5RnG3xI5yLmU1spuVl7qW"

    const/4 v0, 0x3

    aput-object v1, v2, v0

    invoke-interface {v4}, Lcom/facebook/ads/redexgen/X/0S;->unregisterView()V

    .line 57153
    :cond_4
    iget-object v4, p0, Lcom/facebook/ads/redexgen/X/V2;->A0c:Lcom/facebook/ads/redexgen/X/Yn;

    sget-object v2, Lcom/facebook/ads/redexgen/X/V2;->A0m:[Ljava/lang/String;

    const/4 v0, 0x1

    aget-object v1, v2, v0

    const/4 v0, 0x3

    aget-object v2, v2, v0

    const/16 v0, 0x10

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    invoke-virtual {v2, v0}, Ljava/lang/String;->charAt(I)C

    move-result v0

    if-eq v1, v0, :cond_b

    sget-object v2, Lcom/facebook/ads/redexgen/X/V2;->A0m:[Ljava/lang/String;

    const-string v1, "MOnirYFFQGBOpDM1usFuy9dITQXNjsmg"

    const/4 v0, 0x0

    aput-object v1, v2, v0

    const-string v1, "BENjy9ISBGpLCtRbprsy2gYEceZw0rIm"

    const/4 v0, 0x2

    aput-object v1, v2, v0

    invoke-static {v4}, Lcom/facebook/ads/redexgen/X/Ih;->A1W(Landroid/content/Context;)Z

    move-result v0

    if-eqz v0, :cond_5

    .line 57154
    :goto_0
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/V2;->A11()Lcom/facebook/ads/redexgen/X/Yn;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/7f;->A0A()Lcom/facebook/ads/redexgen/X/JE;

    move-result-object v1

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A03:Landroid/view/View;

    invoke-interface {v1, v0}, Lcom/facebook/ads/redexgen/X/JE;->AGk(Landroid/view/View;)V

    .line 57155
    :cond_5
    sget-object v4, Lcom/facebook/ads/redexgen/X/V2;->A0o:Ljava/util/WeakHashMap;

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A04:Landroid/view/View;

    invoke-virtual {v4, v0}, Ljava/util/WeakHashMap;->containsKey(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_c

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A04:Landroid/view/View;

    invoke-virtual {v4, v0}, Ljava/util/WeakHashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/lang/ref/WeakReference;

    invoke-virtual {v0}, Ljava/lang/ref/WeakReference;->get()Ljava/lang/Object;

    move-result-object v0

    if-ne v0, p0, :cond_c

    .line 57156
    iget-object v5, p0, Lcom/facebook/ads/redexgen/X/V2;->A04:Landroid/view/View;

    instance-of v6, v5, Landroid/view/ViewGroup;

    sget-object v2, Lcom/facebook/ads/redexgen/X/V2;->A0m:[Ljava/lang/String;

    const/4 v0, 0x4

    aget-object v1, v2, v0

    const/4 v0, 0x6

    aget-object v0, v2, v0

    invoke-virtual {v1}, Ljava/lang/String;->length()I

    move-result v1

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v0

    if-eq v1, v0, :cond_a

    sget-object v2, Lcom/facebook/ads/redexgen/X/V2;->A0m:[Ljava/lang/String;

    const-string v1, "lVWmVGygxw0UbRbggHe19cSptvLLteak"

    const/4 v0, 0x0

    aput-object v1, v2, v0

    const-string v1, "yD1YSLWVotW9RM3Ng5RpEu8J61bKeSm7"

    const/4 v0, 0x2

    aput-object v1, v2, v0

    if-eqz v6, :cond_6

    :goto_1
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0K:Lcom/facebook/ads/redexgen/X/Mi;

    if-eqz v0, :cond_6

    .line 57157
    check-cast v5, Landroid/view/ViewGroup;

    invoke-virtual {v5, v0}, Landroid/view/ViewGroup;->removeView(Landroid/view/View;)V

    .line 57158
    iput-object v3, p0, Lcom/facebook/ads/redexgen/X/V2;->A0K:Lcom/facebook/ads/redexgen/X/Mi;

    .line 57159
    :cond_6
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0a:Lcom/facebook/ads/redexgen/X/bK;

    if-eqz v0, :cond_7

    .line 57160
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/bK;->A0J()V

    .line 57161
    :cond_7
    sget v1, Landroid/os/Build$VERSION;->SDK_INT:I

    const/16 v0, 0x12

    if-lt v1, v0, :cond_8

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0L:Lcom/facebook/ads/redexgen/X/Ng;

    if-eqz v0, :cond_8

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0c:Lcom/facebook/ads/redexgen/X/Yn;

    .line 57162
    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/Ih;->A11(Landroid/content/Context;)Z

    move-result v0

    if-eqz v0, :cond_8

    .line 57163
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0L:Lcom/facebook/ads/redexgen/X/Ng;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/Ng;->A07()V

    .line 57164
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A04:Landroid/view/View;

    invoke-virtual {v0}, Landroid/view/View;->getOverlay()Landroid/view/ViewOverlay;

    move-result-object v1

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0L:Lcom/facebook/ads/redexgen/X/Ng;

    invoke-virtual {v1, v0}, Landroid/view/ViewOverlay;->remove(Landroid/graphics/drawable/Drawable;)V

    .line 57165
    :cond_8
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A04:Landroid/view/View;

    invoke-virtual {v4, v0}, Ljava/util/WeakHashMap;->remove(Ljava/lang/Object;)Ljava/lang/Object;

    .line 57166
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/V2;->A0Z()V

    .line 57167
    iput-object v3, p0, Lcom/facebook/ads/redexgen/X/V2;->A04:Landroid/view/View;

    .line 57168
    iput-object v3, p0, Lcom/facebook/ads/redexgen/X/V2;->A06:Landroid/view/View;

    .line 57169
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V2;->A0R:Lcom/facebook/ads/redexgen/X/RE;

    if-eqz v0, :cond_9

    .line 57170
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/RE;->A0V()V

    .line 57171
    iput-object v3, p0, Lcom/facebook/ads/redexgen/X/V2;->A0R:Lcom/facebook/ads/redexgen/X/RE;

    .line 57172
    :cond_9
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/V2;->A0c()V

    .line 57173
    iput-object v3, p0, Lcom/facebook/ads/redexgen/X/V2;->A09:Lcom/facebook/ads/redexgen/X/bA;

    .line 57174
    return-void

    :cond_a
    if-eqz v6, :cond_6

    goto :goto_1

    :cond_b
    invoke-static {v4}, Lcom/facebook/ads/redexgen/X/Ih;->A1W(Landroid/content/Context;)Z

    move-result v0

    if-eqz v0, :cond_5

    goto/16 :goto_0

    .line 57175
    :cond_c
    const/16 v2, 0x1e1

    const/16 v1, 0x26

    const/16 v0, 0x48

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/V2;->A0W(III)Ljava/lang/String;

    move-result-object v1

    new-instance v0, Ljava/lang/IllegalStateException;

    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw v0

    :cond_d
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0
.end method
