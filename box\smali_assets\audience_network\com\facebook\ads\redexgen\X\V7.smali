.class public final Lcom/facebook/ads/redexgen/X/V7;
.super Lcom/facebook/ads/redexgen/X/RD;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/facebook/ads/redexgen/X/V2;->A0f(Landroid/view/View;Landroid/view/View;Ljava/util/List;Z)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field public final synthetic A00:Lcom/facebook/ads/redexgen/X/V2;


# direct methods
.method public constructor <init>(Lcom/facebook/ads/redexgen/X/V2;)V
    .locals 0

    .line 57274
    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/V7;->A00:Lcom/facebook/ads/redexgen/X/V2;

    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/RD;-><init>()V

    return-void
.end method


# virtual methods
.method public final A03()V
    .locals 1

    .line 57275
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V7;->A00:Lcom/facebook/ads/redexgen/X/V2;

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/V2;->A0I(Lcom/facebook/ads/redexgen/X/V2;)Lcom/facebook/ads/redexgen/X/Yn;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/Yn;->A0E()Lcom/facebook/ads/redexgen/X/0S;

    move-result-object v0

    invoke-interface {v0}, Lcom/facebook/ads/redexgen/X/0S;->AAV()V

    .line 57276
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/V7;->A00:Lcom/facebook/ads/redexgen/X/V2;

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/V2;->A0l(Lcom/facebook/ads/redexgen/X/V2;)V

    .line 57277
    return-void
.end method
