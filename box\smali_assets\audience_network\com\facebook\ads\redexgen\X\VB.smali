.class public final Lcom/facebook/ads/redexgen/X/VB;
.super Ljava/lang/Object;
.source ""

# interfaces
.implements Lcom/facebook/ads/redexgen/X/15;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/facebook/ads/redexgen/X/V2;->A1T(Lcom/facebook/ads/redexgen/X/bK;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field public final synthetic A00:Lcom/facebook/ads/redexgen/X/V2;


# direct methods
.method public constructor <init>(Lcom/facebook/ads/redexgen/X/V2;)V
    .locals 0

    .line 57312
    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/VB;->A00:Lcom/facebook/ads/redexgen/X/V2;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final ACM(Lcom/facebook/ads/redexgen/X/bK;)V
    .locals 1

    .line 57313
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/VB;->A00:Lcom/facebook/ads/redexgen/X/V2;

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/V2;->A0N(Lcom/facebook/ads/redexgen/X/V2;)Lcom/facebook/ads/redexgen/X/V1;

    move-result-object v0

    if-eqz v0, :cond_0

    .line 57314
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/VB;->A00:Lcom/facebook/ads/redexgen/X/V2;

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/V2;->A0N(Lcom/facebook/ads/redexgen/X/V2;)Lcom/facebook/ads/redexgen/X/V1;

    move-result-object v0

    invoke-interface {v0}, Lcom/facebook/ads/redexgen/X/JH;->AAc()V

    .line 57315
    :cond_0
    return-void
.end method

.method public final ACN(Lcom/facebook/ads/redexgen/X/bK;)V
    .locals 0

    .line 57316
    return-void
.end method

.method public final ACO(Lcom/facebook/ads/redexgen/X/bK;)V
    .locals 0

    .line 57317
    return-void
.end method

.method public final ACQ(Lcom/facebook/ads/redexgen/X/bK;Lcom/facebook/ads/redexgen/X/Jb;)V
    .locals 0

    .line 57318
    return-void
.end method
