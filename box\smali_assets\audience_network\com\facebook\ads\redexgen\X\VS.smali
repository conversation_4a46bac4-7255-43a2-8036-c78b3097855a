.class public final Lcom/facebook/ads/redexgen/X/VS;
.super Lcom/facebook/ads/redexgen/X/KT;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/facebook/ads/redexgen/X/VQ;-><init>(Lcom/facebook/ads/redexgen/X/Ym;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field public final synthetic A00:Lcom/facebook/ads/redexgen/X/VQ;


# direct methods
.method public constructor <init>(Lcom/facebook/ads/redexgen/X/VQ;)V
    .locals 0

    .line 58055
    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/VS;->A00:Lcom/facebook/ads/redexgen/X/VQ;

    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/KT;-><init>()V

    return-void
.end method


# virtual methods
.method public final A06()V
    .locals 1

    .line 58056
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/VS;->A00:Lcom/facebook/ads/redexgen/X/VQ;

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/VQ;->A00(Lcom/facebook/ads/redexgen/X/VQ;)Lcom/facebook/ads/redexgen/X/J1;

    move-result-object v0

    invoke-interface {v0}, Lcom/facebook/ads/redexgen/X/J1;->A5B()V

    .line 58057
    return-void
.end method
