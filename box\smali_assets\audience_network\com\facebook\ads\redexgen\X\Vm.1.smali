.class public final Lcom/facebook/ads/redexgen/X/Vm;
.super Lcom/facebook/ads/redexgen/X/HI;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/facebook/ads/redexgen/X/Vl;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "CacheDataSinkException"
.end annotation


# direct methods
.method public constructor <init>(Ljava/io/IOException;)V
    .locals 0

    .line 58952
    invoke-direct {p0, p1}, Lcom/facebook/ads/redexgen/X/HI;-><init>(Ljava/lang/Throwable;)V

    .line 58953
    return-void
.end method
