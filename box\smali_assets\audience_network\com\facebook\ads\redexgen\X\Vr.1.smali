.class public interface abstract Lcom/facebook/ads/redexgen/X/Vr;
.super Ljava/lang/Object;
.source ""

# interfaces
.implements Lcom/facebook/ads/redexgen/X/Gu;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/facebook/ads/redexgen/X/Vs;,
        Lcom/facebook/ads/redexgen/X/Vt;,
        Lcom/facebook/ads/redexgen/X/H4;,
        Lcom/facebook/ads/redexgen/X/Bl;,
        Lcom/facebook/ads/redexgen/X/H5;,
        Lcom/facebook/ads/internal/exoplayer2/thirdparty/upstream/HttpDataSource$Factory;
    }
.end annotation


# static fields
.field public static final A00:Lcom/facebook/ads/redexgen/X/I1;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/facebook/ads/redexgen/X/I1<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public static constructor <clinit>()V
    .locals 1

    .line 2483
    new-instance v0, Lcom/facebook/ads/redexgen/X/Vu;

    invoke-direct {v0}, Lcom/facebook/ads/redexgen/X/Vu;-><init>()V

    sput-object v0, Lcom/facebook/ads/redexgen/X/Vr;->A00:Lcom/facebook/ads/redexgen/X/I1;

    return-void
.end method
