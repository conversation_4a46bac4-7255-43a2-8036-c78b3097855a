.class public interface abstract Lcom/facebook/ads/redexgen/X/WL;
.super Ljava/lang/Object;
.source ""

# interfaces
.implements Lcom/facebook/ads/redexgen/X/BB;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Lcom/facebook/ads/redexgen/X/BB<",
        "Lcom/facebook/ads/redexgen/X/C5;",
        "Lcom/facebook/ads/redexgen/X/Br;",
        "Lcom/facebook/ads/redexgen/X/Fp;",
        ">;"
    }
.end annotation


# virtual methods
.method public abstract AGB(J)V
.end method
