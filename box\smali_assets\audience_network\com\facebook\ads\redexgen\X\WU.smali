.class public final Lcom/facebook/ads/redexgen/X/WU;
.super Ljava/lang/Object;
.source ""

# interfaces
.implements Lcom/facebook/ads/redexgen/X/FB;


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 60131
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final A9C()Z
    .locals 1

    .line 60132
    const/4 v0, 0x1

    return v0
.end method

.method public final AAM()V
    .locals 0
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 60133
    return-void
.end method

.method public final AEK(Lcom/facebook/ads/redexgen/X/9p;Lcom/facebook/ads/redexgen/X/Xr;Z)I
    .locals 1

    .line 60134
    const/4 v0, 0x4

    invoke-virtual {p2, v0}, Lcom/facebook/ads/redexgen/X/B7;->A02(I)V

    .line 60135
    const/4 v0, -0x4

    return v0
.end method

.method public final AGO(J)I
    .locals 1

    .line 60136
    const/4 v0, 0x0

    return v0
.end method
