.class public final Lcom/facebook/ads/redexgen/X/Wk;
.super Ljava/lang/Object;
.source ""

# interfaces
.implements Lcom/facebook/ads/redexgen/X/DM;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/facebook/ads/redexgen/X/DM;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 60797
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final A6m(Ljava/lang/String;Z)Lcom/facebook/ads/redexgen/X/DG;
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lcom/facebook/ads/redexgen/X/DP;
        }
    .end annotation

    .line 60798
    invoke-static {p1, p2}, Lcom/facebook/ads/redexgen/X/DR;->A06(<PERSON><PERSON><PERSON>/lang/String;Z)Lcom/facebook/ads/redexgen/X/DG;

    move-result-object v0

    return-object v0
.end method

.method public final A7c()Lcom/facebook/ads/redexgen/X/DG;
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lcom/facebook/ads/redexgen/X/DP;
        }
    .end annotation

    .line 60799
    invoke-static {}, Lcom/facebook/ads/redexgen/X/DR;->A05()Lcom/facebook/ads/redexgen/X/DG;

    move-result-object v0

    return-object v0
.end method
