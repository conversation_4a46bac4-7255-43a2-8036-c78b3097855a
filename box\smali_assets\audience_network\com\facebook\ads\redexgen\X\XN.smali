.class public final Lcom/facebook/ads/redexgen/X/XN;
.super Ljava/lang/Object;
.source ""

# interfaces
.implements Lcom/facebook/ads/redexgen/X/Bs;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/facebook/ads/redexgen/X/CV;,
        Lcom/facebook/ads/redexgen/X/CU;,
        Lcom/facebook/ads/internal/exoplayer2/thirdparty/extractor/mp4/FragmentedMp4Extractor$Flags;
    }
.end annotation


# static fields
.field public static A0X:[B

.field public static A0Y:[Ljava/lang/String;

.field public static final A0Z:Lcom/facebook/ads/redexgen/X/Bv;

.field public static final A0a:I

.field public static final A0b:Lcom/facebook/ads/internal/exoplayer2/thirdparty/Format;

.field public static final A0c:[B


# instance fields
.field public A00:I

.field public A01:I

.field public A02:I

.field public A03:I

.field public A04:I

.field public A05:I

.field public A06:I

.field public A07:J

.field public A08:J

.field public A09:J

.field public A0A:J

.field public A0B:J

.field public A0C:Lcom/facebook/ads/redexgen/X/Bu;

.field public A0D:Lcom/facebook/ads/redexgen/X/CV;

.field public A0E:Lcom/facebook/ads/redexgen/X/Hz;

.field public A0F:Z

.field public A0G:Z

.field public A0H:[Lcom/facebook/ads/redexgen/X/C4;

.field public A0I:[Lcom/facebook/ads/redexgen/X/C4;

.field public final A0J:I

.field public final A0K:Landroid/util/SparseArray;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Landroid/util/SparseArray<",
            "Lcom/facebook/ads/redexgen/X/CV;",
            ">;"
        }
    .end annotation
.end field

.field public final A0L:Lcom/facebook/ads/internal/exoplayer2/thirdparty/drm/DrmInitData;

.field public final A0M:Lcom/facebook/ads/redexgen/X/C4;

.field public final A0N:Lcom/facebook/ads/redexgen/X/Ce;

.field public final A0O:Lcom/facebook/ads/redexgen/X/Hz;

.field public final A0P:Lcom/facebook/ads/redexgen/X/Hz;

.field public final A0Q:Lcom/facebook/ads/redexgen/X/Hz;

.field public final A0R:Lcom/facebook/ads/redexgen/X/Hz;

.field public final A0S:Lcom/facebook/ads/redexgen/X/IB;

.field public final A0T:Ljava/util/ArrayDeque;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/ArrayDeque<",
            "Lcom/facebook/ads/redexgen/X/XT;",
            ">;"
        }
    .end annotation
.end field

.field public final A0U:Ljava/util/ArrayDeque;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/ArrayDeque<",
            "Lcom/facebook/ads/redexgen/X/CU;",
            ">;"
        }
    .end annotation
.end field

.field public final A0V:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lcom/facebook/ads/internal/exoplayer2/thirdparty/Format;",
            ">;"
        }
    .end annotation
.end field

.field public final A0W:[B


# direct methods
.method public static constructor <clinit>()V
    .locals 4

    .line 2541
    const/16 v0, 0x8

    new-array v2, v0, [Ljava/lang/String;

    const/4 v1, 0x0

    const-string v0, "XdJWu"

    aput-object v0, v2, v1

    const/4 v1, 0x1

    const-string v0, "FUlE3E4RGCFuY0n1jkLa4sDNStY"

    aput-object v0, v2, v1

    const/4 v1, 0x2

    const-string v0, "LTfWg4MeBgD"

    aput-object v0, v2, v1

    const/4 v1, 0x3

    const-string v0, "ea2mTe"

    aput-object v0, v2, v1

    const/4 v1, 0x4

    const-string v0, "5aN4xnjUR54"

    aput-object v0, v2, v1

    const/4 v1, 0x5

    const-string v0, "JRDUjTwIRED0cdRjG5ryaKaac6vLzQ8c"

    aput-object v0, v2, v1

    const/4 v1, 0x6

    const-string v0, "s80pf3iUB"

    aput-object v0, v2, v1

    const/4 v1, 0x7

    const-string v0, "VFz"

    aput-object v0, v2, v1

    sput-object v2, Lcom/facebook/ads/redexgen/X/XN;->A0Y:[Ljava/lang/String;

    invoke-static {}, Lcom/facebook/ads/redexgen/X/XN;->A0D()V

    new-instance v0, Lcom/facebook/ads/redexgen/X/XO;

    invoke-direct {v0}, Lcom/facebook/ads/redexgen/X/XO;-><init>()V

    sput-object v0, Lcom/facebook/ads/redexgen/X/XN;->A0Z:Lcom/facebook/ads/redexgen/X/Bv;

    .line 2542
    const/16 v2, 0x2aa

    const/4 v1, 0x4

    const/16 v0, 0x17

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/XN;->A0A(III)Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/IF;->A08(Ljava/lang/String;)I

    move-result v0

    sput v0, Lcom/facebook/ads/redexgen/X/XN;->A0a:I

    .line 2543
    const/16 v0, 0x10

    new-array v0, v0, [B

    fill-array-data v0, :array_0

    sput-object v0, Lcom/facebook/ads/redexgen/X/XN;->A0c:[B

    .line 2544
    const/4 v3, 0x0

    const/16 v2, 0x298

    const/16 v1, 0x12

    const/16 v0, 0x19

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/XN;->A0A(III)Ljava/lang/String;

    move-result-object v2

    const-wide v0, 0x7fffffffffffffffL

    invoke-static {v3, v2, v0, v1}, Lcom/facebook/ads/internal/exoplayer2/thirdparty/Format;->A02(Ljava/lang/String;Ljava/lang/String;J)Lcom/facebook/ads/internal/exoplayer2/thirdparty/Format;

    move-result-object v0

    sput-object v0, Lcom/facebook/ads/redexgen/X/XN;->A0b:Lcom/facebook/ads/internal/exoplayer2/thirdparty/Format;

    .line 2545
    return-void

    nop

    :array_0
    .array-data 1
        -0x5et
        0x39t
        0x4ft
        0x52t
        0x5at
        -0x65t
        0x4ft
        0x14t
        -0x5et
        0x44t
        0x6ct
        0x42t
        0x7ct
        0x64t
        -0x73t
        -0xct
    .end array-data
.end method

.method public constructor <init>()V
    .locals 1

    .line 63218
    const/4 v0, 0x0

    invoke-direct {p0, v0}, Lcom/facebook/ads/redexgen/X/XN;-><init>(I)V

    .line 63219
    return-void
.end method

.method public constructor <init>(I)V
    .locals 1

    .line 63220
    const/4 v0, 0x0

    invoke-direct {p0, p1, v0}, Lcom/facebook/ads/redexgen/X/XN;-><init>(ILcom/facebook/ads/redexgen/X/IB;)V

    .line 63221
    return-void
.end method

.method public constructor <init>(ILcom/facebook/ads/redexgen/X/IB;)V
    .locals 1

    .line 63222
    const/4 v0, 0x0

    invoke-direct {p0, p1, p2, v0, v0}, Lcom/facebook/ads/redexgen/X/XN;-><init>(ILcom/facebook/ads/redexgen/X/IB;Lcom/facebook/ads/redexgen/X/Ce;Lcom/facebook/ads/internal/exoplayer2/thirdparty/drm/DrmInitData;)V

    .line 63223
    return-void
.end method

.method public constructor <init>(ILcom/facebook/ads/redexgen/X/IB;Lcom/facebook/ads/redexgen/X/Ce;Lcom/facebook/ads/internal/exoplayer2/thirdparty/drm/DrmInitData;)V
    .locals 6

    .line 63224
    invoke-static {}, Ljava/util/Collections;->emptyList()Ljava/util/List;

    move-result-object v5

    .line 63225
    move-object v0, p0

    move v1, p1

    move-object v2, p2

    move-object v3, p3

    move-object v4, p4

    invoke-direct/range {v0 .. v5}, Lcom/facebook/ads/redexgen/X/XN;-><init>(ILcom/facebook/ads/redexgen/X/IB;Lcom/facebook/ads/redexgen/X/Ce;Lcom/facebook/ads/internal/exoplayer2/thirdparty/drm/DrmInitData;Ljava/util/List;)V

    .line 63226
    return-void
.end method

.method public constructor <init>(ILcom/facebook/ads/redexgen/X/IB;Lcom/facebook/ads/redexgen/X/Ce;Lcom/facebook/ads/internal/exoplayer2/thirdparty/drm/DrmInitData;Ljava/util/List;)V
    .locals 7
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I",
            "Lcom/facebook/ads/redexgen/X/IB;",
            "Lcom/facebook/ads/redexgen/X/Ce;",
            "Lcom/facebook/ads/internal/exoplayer2/thirdparty/drm/DrmInitData;",
            "Ljava/util/List<",
            "Lcom/facebook/ads/internal/exoplayer2/thirdparty/Format;",
            ">;)V"
        }
    .end annotation

    .line 63227
    .local p5, "closedCaptionFormats":Ljava/util/List;, "Ljava/util/List<Lcom/facebook/ads/internal/exoplayer2/thirdparty/Format;>;"
    const/4 v6, 0x0

    move-object v0, p0

    move v1, p1

    move-object v2, p2

    move-object v3, p3

    move-object v4, p4

    move-object v5, p5

    invoke-direct/range {v0 .. v6}, Lcom/facebook/ads/redexgen/X/XN;-><init>(ILcom/facebook/ads/redexgen/X/IB;Lcom/facebook/ads/redexgen/X/Ce;Lcom/facebook/ads/internal/exoplayer2/thirdparty/drm/DrmInitData;Ljava/util/List;Lcom/facebook/ads/redexgen/X/C4;)V

    .line 63228
    return-void
.end method

.method public constructor <init>(ILcom/facebook/ads/redexgen/X/IB;Lcom/facebook/ads/redexgen/X/Ce;Lcom/facebook/ads/internal/exoplayer2/thirdparty/drm/DrmInitData;Ljava/util/List;Lcom/facebook/ads/redexgen/X/C4;)V
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I",
            "Lcom/facebook/ads/redexgen/X/IB;",
            "Lcom/facebook/ads/redexgen/X/Ce;",
            "Lcom/facebook/ads/internal/exoplayer2/thirdparty/drm/DrmInitData;",
            "Ljava/util/List<",
            "Lcom/facebook/ads/internal/exoplayer2/thirdparty/Format;",
            ">;",
            "Lcom/facebook/ads/redexgen/X/C4;",
            ")V"
        }
    .end annotation

    .line 63229
    .local p5, "closedCaptionFormats":Ljava/util/List;, "Ljava/util/List<Lcom/facebook/ads/internal/exoplayer2/thirdparty/Format;>;"
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 63230
    if-eqz p3, :cond_0

    const/16 v0, 0x8

    :goto_0
    or-int/2addr v0, p1

    iput v0, p0, Lcom/facebook/ads/redexgen/X/XN;->A0J:I

    .line 63231
    iput-object p2, p0, Lcom/facebook/ads/redexgen/X/XN;->A0S:Lcom/facebook/ads/redexgen/X/IB;

    .line 63232
    iput-object p3, p0, Lcom/facebook/ads/redexgen/X/XN;->A0N:Lcom/facebook/ads/redexgen/X/Ce;

    .line 63233
    iput-object p4, p0, Lcom/facebook/ads/redexgen/X/XN;->A0L:Lcom/facebook/ads/internal/exoplayer2/thirdparty/drm/DrmInitData;

    .line 63234
    invoke-static {p5}, Ljava/util/Collections;->unmodifiableList(Ljava/util/List;)Ljava/util/List;

    move-result-object v0

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/XN;->A0V:Ljava/util/List;

    .line 63235
    iput-object p6, p0, Lcom/facebook/ads/redexgen/X/XN;->A0M:Lcom/facebook/ads/redexgen/X/C4;

    .line 63236
    const/16 v2, 0x10

    new-instance v0, Lcom/facebook/ads/redexgen/X/Hz;

    invoke-direct {v0, v2}, Lcom/facebook/ads/redexgen/X/Hz;-><init>(I)V

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/XN;->A0O:Lcom/facebook/ads/redexgen/X/Hz;

    .line 63237
    sget-object v1, Lcom/facebook/ads/redexgen/X/Hv;->A03:[B

    new-instance v0, Lcom/facebook/ads/redexgen/X/Hz;

    invoke-direct {v0, v1}, Lcom/facebook/ads/redexgen/X/Hz;-><init>([B)V

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/XN;->A0R:Lcom/facebook/ads/redexgen/X/Hz;

    .line 63238
    const/4 v1, 0x5

    new-instance v0, Lcom/facebook/ads/redexgen/X/Hz;

    invoke-direct {v0, v1}, Lcom/facebook/ads/redexgen/X/Hz;-><init>(I)V

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/XN;->A0Q:Lcom/facebook/ads/redexgen/X/Hz;

    .line 63239
    new-instance v0, Lcom/facebook/ads/redexgen/X/Hz;

    invoke-direct {v0}, Lcom/facebook/ads/redexgen/X/Hz;-><init>()V

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/XN;->A0P:Lcom/facebook/ads/redexgen/X/Hz;

    .line 63240
    new-array v0, v2, [B

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/XN;->A0W:[B

    .line 63241
    new-instance v0, Ljava/util/ArrayDeque;

    invoke-direct {v0}, Ljava/util/ArrayDeque;-><init>()V

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/XN;->A0T:Ljava/util/ArrayDeque;

    .line 63242
    new-instance v0, Ljava/util/ArrayDeque;

    invoke-direct {v0}, Ljava/util/ArrayDeque;-><init>()V

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/XN;->A0U:Ljava/util/ArrayDeque;

    .line 63243
    new-instance v0, Landroid/util/SparseArray;

    invoke-direct {v0}, Landroid/util/SparseArray;-><init>()V

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/XN;->A0K:Landroid/util/SparseArray;

    .line 63244
    const-wide v0, -0x7fffffffffffffffL    # -4.9E-324

    iput-wide v0, p0, Lcom/facebook/ads/redexgen/X/XN;->A08:J

    .line 63245
    iput-wide v0, p0, Lcom/facebook/ads/redexgen/X/XN;->A0A:J

    .line 63246
    iput-wide v0, p0, Lcom/facebook/ads/redexgen/X/XN;->A0B:J

    .line 63247
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/XN;->A0B()V

    .line 63248
    return-void

    .line 63249
    :cond_0
    const/4 v0, 0x0

    goto :goto_0
.end method

.method public static A00(Lcom/facebook/ads/redexgen/X/CV;IJILcom/facebook/ads/redexgen/X/Hz;I)I
    .locals 28

    .line 63250
    move/from16 v5, p6

    move-wide/from16 v0, p2

    const/16 v3, 0x8

    move-object/from16 p6, p5

    move-object/from16 v2, p6

    invoke-virtual {v2, v3}, Lcom/facebook/ads/redexgen/X/Hz;->A0Y(I)V

    .line 63251
    invoke-virtual/range {p6 .. p6}, Lcom/facebook/ads/redexgen/X/Hz;->A08()I

    move-result v2

    .line 63252
    .local v1, "fullAtom":I
    invoke-static {v2}, Lcom/facebook/ads/redexgen/X/CJ;->A00(I)I

    move-result v8

    .line 63253
    .local v3, "atomFlags":I
    move-object/from16 v2, p0

    iget-object v6, v2, Lcom/facebook/ads/redexgen/X/CV;->A05:Lcom/facebook/ads/redexgen/X/Ce;

    .line 63254
    .local v4, "track":Lcom/facebook/ads/redexgen/X/Ce;
    iget-object v4, v2, Lcom/facebook/ads/redexgen/X/CV;->A07:Lcom/facebook/ads/redexgen/X/Cg;

    .line 63255
    .local v5, "fragment":Lcom/facebook/ads/redexgen/X/Cg;
    iget-object v7, v4, Lcom/facebook/ads/redexgen/X/Cg;->A07:Lcom/facebook/ads/redexgen/X/CP;

    .line 63256
    .local v6, "defaultSampleValues":Lcom/facebook/ads/redexgen/X/CP;
    iget-object v3, v4, Lcom/facebook/ads/redexgen/X/Cg;->A0E:[I

    invoke-virtual/range {p6 .. p6}, Lcom/facebook/ads/redexgen/X/Hz;->A0H()I

    move-result v2

    aput v2, v3, p1

    .line 63257
    iget-object v9, v4, Lcom/facebook/ads/redexgen/X/Cg;->A0G:[J

    iget-wide v2, v4, Lcom/facebook/ads/redexgen/X/Cg;->A05:J

    aput-wide v2, v9, p1

    .line 63258
    and-int/lit8 v2, v8, 0x1

    if-eqz v2, :cond_0

    .line 63259
    iget-object v11, v4, Lcom/facebook/ads/redexgen/X/Cg;->A0G:[J

    aget-wide v9, v11, p1

    invoke-virtual/range {p6 .. p6}, Lcom/facebook/ads/redexgen/X/Hz;->A08()I

    move-result v2

    int-to-long v2, v2

    add-long/2addr v9, v2

    aput-wide v9, v11, p1

    .line 63260
    :cond_0
    and-int/lit8 v2, v8, 0x4

    const/4 v3, 0x1

    if-eqz v2, :cond_10

    const/16 v27, 0x1

    .line 63261
    .local v7, "firstSampleFlagsPresent":Z
    :goto_0
    iget v2, v7, Lcom/facebook/ads/redexgen/X/CP;->A01:I

    move/from16 v26, v2

    .line 63262
    .local v10, "firstSampleFlags":I
    if-eqz v27, :cond_1

    .line 63263
    invoke-virtual/range {p6 .. p6}, Lcom/facebook/ads/redexgen/X/Hz;->A0H()I

    move-result v26

    .line 63264
    :cond_1
    and-int/lit16 v2, v8, 0x100

    if-eqz v2, :cond_f

    const/16 v25, 0x1

    .line 63265
    .local v11, "sampleDurationsPresent":Z
    :goto_1
    and-int/lit16 v2, v8, 0x200

    if-eqz v2, :cond_e

    const/16 v24, 0x1

    .line 63266
    .local v12, "sampleSizesPresent":Z
    :goto_2
    and-int/lit16 v2, v8, 0x400

    if-eqz v2, :cond_d

    const/16 v23, 0x1

    .line 63267
    .local v13, "sampleFlagsPresent":Z
    :goto_3
    and-int/lit16 v2, v8, 0x800

    if-eqz v2, :cond_c

    const/16 v22, 0x1

    .line 63268
    .local v14, "sampleCompositionTimeOffsetsPresent":Z
    :goto_4
    const-wide/16 v20, 0x0

    .line 63269
    .local v15, "edtsOffset":J
    iget-object v2, v6, Lcom/facebook/ads/redexgen/X/Ce;->A08:[J

    if-eqz v2, :cond_2

    iget-object v2, v6, Lcom/facebook/ads/redexgen/X/Ce;->A08:[J

    array-length v2, v2

    if-ne v2, v3, :cond_2

    iget-object v2, v6, Lcom/facebook/ads/redexgen/X/Ce;->A08:[J

    const/4 v3, 0x0

    aget-wide v10, v2, v3

    const-wide/16 v8, 0x0

    cmp-long v2, v10, v8

    if-nez v2, :cond_2

    .line 63270
    iget-object v2, v6, Lcom/facebook/ads/redexgen/X/Ce;->A09:[J

    aget-wide v8, v2, v3

    const-wide/16 v10, 0x3e8

    .end local v10    # "firstSampleFlags":I
    .local v24, "firstSampleFlags":I
    iget-wide v12, v6, Lcom/facebook/ads/redexgen/X/Ce;->A06:J

    invoke-static/range {v8 .. v13}, Lcom/facebook/ads/redexgen/X/IF;->A0F(JJJ)J

    move-result-wide v20

    .line 63271
    .end local v10
    .restart local v24    # "firstSampleFlags":I
    :cond_2
    iget-object v2, v4, Lcom/facebook/ads/redexgen/X/Cg;->A0D:[I

    move-object/from16 v19, v2

    .line 63272
    .local v9, "sampleSizeTable":[I
    iget-object v11, v4, Lcom/facebook/ads/redexgen/X/Cg;->A0C:[I

    .line 63273
    .local v10, "sampleCompositionTimeOffsetTable":[I
    iget-object v2, v4, Lcom/facebook/ads/redexgen/X/Cg;->A0F:[J

    move-object/from16 v18, v2

    .line 63274
    .local v8, "sampleDecodingTimeTable":[J
    iget-object v14, v4, Lcom/facebook/ads/redexgen/X/Cg;->A0I:[Z

    .line 63275
    .local v0, "sampleIsSyncFrameTable":[Z
    .end local v1    # "fullAtom":I
    .local v19, "fullAtom":I
    iget v3, v6, Lcom/facebook/ads/redexgen/X/Ce;->A03:I

    const/4 v2, 0x2

    if-ne v3, v2, :cond_b

    and-int/lit8 v2, p4, 0x1

    if-eqz v2, :cond_b

    const/16 v17, 0x1

    .line 63276
    .local v1, "workaroundEveryVideoFrameIsSyncFrame":Z
    :goto_5
    iget-object v2, v4, Lcom/facebook/ads/redexgen/X/Cg;->A0E:[I

    aget v2, v2, p1

    add-int v10, v5, v2

    .line 63277
    .local v2, "trackRunEnd":I
    .end local v0    # "sampleIsSyncFrameTable":[Z
    .end local v1    # "workaroundEveryVideoFrameIsSyncFrame":Z
    .local v20, "sampleIsSyncFrameTable":[Z
    .local v21, "workaroundEveryVideoFrameIsSyncFrame":Z
    iget-wide v2, v6, Lcom/facebook/ads/redexgen/X/Ce;->A06:J

    .line 63278
    .local v0, "timescale":J
    if-lez p1, :cond_3

    .end local v3    # "atomFlags":I
    .end local v4    # "track":Lcom/facebook/ads/redexgen/X/Ce;
    .local v22, "atomFlags":I
    .local v23, "track":Lcom/facebook/ads/redexgen/X/Ce;
    iget-wide v0, v4, Lcom/facebook/ads/redexgen/X/Cg;->A06:J

    .line 63279
    .local v5, "i":I
    .local p3, "fragment":Lcom/facebook/ads/redexgen/X/Cg;
    :cond_3
    :goto_6
    if-ge v5, v10, :cond_12

    .line 63280
    if-eqz v25, :cond_a

    invoke-virtual/range {p6 .. p6}, Lcom/facebook/ads/redexgen/X/Hz;->A0H()I

    move-result v13

    .line 63281
    .local v11, "sampleDuration":I
    :goto_7
    if-eqz v24, :cond_9

    invoke-virtual/range {p6 .. p6}, Lcom/facebook/ads/redexgen/X/Hz;->A0H()I

    move-result v12

    .line 63282
    .local v12, "sampleSize":I
    :goto_8
    if-nez v5, :cond_7

    if-eqz v27, :cond_7

    .line 63283
    move/from16 v6, v26

    .line 63284
    .local v7, "sampleFlags":I
    :goto_9
    if-eqz v22, :cond_6

    .line 63285
    .end local v6    # "defaultSampleValues":Lcom/facebook/ads/redexgen/X/CP;
    .local p7, "defaultSampleValues":Lcom/facebook/ads/redexgen/X/CP;
    invoke-virtual/range {p6 .. p6}, Lcom/facebook/ads/redexgen/X/Hz;->A08()I

    move-result v8

    .line 63286
    .local v6, "sampleOffset":I
    .end local v13    # "sampleFlagsPresent":Z
    .end local v14    # "sampleCompositionTimeOffsetsPresent":Z
    .local p8, "sampleFlagsPresent":Z
    .local p9, "sampleCompositionTimeOffsetsPresent":Z
    int-to-long v8, v8

    const-wide/16 v15, 0x3e8

    mul-long/2addr v8, v15

    div-long/2addr v8, v2

    long-to-int v15, v8

    aput v15, v11, v5

    .line 63287
    .end local v6    # "sampleOffset":I
    :goto_a
    const-wide/16 p2, 0x3e8

    .line 63288
    move-wide/from16 p0, v0

    move-wide/from16 p4, v2

    invoke-static/range {p0 .. p5}, Lcom/facebook/ads/redexgen/X/IF;->A0F(JJJ)J

    move-result-wide v8

    sub-long v8, v8, v20

    aput-wide v8, v18, v5

    .line 63289
    aput v12, v19, v5

    .line 63290
    shr-int/lit8 v8, v6, 0x10

    const/4 v6, 0x1

    and-int/2addr v8, v6

    if-nez v8, :cond_5

    if-eqz v17, :cond_4

    if-nez v5, :cond_5

    :cond_4
    const/4 v6, 0x1

    :goto_b
    aput-boolean v6, v14, v5

    .line 63291
    int-to-long v8, v13

    add-long/2addr v0, v8

    .line 63292
    .end local v7    # "sampleFlags":I
    .end local v11    # "sampleDuration":I
    .end local v12    # "sampleSize":I
    add-int/lit8 v5, v5, 0x1

    goto :goto_6

    .line 63293
    :cond_5
    const/4 v6, 0x0

    goto :goto_b

    .line 63294
    .end local p7
    .end local p8
    .end local p9
    .local v6, "defaultSampleValues":Lcom/facebook/ads/redexgen/X/CP;
    .restart local v13    # "sampleFlagsPresent":Z
    .restart local v14    # "sampleCompositionTimeOffsetsPresent":Z
    .end local v6    # "defaultSampleValues":Lcom/facebook/ads/redexgen/X/CP;
    .end local v13    # "sampleFlagsPresent":Z
    .end local v14    # "sampleCompositionTimeOffsetsPresent":Z
    .restart local p7
    .restart local p8
    .restart local p9
    :cond_6
    const/4 v8, 0x0

    aput v8, v11, v5

    goto :goto_a

    .line 63295
    :cond_7
    if-eqz v23, :cond_8

    invoke-virtual/range {p6 .. p6}, Lcom/facebook/ads/redexgen/X/Hz;->A08()I

    move-result v6

    goto :goto_9

    .end local v7
    .local p6, "firstSampleFlagsPresent":Z
    :cond_8
    iget v6, v7, Lcom/facebook/ads/redexgen/X/CP;->A01:I

    goto :goto_9

    .line 63296
    .end local v12
    .local p5, "sampleSizesPresent":Z
    :cond_9
    iget v12, v7, Lcom/facebook/ads/redexgen/X/CP;->A03:I

    sget-object v8, Lcom/facebook/ads/redexgen/X/XN;->A0Y:[Ljava/lang/String;

    const/4 v6, 0x5

    aget-object v8, v8, v6

    const/4 v6, 0x2

    invoke-virtual {v8, v6}, Ljava/lang/String;->charAt(I)C

    move-result v8

    const/16 v6, 0x43

    if-eq v8, v6, :cond_11

    sget-object v9, Lcom/facebook/ads/redexgen/X/XN;->A0Y:[Ljava/lang/String;

    const-string v8, "dkwnFPd0"

    const/4 v6, 0x6

    aput-object v8, v9, v6

    goto :goto_8

    .line 63297
    .end local v11
    .local p4, "sampleDurationsPresent":Z
    :cond_a
    iget v13, v7, Lcom/facebook/ads/redexgen/X/CP;->A00:I

    goto :goto_7

    .line 63298
    :cond_b
    const/16 v17, 0x0

    goto :goto_5

    .line 63299
    :cond_c
    const/16 v22, 0x0

    goto/16 :goto_4

    .line 63300
    :cond_d
    const/16 v23, 0x0

    goto/16 :goto_3

    .line 63301
    :cond_e
    const/16 v24, 0x0

    goto/16 :goto_2

    .line 63302
    :cond_f
    const/16 v25, 0x0

    goto/16 :goto_1

    .line 63303
    :cond_10
    const/16 v27, 0x0

    goto/16 :goto_0

    :cond_11
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    .line 63304
    .end local v5    # "i":I
    .end local p4    # "sampleDurationsPresent":Z
    .end local p5    # "sampleSizesPresent":Z
    .end local p6    # "firstSampleFlagsPresent":Z
    .end local p7
    .end local p8
    .end local p9
    .restart local v6    # "defaultSampleValues":Lcom/facebook/ads/redexgen/X/CP;
    .local v7, "firstSampleFlagsPresent":Z
    .local v11, "sampleDurationsPresent":Z
    .local v12, "sampleSizesPresent":Z
    .restart local v13    # "sampleFlagsPresent":Z
    .restart local v14    # "sampleCompositionTimeOffsetsPresent":Z
    .end local p3    # "fragment":Lcom/facebook/ads/redexgen/X/Cg;
    .local v5, "fragment":Lcom/facebook/ads/redexgen/X/Cg;
    :cond_12
    iput-wide v0, v4, Lcom/facebook/ads/redexgen/X/Cg;->A06:J

    .line 63305
    return v10
.end method

.method public static A01(Lcom/facebook/ads/redexgen/X/Hz;)J
    .locals 1

    .line 63306
    const/16 v0, 0x8

    invoke-virtual {p0, v0}, Lcom/facebook/ads/redexgen/X/Hz;->A0Y(I)V

    .line 63307
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Hz;->A08()I

    move-result v0

    .line 63308
    .local v0, "fullAtom":I
    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/CJ;->A01(I)I

    move-result v0

    .line 63309
    .local p0, "version":I
    if-nez v0, :cond_0

    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Hz;->A0M()J

    move-result-wide v0

    :goto_0
    return-wide v0

    :cond_0
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Hz;->A0N()J

    move-result-wide v0

    goto :goto_0
.end method

.method public static A02(Lcom/facebook/ads/redexgen/X/Hz;)J
    .locals 2

    .line 63310
    const/16 v0, 0x8

    invoke-virtual {p0, v0}, Lcom/facebook/ads/redexgen/X/Hz;->A0Y(I)V

    .line 63311
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Hz;->A08()I

    move-result v0

    .line 63312
    .local v0, "fullAtom":I
    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/CJ;->A01(I)I

    move-result v1

    .line 63313
    .local v1, "version":I
    const/4 v0, 0x1

    if-ne v1, v0, :cond_0

    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Hz;->A0N()J

    move-result-wide v0

    :goto_0
    return-wide v0

    :cond_0
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Hz;->A0M()J

    move-result-wide v0

    goto :goto_0
.end method

.method public static A03(Lcom/facebook/ads/redexgen/X/Hz;)Landroid/util/Pair;
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/facebook/ads/redexgen/X/Hz;",
            ")",
            "Landroid/util/Pair<",
            "Ljava/lang/Integer;",
            "Lcom/facebook/ads/redexgen/X/CP;",
            ">;"
        }
    .end annotation

    .line 63314
    const/16 v0, 0xc

    invoke-virtual {p0, v0}, Lcom/facebook/ads/redexgen/X/Hz;->A0Y(I)V

    .line 63315
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Hz;->A08()I

    move-result v1

    .line 63316
    .local v0, "trackId":I
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Hz;->A0H()I

    move-result v0

    add-int/lit8 v5, v0, -0x1

    .line 63317
    .local v1, "defaultSampleDescriptionIndex":I
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Hz;->A0H()I

    move-result v4

    .line 63318
    .local v2, "defaultSampleDuration":I
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Hz;->A0H()I

    move-result v3

    .line 63319
    .local v3, "defaultSampleSize":I
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Hz;->A08()I

    move-result v2

    .line 63320
    .local v4, "defaultSampleFlags":I
    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    new-instance v0, Lcom/facebook/ads/redexgen/X/CP;

    invoke-direct {v0, v5, v4, v3, v2}, Lcom/facebook/ads/redexgen/X/CP;-><init>(IIII)V

    .line 63321
    invoke-static {v1, v0}, Landroid/util/Pair;->create(Ljava/lang/Object;Ljava/lang/Object;)Landroid/util/Pair;

    move-result-object v0

    return-object v0
.end method

.method public static A04(Lcom/facebook/ads/redexgen/X/Hz;J)Landroid/util/Pair;
    .locals 20
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/facebook/ads/redexgen/X/Hz;",
            "J)",
            "Landroid/util/Pair<",
            "Ljava/lang/Long;",
            "Lcom/facebook/ads/redexgen/X/Xo;",
            ">;"
        }
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lcom/facebook/ads/redexgen/X/9v;
        }
    .end annotation

    .line 63322
    const/16 v0, 0x8

    move-object/from16 v5, p0

    invoke-virtual {v5, v0}, Lcom/facebook/ads/redexgen/X/Hz;->A0Y(I)V

    .line 63323
    invoke-virtual {v5}, Lcom/facebook/ads/redexgen/X/Hz;->A08()I

    move-result v0

    .line 63324
    .local v1, "fullAtom":I
    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/CJ;->A01(I)I

    move-result v1

    .line 63325
    .local v2, "version":I
    const/4 v0, 0x4

    invoke-virtual {v5, v0}, Lcom/facebook/ads/redexgen/X/Hz;->A0Z(I)V

    .line 63326
    invoke-virtual {v5}, Lcom/facebook/ads/redexgen/X/Hz;->A0M()J

    move-result-wide v19

    .line 63327
    .local v10, "timescale":J
    .local v4, "offset":J
    if-nez v1, :cond_0

    .line 63328
    invoke-virtual {v5}, Lcom/facebook/ads/redexgen/X/Hz;->A0M()J

    move-result-wide v15

    .line 63329
    .local v6, "earliestPresentationTime":J
    invoke-virtual {v5}, Lcom/facebook/ads/redexgen/X/Hz;->A0M()J

    move-result-wide v0

    add-long p1, p1, v0

    .line 63330
    .end local v4    # "offset":J
    .end local v6    # "earliestPresentationTime":J
    .local v12, "offset":J
    .local v14, "earliestPresentationTime":J
    :goto_0
    const-wide/32 v17, 0xf4240

    .line 63331
    invoke-static/range {v15 .. v20}, Lcom/facebook/ads/redexgen/X/IF;->A0F(JJJ)J

    move-result-wide v13

    .line 63332
    .local v16, "earliestPresentationTimeUs":J
    const/4 v0, 0x2

    invoke-virtual {v5, v0}, Lcom/facebook/ads/redexgen/X/Hz;->A0Z(I)V

    .line 63333
    invoke-virtual {v5}, Lcom/facebook/ads/redexgen/X/Hz;->A0I()I

    move-result v7

    .line 63334
    .local v8, "referenceCount":I
    new-array v6, v7, [I

    .line 63335
    .local v9, "sizes":[I
    new-array v8, v7, [J

    .line 63336
    .local v6, "offsets":[J
    new-array v4, v7, [J

    .line 63337
    .local v7, "durationsUs":[J
    new-array v3, v7, [J

    .line 63338
    .local v4, "timesUs":[J
    .local v18, "time":J
    .local p0, "timeUs":J
    const/4 v2, 0x0

    move-wide v11, v13

    .local v12, "i":I
    .local v18, "offset":J
    .local p0, "time":J
    .local p2, "timeUs":J
    :goto_1
    if-ge v2, v7, :cond_2

    .line 63339
    invoke-virtual {v5}, Lcom/facebook/ads/redexgen/X/Hz;->A08()I

    move-result v1

    .line 63340
    .local v13, "firstInt":I
    const/high16 v0, -0x80000000

    and-int/2addr v0, v1

    .line 63341
    .local p4, "type":I
    if-nez v0, :cond_1

    .line 63342
    invoke-virtual {v5}, Lcom/facebook/ads/redexgen/X/Hz;->A0M()J

    move-result-wide v9

    .line 63343
    .local p5, "referenceDuration":J
    const v0, 0x7fffffff

    and-int/2addr v0, v1

    aput v0, v6, v2

    .line 63344
    aput-wide p1, v8, v2

    .line 63345
    aput-wide v11, v3, v2

    .line 63346
    add-long/2addr v15, v9

    .line 63347
    const-wide/32 v17, 0xf4240

    .end local v4    # "timesUs":[J
    .local v3, "timesUs":[J
    .end local v6    # "offsets":[J
    .end local v7    # "durationsUs":[J
    .local v1, "offsets":[J
    .local v2, "durationsUs":[J
    .local p9, "fullAtom":I
    .local p10, "version":I
    .end local v8    # "referenceCount":I
    .end local v9    # "sizes":[I
    .local v13, "sizes":[I
    .local p7, "referenceCount":I
    .local p8, "firstInt":I
    invoke-static/range {v15 .. v20}, Lcom/facebook/ads/redexgen/X/IF;->A0F(JJJ)J

    move-result-wide v11

    .line 63348
    aget-wide v9, v3, v2

    sub-long v0, v11, v9

    aput-wide v0, v4, v2

    .line 63349
    const/4 v0, 0x4

    invoke-virtual {v5, v0}, Lcom/facebook/ads/redexgen/X/Hz;->A0Z(I)V

    .line 63350
    aget v0, v6, v2

    int-to-long v0, v0

    add-long p1, p1, v0

    .line 63351
    .end local p4
    .end local p5
    .end local p8
    add-int/lit8 v2, v2, 0x1

    goto :goto_1

    .line 63352
    .end local v6
    :cond_0
    invoke-virtual {v5}, Lcom/facebook/ads/redexgen/X/Hz;->A0N()J

    move-result-wide v15

    .line 63353
    .restart local v6    # "offsets":[J
    invoke-virtual {v5}, Lcom/facebook/ads/redexgen/X/Hz;->A0N()J

    move-result-wide v0

    add-long p1, p1, v0

    goto :goto_0

    .line 63354
    .end local v3    # "timesUs":[J
    .end local p7
    .end local p9
    .end local p10
    .local v1, "fullAtom":I
    .local v2, "version":I
    .restart local v4    # "timesUs":[J
    .restart local v6    # "offsets":[J
    .restart local v7    # "durationsUs":[J
    .restart local v8    # "referenceCount":I
    .restart local v9    # "sizes":[I
    .local v13, "firstInt":I
    .restart local p4
    .end local v4    # "timesUs":[J
    .restart local v3    # "timesUs":[J
    :cond_1
    const/16 v2, 0x245

    const/16 v1, 0x1c

    const/16 v0, 0x7e

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/XN;->A0A(III)Ljava/lang/String;

    move-result-object v1

    new-instance v0, Lcom/facebook/ads/redexgen/X/9v;

    invoke-direct {v0, v1}, Lcom/facebook/ads/redexgen/X/9v;-><init>(Ljava/lang/String;)V

    throw v0

    .line 63355
    .end local v3    # "timesUs":[J
    .end local v13    # "firstInt":I
    .end local p4
    .restart local v4    # "timesUs":[J
    .end local v4    # "timesUs":[J
    .end local v6    # "offsets":[J
    .end local v7    # "durationsUs":[J
    .end local v9    # "sizes":[I
    .end local v12    # "i":I
    .local v1, "offsets":[J
    .local v2, "durationsUs":[J
    .restart local v3    # "timesUs":[J
    .local v13, "sizes":[I
    .restart local p9
    .restart local p10
    :cond_2
    invoke-static {v13, v14}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    move-result-object v1

    new-instance v0, Lcom/facebook/ads/redexgen/X/Xo;

    invoke-direct {v0, v6, v8, v4, v3}, Lcom/facebook/ads/redexgen/X/Xo;-><init>([I[J[J[J)V

    .line 63356
    invoke-static {v1, v0}, Landroid/util/Pair;->create(Ljava/lang/Object;Ljava/lang/Object;)Landroid/util/Pair;

    move-result-object v0

    return-object v0
.end method

.method public static A05(Ljava/util/List;)Lcom/facebook/ads/internal/exoplayer2/thirdparty/drm/DrmInitData;
    .locals 8
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lcom/facebook/ads/redexgen/X/XS;",
            ">;)",
            "Lcom/facebook/ads/internal/exoplayer2/thirdparty/drm/DrmInitData;"
        }
    .end annotation

    .line 63357
    .local p0, "leafChildren":Ljava/util/List;, "Ljava/util/List<Lcom/facebook/ads/internal/exoplayer2/thirdparty/extractor/mp4/Atom$LeafAtom;>;"
    const/4 v3, 0x0

    .line 63358
    .local v0, "schemeDatas":Ljava/util/ArrayList;, "Ljava/util/ArrayList<Lcom/facebook/ads/internal/exoplayer2/thirdparty/drm/DrmInitData$SchemeData;>;"
    invoke-interface {p0}, Ljava/util/List;->size()I

    move-result v5

    .line 63359
    .local v1, "leafChildrenSize":I
    const/4 v4, 0x0

    .local v2, "i":I
    :goto_0
    if-ge v4, v5, :cond_4

    .line 63360
    invoke-interface {p0, v4}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/facebook/ads/redexgen/X/XS;

    .line 63361
    .local v3, "child":Lcom/facebook/ads/redexgen/X/XS;
    iget v1, v2, Lcom/facebook/ads/redexgen/X/CJ;->A00:I

    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A0s:I

    if-ne v1, v0, :cond_2

    .line 63362
    if-nez v3, :cond_0

    .line 63363
    new-instance v3, Ljava/util/ArrayList;

    invoke-direct {v3}, Ljava/util/ArrayList;-><init>()V

    .line 63364
    :cond_0
    iget-object v0, v2, Lcom/facebook/ads/redexgen/X/XS;->A00:Lcom/facebook/ads/redexgen/X/Hz;

    iget-object v7, v0, Lcom/facebook/ads/redexgen/X/Hz;->A00:[B

    .line 63365
    .local v4, "psshData":[B
    invoke-static {v7}, Lcom/facebook/ads/redexgen/X/Cb;->A02([B)Ljava/util/UUID;

    move-result-object v6

    sget-object v1, Lcom/facebook/ads/redexgen/X/XN;->A0Y:[Ljava/lang/String;

    const/4 v0, 0x3

    aget-object v0, v1, v0

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v1

    const/4 v0, 0x6

    if-eq v1, v0, :cond_1

    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    .line 63366
    .local v5, "uuid":Ljava/util/UUID;
    :cond_1
    sget-object v2, Lcom/facebook/ads/redexgen/X/XN;->A0Y:[Ljava/lang/String;

    const-string v1, "tzO"

    const/4 v0, 0x7

    aput-object v1, v2, v0

    if-nez v6, :cond_3

    .line 63367
    const/16 v2, 0x80

    const/16 v1, 0x16

    const/16 v0, 0x18

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/XN;->A0A(III)Ljava/lang/String;

    move-result-object v6

    const/16 v2, 0x1b5

    const/16 v1, 0x2a

    const/16 v0, 0x3a

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/XN;->A0A(III)Ljava/lang/String;

    move-result-object v0

    invoke-static {v6, v0}, Landroid/util/Log;->w(Ljava/lang/String;Ljava/lang/String;)I

    .line 63368
    .end local v3    # "child":Lcom/facebook/ads/redexgen/X/XS;
    .end local v4    # "psshData":[B
    .end local v5    # "uuid":Ljava/util/UUID;
    :cond_2
    :goto_1
    add-int/lit8 v4, v4, 0x1

    goto :goto_0

    .line 63369
    :cond_3
    const/16 v2, 0x2b8

    const/16 v1, 0x9

    const/16 v0, 0x17

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/XN;->A0A(III)Ljava/lang/String;

    move-result-object v1

    new-instance v0, Lcom/facebook/ads/internal/exoplayer2/thirdparty/drm/DrmInitData$SchemeData;

    invoke-direct {v0, v6, v1, v7}, Lcom/facebook/ads/internal/exoplayer2/thirdparty/drm/DrmInitData$SchemeData;-><init>(Ljava/util/UUID;Ljava/lang/String;[B)V

    invoke-virtual {v3, v0}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    goto :goto_1

    .line 63370
    .end local v2    # "i":I
    :cond_4
    if-nez v3, :cond_5

    const/4 v0, 0x0

    :goto_2
    return-object v0

    :cond_5
    new-instance v0, Lcom/facebook/ads/internal/exoplayer2/thirdparty/drm/DrmInitData;

    invoke-direct {v0, v3}, Lcom/facebook/ads/internal/exoplayer2/thirdparty/drm/DrmInitData;-><init>(Ljava/util/List;)V

    goto :goto_2
.end method

.method private A06(Landroid/util/SparseArray;I)Lcom/facebook/ads/redexgen/X/CP;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/util/SparseArray<",
            "Lcom/facebook/ads/redexgen/X/CP;",
            ">;I)",
            "Lcom/facebook/ads/redexgen/X/CP;"
        }
    .end annotation

    .line 63371
    .local p1, "defaultSampleValuesArray":Landroid/util/SparseArray;, "Landroid/util/SparseArray<Lcom/facebook/ads/internal/exoplayer2/thirdparty/extractor/mp4/DefaultSampleValues;>;"
    invoke-virtual {p1}, Landroid/util/SparseArray;->size()I

    move-result v1

    const/4 v0, 0x1

    if-ne v1, v0, :cond_0

    .line 63372
    const/4 v0, 0x0

    invoke-virtual {p1, v0}, Landroid/util/SparseArray;->valueAt(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/facebook/ads/redexgen/X/CP;

    return-object v0

    .line 63373
    :cond_0
    invoke-virtual {p1, p2}, Landroid/util/SparseArray;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/facebook/ads/redexgen/X/CP;

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/Ha;->A01(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/facebook/ads/redexgen/X/CP;

    return-object v0
.end method

.method public static A07(Landroid/util/SparseArray;)Lcom/facebook/ads/redexgen/X/CV;
    .locals 10
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/util/SparseArray<",
            "Lcom/facebook/ads/redexgen/X/CV;",
            ">;)",
            "Lcom/facebook/ads/redexgen/X/CV;"
        }
    .end annotation

    .line 63374
    .local v9, "trackBundles":Landroid/util/SparseArray;, "Landroid/util/SparseArray<Lcom/facebook/ads/internal/exoplayer2/thirdparty/extractor/mp4/FragmentedMp4Extractor$TrackBundle;>;"
    const/4 v9, 0x0

    .line 63375
    .local v0, "nextTrackBundle":Lcom/facebook/ads/redexgen/X/CV;
    const-wide v7, 0x7fffffffffffffffL

    .line 63376
    .local v1, "nextTrackRunOffset":J
    invoke-virtual {p0}, Landroid/util/SparseArray;->size()I

    move-result v5

    .line 63377
    .local v3, "trackBundlesSize":I
    const/4 v4, 0x0

    .local v4, "i":I
    :goto_0
    if-ge v4, v5, :cond_3

    .line 63378
    invoke-virtual {p0, v4}, Landroid/util/SparseArray;->valueAt(I)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lcom/facebook/ads/redexgen/X/CV;

    .line 63379
    .local v5, "trackBundle":Lcom/facebook/ads/redexgen/X/CV;
    iget v6, v3, Lcom/facebook/ads/redexgen/X/CV;->A02:I

    sget-object v1, Lcom/facebook/ads/redexgen/X/XN;->A0Y:[Ljava/lang/String;

    const/4 v0, 0x1

    aget-object v0, v1, v0

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v1

    const/4 v0, 0x5

    if-eq v1, v0, :cond_2

    sget-object v2, Lcom/facebook/ads/redexgen/X/XN;->A0Y:[Ljava/lang/String;

    const-string v1, "VXm0n"

    const/4 v0, 0x0

    aput-object v1, v2, v0

    iget-object v0, v3, Lcom/facebook/ads/redexgen/X/CV;->A07:Lcom/facebook/ads/redexgen/X/Cg;

    iget v0, v0, Lcom/facebook/ads/redexgen/X/Cg;->A02:I

    if-ne v6, v0, :cond_1

    .line 63380
    .end local v5    # "trackBundle":Lcom/facebook/ads/redexgen/X/CV;
    .end local v7
    :cond_0
    :goto_1
    add-int/lit8 v4, v4, 0x1

    goto :goto_0

    .line 63381
    :cond_1
    iget-object v0, v3, Lcom/facebook/ads/redexgen/X/CV;->A07:Lcom/facebook/ads/redexgen/X/Cg;

    iget-object v1, v0, Lcom/facebook/ads/redexgen/X/Cg;->A0G:[J

    iget v0, v3, Lcom/facebook/ads/redexgen/X/CV;->A02:I

    aget-wide v1, v1, v0

    .line 63382
    .local v7, "trunOffset":J
    cmp-long v0, v1, v7

    if-gez v0, :cond_0

    .line 63383
    move-object v9, v3

    .line 63384
    move-wide v7, v1

    goto :goto_1

    :cond_2
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    .line 63385
    .end local v4    # "i":I
    :cond_3
    return-object v9
.end method

.method public static A08(Landroid/util/SparseArray;I)Lcom/facebook/ads/redexgen/X/CV;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/util/SparseArray<",
            "Lcom/facebook/ads/redexgen/X/CV;",
            ">;I)",
            "Lcom/facebook/ads/redexgen/X/CV;"
        }
    .end annotation

    .line 63386
    .local p0, "trackBundles":Landroid/util/SparseArray;, "Landroid/util/SparseArray<Lcom/facebook/ads/internal/exoplayer2/thirdparty/extractor/mp4/FragmentedMp4Extractor$TrackBundle;>;"
    invoke-virtual {p0}, Landroid/util/SparseArray;->size()I

    move-result v1

    const/4 v0, 0x1

    if-ne v1, v0, :cond_0

    .line 63387
    const/4 v0, 0x0

    invoke-virtual {p0, v0}, Landroid/util/SparseArray;->valueAt(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/facebook/ads/redexgen/X/CV;

    return-object v0

    .line 63388
    :cond_0
    invoke-virtual {p0, p1}, Landroid/util/SparseArray;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/facebook/ads/redexgen/X/CV;

    return-object v0
.end method

.method public static A09(Lcom/facebook/ads/redexgen/X/Hz;Landroid/util/SparseArray;)Lcom/facebook/ads/redexgen/X/CV;
    .locals 8
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/facebook/ads/redexgen/X/Hz;",
            "Landroid/util/SparseArray<",
            "Lcom/facebook/ads/redexgen/X/CV;",
            ">;)",
            "Lcom/facebook/ads/redexgen/X/CV;"
        }
    .end annotation

    .line 63389
    .local p4, "trackBundles":Landroid/util/SparseArray;, "Landroid/util/SparseArray<Lcom/facebook/ads/internal/exoplayer2/thirdparty/extractor/mp4/FragmentedMp4Extractor$TrackBundle;>;"
    const/16 v0, 0x8

    invoke-virtual {p0, v0}, Lcom/facebook/ads/redexgen/X/Hz;->A0Y(I)V

    .line 63390
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Hz;->A08()I

    move-result v0

    .line 63391
    .local v0, "fullAtom":I
    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/CJ;->A00(I)I

    move-result v7

    .line 63392
    .local v1, "atomFlags":I
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Hz;->A08()I

    move-result v0

    .line 63393
    .local v2, "trackId":I
    invoke-static {p1, v0}, Lcom/facebook/ads/redexgen/X/XN;->A08(Landroid/util/SparseArray;I)Lcom/facebook/ads/redexgen/X/CV;

    move-result-object v6

    .line 63394
    .local v3, "trackBundle":Lcom/facebook/ads/redexgen/X/CV;
    if-nez v6, :cond_0

    .line 63395
    const/4 v0, 0x0

    return-object v0

    .line 63396
    :cond_0
    and-int/lit8 v0, v7, 0x1

    if-eqz v0, :cond_1

    .line 63397
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Hz;->A0N()J

    move-result-wide v1

    .line 63398
    .local v4, "baseDataPosition":J
    iget-object v0, v6, Lcom/facebook/ads/redexgen/X/CV;->A07:Lcom/facebook/ads/redexgen/X/Cg;

    iput-wide v1, v0, Lcom/facebook/ads/redexgen/X/Cg;->A05:J

    .line 63399
    iget-object v0, v6, Lcom/facebook/ads/redexgen/X/CV;->A07:Lcom/facebook/ads/redexgen/X/Cg;

    iput-wide v1, v0, Lcom/facebook/ads/redexgen/X/Cg;->A04:J

    .line 63400
    .end local v4    # "baseDataPosition":J
    :cond_1
    iget-object v1, v6, Lcom/facebook/ads/redexgen/X/CV;->A04:Lcom/facebook/ads/redexgen/X/CP;

    .line 63401
    .local v4, "defaultSampleValues":Lcom/facebook/ads/redexgen/X/CP;
    and-int/lit8 v0, v7, 0x2

    if-eqz v0, :cond_5

    .line 63402
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Hz;->A0H()I

    move-result v0

    add-int/lit8 v5, v0, -0x1

    .line 63403
    .local v5, "defaultSampleDescriptionIndex":I
    :goto_0
    and-int/lit8 v0, v7, 0x8

    if-eqz v0, :cond_4

    .line 63404
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Hz;->A0H()I

    move-result v4

    .line 63405
    .local v6, "defaultSampleDuration":I
    :goto_1
    and-int/lit8 v0, v7, 0x10

    if-eqz v0, :cond_3

    .line 63406
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Hz;->A0H()I

    move-result v3

    .line 63407
    .local v7, "defaultSampleSize":I
    :goto_2
    and-int/lit8 v0, v7, 0x20

    if-eqz v0, :cond_2

    .line 63408
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Hz;->A0H()I

    move-result v2

    .line 63409
    .local p0, "defaultSampleFlags":I
    :goto_3
    iget-object v1, v6, Lcom/facebook/ads/redexgen/X/CV;->A07:Lcom/facebook/ads/redexgen/X/Cg;

    new-instance v0, Lcom/facebook/ads/redexgen/X/CP;

    invoke-direct {v0, v5, v4, v3, v2}, Lcom/facebook/ads/redexgen/X/CP;-><init>(IIII)V

    iput-object v0, v1, Lcom/facebook/ads/redexgen/X/Cg;->A07:Lcom/facebook/ads/redexgen/X/CP;

    .line 63410
    return-object v6

    .line 63411
    :cond_2
    iget v2, v1, Lcom/facebook/ads/redexgen/X/CP;->A01:I

    goto :goto_3

    .line 63412
    :cond_3
    iget v3, v1, Lcom/facebook/ads/redexgen/X/CP;->A03:I

    goto :goto_2

    .line 63413
    :cond_4
    iget v4, v1, Lcom/facebook/ads/redexgen/X/CP;->A00:I

    goto :goto_1

    .line 63414
    :cond_5
    iget v5, v1, Lcom/facebook/ads/redexgen/X/CP;->A02:I

    goto :goto_0
.end method

.method public static A0A(III)Ljava/lang/String;
    .locals 2

    sget-object v1, Lcom/facebook/ads/redexgen/X/XN;->A0X:[B

    add-int v0, p0, p1

    invoke-static {v1, p0, v0}, Ljava/util/Arrays;->copyOfRange([BII)[B

    move-result-object p0

    const/4 v1, 0x0

    :goto_0
    array-length v0, p0

    if-ge v1, v0, :cond_0

    aget-byte v0, p0, v1

    xor-int/2addr v0, p2

    xor-int/lit8 v0, v0, 0x67

    int-to-byte v0, v0

    aput-byte v0, p0, v1

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_0
    new-instance v0, Ljava/lang/String;

    invoke-direct {v0, p0}, Ljava/lang/String;-><init>([B)V

    return-object v0
.end method

.method private A0B()V
    .locals 1

    .line 63415
    const/4 v0, 0x0

    iput v0, p0, Lcom/facebook/ads/redexgen/X/XN;->A02:I

    .line 63416
    iput v0, p0, Lcom/facebook/ads/redexgen/X/XN;->A00:I

    .line 63417
    return-void
.end method

.method private A0C()V
    .locals 6

    .line 63418
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XN;->A0I:[Lcom/facebook/ads/redexgen/X/C4;

    if-nez v0, :cond_2

    .line 63419
    const/4 v0, 0x2

    new-array v5, v0, [Lcom/facebook/ads/redexgen/X/C4;

    iput-object v5, p0, Lcom/facebook/ads/redexgen/X/XN;->A0I:[Lcom/facebook/ads/redexgen/X/C4;

    .line 63420
    const/4 v4, 0x0

    .line 63421
    .local v1, "emsgTrackOutputCount":I
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/XN;->A0M:Lcom/facebook/ads/redexgen/X/C4;

    if-eqz v1, :cond_0

    .line 63422
    add-int/lit8 v0, v4, 0x1

    .end local v1    # "emsgTrackOutputCount":I
    .local v3, "emsgTrackOutputCount":I
    aput-object v1, v5, v4

    move v4, v0

    .line 63423
    .end local v3    # "emsgTrackOutputCount":I
    .restart local v1    # "emsgTrackOutputCount":I
    :cond_0
    iget v0, p0, Lcom/facebook/ads/redexgen/X/XN;->A0J:I

    const/4 v3, 0x4

    and-int/2addr v0, v3

    if-eqz v0, :cond_1

    .line 63424
    add-int/lit8 v2, v4, 0x1

    .end local v1    # "emsgTrackOutputCount":I
    .local v2, "emsgTrackOutputCount":I
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/XN;->A0C:Lcom/facebook/ads/redexgen/X/Bu;

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XN;->A0K:Landroid/util/SparseArray;

    .line 63425
    invoke-virtual {v0}, Landroid/util/SparseArray;->size()I

    move-result v0

    invoke-interface {v1, v0, v3}, Lcom/facebook/ads/redexgen/X/Bu;->AGi(II)Lcom/facebook/ads/redexgen/X/C4;

    move-result-object v0

    aput-object v0, v5, v4

    move v4, v2

    .line 63426
    .end local v2    # "emsgTrackOutputCount":I
    .restart local v1    # "emsgTrackOutputCount":I
    :cond_1
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XN;->A0I:[Lcom/facebook/ads/redexgen/X/C4;

    invoke-static {v0, v4}, Ljava/util/Arrays;->copyOf([Ljava/lang/Object;I)[Ljava/lang/Object;

    move-result-object v4

    check-cast v4, [Lcom/facebook/ads/redexgen/X/C4;

    iput-object v4, p0, Lcom/facebook/ads/redexgen/X/XN;->A0I:[Lcom/facebook/ads/redexgen/X/C4;

    .line 63427
    array-length v3, v4

    const/4 v2, 0x0

    :goto_0
    if-ge v2, v3, :cond_2

    aget-object v1, v4, v2

    .line 63428
    .local v4, "eventMessageTrackOutput":Lcom/facebook/ads/redexgen/X/C4;
    sget-object v0, Lcom/facebook/ads/redexgen/X/XN;->A0b:Lcom/facebook/ads/internal/exoplayer2/thirdparty/Format;

    invoke-interface {v1, v0}, Lcom/facebook/ads/redexgen/X/C4;->A5n(Lcom/facebook/ads/internal/exoplayer2/thirdparty/Format;)V

    .line 63429
    .end local v4    # "eventMessageTrackOutput":Lcom/facebook/ads/redexgen/X/C4;
    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    .line 63430
    .end local v1    # "emsgTrackOutputCount":I
    :cond_2
    iget-object v3, p0, Lcom/facebook/ads/redexgen/X/XN;->A0H:[Lcom/facebook/ads/redexgen/X/C4;

    sget-object v1, Lcom/facebook/ads/redexgen/X/XN;->A0Y:[Ljava/lang/String;

    const/4 v0, 0x6

    aget-object v0, v1, v0

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v1

    const/16 v0, 0x11

    if-eq v1, v0, :cond_4

    sget-object v2, Lcom/facebook/ads/redexgen/X/XN;->A0Y:[Ljava/lang/String;

    const-string v1, "8HQd7O"

    const/4 v0, 0x3

    aput-object v1, v2, v0

    if-nez v3, :cond_3

    .line 63431
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XN;->A0V:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    new-array v0, v0, [Lcom/facebook/ads/redexgen/X/C4;

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/XN;->A0H:[Lcom/facebook/ads/redexgen/X/C4;

    .line 63432
    const/4 v3, 0x0

    .local v0, "i":I
    :goto_1
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XN;->A0H:[Lcom/facebook/ads/redexgen/X/C4;

    array-length v0, v0

    if-ge v3, v0, :cond_3

    .line 63433
    iget-object v2, p0, Lcom/facebook/ads/redexgen/X/XN;->A0C:Lcom/facebook/ads/redexgen/X/Bu;

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XN;->A0K:Landroid/util/SparseArray;

    invoke-virtual {v0}, Landroid/util/SparseArray;->size()I

    move-result v0

    add-int/lit8 v1, v0, 0x1

    add-int/2addr v1, v3

    const/4 v0, 0x3

    invoke-interface {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Bu;->AGi(II)Lcom/facebook/ads/redexgen/X/C4;

    move-result-object v1

    .line 63434
    .local v1, "output":Lcom/facebook/ads/redexgen/X/C4;
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XN;->A0V:Ljava/util/List;

    invoke-interface {v0, v3}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/facebook/ads/internal/exoplayer2/thirdparty/Format;

    invoke-interface {v1, v0}, Lcom/facebook/ads/redexgen/X/C4;->A5n(Lcom/facebook/ads/internal/exoplayer2/thirdparty/Format;)V

    .line 63435
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XN;->A0H:[Lcom/facebook/ads/redexgen/X/C4;

    aput-object v1, v0, v3

    .line 63436
    .end local v1    # "output":Lcom/facebook/ads/redexgen/X/C4;
    add-int/lit8 v3, v3, 0x1

    goto :goto_1

    .line 63437
    .end local v0    # "i":I
    :cond_3
    return-void

    :cond_4
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0
.end method

.method public static A0D()V
    .locals 1

    const/16 v0, 0x2c1

    new-array v0, v0, [B

    fill-array-data v0, :array_0

    sput-object v0, Lcom/facebook/ads/redexgen/X/XN;->A0X:[B

    return-void

    :array_0
    .array-data 1
        0x75t
        0x79t
        0x54t
        0x61t
        0x7at
        0x78t
        0x35t
        0x66t
        0x7ct
        0x6ft
        0x70t
        0x35t
        0x79t
        0x70t
        0x66t
        0x66t
        0x35t
        0x61t
        0x7dt
        0x74t
        0x7bt
        0x35t
        0x7dt
        0x70t
        0x74t
        0x71t
        0x70t
        0x67t
        0x35t
        0x79t
        0x70t
        0x7bt
        0x72t
        0x61t
        0x7dt
        0x35t
        0x3dt
        0x60t
        0x7bt
        0x66t
        0x60t
        0x65t
        0x65t
        0x7at
        0x67t
        0x61t
        0x70t
        0x71t
        0x3ct
        0x3bt
        0x72t
        0x59t
        0x43t
        0x45t
        0x4et
        0x17t
        0x54t
        0x58t
        0x42t
        0x59t
        0x43t
        0x17t
        0x5et
        0x59t
        0x17t
        0x44t
        0x55t
        0x50t
        0x47t
        0x17t
        0x16t
        0xat
        0x17t
        0x6t
        0x17t
        0x1ft
        0x42t
        0x59t
        0x44t
        0x42t
        0x47t
        0x47t
        0x58t
        0x45t
        0x43t
        0x52t
        0x53t
        0x1et
        0x19t
        0x77t
        0x5ct
        0x46t
        0x40t
        0x4bt
        0x12t
        0x51t
        0x5dt
        0x47t
        0x5ct
        0x46t
        0x12t
        0x5bt
        0x5ct
        0x12t
        0x41t
        0x55t
        0x42t
        0x56t
        0x12t
        0x13t
        0xft
        0x12t
        0x3t
        0x12t
        0x1at
        0x47t
        0x5ct
        0x41t
        0x47t
        0x42t
        0x42t
        0x5dt
        0x40t
        0x46t
        0x57t
        0x56t
        0x1bt
        0x1ct
        0x39t
        0xdt
        0x1et
        0x18t
        0x12t
        0x1at
        0x11t
        0xbt
        0x1at
        0x1bt
        0x32t
        0xft
        0x4bt
        0x3at
        0x7t
        0xbt
        0xdt
        0x1et
        0x1ct
        0xbt
        0x10t
        0xdt
        0x0t
        0x2et
        0x27t
        0x26t
        0x3bt
        0x20t
        0x27t
        0x2et
        0x69t
        0x27t
        0x2ct
        0x2et
        0x28t
        0x3dt
        0x20t
        0x3ft
        0x2ct
        0x69t
        0x26t
        0x2ft
        0x2ft
        0x3at
        0x2ct
        0x3dt
        0x69t
        0x3dt
        0x26t
        0x69t
        0x3at
        0x28t
        0x24t
        0x39t
        0x25t
        0x2ct
        0x69t
        0x2dt
        0x28t
        0x3dt
        0x28t
        0x67t
        0x33t
        0x1at
        0x1et
        0x19t
        0x5ft
        0x1et
        0xbt
        0x10t
        0x12t
        0x5ft
        0x1bt
        0x1at
        0x19t
        0x16t
        0x11t
        0x1at
        0xct
        0x5ft
        0x1at
        0x7t
        0xbt
        0x1at
        0x11t
        0x1bt
        0x1at
        0x1bt
        0x5ft
        0x1et
        0xbt
        0x10t
        0x12t
        0x5ft
        0xct
        0x16t
        0x5t
        0x1at
        0x5ft
        0x57t
        0xat
        0x11t
        0xct
        0xat
        0xft
        0xft
        0x10t
        0xdt
        0xbt
        0x1at
        0x1bt
        0x56t
        0x51t
        0x1et
        0x37t
        0x33t
        0x34t
        0x72t
        0x33t
        0x26t
        0x3dt
        0x3ft
        0x72t
        0x25t
        0x3bt
        0x26t
        0x3at
        0x72t
        0x3et
        0x37t
        0x3ct
        0x35t
        0x26t
        0x3at
        0x72t
        0x6ct
        0x72t
        0x60t
        0x63t
        0x66t
        0x65t
        0x66t
        0x6at
        0x61t
        0x64t
        0x66t
        0x65t
        0x72t
        0x7at
        0x27t
        0x3ct
        0x21t
        0x27t
        0x22t
        0x22t
        0x3dt
        0x20t
        0x26t
        0x37t
        0x36t
        0x7bt
        0x7ct
        0x3ft
        0x16t
        0x1dt
        0x14t
        0x7t
        0x1bt
        0x53t
        0x1et
        0x1at
        0x0t
        0x1et
        0x12t
        0x7t
        0x10t
        0x1bt
        0x49t
        0x53t
        0xft
        0x26t
        0x26t
        0x33t
        0x25t
        0x34t
        0x60t
        0x34t
        0x2ft
        0x60t
        0x25t
        0x2et
        0x23t
        0x32t
        0x39t
        0x30t
        0x34t
        0x29t
        0x2ft
        0x2et
        0x60t
        0x24t
        0x21t
        0x34t
        0x21t
        0x60t
        0x37t
        0x21t
        0x33t
        0x60t
        0x2et
        0x25t
        0x27t
        0x21t
        0x34t
        0x29t
        0x36t
        0x25t
        0x6et
        0x10t
        0x39t
        0x39t
        0x2ct
        0x3at
        0x2bt
        0x7ft
        0x2bt
        0x30t
        0x7ft
        0x3at
        0x31t
        0x3bt
        0x7ft
        0x30t
        0x39t
        0x7ft
        0x32t
        0x3bt
        0x3et
        0x2bt
        0x7ft
        0x28t
        0x3et
        0x2ct
        0x7ft
        0x31t
        0x3at
        0x38t
        0x3et
        0x2bt
        0x36t
        0x29t
        0x3at
        0x71t
        0x6at
        0x53t
        0x40t
        0x57t
        0x57t
        0x4ct
        0x41t
        0x4ct
        0x4bt
        0x42t
        0x5t
        0x71t
        0x57t
        0x44t
        0x46t
        0x4et
        0x60t
        0x4bt
        0x46t
        0x57t
        0x5ct
        0x55t
        0x51t
        0x4ct
        0x4at
        0x4bt
        0x67t
        0x4at
        0x5dt
        0x5t
        0x55t
        0x44t
        0x57t
        0x44t
        0x48t
        0x40t
        0x51t
        0x40t
        0x57t
        0x56t
        0x5t
        0x4ct
        0x56t
        0x5t
        0x50t
        0x4bt
        0x56t
        0x50t
        0x55t
        0x55t
        0x4at
        0x57t
        0x51t
        0x40t
        0x41t
        0xbt
        0xet
        0x36t
        0x34t
        0x2dt
        0x2dt
        0x38t
        0x39t
        0x7dt
        0x2dt
        0x2et
        0x2et
        0x35t
        0x7dt
        0x3ct
        0x29t
        0x32t
        0x30t
        0x7dt
        0x75t
        0x3bt
        0x3ct
        0x34t
        0x31t
        0x38t
        0x39t
        0x7dt
        0x29t
        0x32t
        0x7dt
        0x38t
        0x25t
        0x29t
        0x2ft
        0x3ct
        0x3et
        0x29t
        0x7dt
        0x28t
        0x28t
        0x34t
        0x39t
        0x74t
        0x36t
        0xet
        0xct
        0x15t
        0x15t
        0xct
        0xbt
        0x2t
        0x45t
        0x4t
        0x11t
        0xat
        0x8t
        0x45t
        0x12t
        0xct
        0x11t
        0xdt
        0x45t
        0x9t
        0x0t
        0xbt
        0x2t
        0x11t
        0xdt
        0x45t
        0x5bt
        0x45t
        0x57t
        0x54t
        0x51t
        0x52t
        0x51t
        0x5dt
        0x56t
        0x53t
        0x51t
        0x52t
        0x45t
        0x4dt
        0x10t
        0xbt
        0x16t
        0x10t
        0x15t
        0x15t
        0xat
        0x17t
        0x11t
        0x0t
        0x1t
        0x4ct
        0x4bt
        0x3et
        0x5t
        0xet
        0x13t
        0x1bt
        0xet
        0x8t
        0x1ft
        0xet
        0xft
        0x4bt
        0x6t
        0x4t
        0x4t
        0x1dt
        0x4bt
        0x9t
        0x4t
        0x13t
        0x45t
        0xdt
        0x36t
        0x3dt
        0x20t
        0x28t
        0x3dt
        0x3bt
        0x2ct
        0x3dt
        0x3ct
        0x78t
        0x2bt
        0x39t
        0x31t
        0x37t
        0x78t
        0x3dt
        0x36t
        0x2ct
        0x2at
        0x21t
        0x78t
        0x3bt
        0x37t
        0x2dt
        0x36t
        0x2ct
        0x62t
        0x78t
        0x4ct
        0x77t
        0x71t
        0x78t
        0x77t
        0x7dt
        0x75t
        0x7ct
        0x7dt
        0x39t
        0x70t
        0x77t
        0x7dt
        0x70t
        0x6bt
        0x7ct
        0x7at
        0x6dt
        0x39t
        0x6bt
        0x7ct
        0x7ft
        0x7ct
        0x6bt
        0x7ct
        0x77t
        0x7at
        0x7ct
        0x3at
        0xdt
        0x1et
        0x5t
        0xdt
        0xet
        0x0t
        0x9t
        0x4ct
        0x0t
        0x9t
        0x2t
        0xbt
        0x18t
        0x4t
        0x4ct
        0x8t
        0x9t
        0x1ft
        0xft
        0x1et
        0x5t
        0x1ct
        0x18t
        0x5t
        0x3t
        0x2t
        0x4ct
        0x5t
        0x2t
        0x4ct
        0x1ft
        0xbt
        0x1ct
        0x8t
        0x4ct
        0xat
        0x3t
        0x19t
        0x2t
        0x8t
        0x4ct
        0x44t
        0x19t
        0x2t
        0x1ft
        0x19t
        0x1ct
        0x1ct
        0x3t
        0x1et
        0x18t
        0x9t
        0x8t
        0x45t
        0x1ft
        0xet
        0xet
        0x12t
        0x17t
        0x1dt
        0x1ft
        0xat
        0x17t
        0x11t
        0x10t
        0x51t
        0x6t
        0x53t
        0x1bt
        0x13t
        0xdt
        0x19t
        0x3t
        0x15t
        0x19t
        0x17t
        0x17t
        0x8t
        0x5t
        0x4t
        0xet
        0x4et
        0x9t
        0x4t
        0x17t
        0x2t
        0x6t
        0x19t
        0x14t
        0x15t
        0x1ft
        0x5ft
        0x1dt
        0x0t
        0x44t
    .end array-data
.end method

.method private A0E(J)V
    .locals 14

    .line 63438
    move-object v6, p0

    :cond_0
    iget-object v0, v6, Lcom/facebook/ads/redexgen/X/XN;->A0U:Ljava/util/ArrayDeque;

    invoke-virtual {v0}, Ljava/util/ArrayDeque;->isEmpty()Z

    move-result v0

    if-nez v0, :cond_3

    .line 63439
    iget-object v0, v6, Lcom/facebook/ads/redexgen/X/XN;->A0U:Ljava/util/ArrayDeque;

    invoke-virtual {v0}, Ljava/util/ArrayDeque;->removeFirst()Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Lcom/facebook/ads/redexgen/X/CU;

    .line 63440
    .local v1, "sampleInfo":Lcom/facebook/ads/redexgen/X/CU;
    iget v1, v6, Lcom/facebook/ads/redexgen/X/XN;->A03:I

    iget v0, v5, Lcom/facebook/ads/redexgen/X/CU;->A00:I

    sub-int/2addr v1, v0

    iput v1, v6, Lcom/facebook/ads/redexgen/X/XN;->A03:I

    .line 63441
    iget-wide v0, v5, Lcom/facebook/ads/redexgen/X/CU;->A01:J

    add-long v8, p1, v0

    .line 63442
    .local v2, "metadataTimeUs":J
    iget-object v0, v6, Lcom/facebook/ads/redexgen/X/XN;->A0S:Lcom/facebook/ads/redexgen/X/IB;

    if-eqz v0, :cond_1

    .line 63443
    invoke-virtual {v0, v8, v9}, Lcom/facebook/ads/redexgen/X/IB;->A06(J)J

    move-result-wide v8

    .line 63444
    :cond_1
    iget-object v4, v6, Lcom/facebook/ads/redexgen/X/XN;->A0I:[Lcom/facebook/ads/redexgen/X/C4;

    array-length v3, v4

    sget-object v1, Lcom/facebook/ads/redexgen/X/XN;->A0Y:[Ljava/lang/String;

    const/4 v0, 0x7

    aget-object v0, v1, v0

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v1

    const/4 v0, 0x3

    if-eq v1, v0, :cond_2

    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    :cond_2
    sget-object v2, Lcom/facebook/ads/redexgen/X/XN;->A0Y:[Ljava/lang/String;

    const-string v1, "ltl"

    const/4 v0, 0x7

    aput-object v1, v2, v0

    const/4 v0, 0x0

    :goto_0
    if-ge v0, v3, :cond_0

    aget-object v7, v4, v0

    .line 63445
    .local p0, "emsgTrackOutput":Lcom/facebook/ads/redexgen/X/C4;
    const/4 v10, 0x1

    iget v11, v5, Lcom/facebook/ads/redexgen/X/CU;->A00:I

    iget v12, v6, Lcom/facebook/ads/redexgen/X/XN;->A03:I

    const/4 v13, 0x0

    invoke-interface/range {v7 .. v13}, Lcom/facebook/ads/redexgen/X/C4;->AFS(JIIILcom/facebook/ads/redexgen/X/C3;)V

    .line 63446
    .end local p0    # "emsgTrackOutput":Lcom/facebook/ads/redexgen/X/C4;
    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    .line 63447
    :cond_3
    return-void
.end method

.method private A0F(J)V
    .locals 3
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lcom/facebook/ads/redexgen/X/9v;
        }
    .end annotation

    .line 63448
    :goto_0
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XN;->A0T:Ljava/util/ArrayDeque;

    invoke-virtual {v0}, Ljava/util/ArrayDeque;->isEmpty()Z

    move-result v0

    if-nez v0, :cond_0

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XN;->A0T:Ljava/util/ArrayDeque;

    invoke-virtual {v0}, Ljava/util/ArrayDeque;->peek()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/facebook/ads/redexgen/X/XT;

    iget-wide v1, v0, Lcom/facebook/ads/redexgen/X/XT;->A00:J

    cmp-long v0, v1, p1

    if-nez v0, :cond_0

    .line 63449
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XN;->A0T:Ljava/util/ArrayDeque;

    invoke-virtual {v0}, Ljava/util/ArrayDeque;->pop()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/facebook/ads/redexgen/X/XT;

    invoke-direct {p0, v0}, Lcom/facebook/ads/redexgen/X/XN;->A0I(Lcom/facebook/ads/redexgen/X/XT;)V

    goto :goto_0

    .line 63450
    :cond_0
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/XN;->A0B()V

    .line 63451
    return-void
.end method

.method private A0G(Lcom/facebook/ads/redexgen/X/Bt;)V
    .locals 3
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;,
            Ljava/lang/InterruptedException;
        }
    .end annotation

    .line 63452
    iget-wide v0, p0, Lcom/facebook/ads/redexgen/X/XN;->A07:J

    long-to-int v2, v0

    iget v0, p0, Lcom/facebook/ads/redexgen/X/XN;->A00:I

    sub-int/2addr v2, v0

    .line 63453
    .local v1, "atomPayloadSize":I
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XN;->A0E:Lcom/facebook/ads/redexgen/X/Hz;

    if-eqz v0, :cond_0

    .line 63454
    iget-object v1, v0, Lcom/facebook/ads/redexgen/X/Hz;->A00:[B

    const/16 v0, 0x8

    invoke-interface {p1, v1, v0, v2}, Lcom/facebook/ads/redexgen/X/Bt;->readFully([BII)V

    .line 63455
    iget v1, p0, Lcom/facebook/ads/redexgen/X/XN;->A01:I

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XN;->A0E:Lcom/facebook/ads/redexgen/X/Hz;

    new-instance v2, Lcom/facebook/ads/redexgen/X/XS;

    invoke-direct {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/XS;-><init>(ILcom/facebook/ads/redexgen/X/Hz;)V

    invoke-interface {p1}, Lcom/facebook/ads/redexgen/X/Bt;->A7i()J

    move-result-wide v0

    invoke-direct {p0, v2, v0, v1}, Lcom/facebook/ads/redexgen/X/XN;->A0O(Lcom/facebook/ads/redexgen/X/XS;J)V

    .line 63456
    :goto_0
    invoke-interface {p1}, Lcom/facebook/ads/redexgen/X/Bt;->A7i()J

    move-result-wide v0

    invoke-direct {p0, v0, v1}, Lcom/facebook/ads/redexgen/X/XN;->A0F(J)V

    sget-object v2, Lcom/facebook/ads/redexgen/X/XN;->A0Y:[Ljava/lang/String;

    const/4 v0, 0x2

    aget-object v1, v2, v0

    const/4 v0, 0x4

    aget-object v0, v2, v0

    invoke-virtual {v1}, Ljava/lang/String;->length()I

    move-result v1

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v0

    if-eq v1, v0, :cond_1

    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    .line 63457
    :cond_0
    invoke-interface {p1, v2}, Lcom/facebook/ads/redexgen/X/Bt;->AGP(I)V

    goto :goto_0

    .line 63458
    :cond_1
    sget-object v2, Lcom/facebook/ads/redexgen/X/XN;->A0Y:[Ljava/lang/String;

    const-string v1, "jmSEpbOdW733bHBoDHkBty1cmj"

    const/4 v0, 0x1

    aput-object v1, v2, v0

    return-void
.end method

.method private A0H(Lcom/facebook/ads/redexgen/X/Bt;)V
    .locals 9
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;,
            Ljava/lang/InterruptedException;
        }
    .end annotation

    .line 63459
    const/4 v2, 0x0

    .line 63460
    .local v0, "nextTrackBundle":Lcom/facebook/ads/redexgen/X/CV;
    const-wide v3, 0x7fffffffffffffffL

    .line 63461
    .local v1, "nextDataOffset":J
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XN;->A0K:Landroid/util/SparseArray;

    invoke-virtual {v0}, Landroid/util/SparseArray;->size()I

    move-result v8

    .line 63462
    .local v3, "trackBundlesSize":I
    const/4 v7, 0x0

    .local v4, "i":I
    :goto_0
    if-ge v7, v8, :cond_1

    .line 63463
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XN;->A0K:Landroid/util/SparseArray;

    invoke-virtual {v0, v7}, Landroid/util/SparseArray;->valueAt(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/facebook/ads/redexgen/X/CV;

    iget-object v1, v0, Lcom/facebook/ads/redexgen/X/CV;->A07:Lcom/facebook/ads/redexgen/X/Cg;

    .line 63464
    .local v5, "trackFragment":Lcom/facebook/ads/redexgen/X/Cg;
    iget-boolean v0, v1, Lcom/facebook/ads/redexgen/X/Cg;->A0B:Z

    if-eqz v0, :cond_0

    iget-wide v5, v1, Lcom/facebook/ads/redexgen/X/Cg;->A04:J

    cmp-long v0, v5, v3

    if-gez v0, :cond_0

    .line 63465
    iget-wide v3, v1, Lcom/facebook/ads/redexgen/X/Cg;->A04:J

    .line 63466
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XN;->A0K:Landroid/util/SparseArray;

    invoke-virtual {v0, v7}, Landroid/util/SparseArray;->valueAt(I)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/facebook/ads/redexgen/X/CV;

    .line 63467
    .end local v5    # "trackFragment":Lcom/facebook/ads/redexgen/X/Cg;
    :cond_0
    add-int/lit8 v7, v7, 0x1

    goto :goto_0

    .line 63468
    .end local v4    # "i":I
    :cond_1
    if-nez v2, :cond_2

    .line 63469
    const/4 v0, 0x3

    iput v0, p0, Lcom/facebook/ads/redexgen/X/XN;->A02:I

    .line 63470
    return-void

    .line 63471
    :cond_2
    invoke-interface {p1}, Lcom/facebook/ads/redexgen/X/Bt;->A7i()J

    move-result-wide v0

    sub-long/2addr v3, v0

    long-to-int v0, v3

    .line 63472
    .local v5, "bytesToSkip":I
    if-ltz v0, :cond_3

    .line 63473
    invoke-interface {p1, v0}, Lcom/facebook/ads/redexgen/X/Bt;->AGP(I)V

    .line 63474
    iget-object v0, v2, Lcom/facebook/ads/redexgen/X/CV;->A07:Lcom/facebook/ads/redexgen/X/Cg;

    invoke-virtual {v0, p1}, Lcom/facebook/ads/redexgen/X/Cg;->A04(Lcom/facebook/ads/redexgen/X/Bt;)V

    .line 63475
    return-void

    .line 63476
    :cond_3
    const/16 v2, 0x133

    const/16 v1, 0x27

    const/16 v0, 0x27

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/XN;->A0A(III)Ljava/lang/String;

    move-result-object v1

    new-instance v0, Lcom/facebook/ads/redexgen/X/9v;

    invoke-direct {v0, v1}, Lcom/facebook/ads/redexgen/X/9v;-><init>(Ljava/lang/String;)V

    throw v0
.end method

.method private A0I(Lcom/facebook/ads/redexgen/X/XT;)V
    .locals 5
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lcom/facebook/ads/redexgen/X/9v;
        }
    .end annotation

    .line 63477
    iget v1, p1, Lcom/facebook/ads/redexgen/X/CJ;->A00:I

    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A0k:I

    if-ne v1, v0, :cond_1

    .line 63478
    invoke-direct {p0, p1}, Lcom/facebook/ads/redexgen/X/XN;->A0K(Lcom/facebook/ads/redexgen/X/XT;)V

    .line 63479
    :cond_0
    :goto_0
    return-void

    .line 63480
    :cond_1
    iget v4, p1, Lcom/facebook/ads/redexgen/X/CJ;->A00:I

    sget v3, Lcom/facebook/ads/redexgen/X/CJ;->A0j:I

    sget-object v2, Lcom/facebook/ads/redexgen/X/XN;->A0Y:[Ljava/lang/String;

    const/4 v0, 0x2

    aget-object v1, v2, v0

    const/4 v0, 0x4

    aget-object v0, v2, v0

    invoke-virtual {v1}, Ljava/lang/String;->length()I

    move-result v1

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v0

    if-eq v1, v0, :cond_2

    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    :cond_2
    sget-object v2, Lcom/facebook/ads/redexgen/X/XN;->A0Y:[Ljava/lang/String;

    const-string v1, "QeKmRdkmErMZReeQj8"

    const/4 v0, 0x6

    aput-object v1, v2, v0

    if-ne v4, v3, :cond_3

    .line 63481
    invoke-direct {p0, p1}, Lcom/facebook/ads/redexgen/X/XN;->A0J(Lcom/facebook/ads/redexgen/X/XT;)V

    goto :goto_0

    .line 63482
    :cond_3
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XN;->A0T:Ljava/util/ArrayDeque;

    invoke-virtual {v0}, Ljava/util/ArrayDeque;->isEmpty()Z

    move-result v0

    if-nez v0, :cond_0

    .line 63483
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XN;->A0T:Ljava/util/ArrayDeque;

    invoke-virtual {v0}, Ljava/util/ArrayDeque;->peek()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lcom/facebook/ads/redexgen/X/XT;

    sget-object v1, Lcom/facebook/ads/redexgen/X/XN;->A0Y:[Ljava/lang/String;

    const/4 v0, 0x6

    aget-object v0, v1, v0

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v1

    const/16 v0, 0x11

    if-eq v1, v0, :cond_4

    sget-object v2, Lcom/facebook/ads/redexgen/X/XN;->A0Y:[Ljava/lang/String;

    const-string v1, "g8UpS1NyodMZ5eBXtPatKQwsoR"

    const/4 v0, 0x1

    aput-object v1, v2, v0

    invoke-virtual {v3, p1}, Lcom/facebook/ads/redexgen/X/XT;->A08(Lcom/facebook/ads/redexgen/X/XT;)V

    goto :goto_0

    :cond_4
    sget-object v2, Lcom/facebook/ads/redexgen/X/XN;->A0Y:[Ljava/lang/String;

    const-string v1, "mkv5is6F5Mu6y6USr0b4mkDGodGLaqlp"

    const/4 v0, 0x5

    aput-object v1, v2, v0

    invoke-virtual {v3, p1}, Lcom/facebook/ads/redexgen/X/XT;->A08(Lcom/facebook/ads/redexgen/X/XT;)V

    goto :goto_0
.end method

.method private A0J(Lcom/facebook/ads/redexgen/X/XT;)V
    .locals 7
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lcom/facebook/ads/redexgen/X/9v;
        }
    .end annotation

    .line 63484
    iget-object v2, p0, Lcom/facebook/ads/redexgen/X/XN;->A0K:Landroid/util/SparseArray;

    iget v1, p0, Lcom/facebook/ads/redexgen/X/XN;->A0J:I

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XN;->A0W:[B

    invoke-static {p1, v2, v1, v0}, Lcom/facebook/ads/redexgen/X/XN;->A0L(Lcom/facebook/ads/redexgen/X/XT;Landroid/util/SparseArray;I[B)V

    .line 63485
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XN;->A0L:Lcom/facebook/ads/internal/exoplayer2/thirdparty/drm/DrmInitData;

    if-eqz v0, :cond_0

    const/4 v3, 0x0

    .line 63486
    .local v0, "drmInitData":Lcom/facebook/ads/internal/exoplayer2/thirdparty/drm/DrmInitData;
    :goto_0
    if-eqz v3, :cond_2

    .line 63487
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XN;->A0K:Landroid/util/SparseArray;

    invoke-virtual {v0}, Landroid/util/SparseArray;->size()I

    move-result v2

    .line 63488
    .local v1, "trackCount":I
    const/4 v1, 0x0

    .local v2, "i":I
    :goto_1
    if-ge v1, v2, :cond_2

    .line 63489
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XN;->A0K:Landroid/util/SparseArray;

    invoke-virtual {v0, v1}, Landroid/util/SparseArray;->valueAt(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/facebook/ads/redexgen/X/CV;

    invoke-virtual {v0, v3}, Lcom/facebook/ads/redexgen/X/CV;->A06(Lcom/facebook/ads/internal/exoplayer2/thirdparty/drm/DrmInitData;)V

    .line 63490
    add-int/lit8 v1, v1, 0x1

    goto :goto_1

    .line 63491
    :cond_0
    iget-object v3, p1, Lcom/facebook/ads/redexgen/X/XT;->A02:Ljava/util/List;

    sget-object v1, Lcom/facebook/ads/redexgen/X/XN;->A0Y:[Ljava/lang/String;

    const/4 v0, 0x3

    aget-object v0, v1, v0

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v1

    const/4 v0, 0x6

    if-eq v1, v0, :cond_1

    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    :cond_1
    sget-object v2, Lcom/facebook/ads/redexgen/X/XN;->A0Y:[Ljava/lang/String;

    const-string v1, "YTpzURZBrt0"

    const/4 v0, 0x2

    aput-object v1, v2, v0

    const-string v1, "jDJUPBNch9x"

    const/4 v0, 0x4

    aput-object v1, v2, v0

    invoke-static {v3}, Lcom/facebook/ads/redexgen/X/XN;->A05(Ljava/util/List;)Lcom/facebook/ads/internal/exoplayer2/thirdparty/drm/DrmInitData;

    move-result-object v3

    goto :goto_0

    .line 63492
    .end local v1    # "trackCount":I
    .end local v2    # "i":I
    :cond_2
    iget-wide v1, p0, Lcom/facebook/ads/redexgen/X/XN;->A0A:J

    const-wide v4, -0x7fffffffffffffffL    # -4.9E-324

    cmp-long v0, v1, v4

    if-eqz v0, :cond_4

    .line 63493
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XN;->A0K:Landroid/util/SparseArray;

    invoke-virtual {v0}, Landroid/util/SparseArray;->size()I

    move-result v6

    .line 63494
    .restart local v1    # "trackCount":I
    const/4 v3, 0x0

    .restart local v2    # "i":I
    :goto_2
    if-ge v3, v6, :cond_3

    .line 63495
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XN;->A0K:Landroid/util/SparseArray;

    invoke-virtual {v0, v3}, Landroid/util/SparseArray;->valueAt(I)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/facebook/ads/redexgen/X/CV;

    iget-wide v0, p0, Lcom/facebook/ads/redexgen/X/XN;->A0A:J

    invoke-virtual {v2, v0, v1}, Lcom/facebook/ads/redexgen/X/CV;->A05(J)V

    .line 63496
    add-int/lit8 v3, v3, 0x1

    goto :goto_2

    .line 63497
    .end local v2    # "i":I
    :cond_3
    iput-wide v4, p0, Lcom/facebook/ads/redexgen/X/XN;->A0A:J

    .line 63498
    .end local v1    # "trackCount":I
    :cond_4
    return-void
.end method

.method private A0K(Lcom/facebook/ads/redexgen/X/XT;)V
    .locals 14
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lcom/facebook/ads/redexgen/X/9v;
        }
    .end annotation

    .line 63499
    move-object v5, p0

    iget-object v0, v5, Lcom/facebook/ads/redexgen/X/XN;->A0N:Lcom/facebook/ads/redexgen/X/Ce;

    if-nez v0, :cond_3

    const/4 v3, 0x1

    :goto_0
    const/16 v2, 0x214

    const/16 v1, 0x14

    const/16 v0, 0xc

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/XN;->A0A(III)Ljava/lang/String;

    move-result-object v0

    invoke-static {v3, v0}, Lcom/facebook/ads/redexgen/X/Ha;->A06(ZLjava/lang/Object;)V

    .line 63500
    iget-object v11, v5, Lcom/facebook/ads/redexgen/X/XN;->A0L:Lcom/facebook/ads/internal/exoplayer2/thirdparty/drm/DrmInitData;

    if-eqz v11, :cond_2

    .line 63501
    .local v9, "drmInitData":Lcom/facebook/ads/internal/exoplayer2/thirdparty/drm/DrmInitData;
    :goto_1
    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A0n:I

    invoke-virtual {p1, v0}, Lcom/facebook/ads/redexgen/X/XT;->A06(I)Lcom/facebook/ads/redexgen/X/XT;

    move-result-object v7

    .line 63502
    .local v2, "mvex":Lcom/facebook/ads/redexgen/X/XT;
    new-instance v4, Landroid/util/SparseArray;

    invoke-direct {v4}, Landroid/util/SparseArray;-><init>()V

    .line 63503
    .local v12, "defaultSampleValuesArray":Landroid/util/SparseArray;, "Landroid/util/SparseArray<Lcom/facebook/ads/internal/exoplayer2/thirdparty/extractor/mp4/DefaultSampleValues;>;"
    const-wide v9, -0x7fffffffffffffffL    # -4.9E-324

    .line 63504
    .local v5, "duration":J
    iget-object v0, v7, Lcom/facebook/ads/redexgen/X/XT;->A02:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v6

    .line 63505
    .local v13, "mvexChildrenSize":I
    const/4 v3, 0x0

    .end local v5    # "duration":J
    .local v7, "i":I
    .local p0, "duration":J
    :goto_2
    if-ge v3, v6, :cond_4

    .line 63506
    iget-object v0, v7, Lcom/facebook/ads/redexgen/X/XT;->A02:Ljava/util/List;

    invoke-interface {v0, v3}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/facebook/ads/redexgen/X/XS;

    .line 63507
    .local v5, "atom":Lcom/facebook/ads/redexgen/X/XS;
    iget v1, v2, Lcom/facebook/ads/redexgen/X/CJ;->A00:I

    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A1N:I

    if-ne v1, v0, :cond_1

    .line 63508
    iget-object v0, v2, Lcom/facebook/ads/redexgen/X/XS;->A00:Lcom/facebook/ads/redexgen/X/Hz;

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/XN;->A03(Lcom/facebook/ads/redexgen/X/Hz;)Landroid/util/Pair;

    move-result-object v2

    .line 63509
    .local v6, "trexData":Landroid/util/Pair;, "Landroid/util/Pair<Ljava/lang/Integer;Lcom/facebook/ads/internal/exoplayer2/thirdparty/extractor/mp4/DefaultSampleValues;>;"
    iget-object v0, v2, Landroid/util/Pair;->first:Ljava/lang/Object;

    check-cast v0, Ljava/lang/Integer;

    invoke-virtual {v0}, Ljava/lang/Integer;->intValue()I

    move-result v1

    iget-object v0, v2, Landroid/util/Pair;->second:Ljava/lang/Object;

    check-cast v0, Lcom/facebook/ads/redexgen/X/CP;

    invoke-virtual {v4, v1, v0}, Landroid/util/SparseArray;->put(ILjava/lang/Object;)V

    .line 63510
    .end local v6    # "trexData":Landroid/util/Pair;, "Landroid/util/Pair<Ljava/lang/Integer;Lcom/facebook/ads/internal/exoplayer2/thirdparty/extractor/mp4/DefaultSampleValues;>;"
    .end local v5    # "atom":Lcom/facebook/ads/redexgen/X/XS;
    :cond_0
    :goto_3
    add-int/lit8 v3, v3, 0x1

    goto :goto_2

    .line 63511
    :cond_1
    iget v1, v2, Lcom/facebook/ads/redexgen/X/CJ;->A00:I

    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A0g:I

    if-ne v1, v0, :cond_0

    .line 63512
    iget-object v0, v2, Lcom/facebook/ads/redexgen/X/XS;->A00:Lcom/facebook/ads/redexgen/X/Hz;

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/XN;->A01(Lcom/facebook/ads/redexgen/X/Hz;)J

    move-result-wide v9

    .end local p0    # "duration":J
    .local v10, "duration":J
    goto :goto_3

    .line 63513
    :cond_2
    iget-object v0, p1, Lcom/facebook/ads/redexgen/X/XT;->A02:Ljava/util/List;

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/XN;->A05(Ljava/util/List;)Lcom/facebook/ads/internal/exoplayer2/thirdparty/drm/DrmInitData;

    move-result-object v11

    goto :goto_1

    .line 63514
    :cond_3
    const/4 v3, 0x0

    goto :goto_0

    .line 63515
    .end local v7    # "i":I
    :cond_4
    new-instance v3, Landroid/util/SparseArray;

    invoke-direct {v3}, Landroid/util/SparseArray;-><init>()V

    .line 63516
    .local v11, "tracks":Landroid/util/SparseArray;, "Landroid/util/SparseArray<Lcom/facebook/ads/internal/exoplayer2/thirdparty/extractor/mp4/Track;>;"
    iget-object v0, p1, Lcom/facebook/ads/redexgen/X/XT;->A01:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v6

    .line 63517
    .local v10, "moovContainerChildrenSize":I
    const/4 v2, 0x0

    .restart local v7    # "i":I
    :goto_4
    if-ge v2, v6, :cond_7

    .line 63518
    iget-object v0, p1, Lcom/facebook/ads/redexgen/X/XT;->A01:Ljava/util/List;

    invoke-interface {v0, v2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v7

    check-cast v7, Lcom/facebook/ads/redexgen/X/XT;

    .line 63519
    .local v8, "atom":Lcom/facebook/ads/redexgen/X/XT;
    iget v1, v7, Lcom/facebook/ads/redexgen/X/CJ;->A00:I

    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A1M:I

    if-ne v1, v0, :cond_5

    .line 63520
    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A0o:I

    .line 63521
    invoke-virtual {p1, v0}, Lcom/facebook/ads/redexgen/X/XT;->A07(I)Lcom/facebook/ads/redexgen/X/XS;

    move-result-object v8

    iget v0, v5, Lcom/facebook/ads/redexgen/X/XN;->A0J:I

    and-int/lit8 v0, v0, 0x10

    if-eqz v0, :cond_6

    const/4 v12, 0x1

    :goto_5
    const/4 v13, 0x0

    .line 63522
    .end local v7    # "i":I
    .end local v8    # "atom":Lcom/facebook/ads/redexgen/X/XT;
    .local p4, "i":I
    .local p5, "atom":Lcom/facebook/ads/redexgen/X/XT;
    .end local v10    # "moovContainerChildrenSize":I
    .local p6, "moovContainerChildrenSize":I
    .end local v11    # "tracks":Landroid/util/SparseArray;, "Landroid/util/SparseArray<Lcom/facebook/ads/internal/exoplayer2/thirdparty/extractor/mp4/Track;>;"
    .local v3, "tracks":Landroid/util/SparseArray;, "Landroid/util/SparseArray<Lcom/facebook/ads/internal/exoplayer2/thirdparty/extractor/mp4/Track;>;"
    invoke-static/range {v7 .. v13}, Lcom/facebook/ads/redexgen/X/CO;->A0C(Lcom/facebook/ads/redexgen/X/XT;Lcom/facebook/ads/redexgen/X/XS;JLcom/facebook/ads/internal/exoplayer2/thirdparty/drm/DrmInitData;ZZ)Lcom/facebook/ads/redexgen/X/Ce;

    move-result-object v1

    .line 63523
    .local v5, "track":Lcom/facebook/ads/redexgen/X/Ce;
    if-eqz v1, :cond_5

    .line 63524
    iget v0, v1, Lcom/facebook/ads/redexgen/X/Ce;->A00:I

    invoke-virtual {v3, v0, v1}, Landroid/util/SparseArray;->put(ILjava/lang/Object;)V

    .line 63525
    .end local v7
    .end local v8
    .end local v10
    .end local v11
    .restart local v3    # "tracks":Landroid/util/SparseArray;, "Landroid/util/SparseArray<Lcom/facebook/ads/internal/exoplayer2/thirdparty/extractor/mp4/Track;>;"
    .restart local p4
    .restart local p6
    :cond_5
    add-int/lit8 v2, v2, 0x1

    .end local p4
    .restart local v7    # "i":I
    goto :goto_4

    .line 63526
    :cond_6
    const/4 v12, 0x0

    goto :goto_5

    .line 63527
    .end local v3    # "tracks":Landroid/util/SparseArray;, "Landroid/util/SparseArray<Lcom/facebook/ads/internal/exoplayer2/thirdparty/extractor/mp4/Track;>;"
    .end local p6
    .restart local v10    # "moovContainerChildrenSize":I
    .restart local v11    # "tracks":Landroid/util/SparseArray;, "Landroid/util/SparseArray<Lcom/facebook/ads/internal/exoplayer2/thirdparty/extractor/mp4/Track;>;"
    .end local v7    # "i":I
    .end local v10    # "moovContainerChildrenSize":I
    .end local v11    # "tracks":Landroid/util/SparseArray;, "Landroid/util/SparseArray<Lcom/facebook/ads/internal/exoplayer2/thirdparty/extractor/mp4/Track;>;"
    .restart local v3    # "tracks":Landroid/util/SparseArray;, "Landroid/util/SparseArray<Lcom/facebook/ads/internal/exoplayer2/thirdparty/extractor/mp4/Track;>;"
    .restart local p6
    :cond_7
    invoke-virtual {v3}, Landroid/util/SparseArray;->size()I

    move-result v8

    .line 63528
    .local v5, "trackCount":I
    iget-object v0, v5, Lcom/facebook/ads/redexgen/X/XN;->A0K:Landroid/util/SparseArray;

    invoke-virtual {v0}, Landroid/util/SparseArray;->size()I

    move-result v0

    if-nez v0, :cond_8

    .line 63529
    const/4 v9, 0x0

    .local v4, "i":I
    :goto_6
    if-ge v9, v8, :cond_a

    .line 63530
    invoke-virtual {v3, v9}, Landroid/util/SparseArray;->valueAt(I)Ljava/lang/Object;

    move-result-object v10

    check-cast v10, Lcom/facebook/ads/redexgen/X/Ce;

    .line 63531
    .local v6, "track":Lcom/facebook/ads/redexgen/X/Ce;
    iget-object v1, v5, Lcom/facebook/ads/redexgen/X/XN;->A0C:Lcom/facebook/ads/redexgen/X/Bu;

    iget v0, v10, Lcom/facebook/ads/redexgen/X/Ce;->A03:I

    invoke-interface {v1, v9, v0}, Lcom/facebook/ads/redexgen/X/Bu;->AGi(II)Lcom/facebook/ads/redexgen/X/C4;

    move-result-object v0

    new-instance v2, Lcom/facebook/ads/redexgen/X/CV;

    invoke-direct {v2, v0}, Lcom/facebook/ads/redexgen/X/CV;-><init>(Lcom/facebook/ads/redexgen/X/C4;)V

    .line 63532
    .local v7, "trackBundle":Lcom/facebook/ads/redexgen/X/CV;
    iget v0, v10, Lcom/facebook/ads/redexgen/X/Ce;->A00:I

    invoke-direct {v5, v4, v0}, Lcom/facebook/ads/redexgen/X/XN;->A06(Landroid/util/SparseArray;I)Lcom/facebook/ads/redexgen/X/CP;

    move-result-object v0

    invoke-virtual {v2, v10, v0}, Lcom/facebook/ads/redexgen/X/CV;->A07(Lcom/facebook/ads/redexgen/X/Ce;Lcom/facebook/ads/redexgen/X/CP;)V

    .line 63533
    iget-object v1, v5, Lcom/facebook/ads/redexgen/X/XN;->A0K:Landroid/util/SparseArray;

    iget v0, v10, Lcom/facebook/ads/redexgen/X/Ce;->A00:I

    invoke-virtual {v1, v0, v2}, Landroid/util/SparseArray;->put(ILjava/lang/Object;)V

    .line 63534
    iget-wide v6, v5, Lcom/facebook/ads/redexgen/X/XN;->A08:J

    .end local v2    # "mvex":Lcom/facebook/ads/redexgen/X/XT;
    .local v8, "mvex":Lcom/facebook/ads/redexgen/X/XT;
    iget-wide v0, v10, Lcom/facebook/ads/redexgen/X/Ce;->A04:J

    invoke-static {v6, v7, v0, v1}, Ljava/lang/Math;->max(JJ)J

    move-result-wide v0

    iput-wide v0, v5, Lcom/facebook/ads/redexgen/X/XN;->A08:J

    .line 63535
    .end local v6    # "track":Lcom/facebook/ads/redexgen/X/Ce;
    .end local v7    # "trackBundle":Lcom/facebook/ads/redexgen/X/CV;
    add-int/lit8 v9, v9, 0x1

    goto :goto_6

    .line 63536
    .end local v8    # "mvex":Lcom/facebook/ads/redexgen/X/XT;
    .restart local v2    # "mvex":Lcom/facebook/ads/redexgen/X/XT;
    .end local v2    # "mvex":Lcom/facebook/ads/redexgen/X/XT;
    .restart local v8    # "mvex":Lcom/facebook/ads/redexgen/X/XT;
    :cond_8
    iget-object v0, v5, Lcom/facebook/ads/redexgen/X/XN;->A0K:Landroid/util/SparseArray;

    invoke-virtual {v0}, Landroid/util/SparseArray;->size()I

    move-result v0

    if-ne v0, v8, :cond_9

    const/4 v0, 0x1

    :goto_7
    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/Ha;->A04(Z)V

    .line 63537
    const/4 v6, 0x0

    .local v1, "i":I
    :goto_8
    if-ge v6, v8, :cond_b

    .line 63538
    invoke-virtual {v3, v6}, Landroid/util/SparseArray;->valueAt(I)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/facebook/ads/redexgen/X/Ce;

    .line 63539
    .local v2, "track":Lcom/facebook/ads/redexgen/X/Ce;
    iget-object v1, v5, Lcom/facebook/ads/redexgen/X/XN;->A0K:Landroid/util/SparseArray;

    iget v0, v2, Lcom/facebook/ads/redexgen/X/Ce;->A00:I

    .line 63540
    invoke-virtual {v1, v0}, Landroid/util/SparseArray;->get(I)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/facebook/ads/redexgen/X/CV;

    iget v0, v2, Lcom/facebook/ads/redexgen/X/Ce;->A00:I

    .line 63541
    invoke-direct {v5, v4, v0}, Lcom/facebook/ads/redexgen/X/XN;->A06(Landroid/util/SparseArray;I)Lcom/facebook/ads/redexgen/X/CP;

    move-result-object v0

    invoke-virtual {v1, v2, v0}, Lcom/facebook/ads/redexgen/X/CV;->A07(Lcom/facebook/ads/redexgen/X/Ce;Lcom/facebook/ads/redexgen/X/CP;)V

    .line 63542
    .end local v2    # "track":Lcom/facebook/ads/redexgen/X/Ce;
    add-int/lit8 v6, v6, 0x1

    goto :goto_8

    .line 63543
    :cond_9
    const/4 v0, 0x0

    goto :goto_7

    .line 63544
    .end local v8    # "mvex":Lcom/facebook/ads/redexgen/X/XT;
    .restart local v2    # "track":Lcom/facebook/ads/redexgen/X/Ce;
    .end local v2    # "track":Lcom/facebook/ads/redexgen/X/Ce;
    .end local v4    # "i":I
    .restart local v8    # "mvex":Lcom/facebook/ads/redexgen/X/XT;
    :cond_a
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/XN;->A0C()V

    .line 63545
    iget-object v0, v5, Lcom/facebook/ads/redexgen/X/XN;->A0C:Lcom/facebook/ads/redexgen/X/Bu;

    invoke-interface {v0}, Lcom/facebook/ads/redexgen/X/Bu;->A5Y()V

    .line 63546
    .end local v1    # "i":I
    :cond_b
    return-void
.end method

.method public static A0L(Lcom/facebook/ads/redexgen/X/XT;Landroid/util/SparseArray;I[B)V
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/facebook/ads/redexgen/X/XT;",
            "Landroid/util/SparseArray<",
            "Lcom/facebook/ads/redexgen/X/CV;",
            ">;I[B)V"
        }
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lcom/facebook/ads/redexgen/X/9v;
        }
    .end annotation

    .line 63547
    .local p1, "trackBundleArray":Landroid/util/SparseArray;, "Landroid/util/SparseArray<Lcom/facebook/ads/internal/exoplayer2/thirdparty/extractor/mp4/FragmentedMp4Extractor$TrackBundle;>;"
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XT;->A01:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v4

    .line 63548
    .local v0, "moofContainerChildrenSize":I
    const/4 v3, 0x0

    .local v1, "i":I
    :goto_0
    if-ge v3, v4, :cond_1

    .line 63549
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XT;->A01:Ljava/util/List;

    invoke-interface {v0, v3}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/facebook/ads/redexgen/X/XT;

    .line 63550
    .local v2, "child":Lcom/facebook/ads/redexgen/X/XT;
    iget v1, v2, Lcom/facebook/ads/redexgen/X/CJ;->A00:I

    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A1L:I

    if-ne v1, v0, :cond_0

    .line 63551
    invoke-static {v2, p1, p2, p3}, Lcom/facebook/ads/redexgen/X/XN;->A0M(Lcom/facebook/ads/redexgen/X/XT;Landroid/util/SparseArray;I[B)V

    .line 63552
    .end local v2    # "child":Lcom/facebook/ads/redexgen/X/XT;
    :cond_0
    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    .line 63553
    .end local v1    # "i":I
    :cond_1
    return-void
.end method

.method public static A0M(Lcom/facebook/ads/redexgen/X/XT;Landroid/util/SparseArray;I[B)V
    .locals 7
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/facebook/ads/redexgen/X/XT;",
            "Landroid/util/SparseArray<",
            "Lcom/facebook/ads/redexgen/X/CV;",
            ">;I[B)V"
        }
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lcom/facebook/ads/redexgen/X/9v;
        }
    .end annotation

    .line 63554
    .local p12, "trackBundleArray":Landroid/util/SparseArray;, "Landroid/util/SparseArray<Lcom/facebook/ads/internal/exoplayer2/thirdparty/extractor/mp4/FragmentedMp4Extractor$TrackBundle;>;"
    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A1J:I

    invoke-virtual {p0, v0}, Lcom/facebook/ads/redexgen/X/XT;->A07(I)Lcom/facebook/ads/redexgen/X/XS;

    move-result-object v0

    .line 63555
    .local v2, "tfhd":Lcom/facebook/ads/redexgen/X/XS;
    iget-object v0, v0, Lcom/facebook/ads/redexgen/X/XS;->A00:Lcom/facebook/ads/redexgen/X/Hz;

    invoke-static {v0, p1}, Lcom/facebook/ads/redexgen/X/XN;->A09(Lcom/facebook/ads/redexgen/X/Hz;Landroid/util/SparseArray;)Lcom/facebook/ads/redexgen/X/CV;

    move-result-object v3

    .line 63556
    .local v3, "trackBundle":Lcom/facebook/ads/redexgen/X/CV;
    if-nez v3, :cond_0

    .line 63557
    return-void

    .line 63558
    :cond_0
    iget-object v6, v3, Lcom/facebook/ads/redexgen/X/CV;->A07:Lcom/facebook/ads/redexgen/X/Cg;

    .line 63559
    .local v5, "fragment":Lcom/facebook/ads/redexgen/X/Cg;
    iget-wide v1, v6, Lcom/facebook/ads/redexgen/X/Cg;->A06:J

    .line 63560
    .local v6, "decodeTime":J
    invoke-virtual {v3}, Lcom/facebook/ads/redexgen/X/CV;->A04()V

    .line 63561
    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A1I:I

    invoke-virtual {p0, v0}, Lcom/facebook/ads/redexgen/X/XT;->A07(I)Lcom/facebook/ads/redexgen/X/XS;

    move-result-object v0

    .line 63562
    .local p1, "tfdtAtom":Lcom/facebook/ads/redexgen/X/XS;
    if-eqz v0, :cond_1

    and-int/lit8 v0, p2, 0x2

    if-nez v0, :cond_1

    .line 63563
    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A1I:I

    invoke-virtual {p0, v0}, Lcom/facebook/ads/redexgen/X/XT;->A07(I)Lcom/facebook/ads/redexgen/X/XS;

    move-result-object v0

    iget-object v0, v0, Lcom/facebook/ads/redexgen/X/XS;->A00:Lcom/facebook/ads/redexgen/X/Hz;

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/XN;->A02(Lcom/facebook/ads/redexgen/X/Hz;)J

    move-result-wide v1

    .line 63564
    :cond_1
    invoke-static {p0, v3, v1, v2, p2}, Lcom/facebook/ads/redexgen/X/XN;->A0N(Lcom/facebook/ads/redexgen/X/XT;Lcom/facebook/ads/redexgen/X/CV;JI)V

    .line 63565
    iget-object v1, v3, Lcom/facebook/ads/redexgen/X/CV;->A05:Lcom/facebook/ads/redexgen/X/Ce;

    iget-object v0, v6, Lcom/facebook/ads/redexgen/X/Cg;->A07:Lcom/facebook/ads/redexgen/X/CP;

    iget v0, v0, Lcom/facebook/ads/redexgen/X/CP;->A02:I

    .line 63566
    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/Ce;->A00(I)Lcom/facebook/ads/redexgen/X/Cf;

    move-result-object v3

    .line 63567
    .local p2, "encryptionBox":Lcom/facebook/ads/redexgen/X/Cf;
    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A0v:I

    invoke-virtual {p0, v0}, Lcom/facebook/ads/redexgen/X/XT;->A07(I)Lcom/facebook/ads/redexgen/X/XS;

    move-result-object v0

    .line 63568
    .local p3, "saiz":Lcom/facebook/ads/redexgen/X/XS;
    if-eqz v0, :cond_2

    .line 63569
    iget-object v0, v0, Lcom/facebook/ads/redexgen/X/XS;->A00:Lcom/facebook/ads/redexgen/X/Hz;

    invoke-static {v3, v0, v6}, Lcom/facebook/ads/redexgen/X/XN;->A0P(Lcom/facebook/ads/redexgen/X/Cf;Lcom/facebook/ads/redexgen/X/Hz;Lcom/facebook/ads/redexgen/X/Cg;)V

    .line 63570
    :cond_2
    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A0u:I

    invoke-virtual {p0, v0}, Lcom/facebook/ads/redexgen/X/XT;->A07(I)Lcom/facebook/ads/redexgen/X/XS;

    move-result-object v0

    .line 63571
    .local p4, "saio":Lcom/facebook/ads/redexgen/X/XS;
    if-eqz v0, :cond_3

    .line 63572
    iget-object v0, v0, Lcom/facebook/ads/redexgen/X/XS;->A00:Lcom/facebook/ads/redexgen/X/Hz;

    invoke-static {v0, v6}, Lcom/facebook/ads/redexgen/X/XN;->A0S(Lcom/facebook/ads/redexgen/X/Hz;Lcom/facebook/ads/redexgen/X/Cg;)V

    .line 63573
    :cond_3
    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A11:I

    invoke-virtual {p0, v0}, Lcom/facebook/ads/redexgen/X/XT;->A07(I)Lcom/facebook/ads/redexgen/X/XS;

    move-result-object v0

    .line 63574
    .local p5, "senc":Lcom/facebook/ads/redexgen/X/XS;
    if-eqz v0, :cond_4

    .line 63575
    iget-object v0, v0, Lcom/facebook/ads/redexgen/X/XS;->A00:Lcom/facebook/ads/redexgen/X/Hz;

    invoke-static {v0, v6}, Lcom/facebook/ads/redexgen/X/XN;->A0T(Lcom/facebook/ads/redexgen/X/Hz;Lcom/facebook/ads/redexgen/X/Cg;)V

    .line 63576
    :cond_4
    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A0y:I

    invoke-virtual {p0, v0}, Lcom/facebook/ads/redexgen/X/XT;->A07(I)Lcom/facebook/ads/redexgen/X/XS;

    move-result-object v1

    .line 63577
    .local p6, "sbgp":Lcom/facebook/ads/redexgen/X/XS;
    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A12:I

    invoke-virtual {p0, v0}, Lcom/facebook/ads/redexgen/X/XT;->A07(I)Lcom/facebook/ads/redexgen/X/XS;

    move-result-object v0

    .line 63578
    .local p7, "sgpd":Lcom/facebook/ads/redexgen/X/XS;
    if-eqz v1, :cond_5

    if-eqz v0, :cond_5

    .line 63579
    iget-object v2, v1, Lcom/facebook/ads/redexgen/X/XS;->A00:Lcom/facebook/ads/redexgen/X/Hz;

    iget-object v1, v0, Lcom/facebook/ads/redexgen/X/XS;->A00:Lcom/facebook/ads/redexgen/X/Hz;

    .line 63580
    if-eqz v3, :cond_8

    .end local v2    # "tfhd":Lcom/facebook/ads/redexgen/X/XS;
    .local p9, "tfhd":Lcom/facebook/ads/redexgen/X/XS;
    iget-object v0, v3, Lcom/facebook/ads/redexgen/X/Cf;->A02:Ljava/lang/String;

    .line 63581
    :goto_0
    invoke-static {v2, v1, v0, v6}, Lcom/facebook/ads/redexgen/X/XN;->A0V(Lcom/facebook/ads/redexgen/X/Hz;Lcom/facebook/ads/redexgen/X/Hz;Ljava/lang/String;Lcom/facebook/ads/redexgen/X/Cg;)V

    .line 63582
    .end local v2
    .restart local p9
    :cond_5
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XT;->A02:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v1

    .line 63583
    .local v1, "leafChildrenSize":I
    const/4 v5, 0x0

    .local v2, "i":I
    :goto_1
    if-ge v5, v1, :cond_9

    .line 63584
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XT;->A02:Ljava/util/List;

    invoke-interface {v0, v5}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v4

    sget-object v2, Lcom/facebook/ads/redexgen/X/XN;->A0Y:[Ljava/lang/String;

    const/4 v0, 0x7

    aget-object v0, v2, v0

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v2

    const/4 v0, 0x3

    if-eq v2, v0, :cond_6

    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    :cond_6
    sget-object v3, Lcom/facebook/ads/redexgen/X/XN;->A0Y:[Ljava/lang/String;

    const-string v2, "89j"

    const/4 v0, 0x7

    aput-object v2, v3, v0

    check-cast v4, Lcom/facebook/ads/redexgen/X/XS;

    .line 63585
    .local p8, "atom":Lcom/facebook/ads/redexgen/X/XS;
    iget v2, v4, Lcom/facebook/ads/redexgen/X/CJ;->A00:I

    .end local v1    # "leafChildrenSize":I
    .local p10, "leafChildrenSize":I
    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A1R:I

    if-ne v2, v0, :cond_7

    .line 63586
    iget-object v0, v4, Lcom/facebook/ads/redexgen/X/XS;->A00:Lcom/facebook/ads/redexgen/X/Hz;

    invoke-static {v0, v6, p3}, Lcom/facebook/ads/redexgen/X/XN;->A0U(Lcom/facebook/ads/redexgen/X/Hz;Lcom/facebook/ads/redexgen/X/Cg;[B)V

    .line 63587
    .end local p8
    :cond_7
    add-int/lit8 v5, v5, 0x1

    goto :goto_1

    .line 63588
    .end local p9
    .restart local v2    # "i":I
    .end local v2    # "i":I
    .restart local p9
    :cond_8
    const/4 v0, 0x0

    goto :goto_0

    .line 63589
    .end local v2
    .end local p10
    .restart local v1    # "leafChildrenSize":I
    :cond_9
    return-void
.end method

.method public static A0N(Lcom/facebook/ads/redexgen/X/XT;Lcom/facebook/ads/redexgen/X/CV;JI)V
    .locals 13

    .line 63590
    const/4 v7, 0x0

    .line 63591
    .local v0, "trunCount":I
    const/4 v6, 0x0

    .line 63592
    .local v1, "totalSampleCount":I
    iget-object v5, p0, Lcom/facebook/ads/redexgen/X/XT;->A02:Ljava/util/List;

    .line 63593
    .local v9, "leafChildren":Ljava/util/List;, "Ljava/util/List<Lcom/facebook/ads/internal/exoplayer2/thirdparty/extractor/mp4/Atom$LeafAtom;>;"
    invoke-interface {v5}, Ljava/util/List;->size()I

    move-result v4

    .line 63594
    .local v10, "leafChildrenSize":I
    const/4 v3, 0x0

    .end local v0    # "trunCount":I
    .end local v1    # "totalSampleCount":I
    .local v2, "i":I
    .local v11, "trunCount":I
    .local v12, "totalSampleCount":I
    :goto_0
    if-ge v3, v4, :cond_1

    .line 63595
    invoke-interface {v5, v3}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/facebook/ads/redexgen/X/XS;

    .line 63596
    .local v0, "atom":Lcom/facebook/ads/redexgen/X/XS;
    iget v1, v2, Lcom/facebook/ads/redexgen/X/CJ;->A00:I

    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A1O:I

    if-ne v1, v0, :cond_0

    .line 63597
    iget-object v1, v2, Lcom/facebook/ads/redexgen/X/XS;->A00:Lcom/facebook/ads/redexgen/X/Hz;

    .line 63598
    .local v1, "trunData":Lcom/facebook/ads/redexgen/X/Hz;
    const/16 v0, 0xc

    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/Hz;->A0Y(I)V

    .line 63599
    invoke-virtual {v1}, Lcom/facebook/ads/redexgen/X/Hz;->A0H()I

    move-result v0

    .line 63600
    .local v3, "trunSampleCount":I
    if-lez v0, :cond_0

    .line 63601
    add-int/2addr v6, v0

    .line 63602
    add-int/lit8 v7, v7, 0x1

    .line 63603
    .end local v0    # "atom":Lcom/facebook/ads/redexgen/X/XS;
    .end local v1    # "trunData":Lcom/facebook/ads/redexgen/X/Hz;
    .end local v3    # "trunSampleCount":I
    :cond_0
    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    .line 63604
    .end local v2    # "i":I
    :cond_1
    const/4 v0, 0x0

    move-object v8, p1

    iput v0, v8, Lcom/facebook/ads/redexgen/X/CV;->A02:I

    .line 63605
    iput v0, v8, Lcom/facebook/ads/redexgen/X/CV;->A00:I

    .line 63606
    iput v0, v8, Lcom/facebook/ads/redexgen/X/CV;->A01:I

    .line 63607
    iget-object v0, v8, Lcom/facebook/ads/redexgen/X/CV;->A07:Lcom/facebook/ads/redexgen/X/Cg;

    invoke-virtual {v0, v7, v6}, Lcom/facebook/ads/redexgen/X/Cg;->A03(II)V

    .line 63608
    const/4 v9, 0x0

    .line 63609
    .local v0, "trunIndex":I
    const/4 p1, 0x0

    .line 63610
    .local v1, "trunStartPosition":I
    const/4 v3, 0x0

    .end local v0    # "trunIndex":I
    .local v1, "trunIndex":I
    .local p0, "trunStartPosition":I
    .local p1, "i":I
    :goto_1
    if-ge v3, v4, :cond_3

    .line 63611
    invoke-interface {v5, v3}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/facebook/ads/redexgen/X/XS;

    .line 63612
    .local p2, "trun":Lcom/facebook/ads/redexgen/X/XS;
    iget v1, v2, Lcom/facebook/ads/redexgen/X/CJ;->A00:I

    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A1O:I

    if-ne v1, v0, :cond_2

    .line 63613
    add-int/lit8 v0, v9, 0x1

    .end local v1    # "trunIndex":I
    .local p3, "trunIndex":I
    iget-object p0, v2, Lcom/facebook/ads/redexgen/X/XS;->A00:Lcom/facebook/ads/redexgen/X/Hz;

    .line 63614
    move-wide v10, p2

    move/from16 v12, p4

    invoke-static/range {v8 .. v14}, Lcom/facebook/ads/redexgen/X/XN;->A00(Lcom/facebook/ads/redexgen/X/CV;IJILcom/facebook/ads/redexgen/X/Hz;I)I

    move-result p1

    move v9, v0

    .line 63615
    .end local p2    # "trun":Lcom/facebook/ads/redexgen/X/XS;
    .end local p3    # "trunIndex":I
    .restart local v1    # "trunIndex":I
    :cond_2
    add-int/lit8 v3, v3, 0x1

    goto :goto_1

    .line 63616
    .end local p1    # "i":I
    :cond_3
    return-void
.end method

.method private A0O(Lcom/facebook/ads/redexgen/X/XS;J)V
    .locals 3
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lcom/facebook/ads/redexgen/X/9v;
        }
    .end annotation

    .line 63617
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XN;->A0T:Ljava/util/ArrayDeque;

    invoke-virtual {v0}, Ljava/util/ArrayDeque;->isEmpty()Z

    move-result v0

    if-nez v0, :cond_1

    .line 63618
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XN;->A0T:Ljava/util/ArrayDeque;

    invoke-virtual {v0}, Ljava/util/ArrayDeque;->peek()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/facebook/ads/redexgen/X/XT;

    invoke-virtual {v0, p1}, Lcom/facebook/ads/redexgen/X/XT;->A09(Lcom/facebook/ads/redexgen/X/XS;)V

    .line 63619
    :cond_0
    :goto_0
    return-void

    .line 63620
    :cond_1
    iget v1, p1, Lcom/facebook/ads/redexgen/X/CJ;->A00:I

    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A13:I

    if-ne v1, v0, :cond_2

    .line 63621
    iget-object v0, p1, Lcom/facebook/ads/redexgen/X/XS;->A00:Lcom/facebook/ads/redexgen/X/Hz;

    invoke-static {v0, p2, p3}, Lcom/facebook/ads/redexgen/X/XN;->A04(Lcom/facebook/ads/redexgen/X/Hz;J)Landroid/util/Pair;

    move-result-object v2

    .line 63622
    .local v0, "result":Landroid/util/Pair;, "Landroid/util/Pair<Ljava/lang/Long;Lcom/facebook/ads/internal/exoplayer2/thirdparty/extractor/ChunkIndex;>;"
    iget-object v0, v2, Landroid/util/Pair;->first:Ljava/lang/Object;

    check-cast v0, Ljava/lang/Long;

    invoke-virtual {v0}, Ljava/lang/Long;->longValue()J

    move-result-wide v0

    iput-wide v0, p0, Lcom/facebook/ads/redexgen/X/XN;->A0B:J

    .line 63623
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/XN;->A0C:Lcom/facebook/ads/redexgen/X/Bu;

    iget-object v0, v2, Landroid/util/Pair;->second:Ljava/lang/Object;

    check-cast v0, Lcom/facebook/ads/redexgen/X/C1;

    invoke-interface {v1, v0}, Lcom/facebook/ads/redexgen/X/Bu;->AFi(Lcom/facebook/ads/redexgen/X/C1;)V

    .line 63624
    const/4 v0, 0x1

    iput-boolean v0, p0, Lcom/facebook/ads/redexgen/X/XN;->A0F:Z

    .end local v0    # "result":Landroid/util/Pair;, "Landroid/util/Pair<Ljava/lang/Long;Lcom/facebook/ads/internal/exoplayer2/thirdparty/extractor/ChunkIndex;>;"
    goto :goto_0

    .line 63625
    :cond_2
    iget v1, p1, Lcom/facebook/ads/redexgen/X/CJ;->A00:I

    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A0Q:I

    if-ne v1, v0, :cond_0

    .line 63626
    iget-object v0, p1, Lcom/facebook/ads/redexgen/X/XS;->A00:Lcom/facebook/ads/redexgen/X/Hz;

    invoke-direct {p0, v0}, Lcom/facebook/ads/redexgen/X/XN;->A0Q(Lcom/facebook/ads/redexgen/X/Hz;)V

    goto :goto_0
.end method

.method public static A0P(Lcom/facebook/ads/redexgen/X/Cf;Lcom/facebook/ads/redexgen/X/Hz;Lcom/facebook/ads/redexgen/X/Cg;)V
    .locals 6
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lcom/facebook/ads/redexgen/X/9v;
        }
    .end annotation

    .line 63627
    iget p0, p0, Lcom/facebook/ads/redexgen/X/Cf;->A00:I

    .line 63628
    .local v0, "vectorSize":I
    const/16 v1, 0x8

    invoke-virtual {p1, v1}, Lcom/facebook/ads/redexgen/X/Hz;->A0Y(I)V

    .line 63629
    invoke-virtual {p1}, Lcom/facebook/ads/redexgen/X/Hz;->A08()I

    move-result v0

    .line 63630
    .local v2, "fullAtom":I
    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/CJ;->A00(I)I

    move-result v0

    .line 63631
    .local v3, "flags":I
    and-int/lit8 v0, v0, 0x1

    const/4 v5, 0x1

    if-ne v0, v5, :cond_0

    .line 63632
    invoke-virtual {p1, v1}, Lcom/facebook/ads/redexgen/X/Hz;->A0Z(I)V

    .line 63633
    :cond_0
    invoke-virtual {p1}, Lcom/facebook/ads/redexgen/X/Hz;->A0E()I

    move-result v2

    .line 63634
    .local v1, "defaultSampleInfoSize":I
    invoke-virtual {p1}, Lcom/facebook/ads/redexgen/X/Hz;->A0H()I

    move-result v4

    .line 63635
    .local v4, "sampleCount":I
    iget v0, p2, Lcom/facebook/ads/redexgen/X/Cg;->A00:I

    if-ne v4, v0, :cond_6

    .line 63636
    const/4 v3, 0x0

    .line 63637
    .local p0, "totalSize":I
    const/4 v1, 0x0

    if-nez v2, :cond_2

    .line 63638
    iget-object v2, p2, Lcom/facebook/ads/redexgen/X/Cg;->A0H:[Z

    .line 63639
    .local p2, "sampleHasSubsampleEncryptionTable":[Z
    const/4 v1, 0x0

    .local p3, "i":I
    :goto_0
    if-ge v1, v4, :cond_3

    .line 63640
    invoke-virtual {p1}, Lcom/facebook/ads/redexgen/X/Hz;->A0E()I

    move-result v0

    .line 63641
    .local p4, "sampleInfoSize":I
    add-int/2addr v3, v0

    .line 63642
    if-le v0, p0, :cond_1

    const/4 v0, 0x1

    :goto_1
    aput-boolean v0, v2, v1

    .line 63643
    .end local p4
    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    .line 63644
    :cond_1
    const/4 v0, 0x0

    goto :goto_1

    .line 63645
    :cond_2
    if-le v2, p0, :cond_4

    .line 63646
    .local v5, "subsampleEncryption":Z
    :goto_2
    mul-int/2addr v2, v4

    add-int/2addr v3, v2

    .line 63647
    iget-object v0, p2, Lcom/facebook/ads/redexgen/X/Cg;->A0H:[Z

    invoke-static {v0, v1, v4, v5}, Ljava/util/Arrays;->fill([ZIIZ)V

    .line 63648
    .end local v5    # "subsampleEncryption":Z
    :cond_3
    invoke-virtual {p2, v3}, Lcom/facebook/ads/redexgen/X/Cg;->A02(I)V

    sget-object v1, Lcom/facebook/ads/redexgen/X/XN;->A0Y:[Ljava/lang/String;

    const/4 v0, 0x6

    aget-object v0, v1, v0

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v1

    const/16 v0, 0x11

    if-eq v1, v0, :cond_5

    .line 63649
    sget-object v2, Lcom/facebook/ads/redexgen/X/XN;->A0Y:[Ljava/lang/String;

    const-string v1, "tC3WLCXP6DZ"

    const/4 v0, 0x2

    aput-object v1, v2, v0

    const-string v1, "0Zxl8CEli3D"

    const/4 v0, 0x4

    aput-object v1, v2, v0

    return-void

    .line 63650
    :cond_4
    const/4 v5, 0x0

    goto :goto_2

    :cond_5
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    .line 63651
    .end local p0    # "totalSize":I
    :cond_6
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const/16 v2, 0x122

    const/16 v1, 0x11

    const/16 v0, 0x14

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/XN;->A0A(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, v4}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v3

    const/4 v2, 0x0

    const/4 v1, 0x2

    const/16 v0, 0x3e

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/XN;->A0A(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    iget v0, p2, Lcom/facebook/ads/redexgen/X/Cg;->A00:I

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    new-instance v0, Lcom/facebook/ads/redexgen/X/9v;

    invoke-direct {v0, v1}, Lcom/facebook/ads/redexgen/X/9v;-><init>(Ljava/lang/String;)V

    throw v0
.end method

.method private A0Q(Lcom/facebook/ads/redexgen/X/Hz;)V
    .locals 17

    .line 63652
    move-object/from16 v4, p0

    iget-object v0, v4, Lcom/facebook/ads/redexgen/X/XN;->A0I:[Lcom/facebook/ads/redexgen/X/C4;

    if-eqz v0, :cond_0

    array-length v0, v0

    if-nez v0, :cond_1

    .line 63653
    .end local v10
    .end local v11
    .end local v13
    :cond_0
    return-void

    .line 63654
    :cond_1
    const/16 v7, 0xc

    move-object/from16 v8, p1

    invoke-virtual {v8, v7}, Lcom/facebook/ads/redexgen/X/Hz;->A0Y(I)V

    .line 63655
    invoke-virtual {v8}, Lcom/facebook/ads/redexgen/X/Hz;->A04()I

    move-result v10

    .line 63656
    .local v10, "sampleSize":I
    invoke-virtual {v8}, Lcom/facebook/ads/redexgen/X/Hz;->A0Q()Ljava/lang/String;

    .line 63657
    invoke-virtual {v8}, Lcom/facebook/ads/redexgen/X/Hz;->A0Q()Ljava/lang/String;

    .line 63658
    invoke-virtual {v8}, Lcom/facebook/ads/redexgen/X/Hz;->A0M()J

    move-result-wide v15

    .line 63659
    .local v11, "timescale":J
    invoke-virtual {v8}, Lcom/facebook/ads/redexgen/X/Hz;->A0M()J

    move-result-wide v11

    const-wide/32 v13, 0xf4240

    invoke-static/range {v11 .. v16}, Lcom/facebook/ads/redexgen/X/IF;->A0F(JJJ)J

    move-result-wide v2

    .line 63660
    .local v13, "presentationTimeDeltaUs":J
    iget-object v6, v4, Lcom/facebook/ads/redexgen/X/XN;->A0I:[Lcom/facebook/ads/redexgen/X/C4;

    array-length v5, v6

    const/4 v1, 0x0

    :goto_0
    if-ge v1, v5, :cond_2

    aget-object v0, v6, v1

    .line 63661
    .local v7, "emsgTrackOutput":Lcom/facebook/ads/redexgen/X/C4;
    invoke-virtual {v8, v7}, Lcom/facebook/ads/redexgen/X/Hz;->A0Y(I)V

    .line 63662
    invoke-interface {v0, v8, v10}, Lcom/facebook/ads/redexgen/X/C4;->AFR(Lcom/facebook/ads/redexgen/X/Hz;I)V

    .line 63663
    .end local v7    # "emsgTrackOutput":Lcom/facebook/ads/redexgen/X/C4;
    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    .line 63664
    :cond_2
    iget-wide v7, v4, Lcom/facebook/ads/redexgen/X/XN;->A0B:J

    const-wide v5, -0x7fffffffffffffffL    # -4.9E-324

    cmp-long v0, v7, v5

    if-eqz v0, :cond_4

    .line 63665
    add-long/2addr v7, v2

    .line 63666
    .local v2, "sampleTimeUs":J
    iget-object v0, v4, Lcom/facebook/ads/redexgen/X/XN;->A0S:Lcom/facebook/ads/redexgen/X/IB;

    if-eqz v0, :cond_3

    .line 63667
    invoke-virtual {v0, v7, v8}, Lcom/facebook/ads/redexgen/X/IB;->A06(J)J

    move-result-wide v7

    .line 63668
    .end local v2    # "sampleTimeUs":J
    .local v15, "sampleTimeUs":J
    :cond_3
    iget-object v2, v4, Lcom/facebook/ads/redexgen/X/XN;->A0I:[Lcom/facebook/ads/redexgen/X/C4;

    array-length v1, v2

    const/4 v0, 0x0

    :goto_1
    if-ge v0, v1, :cond_5

    aget-object v6, v2, v0

    .line 63669
    .local p0, "emsgTrackOutput":Lcom/facebook/ads/redexgen/X/C4;
    const/4 v9, 0x1

    const/4 v11, 0x0

    const/4 v12, 0x0

    invoke-interface/range {v6 .. v12}, Lcom/facebook/ads/redexgen/X/C4;->AFS(JIIILcom/facebook/ads/redexgen/X/C3;)V

    .line 63670
    .end local p0    # "emsgTrackOutput":Lcom/facebook/ads/redexgen/X/C4;
    add-int/lit8 v0, v0, 0x1

    goto :goto_1

    .line 63671
    :cond_4
    iget-object v1, v4, Lcom/facebook/ads/redexgen/X/XN;->A0U:Ljava/util/ArrayDeque;

    new-instance v0, Lcom/facebook/ads/redexgen/X/CU;

    invoke-direct {v0, v2, v3, v10}, Lcom/facebook/ads/redexgen/X/CU;-><init>(JI)V

    invoke-virtual {v1, v0}, Ljava/util/ArrayDeque;->addLast(Ljava/lang/Object;)V

    .line 63672
    iget v0, v4, Lcom/facebook/ads/redexgen/X/XN;->A03:I

    add-int/2addr v0, v10

    iput v0, v4, Lcom/facebook/ads/redexgen/X/XN;->A03:I

    .line 63673
    :cond_5
    return-void
.end method

.method public static A0R(Lcom/facebook/ads/redexgen/X/Hz;ILcom/facebook/ads/redexgen/X/Cg;)V
    .locals 3
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lcom/facebook/ads/redexgen/X/9v;
        }
    .end annotation

    .line 63674
    add-int/lit8 v0, p1, 0x8

    invoke-virtual {p0, v0}, Lcom/facebook/ads/redexgen/X/Hz;->A0Y(I)V

    .line 63675
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Hz;->A08()I

    move-result v0

    .line 63676
    .local v0, "fullAtom":I
    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/CJ;->A00(I)I

    move-result v1

    .line 63677
    .local v1, "flags":I
    and-int/lit8 v0, v1, 0x1

    if-nez v0, :cond_2

    .line 63678
    and-int/lit8 v0, v1, 0x2

    const/4 v2, 0x0

    if-eqz v0, :cond_0

    const/4 v1, 0x1

    .line 63679
    .local v2, "subsampleEncryption":Z
    :goto_0
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Hz;->A0H()I

    move-result p1

    .line 63680
    .local p1, "sampleCount":I
    iget v0, p2, Lcom/facebook/ads/redexgen/X/Cg;->A00:I

    if-ne p1, v0, :cond_1

    .line 63681
    iget-object v0, p2, Lcom/facebook/ads/redexgen/X/Cg;->A0H:[Z

    invoke-static {v0, v2, p1, v1}, Ljava/util/Arrays;->fill([ZIIZ)V

    .line 63682
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Hz;->A04()I

    move-result v0

    invoke-virtual {p2, v0}, Lcom/facebook/ads/redexgen/X/Cg;->A02(I)V

    .line 63683
    invoke-virtual {p2, p0}, Lcom/facebook/ads/redexgen/X/Cg;->A05(Lcom/facebook/ads/redexgen/X/Hz;)V

    .line 63684
    return-void

    .line 63685
    :cond_0
    const/4 v1, 0x0

    goto :goto_0

    .line 63686
    :cond_1
    new-instance p0, Ljava/lang/StringBuilder;

    invoke-direct {p0}, Ljava/lang/StringBuilder;-><init>()V

    const/16 v2, 0x122

    const/16 v1, 0x11

    const/16 v0, 0x14

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/XN;->A0A(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p0, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p0

    const/4 v2, 0x0

    const/4 v1, 0x2

    const/16 v0, 0x3e

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/XN;->A0A(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p0, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    iget v0, p2, Lcom/facebook/ads/redexgen/X/Cg;->A00:I

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    new-instance v0, Lcom/facebook/ads/redexgen/X/9v;

    invoke-direct {v0, v1}, Lcom/facebook/ads/redexgen/X/9v;-><init>(Ljava/lang/String;)V

    throw v0

    .line 63687
    .end local v2    # "subsampleEncryption":Z
    .end local p1    # "sampleCount":I
    :cond_2
    const/16 v2, 0x17d

    const/16 v1, 0x38

    const/16 v0, 0x42

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/XN;->A0A(III)Ljava/lang/String;

    move-result-object v1

    new-instance v0, Lcom/facebook/ads/redexgen/X/9v;

    invoke-direct {v0, v1}, Lcom/facebook/ads/redexgen/X/9v;-><init>(Ljava/lang/String;)V

    throw v0
.end method

.method public static A0S(Lcom/facebook/ads/redexgen/X/Hz;Lcom/facebook/ads/redexgen/X/Cg;)V
    .locals 5
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lcom/facebook/ads/redexgen/X/9v;
        }
    .end annotation

    .line 63688
    const/16 v3, 0x8

    invoke-virtual {p0, v3}, Lcom/facebook/ads/redexgen/X/Hz;->A0Y(I)V

    .line 63689
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Hz;->A08()I

    move-result v2

    .line 63690
    .local v1, "fullAtom":I
    invoke-static {v2}, Lcom/facebook/ads/redexgen/X/CJ;->A00(I)I

    move-result v0

    .line 63691
    .local v2, "flags":I
    and-int/lit8 v1, v0, 0x1

    const/4 v0, 0x1

    if-ne v1, v0, :cond_0

    .line 63692
    invoke-virtual {p0, v3}, Lcom/facebook/ads/redexgen/X/Hz;->A0Z(I)V

    .line 63693
    :cond_0
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Hz;->A0H()I

    move-result v4

    .line 63694
    .local v0, "entryCount":I
    if-ne v4, v0, :cond_2

    .line 63695
    invoke-static {v2}, Lcom/facebook/ads/redexgen/X/CJ;->A01(I)I

    move-result v0

    .line 63696
    .local v3, "version":I
    iget-wide v2, p1, Lcom/facebook/ads/redexgen/X/Cg;->A04:J

    .line 63697
    if-nez v0, :cond_1

    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Hz;->A0M()J

    move-result-wide v0

    :goto_0
    add-long/2addr v2, v0

    iput-wide v2, p1, Lcom/facebook/ads/redexgen/X/Cg;->A04:J

    .line 63698
    return-void

    .line 63699
    :cond_1
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Hz;->A0N()J

    move-result-wide v0

    goto :goto_0

    .line 63700
    .end local v3    # "version":I
    :cond_2
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const/16 v2, 0x228

    const/16 v1, 0x1d

    const/16 v0, 0x3f

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/XN;->A0A(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, v4}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    new-instance v0, Lcom/facebook/ads/redexgen/X/9v;

    invoke-direct {v0, v1}, Lcom/facebook/ads/redexgen/X/9v;-><init>(Ljava/lang/String;)V

    throw v0
.end method

.method public static A0T(Lcom/facebook/ads/redexgen/X/Hz;Lcom/facebook/ads/redexgen/X/Cg;)V
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lcom/facebook/ads/redexgen/X/9v;
        }
    .end annotation

    .line 63701
    const/4 v0, 0x0

    invoke-static {p0, v0, p1}, Lcom/facebook/ads/redexgen/X/XN;->A0R(Lcom/facebook/ads/redexgen/X/Hz;ILcom/facebook/ads/redexgen/X/Cg;)V

    .line 63702
    return-void
.end method

.method public static A0U(Lcom/facebook/ads/redexgen/X/Hz;Lcom/facebook/ads/redexgen/X/Cg;[B)V
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lcom/facebook/ads/redexgen/X/9v;
        }
    .end annotation

    .line 63703
    const/16 v0, 0x8

    invoke-virtual {p0, v0}, Lcom/facebook/ads/redexgen/X/Hz;->A0Y(I)V

    .line 63704
    const/4 v0, 0x0

    const/16 v1, 0x10

    invoke-virtual {p0, p2, v0, v1}, Lcom/facebook/ads/redexgen/X/Hz;->A0c([BII)V

    .line 63705
    sget-object v0, Lcom/facebook/ads/redexgen/X/XN;->A0c:[B

    invoke-static {p2, v0}, Ljava/util/Arrays;->equals([B[B)Z

    move-result v0

    if-nez v0, :cond_0

    .line 63706
    return-void

    .line 63707
    :cond_0
    invoke-static {p0, v1, p1}, Lcom/facebook/ads/redexgen/X/XN;->A0R(Lcom/facebook/ads/redexgen/X/Hz;ILcom/facebook/ads/redexgen/X/Cg;)V

    .line 63708
    return-void
.end method

.method public static A0V(Lcom/facebook/ads/redexgen/X/Hz;Lcom/facebook/ads/redexgen/X/Hz;Ljava/lang/String;Lcom/facebook/ads/redexgen/X/Cg;)V
    .locals 9
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lcom/facebook/ads/redexgen/X/9v;
        }
    .end annotation

    .line 63709
    const/16 v5, 0x8

    invoke-virtual {p0, v5}, Lcom/facebook/ads/redexgen/X/Hz;->A0Y(I)V

    .line 63710
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Hz;->A08()I

    move-result v1

    .line 63711
    .local v4, "sbgpFullAtom":I
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Hz;->A08()I

    move-result v0

    sget v4, Lcom/facebook/ads/redexgen/X/XN;->A0a:I

    if-eq v0, v4, :cond_0

    .line 63712
    return-void

    .line 63713
    :cond_0
    invoke-static {v1}, Lcom/facebook/ads/redexgen/X/CJ;->A01(I)I

    move-result v0

    const/4 v3, 0x4

    const/4 v2, 0x1

    if-ne v0, v2, :cond_1

    .line 63714
    invoke-virtual {p0, v3}, Lcom/facebook/ads/redexgen/X/Hz;->A0Z(I)V

    .line 63715
    :cond_1
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/Hz;->A08()I

    move-result v0

    if-ne v0, v2, :cond_a

    .line 63716
    invoke-virtual {p1, v5}, Lcom/facebook/ads/redexgen/X/Hz;->A0Y(I)V

    .line 63717
    invoke-virtual {p1}, Lcom/facebook/ads/redexgen/X/Hz;->A08()I

    move-result v1

    .line 63718
    .local v3, "sgpdFullAtom":I
    invoke-virtual {p1}, Lcom/facebook/ads/redexgen/X/Hz;->A08()I

    move-result v0

    if-eq v0, v4, :cond_2

    .line 63719
    return-void

    .line 63720
    :cond_2
    invoke-static {v1}, Lcom/facebook/ads/redexgen/X/CJ;->A01(I)I

    move-result v1

    .line 63721
    .local v5, "sgpdVersion":I
    if-ne v1, v2, :cond_5

    .line 63722
    invoke-virtual {p1}, Lcom/facebook/ads/redexgen/X/Hz;->A0M()J

    move-result-wide v5

    const-wide/16 v3, 0x0

    cmp-long v0, v5, v3

    if-eqz v0, :cond_9

    .line 63723
    :cond_3
    :goto_0
    invoke-virtual {p1}, Lcom/facebook/ads/redexgen/X/Hz;->A0M()J

    move-result-wide v5

    const-wide/16 v3, 0x1

    cmp-long v0, v5, v3

    if-nez v0, :cond_8

    .line 63724
    invoke-virtual {p1, v2}, Lcom/facebook/ads/redexgen/X/Hz;->A0Z(I)V

    .line 63725
    invoke-virtual {p1}, Lcom/facebook/ads/redexgen/X/Hz;->A0E()I

    move-result v1

    .line 63726
    .local v6, "patternByte":I
    and-int/lit16 v0, v1, 0xf0

    shr-int/lit8 v7, v0, 0x4

    .line 63727
    .local v7, "cryptByteBlock":I
    and-int/lit8 v8, v1, 0xf

    .line 63728
    .local p0, "skipByteBlock":I
    invoke-virtual {p1}, Lcom/facebook/ads/redexgen/X/Hz;->A0E()I

    move-result v0

    const/4 v1, 0x0

    if-ne v0, v2, :cond_4

    const/4 v3, 0x1

    .line 63729
    .local p9, "isProtected":Z
    :goto_1
    if-nez v3, :cond_6

    .line 63730
    return-void

    .line 63731
    :cond_4
    const/4 v3, 0x0

    goto :goto_1

    .line 63732
    :cond_5
    const/4 v0, 0x2

    if-lt v1, v0, :cond_3

    .line 63733
    invoke-virtual {p1, v3}, Lcom/facebook/ads/redexgen/X/Hz;->A0Z(I)V

    goto :goto_0

    .line 63734
    :cond_6
    invoke-virtual {p1}, Lcom/facebook/ads/redexgen/X/Hz;->A0E()I

    move-result v5

    .line 63735
    .local p10, "perSampleIvSize":I
    const/16 v0, 0x10

    new-array v6, v0, [B

    .line 63736
    .local p6, "keyId":[B
    array-length v0, v6

    invoke-virtual {p1, v6, v1, v0}, Lcom/facebook/ads/redexgen/X/Hz;->A0c([BII)V

    .line 63737
    const/4 p0, 0x0

    .line 63738
    .local p1, "constantIv":[B
    if-eqz v3, :cond_7

    if-nez v5, :cond_7

    .line 63739
    invoke-virtual {p1}, Lcom/facebook/ads/redexgen/X/Hz;->A0E()I

    move-result v0

    .line 63740
    .local p3, "constantIvSize":I
    new-array p0, v0, [B

    .line 63741
    invoke-virtual {p1, p0, v1, v0}, Lcom/facebook/ads/redexgen/X/Hz;->A0c([BII)V

    .line 63742
    .end local p1    # "constantIv":[B
    .local p11, "constantIv":[B
    :cond_7
    iput-boolean v2, p3, Lcom/facebook/ads/redexgen/X/Cg;->A0A:Z

    .line 63743
    new-instance v2, Lcom/facebook/ads/redexgen/X/Cf;

    .end local p6
    .local p12, "keyId":[B
    move-object v4, p2

    invoke-direct/range {v2 .. v9}, Lcom/facebook/ads/redexgen/X/Cf;-><init>(ZLjava/lang/String;I[BII[B)V

    iput-object v2, p3, Lcom/facebook/ads/redexgen/X/Cg;->A08:Lcom/facebook/ads/redexgen/X/Cf;

    .line 63744
    return-void

    .line 63745
    .end local v6    # "patternByte":I
    .end local v7    # "cryptByteBlock":I
    .end local p0    # "skipByteBlock":I
    .end local p9
    .end local p10
    .end local p11
    .end local p12
    :cond_8
    const/16 v2, 0x59

    const/16 v1, 0x27

    const/16 v0, 0x55

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/XN;->A0A(III)Ljava/lang/String;

    move-result-object v1

    new-instance v0, Lcom/facebook/ads/redexgen/X/9v;

    invoke-direct {v0, v1}, Lcom/facebook/ads/redexgen/X/9v;-><init>(Ljava/lang/String;)V

    throw v0

    .line 63746
    :cond_9
    const/16 v2, 0x261

    const/16 v1, 0x37

    const/16 v0, 0xb

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/XN;->A0A(III)Ljava/lang/String;

    move-result-object v1

    new-instance v0, Lcom/facebook/ads/redexgen/X/9v;

    invoke-direct {v0, v1}, Lcom/facebook/ads/redexgen/X/9v;-><init>(Ljava/lang/String;)V

    throw v0

    .line 63747
    .end local v3    # "sgpdFullAtom":I
    .end local v5    # "sgpdVersion":I
    :cond_a
    const/16 v2, 0x32

    const/16 v1, 0x27

    const/16 v0, 0x50

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/XN;->A0A(III)Ljava/lang/String;

    move-result-object v1

    new-instance v0, Lcom/facebook/ads/redexgen/X/9v;

    invoke-direct {v0, v1}, Lcom/facebook/ads/redexgen/X/9v;-><init>(Ljava/lang/String;)V

    throw v0
.end method

.method public static A0W(I)Z
    .locals 1

    .line 63748
    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A0k:I

    if-eq p0, v0, :cond_0

    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A1M:I

    if-eq p0, v0, :cond_0

    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A0e:I

    if-eq p0, v0, :cond_0

    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A0i:I

    if-eq p0, v0, :cond_0

    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A17:I

    if-eq p0, v0, :cond_0

    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A0j:I

    if-eq p0, v0, :cond_0

    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A1L:I

    if-eq p0, v0, :cond_0

    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A0n:I

    if-eq p0, v0, :cond_0

    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A0O:I

    if-ne p0, v0, :cond_1

    :cond_0
    const/4 v0, 0x1

    :goto_0
    return v0

    :cond_1
    const/4 v0, 0x0

    goto :goto_0
.end method

.method public static A0X(I)Z
    .locals 4

    .line 63749
    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A0W:I

    if-eq p0, v0, :cond_2

    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A0d:I

    if-eq p0, v0, :cond_2

    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A0o:I

    if-eq p0, v0, :cond_2

    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A13:I

    if-eq p0, v0, :cond_2

    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A1B:I

    if-eq p0, v0, :cond_2

    sget v3, Lcom/facebook/ads/redexgen/X/CJ;->A1I:I

    sget-object v1, Lcom/facebook/ads/redexgen/X/XN;->A0Y:[Ljava/lang/String;

    const/4 v0, 0x6

    aget-object v0, v1, v0

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v1

    const/16 v0, 0x11

    if-eq v1, v0, :cond_0

    sget-object v2, Lcom/facebook/ads/redexgen/X/XN;->A0Y:[Ljava/lang/String;

    const-string v1, "0bfDB"

    const/4 v0, 0x0

    aput-object v1, v2, v0

    if-eq p0, v3, :cond_2

    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A1J:I

    if-eq p0, v0, :cond_2

    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A1K:I

    if-eq p0, v0, :cond_2

    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A1N:I

    if-eq p0, v0, :cond_2

    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A1O:I

    if-eq p0, v0, :cond_2

    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A0s:I

    if-eq p0, v0, :cond_2

    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A0v:I

    if-eq p0, v0, :cond_2

    sget v3, Lcom/facebook/ads/redexgen/X/CJ;->A0u:I

    sget-object v1, Lcom/facebook/ads/redexgen/X/XN;->A0Y:[Ljava/lang/String;

    const/4 v0, 0x1

    aget-object v0, v1, v0

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v1

    const/4 v0, 0x5

    if-eq v1, v0, :cond_0

    sget-object v2, Lcom/facebook/ads/redexgen/X/XN;->A0Y:[Ljava/lang/String;

    const-string v1, "GM1eDi9JpLsxcWxFcWWsCjSZthCJdr4f"

    const/4 v0, 0x5

    aput-object v1, v2, v0

    if-eq p0, v3, :cond_2

    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A11:I

    if-eq p0, v0, :cond_2

    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A1R:I

    if-eq p0, v0, :cond_2

    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A0y:I

    if-eq p0, v0, :cond_2

    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A12:I

    if-eq p0, v0, :cond_2

    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A0P:I

    if-eq p0, v0, :cond_2

    sget v3, Lcom/facebook/ads/redexgen/X/CJ;->A0g:I

    sget-object v1, Lcom/facebook/ads/redexgen/X/XN;->A0Y:[Ljava/lang/String;

    const/4 v0, 0x3

    aget-object v0, v1, v0

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v1

    const/4 v0, 0x6

    if-eq v1, v0, :cond_1

    :cond_0
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    :cond_1
    sget-object v2, Lcom/facebook/ads/redexgen/X/XN;->A0Y:[Ljava/lang/String;

    const-string v1, "Eu3"

    const/4 v0, 0x7

    aput-object v1, v2, v0

    if-eq p0, v3, :cond_2

    sget v0, Lcom/facebook/ads/redexgen/X/CJ;->A0Q:I

    if-ne p0, v0, :cond_3

    :cond_2
    const/4 v0, 0x1

    :goto_0
    return v0

    :cond_3
    const/4 v0, 0x0

    goto :goto_0
.end method

.method private A0Y(Lcom/facebook/ads/redexgen/X/Bt;)Z
    .locals 10
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;,
            Ljava/lang/InterruptedException;
        }
    .end annotation

    .line 63750
    iget v0, p0, Lcom/facebook/ads/redexgen/X/XN;->A00:I

    const/16 v6, 0x8

    const/4 v5, 0x0

    const/4 v4, 0x1

    if-nez v0, :cond_1

    .line 63751
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XN;->A0O:Lcom/facebook/ads/redexgen/X/Hz;

    iget-object v0, v0, Lcom/facebook/ads/redexgen/X/Hz;->A00:[B

    invoke-interface {p1, v0, v5, v6, v4}, Lcom/facebook/ads/redexgen/X/Bt;->AEM([BIIZ)Z

    move-result v0

    if-nez v0, :cond_0

    .line 63752
    return v5

    .line 63753
    :cond_0
    iput v6, p0, Lcom/facebook/ads/redexgen/X/XN;->A00:I

    .line 63754
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XN;->A0O:Lcom/facebook/ads/redexgen/X/Hz;

    invoke-virtual {v0, v5}, Lcom/facebook/ads/redexgen/X/Hz;->A0Y(I)V

    .line 63755
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XN;->A0O:Lcom/facebook/ads/redexgen/X/Hz;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/Hz;->A0M()J

    move-result-wide v0

    iput-wide v0, p0, Lcom/facebook/ads/redexgen/X/XN;->A07:J

    .line 63756
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XN;->A0O:Lcom/facebook/ads/redexgen/X/Hz;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/Hz;->A08()I

    move-result v0

    iput v0, p0, Lcom/facebook/ads/redexgen/X/XN;->A01:I

    .line 63757
    :cond_1
    iget-wide v0, p0, Lcom/facebook/ads/redexgen/X/XN;->A07:J

    const-wide/16 v7, 0x1

    cmp-long v2, v0, v7

    if-nez v2, :cond_5

    .line 63758
    const/16 v1, 0x8

    .line 63759
    .local v0, "headerBytesRemaining":I
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XN;->A0O:Lcom/facebook/ads/redexgen/X/Hz;

    iget-object v0, v0, Lcom/facebook/ads/redexgen/X/Hz;->A00:[B

    invoke-interface {p1, v0, v6, v1}, Lcom/facebook/ads/redexgen/X/Bt;->readFully([BII)V

    .line 63760
    iget v0, p0, Lcom/facebook/ads/redexgen/X/XN;->A00:I

    add-int/2addr v0, v1

    iput v0, p0, Lcom/facebook/ads/redexgen/X/XN;->A00:I

    sget-object v1, Lcom/facebook/ads/redexgen/X/XN;->A0Y:[Ljava/lang/String;

    const/4 v0, 0x5

    aget-object v1, v1, v0

    const/4 v0, 0x2

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    const/16 v0, 0x43

    if-eq v1, v0, :cond_e

    .line 63761
    sget-object v2, Lcom/facebook/ads/redexgen/X/XN;->A0Y:[Ljava/lang/String;

    const-string v1, "6cAY2NAyMtL9j2hMAwiIzwyy"

    const/4 v0, 0x6

    aput-object v1, v2, v0

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XN;->A0O:Lcom/facebook/ads/redexgen/X/Hz;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/Hz;->A0N()J

    move-result-wide v0

    iput-wide v0, p0, Lcom/facebook/ads/redexgen/X/XN;->A07:J

    .line 63762
    .end local v0    # "headerBytesRemaining":I
    :cond_2
    :goto_0
    iget-wide v2, p0, Lcom/facebook/ads/redexgen/X/XN;->A07:J

    iget v8, p0, Lcom/facebook/ads/redexgen/X/XN;->A00:I

    sget-object v1, Lcom/facebook/ads/redexgen/X/XN;->A0Y:[Ljava/lang/String;

    const/4 v0, 0x1

    aget-object v0, v1, v0

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v1

    const/4 v0, 0x5

    if-eq v1, v0, :cond_4

    sget-object v7, Lcom/facebook/ads/redexgen/X/XN;->A0Y:[Ljava/lang/String;

    const-string v1, "YhN2vmsMCgc"

    const/4 v0, 0x2

    aput-object v1, v7, v0

    const-string v1, "GXPkMa8ctqb"

    const/4 v0, 0x4

    aput-object v1, v7, v0

    int-to-long v0, v8

    cmp-long v7, v2, v0

    if-ltz v7, :cond_c

    .line 63763
    :goto_1
    invoke-interface {p1}, Lcom/facebook/ads/redexgen/X/Bt;->A7i()J

    move-result-wide v0

    iget v2, p0, Lcom/facebook/ads/redexgen/X/XN;->A00:I

    int-to-long v2, v2

    sub-long/2addr v0, v2

    .line 63764
    .local v4, "atomPosition":J
    iget v3, p0, Lcom/facebook/ads/redexgen/X/XN;->A01:I

    sget v2, Lcom/facebook/ads/redexgen/X/CJ;->A0j:I

    if-ne v3, v2, :cond_8

    .line 63765
    iget-object v2, p0, Lcom/facebook/ads/redexgen/X/XN;->A0K:Landroid/util/SparseArray;

    invoke-virtual {v2}, Landroid/util/SparseArray;->size()I

    move-result v9

    .line 63766
    .local v0, "trackCount":I
    const/4 v8, 0x0

    .local v6, "i":I
    :goto_2
    if-ge v8, v9, :cond_8

    .line 63767
    iget-object v2, p0, Lcom/facebook/ads/redexgen/X/XN;->A0K:Landroid/util/SparseArray;

    invoke-virtual {v2, v8}, Landroid/util/SparseArray;->valueAt(I)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/facebook/ads/redexgen/X/CV;

    iget-object v2, v2, Lcom/facebook/ads/redexgen/X/CV;->A07:Lcom/facebook/ads/redexgen/X/Cg;

    .line 63768
    .local v7, "fragment":Lcom/facebook/ads/redexgen/X/Cg;
    iput-wide v0, v2, Lcom/facebook/ads/redexgen/X/Cg;->A03:J

    .line 63769
    iput-wide v0, v2, Lcom/facebook/ads/redexgen/X/Cg;->A04:J

    .line 63770
    iput-wide v0, v2, Lcom/facebook/ads/redexgen/X/Cg;->A05:J

    sget-object v3, Lcom/facebook/ads/redexgen/X/XN;->A0Y:[Ljava/lang/String;

    const/4 v2, 0x3

    aget-object v2, v3, v2

    invoke-virtual {v2}, Ljava/lang/String;->length()I

    move-result v3

    const/4 v2, 0x6

    if-eq v3, v2, :cond_3

    .line 63771
    .end local v7    # "fragment":Lcom/facebook/ads/redexgen/X/Cg;
    sget-object v7, Lcom/facebook/ads/redexgen/X/XN;->A0Y:[Ljava/lang/String;

    const-string v3, "tKeeSnEBB5V"

    const/4 v2, 0x2

    aput-object v3, v7, v2

    const-string v3, "1ppH9MP2kpM"

    const/4 v2, 0x4

    aput-object v3, v7, v2

    add-int/lit8 v8, v8, 0x0

    goto :goto_2

    .end local v7
    :cond_3
    sget-object v7, Lcom/facebook/ads/redexgen/X/XN;->A0Y:[Ljava/lang/String;

    const-string v3, "GrBVN"

    const/4 v2, 0x0

    aput-object v3, v7, v2

    add-int/lit8 v8, v8, 0x1

    goto :goto_2

    :cond_4
    sget-object v7, Lcom/facebook/ads/redexgen/X/XN;->A0Y:[Ljava/lang/String;

    const-string v1, "I3p"

    const/4 v0, 0x7

    aput-object v1, v7, v0

    int-to-long v0, v8

    cmp-long v7, v2, v0

    if-ltz v7, :cond_c

    goto :goto_1

    .line 63772
    :cond_5
    const-wide/16 v7, 0x0

    cmp-long v2, v0, v7

    if-nez v2, :cond_2

    .line 63773
    invoke-interface {p1}, Lcom/facebook/ads/redexgen/X/Bt;->A7I()J

    move-result-wide v2

    .line 63774
    .local v4, "endPosition":J
    const-wide/16 v7, -0x1

    cmp-long v0, v2, v7

    if-nez v0, :cond_6

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XN;->A0T:Ljava/util/ArrayDeque;

    invoke-virtual {v0}, Ljava/util/ArrayDeque;->isEmpty()Z

    move-result v0

    if-nez v0, :cond_6

    .line 63775
    iget-object v3, p0, Lcom/facebook/ads/redexgen/X/XN;->A0T:Ljava/util/ArrayDeque;

    sget-object v1, Lcom/facebook/ads/redexgen/X/XN;->A0Y:[Ljava/lang/String;

    const/4 v0, 0x6

    aget-object v0, v1, v0

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v1

    const/16 v0, 0x11

    if-eq v1, v0, :cond_7

    sget-object v2, Lcom/facebook/ads/redexgen/X/XN;->A0Y:[Ljava/lang/String;

    const-string v1, "9SBPW"

    const/4 v0, 0x0

    aput-object v1, v2, v0

    invoke-virtual {v3}, Ljava/util/ArrayDeque;->peek()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/facebook/ads/redexgen/X/XT;

    iget-wide v2, v0, Lcom/facebook/ads/redexgen/X/XT;->A00:J

    .line 63776
    :cond_6
    :goto_3
    cmp-long v0, v2, v7

    if-eqz v0, :cond_2

    .line 63777
    invoke-interface {p1}, Lcom/facebook/ads/redexgen/X/Bt;->A7i()J

    move-result-wide v0

    sub-long/2addr v2, v0

    iget v0, p0, Lcom/facebook/ads/redexgen/X/XN;->A00:I

    int-to-long v0, v0

    add-long/2addr v2, v0

    iput-wide v2, p0, Lcom/facebook/ads/redexgen/X/XN;->A07:J

    goto/16 :goto_0

    :cond_7
    invoke-virtual {v3}, Ljava/util/ArrayDeque;->peek()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/facebook/ads/redexgen/X/XT;

    iget-wide v2, v0, Lcom/facebook/ads/redexgen/X/XT;->A00:J

    goto :goto_3

    .line 63778
    .end local v0    # "trackCount":I
    .end local v6    # "i":I
    :cond_8
    iget v7, p0, Lcom/facebook/ads/redexgen/X/XN;->A01:I

    sget v2, Lcom/facebook/ads/redexgen/X/CJ;->A0c:I

    const/4 v3, 0x0

    if-ne v7, v2, :cond_a

    .line 63779
    iput-object v3, p0, Lcom/facebook/ads/redexgen/X/XN;->A0D:Lcom/facebook/ads/redexgen/X/CV;

    .line 63780
    iget-wide v2, p0, Lcom/facebook/ads/redexgen/X/XN;->A07:J

    add-long/2addr v2, v0

    iput-wide v2, p0, Lcom/facebook/ads/redexgen/X/XN;->A09:J

    .line 63781
    iget-boolean v2, p0, Lcom/facebook/ads/redexgen/X/XN;->A0F:Z

    if-nez v2, :cond_9

    .line 63782
    iget-object v3, p0, Lcom/facebook/ads/redexgen/X/XN;->A0C:Lcom/facebook/ads/redexgen/X/Bu;

    iget-wide v5, p0, Lcom/facebook/ads/redexgen/X/XN;->A08:J

    new-instance v2, Lcom/facebook/ads/redexgen/X/Xj;

    invoke-direct {v2, v5, v6, v0, v1}, Lcom/facebook/ads/redexgen/X/Xj;-><init>(JJ)V

    invoke-interface {v3, v2}, Lcom/facebook/ads/redexgen/X/Bu;->AFi(Lcom/facebook/ads/redexgen/X/C1;)V

    .line 63783
    iput-boolean v4, p0, Lcom/facebook/ads/redexgen/X/XN;->A0F:Z

    .line 63784
    :cond_9
    const/4 v0, 0x2

    iput v0, p0, Lcom/facebook/ads/redexgen/X/XN;->A02:I

    .line 63785
    return v4

    .line 63786
    :cond_a
    iget v0, p0, Lcom/facebook/ads/redexgen/X/XN;->A01:I

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/XN;->A0W(I)Z

    move-result v0

    if-eqz v0, :cond_d

    .line 63787
    invoke-interface {p1}, Lcom/facebook/ads/redexgen/X/Bt;->A7i()J

    move-result-wide v5

    iget-wide v0, p0, Lcom/facebook/ads/redexgen/X/XN;->A07:J

    add-long/2addr v5, v0

    const-wide/16 v0, 0x8

    sub-long/2addr v5, v0

    .line 63788
    .local v0, "endPosition":J
    iget-object v2, p0, Lcom/facebook/ads/redexgen/X/XN;->A0T:Ljava/util/ArrayDeque;

    iget v1, p0, Lcom/facebook/ads/redexgen/X/XN;->A01:I

    new-instance v0, Lcom/facebook/ads/redexgen/X/XT;

    invoke-direct {v0, v1, v5, v6}, Lcom/facebook/ads/redexgen/X/XT;-><init>(IJ)V

    invoke-virtual {v2, v0}, Ljava/util/ArrayDeque;->push(Ljava/lang/Object;)V

    .line 63789
    iget-wide v7, p0, Lcom/facebook/ads/redexgen/X/XN;->A07:J

    iget v0, p0, Lcom/facebook/ads/redexgen/X/XN;->A00:I

    int-to-long v1, v0

    cmp-long v0, v7, v1

    if-nez v0, :cond_b

    .line 63790
    invoke-direct {p0, v5, v6}, Lcom/facebook/ads/redexgen/X/XN;->A0F(J)V

    goto :goto_4

    .line 63791
    :cond_b
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/XN;->A0B()V

    goto :goto_4

    .line 63792
    .end local v4    # "endPosition":J
    :cond_c
    const/4 v2, 0x2

    const/16 v1, 0x30

    const/16 v0, 0x72

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/XN;->A0A(III)Ljava/lang/String;

    move-result-object v1

    new-instance v0, Lcom/facebook/ads/redexgen/X/9v;

    invoke-direct {v0, v1}, Lcom/facebook/ads/redexgen/X/9v;-><init>(Ljava/lang/String;)V

    throw v0

    .line 63793
    :cond_d
    iget v0, p0, Lcom/facebook/ads/redexgen/X/XN;->A01:I

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/XN;->A0X(I)Z

    move-result v0

    const-wide/32 v7, 0x7fffffff

    if-eqz v0, :cond_f

    .line 63794
    iget v3, p0, Lcom/facebook/ads/redexgen/X/XN;->A00:I

    sget-object v2, Lcom/facebook/ads/redexgen/X/XN;->A0Y:[Ljava/lang/String;

    const/4 v0, 0x2

    aget-object v1, v2, v0

    const/4 v0, 0x4

    aget-object v0, v2, v0

    invoke-virtual {v1}, Ljava/lang/String;->length()I

    move-result v1

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v0

    if-eq v1, v0, :cond_10

    :cond_e
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    .line 63795
    :cond_f
    iget-wide v1, p0, Lcom/facebook/ads/redexgen/X/XN;->A07:J

    cmp-long v0, v1, v7

    if-gtz v0, :cond_13

    .line 63796
    iput-object v3, p0, Lcom/facebook/ads/redexgen/X/XN;->A0E:Lcom/facebook/ads/redexgen/X/Hz;

    .line 63797
    iput v4, p0, Lcom/facebook/ads/redexgen/X/XN;->A02:I

    goto :goto_4

    .line 63798
    :cond_10
    sget-object v2, Lcom/facebook/ads/redexgen/X/XN;->A0Y:[Ljava/lang/String;

    const-string v1, "6Yw"

    const/4 v0, 0x7

    aput-object v1, v2, v0

    if-ne v3, v6, :cond_12

    .line 63799
    iget-wide v2, p0, Lcom/facebook/ads/redexgen/X/XN;->A07:J

    cmp-long v0, v2, v7

    if-gtz v0, :cond_11

    .line 63800
    long-to-int v1, v2

    new-instance v0, Lcom/facebook/ads/redexgen/X/Hz;

    invoke-direct {v0, v1}, Lcom/facebook/ads/redexgen/X/Hz;-><init>(I)V

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/XN;->A0E:Lcom/facebook/ads/redexgen/X/Hz;

    .line 63801
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XN;->A0O:Lcom/facebook/ads/redexgen/X/Hz;

    iget-object v1, v0, Lcom/facebook/ads/redexgen/X/Hz;->A00:[B

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XN;->A0E:Lcom/facebook/ads/redexgen/X/Hz;

    iget-object v0, v0, Lcom/facebook/ads/redexgen/X/Hz;->A00:[B

    invoke-static {v1, v5, v0, v5, v6}, Ljava/lang/System;->arraycopy(Ljava/lang/Object;ILjava/lang/Object;II)V

    .line 63802
    iput v4, p0, Lcom/facebook/ads/redexgen/X/XN;->A02:I

    .line 63803
    :goto_4
    return v4

    .line 63804
    :cond_11
    const/16 v2, 0xf1

    const/16 v1, 0x31

    const/16 v0, 0x35

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/XN;->A0A(III)Ljava/lang/String;

    move-result-object v1

    new-instance v0, Lcom/facebook/ads/redexgen/X/9v;

    invoke-direct {v0, v1}, Lcom/facebook/ads/redexgen/X/9v;-><init>(Ljava/lang/String;)V

    throw v0

    .line 63805
    :cond_12
    const/16 v2, 0xbe

    const/16 v1, 0x33

    const/16 v0, 0x18

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/XN;->A0A(III)Ljava/lang/String;

    move-result-object v1

    new-instance v0, Lcom/facebook/ads/redexgen/X/9v;

    invoke-direct {v0, v1}, Lcom/facebook/ads/redexgen/X/9v;-><init>(Ljava/lang/String;)V

    throw v0

    .line 63806
    :cond_13
    const/16 v2, 0x1df

    const/16 v1, 0x35

    const/4 v0, 0x2

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/XN;->A0A(III)Ljava/lang/String;

    move-result-object v1

    new-instance v0, Lcom/facebook/ads/redexgen/X/9v;

    invoke-direct {v0, v1}, Lcom/facebook/ads/redexgen/X/9v;-><init>(Ljava/lang/String;)V

    throw v0
.end method

.method private A0Z(Lcom/facebook/ads/redexgen/X/Bt;)Z
    .locals 18
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;,
            Ljava/lang/InterruptedException;
        }
    .end annotation

    .line 63807
    move-object/from16 v7, p0

    iget v0, v7, Lcom/facebook/ads/redexgen/X/XN;->A02:I

    const/4 v4, 0x0

    const/4 v14, 0x4

    const/4 v13, 0x1

    const/4 v12, 0x0

    const/4 v3, 0x3

    move-object/from16 v11, p1

    if-ne v0, v3, :cond_8

    .line 63808
    iget-object v0, v7, Lcom/facebook/ads/redexgen/X/XN;->A0D:Lcom/facebook/ads/redexgen/X/CV;

    if-nez v0, :cond_3

    .line 63809
    iget-object v0, v7, Lcom/facebook/ads/redexgen/X/XN;->A0K:Landroid/util/SparseArray;

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/XN;->A07(Landroid/util/SparseArray;)Lcom/facebook/ads/redexgen/X/CV;

    move-result-object v5

    .line 63810
    .local v2, "currentTrackBundle":Lcom/facebook/ads/redexgen/X/CV;
    if-nez v5, :cond_1

    .line 63811
    iget-wide v2, v7, Lcom/facebook/ads/redexgen/X/XN;->A09:J

    invoke-interface {v11}, Lcom/facebook/ads/redexgen/X/Bt;->A7i()J

    move-result-wide v0

    sub-long/2addr v2, v0

    long-to-int v0, v2

    .line 63812
    .local v4, "bytesToSkip":I
    if-ltz v0, :cond_0

    .line 63813
    invoke-interface {v11, v0}, Lcom/facebook/ads/redexgen/X/Bt;->AGP(I)V

    .line 63814
    invoke-direct/range {p0 .. p0}, Lcom/facebook/ads/redexgen/X/XN;->A0B()V

    .line 63815
    return v12

    .line 63816
    :cond_0
    const/16 v2, 0x15a

    const/16 v1, 0x23

    const/16 v0, 0x38

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/XN;->A0A(III)Ljava/lang/String;

    move-result-object v1

    new-instance v0, Lcom/facebook/ads/redexgen/X/9v;

    invoke-direct {v0, v1}, Lcom/facebook/ads/redexgen/X/9v;-><init>(Ljava/lang/String;)V

    throw v0

    .line 63817
    .end local v4    # "bytesToSkip":I
    :cond_1
    iget-object v0, v5, Lcom/facebook/ads/redexgen/X/CV;->A07:Lcom/facebook/ads/redexgen/X/Cg;

    iget-object v1, v0, Lcom/facebook/ads/redexgen/X/Cg;->A0G:[J

    iget v0, v5, Lcom/facebook/ads/redexgen/X/CV;->A02:I

    aget-wide v0, v1, v0

    .line 63818
    .local v9, "nextDataPosition":J
    invoke-interface {v11}, Lcom/facebook/ads/redexgen/X/Bt;->A7i()J

    move-result-wide v8

    sub-long/2addr v0, v8

    long-to-int v2, v0

    .line 63819
    .local v8, "bytesToSkip":I
    if-gez v2, :cond_2

    .line 63820
    const/16 v2, 0x80

    const/16 v1, 0x16

    const/16 v0, 0x18

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/XN;->A0A(III)Ljava/lang/String;

    move-result-object v6

    const/16 v2, 0x96

    const/16 v1, 0x28

    const/16 v0, 0x2e

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/XN;->A0A(III)Ljava/lang/String;

    move-result-object v0

    invoke-static {v6, v0}, Landroid/util/Log;->w(Ljava/lang/String;Ljava/lang/String;)I

    .line 63821
    const/4 v2, 0x0

    .line 63822
    :cond_2
    invoke-interface {v11, v2}, Lcom/facebook/ads/redexgen/X/Bt;->AGP(I)V

    sget-object v1, Lcom/facebook/ads/redexgen/X/XN;->A0Y:[Ljava/lang/String;

    const/4 v0, 0x6

    aget-object v0, v1, v0

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v1

    const/16 v0, 0x11

    if-eq v1, v0, :cond_5

    .line 63823
    sget-object v2, Lcom/facebook/ads/redexgen/X/XN;->A0Y:[Ljava/lang/String;

    const-string v1, "TF0FBE"

    const/4 v0, 0x3

    aput-object v1, v2, v0

    iput-object v5, v7, Lcom/facebook/ads/redexgen/X/XN;->A0D:Lcom/facebook/ads/redexgen/X/CV;

    .line 63824
    .end local v2    # "currentTrackBundle":Lcom/facebook/ads/redexgen/X/CV;
    .end local v8    # "bytesToSkip":I
    .end local v9    # "nextDataPosition":J
    :cond_3
    iget-object v0, v7, Lcom/facebook/ads/redexgen/X/XN;->A0D:Lcom/facebook/ads/redexgen/X/CV;

    iget-object v0, v0, Lcom/facebook/ads/redexgen/X/CV;->A07:Lcom/facebook/ads/redexgen/X/Cg;

    iget-object v1, v0, Lcom/facebook/ads/redexgen/X/Cg;->A0D:[I

    iget-object v0, v7, Lcom/facebook/ads/redexgen/X/XN;->A0D:Lcom/facebook/ads/redexgen/X/CV;

    iget v0, v0, Lcom/facebook/ads/redexgen/X/CV;->A01:I

    aget v0, v1, v0

    iput v0, v7, Lcom/facebook/ads/redexgen/X/XN;->A06:I

    .line 63825
    iget-object v0, v7, Lcom/facebook/ads/redexgen/X/XN;->A0D:Lcom/facebook/ads/redexgen/X/CV;

    iget v1, v0, Lcom/facebook/ads/redexgen/X/CV;->A01:I

    iget-object v0, v7, Lcom/facebook/ads/redexgen/X/XN;->A0D:Lcom/facebook/ads/redexgen/X/CV;

    iget v0, v0, Lcom/facebook/ads/redexgen/X/CV;->A03:I

    if-ge v1, v0, :cond_6

    .line 63826
    iget v0, v7, Lcom/facebook/ads/redexgen/X/XN;->A06:I

    invoke-interface {v11, v0}, Lcom/facebook/ads/redexgen/X/Bt;->AGP(I)V

    .line 63827
    iget-object v0, v7, Lcom/facebook/ads/redexgen/X/XN;->A0D:Lcom/facebook/ads/redexgen/X/CV;

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/CV;->A02(Lcom/facebook/ads/redexgen/X/CV;)V

    .line 63828
    iget-object v0, v7, Lcom/facebook/ads/redexgen/X/XN;->A0D:Lcom/facebook/ads/redexgen/X/CV;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/CV;->A08()Z

    move-result v0

    if-nez v0, :cond_4

    .line 63829
    iput-object v4, v7, Lcom/facebook/ads/redexgen/X/XN;->A0D:Lcom/facebook/ads/redexgen/X/CV;

    .line 63830
    :cond_4
    iput v3, v7, Lcom/facebook/ads/redexgen/X/XN;->A02:I

    .line 63831
    return v13

    :cond_5
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    .line 63832
    :cond_6
    iget-object v0, v7, Lcom/facebook/ads/redexgen/X/XN;->A0D:Lcom/facebook/ads/redexgen/X/CV;

    iget-object v0, v0, Lcom/facebook/ads/redexgen/X/CV;->A05:Lcom/facebook/ads/redexgen/X/Ce;

    iget v0, v0, Lcom/facebook/ads/redexgen/X/Ce;->A02:I

    if-ne v0, v13, :cond_7

    .line 63833
    iget v1, v7, Lcom/facebook/ads/redexgen/X/XN;->A06:I

    const/16 v0, 0x8

    sub-int/2addr v1, v0

    iput v1, v7, Lcom/facebook/ads/redexgen/X/XN;->A06:I

    .line 63834
    invoke-interface {v11, v0}, Lcom/facebook/ads/redexgen/X/Bt;->AGP(I)V

    .line 63835
    :cond_7
    iget-object v0, v7, Lcom/facebook/ads/redexgen/X/XN;->A0D:Lcom/facebook/ads/redexgen/X/CV;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/CV;->A03()I

    move-result v1

    iput v1, v7, Lcom/facebook/ads/redexgen/X/XN;->A04:I

    .line 63836
    iget v0, v7, Lcom/facebook/ads/redexgen/X/XN;->A06:I

    add-int/2addr v0, v1

    iput v0, v7, Lcom/facebook/ads/redexgen/X/XN;->A06:I

    .line 63837
    iput v14, v7, Lcom/facebook/ads/redexgen/X/XN;->A02:I

    .line 63838
    iput v12, v7, Lcom/facebook/ads/redexgen/X/XN;->A05:I

    .line 63839
    :cond_8
    iget-object v0, v7, Lcom/facebook/ads/redexgen/X/XN;->A0D:Lcom/facebook/ads/redexgen/X/CV;

    iget-object v10, v0, Lcom/facebook/ads/redexgen/X/CV;->A07:Lcom/facebook/ads/redexgen/X/Cg;

    .line 63840
    .local v2, "fragment":Lcom/facebook/ads/redexgen/X/Cg;
    iget-object v3, v7, Lcom/facebook/ads/redexgen/X/XN;->A0D:Lcom/facebook/ads/redexgen/X/CV;

    sget-object v2, Lcom/facebook/ads/redexgen/X/XN;->A0Y:[Ljava/lang/String;

    const/4 v0, 0x2

    aget-object v1, v2, v0

    const/4 v0, 0x4

    aget-object v0, v2, v0

    invoke-virtual {v1}, Ljava/lang/String;->length()I

    move-result v1

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v0

    if-eq v1, v0, :cond_e

    sget-object v2, Lcom/facebook/ads/redexgen/X/XN;->A0Y:[Ljava/lang/String;

    const-string v1, "Pqi"

    const/4 v0, 0x7

    aput-object v1, v2, v0

    iget-object v6, v3, Lcom/facebook/ads/redexgen/X/CV;->A05:Lcom/facebook/ads/redexgen/X/Ce;

    .line 63841
    .local v8, "track":Lcom/facebook/ads/redexgen/X/Ce;
    iget-object v0, v7, Lcom/facebook/ads/redexgen/X/XN;->A0D:Lcom/facebook/ads/redexgen/X/CV;

    iget-object v5, v0, Lcom/facebook/ads/redexgen/X/CV;->A06:Lcom/facebook/ads/redexgen/X/C4;

    .line 63842
    .local v9, "output":Lcom/facebook/ads/redexgen/X/C4;
    iget-object v0, v7, Lcom/facebook/ads/redexgen/X/XN;->A0D:Lcom/facebook/ads/redexgen/X/CV;

    iget v4, v0, Lcom/facebook/ads/redexgen/X/CV;->A01:I

    .line 63843
    .local v15, "sampleIndex":I
    invoke-virtual {v10, v4}, Lcom/facebook/ads/redexgen/X/Cg;->A00(I)J

    move-result-wide v1

    const-wide/16 v8, 0x3e8

    mul-long/2addr v1, v8

    .line 63844
    .local v10, "sampleTimeUs":J
    iget-object v0, v7, Lcom/facebook/ads/redexgen/X/XN;->A0S:Lcom/facebook/ads/redexgen/X/IB;

    if-eqz v0, :cond_9

    .line 63845
    :goto_0
    invoke-virtual {v0, v1, v2}, Lcom/facebook/ads/redexgen/X/IB;->A06(J)J

    move-result-wide v1

    .line 63846
    .end local v10    # "sampleTimeUs":J
    .local v13, "sampleTimeUs":J
    :cond_9
    iget v0, v6, Lcom/facebook/ads/redexgen/X/Ce;->A01:I

    if-eqz v0, :cond_f

    .line 63847
    iget-object v0, v7, Lcom/facebook/ads/redexgen/X/XN;->A0Q:Lcom/facebook/ads/redexgen/X/Hz;

    iget-object v9, v0, Lcom/facebook/ads/redexgen/X/Hz;->A00:[B

    .line 63848
    .local v10, "nalPrefixData":[B
    aput-byte v12, v9, v12

    .line 63849
    aput-byte v12, v9, v13

    .line 63850
    const/4 v0, 0x2

    aput-byte v12, v9, v0

    .line 63851
    iget v8, v6, Lcom/facebook/ads/redexgen/X/Ce;->A01:I

    add-int/2addr v8, v13

    .line 63852
    .local v11, "nalUnitPrefixLength":I
    iget v0, v6, Lcom/facebook/ads/redexgen/X/Ce;->A01:I

    rsub-int/lit8 v17, v0, 0x4

    .line 63853
    .local v12, "nalUnitLengthFieldLengthDiff":I
    :goto_1
    iget v3, v7, Lcom/facebook/ads/redexgen/X/XN;->A04:I

    iget v0, v7, Lcom/facebook/ads/redexgen/X/XN;->A06:I

    if-ge v3, v0, :cond_10

    .line 63854
    iget v15, v7, Lcom/facebook/ads/redexgen/X/XN;->A05:I

    sget-object v3, Lcom/facebook/ads/redexgen/X/XN;->A0Y:[Ljava/lang/String;

    const/4 v0, 0x1

    aget-object v0, v3, v0

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v3

    const/4 v0, 0x5

    move v3, v3

    move v0, v0

    if-eq v3, v0, :cond_b

    sget-object v16, Lcom/facebook/ads/redexgen/X/XN;->A0Y:[Ljava/lang/String;

    const-string v3, "oY9Cfc6NZLH"

    const/4 v0, 0x2

    aput-object v3, v16, v0

    const-string v3, "IHvoiGll8tT"

    const/4 v0, 0x4

    aput-object v3, v16, v0

    if-nez v15, :cond_c

    .line 63855
    :goto_2
    move/from16 v0, v17

    invoke-interface {v11, v9, v0, v8}, Lcom/facebook/ads/redexgen/X/Bt;->readFully([BII)V

    .line 63856
    iget-object v0, v7, Lcom/facebook/ads/redexgen/X/XN;->A0Q:Lcom/facebook/ads/redexgen/X/Hz;

    invoke-virtual {v0, v12}, Lcom/facebook/ads/redexgen/X/Hz;->A0Y(I)V

    .line 63857
    iget-object v0, v7, Lcom/facebook/ads/redexgen/X/XN;->A0Q:Lcom/facebook/ads/redexgen/X/Hz;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/Hz;->A0H()I

    move-result v0

    sub-int/2addr v0, v13

    iput v0, v7, Lcom/facebook/ads/redexgen/X/XN;->A05:I

    .line 63858
    iget-object v0, v7, Lcom/facebook/ads/redexgen/X/XN;->A0R:Lcom/facebook/ads/redexgen/X/Hz;

    invoke-virtual {v0, v12}, Lcom/facebook/ads/redexgen/X/Hz;->A0Y(I)V

    .line 63859
    iget-object v0, v7, Lcom/facebook/ads/redexgen/X/XN;->A0R:Lcom/facebook/ads/redexgen/X/Hz;

    invoke-interface {v5, v0, v14}, Lcom/facebook/ads/redexgen/X/C4;->AFR(Lcom/facebook/ads/redexgen/X/Hz;I)V

    .line 63860
    iget-object v0, v7, Lcom/facebook/ads/redexgen/X/XN;->A0Q:Lcom/facebook/ads/redexgen/X/Hz;

    invoke-interface {v5, v0, v13}, Lcom/facebook/ads/redexgen/X/C4;->AFR(Lcom/facebook/ads/redexgen/X/Hz;I)V

    .line 63861
    iget-object v0, v7, Lcom/facebook/ads/redexgen/X/XN;->A0H:[Lcom/facebook/ads/redexgen/X/C4;

    array-length v0, v0

    if-lez v0, :cond_a

    iget-object v0, v6, Lcom/facebook/ads/redexgen/X/Ce;->A07:Lcom/facebook/ads/internal/exoplayer2/thirdparty/Format;

    iget-object v3, v0, Lcom/facebook/ads/internal/exoplayer2/thirdparty/Format;->A0O:Ljava/lang/String;

    aget-byte v0, v9, v14

    .line 63862
    invoke-static {v3, v0}, Lcom/facebook/ads/redexgen/X/Hv;->A0C(Ljava/lang/String;B)Z

    move-result v0

    if-eqz v0, :cond_a

    const/4 v0, 0x1

    :goto_3
    iput-boolean v0, v7, Lcom/facebook/ads/redexgen/X/XN;->A0G:Z

    .line 63863
    iget v0, v7, Lcom/facebook/ads/redexgen/X/XN;->A04:I

    add-int/lit8 v0, v0, 0x5

    iput v0, v7, Lcom/facebook/ads/redexgen/X/XN;->A04:I

    .line 63864
    iget v0, v7, Lcom/facebook/ads/redexgen/X/XN;->A06:I

    add-int v0, v0, v17

    iput v0, v7, Lcom/facebook/ads/redexgen/X/XN;->A06:I

    goto :goto_1

    .line 63865
    :cond_a
    const/4 v0, 0x0

    goto :goto_3

    :cond_b
    if-nez v15, :cond_c

    goto :goto_2

    .line 63866
    :cond_c
    iget-boolean v0, v7, Lcom/facebook/ads/redexgen/X/XN;->A0G:Z

    if-eqz v0, :cond_d

    .line 63867
    iget-object v0, v7, Lcom/facebook/ads/redexgen/X/XN;->A0P:Lcom/facebook/ads/redexgen/X/Hz;

    invoke-virtual {v0, v15}, Lcom/facebook/ads/redexgen/X/Hz;->A0W(I)V

    .line 63868
    iget-object v0, v7, Lcom/facebook/ads/redexgen/X/XN;->A0P:Lcom/facebook/ads/redexgen/X/Hz;

    iget-object v3, v0, Lcom/facebook/ads/redexgen/X/Hz;->A00:[B

    iget v0, v7, Lcom/facebook/ads/redexgen/X/XN;->A05:I

    invoke-interface {v11, v3, v12, v0}, Lcom/facebook/ads/redexgen/X/Bt;->readFully([BII)V

    .line 63869
    iget-object v3, v7, Lcom/facebook/ads/redexgen/X/XN;->A0P:Lcom/facebook/ads/redexgen/X/Hz;

    iget v0, v7, Lcom/facebook/ads/redexgen/X/XN;->A05:I

    invoke-interface {v5, v3, v0}, Lcom/facebook/ads/redexgen/X/C4;->AFR(Lcom/facebook/ads/redexgen/X/Hz;I)V

    .line 63870
    iget v3, v7, Lcom/facebook/ads/redexgen/X/XN;->A05:I

    .line 63871
    .local v3, "writtenBytes":I
    iget-object v0, v7, Lcom/facebook/ads/redexgen/X/XN;->A0P:Lcom/facebook/ads/redexgen/X/Hz;

    iget-object v12, v0, Lcom/facebook/ads/redexgen/X/Hz;->A00:[B

    iget-object v0, v7, Lcom/facebook/ads/redexgen/X/XN;->A0P:Lcom/facebook/ads/redexgen/X/Hz;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/Hz;->A07()I

    move-result v0

    invoke-static {v12, v0}, Lcom/facebook/ads/redexgen/X/Hv;->A02([BI)I

    move-result v15

    .line 63872
    .local v4, "unescapedLength":I
    iget-object v0, v7, Lcom/facebook/ads/redexgen/X/XN;->A0P:Lcom/facebook/ads/redexgen/X/Hz;

    move-object/from16 v16, v0

    iget-object v0, v6, Lcom/facebook/ads/redexgen/X/Ce;->A07:Lcom/facebook/ads/internal/exoplayer2/thirdparty/Format;

    iget-object v14, v0, Lcom/facebook/ads/internal/exoplayer2/thirdparty/Format;->A0O:Ljava/lang/String;

    const/16 v13, 0x2ae

    const/16 v12, 0xa

    const/4 v0, 0x6

    invoke-static {v13, v12, v0}, Lcom/facebook/ads/redexgen/X/XN;->A0A(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v0, v14}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v12

    move-object/from16 v0, v16

    invoke-virtual {v0, v12}, Lcom/facebook/ads/redexgen/X/Hz;->A0Y(I)V

    .line 63873
    iget-object v12, v7, Lcom/facebook/ads/redexgen/X/XN;->A0P:Lcom/facebook/ads/redexgen/X/Hz;

    move v0, v15

    invoke-virtual {v12, v0}, Lcom/facebook/ads/redexgen/X/Hz;->A0X(I)V

    .line 63874
    iget-object v12, v7, Lcom/facebook/ads/redexgen/X/XN;->A0P:Lcom/facebook/ads/redexgen/X/Hz;

    iget-object v0, v7, Lcom/facebook/ads/redexgen/X/XN;->A0H:[Lcom/facebook/ads/redexgen/X/C4;

    invoke-static {v1, v2, v12, v0}, Lcom/facebook/ads/redexgen/X/Fy;->A03(JLcom/facebook/ads/redexgen/X/Hz;[Lcom/facebook/ads/redexgen/X/C4;)V

    .line 63875
    .end local v4    # "unescapedLength":I
    .restart local v3    # "writtenBytes":I
    :goto_4
    iget v0, v7, Lcom/facebook/ads/redexgen/X/XN;->A04:I

    add-int/2addr v0, v3

    iput v0, v7, Lcom/facebook/ads/redexgen/X/XN;->A04:I

    .line 63876
    iget v0, v7, Lcom/facebook/ads/redexgen/X/XN;->A05:I

    sub-int/2addr v0, v3

    iput v0, v7, Lcom/facebook/ads/redexgen/X/XN;->A05:I

    .line 63877
    .end local v3    # "writtenBytes":I
    const/4 v14, 0x4

    const/4 v13, 0x1

    const/4 v12, 0x0

    goto/16 :goto_1

    .line 63878
    .end local v3
    :cond_d
    const/4 v0, 0x0

    invoke-interface {v5, v11, v15, v0}, Lcom/facebook/ads/redexgen/X/C4;->AFQ(Lcom/facebook/ads/redexgen/X/Bt;IZ)I

    move-result v3

    goto :goto_4

    :cond_e
    sget-object v2, Lcom/facebook/ads/redexgen/X/XN;->A0Y:[Ljava/lang/String;

    const-string v1, "M6XHB"

    const/4 v0, 0x0

    aput-object v1, v2, v0

    iget-object v6, v3, Lcom/facebook/ads/redexgen/X/CV;->A05:Lcom/facebook/ads/redexgen/X/Ce;

    .line 63879
    .local v8, "track":Lcom/facebook/ads/redexgen/X/Ce;
    iget-object v0, v7, Lcom/facebook/ads/redexgen/X/XN;->A0D:Lcom/facebook/ads/redexgen/X/CV;

    iget-object v5, v0, Lcom/facebook/ads/redexgen/X/CV;->A06:Lcom/facebook/ads/redexgen/X/C4;

    .line 63880
    .local v9, "output":Lcom/facebook/ads/redexgen/X/C4;
    iget-object v0, v7, Lcom/facebook/ads/redexgen/X/XN;->A0D:Lcom/facebook/ads/redexgen/X/CV;

    iget v4, v0, Lcom/facebook/ads/redexgen/X/CV;->A01:I

    .line 63881
    .local v15, "sampleIndex":I
    invoke-virtual {v10, v4}, Lcom/facebook/ads/redexgen/X/Cg;->A00(I)J

    move-result-wide v1

    const-wide/16 v8, 0x3e8

    mul-long/2addr v1, v8

    .line 63882
    .local v10, "sampleTimeUs":J
    iget-object v0, v7, Lcom/facebook/ads/redexgen/X/XN;->A0S:Lcom/facebook/ads/redexgen/X/IB;

    if-eqz v0, :cond_9

    goto/16 :goto_0

    .line 63883
    :cond_f
    :goto_5
    iget v0, v7, Lcom/facebook/ads/redexgen/X/XN;->A04:I

    iget v3, v7, Lcom/facebook/ads/redexgen/X/XN;->A06:I

    if-ge v0, v3, :cond_10

    .line 63884
    sub-int/2addr v3, v0

    const/4 v0, 0x0

    invoke-interface {v5, v11, v3, v0}, Lcom/facebook/ads/redexgen/X/C4;->AFQ(Lcom/facebook/ads/redexgen/X/Bt;IZ)I

    move-result v3

    .line 63885
    .local v4, "writtenBytes":I
    iget v0, v7, Lcom/facebook/ads/redexgen/X/XN;->A04:I

    add-int/2addr v0, v3

    iput v0, v7, Lcom/facebook/ads/redexgen/X/XN;->A04:I

    .line 63886
    .end local v4    # "writtenBytes":I
    goto :goto_5

    .line 63887
    :cond_10
    iget-object v0, v10, Lcom/facebook/ads/redexgen/X/Cg;->A0I:[Z

    aget-boolean v11, v0, v4

    .line 63888
    .local v3, "sampleFlags":I
    const/4 v8, 0x0

    .line 63889
    .local v4, "cryptoData":Lcom/facebook/ads/redexgen/X/C3;
    iget-boolean v0, v10, Lcom/facebook/ads/redexgen/X/Cg;->A0A:Z

    if-eqz v0, :cond_11

    .line 63890
    const/high16 v0, 0x40000000    # 2.0f

    or-int/2addr v11, v0

    .line 63891
    iget-object v0, v10, Lcom/facebook/ads/redexgen/X/Cg;->A08:Lcom/facebook/ads/redexgen/X/Cf;

    if-eqz v0, :cond_13

    .line 63892
    iget-object v0, v10, Lcom/facebook/ads/redexgen/X/Cg;->A08:Lcom/facebook/ads/redexgen/X/Cf;

    .line 63893
    .local v5, "encryptionBox":Lcom/facebook/ads/redexgen/X/Cf;
    :goto_6
    iget-object v8, v0, Lcom/facebook/ads/redexgen/X/Cf;->A01:Lcom/facebook/ads/redexgen/X/C3;

    .line 63894
    .end local v5    # "encryptionBox":Lcom/facebook/ads/redexgen/X/Cf;
    :cond_11
    iget v0, v7, Lcom/facebook/ads/redexgen/X/XN;->A06:I

    const/4 v13, 0x0

    move-wide v3, v1

    .end local v13    # "sampleTimeUs":J
    .local v17, "sampleTimeUs":J
    .end local v15    # "sampleIndex":I
    .local v5, "sampleIndex":I
    move-wide v9, v1

    move v12, v0

    move-object v14, v8

    move-object v8, v5

    invoke-interface/range {v8 .. v14}, Lcom/facebook/ads/redexgen/X/C4;->AFS(JIIILcom/facebook/ads/redexgen/X/C3;)V

    .line 63895
    .end local v17    # "sampleTimeUs":J
    .local v10, "sampleTimeUs":J
    invoke-direct {v7, v3, v4}, Lcom/facebook/ads/redexgen/X/XN;->A0E(J)V

    .line 63896
    iget-object v0, v7, Lcom/facebook/ads/redexgen/X/XN;->A0D:Lcom/facebook/ads/redexgen/X/CV;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/CV;->A08()Z

    move-result v0

    if-nez v0, :cond_12

    .line 63897
    const/4 v0, 0x0

    iput-object v0, v7, Lcom/facebook/ads/redexgen/X/XN;->A0D:Lcom/facebook/ads/redexgen/X/CV;

    .line 63898
    :cond_12
    const/4 v0, 0x3

    iput v0, v7, Lcom/facebook/ads/redexgen/X/XN;->A02:I

    .line 63899
    const/4 v0, 0x1

    return v0

    .line 63900
    :cond_13
    iget-object v0, v10, Lcom/facebook/ads/redexgen/X/Cg;->A07:Lcom/facebook/ads/redexgen/X/CP;

    iget v0, v0, Lcom/facebook/ads/redexgen/X/CP;->A02:I

    invoke-virtual {v6, v0}, Lcom/facebook/ads/redexgen/X/Ce;->A00(I)Lcom/facebook/ads/redexgen/X/Cf;

    move-result-object v0

    goto :goto_6
.end method


# virtual methods
.method public final A8o(Lcom/facebook/ads/redexgen/X/Bu;)V
    .locals 4

    .line 63901
    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/XN;->A0C:Lcom/facebook/ads/redexgen/X/Bu;

    .line 63902
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XN;->A0N:Lcom/facebook/ads/redexgen/X/Ce;

    if-eqz v0, :cond_0

    .line 63903
    iget v0, v0, Lcom/facebook/ads/redexgen/X/Ce;->A03:I

    const/4 v3, 0x0

    invoke-interface {p1, v3, v0}, Lcom/facebook/ads/redexgen/X/Bu;->AGi(II)Lcom/facebook/ads/redexgen/X/C4;

    move-result-object v0

    new-instance v2, Lcom/facebook/ads/redexgen/X/CV;

    invoke-direct {v2, v0}, Lcom/facebook/ads/redexgen/X/CV;-><init>(Lcom/facebook/ads/redexgen/X/C4;)V

    .line 63904
    .local v0, "bundle":Lcom/facebook/ads/redexgen/X/CV;
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/XN;->A0N:Lcom/facebook/ads/redexgen/X/Ce;

    new-instance v0, Lcom/facebook/ads/redexgen/X/CP;

    invoke-direct {v0, v3, v3, v3, v3}, Lcom/facebook/ads/redexgen/X/CP;-><init>(IIII)V

    invoke-virtual {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/CV;->A07(Lcom/facebook/ads/redexgen/X/Ce;Lcom/facebook/ads/redexgen/X/CP;)V

    .line 63905
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XN;->A0K:Landroid/util/SparseArray;

    invoke-virtual {v0, v3, v2}, Landroid/util/SparseArray;->put(ILjava/lang/Object;)V

    .line 63906
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/XN;->A0C()V

    .line 63907
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XN;->A0C:Lcom/facebook/ads/redexgen/X/Bu;

    invoke-interface {v0}, Lcom/facebook/ads/redexgen/X/Bu;->A5Y()V

    .line 63908
    .end local v0    # "bundle":Lcom/facebook/ads/redexgen/X/CV;
    :cond_0
    return-void
.end method

.method public final AEH(Lcom/facebook/ads/redexgen/X/Bt;Lcom/facebook/ads/redexgen/X/Bz;)I
    .locals 4
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;,
            Ljava/lang/InterruptedException;
        }
    .end annotation

    .line 63909
    :cond_0
    :goto_0
    iget v3, p0, Lcom/facebook/ads/redexgen/X/XN;->A02:I

    sget-object v1, Lcom/facebook/ads/redexgen/X/XN;->A0Y:[Ljava/lang/String;

    const/4 v0, 0x6

    aget-object v0, v1, v0

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v1

    const/16 v0, 0x11

    if-eq v1, v0, :cond_1

    sget-object v2, Lcom/facebook/ads/redexgen/X/XN;->A0Y:[Ljava/lang/String;

    const-string v1, "Adcvl3OLLWBEEjCvRjB7l1tHvWvuGk"

    const/4 v0, 0x6

    aput-object v1, v2, v0

    packed-switch v3, :pswitch_data_0

    .line 63910
    invoke-direct {p0, p1}, Lcom/facebook/ads/redexgen/X/XN;->A0Z(Lcom/facebook/ads/redexgen/X/Bt;)Z

    move-result v0

    if-eqz v0, :cond_0

    .line 63911
    const/4 v0, 0x0

    return v0

    .line 63912
    :pswitch_0
    invoke-direct {p0, p1}, Lcom/facebook/ads/redexgen/X/XN;->A0H(Lcom/facebook/ads/redexgen/X/Bt;)V

    .line 63913
    goto :goto_0

    .line 63914
    :pswitch_1
    invoke-direct {p0, p1}, Lcom/facebook/ads/redexgen/X/XN;->A0G(Lcom/facebook/ads/redexgen/X/Bt;)V

    .line 63915
    goto :goto_0

    .line 63916
    :pswitch_2
    invoke-direct {p0, p1}, Lcom/facebook/ads/redexgen/X/XN;->A0Y(Lcom/facebook/ads/redexgen/X/Bt;)Z

    move-result v0

    if-nez v0, :cond_0

    .line 63917
    const/4 v0, -0x1

    return v0

    .line 63918
    :cond_1
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    nop

    :pswitch_data_0
    .packed-switch 0x0
        :pswitch_2
        :pswitch_1
        :pswitch_0
    .end packed-switch
.end method

.method public final AFh(JJ)V
    .locals 3

    .line 63919
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XN;->A0K:Landroid/util/SparseArray;

    invoke-virtual {v0}, Landroid/util/SparseArray;->size()I

    move-result v2

    .line 63920
    .local v0, "trackCount":I
    const/4 v1, 0x0

    .local v1, "i":I
    :goto_0
    if-ge v1, v2, :cond_0

    .line 63921
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XN;->A0K:Landroid/util/SparseArray;

    invoke-virtual {v0, v1}, Landroid/util/SparseArray;->valueAt(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/facebook/ads/redexgen/X/CV;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/CV;->A04()V

    .line 63922
    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    .line 63923
    .end local v1    # "i":I
    :cond_0
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XN;->A0U:Ljava/util/ArrayDeque;

    invoke-virtual {v0}, Ljava/util/ArrayDeque;->clear()V

    .line 63924
    const/4 v0, 0x0

    iput v0, p0, Lcom/facebook/ads/redexgen/X/XN;->A03:I

    .line 63925
    iput-wide p3, p0, Lcom/facebook/ads/redexgen/X/XN;->A0A:J

    .line 63926
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XN;->A0T:Ljava/util/ArrayDeque;

    invoke-virtual {v0}, Ljava/util/ArrayDeque;->clear()V

    .line 63927
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/XN;->A0B()V

    .line 63928
    return-void
.end method

.method public final AGR(Lcom/facebook/ads/redexgen/X/Bt;)Z
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;,
            Ljava/lang/InterruptedException;
        }
    .end annotation

    .line 63929
    invoke-static {p1}, Lcom/facebook/ads/redexgen/X/Cc;->A03(Lcom/facebook/ads/redexgen/X/Bt;)Z

    move-result v0

    return v0
.end method
