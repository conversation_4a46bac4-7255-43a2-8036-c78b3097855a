.class public final Lcom/facebook/ads/redexgen/X/XX;
.super Ljava/lang/Object;
.source ""

# interfaces
.implements Lcom/facebook/ads/redexgen/X/Bs;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/facebook/ads/redexgen/X/CE;,
        Lcom/facebook/ads/redexgen/X/CF;,
        Lcom/facebook/ads/redexgen/X/XY;,
        Lcom/facebook/ads/internal/exoplayer2/thirdparty/extractor/mkv/MatroskaExtractor$Flags;
    }
.end annotation


# static fields
.field public static A0p:[B

.field public static A0q:[Ljava/lang/String;

.field public static final A0r:Lcom/facebook/ads/redexgen/X/Bv;

.field public static final A0s:Ljava/util/UUID;

.field public static final A0t:[B

.field public static final A0u:[B

.field public static final A0v:[B

.field public static final A0w:[B

.field public static final A0x:[B


# instance fields
.field public A00:B

.field public A01:I

.field public A02:I

.field public A03:I

.field public A04:I

.field public A05:I

.field public A06:I

.field public A07:I

.field public A08:I

.field public A09:I

.field public A0A:I

.field public A0B:I

.field public A0C:J

.field public A0D:J

.field public A0E:J

.field public A0F:J

.field public A0G:J

.field public A0H:J

.field public A0I:J

.field public A0J:J

.field public A0K:J

.field public A0L:J

.field public A0M:J

.field public A0N:Lcom/facebook/ads/redexgen/X/Bu;

.field public A0O:Lcom/facebook/ads/redexgen/X/CE;

.field public A0P:Lcom/facebook/ads/redexgen/X/Hp;

.field public A0Q:Lcom/facebook/ads/redexgen/X/Hp;

.field public A0R:Ljava/nio/ByteBuffer;

.field public A0S:Z

.field public A0T:Z

.field public A0U:Z

.field public A0V:Z

.field public A0W:Z

.field public A0X:Z

.field public A0Y:Z

.field public A0Z:Z

.field public A0a:Z

.field public A0b:[I

.field public final A0c:Landroid/util/SparseArray;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Landroid/util/SparseArray<",
            "Lcom/facebook/ads/redexgen/X/CE;",
            ">;"
        }
    .end annotation
.end field

.field public final A0d:Lcom/facebook/ads/redexgen/X/CA;

.field public final A0e:Lcom/facebook/ads/redexgen/X/CH;

.field public final A0f:Lcom/facebook/ads/redexgen/X/Hz;

.field public final A0g:Lcom/facebook/ads/redexgen/X/Hz;

.field public final A0h:Lcom/facebook/ads/redexgen/X/Hz;

.field public final A0i:Lcom/facebook/ads/redexgen/X/Hz;

.field public final A0j:Lcom/facebook/ads/redexgen/X/Hz;

.field public final A0k:Lcom/facebook/ads/redexgen/X/Hz;

.field public final A0l:Lcom/facebook/ads/redexgen/X/Hz;

.field public final A0m:Lcom/facebook/ads/redexgen/X/Hz;

.field public final A0n:Lcom/facebook/ads/redexgen/X/Hz;

.field public final A0o:Z


# direct methods
.method public static constructor <clinit>()V
    .locals 5

    .line 2551
    const/16 v0, 0x8

    new-array v2, v0, [Ljava/lang/String;

    const/4 v1, 0x0

    const-string v0, "WhKu7pkuAeXCsVE8uRhv3EZwgBp9riSc"

    aput-object v0, v2, v1

    const/4 v1, 0x1

    const-string v0, "wrDeNyGLCWUv14ZggE2zPWUzu5gjoOIg"

    aput-object v0, v2, v1

    const/4 v1, 0x2

    const-string v0, "xUs1VEgTJlmvEesGlKYim2mGX89Mn6Ap"

    aput-object v0, v2, v1

    const/4 v1, 0x3

    const-string v0, "W8YnXYO8xf4ClNYvDZVYWX"

    aput-object v0, v2, v1

    const/4 v1, 0x4

    const-string v0, "B88t6KSt5rHqx0X"

    aput-object v0, v2, v1

    const/4 v1, 0x5

    const-string v0, "mP1RKkMa67OSAD0gBpbGTYhDs7d4UNMk"

    aput-object v0, v2, v1

    const/4 v1, 0x6

    const-string v0, "rmkWP1gG3PZnRTV6Vu72xnpKfVt"

    aput-object v0, v2, v1

    const/4 v1, 0x7

    const-string v0, "q3NSDD9bEbAX3BTi5XmjKvgCV917aeoR"

    aput-object v0, v2, v1

    sput-object v2, Lcom/facebook/ads/redexgen/X/XX;->A0q:[Ljava/lang/String;

    invoke-static {}, Lcom/facebook/ads/redexgen/X/XX;->A06()V

    new-instance v0, Lcom/facebook/ads/redexgen/X/XZ;

    invoke-direct {v0}, Lcom/facebook/ads/redexgen/X/XZ;-><init>()V

    sput-object v0, Lcom/facebook/ads/redexgen/X/XX;->A0r:Lcom/facebook/ads/redexgen/X/Bv;

    .line 2552
    const/16 v3, 0x20

    new-array v0, v3, [B

    fill-array-data v0, :array_0

    sput-object v0, Lcom/facebook/ads/redexgen/X/XX;->A0w:[B

    .line 2553
    const/16 v0, 0xc

    new-array v0, v0, [B

    fill-array-data v0, :array_1

    sput-object v0, Lcom/facebook/ads/redexgen/X/XX;->A0x:[B

    .line 2554
    const/16 v2, 0x21b

    const/16 v1, 0x5a

    const/16 v0, 0x53

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/XX;->A03(III)Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/IF;->A0i(Ljava/lang/String;)[B

    move-result-object v0

    sput-object v0, Lcom/facebook/ads/redexgen/X/XX;->A0t:[B

    .line 2555
    new-array v0, v3, [B

    fill-array-data v0, :array_2

    sput-object v0, Lcom/facebook/ads/redexgen/X/XX;->A0u:[B

    .line 2556
    const/16 v0, 0xa

    new-array v0, v0, [B

    fill-array-data v0, :array_3

    sput-object v0, Lcom/facebook/ads/redexgen/X/XX;->A0v:[B

    .line 2557
    const-wide v3, 0x100000000001000L

    const-wide v1, -0x7fffff55ffc7648fL    # -3.607411173533E-312

    new-instance v0, Ljava/util/UUID;

    invoke-direct {v0, v3, v4, v1, v2}, Ljava/util/UUID;-><init>(JJ)V

    sput-object v0, Lcom/facebook/ads/redexgen/X/XX;->A0s:Ljava/util/UUID;

    return-void

    :array_0
    .array-data 1
        0x31t
        0xat
        0x30t
        0x30t
        0x3at
        0x30t
        0x30t
        0x3at
        0x30t
        0x30t
        0x2ct
        0x30t
        0x30t
        0x30t
        0x20t
        0x2dt
        0x2dt
        0x3et
        0x20t
        0x30t
        0x30t
        0x3at
        0x30t
        0x30t
        0x3at
        0x30t
        0x30t
        0x2ct
        0x30t
        0x30t
        0x30t
        0xat
    .end array-data

    :array_1
    .array-data 1
        0x20t
        0x20t
        0x20t
        0x20t
        0x20t
        0x20t
        0x20t
        0x20t
        0x20t
        0x20t
        0x20t
        0x20t
    .end array-data

    :array_2
    .array-data 1
        0x44t
        0x69t
        0x61t
        0x6ct
        0x6ft
        0x67t
        0x75t
        0x65t
        0x3at
        0x20t
        0x30t
        0x3at
        0x30t
        0x30t
        0x3at
        0x30t
        0x30t
        0x3at
        0x30t
        0x30t
        0x2ct
        0x30t
        0x3at
        0x30t
        0x30t
        0x3at
        0x30t
        0x30t
        0x3at
        0x30t
        0x30t
        0x2ct
    .end array-data

    :array_3
    .array-data 1
        0x20t
        0x20t
        0x20t
        0x20t
        0x20t
        0x20t
        0x20t
        0x20t
        0x20t
        0x20t
    .end array-data
.end method

.method public constructor <init>()V
    .locals 1

    .line 64143
    const/4 v0, 0x0

    invoke-direct {p0, v0}, Lcom/facebook/ads/redexgen/X/XX;-><init>(I)V

    .line 64144
    return-void
.end method

.method public constructor <init>(I)V
    .locals 1

    .line 64145
    new-instance v0, Lcom/facebook/ads/redexgen/X/Xa;

    invoke-direct {v0}, Lcom/facebook/ads/redexgen/X/Xa;-><init>()V

    invoke-direct {p0, v0, p1}, Lcom/facebook/ads/redexgen/X/XX;-><init>(Lcom/facebook/ads/redexgen/X/CA;I)V

    .line 64146
    return-void
.end method

.method public constructor <init>(Lcom/facebook/ads/redexgen/X/CA;I)V
    .locals 4

    .line 64147
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 64148
    const-wide/16 v2, -0x1

    iput-wide v2, p0, Lcom/facebook/ads/redexgen/X/XX;->A0K:J

    .line 64149
    const-wide v0, -0x7fffffffffffffffL    # -4.9E-324

    iput-wide v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0M:J

    .line 64150
    iput-wide v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0G:J

    .line 64151
    iput-wide v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0H:J

    .line 64152
    iput-wide v2, p0, Lcom/facebook/ads/redexgen/X/XX;->A0F:J

    .line 64153
    iput-wide v2, p0, Lcom/facebook/ads/redexgen/X/XX;->A0J:J

    .line 64154
    iput-wide v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0E:J

    .line 64155
    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/XX;->A0d:Lcom/facebook/ads/redexgen/X/CA;

    .line 64156
    const/4 v1, 0x0

    new-instance v0, Lcom/facebook/ads/redexgen/X/XY;

    invoke-direct {v0, p0, v1}, Lcom/facebook/ads/redexgen/X/XY;-><init>(Lcom/facebook/ads/redexgen/X/XX;Lcom/facebook/ads/redexgen/X/XZ;)V

    invoke-interface {p1, v0}, Lcom/facebook/ads/redexgen/X/CA;->A8p(Lcom/facebook/ads/redexgen/X/CC;)V

    .line 64157
    and-int/lit8 v0, p2, 0x1

    if-nez v0, :cond_0

    const/4 v0, 0x1

    :goto_0
    iput-boolean v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0o:Z

    .line 64158
    new-instance v0, Lcom/facebook/ads/redexgen/X/CH;

    invoke-direct {v0}, Lcom/facebook/ads/redexgen/X/CH;-><init>()V

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0e:Lcom/facebook/ads/redexgen/X/CH;

    .line 64159
    new-instance v0, Landroid/util/SparseArray;

    invoke-direct {v0}, Landroid/util/SparseArray;-><init>()V

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0c:Landroid/util/SparseArray;

    .line 64160
    const/4 v2, 0x4

    new-instance v0, Lcom/facebook/ads/redexgen/X/Hz;

    invoke-direct {v0, v2}, Lcom/facebook/ads/redexgen/X/Hz;-><init>(I)V

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0k:Lcom/facebook/ads/redexgen/X/Hz;

    .line 64161
    invoke-static {v2}, Ljava/nio/ByteBuffer;->allocate(I)Ljava/nio/ByteBuffer;

    move-result-object v1

    const/4 v0, -0x1

    invoke-virtual {v1, v0}, Ljava/nio/ByteBuffer;->putInt(I)Ljava/nio/ByteBuffer;

    move-result-object v0

    invoke-virtual {v0}, Ljava/nio/ByteBuffer;->array()[B

    move-result-object v1

    new-instance v0, Lcom/facebook/ads/redexgen/X/Hz;

    invoke-direct {v0, v1}, Lcom/facebook/ads/redexgen/X/Hz;-><init>([B)V

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0n:Lcom/facebook/ads/redexgen/X/Hz;

    .line 64162
    new-instance v0, Lcom/facebook/ads/redexgen/X/Hz;

    invoke-direct {v0, v2}, Lcom/facebook/ads/redexgen/X/Hz;-><init>(I)V

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0l:Lcom/facebook/ads/redexgen/X/Hz;

    .line 64163
    sget-object v1, Lcom/facebook/ads/redexgen/X/Hv;->A03:[B

    new-instance v0, Lcom/facebook/ads/redexgen/X/Hz;

    invoke-direct {v0, v1}, Lcom/facebook/ads/redexgen/X/Hz;-><init>([B)V

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0i:Lcom/facebook/ads/redexgen/X/Hz;

    .line 64164
    new-instance v0, Lcom/facebook/ads/redexgen/X/Hz;

    invoke-direct {v0, v2}, Lcom/facebook/ads/redexgen/X/Hz;-><init>(I)V

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0h:Lcom/facebook/ads/redexgen/X/Hz;

    .line 64165
    new-instance v0, Lcom/facebook/ads/redexgen/X/Hz;

    invoke-direct {v0}, Lcom/facebook/ads/redexgen/X/Hz;-><init>()V

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0j:Lcom/facebook/ads/redexgen/X/Hz;

    .line 64166
    new-instance v0, Lcom/facebook/ads/redexgen/X/Hz;

    invoke-direct {v0}, Lcom/facebook/ads/redexgen/X/Hz;-><init>()V

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0m:Lcom/facebook/ads/redexgen/X/Hz;

    .line 64167
    const/16 v1, 0x8

    new-instance v0, Lcom/facebook/ads/redexgen/X/Hz;

    invoke-direct {v0, v1}, Lcom/facebook/ads/redexgen/X/Hz;-><init>(I)V

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0f:Lcom/facebook/ads/redexgen/X/Hz;

    .line 64168
    new-instance v0, Lcom/facebook/ads/redexgen/X/Hz;

    invoke-direct {v0}, Lcom/facebook/ads/redexgen/X/Hz;-><init>()V

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0g:Lcom/facebook/ads/redexgen/X/Hz;

    .line 64169
    return-void

    .line 64170
    :cond_0
    const/4 v0, 0x0

    goto :goto_0
.end method

.method private A00(Lcom/facebook/ads/redexgen/X/Bt;Lcom/facebook/ads/redexgen/X/C4;I)I
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;,
            Ljava/lang/InterruptedException;
        }
    .end annotation

    .line 64171
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0j:Lcom/facebook/ads/redexgen/X/Hz;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/Hz;->A04()I

    move-result v0

    .line 64172
    .local v0, "strippedBytesLeft":I
    if-lez v0, :cond_0

    .line 64173
    invoke-static {p3, v0}, Ljava/lang/Math;->min(II)I

    move-result v1

    .line 64174
    .local v1, "bytesRead":I
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0j:Lcom/facebook/ads/redexgen/X/Hz;

    invoke-interface {p2, v0, v1}, Lcom/facebook/ads/redexgen/X/C4;->AFR(Lcom/facebook/ads/redexgen/X/Hz;I)V

    .line 64175
    .restart local v1    # "bytesRead":I
    :goto_0
    iget v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A07:I

    add-int/2addr v0, v1

    iput v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A07:I

    .line 64176
    iget v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A08:I

    add-int/2addr v0, v1

    iput v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A08:I

    .line 64177
    return v1

    .line 64178
    .end local v1    # "bytesRead":I
    :cond_0
    const/4 v0, 0x0

    invoke-interface {p2, p1, p3, v0}, Lcom/facebook/ads/redexgen/X/C4;->AFQ(Lcom/facebook/ads/redexgen/X/Bt;IZ)I

    move-result v1

    goto :goto_0
.end method

.method private A01(J)J
    .locals 7
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lcom/facebook/ads/redexgen/X/9v;
        }
    .end annotation

    .line 64179
    iget-wide v3, p0, Lcom/facebook/ads/redexgen/X/XX;->A0M:J

    const-wide v1, -0x7fffffffffffffffL    # -4.9E-324

    cmp-long v0, v3, v1

    if-eqz v0, :cond_0

    .line 64180
    const-wide/16 v5, 0x3e8

    move-wide v1, p1

    invoke-static/range {v1 .. v6}, Lcom/facebook/ads/redexgen/X/IF;->A0F(JJJ)J

    move-result-wide v0

    return-wide v0

    .line 64181
    :cond_0
    const/16 v2, 0xbd

    const/16 v1, 0x36

    const/16 v0, 0x44

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/XX;->A03(III)Ljava/lang/String;

    move-result-object v1

    new-instance v0, Lcom/facebook/ads/redexgen/X/9v;

    invoke-direct {v0, v1}, Lcom/facebook/ads/redexgen/X/9v;-><init>(Ljava/lang/String;)V

    throw v0
.end method

.method private A02()Lcom/facebook/ads/redexgen/X/C1;
    .locals 11

    .line 64182
    iget-wide v3, p0, Lcom/facebook/ads/redexgen/X/XX;->A0K:J

    const-wide/16 v1, -0x1

    const/4 v8, 0x0

    cmp-long v0, v3, v1

    if-eqz v0, :cond_0

    iget-wide v3, p0, Lcom/facebook/ads/redexgen/X/XX;->A0H:J

    const-wide v1, -0x7fffffffffffffffL    # -4.9E-324

    cmp-long v0, v3, v1

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0Q:Lcom/facebook/ads/redexgen/X/Hp;

    if-eqz v0, :cond_0

    .line 64183
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/Hp;->A02()I

    move-result v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0P:Lcom/facebook/ads/redexgen/X/Hp;

    if-eqz v0, :cond_0

    .line 64184
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/Hp;->A02()I

    move-result v1

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0Q:Lcom/facebook/ads/redexgen/X/Hp;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/Hp;->A02()I

    move-result v0

    if-eq v1, v0, :cond_1

    .line 64185
    .end local v0
    .end local v1
    .end local v2
    .end local v3
    .end local v5
    :cond_0
    iput-object v8, p0, Lcom/facebook/ads/redexgen/X/XX;->A0Q:Lcom/facebook/ads/redexgen/X/Hp;

    .line 64186
    iput-object v8, p0, Lcom/facebook/ads/redexgen/X/XX;->A0P:Lcom/facebook/ads/redexgen/X/Hp;

    .line 64187
    iget-wide v1, p0, Lcom/facebook/ads/redexgen/X/XX;->A0H:J

    new-instance v0, Lcom/facebook/ads/redexgen/X/Xj;

    invoke-direct {v0, v1, v2}, Lcom/facebook/ads/redexgen/X/Xj;-><init>(J)V

    return-object v0

    .line 64188
    :cond_1
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0Q:Lcom/facebook/ads/redexgen/X/Hp;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/Hp;->A02()I

    move-result v7

    .line 64189
    .local v0, "cuePointsSize":I
    new-array v9, v7, [I

    .line 64190
    .local v1, "sizes":[I
    new-array v6, v7, [J

    .line 64191
    .local v2, "offsets":[J
    new-array v5, v7, [J

    .line 64192
    .local v3, "durationsUs":[J
    new-array v4, v7, [J

    .line 64193
    .local v5, "timesUs":[J
    const/4 v10, 0x0

    .local v6, "i":I
    :goto_0
    if-ge v10, v7, :cond_2

    .line 64194
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0Q:Lcom/facebook/ads/redexgen/X/Hp;

    invoke-virtual {v0, v10}, Lcom/facebook/ads/redexgen/X/Hp;->A03(I)J

    move-result-wide v0

    aput-wide v0, v4, v10

    .line 64195
    iget-wide v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0K:J

    iget-object v2, p0, Lcom/facebook/ads/redexgen/X/XX;->A0P:Lcom/facebook/ads/redexgen/X/Hp;

    invoke-virtual {v2, v10}, Lcom/facebook/ads/redexgen/X/Hp;->A03(I)J

    move-result-wide v2

    add-long/2addr v0, v2

    aput-wide v0, v6, v10

    .line 64196
    add-int/lit8 v10, v10, 0x1

    goto :goto_0

    .line 64197
    .end local v6    # "i":I
    :cond_2
    const/4 v10, 0x0

    .restart local v6    # "i":I
    :goto_1
    add-int/lit8 v0, v7, -0x1

    if-ge v10, v0, :cond_3

    .line 64198
    add-int/lit8 v0, v10, 0x1

    aget-wide v0, v6, v0

    aget-wide v2, v6, v10

    sub-long/2addr v0, v2

    long-to-int v2, v0

    aput v2, v9, v10

    .line 64199
    add-int/lit8 v0, v10, 0x1

    aget-wide v2, v4, v0

    aget-wide v0, v4, v10

    sub-long/2addr v2, v0

    aput-wide v2, v5, v10

    .line 64200
    add-int/lit8 v10, v10, 0x1

    goto :goto_1

    .line 64201
    .end local v6    # "i":I
    :cond_3
    add-int/lit8 v10, v7, -0x1

    iget-wide v2, p0, Lcom/facebook/ads/redexgen/X/XX;->A0K:J

    iget-wide v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0L:J

    add-long/2addr v2, v0

    add-int/lit8 v0, v7, -0x1

    aget-wide v0, v6, v0

    sub-long/2addr v2, v0

    long-to-int v0, v2

    aput v0, v9, v10

    .line 64202
    add-int/lit8 v10, v7, -0x1

    iget-wide v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0H:J

    add-int/lit8 v2, v7, -0x1

    aget-wide v2, v4, v2

    sub-long/2addr v0, v2

    aput-wide v0, v5, v10

    .line 64203
    iput-object v8, p0, Lcom/facebook/ads/redexgen/X/XX;->A0Q:Lcom/facebook/ads/redexgen/X/Hp;

    .line 64204
    iput-object v8, p0, Lcom/facebook/ads/redexgen/X/XX;->A0P:Lcom/facebook/ads/redexgen/X/Hp;

    .line 64205
    new-instance v0, Lcom/facebook/ads/redexgen/X/Xo;

    invoke-direct {v0, v9, v6, v5, v4}, Lcom/facebook/ads/redexgen/X/Xo;-><init>([I[J[J[J)V

    return-object v0
.end method

.method public static A03(III)Ljava/lang/String;
    .locals 2

    sget-object v1, Lcom/facebook/ads/redexgen/X/XX;->A0p:[B

    add-int v0, p0, p1

    invoke-static {v1, p0, v0}, Ljava/util/Arrays;->copyOfRange([BII)[B

    move-result-object p0

    const/4 v1, 0x0

    :goto_0
    array-length v0, p0

    if-ge v1, v0, :cond_0

    aget-byte v0, p0, v1

    sub-int/2addr v0, p2

    add-int/lit8 v0, v0, -0xc

    int-to-byte v0, v0

    aput-byte v0, p0, v1

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_0
    new-instance v0, Ljava/lang/String;

    invoke-direct {v0, p0}, Ljava/lang/String;-><init>([B)V

    return-object v0
.end method

.method public static synthetic A04()Ljava/util/UUID;
    .locals 1

    .line 64206
    sget-object v0, Lcom/facebook/ads/redexgen/X/XX;->A0s:Ljava/util/UUID;

    return-object v0
.end method

.method private A05()V
    .locals 1

    .line 64207
    const/4 v0, 0x0

    iput v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A07:I

    .line 64208
    iput v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A08:I

    .line 64209
    iput v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A09:I

    .line 64210
    iput-boolean v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0S:Z

    .line 64211
    iput-boolean v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0X:Z

    .line 64212
    iput-boolean v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0U:Z

    .line 64213
    iput v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0A:I

    .line 64214
    iput-byte v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A00:B

    .line 64215
    iput-boolean v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0T:Z

    .line 64216
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0j:Lcom/facebook/ads/redexgen/X/Hz;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/Hz;->A0V()V

    .line 64217
    return-void
.end method

.method public static A06()V
    .locals 1

    const/16 v0, 0x40e

    new-array v0, v0, [B

    fill-array-data v0, :array_0

    sput-object v0, Lcom/facebook/ads/redexgen/X/XX;->A0p:[B

    return-void

    :array_0
    .array-data 1
        0x41t
        -0x71t
        -0x70t
        -0x6bt
        0x41t
        -0x6ct
        -0x6at
        -0x6ft
        -0x6ft
        -0x70t
        -0x6dt
        -0x6bt
        -0x7at
        -0x7bt
        -0x62t
        -0x57t
        -0x56t
        -0x23t
        -0x4dt
        -0x62t
        -0x57t
        -0x55t
        -0x23t
        -0x4dt
        -0x62t
        -0x57t
        -0x55t
        -0x23t
        -0x4dt
        -0x62t
        -0x57t
        -0x55t
        -0x23t
        0x38t
        0x43t
        0x45t
        0x77t
        0x4dt
        0x38t
        0x43t
        0x45t
        0x77t
        0x4dt
        0x38t
        0x43t
        0x45t
        0x77t
        0x3ft
        0x38t
        0x43t
        0x46t
        0x77t
        -0x6bt
        -0x67t
        -0x59t
        -0x59t
        -0x47t
        -0x38t
        -0x38t
        -0x43t
        -0x3et
        -0x45t
        -0x39t
        -0x69t
        -0x43t
        -0x3ct
        -0x44t
        -0x47t
        -0x3at
        -0x5ft
        -0x3dt
        -0x48t
        -0x47t
        0x74t
        -0x35t
        -0x17t
        -0x35t
        -0x35t
        -0x33t
        -0x37t
        -0x19t
        -0x37t
        -0x35t
        -0x45t
        -0x51t
        -0x33t
        -0x4et
        -0x3et
        -0x3ft
        -0x75t
        -0x57t
        -0x72t
        -0x62t
        -0x63t
        0x79t
        -0x71t
        -0x5et
        -0x66t
        -0x64t
        -0x71t
        -0x63t
        -0x63t
        -0x5dt
        -0x3ft
        -0x5at
        -0x4at
        -0x4bt
        -0x6ft
        -0x52t
        -0x4ft
        -0x4bt
        -0x4bt
        -0x52t
        -0x59t
        -0x4bt
        -0x4bt
        -0x78t
        -0x5at
        -0x74t
        -0x78t
        -0x76t
        0x7at
        0x5et
        0x7ct
        0x63t
        0x69t
        0x5et
        0x60t
        -0x4dt
        -0x2ft
        -0x41t
        -0x3et
        -0x49t
        -0x47t
        -0x5ft
        -0x42t
        -0x5ct
        0x55t
        0x73t
        0x61t
        0x64t
        0x59t
        0x5bt
        0x43t
        0x60t
        0x47t
        0x74t
        -0x6et
        -0x80t
        -0x7at
        0x62t
        0x74t
        0x76t
        -0x80t
        -0x4dt
        -0x2ft
        -0x3ft
        -0x3et
        -0x39t
        -0x3bt
        -0x64t
        -0x46t
        -0x55t
        -0x62t
        -0x58t
        -0x76t
        -0x5ct
        -0x57t
        -0x51t
        -0x76t
        -0x59t
        -0x5ct
        -0x51t
        -0x75t
        -0x57t
        -0x62t
        -0x64t
        -0x61t
        -0x71t
        -0x6et
        -0x72t
        -0x5ct
        -0x3et
        -0x47t
        -0x4et
        -0x4bt
        -0x5bt
        -0x54t
        -0x4at
        -0x6dt
        -0x4ft
        -0x42t
        0x77t
        -0x3ct
        0x70t
        -0x3dt
        -0x4dt
        -0x4ft
        -0x44t
        -0x4bt
        0x70t
        -0x3ct
        -0x47t
        -0x43t
        -0x4bt
        -0x4dt
        -0x41t
        -0x4ct
        -0x4bt
        0x70t
        -0x40t
        -0x3et
        -0x47t
        -0x41t
        -0x3et
        0x70t
        -0x3ct
        -0x41t
        0x70t
        -0x3ct
        -0x47t
        -0x43t
        -0x4bt
        -0x4dt
        -0x41t
        -0x4ct
        -0x4bt
        -0x5dt
        -0x4dt
        -0x4ft
        -0x44t
        -0x4bt
        0x70t
        -0x4et
        -0x4bt
        -0x47t
        -0x42t
        -0x49t
        0x70t
        -0x3dt
        -0x4bt
        -0x3ct
        0x7et
        0x59t
        -0x7bt
        -0x7dt
        0x78t
        0x7ft
        -0x7ct
        0x7ft
        -0x7ct
        0x7dt
        0x36t
        0x7bt
        -0x7ct
        0x79t
        -0x78t
        -0x71t
        -0x7at
        -0x76t
        0x7ft
        -0x7bt
        -0x7ct
        0x36t
        0x77t
        -0x7ct
        0x7at
        0x36t
        0x79t
        -0x7bt
        -0x7dt
        -0x7at
        -0x78t
        0x7bt
        -0x77t
        -0x77t
        0x7ft
        -0x7bt
        -0x7ct
        0x36t
        0x7ft
        -0x77t
        0x36t
        -0x7ct
        -0x7bt
        -0x76t
        0x36t
        -0x77t
        -0x75t
        -0x7at
        -0x7at
        -0x7bt
        -0x78t
        -0x76t
        0x7bt
        0x7at
        -0x45t
        -0x19t
        -0x1at
        -0x14t
        -0x23t
        -0x1at
        -0x14t
        -0x45t
        -0x19t
        -0x1bt
        -0x18t
        -0x47t
        -0x1ct
        -0x21t
        -0x19t
        -0x68t
        -0x6ft
        -0x43t
        -0x44t
        -0x3et
        -0x4dt
        -0x44t
        -0x3et
        -0x6dt
        -0x44t
        -0x4ft
        -0x71t
        -0x46t
        -0x4bt
        -0x43t
        0x6et
        -0x59t
        -0x2dt
        -0x2et
        -0x28t
        -0x37t
        -0x2et
        -0x28t
        -0x57t
        -0x2et
        -0x39t
        -0x2dt
        -0x38t
        -0x33t
        -0x2et
        -0x35t
        -0x4dt
        -0x2at
        -0x38t
        -0x37t
        -0x2at
        -0x7ct
        0x78t
        -0x5ct
        -0x5dt
        -0x57t
        -0x66t
        -0x5dt
        -0x57t
        0x7at
        -0x5dt
        -0x68t
        -0x5ct
        -0x67t
        -0x62t
        -0x5dt
        -0x64t
        -0x78t
        -0x68t
        -0x5ct
        -0x5bt
        -0x66t
        0x55t
        -0x3bt
        -0x10t
        -0x1ct
        -0x2bt
        -0x6t
        -0xft
        -0x1at
        -0x5ft
        0x5et
        -0x77t
        0x7dt
        0x6et
        -0x6dt
        -0x76t
        0x7ft
        0x6ct
        0x7ft
        0x7bt
        0x7et
        0x70t
        0x7ft
        -0x74t
        -0x73t
        -0x7dt
        -0x77t
        -0x78t
        0x3at
        -0x44t
        -0x47t
        -0x3ct
        -0x3dt
        -0x69t
        -0x1dt
        -0x28t
        -0x26t
        -0x20t
        -0x1bt
        -0x22t
        -0x69t
        -0x16t
        -0x28t
        -0x1ct
        -0x19t
        -0x1dt
        -0x24t
        -0x69t
        -0x16t
        -0x20t
        -0xft
        -0x24t
        -0x69t
        -0x1at
        -0x14t
        -0x15t
        -0x69t
        -0x1at
        -0x23t
        -0x69t
        -0x17t
        -0x28t
        -0x1bt
        -0x22t
        -0x24t
        -0x5bt
        -0x4ft
        -0x52t
        -0x47t
        -0x48t
        -0x42t
        -0x2ft
        -0x33t
        -0x30t
        -0x3et
        -0x2ft
        -0x22t
        -0x21t
        -0x2bt
        -0x25t
        -0x26t
        -0x74t
        -0x3ct
        -0x13t
        -0x1et
        -0xft
        -0x8t
        -0x11t
        -0xdt
        -0x1ct
        -0x1dt
        -0x61t
        -0x2dt
        -0xft
        -0x20t
        -0x1et
        -0x16t
        -0x61t
        -0x1bt
        -0x12t
        -0xct
        -0x13t
        -0x1dt
        -0x61t
        -0x1ft
        -0xct
        -0xdt
        -0x61t
        -0x3et
        -0x12t
        -0x13t
        -0xdt
        -0x1ct
        -0x13t
        -0xdt
        -0x3ct
        -0x13t
        -0x1et
        -0x36t
        -0x1ct
        -0x8t
        -0x38t
        -0x3dt
        -0x61t
        -0xat
        -0x20t
        -0xet
        -0x61t
        -0x13t
        -0x12t
        -0xdt
        -0x61t
        -0x1bt
        -0x12t
        -0xct
        -0x13t
        -0x1dt
        0x67t
        -0x66t
        -0x6at
        -0x79t
        -0x70t
        -0x6bt
        -0x75t
        -0x6ft
        -0x70t
        0x42t
        -0x7ct
        -0x75t
        -0x6at
        0x42t
        -0x75t
        -0x6bt
        0x42t
        -0x6bt
        -0x79t
        -0x6at
        0x42t
        -0x75t
        -0x70t
        0x42t
        -0x6bt
        -0x75t
        -0x77t
        -0x70t
        -0x7dt
        -0x72t
        0x42t
        -0x7ct
        -0x65t
        -0x6at
        -0x79t
        -0x5bt
        -0x32t
        -0x2ft
        -0x34t
        -0x40t
        -0x2dt
        -0x67t
        0x7ft
        -0x4et
        -0x2dt
        -0x40t
        -0x2ft
        -0x2dt
        -0x75t
        0x7ft
        -0x5ct
        -0x33t
        -0x3dt
        -0x75t
        0x7ft
        -0x4ft
        -0x3ct
        -0x40t
        -0x3dt
        -0x52t
        -0x2ft
        -0x3dt
        -0x3ct
        -0x2ft
        -0x75t
        0x7ft
        -0x55t
        -0x40t
        -0x28t
        -0x3ct
        -0x2ft
        -0x75t
        0x7ft
        -0x4et
        -0x2dt
        -0x28t
        -0x35t
        -0x3ct
        -0x75t
        0x7ft
        -0x53t
        -0x40t
        -0x34t
        -0x3ct
        -0x75t
        0x7ft
        -0x54t
        -0x40t
        -0x2ft
        -0x3at
        -0x38t
        -0x33t
        -0x55t
        -0x75t
        0x7ft
        -0x54t
        -0x40t
        -0x2ft
        -0x3at
        -0x38t
        -0x33t
        -0x4ft
        -0x75t
        0x7ft
        -0x54t
        -0x40t
        -0x2ft
        -0x3at
        -0x38t
        -0x33t
        -0x4bt
        -0x75t
        0x7ft
        -0x5ct
        -0x3bt
        -0x3bt
        -0x3ct
        -0x3et
        -0x2dt
        -0x75t
        0x7ft
        -0x4dt
        -0x3ct
        -0x29t
        -0x2dt
        -0x63t
        -0x4et
        -0x4ct
        -0x46t
        -0x41t
        -0x48t
        0x71t
        -0x40t
        -0x41t
        -0x43t
        -0x36t
        0x71t
        -0x3ct
        -0x3at
        -0x3ft
        -0x3ft
        -0x40t
        -0x3dt
        -0x3bt
        -0x4at
        -0x4bt
        0x71t
        -0x46t
        -0x41t
        0x71t
        -0x5ct
        -0x46t
        -0x42t
        -0x3ft
        -0x43t
        -0x4at
        -0x6dt
        -0x43t
        -0x40t
        -0x4ct
        -0x44t
        -0x3ct
        0x7ft
        0x73t
        -0x79t
        -0x6ct
        -0x76t
        -0x79t
        -0x66t
        -0x6bt
        -0x68t
        -0x61t
        0x46t
        -0x75t
        -0x6et
        -0x75t
        -0x6dt
        -0x75t
        -0x6ct
        -0x66t
        0x46t
        0x79t
        -0x75t
        -0x75t
        -0x6ft
        0x6ft
        0x6at
        0x46t
        -0x6bt
        -0x68t
        0x46t
        0x79t
        -0x75t
        -0x75t
        -0x6ft
        0x76t
        -0x6bt
        -0x67t
        -0x71t
        -0x66t
        -0x71t
        -0x6bt
        -0x6ct
        0x46t
        -0x6ct
        -0x6bt
        -0x66t
        0x46t
        -0x74t
        -0x6bt
        -0x65t
        -0x6ct
        -0x76t
        -0x36t
        -0xet
        -0x17t
        -0xft
        -0x1at
        -0x13t
        -0x17t
        -0x1et
        -0x63t
        -0x30t
        -0x1et
        -0x1ct
        -0x16t
        -0x1et
        -0x15t
        -0xft
        -0x63t
        -0x1et
        -0x17t
        -0x1et
        -0x16t
        -0x1et
        -0x15t
        -0xft
        -0x10t
        -0x63t
        -0x15t
        -0x14t
        -0xft
        -0x63t
        -0x10t
        -0xet
        -0x13t
        -0x13t
        -0x14t
        -0x11t
        -0xft
        -0x1et
        -0x1ft
        -0x66t
        -0x45t
        0x6ct
        -0x3et
        -0x53t
        -0x48t
        -0x4bt
        -0x50t
        0x6ct
        -0x40t
        -0x42t
        -0x53t
        -0x51t
        -0x49t
        -0x41t
        0x6ct
        -0x3dt
        -0x4ft
        -0x42t
        -0x4ft
        0x6ct
        -0x4et
        -0x45t
        -0x3ft
        -0x46t
        -0x50t
        0x77t
        -0x68t
        0x49t
        -0x61t
        -0x76t
        -0x6bt
        -0x6et
        -0x73t
        0x49t
        -0x61t
        -0x76t
        -0x65t
        -0x6et
        -0x69t
        -0x63t
        0x49t
        -0x6bt
        -0x72t
        -0x69t
        -0x70t
        -0x63t
        -0x6ft
        0x49t
        -0x6at
        -0x76t
        -0x64t
        -0x6ct
        0x49t
        -0x71t
        -0x68t
        -0x62t
        -0x69t
        -0x73t
        0x70t
        0x7ct
        0x61t
        0x73t
        0x5ft
        0x70t
        0x72t
        0x5ft
        -0x2ct
        -0x20t
        -0x37t
        -0x3bt
        -0x32t
        -0x29t
        -0x50t
        -0x2ft
        -0x38t
        -0x2ct
        -0x68t
        -0x5ct
        -0x67t
        -0x76t
        -0x63t
        -0x67t
        0x74t
        -0x7at
        -0x68t
        -0x68t
        -0x48t
        -0x3ct
        -0x47t
        -0x56t
        -0x43t
        -0x47t
        -0x6ct
        -0x46t
        -0x47t
        -0x55t
        -0x63t
        -0x2at
        -0x1et
        -0x27t
        -0x2et
        -0x3bt
        -0x2at
        -0x28t
        -0x3bt
        0x68t
        -0x7ft
        0x78t
        -0x75t
        -0x7dt
        0x78t
        0x76t
        -0x79t
        0x78t
        0x77t
        0x33t
        0x7ct
        0x77t
        0x4dt
        0x33t
        0x7ft
        -0x68t
        -0x71t
        -0x5et
        -0x66t
        -0x71t
        -0x73t
        -0x62t
        -0x71t
        -0x72t
        0x4at
        -0x6at
        -0x75t
        -0x73t
        -0x6dt
        -0x68t
        -0x6ft
        0x4at
        -0x60t
        -0x75t
        -0x6at
        -0x61t
        -0x71t
        0x64t
        0x4at
        -0x56t
        -0x4dt
        -0x5ft
        -0x5ct
        -0x67t
        -0x65t
        -0x7at
        -0x26t
        -0x1dt
        -0x2ft
        -0x2ct
        -0x37t
        -0x35t
        -0x48t
        -0x4dt
        -0x33t
        -0x29t
        -0x2dt
        -0x4dt
        -0x3bt
        -0x2ct
        -0x48t
        -0x3ft
        -0x51t
        -0x4et
        -0x59t
        -0x57t
        -0x6at
        -0x6ft
        -0x55t
        -0x4bt
        -0x4ft
        -0x6ft
        -0x5dt
        -0x4bt
        -0x4et
        0x7at
        -0x7dt
        0x71t
        0x74t
        0x69t
        0x6bt
        0x58t
        0x53t
        0x6dt
        0x77t
        0x73t
        0x53t
        0x65t
        0x7at
        0x67t
        -0x35t
        -0x2ct
        -0x3et
        -0x3bt
        -0x46t
        -0x44t
        -0x57t
        -0x5ct
        -0x42t
        -0x38t
        -0x3ct
        -0x5ct
        -0x38t
        -0x3bt
        0x74t
        0x7dt
        0x6bt
        0x6et
        0x63t
        0x65t
        0x66t
        0x4dt
        0x67t
        0x71t
        0x6dt
        0x4dt
        0x66t
        0x63t
        0x74t
        0x61t
        -0x2ft
        -0x26t
        -0x38t
        -0x32t
        -0x56t
        -0x2ft
        -0x3ft
        -0x2et
        -0x56t
        -0x3ft
        -0x36t
        -0x30t
        -0x33t
        -0x42t
        -0x42t
        -0x43t
        -0x3at
        -0x45t
        -0x51t
        -0x54t
        -0x4at
        -0x47t
        -0x58t
        -0x64t
        -0x5bt
        -0x64t
        -0x6at
        0x7et
        0x76t
        0x7ft
        0x76t
        0x70t
        0x59t
        -0xdt
        -0x19t
        -0x6t
        -0x8t
        -0xbt
        -0x7t
        -0xft
        -0x19t
        -0x3ft
        -0x4ct
        -0x51t
        -0x50t
        -0x46t
        0x7at
        -0x3et
        -0x50t
        -0x53t
        -0x48t
        -0xct
        -0x1et
        -0x21t
        -0x16t
    .end array-data
.end method

.method private A07(Lcom/facebook/ads/redexgen/X/Bt;I)V
    .locals 5
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;,
            Ljava/lang/InterruptedException;
        }
    .end annotation

    .line 64218
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0k:Lcom/facebook/ads/redexgen/X/Hz;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/Hz;->A07()I

    move-result v0

    if-lt v0, p2, :cond_0

    .line 64219
    return-void

    .line 64220
    :cond_0
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0k:Lcom/facebook/ads/redexgen/X/Hz;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/Hz;->A05()I

    move-result v0

    if-ge v0, p2, :cond_1

    .line 64221
    iget-object v2, p0, Lcom/facebook/ads/redexgen/X/XX;->A0k:Lcom/facebook/ads/redexgen/X/Hz;

    iget-object v1, v2, Lcom/facebook/ads/redexgen/X/Hz;->A00:[B

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0k:Lcom/facebook/ads/redexgen/X/Hz;

    iget-object v0, v0, Lcom/facebook/ads/redexgen/X/Hz;->A00:[B

    array-length v0, v0

    mul-int/lit8 v0, v0, 0x2

    .line 64222
    invoke-static {v0, p2}, Ljava/lang/Math;->max(II)I

    move-result v0

    invoke-static {v1, v0}, Ljava/util/Arrays;->copyOf([BI)[B

    move-result-object v1

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0k:Lcom/facebook/ads/redexgen/X/Hz;

    .line 64223
    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/Hz;->A07()I

    move-result v0

    .line 64224
    invoke-virtual {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Hz;->A0b([BI)V

    .line 64225
    :cond_1
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0k:Lcom/facebook/ads/redexgen/X/Hz;

    iget-object v4, v0, Lcom/facebook/ads/redexgen/X/Hz;->A00:[B

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0k:Lcom/facebook/ads/redexgen/X/Hz;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/Hz;->A07()I

    move-result v3

    sget-object v1, Lcom/facebook/ads/redexgen/X/XX;->A0q:[Ljava/lang/String;

    const/4 v0, 0x6

    aget-object v0, v1, v0

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v1

    const/16 v0, 0x1b

    if-eq v1, v0, :cond_2

    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    :cond_2
    sget-object v2, Lcom/facebook/ads/redexgen/X/XX;->A0q:[Ljava/lang/String;

    const-string v1, "ZmQc4cegfKT6TM2j1HdEhTMrEsYvVAK8"

    const/4 v0, 0x0

    aput-object v1, v2, v0

    const-string v1, "PLs1alOmXmQPSsdSdZUe4T02xUG6CaQT"

    const/4 v0, 0x2

    aput-object v1, v2, v0

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0k:Lcom/facebook/ads/redexgen/X/Hz;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/Hz;->A07()I

    move-result v0

    sub-int v0, p2, v0

    invoke-interface {p1, v4, v3, v0}, Lcom/facebook/ads/redexgen/X/Bt;->readFully([BII)V

    .line 64226
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0k:Lcom/facebook/ads/redexgen/X/Hz;

    invoke-virtual {v0, p2}, Lcom/facebook/ads/redexgen/X/Hz;->A0X(I)V

    .line 64227
    return-void
.end method

.method private A08(Lcom/facebook/ads/redexgen/X/Bt;Lcom/facebook/ads/redexgen/X/CE;I)V
    .locals 10
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;,
            Ljava/lang/InterruptedException;
        }
    .end annotation

    .line 64228
    move-object v4, p0

    const/16 v2, 0x34b

    const/16 v1, 0xb

    const/16 v0, 0x59

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/XX;->A03(III)Ljava/lang/String;

    move-result-object v1

    iget-object v0, p2, Lcom/facebook/ads/redexgen/X/CE;->A0Y:Ljava/lang/String;

    invoke-virtual {v1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_0

    .line 64229
    sget-object v0, Lcom/facebook/ads/redexgen/X/XX;->A0w:[B

    invoke-direct {v4, p1, v0, p3}, Lcom/facebook/ads/redexgen/X/XX;->A09(Lcom/facebook/ads/redexgen/X/Bt;[BI)V

    .line 64230
    return-void

    .line 64231
    :cond_0
    const/16 v2, 0x341

    const/16 v1, 0xa

    const/16 v0, 0x39

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/XX;->A03(III)Ljava/lang/String;

    move-result-object v1

    iget-object v0, p2, Lcom/facebook/ads/redexgen/X/CE;->A0Y:Ljava/lang/String;

    invoke-virtual {v1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_1

    .line 64232
    sget-object v0, Lcom/facebook/ads/redexgen/X/XX;->A0u:[B

    invoke-direct {v4, p1, v0, p3}, Lcom/facebook/ads/redexgen/X/XX;->A09(Lcom/facebook/ads/redexgen/X/Bt;[BI)V

    .line 64233
    return-void

    .line 64234
    :cond_1
    iget-object v3, p2, Lcom/facebook/ads/redexgen/X/CE;->A0W:Lcom/facebook/ads/redexgen/X/C4;

    .line 64235
    .local v4, "output":Lcom/facebook/ads/redexgen/X/C4;
    iget-boolean v0, v4, Lcom/facebook/ads/redexgen/X/XX;->A0S:Z

    const/4 v5, 0x2

    const/4 v6, 0x1

    const/4 v1, 0x0

    if-nez v0, :cond_10

    .line 64236
    iget-boolean v0, p2, Lcom/facebook/ads/redexgen/X/CE;->A0c:Z

    if-eqz v0, :cond_e

    .line 64237
    iget v2, v4, Lcom/facebook/ads/redexgen/X/XX;->A01:I

    const v0, -0x40000001    # -1.9999999f

    and-int/2addr v2, v0

    iput v2, v4, Lcom/facebook/ads/redexgen/X/XX;->A01:I

    .line 64238
    iget-boolean v0, v4, Lcom/facebook/ads/redexgen/X/XX;->A0X:Z

    const/16 v8, 0x80

    if-nez v0, :cond_2

    .line 64239
    iget-object v0, v4, Lcom/facebook/ads/redexgen/X/XX;->A0k:Lcom/facebook/ads/redexgen/X/Hz;

    iget-object v0, v0, Lcom/facebook/ads/redexgen/X/Hz;->A00:[B

    invoke-interface {p1, v0, v1, v6}, Lcom/facebook/ads/redexgen/X/Bt;->readFully([BII)V

    .line 64240
    iget v0, v4, Lcom/facebook/ads/redexgen/X/XX;->A07:I

    add-int/2addr v0, v6

    iput v0, v4, Lcom/facebook/ads/redexgen/X/XX;->A07:I

    .line 64241
    iget-object v0, v4, Lcom/facebook/ads/redexgen/X/XX;->A0k:Lcom/facebook/ads/redexgen/X/Hz;

    iget-object v0, v0, Lcom/facebook/ads/redexgen/X/Hz;->A00:[B

    aget-byte v0, v0, v1

    and-int/2addr v0, v8

    if-eq v0, v8, :cond_d

    .line 64242
    iget-object v0, v4, Lcom/facebook/ads/redexgen/X/XX;->A0k:Lcom/facebook/ads/redexgen/X/Hz;

    iget-object v0, v0, Lcom/facebook/ads/redexgen/X/Hz;->A00:[B

    aget-byte v0, v0, v1

    iput-byte v0, v4, Lcom/facebook/ads/redexgen/X/XX;->A00:B

    .line 64243
    iput-boolean v6, v4, Lcom/facebook/ads/redexgen/X/XX;->A0X:Z

    .line 64244
    :cond_2
    iget-byte v2, v4, Lcom/facebook/ads/redexgen/X/XX;->A00:B

    and-int/lit8 v0, v2, 0x1

    if-ne v0, v6, :cond_a

    const/4 v0, 0x1

    .line 64245
    .local p1, "isEncrypted":Z
    :goto_0
    if-eqz v0, :cond_f

    .line 64246
    and-int/2addr v2, v5

    if-ne v2, v5, :cond_9

    const/4 v9, 0x1

    .line 64247
    .local v5, "hasSubsampleEncryption":Z
    :goto_1
    iget v2, v4, Lcom/facebook/ads/redexgen/X/XX;->A01:I

    const/high16 v0, 0x40000000    # 2.0f

    or-int/2addr v2, v0

    iput v2, v4, Lcom/facebook/ads/redexgen/X/XX;->A01:I

    .line 64248
    iget-boolean v0, v4, Lcom/facebook/ads/redexgen/X/XX;->A0T:Z

    if-nez v0, :cond_3

    .line 64249
    iget-object v0, v4, Lcom/facebook/ads/redexgen/X/XX;->A0f:Lcom/facebook/ads/redexgen/X/Hz;

    iget-object v0, v0, Lcom/facebook/ads/redexgen/X/Hz;->A00:[B

    const/16 v2, 0x8

    invoke-interface {p1, v0, v1, v2}, Lcom/facebook/ads/redexgen/X/Bt;->readFully([BII)V

    .line 64250
    iget v0, v4, Lcom/facebook/ads/redexgen/X/XX;->A07:I

    add-int/2addr v0, v2

    iput v0, v4, Lcom/facebook/ads/redexgen/X/XX;->A07:I

    .line 64251
    iput-boolean v6, v4, Lcom/facebook/ads/redexgen/X/XX;->A0T:Z

    .line 64252
    iget-object v0, v4, Lcom/facebook/ads/redexgen/X/XX;->A0k:Lcom/facebook/ads/redexgen/X/Hz;

    iget-object v7, v0, Lcom/facebook/ads/redexgen/X/Hz;->A00:[B

    if-eqz v9, :cond_8

    :goto_2
    or-int/2addr v8, v2

    int-to-byte v0, v8

    aput-byte v0, v7, v1

    .line 64253
    iget-object v0, v4, Lcom/facebook/ads/redexgen/X/XX;->A0k:Lcom/facebook/ads/redexgen/X/Hz;

    invoke-virtual {v0, v1}, Lcom/facebook/ads/redexgen/X/Hz;->A0Y(I)V

    .line 64254
    iget-object v0, v4, Lcom/facebook/ads/redexgen/X/XX;->A0k:Lcom/facebook/ads/redexgen/X/Hz;

    invoke-interface {v3, v0, v6}, Lcom/facebook/ads/redexgen/X/C4;->AFR(Lcom/facebook/ads/redexgen/X/Hz;I)V

    .line 64255
    iget v0, v4, Lcom/facebook/ads/redexgen/X/XX;->A08:I

    add-int/2addr v0, v6

    iput v0, v4, Lcom/facebook/ads/redexgen/X/XX;->A08:I

    .line 64256
    iget-object v0, v4, Lcom/facebook/ads/redexgen/X/XX;->A0f:Lcom/facebook/ads/redexgen/X/Hz;

    invoke-virtual {v0, v1}, Lcom/facebook/ads/redexgen/X/Hz;->A0Y(I)V

    .line 64257
    iget-object v0, v4, Lcom/facebook/ads/redexgen/X/XX;->A0f:Lcom/facebook/ads/redexgen/X/Hz;

    invoke-interface {v3, v0, v2}, Lcom/facebook/ads/redexgen/X/C4;->AFR(Lcom/facebook/ads/redexgen/X/Hz;I)V

    .line 64258
    iget v0, v4, Lcom/facebook/ads/redexgen/X/XX;->A08:I

    add-int/2addr v0, v2

    iput v0, v4, Lcom/facebook/ads/redexgen/X/XX;->A08:I

    .line 64259
    :cond_3
    if-eqz v9, :cond_f

    .line 64260
    iget-boolean v0, v4, Lcom/facebook/ads/redexgen/X/XX;->A0U:Z

    if-nez v0, :cond_4

    .line 64261
    iget-object v0, v4, Lcom/facebook/ads/redexgen/X/XX;->A0k:Lcom/facebook/ads/redexgen/X/Hz;

    iget-object v0, v0, Lcom/facebook/ads/redexgen/X/Hz;->A00:[B

    invoke-interface {p1, v0, v1, v6}, Lcom/facebook/ads/redexgen/X/Bt;->readFully([BII)V

    .line 64262
    iget v0, v4, Lcom/facebook/ads/redexgen/X/XX;->A07:I

    add-int/2addr v0, v6

    iput v0, v4, Lcom/facebook/ads/redexgen/X/XX;->A07:I

    .line 64263
    iget-object v0, v4, Lcom/facebook/ads/redexgen/X/XX;->A0k:Lcom/facebook/ads/redexgen/X/Hz;

    invoke-virtual {v0, v1}, Lcom/facebook/ads/redexgen/X/Hz;->A0Y(I)V

    .line 64264
    iget-object v0, v4, Lcom/facebook/ads/redexgen/X/XX;->A0k:Lcom/facebook/ads/redexgen/X/Hz;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/Hz;->A0E()I

    move-result v0

    iput v0, v4, Lcom/facebook/ads/redexgen/X/XX;->A0A:I

    .line 64265
    iput-boolean v6, v4, Lcom/facebook/ads/redexgen/X/XX;->A0U:Z

    .line 64266
    :cond_4
    iget v0, v4, Lcom/facebook/ads/redexgen/X/XX;->A0A:I

    mul-int/lit8 v2, v0, 0x4

    .line 64267
    .local p0, "samplePartitionDataSize":I
    iget-object v0, v4, Lcom/facebook/ads/redexgen/X/XX;->A0k:Lcom/facebook/ads/redexgen/X/Hz;

    invoke-virtual {v0, v2}, Lcom/facebook/ads/redexgen/X/Hz;->A0W(I)V

    .line 64268
    iget-object v0, v4, Lcom/facebook/ads/redexgen/X/XX;->A0k:Lcom/facebook/ads/redexgen/X/Hz;

    iget-object v0, v0, Lcom/facebook/ads/redexgen/X/Hz;->A00:[B

    invoke-interface {p1, v0, v1, v2}, Lcom/facebook/ads/redexgen/X/Bt;->readFully([BII)V

    .line 64269
    iget v0, v4, Lcom/facebook/ads/redexgen/X/XX;->A07:I

    add-int/2addr v0, v2

    iput v0, v4, Lcom/facebook/ads/redexgen/X/XX;->A07:I

    .line 64270
    iget v0, v4, Lcom/facebook/ads/redexgen/X/XX;->A0A:I

    div-int/2addr v0, v5

    add-int/2addr v0, v6

    int-to-short v7, v0

    .line 64271
    .local p2, "subsampleCount":S
    mul-int/lit8 v6, v7, 0x6

    add-int/2addr v6, v5

    .line 64272
    .local p3, "subsampleDataSize":I
    iget-object v0, v4, Lcom/facebook/ads/redexgen/X/XX;->A0R:Ljava/nio/ByteBuffer;

    if-eqz v0, :cond_5

    .line 64273
    invoke-virtual {v0}, Ljava/nio/ByteBuffer;->capacity()I

    move-result v0

    if-ge v0, v6, :cond_6

    .line 64274
    :cond_5
    invoke-static {v6}, Ljava/nio/ByteBuffer;->allocate(I)Ljava/nio/ByteBuffer;

    move-result-object v0

    iput-object v0, v4, Lcom/facebook/ads/redexgen/X/XX;->A0R:Ljava/nio/ByteBuffer;

    .line 64275
    :cond_6
    iget-object v0, v4, Lcom/facebook/ads/redexgen/X/XX;->A0R:Ljava/nio/ByteBuffer;

    invoke-virtual {v0, v1}, Ljava/nio/ByteBuffer;->position(I)Ljava/nio/Buffer;

    sget-object v1, Lcom/facebook/ads/redexgen/X/XX;->A0q:[Ljava/lang/String;

    const/4 v0, 0x7

    aget-object v1, v1, v0

    const/4 v0, 0x2

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    const/16 v0, 0x4a

    if-eq v1, v0, :cond_13

    .line 64276
    sget-object v2, Lcom/facebook/ads/redexgen/X/XX;->A0q:[Ljava/lang/String;

    const-string v1, "THDQpgmvr4cFRBgiNMs8PF"

    const/4 v0, 0x3

    aput-object v1, v2, v0

    const-string v1, "1poAoCQZUv3pf1E"

    const/4 v0, 0x4

    aput-object v1, v2, v0

    iget-object v0, v4, Lcom/facebook/ads/redexgen/X/XX;->A0R:Ljava/nio/ByteBuffer;

    invoke-virtual {v0, v7}, Ljava/nio/ByteBuffer;->putShort(S)Ljava/nio/ByteBuffer;

    .line 64277
    const/4 v7, 0x0

    .line 64278
    .local p4, "partitionOffset":I
    const/4 v2, 0x0

    .local p5, "i":I
    :goto_3
    iget v1, v4, Lcom/facebook/ads/redexgen/X/XX;->A0A:I

    if-ge v2, v1, :cond_b

    .line 64279
    move v8, v7

    .line 64280
    .local v6, "previousPartitionOffset":I
    iget-object v0, v4, Lcom/facebook/ads/redexgen/X/XX;->A0k:Lcom/facebook/ads/redexgen/X/Hz;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/Hz;->A0H()I

    move-result v7

    .line 64281
    rem-int/lit8 v0, v2, 0x2

    if-nez v0, :cond_7

    .line 64282
    iget-object v1, v4, Lcom/facebook/ads/redexgen/X/XX;->A0R:Ljava/nio/ByteBuffer;

    sub-int v0, v7, v8

    int-to-short v0, v0

    invoke-virtual {v1, v0}, Ljava/nio/ByteBuffer;->putShort(S)Ljava/nio/ByteBuffer;

    .line 64283
    .end local v6    # "previousPartitionOffset":I
    :goto_4
    add-int/lit8 v2, v2, 0x1

    goto :goto_3

    .line 64284
    :cond_7
    iget-object v1, v4, Lcom/facebook/ads/redexgen/X/XX;->A0R:Ljava/nio/ByteBuffer;

    sub-int v0, v7, v8

    invoke-virtual {v1, v0}, Ljava/nio/ByteBuffer;->putInt(I)Ljava/nio/ByteBuffer;

    goto :goto_4

    .line 64285
    :cond_8
    const/4 v8, 0x0

    goto/16 :goto_2

    .line 64286
    :cond_9
    const/4 v9, 0x0

    goto/16 :goto_1

    .line 64287
    :cond_a
    const/4 v0, 0x0

    goto/16 :goto_0

    .line 64288
    .end local p5
    :cond_b
    iget v0, v4, Lcom/facebook/ads/redexgen/X/XX;->A07:I

    sub-int v2, p3, v0

    sub-int/2addr v2, v7

    .line 64289
    .local v8, "finalPartitionSize":I
    rem-int/2addr v1, v5

    const/4 v0, 0x1

    if-ne v1, v0, :cond_c

    .line 64290
    iget-object v0, v4, Lcom/facebook/ads/redexgen/X/XX;->A0R:Ljava/nio/ByteBuffer;

    invoke-virtual {v0, v2}, Ljava/nio/ByteBuffer;->putInt(I)Ljava/nio/ByteBuffer;

    .line 64291
    :goto_5
    iget-object v1, v4, Lcom/facebook/ads/redexgen/X/XX;->A0g:Lcom/facebook/ads/redexgen/X/Hz;

    iget-object v0, v4, Lcom/facebook/ads/redexgen/X/XX;->A0R:Ljava/nio/ByteBuffer;

    invoke-virtual {v0}, Ljava/nio/ByteBuffer;->array()[B

    move-result-object v0

    invoke-virtual {v1, v0, v6}, Lcom/facebook/ads/redexgen/X/Hz;->A0b([BI)V

    .line 64292
    iget-object v0, v4, Lcom/facebook/ads/redexgen/X/XX;->A0g:Lcom/facebook/ads/redexgen/X/Hz;

    invoke-interface {v3, v0, v6}, Lcom/facebook/ads/redexgen/X/C4;->AFR(Lcom/facebook/ads/redexgen/X/Hz;I)V

    .line 64293
    iget v0, v4, Lcom/facebook/ads/redexgen/X/XX;->A08:I

    add-int/2addr v0, v6

    iput v0, v4, Lcom/facebook/ads/redexgen/X/XX;->A08:I

    goto :goto_6

    .line 64294
    :cond_c
    iget-object v1, v4, Lcom/facebook/ads/redexgen/X/XX;->A0R:Ljava/nio/ByteBuffer;

    int-to-short v0, v2

    invoke-virtual {v1, v0}, Ljava/nio/ByteBuffer;->putShort(S)Ljava/nio/ByteBuffer;

    .line 64295
    iget-object v1, v4, Lcom/facebook/ads/redexgen/X/XX;->A0R:Ljava/nio/ByteBuffer;

    const/4 v0, 0x0

    invoke-virtual {v1, v0}, Ljava/nio/ByteBuffer;->putInt(I)Ljava/nio/ByteBuffer;

    goto :goto_5

    .line 64296
    :cond_d
    const/16 v2, 0x1f8

    const/16 v1, 0x23

    const/16 v0, 0x16

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/XX;->A03(III)Ljava/lang/String;

    move-result-object v1

    new-instance v0, Lcom/facebook/ads/redexgen/X/9v;

    invoke-direct {v0, v1}, Lcom/facebook/ads/redexgen/X/9v;-><init>(Ljava/lang/String;)V

    throw v0

    .line 64297
    .end local v5    # "hasSubsampleEncryption":Z
    .end local v8    # "finalPartitionSize":I
    .end local p0    # "samplePartitionDataSize":I
    .end local p1    # "isEncrypted":Z
    .end local p2    # "subsampleCount":S
    .end local p3    # "subsampleDataSize":I
    .end local p4
    :cond_e
    iget-object v0, p2, Lcom/facebook/ads/redexgen/X/CE;->A0f:[B

    if-eqz v0, :cond_f

    .line 64298
    iget-object v2, v4, Lcom/facebook/ads/redexgen/X/XX;->A0j:Lcom/facebook/ads/redexgen/X/Hz;

    iget-object v1, p2, Lcom/facebook/ads/redexgen/X/CE;->A0f:[B

    iget-object v0, p2, Lcom/facebook/ads/redexgen/X/CE;->A0f:[B

    array-length v0, v0

    invoke-virtual {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Hz;->A0b([BI)V

    .line 64299
    :cond_f
    :goto_6
    const/4 v0, 0x1

    iput-boolean v0, v4, Lcom/facebook/ads/redexgen/X/XX;->A0S:Z

    .line 64300
    :cond_10
    iget-object v0, v4, Lcom/facebook/ads/redexgen/X/XX;->A0j:Lcom/facebook/ads/redexgen/X/Hz;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/Hz;->A07()I

    move-result v0

    add-int/2addr p3, v0

    .line 64301
    .end local p9
    .local v3, "size":I
    const/16 v2, 0x3aa

    const/16 v1, 0xf

    const/16 v0, 0x18

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/XX;->A03(III)Ljava/lang/String;

    move-result-object v1

    iget-object v0, p2, Lcom/facebook/ads/redexgen/X/CE;->A0Y:Ljava/lang/String;

    invoke-virtual {v1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_11

    const/16 v8, 0x3c7

    const/16 v7, 0x10

    const/16 v6, 0x12

    sget-object v2, Lcom/facebook/ads/redexgen/X/XX;->A0q:[Ljava/lang/String;

    const/4 v0, 0x3

    aget-object v1, v2, v0

    const/4 v0, 0x4

    aget-object v0, v2, v0

    invoke-virtual {v1}, Ljava/lang/String;->length()I

    move-result v1

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v0

    if-eq v1, v0, :cond_15

    sget-object v2, Lcom/facebook/ads/redexgen/X/XX;->A0q:[Ljava/lang/String;

    const-string v1, "n5KvGwKmSVXSgkcPIzRNIJsu4vgaIOKI"

    const/4 v0, 0x0

    aput-object v1, v2, v0

    const-string v1, "bzKZ8kTny4n19sCxh6YTLkMgvo4kJK45"

    const/4 v0, 0x2

    aput-object v1, v2, v0

    invoke-static {v8, v7, v6}, Lcom/facebook/ads/redexgen/X/XX;->A03(III)Ljava/lang/String;

    move-result-object v1

    iget-object v0, p2, Lcom/facebook/ads/redexgen/X/CE;->A0Y:Ljava/lang/String;

    invoke-virtual {v1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_16

    .line 64302
    :cond_11
    :goto_7
    iget-object v0, v4, Lcom/facebook/ads/redexgen/X/XX;->A0h:Lcom/facebook/ads/redexgen/X/Hz;

    iget-object v7, v0, Lcom/facebook/ads/redexgen/X/Hz;->A00:[B

    .line 64303
    .local v5, "nalLengthData":[B
    const/4 v1, 0x0

    aput-byte v1, v7, v1

    .line 64304
    const/4 v0, 0x1

    aput-byte v1, v7, v0

    .line 64305
    aput-byte v1, v7, v5

    .line 64306
    iget v8, p2, Lcom/facebook/ads/redexgen/X/CE;->A0M:I

    .line 64307
    .local v6, "nalUnitLengthFieldLength":I
    iget v0, p2, Lcom/facebook/ads/redexgen/X/CE;->A0M:I

    rsub-int/lit8 v6, v0, 0x4

    .line 64308
    .local v7, "nalUnitLengthFieldLengthDiff":I
    :goto_8
    iget v0, v4, Lcom/facebook/ads/redexgen/X/XX;->A07:I

    if-ge v0, p3, :cond_19

    .line 64309
    iget v5, v4, Lcom/facebook/ads/redexgen/X/XX;->A09:I

    if-nez v5, :cond_12

    .line 64310
    invoke-direct {v4, p1, v7, v6, v8}, Lcom/facebook/ads/redexgen/X/XX;->A0A(Lcom/facebook/ads/redexgen/X/Bt;[BII)V

    .line 64311
    iget-object v0, v4, Lcom/facebook/ads/redexgen/X/XX;->A0h:Lcom/facebook/ads/redexgen/X/Hz;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Lcom/facebook/ads/redexgen/X/Hz;->A0Y(I)V

    .line 64312
    iget-object v0, v4, Lcom/facebook/ads/redexgen/X/XX;->A0h:Lcom/facebook/ads/redexgen/X/Hz;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/Hz;->A0H()I

    move-result v0

    iput v0, v4, Lcom/facebook/ads/redexgen/X/XX;->A09:I

    .line 64313
    iget-object v0, v4, Lcom/facebook/ads/redexgen/X/XX;->A0i:Lcom/facebook/ads/redexgen/X/Hz;

    invoke-virtual {v0, v1}, Lcom/facebook/ads/redexgen/X/Hz;->A0Y(I)V

    .line 64314
    iget-object v0, v4, Lcom/facebook/ads/redexgen/X/XX;->A0i:Lcom/facebook/ads/redexgen/X/Hz;

    const/4 v1, 0x4

    invoke-interface {v3, v0, v1}, Lcom/facebook/ads/redexgen/X/C4;->AFR(Lcom/facebook/ads/redexgen/X/Hz;I)V

    .line 64315
    iget v0, v4, Lcom/facebook/ads/redexgen/X/XX;->A08:I

    add-int/2addr v0, v1

    iput v0, v4, Lcom/facebook/ads/redexgen/X/XX;->A08:I

    goto :goto_8

    .line 64316
    :cond_12
    invoke-direct {v4, p1, v3, v5}, Lcom/facebook/ads/redexgen/X/XX;->A00(Lcom/facebook/ads/redexgen/X/Bt;Lcom/facebook/ads/redexgen/X/C4;I)I

    move-result v0

    sub-int/2addr v5, v0

    sget-object v2, Lcom/facebook/ads/redexgen/X/XX;->A0q:[Ljava/lang/String;

    const/4 v0, 0x1

    aget-object v1, v2, v0

    const/4 v0, 0x5

    aget-object v2, v2, v0

    const/16 v0, 0xf

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    invoke-virtual {v2, v0}, Ljava/lang/String;->charAt(I)C

    move-result v0

    if-eq v1, v0, :cond_14

    :cond_13
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    :cond_14
    sget-object v2, Lcom/facebook/ads/redexgen/X/XX;->A0q:[Ljava/lang/String;

    const-string v1, "agkFlLz8FmVBufdg5xCACjDlG3vMW3hz"

    const/4 v0, 0x1

    aput-object v1, v2, v0

    const-string v1, "TJ6FvEhwXk7W3hxgnwrmoI06U5KOwDqT"

    const/4 v0, 0x5

    aput-object v1, v2, v0

    iput v5, v4, Lcom/facebook/ads/redexgen/X/XX;->A09:I

    goto :goto_8

    :cond_15
    sget-object v2, Lcom/facebook/ads/redexgen/X/XX;->A0q:[Ljava/lang/String;

    const-string v1, "RszYuFLetKptIKAzZi5TrivHGyzVJlzF"

    const/4 v0, 0x7

    aput-object v1, v2, v0

    invoke-static {v8, v7, v6}, Lcom/facebook/ads/redexgen/X/XX;->A03(III)Ljava/lang/String;

    move-result-object v1

    iget-object v0, p2, Lcom/facebook/ads/redexgen/X/CE;->A0Y:Ljava/lang/String;

    invoke-virtual {v1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_16

    goto :goto_7

    .line 64317
    :cond_16
    iget-object v0, p2, Lcom/facebook/ads/redexgen/X/CE;->A0X:Lcom/facebook/ads/redexgen/X/CF;

    if-eqz v0, :cond_17

    .line 64318
    iget-object v0, v4, Lcom/facebook/ads/redexgen/X/XX;->A0j:Lcom/facebook/ads/redexgen/X/Hz;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/Hz;->A07()I

    move-result v0

    if-nez v0, :cond_18

    const/4 v0, 0x1

    :goto_9
    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/Ha;->A04(Z)V

    .line 64319
    iget-object v1, p2, Lcom/facebook/ads/redexgen/X/CE;->A0X:Lcom/facebook/ads/redexgen/X/CF;

    iget v0, v4, Lcom/facebook/ads/redexgen/X/XX;->A01:I

    invoke-virtual {v1, p1, v0, p3}, Lcom/facebook/ads/redexgen/X/CF;->A01(Lcom/facebook/ads/redexgen/X/Bt;II)V

    .line 64320
    :cond_17
    :goto_a
    iget v0, v4, Lcom/facebook/ads/redexgen/X/XX;->A07:I

    if-ge v0, p3, :cond_19

    .line 64321
    sub-int v0, p3, v0

    invoke-direct {v4, p1, v3, v0}, Lcom/facebook/ads/redexgen/X/XX;->A00(Lcom/facebook/ads/redexgen/X/Bt;Lcom/facebook/ads/redexgen/X/C4;I)I

    goto :goto_a

    .line 64322
    :cond_18
    const/4 v0, 0x0

    goto :goto_9

    .line 64323
    :cond_19
    const/16 v2, 0xb5

    const/16 v1, 0x8

    const/16 v0, 0x57

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/XX;->A03(III)Ljava/lang/String;

    move-result-object v1

    iget-object v0, p2, Lcom/facebook/ads/redexgen/X/CE;->A0Y:Ljava/lang/String;

    invoke-virtual {v1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_1a

    .line 64324
    iget-object v1, v4, Lcom/facebook/ads/redexgen/X/XX;->A0n:Lcom/facebook/ads/redexgen/X/Hz;

    const/4 v0, 0x0

    invoke-virtual {v1, v0}, Lcom/facebook/ads/redexgen/X/Hz;->A0Y(I)V

    .line 64325
    iget-object v0, v4, Lcom/facebook/ads/redexgen/X/XX;->A0n:Lcom/facebook/ads/redexgen/X/Hz;

    const/4 v1, 0x4

    invoke-interface {v3, v0, v1}, Lcom/facebook/ads/redexgen/X/C4;->AFR(Lcom/facebook/ads/redexgen/X/Hz;I)V

    .line 64326
    iget v0, v4, Lcom/facebook/ads/redexgen/X/XX;->A08:I

    add-int/2addr v0, v1

    iput v0, v4, Lcom/facebook/ads/redexgen/X/XX;->A08:I

    .line 64327
    :cond_1a
    return-void
.end method

.method private A09(Lcom/facebook/ads/redexgen/X/Bt;[BI)V
    .locals 4
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;,
            Ljava/lang/InterruptedException;
        }
    .end annotation

    .line 64328
    array-length v3, p2

    add-int/2addr v3, p3

    .line 64329
    .local v0, "sizeWithPrefix":I
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0m:Lcom/facebook/ads/redexgen/X/Hz;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/Hz;->A05()I

    move-result v0

    if-ge v0, v3, :cond_0

    .line 64330
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/XX;->A0m:Lcom/facebook/ads/redexgen/X/Hz;

    add-int v0, v3, p3

    invoke-static {p2, v0}, Ljava/util/Arrays;->copyOf([BI)[B

    move-result-object v0

    iput-object v0, v1, Lcom/facebook/ads/redexgen/X/Hz;->A00:[B

    .line 64331
    :goto_0
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0m:Lcom/facebook/ads/redexgen/X/Hz;

    iget-object v1, v0, Lcom/facebook/ads/redexgen/X/Hz;->A00:[B

    array-length v0, p2

    invoke-interface {p1, v1, v0, p3}, Lcom/facebook/ads/redexgen/X/Bt;->readFully([BII)V

    .line 64332
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0m:Lcom/facebook/ads/redexgen/X/Hz;

    invoke-virtual {v0, v3}, Lcom/facebook/ads/redexgen/X/Hz;->A0W(I)V

    .line 64333
    return-void

    .line 64334
    :cond_0
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0m:Lcom/facebook/ads/redexgen/X/Hz;

    iget-object v2, v0, Lcom/facebook/ads/redexgen/X/Hz;->A00:[B

    array-length v1, p2

    const/4 v0, 0x0

    invoke-static {p2, v0, v2, v0, v1}, Ljava/lang/System;->arraycopy(Ljava/lang/Object;ILjava/lang/Object;II)V

    goto :goto_0
.end method

.method private A0A(Lcom/facebook/ads/redexgen/X/Bt;[BII)V
    .locals 3
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;,
            Ljava/lang/InterruptedException;
        }
    .end annotation

    .line 64335
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0j:Lcom/facebook/ads/redexgen/X/Hz;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/Hz;->A04()I

    move-result v0

    invoke-static {p4, v0}, Ljava/lang/Math;->min(II)I

    move-result v2

    .line 64336
    .local v0, "pendingStrippedBytes":I
    add-int v1, p3, v2

    sub-int v0, p4, v2

    invoke-interface {p1, p2, v1, v0}, Lcom/facebook/ads/redexgen/X/Bt;->readFully([BII)V

    .line 64337
    if-lez v2, :cond_0

    .line 64338
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0j:Lcom/facebook/ads/redexgen/X/Hz;

    invoke-virtual {v0, p2, p3, v2}, Lcom/facebook/ads/redexgen/X/Hz;->A0c([BII)V

    .line 64339
    :cond_0
    iget v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A07:I

    add-int/2addr v0, p4

    iput v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A07:I

    .line 64340
    return-void
.end method

.method private A0B(Lcom/facebook/ads/redexgen/X/CE;J)V
    .locals 14

    .line 64341
    move-object v0, p0

    iget-object v1, p1, Lcom/facebook/ads/redexgen/X/CE;->A0X:Lcom/facebook/ads/redexgen/X/CF;

    move-wide/from16 v8, p2

    if-eqz v1, :cond_0

    .line 64342
    iget-object v4, p1, Lcom/facebook/ads/redexgen/X/CE;->A0X:Lcom/facebook/ads/redexgen/X/CF;

    sget-object v2, Lcom/facebook/ads/redexgen/X/XX;->A0q:[Ljava/lang/String;

    const/4 v1, 0x6

    aget-object v1, v2, v1

    invoke-virtual {v1}, Ljava/lang/String;->length()I

    move-result v2

    const/16 v1, 0x1b

    if-eq v2, v1, :cond_3

    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    .line 64343
    :cond_0
    const/16 v3, 0x34b

    const/16 v2, 0xb

    const/16 v1, 0x59

    invoke-static {v3, v2, v1}, Lcom/facebook/ads/redexgen/X/XX;->A03(III)Ljava/lang/String;

    move-result-object v2

    iget-object v1, p1, Lcom/facebook/ads/redexgen/X/CE;->A0Y:Ljava/lang/String;

    invoke-virtual {v2, v1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_2

    .line 64344
    const/16 v3, 0x21

    const/16 v2, 0x13

    const/4 v1, 0x7

    invoke-static {v3, v2, v1}, Lcom/facebook/ads/redexgen/X/XX;->A03(III)Ljava/lang/String;

    move-result-object v3

    const/16 v4, 0x13

    const-wide/16 v5, 0x3e8

    sget-object v7, Lcom/facebook/ads/redexgen/X/XX;->A0x:[B

    move-object v1, p0

    move-object v2, p1

    invoke-direct/range {v1 .. v7}, Lcom/facebook/ads/redexgen/X/XX;->A0C(Lcom/facebook/ads/redexgen/X/CE;Ljava/lang/String;IJ[B)V

    .line 64345
    :cond_1
    :goto_0
    iget-object v7, p1, Lcom/facebook/ads/redexgen/X/CE;->A0W:Lcom/facebook/ads/redexgen/X/C4;

    iget v10, v0, Lcom/facebook/ads/redexgen/X/XX;->A01:I

    iget v11, v0, Lcom/facebook/ads/redexgen/X/XX;->A08:I

    const/4 v12, 0x0

    iget-object v13, p1, Lcom/facebook/ads/redexgen/X/CE;->A0V:Lcom/facebook/ads/redexgen/X/C3;

    invoke-interface/range {v7 .. v13}, Lcom/facebook/ads/redexgen/X/C4;->AFS(JIIILcom/facebook/ads/redexgen/X/C3;)V

    goto :goto_1

    .line 64346
    :cond_2
    const/16 v3, 0x341

    const/16 v2, 0xa

    const/16 v1, 0x39

    invoke-static {v3, v2, v1}, Lcom/facebook/ads/redexgen/X/XX;->A03(III)Ljava/lang/String;

    move-result-object v2

    iget-object v1, p1, Lcom/facebook/ads/redexgen/X/CE;->A0Y:Ljava/lang/String;

    invoke-virtual {v2, v1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_1

    .line 64347
    const/16 v3, 0xe

    const/16 v2, 0x13

    const/16 v1, 0x6d

    invoke-static {v3, v2, v1}, Lcom/facebook/ads/redexgen/X/XX;->A03(III)Ljava/lang/String;

    move-result-object v3

    const/16 v4, 0x15

    const-wide/16 v5, 0x2710

    sget-object v7, Lcom/facebook/ads/redexgen/X/XX;->A0v:[B

    move-object v1, p0

    move-object v2, p1

    invoke-direct/range {v1 .. v7}, Lcom/facebook/ads/redexgen/X/XX;->A0C(Lcom/facebook/ads/redexgen/X/CE;Ljava/lang/String;IJ[B)V

    goto :goto_0

    .line 64348
    :cond_3
    sget-object v3, Lcom/facebook/ads/redexgen/X/XX;->A0q:[Ljava/lang/String;

    const-string v2, "nKrIVrdQs7NkqXN01cL0onp1lFd"

    const/4 v1, 0x6

    aput-object v2, v3, v1

    invoke-virtual {v4, p1, v8, v9}, Lcom/facebook/ads/redexgen/X/CF;->A03(Lcom/facebook/ads/redexgen/X/CE;J)V

    .line 64349
    :goto_1
    const/4 v1, 0x1

    iput-boolean v1, v0, Lcom/facebook/ads/redexgen/X/XX;->A0V:Z

    .line 64350
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/XX;->A05()V

    .line 64351
    return-void
.end method

.method private A0C(Lcom/facebook/ads/redexgen/X/CE;Ljava/lang/String;IJ[B)V
    .locals 8

    .line 64352
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0m:Lcom/facebook/ads/redexgen/X/Hz;

    iget-object v0, v0, Lcom/facebook/ads/redexgen/X/Hz;->A00:[B

    iget-wide v1, p0, Lcom/facebook/ads/redexgen/X/XX;->A0C:J

    move-object v3, p2

    move v4, p3

    move-wide v5, p4

    move-object v7, p6

    invoke-static/range {v0 .. v7}, Lcom/facebook/ads/redexgen/X/XX;->A0D([BJLjava/lang/String;IJ[B)V

    .line 64353
    iget-object v2, p1, Lcom/facebook/ads/redexgen/X/CE;->A0W:Lcom/facebook/ads/redexgen/X/C4;

    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/XX;->A0m:Lcom/facebook/ads/redexgen/X/Hz;

    invoke-virtual {v1}, Lcom/facebook/ads/redexgen/X/Hz;->A07()I

    move-result v0

    invoke-interface {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/C4;->AFR(Lcom/facebook/ads/redexgen/X/Hz;I)V

    .line 64354
    iget v1, p0, Lcom/facebook/ads/redexgen/X/XX;->A08:I

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0m:Lcom/facebook/ads/redexgen/X/Hz;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/Hz;->A07()I

    move-result v0

    add-int/2addr v1, v0

    iput v1, p0, Lcom/facebook/ads/redexgen/X/XX;->A08:I

    .line 64355
    return-void
.end method

.method public static A0D([BJLjava/lang/String;IJ[B)V
    .locals 10

    .line 64356
    const-wide v3, -0x7fffffffffffffffL    # -4.9E-324

    const/4 v2, 0x0

    cmp-long v0, p1, v3

    move-object/from16 v5, p7

    if-nez v0, :cond_0

    .line 64357
    move-object v0, v5

    .line 64358
    .local v0, "timeCodeData":[B
    .end local v1
    .end local v6
    .end local v8
    .local v0, "timeCodeData":[B
    :goto_0
    array-length v1, v5

    invoke-static {v0, v2, p0, p4, v1}, Ljava/lang/System;->arraycopy(Ljava/lang/Object;ILjava/lang/Object;II)V

    .line 64359
    return-void

    .line 64360
    .end local v0    # "timeCodeData":[B
    :cond_0
    const-wide v0, 0xd693a400L

    div-long v0, p1, v0

    long-to-int v4, v0

    .line 64361
    .local v1, "hours":I
    mul-int/lit16 v0, v4, 0xe10

    int-to-long v0, v0

    const-wide/32 v7, 0xf4240

    mul-long/2addr v0, v7

    sub-long/2addr p1, v0

    .line 64362
    .end local p2
    .local v3, "durationUs":J
    const-wide/32 v0, 0x3938700

    div-long v0, p1, v0

    long-to-int v3, v0

    .line 64363
    .local v0, "minutes":I
    mul-int/lit8 v0, v3, 0x3c

    int-to-long v0, v0

    mul-long/2addr v0, v7

    sub-long/2addr p1, v0

    .line 64364
    div-long v0, p1, v7

    long-to-int v6, v0

    .line 64365
    .local v8, "seconds":I
    int-to-long v0, v6

    mul-long/2addr v0, v7

    sub-long/2addr p1, v0

    .line 64366
    div-long/2addr p1, p5

    long-to-int v8, p1

    .line 64367
    .local v6, "lastValue":I
    sget-object v9, Ljava/util/Locale;->US:Ljava/util/Locale;

    const/4 v0, 0x4

    new-array v7, v0, [Ljava/lang/Object;

    .line 64368
    invoke-static {v4}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v0

    aput-object v0, v7, v2

    const/4 v1, 0x1

    invoke-static {v3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v0

    aput-object v0, v7, v1

    const/4 v1, 0x2

    invoke-static {v6}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v0

    aput-object v0, v7, v1

    const/4 v1, 0x3

    invoke-static {v8}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v0

    aput-object v0, v7, v1

    invoke-static {v9, p3, v7}, Ljava/lang/String;->format(Ljava/util/Locale;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v0

    .line 64369
    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/IF;->A0i(Ljava/lang/String;)[B

    move-result-object v0

    goto :goto_0
.end method

.method private A0E(Lcom/facebook/ads/redexgen/X/Bz;J)Z
    .locals 7

    .line 64370
    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0Y:Z

    const/4 v6, 0x1

    const/4 v5, 0x0

    if-eqz v0, :cond_0

    .line 64371
    iput-wide p2, p0, Lcom/facebook/ads/redexgen/X/XX;->A0J:J

    .line 64372
    iget-wide v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0F:J

    iput-wide v0, p1, Lcom/facebook/ads/redexgen/X/Bz;->A00:J

    .line 64373
    iput-boolean v5, p0, Lcom/facebook/ads/redexgen/X/XX;->A0Y:Z

    .line 64374
    return v6

    .line 64375
    :cond_0
    iget-boolean v3, p0, Lcom/facebook/ads/redexgen/X/XX;->A0a:Z

    sget-object v2, Lcom/facebook/ads/redexgen/X/XX;->A0q:[Ljava/lang/String;

    const/4 v0, 0x0

    aget-object v1, v2, v0

    const/4 v0, 0x2

    aget-object v2, v2, v0

    const/16 v0, 0x1f

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    invoke-virtual {v2, v0}, Ljava/lang/String;->charAt(I)C

    move-result v0

    if-eq v1, v0, :cond_2

    sget-object v2, Lcom/facebook/ads/redexgen/X/XX;->A0q:[Ljava/lang/String;

    const-string v1, "bgcuSmmv9nMD5qNgR1ZnJGCWOsXR4zDF"

    const/4 v0, 0x1

    aput-object v1, v2, v0

    const-string v1, "rXrXJdml0w7r8hjghYXvJH5LLiJomjK3"

    const/4 v0, 0x5

    aput-object v1, v2, v0

    if-eqz v3, :cond_1

    iget-wide v3, p0, Lcom/facebook/ads/redexgen/X/XX;->A0J:J

    const-wide/16 v1, -0x1

    cmp-long v0, v3, v1

    if-eqz v0, :cond_1

    .line 64376
    iput-wide v3, p1, Lcom/facebook/ads/redexgen/X/Bz;->A00:J

    .line 64377
    iput-wide v1, p0, Lcom/facebook/ads/redexgen/X/XX;->A0J:J

    .line 64378
    return v6

    .line 64379
    :cond_1
    return v5

    :cond_2
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0
.end method

.method public static A0F(Ljava/lang/String;)Z
    .locals 4

    .line 64380
    const/16 v2, 0x3ee

    const/4 v1, 0x5

    const/16 v0, 0x3a

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/XX;->A03(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v0, p0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_3

    .line 64381
    const/16 v2, 0x3f3

    const/4 v1, 0x5

    const/16 v0, 0x14

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/XX;->A03(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v0, p0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_3

    .line 64382
    const/16 v2, 0x386

    const/4 v1, 0x7

    const/16 v0, 0x48

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/XX;->A03(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v0, p0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_3

    .line 64383
    const/16 v2, 0x3b9

    const/16 v1, 0xe

    const/16 v0, 0x69

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/XX;->A03(III)Ljava/lang/String;

    move-result-object v3

    sget-object v2, Lcom/facebook/ads/redexgen/X/XX;->A0q:[Ljava/lang/String;

    const/4 v0, 0x1

    aget-object v1, v2, v0

    const/4 v0, 0x5

    aget-object v2, v2, v0

    const/16 v0, 0xf

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    invoke-virtual {v2, v0}, Ljava/lang/String;->charAt(I)C

    move-result v0

    if-eq v1, v0, :cond_1

    :cond_0
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    :cond_1
    sget-object v2, Lcom/facebook/ads/redexgen/X/XX;->A0q:[Ljava/lang/String;

    const-string v1, "rYhgwmDh4TKBcj4Uwcvshsmah3Wf4ae0"

    const/4 v0, 0x7

    aput-object v1, v2, v0

    invoke-virtual {v3, p0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_3

    .line 64384
    const/16 v2, 0x39b

    const/16 v1, 0xf

    const/16 v0, 0x56

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/XX;->A03(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v0, p0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_3

    .line 64385
    const/16 v2, 0x38d

    const/16 v1, 0xe

    const/16 v0, 0x78

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/XX;->A03(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v0, p0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_3

    .line 64386
    const/16 v2, 0x3aa

    const/16 v1, 0xf

    const/16 v0, 0x18

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/XX;->A03(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v0, p0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_3

    .line 64387
    const/16 v2, 0x3c7

    const/16 v1, 0x10

    const/16 v0, 0x12

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/XX;->A03(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v0, p0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_3

    .line 64388
    const/16 v2, 0x3d7

    const/16 v1, 0xf

    const/16 v0, 0x6f

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/XX;->A03(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v0, p0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_3

    .line 64389
    const/16 v3, 0x3e6

    sget-object v2, Lcom/facebook/ads/redexgen/X/XX;->A0q:[Ljava/lang/String;

    const/4 v0, 0x1

    aget-object v1, v2, v0

    const/4 v0, 0x5

    aget-object v2, v2, v0

    const/16 v0, 0xf

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    invoke-virtual {v2, v0}, Ljava/lang/String;->charAt(I)C

    move-result v0

    if-eq v1, v0, :cond_2

    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    :cond_2
    sget-object v2, Lcom/facebook/ads/redexgen/X/XX;->A0q:[Ljava/lang/String;

    const-string v1, "rlxG2GXYid9bCHwXApke8Gz33sW"

    const/4 v0, 0x6

    aput-object v1, v2, v0

    const/16 v1, 0x8

    const/16 v0, 0x5b

    invoke-static {v3, v1, v0}, Lcom/facebook/ads/redexgen/X/XX;->A03(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v0, p0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_3

    .line 64390
    const/16 v2, 0x9a

    const/4 v1, 0x6

    const/16 v0, 0x66

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/XX;->A03(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v0, p0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_3

    .line 64391
    const/16 v2, 0xb5

    const/16 v1, 0x8

    const/16 v0, 0x57

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/XX;->A03(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v0, p0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_3

    .line 64392
    const/16 v2, 0x4a

    const/4 v1, 0x5

    const/16 v0, 0x7e

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/XX;->A03(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v0, p0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_3

    .line 64393
    const/16 v2, 0x80

    const/16 v1, 0x9

    const/16 v0, 0x66

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/XX;->A03(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v0, p0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_3

    .line 64394
    const/16 v2, 0x89

    const/16 v1, 0x9

    const/16 v0, 0x8

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/XX;->A03(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v0, p0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_3

    .line 64395
    const/16 v2, 0x4f

    const/4 v1, 0x5

    const/16 v0, 0x7c

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/XX;->A03(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v0, p0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_3

    .line 64396
    const/16 v2, 0x74

    const/4 v1, 0x6

    const/16 v0, 0x3b

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/XX;->A03(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v0, p0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_3

    .line 64397
    const/16 v2, 0xad

    const/16 v1, 0x8

    const/16 v0, 0x3e

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/XX;->A03(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v0, p0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_3

    .line 64398
    const/16 v2, 0x54

    const/4 v1, 0x5

    const/16 v0, 0x62

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/XX;->A03(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v0, p0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_3

    .line 64399
    const/16 v2, 0x59

    const/16 v1, 0xd

    const/16 v0, 0x3e

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/XX;->A03(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v0, p0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_3

    .line 64400
    const/16 v2, 0x66

    const/16 v1, 0xe

    const/16 v0, 0x56

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/XX;->A03(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v0, p0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_3

    .line 64401
    const/16 v2, 0x7a

    const/4 v1, 0x6

    const/16 v0, 0x11

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/XX;->A03(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v0, p0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_3

    .line 64402
    const/16 v2, 0x92

    const/16 v1, 0x8

    const/16 v0, 0x27

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/XX;->A03(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v0, p0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_3

    .line 64403
    const/16 v2, 0xa0

    const/16 v1, 0xd

    const/16 v0, 0x4f

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/XX;->A03(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v0, p0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_3

    .line 64404
    const/16 v2, 0x34b

    const/16 v1, 0xb

    const/16 v0, 0x59

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/XX;->A03(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v0, p0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v3

    sget-object v2, Lcom/facebook/ads/redexgen/X/XX;->A0q:[Ljava/lang/String;

    const/4 v0, 0x0

    aget-object v1, v2, v0

    const/4 v0, 0x2

    aget-object v2, v2, v0

    const/16 v0, 0x1f

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    invoke-virtual {v2, v0}, Ljava/lang/String;->charAt(I)C

    move-result v0

    if-eq v1, v0, :cond_0

    sget-object v2, Lcom/facebook/ads/redexgen/X/XX;->A0q:[Ljava/lang/String;

    const-string v1, "DfP1dlVZEdoO22ggDOih4DhJX4h9DttB"

    const/4 v0, 0x1

    aput-object v1, v2, v0

    const-string v1, "iPMsbxRLvFu5COzgJWPWeI2fmUuAHT3S"

    const/4 v0, 0x5

    aput-object v1, v2, v0

    if-nez v3, :cond_3

    .line 64405
    const/16 v2, 0x341

    const/16 v1, 0xa

    const/16 v0, 0x39

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/XX;->A03(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v0, p0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_3

    .line 64406
    const/16 v2, 0x356

    const/16 v1, 0x8

    const/16 v0, 0x77

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/XX;->A03(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v0, p0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_3

    .line 64407
    const/16 v2, 0x337

    const/16 v1, 0xa

    const/16 v0, 0x75

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/XX;->A03(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v0, p0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_3

    .line 64408
    const/16 v2, 0x32f

    const/16 v1, 0x8

    const/16 v0, 0x11

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/XX;->A03(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v0, p0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_4

    :cond_3
    const/4 v0, 0x1

    .line 64409
    :goto_0
    return v0

    .line 64410
    :cond_4
    const/4 v0, 0x0

    goto :goto_0
.end method

.method public static synthetic A0G()[B
    .locals 1

    .line 64411
    sget-object v0, Lcom/facebook/ads/redexgen/X/XX;->A0t:[B

    return-object v0
.end method

.method public static A0H([II)[I
    .locals 1

    .line 64412
    if-nez p0, :cond_0

    .line 64413
    new-array v0, p1, [I

    return-object v0

    .line 64414
    :cond_0
    array-length v0, p0

    if-lt v0, p1, :cond_1

    .line 64415
    return-object p0

    .line 64416
    :cond_1
    array-length v0, p0

    mul-int/lit8 v0, v0, 0x2

    invoke-static {v0, p1}, Ljava/lang/Math;->max(II)I

    move-result v0

    new-array v0, v0, [I

    return-object v0
.end method


# virtual methods
.method public final A0I(I)V
    .locals 8
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lcom/facebook/ads/redexgen/X/9v;
        }
    .end annotation

    .line 64417
    const/4 v3, 0x0

    const/4 v2, 0x1

    sparse-switch p1, :sswitch_data_0

    .line 64418
    :cond_0
    :goto_0
    return-void

    .line 64419
    :sswitch_0
    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0a:Z

    if-nez v0, :cond_0

    .line 64420
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/XX;->A0N:Lcom/facebook/ads/redexgen/X/Bu;

    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/XX;->A02()Lcom/facebook/ads/redexgen/X/C1;

    move-result-object v0

    invoke-interface {v1, v0}, Lcom/facebook/ads/redexgen/X/Bu;->AFi(Lcom/facebook/ads/redexgen/X/C1;)V

    .line 64421
    iput-boolean v2, p0, Lcom/facebook/ads/redexgen/X/XX;->A0a:Z

    goto :goto_0

    .line 64422
    :sswitch_1
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0c:Landroid/util/SparseArray;

    invoke-virtual {v0}, Landroid/util/SparseArray;->size()I

    move-result v0

    if-eqz v0, :cond_5

    .line 64423
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0N:Lcom/facebook/ads/redexgen/X/Bu;

    invoke-interface {v0}, Lcom/facebook/ads/redexgen/X/Bu;->A5Y()V

    .line 64424
    goto :goto_0

    .line 64425
    :sswitch_2
    iget-wide v1, p0, Lcom/facebook/ads/redexgen/X/XX;->A0M:J

    const-wide v3, -0x7fffffffffffffffL    # -4.9E-324

    cmp-long v0, v1, v3

    if-nez v0, :cond_1

    .line 64426
    const-wide/32 v0, 0xf4240

    iput-wide v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0M:J

    .line 64427
    :cond_1
    iget-wide v1, p0, Lcom/facebook/ads/redexgen/X/XX;->A0G:J

    cmp-long v0, v1, v3

    if-eqz v0, :cond_0

    .line 64428
    invoke-direct {p0, v1, v2}, Lcom/facebook/ads/redexgen/X/XX;->A01(J)J

    move-result-wide v0

    iput-wide v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0H:J

    goto :goto_0

    .line 64429
    :sswitch_3
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0O:Lcom/facebook/ads/redexgen/X/CE;

    iget-boolean v0, v0, Lcom/facebook/ads/redexgen/X/CE;->A0c:Z

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0O:Lcom/facebook/ads/redexgen/X/CE;

    iget-object v0, v0, Lcom/facebook/ads/redexgen/X/CE;->A0f:[B

    if-nez v0, :cond_6

    goto :goto_0

    .line 64430
    :sswitch_4
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0O:Lcom/facebook/ads/redexgen/X/CE;

    iget-boolean v0, v0, Lcom/facebook/ads/redexgen/X/CE;->A0c:Z

    if-eqz v0, :cond_0

    .line 64431
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0O:Lcom/facebook/ads/redexgen/X/CE;

    iget-object v0, v0, Lcom/facebook/ads/redexgen/X/CE;->A0V:Lcom/facebook/ads/redexgen/X/C3;

    if-eqz v0, :cond_7

    .line 64432
    iget-object v7, p0, Lcom/facebook/ads/redexgen/X/XX;->A0O:Lcom/facebook/ads/redexgen/X/CE;

    new-array v6, v2, [Lcom/facebook/ads/internal/exoplayer2/thirdparty/drm/DrmInitData$SchemeData;

    sget-object v5, Lcom/facebook/ads/redexgen/X/9W;->A04:Ljava/util/UUID;

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0O:Lcom/facebook/ads/redexgen/X/CE;

    iget-object v0, v0, Lcom/facebook/ads/redexgen/X/CE;->A0V:Lcom/facebook/ads/redexgen/X/C3;

    iget-object v4, v0, Lcom/facebook/ads/redexgen/X/C3;->A03:[B

    const/16 v2, 0x400

    const/16 v1, 0xa

    const/16 v0, 0x3f

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/XX;->A03(III)Ljava/lang/String;

    move-result-object v1

    new-instance v0, Lcom/facebook/ads/internal/exoplayer2/thirdparty/drm/DrmInitData$SchemeData;

    invoke-direct {v0, v5, v1, v4}, Lcom/facebook/ads/internal/exoplayer2/thirdparty/drm/DrmInitData$SchemeData;-><init>(Ljava/util/UUID;Ljava/lang/String;[B)V

    aput-object v0, v6, v3

    new-instance v0, Lcom/facebook/ads/internal/exoplayer2/thirdparty/drm/DrmInitData;

    invoke-direct {v0, v6}, Lcom/facebook/ads/internal/exoplayer2/thirdparty/drm/DrmInitData;-><init>([Lcom/facebook/ads/internal/exoplayer2/thirdparty/drm/DrmInitData$SchemeData;)V

    iput-object v0, v7, Lcom/facebook/ads/redexgen/X/CE;->A0U:Lcom/facebook/ads/internal/exoplayer2/thirdparty/drm/DrmInitData;

    goto :goto_0

    .line 64433
    :sswitch_5
    iget v5, p0, Lcom/facebook/ads/redexgen/X/XX;->A0B:I

    const/4 v0, -0x1

    if-eq v5, v0, :cond_8

    iget-wide v3, p0, Lcom/facebook/ads/redexgen/X/XX;->A0I:J

    const-wide/16 v1, -0x1

    cmp-long v0, v3, v1

    if-eqz v0, :cond_8

    .line 64434
    const v0, 0x1c53bb6b

    if-ne v5, v0, :cond_0

    .line 64435
    iput-wide v3, p0, Lcom/facebook/ads/redexgen/X/XX;->A0F:J

    goto/16 :goto_0

    .line 64436
    :sswitch_6
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0O:Lcom/facebook/ads/redexgen/X/CE;

    iget-object v0, v0, Lcom/facebook/ads/redexgen/X/CE;->A0Y:Ljava/lang/String;

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/XX;->A0F(Ljava/lang/String;)Z

    move-result v0

    if-eqz v0, :cond_2

    .line 64437
    iget-object v2, p0, Lcom/facebook/ads/redexgen/X/XX;->A0O:Lcom/facebook/ads/redexgen/X/CE;

    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/XX;->A0N:Lcom/facebook/ads/redexgen/X/Bu;

    iget v0, v2, Lcom/facebook/ads/redexgen/X/CE;->A0N:I

    invoke-virtual {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/CE;->A09(Lcom/facebook/ads/redexgen/X/Bu;I)V

    .line 64438
    iget-object v2, p0, Lcom/facebook/ads/redexgen/X/XX;->A0c:Landroid/util/SparseArray;

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0O:Lcom/facebook/ads/redexgen/X/CE;

    iget v1, v0, Lcom/facebook/ads/redexgen/X/CE;->A0N:I

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0O:Lcom/facebook/ads/redexgen/X/CE;

    invoke-virtual {v2, v1, v0}, Landroid/util/SparseArray;->put(ILjava/lang/Object;)V

    .line 64439
    :cond_2
    const/4 v0, 0x0

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0O:Lcom/facebook/ads/redexgen/X/CE;

    .line 64440
    goto/16 :goto_0

    .line 64441
    :sswitch_7
    iget v1, p0, Lcom/facebook/ads/redexgen/X/XX;->A04:I

    const/4 v0, 0x2

    if-eq v1, v0, :cond_3

    .line 64442
    return-void

    .line 64443
    :cond_3
    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0W:Z

    if-nez v0, :cond_4

    .line 64444
    iget v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A01:I

    or-int/2addr v2, v0

    iput v2, p0, Lcom/facebook/ads/redexgen/X/XX;->A01:I

    .line 64445
    :cond_4
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/XX;->A0c:Landroid/util/SparseArray;

    iget v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A05:I

    invoke-virtual {v1, v0}, Landroid/util/SparseArray;->get(I)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/facebook/ads/redexgen/X/CE;

    iget-wide v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0D:J

    invoke-direct {p0, v2, v0, v1}, Lcom/facebook/ads/redexgen/X/XX;->A0B(Lcom/facebook/ads/redexgen/X/CE;J)V

    .line 64446
    iput v3, p0, Lcom/facebook/ads/redexgen/X/XX;->A04:I

    .line 64447
    goto/16 :goto_0

    .line 64448
    :cond_5
    const/16 v2, 0x2f4

    const/16 v1, 0x1a

    const/16 v0, 0x40

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/XX;->A03(III)Ljava/lang/String;

    move-result-object v1

    new-instance v0, Lcom/facebook/ads/redexgen/X/9v;

    invoke-direct {v0, v1}, Lcom/facebook/ads/redexgen/X/9v;-><init>(Ljava/lang/String;)V

    throw v0

    .line 64449
    :cond_6
    const/16 v2, 0xf3

    const/16 v1, 0x35

    const/16 v0, 0xa

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/XX;->A03(III)Ljava/lang/String;

    move-result-object v1

    new-instance v0, Lcom/facebook/ads/redexgen/X/9v;

    invoke-direct {v0, v1}, Lcom/facebook/ads/redexgen/X/9v;-><init>(Ljava/lang/String;)V

    throw v0

    .line 64450
    :cond_7
    const/16 v2, 0x1c1

    const/16 v1, 0x37

    const/16 v0, 0x73

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/XX;->A03(III)Ljava/lang/String;

    move-result-object v1

    new-instance v0, Lcom/facebook/ads/redexgen/X/9v;

    invoke-direct {v0, v1}, Lcom/facebook/ads/redexgen/X/9v;-><init>(Ljava/lang/String;)V

    throw v0

    .line 64451
    :cond_8
    const/16 v2, 0x29b

    const/16 v1, 0x32

    const/16 v0, 0x1a

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/XX;->A03(III)Ljava/lang/String;

    move-result-object v1

    new-instance v0, Lcom/facebook/ads/redexgen/X/9v;

    invoke-direct {v0, v1}, Lcom/facebook/ads/redexgen/X/9v;-><init>(Ljava/lang/String;)V

    throw v0

    :sswitch_data_0
    .sparse-switch
        0xa0 -> :sswitch_7
        0xae -> :sswitch_6
        0x4dbb -> :sswitch_5
        0x6240 -> :sswitch_4
        0x6d80 -> :sswitch_3
        0x1549a966 -> :sswitch_2
        0x1654ae6b -> :sswitch_1
        0x1c53bb6b -> :sswitch_0
    .end sparse-switch
.end method

.method public final A0J(ID)V
    .locals 2

    .line 64452
    sparse-switch p1, :sswitch_data_0

    .line 64453
    :goto_0
    return-void

    .line 64454
    :sswitch_0
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/XX;->A0O:Lcom/facebook/ads/redexgen/X/CE;

    double-to-float v0, p2

    iput v0, v1, Lcom/facebook/ads/redexgen/X/CE;->A01:F

    .line 64455
    goto :goto_0

    .line 64456
    :sswitch_1
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/XX;->A0O:Lcom/facebook/ads/redexgen/X/CE;

    double-to-float v0, p2

    iput v0, v1, Lcom/facebook/ads/redexgen/X/CE;->A00:F

    .line 64457
    goto :goto_0

    .line 64458
    :sswitch_2
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/XX;->A0O:Lcom/facebook/ads/redexgen/X/CE;

    double-to-float v0, p2

    iput v0, v1, Lcom/facebook/ads/redexgen/X/CE;->A09:F

    .line 64459
    goto :goto_0

    .line 64460
    :sswitch_3
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/XX;->A0O:Lcom/facebook/ads/redexgen/X/CE;

    double-to-float v0, p2

    iput v0, v1, Lcom/facebook/ads/redexgen/X/CE;->A08:F

    .line 64461
    goto :goto_0

    .line 64462
    :sswitch_4
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/XX;->A0O:Lcom/facebook/ads/redexgen/X/CE;

    double-to-float v0, p2

    iput v0, v1, Lcom/facebook/ads/redexgen/X/CE;->A03:F

    .line 64463
    goto :goto_0

    .line 64464
    :sswitch_5
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/XX;->A0O:Lcom/facebook/ads/redexgen/X/CE;

    double-to-float v0, p2

    iput v0, v1, Lcom/facebook/ads/redexgen/X/CE;->A02:F

    .line 64465
    goto :goto_0

    .line 64466
    :sswitch_6
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/XX;->A0O:Lcom/facebook/ads/redexgen/X/CE;

    double-to-float v0, p2

    iput v0, v1, Lcom/facebook/ads/redexgen/X/CE;->A05:F

    .line 64467
    goto :goto_0

    .line 64468
    :sswitch_7
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/XX;->A0O:Lcom/facebook/ads/redexgen/X/CE;

    double-to-float v0, p2

    iput v0, v1, Lcom/facebook/ads/redexgen/X/CE;->A04:F

    .line 64469
    goto :goto_0

    .line 64470
    :sswitch_8
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/XX;->A0O:Lcom/facebook/ads/redexgen/X/CE;

    double-to-float v0, p2

    iput v0, v1, Lcom/facebook/ads/redexgen/X/CE;->A07:F

    .line 64471
    goto :goto_0

    .line 64472
    :sswitch_9
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/XX;->A0O:Lcom/facebook/ads/redexgen/X/CE;

    double-to-float v0, p2

    iput v0, v1, Lcom/facebook/ads/redexgen/X/CE;->A06:F

    .line 64473
    goto :goto_0

    .line 64474
    :sswitch_a
    double-to-long v0, p2

    iput-wide v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0G:J

    .line 64475
    goto :goto_0

    .line 64476
    :sswitch_b
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/XX;->A0O:Lcom/facebook/ads/redexgen/X/CE;

    double-to-int v0, p2

    iput v0, v1, Lcom/facebook/ads/redexgen/X/CE;->A0O:I

    .line 64477
    goto :goto_0

    :sswitch_data_0
    .sparse-switch
        0xb5 -> :sswitch_b
        0x4489 -> :sswitch_a
        0x55d1 -> :sswitch_9
        0x55d2 -> :sswitch_8
        0x55d3 -> :sswitch_7
        0x55d4 -> :sswitch_6
        0x55d5 -> :sswitch_5
        0x55d6 -> :sswitch_4
        0x55d7 -> :sswitch_3
        0x55d8 -> :sswitch_2
        0x55d9 -> :sswitch_1
        0x55da -> :sswitch_0
    .end sparse-switch
.end method

.method public final A0K(IILcom/facebook/ads/redexgen/X/Bt;)V
    .locals 19
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;,
            Ljava/lang/InterruptedException;
        }
    .end annotation

    .line 64478
    move/from16 v9, p2

    move-object/from16 v6, p0

    const/4 v2, 0x0

    const/4 v10, 0x1

    move/from16 v18, p1

    move-object/from16 v7, p3

    sparse-switch v18, :sswitch_data_0

    .line 64479
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const/16 v2, 0x35e

    const/16 v1, 0xf

    const/4 v0, 0x7

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/XX;->A03(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    move/from16 v0, v18

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    new-instance v0, Lcom/facebook/ads/redexgen/X/9v;

    invoke-direct {v0, v1}, Lcom/facebook/ads/redexgen/X/9v;-><init>(Ljava/lang/String;)V

    throw v0

    .line 64480
    :sswitch_0
    iget-object v1, v6, Lcom/facebook/ads/redexgen/X/XX;->A0O:Lcom/facebook/ads/redexgen/X/CE;

    new-array v0, v9, [B

    iput-object v0, v1, Lcom/facebook/ads/redexgen/X/CE;->A0e:[B

    .line 64481
    iget-object v0, v6, Lcom/facebook/ads/redexgen/X/XX;->A0O:Lcom/facebook/ads/redexgen/X/CE;

    iget-object v0, v0, Lcom/facebook/ads/redexgen/X/CE;->A0e:[B

    invoke-interface {v7, v0, v2, v9}, Lcom/facebook/ads/redexgen/X/Bt;->readFully([BII)V

    .line 64482
    goto/16 :goto_e

    .line 64483
    :sswitch_1
    iget-object v1, v6, Lcom/facebook/ads/redexgen/X/XX;->A0O:Lcom/facebook/ads/redexgen/X/CE;

    new-array v0, v9, [B

    iput-object v0, v1, Lcom/facebook/ads/redexgen/X/CE;->A0d:[B

    .line 64484
    iget-object v0, v6, Lcom/facebook/ads/redexgen/X/XX;->A0O:Lcom/facebook/ads/redexgen/X/CE;

    iget-object v0, v0, Lcom/facebook/ads/redexgen/X/CE;->A0d:[B

    invoke-interface {v7, v0, v2, v9}, Lcom/facebook/ads/redexgen/X/Bt;->readFully([BII)V

    .line 64485
    goto/16 :goto_e

    .line 64486
    :sswitch_2
    iget-object v0, v6, Lcom/facebook/ads/redexgen/X/XX;->A0l:Lcom/facebook/ads/redexgen/X/Hz;

    iget-object v0, v0, Lcom/facebook/ads/redexgen/X/Hz;->A00:[B

    invoke-static {v0, v2}, Ljava/util/Arrays;->fill([BB)V

    .line 64487
    iget-object v0, v6, Lcom/facebook/ads/redexgen/X/XX;->A0l:Lcom/facebook/ads/redexgen/X/Hz;

    iget-object v1, v0, Lcom/facebook/ads/redexgen/X/Hz;->A00:[B

    rsub-int/lit8 v0, v9, 0x4

    invoke-interface {v7, v1, v0, v9}, Lcom/facebook/ads/redexgen/X/Bt;->readFully([BII)V

    .line 64488
    iget-object v0, v6, Lcom/facebook/ads/redexgen/X/XX;->A0l:Lcom/facebook/ads/redexgen/X/Hz;

    invoke-virtual {v0, v2}, Lcom/facebook/ads/redexgen/X/Hz;->A0Y(I)V

    .line 64489
    iget-object v0, v6, Lcom/facebook/ads/redexgen/X/XX;->A0l:Lcom/facebook/ads/redexgen/X/Hz;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/Hz;->A0M()J

    move-result-wide v1

    long-to-int v0, v1

    iput v0, v6, Lcom/facebook/ads/redexgen/X/XX;->A0B:I

    .line 64490
    goto/16 :goto_e

    .line 64491
    :sswitch_3
    new-array v3, v9, [B

    .line 64492
    .local v6, "encryptionKey":[B
    invoke-interface {v7, v3, v2, v9}, Lcom/facebook/ads/redexgen/X/Bt;->readFully([BII)V

    .line 64493
    iget-object v1, v6, Lcom/facebook/ads/redexgen/X/XX;->A0O:Lcom/facebook/ads/redexgen/X/CE;

    new-instance v0, Lcom/facebook/ads/redexgen/X/C3;

    invoke-direct {v0, v10, v3, v2, v2}, Lcom/facebook/ads/redexgen/X/C3;-><init>(I[BII)V

    iput-object v0, v1, Lcom/facebook/ads/redexgen/X/CE;->A0V:Lcom/facebook/ads/redexgen/X/C3;

    .line 64494
    goto/16 :goto_e

    .line 64495
    .end local v6    # "encryptionKey":[B
    :sswitch_4
    iget-object v1, v6, Lcom/facebook/ads/redexgen/X/XX;->A0O:Lcom/facebook/ads/redexgen/X/CE;

    new-array v0, v9, [B

    iput-object v0, v1, Lcom/facebook/ads/redexgen/X/CE;->A0f:[B

    .line 64496
    iget-object v0, v6, Lcom/facebook/ads/redexgen/X/XX;->A0O:Lcom/facebook/ads/redexgen/X/CE;

    iget-object v4, v0, Lcom/facebook/ads/redexgen/X/CE;->A0f:[B

    sget-object v3, Lcom/facebook/ads/redexgen/X/XX;->A0q:[Ljava/lang/String;

    const/4 v0, 0x1

    aget-object v1, v3, v0

    const/4 v0, 0x5

    aget-object v3, v3, v0

    const/16 v0, 0xf

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    invoke-virtual {v3, v0}, Ljava/lang/String;->charAt(I)C

    move-result v0

    if-eq v1, v0, :cond_0

    goto/16 :goto_7

    :cond_0
    sget-object v3, Lcom/facebook/ads/redexgen/X/XX;->A0q:[Ljava/lang/String;

    const-string v1, "apQqLvL9mVkkZpyD1gGQRufbtxV"

    const/4 v0, 0x6

    aput-object v1, v3, v0

    invoke-interface {v7, v4, v2, v9}, Lcom/facebook/ads/redexgen/X/Bt;->readFully([BII)V

    .line 64497
    goto/16 :goto_e

    .line 64498
    :sswitch_5
    iget v0, v6, Lcom/facebook/ads/redexgen/X/XX;->A04:I

    const/16 v8, 0x8

    if-nez v0, :cond_1

    .line 64499
    iget-object v0, v6, Lcom/facebook/ads/redexgen/X/XX;->A0e:Lcom/facebook/ads/redexgen/X/CH;

    invoke-virtual {v0, v7, v2, v10, v8}, Lcom/facebook/ads/redexgen/X/CH;->A05(Lcom/facebook/ads/redexgen/X/Bt;ZZI)J

    move-result-wide v0

    long-to-int v3, v0

    iput v3, v6, Lcom/facebook/ads/redexgen/X/XX;->A05:I

    .line 64500
    iget-object v0, v6, Lcom/facebook/ads/redexgen/X/XX;->A0e:Lcom/facebook/ads/redexgen/X/CH;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/CH;->A04()I

    move-result v0

    iput v0, v6, Lcom/facebook/ads/redexgen/X/XX;->A06:I

    .line 64501
    const-wide v0, -0x7fffffffffffffffL    # -4.9E-324

    iput-wide v0, v6, Lcom/facebook/ads/redexgen/X/XX;->A0C:J

    .line 64502
    iput v10, v6, Lcom/facebook/ads/redexgen/X/XX;->A04:I

    .line 64503
    iget-object v0, v6, Lcom/facebook/ads/redexgen/X/XX;->A0k:Lcom/facebook/ads/redexgen/X/Hz;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/Hz;->A0V()V

    .line 64504
    :cond_1
    iget-object v1, v6, Lcom/facebook/ads/redexgen/X/XX;->A0c:Landroid/util/SparseArray;

    iget v0, v6, Lcom/facebook/ads/redexgen/X/XX;->A05:I

    invoke-virtual {v1, v0}, Landroid/util/SparseArray;->get(I)Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Lcom/facebook/ads/redexgen/X/CE;

    .line 64505
    .local v6, "track":Lcom/facebook/ads/redexgen/X/CE;
    if-nez v5, :cond_2

    .line 64506
    iget v0, v6, Lcom/facebook/ads/redexgen/X/XX;->A06:I

    sub-int/2addr v9, v0

    invoke-interface {v7, v9}, Lcom/facebook/ads/redexgen/X/Bt;->AGP(I)V

    .line 64507
    iput v2, v6, Lcom/facebook/ads/redexgen/X/XX;->A04:I

    .line 64508
    return-void

    .line 64509
    :cond_2
    iget v0, v6, Lcom/facebook/ads/redexgen/X/XX;->A04:I

    const/16 v1, 0xa3

    if-ne v0, v10, :cond_4

    .line 64510
    const/4 v13, 0x3

    invoke-direct {v6, v7, v13}, Lcom/facebook/ads/redexgen/X/XX;->A07(Lcom/facebook/ads/redexgen/X/Bt;I)V

    .line 64511
    iget-object v0, v6, Lcom/facebook/ads/redexgen/X/XX;->A0k:Lcom/facebook/ads/redexgen/X/Hz;

    iget-object v0, v0, Lcom/facebook/ads/redexgen/X/Hz;->A00:[B

    const/4 v12, 0x2

    aget-byte v0, v0, v12

    and-int/lit8 v11, v0, 0x6

    shr-int/2addr v11, v10

    .line 64512
    .local v10, "lacing":I
    const/16 v4, 0xff

    if-nez v11, :cond_a

    .line 64513
    iput v10, v6, Lcom/facebook/ads/redexgen/X/XX;->A02:I

    .line 64514
    iget-object v0, v6, Lcom/facebook/ads/redexgen/X/XX;->A0b:[I

    invoke-static {v0, v10}, Lcom/facebook/ads/redexgen/X/XX;->A0H([II)[I

    move-result-object v1

    iput-object v1, v6, Lcom/facebook/ads/redexgen/X/XX;->A0b:[I

    .line 64515
    iget v0, v6, Lcom/facebook/ads/redexgen/X/XX;->A06:I

    sub-int/2addr v9, v0

    sub-int/2addr v9, v13

    aput v9, v1, v2

    .line 64516
    :goto_0
    iget-object v0, v6, Lcom/facebook/ads/redexgen/X/XX;->A0k:Lcom/facebook/ads/redexgen/X/Hz;

    iget-object v1, v0, Lcom/facebook/ads/redexgen/X/Hz;->A00:[B

    const/4 v0, 0x0

    aget-byte v9, v1, v0

    shl-int/2addr v9, v8

    iget-object v0, v6, Lcom/facebook/ads/redexgen/X/XX;->A0k:Lcom/facebook/ads/redexgen/X/Hz;

    iget-object v1, v0, Lcom/facebook/ads/redexgen/X/Hz;->A00:[B

    const/4 v0, 0x1

    aget-byte v0, v1, v0

    and-int/2addr v0, v4

    or-int/2addr v9, v0

    .line 64517
    .local v4, "timecode":I
    iget-wide v2, v6, Lcom/facebook/ads/redexgen/X/XX;->A0E:J

    int-to-long v0, v9

    invoke-direct {v6, v0, v1}, Lcom/facebook/ads/redexgen/X/XX;->A01(J)J

    move-result-wide v0

    add-long/2addr v2, v0

    iput-wide v2, v6, Lcom/facebook/ads/redexgen/X/XX;->A0D:J

    .line 64518
    iget-object v0, v6, Lcom/facebook/ads/redexgen/X/XX;->A0k:Lcom/facebook/ads/redexgen/X/Hz;

    iget-object v0, v0, Lcom/facebook/ads/redexgen/X/Hz;->A00:[B

    const/4 v3, 0x2

    aget-byte v0, v0, v3

    and-int/2addr v0, v8

    if-ne v0, v8, :cond_9

    const/4 v2, 0x1

    .line 64519
    .local v5, "isInvisible":Z
    :goto_1
    iget v0, v5, Lcom/facebook/ads/redexgen/X/CE;->A0Q:I

    if-eq v0, v3, :cond_3

    const/16 v1, 0xa3

    move/from16 v0, v18

    if-ne v0, v1, :cond_8

    iget-object v0, v6, Lcom/facebook/ads/redexgen/X/XX;->A0k:Lcom/facebook/ads/redexgen/X/Hz;

    iget-object v0, v0, Lcom/facebook/ads/redexgen/X/Hz;->A00:[B

    aget-byte v1, v0, v3

    const/16 v0, 0x80

    and-int/2addr v1, v0

    if-ne v1, v0, :cond_8

    :cond_3
    const/4 v0, 0x1

    .line 64520
    .local v7, "isKeyframe":Z
    :goto_2
    if-eqz v0, :cond_7

    const/4 v1, 0x1

    .line 64521
    :goto_3
    if-eqz v2, :cond_6

    const/high16 v0, -0x80000000

    :goto_4
    or-int/2addr v1, v0

    iput v1, v6, Lcom/facebook/ads/redexgen/X/XX;->A01:I

    .line 64522
    const/4 v0, 0x2

    iput v0, v6, Lcom/facebook/ads/redexgen/X/XX;->A04:I

    .line 64523
    const/4 v0, 0x0

    iput v0, v6, Lcom/facebook/ads/redexgen/X/XX;->A03:I

    .line 64524
    .end local v10    # "lacing":I
    :cond_4
    const/16 v1, 0xa3

    move/from16 v0, v18

    if-ne v0, v1, :cond_19

    .line 64525
    :goto_5
    iget v1, v6, Lcom/facebook/ads/redexgen/X/XX;->A03:I

    iget v0, v6, Lcom/facebook/ads/redexgen/X/XX;->A02:I

    if-ge v1, v0, :cond_1a

    .line 64526
    iget-object v0, v6, Lcom/facebook/ads/redexgen/X/XX;->A0b:[I

    aget v0, v0, v1

    invoke-direct {v6, v7, v5, v0}, Lcom/facebook/ads/redexgen/X/XX;->A08(Lcom/facebook/ads/redexgen/X/Bt;Lcom/facebook/ads/redexgen/X/CE;I)V

    .line 64527
    iget-wide v2, v6, Lcom/facebook/ads/redexgen/X/XX;->A0D:J

    iget v1, v6, Lcom/facebook/ads/redexgen/X/XX;->A03:I

    iget v0, v5, Lcom/facebook/ads/redexgen/X/CE;->A0F:I

    mul-int/2addr v1, v0

    div-int/lit16 v0, v1, 0x3e8

    int-to-long v0, v0

    add-long/2addr v2, v0

    .line 64528
    .local v4, "sampleTimeUs":J
    invoke-direct {v6, v5, v2, v3}, Lcom/facebook/ads/redexgen/X/XX;->A0B(Lcom/facebook/ads/redexgen/X/CE;J)V

    .line 64529
    iget v3, v6, Lcom/facebook/ads/redexgen/X/XX;->A03:I

    const/4 v4, 0x1

    sget-object v2, Lcom/facebook/ads/redexgen/X/XX;->A0q:[Ljava/lang/String;

    const/4 v0, 0x3

    aget-object v1, v2, v0

    const/4 v0, 0x4

    aget-object v0, v2, v0

    invoke-virtual {v1}, Ljava/lang/String;->length()I

    move-result v1

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v0

    if-eq v1, v0, :cond_5

    sget-object v2, Lcom/facebook/ads/redexgen/X/XX;->A0q:[Ljava/lang/String;

    const-string v1, "D8CgfCqU2RyOXDgpSzfJOF"

    const/4 v0, 0x3

    aput-object v1, v2, v0

    const-string v1, "qE8wnFvtme9ZmNp"

    const/4 v0, 0x4

    aput-object v1, v2, v0

    add-int/2addr v3, v4

    iput v3, v6, Lcom/facebook/ads/redexgen/X/XX;->A03:I

    .line 64530
    .end local v4    # "sampleTimeUs":J
    goto :goto_5

    :cond_5
    sget-object v2, Lcom/facebook/ads/redexgen/X/XX;->A0q:[Ljava/lang/String;

    const-string v1, "QTaMrDGiQsJaoOEX9JrEklVzvDfFq8um"

    const/4 v0, 0x0

    aput-object v1, v2, v0

    const-string v1, "wfV4ykUdaNXbzihAU76Od3hvCzrHPeln"

    const/4 v0, 0x2

    aput-object v1, v2, v0

    add-int/2addr v3, v4

    iput v3, v6, Lcom/facebook/ads/redexgen/X/XX;->A03:I

    .end local v4
    goto :goto_5

    .line 64531
    :cond_6
    const/4 v0, 0x0

    goto :goto_4

    .line 64532
    :cond_7
    const/4 v1, 0x0

    goto :goto_3

    .line 64533
    :cond_8
    const/4 v0, 0x0

    goto :goto_2

    .line 64534
    :cond_9
    const/4 v2, 0x0

    goto/16 :goto_1

    .line 64535
    :cond_a
    move/from16 v0, v18

    if-ne v0, v1, :cond_1e

    .line 64536
    const/4 v14, 0x4

    invoke-direct {v6, v7, v14}, Lcom/facebook/ads/redexgen/X/XX;->A07(Lcom/facebook/ads/redexgen/X/Bt;I)V

    .line 64537
    iget-object v0, v6, Lcom/facebook/ads/redexgen/X/XX;->A0k:Lcom/facebook/ads/redexgen/X/Hz;

    iget-object v0, v0, Lcom/facebook/ads/redexgen/X/Hz;->A00:[B

    aget-byte v3, v0, v13

    and-int/2addr v3, v4

    add-int/2addr v3, v10

    iput v3, v6, Lcom/facebook/ads/redexgen/X/XX;->A02:I

    .line 64538
    iget-object v1, v6, Lcom/facebook/ads/redexgen/X/XX;->A0b:[I

    sget-object v15, Lcom/facebook/ads/redexgen/X/XX;->A0q:[Ljava/lang/String;

    const/4 v0, 0x0

    aget-object v16, v15, v0

    const/4 v0, 0x2

    aget-object v17, v15, v0

    const/16 v15, 0x1f

    move-object/from16 v0, v16

    invoke-virtual {v0, v15}, Ljava/lang/String;->charAt(I)C

    move-result v16

    move-object/from16 v0, v17

    invoke-virtual {v0, v15}, Ljava/lang/String;->charAt(I)C

    move-result v15

    move/from16 v0, v16

    if-eq v0, v15, :cond_b

    .line 64539
    sget-object v16, Lcom/facebook/ads/redexgen/X/XX;->A0q:[Ljava/lang/String;

    const-string v15, "RwuJZhEYbPwrYhgspkpAZU"

    const/4 v0, 0x3

    aput-object v15, v16, v0

    const-string v15, "DTAVHXJ2rXJnopR"

    const/4 v0, 0x4

    aput-object v15, v16, v0

    invoke-static {v1, v3}, Lcom/facebook/ads/redexgen/X/XX;->A0H([II)[I

    move-result-object v3

    iput-object v3, v6, Lcom/facebook/ads/redexgen/X/XX;->A0b:[I

    .line 64540
    if-ne v11, v12, :cond_d

    .line 64541
    :goto_6
    iget v0, v6, Lcom/facebook/ads/redexgen/X/XX;->A06:I

    sub-int/2addr v9, v0

    sub-int/2addr v9, v14

    iget v11, v6, Lcom/facebook/ads/redexgen/X/XX;->A02:I

    sget-object v10, Lcom/facebook/ads/redexgen/X/XX;->A0q:[Ljava/lang/String;

    const/4 v0, 0x1

    aget-object v1, v10, v0

    const/4 v0, 0x5

    aget-object v10, v10, v0

    const/16 v0, 0xf

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    invoke-virtual {v10, v0}, Ljava/lang/String;->charAt(I)C

    move-result v0

    if-eq v1, v0, :cond_c

    .line 64542
    :goto_7
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    .line 64543
    :cond_b
    invoke-static {v1, v3}, Lcom/facebook/ads/redexgen/X/XX;->A0H([II)[I

    move-result-object v3

    iput-object v3, v6, Lcom/facebook/ads/redexgen/X/XX;->A0b:[I

    .line 64544
    if-ne v11, v12, :cond_d

    goto :goto_6

    :cond_c
    sget-object v10, Lcom/facebook/ads/redexgen/X/XX;->A0q:[Ljava/lang/String;

    const-string v1, "RQftDwpqwbIXAF1g5dDgy2XjM3R9Fufd"

    const/4 v0, 0x1

    aput-object v1, v10, v0

    const-string v1, "50lzYqnE8by0N6RgVVPD3M33x9kk99sn"

    const/4 v0, 0x5

    aput-object v1, v10, v0

    div-int/2addr v9, v11

    .line 64545
    .local v8, "blockLacingSampleSize":I
    invoke-static {v3, v2, v11, v9}, Ljava/util/Arrays;->fill([IIII)V

    .line 64546
    .end local v8    # "blockLacingSampleSize":I
    goto/16 :goto_0

    :cond_d
    if-ne v11, v10, :cond_10

    .line 64547
    const/4 v13, 0x0

    .line 64548
    .local v8, "totalSamplesSize":I
    const/4 v11, 0x4

    .line 64549
    .local v13, "headerSize":I
    const/4 v12, 0x0

    .local v14, "sampleIndex":I
    :goto_8
    iget v3, v6, Lcom/facebook/ads/redexgen/X/XX;->A02:I

    add-int/lit8 v0, v3, -0x1

    if-ge v12, v0, :cond_f

    .line 64550
    iget-object v0, v6, Lcom/facebook/ads/redexgen/X/XX;->A0b:[I

    aput v2, v0, v12

    .line 64551
    :cond_e
    add-int/2addr v11, v10

    invoke-direct {v6, v7, v11}, Lcom/facebook/ads/redexgen/X/XX;->A07(Lcom/facebook/ads/redexgen/X/Bt;I)V

    .line 64552
    iget-object v0, v6, Lcom/facebook/ads/redexgen/X/XX;->A0k:Lcom/facebook/ads/redexgen/X/Hz;

    iget-object v1, v0, Lcom/facebook/ads/redexgen/X/Hz;->A00:[B

    add-int/lit8 v0, v11, -0x1

    aget-byte v3, v1, v0

    and-int/2addr v3, v4

    .line 64553
    .local v9, "byteValue":I
    iget-object v1, v6, Lcom/facebook/ads/redexgen/X/XX;->A0b:[I

    aget v0, v1, v12

    add-int/2addr v0, v3

    aput v0, v1, v12

    .line 64554
    if-eq v3, v4, :cond_e

    .line 64555
    add-int/2addr v13, v0

    .line 64556
    .end local v9    # "byteValue":I
    add-int/lit8 v12, v12, 0x1

    goto :goto_8

    .line 64557
    .end local v14    # "sampleIndex":I
    :cond_f
    iget-object v1, v6, Lcom/facebook/ads/redexgen/X/XX;->A0b:[I

    sub-int/2addr v3, v10

    iget v0, v6, Lcom/facebook/ads/redexgen/X/XX;->A06:I

    sub-int/2addr v9, v0

    sub-int/2addr v9, v11

    sub-int/2addr v9, v13

    aput v9, v1, v3

    .line 64558
    .end local v8    # "totalSamplesSize":I
    .end local v13    # "headerSize":I
    goto/16 :goto_0

    :cond_10
    if-ne v11, v13, :cond_1d

    .line 64559
    const/4 v15, 0x0

    .line 64560
    .restart local v8    # "totalSamplesSize":I
    const/4 v12, 0x4

    .line 64561
    .local v9, "headerSize":I
    const/4 v11, 0x0

    .local v13, "sampleIndex":I
    :goto_9
    iget v3, v6, Lcom/facebook/ads/redexgen/X/XX;->A02:I

    add-int/lit8 v0, v3, -0x1

    if-ge v11, v0, :cond_18

    .line 64562
    iget-object v0, v6, Lcom/facebook/ads/redexgen/X/XX;->A0b:[I

    aput v2, v0, v11

    sget-object v2, Lcom/facebook/ads/redexgen/X/XX;->A0q:[Ljava/lang/String;

    const/4 v0, 0x1

    aget-object v1, v2, v0

    const/4 v0, 0x5

    aget-object v2, v2, v0

    const/16 v0, 0xf

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    invoke-virtual {v2, v0}, Ljava/lang/String;->charAt(I)C

    move-result v0

    if-eq v1, v0, :cond_11

    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    .line 64563
    :cond_11
    sget-object v2, Lcom/facebook/ads/redexgen/X/XX;->A0q:[Ljava/lang/String;

    const-string v1, "AJCSp0dPwFu9XZoKvCoy3nlmL9O"

    const/4 v0, 0x6

    aput-object v1, v2, v0

    add-int/lit8 v12, v12, 0x1

    invoke-direct {v6, v7, v12}, Lcom/facebook/ads/redexgen/X/XX;->A07(Lcom/facebook/ads/redexgen/X/Bt;I)V

    .line 64564
    iget-object v0, v6, Lcom/facebook/ads/redexgen/X/XX;->A0k:Lcom/facebook/ads/redexgen/X/Hz;

    iget-object v1, v0, Lcom/facebook/ads/redexgen/X/Hz;->A00:[B

    add-int/lit8 v0, v12, -0x1

    aget-byte v0, v1, v0

    if-eqz v0, :cond_1c

    .line 64565
    const-wide/16 v2, 0x0

    .line 64566
    .local v14, "readValue":J
    const/4 v13, 0x0

    .local v11, "i":I
    :goto_a
    if-ge v13, v8, :cond_15

    .line 64567
    rsub-int/lit8 v0, v13, 0x7

    shl-int/2addr v10, v0

    .line 64568
    .local v16, "lengthMask":I
    iget-object v0, v6, Lcom/facebook/ads/redexgen/X/XX;->A0k:Lcom/facebook/ads/redexgen/X/Hz;

    iget-object v1, v0, Lcom/facebook/ads/redexgen/X/Hz;->A00:[B

    add-int/lit8 v0, v12, -0x1

    aget-byte v0, v1, v0

    and-int/2addr v0, v10

    if-eqz v0, :cond_12

    .line 64569
    add-int/lit8 v2, v12, -0x1

    .line 64570
    .local v4, "readPosition":I
    add-int/2addr v12, v13

    .line 64571
    invoke-direct {v6, v7, v12}, Lcom/facebook/ads/redexgen/X/XX;->A07(Lcom/facebook/ads/redexgen/X/Bt;I)V

    .line 64572
    iget-object v0, v6, Lcom/facebook/ads/redexgen/X/XX;->A0k:Lcom/facebook/ads/redexgen/X/Hz;

    iget-object v0, v0, Lcom/facebook/ads/redexgen/X/Hz;->A00:[B

    add-int/lit8 v1, v2, 0x1

    .end local v4    # "readPosition":I
    .local v18, "readPosition":I
    aget-byte v2, v0, v2

    and-int/2addr v2, v4

    xor-int/lit8 v0, v10, -0x1

    and-int/2addr v2, v0

    int-to-long v2, v2

    .line 64573
    .end local v18    # "readPosition":I
    .restart local v4    # "readPosition":I
    :goto_b
    if-ge v1, v12, :cond_14

    .line 64574
    shl-long/2addr v2, v8

    .line 64575
    iget-object v0, v6, Lcom/facebook/ads/redexgen/X/XX;->A0k:Lcom/facebook/ads/redexgen/X/Hz;

    iget-object v0, v0, Lcom/facebook/ads/redexgen/X/Hz;->A00:[B

    add-int/lit8 v10, v1, 0x1

    .end local v4    # "readPosition":I
    .restart local v18    # "readPosition":I
    aget-byte v0, v0, v1

    and-int/2addr v0, v4

    int-to-long v0, v0

    or-long/2addr v2, v0

    move v1, v10

    goto :goto_b

    .line 64576
    .end local v4
    .end local v16    # "lengthMask":I
    :cond_12
    add-int/lit8 v13, v13, 0x1

    sget-object v10, Lcom/facebook/ads/redexgen/X/XX;->A0q:[Ljava/lang/String;

    const/4 v0, 0x0

    aget-object v1, v10, v0

    const/4 v0, 0x2

    aget-object v10, v10, v0

    const/16 v0, 0x1f

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    invoke-virtual {v10, v0}, Ljava/lang/String;->charAt(I)C

    move-result v0

    if-eq v1, v0, :cond_13

    sget-object v10, Lcom/facebook/ads/redexgen/X/XX;->A0q:[Ljava/lang/String;

    const-string v1, "JvemuAcSz5vc2l4QyutSma8u3cOiXxC4"

    const/4 v0, 0x7

    aput-object v1, v10, v0

    const/4 v10, 0x1

    goto :goto_a

    :cond_13
    const/4 v10, 0x1

    goto :goto_a

    .line 64577
    .end local v18    # "readPosition":I
    .restart local v4    # "readPosition":I
    :cond_14
    if-lez v11, :cond_15

    .line 64578
    mul-int/lit8 v0, v13, 0x7

    add-int/lit8 v0, v0, 0x6

    const-wide/16 v13, 0x1

    shl-long v0, v13, v0

    sub-long/2addr v0, v13

    sub-long/2addr v2, v0

    .line 64579
    .end local v11    # "i":I
    :cond_15
    const-wide/32 v13, -0x80000000

    cmp-long v0, v2, v13

    if-ltz v0, :cond_1b

    const-wide/32 v0, 0x7fffffff

    cmp-long v13, v2, v0

    sget-object v1, Lcom/facebook/ads/redexgen/X/XX;->A0q:[Ljava/lang/String;

    const/4 v0, 0x7

    aget-object v1, v1, v0

    const/4 v0, 0x2

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    const/16 v0, 0x4a

    if-eq v1, v0, :cond_17

    sget-object v10, Lcom/facebook/ads/redexgen/X/XX;->A0q:[Ljava/lang/String;

    const-string v1, "iXrhrupjbccuPW1gfooQkR"

    const/4 v0, 0x3

    aput-object v1, v10, v0

    const-string v1, "StwCIbMJGGxOC39"

    const/4 v0, 0x4

    aput-object v1, v10, v0

    if-gtz v13, :cond_1b

    .line 64580
    :goto_c
    long-to-int v10, v2

    .line 64581
    .local v4, "intReadValue":I
    iget-object v1, v6, Lcom/facebook/ads/redexgen/X/XX;->A0b:[I

    .line 64582
    if-nez v11, :cond_16

    .line 64583
    :goto_d
    aput v10, v1, v11

    .line 64584
    add-int/2addr v15, v10

    .line 64585
    .end local v4    # "intReadValue":I
    .end local v14    # "readValue":J
    add-int/lit8 v11, v11, 0x1

    const/4 v2, 0x0

    const/4 v10, 0x1

    goto/16 :goto_9

    .line 64586
    :cond_16
    add-int/lit8 v0, v11, -0x1

    aget v0, v1, v0

    add-int/2addr v10, v0

    goto :goto_d

    :cond_17
    if-gtz v13, :cond_1b

    goto :goto_c

    .line 64587
    .end local v13    # "sampleIndex":I
    :cond_18
    iget-object v1, v6, Lcom/facebook/ads/redexgen/X/XX;->A0b:[I

    const/4 v0, 0x1

    sub-int/2addr v3, v0

    iget v0, v6, Lcom/facebook/ads/redexgen/X/XX;->A06:I

    sub-int/2addr v9, v0

    sub-int/2addr v9, v12

    sub-int/2addr v9, v15

    aput v9, v1, v3

    .line 64588
    .end local v8    # "totalSamplesSize":I
    .end local v9    # "headerSize":I
    goto/16 :goto_0

    .line 64589
    :cond_19
    const/4 v1, 0x0

    iget-object v0, v6, Lcom/facebook/ads/redexgen/X/XX;->A0b:[I

    aget v0, v0, v1

    invoke-direct {v6, v7, v5, v0}, Lcom/facebook/ads/redexgen/X/XX;->A08(Lcom/facebook/ads/redexgen/X/Bt;Lcom/facebook/ads/redexgen/X/CE;I)V

    .line 64590
    goto :goto_e

    .line 64591
    :cond_1a
    const/4 v0, 0x0

    iput v0, v6, Lcom/facebook/ads/redexgen/X/XX;->A04:I

    .line 64592
    .end local v6    # "track":Lcom/facebook/ads/redexgen/X/CE;
    :goto_e
    return-void

    .line 64593
    .restart local v14    # "readValue":J
    :cond_1b
    const/16 v2, 0x18c

    const/16 v1, 0x25

    const/16 v0, 0x6b

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/XX;->A03(III)Ljava/lang/String;

    move-result-object v1

    new-instance v0, Lcom/facebook/ads/redexgen/X/9v;

    invoke-direct {v0, v1}, Lcom/facebook/ads/redexgen/X/9v;-><init>(Ljava/lang/String;)V

    throw v0

    .line 64594
    .end local v14    # "readValue":J
    :cond_1c
    const/16 v2, 0x30e

    const/16 v1, 0x21

    const/16 v0, 0x1d

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/XX;->A03(III)Ljava/lang/String;

    move-result-object v1

    new-instance v0, Lcom/facebook/ads/redexgen/X/9v;

    invoke-direct {v0, v1}, Lcom/facebook/ads/redexgen/X/9v;-><init>(Ljava/lang/String;)V

    throw v0

    .line 64595
    .end local v4
    .end local v5    # "isInvisible":Z
    .end local v7    # "isKeyframe":Z
    :cond_1d
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const/16 v2, 0x36d

    const/16 v1, 0x19

    const/16 v0, 0x1e

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/XX;->A03(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, v11}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    new-instance v0, Lcom/facebook/ads/redexgen/X/9v;

    invoke-direct {v0, v1}, Lcom/facebook/ads/redexgen/X/9v;-><init>(Ljava/lang/String;)V

    throw v0

    .line 64596
    :cond_1e
    const/16 v2, 0x275

    const/16 v1, 0x26

    const/16 v0, 0x45

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/XX;->A03(III)Ljava/lang/String;

    move-result-object v1

    new-instance v0, Lcom/facebook/ads/redexgen/X/9v;

    invoke-direct {v0, v1}, Lcom/facebook/ads/redexgen/X/9v;-><init>(Ljava/lang/String;)V

    throw v0

    :sswitch_data_0
    .sparse-switch
        0xa1 -> :sswitch_5
        0xa3 -> :sswitch_5
        0x4255 -> :sswitch_4
        0x47e2 -> :sswitch_3
        0x53ab -> :sswitch_2
        0x63a2 -> :sswitch_1
        0x7672 -> :sswitch_0
    .end sparse-switch
.end method

.method public final A0L(IJ)V
    .locals 10
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lcom/facebook/ads/redexgen/X/9v;
        }
    .end annotation

    .line 64597
    const/4 v1, 0x6

    const/4 v7, 0x3

    const/4 v6, 0x2

    const/4 v3, 0x0

    const-wide/16 v8, 0x1

    const/4 v4, 0x0

    const/16 v2, 0xe

    const/16 v0, 0x15

    invoke-static {v4, v2, v0}, Lcom/facebook/ads/redexgen/X/XX;->A03(III)Ljava/lang/String;

    move-result-object v4

    const/4 v5, 0x1

    sparse-switch p1, :sswitch_data_0

    .line 64598
    :cond_0
    :goto_0
    :pswitch_0
    return-void

    .line 64599
    :sswitch_0
    iput-wide p2, p0, Lcom/facebook/ads/redexgen/X/XX;->A0M:J

    .line 64600
    goto :goto_0

    .line 64601
    :sswitch_1
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/XX;->A0O:Lcom/facebook/ads/redexgen/X/CE;

    long-to-int v0, p2

    iput v0, v1, Lcom/facebook/ads/redexgen/X/CE;->A0F:I

    .line 64602
    goto :goto_0

    .line 64603
    :sswitch_2
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/XX;->A0O:Lcom/facebook/ads/redexgen/X/CE;

    long-to-int v0, p2

    iput v0, v1, Lcom/facebook/ads/redexgen/X/CE;->A0A:I

    .line 64604
    goto :goto_0

    .line 64605
    :sswitch_3
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0O:Lcom/facebook/ads/redexgen/X/CE;

    iput-wide p2, v0, Lcom/facebook/ads/redexgen/X/CE;->A0T:J

    .line 64606
    goto :goto_0

    .line 64607
    :sswitch_4
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0O:Lcom/facebook/ads/redexgen/X/CE;

    iput-wide p2, v0, Lcom/facebook/ads/redexgen/X/CE;->A0S:J

    .line 64608
    goto :goto_0

    .line 64609
    :sswitch_5
    iget-object v4, p0, Lcom/facebook/ads/redexgen/X/XX;->A0O:Lcom/facebook/ads/redexgen/X/CE;

    long-to-int v3, p2

    sget-object v1, Lcom/facebook/ads/redexgen/X/XX;->A0q:[Ljava/lang/String;

    const/4 v0, 0x6

    aget-object v0, v1, v0

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v1

    const/16 v0, 0x1b

    if-eq v1, v0, :cond_1

    goto/16 :goto_1

    :cond_1
    sget-object v2, Lcom/facebook/ads/redexgen/X/XX;->A0q:[Ljava/lang/String;

    const-string v1, "cRzVQdX7r3sNL6uNWZqYIbMTncmWUqDN"

    const/4 v0, 0x7

    aput-object v1, v2, v0

    iput v3, v4, Lcom/facebook/ads/redexgen/X/CE;->A0L:I

    .line 64610
    goto :goto_0

    .line 64611
    :sswitch_6
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/XX;->A0O:Lcom/facebook/ads/redexgen/X/CE;

    long-to-int v0, p2

    iput v0, v1, Lcom/facebook/ads/redexgen/X/CE;->A0K:I

    .line 64612
    goto :goto_0

    .line 64613
    :sswitch_7
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0O:Lcom/facebook/ads/redexgen/X/CE;

    iput-boolean v5, v0, Lcom/facebook/ads/redexgen/X/CE;->A0b:Z

    .line 64614
    long-to-int v0, p2

    packed-switch v0, :pswitch_data_0

    goto :goto_0

    .line 64615
    :pswitch_1
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0O:Lcom/facebook/ads/redexgen/X/CE;

    iput v1, v0, Lcom/facebook/ads/redexgen/X/CE;->A0D:I

    .line 64616
    goto :goto_0

    .line 64617
    :pswitch_2
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0O:Lcom/facebook/ads/redexgen/X/CE;

    iput v5, v0, Lcom/facebook/ads/redexgen/X/CE;->A0D:I

    .line 64618
    goto :goto_0

    .line 64619
    :sswitch_8
    long-to-int v0, p2

    sparse-switch v0, :sswitch_data_1

    goto :goto_0

    .line 64620
    :sswitch_9
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/XX;->A0O:Lcom/facebook/ads/redexgen/X/CE;

    const/4 v0, 0x7

    iput v0, v1, Lcom/facebook/ads/redexgen/X/CE;->A0E:I

    .line 64621
    goto :goto_0

    .line 64622
    :sswitch_a
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0O:Lcom/facebook/ads/redexgen/X/CE;

    iput v1, v0, Lcom/facebook/ads/redexgen/X/CE;->A0E:I

    .line 64623
    goto :goto_0

    .line 64624
    :sswitch_b
    iget-object v3, p0, Lcom/facebook/ads/redexgen/X/XX;->A0O:Lcom/facebook/ads/redexgen/X/CE;

    sget-object v2, Lcom/facebook/ads/redexgen/X/XX;->A0q:[Ljava/lang/String;

    const/4 v0, 0x3

    aget-object v1, v2, v0

    const/4 v0, 0x4

    aget-object v0, v2, v0

    invoke-virtual {v1}, Ljava/lang/String;->length()I

    move-result v1

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v0

    if-eq v1, v0, :cond_3

    sget-object v2, Lcom/facebook/ads/redexgen/X/XX;->A0q:[Ljava/lang/String;

    const-string v1, "mV5wi4RdAtLICzfql5r9CviPSqv5gEzL"

    const/4 v0, 0x7

    aput-object v1, v2, v0

    iput v7, v3, Lcom/facebook/ads/redexgen/X/CE;->A0E:I

    .line 64625
    goto :goto_0

    .line 64626
    :sswitch_c
    long-to-int v0, p2

    packed-switch v0, :pswitch_data_1

    goto/16 :goto_0

    .line 64627
    :pswitch_3
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0O:Lcom/facebook/ads/redexgen/X/CE;

    iput v5, v0, Lcom/facebook/ads/redexgen/X/CE;->A0C:I

    .line 64628
    goto/16 :goto_0

    .line 64629
    :pswitch_4
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0O:Lcom/facebook/ads/redexgen/X/CE;

    iput v6, v0, Lcom/facebook/ads/redexgen/X/CE;->A0C:I

    .line 64630
    goto/16 :goto_0

    .line 64631
    :sswitch_d
    iget-object v4, p0, Lcom/facebook/ads/redexgen/X/XX;->A0O:Lcom/facebook/ads/redexgen/X/CE;

    sget-object v1, Lcom/facebook/ads/redexgen/X/XX;->A0q:[Ljava/lang/String;

    const/4 v0, 0x7

    aget-object v1, v1, v0

    const/4 v0, 0x2

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    const/16 v0, 0x4a

    if-eq v1, v0, :cond_3

    sget-object v2, Lcom/facebook/ads/redexgen/X/XX;->A0q:[Ljava/lang/String;

    const-string v1, "jzNtO6nbghsOK2zNzcbiuAHR0hMudXvD"

    const/4 v0, 0x7

    aput-object v1, v2, v0

    cmp-long v0, p2, v8

    if-nez v0, :cond_2

    const/4 v3, 0x1

    :cond_2
    iput-boolean v3, v4, Lcom/facebook/ads/redexgen/X/CE;->A0a:Z

    .line 64632
    goto/16 :goto_0

    .line 64633
    :pswitch_5
    iget-object v3, p0, Lcom/facebook/ads/redexgen/X/XX;->A0O:Lcom/facebook/ads/redexgen/X/CE;

    sget-object v1, Lcom/facebook/ads/redexgen/X/XX;->A0q:[Ljava/lang/String;

    const/4 v0, 0x6

    aget-object v0, v1, v0

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v1

    const/16 v0, 0x1b

    if-eq v1, v0, :cond_4

    .line 64634
    :cond_3
    :goto_1
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    .line 64635
    :cond_4
    sget-object v2, Lcom/facebook/ads/redexgen/X/XX;->A0q:[Ljava/lang/String;

    const-string v1, "a0uWSiKj3FE77SqtP8skZXqIoOHsVkha"

    const/4 v0, 0x0

    aput-object v1, v2, v0

    const-string v1, "zxuZMYlEoYkqiqBcXObqrQLJbRKLzsvV"

    const/4 v0, 0x2

    aput-object v1, v2, v0

    iput v6, v3, Lcom/facebook/ads/redexgen/X/CE;->A0D:I

    .line 64636
    goto/16 :goto_0

    .line 64637
    :sswitch_e
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/XX;->A0O:Lcom/facebook/ads/redexgen/X/CE;

    long-to-int v0, p2

    iput v0, v1, Lcom/facebook/ads/redexgen/X/CE;->A0G:I

    .line 64638
    goto/16 :goto_0

    .line 64639
    :sswitch_f
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/XX;->A0O:Lcom/facebook/ads/redexgen/X/CE;

    long-to-int v0, p2

    iput v0, v1, Lcom/facebook/ads/redexgen/X/CE;->A0H:I

    .line 64640
    goto/16 :goto_0

    .line 64641
    :sswitch_10
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/XX;->A0O:Lcom/facebook/ads/redexgen/X/CE;

    long-to-int v0, p2

    iput v0, v1, Lcom/facebook/ads/redexgen/X/CE;->A0I:I

    .line 64642
    goto/16 :goto_0

    .line 64643
    :sswitch_11
    long-to-int v0, p2

    .line 64644
    .local v0, "layout":I
    sparse-switch v0, :sswitch_data_2

    goto/16 :goto_0

    .line 64645
    :sswitch_12
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0O:Lcom/facebook/ads/redexgen/X/CE;

    iput v7, v0, Lcom/facebook/ads/redexgen/X/CE;->A0P:I

    .line 64646
    goto/16 :goto_0

    .line 64647
    :sswitch_13
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0O:Lcom/facebook/ads/redexgen/X/CE;

    iput v5, v0, Lcom/facebook/ads/redexgen/X/CE;->A0P:I

    .line 64648
    goto/16 :goto_0

    .line 64649
    :sswitch_14
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0O:Lcom/facebook/ads/redexgen/X/CE;

    iput v6, v0, Lcom/facebook/ads/redexgen/X/CE;->A0P:I

    .line 64650
    goto/16 :goto_0

    .line 64651
    :sswitch_15
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0O:Lcom/facebook/ads/redexgen/X/CE;

    iput v3, v0, Lcom/facebook/ads/redexgen/X/CE;->A0P:I

    .line 64652
    goto/16 :goto_0

    .line 64653
    .end local v0    # "layout":I
    :sswitch_16
    iget-wide v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0K:J

    add-long/2addr v0, p2

    iput-wide v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0I:J

    .line 64654
    goto/16 :goto_0

    .line 64655
    :sswitch_17
    cmp-long v0, p2, v8

    if-nez v0, :cond_7

    goto/16 :goto_0

    .line 64656
    :sswitch_18
    const-wide/16 v1, 0x0

    cmp-long v0, p2, v1

    if-nez v0, :cond_8

    goto/16 :goto_0

    .line 64657
    :sswitch_19
    cmp-long v0, p2, v8

    if-nez v0, :cond_9

    goto/16 :goto_0

    .line 64658
    :sswitch_1a
    const-wide/16 v1, 0x5

    cmp-long v0, p2, v1

    if-nez v0, :cond_a

    goto/16 :goto_0

    .line 64659
    :sswitch_1b
    cmp-long v0, p2, v8

    if-nez v0, :cond_b

    goto/16 :goto_0

    .line 64660
    :sswitch_1c
    cmp-long v0, p2, v8

    if-ltz v0, :cond_c

    const-wide/16 v1, 0x2

    cmp-long v0, p2, v1

    if-gtz v0, :cond_c

    goto/16 :goto_0

    .line 64661
    :sswitch_1d
    const-wide/16 v1, 0x3

    cmp-long v0, p2, v1

    if-nez v0, :cond_d

    goto/16 :goto_0

    .line 64662
    :sswitch_1e
    iput-boolean v5, p0, Lcom/facebook/ads/redexgen/X/XX;->A0W:Z

    .line 64663
    goto/16 :goto_0

    .line 64664
    :sswitch_1f
    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0Z:Z

    if-nez v0, :cond_0

    .line 64665
    iget-object v3, p0, Lcom/facebook/ads/redexgen/X/XX;->A0P:Lcom/facebook/ads/redexgen/X/Hp;

    sget-object v2, Lcom/facebook/ads/redexgen/X/XX;->A0q:[Ljava/lang/String;

    const/4 v0, 0x0

    aget-object v1, v2, v0

    const/4 v0, 0x2

    aget-object v2, v2, v0

    const/16 v0, 0x1f

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    invoke-virtual {v2, v0}, Ljava/lang/String;->charAt(I)C

    move-result v0

    if-eq v1, v0, :cond_e

    sget-object v2, Lcom/facebook/ads/redexgen/X/XX;->A0q:[Ljava/lang/String;

    const-string v1, "PHYzuIxoVCpcvyGp46FNV8"

    const/4 v0, 0x3

    aput-object v1, v2, v0

    const-string v1, "jONBlxc6dKLj6ao"

    const/4 v0, 0x4

    aput-object v1, v2, v0

    invoke-virtual {v3, p2, p3}, Lcom/facebook/ads/redexgen/X/Hp;->A04(J)V

    .line 64666
    iput-boolean v5, p0, Lcom/facebook/ads/redexgen/X/XX;->A0Z:Z

    goto/16 :goto_0

    .line 64667
    :sswitch_20
    invoke-direct {p0, p2, p3}, Lcom/facebook/ads/redexgen/X/XX;->A01(J)J

    move-result-wide v0

    iput-wide v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0E:J

    .line 64668
    goto/16 :goto_0

    .line 64669
    :sswitch_21
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/XX;->A0O:Lcom/facebook/ads/redexgen/X/CE;

    long-to-int v0, p2

    iput v0, v1, Lcom/facebook/ads/redexgen/X/CE;->A0N:I

    .line 64670
    goto/16 :goto_0

    .line 64671
    :sswitch_22
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/XX;->A0O:Lcom/facebook/ads/redexgen/X/CE;

    long-to-int v0, p2

    iput v0, v1, Lcom/facebook/ads/redexgen/X/CE;->A0J:I

    .line 64672
    goto/16 :goto_0

    .line 64673
    :sswitch_23
    iget-object v2, p0, Lcom/facebook/ads/redexgen/X/XX;->A0Q:Lcom/facebook/ads/redexgen/X/Hp;

    invoke-direct {p0, p2, p3}, Lcom/facebook/ads/redexgen/X/XX;->A01(J)J

    move-result-wide v0

    invoke-virtual {v2, v0, v1}, Lcom/facebook/ads/redexgen/X/Hp;->A04(J)V

    .line 64674
    goto/16 :goto_0

    .line 64675
    :sswitch_24
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/XX;->A0O:Lcom/facebook/ads/redexgen/X/CE;

    long-to-int v0, p2

    iput v0, v1, Lcom/facebook/ads/redexgen/X/CE;->A0R:I

    .line 64676
    goto/16 :goto_0

    .line 64677
    :sswitch_25
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/XX;->A0O:Lcom/facebook/ads/redexgen/X/CE;

    long-to-int v0, p2

    iput v0, v1, Lcom/facebook/ads/redexgen/X/CE;->A0B:I

    .line 64678
    goto/16 :goto_0

    .line 64679
    :sswitch_26
    invoke-direct {p0, p2, p3}, Lcom/facebook/ads/redexgen/X/XX;->A01(J)J

    move-result-wide v0

    iput-wide v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0C:J

    .line 64680
    goto/16 :goto_0

    .line 64681
    :sswitch_27
    iget-object v4, p0, Lcom/facebook/ads/redexgen/X/XX;->A0O:Lcom/facebook/ads/redexgen/X/CE;

    sget-object v2, Lcom/facebook/ads/redexgen/X/XX;->A0q:[Ljava/lang/String;

    const/4 v0, 0x1

    aget-object v1, v2, v0

    const/4 v0, 0x5

    aget-object v2, v2, v0

    const/16 v0, 0xf

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    invoke-virtual {v2, v0}, Ljava/lang/String;->charAt(I)C

    move-result v0

    if-eq v1, v0, :cond_6

    sget-object v2, Lcom/facebook/ads/redexgen/X/XX;->A0q:[Ljava/lang/String;

    const-string v1, "dnlPxGSMw9IfVDogfualtgwGzOIqKL0v"

    const/4 v0, 0x1

    aput-object v1, v2, v0

    const-string v1, "sfCl2vw1fgJd0VDghIrSAi3kGetUjpNf"

    const/4 v0, 0x5

    aput-object v1, v2, v0

    cmp-long v0, p2, v8

    if-nez v0, :cond_5

    :goto_2
    const/4 v3, 0x1

    :cond_5
    iput-boolean v3, v4, Lcom/facebook/ads/redexgen/X/CE;->A0Z:Z

    .line 64682
    goto/16 :goto_0

    :cond_6
    sget-object v2, Lcom/facebook/ads/redexgen/X/XX;->A0q:[Ljava/lang/String;

    const-string v1, "e8S13q37f3oqi2Xgxg7oDCnYjszKvzMZ"

    const/4 v0, 0x1

    aput-object v1, v2, v0

    const-string v1, "sUUQ807hVNhLoz0ge5jS5NLDJS8BF8yk"

    const/4 v0, 0x5

    aput-object v1, v2, v0

    cmp-long v0, p2, v8

    if-nez v0, :cond_5

    goto :goto_2

    .line 64683
    :sswitch_28
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/XX;->A0O:Lcom/facebook/ads/redexgen/X/CE;

    long-to-int v0, p2

    iput v0, v1, Lcom/facebook/ads/redexgen/X/CE;->A0Q:I

    .line 64684
    goto/16 :goto_0

    .line 64685
    :cond_7
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const/16 v2, 0x15c

    const/16 v1, 0x15

    const/16 v0, 0x29

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/XX;->A03(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p2, p3}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    new-instance v0, Lcom/facebook/ads/redexgen/X/9v;

    invoke-direct {v0, v1}, Lcom/facebook/ads/redexgen/X/9v;-><init>(Ljava/lang/String;)V

    throw v0

    .line 64686
    :cond_8
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const/16 v2, 0x147

    const/16 v1, 0x15

    const/16 v0, 0x58

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/XX;->A03(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p2, p3}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    new-instance v0, Lcom/facebook/ads/redexgen/X/9v;

    invoke-direct {v0, v1}, Lcom/facebook/ads/redexgen/X/9v;-><init>(Ljava/lang/String;)V

    throw v0

    .line 64687
    :cond_9
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const/16 v2, 0x34

    const/16 v1, 0x16

    const/16 v0, 0x48

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/XX;->A03(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p2, p3}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    new-instance v0, Lcom/facebook/ads/redexgen/X/9v;

    invoke-direct {v0, v1}, Lcom/facebook/ads/redexgen/X/9v;-><init>(Ljava/lang/String;)V

    throw v0

    .line 64688
    :cond_a
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const/16 v2, 0x138

    const/16 v1, 0xf

    const/16 v0, 0x42

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/XX;->A03(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p2, p3}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    new-instance v0, Lcom/facebook/ads/redexgen/X/9v;

    invoke-direct {v0, v1}, Lcom/facebook/ads/redexgen/X/9v;-><init>(Ljava/lang/String;)V

    throw v0

    .line 64689
    :cond_b
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const/16 v2, 0x1b1

    const/16 v1, 0x10

    const/16 v0, 0x60

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/XX;->A03(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p2, p3}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    new-instance v0, Lcom/facebook/ads/redexgen/X/9v;

    invoke-direct {v0, v1}, Lcom/facebook/ads/redexgen/X/9v;-><init>(Ljava/lang/String;)V

    throw v0

    .line 64690
    :cond_c
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const/16 v2, 0x179

    const/16 v1, 0x13

    const/16 v0, 0xe

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/XX;->A03(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p2, p3}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    new-instance v0, Lcom/facebook/ads/redexgen/X/9v;

    invoke-direct {v0, v1}, Lcom/facebook/ads/redexgen/X/9v;-><init>(Ljava/lang/String;)V

    throw v0

    .line 64691
    :cond_d
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const/16 v2, 0x128

    const/16 v1, 0x10

    const/16 v0, 0x6c

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/XX;->A03(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p2, p3}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    new-instance v0, Lcom/facebook/ads/redexgen/X/9v;

    invoke-direct {v0, v1}, Lcom/facebook/ads/redexgen/X/9v;-><init>(Ljava/lang/String;)V

    throw v0

    .line 64692
    :cond_e
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    nop

    :sswitch_data_0
    .sparse-switch
        0x83 -> :sswitch_28
        0x88 -> :sswitch_27
        0x9b -> :sswitch_26
        0x9f -> :sswitch_25
        0xb0 -> :sswitch_24
        0xb3 -> :sswitch_23
        0xba -> :sswitch_22
        0xd7 -> :sswitch_21
        0xe7 -> :sswitch_20
        0xf1 -> :sswitch_1f
        0xfb -> :sswitch_1e
        0x4254 -> :sswitch_1d
        0x4285 -> :sswitch_1c
        0x42f7 -> :sswitch_1b
        0x47e1 -> :sswitch_1a
        0x47e8 -> :sswitch_19
        0x5031 -> :sswitch_18
        0x5032 -> :sswitch_17
        0x53ac -> :sswitch_16
        0x53b8 -> :sswitch_11
        0x54b0 -> :sswitch_10
        0x54b2 -> :sswitch_f
        0x54ba -> :sswitch_e
        0x55aa -> :sswitch_d
        0x55b9 -> :sswitch_c
        0x55ba -> :sswitch_8
        0x55bb -> :sswitch_7
        0x55bc -> :sswitch_6
        0x55bd -> :sswitch_5
        0x56aa -> :sswitch_4
        0x56bb -> :sswitch_3
        0x6264 -> :sswitch_2
        0x23e383 -> :sswitch_1
        0x2ad7b1 -> :sswitch_0
    .end sparse-switch

    :pswitch_data_0
    .packed-switch 0x1
        :pswitch_2
        :pswitch_0
        :pswitch_0
        :pswitch_5
        :pswitch_5
        :pswitch_5
        :pswitch_5
        :pswitch_0
        :pswitch_1
    .end packed-switch

    :sswitch_data_1
    .sparse-switch
        0x1 -> :sswitch_b
        0x6 -> :sswitch_b
        0x7 -> :sswitch_b
        0x10 -> :sswitch_a
        0x12 -> :sswitch_9
    .end sparse-switch

    :pswitch_data_1
    .packed-switch 0x1
        :pswitch_4
        :pswitch_3
    .end packed-switch

    :sswitch_data_2
    .sparse-switch
        0x0 -> :sswitch_15
        0x1 -> :sswitch_14
        0x3 -> :sswitch_13
        0xf -> :sswitch_12
    .end sparse-switch
.end method

.method public final A0M(IJJ)V
    .locals 7
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lcom/facebook/ads/redexgen/X/9v;
        }
    .end annotation

    .line 64693
    const/4 v0, 0x0

    const-wide/16 v1, -0x1

    const/4 v4, 0x1

    sparse-switch p1, :sswitch_data_0

    .line 64694
    :cond_0
    :goto_0
    :sswitch_0
    return-void

    .line 64695
    :sswitch_1
    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0a:Z

    if-nez v0, :cond_0

    .line 64696
    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0o:Z

    if-eqz v0, :cond_1

    iget-wide v5, p0, Lcom/facebook/ads/redexgen/X/XX;->A0F:J

    cmp-long v0, v5, v1

    if-eqz v0, :cond_1

    .line 64697
    iput-boolean v4, p0, Lcom/facebook/ads/redexgen/X/XX;->A0Y:Z

    goto :goto_0

    .line 64698
    :cond_1
    iget-object v3, p0, Lcom/facebook/ads/redexgen/X/XX;->A0N:Lcom/facebook/ads/redexgen/X/Bu;

    iget-wide v1, p0, Lcom/facebook/ads/redexgen/X/XX;->A0H:J

    new-instance v0, Lcom/facebook/ads/redexgen/X/Xj;

    invoke-direct {v0, v1, v2}, Lcom/facebook/ads/redexgen/X/Xj;-><init>(J)V

    invoke-interface {v3, v0}, Lcom/facebook/ads/redexgen/X/Bu;->AFi(Lcom/facebook/ads/redexgen/X/C1;)V

    .line 64699
    iput-boolean v4, p0, Lcom/facebook/ads/redexgen/X/XX;->A0a:Z

    goto :goto_0

    .line 64700
    :sswitch_2
    new-instance v0, Lcom/facebook/ads/redexgen/X/Hp;

    invoke-direct {v0}, Lcom/facebook/ads/redexgen/X/Hp;-><init>()V

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0Q:Lcom/facebook/ads/redexgen/X/Hp;

    .line 64701
    new-instance v0, Lcom/facebook/ads/redexgen/X/Hp;

    invoke-direct {v0}, Lcom/facebook/ads/redexgen/X/Hp;-><init>()V

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0P:Lcom/facebook/ads/redexgen/X/Hp;

    .line 64702
    goto :goto_0

    .line 64703
    :sswitch_3
    iget-wide v3, p0, Lcom/facebook/ads/redexgen/X/XX;->A0K:J

    cmp-long v0, v3, v1

    if-eqz v0, :cond_2

    cmp-long v0, v3, p2

    if-nez v0, :cond_3

    .line 64704
    :cond_2
    iput-wide p2, p0, Lcom/facebook/ads/redexgen/X/XX;->A0K:J

    .line 64705
    iput-wide p4, p0, Lcom/facebook/ads/redexgen/X/XX;->A0L:J

    .line 64706
    goto :goto_0

    .line 64707
    :sswitch_4
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0O:Lcom/facebook/ads/redexgen/X/CE;

    iput-boolean v4, v0, Lcom/facebook/ads/redexgen/X/CE;->A0b:Z

    .line 64708
    goto :goto_0

    .line 64709
    :sswitch_5
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0O:Lcom/facebook/ads/redexgen/X/CE;

    iput-boolean v4, v0, Lcom/facebook/ads/redexgen/X/CE;->A0c:Z

    .line 64710
    goto :goto_0

    .line 64711
    :sswitch_6
    const/4 v0, -0x1

    iput v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0B:I

    .line 64712
    iput-wide v1, p0, Lcom/facebook/ads/redexgen/X/XX;->A0I:J

    .line 64713
    goto :goto_0

    .line 64714
    :sswitch_7
    iput-boolean v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0Z:Z

    .line 64715
    goto :goto_0

    .line 64716
    :sswitch_8
    const/4 v1, 0x0

    new-instance v0, Lcom/facebook/ads/redexgen/X/CE;

    invoke-direct {v0, v1}, Lcom/facebook/ads/redexgen/X/CE;-><init>(Lcom/facebook/ads/redexgen/X/XZ;)V

    iput-object v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0O:Lcom/facebook/ads/redexgen/X/CE;

    .line 64717
    goto :goto_0

    .line 64718
    :sswitch_9
    iput-boolean v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0W:Z

    .line 64719
    goto :goto_0

    .line 64720
    :cond_3
    const/16 v2, 0x2cd

    const/16 v1, 0x27

    const/16 v0, 0x71

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/XX;->A03(III)Ljava/lang/String;

    move-result-object v1

    new-instance v0, Lcom/facebook/ads/redexgen/X/9v;

    invoke-direct {v0, v1}, Lcom/facebook/ads/redexgen/X/9v;-><init>(Ljava/lang/String;)V

    throw v0

    nop

    :sswitch_data_0
    .sparse-switch
        0xa0 -> :sswitch_9
        0xae -> :sswitch_8
        0xbb -> :sswitch_7
        0x4dbb -> :sswitch_6
        0x5035 -> :sswitch_5
        0x55d0 -> :sswitch_4
        0x6240 -> :sswitch_0
        0x18538067 -> :sswitch_3
        0x1c53bb6b -> :sswitch_2
        0x1f43b675 -> :sswitch_1
    .end sparse-switch
.end method

.method public final A0N(ILjava/lang/String;)V
    .locals 4
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lcom/facebook/ads/redexgen/X/9v;
        }
    .end annotation

    .line 64721
    sparse-switch p1, :sswitch_data_0

    .line 64722
    :cond_0
    :goto_0
    return-void

    .line 64723
    :sswitch_0
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0O:Lcom/facebook/ads/redexgen/X/CE;

    invoke-static {v0, p2}, Lcom/facebook/ads/redexgen/X/CE;->A02(Lcom/facebook/ads/redexgen/X/CE;Ljava/lang/String;)Ljava/lang/String;

    .line 64724
    goto :goto_0

    .line 64725
    :sswitch_1
    const/16 v2, 0x40a

    const/4 v1, 0x4

    const/16 v0, 0x71

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/XX;->A03(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v0, p2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_0

    const/16 v2, 0x3f8

    const/16 v1, 0x8

    const/16 v0, 0x7a

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/XX;->A03(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v0, p2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_1

    goto :goto_0

    .line 64726
    :sswitch_2
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0O:Lcom/facebook/ads/redexgen/X/CE;

    iput-object p2, v0, Lcom/facebook/ads/redexgen/X/CE;->A0Y:Ljava/lang/String;

    .line 64727
    goto :goto_0

    .line 64728
    :cond_1
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const/16 v2, 0x171

    const/16 v1, 0x8

    const/16 v0, 0x75

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/XX;->A03(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    const/4 v2, 0x0

    const/16 v1, 0xe

    const/16 v0, 0x15

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/XX;->A03(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    new-instance v0, Lcom/facebook/ads/redexgen/X/9v;

    invoke-direct {v0, v1}, Lcom/facebook/ads/redexgen/X/9v;-><init>(Ljava/lang/String;)V

    throw v0

    nop

    :sswitch_data_0
    .sparse-switch
        0x86 -> :sswitch_2
        0x4282 -> :sswitch_1
        0x22b59c -> :sswitch_0
    .end sparse-switch
.end method

.method public final A8o(Lcom/facebook/ads/redexgen/X/Bu;)V
    .locals 0

    .line 64729
    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/XX;->A0N:Lcom/facebook/ads/redexgen/X/Bu;

    .line 64730
    return-void
.end method

.method public final AEH(Lcom/facebook/ads/redexgen/X/Bt;Lcom/facebook/ads/redexgen/X/Bz;)I
    .locals 5
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;,
            Ljava/lang/InterruptedException;
        }
    .end annotation

    .line 64731
    const/4 v3, 0x0

    iput-boolean v3, p0, Lcom/facebook/ads/redexgen/X/XX;->A0V:Z

    .line 64732
    const/4 v2, 0x1

    .line 64733
    .local v1, "continueReading":Z
    :cond_0
    if-eqz v2, :cond_1

    iget-boolean v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0V:Z

    if-nez v0, :cond_1

    .line 64734
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0d:Lcom/facebook/ads/redexgen/X/CA;

    invoke-interface {v0, p1}, Lcom/facebook/ads/redexgen/X/CA;->AEJ(Lcom/facebook/ads/redexgen/X/Bt;)Z

    move-result v2

    .line 64735
    if-eqz v2, :cond_0

    invoke-interface {p1}, Lcom/facebook/ads/redexgen/X/Bt;->A7i()J

    move-result-wide v0

    invoke-direct {p0, p2, v0, v1}, Lcom/facebook/ads/redexgen/X/XX;->A0E(Lcom/facebook/ads/redexgen/X/Bz;J)Z

    move-result v0

    if-eqz v0, :cond_0

    .line 64736
    const/4 v0, 0x1

    return v0

    .line 64737
    :cond_1
    if-nez v2, :cond_4

    .line 64738
    const/4 v3, 0x0

    .local v0, "i":I
    :goto_0
    iget-object v4, p0, Lcom/facebook/ads/redexgen/X/XX;->A0c:Landroid/util/SparseArray;

    sget-object v2, Lcom/facebook/ads/redexgen/X/XX;->A0q:[Ljava/lang/String;

    const/4 v0, 0x0

    aget-object v1, v2, v0

    const/4 v0, 0x2

    aget-object v2, v2, v0

    const/16 v0, 0x1f

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    invoke-virtual {v2, v0}, Ljava/lang/String;->charAt(I)C

    move-result v0

    if-eq v1, v0, :cond_3

    sget-object v2, Lcom/facebook/ads/redexgen/X/XX;->A0q:[Ljava/lang/String;

    const-string v1, "Tajypdz7aNAm7FibYO8x24"

    const/4 v0, 0x3

    aput-object v1, v2, v0

    const-string v1, "TBunjD0UUDKaUo7"

    const/4 v0, 0x4

    aput-object v1, v2, v0

    invoke-virtual {v4}, Landroid/util/SparseArray;->size()I

    move-result v0

    if-ge v3, v0, :cond_2

    .line 64739
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0c:Landroid/util/SparseArray;

    invoke-virtual {v0, v3}, Landroid/util/SparseArray;->valueAt(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/facebook/ads/redexgen/X/CE;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/CE;->A07()V

    .line 64740
    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    .line 64741
    .end local v0    # "i":I
    :cond_2
    const/4 v0, -0x1

    return v0

    :cond_3
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0

    .line 64742
    :cond_4
    return v3
.end method

.method public final AFh(JJ)V
    .locals 2

    .line 64743
    const-wide v0, -0x7fffffffffffffffL    # -4.9E-324

    iput-wide v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0E:J

    .line 64744
    const/4 v0, 0x0

    iput v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A04:I

    .line 64745
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0d:Lcom/facebook/ads/redexgen/X/CA;

    invoke-interface {v0}, Lcom/facebook/ads/redexgen/X/CA;->reset()V

    .line 64746
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0e:Lcom/facebook/ads/redexgen/X/CH;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/CH;->A06()V

    .line 64747
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/XX;->A05()V

    .line 64748
    const/4 v1, 0x0

    .local v0, "i":I
    :goto_0
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0c:Landroid/util/SparseArray;

    invoke-virtual {v0}, Landroid/util/SparseArray;->size()I

    move-result v0

    if-ge v1, v0, :cond_0

    .line 64749
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/XX;->A0c:Landroid/util/SparseArray;

    invoke-virtual {v0, v1}, Landroid/util/SparseArray;->valueAt(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/facebook/ads/redexgen/X/CE;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/CE;->A08()V

    .line 64750
    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    .line 64751
    .end local v0    # "i":I
    :cond_0
    return-void
.end method

.method public final AGR(Lcom/facebook/ads/redexgen/X/Bt;)Z
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;,
            Ljava/lang/InterruptedException;
        }
    .end annotation

    .line 64752
    new-instance v0, Lcom/facebook/ads/redexgen/X/CG;

    invoke-direct {v0}, Lcom/facebook/ads/redexgen/X/CG;-><init>()V

    invoke-virtual {v0, p1}, Lcom/facebook/ads/redexgen/X/CG;->A01(Lcom/facebook/ads/redexgen/X/Bt;)Z

    move-result v0

    return v0
.end method
