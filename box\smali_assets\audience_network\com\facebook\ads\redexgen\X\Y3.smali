.class public final Lcom/facebook/ads/redexgen/X/Y3;
.super Lcom/facebook/ads/redexgen/X/AH;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/facebook/ads/redexgen/X/AH;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 66780
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/AH;-><init>()V

    return-void
.end method


# virtual methods
.method public final A00()I
    .locals 1

    .line 66781
    const/4 v0, 0x0

    return v0
.end method

.method public final A01()I
    .locals 1

    .line 66782
    const/4 v0, 0x0

    return v0
.end method

.method public final A04(Ljava/lang/Object;)I
    .locals 1

    .line 66783
    const/4 v0, -0x1

    return v0
.end method

.method public final A0A(ILcom/facebook/ads/redexgen/X/AF;Z)Lcom/facebook/ads/redexgen/X/AF;
    .locals 1

    .line 66784
    new-instance v0, Ljava/lang/IndexOutOfBoundsException;

    invoke-direct {v0}, Ljava/lang/IndexOutOfBoundsException;-><init>()V

    throw v0
.end method

.method public final A0D(ILcom/facebook/ads/redexgen/X/AG;ZJ)Lcom/facebook/ads/redexgen/X/AG;
    .locals 1

    .line 66785
    new-instance v0, Ljava/lang/IndexOutOfBoundsException;

    invoke-direct {v0}, Ljava/lang/IndexOutOfBoundsException;-><init>()V

    throw v0
.end method
