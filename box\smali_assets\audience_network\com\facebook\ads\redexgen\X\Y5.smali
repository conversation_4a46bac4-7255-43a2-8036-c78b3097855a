.class public interface abstract Lcom/facebook/ads/redexgen/X/Y5;
.super Ljava/lang/Object;
.source ""

# interfaces
.implements Lcom/facebook/ads/redexgen/X/A7;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/facebook/ads/internal/exoplayer2/thirdparty/Renderer$State;
    }
.end annotation


# virtual methods
.method public abstract A58()V
.end method

.method public abstract A5V(Lcom/facebook/ads/redexgen/X/AB;[Lcom/facebook/ads/internal/exoplayer2/thirdparty/Format;Lcom/facebook/ads/redexgen/X/FB;JZJ)V
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lcom/facebook/ads/redexgen/X/9c;
        }
    .end annotation
.end method

.method public abstract A6I()Lcom/facebook/ads/redexgen/X/AA;
.end method

.method public abstract A7N()Lcom/facebook/ads/redexgen/X/Hq;
.end method

.method public abstract A81()I
.end method

.method public abstract A84()Lcom/facebook/ads/redexgen/X/FB;
.end method

.method public abstract A8C()I
.end method

.method public abstract A8a()Z
.end method

.method public abstract A8y()Z
.end method

.method public abstract A91()Z
.end method

.method public abstract A9C()Z
.end method

.method public abstract AAP()V
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation
.end method

.method public abstract AFB(JJ)V
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lcom/facebook/ads/redexgen/X/9c;
        }
    .end annotation
.end method

.method public abstract AFD([Lcom/facebook/ads/internal/exoplayer2/thirdparty/Format;Lcom/facebook/ads/redexgen/X/FB;J)V
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lcom/facebook/ads/redexgen/X/9c;
        }
    .end annotation
.end method

.method public abstract AFN(J)V
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lcom/facebook/ads/redexgen/X/9c;
        }
    .end annotation
.end method

.method public abstract AFy()V
.end method

.method public abstract AG2(I)V
.end method

.method public abstract start()V
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lcom/facebook/ads/redexgen/X/9c;
        }
    .end annotation
.end method

.method public abstract stop()V
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lcom/facebook/ads/redexgen/X/9c;
        }
    .end annotation
.end method
