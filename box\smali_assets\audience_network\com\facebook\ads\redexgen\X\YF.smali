.class public final Lcom/facebook/ads/redexgen/X/YF;
.super Ljava/lang/Object;
.source ""

# interfaces
.implements Lcom/facebook/ads/redexgen/X/9C;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/facebook/ads/redexgen/X/YD;-><init>(Lcom/facebook/ads/redexgen/X/Ym;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field public final synthetic A00:Lcom/facebook/ads/redexgen/X/YD;


# direct methods
.method public constructor <init>(Lcom/facebook/ads/redexgen/X/YD;)V
    .locals 0

    .line 67479
    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/YF;->A00:Lcom/facebook/ads/redexgen/X/YD;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final AFF(Ljava/lang/String;)V
    .locals 2

    .line 67480
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/YF;->A00:Lcom/facebook/ads/redexgen/X/YD;

    sget v0, Lcom/facebook/ads/redexgen/X/8A;->A2O:I

    invoke-static {v1, v0, p1}, Lcom/facebook/ads/redexgen/X/YD;->A06(Lcom/facebook/ads/redexgen/X/YD;ILjava/lang/String;)V

    .line 67481
    return-void
.end method

.method public final AFG(Ljava/lang/String;Ljava/lang/Exception;)V
    .locals 2

    .line 67482
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/YF;->A00:Lcom/facebook/ads/redexgen/X/YD;

    sget v0, Lcom/facebook/ads/redexgen/X/8A;->A2O:I

    invoke-static {v1, v0, p1}, Lcom/facebook/ads/redexgen/X/YD;->A06(Lcom/facebook/ads/redexgen/X/YD;ILjava/lang/String;)V

    .line 67483
    return-void
.end method
