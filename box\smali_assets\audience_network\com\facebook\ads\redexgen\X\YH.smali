.class public interface abstract Lcom/facebook/ads/redexgen/X/YH;
.super Ljava/lang/Object;
.source ""

# interfaces
.implements Lcom/facebook/ads/redexgen/X/8v;
.implements Lcom/facebook/ads/redexgen/X/8w;


# static fields
.field public static final A00:Ljava/lang/String;


# direct methods
.method public static constructor <clinit>()V
    .locals 1

    .line 2596
    const-class v0, Lcom/facebook/ads/redexgen/X/YH;

    invoke-virtual {v0}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    move-result-object v0

    sput-object v0, Lcom/facebook/ads/redexgen/X/YH;->A00:Ljava/lang/String;

    return-void
.end method
