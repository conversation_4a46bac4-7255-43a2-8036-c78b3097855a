.class public final Lcom/facebook/ads/redexgen/X/YU;
.super Ljava/lang/Object;
.source ""

# interfaces
.implements Lcom/facebook/ads/redexgen/X/7z;


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 67846
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final A7z(Lcom/facebook/ads/redexgen/X/Ym;)Ljava/util/Map;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/facebook/ads/redexgen/X/Ym;",
            ")",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    .line 67847
    invoke-static {p1}, Lcom/facebook/ads/redexgen/X/8d;->A01(Lcom/facebook/ads/redexgen/X/7f;)Ljava/util/Map;

    move-result-object v0

    return-object v0
.end method
