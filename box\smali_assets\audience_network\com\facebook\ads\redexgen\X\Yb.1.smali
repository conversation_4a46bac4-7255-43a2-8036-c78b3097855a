.class public final Lcom/facebook/ads/redexgen/X/Yb;
.super Ljava/lang/Object;
.source ""

# interfaces
.implements Lcom/facebook/ads/redexgen/X/89;


# instance fields
.field public final A00:Lcom/facebook/ads/redexgen/X/7f;


# direct methods
.method public constructor <init>(Lcom/facebook/ads/redexgen/X/7f;)V
    .locals 0

    .line 67894
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 67895
    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/Yb;->A00:Lcom/facebook/ads/redexgen/X/7f;

    .line 67896
    return-void
.end method


# virtual methods
.method public final A3c(Ljava/lang/Throwable;)V
    .locals 0

    .line 67897
    invoke-static {p1}, Lcom/facebook/ads/redexgen/X/84;->A0E(Ljava/lang/Throwable;)V

    .line 67898
    return-void
.end method

.method public final A8l(Ljava/lang/String;)V
    .locals 1

    .line 67899
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Yb;->A00:Lcom/facebook/ads/redexgen/X/7f;

    invoke-static {v0, p1}, Lcom/facebook/ads/redexgen/X/JD;->A08(Lcom/facebook/ads/redexgen/X/7f;Ljava/lang/String;)V

    .line 67900
    return-void
.end method

.method public final A9a(Ljava/lang/String;ILcom/facebook/ads/redexgen/X/8B;)V
    .locals 1

    .line 67901
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Yb;->A00:Lcom/facebook/ads/redexgen/X/7f;

    invoke-static {v0, p1, p2, p3}, Lcom/facebook/ads/redexgen/X/84;->A06(Lcom/facebook/ads/redexgen/X/7f;Ljava/lang/String;ILcom/facebook/ads/redexgen/X/8B;)V

    .line 67902
    return-void
.end method

.method public final A9b(Ljava/lang/String;ILcom/facebook/ads/redexgen/X/8B;)V
    .locals 1

    .line 67903
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Yb;->A00:Lcom/facebook/ads/redexgen/X/7f;

    invoke-static {v0, p1, p2, p3}, Lcom/facebook/ads/redexgen/X/84;->A06(Lcom/facebook/ads/redexgen/X/7f;Ljava/lang/String;ILcom/facebook/ads/redexgen/X/8B;)V

    .line 67904
    return-void
.end method

.method public final A9t(JJJJILjava/lang/Exception;)V
    .locals 11

    .line 67905
    move-object v0, p0

    iget-object v0, v0, Lcom/facebook/ads/redexgen/X/Yb;->A00:Lcom/facebook/ads/redexgen/X/7f;

    move-wide v1, p1

    move-wide v3, p3

    move-wide/from16 v5, p5

    move-wide/from16 v7, p7

    move/from16 v9, p9

    move-object/from16 v10, p10

    invoke-static/range {v0 .. v10}, Lcom/facebook/ads/redexgen/X/8F;->A03(Lcom/facebook/ads/redexgen/X/7f;JJJJILjava/lang/Exception;)V

    .line 67906
    return-void
.end method

.method public final AA2(Ljava/lang/String;ILcom/facebook/ads/redexgen/X/8B;)V
    .locals 1

    .line 67907
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Yb;->A00:Lcom/facebook/ads/redexgen/X/7f;

    invoke-static {v0, p1, p2, p3}, Lcom/facebook/ads/redexgen/X/84;->A07(Lcom/facebook/ads/redexgen/X/7f;Ljava/lang/String;ILcom/facebook/ads/redexgen/X/8B;)V

    .line 67908
    return-void
.end method

.method public final AAA(Ljava/lang/String;ILcom/facebook/ads/redexgen/X/8B;)V
    .locals 1

    .line 67909
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Yb;->A00:Lcom/facebook/ads/redexgen/X/7f;

    invoke-static {v0, p1, p2, p3}, Lcom/facebook/ads/redexgen/X/84;->A08(Lcom/facebook/ads/redexgen/X/7f;Ljava/lang/String;ILcom/facebook/ads/redexgen/X/8B;)V

    .line 67910
    return-void
.end method

.method public final AAL()V
    .locals 1

    .line 67911
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Yb;->A00:Lcom/facebook/ads/redexgen/X/7f;

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/7f;->A03()Lcom/facebook/ads/redexgen/X/7i;

    move-result-object v0

    invoke-interface {v0}, Lcom/facebook/ads/redexgen/X/7i;->AAL()V

    .line 67912
    return-void
.end method
