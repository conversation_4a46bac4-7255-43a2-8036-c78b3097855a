.class public final Lcom/facebook/ads/redexgen/X/Yc;
.super Ljava/lang/Object;
.source ""

# interfaces
.implements Lcom/facebook/ads/redexgen/X/87;


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 67913
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final AHD(Ljava/lang/String;Ljava/util/Map;Lcom/facebook/ads/redexgen/X/7f;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ">;",
            "Lcom/facebook/ads/redexgen/X/7f;",
            ")V"
        }
    .end annotation

    .line 67914
    .local p2, "staticEnvironmentData":Ljava/util/Map;, "Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"
    invoke-static {p1, p3, p2}, Lcom/facebook/ads/redexgen/X/8C;->A01(Ljava/lang/String;Lcom/facebook/ads/redexgen/X/7f;Ljava/util/Map;)Lcom/facebook/ads/redexgen/X/8E;

    .line 67915
    return-void
.end method

.method public final AHF(Lcom/facebook/ads/redexgen/X/8E;Lcom/facebook/ads/redexgen/X/Ym;)V
    .locals 0

    .line 67916
    invoke-static {p1, p2}, Lcom/facebook/ads/redexgen/X/8C;->A0C(Lcom/facebook/ads/redexgen/X/8E;Lcom/facebook/ads/redexgen/X/7f;)V

    .line 67917
    return-void
.end method
