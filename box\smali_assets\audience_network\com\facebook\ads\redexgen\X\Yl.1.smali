.class public final Lcom/facebook/ads/redexgen/X/Yl;
.super Lcom/facebook/ads/redexgen/X/7s;
.source ""


# static fields
.field public static A00:[B

.field public static A01:[Ljava/lang/String;

.field public static final A02:Lcom/facebook/ads/redexgen/X/7m;

.field public static final A03:Lcom/facebook/ads/redexgen/X/7m;

.field public static final A04:Lcom/facebook/ads/redexgen/X/7m;

.field public static final A05:Lcom/facebook/ads/redexgen/X/7m;

.field public static final A06:Lcom/facebook/ads/redexgen/X/7m;

.field public static final A07:Lcom/facebook/ads/redexgen/X/7m;

.field public static final A08:Lcom/facebook/ads/redexgen/X/7m;

.field public static final A09:Lcom/facebook/ads/redexgen/X/7m;

.field public static final A0A:Lcom/facebook/ads/redexgen/X/7m;

.field public static final A0B:[Lcom/facebook/ads/redexgen/X/7m;

.field public static final A0C:Ljava/lang/String;


# direct methods
.method public static constructor <clinit>()V
    .locals 16

    .line 2610
    const/16 v0, 0x8

    new-array v2, v0, [Ljava/lang/String;

    const/4 v1, 0x0

    const-string v0, "jrDlnlrK"

    aput-object v0, v2, v1

    const/4 v1, 0x1

    const-string v0, "iy84zN2ZyMyHjuWhjHotEPEqpiKyPe89"

    aput-object v0, v2, v1

    const/4 v1, 0x2

    const-string v0, "dhX4OOJDnXlCZP9pL8vYTq2cDSC1Ep0f"

    aput-object v0, v2, v1

    const/4 v1, 0x3

    const-string v0, "V5wGJKV"

    aput-object v0, v2, v1

    const/4 v1, 0x4

    const-string v0, "mjidYuf4"

    aput-object v0, v2, v1

    const/4 v1, 0x5

    const-string v0, "f2fONlBHJxOBrlG7qLTpq6ivaaiBfV2e"

    aput-object v0, v2, v1

    const/4 v1, 0x6

    const-string v0, "tNB7OhzIAdgb5rZvvS5ZVqcHEeMF5GsI"

    aput-object v0, v2, v1

    const/4 v1, 0x7

    const-string v0, "2zYeFugNugInNaPrLN78SJaAZY8hyaCl"

    aput-object v0, v2, v1

    sput-object v2, Lcom/facebook/ads/redexgen/X/Yl;->A01:[Ljava/lang/String;

    invoke-static {}, Lcom/facebook/ads/redexgen/X/Yl;->A04()V

    const/16 v2, 0x9f

    const/16 v1, 0x8

    const/16 v0, 0x1f

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Yl;->A00(III)Ljava/lang/String;

    move-result-object v3

    const/16 v2, 0x49

    const/16 v1, 0x10

    const/16 v0, 0x23

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Yl;->A00(III)Ljava/lang/String;

    move-result-object v0

    const/4 v15, 0x0

    new-instance v14, Lcom/facebook/ads/redexgen/X/7m;

    invoke-direct {v14, v15, v3, v0}, Lcom/facebook/ads/redexgen/X/7m;-><init>(ILjava/lang/String;Ljava/lang/String;)V

    sput-object v14, Lcom/facebook/ads/redexgen/X/Yl;->A04:Lcom/facebook/ads/redexgen/X/7m;

    .line 2611
    const/16 v2, 0xcf

    const/16 v1, 0x8

    const/4 v0, 0x2

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Yl;->A00(III)Ljava/lang/String;

    move-result-object v3

    const/16 v2, 0x59

    const/16 v1, 0x3b

    const/16 v0, 0x6f

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Yl;->A00(III)Ljava/lang/String;

    move-result-object v0

    const/4 v13, 0x1

    new-instance v12, Lcom/facebook/ads/redexgen/X/7m;

    invoke-direct {v12, v13, v3, v0}, Lcom/facebook/ads/redexgen/X/7m;-><init>(ILjava/lang/String;Ljava/lang/String;)V

    sput-object v12, Lcom/facebook/ads/redexgen/X/Yl;->A09:Lcom/facebook/ads/redexgen/X/7m;

    .line 2612
    const/4 v11, 0x2

    const/16 v2, 0xad

    const/16 v1, 0x8

    const/16 v0, 0x19

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Yl;->A00(III)Ljava/lang/String;

    move-result-object v4

    const/4 v2, 0x4

    const/4 v1, 0x7

    const/16 v0, 0x31

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Yl;->A00(III)Ljava/lang/String;

    move-result-object v3

    new-instance v10, Lcom/facebook/ads/redexgen/X/7m;

    invoke-direct {v10, v11, v4, v3}, Lcom/facebook/ads/redexgen/X/7m;-><init>(ILjava/lang/String;Ljava/lang/String;)V

    sput-object v10, Lcom/facebook/ads/redexgen/X/Yl;->A05:Lcom/facebook/ads/redexgen/X/7m;

    .line 2613
    const/4 v9, 0x3

    const/16 v2, 0xd7

    const/4 v1, 0x4

    const/16 v0, 0x42

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Yl;->A00(III)Ljava/lang/String;

    move-result-object v4

    const/16 v2, 0x45

    const/4 v1, 0x4

    const/16 v0, 0x51

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Yl;->A00(III)Ljava/lang/String;

    move-result-object v2

    new-instance v8, Lcom/facebook/ads/redexgen/X/7m;

    invoke-direct {v8, v9, v4, v2}, Lcom/facebook/ads/redexgen/X/7m;-><init>(ILjava/lang/String;Ljava/lang/String;)V

    sput-object v8, Lcom/facebook/ads/redexgen/X/Yl;->A0A:Lcom/facebook/ads/redexgen/X/7m;

    .line 2614
    const/4 v6, 0x4

    const/16 v4, 0xcb

    const/4 v1, 0x4

    const/16 v0, 0x37

    invoke-static {v4, v1, v0}, Lcom/facebook/ads/redexgen/X/Yl;->A00(III)Ljava/lang/String;

    move-result-object v5

    const/16 v4, 0xb

    const/4 v1, 0x4

    const/16 v0, 0x6e

    invoke-static {v4, v1, v0}, Lcom/facebook/ads/redexgen/X/Yl;->A00(III)Ljava/lang/String;

    move-result-object v4

    new-instance v7, Lcom/facebook/ads/redexgen/X/7m;

    invoke-direct {v7, v6, v5, v4}, Lcom/facebook/ads/redexgen/X/7m;-><init>(ILjava/lang/String;Ljava/lang/String;)V

    sput-object v7, Lcom/facebook/ads/redexgen/X/Yl;->A08:Lcom/facebook/ads/redexgen/X/7m;

    .line 2615
    const/16 v5, 0xbf

    const/16 v1, 0xc

    const/16 v0, 0x9

    invoke-static {v5, v1, v0}, Lcom/facebook/ads/redexgen/X/Yl;->A00(III)Ljava/lang/String;

    move-result-object v1

    const/4 v0, 0x5

    new-instance v6, Lcom/facebook/ads/redexgen/X/7m;

    invoke-direct {v6, v0, v1, v4}, Lcom/facebook/ads/redexgen/X/7m;-><init>(ILjava/lang/String;Ljava/lang/String;)V

    sput-object v6, Lcom/facebook/ads/redexgen/X/Yl;->A07:Lcom/facebook/ads/redexgen/X/7m;

    .line 2616
    const/16 v4, 0xb5

    const/16 v1, 0xa

    const/16 v0, 0x13

    invoke-static {v4, v1, v0}, Lcom/facebook/ads/redexgen/X/Yl;->A00(III)Ljava/lang/String;

    move-result-object v1

    const/4 v0, 0x6

    new-instance v5, Lcom/facebook/ads/redexgen/X/7m;

    invoke-direct {v5, v0, v1, v2}, Lcom/facebook/ads/redexgen/X/7m;-><init>(ILjava/lang/String;Ljava/lang/String;)V

    sput-object v5, Lcom/facebook/ads/redexgen/X/Yl;->A06:Lcom/facebook/ads/redexgen/X/7m;

    .line 2617
    const/16 v4, 0x9b

    const/4 v1, 0x4

    const/16 v0, 0x1d

    invoke-static {v4, v1, v0}, Lcom/facebook/ads/redexgen/X/Yl;->A00(III)Ljava/lang/String;

    move-result-object v1

    const/4 v0, 0x7

    new-instance v4, Lcom/facebook/ads/redexgen/X/7m;

    invoke-direct {v4, v0, v1, v2}, Lcom/facebook/ads/redexgen/X/7m;-><init>(ILjava/lang/String;Ljava/lang/String;)V

    sput-object v4, Lcom/facebook/ads/redexgen/X/Yl;->A03:Lcom/facebook/ads/redexgen/X/7m;

    .line 2618
    const/16 v2, 0x94

    const/4 v1, 0x7

    const/16 v0, 0x64

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Yl;->A00(III)Ljava/lang/String;

    move-result-object v0

    const/16 v2, 0x8

    new-instance v1, Lcom/facebook/ads/redexgen/X/7m;

    invoke-direct {v1, v2, v0, v3}, Lcom/facebook/ads/redexgen/X/7m;-><init>(ILjava/lang/String;Ljava/lang/String;)V

    sput-object v1, Lcom/facebook/ads/redexgen/X/Yl;->A02:Lcom/facebook/ads/redexgen/X/7m;

    .line 2619
    const/16 v0, 0x9

    new-array v3, v0, [Lcom/facebook/ads/redexgen/X/7m;

    aput-object v14, v3, v15

    aput-object v12, v3, v13

    aput-object v10, v3, v11

    aput-object v8, v3, v9

    const/4 v0, 0x4

    aput-object v7, v3, v0

    const/4 v0, 0x5

    aput-object v6, v3, v0

    const/4 v0, 0x6

    aput-object v5, v3, v0

    const/4 v0, 0x7

    aput-object v4, v3, v0

    aput-object v1, v3, v2

    sput-object v3, Lcom/facebook/ads/redexgen/X/Yl;->A0B:[Lcom/facebook/ads/redexgen/X/7m;

    .line 2620
    const/16 v2, 0xa7

    const/4 v1, 0x6

    const/16 v0, 0x6d

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Yl;->A00(III)Ljava/lang/String;

    move-result-object v0

    invoke-static {v0, v3}, Lcom/facebook/ads/redexgen/X/7s;->A02(Ljava/lang/String;[Lcom/facebook/ads/redexgen/X/7m;)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Lcom/facebook/ads/redexgen/X/Yl;->A0C:Ljava/lang/String;

    return-void
.end method

.method public constructor <init>(Lcom/facebook/ads/redexgen/X/7o;)V
    .locals 0

    .line 68046
    invoke-direct {p0, p1}, Lcom/facebook/ads/redexgen/X/7s;-><init>(Lcom/facebook/ads/redexgen/X/7o;)V

    .line 68047
    return-void
.end method

.method public static A00(III)Ljava/lang/String;
    .locals 2

    sget-object v1, Lcom/facebook/ads/redexgen/X/Yl;->A00:[B

    add-int v0, p0, p1

    invoke-static {v1, p0, v0}, Ljava/util/Arrays;->copyOfRange([BII)[B

    move-result-object p0

    const/4 v1, 0x0

    :goto_0
    array-length v0, p0

    if-ge v1, v0, :cond_0

    aget-byte v0, p0, v1

    sub-int/2addr v0, p2

    add-int/lit8 v0, v0, -0x2a

    int-to-byte v0, v0

    aput-byte v0, p0, v1

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_0
    new-instance v0, Ljava/lang/String;

    invoke-direct {v0, p0}, Ljava/lang/String;-><init>([B)V

    return-object v0
.end method

.method public static A04()V
    .locals 1

    const/16 v0, 0xdb

    new-array v0, v0, [B

    fill-array-data v0, :array_0

    sput-object v0, Lcom/facebook/ads/redexgen/X/Yl;->A00:[B

    return-void

    :array_0
    .array-data 1
        -0x66t
        -0x49t
        -0x66t
        -0x47t
        -0x5ct
        -0x57t
        -0x51t
        -0x60t
        -0x5et
        -0x60t
        -0x53t
        -0x16t
        -0x23t
        -0x27t
        -0x1ct
        -0x5dt
        -0x6bt
        -0x64t
        -0x6bt
        -0x6dt
        -0x5ct
        0x70t
        0x7at
        0x70t
        -0x6at
        -0x5et
        -0x61t
        -0x63t
        0x70t
        -0x4bt
        -0x3at
        -0x4bt
        -0x42t
        -0x3ct
        -0x3dt
        0x70t
        -0x59t
        -0x68t
        -0x6bt
        -0x5et
        -0x6bt
        0x70t
        -0xbt
        -0x19t
        -0x12t
        -0x19t
        -0x1bt
        -0xat
        -0x3et
        0x5t
        0x11t
        0x17t
        0x10t
        0x16t
        -0x36t
        -0x34t
        -0x35t
        -0x3et
        -0x18t
        -0xct
        -0xft
        -0x11t
        -0x3et
        0x7t
        0x18t
        0x7t
        0x10t
        0x16t
        0x15t
        -0x31t
        -0x40t
        -0x2dt
        -0x31t
        -0x5ft
        -0x6et
        -0x5bt
        -0x5ft
        0x6dt
        -0x63t
        -0x61t
        -0x6at
        -0x66t
        -0x72t
        -0x61t
        -0x5at
        0x6dt
        -0x68t
        -0x6et
        -0x5at
        -0x13t
        -0x22t
        -0xft
        -0x13t
        -0x47t
        -0x15t
        -0x22t
        -0x21t
        -0x22t
        -0x15t
        -0x22t
        -0x19t
        -0x24t
        -0x22t
        -0x14t
        -0x47t
        0xdt
        0x8t
        0x4t
        -0x2t
        0x7t
        0xct
        -0x47t
        -0x18t
        -0x19t
        -0x47t
        -0x12t
        -0x17t
        -0x23t
        -0x26t
        -0x13t
        -0x22t
        -0x47t
        -0x24t
        -0x26t
        -0x14t
        -0x24t
        -0x26t
        -0x23t
        -0x22t
        -0x47t
        -0x18t
        -0x19t
        -0x47t
        -0x23t
        -0x22t
        -0x1bt
        -0x22t
        -0x13t
        -0x22t
        -0x47t
        -0x15t
        -0x22t
        -0x14t
        -0x13t
        -0x15t
        -0x1et
        -0x24t
        -0x13t
        -0x11t
        0x2t
        0x2t
        -0xdt
        -0x5t
        -0x2t
        0x2t
        -0x55t
        -0x58t
        -0x45t
        -0x58t
        -0x52t
        -0x41t
        -0x52t
        -0x49t
        -0x43t
        -0x58t
        -0x4et
        -0x53t
        -0x4t
        0xdt
        -0x4t
        0x5t
        0xbt
        0xat
        -0x4dt
        -0x4bt
        -0x54t
        -0x4et
        -0x4bt
        -0x54t
        -0x49t
        -0x44t
        -0x50t
        -0x5et
        -0x50t
        -0x50t
        -0x5at
        -0x54t
        -0x55t
        -0x64t
        -0x5at
        -0x5ft
        -0x5at
        -0x68t
        -0x5at
        -0x5at
        -0x64t
        -0x5et
        -0x5ft
        -0x6et
        -0x59t
        -0x64t
        -0x60t
        -0x68t
        -0x2bt
        -0x36t
        -0x32t
        -0x3at
        -0x60t
        -0x65t
        -0x69t
        -0x6ft
        -0x66t
        -0x75t
        -0x6bt
        -0x70t
        -0x20t
        -0x1bt
        -0x24t
        -0x2ft
    .end array-data
.end method


# virtual methods
.method public final A06()Ljava/lang/String;
    .locals 3

    .line 68048
    const/16 v2, 0xa7

    const/4 v1, 0x6

    const/16 v0, 0x6d

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Yl;->A00(III)Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public final A0A()[Lcom/facebook/ads/redexgen/X/7m;
    .locals 1

    .line 68049
    sget-object v0, Lcom/facebook/ads/redexgen/X/Yl;->A0B:[Lcom/facebook/ads/redexgen/X/7m;

    return-object v0
.end method

.method public final A0B()Landroid/database/Cursor;
    .locals 4

    .line 68050
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/7s;->A05()Landroid/database/sqlite/SQLiteDatabase;

    move-result-object v3

    const/16 v2, 0x2a

    const/16 v1, 0x1b

    const/16 v0, 0x78

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Yl;->A00(III)Ljava/lang/String;

    move-result-object v1

    const/4 v0, 0x0

    invoke-virtual {v3, v1, v0}, Landroid/database/sqlite/SQLiteDatabase;->rawQuery(Ljava/lang/String;[Ljava/lang/String;)Landroid/database/Cursor;

    move-result-object v0

    return-object v0
.end method

.method public final A0C()Landroid/database/Cursor;
    .locals 3

    .line 68051
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/7s;->A05()Landroid/database/sqlite/SQLiteDatabase;

    move-result-object v2

    sget-object v1, Lcom/facebook/ads/redexgen/X/Yl;->A0C:Ljava/lang/String;

    const/4 v0, 0x0

    invoke-virtual {v2, v1, v0}, Landroid/database/sqlite/SQLiteDatabase;->rawQuery(Ljava/lang/String;[Ljava/lang/String;)Landroid/database/Cursor;

    move-result-object v0

    return-object v0
.end method

.method public final A0D(Ljava/lang/String;)Landroid/database/Cursor;
    .locals 5

    .line 68052
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/7s;->A05()Landroid/database/sqlite/SQLiteDatabase;

    move-result-object v4

    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const/16 v2, 0xf

    const/16 v1, 0x1b

    const/16 v0, 0x26

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Yl;->A00(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    sget-object v0, Lcom/facebook/ads/redexgen/X/Yl;->A04:Lcom/facebook/ads/redexgen/X/7m;

    iget-object v0, v0, Lcom/facebook/ads/redexgen/X/7m;->A01:Ljava/lang/String;

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    const/4 v2, 0x0

    const/4 v1, 0x4

    const/16 v0, 0x50

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Yl;->A00(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    const/4 v0, 0x1

    new-array v1, v0, [Ljava/lang/String;

    const/4 v0, 0x0

    aput-object p1, v1, v0

    .line 68053
    invoke-virtual {v4, v2, v1}, Landroid/database/sqlite/SQLiteDatabase;->rawQuery(Ljava/lang/String;[Ljava/lang/String;)Landroid/database/Cursor;

    move-result-object v0

    .line 68054
    return-object v0
.end method

.method public final A0E(Ljava/lang/String;ILjava/lang/String;DDLjava/lang/String;Ljava/util/Map;)Ljava/lang/String;
    .locals 7
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "I",
            "Ljava/lang/String;",
            "DD",
            "Ljava/lang/String;",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ">;)",
            "Ljava/lang/String;"
        }
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroid/database/sqlite/SQLiteException;
        }
    .end annotation

    .line 68055
    .local p7, "data":Ljava/util/Map;, "Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;"
    invoke-static {}, Ljava/util/UUID;->randomUUID()Ljava/util/UUID;

    move-result-object v0

    invoke-virtual {v0}, Ljava/util/UUID;->toString()Ljava/lang/String;

    move-result-object v5

    .line 68056
    .local v0, "eventId":Ljava/lang/String;
    const/16 v0, 0x9

    new-instance v4, Landroid/content/ContentValues;

    invoke-direct {v4, v0}, Landroid/content/ContentValues;-><init>(I)V

    .line 68057
    .local v1, "values":Landroid/content/ContentValues;
    sget-object v0, Lcom/facebook/ads/redexgen/X/Yl;->A04:Lcom/facebook/ads/redexgen/X/7m;

    iget-object v0, v0, Lcom/facebook/ads/redexgen/X/7m;->A01:Ljava/lang/String;

    invoke-virtual {v4, v0, v5}, Landroid/content/ContentValues;->put(Ljava/lang/String;Ljava/lang/String;)V

    .line 68058
    sget-object v0, Lcom/facebook/ads/redexgen/X/Yl;->A09:Lcom/facebook/ads/redexgen/X/7m;

    iget-object v0, v0, Lcom/facebook/ads/redexgen/X/7m;->A01:Ljava/lang/String;

    invoke-virtual {v4, v0, p1}, Landroid/content/ContentValues;->put(Ljava/lang/String;Ljava/lang/String;)V

    .line 68059
    sget-object v0, Lcom/facebook/ads/redexgen/X/Yl;->A05:Lcom/facebook/ads/redexgen/X/7m;

    iget-object v1, v0, Lcom/facebook/ads/redexgen/X/7m;->A01:Ljava/lang/String;

    invoke-static {p2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v0

    invoke-virtual {v4, v1, v0}, Landroid/content/ContentValues;->put(Ljava/lang/String;Ljava/lang/Integer;)V

    .line 68060
    sget-object v0, Lcom/facebook/ads/redexgen/X/Yl;->A0A:Lcom/facebook/ads/redexgen/X/7m;

    iget-object v0, v0, Lcom/facebook/ads/redexgen/X/7m;->A01:Ljava/lang/String;

    invoke-virtual {v4, v0, p3}, Landroid/content/ContentValues;->put(Ljava/lang/String;Ljava/lang/String;)V

    .line 68061
    sget-object v0, Lcom/facebook/ads/redexgen/X/Yl;->A08:Lcom/facebook/ads/redexgen/X/7m;

    iget-object v1, v0, Lcom/facebook/ads/redexgen/X/7m;->A01:Ljava/lang/String;

    invoke-static {p4, p5}, Ljava/lang/Double;->valueOf(D)Ljava/lang/Double;

    move-result-object v0

    invoke-virtual {v4, v1, v0}, Landroid/content/ContentValues;->put(Ljava/lang/String;Ljava/lang/Double;)V

    .line 68062
    sget-object v0, Lcom/facebook/ads/redexgen/X/Yl;->A07:Lcom/facebook/ads/redexgen/X/7m;

    iget-object v1, v0, Lcom/facebook/ads/redexgen/X/7m;->A01:Ljava/lang/String;

    invoke-static {p6, p7}, Ljava/lang/Double;->valueOf(D)Ljava/lang/Double;

    move-result-object v0

    invoke-virtual {v4, v1, v0}, Landroid/content/ContentValues;->put(Ljava/lang/String;Ljava/lang/Double;)V

    .line 68063
    sget-object v0, Lcom/facebook/ads/redexgen/X/Yl;->A06:Lcom/facebook/ads/redexgen/X/7m;

    iget-object v0, v0, Lcom/facebook/ads/redexgen/X/7m;->A01:Ljava/lang/String;

    invoke-virtual {v4, v0, p8}, Landroid/content/ContentValues;->put(Ljava/lang/String;Ljava/lang/String;)V

    .line 68064
    sget-object v0, Lcom/facebook/ads/redexgen/X/Yl;->A03:Lcom/facebook/ads/redexgen/X/7m;

    iget-object v1, v0, Lcom/facebook/ads/redexgen/X/7m;->A01:Ljava/lang/String;

    const/4 v6, 0x0

    move-object/from16 v2, p9

    if-eqz v2, :cond_0

    new-instance v0, Lorg/json/JSONObject;

    invoke-direct {v0, v2}, Lorg/json/JSONObject;-><init>(Ljava/util/Map;)V

    invoke-virtual {v0}, Lorg/json/JSONObject;->toString()Ljava/lang/String;

    move-result-object v0

    :goto_0
    invoke-virtual {v4, v1, v0}, Landroid/content/ContentValues;->put(Ljava/lang/String;Ljava/lang/String;)V

    sget-object v1, Lcom/facebook/ads/redexgen/X/Yl;->A01:[Ljava/lang/String;

    const/4 v0, 0x2

    aget-object v1, v1, v0

    const/4 v0, 0x6

    invoke-virtual {v1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    const/16 v0, 0x39

    if-eq v1, v0, :cond_1

    .line 68065
    sget-object v2, Lcom/facebook/ads/redexgen/X/Yl;->A01:[Ljava/lang/String;

    const-string v1, "s3YvQaCKhotLBtEqHITMzm2bHxiojI5j"

    const/4 v0, 0x6

    aput-object v1, v2, v0

    const-string v1, "6LuOsEhvTekWWK5Zi4EuC4VUkJbxKkeL"

    const/4 v0, 0x5

    aput-object v1, v2, v0

    sget-object v0, Lcom/facebook/ads/redexgen/X/Yl;->A02:Lcom/facebook/ads/redexgen/X/7m;

    iget-object v1, v0, Lcom/facebook/ads/redexgen/X/7m;->A01:Ljava/lang/String;

    const/4 v0, 0x0

    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v0

    invoke-virtual {v4, v1, v0}, Landroid/content/ContentValues;->put(Ljava/lang/String;Ljava/lang/Integer;)V

    .line 68066
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/7s;->A05()Landroid/database/sqlite/SQLiteDatabase;

    move-result-object v3

    const/16 v2, 0xa7

    const/4 v1, 0x6

    const/16 v0, 0x6d

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Yl;->A00(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v3, v0, v6, v4}, Landroid/database/sqlite/SQLiteDatabase;->insertOrThrow(Ljava/lang/String;Ljava/lang/String;Landroid/content/ContentValues;)J

    .line 68067
    return-object v5

    .line 68068
    :cond_0
    move-object v0, v6

    goto :goto_0

    :cond_1
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0}, Ljava/lang/RuntimeException;-><init>()V

    throw v0
.end method

.method public final A0F(Ljava/lang/String;)Z
    .locals 7

    .line 68069
    invoke-virtual {p0}, Lcom/facebook/ads/redexgen/X/7s;->A05()Landroid/database/sqlite/SQLiteDatabase;

    move-result-object v6

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    sget-object v0, Lcom/facebook/ads/redexgen/X/Yl;->A04:Lcom/facebook/ads/redexgen/X/7m;

    iget-object v0, v0, Lcom/facebook/ads/redexgen/X/7m;->A01:Ljava/lang/String;

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    const/4 v2, 0x0

    const/4 v1, 0x4

    const/16 v0, 0x50

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Yl;->A00(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v5

    const/4 v4, 0x1

    new-array v3, v4, [Ljava/lang/String;

    const/4 v0, 0x0

    aput-object p1, v3, v0

    const/16 v2, 0xa7

    const/4 v1, 0x6

    const/16 v0, 0x6d

    invoke-static {v2, v1, v0}, Lcom/facebook/ads/redexgen/X/Yl;->A00(III)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v6, v0, v5, v3}, Landroid/database/sqlite/SQLiteDatabase;->delete(Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;)I

    move-result v0

    if-lez v0, :cond_0

    :goto_0
    return v4

    :cond_0
    const/4 v4, 0x0

    goto :goto_0
.end method
