.class public final Lcom/facebook/ads/redexgen/X/Yq;
.super Lcom/facebook/ads/redexgen/X/KT;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/facebook/ads/redexgen/X/6X;-><init>(Lcom/facebook/ads/redexgen/X/6c;Lcom/facebook/ads/redexgen/X/6Y;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field public final synthetic A00:Lcom/facebook/ads/redexgen/X/6X;

.field public final synthetic A01:Lcom/facebook/ads/redexgen/X/6Y;

.field public final synthetic A02:Lcom/facebook/ads/redexgen/X/6c;


# direct methods
.method public constructor <init>(Lcom/facebook/ads/redexgen/X/6X;Lcom/facebook/ads/redexgen/X/6c;Lcom/facebook/ads/redexgen/X/6Y;)V
    .locals 0

    .line 68163
    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/Yq;->A00:Lcom/facebook/ads/redexgen/X/6X;

    iput-object p2, p0, Lcom/facebook/ads/redexgen/X/Yq;->A02:Lcom/facebook/ads/redexgen/X/6c;

    iput-object p3, p0, Lcom/facebook/ads/redexgen/X/Yq;->A01:Lcom/facebook/ads/redexgen/X/6Y;

    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/KT;-><init>()V

    return-void
.end method


# virtual methods
.method public final A06()V
    .locals 2

    .line 68164
    iget-object v1, p0, Lcom/facebook/ads/redexgen/X/Yq;->A00:Lcom/facebook/ads/redexgen/X/6X;

    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Yq;->A01:Lcom/facebook/ads/redexgen/X/6Y;

    invoke-static {v1, v0}, Lcom/facebook/ads/redexgen/X/6X;->A02(Lcom/facebook/ads/redexgen/X/6X;Lcom/facebook/ads/redexgen/X/6Y;)V

    .line 68165
    return-void
.end method
