.class public final Lcom/facebook/ads/redexgen/X/Yu;
.super Lcom/facebook/ads/redexgen/X/KT;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/facebook/ads/redexgen/X/6T;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field public final synthetic A00:Lcom/facebook/ads/redexgen/X/6T;


# direct methods
.method public constructor <init>(Lcom/facebook/ads/redexgen/X/6T;)V
    .locals 0

    .line 68212
    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/Yu;->A00:Lcom/facebook/ads/redexgen/X/6T;

    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/KT;-><init>()V

    return-void
.end method


# virtual methods
.method public final A06()V
    .locals 1

    .line 68213
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Yu;->A00:Lcom/facebook/ads/redexgen/X/6T;

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/6T;->A05(Lcom/facebook/ads/redexgen/X/6T;)V

    .line 68214
    return-void
.end method
