.class public final Lcom/facebook/ads/redexgen/X/Yv;
.super Ljava/lang/Object;
.source ""

# interfaces
.implements Lcom/facebook/ads/redexgen/X/Rl;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/facebook/ads/redexgen/X/6R;-><init>(Lcom/facebook/ads/redexgen/X/Ym;Lcom/facebook/ads/redexgen/X/Hj;Lcom/facebook/ads/redexgen/X/6S;Lcom/facebook/ads/redexgen/X/6T;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field public final synthetic A00:Lcom/facebook/ads/redexgen/X/6R;


# direct methods
.method public constructor <init>(Lcom/facebook/ads/redexgen/X/6R;)V
    .locals 0

    .line 68215
    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/Yv;->A00:Lcom/facebook/ads/redexgen/X/6R;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final A45()V
    .locals 1

    .line 68216
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Yv;->A00:Lcom/facebook/ads/redexgen/X/6R;

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/6R;->A03(Lcom/facebook/ads/redexgen/X/6R;)V

    .line 68217
    return-void
.end method
