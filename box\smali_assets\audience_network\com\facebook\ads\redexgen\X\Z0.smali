.class public final Lcom/facebook/ads/redexgen/X/Z0;
.super Lcom/facebook/ads/redexgen/X/67;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/facebook/ads/redexgen/X/67;->A00()Lcom/facebook/ads/redexgen/X/Z0;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 68288
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/67;-><init>()V

    return-void
.end method


# virtual methods
.method public final A00(Lcom/facebook/ads/redexgen/X/Hj;)Lcom/facebook/ads/redexgen/X/Yy;
    .locals 1

    .line 68289
    new-instance v0, Lcom/facebook/ads/redexgen/X/Yy;

    invoke-direct {v0, p1}, Lcom/facebook/ads/redexgen/X/Yy;-><init>(Lcom/facebook/ads/redexgen/X/Hj;)V

    return-object v0
.end method
