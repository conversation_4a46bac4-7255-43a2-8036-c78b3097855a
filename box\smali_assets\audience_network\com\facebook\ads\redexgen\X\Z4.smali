.class public final Lcom/facebook/ads/redexgen/X/Z4;
.super Lcom/facebook/ads/redexgen/X/64;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/facebook/ads/redexgen/X/64;->A00()Lcom/facebook/ads/redexgen/X/Z4;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 68337
    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/64;-><init>()V

    return-void
.end method


# virtual methods
.method public final A00(Lcom/facebook/ads/redexgen/X/66;Lcom/facebook/ads/redexgen/X/Ym;)Lcom/facebook/ads/redexgen/X/Z1;
    .locals 1

    .line 68338
    new-instance v0, Lcom/facebook/ads/redexgen/X/Z1;

    invoke-direct {v0, p1, p2}, Lcom/facebook/ads/redexgen/X/Z1;-><init>(Lcom/facebook/ads/redexgen/X/66;Lcom/facebook/ads/redexgen/X/Ym;)V

    return-object v0
.end method
