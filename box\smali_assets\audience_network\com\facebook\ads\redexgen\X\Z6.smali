.class public final Lcom/facebook/ads/redexgen/X/Z6;
.super Lcom/facebook/ads/redexgen/X/KT;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/facebook/ads/redexgen/X/Z5;->A06()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field public final synthetic A00:Lcom/facebook/ads/redexgen/X/Z5;


# direct methods
.method public constructor <init>(Lcom/facebook/ads/redexgen/X/Z5;)V
    .locals 0

    .line 68350
    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/Z6;->A00:Lcom/facebook/ads/redexgen/X/Z5;

    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/KT;-><init>()V

    return-void
.end method


# virtual methods
.method public final A06()V
    .locals 1

    .line 68351
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/Z6;->A00:Lcom/facebook/ads/redexgen/X/Z5;

    iget-object v0, v0, Lcom/facebook/ads/redexgen/X/Z5;->A00:Lcom/facebook/ads/redexgen/X/62;

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/62;->A01(Lcom/facebook/ads/redexgen/X/62;)Lcom/facebook/ads/redexgen/X/61;

    move-result-object v0

    invoke-interface {v0}, Lcom/facebook/ads/redexgen/X/61;->AAm()V

    .line 68352
    return-void
.end method
