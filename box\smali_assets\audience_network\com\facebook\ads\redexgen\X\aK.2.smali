.class public final Lcom/facebook/ads/redexgen/X/aK;
.super Lcom/facebook/ads/redexgen/X/KT;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/facebook/ads/redexgen/X/2C;-><init>(Lcom/facebook/ads/redexgen/X/Ym;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field public final synthetic A00:Lcom/facebook/ads/redexgen/X/2C;


# direct methods
.method public constructor <init>(Lcom/facebook/ads/redexgen/X/2C;)V
    .locals 0

    .line 70901
    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/aK;->A00:Lcom/facebook/ads/redexgen/X/2C;

    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/KT;-><init>()V

    return-void
.end method


# virtual methods
.method public final A06()V
    .locals 1

    .line 70902
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/aK;->A00:Lcom/facebook/ads/redexgen/X/2C;

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/2C;->A03(Lcom/facebook/ads/redexgen/X/2C;)Lcom/facebook/ads/redexgen/X/Ym;

    move-result-object v0

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/8b;->A04(Lcom/facebook/ads/redexgen/X/Ym;)V

    .line 70903
    return-void
.end method
