.class public final Lcom/facebook/ads/redexgen/X/aM;
.super Lcom/facebook/ads/redexgen/X/KT;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/facebook/ads/redexgen/X/29;-><init>(Lcom/facebook/ads/redexgen/X/Yn;Lcom/facebook/ads/redexgen/X/aa;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field public final synthetic A00:Lcom/facebook/ads/redexgen/X/29;


# direct methods
.method public constructor <init>(Lcom/facebook/ads/redexgen/X/29;)V
    .locals 0

    .line 70940
    iput-object p1, p0, Lcom/facebook/ads/redexgen/X/aM;->A00:Lcom/facebook/ads/redexgen/X/29;

    invoke-direct {p0}, Lcom/facebook/ads/redexgen/X/KT;-><init>()V

    return-void
.end method


# virtual methods
.method public final A06()V
    .locals 1

    .line 70941
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/aM;->A00:Lcom/facebook/ads/redexgen/X/29;

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/29;->A02(Lcom/facebook/ads/redexgen/X/29;)Lcom/facebook/ads/redexgen/X/Yn;

    move-result-object v0

    invoke-virtual {v0}, Lcom/facebook/ads/redexgen/X/Yn;->A0E()Lcom/facebook/ads/redexgen/X/0S;

    move-result-object v0

    invoke-interface {v0}, Lcom/facebook/ads/redexgen/X/0S;->AEo()V

    .line 70942
    iget-object v0, p0, Lcom/facebook/ads/redexgen/X/aM;->A00:Lcom/facebook/ads/redexgen/X/29;

    invoke-static {v0}, Lcom/facebook/ads/redexgen/X/29;->A09(Lcom/facebook/ads/redexgen/X/29;)V

    .line 70943
    return-void
.end method
