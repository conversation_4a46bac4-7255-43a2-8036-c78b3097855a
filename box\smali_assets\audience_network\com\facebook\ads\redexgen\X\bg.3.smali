.class public interface abstract Lcom/facebook/ads/redexgen/X/bg;
.super Ljava/lang/Object;
.source ""

# interfaces
.implements Lcom/facebook/ads/redexgen/X/0S;


# virtual methods
.method public abstract A3f(Z)V
.end method

.method public abstract A3g()V
.end method

.method public abstract A3h()V
.end method

.method public abstract A3i(Z)V
.end method

.method public abstract A3j()V
.end method

.method public abstract A3k(Z)V
.end method

.method public abstract A3l(Z)V
.end method

.method public abstract A3m()V
.end method

.method public abstract A3n()V
.end method

.method public abstract A3o()V
.end method

.method public abstract A3p(Z)V
.end method

.method public abstract A3q()V
.end method

.method public abstract A3r()V
.end method

.method public abstract A3s()V
.end method

.method public abstract A3t(Z)V
.end method

.method public abstract A3u()V
.end method

.method public abstract A3v(ZI)V
.end method

.method public abstract A3w()V
.end method

.method public abstract A3x(Z)V
.end method

.method public abstract A3y()V
.end method

.method public abstract A3z()V
.end method

.method public abstract A40(Z)V
.end method
