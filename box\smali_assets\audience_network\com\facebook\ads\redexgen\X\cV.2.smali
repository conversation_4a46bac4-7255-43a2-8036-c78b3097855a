.class public interface abstract Lcom/facebook/ads/redexgen/X/cV;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/facebook/ads/redexgen/X/cU;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "ViewpointScanListener"
.end annotation


# virtual methods
.method public abstract AD2()V
.end method
