.class public interface abstract Lcom/facebook/ads/redexgen/X/cZ;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/facebook/ads/redexgen/X/cY;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "OnLifecycleEventListener"
.end annotation


# virtual methods
.method public abstract onStart()V
.end method

.method public abstract onStop()V
.end method
