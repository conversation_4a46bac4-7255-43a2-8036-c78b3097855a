.class public interface abstract Lcom/facebook/ads/redexgen/X/cd;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "<ModelType:",
        "Ljava/lang/Object;",
        "StateType:",
        "Ljava/lang/Object;",
        ">",
        "Ljava/lang/Object;"
    }
.end annotation


# virtual methods
.method public abstract A5c(Lcom/facebook/ads/redexgen/X/ca;Lcom/facebook/ads/redexgen/X/cP;)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/facebook/ads/redexgen/X/ca<",
            "TModelType;TStateType;>;",
            "Lcom/facebook/ads/redexgen/X/cP;",
            ")V"
        }
    .end annotation
.end method
