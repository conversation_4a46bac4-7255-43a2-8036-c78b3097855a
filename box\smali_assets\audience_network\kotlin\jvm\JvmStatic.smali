.class public interface abstract annotation Lkotlin/jvm/JvmStatic;
.super Ljava/lang/Object;
.source ""

# interfaces
.implements Ljava/lang/annotation/Annotation;


# annotations
.annotation runtime Ljava/lang/annotation/Documented;
.end annotation

.annotation runtime Ljava/lang/annotation/Retention;
    value = .enum Ljava/lang/annotation/RetentionPolicy;->RUNTIME:Ljava/lang/annotation/RetentionPolicy;
.end annotation

.annotation runtime Ljava/lang/annotation/Target;
    value = {
        .enum Ljava/lang/annotation/ElementType;->METHOD:Ljava/lang/annotation/ElementType;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\n\n\u0002\u0018\u0002\n\u0002\u0010\u001b\n\u0000\u0008\u0087\u0002\u0018\u00002\u00020\u0001B\u0000\u00a8\u0006\u0002"
    }
    d2 = {
        "Lkotlin/jvm/JvmStatic;",
        "",
        "kotlin-stdlib"
    }
    k = 0x1
    mv = {
        0x1,
        0x9,
        0x0
    }
    xi = 0x30
.end annotation

.annotation runtime Lkotlin/annotation/MustBeDocumented;
.end annotation

.annotation runtime Lkotlin/annotation/Retention;
    value = .enum Lcom/facebook/ads/redexgen/X/c6;->A04:Lcom/facebook/ads/redexgen/X/c6;
.end annotation

.annotation runtime Lkotlin/annotation/Target;
    allowedTargets = {
        .enum Lcom/facebook/ads/redexgen/X/c5;->A09:Lcom/facebook/ads/redexgen/X/c5;,
        .enum Lcom/facebook/ads/redexgen/X/c5;->A0B:Lcom/facebook/ads/redexgen/X/c5;,
        .enum Lcom/facebook/ads/redexgen/X/c5;->A0C:Lcom/facebook/ads/redexgen/X/c5;,
        .enum Lcom/facebook/ads/redexgen/X/c5;->A0D:Lcom/facebook/ads/redexgen/X/c5;
    }
.end annotation
