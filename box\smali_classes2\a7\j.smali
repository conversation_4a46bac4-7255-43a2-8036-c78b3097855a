.class public final synthetic La7/j;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/cloud/sdk/commonutil/util/Preconditions$a;


# instance fields
.field public final synthetic a:La7/g;


# direct methods
.method public synthetic constructor <init>(La7/g;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, La7/j;->a:La7/g;

    return-void
.end method


# virtual methods
.method public final onRun()V
    .locals 1

    iget-object v0, p0, La7/j;->a:La7/g;

    invoke-static {v0}, La7/k;->a(La7/g;)V

    return-void
.end method
