.class public final synthetic La7/p;
.super Ljava/lang/Object;

# interfaces
.implements Lri/c;


# instance fields
.field public final synthetic a:Lkotlin/jvm/functions/Function2;


# direct methods
.method public synthetic constructor <init>(Lkotlin/jvm/functions/Function2;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, La7/p;->a:Lkotlin/jvm/functions/Function2;

    return-void
.end method


# virtual methods
.method public final accept(Ljava/lang/Object;Ljava/lang/Object;)V
    .locals 1

    iget-object v0, p0, La7/p;->a:Lkotlin/jvm/functions/Function2;

    check-cast p1, Lmi/n;

    invoke-static {v0, p1, p2}, Lcom/cloud/hisavana/sdk/common/util/PolyGammaUtil$Companion;->b(Lkotlin/jvm/functions/Function2;Lmi/n;Ljava/lang/Object;)V

    return-void
.end method
