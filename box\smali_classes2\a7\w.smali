.class public final La7/w;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        La7/w$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation


# static fields
.field public static final a:La7/w$a;


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, La7/w$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, La7/w$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, La7/w;->a:La7/w$a;

    return-void
.end method
