.class public final La7/x$a$b;
.super Lcom/cloud/hisavana/sdk/common/http/listener/DrawableResponseListener;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = La7/x$a;->c(La7/x$b;Lcom/cloud/hisavana/sdk/data/bean/response/AdsDTO;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation


# instance fields
.field public final synthetic b:Lkotlin/jvm/internal/Ref$BooleanRef;

.field public final synthetic c:Lkotlin/jvm/internal/Ref$BooleanRef;

.field public final synthetic d:Lkotlin/jvm/internal/Ref$BooleanRef;

.field public final synthetic e:Lkotlin/jvm/internal/Ref$BooleanRef;

.field public final synthetic f:La7/x$b;


# direct methods
.method public constructor <init>(Lkotlin/jvm/internal/Ref$BooleanRef;Lkotlin/jvm/internal/Ref$BooleanRef;Lkotlin/jvm/internal/Ref$BooleanRef;Lkotlin/jvm/internal/Ref$BooleanRef;La7/x$b;)V
    .locals 0

    iput-object p1, p0, La7/x$a$b;->b:Lkotlin/jvm/internal/Ref$BooleanRef;

    iput-object p2, p0, La7/x$a$b;->c:Lkotlin/jvm/internal/Ref$BooleanRef;

    iput-object p3, p0, La7/x$a$b;->d:Lkotlin/jvm/internal/Ref$BooleanRef;

    iput-object p4, p0, La7/x$a$b;->e:Lkotlin/jvm/internal/Ref$BooleanRef;

    iput-object p5, p0, La7/x$a$b;->f:La7/x$b;

    invoke-direct {p0}, Lcom/cloud/hisavana/sdk/common/http/listener/DrawableResponseListener;-><init>()V

    return-void
.end method


# virtual methods
.method public a(Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;)V
    .locals 7

    iget-object p1, p0, La7/x$a$b;->b:Lkotlin/jvm/internal/Ref$BooleanRef;

    const/4 v0, 0x1

    iput-boolean v0, p1, Lkotlin/jvm/internal/Ref$BooleanRef;->element:Z

    sget-object v1, La7/x;->a:La7/x$a;

    iget-object p1, p0, La7/x$a$b;->c:Lkotlin/jvm/internal/Ref$BooleanRef;

    iget-boolean v2, p1, Lkotlin/jvm/internal/Ref$BooleanRef;->element:Z

    iget-object p1, p0, La7/x$a$b;->d:Lkotlin/jvm/internal/Ref$BooleanRef;

    iget-boolean v4, p1, Lkotlin/jvm/internal/Ref$BooleanRef;->element:Z

    iget-object p1, p0, La7/x$a$b;->e:Lkotlin/jvm/internal/Ref$BooleanRef;

    iget-boolean v5, p1, Lkotlin/jvm/internal/Ref$BooleanRef;->element:Z

    iget-object v6, p0, La7/x$a$b;->f:La7/x$b;

    const/4 v3, 0x1

    invoke-static/range {v1 .. v6}, La7/x$a;->a(La7/x$a;ZZZZLa7/x$b;)V

    return-void
.end method

.method public g(ILcom/cloud/hisavana/sdk/common/bean/AdImage;)V
    .locals 6

    iget-object p1, p0, La7/x$a$b;->b:Lkotlin/jvm/internal/Ref$BooleanRef;

    const/4 p2, 0x1

    iput-boolean p2, p1, Lkotlin/jvm/internal/Ref$BooleanRef;->element:Z

    sget-object v0, La7/x;->a:La7/x$a;

    iget-object p1, p0, La7/x$a$b;->c:Lkotlin/jvm/internal/Ref$BooleanRef;

    iget-boolean v1, p1, Lkotlin/jvm/internal/Ref$BooleanRef;->element:Z

    iget-object p1, p0, La7/x$a$b;->d:Lkotlin/jvm/internal/Ref$BooleanRef;

    iget-boolean v3, p1, Lkotlin/jvm/internal/Ref$BooleanRef;->element:Z

    iget-object p1, p0, La7/x$a$b;->e:Lkotlin/jvm/internal/Ref$BooleanRef;

    iget-boolean v4, p1, Lkotlin/jvm/internal/Ref$BooleanRef;->element:Z

    iget-object v5, p0, La7/x$a$b;->f:La7/x$b;

    const/4 v2, 0x1

    invoke-static/range {v0 .. v5}, La7/x$a;->a(La7/x$a;ZZZZLa7/x$b;)V

    return-void
.end method
