.class public interface abstract La7/x$b;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = La7/x;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "b"
.end annotation

.annotation runtime L<PERSON>lin/Metadata;
.end annotation


# virtual methods
.method public abstract a(Lcom/cloud/hisavana/sdk/common/constant/TaErrorCode;)V
.end method

.method public abstract onSuccess()V
.end method
