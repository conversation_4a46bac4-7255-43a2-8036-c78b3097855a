.class public final La7/x;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        La7/x$a;,
        La7/x$b;
    }
.end annotation

.annotation runtime L<PERSON><PERSON>/Metadata;
.end annotation


# static fields
.field public static final a:La7/x$a;


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, La7/x$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, La7/x$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, La7/x;->a:La7/x$a;

    return-void
.end method
