.class public final La9/a$a;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = La9/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation


# instance fields
.field public a:La9/e;

.field public b:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "La9/c;",
            ">;"
        }
    .end annotation
.end field

.field public c:La9/b;

.field public d:Ljava/lang/String;


# direct methods
.method public constructor <init>()V
    .locals 2

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, 0x0

    iput-object v0, p0, La9/a$a;->a:La9/e;

    new-instance v1, Ljava/util/ArrayList;

    invoke-direct {v1}, Ljava/util/ArrayList;-><init>()V

    iput-object v1, p0, La9/a$a;->b:Ljava/util/List;

    iput-object v0, p0, La9/a$a;->c:La9/b;

    const-string v0, ""

    iput-object v0, p0, La9/a$a;->d:Ljava/lang/String;

    return-void
.end method


# virtual methods
.method public a(La9/c;)La9/a$a;
    .locals 1

    iget-object v0, p0, La9/a$a;->b:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-object p0
.end method

.method public b()La9/a;
    .locals 5

    new-instance v0, La9/a;

    iget-object v1, p0, La9/a$a;->a:La9/e;

    iget-object v2, p0, La9/a$a;->b:Ljava/util/List;

    invoke-static {v2}, Ljava/util/Collections;->unmodifiableList(Ljava/util/List;)Ljava/util/List;

    move-result-object v2

    iget-object v3, p0, La9/a$a;->c:La9/b;

    iget-object v4, p0, La9/a$a;->d:Ljava/lang/String;

    invoke-direct {v0, v1, v2, v3, v4}, La9/a;-><init>(La9/e;Ljava/util/List;La9/b;Ljava/lang/String;)V

    return-object v0
.end method

.method public c(Ljava/lang/String;)La9/a$a;
    .locals 0

    iput-object p1, p0, La9/a$a;->d:Ljava/lang/String;

    return-object p0
.end method

.method public d(La9/b;)La9/a$a;
    .locals 0

    iput-object p1, p0, La9/a$a;->c:La9/b;

    return-object p0
.end method

.method public e(La9/e;)La9/a$a;
    .locals 0

    iput-object p1, p0, La9/a$a;->a:La9/e;

    return-object p0
.end method
