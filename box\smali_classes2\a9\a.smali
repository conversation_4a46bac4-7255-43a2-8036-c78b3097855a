.class public final La9/a;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        La9/a$a;
    }
.end annotation


# static fields
.field public static final e:La9/a;


# instance fields
.field public final a:La9/e;

.field public final b:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "La9/c;",
            ">;"
        }
    .end annotation
.end field

.field public final c:La9/b;

.field public final d:Ljava/lang/String;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    new-instance v0, La9/a$a;

    invoke-direct {v0}, La9/a$a;-><init>()V

    invoke-virtual {v0}, La9/a$a;->b()La9/a;

    move-result-object v0

    sput-object v0, La9/a;->e:La9/a;

    return-void
.end method

.method public constructor <init>(La9/e;Ljava/util/List;La9/b;Ljava/lang/String;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "La9/e;",
            "Ljava/util/List<",
            "La9/c;",
            ">;",
            "La9/b;",
            "Ljava/lang/String;",
            ")V"
        }
    .end annotation

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, La9/a;->a:La9/e;

    iput-object p2, p0, La9/a;->b:Ljava/util/List;

    iput-object p3, p0, La9/a;->c:La9/b;

    iput-object p4, p0, La9/a;->d:Ljava/lang/String;

    return-void
.end method

.method public static e()La9/a$a;
    .locals 1

    new-instance v0, La9/a$a;

    invoke-direct {v0}, La9/a$a;-><init>()V

    return-object v0
.end method


# virtual methods
.method public a()Ljava/lang/String;
    .locals 1
    .annotation build Lcom/google/firebase/encoders/proto/Protobuf;
        tag = 0x4
    .end annotation

    iget-object v0, p0, La9/a;->d:Ljava/lang/String;

    return-object v0
.end method

.method public b()La9/b;
    .locals 1
    .annotation build Lcom/google/firebase/encoders/proto/Protobuf;
        tag = 0x3
    .end annotation

    iget-object v0, p0, La9/a;->c:La9/b;

    return-object v0
.end method

.method public c()Ljava/util/List;
    .locals 1
    .annotation build Lcom/google/firebase/encoders/proto/Protobuf;
        tag = 0x2
    .end annotation

    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "La9/c;",
            ">;"
        }
    .end annotation

    iget-object v0, p0, La9/a;->b:Ljava/util/List;

    return-object v0
.end method

.method public d()La9/e;
    .locals 1
    .annotation build Lcom/google/firebase/encoders/proto/Protobuf;
        tag = 0x1
    .end annotation

    iget-object v0, p0, La9/a;->a:La9/e;

    return-object v0
.end method

.method public f()[B
    .locals 1

    invoke-static {p0}, Lcom/google/android/datatransport/runtime/m;->a(Ljava/lang/Object;)[B

    move-result-object v0

    return-object v0
.end method
