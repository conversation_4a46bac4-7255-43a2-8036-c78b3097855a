.class public final La9/b$a;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = La9/b;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation


# instance fields
.field public a:La9/d;


# direct methods
.method public constructor <init>()V
    .locals 1

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, 0x0

    iput-object v0, p0, La9/b$a;->a:La9/d;

    return-void
.end method


# virtual methods
.method public a()La9/b;
    .locals 2

    new-instance v0, La9/b;

    iget-object v1, p0, La9/b$a;->a:La9/d;

    invoke-direct {v0, v1}, La9/b;-><init>(La9/d;)V

    return-object v0
.end method

.method public b(La9/d;)La9/b$a;
    .locals 0

    iput-object p1, p0, La9/b$a;->a:La9/d;

    return-object p0
.end method
