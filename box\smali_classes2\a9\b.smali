.class public final La9/b;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        La9/b$a;
    }
.end annotation


# static fields
.field public static final b:La9/b;


# instance fields
.field public final a:La9/d;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    new-instance v0, La9/b$a;

    invoke-direct {v0}, La9/b$a;-><init>()V

    invoke-virtual {v0}, La9/b$a;->a()La9/b;

    move-result-object v0

    sput-object v0, La9/b;->b:La9/b;

    return-void
.end method

.method public constructor <init>(La9/d;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, La9/b;->a:La9/d;

    return-void
.end method

.method public static b()La9/b$a;
    .locals 1

    new-instance v0, La9/b$a;

    invoke-direct {v0}, La9/b$a;-><init>()V

    return-object v0
.end method


# virtual methods
.method public a()La9/d;
    .locals 1
    .annotation build Lcom/google/firebase/encoders/proto/Protobuf;
        tag = 0x1
    .end annotation

    iget-object v0, p0, La9/b;->a:La9/d;

    return-object v0
.end method
