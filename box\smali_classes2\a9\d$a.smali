.class public final La9/d$a;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = La9/d;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation


# instance fields
.field public a:J

.field public b:J


# direct methods
.method public constructor <init>()V
    .locals 2

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const-wide/16 v0, 0x0

    iput-wide v0, p0, La9/d$a;->a:J

    iput-wide v0, p0, La9/d$a;->b:J

    return-void
.end method


# virtual methods
.method public a()La9/d;
    .locals 5

    new-instance v0, La9/d;

    iget-wide v1, p0, La9/d$a;->a:J

    iget-wide v3, p0, La9/d$a;->b:J

    invoke-direct {v0, v1, v2, v3, v4}, La9/d;-><init>(JJ)V

    return-object v0
.end method

.method public b(J)La9/d$a;
    .locals 0

    iput-wide p1, p0, La9/d$a;->a:J

    return-object p0
.end method

.method public c(J)La9/d$a;
    .locals 0

    iput-wide p1, p0, La9/d$a;->b:J

    return-object p0
.end method
