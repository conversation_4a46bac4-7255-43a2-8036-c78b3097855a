.class public interface abstract Landroidx/core/app/x;
.super Ljava/lang/Object;


# annotations
.annotation runtime Lkotlin/Metadata;
.end annotation


# virtual methods
.method public abstract addOnMultiWindowModeChangedListener(Landroidx/core/util/a;)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroidx/core/util/a<",
            "Landroidx/core/app/k;",
            ">;)V"
        }
    .end annotation
.end method

.method public abstract removeOnMultiWindowModeChangedListener(Landroidx/core/util/a;)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroidx/core/util/a<",
            "Landroidx/core/app/k;",
            ">;)V"
        }
    .end annotation
.end method
