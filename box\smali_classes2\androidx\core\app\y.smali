.class public interface abstract Landroidx/core/app/y;
.super Ljava/lang/Object;


# annotations
.annotation runtime Lkotlin/Metadata;
.end annotation


# virtual methods
.method public abstract addOnPictureInPictureModeChangedListener(Landroidx/core/util/a;)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroidx/core/util/a<",
            "Landroidx/core/app/a0;",
            ">;)V"
        }
    .end annotation
.end method

.method public abstract removeOnPictureInPictureModeChangedListener(Landroidx/core/util/a;)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroidx/core/util/a<",
            "Landroidx/core/app/a0;",
            ">;)V"
        }
    .end annotation
.end method
