.class public interface abstract Landroidx/core/content/FileProvider$b;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/core/content/FileProvider;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "b"
.end annotation


# virtual methods
.method public abstract a(Ljava/io/File;)Landroid/net/Uri;
.end method

.method public abstract b(Landroid/net/Uri;)Ljava/io/File;
.end method
