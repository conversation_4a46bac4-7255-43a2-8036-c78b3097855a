.class public final Landroidx/core/splashscreen/R$dimen;
.super Ljava/lang/Object;


# static fields
.field public static splashscreen_icon_mask_size_no_background:I = 0x7f0703b1

.field public static splashscreen_icon_mask_size_with_background:I = 0x7f0703b2

.field public static splashscreen_icon_mask_stroke_no_background:I = 0x7f0703b3

.field public static splashscreen_icon_mask_stroke_with_background:I = 0x7f0703b4

.field public static splashscreen_icon_size:I = 0x7f0703b5

.field public static splashscreen_icon_size_no_background:I = 0x7f0703b6

.field public static splashscreen_icon_size_with_background:I = 0x7f0703b7


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
