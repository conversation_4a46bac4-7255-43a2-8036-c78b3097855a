.class public final Landroidx/core/util/k;
.super Ljava/lang/Object;


# annotations
.annotation runtime Lkotlin/Metadata;
.end annotation

.annotation build Lkotlin/jvm/internal/SourceDebugExtension;
.end annotation


# direct methods
.method public static final a(Landroid/util/SparseArray;)Lkotlin/collections/IntIterator;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">(",
            "Landroid/util/SparseArray<",
            "TT;>;)",
            "Lkotlin/collections/IntIterator;"
        }
    .end annotation

    new-instance v0, Landroidx/core/util/k$a;

    invoke-direct {v0, p0}, Landroidx/core/util/k$a;-><init>(Landroid/util/SparseArray;)V

    return-object v0
.end method
