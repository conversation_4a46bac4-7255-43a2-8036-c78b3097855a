.class public interface abstract Landroidx/core/view/ActionProvider$SubUiVisibilityListener;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/core/view/ActionProvider;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "SubUiVisibilityListener"
.end annotation


# virtual methods
.method public abstract onSubUiVisibilityChanged(Z)V
.end method
