.class public interface abstract Landroidx/core/view/DifferentialMotionFlingController$a;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/core/view/DifferentialMotionFlingController;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "a"
.end annotation


# virtual methods
.method public abstract a(Landroid/view/VelocityTracker;Landroid/view/MotionEvent;I)F
.end method
