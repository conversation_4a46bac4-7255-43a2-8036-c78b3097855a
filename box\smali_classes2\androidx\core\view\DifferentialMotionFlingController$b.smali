.class public interface abstract Landroidx/core/view/DifferentialMotionFlingController$b;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/core/view/DifferentialMotionFlingController;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "b"
.end annotation


# virtual methods
.method public abstract a(Landroid/content/Context;[ILandroid/view/MotionEvent;I)V
.end method
