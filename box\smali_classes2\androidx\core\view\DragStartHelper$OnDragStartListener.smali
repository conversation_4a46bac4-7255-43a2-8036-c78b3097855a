.class public interface abstract Landroidx/core/view/DragStartHelper$OnDragStartListener;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/core/view/DragStartHelper;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "OnDragStartListener"
.end annotation


# virtual methods
.method public abstract onDragStart(Landroid/view/View;Landroidx/core/view/DragStartHelper;)Z
    .param p1    # Landroid/view/View;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p2    # Landroidx/core/view/DragStartHelper;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
.end method
