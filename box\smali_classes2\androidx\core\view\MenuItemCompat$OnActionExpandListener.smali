.class public interface abstract Landroidx/core/view/MenuItemCompat$OnActionExpandListener;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/core/view/MenuItemCompat;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "OnActionExpandListener"
.end annotation

.annotation runtime Ljava/lang/Deprecated;
.end annotation


# virtual methods
.method public abstract onMenuItemActionCollapse(Landroid/view/MenuItem;)Z
.end method

.method public abstract onMenuItemActionExpand(Landroid/view/MenuItem;)Z
.end method
