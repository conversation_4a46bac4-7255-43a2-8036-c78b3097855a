.class public interface abstract Landroidx/core/view/WindowInsetsAnimationControlListenerCompat;
.super Ljava/lang/Object;


# virtual methods
.method public abstract onCancelled(Landroidx/core/view/WindowInsetsAnimationControllerCompat;)V
    .param p1    # Landroidx/core/view/WindowInsetsAnimationControllerCompat;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
.end method

.method public abstract onFinished(Landroidx/core/view/WindowInsetsAnimationControllerCompat;)V
    .param p1    # Landroidx/core/view/WindowInsetsAnimationControllerCompat;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
.end method

.method public abstract onReady(Landroidx/core/view/WindowInsetsAnimationControllerCompat;I)V
    .param p1    # Landroidx/core/view/WindowInsetsAnimationControllerCompat;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
.end method
