.class public Landroidx/core/view/WindowInsetsAnimationControllerCompat$b;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/core/view/WindowInsetsAnimationControllerCompat;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "b"
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public a(Z)V
    .locals 0

    const/4 p0, 0x0

    throw p0
.end method

.method public b()F
    .locals 0

    const/4 p0, 0x0

    throw p0
.end method

.method public c()F
    .locals 0

    const/4 p0, 0x0

    throw p0
.end method

.method public d()Lh1/c;
    .locals 0
    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    const/4 p0, 0x0

    throw p0
.end method

.method public e()Lh1/c;
    .locals 0
    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    const/4 p0, 0x0

    throw p0
.end method

.method public f()Lh1/c;
    .locals 0
    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    const/4 p0, 0x0

    throw p0
.end method

.method public g()I
    .locals 0

    const/4 p0, 0x0

    throw p0
.end method

.method public h()Z
    .locals 0

    const/4 p0, 0x0

    throw p0
.end method

.method public i()Z
    .locals 0

    const/4 p0, 0x0

    throw p0
.end method

.method public j(Lh1/c;FF)V
    .locals 0
    .param p1    # Lh1/c;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    const/4 p0, 0x0

    throw p0
.end method
