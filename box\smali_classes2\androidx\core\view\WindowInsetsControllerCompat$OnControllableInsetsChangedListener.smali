.class public interface abstract Landroidx/core/view/WindowInsetsControllerCompat$OnControllableInsetsChangedListener;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/core/view/WindowInsetsControllerCompat;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "OnControllableInsetsChangedListener"
.end annotation


# virtual methods
.method public abstract onControllableInsetsChanged(Landroidx/core/view/WindowInsetsControllerCompat;I)V
    .param p1    # Landroidx/core/view/WindowInsetsControllerCompat;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
.end method
