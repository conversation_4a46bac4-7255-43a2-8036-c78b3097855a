.class public interface abstract Landroidx/core/view/accessibility/AccessibilityManagerCompat$AccessibilityStateChangeListener;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/core/view/accessibility/AccessibilityManagerCompat;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "AccessibilityStateChangeListener"
.end annotation

.annotation runtime Ljava/lang/Deprecated;
.end annotation


# virtual methods
.method public abstract onAccessibilityStateChanged(Z)V
    .annotation runtime Ljava/lang/Deprecated;
    .end annotation
.end method
