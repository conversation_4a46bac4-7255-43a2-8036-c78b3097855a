.class public abstract Landroidx/dynamicanimation/animation/b$r;
.super Landroidx/dynamicanimation/animation/c;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/dynamicanimation/animation/b;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x409
    name = "r"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Landroidx/dynamicanimation/animation/c<",
        "Landroid/view/View;",
        ">;"
    }
.end annotation


# direct methods
.method public constructor <init>(Ljava/lang/String;)V
    .locals 0

    invoke-direct {p0, p1}, Landroidx/dynamicanimation/animation/c;-><init>(Ljava/lang/String;)V

    return-void
.end method

.method public synthetic constructor <init>(Ljava/lang/String;Landroidx/dynamicanimation/animation/b$f;)V
    .locals 0

    invoke-direct {p0, p1}, Landroidx/dynamicanimation/animation/b$r;-><init>(Ljava/lang/String;)V

    return-void
.end method
