.class public Landroidx/emoji2/text/s$a;
.super Ljava/lang/Object;


# annotations
.annotation build Landroidx/annotation/RequiresApi;
    value = 0x18
.end annotation

.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/emoji2/text/s;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "a"
.end annotation


# direct methods
.method public static a(Ljava/lang/CharSequence;)Ljava/util/stream/IntStream;
    .locals 0

    invoke-static {p0}, Landroidx/emoji2/text/q;->a(Ljava/lang/CharSequence;)Ljava/util/stream/IntStream;

    move-result-object p0

    return-object p0
.end method

.method public static b(Ljava/lang/CharSequence;)Ljava/util/stream/IntStream;
    .locals 0

    invoke-static {p0}, Landroidx/emoji2/text/r;->a(<PERSON><PERSON><PERSON>/lang/CharSequence;)Ljava/util/stream/IntStream;

    move-result-object p0

    return-object p0
.end method
