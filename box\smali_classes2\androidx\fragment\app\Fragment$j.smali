.class public abstract Landroidx/fragment/app/Fragment$j;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/fragment/app/Fragment;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x409
    name = "j"
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public synthetic constructor <init>(Landroidx/fragment/app/Fragment$1;)V
    .locals 0

    invoke-direct {p0}, Landroidx/fragment/app/Fragment$j;-><init>()V

    return-void
.end method


# virtual methods
.method public abstract a()V
.end method
