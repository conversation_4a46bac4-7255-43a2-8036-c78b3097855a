.class public interface abstract Landroidx/fragment/app/t;
.super Ljava/lang/Object;


# virtual methods
.method public abstract a(Landroidx/fragment/app/FragmentManager;Landroidx/fragment/app/Fragment;)V
    .param p1    # Landroidx/fragment/app/FragmentManager;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p2    # Landroidx/fragment/app/Fragment;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
.end method
