.class public final Landroidx/lifecycle/h0$d;
.super Ljava/lang/Object;

# interfaces
.implements Landroidx/lifecycle/j0$a;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Landroidx/lifecycle/h0;-><init>()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation


# instance fields
.field public final synthetic a:Landroidx/lifecycle/h0;


# direct methods
.method public constructor <init>(Landroidx/lifecycle/h0;)V
    .locals 0

    iput-object p1, p0, Landroidx/lifecycle/h0$d;->a:Landroidx/lifecycle/h0;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public onCreate()V
    .locals 0

    return-void
.end method

.method public onResume()V
    .locals 1

    iget-object v0, p0, Landroidx/lifecycle/h0$d;->a:Landroidx/lifecycle/h0;

    invoke-virtual {v0}, Landroidx/lifecycle/h0;->e()V

    return-void
.end method

.method public onStart()V
    .locals 1

    iget-object v0, p0, Landroidx/lifecycle/h0$d;->a:Landroidx/lifecycle/h0;

    invoke-virtual {v0}, Landroidx/lifecycle/h0;->f()V

    return-void
.end method
