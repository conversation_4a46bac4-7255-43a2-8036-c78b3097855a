.class public final Landroidx/lifecycle/y;
.super Ljava/lang/Object;


# annotations
.annotation runtime Lkotlin/Metadata;
.end annotation


# direct methods
.method public static final a()Z
    .locals 1
    .annotation build Landroid/annotation/SuppressLint;
        value = {
            "RestrictedApi"
        }
    .end annotation

    invoke-static {}, Lm/c;->h()Lm/c;

    move-result-object v0

    invoke-virtual {v0}, Lm/c;->c()Z

    move-result v0

    return v0
.end method
