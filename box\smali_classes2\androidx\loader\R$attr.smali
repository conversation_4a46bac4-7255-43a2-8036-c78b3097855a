.class public final Landroidx/loader/R$attr;
.super Ljava/lang/Object;


# static fields
.field public static alpha:I = 0x7f040040

.field public static font:I = 0x7f040399

.field public static fontProviderAuthority:I = 0x7f04039b

.field public static fontProviderCerts:I = 0x7f04039c

.field public static fontProviderFetchStrategy:I = 0x7f04039d

.field public static fontProviderFetchTimeout:I = 0x7f04039e

.field public static fontProviderPackage:I = 0x7f04039f

.field public static fontProviderQuery:I = 0x7f0403a0

.field public static fontStyle:I = 0x7f0403a2

.field public static fontVariationSettings:I = 0x7f0403a3

.field public static fontWeight:I = 0x7f0403a4

.field public static ttcIndex:I = 0x7f0407a3


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
