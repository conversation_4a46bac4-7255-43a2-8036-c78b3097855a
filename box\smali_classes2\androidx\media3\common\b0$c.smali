.class public final Landroidx/media3/common/b0$c;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/media3/common/b0;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "c"
.end annotation


# instance fields
.field public a:Ljava/lang/String;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public b:Landroid/net/Uri;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public c:Ljava/lang/String;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public d:Landroidx/media3/common/b0$d$a;

.field public e:Landroidx/media3/common/b0$f$a;

.field public f:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Landroidx/media3/common/StreamKey;",
            ">;"
        }
    .end annotation
.end field

.field public g:Ljava/lang/String;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public h:Lcom/google/common/collect/ImmutableList;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/google/common/collect/ImmutableList<",
            "Landroidx/media3/common/b0$k;",
            ">;"
        }
    .end annotation
.end field

.field public i:Ljava/lang/Object;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public j:J

.field public k:Landroidx/media3/common/d0;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public l:Landroidx/media3/common/b0$g$a;

.field public m:Landroidx/media3/common/b0$i;


# direct methods
.method public constructor <init>()V
    .locals 2

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    new-instance v0, Landroidx/media3/common/b0$d$a;

    invoke-direct {v0}, Landroidx/media3/common/b0$d$a;-><init>()V

    iput-object v0, p0, Landroidx/media3/common/b0$c;->d:Landroidx/media3/common/b0$d$a;

    new-instance v0, Landroidx/media3/common/b0$f$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Landroidx/media3/common/b0$f$a;-><init>(Landroidx/media3/common/b0$a;)V

    iput-object v0, p0, Landroidx/media3/common/b0$c;->e:Landroidx/media3/common/b0$f$a;

    invoke-static {}, Ljava/util/Collections;->emptyList()Ljava/util/List;

    move-result-object v0

    iput-object v0, p0, Landroidx/media3/common/b0$c;->f:Ljava/util/List;

    invoke-static {}, Lcom/google/common/collect/ImmutableList;->of()Lcom/google/common/collect/ImmutableList;

    move-result-object v0

    iput-object v0, p0, Landroidx/media3/common/b0$c;->h:Lcom/google/common/collect/ImmutableList;

    new-instance v0, Landroidx/media3/common/b0$g$a;

    invoke-direct {v0}, Landroidx/media3/common/b0$g$a;-><init>()V

    iput-object v0, p0, Landroidx/media3/common/b0$c;->l:Landroidx/media3/common/b0$g$a;

    sget-object v0, Landroidx/media3/common/b0$i;->d:Landroidx/media3/common/b0$i;

    iput-object v0, p0, Landroidx/media3/common/b0$c;->m:Landroidx/media3/common/b0$i;

    const-wide v0, -0x7fffffffffffffffL    # -4.9E-324

    iput-wide v0, p0, Landroidx/media3/common/b0$c;->j:J

    return-void
.end method

.method public constructor <init>(Landroidx/media3/common/b0;)V
    .locals 2

    invoke-direct {p0}, Landroidx/media3/common/b0$c;-><init>()V

    iget-object v0, p1, Landroidx/media3/common/b0;->f:Landroidx/media3/common/b0$d;

    invoke-virtual {v0}, Landroidx/media3/common/b0$d;->a()Landroidx/media3/common/b0$d$a;

    move-result-object v0

    iput-object v0, p0, Landroidx/media3/common/b0$c;->d:Landroidx/media3/common/b0$d$a;

    iget-object v0, p1, Landroidx/media3/common/b0;->a:Ljava/lang/String;

    iput-object v0, p0, Landroidx/media3/common/b0$c;->a:Ljava/lang/String;

    iget-object v0, p1, Landroidx/media3/common/b0;->e:Landroidx/media3/common/d0;

    iput-object v0, p0, Landroidx/media3/common/b0$c;->k:Landroidx/media3/common/d0;

    iget-object v0, p1, Landroidx/media3/common/b0;->d:Landroidx/media3/common/b0$g;

    invoke-virtual {v0}, Landroidx/media3/common/b0$g;->a()Landroidx/media3/common/b0$g$a;

    move-result-object v0

    iput-object v0, p0, Landroidx/media3/common/b0$c;->l:Landroidx/media3/common/b0$g$a;

    iget-object v0, p1, Landroidx/media3/common/b0;->h:Landroidx/media3/common/b0$i;

    iput-object v0, p0, Landroidx/media3/common/b0$c;->m:Landroidx/media3/common/b0$i;

    iget-object p1, p1, Landroidx/media3/common/b0;->b:Landroidx/media3/common/b0$h;

    if-eqz p1, :cond_1

    iget-object v0, p1, Landroidx/media3/common/b0$h;->e:Ljava/lang/String;

    iput-object v0, p0, Landroidx/media3/common/b0$c;->g:Ljava/lang/String;

    iget-object v0, p1, Landroidx/media3/common/b0$h;->b:Ljava/lang/String;

    iput-object v0, p0, Landroidx/media3/common/b0$c;->c:Ljava/lang/String;

    iget-object v0, p1, Landroidx/media3/common/b0$h;->a:Landroid/net/Uri;

    iput-object v0, p0, Landroidx/media3/common/b0$c;->b:Landroid/net/Uri;

    iget-object v0, p1, Landroidx/media3/common/b0$h;->d:Ljava/util/List;

    iput-object v0, p0, Landroidx/media3/common/b0$c;->f:Ljava/util/List;

    iget-object v0, p1, Landroidx/media3/common/b0$h;->f:Lcom/google/common/collect/ImmutableList;

    iput-object v0, p0, Landroidx/media3/common/b0$c;->h:Lcom/google/common/collect/ImmutableList;

    iget-object v0, p1, Landroidx/media3/common/b0$h;->h:Ljava/lang/Object;

    iput-object v0, p0, Landroidx/media3/common/b0$c;->i:Ljava/lang/Object;

    iget-object v0, p1, Landroidx/media3/common/b0$h;->c:Landroidx/media3/common/b0$f;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Landroidx/media3/common/b0$f;->b()Landroidx/media3/common/b0$f$a;

    move-result-object v0

    goto :goto_0

    :cond_0
    new-instance v0, Landroidx/media3/common/b0$f$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Landroidx/media3/common/b0$f$a;-><init>(Landroidx/media3/common/b0$a;)V

    :goto_0
    iput-object v0, p0, Landroidx/media3/common/b0$c;->e:Landroidx/media3/common/b0$f$a;

    iget-wide v0, p1, Landroidx/media3/common/b0$h;->i:J

    iput-wide v0, p0, Landroidx/media3/common/b0$c;->j:J

    :cond_1
    return-void
.end method

.method public synthetic constructor <init>(Landroidx/media3/common/b0;Landroidx/media3/common/b0$a;)V
    .locals 0

    invoke-direct {p0, p1}, Landroidx/media3/common/b0$c;-><init>(Landroidx/media3/common/b0;)V

    return-void
.end method


# virtual methods
.method public a()Landroidx/media3/common/b0;
    .locals 23

    move-object/from16 v0, p0

    iget-object v1, v0, Landroidx/media3/common/b0$c;->e:Landroidx/media3/common/b0$f$a;

    invoke-static {v1}, Landroidx/media3/common/b0$f$a;->e(Landroidx/media3/common/b0$f$a;)Landroid/net/Uri;

    move-result-object v1

    if-eqz v1, :cond_1

    iget-object v1, v0, Landroidx/media3/common/b0$c;->e:Landroidx/media3/common/b0$f$a;

    invoke-static {v1}, Landroidx/media3/common/b0$f$a;->f(Landroidx/media3/common/b0$f$a;)Ljava/util/UUID;

    move-result-object v1

    if-eqz v1, :cond_0

    goto :goto_0

    :cond_0
    const/4 v1, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 v1, 0x1

    :goto_1
    invoke-static {v1}, Le2/a;->g(Z)V

    iget-object v3, v0, Landroidx/media3/common/b0$c;->b:Landroid/net/Uri;

    const/4 v1, 0x0

    if-eqz v3, :cond_3

    new-instance v14, Landroidx/media3/common/b0$h;

    iget-object v4, v0, Landroidx/media3/common/b0$c;->c:Ljava/lang/String;

    iget-object v2, v0, Landroidx/media3/common/b0$c;->e:Landroidx/media3/common/b0$f$a;

    invoke-static {v2}, Landroidx/media3/common/b0$f$a;->f(Landroidx/media3/common/b0$f$a;)Ljava/util/UUID;

    move-result-object v2

    if-eqz v2, :cond_2

    iget-object v1, v0, Landroidx/media3/common/b0$c;->e:Landroidx/media3/common/b0$f$a;

    invoke-virtual {v1}, Landroidx/media3/common/b0$f$a;->i()Landroidx/media3/common/b0$f;

    move-result-object v1

    :cond_2
    move-object v5, v1

    const/4 v6, 0x0

    iget-object v7, v0, Landroidx/media3/common/b0$c;->f:Ljava/util/List;

    iget-object v8, v0, Landroidx/media3/common/b0$c;->g:Ljava/lang/String;

    iget-object v9, v0, Landroidx/media3/common/b0$c;->h:Lcom/google/common/collect/ImmutableList;

    iget-object v10, v0, Landroidx/media3/common/b0$c;->i:Ljava/lang/Object;

    iget-wide v11, v0, Landroidx/media3/common/b0$c;->j:J

    const/4 v13, 0x0

    move-object v2, v14

    invoke-direct/range {v2 .. v13}, Landroidx/media3/common/b0$h;-><init>(Landroid/net/Uri;Ljava/lang/String;Landroidx/media3/common/b0$f;Landroidx/media3/common/b0$b;Ljava/util/List;Ljava/lang/String;Lcom/google/common/collect/ImmutableList;Ljava/lang/Object;JLandroidx/media3/common/b0$a;)V

    move-object/from16 v18, v14

    goto :goto_2

    :cond_3
    move-object/from16 v18, v1

    :goto_2
    new-instance v1, Landroidx/media3/common/b0;

    iget-object v2, v0, Landroidx/media3/common/b0$c;->a:Ljava/lang/String;

    if-eqz v2, :cond_4

    :goto_3
    move-object/from16 v16, v2

    goto :goto_4

    :cond_4
    const-string v2, ""

    goto :goto_3

    :goto_4
    iget-object v2, v0, Landroidx/media3/common/b0$c;->d:Landroidx/media3/common/b0$d$a;

    invoke-virtual {v2}, Landroidx/media3/common/b0$d$a;->g()Landroidx/media3/common/b0$e;

    move-result-object v17

    iget-object v2, v0, Landroidx/media3/common/b0$c;->l:Landroidx/media3/common/b0$g$a;

    invoke-virtual {v2}, Landroidx/media3/common/b0$g$a;->f()Landroidx/media3/common/b0$g;

    move-result-object v19

    iget-object v2, v0, Landroidx/media3/common/b0$c;->k:Landroidx/media3/common/d0;

    if-eqz v2, :cond_5

    :goto_5
    move-object/from16 v20, v2

    goto :goto_6

    :cond_5
    sget-object v2, Landroidx/media3/common/d0;->G:Landroidx/media3/common/d0;

    goto :goto_5

    :goto_6
    iget-object v2, v0, Landroidx/media3/common/b0$c;->m:Landroidx/media3/common/b0$i;

    const/16 v22, 0x0

    move-object v15, v1

    move-object/from16 v21, v2

    invoke-direct/range {v15 .. v22}, Landroidx/media3/common/b0;-><init>(Ljava/lang/String;Landroidx/media3/common/b0$e;Landroidx/media3/common/b0$h;Landroidx/media3/common/b0$g;Landroidx/media3/common/d0;Landroidx/media3/common/b0$i;Landroidx/media3/common/b0$a;)V

    return-object v1
.end method

.method public b(Ljava/lang/String;)Landroidx/media3/common/b0$c;
    .locals 0
    .param p1    # Ljava/lang/String;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    iput-object p1, p0, Landroidx/media3/common/b0$c;->g:Ljava/lang/String;

    return-object p0
.end method

.method public c(Landroidx/media3/common/b0$g;)Landroidx/media3/common/b0$c;
    .locals 0

    invoke-virtual {p1}, Landroidx/media3/common/b0$g;->a()Landroidx/media3/common/b0$g$a;

    move-result-object p1

    iput-object p1, p0, Landroidx/media3/common/b0$c;->l:Landroidx/media3/common/b0$g$a;

    return-object p0
.end method

.method public d(Ljava/lang/String;)Landroidx/media3/common/b0$c;
    .locals 0

    invoke-static {p1}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/lang/String;

    iput-object p1, p0, Landroidx/media3/common/b0$c;->a:Ljava/lang/String;

    return-object p0
.end method

.method public e(Ljava/util/List;)Landroidx/media3/common/b0$c;
    .locals 1
    .param p1    # Ljava/util/List;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Landroidx/media3/common/StreamKey;",
            ">;)",
            "Landroidx/media3/common/b0$c;"
        }
    .end annotation

    if-eqz p1, :cond_0

    invoke-interface {p1}, Ljava/util/List;->isEmpty()Z

    move-result v0

    if-nez v0, :cond_0

    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0, p1}, Ljava/util/ArrayList;-><init>(Ljava/util/Collection;)V

    invoke-static {v0}, Ljava/util/Collections;->unmodifiableList(Ljava/util/List;)Ljava/util/List;

    move-result-object p1

    goto :goto_0

    :cond_0
    invoke-static {}, Ljava/util/Collections;->emptyList()Ljava/util/List;

    move-result-object p1

    :goto_0
    iput-object p1, p0, Landroidx/media3/common/b0$c;->f:Ljava/util/List;

    return-object p0
.end method

.method public f(Ljava/util/List;)Landroidx/media3/common/b0$c;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Landroidx/media3/common/b0$k;",
            ">;)",
            "Landroidx/media3/common/b0$c;"
        }
    .end annotation

    invoke-static {p1}, Lcom/google/common/collect/ImmutableList;->copyOf(Ljava/util/Collection;)Lcom/google/common/collect/ImmutableList;

    move-result-object p1

    iput-object p1, p0, Landroidx/media3/common/b0$c;->h:Lcom/google/common/collect/ImmutableList;

    return-object p0
.end method

.method public g(Ljava/lang/Object;)Landroidx/media3/common/b0$c;
    .locals 0
    .param p1    # Ljava/lang/Object;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    iput-object p1, p0, Landroidx/media3/common/b0$c;->i:Ljava/lang/Object;

    return-object p0
.end method

.method public h(Landroid/net/Uri;)Landroidx/media3/common/b0$c;
    .locals 0
    .param p1    # Landroid/net/Uri;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    iput-object p1, p0, Landroidx/media3/common/b0$c;->b:Landroid/net/Uri;

    return-object p0
.end method

.method public i(Ljava/lang/String;)Landroidx/media3/common/b0$c;
    .locals 0
    .param p1    # Ljava/lang/String;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    if-nez p1, :cond_0

    const/4 p1, 0x0

    goto :goto_0

    :cond_0
    invoke-static {p1}, Landroid/net/Uri;->parse(Ljava/lang/String;)Landroid/net/Uri;

    move-result-object p1

    :goto_0
    invoke-virtual {p0, p1}, Landroidx/media3/common/b0$c;->h(Landroid/net/Uri;)Landroidx/media3/common/b0$c;

    move-result-object p1

    return-object p1
.end method
