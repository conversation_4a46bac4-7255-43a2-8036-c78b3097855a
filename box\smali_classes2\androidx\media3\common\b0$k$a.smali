.class public final Landroidx/media3/common/b0$k$a;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/media3/common/b0$k;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation


# instance fields
.field public a:Landroid/net/Uri;

.field public b:Ljava/lang/String;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public c:Ljava/lang/String;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public d:I

.field public e:I

.field public f:Ljava/lang/String;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public g:Ljava/lang/String;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field


# direct methods
.method public constructor <init>(Landroidx/media3/common/b0$k;)V
    .locals 1

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iget-object v0, p1, Landroidx/media3/common/b0$k;->a:Landroid/net/Uri;

    iput-object v0, p0, Landroidx/media3/common/b0$k$a;->a:Landroid/net/Uri;

    iget-object v0, p1, Landroidx/media3/common/b0$k;->b:Ljava/lang/String;

    iput-object v0, p0, Landroidx/media3/common/b0$k$a;->b:Ljava/lang/String;

    iget-object v0, p1, Landroidx/media3/common/b0$k;->c:Ljava/lang/String;

    iput-object v0, p0, Landroidx/media3/common/b0$k$a;->c:Ljava/lang/String;

    iget v0, p1, Landroidx/media3/common/b0$k;->d:I

    iput v0, p0, Landroidx/media3/common/b0$k$a;->d:I

    iget v0, p1, Landroidx/media3/common/b0$k;->e:I

    iput v0, p0, Landroidx/media3/common/b0$k$a;->e:I

    iget-object v0, p1, Landroidx/media3/common/b0$k;->f:Ljava/lang/String;

    iput-object v0, p0, Landroidx/media3/common/b0$k$a;->f:Ljava/lang/String;

    iget-object p1, p1, Landroidx/media3/common/b0$k;->g:Ljava/lang/String;

    iput-object p1, p0, Landroidx/media3/common/b0$k$a;->g:Ljava/lang/String;

    return-void
.end method

.method public synthetic constructor <init>(Landroidx/media3/common/b0$k;Landroidx/media3/common/b0$a;)V
    .locals 0

    invoke-direct {p0, p1}, Landroidx/media3/common/b0$k$a;-><init>(Landroidx/media3/common/b0$k;)V

    return-void
.end method

.method public static synthetic a(Landroidx/media3/common/b0$k$a;)Landroidx/media3/common/b0$j;
    .locals 0

    invoke-virtual {p0}, Landroidx/media3/common/b0$k$a;->i()Landroidx/media3/common/b0$j;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(Landroidx/media3/common/b0$k$a;)Landroid/net/Uri;
    .locals 0

    iget-object p0, p0, Landroidx/media3/common/b0$k$a;->a:Landroid/net/Uri;

    return-object p0
.end method

.method public static synthetic c(Landroidx/media3/common/b0$k$a;)Ljava/lang/String;
    .locals 0

    iget-object p0, p0, Landroidx/media3/common/b0$k$a;->b:Ljava/lang/String;

    return-object p0
.end method

.method public static synthetic d(Landroidx/media3/common/b0$k$a;)Ljava/lang/String;
    .locals 0

    iget-object p0, p0, Landroidx/media3/common/b0$k$a;->c:Ljava/lang/String;

    return-object p0
.end method

.method public static synthetic e(Landroidx/media3/common/b0$k$a;)I
    .locals 0

    iget p0, p0, Landroidx/media3/common/b0$k$a;->d:I

    return p0
.end method

.method public static synthetic f(Landroidx/media3/common/b0$k$a;)I
    .locals 0

    iget p0, p0, Landroidx/media3/common/b0$k$a;->e:I

    return p0
.end method

.method public static synthetic g(Landroidx/media3/common/b0$k$a;)Ljava/lang/String;
    .locals 0

    iget-object p0, p0, Landroidx/media3/common/b0$k$a;->f:Ljava/lang/String;

    return-object p0
.end method

.method public static synthetic h(Landroidx/media3/common/b0$k$a;)Ljava/lang/String;
    .locals 0

    iget-object p0, p0, Landroidx/media3/common/b0$k$a;->g:Ljava/lang/String;

    return-object p0
.end method


# virtual methods
.method public final i()Landroidx/media3/common/b0$j;
    .locals 2

    new-instance v0, Landroidx/media3/common/b0$j;

    const/4 v1, 0x0

    invoke-direct {v0, p0, v1}, Landroidx/media3/common/b0$j;-><init>(Landroidx/media3/common/b0$k$a;Landroidx/media3/common/b0$a;)V

    return-object v0
.end method
