.class public final Landroidx/media3/common/d;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Landroidx/media3/common/d$d;,
        Landroidx/media3/common/d$e;,
        Landroidx/media3/common/d$c;,
        Landroidx/media3/common/d$b;
    }
.end annotation


# static fields
.field public static final g:Landroidx/media3/common/d;

.field public static final h:Ljava/lang/String;

.field public static final i:Ljava/lang/String;

.field public static final j:Ljava/lang/String;

.field public static final k:Ljava/lang/String;

.field public static final l:Ljava/lang/String;

.field public static final m:Landroidx/media3/common/i;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Landroidx/media3/common/i<",
            "Landroidx/media3/common/d;",
            ">;"
        }
    .end annotation

    .annotation runtime Ljava/lang/Deprecated;
    .end annotation
.end field


# instance fields
.field public final a:I

.field public final b:I

.field public final c:I

.field public final d:I

.field public final e:I

.field public f:Landroidx/media3/common/d$d;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 1

    new-instance v0, Landroidx/media3/common/d$e;

    invoke-direct {v0}, Landroidx/media3/common/d$e;-><init>()V

    invoke-virtual {v0}, Landroidx/media3/common/d$e;->a()Landroidx/media3/common/d;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/d;->g:Landroidx/media3/common/d;

    const/4 v0, 0x0

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/d;->h:Ljava/lang/String;

    const/4 v0, 0x1

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/d;->i:Ljava/lang/String;

    const/4 v0, 0x2

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/d;->j:Ljava/lang/String;

    const/4 v0, 0x3

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/d;->k:Ljava/lang/String;

    const/4 v0, 0x4

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/d;->l:Ljava/lang/String;

    new-instance v0, Landroidx/media3/common/b;

    invoke-direct {v0}, Landroidx/media3/common/b;-><init>()V

    sput-object v0, Landroidx/media3/common/d;->m:Landroidx/media3/common/i;

    return-void
.end method

.method public constructor <init>(IIIII)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput p1, p0, Landroidx/media3/common/d;->a:I

    iput p2, p0, Landroidx/media3/common/d;->b:I

    iput p3, p0, Landroidx/media3/common/d;->c:I

    iput p4, p0, Landroidx/media3/common/d;->d:I

    iput p5, p0, Landroidx/media3/common/d;->e:I

    return-void
.end method

.method public synthetic constructor <init>(IIIIILandroidx/media3/common/d$a;)V
    .locals 0

    invoke-direct/range {p0 .. p5}, Landroidx/media3/common/d;-><init>(IIIII)V

    return-void
.end method


# virtual methods
.method public a()Landroidx/media3/common/d$d;
    .locals 2
    .annotation build Landroidx/annotation/RequiresApi;
        value = 0x15
    .end annotation

    iget-object v0, p0, Landroidx/media3/common/d;->f:Landroidx/media3/common/d$d;

    if-nez v0, :cond_0

    new-instance v0, Landroidx/media3/common/d$d;

    const/4 v1, 0x0

    invoke-direct {v0, p0, v1}, Landroidx/media3/common/d$d;-><init>(Landroidx/media3/common/d;Landroidx/media3/common/d$a;)V

    iput-object v0, p0, Landroidx/media3/common/d;->f:Landroidx/media3/common/d$d;

    :cond_0
    iget-object v0, p0, Landroidx/media3/common/d;->f:Landroidx/media3/common/d$d;

    return-object v0
.end method

.method public equals(Ljava/lang/Object;)Z
    .locals 4
    .param p1    # Ljava/lang/Object;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    const/4 v0, 0x1

    if-ne p0, p1, :cond_0

    return v0

    :cond_0
    const/4 v1, 0x0

    if-eqz p1, :cond_3

    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v2

    const-class v3, Landroidx/media3/common/d;

    if-eq v3, v2, :cond_1

    goto :goto_1

    :cond_1
    check-cast p1, Landroidx/media3/common/d;

    iget v2, p0, Landroidx/media3/common/d;->a:I

    iget v3, p1, Landroidx/media3/common/d;->a:I

    if-ne v2, v3, :cond_2

    iget v2, p0, Landroidx/media3/common/d;->b:I

    iget v3, p1, Landroidx/media3/common/d;->b:I

    if-ne v2, v3, :cond_2

    iget v2, p0, Landroidx/media3/common/d;->c:I

    iget v3, p1, Landroidx/media3/common/d;->c:I

    if-ne v2, v3, :cond_2

    iget v2, p0, Landroidx/media3/common/d;->d:I

    iget v3, p1, Landroidx/media3/common/d;->d:I

    if-ne v2, v3, :cond_2

    iget v2, p0, Landroidx/media3/common/d;->e:I

    iget p1, p1, Landroidx/media3/common/d;->e:I

    if-ne v2, p1, :cond_2

    goto :goto_0

    :cond_2
    const/4 v0, 0x0

    :goto_0
    return v0

    :cond_3
    :goto_1
    return v1
.end method

.method public hashCode()I
    .locals 2

    const/16 v0, 0x20f

    iget v1, p0, Landroidx/media3/common/d;->a:I

    add-int/2addr v0, v1

    mul-int/lit8 v0, v0, 0x1f

    iget v1, p0, Landroidx/media3/common/d;->b:I

    add-int/2addr v0, v1

    mul-int/lit8 v0, v0, 0x1f

    iget v1, p0, Landroidx/media3/common/d;->c:I

    add-int/2addr v0, v1

    mul-int/lit8 v0, v0, 0x1f

    iget v1, p0, Landroidx/media3/common/d;->d:I

    add-int/2addr v0, v1

    mul-int/lit8 v0, v0, 0x1f

    iget v1, p0, Landroidx/media3/common/d;->e:I

    add-int/2addr v0, v1

    return v0
.end method
