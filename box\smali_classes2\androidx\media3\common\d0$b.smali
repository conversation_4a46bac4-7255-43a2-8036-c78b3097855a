.class public final Landroidx/media3/common/d0$b;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/media3/common/d0;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "b"
.end annotation


# instance fields
.field public A:Ljava/lang/CharSequence;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public B:Ljava/lang/CharSequence;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public C:Ljava/lang/CharSequence;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public D:Ljava/lang/Integer;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public E:Landroid/os/Bundle;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public a:Ljava/lang/CharSequence;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public b:Ljava/lang/CharSequence;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public c:Ljava/lang/CharSequence;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public d:Ljava/lang/CharSequence;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public e:Ljava/lang/CharSequence;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public f:Ljava/lang/CharSequence;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public g:Ljava/lang/CharSequence;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public h:[B
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public i:Ljava/lang/Integer;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public j:Landroid/net/Uri;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public k:Ljava/lang/Integer;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public l:Ljava/lang/Integer;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public m:Ljava/lang/Integer;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public n:Ljava/lang/Boolean;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public o:Ljava/lang/Boolean;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public p:Ljava/lang/Integer;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public q:Ljava/lang/Integer;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public r:Ljava/lang/Integer;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public s:Ljava/lang/Integer;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public t:Ljava/lang/Integer;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public u:Ljava/lang/Integer;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public v:Ljava/lang/CharSequence;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public w:Ljava/lang/CharSequence;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public x:Ljava/lang/CharSequence;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public y:Ljava/lang/Integer;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public z:Ljava/lang/Integer;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public constructor <init>(Landroidx/media3/common/d0;)V
    .locals 1

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iget-object v0, p1, Landroidx/media3/common/d0;->a:Ljava/lang/CharSequence;

    iput-object v0, p0, Landroidx/media3/common/d0$b;->a:Ljava/lang/CharSequence;

    iget-object v0, p1, Landroidx/media3/common/d0;->b:Ljava/lang/CharSequence;

    iput-object v0, p0, Landroidx/media3/common/d0$b;->b:Ljava/lang/CharSequence;

    iget-object v0, p1, Landroidx/media3/common/d0;->c:Ljava/lang/CharSequence;

    iput-object v0, p0, Landroidx/media3/common/d0$b;->c:Ljava/lang/CharSequence;

    iget-object v0, p1, Landroidx/media3/common/d0;->d:Ljava/lang/CharSequence;

    iput-object v0, p0, Landroidx/media3/common/d0$b;->d:Ljava/lang/CharSequence;

    iget-object v0, p1, Landroidx/media3/common/d0;->e:Ljava/lang/CharSequence;

    iput-object v0, p0, Landroidx/media3/common/d0$b;->e:Ljava/lang/CharSequence;

    iget-object v0, p1, Landroidx/media3/common/d0;->f:Ljava/lang/CharSequence;

    iput-object v0, p0, Landroidx/media3/common/d0$b;->f:Ljava/lang/CharSequence;

    iget-object v0, p1, Landroidx/media3/common/d0;->g:Ljava/lang/CharSequence;

    iput-object v0, p0, Landroidx/media3/common/d0$b;->g:Ljava/lang/CharSequence;

    iget-object v0, p1, Landroidx/media3/common/d0;->h:[B

    iput-object v0, p0, Landroidx/media3/common/d0$b;->h:[B

    iget-object v0, p1, Landroidx/media3/common/d0;->i:Ljava/lang/Integer;

    iput-object v0, p0, Landroidx/media3/common/d0$b;->i:Ljava/lang/Integer;

    iget-object v0, p1, Landroidx/media3/common/d0;->j:Landroid/net/Uri;

    iput-object v0, p0, Landroidx/media3/common/d0$b;->j:Landroid/net/Uri;

    iget-object v0, p1, Landroidx/media3/common/d0;->k:Ljava/lang/Integer;

    iput-object v0, p0, Landroidx/media3/common/d0$b;->k:Ljava/lang/Integer;

    iget-object v0, p1, Landroidx/media3/common/d0;->l:Ljava/lang/Integer;

    iput-object v0, p0, Landroidx/media3/common/d0$b;->l:Ljava/lang/Integer;

    iget-object v0, p1, Landroidx/media3/common/d0;->m:Ljava/lang/Integer;

    iput-object v0, p0, Landroidx/media3/common/d0$b;->m:Ljava/lang/Integer;

    iget-object v0, p1, Landroidx/media3/common/d0;->n:Ljava/lang/Boolean;

    iput-object v0, p0, Landroidx/media3/common/d0$b;->n:Ljava/lang/Boolean;

    iget-object v0, p1, Landroidx/media3/common/d0;->o:Ljava/lang/Boolean;

    iput-object v0, p0, Landroidx/media3/common/d0$b;->o:Ljava/lang/Boolean;

    iget-object v0, p1, Landroidx/media3/common/d0;->q:Ljava/lang/Integer;

    iput-object v0, p0, Landroidx/media3/common/d0$b;->p:Ljava/lang/Integer;

    iget-object v0, p1, Landroidx/media3/common/d0;->r:Ljava/lang/Integer;

    iput-object v0, p0, Landroidx/media3/common/d0$b;->q:Ljava/lang/Integer;

    iget-object v0, p1, Landroidx/media3/common/d0;->s:Ljava/lang/Integer;

    iput-object v0, p0, Landroidx/media3/common/d0$b;->r:Ljava/lang/Integer;

    iget-object v0, p1, Landroidx/media3/common/d0;->t:Ljava/lang/Integer;

    iput-object v0, p0, Landroidx/media3/common/d0$b;->s:Ljava/lang/Integer;

    iget-object v0, p1, Landroidx/media3/common/d0;->u:Ljava/lang/Integer;

    iput-object v0, p0, Landroidx/media3/common/d0$b;->t:Ljava/lang/Integer;

    iget-object v0, p1, Landroidx/media3/common/d0;->v:Ljava/lang/Integer;

    iput-object v0, p0, Landroidx/media3/common/d0$b;->u:Ljava/lang/Integer;

    iget-object v0, p1, Landroidx/media3/common/d0;->w:Ljava/lang/CharSequence;

    iput-object v0, p0, Landroidx/media3/common/d0$b;->v:Ljava/lang/CharSequence;

    iget-object v0, p1, Landroidx/media3/common/d0;->x:Ljava/lang/CharSequence;

    iput-object v0, p0, Landroidx/media3/common/d0$b;->w:Ljava/lang/CharSequence;

    iget-object v0, p1, Landroidx/media3/common/d0;->y:Ljava/lang/CharSequence;

    iput-object v0, p0, Landroidx/media3/common/d0$b;->x:Ljava/lang/CharSequence;

    iget-object v0, p1, Landroidx/media3/common/d0;->z:Ljava/lang/Integer;

    iput-object v0, p0, Landroidx/media3/common/d0$b;->y:Ljava/lang/Integer;

    iget-object v0, p1, Landroidx/media3/common/d0;->A:Ljava/lang/Integer;

    iput-object v0, p0, Landroidx/media3/common/d0$b;->z:Ljava/lang/Integer;

    iget-object v0, p1, Landroidx/media3/common/d0;->B:Ljava/lang/CharSequence;

    iput-object v0, p0, Landroidx/media3/common/d0$b;->A:Ljava/lang/CharSequence;

    iget-object v0, p1, Landroidx/media3/common/d0;->C:Ljava/lang/CharSequence;

    iput-object v0, p0, Landroidx/media3/common/d0$b;->B:Ljava/lang/CharSequence;

    iget-object v0, p1, Landroidx/media3/common/d0;->D:Ljava/lang/CharSequence;

    iput-object v0, p0, Landroidx/media3/common/d0$b;->C:Ljava/lang/CharSequence;

    iget-object v0, p1, Landroidx/media3/common/d0;->E:Ljava/lang/Integer;

    iput-object v0, p0, Landroidx/media3/common/d0$b;->D:Ljava/lang/Integer;

    iget-object p1, p1, Landroidx/media3/common/d0;->F:Landroid/os/Bundle;

    iput-object p1, p0, Landroidx/media3/common/d0$b;->E:Landroid/os/Bundle;

    return-void
.end method

.method public synthetic constructor <init>(Landroidx/media3/common/d0;Landroidx/media3/common/d0$a;)V
    .locals 0

    invoke-direct {p0, p1}, Landroidx/media3/common/d0$b;-><init>(Landroidx/media3/common/d0;)V

    return-void
.end method

.method public static synthetic A(Landroidx/media3/common/d0$b;)Landroid/os/Bundle;
    .locals 0

    iget-object p0, p0, Landroidx/media3/common/d0$b;->E:Landroid/os/Bundle;

    return-object p0
.end method

.method public static synthetic B(Landroidx/media3/common/d0$b;)Ljava/lang/CharSequence;
    .locals 0

    iget-object p0, p0, Landroidx/media3/common/d0$b;->a:Ljava/lang/CharSequence;

    return-object p0
.end method

.method public static synthetic C(Landroidx/media3/common/d0$b;)Ljava/lang/CharSequence;
    .locals 0

    iget-object p0, p0, Landroidx/media3/common/d0$b;->b:Ljava/lang/CharSequence;

    return-object p0
.end method

.method public static synthetic D(Landroidx/media3/common/d0$b;)Ljava/lang/CharSequence;
    .locals 0

    iget-object p0, p0, Landroidx/media3/common/d0$b;->c:Ljava/lang/CharSequence;

    return-object p0
.end method

.method public static synthetic E(Landroidx/media3/common/d0$b;)Ljava/lang/CharSequence;
    .locals 0

    iget-object p0, p0, Landroidx/media3/common/d0$b;->d:Ljava/lang/CharSequence;

    return-object p0
.end method

.method public static synthetic F(Landroidx/media3/common/d0$b;)Ljava/lang/CharSequence;
    .locals 0

    iget-object p0, p0, Landroidx/media3/common/d0$b;->e:Ljava/lang/CharSequence;

    return-object p0
.end method

.method public static synthetic G(Landroidx/media3/common/d0$b;)Ljava/lang/CharSequence;
    .locals 0

    iget-object p0, p0, Landroidx/media3/common/d0$b;->f:Ljava/lang/CharSequence;

    return-object p0
.end method

.method public static synthetic a(Landroidx/media3/common/d0$b;)Ljava/lang/Boolean;
    .locals 0

    iget-object p0, p0, Landroidx/media3/common/d0$b;->n:Ljava/lang/Boolean;

    return-object p0
.end method

.method public static synthetic b(Landroidx/media3/common/d0$b;)Ljava/lang/CharSequence;
    .locals 0

    iget-object p0, p0, Landroidx/media3/common/d0$b;->g:Ljava/lang/CharSequence;

    return-object p0
.end method

.method public static synthetic c(Landroidx/media3/common/d0$b;)Landroidx/media3/common/k0;
    .locals 0

    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    const/4 p0, 0x0

    return-object p0
.end method

.method public static synthetic d(Landroidx/media3/common/d0$b;)Landroidx/media3/common/k0;
    .locals 0

    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    const/4 p0, 0x0

    return-object p0
.end method

.method public static synthetic e(Landroidx/media3/common/d0$b;)[B
    .locals 0

    iget-object p0, p0, Landroidx/media3/common/d0$b;->h:[B

    return-object p0
.end method

.method public static synthetic f(Landroidx/media3/common/d0$b;)Ljava/lang/Integer;
    .locals 0

    iget-object p0, p0, Landroidx/media3/common/d0$b;->i:Ljava/lang/Integer;

    return-object p0
.end method

.method public static synthetic g(Landroidx/media3/common/d0$b;)Landroid/net/Uri;
    .locals 0

    iget-object p0, p0, Landroidx/media3/common/d0$b;->j:Landroid/net/Uri;

    return-object p0
.end method

.method public static synthetic h(Landroidx/media3/common/d0$b;)Ljava/lang/Integer;
    .locals 0

    iget-object p0, p0, Landroidx/media3/common/d0$b;->k:Ljava/lang/Integer;

    return-object p0
.end method

.method public static synthetic i(Landroidx/media3/common/d0$b;)Ljava/lang/Integer;
    .locals 0

    iget-object p0, p0, Landroidx/media3/common/d0$b;->l:Ljava/lang/Integer;

    return-object p0
.end method

.method public static synthetic j(Landroidx/media3/common/d0$b;)Ljava/lang/Boolean;
    .locals 0

    iget-object p0, p0, Landroidx/media3/common/d0$b;->o:Ljava/lang/Boolean;

    return-object p0
.end method

.method public static synthetic k(Landroidx/media3/common/d0$b;)Ljava/lang/Integer;
    .locals 0

    iget-object p0, p0, Landroidx/media3/common/d0$b;->p:Ljava/lang/Integer;

    return-object p0
.end method

.method public static synthetic l(Landroidx/media3/common/d0$b;)Ljava/lang/Integer;
    .locals 0

    iget-object p0, p0, Landroidx/media3/common/d0$b;->m:Ljava/lang/Integer;

    return-object p0
.end method

.method public static synthetic m(Landroidx/media3/common/d0$b;)Ljava/lang/Integer;
    .locals 0

    iget-object p0, p0, Landroidx/media3/common/d0$b;->q:Ljava/lang/Integer;

    return-object p0
.end method

.method public static synthetic n(Landroidx/media3/common/d0$b;)Ljava/lang/Integer;
    .locals 0

    iget-object p0, p0, Landroidx/media3/common/d0$b;->r:Ljava/lang/Integer;

    return-object p0
.end method

.method public static synthetic o(Landroidx/media3/common/d0$b;)Ljava/lang/Integer;
    .locals 0

    iget-object p0, p0, Landroidx/media3/common/d0$b;->s:Ljava/lang/Integer;

    return-object p0
.end method

.method public static synthetic p(Landroidx/media3/common/d0$b;)Ljava/lang/Integer;
    .locals 0

    iget-object p0, p0, Landroidx/media3/common/d0$b;->t:Ljava/lang/Integer;

    return-object p0
.end method

.method public static synthetic q(Landroidx/media3/common/d0$b;)Ljava/lang/Integer;
    .locals 0

    iget-object p0, p0, Landroidx/media3/common/d0$b;->u:Ljava/lang/Integer;

    return-object p0
.end method

.method public static synthetic r(Landroidx/media3/common/d0$b;)Ljava/lang/CharSequence;
    .locals 0

    iget-object p0, p0, Landroidx/media3/common/d0$b;->v:Ljava/lang/CharSequence;

    return-object p0
.end method

.method public static synthetic s(Landroidx/media3/common/d0$b;)Ljava/lang/CharSequence;
    .locals 0

    iget-object p0, p0, Landroidx/media3/common/d0$b;->w:Ljava/lang/CharSequence;

    return-object p0
.end method

.method public static synthetic t(Landroidx/media3/common/d0$b;)Ljava/lang/CharSequence;
    .locals 0

    iget-object p0, p0, Landroidx/media3/common/d0$b;->x:Ljava/lang/CharSequence;

    return-object p0
.end method

.method public static synthetic u(Landroidx/media3/common/d0$b;)Ljava/lang/Integer;
    .locals 0

    iget-object p0, p0, Landroidx/media3/common/d0$b;->y:Ljava/lang/Integer;

    return-object p0
.end method

.method public static synthetic v(Landroidx/media3/common/d0$b;)Ljava/lang/Integer;
    .locals 0

    iget-object p0, p0, Landroidx/media3/common/d0$b;->z:Ljava/lang/Integer;

    return-object p0
.end method

.method public static synthetic w(Landroidx/media3/common/d0$b;)Ljava/lang/Integer;
    .locals 0

    iget-object p0, p0, Landroidx/media3/common/d0$b;->D:Ljava/lang/Integer;

    return-object p0
.end method

.method public static synthetic x(Landroidx/media3/common/d0$b;)Ljava/lang/CharSequence;
    .locals 0

    iget-object p0, p0, Landroidx/media3/common/d0$b;->A:Ljava/lang/CharSequence;

    return-object p0
.end method

.method public static synthetic y(Landroidx/media3/common/d0$b;)Ljava/lang/CharSequence;
    .locals 0

    iget-object p0, p0, Landroidx/media3/common/d0$b;->B:Ljava/lang/CharSequence;

    return-object p0
.end method

.method public static synthetic z(Landroidx/media3/common/d0$b;)Ljava/lang/CharSequence;
    .locals 0

    iget-object p0, p0, Landroidx/media3/common/d0$b;->C:Ljava/lang/CharSequence;

    return-object p0
.end method


# virtual methods
.method public H()Landroidx/media3/common/d0;
    .locals 2

    new-instance v0, Landroidx/media3/common/d0;

    const/4 v1, 0x0

    invoke-direct {v0, p0, v1}, Landroidx/media3/common/d0;-><init>(Landroidx/media3/common/d0$b;Landroidx/media3/common/d0$a;)V

    return-object v0
.end method

.method public I([BI)Landroidx/media3/common/d0$b;
    .locals 3

    iget-object v0, p0, Landroidx/media3/common/d0$b;->h:[B

    if-eqz v0, :cond_0

    invoke-static {p2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v0

    const/4 v1, 0x3

    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v2

    invoke-static {v0, v2}, Le2/u0;->c(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_0

    iget-object v0, p0, Landroidx/media3/common/d0$b;->i:Ljava/lang/Integer;

    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    invoke-static {v0, v1}, Le2/u0;->c(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_1

    :cond_0
    invoke-virtual {p1}, [B->clone()Ljava/lang/Object;

    move-result-object p1

    check-cast p1, [B

    iput-object p1, p0, Landroidx/media3/common/d0$b;->h:[B

    invoke-static {p2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p1

    iput-object p1, p0, Landroidx/media3/common/d0$b;->i:Ljava/lang/Integer;

    :cond_1
    return-object p0
.end method

.method public J(Landroidx/media3/common/d0;)Landroidx/media3/common/d0$b;
    .locals 2
    .param p1    # Landroidx/media3/common/d0;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    if-nez p1, :cond_0

    return-object p0

    :cond_0
    iget-object v0, p1, Landroidx/media3/common/d0;->a:Ljava/lang/CharSequence;

    if-eqz v0, :cond_1

    invoke-virtual {p0, v0}, Landroidx/media3/common/d0$b;->l0(Ljava/lang/CharSequence;)Landroidx/media3/common/d0$b;

    :cond_1
    iget-object v0, p1, Landroidx/media3/common/d0;->b:Ljava/lang/CharSequence;

    if-eqz v0, :cond_2

    invoke-virtual {p0, v0}, Landroidx/media3/common/d0$b;->O(Ljava/lang/CharSequence;)Landroidx/media3/common/d0$b;

    :cond_2
    iget-object v0, p1, Landroidx/media3/common/d0;->c:Ljava/lang/CharSequence;

    if-eqz v0, :cond_3

    invoke-virtual {p0, v0}, Landroidx/media3/common/d0$b;->N(Ljava/lang/CharSequence;)Landroidx/media3/common/d0$b;

    :cond_3
    iget-object v0, p1, Landroidx/media3/common/d0;->d:Ljava/lang/CharSequence;

    if-eqz v0, :cond_4

    invoke-virtual {p0, v0}, Landroidx/media3/common/d0$b;->M(Ljava/lang/CharSequence;)Landroidx/media3/common/d0$b;

    :cond_4
    iget-object v0, p1, Landroidx/media3/common/d0;->e:Ljava/lang/CharSequence;

    if-eqz v0, :cond_5

    invoke-virtual {p0, v0}, Landroidx/media3/common/d0$b;->W(Ljava/lang/CharSequence;)Landroidx/media3/common/d0$b;

    :cond_5
    iget-object v0, p1, Landroidx/media3/common/d0;->f:Ljava/lang/CharSequence;

    if-eqz v0, :cond_6

    invoke-virtual {p0, v0}, Landroidx/media3/common/d0$b;->k0(Ljava/lang/CharSequence;)Landroidx/media3/common/d0$b;

    :cond_6
    iget-object v0, p1, Landroidx/media3/common/d0;->g:Ljava/lang/CharSequence;

    if-eqz v0, :cond_7

    invoke-virtual {p0, v0}, Landroidx/media3/common/d0$b;->U(Ljava/lang/CharSequence;)Landroidx/media3/common/d0$b;

    :cond_7
    iget-object v0, p1, Landroidx/media3/common/d0;->j:Landroid/net/Uri;

    if-nez v0, :cond_8

    iget-object v1, p1, Landroidx/media3/common/d0;->h:[B

    if-eqz v1, :cond_9

    :cond_8
    invoke-virtual {p0, v0}, Landroidx/media3/common/d0$b;->Q(Landroid/net/Uri;)Landroidx/media3/common/d0$b;

    iget-object v0, p1, Landroidx/media3/common/d0;->h:[B

    iget-object v1, p1, Landroidx/media3/common/d0;->i:Ljava/lang/Integer;

    invoke-virtual {p0, v0, v1}, Landroidx/media3/common/d0$b;->P([BLjava/lang/Integer;)Landroidx/media3/common/d0$b;

    :cond_9
    iget-object v0, p1, Landroidx/media3/common/d0;->k:Ljava/lang/Integer;

    if-eqz v0, :cond_a

    invoke-virtual {p0, v0}, Landroidx/media3/common/d0$b;->o0(Ljava/lang/Integer;)Landroidx/media3/common/d0$b;

    :cond_a
    iget-object v0, p1, Landroidx/media3/common/d0;->l:Ljava/lang/Integer;

    if-eqz v0, :cond_b

    invoke-virtual {p0, v0}, Landroidx/media3/common/d0$b;->n0(Ljava/lang/Integer;)Landroidx/media3/common/d0$b;

    :cond_b
    iget-object v0, p1, Landroidx/media3/common/d0;->m:Ljava/lang/Integer;

    if-eqz v0, :cond_c

    invoke-virtual {p0, v0}, Landroidx/media3/common/d0$b;->Y(Ljava/lang/Integer;)Landroidx/media3/common/d0$b;

    :cond_c
    iget-object v0, p1, Landroidx/media3/common/d0;->n:Ljava/lang/Boolean;

    if-eqz v0, :cond_d

    invoke-virtual {p0, v0}, Landroidx/media3/common/d0$b;->a0(Ljava/lang/Boolean;)Landroidx/media3/common/d0$b;

    :cond_d
    iget-object v0, p1, Landroidx/media3/common/d0;->o:Ljava/lang/Boolean;

    if-eqz v0, :cond_e

    invoke-virtual {p0, v0}, Landroidx/media3/common/d0$b;->b0(Ljava/lang/Boolean;)Landroidx/media3/common/d0$b;

    :cond_e
    iget-object v0, p1, Landroidx/media3/common/d0;->p:Ljava/lang/Integer;

    if-eqz v0, :cond_f

    invoke-virtual {p0, v0}, Landroidx/media3/common/d0$b;->f0(Ljava/lang/Integer;)Landroidx/media3/common/d0$b;

    :cond_f
    iget-object v0, p1, Landroidx/media3/common/d0;->q:Ljava/lang/Integer;

    if-eqz v0, :cond_10

    invoke-virtual {p0, v0}, Landroidx/media3/common/d0$b;->f0(Ljava/lang/Integer;)Landroidx/media3/common/d0$b;

    :cond_10
    iget-object v0, p1, Landroidx/media3/common/d0;->r:Ljava/lang/Integer;

    if-eqz v0, :cond_11

    invoke-virtual {p0, v0}, Landroidx/media3/common/d0$b;->e0(Ljava/lang/Integer;)Landroidx/media3/common/d0$b;

    :cond_11
    iget-object v0, p1, Landroidx/media3/common/d0;->s:Ljava/lang/Integer;

    if-eqz v0, :cond_12

    invoke-virtual {p0, v0}, Landroidx/media3/common/d0$b;->d0(Ljava/lang/Integer;)Landroidx/media3/common/d0$b;

    :cond_12
    iget-object v0, p1, Landroidx/media3/common/d0;->t:Ljava/lang/Integer;

    if-eqz v0, :cond_13

    invoke-virtual {p0, v0}, Landroidx/media3/common/d0$b;->i0(Ljava/lang/Integer;)Landroidx/media3/common/d0$b;

    :cond_13
    iget-object v0, p1, Landroidx/media3/common/d0;->u:Ljava/lang/Integer;

    if-eqz v0, :cond_14

    invoke-virtual {p0, v0}, Landroidx/media3/common/d0$b;->h0(Ljava/lang/Integer;)Landroidx/media3/common/d0$b;

    :cond_14
    iget-object v0, p1, Landroidx/media3/common/d0;->v:Ljava/lang/Integer;

    if-eqz v0, :cond_15

    invoke-virtual {p0, v0}, Landroidx/media3/common/d0$b;->g0(Ljava/lang/Integer;)Landroidx/media3/common/d0$b;

    :cond_15
    iget-object v0, p1, Landroidx/media3/common/d0;->w:Ljava/lang/CharSequence;

    if-eqz v0, :cond_16

    invoke-virtual {p0, v0}, Landroidx/media3/common/d0$b;->p0(Ljava/lang/CharSequence;)Landroidx/media3/common/d0$b;

    :cond_16
    iget-object v0, p1, Landroidx/media3/common/d0;->x:Ljava/lang/CharSequence;

    if-eqz v0, :cond_17

    invoke-virtual {p0, v0}, Landroidx/media3/common/d0$b;->S(Ljava/lang/CharSequence;)Landroidx/media3/common/d0$b;

    :cond_17
    iget-object v0, p1, Landroidx/media3/common/d0;->y:Ljava/lang/CharSequence;

    if-eqz v0, :cond_18

    invoke-virtual {p0, v0}, Landroidx/media3/common/d0$b;->T(Ljava/lang/CharSequence;)Landroidx/media3/common/d0$b;

    :cond_18
    iget-object v0, p1, Landroidx/media3/common/d0;->z:Ljava/lang/Integer;

    if-eqz v0, :cond_19

    invoke-virtual {p0, v0}, Landroidx/media3/common/d0$b;->V(Ljava/lang/Integer;)Landroidx/media3/common/d0$b;

    :cond_19
    iget-object v0, p1, Landroidx/media3/common/d0;->A:Ljava/lang/Integer;

    if-eqz v0, :cond_1a

    invoke-virtual {p0, v0}, Landroidx/media3/common/d0$b;->m0(Ljava/lang/Integer;)Landroidx/media3/common/d0$b;

    :cond_1a
    iget-object v0, p1, Landroidx/media3/common/d0;->B:Ljava/lang/CharSequence;

    if-eqz v0, :cond_1b

    invoke-virtual {p0, v0}, Landroidx/media3/common/d0$b;->Z(Ljava/lang/CharSequence;)Landroidx/media3/common/d0$b;

    :cond_1b
    iget-object v0, p1, Landroidx/media3/common/d0;->C:Ljava/lang/CharSequence;

    if-eqz v0, :cond_1c

    invoke-virtual {p0, v0}, Landroidx/media3/common/d0$b;->R(Ljava/lang/CharSequence;)Landroidx/media3/common/d0$b;

    :cond_1c
    iget-object v0, p1, Landroidx/media3/common/d0;->D:Ljava/lang/CharSequence;

    if-eqz v0, :cond_1d

    invoke-virtual {p0, v0}, Landroidx/media3/common/d0$b;->j0(Ljava/lang/CharSequence;)Landroidx/media3/common/d0$b;

    :cond_1d
    iget-object v0, p1, Landroidx/media3/common/d0;->E:Ljava/lang/Integer;

    if-eqz v0, :cond_1e

    invoke-virtual {p0, v0}, Landroidx/media3/common/d0$b;->c0(Ljava/lang/Integer;)Landroidx/media3/common/d0$b;

    :cond_1e
    iget-object p1, p1, Landroidx/media3/common/d0;->F:Landroid/os/Bundle;

    if-eqz p1, :cond_1f

    invoke-virtual {p0, p1}, Landroidx/media3/common/d0$b;->X(Landroid/os/Bundle;)Landroidx/media3/common/d0$b;

    :cond_1f
    return-object p0
.end method

.method public K(Landroidx/media3/common/Metadata;)Landroidx/media3/common/d0$b;
    .locals 2

    const/4 v0, 0x0

    :goto_0
    invoke-virtual {p1}, Landroidx/media3/common/Metadata;->g()I

    move-result v1

    if-ge v0, v1, :cond_0

    invoke-virtual {p1, v0}, Landroidx/media3/common/Metadata;->e(I)Landroidx/media3/common/Metadata$Entry;

    move-result-object v1

    invoke-interface {v1, p0}, Landroidx/media3/common/Metadata$Entry;->k0(Landroidx/media3/common/d0$b;)V

    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :cond_0
    return-object p0
.end method

.method public L(Ljava/util/List;)Landroidx/media3/common/d0$b;
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Landroidx/media3/common/Metadata;",
            ">;)",
            "Landroidx/media3/common/d0$b;"
        }
    .end annotation

    const/4 v0, 0x0

    const/4 v1, 0x0

    :goto_0
    invoke-interface {p1}, Ljava/util/List;->size()I

    move-result v2

    if-ge v1, v2, :cond_1

    invoke-interface {p1, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Landroidx/media3/common/Metadata;

    const/4 v3, 0x0

    :goto_1
    invoke-virtual {v2}, Landroidx/media3/common/Metadata;->g()I

    move-result v4

    if-ge v3, v4, :cond_0

    invoke-virtual {v2, v3}, Landroidx/media3/common/Metadata;->e(I)Landroidx/media3/common/Metadata$Entry;

    move-result-object v4

    invoke-interface {v4, p0}, Landroidx/media3/common/Metadata$Entry;->k0(Landroidx/media3/common/d0$b;)V

    add-int/lit8 v3, v3, 0x1

    goto :goto_1

    :cond_0
    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_1
    return-object p0
.end method

.method public M(Ljava/lang/CharSequence;)Landroidx/media3/common/d0$b;
    .locals 0
    .param p1    # Ljava/lang/CharSequence;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    iput-object p1, p0, Landroidx/media3/common/d0$b;->d:Ljava/lang/CharSequence;

    return-object p0
.end method

.method public N(Ljava/lang/CharSequence;)Landroidx/media3/common/d0$b;
    .locals 0
    .param p1    # Ljava/lang/CharSequence;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    iput-object p1, p0, Landroidx/media3/common/d0$b;->c:Ljava/lang/CharSequence;

    return-object p0
.end method

.method public O(Ljava/lang/CharSequence;)Landroidx/media3/common/d0$b;
    .locals 0
    .param p1    # Ljava/lang/CharSequence;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    iput-object p1, p0, Landroidx/media3/common/d0$b;->b:Ljava/lang/CharSequence;

    return-object p0
.end method

.method public P([BLjava/lang/Integer;)Landroidx/media3/common/d0$b;
    .locals 0
    .param p1    # [B
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .param p2    # Ljava/lang/Integer;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    if-nez p1, :cond_0

    const/4 p1, 0x0

    goto :goto_0

    :cond_0
    invoke-virtual {p1}, [B->clone()Ljava/lang/Object;

    move-result-object p1

    check-cast p1, [B

    :goto_0
    iput-object p1, p0, Landroidx/media3/common/d0$b;->h:[B

    iput-object p2, p0, Landroidx/media3/common/d0$b;->i:Ljava/lang/Integer;

    return-object p0
.end method

.method public Q(Landroid/net/Uri;)Landroidx/media3/common/d0$b;
    .locals 0
    .param p1    # Landroid/net/Uri;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    iput-object p1, p0, Landroidx/media3/common/d0$b;->j:Landroid/net/Uri;

    return-object p0
.end method

.method public R(Ljava/lang/CharSequence;)Landroidx/media3/common/d0$b;
    .locals 0
    .param p1    # Ljava/lang/CharSequence;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    iput-object p1, p0, Landroidx/media3/common/d0$b;->B:Ljava/lang/CharSequence;

    return-object p0
.end method

.method public S(Ljava/lang/CharSequence;)Landroidx/media3/common/d0$b;
    .locals 0
    .param p1    # Ljava/lang/CharSequence;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    iput-object p1, p0, Landroidx/media3/common/d0$b;->w:Ljava/lang/CharSequence;

    return-object p0
.end method

.method public T(Ljava/lang/CharSequence;)Landroidx/media3/common/d0$b;
    .locals 0
    .param p1    # Ljava/lang/CharSequence;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    iput-object p1, p0, Landroidx/media3/common/d0$b;->x:Ljava/lang/CharSequence;

    return-object p0
.end method

.method public U(Ljava/lang/CharSequence;)Landroidx/media3/common/d0$b;
    .locals 0
    .param p1    # Ljava/lang/CharSequence;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    iput-object p1, p0, Landroidx/media3/common/d0$b;->g:Ljava/lang/CharSequence;

    return-object p0
.end method

.method public V(Ljava/lang/Integer;)Landroidx/media3/common/d0$b;
    .locals 0
    .param p1    # Ljava/lang/Integer;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    iput-object p1, p0, Landroidx/media3/common/d0$b;->y:Ljava/lang/Integer;

    return-object p0
.end method

.method public W(Ljava/lang/CharSequence;)Landroidx/media3/common/d0$b;
    .locals 0
    .param p1    # Ljava/lang/CharSequence;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    iput-object p1, p0, Landroidx/media3/common/d0$b;->e:Ljava/lang/CharSequence;

    return-object p0
.end method

.method public X(Landroid/os/Bundle;)Landroidx/media3/common/d0$b;
    .locals 0
    .param p1    # Landroid/os/Bundle;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    iput-object p1, p0, Landroidx/media3/common/d0$b;->E:Landroid/os/Bundle;

    return-object p0
.end method

.method public Y(Ljava/lang/Integer;)Landroidx/media3/common/d0$b;
    .locals 0
    .param p1    # Ljava/lang/Integer;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .annotation runtime Ljava/lang/Deprecated;
    .end annotation

    iput-object p1, p0, Landroidx/media3/common/d0$b;->m:Ljava/lang/Integer;

    return-object p0
.end method

.method public Z(Ljava/lang/CharSequence;)Landroidx/media3/common/d0$b;
    .locals 0
    .param p1    # Ljava/lang/CharSequence;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    iput-object p1, p0, Landroidx/media3/common/d0$b;->A:Ljava/lang/CharSequence;

    return-object p0
.end method

.method public a0(Ljava/lang/Boolean;)Landroidx/media3/common/d0$b;
    .locals 0
    .param p1    # Ljava/lang/Boolean;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    iput-object p1, p0, Landroidx/media3/common/d0$b;->n:Ljava/lang/Boolean;

    return-object p0
.end method

.method public b0(Ljava/lang/Boolean;)Landroidx/media3/common/d0$b;
    .locals 0
    .param p1    # Ljava/lang/Boolean;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    iput-object p1, p0, Landroidx/media3/common/d0$b;->o:Ljava/lang/Boolean;

    return-object p0
.end method

.method public c0(Ljava/lang/Integer;)Landroidx/media3/common/d0$b;
    .locals 0
    .param p1    # Ljava/lang/Integer;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    iput-object p1, p0, Landroidx/media3/common/d0$b;->D:Ljava/lang/Integer;

    return-object p0
.end method

.method public d0(Ljava/lang/Integer;)Landroidx/media3/common/d0$b;
    .locals 0
    .param p1    # Ljava/lang/Integer;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    iput-object p1, p0, Landroidx/media3/common/d0$b;->r:Ljava/lang/Integer;

    return-object p0
.end method

.method public e0(Ljava/lang/Integer;)Landroidx/media3/common/d0$b;
    .locals 0
    .param p1    # Ljava/lang/Integer;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    iput-object p1, p0, Landroidx/media3/common/d0$b;->q:Ljava/lang/Integer;

    return-object p0
.end method

.method public f0(Ljava/lang/Integer;)Landroidx/media3/common/d0$b;
    .locals 0
    .param p1    # Ljava/lang/Integer;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    iput-object p1, p0, Landroidx/media3/common/d0$b;->p:Ljava/lang/Integer;

    return-object p0
.end method

.method public g0(Ljava/lang/Integer;)Landroidx/media3/common/d0$b;
    .locals 0
    .param p1    # Ljava/lang/Integer;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    iput-object p1, p0, Landroidx/media3/common/d0$b;->u:Ljava/lang/Integer;

    return-object p0
.end method

.method public h0(Ljava/lang/Integer;)Landroidx/media3/common/d0$b;
    .locals 0
    .param p1    # Ljava/lang/Integer;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    iput-object p1, p0, Landroidx/media3/common/d0$b;->t:Ljava/lang/Integer;

    return-object p0
.end method

.method public i0(Ljava/lang/Integer;)Landroidx/media3/common/d0$b;
    .locals 0
    .param p1    # Ljava/lang/Integer;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    iput-object p1, p0, Landroidx/media3/common/d0$b;->s:Ljava/lang/Integer;

    return-object p0
.end method

.method public j0(Ljava/lang/CharSequence;)Landroidx/media3/common/d0$b;
    .locals 0
    .param p1    # Ljava/lang/CharSequence;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    iput-object p1, p0, Landroidx/media3/common/d0$b;->C:Ljava/lang/CharSequence;

    return-object p0
.end method

.method public k0(Ljava/lang/CharSequence;)Landroidx/media3/common/d0$b;
    .locals 0
    .param p1    # Ljava/lang/CharSequence;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    iput-object p1, p0, Landroidx/media3/common/d0$b;->f:Ljava/lang/CharSequence;

    return-object p0
.end method

.method public l0(Ljava/lang/CharSequence;)Landroidx/media3/common/d0$b;
    .locals 0
    .param p1    # Ljava/lang/CharSequence;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    iput-object p1, p0, Landroidx/media3/common/d0$b;->a:Ljava/lang/CharSequence;

    return-object p0
.end method

.method public m0(Ljava/lang/Integer;)Landroidx/media3/common/d0$b;
    .locals 0
    .param p1    # Ljava/lang/Integer;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    iput-object p1, p0, Landroidx/media3/common/d0$b;->z:Ljava/lang/Integer;

    return-object p0
.end method

.method public n0(Ljava/lang/Integer;)Landroidx/media3/common/d0$b;
    .locals 0
    .param p1    # Ljava/lang/Integer;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    iput-object p1, p0, Landroidx/media3/common/d0$b;->l:Ljava/lang/Integer;

    return-object p0
.end method

.method public o0(Ljava/lang/Integer;)Landroidx/media3/common/d0$b;
    .locals 0
    .param p1    # Ljava/lang/Integer;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    iput-object p1, p0, Landroidx/media3/common/d0$b;->k:Ljava/lang/Integer;

    return-object p0
.end method

.method public p0(Ljava/lang/CharSequence;)Landroidx/media3/common/d0$b;
    .locals 0
    .param p1    # Ljava/lang/CharSequence;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    iput-object p1, p0, Landroidx/media3/common/d0$b;->v:Ljava/lang/CharSequence;

    return-object p0
.end method
