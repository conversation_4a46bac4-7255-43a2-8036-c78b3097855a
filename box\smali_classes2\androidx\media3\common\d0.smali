.class public final Landroidx/media3/common/d0;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Landroidx/media3/common/d0$b;
    }
.end annotation


# static fields
.field public static final G:Landroidx/media3/common/d0;

.field public static final H:Ljava/lang/String;

.field public static final I:Ljava/lang/String;

.field public static final J:Ljava/lang/String;

.field public static final K:Ljava/lang/String;

.field public static final L:Ljava/lang/String;

.field public static final M:Ljava/lang/String;

.field public static final N:Ljava/lang/String;

.field public static final O:Ljava/lang/String;

.field public static final P:Ljava/lang/String;

.field public static final Q:Ljava/lang/String;

.field public static final R:Ljava/lang/String;

.field public static final S:Ljava/lang/String;

.field public static final T:Ljava/lang/String;

.field public static final U:Ljava/lang/String;

.field public static final V:Ljava/lang/String;

.field public static final W:Ljava/lang/String;

.field public static final X:Ljava/lang/String;

.field public static final Y:Ljava/lang/String;

.field public static final Z:Ljava/lang/String;

.field public static final a0:Ljava/lang/String;

.field public static final b0:Ljava/lang/String;

.field public static final c0:Ljava/lang/String;

.field public static final d0:Ljava/lang/String;

.field public static final e0:Ljava/lang/String;

.field public static final f0:Ljava/lang/String;

.field public static final g0:Ljava/lang/String;

.field public static final h0:Ljava/lang/String;

.field public static final i0:Ljava/lang/String;

.field public static final j0:Ljava/lang/String;

.field public static final k0:Ljava/lang/String;

.field public static final l0:Ljava/lang/String;

.field public static final m0:Ljava/lang/String;

.field public static final n0:Ljava/lang/String;

.field public static final o0:Landroidx/media3/common/i;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Landroidx/media3/common/i<",
            "Landroidx/media3/common/d0;",
            ">;"
        }
    .end annotation

    .annotation runtime Ljava/lang/Deprecated;
    .end annotation
.end field


# instance fields
.field public final A:Ljava/lang/Integer;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public final B:Ljava/lang/CharSequence;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public final C:Ljava/lang/CharSequence;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public final D:Ljava/lang/CharSequence;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public final E:Ljava/lang/Integer;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public final F:Landroid/os/Bundle;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public final a:Ljava/lang/CharSequence;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public final b:Ljava/lang/CharSequence;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public final c:Ljava/lang/CharSequence;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public final d:Ljava/lang/CharSequence;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public final e:Ljava/lang/CharSequence;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public final f:Ljava/lang/CharSequence;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public final g:Ljava/lang/CharSequence;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public final h:[B
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public final i:Ljava/lang/Integer;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public final j:Landroid/net/Uri;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public final k:Ljava/lang/Integer;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public final l:Ljava/lang/Integer;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public final m:Ljava/lang/Integer;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation

    .annotation runtime Ljava/lang/Deprecated;
    .end annotation
.end field

.field public final n:Ljava/lang/Boolean;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public final o:Ljava/lang/Boolean;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public final p:Ljava/lang/Integer;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation

    .annotation runtime Ljava/lang/Deprecated;
    .end annotation
.end field

.field public final q:Ljava/lang/Integer;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public final r:Ljava/lang/Integer;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public final s:Ljava/lang/Integer;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public final t:Ljava/lang/Integer;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public final u:Ljava/lang/Integer;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public final v:Ljava/lang/Integer;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public final w:Ljava/lang/CharSequence;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public final x:Ljava/lang/CharSequence;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public final y:Ljava/lang/CharSequence;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public final z:Ljava/lang/Integer;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 1

    new-instance v0, Landroidx/media3/common/d0$b;

    invoke-direct {v0}, Landroidx/media3/common/d0$b;-><init>()V

    invoke-virtual {v0}, Landroidx/media3/common/d0$b;->H()Landroidx/media3/common/d0;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/d0;->G:Landroidx/media3/common/d0;

    const/4 v0, 0x0

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/d0;->H:Ljava/lang/String;

    const/4 v0, 0x1

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/d0;->I:Ljava/lang/String;

    const/4 v0, 0x2

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/d0;->J:Ljava/lang/String;

    const/4 v0, 0x3

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/d0;->K:Ljava/lang/String;

    const/4 v0, 0x4

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/d0;->L:Ljava/lang/String;

    const/4 v0, 0x5

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/d0;->M:Ljava/lang/String;

    const/4 v0, 0x6

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/d0;->N:Ljava/lang/String;

    const/16 v0, 0x8

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/d0;->O:Ljava/lang/String;

    const/16 v0, 0x9

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/d0;->P:Ljava/lang/String;

    const/16 v0, 0xa

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/d0;->Q:Ljava/lang/String;

    const/16 v0, 0xb

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/d0;->R:Ljava/lang/String;

    const/16 v0, 0xc

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/d0;->S:Ljava/lang/String;

    const/16 v0, 0xd

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/d0;->T:Ljava/lang/String;

    const/16 v0, 0xe

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/d0;->U:Ljava/lang/String;

    const/16 v0, 0xf

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/d0;->V:Ljava/lang/String;

    const/16 v0, 0x10

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/d0;->W:Ljava/lang/String;

    const/16 v0, 0x11

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/d0;->X:Ljava/lang/String;

    const/16 v0, 0x12

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/d0;->Y:Ljava/lang/String;

    const/16 v0, 0x13

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/d0;->Z:Ljava/lang/String;

    const/16 v0, 0x14

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/d0;->a0:Ljava/lang/String;

    const/16 v0, 0x15

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/d0;->b0:Ljava/lang/String;

    const/16 v0, 0x16

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/d0;->c0:Ljava/lang/String;

    const/16 v0, 0x17

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/d0;->d0:Ljava/lang/String;

    const/16 v0, 0x18

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/d0;->e0:Ljava/lang/String;

    const/16 v0, 0x19

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/d0;->f0:Ljava/lang/String;

    const/16 v0, 0x1a

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/d0;->g0:Ljava/lang/String;

    const/16 v0, 0x1b

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/d0;->h0:Ljava/lang/String;

    const/16 v0, 0x1c

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/d0;->i0:Ljava/lang/String;

    const/16 v0, 0x1d

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/d0;->j0:Ljava/lang/String;

    const/16 v0, 0x1e

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/d0;->k0:Ljava/lang/String;

    const/16 v0, 0x1f

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/d0;->l0:Ljava/lang/String;

    const/16 v0, 0x20

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/d0;->m0:Ljava/lang/String;

    const/16 v0, 0x3e8

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/d0;->n0:Ljava/lang/String;

    new-instance v0, Landroidx/media3/common/b;

    invoke-direct {v0}, Landroidx/media3/common/b;-><init>()V

    sput-object v0, Landroidx/media3/common/d0;->o0:Landroidx/media3/common/i;

    return-void
.end method

.method public constructor <init>(Landroidx/media3/common/d0$b;)V
    .locals 6

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    invoke-static {p1}, Landroidx/media3/common/d0$b;->a(Landroidx/media3/common/d0$b;)Ljava/lang/Boolean;

    move-result-object v0

    invoke-static {p1}, Landroidx/media3/common/d0$b;->l(Landroidx/media3/common/d0$b;)Ljava/lang/Integer;

    move-result-object v1

    invoke-static {p1}, Landroidx/media3/common/d0$b;->w(Landroidx/media3/common/d0$b;)Ljava/lang/Integer;

    move-result-object v2

    const/4 v3, 0x0

    const/4 v4, -0x1

    if-eqz v0, :cond_3

    invoke-virtual {v0}, Ljava/lang/Boolean;->booleanValue()Z

    move-result v5

    if-nez v5, :cond_0

    invoke-static {v4}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    goto :goto_0

    :cond_0
    if-eqz v1, :cond_1

    invoke-virtual {v1}, Ljava/lang/Integer;->intValue()I

    move-result v5

    if-ne v5, v4, :cond_5

    :cond_1
    if-eqz v2, :cond_2

    invoke-virtual {v2}, Ljava/lang/Integer;->intValue()I

    move-result v1

    invoke-static {v1}, Landroidx/media3/common/d0;->b(I)I

    move-result v3

    :cond_2
    invoke-static {v3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    goto :goto_0

    :cond_3
    if-eqz v1, :cond_5

    invoke-virtual {v1}, Ljava/lang/Integer;->intValue()I

    move-result v0

    if-eq v0, v4, :cond_4

    const/4 v3, 0x1

    :cond_4
    invoke-static {v3}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/Boolean;->booleanValue()Z

    move-result v3

    if-eqz v3, :cond_5

    if-nez v2, :cond_5

    invoke-virtual {v1}, Ljava/lang/Integer;->intValue()I

    move-result v2

    invoke-static {v2}, Landroidx/media3/common/d0;->c(I)I

    move-result v2

    invoke-static {v2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v2

    :cond_5
    :goto_0
    invoke-static {p1}, Landroidx/media3/common/d0$b;->B(Landroidx/media3/common/d0$b;)Ljava/lang/CharSequence;

    move-result-object v3

    iput-object v3, p0, Landroidx/media3/common/d0;->a:Ljava/lang/CharSequence;

    invoke-static {p1}, Landroidx/media3/common/d0$b;->C(Landroidx/media3/common/d0$b;)Ljava/lang/CharSequence;

    move-result-object v3

    iput-object v3, p0, Landroidx/media3/common/d0;->b:Ljava/lang/CharSequence;

    invoke-static {p1}, Landroidx/media3/common/d0$b;->D(Landroidx/media3/common/d0$b;)Ljava/lang/CharSequence;

    move-result-object v3

    iput-object v3, p0, Landroidx/media3/common/d0;->c:Ljava/lang/CharSequence;

    invoke-static {p1}, Landroidx/media3/common/d0$b;->E(Landroidx/media3/common/d0$b;)Ljava/lang/CharSequence;

    move-result-object v3

    iput-object v3, p0, Landroidx/media3/common/d0;->d:Ljava/lang/CharSequence;

    invoke-static {p1}, Landroidx/media3/common/d0$b;->F(Landroidx/media3/common/d0$b;)Ljava/lang/CharSequence;

    move-result-object v3

    iput-object v3, p0, Landroidx/media3/common/d0;->e:Ljava/lang/CharSequence;

    invoke-static {p1}, Landroidx/media3/common/d0$b;->G(Landroidx/media3/common/d0$b;)Ljava/lang/CharSequence;

    move-result-object v3

    iput-object v3, p0, Landroidx/media3/common/d0;->f:Ljava/lang/CharSequence;

    invoke-static {p1}, Landroidx/media3/common/d0$b;->b(Landroidx/media3/common/d0$b;)Ljava/lang/CharSequence;

    move-result-object v3

    iput-object v3, p0, Landroidx/media3/common/d0;->g:Ljava/lang/CharSequence;

    invoke-static {p1}, Landroidx/media3/common/d0$b;->c(Landroidx/media3/common/d0$b;)Landroidx/media3/common/k0;

    invoke-static {p1}, Landroidx/media3/common/d0$b;->d(Landroidx/media3/common/d0$b;)Landroidx/media3/common/k0;

    invoke-static {p1}, Landroidx/media3/common/d0$b;->e(Landroidx/media3/common/d0$b;)[B

    move-result-object v3

    iput-object v3, p0, Landroidx/media3/common/d0;->h:[B

    invoke-static {p1}, Landroidx/media3/common/d0$b;->f(Landroidx/media3/common/d0$b;)Ljava/lang/Integer;

    move-result-object v3

    iput-object v3, p0, Landroidx/media3/common/d0;->i:Ljava/lang/Integer;

    invoke-static {p1}, Landroidx/media3/common/d0$b;->g(Landroidx/media3/common/d0$b;)Landroid/net/Uri;

    move-result-object v3

    iput-object v3, p0, Landroidx/media3/common/d0;->j:Landroid/net/Uri;

    invoke-static {p1}, Landroidx/media3/common/d0$b;->h(Landroidx/media3/common/d0$b;)Ljava/lang/Integer;

    move-result-object v3

    iput-object v3, p0, Landroidx/media3/common/d0;->k:Ljava/lang/Integer;

    invoke-static {p1}, Landroidx/media3/common/d0$b;->i(Landroidx/media3/common/d0$b;)Ljava/lang/Integer;

    move-result-object v3

    iput-object v3, p0, Landroidx/media3/common/d0;->l:Ljava/lang/Integer;

    iput-object v1, p0, Landroidx/media3/common/d0;->m:Ljava/lang/Integer;

    iput-object v0, p0, Landroidx/media3/common/d0;->n:Ljava/lang/Boolean;

    invoke-static {p1}, Landroidx/media3/common/d0$b;->j(Landroidx/media3/common/d0$b;)Ljava/lang/Boolean;

    move-result-object v0

    iput-object v0, p0, Landroidx/media3/common/d0;->o:Ljava/lang/Boolean;

    invoke-static {p1}, Landroidx/media3/common/d0$b;->k(Landroidx/media3/common/d0$b;)Ljava/lang/Integer;

    move-result-object v0

    iput-object v0, p0, Landroidx/media3/common/d0;->p:Ljava/lang/Integer;

    invoke-static {p1}, Landroidx/media3/common/d0$b;->k(Landroidx/media3/common/d0$b;)Ljava/lang/Integer;

    move-result-object v0

    iput-object v0, p0, Landroidx/media3/common/d0;->q:Ljava/lang/Integer;

    invoke-static {p1}, Landroidx/media3/common/d0$b;->m(Landroidx/media3/common/d0$b;)Ljava/lang/Integer;

    move-result-object v0

    iput-object v0, p0, Landroidx/media3/common/d0;->r:Ljava/lang/Integer;

    invoke-static {p1}, Landroidx/media3/common/d0$b;->n(Landroidx/media3/common/d0$b;)Ljava/lang/Integer;

    move-result-object v0

    iput-object v0, p0, Landroidx/media3/common/d0;->s:Ljava/lang/Integer;

    invoke-static {p1}, Landroidx/media3/common/d0$b;->o(Landroidx/media3/common/d0$b;)Ljava/lang/Integer;

    move-result-object v0

    iput-object v0, p0, Landroidx/media3/common/d0;->t:Ljava/lang/Integer;

    invoke-static {p1}, Landroidx/media3/common/d0$b;->p(Landroidx/media3/common/d0$b;)Ljava/lang/Integer;

    move-result-object v0

    iput-object v0, p0, Landroidx/media3/common/d0;->u:Ljava/lang/Integer;

    invoke-static {p1}, Landroidx/media3/common/d0$b;->q(Landroidx/media3/common/d0$b;)Ljava/lang/Integer;

    move-result-object v0

    iput-object v0, p0, Landroidx/media3/common/d0;->v:Ljava/lang/Integer;

    invoke-static {p1}, Landroidx/media3/common/d0$b;->r(Landroidx/media3/common/d0$b;)Ljava/lang/CharSequence;

    move-result-object v0

    iput-object v0, p0, Landroidx/media3/common/d0;->w:Ljava/lang/CharSequence;

    invoke-static {p1}, Landroidx/media3/common/d0$b;->s(Landroidx/media3/common/d0$b;)Ljava/lang/CharSequence;

    move-result-object v0

    iput-object v0, p0, Landroidx/media3/common/d0;->x:Ljava/lang/CharSequence;

    invoke-static {p1}, Landroidx/media3/common/d0$b;->t(Landroidx/media3/common/d0$b;)Ljava/lang/CharSequence;

    move-result-object v0

    iput-object v0, p0, Landroidx/media3/common/d0;->y:Ljava/lang/CharSequence;

    invoke-static {p1}, Landroidx/media3/common/d0$b;->u(Landroidx/media3/common/d0$b;)Ljava/lang/Integer;

    move-result-object v0

    iput-object v0, p0, Landroidx/media3/common/d0;->z:Ljava/lang/Integer;

    invoke-static {p1}, Landroidx/media3/common/d0$b;->v(Landroidx/media3/common/d0$b;)Ljava/lang/Integer;

    move-result-object v0

    iput-object v0, p0, Landroidx/media3/common/d0;->A:Ljava/lang/Integer;

    invoke-static {p1}, Landroidx/media3/common/d0$b;->x(Landroidx/media3/common/d0$b;)Ljava/lang/CharSequence;

    move-result-object v0

    iput-object v0, p0, Landroidx/media3/common/d0;->B:Ljava/lang/CharSequence;

    invoke-static {p1}, Landroidx/media3/common/d0$b;->y(Landroidx/media3/common/d0$b;)Ljava/lang/CharSequence;

    move-result-object v0

    iput-object v0, p0, Landroidx/media3/common/d0;->C:Ljava/lang/CharSequence;

    invoke-static {p1}, Landroidx/media3/common/d0$b;->z(Landroidx/media3/common/d0$b;)Ljava/lang/CharSequence;

    move-result-object v0

    iput-object v0, p0, Landroidx/media3/common/d0;->D:Ljava/lang/CharSequence;

    iput-object v2, p0, Landroidx/media3/common/d0;->E:Ljava/lang/Integer;

    invoke-static {p1}, Landroidx/media3/common/d0$b;->A(Landroidx/media3/common/d0$b;)Landroid/os/Bundle;

    move-result-object p1

    iput-object p1, p0, Landroidx/media3/common/d0;->F:Landroid/os/Bundle;

    return-void
.end method

.method public synthetic constructor <init>(Landroidx/media3/common/d0$b;Landroidx/media3/common/d0$a;)V
    .locals 0

    invoke-direct {p0, p1}, Landroidx/media3/common/d0;-><init>(Landroidx/media3/common/d0$b;)V

    return-void
.end method

.method public static b(I)I
    .locals 0

    packed-switch p0, :pswitch_data_0

    :pswitch_0
    const/4 p0, 0x0

    return p0

    :pswitch_1
    const/4 p0, 0x6

    return p0

    :pswitch_2
    const/4 p0, 0x5

    return p0

    :pswitch_3
    const/4 p0, 0x4

    return p0

    :pswitch_4
    const/4 p0, 0x3

    return p0

    :pswitch_5
    const/4 p0, 0x2

    return p0

    :pswitch_6
    const/4 p0, 0x1

    return p0

    nop

    :pswitch_data_0
    .packed-switch 0x1
        :pswitch_6
        :pswitch_6
        :pswitch_6
        :pswitch_6
        :pswitch_6
        :pswitch_6
        :pswitch_6
        :pswitch_6
        :pswitch_6
        :pswitch_6
        :pswitch_6
        :pswitch_6
        :pswitch_6
        :pswitch_6
        :pswitch_6
        :pswitch_6
        :pswitch_6
        :pswitch_6
        :pswitch_6
        :pswitch_0
        :pswitch_5
        :pswitch_4
        :pswitch_3
        :pswitch_2
        :pswitch_1
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_6
        :pswitch_6
        :pswitch_6
        :pswitch_6
        :pswitch_6
    .end packed-switch
.end method

.method public static c(I)I
    .locals 0

    packed-switch p0, :pswitch_data_0

    const/16 p0, 0x14

    return p0

    :pswitch_0
    const/16 p0, 0x19

    return p0

    :pswitch_1
    const/16 p0, 0x18

    return p0

    :pswitch_2
    const/16 p0, 0x17

    return p0

    :pswitch_3
    const/16 p0, 0x16

    return p0

    :pswitch_4
    const/16 p0, 0x15

    return p0

    :pswitch_5
    const/4 p0, 0x0

    return p0

    nop

    :pswitch_data_0
    .packed-switch 0x1
        :pswitch_5
        :pswitch_4
        :pswitch_3
        :pswitch_2
        :pswitch_1
        :pswitch_0
    .end packed-switch
.end method


# virtual methods
.method public a()Landroidx/media3/common/d0$b;
    .locals 2

    new-instance v0, Landroidx/media3/common/d0$b;

    const/4 v1, 0x0

    invoke-direct {v0, p0, v1}, Landroidx/media3/common/d0$b;-><init>(Landroidx/media3/common/d0;Landroidx/media3/common/d0$a;)V

    return-object v0
.end method

.method public equals(Ljava/lang/Object;)Z
    .locals 4
    .param p1    # Ljava/lang/Object;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    const/4 v0, 0x1

    if-ne p0, p1, :cond_0

    return v0

    :cond_0
    const/4 v1, 0x0

    if-eqz p1, :cond_5

    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v2

    const-class v3, Landroidx/media3/common/d0;

    if-eq v3, v2, :cond_1

    goto/16 :goto_3

    :cond_1
    check-cast p1, Landroidx/media3/common/d0;

    iget-object v2, p0, Landroidx/media3/common/d0;->a:Ljava/lang/CharSequence;

    iget-object v3, p1, Landroidx/media3/common/d0;->a:Ljava/lang/CharSequence;

    invoke-static {v2, v3}, Le2/u0;->c(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_4

    iget-object v2, p0, Landroidx/media3/common/d0;->b:Ljava/lang/CharSequence;

    iget-object v3, p1, Landroidx/media3/common/d0;->b:Ljava/lang/CharSequence;

    invoke-static {v2, v3}, Le2/u0;->c(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_4

    iget-object v2, p0, Landroidx/media3/common/d0;->c:Ljava/lang/CharSequence;

    iget-object v3, p1, Landroidx/media3/common/d0;->c:Ljava/lang/CharSequence;

    invoke-static {v2, v3}, Le2/u0;->c(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_4

    iget-object v2, p0, Landroidx/media3/common/d0;->d:Ljava/lang/CharSequence;

    iget-object v3, p1, Landroidx/media3/common/d0;->d:Ljava/lang/CharSequence;

    invoke-static {v2, v3}, Le2/u0;->c(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_4

    iget-object v2, p0, Landroidx/media3/common/d0;->e:Ljava/lang/CharSequence;

    iget-object v3, p1, Landroidx/media3/common/d0;->e:Ljava/lang/CharSequence;

    invoke-static {v2, v3}, Le2/u0;->c(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_4

    iget-object v2, p0, Landroidx/media3/common/d0;->f:Ljava/lang/CharSequence;

    iget-object v3, p1, Landroidx/media3/common/d0;->f:Ljava/lang/CharSequence;

    invoke-static {v2, v3}, Le2/u0;->c(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_4

    iget-object v2, p0, Landroidx/media3/common/d0;->g:Ljava/lang/CharSequence;

    iget-object v3, p1, Landroidx/media3/common/d0;->g:Ljava/lang/CharSequence;

    invoke-static {v2, v3}, Le2/u0;->c(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_4

    const/4 v2, 0x0

    invoke-static {v2, v2}, Le2/u0;->c(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v3

    if-eqz v3, :cond_4

    invoke-static {v2, v2}, Le2/u0;->c(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_4

    iget-object v2, p0, Landroidx/media3/common/d0;->h:[B

    iget-object v3, p1, Landroidx/media3/common/d0;->h:[B

    invoke-static {v2, v3}, Ljava/util/Arrays;->equals([B[B)Z

    move-result v2

    if-eqz v2, :cond_4

    iget-object v2, p0, Landroidx/media3/common/d0;->i:Ljava/lang/Integer;

    iget-object v3, p1, Landroidx/media3/common/d0;->i:Ljava/lang/Integer;

    invoke-static {v2, v3}, Le2/u0;->c(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_4

    iget-object v2, p0, Landroidx/media3/common/d0;->j:Landroid/net/Uri;

    iget-object v3, p1, Landroidx/media3/common/d0;->j:Landroid/net/Uri;

    invoke-static {v2, v3}, Le2/u0;->c(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_4

    iget-object v2, p0, Landroidx/media3/common/d0;->k:Ljava/lang/Integer;

    iget-object v3, p1, Landroidx/media3/common/d0;->k:Ljava/lang/Integer;

    invoke-static {v2, v3}, Le2/u0;->c(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_4

    iget-object v2, p0, Landroidx/media3/common/d0;->l:Ljava/lang/Integer;

    iget-object v3, p1, Landroidx/media3/common/d0;->l:Ljava/lang/Integer;

    invoke-static {v2, v3}, Le2/u0;->c(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_4

    iget-object v2, p0, Landroidx/media3/common/d0;->m:Ljava/lang/Integer;

    iget-object v3, p1, Landroidx/media3/common/d0;->m:Ljava/lang/Integer;

    invoke-static {v2, v3}, Le2/u0;->c(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_4

    iget-object v2, p0, Landroidx/media3/common/d0;->n:Ljava/lang/Boolean;

    iget-object v3, p1, Landroidx/media3/common/d0;->n:Ljava/lang/Boolean;

    invoke-static {v2, v3}, Le2/u0;->c(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_4

    iget-object v2, p0, Landroidx/media3/common/d0;->o:Ljava/lang/Boolean;

    iget-object v3, p1, Landroidx/media3/common/d0;->o:Ljava/lang/Boolean;

    invoke-static {v2, v3}, Le2/u0;->c(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_4

    iget-object v2, p0, Landroidx/media3/common/d0;->q:Ljava/lang/Integer;

    iget-object v3, p1, Landroidx/media3/common/d0;->q:Ljava/lang/Integer;

    invoke-static {v2, v3}, Le2/u0;->c(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_4

    iget-object v2, p0, Landroidx/media3/common/d0;->r:Ljava/lang/Integer;

    iget-object v3, p1, Landroidx/media3/common/d0;->r:Ljava/lang/Integer;

    invoke-static {v2, v3}, Le2/u0;->c(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_4

    iget-object v2, p0, Landroidx/media3/common/d0;->s:Ljava/lang/Integer;

    iget-object v3, p1, Landroidx/media3/common/d0;->s:Ljava/lang/Integer;

    invoke-static {v2, v3}, Le2/u0;->c(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_4

    iget-object v2, p0, Landroidx/media3/common/d0;->t:Ljava/lang/Integer;

    iget-object v3, p1, Landroidx/media3/common/d0;->t:Ljava/lang/Integer;

    invoke-static {v2, v3}, Le2/u0;->c(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_4

    iget-object v2, p0, Landroidx/media3/common/d0;->u:Ljava/lang/Integer;

    iget-object v3, p1, Landroidx/media3/common/d0;->u:Ljava/lang/Integer;

    invoke-static {v2, v3}, Le2/u0;->c(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_4

    iget-object v2, p0, Landroidx/media3/common/d0;->v:Ljava/lang/Integer;

    iget-object v3, p1, Landroidx/media3/common/d0;->v:Ljava/lang/Integer;

    invoke-static {v2, v3}, Le2/u0;->c(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_4

    iget-object v2, p0, Landroidx/media3/common/d0;->w:Ljava/lang/CharSequence;

    iget-object v3, p1, Landroidx/media3/common/d0;->w:Ljava/lang/CharSequence;

    invoke-static {v2, v3}, Le2/u0;->c(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_4

    iget-object v2, p0, Landroidx/media3/common/d0;->x:Ljava/lang/CharSequence;

    iget-object v3, p1, Landroidx/media3/common/d0;->x:Ljava/lang/CharSequence;

    invoke-static {v2, v3}, Le2/u0;->c(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_4

    iget-object v2, p0, Landroidx/media3/common/d0;->y:Ljava/lang/CharSequence;

    iget-object v3, p1, Landroidx/media3/common/d0;->y:Ljava/lang/CharSequence;

    invoke-static {v2, v3}, Le2/u0;->c(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_4

    iget-object v2, p0, Landroidx/media3/common/d0;->z:Ljava/lang/Integer;

    iget-object v3, p1, Landroidx/media3/common/d0;->z:Ljava/lang/Integer;

    invoke-static {v2, v3}, Le2/u0;->c(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_4

    iget-object v2, p0, Landroidx/media3/common/d0;->A:Ljava/lang/Integer;

    iget-object v3, p1, Landroidx/media3/common/d0;->A:Ljava/lang/Integer;

    invoke-static {v2, v3}, Le2/u0;->c(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_4

    iget-object v2, p0, Landroidx/media3/common/d0;->B:Ljava/lang/CharSequence;

    iget-object v3, p1, Landroidx/media3/common/d0;->B:Ljava/lang/CharSequence;

    invoke-static {v2, v3}, Le2/u0;->c(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_4

    iget-object v2, p0, Landroidx/media3/common/d0;->C:Ljava/lang/CharSequence;

    iget-object v3, p1, Landroidx/media3/common/d0;->C:Ljava/lang/CharSequence;

    invoke-static {v2, v3}, Le2/u0;->c(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_4

    iget-object v2, p0, Landroidx/media3/common/d0;->D:Ljava/lang/CharSequence;

    iget-object v3, p1, Landroidx/media3/common/d0;->D:Ljava/lang/CharSequence;

    invoke-static {v2, v3}, Le2/u0;->c(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_4

    iget-object v2, p0, Landroidx/media3/common/d0;->E:Ljava/lang/Integer;

    iget-object v3, p1, Landroidx/media3/common/d0;->E:Ljava/lang/Integer;

    invoke-static {v2, v3}, Le2/u0;->c(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_4

    iget-object v2, p0, Landroidx/media3/common/d0;->F:Landroid/os/Bundle;

    if-nez v2, :cond_2

    const/4 v2, 0x1

    goto :goto_0

    :cond_2
    const/4 v2, 0x0

    :goto_0
    iget-object p1, p1, Landroidx/media3/common/d0;->F:Landroid/os/Bundle;

    if-nez p1, :cond_3

    const/4 p1, 0x1

    goto :goto_1

    :cond_3
    const/4 p1, 0x0

    :goto_1
    if-ne v2, p1, :cond_4

    goto :goto_2

    :cond_4
    const/4 v0, 0x0

    :goto_2
    return v0

    :cond_5
    :goto_3
    return v1
.end method

.method public hashCode()I
    .locals 5

    const/16 v0, 0x21

    new-array v0, v0, [Ljava/lang/Object;

    iget-object v1, p0, Landroidx/media3/common/d0;->a:Ljava/lang/CharSequence;

    const/4 v2, 0x0

    aput-object v1, v0, v2

    iget-object v1, p0, Landroidx/media3/common/d0;->b:Ljava/lang/CharSequence;

    const/4 v3, 0x1

    aput-object v1, v0, v3

    const/4 v1, 0x2

    iget-object v4, p0, Landroidx/media3/common/d0;->c:Ljava/lang/CharSequence;

    aput-object v4, v0, v1

    const/4 v1, 0x3

    iget-object v4, p0, Landroidx/media3/common/d0;->d:Ljava/lang/CharSequence;

    aput-object v4, v0, v1

    const/4 v1, 0x4

    iget-object v4, p0, Landroidx/media3/common/d0;->e:Ljava/lang/CharSequence;

    aput-object v4, v0, v1

    const/4 v1, 0x5

    iget-object v4, p0, Landroidx/media3/common/d0;->f:Ljava/lang/CharSequence;

    aput-object v4, v0, v1

    const/4 v1, 0x6

    iget-object v4, p0, Landroidx/media3/common/d0;->g:Ljava/lang/CharSequence;

    aput-object v4, v0, v1

    const/4 v1, 0x7

    const/4 v4, 0x0

    aput-object v4, v0, v1

    const/16 v1, 0x8

    aput-object v4, v0, v1

    iget-object v1, p0, Landroidx/media3/common/d0;->h:[B

    invoke-static {v1}, Ljava/util/Arrays;->hashCode([B)I

    move-result v1

    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    const/16 v4, 0x9

    aput-object v1, v0, v4

    const/16 v1, 0xa

    iget-object v4, p0, Landroidx/media3/common/d0;->i:Ljava/lang/Integer;

    aput-object v4, v0, v1

    const/16 v1, 0xb

    iget-object v4, p0, Landroidx/media3/common/d0;->j:Landroid/net/Uri;

    aput-object v4, v0, v1

    const/16 v1, 0xc

    iget-object v4, p0, Landroidx/media3/common/d0;->k:Ljava/lang/Integer;

    aput-object v4, v0, v1

    const/16 v1, 0xd

    iget-object v4, p0, Landroidx/media3/common/d0;->l:Ljava/lang/Integer;

    aput-object v4, v0, v1

    const/16 v1, 0xe

    iget-object v4, p0, Landroidx/media3/common/d0;->m:Ljava/lang/Integer;

    aput-object v4, v0, v1

    const/16 v1, 0xf

    iget-object v4, p0, Landroidx/media3/common/d0;->n:Ljava/lang/Boolean;

    aput-object v4, v0, v1

    const/16 v1, 0x10

    iget-object v4, p0, Landroidx/media3/common/d0;->o:Ljava/lang/Boolean;

    aput-object v4, v0, v1

    const/16 v1, 0x11

    iget-object v4, p0, Landroidx/media3/common/d0;->q:Ljava/lang/Integer;

    aput-object v4, v0, v1

    const/16 v1, 0x12

    iget-object v4, p0, Landroidx/media3/common/d0;->r:Ljava/lang/Integer;

    aput-object v4, v0, v1

    const/16 v1, 0x13

    iget-object v4, p0, Landroidx/media3/common/d0;->s:Ljava/lang/Integer;

    aput-object v4, v0, v1

    const/16 v1, 0x14

    iget-object v4, p0, Landroidx/media3/common/d0;->t:Ljava/lang/Integer;

    aput-object v4, v0, v1

    const/16 v1, 0x15

    iget-object v4, p0, Landroidx/media3/common/d0;->u:Ljava/lang/Integer;

    aput-object v4, v0, v1

    const/16 v1, 0x16

    iget-object v4, p0, Landroidx/media3/common/d0;->v:Ljava/lang/Integer;

    aput-object v4, v0, v1

    const/16 v1, 0x17

    iget-object v4, p0, Landroidx/media3/common/d0;->w:Ljava/lang/CharSequence;

    aput-object v4, v0, v1

    const/16 v1, 0x18

    iget-object v4, p0, Landroidx/media3/common/d0;->x:Ljava/lang/CharSequence;

    aput-object v4, v0, v1

    const/16 v1, 0x19

    iget-object v4, p0, Landroidx/media3/common/d0;->y:Ljava/lang/CharSequence;

    aput-object v4, v0, v1

    const/16 v1, 0x1a

    iget-object v4, p0, Landroidx/media3/common/d0;->z:Ljava/lang/Integer;

    aput-object v4, v0, v1

    const/16 v1, 0x1b

    iget-object v4, p0, Landroidx/media3/common/d0;->A:Ljava/lang/Integer;

    aput-object v4, v0, v1

    const/16 v1, 0x1c

    iget-object v4, p0, Landroidx/media3/common/d0;->B:Ljava/lang/CharSequence;

    aput-object v4, v0, v1

    const/16 v1, 0x1d

    iget-object v4, p0, Landroidx/media3/common/d0;->C:Ljava/lang/CharSequence;

    aput-object v4, v0, v1

    const/16 v1, 0x1e

    iget-object v4, p0, Landroidx/media3/common/d0;->D:Ljava/lang/CharSequence;

    aput-object v4, v0, v1

    const/16 v1, 0x1f

    iget-object v4, p0, Landroidx/media3/common/d0;->E:Ljava/lang/Integer;

    aput-object v4, v0, v1

    iget-object v1, p0, Landroidx/media3/common/d0;->F:Landroid/os/Bundle;

    if-nez v1, :cond_0

    const/4 v2, 0x1

    :cond_0
    invoke-static {v2}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v1

    const/16 v2, 0x20

    aput-object v1, v0, v2

    invoke-static {v0}, Lcom/google/common/base/j;->b([Ljava/lang/Object;)I

    move-result v0

    return v0
.end method
