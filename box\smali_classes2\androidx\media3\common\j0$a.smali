.class public interface abstract Landroidx/media3/common/j0$a;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/media3/common/j0;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "a"
.end annotation


# virtual methods
.method public abstract a(Landroid/content/Context;Landroidx/media3/common/k;Landroidx/media3/common/k;Landroidx/media3/common/n;Landroidx/media3/common/s0$a;Ljava/util/concurrent/Executor;Ljava/util/List;J)Landroidx/media3/common/j0;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/content/Context;",
            "Landroidx/media3/common/k;",
            "Landroidx/media3/common/k;",
            "Landroidx/media3/common/n;",
            "Landroidx/media3/common/s0$a;",
            "Ljava/util/concurrent/Executor;",
            "Ljava/util/List<",
            "Landroidx/media3/common/p;",
            ">;J)",
            "Landroidx/media3/common/j0;"
        }
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/common/VideoFrameProcessingException;
        }
    .end annotation
.end method
