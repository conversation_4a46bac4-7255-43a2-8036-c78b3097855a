.class public final synthetic Landroidx/media3/common/v;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/google/common/base/f;


# direct methods
.method public synthetic constructor <init>()V
    .locals 0

    invoke-direct {p0}, <PERSON>java/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final apply(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    check-cast p1, Landroidx/media3/common/a0;

    invoke-virtual {p1}, Landroidx/media3/common/a0;->b()Landroid/os/Bundle;

    move-result-object p1

    return-object p1
.end method
