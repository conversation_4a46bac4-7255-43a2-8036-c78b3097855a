.class public final Landroidx/media3/common/y;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Landroidx/media3/common/y$b;
    }
.end annotation


# static fields
.field public static final K:Landroidx/media3/common/y;

.field public static final L:Ljava/lang/String;

.field public static final M:Ljava/lang/String;

.field public static final N:Ljava/lang/String;

.field public static final O:Ljava/lang/String;

.field public static final P:Ljava/lang/String;

.field public static final Q:Ljava/lang/String;

.field public static final R:Ljava/lang/String;

.field public static final S:Ljava/lang/String;

.field public static final T:Ljava/lang/String;

.field public static final U:Ljava/lang/String;

.field public static final V:Ljava/lang/String;

.field public static final W:Ljava/lang/String;

.field public static final X:Ljava/lang/String;

.field public static final Y:Ljava/lang/String;

.field public static final Z:Ljava/lang/String;

.field public static final a0:Ljava/lang/String;

.field public static final b0:Ljava/lang/String;

.field public static final c0:Ljava/lang/String;

.field public static final d0:Ljava/lang/String;

.field public static final e0:Ljava/lang/String;

.field public static final f0:Ljava/lang/String;

.field public static final g0:Ljava/lang/String;

.field public static final h0:Ljava/lang/String;

.field public static final i0:Ljava/lang/String;

.field public static final j0:Ljava/lang/String;

.field public static final k0:Ljava/lang/String;

.field public static final l0:Ljava/lang/String;

.field public static final m0:Ljava/lang/String;

.field public static final n0:Ljava/lang/String;

.field public static final o0:Ljava/lang/String;

.field public static final p0:Ljava/lang/String;

.field public static final q0:Ljava/lang/String;

.field public static final r0:Ljava/lang/String;

.field public static final s0:Landroidx/media3/common/i;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Landroidx/media3/common/i<",
            "Landroidx/media3/common/y;",
            ">;"
        }
    .end annotation

    .annotation runtime Ljava/lang/Deprecated;
    .end annotation
.end field


# instance fields
.field public final A:I

.field public final B:I

.field public final C:I

.field public final D:I

.field public final E:I

.field public final F:I

.field public final G:I

.field public final H:I

.field public final I:I

.field public J:I

.field public final a:Ljava/lang/String;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public final b:Ljava/lang/String;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public final c:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Landroidx/media3/common/a0;",
            ">;"
        }
    .end annotation
.end field

.field public final d:Ljava/lang/String;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public final e:I

.field public final f:I

.field public final g:I

.field public final h:I

.field public final i:I

.field public final j:Ljava/lang/String;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public final k:Landroidx/media3/common/Metadata;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public final l:Ljava/lang/String;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public final m:Ljava/lang/String;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public final n:I

.field public final o:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "[B>;"
        }
    .end annotation
.end field

.field public final p:Landroidx/media3/common/DrmInitData;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public final q:J

.field public final r:I

.field public final s:I

.field public final t:F

.field public final u:I

.field public final v:F

.field public final w:[B
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public final x:I

.field public final y:Landroidx/media3/common/k;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public final z:I


# direct methods
.method static constructor <clinit>()V
    .locals 1

    new-instance v0, Landroidx/media3/common/y$b;

    invoke-direct {v0}, Landroidx/media3/common/y$b;-><init>()V

    invoke-virtual {v0}, Landroidx/media3/common/y$b;->I()Landroidx/media3/common/y;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/y;->K:Landroidx/media3/common/y;

    const/4 v0, 0x0

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/y;->L:Ljava/lang/String;

    const/4 v0, 0x1

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/y;->M:Ljava/lang/String;

    const/4 v0, 0x2

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/y;->N:Ljava/lang/String;

    const/4 v0, 0x3

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/y;->O:Ljava/lang/String;

    const/4 v0, 0x4

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/y;->P:Ljava/lang/String;

    const/4 v0, 0x5

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/y;->Q:Ljava/lang/String;

    const/4 v0, 0x6

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/y;->R:Ljava/lang/String;

    const/4 v0, 0x7

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/y;->S:Ljava/lang/String;

    const/16 v0, 0x8

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/y;->T:Ljava/lang/String;

    const/16 v0, 0x9

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/y;->U:Ljava/lang/String;

    const/16 v0, 0xa

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/y;->V:Ljava/lang/String;

    const/16 v0, 0xb

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/y;->W:Ljava/lang/String;

    const/16 v0, 0xc

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/y;->X:Ljava/lang/String;

    const/16 v0, 0xd

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/y;->Y:Ljava/lang/String;

    const/16 v0, 0xe

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/y;->Z:Ljava/lang/String;

    const/16 v0, 0xf

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/y;->a0:Ljava/lang/String;

    const/16 v0, 0x10

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/y;->b0:Ljava/lang/String;

    const/16 v0, 0x11

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/y;->c0:Ljava/lang/String;

    const/16 v0, 0x12

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/y;->d0:Ljava/lang/String;

    const/16 v0, 0x13

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/y;->e0:Ljava/lang/String;

    const/16 v0, 0x14

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/y;->f0:Ljava/lang/String;

    const/16 v0, 0x15

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/y;->g0:Ljava/lang/String;

    const/16 v0, 0x16

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/y;->h0:Ljava/lang/String;

    const/16 v0, 0x17

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/y;->i0:Ljava/lang/String;

    const/16 v0, 0x18

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/y;->j0:Ljava/lang/String;

    const/16 v0, 0x19

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/y;->k0:Ljava/lang/String;

    const/16 v0, 0x1a

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/y;->l0:Ljava/lang/String;

    const/16 v0, 0x1b

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/y;->m0:Ljava/lang/String;

    const/16 v0, 0x1c

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/y;->n0:Ljava/lang/String;

    const/16 v0, 0x1d

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/y;->o0:Ljava/lang/String;

    const/16 v0, 0x1e

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/y;->p0:Ljava/lang/String;

    const/16 v0, 0x1f

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/y;->q0:Ljava/lang/String;

    const/16 v0, 0x20

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/y;->r0:Ljava/lang/String;

    new-instance v0, Landroidx/media3/common/b;

    invoke-direct {v0}, Landroidx/media3/common/b;-><init>()V

    sput-object v0, Landroidx/media3/common/y;->s0:Landroidx/media3/common/i;

    return-void
.end method

.method public constructor <init>(Landroidx/media3/common/y$b;)V
    .locals 7

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    invoke-static {p1}, Landroidx/media3/common/y$b;->a(Landroidx/media3/common/y$b;)Ljava/lang/String;

    move-result-object v0

    iput-object v0, p0, Landroidx/media3/common/y;->a:Ljava/lang/String;

    invoke-static {p1}, Landroidx/media3/common/y$b;->l(Landroidx/media3/common/y$b;)Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Le2/u0;->U0(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    iput-object v0, p0, Landroidx/media3/common/y;->d:Ljava/lang/String;

    invoke-static {p1}, Landroidx/media3/common/y$b;->w(Landroidx/media3/common/y$b;)Ljava/util/List;

    move-result-object v1

    invoke-interface {v1}, Ljava/util/List;->isEmpty()Z

    move-result v1

    const/4 v2, 0x1

    const/4 v3, 0x0

    if-eqz v1, :cond_0

    invoke-static {p1}, Landroidx/media3/common/y$b;->C(Landroidx/media3/common/y$b;)Ljava/lang/String;

    move-result-object v1

    if-eqz v1, :cond_0

    new-instance v1, Landroidx/media3/common/a0;

    invoke-static {p1}, Landroidx/media3/common/y$b;->C(Landroidx/media3/common/y$b;)Ljava/lang/String;

    move-result-object v4

    invoke-direct {v1, v0, v4}, Landroidx/media3/common/a0;-><init>(Ljava/lang/String;Ljava/lang/String;)V

    invoke-static {v1}, Lcom/google/common/collect/ImmutableList;->of(Ljava/lang/Object;)Lcom/google/common/collect/ImmutableList;

    move-result-object v0

    iput-object v0, p0, Landroidx/media3/common/y;->c:Ljava/util/List;

    invoke-static {p1}, Landroidx/media3/common/y$b;->C(Landroidx/media3/common/y$b;)Ljava/lang/String;

    move-result-object v0

    iput-object v0, p0, Landroidx/media3/common/y;->b:Ljava/lang/String;

    goto :goto_1

    :cond_0
    invoke-static {p1}, Landroidx/media3/common/y$b;->w(Landroidx/media3/common/y$b;)Ljava/util/List;

    move-result-object v1

    invoke-interface {v1}, Ljava/util/List;->isEmpty()Z

    move-result v1

    if-nez v1, :cond_1

    invoke-static {p1}, Landroidx/media3/common/y$b;->C(Landroidx/media3/common/y$b;)Ljava/lang/String;

    move-result-object v1

    if-nez v1, :cond_1

    invoke-static {p1}, Landroidx/media3/common/y$b;->w(Landroidx/media3/common/y$b;)Ljava/util/List;

    move-result-object v1

    iput-object v1, p0, Landroidx/media3/common/y;->c:Ljava/util/List;

    invoke-static {p1}, Landroidx/media3/common/y$b;->w(Landroidx/media3/common/y$b;)Ljava/util/List;

    move-result-object v1

    invoke-static {v1, v0}, Landroidx/media3/common/y;->f(Ljava/util/List;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    iput-object v0, p0, Landroidx/media3/common/y;->b:Ljava/lang/String;

    goto :goto_1

    :cond_1
    invoke-static {p1}, Landroidx/media3/common/y$b;->w(Landroidx/media3/common/y$b;)Ljava/util/List;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_2

    invoke-static {p1}, Landroidx/media3/common/y$b;->C(Landroidx/media3/common/y$b;)Ljava/lang/String;

    move-result-object v0

    if-eqz v0, :cond_3

    :cond_2
    invoke-static {p1}, Landroidx/media3/common/y$b;->w(Landroidx/media3/common/y$b;)Ljava/util/List;

    move-result-object v0

    invoke-static {v0}, Landroidx/media3/common/t;->a(Ljava/util/List;)Ljava/util/stream/Stream;

    move-result-object v0

    new-instance v1, Landroidx/media3/common/x;

    invoke-direct {v1, p1}, Landroidx/media3/common/x;-><init>(Landroidx/media3/common/y$b;)V

    invoke-static {v0, v1}, Landroidx/media3/common/u;->a(Ljava/util/stream/Stream;Ljava/util/function/Predicate;)Z

    move-result v0

    if-eqz v0, :cond_4

    :cond_3
    const/4 v0, 0x1

    goto :goto_0

    :cond_4
    const/4 v0, 0x0

    :goto_0
    invoke-static {v0}, Le2/a;->g(Z)V

    invoke-static {p1}, Landroidx/media3/common/y$b;->w(Landroidx/media3/common/y$b;)Ljava/util/List;

    move-result-object v0

    iput-object v0, p0, Landroidx/media3/common/y;->c:Ljava/util/List;

    invoke-static {p1}, Landroidx/media3/common/y$b;->C(Landroidx/media3/common/y$b;)Ljava/lang/String;

    move-result-object v0

    iput-object v0, p0, Landroidx/media3/common/y;->b:Ljava/lang/String;

    :goto_1
    invoke-static {p1}, Landroidx/media3/common/y$b;->D(Landroidx/media3/common/y$b;)I

    move-result v0

    iput v0, p0, Landroidx/media3/common/y;->e:I

    invoke-static {p1}, Landroidx/media3/common/y$b;->E(Landroidx/media3/common/y$b;)I

    move-result v0

    iput v0, p0, Landroidx/media3/common/y;->f:I

    invoke-static {p1}, Landroidx/media3/common/y$b;->F(Landroidx/media3/common/y$b;)I

    move-result v0

    iput v0, p0, Landroidx/media3/common/y;->g:I

    invoke-static {p1}, Landroidx/media3/common/y$b;->G(Landroidx/media3/common/y$b;)I

    move-result v1

    iput v1, p0, Landroidx/media3/common/y;->h:I

    const/4 v4, -0x1

    if-eq v1, v4, :cond_5

    move v0, v1

    :cond_5
    iput v0, p0, Landroidx/media3/common/y;->i:I

    invoke-static {p1}, Landroidx/media3/common/y$b;->H(Landroidx/media3/common/y$b;)Ljava/lang/String;

    move-result-object v0

    iput-object v0, p0, Landroidx/media3/common/y;->j:Ljava/lang/String;

    invoke-static {p1}, Landroidx/media3/common/y$b;->b(Landroidx/media3/common/y$b;)Landroidx/media3/common/Metadata;

    move-result-object v0

    iput-object v0, p0, Landroidx/media3/common/y;->k:Landroidx/media3/common/Metadata;

    invoke-static {p1}, Landroidx/media3/common/y$b;->c(Landroidx/media3/common/y$b;)Ljava/lang/String;

    move-result-object v0

    iput-object v0, p0, Landroidx/media3/common/y;->l:Ljava/lang/String;

    invoke-static {p1}, Landroidx/media3/common/y$b;->d(Landroidx/media3/common/y$b;)Ljava/lang/String;

    move-result-object v0

    iput-object v0, p0, Landroidx/media3/common/y;->m:Ljava/lang/String;

    invoke-static {p1}, Landroidx/media3/common/y$b;->e(Landroidx/media3/common/y$b;)I

    move-result v0

    iput v0, p0, Landroidx/media3/common/y;->n:I

    invoke-static {p1}, Landroidx/media3/common/y$b;->f(Landroidx/media3/common/y$b;)Ljava/util/List;

    move-result-object v0

    if-nez v0, :cond_6

    invoke-static {}, Ljava/util/Collections;->emptyList()Ljava/util/List;

    move-result-object v0

    goto :goto_2

    :cond_6
    invoke-static {p1}, Landroidx/media3/common/y$b;->f(Landroidx/media3/common/y$b;)Ljava/util/List;

    move-result-object v0

    :goto_2
    iput-object v0, p0, Landroidx/media3/common/y;->o:Ljava/util/List;

    invoke-static {p1}, Landroidx/media3/common/y$b;->g(Landroidx/media3/common/y$b;)Landroidx/media3/common/DrmInitData;

    move-result-object v0

    iput-object v0, p0, Landroidx/media3/common/y;->p:Landroidx/media3/common/DrmInitData;

    invoke-static {p1}, Landroidx/media3/common/y$b;->h(Landroidx/media3/common/y$b;)J

    move-result-wide v5

    iput-wide v5, p0, Landroidx/media3/common/y;->q:J

    invoke-static {p1}, Landroidx/media3/common/y$b;->i(Landroidx/media3/common/y$b;)I

    move-result v1

    iput v1, p0, Landroidx/media3/common/y;->r:I

    invoke-static {p1}, Landroidx/media3/common/y$b;->j(Landroidx/media3/common/y$b;)I

    move-result v1

    iput v1, p0, Landroidx/media3/common/y;->s:I

    invoke-static {p1}, Landroidx/media3/common/y$b;->k(Landroidx/media3/common/y$b;)F

    move-result v1

    iput v1, p0, Landroidx/media3/common/y;->t:F

    invoke-static {p1}, Landroidx/media3/common/y$b;->m(Landroidx/media3/common/y$b;)I

    move-result v1

    if-ne v1, v4, :cond_7

    const/4 v1, 0x0

    goto :goto_3

    :cond_7
    invoke-static {p1}, Landroidx/media3/common/y$b;->m(Landroidx/media3/common/y$b;)I

    move-result v1

    :goto_3
    iput v1, p0, Landroidx/media3/common/y;->u:I

    invoke-static {p1}, Landroidx/media3/common/y$b;->n(Landroidx/media3/common/y$b;)F

    move-result v1

    const/high16 v5, -0x40800000    # -1.0f

    cmpl-float v1, v1, v5

    if-nez v1, :cond_8

    const/high16 v1, 0x3f800000    # 1.0f

    goto :goto_4

    :cond_8
    invoke-static {p1}, Landroidx/media3/common/y$b;->n(Landroidx/media3/common/y$b;)F

    move-result v1

    :goto_4
    iput v1, p0, Landroidx/media3/common/y;->v:F

    invoke-static {p1}, Landroidx/media3/common/y$b;->o(Landroidx/media3/common/y$b;)[B

    move-result-object v1

    iput-object v1, p0, Landroidx/media3/common/y;->w:[B

    invoke-static {p1}, Landroidx/media3/common/y$b;->p(Landroidx/media3/common/y$b;)I

    move-result v1

    iput v1, p0, Landroidx/media3/common/y;->x:I

    invoke-static {p1}, Landroidx/media3/common/y$b;->q(Landroidx/media3/common/y$b;)Landroidx/media3/common/k;

    move-result-object v1

    iput-object v1, p0, Landroidx/media3/common/y;->y:Landroidx/media3/common/k;

    invoke-static {p1}, Landroidx/media3/common/y$b;->r(Landroidx/media3/common/y$b;)I

    move-result v1

    iput v1, p0, Landroidx/media3/common/y;->z:I

    invoke-static {p1}, Landroidx/media3/common/y$b;->s(Landroidx/media3/common/y$b;)I

    move-result v1

    iput v1, p0, Landroidx/media3/common/y;->A:I

    invoke-static {p1}, Landroidx/media3/common/y$b;->t(Landroidx/media3/common/y$b;)I

    move-result v1

    iput v1, p0, Landroidx/media3/common/y;->B:I

    invoke-static {p1}, Landroidx/media3/common/y$b;->u(Landroidx/media3/common/y$b;)I

    move-result v1

    if-ne v1, v4, :cond_9

    const/4 v1, 0x0

    goto :goto_5

    :cond_9
    invoke-static {p1}, Landroidx/media3/common/y$b;->u(Landroidx/media3/common/y$b;)I

    move-result v1

    :goto_5
    iput v1, p0, Landroidx/media3/common/y;->C:I

    invoke-static {p1}, Landroidx/media3/common/y$b;->v(Landroidx/media3/common/y$b;)I

    move-result v1

    if-ne v1, v4, :cond_a

    goto :goto_6

    :cond_a
    invoke-static {p1}, Landroidx/media3/common/y$b;->v(Landroidx/media3/common/y$b;)I

    move-result v3

    :goto_6
    iput v3, p0, Landroidx/media3/common/y;->D:I

    invoke-static {p1}, Landroidx/media3/common/y$b;->x(Landroidx/media3/common/y$b;)I

    move-result v1

    iput v1, p0, Landroidx/media3/common/y;->E:I

    invoke-static {p1}, Landroidx/media3/common/y$b;->y(Landroidx/media3/common/y$b;)I

    move-result v1

    iput v1, p0, Landroidx/media3/common/y;->F:I

    invoke-static {p1}, Landroidx/media3/common/y$b;->z(Landroidx/media3/common/y$b;)I

    move-result v1

    iput v1, p0, Landroidx/media3/common/y;->G:I

    invoke-static {p1}, Landroidx/media3/common/y$b;->A(Landroidx/media3/common/y$b;)I

    move-result v1

    iput v1, p0, Landroidx/media3/common/y;->H:I

    invoke-static {p1}, Landroidx/media3/common/y$b;->B(Landroidx/media3/common/y$b;)I

    move-result v1

    if-nez v1, :cond_b

    if-eqz v0, :cond_b

    iput v2, p0, Landroidx/media3/common/y;->I:I

    goto :goto_7

    :cond_b
    invoke-static {p1}, Landroidx/media3/common/y$b;->B(Landroidx/media3/common/y$b;)I

    move-result p1

    iput p1, p0, Landroidx/media3/common/y;->I:I

    :goto_7
    return-void
.end method

.method public synthetic constructor <init>(Landroidx/media3/common/y$b;Landroidx/media3/common/y$a;)V
    .locals 0

    invoke-direct {p0, p1}, Landroidx/media3/common/y;-><init>(Landroidx/media3/common/y$b;)V

    return-void
.end method

.method public static synthetic a(Landroidx/media3/common/y$b;Landroidx/media3/common/a0;)Z
    .locals 0

    invoke-static {p0, p1}, Landroidx/media3/common/y;->j(Landroidx/media3/common/y$b;Landroidx/media3/common/a0;)Z

    move-result p0

    return p0
.end method

.method public static d(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0
    .param p0    # Ljava/lang/Object;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .param p1    # Ljava/lang/Object;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .annotation build Landroidx/annotation/Nullable;
    .end annotation

    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">(TT;TT;)TT;"
        }
    .end annotation

    if-eqz p0, :cond_0

    goto :goto_0

    :cond_0
    move-object p0, p1

    :goto_0
    return-object p0
.end method

.method public static e(Landroid/os/Bundle;)Landroidx/media3/common/y;
    .locals 6

    new-instance v0, Landroidx/media3/common/y$b;

    invoke-direct {v0}, Landroidx/media3/common/y$b;-><init>()V

    invoke-static {p0}, Le2/c;->a(Landroid/os/Bundle;)V

    sget-object v1, Landroidx/media3/common/y;->L:Ljava/lang/String;

    invoke-virtual {p0, v1}, Landroid/os/BaseBundle;->getString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v1

    sget-object v2, Landroidx/media3/common/y;->K:Landroidx/media3/common/y;

    iget-object v3, v2, Landroidx/media3/common/y;->a:Ljava/lang/String;

    invoke-static {v1, v3}, Landroidx/media3/common/y;->d(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/lang/String;

    invoke-virtual {v0, v1}, Landroidx/media3/common/y$b;->X(Ljava/lang/String;)Landroidx/media3/common/y$b;

    move-result-object v1

    sget-object v3, Landroidx/media3/common/y;->M:Ljava/lang/String;

    invoke-virtual {p0, v3}, Landroid/os/BaseBundle;->getString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v3

    iget-object v4, v2, Landroidx/media3/common/y;->b:Ljava/lang/String;

    invoke-static {v3, v4}, Landroidx/media3/common/y;->d(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Ljava/lang/String;

    invoke-virtual {v1, v3}, Landroidx/media3/common/y$b;->Z(Ljava/lang/String;)Landroidx/media3/common/y$b;

    sget-object v1, Landroidx/media3/common/y;->r0:Ljava/lang/String;

    invoke-virtual {p0, v1}, Landroid/os/Bundle;->getParcelableArrayList(Ljava/lang/String;)Ljava/util/ArrayList;

    move-result-object v1

    if-nez v1, :cond_0

    invoke-static {}, Lcom/google/common/collect/ImmutableList;->of()Lcom/google/common/collect/ImmutableList;

    move-result-object v1

    goto :goto_0

    :cond_0
    new-instance v3, Landroidx/media3/common/w;

    invoke-direct {v3}, Landroidx/media3/common/w;-><init>()V

    invoke-static {v3, v1}, Le2/c;->b(Lcom/google/common/base/f;Ljava/util/List;)Lcom/google/common/collect/ImmutableList;

    move-result-object v1

    :goto_0
    invoke-virtual {v0, v1}, Landroidx/media3/common/y$b;->a0(Ljava/util/List;)Landroidx/media3/common/y$b;

    move-result-object v1

    sget-object v3, Landroidx/media3/common/y;->N:Ljava/lang/String;

    invoke-virtual {p0, v3}, Landroid/os/BaseBundle;->getString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v3

    iget-object v4, v2, Landroidx/media3/common/y;->d:Ljava/lang/String;

    invoke-static {v3, v4}, Landroidx/media3/common/y;->d(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Ljava/lang/String;

    invoke-virtual {v1, v3}, Landroidx/media3/common/y$b;->b0(Ljava/lang/String;)Landroidx/media3/common/y$b;

    move-result-object v1

    sget-object v3, Landroidx/media3/common/y;->O:Ljava/lang/String;

    iget v4, v2, Landroidx/media3/common/y;->e:I

    invoke-virtual {p0, v3, v4}, Landroid/os/BaseBundle;->getInt(Ljava/lang/String;I)I

    move-result v3

    invoke-virtual {v1, v3}, Landroidx/media3/common/y$b;->m0(I)Landroidx/media3/common/y$b;

    move-result-object v1

    sget-object v3, Landroidx/media3/common/y;->P:Ljava/lang/String;

    iget v4, v2, Landroidx/media3/common/y;->f:I

    invoke-virtual {p0, v3, v4}, Landroid/os/BaseBundle;->getInt(Ljava/lang/String;I)I

    move-result v3

    invoke-virtual {v1, v3}, Landroidx/media3/common/y$b;->i0(I)Landroidx/media3/common/y$b;

    move-result-object v1

    sget-object v3, Landroidx/media3/common/y;->Q:Ljava/lang/String;

    iget v4, v2, Landroidx/media3/common/y;->g:I

    invoke-virtual {p0, v3, v4}, Landroid/os/BaseBundle;->getInt(Ljava/lang/String;I)I

    move-result v3

    invoke-virtual {v1, v3}, Landroidx/media3/common/y$b;->K(I)Landroidx/media3/common/y$b;

    move-result-object v1

    sget-object v3, Landroidx/media3/common/y;->R:Ljava/lang/String;

    iget v4, v2, Landroidx/media3/common/y;->h:I

    invoke-virtual {p0, v3, v4}, Landroid/os/BaseBundle;->getInt(Ljava/lang/String;I)I

    move-result v3

    invoke-virtual {v1, v3}, Landroidx/media3/common/y$b;->f0(I)Landroidx/media3/common/y$b;

    move-result-object v1

    sget-object v3, Landroidx/media3/common/y;->S:Ljava/lang/String;

    invoke-virtual {p0, v3}, Landroid/os/BaseBundle;->getString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v3

    iget-object v4, v2, Landroidx/media3/common/y;->j:Ljava/lang/String;

    invoke-static {v3, v4}, Landroidx/media3/common/y;->d(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Ljava/lang/String;

    invoke-virtual {v1, v3}, Landroidx/media3/common/y$b;->M(Ljava/lang/String;)Landroidx/media3/common/y$b;

    move-result-object v1

    sget-object v3, Landroidx/media3/common/y;->T:Ljava/lang/String;

    invoke-virtual {p0, v3}, Landroid/os/Bundle;->getParcelable(Ljava/lang/String;)Landroid/os/Parcelable;

    move-result-object v3

    check-cast v3, Landroidx/media3/common/Metadata;

    iget-object v4, v2, Landroidx/media3/common/y;->k:Landroidx/media3/common/Metadata;

    invoke-static {v3, v4}, Landroidx/media3/common/y;->d(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Landroidx/media3/common/Metadata;

    invoke-virtual {v1, v3}, Landroidx/media3/common/y$b;->d0(Landroidx/media3/common/Metadata;)Landroidx/media3/common/y$b;

    move-result-object v1

    sget-object v3, Landroidx/media3/common/y;->U:Ljava/lang/String;

    invoke-virtual {p0, v3}, Landroid/os/BaseBundle;->getString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v3

    iget-object v4, v2, Landroidx/media3/common/y;->l:Ljava/lang/String;

    invoke-static {v3, v4}, Landroidx/media3/common/y;->d(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Ljava/lang/String;

    invoke-virtual {v1, v3}, Landroidx/media3/common/y$b;->O(Ljava/lang/String;)Landroidx/media3/common/y$b;

    move-result-object v1

    sget-object v3, Landroidx/media3/common/y;->V:Ljava/lang/String;

    invoke-virtual {p0, v3}, Landroid/os/BaseBundle;->getString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v3

    iget-object v4, v2, Landroidx/media3/common/y;->m:Ljava/lang/String;

    invoke-static {v3, v4}, Landroidx/media3/common/y;->d(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Ljava/lang/String;

    invoke-virtual {v1, v3}, Landroidx/media3/common/y$b;->k0(Ljava/lang/String;)Landroidx/media3/common/y$b;

    move-result-object v1

    sget-object v3, Landroidx/media3/common/y;->W:Ljava/lang/String;

    iget v2, v2, Landroidx/media3/common/y;->n:I

    invoke-virtual {p0, v3, v2}, Landroid/os/BaseBundle;->getInt(Ljava/lang/String;I)I

    move-result v2

    invoke-virtual {v1, v2}, Landroidx/media3/common/y$b;->c0(I)Landroidx/media3/common/y$b;

    new-instance v1, Ljava/util/ArrayList;

    invoke-direct {v1}, Ljava/util/ArrayList;-><init>()V

    const/4 v2, 0x0

    :goto_1
    invoke-static {v2}, Landroidx/media3/common/y;->i(I)Ljava/lang/String;

    move-result-object v3

    invoke-virtual {p0, v3}, Landroid/os/Bundle;->getByteArray(Ljava/lang/String;)[B

    move-result-object v3

    if-nez v3, :cond_2

    invoke-virtual {v0, v1}, Landroidx/media3/common/y$b;->Y(Ljava/util/List;)Landroidx/media3/common/y$b;

    move-result-object v1

    sget-object v2, Landroidx/media3/common/y;->Y:Ljava/lang/String;

    invoke-virtual {p0, v2}, Landroid/os/Bundle;->getParcelable(Ljava/lang/String;)Landroid/os/Parcelable;

    move-result-object v2

    check-cast v2, Landroidx/media3/common/DrmInitData;

    invoke-virtual {v1, v2}, Landroidx/media3/common/y$b;->R(Landroidx/media3/common/DrmInitData;)Landroidx/media3/common/y$b;

    move-result-object v1

    sget-object v2, Landroidx/media3/common/y;->Z:Ljava/lang/String;

    sget-object v3, Landroidx/media3/common/y;->K:Landroidx/media3/common/y;

    iget-wide v4, v3, Landroidx/media3/common/y;->q:J

    invoke-virtual {p0, v2, v4, v5}, Landroid/os/BaseBundle;->getLong(Ljava/lang/String;J)J

    move-result-wide v4

    invoke-virtual {v1, v4, v5}, Landroidx/media3/common/y$b;->o0(J)Landroidx/media3/common/y$b;

    move-result-object v1

    sget-object v2, Landroidx/media3/common/y;->a0:Ljava/lang/String;

    iget v4, v3, Landroidx/media3/common/y;->r:I

    invoke-virtual {p0, v2, v4}, Landroid/os/BaseBundle;->getInt(Ljava/lang/String;I)I

    move-result v2

    invoke-virtual {v1, v2}, Landroidx/media3/common/y$b;->r0(I)Landroidx/media3/common/y$b;

    move-result-object v1

    sget-object v2, Landroidx/media3/common/y;->b0:Ljava/lang/String;

    iget v4, v3, Landroidx/media3/common/y;->s:I

    invoke-virtual {p0, v2, v4}, Landroid/os/BaseBundle;->getInt(Ljava/lang/String;I)I

    move-result v2

    invoke-virtual {v1, v2}, Landroidx/media3/common/y$b;->V(I)Landroidx/media3/common/y$b;

    move-result-object v1

    sget-object v2, Landroidx/media3/common/y;->c0:Ljava/lang/String;

    iget v4, v3, Landroidx/media3/common/y;->t:F

    invoke-virtual {p0, v2, v4}, Landroid/os/Bundle;->getFloat(Ljava/lang/String;F)F

    move-result v2

    invoke-virtual {v1, v2}, Landroidx/media3/common/y$b;->U(F)Landroidx/media3/common/y$b;

    move-result-object v1

    sget-object v2, Landroidx/media3/common/y;->d0:Ljava/lang/String;

    iget v4, v3, Landroidx/media3/common/y;->u:I

    invoke-virtual {p0, v2, v4}, Landroid/os/BaseBundle;->getInt(Ljava/lang/String;I)I

    move-result v2

    invoke-virtual {v1, v2}, Landroidx/media3/common/y$b;->j0(I)Landroidx/media3/common/y$b;

    move-result-object v1

    sget-object v2, Landroidx/media3/common/y;->e0:Ljava/lang/String;

    iget v4, v3, Landroidx/media3/common/y;->v:F

    invoke-virtual {p0, v2, v4}, Landroid/os/Bundle;->getFloat(Ljava/lang/String;F)F

    move-result v2

    invoke-virtual {v1, v2}, Landroidx/media3/common/y$b;->g0(F)Landroidx/media3/common/y$b;

    move-result-object v1

    sget-object v2, Landroidx/media3/common/y;->f0:Ljava/lang/String;

    invoke-virtual {p0, v2}, Landroid/os/Bundle;->getByteArray(Ljava/lang/String;)[B

    move-result-object v2

    invoke-virtual {v1, v2}, Landroidx/media3/common/y$b;->h0([B)Landroidx/media3/common/y$b;

    move-result-object v1

    sget-object v2, Landroidx/media3/common/y;->g0:Ljava/lang/String;

    iget v4, v3, Landroidx/media3/common/y;->x:I

    invoke-virtual {p0, v2, v4}, Landroid/os/BaseBundle;->getInt(Ljava/lang/String;I)I

    move-result v2

    invoke-virtual {v1, v2}, Landroidx/media3/common/y$b;->n0(I)Landroidx/media3/common/y$b;

    sget-object v1, Landroidx/media3/common/y;->h0:Ljava/lang/String;

    invoke-virtual {p0, v1}, Landroid/os/Bundle;->getBundle(Ljava/lang/String;)Landroid/os/Bundle;

    move-result-object v1

    if-eqz v1, :cond_1

    invoke-static {v1}, Landroidx/media3/common/k;->f(Landroid/os/Bundle;)Landroidx/media3/common/k;

    move-result-object v1

    invoke-virtual {v0, v1}, Landroidx/media3/common/y$b;->N(Landroidx/media3/common/k;)Landroidx/media3/common/y$b;

    :cond_1
    sget-object v1, Landroidx/media3/common/y;->i0:Ljava/lang/String;

    iget v2, v3, Landroidx/media3/common/y;->z:I

    invoke-virtual {p0, v1, v2}, Landroid/os/BaseBundle;->getInt(Ljava/lang/String;I)I

    move-result v1

    invoke-virtual {v0, v1}, Landroidx/media3/common/y$b;->L(I)Landroidx/media3/common/y$b;

    move-result-object v1

    sget-object v2, Landroidx/media3/common/y;->j0:Ljava/lang/String;

    iget v4, v3, Landroidx/media3/common/y;->A:I

    invoke-virtual {p0, v2, v4}, Landroid/os/BaseBundle;->getInt(Ljava/lang/String;I)I

    move-result v2

    invoke-virtual {v1, v2}, Landroidx/media3/common/y$b;->l0(I)Landroidx/media3/common/y$b;

    move-result-object v1

    sget-object v2, Landroidx/media3/common/y;->k0:Ljava/lang/String;

    iget v4, v3, Landroidx/media3/common/y;->B:I

    invoke-virtual {p0, v2, v4}, Landroid/os/BaseBundle;->getInt(Ljava/lang/String;I)I

    move-result v2

    invoke-virtual {v1, v2}, Landroidx/media3/common/y$b;->e0(I)Landroidx/media3/common/y$b;

    move-result-object v1

    sget-object v2, Landroidx/media3/common/y;->l0:Ljava/lang/String;

    iget v4, v3, Landroidx/media3/common/y;->C:I

    invoke-virtual {p0, v2, v4}, Landroid/os/BaseBundle;->getInt(Ljava/lang/String;I)I

    move-result v2

    invoke-virtual {v1, v2}, Landroidx/media3/common/y$b;->S(I)Landroidx/media3/common/y$b;

    move-result-object v1

    sget-object v2, Landroidx/media3/common/y;->m0:Ljava/lang/String;

    iget v4, v3, Landroidx/media3/common/y;->D:I

    invoke-virtual {p0, v2, v4}, Landroid/os/BaseBundle;->getInt(Ljava/lang/String;I)I

    move-result v2

    invoke-virtual {v1, v2}, Landroidx/media3/common/y$b;->T(I)Landroidx/media3/common/y$b;

    move-result-object v1

    sget-object v2, Landroidx/media3/common/y;->n0:Ljava/lang/String;

    iget v4, v3, Landroidx/media3/common/y;->E:I

    invoke-virtual {p0, v2, v4}, Landroid/os/BaseBundle;->getInt(Ljava/lang/String;I)I

    move-result v2

    invoke-virtual {v1, v2}, Landroidx/media3/common/y$b;->J(I)Landroidx/media3/common/y$b;

    move-result-object v1

    sget-object v2, Landroidx/media3/common/y;->p0:Ljava/lang/String;

    iget v4, v3, Landroidx/media3/common/y;->G:I

    invoke-virtual {p0, v2, v4}, Landroid/os/BaseBundle;->getInt(Ljava/lang/String;I)I

    move-result v2

    invoke-virtual {v1, v2}, Landroidx/media3/common/y$b;->p0(I)Landroidx/media3/common/y$b;

    move-result-object v1

    sget-object v2, Landroidx/media3/common/y;->q0:Ljava/lang/String;

    iget v4, v3, Landroidx/media3/common/y;->H:I

    invoke-virtual {p0, v2, v4}, Landroid/os/BaseBundle;->getInt(Ljava/lang/String;I)I

    move-result v2

    invoke-virtual {v1, v2}, Landroidx/media3/common/y$b;->q0(I)Landroidx/media3/common/y$b;

    move-result-object v1

    sget-object v2, Landroidx/media3/common/y;->o0:Ljava/lang/String;

    iget v3, v3, Landroidx/media3/common/y;->I:I

    invoke-virtual {p0, v2, v3}, Landroid/os/BaseBundle;->getInt(Ljava/lang/String;I)I

    move-result p0

    invoke-virtual {v1, p0}, Landroidx/media3/common/y$b;->P(I)Landroidx/media3/common/y$b;

    invoke-virtual {v0}, Landroidx/media3/common/y$b;->I()Landroidx/media3/common/y;

    move-result-object p0

    return-object p0

    :cond_2
    invoke-interface {v1, v3}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    add-int/lit8 v2, v2, 0x1

    goto/16 :goto_1
.end method

.method public static f(Ljava/util/List;Ljava/lang/String;)Ljava/lang/String;
    .locals 3
    .param p1    # Ljava/lang/String;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Landroidx/media3/common/a0;",
            ">;",
            "Ljava/lang/String;",
            ")",
            "Ljava/lang/String;"
        }
    .end annotation

    invoke-interface {p0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :cond_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_1

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Landroidx/media3/common/a0;

    iget-object v2, v1, Landroidx/media3/common/a0;->a:Ljava/lang/String;

    invoke-static {v2, p1}, Landroid/text/TextUtils;->equals(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Z

    move-result v2

    if-eqz v2, :cond_0

    iget-object p0, v1, Landroidx/media3/common/a0;->b:Ljava/lang/String;

    return-object p0

    :cond_1
    const/4 p1, 0x0

    invoke-interface {p0, p1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p0

    check-cast p0, Landroidx/media3/common/a0;

    iget-object p0, p0, Landroidx/media3/common/a0;->b:Ljava/lang/String;

    return-object p0
.end method

.method public static i(I)Ljava/lang/String;
    .locals 2

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    sget-object v1, Landroidx/media3/common/y;->X:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v1, "_"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const/16 v1, 0x24

    invoke-static {p0, v1}, Ljava/lang/Integer;->toString(II)Ljava/lang/String;

    move-result-object p0

    invoke-virtual {v0, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic j(Landroidx/media3/common/y$b;Landroidx/media3/common/a0;)Z
    .locals 0

    iget-object p1, p1, Landroidx/media3/common/a0;->b:Ljava/lang/String;

    invoke-static {p0}, Landroidx/media3/common/y$b;->C(Landroidx/media3/common/y$b;)Ljava/lang/String;

    move-result-object p0

    invoke-virtual {p1, p0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result p0

    return p0
.end method

.method public static m(Landroidx/media3/common/y;)Ljava/lang/String;
    .locals 8
    .param p0    # Landroidx/media3/common/y;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    if-nez p0, :cond_0

    const-string p0, "null"

    return-object p0

    :cond_0
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "id="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v1, p0, Landroidx/media3/common/y;->a:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v1, ", mimeType="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v1, p0, Landroidx/media3/common/y;->m:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v1, p0, Landroidx/media3/common/y;->l:Ljava/lang/String;

    if-eqz v1, :cond_1

    const-string v1, ", container="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v1, p0, Landroidx/media3/common/y;->l:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    :cond_1
    iget v1, p0, Landroidx/media3/common/y;->i:I

    const/4 v2, -0x1

    if-eq v1, v2, :cond_2

    const-string v1, ", bitrate="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget v1, p0, Landroidx/media3/common/y;->i:I

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    :cond_2
    iget-object v1, p0, Landroidx/media3/common/y;->j:Ljava/lang/String;

    if-eqz v1, :cond_3

    const-string v1, ", codecs="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v1, p0, Landroidx/media3/common/y;->j:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    :cond_3
    iget-object v1, p0, Landroidx/media3/common/y;->p:Landroidx/media3/common/DrmInitData;

    const/16 v3, 0x2c

    if-eqz v1, :cond_a

    new-instance v1, Ljava/util/LinkedHashSet;

    invoke-direct {v1}, Ljava/util/LinkedHashSet;-><init>()V

    const/4 v4, 0x0

    :goto_0
    iget-object v5, p0, Landroidx/media3/common/y;->p:Landroidx/media3/common/DrmInitData;

    iget v6, v5, Landroidx/media3/common/DrmInitData;->schemeDataCount:I

    if-ge v4, v6, :cond_9

    invoke-virtual {v5, v4}, Landroidx/media3/common/DrmInitData;->get(I)Landroidx/media3/common/DrmInitData$SchemeData;

    move-result-object v5

    iget-object v5, v5, Landroidx/media3/common/DrmInitData$SchemeData;->uuid:Ljava/util/UUID;

    sget-object v6, Landroidx/media3/common/j;->b:Ljava/util/UUID;

    invoke-virtual {v5, v6}, Ljava/util/UUID;->equals(Ljava/lang/Object;)Z

    move-result v6

    if-eqz v6, :cond_4

    const-string v5, "cenc"

    invoke-interface {v1, v5}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    goto :goto_1

    :cond_4
    sget-object v6, Landroidx/media3/common/j;->c:Ljava/util/UUID;

    invoke-virtual {v5, v6}, Ljava/util/UUID;->equals(Ljava/lang/Object;)Z

    move-result v6

    if-eqz v6, :cond_5

    const-string v5, "clearkey"

    invoke-interface {v1, v5}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    goto :goto_1

    :cond_5
    sget-object v6, Landroidx/media3/common/j;->e:Ljava/util/UUID;

    invoke-virtual {v5, v6}, Ljava/util/UUID;->equals(Ljava/lang/Object;)Z

    move-result v6

    if-eqz v6, :cond_6

    const-string v5, "playready"

    invoke-interface {v1, v5}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    goto :goto_1

    :cond_6
    sget-object v6, Landroidx/media3/common/j;->d:Ljava/util/UUID;

    invoke-virtual {v5, v6}, Ljava/util/UUID;->equals(Ljava/lang/Object;)Z

    move-result v6

    if-eqz v6, :cond_7

    const-string v5, "widevine"

    invoke-interface {v1, v5}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    goto :goto_1

    :cond_7
    sget-object v6, Landroidx/media3/common/j;->a:Ljava/util/UUID;

    invoke-virtual {v5, v6}, Ljava/util/UUID;->equals(Ljava/lang/Object;)Z

    move-result v6

    if-eqz v6, :cond_8

    const-string v5, "universal"

    invoke-interface {v1, v5}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    goto :goto_1

    :cond_8
    new-instance v6, Ljava/lang/StringBuilder;

    invoke-direct {v6}, Ljava/lang/StringBuilder;-><init>()V

    const-string v7, "unknown ("

    invoke-virtual {v6, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v6, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v5, ")"

    invoke-virtual {v6, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v6}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v5

    invoke-interface {v1, v5}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    :goto_1
    add-int/lit8 v4, v4, 0x1

    goto :goto_0

    :cond_9
    const-string v4, ", drm=["

    invoke-virtual {v0, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-static {v3}, Lcom/google/common/base/g;->g(C)Lcom/google/common/base/g;

    move-result-object v4

    invoke-virtual {v4, v0, v1}, Lcom/google/common/base/g;->b(Ljava/lang/StringBuilder;Ljava/lang/Iterable;)Ljava/lang/StringBuilder;

    const/16 v1, 0x5d

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    :cond_a
    iget v1, p0, Landroidx/media3/common/y;->r:I

    if-eq v1, v2, :cond_b

    iget v1, p0, Landroidx/media3/common/y;->s:I

    if-eq v1, v2, :cond_b

    const-string v1, ", res="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget v1, p0, Landroidx/media3/common/y;->r:I

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v1, "x"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget v1, p0, Landroidx/media3/common/y;->s:I

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    :cond_b
    iget-object v1, p0, Landroidx/media3/common/y;->y:Landroidx/media3/common/k;

    if-eqz v1, :cond_c

    invoke-virtual {v1}, Landroidx/media3/common/k;->k()Z

    move-result v1

    if-eqz v1, :cond_c

    const-string v1, ", color="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v1, p0, Landroidx/media3/common/y;->y:Landroidx/media3/common/k;

    invoke-virtual {v1}, Landroidx/media3/common/k;->p()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    :cond_c
    iget v1, p0, Landroidx/media3/common/y;->t:F

    const/high16 v4, -0x40800000    # -1.0f

    cmpl-float v1, v1, v4

    if-eqz v1, :cond_d

    const-string v1, ", fps="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget v1, p0, Landroidx/media3/common/y;->t:F

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(F)Ljava/lang/StringBuilder;

    :cond_d
    iget v1, p0, Landroidx/media3/common/y;->z:I

    if-eq v1, v2, :cond_e

    const-string v1, ", channels="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget v1, p0, Landroidx/media3/common/y;->z:I

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    :cond_e
    iget v1, p0, Landroidx/media3/common/y;->A:I

    if-eq v1, v2, :cond_f

    const-string v1, ", sample_rate="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget v1, p0, Landroidx/media3/common/y;->A:I

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    :cond_f
    iget-object v1, p0, Landroidx/media3/common/y;->d:Ljava/lang/String;

    if-eqz v1, :cond_10

    const-string v1, ", language="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v1, p0, Landroidx/media3/common/y;->d:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    :cond_10
    iget-object v1, p0, Landroidx/media3/common/y;->c:Ljava/util/List;

    invoke-interface {v1}, Ljava/util/List;->isEmpty()Z

    move-result v1

    const-string v2, "]"

    if-nez v1, :cond_11

    const-string v1, ", labels=["

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-static {v3}, Lcom/google/common/base/g;->g(C)Lcom/google/common/base/g;

    move-result-object v1

    iget-object v4, p0, Landroidx/media3/common/y;->c:Ljava/util/List;

    invoke-virtual {v1, v0, v4}, Lcom/google/common/base/g;->b(Ljava/lang/StringBuilder;Ljava/lang/Iterable;)Ljava/lang/StringBuilder;

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    :cond_11
    iget v1, p0, Landroidx/media3/common/y;->e:I

    if-eqz v1, :cond_12

    const-string v1, ", selectionFlags=["

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-static {v3}, Lcom/google/common/base/g;->g(C)Lcom/google/common/base/g;

    move-result-object v1

    iget v4, p0, Landroidx/media3/common/y;->e:I

    invoke-static {v4}, Le2/u0;->o0(I)Ljava/util/List;

    move-result-object v4

    invoke-virtual {v1, v0, v4}, Lcom/google/common/base/g;->b(Ljava/lang/StringBuilder;Ljava/lang/Iterable;)Ljava/lang/StringBuilder;

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    :cond_12
    iget v1, p0, Landroidx/media3/common/y;->f:I

    if-eqz v1, :cond_13

    const-string v1, ", roleFlags=["

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-static {v3}, Lcom/google/common/base/g;->g(C)Lcom/google/common/base/g;

    move-result-object v1

    iget p0, p0, Landroidx/media3/common/y;->f:I

    invoke-static {p0}, Le2/u0;->n0(I)Ljava/util/List;

    move-result-object p0

    invoke-virtual {v1, v0, p0}, Lcom/google/common/base/g;->b(Ljava/lang/StringBuilder;Ljava/lang/Iterable;)Ljava/lang/StringBuilder;

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    :cond_13
    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    return-object p0
.end method


# virtual methods
.method public b()Landroidx/media3/common/y$b;
    .locals 2

    new-instance v0, Landroidx/media3/common/y$b;

    const/4 v1, 0x0

    invoke-direct {v0, p0, v1}, Landroidx/media3/common/y$b;-><init>(Landroidx/media3/common/y;Landroidx/media3/common/y$a;)V

    return-object v0
.end method

.method public c(I)Landroidx/media3/common/y;
    .locals 1

    invoke-virtual {p0}, Landroidx/media3/common/y;->b()Landroidx/media3/common/y$b;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroidx/media3/common/y$b;->P(I)Landroidx/media3/common/y$b;

    move-result-object p1

    invoke-virtual {p1}, Landroidx/media3/common/y$b;->I()Landroidx/media3/common/y;

    move-result-object p1

    return-object p1
.end method

.method public equals(Ljava/lang/Object;)Z
    .locals 7
    .param p1    # Ljava/lang/Object;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    const/4 v0, 0x1

    if-ne p0, p1, :cond_0

    return v0

    :cond_0
    const/4 v1, 0x0

    if-eqz p1, :cond_4

    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v2

    const-class v3, Landroidx/media3/common/y;

    if-eq v3, v2, :cond_1

    goto/16 :goto_1

    :cond_1
    check-cast p1, Landroidx/media3/common/y;

    iget v2, p0, Landroidx/media3/common/y;->J:I

    if-eqz v2, :cond_2

    iget v3, p1, Landroidx/media3/common/y;->J:I

    if-eqz v3, :cond_2

    if-eq v2, v3, :cond_2

    return v1

    :cond_2
    iget v2, p0, Landroidx/media3/common/y;->e:I

    iget v3, p1, Landroidx/media3/common/y;->e:I

    if-ne v2, v3, :cond_3

    iget v2, p0, Landroidx/media3/common/y;->f:I

    iget v3, p1, Landroidx/media3/common/y;->f:I

    if-ne v2, v3, :cond_3

    iget v2, p0, Landroidx/media3/common/y;->g:I

    iget v3, p1, Landroidx/media3/common/y;->g:I

    if-ne v2, v3, :cond_3

    iget v2, p0, Landroidx/media3/common/y;->h:I

    iget v3, p1, Landroidx/media3/common/y;->h:I

    if-ne v2, v3, :cond_3

    iget v2, p0, Landroidx/media3/common/y;->n:I

    iget v3, p1, Landroidx/media3/common/y;->n:I

    if-ne v2, v3, :cond_3

    iget-wide v2, p0, Landroidx/media3/common/y;->q:J

    iget-wide v4, p1, Landroidx/media3/common/y;->q:J

    cmp-long v6, v2, v4

    if-nez v6, :cond_3

    iget v2, p0, Landroidx/media3/common/y;->r:I

    iget v3, p1, Landroidx/media3/common/y;->r:I

    if-ne v2, v3, :cond_3

    iget v2, p0, Landroidx/media3/common/y;->s:I

    iget v3, p1, Landroidx/media3/common/y;->s:I

    if-ne v2, v3, :cond_3

    iget v2, p0, Landroidx/media3/common/y;->u:I

    iget v3, p1, Landroidx/media3/common/y;->u:I

    if-ne v2, v3, :cond_3

    iget v2, p0, Landroidx/media3/common/y;->x:I

    iget v3, p1, Landroidx/media3/common/y;->x:I

    if-ne v2, v3, :cond_3

    iget v2, p0, Landroidx/media3/common/y;->z:I

    iget v3, p1, Landroidx/media3/common/y;->z:I

    if-ne v2, v3, :cond_3

    iget v2, p0, Landroidx/media3/common/y;->A:I

    iget v3, p1, Landroidx/media3/common/y;->A:I

    if-ne v2, v3, :cond_3

    iget v2, p0, Landroidx/media3/common/y;->B:I

    iget v3, p1, Landroidx/media3/common/y;->B:I

    if-ne v2, v3, :cond_3

    iget v2, p0, Landroidx/media3/common/y;->C:I

    iget v3, p1, Landroidx/media3/common/y;->C:I

    if-ne v2, v3, :cond_3

    iget v2, p0, Landroidx/media3/common/y;->D:I

    iget v3, p1, Landroidx/media3/common/y;->D:I

    if-ne v2, v3, :cond_3

    iget v2, p0, Landroidx/media3/common/y;->E:I

    iget v3, p1, Landroidx/media3/common/y;->E:I

    if-ne v2, v3, :cond_3

    iget v2, p0, Landroidx/media3/common/y;->G:I

    iget v3, p1, Landroidx/media3/common/y;->G:I

    if-ne v2, v3, :cond_3

    iget v2, p0, Landroidx/media3/common/y;->H:I

    iget v3, p1, Landroidx/media3/common/y;->H:I

    if-ne v2, v3, :cond_3

    iget v2, p0, Landroidx/media3/common/y;->I:I

    iget v3, p1, Landroidx/media3/common/y;->I:I

    if-ne v2, v3, :cond_3

    iget v2, p0, Landroidx/media3/common/y;->t:F

    iget v3, p1, Landroidx/media3/common/y;->t:F

    invoke-static {v2, v3}, Ljava/lang/Float;->compare(FF)I

    move-result v2

    if-nez v2, :cond_3

    iget v2, p0, Landroidx/media3/common/y;->v:F

    iget v3, p1, Landroidx/media3/common/y;->v:F

    invoke-static {v2, v3}, Ljava/lang/Float;->compare(FF)I

    move-result v2

    if-nez v2, :cond_3

    iget-object v2, p0, Landroidx/media3/common/y;->a:Ljava/lang/String;

    iget-object v3, p1, Landroidx/media3/common/y;->a:Ljava/lang/String;

    invoke-static {v2, v3}, Le2/u0;->c(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_3

    iget-object v2, p0, Landroidx/media3/common/y;->b:Ljava/lang/String;

    iget-object v3, p1, Landroidx/media3/common/y;->b:Ljava/lang/String;

    invoke-static {v2, v3}, Le2/u0;->c(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_3

    iget-object v2, p0, Landroidx/media3/common/y;->c:Ljava/util/List;

    iget-object v3, p1, Landroidx/media3/common/y;->c:Ljava/util/List;

    invoke-interface {v2, v3}, Ljava/util/List;->equals(Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_3

    iget-object v2, p0, Landroidx/media3/common/y;->j:Ljava/lang/String;

    iget-object v3, p1, Landroidx/media3/common/y;->j:Ljava/lang/String;

    invoke-static {v2, v3}, Le2/u0;->c(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_3

    iget-object v2, p0, Landroidx/media3/common/y;->l:Ljava/lang/String;

    iget-object v3, p1, Landroidx/media3/common/y;->l:Ljava/lang/String;

    invoke-static {v2, v3}, Le2/u0;->c(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_3

    iget-object v2, p0, Landroidx/media3/common/y;->m:Ljava/lang/String;

    iget-object v3, p1, Landroidx/media3/common/y;->m:Ljava/lang/String;

    invoke-static {v2, v3}, Le2/u0;->c(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_3

    iget-object v2, p0, Landroidx/media3/common/y;->d:Ljava/lang/String;

    iget-object v3, p1, Landroidx/media3/common/y;->d:Ljava/lang/String;

    invoke-static {v2, v3}, Le2/u0;->c(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_3

    iget-object v2, p0, Landroidx/media3/common/y;->w:[B

    iget-object v3, p1, Landroidx/media3/common/y;->w:[B

    invoke-static {v2, v3}, Ljava/util/Arrays;->equals([B[B)Z

    move-result v2

    if-eqz v2, :cond_3

    iget-object v2, p0, Landroidx/media3/common/y;->k:Landroidx/media3/common/Metadata;

    iget-object v3, p1, Landroidx/media3/common/y;->k:Landroidx/media3/common/Metadata;

    invoke-static {v2, v3}, Le2/u0;->c(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_3

    iget-object v2, p0, Landroidx/media3/common/y;->y:Landroidx/media3/common/k;

    iget-object v3, p1, Landroidx/media3/common/y;->y:Landroidx/media3/common/k;

    invoke-static {v2, v3}, Le2/u0;->c(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_3

    iget-object v2, p0, Landroidx/media3/common/y;->p:Landroidx/media3/common/DrmInitData;

    iget-object v3, p1, Landroidx/media3/common/y;->p:Landroidx/media3/common/DrmInitData;

    invoke-static {v2, v3}, Le2/u0;->c(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_3

    invoke-virtual {p0, p1}, Landroidx/media3/common/y;->h(Landroidx/media3/common/y;)Z

    move-result p1

    if-eqz p1, :cond_3

    goto :goto_0

    :cond_3
    const/4 v0, 0x0

    :goto_0
    return v0

    :cond_4
    :goto_1
    return v1
.end method

.method public g()I
    .locals 3

    iget v0, p0, Landroidx/media3/common/y;->r:I

    const/4 v1, -0x1

    if-eq v0, v1, :cond_1

    iget v2, p0, Landroidx/media3/common/y;->s:I

    if-ne v2, v1, :cond_0

    goto :goto_0

    :cond_0
    mul-int v1, v0, v2

    :cond_1
    :goto_0
    return v1
.end method

.method public h(Landroidx/media3/common/y;)Z
    .locals 4

    iget-object v0, p0, Landroidx/media3/common/y;->o:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    iget-object v1, p1, Landroidx/media3/common/y;->o:Ljava/util/List;

    invoke-interface {v1}, Ljava/util/List;->size()I

    move-result v1

    const/4 v2, 0x0

    if-eq v0, v1, :cond_0

    return v2

    :cond_0
    const/4 v0, 0x0

    :goto_0
    iget-object v1, p0, Landroidx/media3/common/y;->o:Ljava/util/List;

    invoke-interface {v1}, Ljava/util/List;->size()I

    move-result v1

    if-ge v0, v1, :cond_2

    iget-object v1, p0, Landroidx/media3/common/y;->o:Ljava/util/List;

    invoke-interface {v1, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, [B

    iget-object v3, p1, Landroidx/media3/common/y;->o:Ljava/util/List;

    invoke-interface {v3, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, [B

    invoke-static {v1, v3}, Ljava/util/Arrays;->equals([B[B)Z

    move-result v1

    if-nez v1, :cond_1

    return v2

    :cond_1
    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :cond_2
    const/4 p1, 0x1

    return p1
.end method

.method public hashCode()I
    .locals 3

    iget v0, p0, Landroidx/media3/common/y;->J:I

    if-nez v0, :cond_7

    iget-object v0, p0, Landroidx/media3/common/y;->a:Ljava/lang/String;

    const/4 v1, 0x0

    if-nez v0, :cond_0

    const/4 v0, 0x0

    goto :goto_0

    :cond_0
    invoke-virtual {v0}, Ljava/lang/String;->hashCode()I

    move-result v0

    :goto_0
    const/16 v2, 0x20f

    add-int/2addr v2, v0

    mul-int/lit8 v2, v2, 0x1f

    iget-object v0, p0, Landroidx/media3/common/y;->b:Ljava/lang/String;

    if-nez v0, :cond_1

    const/4 v0, 0x0

    goto :goto_1

    :cond_1
    invoke-virtual {v0}, Ljava/lang/String;->hashCode()I

    move-result v0

    :goto_1
    add-int/2addr v2, v0

    mul-int/lit8 v2, v2, 0x1f

    iget-object v0, p0, Landroidx/media3/common/y;->c:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->hashCode()I

    move-result v0

    add-int/2addr v2, v0

    mul-int/lit8 v2, v2, 0x1f

    iget-object v0, p0, Landroidx/media3/common/y;->d:Ljava/lang/String;

    if-nez v0, :cond_2

    const/4 v0, 0x0

    goto :goto_2

    :cond_2
    invoke-virtual {v0}, Ljava/lang/String;->hashCode()I

    move-result v0

    :goto_2
    add-int/2addr v2, v0

    mul-int/lit8 v2, v2, 0x1f

    iget v0, p0, Landroidx/media3/common/y;->e:I

    add-int/2addr v2, v0

    mul-int/lit8 v2, v2, 0x1f

    iget v0, p0, Landroidx/media3/common/y;->f:I

    add-int/2addr v2, v0

    mul-int/lit8 v2, v2, 0x1f

    iget v0, p0, Landroidx/media3/common/y;->g:I

    add-int/2addr v2, v0

    mul-int/lit8 v2, v2, 0x1f

    iget v0, p0, Landroidx/media3/common/y;->h:I

    add-int/2addr v2, v0

    mul-int/lit8 v2, v2, 0x1f

    iget-object v0, p0, Landroidx/media3/common/y;->j:Ljava/lang/String;

    if-nez v0, :cond_3

    const/4 v0, 0x0

    goto :goto_3

    :cond_3
    invoke-virtual {v0}, Ljava/lang/String;->hashCode()I

    move-result v0

    :goto_3
    add-int/2addr v2, v0

    mul-int/lit8 v2, v2, 0x1f

    iget-object v0, p0, Landroidx/media3/common/y;->k:Landroidx/media3/common/Metadata;

    if-nez v0, :cond_4

    const/4 v0, 0x0

    goto :goto_4

    :cond_4
    invoke-virtual {v0}, Landroidx/media3/common/Metadata;->hashCode()I

    move-result v0

    :goto_4
    add-int/2addr v2, v0

    mul-int/lit8 v2, v2, 0x1f

    iget-object v0, p0, Landroidx/media3/common/y;->l:Ljava/lang/String;

    if-nez v0, :cond_5

    const/4 v0, 0x0

    goto :goto_5

    :cond_5
    invoke-virtual {v0}, Ljava/lang/String;->hashCode()I

    move-result v0

    :goto_5
    add-int/2addr v2, v0

    mul-int/lit8 v2, v2, 0x1f

    iget-object v0, p0, Landroidx/media3/common/y;->m:Ljava/lang/String;

    if-nez v0, :cond_6

    goto :goto_6

    :cond_6
    invoke-virtual {v0}, Ljava/lang/String;->hashCode()I

    move-result v1

    :goto_6
    add-int/2addr v2, v1

    mul-int/lit8 v2, v2, 0x1f

    iget v0, p0, Landroidx/media3/common/y;->n:I

    add-int/2addr v2, v0

    mul-int/lit8 v2, v2, 0x1f

    iget-wide v0, p0, Landroidx/media3/common/y;->q:J

    long-to-int v1, v0

    add-int/2addr v2, v1

    mul-int/lit8 v2, v2, 0x1f

    iget v0, p0, Landroidx/media3/common/y;->r:I

    add-int/2addr v2, v0

    mul-int/lit8 v2, v2, 0x1f

    iget v0, p0, Landroidx/media3/common/y;->s:I

    add-int/2addr v2, v0

    mul-int/lit8 v2, v2, 0x1f

    iget v0, p0, Landroidx/media3/common/y;->t:F

    invoke-static {v0}, Ljava/lang/Float;->floatToIntBits(F)I

    move-result v0

    add-int/2addr v2, v0

    mul-int/lit8 v2, v2, 0x1f

    iget v0, p0, Landroidx/media3/common/y;->u:I

    add-int/2addr v2, v0

    mul-int/lit8 v2, v2, 0x1f

    iget v0, p0, Landroidx/media3/common/y;->v:F

    invoke-static {v0}, Ljava/lang/Float;->floatToIntBits(F)I

    move-result v0

    add-int/2addr v2, v0

    mul-int/lit8 v2, v2, 0x1f

    iget v0, p0, Landroidx/media3/common/y;->x:I

    add-int/2addr v2, v0

    mul-int/lit8 v2, v2, 0x1f

    iget v0, p0, Landroidx/media3/common/y;->z:I

    add-int/2addr v2, v0

    mul-int/lit8 v2, v2, 0x1f

    iget v0, p0, Landroidx/media3/common/y;->A:I

    add-int/2addr v2, v0

    mul-int/lit8 v2, v2, 0x1f

    iget v0, p0, Landroidx/media3/common/y;->B:I

    add-int/2addr v2, v0

    mul-int/lit8 v2, v2, 0x1f

    iget v0, p0, Landroidx/media3/common/y;->C:I

    add-int/2addr v2, v0

    mul-int/lit8 v2, v2, 0x1f

    iget v0, p0, Landroidx/media3/common/y;->D:I

    add-int/2addr v2, v0

    mul-int/lit8 v2, v2, 0x1f

    iget v0, p0, Landroidx/media3/common/y;->E:I

    add-int/2addr v2, v0

    mul-int/lit8 v2, v2, 0x1f

    iget v0, p0, Landroidx/media3/common/y;->G:I

    add-int/2addr v2, v0

    mul-int/lit8 v2, v2, 0x1f

    iget v0, p0, Landroidx/media3/common/y;->H:I

    add-int/2addr v2, v0

    mul-int/lit8 v2, v2, 0x1f

    iget v0, p0, Landroidx/media3/common/y;->I:I

    add-int/2addr v2, v0

    iput v2, p0, Landroidx/media3/common/y;->J:I

    :cond_7
    iget v0, p0, Landroidx/media3/common/y;->J:I

    return v0
.end method

.method public k()Landroid/os/Bundle;
    .locals 1

    const/4 v0, 0x0

    invoke-virtual {p0, v0}, Landroidx/media3/common/y;->l(Z)Landroid/os/Bundle;

    move-result-object v0

    return-object v0
.end method

.method public l(Z)Landroid/os/Bundle;
    .locals 4

    new-instance v0, Landroid/os/Bundle;

    invoke-direct {v0}, Landroid/os/Bundle;-><init>()V

    sget-object v1, Landroidx/media3/common/y;->L:Ljava/lang/String;

    iget-object v2, p0, Landroidx/media3/common/y;->a:Ljava/lang/String;

    invoke-virtual {v0, v1, v2}, Landroid/os/BaseBundle;->putString(Ljava/lang/String;Ljava/lang/String;)V

    sget-object v1, Landroidx/media3/common/y;->M:Ljava/lang/String;

    iget-object v2, p0, Landroidx/media3/common/y;->b:Ljava/lang/String;

    invoke-virtual {v0, v1, v2}, Landroid/os/BaseBundle;->putString(Ljava/lang/String;Ljava/lang/String;)V

    sget-object v1, Landroidx/media3/common/y;->r0:Ljava/lang/String;

    iget-object v2, p0, Landroidx/media3/common/y;->c:Ljava/util/List;

    new-instance v3, Landroidx/media3/common/v;

    invoke-direct {v3}, Landroidx/media3/common/v;-><init>()V

    invoke-static {v2, v3}, Le2/c;->c(Ljava/util/Collection;Lcom/google/common/base/f;)Ljava/util/ArrayList;

    move-result-object v2

    invoke-virtual {v0, v1, v2}, Landroid/os/Bundle;->putParcelableArrayList(Ljava/lang/String;Ljava/util/ArrayList;)V

    sget-object v1, Landroidx/media3/common/y;->N:Ljava/lang/String;

    iget-object v2, p0, Landroidx/media3/common/y;->d:Ljava/lang/String;

    invoke-virtual {v0, v1, v2}, Landroid/os/BaseBundle;->putString(Ljava/lang/String;Ljava/lang/String;)V

    sget-object v1, Landroidx/media3/common/y;->O:Ljava/lang/String;

    iget v2, p0, Landroidx/media3/common/y;->e:I

    invoke-virtual {v0, v1, v2}, Landroid/os/BaseBundle;->putInt(Ljava/lang/String;I)V

    sget-object v1, Landroidx/media3/common/y;->P:Ljava/lang/String;

    iget v2, p0, Landroidx/media3/common/y;->f:I

    invoke-virtual {v0, v1, v2}, Landroid/os/BaseBundle;->putInt(Ljava/lang/String;I)V

    sget-object v1, Landroidx/media3/common/y;->Q:Ljava/lang/String;

    iget v2, p0, Landroidx/media3/common/y;->g:I

    invoke-virtual {v0, v1, v2}, Landroid/os/BaseBundle;->putInt(Ljava/lang/String;I)V

    sget-object v1, Landroidx/media3/common/y;->R:Ljava/lang/String;

    iget v2, p0, Landroidx/media3/common/y;->h:I

    invoke-virtual {v0, v1, v2}, Landroid/os/BaseBundle;->putInt(Ljava/lang/String;I)V

    sget-object v1, Landroidx/media3/common/y;->S:Ljava/lang/String;

    iget-object v2, p0, Landroidx/media3/common/y;->j:Ljava/lang/String;

    invoke-virtual {v0, v1, v2}, Landroid/os/BaseBundle;->putString(Ljava/lang/String;Ljava/lang/String;)V

    if-nez p1, :cond_0

    sget-object p1, Landroidx/media3/common/y;->T:Ljava/lang/String;

    iget-object v1, p0, Landroidx/media3/common/y;->k:Landroidx/media3/common/Metadata;

    invoke-virtual {v0, p1, v1}, Landroid/os/Bundle;->putParcelable(Ljava/lang/String;Landroid/os/Parcelable;)V

    :cond_0
    sget-object p1, Landroidx/media3/common/y;->U:Ljava/lang/String;

    iget-object v1, p0, Landroidx/media3/common/y;->l:Ljava/lang/String;

    invoke-virtual {v0, p1, v1}, Landroid/os/BaseBundle;->putString(Ljava/lang/String;Ljava/lang/String;)V

    sget-object p1, Landroidx/media3/common/y;->V:Ljava/lang/String;

    iget-object v1, p0, Landroidx/media3/common/y;->m:Ljava/lang/String;

    invoke-virtual {v0, p1, v1}, Landroid/os/BaseBundle;->putString(Ljava/lang/String;Ljava/lang/String;)V

    sget-object p1, Landroidx/media3/common/y;->W:Ljava/lang/String;

    iget v1, p0, Landroidx/media3/common/y;->n:I

    invoke-virtual {v0, p1, v1}, Landroid/os/BaseBundle;->putInt(Ljava/lang/String;I)V

    const/4 p1, 0x0

    :goto_0
    iget-object v1, p0, Landroidx/media3/common/y;->o:Ljava/util/List;

    invoke-interface {v1}, Ljava/util/List;->size()I

    move-result v1

    if-ge p1, v1, :cond_1

    invoke-static {p1}, Landroidx/media3/common/y;->i(I)Ljava/lang/String;

    move-result-object v1

    iget-object v2, p0, Landroidx/media3/common/y;->o:Ljava/util/List;

    invoke-interface {v2, p1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, [B

    invoke-virtual {v0, v1, v2}, Landroid/os/Bundle;->putByteArray(Ljava/lang/String;[B)V

    add-int/lit8 p1, p1, 0x1

    goto :goto_0

    :cond_1
    sget-object p1, Landroidx/media3/common/y;->Y:Ljava/lang/String;

    iget-object v1, p0, Landroidx/media3/common/y;->p:Landroidx/media3/common/DrmInitData;

    invoke-virtual {v0, p1, v1}, Landroid/os/Bundle;->putParcelable(Ljava/lang/String;Landroid/os/Parcelable;)V

    sget-object p1, Landroidx/media3/common/y;->Z:Ljava/lang/String;

    iget-wide v1, p0, Landroidx/media3/common/y;->q:J

    invoke-virtual {v0, p1, v1, v2}, Landroid/os/BaseBundle;->putLong(Ljava/lang/String;J)V

    sget-object p1, Landroidx/media3/common/y;->a0:Ljava/lang/String;

    iget v1, p0, Landroidx/media3/common/y;->r:I

    invoke-virtual {v0, p1, v1}, Landroid/os/BaseBundle;->putInt(Ljava/lang/String;I)V

    sget-object p1, Landroidx/media3/common/y;->b0:Ljava/lang/String;

    iget v1, p0, Landroidx/media3/common/y;->s:I

    invoke-virtual {v0, p1, v1}, Landroid/os/BaseBundle;->putInt(Ljava/lang/String;I)V

    sget-object p1, Landroidx/media3/common/y;->c0:Ljava/lang/String;

    iget v1, p0, Landroidx/media3/common/y;->t:F

    invoke-virtual {v0, p1, v1}, Landroid/os/Bundle;->putFloat(Ljava/lang/String;F)V

    sget-object p1, Landroidx/media3/common/y;->d0:Ljava/lang/String;

    iget v1, p0, Landroidx/media3/common/y;->u:I

    invoke-virtual {v0, p1, v1}, Landroid/os/BaseBundle;->putInt(Ljava/lang/String;I)V

    sget-object p1, Landroidx/media3/common/y;->e0:Ljava/lang/String;

    iget v1, p0, Landroidx/media3/common/y;->v:F

    invoke-virtual {v0, p1, v1}, Landroid/os/Bundle;->putFloat(Ljava/lang/String;F)V

    sget-object p1, Landroidx/media3/common/y;->f0:Ljava/lang/String;

    iget-object v1, p0, Landroidx/media3/common/y;->w:[B

    invoke-virtual {v0, p1, v1}, Landroid/os/Bundle;->putByteArray(Ljava/lang/String;[B)V

    sget-object p1, Landroidx/media3/common/y;->g0:Ljava/lang/String;

    iget v1, p0, Landroidx/media3/common/y;->x:I

    invoke-virtual {v0, p1, v1}, Landroid/os/BaseBundle;->putInt(Ljava/lang/String;I)V

    iget-object p1, p0, Landroidx/media3/common/y;->y:Landroidx/media3/common/k;

    if-eqz p1, :cond_2

    sget-object v1, Landroidx/media3/common/y;->h0:Ljava/lang/String;

    invoke-virtual {p1}, Landroidx/media3/common/k;->o()Landroid/os/Bundle;

    move-result-object p1

    invoke-virtual {v0, v1, p1}, Landroid/os/Bundle;->putBundle(Ljava/lang/String;Landroid/os/Bundle;)V

    :cond_2
    sget-object p1, Landroidx/media3/common/y;->i0:Ljava/lang/String;

    iget v1, p0, Landroidx/media3/common/y;->z:I

    invoke-virtual {v0, p1, v1}, Landroid/os/BaseBundle;->putInt(Ljava/lang/String;I)V

    sget-object p1, Landroidx/media3/common/y;->j0:Ljava/lang/String;

    iget v1, p0, Landroidx/media3/common/y;->A:I

    invoke-virtual {v0, p1, v1}, Landroid/os/BaseBundle;->putInt(Ljava/lang/String;I)V

    sget-object p1, Landroidx/media3/common/y;->k0:Ljava/lang/String;

    iget v1, p0, Landroidx/media3/common/y;->B:I

    invoke-virtual {v0, p1, v1}, Landroid/os/BaseBundle;->putInt(Ljava/lang/String;I)V

    sget-object p1, Landroidx/media3/common/y;->l0:Ljava/lang/String;

    iget v1, p0, Landroidx/media3/common/y;->C:I

    invoke-virtual {v0, p1, v1}, Landroid/os/BaseBundle;->putInt(Ljava/lang/String;I)V

    sget-object p1, Landroidx/media3/common/y;->m0:Ljava/lang/String;

    iget v1, p0, Landroidx/media3/common/y;->D:I

    invoke-virtual {v0, p1, v1}, Landroid/os/BaseBundle;->putInt(Ljava/lang/String;I)V

    sget-object p1, Landroidx/media3/common/y;->n0:Ljava/lang/String;

    iget v1, p0, Landroidx/media3/common/y;->E:I

    invoke-virtual {v0, p1, v1}, Landroid/os/BaseBundle;->putInt(Ljava/lang/String;I)V

    sget-object p1, Landroidx/media3/common/y;->p0:Ljava/lang/String;

    iget v1, p0, Landroidx/media3/common/y;->G:I

    invoke-virtual {v0, p1, v1}, Landroid/os/BaseBundle;->putInt(Ljava/lang/String;I)V

    sget-object p1, Landroidx/media3/common/y;->q0:Ljava/lang/String;

    iget v1, p0, Landroidx/media3/common/y;->H:I

    invoke-virtual {v0, p1, v1}, Landroid/os/BaseBundle;->putInt(Ljava/lang/String;I)V

    sget-object p1, Landroidx/media3/common/y;->o0:Ljava/lang/String;

    iget v1, p0, Landroidx/media3/common/y;->I:I

    invoke-virtual {v0, p1, v1}, Landroid/os/BaseBundle;->putInt(Ljava/lang/String;I)V

    return-object v0
.end method

.method public n(Landroidx/media3/common/y;)Landroidx/media3/common/y;
    .locals 14

    if-ne p0, p1, :cond_0

    return-object p0

    :cond_0
    iget-object v0, p0, Landroidx/media3/common/y;->m:Ljava/lang/String;

    invoke-static {v0}, Landroidx/media3/common/f0;->k(Ljava/lang/String;)I

    move-result v0

    iget-object v1, p1, Landroidx/media3/common/y;->a:Ljava/lang/String;

    iget v2, p1, Landroidx/media3/common/y;->G:I

    iget v3, p1, Landroidx/media3/common/y;->H:I

    iget-object v4, p1, Landroidx/media3/common/y;->b:Ljava/lang/String;

    if-eqz v4, :cond_1

    goto :goto_0

    :cond_1
    iget-object v4, p0, Landroidx/media3/common/y;->b:Ljava/lang/String;

    :goto_0
    iget-object v5, p1, Landroidx/media3/common/y;->c:Ljava/util/List;

    invoke-interface {v5}, Ljava/util/List;->isEmpty()Z

    move-result v5

    if-nez v5, :cond_2

    iget-object v5, p1, Landroidx/media3/common/y;->c:Ljava/util/List;

    goto :goto_1

    :cond_2
    iget-object v5, p0, Landroidx/media3/common/y;->c:Ljava/util/List;

    :goto_1
    iget-object v6, p0, Landroidx/media3/common/y;->d:Ljava/lang/String;

    const/4 v7, 0x3

    const/4 v8, 0x1

    if-eq v0, v7, :cond_3

    if-ne v0, v8, :cond_4

    :cond_3
    iget-object v7, p1, Landroidx/media3/common/y;->d:Ljava/lang/String;

    if-eqz v7, :cond_4

    move-object v6, v7

    :cond_4
    iget v7, p0, Landroidx/media3/common/y;->g:I

    const/4 v9, -0x1

    if-ne v7, v9, :cond_5

    iget v7, p1, Landroidx/media3/common/y;->g:I

    :cond_5
    iget v10, p0, Landroidx/media3/common/y;->h:I

    if-ne v10, v9, :cond_6

    iget v10, p1, Landroidx/media3/common/y;->h:I

    :cond_6
    iget-object v9, p0, Landroidx/media3/common/y;->j:Ljava/lang/String;

    if-nez v9, :cond_7

    iget-object v11, p1, Landroidx/media3/common/y;->j:Ljava/lang/String;

    invoke-static {v11, v0}, Le2/u0;->Q(Ljava/lang/String;I)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Le2/u0;->q1(Ljava/lang/String;)[Ljava/lang/String;

    move-result-object v12

    array-length v12, v12

    if-ne v12, v8, :cond_7

    move-object v9, v11

    :cond_7
    iget-object v8, p0, Landroidx/media3/common/y;->k:Landroidx/media3/common/Metadata;

    if-nez v8, :cond_8

    iget-object v8, p1, Landroidx/media3/common/y;->k:Landroidx/media3/common/Metadata;

    goto :goto_2

    :cond_8
    iget-object v11, p1, Landroidx/media3/common/y;->k:Landroidx/media3/common/Metadata;

    invoke-virtual {v8, v11}, Landroidx/media3/common/Metadata;->c(Landroidx/media3/common/Metadata;)Landroidx/media3/common/Metadata;

    move-result-object v8

    :goto_2
    iget v11, p0, Landroidx/media3/common/y;->t:F

    const/high16 v12, -0x40800000    # -1.0f

    cmpl-float v12, v11, v12

    if-nez v12, :cond_9

    const/4 v12, 0x2

    if-ne v0, v12, :cond_9

    iget v11, p1, Landroidx/media3/common/y;->t:F

    :cond_9
    iget v0, p0, Landroidx/media3/common/y;->e:I

    iget v12, p1, Landroidx/media3/common/y;->e:I

    or-int/2addr v0, v12

    iget v12, p0, Landroidx/media3/common/y;->f:I

    iget v13, p1, Landroidx/media3/common/y;->f:I

    or-int/2addr v12, v13

    iget-object p1, p1, Landroidx/media3/common/y;->p:Landroidx/media3/common/DrmInitData;

    iget-object v13, p0, Landroidx/media3/common/y;->p:Landroidx/media3/common/DrmInitData;

    invoke-static {p1, v13}, Landroidx/media3/common/DrmInitData;->createSessionCreationData(Landroidx/media3/common/DrmInitData;Landroidx/media3/common/DrmInitData;)Landroidx/media3/common/DrmInitData;

    move-result-object p1

    invoke-virtual {p0}, Landroidx/media3/common/y;->b()Landroidx/media3/common/y$b;

    move-result-object v13

    invoke-virtual {v13, v1}, Landroidx/media3/common/y$b;->X(Ljava/lang/String;)Landroidx/media3/common/y$b;

    move-result-object v1

    invoke-virtual {v1, v4}, Landroidx/media3/common/y$b;->Z(Ljava/lang/String;)Landroidx/media3/common/y$b;

    move-result-object v1

    invoke-virtual {v1, v5}, Landroidx/media3/common/y$b;->a0(Ljava/util/List;)Landroidx/media3/common/y$b;

    move-result-object v1

    invoke-virtual {v1, v6}, Landroidx/media3/common/y$b;->b0(Ljava/lang/String;)Landroidx/media3/common/y$b;

    move-result-object v1

    invoke-virtual {v1, v0}, Landroidx/media3/common/y$b;->m0(I)Landroidx/media3/common/y$b;

    move-result-object v0

    invoke-virtual {v0, v12}, Landroidx/media3/common/y$b;->i0(I)Landroidx/media3/common/y$b;

    move-result-object v0

    invoke-virtual {v0, v7}, Landroidx/media3/common/y$b;->K(I)Landroidx/media3/common/y$b;

    move-result-object v0

    invoke-virtual {v0, v10}, Landroidx/media3/common/y$b;->f0(I)Landroidx/media3/common/y$b;

    move-result-object v0

    invoke-virtual {v0, v9}, Landroidx/media3/common/y$b;->M(Ljava/lang/String;)Landroidx/media3/common/y$b;

    move-result-object v0

    invoke-virtual {v0, v8}, Landroidx/media3/common/y$b;->d0(Landroidx/media3/common/Metadata;)Landroidx/media3/common/y$b;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroidx/media3/common/y$b;->R(Landroidx/media3/common/DrmInitData;)Landroidx/media3/common/y$b;

    move-result-object p1

    invoke-virtual {p1, v11}, Landroidx/media3/common/y$b;->U(F)Landroidx/media3/common/y$b;

    move-result-object p1

    invoke-virtual {p1, v2}, Landroidx/media3/common/y$b;->p0(I)Landroidx/media3/common/y$b;

    move-result-object p1

    invoke-virtual {p1, v3}, Landroidx/media3/common/y$b;->q0(I)Landroidx/media3/common/y$b;

    move-result-object p1

    invoke-virtual {p1}, Landroidx/media3/common/y$b;->I()Landroidx/media3/common/y;

    move-result-object p1

    return-object p1
.end method

.method public toString()Ljava/lang/String;
    .locals 3

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "Format("

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v1, p0, Landroidx/media3/common/y;->a:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v1, ", "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v2, p0, Landroidx/media3/common/y;->b:Ljava/lang/String;

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v2, p0, Landroidx/media3/common/y;->l:Ljava/lang/String;

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v2, p0, Landroidx/media3/common/y;->m:Ljava/lang/String;

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v2, p0, Landroidx/media3/common/y;->j:Ljava/lang/String;

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget v2, p0, Landroidx/media3/common/y;->i:I

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v2, p0, Landroidx/media3/common/y;->d:Ljava/lang/String;

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v2, ", ["

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget v2, p0, Landroidx/media3/common/y;->r:I

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget v2, p0, Landroidx/media3/common/y;->s:I

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget v2, p0, Landroidx/media3/common/y;->t:F

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(F)Ljava/lang/StringBuilder;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v2, p0, Landroidx/media3/common/y;->y:Landroidx/media3/common/k;

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v2, "], ["

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget v2, p0, Landroidx/media3/common/y;->z:I

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget v1, p0, Landroidx/media3/common/y;->A:I

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v1, "])"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
