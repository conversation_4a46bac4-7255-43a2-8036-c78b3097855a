.class public interface abstract Landroidx/media3/datasource/HttpDataSource$a;
.super Ljava/lang/Object;

# interfaces
.implements Landroidx/media3/datasource/a$a;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/media3/datasource/HttpDataSource;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "a"
.end annotation


# virtual methods
.method public abstract createDataSource()Landroidx/media3/datasource/HttpDataSource;
.end method
