.class public interface abstract Landroidx/media3/datasource/cache/Cache$a;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/media3/datasource/cache/Cache;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "a"
.end annotation


# virtual methods
.method public abstract b(Landroidx/media3/datasource/cache/Cache;Landroidx/media3/datasource/cache/h;Landroidx/media3/datasource/cache/h;)V
.end method

.method public abstract c(Landroidx/media3/datasource/cache/Cache;Landroidx/media3/datasource/cache/h;)V
.end method

.method public abstract e(Landroidx/media3/datasource/cache/Cache;Landroidx/media3/datasource/cache/h;)V
.end method
