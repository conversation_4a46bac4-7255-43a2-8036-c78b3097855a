.class public interface abstract Landroidx/media3/datasource/cache/g;
.super Ljava/lang/Object;


# static fields
.field public static final a:Landroidx/media3/datasource/cache/g;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    new-instance v0, Landroidx/media3/datasource/cache/e;

    invoke-direct {v0}, Landroidx/media3/datasource/cache/e;-><init>()V

    sput-object v0, Landroidx/media3/datasource/cache/g;->a:Landroidx/media3/datasource/cache/g;

    return-void
.end method


# virtual methods
.method public abstract a(Lh2/g;)Ljava/lang/String;
.end method
