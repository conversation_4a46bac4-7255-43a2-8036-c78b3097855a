.class public interface abstract Landroidx/media3/exoplayer/audio/c;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Landroidx/media3/exoplayer/audio/c$a;
    }
.end annotation


# virtual methods
.method public abstract A(Landroidx/media3/common/y;)V
    .annotation runtime Ljava/lang/Deprecated;
    .end annotation
.end method

.method public abstract a(Ljava/lang/Exception;)V
.end method

.method public abstract c(Ljava/lang/String;)V
.end method

.method public abstract d(J)V
.end method

.method public abstract h(Ljava/lang/Exception;)V
.end method

.method public abstract i(IJJ)V
.end method

.method public abstract k(Landroidx/media3/exoplayer/audio/AudioSink$a;)V
.end method

.method public abstract l(Landroidx/media3/exoplayer/audio/AudioSink$a;)V
.end method

.method public abstract o(Landroidx/media3/exoplayer/n;)V
.end method

.method public abstract onAudioDecoderInitialized(Ljava/lang/String;JJ)V
.end method

.method public abstract onSkipSilenceEnabledChanged(Z)V
.end method

.method public abstract s(Landroidx/media3/common/y;Landroidx/media3/exoplayer/o;)V
    .param p2    # Landroidx/media3/exoplayer/o;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
.end method

.method public abstract t(Landroidx/media3/exoplayer/n;)V
.end method
