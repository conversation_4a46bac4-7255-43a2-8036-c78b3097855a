.class public abstract Landroidx/media3/exoplayer/audio/e;
.super Landroidx/media3/exoplayer/m;

# interfaces
.implements Landroidx/media3/exoplayer/y1;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Landroidx/media3/exoplayer/audio/e$c;,
        Landroidx/media3/exoplayer/audio/e$b;
    }
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "<T::",
        "Landroidx/media3/decoder/g<",
        "Landroidx/media3/decoder/DecoderInputBuffer;",
        "+",
        "Landroidx/media3/decoder/i;",
        "+",
        "Landroidx/media3/decoder/DecoderException;",
        ">;>",
        "Landroidx/media3/exoplayer/m;",
        "Landroidx/media3/exoplayer/y1;"
    }
.end annotation


# instance fields
.field public A:Landroidx/media3/decoder/DecoderInputBuffer;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public B:Landroidx/media3/decoder/i;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public C:Landroidx/media3/exoplayer/drm/DrmSession;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public D:Landroidx/media3/exoplayer/drm/DrmSession;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public E:I

.field public F:Z

.field public G:Z

.field public H:J

.field public I:Z

.field public J:Z

.field public K:Z

.field public L:J

.field public final M:[J

.field public N:I

.field public O:Z

.field public final r:Landroidx/media3/exoplayer/audio/c$a;

.field public final s:Landroidx/media3/exoplayer/audio/AudioSink;

.field public final t:Landroidx/media3/decoder/DecoderInputBuffer;

.field public u:Landroidx/media3/exoplayer/n;

.field public v:Landroidx/media3/common/y;

.field public w:I

.field public x:I

.field public y:Z

.field public z:Landroidx/media3/decoder/g;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation

    .annotation system Ldalvik/annotation/Signature;
        value = {
            "TT;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(Landroid/os/Handler;Landroidx/media3/exoplayer/audio/c;Landroidx/media3/exoplayer/audio/AudioSink;)V
    .locals 2
    .param p1    # Landroid/os/Handler;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .param p2    # Landroidx/media3/exoplayer/audio/c;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    const/4 v0, 0x1

    invoke-direct {p0, v0}, Landroidx/media3/exoplayer/m;-><init>(I)V

    new-instance v1, Landroidx/media3/exoplayer/audio/c$a;

    invoke-direct {v1, p1, p2}, Landroidx/media3/exoplayer/audio/c$a;-><init>(Landroid/os/Handler;Landroidx/media3/exoplayer/audio/c;)V

    iput-object v1, p0, Landroidx/media3/exoplayer/audio/e;->r:Landroidx/media3/exoplayer/audio/c$a;

    iput-object p3, p0, Landroidx/media3/exoplayer/audio/e;->s:Landroidx/media3/exoplayer/audio/AudioSink;

    new-instance p1, Landroidx/media3/exoplayer/audio/e$c;

    const/4 p2, 0x0

    invoke-direct {p1, p0, p2}, Landroidx/media3/exoplayer/audio/e$c;-><init>(Landroidx/media3/exoplayer/audio/e;Landroidx/media3/exoplayer/audio/e$a;)V

    invoke-interface {p3, p1}, Landroidx/media3/exoplayer/audio/AudioSink;->j(Landroidx/media3/exoplayer/audio/AudioSink$b;)V

    invoke-static {}, Landroidx/media3/decoder/DecoderInputBuffer;->g()Landroidx/media3/decoder/DecoderInputBuffer;

    move-result-object p1

    iput-object p1, p0, Landroidx/media3/exoplayer/audio/e;->t:Landroidx/media3/decoder/DecoderInputBuffer;

    const/4 p1, 0x0

    iput p1, p0, Landroidx/media3/exoplayer/audio/e;->E:I

    iput-boolean v0, p0, Landroidx/media3/exoplayer/audio/e;->G:Z

    const-wide p1, -0x7fffffffffffffffL    # -4.9E-324

    invoke-virtual {p0, p1, p2}, Landroidx/media3/exoplayer/audio/e;->e0(J)V

    const/16 p1, 0xa

    new-array p1, p1, [J

    iput-object p1, p0, Landroidx/media3/exoplayer/audio/e;->M:[J

    return-void
.end method

.method public static synthetic N(Landroidx/media3/exoplayer/audio/e;Z)Z
    .locals 0

    iput-boolean p1, p0, Landroidx/media3/exoplayer/audio/e;->O:Z

    return p1
.end method

.method public static synthetic O(Landroidx/media3/exoplayer/audio/e;)Landroidx/media3/exoplayer/audio/c$a;
    .locals 0

    iget-object p0, p0, Landroidx/media3/exoplayer/audio/e;->r:Landroidx/media3/exoplayer/audio/c$a;

    return-object p0
.end method

.method private T()V
    .locals 3
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/exoplayer/ExoPlaybackException;
        }
    .end annotation

    iget v0, p0, Landroidx/media3/exoplayer/audio/e;->E:I

    if-eqz v0, :cond_0

    invoke-direct {p0}, Landroidx/media3/exoplayer/audio/e;->c0()V

    invoke-virtual {p0}, Landroidx/media3/exoplayer/audio/e;->X()V

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    iput-object v0, p0, Landroidx/media3/exoplayer/audio/e;->A:Landroidx/media3/decoder/DecoderInputBuffer;

    iget-object v1, p0, Landroidx/media3/exoplayer/audio/e;->B:Landroidx/media3/decoder/i;

    if-eqz v1, :cond_1

    invoke-virtual {v1}, Landroidx/media3/decoder/i;->release()V

    iput-object v0, p0, Landroidx/media3/exoplayer/audio/e;->B:Landroidx/media3/decoder/i;

    :cond_1
    iget-object v0, p0, Landroidx/media3/exoplayer/audio/e;->z:Landroidx/media3/decoder/g;

    invoke-static {v0}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroidx/media3/decoder/g;

    invoke-interface {v0}, Landroidx/media3/decoder/g;->flush()V

    invoke-virtual {p0}, Landroidx/media3/exoplayer/m;->v()J

    move-result-wide v1

    invoke-interface {v0, v1, v2}, Landroidx/media3/decoder/g;->a(J)V

    const/4 v0, 0x0

    iput-boolean v0, p0, Landroidx/media3/exoplayer/audio/e;->F:Z

    :goto_0
    return-void
.end method

.method private Y(Landroidx/media3/exoplayer/t1;)V
    .locals 7
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/exoplayer/ExoPlaybackException;
        }
    .end annotation

    iget-object v0, p1, Landroidx/media3/exoplayer/t1;->b:Landroidx/media3/common/y;

    invoke-static {v0}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    move-object v4, v0

    check-cast v4, Landroidx/media3/common/y;

    iget-object p1, p1, Landroidx/media3/exoplayer/t1;->a:Landroidx/media3/exoplayer/drm/DrmSession;

    invoke-virtual {p0, p1}, Landroidx/media3/exoplayer/audio/e;->f0(Landroidx/media3/exoplayer/drm/DrmSession;)V

    iget-object v3, p0, Landroidx/media3/exoplayer/audio/e;->v:Landroidx/media3/common/y;

    iput-object v4, p0, Landroidx/media3/exoplayer/audio/e;->v:Landroidx/media3/common/y;

    iget p1, v4, Landroidx/media3/common/y;->C:I

    iput p1, p0, Landroidx/media3/exoplayer/audio/e;->w:I

    iget p1, v4, Landroidx/media3/common/y;->D:I

    iput p1, p0, Landroidx/media3/exoplayer/audio/e;->x:I

    iget-object p1, p0, Landroidx/media3/exoplayer/audio/e;->z:Landroidx/media3/decoder/g;

    if-nez p1, :cond_0

    invoke-virtual {p0}, Landroidx/media3/exoplayer/audio/e;->X()V

    iget-object p1, p0, Landroidx/media3/exoplayer/audio/e;->r:Landroidx/media3/exoplayer/audio/c$a;

    iget-object v0, p0, Landroidx/media3/exoplayer/audio/e;->v:Landroidx/media3/common/y;

    const/4 v1, 0x0

    invoke-virtual {p1, v0, v1}, Landroidx/media3/exoplayer/audio/c$a;->u(Landroidx/media3/common/y;Landroidx/media3/exoplayer/o;)V

    return-void

    :cond_0
    iget-object v0, p0, Landroidx/media3/exoplayer/audio/e;->D:Landroidx/media3/exoplayer/drm/DrmSession;

    iget-object v1, p0, Landroidx/media3/exoplayer/audio/e;->C:Landroidx/media3/exoplayer/drm/DrmSession;

    if-eq v0, v1, :cond_1

    new-instance v0, Landroidx/media3/exoplayer/o;

    invoke-interface {p1}, Landroidx/media3/decoder/g;->getName()Ljava/lang/String;

    move-result-object v2

    const/4 v5, 0x0

    const/16 v6, 0x80

    move-object v1, v0

    invoke-direct/range {v1 .. v6}, Landroidx/media3/exoplayer/o;-><init>(Ljava/lang/String;Landroidx/media3/common/y;Landroidx/media3/common/y;II)V

    goto :goto_0

    :cond_1
    invoke-interface {p1}, Landroidx/media3/decoder/g;->getName()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1, v3, v4}, Landroidx/media3/exoplayer/audio/e;->P(Ljava/lang/String;Landroidx/media3/common/y;Landroidx/media3/common/y;)Landroidx/media3/exoplayer/o;

    move-result-object v0

    :goto_0
    iget p1, v0, Landroidx/media3/exoplayer/o;->d:I

    if-nez p1, :cond_3

    iget-boolean p1, p0, Landroidx/media3/exoplayer/audio/e;->F:Z

    const/4 v1, 0x1

    if-eqz p1, :cond_2

    iput v1, p0, Landroidx/media3/exoplayer/audio/e;->E:I

    goto :goto_1

    :cond_2
    invoke-direct {p0}, Landroidx/media3/exoplayer/audio/e;->c0()V

    invoke-virtual {p0}, Landroidx/media3/exoplayer/audio/e;->X()V

    iput-boolean v1, p0, Landroidx/media3/exoplayer/audio/e;->G:Z

    :cond_3
    :goto_1
    iget-object p1, p0, Landroidx/media3/exoplayer/audio/e;->r:Landroidx/media3/exoplayer/audio/c$a;

    iget-object v1, p0, Landroidx/media3/exoplayer/audio/e;->v:Landroidx/media3/common/y;

    invoke-virtual {p1, v1, v0}, Landroidx/media3/exoplayer/audio/c$a;->u(Landroidx/media3/common/y;Landroidx/media3/exoplayer/o;)V

    return-void
.end method

.method private c0()V
    .locals 4

    const/4 v0, 0x0

    iput-object v0, p0, Landroidx/media3/exoplayer/audio/e;->A:Landroidx/media3/decoder/DecoderInputBuffer;

    iput-object v0, p0, Landroidx/media3/exoplayer/audio/e;->B:Landroidx/media3/decoder/i;

    const/4 v1, 0x0

    iput v1, p0, Landroidx/media3/exoplayer/audio/e;->E:I

    iput-boolean v1, p0, Landroidx/media3/exoplayer/audio/e;->F:Z

    iget-object v1, p0, Landroidx/media3/exoplayer/audio/e;->z:Landroidx/media3/decoder/g;

    if-eqz v1, :cond_0

    iget-object v2, p0, Landroidx/media3/exoplayer/audio/e;->u:Landroidx/media3/exoplayer/n;

    iget v3, v2, Landroidx/media3/exoplayer/n;->b:I

    add-int/lit8 v3, v3, 0x1

    iput v3, v2, Landroidx/media3/exoplayer/n;->b:I

    invoke-interface {v1}, Landroidx/media3/decoder/g;->release()V

    iget-object v1, p0, Landroidx/media3/exoplayer/audio/e;->r:Landroidx/media3/exoplayer/audio/c$a;

    iget-object v2, p0, Landroidx/media3/exoplayer/audio/e;->z:Landroidx/media3/decoder/g;

    invoke-interface {v2}, Landroidx/media3/decoder/g;->getName()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v2}, Landroidx/media3/exoplayer/audio/c$a;->r(Ljava/lang/String;)V

    iput-object v0, p0, Landroidx/media3/exoplayer/audio/e;->z:Landroidx/media3/decoder/g;

    :cond_0
    invoke-virtual {p0, v0}, Landroidx/media3/exoplayer/audio/e;->d0(Landroidx/media3/exoplayer/drm/DrmSession;)V

    return-void
.end method


# virtual methods
.method public A(ZZ)V
    .locals 0
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/exoplayer/ExoPlaybackException;
        }
    .end annotation

    new-instance p1, Landroidx/media3/exoplayer/n;

    invoke-direct {p1}, Landroidx/media3/exoplayer/n;-><init>()V

    iput-object p1, p0, Landroidx/media3/exoplayer/audio/e;->u:Landroidx/media3/exoplayer/n;

    iget-object p2, p0, Landroidx/media3/exoplayer/audio/e;->r:Landroidx/media3/exoplayer/audio/c$a;

    invoke-virtual {p2, p1}, Landroidx/media3/exoplayer/audio/c$a;->t(Landroidx/media3/exoplayer/n;)V

    invoke-virtual {p0}, Landroidx/media3/exoplayer/m;->s()Landroidx/media3/exoplayer/z2;

    move-result-object p1

    iget-boolean p1, p1, Landroidx/media3/exoplayer/z2;->b:Z

    if-eqz p1, :cond_0

    iget-object p1, p0, Landroidx/media3/exoplayer/audio/e;->s:Landroidx/media3/exoplayer/audio/AudioSink;

    invoke-interface {p1}, Landroidx/media3/exoplayer/audio/AudioSink;->e()V

    goto :goto_0

    :cond_0
    iget-object p1, p0, Landroidx/media3/exoplayer/audio/e;->s:Landroidx/media3/exoplayer/audio/AudioSink;

    invoke-interface {p1}, Landroidx/media3/exoplayer/audio/AudioSink;->disableTunneling()V

    :goto_0
    iget-object p1, p0, Landroidx/media3/exoplayer/audio/e;->s:Landroidx/media3/exoplayer/audio/AudioSink;

    invoke-virtual {p0}, Landroidx/media3/exoplayer/m;->w()Lj2/x3;

    move-result-object p2

    invoke-interface {p1, p2}, Landroidx/media3/exoplayer/audio/AudioSink;->l(Lj2/x3;)V

    iget-object p1, p0, Landroidx/media3/exoplayer/audio/e;->s:Landroidx/media3/exoplayer/audio/AudioSink;

    invoke-virtual {p0}, Landroidx/media3/exoplayer/m;->r()Le2/d;

    move-result-object p2

    invoke-interface {p1, p2}, Landroidx/media3/exoplayer/audio/AudioSink;->d(Le2/d;)V

    return-void
.end method

.method public C(JZ)V
    .locals 0
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/exoplayer/ExoPlaybackException;
        }
    .end annotation

    iget-object p3, p0, Landroidx/media3/exoplayer/audio/e;->s:Landroidx/media3/exoplayer/audio/AudioSink;

    invoke-interface {p3}, Landroidx/media3/exoplayer/audio/AudioSink;->flush()V

    iput-wide p1, p0, Landroidx/media3/exoplayer/audio/e;->H:J

    const/4 p1, 0x0

    iput-boolean p1, p0, Landroidx/media3/exoplayer/audio/e;->O:Z

    const/4 p2, 0x1

    iput-boolean p2, p0, Landroidx/media3/exoplayer/audio/e;->I:Z

    iput-boolean p1, p0, Landroidx/media3/exoplayer/audio/e;->J:Z

    iput-boolean p1, p0, Landroidx/media3/exoplayer/audio/e;->K:Z

    iget-object p1, p0, Landroidx/media3/exoplayer/audio/e;->z:Landroidx/media3/decoder/g;

    if-eqz p1, :cond_0

    invoke-direct {p0}, Landroidx/media3/exoplayer/audio/e;->T()V

    :cond_0
    return-void
.end method

.method public G()V
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/audio/e;->s:Landroidx/media3/exoplayer/audio/AudioSink;

    invoke-interface {v0}, Landroidx/media3/exoplayer/audio/AudioSink;->play()V

    return-void
.end method

.method public H()V
    .locals 1

    invoke-virtual {p0}, Landroidx/media3/exoplayer/audio/e;->i0()V

    iget-object v0, p0, Landroidx/media3/exoplayer/audio/e;->s:Landroidx/media3/exoplayer/audio/AudioSink;

    invoke-interface {v0}, Landroidx/media3/exoplayer/audio/AudioSink;->pause()V

    return-void
.end method

.method public I([Landroidx/media3/common/y;JJLandroidx/media3/exoplayer/source/l$b;)V
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/exoplayer/ExoPlaybackException;
        }
    .end annotation

    invoke-super/range {p0 .. p6}, Landroidx/media3/exoplayer/m;->I([Landroidx/media3/common/y;JJLandroidx/media3/exoplayer/source/l$b;)V

    const/4 p1, 0x0

    iput-boolean p1, p0, Landroidx/media3/exoplayer/audio/e;->y:Z

    iget-wide p1, p0, Landroidx/media3/exoplayer/audio/e;->L:J

    const-wide v0, -0x7fffffffffffffffL    # -4.9E-324

    cmp-long p3, p1, v0

    if-nez p3, :cond_0

    invoke-virtual {p0, p4, p5}, Landroidx/media3/exoplayer/audio/e;->e0(J)V

    goto :goto_1

    :cond_0
    iget p1, p0, Landroidx/media3/exoplayer/audio/e;->N:I

    iget-object p2, p0, Landroidx/media3/exoplayer/audio/e;->M:[J

    array-length p2, p2

    if-ne p1, p2, :cond_1

    new-instance p1, Ljava/lang/StringBuilder;

    invoke-direct {p1}, Ljava/lang/StringBuilder;-><init>()V

    const-string p2, "Too many stream changes, so dropping offset: "

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object p2, p0, Landroidx/media3/exoplayer/audio/e;->M:[J

    iget p3, p0, Landroidx/media3/exoplayer/audio/e;->N:I

    add-int/lit8 p3, p3, -0x1

    aget-wide v0, p2, p3

    invoke-virtual {p1, v0, v1}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    const-string p2, "DecoderAudioRenderer"

    invoke-static {p2, p1}, Le2/o;->h(Ljava/lang/String;Ljava/lang/String;)V

    goto :goto_0

    :cond_1
    add-int/lit8 p1, p1, 0x1

    iput p1, p0, Landroidx/media3/exoplayer/audio/e;->N:I

    :goto_0
    iget-object p1, p0, Landroidx/media3/exoplayer/audio/e;->M:[J

    iget p2, p0, Landroidx/media3/exoplayer/audio/e;->N:I

    add-int/lit8 p2, p2, -0x1

    aput-wide p4, p1, p2

    :goto_1
    return-void
.end method

.method public P(Ljava/lang/String;Landroidx/media3/common/y;Landroidx/media3/common/y;)Landroidx/media3/exoplayer/o;
    .locals 7

    new-instance v6, Landroidx/media3/exoplayer/o;

    const/4 v4, 0x0

    const/4 v5, 0x1

    move-object v0, v6

    move-object v1, p1

    move-object v2, p2

    move-object v3, p3

    invoke-direct/range {v0 .. v5}, Landroidx/media3/exoplayer/o;-><init>(Ljava/lang/String;Landroidx/media3/common/y;Landroidx/media3/common/y;II)V

    return-object v6
.end method

.method public abstract Q(Landroidx/media3/common/y;Landroidx/media3/decoder/b;)Landroidx/media3/decoder/g;
    .param p2    # Landroidx/media3/decoder/b;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroidx/media3/common/y;",
            "Landroidx/media3/decoder/b;",
            ")TT;"
        }
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/decoder/DecoderException;
        }
    .end annotation
.end method

.method public final R()Z
    .locals 8
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/exoplayer/ExoPlaybackException;,
            Landroidx/media3/decoder/DecoderException;,
            Landroidx/media3/exoplayer/audio/AudioSink$ConfigurationException;,
            Landroidx/media3/exoplayer/audio/AudioSink$InitializationException;,
            Landroidx/media3/exoplayer/audio/AudioSink$WriteException;
        }
    .end annotation

    iget-object v0, p0, Landroidx/media3/exoplayer/audio/e;->B:Landroidx/media3/decoder/i;

    const/4 v1, 0x0

    if-nez v0, :cond_2

    iget-object v0, p0, Landroidx/media3/exoplayer/audio/e;->z:Landroidx/media3/decoder/g;

    invoke-interface {v0}, Landroidx/media3/decoder/g;->dequeueOutputBuffer()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroidx/media3/decoder/i;

    iput-object v0, p0, Landroidx/media3/exoplayer/audio/e;->B:Landroidx/media3/decoder/i;

    if-nez v0, :cond_0

    return v1

    :cond_0
    iget v0, v0, Landroidx/media3/decoder/h;->skippedOutputBufferCount:I

    if-lez v0, :cond_1

    iget-object v2, p0, Landroidx/media3/exoplayer/audio/e;->u:Landroidx/media3/exoplayer/n;

    iget v3, v2, Landroidx/media3/exoplayer/n;->f:I

    add-int/2addr v3, v0

    iput v3, v2, Landroidx/media3/exoplayer/n;->f:I

    iget-object v0, p0, Landroidx/media3/exoplayer/audio/e;->s:Landroidx/media3/exoplayer/audio/AudioSink;

    invoke-interface {v0}, Landroidx/media3/exoplayer/audio/AudioSink;->handleDiscontinuity()V

    :cond_1
    iget-object v0, p0, Landroidx/media3/exoplayer/audio/e;->B:Landroidx/media3/decoder/i;

    invoke-virtual {v0}, Landroidx/media3/decoder/a;->isFirstSample()Z

    move-result v0

    if-eqz v0, :cond_2

    invoke-virtual {p0}, Landroidx/media3/exoplayer/audio/e;->b0()V

    :cond_2
    iget-object v0, p0, Landroidx/media3/exoplayer/audio/e;->B:Landroidx/media3/decoder/i;

    invoke-virtual {v0}, Landroidx/media3/decoder/a;->isEndOfStream()Z

    move-result v0

    const/4 v2, 0x0

    const/4 v3, 0x1

    if-eqz v0, :cond_4

    iget v0, p0, Landroidx/media3/exoplayer/audio/e;->E:I

    const/4 v4, 0x2

    if-ne v0, v4, :cond_3

    invoke-direct {p0}, Landroidx/media3/exoplayer/audio/e;->c0()V

    invoke-virtual {p0}, Landroidx/media3/exoplayer/audio/e;->X()V

    iput-boolean v3, p0, Landroidx/media3/exoplayer/audio/e;->G:Z

    goto :goto_0

    :cond_3
    iget-object v0, p0, Landroidx/media3/exoplayer/audio/e;->B:Landroidx/media3/decoder/i;

    invoke-virtual {v0}, Landroidx/media3/decoder/i;->release()V

    iput-object v2, p0, Landroidx/media3/exoplayer/audio/e;->B:Landroidx/media3/decoder/i;

    :try_start_0
    invoke-virtual {p0}, Landroidx/media3/exoplayer/audio/e;->a0()V
    :try_end_0
    .catch Landroidx/media3/exoplayer/audio/AudioSink$WriteException; {:try_start_0 .. :try_end_0} :catch_0

    :goto_0
    return v1

    :catch_0
    move-exception v0

    iget-object v1, v0, Landroidx/media3/exoplayer/audio/AudioSink$WriteException;->format:Landroidx/media3/common/y;

    iget-boolean v2, v0, Landroidx/media3/exoplayer/audio/AudioSink$WriteException;->isRecoverable:Z

    const/16 v3, 0x138a

    invoke-virtual {p0, v0, v1, v2, v3}, Landroidx/media3/exoplayer/m;->q(Ljava/lang/Throwable;Landroidx/media3/common/y;ZI)Landroidx/media3/exoplayer/ExoPlaybackException;

    move-result-object v0

    throw v0

    :cond_4
    iget-boolean v0, p0, Landroidx/media3/exoplayer/audio/e;->G:Z

    if-eqz v0, :cond_5

    iget-object v0, p0, Landroidx/media3/exoplayer/audio/e;->z:Landroidx/media3/decoder/g;

    invoke-virtual {p0, v0}, Landroidx/media3/exoplayer/audio/e;->V(Landroidx/media3/decoder/g;)Landroidx/media3/common/y;

    move-result-object v0

    invoke-virtual {v0}, Landroidx/media3/common/y;->b()Landroidx/media3/common/y$b;

    move-result-object v0

    iget v4, p0, Landroidx/media3/exoplayer/audio/e;->w:I

    invoke-virtual {v0, v4}, Landroidx/media3/common/y$b;->S(I)Landroidx/media3/common/y$b;

    move-result-object v0

    iget v4, p0, Landroidx/media3/exoplayer/audio/e;->x:I

    invoke-virtual {v0, v4}, Landroidx/media3/common/y$b;->T(I)Landroidx/media3/common/y$b;

    move-result-object v0

    iget-object v4, p0, Landroidx/media3/exoplayer/audio/e;->v:Landroidx/media3/common/y;

    iget-object v4, v4, Landroidx/media3/common/y;->k:Landroidx/media3/common/Metadata;

    invoke-virtual {v0, v4}, Landroidx/media3/common/y$b;->d0(Landroidx/media3/common/Metadata;)Landroidx/media3/common/y$b;

    move-result-object v0

    iget-object v4, p0, Landroidx/media3/exoplayer/audio/e;->v:Landroidx/media3/common/y;

    iget-object v4, v4, Landroidx/media3/common/y;->a:Ljava/lang/String;

    invoke-virtual {v0, v4}, Landroidx/media3/common/y$b;->X(Ljava/lang/String;)Landroidx/media3/common/y$b;

    move-result-object v0

    iget-object v4, p0, Landroidx/media3/exoplayer/audio/e;->v:Landroidx/media3/common/y;

    iget-object v4, v4, Landroidx/media3/common/y;->b:Ljava/lang/String;

    invoke-virtual {v0, v4}, Landroidx/media3/common/y$b;->Z(Ljava/lang/String;)Landroidx/media3/common/y$b;

    move-result-object v0

    iget-object v4, p0, Landroidx/media3/exoplayer/audio/e;->v:Landroidx/media3/common/y;

    iget-object v4, v4, Landroidx/media3/common/y;->c:Ljava/util/List;

    invoke-virtual {v0, v4}, Landroidx/media3/common/y$b;->a0(Ljava/util/List;)Landroidx/media3/common/y$b;

    move-result-object v0

    iget-object v4, p0, Landroidx/media3/exoplayer/audio/e;->v:Landroidx/media3/common/y;

    iget-object v4, v4, Landroidx/media3/common/y;->d:Ljava/lang/String;

    invoke-virtual {v0, v4}, Landroidx/media3/common/y$b;->b0(Ljava/lang/String;)Landroidx/media3/common/y$b;

    move-result-object v0

    iget-object v4, p0, Landroidx/media3/exoplayer/audio/e;->v:Landroidx/media3/common/y;

    iget v4, v4, Landroidx/media3/common/y;->e:I

    invoke-virtual {v0, v4}, Landroidx/media3/common/y$b;->m0(I)Landroidx/media3/common/y$b;

    move-result-object v0

    iget-object v4, p0, Landroidx/media3/exoplayer/audio/e;->v:Landroidx/media3/common/y;

    iget v4, v4, Landroidx/media3/common/y;->f:I

    invoke-virtual {v0, v4}, Landroidx/media3/common/y$b;->i0(I)Landroidx/media3/common/y$b;

    move-result-object v0

    invoke-virtual {v0}, Landroidx/media3/common/y$b;->I()Landroidx/media3/common/y;

    move-result-object v0

    iget-object v4, p0, Landroidx/media3/exoplayer/audio/e;->s:Landroidx/media3/exoplayer/audio/AudioSink;

    iget-object v5, p0, Landroidx/media3/exoplayer/audio/e;->z:Landroidx/media3/decoder/g;

    invoke-virtual {p0, v5}, Landroidx/media3/exoplayer/audio/e;->U(Landroidx/media3/decoder/g;)[I

    move-result-object v5

    invoke-interface {v4, v0, v1, v5}, Landroidx/media3/exoplayer/audio/AudioSink;->m(Landroidx/media3/common/y;I[I)V

    iput-boolean v1, p0, Landroidx/media3/exoplayer/audio/e;->G:Z

    :cond_5
    iget-object v0, p0, Landroidx/media3/exoplayer/audio/e;->s:Landroidx/media3/exoplayer/audio/AudioSink;

    iget-object v4, p0, Landroidx/media3/exoplayer/audio/e;->B:Landroidx/media3/decoder/i;

    iget-object v5, v4, Landroidx/media3/decoder/i;->b:Ljava/nio/ByteBuffer;

    iget-wide v6, v4, Landroidx/media3/decoder/h;->timeUs:J

    invoke-interface {v0, v5, v6, v7, v3}, Landroidx/media3/exoplayer/audio/AudioSink;->c(Ljava/nio/ByteBuffer;JI)Z

    move-result v0

    if-eqz v0, :cond_6

    iget-object v0, p0, Landroidx/media3/exoplayer/audio/e;->u:Landroidx/media3/exoplayer/n;

    iget v1, v0, Landroidx/media3/exoplayer/n;->e:I

    add-int/2addr v1, v3

    iput v1, v0, Landroidx/media3/exoplayer/n;->e:I

    iget-object v0, p0, Landroidx/media3/exoplayer/audio/e;->B:Landroidx/media3/decoder/i;

    invoke-virtual {v0}, Landroidx/media3/decoder/i;->release()V

    iput-object v2, p0, Landroidx/media3/exoplayer/audio/e;->B:Landroidx/media3/decoder/i;

    return v3

    :cond_6
    return v1
.end method

.method public final S()Z
    .locals 7
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/decoder/DecoderException;,
            Landroidx/media3/exoplayer/ExoPlaybackException;
        }
    .end annotation

    iget-object v0, p0, Landroidx/media3/exoplayer/audio/e;->z:Landroidx/media3/decoder/g;

    const/4 v1, 0x0

    if-eqz v0, :cond_9

    iget v2, p0, Landroidx/media3/exoplayer/audio/e;->E:I

    const/4 v3, 0x2

    if-eq v2, v3, :cond_9

    iget-boolean v2, p0, Landroidx/media3/exoplayer/audio/e;->J:Z

    if-eqz v2, :cond_0

    goto/16 :goto_0

    :cond_0
    iget-object v2, p0, Landroidx/media3/exoplayer/audio/e;->A:Landroidx/media3/decoder/DecoderInputBuffer;

    if-nez v2, :cond_1

    invoke-interface {v0}, Landroidx/media3/decoder/g;->dequeueInputBuffer()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroidx/media3/decoder/DecoderInputBuffer;

    iput-object v0, p0, Landroidx/media3/exoplayer/audio/e;->A:Landroidx/media3/decoder/DecoderInputBuffer;

    if-nez v0, :cond_1

    return v1

    :cond_1
    iget v0, p0, Landroidx/media3/exoplayer/audio/e;->E:I

    const/4 v2, 0x0

    const/4 v4, 0x1

    if-ne v0, v4, :cond_2

    iget-object v0, p0, Landroidx/media3/exoplayer/audio/e;->A:Landroidx/media3/decoder/DecoderInputBuffer;

    const/4 v4, 0x4

    invoke-virtual {v0, v4}, Landroidx/media3/decoder/a;->setFlags(I)V

    iget-object v0, p0, Landroidx/media3/exoplayer/audio/e;->z:Landroidx/media3/decoder/g;

    iget-object v4, p0, Landroidx/media3/exoplayer/audio/e;->A:Landroidx/media3/decoder/DecoderInputBuffer;

    invoke-interface {v0, v4}, Landroidx/media3/decoder/g;->queueInputBuffer(Ljava/lang/Object;)V

    iput-object v2, p0, Landroidx/media3/exoplayer/audio/e;->A:Landroidx/media3/decoder/DecoderInputBuffer;

    iput v3, p0, Landroidx/media3/exoplayer/audio/e;->E:I

    return v1

    :cond_2
    invoke-virtual {p0}, Landroidx/media3/exoplayer/m;->t()Landroidx/media3/exoplayer/t1;

    move-result-object v0

    iget-object v3, p0, Landroidx/media3/exoplayer/audio/e;->A:Landroidx/media3/decoder/DecoderInputBuffer;

    invoke-virtual {p0, v0, v3, v1}, Landroidx/media3/exoplayer/m;->K(Landroidx/media3/exoplayer/t1;Landroidx/media3/decoder/DecoderInputBuffer;I)I

    move-result v3

    const/4 v5, -0x5

    if-eq v3, v5, :cond_8

    const/4 v0, -0x4

    if-eq v3, v0, :cond_4

    const/4 v0, -0x3

    if-ne v3, v0, :cond_3

    return v1

    :cond_3
    new-instance v0, Ljava/lang/IllegalStateException;

    invoke-direct {v0}, Ljava/lang/IllegalStateException;-><init>()V

    throw v0

    :cond_4
    iget-object v0, p0, Landroidx/media3/exoplayer/audio/e;->A:Landroidx/media3/decoder/DecoderInputBuffer;

    invoke-virtual {v0}, Landroidx/media3/decoder/a;->isEndOfStream()Z

    move-result v0

    if-eqz v0, :cond_5

    iput-boolean v4, p0, Landroidx/media3/exoplayer/audio/e;->J:Z

    iget-object v0, p0, Landroidx/media3/exoplayer/audio/e;->z:Landroidx/media3/decoder/g;

    iget-object v3, p0, Landroidx/media3/exoplayer/audio/e;->A:Landroidx/media3/decoder/DecoderInputBuffer;

    invoke-interface {v0, v3}, Landroidx/media3/decoder/g;->queueInputBuffer(Ljava/lang/Object;)V

    iput-object v2, p0, Landroidx/media3/exoplayer/audio/e;->A:Landroidx/media3/decoder/DecoderInputBuffer;

    return v1

    :cond_5
    iget-boolean v0, p0, Landroidx/media3/exoplayer/audio/e;->y:Z

    if-nez v0, :cond_6

    iput-boolean v4, p0, Landroidx/media3/exoplayer/audio/e;->y:Z

    iget-object v0, p0, Landroidx/media3/exoplayer/audio/e;->A:Landroidx/media3/decoder/DecoderInputBuffer;

    const/high16 v1, 0x8000000

    invoke-virtual {v0, v1}, Landroidx/media3/decoder/a;->addFlag(I)V

    :cond_6
    iget-object v0, p0, Landroidx/media3/exoplayer/audio/e;->A:Landroidx/media3/decoder/DecoderInputBuffer;

    iget-wide v0, v0, Landroidx/media3/decoder/DecoderInputBuffer;->e:J

    invoke-virtual {p0}, Landroidx/media3/exoplayer/m;->v()J

    move-result-wide v5

    cmp-long v3, v0, v5

    if-gez v3, :cond_7

    iget-object v0, p0, Landroidx/media3/exoplayer/audio/e;->A:Landroidx/media3/decoder/DecoderInputBuffer;

    const/high16 v1, -0x80000000

    invoke-virtual {v0, v1}, Landroidx/media3/decoder/a;->addFlag(I)V

    :cond_7
    iget-object v0, p0, Landroidx/media3/exoplayer/audio/e;->A:Landroidx/media3/decoder/DecoderInputBuffer;

    invoke-virtual {v0}, Landroidx/media3/decoder/DecoderInputBuffer;->e()V

    iget-object v0, p0, Landroidx/media3/exoplayer/audio/e;->A:Landroidx/media3/decoder/DecoderInputBuffer;

    iget-object v1, p0, Landroidx/media3/exoplayer/audio/e;->v:Landroidx/media3/common/y;

    iput-object v1, v0, Landroidx/media3/decoder/DecoderInputBuffer;->a:Landroidx/media3/common/y;

    iget-object v1, p0, Landroidx/media3/exoplayer/audio/e;->z:Landroidx/media3/decoder/g;

    invoke-interface {v1, v0}, Landroidx/media3/decoder/g;->queueInputBuffer(Ljava/lang/Object;)V

    iput-boolean v4, p0, Landroidx/media3/exoplayer/audio/e;->F:Z

    iget-object v0, p0, Landroidx/media3/exoplayer/audio/e;->u:Landroidx/media3/exoplayer/n;

    iget v1, v0, Landroidx/media3/exoplayer/n;->c:I

    add-int/2addr v1, v4

    iput v1, v0, Landroidx/media3/exoplayer/n;->c:I

    iput-object v2, p0, Landroidx/media3/exoplayer/audio/e;->A:Landroidx/media3/decoder/DecoderInputBuffer;

    return v4

    :cond_8
    invoke-direct {p0, v0}, Landroidx/media3/exoplayer/audio/e;->Y(Landroidx/media3/exoplayer/t1;)V

    return v4

    :cond_9
    :goto_0
    return v1
.end method

.method public U(Landroidx/media3/decoder/g;)[I
    .locals 0
    .annotation build Landroidx/annotation/Nullable;
    .end annotation

    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(TT;)[I"
        }
    .end annotation

    const/4 p1, 0x0

    return-object p1
.end method

.method public abstract V(Landroidx/media3/decoder/g;)Landroidx/media3/common/y;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(TT;)",
            "Landroidx/media3/common/y;"
        }
    .end annotation
.end method

.method public final W(Landroidx/media3/common/y;)I
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/audio/e;->s:Landroidx/media3/exoplayer/audio/AudioSink;

    invoke-interface {v0, p1}, Landroidx/media3/exoplayer/audio/AudioSink;->p(Landroidx/media3/common/y;)I

    move-result p1

    return p1
.end method

.method public final X()V
    .locals 12
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/exoplayer/ExoPlaybackException;
        }
    .end annotation

    iget-object v0, p0, Landroidx/media3/exoplayer/audio/e;->z:Landroidx/media3/decoder/g;

    if-eqz v0, :cond_0

    return-void

    :cond_0
    iget-object v0, p0, Landroidx/media3/exoplayer/audio/e;->D:Landroidx/media3/exoplayer/drm/DrmSession;

    invoke-virtual {p0, v0}, Landroidx/media3/exoplayer/audio/e;->d0(Landroidx/media3/exoplayer/drm/DrmSession;)V

    iget-object v0, p0, Landroidx/media3/exoplayer/audio/e;->C:Landroidx/media3/exoplayer/drm/DrmSession;

    if-eqz v0, :cond_2

    invoke-interface {v0}, Landroidx/media3/exoplayer/drm/DrmSession;->c()Landroidx/media3/decoder/b;

    move-result-object v0

    if-nez v0, :cond_3

    iget-object v1, p0, Landroidx/media3/exoplayer/audio/e;->C:Landroidx/media3/exoplayer/drm/DrmSession;

    invoke-interface {v1}, Landroidx/media3/exoplayer/drm/DrmSession;->getError()Landroidx/media3/exoplayer/drm/DrmSession$DrmSessionException;

    move-result-object v1

    if-eqz v1, :cond_1

    goto :goto_0

    :cond_1
    return-void

    :cond_2
    const/4 v0, 0x0

    :cond_3
    :goto_0
    const/16 v1, 0xfa1

    :try_start_0
    invoke-static {}, Landroid/os/SystemClock;->elapsedRealtime()J

    move-result-wide v2

    const-string v4, "createAudioDecoder"

    invoke-static {v4}, Le2/j0;->a(Ljava/lang/String;)V

    iget-object v4, p0, Landroidx/media3/exoplayer/audio/e;->v:Landroidx/media3/common/y;

    invoke-virtual {p0, v4, v0}, Landroidx/media3/exoplayer/audio/e;->Q(Landroidx/media3/common/y;Landroidx/media3/decoder/b;)Landroidx/media3/decoder/g;

    move-result-object v0

    iput-object v0, p0, Landroidx/media3/exoplayer/audio/e;->z:Landroidx/media3/decoder/g;

    invoke-virtual {p0}, Landroidx/media3/exoplayer/m;->v()J

    move-result-wide v4

    invoke-interface {v0, v4, v5}, Landroidx/media3/decoder/g;->a(J)V

    invoke-static {}, Le2/j0;->c()V

    invoke-static {}, Landroid/os/SystemClock;->elapsedRealtime()J

    move-result-wide v8

    iget-object v6, p0, Landroidx/media3/exoplayer/audio/e;->r:Landroidx/media3/exoplayer/audio/c$a;

    iget-object v0, p0, Landroidx/media3/exoplayer/audio/e;->z:Landroidx/media3/decoder/g;

    invoke-interface {v0}, Landroidx/media3/decoder/g;->getName()Ljava/lang/String;

    move-result-object v7

    sub-long v10, v8, v2

    invoke-virtual/range {v6 .. v11}, Landroidx/media3/exoplayer/audio/c$a;->q(Ljava/lang/String;JJ)V

    iget-object v0, p0, Landroidx/media3/exoplayer/audio/e;->u:Landroidx/media3/exoplayer/n;

    iget v2, v0, Landroidx/media3/exoplayer/n;->a:I

    add-int/lit8 v2, v2, 0x1

    iput v2, v0, Landroidx/media3/exoplayer/n;->a:I
    :try_end_0
    .catch Landroidx/media3/decoder/DecoderException; {:try_start_0 .. :try_end_0} :catch_1
    .catch Ljava/lang/OutOfMemoryError; {:try_start_0 .. :try_end_0} :catch_0

    return-void

    :catch_0
    move-exception v0

    goto :goto_1

    :catch_1
    move-exception v0

    goto :goto_2

    :goto_1
    iget-object v2, p0, Landroidx/media3/exoplayer/audio/e;->v:Landroidx/media3/common/y;

    invoke-virtual {p0, v0, v2, v1}, Landroidx/media3/exoplayer/m;->p(Ljava/lang/Throwable;Landroidx/media3/common/y;I)Landroidx/media3/exoplayer/ExoPlaybackException;

    move-result-object v0

    throw v0

    :goto_2
    const-string v2, "DecoderAudioRenderer"

    const-string v3, "Audio codec error"

    invoke-static {v2, v3, v0}, Le2/o;->d(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Throwable;)V

    iget-object v2, p0, Landroidx/media3/exoplayer/audio/e;->r:Landroidx/media3/exoplayer/audio/c$a;

    invoke-virtual {v2, v0}, Landroidx/media3/exoplayer/audio/c$a;->m(Ljava/lang/Exception;)V

    iget-object v2, p0, Landroidx/media3/exoplayer/audio/e;->v:Landroidx/media3/common/y;

    invoke-virtual {p0, v0, v2, v1}, Landroidx/media3/exoplayer/m;->p(Ljava/lang/Throwable;Landroidx/media3/common/y;I)Landroidx/media3/exoplayer/ExoPlaybackException;

    move-result-object v0

    throw v0
.end method

.method public Z()V
    .locals 1
    .annotation build Landroidx/annotation/CallSuper;
    .end annotation

    const/4 v0, 0x1

    iput-boolean v0, p0, Landroidx/media3/exoplayer/audio/e;->I:Z

    return-void
.end method

.method public final a(Landroidx/media3/common/y;)I
    .locals 3

    iget-object v0, p1, Landroidx/media3/common/y;->m:Ljava/lang/String;

    invoke-static {v0}, Landroidx/media3/common/f0;->o(Ljava/lang/String;)Z

    move-result v0

    const/4 v1, 0x0

    if-nez v0, :cond_0

    invoke-static {v1}, Landroidx/media3/exoplayer/x2;->a(I)I

    move-result p1

    return p1

    :cond_0
    invoke-virtual {p0, p1}, Landroidx/media3/exoplayer/audio/e;->h0(Landroidx/media3/common/y;)I

    move-result p1

    const/4 v0, 0x2

    if-gt p1, v0, :cond_1

    invoke-static {p1}, Landroidx/media3/exoplayer/x2;->a(I)I

    move-result p1

    return p1

    :cond_1
    sget v0, Le2/u0;->a:I

    const/16 v2, 0x15

    if-lt v0, v2, :cond_2

    const/16 v1, 0x20

    :cond_2
    const/16 v0, 0x8

    invoke-static {p1, v0, v1}, Landroidx/media3/exoplayer/x2;->b(III)I

    move-result p1

    return p1
.end method

.method public final a0()V
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/exoplayer/audio/AudioSink$WriteException;
        }
    .end annotation

    const/4 v0, 0x1

    iput-boolean v0, p0, Landroidx/media3/exoplayer/audio/e;->K:Z

    iget-object v0, p0, Landroidx/media3/exoplayer/audio/e;->s:Landroidx/media3/exoplayer/audio/AudioSink;

    invoke-interface {v0}, Landroidx/media3/exoplayer/audio/AudioSink;->playToEndOfStream()V

    return-void
.end method

.method public b(Landroidx/media3/common/g0;)V
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/audio/e;->s:Landroidx/media3/exoplayer/audio/AudioSink;

    invoke-interface {v0, p1}, Landroidx/media3/exoplayer/audio/AudioSink;->b(Landroidx/media3/common/g0;)V

    return-void
.end method

.method public final b0()V
    .locals 4

    iget-object v0, p0, Landroidx/media3/exoplayer/audio/e;->s:Landroidx/media3/exoplayer/audio/AudioSink;

    invoke-interface {v0}, Landroidx/media3/exoplayer/audio/AudioSink;->handleDiscontinuity()V

    iget v0, p0, Landroidx/media3/exoplayer/audio/e;->N:I

    if-eqz v0, :cond_0

    iget-object v0, p0, Landroidx/media3/exoplayer/audio/e;->M:[J

    const/4 v1, 0x0

    aget-wide v2, v0, v1

    invoke-virtual {p0, v2, v3}, Landroidx/media3/exoplayer/audio/e;->e0(J)V

    iget v0, p0, Landroidx/media3/exoplayer/audio/e;->N:I

    const/4 v2, 0x1

    sub-int/2addr v0, v2

    iput v0, p0, Landroidx/media3/exoplayer/audio/e;->N:I

    iget-object v3, p0, Landroidx/media3/exoplayer/audio/e;->M:[J

    invoke-static {v3, v2, v3, v1, v0}, Ljava/lang/System;->arraycopy(Ljava/lang/Object;ILjava/lang/Object;II)V

    :cond_0
    return-void
.end method

.method public final d0(Landroidx/media3/exoplayer/drm/DrmSession;)V
    .locals 1
    .param p1    # Landroidx/media3/exoplayer/drm/DrmSession;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    iget-object v0, p0, Landroidx/media3/exoplayer/audio/e;->C:Landroidx/media3/exoplayer/drm/DrmSession;

    invoke-static {v0, p1}, Ln2/j;->a(Landroidx/media3/exoplayer/drm/DrmSession;Landroidx/media3/exoplayer/drm/DrmSession;)V

    iput-object p1, p0, Landroidx/media3/exoplayer/audio/e;->C:Landroidx/media3/exoplayer/drm/DrmSession;

    return-void
.end method

.method public final e0(J)V
    .locals 3

    iput-wide p1, p0, Landroidx/media3/exoplayer/audio/e;->L:J

    const-wide v0, -0x7fffffffffffffffL    # -4.9E-324

    cmp-long v2, p1, v0

    if-eqz v2, :cond_0

    iget-object v0, p0, Landroidx/media3/exoplayer/audio/e;->s:Landroidx/media3/exoplayer/audio/AudioSink;

    invoke-interface {v0, p1, p2}, Landroidx/media3/exoplayer/audio/AudioSink;->o(J)V

    :cond_0
    return-void
.end method

.method public final f0(Landroidx/media3/exoplayer/drm/DrmSession;)V
    .locals 1
    .param p1    # Landroidx/media3/exoplayer/drm/DrmSession;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    iget-object v0, p0, Landroidx/media3/exoplayer/audio/e;->D:Landroidx/media3/exoplayer/drm/DrmSession;

    invoke-static {v0, p1}, Ln2/j;->a(Landroidx/media3/exoplayer/drm/DrmSession;Landroidx/media3/exoplayer/drm/DrmSession;)V

    iput-object p1, p0, Landroidx/media3/exoplayer/audio/e;->D:Landroidx/media3/exoplayer/drm/DrmSession;

    return-void
.end method

.method public g()Z
    .locals 2

    iget-boolean v0, p0, Landroidx/media3/exoplayer/audio/e;->O:Z

    const/4 v1, 0x0

    iput-boolean v1, p0, Landroidx/media3/exoplayer/audio/e;->O:Z

    return v0
.end method

.method public final g0(Landroidx/media3/common/y;)Z
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/audio/e;->s:Landroidx/media3/exoplayer/audio/AudioSink;

    invoke-interface {v0, p1}, Landroidx/media3/exoplayer/audio/AudioSink;->a(Landroidx/media3/common/y;)Z

    move-result p1

    return p1
.end method

.method public getMediaClock()Landroidx/media3/exoplayer/y1;
    .locals 0
    .annotation build Landroidx/annotation/Nullable;
    .end annotation

    return-object p0
.end method

.method public getPlaybackParameters()Landroidx/media3/common/g0;
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/audio/e;->s:Landroidx/media3/exoplayer/audio/AudioSink;

    invoke-interface {v0}, Landroidx/media3/exoplayer/audio/AudioSink;->getPlaybackParameters()Landroidx/media3/common/g0;

    move-result-object v0

    return-object v0
.end method

.method public getPositionUs()J
    .locals 2

    invoke-virtual {p0}, Landroidx/media3/exoplayer/m;->getState()I

    move-result v0

    const/4 v1, 0x2

    if-ne v0, v1, :cond_0

    invoke-virtual {p0}, Landroidx/media3/exoplayer/audio/e;->i0()V

    :cond_0
    iget-wide v0, p0, Landroidx/media3/exoplayer/audio/e;->H:J

    return-wide v0
.end method

.method public abstract h0(Landroidx/media3/common/y;)I
.end method

.method public handleMessage(ILjava/lang/Object;)V
    .locals 1
    .param p2    # Ljava/lang/Object;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/exoplayer/ExoPlaybackException;
        }
    .end annotation

    const/4 v0, 0x2

    if-eq p1, v0, :cond_5

    const/4 v0, 0x3

    if-eq p1, v0, :cond_4

    const/4 v0, 0x6

    if-eq p1, v0, :cond_3

    const/16 v0, 0xc

    if-eq p1, v0, :cond_2

    const/16 v0, 0x9

    if-eq p1, v0, :cond_1

    const/16 v0, 0xa

    if-eq p1, v0, :cond_0

    invoke-super {p0, p1, p2}, Landroidx/media3/exoplayer/m;->handleMessage(ILjava/lang/Object;)V

    goto :goto_0

    :cond_0
    iget-object p1, p0, Landroidx/media3/exoplayer/audio/e;->s:Landroidx/media3/exoplayer/audio/AudioSink;

    check-cast p2, Ljava/lang/Integer;

    invoke-virtual {p2}, Ljava/lang/Integer;->intValue()I

    move-result p2

    invoke-interface {p1, p2}, Landroidx/media3/exoplayer/audio/AudioSink;->setAudioSessionId(I)V

    goto :goto_0

    :cond_1
    iget-object p1, p0, Landroidx/media3/exoplayer/audio/e;->s:Landroidx/media3/exoplayer/audio/AudioSink;

    check-cast p2, Ljava/lang/Boolean;

    invoke-virtual {p2}, Ljava/lang/Boolean;->booleanValue()Z

    move-result p2

    invoke-interface {p1, p2}, Landroidx/media3/exoplayer/audio/AudioSink;->f(Z)V

    goto :goto_0

    :cond_2
    sget p1, Le2/u0;->a:I

    const/16 v0, 0x17

    if-lt p1, v0, :cond_6

    iget-object p1, p0, Landroidx/media3/exoplayer/audio/e;->s:Landroidx/media3/exoplayer/audio/AudioSink;

    invoke-static {p1, p2}, Landroidx/media3/exoplayer/audio/e$b;->a(Landroidx/media3/exoplayer/audio/AudioSink;Ljava/lang/Object;)V

    goto :goto_0

    :cond_3
    check-cast p2, Landroidx/media3/common/g;

    iget-object p1, p0, Landroidx/media3/exoplayer/audio/e;->s:Landroidx/media3/exoplayer/audio/AudioSink;

    invoke-interface {p1, p2}, Landroidx/media3/exoplayer/audio/AudioSink;->q(Landroidx/media3/common/g;)V

    goto :goto_0

    :cond_4
    check-cast p2, Landroidx/media3/common/d;

    iget-object p1, p0, Landroidx/media3/exoplayer/audio/e;->s:Landroidx/media3/exoplayer/audio/AudioSink;

    invoke-interface {p1, p2}, Landroidx/media3/exoplayer/audio/AudioSink;->g(Landroidx/media3/common/d;)V

    goto :goto_0

    :cond_5
    iget-object p1, p0, Landroidx/media3/exoplayer/audio/e;->s:Landroidx/media3/exoplayer/audio/AudioSink;

    check-cast p2, Ljava/lang/Float;

    invoke-virtual {p2}, Ljava/lang/Float;->floatValue()F

    move-result p2

    invoke-interface {p1, p2}, Landroidx/media3/exoplayer/audio/AudioSink;->setVolume(F)V

    :cond_6
    :goto_0
    return-void
.end method

.method public final i0()V
    .locals 5

    iget-object v0, p0, Landroidx/media3/exoplayer/audio/e;->s:Landroidx/media3/exoplayer/audio/AudioSink;

    invoke-virtual {p0}, Landroidx/media3/exoplayer/audio/e;->isEnded()Z

    move-result v1

    invoke-interface {v0, v1}, Landroidx/media3/exoplayer/audio/AudioSink;->getCurrentPositionUs(Z)J

    move-result-wide v0

    const-wide/high16 v2, -0x8000000000000000L

    cmp-long v4, v0, v2

    if-eqz v4, :cond_1

    iget-boolean v2, p0, Landroidx/media3/exoplayer/audio/e;->I:Z

    if-eqz v2, :cond_0

    goto :goto_0

    :cond_0
    iget-wide v2, p0, Landroidx/media3/exoplayer/audio/e;->H:J

    invoke-static {v2, v3, v0, v1}, Ljava/lang/Math;->max(JJ)J

    move-result-wide v0

    :goto_0
    iput-wide v0, p0, Landroidx/media3/exoplayer/audio/e;->H:J

    const/4 v0, 0x0

    iput-boolean v0, p0, Landroidx/media3/exoplayer/audio/e;->I:Z

    :cond_1
    return-void
.end method

.method public isEnded()Z
    .locals 1

    iget-boolean v0, p0, Landroidx/media3/exoplayer/audio/e;->K:Z

    if-eqz v0, :cond_0

    iget-object v0, p0, Landroidx/media3/exoplayer/audio/e;->s:Landroidx/media3/exoplayer/audio/AudioSink;

    invoke-interface {v0}, Landroidx/media3/exoplayer/audio/AudioSink;->isEnded()Z

    move-result v0

    if-eqz v0, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method public isReady()Z
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/audio/e;->s:Landroidx/media3/exoplayer/audio/AudioSink;

    invoke-interface {v0}, Landroidx/media3/exoplayer/audio/AudioSink;->hasPendingData()Z

    move-result v0

    if-nez v0, :cond_1

    iget-object v0, p0, Landroidx/media3/exoplayer/audio/e;->v:Landroidx/media3/common/y;

    if-eqz v0, :cond_0

    invoke-virtual {p0}, Landroidx/media3/exoplayer/m;->y()Z

    move-result v0

    if-nez v0, :cond_1

    iget-object v0, p0, Landroidx/media3/exoplayer/audio/e;->B:Landroidx/media3/decoder/i;

    if-eqz v0, :cond_0

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 v0, 0x1

    :goto_1
    return v0
.end method

.method public render(JJ)V
    .locals 0
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/exoplayer/ExoPlaybackException;
        }
    .end annotation

    iget-boolean p1, p0, Landroidx/media3/exoplayer/audio/e;->K:Z

    const/16 p2, 0x138a

    if-eqz p1, :cond_0

    :try_start_0
    iget-object p1, p0, Landroidx/media3/exoplayer/audio/e;->s:Landroidx/media3/exoplayer/audio/AudioSink;

    invoke-interface {p1}, Landroidx/media3/exoplayer/audio/AudioSink;->playToEndOfStream()V
    :try_end_0
    .catch Landroidx/media3/exoplayer/audio/AudioSink$WriteException; {:try_start_0 .. :try_end_0} :catch_0

    return-void

    :catch_0
    move-exception p1

    iget-object p3, p1, Landroidx/media3/exoplayer/audio/AudioSink$WriteException;->format:Landroidx/media3/common/y;

    iget-boolean p4, p1, Landroidx/media3/exoplayer/audio/AudioSink$WriteException;->isRecoverable:Z

    invoke-virtual {p0, p1, p3, p4, p2}, Landroidx/media3/exoplayer/m;->q(Ljava/lang/Throwable;Landroidx/media3/common/y;ZI)Landroidx/media3/exoplayer/ExoPlaybackException;

    move-result-object p1

    throw p1

    :cond_0
    iget-object p1, p0, Landroidx/media3/exoplayer/audio/e;->v:Landroidx/media3/common/y;

    if-nez p1, :cond_3

    invoke-virtual {p0}, Landroidx/media3/exoplayer/m;->t()Landroidx/media3/exoplayer/t1;

    move-result-object p1

    iget-object p3, p0, Landroidx/media3/exoplayer/audio/e;->t:Landroidx/media3/decoder/DecoderInputBuffer;

    invoke-virtual {p3}, Landroidx/media3/decoder/DecoderInputBuffer;->clear()V

    iget-object p3, p0, Landroidx/media3/exoplayer/audio/e;->t:Landroidx/media3/decoder/DecoderInputBuffer;

    const/4 p4, 0x2

    invoke-virtual {p0, p1, p3, p4}, Landroidx/media3/exoplayer/m;->K(Landroidx/media3/exoplayer/t1;Landroidx/media3/decoder/DecoderInputBuffer;I)I

    move-result p3

    const/4 p4, -0x5

    if-ne p3, p4, :cond_1

    invoke-direct {p0, p1}, Landroidx/media3/exoplayer/audio/e;->Y(Landroidx/media3/exoplayer/t1;)V

    goto :goto_0

    :cond_1
    const/4 p1, -0x4

    if-ne p3, p1, :cond_2

    iget-object p1, p0, Landroidx/media3/exoplayer/audio/e;->t:Landroidx/media3/decoder/DecoderInputBuffer;

    invoke-virtual {p1}, Landroidx/media3/decoder/a;->isEndOfStream()Z

    move-result p1

    invoke-static {p1}, Le2/a;->g(Z)V

    const/4 p1, 0x1

    iput-boolean p1, p0, Landroidx/media3/exoplayer/audio/e;->J:Z

    :try_start_1
    invoke-virtual {p0}, Landroidx/media3/exoplayer/audio/e;->a0()V
    :try_end_1
    .catch Landroidx/media3/exoplayer/audio/AudioSink$WriteException; {:try_start_1 .. :try_end_1} :catch_1

    return-void

    :catch_1
    move-exception p1

    const/4 p3, 0x0

    invoke-virtual {p0, p1, p3, p2}, Landroidx/media3/exoplayer/m;->p(Ljava/lang/Throwable;Landroidx/media3/common/y;I)Landroidx/media3/exoplayer/ExoPlaybackException;

    move-result-object p1

    throw p1

    :cond_2
    return-void

    :cond_3
    :goto_0
    invoke-virtual {p0}, Landroidx/media3/exoplayer/audio/e;->X()V

    iget-object p1, p0, Landroidx/media3/exoplayer/audio/e;->z:Landroidx/media3/decoder/g;

    if-eqz p1, :cond_6

    const/16 p1, 0x1389

    :try_start_2
    const-string p3, "drainAndFeed"

    invoke-static {p3}, Le2/j0;->a(Ljava/lang/String;)V

    :goto_1
    invoke-virtual {p0}, Landroidx/media3/exoplayer/audio/e;->R()Z

    move-result p3

    if-eqz p3, :cond_4

    goto :goto_1

    :cond_4
    :goto_2
    invoke-virtual {p0}, Landroidx/media3/exoplayer/audio/e;->S()Z

    move-result p3

    if-eqz p3, :cond_5

    goto :goto_2

    :cond_5
    invoke-static {}, Le2/j0;->c()V
    :try_end_2
    .catch Landroidx/media3/decoder/DecoderException; {:try_start_2 .. :try_end_2} :catch_5
    .catch Landroidx/media3/exoplayer/audio/AudioSink$ConfigurationException; {:try_start_2 .. :try_end_2} :catch_4
    .catch Landroidx/media3/exoplayer/audio/AudioSink$InitializationException; {:try_start_2 .. :try_end_2} :catch_3
    .catch Landroidx/media3/exoplayer/audio/AudioSink$WriteException; {:try_start_2 .. :try_end_2} :catch_2

    iget-object p1, p0, Landroidx/media3/exoplayer/audio/e;->u:Landroidx/media3/exoplayer/n;

    invoke-virtual {p1}, Landroidx/media3/exoplayer/n;->c()V

    goto :goto_7

    :catch_2
    move-exception p1

    goto :goto_3

    :catch_3
    move-exception p2

    goto :goto_4

    :catch_4
    move-exception p2

    goto :goto_5

    :catch_5
    move-exception p1

    goto :goto_6

    :goto_3
    iget-object p3, p1, Landroidx/media3/exoplayer/audio/AudioSink$WriteException;->format:Landroidx/media3/common/y;

    iget-boolean p4, p1, Landroidx/media3/exoplayer/audio/AudioSink$WriteException;->isRecoverable:Z

    invoke-virtual {p0, p1, p3, p4, p2}, Landroidx/media3/exoplayer/m;->q(Ljava/lang/Throwable;Landroidx/media3/common/y;ZI)Landroidx/media3/exoplayer/ExoPlaybackException;

    move-result-object p1

    throw p1

    :goto_4
    iget-object p3, p2, Landroidx/media3/exoplayer/audio/AudioSink$InitializationException;->format:Landroidx/media3/common/y;

    iget-boolean p4, p2, Landroidx/media3/exoplayer/audio/AudioSink$InitializationException;->isRecoverable:Z

    invoke-virtual {p0, p2, p3, p4, p1}, Landroidx/media3/exoplayer/m;->q(Ljava/lang/Throwable;Landroidx/media3/common/y;ZI)Landroidx/media3/exoplayer/ExoPlaybackException;

    move-result-object p1

    throw p1

    :goto_5
    iget-object p3, p2, Landroidx/media3/exoplayer/audio/AudioSink$ConfigurationException;->format:Landroidx/media3/common/y;

    invoke-virtual {p0, p2, p3, p1}, Landroidx/media3/exoplayer/m;->p(Ljava/lang/Throwable;Landroidx/media3/common/y;I)Landroidx/media3/exoplayer/ExoPlaybackException;

    move-result-object p1

    throw p1

    :goto_6
    const-string p2, "DecoderAudioRenderer"

    const-string p3, "Audio codec error"

    invoke-static {p2, p3, p1}, Le2/o;->d(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Throwable;)V

    iget-object p2, p0, Landroidx/media3/exoplayer/audio/e;->r:Landroidx/media3/exoplayer/audio/c$a;

    invoke-virtual {p2, p1}, Landroidx/media3/exoplayer/audio/c$a;->m(Ljava/lang/Exception;)V

    iget-object p2, p0, Landroidx/media3/exoplayer/audio/e;->v:Landroidx/media3/common/y;

    const/16 p3, 0xfa3

    invoke-virtual {p0, p1, p2, p3}, Landroidx/media3/exoplayer/m;->p(Ljava/lang/Throwable;Landroidx/media3/common/y;I)Landroidx/media3/exoplayer/ExoPlaybackException;

    move-result-object p1

    throw p1

    :cond_6
    :goto_7
    return-void
.end method

.method public z()V
    .locals 3

    const/4 v0, 0x0

    iput-object v0, p0, Landroidx/media3/exoplayer/audio/e;->v:Landroidx/media3/common/y;

    const/4 v1, 0x1

    iput-boolean v1, p0, Landroidx/media3/exoplayer/audio/e;->G:Z

    const-wide v1, -0x7fffffffffffffffL    # -4.9E-324

    invoke-virtual {p0, v1, v2}, Landroidx/media3/exoplayer/audio/e;->e0(J)V

    const/4 v1, 0x0

    iput-boolean v1, p0, Landroidx/media3/exoplayer/audio/e;->O:Z

    :try_start_0
    invoke-virtual {p0, v0}, Landroidx/media3/exoplayer/audio/e;->f0(Landroidx/media3/exoplayer/drm/DrmSession;)V

    invoke-direct {p0}, Landroidx/media3/exoplayer/audio/e;->c0()V

    iget-object v0, p0, Landroidx/media3/exoplayer/audio/e;->s:Landroidx/media3/exoplayer/audio/AudioSink;

    invoke-interface {v0}, Landroidx/media3/exoplayer/audio/AudioSink;->reset()V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    iget-object v0, p0, Landroidx/media3/exoplayer/audio/e;->r:Landroidx/media3/exoplayer/audio/c$a;

    iget-object v1, p0, Landroidx/media3/exoplayer/audio/e;->u:Landroidx/media3/exoplayer/n;

    invoke-virtual {v0, v1}, Landroidx/media3/exoplayer/audio/c$a;->s(Landroidx/media3/exoplayer/n;)V

    return-void

    :catchall_0
    move-exception v0

    iget-object v1, p0, Landroidx/media3/exoplayer/audio/e;->r:Landroidx/media3/exoplayer/audio/c$a;

    iget-object v2, p0, Landroidx/media3/exoplayer/audio/e;->u:Landroidx/media3/exoplayer/n;

    invoke-virtual {v1, v2}, Landroidx/media3/exoplayer/audio/c$a;->s(Landroidx/media3/exoplayer/n;)V

    throw v0
.end method
