.class public final Landroidx/media3/exoplayer/c1$c;
.super Ljava/lang/Object;


# annotations
.annotation build Landroidx/annotation/RequiresApi;
    value = 0x1f
.end annotation

.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/media3/exoplayer/c1;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "c"
.end annotation


# direct methods
.method public static a(Landroid/content/Context;Landroidx/media3/exoplayer/c1;Z)Lj2/x3;
    .locals 0

    invoke-static {p0}, Lj2/v3;->y0(Landroid/content/Context;)Lj2/v3;

    move-result-object p0

    if-nez p0, :cond_0

    const-string p0, "ExoPlayerImpl"

    const-string p1, "MediaMetricsService unavailable."

    invoke-static {p0, p1}, Le2/o;->h(Ljava/lang/String;Ljava/lang/String;)V

    new-instance p0, Lj2/x3;

    invoke-static {}, Landroidx/media3/exoplayer/f1;->a()Landroid/media/metrics/LogSessionId;

    move-result-object p1

    invoke-direct {p0, p1}, Lj2/x3;-><init>(Landroid/media/metrics/LogSessionId;)V

    return-object p0

    :cond_0
    if-eqz p2, :cond_1

    invoke-virtual {p1, p0}, Landroidx/media3/exoplayer/c1;->L(Lj2/c;)V

    :cond_1
    new-instance p1, Lj2/x3;

    invoke-virtual {p0}, Lj2/v3;->F0()Landroid/media/metrics/LogSessionId;

    move-result-object p0

    invoke-direct {p1, p0}, Lj2/x3;-><init>(Landroid/media/metrics/LogSessionId;)V

    return-object p1
.end method
