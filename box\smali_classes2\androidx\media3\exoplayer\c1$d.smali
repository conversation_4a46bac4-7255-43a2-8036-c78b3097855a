.class public final Landroidx/media3/exoplayer/c1$d;
.super Ljava/lang/Object;

# interfaces
.implements Landroidx/media3/exoplayer/video/f0;
.implements Landroidx/media3/exoplayer/audio/c;
.implements Lw2/h;
.implements Ls2/b;
.implements Landroid/view/SurfaceHolder$Callback;
.implements Landroid/view/TextureView$SurfaceTextureListener;
.implements Landroidx/media3/exoplayer/video/spherical/SphericalGLSurfaceView$b;
.implements Landroidx/media3/exoplayer/l$b;
.implements Landroidx/media3/exoplayer/AudioBecomingNoisyManager$a;
.implements Landroidx/media3/exoplayer/f3$b;
.implements Landroidx/media3/exoplayer/u$a;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/media3/exoplayer/c1;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x11
    name = "d"
.end annotation


# instance fields
.field public final synthetic a:Landroidx/media3/exoplayer/c1;


# direct methods
.method public constructor <init>(Landroidx/media3/exoplayer/c1;)V
    .locals 0

    iput-object p1, p0, Landroidx/media3/exoplayer/c1$d;->a:Landroidx/media3/exoplayer/c1;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public synthetic constructor <init>(Landroidx/media3/exoplayer/c1;Landroidx/media3/exoplayer/c1$a;)V
    .locals 0

    invoke-direct {p0, p1}, Landroidx/media3/exoplayer/c1$d;-><init>(Landroidx/media3/exoplayer/c1;)V

    return-void
.end method

.method public static synthetic C(Ld2/b;Landroidx/media3/common/h0$d;)V
    .locals 0

    invoke-static {p0, p1}, Landroidx/media3/exoplayer/c1$d;->L(Ld2/b;Landroidx/media3/common/h0$d;)V

    return-void
.end method

.method public static synthetic D(IZLandroidx/media3/common/h0$d;)V
    .locals 0

    invoke-static {p0, p1, p2}, Landroidx/media3/exoplayer/c1$d;->Q(IZLandroidx/media3/common/h0$d;)V

    return-void
.end method

.method public static synthetic E(Landroidx/media3/common/Metadata;Landroidx/media3/common/h0$d;)V
    .locals 0

    invoke-static {p0, p1}, Landroidx/media3/exoplayer/c1$d;->N(Landroidx/media3/common/Metadata;Landroidx/media3/common/h0$d;)V

    return-void
.end method

.method public static synthetic F(Landroidx/media3/common/o;Landroidx/media3/common/h0$d;)V
    .locals 0

    invoke-static {p0, p1}, Landroidx/media3/exoplayer/c1$d;->P(Landroidx/media3/common/o;Landroidx/media3/common/h0$d;)V

    return-void
.end method

.method public static synthetic G(Landroidx/media3/common/t0;Landroidx/media3/common/h0$d;)V
    .locals 0

    invoke-static {p0, p1}, Landroidx/media3/exoplayer/c1$d;->R(Landroidx/media3/common/t0;Landroidx/media3/common/h0$d;)V

    return-void
.end method

.method public static synthetic H(ZLandroidx/media3/common/h0$d;)V
    .locals 0

    invoke-static {p0, p1}, Landroidx/media3/exoplayer/c1$d;->O(ZLandroidx/media3/common/h0$d;)V

    return-void
.end method

.method public static synthetic I(Landroidx/media3/exoplayer/c1$d;Landroidx/media3/common/h0$d;)V
    .locals 0

    invoke-virtual {p0, p1}, Landroidx/media3/exoplayer/c1$d;->M(Landroidx/media3/common/h0$d;)V

    return-void
.end method

.method public static synthetic J(Ljava/util/List;Landroidx/media3/common/h0$d;)V
    .locals 0

    invoke-static {p0, p1}, Landroidx/media3/exoplayer/c1$d;->K(Ljava/util/List;Landroidx/media3/common/h0$d;)V

    return-void
.end method

.method public static synthetic K(Ljava/util/List;Landroidx/media3/common/h0$d;)V
    .locals 0

    invoke-interface {p1, p0}, Landroidx/media3/common/h0$d;->onCues(Ljava/util/List;)V

    return-void
.end method

.method public static synthetic L(Ld2/b;Landroidx/media3/common/h0$d;)V
    .locals 0

    invoke-interface {p1, p0}, Landroidx/media3/common/h0$d;->onCues(Ld2/b;)V

    return-void
.end method

.method public static synthetic N(Landroidx/media3/common/Metadata;Landroidx/media3/common/h0$d;)V
    .locals 0

    invoke-interface {p1, p0}, Landroidx/media3/common/h0$d;->onMetadata(Landroidx/media3/common/Metadata;)V

    return-void
.end method

.method public static synthetic O(ZLandroidx/media3/common/h0$d;)V
    .locals 0

    invoke-interface {p1, p0}, Landroidx/media3/common/h0$d;->onSkipSilenceEnabledChanged(Z)V

    return-void
.end method

.method public static synthetic P(Landroidx/media3/common/o;Landroidx/media3/common/h0$d;)V
    .locals 0

    invoke-interface {p1, p0}, Landroidx/media3/common/h0$d;->onDeviceInfoChanged(Landroidx/media3/common/o;)V

    return-void
.end method

.method public static synthetic Q(IZLandroidx/media3/common/h0$d;)V
    .locals 0

    invoke-interface {p2, p0, p1}, Landroidx/media3/common/h0$d;->onDeviceVolumeChanged(IZ)V

    return-void
.end method

.method public static synthetic R(Landroidx/media3/common/t0;Landroidx/media3/common/h0$d;)V
    .locals 0

    invoke-interface {p1, p0}, Landroidx/media3/common/h0$d;->onVideoSizeChanged(Landroidx/media3/common/t0;)V

    return-void
.end method


# virtual methods
.method public synthetic A(Landroidx/media3/common/y;)V
    .locals 0

    invoke-static {p0, p1}, Lk2/l;->a(Landroidx/media3/exoplayer/audio/c;Landroidx/media3/common/y;)V

    return-void
.end method

.method public synthetic B(Landroidx/media3/common/y;)V
    .locals 0

    invoke-static {p0, p1}, Landroidx/media3/exoplayer/video/u;->a(Landroidx/media3/exoplayer/video/f0;Landroidx/media3/common/y;)V

    return-void
.end method

.method public final synthetic M(Landroidx/media3/common/h0$d;)V
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/c1$d;->a:Landroidx/media3/exoplayer/c1;

    invoke-static {v0}, Landroidx/media3/exoplayer/c1;->N0(Landroidx/media3/exoplayer/c1;)Landroidx/media3/common/d0;

    move-result-object v0

    invoke-interface {p1, v0}, Landroidx/media3/common/h0$d;->onMediaMetadataChanged(Landroidx/media3/common/d0;)V

    return-void
.end method

.method public a(Ljava/lang/Exception;)V
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/c1$d;->a:Landroidx/media3/exoplayer/c1;

    invoke-static {v0}, Landroidx/media3/exoplayer/c1;->f1(Landroidx/media3/exoplayer/c1;)Lj2/a;

    move-result-object v0

    invoke-interface {v0, p1}, Lj2/a;->a(Ljava/lang/Exception;)V

    return-void
.end method

.method public b(Ljava/lang/String;)V
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/c1$d;->a:Landroidx/media3/exoplayer/c1;

    invoke-static {v0}, Landroidx/media3/exoplayer/c1;->f1(Landroidx/media3/exoplayer/c1;)Lj2/a;

    move-result-object v0

    invoke-interface {v0, p1}, Lj2/a;->b(Ljava/lang/String;)V

    return-void
.end method

.method public c(Ljava/lang/String;)V
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/c1$d;->a:Landroidx/media3/exoplayer/c1;

    invoke-static {v0}, Landroidx/media3/exoplayer/c1;->f1(Landroidx/media3/exoplayer/c1;)Lj2/a;

    move-result-object v0

    invoke-interface {v0, p1}, Lj2/a;->c(Ljava/lang/String;)V

    return-void
.end method

.method public d(J)V
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/c1$d;->a:Landroidx/media3/exoplayer/c1;

    invoke-static {v0}, Landroidx/media3/exoplayer/c1;->f1(Landroidx/media3/exoplayer/c1;)Lj2/a;

    move-result-object v0

    invoke-interface {v0, p1, p2}, Lj2/a;->d(J)V

    return-void
.end method

.method public e(Ljava/lang/Exception;)V
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/c1$d;->a:Landroidx/media3/exoplayer/c1;

    invoke-static {v0}, Landroidx/media3/exoplayer/c1;->f1(Landroidx/media3/exoplayer/c1;)Lj2/a;

    move-result-object v0

    invoke-interface {v0, p1}, Lj2/a;->e(Ljava/lang/Exception;)V

    return-void
.end method

.method public f(I)V
    .locals 2

    iget-object p1, p0, Landroidx/media3/exoplayer/c1$d;->a:Landroidx/media3/exoplayer/c1;

    invoke-static {p1}, Landroidx/media3/exoplayer/c1;->W0(Landroidx/media3/exoplayer/c1;)Landroidx/media3/exoplayer/f3;

    move-result-object p1

    invoke-static {p1}, Landroidx/media3/exoplayer/c1;->X0(Landroidx/media3/exoplayer/f3;)Landroidx/media3/common/o;

    move-result-object p1

    iget-object v0, p0, Landroidx/media3/exoplayer/c1$d;->a:Landroidx/media3/exoplayer/c1;

    invoke-static {v0}, Landroidx/media3/exoplayer/c1;->Y0(Landroidx/media3/exoplayer/c1;)Landroidx/media3/common/o;

    move-result-object v0

    invoke-virtual {p1, v0}, Landroidx/media3/common/o;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_0

    iget-object v0, p0, Landroidx/media3/exoplayer/c1$d;->a:Landroidx/media3/exoplayer/c1;

    invoke-static {v0, p1}, Landroidx/media3/exoplayer/c1;->Z0(Landroidx/media3/exoplayer/c1;Landroidx/media3/common/o;)Landroidx/media3/common/o;

    iget-object v0, p0, Landroidx/media3/exoplayer/c1$d;->a:Landroidx/media3/exoplayer/c1;

    invoke-static {v0}, Landroidx/media3/exoplayer/c1;->i1(Landroidx/media3/exoplayer/c1;)Le2/n;

    move-result-object v0

    new-instance v1, Landroidx/media3/exoplayer/l1;

    invoke-direct {v1, p1}, Landroidx/media3/exoplayer/l1;-><init>(Landroidx/media3/common/o;)V

    const/16 p1, 0x1d

    invoke-virtual {v0, p1, v1}, Le2/n;->l(ILe2/n$a;)V

    :cond_0
    return-void
.end method

.method public g(Ljava/lang/Object;J)V
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/c1$d;->a:Landroidx/media3/exoplayer/c1;

    invoke-static {v0}, Landroidx/media3/exoplayer/c1;->f1(Landroidx/media3/exoplayer/c1;)Lj2/a;

    move-result-object v0

    invoke-interface {v0, p1, p2, p3}, Lj2/a;->g(Ljava/lang/Object;J)V

    iget-object p2, p0, Landroidx/media3/exoplayer/c1$d;->a:Landroidx/media3/exoplayer/c1;

    invoke-static {p2}, Landroidx/media3/exoplayer/c1;->j1(Landroidx/media3/exoplayer/c1;)Ljava/lang/Object;

    move-result-object p2

    if-ne p2, p1, :cond_0

    iget-object p1, p0, Landroidx/media3/exoplayer/c1$d;->a:Landroidx/media3/exoplayer/c1;

    invoke-static {p1}, Landroidx/media3/exoplayer/c1;->i1(Landroidx/media3/exoplayer/c1;)Le2/n;

    move-result-object p1

    new-instance p2, Landroidx/media3/exoplayer/m1;

    invoke-direct {p2}, Landroidx/media3/exoplayer/m1;-><init>()V

    const/16 p3, 0x1a

    invoke-virtual {p1, p3, p2}, Le2/n;->l(ILe2/n$a;)V

    :cond_0
    return-void
.end method

.method public h(Ljava/lang/Exception;)V
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/c1$d;->a:Landroidx/media3/exoplayer/c1;

    invoke-static {v0}, Landroidx/media3/exoplayer/c1;->f1(Landroidx/media3/exoplayer/c1;)Lj2/a;

    move-result-object v0

    invoke-interface {v0, p1}, Lj2/a;->h(Ljava/lang/Exception;)V

    return-void
.end method

.method public i(IJJ)V
    .locals 7

    iget-object v0, p0, Landroidx/media3/exoplayer/c1$d;->a:Landroidx/media3/exoplayer/c1;

    invoke-static {v0}, Landroidx/media3/exoplayer/c1;->f1(Landroidx/media3/exoplayer/c1;)Lj2/a;

    move-result-object v1

    move v2, p1

    move-wide v3, p2

    move-wide v5, p4

    invoke-interface/range {v1 .. v6}, Lj2/a;->i(IJJ)V

    return-void
.end method

.method public j(JI)V
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/c1$d;->a:Landroidx/media3/exoplayer/c1;

    invoke-static {v0}, Landroidx/media3/exoplayer/c1;->f1(Landroidx/media3/exoplayer/c1;)Lj2/a;

    move-result-object v0

    invoke-interface {v0, p1, p2, p3}, Lj2/a;->j(JI)V

    return-void
.end method

.method public k(Landroidx/media3/exoplayer/audio/AudioSink$a;)V
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/c1$d;->a:Landroidx/media3/exoplayer/c1;

    invoke-static {v0}, Landroidx/media3/exoplayer/c1;->f1(Landroidx/media3/exoplayer/c1;)Lj2/a;

    move-result-object v0

    invoke-interface {v0, p1}, Lj2/a;->k(Landroidx/media3/exoplayer/audio/AudioSink$a;)V

    return-void
.end method

.method public l(Landroidx/media3/exoplayer/audio/AudioSink$a;)V
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/c1$d;->a:Landroidx/media3/exoplayer/c1;

    invoke-static {v0}, Landroidx/media3/exoplayer/c1;->f1(Landroidx/media3/exoplayer/c1;)Lj2/a;

    move-result-object v0

    invoke-interface {v0, p1}, Lj2/a;->l(Landroidx/media3/exoplayer/audio/AudioSink$a;)V

    return-void
.end method

.method public m()V
    .locals 4

    iget-object v0, p0, Landroidx/media3/exoplayer/c1$d;->a:Landroidx/media3/exoplayer/c1;

    const/4 v1, -0x1

    const/4 v2, 0x3

    const/4 v3, 0x0

    invoke-static {v0, v3, v1, v2}, Landroidx/media3/exoplayer/c1;->V0(Landroidx/media3/exoplayer/c1;ZII)V

    return-void
.end method

.method public n(Landroid/view/Surface;)V
    .locals 1

    iget-object p1, p0, Landroidx/media3/exoplayer/c1$d;->a:Landroidx/media3/exoplayer/c1;

    const/4 v0, 0x0

    invoke-static {p1, v0}, Landroidx/media3/exoplayer/c1;->Q0(Landroidx/media3/exoplayer/c1;Ljava/lang/Object;)V

    return-void
.end method

.method public o(Landroidx/media3/exoplayer/n;)V
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/c1$d;->a:Landroidx/media3/exoplayer/c1;

    invoke-static {v0, p1}, Landroidx/media3/exoplayer/c1;->F0(Landroidx/media3/exoplayer/c1;Landroidx/media3/exoplayer/n;)Landroidx/media3/exoplayer/n;

    iget-object v0, p0, Landroidx/media3/exoplayer/c1$d;->a:Landroidx/media3/exoplayer/c1;

    invoke-static {v0}, Landroidx/media3/exoplayer/c1;->f1(Landroidx/media3/exoplayer/c1;)Lj2/a;

    move-result-object v0

    invoke-interface {v0, p1}, Lj2/a;->o(Landroidx/media3/exoplayer/n;)V

    return-void
.end method

.method public onAudioDecoderInitialized(Ljava/lang/String;JJ)V
    .locals 7

    iget-object v0, p0, Landroidx/media3/exoplayer/c1$d;->a:Landroidx/media3/exoplayer/c1;

    invoke-static {v0}, Landroidx/media3/exoplayer/c1;->f1(Landroidx/media3/exoplayer/c1;)Lj2/a;

    move-result-object v1

    move-object v2, p1

    move-wide v3, p2

    move-wide v5, p4

    invoke-interface/range {v1 .. v6}, Lj2/a;->onAudioDecoderInitialized(Ljava/lang/String;JJ)V

    return-void
.end method

.method public onCues(Ld2/b;)V
    .locals 2

    iget-object v0, p0, Landroidx/media3/exoplayer/c1$d;->a:Landroidx/media3/exoplayer/c1;

    invoke-static {v0, p1}, Landroidx/media3/exoplayer/c1;->J0(Landroidx/media3/exoplayer/c1;Ld2/b;)Ld2/b;

    iget-object v0, p0, Landroidx/media3/exoplayer/c1$d;->a:Landroidx/media3/exoplayer/c1;

    invoke-static {v0}, Landroidx/media3/exoplayer/c1;->i1(Landroidx/media3/exoplayer/c1;)Le2/n;

    move-result-object v0

    new-instance v1, Landroidx/media3/exoplayer/k1;

    invoke-direct {v1, p1}, Landroidx/media3/exoplayer/k1;-><init>(Ld2/b;)V

    const/16 p1, 0x1b

    invoke-virtual {v0, p1, v1}, Le2/n;->l(ILe2/n$a;)V

    return-void
.end method

.method public onCues(Ljava/util/List;)V
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Ld2/a;",
            ">;)V"
        }
    .end annotation

    iget-object v0, p0, Landroidx/media3/exoplayer/c1$d;->a:Landroidx/media3/exoplayer/c1;

    invoke-static {v0}, Landroidx/media3/exoplayer/c1;->i1(Landroidx/media3/exoplayer/c1;)Le2/n;

    move-result-object v0

    new-instance v1, Landroidx/media3/exoplayer/g1;

    invoke-direct {v1, p1}, Landroidx/media3/exoplayer/g1;-><init>(Ljava/util/List;)V

    const/16 p1, 0x1b

    invoke-virtual {v0, p1, v1}, Le2/n;->l(ILe2/n$a;)V

    return-void
.end method

.method public onDroppedFrames(IJ)V
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/c1$d;->a:Landroidx/media3/exoplayer/c1;

    invoke-static {v0}, Landroidx/media3/exoplayer/c1;->f1(Landroidx/media3/exoplayer/c1;)Lj2/a;

    move-result-object v0

    invoke-interface {v0, p1, p2, p3}, Lj2/a;->onDroppedFrames(IJ)V

    return-void
.end method

.method public onMetadata(Landroidx/media3/common/Metadata;)V
    .locals 3

    iget-object v0, p0, Landroidx/media3/exoplayer/c1$d;->a:Landroidx/media3/exoplayer/c1;

    invoke-static {v0}, Landroidx/media3/exoplayer/c1;->K0(Landroidx/media3/exoplayer/c1;)Landroidx/media3/common/d0;

    move-result-object v1

    invoke-virtual {v1}, Landroidx/media3/common/d0;->a()Landroidx/media3/common/d0$b;

    move-result-object v1

    invoke-virtual {v1, p1}, Landroidx/media3/common/d0$b;->K(Landroidx/media3/common/Metadata;)Landroidx/media3/common/d0$b;

    move-result-object v1

    invoke-virtual {v1}, Landroidx/media3/common/d0$b;->H()Landroidx/media3/common/d0;

    move-result-object v1

    invoke-static {v0, v1}, Landroidx/media3/exoplayer/c1;->L0(Landroidx/media3/exoplayer/c1;Landroidx/media3/common/d0;)Landroidx/media3/common/d0;

    iget-object v0, p0, Landroidx/media3/exoplayer/c1$d;->a:Landroidx/media3/exoplayer/c1;

    invoke-static {v0}, Landroidx/media3/exoplayer/c1;->M0(Landroidx/media3/exoplayer/c1;)Landroidx/media3/common/d0;

    move-result-object v0

    iget-object v1, p0, Landroidx/media3/exoplayer/c1$d;->a:Landroidx/media3/exoplayer/c1;

    invoke-static {v1}, Landroidx/media3/exoplayer/c1;->N0(Landroidx/media3/exoplayer/c1;)Landroidx/media3/common/d0;

    move-result-object v1

    invoke-virtual {v0, v1}, Landroidx/media3/common/d0;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_0

    iget-object v1, p0, Landroidx/media3/exoplayer/c1$d;->a:Landroidx/media3/exoplayer/c1;

    invoke-static {v1, v0}, Landroidx/media3/exoplayer/c1;->O0(Landroidx/media3/exoplayer/c1;Landroidx/media3/common/d0;)Landroidx/media3/common/d0;

    iget-object v0, p0, Landroidx/media3/exoplayer/c1$d;->a:Landroidx/media3/exoplayer/c1;

    invoke-static {v0}, Landroidx/media3/exoplayer/c1;->i1(Landroidx/media3/exoplayer/c1;)Le2/n;

    move-result-object v0

    new-instance v1, Landroidx/media3/exoplayer/h1;

    invoke-direct {v1, p0}, Landroidx/media3/exoplayer/h1;-><init>(Landroidx/media3/exoplayer/c1$d;)V

    const/16 v2, 0xe

    invoke-virtual {v0, v2, v1}, Le2/n;->i(ILe2/n$a;)V

    :cond_0
    iget-object v0, p0, Landroidx/media3/exoplayer/c1$d;->a:Landroidx/media3/exoplayer/c1;

    invoke-static {v0}, Landroidx/media3/exoplayer/c1;->i1(Landroidx/media3/exoplayer/c1;)Le2/n;

    move-result-object v0

    new-instance v1, Landroidx/media3/exoplayer/i1;

    invoke-direct {v1, p1}, Landroidx/media3/exoplayer/i1;-><init>(Landroidx/media3/common/Metadata;)V

    const/16 p1, 0x1c

    invoke-virtual {v0, p1, v1}, Le2/n;->i(ILe2/n$a;)V

    iget-object p1, p0, Landroidx/media3/exoplayer/c1$d;->a:Landroidx/media3/exoplayer/c1;

    invoke-static {p1}, Landroidx/media3/exoplayer/c1;->i1(Landroidx/media3/exoplayer/c1;)Le2/n;

    move-result-object p1

    invoke-virtual {p1}, Le2/n;->f()V

    return-void
.end method

.method public onSkipSilenceEnabledChanged(Z)V
    .locals 2

    iget-object v0, p0, Landroidx/media3/exoplayer/c1$d;->a:Landroidx/media3/exoplayer/c1;

    invoke-static {v0}, Landroidx/media3/exoplayer/c1;->H0(Landroidx/media3/exoplayer/c1;)Z

    move-result v0

    if-ne v0, p1, :cond_0

    return-void

    :cond_0
    iget-object v0, p0, Landroidx/media3/exoplayer/c1$d;->a:Landroidx/media3/exoplayer/c1;

    invoke-static {v0, p1}, Landroidx/media3/exoplayer/c1;->I0(Landroidx/media3/exoplayer/c1;Z)Z

    iget-object v0, p0, Landroidx/media3/exoplayer/c1$d;->a:Landroidx/media3/exoplayer/c1;

    invoke-static {v0}, Landroidx/media3/exoplayer/c1;->i1(Landroidx/media3/exoplayer/c1;)Le2/n;

    move-result-object v0

    new-instance v1, Landroidx/media3/exoplayer/o1;

    invoke-direct {v1, p1}, Landroidx/media3/exoplayer/o1;-><init>(Z)V

    const/16 p1, 0x17

    invoke-virtual {v0, p1, v1}, Le2/n;->l(ILe2/n$a;)V

    return-void
.end method

.method public onSurfaceTextureAvailable(Landroid/graphics/SurfaceTexture;II)V
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/c1$d;->a:Landroidx/media3/exoplayer/c1;

    invoke-static {v0, p1}, Landroidx/media3/exoplayer/c1;->S0(Landroidx/media3/exoplayer/c1;Landroid/graphics/SurfaceTexture;)V

    iget-object p1, p0, Landroidx/media3/exoplayer/c1$d;->a:Landroidx/media3/exoplayer/c1;

    invoke-static {p1, p2, p3}, Landroidx/media3/exoplayer/c1;->R0(Landroidx/media3/exoplayer/c1;II)V

    return-void
.end method

.method public onSurfaceTextureDestroyed(Landroid/graphics/SurfaceTexture;)Z
    .locals 1

    iget-object p1, p0, Landroidx/media3/exoplayer/c1$d;->a:Landroidx/media3/exoplayer/c1;

    const/4 v0, 0x0

    invoke-static {p1, v0}, Landroidx/media3/exoplayer/c1;->Q0(Landroidx/media3/exoplayer/c1;Ljava/lang/Object;)V

    iget-object p1, p0, Landroidx/media3/exoplayer/c1$d;->a:Landroidx/media3/exoplayer/c1;

    const/4 v0, 0x0

    invoke-static {p1, v0, v0}, Landroidx/media3/exoplayer/c1;->R0(Landroidx/media3/exoplayer/c1;II)V

    const/4 p1, 0x1

    return p1
.end method

.method public onSurfaceTextureSizeChanged(Landroid/graphics/SurfaceTexture;II)V
    .locals 0

    iget-object p1, p0, Landroidx/media3/exoplayer/c1$d;->a:Landroidx/media3/exoplayer/c1;

    invoke-static {p1, p2, p3}, Landroidx/media3/exoplayer/c1;->R0(Landroidx/media3/exoplayer/c1;II)V

    return-void
.end method

.method public onSurfaceTextureUpdated(Landroid/graphics/SurfaceTexture;)V
    .locals 0

    return-void
.end method

.method public onVideoDecoderInitialized(Ljava/lang/String;JJ)V
    .locals 7

    iget-object v0, p0, Landroidx/media3/exoplayer/c1$d;->a:Landroidx/media3/exoplayer/c1;

    invoke-static {v0}, Landroidx/media3/exoplayer/c1;->f1(Landroidx/media3/exoplayer/c1;)Lj2/a;

    move-result-object v1

    move-object v2, p1

    move-wide v3, p2

    move-wide v5, p4

    invoke-interface/range {v1 .. v6}, Lj2/a;->onVideoDecoderInitialized(Ljava/lang/String;JJ)V

    return-void
.end method

.method public onVideoSizeChanged(Landroidx/media3/common/t0;)V
    .locals 2

    iget-object v0, p0, Landroidx/media3/exoplayer/c1$d;->a:Landroidx/media3/exoplayer/c1;

    invoke-static {v0, p1}, Landroidx/media3/exoplayer/c1;->h1(Landroidx/media3/exoplayer/c1;Landroidx/media3/common/t0;)Landroidx/media3/common/t0;

    iget-object v0, p0, Landroidx/media3/exoplayer/c1$d;->a:Landroidx/media3/exoplayer/c1;

    invoke-static {v0}, Landroidx/media3/exoplayer/c1;->i1(Landroidx/media3/exoplayer/c1;)Le2/n;

    move-result-object v0

    new-instance v1, Landroidx/media3/exoplayer/n1;

    invoke-direct {v1, p1}, Landroidx/media3/exoplayer/n1;-><init>(Landroidx/media3/common/t0;)V

    const/16 p1, 0x19

    invoke-virtual {v0, p1, v1}, Le2/n;->l(ILe2/n$a;)V

    return-void
.end method

.method public p(Landroidx/media3/exoplayer/n;)V
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/c1$d;->a:Landroidx/media3/exoplayer/c1;

    invoke-static {v0, p1}, Landroidx/media3/exoplayer/c1;->e1(Landroidx/media3/exoplayer/c1;Landroidx/media3/exoplayer/n;)Landroidx/media3/exoplayer/n;

    iget-object v0, p0, Landroidx/media3/exoplayer/c1$d;->a:Landroidx/media3/exoplayer/c1;

    invoke-static {v0}, Landroidx/media3/exoplayer/c1;->f1(Landroidx/media3/exoplayer/c1;)Lj2/a;

    move-result-object v0

    invoke-interface {v0, p1}, Lj2/a;->p(Landroidx/media3/exoplayer/n;)V

    return-void
.end method

.method public q(Landroid/view/Surface;)V
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/c1$d;->a:Landroidx/media3/exoplayer/c1;

    invoke-static {v0, p1}, Landroidx/media3/exoplayer/c1;->Q0(Landroidx/media3/exoplayer/c1;Ljava/lang/Object;)V

    return-void
.end method

.method public r(IZ)V
    .locals 2

    iget-object v0, p0, Landroidx/media3/exoplayer/c1$d;->a:Landroidx/media3/exoplayer/c1;

    invoke-static {v0}, Landroidx/media3/exoplayer/c1;->i1(Landroidx/media3/exoplayer/c1;)Le2/n;

    move-result-object v0

    new-instance v1, Landroidx/media3/exoplayer/j1;

    invoke-direct {v1, p1, p2}, Landroidx/media3/exoplayer/j1;-><init>(IZ)V

    const/16 p1, 0x1e

    invoke-virtual {v0, p1, v1}, Le2/n;->l(ILe2/n$a;)V

    return-void
.end method

.method public s(Landroidx/media3/common/y;Landroidx/media3/exoplayer/o;)V
    .locals 1
    .param p2    # Landroidx/media3/exoplayer/o;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    iget-object v0, p0, Landroidx/media3/exoplayer/c1$d;->a:Landroidx/media3/exoplayer/c1;

    invoke-static {v0, p1}, Landroidx/media3/exoplayer/c1;->G0(Landroidx/media3/exoplayer/c1;Landroidx/media3/common/y;)Landroidx/media3/common/y;

    iget-object v0, p0, Landroidx/media3/exoplayer/c1$d;->a:Landroidx/media3/exoplayer/c1;

    invoke-static {v0}, Landroidx/media3/exoplayer/c1;->f1(Landroidx/media3/exoplayer/c1;)Lj2/a;

    move-result-object v0

    invoke-interface {v0, p1, p2}, Lj2/a;->s(Landroidx/media3/common/y;Landroidx/media3/exoplayer/o;)V

    return-void
.end method

.method public surfaceChanged(Landroid/view/SurfaceHolder;III)V
    .locals 0

    iget-object p1, p0, Landroidx/media3/exoplayer/c1$d;->a:Landroidx/media3/exoplayer/c1;

    invoke-static {p1, p3, p4}, Landroidx/media3/exoplayer/c1;->R0(Landroidx/media3/exoplayer/c1;II)V

    return-void
.end method

.method public surfaceCreated(Landroid/view/SurfaceHolder;)V
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/c1$d;->a:Landroidx/media3/exoplayer/c1;

    invoke-static {v0}, Landroidx/media3/exoplayer/c1;->P0(Landroidx/media3/exoplayer/c1;)Z

    move-result v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Landroidx/media3/exoplayer/c1$d;->a:Landroidx/media3/exoplayer/c1;

    invoke-interface {p1}, Landroid/view/SurfaceHolder;->getSurface()Landroid/view/Surface;

    move-result-object p1

    invoke-static {v0, p1}, Landroidx/media3/exoplayer/c1;->Q0(Landroidx/media3/exoplayer/c1;Ljava/lang/Object;)V

    :cond_0
    return-void
.end method

.method public surfaceDestroyed(Landroid/view/SurfaceHolder;)V
    .locals 1

    iget-object p1, p0, Landroidx/media3/exoplayer/c1$d;->a:Landroidx/media3/exoplayer/c1;

    invoke-static {p1}, Landroidx/media3/exoplayer/c1;->P0(Landroidx/media3/exoplayer/c1;)Z

    move-result p1

    if-eqz p1, :cond_0

    iget-object p1, p0, Landroidx/media3/exoplayer/c1$d;->a:Landroidx/media3/exoplayer/c1;

    const/4 v0, 0x0

    invoke-static {p1, v0}, Landroidx/media3/exoplayer/c1;->Q0(Landroidx/media3/exoplayer/c1;Ljava/lang/Object;)V

    :cond_0
    iget-object p1, p0, Landroidx/media3/exoplayer/c1$d;->a:Landroidx/media3/exoplayer/c1;

    const/4 v0, 0x0

    invoke-static {p1, v0, v0}, Landroidx/media3/exoplayer/c1;->R0(Landroidx/media3/exoplayer/c1;II)V

    return-void
.end method

.method public t(Landroidx/media3/exoplayer/n;)V
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/c1$d;->a:Landroidx/media3/exoplayer/c1;

    invoke-static {v0}, Landroidx/media3/exoplayer/c1;->f1(Landroidx/media3/exoplayer/c1;)Lj2/a;

    move-result-object v0

    invoke-interface {v0, p1}, Lj2/a;->t(Landroidx/media3/exoplayer/n;)V

    iget-object p1, p0, Landroidx/media3/exoplayer/c1$d;->a:Landroidx/media3/exoplayer/c1;

    const/4 v0, 0x0

    invoke-static {p1, v0}, Landroidx/media3/exoplayer/c1;->G0(Landroidx/media3/exoplayer/c1;Landroidx/media3/common/y;)Landroidx/media3/common/y;

    iget-object p1, p0, Landroidx/media3/exoplayer/c1$d;->a:Landroidx/media3/exoplayer/c1;

    invoke-static {p1, v0}, Landroidx/media3/exoplayer/c1;->F0(Landroidx/media3/exoplayer/c1;Landroidx/media3/exoplayer/n;)Landroidx/media3/exoplayer/n;

    return-void
.end method

.method public u(F)V
    .locals 0

    iget-object p1, p0, Landroidx/media3/exoplayer/c1$d;->a:Landroidx/media3/exoplayer/c1;

    invoke-static {p1}, Landroidx/media3/exoplayer/c1;->T0(Landroidx/media3/exoplayer/c1;)V

    return-void
.end method

.method public v(I)V
    .locals 3

    iget-object v0, p0, Landroidx/media3/exoplayer/c1$d;->a:Landroidx/media3/exoplayer/c1;

    invoke-virtual {v0}, Landroidx/media3/exoplayer/c1;->getPlayWhenReady()Z

    move-result v0

    iget-object v1, p0, Landroidx/media3/exoplayer/c1$d;->a:Landroidx/media3/exoplayer/c1;

    invoke-static {v0, p1}, Landroidx/media3/exoplayer/c1;->U0(ZI)I

    move-result v2

    invoke-static {v1, v0, p1, v2}, Landroidx/media3/exoplayer/c1;->V0(Landroidx/media3/exoplayer/c1;ZII)V

    return-void
.end method

.method public w(Landroidx/media3/common/y;Landroidx/media3/exoplayer/o;)V
    .locals 1
    .param p2    # Landroidx/media3/exoplayer/o;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    iget-object v0, p0, Landroidx/media3/exoplayer/c1$d;->a:Landroidx/media3/exoplayer/c1;

    invoke-static {v0, p1}, Landroidx/media3/exoplayer/c1;->g1(Landroidx/media3/exoplayer/c1;Landroidx/media3/common/y;)Landroidx/media3/common/y;

    iget-object v0, p0, Landroidx/media3/exoplayer/c1$d;->a:Landroidx/media3/exoplayer/c1;

    invoke-static {v0}, Landroidx/media3/exoplayer/c1;->f1(Landroidx/media3/exoplayer/c1;)Lj2/a;

    move-result-object v0

    invoke-interface {v0, p1, p2}, Lj2/a;->w(Landroidx/media3/common/y;Landroidx/media3/exoplayer/o;)V

    return-void
.end method

.method public x(Landroidx/media3/exoplayer/n;)V
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/c1$d;->a:Landroidx/media3/exoplayer/c1;

    invoke-static {v0}, Landroidx/media3/exoplayer/c1;->f1(Landroidx/media3/exoplayer/c1;)Lj2/a;

    move-result-object v0

    invoke-interface {v0, p1}, Lj2/a;->x(Landroidx/media3/exoplayer/n;)V

    iget-object p1, p0, Landroidx/media3/exoplayer/c1$d;->a:Landroidx/media3/exoplayer/c1;

    const/4 v0, 0x0

    invoke-static {p1, v0}, Landroidx/media3/exoplayer/c1;->g1(Landroidx/media3/exoplayer/c1;Landroidx/media3/common/y;)Landroidx/media3/common/y;

    iget-object p1, p0, Landroidx/media3/exoplayer/c1$d;->a:Landroidx/media3/exoplayer/c1;

    invoke-static {p1, v0}, Landroidx/media3/exoplayer/c1;->e1(Landroidx/media3/exoplayer/c1;Landroidx/media3/exoplayer/n;)Landroidx/media3/exoplayer/n;

    return-void
.end method

.method public synthetic y(Z)V
    .locals 0

    invoke-static {p0, p1}, Landroidx/media3/exoplayer/t;->a(Landroidx/media3/exoplayer/u$a;Z)V

    return-void
.end method

.method public z(Z)V
    .locals 0

    iget-object p1, p0, Landroidx/media3/exoplayer/c1$d;->a:Landroidx/media3/exoplayer/c1;

    invoke-static {p1}, Landroidx/media3/exoplayer/c1;->a1(Landroidx/media3/exoplayer/c1;)V

    return-void
.end method
