.class public final Landroidx/media3/exoplayer/c1;
.super Landroidx/media3/common/h;

# interfaces
.implements Landroidx/media3/exoplayer/u;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Landroidx/media3/exoplayer/c1$d;,
        Landroidx/media3/exoplayer/c1$e;,
        Landroidx/media3/exoplayer/c1$c;,
        Landroidx/media3/exoplayer/c1$g;,
        Landroidx/media3/exoplayer/c1$b;,
        Landroidx/media3/exoplayer/c1$f;
    }
.end annotation


# instance fields
.field public final A:Landroidx/media3/exoplayer/l;

.field public final B:Landroidx/media3/exoplayer/f3;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public final C:Landroidx/media3/exoplayer/h3;

.field public final D:Landroidx/media3/exoplayer/i3;

.field public final E:J

.field public F:Landroid/media/AudioManager;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public final G:Z

.field public H:I

.field public I:Z

.field public J:I

.field public K:I

.field public L:Z

.field public M:I

.field public N:Landroidx/media3/exoplayer/b3;

.field public O:Lu2/f0;

.field public P:Z

.field public Q:Landroidx/media3/common/h0$b;

.field public R:Landroidx/media3/common/d0;

.field public S:Landroidx/media3/common/d0;

.field public T:Landroidx/media3/common/y;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public U:Landroidx/media3/common/y;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public V:Landroid/media/AudioTrack;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public W:Ljava/lang/Object;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public X:Landroid/view/Surface;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public Y:Landroid/view/SurfaceHolder;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public Z:Landroidx/media3/exoplayer/video/spherical/SphericalGLSurfaceView;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public a0:Z

.field public final b:Lx2/f0;

.field public b0:Landroid/view/TextureView;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public final c:Landroidx/media3/common/h0$b;

.field public c0:I

.field public final d:Le2/g;

.field public d0:I

.field public final e:Landroid/content/Context;

.field public e0:Le2/e0;

.field public final f:Landroidx/media3/common/h0;

.field public f0:Landroidx/media3/exoplayer/n;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public final g:[Landroidx/media3/exoplayer/w2;

.field public g0:Landroidx/media3/exoplayer/n;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public final h:Lx2/e0;

.field public h0:I

.field public final i:Le2/j;

.field public i0:Landroidx/media3/common/d;

.field public final j:Landroidx/media3/exoplayer/s1$f;

.field public j0:F

.field public final k:Landroidx/media3/exoplayer/s1;

.field public k0:Z

.field public final l:Le2/n;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Le2/n<",
            "Landroidx/media3/common/h0$d;",
            ">;"
        }
    .end annotation
.end field

.field public l0:Ld2/b;

.field public final m:Ljava/util/concurrent/CopyOnWriteArraySet;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/concurrent/CopyOnWriteArraySet<",
            "Landroidx/media3/exoplayer/u$a;",
            ">;"
        }
    .end annotation
.end field

.field public m0:Z

.field public final n:Landroidx/media3/common/m0$b;

.field public n0:Z

.field public final o:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Landroidx/media3/exoplayer/c1$f;",
            ">;"
        }
    .end annotation
.end field

.field public o0:Landroidx/media3/common/PriorityTaskManager;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public final p:Z

.field public p0:Z

.field public final q:Landroidx/media3/exoplayer/source/l$a;

.field public q0:Z

.field public final r:Lj2/a;

.field public r0:Landroidx/media3/common/o;

.field public final s:Landroid/os/Looper;

.field public s0:Landroidx/media3/common/t0;

.field public final t:Landroidx/media3/exoplayer/upstream/e;

.field public t0:Landroidx/media3/common/d0;

.field public final u:J

.field public u0:Landroidx/media3/exoplayer/s2;

.field public final v:J

.field public v0:I

.field public final w:Le2/d;

.field public w0:I

.field public final x:Landroidx/media3/exoplayer/c1$d;

.field public x0:J

.field public final y:Landroidx/media3/exoplayer/c1$e;

.field public final z:Landroidx/media3/exoplayer/AudioBecomingNoisyManager;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    const-string v0, "media3.exoplayer"

    invoke-static {v0}, Landroidx/media3/common/c0;->a(Ljava/lang/String;)V

    return-void
.end method

.method public constructor <init>(Landroidx/media3/exoplayer/u$b;Landroidx/media3/common/h0;)V
    .locals 39
    .param p2    # Landroidx/media3/common/h0;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .annotation build Landroid/annotation/SuppressLint;
        value = {
            "HandlerLeak"
        }
    .end annotation

    move-object/from16 v1, p0

    move-object/from16 v0, p1

    invoke-direct/range {p0 .. p0}, Landroidx/media3/common/h;-><init>()V

    new-instance v2, Le2/g;

    invoke-direct {v2}, Le2/g;-><init>()V

    iput-object v2, v1, Landroidx/media3/exoplayer/c1;->d:Le2/g;

    :try_start_0
    const-string v3, "ExoPlayerImpl"

    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    const-string v5, "Init "

    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-static/range {p0 .. p0}, Ljava/lang/System;->identityHashCode(Ljava/lang/Object;)I

    move-result v5

    invoke-static {v5}, Ljava/lang/Integer;->toHexString(I)Ljava/lang/String;

    move-result-object v5

    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v5, " ["

    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v5, "AndroidXMedia3/1.3.1"

    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v5, "] ["

    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    sget-object v5, Le2/u0;->e:Ljava/lang/String;

    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v5, "]"

    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v4

    invoke-static {v3, v4}, Le2/o;->f(Ljava/lang/String;Ljava/lang/String;)V

    iget-object v3, v0, Landroidx/media3/exoplayer/u$b;->a:Landroid/content/Context;

    invoke-virtual {v3}, Landroid/content/Context;->getApplicationContext()Landroid/content/Context;

    move-result-object v3

    iput-object v3, v1, Landroidx/media3/exoplayer/c1;->e:Landroid/content/Context;

    iget-object v4, v0, Landroidx/media3/exoplayer/u$b;->i:Lcom/google/common/base/f;

    iget-object v5, v0, Landroidx/media3/exoplayer/u$b;->b:Le2/d;

    invoke-interface {v4, v5}, Lcom/google/common/base/f;->apply(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lj2/a;

    iput-object v4, v1, Landroidx/media3/exoplayer/c1;->r:Lj2/a;

    iget-object v5, v0, Landroidx/media3/exoplayer/u$b;->k:Landroidx/media3/common/PriorityTaskManager;

    iput-object v5, v1, Landroidx/media3/exoplayer/c1;->o0:Landroidx/media3/common/PriorityTaskManager;

    iget-object v5, v0, Landroidx/media3/exoplayer/u$b;->l:Landroidx/media3/common/d;

    iput-object v5, v1, Landroidx/media3/exoplayer/c1;->i0:Landroidx/media3/common/d;

    iget v5, v0, Landroidx/media3/exoplayer/u$b;->r:I

    iput v5, v1, Landroidx/media3/exoplayer/c1;->c0:I

    iget v5, v0, Landroidx/media3/exoplayer/u$b;->s:I

    iput v5, v1, Landroidx/media3/exoplayer/c1;->d0:I

    iget-boolean v5, v0, Landroidx/media3/exoplayer/u$b;->p:Z

    iput-boolean v5, v1, Landroidx/media3/exoplayer/c1;->k0:Z

    iget-wide v5, v0, Landroidx/media3/exoplayer/u$b;->z:J

    iput-wide v5, v1, Landroidx/media3/exoplayer/c1;->E:J

    new-instance v15, Landroidx/media3/exoplayer/c1$d;

    const/4 v14, 0x0

    invoke-direct {v15, v1, v14}, Landroidx/media3/exoplayer/c1$d;-><init>(Landroidx/media3/exoplayer/c1;Landroidx/media3/exoplayer/c1$a;)V

    iput-object v15, v1, Landroidx/media3/exoplayer/c1;->x:Landroidx/media3/exoplayer/c1$d;

    new-instance v13, Landroidx/media3/exoplayer/c1$e;

    invoke-direct {v13, v14}, Landroidx/media3/exoplayer/c1$e;-><init>(Landroidx/media3/exoplayer/c1$a;)V

    iput-object v13, v1, Landroidx/media3/exoplayer/c1;->y:Landroidx/media3/exoplayer/c1$e;

    new-instance v6, Landroid/os/Handler;

    iget-object v5, v0, Landroidx/media3/exoplayer/u$b;->j:Landroid/os/Looper;

    invoke-direct {v6, v5}, Landroid/os/Handler;-><init>(Landroid/os/Looper;)V

    iget-object v5, v0, Landroidx/media3/exoplayer/u$b;->d:Lcom/google/common/base/q;

    invoke-interface {v5}, Lcom/google/common/base/q;->get()Ljava/lang/Object;

    move-result-object v5

    move-object v7, v5

    check-cast v7, Landroidx/media3/exoplayer/a3;

    move-object v8, v6

    move-object v9, v15

    move-object v10, v15

    move-object v11, v15

    move-object v12, v15

    invoke-interface/range {v7 .. v12}, Landroidx/media3/exoplayer/a3;->a(Landroid/os/Handler;Landroidx/media3/exoplayer/video/f0;Landroidx/media3/exoplayer/audio/c;Lw2/h;Ls2/b;)[Landroidx/media3/exoplayer/w2;

    move-result-object v7

    iput-object v7, v1, Landroidx/media3/exoplayer/c1;->g:[Landroidx/media3/exoplayer/w2;

    array-length v5, v7

    if-lez v5, :cond_0

    const/4 v5, 0x1

    goto :goto_0

    :cond_0
    const/4 v5, 0x0

    :goto_0
    invoke-static {v5}, Le2/a;->g(Z)V

    iget-object v5, v0, Landroidx/media3/exoplayer/u$b;->f:Lcom/google/common/base/q;

    invoke-interface {v5}, Lcom/google/common/base/q;->get()Ljava/lang/Object;

    move-result-object v5

    move-object v10, v5

    check-cast v10, Lx2/e0;

    iput-object v10, v1, Landroidx/media3/exoplayer/c1;->h:Lx2/e0;

    iget-object v5, v0, Landroidx/media3/exoplayer/u$b;->e:Lcom/google/common/base/q;

    invoke-interface {v5}, Lcom/google/common/base/q;->get()Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Landroidx/media3/exoplayer/source/l$a;

    iput-object v5, v1, Landroidx/media3/exoplayer/c1;->q:Landroidx/media3/exoplayer/source/l$a;

    iget-object v5, v0, Landroidx/media3/exoplayer/u$b;->h:Lcom/google/common/base/q;

    invoke-interface {v5}, Lcom/google/common/base/q;->get()Ljava/lang/Object;

    move-result-object v5

    move-object v9, v5

    check-cast v9, Landroidx/media3/exoplayer/upstream/e;

    iput-object v9, v1, Landroidx/media3/exoplayer/c1;->t:Landroidx/media3/exoplayer/upstream/e;

    iget-boolean v5, v0, Landroidx/media3/exoplayer/u$b;->t:Z

    iput-boolean v5, v1, Landroidx/media3/exoplayer/c1;->p:Z

    iget-object v5, v0, Landroidx/media3/exoplayer/u$b;->u:Landroidx/media3/exoplayer/b3;

    iput-object v5, v1, Landroidx/media3/exoplayer/c1;->N:Landroidx/media3/exoplayer/b3;

    move-object/from16 v16, v15

    iget-wide v14, v0, Landroidx/media3/exoplayer/u$b;->v:J

    iput-wide v14, v1, Landroidx/media3/exoplayer/c1;->u:J

    iget-wide v14, v0, Landroidx/media3/exoplayer/u$b;->w:J

    iput-wide v14, v1, Landroidx/media3/exoplayer/c1;->v:J

    iget-boolean v5, v0, Landroidx/media3/exoplayer/u$b;->A:Z

    iput-boolean v5, v1, Landroidx/media3/exoplayer/c1;->P:Z

    iget-object v15, v0, Landroidx/media3/exoplayer/u$b;->j:Landroid/os/Looper;

    iput-object v15, v1, Landroidx/media3/exoplayer/c1;->s:Landroid/os/Looper;

    iget-object v14, v0, Landroidx/media3/exoplayer/u$b;->b:Le2/d;

    iput-object v14, v1, Landroidx/media3/exoplayer/c1;->w:Le2/d;

    if-nez p2, :cond_1

    move-object v5, v1

    goto :goto_1

    :cond_1
    move-object/from16 v5, p2

    :goto_1
    iput-object v5, v1, Landroidx/media3/exoplayer/c1;->f:Landroidx/media3/common/h0;

    iget-boolean v8, v0, Landroidx/media3/exoplayer/u$b;->E:Z

    iput-boolean v8, v1, Landroidx/media3/exoplayer/c1;->G:Z

    new-instance v11, Le2/n;

    new-instance v12, Landroidx/media3/exoplayer/k0;

    invoke-direct {v12, v1}, Landroidx/media3/exoplayer/k0;-><init>(Landroidx/media3/exoplayer/c1;)V

    invoke-direct {v11, v15, v14, v12}, Le2/n;-><init>(Landroid/os/Looper;Le2/d;Le2/n$b;)V

    iput-object v11, v1, Landroidx/media3/exoplayer/c1;->l:Le2/n;

    new-instance v11, Ljava/util/concurrent/CopyOnWriteArraySet;

    invoke-direct {v11}, Ljava/util/concurrent/CopyOnWriteArraySet;-><init>()V

    iput-object v11, v1, Landroidx/media3/exoplayer/c1;->m:Ljava/util/concurrent/CopyOnWriteArraySet;

    new-instance v11, Ljava/util/ArrayList;

    invoke-direct {v11}, Ljava/util/ArrayList;-><init>()V

    iput-object v11, v1, Landroidx/media3/exoplayer/c1;->o:Ljava/util/List;

    new-instance v11, Lu2/f0$a;

    const/4 v12, 0x0

    invoke-direct {v11, v12}, Lu2/f0$a;-><init>(I)V

    iput-object v11, v1, Landroidx/media3/exoplayer/c1;->O:Lu2/f0;

    new-instance v11, Lx2/f0;

    array-length v12, v7

    new-array v12, v12, [Landroidx/media3/exoplayer/z2;

    move-object/from16 v20, v6

    array-length v6, v7

    new-array v6, v6, [Lx2/z;

    move/from16 v21, v8

    sget-object v8, Landroidx/media3/common/q0;->b:Landroidx/media3/common/q0;

    move-object/from16 v22, v9

    const/4 v9, 0x0

    invoke-direct {v11, v12, v6, v8, v9}, Lx2/f0;-><init>([Landroidx/media3/exoplayer/z2;[Lx2/z;Landroidx/media3/common/q0;Ljava/lang/Object;)V

    iput-object v11, v1, Landroidx/media3/exoplayer/c1;->b:Lx2/f0;

    new-instance v6, Landroidx/media3/common/m0$b;

    invoke-direct {v6}, Landroidx/media3/common/m0$b;-><init>()V

    iput-object v6, v1, Landroidx/media3/exoplayer/c1;->n:Landroidx/media3/common/m0$b;

    new-instance v6, Landroidx/media3/common/h0$b$a;

    invoke-direct {v6}, Landroidx/media3/common/h0$b$a;-><init>()V

    const/16 v8, 0x14

    new-array v8, v8, [I

    fill-array-data v8, :array_0

    invoke-virtual {v6, v8}, Landroidx/media3/common/h0$b$a;->c([I)Landroidx/media3/common/h0$b$a;

    move-result-object v6

    invoke-virtual {v10}, Lx2/e0;->h()Z

    move-result v8

    const/16 v9, 0x1d

    invoke-virtual {v6, v9, v8}, Landroidx/media3/common/h0$b$a;->d(IZ)Landroidx/media3/common/h0$b$a;

    move-result-object v6

    iget-boolean v8, v0, Landroidx/media3/exoplayer/u$b;->q:Z

    const/16 v12, 0x17

    invoke-virtual {v6, v12, v8}, Landroidx/media3/common/h0$b$a;->d(IZ)Landroidx/media3/common/h0$b$a;

    move-result-object v6

    iget-boolean v8, v0, Landroidx/media3/exoplayer/u$b;->q:Z

    const/16 v9, 0x19

    invoke-virtual {v6, v9, v8}, Landroidx/media3/common/h0$b$a;->d(IZ)Landroidx/media3/common/h0$b$a;

    move-result-object v6

    iget-boolean v8, v0, Landroidx/media3/exoplayer/u$b;->q:Z

    const/16 v9, 0x21

    invoke-virtual {v6, v9, v8}, Landroidx/media3/common/h0$b$a;->d(IZ)Landroidx/media3/common/h0$b$a;

    move-result-object v6

    iget-boolean v8, v0, Landroidx/media3/exoplayer/u$b;->q:Z

    const/16 v9, 0x1a

    invoke-virtual {v6, v9, v8}, Landroidx/media3/common/h0$b$a;->d(IZ)Landroidx/media3/common/h0$b$a;

    move-result-object v6

    iget-boolean v8, v0, Landroidx/media3/exoplayer/u$b;->q:Z

    const/16 v9, 0x22

    invoke-virtual {v6, v9, v8}, Landroidx/media3/common/h0$b$a;->d(IZ)Landroidx/media3/common/h0$b$a;

    move-result-object v6

    invoke-virtual {v6}, Landroidx/media3/common/h0$b$a;->e()Landroidx/media3/common/h0$b;

    move-result-object v6

    iput-object v6, v1, Landroidx/media3/exoplayer/c1;->c:Landroidx/media3/common/h0$b;

    new-instance v8, Landroidx/media3/common/h0$b$a;

    invoke-direct {v8}, Landroidx/media3/common/h0$b$a;-><init>()V

    invoke-virtual {v8, v6}, Landroidx/media3/common/h0$b$a;->b(Landroidx/media3/common/h0$b;)Landroidx/media3/common/h0$b$a;

    move-result-object v6

    const/4 v9, 0x4

    invoke-virtual {v6, v9}, Landroidx/media3/common/h0$b$a;->a(I)Landroidx/media3/common/h0$b$a;

    move-result-object v6

    const/16 v8, 0xa

    invoke-virtual {v6, v8}, Landroidx/media3/common/h0$b$a;->a(I)Landroidx/media3/common/h0$b$a;

    move-result-object v6

    invoke-virtual {v6}, Landroidx/media3/common/h0$b$a;->e()Landroidx/media3/common/h0$b;

    move-result-object v6

    iput-object v6, v1, Landroidx/media3/exoplayer/c1;->Q:Landroidx/media3/common/h0$b;

    const/4 v6, 0x0

    invoke-interface {v14, v15, v6}, Le2/d;->createHandler(Landroid/os/Looper;Landroid/os/Handler$Callback;)Le2/j;

    move-result-object v8

    iput-object v8, v1, Landroidx/media3/exoplayer/c1;->i:Le2/j;

    new-instance v8, Landroidx/media3/exoplayer/l0;

    invoke-direct {v8, v1}, Landroidx/media3/exoplayer/l0;-><init>(Landroidx/media3/exoplayer/c1;)V

    iput-object v8, v1, Landroidx/media3/exoplayer/c1;->j:Landroidx/media3/exoplayer/s1$f;

    invoke-static {v11}, Landroidx/media3/exoplayer/s2;->k(Lx2/f0;)Landroidx/media3/exoplayer/s2;

    move-result-object v6

    iput-object v6, v1, Landroidx/media3/exoplayer/c1;->u0:Landroidx/media3/exoplayer/s2;

    invoke-interface {v4, v5, v15}, Lj2/a;->I(Landroidx/media3/common/h0;Landroid/os/Looper;)V

    sget v6, Le2/u0;->a:I

    const/16 v5, 0x1f

    if-ge v6, v5, :cond_2

    new-instance v5, Lj2/x3;

    invoke-direct {v5}, Lj2/x3;-><init>()V

    :goto_2
    move-object/from16 v23, v5

    goto :goto_3

    :catchall_0
    move-exception v0

    goto/16 :goto_a

    :cond_2
    iget-boolean v5, v0, Landroidx/media3/exoplayer/u$b;->B:Z

    invoke-static {v3, v1, v5}, Landroidx/media3/exoplayer/c1$c;->a(Landroid/content/Context;Landroidx/media3/exoplayer/c1;Z)Lj2/x3;

    move-result-object v5

    goto :goto_2

    :goto_3
    new-instance v5, Landroidx/media3/exoplayer/s1;

    iget-object v9, v0, Landroidx/media3/exoplayer/u$b;->g:Lcom/google/common/base/q;

    invoke-interface {v9}, Lcom/google/common/base/q;->get()Ljava/lang/Object;

    move-result-object v9

    check-cast v9, Landroidx/media3/exoplayer/v1;

    iget v12, v1, Landroidx/media3/exoplayer/c1;->H:I

    move-object/from16 v25, v13

    iget-boolean v13, v1, Landroidx/media3/exoplayer/c1;->I:Z

    move-object/from16 v26, v14

    iget-object v14, v1, Landroidx/media3/exoplayer/c1;->N:Landroidx/media3/exoplayer/b3;

    move-object/from16 v27, v15

    iget-object v15, v0, Landroidx/media3/exoplayer/u$b;->x:Landroidx/media3/exoplayer/u1;

    move-object/from16 v28, v2

    move-object/from16 v29, v3

    iget-wide v2, v0, Landroidx/media3/exoplayer/u$b;->y:J

    move-wide/from16 v30, v2

    iget-boolean v2, v1, Landroidx/media3/exoplayer/c1;->P:Z

    iget-object v3, v0, Landroidx/media3/exoplayer/u$b;->C:Landroid/os/Looper;

    move-object/from16 v32, v5

    move-object/from16 v5, v32

    move v0, v6

    move-object/from16 v33, v20

    const/16 v17, 0x0

    move-object v6, v7

    move-object v7, v10

    move/from16 v34, v21

    move-object/from16 v21, v8

    move-object v8, v11

    move-object/from16 v11, v22

    move-object/from16 v35, v10

    move-object v10, v11

    move-object/from16 v36, v11

    move v11, v12

    move/from16 v24, v0

    const/4 v0, 0x0

    move v12, v13

    move-object/from16 v37, v25

    move-object v13, v4

    move-object/from16 v20, v26

    move-object/from16 v38, v16

    move-wide/from16 v16, v30

    move/from16 v18, v2

    move-object/from16 v19, v27

    move-object/from16 v22, v23

    move-object/from16 v23, v3

    invoke-direct/range {v5 .. v23}, Landroidx/media3/exoplayer/s1;-><init>([Landroidx/media3/exoplayer/w2;Lx2/e0;Lx2/f0;Landroidx/media3/exoplayer/v1;Landroidx/media3/exoplayer/upstream/e;IZLj2/a;Landroidx/media3/exoplayer/b3;Landroidx/media3/exoplayer/u1;JZLandroid/os/Looper;Le2/d;Landroidx/media3/exoplayer/s1$f;Lj2/x3;Landroid/os/Looper;)V

    move-object/from16 v2, v32

    iput-object v2, v1, Landroidx/media3/exoplayer/c1;->k:Landroidx/media3/exoplayer/s1;

    const/high16 v3, 0x3f800000    # 1.0f

    iput v3, v1, Landroidx/media3/exoplayer/c1;->j0:F

    iput v0, v1, Landroidx/media3/exoplayer/c1;->H:I

    sget-object v3, Landroidx/media3/common/d0;->G:Landroidx/media3/common/d0;

    iput-object v3, v1, Landroidx/media3/exoplayer/c1;->R:Landroidx/media3/common/d0;

    iput-object v3, v1, Landroidx/media3/exoplayer/c1;->S:Landroidx/media3/common/d0;

    iput-object v3, v1, Landroidx/media3/exoplayer/c1;->t0:Landroidx/media3/common/d0;

    const/4 v3, -0x1

    iput v3, v1, Landroidx/media3/exoplayer/c1;->v0:I

    const/16 v3, 0x15

    move/from16 v5, v24

    if-ge v5, v3, :cond_3

    invoke-virtual {v1, v0}, Landroidx/media3/exoplayer/c1;->H1(I)I

    move-result v3

    iput v3, v1, Landroidx/media3/exoplayer/c1;->h0:I

    goto :goto_4

    :cond_3
    invoke-static/range {v29 .. v29}, Le2/u0;->J(Landroid/content/Context;)I

    move-result v3

    iput v3, v1, Landroidx/media3/exoplayer/c1;->h0:I

    :goto_4
    sget-object v3, Ld2/b;->c:Ld2/b;

    iput-object v3, v1, Landroidx/media3/exoplayer/c1;->l0:Ld2/b;

    const/4 v3, 0x1

    iput-boolean v3, v1, Landroidx/media3/exoplayer/c1;->m0:Z

    invoke-virtual {v1, v4}, Landroidx/media3/exoplayer/c1;->K(Landroidx/media3/common/h0$d;)V

    new-instance v6, Landroid/os/Handler;

    move-object/from16 v7, v27

    invoke-direct {v6, v7}, Landroid/os/Handler;-><init>(Landroid/os/Looper;)V

    move-object/from16 v8, v36

    invoke-interface {v8, v6, v4}, Landroidx/media3/exoplayer/upstream/e;->d(Landroid/os/Handler;Landroidx/media3/exoplayer/upstream/e$a;)V

    move-object/from16 v4, v38

    invoke-virtual {v1, v4}, Landroidx/media3/exoplayer/c1;->k1(Landroidx/media3/exoplayer/u$a;)V

    move v6, v5

    move-object/from16 v5, p1

    iget-wide v8, v5, Landroidx/media3/exoplayer/u$b;->c:J

    const-wide/16 v10, 0x0

    cmp-long v12, v8, v10

    if-lez v12, :cond_4

    invoke-virtual {v2, v8, v9}, Landroidx/media3/exoplayer/s1;->x(J)V

    :cond_4
    new-instance v2, Landroidx/media3/exoplayer/AudioBecomingNoisyManager;

    iget-object v8, v5, Landroidx/media3/exoplayer/u$b;->a:Landroid/content/Context;

    move-object/from16 v9, v33

    invoke-direct {v2, v8, v9, v4}, Landroidx/media3/exoplayer/AudioBecomingNoisyManager;-><init>(Landroid/content/Context;Landroid/os/Handler;Landroidx/media3/exoplayer/AudioBecomingNoisyManager$a;)V

    iput-object v2, v1, Landroidx/media3/exoplayer/c1;->z:Landroidx/media3/exoplayer/AudioBecomingNoisyManager;

    iget-boolean v8, v5, Landroidx/media3/exoplayer/u$b;->o:Z

    invoke-virtual {v2, v8}, Landroidx/media3/exoplayer/AudioBecomingNoisyManager;->b(Z)V

    new-instance v2, Landroidx/media3/exoplayer/l;

    iget-object v8, v5, Landroidx/media3/exoplayer/u$b;->a:Landroid/content/Context;

    invoke-direct {v2, v8, v9, v4}, Landroidx/media3/exoplayer/l;-><init>(Landroid/content/Context;Landroid/os/Handler;Landroidx/media3/exoplayer/l$b;)V

    iput-object v2, v1, Landroidx/media3/exoplayer/c1;->A:Landroidx/media3/exoplayer/l;

    iget-boolean v8, v5, Landroidx/media3/exoplayer/u$b;->m:Z

    if-eqz v8, :cond_5

    iget-object v14, v1, Landroidx/media3/exoplayer/c1;->i0:Landroidx/media3/common/d;

    goto :goto_5

    :cond_5
    const/4 v14, 0x0

    :goto_5
    invoke-virtual {v2, v14}, Landroidx/media3/exoplayer/l;->m(Landroidx/media3/common/d;)V

    if-eqz v34, :cond_6

    const/16 v2, 0x17

    if-lt v6, v2, :cond_6

    const-string v2, "audio"

    move-object/from16 v6, v29

    invoke-virtual {v6, v2}, Landroid/content/Context;->getSystemService(Ljava/lang/String;)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Landroid/media/AudioManager;

    iput-object v2, v1, Landroidx/media3/exoplayer/c1;->F:Landroid/media/AudioManager;

    new-instance v6, Landroidx/media3/exoplayer/c1$g;

    const/4 v8, 0x0

    invoke-direct {v6, v1, v8}, Landroidx/media3/exoplayer/c1$g;-><init>(Landroidx/media3/exoplayer/c1;Landroidx/media3/exoplayer/c1$a;)V

    new-instance v10, Landroid/os/Handler;

    invoke-direct {v10, v7}, Landroid/os/Handler;-><init>(Landroid/os/Looper;)V

    invoke-static {v2, v6, v10}, Landroidx/media3/exoplayer/c1$b;->b(Landroid/media/AudioManager;Landroid/media/AudioDeviceCallback;Landroid/os/Handler;)V

    goto :goto_6

    :cond_6
    const/4 v8, 0x0

    :goto_6
    iget-boolean v2, v5, Landroidx/media3/exoplayer/u$b;->q:Z

    if-eqz v2, :cond_7

    new-instance v2, Landroidx/media3/exoplayer/f3;

    iget-object v6, v5, Landroidx/media3/exoplayer/u$b;->a:Landroid/content/Context;

    invoke-direct {v2, v6, v9, v4}, Landroidx/media3/exoplayer/f3;-><init>(Landroid/content/Context;Landroid/os/Handler;Landroidx/media3/exoplayer/f3$b;)V

    iput-object v2, v1, Landroidx/media3/exoplayer/c1;->B:Landroidx/media3/exoplayer/f3;

    iget-object v4, v1, Landroidx/media3/exoplayer/c1;->i0:Landroidx/media3/common/d;

    iget v4, v4, Landroidx/media3/common/d;->c:I

    invoke-static {v4}, Le2/u0;->p0(I)I

    move-result v4

    invoke-virtual {v2, v4}, Landroidx/media3/exoplayer/f3;->j(I)V

    goto :goto_7

    :cond_7
    iput-object v8, v1, Landroidx/media3/exoplayer/c1;->B:Landroidx/media3/exoplayer/f3;

    :goto_7
    new-instance v2, Landroidx/media3/exoplayer/h3;

    iget-object v4, v5, Landroidx/media3/exoplayer/u$b;->a:Landroid/content/Context;

    invoke-direct {v2, v4}, Landroidx/media3/exoplayer/h3;-><init>(Landroid/content/Context;)V

    iput-object v2, v1, Landroidx/media3/exoplayer/c1;->C:Landroidx/media3/exoplayer/h3;

    iget v4, v5, Landroidx/media3/exoplayer/u$b;->n:I

    if-eqz v4, :cond_8

    const/4 v12, 0x1

    goto :goto_8

    :cond_8
    const/4 v12, 0x0

    :goto_8
    invoke-virtual {v2, v12}, Landroidx/media3/exoplayer/h3;->a(Z)V

    new-instance v2, Landroidx/media3/exoplayer/i3;

    iget-object v4, v5, Landroidx/media3/exoplayer/u$b;->a:Landroid/content/Context;

    invoke-direct {v2, v4}, Landroidx/media3/exoplayer/i3;-><init>(Landroid/content/Context;)V

    iput-object v2, v1, Landroidx/media3/exoplayer/c1;->D:Landroidx/media3/exoplayer/i3;

    iget v4, v5, Landroidx/media3/exoplayer/u$b;->n:I

    const/4 v5, 0x2

    if-ne v4, v5, :cond_9

    const/4 v12, 0x1

    goto :goto_9

    :cond_9
    const/4 v12, 0x0

    :goto_9
    invoke-virtual {v2, v12}, Landroidx/media3/exoplayer/i3;->a(Z)V

    iget-object v0, v1, Landroidx/media3/exoplayer/c1;->B:Landroidx/media3/exoplayer/f3;

    invoke-static {v0}, Landroidx/media3/exoplayer/c1;->s1(Landroidx/media3/exoplayer/f3;)Landroidx/media3/common/o;

    move-result-object v0

    iput-object v0, v1, Landroidx/media3/exoplayer/c1;->r0:Landroidx/media3/common/o;

    sget-object v0, Landroidx/media3/common/t0;->e:Landroidx/media3/common/t0;

    iput-object v0, v1, Landroidx/media3/exoplayer/c1;->s0:Landroidx/media3/common/t0;

    sget-object v0, Le2/e0;->c:Le2/e0;

    iput-object v0, v1, Landroidx/media3/exoplayer/c1;->e0:Le2/e0;

    iget-object v0, v1, Landroidx/media3/exoplayer/c1;->i0:Landroidx/media3/common/d;

    move-object/from16 v2, v35

    invoke-virtual {v2, v0}, Lx2/e0;->l(Landroidx/media3/common/d;)V

    iget v0, v1, Landroidx/media3/exoplayer/c1;->h0:I

    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v0

    const/16 v2, 0xa

    invoke-virtual {v1, v3, v2, v0}, Landroidx/media3/exoplayer/c1;->o2(IILjava/lang/Object;)V

    iget v0, v1, Landroidx/media3/exoplayer/c1;->h0:I

    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v0

    invoke-virtual {v1, v5, v2, v0}, Landroidx/media3/exoplayer/c1;->o2(IILjava/lang/Object;)V

    iget-object v0, v1, Landroidx/media3/exoplayer/c1;->i0:Landroidx/media3/common/d;

    const/4 v2, 0x3

    invoke-virtual {v1, v3, v2, v0}, Landroidx/media3/exoplayer/c1;->o2(IILjava/lang/Object;)V

    iget v0, v1, Landroidx/media3/exoplayer/c1;->c0:I

    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v0

    const/4 v2, 0x4

    invoke-virtual {v1, v5, v2, v0}, Landroidx/media3/exoplayer/c1;->o2(IILjava/lang/Object;)V

    iget v0, v1, Landroidx/media3/exoplayer/c1;->d0:I

    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v0

    const/4 v2, 0x5

    invoke-virtual {v1, v5, v2, v0}, Landroidx/media3/exoplayer/c1;->o2(IILjava/lang/Object;)V

    iget-boolean v0, v1, Landroidx/media3/exoplayer/c1;->k0:Z

    invoke-static {v0}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v0

    const/16 v2, 0x9

    invoke-virtual {v1, v3, v2, v0}, Landroidx/media3/exoplayer/c1;->o2(IILjava/lang/Object;)V

    const/4 v0, 0x7

    move-object/from16 v2, v37

    invoke-virtual {v1, v5, v0, v2}, Landroidx/media3/exoplayer/c1;->o2(IILjava/lang/Object;)V

    const/4 v0, 0x6

    const/16 v3, 0x8

    invoke-virtual {v1, v0, v3, v2}, Landroidx/media3/exoplayer/c1;->o2(IILjava/lang/Object;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    invoke-virtual/range {v28 .. v28}, Le2/g;->f()Z

    return-void

    :goto_a
    iget-object v2, v1, Landroidx/media3/exoplayer/c1;->d:Le2/g;

    invoke-virtual {v2}, Le2/g;->f()Z

    throw v0

    nop

    :array_0
    .array-data 4
        0x1
        0x2
        0x3
        0xd
        0xe
        0xf
        0x10
        0x11
        0x12
        0x13
        0x1f
        0x14
        0x1e
        0x15
        0x23
        0x16
        0x18
        0x1b
        0x1c
        0x20
    .end array-data
.end method

.method public static synthetic A0(Landroidx/media3/exoplayer/s2;Landroidx/media3/common/h0$d;)V
    .locals 0

    invoke-static {p0, p1}, Landroidx/media3/exoplayer/c1;->X1(Landroidx/media3/exoplayer/s2;Landroidx/media3/common/h0$d;)V

    return-void
.end method

.method public static synthetic B0(Landroidx/media3/exoplayer/s2;ILandroidx/media3/common/h0$d;)V
    .locals 0

    invoke-static {p0, p1, p2}, Landroidx/media3/exoplayer/c1;->T1(Landroidx/media3/exoplayer/s2;ILandroidx/media3/common/h0$d;)V

    return-void
.end method

.method public static B1(ZI)I
    .locals 1

    const/4 v0, 0x1

    if-eqz p0, :cond_0

    if-eq p1, v0, :cond_0

    const/4 v0, 0x2

    :cond_0
    return v0
.end method

.method public static synthetic C0(Landroidx/media3/common/d0;Landroidx/media3/common/h0$d;)V
    .locals 0

    invoke-static {p0, p1}, Landroidx/media3/exoplayer/c1;->Z1(Landroidx/media3/common/d0;Landroidx/media3/common/h0$d;)V

    return-void
.end method

.method public static synthetic D0(ILandroidx/media3/common/h0$d;)V
    .locals 0

    invoke-static {p0, p1}, Landroidx/media3/exoplayer/c1;->O1(ILandroidx/media3/common/h0$d;)V

    return-void
.end method

.method public static synthetic E0(Landroidx/media3/exoplayer/s2;Landroidx/media3/common/h0$d;)V
    .locals 0

    invoke-static {p0, p1}, Landroidx/media3/exoplayer/c1;->Y1(Landroidx/media3/exoplayer/s2;Landroidx/media3/common/h0$d;)V

    return-void
.end method

.method public static E1(Landroidx/media3/exoplayer/s2;)J
    .locals 7

    new-instance v0, Landroidx/media3/common/m0$c;

    invoke-direct {v0}, Landroidx/media3/common/m0$c;-><init>()V

    new-instance v1, Landroidx/media3/common/m0$b;

    invoke-direct {v1}, Landroidx/media3/common/m0$b;-><init>()V

    iget-object v2, p0, Landroidx/media3/exoplayer/s2;->a:Landroidx/media3/common/m0;

    iget-object v3, p0, Landroidx/media3/exoplayer/s2;->b:Landroidx/media3/exoplayer/source/l$b;

    iget-object v3, v3, Landroidx/media3/exoplayer/source/l$b;->a:Ljava/lang/Object;

    invoke-virtual {v2, v3, v1}, Landroidx/media3/common/m0;->h(Ljava/lang/Object;Landroidx/media3/common/m0$b;)Landroidx/media3/common/m0$b;

    iget-wide v2, p0, Landroidx/media3/exoplayer/s2;->c:J

    const-wide v4, -0x7fffffffffffffffL    # -4.9E-324

    cmp-long v6, v2, v4

    if-nez v6, :cond_0

    iget-object p0, p0, Landroidx/media3/exoplayer/s2;->a:Landroidx/media3/common/m0;

    iget v1, v1, Landroidx/media3/common/m0$b;->c:I

    invoke-virtual {p0, v1, v0}, Landroidx/media3/common/m0;->n(ILandroidx/media3/common/m0$c;)Landroidx/media3/common/m0$c;

    move-result-object p0

    invoke-virtual {p0}, Landroidx/media3/common/m0$c;->c()J

    move-result-wide v0

    goto :goto_0

    :cond_0
    invoke-virtual {v1}, Landroidx/media3/common/m0$b;->o()J

    move-result-wide v0

    iget-wide v2, p0, Landroidx/media3/exoplayer/s2;->c:J

    add-long/2addr v0, v2

    :goto_0
    return-wide v0
.end method

.method public static synthetic F0(Landroidx/media3/exoplayer/c1;Landroidx/media3/exoplayer/n;)Landroidx/media3/exoplayer/n;
    .locals 0

    iput-object p1, p0, Landroidx/media3/exoplayer/c1;->g0:Landroidx/media3/exoplayer/n;

    return-object p1
.end method

.method public static synthetic G0(Landroidx/media3/exoplayer/c1;Landroidx/media3/common/y;)Landroidx/media3/common/y;
    .locals 0

    iput-object p1, p0, Landroidx/media3/exoplayer/c1;->U:Landroidx/media3/common/y;

    return-object p1
.end method

.method public static synthetic H0(Landroidx/media3/exoplayer/c1;)Z
    .locals 0

    iget-boolean p0, p0, Landroidx/media3/exoplayer/c1;->k0:Z

    return p0
.end method

.method public static synthetic I0(Landroidx/media3/exoplayer/c1;Z)Z
    .locals 0

    iput-boolean p1, p0, Landroidx/media3/exoplayer/c1;->k0:Z

    return p1
.end method

.method public static synthetic J0(Landroidx/media3/exoplayer/c1;Ld2/b;)Ld2/b;
    .locals 0

    iput-object p1, p0, Landroidx/media3/exoplayer/c1;->l0:Ld2/b;

    return-object p1
.end method

.method public static synthetic J1(IILandroidx/media3/common/h0$d;)V
    .locals 0

    invoke-interface {p2, p0, p1}, Landroidx/media3/common/h0$d;->onSurfaceSizeChanged(II)V

    return-void
.end method

.method public static synthetic K0(Landroidx/media3/exoplayer/c1;)Landroidx/media3/common/d0;
    .locals 0

    iget-object p0, p0, Landroidx/media3/exoplayer/c1;->t0:Landroidx/media3/common/d0;

    return-object p0
.end method

.method public static synthetic L0(Landroidx/media3/exoplayer/c1;Landroidx/media3/common/d0;)Landroidx/media3/common/d0;
    .locals 0

    iput-object p1, p0, Landroidx/media3/exoplayer/c1;->t0:Landroidx/media3/common/d0;

    return-object p1
.end method

.method public static synthetic M0(Landroidx/media3/exoplayer/c1;)Landroidx/media3/common/d0;
    .locals 0

    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->o1()Landroidx/media3/common/d0;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic N0(Landroidx/media3/exoplayer/c1;)Landroidx/media3/common/d0;
    .locals 0

    iget-object p0, p0, Landroidx/media3/exoplayer/c1;->R:Landroidx/media3/common/d0;

    return-object p0
.end method

.method public static synthetic N1(Landroidx/media3/common/h0$d;)V
    .locals 2

    new-instance v0, Landroidx/media3/exoplayer/ExoTimeoutException;

    const/4 v1, 0x1

    invoke-direct {v0, v1}, Landroidx/media3/exoplayer/ExoTimeoutException;-><init>(I)V

    const/16 v1, 0x3eb

    invoke-static {v0, v1}, Landroidx/media3/exoplayer/ExoPlaybackException;->createForUnexpected(Ljava/lang/RuntimeException;I)Landroidx/media3/exoplayer/ExoPlaybackException;

    move-result-object v0

    invoke-interface {p0, v0}, Landroidx/media3/common/h0$d;->onPlayerError(Landroidx/media3/common/PlaybackException;)V

    return-void
.end method

.method public static synthetic O0(Landroidx/media3/exoplayer/c1;Landroidx/media3/common/d0;)Landroidx/media3/common/d0;
    .locals 0

    iput-object p1, p0, Landroidx/media3/exoplayer/c1;->R:Landroidx/media3/common/d0;

    return-object p1
.end method

.method public static synthetic O1(ILandroidx/media3/common/h0$d;)V
    .locals 0

    invoke-interface {p1, p0}, Landroidx/media3/common/h0$d;->onRepeatModeChanged(I)V

    return-void
.end method

.method public static synthetic P0(Landroidx/media3/exoplayer/c1;)Z
    .locals 0

    iget-boolean p0, p0, Landroidx/media3/exoplayer/c1;->a0:Z

    return p0
.end method

.method public static synthetic P1(ZLandroidx/media3/common/h0$d;)V
    .locals 0

    invoke-interface {p1, p0}, Landroidx/media3/common/h0$d;->onShuffleModeEnabledChanged(Z)V

    return-void
.end method

.method public static synthetic Q0(Landroidx/media3/exoplayer/c1;Ljava/lang/Object;)V
    .locals 0

    invoke-virtual {p0, p1}, Landroidx/media3/exoplayer/c1;->v2(Ljava/lang/Object;)V

    return-void
.end method

.method public static synthetic Q1(Landroidx/media3/common/p0;Landroidx/media3/common/h0$d;)V
    .locals 0

    invoke-interface {p1, p0}, Landroidx/media3/common/h0$d;->onTrackSelectionParametersChanged(Landroidx/media3/common/p0;)V

    return-void
.end method

.method public static synthetic R0(Landroidx/media3/exoplayer/c1;II)V
    .locals 0

    invoke-virtual {p0, p1, p2}, Landroidx/media3/exoplayer/c1;->j2(II)V

    return-void
.end method

.method public static synthetic R1(FLandroidx/media3/common/h0$d;)V
    .locals 0

    invoke-interface {p1, p0}, Landroidx/media3/common/h0$d;->onVolumeChanged(F)V

    return-void
.end method

.method public static synthetic S0(Landroidx/media3/exoplayer/c1;Landroid/graphics/SurfaceTexture;)V
    .locals 0

    invoke-virtual {p0, p1}, Landroidx/media3/exoplayer/c1;->u2(Landroid/graphics/SurfaceTexture;)V

    return-void
.end method

.method public static synthetic T0(Landroidx/media3/exoplayer/c1;)V
    .locals 0

    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->p2()V

    return-void
.end method

.method public static synthetic T1(Landroidx/media3/exoplayer/s2;ILandroidx/media3/common/h0$d;)V
    .locals 0

    iget-object p0, p0, Landroidx/media3/exoplayer/s2;->a:Landroidx/media3/common/m0;

    invoke-interface {p2, p0, p1}, Landroidx/media3/common/h0$d;->onTimelineChanged(Landroidx/media3/common/m0;I)V

    return-void
.end method

.method public static synthetic U0(ZI)I
    .locals 0

    invoke-static {p0, p1}, Landroidx/media3/exoplayer/c1;->B1(ZI)I

    move-result p0

    return p0
.end method

.method public static synthetic U1(ILandroidx/media3/common/h0$e;Landroidx/media3/common/h0$e;Landroidx/media3/common/h0$d;)V
    .locals 0

    invoke-interface {p3, p0}, Landroidx/media3/common/h0$d;->onPositionDiscontinuity(I)V

    invoke-interface {p3, p1, p2, p0}, Landroidx/media3/common/h0$d;->onPositionDiscontinuity(Landroidx/media3/common/h0$e;Landroidx/media3/common/h0$e;I)V

    return-void
.end method

.method public static synthetic V0(Landroidx/media3/exoplayer/c1;ZII)V
    .locals 0

    invoke-virtual {p0, p1, p2, p3}, Landroidx/media3/exoplayer/c1;->A2(ZII)V

    return-void
.end method

.method public static synthetic V1(Landroidx/media3/common/b0;ILandroidx/media3/common/h0$d;)V
    .locals 0

    invoke-interface {p2, p0, p1}, Landroidx/media3/common/h0$d;->onMediaItemTransition(Landroidx/media3/common/b0;I)V

    return-void
.end method

.method public static synthetic W0(Landroidx/media3/exoplayer/c1;)Landroidx/media3/exoplayer/f3;
    .locals 0

    iget-object p0, p0, Landroidx/media3/exoplayer/c1;->B:Landroidx/media3/exoplayer/f3;

    return-object p0
.end method

.method public static synthetic W1(Landroidx/media3/exoplayer/s2;Landroidx/media3/common/h0$d;)V
    .locals 0

    iget-object p0, p0, Landroidx/media3/exoplayer/s2;->f:Landroidx/media3/exoplayer/ExoPlaybackException;

    invoke-interface {p1, p0}, Landroidx/media3/common/h0$d;->onPlayerErrorChanged(Landroidx/media3/common/PlaybackException;)V

    return-void
.end method

.method public static synthetic X0(Landroidx/media3/exoplayer/f3;)Landroidx/media3/common/o;
    .locals 0

    invoke-static {p0}, Landroidx/media3/exoplayer/c1;->s1(Landroidx/media3/exoplayer/f3;)Landroidx/media3/common/o;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic X1(Landroidx/media3/exoplayer/s2;Landroidx/media3/common/h0$d;)V
    .locals 0

    iget-object p0, p0, Landroidx/media3/exoplayer/s2;->f:Landroidx/media3/exoplayer/ExoPlaybackException;

    invoke-interface {p1, p0}, Landroidx/media3/common/h0$d;->onPlayerError(Landroidx/media3/common/PlaybackException;)V

    return-void
.end method

.method public static synthetic Y0(Landroidx/media3/exoplayer/c1;)Landroidx/media3/common/o;
    .locals 0

    iget-object p0, p0, Landroidx/media3/exoplayer/c1;->r0:Landroidx/media3/common/o;

    return-object p0
.end method

.method public static synthetic Y1(Landroidx/media3/exoplayer/s2;Landroidx/media3/common/h0$d;)V
    .locals 0

    iget-object p0, p0, Landroidx/media3/exoplayer/s2;->i:Lx2/f0;

    iget-object p0, p0, Lx2/f0;->d:Landroidx/media3/common/q0;

    invoke-interface {p1, p0}, Landroidx/media3/common/h0$d;->onTracksChanged(Landroidx/media3/common/q0;)V

    return-void
.end method

.method public static synthetic Z0(Landroidx/media3/exoplayer/c1;Landroidx/media3/common/o;)Landroidx/media3/common/o;
    .locals 0

    iput-object p1, p0, Landroidx/media3/exoplayer/c1;->r0:Landroidx/media3/common/o;

    return-object p1
.end method

.method public static synthetic Z1(Landroidx/media3/common/d0;Landroidx/media3/common/h0$d;)V
    .locals 0

    invoke-interface {p1, p0}, Landroidx/media3/common/h0$d;->onMediaMetadataChanged(Landroidx/media3/common/d0;)V

    return-void
.end method

.method public static synthetic a1(Landroidx/media3/exoplayer/c1;)V
    .locals 0

    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->E2()V

    return-void
.end method

.method public static synthetic a2(Landroidx/media3/exoplayer/s2;Landroidx/media3/common/h0$d;)V
    .locals 1

    iget-boolean v0, p0, Landroidx/media3/exoplayer/s2;->g:Z

    invoke-interface {p1, v0}, Landroidx/media3/common/h0$d;->onLoadingChanged(Z)V

    iget-boolean p0, p0, Landroidx/media3/exoplayer/s2;->g:Z

    invoke-interface {p1, p0}, Landroidx/media3/common/h0$d;->onIsLoadingChanged(Z)V

    return-void
.end method

.method public static synthetic b1(Landroidx/media3/exoplayer/c1;)Z
    .locals 0

    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->G1()Z

    move-result p0

    return p0
.end method

.method public static synthetic b2(Landroidx/media3/exoplayer/s2;Landroidx/media3/common/h0$d;)V
    .locals 1

    iget-boolean v0, p0, Landroidx/media3/exoplayer/s2;->l:Z

    iget p0, p0, Landroidx/media3/exoplayer/s2;->e:I

    invoke-interface {p1, v0, p0}, Landroidx/media3/common/h0$d;->onPlayerStateChanged(ZI)V

    return-void
.end method

.method public static synthetic c1(Landroidx/media3/exoplayer/c1;)Landroidx/media3/exoplayer/s2;
    .locals 0

    iget-object p0, p0, Landroidx/media3/exoplayer/c1;->u0:Landroidx/media3/exoplayer/s2;

    return-object p0
.end method

.method public static synthetic c2(Landroidx/media3/exoplayer/s2;Landroidx/media3/common/h0$d;)V
    .locals 0

    iget p0, p0, Landroidx/media3/exoplayer/s2;->e:I

    invoke-interface {p1, p0}, Landroidx/media3/common/h0$d;->onPlaybackStateChanged(I)V

    return-void
.end method

.method public static synthetic d1(Landroidx/media3/exoplayer/c1;ZII)V
    .locals 0

    invoke-virtual {p0, p1, p2, p3}, Landroidx/media3/exoplayer/c1;->C2(ZII)V

    return-void
.end method

.method public static synthetic d2(Landroidx/media3/exoplayer/s2;ILandroidx/media3/common/h0$d;)V
    .locals 0

    iget-boolean p0, p0, Landroidx/media3/exoplayer/s2;->l:Z

    invoke-interface {p2, p0, p1}, Landroidx/media3/common/h0$d;->onPlayWhenReadyChanged(ZI)V

    return-void
.end method

.method public static synthetic e1(Landroidx/media3/exoplayer/c1;Landroidx/media3/exoplayer/n;)Landroidx/media3/exoplayer/n;
    .locals 0

    iput-object p1, p0, Landroidx/media3/exoplayer/c1;->f0:Landroidx/media3/exoplayer/n;

    return-object p1
.end method

.method public static synthetic e2(Landroidx/media3/exoplayer/s2;Landroidx/media3/common/h0$d;)V
    .locals 0

    iget p0, p0, Landroidx/media3/exoplayer/s2;->m:I

    invoke-interface {p1, p0}, Landroidx/media3/common/h0$d;->onPlaybackSuppressionReasonChanged(I)V

    return-void
.end method

.method public static synthetic f1(Landroidx/media3/exoplayer/c1;)Lj2/a;
    .locals 0

    iget-object p0, p0, Landroidx/media3/exoplayer/c1;->r:Lj2/a;

    return-object p0
.end method

.method public static synthetic f2(Landroidx/media3/exoplayer/s2;Landroidx/media3/common/h0$d;)V
    .locals 0

    invoke-virtual {p0}, Landroidx/media3/exoplayer/s2;->n()Z

    move-result p0

    invoke-interface {p1, p0}, Landroidx/media3/common/h0$d;->onIsPlayingChanged(Z)V

    return-void
.end method

.method public static synthetic g1(Landroidx/media3/exoplayer/c1;Landroidx/media3/common/y;)Landroidx/media3/common/y;
    .locals 0

    iput-object p1, p0, Landroidx/media3/exoplayer/c1;->T:Landroidx/media3/common/y;

    return-object p1
.end method

.method public static synthetic g2(Landroidx/media3/exoplayer/s2;Landroidx/media3/common/h0$d;)V
    .locals 0

    iget-object p0, p0, Landroidx/media3/exoplayer/s2;->n:Landroidx/media3/common/g0;

    invoke-interface {p1, p0}, Landroidx/media3/common/h0$d;->onPlaybackParametersChanged(Landroidx/media3/common/g0;)V

    return-void
.end method

.method public static synthetic h0(Landroidx/media3/exoplayer/s2;Landroidx/media3/common/h0$d;)V
    .locals 0

    invoke-static {p0, p1}, Landroidx/media3/exoplayer/c1;->a2(Landroidx/media3/exoplayer/s2;Landroidx/media3/common/h0$d;)V

    return-void
.end method

.method public static synthetic h1(Landroidx/media3/exoplayer/c1;Landroidx/media3/common/t0;)Landroidx/media3/common/t0;
    .locals 0

    iput-object p1, p0, Landroidx/media3/exoplayer/c1;->s0:Landroidx/media3/common/t0;

    return-object p1
.end method

.method public static synthetic i0(FLandroidx/media3/common/h0$d;)V
    .locals 0

    invoke-static {p0, p1}, Landroidx/media3/exoplayer/c1;->R1(FLandroidx/media3/common/h0$d;)V

    return-void
.end method

.method public static synthetic i1(Landroidx/media3/exoplayer/c1;)Le2/n;
    .locals 0

    iget-object p0, p0, Landroidx/media3/exoplayer/c1;->l:Le2/n;

    return-object p0
.end method

.method public static synthetic j0(Landroidx/media3/exoplayer/s2;ILandroidx/media3/common/h0$d;)V
    .locals 0

    invoke-static {p0, p1, p2}, Landroidx/media3/exoplayer/c1;->d2(Landroidx/media3/exoplayer/s2;ILandroidx/media3/common/h0$d;)V

    return-void
.end method

.method public static synthetic j1(Landroidx/media3/exoplayer/c1;)Ljava/lang/Object;
    .locals 0

    iget-object p0, p0, Landroidx/media3/exoplayer/c1;->W:Ljava/lang/Object;

    return-object p0
.end method

.method public static synthetic k0(Landroidx/media3/exoplayer/s2;Landroidx/media3/common/h0$d;)V
    .locals 0

    invoke-static {p0, p1}, Landroidx/media3/exoplayer/c1;->b2(Landroidx/media3/exoplayer/s2;Landroidx/media3/common/h0$d;)V

    return-void
.end method

.method public static synthetic l0(Landroidx/media3/exoplayer/c1;Landroidx/media3/common/h0$d;Landroidx/media3/common/s;)V
    .locals 0

    invoke-virtual {p0, p1, p2}, Landroidx/media3/exoplayer/c1;->K1(Landroidx/media3/common/h0$d;Landroidx/media3/common/s;)V

    return-void
.end method

.method public static synthetic m0(Landroidx/media3/exoplayer/s2;Landroidx/media3/common/h0$d;)V
    .locals 0

    invoke-static {p0, p1}, Landroidx/media3/exoplayer/c1;->f2(Landroidx/media3/exoplayer/s2;Landroidx/media3/common/h0$d;)V

    return-void
.end method

.method public static synthetic n0(Landroidx/media3/common/h0$d;)V
    .locals 0

    invoke-static {p0}, Landroidx/media3/exoplayer/c1;->N1(Landroidx/media3/common/h0$d;)V

    return-void
.end method

.method public static synthetic o0(Landroidx/media3/exoplayer/s2;Landroidx/media3/common/h0$d;)V
    .locals 0

    invoke-static {p0, p1}, Landroidx/media3/exoplayer/c1;->W1(Landroidx/media3/exoplayer/s2;Landroidx/media3/common/h0$d;)V

    return-void
.end method

.method public static synthetic p0(IILandroidx/media3/common/h0$d;)V
    .locals 0

    invoke-static {p0, p1, p2}, Landroidx/media3/exoplayer/c1;->J1(IILandroidx/media3/common/h0$d;)V

    return-void
.end method

.method public static synthetic q0(Landroidx/media3/exoplayer/s2;Landroidx/media3/common/h0$d;)V
    .locals 0

    invoke-static {p0, p1}, Landroidx/media3/exoplayer/c1;->g2(Landroidx/media3/exoplayer/s2;Landroidx/media3/common/h0$d;)V

    return-void
.end method

.method public static synthetic r0(Landroidx/media3/common/b0;ILandroidx/media3/common/h0$d;)V
    .locals 0

    invoke-static {p0, p1, p2}, Landroidx/media3/exoplayer/c1;->V1(Landroidx/media3/common/b0;ILandroidx/media3/common/h0$d;)V

    return-void
.end method

.method public static synthetic s0(ILandroidx/media3/common/h0$e;Landroidx/media3/common/h0$e;Landroidx/media3/common/h0$d;)V
    .locals 0

    invoke-static {p0, p1, p2, p3}, Landroidx/media3/exoplayer/c1;->U1(ILandroidx/media3/common/h0$e;Landroidx/media3/common/h0$e;Landroidx/media3/common/h0$d;)V

    return-void
.end method

.method public static s1(Landroidx/media3/exoplayer/f3;)Landroidx/media3/common/o;
    .locals 3
    .param p0    # Landroidx/media3/exoplayer/f3;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    new-instance v0, Landroidx/media3/common/o$b;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Landroidx/media3/common/o$b;-><init>(I)V

    if-eqz p0, :cond_0

    invoke-virtual {p0}, Landroidx/media3/exoplayer/f3;->d()I

    move-result v2

    goto :goto_0

    :cond_0
    const/4 v2, 0x0

    :goto_0
    invoke-virtual {v0, v2}, Landroidx/media3/common/o$b;->g(I)Landroidx/media3/common/o$b;

    move-result-object v0

    if-eqz p0, :cond_1

    invoke-virtual {p0}, Landroidx/media3/exoplayer/f3;->c()I

    move-result v1

    :cond_1
    invoke-virtual {v0, v1}, Landroidx/media3/common/o$b;->f(I)Landroidx/media3/common/o$b;

    move-result-object p0

    invoke-virtual {p0}, Landroidx/media3/common/o$b;->e()Landroidx/media3/common/o;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic t0(Landroidx/media3/exoplayer/c1;Landroidx/media3/exoplayer/s1$e;)V
    .locals 0

    invoke-virtual {p0, p1}, Landroidx/media3/exoplayer/c1;->L1(Landroidx/media3/exoplayer/s1$e;)V

    return-void
.end method

.method public static synthetic u0(Landroidx/media3/common/p0;Landroidx/media3/common/h0$d;)V
    .locals 0

    invoke-static {p0, p1}, Landroidx/media3/exoplayer/c1;->Q1(Landroidx/media3/common/p0;Landroidx/media3/common/h0$d;)V

    return-void
.end method

.method public static synthetic v0(Landroidx/media3/exoplayer/c1;Landroidx/media3/exoplayer/s1$e;)V
    .locals 0

    invoke-virtual {p0, p1}, Landroidx/media3/exoplayer/c1;->M1(Landroidx/media3/exoplayer/s1$e;)V

    return-void
.end method

.method public static synthetic w0(ZLandroidx/media3/common/h0$d;)V
    .locals 0

    invoke-static {p0, p1}, Landroidx/media3/exoplayer/c1;->P1(ZLandroidx/media3/common/h0$d;)V

    return-void
.end method

.method public static synthetic x0(Landroidx/media3/exoplayer/s2;Landroidx/media3/common/h0$d;)V
    .locals 0

    invoke-static {p0, p1}, Landroidx/media3/exoplayer/c1;->c2(Landroidx/media3/exoplayer/s2;Landroidx/media3/common/h0$d;)V

    return-void
.end method

.method public static synthetic y0(Landroidx/media3/exoplayer/s2;Landroidx/media3/common/h0$d;)V
    .locals 0

    invoke-static {p0, p1}, Landroidx/media3/exoplayer/c1;->e2(Landroidx/media3/exoplayer/s2;Landroidx/media3/common/h0$d;)V

    return-void
.end method

.method public static synthetic z0(Landroidx/media3/exoplayer/c1;Landroidx/media3/common/h0$d;)V
    .locals 0

    invoke-virtual {p0, p1}, Landroidx/media3/exoplayer/c1;->S1(Landroidx/media3/common/h0$d;)V

    return-void
.end method


# virtual methods
.method public final A1(Landroidx/media3/common/m0;Landroidx/media3/common/m0;IJ)Landroid/util/Pair;
    .locals 12
    .annotation build Landroidx/annotation/Nullable;
    .end annotation

    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroidx/media3/common/m0;",
            "Landroidx/media3/common/m0;",
            "IJ)",
            "Landroid/util/Pair<",
            "Ljava/lang/Object;",
            "Ljava/lang/Long;",
            ">;"
        }
    .end annotation

    move-object v0, p0

    move-object v8, p2

    invoke-virtual {p1}, Landroidx/media3/common/m0;->q()Z

    move-result v1

    const-wide v9, -0x7fffffffffffffffL    # -4.9E-324

    const/4 v11, -0x1

    if-nez v1, :cond_3

    invoke-virtual {p2}, Landroidx/media3/common/m0;->q()Z

    move-result v1

    if-eqz v1, :cond_0

    goto :goto_0

    :cond_0
    iget-object v3, v0, Landroidx/media3/common/h;->a:Landroidx/media3/common/m0$c;

    iget-object v4, v0, Landroidx/media3/exoplayer/c1;->n:Landroidx/media3/common/m0$b;

    invoke-static/range {p4 .. p5}, Le2/u0;->S0(J)J

    move-result-wide v6

    move-object v2, p1

    move v5, p3

    invoke-virtual/range {v2 .. v7}, Landroidx/media3/common/m0;->j(Landroidx/media3/common/m0$c;Landroidx/media3/common/m0$b;IJ)Landroid/util/Pair;

    move-result-object v1

    invoke-static {v1}, Le2/u0;->i(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Landroid/util/Pair;

    iget-object v5, v2, Landroid/util/Pair;->first:Ljava/lang/Object;

    invoke-virtual {p2, v5}, Landroidx/media3/common/m0;->b(Ljava/lang/Object;)I

    move-result v2

    if-eq v2, v11, :cond_1

    return-object v1

    :cond_1
    iget-object v1, v0, Landroidx/media3/common/h;->a:Landroidx/media3/common/m0$c;

    iget-object v2, v0, Landroidx/media3/exoplayer/c1;->n:Landroidx/media3/common/m0$b;

    iget v3, v0, Landroidx/media3/exoplayer/c1;->H:I

    iget-boolean v4, v0, Landroidx/media3/exoplayer/c1;->I:Z

    move-object v6, p1

    move-object v7, p2

    invoke-static/range {v1 .. v7}, Landroidx/media3/exoplayer/s1;->E0(Landroidx/media3/common/m0$c;Landroidx/media3/common/m0$b;IZLjava/lang/Object;Landroidx/media3/common/m0;Landroidx/media3/common/m0;)Ljava/lang/Object;

    move-result-object v1

    if-eqz v1, :cond_2

    iget-object v2, v0, Landroidx/media3/exoplayer/c1;->n:Landroidx/media3/common/m0$b;

    invoke-virtual {p2, v1, v2}, Landroidx/media3/common/m0;->h(Ljava/lang/Object;Landroidx/media3/common/m0$b;)Landroidx/media3/common/m0$b;

    iget-object v1, v0, Landroidx/media3/exoplayer/c1;->n:Landroidx/media3/common/m0$b;

    iget v1, v1, Landroidx/media3/common/m0$b;->c:I

    iget-object v2, v0, Landroidx/media3/common/h;->a:Landroidx/media3/common/m0$c;

    invoke-virtual {p2, v1, v2}, Landroidx/media3/common/m0;->n(ILandroidx/media3/common/m0$c;)Landroidx/media3/common/m0$c;

    move-result-object v2

    invoke-virtual {v2}, Landroidx/media3/common/m0$c;->b()J

    move-result-wide v2

    invoke-virtual {p0, p2, v1, v2, v3}, Landroidx/media3/exoplayer/c1;->i2(Landroidx/media3/common/m0;IJ)Landroid/util/Pair;

    move-result-object v1

    return-object v1

    :cond_2
    invoke-virtual {p0, p2, v11, v9, v10}, Landroidx/media3/exoplayer/c1;->i2(Landroidx/media3/common/m0;IJ)Landroid/util/Pair;

    move-result-object v1

    return-object v1

    :cond_3
    :goto_0
    invoke-virtual {p1}, Landroidx/media3/common/m0;->q()Z

    move-result v1

    if-nez v1, :cond_4

    invoke-virtual {p2}, Landroidx/media3/common/m0;->q()Z

    move-result v1

    if-eqz v1, :cond_4

    const/4 v1, 0x1

    goto :goto_1

    :cond_4
    const/4 v1, 0x0

    :goto_1
    if-eqz v1, :cond_5

    goto :goto_2

    :cond_5
    move v11, p3

    :goto_2
    if-eqz v1, :cond_6

    goto :goto_3

    :cond_6
    move-wide/from16 v9, p4

    :goto_3
    invoke-virtual {p0, p2, v11, v9, v10}, Landroidx/media3/exoplayer/c1;->i2(Landroidx/media3/common/m0;IJ)Landroid/util/Pair;

    move-result-object v1

    return-object v1
.end method

.method public final A2(ZII)V
    .locals 2

    if-eqz p1, :cond_0

    const/4 p1, -0x1

    if-eq p2, p1, :cond_0

    const/4 p1, 0x1

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    :goto_0
    invoke-virtual {p0, p1, p2}, Landroidx/media3/exoplayer/c1;->r1(ZI)I

    move-result p2

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->u0:Landroidx/media3/exoplayer/s2;

    iget-boolean v1, v0, Landroidx/media3/exoplayer/s2;->l:Z

    if-ne v1, p1, :cond_1

    iget v0, v0, Landroidx/media3/exoplayer/s2;->m:I

    if-ne v0, p2, :cond_1

    return-void

    :cond_1
    invoke-virtual {p0, p1, p3, p2}, Landroidx/media3/exoplayer/c1;->C2(ZII)V

    return-void
.end method

.method public B(ZI)V
    .locals 1

    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->F2()V

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->B:Landroidx/media3/exoplayer/f3;

    if-eqz v0, :cond_0

    invoke-virtual {v0, p1, p2}, Landroidx/media3/exoplayer/f3;->i(ZI)V

    :cond_0
    return-void
.end method

.method public final B2(Landroidx/media3/exoplayer/s2;IIZIJIZ)V
    .locals 16

    move-object/from16 v7, p0

    move-object/from16 v8, p1

    move/from16 v9, p5

    iget-object v10, v7, Landroidx/media3/exoplayer/c1;->u0:Landroidx/media3/exoplayer/s2;

    iput-object v8, v7, Landroidx/media3/exoplayer/c1;->u0:Landroidx/media3/exoplayer/s2;

    iget-object v0, v10, Landroidx/media3/exoplayer/s2;->a:Landroidx/media3/common/m0;

    iget-object v1, v8, Landroidx/media3/exoplayer/s2;->a:Landroidx/media3/common/m0;

    invoke-virtual {v0, v1}, Landroidx/media3/common/m0;->equals(Ljava/lang/Object;)Z

    move-result v0

    const/4 v11, 0x1

    xor-int/lit8 v12, v0, 0x1

    move-object/from16 v0, p0

    move-object/from16 v1, p1

    move-object v2, v10

    move/from16 v3, p4

    move/from16 v4, p5

    move v5, v12

    move/from16 v6, p9

    invoke-virtual/range {v0 .. v6}, Landroidx/media3/exoplayer/c1;->w1(Landroidx/media3/exoplayer/s2;Landroidx/media3/exoplayer/s2;ZIZZ)Landroid/util/Pair;

    move-result-object v0

    iget-object v1, v0, Landroid/util/Pair;->first:Ljava/lang/Object;

    check-cast v1, Ljava/lang/Boolean;

    invoke-virtual {v1}, Ljava/lang/Boolean;->booleanValue()Z

    move-result v1

    iget-object v0, v0, Landroid/util/Pair;->second:Ljava/lang/Object;

    check-cast v0, Ljava/lang/Integer;

    invoke-virtual {v0}, Ljava/lang/Integer;->intValue()I

    move-result v0

    const/4 v2, 0x0

    if-eqz v1, :cond_1

    iget-object v3, v8, Landroidx/media3/exoplayer/s2;->a:Landroidx/media3/common/m0;

    invoke-virtual {v3}, Landroidx/media3/common/m0;->q()Z

    move-result v3

    if-nez v3, :cond_0

    iget-object v2, v8, Landroidx/media3/exoplayer/s2;->a:Landroidx/media3/common/m0;

    iget-object v3, v8, Landroidx/media3/exoplayer/s2;->b:Landroidx/media3/exoplayer/source/l$b;

    iget-object v3, v3, Landroidx/media3/exoplayer/source/l$b;->a:Ljava/lang/Object;

    iget-object v4, v7, Landroidx/media3/exoplayer/c1;->n:Landroidx/media3/common/m0$b;

    invoke-virtual {v2, v3, v4}, Landroidx/media3/common/m0;->h(Ljava/lang/Object;Landroidx/media3/common/m0$b;)Landroidx/media3/common/m0$b;

    move-result-object v2

    iget v2, v2, Landroidx/media3/common/m0$b;->c:I

    iget-object v3, v8, Landroidx/media3/exoplayer/s2;->a:Landroidx/media3/common/m0;

    iget-object v4, v7, Landroidx/media3/common/h;->a:Landroidx/media3/common/m0$c;

    invoke-virtual {v3, v2, v4}, Landroidx/media3/common/m0;->n(ILandroidx/media3/common/m0$c;)Landroidx/media3/common/m0$c;

    move-result-object v2

    iget-object v2, v2, Landroidx/media3/common/m0$c;->c:Landroidx/media3/common/b0;

    :cond_0
    sget-object v3, Landroidx/media3/common/d0;->G:Landroidx/media3/common/d0;

    iput-object v3, v7, Landroidx/media3/exoplayer/c1;->t0:Landroidx/media3/common/d0;

    :cond_1
    if-nez v1, :cond_2

    iget-object v3, v10, Landroidx/media3/exoplayer/s2;->j:Ljava/util/List;

    iget-object v4, v8, Landroidx/media3/exoplayer/s2;->j:Ljava/util/List;

    invoke-interface {v3, v4}, Ljava/util/List;->equals(Ljava/lang/Object;)Z

    move-result v3

    if-nez v3, :cond_3

    :cond_2
    iget-object v3, v7, Landroidx/media3/exoplayer/c1;->t0:Landroidx/media3/common/d0;

    invoke-virtual {v3}, Landroidx/media3/common/d0;->a()Landroidx/media3/common/d0$b;

    move-result-object v3

    iget-object v4, v8, Landroidx/media3/exoplayer/s2;->j:Ljava/util/List;

    invoke-virtual {v3, v4}, Landroidx/media3/common/d0$b;->L(Ljava/util/List;)Landroidx/media3/common/d0$b;

    move-result-object v3

    invoke-virtual {v3}, Landroidx/media3/common/d0$b;->H()Landroidx/media3/common/d0;

    move-result-object v3

    iput-object v3, v7, Landroidx/media3/exoplayer/c1;->t0:Landroidx/media3/common/d0;

    :cond_3
    invoke-virtual/range {p0 .. p0}, Landroidx/media3/exoplayer/c1;->o1()Landroidx/media3/common/d0;

    move-result-object v3

    iget-object v4, v7, Landroidx/media3/exoplayer/c1;->R:Landroidx/media3/common/d0;

    invoke-virtual {v3, v4}, Landroidx/media3/common/d0;->equals(Ljava/lang/Object;)Z

    move-result v4

    xor-int/2addr v4, v11

    iput-object v3, v7, Landroidx/media3/exoplayer/c1;->R:Landroidx/media3/common/d0;

    iget-boolean v3, v10, Landroidx/media3/exoplayer/s2;->l:Z

    iget-boolean v5, v8, Landroidx/media3/exoplayer/s2;->l:Z

    const/4 v6, 0x0

    if-eq v3, v5, :cond_4

    const/4 v3, 0x1

    goto :goto_0

    :cond_4
    const/4 v3, 0x0

    :goto_0
    iget v5, v10, Landroidx/media3/exoplayer/s2;->e:I

    iget v13, v8, Landroidx/media3/exoplayer/s2;->e:I

    if-eq v5, v13, :cond_5

    const/4 v5, 0x1

    goto :goto_1

    :cond_5
    const/4 v5, 0x0

    :goto_1
    if-nez v5, :cond_6

    if-eqz v3, :cond_7

    :cond_6
    invoke-virtual/range {p0 .. p0}, Landroidx/media3/exoplayer/c1;->E2()V

    :cond_7
    iget-boolean v13, v10, Landroidx/media3/exoplayer/s2;->g:Z

    iget-boolean v14, v8, Landroidx/media3/exoplayer/s2;->g:Z

    if-eq v13, v14, :cond_8

    const/4 v13, 0x1

    goto :goto_2

    :cond_8
    const/4 v13, 0x0

    :goto_2
    if-eqz v13, :cond_9

    invoke-virtual {v7, v14}, Landroidx/media3/exoplayer/c1;->D2(Z)V

    :cond_9
    if-eqz v12, :cond_a

    iget-object v12, v7, Landroidx/media3/exoplayer/c1;->l:Le2/n;

    new-instance v14, Landroidx/media3/exoplayer/u0;

    move/from16 v15, p2

    invoke-direct {v14, v8, v15}, Landroidx/media3/exoplayer/u0;-><init>(Landroidx/media3/exoplayer/s2;I)V

    invoke-virtual {v12, v6, v14}, Le2/n;->i(ILe2/n$a;)V

    :cond_a
    if-eqz p4, :cond_b

    move/from16 v6, p8

    invoke-virtual {v7, v9, v10, v6}, Landroidx/media3/exoplayer/c1;->D1(ILandroidx/media3/exoplayer/s2;I)Landroidx/media3/common/h0$e;

    move-result-object v6

    move-wide/from16 v14, p6

    invoke-virtual {v7, v14, v15}, Landroidx/media3/exoplayer/c1;->C1(J)Landroidx/media3/common/h0$e;

    move-result-object v12

    iget-object v14, v7, Landroidx/media3/exoplayer/c1;->l:Le2/n;

    new-instance v15, Landroidx/media3/exoplayer/z0;

    invoke-direct {v15, v9, v6, v12}, Landroidx/media3/exoplayer/z0;-><init>(ILandroidx/media3/common/h0$e;Landroidx/media3/common/h0$e;)V

    const/16 v6, 0xb

    invoke-virtual {v14, v6, v15}, Le2/n;->i(ILe2/n$a;)V

    :cond_b
    if-eqz v1, :cond_c

    iget-object v1, v7, Landroidx/media3/exoplayer/c1;->l:Le2/n;

    new-instance v6, Landroidx/media3/exoplayer/a1;

    invoke-direct {v6, v2, v0}, Landroidx/media3/exoplayer/a1;-><init>(Landroidx/media3/common/b0;I)V

    invoke-virtual {v1, v11, v6}, Le2/n;->i(ILe2/n$a;)V

    :cond_c
    iget-object v0, v10, Landroidx/media3/exoplayer/s2;->f:Landroidx/media3/exoplayer/ExoPlaybackException;

    iget-object v1, v8, Landroidx/media3/exoplayer/s2;->f:Landroidx/media3/exoplayer/ExoPlaybackException;

    if-eq v0, v1, :cond_d

    iget-object v0, v7, Landroidx/media3/exoplayer/c1;->l:Le2/n;

    new-instance v1, Landroidx/media3/exoplayer/b1;

    invoke-direct {v1, v8}, Landroidx/media3/exoplayer/b1;-><init>(Landroidx/media3/exoplayer/s2;)V

    const/16 v2, 0xa

    invoke-virtual {v0, v2, v1}, Le2/n;->i(ILe2/n$a;)V

    iget-object v0, v8, Landroidx/media3/exoplayer/s2;->f:Landroidx/media3/exoplayer/ExoPlaybackException;

    if-eqz v0, :cond_d

    iget-object v0, v7, Landroidx/media3/exoplayer/c1;->l:Le2/n;

    new-instance v1, Landroidx/media3/exoplayer/e0;

    invoke-direct {v1, v8}, Landroidx/media3/exoplayer/e0;-><init>(Landroidx/media3/exoplayer/s2;)V

    invoke-virtual {v0, v2, v1}, Le2/n;->i(ILe2/n$a;)V

    :cond_d
    iget-object v0, v10, Landroidx/media3/exoplayer/s2;->i:Lx2/f0;

    iget-object v1, v8, Landroidx/media3/exoplayer/s2;->i:Lx2/f0;

    if-eq v0, v1, :cond_e

    iget-object v0, v7, Landroidx/media3/exoplayer/c1;->h:Lx2/e0;

    iget-object v1, v1, Lx2/f0;->e:Ljava/lang/Object;

    invoke-virtual {v0, v1}, Lx2/e0;->i(Ljava/lang/Object;)V

    iget-object v0, v7, Landroidx/media3/exoplayer/c1;->l:Le2/n;

    new-instance v1, Landroidx/media3/exoplayer/f0;

    invoke-direct {v1, v8}, Landroidx/media3/exoplayer/f0;-><init>(Landroidx/media3/exoplayer/s2;)V

    const/4 v2, 0x2

    invoke-virtual {v0, v2, v1}, Le2/n;->i(ILe2/n$a;)V

    :cond_e
    if-eqz v4, :cond_f

    iget-object v0, v7, Landroidx/media3/exoplayer/c1;->R:Landroidx/media3/common/d0;

    iget-object v1, v7, Landroidx/media3/exoplayer/c1;->l:Le2/n;

    new-instance v2, Landroidx/media3/exoplayer/g0;

    invoke-direct {v2, v0}, Landroidx/media3/exoplayer/g0;-><init>(Landroidx/media3/common/d0;)V

    const/16 v0, 0xe

    invoke-virtual {v1, v0, v2}, Le2/n;->i(ILe2/n$a;)V

    :cond_f
    if-eqz v13, :cond_10

    iget-object v0, v7, Landroidx/media3/exoplayer/c1;->l:Le2/n;

    new-instance v1, Landroidx/media3/exoplayer/h0;

    invoke-direct {v1, v8}, Landroidx/media3/exoplayer/h0;-><init>(Landroidx/media3/exoplayer/s2;)V

    const/4 v2, 0x3

    invoke-virtual {v0, v2, v1}, Le2/n;->i(ILe2/n$a;)V

    :cond_10
    if-nez v5, :cond_11

    if-eqz v3, :cond_12

    :cond_11
    iget-object v0, v7, Landroidx/media3/exoplayer/c1;->l:Le2/n;

    new-instance v1, Landroidx/media3/exoplayer/i0;

    invoke-direct {v1, v8}, Landroidx/media3/exoplayer/i0;-><init>(Landroidx/media3/exoplayer/s2;)V

    const/4 v2, -0x1

    invoke-virtual {v0, v2, v1}, Le2/n;->i(ILe2/n$a;)V

    :cond_12
    if-eqz v5, :cond_13

    iget-object v0, v7, Landroidx/media3/exoplayer/c1;->l:Le2/n;

    new-instance v1, Landroidx/media3/exoplayer/j0;

    invoke-direct {v1, v8}, Landroidx/media3/exoplayer/j0;-><init>(Landroidx/media3/exoplayer/s2;)V

    const/4 v2, 0x4

    invoke-virtual {v0, v2, v1}, Le2/n;->i(ILe2/n$a;)V

    :cond_13
    if-eqz v3, :cond_14

    iget-object v0, v7, Landroidx/media3/exoplayer/c1;->l:Le2/n;

    new-instance v1, Landroidx/media3/exoplayer/v0;

    move/from16 v2, p3

    invoke-direct {v1, v8, v2}, Landroidx/media3/exoplayer/v0;-><init>(Landroidx/media3/exoplayer/s2;I)V

    const/4 v2, 0x5

    invoke-virtual {v0, v2, v1}, Le2/n;->i(ILe2/n$a;)V

    :cond_14
    iget v0, v10, Landroidx/media3/exoplayer/s2;->m:I

    iget v1, v8, Landroidx/media3/exoplayer/s2;->m:I

    if-eq v0, v1, :cond_15

    iget-object v0, v7, Landroidx/media3/exoplayer/c1;->l:Le2/n;

    new-instance v1, Landroidx/media3/exoplayer/w0;

    invoke-direct {v1, v8}, Landroidx/media3/exoplayer/w0;-><init>(Landroidx/media3/exoplayer/s2;)V

    const/4 v2, 0x6

    invoke-virtual {v0, v2, v1}, Le2/n;->i(ILe2/n$a;)V

    :cond_15
    invoke-virtual {v10}, Landroidx/media3/exoplayer/s2;->n()Z

    move-result v0

    invoke-virtual/range {p1 .. p1}, Landroidx/media3/exoplayer/s2;->n()Z

    move-result v1

    if-eq v0, v1, :cond_16

    iget-object v0, v7, Landroidx/media3/exoplayer/c1;->l:Le2/n;

    new-instance v1, Landroidx/media3/exoplayer/x0;

    invoke-direct {v1, v8}, Landroidx/media3/exoplayer/x0;-><init>(Landroidx/media3/exoplayer/s2;)V

    const/4 v2, 0x7

    invoke-virtual {v0, v2, v1}, Le2/n;->i(ILe2/n$a;)V

    :cond_16
    iget-object v0, v10, Landroidx/media3/exoplayer/s2;->n:Landroidx/media3/common/g0;

    iget-object v1, v8, Landroidx/media3/exoplayer/s2;->n:Landroidx/media3/common/g0;

    invoke-virtual {v0, v1}, Landroidx/media3/common/g0;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_17

    iget-object v0, v7, Landroidx/media3/exoplayer/c1;->l:Le2/n;

    new-instance v1, Landroidx/media3/exoplayer/y0;

    invoke-direct {v1, v8}, Landroidx/media3/exoplayer/y0;-><init>(Landroidx/media3/exoplayer/s2;)V

    const/16 v2, 0xc

    invoke-virtual {v0, v2, v1}, Le2/n;->i(ILe2/n$a;)V

    :cond_17
    invoke-virtual/range {p0 .. p0}, Landroidx/media3/exoplayer/c1;->y2()V

    iget-object v0, v7, Landroidx/media3/exoplayer/c1;->l:Le2/n;

    invoke-virtual {v0}, Le2/n;->f()V

    iget-boolean v0, v10, Landroidx/media3/exoplayer/s2;->o:Z

    iget-boolean v1, v8, Landroidx/media3/exoplayer/s2;->o:Z

    if-eq v0, v1, :cond_18

    iget-object v0, v7, Landroidx/media3/exoplayer/c1;->m:Ljava/util/concurrent/CopyOnWriteArraySet;

    invoke-virtual {v0}, Ljava/util/concurrent/CopyOnWriteArraySet;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_3
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_18

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Landroidx/media3/exoplayer/u$a;

    iget-boolean v2, v8, Landroidx/media3/exoplayer/s2;->o:Z

    invoke-interface {v1, v2}, Landroidx/media3/exoplayer/u$a;->z(Z)V

    goto :goto_3

    :cond_18
    return-void
.end method

.method public final C1(J)Landroidx/media3/common/h0$e;
    .locals 13

    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->u()I

    move-result v2

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->u0:Landroidx/media3/exoplayer/s2;

    iget-object v0, v0, Landroidx/media3/exoplayer/s2;->a:Landroidx/media3/common/m0;

    invoke-virtual {v0}, Landroidx/media3/common/m0;->q()Z

    move-result v0

    if-nez v0, :cond_0

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->u0:Landroidx/media3/exoplayer/s2;

    iget-object v1, v0, Landroidx/media3/exoplayer/s2;->b:Landroidx/media3/exoplayer/source/l$b;

    iget-object v1, v1, Landroidx/media3/exoplayer/source/l$b;->a:Ljava/lang/Object;

    iget-object v0, v0, Landroidx/media3/exoplayer/s2;->a:Landroidx/media3/common/m0;

    iget-object v3, p0, Landroidx/media3/exoplayer/c1;->n:Landroidx/media3/common/m0$b;

    invoke-virtual {v0, v1, v3}, Landroidx/media3/common/m0;->h(Ljava/lang/Object;Landroidx/media3/common/m0$b;)Landroidx/media3/common/m0$b;

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->u0:Landroidx/media3/exoplayer/s2;

    iget-object v0, v0, Landroidx/media3/exoplayer/s2;->a:Landroidx/media3/common/m0;

    invoke-virtual {v0, v1}, Landroidx/media3/common/m0;->b(Ljava/lang/Object;)I

    move-result v0

    iget-object v3, p0, Landroidx/media3/exoplayer/c1;->u0:Landroidx/media3/exoplayer/s2;

    iget-object v3, v3, Landroidx/media3/exoplayer/s2;->a:Landroidx/media3/common/m0;

    iget-object v4, p0, Landroidx/media3/common/h;->a:Landroidx/media3/common/m0$c;

    invoke-virtual {v3, v2, v4}, Landroidx/media3/common/m0;->n(ILandroidx/media3/common/m0$c;)Landroidx/media3/common/m0$c;

    move-result-object v3

    iget-object v3, v3, Landroidx/media3/common/m0$c;->a:Ljava/lang/Object;

    iget-object v4, p0, Landroidx/media3/common/h;->a:Landroidx/media3/common/m0$c;

    iget-object v4, v4, Landroidx/media3/common/m0$c;->c:Landroidx/media3/common/b0;

    move v5, v0

    move-object v12, v4

    move-object v4, v1

    move-object v1, v3

    move-object v3, v12

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    const/4 v1, -0x1

    move-object v1, v0

    move-object v3, v1

    move-object v4, v3

    const/4 v5, -0x1

    :goto_0
    invoke-static {p1, p2}, Le2/u0;->B1(J)J

    move-result-wide v6

    new-instance p1, Landroidx/media3/common/h0$e;

    iget-object p2, p0, Landroidx/media3/exoplayer/c1;->u0:Landroidx/media3/exoplayer/s2;

    iget-object p2, p2, Landroidx/media3/exoplayer/s2;->b:Landroidx/media3/exoplayer/source/l$b;

    invoke-virtual {p2}, Landroidx/media3/exoplayer/source/l$b;->b()Z

    move-result p2

    if-eqz p2, :cond_1

    iget-object p2, p0, Landroidx/media3/exoplayer/c1;->u0:Landroidx/media3/exoplayer/s2;

    invoke-static {p2}, Landroidx/media3/exoplayer/c1;->E1(Landroidx/media3/exoplayer/s2;)J

    move-result-wide v8

    invoke-static {v8, v9}, Le2/u0;->B1(J)J

    move-result-wide v8

    goto :goto_1

    :cond_1
    move-wide v8, v6

    :goto_1
    iget-object p2, p0, Landroidx/media3/exoplayer/c1;->u0:Landroidx/media3/exoplayer/s2;

    iget-object p2, p2, Landroidx/media3/exoplayer/s2;->b:Landroidx/media3/exoplayer/source/l$b;

    iget v10, p2, Landroidx/media3/exoplayer/source/l$b;->b:I

    iget v11, p2, Landroidx/media3/exoplayer/source/l$b;->c:I

    move-object v0, p1

    invoke-direct/range {v0 .. v11}, Landroidx/media3/common/h0$e;-><init>(Ljava/lang/Object;ILandroidx/media3/common/b0;Ljava/lang/Object;IJJII)V

    return-object p1
.end method

.method public final C2(ZII)V
    .locals 11

    iget v0, p0, Landroidx/media3/exoplayer/c1;->J:I

    add-int/lit8 v0, v0, 0x1

    iput v0, p0, Landroidx/media3/exoplayer/c1;->J:I

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->u0:Landroidx/media3/exoplayer/s2;

    iget-boolean v1, v0, Landroidx/media3/exoplayer/s2;->o:Z

    if-eqz v1, :cond_0

    invoke-virtual {v0}, Landroidx/media3/exoplayer/s2;->a()Landroidx/media3/exoplayer/s2;

    move-result-object v0

    :cond_0
    invoke-virtual {v0, p1, p3}, Landroidx/media3/exoplayer/s2;->e(ZI)Landroidx/media3/exoplayer/s2;

    move-result-object v2

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->k:Landroidx/media3/exoplayer/s1;

    invoke-virtual {v0, p1, p3}, Landroidx/media3/exoplayer/s1;->W0(ZI)V

    const/4 v3, 0x0

    const/4 v5, 0x0

    const/4 v6, 0x5

    const-wide v7, -0x7fffffffffffffffL    # -4.9E-324

    const/4 v9, -0x1

    const/4 v10, 0x0

    move-object v1, p0

    move v4, p2

    invoke-virtual/range {v1 .. v10}, Landroidx/media3/exoplayer/c1;->B2(Landroidx/media3/exoplayer/s2;IIZIJIZ)V

    return-void
.end method

.method public D(IILjava/util/List;)V
    .locals 12
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(II",
            "Ljava/util/List<",
            "Landroidx/media3/common/b0;",
            ">;)V"
        }
    .end annotation

    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->F2()V

    const/4 v0, 0x0

    const/4 v1, 0x1

    if-ltz p1, :cond_0

    if-lt p2, p1, :cond_0

    const/4 v2, 0x1

    goto :goto_0

    :cond_0
    const/4 v2, 0x0

    :goto_0
    invoke-static {v2}, Le2/a;->a(Z)V

    iget-object v2, p0, Landroidx/media3/exoplayer/c1;->o:Ljava/util/List;

    invoke-interface {v2}, Ljava/util/List;->size()I

    move-result v2

    if-le p1, v2, :cond_1

    return-void

    :cond_1
    invoke-static {p2, v2}, Ljava/lang/Math;->min(II)I

    move-result p2

    invoke-virtual {p0, p1, p2, p3}, Landroidx/media3/exoplayer/c1;->p1(IILjava/util/List;)Z

    move-result v2

    if-eqz v2, :cond_2

    invoke-virtual {p0, p1, p2, p3}, Landroidx/media3/exoplayer/c1;->z2(IILjava/util/List;)V

    return-void

    :cond_2
    invoke-virtual {p0, p3}, Landroidx/media3/exoplayer/c1;->u1(Ljava/util/List;)Ljava/util/List;

    move-result-object p3

    iget-object v2, p0, Landroidx/media3/exoplayer/c1;->o:Ljava/util/List;

    invoke-interface {v2}, Ljava/util/List;->isEmpty()Z

    move-result v2

    if-eqz v2, :cond_4

    iget p1, p0, Landroidx/media3/exoplayer/c1;->v0:I

    const/4 p2, -0x1

    if-ne p1, p2, :cond_3

    const/4 v0, 0x1

    :cond_3
    invoke-virtual {p0, p3, v0}, Landroidx/media3/exoplayer/c1;->r2(Ljava/util/List;Z)V

    return-void

    :cond_4
    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->u0:Landroidx/media3/exoplayer/s2;

    invoke-virtual {p0, v0, p2, p3}, Landroidx/media3/exoplayer/c1;->n1(Landroidx/media3/exoplayer/s2;ILjava/util/List;)Landroidx/media3/exoplayer/s2;

    move-result-object p3

    invoke-virtual {p0, p3, p1, p2}, Landroidx/media3/exoplayer/c1;->l2(Landroidx/media3/exoplayer/s2;II)Landroidx/media3/exoplayer/s2;

    move-result-object v3

    iget-object p1, v3, Landroidx/media3/exoplayer/s2;->b:Landroidx/media3/exoplayer/source/l$b;

    iget-object p1, p1, Landroidx/media3/exoplayer/source/l$b;->a:Ljava/lang/Object;

    iget-object p2, p0, Landroidx/media3/exoplayer/c1;->u0:Landroidx/media3/exoplayer/s2;

    iget-object p2, p2, Landroidx/media3/exoplayer/s2;->b:Landroidx/media3/exoplayer/source/l$b;

    iget-object p2, p2, Landroidx/media3/exoplayer/source/l$b;->a:Ljava/lang/Object;

    invoke-virtual {p1, p2}, Ljava/lang/Object;->equals(Ljava/lang/Object;)Z

    move-result p1

    xor-int/lit8 v6, p1, 0x1

    const/4 v4, 0x0

    const/4 v5, 0x1

    const/4 v7, 0x4

    invoke-virtual {p0, v3}, Landroidx/media3/exoplayer/c1;->y1(Landroidx/media3/exoplayer/s2;)J

    move-result-wide v8

    const/4 v10, -0x1

    const/4 v11, 0x0

    move-object v2, p0

    invoke-virtual/range {v2 .. v11}, Landroidx/media3/exoplayer/c1;->B2(Landroidx/media3/exoplayer/s2;IIZIJIZ)V

    return-void
.end method

.method public final D1(ILandroidx/media3/exoplayer/s2;I)Landroidx/media3/common/h0$e;
    .locals 18

    move-object/from16 v0, p0

    move-object/from16 v1, p2

    new-instance v2, Landroidx/media3/common/m0$b;

    invoke-direct {v2}, Landroidx/media3/common/m0$b;-><init>()V

    iget-object v3, v1, Landroidx/media3/exoplayer/s2;->a:Landroidx/media3/common/m0;

    invoke-virtual {v3}, Landroidx/media3/common/m0;->q()Z

    move-result v3

    const/4 v4, -0x1

    if-nez v3, :cond_0

    iget-object v3, v1, Landroidx/media3/exoplayer/s2;->b:Landroidx/media3/exoplayer/source/l$b;

    iget-object v3, v3, Landroidx/media3/exoplayer/source/l$b;->a:Ljava/lang/Object;

    iget-object v5, v1, Landroidx/media3/exoplayer/s2;->a:Landroidx/media3/common/m0;

    invoke-virtual {v5, v3, v2}, Landroidx/media3/common/m0;->h(Ljava/lang/Object;Landroidx/media3/common/m0$b;)Landroidx/media3/common/m0$b;

    iget v5, v2, Landroidx/media3/common/m0$b;->c:I

    iget-object v6, v1, Landroidx/media3/exoplayer/s2;->a:Landroidx/media3/common/m0;

    invoke-virtual {v6, v3}, Landroidx/media3/common/m0;->b(Ljava/lang/Object;)I

    move-result v6

    iget-object v7, v1, Landroidx/media3/exoplayer/s2;->a:Landroidx/media3/common/m0;

    iget-object v8, v0, Landroidx/media3/common/h;->a:Landroidx/media3/common/m0$c;

    invoke-virtual {v7, v5, v8}, Landroidx/media3/common/m0;->n(ILandroidx/media3/common/m0$c;)Landroidx/media3/common/m0$c;

    move-result-object v7

    iget-object v7, v7, Landroidx/media3/common/m0$c;->a:Ljava/lang/Object;

    iget-object v8, v0, Landroidx/media3/common/h;->a:Landroidx/media3/common/m0$c;

    iget-object v8, v8, Landroidx/media3/common/m0$c;->c:Landroidx/media3/common/b0;

    move-object v9, v3

    move v10, v6

    move-object v6, v7

    move v7, v5

    goto :goto_0

    :cond_0
    const/4 v3, 0x0

    move/from16 v7, p3

    move-object v6, v3

    move-object v8, v6

    move-object v9, v8

    const/4 v10, -0x1

    :goto_0
    if-nez p1, :cond_3

    iget-object v3, v1, Landroidx/media3/exoplayer/s2;->b:Landroidx/media3/exoplayer/source/l$b;

    invoke-virtual {v3}, Landroidx/media3/exoplayer/source/l$b;->b()Z

    move-result v3

    if-eqz v3, :cond_1

    iget-object v3, v1, Landroidx/media3/exoplayer/s2;->b:Landroidx/media3/exoplayer/source/l$b;

    iget v4, v3, Landroidx/media3/exoplayer/source/l$b;->b:I

    iget v3, v3, Landroidx/media3/exoplayer/source/l$b;->c:I

    invoke-virtual {v2, v4, v3}, Landroidx/media3/common/m0$b;->b(II)J

    move-result-wide v2

    invoke-static/range {p2 .. p2}, Landroidx/media3/exoplayer/c1;->E1(Landroidx/media3/exoplayer/s2;)J

    move-result-wide v4

    goto :goto_2

    :cond_1
    iget-object v3, v1, Landroidx/media3/exoplayer/s2;->b:Landroidx/media3/exoplayer/source/l$b;

    iget v3, v3, Landroidx/media3/exoplayer/source/l$b;->e:I

    if-eq v3, v4, :cond_2

    iget-object v2, v0, Landroidx/media3/exoplayer/c1;->u0:Landroidx/media3/exoplayer/s2;

    invoke-static {v2}, Landroidx/media3/exoplayer/c1;->E1(Landroidx/media3/exoplayer/s2;)J

    move-result-wide v2

    :goto_1
    move-wide v4, v2

    goto :goto_2

    :cond_2
    iget-wide v3, v2, Landroidx/media3/common/m0$b;->e:J

    iget-wide v11, v2, Landroidx/media3/common/m0$b;->d:J

    add-long v2, v3, v11

    goto :goto_1

    :cond_3
    iget-object v3, v1, Landroidx/media3/exoplayer/s2;->b:Landroidx/media3/exoplayer/source/l$b;

    invoke-virtual {v3}, Landroidx/media3/exoplayer/source/l$b;->b()Z

    move-result v3

    if-eqz v3, :cond_4

    iget-wide v2, v1, Landroidx/media3/exoplayer/s2;->r:J

    invoke-static/range {p2 .. p2}, Landroidx/media3/exoplayer/c1;->E1(Landroidx/media3/exoplayer/s2;)J

    move-result-wide v4

    goto :goto_2

    :cond_4
    iget-wide v2, v2, Landroidx/media3/common/m0$b;->e:J

    iget-wide v4, v1, Landroidx/media3/exoplayer/s2;->r:J

    add-long/2addr v2, v4

    goto :goto_1

    :goto_2
    new-instance v17, Landroidx/media3/common/h0$e;

    invoke-static {v2, v3}, Le2/u0;->B1(J)J

    move-result-wide v11

    invoke-static {v4, v5}, Le2/u0;->B1(J)J

    move-result-wide v13

    iget-object v1, v1, Landroidx/media3/exoplayer/s2;->b:Landroidx/media3/exoplayer/source/l$b;

    iget v15, v1, Landroidx/media3/exoplayer/source/l$b;->b:I

    iget v1, v1, Landroidx/media3/exoplayer/source/l$b;->c:I

    move-object/from16 v5, v17

    move/from16 v16, v1

    invoke-direct/range {v5 .. v16}, Landroidx/media3/common/h0$e;-><init>(Ljava/lang/Object;ILandroidx/media3/common/b0;Ljava/lang/Object;IJJII)V

    return-object v17
.end method

.method public final D2(Z)V
    .locals 3

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->o0:Landroidx/media3/common/PriorityTaskManager;

    if-eqz v0, :cond_1

    const/4 v1, 0x0

    if-eqz p1, :cond_0

    iget-boolean v2, p0, Landroidx/media3/exoplayer/c1;->p0:Z

    if-nez v2, :cond_0

    invoke-virtual {v0, v1}, Landroidx/media3/common/PriorityTaskManager;->a(I)V

    const/4 p1, 0x1

    iput-boolean p1, p0, Landroidx/media3/exoplayer/c1;->p0:Z

    goto :goto_0

    :cond_0
    if-nez p1, :cond_1

    iget-boolean p1, p0, Landroidx/media3/exoplayer/c1;->p0:Z

    if-eqz p1, :cond_1

    invoke-virtual {v0, v1}, Landroidx/media3/common/PriorityTaskManager;->d(I)V

    iput-boolean v1, p0, Landroidx/media3/exoplayer/c1;->p0:Z

    :cond_1
    :goto_0
    return-void
.end method

.method public final E2()V
    .locals 5

    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->getPlaybackState()I

    move-result v0

    const/4 v1, 0x1

    const/4 v2, 0x0

    if-eq v0, v1, :cond_3

    const/4 v3, 0x2

    if-eq v0, v3, :cond_1

    const/4 v3, 0x3

    if-eq v0, v3, :cond_1

    const/4 v1, 0x4

    if-ne v0, v1, :cond_0

    goto :goto_1

    :cond_0
    new-instance v0, Ljava/lang/IllegalStateException;

    invoke-direct {v0}, Ljava/lang/IllegalStateException;-><init>()V

    throw v0

    :cond_1
    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->I1()Z

    move-result v0

    iget-object v3, p0, Landroidx/media3/exoplayer/c1;->C:Landroidx/media3/exoplayer/h3;

    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->getPlayWhenReady()Z

    move-result v4

    if-eqz v4, :cond_2

    if-nez v0, :cond_2

    goto :goto_0

    :cond_2
    const/4 v1, 0x0

    :goto_0
    invoke-virtual {v3, v1}, Landroidx/media3/exoplayer/h3;->b(Z)V

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->D:Landroidx/media3/exoplayer/i3;

    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->getPlayWhenReady()Z

    move-result v1

    invoke-virtual {v0, v1}, Landroidx/media3/exoplayer/i3;->b(Z)V

    goto :goto_2

    :cond_3
    :goto_1
    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->C:Landroidx/media3/exoplayer/h3;

    invoke-virtual {v0, v2}, Landroidx/media3/exoplayer/h3;->b(Z)V

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->D:Landroidx/media3/exoplayer/i3;

    invoke-virtual {v0, v2}, Landroidx/media3/exoplayer/i3;->b(Z)V

    :goto_2
    return-void
.end method

.method public final F1(Landroidx/media3/exoplayer/s1$e;)V
    .locals 12

    iget v1, p0, Landroidx/media3/exoplayer/c1;->J:I

    iget v2, p1, Landroidx/media3/exoplayer/s1$e;->c:I

    sub-int/2addr v1, v2

    iput v1, p0, Landroidx/media3/exoplayer/c1;->J:I

    iget-boolean v2, p1, Landroidx/media3/exoplayer/s1$e;->d:Z

    const/4 v3, 0x1

    if-eqz v2, :cond_0

    iget v2, p1, Landroidx/media3/exoplayer/s1$e;->e:I

    iput v2, p0, Landroidx/media3/exoplayer/c1;->K:I

    iput-boolean v3, p0, Landroidx/media3/exoplayer/c1;->L:Z

    :cond_0
    iget-boolean v2, p1, Landroidx/media3/exoplayer/s1$e;->f:Z

    if-eqz v2, :cond_1

    iget v2, p1, Landroidx/media3/exoplayer/s1$e;->g:I

    iput v2, p0, Landroidx/media3/exoplayer/c1;->M:I

    :cond_1
    if-nez v1, :cond_b

    iget-object v1, p1, Landroidx/media3/exoplayer/s1$e;->b:Landroidx/media3/exoplayer/s2;

    iget-object v1, v1, Landroidx/media3/exoplayer/s2;->a:Landroidx/media3/common/m0;

    iget-object v2, p0, Landroidx/media3/exoplayer/c1;->u0:Landroidx/media3/exoplayer/s2;

    iget-object v2, v2, Landroidx/media3/exoplayer/s2;->a:Landroidx/media3/common/m0;

    invoke-virtual {v2}, Landroidx/media3/common/m0;->q()Z

    move-result v2

    const/4 v4, 0x0

    if-nez v2, :cond_2

    invoke-virtual {v1}, Landroidx/media3/common/m0;->q()Z

    move-result v2

    if-eqz v2, :cond_2

    const/4 v2, -0x1

    iput v2, p0, Landroidx/media3/exoplayer/c1;->v0:I

    const-wide/16 v5, 0x0

    iput-wide v5, p0, Landroidx/media3/exoplayer/c1;->x0:J

    iput v4, p0, Landroidx/media3/exoplayer/c1;->w0:I

    :cond_2
    invoke-virtual {v1}, Landroidx/media3/common/m0;->q()Z

    move-result v2

    if-nez v2, :cond_4

    move-object v2, v1

    check-cast v2, Landroidx/media3/exoplayer/u2;

    invoke-virtual {v2}, Landroidx/media3/exoplayer/u2;->F()Ljava/util/List;

    move-result-object v2

    invoke-interface {v2}, Ljava/util/List;->size()I

    move-result v5

    iget-object v6, p0, Landroidx/media3/exoplayer/c1;->o:Ljava/util/List;

    invoke-interface {v6}, Ljava/util/List;->size()I

    move-result v6

    if-ne v5, v6, :cond_3

    const/4 v5, 0x1

    goto :goto_0

    :cond_3
    const/4 v5, 0x0

    :goto_0
    invoke-static {v5}, Le2/a;->g(Z)V

    const/4 v5, 0x0

    :goto_1
    invoke-interface {v2}, Ljava/util/List;->size()I

    move-result v6

    if-ge v5, v6, :cond_4

    iget-object v6, p0, Landroidx/media3/exoplayer/c1;->o:Ljava/util/List;

    invoke-interface {v6, v5}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v6

    check-cast v6, Landroidx/media3/exoplayer/c1$f;

    invoke-interface {v2, v5}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v7

    check-cast v7, Landroidx/media3/common/m0;

    invoke-virtual {v6, v7}, Landroidx/media3/exoplayer/c1$f;->d(Landroidx/media3/common/m0;)V

    add-int/lit8 v5, v5, 0x1

    goto :goto_1

    :cond_4
    iget-boolean v2, p0, Landroidx/media3/exoplayer/c1;->L:Z

    const-wide v5, -0x7fffffffffffffffL    # -4.9E-324

    if-eqz v2, :cond_a

    iget-object v2, p1, Landroidx/media3/exoplayer/s1$e;->b:Landroidx/media3/exoplayer/s2;

    iget-object v2, v2, Landroidx/media3/exoplayer/s2;->b:Landroidx/media3/exoplayer/source/l$b;

    iget-object v7, p0, Landroidx/media3/exoplayer/c1;->u0:Landroidx/media3/exoplayer/s2;

    iget-object v7, v7, Landroidx/media3/exoplayer/s2;->b:Landroidx/media3/exoplayer/source/l$b;

    invoke-virtual {v2, v7}, Landroidx/media3/exoplayer/source/l$b;->equals(Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_6

    iget-object v2, p1, Landroidx/media3/exoplayer/s1$e;->b:Landroidx/media3/exoplayer/s2;

    iget-wide v7, v2, Landroidx/media3/exoplayer/s2;->d:J

    iget-object v2, p0, Landroidx/media3/exoplayer/c1;->u0:Landroidx/media3/exoplayer/s2;

    iget-wide v10, v2, Landroidx/media3/exoplayer/s2;->r:J

    cmp-long v2, v7, v10

    if-eqz v2, :cond_5

    goto :goto_2

    :cond_5
    const/4 v3, 0x0

    :cond_6
    :goto_2
    if-eqz v3, :cond_9

    invoke-virtual {v1}, Landroidx/media3/common/m0;->q()Z

    move-result v2

    if-nez v2, :cond_8

    iget-object v2, p1, Landroidx/media3/exoplayer/s1$e;->b:Landroidx/media3/exoplayer/s2;

    iget-object v2, v2, Landroidx/media3/exoplayer/s2;->b:Landroidx/media3/exoplayer/source/l$b;

    invoke-virtual {v2}, Landroidx/media3/exoplayer/source/l$b;->b()Z

    move-result v2

    if-eqz v2, :cond_7

    goto :goto_3

    :cond_7
    iget-object v2, p1, Landroidx/media3/exoplayer/s1$e;->b:Landroidx/media3/exoplayer/s2;

    iget-object v5, v2, Landroidx/media3/exoplayer/s2;->b:Landroidx/media3/exoplayer/source/l$b;

    iget-wide v6, v2, Landroidx/media3/exoplayer/s2;->d:J

    invoke-virtual {p0, v1, v5, v6, v7}, Landroidx/media3/exoplayer/c1;->k2(Landroidx/media3/common/m0;Landroidx/media3/exoplayer/source/l$b;J)J

    move-result-wide v1

    goto :goto_4

    :cond_8
    :goto_3
    iget-object v1, p1, Landroidx/media3/exoplayer/s1$e;->b:Landroidx/media3/exoplayer/s2;

    iget-wide v1, v1, Landroidx/media3/exoplayer/s2;->d:J

    :goto_4
    move-wide v6, v1

    :goto_5
    move v5, v3

    goto :goto_6

    :cond_9
    move-wide v6, v5

    goto :goto_5

    :cond_a
    move-wide v6, v5

    const/4 v5, 0x0

    :goto_6
    iput-boolean v4, p0, Landroidx/media3/exoplayer/c1;->L:Z

    iget-object v1, p1, Landroidx/media3/exoplayer/s1$e;->b:Landroidx/media3/exoplayer/s2;

    const/4 v2, 0x1

    iget v3, p0, Landroidx/media3/exoplayer/c1;->M:I

    iget v8, p0, Landroidx/media3/exoplayer/c1;->K:I

    const/4 v9, -0x1

    const/4 v10, 0x0

    move-object v0, p0

    move v4, v5

    move v5, v8

    move v8, v9

    move v9, v10

    invoke-virtual/range {v0 .. v9}, Landroidx/media3/exoplayer/c1;->B2(Landroidx/media3/exoplayer/s2;IIZIJIZ)V

    :cond_b
    return-void
.end method

.method public final F2()V
    .locals 4

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->d:Le2/g;

    invoke-virtual {v0}, Le2/g;->c()V

    invoke-static {}, Ljava/lang/Thread;->currentThread()Ljava/lang/Thread;

    move-result-object v0

    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->l()Landroid/os/Looper;

    move-result-object v1

    invoke-virtual {v1}, Landroid/os/Looper;->getThread()Ljava/lang/Thread;

    move-result-object v1

    if-eq v0, v1, :cond_2

    const/4 v0, 0x2

    new-array v0, v0, [Ljava/lang/Object;

    invoke-static {}, Ljava/lang/Thread;->currentThread()Ljava/lang/Thread;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/Thread;->getName()Ljava/lang/String;

    move-result-object v1

    const/4 v2, 0x0

    aput-object v1, v0, v2

    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->l()Landroid/os/Looper;

    move-result-object v1

    invoke-virtual {v1}, Landroid/os/Looper;->getThread()Ljava/lang/Thread;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/Thread;->getName()Ljava/lang/String;

    move-result-object v1

    const/4 v2, 0x1

    aput-object v1, v0, v2

    const-string v1, "Player is accessed on the wrong thread.\nCurrent thread: \'%s\'\nExpected thread: \'%s\'\nSee https://developer.android.com/guide/topics/media/issues/player-accessed-on-wrong-thread"

    invoke-static {v1, v0}, Le2/u0;->G(Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v0

    iget-boolean v1, p0, Landroidx/media3/exoplayer/c1;->m0:Z

    if-nez v1, :cond_1

    iget-boolean v1, p0, Landroidx/media3/exoplayer/c1;->n0:Z

    if-eqz v1, :cond_0

    const/4 v1, 0x0

    goto :goto_0

    :cond_0
    new-instance v1, Ljava/lang/IllegalStateException;

    invoke-direct {v1}, Ljava/lang/IllegalStateException;-><init>()V

    :goto_0
    const-string v3, "ExoPlayerImpl"

    invoke-static {v3, v0, v1}, Le2/o;->i(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Throwable;)V

    iput-boolean v2, p0, Landroidx/media3/exoplayer/c1;->n0:Z

    goto :goto_1

    :cond_1
    new-instance v1, Ljava/lang/IllegalStateException;

    invoke-direct {v1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw v1

    :cond_2
    :goto_1
    return-void
.end method

.method public G(II)V
    .locals 12

    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->F2()V

    const/4 v0, 0x1

    if-ltz p1, :cond_0

    if-lt p2, p1, :cond_0

    const/4 v1, 0x1

    goto :goto_0

    :cond_0
    const/4 v1, 0x0

    :goto_0
    invoke-static {v1}, Le2/a;->a(Z)V

    iget-object v1, p0, Landroidx/media3/exoplayer/c1;->o:Ljava/util/List;

    invoke-interface {v1}, Ljava/util/List;->size()I

    move-result v1

    invoke-static {p2, v1}, Ljava/lang/Math;->min(II)I

    move-result p2

    if-ge p1, v1, :cond_2

    if-ne p1, p2, :cond_1

    goto :goto_1

    :cond_1
    iget-object v1, p0, Landroidx/media3/exoplayer/c1;->u0:Landroidx/media3/exoplayer/s2;

    invoke-virtual {p0, v1, p1, p2}, Landroidx/media3/exoplayer/c1;->l2(Landroidx/media3/exoplayer/s2;II)Landroidx/media3/exoplayer/s2;

    move-result-object v3

    iget-object p1, v3, Landroidx/media3/exoplayer/s2;->b:Landroidx/media3/exoplayer/source/l$b;

    iget-object p1, p1, Landroidx/media3/exoplayer/source/l$b;->a:Ljava/lang/Object;

    iget-object p2, p0, Landroidx/media3/exoplayer/c1;->u0:Landroidx/media3/exoplayer/s2;

    iget-object p2, p2, Landroidx/media3/exoplayer/s2;->b:Landroidx/media3/exoplayer/source/l$b;

    iget-object p2, p2, Landroidx/media3/exoplayer/source/l$b;->a:Ljava/lang/Object;

    invoke-virtual {p1, p2}, Ljava/lang/Object;->equals(Ljava/lang/Object;)Z

    move-result p1

    xor-int/lit8 v6, p1, 0x1

    const/4 v4, 0x0

    const/4 v5, 0x1

    const/4 v7, 0x4

    invoke-virtual {p0, v3}, Landroidx/media3/exoplayer/c1;->y1(Landroidx/media3/exoplayer/s2;)J

    move-result-wide v8

    const/4 v10, -0x1

    const/4 v11, 0x0

    move-object v2, p0

    invoke-virtual/range {v2 .. v11}, Landroidx/media3/exoplayer/c1;->B2(Landroidx/media3/exoplayer/s2;IIZIJIZ)V

    :cond_2
    :goto_1
    return-void
.end method

.method public final G1()Z
    .locals 3

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->F:Landroid/media/AudioManager;

    if-eqz v0, :cond_1

    sget v1, Le2/u0;->a:I

    const/16 v2, 0x17

    if-ge v1, v2, :cond_0

    goto :goto_0

    :cond_0
    iget-object v1, p0, Landroidx/media3/exoplayer/c1;->e:Landroid/content/Context;

    const/4 v2, 0x2

    invoke-static {v0, v2}, Landroidx/media3/exoplayer/d0;->a(Landroid/media/AudioManager;I)[Landroid/media/AudioDeviceInfo;

    move-result-object v0

    invoke-static {v1, v0}, Landroidx/media3/exoplayer/c1$b;->a(Landroid/content/Context;[Landroid/media/AudioDeviceInfo;)Z

    move-result v0

    return v0

    :cond_1
    :goto_0
    const/4 v0, 0x1

    return v0
.end method

.method public final H1(I)I
    .locals 9

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->V:Landroid/media/AudioTrack;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Landroid/media/AudioTrack;->getAudioSessionId()I

    move-result v0

    if-eq v0, p1, :cond_0

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->V:Landroid/media/AudioTrack;

    invoke-virtual {v0}, Landroid/media/AudioTrack;->release()V

    const/4 v0, 0x0

    iput-object v0, p0, Landroidx/media3/exoplayer/c1;->V:Landroid/media/AudioTrack;

    :cond_0
    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->V:Landroid/media/AudioTrack;

    if-nez v0, :cond_1

    const/16 v3, 0xfa0

    const/4 v4, 0x4

    const/4 v5, 0x2

    const/4 v6, 0x2

    new-instance v0, Landroid/media/AudioTrack;

    const/4 v2, 0x3

    const/4 v7, 0x0

    move-object v1, v0

    move v8, p1

    invoke-direct/range {v1 .. v8}, Landroid/media/AudioTrack;-><init>(IIIIIII)V

    iput-object v0, p0, Landroidx/media3/exoplayer/c1;->V:Landroid/media/AudioTrack;

    :cond_1
    iget-object p1, p0, Landroidx/media3/exoplayer/c1;->V:Landroid/media/AudioTrack;

    invoke-virtual {p1}, Landroid/media/AudioTrack;->getAudioSessionId()I

    move-result p1

    return p1
.end method

.method public I1()Z
    .locals 1

    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->F2()V

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->u0:Landroidx/media3/exoplayer/s2;

    iget-boolean v0, v0, Landroidx/media3/exoplayer/s2;->o:Z

    return v0
.end method

.method public J(Landroidx/media3/common/h0$d;)V
    .locals 1

    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->F2()V

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->l:Le2/n;

    invoke-static {p1}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Landroidx/media3/common/h0$d;

    invoke-virtual {v0, p1}, Le2/n;->k(Ljava/lang/Object;)V

    return-void
.end method

.method public K(Landroidx/media3/common/h0$d;)V
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->l:Le2/n;

    invoke-static {p1}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Landroidx/media3/common/h0$d;

    invoke-virtual {v0, p1}, Le2/n;->c(Ljava/lang/Object;)V

    return-void
.end method

.method public final synthetic K1(Landroidx/media3/common/h0$d;Landroidx/media3/common/s;)V
    .locals 2

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->f:Landroidx/media3/common/h0;

    new-instance v1, Landroidx/media3/common/h0$c;

    invoke-direct {v1, p2}, Landroidx/media3/common/h0$c;-><init>(Landroidx/media3/common/s;)V

    invoke-interface {p1, v0, v1}, Landroidx/media3/common/h0$d;->onEvents(Landroidx/media3/common/h0;Landroidx/media3/common/h0$c;)V

    return-void
.end method

.method public L(Lj2/c;)V
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->r:Lj2/a;

    invoke-static {p1}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lj2/c;

    invoke-interface {v0, p1}, Lj2/a;->u(Lj2/c;)V

    return-void
.end method

.method public final synthetic L1(Landroidx/media3/exoplayer/s1$e;)V
    .locals 0

    invoke-virtual {p0, p1}, Landroidx/media3/exoplayer/c1;->F1(Landroidx/media3/exoplayer/s1$e;)V

    return-void
.end method

.method public final synthetic M1(Landroidx/media3/exoplayer/s1$e;)V
    .locals 2

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->i:Le2/j;

    new-instance v1, Landroidx/media3/exoplayer/t0;

    invoke-direct {v1, p0, p1}, Landroidx/media3/exoplayer/t0;-><init>(Landroidx/media3/exoplayer/c1;Landroidx/media3/exoplayer/s1$e;)V

    invoke-interface {v0, v1}, Le2/j;->post(Ljava/lang/Runnable;)Z

    return-void
.end method

.method public Q()Landroidx/media3/common/y;
    .locals 1
    .annotation build Landroidx/annotation/Nullable;
    .end annotation

    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->F2()V

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->U:Landroidx/media3/common/y;

    return-object v0
.end method

.method public R(ILjava/util/List;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I",
            "Ljava/util/List<",
            "Landroidx/media3/common/b0;",
            ">;)V"
        }
    .end annotation

    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->F2()V

    invoke-virtual {p0, p2}, Landroidx/media3/exoplayer/c1;->u1(Ljava/util/List;)Ljava/util/List;

    move-result-object p2

    invoke-virtual {p0, p1, p2}, Landroidx/media3/exoplayer/c1;->m1(ILjava/util/List;)V

    return-void
.end method

.method public S(Landroidx/media3/exoplayer/source/l;)V
    .locals 0

    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->F2()V

    invoke-static {p1}, Ljava/util/Collections;->singletonList(Ljava/lang/Object;)Ljava/util/List;

    move-result-object p1

    invoke-virtual {p0, p1}, Landroidx/media3/exoplayer/c1;->q2(Ljava/util/List;)V

    return-void
.end method

.method public final synthetic S1(Landroidx/media3/common/h0$d;)V
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->Q:Landroidx/media3/common/h0$b;

    invoke-interface {p1, v0}, Landroidx/media3/common/h0$d;->onAvailableCommandsChanged(Landroidx/media3/common/h0$b;)V

    return-void
.end method

.method public T(Landroidx/media3/common/p0;)V
    .locals 2

    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->F2()V

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->h:Lx2/e0;

    invoke-virtual {v0}, Lx2/e0;->h()Z

    move-result v0

    if-eqz v0, :cond_1

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->h:Lx2/e0;

    invoke-virtual {v0}, Lx2/e0;->c()Landroidx/media3/common/p0;

    move-result-object v0

    invoke-virtual {p1, v0}, Landroidx/media3/common/p0;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_0

    goto :goto_0

    :cond_0
    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->h:Lx2/e0;

    invoke-virtual {v0, p1}, Lx2/e0;->m(Landroidx/media3/common/p0;)V

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->l:Le2/n;

    new-instance v1, Landroidx/media3/exoplayer/o0;

    invoke-direct {v1, p1}, Landroidx/media3/exoplayer/o0;-><init>(Landroidx/media3/common/p0;)V

    const/16 p1, 0x13

    invoke-virtual {v0, p1, v1}, Le2/n;->l(ILe2/n$a;)V

    :cond_1
    :goto_0
    return-void
.end method

.method public U()Z
    .locals 1

    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->F2()V

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->B:Landroidx/media3/exoplayer/f3;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Landroidx/media3/exoplayer/f3;->g()Z

    move-result v0

    return v0

    :cond_0
    const/4 v0, 0x0

    return v0
.end method

.method public a()Landroidx/media3/common/y;
    .locals 1
    .annotation build Landroidx/annotation/Nullable;
    .end annotation

    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->F2()V

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->T:Landroidx/media3/common/y;

    return-object v0
.end method

.method public a0(IJIZ)V
    .locals 11

    move-object v10, p0

    move v0, p1

    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->F2()V

    const/4 v1, 0x1

    if-ltz v0, :cond_0

    const/4 v2, 0x1

    goto :goto_0

    :cond_0
    const/4 v2, 0x0

    :goto_0
    invoke-static {v2}, Le2/a;->a(Z)V

    iget-object v2, v10, Landroidx/media3/exoplayer/c1;->r:Lj2/a;

    invoke-interface {v2}, Lj2/a;->n()V

    iget-object v2, v10, Landroidx/media3/exoplayer/c1;->u0:Landroidx/media3/exoplayer/s2;

    iget-object v2, v2, Landroidx/media3/exoplayer/s2;->a:Landroidx/media3/common/m0;

    invoke-virtual {v2}, Landroidx/media3/common/m0;->q()Z

    move-result v3

    if-nez v3, :cond_1

    invoke-virtual {v2}, Landroidx/media3/common/m0;->p()I

    move-result v3

    if-lt v0, v3, :cond_1

    return-void

    :cond_1
    iget v3, v10, Landroidx/media3/exoplayer/c1;->J:I

    add-int/2addr v3, v1

    iput v3, v10, Landroidx/media3/exoplayer/c1;->J:I

    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->isPlayingAd()Z

    move-result v3

    if-eqz v3, :cond_2

    const-string v0, "ExoPlayerImpl"

    const-string v2, "seekTo ignored because an ad is playing"

    invoke-static {v0, v2}, Le2/o;->h(Ljava/lang/String;Ljava/lang/String;)V

    new-instance v0, Landroidx/media3/exoplayer/s1$e;

    iget-object v2, v10, Landroidx/media3/exoplayer/c1;->u0:Landroidx/media3/exoplayer/s2;

    invoke-direct {v0, v2}, Landroidx/media3/exoplayer/s1$e;-><init>(Landroidx/media3/exoplayer/s2;)V

    invoke-virtual {v0, v1}, Landroidx/media3/exoplayer/s1$e;->b(I)V

    iget-object v1, v10, Landroidx/media3/exoplayer/c1;->j:Landroidx/media3/exoplayer/s1$f;

    invoke-interface {v1, v0}, Landroidx/media3/exoplayer/s1$f;->a(Landroidx/media3/exoplayer/s1$e;)V

    return-void

    :cond_2
    iget-object v1, v10, Landroidx/media3/exoplayer/c1;->u0:Landroidx/media3/exoplayer/s2;

    iget v3, v1, Landroidx/media3/exoplayer/s2;->e:I

    const/4 v4, 0x3

    if-eq v3, v4, :cond_3

    const/4 v4, 0x4

    if-ne v3, v4, :cond_4

    invoke-virtual {v2}, Landroidx/media3/common/m0;->q()Z

    move-result v3

    if-nez v3, :cond_4

    :cond_3
    iget-object v1, v10, Landroidx/media3/exoplayer/c1;->u0:Landroidx/media3/exoplayer/s2;

    const/4 v3, 0x2

    invoke-virtual {v1, v3}, Landroidx/media3/exoplayer/s2;->h(I)Landroidx/media3/exoplayer/s2;

    move-result-object v1

    :cond_4
    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->u()I

    move-result v8

    move-wide v3, p2

    invoke-virtual {p0, v2, p1, p2, p3}, Landroidx/media3/exoplayer/c1;->i2(Landroidx/media3/common/m0;IJ)Landroid/util/Pair;

    move-result-object v5

    invoke-virtual {p0, v1, v2, v5}, Landroidx/media3/exoplayer/c1;->h2(Landroidx/media3/exoplayer/s2;Landroidx/media3/common/m0;Landroid/util/Pair;)Landroidx/media3/exoplayer/s2;

    move-result-object v1

    iget-object v5, v10, Landroidx/media3/exoplayer/c1;->k:Landroidx/media3/exoplayer/s1;

    invoke-static {p2, p3}, Le2/u0;->S0(J)J

    move-result-wide v3

    invoke-virtual {v5, v2, p1, v3, v4}, Landroidx/media3/exoplayer/s1;->G0(Landroidx/media3/common/m0;IJ)V

    const/4 v2, 0x0

    const/4 v3, 0x1

    const/4 v4, 0x1

    const/4 v5, 0x1

    invoke-virtual {p0, v1}, Landroidx/media3/exoplayer/c1;->y1(Landroidx/media3/exoplayer/s2;)J

    move-result-wide v6

    move-object v0, p0

    move/from16 v9, p5

    invoke-virtual/range {v0 .. v9}, Landroidx/media3/exoplayer/c1;->B2(Landroidx/media3/exoplayer/s2;IIZIJIZ)V

    return-void
.end method

.method public b(Landroidx/media3/common/g0;)V
    .locals 11

    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->F2()V

    if-nez p1, :cond_0

    sget-object p1, Landroidx/media3/common/g0;->d:Landroidx/media3/common/g0;

    :cond_0
    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->u0:Landroidx/media3/exoplayer/s2;

    iget-object v0, v0, Landroidx/media3/exoplayer/s2;->n:Landroidx/media3/common/g0;

    invoke-virtual {v0, p1}, Landroidx/media3/common/g0;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_1

    return-void

    :cond_1
    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->u0:Landroidx/media3/exoplayer/s2;

    invoke-virtual {v0, p1}, Landroidx/media3/exoplayer/s2;->g(Landroidx/media3/common/g0;)Landroidx/media3/exoplayer/s2;

    move-result-object v2

    iget v0, p0, Landroidx/media3/exoplayer/c1;->J:I

    add-int/lit8 v0, v0, 0x1

    iput v0, p0, Landroidx/media3/exoplayer/c1;->J:I

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->k:Landroidx/media3/exoplayer/s1;

    invoke-virtual {v0, p1}, Landroidx/media3/exoplayer/s1;->Y0(Landroidx/media3/common/g0;)V

    const/4 v3, 0x0

    const/4 v4, 0x1

    const/4 v5, 0x0

    const/4 v6, 0x5

    const-wide v7, -0x7fffffffffffffffL    # -4.9E-324

    const/4 v9, -0x1

    const/4 v10, 0x0

    move-object v1, p0

    invoke-virtual/range {v1 .. v10}, Landroidx/media3/exoplayer/c1;->B2(Landroidx/media3/exoplayer/s2;IIZIJIZ)V

    return-void
.end method

.method public bridge synthetic c()Landroidx/media3/common/PlaybackException;
    .locals 1
    .annotation build Landroidx/annotation/Nullable;
    .end annotation

    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->c()Landroidx/media3/exoplayer/ExoPlaybackException;

    move-result-object v0

    return-object v0
.end method

.method public c()Landroidx/media3/exoplayer/ExoPlaybackException;
    .locals 1
    .annotation build Landroidx/annotation/Nullable;
    .end annotation

    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->F2()V

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->u0:Landroidx/media3/exoplayer/s2;

    iget-object v0, v0, Landroidx/media3/exoplayer/s2;->f:Landroidx/media3/exoplayer/ExoPlaybackException;

    return-object v0
.end method

.method public clearVideoSurface()V
    .locals 1

    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->F2()V

    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->n2()V

    const/4 v0, 0x0

    invoke-virtual {p0, v0}, Landroidx/media3/exoplayer/c1;->v2(Ljava/lang/Object;)V

    const/4 v0, 0x0

    invoke-virtual {p0, v0, v0}, Landroidx/media3/exoplayer/c1;->j2(II)V

    return-void
.end method

.method public clearVideoSurfaceView(Landroid/view/SurfaceView;)V
    .locals 0
    .param p1    # Landroid/view/SurfaceView;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->F2()V

    if-nez p1, :cond_0

    const/4 p1, 0x0

    goto :goto_0

    :cond_0
    invoke-virtual {p1}, Landroid/view/SurfaceView;->getHolder()Landroid/view/SurfaceHolder;

    move-result-object p1

    :goto_0
    invoke-virtual {p0, p1}, Landroidx/media3/exoplayer/c1;->q1(Landroid/view/SurfaceHolder;)V

    return-void
.end method

.method public clearVideoTextureView(Landroid/view/TextureView;)V
    .locals 1
    .param p1    # Landroid/view/TextureView;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->F2()V

    if-eqz p1, :cond_0

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->b0:Landroid/view/TextureView;

    if-ne p1, v0, :cond_0

    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->clearVideoSurface()V

    :cond_0
    return-void
.end method

.method public d()J
    .locals 2

    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->F2()V

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->u0:Landroidx/media3/exoplayer/s2;

    iget-wide v0, v0, Landroidx/media3/exoplayer/s2;->q:J

    invoke-static {v0, v1}, Le2/u0;->B1(J)J

    move-result-wide v0

    return-wide v0
.end method

.method public e(Ljava/util/List;Z)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Landroidx/media3/common/b0;",
            ">;Z)V"
        }
    .end annotation

    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->F2()V

    invoke-virtual {p0, p1}, Landroidx/media3/exoplayer/c1;->u1(Ljava/util/List;)Ljava/util/List;

    move-result-object p1

    invoke-virtual {p0, p1, p2}, Landroidx/media3/exoplayer/c1;->r2(Ljava/util/List;Z)V

    return-void
.end method

.method public getContentPosition()J
    .locals 2

    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->F2()V

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->u0:Landroidx/media3/exoplayer/s2;

    invoke-virtual {p0, v0}, Landroidx/media3/exoplayer/c1;->x1(Landroidx/media3/exoplayer/s2;)J

    move-result-wide v0

    return-wide v0
.end method

.method public getCurrentAdGroupIndex()I
    .locals 1

    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->F2()V

    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->isPlayingAd()Z

    move-result v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->u0:Landroidx/media3/exoplayer/s2;

    iget-object v0, v0, Landroidx/media3/exoplayer/s2;->b:Landroidx/media3/exoplayer/source/l$b;

    iget v0, v0, Landroidx/media3/exoplayer/source/l$b;->b:I

    goto :goto_0

    :cond_0
    const/4 v0, -0x1

    :goto_0
    return v0
.end method

.method public getCurrentAdIndexInAdGroup()I
    .locals 1

    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->F2()V

    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->isPlayingAd()Z

    move-result v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->u0:Landroidx/media3/exoplayer/s2;

    iget-object v0, v0, Landroidx/media3/exoplayer/s2;->b:Landroidx/media3/exoplayer/source/l$b;

    iget v0, v0, Landroidx/media3/exoplayer/source/l$b;->c:I

    goto :goto_0

    :cond_0
    const/4 v0, -0x1

    :goto_0
    return v0
.end method

.method public getCurrentPeriodIndex()I
    .locals 2

    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->F2()V

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->u0:Landroidx/media3/exoplayer/s2;

    iget-object v0, v0, Landroidx/media3/exoplayer/s2;->a:Landroidx/media3/common/m0;

    invoke-virtual {v0}, Landroidx/media3/common/m0;->q()Z

    move-result v0

    if-eqz v0, :cond_0

    iget v0, p0, Landroidx/media3/exoplayer/c1;->w0:I

    return v0

    :cond_0
    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->u0:Landroidx/media3/exoplayer/s2;

    iget-object v1, v0, Landroidx/media3/exoplayer/s2;->a:Landroidx/media3/common/m0;

    iget-object v0, v0, Landroidx/media3/exoplayer/s2;->b:Landroidx/media3/exoplayer/source/l$b;

    iget-object v0, v0, Landroidx/media3/exoplayer/source/l$b;->a:Ljava/lang/Object;

    invoke-virtual {v1, v0}, Landroidx/media3/common/m0;->b(Ljava/lang/Object;)I

    move-result v0

    return v0
.end method

.method public getCurrentPosition()J
    .locals 2

    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->F2()V

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->u0:Landroidx/media3/exoplayer/s2;

    invoke-virtual {p0, v0}, Landroidx/media3/exoplayer/c1;->y1(Landroidx/media3/exoplayer/s2;)J

    move-result-wide v0

    invoke-static {v0, v1}, Le2/u0;->B1(J)J

    move-result-wide v0

    return-wide v0
.end method

.method public getCurrentTimeline()Landroidx/media3/common/m0;
    .locals 1

    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->F2()V

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->u0:Landroidx/media3/exoplayer/s2;

    iget-object v0, v0, Landroidx/media3/exoplayer/s2;->a:Landroidx/media3/common/m0;

    return-object v0
.end method

.method public getCurrentTracks()Landroidx/media3/common/q0;
    .locals 1

    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->F2()V

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->u0:Landroidx/media3/exoplayer/s2;

    iget-object v0, v0, Landroidx/media3/exoplayer/s2;->i:Lx2/f0;

    iget-object v0, v0, Lx2/f0;->d:Landroidx/media3/common/q0;

    return-object v0
.end method

.method public getDuration()J
    .locals 4

    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->F2()V

    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->isPlayingAd()Z

    move-result v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->u0:Landroidx/media3/exoplayer/s2;

    iget-object v1, v0, Landroidx/media3/exoplayer/s2;->b:Landroidx/media3/exoplayer/source/l$b;

    iget-object v0, v0, Landroidx/media3/exoplayer/s2;->a:Landroidx/media3/common/m0;

    iget-object v2, v1, Landroidx/media3/exoplayer/source/l$b;->a:Ljava/lang/Object;

    iget-object v3, p0, Landroidx/media3/exoplayer/c1;->n:Landroidx/media3/common/m0$b;

    invoke-virtual {v0, v2, v3}, Landroidx/media3/common/m0;->h(Ljava/lang/Object;Landroidx/media3/common/m0$b;)Landroidx/media3/common/m0$b;

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->n:Landroidx/media3/common/m0$b;

    iget v2, v1, Landroidx/media3/exoplayer/source/l$b;->b:I

    iget v1, v1, Landroidx/media3/exoplayer/source/l$b;->c:I

    invoke-virtual {v0, v2, v1}, Landroidx/media3/common/m0$b;->b(II)J

    move-result-wide v0

    invoke-static {v0, v1}, Le2/u0;->B1(J)J

    move-result-wide v0

    return-wide v0

    :cond_0
    invoke-virtual {p0}, Landroidx/media3/common/h;->P()J

    move-result-wide v0

    return-wide v0
.end method

.method public getPlayWhenReady()Z
    .locals 1

    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->F2()V

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->u0:Landroidx/media3/exoplayer/s2;

    iget-boolean v0, v0, Landroidx/media3/exoplayer/s2;->l:Z

    return v0
.end method

.method public getPlaybackParameters()Landroidx/media3/common/g0;
    .locals 1

    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->F2()V

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->u0:Landroidx/media3/exoplayer/s2;

    iget-object v0, v0, Landroidx/media3/exoplayer/s2;->n:Landroidx/media3/common/g0;

    return-object v0
.end method

.method public getPlaybackState()I
    .locals 1

    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->F2()V

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->u0:Landroidx/media3/exoplayer/s2;

    iget v0, v0, Landroidx/media3/exoplayer/s2;->e:I

    return v0
.end method

.method public getRepeatMode()I
    .locals 1

    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->F2()V

    iget v0, p0, Landroidx/media3/exoplayer/c1;->H:I

    return v0
.end method

.method public getShuffleModeEnabled()Z
    .locals 1

    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->F2()V

    iget-boolean v0, p0, Landroidx/media3/exoplayer/c1;->I:Z

    return v0
.end method

.method public getVolume()F
    .locals 1

    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->F2()V

    iget v0, p0, Landroidx/media3/exoplayer/c1;->j0:F

    return v0
.end method

.method public h()Ld2/b;
    .locals 1

    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->F2()V

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->l0:Ld2/b;

    return-object v0
.end method

.method public final h2(Landroidx/media3/exoplayer/s2;Landroidx/media3/common/m0;Landroid/util/Pair;)Landroidx/media3/exoplayer/s2;
    .locals 21
    .param p3    # Landroid/util/Pair;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroidx/media3/exoplayer/s2;",
            "Landroidx/media3/common/m0;",
            "Landroid/util/Pair<",
            "Ljava/lang/Object;",
            "Ljava/lang/Long;",
            ">;)",
            "Landroidx/media3/exoplayer/s2;"
        }
    .end annotation

    move-object/from16 v0, p0

    move-object/from16 v1, p2

    move-object/from16 v2, p3

    invoke-virtual/range {p2 .. p2}, Landroidx/media3/common/m0;->q()Z

    move-result v3

    const/4 v4, 0x1

    if-nez v3, :cond_1

    if-eqz v2, :cond_0

    goto :goto_0

    :cond_0
    const/4 v3, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 v3, 0x1

    :goto_1
    invoke-static {v3}, Le2/a;->a(Z)V

    move-object/from16 v3, p1

    iget-object v5, v3, Landroidx/media3/exoplayer/s2;->a:Landroidx/media3/common/m0;

    invoke-virtual/range {p0 .. p1}, Landroidx/media3/exoplayer/c1;->x1(Landroidx/media3/exoplayer/s2;)J

    move-result-wide v6

    invoke-virtual/range {p1 .. p2}, Landroidx/media3/exoplayer/s2;->j(Landroidx/media3/common/m0;)Landroidx/media3/exoplayer/s2;

    move-result-object v8

    invoke-virtual/range {p2 .. p2}, Landroidx/media3/common/m0;->q()Z

    move-result v3

    if-eqz v3, :cond_2

    invoke-static {}, Landroidx/media3/exoplayer/s2;->l()Landroidx/media3/exoplayer/source/l$b;

    move-result-object v1

    iget-wide v2, v0, Landroidx/media3/exoplayer/c1;->x0:J

    invoke-static {v2, v3}, Le2/u0;->S0(J)J

    move-result-wide v14

    const-wide/16 v16, 0x0

    sget-object v18, Lu2/k0;->d:Lu2/k0;

    iget-object v2, v0, Landroidx/media3/exoplayer/c1;->b:Lx2/f0;

    invoke-static {}, Lcom/google/common/collect/ImmutableList;->of()Lcom/google/common/collect/ImmutableList;

    move-result-object v20

    move-object v9, v1

    move-wide v10, v14

    move-wide v12, v14

    move-object/from16 v19, v2

    invoke-virtual/range {v8 .. v20}, Landroidx/media3/exoplayer/s2;->d(Landroidx/media3/exoplayer/source/l$b;JJJJLu2/k0;Lx2/f0;Ljava/util/List;)Landroidx/media3/exoplayer/s2;

    move-result-object v2

    invoke-virtual {v2, v1}, Landroidx/media3/exoplayer/s2;->c(Landroidx/media3/exoplayer/source/l$b;)Landroidx/media3/exoplayer/s2;

    move-result-object v1

    iget-wide v2, v1, Landroidx/media3/exoplayer/s2;->r:J

    iput-wide v2, v1, Landroidx/media3/exoplayer/s2;->p:J

    return-object v1

    :cond_2
    iget-object v3, v8, Landroidx/media3/exoplayer/s2;->b:Landroidx/media3/exoplayer/source/l$b;

    iget-object v3, v3, Landroidx/media3/exoplayer/source/l$b;->a:Ljava/lang/Object;

    invoke-static/range {p3 .. p3}, Le2/u0;->i(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v9

    check-cast v9, Landroid/util/Pair;

    iget-object v9, v9, Landroid/util/Pair;->first:Ljava/lang/Object;

    invoke-virtual {v3, v9}, Ljava/lang/Object;->equals(Ljava/lang/Object;)Z

    move-result v9

    xor-int/2addr v9, v4

    if-eqz v9, :cond_3

    new-instance v10, Landroidx/media3/exoplayer/source/l$b;

    iget-object v11, v2, Landroid/util/Pair;->first:Ljava/lang/Object;

    invoke-direct {v10, v11}, Landroidx/media3/exoplayer/source/l$b;-><init>(Ljava/lang/Object;)V

    :goto_2
    move-object v14, v10

    goto :goto_3

    :cond_3
    iget-object v10, v8, Landroidx/media3/exoplayer/s2;->b:Landroidx/media3/exoplayer/source/l$b;

    goto :goto_2

    :goto_3
    iget-object v2, v2, Landroid/util/Pair;->second:Ljava/lang/Object;

    check-cast v2, Ljava/lang/Long;

    invoke-virtual {v2}, Ljava/lang/Long;->longValue()J

    move-result-wide v12

    invoke-static {v6, v7}, Le2/u0;->S0(J)J

    move-result-wide v6

    invoke-virtual {v5}, Landroidx/media3/common/m0;->q()Z

    move-result v2

    if-nez v2, :cond_4

    iget-object v2, v0, Landroidx/media3/exoplayer/c1;->n:Landroidx/media3/common/m0$b;

    invoke-virtual {v5, v3, v2}, Landroidx/media3/common/m0;->h(Ljava/lang/Object;Landroidx/media3/common/m0$b;)Landroidx/media3/common/m0$b;

    move-result-object v2

    invoke-virtual {v2}, Landroidx/media3/common/m0$b;->o()J

    move-result-wide v2

    sub-long/2addr v6, v2

    :cond_4
    if-nez v9, :cond_5

    cmp-long v2, v12, v6

    if-gez v2, :cond_6

    :cond_5
    move-wide v6, v12

    move-object v0, v14

    goto/16 :goto_6

    :cond_6
    if-nez v2, :cond_a

    iget-object v2, v8, Landroidx/media3/exoplayer/s2;->k:Landroidx/media3/exoplayer/source/l$b;

    iget-object v2, v2, Landroidx/media3/exoplayer/source/l$b;->a:Ljava/lang/Object;

    invoke-virtual {v1, v2}, Landroidx/media3/common/m0;->b(Ljava/lang/Object;)I

    move-result v2

    const/4 v3, -0x1

    if-eq v2, v3, :cond_7

    iget-object v3, v0, Landroidx/media3/exoplayer/c1;->n:Landroidx/media3/common/m0$b;

    invoke-virtual {v1, v2, v3}, Landroidx/media3/common/m0;->f(ILandroidx/media3/common/m0$b;)Landroidx/media3/common/m0$b;

    move-result-object v2

    iget v2, v2, Landroidx/media3/common/m0$b;->c:I

    iget-object v3, v14, Landroidx/media3/exoplayer/source/l$b;->a:Ljava/lang/Object;

    iget-object v4, v0, Landroidx/media3/exoplayer/c1;->n:Landroidx/media3/common/m0$b;

    invoke-virtual {v1, v3, v4}, Landroidx/media3/common/m0;->h(Ljava/lang/Object;Landroidx/media3/common/m0$b;)Landroidx/media3/common/m0$b;

    move-result-object v3

    iget v3, v3, Landroidx/media3/common/m0$b;->c:I

    if-eq v2, v3, :cond_9

    :cond_7
    iget-object v2, v14, Landroidx/media3/exoplayer/source/l$b;->a:Ljava/lang/Object;

    iget-object v3, v0, Landroidx/media3/exoplayer/c1;->n:Landroidx/media3/common/m0$b;

    invoke-virtual {v1, v2, v3}, Landroidx/media3/common/m0;->h(Ljava/lang/Object;Landroidx/media3/common/m0$b;)Landroidx/media3/common/m0$b;

    invoke-virtual {v14}, Landroidx/media3/exoplayer/source/l$b;->b()Z

    move-result v1

    if-eqz v1, :cond_8

    iget-object v1, v0, Landroidx/media3/exoplayer/c1;->n:Landroidx/media3/common/m0$b;

    iget v2, v14, Landroidx/media3/exoplayer/source/l$b;->b:I

    iget v3, v14, Landroidx/media3/exoplayer/source/l$b;->c:I

    invoke-virtual {v1, v2, v3}, Landroidx/media3/common/m0$b;->b(II)J

    move-result-wide v1

    goto :goto_4

    :cond_8
    iget-object v1, v0, Landroidx/media3/exoplayer/c1;->n:Landroidx/media3/common/m0$b;

    iget-wide v1, v1, Landroidx/media3/common/m0$b;->d:J

    :goto_4
    iget-wide v10, v8, Landroidx/media3/exoplayer/s2;->r:J

    iget-wide v12, v8, Landroidx/media3/exoplayer/s2;->r:J

    iget-wide v3, v8, Landroidx/media3/exoplayer/s2;->d:J

    iget-wide v5, v8, Landroidx/media3/exoplayer/s2;->r:J

    sub-long v16, v1, v5

    iget-object v5, v8, Landroidx/media3/exoplayer/s2;->h:Lu2/k0;

    iget-object v6, v8, Landroidx/media3/exoplayer/s2;->i:Lx2/f0;

    iget-object v7, v8, Landroidx/media3/exoplayer/s2;->j:Ljava/util/List;

    move-object v9, v14

    move-object v0, v14

    move-wide v14, v3

    move-object/from16 v18, v5

    move-object/from16 v19, v6

    move-object/from16 v20, v7

    invoke-virtual/range {v8 .. v20}, Landroidx/media3/exoplayer/s2;->d(Landroidx/media3/exoplayer/source/l$b;JJJJLu2/k0;Lx2/f0;Ljava/util/List;)Landroidx/media3/exoplayer/s2;

    move-result-object v3

    invoke-virtual {v3, v0}, Landroidx/media3/exoplayer/s2;->c(Landroidx/media3/exoplayer/source/l$b;)Landroidx/media3/exoplayer/s2;

    move-result-object v8

    iput-wide v1, v8, Landroidx/media3/exoplayer/s2;->p:J

    :cond_9
    :goto_5
    move-object/from16 v0, p0

    goto/16 :goto_d

    :cond_a
    move-object v0, v14

    invoke-virtual {v0}, Landroidx/media3/exoplayer/source/l$b;->b()Z

    move-result v1

    xor-int/2addr v1, v4

    invoke-static {v1}, Le2/a;->g(Z)V

    iget-wide v1, v8, Landroidx/media3/exoplayer/s2;->q:J

    sub-long v3, v12, v6

    sub-long/2addr v1, v3

    const-wide/16 v3, 0x0

    invoke-static {v3, v4, v1, v2}, Ljava/lang/Math;->max(JJ)J

    move-result-wide v16

    iget-wide v1, v8, Landroidx/media3/exoplayer/s2;->p:J

    iget-object v3, v8, Landroidx/media3/exoplayer/s2;->k:Landroidx/media3/exoplayer/source/l$b;

    iget-object v4, v8, Landroidx/media3/exoplayer/s2;->b:Landroidx/media3/exoplayer/source/l$b;

    invoke-virtual {v3, v4}, Landroidx/media3/exoplayer/source/l$b;->equals(Ljava/lang/Object;)Z

    move-result v3

    if-eqz v3, :cond_b

    add-long v1, v12, v16

    :cond_b
    iget-object v3, v8, Landroidx/media3/exoplayer/s2;->h:Lu2/k0;

    iget-object v4, v8, Landroidx/media3/exoplayer/s2;->i:Lx2/f0;

    iget-object v5, v8, Landroidx/media3/exoplayer/s2;->j:Ljava/util/List;

    move-object v9, v0

    move-wide v10, v12

    move-wide v6, v12

    move-wide v14, v6

    move-object/from16 v18, v3

    move-object/from16 v19, v4

    move-object/from16 v20, v5

    invoke-virtual/range {v8 .. v20}, Landroidx/media3/exoplayer/s2;->d(Landroidx/media3/exoplayer/source/l$b;JJJJLu2/k0;Lx2/f0;Ljava/util/List;)Landroidx/media3/exoplayer/s2;

    move-result-object v8

    iput-wide v1, v8, Landroidx/media3/exoplayer/s2;->p:J

    goto :goto_5

    :goto_6
    invoke-virtual {v0}, Landroidx/media3/exoplayer/source/l$b;->b()Z

    move-result v1

    xor-int/2addr v1, v4

    invoke-static {v1}, Le2/a;->g(Z)V

    const-wide/16 v16, 0x0

    if-eqz v9, :cond_c

    sget-object v1, Lu2/k0;->d:Lu2/k0;

    :goto_7
    move-object/from16 v18, v1

    goto :goto_8

    :cond_c
    iget-object v1, v8, Landroidx/media3/exoplayer/s2;->h:Lu2/k0;

    goto :goto_7

    :goto_8
    move-object v1, v0

    move-object/from16 v0, p0

    if-eqz v9, :cond_d

    iget-object v2, v0, Landroidx/media3/exoplayer/c1;->b:Lx2/f0;

    :goto_9
    move-object/from16 v19, v2

    goto :goto_a

    :cond_d
    iget-object v2, v8, Landroidx/media3/exoplayer/s2;->i:Lx2/f0;

    goto :goto_9

    :goto_a
    if-eqz v9, :cond_e

    invoke-static {}, Lcom/google/common/collect/ImmutableList;->of()Lcom/google/common/collect/ImmutableList;

    move-result-object v2

    :goto_b
    move-object/from16 v20, v2

    goto :goto_c

    :cond_e
    iget-object v2, v8, Landroidx/media3/exoplayer/s2;->j:Ljava/util/List;

    goto :goto_b

    :goto_c
    move-object v9, v1

    move-wide v10, v6

    move-wide v12, v6

    move-wide v14, v6

    invoke-virtual/range {v8 .. v20}, Landroidx/media3/exoplayer/s2;->d(Landroidx/media3/exoplayer/source/l$b;JJJJLu2/k0;Lx2/f0;Ljava/util/List;)Landroidx/media3/exoplayer/s2;

    move-result-object v2

    invoke-virtual {v2, v1}, Landroidx/media3/exoplayer/s2;->c(Landroidx/media3/exoplayer/source/l$b;)Landroidx/media3/exoplayer/s2;

    move-result-object v8

    iput-wide v6, v8, Landroidx/media3/exoplayer/s2;->p:J

    :goto_d
    return-object v8
.end method

.method public final i2(Landroidx/media3/common/m0;IJ)Landroid/util/Pair;
    .locals 6
    .annotation build Landroidx/annotation/Nullable;
    .end annotation

    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroidx/media3/common/m0;",
            "IJ)",
            "Landroid/util/Pair<",
            "Ljava/lang/Object;",
            "Ljava/lang/Long;",
            ">;"
        }
    .end annotation

    invoke-virtual {p1}, Landroidx/media3/common/m0;->q()Z

    move-result v0

    if-eqz v0, :cond_1

    iput p2, p0, Landroidx/media3/exoplayer/c1;->v0:I

    const-wide p1, -0x7fffffffffffffffL    # -4.9E-324

    cmp-long v0, p3, p1

    if-nez v0, :cond_0

    const-wide/16 p3, 0x0

    :cond_0
    iput-wide p3, p0, Landroidx/media3/exoplayer/c1;->x0:J

    const/4 p1, 0x0

    iput p1, p0, Landroidx/media3/exoplayer/c1;->w0:I

    const/4 p1, 0x0

    return-object p1

    :cond_1
    const/4 v0, -0x1

    if-eq p2, v0, :cond_3

    invoke-virtual {p1}, Landroidx/media3/common/m0;->p()I

    move-result v0

    if-lt p2, v0, :cond_2

    goto :goto_1

    :cond_2
    :goto_0
    move v3, p2

    goto :goto_2

    :cond_3
    :goto_1
    iget-boolean p2, p0, Landroidx/media3/exoplayer/c1;->I:Z

    invoke-virtual {p1, p2}, Landroidx/media3/common/m0;->a(Z)I

    move-result p2

    iget-object p3, p0, Landroidx/media3/common/h;->a:Landroidx/media3/common/m0$c;

    invoke-virtual {p1, p2, p3}, Landroidx/media3/common/m0;->n(ILandroidx/media3/common/m0$c;)Landroidx/media3/common/m0$c;

    move-result-object p3

    invoke-virtual {p3}, Landroidx/media3/common/m0$c;->b()J

    move-result-wide p3

    goto :goto_0

    :goto_2
    iget-object v1, p0, Landroidx/media3/common/h;->a:Landroidx/media3/common/m0$c;

    iget-object v2, p0, Landroidx/media3/exoplayer/c1;->n:Landroidx/media3/common/m0$b;

    invoke-static {p3, p4}, Le2/u0;->S0(J)J

    move-result-wide v4

    move-object v0, p1

    invoke-virtual/range {v0 .. v5}, Landroidx/media3/common/m0;->j(Landroidx/media3/common/m0$c;Landroidx/media3/common/m0$b;IJ)Landroid/util/Pair;

    move-result-object p1

    return-object p1
.end method

.method public isPlayingAd()Z
    .locals 1

    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->F2()V

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->u0:Landroidx/media3/exoplayer/s2;

    iget-object v0, v0, Landroidx/media3/exoplayer/s2;->b:Landroidx/media3/exoplayer/source/l$b;

    invoke-virtual {v0}, Landroidx/media3/exoplayer/source/l$b;->b()Z

    move-result v0

    return v0
.end method

.method public final j2(II)V
    .locals 3

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->e0:Le2/e0;

    invoke-virtual {v0}, Le2/e0;->b()I

    move-result v0

    if-ne p1, v0, :cond_0

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->e0:Le2/e0;

    invoke-virtual {v0}, Le2/e0;->a()I

    move-result v0

    if-eq p2, v0, :cond_1

    :cond_0
    new-instance v0, Le2/e0;

    invoke-direct {v0, p1, p2}, Le2/e0;-><init>(II)V

    iput-object v0, p0, Landroidx/media3/exoplayer/c1;->e0:Le2/e0;

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->l:Le2/n;

    new-instance v1, Landroidx/media3/exoplayer/q0;

    invoke-direct {v1, p1, p2}, Landroidx/media3/exoplayer/q0;-><init>(II)V

    const/16 v2, 0x18

    invoke-virtual {v0, v2, v1}, Le2/n;->l(ILe2/n$a;)V

    new-instance v0, Le2/e0;

    invoke-direct {v0, p1, p2}, Le2/e0;-><init>(II)V

    const/4 p1, 0x2

    const/16 p2, 0xe

    invoke-virtual {p0, p1, p2, v0}, Landroidx/media3/exoplayer/c1;->o2(IILjava/lang/Object;)V

    :cond_1
    return-void
.end method

.method public k()I
    .locals 1

    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->F2()V

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->u0:Landroidx/media3/exoplayer/s2;

    iget v0, v0, Landroidx/media3/exoplayer/s2;->m:I

    return v0
.end method

.method public k1(Landroidx/media3/exoplayer/u$a;)V
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->m:Ljava/util/concurrent/CopyOnWriteArraySet;

    invoke-virtual {v0, p1}, Ljava/util/concurrent/CopyOnWriteArraySet;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public final k2(Landroidx/media3/common/m0;Landroidx/media3/exoplayer/source/l$b;J)J
    .locals 1

    iget-object p2, p2, Landroidx/media3/exoplayer/source/l$b;->a:Ljava/lang/Object;

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->n:Landroidx/media3/common/m0$b;

    invoke-virtual {p1, p2, v0}, Landroidx/media3/common/m0;->h(Ljava/lang/Object;Landroidx/media3/common/m0$b;)Landroidx/media3/common/m0$b;

    iget-object p1, p0, Landroidx/media3/exoplayer/c1;->n:Landroidx/media3/common/m0$b;

    invoke-virtual {p1}, Landroidx/media3/common/m0$b;->o()J

    move-result-wide p1

    add-long/2addr p3, p1

    return-wide p3
.end method

.method public l()Landroid/os/Looper;
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->s:Landroid/os/Looper;

    return-object v0
.end method

.method public final l1(ILjava/util/List;)Ljava/util/List;
    .locals 7
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I",
            "Ljava/util/List<",
            "Landroidx/media3/exoplayer/source/l;",
            ">;)",
            "Ljava/util/List<",
            "Landroidx/media3/exoplayer/r2$c;",
            ">;"
        }
    .end annotation

    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    const/4 v1, 0x0

    :goto_0
    invoke-interface {p2}, Ljava/util/List;->size()I

    move-result v2

    if-ge v1, v2, :cond_0

    new-instance v2, Landroidx/media3/exoplayer/r2$c;

    invoke-interface {p2, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Landroidx/media3/exoplayer/source/l;

    iget-boolean v4, p0, Landroidx/media3/exoplayer/c1;->p:Z

    invoke-direct {v2, v3, v4}, Landroidx/media3/exoplayer/r2$c;-><init>(Landroidx/media3/exoplayer/source/l;Z)V

    invoke-interface {v0, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    iget-object v3, p0, Landroidx/media3/exoplayer/c1;->o:Ljava/util/List;

    add-int v4, v1, p1

    new-instance v5, Landroidx/media3/exoplayer/c1$f;

    iget-object v6, v2, Landroidx/media3/exoplayer/r2$c;->b:Ljava/lang/Object;

    iget-object v2, v2, Landroidx/media3/exoplayer/r2$c;->a:Landroidx/media3/exoplayer/source/j;

    invoke-direct {v5, v6, v2}, Landroidx/media3/exoplayer/c1$f;-><init>(Ljava/lang/Object;Landroidx/media3/exoplayer/source/j;)V

    invoke-interface {v3, v4, v5}, Ljava/util/List;->add(ILjava/lang/Object;)V

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_0
    iget-object p2, p0, Landroidx/media3/exoplayer/c1;->O:Lu2/f0;

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v1

    invoke-interface {p2, p1, v1}, Lu2/f0;->cloneAndInsert(II)Lu2/f0;

    move-result-object p1

    iput-object p1, p0, Landroidx/media3/exoplayer/c1;->O:Lu2/f0;

    return-object v0
.end method

.method public final l2(Landroidx/media3/exoplayer/s2;II)Landroidx/media3/exoplayer/s2;
    .locals 10

    invoke-virtual {p0, p1}, Landroidx/media3/exoplayer/c1;->z1(Landroidx/media3/exoplayer/s2;)I

    move-result v6

    invoke-virtual {p0, p1}, Landroidx/media3/exoplayer/c1;->x1(Landroidx/media3/exoplayer/s2;)J

    move-result-wide v4

    iget-object v1, p1, Landroidx/media3/exoplayer/s2;->a:Landroidx/media3/common/m0;

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->o:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v7

    iget v0, p0, Landroidx/media3/exoplayer/c1;->J:I

    const/4 v8, 0x1

    add-int/2addr v0, v8

    iput v0, p0, Landroidx/media3/exoplayer/c1;->J:I

    invoke-virtual {p0, p2, p3}, Landroidx/media3/exoplayer/c1;->m2(II)V

    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->t1()Landroidx/media3/common/m0;

    move-result-object v9

    move-object v0, p0

    move-object v2, v9

    move v3, v6

    invoke-virtual/range {v0 .. v5}, Landroidx/media3/exoplayer/c1;->A1(Landroidx/media3/common/m0;Landroidx/media3/common/m0;IJ)Landroid/util/Pair;

    move-result-object v0

    invoke-virtual {p0, p1, v9, v0}, Landroidx/media3/exoplayer/c1;->h2(Landroidx/media3/exoplayer/s2;Landroidx/media3/common/m0;Landroid/util/Pair;)Landroidx/media3/exoplayer/s2;

    move-result-object p1

    iget v0, p1, Landroidx/media3/exoplayer/s2;->e:I

    if-eq v0, v8, :cond_0

    const/4 v1, 0x4

    if-eq v0, v1, :cond_0

    if-ge p2, p3, :cond_0

    if-ne p3, v7, :cond_0

    iget-object v0, p1, Landroidx/media3/exoplayer/s2;->a:Landroidx/media3/common/m0;

    invoke-virtual {v0}, Landroidx/media3/common/m0;->p()I

    move-result v0

    if-lt v6, v0, :cond_0

    invoke-virtual {p1, v1}, Landroidx/media3/exoplayer/s2;->h(I)Landroidx/media3/exoplayer/s2;

    move-result-object p1

    :cond_0
    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->k:Landroidx/media3/exoplayer/s1;

    iget-object v1, p0, Landroidx/media3/exoplayer/c1;->O:Lu2/f0;

    invoke-virtual {v0, p2, p3, v1}, Landroidx/media3/exoplayer/s1;->s0(IILu2/f0;)V

    return-object p1
.end method

.method public m()Landroidx/media3/common/p0;
    .locals 1

    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->F2()V

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->h:Lx2/e0;

    invoke-virtual {v0}, Lx2/e0;->c()Landroidx/media3/common/p0;

    move-result-object v0

    return-object v0
.end method

.method public m1(ILjava/util/List;)V
    .locals 11
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I",
            "Ljava/util/List<",
            "Landroidx/media3/exoplayer/source/l;",
            ">;)V"
        }
    .end annotation

    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->F2()V

    const/4 v0, 0x0

    const/4 v1, 0x1

    if-ltz p1, :cond_0

    const/4 v2, 0x1

    goto :goto_0

    :cond_0
    const/4 v2, 0x0

    :goto_0
    invoke-static {v2}, Le2/a;->a(Z)V

    iget-object v2, p0, Landroidx/media3/exoplayer/c1;->o:Ljava/util/List;

    invoke-interface {v2}, Ljava/util/List;->size()I

    move-result v2

    invoke-static {p1, v2}, Ljava/lang/Math;->min(II)I

    move-result p1

    iget-object v2, p0, Landroidx/media3/exoplayer/c1;->o:Ljava/util/List;

    invoke-interface {v2}, Ljava/util/List;->isEmpty()Z

    move-result v2

    if-eqz v2, :cond_2

    iget p1, p0, Landroidx/media3/exoplayer/c1;->v0:I

    const/4 v2, -0x1

    if-ne p1, v2, :cond_1

    const/4 v0, 0x1

    :cond_1
    invoke-virtual {p0, p2, v0}, Landroidx/media3/exoplayer/c1;->r2(Ljava/util/List;Z)V

    return-void

    :cond_2
    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->u0:Landroidx/media3/exoplayer/s2;

    invoke-virtual {p0, v0, p1, p2}, Landroidx/media3/exoplayer/c1;->n1(Landroidx/media3/exoplayer/s2;ILjava/util/List;)Landroidx/media3/exoplayer/s2;

    move-result-object v2

    const/4 v3, 0x0

    const/4 v4, 0x1

    const/4 v5, 0x0

    const/4 v6, 0x5

    const-wide v7, -0x7fffffffffffffffL    # -4.9E-324

    const/4 v9, -0x1

    const/4 v10, 0x0

    move-object v1, p0

    invoke-virtual/range {v1 .. v10}, Landroidx/media3/exoplayer/c1;->B2(Landroidx/media3/exoplayer/s2;IIZIJIZ)V

    return-void
.end method

.method public final m2(II)V
    .locals 2

    add-int/lit8 v0, p2, -0x1

    :goto_0
    if-lt v0, p1, :cond_0

    iget-object v1, p0, Landroidx/media3/exoplayer/c1;->o:Ljava/util/List;

    invoke-interface {v1, v0}, Ljava/util/List;->remove(I)Ljava/lang/Object;

    add-int/lit8 v0, v0, -0x1

    goto :goto_0

    :cond_0
    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->O:Lu2/f0;

    invoke-interface {v0, p1, p2}, Lu2/f0;->a(II)Lu2/f0;

    move-result-object p1

    iput-object p1, p0, Landroidx/media3/exoplayer/c1;->O:Lu2/f0;

    return-void
.end method

.method public final n1(Landroidx/media3/exoplayer/s2;ILjava/util/List;)Landroidx/media3/exoplayer/s2;
    .locals 7
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroidx/media3/exoplayer/s2;",
            "I",
            "Ljava/util/List<",
            "Landroidx/media3/exoplayer/source/l;",
            ">;)",
            "Landroidx/media3/exoplayer/s2;"
        }
    .end annotation

    iget-object v1, p1, Landroidx/media3/exoplayer/s2;->a:Landroidx/media3/common/m0;

    iget v0, p0, Landroidx/media3/exoplayer/c1;->J:I

    add-int/lit8 v0, v0, 0x1

    iput v0, p0, Landroidx/media3/exoplayer/c1;->J:I

    invoke-virtual {p0, p2, p3}, Landroidx/media3/exoplayer/c1;->l1(ILjava/util/List;)Ljava/util/List;

    move-result-object p3

    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->t1()Landroidx/media3/common/m0;

    move-result-object v6

    invoke-virtual {p0, p1}, Landroidx/media3/exoplayer/c1;->z1(Landroidx/media3/exoplayer/s2;)I

    move-result v3

    invoke-virtual {p0, p1}, Landroidx/media3/exoplayer/c1;->x1(Landroidx/media3/exoplayer/s2;)J

    move-result-wide v4

    move-object v0, p0

    move-object v2, v6

    invoke-virtual/range {v0 .. v5}, Landroidx/media3/exoplayer/c1;->A1(Landroidx/media3/common/m0;Landroidx/media3/common/m0;IJ)Landroid/util/Pair;

    move-result-object v0

    invoke-virtual {p0, p1, v6, v0}, Landroidx/media3/exoplayer/c1;->h2(Landroidx/media3/exoplayer/s2;Landroidx/media3/common/m0;Landroid/util/Pair;)Landroidx/media3/exoplayer/s2;

    move-result-object p1

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->k:Landroidx/media3/exoplayer/s1;

    iget-object v1, p0, Landroidx/media3/exoplayer/c1;->O:Lu2/f0;

    invoke-virtual {v0, p2, p3, v1}, Landroidx/media3/exoplayer/s1;->m(ILjava/util/List;Lu2/f0;)V

    return-object p1
.end method

.method public final n2()V
    .locals 3

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->Z:Landroidx/media3/exoplayer/video/spherical/SphericalGLSurfaceView;

    const/4 v1, 0x0

    if-eqz v0, :cond_0

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->y:Landroidx/media3/exoplayer/c1$e;

    invoke-virtual {p0, v0}, Landroidx/media3/exoplayer/c1;->v1(Landroidx/media3/exoplayer/t2$b;)Landroidx/media3/exoplayer/t2;

    move-result-object v0

    const/16 v2, 0x2710

    invoke-virtual {v0, v2}, Landroidx/media3/exoplayer/t2;->n(I)Landroidx/media3/exoplayer/t2;

    move-result-object v0

    invoke-virtual {v0, v1}, Landroidx/media3/exoplayer/t2;->m(Ljava/lang/Object;)Landroidx/media3/exoplayer/t2;

    move-result-object v0

    invoke-virtual {v0}, Landroidx/media3/exoplayer/t2;->l()Landroidx/media3/exoplayer/t2;

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->Z:Landroidx/media3/exoplayer/video/spherical/SphericalGLSurfaceView;

    iget-object v2, p0, Landroidx/media3/exoplayer/c1;->x:Landroidx/media3/exoplayer/c1$d;

    invoke-virtual {v0, v2}, Landroidx/media3/exoplayer/video/spherical/SphericalGLSurfaceView;->removeVideoSurfaceListener(Landroidx/media3/exoplayer/video/spherical/SphericalGLSurfaceView$b;)V

    iput-object v1, p0, Landroidx/media3/exoplayer/c1;->Z:Landroidx/media3/exoplayer/video/spherical/SphericalGLSurfaceView;

    :cond_0
    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->b0:Landroid/view/TextureView;

    if-eqz v0, :cond_2

    invoke-virtual {v0}, Landroid/view/TextureView;->getSurfaceTextureListener()Landroid/view/TextureView$SurfaceTextureListener;

    move-result-object v0

    iget-object v2, p0, Landroidx/media3/exoplayer/c1;->x:Landroidx/media3/exoplayer/c1$d;

    if-eq v0, v2, :cond_1

    const-string v0, "ExoPlayerImpl"

    const-string v2, "SurfaceTextureListener already unset or replaced."

    invoke-static {v0, v2}, Le2/o;->h(Ljava/lang/String;Ljava/lang/String;)V

    goto :goto_0

    :cond_1
    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->b0:Landroid/view/TextureView;

    invoke-virtual {v0, v1}, Landroid/view/TextureView;->setSurfaceTextureListener(Landroid/view/TextureView$SurfaceTextureListener;)V

    :goto_0
    iput-object v1, p0, Landroidx/media3/exoplayer/c1;->b0:Landroid/view/TextureView;

    :cond_2
    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->Y:Landroid/view/SurfaceHolder;

    if-eqz v0, :cond_3

    iget-object v2, p0, Landroidx/media3/exoplayer/c1;->x:Landroidx/media3/exoplayer/c1$d;

    invoke-interface {v0, v2}, Landroid/view/SurfaceHolder;->removeCallback(Landroid/view/SurfaceHolder$Callback;)V

    iput-object v1, p0, Landroidx/media3/exoplayer/c1;->Y:Landroid/view/SurfaceHolder;

    :cond_3
    return-void
.end method

.method public o()Landroidx/media3/common/h0$b;
    .locals 1

    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->F2()V

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->Q:Landroidx/media3/common/h0$b;

    return-object v0
.end method

.method public final o1()Landroidx/media3/common/d0;
    .locals 3

    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->getCurrentTimeline()Landroidx/media3/common/m0;

    move-result-object v0

    invoke-virtual {v0}, Landroidx/media3/common/m0;->q()Z

    move-result v1

    if-eqz v1, :cond_0

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->t0:Landroidx/media3/common/d0;

    return-object v0

    :cond_0
    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->u()I

    move-result v1

    iget-object v2, p0, Landroidx/media3/common/h;->a:Landroidx/media3/common/m0$c;

    invoke-virtual {v0, v1, v2}, Landroidx/media3/common/m0;->n(ILandroidx/media3/common/m0$c;)Landroidx/media3/common/m0$c;

    move-result-object v0

    iget-object v0, v0, Landroidx/media3/common/m0$c;->c:Landroidx/media3/common/b0;

    iget-object v1, p0, Landroidx/media3/exoplayer/c1;->t0:Landroidx/media3/common/d0;

    invoke-virtual {v1}, Landroidx/media3/common/d0;->a()Landroidx/media3/common/d0$b;

    move-result-object v1

    iget-object v0, v0, Landroidx/media3/common/b0;->e:Landroidx/media3/common/d0;

    invoke-virtual {v1, v0}, Landroidx/media3/common/d0$b;->J(Landroidx/media3/common/d0;)Landroidx/media3/common/d0$b;

    move-result-object v0

    invoke-virtual {v0}, Landroidx/media3/common/d0$b;->H()Landroidx/media3/common/d0;

    move-result-object v0

    return-object v0
.end method

.method public final o2(IILjava/lang/Object;)V
    .locals 5
    .param p3    # Ljava/lang/Object;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->g:[Landroidx/media3/exoplayer/w2;

    array-length v1, v0

    const/4 v2, 0x0

    :goto_0
    if-ge v2, v1, :cond_1

    aget-object v3, v0, v2

    invoke-interface {v3}, Landroidx/media3/exoplayer/w2;->getTrackType()I

    move-result v4

    if-ne v4, p1, :cond_0

    invoke-virtual {p0, v3}, Landroidx/media3/exoplayer/c1;->v1(Landroidx/media3/exoplayer/t2$b;)Landroidx/media3/exoplayer/t2;

    move-result-object v3

    invoke-virtual {v3, p2}, Landroidx/media3/exoplayer/t2;->n(I)Landroidx/media3/exoplayer/t2;

    move-result-object v3

    invoke-virtual {v3, p3}, Landroidx/media3/exoplayer/t2;->m(Ljava/lang/Object;)Landroidx/media3/exoplayer/t2;

    move-result-object v3

    invoke-virtual {v3}, Landroidx/media3/exoplayer/t2;->l()Landroidx/media3/exoplayer/t2;

    :cond_0
    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_1
    return-void
.end method

.method public p()J
    .locals 2

    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->F2()V

    const-wide/16 v0, 0xbb8

    return-wide v0
.end method

.method public final p1(IILjava/util/List;)Z
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(II",
            "Ljava/util/List<",
            "Landroidx/media3/common/b0;",
            ">;)Z"
        }
    .end annotation

    sub-int v0, p2, p1

    invoke-interface {p3}, Ljava/util/List;->size()I

    move-result v1

    const/4 v2, 0x0

    if-eq v0, v1, :cond_0

    return v2

    :cond_0
    move v0, p1

    :goto_0
    if-ge v0, p2, :cond_2

    iget-object v1, p0, Landroidx/media3/exoplayer/c1;->o:Ljava/util/List;

    invoke-interface {v1, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Landroidx/media3/exoplayer/c1$f;

    invoke-static {v1}, Landroidx/media3/exoplayer/c1$f;->c(Landroidx/media3/exoplayer/c1$f;)Landroidx/media3/exoplayer/source/l;

    move-result-object v1

    sub-int v3, v0, p1

    invoke-interface {p3, v3}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Landroidx/media3/common/b0;

    invoke-interface {v1, v3}, Landroidx/media3/exoplayer/source/l;->q(Landroidx/media3/common/b0;)Z

    move-result v1

    if-nez v1, :cond_1

    return v2

    :cond_1
    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :cond_2
    const/4 p1, 0x1

    return p1
.end method

.method public final p2()V
    .locals 3

    iget v0, p0, Landroidx/media3/exoplayer/c1;->j0:F

    iget-object v1, p0, Landroidx/media3/exoplayer/c1;->A:Landroidx/media3/exoplayer/l;

    invoke-virtual {v1}, Landroidx/media3/exoplayer/l;->g()F

    move-result v1

    mul-float v0, v0, v1

    const/4 v1, 0x2

    invoke-static {v0}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    move-result-object v0

    const/4 v2, 0x1

    invoke-virtual {p0, v2, v1, v0}, Landroidx/media3/exoplayer/c1;->o2(IILjava/lang/Object;)V

    return-void
.end method

.method public prepare()V
    .locals 14

    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->F2()V

    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->getPlayWhenReady()Z

    move-result v0

    iget-object v1, p0, Landroidx/media3/exoplayer/c1;->A:Landroidx/media3/exoplayer/l;

    const/4 v2, 0x2

    invoke-virtual {v1, v0, v2}, Landroidx/media3/exoplayer/l;->p(ZI)I

    move-result v1

    invoke-static {v0, v1}, Landroidx/media3/exoplayer/c1;->B1(ZI)I

    move-result v3

    invoke-virtual {p0, v0, v1, v3}, Landroidx/media3/exoplayer/c1;->A2(ZII)V

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->u0:Landroidx/media3/exoplayer/s2;

    iget v1, v0, Landroidx/media3/exoplayer/s2;->e:I

    const/4 v3, 0x1

    if-eq v1, v3, :cond_0

    return-void

    :cond_0
    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Landroidx/media3/exoplayer/s2;->f(Landroidx/media3/exoplayer/ExoPlaybackException;)Landroidx/media3/exoplayer/s2;

    move-result-object v0

    iget-object v1, v0, Landroidx/media3/exoplayer/s2;->a:Landroidx/media3/common/m0;

    invoke-virtual {v1}, Landroidx/media3/common/m0;->q()Z

    move-result v1

    if-eqz v1, :cond_1

    const/4 v2, 0x4

    :cond_1
    invoke-virtual {v0, v2}, Landroidx/media3/exoplayer/s2;->h(I)Landroidx/media3/exoplayer/s2;

    move-result-object v5

    iget v0, p0, Landroidx/media3/exoplayer/c1;->J:I

    add-int/2addr v0, v3

    iput v0, p0, Landroidx/media3/exoplayer/c1;->J:I

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->k:Landroidx/media3/exoplayer/s1;

    invoke-virtual {v0}, Landroidx/media3/exoplayer/s1;->m0()V

    const/4 v6, 0x1

    const/4 v7, 0x1

    const/4 v8, 0x0

    const/4 v9, 0x5

    const-wide v10, -0x7fffffffffffffffL    # -4.9E-324

    const/4 v12, -0x1

    const/4 v13, 0x0

    move-object v4, p0

    invoke-virtual/range {v4 .. v13}, Landroidx/media3/exoplayer/c1;->B2(Landroidx/media3/exoplayer/s2;IIZIJIZ)V

    return-void
.end method

.method public q()Landroidx/media3/common/t0;
    .locals 1

    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->F2()V

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->s0:Landroidx/media3/common/t0;

    return-object v0
.end method

.method public q1(Landroid/view/SurfaceHolder;)V
    .locals 1
    .param p1    # Landroid/view/SurfaceHolder;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->F2()V

    if-eqz p1, :cond_0

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->Y:Landroid/view/SurfaceHolder;

    if-ne p1, v0, :cond_0

    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->clearVideoSurface()V

    :cond_0
    return-void
.end method

.method public q2(Ljava/util/List;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Landroidx/media3/exoplayer/source/l;",
            ">;)V"
        }
    .end annotation

    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->F2()V

    const/4 v0, 0x1

    invoke-virtual {p0, p1, v0}, Landroidx/media3/exoplayer/c1;->r2(Ljava/util/List;Z)V

    return-void
.end method

.method public final r1(ZI)I
    .locals 1

    if-eqz p1, :cond_0

    const/4 v0, 0x1

    if-eq p2, v0, :cond_0

    return v0

    :cond_0
    iget-boolean p2, p0, Landroidx/media3/exoplayer/c1;->G:Z

    if-eqz p2, :cond_2

    const/4 p2, 0x3

    if-eqz p1, :cond_1

    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->G1()Z

    move-result v0

    if-nez v0, :cond_1

    return p2

    :cond_1
    if-nez p1, :cond_2

    iget-object p1, p0, Landroidx/media3/exoplayer/c1;->u0:Landroidx/media3/exoplayer/s2;

    iget p1, p1, Landroidx/media3/exoplayer/s2;->m:I

    if-ne p1, p2, :cond_2

    return p2

    :cond_2
    const/4 p1, 0x0

    return p1
.end method

.method public r2(Ljava/util/List;Z)V
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Landroidx/media3/exoplayer/source/l;",
            ">;Z)V"
        }
    .end annotation

    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->F2()V

    const/4 v2, -0x1

    const-wide v3, -0x7fffffffffffffffL    # -4.9E-324

    move-object v0, p0

    move-object v1, p1

    move v5, p2

    invoke-virtual/range {v0 .. v5}, Landroidx/media3/exoplayer/c1;->s2(Ljava/util/List;IJZ)V

    return-void
.end method

.method public release()V
    .locals 6

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "Release "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-static {p0}, Ljava/lang/System;->identityHashCode(Ljava/lang/Object;)I

    move-result v1

    invoke-static {v1}, Ljava/lang/Integer;->toHexString(I)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v1, " ["

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v1, "AndroidXMedia3/1.3.1"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v1, "] ["

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    sget-object v2, Le2/u0;->e:Ljava/lang/String;

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-static {}, Landroidx/media3/common/c0;->b()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v1, "]"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    const-string v1, "ExoPlayerImpl"

    invoke-static {v1, v0}, Le2/o;->f(Ljava/lang/String;Ljava/lang/String;)V

    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->F2()V

    sget v0, Le2/u0;->a:I

    const/16 v1, 0x15

    const/4 v2, 0x0

    if-ge v0, v1, :cond_0

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->V:Landroid/media/AudioTrack;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Landroid/media/AudioTrack;->release()V

    iput-object v2, p0, Landroidx/media3/exoplayer/c1;->V:Landroid/media/AudioTrack;

    :cond_0
    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->z:Landroidx/media3/exoplayer/AudioBecomingNoisyManager;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Landroidx/media3/exoplayer/AudioBecomingNoisyManager;->b(Z)V

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->B:Landroidx/media3/exoplayer/f3;

    if-eqz v0, :cond_1

    invoke-virtual {v0}, Landroidx/media3/exoplayer/f3;->h()V

    :cond_1
    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->C:Landroidx/media3/exoplayer/h3;

    invoke-virtual {v0, v1}, Landroidx/media3/exoplayer/h3;->b(Z)V

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->D:Landroidx/media3/exoplayer/i3;

    invoke-virtual {v0, v1}, Landroidx/media3/exoplayer/i3;->b(Z)V

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->A:Landroidx/media3/exoplayer/l;

    invoke-virtual {v0}, Landroidx/media3/exoplayer/l;->i()V

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->k:Landroidx/media3/exoplayer/s1;

    invoke-virtual {v0}, Landroidx/media3/exoplayer/s1;->o0()Z

    move-result v0

    if-nez v0, :cond_2

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->l:Le2/n;

    new-instance v3, Landroidx/media3/exoplayer/n0;

    invoke-direct {v3}, Landroidx/media3/exoplayer/n0;-><init>()V

    const/16 v4, 0xa

    invoke-virtual {v0, v4, v3}, Le2/n;->l(ILe2/n$a;)V

    :cond_2
    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->l:Le2/n;

    invoke-virtual {v0}, Le2/n;->j()V

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->i:Le2/j;

    invoke-interface {v0, v2}, Le2/j;->removeCallbacksAndMessages(Ljava/lang/Object;)V

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->t:Landroidx/media3/exoplayer/upstream/e;

    iget-object v3, p0, Landroidx/media3/exoplayer/c1;->r:Lj2/a;

    invoke-interface {v0, v3}, Landroidx/media3/exoplayer/upstream/e;->c(Landroidx/media3/exoplayer/upstream/e$a;)V

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->u0:Landroidx/media3/exoplayer/s2;

    iget-boolean v3, v0, Landroidx/media3/exoplayer/s2;->o:Z

    if-eqz v3, :cond_3

    invoke-virtual {v0}, Landroidx/media3/exoplayer/s2;->a()Landroidx/media3/exoplayer/s2;

    move-result-object v0

    iput-object v0, p0, Landroidx/media3/exoplayer/c1;->u0:Landroidx/media3/exoplayer/s2;

    :cond_3
    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->u0:Landroidx/media3/exoplayer/s2;

    const/4 v3, 0x1

    invoke-virtual {v0, v3}, Landroidx/media3/exoplayer/s2;->h(I)Landroidx/media3/exoplayer/s2;

    move-result-object v0

    iput-object v0, p0, Landroidx/media3/exoplayer/c1;->u0:Landroidx/media3/exoplayer/s2;

    iget-object v4, v0, Landroidx/media3/exoplayer/s2;->b:Landroidx/media3/exoplayer/source/l$b;

    invoke-virtual {v0, v4}, Landroidx/media3/exoplayer/s2;->c(Landroidx/media3/exoplayer/source/l$b;)Landroidx/media3/exoplayer/s2;

    move-result-object v0

    iput-object v0, p0, Landroidx/media3/exoplayer/c1;->u0:Landroidx/media3/exoplayer/s2;

    iget-wide v4, v0, Landroidx/media3/exoplayer/s2;->r:J

    iput-wide v4, v0, Landroidx/media3/exoplayer/s2;->p:J

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->u0:Landroidx/media3/exoplayer/s2;

    const-wide/16 v4, 0x0

    iput-wide v4, v0, Landroidx/media3/exoplayer/s2;->q:J

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->r:Lj2/a;

    invoke-interface {v0}, Lj2/a;->release()V

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->h:Lx2/e0;

    invoke-virtual {v0}, Lx2/e0;->j()V

    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->n2()V

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->X:Landroid/view/Surface;

    if-eqz v0, :cond_4

    invoke-virtual {v0}, Landroid/view/Surface;->release()V

    iput-object v2, p0, Landroidx/media3/exoplayer/c1;->X:Landroid/view/Surface;

    :cond_4
    iget-boolean v0, p0, Landroidx/media3/exoplayer/c1;->p0:Z

    if-eqz v0, :cond_5

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->o0:Landroidx/media3/common/PriorityTaskManager;

    invoke-static {v0}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroidx/media3/common/PriorityTaskManager;

    invoke-virtual {v0, v1}, Landroidx/media3/common/PriorityTaskManager;->d(I)V

    iput-boolean v1, p0, Landroidx/media3/exoplayer/c1;->p0:Z

    :cond_5
    sget-object v0, Ld2/b;->c:Ld2/b;

    iput-object v0, p0, Landroidx/media3/exoplayer/c1;->l0:Ld2/b;

    iput-boolean v3, p0, Landroidx/media3/exoplayer/c1;->q0:Z

    return-void
.end method

.method public s()J
    .locals 2

    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->F2()V

    iget-wide v0, p0, Landroidx/media3/exoplayer/c1;->v:J

    return-wide v0
.end method

.method public final s2(Ljava/util/List;IJZ)V
    .locals 17
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Landroidx/media3/exoplayer/source/l;",
            ">;IJZ)V"
        }
    .end annotation

    move-object/from16 v10, p0

    move/from16 v0, p2

    iget-object v1, v10, Landroidx/media3/exoplayer/c1;->u0:Landroidx/media3/exoplayer/s2;

    invoke-virtual {v10, v1}, Landroidx/media3/exoplayer/c1;->z1(Landroidx/media3/exoplayer/s2;)I

    move-result v1

    invoke-virtual/range {p0 .. p0}, Landroidx/media3/exoplayer/c1;->getCurrentPosition()J

    move-result-wide v2

    iget v4, v10, Landroidx/media3/exoplayer/c1;->J:I

    const/4 v5, 0x1

    add-int/2addr v4, v5

    iput v4, v10, Landroidx/media3/exoplayer/c1;->J:I

    iget-object v4, v10, Landroidx/media3/exoplayer/c1;->o:Ljava/util/List;

    invoke-interface {v4}, Ljava/util/List;->isEmpty()Z

    move-result v4

    const/4 v6, 0x0

    if-nez v4, :cond_0

    iget-object v4, v10, Landroidx/media3/exoplayer/c1;->o:Ljava/util/List;

    invoke-interface {v4}, Ljava/util/List;->size()I

    move-result v4

    invoke-virtual {v10, v6, v4}, Landroidx/media3/exoplayer/c1;->m2(II)V

    :cond_0
    move-object/from16 v4, p1

    invoke-virtual {v10, v6, v4}, Landroidx/media3/exoplayer/c1;->l1(ILjava/util/List;)Ljava/util/List;

    move-result-object v12

    invoke-virtual/range {p0 .. p0}, Landroidx/media3/exoplayer/c1;->t1()Landroidx/media3/common/m0;

    move-result-object v4

    invoke-virtual {v4}, Landroidx/media3/common/m0;->q()Z

    move-result v7

    if-nez v7, :cond_1

    invoke-virtual {v4}, Landroidx/media3/common/m0;->p()I

    move-result v7

    if-ge v0, v7, :cond_2

    :cond_1
    move-wide/from16 v7, p3

    goto :goto_0

    :cond_2
    new-instance v1, Landroidx/media3/common/IllegalSeekPositionException;

    move-wide/from16 v7, p3

    invoke-direct {v1, v4, v0, v7, v8}, Landroidx/media3/common/IllegalSeekPositionException;-><init>(Landroidx/media3/common/m0;IJ)V

    throw v1

    :goto_0
    const/4 v9, -0x1

    if-eqz p5, :cond_3

    iget-boolean v0, v10, Landroidx/media3/exoplayer/c1;->I:Z

    invoke-virtual {v4, v0}, Landroidx/media3/common/m0;->a(Z)I

    move-result v0

    const-wide v1, -0x7fffffffffffffffL    # -4.9E-324

    move v13, v0

    goto :goto_1

    :cond_3
    if-ne v0, v9, :cond_4

    move v13, v1

    move-wide v1, v2

    goto :goto_1

    :cond_4
    move v13, v0

    move-wide v1, v7

    :goto_1
    iget-object v0, v10, Landroidx/media3/exoplayer/c1;->u0:Landroidx/media3/exoplayer/s2;

    invoke-virtual {v10, v4, v13, v1, v2}, Landroidx/media3/exoplayer/c1;->i2(Landroidx/media3/common/m0;IJ)Landroid/util/Pair;

    move-result-object v3

    invoke-virtual {v10, v0, v4, v3}, Landroidx/media3/exoplayer/c1;->h2(Landroidx/media3/exoplayer/s2;Landroidx/media3/common/m0;Landroid/util/Pair;)Landroidx/media3/exoplayer/s2;

    move-result-object v0

    iget v3, v0, Landroidx/media3/exoplayer/s2;->e:I

    if-eq v13, v9, :cond_7

    if-eq v3, v5, :cond_7

    invoke-virtual {v4}, Landroidx/media3/common/m0;->q()Z

    move-result v3

    if-nez v3, :cond_6

    invoke-virtual {v4}, Landroidx/media3/common/m0;->p()I

    move-result v3

    if-lt v13, v3, :cond_5

    goto :goto_2

    :cond_5
    const/4 v3, 0x2

    goto :goto_3

    :cond_6
    :goto_2
    const/4 v3, 0x4

    :cond_7
    :goto_3
    invoke-virtual {v0, v3}, Landroidx/media3/exoplayer/s2;->h(I)Landroidx/media3/exoplayer/s2;

    move-result-object v3

    iget-object v11, v10, Landroidx/media3/exoplayer/c1;->k:Landroidx/media3/exoplayer/s1;

    invoke-static {v1, v2}, Le2/u0;->S0(J)J

    move-result-wide v14

    iget-object v0, v10, Landroidx/media3/exoplayer/c1;->O:Lu2/f0;

    move-object/from16 v16, v0

    invoke-virtual/range {v11 .. v16}, Landroidx/media3/exoplayer/s1;->T0(Ljava/util/List;IJLu2/f0;)V

    iget-object v0, v10, Landroidx/media3/exoplayer/c1;->u0:Landroidx/media3/exoplayer/s2;

    iget-object v0, v0, Landroidx/media3/exoplayer/s2;->b:Landroidx/media3/exoplayer/source/l$b;

    iget-object v0, v0, Landroidx/media3/exoplayer/source/l$b;->a:Ljava/lang/Object;

    iget-object v1, v3, Landroidx/media3/exoplayer/s2;->b:Landroidx/media3/exoplayer/source/l$b;

    iget-object v1, v1, Landroidx/media3/exoplayer/source/l$b;->a:Ljava/lang/Object;

    invoke-virtual {v0, v1}, Ljava/lang/Object;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_8

    iget-object v0, v10, Landroidx/media3/exoplayer/c1;->u0:Landroidx/media3/exoplayer/s2;

    iget-object v0, v0, Landroidx/media3/exoplayer/s2;->a:Landroidx/media3/common/m0;

    invoke-virtual {v0}, Landroidx/media3/common/m0;->q()Z

    move-result v0

    if-nez v0, :cond_8

    const/4 v4, 0x1

    goto :goto_4

    :cond_8
    const/4 v4, 0x0

    :goto_4
    const/4 v2, 0x0

    const/4 v5, 0x1

    const/4 v6, 0x4

    invoke-virtual {v10, v3}, Landroidx/media3/exoplayer/c1;->y1(Landroidx/media3/exoplayer/s2;)J

    move-result-wide v7

    const/4 v9, -0x1

    const/4 v11, 0x0

    move-object/from16 v0, p0

    move-object v1, v3

    move v3, v5

    move v5, v6

    move-wide v6, v7

    move v8, v9

    move v9, v11

    invoke-virtual/range {v0 .. v9}, Landroidx/media3/exoplayer/c1;->B2(Landroidx/media3/exoplayer/s2;IIZIJIZ)V

    return-void
.end method

.method public setPlayWhenReady(Z)V
    .locals 2

    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->F2()V

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->A:Landroidx/media3/exoplayer/l;

    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->getPlaybackState()I

    move-result v1

    invoke-virtual {v0, p1, v1}, Landroidx/media3/exoplayer/l;->p(ZI)I

    move-result v0

    invoke-static {p1, v0}, Landroidx/media3/exoplayer/c1;->B1(ZI)I

    move-result v1

    invoke-virtual {p0, p1, v0, v1}, Landroidx/media3/exoplayer/c1;->A2(ZII)V

    return-void
.end method

.method public setRepeatMode(I)V
    .locals 2

    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->F2()V

    iget v0, p0, Landroidx/media3/exoplayer/c1;->H:I

    if-eq v0, p1, :cond_0

    iput p1, p0, Landroidx/media3/exoplayer/c1;->H:I

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->k:Landroidx/media3/exoplayer/s1;

    invoke-virtual {v0, p1}, Landroidx/media3/exoplayer/s1;->a1(I)V

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->l:Le2/n;

    new-instance v1, Landroidx/media3/exoplayer/m0;

    invoke-direct {v1, p1}, Landroidx/media3/exoplayer/m0;-><init>(I)V

    const/16 p1, 0x8

    invoke-virtual {v0, p1, v1}, Le2/n;->i(ILe2/n$a;)V

    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->y2()V

    iget-object p1, p0, Landroidx/media3/exoplayer/c1;->l:Le2/n;

    invoke-virtual {p1}, Le2/n;->f()V

    :cond_0
    return-void
.end method

.method public setShuffleModeEnabled(Z)V
    .locals 2

    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->F2()V

    iget-boolean v0, p0, Landroidx/media3/exoplayer/c1;->I:Z

    if-eq v0, p1, :cond_0

    iput-boolean p1, p0, Landroidx/media3/exoplayer/c1;->I:Z

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->k:Landroidx/media3/exoplayer/s1;

    invoke-virtual {v0, p1}, Landroidx/media3/exoplayer/s1;->d1(Z)V

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->l:Le2/n;

    new-instance v1, Landroidx/media3/exoplayer/p0;

    invoke-direct {v1, p1}, Landroidx/media3/exoplayer/p0;-><init>(Z)V

    const/16 p1, 0x9

    invoke-virtual {v0, p1, v1}, Le2/n;->i(ILe2/n$a;)V

    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->y2()V

    iget-object p1, p0, Landroidx/media3/exoplayer/c1;->l:Le2/n;

    invoke-virtual {p1}, Le2/n;->f()V

    :cond_0
    return-void
.end method

.method public setVideoSurfaceView(Landroid/view/SurfaceView;)V
    .locals 2
    .param p1    # Landroid/view/SurfaceView;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->F2()V

    instance-of v0, p1, Landroidx/media3/exoplayer/video/n;

    if-eqz v0, :cond_0

    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->n2()V

    invoke-virtual {p0, p1}, Landroidx/media3/exoplayer/c1;->v2(Ljava/lang/Object;)V

    invoke-virtual {p1}, Landroid/view/SurfaceView;->getHolder()Landroid/view/SurfaceHolder;

    move-result-object p1

    invoke-virtual {p0, p1}, Landroidx/media3/exoplayer/c1;->t2(Landroid/view/SurfaceHolder;)V

    goto :goto_1

    :cond_0
    instance-of v0, p1, Landroidx/media3/exoplayer/video/spherical/SphericalGLSurfaceView;

    if-eqz v0, :cond_1

    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->n2()V

    move-object v0, p1

    check-cast v0, Landroidx/media3/exoplayer/video/spherical/SphericalGLSurfaceView;

    iput-object v0, p0, Landroidx/media3/exoplayer/c1;->Z:Landroidx/media3/exoplayer/video/spherical/SphericalGLSurfaceView;

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->y:Landroidx/media3/exoplayer/c1$e;

    invoke-virtual {p0, v0}, Landroidx/media3/exoplayer/c1;->v1(Landroidx/media3/exoplayer/t2$b;)Landroidx/media3/exoplayer/t2;

    move-result-object v0

    const/16 v1, 0x2710

    invoke-virtual {v0, v1}, Landroidx/media3/exoplayer/t2;->n(I)Landroidx/media3/exoplayer/t2;

    move-result-object v0

    iget-object v1, p0, Landroidx/media3/exoplayer/c1;->Z:Landroidx/media3/exoplayer/video/spherical/SphericalGLSurfaceView;

    invoke-virtual {v0, v1}, Landroidx/media3/exoplayer/t2;->m(Ljava/lang/Object;)Landroidx/media3/exoplayer/t2;

    move-result-object v0

    invoke-virtual {v0}, Landroidx/media3/exoplayer/t2;->l()Landroidx/media3/exoplayer/t2;

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->Z:Landroidx/media3/exoplayer/video/spherical/SphericalGLSurfaceView;

    iget-object v1, p0, Landroidx/media3/exoplayer/c1;->x:Landroidx/media3/exoplayer/c1$d;

    invoke-virtual {v0, v1}, Landroidx/media3/exoplayer/video/spherical/SphericalGLSurfaceView;->addVideoSurfaceListener(Landroidx/media3/exoplayer/video/spherical/SphericalGLSurfaceView$b;)V

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->Z:Landroidx/media3/exoplayer/video/spherical/SphericalGLSurfaceView;

    invoke-virtual {v0}, Landroidx/media3/exoplayer/video/spherical/SphericalGLSurfaceView;->getVideoSurface()Landroid/view/Surface;

    move-result-object v0

    invoke-virtual {p0, v0}, Landroidx/media3/exoplayer/c1;->v2(Ljava/lang/Object;)V

    invoke-virtual {p1}, Landroid/view/SurfaceView;->getHolder()Landroid/view/SurfaceHolder;

    move-result-object p1

    invoke-virtual {p0, p1}, Landroidx/media3/exoplayer/c1;->t2(Landroid/view/SurfaceHolder;)V

    goto :goto_1

    :cond_1
    if-nez p1, :cond_2

    const/4 p1, 0x0

    goto :goto_0

    :cond_2
    invoke-virtual {p1}, Landroid/view/SurfaceView;->getHolder()Landroid/view/SurfaceHolder;

    move-result-object p1

    :goto_0
    invoke-virtual {p0, p1}, Landroidx/media3/exoplayer/c1;->w2(Landroid/view/SurfaceHolder;)V

    :goto_1
    return-void
.end method

.method public setVideoTextureView(Landroid/view/TextureView;)V
    .locals 2
    .param p1    # Landroid/view/TextureView;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->F2()V

    if-nez p1, :cond_0

    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->clearVideoSurface()V

    goto :goto_1

    :cond_0
    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->n2()V

    iput-object p1, p0, Landroidx/media3/exoplayer/c1;->b0:Landroid/view/TextureView;

    invoke-virtual {p1}, Landroid/view/TextureView;->getSurfaceTextureListener()Landroid/view/TextureView$SurfaceTextureListener;

    move-result-object v0

    if-eqz v0, :cond_1

    const-string v0, "ExoPlayerImpl"

    const-string v1, "Replacing existing SurfaceTextureListener."

    invoke-static {v0, v1}, Le2/o;->h(Ljava/lang/String;Ljava/lang/String;)V

    :cond_1
    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->x:Landroidx/media3/exoplayer/c1$d;

    invoke-virtual {p1, v0}, Landroid/view/TextureView;->setSurfaceTextureListener(Landroid/view/TextureView$SurfaceTextureListener;)V

    invoke-virtual {p1}, Landroid/view/TextureView;->isAvailable()Z

    move-result v0

    const/4 v1, 0x0

    if-eqz v0, :cond_2

    invoke-virtual {p1}, Landroid/view/TextureView;->getSurfaceTexture()Landroid/graphics/SurfaceTexture;

    move-result-object v0

    goto :goto_0

    :cond_2
    move-object v0, v1

    :goto_0
    if-nez v0, :cond_3

    invoke-virtual {p0, v1}, Landroidx/media3/exoplayer/c1;->v2(Ljava/lang/Object;)V

    const/4 p1, 0x0

    invoke-virtual {p0, p1, p1}, Landroidx/media3/exoplayer/c1;->j2(II)V

    goto :goto_1

    :cond_3
    invoke-virtual {p0, v0}, Landroidx/media3/exoplayer/c1;->u2(Landroid/graphics/SurfaceTexture;)V

    invoke-virtual {p1}, Landroid/view/View;->getWidth()I

    move-result v0

    invoke-virtual {p1}, Landroid/view/View;->getHeight()I

    move-result p1

    invoke-virtual {p0, v0, p1}, Landroidx/media3/exoplayer/c1;->j2(II)V

    :goto_1
    return-void
.end method

.method public setVolume(F)V
    .locals 2

    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->F2()V

    const/4 v0, 0x0

    const/high16 v1, 0x3f800000    # 1.0f

    invoke-static {p1, v0, v1}, Le2/u0;->o(FFF)F

    move-result p1

    iget v0, p0, Landroidx/media3/exoplayer/c1;->j0:F

    cmpl-float v0, v0, p1

    if-nez v0, :cond_0

    return-void

    :cond_0
    iput p1, p0, Landroidx/media3/exoplayer/c1;->j0:F

    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->p2()V

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->l:Le2/n;

    new-instance v1, Landroidx/media3/exoplayer/r0;

    invoke-direct {v1, p1}, Landroidx/media3/exoplayer/r0;-><init>(F)V

    const/16 p1, 0x16

    invoke-virtual {v0, p1, v1}, Le2/n;->l(ILe2/n$a;)V

    return-void
.end method

.method public stop()V
    .locals 4

    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->F2()V

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->A:Landroidx/media3/exoplayer/l;

    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->getPlayWhenReady()Z

    move-result v1

    const/4 v2, 0x1

    invoke-virtual {v0, v1, v2}, Landroidx/media3/exoplayer/l;->p(ZI)I

    const/4 v0, 0x0

    invoke-virtual {p0, v0}, Landroidx/media3/exoplayer/c1;->x2(Landroidx/media3/exoplayer/ExoPlaybackException;)V

    new-instance v0, Ld2/b;

    invoke-static {}, Lcom/google/common/collect/ImmutableList;->of()Lcom/google/common/collect/ImmutableList;

    move-result-object v1

    iget-object v2, p0, Landroidx/media3/exoplayer/c1;->u0:Landroidx/media3/exoplayer/s2;

    iget-wide v2, v2, Landroidx/media3/exoplayer/s2;->r:J

    invoke-direct {v0, v1, v2, v3}, Ld2/b;-><init>(Ljava/util/List;J)V

    iput-object v0, p0, Landroidx/media3/exoplayer/c1;->l0:Ld2/b;

    return-void
.end method

.method public final t1()Landroidx/media3/common/m0;
    .locals 3

    new-instance v0, Landroidx/media3/exoplayer/u2;

    iget-object v1, p0, Landroidx/media3/exoplayer/c1;->o:Ljava/util/List;

    iget-object v2, p0, Landroidx/media3/exoplayer/c1;->O:Lu2/f0;

    invoke-direct {v0, v1, v2}, Landroidx/media3/exoplayer/u2;-><init>(Ljava/util/Collection;Lu2/f0;)V

    return-object v0
.end method

.method public final t2(Landroid/view/SurfaceHolder;)V
    .locals 2

    const/4 v0, 0x0

    iput-boolean v0, p0, Landroidx/media3/exoplayer/c1;->a0:Z

    iput-object p1, p0, Landroidx/media3/exoplayer/c1;->Y:Landroid/view/SurfaceHolder;

    iget-object v1, p0, Landroidx/media3/exoplayer/c1;->x:Landroidx/media3/exoplayer/c1$d;

    invoke-interface {p1, v1}, Landroid/view/SurfaceHolder;->addCallback(Landroid/view/SurfaceHolder$Callback;)V

    iget-object p1, p0, Landroidx/media3/exoplayer/c1;->Y:Landroid/view/SurfaceHolder;

    invoke-interface {p1}, Landroid/view/SurfaceHolder;->getSurface()Landroid/view/Surface;

    move-result-object p1

    if-eqz p1, :cond_0

    invoke-virtual {p1}, Landroid/view/Surface;->isValid()Z

    move-result p1

    if-eqz p1, :cond_0

    iget-object p1, p0, Landroidx/media3/exoplayer/c1;->Y:Landroid/view/SurfaceHolder;

    invoke-interface {p1}, Landroid/view/SurfaceHolder;->getSurfaceFrame()Landroid/graphics/Rect;

    move-result-object p1

    invoke-virtual {p1}, Landroid/graphics/Rect;->width()I

    move-result v0

    invoke-virtual {p1}, Landroid/graphics/Rect;->height()I

    move-result p1

    invoke-virtual {p0, v0, p1}, Landroidx/media3/exoplayer/c1;->j2(II)V

    goto :goto_0

    :cond_0
    invoke-virtual {p0, v0, v0}, Landroidx/media3/exoplayer/c1;->j2(II)V

    :goto_0
    return-void
.end method

.method public u()I
    .locals 2

    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->F2()V

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->u0:Landroidx/media3/exoplayer/s2;

    invoke-virtual {p0, v0}, Landroidx/media3/exoplayer/c1;->z1(Landroidx/media3/exoplayer/s2;)I

    move-result v0

    const/4 v1, -0x1

    if-ne v0, v1, :cond_0

    const/4 v0, 0x0

    :cond_0
    return v0
.end method

.method public final u1(Ljava/util/List;)Ljava/util/List;
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Landroidx/media3/common/b0;",
            ">;)",
            "Ljava/util/List<",
            "Landroidx/media3/exoplayer/source/l;",
            ">;"
        }
    .end annotation

    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    const/4 v1, 0x0

    :goto_0
    invoke-interface {p1}, Ljava/util/List;->size()I

    move-result v2

    if-ge v1, v2, :cond_0

    iget-object v2, p0, Landroidx/media3/exoplayer/c1;->q:Landroidx/media3/exoplayer/source/l$a;

    invoke-interface {p1, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Landroidx/media3/common/b0;

    invoke-interface {v2, v3}, Landroidx/media3/exoplayer/source/l$a;->c(Landroidx/media3/common/b0;)Landroidx/media3/exoplayer/source/l;

    move-result-object v2

    invoke-interface {v0, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_0
    return-object v0
.end method

.method public final u2(Landroid/graphics/SurfaceTexture;)V
    .locals 1

    new-instance v0, Landroid/view/Surface;

    invoke-direct {v0, p1}, Landroid/view/Surface;-><init>(Landroid/graphics/SurfaceTexture;)V

    invoke-virtual {p0, v0}, Landroidx/media3/exoplayer/c1;->v2(Ljava/lang/Object;)V

    iput-object v0, p0, Landroidx/media3/exoplayer/c1;->X:Landroid/view/Surface;

    return-void
.end method

.method public v()J
    .locals 6

    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->F2()V

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->u0:Landroidx/media3/exoplayer/s2;

    iget-object v0, v0, Landroidx/media3/exoplayer/s2;->a:Landroidx/media3/common/m0;

    invoke-virtual {v0}, Landroidx/media3/common/m0;->q()Z

    move-result v0

    if-eqz v0, :cond_0

    iget-wide v0, p0, Landroidx/media3/exoplayer/c1;->x0:J

    return-wide v0

    :cond_0
    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->u0:Landroidx/media3/exoplayer/s2;

    iget-object v1, v0, Landroidx/media3/exoplayer/s2;->k:Landroidx/media3/exoplayer/source/l$b;

    iget-wide v1, v1, Landroidx/media3/exoplayer/source/l$b;->d:J

    iget-object v3, v0, Landroidx/media3/exoplayer/s2;->b:Landroidx/media3/exoplayer/source/l$b;

    iget-wide v3, v3, Landroidx/media3/exoplayer/source/l$b;->d:J

    cmp-long v5, v1, v3

    if-eqz v5, :cond_1

    iget-object v0, v0, Landroidx/media3/exoplayer/s2;->a:Landroidx/media3/common/m0;

    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->u()I

    move-result v1

    iget-object v2, p0, Landroidx/media3/common/h;->a:Landroidx/media3/common/m0$c;

    invoke-virtual {v0, v1, v2}, Landroidx/media3/common/m0;->n(ILandroidx/media3/common/m0$c;)Landroidx/media3/common/m0$c;

    move-result-object v0

    invoke-virtual {v0}, Landroidx/media3/common/m0$c;->d()J

    move-result-wide v0

    return-wide v0

    :cond_1
    iget-wide v0, v0, Landroidx/media3/exoplayer/s2;->p:J

    iget-object v2, p0, Landroidx/media3/exoplayer/c1;->u0:Landroidx/media3/exoplayer/s2;

    iget-object v2, v2, Landroidx/media3/exoplayer/s2;->k:Landroidx/media3/exoplayer/source/l$b;

    invoke-virtual {v2}, Landroidx/media3/exoplayer/source/l$b;->b()Z

    move-result v2

    if-eqz v2, :cond_3

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->u0:Landroidx/media3/exoplayer/s2;

    iget-object v1, v0, Landroidx/media3/exoplayer/s2;->a:Landroidx/media3/common/m0;

    iget-object v0, v0, Landroidx/media3/exoplayer/s2;->k:Landroidx/media3/exoplayer/source/l$b;

    iget-object v0, v0, Landroidx/media3/exoplayer/source/l$b;->a:Ljava/lang/Object;

    iget-object v2, p0, Landroidx/media3/exoplayer/c1;->n:Landroidx/media3/common/m0$b;

    invoke-virtual {v1, v0, v2}, Landroidx/media3/common/m0;->h(Ljava/lang/Object;Landroidx/media3/common/m0$b;)Landroidx/media3/common/m0$b;

    move-result-object v0

    iget-object v1, p0, Landroidx/media3/exoplayer/c1;->u0:Landroidx/media3/exoplayer/s2;

    iget-object v1, v1, Landroidx/media3/exoplayer/s2;->k:Landroidx/media3/exoplayer/source/l$b;

    iget v1, v1, Landroidx/media3/exoplayer/source/l$b;->b:I

    invoke-virtual {v0, v1}, Landroidx/media3/common/m0$b;->f(I)J

    move-result-wide v1

    const-wide/high16 v3, -0x8000000000000000L

    cmp-long v5, v1, v3

    if-nez v5, :cond_2

    iget-wide v0, v0, Landroidx/media3/common/m0$b;->d:J

    goto :goto_0

    :cond_2
    move-wide v0, v1

    :cond_3
    :goto_0
    iget-object v2, p0, Landroidx/media3/exoplayer/c1;->u0:Landroidx/media3/exoplayer/s2;

    iget-object v3, v2, Landroidx/media3/exoplayer/s2;->a:Landroidx/media3/common/m0;

    iget-object v2, v2, Landroidx/media3/exoplayer/s2;->k:Landroidx/media3/exoplayer/source/l$b;

    invoke-virtual {p0, v3, v2, v0, v1}, Landroidx/media3/exoplayer/c1;->k2(Landroidx/media3/common/m0;Landroidx/media3/exoplayer/source/l$b;J)J

    move-result-wide v0

    invoke-static {v0, v1}, Le2/u0;->B1(J)J

    move-result-wide v0

    return-wide v0
.end method

.method public final v1(Landroidx/media3/exoplayer/t2$b;)Landroidx/media3/exoplayer/t2;
    .locals 9

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->u0:Landroidx/media3/exoplayer/s2;

    invoke-virtual {p0, v0}, Landroidx/media3/exoplayer/c1;->z1(Landroidx/media3/exoplayer/s2;)I

    move-result v0

    new-instance v8, Landroidx/media3/exoplayer/t2;

    iget-object v2, p0, Landroidx/media3/exoplayer/c1;->k:Landroidx/media3/exoplayer/s1;

    iget-object v1, p0, Landroidx/media3/exoplayer/c1;->u0:Landroidx/media3/exoplayer/s2;

    iget-object v4, v1, Landroidx/media3/exoplayer/s2;->a:Landroidx/media3/common/m0;

    const/4 v1, -0x1

    if-ne v0, v1, :cond_0

    const/4 v0, 0x0

    const/4 v5, 0x0

    goto :goto_0

    :cond_0
    move v5, v0

    :goto_0
    iget-object v6, p0, Landroidx/media3/exoplayer/c1;->w:Le2/d;

    invoke-virtual {v2}, Landroidx/media3/exoplayer/s1;->E()Landroid/os/Looper;

    move-result-object v7

    move-object v1, v8

    move-object v3, p1

    invoke-direct/range {v1 .. v7}, Landroidx/media3/exoplayer/t2;-><init>(Landroidx/media3/exoplayer/t2$a;Landroidx/media3/exoplayer/t2$b;Landroidx/media3/common/m0;ILe2/d;Landroid/os/Looper;)V

    return-object v8
.end method

.method public final v2(Ljava/lang/Object;)V
    .locals 9
    .param p1    # Ljava/lang/Object;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iget-object v1, p0, Landroidx/media3/exoplayer/c1;->g:[Landroidx/media3/exoplayer/w2;

    array-length v2, v1

    const/4 v3, 0x0

    const/4 v4, 0x0

    :goto_0
    const/4 v5, 0x1

    if-ge v4, v2, :cond_1

    aget-object v6, v1, v4

    invoke-interface {v6}, Landroidx/media3/exoplayer/w2;->getTrackType()I

    move-result v7

    const/4 v8, 0x2

    if-ne v7, v8, :cond_0

    invoke-virtual {p0, v6}, Landroidx/media3/exoplayer/c1;->v1(Landroidx/media3/exoplayer/t2$b;)Landroidx/media3/exoplayer/t2;

    move-result-object v6

    invoke-virtual {v6, v5}, Landroidx/media3/exoplayer/t2;->n(I)Landroidx/media3/exoplayer/t2;

    move-result-object v5

    invoke-virtual {v5, p1}, Landroidx/media3/exoplayer/t2;->m(Ljava/lang/Object;)Landroidx/media3/exoplayer/t2;

    move-result-object v5

    invoke-virtual {v5}, Landroidx/media3/exoplayer/t2;->l()Landroidx/media3/exoplayer/t2;

    move-result-object v5

    invoke-interface {v0, v5}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    :cond_0
    add-int/lit8 v4, v4, 0x1

    goto :goto_0

    :cond_1
    iget-object v1, p0, Landroidx/media3/exoplayer/c1;->W:Ljava/lang/Object;

    if-eqz v1, :cond_3

    if-eq v1, p1, :cond_3

    :try_start_0
    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_1
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_2

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Landroidx/media3/exoplayer/t2;

    iget-wide v6, p0, Landroidx/media3/exoplayer/c1;->E:J

    invoke-virtual {v1, v6, v7}, Landroidx/media3/exoplayer/t2;->a(J)Z
    :try_end_0
    .catch Ljava/lang/InterruptedException; {:try_start_0 .. :try_end_0} :catch_1
    .catch Ljava/util/concurrent/TimeoutException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_1

    :catch_0
    nop

    const/4 v3, 0x1

    goto :goto_2

    :catch_1
    invoke-static {}, Ljava/lang/Thread;->currentThread()Ljava/lang/Thread;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/Thread;->interrupt()V

    :cond_2
    :goto_2
    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->W:Ljava/lang/Object;

    iget-object v1, p0, Landroidx/media3/exoplayer/c1;->X:Landroid/view/Surface;

    if-ne v0, v1, :cond_3

    invoke-virtual {v1}, Landroid/view/Surface;->release()V

    const/4 v0, 0x0

    iput-object v0, p0, Landroidx/media3/exoplayer/c1;->X:Landroid/view/Surface;

    :cond_3
    iput-object p1, p0, Landroidx/media3/exoplayer/c1;->W:Ljava/lang/Object;

    if-eqz v3, :cond_4

    new-instance p1, Landroidx/media3/exoplayer/ExoTimeoutException;

    const/4 v0, 0x3

    invoke-direct {p1, v0}, Landroidx/media3/exoplayer/ExoTimeoutException;-><init>(I)V

    const/16 v0, 0x3eb

    invoke-static {p1, v0}, Landroidx/media3/exoplayer/ExoPlaybackException;->createForUnexpected(Ljava/lang/RuntimeException;I)Landroidx/media3/exoplayer/ExoPlaybackException;

    move-result-object p1

    invoke-virtual {p0, p1}, Landroidx/media3/exoplayer/c1;->x2(Landroidx/media3/exoplayer/ExoPlaybackException;)V

    :cond_4
    return-void
.end method

.method public final w1(Landroidx/media3/exoplayer/s2;Landroidx/media3/exoplayer/s2;ZIZZ)Landroid/util/Pair;
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroidx/media3/exoplayer/s2;",
            "Landroidx/media3/exoplayer/s2;",
            "ZIZZ)",
            "Landroid/util/Pair<",
            "Ljava/lang/Boolean;",
            "Ljava/lang/Integer;",
            ">;"
        }
    .end annotation

    iget-object v0, p2, Landroidx/media3/exoplayer/s2;->a:Landroidx/media3/common/m0;

    iget-object v1, p1, Landroidx/media3/exoplayer/s2;->a:Landroidx/media3/common/m0;

    invoke-virtual {v1}, Landroidx/media3/common/m0;->q()Z

    move-result v2

    const/4 v3, -0x1

    invoke-static {v3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v3

    if-eqz v2, :cond_0

    invoke-virtual {v0}, Landroidx/media3/common/m0;->q()Z

    move-result v2

    if-eqz v2, :cond_0

    new-instance p1, Landroid/util/Pair;

    sget-object p2, Ljava/lang/Boolean;->FALSE:Ljava/lang/Boolean;

    invoke-direct {p1, p2, v3}, Landroid/util/Pair;-><init>(Ljava/lang/Object;Ljava/lang/Object;)V

    return-object p1

    :cond_0
    invoke-virtual {v1}, Landroidx/media3/common/m0;->q()Z

    move-result v2

    invoke-virtual {v0}, Landroidx/media3/common/m0;->q()Z

    move-result v4

    const/4 v5, 0x3

    if-eq v2, v4, :cond_1

    new-instance p1, Landroid/util/Pair;

    sget-object p2, Ljava/lang/Boolean;->TRUE:Ljava/lang/Boolean;

    invoke-static {v5}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p3

    invoke-direct {p1, p2, p3}, Landroid/util/Pair;-><init>(Ljava/lang/Object;Ljava/lang/Object;)V

    return-object p1

    :cond_1
    iget-object v2, p2, Landroidx/media3/exoplayer/s2;->b:Landroidx/media3/exoplayer/source/l$b;

    iget-object v2, v2, Landroidx/media3/exoplayer/source/l$b;->a:Ljava/lang/Object;

    iget-object v4, p0, Landroidx/media3/exoplayer/c1;->n:Landroidx/media3/common/m0$b;

    invoke-virtual {v0, v2, v4}, Landroidx/media3/common/m0;->h(Ljava/lang/Object;Landroidx/media3/common/m0$b;)Landroidx/media3/common/m0$b;

    move-result-object v2

    iget v2, v2, Landroidx/media3/common/m0$b;->c:I

    iget-object v4, p0, Landroidx/media3/common/h;->a:Landroidx/media3/common/m0$c;

    invoke-virtual {v0, v2, v4}, Landroidx/media3/common/m0;->n(ILandroidx/media3/common/m0$c;)Landroidx/media3/common/m0$c;

    move-result-object v0

    iget-object v0, v0, Landroidx/media3/common/m0$c;->a:Ljava/lang/Object;

    iget-object v2, p1, Landroidx/media3/exoplayer/s2;->b:Landroidx/media3/exoplayer/source/l$b;

    iget-object v2, v2, Landroidx/media3/exoplayer/source/l$b;->a:Ljava/lang/Object;

    iget-object v4, p0, Landroidx/media3/exoplayer/c1;->n:Landroidx/media3/common/m0$b;

    invoke-virtual {v1, v2, v4}, Landroidx/media3/common/m0;->h(Ljava/lang/Object;Landroidx/media3/common/m0$b;)Landroidx/media3/common/m0$b;

    move-result-object v2

    iget v2, v2, Landroidx/media3/common/m0$b;->c:I

    iget-object v4, p0, Landroidx/media3/common/h;->a:Landroidx/media3/common/m0$c;

    invoke-virtual {v1, v2, v4}, Landroidx/media3/common/m0;->n(ILandroidx/media3/common/m0$c;)Landroidx/media3/common/m0$c;

    move-result-object v1

    iget-object v1, v1, Landroidx/media3/common/m0$c;->a:Ljava/lang/Object;

    invoke-virtual {v0, v1}, Ljava/lang/Object;->equals(Ljava/lang/Object;)Z

    move-result v0

    const/4 v1, 0x2

    const/4 v2, 0x1

    if-nez v0, :cond_5

    if-eqz p3, :cond_2

    if-nez p4, :cond_2

    const/4 v5, 0x1

    goto :goto_0

    :cond_2
    if-eqz p3, :cond_3

    if-ne p4, v2, :cond_3

    const/4 v5, 0x2

    goto :goto_0

    :cond_3
    if-eqz p5, :cond_4

    :goto_0
    new-instance p1, Landroid/util/Pair;

    sget-object p2, Ljava/lang/Boolean;->TRUE:Ljava/lang/Boolean;

    invoke-static {v5}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p3

    invoke-direct {p1, p2, p3}, Landroid/util/Pair;-><init>(Ljava/lang/Object;Ljava/lang/Object;)V

    return-object p1

    :cond_4
    new-instance p1, Ljava/lang/IllegalStateException;

    invoke-direct {p1}, Ljava/lang/IllegalStateException;-><init>()V

    throw p1

    :cond_5
    if-eqz p3, :cond_6

    if-nez p4, :cond_6

    iget-object p2, p2, Landroidx/media3/exoplayer/s2;->b:Landroidx/media3/exoplayer/source/l$b;

    iget-wide v4, p2, Landroidx/media3/exoplayer/source/l$b;->d:J

    iget-object p1, p1, Landroidx/media3/exoplayer/s2;->b:Landroidx/media3/exoplayer/source/l$b;

    iget-wide p1, p1, Landroidx/media3/exoplayer/source/l$b;->d:J

    cmp-long p5, v4, p1

    if-gez p5, :cond_6

    new-instance p1, Landroid/util/Pair;

    sget-object p2, Ljava/lang/Boolean;->TRUE:Ljava/lang/Boolean;

    const/4 p3, 0x0

    invoke-static {p3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p3

    invoke-direct {p1, p2, p3}, Landroid/util/Pair;-><init>(Ljava/lang/Object;Ljava/lang/Object;)V

    return-object p1

    :cond_6
    if-eqz p3, :cond_7

    if-ne p4, v2, :cond_7

    if-eqz p6, :cond_7

    new-instance p1, Landroid/util/Pair;

    sget-object p2, Ljava/lang/Boolean;->TRUE:Ljava/lang/Boolean;

    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p3

    invoke-direct {p1, p2, p3}, Landroid/util/Pair;-><init>(Ljava/lang/Object;Ljava/lang/Object;)V

    return-object p1

    :cond_7
    new-instance p1, Landroid/util/Pair;

    sget-object p2, Ljava/lang/Boolean;->FALSE:Ljava/lang/Boolean;

    invoke-direct {p1, p2, v3}, Landroid/util/Pair;-><init>(Ljava/lang/Object;Ljava/lang/Object;)V

    return-object p1
.end method

.method public w2(Landroid/view/SurfaceHolder;)V
    .locals 2
    .param p1    # Landroid/view/SurfaceHolder;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->F2()V

    if-nez p1, :cond_0

    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->clearVideoSurface()V

    goto :goto_0

    :cond_0
    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->n2()V

    const/4 v0, 0x1

    iput-boolean v0, p0, Landroidx/media3/exoplayer/c1;->a0:Z

    iput-object p1, p0, Landroidx/media3/exoplayer/c1;->Y:Landroid/view/SurfaceHolder;

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->x:Landroidx/media3/exoplayer/c1$d;

    invoke-interface {p1, v0}, Landroid/view/SurfaceHolder;->addCallback(Landroid/view/SurfaceHolder$Callback;)V

    invoke-interface {p1}, Landroid/view/SurfaceHolder;->getSurface()Landroid/view/Surface;

    move-result-object v0

    if-eqz v0, :cond_1

    invoke-virtual {v0}, Landroid/view/Surface;->isValid()Z

    move-result v1

    if-eqz v1, :cond_1

    invoke-virtual {p0, v0}, Landroidx/media3/exoplayer/c1;->v2(Ljava/lang/Object;)V

    invoke-interface {p1}, Landroid/view/SurfaceHolder;->getSurfaceFrame()Landroid/graphics/Rect;

    move-result-object p1

    invoke-virtual {p1}, Landroid/graphics/Rect;->width()I

    move-result v0

    invoke-virtual {p1}, Landroid/graphics/Rect;->height()I

    move-result p1

    invoke-virtual {p0, v0, p1}, Landroidx/media3/exoplayer/c1;->j2(II)V

    goto :goto_0

    :cond_1
    const/4 p1, 0x0

    invoke-virtual {p0, p1}, Landroidx/media3/exoplayer/c1;->v2(Ljava/lang/Object;)V

    const/4 p1, 0x0

    invoke-virtual {p0, p1, p1}, Landroidx/media3/exoplayer/c1;->j2(II)V

    :goto_0
    return-void
.end method

.method public final x1(Landroidx/media3/exoplayer/s2;)J
    .locals 5

    iget-object v0, p1, Landroidx/media3/exoplayer/s2;->b:Landroidx/media3/exoplayer/source/l$b;

    invoke-virtual {v0}, Landroidx/media3/exoplayer/source/l$b;->b()Z

    move-result v0

    if-eqz v0, :cond_1

    iget-object v0, p1, Landroidx/media3/exoplayer/s2;->a:Landroidx/media3/common/m0;

    iget-object v1, p1, Landroidx/media3/exoplayer/s2;->b:Landroidx/media3/exoplayer/source/l$b;

    iget-object v1, v1, Landroidx/media3/exoplayer/source/l$b;->a:Ljava/lang/Object;

    iget-object v2, p0, Landroidx/media3/exoplayer/c1;->n:Landroidx/media3/common/m0$b;

    invoke-virtual {v0, v1, v2}, Landroidx/media3/common/m0;->h(Ljava/lang/Object;Landroidx/media3/common/m0$b;)Landroidx/media3/common/m0$b;

    iget-wide v0, p1, Landroidx/media3/exoplayer/s2;->c:J

    const-wide v2, -0x7fffffffffffffffL    # -4.9E-324

    cmp-long v4, v0, v2

    if-nez v4, :cond_0

    iget-object v0, p1, Landroidx/media3/exoplayer/s2;->a:Landroidx/media3/common/m0;

    invoke-virtual {p0, p1}, Landroidx/media3/exoplayer/c1;->z1(Landroidx/media3/exoplayer/s2;)I

    move-result p1

    iget-object v1, p0, Landroidx/media3/common/h;->a:Landroidx/media3/common/m0$c;

    invoke-virtual {v0, p1, v1}, Landroidx/media3/common/m0;->n(ILandroidx/media3/common/m0$c;)Landroidx/media3/common/m0$c;

    move-result-object p1

    invoke-virtual {p1}, Landroidx/media3/common/m0$c;->b()J

    move-result-wide v0

    goto :goto_0

    :cond_0
    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->n:Landroidx/media3/common/m0$b;

    invoke-virtual {v0}, Landroidx/media3/common/m0$b;->n()J

    move-result-wide v0

    iget-wide v2, p1, Landroidx/media3/exoplayer/s2;->c:J

    invoke-static {v2, v3}, Le2/u0;->B1(J)J

    move-result-wide v2

    add-long/2addr v0, v2

    :goto_0
    return-wide v0

    :cond_1
    invoke-virtual {p0, p1}, Landroidx/media3/exoplayer/c1;->y1(Landroidx/media3/exoplayer/s2;)J

    move-result-wide v0

    invoke-static {v0, v1}, Le2/u0;->B1(J)J

    move-result-wide v0

    return-wide v0
.end method

.method public final x2(Landroidx/media3/exoplayer/ExoPlaybackException;)V
    .locals 12
    .param p1    # Landroidx/media3/exoplayer/ExoPlaybackException;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->u0:Landroidx/media3/exoplayer/s2;

    iget-object v1, v0, Landroidx/media3/exoplayer/s2;->b:Landroidx/media3/exoplayer/source/l$b;

    invoke-virtual {v0, v1}, Landroidx/media3/exoplayer/s2;->c(Landroidx/media3/exoplayer/source/l$b;)Landroidx/media3/exoplayer/s2;

    move-result-object v0

    iget-wide v1, v0, Landroidx/media3/exoplayer/s2;->r:J

    iput-wide v1, v0, Landroidx/media3/exoplayer/s2;->p:J

    const-wide/16 v1, 0x0

    iput-wide v1, v0, Landroidx/media3/exoplayer/s2;->q:J

    const/4 v1, 0x1

    invoke-virtual {v0, v1}, Landroidx/media3/exoplayer/s2;->h(I)Landroidx/media3/exoplayer/s2;

    move-result-object v0

    if-eqz p1, :cond_0

    invoke-virtual {v0, p1}, Landroidx/media3/exoplayer/s2;->f(Landroidx/media3/exoplayer/ExoPlaybackException;)Landroidx/media3/exoplayer/s2;

    move-result-object v0

    :cond_0
    move-object v3, v0

    iget p1, p0, Landroidx/media3/exoplayer/c1;->J:I

    add-int/2addr p1, v1

    iput p1, p0, Landroidx/media3/exoplayer/c1;->J:I

    iget-object p1, p0, Landroidx/media3/exoplayer/c1;->k:Landroidx/media3/exoplayer/s1;

    invoke-virtual {p1}, Landroidx/media3/exoplayer/s1;->n1()V

    const/4 v4, 0x0

    const/4 v5, 0x1

    const/4 v6, 0x0

    const/4 v7, 0x5

    const-wide v8, -0x7fffffffffffffffL    # -4.9E-324

    const/4 v10, -0x1

    const/4 v11, 0x0

    move-object v2, p0

    invoke-virtual/range {v2 .. v11}, Landroidx/media3/exoplayer/c1;->B2(Landroidx/media3/exoplayer/s2;IIZIJIZ)V

    return-void
.end method

.method public y()Landroidx/media3/common/d0;
    .locals 1

    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->F2()V

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->R:Landroidx/media3/common/d0;

    return-object v0
.end method

.method public final y1(Landroidx/media3/exoplayer/s2;)J
    .locals 3

    iget-object v0, p1, Landroidx/media3/exoplayer/s2;->a:Landroidx/media3/common/m0;

    invoke-virtual {v0}, Landroidx/media3/common/m0;->q()Z

    move-result v0

    if-eqz v0, :cond_0

    iget-wide v0, p0, Landroidx/media3/exoplayer/c1;->x0:J

    invoke-static {v0, v1}, Le2/u0;->S0(J)J

    move-result-wide v0

    return-wide v0

    :cond_0
    iget-boolean v0, p1, Landroidx/media3/exoplayer/s2;->o:Z

    if-eqz v0, :cond_1

    invoke-virtual {p1}, Landroidx/media3/exoplayer/s2;->m()J

    move-result-wide v0

    goto :goto_0

    :cond_1
    iget-wide v0, p1, Landroidx/media3/exoplayer/s2;->r:J

    :goto_0
    iget-object v2, p1, Landroidx/media3/exoplayer/s2;->b:Landroidx/media3/exoplayer/source/l$b;

    invoke-virtual {v2}, Landroidx/media3/exoplayer/source/l$b;->b()Z

    move-result v2

    if-eqz v2, :cond_2

    return-wide v0

    :cond_2
    iget-object v2, p1, Landroidx/media3/exoplayer/s2;->a:Landroidx/media3/common/m0;

    iget-object p1, p1, Landroidx/media3/exoplayer/s2;->b:Landroidx/media3/exoplayer/source/l$b;

    invoke-virtual {p0, v2, p1, v0, v1}, Landroidx/media3/exoplayer/c1;->k2(Landroidx/media3/common/m0;Landroidx/media3/exoplayer/source/l$b;J)J

    move-result-wide v0

    return-wide v0
.end method

.method public final y2()V
    .locals 3

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->Q:Landroidx/media3/common/h0$b;

    iget-object v1, p0, Landroidx/media3/exoplayer/c1;->f:Landroidx/media3/common/h0;

    iget-object v2, p0, Landroidx/media3/exoplayer/c1;->c:Landroidx/media3/common/h0$b;

    invoke-static {v1, v2}, Le2/u0;->N(Landroidx/media3/common/h0;Landroidx/media3/common/h0$b;)Landroidx/media3/common/h0$b;

    move-result-object v1

    iput-object v1, p0, Landroidx/media3/exoplayer/c1;->Q:Landroidx/media3/common/h0$b;

    invoke-virtual {v1, v0}, Landroidx/media3/common/h0$b;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_0

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->l:Le2/n;

    new-instance v1, Landroidx/media3/exoplayer/s0;

    invoke-direct {v1, p0}, Landroidx/media3/exoplayer/s0;-><init>(Landroidx/media3/exoplayer/c1;)V

    const/16 v2, 0xd

    invoke-virtual {v0, v2, v1}, Le2/n;->i(ILe2/n$a;)V

    :cond_0
    return-void
.end method

.method public z()J
    .locals 2

    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->F2()V

    iget-wide v0, p0, Landroidx/media3/exoplayer/c1;->u:J

    return-wide v0
.end method

.method public final z1(Landroidx/media3/exoplayer/s2;)I
    .locals 2

    iget-object v0, p1, Landroidx/media3/exoplayer/s2;->a:Landroidx/media3/common/m0;

    invoke-virtual {v0}, Landroidx/media3/common/m0;->q()Z

    move-result v0

    if-eqz v0, :cond_0

    iget p1, p0, Landroidx/media3/exoplayer/c1;->v0:I

    return p1

    :cond_0
    iget-object v0, p1, Landroidx/media3/exoplayer/s2;->a:Landroidx/media3/common/m0;

    iget-object p1, p1, Landroidx/media3/exoplayer/s2;->b:Landroidx/media3/exoplayer/source/l$b;

    iget-object p1, p1, Landroidx/media3/exoplayer/source/l$b;->a:Ljava/lang/Object;

    iget-object v1, p0, Landroidx/media3/exoplayer/c1;->n:Landroidx/media3/common/m0$b;

    invoke-virtual {v0, p1, v1}, Landroidx/media3/common/m0;->h(Ljava/lang/Object;Landroidx/media3/common/m0$b;)Landroidx/media3/common/m0$b;

    move-result-object p1

    iget p1, p1, Landroidx/media3/common/m0$b;->c:I

    return p1
.end method

.method public final z2(IILjava/util/List;)V
    .locals 10
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(II",
            "Ljava/util/List<",
            "Landroidx/media3/common/b0;",
            ">;)V"
        }
    .end annotation

    iget v0, p0, Landroidx/media3/exoplayer/c1;->J:I

    add-int/lit8 v0, v0, 0x1

    iput v0, p0, Landroidx/media3/exoplayer/c1;->J:I

    iget-object v0, p0, Landroidx/media3/exoplayer/c1;->k:Landroidx/media3/exoplayer/s1;

    invoke-virtual {v0, p1, p2, p3}, Landroidx/media3/exoplayer/s1;->s1(IILjava/util/List;)V

    move v0, p1

    :goto_0
    if-ge v0, p2, :cond_0

    iget-object v1, p0, Landroidx/media3/exoplayer/c1;->o:Ljava/util/List;

    invoke-interface {v1, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Landroidx/media3/exoplayer/c1$f;

    new-instance v2, Lu2/i0;

    invoke-virtual {v1}, Landroidx/media3/exoplayer/c1$f;->b()Landroidx/media3/common/m0;

    move-result-object v3

    sub-int v4, v0, p1

    invoke-interface {p3, v4}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Landroidx/media3/common/b0;

    invoke-direct {v2, v3, v4}, Lu2/i0;-><init>(Landroidx/media3/common/m0;Landroidx/media3/common/b0;)V

    invoke-virtual {v1, v2}, Landroidx/media3/exoplayer/c1$f;->d(Landroidx/media3/common/m0;)V

    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :cond_0
    invoke-virtual {p0}, Landroidx/media3/exoplayer/c1;->t1()Landroidx/media3/common/m0;

    move-result-object p1

    iget-object p2, p0, Landroidx/media3/exoplayer/c1;->u0:Landroidx/media3/exoplayer/s2;

    invoke-virtual {p2, p1}, Landroidx/media3/exoplayer/s2;->j(Landroidx/media3/common/m0;)Landroidx/media3/exoplayer/s2;

    move-result-object v1

    const/4 v2, 0x0

    const/4 v3, 0x1

    const/4 v4, 0x0

    const/4 v5, 0x4

    const-wide v6, -0x7fffffffffffffffL    # -4.9E-324

    const/4 v8, -0x1

    const/4 v9, 0x0

    move-object v0, p0

    invoke-virtual/range {v0 .. v9}, Landroidx/media3/exoplayer/c1;->B2(Landroidx/media3/exoplayer/s2;IIZIJIZ)V

    return-void
.end method
