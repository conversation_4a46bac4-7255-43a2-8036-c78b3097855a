.class public interface abstract Landroidx/media3/exoplayer/drm/c$b;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/media3/exoplayer/drm/c;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "b"
.end annotation


# static fields
.field public static final a:Landroidx/media3/exoplayer/drm/c$b;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    new-instance v0, Ln2/s;

    invoke-direct {v0}, Ln2/s;-><init>()V

    sput-object v0, Landroidx/media3/exoplayer/drm/c$b;->a:Landroidx/media3/exoplayer/drm/c$b;

    return-void
.end method


# virtual methods
.method public abstract release()V
.end method
