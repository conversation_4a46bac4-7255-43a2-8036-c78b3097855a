.class public final synthetic Landroidx/media3/exoplayer/e2;
.super Ljava/lang/Object;

# interfaces
.implements Landroidx/media3/exoplayer/source/l$c;


# instance fields
.field public final synthetic a:Landroidx/media3/exoplayer/r2;


# direct methods
.method public synthetic constructor <init>(Landroidx/media3/exoplayer/r2;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Landroidx/media3/exoplayer/e2;->a:Landroidx/media3/exoplayer/r2;

    return-void
.end method


# virtual methods
.method public final a(Landroidx/media3/exoplayer/source/l;Landroidx/media3/common/m0;)V
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/e2;->a:Landroidx/media3/exoplayer/r2;

    invoke-static {v0, p1, p2}, Landroidx/media3/exoplayer/r2;->a(Landroidx/media3/exoplayer/r2;Landroidx/media3/exoplayer/source/l;Landroidx/media3/common/m0;)V

    return-void
.end method
