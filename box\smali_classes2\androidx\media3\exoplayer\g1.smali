.class public final synthetic Landroidx/media3/exoplayer/g1;
.super Ljava/lang/Object;

# interfaces
.implements Le2/n$a;


# instance fields
.field public final synthetic a:Ljava/util/List;


# direct methods
.method public synthetic constructor <init>(Ljava/util/List;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Landroidx/media3/exoplayer/g1;->a:Ljava/util/List;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)V
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/g1;->a:Ljava/util/List;

    check-cast p1, Landroidx/media3/common/h0$d;

    invoke-static {v0, p1}, Landroidx/media3/exoplayer/c1$d;->J(Ljava/util/List;Landroidx/media3/common/h0$d;)V

    return-void
.end method
