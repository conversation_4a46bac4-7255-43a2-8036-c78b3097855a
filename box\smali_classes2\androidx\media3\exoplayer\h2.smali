.class public final synthetic Landroidx/media3/exoplayer/h2;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/lang/Runnable;


# instance fields
.field public final synthetic a:Landroidx/media3/exoplayer/r2$a;

.field public final synthetic b:Landroid/util/Pair;


# direct methods
.method public synthetic constructor <init>(Landroidx/media3/exoplayer/r2$a;Landroid/util/Pair;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Landroidx/media3/exoplayer/h2;->a:Landroidx/media3/exoplayer/r2$a;

    iput-object p2, p0, Landroidx/media3/exoplayer/h2;->b:Landroid/util/Pair;

    return-void
.end method


# virtual methods
.method public final run()V
    .locals 2

    iget-object v0, p0, Landroidx/media3/exoplayer/h2;->a:Landroidx/media3/exoplayer/r2$a;

    iget-object v1, p0, Landroidx/media3/exoplayer/h2;->b:Landroid/util/Pair;

    invoke-static {v0, v1}, Landroidx/media3/exoplayer/r2$a;->L(Landroidx/media3/exoplayer/r2$a;Landroid/util/Pair;)V

    return-void
.end method
