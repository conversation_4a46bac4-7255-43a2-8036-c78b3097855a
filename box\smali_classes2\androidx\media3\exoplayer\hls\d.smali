.class public final Landroidx/media3/exoplayer/hls/d;
.super Ljava/lang/Object;

# interfaces
.implements Landroidx/media3/exoplayer/hls/g;


# static fields
.field public static final f:[I


# instance fields
.field public final b:I

.field public c:Lt3/s$a;

.field public d:Z

.field public final e:Z


# direct methods
.method static constructor <clinit>()V
    .locals 1

    const/4 v0, 0x7

    new-array v0, v0, [I

    fill-array-data v0, :array_0

    sput-object v0, Landroidx/media3/exoplayer/hls/d;->f:[I

    return-void

    nop

    :array_0
    .array-data 4
        0x8
        0xd
        0xb
        0x2
        0x0
        0x1
        0x7
    .end array-data
.end method

.method public constructor <init>()V
    .locals 2

    const/4 v0, 0x0

    const/4 v1, 0x1

    invoke-direct {p0, v0, v1}, Landroidx/media3/exoplayer/hls/d;-><init>(IZ)V

    return-void
.end method

.method public constructor <init>(IZ)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput p1, p0, Landroidx/media3/exoplayer/hls/d;->b:I

    iput-boolean p2, p0, Landroidx/media3/exoplayer/hls/d;->e:Z

    new-instance p1, Lt3/h;

    invoke-direct {p1}, Lt3/h;-><init>()V

    iput-object p1, p0, Landroidx/media3/exoplayer/hls/d;->c:Lt3/s$a;

    return-void
.end method

.method public static e(ILjava/util/List;)V
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I",
            "Ljava/util/List<",
            "Ljava/lang/Integer;",
            ">;)V"
        }
    .end annotation

    sget-object v0, Landroidx/media3/exoplayer/hls/d;->f:[I

    invoke-static {v0, p0}, Lcom/google/common/primitives/Ints;->h([II)I

    move-result v0

    const/4 v1, -0x1

    if-eq v0, v1, :cond_1

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v0

    invoke-interface {p1, v0}, Ljava/util/List;->contains(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_0

    goto :goto_0

    :cond_0
    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    :cond_1
    :goto_0
    return-void
.end method

.method public static h(Lt3/s$a;ZLe2/i0;Landroidx/media3/common/y;Ljava/util/List;)Lq3/g;
    .locals 7
    .param p4    # Ljava/util/List;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lt3/s$a;",
            "Z",
            "Le2/i0;",
            "Landroidx/media3/common/y;",
            "Ljava/util/List<",
            "Landroidx/media3/common/y;",
            ">;)",
            "Lq3/g;"
        }
    .end annotation

    invoke-static {p3}, Landroidx/media3/exoplayer/hls/d;->k(Landroidx/media3/common/y;)Z

    move-result p3

    if-eqz p3, :cond_0

    const/4 p3, 0x4

    goto :goto_0

    :cond_0
    const/4 p3, 0x0

    :goto_0
    if-nez p1, :cond_1

    sget-object p0, Lt3/s$a;->a:Lt3/s$a;

    or-int/lit8 p3, p3, 0x20

    :cond_1
    move-object v1, p0

    move v2, p3

    new-instance p0, Lq3/g;

    const/4 v4, 0x0

    if-eqz p4, :cond_2

    :goto_1
    move-object v5, p4

    goto :goto_2

    :cond_2
    invoke-static {}, Lcom/google/common/collect/ImmutableList;->of()Lcom/google/common/collect/ImmutableList;

    move-result-object p4

    goto :goto_1

    :goto_2
    const/4 v6, 0x0

    move-object v0, p0

    move-object v3, p2

    invoke-direct/range {v0 .. v6}, Lq3/g;-><init>(Lt3/s$a;ILe2/i0;Lq3/p;Ljava/util/List;Lz2/r0;)V

    return-object p0
.end method

.method public static i(IZLandroidx/media3/common/y;Ljava/util/List;Le2/i0;Lt3/s$a;Z)Lc4/h0;
    .locals 8
    .param p3    # Ljava/util/List;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(IZ",
            "Landroidx/media3/common/y;",
            "Ljava/util/List<",
            "Landroidx/media3/common/y;",
            ">;",
            "Le2/i0;",
            "Lt3/s$a;",
            "Z)",
            "Lc4/h0;"
        }
    .end annotation

    or-int/lit8 v0, p0, 0x10

    if-eqz p3, :cond_0

    or-int/lit8 v0, p0, 0x30

    goto :goto_0

    :cond_0
    if-eqz p1, :cond_1

    new-instance p0, Landroidx/media3/common/y$b;

    invoke-direct {p0}, Landroidx/media3/common/y$b;-><init>()V

    const-string p1, "application/cea-608"

    invoke-virtual {p0, p1}, Landroidx/media3/common/y$b;->k0(Ljava/lang/String;)Landroidx/media3/common/y$b;

    move-result-object p0

    invoke-virtual {p0}, Landroidx/media3/common/y$b;->I()Landroidx/media3/common/y;

    move-result-object p0

    invoke-static {p0}, Ljava/util/Collections;->singletonList(Ljava/lang/Object;)Ljava/util/List;

    move-result-object p3

    goto :goto_0

    :cond_1
    invoke-static {}, Ljava/util/Collections;->emptyList()Ljava/util/List;

    move-result-object p3

    :goto_0
    iget-object p0, p2, Landroidx/media3/common/y;->j:Ljava/lang/String;

    invoke-static {p0}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result p1

    if-nez p1, :cond_3

    const-string p1, "audio/mp4a-latm"

    invoke-static {p0, p1}, Landroidx/media3/common/f0;->b(Ljava/lang/String;Ljava/lang/String;)Z

    move-result p1

    if-nez p1, :cond_2

    or-int/lit8 v0, v0, 0x2

    :cond_2
    const-string p1, "video/avc"

    invoke-static {p0, p1}, Landroidx/media3/common/f0;->b(Ljava/lang/String;Ljava/lang/String;)Z

    move-result p0

    if-nez p0, :cond_3

    or-int/lit8 v0, v0, 0x4

    :cond_3
    if-nez p6, :cond_4

    sget-object p5, Lt3/s$a;->a:Lt3/s$a;

    const/4 p0, 0x1

    move-object v4, p5

    const/4 v3, 0x1

    goto :goto_1

    :cond_4
    const/4 p0, 0x0

    move-object v4, p5

    const/4 v3, 0x0

    :goto_1
    new-instance p0, Lc4/h0;

    const/4 v2, 0x2

    new-instance v6, Lc4/j;

    invoke-direct {v6, v0, p3}, Lc4/j;-><init>(ILjava/util/List;)V

    const v7, 0x1b8a0

    move-object v1, p0

    move-object v5, p4

    invoke-direct/range {v1 .. v7}, Lc4/h0;-><init>(IILt3/s$a;Le2/i0;Lc4/i0$c;I)V

    return-object p0
.end method

.method public static k(Landroidx/media3/common/y;)Z
    .locals 4

    iget-object p0, p0, Landroidx/media3/common/y;->k:Landroidx/media3/common/Metadata;

    const/4 v0, 0x0

    if-nez p0, :cond_0

    return v0

    :cond_0
    const/4 v1, 0x0

    :goto_0
    invoke-virtual {p0}, Landroidx/media3/common/Metadata;->g()I

    move-result v2

    if-ge v1, v2, :cond_2

    invoke-virtual {p0, v1}, Landroidx/media3/common/Metadata;->e(I)Landroidx/media3/common/Metadata$Entry;

    move-result-object v2

    instance-of v3, v2, Landroidx/media3/exoplayer/hls/HlsTrackMetadataEntry;

    if-eqz v3, :cond_1

    check-cast v2, Landroidx/media3/exoplayer/hls/HlsTrackMetadataEntry;

    iget-object p0, v2, Landroidx/media3/exoplayer/hls/HlsTrackMetadataEntry;->variantInfos:Ljava/util/List;

    invoke-interface {p0}, Ljava/util/List;->isEmpty()Z

    move-result p0

    xor-int/lit8 p0, p0, 0x1

    return p0

    :cond_1
    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_2
    return v0
.end method

.method public static m(Lz2/s;Lz2/t;)Z
    .locals 0
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    :try_start_0
    invoke-interface {p0, p1}, Lz2/s;->e(Lz2/t;)Z

    move-result p0
    :try_end_0
    .catch Ljava/io/EOFException; {:try_start_0 .. :try_end_0} :catch_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    invoke-interface {p1}, Lz2/t;->resetPeekPosition()V

    goto :goto_0

    :catchall_0
    move-exception p0

    invoke-interface {p1}, Lz2/t;->resetPeekPosition()V

    throw p0

    :catch_0
    invoke-interface {p1}, Lz2/t;->resetPeekPosition()V

    const/4 p0, 0x0

    :goto_0
    return p0
.end method


# virtual methods
.method public bridge synthetic a(Lt3/s$a;)Landroidx/media3/exoplayer/hls/g;
    .locals 0

    invoke-virtual {p0, p1}, Landroidx/media3/exoplayer/hls/d;->l(Lt3/s$a;)Landroidx/media3/exoplayer/hls/d;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic b(Z)Landroidx/media3/exoplayer/hls/g;
    .locals 0

    invoke-virtual {p0, p1}, Landroidx/media3/exoplayer/hls/d;->j(Z)Landroidx/media3/exoplayer/hls/d;

    move-result-object p1

    return-object p1
.end method

.method public c(Landroidx/media3/common/y;)Landroidx/media3/common/y;
    .locals 4

    iget-boolean v0, p0, Landroidx/media3/exoplayer/hls/d;->d:Z

    if-eqz v0, :cond_1

    iget-object v0, p0, Landroidx/media3/exoplayer/hls/d;->c:Lt3/s$a;

    invoke-interface {v0, p1}, Lt3/s$a;->a(Landroidx/media3/common/y;)Z

    move-result v0

    if-eqz v0, :cond_1

    invoke-virtual {p1}, Landroidx/media3/common/y;->b()Landroidx/media3/common/y$b;

    move-result-object v0

    const-string v1, "application/x-media3-cues"

    invoke-virtual {v0, v1}, Landroidx/media3/common/y$b;->k0(Ljava/lang/String;)Landroidx/media3/common/y$b;

    move-result-object v0

    iget-object v1, p0, Landroidx/media3/exoplayer/hls/d;->c:Lt3/s$a;

    invoke-interface {v1, p1}, Lt3/s$a;->b(Landroidx/media3/common/y;)I

    move-result v1

    invoke-virtual {v0, v1}, Landroidx/media3/common/y$b;->Q(I)Landroidx/media3/common/y$b;

    move-result-object v0

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    iget-object v2, p1, Landroidx/media3/common/y;->m:Ljava/lang/String;

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v2, p1, Landroidx/media3/common/y;->j:Ljava/lang/String;

    if-eqz v2, :cond_0

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, " "

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object p1, p1, Landroidx/media3/common/y;->j:Ljava/lang/String;

    invoke-virtual {v2, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    goto :goto_0

    :cond_0
    const-string p1, ""

    :goto_0
    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v0, p1}, Landroidx/media3/common/y$b;->M(Ljava/lang/String;)Landroidx/media3/common/y$b;

    move-result-object p1

    const-wide v0, 0x7fffffffffffffffL

    invoke-virtual {p1, v0, v1}, Landroidx/media3/common/y$b;->o0(J)Landroidx/media3/common/y$b;

    move-result-object p1

    invoke-virtual {p1}, Landroidx/media3/common/y$b;->I()Landroidx/media3/common/y;

    move-result-object p1

    :cond_1
    return-object p1
.end method

.method public bridge synthetic d(Landroid/net/Uri;Landroidx/media3/common/y;Ljava/util/List;Le2/i0;Ljava/util/Map;Lz2/t;Lj2/x3;)Landroidx/media3/exoplayer/hls/j;
    .locals 0
    .param p3    # Ljava/util/List;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    invoke-virtual/range {p0 .. p7}, Landroidx/media3/exoplayer/hls/d;->f(Landroid/net/Uri;Landroidx/media3/common/y;Ljava/util/List;Le2/i0;Ljava/util/Map;Lz2/t;Lj2/x3;)Landroidx/media3/exoplayer/hls/b;

    move-result-object p1

    return-object p1
.end method

.method public f(Landroid/net/Uri;Landroidx/media3/common/y;Ljava/util/List;Le2/i0;Ljava/util/Map;Lz2/t;Lj2/x3;)Landroidx/media3/exoplayer/hls/b;
    .locals 14
    .param p3    # Ljava/util/List;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/net/Uri;",
            "Landroidx/media3/common/y;",
            "Ljava/util/List<",
            "Landroidx/media3/common/y;",
            ">;",
            "Le2/i0;",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;>;",
            "Lz2/t;",
            "Lj2/x3;",
            ")",
            "Landroidx/media3/exoplayer/hls/b;"
        }
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    move-object v0, p0

    move-object/from16 v3, p2

    iget-object v1, v3, Landroidx/media3/common/y;->m:Ljava/lang/String;

    invoke-static {v1}, Landroidx/media3/common/r;->a(Ljava/lang/String;)I

    move-result v1

    invoke-static/range {p5 .. p5}, Landroidx/media3/common/r;->b(Ljava/util/Map;)I

    move-result v2

    invoke-static {p1}, Landroidx/media3/common/r;->c(Landroid/net/Uri;)I

    move-result v4

    new-instance v5, Ljava/util/ArrayList;

    sget-object v6, Landroidx/media3/exoplayer/hls/d;->f:[I

    array-length v7, v6

    invoke-direct {v5, v7}, Ljava/util/ArrayList;-><init>(I)V

    invoke-static {v1, v5}, Landroidx/media3/exoplayer/hls/d;->e(ILjava/util/List;)V

    invoke-static {v2, v5}, Landroidx/media3/exoplayer/hls/d;->e(ILjava/util/List;)V

    invoke-static {v4, v5}, Landroidx/media3/exoplayer/hls/d;->e(ILjava/util/List;)V

    array-length v7, v6

    const/4 v8, 0x0

    const/4 v9, 0x0

    :goto_0
    if-ge v9, v7, :cond_0

    aget v10, v6, v9

    invoke-static {v10, v5}, Landroidx/media3/exoplayer/hls/d;->e(ILjava/util/List;)V

    add-int/lit8 v9, v9, 0x1

    goto :goto_0

    :cond_0
    invoke-interface/range {p6 .. p6}, Lz2/t;->resetPeekPosition()V

    const/4 v6, 0x0

    :goto_1
    invoke-interface {v5}, Ljava/util/List;->size()I

    move-result v7

    if-ge v8, v7, :cond_4

    invoke-interface {v5, v8}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v7

    check-cast v7, Ljava/lang/Integer;

    invoke-virtual {v7}, Ljava/lang/Integer;->intValue()I

    move-result v7

    move-object/from16 v9, p3

    move-object/from16 v10, p4

    invoke-virtual {p0, v7, v3, v9, v10}, Landroidx/media3/exoplayer/hls/d;->g(ILandroidx/media3/common/y;Ljava/util/List;Le2/i0;)Lz2/s;

    move-result-object v11

    invoke-static {v11}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v11

    check-cast v11, Lz2/s;

    move-object/from16 v12, p6

    invoke-static {v11, v12}, Landroidx/media3/exoplayer/hls/d;->m(Lz2/s;Lz2/t;)Z

    move-result v13

    if-eqz v13, :cond_1

    new-instance v7, Landroidx/media3/exoplayer/hls/b;

    iget-object v5, v0, Landroidx/media3/exoplayer/hls/d;->c:Lt3/s$a;

    iget-boolean v6, v0, Landroidx/media3/exoplayer/hls/d;->d:Z

    move-object v1, v7

    move-object v2, v11

    move-object/from16 v3, p2

    move-object/from16 v4, p4

    invoke-direct/range {v1 .. v6}, Landroidx/media3/exoplayer/hls/b;-><init>(Lz2/s;Landroidx/media3/common/y;Le2/i0;Lt3/s$a;Z)V

    return-object v7

    :cond_1
    if-nez v6, :cond_3

    if-eq v7, v1, :cond_2

    if-eq v7, v2, :cond_2

    if-eq v7, v4, :cond_2

    const/16 v13, 0xb

    if-ne v7, v13, :cond_3

    :cond_2
    move-object v6, v11

    :cond_3
    add-int/lit8 v8, v8, 0x1

    goto :goto_1

    :cond_4
    move-object/from16 v10, p4

    new-instance v7, Landroidx/media3/exoplayer/hls/b;

    invoke-static {v6}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    move-object v2, v1

    check-cast v2, Lz2/s;

    iget-object v5, v0, Landroidx/media3/exoplayer/hls/d;->c:Lt3/s$a;

    iget-boolean v6, v0, Landroidx/media3/exoplayer/hls/d;->d:Z

    move-object v1, v7

    move-object/from16 v3, p2

    move-object/from16 v4, p4

    invoke-direct/range {v1 .. v6}, Landroidx/media3/exoplayer/hls/b;-><init>(Lz2/s;Landroidx/media3/common/y;Le2/i0;Lt3/s$a;Z)V

    return-object v7
.end method

.method public final g(ILandroidx/media3/common/y;Ljava/util/List;Le2/i0;)Lz2/s;
    .locals 8
    .param p3    # Ljava/util/List;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .annotation build Landroid/annotation/SuppressLint;
        value = {
            "SwitchIntDef"
        }
    .end annotation

    .annotation build Landroidx/annotation/Nullable;
    .end annotation

    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I",
            "Landroidx/media3/common/y;",
            "Ljava/util/List<",
            "Landroidx/media3/common/y;",
            ">;",
            "Le2/i0;",
            ")",
            "Lz2/s;"
        }
    .end annotation

    if-eqz p1, :cond_6

    const/4 v0, 0x1

    if-eq p1, v0, :cond_5

    const/4 v0, 0x2

    if-eq p1, v0, :cond_4

    const/4 v0, 0x7

    if-eq p1, v0, :cond_3

    const/16 v0, 0x8

    if-eq p1, v0, :cond_2

    const/16 v0, 0xb

    if-eq p1, v0, :cond_1

    const/16 p3, 0xd

    if-eq p1, p3, :cond_0

    const/4 p1, 0x0

    return-object p1

    :cond_0
    new-instance p1, Landroidx/media3/exoplayer/hls/s;

    iget-object p2, p2, Landroidx/media3/common/y;->d:Ljava/lang/String;

    iget-object p3, p0, Landroidx/media3/exoplayer/hls/d;->c:Lt3/s$a;

    iget-boolean v0, p0, Landroidx/media3/exoplayer/hls/d;->d:Z

    invoke-direct {p1, p2, p4, p3, v0}, Landroidx/media3/exoplayer/hls/s;-><init>(Ljava/lang/String;Le2/i0;Lt3/s$a;Z)V

    return-object p1

    :cond_1
    iget v1, p0, Landroidx/media3/exoplayer/hls/d;->b:I

    iget-boolean v2, p0, Landroidx/media3/exoplayer/hls/d;->e:Z

    iget-object v6, p0, Landroidx/media3/exoplayer/hls/d;->c:Lt3/s$a;

    iget-boolean v7, p0, Landroidx/media3/exoplayer/hls/d;->d:Z

    move-object v3, p2

    move-object v4, p3

    move-object v5, p4

    invoke-static/range {v1 .. v7}, Landroidx/media3/exoplayer/hls/d;->i(IZLandroidx/media3/common/y;Ljava/util/List;Le2/i0;Lt3/s$a;Z)Lc4/h0;

    move-result-object p1

    return-object p1

    :cond_2
    iget-object p1, p0, Landroidx/media3/exoplayer/hls/d;->c:Lt3/s$a;

    iget-boolean v0, p0, Landroidx/media3/exoplayer/hls/d;->d:Z

    invoke-static {p1, v0, p4, p2, p3}, Landroidx/media3/exoplayer/hls/d;->h(Lt3/s$a;ZLe2/i0;Landroidx/media3/common/y;Ljava/util/List;)Lq3/g;

    move-result-object p1

    return-object p1

    :cond_3
    new-instance p1, Lp3/f;

    const/4 p2, 0x0

    const-wide/16 p3, 0x0

    invoke-direct {p1, p2, p3, p4}, Lp3/f;-><init>(IJ)V

    return-object p1

    :cond_4
    new-instance p1, Lc4/h;

    invoke-direct {p1}, Lc4/h;-><init>()V

    return-object p1

    :cond_5
    new-instance p1, Lc4/e;

    invoke-direct {p1}, Lc4/e;-><init>()V

    return-object p1

    :cond_6
    new-instance p1, Lc4/b;

    invoke-direct {p1}, Lc4/b;-><init>()V

    return-object p1
.end method

.method public j(Z)Landroidx/media3/exoplayer/hls/d;
    .locals 0

    iput-boolean p1, p0, Landroidx/media3/exoplayer/hls/d;->d:Z

    return-object p0
.end method

.method public l(Lt3/s$a;)Landroidx/media3/exoplayer/hls/d;
    .locals 0

    iput-object p1, p0, Landroidx/media3/exoplayer/hls/d;->c:Lt3/s$a;

    return-object p0
.end method
