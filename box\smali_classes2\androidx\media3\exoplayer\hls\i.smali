.class public final Landroidx/media3/exoplayer/hls/i;
.super Lv2/m;


# static fields
.field public static final N:Ljava/util/concurrent/atomic/AtomicInteger;


# instance fields
.field public final A:Z

.field public final B:Z

.field public final C:Lj2/x3;

.field public final D:J

.field public E:Landroidx/media3/exoplayer/hls/j;

.field public F:Landroidx/media3/exoplayer/hls/q;

.field public G:I

.field public H:Z

.field public volatile I:Z

.field public J:Z

.field public K:Lcom/google/common/collect/ImmutableList;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/google/common/collect/ImmutableList<",
            "Ljava/lang/Integer;",
            ">;"
        }
    .end annotation
.end field

.field public L:Z

.field public M:Z

.field public final k:I

.field public final l:I

.field public final m:Landroid/net/Uri;

.field public final n:Z

.field public final o:I

.field public final p:Landroidx/media3/datasource/a;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public final q:Lh2/g;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public final r:Landroidx/media3/exoplayer/hls/j;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public final s:Z

.field public final t:Z

.field public final u:Le2/i0;

.field public final v:Landroidx/media3/exoplayer/hls/g;

.field public final w:Ljava/util/List;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation

    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Landroidx/media3/common/y;",
            ">;"
        }
    .end annotation
.end field

.field public final x:Landroidx/media3/common/DrmInitData;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public final y:Ll3/b;

.field public final z:Le2/c0;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    new-instance v0, Ljava/util/concurrent/atomic/AtomicInteger;

    invoke-direct {v0}, Ljava/util/concurrent/atomic/AtomicInteger;-><init>()V

    sput-object v0, Landroidx/media3/exoplayer/hls/i;->N:Ljava/util/concurrent/atomic/AtomicInteger;

    return-void
.end method

.method public constructor <init>(Landroidx/media3/exoplayer/hls/g;Landroidx/media3/datasource/a;Lh2/g;Landroidx/media3/common/y;ZLandroidx/media3/datasource/a;Lh2/g;ZLandroid/net/Uri;Ljava/util/List;ILjava/lang/Object;JJJIZIZZLe2/i0;JLandroidx/media3/common/DrmInitData;Landroidx/media3/exoplayer/hls/j;Ll3/b;Le2/c0;ZLj2/x3;)V
    .locals 14
    .param p6    # Landroidx/media3/datasource/a;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .param p7    # Lh2/g;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .param p10    # Ljava/util/List;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .param p12    # Ljava/lang/Object;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .param p27    # Landroidx/media3/common/DrmInitData;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .param p28    # Landroidx/media3/exoplayer/hls/j;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroidx/media3/exoplayer/hls/g;",
            "Landroidx/media3/datasource/a;",
            "Lh2/g;",
            "Landroidx/media3/common/y;",
            "Z",
            "Landroidx/media3/datasource/a;",
            "Lh2/g;",
            "Z",
            "Landroid/net/Uri;",
            "Ljava/util/List<",
            "Landroidx/media3/common/y;",
            ">;I",
            "Ljava/lang/Object;",
            "JJJIZIZZ",
            "Le2/i0;",
            "J",
            "Landroidx/media3/common/DrmInitData;",
            "Landroidx/media3/exoplayer/hls/j;",
            "Ll3/b;",
            "Le2/c0;",
            "Z",
            "Lj2/x3;",
            ")V"
        }
    .end annotation

    move-object v12, p0

    move-object/from16 v13, p7

    move-object v0, p0

    move-object/from16 v1, p2

    move-object/from16 v2, p3

    move-object/from16 v3, p4

    move/from16 v4, p11

    move-object/from16 v5, p12

    move-wide/from16 v6, p13

    move-wide/from16 v8, p15

    move-wide/from16 v10, p17

    invoke-direct/range {v0 .. v11}, Lv2/m;-><init>(Landroidx/media3/datasource/a;Lh2/g;Landroidx/media3/common/y;ILjava/lang/Object;JJJ)V

    move/from16 v0, p5

    iput-boolean v0, v12, Landroidx/media3/exoplayer/hls/i;->A:Z

    move/from16 v0, p19

    iput v0, v12, Landroidx/media3/exoplayer/hls/i;->o:I

    move/from16 v0, p20

    iput-boolean v0, v12, Landroidx/media3/exoplayer/hls/i;->M:Z

    move/from16 v0, p21

    iput v0, v12, Landroidx/media3/exoplayer/hls/i;->l:I

    iput-object v13, v12, Landroidx/media3/exoplayer/hls/i;->q:Lh2/g;

    move-object/from16 v0, p6

    iput-object v0, v12, Landroidx/media3/exoplayer/hls/i;->p:Landroidx/media3/datasource/a;

    if-eqz v13, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    iput-boolean v0, v12, Landroidx/media3/exoplayer/hls/i;->H:Z

    move/from16 v0, p8

    iput-boolean v0, v12, Landroidx/media3/exoplayer/hls/i;->B:Z

    move-object/from16 v0, p9

    iput-object v0, v12, Landroidx/media3/exoplayer/hls/i;->m:Landroid/net/Uri;

    move/from16 v0, p23

    iput-boolean v0, v12, Landroidx/media3/exoplayer/hls/i;->s:Z

    move-object/from16 v0, p24

    iput-object v0, v12, Landroidx/media3/exoplayer/hls/i;->u:Le2/i0;

    move-wide/from16 v0, p25

    iput-wide v0, v12, Landroidx/media3/exoplayer/hls/i;->D:J

    move/from16 v0, p22

    iput-boolean v0, v12, Landroidx/media3/exoplayer/hls/i;->t:Z

    move-object v0, p1

    iput-object v0, v12, Landroidx/media3/exoplayer/hls/i;->v:Landroidx/media3/exoplayer/hls/g;

    move-object/from16 v0, p10

    iput-object v0, v12, Landroidx/media3/exoplayer/hls/i;->w:Ljava/util/List;

    move-object/from16 v0, p27

    iput-object v0, v12, Landroidx/media3/exoplayer/hls/i;->x:Landroidx/media3/common/DrmInitData;

    move-object/from16 v0, p28

    iput-object v0, v12, Landroidx/media3/exoplayer/hls/i;->r:Landroidx/media3/exoplayer/hls/j;

    move-object/from16 v0, p29

    iput-object v0, v12, Landroidx/media3/exoplayer/hls/i;->y:Ll3/b;

    move-object/from16 v0, p30

    iput-object v0, v12, Landroidx/media3/exoplayer/hls/i;->z:Le2/c0;

    move/from16 v0, p31

    iput-boolean v0, v12, Landroidx/media3/exoplayer/hls/i;->n:Z

    move-object/from16 v0, p32

    iput-object v0, v12, Landroidx/media3/exoplayer/hls/i;->C:Lj2/x3;

    invoke-static {}, Lcom/google/common/collect/ImmutableList;->of()Lcom/google/common/collect/ImmutableList;

    move-result-object v0

    iput-object v0, v12, Landroidx/media3/exoplayer/hls/i;->K:Lcom/google/common/collect/ImmutableList;

    sget-object v0, Landroidx/media3/exoplayer/hls/i;->N:Ljava/util/concurrent/atomic/AtomicInteger;

    invoke-virtual {v0}, Ljava/util/concurrent/atomic/AtomicInteger;->getAndIncrement()I

    move-result v0

    iput v0, v12, Landroidx/media3/exoplayer/hls/i;->k:I

    return-void
.end method

.method public static g(Landroidx/media3/datasource/a;[B[B)Landroidx/media3/datasource/a;
    .locals 1
    .param p1    # [B
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .param p2    # [B
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    if-eqz p1, :cond_0

    invoke-static {p2}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    new-instance v0, Landroidx/media3/exoplayer/hls/a;

    invoke-direct {v0, p0, p1, p2}, Landroidx/media3/exoplayer/hls/a;-><init>(Landroidx/media3/datasource/a;[B[B)V

    return-object v0

    :cond_0
    return-object p0
.end method

.method public static h(Landroidx/media3/exoplayer/hls/g;Landroidx/media3/datasource/a;Landroidx/media3/common/y;JLandroidx/media3/exoplayer/hls/playlist/b;Landroidx/media3/exoplayer/hls/e$e;Landroid/net/Uri;Ljava/util/List;ILjava/lang/Object;ZLandroidx/media3/exoplayer/hls/r;JLandroidx/media3/exoplayer/hls/i;[B[BZLj2/x3;Landroidx/media3/exoplayer/upstream/g$a;)Landroidx/media3/exoplayer/hls/i;
    .locals 43
    .param p8    # Ljava/util/List;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .param p10    # Ljava/lang/Object;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .param p15    # Landroidx/media3/exoplayer/hls/i;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .param p16    # [B
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .param p17    # [B
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .param p20    # Landroidx/media3/exoplayer/upstream/g$a;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroidx/media3/exoplayer/hls/g;",
            "Landroidx/media3/datasource/a;",
            "Landroidx/media3/common/y;",
            "J",
            "Landroidx/media3/exoplayer/hls/playlist/b;",
            "Landroidx/media3/exoplayer/hls/e$e;",
            "Landroid/net/Uri;",
            "Ljava/util/List<",
            "Landroidx/media3/common/y;",
            ">;I",
            "Ljava/lang/Object;",
            "Z",
            "Landroidx/media3/exoplayer/hls/r;",
            "J",
            "Landroidx/media3/exoplayer/hls/i;",
            "[B[BZ",
            "Lj2/x3;",
            "Landroidx/media3/exoplayer/upstream/g$a;",
            ")",
            "Landroidx/media3/exoplayer/hls/i;"
        }
    .end annotation

    move-object/from16 v0, p1

    move-object/from16 v1, p5

    move-object/from16 v2, p6

    move-object/from16 v3, p15

    move-object/from16 v4, p16

    move-object/from16 v5, p17

    iget-object v6, v2, Landroidx/media3/exoplayer/hls/e$e;->a:Landroidx/media3/exoplayer/hls/playlist/b$e;

    new-instance v7, Lh2/g$b;

    invoke-direct {v7}, Lh2/g$b;-><init>()V

    iget-object v8, v1, Lp2/e;->a:Ljava/lang/String;

    iget-object v9, v6, Landroidx/media3/exoplayer/hls/playlist/b$e;->a:Ljava/lang/String;

    invoke-static {v8, v9}, Le2/k0;->f(Ljava/lang/String;Ljava/lang/String;)Landroid/net/Uri;

    move-result-object v8

    invoke-virtual {v7, v8}, Lh2/g$b;->i(Landroid/net/Uri;)Lh2/g$b;

    move-result-object v7

    iget-wide v8, v6, Landroidx/media3/exoplayer/hls/playlist/b$e;->i:J

    invoke-virtual {v7, v8, v9}, Lh2/g$b;->h(J)Lh2/g$b;

    move-result-object v7

    iget-wide v8, v6, Landroidx/media3/exoplayer/hls/playlist/b$e;->j:J

    invoke-virtual {v7, v8, v9}, Lh2/g$b;->g(J)Lh2/g$b;

    move-result-object v7

    iget-boolean v8, v2, Landroidx/media3/exoplayer/hls/e$e;->d:Z

    if-eqz v8, :cond_0

    const/16 v8, 0x8

    goto :goto_0

    :cond_0
    const/4 v8, 0x0

    :goto_0
    invoke-virtual {v7, v8}, Lh2/g$b;->b(I)Lh2/g$b;

    move-result-object v7

    invoke-virtual {v7}, Lh2/g$b;->a()Lh2/g;

    move-result-object v13

    const/4 v7, 0x1

    if-eqz v4, :cond_1

    const/4 v15, 0x1

    goto :goto_1

    :cond_1
    const/4 v15, 0x0

    :goto_1
    if-eqz v15, :cond_2

    iget-object v10, v6, Landroidx/media3/exoplayer/hls/playlist/b$e;->h:Ljava/lang/String;

    invoke-static {v10}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v10

    check-cast v10, Ljava/lang/String;

    invoke-static {v10}, Landroidx/media3/exoplayer/hls/i;->j(Ljava/lang/String;)[B

    move-result-object v10

    goto :goto_2

    :cond_2
    const/4 v10, 0x0

    :goto_2
    invoke-static {v0, v4, v10}, Landroidx/media3/exoplayer/hls/i;->g(Landroidx/media3/datasource/a;[B[B)Landroidx/media3/datasource/a;

    move-result-object v12

    iget-object v4, v6, Landroidx/media3/exoplayer/hls/playlist/b$e;->b:Landroidx/media3/exoplayer/hls/playlist/b$d;

    if-eqz v4, :cond_5

    if-eqz v5, :cond_3

    const/4 v10, 0x1

    goto :goto_3

    :cond_3
    const/4 v10, 0x0

    :goto_3
    if-eqz v10, :cond_4

    iget-object v11, v4, Landroidx/media3/exoplayer/hls/playlist/b$e;->h:Ljava/lang/String;

    invoke-static {v11}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v11

    check-cast v11, Ljava/lang/String;

    invoke-static {v11}, Landroidx/media3/exoplayer/hls/i;->j(Ljava/lang/String;)[B

    move-result-object v11

    goto :goto_4

    :cond_4
    const/4 v11, 0x0

    :goto_4
    iget-object v14, v1, Lp2/e;->a:Ljava/lang/String;

    iget-object v8, v4, Landroidx/media3/exoplayer/hls/playlist/b$e;->a:Ljava/lang/String;

    invoke-static {v14, v8}, Le2/k0;->f(Ljava/lang/String;Ljava/lang/String;)Landroid/net/Uri;

    move-result-object v8

    new-instance v14, Lh2/g$b;

    invoke-direct {v14}, Lh2/g$b;-><init>()V

    invoke-virtual {v14, v8}, Lh2/g$b;->i(Landroid/net/Uri;)Lh2/g$b;

    move-result-object v8

    move/from16 p16, v10

    iget-wide v9, v4, Landroidx/media3/exoplayer/hls/playlist/b$e;->i:J

    invoke-virtual {v8, v9, v10}, Lh2/g$b;->h(J)Lh2/g$b;

    move-result-object v8

    iget-wide v9, v4, Landroidx/media3/exoplayer/hls/playlist/b$e;->j:J

    invoke-virtual {v8, v9, v10}, Lh2/g$b;->g(J)Lh2/g$b;

    move-result-object v4

    invoke-virtual {v4}, Lh2/g$b;->a()Lh2/g;

    move-result-object v4

    invoke-static {v0, v5, v11}, Landroidx/media3/exoplayer/hls/i;->g(Landroidx/media3/datasource/a;[B[B)Landroidx/media3/datasource/a;

    move-result-object v0

    move/from16 v18, p16

    move-object/from16 v16, v0

    goto :goto_5

    :cond_5
    const/4 v4, 0x0

    const/16 v16, 0x0

    const/16 v18, 0x0

    :goto_5
    iget-wide v8, v6, Landroidx/media3/exoplayer/hls/playlist/b$e;->e:J

    add-long v23, p3, v8

    iget-wide v8, v6, Landroidx/media3/exoplayer/hls/playlist/b$e;->c:J

    add-long v25, v23, v8

    iget v0, v1, Landroidx/media3/exoplayer/hls/playlist/b;->j:I

    iget v1, v6, Landroidx/media3/exoplayer/hls/playlist/b$e;->d:I

    add-int/2addr v0, v1

    if-eqz v3, :cond_a

    iget-object v1, v3, Landroidx/media3/exoplayer/hls/i;->q:Lh2/g;

    if-eq v4, v1, :cond_7

    if-eqz v4, :cond_6

    if-eqz v1, :cond_6

    iget-object v5, v4, Lh2/g;->a:Landroid/net/Uri;

    iget-object v1, v1, Lh2/g;->a:Landroid/net/Uri;

    invoke-virtual {v5, v1}, Landroid/net/Uri;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_6

    iget-wide v8, v4, Lh2/g;->g:J

    iget-object v1, v3, Landroidx/media3/exoplayer/hls/i;->q:Lh2/g;

    iget-wide v10, v1, Lh2/g;->g:J

    cmp-long v1, v8, v10

    if-nez v1, :cond_6

    goto :goto_6

    :cond_6
    const/4 v1, 0x0

    goto :goto_7

    :cond_7
    :goto_6
    const/4 v1, 0x1

    :goto_7
    iget-object v5, v3, Landroidx/media3/exoplayer/hls/i;->m:Landroid/net/Uri;

    move-object/from16 v8, p7

    invoke-virtual {v8, v5}, Landroid/net/Uri;->equals(Ljava/lang/Object;)Z

    move-result v5

    if-eqz v5, :cond_8

    iget-boolean v5, v3, Landroidx/media3/exoplayer/hls/i;->J:Z

    if-eqz v5, :cond_8

    const/4 v9, 0x1

    goto :goto_8

    :cond_8
    const/4 v9, 0x0

    :goto_8
    iget-object v5, v3, Landroidx/media3/exoplayer/hls/i;->y:Ll3/b;

    iget-object v10, v3, Landroidx/media3/exoplayer/hls/i;->z:Le2/c0;

    if-eqz v1, :cond_9

    if-eqz v9, :cond_9

    iget-boolean v1, v3, Landroidx/media3/exoplayer/hls/i;->L:Z

    if-nez v1, :cond_9

    iget v1, v3, Landroidx/media3/exoplayer/hls/i;->l:I

    if-ne v1, v0, :cond_9

    iget-object v1, v3, Landroidx/media3/exoplayer/hls/i;->E:Landroidx/media3/exoplayer/hls/j;

    goto :goto_9

    :cond_9
    const/4 v1, 0x0

    :goto_9
    move-object/from16 v38, v1

    move-object/from16 v39, v5

    move-object/from16 v40, v10

    goto :goto_a

    :cond_a
    move-object/from16 v8, p7

    new-instance v1, Ll3/b;

    invoke-direct {v1}, Ll3/b;-><init>()V

    new-instance v3, Le2/c0;

    const/16 v5, 0xa

    invoke-direct {v3, v5}, Le2/c0;-><init>(I)V

    move-object/from16 v39, v1

    move-object/from16 v40, v3

    const/16 v38, 0x0

    :goto_a
    new-instance v1, Landroidx/media3/exoplayer/hls/i;

    iget-wide v9, v2, Landroidx/media3/exoplayer/hls/e$e;->b:J

    iget v3, v2, Landroidx/media3/exoplayer/hls/e$e;->c:I

    iget-boolean v2, v2, Landroidx/media3/exoplayer/hls/e$e;->d:Z

    xor-int/lit8 v30, v2, 0x1

    iget-boolean v2, v6, Landroidx/media3/exoplayer/hls/playlist/b$e;->k:Z

    move/from16 v32, v2

    move-object/from16 v2, p12

    invoke-virtual {v2, v0}, Landroidx/media3/exoplayer/hls/r;->a(I)Le2/i0;

    move-result-object v34

    iget-object v2, v6, Landroidx/media3/exoplayer/hls/playlist/b$e;->f:Landroidx/media3/common/DrmInitData;

    move-object/from16 v37, v2

    move-wide v5, v9

    move-object v10, v1

    move-object/from16 v11, p0

    move-object/from16 v14, p2

    move-object/from16 v17, v4

    move-object/from16 v19, p7

    move-object/from16 v20, p8

    move/from16 v21, p9

    move-object/from16 v22, p10

    move-wide/from16 v27, v5

    move/from16 v29, v3

    move/from16 v31, v0

    move/from16 v33, p11

    move-wide/from16 v35, p13

    move/from16 v41, p18

    move-object/from16 v42, p19

    invoke-direct/range {v10 .. v42}, Landroidx/media3/exoplayer/hls/i;-><init>(Landroidx/media3/exoplayer/hls/g;Landroidx/media3/datasource/a;Lh2/g;Landroidx/media3/common/y;ZLandroidx/media3/datasource/a;Lh2/g;ZLandroid/net/Uri;Ljava/util/List;ILjava/lang/Object;JJJIZIZZLe2/i0;JLandroidx/media3/common/DrmInitData;Landroidx/media3/exoplayer/hls/j;Ll3/b;Le2/c0;ZLj2/x3;)V

    return-object v1
.end method

.method public static j(Ljava/lang/String;)[B
    .locals 4

    invoke-static {p0}, Lcom/google/common/base/a;->e(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    const-string v1, "0x"

    invoke-virtual {v0, v1}, Ljava/lang/String;->startsWith(Ljava/lang/String;)Z

    move-result v0

    if-eqz v0, :cond_0

    const/4 v0, 0x2

    invoke-virtual {p0, v0}, Ljava/lang/String;->substring(I)Ljava/lang/String;

    move-result-object p0

    :cond_0
    new-instance v0, Ljava/math/BigInteger;

    const/16 v1, 0x10

    invoke-direct {v0, p0, v1}, Ljava/math/BigInteger;-><init>(Ljava/lang/String;I)V

    invoke-virtual {v0}, Ljava/math/BigInteger;->toByteArray()[B

    move-result-object p0

    new-array v0, v1, [B

    array-length v2, p0

    if-le v2, v1, :cond_1

    array-length v2, p0

    sub-int/2addr v2, v1

    goto :goto_0

    :cond_1
    const/4 v2, 0x0

    :goto_0
    array-length v3, p0

    sub-int/2addr v1, v3

    add-int/2addr v1, v2

    array-length v3, p0

    sub-int/2addr v3, v2

    invoke-static {p0, v2, v0, v1, v3}, Ljava/lang/System;->arraycopy(Ljava/lang/Object;ILjava/lang/Object;II)V

    return-object v0
.end method

.method public static n(Landroidx/media3/exoplayer/hls/e$e;Landroidx/media3/exoplayer/hls/playlist/b;)Z
    .locals 2

    iget-object v0, p0, Landroidx/media3/exoplayer/hls/e$e;->a:Landroidx/media3/exoplayer/hls/playlist/b$e;

    instance-of v1, v0, Landroidx/media3/exoplayer/hls/playlist/b$b;

    if-eqz v1, :cond_2

    check-cast v0, Landroidx/media3/exoplayer/hls/playlist/b$b;

    iget-boolean v0, v0, Landroidx/media3/exoplayer/hls/playlist/b$b;->l:Z

    if-nez v0, :cond_1

    iget p0, p0, Landroidx/media3/exoplayer/hls/e$e;->c:I

    if-nez p0, :cond_0

    iget-boolean p0, p1, Lp2/e;->c:Z

    if-eqz p0, :cond_0

    goto :goto_0

    :cond_0
    const/4 p0, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 p0, 0x1

    :goto_1
    return p0

    :cond_2
    iget-boolean p0, p1, Lp2/e;->c:Z

    return p0
.end method

.method public static u(Landroidx/media3/exoplayer/hls/i;Landroid/net/Uri;Landroidx/media3/exoplayer/hls/playlist/b;Landroidx/media3/exoplayer/hls/e$e;J)Z
    .locals 3
    .param p0    # Landroidx/media3/exoplayer/hls/i;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    const/4 v0, 0x0

    if-nez p0, :cond_0

    return v0

    :cond_0
    iget-object v1, p0, Landroidx/media3/exoplayer/hls/i;->m:Landroid/net/Uri;

    invoke-virtual {p1, v1}, Landroid/net/Uri;->equals(Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_1

    iget-boolean p1, p0, Landroidx/media3/exoplayer/hls/i;->J:Z

    if-eqz p1, :cond_1

    return v0

    :cond_1
    iget-object p1, p3, Landroidx/media3/exoplayer/hls/e$e;->a:Landroidx/media3/exoplayer/hls/playlist/b$e;

    iget-wide v1, p1, Landroidx/media3/exoplayer/hls/playlist/b$e;->e:J

    add-long/2addr p4, v1

    invoke-static {p3, p2}, Landroidx/media3/exoplayer/hls/i;->n(Landroidx/media3/exoplayer/hls/e$e;Landroidx/media3/exoplayer/hls/playlist/b;)Z

    move-result p1

    if-eqz p1, :cond_2

    iget-wide p0, p0, Lv2/e;->h:J

    cmp-long p2, p4, p0

    if-gez p2, :cond_3

    :cond_2
    const/4 v0, 0x1

    :cond_3
    return v0
.end method


# virtual methods
.method public cancelLoad()V
    .locals 1

    const/4 v0, 0x1

    iput-boolean v0, p0, Landroidx/media3/exoplayer/hls/i;->I:Z

    return-void
.end method

.method public f()Z
    .locals 1

    iget-boolean v0, p0, Landroidx/media3/exoplayer/hls/i;->J:Z

    return v0
.end method

.method public final i(Landroidx/media3/datasource/a;Lh2/g;ZZ)V
    .locals 3
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    const/4 v0, 0x0

    if-eqz p3, :cond_1

    iget p3, p0, Landroidx/media3/exoplayer/hls/i;->G:I

    if-eqz p3, :cond_0

    const/4 v0, 0x1

    :cond_0
    move-object p3, p2

    goto :goto_0

    :cond_1
    iget p3, p0, Landroidx/media3/exoplayer/hls/i;->G:I

    int-to-long v1, p3

    invoke-virtual {p2, v1, v2}, Lh2/g;->e(J)Lh2/g;

    move-result-object p3

    :goto_0
    :try_start_0
    invoke-virtual {p0, p1, p3, p4}, Landroidx/media3/exoplayer/hls/i;->s(Landroidx/media3/datasource/a;Lh2/g;Z)Lz2/j;

    move-result-object p3

    if-eqz v0, :cond_2

    iget p4, p0, Landroidx/media3/exoplayer/hls/i;->G:I

    invoke-interface {p3, p4}, Lz2/t;->skipFully(I)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    goto :goto_1

    :catchall_0
    move-exception p2

    goto :goto_6

    :cond_2
    :goto_1
    :try_start_1
    iget-boolean p4, p0, Landroidx/media3/exoplayer/hls/i;->I:Z

    if-nez p4, :cond_3

    iget-object p4, p0, Landroidx/media3/exoplayer/hls/i;->E:Landroidx/media3/exoplayer/hls/j;

    invoke-interface {p4, p3}, Landroidx/media3/exoplayer/hls/j;->a(Lz2/t;)Z

    move-result p4
    :try_end_1
    .catch Ljava/io/EOFException; {:try_start_1 .. :try_end_1} :catch_0
    .catchall {:try_start_1 .. :try_end_1} :catchall_1

    if-eqz p4, :cond_3

    goto :goto_1

    :catchall_1
    move-exception p4

    goto :goto_5

    :catch_0
    move-exception p4

    goto :goto_3

    :cond_3
    :try_start_2
    invoke-interface {p3}, Lz2/t;->getPosition()J

    move-result-wide p3

    iget-wide v0, p2, Lh2/g;->g:J

    :goto_2
    sub-long/2addr p3, v0

    long-to-int p2, p3

    iput p2, p0, Landroidx/media3/exoplayer/hls/i;->G:I
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_0

    goto :goto_4

    :goto_3
    :try_start_3
    iget-object v0, p0, Lv2/e;->d:Landroidx/media3/common/y;

    iget v0, v0, Landroidx/media3/common/y;->f:I

    and-int/lit16 v0, v0, 0x4000

    if-eqz v0, :cond_4

    iget-object p4, p0, Landroidx/media3/exoplayer/hls/i;->E:Landroidx/media3/exoplayer/hls/j;

    invoke-interface {p4}, Landroidx/media3/exoplayer/hls/j;->b()V
    :try_end_3
    .catchall {:try_start_3 .. :try_end_3} :catchall_1

    :try_start_4
    invoke-interface {p3}, Lz2/t;->getPosition()J

    move-result-wide p3

    iget-wide v0, p2, Lh2/g;->g:J
    :try_end_4
    .catchall {:try_start_4 .. :try_end_4} :catchall_0

    goto :goto_2

    :goto_4
    invoke-static {p1}, Lh2/f;->a(Landroidx/media3/datasource/a;)V

    return-void

    :cond_4
    :try_start_5
    throw p4
    :try_end_5
    .catchall {:try_start_5 .. :try_end_5} :catchall_1

    :goto_5
    :try_start_6
    invoke-interface {p3}, Lz2/t;->getPosition()J

    move-result-wide v0

    iget-wide p2, p2, Lh2/g;->g:J

    sub-long/2addr v0, p2

    long-to-int p2, v0

    iput p2, p0, Landroidx/media3/exoplayer/hls/i;->G:I

    throw p4
    :try_end_6
    .catchall {:try_start_6 .. :try_end_6} :catchall_0

    :goto_6
    invoke-static {p1}, Lh2/f;->a(Landroidx/media3/datasource/a;)V

    throw p2
.end method

.method public k(I)I
    .locals 1

    iget-boolean v0, p0, Landroidx/media3/exoplayer/hls/i;->n:Z

    xor-int/lit8 v0, v0, 0x1

    invoke-static {v0}, Le2/a;->g(Z)V

    iget-object v0, p0, Landroidx/media3/exoplayer/hls/i;->K:Lcom/google/common/collect/ImmutableList;

    invoke-virtual {v0}, Ljava/util/AbstractCollection;->size()I

    move-result v0

    if-lt p1, v0, :cond_0

    const/4 p1, 0x0

    return p1

    :cond_0
    iget-object v0, p0, Landroidx/media3/exoplayer/hls/i;->K:Lcom/google/common/collect/ImmutableList;

    invoke-interface {v0, p1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/lang/Integer;

    invoke-virtual {p1}, Ljava/lang/Integer;->intValue()I

    move-result p1

    return p1
.end method

.method public l(Landroidx/media3/exoplayer/hls/q;Lcom/google/common/collect/ImmutableList;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroidx/media3/exoplayer/hls/q;",
            "Lcom/google/common/collect/ImmutableList<",
            "Ljava/lang/Integer;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Landroidx/media3/exoplayer/hls/i;->F:Landroidx/media3/exoplayer/hls/q;

    iput-object p2, p0, Landroidx/media3/exoplayer/hls/i;->K:Lcom/google/common/collect/ImmutableList;

    return-void
.end method

.method public load()V
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget-object v0, p0, Landroidx/media3/exoplayer/hls/i;->F:Landroidx/media3/exoplayer/hls/q;

    invoke-static {v0}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    iget-object v0, p0, Landroidx/media3/exoplayer/hls/i;->E:Landroidx/media3/exoplayer/hls/j;

    if-nez v0, :cond_0

    iget-object v0, p0, Landroidx/media3/exoplayer/hls/i;->r:Landroidx/media3/exoplayer/hls/j;

    if-eqz v0, :cond_0

    invoke-interface {v0}, Landroidx/media3/exoplayer/hls/j;->d()Z

    move-result v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Landroidx/media3/exoplayer/hls/i;->r:Landroidx/media3/exoplayer/hls/j;

    iput-object v0, p0, Landroidx/media3/exoplayer/hls/i;->E:Landroidx/media3/exoplayer/hls/j;

    const/4 v0, 0x0

    iput-boolean v0, p0, Landroidx/media3/exoplayer/hls/i;->H:Z

    :cond_0
    invoke-virtual {p0}, Landroidx/media3/exoplayer/hls/i;->q()V

    iget-boolean v0, p0, Landroidx/media3/exoplayer/hls/i;->I:Z

    if-nez v0, :cond_2

    iget-boolean v0, p0, Landroidx/media3/exoplayer/hls/i;->t:Z

    if-nez v0, :cond_1

    invoke-virtual {p0}, Landroidx/media3/exoplayer/hls/i;->p()V

    :cond_1
    iget-boolean v0, p0, Landroidx/media3/exoplayer/hls/i;->I:Z

    xor-int/lit8 v0, v0, 0x1

    iput-boolean v0, p0, Landroidx/media3/exoplayer/hls/i;->J:Z

    :cond_2
    return-void
.end method

.method public m()V
    .locals 1

    const/4 v0, 0x1

    iput-boolean v0, p0, Landroidx/media3/exoplayer/hls/i;->L:Z

    return-void
.end method

.method public o()Z
    .locals 1

    iget-boolean v0, p0, Landroidx/media3/exoplayer/hls/i;->M:Z

    return v0
.end method

.method public final p()V
    .locals 4
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget-object v0, p0, Lv2/e;->i:Lh2/m;

    iget-object v1, p0, Lv2/e;->b:Lh2/g;

    iget-boolean v2, p0, Landroidx/media3/exoplayer/hls/i;->A:Z

    const/4 v3, 0x1

    invoke-virtual {p0, v0, v1, v2, v3}, Landroidx/media3/exoplayer/hls/i;->i(Landroidx/media3/datasource/a;Lh2/g;ZZ)V

    return-void
.end method

.method public final q()V
    .locals 4
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget-boolean v0, p0, Landroidx/media3/exoplayer/hls/i;->H:Z

    if-nez v0, :cond_0

    return-void

    :cond_0
    iget-object v0, p0, Landroidx/media3/exoplayer/hls/i;->p:Landroidx/media3/datasource/a;

    invoke-static {v0}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    iget-object v0, p0, Landroidx/media3/exoplayer/hls/i;->q:Lh2/g;

    invoke-static {v0}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    iget-object v0, p0, Landroidx/media3/exoplayer/hls/i;->p:Landroidx/media3/datasource/a;

    iget-object v1, p0, Landroidx/media3/exoplayer/hls/i;->q:Lh2/g;

    iget-boolean v2, p0, Landroidx/media3/exoplayer/hls/i;->B:Z

    const/4 v3, 0x0

    invoke-virtual {p0, v0, v1, v2, v3}, Landroidx/media3/exoplayer/hls/i;->i(Landroidx/media3/datasource/a;Lh2/g;ZZ)V

    iput v3, p0, Landroidx/media3/exoplayer/hls/i;->G:I

    iput-boolean v3, p0, Landroidx/media3/exoplayer/hls/i;->H:Z

    return-void
.end method

.method public final r(Lz2/t;)J
    .locals 8
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    invoke-interface {p1}, Lz2/t;->resetPeekPosition()V

    const-wide v0, -0x7fffffffffffffffL    # -4.9E-324

    :try_start_0
    iget-object v2, p0, Landroidx/media3/exoplayer/hls/i;->z:Le2/c0;

    const/16 v3, 0xa

    invoke-virtual {v2, v3}, Le2/c0;->Q(I)V

    iget-object v2, p0, Landroidx/media3/exoplayer/hls/i;->z:Le2/c0;

    invoke-virtual {v2}, Le2/c0;->e()[B

    move-result-object v2

    const/4 v4, 0x0

    invoke-interface {p1, v2, v4, v3}, Lz2/t;->peekFully([BII)V
    :try_end_0
    .catch Ljava/io/EOFException; {:try_start_0 .. :try_end_0} :catch_0

    iget-object v2, p0, Landroidx/media3/exoplayer/hls/i;->z:Le2/c0;

    invoke-virtual {v2}, Le2/c0;->K()I

    move-result v2

    const v5, 0x494433

    if-eq v2, v5, :cond_0

    return-wide v0

    :cond_0
    iget-object v2, p0, Landroidx/media3/exoplayer/hls/i;->z:Le2/c0;

    const/4 v5, 0x3

    invoke-virtual {v2, v5}, Le2/c0;->V(I)V

    iget-object v2, p0, Landroidx/media3/exoplayer/hls/i;->z:Le2/c0;

    invoke-virtual {v2}, Le2/c0;->G()I

    move-result v2

    add-int/lit8 v5, v2, 0xa

    iget-object v6, p0, Landroidx/media3/exoplayer/hls/i;->z:Le2/c0;

    invoke-virtual {v6}, Le2/c0;->b()I

    move-result v6

    if-le v5, v6, :cond_1

    iget-object v6, p0, Landroidx/media3/exoplayer/hls/i;->z:Le2/c0;

    invoke-virtual {v6}, Le2/c0;->e()[B

    move-result-object v6

    iget-object v7, p0, Landroidx/media3/exoplayer/hls/i;->z:Le2/c0;

    invoke-virtual {v7, v5}, Le2/c0;->Q(I)V

    iget-object v5, p0, Landroidx/media3/exoplayer/hls/i;->z:Le2/c0;

    invoke-virtual {v5}, Le2/c0;->e()[B

    move-result-object v5

    invoke-static {v6, v4, v5, v4, v3}, Ljava/lang/System;->arraycopy(Ljava/lang/Object;ILjava/lang/Object;II)V

    :cond_1
    iget-object v5, p0, Landroidx/media3/exoplayer/hls/i;->z:Le2/c0;

    invoke-virtual {v5}, Le2/c0;->e()[B

    move-result-object v5

    invoke-interface {p1, v5, v3, v2}, Lz2/t;->peekFully([BII)V

    iget-object p1, p0, Landroidx/media3/exoplayer/hls/i;->y:Ll3/b;

    iget-object v3, p0, Landroidx/media3/exoplayer/hls/i;->z:Le2/c0;

    invoke-virtual {v3}, Le2/c0;->e()[B

    move-result-object v3

    invoke-virtual {p1, v3, v2}, Ll3/b;->e([BI)Landroidx/media3/common/Metadata;

    move-result-object p1

    if-nez p1, :cond_2

    return-wide v0

    :cond_2
    invoke-virtual {p1}, Landroidx/media3/common/Metadata;->g()I

    move-result v2

    const/4 v3, 0x0

    :goto_0
    if-ge v3, v2, :cond_4

    invoke-virtual {p1, v3}, Landroidx/media3/common/Metadata;->e(I)Landroidx/media3/common/Metadata$Entry;

    move-result-object v5

    instance-of v6, v5, Landroidx/media3/extractor/metadata/id3/PrivFrame;

    if-eqz v6, :cond_3

    check-cast v5, Landroidx/media3/extractor/metadata/id3/PrivFrame;

    iget-object v6, v5, Landroidx/media3/extractor/metadata/id3/PrivFrame;->owner:Ljava/lang/String;

    const-string v7, "com.apple.streaming.transportStreamTimestamp"

    invoke-virtual {v7, v6}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v6

    if-eqz v6, :cond_3

    iget-object p1, v5, Landroidx/media3/extractor/metadata/id3/PrivFrame;->privateData:[B

    iget-object v0, p0, Landroidx/media3/exoplayer/hls/i;->z:Le2/c0;

    invoke-virtual {v0}, Le2/c0;->e()[B

    move-result-object v0

    const/16 v1, 0x8

    invoke-static {p1, v4, v0, v4, v1}, Ljava/lang/System;->arraycopy(Ljava/lang/Object;ILjava/lang/Object;II)V

    iget-object p1, p0, Landroidx/media3/exoplayer/hls/i;->z:Le2/c0;

    invoke-virtual {p1, v4}, Le2/c0;->U(I)V

    iget-object p1, p0, Landroidx/media3/exoplayer/hls/i;->z:Le2/c0;

    invoke-virtual {p1, v1}, Le2/c0;->T(I)V

    iget-object p1, p0, Landroidx/media3/exoplayer/hls/i;->z:Le2/c0;

    invoke-virtual {p1}, Le2/c0;->A()J

    move-result-wide v0

    const-wide v2, 0x1ffffffffL

    and-long/2addr v0, v2

    return-wide v0

    :cond_3
    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    :catch_0
    :cond_4
    return-wide v0
.end method

.method public final s(Landroidx/media3/datasource/a;Lh2/g;Z)Lz2/j;
    .locals 12
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    invoke-interface {p1, p2}, Landroidx/media3/datasource/a;->a(Lh2/g;)J

    move-result-wide v4

    if-eqz p3, :cond_0

    :try_start_0
    iget-object v6, p0, Landroidx/media3/exoplayer/hls/i;->u:Le2/i0;

    iget-boolean v7, p0, Landroidx/media3/exoplayer/hls/i;->s:Z

    iget-wide v8, p0, Lv2/e;->g:J

    iget-wide v10, p0, Landroidx/media3/exoplayer/hls/i;->D:J

    invoke-virtual/range {v6 .. v11}, Le2/i0;->j(ZJJ)V
    :try_end_0
    .catch Ljava/lang/InterruptedException; {:try_start_0 .. :try_end_0} :catch_1
    .catch Ljava/util/concurrent/TimeoutException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception p1

    new-instance p2, Ljava/io/IOException;

    invoke-direct {p2, p1}, Ljava/io/IOException;-><init>(Ljava/lang/Throwable;)V

    throw p2

    :catch_1
    new-instance p1, Ljava/io/InterruptedIOException;

    invoke-direct {p1}, Ljava/io/InterruptedIOException;-><init>()V

    throw p1

    :cond_0
    :goto_0
    new-instance p3, Lz2/j;

    iget-wide v2, p2, Lh2/g;->g:J

    move-object v0, p3

    move-object v1, p1

    invoke-direct/range {v0 .. v5}, Lz2/j;-><init>(Landroidx/media3/common/l;JJ)V

    iget-object v0, p0, Landroidx/media3/exoplayer/hls/i;->E:Landroidx/media3/exoplayer/hls/j;

    if-nez v0, :cond_4

    invoke-virtual {p0, p3}, Landroidx/media3/exoplayer/hls/i;->r(Lz2/t;)J

    move-result-wide v8

    invoke-virtual {p3}, Lz2/j;->resetPeekPosition()V

    iget-object v0, p0, Landroidx/media3/exoplayer/hls/i;->r:Landroidx/media3/exoplayer/hls/j;

    if-eqz v0, :cond_1

    invoke-interface {v0}, Landroidx/media3/exoplayer/hls/j;->f()Landroidx/media3/exoplayer/hls/j;

    move-result-object p1

    goto :goto_1

    :cond_1
    iget-object v0, p0, Landroidx/media3/exoplayer/hls/i;->v:Landroidx/media3/exoplayer/hls/g;

    iget-object v1, p2, Lh2/g;->a:Landroid/net/Uri;

    iget-object v2, p0, Lv2/e;->d:Landroidx/media3/common/y;

    iget-object v3, p0, Landroidx/media3/exoplayer/hls/i;->w:Ljava/util/List;

    iget-object v4, p0, Landroidx/media3/exoplayer/hls/i;->u:Le2/i0;

    invoke-interface {p1}, Landroidx/media3/datasource/a;->getResponseHeaders()Ljava/util/Map;

    move-result-object v5

    iget-object v7, p0, Landroidx/media3/exoplayer/hls/i;->C:Lj2/x3;

    move-object v6, p3

    invoke-interface/range {v0 .. v7}, Landroidx/media3/exoplayer/hls/g;->d(Landroid/net/Uri;Landroidx/media3/common/y;Ljava/util/List;Le2/i0;Ljava/util/Map;Lz2/t;Lj2/x3;)Landroidx/media3/exoplayer/hls/j;

    move-result-object p1

    :goto_1
    iput-object p1, p0, Landroidx/media3/exoplayer/hls/i;->E:Landroidx/media3/exoplayer/hls/j;

    invoke-interface {p1}, Landroidx/media3/exoplayer/hls/j;->e()Z

    move-result p1

    if-eqz p1, :cond_3

    iget-object p1, p0, Landroidx/media3/exoplayer/hls/i;->F:Landroidx/media3/exoplayer/hls/q;

    const-wide v0, -0x7fffffffffffffffL    # -4.9E-324

    cmp-long p2, v8, v0

    if-eqz p2, :cond_2

    iget-object p2, p0, Landroidx/media3/exoplayer/hls/i;->u:Le2/i0;

    invoke-virtual {p2, v8, v9}, Le2/i0;->b(J)J

    move-result-wide v0

    goto :goto_2

    :cond_2
    iget-wide v0, p0, Lv2/e;->g:J

    :goto_2
    invoke-virtual {p1, v0, v1}, Landroidx/media3/exoplayer/hls/q;->b0(J)V

    goto :goto_3

    :cond_3
    iget-object p1, p0, Landroidx/media3/exoplayer/hls/i;->F:Landroidx/media3/exoplayer/hls/q;

    const-wide/16 v0, 0x0

    invoke-virtual {p1, v0, v1}, Landroidx/media3/exoplayer/hls/q;->b0(J)V

    :goto_3
    iget-object p1, p0, Landroidx/media3/exoplayer/hls/i;->F:Landroidx/media3/exoplayer/hls/q;

    invoke-virtual {p1}, Landroidx/media3/exoplayer/hls/q;->N()V

    iget-object p1, p0, Landroidx/media3/exoplayer/hls/i;->E:Landroidx/media3/exoplayer/hls/j;

    iget-object p2, p0, Landroidx/media3/exoplayer/hls/i;->F:Landroidx/media3/exoplayer/hls/q;

    invoke-interface {p1, p2}, Landroidx/media3/exoplayer/hls/j;->c(Lz2/u;)V

    :cond_4
    iget-object p1, p0, Landroidx/media3/exoplayer/hls/i;->F:Landroidx/media3/exoplayer/hls/q;

    iget-object p2, p0, Landroidx/media3/exoplayer/hls/i;->x:Landroidx/media3/common/DrmInitData;

    invoke-virtual {p1, p2}, Landroidx/media3/exoplayer/hls/q;->Y(Landroidx/media3/common/DrmInitData;)V

    return-object p3
.end method

.method public t()V
    .locals 1

    const/4 v0, 0x1

    iput-boolean v0, p0, Landroidx/media3/exoplayer/hls/i;->M:Z

    return-void
.end method
