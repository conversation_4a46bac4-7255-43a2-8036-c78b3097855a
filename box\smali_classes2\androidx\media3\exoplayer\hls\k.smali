.class public final synthetic Landroidx/media3/exoplayer/hls/k;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/google/common/base/f;


# direct methods
.method public synthetic constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final apply(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    check-cast p1, Landroidx/media3/exoplayer/hls/q;

    invoke-static {p1}, Landroidx/media3/exoplayer/hls/l;->g(Landroidx/media3/exoplayer/hls/q;)Ljava/util/List;

    move-result-object p1

    return-object p1
.end method
