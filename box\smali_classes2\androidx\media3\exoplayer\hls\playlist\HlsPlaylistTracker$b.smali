.class public interface abstract Landroidx/media3/exoplayer/hls/playlist/HlsPlaylistTracker$b;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/media3/exoplayer/hls/playlist/HlsPlaylistTracker;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "b"
.end annotation


# virtual methods
.method public abstract c()V
.end method

.method public abstract d(Landroid/net/Uri;Landroidx/media3/exoplayer/upstream/m$c;Z)Z
.end method
