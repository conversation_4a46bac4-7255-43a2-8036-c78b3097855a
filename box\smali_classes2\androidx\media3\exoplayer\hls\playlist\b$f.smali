.class public final Landroidx/media3/exoplayer/hls/playlist/b$f;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/media3/exoplayer/hls/playlist/b;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "f"
.end annotation


# instance fields
.field public final a:J

.field public final b:Z

.field public final c:J

.field public final d:J

.field public final e:Z


# direct methods
.method public constructor <init>(JZJJZ)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-wide p1, p0, Landroidx/media3/exoplayer/hls/playlist/b$f;->a:J

    iput-boolean p3, p0, Landroidx/media3/exoplayer/hls/playlist/b$f;->b:Z

    iput-wide p4, p0, Landroidx/media3/exoplayer/hls/playlist/b$f;->c:J

    iput-wide p6, p0, Landroidx/media3/exoplayer/hls/playlist/b$f;->d:J

    iput-boolean p8, p0, Landroidx/media3/exoplayer/hls/playlist/b$f;->e:Z

    return-void
.end method
