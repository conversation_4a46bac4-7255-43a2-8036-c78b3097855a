.class public final Landroidx/media3/exoplayer/hls/q;
.super Ljava/lang/Object;

# interfaces
.implements Landroidx/media3/exoplayer/upstream/Loader$b;
.implements Landroidx/media3/exoplayer/upstream/Loader$e;
.implements Landroidx/media3/exoplayer/source/t;
.implements Lz2/u;
.implements Landroidx/media3/exoplayer/source/s$d;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Landroidx/media3/exoplayer/hls/q$b;,
        Landroidx/media3/exoplayer/hls/q$d;,
        Landroidx/media3/exoplayer/hls/q$c;
    }
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Landroidx/media3/exoplayer/upstream/Loader$b<",
        "Lv2/e;",
        ">;",
        "Landroidx/media3/exoplayer/upstream/Loader$e;",
        "Landroidx/media3/exoplayer/source/t;",
        "Lz2/u;",
        "Landroidx/media3/exoplayer/source/s$d;"
    }
.end annotation


# static fields
.field public static final Y:Ljava/util/Set;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Set<",
            "Ljava/lang/Integer;",
            ">;"
        }
    .end annotation
.end field


# instance fields
.field public A:I

.field public B:I

.field public C:Z

.field public D:Z

.field public E:I

.field public F:Landroidx/media3/common/y;

.field public G:Landroidx/media3/common/y;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public H:Z

.field public I:Lu2/k0;

.field public J:Ljava/util/Set;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Set<",
            "Landroidx/media3/common/n0;",
            ">;"
        }
    .end annotation
.end field

.field public K:[I

.field public L:I

.field public M:Z

.field public N:[Z

.field public O:[Z

.field public P:J

.field public Q:J

.field public R:Z

.field public S:Z

.field public T:Z

.field public U:Z

.field public V:J

.field public W:Landroidx/media3/common/DrmInitData;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public X:Landroidx/media3/exoplayer/hls/i;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public final a:Ljava/lang/String;

.field public final b:I

.field public final c:Landroidx/media3/exoplayer/hls/q$b;

.field public final d:Landroidx/media3/exoplayer/hls/e;

.field public final e:Landroidx/media3/exoplayer/upstream/b;

.field public final f:Landroidx/media3/common/y;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public final g:Landroidx/media3/exoplayer/drm/c;

.field public final h:Landroidx/media3/exoplayer/drm/b$a;

.field public final i:Landroidx/media3/exoplayer/upstream/m;

.field public final j:Landroidx/media3/exoplayer/upstream/Loader;

.field public final k:Landroidx/media3/exoplayer/source/m$a;

.field public final l:I

.field public final m:Landroidx/media3/exoplayer/hls/e$b;

.field public final n:Ljava/util/ArrayList;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/ArrayList<",
            "Landroidx/media3/exoplayer/hls/i;",
            ">;"
        }
    .end annotation
.end field

.field public final o:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Landroidx/media3/exoplayer/hls/i;",
            ">;"
        }
    .end annotation
.end field

.field public final p:Ljava/lang/Runnable;

.field public final q:Ljava/lang/Runnable;

.field public final r:Landroid/os/Handler;

.field public final s:Ljava/util/ArrayList;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/ArrayList<",
            "Landroidx/media3/exoplayer/hls/m;",
            ">;"
        }
    .end annotation
.end field

.field public final t:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Landroidx/media3/common/DrmInitData;",
            ">;"
        }
    .end annotation
.end field

.field public u:Lv2/e;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public v:[Landroidx/media3/exoplayer/hls/q$d;

.field public w:[I

.field public x:Ljava/util/Set;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Set<",
            "Ljava/lang/Integer;",
            ">;"
        }
    .end annotation
.end field

.field public y:Landroid/util/SparseIntArray;

.field public z:Lz2/r0;


# direct methods
.method static constructor <clinit>()V
    .locals 5

    new-instance v0, Ljava/util/HashSet;

    const/4 v1, 0x3

    new-array v1, v1, [Ljava/lang/Integer;

    const/4 v2, 0x1

    invoke-static {v2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v3

    const/4 v4, 0x0

    aput-object v3, v1, v4

    const/4 v3, 0x2

    invoke-static {v3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v4

    aput-object v4, v1, v2

    const/4 v2, 0x5

    invoke-static {v2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v2

    aput-object v2, v1, v3

    invoke-static {v1}, Ljava/util/Arrays;->asList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v1

    invoke-direct {v0, v1}, Ljava/util/HashSet;-><init>(Ljava/util/Collection;)V

    invoke-static {v0}, Ljava/util/Collections;->unmodifiableSet(Ljava/util/Set;)Ljava/util/Set;

    move-result-object v0

    sput-object v0, Landroidx/media3/exoplayer/hls/q;->Y:Ljava/util/Set;

    return-void
.end method

.method public constructor <init>(Ljava/lang/String;ILandroidx/media3/exoplayer/hls/q$b;Landroidx/media3/exoplayer/hls/e;Ljava/util/Map;Landroidx/media3/exoplayer/upstream/b;JLandroidx/media3/common/y;Landroidx/media3/exoplayer/drm/c;Landroidx/media3/exoplayer/drm/b$a;Landroidx/media3/exoplayer/upstream/m;Landroidx/media3/exoplayer/source/m$a;I)V
    .locals 0
    .param p9    # Landroidx/media3/common/y;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "I",
            "Landroidx/media3/exoplayer/hls/q$b;",
            "Landroidx/media3/exoplayer/hls/e;",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Landroidx/media3/common/DrmInitData;",
            ">;",
            "Landroidx/media3/exoplayer/upstream/b;",
            "J",
            "Landroidx/media3/common/y;",
            "Landroidx/media3/exoplayer/drm/c;",
            "Landroidx/media3/exoplayer/drm/b$a;",
            "Landroidx/media3/exoplayer/upstream/m;",
            "Landroidx/media3/exoplayer/source/m$a;",
            "I)V"
        }
    .end annotation

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Landroidx/media3/exoplayer/hls/q;->a:Ljava/lang/String;

    iput p2, p0, Landroidx/media3/exoplayer/hls/q;->b:I

    iput-object p3, p0, Landroidx/media3/exoplayer/hls/q;->c:Landroidx/media3/exoplayer/hls/q$b;

    iput-object p4, p0, Landroidx/media3/exoplayer/hls/q;->d:Landroidx/media3/exoplayer/hls/e;

    iput-object p5, p0, Landroidx/media3/exoplayer/hls/q;->t:Ljava/util/Map;

    iput-object p6, p0, Landroidx/media3/exoplayer/hls/q;->e:Landroidx/media3/exoplayer/upstream/b;

    iput-object p9, p0, Landroidx/media3/exoplayer/hls/q;->f:Landroidx/media3/common/y;

    iput-object p10, p0, Landroidx/media3/exoplayer/hls/q;->g:Landroidx/media3/exoplayer/drm/c;

    iput-object p11, p0, Landroidx/media3/exoplayer/hls/q;->h:Landroidx/media3/exoplayer/drm/b$a;

    iput-object p12, p0, Landroidx/media3/exoplayer/hls/q;->i:Landroidx/media3/exoplayer/upstream/m;

    iput-object p13, p0, Landroidx/media3/exoplayer/hls/q;->k:Landroidx/media3/exoplayer/source/m$a;

    iput p14, p0, Landroidx/media3/exoplayer/hls/q;->l:I

    new-instance p1, Landroidx/media3/exoplayer/upstream/Loader;

    const-string p2, "Loader:HlsSampleStreamWrapper"

    invoke-direct {p1, p2}, Landroidx/media3/exoplayer/upstream/Loader;-><init>(Ljava/lang/String;)V

    iput-object p1, p0, Landroidx/media3/exoplayer/hls/q;->j:Landroidx/media3/exoplayer/upstream/Loader;

    new-instance p1, Landroidx/media3/exoplayer/hls/e$b;

    invoke-direct {p1}, Landroidx/media3/exoplayer/hls/e$b;-><init>()V

    iput-object p1, p0, Landroidx/media3/exoplayer/hls/q;->m:Landroidx/media3/exoplayer/hls/e$b;

    const/4 p1, 0x0

    new-array p2, p1, [I

    iput-object p2, p0, Landroidx/media3/exoplayer/hls/q;->w:[I

    new-instance p2, Ljava/util/HashSet;

    sget-object p3, Landroidx/media3/exoplayer/hls/q;->Y:Ljava/util/Set;

    invoke-interface {p3}, Ljava/util/Set;->size()I

    move-result p4

    invoke-direct {p2, p4}, Ljava/util/HashSet;-><init>(I)V

    iput-object p2, p0, Landroidx/media3/exoplayer/hls/q;->x:Ljava/util/Set;

    new-instance p2, Landroid/util/SparseIntArray;

    invoke-interface {p3}, Ljava/util/Set;->size()I

    move-result p3

    invoke-direct {p2, p3}, Landroid/util/SparseIntArray;-><init>(I)V

    iput-object p2, p0, Landroidx/media3/exoplayer/hls/q;->y:Landroid/util/SparseIntArray;

    new-array p2, p1, [Landroidx/media3/exoplayer/hls/q$d;

    iput-object p2, p0, Landroidx/media3/exoplayer/hls/q;->v:[Landroidx/media3/exoplayer/hls/q$d;

    new-array p2, p1, [Z

    iput-object p2, p0, Landroidx/media3/exoplayer/hls/q;->O:[Z

    new-array p1, p1, [Z

    iput-object p1, p0, Landroidx/media3/exoplayer/hls/q;->N:[Z

    new-instance p1, Ljava/util/ArrayList;

    invoke-direct {p1}, Ljava/util/ArrayList;-><init>()V

    iput-object p1, p0, Landroidx/media3/exoplayer/hls/q;->n:Ljava/util/ArrayList;

    invoke-static {p1}, Ljava/util/Collections;->unmodifiableList(Ljava/util/List;)Ljava/util/List;

    move-result-object p1

    iput-object p1, p0, Landroidx/media3/exoplayer/hls/q;->o:Ljava/util/List;

    new-instance p1, Ljava/util/ArrayList;

    invoke-direct {p1}, Ljava/util/ArrayList;-><init>()V

    iput-object p1, p0, Landroidx/media3/exoplayer/hls/q;->s:Ljava/util/ArrayList;

    new-instance p1, Landroidx/media3/exoplayer/hls/o;

    invoke-direct {p1, p0}, Landroidx/media3/exoplayer/hls/o;-><init>(Landroidx/media3/exoplayer/hls/q;)V

    iput-object p1, p0, Landroidx/media3/exoplayer/hls/q;->p:Ljava/lang/Runnable;

    new-instance p1, Landroidx/media3/exoplayer/hls/p;

    invoke-direct {p1, p0}, Landroidx/media3/exoplayer/hls/p;-><init>(Landroidx/media3/exoplayer/hls/q;)V

    iput-object p1, p0, Landroidx/media3/exoplayer/hls/q;->q:Ljava/lang/Runnable;

    invoke-static {}, Le2/u0;->A()Landroid/os/Handler;

    move-result-object p1

    iput-object p1, p0, Landroidx/media3/exoplayer/hls/q;->r:Landroid/os/Handler;

    iput-wide p7, p0, Landroidx/media3/exoplayer/hls/q;->P:J

    iput-wide p7, p0, Landroidx/media3/exoplayer/hls/q;->Q:J

    return-void
.end method

.method public static A(I)I
    .locals 3

    const/4 v0, 0x2

    const/4 v1, 0x1

    if-eq p0, v1, :cond_2

    const/4 v2, 0x3

    if-eq p0, v0, :cond_1

    if-eq p0, v2, :cond_0

    const/4 p0, 0x0

    return p0

    :cond_0
    return v1

    :cond_1
    return v2

    :cond_2
    return v0
.end method

.method public static C(Lv2/e;)Z
    .locals 0

    instance-of p0, p0, Landroidx/media3/exoplayer/hls/i;

    return p0
.end method

.method private D()Z
    .locals 5

    iget-wide v0, p0, Landroidx/media3/exoplayer/hls/q;->Q:J

    const-wide v2, -0x7fffffffffffffffL    # -4.9E-324

    cmp-long v4, v0, v2

    if-eqz v4, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method public static synthetic d(Landroidx/media3/exoplayer/hls/q;)V
    .locals 0

    invoke-virtual {p0}, Landroidx/media3/exoplayer/hls/q;->Q()V

    return-void
.end method

.method public static synthetic h(Landroidx/media3/exoplayer/hls/q;)V
    .locals 0

    invoke-virtual {p0}, Landroidx/media3/exoplayer/hls/q;->H()V

    return-void
.end method

.method public static q(II)Lz2/q;
    .locals 2

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "Unmapped track with id "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0, p0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string p0, " of type "

    invoke-virtual {v0, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    const-string p1, "HlsSampleStreamWrapper"

    invoke-static {p1, p0}, Le2/o;->h(Ljava/lang/String;Ljava/lang/String;)V

    new-instance p0, Lz2/q;

    invoke-direct {p0}, Lz2/q;-><init>()V

    return-object p0
.end method

.method public static t(Landroidx/media3/common/y;Landroidx/media3/common/y;Z)Landroidx/media3/common/y;
    .locals 7
    .param p0    # Landroidx/media3/common/y;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    if-nez p0, :cond_0

    return-object p1

    :cond_0
    iget-object v0, p1, Landroidx/media3/common/y;->m:Ljava/lang/String;

    invoke-static {v0}, Landroidx/media3/common/f0;->k(Ljava/lang/String;)I

    move-result v0

    iget-object v1, p0, Landroidx/media3/common/y;->j:Ljava/lang/String;

    invoke-static {v1, v0}, Le2/u0;->P(Ljava/lang/String;I)I

    move-result v1

    const/4 v2, 0x1

    if-ne v1, v2, :cond_1

    iget-object v1, p0, Landroidx/media3/common/y;->j:Ljava/lang/String;

    invoke-static {v1, v0}, Le2/u0;->Q(Ljava/lang/String;I)Ljava/lang/String;

    move-result-object v1

    invoke-static {v1}, Landroidx/media3/common/f0;->g(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v3

    goto :goto_0

    :cond_1
    iget-object v1, p0, Landroidx/media3/common/y;->j:Ljava/lang/String;

    iget-object v3, p1, Landroidx/media3/common/y;->m:Ljava/lang/String;

    invoke-static {v1, v3}, Landroidx/media3/common/f0;->d(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v1

    iget-object v3, p1, Landroidx/media3/common/y;->m:Ljava/lang/String;

    :goto_0
    invoke-virtual {p1}, Landroidx/media3/common/y;->b()Landroidx/media3/common/y$b;

    move-result-object v4

    iget-object v5, p0, Landroidx/media3/common/y;->a:Ljava/lang/String;

    invoke-virtual {v4, v5}, Landroidx/media3/common/y$b;->X(Ljava/lang/String;)Landroidx/media3/common/y$b;

    move-result-object v4

    iget-object v5, p0, Landroidx/media3/common/y;->b:Ljava/lang/String;

    invoke-virtual {v4, v5}, Landroidx/media3/common/y$b;->Z(Ljava/lang/String;)Landroidx/media3/common/y$b;

    move-result-object v4

    iget-object v5, p0, Landroidx/media3/common/y;->c:Ljava/util/List;

    invoke-virtual {v4, v5}, Landroidx/media3/common/y$b;->a0(Ljava/util/List;)Landroidx/media3/common/y$b;

    move-result-object v4

    iget-object v5, p0, Landroidx/media3/common/y;->d:Ljava/lang/String;

    invoke-virtual {v4, v5}, Landroidx/media3/common/y$b;->b0(Ljava/lang/String;)Landroidx/media3/common/y$b;

    move-result-object v4

    iget v5, p0, Landroidx/media3/common/y;->e:I

    invoke-virtual {v4, v5}, Landroidx/media3/common/y$b;->m0(I)Landroidx/media3/common/y$b;

    move-result-object v4

    iget v5, p0, Landroidx/media3/common/y;->f:I

    invoke-virtual {v4, v5}, Landroidx/media3/common/y$b;->i0(I)Landroidx/media3/common/y$b;

    move-result-object v4

    const/4 v5, -0x1

    if-eqz p2, :cond_2

    iget v6, p0, Landroidx/media3/common/y;->g:I

    goto :goto_1

    :cond_2
    const/4 v6, -0x1

    :goto_1
    invoke-virtual {v4, v6}, Landroidx/media3/common/y$b;->K(I)Landroidx/media3/common/y$b;

    move-result-object v4

    if-eqz p2, :cond_3

    iget p2, p0, Landroidx/media3/common/y;->h:I

    goto :goto_2

    :cond_3
    const/4 p2, -0x1

    :goto_2
    invoke-virtual {v4, p2}, Landroidx/media3/common/y$b;->f0(I)Landroidx/media3/common/y$b;

    move-result-object p2

    invoke-virtual {p2, v1}, Landroidx/media3/common/y$b;->M(Ljava/lang/String;)Landroidx/media3/common/y$b;

    move-result-object p2

    const/4 v1, 0x2

    if-ne v0, v1, :cond_4

    iget v1, p0, Landroidx/media3/common/y;->r:I

    invoke-virtual {p2, v1}, Landroidx/media3/common/y$b;->r0(I)Landroidx/media3/common/y$b;

    move-result-object v1

    iget v4, p0, Landroidx/media3/common/y;->s:I

    invoke-virtual {v1, v4}, Landroidx/media3/common/y$b;->V(I)Landroidx/media3/common/y$b;

    move-result-object v1

    iget v4, p0, Landroidx/media3/common/y;->t:F

    invoke-virtual {v1, v4}, Landroidx/media3/common/y$b;->U(F)Landroidx/media3/common/y$b;

    :cond_4
    if-eqz v3, :cond_5

    invoke-virtual {p2, v3}, Landroidx/media3/common/y$b;->k0(Ljava/lang/String;)Landroidx/media3/common/y$b;

    :cond_5
    iget v1, p0, Landroidx/media3/common/y;->z:I

    if-eq v1, v5, :cond_6

    if-ne v0, v2, :cond_6

    invoke-virtual {p2, v1}, Landroidx/media3/common/y$b;->L(I)Landroidx/media3/common/y$b;

    :cond_6
    iget-object p0, p0, Landroidx/media3/common/y;->k:Landroidx/media3/common/Metadata;

    if-eqz p0, :cond_8

    iget-object p1, p1, Landroidx/media3/common/y;->k:Landroidx/media3/common/Metadata;

    if-eqz p1, :cond_7

    invoke-virtual {p1, p0}, Landroidx/media3/common/Metadata;->c(Landroidx/media3/common/Metadata;)Landroidx/media3/common/Metadata;

    move-result-object p0

    :cond_7
    invoke-virtual {p2, p0}, Landroidx/media3/common/y$b;->d0(Landroidx/media3/common/Metadata;)Landroidx/media3/common/y$b;

    :cond_8
    invoke-virtual {p2}, Landroidx/media3/common/y$b;->I()Landroidx/media3/common/y;

    move-result-object p0

    return-object p0
.end method

.method public static x(Landroidx/media3/common/y;Landroidx/media3/common/y;)Z
    .locals 6

    iget-object v0, p0, Landroidx/media3/common/y;->m:Ljava/lang/String;

    iget-object v1, p1, Landroidx/media3/common/y;->m:Ljava/lang/String;

    invoke-static {v0}, Landroidx/media3/common/f0;->k(Ljava/lang/String;)I

    move-result v2

    const/4 v3, 0x3

    const/4 v4, 0x0

    const/4 v5, 0x1

    if-eq v2, v3, :cond_1

    invoke-static {v1}, Landroidx/media3/common/f0;->k(Ljava/lang/String;)I

    move-result p0

    if-ne v2, p0, :cond_0

    const/4 v4, 0x1

    :cond_0
    return v4

    :cond_1
    invoke-static {v0, v1}, Le2/u0;->c(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_2

    return v4

    :cond_2
    const-string v1, "application/cea-608"

    invoke-virtual {v1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_4

    const-string v1, "application/cea-708"

    invoke-virtual {v1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_3

    goto :goto_0

    :cond_3
    return v5

    :cond_4
    :goto_0
    iget p0, p0, Landroidx/media3/common/y;->E:I

    iget p1, p1, Landroidx/media3/common/y;->E:I

    if-ne p0, p1, :cond_5

    const/4 v4, 0x1

    :cond_5
    return v4
.end method


# virtual methods
.method public final B(Landroidx/media3/exoplayer/hls/i;)V
    .locals 6

    iput-object p1, p0, Landroidx/media3/exoplayer/hls/q;->X:Landroidx/media3/exoplayer/hls/i;

    iget-object v0, p1, Lv2/e;->d:Landroidx/media3/common/y;

    iput-object v0, p0, Landroidx/media3/exoplayer/hls/q;->F:Landroidx/media3/common/y;

    const-wide v0, -0x7fffffffffffffffL    # -4.9E-324

    iput-wide v0, p0, Landroidx/media3/exoplayer/hls/q;->Q:J

    iget-object v0, p0, Landroidx/media3/exoplayer/hls/q;->n:Ljava/util/ArrayList;

    invoke-virtual {v0, p1}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    invoke-static {}, Lcom/google/common/collect/ImmutableList;->builder()Lcom/google/common/collect/ImmutableList$a;

    move-result-object v0

    iget-object v1, p0, Landroidx/media3/exoplayer/hls/q;->v:[Landroidx/media3/exoplayer/hls/q$d;

    array-length v2, v1

    const/4 v3, 0x0

    const/4 v4, 0x0

    :goto_0
    if-ge v4, v2, :cond_0

    aget-object v5, v1, v4

    invoke-virtual {v5}, Landroidx/media3/exoplayer/source/s;->H()I

    move-result v5

    invoke-static {v5}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v5

    invoke-virtual {v0, v5}, Lcom/google/common/collect/ImmutableList$a;->i(Ljava/lang/Object;)Lcom/google/common/collect/ImmutableList$a;

    add-int/lit8 v4, v4, 0x1

    goto :goto_0

    :cond_0
    invoke-virtual {v0}, Lcom/google/common/collect/ImmutableList$a;->m()Lcom/google/common/collect/ImmutableList;

    move-result-object v0

    invoke-virtual {p1, p0, v0}, Landroidx/media3/exoplayer/hls/i;->l(Landroidx/media3/exoplayer/hls/q;Lcom/google/common/collect/ImmutableList;)V

    iget-object v0, p0, Landroidx/media3/exoplayer/hls/q;->v:[Landroidx/media3/exoplayer/hls/q$d;

    array-length v1, v0

    :goto_1
    if-ge v3, v1, :cond_2

    aget-object v2, v0, v3

    invoke-virtual {v2, p1}, Landroidx/media3/exoplayer/hls/q$d;->k0(Landroidx/media3/exoplayer/hls/i;)V

    iget-boolean v4, p1, Landroidx/media3/exoplayer/hls/i;->n:Z

    if-eqz v4, :cond_1

    invoke-virtual {v2}, Landroidx/media3/exoplayer/source/s;->h0()V

    :cond_1
    add-int/lit8 v3, v3, 0x1

    goto :goto_1

    :cond_2
    return-void
.end method

.method public E(I)Z
    .locals 1

    invoke-direct {p0}, Landroidx/media3/exoplayer/hls/q;->D()Z

    move-result v0

    if-nez v0, :cond_0

    iget-object v0, p0, Landroidx/media3/exoplayer/hls/q;->v:[Landroidx/media3/exoplayer/hls/q$d;

    aget-object p1, v0, p1

    iget-boolean v0, p0, Landroidx/media3/exoplayer/hls/q;->T:Z

    invoke-virtual {p1, v0}, Landroidx/media3/exoplayer/source/s;->L(Z)Z

    move-result p1

    if-eqz p1, :cond_0

    const/4 p1, 0x1

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    :goto_0
    return p1
.end method

.method public F()Z
    .locals 2

    iget v0, p0, Landroidx/media3/exoplayer/hls/q;->A:I

    const/4 v1, 0x2

    if-ne v0, v1, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method public final G()V
    .locals 6

    iget-object v0, p0, Landroidx/media3/exoplayer/hls/q;->I:Lu2/k0;

    iget v0, v0, Lu2/k0;->a:I

    new-array v1, v0, [I

    iput-object v1, p0, Landroidx/media3/exoplayer/hls/q;->K:[I

    const/4 v2, -0x1

    invoke-static {v1, v2}, Ljava/util/Arrays;->fill([II)V

    const/4 v1, 0x0

    const/4 v2, 0x0

    :goto_0
    if-ge v2, v0, :cond_2

    const/4 v3, 0x0

    :goto_1
    iget-object v4, p0, Landroidx/media3/exoplayer/hls/q;->v:[Landroidx/media3/exoplayer/hls/q$d;

    array-length v5, v4

    if-ge v3, v5, :cond_1

    aget-object v4, v4, v3

    invoke-virtual {v4}, Landroidx/media3/exoplayer/source/s;->G()Landroidx/media3/common/y;

    move-result-object v4

    invoke-static {v4}, Le2/a;->i(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Landroidx/media3/common/y;

    iget-object v5, p0, Landroidx/media3/exoplayer/hls/q;->I:Lu2/k0;

    invoke-virtual {v5, v2}, Lu2/k0;->b(I)Landroidx/media3/common/n0;

    move-result-object v5

    invoke-virtual {v5, v1}, Landroidx/media3/common/n0;->a(I)Landroidx/media3/common/y;

    move-result-object v5

    invoke-static {v4, v5}, Landroidx/media3/exoplayer/hls/q;->x(Landroidx/media3/common/y;Landroidx/media3/common/y;)Z

    move-result v4

    if-eqz v4, :cond_0

    iget-object v4, p0, Landroidx/media3/exoplayer/hls/q;->K:[I

    aput v3, v4, v2

    goto :goto_2

    :cond_0
    add-int/lit8 v3, v3, 0x1

    goto :goto_1

    :cond_1
    :goto_2
    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_2
    iget-object v0, p0, Landroidx/media3/exoplayer/hls/q;->s:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_3
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_3

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Landroidx/media3/exoplayer/hls/m;

    invoke-virtual {v1}, Landroidx/media3/exoplayer/hls/m;->a()V

    goto :goto_3

    :cond_3
    return-void
.end method

.method public final H()V
    .locals 4

    iget-boolean v0, p0, Landroidx/media3/exoplayer/hls/q;->H:Z

    if-nez v0, :cond_4

    iget-object v0, p0, Landroidx/media3/exoplayer/hls/q;->K:[I

    if-nez v0, :cond_4

    iget-boolean v0, p0, Landroidx/media3/exoplayer/hls/q;->C:Z

    if-nez v0, :cond_0

    goto :goto_1

    :cond_0
    iget-object v0, p0, Landroidx/media3/exoplayer/hls/q;->v:[Landroidx/media3/exoplayer/hls/q$d;

    array-length v1, v0

    const/4 v2, 0x0

    :goto_0
    if-ge v2, v1, :cond_2

    aget-object v3, v0, v2

    invoke-virtual {v3}, Landroidx/media3/exoplayer/source/s;->G()Landroidx/media3/common/y;

    move-result-object v3

    if-nez v3, :cond_1

    return-void

    :cond_1
    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_2
    iget-object v0, p0, Landroidx/media3/exoplayer/hls/q;->I:Lu2/k0;

    if-eqz v0, :cond_3

    invoke-virtual {p0}, Landroidx/media3/exoplayer/hls/q;->G()V

    goto :goto_1

    :cond_3
    invoke-virtual {p0}, Landroidx/media3/exoplayer/hls/q;->l()V

    invoke-virtual {p0}, Landroidx/media3/exoplayer/hls/q;->Z()V

    iget-object v0, p0, Landroidx/media3/exoplayer/hls/q;->c:Landroidx/media3/exoplayer/hls/q$b;

    invoke-interface {v0}, Landroidx/media3/exoplayer/hls/q$b;->onPrepared()V

    :cond_4
    :goto_1
    return-void
.end method

.method public I()V
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget-object v0, p0, Landroidx/media3/exoplayer/hls/q;->j:Landroidx/media3/exoplayer/upstream/Loader;

    invoke-virtual {v0}, Landroidx/media3/exoplayer/upstream/Loader;->maybeThrowError()V

    iget-object v0, p0, Landroidx/media3/exoplayer/hls/q;->d:Landroidx/media3/exoplayer/hls/e;

    invoke-virtual {v0}, Landroidx/media3/exoplayer/hls/e;->o()V

    return-void
.end method

.method public J(I)V
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    invoke-virtual {p0}, Landroidx/media3/exoplayer/hls/q;->I()V

    iget-object v0, p0, Landroidx/media3/exoplayer/hls/q;->v:[Landroidx/media3/exoplayer/hls/q$d;

    aget-object p1, v0, p1

    invoke-virtual {p1}, Landroidx/media3/exoplayer/source/s;->O()V

    return-void
.end method

.method public K(Lv2/e;JJZ)V
    .locals 15

    move-object v0, p0

    move-object/from16 v1, p1

    const/4 v2, 0x0

    iput-object v2, v0, Landroidx/media3/exoplayer/hls/q;->u:Lv2/e;

    new-instance v2, Lu2/n;

    iget-wide v4, v1, Lv2/e;->a:J

    iget-object v6, v1, Lv2/e;->b:Lh2/g;

    invoke-virtual/range {p1 .. p1}, Lv2/e;->d()Landroid/net/Uri;

    move-result-object v7

    invoke-virtual/range {p1 .. p1}, Lv2/e;->c()Ljava/util/Map;

    move-result-object v8

    invoke-virtual/range {p1 .. p1}, Lv2/e;->a()J

    move-result-wide v13

    move-object v3, v2

    move-wide/from16 v9, p2

    move-wide/from16 v11, p4

    invoke-direct/range {v3 .. v14}, Lu2/n;-><init>(JLh2/g;Landroid/net/Uri;Ljava/util/Map;JJJ)V

    iget-object v3, v0, Landroidx/media3/exoplayer/hls/q;->i:Landroidx/media3/exoplayer/upstream/m;

    iget-wide v4, v1, Lv2/e;->a:J

    invoke-interface {v3, v4, v5}, Landroidx/media3/exoplayer/upstream/m;->b(J)V

    iget-object v3, v0, Landroidx/media3/exoplayer/hls/q;->k:Landroidx/media3/exoplayer/source/m$a;

    iget v5, v1, Lv2/e;->c:I

    iget v6, v0, Landroidx/media3/exoplayer/hls/q;->b:I

    iget-object v7, v1, Lv2/e;->d:Landroidx/media3/common/y;

    iget v8, v1, Lv2/e;->e:I

    iget-object v9, v1, Lv2/e;->f:Ljava/lang/Object;

    iget-wide v10, v1, Lv2/e;->g:J

    iget-wide v12, v1, Lv2/e;->h:J

    move-object v4, v2

    invoke-virtual/range {v3 .. v13}, Landroidx/media3/exoplayer/source/m$a;->q(Lu2/n;IILandroidx/media3/common/y;ILjava/lang/Object;JJ)V

    if-nez p6, :cond_2

    invoke-direct {p0}, Landroidx/media3/exoplayer/hls/q;->D()Z

    move-result v1

    if-nez v1, :cond_0

    iget v1, v0, Landroidx/media3/exoplayer/hls/q;->E:I

    if-nez v1, :cond_1

    :cond_0
    invoke-virtual {p0}, Landroidx/media3/exoplayer/hls/q;->U()V

    :cond_1
    iget v1, v0, Landroidx/media3/exoplayer/hls/q;->E:I

    if-lez v1, :cond_2

    iget-object v1, v0, Landroidx/media3/exoplayer/hls/q;->c:Landroidx/media3/exoplayer/hls/q$b;

    invoke-interface {v1, p0}, Landroidx/media3/exoplayer/source/t$a;->h(Landroidx/media3/exoplayer/source/t;)V

    :cond_2
    return-void
.end method

.method public L(Lv2/e;JJ)V
    .locals 15

    move-object v0, p0

    move-object/from16 v1, p1

    const/4 v2, 0x0

    iput-object v2, v0, Landroidx/media3/exoplayer/hls/q;->u:Lv2/e;

    iget-object v2, v0, Landroidx/media3/exoplayer/hls/q;->d:Landroidx/media3/exoplayer/hls/e;

    invoke-virtual {v2, v1}, Landroidx/media3/exoplayer/hls/e;->q(Lv2/e;)V

    new-instance v2, Lu2/n;

    iget-wide v4, v1, Lv2/e;->a:J

    iget-object v6, v1, Lv2/e;->b:Lh2/g;

    invoke-virtual/range {p1 .. p1}, Lv2/e;->d()Landroid/net/Uri;

    move-result-object v7

    invoke-virtual/range {p1 .. p1}, Lv2/e;->c()Ljava/util/Map;

    move-result-object v8

    invoke-virtual/range {p1 .. p1}, Lv2/e;->a()J

    move-result-wide v13

    move-object v3, v2

    move-wide/from16 v9, p2

    move-wide/from16 v11, p4

    invoke-direct/range {v3 .. v14}, Lu2/n;-><init>(JLh2/g;Landroid/net/Uri;Ljava/util/Map;JJJ)V

    iget-object v3, v0, Landroidx/media3/exoplayer/hls/q;->i:Landroidx/media3/exoplayer/upstream/m;

    iget-wide v4, v1, Lv2/e;->a:J

    invoke-interface {v3, v4, v5}, Landroidx/media3/exoplayer/upstream/m;->b(J)V

    iget-object v3, v0, Landroidx/media3/exoplayer/hls/q;->k:Landroidx/media3/exoplayer/source/m$a;

    iget v5, v1, Lv2/e;->c:I

    iget v6, v0, Landroidx/media3/exoplayer/hls/q;->b:I

    iget-object v7, v1, Lv2/e;->d:Landroidx/media3/common/y;

    iget v8, v1, Lv2/e;->e:I

    iget-object v9, v1, Lv2/e;->f:Ljava/lang/Object;

    iget-wide v10, v1, Lv2/e;->g:J

    iget-wide v12, v1, Lv2/e;->h:J

    move-object v4, v2

    invoke-virtual/range {v3 .. v13}, Landroidx/media3/exoplayer/source/m$a;->t(Lu2/n;IILandroidx/media3/common/y;ILjava/lang/Object;JJ)V

    iget-boolean v1, v0, Landroidx/media3/exoplayer/hls/q;->D:Z

    if-nez v1, :cond_0

    new-instance v1, Landroidx/media3/exoplayer/w1$b;

    invoke-direct {v1}, Landroidx/media3/exoplayer/w1$b;-><init>()V

    iget-wide v2, v0, Landroidx/media3/exoplayer/hls/q;->P:J

    invoke-virtual {v1, v2, v3}, Landroidx/media3/exoplayer/w1$b;->f(J)Landroidx/media3/exoplayer/w1$b;

    move-result-object v1

    invoke-virtual {v1}, Landroidx/media3/exoplayer/w1$b;->d()Landroidx/media3/exoplayer/w1;

    move-result-object v1

    invoke-virtual {p0, v1}, Landroidx/media3/exoplayer/hls/q;->a(Landroidx/media3/exoplayer/w1;)Z

    goto :goto_0

    :cond_0
    iget-object v1, v0, Landroidx/media3/exoplayer/hls/q;->c:Landroidx/media3/exoplayer/hls/q$b;

    invoke-interface {v1, p0}, Landroidx/media3/exoplayer/source/t$a;->h(Landroidx/media3/exoplayer/source/t;)V

    :goto_0
    return-void
.end method

.method public M(Lv2/e;JJLjava/io/IOException;I)Landroidx/media3/exoplayer/upstream/Loader$c;
    .locals 30

    move-object/from16 v0, p0

    move-object/from16 v1, p1

    move-object/from16 v13, p6

    invoke-static/range {p1 .. p1}, Landroidx/media3/exoplayer/hls/q;->C(Lv2/e;)Z

    move-result v2

    if-eqz v2, :cond_1

    move-object v3, v1

    check-cast v3, Landroidx/media3/exoplayer/hls/i;

    invoke-virtual {v3}, Landroidx/media3/exoplayer/hls/i;->o()Z

    move-result v3

    if-nez v3, :cond_1

    instance-of v3, v13, Landroidx/media3/datasource/HttpDataSource$InvalidResponseCodeException;

    if-eqz v3, :cond_1

    move-object v3, v13

    check-cast v3, Landroidx/media3/datasource/HttpDataSource$InvalidResponseCodeException;

    iget v3, v3, Landroidx/media3/datasource/HttpDataSource$InvalidResponseCodeException;->responseCode:I

    const/16 v4, 0x19a

    if-eq v3, v4, :cond_0

    const/16 v4, 0x194

    if-ne v3, v4, :cond_1

    :cond_0
    sget-object v1, Landroidx/media3/exoplayer/upstream/Loader;->d:Landroidx/media3/exoplayer/upstream/Loader$c;

    return-object v1

    :cond_1
    invoke-virtual/range {p1 .. p1}, Lv2/e;->a()J

    move-result-wide v3

    new-instance v5, Lu2/n;

    iget-wide v6, v1, Lv2/e;->a:J

    iget-object v8, v1, Lv2/e;->b:Lh2/g;

    invoke-virtual/range {p1 .. p1}, Lv2/e;->d()Landroid/net/Uri;

    move-result-object v18

    invoke-virtual/range {p1 .. p1}, Lv2/e;->c()Ljava/util/Map;

    move-result-object v19

    move-object v14, v5

    move-wide v15, v6

    move-object/from16 v17, v8

    move-wide/from16 v20, p2

    move-wide/from16 v22, p4

    move-wide/from16 v24, v3

    invoke-direct/range {v14 .. v25}, Lu2/n;-><init>(JLh2/g;Landroid/net/Uri;Ljava/util/Map;JJJ)V

    new-instance v6, Lu2/o;

    iget v7, v1, Lv2/e;->c:I

    iget v8, v0, Landroidx/media3/exoplayer/hls/q;->b:I

    iget-object v9, v1, Lv2/e;->d:Landroidx/media3/common/y;

    iget v10, v1, Lv2/e;->e:I

    iget-object v11, v1, Lv2/e;->f:Ljava/lang/Object;

    iget-wide v14, v1, Lv2/e;->g:J

    invoke-static {v14, v15}, Le2/u0;->B1(J)J

    move-result-wide v26

    iget-wide v14, v1, Lv2/e;->h:J

    invoke-static {v14, v15}, Le2/u0;->B1(J)J

    move-result-wide v28

    move-object/from16 v20, v6

    move/from16 v21, v7

    move/from16 v22, v8

    move-object/from16 v23, v9

    move/from16 v24, v10

    move-object/from16 v25, v11

    invoke-direct/range {v20 .. v29}, Lu2/o;-><init>(IILandroidx/media3/common/y;ILjava/lang/Object;JJ)V

    new-instance v7, Landroidx/media3/exoplayer/upstream/m$c;

    move/from16 v8, p7

    invoke-direct {v7, v5, v6, v13, v8}, Landroidx/media3/exoplayer/upstream/m$c;-><init>(Lu2/n;Lu2/o;Ljava/io/IOException;I)V

    iget-object v6, v0, Landroidx/media3/exoplayer/hls/q;->i:Landroidx/media3/exoplayer/upstream/m;

    iget-object v8, v0, Landroidx/media3/exoplayer/hls/q;->d:Landroidx/media3/exoplayer/hls/e;

    invoke-virtual {v8}, Landroidx/media3/exoplayer/hls/e;->k()Lx2/z;

    move-result-object v8

    invoke-static {v8}, Lx2/d0;->c(Lx2/z;)Landroidx/media3/exoplayer/upstream/m$a;

    move-result-object v8

    invoke-interface {v6, v8, v7}, Landroidx/media3/exoplayer/upstream/m;->d(Landroidx/media3/exoplayer/upstream/m$a;Landroidx/media3/exoplayer/upstream/m$c;)Landroidx/media3/exoplayer/upstream/m$b;

    move-result-object v6

    const/4 v8, 0x0

    if-eqz v6, :cond_2

    iget v9, v6, Landroidx/media3/exoplayer/upstream/m$b;->a:I

    const/4 v10, 0x2

    if-ne v9, v10, :cond_2

    iget-object v9, v0, Landroidx/media3/exoplayer/hls/q;->d:Landroidx/media3/exoplayer/hls/e;

    iget-wide v10, v6, Landroidx/media3/exoplayer/upstream/m$b;->b:J

    invoke-virtual {v9, v1, v10, v11}, Landroidx/media3/exoplayer/hls/e;->n(Lv2/e;J)Z

    move-result v6

    move v15, v6

    goto :goto_0

    :cond_2
    const/4 v15, 0x0

    :goto_0
    const/4 v6, 0x1

    if-eqz v15, :cond_6

    if-eqz v2, :cond_5

    const-wide/16 v9, 0x0

    cmp-long v2, v3, v9

    if-nez v2, :cond_5

    iget-object v2, v0, Landroidx/media3/exoplayer/hls/q;->n:Ljava/util/ArrayList;

    invoke-virtual {v2}, Ljava/util/ArrayList;->size()I

    move-result v3

    sub-int/2addr v3, v6

    invoke-virtual {v2, v3}, Ljava/util/ArrayList;->remove(I)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Landroidx/media3/exoplayer/hls/i;

    if-ne v2, v1, :cond_3

    const/4 v8, 0x1

    :cond_3
    invoke-static {v8}, Le2/a;->g(Z)V

    iget-object v2, v0, Landroidx/media3/exoplayer/hls/q;->n:Ljava/util/ArrayList;

    invoke-virtual {v2}, Ljava/util/ArrayList;->isEmpty()Z

    move-result v2

    if-eqz v2, :cond_4

    iget-wide v2, v0, Landroidx/media3/exoplayer/hls/q;->P:J

    iput-wide v2, v0, Landroidx/media3/exoplayer/hls/q;->Q:J

    goto :goto_1

    :cond_4
    iget-object v2, v0, Landroidx/media3/exoplayer/hls/q;->n:Ljava/util/ArrayList;

    invoke-static {v2}, Lcom/google/common/collect/e0;->g(Ljava/lang/Iterable;)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Landroidx/media3/exoplayer/hls/i;

    invoke-virtual {v2}, Landroidx/media3/exoplayer/hls/i;->m()V

    :cond_5
    :goto_1
    sget-object v2, Landroidx/media3/exoplayer/upstream/Loader;->f:Landroidx/media3/exoplayer/upstream/Loader$c;

    :goto_2
    move-object/from16 v16, v2

    goto :goto_3

    :cond_6
    iget-object v2, v0, Landroidx/media3/exoplayer/hls/q;->i:Landroidx/media3/exoplayer/upstream/m;

    invoke-interface {v2, v7}, Landroidx/media3/exoplayer/upstream/m;->c(Landroidx/media3/exoplayer/upstream/m$c;)J

    move-result-wide v2

    const-wide v9, -0x7fffffffffffffffL    # -4.9E-324

    cmp-long v4, v2, v9

    if-eqz v4, :cond_7

    invoke-static {v8, v2, v3}, Landroidx/media3/exoplayer/upstream/Loader;->g(ZJ)Landroidx/media3/exoplayer/upstream/Loader$c;

    move-result-object v2

    goto :goto_2

    :cond_7
    sget-object v2, Landroidx/media3/exoplayer/upstream/Loader;->g:Landroidx/media3/exoplayer/upstream/Loader$c;

    goto :goto_2

    :goto_3
    invoke-virtual/range {v16 .. v16}, Landroidx/media3/exoplayer/upstream/Loader$c;->c()Z

    move-result v2

    xor-int/lit8 v17, v2, 0x1

    iget-object v2, v0, Landroidx/media3/exoplayer/hls/q;->k:Landroidx/media3/exoplayer/source/m$a;

    iget v4, v1, Lv2/e;->c:I

    iget v6, v0, Landroidx/media3/exoplayer/hls/q;->b:I

    iget-object v7, v1, Lv2/e;->d:Landroidx/media3/common/y;

    iget v8, v1, Lv2/e;->e:I

    iget-object v9, v1, Lv2/e;->f:Ljava/lang/Object;

    iget-wide v10, v1, Lv2/e;->g:J

    iget-wide v12, v1, Lv2/e;->h:J

    move-object v3, v5

    move v5, v6

    move-object v6, v7

    move v7, v8

    move-object v8, v9

    move-wide v9, v10

    move-wide v11, v12

    move-object/from16 v13, p6

    move/from16 v14, v17

    invoke-virtual/range {v2 .. v14}, Landroidx/media3/exoplayer/source/m$a;->v(Lu2/n;IILandroidx/media3/common/y;ILjava/lang/Object;JJLjava/io/IOException;Z)V

    if-eqz v17, :cond_8

    const/4 v2, 0x0

    iput-object v2, v0, Landroidx/media3/exoplayer/hls/q;->u:Lv2/e;

    iget-object v2, v0, Landroidx/media3/exoplayer/hls/q;->i:Landroidx/media3/exoplayer/upstream/m;

    iget-wide v3, v1, Lv2/e;->a:J

    invoke-interface {v2, v3, v4}, Landroidx/media3/exoplayer/upstream/m;->b(J)V

    :cond_8
    if-eqz v15, :cond_a

    iget-boolean v1, v0, Landroidx/media3/exoplayer/hls/q;->D:Z

    if-nez v1, :cond_9

    new-instance v1, Landroidx/media3/exoplayer/w1$b;

    invoke-direct {v1}, Landroidx/media3/exoplayer/w1$b;-><init>()V

    iget-wide v2, v0, Landroidx/media3/exoplayer/hls/q;->P:J

    invoke-virtual {v1, v2, v3}, Landroidx/media3/exoplayer/w1$b;->f(J)Landroidx/media3/exoplayer/w1$b;

    move-result-object v1

    invoke-virtual {v1}, Landroidx/media3/exoplayer/w1$b;->d()Landroidx/media3/exoplayer/w1;

    move-result-object v1

    invoke-virtual {v0, v1}, Landroidx/media3/exoplayer/hls/q;->a(Landroidx/media3/exoplayer/w1;)Z

    goto :goto_4

    :cond_9
    iget-object v1, v0, Landroidx/media3/exoplayer/hls/q;->c:Landroidx/media3/exoplayer/hls/q$b;

    invoke-interface {v1, v0}, Landroidx/media3/exoplayer/source/t$a;->h(Landroidx/media3/exoplayer/source/t;)V

    :cond_a
    :goto_4
    return-object v16
.end method

.method public N()V
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/hls/q;->x:Ljava/util/Set;

    invoke-interface {v0}, Ljava/util/Set;->clear()V

    return-void
.end method

.method public O(Landroid/net/Uri;Landroidx/media3/exoplayer/upstream/m$c;Z)Z
    .locals 4

    iget-object v0, p0, Landroidx/media3/exoplayer/hls/q;->d:Landroidx/media3/exoplayer/hls/e;

    invoke-virtual {v0, p1}, Landroidx/media3/exoplayer/hls/e;->p(Landroid/net/Uri;)Z

    move-result v0

    const/4 v1, 0x1

    if-nez v0, :cond_0

    return v1

    :cond_0
    const-wide v2, -0x7fffffffffffffffL    # -4.9E-324

    if-nez p3, :cond_1

    iget-object p3, p0, Landroidx/media3/exoplayer/hls/q;->i:Landroidx/media3/exoplayer/upstream/m;

    iget-object v0, p0, Landroidx/media3/exoplayer/hls/q;->d:Landroidx/media3/exoplayer/hls/e;

    invoke-virtual {v0}, Landroidx/media3/exoplayer/hls/e;->k()Lx2/z;

    move-result-object v0

    invoke-static {v0}, Lx2/d0;->c(Lx2/z;)Landroidx/media3/exoplayer/upstream/m$a;

    move-result-object v0

    invoke-interface {p3, v0, p2}, Landroidx/media3/exoplayer/upstream/m;->d(Landroidx/media3/exoplayer/upstream/m$a;Landroidx/media3/exoplayer/upstream/m$c;)Landroidx/media3/exoplayer/upstream/m$b;

    move-result-object p2

    if-eqz p2, :cond_1

    iget p3, p2, Landroidx/media3/exoplayer/upstream/m$b;->a:I

    const/4 v0, 0x2

    if-ne p3, v0, :cond_1

    iget-wide p2, p2, Landroidx/media3/exoplayer/upstream/m$b;->b:J

    goto :goto_0

    :cond_1
    move-wide p2, v2

    :goto_0
    iget-object v0, p0, Landroidx/media3/exoplayer/hls/q;->d:Landroidx/media3/exoplayer/hls/e;

    invoke-virtual {v0, p1, p2, p3}, Landroidx/media3/exoplayer/hls/e;->r(Landroid/net/Uri;J)Z

    move-result p1

    if-eqz p1, :cond_2

    cmp-long p1, p2, v2

    if-eqz p1, :cond_2

    goto :goto_1

    :cond_2
    const/4 v1, 0x0

    :goto_1
    return v1
.end method

.method public P()V
    .locals 3

    iget-object v0, p0, Landroidx/media3/exoplayer/hls/q;->n:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_0

    return-void

    :cond_0
    iget-object v0, p0, Landroidx/media3/exoplayer/hls/q;->n:Ljava/util/ArrayList;

    invoke-static {v0}, Lcom/google/common/collect/e0;->g(Ljava/lang/Iterable;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroidx/media3/exoplayer/hls/i;

    iget-object v1, p0, Landroidx/media3/exoplayer/hls/q;->d:Landroidx/media3/exoplayer/hls/e;

    invoke-virtual {v1, v0}, Landroidx/media3/exoplayer/hls/e;->c(Landroidx/media3/exoplayer/hls/i;)I

    move-result v1

    const/4 v2, 0x1

    if-ne v1, v2, :cond_1

    invoke-virtual {v0}, Landroidx/media3/exoplayer/hls/i;->t()V

    goto :goto_0

    :cond_1
    const/4 v0, 0x2

    if-ne v1, v0, :cond_2

    iget-boolean v0, p0, Landroidx/media3/exoplayer/hls/q;->T:Z

    if-nez v0, :cond_2

    iget-object v0, p0, Landroidx/media3/exoplayer/hls/q;->j:Landroidx/media3/exoplayer/upstream/Loader;

    invoke-virtual {v0}, Landroidx/media3/exoplayer/upstream/Loader;->i()Z

    move-result v0

    if-eqz v0, :cond_2

    iget-object v0, p0, Landroidx/media3/exoplayer/hls/q;->j:Landroidx/media3/exoplayer/upstream/Loader;

    invoke-virtual {v0}, Landroidx/media3/exoplayer/upstream/Loader;->e()V

    :cond_2
    :goto_0
    return-void
.end method

.method public final Q()V
    .locals 1

    const/4 v0, 0x1

    iput-boolean v0, p0, Landroidx/media3/exoplayer/hls/q;->C:Z

    invoke-virtual {p0}, Landroidx/media3/exoplayer/hls/q;->H()V

    return-void
.end method

.method public varargs R([Landroidx/media3/common/n0;I[I)V
    .locals 4

    invoke-virtual {p0, p1}, Landroidx/media3/exoplayer/hls/q;->s([Landroidx/media3/common/n0;)Lu2/k0;

    move-result-object p1

    iput-object p1, p0, Landroidx/media3/exoplayer/hls/q;->I:Lu2/k0;

    new-instance p1, Ljava/util/HashSet;

    invoke-direct {p1}, Ljava/util/HashSet;-><init>()V

    iput-object p1, p0, Landroidx/media3/exoplayer/hls/q;->J:Ljava/util/Set;

    array-length p1, p3

    const/4 v0, 0x0

    :goto_0
    if-ge v0, p1, :cond_0

    aget v1, p3, v0

    iget-object v2, p0, Landroidx/media3/exoplayer/hls/q;->J:Ljava/util/Set;

    iget-object v3, p0, Landroidx/media3/exoplayer/hls/q;->I:Lu2/k0;

    invoke-virtual {v3, v1}, Lu2/k0;->b(I)Landroidx/media3/common/n0;

    move-result-object v1

    invoke-interface {v2, v1}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :cond_0
    iput p2, p0, Landroidx/media3/exoplayer/hls/q;->L:I

    iget-object p1, p0, Landroidx/media3/exoplayer/hls/q;->r:Landroid/os/Handler;

    iget-object p2, p0, Landroidx/media3/exoplayer/hls/q;->c:Landroidx/media3/exoplayer/hls/q$b;

    invoke-static {p2}, Ljava/util/Objects;->requireNonNull(Ljava/lang/Object;)Ljava/lang/Object;

    new-instance p3, Landroidx/media3/exoplayer/hls/n;

    invoke-direct {p3, p2}, Landroidx/media3/exoplayer/hls/n;-><init>(Landroidx/media3/exoplayer/hls/q$b;)V

    invoke-virtual {p1, p3}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    invoke-virtual {p0}, Landroidx/media3/exoplayer/hls/q;->Z()V

    return-void
.end method

.method public S(ILandroidx/media3/exoplayer/t1;Landroidx/media3/decoder/DecoderInputBuffer;I)I
    .locals 11

    invoke-direct {p0}, Landroidx/media3/exoplayer/hls/q;->D()Z

    move-result v0

    const/4 v1, -0x3

    if-eqz v0, :cond_0

    return v1

    :cond_0
    iget-object v0, p0, Landroidx/media3/exoplayer/hls/q;->n:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->isEmpty()Z

    move-result v0

    const/4 v2, 0x0

    if-nez v0, :cond_3

    const/4 v0, 0x0

    :goto_0
    iget-object v3, p0, Landroidx/media3/exoplayer/hls/q;->n:Ljava/util/ArrayList;

    invoke-virtual {v3}, Ljava/util/ArrayList;->size()I

    move-result v3

    add-int/lit8 v3, v3, -0x1

    if-ge v0, v3, :cond_1

    iget-object v3, p0, Landroidx/media3/exoplayer/hls/q;->n:Ljava/util/ArrayList;

    invoke-virtual {v3, v0}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Landroidx/media3/exoplayer/hls/i;

    invoke-virtual {p0, v3}, Landroidx/media3/exoplayer/hls/q;->w(Landroidx/media3/exoplayer/hls/i;)Z

    move-result v3

    if-eqz v3, :cond_1

    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :cond_1
    iget-object v3, p0, Landroidx/media3/exoplayer/hls/q;->n:Ljava/util/ArrayList;

    invoke-static {v3, v2, v0}, Le2/u0;->d1(Ljava/util/List;II)V

    iget-object v0, p0, Landroidx/media3/exoplayer/hls/q;->n:Ljava/util/ArrayList;

    invoke-virtual {v0, v2}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroidx/media3/exoplayer/hls/i;

    iget-object v10, v0, Lv2/e;->d:Landroidx/media3/common/y;

    iget-object v3, p0, Landroidx/media3/exoplayer/hls/q;->G:Landroidx/media3/common/y;

    invoke-virtual {v10, v3}, Landroidx/media3/common/y;->equals(Ljava/lang/Object;)Z

    move-result v3

    if-nez v3, :cond_2

    iget-object v3, p0, Landroidx/media3/exoplayer/hls/q;->k:Landroidx/media3/exoplayer/source/m$a;

    iget v4, p0, Landroidx/media3/exoplayer/hls/q;->b:I

    iget v6, v0, Lv2/e;->e:I

    iget-object v7, v0, Lv2/e;->f:Ljava/lang/Object;

    iget-wide v8, v0, Lv2/e;->g:J

    move-object v5, v10

    invoke-virtual/range {v3 .. v9}, Landroidx/media3/exoplayer/source/m$a;->h(ILandroidx/media3/common/y;ILjava/lang/Object;J)V

    :cond_2
    iput-object v10, p0, Landroidx/media3/exoplayer/hls/q;->G:Landroidx/media3/common/y;

    :cond_3
    iget-object v0, p0, Landroidx/media3/exoplayer/hls/q;->n:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->isEmpty()Z

    move-result v0

    if-nez v0, :cond_4

    iget-object v0, p0, Landroidx/media3/exoplayer/hls/q;->n:Ljava/util/ArrayList;

    invoke-virtual {v0, v2}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroidx/media3/exoplayer/hls/i;

    invoke-virtual {v0}, Landroidx/media3/exoplayer/hls/i;->o()Z

    move-result v0

    if-nez v0, :cond_4

    return v1

    :cond_4
    iget-object v0, p0, Landroidx/media3/exoplayer/hls/q;->v:[Landroidx/media3/exoplayer/hls/q$d;

    aget-object v0, v0, p1

    iget-boolean v1, p0, Landroidx/media3/exoplayer/hls/q;->T:Z

    invoke-virtual {v0, p2, p3, p4, v1}, Landroidx/media3/exoplayer/source/s;->T(Landroidx/media3/exoplayer/t1;Landroidx/media3/decoder/DecoderInputBuffer;IZ)I

    move-result p3

    const/4 p4, -0x5

    if-ne p3, p4, :cond_8

    iget-object p4, p2, Landroidx/media3/exoplayer/t1;->b:Landroidx/media3/common/y;

    invoke-static {p4}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p4

    check-cast p4, Landroidx/media3/common/y;

    iget v0, p0, Landroidx/media3/exoplayer/hls/q;->B:I

    if-ne p1, v0, :cond_7

    iget-object v0, p0, Landroidx/media3/exoplayer/hls/q;->v:[Landroidx/media3/exoplayer/hls/q$d;

    aget-object p1, v0, p1

    invoke-virtual {p1}, Landroidx/media3/exoplayer/source/s;->R()J

    move-result-wide v0

    invoke-static {v0, v1}, Lcom/google/common/primitives/Ints;->d(J)I

    move-result p1

    :goto_1
    iget-object v0, p0, Landroidx/media3/exoplayer/hls/q;->n:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->size()I

    move-result v0

    if-ge v2, v0, :cond_5

    iget-object v0, p0, Landroidx/media3/exoplayer/hls/q;->n:Ljava/util/ArrayList;

    invoke-virtual {v0, v2}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroidx/media3/exoplayer/hls/i;

    iget v0, v0, Landroidx/media3/exoplayer/hls/i;->k:I

    if-eq v0, p1, :cond_5

    add-int/lit8 v2, v2, 0x1

    goto :goto_1

    :cond_5
    iget-object p1, p0, Landroidx/media3/exoplayer/hls/q;->n:Ljava/util/ArrayList;

    invoke-virtual {p1}, Ljava/util/ArrayList;->size()I

    move-result p1

    if-ge v2, p1, :cond_6

    iget-object p1, p0, Landroidx/media3/exoplayer/hls/q;->n:Ljava/util/ArrayList;

    invoke-virtual {p1, v2}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Landroidx/media3/exoplayer/hls/i;

    iget-object p1, p1, Lv2/e;->d:Landroidx/media3/common/y;

    goto :goto_2

    :cond_6
    iget-object p1, p0, Landroidx/media3/exoplayer/hls/q;->F:Landroidx/media3/common/y;

    invoke-static {p1}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Landroidx/media3/common/y;

    :goto_2
    invoke-virtual {p4, p1}, Landroidx/media3/common/y;->n(Landroidx/media3/common/y;)Landroidx/media3/common/y;

    move-result-object p4

    :cond_7
    iput-object p4, p2, Landroidx/media3/exoplayer/t1;->b:Landroidx/media3/common/y;

    :cond_8
    return p3
.end method

.method public T()V
    .locals 4

    iget-boolean v0, p0, Landroidx/media3/exoplayer/hls/q;->D:Z

    if-eqz v0, :cond_0

    iget-object v0, p0, Landroidx/media3/exoplayer/hls/q;->v:[Landroidx/media3/exoplayer/hls/q$d;

    array-length v1, v0

    const/4 v2, 0x0

    :goto_0
    if-ge v2, v1, :cond_0

    aget-object v3, v0, v2

    invoke-virtual {v3}, Landroidx/media3/exoplayer/source/s;->S()V

    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_0
    iget-object v0, p0, Landroidx/media3/exoplayer/hls/q;->j:Landroidx/media3/exoplayer/upstream/Loader;

    invoke-virtual {v0, p0}, Landroidx/media3/exoplayer/upstream/Loader;->l(Landroidx/media3/exoplayer/upstream/Loader$e;)V

    iget-object v0, p0, Landroidx/media3/exoplayer/hls/q;->r:Landroid/os/Handler;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Landroid/os/Handler;->removeCallbacksAndMessages(Ljava/lang/Object;)V

    const/4 v0, 0x1

    iput-boolean v0, p0, Landroidx/media3/exoplayer/hls/q;->H:Z

    iget-object v0, p0, Landroidx/media3/exoplayer/hls/q;->s:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->clear()V

    return-void
.end method

.method public final U()V
    .locals 6

    iget-object v0, p0, Landroidx/media3/exoplayer/hls/q;->v:[Landroidx/media3/exoplayer/hls/q$d;

    array-length v1, v0

    const/4 v2, 0x0

    const/4 v3, 0x0

    :goto_0
    if-ge v3, v1, :cond_0

    aget-object v4, v0, v3

    iget-boolean v5, p0, Landroidx/media3/exoplayer/hls/q;->R:Z

    invoke-virtual {v4, v5}, Landroidx/media3/exoplayer/source/s;->X(Z)V

    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    :cond_0
    iput-boolean v2, p0, Landroidx/media3/exoplayer/hls/q;->R:Z

    return-void
.end method

.method public final V(JLandroidx/media3/exoplayer/hls/i;)Z
    .locals 5
    .param p3    # Landroidx/media3/exoplayer/hls/i;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    iget-object v0, p0, Landroidx/media3/exoplayer/hls/q;->v:[Landroidx/media3/exoplayer/hls/q$d;

    array-length v0, v0

    const/4 v1, 0x0

    const/4 v2, 0x0

    :goto_0
    if-ge v2, v0, :cond_3

    iget-object v3, p0, Landroidx/media3/exoplayer/hls/q;->v:[Landroidx/media3/exoplayer/hls/q$d;

    aget-object v3, v3, v2

    if-eqz p3, :cond_0

    invoke-virtual {p3, v2}, Landroidx/media3/exoplayer/hls/i;->k(I)I

    move-result v4

    invoke-virtual {v3, v4}, Landroidx/media3/exoplayer/source/s;->Z(I)Z

    move-result v3

    goto :goto_1

    :cond_0
    invoke-virtual {v3, p1, p2, v1}, Landroidx/media3/exoplayer/source/s;->a0(JZ)Z

    move-result v3

    :goto_1
    if-nez v3, :cond_2

    iget-object v3, p0, Landroidx/media3/exoplayer/hls/q;->O:[Z

    aget-boolean v3, v3, v2

    if-nez v3, :cond_1

    iget-boolean v3, p0, Landroidx/media3/exoplayer/hls/q;->M:Z

    if-nez v3, :cond_2

    :cond_1
    return v1

    :cond_2
    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_3
    const/4 p1, 0x1

    return p1
.end method

.method public W(JZ)Z
    .locals 7

    iput-wide p1, p0, Landroidx/media3/exoplayer/hls/q;->P:J

    invoke-direct {p0}, Landroidx/media3/exoplayer/hls/q;->D()Z

    move-result v0

    const/4 v1, 0x1

    if-eqz v0, :cond_0

    iput-wide p1, p0, Landroidx/media3/exoplayer/hls/q;->Q:J

    return v1

    :cond_0
    iget-object v0, p0, Landroidx/media3/exoplayer/hls/q;->d:Landroidx/media3/exoplayer/hls/e;

    invoke-virtual {v0}, Landroidx/media3/exoplayer/hls/e;->l()Z

    move-result v0

    const/4 v2, 0x0

    if-eqz v0, :cond_2

    const/4 v0, 0x0

    :goto_0
    iget-object v3, p0, Landroidx/media3/exoplayer/hls/q;->n:Ljava/util/ArrayList;

    invoke-virtual {v3}, Ljava/util/ArrayList;->size()I

    move-result v3

    if-ge v0, v3, :cond_2

    iget-object v3, p0, Landroidx/media3/exoplayer/hls/q;->n:Ljava/util/ArrayList;

    invoke-virtual {v3, v0}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Landroidx/media3/exoplayer/hls/i;

    iget-wide v4, v3, Lv2/e;->g:J

    cmp-long v6, v4, p1

    if-nez v6, :cond_1

    goto :goto_1

    :cond_1
    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :cond_2
    const/4 v3, 0x0

    :goto_1
    iget-boolean v0, p0, Landroidx/media3/exoplayer/hls/q;->C:Z

    if-eqz v0, :cond_3

    if-nez p3, :cond_3

    invoke-virtual {p0, p1, p2, v3}, Landroidx/media3/exoplayer/hls/q;->V(JLandroidx/media3/exoplayer/hls/i;)Z

    move-result p3

    if-eqz p3, :cond_3

    return v2

    :cond_3
    iput-wide p1, p0, Landroidx/media3/exoplayer/hls/q;->Q:J

    iput-boolean v2, p0, Landroidx/media3/exoplayer/hls/q;->T:Z

    iget-object p1, p0, Landroidx/media3/exoplayer/hls/q;->n:Ljava/util/ArrayList;

    invoke-virtual {p1}, Ljava/util/ArrayList;->clear()V

    iget-object p1, p0, Landroidx/media3/exoplayer/hls/q;->j:Landroidx/media3/exoplayer/upstream/Loader;

    invoke-virtual {p1}, Landroidx/media3/exoplayer/upstream/Loader;->i()Z

    move-result p1

    if-eqz p1, :cond_5

    iget-boolean p1, p0, Landroidx/media3/exoplayer/hls/q;->C:Z

    if-eqz p1, :cond_4

    iget-object p1, p0, Landroidx/media3/exoplayer/hls/q;->v:[Landroidx/media3/exoplayer/hls/q$d;

    array-length p2, p1

    :goto_2
    if-ge v2, p2, :cond_4

    aget-object p3, p1, v2

    invoke-virtual {p3}, Landroidx/media3/exoplayer/source/s;->r()V

    add-int/lit8 v2, v2, 0x1

    goto :goto_2

    :cond_4
    iget-object p1, p0, Landroidx/media3/exoplayer/hls/q;->j:Landroidx/media3/exoplayer/upstream/Loader;

    invoke-virtual {p1}, Landroidx/media3/exoplayer/upstream/Loader;->e()V

    goto :goto_3

    :cond_5
    iget-object p1, p0, Landroidx/media3/exoplayer/hls/q;->j:Landroidx/media3/exoplayer/upstream/Loader;

    invoke-virtual {p1}, Landroidx/media3/exoplayer/upstream/Loader;->f()V

    invoke-virtual {p0}, Landroidx/media3/exoplayer/hls/q;->U()V

    :goto_3
    return v1
.end method

.method public X([Lx2/z;[Z[Lu2/e0;[ZJZ)Z
    .locals 19

    move-object/from16 v0, p0

    move-object/from16 v1, p1

    move-object/from16 v2, p3

    move-wide/from16 v12, p5

    invoke-virtual/range {p0 .. p0}, Landroidx/media3/exoplayer/hls/q;->i()V

    iget v3, v0, Landroidx/media3/exoplayer/hls/q;->E:I

    const/4 v14, 0x0

    const/4 v4, 0x0

    :goto_0
    array-length v5, v1

    const/4 v6, 0x0

    const/4 v15, 0x1

    if-ge v4, v5, :cond_2

    aget-object v5, v2, v4

    check-cast v5, Landroidx/media3/exoplayer/hls/m;

    if-eqz v5, :cond_1

    aget-object v7, v1, v4

    if-eqz v7, :cond_0

    aget-boolean v7, p2, v4

    if-nez v7, :cond_1

    :cond_0
    iget v7, v0, Landroidx/media3/exoplayer/hls/q;->E:I

    sub-int/2addr v7, v15

    iput v7, v0, Landroidx/media3/exoplayer/hls/q;->E:I

    invoke-virtual {v5}, Landroidx/media3/exoplayer/hls/m;->d()V

    aput-object v6, v2, v4

    :cond_1
    add-int/lit8 v4, v4, 0x1

    goto :goto_0

    :cond_2
    if-nez p7, :cond_5

    iget-boolean v4, v0, Landroidx/media3/exoplayer/hls/q;->S:Z

    if-eqz v4, :cond_3

    if-nez v3, :cond_4

    goto :goto_1

    :cond_3
    iget-wide v3, v0, Landroidx/media3/exoplayer/hls/q;->P:J

    cmp-long v5, v12, v3

    if-eqz v5, :cond_4

    goto :goto_1

    :cond_4
    const/4 v3, 0x0

    goto :goto_2

    :cond_5
    :goto_1
    const/4 v3, 0x1

    :goto_2
    iget-object v4, v0, Landroidx/media3/exoplayer/hls/q;->d:Landroidx/media3/exoplayer/hls/e;

    invoke-virtual {v4}, Landroidx/media3/exoplayer/hls/e;->k()Lx2/z;

    move-result-object v4

    move/from16 v16, v3

    move-object v11, v4

    const/4 v3, 0x0

    :goto_3
    array-length v5, v1

    if-ge v3, v5, :cond_a

    aget-object v5, v1, v3

    if-nez v5, :cond_6

    goto :goto_5

    :cond_6
    iget-object v7, v0, Landroidx/media3/exoplayer/hls/q;->I:Lu2/k0;

    invoke-interface {v5}, Lx2/c0;->getTrackGroup()Landroidx/media3/common/n0;

    move-result-object v8

    invoke-virtual {v7, v8}, Lu2/k0;->d(Landroidx/media3/common/n0;)I

    move-result v7

    iget v8, v0, Landroidx/media3/exoplayer/hls/q;->L:I

    if-ne v7, v8, :cond_7

    iget-object v8, v0, Landroidx/media3/exoplayer/hls/q;->d:Landroidx/media3/exoplayer/hls/e;

    invoke-virtual {v8, v5}, Landroidx/media3/exoplayer/hls/e;->v(Lx2/z;)V

    move-object v11, v5

    :cond_7
    aget-object v5, v2, v3

    if-nez v5, :cond_9

    iget v5, v0, Landroidx/media3/exoplayer/hls/q;->E:I

    add-int/2addr v5, v15

    iput v5, v0, Landroidx/media3/exoplayer/hls/q;->E:I

    new-instance v5, Landroidx/media3/exoplayer/hls/m;

    invoke-direct {v5, v0, v7}, Landroidx/media3/exoplayer/hls/m;-><init>(Landroidx/media3/exoplayer/hls/q;I)V

    aput-object v5, v2, v3

    aput-boolean v15, p4, v3

    iget-object v8, v0, Landroidx/media3/exoplayer/hls/q;->K:[I

    if-eqz v8, :cond_9

    invoke-virtual {v5}, Landroidx/media3/exoplayer/hls/m;->a()V

    if-nez v16, :cond_9

    iget-object v5, v0, Landroidx/media3/exoplayer/hls/q;->v:[Landroidx/media3/exoplayer/hls/q$d;

    iget-object v8, v0, Landroidx/media3/exoplayer/hls/q;->K:[I

    aget v7, v8, v7

    aget-object v5, v5, v7

    invoke-virtual {v5}, Landroidx/media3/exoplayer/source/s;->D()I

    move-result v7

    if-eqz v7, :cond_8

    invoke-virtual {v5, v12, v13, v15}, Landroidx/media3/exoplayer/source/s;->a0(JZ)Z

    move-result v5

    if-nez v5, :cond_8

    const/4 v5, 0x1

    goto :goto_4

    :cond_8
    const/4 v5, 0x0

    :goto_4
    move/from16 v16, v5

    :cond_9
    :goto_5
    add-int/lit8 v3, v3, 0x1

    goto :goto_3

    :cond_a
    iget v1, v0, Landroidx/media3/exoplayer/hls/q;->E:I

    if-nez v1, :cond_d

    iget-object v1, v0, Landroidx/media3/exoplayer/hls/q;->d:Landroidx/media3/exoplayer/hls/e;

    invoke-virtual {v1}, Landroidx/media3/exoplayer/hls/e;->s()V

    iput-object v6, v0, Landroidx/media3/exoplayer/hls/q;->G:Landroidx/media3/common/y;

    iput-boolean v15, v0, Landroidx/media3/exoplayer/hls/q;->R:Z

    iget-object v1, v0, Landroidx/media3/exoplayer/hls/q;->n:Ljava/util/ArrayList;

    invoke-virtual {v1}, Ljava/util/ArrayList;->clear()V

    iget-object v1, v0, Landroidx/media3/exoplayer/hls/q;->j:Landroidx/media3/exoplayer/upstream/Loader;

    invoke-virtual {v1}, Landroidx/media3/exoplayer/upstream/Loader;->i()Z

    move-result v1

    if-eqz v1, :cond_c

    iget-boolean v1, v0, Landroidx/media3/exoplayer/hls/q;->C:Z

    if-eqz v1, :cond_b

    iget-object v1, v0, Landroidx/media3/exoplayer/hls/q;->v:[Landroidx/media3/exoplayer/hls/q$d;

    array-length v3, v1

    :goto_6
    if-ge v14, v3, :cond_b

    aget-object v4, v1, v14

    invoke-virtual {v4}, Landroidx/media3/exoplayer/source/s;->r()V

    add-int/lit8 v14, v14, 0x1

    goto :goto_6

    :cond_b
    iget-object v1, v0, Landroidx/media3/exoplayer/hls/q;->j:Landroidx/media3/exoplayer/upstream/Loader;

    invoke-virtual {v1}, Landroidx/media3/exoplayer/upstream/Loader;->e()V

    goto :goto_9

    :cond_c
    invoke-virtual/range {p0 .. p0}, Landroidx/media3/exoplayer/hls/q;->U()V

    goto :goto_9

    :cond_d
    iget-object v1, v0, Landroidx/media3/exoplayer/hls/q;->n:Ljava/util/ArrayList;

    invoke-virtual {v1}, Ljava/util/ArrayList;->isEmpty()Z

    move-result v1

    if-nez v1, :cond_10

    invoke-static {v11, v4}, Le2/u0;->c(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_10

    iget-boolean v1, v0, Landroidx/media3/exoplayer/hls/q;->S:Z

    if-nez v1, :cond_f

    const-wide/16 v3, 0x0

    cmp-long v1, v12, v3

    if-gez v1, :cond_e

    neg-long v3, v12

    :cond_e
    move-wide v6, v3

    invoke-virtual/range {p0 .. p0}, Landroidx/media3/exoplayer/hls/q;->y()Landroidx/media3/exoplayer/hls/i;

    move-result-object v1

    iget-object v3, v0, Landroidx/media3/exoplayer/hls/q;->d:Landroidx/media3/exoplayer/hls/e;

    invoke-virtual {v3, v1, v12, v13}, Landroidx/media3/exoplayer/hls/e;->a(Landroidx/media3/exoplayer/hls/i;J)[Lv2/n;

    move-result-object v17

    const-wide v8, -0x7fffffffffffffffL    # -4.9E-324

    iget-object v10, v0, Landroidx/media3/exoplayer/hls/q;->o:Ljava/util/List;

    move-object v3, v11

    move-wide/from16 v4, p5

    move-object/from16 v18, v11

    move-object/from16 v11, v17

    invoke-interface/range {v3 .. v11}, Lx2/z;->g(JJJLjava/util/List;[Lv2/n;)V

    iget-object v3, v0, Landroidx/media3/exoplayer/hls/q;->d:Landroidx/media3/exoplayer/hls/e;

    invoke-virtual {v3}, Landroidx/media3/exoplayer/hls/e;->j()Landroidx/media3/common/n0;

    move-result-object v3

    iget-object v1, v1, Lv2/e;->d:Landroidx/media3/common/y;

    invoke-virtual {v3, v1}, Landroidx/media3/common/n0;->b(Landroidx/media3/common/y;)I

    move-result v1

    invoke-interface/range {v18 .. v18}, Lx2/z;->getSelectedIndexInTrackGroup()I

    move-result v3

    if-eq v3, v1, :cond_10

    :cond_f
    iput-boolean v15, v0, Landroidx/media3/exoplayer/hls/q;->R:Z

    const/4 v1, 0x1

    const/16 v16, 0x1

    goto :goto_7

    :cond_10
    move/from16 v1, p7

    :goto_7
    if-eqz v16, :cond_12

    invoke-virtual {v0, v12, v13, v1}, Landroidx/media3/exoplayer/hls/q;->W(JZ)Z

    :goto_8
    array-length v1, v2

    if-ge v14, v1, :cond_12

    aget-object v1, v2, v14

    if-eqz v1, :cond_11

    aput-boolean v15, p4, v14

    :cond_11
    add-int/lit8 v14, v14, 0x1

    goto :goto_8

    :cond_12
    :goto_9
    invoke-virtual {v0, v2}, Landroidx/media3/exoplayer/hls/q;->e0([Lu2/e0;)V

    iput-boolean v15, v0, Landroidx/media3/exoplayer/hls/q;->S:Z

    return v16
.end method

.method public Y(Landroidx/media3/common/DrmInitData;)V
    .locals 3
    .param p1    # Landroidx/media3/common/DrmInitData;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    iget-object v0, p0, Landroidx/media3/exoplayer/hls/q;->W:Landroidx/media3/common/DrmInitData;

    invoke-static {v0, p1}, Le2/u0;->c(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_1

    iput-object p1, p0, Landroidx/media3/exoplayer/hls/q;->W:Landroidx/media3/common/DrmInitData;

    const/4 v0, 0x0

    :goto_0
    iget-object v1, p0, Landroidx/media3/exoplayer/hls/q;->v:[Landroidx/media3/exoplayer/hls/q$d;

    array-length v2, v1

    if-ge v0, v2, :cond_1

    iget-object v2, p0, Landroidx/media3/exoplayer/hls/q;->O:[Z

    aget-boolean v2, v2, v0

    if-eqz v2, :cond_0

    aget-object v1, v1, v0

    invoke-virtual {v1, p1}, Landroidx/media3/exoplayer/hls/q$d;->j0(Landroidx/media3/common/DrmInitData;)V

    :cond_0
    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :cond_1
    return-void
.end method

.method public final Z()V
    .locals 1

    const/4 v0, 0x1

    iput-boolean v0, p0, Landroidx/media3/exoplayer/hls/q;->D:Z

    return-void
.end method

.method public a(Landroidx/media3/exoplayer/w1;)Z
    .locals 23

    move-object/from16 v0, p0

    iget-boolean v1, v0, Landroidx/media3/exoplayer/hls/q;->T:Z

    const/4 v2, 0x0

    if-nez v1, :cond_a

    iget-object v1, v0, Landroidx/media3/exoplayer/hls/q;->j:Landroidx/media3/exoplayer/upstream/Loader;

    invoke-virtual {v1}, Landroidx/media3/exoplayer/upstream/Loader;->i()Z

    move-result v1

    if-nez v1, :cond_a

    iget-object v1, v0, Landroidx/media3/exoplayer/hls/q;->j:Landroidx/media3/exoplayer/upstream/Loader;

    invoke-virtual {v1}, Landroidx/media3/exoplayer/upstream/Loader;->h()Z

    move-result v1

    if-eqz v1, :cond_0

    goto/16 :goto_5

    :cond_0
    invoke-direct/range {p0 .. p0}, Landroidx/media3/exoplayer/hls/q;->D()Z

    move-result v1

    if-eqz v1, :cond_2

    invoke-static {}, Ljava/util/Collections;->emptyList()Ljava/util/List;

    move-result-object v1

    iget-wide v3, v0, Landroidx/media3/exoplayer/hls/q;->Q:J

    iget-object v5, v0, Landroidx/media3/exoplayer/hls/q;->v:[Landroidx/media3/exoplayer/hls/q$d;

    array-length v6, v5

    const/4 v7, 0x0

    :goto_0
    if-ge v7, v6, :cond_1

    aget-object v8, v5, v7

    iget-wide v9, v0, Landroidx/media3/exoplayer/hls/q;->Q:J

    invoke-virtual {v8, v9, v10}, Landroidx/media3/exoplayer/source/s;->c0(J)V

    add-int/lit8 v7, v7, 0x1

    goto :goto_0

    :cond_1
    :goto_1
    move-object v9, v1

    move-wide v7, v3

    goto :goto_2

    :cond_2
    iget-object v1, v0, Landroidx/media3/exoplayer/hls/q;->o:Ljava/util/List;

    invoke-virtual/range {p0 .. p0}, Landroidx/media3/exoplayer/hls/q;->y()Landroidx/media3/exoplayer/hls/i;

    move-result-object v3

    invoke-virtual {v3}, Landroidx/media3/exoplayer/hls/i;->f()Z

    move-result v4

    if-eqz v4, :cond_3

    iget-wide v3, v3, Lv2/e;->h:J

    goto :goto_1

    :cond_3
    iget-wide v4, v0, Landroidx/media3/exoplayer/hls/q;->P:J

    iget-wide v6, v3, Lv2/e;->g:J

    invoke-static {v4, v5, v6, v7}, Ljava/lang/Math;->max(JJ)J

    move-result-wide v3

    goto :goto_1

    :goto_2
    iget-object v1, v0, Landroidx/media3/exoplayer/hls/q;->m:Landroidx/media3/exoplayer/hls/e$b;

    invoke-virtual {v1}, Landroidx/media3/exoplayer/hls/e$b;->a()V

    iget-object v5, v0, Landroidx/media3/exoplayer/hls/q;->d:Landroidx/media3/exoplayer/hls/e;

    iget-boolean v1, v0, Landroidx/media3/exoplayer/hls/q;->D:Z

    const/4 v3, 0x1

    if-nez v1, :cond_5

    invoke-interface {v9}, Ljava/util/List;->isEmpty()Z

    move-result v1

    if-nez v1, :cond_4

    goto :goto_3

    :cond_4
    const/4 v10, 0x0

    goto :goto_4

    :cond_5
    :goto_3
    const/4 v10, 0x1

    :goto_4
    iget-object v11, v0, Landroidx/media3/exoplayer/hls/q;->m:Landroidx/media3/exoplayer/hls/e$b;

    move-object/from16 v6, p1

    invoke-virtual/range {v5 .. v11}, Landroidx/media3/exoplayer/hls/e;->e(Landroidx/media3/exoplayer/w1;JLjava/util/List;ZLandroidx/media3/exoplayer/hls/e$b;)V

    iget-object v1, v0, Landroidx/media3/exoplayer/hls/q;->m:Landroidx/media3/exoplayer/hls/e$b;

    iget-boolean v4, v1, Landroidx/media3/exoplayer/hls/e$b;->b:Z

    iget-object v5, v1, Landroidx/media3/exoplayer/hls/e$b;->a:Lv2/e;

    iget-object v1, v1, Landroidx/media3/exoplayer/hls/e$b;->c:Landroid/net/Uri;

    if-eqz v4, :cond_6

    const-wide v1, -0x7fffffffffffffffL    # -4.9E-324

    iput-wide v1, v0, Landroidx/media3/exoplayer/hls/q;->Q:J

    iput-boolean v3, v0, Landroidx/media3/exoplayer/hls/q;->T:Z

    return v3

    :cond_6
    if-nez v5, :cond_8

    if-eqz v1, :cond_7

    iget-object v3, v0, Landroidx/media3/exoplayer/hls/q;->c:Landroidx/media3/exoplayer/hls/q$b;

    invoke-interface {v3, v1}, Landroidx/media3/exoplayer/hls/q$b;->d(Landroid/net/Uri;)V

    :cond_7
    return v2

    :cond_8
    invoke-static {v5}, Landroidx/media3/exoplayer/hls/q;->C(Lv2/e;)Z

    move-result v1

    if-eqz v1, :cond_9

    move-object v1, v5

    check-cast v1, Landroidx/media3/exoplayer/hls/i;

    invoke-virtual {v0, v1}, Landroidx/media3/exoplayer/hls/q;->B(Landroidx/media3/exoplayer/hls/i;)V

    :cond_9
    iput-object v5, v0, Landroidx/media3/exoplayer/hls/q;->u:Lv2/e;

    iget-object v1, v0, Landroidx/media3/exoplayer/hls/q;->j:Landroidx/media3/exoplayer/upstream/Loader;

    iget-object v2, v0, Landroidx/media3/exoplayer/hls/q;->i:Landroidx/media3/exoplayer/upstream/m;

    iget v4, v5, Lv2/e;->c:I

    invoke-interface {v2, v4}, Landroidx/media3/exoplayer/upstream/m;->a(I)I

    move-result v2

    invoke-virtual {v1, v5, v0, v2}, Landroidx/media3/exoplayer/upstream/Loader;->m(Landroidx/media3/exoplayer/upstream/Loader$d;Landroidx/media3/exoplayer/upstream/Loader$b;I)J

    move-result-wide v10

    iget-object v12, v0, Landroidx/media3/exoplayer/hls/q;->k:Landroidx/media3/exoplayer/source/m$a;

    new-instance v13, Lu2/n;

    iget-wide v7, v5, Lv2/e;->a:J

    iget-object v9, v5, Lv2/e;->b:Lh2/g;

    move-object v6, v13

    invoke-direct/range {v6 .. v11}, Lu2/n;-><init>(JLh2/g;J)V

    iget v14, v5, Lv2/e;->c:I

    iget v15, v0, Landroidx/media3/exoplayer/hls/q;->b:I

    iget-object v1, v5, Lv2/e;->d:Landroidx/media3/common/y;

    iget v2, v5, Lv2/e;->e:I

    iget-object v4, v5, Lv2/e;->f:Ljava/lang/Object;

    iget-wide v6, v5, Lv2/e;->g:J

    iget-wide v8, v5, Lv2/e;->h:J

    move-object/from16 v16, v1

    move/from16 v17, v2

    move-object/from16 v18, v4

    move-wide/from16 v19, v6

    move-wide/from16 v21, v8

    invoke-virtual/range {v12 .. v22}, Landroidx/media3/exoplayer/source/m$a;->z(Lu2/n;IILandroidx/media3/common/y;ILjava/lang/Object;JJ)V

    return v3

    :cond_a
    :goto_5
    return v2
.end method

.method public a0(Z)V
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/hls/q;->d:Landroidx/media3/exoplayer/hls/e;

    invoke-virtual {v0, p1}, Landroidx/media3/exoplayer/hls/e;->u(Z)V

    return-void
.end method

.method public b(JLandroidx/media3/exoplayer/b3;)J
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/hls/q;->d:Landroidx/media3/exoplayer/hls/e;

    invoke-virtual {v0, p1, p2, p3}, Landroidx/media3/exoplayer/hls/e;->b(JLandroidx/media3/exoplayer/b3;)J

    move-result-wide p1

    return-wide p1
.end method

.method public b0(J)V
    .locals 4

    iget-wide v0, p0, Landroidx/media3/exoplayer/hls/q;->V:J

    cmp-long v2, v0, p1

    if-eqz v2, :cond_0

    iput-wide p1, p0, Landroidx/media3/exoplayer/hls/q;->V:J

    iget-object v0, p0, Landroidx/media3/exoplayer/hls/q;->v:[Landroidx/media3/exoplayer/hls/q$d;

    array-length v1, v0

    const/4 v2, 0x0

    :goto_0
    if-ge v2, v1, :cond_0

    aget-object v3, v0, v2

    invoke-virtual {v3, p1, p2}, Landroidx/media3/exoplayer/source/s;->b0(J)V

    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_0
    return-void
.end method

.method public c(Landroidx/media3/common/y;)V
    .locals 1

    iget-object p1, p0, Landroidx/media3/exoplayer/hls/q;->r:Landroid/os/Handler;

    iget-object v0, p0, Landroidx/media3/exoplayer/hls/q;->p:Ljava/lang/Runnable;

    invoke-virtual {p1, v0}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    return-void
.end method

.method public c0(IJ)I
    .locals 2

    invoke-direct {p0}, Landroidx/media3/exoplayer/hls/q;->D()Z

    move-result v0

    if-eqz v0, :cond_0

    const/4 p1, 0x0

    return p1

    :cond_0
    iget-object v0, p0, Landroidx/media3/exoplayer/hls/q;->v:[Landroidx/media3/exoplayer/hls/q$d;

    aget-object v0, v0, p1

    iget-boolean v1, p0, Landroidx/media3/exoplayer/hls/q;->T:Z

    invoke-virtual {v0, p2, p3, v1}, Landroidx/media3/exoplayer/source/s;->F(JZ)I

    move-result p2

    iget-object p3, p0, Landroidx/media3/exoplayer/hls/q;->n:Ljava/util/ArrayList;

    const/4 v1, 0x0

    invoke-static {p3, v1}, Lcom/google/common/collect/e0;->h(Ljava/lang/Iterable;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p3

    check-cast p3, Landroidx/media3/exoplayer/hls/i;

    if-eqz p3, :cond_1

    invoke-virtual {p3}, Landroidx/media3/exoplayer/hls/i;->o()Z

    move-result v1

    if-nez v1, :cond_1

    invoke-virtual {v0}, Landroidx/media3/exoplayer/source/s;->D()I

    move-result v1

    invoke-virtual {p3, p1}, Landroidx/media3/exoplayer/hls/i;->k(I)I

    move-result p1

    sub-int/2addr p1, v1

    invoke-static {p2, p1}, Ljava/lang/Math;->min(II)I

    move-result p2

    :cond_1
    invoke-virtual {v0, p2}, Landroidx/media3/exoplayer/source/s;->f0(I)V

    return p2
.end method

.method public d0(I)V
    .locals 2

    invoke-virtual {p0}, Landroidx/media3/exoplayer/hls/q;->i()V

    iget-object v0, p0, Landroidx/media3/exoplayer/hls/q;->K:[I

    invoke-static {v0}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    iget-object v0, p0, Landroidx/media3/exoplayer/hls/q;->K:[I

    aget p1, v0, p1

    iget-object v0, p0, Landroidx/media3/exoplayer/hls/q;->N:[Z

    aget-boolean v0, v0, p1

    invoke-static {v0}, Le2/a;->g(Z)V

    iget-object v0, p0, Landroidx/media3/exoplayer/hls/q;->N:[Z

    const/4 v1, 0x0

    aput-boolean v1, v0, p1

    return-void
.end method

.method public discardBuffer(JZ)V
    .locals 4

    iget-boolean v0, p0, Landroidx/media3/exoplayer/hls/q;->C:Z

    if-eqz v0, :cond_1

    invoke-direct {p0}, Landroidx/media3/exoplayer/hls/q;->D()Z

    move-result v0

    if-eqz v0, :cond_0

    goto :goto_1

    :cond_0
    iget-object v0, p0, Landroidx/media3/exoplayer/hls/q;->v:[Landroidx/media3/exoplayer/hls/q$d;

    array-length v0, v0

    const/4 v1, 0x0

    :goto_0
    if-ge v1, v0, :cond_1

    iget-object v2, p0, Landroidx/media3/exoplayer/hls/q;->v:[Landroidx/media3/exoplayer/hls/q$d;

    aget-object v2, v2, v1

    iget-object v3, p0, Landroidx/media3/exoplayer/hls/q;->N:[Z

    aget-boolean v3, v3, v1

    invoke-virtual {v2, p1, p2, p3, v3}, Landroidx/media3/exoplayer/source/s;->q(JZZ)V

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_1
    :goto_1
    return-void
.end method

.method public final e0([Lu2/e0;)V
    .locals 4

    iget-object v0, p0, Landroidx/media3/exoplayer/hls/q;->s:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->clear()V

    array-length v0, p1

    const/4 v1, 0x0

    :goto_0
    if-ge v1, v0, :cond_1

    aget-object v2, p1, v1

    if-eqz v2, :cond_0

    iget-object v3, p0, Landroidx/media3/exoplayer/hls/q;->s:Ljava/util/ArrayList;

    check-cast v2, Landroidx/media3/exoplayer/hls/m;

    invoke-virtual {v3, v2}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    :cond_0
    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_1
    return-void
.end method

.method public endTracks()V
    .locals 2

    const/4 v0, 0x1

    iput-boolean v0, p0, Landroidx/media3/exoplayer/hls/q;->U:Z

    iget-object v0, p0, Landroidx/media3/exoplayer/hls/q;->r:Landroid/os/Handler;

    iget-object v1, p0, Landroidx/media3/exoplayer/hls/q;->q:Ljava/lang/Runnable;

    invoke-virtual {v0, v1}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    return-void
.end method

.method public g(Lz2/m0;)V
    .locals 0

    return-void
.end method

.method public getBufferedPositionUs()J
    .locals 7

    iget-boolean v0, p0, Landroidx/media3/exoplayer/hls/q;->T:Z

    if-eqz v0, :cond_0

    const-wide/high16 v0, -0x8000000000000000L

    return-wide v0

    :cond_0
    invoke-direct {p0}, Landroidx/media3/exoplayer/hls/q;->D()Z

    move-result v0

    if-eqz v0, :cond_1

    iget-wide v0, p0, Landroidx/media3/exoplayer/hls/q;->Q:J

    return-wide v0

    :cond_1
    iget-wide v0, p0, Landroidx/media3/exoplayer/hls/q;->P:J

    invoke-virtual {p0}, Landroidx/media3/exoplayer/hls/q;->y()Landroidx/media3/exoplayer/hls/i;

    move-result-object v2

    invoke-virtual {v2}, Landroidx/media3/exoplayer/hls/i;->f()Z

    move-result v3

    if-eqz v3, :cond_2

    goto :goto_0

    :cond_2
    iget-object v2, p0, Landroidx/media3/exoplayer/hls/q;->n:Ljava/util/ArrayList;

    invoke-virtual {v2}, Ljava/util/ArrayList;->size()I

    move-result v2

    const/4 v3, 0x1

    if-le v2, v3, :cond_3

    iget-object v2, p0, Landroidx/media3/exoplayer/hls/q;->n:Ljava/util/ArrayList;

    invoke-virtual {v2}, Ljava/util/ArrayList;->size()I

    move-result v3

    add-int/lit8 v3, v3, -0x2

    invoke-virtual {v2, v3}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Landroidx/media3/exoplayer/hls/i;

    goto :goto_0

    :cond_3
    const/4 v2, 0x0

    :goto_0
    if-eqz v2, :cond_4

    iget-wide v2, v2, Lv2/e;->h:J

    invoke-static {v0, v1, v2, v3}, Ljava/lang/Math;->max(JJ)J

    move-result-wide v0

    :cond_4
    iget-boolean v2, p0, Landroidx/media3/exoplayer/hls/q;->C:Z

    if-eqz v2, :cond_5

    iget-object v2, p0, Landroidx/media3/exoplayer/hls/q;->v:[Landroidx/media3/exoplayer/hls/q$d;

    array-length v3, v2

    const/4 v4, 0x0

    :goto_1
    if-ge v4, v3, :cond_5

    aget-object v5, v2, v4

    invoke-virtual {v5}, Landroidx/media3/exoplayer/source/s;->A()J

    move-result-wide v5

    invoke-static {v0, v1, v5, v6}, Ljava/lang/Math;->max(JJ)J

    move-result-wide v0

    add-int/lit8 v4, v4, 0x1

    goto :goto_1

    :cond_5
    return-wide v0
.end method

.method public getNextLoadPositionUs()J
    .locals 2

    invoke-direct {p0}, Landroidx/media3/exoplayer/hls/q;->D()Z

    move-result v0

    if-eqz v0, :cond_0

    iget-wide v0, p0, Landroidx/media3/exoplayer/hls/q;->Q:J

    return-wide v0

    :cond_0
    iget-boolean v0, p0, Landroidx/media3/exoplayer/hls/q;->T:Z

    if-eqz v0, :cond_1

    const-wide/high16 v0, -0x8000000000000000L

    goto :goto_0

    :cond_1
    invoke-virtual {p0}, Landroidx/media3/exoplayer/hls/q;->y()Landroidx/media3/exoplayer/hls/i;

    move-result-object v0

    iget-wide v0, v0, Lv2/e;->h:J

    :goto_0
    return-wide v0
.end method

.method public getTrackGroups()Lu2/k0;
    .locals 1

    invoke-virtual {p0}, Landroidx/media3/exoplayer/hls/q;->i()V

    iget-object v0, p0, Landroidx/media3/exoplayer/hls/q;->I:Lu2/k0;

    return-object v0
.end method

.method public final i()V
    .locals 1

    iget-boolean v0, p0, Landroidx/media3/exoplayer/hls/q;->D:Z

    invoke-static {v0}, Le2/a;->g(Z)V

    iget-object v0, p0, Landroidx/media3/exoplayer/hls/q;->I:Lu2/k0;

    invoke-static {v0}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    iget-object v0, p0, Landroidx/media3/exoplayer/hls/q;->J:Ljava/util/Set;

    invoke-static {v0}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    return-void
.end method

.method public isLoading()Z
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/hls/q;->j:Landroidx/media3/exoplayer/upstream/Loader;

    invoke-virtual {v0}, Landroidx/media3/exoplayer/upstream/Loader;->i()Z

    move-result v0

    return v0
.end method

.method public j(I)I
    .locals 3

    invoke-virtual {p0}, Landroidx/media3/exoplayer/hls/q;->i()V

    iget-object v0, p0, Landroidx/media3/exoplayer/hls/q;->K:[I

    invoke-static {v0}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    iget-object v0, p0, Landroidx/media3/exoplayer/hls/q;->K:[I

    aget v0, v0, p1

    const/4 v1, -0x1

    const/4 v2, -0x2

    if-ne v0, v1, :cond_1

    iget-object v0, p0, Landroidx/media3/exoplayer/hls/q;->J:Ljava/util/Set;

    iget-object v1, p0, Landroidx/media3/exoplayer/hls/q;->I:Lu2/k0;

    invoke-virtual {v1, p1}, Lu2/k0;->b(I)Landroidx/media3/common/n0;

    move-result-object p1

    invoke-interface {v0, p1}, Ljava/util/Set;->contains(Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_0

    const/4 v2, -0x3

    :cond_0
    return v2

    :cond_1
    iget-object p1, p0, Landroidx/media3/exoplayer/hls/q;->N:[Z

    aget-boolean v1, p1, v0

    if-eqz v1, :cond_2

    return v2

    :cond_2
    const/4 v1, 0x1

    aput-boolean v1, p1, v0

    return v0
.end method

.method public bridge synthetic k(Landroidx/media3/exoplayer/upstream/Loader$d;JJLjava/io/IOException;I)Landroidx/media3/exoplayer/upstream/Loader$c;
    .locals 0

    check-cast p1, Lv2/e;

    invoke-virtual/range {p0 .. p7}, Landroidx/media3/exoplayer/hls/q;->M(Lv2/e;JJLjava/io/IOException;I)Landroidx/media3/exoplayer/upstream/Loader$c;

    move-result-object p1

    return-object p1
.end method

.method public final l()V
    .locals 15

    iget-object v0, p0, Landroidx/media3/exoplayer/hls/q;->v:[Landroidx/media3/exoplayer/hls/q$d;

    array-length v0, v0

    const/4 v1, -0x2

    const/4 v2, -0x1

    const/4 v3, 0x0

    const/4 v4, 0x0

    const/4 v5, -0x2

    const/4 v6, -0x1

    :goto_0
    const/4 v7, 0x2

    const/4 v8, 0x1

    if-ge v4, v0, :cond_5

    iget-object v9, p0, Landroidx/media3/exoplayer/hls/q;->v:[Landroidx/media3/exoplayer/hls/q$d;

    aget-object v9, v9, v4

    invoke-virtual {v9}, Landroidx/media3/exoplayer/source/s;->G()Landroidx/media3/common/y;

    move-result-object v9

    invoke-static {v9}, Le2/a;->i(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v9

    check-cast v9, Landroidx/media3/common/y;

    iget-object v9, v9, Landroidx/media3/common/y;->m:Ljava/lang/String;

    invoke-static {v9}, Landroidx/media3/common/f0;->s(Ljava/lang/String;)Z

    move-result v10

    if-eqz v10, :cond_0

    goto :goto_1

    :cond_0
    invoke-static {v9}, Landroidx/media3/common/f0;->o(Ljava/lang/String;)Z

    move-result v7

    if-eqz v7, :cond_1

    const/4 v7, 0x1

    goto :goto_1

    :cond_1
    invoke-static {v9}, Landroidx/media3/common/f0;->r(Ljava/lang/String;)Z

    move-result v7

    if-eqz v7, :cond_2

    const/4 v7, 0x3

    goto :goto_1

    :cond_2
    const/4 v7, -0x2

    :goto_1
    invoke-static {v7}, Landroidx/media3/exoplayer/hls/q;->A(I)I

    move-result v8

    invoke-static {v5}, Landroidx/media3/exoplayer/hls/q;->A(I)I

    move-result v9

    if-le v8, v9, :cond_3

    move v6, v4

    move v5, v7

    goto :goto_2

    :cond_3
    if-ne v7, v5, :cond_4

    if-eq v6, v2, :cond_4

    const/4 v6, -0x1

    :cond_4
    :goto_2
    add-int/lit8 v4, v4, 0x1

    goto :goto_0

    :cond_5
    iget-object v1, p0, Landroidx/media3/exoplayer/hls/q;->d:Landroidx/media3/exoplayer/hls/e;

    invoke-virtual {v1}, Landroidx/media3/exoplayer/hls/e;->j()Landroidx/media3/common/n0;

    move-result-object v1

    iget v4, v1, Landroidx/media3/common/n0;->a:I

    iput v2, p0, Landroidx/media3/exoplayer/hls/q;->L:I

    new-array v2, v0, [I

    iput-object v2, p0, Landroidx/media3/exoplayer/hls/q;->K:[I

    const/4 v2, 0x0

    :goto_3
    if-ge v2, v0, :cond_6

    iget-object v9, p0, Landroidx/media3/exoplayer/hls/q;->K:[I

    aput v2, v9, v2

    add-int/lit8 v2, v2, 0x1

    goto :goto_3

    :cond_6
    new-array v2, v0, [Landroidx/media3/common/n0;

    const/4 v9, 0x0

    :goto_4
    if-ge v9, v0, :cond_d

    iget-object v10, p0, Landroidx/media3/exoplayer/hls/q;->v:[Landroidx/media3/exoplayer/hls/q$d;

    aget-object v10, v10, v9

    invoke-virtual {v10}, Landroidx/media3/exoplayer/source/s;->G()Landroidx/media3/common/y;

    move-result-object v10

    invoke-static {v10}, Le2/a;->i(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v10

    check-cast v10, Landroidx/media3/common/y;

    if-ne v9, v6, :cond_a

    new-array v11, v4, [Landroidx/media3/common/y;

    const/4 v12, 0x0

    :goto_5
    if-ge v12, v4, :cond_9

    invoke-virtual {v1, v12}, Landroidx/media3/common/n0;->a(I)Landroidx/media3/common/y;

    move-result-object v13

    if-ne v5, v8, :cond_7

    iget-object v14, p0, Landroidx/media3/exoplayer/hls/q;->f:Landroidx/media3/common/y;

    if-eqz v14, :cond_7

    invoke-virtual {v13, v14}, Landroidx/media3/common/y;->n(Landroidx/media3/common/y;)Landroidx/media3/common/y;

    move-result-object v13

    :cond_7
    if-ne v4, v8, :cond_8

    invoke-virtual {v10, v13}, Landroidx/media3/common/y;->n(Landroidx/media3/common/y;)Landroidx/media3/common/y;

    move-result-object v13

    goto :goto_6

    :cond_8
    invoke-static {v13, v10, v8}, Landroidx/media3/exoplayer/hls/q;->t(Landroidx/media3/common/y;Landroidx/media3/common/y;Z)Landroidx/media3/common/y;

    move-result-object v13

    :goto_6
    aput-object v13, v11, v12

    add-int/lit8 v12, v12, 0x1

    goto :goto_5

    :cond_9
    new-instance v10, Landroidx/media3/common/n0;

    iget-object v12, p0, Landroidx/media3/exoplayer/hls/q;->a:Ljava/lang/String;

    invoke-direct {v10, v12, v11}, Landroidx/media3/common/n0;-><init>(Ljava/lang/String;[Landroidx/media3/common/y;)V

    aput-object v10, v2, v9

    iput v9, p0, Landroidx/media3/exoplayer/hls/q;->L:I

    goto :goto_9

    :cond_a
    if-ne v5, v7, :cond_b

    iget-object v11, v10, Landroidx/media3/common/y;->m:Ljava/lang/String;

    invoke-static {v11}, Landroidx/media3/common/f0;->o(Ljava/lang/String;)Z

    move-result v11

    if-eqz v11, :cond_b

    iget-object v11, p0, Landroidx/media3/exoplayer/hls/q;->f:Landroidx/media3/common/y;

    goto :goto_7

    :cond_b
    const/4 v11, 0x0

    :goto_7
    new-instance v12, Ljava/lang/StringBuilder;

    invoke-direct {v12}, Ljava/lang/StringBuilder;-><init>()V

    iget-object v13, p0, Landroidx/media3/exoplayer/hls/q;->a:Ljava/lang/String;

    invoke-virtual {v12, v13}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v13, ":muxed:"

    invoke-virtual {v12, v13}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    if-ge v9, v6, :cond_c

    move v13, v9

    goto :goto_8

    :cond_c
    add-int/lit8 v13, v9, -0x1

    :goto_8
    invoke-virtual {v12, v13}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v12}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v12

    new-instance v13, Landroidx/media3/common/n0;

    new-array v14, v8, [Landroidx/media3/common/y;

    invoke-static {v11, v10, v3}, Landroidx/media3/exoplayer/hls/q;->t(Landroidx/media3/common/y;Landroidx/media3/common/y;Z)Landroidx/media3/common/y;

    move-result-object v10

    aput-object v10, v14, v3

    invoke-direct {v13, v12, v14}, Landroidx/media3/common/n0;-><init>(Ljava/lang/String;[Landroidx/media3/common/y;)V

    aput-object v13, v2, v9

    :goto_9
    add-int/lit8 v9, v9, 0x1

    goto :goto_4

    :cond_d
    invoke-virtual {p0, v2}, Landroidx/media3/exoplayer/hls/q;->s([Landroidx/media3/common/n0;)Lu2/k0;

    move-result-object v0

    iput-object v0, p0, Landroidx/media3/exoplayer/hls/q;->I:Lu2/k0;

    iget-object v0, p0, Landroidx/media3/exoplayer/hls/q;->J:Ljava/util/Set;

    if-nez v0, :cond_e

    const/4 v3, 0x1

    :cond_e
    invoke-static {v3}, Le2/a;->g(Z)V

    invoke-static {}, Ljava/util/Collections;->emptySet()Ljava/util/Set;

    move-result-object v0

    iput-object v0, p0, Landroidx/media3/exoplayer/hls/q;->J:Ljava/util/Set;

    return-void
.end method

.method public final m(I)Z
    .locals 4

    move v0, p1

    :goto_0
    iget-object v1, p0, Landroidx/media3/exoplayer/hls/q;->n:Ljava/util/ArrayList;

    invoke-virtual {v1}, Ljava/util/ArrayList;->size()I

    move-result v1

    const/4 v2, 0x0

    if-ge v0, v1, :cond_1

    iget-object v1, p0, Landroidx/media3/exoplayer/hls/q;->n:Ljava/util/ArrayList;

    invoke-virtual {v1, v0}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Landroidx/media3/exoplayer/hls/i;

    iget-boolean v1, v1, Landroidx/media3/exoplayer/hls/i;->n:Z

    if-eqz v1, :cond_0

    return v2

    :cond_0
    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :cond_1
    iget-object v0, p0, Landroidx/media3/exoplayer/hls/q;->n:Ljava/util/ArrayList;

    invoke-virtual {v0, p1}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Landroidx/media3/exoplayer/hls/i;

    const/4 v0, 0x0

    :goto_1
    iget-object v1, p0, Landroidx/media3/exoplayer/hls/q;->v:[Landroidx/media3/exoplayer/hls/q$d;

    array-length v1, v1

    if-ge v0, v1, :cond_3

    invoke-virtual {p1, v0}, Landroidx/media3/exoplayer/hls/i;->k(I)I

    move-result v1

    iget-object v3, p0, Landroidx/media3/exoplayer/hls/q;->v:[Landroidx/media3/exoplayer/hls/q$d;

    aget-object v3, v3, v0

    invoke-virtual {v3}, Landroidx/media3/exoplayer/source/s;->D()I

    move-result v3

    if-le v3, v1, :cond_2

    return v2

    :cond_2
    add-int/lit8 v0, v0, 0x1

    goto :goto_1

    :cond_3
    const/4 p1, 0x1

    return p1
.end method

.method public maybeThrowPrepareError()V
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    invoke-virtual {p0}, Landroidx/media3/exoplayer/hls/q;->I()V

    iget-boolean v0, p0, Landroidx/media3/exoplayer/hls/q;->T:Z

    if-eqz v0, :cond_1

    iget-boolean v0, p0, Landroidx/media3/exoplayer/hls/q;->D:Z

    if-eqz v0, :cond_0

    goto :goto_0

    :cond_0
    const-string v0, "Loading finished before preparation is complete."

    const/4 v1, 0x0

    invoke-static {v0, v1}, Landroidx/media3/common/ParserException;->createForMalformedContainer(Ljava/lang/String;Ljava/lang/Throwable;)Landroidx/media3/common/ParserException;

    move-result-object v0

    throw v0

    :cond_1
    :goto_0
    return-void
.end method

.method public bridge synthetic n(Landroidx/media3/exoplayer/upstream/Loader$d;JJ)V
    .locals 0

    check-cast p1, Lv2/e;

    invoke-virtual/range {p0 .. p5}, Landroidx/media3/exoplayer/hls/q;->L(Lv2/e;JJ)V

    return-void
.end method

.method public bridge synthetic o(Landroidx/media3/exoplayer/upstream/Loader$d;JJZ)V
    .locals 0

    check-cast p1, Lv2/e;

    invoke-virtual/range {p0 .. p6}, Landroidx/media3/exoplayer/hls/q;->K(Lv2/e;JJZ)V

    return-void
.end method

.method public onLoaderReleased()V
    .locals 4

    iget-object v0, p0, Landroidx/media3/exoplayer/hls/q;->v:[Landroidx/media3/exoplayer/hls/q$d;

    array-length v1, v0

    const/4 v2, 0x0

    :goto_0
    if-ge v2, v1, :cond_0

    aget-object v3, v0, v2

    invoke-virtual {v3}, Landroidx/media3/exoplayer/source/s;->U()V

    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_0
    return-void
.end method

.method public p()V
    .locals 3

    iget-boolean v0, p0, Landroidx/media3/exoplayer/hls/q;->D:Z

    if-nez v0, :cond_0

    new-instance v0, Landroidx/media3/exoplayer/w1$b;

    invoke-direct {v0}, Landroidx/media3/exoplayer/w1$b;-><init>()V

    iget-wide v1, p0, Landroidx/media3/exoplayer/hls/q;->P:J

    invoke-virtual {v0, v1, v2}, Landroidx/media3/exoplayer/w1$b;->f(J)Landroidx/media3/exoplayer/w1$b;

    move-result-object v0

    invoke-virtual {v0}, Landroidx/media3/exoplayer/w1$b;->d()Landroidx/media3/exoplayer/w1;

    move-result-object v0

    invoke-virtual {p0, v0}, Landroidx/media3/exoplayer/hls/q;->a(Landroidx/media3/exoplayer/w1;)Z

    :cond_0
    return-void
.end method

.method public final r(II)Landroidx/media3/exoplayer/source/s;
    .locals 9

    iget-object v0, p0, Landroidx/media3/exoplayer/hls/q;->v:[Landroidx/media3/exoplayer/hls/q$d;

    array-length v0, v0

    const/4 v1, 0x1

    if-eq p2, v1, :cond_1

    const/4 v2, 0x2

    if-ne p2, v2, :cond_0

    goto :goto_0

    :cond_0
    const/4 v1, 0x0

    :cond_1
    :goto_0
    new-instance v8, Landroidx/media3/exoplayer/hls/q$d;

    iget-object v3, p0, Landroidx/media3/exoplayer/hls/q;->e:Landroidx/media3/exoplayer/upstream/b;

    iget-object v4, p0, Landroidx/media3/exoplayer/hls/q;->g:Landroidx/media3/exoplayer/drm/c;

    iget-object v5, p0, Landroidx/media3/exoplayer/hls/q;->h:Landroidx/media3/exoplayer/drm/b$a;

    iget-object v6, p0, Landroidx/media3/exoplayer/hls/q;->t:Ljava/util/Map;

    const/4 v7, 0x0

    move-object v2, v8

    invoke-direct/range {v2 .. v7}, Landroidx/media3/exoplayer/hls/q$d;-><init>(Landroidx/media3/exoplayer/upstream/b;Landroidx/media3/exoplayer/drm/c;Landroidx/media3/exoplayer/drm/b$a;Ljava/util/Map;Landroidx/media3/exoplayer/hls/q$a;)V

    iget-wide v2, p0, Landroidx/media3/exoplayer/hls/q;->P:J

    invoke-virtual {v8, v2, v3}, Landroidx/media3/exoplayer/source/s;->c0(J)V

    if-eqz v1, :cond_2

    iget-object v2, p0, Landroidx/media3/exoplayer/hls/q;->W:Landroidx/media3/common/DrmInitData;

    invoke-virtual {v8, v2}, Landroidx/media3/exoplayer/hls/q$d;->j0(Landroidx/media3/common/DrmInitData;)V

    :cond_2
    iget-wide v2, p0, Landroidx/media3/exoplayer/hls/q;->V:J

    invoke-virtual {v8, v2, v3}, Landroidx/media3/exoplayer/source/s;->b0(J)V

    iget-object v2, p0, Landroidx/media3/exoplayer/hls/q;->X:Landroidx/media3/exoplayer/hls/i;

    if-eqz v2, :cond_3

    invoke-virtual {v8, v2}, Landroidx/media3/exoplayer/hls/q$d;->k0(Landroidx/media3/exoplayer/hls/i;)V

    :cond_3
    invoke-virtual {v8, p0}, Landroidx/media3/exoplayer/source/s;->e0(Landroidx/media3/exoplayer/source/s$d;)V

    iget-object v2, p0, Landroidx/media3/exoplayer/hls/q;->w:[I

    add-int/lit8 v3, v0, 0x1

    invoke-static {v2, v3}, Ljava/util/Arrays;->copyOf([II)[I

    move-result-object v2

    iput-object v2, p0, Landroidx/media3/exoplayer/hls/q;->w:[I

    aput p1, v2, v0

    iget-object p1, p0, Landroidx/media3/exoplayer/hls/q;->v:[Landroidx/media3/exoplayer/hls/q$d;

    invoke-static {p1, v8}, Le2/u0;->V0([Ljava/lang/Object;Ljava/lang/Object;)[Ljava/lang/Object;

    move-result-object p1

    check-cast p1, [Landroidx/media3/exoplayer/hls/q$d;

    iput-object p1, p0, Landroidx/media3/exoplayer/hls/q;->v:[Landroidx/media3/exoplayer/hls/q$d;

    iget-object p1, p0, Landroidx/media3/exoplayer/hls/q;->O:[Z

    invoke-static {p1, v3}, Ljava/util/Arrays;->copyOf([ZI)[Z

    move-result-object p1

    iput-object p1, p0, Landroidx/media3/exoplayer/hls/q;->O:[Z

    aput-boolean v1, p1, v0

    iget-boolean p1, p0, Landroidx/media3/exoplayer/hls/q;->M:Z

    or-int/2addr p1, v1

    iput-boolean p1, p0, Landroidx/media3/exoplayer/hls/q;->M:Z

    iget-object p1, p0, Landroidx/media3/exoplayer/hls/q;->x:Ljava/util/Set;

    invoke-static {p2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    invoke-interface {p1, v1}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    iget-object p1, p0, Landroidx/media3/exoplayer/hls/q;->y:Landroid/util/SparseIntArray;

    invoke-virtual {p1, p2, v0}, Landroid/util/SparseIntArray;->append(II)V

    invoke-static {p2}, Landroidx/media3/exoplayer/hls/q;->A(I)I

    move-result p1

    iget v1, p0, Landroidx/media3/exoplayer/hls/q;->A:I

    invoke-static {v1}, Landroidx/media3/exoplayer/hls/q;->A(I)I

    move-result v1

    if-le p1, v1, :cond_4

    iput v0, p0, Landroidx/media3/exoplayer/hls/q;->B:I

    iput p2, p0, Landroidx/media3/exoplayer/hls/q;->A:I

    :cond_4
    iget-object p1, p0, Landroidx/media3/exoplayer/hls/q;->N:[Z

    invoke-static {p1, v3}, Ljava/util/Arrays;->copyOf([ZI)[Z

    move-result-object p1

    iput-object p1, p0, Landroidx/media3/exoplayer/hls/q;->N:[Z

    return-object v8
.end method

.method public reevaluateBuffer(J)V
    .locals 4

    iget-object v0, p0, Landroidx/media3/exoplayer/hls/q;->j:Landroidx/media3/exoplayer/upstream/Loader;

    invoke-virtual {v0}, Landroidx/media3/exoplayer/upstream/Loader;->h()Z

    move-result v0

    if-nez v0, :cond_5

    invoke-direct {p0}, Landroidx/media3/exoplayer/hls/q;->D()Z

    move-result v0

    if-eqz v0, :cond_0

    goto :goto_1

    :cond_0
    iget-object v0, p0, Landroidx/media3/exoplayer/hls/q;->j:Landroidx/media3/exoplayer/upstream/Loader;

    invoke-virtual {v0}, Landroidx/media3/exoplayer/upstream/Loader;->i()Z

    move-result v0

    if-eqz v0, :cond_2

    iget-object v0, p0, Landroidx/media3/exoplayer/hls/q;->u:Lv2/e;

    invoke-static {v0}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    iget-object v0, p0, Landroidx/media3/exoplayer/hls/q;->d:Landroidx/media3/exoplayer/hls/e;

    iget-object v1, p0, Landroidx/media3/exoplayer/hls/q;->u:Lv2/e;

    iget-object v2, p0, Landroidx/media3/exoplayer/hls/q;->o:Ljava/util/List;

    invoke-virtual {v0, p1, p2, v1, v2}, Landroidx/media3/exoplayer/hls/e;->w(JLv2/e;Ljava/util/List;)Z

    move-result p1

    if-eqz p1, :cond_1

    iget-object p1, p0, Landroidx/media3/exoplayer/hls/q;->j:Landroidx/media3/exoplayer/upstream/Loader;

    invoke-virtual {p1}, Landroidx/media3/exoplayer/upstream/Loader;->e()V

    :cond_1
    return-void

    :cond_2
    iget-object v0, p0, Landroidx/media3/exoplayer/hls/q;->o:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    :goto_0
    if-lez v0, :cond_3

    iget-object v1, p0, Landroidx/media3/exoplayer/hls/q;->d:Landroidx/media3/exoplayer/hls/e;

    iget-object v2, p0, Landroidx/media3/exoplayer/hls/q;->o:Ljava/util/List;

    add-int/lit8 v3, v0, -0x1

    invoke-interface {v2, v3}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Landroidx/media3/exoplayer/hls/i;

    invoke-virtual {v1, v2}, Landroidx/media3/exoplayer/hls/e;->c(Landroidx/media3/exoplayer/hls/i;)I

    move-result v1

    const/4 v2, 0x2

    if-ne v1, v2, :cond_3

    add-int/lit8 v0, v0, -0x1

    goto :goto_0

    :cond_3
    iget-object v1, p0, Landroidx/media3/exoplayer/hls/q;->o:Ljava/util/List;

    invoke-interface {v1}, Ljava/util/List;->size()I

    move-result v1

    if-ge v0, v1, :cond_4

    invoke-virtual {p0, v0}, Landroidx/media3/exoplayer/hls/q;->u(I)V

    :cond_4
    iget-object v0, p0, Landroidx/media3/exoplayer/hls/q;->d:Landroidx/media3/exoplayer/hls/e;

    iget-object v1, p0, Landroidx/media3/exoplayer/hls/q;->o:Ljava/util/List;

    invoke-virtual {v0, p1, p2, v1}, Landroidx/media3/exoplayer/hls/e;->h(JLjava/util/List;)I

    move-result p1

    iget-object p2, p0, Landroidx/media3/exoplayer/hls/q;->n:Ljava/util/ArrayList;

    invoke-virtual {p2}, Ljava/util/ArrayList;->size()I

    move-result p2

    if-ge p1, p2, :cond_5

    invoke-virtual {p0, p1}, Landroidx/media3/exoplayer/hls/q;->u(I)V

    :cond_5
    :goto_1
    return-void
.end method

.method public final s([Landroidx/media3/common/n0;)Lu2/k0;
    .locals 7

    const/4 v0, 0x0

    const/4 v1, 0x0

    :goto_0
    array-length v2, p1

    if-ge v1, v2, :cond_1

    aget-object v2, p1, v1

    iget v3, v2, Landroidx/media3/common/n0;->a:I

    new-array v3, v3, [Landroidx/media3/common/y;

    const/4 v4, 0x0

    :goto_1
    iget v5, v2, Landroidx/media3/common/n0;->a:I

    if-ge v4, v5, :cond_0

    invoke-virtual {v2, v4}, Landroidx/media3/common/n0;->a(I)Landroidx/media3/common/y;

    move-result-object v5

    iget-object v6, p0, Landroidx/media3/exoplayer/hls/q;->g:Landroidx/media3/exoplayer/drm/c;

    invoke-interface {v6, v5}, Landroidx/media3/exoplayer/drm/c;->c(Landroidx/media3/common/y;)I

    move-result v6

    invoke-virtual {v5, v6}, Landroidx/media3/common/y;->c(I)Landroidx/media3/common/y;

    move-result-object v5

    aput-object v5, v3, v4

    add-int/lit8 v4, v4, 0x1

    goto :goto_1

    :cond_0
    new-instance v4, Landroidx/media3/common/n0;

    iget-object v2, v2, Landroidx/media3/common/n0;->b:Ljava/lang/String;

    invoke-direct {v4, v2, v3}, Landroidx/media3/common/n0;-><init>(Ljava/lang/String;[Landroidx/media3/common/y;)V

    aput-object v4, p1, v1

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_1
    new-instance v0, Lu2/k0;

    invoke-direct {v0, p1}, Lu2/k0;-><init>([Landroidx/media3/common/n0;)V

    return-object v0
.end method

.method public track(II)Lz2/r0;
    .locals 3

    sget-object v0, Landroidx/media3/exoplayer/hls/q;->Y:Ljava/util/Set;

    invoke-static {p2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    invoke-interface {v0, v1}, Ljava/util/Set;->contains(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_0

    invoke-virtual {p0, p1, p2}, Landroidx/media3/exoplayer/hls/q;->z(II)Lz2/r0;

    move-result-object v0

    goto :goto_1

    :cond_0
    const/4 v0, 0x0

    :goto_0
    iget-object v1, p0, Landroidx/media3/exoplayer/hls/q;->v:[Landroidx/media3/exoplayer/hls/q$d;

    array-length v2, v1

    if-ge v0, v2, :cond_2

    iget-object v2, p0, Landroidx/media3/exoplayer/hls/q;->w:[I

    aget v2, v2, v0

    if-ne v2, p1, :cond_1

    aget-object v0, v1, v0

    goto :goto_1

    :cond_1
    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :cond_2
    const/4 v0, 0x0

    :goto_1
    if-nez v0, :cond_4

    iget-boolean v0, p0, Landroidx/media3/exoplayer/hls/q;->U:Z

    if-eqz v0, :cond_3

    invoke-static {p1, p2}, Landroidx/media3/exoplayer/hls/q;->q(II)Lz2/q;

    move-result-object p1

    return-object p1

    :cond_3
    invoke-virtual {p0, p1, p2}, Landroidx/media3/exoplayer/hls/q;->r(II)Landroidx/media3/exoplayer/source/s;

    move-result-object v0

    :cond_4
    const/4 p1, 0x5

    if-ne p2, p1, :cond_6

    iget-object p1, p0, Landroidx/media3/exoplayer/hls/q;->z:Lz2/r0;

    if-nez p1, :cond_5

    new-instance p1, Landroidx/media3/exoplayer/hls/q$c;

    iget p2, p0, Landroidx/media3/exoplayer/hls/q;->l:I

    invoke-direct {p1, v0, p2}, Landroidx/media3/exoplayer/hls/q$c;-><init>(Lz2/r0;I)V

    iput-object p1, p0, Landroidx/media3/exoplayer/hls/q;->z:Lz2/r0;

    :cond_5
    iget-object p1, p0, Landroidx/media3/exoplayer/hls/q;->z:Lz2/r0;

    return-object p1

    :cond_6
    return-object v0
.end method

.method public final u(I)V
    .locals 7

    iget-object v0, p0, Landroidx/media3/exoplayer/hls/q;->j:Landroidx/media3/exoplayer/upstream/Loader;

    invoke-virtual {v0}, Landroidx/media3/exoplayer/upstream/Loader;->i()Z

    move-result v0

    xor-int/lit8 v0, v0, 0x1

    invoke-static {v0}, Le2/a;->g(Z)V

    :goto_0
    iget-object v0, p0, Landroidx/media3/exoplayer/hls/q;->n:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->size()I

    move-result v0

    const/4 v1, -0x1

    if-ge p1, v0, :cond_1

    invoke-virtual {p0, p1}, Landroidx/media3/exoplayer/hls/q;->m(I)Z

    move-result v0

    if-eqz v0, :cond_0

    goto :goto_1

    :cond_0
    add-int/lit8 p1, p1, 0x1

    goto :goto_0

    :cond_1
    const/4 p1, -0x1

    :goto_1
    if-ne p1, v1, :cond_2

    return-void

    :cond_2
    invoke-virtual {p0}, Landroidx/media3/exoplayer/hls/q;->y()Landroidx/media3/exoplayer/hls/i;

    move-result-object v0

    iget-wide v5, v0, Lv2/e;->h:J

    invoke-virtual {p0, p1}, Landroidx/media3/exoplayer/hls/q;->v(I)Landroidx/media3/exoplayer/hls/i;

    move-result-object p1

    iget-object v0, p0, Landroidx/media3/exoplayer/hls/q;->n:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_3

    iget-wide v0, p0, Landroidx/media3/exoplayer/hls/q;->P:J

    iput-wide v0, p0, Landroidx/media3/exoplayer/hls/q;->Q:J

    goto :goto_2

    :cond_3
    iget-object v0, p0, Landroidx/media3/exoplayer/hls/q;->n:Ljava/util/ArrayList;

    invoke-static {v0}, Lcom/google/common/collect/e0;->g(Ljava/lang/Iterable;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroidx/media3/exoplayer/hls/i;

    invoke-virtual {v0}, Landroidx/media3/exoplayer/hls/i;->m()V

    :goto_2
    const/4 v0, 0x0

    iput-boolean v0, p0, Landroidx/media3/exoplayer/hls/q;->T:Z

    iget-object v1, p0, Landroidx/media3/exoplayer/hls/q;->k:Landroidx/media3/exoplayer/source/m$a;

    iget v2, p0, Landroidx/media3/exoplayer/hls/q;->A:I

    iget-wide v3, p1, Lv2/e;->g:J

    invoke-virtual/range {v1 .. v6}, Landroidx/media3/exoplayer/source/m$a;->C(IJJ)V

    return-void
.end method

.method public final v(I)Landroidx/media3/exoplayer/hls/i;
    .locals 3

    iget-object v0, p0, Landroidx/media3/exoplayer/hls/q;->n:Ljava/util/ArrayList;

    invoke-virtual {v0, p1}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroidx/media3/exoplayer/hls/i;

    iget-object v1, p0, Landroidx/media3/exoplayer/hls/q;->n:Ljava/util/ArrayList;

    invoke-virtual {v1}, Ljava/util/ArrayList;->size()I

    move-result v2

    invoke-static {v1, p1, v2}, Le2/u0;->d1(Ljava/util/List;II)V

    const/4 p1, 0x0

    :goto_0
    iget-object v1, p0, Landroidx/media3/exoplayer/hls/q;->v:[Landroidx/media3/exoplayer/hls/q$d;

    array-length v1, v1

    if-ge p1, v1, :cond_0

    invoke-virtual {v0, p1}, Landroidx/media3/exoplayer/hls/i;->k(I)I

    move-result v1

    iget-object v2, p0, Landroidx/media3/exoplayer/hls/q;->v:[Landroidx/media3/exoplayer/hls/q$d;

    aget-object v2, v2, p1

    invoke-virtual {v2, v1}, Landroidx/media3/exoplayer/source/s;->u(I)V

    add-int/lit8 p1, p1, 0x1

    goto :goto_0

    :cond_0
    return-object v0
.end method

.method public final w(Landroidx/media3/exoplayer/hls/i;)Z
    .locals 8

    iget p1, p1, Landroidx/media3/exoplayer/hls/i;->k:I

    iget-object v0, p0, Landroidx/media3/exoplayer/hls/q;->v:[Landroidx/media3/exoplayer/hls/q$d;

    array-length v0, v0

    const/4 v1, 0x0

    const/4 v2, 0x0

    :goto_0
    if-ge v2, v0, :cond_1

    iget-object v3, p0, Landroidx/media3/exoplayer/hls/q;->N:[Z

    aget-boolean v3, v3, v2

    if-eqz v3, :cond_0

    iget-object v3, p0, Landroidx/media3/exoplayer/hls/q;->v:[Landroidx/media3/exoplayer/hls/q$d;

    aget-object v3, v3, v2

    invoke-virtual {v3}, Landroidx/media3/exoplayer/source/s;->R()J

    move-result-wide v3

    int-to-long v5, p1

    cmp-long v7, v3, v5

    if-nez v7, :cond_0

    return v1

    :cond_0
    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_1
    const/4 p1, 0x1

    return p1
.end method

.method public final y()Landroidx/media3/exoplayer/hls/i;
    .locals 2

    iget-object v0, p0, Landroidx/media3/exoplayer/hls/q;->n:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->size()I

    move-result v1

    add-int/lit8 v1, v1, -0x1

    invoke-virtual {v0, v1}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroidx/media3/exoplayer/hls/i;

    return-object v0
.end method

.method public final z(II)Lz2/r0;
    .locals 3
    .annotation build Landroidx/annotation/Nullable;
    .end annotation

    sget-object v0, Landroidx/media3/exoplayer/hls/q;->Y:Ljava/util/Set;

    invoke-static {p2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    invoke-interface {v0, v1}, Ljava/util/Set;->contains(Ljava/lang/Object;)Z

    move-result v0

    invoke-static {v0}, Le2/a;->a(Z)V

    iget-object v0, p0, Landroidx/media3/exoplayer/hls/q;->y:Landroid/util/SparseIntArray;

    const/4 v1, -0x1

    invoke-virtual {v0, p2, v1}, Landroid/util/SparseIntArray;->get(II)I

    move-result v0

    if-ne v0, v1, :cond_0

    const/4 p1, 0x0

    return-object p1

    :cond_0
    iget-object v1, p0, Landroidx/media3/exoplayer/hls/q;->x:Ljava/util/Set;

    invoke-static {p2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v2

    invoke-interface {v1, v2}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_1

    iget-object v1, p0, Landroidx/media3/exoplayer/hls/q;->w:[I

    aput p1, v1, v0

    :cond_1
    iget-object v1, p0, Landroidx/media3/exoplayer/hls/q;->w:[I

    aget v1, v1, v0

    if-ne v1, p1, :cond_2

    iget-object p1, p0, Landroidx/media3/exoplayer/hls/q;->v:[Landroidx/media3/exoplayer/hls/q$d;

    aget-object p1, p1, v0

    goto :goto_0

    :cond_2
    invoke-static {p1, p2}, Landroidx/media3/exoplayer/hls/q;->q(II)Lz2/q;

    move-result-object p1

    :goto_0
    return-object p1
.end method
