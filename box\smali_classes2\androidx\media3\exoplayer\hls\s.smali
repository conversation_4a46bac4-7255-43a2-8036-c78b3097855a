.class public final Landroidx/media3/exoplayer/hls/s;
.super Ljava/lang/Object;

# interfaces
.implements Lz2/s;


# static fields
.field public static final i:Ljava/util/regex/Pattern;

.field public static final j:Ljava/util/regex/Pattern;


# instance fields
.field public final a:Ljava/lang/String;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public final b:Le2/i0;

.field public final c:Le2/c0;

.field public final d:Lt3/s$a;

.field public final e:Z

.field public f:Lz2/u;

.field public g:[B

.field public h:I


# direct methods
.method static constructor <clinit>()V
    .locals 1

    const-string v0, "LOCAL:([^,]+)"

    invoke-static {v0}, Ljava/util/regex/Pattern;->compile(Ljava/lang/String;)Ljava/util/regex/Pattern;

    move-result-object v0

    sput-object v0, Landroidx/media3/exoplayer/hls/s;->i:Ljava/util/regex/Pattern;

    const-string v0, "MPEGTS:(-?\\d+)"

    invoke-static {v0}, Ljava/util/regex/Pattern;->compile(Ljava/lang/String;)Ljava/util/regex/Pattern;

    move-result-object v0

    sput-object v0, Landroidx/media3/exoplayer/hls/s;->j:Ljava/util/regex/Pattern;

    return-void
.end method

.method public constructor <init>(Ljava/lang/String;Le2/i0;Lt3/s$a;Z)V
    .locals 0
    .param p1    # Ljava/lang/String;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Landroidx/media3/exoplayer/hls/s;->a:Ljava/lang/String;

    iput-object p2, p0, Landroidx/media3/exoplayer/hls/s;->b:Le2/i0;

    new-instance p1, Le2/c0;

    invoke-direct {p1}, Le2/c0;-><init>()V

    iput-object p1, p0, Landroidx/media3/exoplayer/hls/s;->c:Le2/c0;

    const/16 p1, 0x400

    new-array p1, p1, [B

    iput-object p1, p0, Landroidx/media3/exoplayer/hls/s;->g:[B

    iput-object p3, p0, Landroidx/media3/exoplayer/hls/s;->d:Lt3/s$a;

    iput-boolean p4, p0, Landroidx/media3/exoplayer/hls/s;->e:Z

    return-void
.end method


# virtual methods
.method public final a(J)Lz2/r0;
    .locals 3

    iget-object v0, p0, Landroidx/media3/exoplayer/hls/s;->f:Lz2/u;

    const/4 v1, 0x0

    const/4 v2, 0x3

    invoke-interface {v0, v1, v2}, Lz2/u;->track(II)Lz2/r0;

    move-result-object v0

    new-instance v1, Landroidx/media3/common/y$b;

    invoke-direct {v1}, Landroidx/media3/common/y$b;-><init>()V

    const-string v2, "text/vtt"

    invoke-virtual {v1, v2}, Landroidx/media3/common/y$b;->k0(Ljava/lang/String;)Landroidx/media3/common/y$b;

    move-result-object v1

    iget-object v2, p0, Landroidx/media3/exoplayer/hls/s;->a:Ljava/lang/String;

    invoke-virtual {v1, v2}, Landroidx/media3/common/y$b;->b0(Ljava/lang/String;)Landroidx/media3/common/y$b;

    move-result-object v1

    invoke-virtual {v1, p1, p2}, Landroidx/media3/common/y$b;->o0(J)Landroidx/media3/common/y$b;

    move-result-object p1

    invoke-virtual {p1}, Landroidx/media3/common/y$b;->I()Landroidx/media3/common/y;

    move-result-object p1

    invoke-interface {v0, p1}, Lz2/r0;->a(Landroidx/media3/common/y;)V

    iget-object p1, p0, Landroidx/media3/exoplayer/hls/s;->f:Lz2/u;

    invoke-interface {p1}, Lz2/u;->endTracks()V

    return-object v0
.end method

.method public synthetic b()Lz2/s;
    .locals 1

    invoke-static {p0}, Lz2/r;->a(Lz2/s;)Lz2/s;

    move-result-object v0

    return-object v0
.end method

.method public c(Lz2/u;)V
    .locals 3

    iget-boolean v0, p0, Landroidx/media3/exoplayer/hls/s;->e:Z

    if-eqz v0, :cond_0

    new-instance v0, Lt3/u;

    iget-object v1, p0, Landroidx/media3/exoplayer/hls/s;->d:Lt3/s$a;

    invoke-direct {v0, p1, v1}, Lt3/u;-><init>(Lz2/u;Lt3/s$a;)V

    goto :goto_0

    :cond_0
    move-object v0, p1

    :goto_0
    iput-object v0, p0, Landroidx/media3/exoplayer/hls/s;->f:Lz2/u;

    new-instance v0, Lz2/m0$b;

    const-wide v1, -0x7fffffffffffffffL    # -4.9E-324

    invoke-direct {v0, v1, v2}, Lz2/m0$b;-><init>(J)V

    invoke-interface {p1, v0}, Lz2/u;->g(Lz2/m0;)V

    return-void
.end method

.method public d(Lz2/t;Lz2/l0;)I
    .locals 4
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget-object p2, p0, Landroidx/media3/exoplayer/hls/s;->f:Lz2/u;

    invoke-static {p2}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    invoke-interface {p1}, Lz2/t;->getLength()J

    move-result-wide v0

    long-to-int p2, v0

    iget v0, p0, Landroidx/media3/exoplayer/hls/s;->h:I

    iget-object v1, p0, Landroidx/media3/exoplayer/hls/s;->g:[B

    array-length v2, v1

    const/4 v3, -0x1

    if-ne v0, v2, :cond_1

    if-eq p2, v3, :cond_0

    move v0, p2

    goto :goto_0

    :cond_0
    array-length v0, v1

    :goto_0
    mul-int/lit8 v0, v0, 0x3

    div-int/lit8 v0, v0, 0x2

    invoke-static {v1, v0}, Ljava/util/Arrays;->copyOf([BI)[B

    move-result-object v0

    iput-object v0, p0, Landroidx/media3/exoplayer/hls/s;->g:[B

    :cond_1
    iget-object v0, p0, Landroidx/media3/exoplayer/hls/s;->g:[B

    iget v1, p0, Landroidx/media3/exoplayer/hls/s;->h:I

    array-length v2, v0

    sub-int/2addr v2, v1

    invoke-interface {p1, v0, v1, v2}, Lz2/t;->read([BII)I

    move-result p1

    if-eq p1, v3, :cond_3

    iget v0, p0, Landroidx/media3/exoplayer/hls/s;->h:I

    add-int/2addr v0, p1

    iput v0, p0, Landroidx/media3/exoplayer/hls/s;->h:I

    if-eq p2, v3, :cond_2

    if-eq v0, p2, :cond_3

    :cond_2
    const/4 p1, 0x0

    return p1

    :cond_3
    invoke-virtual {p0}, Landroidx/media3/exoplayer/hls/s;->f()V

    return v3
.end method

.method public e(Lz2/t;)Z
    .locals 4
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget-object v0, p0, Landroidx/media3/exoplayer/hls/s;->g:[B

    const/4 v1, 0x0

    const/4 v2, 0x6

    invoke-interface {p1, v0, v1, v2, v1}, Lz2/t;->peekFully([BIIZ)Z

    iget-object v0, p0, Landroidx/media3/exoplayer/hls/s;->c:Le2/c0;

    iget-object v3, p0, Landroidx/media3/exoplayer/hls/s;->g:[B

    invoke-virtual {v0, v3, v2}, Le2/c0;->S([BI)V

    iget-object v0, p0, Landroidx/media3/exoplayer/hls/s;->c:Le2/c0;

    invoke-static {v0}, Lb4/h;->b(Le2/c0;)Z

    move-result v0

    if-eqz v0, :cond_0

    const/4 p1, 0x1

    return p1

    :cond_0
    iget-object v0, p0, Landroidx/media3/exoplayer/hls/s;->g:[B

    const/4 v3, 0x3

    invoke-interface {p1, v0, v2, v3, v1}, Lz2/t;->peekFully([BIIZ)Z

    iget-object p1, p0, Landroidx/media3/exoplayer/hls/s;->c:Le2/c0;

    iget-object v0, p0, Landroidx/media3/exoplayer/hls/s;->g:[B

    const/16 v1, 0x9

    invoke-virtual {p1, v0, v1}, Le2/c0;->S([BI)V

    iget-object p1, p0, Landroidx/media3/exoplayer/hls/s;->c:Le2/c0;

    invoke-static {p1}, Lb4/h;->b(Le2/c0;)Z

    move-result p1

    return p1
.end method

.method public final f()V
    .locals 12
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/common/ParserException;
        }
    .end annotation

    new-instance v0, Le2/c0;

    iget-object v1, p0, Landroidx/media3/exoplayer/hls/s;->g:[B

    invoke-direct {v0, v1}, Le2/c0;-><init>([B)V

    invoke-static {v0}, Lb4/h;->e(Le2/c0;)V

    invoke-virtual {v0}, Le2/c0;->s()Ljava/lang/String;

    move-result-object v1

    const-wide/16 v2, 0x0

    move-wide v4, v2

    move-wide v6, v4

    :goto_0
    invoke-static {v1}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v8

    const/4 v9, 0x1

    if-nez v8, :cond_3

    const-string v8, "X-TIMESTAMP-MAP"

    invoke-virtual {v1, v8}, Ljava/lang/String;->startsWith(Ljava/lang/String;)Z

    move-result v8

    if-eqz v8, :cond_2

    sget-object v4, Landroidx/media3/exoplayer/hls/s;->i:Ljava/util/regex/Pattern;

    invoke-virtual {v4, v1}, Ljava/util/regex/Pattern;->matcher(Ljava/lang/CharSequence;)Ljava/util/regex/Matcher;

    move-result-object v4

    invoke-virtual {v4}, Ljava/util/regex/Matcher;->find()Z

    move-result v5

    const/4 v6, 0x0

    if-eqz v5, :cond_1

    sget-object v5, Landroidx/media3/exoplayer/hls/s;->j:Ljava/util/regex/Pattern;

    invoke-virtual {v5, v1}, Ljava/util/regex/Pattern;->matcher(Ljava/lang/CharSequence;)Ljava/util/regex/Matcher;

    move-result-object v5

    invoke-virtual {v5}, Ljava/util/regex/Matcher;->find()Z

    move-result v7

    if-eqz v7, :cond_0

    invoke-virtual {v4, v9}, Ljava/util/regex/Matcher;->group(I)Ljava/lang/String;

    move-result-object v1

    invoke-static {v1}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/lang/String;

    invoke-static {v1}, Lb4/h;->d(Ljava/lang/String;)J

    move-result-wide v6

    invoke-virtual {v5, v9}, Ljava/util/regex/Matcher;->group(I)Ljava/lang/String;

    move-result-object v1

    invoke-static {v1}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/lang/String;

    invoke-static {v1}, Ljava/lang/Long;->parseLong(Ljava/lang/String;)J

    move-result-wide v4

    invoke-static {v4, v5}, Le2/i0;->h(J)J

    move-result-wide v4

    goto :goto_1

    :cond_0
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "X-TIMESTAMP-MAP doesn\'t contain media timestamp: "

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-static {v0, v6}, Landroidx/media3/common/ParserException;->createForMalformedContainer(Ljava/lang/String;Ljava/lang/Throwable;)Landroidx/media3/common/ParserException;

    move-result-object v0

    throw v0

    :cond_1
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "X-TIMESTAMP-MAP doesn\'t contain local timestamp: "

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-static {v0, v6}, Landroidx/media3/common/ParserException;->createForMalformedContainer(Ljava/lang/String;Ljava/lang/Throwable;)Landroidx/media3/common/ParserException;

    move-result-object v0

    throw v0

    :cond_2
    :goto_1
    invoke-virtual {v0}, Le2/c0;->s()Ljava/lang/String;

    move-result-object v1

    goto :goto_0

    :cond_3
    invoke-static {v0}, Lb4/h;->a(Le2/c0;)Ljava/util/regex/Matcher;

    move-result-object v0

    if-nez v0, :cond_4

    invoke-virtual {p0, v2, v3}, Landroidx/media3/exoplayer/hls/s;->a(J)Lz2/r0;

    return-void

    :cond_4
    invoke-virtual {v0, v9}, Ljava/util/regex/Matcher;->group(I)Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/lang/String;

    invoke-static {v0}, Lb4/h;->d(Ljava/lang/String;)J

    move-result-wide v0

    iget-object v2, p0, Landroidx/media3/exoplayer/hls/s;->b:Le2/i0;

    add-long/2addr v4, v0

    sub-long/2addr v4, v6

    invoke-static {v4, v5}, Le2/i0;->l(J)J

    move-result-wide v3

    invoke-virtual {v2, v3, v4}, Le2/i0;->b(J)J

    move-result-wide v6

    sub-long v0, v6, v0

    invoke-virtual {p0, v0, v1}, Landroidx/media3/exoplayer/hls/s;->a(J)Lz2/r0;

    move-result-object v5

    iget-object v0, p0, Landroidx/media3/exoplayer/hls/s;->c:Le2/c0;

    iget-object v1, p0, Landroidx/media3/exoplayer/hls/s;->g:[B

    iget v2, p0, Landroidx/media3/exoplayer/hls/s;->h:I

    invoke-virtual {v0, v1, v2}, Le2/c0;->S([BI)V

    iget-object v0, p0, Landroidx/media3/exoplayer/hls/s;->c:Le2/c0;

    iget v1, p0, Landroidx/media3/exoplayer/hls/s;->h:I

    invoke-interface {v5, v0, v1}, Lz2/r0;->f(Le2/c0;I)V

    const/4 v8, 0x1

    iget v9, p0, Landroidx/media3/exoplayer/hls/s;->h:I

    const/4 v10, 0x0

    const/4 v11, 0x0

    invoke-interface/range {v5 .. v11}, Lz2/r0;->e(JIIILz2/r0$a;)V

    return-void
.end method

.method public release()V
    .locals 0

    return-void
.end method

.method public seek(JJ)V
    .locals 0

    new-instance p1, Ljava/lang/IllegalStateException;

    invoke-direct {p1}, Ljava/lang/IllegalStateException;-><init>()V

    throw p1
.end method
