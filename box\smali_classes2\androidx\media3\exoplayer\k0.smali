.class public final synthetic Landroidx/media3/exoplayer/k0;
.super Ljava/lang/Object;

# interfaces
.implements Le2/n$b;


# instance fields
.field public final synthetic a:Landroidx/media3/exoplayer/c1;


# direct methods
.method public synthetic constructor <init>(Landroidx/media3/exoplayer/c1;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Landroidx/media3/exoplayer/k0;->a:Landroidx/media3/exoplayer/c1;

    return-void
.end method


# virtual methods
.method public final a(Ljava/lang/Object;Landroidx/media3/common/s;)V
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/k0;->a:Landroidx/media3/exoplayer/c1;

    check-cast p1, Landroidx/media3/common/h0$d;

    invoke-static {v0, p1, p2}, Landroidx/media3/exoplayer/c1;->l0(Landroidx/media3/exoplayer/c1;Landroidx/media3/common/h0$d;Landroidx/media3/common/s;)V

    return-void
.end method
