.class public final synthetic Landroidx/media3/exoplayer/l0;
.super Ljava/lang/Object;

# interfaces
.implements Landroidx/media3/exoplayer/s1$f;


# instance fields
.field public final synthetic a:Landroidx/media3/exoplayer/c1;


# direct methods
.method public synthetic constructor <init>(Landroidx/media3/exoplayer/c1;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Landroidx/media3/exoplayer/l0;->a:Landroidx/media3/exoplayer/c1;

    return-void
.end method


# virtual methods
.method public final a(Landroidx/media3/exoplayer/s1$e;)V
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/l0;->a:Landroidx/media3/exoplayer/c1;

    invoke-static {v0, p1}, Landroidx/media3/exoplayer/c1;->v0(Landroidx/media3/exoplayer/c1;Landroidx/media3/exoplayer/s1$e;)V

    return-void
.end method
