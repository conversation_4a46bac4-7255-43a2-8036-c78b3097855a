.class public interface abstract Landroidx/media3/exoplayer/mediacodec/f;
.super Ljava/lang/Object;


# static fields
.field public static final a:Landroidx/media3/exoplayer/mediacodec/f;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    new-instance v0, Lr2/t;

    invoke-direct {v0}, Lr2/t;-><init>()V

    sput-object v0, Landroidx/media3/exoplayer/mediacodec/f;->a:Landroidx/media3/exoplayer/mediacodec/f;

    return-void
.end method


# virtual methods
.method public abstract a(Ljava/lang/String;ZZ)Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "ZZ)",
            "Ljava/util/List<",
            "Landroidx/media3/exoplayer/mediacodec/d;",
            ">;"
        }
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/exoplayer/mediacodec/MediaCodecUtil$DecoderQueryException;
        }
    .end annotation
.end method
