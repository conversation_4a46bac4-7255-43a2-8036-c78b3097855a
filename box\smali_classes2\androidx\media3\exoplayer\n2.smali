.class public final synthetic Landroidx/media3/exoplayer/n2;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/lang/Runnable;


# instance fields
.field public final synthetic a:Landroidx/media3/exoplayer/r2$a;

.field public final synthetic b:Landroid/util/Pair;

.field public final synthetic c:Lu2/n;

.field public final synthetic d:Lu2/o;


# direct methods
.method public synthetic constructor <init>(Landroidx/media3/exoplayer/r2$a;Landroid/util/Pair;Lu2/n;Lu2/o;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Landroidx/media3/exoplayer/n2;->a:Landroidx/media3/exoplayer/r2$a;

    iput-object p2, p0, Landroidx/media3/exoplayer/n2;->b:Landroid/util/Pair;

    iput-object p3, p0, Landroidx/media3/exoplayer/n2;->c:Lu2/n;

    iput-object p4, p0, Landroidx/media3/exoplayer/n2;->d:Lu2/o;

    return-void
.end method


# virtual methods
.method public final run()V
    .locals 4

    iget-object v0, p0, Landroidx/media3/exoplayer/n2;->a:Landroidx/media3/exoplayer/r2$a;

    iget-object v1, p0, Landroidx/media3/exoplayer/n2;->b:Landroid/util/Pair;

    iget-object v2, p0, Landroidx/media3/exoplayer/n2;->c:Lu2/n;

    iget-object v3, p0, Landroidx/media3/exoplayer/n2;->d:Lu2/o;

    invoke-static {v0, v1, v2, v3}, Landroidx/media3/exoplayer/r2$a;->P(Landroidx/media3/exoplayer/r2$a;Landroid/util/Pair;Lu2/n;Lu2/o;)V

    return-void
.end method
