.class public interface abstract Landroidx/media3/exoplayer/offline/DownloadManager$d;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/media3/exoplayer/offline/DownloadManager;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "d"
.end annotation


# virtual methods
.method public abstract a(Landroidx/media3/exoplayer/offline/DownloadManager;Z)V
.end method

.method public abstract b(Landroidx/media3/exoplayer/offline/DownloadManager;Landroidx/media3/exoplayer/offline/c;)V
.end method

.method public abstract c(Landroidx/media3/exoplayer/offline/DownloadManager;Z)V
.end method

.method public abstract d(Landroidx/media3/exoplayer/offline/DownloadManager;)V
.end method

.method public abstract e(Landroidx/media3/exoplayer/offline/DownloadManager;Landroidx/media3/exoplayer/scheduler/Requirements;I)V
.end method

.method public abstract f(Landroidx/media3/exoplayer/offline/DownloadManager;Landroidx/media3/exoplayer/offline/c;Ljava/lang/Exception;)V
    .param p3    # Ljava/lang/Exception;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
.end method

.method public abstract g(Landroidx/media3/exoplayer/offline/DownloadManager;)V
.end method
