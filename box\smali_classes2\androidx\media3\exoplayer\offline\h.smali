.class public final synthetic Landroidx/media3/exoplayer/offline/h;
.super Ljava/lang/Object;

# interfaces
.implements Lt2/e$c;


# instance fields
.field public final synthetic a:Landroidx/media3/exoplayer/offline/DownloadManager;


# direct methods
.method public synthetic constructor <init>(Landroidx/media3/exoplayer/offline/DownloadManager;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Landroidx/media3/exoplayer/offline/h;->a:Landroidx/media3/exoplayer/offline/DownloadManager;

    return-void
.end method


# virtual methods
.method public final a(Lt2/e;I)V
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/offline/h;->a:Landroidx/media3/exoplayer/offline/DownloadManager;

    invoke-static {v0, p1, p2}, Landroidx/media3/exoplayer/offline/DownloadManager;->b(Landroidx/media3/exoplayer/offline/DownloadManager;Lt2/e;I)V

    return-void
.end method
