.class public final synthetic Landroidx/media3/exoplayer/offline/n;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/lang/Runnable;


# instance fields
.field public final synthetic a:Landroidx/media3/exoplayer/offline/DownloadService$c;


# direct methods
.method public synthetic constructor <init>(Landroidx/media3/exoplayer/offline/DownloadService$c;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Landroidx/media3/exoplayer/offline/n;->a:Landroidx/media3/exoplayer/offline/DownloadService$c;

    return-void
.end method


# virtual methods
.method public final run()V
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/offline/n;->a:Landroidx/media3/exoplayer/offline/DownloadService$c;

    invoke-static {v0}, Landroidx/media3/exoplayer/offline/DownloadService$c;->a(Landroidx/media3/exoplayer/offline/DownloadService$c;)V

    return-void
.end method
