.class public final synthetic Landroidx/media3/exoplayer/p1;
.super Ljava/lang/Object;

# interfaces
.implements Landroidx/media3/exoplayer/z1$a;


# instance fields
.field public final synthetic a:Landroidx/media3/exoplayer/s1;


# direct methods
.method public synthetic constructor <init>(Landroidx/media3/exoplayer/s1;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Landroidx/media3/exoplayer/p1;->a:Landroidx/media3/exoplayer/s1;

    return-void
.end method


# virtual methods
.method public final a(Landroidx/media3/exoplayer/a2;J)Landroidx/media3/exoplayer/z1;
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/p1;->a:Landroidx/media3/exoplayer/s1;

    invoke-static {v0, p1, p2, p3}, Landroidx/media3/exoplayer/s1;->f(Landroidx/media3/exoplayer/s1;Landroidx/media3/exoplayer/a2;J)Landroidx/media3/exoplayer/z1;

    move-result-object p1

    return-object p1
.end method
