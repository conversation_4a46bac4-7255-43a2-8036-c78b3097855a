.class public final synthetic Landroidx/media3/exoplayer/p2;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/lang/Runnable;


# instance fields
.field public final synthetic a:Landroidx/media3/exoplayer/r2$a;

.field public final synthetic b:Landroid/util/Pair;

.field public final synthetic c:Lu2/n;

.field public final synthetic d:Lu2/o;

.field public final synthetic e:Ljava/io/IOException;

.field public final synthetic f:Z


# direct methods
.method public synthetic constructor <init>(Landroidx/media3/exoplayer/r2$a;Landroid/util/Pair;Lu2/n;Lu2/o;Ljava/io/IOException;Z)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Landroidx/media3/exoplayer/p2;->a:Landroidx/media3/exoplayer/r2$a;

    iput-object p2, p0, Landroidx/media3/exoplayer/p2;->b:Landroid/util/Pair;

    iput-object p3, p0, Landroidx/media3/exoplayer/p2;->c:Lu2/n;

    iput-object p4, p0, Landroidx/media3/exoplayer/p2;->d:Lu2/o;

    iput-object p5, p0, Landroidx/media3/exoplayer/p2;->e:Ljava/io/IOException;

    iput-boolean p6, p0, Landroidx/media3/exoplayer/p2;->f:Z

    return-void
.end method


# virtual methods
.method public final run()V
    .locals 6

    iget-object v0, p0, Landroidx/media3/exoplayer/p2;->a:Landroidx/media3/exoplayer/r2$a;

    iget-object v1, p0, Landroidx/media3/exoplayer/p2;->b:Landroid/util/Pair;

    iget-object v2, p0, Landroidx/media3/exoplayer/p2;->c:Lu2/n;

    iget-object v3, p0, Landroidx/media3/exoplayer/p2;->d:Lu2/o;

    iget-object v4, p0, Landroidx/media3/exoplayer/p2;->e:Ljava/io/IOException;

    iget-boolean v5, p0, Landroidx/media3/exoplayer/p2;->f:Z

    invoke-static/range {v0 .. v5}, Landroidx/media3/exoplayer/r2$a;->N(Landroidx/media3/exoplayer/r2$a;Landroid/util/Pair;Lu2/n;Lu2/o;Ljava/io/IOException;Z)V

    return-void
.end method
