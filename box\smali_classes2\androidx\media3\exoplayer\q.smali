.class public Landroidx/media3/exoplayer/q;
.super Ljava/lang/Object;

# interfaces
.implements Landroidx/media3/exoplayer/v1;


# instance fields
.field public final a:Landroidx/media3/exoplayer/upstream/h;

.field public final b:J

.field public final c:J

.field public final d:J

.field public final e:J

.field public final f:I

.field public final g:Z

.field public final h:J

.field public final i:Z

.field public j:I

.field public k:Z


# direct methods
.method public constructor <init>()V
    .locals 10

    new-instance v1, Landroidx/media3/exoplayer/upstream/h;

    const/4 v0, 0x1

    const/high16 v2, 0x10000

    invoke-direct {v1, v0, v2}, Landroidx/media3/exoplayer/upstream/h;-><init>(ZI)V

    const v2, 0xc350

    const v3, 0xc350

    const/16 v4, 0x9c4

    const/16 v5, 0x1388

    const/4 v6, -0x1

    const/4 v7, 0x0

    const/4 v8, 0x0

    const/4 v9, 0x0

    move-object v0, p0

    invoke-direct/range {v0 .. v9}, Landroidx/media3/exoplayer/q;-><init>(Landroidx/media3/exoplayer/upstream/h;IIIIIZIZ)V

    return-void
.end method

.method public constructor <init>(Landroidx/media3/exoplayer/upstream/h;IIIIIZIZ)V
    .locals 5

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, 0x0

    const-string v1, "bufferForPlaybackMs"

    const-string v2, "0"

    invoke-static {p4, v0, v1, v2}, Landroidx/media3/exoplayer/q;->d(IILjava/lang/String;Ljava/lang/String;)V

    const-string v3, "bufferForPlaybackAfterRebufferMs"

    invoke-static {p5, v0, v3, v2}, Landroidx/media3/exoplayer/q;->d(IILjava/lang/String;Ljava/lang/String;)V

    const-string v4, "minBufferMs"

    invoke-static {p2, p4, v4, v1}, Landroidx/media3/exoplayer/q;->d(IILjava/lang/String;Ljava/lang/String;)V

    invoke-static {p2, p5, v4, v3}, Landroidx/media3/exoplayer/q;->d(IILjava/lang/String;Ljava/lang/String;)V

    const-string v1, "maxBufferMs"

    invoke-static {p3, p2, v1, v4}, Landroidx/media3/exoplayer/q;->d(IILjava/lang/String;Ljava/lang/String;)V

    const-string v1, "backBufferDurationMs"

    invoke-static {p8, v0, v1, v2}, Landroidx/media3/exoplayer/q;->d(IILjava/lang/String;Ljava/lang/String;)V

    iput-object p1, p0, Landroidx/media3/exoplayer/q;->a:Landroidx/media3/exoplayer/upstream/h;

    int-to-long p1, p2

    invoke-static {p1, p2}, Le2/u0;->S0(J)J

    move-result-wide p1

    iput-wide p1, p0, Landroidx/media3/exoplayer/q;->b:J

    int-to-long p1, p3

    invoke-static {p1, p2}, Le2/u0;->S0(J)J

    move-result-wide p1

    iput-wide p1, p0, Landroidx/media3/exoplayer/q;->c:J

    int-to-long p1, p4

    invoke-static {p1, p2}, Le2/u0;->S0(J)J

    move-result-wide p1

    iput-wide p1, p0, Landroidx/media3/exoplayer/q;->d:J

    int-to-long p1, p5

    invoke-static {p1, p2}, Le2/u0;->S0(J)J

    move-result-wide p1

    iput-wide p1, p0, Landroidx/media3/exoplayer/q;->e:J

    iput p6, p0, Landroidx/media3/exoplayer/q;->f:I

    const/4 p1, -0x1

    if-eq p6, p1, :cond_0

    goto :goto_0

    :cond_0
    const/high16 p6, 0xc80000

    :goto_0
    iput p6, p0, Landroidx/media3/exoplayer/q;->j:I

    iput-boolean p7, p0, Landroidx/media3/exoplayer/q;->g:Z

    int-to-long p1, p8

    invoke-static {p1, p2}, Le2/u0;->S0(J)J

    move-result-wide p1

    iput-wide p1, p0, Landroidx/media3/exoplayer/q;->h:J

    iput-boolean p9, p0, Landroidx/media3/exoplayer/q;->i:Z

    return-void
.end method

.method public static d(IILjava/lang/String;Ljava/lang/String;)V
    .locals 0

    if-lt p0, p1, :cond_0

    const/4 p0, 0x1

    goto :goto_0

    :cond_0
    const/4 p0, 0x0

    :goto_0
    new-instance p1, Ljava/lang/StringBuilder;

    invoke-direct {p1}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string p2, " cannot be less than "

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p1, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-static {p0, p1}, Le2/a;->b(ZLjava/lang/Object;)V

    return-void
.end method

.method public static f(I)I
    .locals 1

    const/high16 v0, 0x20000

    packed-switch p0, :pswitch_data_0

    :pswitch_0
    new-instance p0, Ljava/lang/IllegalArgumentException;

    invoke-direct {p0}, Ljava/lang/IllegalArgumentException;-><init>()V

    throw p0

    :pswitch_1
    return v0

    :pswitch_2
    const/high16 p0, 0x7d00000

    return p0

    :pswitch_3
    const/high16 p0, 0xc80000

    return p0

    :pswitch_4
    const/high16 p0, 0x89a0000

    return p0

    :pswitch_5
    const/4 p0, 0x0

    return p0

    nop

    :pswitch_data_0
    .packed-switch -0x2
        :pswitch_5
        :pswitch_0
        :pswitch_4
        :pswitch_3
        :pswitch_2
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
    .end packed-switch
.end method


# virtual methods
.method public a(JJF)Z
    .locals 6

    iget-object p1, p0, Landroidx/media3/exoplayer/q;->a:Landroidx/media3/exoplayer/upstream/h;

    invoke-virtual {p1}, Landroidx/media3/exoplayer/upstream/h;->c()I

    move-result p1

    iget p2, p0, Landroidx/media3/exoplayer/q;->j:I

    const/4 v0, 0x1

    const/4 v1, 0x0

    if-lt p1, p2, :cond_0

    const/4 p1, 0x1

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    :goto_0
    iget-wide v2, p0, Landroidx/media3/exoplayer/q;->b:J

    const/high16 p2, 0x3f800000    # 1.0f

    cmpl-float p2, p5, p2

    if-lez p2, :cond_1

    invoke-static {v2, v3, p5}, Le2/u0;->h0(JF)J

    move-result-wide v2

    iget-wide v4, p0, Landroidx/media3/exoplayer/q;->c:J

    invoke-static {v2, v3, v4, v5}, Ljava/lang/Math;->min(JJ)J

    move-result-wide v2

    :cond_1
    const-wide/32 v4, 0x7a120

    invoke-static {v2, v3, v4, v5}, Ljava/lang/Math;->max(JJ)J

    move-result-wide v2

    cmp-long p2, p3, v2

    if-gez p2, :cond_4

    iget-boolean p2, p0, Landroidx/media3/exoplayer/q;->g:Z

    if-nez p2, :cond_3

    if-nez p1, :cond_2

    goto :goto_1

    :cond_2
    const/4 v0, 0x0

    :cond_3
    :goto_1
    iput-boolean v0, p0, Landroidx/media3/exoplayer/q;->k:Z

    if-nez v0, :cond_6

    cmp-long p1, p3, v4

    if-gez p1, :cond_6

    const-string p1, "DefaultLoadControl"

    const-string p2, "Target buffer size reached with less than 500ms of buffered media data."

    invoke-static {p1, p2}, Le2/o;->h(Ljava/lang/String;Ljava/lang/String;)V

    goto :goto_2

    :cond_4
    iget-wide v2, p0, Landroidx/media3/exoplayer/q;->c:J

    cmp-long p2, p3, v2

    if-gez p2, :cond_5

    if-eqz p1, :cond_6

    :cond_5
    iput-boolean v1, p0, Landroidx/media3/exoplayer/q;->k:Z

    :cond_6
    :goto_2
    iget-boolean p1, p0, Landroidx/media3/exoplayer/q;->k:Z

    return p1
.end method

.method public b(Landroidx/media3/common/m0;Landroidx/media3/exoplayer/source/l$b;[Landroidx/media3/exoplayer/w2;Lu2/k0;[Lx2/z;)V
    .locals 0

    iget p1, p0, Landroidx/media3/exoplayer/q;->f:I

    const/4 p2, -0x1

    if-ne p1, p2, :cond_0

    invoke-virtual {p0, p3, p5}, Landroidx/media3/exoplayer/q;->e([Landroidx/media3/exoplayer/w2;[Lx2/z;)I

    move-result p1

    :cond_0
    iput p1, p0, Landroidx/media3/exoplayer/q;->j:I

    iget-object p2, p0, Landroidx/media3/exoplayer/q;->a:Landroidx/media3/exoplayer/upstream/h;

    invoke-virtual {p2, p1}, Landroidx/media3/exoplayer/upstream/h;->e(I)V

    return-void
.end method

.method public c(Landroidx/media3/common/m0;Landroidx/media3/exoplayer/source/l$b;JFZJ)Z
    .locals 1

    invoke-static {p3, p4, p5}, Le2/u0;->m0(JF)J

    move-result-wide p1

    if-eqz p6, :cond_0

    iget-wide p3, p0, Landroidx/media3/exoplayer/q;->e:J

    goto :goto_0

    :cond_0
    iget-wide p3, p0, Landroidx/media3/exoplayer/q;->d:J

    :goto_0
    const-wide p5, -0x7fffffffffffffffL    # -4.9E-324

    cmp-long v0, p7, p5

    if-eqz v0, :cond_1

    const-wide/16 p5, 0x2

    div-long/2addr p7, p5

    invoke-static {p7, p8, p3, p4}, Ljava/lang/Math;->min(JJ)J

    move-result-wide p3

    :cond_1
    const-wide/16 p5, 0x0

    cmp-long p7, p3, p5

    if-lez p7, :cond_3

    cmp-long p5, p1, p3

    if-gez p5, :cond_3

    iget-boolean p1, p0, Landroidx/media3/exoplayer/q;->g:Z

    if-nez p1, :cond_2

    iget-object p1, p0, Landroidx/media3/exoplayer/q;->a:Landroidx/media3/exoplayer/upstream/h;

    invoke-virtual {p1}, Landroidx/media3/exoplayer/upstream/h;->c()I

    move-result p1

    iget p2, p0, Landroidx/media3/exoplayer/q;->j:I

    if-lt p1, p2, :cond_2

    goto :goto_1

    :cond_2
    const/4 p1, 0x0

    goto :goto_2

    :cond_3
    :goto_1
    const/4 p1, 0x1

    :goto_2
    return p1
.end method

.method public e([Landroidx/media3/exoplayer/w2;[Lx2/z;)I
    .locals 3

    const/4 v0, 0x0

    const/4 v1, 0x0

    :goto_0
    array-length v2, p1

    if-ge v0, v2, :cond_1

    aget-object v2, p2, v0

    if-eqz v2, :cond_0

    aget-object v2, p1, v0

    invoke-interface {v2}, Landroidx/media3/exoplayer/w2;->getTrackType()I

    move-result v2

    invoke-static {v2}, Landroidx/media3/exoplayer/q;->f(I)I

    move-result v2

    add-int/2addr v1, v2

    :cond_0
    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :cond_1
    const/high16 p1, 0xc80000

    invoke-static {p1, v1}, Ljava/lang/Math;->max(II)I

    move-result p1

    return p1
.end method

.method public final g(Z)V
    .locals 2

    iget v0, p0, Landroidx/media3/exoplayer/q;->f:I

    const/4 v1, -0x1

    if-ne v0, v1, :cond_0

    const/high16 v0, 0xc80000

    :cond_0
    iput v0, p0, Landroidx/media3/exoplayer/q;->j:I

    const/4 v0, 0x0

    iput-boolean v0, p0, Landroidx/media3/exoplayer/q;->k:Z

    if-eqz p1, :cond_1

    iget-object p1, p0, Landroidx/media3/exoplayer/q;->a:Landroidx/media3/exoplayer/upstream/h;

    invoke-virtual {p1}, Landroidx/media3/exoplayer/upstream/h;->d()V

    :cond_1
    return-void
.end method

.method public getAllocator()Landroidx/media3/exoplayer/upstream/b;
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/q;->a:Landroidx/media3/exoplayer/upstream/h;

    return-object v0
.end method

.method public getBackBufferDurationUs()J
    .locals 2

    iget-wide v0, p0, Landroidx/media3/exoplayer/q;->h:J

    return-wide v0
.end method

.method public onPrepared()V
    .locals 1

    const/4 v0, 0x0

    invoke-virtual {p0, v0}, Landroidx/media3/exoplayer/q;->g(Z)V

    return-void
.end method

.method public onReleased()V
    .locals 1

    const/4 v0, 0x1

    invoke-virtual {p0, v0}, Landroidx/media3/exoplayer/q;->g(Z)V

    return-void
.end method

.method public onStopped()V
    .locals 1

    const/4 v0, 0x1

    invoke-virtual {p0, v0}, Landroidx/media3/exoplayer/q;->g(Z)V

    return-void
.end method

.method public retainBackBufferFromKeyframe()Z
    .locals 1

    iget-boolean v0, p0, Landroidx/media3/exoplayer/q;->i:Z

    return v0
.end method
