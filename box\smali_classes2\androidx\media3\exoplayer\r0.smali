.class public final synthetic Landroidx/media3/exoplayer/r0;
.super Ljava/lang/Object;

# interfaces
.implements Le2/n$a;


# instance fields
.field public final synthetic a:F


# direct methods
.method public synthetic constructor <init>(F)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput p1, p0, Landroidx/media3/exoplayer/r0;->a:F

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)V
    .locals 1

    iget v0, p0, Landroidx/media3/exoplayer/r0;->a:F

    check-cast p1, Landroidx/media3/common/h0$d;

    invoke-static {v0, p1}, Landroidx/media3/exoplayer/c1;->i0(FLandroidx/media3/common/h0$d;)V

    return-void
.end method
