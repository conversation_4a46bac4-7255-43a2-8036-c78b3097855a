.class public final synthetic Landroidx/media3/exoplayer/r1;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/lang/Runnable;


# instance fields
.field public final synthetic a:Landroidx/media3/exoplayer/s1;

.field public final synthetic b:Landroidx/media3/exoplayer/t2;


# direct methods
.method public synthetic constructor <init>(Landroidx/media3/exoplayer/s1;Landroidx/media3/exoplayer/t2;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Landroidx/media3/exoplayer/r1;->a:Landroidx/media3/exoplayer/s1;

    iput-object p2, p0, Landroidx/media3/exoplayer/r1;->b:Landroidx/media3/exoplayer/t2;

    return-void
.end method


# virtual methods
.method public final run()V
    .locals 2

    iget-object v0, p0, Landroidx/media3/exoplayer/r1;->a:Landroidx/media3/exoplayer/s1;

    iget-object v1, p0, Landroidx/media3/exoplayer/r1;->b:Landroidx/media3/exoplayer/t2;

    invoke-static {v0, v1}, Landroidx/media3/exoplayer/s1;->e(Landroidx/media3/exoplayer/s1;Landroidx/media3/exoplayer/t2;)V

    return-void
.end method
