.class public final Landroidx/media3/exoplayer/s2;
.super Ljava/lang/Object;


# static fields
.field public static final t:Landroidx/media3/exoplayer/source/l$b;


# instance fields
.field public final a:Landroidx/media3/common/m0;

.field public final b:Landroidx/media3/exoplayer/source/l$b;

.field public final c:J

.field public final d:J

.field public final e:I

.field public final f:Landroidx/media3/exoplayer/ExoPlaybackException;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public final g:Z

.field public final h:Lu2/k0;

.field public final i:Lx2/f0;

.field public final j:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Landroidx/media3/common/Metadata;",
            ">;"
        }
    .end annotation
.end field

.field public final k:Landroidx/media3/exoplayer/source/l$b;

.field public final l:Z

.field public final m:I

.field public final n:Landroidx/media3/common/g0;

.field public final o:Z

.field public volatile p:J

.field public volatile q:J

.field public volatile r:J

.field public volatile s:J


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Landroidx/media3/exoplayer/source/l$b;

    new-instance v1, Ljava/lang/Object;

    invoke-direct {v1}, Ljava/lang/Object;-><init>()V

    invoke-direct {v0, v1}, Landroidx/media3/exoplayer/source/l$b;-><init>(Ljava/lang/Object;)V

    sput-object v0, Landroidx/media3/exoplayer/s2;->t:Landroidx/media3/exoplayer/source/l$b;

    return-void
.end method

.method public constructor <init>(Landroidx/media3/common/m0;Landroidx/media3/exoplayer/source/l$b;JJILandroidx/media3/exoplayer/ExoPlaybackException;ZLu2/k0;Lx2/f0;Ljava/util/List;Landroidx/media3/exoplayer/source/l$b;ZILandroidx/media3/common/g0;JJJJZ)V
    .locals 3
    .param p8    # Landroidx/media3/exoplayer/ExoPlaybackException;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroidx/media3/common/m0;",
            "Landroidx/media3/exoplayer/source/l$b;",
            "JJI",
            "Landroidx/media3/exoplayer/ExoPlaybackException;",
            "Z",
            "Lu2/k0;",
            "Lx2/f0;",
            "Ljava/util/List<",
            "Landroidx/media3/common/Metadata;",
            ">;",
            "Landroidx/media3/exoplayer/source/l$b;",
            "ZI",
            "Landroidx/media3/common/g0;",
            "JJJJZ)V"
        }
    .end annotation

    move-object v0, p0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    move-object v1, p1

    iput-object v1, v0, Landroidx/media3/exoplayer/s2;->a:Landroidx/media3/common/m0;

    move-object v1, p2

    iput-object v1, v0, Landroidx/media3/exoplayer/s2;->b:Landroidx/media3/exoplayer/source/l$b;

    move-wide v1, p3

    iput-wide v1, v0, Landroidx/media3/exoplayer/s2;->c:J

    move-wide v1, p5

    iput-wide v1, v0, Landroidx/media3/exoplayer/s2;->d:J

    move v1, p7

    iput v1, v0, Landroidx/media3/exoplayer/s2;->e:I

    move-object v1, p8

    iput-object v1, v0, Landroidx/media3/exoplayer/s2;->f:Landroidx/media3/exoplayer/ExoPlaybackException;

    move v1, p9

    iput-boolean v1, v0, Landroidx/media3/exoplayer/s2;->g:Z

    move-object v1, p10

    iput-object v1, v0, Landroidx/media3/exoplayer/s2;->h:Lu2/k0;

    move-object v1, p11

    iput-object v1, v0, Landroidx/media3/exoplayer/s2;->i:Lx2/f0;

    move-object v1, p12

    iput-object v1, v0, Landroidx/media3/exoplayer/s2;->j:Ljava/util/List;

    move-object/from16 v1, p13

    iput-object v1, v0, Landroidx/media3/exoplayer/s2;->k:Landroidx/media3/exoplayer/source/l$b;

    move/from16 v1, p14

    iput-boolean v1, v0, Landroidx/media3/exoplayer/s2;->l:Z

    move/from16 v1, p15

    iput v1, v0, Landroidx/media3/exoplayer/s2;->m:I

    move-object/from16 v1, p16

    iput-object v1, v0, Landroidx/media3/exoplayer/s2;->n:Landroidx/media3/common/g0;

    move-wide/from16 v1, p17

    iput-wide v1, v0, Landroidx/media3/exoplayer/s2;->p:J

    move-wide/from16 v1, p19

    iput-wide v1, v0, Landroidx/media3/exoplayer/s2;->q:J

    move-wide/from16 v1, p21

    iput-wide v1, v0, Landroidx/media3/exoplayer/s2;->r:J

    move-wide/from16 v1, p23

    iput-wide v1, v0, Landroidx/media3/exoplayer/s2;->s:J

    move/from16 v1, p25

    iput-boolean v1, v0, Landroidx/media3/exoplayer/s2;->o:Z

    return-void
.end method

.method public static k(Lx2/f0;)Landroidx/media3/exoplayer/s2;
    .locals 27

    move-object/from16 v11, p0

    new-instance v26, Landroidx/media3/exoplayer/s2;

    move-object/from16 v0, v26

    sget-object v1, Landroidx/media3/common/m0;->a:Landroidx/media3/common/m0;

    sget-object v13, Landroidx/media3/exoplayer/s2;->t:Landroidx/media3/exoplayer/source/l$b;

    move-object v2, v13

    const-wide v3, -0x7fffffffffffffffL    # -4.9E-324

    const-wide/16 v5, 0x0

    const/4 v7, 0x1

    const/4 v8, 0x0

    const/4 v9, 0x0

    sget-object v10, Lu2/k0;->d:Lu2/k0;

    invoke-static {}, Lcom/google/common/collect/ImmutableList;->of()Lcom/google/common/collect/ImmutableList;

    move-result-object v12

    const/4 v14, 0x0

    const/4 v15, 0x0

    sget-object v16, Landroidx/media3/common/g0;->d:Landroidx/media3/common/g0;

    const-wide/16 v17, 0x0

    const-wide/16 v19, 0x0

    const-wide/16 v21, 0x0

    const-wide/16 v23, 0x0

    const/16 v25, 0x0

    invoke-direct/range {v0 .. v25}, Landroidx/media3/exoplayer/s2;-><init>(Landroidx/media3/common/m0;Landroidx/media3/exoplayer/source/l$b;JJILandroidx/media3/exoplayer/ExoPlaybackException;ZLu2/k0;Lx2/f0;Ljava/util/List;Landroidx/media3/exoplayer/source/l$b;ZILandroidx/media3/common/g0;JJJJZ)V

    return-object v26
.end method

.method public static l()Landroidx/media3/exoplayer/source/l$b;
    .locals 1

    sget-object v0, Landroidx/media3/exoplayer/s2;->t:Landroidx/media3/exoplayer/source/l$b;

    return-object v0
.end method


# virtual methods
.method public a()Landroidx/media3/exoplayer/s2;
    .locals 30
    .annotation build Landroidx/annotation/CheckResult;
    .end annotation

    move-object/from16 v0, p0

    new-instance v27, Landroidx/media3/exoplayer/s2;

    move-object/from16 v1, v27

    iget-object v2, v0, Landroidx/media3/exoplayer/s2;->a:Landroidx/media3/common/m0;

    iget-object v3, v0, Landroidx/media3/exoplayer/s2;->b:Landroidx/media3/exoplayer/source/l$b;

    iget-wide v4, v0, Landroidx/media3/exoplayer/s2;->c:J

    iget-wide v6, v0, Landroidx/media3/exoplayer/s2;->d:J

    iget v8, v0, Landroidx/media3/exoplayer/s2;->e:I

    iget-object v9, v0, Landroidx/media3/exoplayer/s2;->f:Landroidx/media3/exoplayer/ExoPlaybackException;

    iget-boolean v10, v0, Landroidx/media3/exoplayer/s2;->g:Z

    iget-object v11, v0, Landroidx/media3/exoplayer/s2;->h:Lu2/k0;

    iget-object v12, v0, Landroidx/media3/exoplayer/s2;->i:Lx2/f0;

    iget-object v13, v0, Landroidx/media3/exoplayer/s2;->j:Ljava/util/List;

    iget-object v14, v0, Landroidx/media3/exoplayer/s2;->k:Landroidx/media3/exoplayer/source/l$b;

    iget-boolean v15, v0, Landroidx/media3/exoplayer/s2;->l:Z

    move-object/from16 v28, v1

    iget v1, v0, Landroidx/media3/exoplayer/s2;->m:I

    move/from16 v16, v1

    iget-object v1, v0, Landroidx/media3/exoplayer/s2;->n:Landroidx/media3/common/g0;

    move-object/from16 v17, v1

    move-object/from16 v29, v2

    iget-wide v1, v0, Landroidx/media3/exoplayer/s2;->p:J

    move-wide/from16 v18, v1

    iget-wide v1, v0, Landroidx/media3/exoplayer/s2;->q:J

    move-wide/from16 v20, v1

    invoke-virtual/range {p0 .. p0}, Landroidx/media3/exoplayer/s2;->m()J

    move-result-wide v22

    invoke-static {}, Landroid/os/SystemClock;->elapsedRealtime()J

    move-result-wide v24

    iget-boolean v1, v0, Landroidx/media3/exoplayer/s2;->o:Z

    move/from16 v26, v1

    move-object/from16 v1, v28

    move-object/from16 v2, v29

    invoke-direct/range {v1 .. v26}, Landroidx/media3/exoplayer/s2;-><init>(Landroidx/media3/common/m0;Landroidx/media3/exoplayer/source/l$b;JJILandroidx/media3/exoplayer/ExoPlaybackException;ZLu2/k0;Lx2/f0;Ljava/util/List;Landroidx/media3/exoplayer/source/l$b;ZILandroidx/media3/common/g0;JJJJZ)V

    return-object v27
.end method

.method public b(Z)Landroidx/media3/exoplayer/s2;
    .locals 29
    .annotation build Landroidx/annotation/CheckResult;
    .end annotation

    move-object/from16 v0, p0

    move/from16 v10, p1

    new-instance v27, Landroidx/media3/exoplayer/s2;

    move-object/from16 v1, v27

    iget-object v2, v0, Landroidx/media3/exoplayer/s2;->a:Landroidx/media3/common/m0;

    iget-object v3, v0, Landroidx/media3/exoplayer/s2;->b:Landroidx/media3/exoplayer/source/l$b;

    iget-wide v4, v0, Landroidx/media3/exoplayer/s2;->c:J

    iget-wide v6, v0, Landroidx/media3/exoplayer/s2;->d:J

    iget v8, v0, Landroidx/media3/exoplayer/s2;->e:I

    iget-object v9, v0, Landroidx/media3/exoplayer/s2;->f:Landroidx/media3/exoplayer/ExoPlaybackException;

    iget-object v11, v0, Landroidx/media3/exoplayer/s2;->h:Lu2/k0;

    iget-object v12, v0, Landroidx/media3/exoplayer/s2;->i:Lx2/f0;

    iget-object v13, v0, Landroidx/media3/exoplayer/s2;->j:Ljava/util/List;

    iget-object v14, v0, Landroidx/media3/exoplayer/s2;->k:Landroidx/media3/exoplayer/source/l$b;

    iget-boolean v15, v0, Landroidx/media3/exoplayer/s2;->l:Z

    move-object/from16 p1, v1

    iget v1, v0, Landroidx/media3/exoplayer/s2;->m:I

    move/from16 v16, v1

    iget-object v1, v0, Landroidx/media3/exoplayer/s2;->n:Landroidx/media3/common/g0;

    move-object/from16 v17, v1

    move-object/from16 v28, v2

    iget-wide v1, v0, Landroidx/media3/exoplayer/s2;->p:J

    move-wide/from16 v18, v1

    iget-wide v1, v0, Landroidx/media3/exoplayer/s2;->q:J

    move-wide/from16 v20, v1

    iget-wide v1, v0, Landroidx/media3/exoplayer/s2;->r:J

    move-wide/from16 v22, v1

    iget-wide v1, v0, Landroidx/media3/exoplayer/s2;->s:J

    move-wide/from16 v24, v1

    iget-boolean v1, v0, Landroidx/media3/exoplayer/s2;->o:Z

    move/from16 v26, v1

    move-object/from16 v1, p1

    move-object/from16 v2, v28

    invoke-direct/range {v1 .. v26}, Landroidx/media3/exoplayer/s2;-><init>(Landroidx/media3/common/m0;Landroidx/media3/exoplayer/source/l$b;JJILandroidx/media3/exoplayer/ExoPlaybackException;ZLu2/k0;Lx2/f0;Ljava/util/List;Landroidx/media3/exoplayer/source/l$b;ZILandroidx/media3/common/g0;JJJJZ)V

    return-object v27
.end method

.method public c(Landroidx/media3/exoplayer/source/l$b;)Landroidx/media3/exoplayer/s2;
    .locals 29
    .annotation build Landroidx/annotation/CheckResult;
    .end annotation

    move-object/from16 v0, p0

    move-object/from16 v14, p1

    new-instance v27, Landroidx/media3/exoplayer/s2;

    move-object/from16 v1, v27

    iget-object v2, v0, Landroidx/media3/exoplayer/s2;->a:Landroidx/media3/common/m0;

    iget-object v3, v0, Landroidx/media3/exoplayer/s2;->b:Landroidx/media3/exoplayer/source/l$b;

    iget-wide v4, v0, Landroidx/media3/exoplayer/s2;->c:J

    iget-wide v6, v0, Landroidx/media3/exoplayer/s2;->d:J

    iget v8, v0, Landroidx/media3/exoplayer/s2;->e:I

    iget-object v9, v0, Landroidx/media3/exoplayer/s2;->f:Landroidx/media3/exoplayer/ExoPlaybackException;

    iget-boolean v10, v0, Landroidx/media3/exoplayer/s2;->g:Z

    iget-object v11, v0, Landroidx/media3/exoplayer/s2;->h:Lu2/k0;

    iget-object v12, v0, Landroidx/media3/exoplayer/s2;->i:Lx2/f0;

    iget-object v13, v0, Landroidx/media3/exoplayer/s2;->j:Ljava/util/List;

    iget-boolean v15, v0, Landroidx/media3/exoplayer/s2;->l:Z

    move-object/from16 p1, v1

    iget v1, v0, Landroidx/media3/exoplayer/s2;->m:I

    move/from16 v16, v1

    iget-object v1, v0, Landroidx/media3/exoplayer/s2;->n:Landroidx/media3/common/g0;

    move-object/from16 v17, v1

    move-object/from16 v28, v2

    iget-wide v1, v0, Landroidx/media3/exoplayer/s2;->p:J

    move-wide/from16 v18, v1

    iget-wide v1, v0, Landroidx/media3/exoplayer/s2;->q:J

    move-wide/from16 v20, v1

    iget-wide v1, v0, Landroidx/media3/exoplayer/s2;->r:J

    move-wide/from16 v22, v1

    iget-wide v1, v0, Landroidx/media3/exoplayer/s2;->s:J

    move-wide/from16 v24, v1

    iget-boolean v1, v0, Landroidx/media3/exoplayer/s2;->o:Z

    move/from16 v26, v1

    move-object/from16 v1, p1

    move-object/from16 v2, v28

    invoke-direct/range {v1 .. v26}, Landroidx/media3/exoplayer/s2;-><init>(Landroidx/media3/common/m0;Landroidx/media3/exoplayer/source/l$b;JJILandroidx/media3/exoplayer/ExoPlaybackException;ZLu2/k0;Lx2/f0;Ljava/util/List;Landroidx/media3/exoplayer/source/l$b;ZILandroidx/media3/common/g0;JJJJZ)V

    return-object v27
.end method

.method public d(Landroidx/media3/exoplayer/source/l$b;JJJJLu2/k0;Lx2/f0;Ljava/util/List;)Landroidx/media3/exoplayer/s2;
    .locals 28
    .annotation build Landroidx/annotation/CheckResult;
    .end annotation

    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroidx/media3/exoplayer/source/l$b;",
            "JJJJ",
            "Lu2/k0;",
            "Lx2/f0;",
            "Ljava/util/List<",
            "Landroidx/media3/common/Metadata;",
            ">;)",
            "Landroidx/media3/exoplayer/s2;"
        }
    .end annotation

    move-object/from16 v0, p0

    move-object/from16 v3, p1

    move-wide/from16 v22, p2

    move-wide/from16 v4, p4

    move-wide/from16 v6, p6

    move-wide/from16 v20, p8

    move-object/from16 v11, p10

    move-object/from16 v12, p11

    move-object/from16 v13, p12

    new-instance v27, Landroidx/media3/exoplayer/s2;

    move-object/from16 v1, v27

    iget-object v2, v0, Landroidx/media3/exoplayer/s2;->a:Landroidx/media3/common/m0;

    iget v8, v0, Landroidx/media3/exoplayer/s2;->e:I

    iget-object v9, v0, Landroidx/media3/exoplayer/s2;->f:Landroidx/media3/exoplayer/ExoPlaybackException;

    iget-boolean v10, v0, Landroidx/media3/exoplayer/s2;->g:Z

    iget-object v14, v0, Landroidx/media3/exoplayer/s2;->k:Landroidx/media3/exoplayer/source/l$b;

    iget-boolean v15, v0, Landroidx/media3/exoplayer/s2;->l:Z

    move-object/from16 p1, v1

    iget v1, v0, Landroidx/media3/exoplayer/s2;->m:I

    move/from16 v16, v1

    iget-object v1, v0, Landroidx/media3/exoplayer/s2;->n:Landroidx/media3/common/g0;

    move-object/from16 v17, v1

    move-object/from16 p2, v2

    iget-wide v1, v0, Landroidx/media3/exoplayer/s2;->p:J

    move-wide/from16 v18, v1

    invoke-static {}, Landroid/os/SystemClock;->elapsedRealtime()J

    move-result-wide v24

    iget-boolean v1, v0, Landroidx/media3/exoplayer/s2;->o:Z

    move/from16 v26, v1

    move-object/from16 v1, p1

    move-object/from16 v2, p2

    invoke-direct/range {v1 .. v26}, Landroidx/media3/exoplayer/s2;-><init>(Landroidx/media3/common/m0;Landroidx/media3/exoplayer/source/l$b;JJILandroidx/media3/exoplayer/ExoPlaybackException;ZLu2/k0;Lx2/f0;Ljava/util/List;Landroidx/media3/exoplayer/source/l$b;ZILandroidx/media3/common/g0;JJJJZ)V

    return-object v27
.end method

.method public e(ZI)Landroidx/media3/exoplayer/s2;
    .locals 28
    .annotation build Landroidx/annotation/CheckResult;
    .end annotation

    move-object/from16 v0, p0

    move/from16 v15, p1

    move/from16 v16, p2

    new-instance v27, Landroidx/media3/exoplayer/s2;

    move-object/from16 v1, v27

    iget-object v2, v0, Landroidx/media3/exoplayer/s2;->a:Landroidx/media3/common/m0;

    iget-object v3, v0, Landroidx/media3/exoplayer/s2;->b:Landroidx/media3/exoplayer/source/l$b;

    iget-wide v4, v0, Landroidx/media3/exoplayer/s2;->c:J

    iget-wide v6, v0, Landroidx/media3/exoplayer/s2;->d:J

    iget v8, v0, Landroidx/media3/exoplayer/s2;->e:I

    iget-object v9, v0, Landroidx/media3/exoplayer/s2;->f:Landroidx/media3/exoplayer/ExoPlaybackException;

    iget-boolean v10, v0, Landroidx/media3/exoplayer/s2;->g:Z

    iget-object v11, v0, Landroidx/media3/exoplayer/s2;->h:Lu2/k0;

    iget-object v12, v0, Landroidx/media3/exoplayer/s2;->i:Lx2/f0;

    iget-object v13, v0, Landroidx/media3/exoplayer/s2;->j:Ljava/util/List;

    iget-object v14, v0, Landroidx/media3/exoplayer/s2;->k:Landroidx/media3/exoplayer/source/l$b;

    move-object/from16 p1, v1

    iget-object v1, v0, Landroidx/media3/exoplayer/s2;->n:Landroidx/media3/common/g0;

    move-object/from16 v17, v1

    move-object/from16 p2, v2

    iget-wide v1, v0, Landroidx/media3/exoplayer/s2;->p:J

    move-wide/from16 v18, v1

    iget-wide v1, v0, Landroidx/media3/exoplayer/s2;->q:J

    move-wide/from16 v20, v1

    iget-wide v1, v0, Landroidx/media3/exoplayer/s2;->r:J

    move-wide/from16 v22, v1

    iget-wide v1, v0, Landroidx/media3/exoplayer/s2;->s:J

    move-wide/from16 v24, v1

    iget-boolean v1, v0, Landroidx/media3/exoplayer/s2;->o:Z

    move/from16 v26, v1

    move-object/from16 v1, p1

    move-object/from16 v2, p2

    invoke-direct/range {v1 .. v26}, Landroidx/media3/exoplayer/s2;-><init>(Landroidx/media3/common/m0;Landroidx/media3/exoplayer/source/l$b;JJILandroidx/media3/exoplayer/ExoPlaybackException;ZLu2/k0;Lx2/f0;Ljava/util/List;Landroidx/media3/exoplayer/source/l$b;ZILandroidx/media3/common/g0;JJJJZ)V

    return-object v27
.end method

.method public f(Landroidx/media3/exoplayer/ExoPlaybackException;)Landroidx/media3/exoplayer/s2;
    .locals 29
    .param p1    # Landroidx/media3/exoplayer/ExoPlaybackException;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .annotation build Landroidx/annotation/CheckResult;
    .end annotation

    move-object/from16 v0, p0

    move-object/from16 v9, p1

    new-instance v27, Landroidx/media3/exoplayer/s2;

    move-object/from16 v1, v27

    iget-object v2, v0, Landroidx/media3/exoplayer/s2;->a:Landroidx/media3/common/m0;

    iget-object v3, v0, Landroidx/media3/exoplayer/s2;->b:Landroidx/media3/exoplayer/source/l$b;

    iget-wide v4, v0, Landroidx/media3/exoplayer/s2;->c:J

    iget-wide v6, v0, Landroidx/media3/exoplayer/s2;->d:J

    iget v8, v0, Landroidx/media3/exoplayer/s2;->e:I

    iget-boolean v10, v0, Landroidx/media3/exoplayer/s2;->g:Z

    iget-object v11, v0, Landroidx/media3/exoplayer/s2;->h:Lu2/k0;

    iget-object v12, v0, Landroidx/media3/exoplayer/s2;->i:Lx2/f0;

    iget-object v13, v0, Landroidx/media3/exoplayer/s2;->j:Ljava/util/List;

    iget-object v14, v0, Landroidx/media3/exoplayer/s2;->k:Landroidx/media3/exoplayer/source/l$b;

    iget-boolean v15, v0, Landroidx/media3/exoplayer/s2;->l:Z

    move-object/from16 p1, v1

    iget v1, v0, Landroidx/media3/exoplayer/s2;->m:I

    move/from16 v16, v1

    iget-object v1, v0, Landroidx/media3/exoplayer/s2;->n:Landroidx/media3/common/g0;

    move-object/from16 v17, v1

    move-object/from16 v28, v2

    iget-wide v1, v0, Landroidx/media3/exoplayer/s2;->p:J

    move-wide/from16 v18, v1

    iget-wide v1, v0, Landroidx/media3/exoplayer/s2;->q:J

    move-wide/from16 v20, v1

    iget-wide v1, v0, Landroidx/media3/exoplayer/s2;->r:J

    move-wide/from16 v22, v1

    iget-wide v1, v0, Landroidx/media3/exoplayer/s2;->s:J

    move-wide/from16 v24, v1

    iget-boolean v1, v0, Landroidx/media3/exoplayer/s2;->o:Z

    move/from16 v26, v1

    move-object/from16 v1, p1

    move-object/from16 v2, v28

    invoke-direct/range {v1 .. v26}, Landroidx/media3/exoplayer/s2;-><init>(Landroidx/media3/common/m0;Landroidx/media3/exoplayer/source/l$b;JJILandroidx/media3/exoplayer/ExoPlaybackException;ZLu2/k0;Lx2/f0;Ljava/util/List;Landroidx/media3/exoplayer/source/l$b;ZILandroidx/media3/common/g0;JJJJZ)V

    return-object v27
.end method

.method public g(Landroidx/media3/common/g0;)Landroidx/media3/exoplayer/s2;
    .locals 29
    .annotation build Landroidx/annotation/CheckResult;
    .end annotation

    move-object/from16 v0, p0

    move-object/from16 v17, p1

    new-instance v27, Landroidx/media3/exoplayer/s2;

    move-object/from16 v1, v27

    iget-object v2, v0, Landroidx/media3/exoplayer/s2;->a:Landroidx/media3/common/m0;

    iget-object v3, v0, Landroidx/media3/exoplayer/s2;->b:Landroidx/media3/exoplayer/source/l$b;

    iget-wide v4, v0, Landroidx/media3/exoplayer/s2;->c:J

    iget-wide v6, v0, Landroidx/media3/exoplayer/s2;->d:J

    iget v8, v0, Landroidx/media3/exoplayer/s2;->e:I

    iget-object v9, v0, Landroidx/media3/exoplayer/s2;->f:Landroidx/media3/exoplayer/ExoPlaybackException;

    iget-boolean v10, v0, Landroidx/media3/exoplayer/s2;->g:Z

    iget-object v11, v0, Landroidx/media3/exoplayer/s2;->h:Lu2/k0;

    iget-object v12, v0, Landroidx/media3/exoplayer/s2;->i:Lx2/f0;

    iget-object v13, v0, Landroidx/media3/exoplayer/s2;->j:Ljava/util/List;

    iget-object v14, v0, Landroidx/media3/exoplayer/s2;->k:Landroidx/media3/exoplayer/source/l$b;

    iget-boolean v15, v0, Landroidx/media3/exoplayer/s2;->l:Z

    move-object/from16 p1, v1

    iget v1, v0, Landroidx/media3/exoplayer/s2;->m:I

    move/from16 v16, v1

    move-object/from16 v28, v2

    iget-wide v1, v0, Landroidx/media3/exoplayer/s2;->p:J

    move-wide/from16 v18, v1

    iget-wide v1, v0, Landroidx/media3/exoplayer/s2;->q:J

    move-wide/from16 v20, v1

    iget-wide v1, v0, Landroidx/media3/exoplayer/s2;->r:J

    move-wide/from16 v22, v1

    iget-wide v1, v0, Landroidx/media3/exoplayer/s2;->s:J

    move-wide/from16 v24, v1

    iget-boolean v1, v0, Landroidx/media3/exoplayer/s2;->o:Z

    move/from16 v26, v1

    move-object/from16 v1, p1

    move-object/from16 v2, v28

    invoke-direct/range {v1 .. v26}, Landroidx/media3/exoplayer/s2;-><init>(Landroidx/media3/common/m0;Landroidx/media3/exoplayer/source/l$b;JJILandroidx/media3/exoplayer/ExoPlaybackException;ZLu2/k0;Lx2/f0;Ljava/util/List;Landroidx/media3/exoplayer/source/l$b;ZILandroidx/media3/common/g0;JJJJZ)V

    return-object v27
.end method

.method public h(I)Landroidx/media3/exoplayer/s2;
    .locals 29
    .annotation build Landroidx/annotation/CheckResult;
    .end annotation

    move-object/from16 v0, p0

    move/from16 v8, p1

    new-instance v27, Landroidx/media3/exoplayer/s2;

    move-object/from16 v1, v27

    iget-object v2, v0, Landroidx/media3/exoplayer/s2;->a:Landroidx/media3/common/m0;

    iget-object v3, v0, Landroidx/media3/exoplayer/s2;->b:Landroidx/media3/exoplayer/source/l$b;

    iget-wide v4, v0, Landroidx/media3/exoplayer/s2;->c:J

    iget-wide v6, v0, Landroidx/media3/exoplayer/s2;->d:J

    iget-object v9, v0, Landroidx/media3/exoplayer/s2;->f:Landroidx/media3/exoplayer/ExoPlaybackException;

    iget-boolean v10, v0, Landroidx/media3/exoplayer/s2;->g:Z

    iget-object v11, v0, Landroidx/media3/exoplayer/s2;->h:Lu2/k0;

    iget-object v12, v0, Landroidx/media3/exoplayer/s2;->i:Lx2/f0;

    iget-object v13, v0, Landroidx/media3/exoplayer/s2;->j:Ljava/util/List;

    iget-object v14, v0, Landroidx/media3/exoplayer/s2;->k:Landroidx/media3/exoplayer/source/l$b;

    iget-boolean v15, v0, Landroidx/media3/exoplayer/s2;->l:Z

    move-object/from16 p1, v1

    iget v1, v0, Landroidx/media3/exoplayer/s2;->m:I

    move/from16 v16, v1

    iget-object v1, v0, Landroidx/media3/exoplayer/s2;->n:Landroidx/media3/common/g0;

    move-object/from16 v17, v1

    move-object/from16 v28, v2

    iget-wide v1, v0, Landroidx/media3/exoplayer/s2;->p:J

    move-wide/from16 v18, v1

    iget-wide v1, v0, Landroidx/media3/exoplayer/s2;->q:J

    move-wide/from16 v20, v1

    iget-wide v1, v0, Landroidx/media3/exoplayer/s2;->r:J

    move-wide/from16 v22, v1

    iget-wide v1, v0, Landroidx/media3/exoplayer/s2;->s:J

    move-wide/from16 v24, v1

    iget-boolean v1, v0, Landroidx/media3/exoplayer/s2;->o:Z

    move/from16 v26, v1

    move-object/from16 v1, p1

    move-object/from16 v2, v28

    invoke-direct/range {v1 .. v26}, Landroidx/media3/exoplayer/s2;-><init>(Landroidx/media3/common/m0;Landroidx/media3/exoplayer/source/l$b;JJILandroidx/media3/exoplayer/ExoPlaybackException;ZLu2/k0;Lx2/f0;Ljava/util/List;Landroidx/media3/exoplayer/source/l$b;ZILandroidx/media3/common/g0;JJJJZ)V

    return-object v27
.end method

.method public i(Z)Landroidx/media3/exoplayer/s2;
    .locals 29
    .annotation build Landroidx/annotation/CheckResult;
    .end annotation

    move-object/from16 v0, p0

    move/from16 v26, p1

    new-instance v27, Landroidx/media3/exoplayer/s2;

    move-object/from16 v1, v27

    iget-object v2, v0, Landroidx/media3/exoplayer/s2;->a:Landroidx/media3/common/m0;

    iget-object v3, v0, Landroidx/media3/exoplayer/s2;->b:Landroidx/media3/exoplayer/source/l$b;

    iget-wide v4, v0, Landroidx/media3/exoplayer/s2;->c:J

    iget-wide v6, v0, Landroidx/media3/exoplayer/s2;->d:J

    iget v8, v0, Landroidx/media3/exoplayer/s2;->e:I

    iget-object v9, v0, Landroidx/media3/exoplayer/s2;->f:Landroidx/media3/exoplayer/ExoPlaybackException;

    iget-boolean v10, v0, Landroidx/media3/exoplayer/s2;->g:Z

    iget-object v11, v0, Landroidx/media3/exoplayer/s2;->h:Lu2/k0;

    iget-object v12, v0, Landroidx/media3/exoplayer/s2;->i:Lx2/f0;

    iget-object v13, v0, Landroidx/media3/exoplayer/s2;->j:Ljava/util/List;

    iget-object v14, v0, Landroidx/media3/exoplayer/s2;->k:Landroidx/media3/exoplayer/source/l$b;

    iget-boolean v15, v0, Landroidx/media3/exoplayer/s2;->l:Z

    move-object/from16 p1, v1

    iget v1, v0, Landroidx/media3/exoplayer/s2;->m:I

    move/from16 v16, v1

    iget-object v1, v0, Landroidx/media3/exoplayer/s2;->n:Landroidx/media3/common/g0;

    move-object/from16 v17, v1

    move-object/from16 v28, v2

    iget-wide v1, v0, Landroidx/media3/exoplayer/s2;->p:J

    move-wide/from16 v18, v1

    iget-wide v1, v0, Landroidx/media3/exoplayer/s2;->q:J

    move-wide/from16 v20, v1

    iget-wide v1, v0, Landroidx/media3/exoplayer/s2;->r:J

    move-wide/from16 v22, v1

    iget-wide v1, v0, Landroidx/media3/exoplayer/s2;->s:J

    move-wide/from16 v24, v1

    move-object/from16 v1, p1

    move-object/from16 v2, v28

    invoke-direct/range {v1 .. v26}, Landroidx/media3/exoplayer/s2;-><init>(Landroidx/media3/common/m0;Landroidx/media3/exoplayer/source/l$b;JJILandroidx/media3/exoplayer/ExoPlaybackException;ZLu2/k0;Lx2/f0;Ljava/util/List;Landroidx/media3/exoplayer/source/l$b;ZILandroidx/media3/common/g0;JJJJZ)V

    return-object v27
.end method

.method public j(Landroidx/media3/common/m0;)Landroidx/media3/exoplayer/s2;
    .locals 29
    .annotation build Landroidx/annotation/CheckResult;
    .end annotation

    move-object/from16 v0, p0

    move-object/from16 v2, p1

    new-instance v27, Landroidx/media3/exoplayer/s2;

    move-object/from16 v1, v27

    iget-object v3, v0, Landroidx/media3/exoplayer/s2;->b:Landroidx/media3/exoplayer/source/l$b;

    iget-wide v4, v0, Landroidx/media3/exoplayer/s2;->c:J

    iget-wide v6, v0, Landroidx/media3/exoplayer/s2;->d:J

    iget v8, v0, Landroidx/media3/exoplayer/s2;->e:I

    iget-object v9, v0, Landroidx/media3/exoplayer/s2;->f:Landroidx/media3/exoplayer/ExoPlaybackException;

    iget-boolean v10, v0, Landroidx/media3/exoplayer/s2;->g:Z

    iget-object v11, v0, Landroidx/media3/exoplayer/s2;->h:Lu2/k0;

    iget-object v12, v0, Landroidx/media3/exoplayer/s2;->i:Lx2/f0;

    iget-object v13, v0, Landroidx/media3/exoplayer/s2;->j:Ljava/util/List;

    iget-object v14, v0, Landroidx/media3/exoplayer/s2;->k:Landroidx/media3/exoplayer/source/l$b;

    iget-boolean v15, v0, Landroidx/media3/exoplayer/s2;->l:Z

    move-object/from16 p1, v1

    iget v1, v0, Landroidx/media3/exoplayer/s2;->m:I

    move/from16 v16, v1

    iget-object v1, v0, Landroidx/media3/exoplayer/s2;->n:Landroidx/media3/common/g0;

    move-object/from16 v17, v1

    move-object/from16 v28, v2

    iget-wide v1, v0, Landroidx/media3/exoplayer/s2;->p:J

    move-wide/from16 v18, v1

    iget-wide v1, v0, Landroidx/media3/exoplayer/s2;->q:J

    move-wide/from16 v20, v1

    iget-wide v1, v0, Landroidx/media3/exoplayer/s2;->r:J

    move-wide/from16 v22, v1

    iget-wide v1, v0, Landroidx/media3/exoplayer/s2;->s:J

    move-wide/from16 v24, v1

    iget-boolean v1, v0, Landroidx/media3/exoplayer/s2;->o:Z

    move/from16 v26, v1

    move-object/from16 v1, p1

    move-object/from16 v2, v28

    invoke-direct/range {v1 .. v26}, Landroidx/media3/exoplayer/s2;-><init>(Landroidx/media3/common/m0;Landroidx/media3/exoplayer/source/l$b;JJILandroidx/media3/exoplayer/ExoPlaybackException;ZLu2/k0;Lx2/f0;Ljava/util/List;Landroidx/media3/exoplayer/source/l$b;ZILandroidx/media3/common/g0;JJJJZ)V

    return-object v27
.end method

.method public m()J
    .locals 7

    invoke-virtual {p0}, Landroidx/media3/exoplayer/s2;->n()Z

    move-result v0

    if-nez v0, :cond_0

    iget-wide v0, p0, Landroidx/media3/exoplayer/s2;->r:J

    return-wide v0

    :cond_0
    iget-wide v0, p0, Landroidx/media3/exoplayer/s2;->s:J

    iget-wide v2, p0, Landroidx/media3/exoplayer/s2;->r:J

    iget-wide v4, p0, Landroidx/media3/exoplayer/s2;->s:J

    cmp-long v6, v0, v4

    if-nez v6, :cond_0

    invoke-static {}, Landroid/os/SystemClock;->elapsedRealtime()J

    move-result-wide v4

    sub-long/2addr v4, v0

    invoke-static {v2, v3}, Le2/u0;->B1(J)J

    move-result-wide v0

    long-to-float v2, v4

    iget-object v3, p0, Landroidx/media3/exoplayer/s2;->n:Landroidx/media3/common/g0;

    iget v3, v3, Landroidx/media3/common/g0;->a:F

    mul-float v2, v2, v3

    float-to-long v2, v2

    add-long/2addr v0, v2

    invoke-static {v0, v1}, Le2/u0;->S0(J)J

    move-result-wide v0

    return-wide v0
.end method

.method public n()Z
    .locals 2

    iget v0, p0, Landroidx/media3/exoplayer/s2;->e:I

    const/4 v1, 0x3

    if-ne v0, v1, :cond_0

    iget-boolean v0, p0, Landroidx/media3/exoplayer/s2;->l:Z

    if-eqz v0, :cond_0

    iget v0, p0, Landroidx/media3/exoplayer/s2;->m:I

    if-nez v0, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method public o(J)V
    .locals 0

    iput-wide p1, p0, Landroidx/media3/exoplayer/s2;->r:J

    invoke-static {}, Landroid/os/SystemClock;->elapsedRealtime()J

    move-result-wide p1

    iput-wide p1, p0, Landroidx/media3/exoplayer/s2;->s:J

    return-void
.end method
