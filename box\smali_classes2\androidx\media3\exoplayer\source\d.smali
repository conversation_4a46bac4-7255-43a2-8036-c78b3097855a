.class public final Landroidx/media3/exoplayer/source/d;
.super Ljava/lang/Object;

# interfaces
.implements Landroidx/media3/exoplayer/source/l$a;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Landroidx/media3/exoplayer/source/d$a;,
        Landroidx/media3/exoplayer/source/d$b;
    }
.end annotation


# instance fields
.field public final a:Landroidx/media3/exoplayer/source/d$a;

.field public b:Landroidx/media3/datasource/a$a;

.field public c:Lt3/s$a;

.field public d:Landroidx/media3/exoplayer/source/l$a;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public e:Landroidx/media3/exoplayer/source/e;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public f:Landroidx/media3/exoplayer/upstream/m;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public g:J

.field public h:J

.field public i:J

.field public j:F

.field public k:F

.field public l:Z


# direct methods
.method public constructor <init>(Landroid/content/Context;)V
    .locals 1

    new-instance v0, Landroidx/media3/datasource/b$a;

    invoke-direct {v0, p1}, Landroidx/media3/datasource/b$a;-><init>(Landroid/content/Context;)V

    invoke-direct {p0, v0}, Landroidx/media3/exoplayer/source/d;-><init>(Landroidx/media3/datasource/a$a;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Lz2/y;)V
    .locals 1

    new-instance v0, Landroidx/media3/datasource/b$a;

    invoke-direct {v0, p1}, Landroidx/media3/datasource/b$a;-><init>(Landroid/content/Context;)V

    invoke-direct {p0, v0, p2}, Landroidx/media3/exoplayer/source/d;-><init>(Landroidx/media3/datasource/a$a;Lz2/y;)V

    return-void
.end method

.method public constructor <init>(Landroidx/media3/datasource/a$a;)V
    .locals 1

    new-instance v0, Lz2/m;

    invoke-direct {v0}, Lz2/m;-><init>()V

    invoke-direct {p0, p1, v0}, Landroidx/media3/exoplayer/source/d;-><init>(Landroidx/media3/datasource/a$a;Lz2/y;)V

    return-void
.end method

.method public constructor <init>(Landroidx/media3/datasource/a$a;Lz2/y;)V
    .locals 2

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Landroidx/media3/exoplayer/source/d;->b:Landroidx/media3/datasource/a$a;

    new-instance v0, Lt3/h;

    invoke-direct {v0}, Lt3/h;-><init>()V

    iput-object v0, p0, Landroidx/media3/exoplayer/source/d;->c:Lt3/s$a;

    new-instance v1, Landroidx/media3/exoplayer/source/d$a;

    invoke-direct {v1, p2, v0}, Landroidx/media3/exoplayer/source/d$a;-><init>(Lz2/y;Lt3/s$a;)V

    iput-object v1, p0, Landroidx/media3/exoplayer/source/d;->a:Landroidx/media3/exoplayer/source/d$a;

    invoke-virtual {v1, p1}, Landroidx/media3/exoplayer/source/d$a;->n(Landroidx/media3/datasource/a$a;)V

    const-wide p1, -0x7fffffffffffffffL    # -4.9E-324

    iput-wide p1, p0, Landroidx/media3/exoplayer/source/d;->g:J

    iput-wide p1, p0, Landroidx/media3/exoplayer/source/d;->h:J

    iput-wide p1, p0, Landroidx/media3/exoplayer/source/d;->i:J

    const p1, -0x800001

    iput p1, p0, Landroidx/media3/exoplayer/source/d;->j:F

    iput p1, p0, Landroidx/media3/exoplayer/source/d;->k:F

    return-void
.end method

.method public static synthetic g(Landroidx/media3/exoplayer/source/d;Landroidx/media3/common/y;)[Lz2/s;
    .locals 0

    invoke-virtual {p0, p1}, Landroidx/media3/exoplayer/source/d;->k(Landroidx/media3/common/y;)[Lz2/s;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic h(Ljava/lang/Class;)Landroidx/media3/exoplayer/source/l$a;
    .locals 0

    invoke-static {p0}, Landroidx/media3/exoplayer/source/d;->n(Ljava/lang/Class;)Landroidx/media3/exoplayer/source/l$a;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic i(Ljava/lang/Class;Landroidx/media3/datasource/a$a;)Landroidx/media3/exoplayer/source/l$a;
    .locals 0

    invoke-static {p0, p1}, Landroidx/media3/exoplayer/source/d;->o(Ljava/lang/Class;Landroidx/media3/datasource/a$a;)Landroidx/media3/exoplayer/source/l$a;

    move-result-object p0

    return-object p0
.end method

.method public static l(Landroidx/media3/common/b0;Landroidx/media3/exoplayer/source/l;)Landroidx/media3/exoplayer/source/l;
    .locals 10

    iget-object v0, p0, Landroidx/media3/common/b0;->f:Landroidx/media3/common/b0$d;

    iget-wide v1, v0, Landroidx/media3/common/b0$d;->b:J

    const-wide/16 v3, 0x0

    cmp-long v5, v1, v3

    if-nez v5, :cond_0

    iget-wide v1, v0, Landroidx/media3/common/b0$d;->d:J

    const-wide/high16 v3, -0x8000000000000000L

    cmp-long v5, v1, v3

    if-nez v5, :cond_0

    iget-boolean v0, v0, Landroidx/media3/common/b0$d;->f:Z

    if-nez v0, :cond_0

    return-object p1

    :cond_0
    new-instance v0, Landroidx/media3/exoplayer/source/ClippingMediaSource;

    iget-object p0, p0, Landroidx/media3/common/b0;->f:Landroidx/media3/common/b0$d;

    iget-wide v3, p0, Landroidx/media3/common/b0$d;->b:J

    iget-wide v5, p0, Landroidx/media3/common/b0$d;->d:J

    iget-boolean v1, p0, Landroidx/media3/common/b0$d;->g:Z

    xor-int/lit8 v7, v1, 0x1

    iget-boolean v8, p0, Landroidx/media3/common/b0$d;->e:Z

    iget-boolean v9, p0, Landroidx/media3/common/b0$d;->f:Z

    move-object v1, v0

    move-object v2, p1

    invoke-direct/range {v1 .. v9}, Landroidx/media3/exoplayer/source/ClippingMediaSource;-><init>(Landroidx/media3/exoplayer/source/l;JJZZZ)V

    return-object v0
.end method

.method public static n(Ljava/lang/Class;)Landroidx/media3/exoplayer/source/l$a;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Class<",
            "+",
            "Landroidx/media3/exoplayer/source/l$a;",
            ">;)",
            "Landroidx/media3/exoplayer/source/l$a;"
        }
    .end annotation

    const/4 v0, 0x0

    :try_start_0
    new-array v1, v0, [Ljava/lang/Class;

    invoke-virtual {p0, v1}, Ljava/lang/Class;->getConstructor([Ljava/lang/Class;)Ljava/lang/reflect/Constructor;

    move-result-object p0

    new-array v0, v0, [Ljava/lang/Object;

    invoke-virtual {p0, v0}, Ljava/lang/reflect/Constructor;->newInstance([Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    check-cast p0, Landroidx/media3/exoplayer/source/l$a;
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    return-object p0

    :catch_0
    move-exception p0

    new-instance v0, Ljava/lang/IllegalStateException;

    invoke-direct {v0, p0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/Throwable;)V

    throw v0
.end method

.method public static o(Ljava/lang/Class;Landroidx/media3/datasource/a$a;)Landroidx/media3/exoplayer/source/l$a;
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Class<",
            "+",
            "Landroidx/media3/exoplayer/source/l$a;",
            ">;",
            "Landroidx/media3/datasource/a$a;",
            ")",
            "Landroidx/media3/exoplayer/source/l$a;"
        }
    .end annotation

    const/4 v0, 0x1

    :try_start_0
    new-array v1, v0, [Ljava/lang/Class;

    const-class v2, Landroidx/media3/datasource/a$a;

    const/4 v3, 0x0

    aput-object v2, v1, v3

    invoke-virtual {p0, v1}, Ljava/lang/Class;->getConstructor([Ljava/lang/Class;)Ljava/lang/reflect/Constructor;

    move-result-object p0

    new-array v0, v0, [Ljava/lang/Object;

    aput-object p1, v0, v3

    invoke-virtual {p0, v0}, Ljava/lang/reflect/Constructor;->newInstance([Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    check-cast p0, Landroidx/media3/exoplayer/source/l$a;
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    return-object p0

    :catch_0
    move-exception p0

    new-instance p1, Ljava/lang/IllegalStateException;

    invoke-direct {p1, p0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/Throwable;)V

    throw p1
.end method


# virtual methods
.method public bridge synthetic a(Lt3/s$a;)Landroidx/media3/exoplayer/source/l$a;
    .locals 0

    invoke-virtual {p0, p1}, Landroidx/media3/exoplayer/source/d;->t(Lt3/s$a;)Landroidx/media3/exoplayer/source/d;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic b(Z)Landroidx/media3/exoplayer/source/l$a;
    .locals 0

    invoke-virtual {p0, p1}, Landroidx/media3/exoplayer/source/d;->j(Z)Landroidx/media3/exoplayer/source/d;

    move-result-object p1

    return-object p1
.end method

.method public c(Landroidx/media3/common/b0;)Landroidx/media3/exoplayer/source/l;
    .locals 8

    iget-object v0, p1, Landroidx/media3/common/b0;->b:Landroidx/media3/common/b0$h;

    invoke-static {v0}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    iget-object v0, p1, Landroidx/media3/common/b0;->b:Landroidx/media3/common/b0$h;

    iget-object v0, v0, Landroidx/media3/common/b0$h;->a:Landroid/net/Uri;

    invoke-virtual {v0}, Landroid/net/Uri;->getScheme()Ljava/lang/String;

    move-result-object v0

    if-eqz v0, :cond_0

    const-string v1, "ssai"

    invoke-virtual {v0, v1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Landroidx/media3/exoplayer/source/d;->d:Landroidx/media3/exoplayer/source/l$a;

    invoke-static {v0}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroidx/media3/exoplayer/source/l$a;

    invoke-interface {v0, p1}, Landroidx/media3/exoplayer/source/l$a;->c(Landroidx/media3/common/b0;)Landroidx/media3/exoplayer/source/l;

    move-result-object p1

    return-object p1

    :cond_0
    iget-object v0, p1, Landroidx/media3/common/b0;->b:Landroidx/media3/common/b0$h;

    iget-object v0, v0, Landroidx/media3/common/b0$h;->b:Ljava/lang/String;

    const-string v1, "application/x-image-uri"

    invoke-static {v0, v1}, Ljava/util/Objects;->equals(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_1

    new-instance v0, Landroidx/media3/exoplayer/source/g$b;

    iget-object v1, p1, Landroidx/media3/common/b0;->b:Landroidx/media3/common/b0$h;

    iget-wide v1, v1, Landroidx/media3/common/b0$h;->i:J

    invoke-static {v1, v2}, Le2/u0;->S0(J)J

    move-result-wide v1

    iget-object v3, p0, Landroidx/media3/exoplayer/source/d;->e:Landroidx/media3/exoplayer/source/e;

    invoke-static {v3}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Landroidx/media3/exoplayer/source/e;

    invoke-direct {v0, v1, v2, v3}, Landroidx/media3/exoplayer/source/g$b;-><init>(JLandroidx/media3/exoplayer/source/e;)V

    invoke-virtual {v0, p1}, Landroidx/media3/exoplayer/source/g$b;->g(Landroidx/media3/common/b0;)Landroidx/media3/exoplayer/source/g;

    move-result-object p1

    return-object p1

    :cond_1
    iget-object v0, p1, Landroidx/media3/common/b0;->b:Landroidx/media3/common/b0$h;

    iget-object v1, v0, Landroidx/media3/common/b0$h;->a:Landroid/net/Uri;

    iget-object v0, v0, Landroidx/media3/common/b0$h;->b:Ljava/lang/String;

    invoke-static {v1, v0}, Le2/u0;->C0(Landroid/net/Uri;Ljava/lang/String;)I

    move-result v0

    iget-object v1, p1, Landroidx/media3/common/b0;->b:Landroidx/media3/common/b0$h;

    iget-wide v1, v1, Landroidx/media3/common/b0$h;->i:J

    const/4 v3, 0x1

    const-wide v4, -0x7fffffffffffffffL    # -4.9E-324

    cmp-long v6, v1, v4

    if-eqz v6, :cond_2

    iget-object v1, p0, Landroidx/media3/exoplayer/source/d;->a:Landroidx/media3/exoplayer/source/d$a;

    invoke-virtual {v1, v3}, Landroidx/media3/exoplayer/source/d$a;->p(I)V

    :cond_2
    iget-object v1, p0, Landroidx/media3/exoplayer/source/d;->a:Landroidx/media3/exoplayer/source/d$a;

    invoke-virtual {v1, v0}, Landroidx/media3/exoplayer/source/d$a;->f(I)Landroidx/media3/exoplayer/source/l$a;

    move-result-object v1

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v6, "No suitable media source factory found for content type: "

    invoke-virtual {v2, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-static {v1, v0}, Le2/a;->j(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    iget-object v0, p1, Landroidx/media3/common/b0;->d:Landroidx/media3/common/b0$g;

    invoke-virtual {v0}, Landroidx/media3/common/b0$g;->a()Landroidx/media3/common/b0$g$a;

    move-result-object v0

    iget-object v2, p1, Landroidx/media3/common/b0;->d:Landroidx/media3/common/b0$g;

    iget-wide v6, v2, Landroidx/media3/common/b0$g;->a:J

    cmp-long v2, v6, v4

    if-nez v2, :cond_3

    iget-wide v6, p0, Landroidx/media3/exoplayer/source/d;->g:J

    invoke-virtual {v0, v6, v7}, Landroidx/media3/common/b0$g$a;->k(J)Landroidx/media3/common/b0$g$a;

    :cond_3
    iget-object v2, p1, Landroidx/media3/common/b0;->d:Landroidx/media3/common/b0$g;

    iget v2, v2, Landroidx/media3/common/b0$g;->d:F

    const v6, -0x800001

    cmpl-float v2, v2, v6

    if-nez v2, :cond_4

    iget v2, p0, Landroidx/media3/exoplayer/source/d;->j:F

    invoke-virtual {v0, v2}, Landroidx/media3/common/b0$g$a;->j(F)Landroidx/media3/common/b0$g$a;

    :cond_4
    iget-object v2, p1, Landroidx/media3/common/b0;->d:Landroidx/media3/common/b0$g;

    iget v2, v2, Landroidx/media3/common/b0$g;->e:F

    cmpl-float v2, v2, v6

    if-nez v2, :cond_5

    iget v2, p0, Landroidx/media3/exoplayer/source/d;->k:F

    invoke-virtual {v0, v2}, Landroidx/media3/common/b0$g$a;->h(F)Landroidx/media3/common/b0$g$a;

    :cond_5
    iget-object v2, p1, Landroidx/media3/common/b0;->d:Landroidx/media3/common/b0$g;

    iget-wide v6, v2, Landroidx/media3/common/b0$g;->b:J

    cmp-long v2, v6, v4

    if-nez v2, :cond_6

    iget-wide v6, p0, Landroidx/media3/exoplayer/source/d;->h:J

    invoke-virtual {v0, v6, v7}, Landroidx/media3/common/b0$g$a;->i(J)Landroidx/media3/common/b0$g$a;

    :cond_6
    iget-object v2, p1, Landroidx/media3/common/b0;->d:Landroidx/media3/common/b0$g;

    iget-wide v6, v2, Landroidx/media3/common/b0$g;->c:J

    cmp-long v2, v6, v4

    if-nez v2, :cond_7

    iget-wide v6, p0, Landroidx/media3/exoplayer/source/d;->i:J

    invoke-virtual {v0, v6, v7}, Landroidx/media3/common/b0$g$a;->g(J)Landroidx/media3/common/b0$g$a;

    :cond_7
    invoke-virtual {v0}, Landroidx/media3/common/b0$g$a;->f()Landroidx/media3/common/b0$g;

    move-result-object v0

    iget-object v2, p1, Landroidx/media3/common/b0;->d:Landroidx/media3/common/b0$g;

    invoke-virtual {v0, v2}, Landroidx/media3/common/b0$g;->equals(Ljava/lang/Object;)Z

    move-result v2

    if-nez v2, :cond_8

    invoke-virtual {p1}, Landroidx/media3/common/b0;->a()Landroidx/media3/common/b0$c;

    move-result-object p1

    invoke-virtual {p1, v0}, Landroidx/media3/common/b0$c;->c(Landroidx/media3/common/b0$g;)Landroidx/media3/common/b0$c;

    move-result-object p1

    invoke-virtual {p1}, Landroidx/media3/common/b0$c;->a()Landroidx/media3/common/b0;

    move-result-object p1

    :cond_8
    invoke-interface {v1, p1}, Landroidx/media3/exoplayer/source/l$a;->c(Landroidx/media3/common/b0;)Landroidx/media3/exoplayer/source/l;

    move-result-object v0

    iget-object v1, p1, Landroidx/media3/common/b0;->b:Landroidx/media3/common/b0$h;

    invoke-static {v1}, Le2/u0;->i(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Landroidx/media3/common/b0$h;

    iget-object v1, v1, Landroidx/media3/common/b0$h;->f:Lcom/google/common/collect/ImmutableList;

    invoke-interface {v1}, Ljava/util/List;->isEmpty()Z

    move-result v2

    if-nez v2, :cond_d

    invoke-interface {v1}, Ljava/util/List;->size()I

    move-result v2

    add-int/2addr v2, v3

    new-array v2, v2, [Landroidx/media3/exoplayer/source/l;

    const/4 v3, 0x0

    aput-object v0, v2, v3

    :goto_0
    invoke-interface {v1}, Ljava/util/List;->size()I

    move-result v0

    if-ge v3, v0, :cond_c

    iget-boolean v0, p0, Landroidx/media3/exoplayer/source/d;->l:Z

    if-eqz v0, :cond_a

    new-instance v0, Landroidx/media3/common/y$b;

    invoke-direct {v0}, Landroidx/media3/common/y$b;-><init>()V

    invoke-interface {v1, v3}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v6

    check-cast v6, Landroidx/media3/common/b0$k;

    iget-object v6, v6, Landroidx/media3/common/b0$k;->b:Ljava/lang/String;

    invoke-virtual {v0, v6}, Landroidx/media3/common/y$b;->k0(Ljava/lang/String;)Landroidx/media3/common/y$b;

    move-result-object v0

    invoke-interface {v1, v3}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v6

    check-cast v6, Landroidx/media3/common/b0$k;

    iget-object v6, v6, Landroidx/media3/common/b0$k;->c:Ljava/lang/String;

    invoke-virtual {v0, v6}, Landroidx/media3/common/y$b;->b0(Ljava/lang/String;)Landroidx/media3/common/y$b;

    move-result-object v0

    invoke-interface {v1, v3}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v6

    check-cast v6, Landroidx/media3/common/b0$k;

    iget v6, v6, Landroidx/media3/common/b0$k;->d:I

    invoke-virtual {v0, v6}, Landroidx/media3/common/y$b;->m0(I)Landroidx/media3/common/y$b;

    move-result-object v0

    invoke-interface {v1, v3}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v6

    check-cast v6, Landroidx/media3/common/b0$k;

    iget v6, v6, Landroidx/media3/common/b0$k;->e:I

    invoke-virtual {v0, v6}, Landroidx/media3/common/y$b;->i0(I)Landroidx/media3/common/y$b;

    move-result-object v0

    invoke-interface {v1, v3}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v6

    check-cast v6, Landroidx/media3/common/b0$k;

    iget-object v6, v6, Landroidx/media3/common/b0$k;->f:Ljava/lang/String;

    invoke-virtual {v0, v6}, Landroidx/media3/common/y$b;->Z(Ljava/lang/String;)Landroidx/media3/common/y$b;

    move-result-object v0

    invoke-interface {v1, v3}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v6

    check-cast v6, Landroidx/media3/common/b0$k;

    iget-object v6, v6, Landroidx/media3/common/b0$k;->g:Ljava/lang/String;

    invoke-virtual {v0, v6}, Landroidx/media3/common/y$b;->X(Ljava/lang/String;)Landroidx/media3/common/y$b;

    move-result-object v0

    invoke-virtual {v0}, Landroidx/media3/common/y$b;->I()Landroidx/media3/common/y;

    move-result-object v0

    new-instance v6, Lu2/f;

    invoke-direct {v6, p0, v0}, Lu2/f;-><init>(Landroidx/media3/exoplayer/source/d;Landroidx/media3/common/y;)V

    new-instance v0, Landroidx/media3/exoplayer/source/q$b;

    iget-object v7, p0, Landroidx/media3/exoplayer/source/d;->b:Landroidx/media3/datasource/a$a;

    invoke-direct {v0, v7, v6}, Landroidx/media3/exoplayer/source/q$b;-><init>(Landroidx/media3/datasource/a$a;Lz2/y;)V

    iget-object v6, p0, Landroidx/media3/exoplayer/source/d;->f:Landroidx/media3/exoplayer/upstream/m;

    if-eqz v6, :cond_9

    invoke-virtual {v0, v6}, Landroidx/media3/exoplayer/source/q$b;->k(Landroidx/media3/exoplayer/upstream/m;)Landroidx/media3/exoplayer/source/q$b;

    :cond_9
    add-int/lit8 v6, v3, 0x1

    invoke-interface {v1, v3}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v7

    check-cast v7, Landroidx/media3/common/b0$k;

    iget-object v7, v7, Landroidx/media3/common/b0$k;->a:Landroid/net/Uri;

    invoke-virtual {v7}, Landroid/net/Uri;->toString()Ljava/lang/String;

    move-result-object v7

    invoke-static {v7}, Landroidx/media3/common/b0;->b(Ljava/lang/String;)Landroidx/media3/common/b0;

    move-result-object v7

    invoke-virtual {v0, v7}, Landroidx/media3/exoplayer/source/q$b;->h(Landroidx/media3/common/b0;)Landroidx/media3/exoplayer/source/q;

    move-result-object v0

    aput-object v0, v2, v6

    goto :goto_1

    :cond_a
    new-instance v0, Landroidx/media3/exoplayer/source/v$b;

    iget-object v6, p0, Landroidx/media3/exoplayer/source/d;->b:Landroidx/media3/datasource/a$a;

    invoke-direct {v0, v6}, Landroidx/media3/exoplayer/source/v$b;-><init>(Landroidx/media3/datasource/a$a;)V

    iget-object v6, p0, Landroidx/media3/exoplayer/source/d;->f:Landroidx/media3/exoplayer/upstream/m;

    if-eqz v6, :cond_b

    invoke-virtual {v0, v6}, Landroidx/media3/exoplayer/source/v$b;->b(Landroidx/media3/exoplayer/upstream/m;)Landroidx/media3/exoplayer/source/v$b;

    :cond_b
    add-int/lit8 v6, v3, 0x1

    invoke-interface {v1, v3}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v7

    check-cast v7, Landroidx/media3/common/b0$k;

    invoke-virtual {v0, v7, v4, v5}, Landroidx/media3/exoplayer/source/v$b;->a(Landroidx/media3/common/b0$k;J)Landroidx/media3/exoplayer/source/v;

    move-result-object v0

    aput-object v0, v2, v6

    :goto_1
    add-int/lit8 v3, v3, 0x1

    goto/16 :goto_0

    :cond_c
    new-instance v0, Landroidx/media3/exoplayer/source/MergingMediaSource;

    invoke-direct {v0, v2}, Landroidx/media3/exoplayer/source/MergingMediaSource;-><init>([Landroidx/media3/exoplayer/source/l;)V

    :cond_d
    invoke-static {p1, v0}, Landroidx/media3/exoplayer/source/d;->l(Landroidx/media3/common/b0;Landroidx/media3/exoplayer/source/l;)Landroidx/media3/exoplayer/source/l;

    move-result-object v0

    invoke-virtual {p0, p1, v0}, Landroidx/media3/exoplayer/source/d;->m(Landroidx/media3/common/b0;Landroidx/media3/exoplayer/source/l;)Landroidx/media3/exoplayer/source/l;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic d(Ln2/u;)Landroidx/media3/exoplayer/source/l$a;
    .locals 0

    invoke-virtual {p0, p1}, Landroidx/media3/exoplayer/source/d;->r(Ln2/u;)Landroidx/media3/exoplayer/source/d;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic e(Landroidx/media3/exoplayer/upstream/m;)Landroidx/media3/exoplayer/source/l$a;
    .locals 0

    invoke-virtual {p0, p1}, Landroidx/media3/exoplayer/source/d;->s(Landroidx/media3/exoplayer/upstream/m;)Landroidx/media3/exoplayer/source/d;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic f(Landroidx/media3/exoplayer/upstream/f$a;)Landroidx/media3/exoplayer/source/l$a;
    .locals 0

    invoke-virtual {p0, p1}, Landroidx/media3/exoplayer/source/d;->p(Landroidx/media3/exoplayer/upstream/f$a;)Landroidx/media3/exoplayer/source/d;

    move-result-object p1

    return-object p1
.end method

.method public j(Z)Landroidx/media3/exoplayer/source/d;
    .locals 1

    iput-boolean p1, p0, Landroidx/media3/exoplayer/source/d;->l:Z

    iget-object v0, p0, Landroidx/media3/exoplayer/source/d;->a:Landroidx/media3/exoplayer/source/d$a;

    invoke-virtual {v0, p1}, Landroidx/media3/exoplayer/source/d$a;->r(Z)V

    return-object p0
.end method

.method public final synthetic k(Landroidx/media3/common/y;)[Lz2/s;
    .locals 3

    const/4 v0, 0x1

    new-array v0, v0, [Lz2/s;

    iget-object v1, p0, Landroidx/media3/exoplayer/source/d;->c:Lt3/s$a;

    invoke-interface {v1, p1}, Lt3/s$a;->a(Landroidx/media3/common/y;)Z

    move-result v1

    if-eqz v1, :cond_0

    new-instance v1, Lt3/n;

    iget-object v2, p0, Landroidx/media3/exoplayer/source/d;->c:Lt3/s$a;

    invoke-interface {v2, p1}, Lt3/s$a;->c(Landroidx/media3/common/y;)Lt3/s;

    move-result-object v2

    invoke-direct {v1, v2, p1}, Lt3/n;-><init>(Lt3/s;Landroidx/media3/common/y;)V

    goto :goto_0

    :cond_0
    new-instance v1, Landroidx/media3/exoplayer/source/d$b;

    invoke-direct {v1, p1}, Landroidx/media3/exoplayer/source/d$b;-><init>(Landroidx/media3/common/y;)V

    :goto_0
    const/4 p1, 0x0

    aput-object v1, v0, p1

    return-object v0
.end method

.method public final m(Landroidx/media3/common/b0;Landroidx/media3/exoplayer/source/l;)Landroidx/media3/exoplayer/source/l;
    .locals 1

    iget-object v0, p1, Landroidx/media3/common/b0;->b:Landroidx/media3/common/b0$h;

    invoke-static {v0}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    iget-object p1, p1, Landroidx/media3/common/b0;->b:Landroidx/media3/common/b0$h;

    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    return-object p2
.end method

.method public p(Landroidx/media3/exoplayer/upstream/f$a;)Landroidx/media3/exoplayer/source/d;
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/source/d;->a:Landroidx/media3/exoplayer/source/d$a;

    invoke-static {p1}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Landroidx/media3/exoplayer/upstream/f$a;

    invoke-virtual {v0, p1}, Landroidx/media3/exoplayer/source/d$a;->m(Landroidx/media3/exoplayer/upstream/f$a;)V

    return-object p0
.end method

.method public q(Landroidx/media3/datasource/a$a;)Landroidx/media3/exoplayer/source/d;
    .locals 1

    iput-object p1, p0, Landroidx/media3/exoplayer/source/d;->b:Landroidx/media3/datasource/a$a;

    iget-object v0, p0, Landroidx/media3/exoplayer/source/d;->a:Landroidx/media3/exoplayer/source/d$a;

    invoke-virtual {v0, p1}, Landroidx/media3/exoplayer/source/d$a;->n(Landroidx/media3/datasource/a$a;)V

    return-object p0
.end method

.method public r(Ln2/u;)Landroidx/media3/exoplayer/source/d;
    .locals 2

    iget-object v0, p0, Landroidx/media3/exoplayer/source/d;->a:Landroidx/media3/exoplayer/source/d$a;

    const-string v1, "MediaSource.Factory#setDrmSessionManagerProvider no longer handles null by instantiating a new DefaultDrmSessionManagerProvider. Explicitly construct and pass an instance in order to retain the old behavior."

    invoke-static {p1, v1}, Le2/a;->f(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ln2/u;

    invoke-virtual {v0, p1}, Landroidx/media3/exoplayer/source/d$a;->o(Ln2/u;)V

    return-object p0
.end method

.method public s(Landroidx/media3/exoplayer/upstream/m;)Landroidx/media3/exoplayer/source/d;
    .locals 1

    const-string v0, "MediaSource.Factory#setLoadErrorHandlingPolicy no longer handles null by instantiating a new DefaultLoadErrorHandlingPolicy. Explicitly construct and pass an instance in order to retain the old behavior."

    invoke-static {p1, v0}, Le2/a;->f(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroidx/media3/exoplayer/upstream/m;

    iput-object v0, p0, Landroidx/media3/exoplayer/source/d;->f:Landroidx/media3/exoplayer/upstream/m;

    iget-object v0, p0, Landroidx/media3/exoplayer/source/d;->a:Landroidx/media3/exoplayer/source/d$a;

    invoke-virtual {v0, p1}, Landroidx/media3/exoplayer/source/d$a;->q(Landroidx/media3/exoplayer/upstream/m;)V

    return-object p0
.end method

.method public t(Lt3/s$a;)Landroidx/media3/exoplayer/source/d;
    .locals 1

    invoke-static {p1}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lt3/s$a;

    iput-object v0, p0, Landroidx/media3/exoplayer/source/d;->c:Lt3/s$a;

    iget-object v0, p0, Landroidx/media3/exoplayer/source/d;->a:Landroidx/media3/exoplayer/source/d$a;

    invoke-virtual {v0, p1}, Landroidx/media3/exoplayer/source/d$a;->s(Lt3/s$a;)V

    return-object p0
.end method
