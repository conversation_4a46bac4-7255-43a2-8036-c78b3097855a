.class public final Landroidx/media3/exoplayer/source/n$a;
.super Ljava/lang/Object;

# interfaces
.implements Lx2/z;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/media3/exoplayer/source/n;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation


# instance fields
.field public final a:Lx2/z;

.field public final b:Landroidx/media3/common/n0;


# direct methods
.method public constructor <init>(Lx2/z;Landroidx/media3/common/n0;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Landroidx/media3/exoplayer/source/n$a;->a:Lx2/z;

    iput-object p2, p0, Landroidx/media3/exoplayer/source/n$a;->b:Landroidx/media3/common/n0;

    return-void
.end method


# virtual methods
.method public a()V
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/source/n$a;->a:Lx2/z;

    invoke-interface {v0}, Lx2/z;->a()V

    return-void
.end method

.method public b(Z)V
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/source/n$a;->a:Lx2/z;

    invoke-interface {v0, p1}, Lx2/z;->b(Z)V

    return-void
.end method

.method public c()V
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/source/n$a;->a:Lx2/z;

    invoke-interface {v0}, Lx2/z;->c()V

    return-void
.end method

.method public d(IJ)Z
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/source/n$a;->a:Lx2/z;

    invoke-interface {v0, p1, p2, p3}, Lx2/z;->d(IJ)Z

    move-result p1

    return p1
.end method

.method public disable()V
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/source/n$a;->a:Lx2/z;

    invoke-interface {v0}, Lx2/z;->disable()V

    return-void
.end method

.method public e(Landroidx/media3/common/y;)I
    .locals 2

    iget-object v0, p0, Landroidx/media3/exoplayer/source/n$a;->a:Lx2/z;

    iget-object v1, p0, Landroidx/media3/exoplayer/source/n$a;->b:Landroidx/media3/common/n0;

    invoke-virtual {v1, p1}, Landroidx/media3/common/n0;->b(Landroidx/media3/common/y;)I

    move-result p1

    invoke-interface {v0, p1}, Lx2/c0;->indexOf(I)I

    move-result p1

    return p1
.end method

.method public enable()V
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/source/n$a;->a:Lx2/z;

    invoke-interface {v0}, Lx2/z;->enable()V

    return-void
.end method

.method public equals(Ljava/lang/Object;)Z
    .locals 4
    .param p1    # Ljava/lang/Object;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    const/4 v0, 0x1

    if-ne p0, p1, :cond_0

    return v0

    :cond_0
    instance-of v1, p1, Landroidx/media3/exoplayer/source/n$a;

    const/4 v2, 0x0

    if-nez v1, :cond_1

    return v2

    :cond_1
    check-cast p1, Landroidx/media3/exoplayer/source/n$a;

    iget-object v1, p0, Landroidx/media3/exoplayer/source/n$a;->a:Lx2/z;

    iget-object v3, p1, Landroidx/media3/exoplayer/source/n$a;->a:Lx2/z;

    invoke-virtual {v1, v3}, Ljava/lang/Object;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_2

    iget-object v1, p0, Landroidx/media3/exoplayer/source/n$a;->b:Landroidx/media3/common/n0;

    iget-object p1, p1, Landroidx/media3/exoplayer/source/n$a;->b:Landroidx/media3/common/n0;

    invoke-virtual {v1, p1}, Landroidx/media3/common/n0;->equals(Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_2

    goto :goto_0

    :cond_2
    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method public evaluateQueueSize(JLjava/util/List;)I
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(J",
            "Ljava/util/List<",
            "+",
            "Lv2/m;",
            ">;)I"
        }
    .end annotation

    iget-object v0, p0, Landroidx/media3/exoplayer/source/n$a;->a:Lx2/z;

    invoke-interface {v0, p1, p2, p3}, Lx2/z;->evaluateQueueSize(JLjava/util/List;)I

    move-result p1

    return p1
.end method

.method public f(JLv2/e;Ljava/util/List;)Z
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(J",
            "Lv2/e;",
            "Ljava/util/List<",
            "+",
            "Lv2/m;",
            ">;)Z"
        }
    .end annotation

    iget-object v0, p0, Landroidx/media3/exoplayer/source/n$a;->a:Lx2/z;

    invoke-interface {v0, p1, p2, p3, p4}, Lx2/z;->f(JLv2/e;Ljava/util/List;)Z

    move-result p1

    return p1
.end method

.method public g(JJJLjava/util/List;[Lv2/n;)V
    .locals 10
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(JJJ",
            "Ljava/util/List<",
            "+",
            "Lv2/m;",
            ">;[",
            "Lv2/n;",
            ")V"
        }
    .end annotation

    move-object v0, p0

    iget-object v1, v0, Landroidx/media3/exoplayer/source/n$a;->a:Lx2/z;

    move-wide v2, p1

    move-wide v4, p3

    move-wide v6, p5

    move-object/from16 v8, p7

    move-object/from16 v9, p8

    invoke-interface/range {v1 .. v9}, Lx2/z;->g(JJJLjava/util/List;[Lv2/n;)V

    return-void
.end method

.method public getFormat(I)Landroidx/media3/common/y;
    .locals 2

    iget-object v0, p0, Landroidx/media3/exoplayer/source/n$a;->b:Landroidx/media3/common/n0;

    iget-object v1, p0, Landroidx/media3/exoplayer/source/n$a;->a:Lx2/z;

    invoke-interface {v1, p1}, Lx2/c0;->getIndexInTrackGroup(I)I

    move-result p1

    invoke-virtual {v0, p1}, Landroidx/media3/common/n0;->a(I)Landroidx/media3/common/y;

    move-result-object p1

    return-object p1
.end method

.method public getIndexInTrackGroup(I)I
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/source/n$a;->a:Lx2/z;

    invoke-interface {v0, p1}, Lx2/c0;->getIndexInTrackGroup(I)I

    move-result p1

    return p1
.end method

.method public getSelectedFormat()Landroidx/media3/common/y;
    .locals 2

    iget-object v0, p0, Landroidx/media3/exoplayer/source/n$a;->b:Landroidx/media3/common/n0;

    iget-object v1, p0, Landroidx/media3/exoplayer/source/n$a;->a:Lx2/z;

    invoke-interface {v1}, Lx2/z;->getSelectedIndexInTrackGroup()I

    move-result v1

    invoke-virtual {v0, v1}, Landroidx/media3/common/n0;->a(I)Landroidx/media3/common/y;

    move-result-object v0

    return-object v0
.end method

.method public getSelectedIndex()I
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/source/n$a;->a:Lx2/z;

    invoke-interface {v0}, Lx2/z;->getSelectedIndex()I

    move-result v0

    return v0
.end method

.method public getSelectedIndexInTrackGroup()I
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/source/n$a;->a:Lx2/z;

    invoke-interface {v0}, Lx2/z;->getSelectedIndexInTrackGroup()I

    move-result v0

    return v0
.end method

.method public getSelectionData()Ljava/lang/Object;
    .locals 1
    .annotation build Landroidx/annotation/Nullable;
    .end annotation

    iget-object v0, p0, Landroidx/media3/exoplayer/source/n$a;->a:Lx2/z;

    invoke-interface {v0}, Lx2/z;->getSelectionData()Ljava/lang/Object;

    move-result-object v0

    return-object v0
.end method

.method public getSelectionReason()I
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/source/n$a;->a:Lx2/z;

    invoke-interface {v0}, Lx2/z;->getSelectionReason()I

    move-result v0

    return v0
.end method

.method public getTrackGroup()Landroidx/media3/common/n0;
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/source/n$a;->b:Landroidx/media3/common/n0;

    return-object v0
.end method

.method public h(IJ)Z
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/source/n$a;->a:Lx2/z;

    invoke-interface {v0, p1, p2, p3}, Lx2/z;->h(IJ)Z

    move-result p1

    return p1
.end method

.method public hashCode()I
    .locals 2

    iget-object v0, p0, Landroidx/media3/exoplayer/source/n$a;->b:Landroidx/media3/common/n0;

    invoke-virtual {v0}, Landroidx/media3/common/n0;->hashCode()I

    move-result v0

    const/16 v1, 0x20f

    add-int/2addr v1, v0

    mul-int/lit8 v1, v1, 0x1f

    iget-object v0, p0, Landroidx/media3/exoplayer/source/n$a;->a:Lx2/z;

    invoke-virtual {v0}, Ljava/lang/Object;->hashCode()I

    move-result v0

    add-int/2addr v1, v0

    return v1
.end method

.method public indexOf(I)I
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/source/n$a;->a:Lx2/z;

    invoke-interface {v0, p1}, Lx2/c0;->indexOf(I)I

    move-result p1

    return p1
.end method

.method public length()I
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/source/n$a;->a:Lx2/z;

    invoke-interface {v0}, Lx2/c0;->length()I

    move-result v0

    return v0
.end method

.method public onPlaybackSpeed(F)V
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/source/n$a;->a:Lx2/z;

    invoke-interface {v0, p1}, Lx2/z;->onPlaybackSpeed(F)V

    return-void
.end method
