.class public Landroidx/media3/exoplayer/source/q$a;
.super Lu2/m;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Landroidx/media3/exoplayer/source/q;->D()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field public final synthetic g:Landroidx/media3/exoplayer/source/q;


# direct methods
.method public constructor <init>(Landroidx/media3/exoplayer/source/q;Landroidx/media3/common/m0;)V
    .locals 0

    iput-object p1, p0, Landroidx/media3/exoplayer/source/q$a;->g:Landroidx/media3/exoplayer/source/q;

    invoke-direct {p0, p2}, Lu2/m;-><init>(Landroidx/media3/common/m0;)V

    return-void
.end method


# virtual methods
.method public g(ILandroidx/media3/common/m0$b;Z)Landroidx/media3/common/m0$b;
    .locals 0

    invoke-super {p0, p1, p2, p3}, Lu2/m;->g(ILandroidx/media3/common/m0$b;Z)Landroidx/media3/common/m0$b;

    const/4 p1, 0x1

    iput-boolean p1, p2, Landroidx/media3/common/m0$b;->f:Z

    return-object p2
.end method

.method public o(ILandroidx/media3/common/m0$c;J)Landroidx/media3/common/m0$c;
    .locals 0

    invoke-super {p0, p1, p2, p3, p4}, Lu2/m;->o(ILandroidx/media3/common/m0$c;J)Landroidx/media3/common/m0$c;

    const/4 p1, 0x1

    iput-boolean p1, p2, Landroidx/media3/common/m0$c;->l:Z

    return-object p2
.end method
