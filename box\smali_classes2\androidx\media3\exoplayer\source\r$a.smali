.class public final Landroidx/media3/exoplayer/source/r$a;
.super Ljava/lang/Object;

# interfaces
.implements Landroidx/media3/exoplayer/upstream/b$a;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/media3/exoplayer/source/r;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation


# instance fields
.field public a:J

.field public b:J

.field public c:Landroidx/media3/exoplayer/upstream/a;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public d:Landroidx/media3/exoplayer/source/r$a;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field


# direct methods
.method public constructor <init>(JI)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    invoke-virtual {p0, p1, p2, p3}, Landroidx/media3/exoplayer/source/r$a;->d(JI)V

    return-void
.end method


# virtual methods
.method public a()Landroidx/media3/exoplayer/upstream/a;
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/source/r$a;->c:Landroidx/media3/exoplayer/upstream/a;

    invoke-static {v0}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroidx/media3/exoplayer/upstream/a;

    return-object v0
.end method

.method public b()Landroidx/media3/exoplayer/source/r$a;
    .locals 2

    const/4 v0, 0x0

    iput-object v0, p0, Landroidx/media3/exoplayer/source/r$a;->c:Landroidx/media3/exoplayer/upstream/a;

    iget-object v1, p0, Landroidx/media3/exoplayer/source/r$a;->d:Landroidx/media3/exoplayer/source/r$a;

    iput-object v0, p0, Landroidx/media3/exoplayer/source/r$a;->d:Landroidx/media3/exoplayer/source/r$a;

    return-object v1
.end method

.method public c(Landroidx/media3/exoplayer/upstream/a;Landroidx/media3/exoplayer/source/r$a;)V
    .locals 0

    iput-object p1, p0, Landroidx/media3/exoplayer/source/r$a;->c:Landroidx/media3/exoplayer/upstream/a;

    iput-object p2, p0, Landroidx/media3/exoplayer/source/r$a;->d:Landroidx/media3/exoplayer/source/r$a;

    return-void
.end method

.method public d(JI)V
    .locals 2

    iget-object v0, p0, Landroidx/media3/exoplayer/source/r$a;->c:Landroidx/media3/exoplayer/upstream/a;

    if-nez v0, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    invoke-static {v0}, Le2/a;->g(Z)V

    iput-wide p1, p0, Landroidx/media3/exoplayer/source/r$a;->a:J

    int-to-long v0, p3

    add-long/2addr p1, v0

    iput-wide p1, p0, Landroidx/media3/exoplayer/source/r$a;->b:J

    return-void
.end method

.method public e(J)I
    .locals 2

    iget-wide v0, p0, Landroidx/media3/exoplayer/source/r$a;->a:J

    sub-long/2addr p1, v0

    long-to-int p2, p1

    iget-object p1, p0, Landroidx/media3/exoplayer/source/r$a;->c:Landroidx/media3/exoplayer/upstream/a;

    iget p1, p1, Landroidx/media3/exoplayer/upstream/a;->b:I

    add-int/2addr p2, p1

    return p2
.end method

.method public next()Landroidx/media3/exoplayer/upstream/b$a;
    .locals 2
    .annotation build Landroidx/annotation/Nullable;
    .end annotation

    iget-object v0, p0, Landroidx/media3/exoplayer/source/r$a;->d:Landroidx/media3/exoplayer/source/r$a;

    if-eqz v0, :cond_1

    iget-object v1, v0, Landroidx/media3/exoplayer/source/r$a;->c:Landroidx/media3/exoplayer/upstream/a;

    if-nez v1, :cond_0

    goto :goto_0

    :cond_0
    return-object v0

    :cond_1
    :goto_0
    const/4 v0, 0x0

    return-object v0
.end method
