.class public Landroidx/media3/exoplayer/source/s;
.super Ljava/lang/Object;

# interfaces
.implements Lz2/r0;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Landroidx/media3/exoplayer/source/s$b;,
        Landroidx/media3/exoplayer/source/s$c;,
        Landroidx/media3/exoplayer/source/s$d;
    }
.end annotation


# instance fields
.field public A:Landroidx/media3/common/y;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public B:Landroidx/media3/common/y;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public C:J

.field public D:Z

.field public E:Z

.field public F:J

.field public G:Z

.field public final a:Landroidx/media3/exoplayer/source/r;

.field public final b:Landroidx/media3/exoplayer/source/s$b;

.field public final c:Lu2/h0;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lu2/h0<",
            "Landroidx/media3/exoplayer/source/s$c;",
            ">;"
        }
    .end annotation
.end field

.field public final d:Landroidx/media3/exoplayer/drm/c;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public final e:Landroidx/media3/exoplayer/drm/b$a;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public f:Landroidx/media3/exoplayer/source/s$d;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public g:Landroidx/media3/common/y;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public h:Landroidx/media3/exoplayer/drm/DrmSession;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public i:I

.field public j:[J

.field public k:[J

.field public l:[I

.field public m:[I

.field public n:[J

.field public o:[Lz2/r0$a;

.field public p:I

.field public q:I

.field public r:I

.field public s:I

.field public t:J

.field public u:J

.field public v:J

.field public w:Z

.field public x:Z

.field public y:Z

.field public z:Z


# direct methods
.method public constructor <init>(Landroidx/media3/exoplayer/upstream/b;Landroidx/media3/exoplayer/drm/c;Landroidx/media3/exoplayer/drm/b$a;)V
    .locals 0
    .param p2    # Landroidx/media3/exoplayer/drm/c;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .param p3    # Landroidx/media3/exoplayer/drm/b$a;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p2, p0, Landroidx/media3/exoplayer/source/s;->d:Landroidx/media3/exoplayer/drm/c;

    iput-object p3, p0, Landroidx/media3/exoplayer/source/s;->e:Landroidx/media3/exoplayer/drm/b$a;

    new-instance p2, Landroidx/media3/exoplayer/source/r;

    invoke-direct {p2, p1}, Landroidx/media3/exoplayer/source/r;-><init>(Landroidx/media3/exoplayer/upstream/b;)V

    iput-object p2, p0, Landroidx/media3/exoplayer/source/s;->a:Landroidx/media3/exoplayer/source/r;

    new-instance p1, Landroidx/media3/exoplayer/source/s$b;

    invoke-direct {p1}, Landroidx/media3/exoplayer/source/s$b;-><init>()V

    iput-object p1, p0, Landroidx/media3/exoplayer/source/s;->b:Landroidx/media3/exoplayer/source/s$b;

    const/16 p1, 0x3e8

    iput p1, p0, Landroidx/media3/exoplayer/source/s;->i:I

    new-array p2, p1, [J

    iput-object p2, p0, Landroidx/media3/exoplayer/source/s;->j:[J

    new-array p2, p1, [J

    iput-object p2, p0, Landroidx/media3/exoplayer/source/s;->k:[J

    new-array p2, p1, [J

    iput-object p2, p0, Landroidx/media3/exoplayer/source/s;->n:[J

    new-array p2, p1, [I

    iput-object p2, p0, Landroidx/media3/exoplayer/source/s;->m:[I

    new-array p2, p1, [I

    iput-object p2, p0, Landroidx/media3/exoplayer/source/s;->l:[I

    new-array p1, p1, [Lz2/r0$a;

    iput-object p1, p0, Landroidx/media3/exoplayer/source/s;->o:[Lz2/r0$a;

    new-instance p1, Lu2/h0;

    new-instance p2, Lu2/d0;

    invoke-direct {p2}, Lu2/d0;-><init>()V

    invoke-direct {p1, p2}, Lu2/h0;-><init>(Le2/h;)V

    iput-object p1, p0, Landroidx/media3/exoplayer/source/s;->c:Lu2/h0;

    const-wide/high16 p1, -0x8000000000000000L

    iput-wide p1, p0, Landroidx/media3/exoplayer/source/s;->t:J

    iput-wide p1, p0, Landroidx/media3/exoplayer/source/s;->u:J

    iput-wide p1, p0, Landroidx/media3/exoplayer/source/s;->v:J

    const/4 p1, 0x1

    iput-boolean p1, p0, Landroidx/media3/exoplayer/source/s;->y:Z

    iput-boolean p1, p0, Landroidx/media3/exoplayer/source/s;->x:Z

    iput-boolean p1, p0, Landroidx/media3/exoplayer/source/s;->D:Z

    return-void
.end method

.method public static synthetic M(Landroidx/media3/exoplayer/source/s$c;)V
    .locals 0

    iget-object p0, p0, Landroidx/media3/exoplayer/source/s$c;->b:Landroidx/media3/exoplayer/drm/c$b;

    invoke-interface {p0}, Landroidx/media3/exoplayer/drm/c$b;->release()V

    return-void
.end method

.method public static synthetic g(Landroidx/media3/exoplayer/source/s$c;)V
    .locals 0

    invoke-static {p0}, Landroidx/media3/exoplayer/source/s;->M(Landroidx/media3/exoplayer/source/s$c;)V

    return-void
.end method

.method public static k(Landroidx/media3/exoplayer/upstream/b;Landroidx/media3/exoplayer/drm/c;Landroidx/media3/exoplayer/drm/b$a;)Landroidx/media3/exoplayer/source/s;
    .locals 1

    new-instance v0, Landroidx/media3/exoplayer/source/s;

    invoke-static {p1}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Landroidx/media3/exoplayer/drm/c;

    invoke-static {p2}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p2

    check-cast p2, Landroidx/media3/exoplayer/drm/b$a;

    invoke-direct {v0, p0, p1, p2}, Landroidx/media3/exoplayer/source/s;-><init>(Landroidx/media3/exoplayer/upstream/b;Landroidx/media3/exoplayer/drm/c;Landroidx/media3/exoplayer/drm/b$a;)V

    return-object v0
.end method

.method public static l(Landroidx/media3/exoplayer/upstream/b;)Landroidx/media3/exoplayer/source/s;
    .locals 2

    new-instance v0, Landroidx/media3/exoplayer/source/s;

    const/4 v1, 0x0

    invoke-direct {v0, p0, v1, v1}, Landroidx/media3/exoplayer/source/s;-><init>(Landroidx/media3/exoplayer/upstream/b;Landroidx/media3/exoplayer/drm/c;Landroidx/media3/exoplayer/drm/b$a;)V

    return-object v0
.end method


# virtual methods
.method public final declared-synchronized A()J
    .locals 2

    monitor-enter p0

    :try_start_0
    iget-wide v0, p0, Landroidx/media3/exoplayer/source/s;->v:J
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    monitor-exit p0

    return-wide v0

    :catchall_0
    move-exception v0

    monitor-exit p0

    throw v0
.end method

.method public final declared-synchronized B()J
    .locals 4

    monitor-enter p0

    :try_start_0
    iget-wide v0, p0, Landroidx/media3/exoplayer/source/s;->u:J

    iget v2, p0, Landroidx/media3/exoplayer/source/s;->s:I

    invoke-virtual {p0, v2}, Landroidx/media3/exoplayer/source/s;->C(I)J

    move-result-wide v2

    invoke-static {v0, v1, v2, v3}, Ljava/lang/Math;->max(JJ)J

    move-result-wide v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    monitor-exit p0

    return-wide v0

    :catchall_0
    move-exception v0

    monitor-exit p0

    throw v0
.end method

.method public final C(I)J
    .locals 7

    const-wide/high16 v0, -0x8000000000000000L

    if-nez p1, :cond_0

    return-wide v0

    :cond_0
    add-int/lit8 v2, p1, -0x1

    invoke-virtual {p0, v2}, Landroidx/media3/exoplayer/source/s;->E(I)I

    move-result v2

    const/4 v3, 0x0

    :goto_0
    if-ge v3, p1, :cond_3

    iget-object v4, p0, Landroidx/media3/exoplayer/source/s;->n:[J

    aget-wide v5, v4, v2

    invoke-static {v0, v1, v5, v6}, Ljava/lang/Math;->max(JJ)J

    move-result-wide v0

    iget-object v4, p0, Landroidx/media3/exoplayer/source/s;->m:[I

    aget v4, v4, v2

    and-int/lit8 v4, v4, 0x1

    if-eqz v4, :cond_1

    goto :goto_1

    :cond_1
    add-int/lit8 v2, v2, -0x1

    const/4 v4, -0x1

    if-ne v2, v4, :cond_2

    iget v2, p0, Landroidx/media3/exoplayer/source/s;->i:I

    add-int/lit8 v2, v2, -0x1

    :cond_2
    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    :cond_3
    :goto_1
    return-wide v0
.end method

.method public final D()I
    .locals 2

    iget v0, p0, Landroidx/media3/exoplayer/source/s;->q:I

    iget v1, p0, Landroidx/media3/exoplayer/source/s;->s:I

    add-int/2addr v0, v1

    return v0
.end method

.method public final E(I)I
    .locals 1

    iget v0, p0, Landroidx/media3/exoplayer/source/s;->r:I

    add-int/2addr v0, p1

    iget p1, p0, Landroidx/media3/exoplayer/source/s;->i:I

    if-ge v0, p1, :cond_0

    goto :goto_0

    :cond_0
    sub-int/2addr v0, p1

    :goto_0
    return v0
.end method

.method public final declared-synchronized F(JZ)I
    .locals 8

    monitor-enter p0

    :try_start_0
    iget v0, p0, Landroidx/media3/exoplayer/source/s;->s:I

    invoke-virtual {p0, v0}, Landroidx/media3/exoplayer/source/s;->E(I)I

    move-result v2

    invoke-virtual {p0}, Landroidx/media3/exoplayer/source/s;->I()Z

    move-result v0

    const/4 v7, 0x0

    if-eqz v0, :cond_3

    iget-object v0, p0, Landroidx/media3/exoplayer/source/s;->n:[J

    aget-wide v3, v0, v2

    cmp-long v0, p1, v3

    if-gez v0, :cond_0

    goto :goto_0

    :cond_0
    iget-wide v0, p0, Landroidx/media3/exoplayer/source/s;->v:J

    cmp-long v3, p1, v0

    if-lez v3, :cond_1

    if-eqz p3, :cond_1

    iget p1, p0, Landroidx/media3/exoplayer/source/s;->p:I

    iget p2, p0, Landroidx/media3/exoplayer/source/s;->s:I
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    sub-int/2addr p1, p2

    monitor-exit p0

    return p1

    :catchall_0
    move-exception p1

    goto :goto_1

    :cond_1
    :try_start_1
    iget p3, p0, Landroidx/media3/exoplayer/source/s;->p:I

    iget v0, p0, Landroidx/media3/exoplayer/source/s;->s:I

    sub-int v3, p3, v0

    const/4 v6, 0x1

    move-object v1, p0

    move-wide v4, p1

    invoke-virtual/range {v1 .. v6}, Landroidx/media3/exoplayer/source/s;->w(IIJZ)I

    move-result p1
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    const/4 p2, -0x1

    if-ne p1, p2, :cond_2

    monitor-exit p0

    return v7

    :cond_2
    monitor-exit p0

    return p1

    :cond_3
    :goto_0
    monitor-exit p0

    return v7

    :goto_1
    monitor-exit p0

    throw p1
.end method

.method public final declared-synchronized G()Landroidx/media3/common/y;
    .locals 1
    .annotation build Landroidx/annotation/Nullable;
    .end annotation

    monitor-enter p0

    :try_start_0
    iget-boolean v0, p0, Landroidx/media3/exoplayer/source/s;->y:Z

    if-eqz v0, :cond_0

    const/4 v0, 0x0

    goto :goto_0

    :cond_0
    iget-object v0, p0, Landroidx/media3/exoplayer/source/s;->B:Landroidx/media3/common/y;
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    :goto_0
    monitor-exit p0

    return-object v0

    :catchall_0
    move-exception v0

    monitor-exit p0

    throw v0
.end method

.method public final H()I
    .locals 2

    iget v0, p0, Landroidx/media3/exoplayer/source/s;->q:I

    iget v1, p0, Landroidx/media3/exoplayer/source/s;->p:I

    add-int/2addr v0, v1

    return v0
.end method

.method public final I()Z
    .locals 2

    iget v0, p0, Landroidx/media3/exoplayer/source/s;->s:I

    iget v1, p0, Landroidx/media3/exoplayer/source/s;->p:I

    if-eq v0, v1, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method public final J()V
    .locals 1

    const/4 v0, 0x1

    iput-boolean v0, p0, Landroidx/media3/exoplayer/source/s;->z:Z

    return-void
.end method

.method public final declared-synchronized K()Z
    .locals 1

    monitor-enter p0

    :try_start_0
    iget-boolean v0, p0, Landroidx/media3/exoplayer/source/s;->w:Z
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    monitor-exit p0

    return v0

    :catchall_0
    move-exception v0

    monitor-exit p0

    throw v0
.end method

.method public declared-synchronized L(Z)Z
    .locals 2
    .annotation build Landroidx/annotation/CallSuper;
    .end annotation

    monitor-enter p0

    :try_start_0
    invoke-virtual {p0}, Landroidx/media3/exoplayer/source/s;->I()Z

    move-result v0

    const/4 v1, 0x1

    if-nez v0, :cond_2

    if-nez p1, :cond_1

    iget-boolean p1, p0, Landroidx/media3/exoplayer/source/s;->w:Z

    if-nez p1, :cond_1

    iget-object p1, p0, Landroidx/media3/exoplayer/source/s;->B:Landroidx/media3/common/y;

    if-eqz p1, :cond_0

    iget-object v0, p0, Landroidx/media3/exoplayer/source/s;->g:Landroidx/media3/common/y;
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    if-eq p1, v0, :cond_0

    goto :goto_0

    :catchall_0
    move-exception p1

    goto :goto_1

    :cond_0
    const/4 v1, 0x0

    :cond_1
    :goto_0
    monitor-exit p0

    return v1

    :cond_2
    :try_start_1
    iget-object p1, p0, Landroidx/media3/exoplayer/source/s;->c:Lu2/h0;

    invoke-virtual {p0}, Landroidx/media3/exoplayer/source/s;->D()I

    move-result v0

    invoke-virtual {p1, v0}, Lu2/h0;->e(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Landroidx/media3/exoplayer/source/s$c;

    iget-object p1, p1, Landroidx/media3/exoplayer/source/s$c;->a:Landroidx/media3/common/y;

    iget-object v0, p0, Landroidx/media3/exoplayer/source/s;->g:Landroidx/media3/common/y;
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    if-eq p1, v0, :cond_3

    monitor-exit p0

    return v1

    :cond_3
    :try_start_2
    iget p1, p0, Landroidx/media3/exoplayer/source/s;->s:I

    invoke-virtual {p0, p1}, Landroidx/media3/exoplayer/source/s;->E(I)I

    move-result p1

    invoke-virtual {p0, p1}, Landroidx/media3/exoplayer/source/s;->N(I)Z

    move-result p1
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_0

    monitor-exit p0

    return p1

    :goto_1
    monitor-exit p0

    throw p1
.end method

.method public final N(I)Z
    .locals 2

    iget-object v0, p0, Landroidx/media3/exoplayer/source/s;->h:Landroidx/media3/exoplayer/drm/DrmSession;

    if-eqz v0, :cond_1

    invoke-interface {v0}, Landroidx/media3/exoplayer/drm/DrmSession;->getState()I

    move-result v0

    const/4 v1, 0x4

    if-eq v0, v1, :cond_1

    iget-object v0, p0, Landroidx/media3/exoplayer/source/s;->m:[I

    aget p1, v0, p1

    const/high16 v0, 0x40000000    # 2.0f

    and-int/2addr p1, v0

    if-nez p1, :cond_0

    iget-object p1, p0, Landroidx/media3/exoplayer/source/s;->h:Landroidx/media3/exoplayer/drm/DrmSession;

    invoke-interface {p1}, Landroidx/media3/exoplayer/drm/DrmSession;->b()Z

    move-result p1

    if-eqz p1, :cond_0

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 p1, 0x1

    :goto_1
    return p1
.end method

.method public O()V
    .locals 2
    .annotation build Landroidx/annotation/CallSuper;
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget-object v0, p0, Landroidx/media3/exoplayer/source/s;->h:Landroidx/media3/exoplayer/drm/DrmSession;

    if-eqz v0, :cond_1

    invoke-interface {v0}, Landroidx/media3/exoplayer/drm/DrmSession;->getState()I

    move-result v0

    const/4 v1, 0x1

    if-eq v0, v1, :cond_0

    goto :goto_0

    :cond_0
    iget-object v0, p0, Landroidx/media3/exoplayer/source/s;->h:Landroidx/media3/exoplayer/drm/DrmSession;

    invoke-interface {v0}, Landroidx/media3/exoplayer/drm/DrmSession;->getError()Landroidx/media3/exoplayer/drm/DrmSession$DrmSessionException;

    move-result-object v0

    invoke-static {v0}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroidx/media3/exoplayer/drm/DrmSession$DrmSessionException;

    throw v0

    :cond_1
    :goto_0
    return-void
.end method

.method public final P(Landroidx/media3/common/y;Landroidx/media3/exoplayer/t1;)V
    .locals 4

    iget-object v0, p0, Landroidx/media3/exoplayer/source/s;->g:Landroidx/media3/common/y;

    if-nez v0, :cond_0

    const/4 v1, 0x1

    goto :goto_0

    :cond_0
    const/4 v1, 0x0

    :goto_0
    if-nez v0, :cond_1

    const/4 v0, 0x0

    goto :goto_1

    :cond_1
    iget-object v0, v0, Landroidx/media3/common/y;->p:Landroidx/media3/common/DrmInitData;

    :goto_1
    iput-object p1, p0, Landroidx/media3/exoplayer/source/s;->g:Landroidx/media3/common/y;

    iget-object v2, p1, Landroidx/media3/common/y;->p:Landroidx/media3/common/DrmInitData;

    iget-object v3, p0, Landroidx/media3/exoplayer/source/s;->d:Landroidx/media3/exoplayer/drm/c;

    if-eqz v3, :cond_2

    invoke-interface {v3, p1}, Landroidx/media3/exoplayer/drm/c;->c(Landroidx/media3/common/y;)I

    move-result v3

    invoke-virtual {p1, v3}, Landroidx/media3/common/y;->c(I)Landroidx/media3/common/y;

    move-result-object v3

    goto :goto_2

    :cond_2
    move-object v3, p1

    :goto_2
    iput-object v3, p2, Landroidx/media3/exoplayer/t1;->b:Landroidx/media3/common/y;

    iget-object v3, p0, Landroidx/media3/exoplayer/source/s;->h:Landroidx/media3/exoplayer/drm/DrmSession;

    iput-object v3, p2, Landroidx/media3/exoplayer/t1;->a:Landroidx/media3/exoplayer/drm/DrmSession;

    iget-object v3, p0, Landroidx/media3/exoplayer/source/s;->d:Landroidx/media3/exoplayer/drm/c;

    if-nez v3, :cond_3

    return-void

    :cond_3
    if-nez v1, :cond_4

    invoke-static {v0, v2}, Le2/u0;->c(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_4

    return-void

    :cond_4
    iget-object v0, p0, Landroidx/media3/exoplayer/source/s;->h:Landroidx/media3/exoplayer/drm/DrmSession;

    iget-object v1, p0, Landroidx/media3/exoplayer/source/s;->d:Landroidx/media3/exoplayer/drm/c;

    iget-object v2, p0, Landroidx/media3/exoplayer/source/s;->e:Landroidx/media3/exoplayer/drm/b$a;

    invoke-interface {v1, v2, p1}, Landroidx/media3/exoplayer/drm/c;->b(Landroidx/media3/exoplayer/drm/b$a;Landroidx/media3/common/y;)Landroidx/media3/exoplayer/drm/DrmSession;

    move-result-object p1

    iput-object p1, p0, Landroidx/media3/exoplayer/source/s;->h:Landroidx/media3/exoplayer/drm/DrmSession;

    iput-object p1, p2, Landroidx/media3/exoplayer/t1;->a:Landroidx/media3/exoplayer/drm/DrmSession;

    if-eqz v0, :cond_5

    iget-object p1, p0, Landroidx/media3/exoplayer/source/s;->e:Landroidx/media3/exoplayer/drm/b$a;

    invoke-interface {v0, p1}, Landroidx/media3/exoplayer/drm/DrmSession;->f(Landroidx/media3/exoplayer/drm/b$a;)V

    :cond_5
    return-void
.end method

.method public final declared-synchronized Q(Landroidx/media3/exoplayer/t1;Landroidx/media3/decoder/DecoderInputBuffer;ZZLandroidx/media3/exoplayer/source/s$b;)I
    .locals 5

    monitor-enter p0

    const/4 v0, 0x0

    :try_start_0
    iput-boolean v0, p2, Landroidx/media3/decoder/DecoderInputBuffer;->d:Z

    invoke-virtual {p0}, Landroidx/media3/exoplayer/source/s;->I()Z

    move-result v0

    const/4 v1, -0x4

    const/4 v2, -0x3

    const/4 v3, -0x5

    if-nez v0, :cond_4

    if-nez p4, :cond_3

    iget-boolean p4, p0, Landroidx/media3/exoplayer/source/s;->w:Z

    if-eqz p4, :cond_0

    goto :goto_1

    :cond_0
    iget-object p2, p0, Landroidx/media3/exoplayer/source/s;->B:Landroidx/media3/common/y;

    if-eqz p2, :cond_2

    if-nez p3, :cond_1

    iget-object p3, p0, Landroidx/media3/exoplayer/source/s;->g:Landroidx/media3/common/y;

    if-eq p2, p3, :cond_2

    goto :goto_0

    :catchall_0
    move-exception p1

    goto/16 :goto_3

    :cond_1
    :goto_0
    invoke-static {p2}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p2

    check-cast p2, Landroidx/media3/common/y;

    invoke-virtual {p0, p2, p1}, Landroidx/media3/exoplayer/source/s;->P(Landroidx/media3/common/y;Landroidx/media3/exoplayer/t1;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    monitor-exit p0

    return v3

    :cond_2
    monitor-exit p0

    return v2

    :cond_3
    :goto_1
    const/4 p1, 0x4

    :try_start_1
    invoke-virtual {p2, p1}, Landroidx/media3/decoder/a;->setFlags(I)V

    const-wide/high16 p3, -0x8000000000000000L

    iput-wide p3, p2, Landroidx/media3/decoder/DecoderInputBuffer;->e:J
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    monitor-exit p0

    return v1

    :cond_4
    :try_start_2
    iget-object v0, p0, Landroidx/media3/exoplayer/source/s;->c:Lu2/h0;

    invoke-virtual {p0}, Landroidx/media3/exoplayer/source/s;->D()I

    move-result v4

    invoke-virtual {v0, v4}, Lu2/h0;->e(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroidx/media3/exoplayer/source/s$c;

    iget-object v0, v0, Landroidx/media3/exoplayer/source/s$c;->a:Landroidx/media3/common/y;

    if-nez p3, :cond_a

    iget-object p3, p0, Landroidx/media3/exoplayer/source/s;->g:Landroidx/media3/common/y;

    if-eq v0, p3, :cond_5

    goto :goto_2

    :cond_5
    iget p1, p0, Landroidx/media3/exoplayer/source/s;->s:I

    invoke-virtual {p0, p1}, Landroidx/media3/exoplayer/source/s;->E(I)I

    move-result p1

    invoke-virtual {p0, p1}, Landroidx/media3/exoplayer/source/s;->N(I)Z

    move-result p3

    const/4 v0, 0x1

    if-nez p3, :cond_6

    iput-boolean v0, p2, Landroidx/media3/decoder/DecoderInputBuffer;->d:Z
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_0

    monitor-exit p0

    return v2

    :cond_6
    :try_start_3
    iget-object p3, p0, Landroidx/media3/exoplayer/source/s;->m:[I

    aget p3, p3, p1

    invoke-virtual {p2, p3}, Landroidx/media3/decoder/a;->setFlags(I)V

    iget p3, p0, Landroidx/media3/exoplayer/source/s;->s:I

    iget v2, p0, Landroidx/media3/exoplayer/source/s;->p:I

    sub-int/2addr v2, v0

    if-ne p3, v2, :cond_8

    if-nez p4, :cond_7

    iget-boolean p3, p0, Landroidx/media3/exoplayer/source/s;->w:Z

    if-eqz p3, :cond_8

    :cond_7
    const/high16 p3, 0x20000000

    invoke-virtual {p2, p3}, Landroidx/media3/decoder/a;->addFlag(I)V

    :cond_8
    iget-object p3, p0, Landroidx/media3/exoplayer/source/s;->n:[J

    aget-wide v2, p3, p1

    iput-wide v2, p2, Landroidx/media3/decoder/DecoderInputBuffer;->e:J

    iget-wide p3, p0, Landroidx/media3/exoplayer/source/s;->t:J

    cmp-long v0, v2, p3

    if-gez v0, :cond_9

    const/high16 p3, -0x80000000

    invoke-virtual {p2, p3}, Landroidx/media3/decoder/a;->addFlag(I)V

    :cond_9
    iget-object p2, p0, Landroidx/media3/exoplayer/source/s;->l:[I

    aget p2, p2, p1

    iput p2, p5, Landroidx/media3/exoplayer/source/s$b;->a:I

    iget-object p2, p0, Landroidx/media3/exoplayer/source/s;->k:[J

    aget-wide p3, p2, p1

    iput-wide p3, p5, Landroidx/media3/exoplayer/source/s$b;->b:J

    iget-object p2, p0, Landroidx/media3/exoplayer/source/s;->o:[Lz2/r0$a;

    aget-object p1, p2, p1

    iput-object p1, p5, Landroidx/media3/exoplayer/source/s$b;->c:Lz2/r0$a;
    :try_end_3
    .catchall {:try_start_3 .. :try_end_3} :catchall_0

    monitor-exit p0

    return v1

    :cond_a
    :goto_2
    :try_start_4
    invoke-virtual {p0, v0, p1}, Landroidx/media3/exoplayer/source/s;->P(Landroidx/media3/common/y;Landroidx/media3/exoplayer/t1;)V
    :try_end_4
    .catchall {:try_start_4 .. :try_end_4} :catchall_0

    monitor-exit p0

    return v3

    :goto_3
    monitor-exit p0

    throw p1
.end method

.method public final declared-synchronized R()J
    .locals 2

    monitor-enter p0

    :try_start_0
    iget v0, p0, Landroidx/media3/exoplayer/source/s;->s:I

    invoke-virtual {p0, v0}, Landroidx/media3/exoplayer/source/s;->E(I)I

    move-result v0

    invoke-virtual {p0}, Landroidx/media3/exoplayer/source/s;->I()Z

    move-result v1

    if-eqz v1, :cond_0

    iget-object v1, p0, Landroidx/media3/exoplayer/source/s;->j:[J

    aget-wide v0, v1, v0

    goto :goto_0

    :catchall_0
    move-exception v0

    goto :goto_1

    :cond_0
    iget-wide v0, p0, Landroidx/media3/exoplayer/source/s;->C:J
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    :goto_0
    monitor-exit p0

    return-wide v0

    :goto_1
    monitor-exit p0

    throw v0
.end method

.method public S()V
    .locals 0
    .annotation build Landroidx/annotation/CallSuper;
    .end annotation

    invoke-virtual {p0}, Landroidx/media3/exoplayer/source/s;->r()V

    invoke-virtual {p0}, Landroidx/media3/exoplayer/source/s;->V()V

    return-void
.end method

.method public T(Landroidx/media3/exoplayer/t1;Landroidx/media3/decoder/DecoderInputBuffer;IZ)I
    .locals 9
    .annotation build Landroidx/annotation/CallSuper;
    .end annotation

    and-int/lit8 v0, p3, 0x2

    const/4 v1, 0x0

    const/4 v2, 0x1

    if-eqz v0, :cond_0

    const/4 v6, 0x1

    goto :goto_0

    :cond_0
    const/4 v6, 0x0

    :goto_0
    iget-object v8, p0, Landroidx/media3/exoplayer/source/s;->b:Landroidx/media3/exoplayer/source/s$b;

    move-object v3, p0

    move-object v4, p1

    move-object v5, p2

    move v7, p4

    invoke-virtual/range {v3 .. v8}, Landroidx/media3/exoplayer/source/s;->Q(Landroidx/media3/exoplayer/t1;Landroidx/media3/decoder/DecoderInputBuffer;ZZLandroidx/media3/exoplayer/source/s$b;)I

    move-result p1

    const/4 p4, -0x4

    if-ne p1, p4, :cond_4

    invoke-virtual {p2}, Landroidx/media3/decoder/a;->isEndOfStream()Z

    move-result p4

    if-nez p4, :cond_4

    and-int/lit8 p4, p3, 0x1

    if-eqz p4, :cond_1

    const/4 v1, 0x1

    :cond_1
    and-int/lit8 p3, p3, 0x4

    if-nez p3, :cond_3

    if-eqz v1, :cond_2

    iget-object p3, p0, Landroidx/media3/exoplayer/source/s;->a:Landroidx/media3/exoplayer/source/r;

    iget-object p4, p0, Landroidx/media3/exoplayer/source/s;->b:Landroidx/media3/exoplayer/source/s$b;

    invoke-virtual {p3, p2, p4}, Landroidx/media3/exoplayer/source/r;->f(Landroidx/media3/decoder/DecoderInputBuffer;Landroidx/media3/exoplayer/source/s$b;)V

    goto :goto_1

    :cond_2
    iget-object p3, p0, Landroidx/media3/exoplayer/source/s;->a:Landroidx/media3/exoplayer/source/r;

    iget-object p4, p0, Landroidx/media3/exoplayer/source/s;->b:Landroidx/media3/exoplayer/source/s$b;

    invoke-virtual {p3, p2, p4}, Landroidx/media3/exoplayer/source/r;->m(Landroidx/media3/decoder/DecoderInputBuffer;Landroidx/media3/exoplayer/source/s$b;)V

    :cond_3
    :goto_1
    if-nez v1, :cond_4

    iget p2, p0, Landroidx/media3/exoplayer/source/s;->s:I

    add-int/2addr p2, v2

    iput p2, p0, Landroidx/media3/exoplayer/source/s;->s:I

    :cond_4
    return p1
.end method

.method public U()V
    .locals 1
    .annotation build Landroidx/annotation/CallSuper;
    .end annotation

    const/4 v0, 0x1

    invoke-virtual {p0, v0}, Landroidx/media3/exoplayer/source/s;->X(Z)V

    invoke-virtual {p0}, Landroidx/media3/exoplayer/source/s;->V()V

    return-void
.end method

.method public final V()V
    .locals 2

    iget-object v0, p0, Landroidx/media3/exoplayer/source/s;->h:Landroidx/media3/exoplayer/drm/DrmSession;

    if-eqz v0, :cond_0

    iget-object v1, p0, Landroidx/media3/exoplayer/source/s;->e:Landroidx/media3/exoplayer/drm/b$a;

    invoke-interface {v0, v1}, Landroidx/media3/exoplayer/drm/DrmSession;->f(Landroidx/media3/exoplayer/drm/b$a;)V

    const/4 v0, 0x0

    iput-object v0, p0, Landroidx/media3/exoplayer/source/s;->h:Landroidx/media3/exoplayer/drm/DrmSession;

    iput-object v0, p0, Landroidx/media3/exoplayer/source/s;->g:Landroidx/media3/common/y;

    :cond_0
    return-void
.end method

.method public final W()V
    .locals 1

    const/4 v0, 0x0

    invoke-virtual {p0, v0}, Landroidx/media3/exoplayer/source/s;->X(Z)V

    return-void
.end method

.method public X(Z)V
    .locals 4
    .annotation build Landroidx/annotation/CallSuper;
    .end annotation

    iget-object v0, p0, Landroidx/media3/exoplayer/source/s;->a:Landroidx/media3/exoplayer/source/r;

    invoke-virtual {v0}, Landroidx/media3/exoplayer/source/r;->n()V

    const/4 v0, 0x0

    iput v0, p0, Landroidx/media3/exoplayer/source/s;->p:I

    iput v0, p0, Landroidx/media3/exoplayer/source/s;->q:I

    iput v0, p0, Landroidx/media3/exoplayer/source/s;->r:I

    iput v0, p0, Landroidx/media3/exoplayer/source/s;->s:I

    const/4 v1, 0x1

    iput-boolean v1, p0, Landroidx/media3/exoplayer/source/s;->x:Z

    const-wide/high16 v2, -0x8000000000000000L

    iput-wide v2, p0, Landroidx/media3/exoplayer/source/s;->t:J

    iput-wide v2, p0, Landroidx/media3/exoplayer/source/s;->u:J

    iput-wide v2, p0, Landroidx/media3/exoplayer/source/s;->v:J

    iput-boolean v0, p0, Landroidx/media3/exoplayer/source/s;->w:Z

    iget-object v0, p0, Landroidx/media3/exoplayer/source/s;->c:Lu2/h0;

    invoke-virtual {v0}, Lu2/h0;->b()V

    if-eqz p1, :cond_0

    const/4 p1, 0x0

    iput-object p1, p0, Landroidx/media3/exoplayer/source/s;->A:Landroidx/media3/common/y;

    iput-object p1, p0, Landroidx/media3/exoplayer/source/s;->B:Landroidx/media3/common/y;

    iput-boolean v1, p0, Landroidx/media3/exoplayer/source/s;->y:Z

    iput-boolean v1, p0, Landroidx/media3/exoplayer/source/s;->D:Z

    :cond_0
    return-void
.end method

.method public final declared-synchronized Y()V
    .locals 1

    monitor-enter p0

    const/4 v0, 0x0

    :try_start_0
    iput v0, p0, Landroidx/media3/exoplayer/source/s;->s:I

    iget-object v0, p0, Landroidx/media3/exoplayer/source/s;->a:Landroidx/media3/exoplayer/source/r;

    invoke-virtual {v0}, Landroidx/media3/exoplayer/source/r;->o()V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    monitor-exit p0

    return-void

    :catchall_0
    move-exception v0

    monitor-exit p0

    throw v0
.end method

.method public final declared-synchronized Z(I)Z
    .locals 3

    monitor-enter p0

    :try_start_0
    invoke-virtual {p0}, Landroidx/media3/exoplayer/source/s;->Y()V

    iget v0, p0, Landroidx/media3/exoplayer/source/s;->q:I

    if-lt p1, v0, :cond_1

    iget v1, p0, Landroidx/media3/exoplayer/source/s;->p:I

    add-int/2addr v1, v0

    if-le p1, v1, :cond_0

    goto :goto_0

    :cond_0
    const-wide/high16 v1, -0x8000000000000000L

    iput-wide v1, p0, Landroidx/media3/exoplayer/source/s;->t:J

    sub-int/2addr p1, v0

    iput p1, p0, Landroidx/media3/exoplayer/source/s;->s:I
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    monitor-exit p0

    const/4 p1, 0x1

    return p1

    :catchall_0
    move-exception p1

    goto :goto_1

    :cond_1
    :goto_0
    monitor-exit p0

    const/4 p1, 0x0

    return p1

    :goto_1
    monitor-exit p0

    throw p1
.end method

.method public final a(Landroidx/media3/common/y;)V
    .locals 2

    invoke-virtual {p0, p1}, Landroidx/media3/exoplayer/source/s;->x(Landroidx/media3/common/y;)Landroidx/media3/common/y;

    move-result-object v0

    const/4 v1, 0x0

    iput-boolean v1, p0, Landroidx/media3/exoplayer/source/s;->z:Z

    iput-object p1, p0, Landroidx/media3/exoplayer/source/s;->A:Landroidx/media3/common/y;

    invoke-virtual {p0, v0}, Landroidx/media3/exoplayer/source/s;->d0(Landroidx/media3/common/y;)Z

    move-result p1

    iget-object v1, p0, Landroidx/media3/exoplayer/source/s;->f:Landroidx/media3/exoplayer/source/s$d;

    if-eqz v1, :cond_0

    if-eqz p1, :cond_0

    invoke-interface {v1, v0}, Landroidx/media3/exoplayer/source/s$d;->c(Landroidx/media3/common/y;)V

    :cond_0
    return-void
.end method

.method public final declared-synchronized a0(JZ)Z
    .locals 8

    monitor-enter p0

    :try_start_0
    invoke-virtual {p0}, Landroidx/media3/exoplayer/source/s;->Y()V

    iget v0, p0, Landroidx/media3/exoplayer/source/s;->s:I

    invoke-virtual {p0, v0}, Landroidx/media3/exoplayer/source/s;->E(I)I

    move-result v2

    invoke-virtual {p0}, Landroidx/media3/exoplayer/source/s;->I()Z

    move-result v0

    const/4 v7, 0x0

    if-eqz v0, :cond_3

    iget-object v0, p0, Landroidx/media3/exoplayer/source/s;->n:[J

    aget-wide v3, v0, v2

    cmp-long v0, p1, v3

    if-ltz v0, :cond_3

    iget-wide v0, p0, Landroidx/media3/exoplayer/source/s;->v:J

    cmp-long v3, p1, v0

    if-lez v3, :cond_0

    if-nez p3, :cond_0

    goto :goto_1

    :cond_0
    iget-boolean v0, p0, Landroidx/media3/exoplayer/source/s;->D:Z

    if-eqz v0, :cond_1

    iget v0, p0, Landroidx/media3/exoplayer/source/s;->p:I

    iget v1, p0, Landroidx/media3/exoplayer/source/s;->s:I

    sub-int v3, v0, v1

    move-object v1, p0

    move-wide v4, p1

    move v6, p3

    invoke-virtual/range {v1 .. v6}, Landroidx/media3/exoplayer/source/s;->v(IIJZ)I

    move-result p3

    goto :goto_0

    :catchall_0
    move-exception p1

    goto :goto_2

    :cond_1
    iget p3, p0, Landroidx/media3/exoplayer/source/s;->p:I

    iget v0, p0, Landroidx/media3/exoplayer/source/s;->s:I

    sub-int v3, p3, v0

    const/4 v6, 0x1

    move-object v1, p0

    move-wide v4, p1

    invoke-virtual/range {v1 .. v6}, Landroidx/media3/exoplayer/source/s;->w(IIJZ)I

    move-result p3
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    :goto_0
    const/4 v0, -0x1

    if-ne p3, v0, :cond_2

    monitor-exit p0

    return v7

    :cond_2
    :try_start_1
    iput-wide p1, p0, Landroidx/media3/exoplayer/source/s;->t:J

    iget p1, p0, Landroidx/media3/exoplayer/source/s;->s:I

    add-int/2addr p1, p3

    iput p1, p0, Landroidx/media3/exoplayer/source/s;->s:I
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    monitor-exit p0

    const/4 p1, 0x1

    return p1

    :cond_3
    :goto_1
    monitor-exit p0

    return v7

    :goto_2
    monitor-exit p0

    throw p1
.end method

.method public final b(Le2/c0;II)V
    .locals 0

    iget-object p3, p0, Landroidx/media3/exoplayer/source/s;->a:Landroidx/media3/exoplayer/source/r;

    invoke-virtual {p3, p1, p2}, Landroidx/media3/exoplayer/source/r;->q(Le2/c0;I)V

    return-void
.end method

.method public final b0(J)V
    .locals 3

    iget-wide v0, p0, Landroidx/media3/exoplayer/source/s;->F:J

    cmp-long v2, v0, p1

    if-eqz v2, :cond_0

    iput-wide p1, p0, Landroidx/media3/exoplayer/source/s;->F:J

    invoke-virtual {p0}, Landroidx/media3/exoplayer/source/s;->J()V

    :cond_0
    return-void
.end method

.method public synthetic c(Landroidx/media3/common/l;IZ)I
    .locals 0

    invoke-static {p0, p1, p2, p3}, Lz2/q0;->a(Lz2/r0;Landroidx/media3/common/l;IZ)I

    move-result p1

    return p1
.end method

.method public final c0(J)V
    .locals 0

    iput-wide p1, p0, Landroidx/media3/exoplayer/source/s;->t:J

    return-void
.end method

.method public final d(Landroidx/media3/common/l;IZI)I
    .locals 0
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget-object p4, p0, Landroidx/media3/exoplayer/source/s;->a:Landroidx/media3/exoplayer/source/r;

    invoke-virtual {p4, p1, p2, p3}, Landroidx/media3/exoplayer/source/r;->p(Landroidx/media3/common/l;IZ)I

    move-result p1

    return p1
.end method

.method public final declared-synchronized d0(Landroidx/media3/common/y;)Z
    .locals 3

    monitor-enter p0

    const/4 v0, 0x0

    :try_start_0
    iput-boolean v0, p0, Landroidx/media3/exoplayer/source/s;->y:Z

    iget-object v1, p0, Landroidx/media3/exoplayer/source/s;->B:Landroidx/media3/common/y;

    invoke-static {p1, v1}, Le2/u0;->c(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    if-eqz v1, :cond_0

    monitor-exit p0

    return v0

    :cond_0
    :try_start_1
    iget-object v1, p0, Landroidx/media3/exoplayer/source/s;->c:Lu2/h0;

    invoke-virtual {v1}, Lu2/h0;->g()Z

    move-result v1

    if-nez v1, :cond_1

    iget-object v1, p0, Landroidx/media3/exoplayer/source/s;->c:Lu2/h0;

    invoke-virtual {v1}, Lu2/h0;->f()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Landroidx/media3/exoplayer/source/s$c;

    iget-object v1, v1, Landroidx/media3/exoplayer/source/s$c;->a:Landroidx/media3/common/y;

    invoke-virtual {v1, p1}, Landroidx/media3/common/y;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_1

    iget-object p1, p0, Landroidx/media3/exoplayer/source/s;->c:Lu2/h0;

    invoke-virtual {p1}, Lu2/h0;->f()Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Landroidx/media3/exoplayer/source/s$c;

    iget-object p1, p1, Landroidx/media3/exoplayer/source/s$c;->a:Landroidx/media3/common/y;

    iput-object p1, p0, Landroidx/media3/exoplayer/source/s;->B:Landroidx/media3/common/y;

    goto :goto_0

    :catchall_0
    move-exception p1

    goto :goto_1

    :cond_1
    iput-object p1, p0, Landroidx/media3/exoplayer/source/s;->B:Landroidx/media3/common/y;

    :goto_0
    iget-boolean p1, p0, Landroidx/media3/exoplayer/source/s;->D:Z

    iget-object v1, p0, Landroidx/media3/exoplayer/source/s;->B:Landroidx/media3/common/y;

    iget-object v2, v1, Landroidx/media3/common/y;->m:Ljava/lang/String;

    iget-object v1, v1, Landroidx/media3/common/y;->j:Ljava/lang/String;

    invoke-static {v2, v1}, Landroidx/media3/common/f0;->a(Ljava/lang/String;Ljava/lang/String;)Z

    move-result v1

    and-int/2addr p1, v1

    iput-boolean p1, p0, Landroidx/media3/exoplayer/source/s;->D:Z

    iput-boolean v0, p0, Landroidx/media3/exoplayer/source/s;->E:Z
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    monitor-exit p0

    const/4 p1, 0x1

    return p1

    :goto_1
    monitor-exit p0

    throw p1
.end method

.method public e(JIIILz2/r0$a;)V
    .locals 11
    .param p6    # Lz2/r0$a;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    move-object v8, p0

    iget-boolean v0, v8, Landroidx/media3/exoplayer/source/s;->z:Z

    if-eqz v0, :cond_0

    iget-object v0, v8, Landroidx/media3/exoplayer/source/s;->A:Landroidx/media3/common/y;

    invoke-static {v0}, Le2/a;->i(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroidx/media3/common/y;

    invoke-virtual {p0, v0}, Landroidx/media3/exoplayer/source/s;->a(Landroidx/media3/common/y;)V

    :cond_0
    and-int/lit8 v0, p3, 0x1

    const/4 v1, 0x0

    const/4 v2, 0x1

    if-eqz v0, :cond_1

    const/4 v3, 0x1

    goto :goto_0

    :cond_1
    const/4 v3, 0x0

    :goto_0
    iget-boolean v4, v8, Landroidx/media3/exoplayer/source/s;->x:Z

    if-eqz v4, :cond_3

    if-nez v3, :cond_2

    return-void

    :cond_2
    iput-boolean v1, v8, Landroidx/media3/exoplayer/source/s;->x:Z

    :cond_3
    iget-wide v4, v8, Landroidx/media3/exoplayer/source/s;->F:J

    add-long/2addr v4, p1

    iget-boolean v6, v8, Landroidx/media3/exoplayer/source/s;->D:Z

    if-eqz v6, :cond_6

    iget-wide v6, v8, Landroidx/media3/exoplayer/source/s;->t:J

    cmp-long v9, v4, v6

    if-gez v9, :cond_4

    return-void

    :cond_4
    if-nez v0, :cond_6

    iget-boolean v0, v8, Landroidx/media3/exoplayer/source/s;->E:Z

    if-nez v0, :cond_5

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v6, "Overriding unexpected non-sync sample for format: "

    invoke-virtual {v0, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v6, v8, Landroidx/media3/exoplayer/source/s;->B:Landroidx/media3/common/y;

    invoke-virtual {v0, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    const-string v6, "SampleQueue"

    invoke-static {v6, v0}, Le2/o;->h(Ljava/lang/String;Ljava/lang/String;)V

    iput-boolean v2, v8, Landroidx/media3/exoplayer/source/s;->E:Z

    :cond_5
    or-int/lit8 v0, p3, 0x1

    move v6, v0

    goto :goto_1

    :cond_6
    move v6, p3

    :goto_1
    iget-boolean v0, v8, Landroidx/media3/exoplayer/source/s;->G:Z

    if-eqz v0, :cond_9

    if-eqz v3, :cond_8

    invoke-virtual {p0, v4, v5}, Landroidx/media3/exoplayer/source/s;->h(J)Z

    move-result v0

    if-nez v0, :cond_7

    goto :goto_2

    :cond_7
    iput-boolean v1, v8, Landroidx/media3/exoplayer/source/s;->G:Z

    goto :goto_3

    :cond_8
    :goto_2
    return-void

    :cond_9
    :goto_3
    iget-object v0, v8, Landroidx/media3/exoplayer/source/s;->a:Landroidx/media3/exoplayer/source/r;

    invoke-virtual {v0}, Landroidx/media3/exoplayer/source/r;->e()J

    move-result-wide v0

    move v7, p4

    int-to-long v2, v7

    sub-long/2addr v0, v2

    move/from16 v2, p5

    int-to-long v2, v2

    sub-long v9, v0, v2

    move-object v0, p0

    move-wide v1, v4

    move v3, v6

    move-wide v4, v9

    move v6, p4

    move-object/from16 v7, p6

    invoke-virtual/range {v0 .. v7}, Landroidx/media3/exoplayer/source/s;->i(JIJILz2/r0$a;)V

    return-void
.end method

.method public final e0(Landroidx/media3/exoplayer/source/s$d;)V
    .locals 0
    .param p1    # Landroidx/media3/exoplayer/source/s$d;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    iput-object p1, p0, Landroidx/media3/exoplayer/source/s;->f:Landroidx/media3/exoplayer/source/s$d;

    return-void
.end method

.method public synthetic f(Le2/c0;I)V
    .locals 0

    invoke-static {p0, p1, p2}, Lz2/q0;->b(Lz2/r0;Le2/c0;I)V

    return-void
.end method

.method public final declared-synchronized f0(I)V
    .locals 2

    monitor-enter p0

    if-ltz p1, :cond_0

    :try_start_0
    iget v0, p0, Landroidx/media3/exoplayer/source/s;->s:I

    add-int/2addr v0, p1

    iget v1, p0, Landroidx/media3/exoplayer/source/s;->p:I

    if-gt v0, v1, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :catchall_0
    move-exception p1

    goto :goto_1

    :cond_0
    const/4 v0, 0x0

    :goto_0
    invoke-static {v0}, Le2/a;->a(Z)V

    iget v0, p0, Landroidx/media3/exoplayer/source/s;->s:I

    add-int/2addr v0, p1

    iput v0, p0, Landroidx/media3/exoplayer/source/s;->s:I
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    monitor-exit p0

    return-void

    :goto_1
    monitor-exit p0

    throw p1
.end method

.method public final g0(J)V
    .locals 0

    iput-wide p1, p0, Landroidx/media3/exoplayer/source/s;->C:J

    return-void
.end method

.method public final declared-synchronized h(J)Z
    .locals 5

    monitor-enter p0

    :try_start_0
    iget v0, p0, Landroidx/media3/exoplayer/source/s;->p:I

    const/4 v1, 0x0

    const/4 v2, 0x1

    if-nez v0, :cond_1

    iget-wide v3, p0, Landroidx/media3/exoplayer/source/s;->u:J
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    cmp-long v0, p1, v3

    if-lez v0, :cond_0

    const/4 v1, 0x1

    :cond_0
    monitor-exit p0

    return v1

    :catchall_0
    move-exception p1

    goto :goto_0

    :cond_1
    :try_start_1
    invoke-virtual {p0}, Landroidx/media3/exoplayer/source/s;->B()J

    move-result-wide v3
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    cmp-long v0, v3, p1

    if-ltz v0, :cond_2

    monitor-exit p0

    return v1

    :cond_2
    :try_start_2
    invoke-virtual {p0, p1, p2}, Landroidx/media3/exoplayer/source/s;->j(J)I

    move-result p1

    iget p2, p0, Landroidx/media3/exoplayer/source/s;->q:I

    add-int/2addr p2, p1

    invoke-virtual {p0, p2}, Landroidx/media3/exoplayer/source/s;->t(I)J
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_0

    monitor-exit p0

    return v2

    :goto_0
    monitor-exit p0

    throw p1
.end method

.method public final h0()V
    .locals 1

    const/4 v0, 0x1

    iput-boolean v0, p0, Landroidx/media3/exoplayer/source/s;->G:Z

    return-void
.end method

.method public final declared-synchronized i(JIJILz2/r0$a;)V
    .locals 8
    .param p7    # Lz2/r0$a;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    monitor-enter p0

    :try_start_0
    iget v0, p0, Landroidx/media3/exoplayer/source/s;->p:I

    const/4 v1, 0x1

    const/4 v2, 0x0

    if-lez v0, :cond_1

    sub-int/2addr v0, v1

    invoke-virtual {p0, v0}, Landroidx/media3/exoplayer/source/s;->E(I)I

    move-result v0

    iget-object v3, p0, Landroidx/media3/exoplayer/source/s;->k:[J

    aget-wide v4, v3, v0

    iget-object v3, p0, Landroidx/media3/exoplayer/source/s;->l:[I

    aget v0, v3, v0

    int-to-long v6, v0

    add-long/2addr v4, v6

    cmp-long v0, v4, p4

    if-gtz v0, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    invoke-static {v0}, Le2/a;->a(Z)V

    goto :goto_1

    :catchall_0
    move-exception p1

    goto/16 :goto_4

    :cond_1
    :goto_1
    const/high16 v0, 0x20000000

    and-int/2addr v0, p3

    if-eqz v0, :cond_2

    const/4 v0, 0x1

    goto :goto_2

    :cond_2
    const/4 v0, 0x0

    :goto_2
    iput-boolean v0, p0, Landroidx/media3/exoplayer/source/s;->w:Z

    iget-wide v3, p0, Landroidx/media3/exoplayer/source/s;->v:J

    invoke-static {v3, v4, p1, p2}, Ljava/lang/Math;->max(JJ)J

    move-result-wide v3

    iput-wide v3, p0, Landroidx/media3/exoplayer/source/s;->v:J

    iget v0, p0, Landroidx/media3/exoplayer/source/s;->p:I

    invoke-virtual {p0, v0}, Landroidx/media3/exoplayer/source/s;->E(I)I

    move-result v0

    iget-object v3, p0, Landroidx/media3/exoplayer/source/s;->n:[J

    aput-wide p1, v3, v0

    iget-object p1, p0, Landroidx/media3/exoplayer/source/s;->k:[J

    aput-wide p4, p1, v0

    iget-object p1, p0, Landroidx/media3/exoplayer/source/s;->l:[I

    aput p6, p1, v0

    iget-object p1, p0, Landroidx/media3/exoplayer/source/s;->m:[I

    aput p3, p1, v0

    iget-object p1, p0, Landroidx/media3/exoplayer/source/s;->o:[Lz2/r0$a;

    aput-object p7, p1, v0

    iget-object p1, p0, Landroidx/media3/exoplayer/source/s;->j:[J

    iget-wide p2, p0, Landroidx/media3/exoplayer/source/s;->C:J

    aput-wide p2, p1, v0

    iget-object p1, p0, Landroidx/media3/exoplayer/source/s;->c:Lu2/h0;

    invoke-virtual {p1}, Lu2/h0;->g()Z

    move-result p1

    if-nez p1, :cond_3

    iget-object p1, p0, Landroidx/media3/exoplayer/source/s;->c:Lu2/h0;

    invoke-virtual {p1}, Lu2/h0;->f()Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Landroidx/media3/exoplayer/source/s$c;

    iget-object p1, p1, Landroidx/media3/exoplayer/source/s$c;->a:Landroidx/media3/common/y;

    iget-object p2, p0, Landroidx/media3/exoplayer/source/s;->B:Landroidx/media3/common/y;

    invoke-virtual {p1, p2}, Landroidx/media3/common/y;->equals(Ljava/lang/Object;)Z

    move-result p1

    if-nez p1, :cond_5

    :cond_3
    iget-object p1, p0, Landroidx/media3/exoplayer/source/s;->B:Landroidx/media3/common/y;

    invoke-static {p1}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Landroidx/media3/common/y;

    iget-object p2, p0, Landroidx/media3/exoplayer/source/s;->d:Landroidx/media3/exoplayer/drm/c;

    if-eqz p2, :cond_4

    iget-object p3, p0, Landroidx/media3/exoplayer/source/s;->e:Landroidx/media3/exoplayer/drm/b$a;

    invoke-interface {p2, p3, p1}, Landroidx/media3/exoplayer/drm/c;->d(Landroidx/media3/exoplayer/drm/b$a;Landroidx/media3/common/y;)Landroidx/media3/exoplayer/drm/c$b;

    move-result-object p2

    goto :goto_3

    :cond_4
    sget-object p2, Landroidx/media3/exoplayer/drm/c$b;->a:Landroidx/media3/exoplayer/drm/c$b;

    :goto_3
    iget-object p3, p0, Landroidx/media3/exoplayer/source/s;->c:Lu2/h0;

    invoke-virtual {p0}, Landroidx/media3/exoplayer/source/s;->H()I

    move-result p4

    new-instance p5, Landroidx/media3/exoplayer/source/s$c;

    const/4 p6, 0x0

    invoke-direct {p5, p1, p2, p6}, Landroidx/media3/exoplayer/source/s$c;-><init>(Landroidx/media3/common/y;Landroidx/media3/exoplayer/drm/c$b;Landroidx/media3/exoplayer/source/s$a;)V

    invoke-virtual {p3, p4, p5}, Lu2/h0;->a(ILjava/lang/Object;)V

    :cond_5
    iget p1, p0, Landroidx/media3/exoplayer/source/s;->p:I

    add-int/2addr p1, v1

    iput p1, p0, Landroidx/media3/exoplayer/source/s;->p:I

    iget p2, p0, Landroidx/media3/exoplayer/source/s;->i:I

    if-ne p1, p2, :cond_6

    add-int/lit16 p1, p2, 0x3e8

    new-array p3, p1, [J

    new-array p4, p1, [J

    new-array p5, p1, [J

    new-array p6, p1, [I

    new-array p7, p1, [I

    new-array v0, p1, [Lz2/r0$a;

    iget v1, p0, Landroidx/media3/exoplayer/source/s;->r:I

    sub-int/2addr p2, v1

    iget-object v3, p0, Landroidx/media3/exoplayer/source/s;->k:[J

    invoke-static {v3, v1, p4, v2, p2}, Ljava/lang/System;->arraycopy(Ljava/lang/Object;ILjava/lang/Object;II)V

    iget-object v1, p0, Landroidx/media3/exoplayer/source/s;->n:[J

    iget v3, p0, Landroidx/media3/exoplayer/source/s;->r:I

    invoke-static {v1, v3, p5, v2, p2}, Ljava/lang/System;->arraycopy(Ljava/lang/Object;ILjava/lang/Object;II)V

    iget-object v1, p0, Landroidx/media3/exoplayer/source/s;->m:[I

    iget v3, p0, Landroidx/media3/exoplayer/source/s;->r:I

    invoke-static {v1, v3, p6, v2, p2}, Ljava/lang/System;->arraycopy(Ljava/lang/Object;ILjava/lang/Object;II)V

    iget-object v1, p0, Landroidx/media3/exoplayer/source/s;->l:[I

    iget v3, p0, Landroidx/media3/exoplayer/source/s;->r:I

    invoke-static {v1, v3, p7, v2, p2}, Ljava/lang/System;->arraycopy(Ljava/lang/Object;ILjava/lang/Object;II)V

    iget-object v1, p0, Landroidx/media3/exoplayer/source/s;->o:[Lz2/r0$a;

    iget v3, p0, Landroidx/media3/exoplayer/source/s;->r:I

    invoke-static {v1, v3, v0, v2, p2}, Ljava/lang/System;->arraycopy(Ljava/lang/Object;ILjava/lang/Object;II)V

    iget-object v1, p0, Landroidx/media3/exoplayer/source/s;->j:[J

    iget v3, p0, Landroidx/media3/exoplayer/source/s;->r:I

    invoke-static {v1, v3, p3, v2, p2}, Ljava/lang/System;->arraycopy(Ljava/lang/Object;ILjava/lang/Object;II)V

    iget v1, p0, Landroidx/media3/exoplayer/source/s;->r:I

    iget-object v3, p0, Landroidx/media3/exoplayer/source/s;->k:[J

    invoke-static {v3, v2, p4, p2, v1}, Ljava/lang/System;->arraycopy(Ljava/lang/Object;ILjava/lang/Object;II)V

    iget-object v3, p0, Landroidx/media3/exoplayer/source/s;->n:[J

    invoke-static {v3, v2, p5, p2, v1}, Ljava/lang/System;->arraycopy(Ljava/lang/Object;ILjava/lang/Object;II)V

    iget-object v3, p0, Landroidx/media3/exoplayer/source/s;->m:[I

    invoke-static {v3, v2, p6, p2, v1}, Ljava/lang/System;->arraycopy(Ljava/lang/Object;ILjava/lang/Object;II)V

    iget-object v3, p0, Landroidx/media3/exoplayer/source/s;->l:[I

    invoke-static {v3, v2, p7, p2, v1}, Ljava/lang/System;->arraycopy(Ljava/lang/Object;ILjava/lang/Object;II)V

    iget-object v3, p0, Landroidx/media3/exoplayer/source/s;->o:[Lz2/r0$a;

    invoke-static {v3, v2, v0, p2, v1}, Ljava/lang/System;->arraycopy(Ljava/lang/Object;ILjava/lang/Object;II)V

    iget-object v3, p0, Landroidx/media3/exoplayer/source/s;->j:[J

    invoke-static {v3, v2, p3, p2, v1}, Ljava/lang/System;->arraycopy(Ljava/lang/Object;ILjava/lang/Object;II)V

    iput-object p4, p0, Landroidx/media3/exoplayer/source/s;->k:[J

    iput-object p5, p0, Landroidx/media3/exoplayer/source/s;->n:[J

    iput-object p6, p0, Landroidx/media3/exoplayer/source/s;->m:[I

    iput-object p7, p0, Landroidx/media3/exoplayer/source/s;->l:[I

    iput-object v0, p0, Landroidx/media3/exoplayer/source/s;->o:[Lz2/r0$a;

    iput-object p3, p0, Landroidx/media3/exoplayer/source/s;->j:[J

    iput v2, p0, Landroidx/media3/exoplayer/source/s;->r:I

    iput p1, p0, Landroidx/media3/exoplayer/source/s;->i:I
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    :cond_6
    monitor-exit p0

    return-void

    :goto_4
    monitor-exit p0

    throw p1
.end method

.method public final j(J)I
    .locals 5

    iget v0, p0, Landroidx/media3/exoplayer/source/s;->p:I

    add-int/lit8 v1, v0, -0x1

    invoke-virtual {p0, v1}, Landroidx/media3/exoplayer/source/s;->E(I)I

    move-result v1

    :cond_0
    :goto_0
    iget v2, p0, Landroidx/media3/exoplayer/source/s;->s:I

    if-le v0, v2, :cond_1

    iget-object v2, p0, Landroidx/media3/exoplayer/source/s;->n:[J

    aget-wide v3, v2, v1

    cmp-long v2, v3, p1

    if-ltz v2, :cond_1

    add-int/lit8 v0, v0, -0x1

    add-int/lit8 v1, v1, -0x1

    const/4 v2, -0x1

    if-ne v1, v2, :cond_0

    iget v1, p0, Landroidx/media3/exoplayer/source/s;->i:I

    add-int/lit8 v1, v1, -0x1

    goto :goto_0

    :cond_1
    return v0
.end method

.method public final declared-synchronized m(JZZ)J
    .locals 10

    monitor-enter p0

    :try_start_0
    iget v0, p0, Landroidx/media3/exoplayer/source/s;->p:I

    const-wide/16 v1, -0x1

    if-eqz v0, :cond_3

    iget-object v3, p0, Landroidx/media3/exoplayer/source/s;->n:[J

    iget v5, p0, Landroidx/media3/exoplayer/source/s;->r:I

    aget-wide v6, v3, v5

    cmp-long v3, p1, v6

    if-gez v3, :cond_0

    goto :goto_1

    :cond_0
    if-eqz p4, :cond_1

    iget p4, p0, Landroidx/media3/exoplayer/source/s;->s:I

    if-eq p4, v0, :cond_1

    add-int/lit8 v0, p4, 0x1

    :cond_1
    move v6, v0

    goto :goto_0

    :catchall_0
    move-exception p1

    goto :goto_2

    :goto_0
    move-object v4, p0

    move-wide v7, p1

    move v9, p3

    invoke-virtual/range {v4 .. v9}, Landroidx/media3/exoplayer/source/s;->w(IIJZ)I

    move-result p1
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    const/4 p2, -0x1

    if-ne p1, p2, :cond_2

    monitor-exit p0

    return-wide v1

    :cond_2
    :try_start_1
    invoke-virtual {p0, p1}, Landroidx/media3/exoplayer/source/s;->p(I)J

    move-result-wide p1
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    monitor-exit p0

    return-wide p1

    :cond_3
    :goto_1
    monitor-exit p0

    return-wide v1

    :goto_2
    monitor-exit p0

    throw p1
.end method

.method public final declared-synchronized n()J
    .locals 2

    monitor-enter p0

    :try_start_0
    iget v0, p0, Landroidx/media3/exoplayer/source/s;->p:I
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    if-nez v0, :cond_0

    monitor-exit p0

    const-wide/16 v0, -0x1

    return-wide v0

    :cond_0
    :try_start_1
    invoke-virtual {p0, v0}, Landroidx/media3/exoplayer/source/s;->p(I)J

    move-result-wide v0
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    monitor-exit p0

    return-wide v0

    :catchall_0
    move-exception v0

    monitor-exit p0

    throw v0
.end method

.method public declared-synchronized o()J
    .locals 2

    monitor-enter p0

    :try_start_0
    iget v0, p0, Landroidx/media3/exoplayer/source/s;->s:I
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    if-nez v0, :cond_0

    monitor-exit p0

    const-wide/16 v0, -0x1

    return-wide v0

    :cond_0
    :try_start_1
    invoke-virtual {p0, v0}, Landroidx/media3/exoplayer/source/s;->p(I)J

    move-result-wide v0
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    monitor-exit p0

    return-wide v0

    :catchall_0
    move-exception v0

    monitor-exit p0

    throw v0
.end method

.method public final p(I)J
    .locals 5

    iget-wide v0, p0, Landroidx/media3/exoplayer/source/s;->u:J

    invoke-virtual {p0, p1}, Landroidx/media3/exoplayer/source/s;->C(I)J

    move-result-wide v2

    invoke-static {v0, v1, v2, v3}, Ljava/lang/Math;->max(JJ)J

    move-result-wide v0

    iput-wide v0, p0, Landroidx/media3/exoplayer/source/s;->u:J

    iget v0, p0, Landroidx/media3/exoplayer/source/s;->p:I

    sub-int/2addr v0, p1

    iput v0, p0, Landroidx/media3/exoplayer/source/s;->p:I

    iget v0, p0, Landroidx/media3/exoplayer/source/s;->q:I

    add-int/2addr v0, p1

    iput v0, p0, Landroidx/media3/exoplayer/source/s;->q:I

    iget v1, p0, Landroidx/media3/exoplayer/source/s;->r:I

    add-int/2addr v1, p1

    iput v1, p0, Landroidx/media3/exoplayer/source/s;->r:I

    iget v2, p0, Landroidx/media3/exoplayer/source/s;->i:I

    if-lt v1, v2, :cond_0

    sub-int/2addr v1, v2

    iput v1, p0, Landroidx/media3/exoplayer/source/s;->r:I

    :cond_0
    iget v1, p0, Landroidx/media3/exoplayer/source/s;->s:I

    sub-int/2addr v1, p1

    iput v1, p0, Landroidx/media3/exoplayer/source/s;->s:I

    if-gez v1, :cond_1

    const/4 p1, 0x0

    iput p1, p0, Landroidx/media3/exoplayer/source/s;->s:I

    :cond_1
    iget-object p1, p0, Landroidx/media3/exoplayer/source/s;->c:Lu2/h0;

    invoke-virtual {p1, v0}, Lu2/h0;->d(I)V

    iget p1, p0, Landroidx/media3/exoplayer/source/s;->p:I

    if-nez p1, :cond_3

    iget p1, p0, Landroidx/media3/exoplayer/source/s;->r:I

    if-nez p1, :cond_2

    iget p1, p0, Landroidx/media3/exoplayer/source/s;->i:I

    :cond_2
    add-int/lit8 p1, p1, -0x1

    iget-object v0, p0, Landroidx/media3/exoplayer/source/s;->k:[J

    aget-wide v1, v0, p1

    iget-object v0, p0, Landroidx/media3/exoplayer/source/s;->l:[I

    aget p1, v0, p1

    int-to-long v3, p1

    add-long/2addr v1, v3

    return-wide v1

    :cond_3
    iget-object p1, p0, Landroidx/media3/exoplayer/source/s;->k:[J

    iget v0, p0, Landroidx/media3/exoplayer/source/s;->r:I

    aget-wide v0, p1, v0

    return-wide v0
.end method

.method public final q(JZZ)V
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/source/s;->a:Landroidx/media3/exoplayer/source/r;

    invoke-virtual {p0, p1, p2, p3, p4}, Landroidx/media3/exoplayer/source/s;->m(JZZ)J

    move-result-wide p1

    invoke-virtual {v0, p1, p2}, Landroidx/media3/exoplayer/source/r;->b(J)V

    return-void
.end method

.method public final r()V
    .locals 3

    iget-object v0, p0, Landroidx/media3/exoplayer/source/s;->a:Landroidx/media3/exoplayer/source/r;

    invoke-virtual {p0}, Landroidx/media3/exoplayer/source/s;->n()J

    move-result-wide v1

    invoke-virtual {v0, v1, v2}, Landroidx/media3/exoplayer/source/r;->b(J)V

    return-void
.end method

.method public final s()V
    .locals 3

    iget-object v0, p0, Landroidx/media3/exoplayer/source/s;->a:Landroidx/media3/exoplayer/source/r;

    invoke-virtual {p0}, Landroidx/media3/exoplayer/source/s;->o()J

    move-result-wide v1

    invoke-virtual {v0, v1, v2}, Landroidx/media3/exoplayer/source/r;->b(J)V

    return-void
.end method

.method public final t(I)J
    .locals 8

    invoke-virtual {p0}, Landroidx/media3/exoplayer/source/s;->H()I

    move-result v0

    sub-int/2addr v0, p1

    const/4 v1, 0x0

    const/4 v2, 0x1

    if-ltz v0, :cond_0

    iget v3, p0, Landroidx/media3/exoplayer/source/s;->p:I

    iget v4, p0, Landroidx/media3/exoplayer/source/s;->s:I

    sub-int/2addr v3, v4

    if-gt v0, v3, :cond_0

    const/4 v3, 0x1

    goto :goto_0

    :cond_0
    const/4 v3, 0x0

    :goto_0
    invoke-static {v3}, Le2/a;->a(Z)V

    iget v3, p0, Landroidx/media3/exoplayer/source/s;->p:I

    sub-int/2addr v3, v0

    iput v3, p0, Landroidx/media3/exoplayer/source/s;->p:I

    iget-wide v4, p0, Landroidx/media3/exoplayer/source/s;->u:J

    invoke-virtual {p0, v3}, Landroidx/media3/exoplayer/source/s;->C(I)J

    move-result-wide v6

    invoke-static {v4, v5, v6, v7}, Ljava/lang/Math;->max(JJ)J

    move-result-wide v3

    iput-wide v3, p0, Landroidx/media3/exoplayer/source/s;->v:J

    if-nez v0, :cond_1

    iget-boolean v0, p0, Landroidx/media3/exoplayer/source/s;->w:Z

    if-eqz v0, :cond_1

    const/4 v1, 0x1

    :cond_1
    iput-boolean v1, p0, Landroidx/media3/exoplayer/source/s;->w:Z

    iget-object v0, p0, Landroidx/media3/exoplayer/source/s;->c:Lu2/h0;

    invoke-virtual {v0, p1}, Lu2/h0;->c(I)V

    iget p1, p0, Landroidx/media3/exoplayer/source/s;->p:I

    if-eqz p1, :cond_2

    sub-int/2addr p1, v2

    invoke-virtual {p0, p1}, Landroidx/media3/exoplayer/source/s;->E(I)I

    move-result p1

    iget-object v0, p0, Landroidx/media3/exoplayer/source/s;->k:[J

    aget-wide v1, v0, p1

    iget-object v0, p0, Landroidx/media3/exoplayer/source/s;->l:[I

    aget p1, v0, p1

    int-to-long v3, p1

    add-long/2addr v1, v3

    return-wide v1

    :cond_2
    const-wide/16 v0, 0x0

    return-wide v0
.end method

.method public final u(I)V
    .locals 3

    iget-object v0, p0, Landroidx/media3/exoplayer/source/s;->a:Landroidx/media3/exoplayer/source/r;

    invoke-virtual {p0, p1}, Landroidx/media3/exoplayer/source/s;->t(I)J

    move-result-wide v1

    invoke-virtual {v0, v1, v2}, Landroidx/media3/exoplayer/source/r;->c(J)V

    return-void
.end method

.method public final v(IIJZ)I
    .locals 5

    const/4 v0, 0x0

    const/4 v1, 0x0

    :goto_0
    if-ge v1, p2, :cond_2

    iget-object v2, p0, Landroidx/media3/exoplayer/source/s;->n:[J

    aget-wide v3, v2, p1

    cmp-long v2, v3, p3

    if-ltz v2, :cond_0

    return v1

    :cond_0
    add-int/lit8 p1, p1, 0x1

    iget v2, p0, Landroidx/media3/exoplayer/source/s;->i:I

    if-ne p1, v2, :cond_1

    const/4 p1, 0x0

    :cond_1
    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_2
    if-eqz p5, :cond_3

    goto :goto_1

    :cond_3
    const/4 p2, -0x1

    :goto_1
    return p2
.end method

.method public final w(IIJZ)I
    .locals 6

    const/4 v0, -0x1

    const/4 v1, 0x0

    const/4 v2, 0x0

    :goto_0
    if-ge v2, p2, :cond_4

    iget-object v3, p0, Landroidx/media3/exoplayer/source/s;->n:[J

    aget-wide v4, v3, p1

    cmp-long v3, v4, p3

    if-gtz v3, :cond_4

    if-eqz p5, :cond_0

    iget-object v3, p0, Landroidx/media3/exoplayer/source/s;->m:[I

    aget v3, v3, p1

    and-int/lit8 v3, v3, 0x1

    if-eqz v3, :cond_2

    :cond_0
    cmp-long v0, v4, p3

    if-nez v0, :cond_1

    move v0, v2

    goto :goto_1

    :cond_1
    move v0, v2

    :cond_2
    add-int/lit8 p1, p1, 0x1

    iget v3, p0, Landroidx/media3/exoplayer/source/s;->i:I

    if-ne p1, v3, :cond_3

    const/4 p1, 0x0

    :cond_3
    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_4
    :goto_1
    return v0
.end method

.method public x(Landroidx/media3/common/y;)Landroidx/media3/common/y;
    .locals 5
    .annotation build Landroidx/annotation/CallSuper;
    .end annotation

    iget-wide v0, p0, Landroidx/media3/exoplayer/source/s;->F:J

    const-wide/16 v2, 0x0

    cmp-long v4, v0, v2

    if-eqz v4, :cond_0

    iget-wide v0, p1, Landroidx/media3/common/y;->q:J

    const-wide v2, 0x7fffffffffffffffL

    cmp-long v4, v0, v2

    if-eqz v4, :cond_0

    invoke-virtual {p1}, Landroidx/media3/common/y;->b()Landroidx/media3/common/y$b;

    move-result-object v0

    iget-wide v1, p1, Landroidx/media3/common/y;->q:J

    iget-wide v3, p0, Landroidx/media3/exoplayer/source/s;->F:J

    add-long/2addr v1, v3

    invoke-virtual {v0, v1, v2}, Landroidx/media3/common/y$b;->o0(J)Landroidx/media3/common/y$b;

    move-result-object p1

    invoke-virtual {p1}, Landroidx/media3/common/y$b;->I()Landroidx/media3/common/y;

    move-result-object p1

    :cond_0
    return-object p1
.end method

.method public final y()I
    .locals 1

    iget v0, p0, Landroidx/media3/exoplayer/source/s;->q:I

    return v0
.end method

.method public final declared-synchronized z()J
    .locals 3

    monitor-enter p0

    :try_start_0
    iget v0, p0, Landroidx/media3/exoplayer/source/s;->p:I

    if-nez v0, :cond_0

    const-wide/high16 v0, -0x8000000000000000L

    goto :goto_0

    :cond_0
    iget-object v0, p0, Landroidx/media3/exoplayer/source/s;->n:[J

    iget v1, p0, Landroidx/media3/exoplayer/source/s;->r:I

    aget-wide v1, v0, v1
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    move-wide v0, v1

    :goto_0
    monitor-exit p0

    return-wide v0

    :catchall_0
    move-exception v0

    monitor-exit p0

    throw v0
.end method
