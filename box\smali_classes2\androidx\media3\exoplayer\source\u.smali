.class public final Landroidx/media3/exoplayer/source/u;
.super Ljava/lang/Object;

# interfaces
.implements Landroidx/media3/exoplayer/source/k;
.implements Landroidx/media3/exoplayer/upstream/Loader$b;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Landroidx/media3/exoplayer/source/u$b;,
        Landroidx/media3/exoplayer/source/u$c;
    }
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Landroidx/media3/exoplayer/source/k;",
        "Landroidx/media3/exoplayer/upstream/Loader$b<",
        "Landroidx/media3/exoplayer/source/u$c;",
        ">;"
    }
.end annotation


# instance fields
.field public final a:Lh2/g;

.field public final b:Landroidx/media3/datasource/a$a;

.field public final c:Lh2/o;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public final d:Landroidx/media3/exoplayer/upstream/m;

.field public final e:Landroidx/media3/exoplayer/source/m$a;

.field public final f:Lu2/k0;

.field public final g:Ljava/util/ArrayList;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/ArrayList<",
            "Landroidx/media3/exoplayer/source/u$b;",
            ">;"
        }
    .end annotation
.end field

.field public final h:J

.field public final i:Landroidx/media3/exoplayer/upstream/Loader;

.field public final j:Landroidx/media3/common/y;

.field public final k:Z

.field public l:Z

.field public m:[B

.field public n:I


# direct methods
.method public constructor <init>(Lh2/g;Landroidx/media3/datasource/a$a;Lh2/o;Landroidx/media3/common/y;JLandroidx/media3/exoplayer/upstream/m;Landroidx/media3/exoplayer/source/m$a;Z)V
    .locals 0
    .param p3    # Lh2/o;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Landroidx/media3/exoplayer/source/u;->a:Lh2/g;

    iput-object p2, p0, Landroidx/media3/exoplayer/source/u;->b:Landroidx/media3/datasource/a$a;

    iput-object p3, p0, Landroidx/media3/exoplayer/source/u;->c:Lh2/o;

    iput-object p4, p0, Landroidx/media3/exoplayer/source/u;->j:Landroidx/media3/common/y;

    iput-wide p5, p0, Landroidx/media3/exoplayer/source/u;->h:J

    iput-object p7, p0, Landroidx/media3/exoplayer/source/u;->d:Landroidx/media3/exoplayer/upstream/m;

    iput-object p8, p0, Landroidx/media3/exoplayer/source/u;->e:Landroidx/media3/exoplayer/source/m$a;

    iput-boolean p9, p0, Landroidx/media3/exoplayer/source/u;->k:Z

    new-instance p1, Lu2/k0;

    const/4 p2, 0x1

    new-array p3, p2, [Landroidx/media3/common/n0;

    new-instance p5, Landroidx/media3/common/n0;

    new-array p2, p2, [Landroidx/media3/common/y;

    const/4 p6, 0x0

    aput-object p4, p2, p6

    invoke-direct {p5, p2}, Landroidx/media3/common/n0;-><init>([Landroidx/media3/common/y;)V

    aput-object p5, p3, p6

    invoke-direct {p1, p3}, Lu2/k0;-><init>([Landroidx/media3/common/n0;)V

    iput-object p1, p0, Landroidx/media3/exoplayer/source/u;->f:Lu2/k0;

    new-instance p1, Ljava/util/ArrayList;

    invoke-direct {p1}, Ljava/util/ArrayList;-><init>()V

    iput-object p1, p0, Landroidx/media3/exoplayer/source/u;->g:Ljava/util/ArrayList;

    new-instance p1, Landroidx/media3/exoplayer/upstream/Loader;

    const-string p2, "SingleSampleMediaPeriod"

    invoke-direct {p1, p2}, Landroidx/media3/exoplayer/upstream/Loader;-><init>(Ljava/lang/String;)V

    iput-object p1, p0, Landroidx/media3/exoplayer/source/u;->i:Landroidx/media3/exoplayer/upstream/Loader;

    return-void
.end method

.method public static synthetic c(Landroidx/media3/exoplayer/source/u;)Landroidx/media3/exoplayer/source/m$a;
    .locals 0

    iget-object p0, p0, Landroidx/media3/exoplayer/source/u;->e:Landroidx/media3/exoplayer/source/m$a;

    return-object p0
.end method


# virtual methods
.method public a(Landroidx/media3/exoplayer/w1;)Z
    .locals 22

    move-object/from16 v0, p0

    iget-boolean v1, v0, Landroidx/media3/exoplayer/source/u;->l:Z

    if-nez v1, :cond_2

    iget-object v1, v0, Landroidx/media3/exoplayer/source/u;->i:Landroidx/media3/exoplayer/upstream/Loader;

    invoke-virtual {v1}, Landroidx/media3/exoplayer/upstream/Loader;->i()Z

    move-result v1

    if-nez v1, :cond_2

    iget-object v1, v0, Landroidx/media3/exoplayer/source/u;->i:Landroidx/media3/exoplayer/upstream/Loader;

    invoke-virtual {v1}, Landroidx/media3/exoplayer/upstream/Loader;->h()Z

    move-result v1

    if-eqz v1, :cond_0

    goto :goto_0

    :cond_0
    iget-object v1, v0, Landroidx/media3/exoplayer/source/u;->b:Landroidx/media3/datasource/a$a;

    invoke-interface {v1}, Landroidx/media3/datasource/a$a;->createDataSource()Landroidx/media3/datasource/a;

    move-result-object v1

    iget-object v2, v0, Landroidx/media3/exoplayer/source/u;->c:Lh2/o;

    if-eqz v2, :cond_1

    invoke-interface {v1, v2}, Landroidx/media3/datasource/a;->c(Lh2/o;)V

    :cond_1
    new-instance v2, Landroidx/media3/exoplayer/source/u$c;

    iget-object v3, v0, Landroidx/media3/exoplayer/source/u;->a:Lh2/g;

    invoke-direct {v2, v3, v1}, Landroidx/media3/exoplayer/source/u$c;-><init>(Lh2/g;Landroidx/media3/datasource/a;)V

    iget-object v1, v0, Landroidx/media3/exoplayer/source/u;->i:Landroidx/media3/exoplayer/upstream/Loader;

    iget-object v3, v0, Landroidx/media3/exoplayer/source/u;->d:Landroidx/media3/exoplayer/upstream/m;

    const/4 v4, 0x1

    invoke-interface {v3, v4}, Landroidx/media3/exoplayer/upstream/m;->a(I)I

    move-result v3

    invoke-virtual {v1, v2, v0, v3}, Landroidx/media3/exoplayer/upstream/Loader;->m(Landroidx/media3/exoplayer/upstream/Loader$d;Landroidx/media3/exoplayer/upstream/Loader$b;I)J

    move-result-wide v9

    iget-object v11, v0, Landroidx/media3/exoplayer/source/u;->e:Landroidx/media3/exoplayer/source/m$a;

    new-instance v12, Lu2/n;

    iget-wide v6, v2, Landroidx/media3/exoplayer/source/u$c;->a:J

    iget-object v8, v0, Landroidx/media3/exoplayer/source/u;->a:Lh2/g;

    move-object v5, v12

    invoke-direct/range {v5 .. v10}, Lu2/n;-><init>(JLh2/g;J)V

    const/4 v13, 0x1

    const/4 v14, -0x1

    iget-object v15, v0, Landroidx/media3/exoplayer/source/u;->j:Landroidx/media3/common/y;

    const/16 v16, 0x0

    const/16 v17, 0x0

    const-wide/16 v18, 0x0

    iget-wide v1, v0, Landroidx/media3/exoplayer/source/u;->h:J

    move-wide/from16 v20, v1

    invoke-virtual/range {v11 .. v21}, Landroidx/media3/exoplayer/source/m$a;->z(Lu2/n;IILandroidx/media3/common/y;ILjava/lang/Object;JJ)V

    return v4

    :cond_2
    :goto_0
    const/4 v1, 0x0

    return v1
.end method

.method public b(JLandroidx/media3/exoplayer/b3;)J
    .locals 0

    return-wide p1
.end method

.method public d(Landroidx/media3/exoplayer/source/u$c;JJZ)V
    .locals 16

    move-object/from16 v0, p0

    move-object/from16 v1, p1

    invoke-static/range {p1 .. p1}, Landroidx/media3/exoplayer/source/u$c;->a(Landroidx/media3/exoplayer/source/u$c;)Lh2/m;

    move-result-object v2

    new-instance v15, Lu2/n;

    iget-wide v4, v1, Landroidx/media3/exoplayer/source/u$c;->a:J

    iget-object v6, v1, Landroidx/media3/exoplayer/source/u$c;->b:Lh2/g;

    invoke-virtual {v2}, Lh2/m;->e()Landroid/net/Uri;

    move-result-object v7

    invoke-virtual {v2}, Lh2/m;->f()Ljava/util/Map;

    move-result-object v8

    invoke-virtual {v2}, Lh2/m;->d()J

    move-result-wide v13

    move-object v3, v15

    move-wide/from16 v9, p2

    move-wide/from16 v11, p4

    invoke-direct/range {v3 .. v14}, Lu2/n;-><init>(JLh2/g;Landroid/net/Uri;Ljava/util/Map;JJJ)V

    iget-object v2, v0, Landroidx/media3/exoplayer/source/u;->d:Landroidx/media3/exoplayer/upstream/m;

    iget-wide v3, v1, Landroidx/media3/exoplayer/source/u$c;->a:J

    invoke-interface {v2, v3, v4}, Landroidx/media3/exoplayer/upstream/m;->b(J)V

    iget-object v3, v0, Landroidx/media3/exoplayer/source/u;->e:Landroidx/media3/exoplayer/source/m$a;

    const/4 v5, 0x1

    const/4 v6, -0x1

    const/4 v7, 0x0

    const/4 v8, 0x0

    const/4 v9, 0x0

    const-wide/16 v10, 0x0

    iget-wide v12, v0, Landroidx/media3/exoplayer/source/u;->h:J

    move-object v4, v15

    invoke-virtual/range {v3 .. v13}, Landroidx/media3/exoplayer/source/m$a;->q(Lu2/n;IILandroidx/media3/common/y;ILjava/lang/Object;JJ)V

    return-void
.end method

.method public discardBuffer(JZ)V
    .locals 0

    return-void
.end method

.method public e(Landroidx/media3/exoplayer/source/k$a;J)V
    .locals 0

    invoke-interface {p1, p0}, Landroidx/media3/exoplayer/source/k$a;->g(Landroidx/media3/exoplayer/source/k;)V

    return-void
.end method

.method public f([Lx2/z;[Z[Lu2/e0;[ZJ)J
    .locals 4

    const/4 v0, 0x0

    :goto_0
    array-length v1, p1

    if-ge v0, v1, :cond_3

    aget-object v1, p3, v0

    const/4 v2, 0x0

    if-eqz v1, :cond_1

    aget-object v3, p1, v0

    if-eqz v3, :cond_0

    aget-boolean v3, p2, v0

    if-nez v3, :cond_1

    :cond_0
    iget-object v3, p0, Landroidx/media3/exoplayer/source/u;->g:Ljava/util/ArrayList;

    invoke-virtual {v3, v1}, Ljava/util/ArrayList;->remove(Ljava/lang/Object;)Z

    aput-object v2, p3, v0

    :cond_1
    aget-object v1, p3, v0

    if-nez v1, :cond_2

    aget-object v1, p1, v0

    if-eqz v1, :cond_2

    new-instance v1, Landroidx/media3/exoplayer/source/u$b;

    invoke-direct {v1, p0, v2}, Landroidx/media3/exoplayer/source/u$b;-><init>(Landroidx/media3/exoplayer/source/u;Landroidx/media3/exoplayer/source/u$a;)V

    iget-object v2, p0, Landroidx/media3/exoplayer/source/u;->g:Ljava/util/ArrayList;

    invoke-virtual {v2, v1}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    aput-object v1, p3, v0

    const/4 v1, 0x1

    aput-boolean v1, p4, v0

    :cond_2
    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :cond_3
    return-wide p5
.end method

.method public g(Landroidx/media3/exoplayer/source/u$c;JJ)V
    .locals 16

    move-object/from16 v0, p0

    move-object/from16 v1, p1

    invoke-static/range {p1 .. p1}, Landroidx/media3/exoplayer/source/u$c;->a(Landroidx/media3/exoplayer/source/u$c;)Lh2/m;

    move-result-object v2

    invoke-virtual {v2}, Lh2/m;->d()J

    move-result-wide v2

    long-to-int v3, v2

    iput v3, v0, Landroidx/media3/exoplayer/source/u;->n:I

    invoke-static/range {p1 .. p1}, Landroidx/media3/exoplayer/source/u$c;->b(Landroidx/media3/exoplayer/source/u$c;)[B

    move-result-object v2

    invoke-static {v2}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, [B

    iput-object v2, v0, Landroidx/media3/exoplayer/source/u;->m:[B

    const/4 v2, 0x1

    iput-boolean v2, v0, Landroidx/media3/exoplayer/source/u;->l:Z

    invoke-static/range {p1 .. p1}, Landroidx/media3/exoplayer/source/u$c;->a(Landroidx/media3/exoplayer/source/u$c;)Lh2/m;

    move-result-object v2

    new-instance v15, Lu2/n;

    iget-wide v4, v1, Landroidx/media3/exoplayer/source/u$c;->a:J

    iget-object v6, v1, Landroidx/media3/exoplayer/source/u$c;->b:Lh2/g;

    invoke-virtual {v2}, Lh2/m;->e()Landroid/net/Uri;

    move-result-object v7

    invoke-virtual {v2}, Lh2/m;->f()Ljava/util/Map;

    move-result-object v8

    iget v2, v0, Landroidx/media3/exoplayer/source/u;->n:I

    int-to-long v13, v2

    move-object v3, v15

    move-wide/from16 v9, p2

    move-wide/from16 v11, p4

    invoke-direct/range {v3 .. v14}, Lu2/n;-><init>(JLh2/g;Landroid/net/Uri;Ljava/util/Map;JJJ)V

    iget-object v2, v0, Landroidx/media3/exoplayer/source/u;->d:Landroidx/media3/exoplayer/upstream/m;

    iget-wide v3, v1, Landroidx/media3/exoplayer/source/u$c;->a:J

    invoke-interface {v2, v3, v4}, Landroidx/media3/exoplayer/upstream/m;->b(J)V

    iget-object v3, v0, Landroidx/media3/exoplayer/source/u;->e:Landroidx/media3/exoplayer/source/m$a;

    const/4 v5, 0x1

    const/4 v6, -0x1

    iget-object v7, v0, Landroidx/media3/exoplayer/source/u;->j:Landroidx/media3/common/y;

    const/4 v8, 0x0

    const/4 v9, 0x0

    const-wide/16 v10, 0x0

    iget-wide v12, v0, Landroidx/media3/exoplayer/source/u;->h:J

    move-object v4, v15

    invoke-virtual/range {v3 .. v13}, Landroidx/media3/exoplayer/source/m$a;->t(Lu2/n;IILandroidx/media3/common/y;ILjava/lang/Object;JJ)V

    return-void
.end method

.method public getBufferedPositionUs()J
    .locals 2

    iget-boolean v0, p0, Landroidx/media3/exoplayer/source/u;->l:Z

    if-eqz v0, :cond_0

    const-wide/high16 v0, -0x8000000000000000L

    goto :goto_0

    :cond_0
    const-wide/16 v0, 0x0

    :goto_0
    return-wide v0
.end method

.method public getNextLoadPositionUs()J
    .locals 2

    iget-boolean v0, p0, Landroidx/media3/exoplayer/source/u;->l:Z

    if-nez v0, :cond_1

    iget-object v0, p0, Landroidx/media3/exoplayer/source/u;->i:Landroidx/media3/exoplayer/upstream/Loader;

    invoke-virtual {v0}, Landroidx/media3/exoplayer/upstream/Loader;->i()Z

    move-result v0

    if-eqz v0, :cond_0

    goto :goto_0

    :cond_0
    const-wide/16 v0, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const-wide/high16 v0, -0x8000000000000000L

    :goto_1
    return-wide v0
.end method

.method public getTrackGroups()Lu2/k0;
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/source/u;->f:Lu2/k0;

    return-object v0
.end method

.method public h(Landroidx/media3/exoplayer/source/u$c;JJLjava/io/IOException;I)Landroidx/media3/exoplayer/upstream/Loader$c;
    .locals 36

    move-object/from16 v0, p0

    move-object/from16 v1, p1

    move-object/from16 v13, p6

    move/from16 v2, p7

    invoke-static/range {p1 .. p1}, Landroidx/media3/exoplayer/source/u$c;->a(Landroidx/media3/exoplayer/source/u$c;)Lh2/m;

    move-result-object v3

    new-instance v4, Lu2/n;

    iget-wide v5, v1, Landroidx/media3/exoplayer/source/u$c;->a:J

    iget-object v7, v1, Landroidx/media3/exoplayer/source/u$c;->b:Lh2/g;

    invoke-virtual {v3}, Lh2/m;->e()Landroid/net/Uri;

    move-result-object v18

    invoke-virtual {v3}, Lh2/m;->f()Ljava/util/Map;

    move-result-object v19

    invoke-virtual {v3}, Lh2/m;->d()J

    move-result-wide v24

    move-object v14, v4

    move-wide v15, v5

    move-object/from16 v17, v7

    move-wide/from16 v20, p2

    move-wide/from16 v22, p4

    invoke-direct/range {v14 .. v25}, Lu2/n;-><init>(JLh2/g;Landroid/net/Uri;Ljava/util/Map;JJJ)V

    new-instance v3, Lu2/o;

    const/16 v27, 0x1

    const/16 v28, -0x1

    iget-object v5, v0, Landroidx/media3/exoplayer/source/u;->j:Landroidx/media3/common/y;

    const/16 v30, 0x0

    const/16 v31, 0x0

    const-wide/16 v32, 0x0

    iget-wide v6, v0, Landroidx/media3/exoplayer/source/u;->h:J

    invoke-static {v6, v7}, Le2/u0;->B1(J)J

    move-result-wide v34

    move-object/from16 v26, v3

    move-object/from16 v29, v5

    invoke-direct/range {v26 .. v35}, Lu2/o;-><init>(IILandroidx/media3/common/y;ILjava/lang/Object;JJ)V

    iget-object v5, v0, Landroidx/media3/exoplayer/source/u;->d:Landroidx/media3/exoplayer/upstream/m;

    new-instance v6, Landroidx/media3/exoplayer/upstream/m$c;

    invoke-direct {v6, v4, v3, v13, v2}, Landroidx/media3/exoplayer/upstream/m$c;-><init>(Lu2/n;Lu2/o;Ljava/io/IOException;I)V

    invoke-interface {v5, v6}, Landroidx/media3/exoplayer/upstream/m;->c(Landroidx/media3/exoplayer/upstream/m$c;)J

    move-result-wide v5

    const-wide v7, -0x7fffffffffffffffL    # -4.9E-324

    const/4 v3, 0x0

    const/4 v9, 0x1

    cmp-long v10, v5, v7

    if-eqz v10, :cond_1

    iget-object v7, v0, Landroidx/media3/exoplayer/source/u;->d:Landroidx/media3/exoplayer/upstream/m;

    invoke-interface {v7, v9}, Landroidx/media3/exoplayer/upstream/m;->a(I)I

    move-result v7

    if-lt v2, v7, :cond_0

    goto :goto_0

    :cond_0
    const/4 v2, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 v2, 0x1

    :goto_1
    iget-boolean v7, v0, Landroidx/media3/exoplayer/source/u;->k:Z

    if-eqz v7, :cond_2

    if-eqz v2, :cond_2

    const-string v2, "SingleSampleMediaPeriod"

    const-string v3, "Loading failed, treating as end-of-stream."

    invoke-static {v2, v3, v13}, Le2/o;->i(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Throwable;)V

    iput-boolean v9, v0, Landroidx/media3/exoplayer/source/u;->l:Z

    sget-object v2, Landroidx/media3/exoplayer/upstream/Loader;->f:Landroidx/media3/exoplayer/upstream/Loader$c;

    :goto_2
    move-object v15, v2

    goto :goto_3

    :cond_2
    if-eqz v10, :cond_3

    invoke-static {v3, v5, v6}, Landroidx/media3/exoplayer/upstream/Loader;->g(ZJ)Landroidx/media3/exoplayer/upstream/Loader$c;

    move-result-object v2

    goto :goto_2

    :cond_3
    sget-object v2, Landroidx/media3/exoplayer/upstream/Loader;->g:Landroidx/media3/exoplayer/upstream/Loader$c;

    goto :goto_2

    :goto_3
    invoke-virtual {v15}, Landroidx/media3/exoplayer/upstream/Loader$c;->c()Z

    move-result v2

    xor-int/lit8 v16, v2, 0x1

    iget-object v2, v0, Landroidx/media3/exoplayer/source/u;->e:Landroidx/media3/exoplayer/source/m$a;

    const/4 v5, 0x1

    const/4 v6, -0x1

    iget-object v7, v0, Landroidx/media3/exoplayer/source/u;->j:Landroidx/media3/common/y;

    const/4 v8, 0x0

    const/4 v9, 0x0

    iget-wide v10, v0, Landroidx/media3/exoplayer/source/u;->h:J

    move-object v3, v4

    move v4, v5

    move v5, v6

    move-object v6, v7

    move v7, v8

    move-object v8, v9

    move-wide v11, v10

    const-wide/16 v9, 0x0

    move-object/from16 v13, p6

    move/from16 v14, v16

    invoke-virtual/range {v2 .. v14}, Landroidx/media3/exoplayer/source/m$a;->v(Lu2/n;IILandroidx/media3/common/y;ILjava/lang/Object;JJLjava/io/IOException;Z)V

    if-eqz v16, :cond_4

    iget-object v2, v0, Landroidx/media3/exoplayer/source/u;->d:Landroidx/media3/exoplayer/upstream/m;

    iget-wide v3, v1, Landroidx/media3/exoplayer/source/u$c;->a:J

    invoke-interface {v2, v3, v4}, Landroidx/media3/exoplayer/upstream/m;->b(J)V

    :cond_4
    return-object v15
.end method

.method public i()V
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/source/u;->i:Landroidx/media3/exoplayer/upstream/Loader;

    invoke-virtual {v0}, Landroidx/media3/exoplayer/upstream/Loader;->k()V

    return-void
.end method

.method public isLoading()Z
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/source/u;->i:Landroidx/media3/exoplayer/upstream/Loader;

    invoke-virtual {v0}, Landroidx/media3/exoplayer/upstream/Loader;->i()Z

    move-result v0

    return v0
.end method

.method public bridge synthetic k(Landroidx/media3/exoplayer/upstream/Loader$d;JJLjava/io/IOException;I)Landroidx/media3/exoplayer/upstream/Loader$c;
    .locals 0

    check-cast p1, Landroidx/media3/exoplayer/source/u$c;

    invoke-virtual/range {p0 .. p7}, Landroidx/media3/exoplayer/source/u;->h(Landroidx/media3/exoplayer/source/u$c;JJLjava/io/IOException;I)Landroidx/media3/exoplayer/upstream/Loader$c;

    move-result-object p1

    return-object p1
.end method

.method public maybeThrowPrepareError()V
    .locals 0

    return-void
.end method

.method public bridge synthetic n(Landroidx/media3/exoplayer/upstream/Loader$d;JJ)V
    .locals 0

    check-cast p1, Landroidx/media3/exoplayer/source/u$c;

    invoke-virtual/range {p0 .. p5}, Landroidx/media3/exoplayer/source/u;->g(Landroidx/media3/exoplayer/source/u$c;JJ)V

    return-void
.end method

.method public bridge synthetic o(Landroidx/media3/exoplayer/upstream/Loader$d;JJZ)V
    .locals 0

    check-cast p1, Landroidx/media3/exoplayer/source/u$c;

    invoke-virtual/range {p0 .. p6}, Landroidx/media3/exoplayer/source/u;->d(Landroidx/media3/exoplayer/source/u$c;JJZ)V

    return-void
.end method

.method public readDiscontinuity()J
    .locals 2

    const-wide v0, -0x7fffffffffffffffL    # -4.9E-324

    return-wide v0
.end method

.method public reevaluateBuffer(J)V
    .locals 0

    return-void
.end method

.method public seekToUs(J)J
    .locals 2

    const/4 v0, 0x0

    :goto_0
    iget-object v1, p0, Landroidx/media3/exoplayer/source/u;->g:Ljava/util/ArrayList;

    invoke-virtual {v1}, Ljava/util/ArrayList;->size()I

    move-result v1

    if-ge v0, v1, :cond_0

    iget-object v1, p0, Landroidx/media3/exoplayer/source/u;->g:Ljava/util/ArrayList;

    invoke-virtual {v1, v0}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Landroidx/media3/exoplayer/source/u$b;

    invoke-virtual {v1}, Landroidx/media3/exoplayer/source/u$b;->b()V

    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :cond_0
    return-wide p1
.end method
