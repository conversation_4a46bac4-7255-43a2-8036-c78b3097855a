.class public Landroidx/media3/exoplayer/u2$a;
.super Lu2/m;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Landroidx/media3/exoplayer/u2;->E(Lu2/f0;)Landroidx/media3/exoplayer/u2;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field public final g:Landroidx/media3/common/m0$c;

.field public final synthetic h:Landroidx/media3/exoplayer/u2;


# direct methods
.method public constructor <init>(Landroidx/media3/exoplayer/u2;Landroidx/media3/common/m0;)V
    .locals 0

    iput-object p1, p0, Landroidx/media3/exoplayer/u2$a;->h:Landroidx/media3/exoplayer/u2;

    invoke-direct {p0, p2}, Lu2/m;-><init>(Landroidx/media3/common/m0;)V

    new-instance p1, Landroidx/media3/common/m0$c;

    invoke-direct {p1}, Landroidx/media3/common/m0$c;-><init>()V

    iput-object p1, p0, Landroidx/media3/exoplayer/u2$a;->g:Landroidx/media3/common/m0$c;

    return-void
.end method


# virtual methods
.method public g(ILandroidx/media3/common/m0$b;Z)Landroidx/media3/common/m0$b;
    .locals 10

    invoke-super {p0, p1, p2, p3}, Lu2/m;->g(ILandroidx/media3/common/m0$b;Z)Landroidx/media3/common/m0$b;

    move-result-object p1

    iget p3, p1, Landroidx/media3/common/m0$b;->c:I

    iget-object v0, p0, Landroidx/media3/exoplayer/u2$a;->g:Landroidx/media3/common/m0$c;

    invoke-super {p0, p3, v0}, Landroidx/media3/common/m0;->n(ILandroidx/media3/common/m0$c;)Landroidx/media3/common/m0$c;

    move-result-object p3

    invoke-virtual {p3}, Landroidx/media3/common/m0$c;->f()Z

    move-result p3

    if-eqz p3, :cond_0

    iget-object v1, p2, Landroidx/media3/common/m0$b;->a:Ljava/lang/Object;

    iget-object v2, p2, Landroidx/media3/common/m0$b;->b:Ljava/lang/Object;

    iget v3, p2, Landroidx/media3/common/m0$b;->c:I

    iget-wide v4, p2, Landroidx/media3/common/m0$b;->d:J

    iget-wide v6, p2, Landroidx/media3/common/m0$b;->e:J

    sget-object v8, Landroidx/media3/common/c;->g:Landroidx/media3/common/c;

    const/4 v9, 0x1

    move-object v0, p1

    invoke-virtual/range {v0 .. v9}, Landroidx/media3/common/m0$b;->u(Ljava/lang/Object;Ljava/lang/Object;IJJLandroidx/media3/common/c;Z)Landroidx/media3/common/m0$b;

    goto :goto_0

    :cond_0
    const/4 p2, 0x1

    iput-boolean p2, p1, Landroidx/media3/common/m0$b;->f:Z

    :goto_0
    return-object p1
.end method
