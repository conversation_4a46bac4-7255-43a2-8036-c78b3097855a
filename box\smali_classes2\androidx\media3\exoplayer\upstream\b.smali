.class public interface abstract Landroidx/media3/exoplayer/upstream/b;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Landroidx/media3/exoplayer/upstream/b$a;
    }
.end annotation


# virtual methods
.method public abstract a(Landroidx/media3/exoplayer/upstream/b$a;)V
.end method

.method public abstract allocate()Landroidx/media3/exoplayer/upstream/a;
.end method

.method public abstract b(Landroidx/media3/exoplayer/upstream/a;)V
.end method

.method public abstract getIndividualAllocationLength()I
.end method

.method public abstract trim()V
.end method
