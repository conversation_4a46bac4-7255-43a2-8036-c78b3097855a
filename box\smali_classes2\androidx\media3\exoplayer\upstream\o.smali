.class public final Landroidx/media3/exoplayer/upstream/o;
.super Ljava/lang/Object;

# interfaces
.implements Landroidx/media3/exoplayer/upstream/Loader$d;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Landroidx/media3/exoplayer/upstream/o$a;
    }
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "<T:",
        "Ljava/lang/Object;",
        ">",
        "Ljava/lang/Object;",
        "Landroidx/media3/exoplayer/upstream/Loader$d;"
    }
.end annotation


# instance fields
.field public final a:J

.field public final b:Lh2/g;

.field public final c:I

.field public final d:Lh2/m;

.field public final e:Landroidx/media3/exoplayer/upstream/o$a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Landroidx/media3/exoplayer/upstream/o$a<",
            "+TT;>;"
        }
    .end annotation
.end field

.field public volatile f:Ljava/lang/Object;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation

    .annotation system Ldalvik/annotation/Signature;
        value = {
            "TT;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(Landroidx/media3/datasource/a;Landroid/net/Uri;ILandroidx/media3/exoplayer/upstream/o$a;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroidx/media3/datasource/a;",
            "Landroid/net/Uri;",
            "I",
            "Landroidx/media3/exoplayer/upstream/o$a<",
            "+TT;>;)V"
        }
    .end annotation

    new-instance v0, Lh2/g$b;

    invoke-direct {v0}, Lh2/g$b;-><init>()V

    invoke-virtual {v0, p2}, Lh2/g$b;->i(Landroid/net/Uri;)Lh2/g$b;

    move-result-object p2

    const/4 v0, 0x1

    invoke-virtual {p2, v0}, Lh2/g$b;->b(I)Lh2/g$b;

    move-result-object p2

    invoke-virtual {p2}, Lh2/g$b;->a()Lh2/g;

    move-result-object p2

    invoke-direct {p0, p1, p2, p3, p4}, Landroidx/media3/exoplayer/upstream/o;-><init>(Landroidx/media3/datasource/a;Lh2/g;ILandroidx/media3/exoplayer/upstream/o$a;)V

    return-void
.end method

.method public constructor <init>(Landroidx/media3/datasource/a;Lh2/g;ILandroidx/media3/exoplayer/upstream/o$a;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroidx/media3/datasource/a;",
            "Lh2/g;",
            "I",
            "Landroidx/media3/exoplayer/upstream/o$a<",
            "+TT;>;)V"
        }
    .end annotation

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    new-instance v0, Lh2/m;

    invoke-direct {v0, p1}, Lh2/m;-><init>(Landroidx/media3/datasource/a;)V

    iput-object v0, p0, Landroidx/media3/exoplayer/upstream/o;->d:Lh2/m;

    iput-object p2, p0, Landroidx/media3/exoplayer/upstream/o;->b:Lh2/g;

    iput p3, p0, Landroidx/media3/exoplayer/upstream/o;->c:I

    iput-object p4, p0, Landroidx/media3/exoplayer/upstream/o;->e:Landroidx/media3/exoplayer/upstream/o$a;

    invoke-static {}, Lu2/n;->a()J

    move-result-wide p1

    iput-wide p1, p0, Landroidx/media3/exoplayer/upstream/o;->a:J

    return-void
.end method

.method public static e(Landroidx/media3/datasource/a;Landroidx/media3/exoplayer/upstream/o$a;Lh2/g;I)Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">(",
            "Landroidx/media3/datasource/a;",
            "Landroidx/media3/exoplayer/upstream/o$a<",
            "+TT;>;",
            "Lh2/g;",
            "I)TT;"
        }
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    new-instance v0, Landroidx/media3/exoplayer/upstream/o;

    invoke-direct {v0, p0, p2, p3, p1}, Landroidx/media3/exoplayer/upstream/o;-><init>(Landroidx/media3/datasource/a;Lh2/g;ILandroidx/media3/exoplayer/upstream/o$a;)V

    invoke-virtual {v0}, Landroidx/media3/exoplayer/upstream/o;->load()V

    invoke-virtual {v0}, Landroidx/media3/exoplayer/upstream/o;->c()Ljava/lang/Object;

    move-result-object p0

    invoke-static {p0}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    return-object p0
.end method


# virtual methods
.method public a()J
    .locals 2

    iget-object v0, p0, Landroidx/media3/exoplayer/upstream/o;->d:Lh2/m;

    invoke-virtual {v0}, Lh2/m;->d()J

    move-result-wide v0

    return-wide v0
.end method

.method public b()Ljava/util/Map;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;>;"
        }
    .end annotation

    iget-object v0, p0, Landroidx/media3/exoplayer/upstream/o;->d:Lh2/m;

    invoke-virtual {v0}, Lh2/m;->f()Ljava/util/Map;

    move-result-object v0

    return-object v0
.end method

.method public final c()Ljava/lang/Object;
    .locals 1
    .annotation build Landroidx/annotation/Nullable;
    .end annotation

    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()TT;"
        }
    .end annotation

    iget-object v0, p0, Landroidx/media3/exoplayer/upstream/o;->f:Ljava/lang/Object;

    return-object v0
.end method

.method public final cancelLoad()V
    .locals 0

    return-void
.end method

.method public d()Landroid/net/Uri;
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/upstream/o;->d:Lh2/m;

    invoke-virtual {v0}, Lh2/m;->e()Landroid/net/Uri;

    move-result-object v0

    return-object v0
.end method

.method public final load()V
    .locals 3
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget-object v0, p0, Landroidx/media3/exoplayer/upstream/o;->d:Lh2/m;

    invoke-virtual {v0}, Lh2/m;->g()V

    new-instance v0, Lh2/e;

    iget-object v1, p0, Landroidx/media3/exoplayer/upstream/o;->d:Lh2/m;

    iget-object v2, p0, Landroidx/media3/exoplayer/upstream/o;->b:Lh2/g;

    invoke-direct {v0, v1, v2}, Lh2/e;-><init>(Landroidx/media3/datasource/a;Lh2/g;)V

    :try_start_0
    invoke-virtual {v0}, Lh2/e;->b()V

    iget-object v1, p0, Landroidx/media3/exoplayer/upstream/o;->d:Lh2/m;

    invoke-virtual {v1}, Lh2/m;->getUri()Landroid/net/Uri;

    move-result-object v1

    invoke-static {v1}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Landroid/net/Uri;

    iget-object v2, p0, Landroidx/media3/exoplayer/upstream/o;->e:Landroidx/media3/exoplayer/upstream/o$a;

    invoke-interface {v2, v1, v0}, Landroidx/media3/exoplayer/upstream/o$a;->parse(Landroid/net/Uri;Ljava/io/InputStream;)Ljava/lang/Object;

    move-result-object v1

    iput-object v1, p0, Landroidx/media3/exoplayer/upstream/o;->f:Ljava/lang/Object;
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    invoke-static {v0}, Le2/u0;->m(Ljava/io/Closeable;)V

    return-void

    :catchall_0
    move-exception v1

    invoke-static {v0}, Le2/u0;->m(Ljava/io/Closeable;)V

    throw v1
.end method
