.class public final synthetic Landroidx/media3/exoplayer/upstream/q;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/util/Comparator;


# direct methods
.method public synthetic constructor <init>()V
    .locals 0

    invoke-direct {p0}, <PERSON>java/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final compare(Ljava/lang/Object;Ljava/lang/Object;)I
    .locals 0

    check-cast p1, Landroidx/media3/exoplayer/upstream/r$b;

    check-cast p2, Landroidx/media3/exoplayer/upstream/r$b;

    invoke-static {p1, p2}, Landroidx/media3/exoplayer/upstream/r;->a(Landroidx/media3/exoplayer/upstream/r$b;Landroidx/media3/exoplayer/upstream/r$b;)I

    move-result p1

    return p1
.end method
