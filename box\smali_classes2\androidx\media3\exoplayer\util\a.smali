.class public Landroidx/media3/exoplayer/util/a;
.super Ljava/lang/Object;

# interfaces
.implements Lj2/c;


# static fields
.field public static final e:Ljava/text/NumberFormat;


# instance fields
.field public final a:Ljava/lang/String;

.field public final b:Landroidx/media3/common/m0$c;

.field public final c:Landroidx/media3/common/m0$b;

.field public final d:J


# direct methods
.method static constructor <clinit>()V
    .locals 2

    sget-object v0, Ljava/util/Locale;->US:Ljava/util/Locale;

    invoke-static {v0}, Ljava/text/NumberFormat;->getInstance(Ljava/util/Locale;)Ljava/text/NumberFormat;

    move-result-object v0

    sput-object v0, Landroidx/media3/exoplayer/util/a;->e:Ljava/text/NumberFormat;

    const/4 v1, 0x2

    invoke-virtual {v0, v1}, Ljava/text/NumberFormat;->setMinimumFractionDigits(I)V

    invoke-virtual {v0, v1}, Ljava/text/NumberFormat;->setMaximumFractionDigits(I)V

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/text/NumberFormat;->setGroupingUsed(Z)V

    return-void
.end method

.method public constructor <init>(Ljava/lang/String;)V
    .locals 2

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Landroidx/media3/exoplayer/util/a;->a:Ljava/lang/String;

    new-instance p1, Landroidx/media3/common/m0$c;

    invoke-direct {p1}, Landroidx/media3/common/m0$c;-><init>()V

    iput-object p1, p0, Landroidx/media3/exoplayer/util/a;->b:Landroidx/media3/common/m0$c;

    new-instance p1, Landroidx/media3/common/m0$b;

    invoke-direct {p1}, Landroidx/media3/common/m0$b;-><init>()V

    iput-object p1, p0, Landroidx/media3/exoplayer/util/a;->c:Landroidx/media3/common/m0$b;

    invoke-static {}, Landroid/os/SystemClock;->elapsedRealtime()J

    move-result-wide v0

    iput-wide v0, p0, Landroidx/media3/exoplayer/util/a;->d:J

    return-void
.end method

.method public static A0(I)Ljava/lang/String;
    .locals 1

    if-eqz p0, :cond_2

    const/4 v0, 0x1

    if-eq p0, v0, :cond_1

    const/4 v0, 0x2

    if-eq p0, v0, :cond_0

    const-string p0, "?"

    return-object p0

    :cond_0
    const-string p0, "ALL"

    return-object p0

    :cond_1
    const-string p0, "ONE"

    return-object p0

    :cond_2
    const-string p0, "OFF"

    return-object p0
.end method

.method public static B0(I)Ljava/lang/String;
    .locals 1

    const/4 v0, 0x1

    if-eq p0, v0, :cond_3

    const/4 v0, 0x2

    if-eq p0, v0, :cond_2

    const/4 v0, 0x3

    if-eq p0, v0, :cond_1

    const/4 v0, 0x4

    if-eq p0, v0, :cond_0

    const-string p0, "?"

    return-object p0

    :cond_0
    const-string p0, "ENDED"

    return-object p0

    :cond_1
    const-string p0, "READY"

    return-object p0

    :cond_2
    const-string p0, "BUFFERING"

    return-object p0

    :cond_3
    const-string p0, "IDLE"

    return-object p0
.end method

.method public static C0(J)Ljava/lang/String;
    .locals 3

    const-wide v0, -0x7fffffffffffffffL    # -4.9E-324

    cmp-long v2, p0, v0

    if-nez v2, :cond_0

    const-string p0, "?"

    goto :goto_0

    :cond_0
    sget-object v0, Landroidx/media3/exoplayer/util/a;->e:Ljava/text/NumberFormat;

    long-to-float p0, p0

    const/high16 p1, 0x447a0000    # 1000.0f

    div-float/2addr p0, p1

    float-to-double p0, p0

    invoke-virtual {v0, p0, p1}, Ljava/text/NumberFormat;->format(D)Ljava/lang/String;

    move-result-object p0

    :goto_0
    return-object p0
.end method

.method public static D0(I)Ljava/lang/String;
    .locals 1

    if-eqz p0, :cond_1

    const/4 v0, 0x1

    if-eq p0, v0, :cond_0

    const-string p0, "?"

    return-object p0

    :cond_0
    const-string p0, "SOURCE_UPDATE"

    return-object p0

    :cond_1
    const-string p0, "PLAYLIST_CHANGED"

    return-object p0
.end method

.method public static E0(Z)Ljava/lang/String;
    .locals 0

    if-eqz p0, :cond_0

    const-string p0, "[X]"

    goto :goto_0

    :cond_0
    const-string p0, "[ ]"

    :goto_0
    return-object p0
.end method

.method public static T(I)Ljava/lang/String;
    .locals 0

    packed-switch p0, :pswitch_data_0

    const-string p0, "?"

    return-object p0

    :pswitch_0
    const-string p0, "SILENCE_SKIP"

    return-object p0

    :pswitch_1
    const-string p0, "INTERNAL"

    return-object p0

    :pswitch_2
    const-string p0, "REMOVE"

    return-object p0

    :pswitch_3
    const-string p0, "SKIP"

    return-object p0

    :pswitch_4
    const-string p0, "SEEK_ADJUSTMENT"

    return-object p0

    :pswitch_5
    const-string p0, "SEEK"

    return-object p0

    :pswitch_6
    const-string p0, "AUTO_TRANSITION"

    return-object p0

    nop

    :pswitch_data_0
    .packed-switch 0x0
        :pswitch_6
        :pswitch_5
        :pswitch_4
        :pswitch_3
        :pswitch_2
        :pswitch_1
        :pswitch_0
    .end packed-switch
.end method

.method public static v(Landroidx/media3/exoplayer/audio/AudioSink$a;)Ljava/lang/String;
    .locals 3

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    iget v1, p0, Landroidx/media3/exoplayer/audio/AudioSink$a;->a:I

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v1, ","

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget v2, p0, Landroidx/media3/exoplayer/audio/AudioSink$a;->c:I

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget v2, p0, Landroidx/media3/exoplayer/audio/AudioSink$a;->b:I

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-boolean v2, p0, Landroidx/media3/exoplayer/audio/AudioSink$a;->d:Z

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-boolean v2, p0, Landroidx/media3/exoplayer/audio/AudioSink$a;->e:Z

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget p0, p0, Landroidx/media3/exoplayer/audio/AudioSink$a;->f:I

    invoke-virtual {v0, p0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    return-object p0
.end method

.method public static x0(I)Ljava/lang/String;
    .locals 1

    if-eqz p0, :cond_3

    const/4 v0, 0x1

    if-eq p0, v0, :cond_2

    const/4 v0, 0x2

    if-eq p0, v0, :cond_1

    const/4 v0, 0x3

    if-eq p0, v0, :cond_0

    const-string p0, "?"

    return-object p0

    :cond_0
    const-string p0, "PLAYLIST_CHANGED"

    return-object p0

    :cond_1
    const-string p0, "SEEK"

    return-object p0

    :cond_2
    const-string p0, "AUTO"

    return-object p0

    :cond_3
    const-string p0, "REPEAT"

    return-object p0
.end method

.method public static y0(I)Ljava/lang/String;
    .locals 1

    const/4 v0, 0x1

    if-eq p0, v0, :cond_4

    const/4 v0, 0x2

    if-eq p0, v0, :cond_3

    const/4 v0, 0x3

    if-eq p0, v0, :cond_2

    const/4 v0, 0x4

    if-eq p0, v0, :cond_1

    const/4 v0, 0x5

    if-eq p0, v0, :cond_0

    const-string p0, "?"

    return-object p0

    :cond_0
    const-string p0, "END_OF_MEDIA_ITEM"

    return-object p0

    :cond_1
    const-string p0, "REMOTE"

    return-object p0

    :cond_2
    const-string p0, "AUDIO_BECOMING_NOISY"

    return-object p0

    :cond_3
    const-string p0, "AUDIO_FOCUS_LOSS"

    return-object p0

    :cond_4
    const-string p0, "USER_REQUEST"

    return-object p0
.end method

.method public static z0(I)Ljava/lang/String;
    .locals 1

    if-eqz p0, :cond_1

    const/4 v0, 0x1

    if-eq p0, v0, :cond_0

    const-string p0, "?"

    return-object p0

    :cond_0
    const-string p0, "TRANSIENT_AUDIO_FOCUS_LOSS"

    return-object p0

    :cond_1
    const-string p0, "NONE"

    return-object p0
.end method


# virtual methods
.method public A(Lj2/c$a;IJJ)V
    .locals 1

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v0, p2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string p2, ", "

    invoke-virtual {v0, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0, p3, p4}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    invoke-virtual {v0, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0, p5, p6}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    const/4 p3, 0x0

    const-string p4, "audioTrackUnderrun"

    invoke-virtual {p0, p1, p4, p2, p3}, Landroidx/media3/exoplayer/util/a;->I0(Lj2/c$a;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Throwable;)V

    return-void
.end method

.method public B(Lj2/c$a;ZI)V
    .locals 1

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v0, p2}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    const-string p2, ", "

    invoke-virtual {v0, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-static {p3}, Landroidx/media3/exoplayer/util/a;->y0(I)Ljava/lang/String;

    move-result-object p2

    invoke-virtual {v0, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    const-string p3, "playWhenReady"

    invoke-virtual {p0, p1, p3, p2}, Landroidx/media3/exoplayer/util/a;->G0(Lj2/c$a;Ljava/lang/String;Ljava/lang/String;)V

    return-void
.end method

.method public C(Lj2/c$a;IJ)V
    .locals 0

    const-string p3, "droppedFrames"

    invoke-static {p2}, Ljava/lang/Integer;->toString(I)Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p0, p1, p3, p2}, Landroidx/media3/exoplayer/util/a;->G0(Lj2/c$a;Ljava/lang/String;Ljava/lang/String;)V

    return-void
.end method

.method public D(Lj2/c$a;)V
    .locals 1

    const-string v0, "drmKeysRestored"

    invoke-virtual {p0, p1, v0}, Landroidx/media3/exoplayer/util/a;->F0(Lj2/c$a;Ljava/lang/String;)V

    return-void
.end method

.method public E(Lj2/c$a;Landroidx/media3/exoplayer/n;)V
    .locals 0

    const-string p2, "audioDisabled"

    invoke-virtual {p0, p1, p2}, Landroidx/media3/exoplayer/util/a;->F0(Lj2/c$a;Ljava/lang/String;)V

    return-void
.end method

.method public F(Lj2/c$a;)V
    .locals 1

    const-string v0, "drmSessionReleased"

    invoke-virtual {p0, p1, v0}, Landroidx/media3/exoplayer/util/a;->F0(Lj2/c$a;Ljava/lang/String;)V

    return-void
.end method

.method public final F0(Lj2/c$a;Ljava/lang/String;)V
    .locals 1

    const/4 v0, 0x0

    invoke-virtual {p0, p1, p2, v0, v0}, Landroidx/media3/exoplayer/util/a;->j0(Lj2/c$a;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Throwable;)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1}, Landroidx/media3/exoplayer/util/a;->H0(Ljava/lang/String;)V

    return-void
.end method

.method public G(Lj2/c$a;Landroidx/media3/common/h0$e;Landroidx/media3/common/h0$e;I)V
    .locals 10

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "reason="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-static {p4}, Landroidx/media3/exoplayer/util/a;->T(I)Ljava/lang/String;

    move-result-object p4

    invoke-virtual {v0, p4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string p4, ", PositionInfo:old ["

    invoke-virtual {v0, p4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string p4, "mediaItem="

    invoke-virtual {v0, p4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget v1, p2, Landroidx/media3/common/h0$e;->c:I

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v1, ", period="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget v2, p2, Landroidx/media3/common/h0$e;->f:I

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v2, ", pos="

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-wide v3, p2, Landroidx/media3/common/h0$e;->g:J

    invoke-virtual {v0, v3, v4}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    iget v3, p2, Landroidx/media3/common/h0$e;->i:I

    const-string v4, ", ad="

    const-string v5, ", adGroup="

    const-string v6, ", contentPos="

    const/4 v7, -0x1

    if-eq v3, v7, :cond_0

    invoke-virtual {v0, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-wide v8, p2, Landroidx/media3/common/h0$e;->h:J

    invoke-virtual {v0, v8, v9}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    invoke-virtual {v0, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget v3, p2, Landroidx/media3/common/h0$e;->i:I

    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v0, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget p2, p2, Landroidx/media3/common/h0$e;->j:I

    invoke-virtual {v0, p2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    :cond_0
    const-string p2, "], PositionInfo:new ["

    invoke-virtual {v0, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0, p4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget p2, p3, Landroidx/media3/common/h0$e;->c:I

    invoke-virtual {v0, p2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget p2, p3, Landroidx/media3/common/h0$e;->f:I

    invoke-virtual {v0, p2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-wide v1, p3, Landroidx/media3/common/h0$e;->g:J

    invoke-virtual {v0, v1, v2}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    iget p2, p3, Landroidx/media3/common/h0$e;->i:I

    if-eq p2, v7, :cond_1

    invoke-virtual {v0, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-wide v1, p3, Landroidx/media3/common/h0$e;->h:J

    invoke-virtual {v0, v1, v2}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    invoke-virtual {v0, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget p2, p3, Landroidx/media3/common/h0$e;->i:I

    invoke-virtual {v0, p2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v0, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget p2, p3, Landroidx/media3/common/h0$e;->j:I

    invoke-virtual {v0, p2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    :cond_1
    const-string p2, "]"

    invoke-virtual {v0, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string p2, "positionDiscontinuity"

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p3

    invoke-virtual {p0, p1, p2, p3}, Landroidx/media3/exoplayer/util/a;->G0(Lj2/c$a;Ljava/lang/String;Ljava/lang/String;)V

    return-void
.end method

.method public final G0(Lj2/c$a;Ljava/lang/String;Ljava/lang/String;)V
    .locals 1

    const/4 v0, 0x0

    invoke-virtual {p0, p1, p2, p3, v0}, Landroidx/media3/exoplayer/util/a;->j0(Lj2/c$a;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Throwable;)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1}, Landroidx/media3/exoplayer/util/a;->H0(Ljava/lang/String;)V

    return-void
.end method

.method public H(Lj2/c$a;Z)V
    .locals 1

    const-string v0, "skipSilenceEnabled"

    invoke-static {p2}, Ljava/lang/Boolean;->toString(Z)Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p0, p1, v0, p2}, Landroidx/media3/exoplayer/util/a;->G0(Lj2/c$a;Ljava/lang/String;Ljava/lang/String;)V

    return-void
.end method

.method public H0(Ljava/lang/String;)V
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/util/a;->a:Ljava/lang/String;

    invoke-static {v0, p1}, Le2/o;->b(Ljava/lang/String;Ljava/lang/String;)V

    return-void
.end method

.method public synthetic I(Lj2/c$a;Ljava/util/List;)V
    .locals 0

    invoke-static {p0, p1, p2}, Lj2/b;->p(Lj2/c;Lj2/c$a;Ljava/util/List;)V

    return-void
.end method

.method public final I0(Lj2/c$a;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Throwable;)V
    .locals 0
    .param p4    # Ljava/lang/Throwable;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    invoke-virtual {p0, p1, p2, p3, p4}, Landroidx/media3/exoplayer/util/a;->j0(Lj2/c$a;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Throwable;)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1}, Landroidx/media3/exoplayer/util/a;->K0(Ljava/lang/String;)V

    return-void
.end method

.method public J(Lj2/c$a;Landroidx/media3/common/PlaybackException;)V
    .locals 1

    const-string v0, "playerFailed"

    invoke-virtual {p0, p1, v0, p2}, Landroidx/media3/exoplayer/util/a;->J0(Lj2/c$a;Ljava/lang/String;Ljava/lang/Throwable;)V

    return-void
.end method

.method public final J0(Lj2/c$a;Ljava/lang/String;Ljava/lang/Throwable;)V
    .locals 1
    .param p3    # Ljava/lang/Throwable;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    const/4 v0, 0x0

    invoke-virtual {p0, p1, p2, v0, p3}, Landroidx/media3/exoplayer/util/a;->j0(Lj2/c$a;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Throwable;)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1}, Landroidx/media3/exoplayer/util/a;->K0(Ljava/lang/String;)V

    return-void
.end method

.method public K(Lj2/c$a;Lu2/n;Lu2/o;)V
    .locals 0

    return-void
.end method

.method public K0(Ljava/lang/String;)V
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/util/a;->a:Ljava/lang/String;

    invoke-static {v0, p1}, Le2/o;->c(Ljava/lang/String;Ljava/lang/String;)V

    return-void
.end method

.method public synthetic L(Lj2/c$a;Landroidx/media3/common/y;)V
    .locals 0

    invoke-static {p0, p1, p2}, Lj2/b;->i0(Lj2/c;Lj2/c$a;Landroidx/media3/common/y;)V

    return-void
.end method

.method public final L0(Lj2/c$a;Ljava/lang/String;Ljava/lang/Exception;)V
    .locals 1

    const-string v0, "internalError"

    invoke-virtual {p0, p1, v0, p2, p3}, Landroidx/media3/exoplayer/util/a;->I0(Lj2/c$a;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Throwable;)V

    return-void
.end method

.method public synthetic M(Lj2/c$a;Landroidx/media3/common/d0;)V
    .locals 0

    invoke-static {p0, p1, p2}, Lj2/b;->I(Lj2/c;Lj2/c$a;Landroidx/media3/common/d0;)V

    return-void
.end method

.method public final M0(Landroidx/media3/common/Metadata;Ljava/lang/String;)V
    .locals 3

    const/4 v0, 0x0

    :goto_0
    invoke-virtual {p1}, Landroidx/media3/common/Metadata;->g()I

    move-result v1

    if-ge v0, v1, :cond_0

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p1, v0}, Landroidx/media3/common/Metadata;->e(I)Landroidx/media3/common/Metadata$Entry;

    move-result-object v2

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p0, v1}, Landroidx/media3/exoplayer/util/a;->H0(Ljava/lang/String;)V

    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :cond_0
    return-void
.end method

.method public N(Lj2/c$a;I)V
    .locals 8

    iget-object v0, p1, Lj2/c$a;->b:Landroidx/media3/common/m0;

    invoke-virtual {v0}, Landroidx/media3/common/m0;->i()I

    move-result v0

    iget-object v1, p1, Lj2/c$a;->b:Landroidx/media3/common/m0;

    invoke-virtual {v1}, Landroidx/media3/common/m0;->p()I

    move-result v1

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "timeline ["

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p0, p1}, Landroidx/media3/exoplayer/util/a;->o0(Lj2/c$a;)Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v3, ", periodCount="

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v3, ", windowCount="

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v3, ", reason="

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-static {p2}, Landroidx/media3/exoplayer/util/a;->D0(I)Ljava/lang/String;

    move-result-object p2

    invoke-virtual {v2, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p0, p2}, Landroidx/media3/exoplayer/util/a;->H0(Ljava/lang/String;)V

    const/4 p2, 0x0

    const/4 v2, 0x0

    :goto_0
    const/4 v3, 0x3

    invoke-static {v0, v3}, Ljava/lang/Math;->min(II)I

    move-result v4

    const-string v5, "]"

    if-ge v2, v4, :cond_0

    iget-object v3, p1, Lj2/c$a;->b:Landroidx/media3/common/m0;

    iget-object v4, p0, Landroidx/media3/exoplayer/util/a;->c:Landroidx/media3/common/m0$b;

    invoke-virtual {v3, v2, v4}, Landroidx/media3/common/m0;->f(ILandroidx/media3/common/m0$b;)Landroidx/media3/common/m0$b;

    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v4, "  period ["

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v4, p0, Landroidx/media3/exoplayer/util/a;->c:Landroidx/media3/common/m0$b;

    invoke-virtual {v4}, Landroidx/media3/common/m0$b;->j()J

    move-result-wide v6

    invoke-static {v6, v7}, Landroidx/media3/exoplayer/util/a;->C0(J)Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {p0, v3}, Landroidx/media3/exoplayer/util/a;->H0(Ljava/lang/String;)V

    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_0
    const-string v2, "  ..."

    if-le v0, v3, :cond_1

    invoke-virtual {p0, v2}, Landroidx/media3/exoplayer/util/a;->H0(Ljava/lang/String;)V

    :cond_1
    :goto_1
    invoke-static {v1, v3}, Ljava/lang/Math;->min(II)I

    move-result v0

    if-ge p2, v0, :cond_2

    iget-object v0, p1, Lj2/c$a;->b:Landroidx/media3/common/m0;

    iget-object v4, p0, Landroidx/media3/exoplayer/util/a;->b:Landroidx/media3/common/m0$c;

    invoke-virtual {v0, p2, v4}, Landroidx/media3/common/m0;->n(ILandroidx/media3/common/m0$c;)Landroidx/media3/common/m0$c;

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v4, "  window ["

    invoke-virtual {v0, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v4, p0, Landroidx/media3/exoplayer/util/a;->b:Landroidx/media3/common/m0$c;

    invoke-virtual {v4}, Landroidx/media3/common/m0$c;->d()J

    move-result-wide v6

    invoke-static {v6, v7}, Landroidx/media3/exoplayer/util/a;->C0(J)Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v0, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v4, ", seekable="

    invoke-virtual {v0, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v4, p0, Landroidx/media3/exoplayer/util/a;->b:Landroidx/media3/common/m0$c;

    iget-boolean v4, v4, Landroidx/media3/common/m0$c;->h:Z

    invoke-virtual {v0, v4}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    const-string v4, ", dynamic="

    invoke-virtual {v0, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v4, p0, Landroidx/media3/exoplayer/util/a;->b:Landroidx/media3/common/m0$c;

    iget-boolean v4, v4, Landroidx/media3/common/m0$c;->i:Z

    invoke-virtual {v0, v4}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    invoke-virtual {v0, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p0, v0}, Landroidx/media3/exoplayer/util/a;->H0(Ljava/lang/String;)V

    add-int/lit8 p2, p2, 0x1

    goto :goto_1

    :cond_2
    if-le v1, v3, :cond_3

    invoke-virtual {p0, v2}, Landroidx/media3/exoplayer/util/a;->H0(Ljava/lang/String;)V

    :cond_3
    invoke-virtual {p0, v5}, Landroidx/media3/exoplayer/util/a;->H0(Ljava/lang/String;)V

    return-void
.end method

.method public O(Lj2/c$a;Landroidx/media3/exoplayer/n;)V
    .locals 0

    const-string p2, "videoEnabled"

    invoke-virtual {p0, p1, p2}, Landroidx/media3/exoplayer/util/a;->F0(Lj2/c$a;Ljava/lang/String;)V

    return-void
.end method

.method public synthetic P(Lj2/c$a;)V
    .locals 0

    invoke-static {p0, p1}, Lj2/b;->U(Lj2/c;Lj2/c$a;)V

    return-void
.end method

.method public synthetic Q(Lj2/c$a;IZ)V
    .locals 0

    invoke-static {p0, p1, p2, p3}, Lj2/b;->r(Lj2/c;Lj2/c$a;IZ)V

    return-void
.end method

.method public synthetic R(Lj2/c$a;Landroidx/media3/common/p0;)V
    .locals 0

    invoke-static {p0, p1, p2}, Lj2/b;->Z(Lj2/c;Lj2/c$a;Landroidx/media3/common/p0;)V

    return-void
.end method

.method public S(Lj2/c$a;I)V
    .locals 1

    const-string v0, "repeatMode"

    invoke-static {p2}, Landroidx/media3/exoplayer/util/a;->A0(I)Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p0, p1, v0, p2}, Landroidx/media3/exoplayer/util/a;->G0(Lj2/c$a;Ljava/lang/String;Ljava/lang/String;)V

    return-void
.end method

.method public U(Lj2/c$a;Landroidx/media3/exoplayer/audio/AudioSink$a;)V
    .locals 1

    const-string v0, "audioTrackReleased"

    invoke-static {p2}, Landroidx/media3/exoplayer/util/a;->v(Landroidx/media3/exoplayer/audio/AudioSink$a;)Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p0, p1, v0, p2}, Landroidx/media3/exoplayer/util/a;->G0(Lj2/c$a;Ljava/lang/String;Ljava/lang/String;)V

    return-void
.end method

.method public V(Lj2/c$a;Ljava/lang/Object;J)V
    .locals 0

    invoke-static {p2}, Ljava/lang/String;->valueOf(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p2

    const-string p3, "renderedFirstFrame"

    invoke-virtual {p0, p1, p3, p2}, Landroidx/media3/exoplayer/util/a;->G0(Lj2/c$a;Ljava/lang/String;Ljava/lang/String;)V

    return-void
.end method

.method public synthetic W(Lj2/c$a;Ld2/b;)V
    .locals 0

    invoke-static {p0, p1, p2}, Lj2/b;->o(Lj2/c;Lj2/c$a;Ld2/b;)V

    return-void
.end method

.method public synthetic X(Lj2/c$a;Ljava/lang/Exception;)V
    .locals 0

    invoke-static {p0, p1, p2}, Lj2/b;->c0(Lj2/c;Lj2/c$a;Ljava/lang/Exception;)V

    return-void
.end method

.method public Y(Lj2/c$a;I)V
    .locals 1

    const-string v0, "state"

    invoke-static {p2}, Landroidx/media3/exoplayer/util/a;->B0(I)Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p0, p1, v0, p2}, Landroidx/media3/exoplayer/util/a;->G0(Lj2/c$a;Ljava/lang/String;Ljava/lang/String;)V

    return-void
.end method

.method public Z(Lj2/c$a;Landroidx/media3/common/g0;)V
    .locals 1

    const-string v0, "playbackParameters"

    invoke-virtual {p2}, Landroidx/media3/common/g0;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p0, p1, v0, p2}, Landroidx/media3/exoplayer/util/a;->G0(Lj2/c$a;Ljava/lang/String;Ljava/lang/String;)V

    return-void
.end method

.method public a(Lj2/c$a;Ljava/lang/String;JJ)V
    .locals 0

    const-string p3, "videoDecoderInitialized"

    invoke-virtual {p0, p1, p3, p2}, Landroidx/media3/exoplayer/util/a;->G0(Lj2/c$a;Ljava/lang/String;Ljava/lang/String;)V

    return-void
.end method

.method public a0(Lj2/c$a;Ljava/lang/String;)V
    .locals 1

    const-string v0, "audioDecoderReleased"

    invoke-virtual {p0, p1, v0, p2}, Landroidx/media3/exoplayer/util/a;->G0(Lj2/c$a;Ljava/lang/String;Ljava/lang/String;)V

    return-void
.end method

.method public b(Lj2/c$a;Landroidx/media3/common/q0;)V
    .locals 8

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "tracks ["

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p0, p1}, Landroidx/media3/exoplayer/util/a;->o0(Lj2/c$a;)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1}, Landroidx/media3/exoplayer/util/a;->H0(Ljava/lang/String;)V

    invoke-virtual {p2}, Landroidx/media3/common/q0;->a()Lcom/google/common/collect/ImmutableList;

    move-result-object p1

    const/4 p2, 0x0

    const/4 v0, 0x0

    :goto_0
    invoke-virtual {p1}, Ljava/util/AbstractCollection;->size()I

    move-result v1

    const-string v2, "  ]"

    const-string v3, "    "

    if-ge v0, v1, :cond_1

    invoke-interface {p1, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Landroidx/media3/common/q0$a;

    const-string v4, "  group ["

    invoke-virtual {p0, v4}, Landroidx/media3/exoplayer/util/a;->H0(Ljava/lang/String;)V

    const/4 v4, 0x0

    :goto_1
    iget v5, v1, Landroidx/media3/common/q0$a;->a:I

    if-ge v4, v5, :cond_0

    invoke-virtual {v1, v4}, Landroidx/media3/common/q0$a;->h(I)Z

    move-result v5

    invoke-static {v5}, Landroidx/media3/exoplayer/util/a;->E0(Z)Ljava/lang/String;

    move-result-object v5

    invoke-virtual {v1, v4}, Landroidx/media3/common/q0$a;->c(I)I

    move-result v6

    invoke-static {v6}, Le2/u0;->d0(I)Ljava/lang/String;

    move-result-object v6

    new-instance v7, Ljava/lang/StringBuilder;

    invoke-direct {v7}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v7, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v7, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v5, " Track:"

    invoke-virtual {v7, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v7, v4}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v5, ", "

    invoke-virtual {v7, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1, v4}, Landroidx/media3/common/q0$a;->b(I)Landroidx/media3/common/y;

    move-result-object v5

    invoke-static {v5}, Landroidx/media3/common/y;->m(Landroidx/media3/common/y;)Ljava/lang/String;

    move-result-object v5

    invoke-virtual {v7, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v5, ", supported="

    invoke-virtual {v7, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v7, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v7}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v5

    invoke-virtual {p0, v5}, Landroidx/media3/exoplayer/util/a;->H0(Ljava/lang/String;)V

    add-int/lit8 v4, v4, 0x1

    goto :goto_1

    :cond_0
    invoke-virtual {p0, v2}, Landroidx/media3/exoplayer/util/a;->H0(Ljava/lang/String;)V

    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :cond_1
    const/4 v0, 0x0

    const/4 v1, 0x0

    :goto_2
    if-nez v0, :cond_4

    invoke-virtual {p1}, Ljava/util/AbstractCollection;->size()I

    move-result v4

    if-ge v1, v4, :cond_4

    invoke-interface {p1, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Landroidx/media3/common/q0$a;

    const/4 v5, 0x0

    :goto_3
    if-nez v0, :cond_3

    iget v6, v4, Landroidx/media3/common/q0$a;->a:I

    if-ge v5, v6, :cond_3

    invoke-virtual {v4, v5}, Landroidx/media3/common/q0$a;->h(I)Z

    move-result v6

    if-eqz v6, :cond_2

    invoke-virtual {v4, v5}, Landroidx/media3/common/q0$a;->b(I)Landroidx/media3/common/y;

    move-result-object v6

    iget-object v6, v6, Landroidx/media3/common/y;->k:Landroidx/media3/common/Metadata;

    if-eqz v6, :cond_2

    invoke-virtual {v6}, Landroidx/media3/common/Metadata;->g()I

    move-result v7

    if-lez v7, :cond_2

    const-string v0, "  Metadata ["

    invoke-virtual {p0, v0}, Landroidx/media3/exoplayer/util/a;->H0(Ljava/lang/String;)V

    invoke-virtual {p0, v6, v3}, Landroidx/media3/exoplayer/util/a;->M0(Landroidx/media3/common/Metadata;Ljava/lang/String;)V

    invoke-virtual {p0, v2}, Landroidx/media3/exoplayer/util/a;->H0(Ljava/lang/String;)V

    const/4 v0, 0x1

    :cond_2
    add-int/lit8 v5, v5, 0x1

    goto :goto_3

    :cond_3
    add-int/lit8 v1, v1, 0x1

    goto :goto_2

    :cond_4
    const-string p1, "]"

    invoke-virtual {p0, p1}, Landroidx/media3/exoplayer/util/a;->H0(Ljava/lang/String;)V

    return-void
.end method

.method public b0(Lj2/c$a;II)V
    .locals 1

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v0, p2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string p2, ", "

    invoke-virtual {v0, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0, p3}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    const-string p3, "surfaceSize"

    invoke-virtual {p0, p1, p3, p2}, Landroidx/media3/exoplayer/util/a;->G0(Lj2/c$a;Ljava/lang/String;Ljava/lang/String;)V

    return-void
.end method

.method public c(Lj2/c$a;Landroidx/media3/common/y;Landroidx/media3/exoplayer/o;)V
    .locals 0
    .param p3    # Landroidx/media3/exoplayer/o;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    const-string p3, "audioInputFormat"

    invoke-static {p2}, Landroidx/media3/common/y;->m(Landroidx/media3/common/y;)Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p0, p1, p3, p2}, Landroidx/media3/exoplayer/util/a;->G0(Lj2/c$a;Ljava/lang/String;Ljava/lang/String;)V

    return-void
.end method

.method public c0(Lj2/c$a;Z)V
    .locals 1

    const-string v0, "shuffleModeEnabled"

    invoke-static {p2}, Ljava/lang/Boolean;->toString(Z)Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p0, p1, v0, p2}, Landroidx/media3/exoplayer/util/a;->G0(Lj2/c$a;Ljava/lang/String;Ljava/lang/String;)V

    return-void
.end method

.method public synthetic d(Lj2/c$a;Landroidx/media3/common/h0$b;)V
    .locals 0

    invoke-static {p0, p1, p2}, Lj2/b;->n(Lj2/c;Lj2/c$a;Landroidx/media3/common/h0$b;)V

    return-void
.end method

.method public synthetic d0(Lj2/c$a;Landroidx/media3/common/o;)V
    .locals 0

    invoke-static {p0, p1, p2}, Lj2/b;->q(Lj2/c;Lj2/c$a;Landroidx/media3/common/o;)V

    return-void
.end method

.method public e(Lj2/c$a;Z)V
    .locals 1

    const-string v0, "isPlaying"

    invoke-static {p2}, Ljava/lang/Boolean;->toString(Z)Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p0, p1, v0, p2}, Landroidx/media3/exoplayer/util/a;->G0(Lj2/c$a;Ljava/lang/String;Ljava/lang/String;)V

    return-void
.end method

.method public e0(Lj2/c$a;F)V
    .locals 1

    const-string v0, "volume"

    invoke-static {p2}, Ljava/lang/Float;->toString(F)Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p0, p1, v0, p2}, Landroidx/media3/exoplayer/util/a;->G0(Lj2/c$a;Ljava/lang/String;Ljava/lang/String;)V

    return-void
.end method

.method public synthetic f(Lj2/c$a;ZI)V
    .locals 0

    invoke-static {p0, p1, p2, p3}, Lj2/b;->Q(Lj2/c;Lj2/c$a;ZI)V

    return-void
.end method

.method public f0(Lj2/c$a;I)V
    .locals 1

    const-string v0, "playbackSuppressionReason"

    invoke-static {p2}, Landroidx/media3/exoplayer/util/a;->z0(I)Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p0, p1, v0, p2}, Landroidx/media3/exoplayer/util/a;->G0(Lj2/c$a;Ljava/lang/String;Ljava/lang/String;)V

    return-void
.end method

.method public synthetic g(Lj2/c$a;Ljava/lang/String;J)V
    .locals 0

    invoke-static {p0, p1, p2, p3, p4}, Lj2/b;->b(Lj2/c;Lj2/c$a;Ljava/lang/String;J)V

    return-void
.end method

.method public synthetic g0(Lj2/c$a;Landroidx/media3/common/y;)V
    .locals 0

    invoke-static {p0, p1, p2}, Lj2/b;->g(Lj2/c;Lj2/c$a;Landroidx/media3/common/y;)V

    return-void
.end method

.method public h(Lj2/c$a;Landroidx/media3/common/t0;)V
    .locals 2

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    iget v1, p2, Landroidx/media3/common/t0;->a:I

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v1, ", "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget p2, p2, Landroidx/media3/common/t0;->b:I

    invoke-virtual {v0, p2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    const-string v0, "videoSize"

    invoke-virtual {p0, p1, v0, p2}, Landroidx/media3/exoplayer/util/a;->G0(Lj2/c$a;Ljava/lang/String;Ljava/lang/String;)V

    return-void
.end method

.method public synthetic h0(Lj2/c$a;Z)V
    .locals 0

    invoke-static {p0, p1, p2}, Lj2/b;->G(Lj2/c;Lj2/c$a;Z)V

    return-void
.end method

.method public i(Lj2/c$a;Landroidx/media3/common/y;Landroidx/media3/exoplayer/o;)V
    .locals 0
    .param p3    # Landroidx/media3/exoplayer/o;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    const-string p3, "videoInputFormat"

    invoke-static {p2}, Landroidx/media3/common/y;->m(Landroidx/media3/common/y;)Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p0, p1, p3, p2}, Landroidx/media3/exoplayer/util/a;->G0(Lj2/c$a;Ljava/lang/String;Ljava/lang/String;)V

    return-void
.end method

.method public i0(Lj2/c$a;Lu2/n;Lu2/o;)V
    .locals 0

    return-void
.end method

.method public j(Lj2/c$a;Ljava/lang/Exception;)V
    .locals 1

    const-string v0, "drmSessionManagerError"

    invoke-virtual {p0, p1, v0, p2}, Landroidx/media3/exoplayer/util/a;->L0(Lj2/c$a;Ljava/lang/String;Ljava/lang/Exception;)V

    return-void
.end method

.method public final j0(Lj2/c$a;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Throwable;)Ljava/lang/String;
    .locals 1
    .param p3    # Ljava/lang/String;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .param p4    # Ljava/lang/Throwable;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v0, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string p2, " ["

    invoke-virtual {v0, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p0, p1}, Landroidx/media3/exoplayer/util/a;->o0(Lj2/c$a;)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    instance-of p2, p4, Landroidx/media3/common/PlaybackException;

    if-eqz p2, :cond_0

    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {p2, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string p1, ", errorCode="

    invoke-virtual {p2, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-object p1, p4

    check-cast p1, Landroidx/media3/common/PlaybackException;

    invoke-virtual {p1}, Landroidx/media3/common/PlaybackException;->getErrorCodeName()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p2, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    :cond_0
    if-eqz p3, :cond_1

    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {p2, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string p1, ", "

    invoke-virtual {p2, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p2, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    :cond_1
    invoke-static {p4}, Le2/o;->e(Ljava/lang/Throwable;)Ljava/lang/String;

    move-result-object p2

    invoke-static {p2}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result p3

    if-nez p3, :cond_2

    new-instance p3, Ljava/lang/StringBuilder;

    invoke-direct {p3}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {p3, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string p1, "\n  "

    invoke-virtual {p3, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string p4, "\n"

    invoke-virtual {p2, p4, p1}, Ljava/lang/String;->replace(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p3, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const/16 p1, 0xa

    invoke-virtual {p3, p1}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    invoke-virtual {p3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    :cond_2
    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {p2, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string p1, "]"

    invoke-virtual {p2, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method public k(Lj2/c$a;Ljava/lang/String;JJ)V
    .locals 0

    const-string p3, "audioDecoderInitialized"

    invoke-virtual {p0, p1, p3, p2}, Landroidx/media3/exoplayer/util/a;->G0(Lj2/c$a;Ljava/lang/String;Ljava/lang/String;)V

    return-void
.end method

.method public k0(Lj2/c$a;Landroidx/media3/exoplayer/audio/AudioSink$a;)V
    .locals 1

    const-string v0, "audioTrackInit"

    invoke-static {p2}, Landroidx/media3/exoplayer/util/a;->v(Landroidx/media3/exoplayer/audio/AudioSink$a;)Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p0, p1, v0, p2}, Landroidx/media3/exoplayer/util/a;->G0(Lj2/c$a;Ljava/lang/String;Ljava/lang/String;)V

    return-void
.end method

.method public synthetic l(Lj2/c$a;Ljava/lang/Exception;)V
    .locals 0

    invoke-static {p0, p1, p2}, Lj2/b;->j(Lj2/c;Lj2/c$a;Ljava/lang/Exception;)V

    return-void
.end method

.method public l0(Lj2/c$a;Ljava/lang/String;)V
    .locals 1

    const-string v0, "videoDecoderReleased"

    invoke-virtual {p0, p1, v0, p2}, Landroidx/media3/exoplayer/util/a;->G0(Lj2/c$a;Ljava/lang/String;Ljava/lang/String;)V

    return-void
.end method

.method public synthetic m(Lj2/c$a;JI)V
    .locals 0

    invoke-static {p0, p1, p2, p3, p4}, Lj2/b;->h0(Lj2/c;Lj2/c$a;JI)V

    return-void
.end method

.method public m0(Lj2/c$a;Z)V
    .locals 1

    const-string v0, "loading"

    invoke-static {p2}, Ljava/lang/Boolean;->toString(Z)Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p0, p1, v0, p2}, Landroidx/media3/exoplayer/util/a;->G0(Lj2/c$a;Ljava/lang/String;Ljava/lang/String;)V

    return-void
.end method

.method public n(Lj2/c$a;Landroidx/media3/common/b0;I)V
    .locals 1
    .param p2    # Landroidx/media3/common/b0;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v0, "mediaItem ["

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p0, p1}, Landroidx/media3/exoplayer/util/a;->o0(Lj2/c$a;)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p2, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string p1, ", reason="

    invoke-virtual {p2, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-static {p3}, Landroidx/media3/exoplayer/util/a;->x0(I)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p2, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string p1, "]"

    invoke-virtual {p2, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1}, Landroidx/media3/exoplayer/util/a;->H0(Ljava/lang/String;)V

    return-void
.end method

.method public n0(Lj2/c$a;Landroidx/media3/exoplayer/n;)V
    .locals 0

    const-string p2, "audioEnabled"

    invoke-virtual {p0, p1, p2}, Landroidx/media3/exoplayer/util/a;->F0(Lj2/c$a;Ljava/lang/String;)V

    return-void
.end method

.method public o(Lj2/c$a;I)V
    .locals 2

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "state="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0, p2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    const-string v0, "drmSessionAcquired"

    invoke-virtual {p0, p1, v0, p2}, Landroidx/media3/exoplayer/util/a;->G0(Lj2/c$a;Ljava/lang/String;Ljava/lang/String;)V

    return-void
.end method

.method public final o0(Lj2/c$a;)Ljava/lang/String;
    .locals 6

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "window="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget v1, p1, Lj2/c$a;->c:I

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    iget-object v1, p1, Lj2/c$a;->d:Landroidx/media3/exoplayer/source/l$b;

    if-eqz v1, :cond_0

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v0, ", period="

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v0, p1, Lj2/c$a;->b:Landroidx/media3/common/m0;

    iget-object v2, p1, Lj2/c$a;->d:Landroidx/media3/exoplayer/source/l$b;

    iget-object v2, v2, Landroidx/media3/exoplayer/source/l$b;->a:Ljava/lang/Object;

    invoke-virtual {v0, v2}, Landroidx/media3/common/m0;->b(Ljava/lang/Object;)I

    move-result v0

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    iget-object v1, p1, Lj2/c$a;->d:Landroidx/media3/exoplayer/source/l$b;

    invoke-virtual {v1}, Landroidx/media3/exoplayer/source/l$b;->b()Z

    move-result v1

    if-eqz v1, :cond_0

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v0, ", adGroup="

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v0, p1, Lj2/c$a;->d:Landroidx/media3/exoplayer/source/l$b;

    iget v0, v0, Landroidx/media3/exoplayer/source/l$b;->b:I

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v0, ", ad="

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v0, p1, Lj2/c$a;->d:Landroidx/media3/exoplayer/source/l$b;

    iget v0, v0, Landroidx/media3/exoplayer/source/l$b;->c:I

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    :cond_0
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "eventTime="

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-wide v2, p1, Lj2/c$a;->a:J

    iget-wide v4, p0, Landroidx/media3/exoplayer/util/a;->d:J

    sub-long/2addr v2, v4

    invoke-static {v2, v3}, Landroidx/media3/exoplayer/util/a;->C0(J)Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v2, ", mediaPos="

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-wide v2, p1, Lj2/c$a;->e:J

    invoke-static {v2, v3}, Landroidx/media3/exoplayer/util/a;->C0(J)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string p1, ", "

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method public synthetic p(Lj2/c$a;)V
    .locals 0

    invoke-static {p0, p1}, Lj2/b;->P(Lj2/c;Lj2/c$a;)V

    return-void
.end method

.method public synthetic p0(Lj2/c$a;)V
    .locals 0

    invoke-static {p0, p1}, Lj2/b;->v(Lj2/c;Lj2/c$a;)V

    return-void
.end method

.method public q(Lj2/c$a;Lu2/o;)V
    .locals 1

    iget-object p2, p2, Lu2/o;->c:Landroidx/media3/common/y;

    invoke-static {p2}, Landroidx/media3/common/y;->m(Landroidx/media3/common/y;)Ljava/lang/String;

    move-result-object p2

    const-string v0, "downstreamFormat"

    invoke-virtual {p0, p1, v0, p2}, Landroidx/media3/exoplayer/util/a;->G0(Lj2/c$a;Ljava/lang/String;Ljava/lang/String;)V

    return-void
.end method

.method public synthetic q0(Lj2/c$a;Ljava/lang/String;J)V
    .locals 0

    invoke-static {p0, p1, p2, p3, p4}, Lj2/b;->d0(Lj2/c;Lj2/c$a;Ljava/lang/String;J)V

    return-void
.end method

.method public r(Lj2/c$a;Lu2/n;Lu2/o;)V
    .locals 0

    return-void
.end method

.method public r0(Lj2/c$a;Lu2/o;)V
    .locals 1

    iget-object p2, p2, Lu2/o;->c:Landroidx/media3/common/y;

    invoke-static {p2}, Landroidx/media3/common/y;->m(Landroidx/media3/common/y;)Ljava/lang/String;

    move-result-object p2

    const-string v0, "upstreamDiscarded"

    invoke-virtual {p0, p1, v0, p2}, Landroidx/media3/exoplayer/util/a;->G0(Lj2/c$a;Ljava/lang/String;Ljava/lang/String;)V

    return-void
.end method

.method public s(Lj2/c$a;)V
    .locals 1

    const-string v0, "drmKeysLoaded"

    invoke-virtual {p0, p1, v0}, Landroidx/media3/exoplayer/util/a;->F0(Lj2/c$a;Ljava/lang/String;)V

    return-void
.end method

.method public s0(Lj2/c$a;Landroidx/media3/common/Metadata;)V
    .locals 2

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "metadata ["

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p0, p1}, Landroidx/media3/exoplayer/util/a;->o0(Lj2/c$a;)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1}, Landroidx/media3/exoplayer/util/a;->H0(Ljava/lang/String;)V

    const-string p1, "  "

    invoke-virtual {p0, p2, p1}, Landroidx/media3/exoplayer/util/a;->M0(Landroidx/media3/common/Metadata;Ljava/lang/String;)V

    const-string p1, "]"

    invoke-virtual {p0, p1}, Landroidx/media3/exoplayer/util/a;->H0(Ljava/lang/String;)V

    return-void
.end method

.method public t(Lj2/c$a;Lu2/n;Lu2/o;Ljava/io/IOException;Z)V
    .locals 0

    const-string p2, "loadError"

    invoke-virtual {p0, p1, p2, p4}, Landroidx/media3/exoplayer/util/a;->L0(Lj2/c$a;Ljava/lang/String;Ljava/lang/Exception;)V

    return-void
.end method

.method public synthetic t0(Lj2/c$a;Landroidx/media3/common/PlaybackException;)V
    .locals 0

    invoke-static {p0, p1, p2}, Lj2/b;->O(Lj2/c;Lj2/c$a;Landroidx/media3/common/PlaybackException;)V

    return-void
.end method

.method public synthetic u(Lj2/c$a;J)V
    .locals 0

    invoke-static {p0, p1, p2, p3}, Lj2/b;->i(Lj2/c;Lj2/c$a;J)V

    return-void
.end method

.method public synthetic u0(Lj2/c$a;Ljava/lang/Exception;)V
    .locals 0

    invoke-static {p0, p1, p2}, Lj2/b;->a(Lj2/c;Lj2/c$a;Ljava/lang/Exception;)V

    return-void
.end method

.method public v0(Lj2/c$a;Landroidx/media3/exoplayer/n;)V
    .locals 0

    const-string p2, "videoDisabled"

    invoke-virtual {p0, p1, p2}, Landroidx/media3/exoplayer/util/a;->F0(Lj2/c$a;Ljava/lang/String;)V

    return-void
.end method

.method public w(Lj2/c$a;IJJ)V
    .locals 0

    return-void
.end method

.method public w0(Lj2/c$a;)V
    .locals 1

    const-string v0, "drmKeysRemoved"

    invoke-virtual {p0, p1, v0}, Landroidx/media3/exoplayer/util/a;->F0(Lj2/c$a;Ljava/lang/String;)V

    return-void
.end method

.method public synthetic x(Landroidx/media3/common/h0;Lj2/c$b;)V
    .locals 0

    invoke-static {p0, p1, p2}, Lj2/b;->A(Lj2/c;Landroidx/media3/common/h0;Lj2/c$b;)V

    return-void
.end method

.method public synthetic y(Lj2/c$a;I)V
    .locals 0

    invoke-static {p0, p1, p2}, Lj2/b;->R(Lj2/c;Lj2/c$a;I)V

    return-void
.end method

.method public synthetic z(Lj2/c$a;IIIF)V
    .locals 0

    invoke-static/range {p0 .. p5}, Lj2/b;->k0(Lj2/c;Lj2/c$a;IIIF)V

    return-void
.end method
