.class public final synthetic Landroidx/media3/exoplayer/v0;
.super Ljava/lang/Object;

# interfaces
.implements Le2/n$a;


# instance fields
.field public final synthetic a:Landroidx/media3/exoplayer/s2;

.field public final synthetic b:I


# direct methods
.method public synthetic constructor <init>(Landroidx/media3/exoplayer/s2;I)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Landroidx/media3/exoplayer/v0;->a:Landroidx/media3/exoplayer/s2;

    iput p2, p0, Landroidx/media3/exoplayer/v0;->b:I

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)V
    .locals 2

    iget-object v0, p0, Landroidx/media3/exoplayer/v0;->a:Landroidx/media3/exoplayer/s2;

    iget v1, p0, Landroidx/media3/exoplayer/v0;->b:I

    check-cast p1, Landroidx/media3/common/h0$d;

    invoke-static {v0, v1, p1}, Landroidx/media3/exoplayer/c1;->j0(Landroidx/media3/exoplayer/s2;ILandroidx/media3/common/h0$d;)V

    return-void
.end method
