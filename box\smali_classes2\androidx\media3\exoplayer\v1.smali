.class public interface abstract Landroidx/media3/exoplayer/v1;
.super Ljava/lang/Object;


# virtual methods
.method public abstract a(JJF)Z
.end method

.method public abstract b(Landroidx/media3/common/m0;Landroidx/media3/exoplayer/source/l$b;[Landroidx/media3/exoplayer/w2;Lu2/k0;[Lx2/z;)V
.end method

.method public abstract c(Landroidx/media3/common/m0;Landroidx/media3/exoplayer/source/l$b;JFZJ)Z
.end method

.method public abstract getAllocator()Landroidx/media3/exoplayer/upstream/b;
.end method

.method public abstract getBackBufferDurationUs()J
.end method

.method public abstract onPrepared()V
.end method

.method public abstract onReleased()V
.end method

.method public abstract onStopped()V
.end method

.method public abstract retainBackBufferFromKeyframe()Z
.end method
