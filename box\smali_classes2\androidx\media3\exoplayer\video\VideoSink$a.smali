.class public interface abstract Landroidx/media3/exoplayer/video/VideoSink$a;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/media3/exoplayer/video/VideoSink;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "a"
.end annotation


# static fields
.field public static final a:Landroidx/media3/exoplayer/video/VideoSink$a;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    new-instance v0, Landroidx/media3/exoplayer/video/VideoSink$a$a;

    invoke-direct {v0}, Landroidx/media3/exoplayer/video/VideoSink$a$a;-><init>()V

    sput-object v0, Landroidx/media3/exoplayer/video/VideoSink$a;->a:Landroidx/media3/exoplayer/video/VideoSink$a;

    return-void
.end method


# virtual methods
.method public abstract a(Landroidx/media3/exoplayer/video/VideoSink;)V
.end method

.method public abstract b(Landroidx/media3/exoplayer/video/VideoSink;Landroidx/media3/common/t0;)V
.end method

.method public abstract c(Landroidx/media3/exoplayer/video/VideoSink;)V
.end method
