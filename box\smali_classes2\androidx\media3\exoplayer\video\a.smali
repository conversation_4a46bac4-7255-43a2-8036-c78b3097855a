.class public final synthetic Landroidx/media3/exoplayer/video/a;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/lang/Runnable;


# instance fields
.field public final synthetic a:Landroidx/media3/exoplayer/video/VideoSink$a;

.field public final synthetic b:Landroidx/media3/exoplayer/video/f$e;

.field public final synthetic c:Landroidx/media3/common/t0;


# direct methods
.method public synthetic constructor <init>(Landroidx/media3/exoplayer/video/VideoSink$a;Landroidx/media3/exoplayer/video/f$e;Landroidx/media3/common/t0;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Landroidx/media3/exoplayer/video/a;->a:Landroidx/media3/exoplayer/video/VideoSink$a;

    iput-object p2, p0, Landroidx/media3/exoplayer/video/a;->b:Landroidx/media3/exoplayer/video/f$e;

    iput-object p3, p0, Landroidx/media3/exoplayer/video/a;->c:Landroidx/media3/common/t0;

    return-void
.end method


# virtual methods
.method public final run()V
    .locals 3

    iget-object v0, p0, Landroidx/media3/exoplayer/video/a;->a:Landroidx/media3/exoplayer/video/VideoSink$a;

    iget-object v1, p0, Landroidx/media3/exoplayer/video/a;->b:Landroidx/media3/exoplayer/video/f$e;

    iget-object v2, p0, Landroidx/media3/exoplayer/video/a;->c:Landroidx/media3/common/t0;

    invoke-static {v0, v1, v2}, Landroidx/media3/exoplayer/video/f;->o(Landroidx/media3/exoplayer/video/VideoSink$a;Landroidx/media3/exoplayer/video/f$e;Landroidx/media3/common/t0;)V

    return-void
.end method
