.class public final synthetic Landroidx/media3/exoplayer/video/c0;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/lang/Runnable;


# instance fields
.field public final synthetic a:Landroidx/media3/exoplayer/video/f0$a;

.field public final synthetic b:Landroidx/media3/common/y;

.field public final synthetic c:Landroidx/media3/exoplayer/o;


# direct methods
.method public synthetic constructor <init>(Landroidx/media3/exoplayer/video/f0$a;Landroidx/media3/common/y;Landroidx/media3/exoplayer/o;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Landroidx/media3/exoplayer/video/c0;->a:Landroidx/media3/exoplayer/video/f0$a;

    iput-object p2, p0, Landroidx/media3/exoplayer/video/c0;->b:Landroidx/media3/common/y;

    iput-object p3, p0, Landroidx/media3/exoplayer/video/c0;->c:Landroidx/media3/exoplayer/o;

    return-void
.end method


# virtual methods
.method public final run()V
    .locals 3

    iget-object v0, p0, Landroidx/media3/exoplayer/video/c0;->a:Landroidx/media3/exoplayer/video/f0$a;

    iget-object v1, p0, Landroidx/media3/exoplayer/video/c0;->b:Landroidx/media3/common/y;

    iget-object v2, p0, Landroidx/media3/exoplayer/video/c0;->c:Landroidx/media3/exoplayer/o;

    invoke-static {v0, v1, v2}, Landroidx/media3/exoplayer/video/f0$a;->b(Landroidx/media3/exoplayer/video/f0$a;Landroidx/media3/common/y;Landroidx/media3/exoplayer/o;)V

    return-void
.end method
