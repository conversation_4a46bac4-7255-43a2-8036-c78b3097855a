.class public final synthetic Landroidx/media3/exoplayer/video/e;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/util/concurrent/Executor;


# instance fields
.field public final synthetic a:Le2/j;


# direct methods
.method public synthetic constructor <init>(Le2/j;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Landroidx/media3/exoplayer/video/e;->a:Le2/j;

    return-void
.end method


# virtual methods
.method public final execute(Ljava/lang/Runnable;)V
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/video/e;->a:Le2/j;

    invoke-interface {v0, p1}, Le2/j;->post(Ljava/lang/Runnable;)Z

    return-void
.end method
