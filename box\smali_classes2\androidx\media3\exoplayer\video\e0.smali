.class public final synthetic Landroidx/media3/exoplayer/video/e0;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/lang/Runnable;


# instance fields
.field public final synthetic a:Landroidx/media3/exoplayer/video/f0$a;

.field public final synthetic b:Ljava/lang/String;


# direct methods
.method public synthetic constructor <init>(Landroidx/media3/exoplayer/video/f0$a;Ljava/lang/String;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Landroidx/media3/exoplayer/video/e0;->a:Landroidx/media3/exoplayer/video/f0$a;

    iput-object p2, p0, Landroidx/media3/exoplayer/video/e0;->b:Ljava/lang/String;

    return-void
.end method


# virtual methods
.method public final run()V
    .locals 2

    iget-object v0, p0, Landroidx/media3/exoplayer/video/e0;->a:Landroidx/media3/exoplayer/video/f0$a;

    iget-object v1, p0, Landroidx/media3/exoplayer/video/e0;->b:Ljava/lang/String;

    invoke-static {v0, v1}, Landroidx/media3/exoplayer/video/f0$a;->g(Landroidx/media3/exoplayer/video/f0$a;Ljava/lang/String;)V

    return-void
.end method
