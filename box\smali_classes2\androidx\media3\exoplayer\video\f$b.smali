.class public final Landroidx/media3/exoplayer/video/f$b;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/media3/exoplayer/video/f;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "b"
.end annotation


# instance fields
.field public final a:Landroid/content/Context;

.field public b:Landroidx/media3/common/r0$a;

.field public c:Landroidx/media3/common/j0$a;

.field public d:Z


# direct methods
.method public constructor <init>(Landroid/content/Context;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Landroidx/media3/exoplayer/video/f$b;->a:Landroid/content/Context;

    return-void
.end method

.method public static synthetic a(Landroidx/media3/exoplayer/video/f$b;)Landroid/content/Context;
    .locals 0

    iget-object p0, p0, Landroidx/media3/exoplayer/video/f$b;->a:Landroid/content/Context;

    return-object p0
.end method

.method public static synthetic b(Landroidx/media3/exoplayer/video/f$b;)Landroidx/media3/common/j0$a;
    .locals 0

    iget-object p0, p0, Landroidx/media3/exoplayer/video/f$b;->c:Landroidx/media3/common/j0$a;

    return-object p0
.end method


# virtual methods
.method public c()Landroidx/media3/exoplayer/video/f;
    .locals 4

    iget-boolean v0, p0, Landroidx/media3/exoplayer/video/f$b;->d:Z

    const/4 v1, 0x1

    xor-int/2addr v0, v1

    invoke-static {v0}, Le2/a;->g(Z)V

    iget-object v0, p0, Landroidx/media3/exoplayer/video/f$b;->c:Landroidx/media3/common/j0$a;

    const/4 v2, 0x0

    if-nez v0, :cond_1

    iget-object v0, p0, Landroidx/media3/exoplayer/video/f$b;->b:Landroidx/media3/common/r0$a;

    if-nez v0, :cond_0

    new-instance v0, Landroidx/media3/exoplayer/video/f$c;

    invoke-direct {v0, v2}, Landroidx/media3/exoplayer/video/f$c;-><init>(Landroidx/media3/exoplayer/video/f$a;)V

    iput-object v0, p0, Landroidx/media3/exoplayer/video/f$b;->b:Landroidx/media3/common/r0$a;

    :cond_0
    new-instance v0, Landroidx/media3/exoplayer/video/f$d;

    iget-object v3, p0, Landroidx/media3/exoplayer/video/f$b;->b:Landroidx/media3/common/r0$a;

    invoke-direct {v0, v3}, Landroidx/media3/exoplayer/video/f$d;-><init>(Landroidx/media3/common/r0$a;)V

    iput-object v0, p0, Landroidx/media3/exoplayer/video/f$b;->c:Landroidx/media3/common/j0$a;

    :cond_1
    new-instance v0, Landroidx/media3/exoplayer/video/f;

    invoke-direct {v0, p0, v2}, Landroidx/media3/exoplayer/video/f;-><init>(Landroidx/media3/exoplayer/video/f$b;Landroidx/media3/exoplayer/video/f$a;)V

    iput-boolean v1, p0, Landroidx/media3/exoplayer/video/f$b;->d:Z

    return-object v0
.end method
