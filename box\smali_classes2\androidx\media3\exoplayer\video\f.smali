.class public final Landroidx/media3/exoplayer/video/f;
.super Ljava/lang/Object;

# interfaces
.implements Landroidx/media3/exoplayer/video/g0;
.implements Landroidx/media3/common/s0$a;
.implements Landroidx/media3/exoplayer/video/t$a;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Landroidx/media3/exoplayer/video/f$b;,
        Landroidx/media3/exoplayer/video/f$e;,
        Landroidx/media3/exoplayer/video/f$c;,
        Landroidx/media3/exoplayer/video/f$d;
    }
.end annotation


# static fields
.field public static final q:Ljava/util/concurrent/Executor;


# instance fields
.field public final a:Landroid/content/Context;

.field public final b:Landroidx/media3/common/j0$a;

.field public c:Le2/d;

.field public d:Landroidx/media3/exoplayer/video/p;

.field public e:Landroidx/media3/exoplayer/video/t;

.field public f:Landroidx/media3/common/y;

.field public g:Landroidx/media3/exoplayer/video/o;

.field public h:Le2/j;

.field public i:Landroidx/media3/common/j0;

.field public j:Landroidx/media3/exoplayer/video/f$e;

.field public k:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Landroidx/media3/common/p;",
            ">;"
        }
    .end annotation
.end field

.field public l:Landroid/util/Pair;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation

    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Landroid/util/Pair<",
            "Landroid/view/Surface;",
            "Le2/e0;",
            ">;"
        }
    .end annotation
.end field

.field public m:Landroidx/media3/exoplayer/video/VideoSink$a;

.field public n:Ljava/util/concurrent/Executor;

.field public o:I

.field public p:I


# direct methods
.method static constructor <clinit>()V
    .locals 1

    new-instance v0, Landroidx/media3/exoplayer/video/c;

    invoke-direct {v0}, Landroidx/media3/exoplayer/video/c;-><init>()V

    sput-object v0, Landroidx/media3/exoplayer/video/f;->q:Ljava/util/concurrent/Executor;

    return-void
.end method

.method public constructor <init>(Landroidx/media3/exoplayer/video/f$b;)V
    .locals 1

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    invoke-static {p1}, Landroidx/media3/exoplayer/video/f$b;->a(Landroidx/media3/exoplayer/video/f$b;)Landroid/content/Context;

    move-result-object v0

    iput-object v0, p0, Landroidx/media3/exoplayer/video/f;->a:Landroid/content/Context;

    invoke-static {p1}, Landroidx/media3/exoplayer/video/f$b;->b(Landroidx/media3/exoplayer/video/f$b;)Landroidx/media3/common/j0$a;

    move-result-object p1

    invoke-static {p1}, Le2/a;->i(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Landroidx/media3/common/j0$a;

    iput-object p1, p0, Landroidx/media3/exoplayer/video/f;->b:Landroidx/media3/common/j0$a;

    sget-object p1, Le2/d;->a:Le2/d;

    iput-object p1, p0, Landroidx/media3/exoplayer/video/f;->c:Le2/d;

    sget-object p1, Landroidx/media3/exoplayer/video/VideoSink$a;->a:Landroidx/media3/exoplayer/video/VideoSink$a;

    iput-object p1, p0, Landroidx/media3/exoplayer/video/f;->m:Landroidx/media3/exoplayer/video/VideoSink$a;

    sget-object p1, Landroidx/media3/exoplayer/video/f;->q:Ljava/util/concurrent/Executor;

    iput-object p1, p0, Landroidx/media3/exoplayer/video/f;->n:Ljava/util/concurrent/Executor;

    const/4 p1, 0x0

    iput p1, p0, Landroidx/media3/exoplayer/video/f;->p:I

    return-void
.end method

.method public synthetic constructor <init>(Landroidx/media3/exoplayer/video/f$b;Landroidx/media3/exoplayer/video/f$a;)V
    .locals 0

    invoke-direct {p0, p1}, Landroidx/media3/exoplayer/video/f;-><init>(Landroidx/media3/exoplayer/video/f$b;)V

    return-void
.end method

.method public static synthetic A(Landroidx/media3/exoplayer/video/VideoSink$a;Landroidx/media3/exoplayer/video/f$e;)V
    .locals 0

    invoke-interface {p0, p1}, Landroidx/media3/exoplayer/video/VideoSink$a;->a(Landroidx/media3/exoplayer/video/VideoSink;)V

    return-void
.end method

.method public static synthetic B(Ljava/lang/Runnable;)V
    .locals 0

    return-void
.end method

.method public static synthetic m(Landroidx/media3/exoplayer/video/f;Landroidx/media3/exoplayer/video/VideoSink$a;)V
    .locals 0

    invoke-virtual {p0, p1}, Landroidx/media3/exoplayer/video/f;->y(Landroidx/media3/exoplayer/video/VideoSink$a;)V

    return-void
.end method

.method public static synthetic n(Ljava/lang/Runnable;)V
    .locals 0

    invoke-static {p0}, Landroidx/media3/exoplayer/video/f;->B(Ljava/lang/Runnable;)V

    return-void
.end method

.method public static synthetic o(Landroidx/media3/exoplayer/video/VideoSink$a;Landroidx/media3/exoplayer/video/f$e;Landroidx/media3/common/t0;)V
    .locals 0

    invoke-static {p0, p1, p2}, Landroidx/media3/exoplayer/video/f;->z(Landroidx/media3/exoplayer/video/VideoSink$a;Landroidx/media3/exoplayer/video/f$e;Landroidx/media3/common/t0;)V

    return-void
.end method

.method public static synthetic p(Landroidx/media3/exoplayer/video/VideoSink$a;Landroidx/media3/exoplayer/video/f$e;)V
    .locals 0

    invoke-static {p0, p1}, Landroidx/media3/exoplayer/video/f;->A(Landroidx/media3/exoplayer/video/VideoSink$a;Landroidx/media3/exoplayer/video/f$e;)V

    return-void
.end method

.method public static synthetic q(Landroidx/media3/common/k;)Landroidx/media3/common/k;
    .locals 0

    invoke-static {p0}, Landroidx/media3/exoplayer/video/f;->v(Landroidx/media3/common/k;)Landroidx/media3/common/k;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic r(Landroidx/media3/exoplayer/video/f;)Z
    .locals 0

    invoke-virtual {p0}, Landroidx/media3/exoplayer/video/f;->x()Z

    move-result p0

    return p0
.end method

.method public static synthetic s(Landroidx/media3/exoplayer/video/f;J)Z
    .locals 0

    invoke-virtual {p0, p1, p2}, Landroidx/media3/exoplayer/video/f;->w(J)Z

    move-result p0

    return p0
.end method

.method public static synthetic t(Landroidx/media3/exoplayer/video/f;Landroidx/media3/exoplayer/video/VideoSink$a;Ljava/util/concurrent/Executor;)V
    .locals 0

    invoke-virtual {p0, p1, p2}, Landroidx/media3/exoplayer/video/f;->E(Landroidx/media3/exoplayer/video/VideoSink$a;Ljava/util/concurrent/Executor;)V

    return-void
.end method

.method public static synthetic u(Landroidx/media3/exoplayer/video/f;F)V
    .locals 0

    invoke-virtual {p0, p1}, Landroidx/media3/exoplayer/video/f;->F(F)V

    return-void
.end method

.method public static v(Landroidx/media3/common/k;)Landroidx/media3/common/k;
    .locals 1
    .param p0    # Landroidx/media3/common/k;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    if-eqz p0, :cond_0

    invoke-static {p0}, Landroidx/media3/common/k;->j(Landroidx/media3/common/k;)Z

    move-result v0

    if-eqz v0, :cond_0

    goto :goto_0

    :cond_0
    sget-object p0, Landroidx/media3/common/k;->h:Landroidx/media3/common/k;

    :goto_0
    return-object p0
.end method

.method public static synthetic z(Landroidx/media3/exoplayer/video/VideoSink$a;Landroidx/media3/exoplayer/video/f$e;Landroidx/media3/common/t0;)V
    .locals 0

    invoke-interface {p0, p1, p2}, Landroidx/media3/exoplayer/video/VideoSink$a;->b(Landroidx/media3/exoplayer/video/VideoSink;Landroidx/media3/common/t0;)V

    return-void
.end method


# virtual methods
.method public final C(Landroid/view/Surface;II)V
    .locals 1
    .param p1    # Landroid/view/Surface;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    iget-object v0, p0, Landroidx/media3/exoplayer/video/f;->i:Landroidx/media3/common/j0;

    if-eqz v0, :cond_1

    if-eqz p1, :cond_0

    new-instance v0, Landroidx/media3/common/l0;

    invoke-direct {v0, p1, p2, p3}, Landroidx/media3/common/l0;-><init>(Landroid/view/Surface;II)V

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    iget-object p2, p0, Landroidx/media3/exoplayer/video/f;->i:Landroidx/media3/common/j0;

    invoke-interface {p2, v0}, Landroidx/media3/common/s0;->c(Landroidx/media3/common/l0;)V

    iget-object p2, p0, Landroidx/media3/exoplayer/video/f;->d:Landroidx/media3/exoplayer/video/p;

    invoke-static {p2}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p2

    check-cast p2, Landroidx/media3/exoplayer/video/p;

    invoke-virtual {p2, p1}, Landroidx/media3/exoplayer/video/p;->q(Landroid/view/Surface;)V

    :cond_1
    return-void
.end method

.method public D(JJ)V
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/exoplayer/ExoPlaybackException;
        }
    .end annotation

    iget v0, p0, Landroidx/media3/exoplayer/video/f;->o:I

    if-nez v0, :cond_0

    iget-object v0, p0, Landroidx/media3/exoplayer/video/f;->e:Landroidx/media3/exoplayer/video/t;

    invoke-static {v0}, Le2/a;->i(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroidx/media3/exoplayer/video/t;

    invoke-virtual {v0, p1, p2, p3, p4}, Landroidx/media3/exoplayer/video/t;->f(JJ)V

    :cond_0
    return-void
.end method

.method public final E(Landroidx/media3/exoplayer/video/VideoSink$a;Ljava/util/concurrent/Executor;)V
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/video/f;->m:Landroidx/media3/exoplayer/video/VideoSink$a;

    invoke-static {p1, v0}, Ljava/util/Objects;->equals(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_0

    iget-object p1, p0, Landroidx/media3/exoplayer/video/f;->n:Ljava/util/concurrent/Executor;

    invoke-static {p2, p1}, Ljava/util/Objects;->equals(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result p1

    invoke-static {p1}, Le2/a;->g(Z)V

    return-void

    :cond_0
    iput-object p1, p0, Landroidx/media3/exoplayer/video/f;->m:Landroidx/media3/exoplayer/video/VideoSink$a;

    iput-object p2, p0, Landroidx/media3/exoplayer/video/f;->n:Ljava/util/concurrent/Executor;

    return-void
.end method

.method public final F(F)V
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/video/f;->e:Landroidx/media3/exoplayer/video/t;

    invoke-static {v0}, Le2/a;->i(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroidx/media3/exoplayer/video/t;

    invoke-virtual {v0, p1}, Landroidx/media3/exoplayer/video/t;->h(F)V

    return-void
.end method

.method public a(Landroidx/media3/exoplayer/video/p;)V
    .locals 1

    invoke-virtual {p0}, Landroidx/media3/exoplayer/video/f;->isInitialized()Z

    move-result v0

    xor-int/lit8 v0, v0, 0x1

    invoke-static {v0}, Le2/a;->g(Z)V

    iput-object p1, p0, Landroidx/media3/exoplayer/video/f;->d:Landroidx/media3/exoplayer/video/p;

    new-instance v0, Landroidx/media3/exoplayer/video/t;

    invoke-direct {v0, p0, p1}, Landroidx/media3/exoplayer/video/t;-><init>(Landroidx/media3/exoplayer/video/t$a;Landroidx/media3/exoplayer/video/p;)V

    iput-object v0, p0, Landroidx/media3/exoplayer/video/f;->e:Landroidx/media3/exoplayer/video/t;

    return-void
.end method

.method public b()V
    .locals 3

    iget-object v0, p0, Landroidx/media3/exoplayer/video/f;->m:Landroidx/media3/exoplayer/video/VideoSink$a;

    iget-object v1, p0, Landroidx/media3/exoplayer/video/f;->n:Ljava/util/concurrent/Executor;

    new-instance v2, Landroidx/media3/exoplayer/video/d;

    invoke-direct {v2, p0, v0}, Landroidx/media3/exoplayer/video/d;-><init>(Landroidx/media3/exoplayer/video/f;Landroidx/media3/exoplayer/video/VideoSink$a;)V

    invoke-interface {v1, v2}, Ljava/util/concurrent/Executor;->execute(Ljava/lang/Runnable;)V

    iget-object v0, p0, Landroidx/media3/exoplayer/video/f;->i:Landroidx/media3/common/j0;

    invoke-static {v0}, Le2/a;->i(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroidx/media3/common/j0;

    const-wide/16 v1, -0x2

    invoke-interface {v0, v1, v2}, Landroidx/media3/common/j0;->b(J)V

    return-void
.end method

.method public c(Landroidx/media3/exoplayer/video/o;)V
    .locals 0

    iput-object p1, p0, Landroidx/media3/exoplayer/video/f;->g:Landroidx/media3/exoplayer/video/o;

    return-void
.end method

.method public d(Le2/d;)V
    .locals 1

    invoke-virtual {p0}, Landroidx/media3/exoplayer/video/f;->isInitialized()Z

    move-result v0

    xor-int/lit8 v0, v0, 0x1

    invoke-static {v0}, Le2/a;->g(Z)V

    iput-object p1, p0, Landroidx/media3/exoplayer/video/f;->c:Le2/d;

    return-void
.end method

.method public e(Ljava/util/List;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Landroidx/media3/common/p;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Landroidx/media3/exoplayer/video/f;->k:Ljava/util/List;

    invoke-virtual {p0}, Landroidx/media3/exoplayer/video/f;->isInitialized()Z

    move-result v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Landroidx/media3/exoplayer/video/f;->j:Landroidx/media3/exoplayer/video/f$e;

    invoke-static {v0}, Le2/a;->i(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroidx/media3/exoplayer/video/f$e;

    invoke-virtual {v0, p1}, Landroidx/media3/exoplayer/video/f$e;->i(Ljava/util/List;)V

    :cond_0
    return-void
.end method

.method public f()Landroidx/media3/exoplayer/video/p;
    .locals 1
    .annotation build Landroidx/annotation/Nullable;
    .end annotation

    iget-object v0, p0, Landroidx/media3/exoplayer/video/f;->d:Landroidx/media3/exoplayer/video/p;

    return-object v0
.end method

.method public g(Landroid/view/Surface;Le2/e0;)V
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/video/f;->l:Landroid/util/Pair;

    if-eqz v0, :cond_0

    iget-object v0, v0, Landroid/util/Pair;->first:Ljava/lang/Object;

    check-cast v0, Landroid/view/Surface;

    invoke-virtual {v0, p1}, Ljava/lang/Object;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Landroidx/media3/exoplayer/video/f;->l:Landroid/util/Pair;

    iget-object v0, v0, Landroid/util/Pair;->second:Ljava/lang/Object;

    check-cast v0, Le2/e0;

    invoke-virtual {v0, p2}, Le2/e0;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_0

    return-void

    :cond_0
    invoke-static {p1, p2}, Landroid/util/Pair;->create(Ljava/lang/Object;Ljava/lang/Object;)Landroid/util/Pair;

    move-result-object v0

    iput-object v0, p0, Landroidx/media3/exoplayer/video/f;->l:Landroid/util/Pair;

    invoke-virtual {p2}, Le2/e0;->b()I

    move-result v0

    invoke-virtual {p2}, Le2/e0;->a()I

    move-result p2

    invoke-virtual {p0, p1, v0, p2}, Landroidx/media3/exoplayer/video/f;->C(Landroid/view/Surface;II)V

    return-void
.end method

.method public h(Landroidx/media3/common/y;)V
    .locals 13
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/exoplayer/video/VideoSink$VideoSinkException;
        }
    .end annotation

    iget v0, p0, Landroidx/media3/exoplayer/video/f;->p:I

    const/4 v1, 0x0

    const/4 v2, 0x1

    if-nez v0, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    invoke-static {v0}, Le2/a;->g(Z)V

    iget-object v0, p0, Landroidx/media3/exoplayer/video/f;->k:Ljava/util/List;

    invoke-static {v0}, Le2/a;->i(Ljava/lang/Object;)Ljava/lang/Object;

    iget-object v0, p0, Landroidx/media3/exoplayer/video/f;->e:Landroidx/media3/exoplayer/video/t;

    if-eqz v0, :cond_1

    iget-object v0, p0, Landroidx/media3/exoplayer/video/f;->d:Landroidx/media3/exoplayer/video/p;

    if-eqz v0, :cond_1

    const/4 v1, 0x1

    :cond_1
    invoke-static {v1}, Le2/a;->g(Z)V

    iget-object v0, p0, Landroidx/media3/exoplayer/video/f;->c:Le2/d;

    invoke-static {}, Landroid/os/Looper;->myLooper()Landroid/os/Looper;

    move-result-object v1

    invoke-static {v1}, Le2/a;->i(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Landroid/os/Looper;

    const/4 v3, 0x0

    invoke-interface {v0, v1, v3}, Le2/d;->createHandler(Landroid/os/Looper;Landroid/os/Handler$Callback;)Le2/j;

    move-result-object v0

    iput-object v0, p0, Landroidx/media3/exoplayer/video/f;->h:Le2/j;

    iget-object v0, p1, Landroidx/media3/common/y;->y:Landroidx/media3/common/k;

    invoke-static {v0}, Landroidx/media3/exoplayer/video/f;->v(Landroidx/media3/common/k;)Landroidx/media3/common/k;

    move-result-object v5

    iget v0, v5, Landroidx/media3/common/k;->c:I

    const/4 v1, 0x7

    if-ne v0, v1, :cond_2

    invoke-virtual {v5}, Landroidx/media3/common/k;->a()Landroidx/media3/common/k$b;

    move-result-object v0

    const/4 v1, 0x6

    invoke-virtual {v0, v1}, Landroidx/media3/common/k$b;->e(I)Landroidx/media3/common/k$b;

    move-result-object v0

    invoke-virtual {v0}, Landroidx/media3/common/k$b;->a()Landroidx/media3/common/k;

    move-result-object v0

    move-object v6, v0

    goto :goto_1

    :cond_2
    move-object v6, v5

    :goto_1
    :try_start_0
    iget-object v3, p0, Landroidx/media3/exoplayer/video/f;->b:Landroidx/media3/common/j0$a;

    iget-object v4, p0, Landroidx/media3/exoplayer/video/f;->a:Landroid/content/Context;

    sget-object v7, Landroidx/media3/common/n;->a:Landroidx/media3/common/n;

    iget-object v0, p0, Landroidx/media3/exoplayer/video/f;->h:Le2/j;

    invoke-static {v0}, Ljava/util/Objects;->requireNonNull(Ljava/lang/Object;)Ljava/lang/Object;

    new-instance v9, Landroidx/media3/exoplayer/video/e;

    invoke-direct {v9, v0}, Landroidx/media3/exoplayer/video/e;-><init>(Le2/j;)V

    invoke-static {}, Lcom/google/common/collect/ImmutableList;->of()Lcom/google/common/collect/ImmutableList;

    move-result-object v10

    const-wide/16 v11, 0x0

    move-object v8, p0

    invoke-interface/range {v3 .. v12}, Landroidx/media3/common/j0$a;->a(Landroid/content/Context;Landroidx/media3/common/k;Landroidx/media3/common/k;Landroidx/media3/common/n;Landroidx/media3/common/s0$a;Ljava/util/concurrent/Executor;Ljava/util/List;J)Landroidx/media3/common/j0;

    move-result-object v0

    iput-object v0, p0, Landroidx/media3/exoplayer/video/f;->i:Landroidx/media3/common/j0;

    iget-object v0, p0, Landroidx/media3/exoplayer/video/f;->l:Landroid/util/Pair;

    if-eqz v0, :cond_3

    iget-object v1, v0, Landroid/util/Pair;->first:Ljava/lang/Object;

    check-cast v1, Landroid/view/Surface;

    iget-object v0, v0, Landroid/util/Pair;->second:Ljava/lang/Object;

    check-cast v0, Le2/e0;

    invoke-virtual {v0}, Le2/e0;->b()I

    move-result v3

    invoke-virtual {v0}, Le2/e0;->a()I

    move-result v0

    invoke-virtual {p0, v1, v3, v0}, Landroidx/media3/exoplayer/video/f;->C(Landroid/view/Surface;II)V

    goto :goto_2

    :catch_0
    move-exception v0

    goto :goto_3

    :cond_3
    :goto_2
    new-instance v0, Landroidx/media3/exoplayer/video/f$e;

    iget-object v1, p0, Landroidx/media3/exoplayer/video/f;->a:Landroid/content/Context;

    iget-object v3, p0, Landroidx/media3/exoplayer/video/f;->i:Landroidx/media3/common/j0;

    invoke-direct {v0, v1, p0, v3}, Landroidx/media3/exoplayer/video/f$e;-><init>(Landroid/content/Context;Landroidx/media3/exoplayer/video/f;Landroidx/media3/common/j0;)V

    iput-object v0, p0, Landroidx/media3/exoplayer/video/f;->j:Landroidx/media3/exoplayer/video/f$e;
    :try_end_0
    .catch Landroidx/media3/common/VideoFrameProcessingException; {:try_start_0 .. :try_end_0} :catch_0

    iget-object p1, p0, Landroidx/media3/exoplayer/video/f;->k:Ljava/util/List;

    invoke-static {p1}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/util/List;

    invoke-virtual {v0, p1}, Landroidx/media3/exoplayer/video/f$e;->i(Ljava/util/List;)V

    iput v2, p0, Landroidx/media3/exoplayer/video/f;->p:I

    return-void

    :goto_3
    new-instance v1, Landroidx/media3/exoplayer/video/VideoSink$VideoSinkException;

    invoke-direct {v1, v0, p1}, Landroidx/media3/exoplayer/video/VideoSink$VideoSinkException;-><init>(Ljava/lang/Throwable;Landroidx/media3/common/y;)V

    throw v1
.end method

.method public i(JJJZ)V
    .locals 7

    if-eqz p7, :cond_0

    iget-object p7, p0, Landroidx/media3/exoplayer/video/f;->n:Ljava/util/concurrent/Executor;

    sget-object v0, Landroidx/media3/exoplayer/video/f;->q:Ljava/util/concurrent/Executor;

    if-eq p7, v0, :cond_0

    iget-object p7, p0, Landroidx/media3/exoplayer/video/f;->j:Landroidx/media3/exoplayer/video/f$e;

    invoke-static {p7}, Le2/a;->i(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p7

    check-cast p7, Landroidx/media3/exoplayer/video/f$e;

    iget-object v0, p0, Landroidx/media3/exoplayer/video/f;->m:Landroidx/media3/exoplayer/video/VideoSink$a;

    iget-object v1, p0, Landroidx/media3/exoplayer/video/f;->n:Ljava/util/concurrent/Executor;

    new-instance v2, Landroidx/media3/exoplayer/video/b;

    invoke-direct {v2, v0, p7}, Landroidx/media3/exoplayer/video/b;-><init>(Landroidx/media3/exoplayer/video/VideoSink$a;Landroidx/media3/exoplayer/video/f$e;)V

    invoke-interface {v1, v2}, Ljava/util/concurrent/Executor;->execute(Ljava/lang/Runnable;)V

    :cond_0
    iget-object p7, p0, Landroidx/media3/exoplayer/video/f;->g:Landroidx/media3/exoplayer/video/o;

    if-eqz p7, :cond_2

    iget-object p7, p0, Landroidx/media3/exoplayer/video/f;->f:Landroidx/media3/common/y;

    if-nez p7, :cond_1

    new-instance p7, Landroidx/media3/common/y$b;

    invoke-direct {p7}, Landroidx/media3/common/y$b;-><init>()V

    invoke-virtual {p7}, Landroidx/media3/common/y$b;->I()Landroidx/media3/common/y;

    move-result-object p7

    :cond_1
    move-object v5, p7

    iget-object v0, p0, Landroidx/media3/exoplayer/video/f;->g:Landroidx/media3/exoplayer/video/o;

    sub-long v1, p3, p5

    iget-object p3, p0, Landroidx/media3/exoplayer/video/f;->c:Le2/d;

    invoke-interface {p3}, Le2/d;->b()J

    move-result-wide v3

    const/4 v6, 0x0

    invoke-interface/range {v0 .. v6}, Landroidx/media3/exoplayer/video/o;->e(JJLandroidx/media3/common/y;Landroid/media/MediaFormat;)V

    :cond_2
    iget-object p3, p0, Landroidx/media3/exoplayer/video/f;->i:Landroidx/media3/common/j0;

    invoke-static {p3}, Le2/a;->i(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p3

    check-cast p3, Landroidx/media3/common/j0;

    invoke-interface {p3, p1, p2}, Landroidx/media3/common/j0;->b(J)V

    return-void
.end method

.method public isInitialized()Z
    .locals 2

    iget v0, p0, Landroidx/media3/exoplayer/video/f;->p:I

    const/4 v1, 0x1

    if-ne v0, v1, :cond_0

    goto :goto_0

    :cond_0
    const/4 v1, 0x0

    :goto_0
    return v1
.end method

.method public j()V
    .locals 3

    sget-object v0, Le2/e0;->c:Le2/e0;

    invoke-virtual {v0}, Le2/e0;->b()I

    move-result v1

    invoke-virtual {v0}, Le2/e0;->a()I

    move-result v0

    const/4 v2, 0x0

    invoke-virtual {p0, v2, v1, v0}, Landroidx/media3/exoplayer/video/f;->C(Landroid/view/Surface;II)V

    iput-object v2, p0, Landroidx/media3/exoplayer/video/f;->l:Landroid/util/Pair;

    return-void
.end method

.method public k()Landroidx/media3/exoplayer/video/VideoSink;
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/video/f;->j:Landroidx/media3/exoplayer/video/f$e;

    invoke-static {v0}, Le2/a;->i(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroidx/media3/exoplayer/video/VideoSink;

    return-object v0
.end method

.method public l(J)V
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/video/f;->j:Landroidx/media3/exoplayer/video/f$e;

    invoke-static {v0}, Le2/a;->i(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroidx/media3/exoplayer/video/f$e;

    invoke-virtual {v0, p1, p2}, Landroidx/media3/exoplayer/video/f$e;->h(J)V

    return-void
.end method

.method public onVideoSizeChanged(Landroidx/media3/common/t0;)V
    .locals 4

    new-instance v0, Landroidx/media3/common/y$b;

    invoke-direct {v0}, Landroidx/media3/common/y$b;-><init>()V

    iget v1, p1, Landroidx/media3/common/t0;->a:I

    invoke-virtual {v0, v1}, Landroidx/media3/common/y$b;->r0(I)Landroidx/media3/common/y$b;

    move-result-object v0

    iget v1, p1, Landroidx/media3/common/t0;->b:I

    invoke-virtual {v0, v1}, Landroidx/media3/common/y$b;->V(I)Landroidx/media3/common/y$b;

    move-result-object v0

    const-string v1, "video/raw"

    invoke-virtual {v0, v1}, Landroidx/media3/common/y$b;->k0(Ljava/lang/String;)Landroidx/media3/common/y$b;

    move-result-object v0

    invoke-virtual {v0}, Landroidx/media3/common/y$b;->I()Landroidx/media3/common/y;

    move-result-object v0

    iput-object v0, p0, Landroidx/media3/exoplayer/video/f;->f:Landroidx/media3/common/y;

    iget-object v0, p0, Landroidx/media3/exoplayer/video/f;->j:Landroidx/media3/exoplayer/video/f$e;

    invoke-static {v0}, Le2/a;->i(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroidx/media3/exoplayer/video/f$e;

    iget-object v1, p0, Landroidx/media3/exoplayer/video/f;->m:Landroidx/media3/exoplayer/video/VideoSink$a;

    iget-object v2, p0, Landroidx/media3/exoplayer/video/f;->n:Ljava/util/concurrent/Executor;

    new-instance v3, Landroidx/media3/exoplayer/video/a;

    invoke-direct {v3, v1, v0, p1}, Landroidx/media3/exoplayer/video/a;-><init>(Landroidx/media3/exoplayer/video/VideoSink$a;Landroidx/media3/exoplayer/video/f$e;Landroidx/media3/common/t0;)V

    invoke-interface {v2, v3}, Ljava/util/concurrent/Executor;->execute(Ljava/lang/Runnable;)V

    return-void
.end method

.method public release()V
    .locals 3

    iget v0, p0, Landroidx/media3/exoplayer/video/f;->p:I

    const/4 v1, 0x2

    if-ne v0, v1, :cond_0

    return-void

    :cond_0
    iget-object v0, p0, Landroidx/media3/exoplayer/video/f;->h:Le2/j;

    const/4 v2, 0x0

    if-eqz v0, :cond_1

    invoke-interface {v0, v2}, Le2/j;->removeCallbacksAndMessages(Ljava/lang/Object;)V

    :cond_1
    iget-object v0, p0, Landroidx/media3/exoplayer/video/f;->i:Landroidx/media3/common/j0;

    if-eqz v0, :cond_2

    invoke-interface {v0}, Landroidx/media3/common/s0;->release()V

    :cond_2
    iput-object v2, p0, Landroidx/media3/exoplayer/video/f;->l:Landroid/util/Pair;

    iput v1, p0, Landroidx/media3/exoplayer/video/f;->p:I

    return-void
.end method

.method public final w(J)Z
    .locals 1

    iget v0, p0, Landroidx/media3/exoplayer/video/f;->o:I

    if-nez v0, :cond_0

    iget-object v0, p0, Landroidx/media3/exoplayer/video/f;->e:Landroidx/media3/exoplayer/video/t;

    invoke-static {v0}, Le2/a;->i(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroidx/media3/exoplayer/video/t;

    invoke-virtual {v0, p1, p2}, Landroidx/media3/exoplayer/video/t;->b(J)Z

    move-result p1

    if-eqz p1, :cond_0

    const/4 p1, 0x1

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    :goto_0
    return p1
.end method

.method public final x()Z
    .locals 1

    iget v0, p0, Landroidx/media3/exoplayer/video/f;->o:I

    if-nez v0, :cond_0

    iget-object v0, p0, Landroidx/media3/exoplayer/video/f;->e:Landroidx/media3/exoplayer/video/t;

    invoke-static {v0}, Le2/a;->i(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroidx/media3/exoplayer/video/t;

    invoke-virtual {v0}, Landroidx/media3/exoplayer/video/t;->c()Z

    move-result v0

    if-eqz v0, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method public final synthetic y(Landroidx/media3/exoplayer/video/VideoSink$a;)V
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/video/f;->j:Landroidx/media3/exoplayer/video/f$e;

    invoke-static {v0}, Le2/a;->i(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroidx/media3/exoplayer/video/VideoSink;

    invoke-interface {p1, v0}, Landroidx/media3/exoplayer/video/VideoSink$a;->c(Landroidx/media3/exoplayer/video/VideoSink;)V

    return-void
.end method
