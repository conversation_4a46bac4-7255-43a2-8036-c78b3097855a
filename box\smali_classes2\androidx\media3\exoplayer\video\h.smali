.class public abstract Landroidx/media3/exoplayer/video/h;
.super Landroidx/media3/exoplayer/m;


# instance fields
.field public A:Landroidx/media3/decoder/VideoDecoderOutputBuffer;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public B:I

.field public C:Ljava/lang/Object;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public D:Landroid/view/Surface;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public E:Landroidx/media3/exoplayer/video/n;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public F:Landroidx/media3/exoplayer/video/o;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public G:Landroidx/media3/exoplayer/drm/DrmSession;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public H:Landroidx/media3/exoplayer/drm/DrmSession;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public I:I

.field public J:Z

.field public K:I

.field public L:J

.field public M:J

.field public N:Z

.field public O:Z

.field public P:Z

.field public Q:Landroidx/media3/common/t0;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public R:J

.field public S:I

.field public T:I

.field public U:I

.field public V:J

.field public W:J

.field public X:Landroidx/media3/exoplayer/n;

.field public final r:J

.field public final s:I

.field public final t:Landroidx/media3/exoplayer/video/f0$a;

.field public final u:Le2/h0;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Le2/h0<",
            "Landroidx/media3/common/y;",
            ">;"
        }
    .end annotation
.end field

.field public final v:Landroidx/media3/decoder/DecoderInputBuffer;

.field public w:Landroidx/media3/common/y;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public x:Landroidx/media3/common/y;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public y:Landroidx/media3/decoder/g;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation

    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Landroidx/media3/decoder/g<",
            "Landroidx/media3/decoder/DecoderInputBuffer;",
            "+",
            "Landroidx/media3/decoder/VideoDecoderOutputBuffer;",
            "+",
            "Landroidx/media3/decoder/DecoderException;",
            ">;"
        }
    .end annotation
.end field

.field public z:Landroidx/media3/decoder/DecoderInputBuffer;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field


# direct methods
.method public constructor <init>(JLandroid/os/Handler;Landroidx/media3/exoplayer/video/f0;I)V
    .locals 1
    .param p3    # Landroid/os/Handler;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .param p4    # Landroidx/media3/exoplayer/video/f0;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    const/4 v0, 0x2

    invoke-direct {p0, v0}, Landroidx/media3/exoplayer/m;-><init>(I)V

    iput-wide p1, p0, Landroidx/media3/exoplayer/video/h;->r:J

    iput p5, p0, Landroidx/media3/exoplayer/video/h;->s:I

    const-wide p1, -0x7fffffffffffffffL    # -4.9E-324

    iput-wide p1, p0, Landroidx/media3/exoplayer/video/h;->M:J

    new-instance p1, Le2/h0;

    invoke-direct {p1}, Le2/h0;-><init>()V

    iput-object p1, p0, Landroidx/media3/exoplayer/video/h;->u:Le2/h0;

    invoke-static {}, Landroidx/media3/decoder/DecoderInputBuffer;->g()Landroidx/media3/decoder/DecoderInputBuffer;

    move-result-object p1

    iput-object p1, p0, Landroidx/media3/exoplayer/video/h;->v:Landroidx/media3/decoder/DecoderInputBuffer;

    new-instance p1, Landroidx/media3/exoplayer/video/f0$a;

    invoke-direct {p1, p3, p4}, Landroidx/media3/exoplayer/video/f0$a;-><init>(Landroid/os/Handler;Landroidx/media3/exoplayer/video/f0;)V

    iput-object p1, p0, Landroidx/media3/exoplayer/video/h;->t:Landroidx/media3/exoplayer/video/f0$a;

    const/4 p1, 0x0

    iput p1, p0, Landroidx/media3/exoplayer/video/h;->I:I

    const/4 p2, -0x1

    iput p2, p0, Landroidx/media3/exoplayer/video/h;->B:I

    iput p1, p0, Landroidx/media3/exoplayer/video/h;->K:I

    new-instance p1, Landroidx/media3/exoplayer/n;

    invoke-direct {p1}, Landroidx/media3/exoplayer/n;-><init>()V

    iput-object p1, p0, Landroidx/media3/exoplayer/video/h;->X:Landroidx/media3/exoplayer/n;

    return-void
.end method

.method private P(JJ)Z
    .locals 4
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/exoplayer/ExoPlaybackException;,
            Landroidx/media3/decoder/DecoderException;
        }
    .end annotation

    iget-object v0, p0, Landroidx/media3/exoplayer/video/h;->A:Landroidx/media3/decoder/VideoDecoderOutputBuffer;

    const/4 v1, 0x0

    if-nez v0, :cond_1

    iget-object v0, p0, Landroidx/media3/exoplayer/video/h;->y:Landroidx/media3/decoder/g;

    invoke-static {v0}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroidx/media3/decoder/g;

    invoke-interface {v0}, Landroidx/media3/decoder/g;->dequeueOutputBuffer()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroidx/media3/decoder/VideoDecoderOutputBuffer;

    iput-object v0, p0, Landroidx/media3/exoplayer/video/h;->A:Landroidx/media3/decoder/VideoDecoderOutputBuffer;

    if-nez v0, :cond_0

    return v1

    :cond_0
    iget-object v2, p0, Landroidx/media3/exoplayer/video/h;->X:Landroidx/media3/exoplayer/n;

    iget v3, v2, Landroidx/media3/exoplayer/n;->f:I

    iget v0, v0, Landroidx/media3/decoder/h;->skippedOutputBufferCount:I

    add-int/2addr v3, v0

    iput v3, v2, Landroidx/media3/exoplayer/n;->f:I

    iget v2, p0, Landroidx/media3/exoplayer/video/h;->U:I

    sub-int/2addr v2, v0

    iput v2, p0, Landroidx/media3/exoplayer/video/h;->U:I

    :cond_1
    iget-object v0, p0, Landroidx/media3/exoplayer/video/h;->A:Landroidx/media3/decoder/VideoDecoderOutputBuffer;

    invoke-virtual {v0}, Landroidx/media3/decoder/a;->isEndOfStream()Z

    move-result v0

    const/4 v2, 0x0

    if-eqz v0, :cond_3

    iget p1, p0, Landroidx/media3/exoplayer/video/h;->I:I

    const/4 p2, 0x2

    if-ne p1, p2, :cond_2

    invoke-virtual {p0}, Landroidx/media3/exoplayer/video/h;->l0()V

    invoke-direct {p0}, Landroidx/media3/exoplayer/video/h;->Y()V

    goto :goto_0

    :cond_2
    iget-object p1, p0, Landroidx/media3/exoplayer/video/h;->A:Landroidx/media3/decoder/VideoDecoderOutputBuffer;

    invoke-virtual {p1}, Landroidx/media3/decoder/VideoDecoderOutputBuffer;->release()V

    iput-object v2, p0, Landroidx/media3/exoplayer/video/h;->A:Landroidx/media3/decoder/VideoDecoderOutputBuffer;

    const/4 p1, 0x1

    iput-boolean p1, p0, Landroidx/media3/exoplayer/video/h;->P:Z

    :goto_0
    return v1

    :cond_3
    invoke-virtual {p0, p1, p2, p3, p4}, Landroidx/media3/exoplayer/video/h;->k0(JJ)Z

    move-result p1

    if-eqz p1, :cond_4

    iget-object p2, p0, Landroidx/media3/exoplayer/video/h;->A:Landroidx/media3/decoder/VideoDecoderOutputBuffer;

    invoke-static {p2}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p2

    check-cast p2, Landroidx/media3/decoder/VideoDecoderOutputBuffer;

    iget-wide p2, p2, Landroidx/media3/decoder/h;->timeUs:J

    invoke-virtual {p0, p2, p3}, Landroidx/media3/exoplayer/video/h;->i0(J)V

    iput-object v2, p0, Landroidx/media3/exoplayer/video/h;->A:Landroidx/media3/decoder/VideoDecoderOutputBuffer;

    :cond_4
    return p1
.end method

.method private R()Z
    .locals 8
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/decoder/DecoderException;,
            Landroidx/media3/exoplayer/ExoPlaybackException;
        }
    .end annotation

    iget-object v0, p0, Landroidx/media3/exoplayer/video/h;->y:Landroidx/media3/decoder/g;

    const/4 v1, 0x0

    if-eqz v0, :cond_9

    iget v2, p0, Landroidx/media3/exoplayer/video/h;->I:I

    const/4 v3, 0x2

    if-eq v2, v3, :cond_9

    iget-boolean v2, p0, Landroidx/media3/exoplayer/video/h;->O:Z

    if-eqz v2, :cond_0

    goto/16 :goto_0

    :cond_0
    iget-object v2, p0, Landroidx/media3/exoplayer/video/h;->z:Landroidx/media3/decoder/DecoderInputBuffer;

    if-nez v2, :cond_1

    invoke-interface {v0}, Landroidx/media3/decoder/g;->dequeueInputBuffer()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroidx/media3/decoder/DecoderInputBuffer;

    iput-object v0, p0, Landroidx/media3/exoplayer/video/h;->z:Landroidx/media3/decoder/DecoderInputBuffer;

    if-nez v0, :cond_1

    return v1

    :cond_1
    iget-object v0, p0, Landroidx/media3/exoplayer/video/h;->z:Landroidx/media3/decoder/DecoderInputBuffer;

    invoke-static {v0}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroidx/media3/decoder/DecoderInputBuffer;

    iget v2, p0, Landroidx/media3/exoplayer/video/h;->I:I

    const/4 v4, 0x0

    const/4 v5, 0x1

    if-ne v2, v5, :cond_2

    const/4 v2, 0x4

    invoke-virtual {v0, v2}, Landroidx/media3/decoder/a;->setFlags(I)V

    iget-object v2, p0, Landroidx/media3/exoplayer/video/h;->y:Landroidx/media3/decoder/g;

    invoke-static {v2}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Landroidx/media3/decoder/g;

    invoke-interface {v2, v0}, Landroidx/media3/decoder/g;->queueInputBuffer(Ljava/lang/Object;)V

    iput-object v4, p0, Landroidx/media3/exoplayer/video/h;->z:Landroidx/media3/decoder/DecoderInputBuffer;

    iput v3, p0, Landroidx/media3/exoplayer/video/h;->I:I

    return v1

    :cond_2
    invoke-virtual {p0}, Landroidx/media3/exoplayer/m;->t()Landroidx/media3/exoplayer/t1;

    move-result-object v2

    invoke-virtual {p0, v2, v0, v1}, Landroidx/media3/exoplayer/m;->K(Landroidx/media3/exoplayer/t1;Landroidx/media3/decoder/DecoderInputBuffer;I)I

    move-result v3

    const/4 v6, -0x5

    if-eq v3, v6, :cond_8

    const/4 v2, -0x4

    if-eq v3, v2, :cond_4

    const/4 v0, -0x3

    if-ne v3, v0, :cond_3

    return v1

    :cond_3
    new-instance v0, Ljava/lang/IllegalStateException;

    invoke-direct {v0}, Ljava/lang/IllegalStateException;-><init>()V

    throw v0

    :cond_4
    invoke-virtual {v0}, Landroidx/media3/decoder/a;->isEndOfStream()Z

    move-result v2

    if-eqz v2, :cond_5

    iput-boolean v5, p0, Landroidx/media3/exoplayer/video/h;->O:Z

    iget-object v2, p0, Landroidx/media3/exoplayer/video/h;->y:Landroidx/media3/decoder/g;

    invoke-static {v2}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Landroidx/media3/decoder/g;

    invoke-interface {v2, v0}, Landroidx/media3/decoder/g;->queueInputBuffer(Ljava/lang/Object;)V

    iput-object v4, p0, Landroidx/media3/exoplayer/video/h;->z:Landroidx/media3/decoder/DecoderInputBuffer;

    return v1

    :cond_5
    iget-boolean v2, p0, Landroidx/media3/exoplayer/video/h;->N:Z

    if-eqz v2, :cond_6

    iget-object v2, p0, Landroidx/media3/exoplayer/video/h;->u:Le2/h0;

    iget-wide v6, v0, Landroidx/media3/decoder/DecoderInputBuffer;->e:J

    iget-object v3, p0, Landroidx/media3/exoplayer/video/h;->w:Landroidx/media3/common/y;

    invoke-static {v3}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Landroidx/media3/common/y;

    invoke-virtual {v2, v6, v7, v3}, Le2/h0;->a(JLjava/lang/Object;)V

    iput-boolean v1, p0, Landroidx/media3/exoplayer/video/h;->N:Z

    :cond_6
    iget-wide v1, v0, Landroidx/media3/decoder/DecoderInputBuffer;->e:J

    invoke-virtual {p0}, Landroidx/media3/exoplayer/m;->v()J

    move-result-wide v6

    cmp-long v3, v1, v6

    if-gez v3, :cond_7

    const/high16 v1, -0x80000000

    invoke-virtual {v0, v1}, Landroidx/media3/decoder/a;->addFlag(I)V

    :cond_7
    invoke-virtual {v0}, Landroidx/media3/decoder/DecoderInputBuffer;->e()V

    iget-object v1, p0, Landroidx/media3/exoplayer/video/h;->w:Landroidx/media3/common/y;

    iput-object v1, v0, Landroidx/media3/decoder/DecoderInputBuffer;->a:Landroidx/media3/common/y;

    invoke-virtual {p0, v0}, Landroidx/media3/exoplayer/video/h;->j0(Landroidx/media3/decoder/DecoderInputBuffer;)V

    iget-object v1, p0, Landroidx/media3/exoplayer/video/h;->y:Landroidx/media3/decoder/g;

    invoke-static {v1}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Landroidx/media3/decoder/g;

    invoke-interface {v1, v0}, Landroidx/media3/decoder/g;->queueInputBuffer(Ljava/lang/Object;)V

    iget v0, p0, Landroidx/media3/exoplayer/video/h;->U:I

    add-int/2addr v0, v5

    iput v0, p0, Landroidx/media3/exoplayer/video/h;->U:I

    iput-boolean v5, p0, Landroidx/media3/exoplayer/video/h;->J:Z

    iget-object v0, p0, Landroidx/media3/exoplayer/video/h;->X:Landroidx/media3/exoplayer/n;

    iget v1, v0, Landroidx/media3/exoplayer/n;->c:I

    add-int/2addr v1, v5

    iput v1, v0, Landroidx/media3/exoplayer/n;->c:I

    iput-object v4, p0, Landroidx/media3/exoplayer/video/h;->z:Landroidx/media3/decoder/DecoderInputBuffer;

    return v5

    :cond_8
    invoke-virtual {p0, v2}, Landroidx/media3/exoplayer/video/h;->e0(Landroidx/media3/exoplayer/t1;)V

    return v5

    :cond_9
    :goto_0
    return v1
.end method

.method public static U(J)Z
    .locals 3

    const-wide/16 v0, -0x7530

    cmp-long v2, p0, v0

    if-gez v2, :cond_0

    const/4 p0, 0x1

    goto :goto_0

    :cond_0
    const/4 p0, 0x0

    :goto_0
    return p0
.end method

.method public static V(J)Z
    .locals 3

    const-wide/32 v0, -0x7a120

    cmp-long v2, p0, v0

    if-gez v2, :cond_0

    const/4 p0, 0x1

    goto :goto_0

    :cond_0
    const/4 p0, 0x0

    :goto_0
    return p0
.end method

.method private W(I)V
    .locals 1

    iget v0, p0, Landroidx/media3/exoplayer/video/h;->K:I

    invoke-static {v0, p1}, Ljava/lang/Math;->min(II)I

    move-result p1

    iput p1, p0, Landroidx/media3/exoplayer/video/h;->K:I

    return-void
.end method

.method private Y()V
    .locals 10
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/exoplayer/ExoPlaybackException;
        }
    .end annotation

    iget-object v0, p0, Landroidx/media3/exoplayer/video/h;->y:Landroidx/media3/decoder/g;

    if-eqz v0, :cond_0

    return-void

    :cond_0
    iget-object v0, p0, Landroidx/media3/exoplayer/video/h;->H:Landroidx/media3/exoplayer/drm/DrmSession;

    invoke-direct {p0, v0}, Landroidx/media3/exoplayer/video/h;->o0(Landroidx/media3/exoplayer/drm/DrmSession;)V

    iget-object v0, p0, Landroidx/media3/exoplayer/video/h;->G:Landroidx/media3/exoplayer/drm/DrmSession;

    if-eqz v0, :cond_2

    invoke-interface {v0}, Landroidx/media3/exoplayer/drm/DrmSession;->c()Landroidx/media3/decoder/b;

    move-result-object v0

    if-nez v0, :cond_3

    iget-object v1, p0, Landroidx/media3/exoplayer/video/h;->G:Landroidx/media3/exoplayer/drm/DrmSession;

    invoke-interface {v1}, Landroidx/media3/exoplayer/drm/DrmSession;->getError()Landroidx/media3/exoplayer/drm/DrmSession$DrmSessionException;

    move-result-object v1

    if-eqz v1, :cond_1

    goto :goto_0

    :cond_1
    return-void

    :cond_2
    const/4 v0, 0x0

    :cond_3
    :goto_0
    const/16 v1, 0xfa1

    :try_start_0
    invoke-static {}, Landroid/os/SystemClock;->elapsedRealtime()J

    move-result-wide v2

    iget-object v4, p0, Landroidx/media3/exoplayer/video/h;->w:Landroidx/media3/common/y;

    invoke-static {v4}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Landroidx/media3/common/y;

    invoke-virtual {p0, v4, v0}, Landroidx/media3/exoplayer/video/h;->O(Landroidx/media3/common/y;Landroidx/media3/decoder/b;)Landroidx/media3/decoder/g;

    move-result-object v0

    iput-object v0, p0, Landroidx/media3/exoplayer/video/h;->y:Landroidx/media3/decoder/g;

    invoke-virtual {p0}, Landroidx/media3/exoplayer/m;->v()J

    move-result-wide v4

    invoke-interface {v0, v4, v5}, Landroidx/media3/decoder/g;->a(J)V

    iget v0, p0, Landroidx/media3/exoplayer/video/h;->B:I

    invoke-virtual {p0, v0}, Landroidx/media3/exoplayer/video/h;->p0(I)V

    invoke-static {}, Landroid/os/SystemClock;->elapsedRealtime()J

    move-result-wide v6

    iget-object v4, p0, Landroidx/media3/exoplayer/video/h;->t:Landroidx/media3/exoplayer/video/f0$a;

    iget-object v0, p0, Landroidx/media3/exoplayer/video/h;->y:Landroidx/media3/decoder/g;

    invoke-static {v0}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroidx/media3/decoder/g;

    invoke-interface {v0}, Landroidx/media3/decoder/g;->getName()Ljava/lang/String;

    move-result-object v5

    sub-long v8, v6, v2

    invoke-virtual/range {v4 .. v9}, Landroidx/media3/exoplayer/video/f0$a;->k(Ljava/lang/String;JJ)V

    iget-object v0, p0, Landroidx/media3/exoplayer/video/h;->X:Landroidx/media3/exoplayer/n;

    iget v2, v0, Landroidx/media3/exoplayer/n;->a:I

    add-int/lit8 v2, v2, 0x1

    iput v2, v0, Landroidx/media3/exoplayer/n;->a:I
    :try_end_0
    .catch Landroidx/media3/decoder/DecoderException; {:try_start_0 .. :try_end_0} :catch_1
    .catch Ljava/lang/OutOfMemoryError; {:try_start_0 .. :try_end_0} :catch_0

    return-void

    :catch_0
    move-exception v0

    goto :goto_1

    :catch_1
    move-exception v0

    goto :goto_2

    :goto_1
    iget-object v2, p0, Landroidx/media3/exoplayer/video/h;->w:Landroidx/media3/common/y;

    invoke-virtual {p0, v0, v2, v1}, Landroidx/media3/exoplayer/m;->p(Ljava/lang/Throwable;Landroidx/media3/common/y;I)Landroidx/media3/exoplayer/ExoPlaybackException;

    move-result-object v0

    throw v0

    :goto_2
    const-string v2, "DecoderVideoRenderer"

    const-string v3, "Video codec error"

    invoke-static {v2, v3, v0}, Le2/o;->d(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Throwable;)V

    iget-object v2, p0, Landroidx/media3/exoplayer/video/h;->t:Landroidx/media3/exoplayer/video/f0$a;

    invoke-virtual {v2, v0}, Landroidx/media3/exoplayer/video/f0$a;->C(Ljava/lang/Exception;)V

    iget-object v2, p0, Landroidx/media3/exoplayer/video/h;->w:Landroidx/media3/common/y;

    invoke-virtual {p0, v0, v2, v1}, Landroidx/media3/exoplayer/m;->p(Ljava/lang/Throwable;Landroidx/media3/common/y;I)Landroidx/media3/exoplayer/ExoPlaybackException;

    move-result-object v0

    throw v0
.end method

.method private Z()V
    .locals 6

    iget v0, p0, Landroidx/media3/exoplayer/video/h;->S:I

    if-lez v0, :cond_0

    invoke-static {}, Landroid/os/SystemClock;->elapsedRealtime()J

    move-result-wide v0

    iget-wide v2, p0, Landroidx/media3/exoplayer/video/h;->R:J

    sub-long v2, v0, v2

    iget-object v4, p0, Landroidx/media3/exoplayer/video/h;->t:Landroidx/media3/exoplayer/video/f0$a;

    iget v5, p0, Landroidx/media3/exoplayer/video/h;->S:I

    invoke-virtual {v4, v5, v2, v3}, Landroidx/media3/exoplayer/video/f0$a;->n(IJ)V

    const/4 v2, 0x0

    iput v2, p0, Landroidx/media3/exoplayer/video/h;->S:I

    iput-wide v0, p0, Landroidx/media3/exoplayer/video/h;->R:J

    :cond_0
    return-void
.end method

.method private a0()V
    .locals 2

    iget v0, p0, Landroidx/media3/exoplayer/video/h;->K:I

    const/4 v1, 0x3

    if-eq v0, v1, :cond_0

    iput v1, p0, Landroidx/media3/exoplayer/video/h;->K:I

    iget-object v0, p0, Landroidx/media3/exoplayer/video/h;->C:Ljava/lang/Object;

    if-eqz v0, :cond_0

    iget-object v1, p0, Landroidx/media3/exoplayer/video/h;->t:Landroidx/media3/exoplayer/video/f0$a;

    invoke-virtual {v1, v0}, Landroidx/media3/exoplayer/video/f0$a;->A(Ljava/lang/Object;)V

    :cond_0
    return-void
.end method

.method private c0()V
    .locals 2

    iget v0, p0, Landroidx/media3/exoplayer/video/h;->K:I

    const/4 v1, 0x3

    if-ne v0, v1, :cond_0

    iget-object v0, p0, Landroidx/media3/exoplayer/video/h;->C:Ljava/lang/Object;

    if-eqz v0, :cond_0

    iget-object v1, p0, Landroidx/media3/exoplayer/video/h;->t:Landroidx/media3/exoplayer/video/f0$a;

    invoke-virtual {v1, v0}, Landroidx/media3/exoplayer/video/f0$a;->A(Ljava/lang/Object;)V

    :cond_0
    return-void
.end method

.method private d0()V
    .locals 2

    iget-object v0, p0, Landroidx/media3/exoplayer/video/h;->Q:Landroidx/media3/common/t0;

    if-eqz v0, :cond_0

    iget-object v1, p0, Landroidx/media3/exoplayer/video/h;->t:Landroidx/media3/exoplayer/video/f0$a;

    invoke-virtual {v1, v0}, Landroidx/media3/exoplayer/video/f0$a;->D(Landroidx/media3/common/t0;)V

    :cond_0
    return-void
.end method

.method private o0(Landroidx/media3/exoplayer/drm/DrmSession;)V
    .locals 1
    .param p1    # Landroidx/media3/exoplayer/drm/DrmSession;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    iget-object v0, p0, Landroidx/media3/exoplayer/video/h;->G:Landroidx/media3/exoplayer/drm/DrmSession;

    invoke-static {v0, p1}, Ln2/j;->a(Landroidx/media3/exoplayer/drm/DrmSession;Landroidx/media3/exoplayer/drm/DrmSession;)V

    iput-object p1, p0, Landroidx/media3/exoplayer/video/h;->G:Landroidx/media3/exoplayer/drm/DrmSession;

    return-void
.end method

.method private s0(Landroidx/media3/exoplayer/drm/DrmSession;)V
    .locals 1
    .param p1    # Landroidx/media3/exoplayer/drm/DrmSession;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    iget-object v0, p0, Landroidx/media3/exoplayer/video/h;->H:Landroidx/media3/exoplayer/drm/DrmSession;

    invoke-static {v0, p1}, Ln2/j;->a(Landroidx/media3/exoplayer/drm/DrmSession;Landroidx/media3/exoplayer/drm/DrmSession;)V

    iput-object p1, p0, Landroidx/media3/exoplayer/video/h;->H:Landroidx/media3/exoplayer/drm/DrmSession;

    return-void
.end method


# virtual methods
.method public A(ZZ)V
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/exoplayer/ExoPlaybackException;
        }
    .end annotation

    new-instance p1, Landroidx/media3/exoplayer/n;

    invoke-direct {p1}, Landroidx/media3/exoplayer/n;-><init>()V

    iput-object p1, p0, Landroidx/media3/exoplayer/video/h;->X:Landroidx/media3/exoplayer/n;

    iget-object v0, p0, Landroidx/media3/exoplayer/video/h;->t:Landroidx/media3/exoplayer/video/f0$a;

    invoke-virtual {v0, p1}, Landroidx/media3/exoplayer/video/f0$a;->o(Landroidx/media3/exoplayer/n;)V

    iput p2, p0, Landroidx/media3/exoplayer/video/h;->K:I

    return-void
.end method

.method public C(JZ)V
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/exoplayer/ExoPlaybackException;
        }
    .end annotation

    const/4 p1, 0x0

    iput-boolean p1, p0, Landroidx/media3/exoplayer/video/h;->O:Z

    iput-boolean p1, p0, Landroidx/media3/exoplayer/video/h;->P:Z

    const/4 p2, 0x1

    invoke-direct {p0, p2}, Landroidx/media3/exoplayer/video/h;->W(I)V

    const-wide v0, -0x7fffffffffffffffL    # -4.9E-324

    iput-wide v0, p0, Landroidx/media3/exoplayer/video/h;->L:J

    iput p1, p0, Landroidx/media3/exoplayer/video/h;->T:I

    iget-object p1, p0, Landroidx/media3/exoplayer/video/h;->y:Landroidx/media3/decoder/g;

    if-eqz p1, :cond_0

    invoke-virtual {p0}, Landroidx/media3/exoplayer/video/h;->S()V

    :cond_0
    if-eqz p3, :cond_1

    invoke-virtual {p0}, Landroidx/media3/exoplayer/video/h;->q0()V

    goto :goto_0

    :cond_1
    iput-wide v0, p0, Landroidx/media3/exoplayer/video/h;->M:J

    :goto_0
    iget-object p1, p0, Landroidx/media3/exoplayer/video/h;->u:Le2/h0;

    invoke-virtual {p1}, Le2/h0;->c()V

    return-void
.end method

.method public G()V
    .locals 2

    const/4 v0, 0x0

    iput v0, p0, Landroidx/media3/exoplayer/video/h;->S:I

    invoke-static {}, Landroid/os/SystemClock;->elapsedRealtime()J

    move-result-wide v0

    iput-wide v0, p0, Landroidx/media3/exoplayer/video/h;->R:J

    invoke-static {}, Landroid/os/SystemClock;->elapsedRealtime()J

    move-result-wide v0

    invoke-static {v0, v1}, Le2/u0;->S0(J)J

    move-result-wide v0

    iput-wide v0, p0, Landroidx/media3/exoplayer/video/h;->V:J

    return-void
.end method

.method public H()V
    .locals 2

    const-wide v0, -0x7fffffffffffffffL    # -4.9E-324

    iput-wide v0, p0, Landroidx/media3/exoplayer/video/h;->M:J

    invoke-direct {p0}, Landroidx/media3/exoplayer/video/h;->Z()V

    return-void
.end method

.method public I([Landroidx/media3/common/y;JJLandroidx/media3/exoplayer/source/l$b;)V
    .locals 0
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/exoplayer/ExoPlaybackException;
        }
    .end annotation

    iput-wide p4, p0, Landroidx/media3/exoplayer/video/h;->W:J

    invoke-super/range {p0 .. p6}, Landroidx/media3/exoplayer/m;->I([Landroidx/media3/common/y;JJLandroidx/media3/exoplayer/source/l$b;)V

    return-void
.end method

.method public N(Ljava/lang/String;Landroidx/media3/common/y;Landroidx/media3/common/y;)Landroidx/media3/exoplayer/o;
    .locals 7

    new-instance v6, Landroidx/media3/exoplayer/o;

    const/4 v4, 0x0

    const/4 v5, 0x1

    move-object v0, v6

    move-object v1, p1

    move-object v2, p2

    move-object v3, p3

    invoke-direct/range {v0 .. v5}, Landroidx/media3/exoplayer/o;-><init>(Ljava/lang/String;Landroidx/media3/common/y;Landroidx/media3/common/y;II)V

    return-object v6
.end method

.method public abstract O(Landroidx/media3/common/y;Landroidx/media3/decoder/b;)Landroidx/media3/decoder/g;
    .param p2    # Landroidx/media3/decoder/b;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroidx/media3/common/y;",
            "Landroidx/media3/decoder/b;",
            ")",
            "Landroidx/media3/decoder/g<",
            "Landroidx/media3/decoder/DecoderInputBuffer;",
            "+",
            "Landroidx/media3/decoder/VideoDecoderOutputBuffer;",
            "+",
            "Landroidx/media3/decoder/DecoderException;",
            ">;"
        }
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/decoder/DecoderException;
        }
    .end annotation
.end method

.method public Q(Landroidx/media3/decoder/VideoDecoderOutputBuffer;)V
    .locals 2

    const/4 v0, 0x0

    const/4 v1, 0x1

    invoke-virtual {p0, v0, v1}, Landroidx/media3/exoplayer/video/h;->y0(II)V

    invoke-virtual {p1}, Landroidx/media3/decoder/VideoDecoderOutputBuffer;->release()V

    return-void
.end method

.method public S()V
    .locals 4
    .annotation build Landroidx/annotation/CallSuper;
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/exoplayer/ExoPlaybackException;
        }
    .end annotation

    const/4 v0, 0x0

    iput v0, p0, Landroidx/media3/exoplayer/video/h;->U:I

    iget v1, p0, Landroidx/media3/exoplayer/video/h;->I:I

    if-eqz v1, :cond_0

    invoke-virtual {p0}, Landroidx/media3/exoplayer/video/h;->l0()V

    invoke-direct {p0}, Landroidx/media3/exoplayer/video/h;->Y()V

    goto :goto_0

    :cond_0
    const/4 v1, 0x0

    iput-object v1, p0, Landroidx/media3/exoplayer/video/h;->z:Landroidx/media3/decoder/DecoderInputBuffer;

    iget-object v2, p0, Landroidx/media3/exoplayer/video/h;->A:Landroidx/media3/decoder/VideoDecoderOutputBuffer;

    if-eqz v2, :cond_1

    invoke-virtual {v2}, Landroidx/media3/decoder/VideoDecoderOutputBuffer;->release()V

    iput-object v1, p0, Landroidx/media3/exoplayer/video/h;->A:Landroidx/media3/decoder/VideoDecoderOutputBuffer;

    :cond_1
    iget-object v1, p0, Landroidx/media3/exoplayer/video/h;->y:Landroidx/media3/decoder/g;

    invoke-static {v1}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Landroidx/media3/decoder/g;

    invoke-interface {v1}, Landroidx/media3/decoder/g;->flush()V

    invoke-virtual {p0}, Landroidx/media3/exoplayer/m;->v()J

    move-result-wide v2

    invoke-interface {v1, v2, v3}, Landroidx/media3/decoder/g;->a(J)V

    iput-boolean v0, p0, Landroidx/media3/exoplayer/video/h;->J:Z

    :goto_0
    return-void
.end method

.method public final T()Z
    .locals 2

    iget v0, p0, Landroidx/media3/exoplayer/video/h;->B:I

    const/4 v1, -0x1

    if-eq v0, v1, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method public X(J)Z
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/exoplayer/ExoPlaybackException;
        }
    .end annotation

    invoke-virtual {p0, p1, p2}, Landroidx/media3/exoplayer/m;->M(J)I

    move-result p1

    if-nez p1, :cond_0

    const/4 p1, 0x0

    return p1

    :cond_0
    iget-object p2, p0, Landroidx/media3/exoplayer/video/h;->X:Landroidx/media3/exoplayer/n;

    iget v0, p2, Landroidx/media3/exoplayer/n;->j:I

    const/4 v1, 0x1

    add-int/2addr v0, v1

    iput v0, p2, Landroidx/media3/exoplayer/n;->j:I

    iget p2, p0, Landroidx/media3/exoplayer/video/h;->U:I

    invoke-virtual {p0, p1, p2}, Landroidx/media3/exoplayer/video/h;->y0(II)V

    invoke-virtual {p0}, Landroidx/media3/exoplayer/video/h;->S()V

    return v1
.end method

.method public final b0(II)V
    .locals 2

    iget-object v0, p0, Landroidx/media3/exoplayer/video/h;->Q:Landroidx/media3/common/t0;

    if-eqz v0, :cond_0

    iget v1, v0, Landroidx/media3/common/t0;->a:I

    if-ne v1, p1, :cond_0

    iget v0, v0, Landroidx/media3/common/t0;->b:I

    if-eq v0, p2, :cond_1

    :cond_0
    new-instance v0, Landroidx/media3/common/t0;

    invoke-direct {v0, p1, p2}, Landroidx/media3/common/t0;-><init>(II)V

    iput-object v0, p0, Landroidx/media3/exoplayer/video/h;->Q:Landroidx/media3/common/t0;

    iget-object p1, p0, Landroidx/media3/exoplayer/video/h;->t:Landroidx/media3/exoplayer/video/f0$a;

    invoke-virtual {p1, v0}, Landroidx/media3/exoplayer/video/f0$a;->D(Landroidx/media3/common/t0;)V

    :cond_1
    return-void
.end method

.method public e0(Landroidx/media3/exoplayer/t1;)V
    .locals 9
    .annotation build Landroidx/annotation/CallSuper;
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/exoplayer/ExoPlaybackException;
        }
    .end annotation

    const/4 v0, 0x1

    iput-boolean v0, p0, Landroidx/media3/exoplayer/video/h;->N:Z

    iget-object v1, p1, Landroidx/media3/exoplayer/t1;->b:Landroidx/media3/common/y;

    invoke-static {v1}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    move-object v5, v1

    check-cast v5, Landroidx/media3/common/y;

    iget-object p1, p1, Landroidx/media3/exoplayer/t1;->a:Landroidx/media3/exoplayer/drm/DrmSession;

    invoke-direct {p0, p1}, Landroidx/media3/exoplayer/video/h;->s0(Landroidx/media3/exoplayer/drm/DrmSession;)V

    iget-object p1, p0, Landroidx/media3/exoplayer/video/h;->w:Landroidx/media3/common/y;

    iput-object v5, p0, Landroidx/media3/exoplayer/video/h;->w:Landroidx/media3/common/y;

    iget-object v1, p0, Landroidx/media3/exoplayer/video/h;->y:Landroidx/media3/decoder/g;

    if-nez v1, :cond_0

    invoke-direct {p0}, Landroidx/media3/exoplayer/video/h;->Y()V

    iget-object p1, p0, Landroidx/media3/exoplayer/video/h;->t:Landroidx/media3/exoplayer/video/f0$a;

    iget-object v0, p0, Landroidx/media3/exoplayer/video/h;->w:Landroidx/media3/common/y;

    invoke-static {v0}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroidx/media3/common/y;

    const/4 v1, 0x0

    invoke-virtual {p1, v0, v1}, Landroidx/media3/exoplayer/video/f0$a;->p(Landroidx/media3/common/y;Landroidx/media3/exoplayer/o;)V

    return-void

    :cond_0
    iget-object v2, p0, Landroidx/media3/exoplayer/video/h;->H:Landroidx/media3/exoplayer/drm/DrmSession;

    iget-object v3, p0, Landroidx/media3/exoplayer/video/h;->G:Landroidx/media3/exoplayer/drm/DrmSession;

    if-eq v2, v3, :cond_1

    new-instance v8, Landroidx/media3/exoplayer/o;

    invoke-interface {v1}, Landroidx/media3/decoder/g;->getName()Ljava/lang/String;

    move-result-object v3

    invoke-static {p1}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    move-object v4, p1

    check-cast v4, Landroidx/media3/common/y;

    const/4 v6, 0x0

    const/16 v7, 0x80

    move-object v2, v8

    invoke-direct/range {v2 .. v7}, Landroidx/media3/exoplayer/o;-><init>(Ljava/lang/String;Landroidx/media3/common/y;Landroidx/media3/common/y;II)V

    goto :goto_0

    :cond_1
    invoke-interface {v1}, Landroidx/media3/decoder/g;->getName()Ljava/lang/String;

    move-result-object v1

    invoke-static {p1}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Landroidx/media3/common/y;

    invoke-virtual {p0, v1, p1, v5}, Landroidx/media3/exoplayer/video/h;->N(Ljava/lang/String;Landroidx/media3/common/y;Landroidx/media3/common/y;)Landroidx/media3/exoplayer/o;

    move-result-object v8

    :goto_0
    iget p1, v8, Landroidx/media3/exoplayer/o;->d:I

    if-nez p1, :cond_3

    iget-boolean p1, p0, Landroidx/media3/exoplayer/video/h;->J:Z

    if-eqz p1, :cond_2

    iput v0, p0, Landroidx/media3/exoplayer/video/h;->I:I

    goto :goto_1

    :cond_2
    invoke-virtual {p0}, Landroidx/media3/exoplayer/video/h;->l0()V

    invoke-direct {p0}, Landroidx/media3/exoplayer/video/h;->Y()V

    :cond_3
    :goto_1
    iget-object p1, p0, Landroidx/media3/exoplayer/video/h;->t:Landroidx/media3/exoplayer/video/f0$a;

    iget-object v0, p0, Landroidx/media3/exoplayer/video/h;->w:Landroidx/media3/common/y;

    invoke-static {v0}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroidx/media3/common/y;

    invoke-virtual {p1, v0, v8}, Landroidx/media3/exoplayer/video/f0$a;->p(Landroidx/media3/common/y;Landroidx/media3/exoplayer/o;)V

    return-void
.end method

.method public f()V
    .locals 1

    iget v0, p0, Landroidx/media3/exoplayer/video/h;->K:I

    if-nez v0, :cond_0

    const/4 v0, 0x1

    iput v0, p0, Landroidx/media3/exoplayer/video/h;->K:I

    :cond_0
    return-void
.end method

.method public final f0()V
    .locals 2

    invoke-direct {p0}, Landroidx/media3/exoplayer/video/h;->d0()V

    const/4 v0, 0x1

    invoke-direct {p0, v0}, Landroidx/media3/exoplayer/video/h;->W(I)V

    invoke-virtual {p0}, Landroidx/media3/exoplayer/m;->getState()I

    move-result v0

    const/4 v1, 0x2

    if-ne v0, v1, :cond_0

    invoke-virtual {p0}, Landroidx/media3/exoplayer/video/h;->q0()V

    :cond_0
    return-void
.end method

.method public final g0()V
    .locals 1

    const/4 v0, 0x0

    iput-object v0, p0, Landroidx/media3/exoplayer/video/h;->Q:Landroidx/media3/common/t0;

    const/4 v0, 0x1

    invoke-direct {p0, v0}, Landroidx/media3/exoplayer/video/h;->W(I)V

    return-void
.end method

.method public final h0()V
    .locals 0

    invoke-direct {p0}, Landroidx/media3/exoplayer/video/h;->d0()V

    invoke-direct {p0}, Landroidx/media3/exoplayer/video/h;->c0()V

    return-void
.end method

.method public handleMessage(ILjava/lang/Object;)V
    .locals 1
    .param p2    # Ljava/lang/Object;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/exoplayer/ExoPlaybackException;
        }
    .end annotation

    const/4 v0, 0x1

    if-ne p1, v0, :cond_0

    invoke-virtual {p0, p2}, Landroidx/media3/exoplayer/video/h;->r0(Ljava/lang/Object;)V

    goto :goto_0

    :cond_0
    const/4 v0, 0x7

    if-ne p1, v0, :cond_1

    check-cast p2, Landroidx/media3/exoplayer/video/o;

    iput-object p2, p0, Landroidx/media3/exoplayer/video/h;->F:Landroidx/media3/exoplayer/video/o;

    goto :goto_0

    :cond_1
    invoke-super {p0, p1, p2}, Landroidx/media3/exoplayer/m;->handleMessage(ILjava/lang/Object;)V

    :goto_0
    return-void
.end method

.method public i0(J)V
    .locals 0
    .annotation build Landroidx/annotation/CallSuper;
    .end annotation

    iget p1, p0, Landroidx/media3/exoplayer/video/h;->U:I

    add-int/lit8 p1, p1, -0x1

    iput p1, p0, Landroidx/media3/exoplayer/video/h;->U:I

    return-void
.end method

.method public isEnded()Z
    .locals 1

    iget-boolean v0, p0, Landroidx/media3/exoplayer/video/h;->P:Z

    return v0
.end method

.method public isReady()Z
    .locals 9

    iget-object v0, p0, Landroidx/media3/exoplayer/video/h;->w:Landroidx/media3/common/y;

    const/4 v1, 0x1

    const-wide v2, -0x7fffffffffffffffL    # -4.9E-324

    if-eqz v0, :cond_2

    invoke-virtual {p0}, Landroidx/media3/exoplayer/m;->y()Z

    move-result v0

    if-nez v0, :cond_0

    iget-object v0, p0, Landroidx/media3/exoplayer/video/h;->A:Landroidx/media3/decoder/VideoDecoderOutputBuffer;

    if-eqz v0, :cond_2

    :cond_0
    iget v0, p0, Landroidx/media3/exoplayer/video/h;->K:I

    const/4 v4, 0x3

    if-eq v0, v4, :cond_1

    invoke-virtual {p0}, Landroidx/media3/exoplayer/video/h;->T()Z

    move-result v0

    if-nez v0, :cond_2

    :cond_1
    iput-wide v2, p0, Landroidx/media3/exoplayer/video/h;->M:J

    return v1

    :cond_2
    iget-wide v4, p0, Landroidx/media3/exoplayer/video/h;->M:J

    const/4 v0, 0x0

    cmp-long v6, v4, v2

    if-nez v6, :cond_3

    return v0

    :cond_3
    invoke-static {}, Landroid/os/SystemClock;->elapsedRealtime()J

    move-result-wide v4

    iget-wide v6, p0, Landroidx/media3/exoplayer/video/h;->M:J

    cmp-long v8, v4, v6

    if-gez v8, :cond_4

    return v1

    :cond_4
    iput-wide v2, p0, Landroidx/media3/exoplayer/video/h;->M:J

    return v0
.end method

.method public j0(Landroidx/media3/decoder/DecoderInputBuffer;)V
    .locals 0

    return-void
.end method

.method public final k0(JJ)Z
    .locals 10
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/exoplayer/ExoPlaybackException;,
            Landroidx/media3/decoder/DecoderException;
        }
    .end annotation

    iget-wide v0, p0, Landroidx/media3/exoplayer/video/h;->L:J

    const-wide v2, -0x7fffffffffffffffL    # -4.9E-324

    cmp-long v4, v0, v2

    if-nez v4, :cond_0

    iput-wide p1, p0, Landroidx/media3/exoplayer/video/h;->L:J

    :cond_0
    iget-object v0, p0, Landroidx/media3/exoplayer/video/h;->A:Landroidx/media3/decoder/VideoDecoderOutputBuffer;

    invoke-static {v0}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroidx/media3/decoder/VideoDecoderOutputBuffer;

    iget-wide v1, v0, Landroidx/media3/decoder/h;->timeUs:J

    sub-long v3, v1, p1

    invoke-virtual {p0}, Landroidx/media3/exoplayer/video/h;->T()Z

    move-result v5

    const/4 v6, 0x0

    const/4 v7, 0x1

    if-nez v5, :cond_2

    invoke-static {v3, v4}, Landroidx/media3/exoplayer/video/h;->U(J)Z

    move-result p1

    if-eqz p1, :cond_1

    invoke-virtual {p0, v0}, Landroidx/media3/exoplayer/video/h;->x0(Landroidx/media3/decoder/VideoDecoderOutputBuffer;)V

    return v7

    :cond_1
    return v6

    :cond_2
    iget-object v5, p0, Landroidx/media3/exoplayer/video/h;->u:Le2/h0;

    invoke-virtual {v5, v1, v2}, Le2/h0;->j(J)Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Landroidx/media3/common/y;

    if-eqz v5, :cond_3

    iput-object v5, p0, Landroidx/media3/exoplayer/video/h;->x:Landroidx/media3/common/y;

    goto :goto_0

    :cond_3
    iget-object v5, p0, Landroidx/media3/exoplayer/video/h;->x:Landroidx/media3/common/y;

    if-nez v5, :cond_4

    iget-object v5, p0, Landroidx/media3/exoplayer/video/h;->u:Le2/h0;

    invoke-virtual {v5}, Le2/h0;->i()Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Landroidx/media3/common/y;

    iput-object v5, p0, Landroidx/media3/exoplayer/video/h;->x:Landroidx/media3/common/y;

    :cond_4
    :goto_0
    iget-wide v8, p0, Landroidx/media3/exoplayer/video/h;->W:J

    sub-long/2addr v1, v8

    invoke-virtual {p0, v3, v4}, Landroidx/media3/exoplayer/video/h;->v0(J)Z

    move-result v5

    if-eqz v5, :cond_5

    iget-object p1, p0, Landroidx/media3/exoplayer/video/h;->x:Landroidx/media3/common/y;

    invoke-static {p1}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Landroidx/media3/common/y;

    invoke-virtual {p0, v0, v1, v2, p1}, Landroidx/media3/exoplayer/video/h;->m0(Landroidx/media3/decoder/VideoDecoderOutputBuffer;JLandroidx/media3/common/y;)V

    return v7

    :cond_5
    invoke-virtual {p0}, Landroidx/media3/exoplayer/m;->getState()I

    move-result v5

    const/4 v8, 0x2

    if-ne v5, v8, :cond_9

    iget-wide v8, p0, Landroidx/media3/exoplayer/video/h;->L:J

    cmp-long v5, p1, v8

    if-nez v5, :cond_6

    goto :goto_1

    :cond_6
    invoke-virtual {p0, v3, v4, p3, p4}, Landroidx/media3/exoplayer/video/h;->t0(JJ)Z

    move-result v5

    if-eqz v5, :cond_7

    invoke-virtual {p0, p1, p2}, Landroidx/media3/exoplayer/video/h;->X(J)Z

    move-result p1

    if-eqz p1, :cond_7

    return v6

    :cond_7
    invoke-virtual {p0, v3, v4, p3, p4}, Landroidx/media3/exoplayer/video/h;->u0(JJ)Z

    move-result p1

    if-eqz p1, :cond_8

    invoke-virtual {p0, v0}, Landroidx/media3/exoplayer/video/h;->Q(Landroidx/media3/decoder/VideoDecoderOutputBuffer;)V

    return v7

    :cond_8
    const-wide/16 p1, 0x7530

    cmp-long p3, v3, p1

    if-gez p3, :cond_9

    iget-object p1, p0, Landroidx/media3/exoplayer/video/h;->x:Landroidx/media3/common/y;

    invoke-static {p1}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Landroidx/media3/common/y;

    invoke-virtual {p0, v0, v1, v2, p1}, Landroidx/media3/exoplayer/video/h;->m0(Landroidx/media3/decoder/VideoDecoderOutputBuffer;JLandroidx/media3/common/y;)V

    return v7

    :cond_9
    :goto_1
    return v6
.end method

.method public l0()V
    .locals 4
    .annotation build Landroidx/annotation/CallSuper;
    .end annotation

    const/4 v0, 0x0

    iput-object v0, p0, Landroidx/media3/exoplayer/video/h;->z:Landroidx/media3/decoder/DecoderInputBuffer;

    iput-object v0, p0, Landroidx/media3/exoplayer/video/h;->A:Landroidx/media3/decoder/VideoDecoderOutputBuffer;

    const/4 v1, 0x0

    iput v1, p0, Landroidx/media3/exoplayer/video/h;->I:I

    iput-boolean v1, p0, Landroidx/media3/exoplayer/video/h;->J:Z

    iput v1, p0, Landroidx/media3/exoplayer/video/h;->U:I

    iget-object v1, p0, Landroidx/media3/exoplayer/video/h;->y:Landroidx/media3/decoder/g;

    if-eqz v1, :cond_0

    iget-object v2, p0, Landroidx/media3/exoplayer/video/h;->X:Landroidx/media3/exoplayer/n;

    iget v3, v2, Landroidx/media3/exoplayer/n;->b:I

    add-int/lit8 v3, v3, 0x1

    iput v3, v2, Landroidx/media3/exoplayer/n;->b:I

    invoke-interface {v1}, Landroidx/media3/decoder/g;->release()V

    iget-object v1, p0, Landroidx/media3/exoplayer/video/h;->t:Landroidx/media3/exoplayer/video/f0$a;

    iget-object v2, p0, Landroidx/media3/exoplayer/video/h;->y:Landroidx/media3/decoder/g;

    invoke-interface {v2}, Landroidx/media3/decoder/g;->getName()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v2}, Landroidx/media3/exoplayer/video/f0$a;->l(Ljava/lang/String;)V

    iput-object v0, p0, Landroidx/media3/exoplayer/video/h;->y:Landroidx/media3/decoder/g;

    :cond_0
    invoke-direct {p0, v0}, Landroidx/media3/exoplayer/video/h;->o0(Landroidx/media3/exoplayer/drm/DrmSession;)V

    return-void
.end method

.method public m0(Landroidx/media3/decoder/VideoDecoderOutputBuffer;JLandroidx/media3/common/y;)V
    .locals 7
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/decoder/DecoderException;
        }
    .end annotation

    iget-object v0, p0, Landroidx/media3/exoplayer/video/h;->F:Landroidx/media3/exoplayer/video/o;

    if-eqz v0, :cond_0

    invoke-virtual {p0}, Landroidx/media3/exoplayer/m;->r()Le2/d;

    move-result-object v1

    invoke-interface {v1}, Le2/d;->b()J

    move-result-wide v3

    const/4 v6, 0x0

    move-wide v1, p2

    move-object v5, p4

    invoke-interface/range {v0 .. v6}, Landroidx/media3/exoplayer/video/o;->e(JJLandroidx/media3/common/y;Landroid/media/MediaFormat;)V

    :cond_0
    invoke-static {}, Landroid/os/SystemClock;->elapsedRealtime()J

    move-result-wide p2

    invoke-static {p2, p3}, Le2/u0;->S0(J)J

    move-result-wide p2

    iput-wide p2, p0, Landroidx/media3/exoplayer/video/h;->V:J

    iget p2, p1, Landroidx/media3/decoder/VideoDecoderOutputBuffer;->mode:I

    const/4 p3, 0x0

    const/4 p4, 0x1

    if-ne p2, p4, :cond_1

    iget-object v0, p0, Landroidx/media3/exoplayer/video/h;->D:Landroid/view/Surface;

    if-eqz v0, :cond_1

    const/4 v0, 0x1

    goto :goto_0

    :cond_1
    const/4 v0, 0x0

    :goto_0
    if-nez p2, :cond_2

    iget-object p2, p0, Landroidx/media3/exoplayer/video/h;->E:Landroidx/media3/exoplayer/video/n;

    if-eqz p2, :cond_2

    const/4 p2, 0x1

    goto :goto_1

    :cond_2
    const/4 p2, 0x0

    :goto_1
    if-nez p2, :cond_3

    if-nez v0, :cond_3

    invoke-virtual {p0, p1}, Landroidx/media3/exoplayer/video/h;->Q(Landroidx/media3/decoder/VideoDecoderOutputBuffer;)V

    goto :goto_3

    :cond_3
    iget v0, p1, Landroidx/media3/decoder/VideoDecoderOutputBuffer;->width:I

    iget v1, p1, Landroidx/media3/decoder/VideoDecoderOutputBuffer;->height:I

    invoke-virtual {p0, v0, v1}, Landroidx/media3/exoplayer/video/h;->b0(II)V

    if-eqz p2, :cond_4

    iget-object p2, p0, Landroidx/media3/exoplayer/video/h;->E:Landroidx/media3/exoplayer/video/n;

    invoke-static {p2}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p2

    check-cast p2, Landroidx/media3/exoplayer/video/n;

    invoke-interface {p2, p1}, Landroidx/media3/exoplayer/video/n;->setOutputBuffer(Landroidx/media3/decoder/VideoDecoderOutputBuffer;)V

    goto :goto_2

    :cond_4
    iget-object p2, p0, Landroidx/media3/exoplayer/video/h;->D:Landroid/view/Surface;

    invoke-static {p2}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p2

    check-cast p2, Landroid/view/Surface;

    invoke-virtual {p0, p1, p2}, Landroidx/media3/exoplayer/video/h;->n0(Landroidx/media3/decoder/VideoDecoderOutputBuffer;Landroid/view/Surface;)V

    :goto_2
    iput p3, p0, Landroidx/media3/exoplayer/video/h;->T:I

    iget-object p1, p0, Landroidx/media3/exoplayer/video/h;->X:Landroidx/media3/exoplayer/n;

    iget p2, p1, Landroidx/media3/exoplayer/n;->e:I

    add-int/2addr p2, p4

    iput p2, p1, Landroidx/media3/exoplayer/n;->e:I

    invoke-direct {p0}, Landroidx/media3/exoplayer/video/h;->a0()V

    :goto_3
    return-void
.end method

.method public abstract n0(Landroidx/media3/decoder/VideoDecoderOutputBuffer;Landroid/view/Surface;)V
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/decoder/DecoderException;
        }
    .end annotation
.end method

.method public abstract p0(I)V
.end method

.method public final q0()V
    .locals 5

    iget-wide v0, p0, Landroidx/media3/exoplayer/video/h;->r:J

    const-wide/16 v2, 0x0

    cmp-long v4, v0, v2

    if-lez v4, :cond_0

    invoke-static {}, Landroid/os/SystemClock;->elapsedRealtime()J

    move-result-wide v0

    iget-wide v2, p0, Landroidx/media3/exoplayer/video/h;->r:J

    add-long/2addr v0, v2

    goto :goto_0

    :cond_0
    const-wide v0, -0x7fffffffffffffffL    # -4.9E-324

    :goto_0
    iput-wide v0, p0, Landroidx/media3/exoplayer/video/h;->M:J

    return-void
.end method

.method public final r0(Ljava/lang/Object;)V
    .locals 2
    .param p1    # Ljava/lang/Object;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    instance-of v0, p1, Landroid/view/Surface;

    const/4 v1, 0x0

    if-eqz v0, :cond_0

    move-object v0, p1

    check-cast v0, Landroid/view/Surface;

    iput-object v0, p0, Landroidx/media3/exoplayer/video/h;->D:Landroid/view/Surface;

    iput-object v1, p0, Landroidx/media3/exoplayer/video/h;->E:Landroidx/media3/exoplayer/video/n;

    const/4 v0, 0x1

    iput v0, p0, Landroidx/media3/exoplayer/video/h;->B:I

    goto :goto_0

    :cond_0
    instance-of v0, p1, Landroidx/media3/exoplayer/video/n;

    if-eqz v0, :cond_1

    iput-object v1, p0, Landroidx/media3/exoplayer/video/h;->D:Landroid/view/Surface;

    move-object v0, p1

    check-cast v0, Landroidx/media3/exoplayer/video/n;

    iput-object v0, p0, Landroidx/media3/exoplayer/video/h;->E:Landroidx/media3/exoplayer/video/n;

    const/4 v0, 0x0

    iput v0, p0, Landroidx/media3/exoplayer/video/h;->B:I

    goto :goto_0

    :cond_1
    iput-object v1, p0, Landroidx/media3/exoplayer/video/h;->D:Landroid/view/Surface;

    iput-object v1, p0, Landroidx/media3/exoplayer/video/h;->E:Landroidx/media3/exoplayer/video/n;

    const/4 p1, -0x1

    iput p1, p0, Landroidx/media3/exoplayer/video/h;->B:I

    move-object p1, v1

    :goto_0
    iget-object v0, p0, Landroidx/media3/exoplayer/video/h;->C:Ljava/lang/Object;

    if-eq v0, p1, :cond_4

    iput-object p1, p0, Landroidx/media3/exoplayer/video/h;->C:Ljava/lang/Object;

    if-eqz p1, :cond_3

    iget-object p1, p0, Landroidx/media3/exoplayer/video/h;->y:Landroidx/media3/decoder/g;

    if-eqz p1, :cond_2

    iget p1, p0, Landroidx/media3/exoplayer/video/h;->B:I

    invoke-virtual {p0, p1}, Landroidx/media3/exoplayer/video/h;->p0(I)V

    :cond_2
    invoke-virtual {p0}, Landroidx/media3/exoplayer/video/h;->f0()V

    goto :goto_1

    :cond_3
    invoke-virtual {p0}, Landroidx/media3/exoplayer/video/h;->g0()V

    goto :goto_1

    :cond_4
    if-eqz p1, :cond_5

    invoke-virtual {p0}, Landroidx/media3/exoplayer/video/h;->h0()V

    :cond_5
    :goto_1
    return-void
.end method

.method public render(JJ)V
    .locals 3
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/exoplayer/ExoPlaybackException;
        }
    .end annotation

    iget-boolean v0, p0, Landroidx/media3/exoplayer/video/h;->P:Z

    if-eqz v0, :cond_0

    return-void

    :cond_0
    iget-object v0, p0, Landroidx/media3/exoplayer/video/h;->w:Landroidx/media3/common/y;

    if-nez v0, :cond_3

    invoke-virtual {p0}, Landroidx/media3/exoplayer/m;->t()Landroidx/media3/exoplayer/t1;

    move-result-object v0

    iget-object v1, p0, Landroidx/media3/exoplayer/video/h;->v:Landroidx/media3/decoder/DecoderInputBuffer;

    invoke-virtual {v1}, Landroidx/media3/decoder/DecoderInputBuffer;->clear()V

    iget-object v1, p0, Landroidx/media3/exoplayer/video/h;->v:Landroidx/media3/decoder/DecoderInputBuffer;

    const/4 v2, 0x2

    invoke-virtual {p0, v0, v1, v2}, Landroidx/media3/exoplayer/m;->K(Landroidx/media3/exoplayer/t1;Landroidx/media3/decoder/DecoderInputBuffer;I)I

    move-result v1

    const/4 v2, -0x5

    if-ne v1, v2, :cond_1

    invoke-virtual {p0, v0}, Landroidx/media3/exoplayer/video/h;->e0(Landroidx/media3/exoplayer/t1;)V

    goto :goto_0

    :cond_1
    const/4 p1, -0x4

    if-ne v1, p1, :cond_2

    iget-object p1, p0, Landroidx/media3/exoplayer/video/h;->v:Landroidx/media3/decoder/DecoderInputBuffer;

    invoke-virtual {p1}, Landroidx/media3/decoder/a;->isEndOfStream()Z

    move-result p1

    invoke-static {p1}, Le2/a;->g(Z)V

    const/4 p1, 0x1

    iput-boolean p1, p0, Landroidx/media3/exoplayer/video/h;->O:Z

    iput-boolean p1, p0, Landroidx/media3/exoplayer/video/h;->P:Z

    :cond_2
    return-void

    :cond_3
    :goto_0
    invoke-direct {p0}, Landroidx/media3/exoplayer/video/h;->Y()V

    iget-object v0, p0, Landroidx/media3/exoplayer/video/h;->y:Landroidx/media3/decoder/g;

    if-eqz v0, :cond_6

    :try_start_0
    const-string v0, "drainAndFeed"

    invoke-static {v0}, Le2/j0;->a(Ljava/lang/String;)V

    :goto_1
    invoke-direct {p0, p1, p2, p3, p4}, Landroidx/media3/exoplayer/video/h;->P(JJ)Z

    move-result v0

    if-eqz v0, :cond_4

    goto :goto_1

    :cond_4
    :goto_2
    invoke-direct {p0}, Landroidx/media3/exoplayer/video/h;->R()Z

    move-result p1

    if-eqz p1, :cond_5

    goto :goto_2

    :cond_5
    invoke-static {}, Le2/j0;->c()V
    :try_end_0
    .catch Landroidx/media3/decoder/DecoderException; {:try_start_0 .. :try_end_0} :catch_0

    iget-object p1, p0, Landroidx/media3/exoplayer/video/h;->X:Landroidx/media3/exoplayer/n;

    invoke-virtual {p1}, Landroidx/media3/exoplayer/n;->c()V

    goto :goto_3

    :catch_0
    move-exception p1

    const-string p2, "DecoderVideoRenderer"

    const-string p3, "Video codec error"

    invoke-static {p2, p3, p1}, Le2/o;->d(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Throwable;)V

    iget-object p2, p0, Landroidx/media3/exoplayer/video/h;->t:Landroidx/media3/exoplayer/video/f0$a;

    invoke-virtual {p2, p1}, Landroidx/media3/exoplayer/video/f0$a;->C(Ljava/lang/Exception;)V

    iget-object p2, p0, Landroidx/media3/exoplayer/video/h;->w:Landroidx/media3/common/y;

    const/16 p3, 0xfa3

    invoke-virtual {p0, p1, p2, p3}, Landroidx/media3/exoplayer/m;->p(Ljava/lang/Throwable;Landroidx/media3/common/y;I)Landroidx/media3/exoplayer/ExoPlaybackException;

    move-result-object p1

    throw p1

    :cond_6
    :goto_3
    return-void
.end method

.method public t0(JJ)Z
    .locals 0

    invoke-static {p1, p2}, Landroidx/media3/exoplayer/video/h;->V(J)Z

    move-result p1

    return p1
.end method

.method public u0(JJ)Z
    .locals 0

    invoke-static {p1, p2}, Landroidx/media3/exoplayer/video/h;->U(J)Z

    move-result p1

    return p1
.end method

.method public final v0(J)Z
    .locals 8

    invoke-virtual {p0}, Landroidx/media3/exoplayer/m;->getState()I

    move-result v0

    const/4 v1, 0x2

    const/4 v2, 0x0

    const/4 v3, 0x1

    if-ne v0, v1, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    iget v1, p0, Landroidx/media3/exoplayer/video/h;->K:I

    if-eqz v1, :cond_4

    if-eq v1, v3, :cond_3

    const/4 v4, 0x3

    if-ne v1, v4, :cond_2

    invoke-static {}, Landroid/os/SystemClock;->elapsedRealtime()J

    move-result-wide v4

    invoke-static {v4, v5}, Le2/u0;->S0(J)J

    move-result-wide v4

    iget-wide v6, p0, Landroidx/media3/exoplayer/video/h;->V:J

    sub-long/2addr v4, v6

    if-eqz v0, :cond_1

    invoke-virtual {p0, p1, p2, v4, v5}, Landroidx/media3/exoplayer/video/h;->w0(JJ)Z

    move-result p1

    if-eqz p1, :cond_1

    const/4 v2, 0x1

    :cond_1
    return v2

    :cond_2
    new-instance p1, Ljava/lang/IllegalStateException;

    invoke-direct {p1}, Ljava/lang/IllegalStateException;-><init>()V

    throw p1

    :cond_3
    return v3

    :cond_4
    return v0
.end method

.method public w0(JJ)Z
    .locals 1

    invoke-static {p1, p2}, Landroidx/media3/exoplayer/video/h;->U(J)Z

    move-result p1

    if-eqz p1, :cond_0

    const-wide/32 p1, 0x186a0

    cmp-long v0, p3, p1

    if-lez v0, :cond_0

    const/4 p1, 0x1

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    :goto_0
    return p1
.end method

.method public x0(Landroidx/media3/decoder/VideoDecoderOutputBuffer;)V
    .locals 2

    iget-object v0, p0, Landroidx/media3/exoplayer/video/h;->X:Landroidx/media3/exoplayer/n;

    iget v1, v0, Landroidx/media3/exoplayer/n;->f:I

    add-int/lit8 v1, v1, 0x1

    iput v1, v0, Landroidx/media3/exoplayer/n;->f:I

    invoke-virtual {p1}, Landroidx/media3/decoder/VideoDecoderOutputBuffer;->release()V

    return-void
.end method

.method public y0(II)V
    .locals 2

    iget-object v0, p0, Landroidx/media3/exoplayer/video/h;->X:Landroidx/media3/exoplayer/n;

    iget v1, v0, Landroidx/media3/exoplayer/n;->h:I

    add-int/2addr v1, p1

    iput v1, v0, Landroidx/media3/exoplayer/n;->h:I

    add-int/2addr p1, p2

    iget p2, v0, Landroidx/media3/exoplayer/n;->g:I

    add-int/2addr p2, p1

    iput p2, v0, Landroidx/media3/exoplayer/n;->g:I

    iget p2, p0, Landroidx/media3/exoplayer/video/h;->S:I

    add-int/2addr p2, p1

    iput p2, p0, Landroidx/media3/exoplayer/video/h;->S:I

    iget p2, p0, Landroidx/media3/exoplayer/video/h;->T:I

    add-int/2addr p2, p1

    iput p2, p0, Landroidx/media3/exoplayer/video/h;->T:I

    iget p1, v0, Landroidx/media3/exoplayer/n;->i:I

    invoke-static {p2, p1}, Ljava/lang/Math;->max(II)I

    move-result p1

    iput p1, v0, Landroidx/media3/exoplayer/n;->i:I

    iget p1, p0, Landroidx/media3/exoplayer/video/h;->s:I

    if-lez p1, :cond_0

    iget p2, p0, Landroidx/media3/exoplayer/video/h;->S:I

    if-lt p2, p1, :cond_0

    invoke-direct {p0}, Landroidx/media3/exoplayer/video/h;->Z()V

    :cond_0
    return-void
.end method

.method public z()V
    .locals 3

    const/4 v0, 0x0

    iput-object v0, p0, Landroidx/media3/exoplayer/video/h;->w:Landroidx/media3/common/y;

    iput-object v0, p0, Landroidx/media3/exoplayer/video/h;->Q:Landroidx/media3/common/t0;

    const/4 v1, 0x0

    invoke-direct {p0, v1}, Landroidx/media3/exoplayer/video/h;->W(I)V

    :try_start_0
    invoke-direct {p0, v0}, Landroidx/media3/exoplayer/video/h;->s0(Landroidx/media3/exoplayer/drm/DrmSession;)V

    invoke-virtual {p0}, Landroidx/media3/exoplayer/video/h;->l0()V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    iget-object v0, p0, Landroidx/media3/exoplayer/video/h;->t:Landroidx/media3/exoplayer/video/f0$a;

    iget-object v1, p0, Landroidx/media3/exoplayer/video/h;->X:Landroidx/media3/exoplayer/n;

    invoke-virtual {v0, v1}, Landroidx/media3/exoplayer/video/f0$a;->m(Landroidx/media3/exoplayer/n;)V

    return-void

    :catchall_0
    move-exception v0

    iget-object v1, p0, Landroidx/media3/exoplayer/video/h;->t:Landroidx/media3/exoplayer/video/f0$a;

    iget-object v2, p0, Landroidx/media3/exoplayer/video/h;->X:Landroidx/media3/exoplayer/n;

    invoke-virtual {v1, v2}, Landroidx/media3/exoplayer/video/f0$a;->m(Landroidx/media3/exoplayer/n;)V

    throw v0
.end method
