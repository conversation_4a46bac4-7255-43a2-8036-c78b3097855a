.class public final synthetic Landroidx/media3/exoplayer/video/q;
.super Ljava/lang/Object;

# interfaces
.implements Landroidx/media3/exoplayer/video/r$c$a;


# instance fields
.field public final synthetic a:Landroidx/media3/exoplayer/video/r;


# direct methods
.method public synthetic constructor <init>(Landroidx/media3/exoplayer/video/r;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Landroidx/media3/exoplayer/video/q;->a:Landroidx/media3/exoplayer/video/r;

    return-void
.end method


# virtual methods
.method public final a(Landroid/view/Display;)V
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/video/q;->a:Landroidx/media3/exoplayer/video/r;

    invoke-static {v0, p1}, Landroidx/media3/exoplayer/video/r;->a(Landroidx/media3/exoplayer/video/r;Landroid/view/Display;)V

    return-void
.end method
