.class public interface abstract Landroidx/media3/exoplayer/video/r$c;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/media3/exoplayer/video/r;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "c"
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Landroidx/media3/exoplayer/video/r$c$a;
    }
.end annotation


# virtual methods
.method public abstract a(Landroidx/media3/exoplayer/video/r$c$a;)V
.end method

.method public abstract unregister()V
.end method
