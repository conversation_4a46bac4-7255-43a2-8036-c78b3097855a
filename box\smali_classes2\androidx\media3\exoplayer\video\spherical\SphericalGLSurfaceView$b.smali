.class public interface abstract Landroidx/media3/exoplayer/video/spherical/SphericalGLSurfaceView$b;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/media3/exoplayer/video/spherical/SphericalGLSurfaceView;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "b"
.end annotation


# virtual methods
.method public abstract n(Landroid/view/Surface;)V
.end method

.method public abstract q(Landroid/view/Surface;)V
.end method
