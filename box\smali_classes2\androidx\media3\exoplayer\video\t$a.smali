.class public interface abstract Landroidx/media3/exoplayer/video/t$a;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/media3/exoplayer/video/t;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "a"
.end annotation


# virtual methods
.method public abstract b()V
.end method

.method public abstract i(JJJZ)V
.end method

.method public abstract onVideoSizeChanged(Landroidx/media3/common/t0;)V
.end method
