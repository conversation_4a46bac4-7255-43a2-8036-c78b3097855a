.class public final Landroidx/media3/exoplayer/w1;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Landroidx/media3/exoplayer/w1$b;
    }
.end annotation


# instance fields
.field public final a:J

.field public final b:F

.field public final c:J


# direct methods
.method public constructor <init>(Landroidx/media3/exoplayer/w1$b;)V
    .locals 2

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    invoke-static {p1}, Landroidx/media3/exoplayer/w1$b;->a(Landroidx/media3/exoplayer/w1$b;)J

    move-result-wide v0

    iput-wide v0, p0, Landroidx/media3/exoplayer/w1;->a:J

    invoke-static {p1}, Landroidx/media3/exoplayer/w1$b;->b(Landroidx/media3/exoplayer/w1$b;)F

    move-result v0

    iput v0, p0, Landroidx/media3/exoplayer/w1;->b:F

    invoke-static {p1}, Landroidx/media3/exoplayer/w1$b;->c(Landroidx/media3/exoplayer/w1$b;)J

    move-result-wide v0

    iput-wide v0, p0, Landroidx/media3/exoplayer/w1;->c:J

    return-void
.end method

.method public synthetic constructor <init>(Landroidx/media3/exoplayer/w1$b;Landroidx/media3/exoplayer/w1$a;)V
    .locals 0

    invoke-direct {p0, p1}, Landroidx/media3/exoplayer/w1;-><init>(Landroidx/media3/exoplayer/w1$b;)V

    return-void
.end method


# virtual methods
.method public a()Landroidx/media3/exoplayer/w1$b;
    .locals 2

    new-instance v0, Landroidx/media3/exoplayer/w1$b;

    const/4 v1, 0x0

    invoke-direct {v0, p0, v1}, Landroidx/media3/exoplayer/w1$b;-><init>(Landroidx/media3/exoplayer/w1;Landroidx/media3/exoplayer/w1$a;)V

    return-object v0
.end method

.method public equals(Ljava/lang/Object;)Z
    .locals 7
    .param p1    # Ljava/lang/Object;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    const/4 v0, 0x1

    if-ne p0, p1, :cond_0

    return v0

    :cond_0
    instance-of v1, p1, Landroidx/media3/exoplayer/w1;

    const/4 v2, 0x0

    if-nez v1, :cond_1

    return v2

    :cond_1
    check-cast p1, Landroidx/media3/exoplayer/w1;

    iget-wide v3, p0, Landroidx/media3/exoplayer/w1;->a:J

    iget-wide v5, p1, Landroidx/media3/exoplayer/w1;->a:J

    cmp-long v1, v3, v5

    if-nez v1, :cond_2

    iget v1, p0, Landroidx/media3/exoplayer/w1;->b:F

    iget v3, p1, Landroidx/media3/exoplayer/w1;->b:F

    cmpl-float v1, v1, v3

    if-nez v1, :cond_2

    iget-wide v3, p0, Landroidx/media3/exoplayer/w1;->c:J

    iget-wide v5, p1, Landroidx/media3/exoplayer/w1;->c:J

    cmp-long p1, v3, v5

    if-nez p1, :cond_2

    goto :goto_0

    :cond_2
    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method public hashCode()I
    .locals 3

    const/4 v0, 0x3

    new-array v0, v0, [Ljava/lang/Object;

    iget-wide v1, p0, Landroidx/media3/exoplayer/w1;->a:J

    invoke-static {v1, v2}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    move-result-object v1

    const/4 v2, 0x0

    aput-object v1, v0, v2

    iget v1, p0, Landroidx/media3/exoplayer/w1;->b:F

    invoke-static {v1}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    move-result-object v1

    const/4 v2, 0x1

    aput-object v1, v0, v2

    iget-wide v1, p0, Landroidx/media3/exoplayer/w1;->c:J

    invoke-static {v1, v2}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    move-result-object v1

    const/4 v2, 0x2

    aput-object v1, v0, v2

    invoke-static {v0}, Lcom/google/common/base/j;->b([Ljava/lang/Object;)I

    move-result v0

    return v0
.end method
