.class public final synthetic Landroidx/media3/exoplayer/x;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/google/common/base/q;


# instance fields
.field public final synthetic a:Landroidx/media3/exoplayer/a3;


# direct methods
.method public synthetic constructor <init>(Landroidx/media3/exoplayer/a3;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Landroidx/media3/exoplayer/x;->a:Landroidx/media3/exoplayer/a3;

    return-void
.end method


# virtual methods
.method public final get()Ljava/lang/Object;
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/x;->a:Landroidx/media3/exoplayer/a3;

    invoke-static {v0}, Landroidx/media3/exoplayer/u$b;->b(Landroidx/media3/exoplayer/a3;)Landroidx/media3/exoplayer/a3;

    move-result-object v0

    return-object v0
.end method
