.class public final synthetic Landroidx/media3/exoplayer/z;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/google/common/base/q;


# instance fields
.field public final synthetic a:Landroid/content/Context;


# direct methods
.method public synthetic constructor <init>(Landroid/content/Context;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Landroidx/media3/exoplayer/z;->a:Landroid/content/Context;

    return-void
.end method


# virtual methods
.method public final get()Ljava/lang/Object;
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/z;->a:Landroid/content/Context;

    invoke-static {v0}, Landroidx/media3/exoplayer/u$b;->d(Landroid/content/Context;)Lx2/e0;

    move-result-object v0

    return-object v0
.end method
