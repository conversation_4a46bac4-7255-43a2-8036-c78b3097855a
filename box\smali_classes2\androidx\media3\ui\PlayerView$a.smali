.class public Landroidx/media3/ui/PlayerView$a;
.super Ljava/lang/Object;


# annotations
.annotation build Landroidx/annotation/RequiresApi;
    value = 0x22
.end annotation

.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/media3/ui/PlayerView;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "a"
.end annotation


# direct methods
.method public static a(Landroid/view/SurfaceView;)V
    .locals 1

    const/4 v0, 0x2

    invoke-static {p0, v0}, Landroidx/media3/ui/g0;->a(Landroid/view/SurfaceView;I)V

    return-void
.end method
