.class public final Landroidx/media3/ui/R$attr;
.super Ljava/lang/Object;


# static fields
.field public static ad_marker_color:I = 0x7f040033

.field public static ad_marker_width:I = 0x7f040034

.field public static alpha:I = 0x7f040040

.field public static animation_enabled:I = 0x7f04004b

.field public static artwork_display_mode:I = 0x7f04005a

.field public static auto_show:I = 0x7f040067

.field public static backgroundTint:I = 0x7f040073

.field public static bar_gravity:I = 0x7f04009e

.field public static bar_height:I = 0x7f04009f

.field public static buffered_color:I = 0x7f0401dd

.field public static controller_layout_id:I = 0x7f0402b4

.field public static default_artwork:I = 0x7f0402e9

.field public static fastScrollEnabled:I = 0x7f040365

.field public static fastScrollHorizontalThumbDrawable:I = 0x7f040366

.field public static fastScrollHorizontalTrackDrawable:I = 0x7f040367

.field public static fastScrollVerticalThumbDrawable:I = 0x7f040368

.field public static fastScrollVerticalTrackDrawable:I = 0x7f040369

.field public static font:I = 0x7f040399

.field public static fontProviderAuthority:I = 0x7f04039b

.field public static fontProviderCerts:I = 0x7f04039c

.field public static fontProviderFetchStrategy:I = 0x7f04039d

.field public static fontProviderFetchTimeout:I = 0x7f04039e

.field public static fontProviderPackage:I = 0x7f04039f

.field public static fontProviderQuery:I = 0x7f0403a0

.field public static fontProviderSystemFontFamily:I = 0x7f0403a1

.field public static fontStyle:I = 0x7f0403a2

.field public static fontVariationSettings:I = 0x7f0403a3

.field public static fontWeight:I = 0x7f0403a4

.field public static hide_during_ads:I = 0x7f0403c5

.field public static hide_on_touch:I = 0x7f0403c6

.field public static keep_content_on_player_reset:I = 0x7f040456

.field public static lStar:I = 0x7f04045a

.field public static layoutManager:I = 0x7f040469

.field public static nestedScrollViewStyle:I = 0x7f040581

.field public static played_ad_marker_color:I = 0x7f0405b7

.field public static played_color:I = 0x7f0405b8

.field public static player_layout_id:I = 0x7f0405b9

.field public static queryPatterns:I = 0x7f0405ec

.field public static recyclerViewStyle:I = 0x7f040602

.field public static repeat_toggle_modes:I = 0x7f040608

.field public static resize_mode:I = 0x7f040609

.field public static reverseLayout:I = 0x7f04060c

.field public static scrubber_color:I = 0x7f040626

.field public static scrubber_disabled_size:I = 0x7f040627

.field public static scrubber_dragged_size:I = 0x7f040628

.field public static scrubber_drawable:I = 0x7f040629

.field public static scrubber_enabled_size:I = 0x7f04062a

.field public static shortcutMatchRequired:I = 0x7f04065b

.field public static show_buffering:I = 0x7f04066c

.field public static show_fastforward_button:I = 0x7f04066d

.field public static show_next_button:I = 0x7f04066e

.field public static show_previous_button:I = 0x7f04066f

.field public static show_rewind_button:I = 0x7f040670

.field public static show_shuffle_button:I = 0x7f040671

.field public static show_subtitle_button:I = 0x7f040672

.field public static show_timeout:I = 0x7f040673

.field public static show_vr_button:I = 0x7f040674

.field public static shutter_background_color:I = 0x7f040676

.field public static spanCount:I = 0x7f040685

.field public static stackFromEnd:I = 0x7f0406a2

.field public static surface_type:I = 0x7f0406ce

.field public static time_bar_min_update_interval:I = 0x7f040759

.field public static touch_target_height:I = 0x7f04078b

.field public static ttcIndex:I = 0x7f0407a3

.field public static unplayed_color:I = 0x7f0407ac

.field public static use_artwork:I = 0x7f0407b3

.field public static use_controller:I = 0x7f0407b5


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
