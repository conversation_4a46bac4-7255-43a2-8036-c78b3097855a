.class public final Landroidx/media3/ui/R$color;
.super Ljava/lang/Object;


# static fields
.field public static androidx_core_ripple_material_light:I = 0x7f060025

.field public static androidx_core_secondary_text_default_material_light:I = 0x7f060026

.field public static exo_black_opacity_60:I = 0x7f06018a

.field public static exo_black_opacity_70:I = 0x7f06018b

.field public static exo_bottom_bar_background:I = 0x7f06018c

.field public static exo_edit_mode_background_color:I = 0x7f06018d

.field public static exo_error_message_background_color:I = 0x7f06018e

.field public static exo_styled_error_message_background:I = 0x7f06018f

.field public static exo_white:I = 0x7f060190

.field public static exo_white_opacity_70:I = 0x7f060191

.field public static notification_action_color_filter:I = 0x7f0604c4

.field public static notification_icon_bg_color:I = 0x7f0604c6

.field public static notification_material_background_media_default_color:I = 0x7f0604c7

.field public static primary_text_default_material_dark:I = 0x7f0604f1

.field public static ripple_material_light:I = 0x7f060659

.field public static secondary_text_default_material_dark:I = 0x7f06065b

.field public static secondary_text_default_material_light:I = 0x7f06065c


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
