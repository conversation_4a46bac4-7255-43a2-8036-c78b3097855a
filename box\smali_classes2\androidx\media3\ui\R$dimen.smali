.class public final Landroidx/media3/ui/R$dimen;
.super Ljava/lang/Object;


# static fields
.field public static compat_button_inset_horizontal_material:I = 0x7f07006e

.field public static compat_button_inset_vertical_material:I = 0x7f07006f

.field public static compat_button_padding_horizontal_material:I = 0x7f070070

.field public static compat_button_padding_vertical_material:I = 0x7f070071

.field public static compat_control_corner_material:I = 0x7f070072

.field public static compat_notification_large_icon_max_height:I = 0x7f070073

.field public static compat_notification_large_icon_max_width:I = 0x7f070074

.field public static exo_error_message_height:I = 0x7f0700bf

.field public static exo_error_message_margin_bottom:I = 0x7f0700c0

.field public static exo_error_message_text_padding_horizontal:I = 0x7f0700c1

.field public static exo_error_message_text_padding_vertical:I = 0x7f0700c2

.field public static exo_error_message_text_size:I = 0x7f0700c3

.field public static exo_icon_horizontal_margin:I = 0x7f0700c4

.field public static exo_icon_padding:I = 0x7f0700c5

.field public static exo_icon_padding_bottom:I = 0x7f0700c6

.field public static exo_icon_size:I = 0x7f0700c7

.field public static exo_icon_text_size:I = 0x7f0700c8

.field public static exo_media_button_height:I = 0x7f0700c9

.field public static exo_media_button_width:I = 0x7f0700ca

.field public static exo_setting_width:I = 0x7f0700cb

.field public static exo_settings_height:I = 0x7f0700cc

.field public static exo_settings_icon_size:I = 0x7f0700cd

.field public static exo_settings_main_text_size:I = 0x7f0700ce

.field public static exo_settings_offset:I = 0x7f0700cf

.field public static exo_settings_sub_text_size:I = 0x7f0700d0

.field public static exo_settings_text_height:I = 0x7f0700d1

.field public static exo_small_icon_height:I = 0x7f0700d2

.field public static exo_small_icon_horizontal_margin:I = 0x7f0700d3

.field public static exo_small_icon_padding_horizontal:I = 0x7f0700d4

.field public static exo_small_icon_padding_vertical:I = 0x7f0700d5

.field public static exo_small_icon_width:I = 0x7f0700d6

.field public static exo_styled_bottom_bar_height:I = 0x7f0700d7

.field public static exo_styled_bottom_bar_margin_top:I = 0x7f0700d8

.field public static exo_styled_bottom_bar_time_padding:I = 0x7f0700d9

.field public static exo_styled_controls_padding:I = 0x7f0700da

.field public static exo_styled_minimal_controls_margin_bottom:I = 0x7f0700db

.field public static exo_styled_progress_bar_height:I = 0x7f0700dc

.field public static exo_styled_progress_dragged_thumb_size:I = 0x7f0700dd

.field public static exo_styled_progress_enabled_thumb_size:I = 0x7f0700de

.field public static exo_styled_progress_layout_height:I = 0x7f0700df

.field public static exo_styled_progress_margin_bottom:I = 0x7f0700e0

.field public static exo_styled_progress_touch_target_height:I = 0x7f0700e1

.field public static fastscroll_default_thickness:I = 0x7f0700e3

.field public static fastscroll_margin:I = 0x7f0700e4

.field public static fastscroll_minimum_range:I = 0x7f0700e5

.field public static item_touch_helper_max_drag_scroll_per_frame:I = 0x7f070100

.field public static item_touch_helper_swipe_escape_max_velocity:I = 0x7f070101

.field public static item_touch_helper_swipe_escape_velocity:I = 0x7f070102

.field public static notification_action_icon_size:I = 0x7f07037f

.field public static notification_action_text_size:I = 0x7f070380

.field public static notification_big_circle_margin:I = 0x7f070381

.field public static notification_content_margin_start:I = 0x7f070382

.field public static notification_large_icon_height:I = 0x7f070383

.field public static notification_large_icon_width:I = 0x7f070384

.field public static notification_main_column_padding_top:I = 0x7f070385

.field public static notification_media_narrow_margin:I = 0x7f070386

.field public static notification_right_icon_size:I = 0x7f070387

.field public static notification_right_side_padding_top:I = 0x7f070388

.field public static notification_small_icon_background_padding:I = 0x7f070389

.field public static notification_small_icon_size_as_large:I = 0x7f07038a

.field public static notification_subtext_size:I = 0x7f07038b

.field public static notification_top_pad:I = 0x7f07038c

.field public static notification_top_pad_large_text:I = 0x7f07038d


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
