.class public final Landroidx/media3/ui/R$layout;
.super Ljava/lang/Object;


# static fields
.field public static custom_dialog:I = 0x7f0d00af

.field public static exo_legacy_player_control_view:I = 0x7f0d0116

.field public static exo_list_divider:I = 0x7f0d0117

.field public static exo_player_control_ffwd_button:I = 0x7f0d0118

.field public static exo_player_control_rewind_button:I = 0x7f0d0119

.field public static exo_player_control_view:I = 0x7f0d011a

.field public static exo_player_view:I = 0x7f0d011b

.field public static exo_styled_settings_list:I = 0x7f0d0120

.field public static exo_styled_settings_list_item:I = 0x7f0d0121

.field public static exo_styled_sub_settings_list_item:I = 0x7f0d0122

.field public static exo_track_selection_dialog:I = 0x7f0d0123

.field public static notification_action:I = 0x7f0d0392

.field public static notification_action_tombstone:I = 0x7f0d0393

.field public static notification_media_action:I = 0x7f0d0398

.field public static notification_media_cancel_action:I = 0x7f0d0399

.field public static notification_template_big_media:I = 0x7f0d039c

.field public static notification_template_big_media_custom:I = 0x7f0d039d

.field public static notification_template_big_media_narrow:I = 0x7f0d039e

.field public static notification_template_big_media_narrow_custom:I = 0x7f0d039f

.field public static notification_template_custom_big:I = 0x7f0d03a0

.field public static notification_template_icon_group:I = 0x7f0d03a1

.field public static notification_template_lines_media:I = 0x7f0d03a2

.field public static notification_template_media:I = 0x7f0d03a3

.field public static notification_template_media_custom:I = 0x7f0d03a4

.field public static notification_template_part_chronometer:I = 0x7f0d03a5

.field public static notification_template_part_time:I = 0x7f0d03a6


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
