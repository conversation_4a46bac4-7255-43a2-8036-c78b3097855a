.class public final Landroidx/media3/ui/R$string;
.super Ljava/lang/Object;


# static fields
.field public static exo_controls_cc_disabled_description:I = 0x7f1201ee

.field public static exo_controls_cc_enabled_description:I = 0x7f1201ef

.field public static exo_controls_custom_playback_speed:I = 0x7f1201f0

.field public static exo_controls_fastforward_description:I = 0x7f1201f1

.field public static exo_controls_fullscreen_enter_description:I = 0x7f1201f2

.field public static exo_controls_fullscreen_exit_description:I = 0x7f1201f3

.field public static exo_controls_hide:I = 0x7f1201f4

.field public static exo_controls_next_description:I = 0x7f1201f5

.field public static exo_controls_overflow_hide_description:I = 0x7f1201f6

.field public static exo_controls_overflow_show_description:I = 0x7f1201f7

.field public static exo_controls_pause_description:I = 0x7f1201f8

.field public static exo_controls_play_description:I = 0x7f1201f9

.field public static exo_controls_playback_speed:I = 0x7f1201fa

.field public static exo_controls_previous_description:I = 0x7f1201fb

.field public static exo_controls_repeat_all_description:I = 0x7f1201fc

.field public static exo_controls_repeat_off_description:I = 0x7f1201fd

.field public static exo_controls_repeat_one_description:I = 0x7f1201fe

.field public static exo_controls_rewind_description:I = 0x7f1201ff

.field public static exo_controls_seek_bar_description:I = 0x7f120200

.field public static exo_controls_settings_description:I = 0x7f120201

.field public static exo_controls_show:I = 0x7f120202

.field public static exo_controls_shuffle_off_description:I = 0x7f120203

.field public static exo_controls_shuffle_on_description:I = 0x7f120204

.field public static exo_controls_stop_description:I = 0x7f120205

.field public static exo_controls_time_placeholder:I = 0x7f120206

.field public static exo_controls_vr_description:I = 0x7f120207

.field public static exo_item_list:I = 0x7f120211

.field public static exo_track_bitrate:I = 0x7f120212

.field public static exo_track_mono:I = 0x7f120213

.field public static exo_track_resolution:I = 0x7f120214

.field public static exo_track_role_alternate:I = 0x7f120215

.field public static exo_track_role_closed_captions:I = 0x7f120216

.field public static exo_track_role_commentary:I = 0x7f120217

.field public static exo_track_role_supplementary:I = 0x7f120218

.field public static exo_track_selection_auto:I = 0x7f120219

.field public static exo_track_selection_none:I = 0x7f12021a

.field public static exo_track_selection_title_audio:I = 0x7f12021b

.field public static exo_track_selection_title_text:I = 0x7f12021c

.field public static exo_track_selection_title_video:I = 0x7f12021d

.field public static exo_track_stereo:I = 0x7f12021e

.field public static exo_track_surround:I = 0x7f12021f

.field public static exo_track_surround_5_point_1:I = 0x7f120220

.field public static exo_track_surround_7_point_1:I = 0x7f120221

.field public static exo_track_unknown:I = 0x7f120222

.field public static exo_track_unknown_name:I = 0x7f120223

.field public static status_bar_notification_info_overflow:I = 0x7f120606


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
