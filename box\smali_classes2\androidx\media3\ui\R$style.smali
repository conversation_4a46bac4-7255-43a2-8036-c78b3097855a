.class public final Landroidx/media3/ui/R$style;
.super Ljava/lang/Object;


# static fields
.field public static ExoMediaButton:I = 0x7f130142

.field public static ExoMediaButton_FastForward:I = 0x7f130143

.field public static ExoMediaButton_Next:I = 0x7f130144

.field public static ExoMediaButton_Pause:I = 0x7f130145

.field public static ExoMediaButton_Play:I = 0x7f130146

.field public static ExoMediaButton_Previous:I = 0x7f130147

.field public static ExoMediaButton_Rewind:I = 0x7f130148

.field public static ExoMediaButton_VR:I = 0x7f130149

.field public static ExoStyledControls:I = 0x7f13014a

.field public static ExoStyledControls_Button:I = 0x7f13014b

.field public static ExoStyledControls_Button_Bottom:I = 0x7f13014c

.field public static ExoStyledControls_Button_Bottom_AudioTrack:I = 0x7f13014d

.field public static ExoStyledControls_Button_Bottom_CC:I = 0x7f13014e

.field public static ExoStyledControls_Button_Bottom_FullScreen:I = 0x7f13014f

.field public static ExoStyledControls_Button_Bottom_OverflowHide:I = 0x7f130150

.field public static ExoStyledControls_Button_Bottom_OverflowShow:I = 0x7f130151

.field public static ExoStyledControls_Button_Bottom_PlaybackSpeed:I = 0x7f130152

.field public static ExoStyledControls_Button_Bottom_RepeatToggle:I = 0x7f130153

.field public static ExoStyledControls_Button_Bottom_Settings:I = 0x7f130154

.field public static ExoStyledControls_Button_Bottom_Shuffle:I = 0x7f130155

.field public static ExoStyledControls_Button_Bottom_VR:I = 0x7f130156

.field public static ExoStyledControls_Button_Center:I = 0x7f130157

.field public static ExoStyledControls_Button_Center_FfwdWithAmount:I = 0x7f130158

.field public static ExoStyledControls_Button_Center_Next:I = 0x7f130159

.field public static ExoStyledControls_Button_Center_PlayPause:I = 0x7f13015a

.field public static ExoStyledControls_Button_Center_Previous:I = 0x7f13015b

.field public static ExoStyledControls_Button_Center_RewWithAmount:I = 0x7f13015c

.field public static ExoStyledControls_TimeBar:I = 0x7f13015d

.field public static ExoStyledControls_TimeText:I = 0x7f13015e

.field public static ExoStyledControls_TimeText_Duration:I = 0x7f13015f

.field public static ExoStyledControls_TimeText_Position:I = 0x7f130160

.field public static ExoStyledControls_TimeText_Separator:I = 0x7f130161

.field public static TextAppearance_Compat_Notification:I = 0x7f130221

.field public static TextAppearance_Compat_Notification_Info:I = 0x7f130222

.field public static TextAppearance_Compat_Notification_Info_Media:I = 0x7f130223

.field public static TextAppearance_Compat_Notification_Line2:I = 0x7f130224

.field public static TextAppearance_Compat_Notification_Line2_Media:I = 0x7f130225

.field public static TextAppearance_Compat_Notification_Media:I = 0x7f130226

.field public static TextAppearance_Compat_Notification_Time:I = 0x7f130227

.field public static TextAppearance_Compat_Notification_Time_Media:I = 0x7f130228

.field public static TextAppearance_Compat_Notification_Title:I = 0x7f130229

.field public static TextAppearance_Compat_Notification_Title_Media:I = 0x7f13022a

.field public static Widget_Compat_NotificationActionContainer:I = 0x7f1303a6

.field public static Widget_Compat_NotificationActionText:I = 0x7f1303a7


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
