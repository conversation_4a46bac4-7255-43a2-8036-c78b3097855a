.class public final Landroidx/media3/ui/R$styleable;
.super Ljava/lang/Object;


# static fields
.field public static AspectRatioFrameLayout:[I = null

.field public static AspectRatioFrameLayout_resize_mode:I = 0x0

.field public static Capability:[I = null

.field public static Capability_queryPatterns:I = 0x0

.field public static Capability_shortcutMatchRequired:I = 0x1

.field public static ColorStateListItem:[I = null

.field public static ColorStateListItem_alpha:I = 0x3

.field public static ColorStateListItem_android_alpha:I = 0x1

.field public static ColorStateListItem_android_color:I = 0x0

.field public static ColorStateListItem_android_lStar:I = 0x2

.field public static ColorStateListItem_lStar:I = 0x4

.field public static DefaultTimeBar:[I = null

.field public static DefaultTimeBar_ad_marker_color:I = 0x0

.field public static DefaultTimeBar_ad_marker_width:I = 0x1

.field public static DefaultTimeBar_bar_gravity:I = 0x2

.field public static DefaultTimeBar_bar_height:I = 0x3

.field public static DefaultTimeBar_buffered_color:I = 0x4

.field public static DefaultTimeBar_played_ad_marker_color:I = 0x5

.field public static DefaultTimeBar_played_color:I = 0x6

.field public static DefaultTimeBar_scrubber_color:I = 0x7

.field public static DefaultTimeBar_scrubber_disabled_size:I = 0x8

.field public static DefaultTimeBar_scrubber_dragged_size:I = 0x9

.field public static DefaultTimeBar_scrubber_drawable:I = 0xa

.field public static DefaultTimeBar_scrubber_enabled_size:I = 0xb

.field public static DefaultTimeBar_touch_target_height:I = 0xc

.field public static DefaultTimeBar_unplayed_color:I = 0xd

.field public static FontFamily:[I = null

.field public static FontFamilyFont:[I = null

.field public static FontFamilyFont_android_font:I = 0x0

.field public static FontFamilyFont_android_fontStyle:I = 0x2

.field public static FontFamilyFont_android_fontVariationSettings:I = 0x4

.field public static FontFamilyFont_android_fontWeight:I = 0x1

.field public static FontFamilyFont_android_ttcIndex:I = 0x3

.field public static FontFamilyFont_font:I = 0x5

.field public static FontFamilyFont_fontStyle:I = 0x6

.field public static FontFamilyFont_fontVariationSettings:I = 0x7

.field public static FontFamilyFont_fontWeight:I = 0x8

.field public static FontFamilyFont_ttcIndex:I = 0x9

.field public static FontFamily_fontProviderAuthority:I = 0x0

.field public static FontFamily_fontProviderCerts:I = 0x1

.field public static FontFamily_fontProviderFetchStrategy:I = 0x2

.field public static FontFamily_fontProviderFetchTimeout:I = 0x3

.field public static FontFamily_fontProviderPackage:I = 0x4

.field public static FontFamily_fontProviderQuery:I = 0x5

.field public static FontFamily_fontProviderSystemFontFamily:I = 0x6

.field public static GradientColor:[I = null

.field public static GradientColorItem:[I = null

.field public static GradientColorItem_android_color:I = 0x0

.field public static GradientColorItem_android_offset:I = 0x1

.field public static GradientColor_android_centerColor:I = 0x7

.field public static GradientColor_android_centerX:I = 0x3

.field public static GradientColor_android_centerY:I = 0x4

.field public static GradientColor_android_endColor:I = 0x1

.field public static GradientColor_android_endX:I = 0xa

.field public static GradientColor_android_endY:I = 0xb

.field public static GradientColor_android_gradientRadius:I = 0x5

.field public static GradientColor_android_startColor:I = 0x0

.field public static GradientColor_android_startX:I = 0x8

.field public static GradientColor_android_startY:I = 0x9

.field public static GradientColor_android_tileMode:I = 0x6

.field public static GradientColor_android_type:I = 0x2

.field public static LegacyPlayerControlView:[I = null

.field public static LegacyPlayerControlView_ad_marker_color:I = 0x0

.field public static LegacyPlayerControlView_ad_marker_width:I = 0x1

.field public static LegacyPlayerControlView_bar_gravity:I = 0x2

.field public static LegacyPlayerControlView_bar_height:I = 0x3

.field public static LegacyPlayerControlView_buffered_color:I = 0x4

.field public static LegacyPlayerControlView_controller_layout_id:I = 0x5

.field public static LegacyPlayerControlView_played_ad_marker_color:I = 0x6

.field public static LegacyPlayerControlView_played_color:I = 0x7

.field public static LegacyPlayerControlView_repeat_toggle_modes:I = 0x8

.field public static LegacyPlayerControlView_scrubber_color:I = 0x9

.field public static LegacyPlayerControlView_scrubber_disabled_size:I = 0xa

.field public static LegacyPlayerControlView_scrubber_dragged_size:I = 0xb

.field public static LegacyPlayerControlView_scrubber_drawable:I = 0xc

.field public static LegacyPlayerControlView_scrubber_enabled_size:I = 0xd

.field public static LegacyPlayerControlView_show_fastforward_button:I = 0xe

.field public static LegacyPlayerControlView_show_next_button:I = 0xf

.field public static LegacyPlayerControlView_show_previous_button:I = 0x10

.field public static LegacyPlayerControlView_show_rewind_button:I = 0x11

.field public static LegacyPlayerControlView_show_shuffle_button:I = 0x12

.field public static LegacyPlayerControlView_show_timeout:I = 0x13

.field public static LegacyPlayerControlView_time_bar_min_update_interval:I = 0x14

.field public static LegacyPlayerControlView_touch_target_height:I = 0x15

.field public static LegacyPlayerControlView_unplayed_color:I = 0x16

.field public static PlayerControlView:[I = null

.field public static PlayerControlView_ad_marker_color:I = 0x0

.field public static PlayerControlView_ad_marker_width:I = 0x1

.field public static PlayerControlView_animation_enabled:I = 0x2

.field public static PlayerControlView_bar_gravity:I = 0x3

.field public static PlayerControlView_bar_height:I = 0x4

.field public static PlayerControlView_buffered_color:I = 0x5

.field public static PlayerControlView_controller_layout_id:I = 0x6

.field public static PlayerControlView_played_ad_marker_color:I = 0x7

.field public static PlayerControlView_played_color:I = 0x8

.field public static PlayerControlView_repeat_toggle_modes:I = 0x9

.field public static PlayerControlView_scrubber_color:I = 0xa

.field public static PlayerControlView_scrubber_disabled_size:I = 0xb

.field public static PlayerControlView_scrubber_dragged_size:I = 0xc

.field public static PlayerControlView_scrubber_drawable:I = 0xd

.field public static PlayerControlView_scrubber_enabled_size:I = 0xe

.field public static PlayerControlView_show_fastforward_button:I = 0xf

.field public static PlayerControlView_show_next_button:I = 0x10

.field public static PlayerControlView_show_previous_button:I = 0x11

.field public static PlayerControlView_show_rewind_button:I = 0x12

.field public static PlayerControlView_show_shuffle_button:I = 0x13

.field public static PlayerControlView_show_subtitle_button:I = 0x14

.field public static PlayerControlView_show_timeout:I = 0x15

.field public static PlayerControlView_show_vr_button:I = 0x16

.field public static PlayerControlView_time_bar_min_update_interval:I = 0x17

.field public static PlayerControlView_touch_target_height:I = 0x18

.field public static PlayerControlView_unplayed_color:I = 0x19

.field public static PlayerView:[I = null

.field public static PlayerView_ad_marker_color:I = 0x0

.field public static PlayerView_ad_marker_width:I = 0x1

.field public static PlayerView_animation_enabled:I = 0x2

.field public static PlayerView_artwork_display_mode:I = 0x3

.field public static PlayerView_auto_show:I = 0x4

.field public static PlayerView_bar_gravity:I = 0x5

.field public static PlayerView_bar_height:I = 0x6

.field public static PlayerView_buffered_color:I = 0x7

.field public static PlayerView_controller_layout_id:I = 0x8

.field public static PlayerView_default_artwork:I = 0x9

.field public static PlayerView_hide_during_ads:I = 0xa

.field public static PlayerView_hide_on_touch:I = 0xb

.field public static PlayerView_keep_content_on_player_reset:I = 0xc

.field public static PlayerView_played_ad_marker_color:I = 0xd

.field public static PlayerView_played_color:I = 0xe

.field public static PlayerView_player_layout_id:I = 0xf

.field public static PlayerView_repeat_toggle_modes:I = 0x10

.field public static PlayerView_resize_mode:I = 0x11

.field public static PlayerView_scrubber_color:I = 0x12

.field public static PlayerView_scrubber_disabled_size:I = 0x13

.field public static PlayerView_scrubber_dragged_size:I = 0x14

.field public static PlayerView_scrubber_drawable:I = 0x15

.field public static PlayerView_scrubber_enabled_size:I = 0x16

.field public static PlayerView_show_buffering:I = 0x17

.field public static PlayerView_show_shuffle_button:I = 0x18

.field public static PlayerView_show_subtitle_button:I = 0x19

.field public static PlayerView_show_timeout:I = 0x1a

.field public static PlayerView_show_vr_button:I = 0x1b

.field public static PlayerView_shutter_background_color:I = 0x1c

.field public static PlayerView_surface_type:I = 0x1d

.field public static PlayerView_time_bar_min_update_interval:I = 0x1e

.field public static PlayerView_touch_target_height:I = 0x1f

.field public static PlayerView_unplayed_color:I = 0x20

.field public static PlayerView_use_artwork:I = 0x21

.field public static PlayerView_use_controller:I = 0x22

.field public static RecyclerView:[I = null

.field public static RecyclerView_android_clipToPadding:I = 0x1

.field public static RecyclerView_android_descendantFocusability:I = 0x2

.field public static RecyclerView_android_orientation:I = 0x0

.field public static RecyclerView_fastScrollEnabled:I = 0x3

.field public static RecyclerView_fastScrollHorizontalThumbDrawable:I = 0x4

.field public static RecyclerView_fastScrollHorizontalTrackDrawable:I = 0x5

.field public static RecyclerView_fastScrollVerticalThumbDrawable:I = 0x6

.field public static RecyclerView_fastScrollVerticalTrackDrawable:I = 0x7

.field public static RecyclerView_layoutManager:I = 0x8

.field public static RecyclerView_reverseLayout:I = 0x9

.field public static RecyclerView_spanCount:I = 0xa

.field public static RecyclerView_stackFromEnd:I = 0xb


# direct methods
.method public static constructor <clinit>()V
    .locals 5

    const v0, 0x7f040609

    filled-new-array {v0}, [I

    move-result-object v0

    sput-object v0, Landroidx/media3/ui/R$styleable;->AspectRatioFrameLayout:[I

    const v0, 0x7f0405ec

    const v1, 0x7f04065b

    filled-new-array {v0, v1}, [I

    move-result-object v0

    sput-object v0, Landroidx/media3/ui/R$styleable;->Capability:[I

    const v0, 0x7f040040

    const v1, 0x7f04045a

    const v2, 0x10101a5

    const v3, 0x101031f

    const v4, 0x1010647

    filled-new-array {v2, v3, v4, v0, v1}, [I

    move-result-object v0

    sput-object v0, Landroidx/media3/ui/R$styleable;->ColorStateListItem:[I

    const/16 v0, 0xe

    new-array v0, v0, [I

    fill-array-data v0, :array_0

    sput-object v0, Landroidx/media3/ui/R$styleable;->DefaultTimeBar:[I

    const/4 v0, 0x7

    new-array v0, v0, [I

    fill-array-data v0, :array_1

    sput-object v0, Landroidx/media3/ui/R$styleable;->FontFamily:[I

    const/16 v0, 0xa

    new-array v0, v0, [I

    fill-array-data v0, :array_2

    sput-object v0, Landroidx/media3/ui/R$styleable;->FontFamilyFont:[I

    const/16 v0, 0xc

    new-array v1, v0, [I

    fill-array-data v1, :array_3

    sput-object v1, Landroidx/media3/ui/R$styleable;->GradientColor:[I

    const v1, 0x1010514

    filled-new-array {v2, v1}, [I

    move-result-object v1

    sput-object v1, Landroidx/media3/ui/R$styleable;->GradientColorItem:[I

    const/16 v1, 0x17

    new-array v1, v1, [I

    fill-array-data v1, :array_4

    sput-object v1, Landroidx/media3/ui/R$styleable;->LegacyPlayerControlView:[I

    const/16 v1, 0x1a

    new-array v1, v1, [I

    fill-array-data v1, :array_5

    sput-object v1, Landroidx/media3/ui/R$styleable;->PlayerControlView:[I

    const/16 v1, 0x23

    new-array v1, v1, [I

    fill-array-data v1, :array_6

    sput-object v1, Landroidx/media3/ui/R$styleable;->PlayerView:[I

    new-array v0, v0, [I

    fill-array-data v0, :array_7

    sput-object v0, Landroidx/media3/ui/R$styleable;->RecyclerView:[I

    return-void

    nop

    :array_0
    .array-data 4
        0x7f040033
        0x7f040034
        0x7f04009e
        0x7f04009f
        0x7f0401dd
        0x7f0405b7
        0x7f0405b8
        0x7f040626
        0x7f040627
        0x7f040628
        0x7f040629
        0x7f04062a
        0x7f04078b
        0x7f0407ac
    .end array-data

    :array_1
    .array-data 4
        0x7f04039b
        0x7f04039c
        0x7f04039d
        0x7f04039e
        0x7f04039f
        0x7f0403a0
        0x7f0403a1
    .end array-data

    :array_2
    .array-data 4
        0x1010532
        0x1010533
        0x101053f
        0x101056f
        0x1010570
        0x7f040399
        0x7f0403a2
        0x7f0403a3
        0x7f0403a4
        0x7f0407a3
    .end array-data

    :array_3
    .array-data 4
        0x101019d
        0x101019e
        0x10101a1
        0x10101a2
        0x10101a3
        0x10101a4
        0x1010201
        0x101020b
        0x1010510
        0x1010511
        0x1010512
        0x1010513
    .end array-data

    :array_4
    .array-data 4
        0x7f040033
        0x7f040034
        0x7f04009e
        0x7f04009f
        0x7f0401dd
        0x7f0402b4
        0x7f0405b7
        0x7f0405b8
        0x7f040608
        0x7f040626
        0x7f040627
        0x7f040628
        0x7f040629
        0x7f04062a
        0x7f04066d
        0x7f04066e
        0x7f04066f
        0x7f040670
        0x7f040671
        0x7f040673
        0x7f040759
        0x7f04078b
        0x7f0407ac
    .end array-data

    :array_5
    .array-data 4
        0x7f040033
        0x7f040034
        0x7f04004b
        0x7f04009e
        0x7f04009f
        0x7f0401dd
        0x7f0402b4
        0x7f0405b7
        0x7f0405b8
        0x7f040608
        0x7f040626
        0x7f040627
        0x7f040628
        0x7f040629
        0x7f04062a
        0x7f04066d
        0x7f04066e
        0x7f04066f
        0x7f040670
        0x7f040671
        0x7f040672
        0x7f040673
        0x7f040674
        0x7f040759
        0x7f04078b
        0x7f0407ac
    .end array-data

    :array_6
    .array-data 4
        0x7f040033
        0x7f040034
        0x7f04004b
        0x7f04005a
        0x7f040067
        0x7f04009e
        0x7f04009f
        0x7f0401dd
        0x7f0402b4
        0x7f0402e9
        0x7f0403c5
        0x7f0403c6
        0x7f040456
        0x7f0405b7
        0x7f0405b8
        0x7f0405b9
        0x7f040608
        0x7f040609
        0x7f040626
        0x7f040627
        0x7f040628
        0x7f040629
        0x7f04062a
        0x7f04066c
        0x7f040671
        0x7f040672
        0x7f040673
        0x7f040674
        0x7f040676
        0x7f0406ce
        0x7f040759
        0x7f04078b
        0x7f0407ac
        0x7f0407b3
        0x7f0407b5
    .end array-data

    :array_7
    .array-data 4
        0x10100c4
        0x10100eb
        0x10100f1
        0x7f040365
        0x7f040366
        0x7f040367
        0x7f040368
        0x7f040369
        0x7f040469
        0x7f04060c
        0x7f040685
        0x7f0406a2
    .end array-data
.end method

.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
