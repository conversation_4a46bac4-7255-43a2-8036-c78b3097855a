.class public Landroidx/media3/ui/f0$g;
.super Landroid/animation/AnimatorListenerAdapter;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Landroidx/media3/ui/f0;-><init>(Landroidx/media3/ui/PlayerControlView;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field public final synthetic a:Landroidx/media3/ui/f0;


# direct methods
.method public constructor <init>(Landroidx/media3/ui/f0;)V
    .locals 0

    iput-object p1, p0, Landroidx/media3/ui/f0$g;->a:Landroidx/media3/ui/f0;

    invoke-direct {p0}, Landroid/animation/AnimatorListenerAdapter;-><init>()V

    return-void
.end method


# virtual methods
.method public onAnimationEnd(Landroid/animation/Animator;)V
    .locals 1

    iget-object p1, p0, Landroidx/media3/ui/f0$g;->a:Landroidx/media3/ui/f0;

    const/4 v0, 0x0

    invoke-static {p1, v0}, Landroidx/media3/ui/f0;->s(Landroidx/media3/ui/f0;I)V

    return-void
.end method

.method public onAnimationStart(Landroid/animation/Animator;)V
    .locals 1

    iget-object p1, p0, Landroidx/media3/ui/f0$g;->a:Landroidx/media3/ui/f0;

    const/4 v0, 0x4

    invoke-static {p1, v0}, Landroidx/media3/ui/f0;->s(Landroidx/media3/ui/f0;I)V

    return-void
.end method
