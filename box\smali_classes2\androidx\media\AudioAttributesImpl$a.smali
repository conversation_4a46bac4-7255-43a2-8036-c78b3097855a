.class public interface abstract Landroidx/media/AudioAttributesImpl$a;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/media/AudioAttributesImpl;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "a"
.end annotation


# virtual methods
.method public abstract a(I)Landroidx/media/AudioAttributesImpl$a;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end method

.method public abstract build()Landroidx/media/AudioAttributesImpl;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end method
