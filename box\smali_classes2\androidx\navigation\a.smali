.class public final Landroidx/navigation/a;
.super Ljava/lang/Object;


# annotations
.annotation runtime Lkotlin/Metadata;
.end annotation


# instance fields
.field public a:I

.field public b:I

.field public c:I

.field public d:I


# direct methods
.method public constructor <init>()V
    .locals 1

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, -0x1

    iput v0, p0, Landroidx/navigation/a;->a:I

    iput v0, p0, Landroidx/navigation/a;->b:I

    iput v0, p0, Landroidx/navigation/a;->c:I

    iput v0, p0, Landroidx/navigation/a;->d:I

    return-void
.end method


# virtual methods
.method public final a()I
    .locals 1

    iget v0, p0, Landroidx/navigation/a;->a:I

    return v0
.end method

.method public final b()I
    .locals 1

    iget v0, p0, Landroidx/navigation/a;->b:I

    return v0
.end method

.method public final c()I
    .locals 1

    iget v0, p0, Landroidx/navigation/a;->c:I

    return v0
.end method

.method public final d()I
    .locals 1

    iget v0, p0, Landroidx/navigation/a;->d:I

    return v0
.end method

.method public final e(I)V
    .locals 0

    iput p1, p0, Landroidx/navigation/a;->a:I

    return-void
.end method

.method public final f(I)V
    .locals 0

    iput p1, p0, Landroidx/navigation/a;->b:I

    return-void
.end method
