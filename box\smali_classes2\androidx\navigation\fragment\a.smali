.class public final synthetic Landroidx/navigation/fragment/a;
.super Ljava/lang/Object;

# interfaces
.implements Landroidx/fragment/app/t;


# instance fields
.field public final synthetic a:Landroidx/navigation/fragment/c;


# direct methods
.method public synthetic constructor <init>(Landroidx/navigation/fragment/c;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Landroidx/navigation/fragment/a;->a:Landroidx/navigation/fragment/c;

    return-void
.end method


# virtual methods
.method public final a(Landroidx/fragment/app/FragmentManager;Landroidx/fragment/app/Fragment;)V
    .locals 1

    iget-object v0, p0, Landroidx/navigation/fragment/a;->a:Landroidx/navigation/fragment/c;

    invoke-static {v0, p1, p2}, Landroidx/navigation/fragment/c;->l(Landroidx/navigation/fragment/c;Landroidx/fragment/app/FragmentManager;Landroidx/fragment/app/Fragment;)V

    return-void
.end method
