.class public final synthetic Landroidx/privacysandbox/ads/adservices/topics/h;
.super Ljava/lang/Object;


# direct methods
.method public static synthetic a()Landroid/adservices/topics/GetTopicsRequest$Builder;
    .locals 1

    new-instance v0, Landroid/adservices/topics/GetTopicsRequest$Builder;

    invoke-direct {v0}, Landroid/adservices/topics/GetTopicsRequest$Builder;-><init>()V

    return-object v0
.end method
