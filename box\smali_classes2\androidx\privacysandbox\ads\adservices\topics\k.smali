.class public final synthetic Landroidx/privacysandbox/ads/adservices/topics/k;
.super Ljava/lang/Object;


# direct methods
.method public static bridge synthetic a(Landroid/adservices/topics/GetTopicsRequest$Builder;)Landroid/adservices/topics/GetTopicsRequest;
    .locals 0

    invoke-virtual {p0}, Landroid/adservices/topics/GetTopicsRequest$Builder;->build()Landroid/adservices/topics/GetTopicsRequest;

    move-result-object p0

    return-object p0
.end method
