.class public final Landroidx/recyclerview/R$dimen;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/recyclerview/R;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "dimen"
.end annotation


# static fields
.field public static fastscroll_default_thickness:I = 0x7f0700e3

.field public static fastscroll_margin:I = 0x7f0700e4

.field public static fastscroll_minimum_range:I = 0x7f0700e5

.field public static item_touch_helper_max_drag_scroll_per_frame:I = 0x7f070100

.field public static item_touch_helper_swipe_escape_max_velocity:I = 0x7f070101

.field public static item_touch_helper_swipe_escape_velocity:I = 0x7f070102


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
