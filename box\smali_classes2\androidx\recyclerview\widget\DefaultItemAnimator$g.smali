.class public Landroidx/recyclerview/widget/DefaultItemAnimator$g;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/recyclerview/widget/DefaultItemAnimator;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "g"
.end annotation


# instance fields
.field public a:Landroidx/recyclerview/widget/RecyclerView$a0;

.field public b:I

.field public c:I

.field public d:I

.field public e:I


# direct methods
.method public constructor <init>(Landroidx/recyclerview/widget/RecyclerView$a0;IIII)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Landroidx/recyclerview/widget/DefaultItemAnimator$g;->a:Landroidx/recyclerview/widget/RecyclerView$a0;

    iput p2, p0, Landroidx/recyclerview/widget/DefaultItemAnimator$g;->b:I

    iput p3, p0, Landroidx/recyclerview/widget/DefaultItemAnimator$g;->c:I

    iput p4, p0, Landroidx/recyclerview/widget/DefaultItemAnimator$g;->d:I

    iput p5, p0, Landroidx/recyclerview/widget/DefaultItemAnimator$g;->e:I

    return-void
.end method
