.class public Landroidx/recyclerview/widget/DiffUtil$f;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/recyclerview/widget/DiffUtil;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "f"
.end annotation


# instance fields
.field public a:I

.field public b:I

.field public c:Z


# direct methods
.method public constructor <init>(IIZ)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput p1, p0, Landroidx/recyclerview/widget/DiffUtil$f;->a:I

    iput p2, p0, Landroidx/recyclerview/widget/DiffUtil$f;->b:I

    iput-boolean p3, p0, Landroidx/recyclerview/widget/DiffUtil$f;->c:Z

    return-void
.end method
