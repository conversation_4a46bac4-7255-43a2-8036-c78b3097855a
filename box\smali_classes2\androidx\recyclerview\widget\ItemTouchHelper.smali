.class public Landroidx/recyclerview/widget/ItemTouchHelper;
.super Landroidx/recyclerview/widget/RecyclerView$l;

# interfaces
.implements Landroidx/recyclerview/widget/RecyclerView$o;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Landroidx/recyclerview/widget/ItemTouchHelper$b;,
        Landroidx/recyclerview/widget/ItemTouchHelper$a;
    }
.end annotation


# instance fields
.field public a:Landroidx/recyclerview/widget/RecyclerView$a0;

.field public b:Landroidx/recyclerview/widget/ItemTouchHelper$a;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public c:Landroidx/recyclerview/widget/RecyclerView;

.field public final d:Ljava/lang/Runnable;


# virtual methods
.method public d()Z
    .locals 0

    const/4 p0, 0x0

    throw p0
.end method

.method public e(Landroidx/recyclerview/widget/RecyclerView$a0;)V
    .locals 0

    const/4 p0, 0x0

    throw p0
.end method

.method public f()Z
    .locals 0

    const/4 p0, 0x0

    throw p0
.end method
