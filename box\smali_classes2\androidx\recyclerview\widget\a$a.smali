.class public interface abstract Landroidx/recyclerview/widget/a$a;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/recyclerview/widget/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "a"
.end annotation


# virtual methods
.method public abstract a(II)V
.end method

.method public abstract b(Landroidx/recyclerview/widget/a$b;)V
.end method

.method public abstract c(Landroidx/recyclerview/widget/a$b;)V
.end method

.method public abstract d(II)V
.end method

.method public abstract e(IILjava/lang/Object;)V
.end method

.method public abstract f(I)Landroidx/recyclerview/widget/RecyclerView$a0;
.end method

.method public abstract g(II)V
.end method

.method public abstract h(II)V
.end method
