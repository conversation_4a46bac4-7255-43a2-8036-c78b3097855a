.class public interface abstract Landroidx/recyclerview/widget/e$b;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/recyclerview/widget/e;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "b"
.end annotation


# virtual methods
.method public abstract a(I)Landroid/view/View;
.end method

.method public abstract addView(Landroid/view/View;I)V
.end method

.method public abstract b(Landroid/view/View;)V
.end method

.method public abstract c()I
.end method

.method public abstract d(Landroid/view/View;)Landroidx/recyclerview/widget/RecyclerView$a0;
.end method

.method public abstract e(I)V
.end method

.method public abstract f()V
.end method

.method public abstract g(Landroid/view/View;)I
.end method

.method public abstract h(Landroid/view/View;)V
.end method

.method public abstract i(I)V
.end method

.method public abstract j(Landroid/view/View;ILandroid/view/ViewGroup$LayoutParams;)V
.end method
