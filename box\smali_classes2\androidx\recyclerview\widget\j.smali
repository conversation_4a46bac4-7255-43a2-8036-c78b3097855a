.class public interface abstract Landroidx/recyclerview/widget/j;
.super Ljava/lang/Object;


# virtual methods
.method public abstract onChanged(IILjava/lang/Object;)V
    .param p3    # Ljava/lang/Object;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
.end method

.method public abstract onInserted(II)V
.end method

.method public abstract onMoved(II)V
.end method

.method public abstract onRemoved(II)V
.end method
