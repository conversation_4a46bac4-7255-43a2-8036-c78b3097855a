.class public interface abstract Landroidx/recyclerview/widget/k$b;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/recyclerview/widget/k;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "b"
.end annotation


# virtual methods
.method public abstract a(Landroidx/recyclerview/widget/k;IILjava/lang/Object;)V
    .param p1    # Landroidx/recyclerview/widget/k;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p4    # Ljava/lang/Object;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
.end method

.method public abstract b(Landroidx/recyclerview/widget/k;II)V
    .param p1    # Landroidx/recyclerview/widget/k;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
.end method

.method public abstract c(Landroidx/recyclerview/widget/k;II)V
    .param p1    # Landroidx/recyclerview/widget/k;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
.end method

.method public abstract d(Landroidx/recyclerview/widget/k;)V
.end method

.method public abstract e(Landroidx/recyclerview/widget/k;)V
    .param p1    # Landroidx/recyclerview/widget/k;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
.end method

.method public abstract f(Landroidx/recyclerview/widget/k;II)V
    .param p1    # Landroidx/recyclerview/widget/k;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
.end method
