.class public interface abstract Landroidx/recyclerview/widget/l$a;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/recyclerview/widget/l;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "a"
.end annotation


# virtual methods
.method public abstract a(IIILjava/lang/Object;)Landroidx/recyclerview/widget/a$b;
.end method

.method public abstract b(Landroidx/recyclerview/widget/a$b;)V
.end method
