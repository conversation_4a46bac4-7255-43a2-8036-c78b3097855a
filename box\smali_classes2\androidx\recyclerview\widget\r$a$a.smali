.class public Landroidx/recyclerview/widget/r$a$a;
.super Ljava/lang/Object;

# interfaces
.implements Landroidx/recyclerview/widget/r$d;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/recyclerview/widget/r$a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = "a"
.end annotation


# instance fields
.field public final a:Landroidx/collection/w;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Landroidx/collection/w<",
            "Ljava/lang/Long;",
            ">;"
        }
    .end annotation
.end field

.field public final synthetic b:Landroidx/recyclerview/widget/r$a;


# direct methods
.method public constructor <init>(Landroidx/recyclerview/widget/r$a;)V
    .locals 0

    iput-object p1, p0, Landroidx/recyclerview/widget/r$a$a;->b:Landroidx/recyclerview/widget/r$a;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    new-instance p1, Landroidx/collection/w;

    invoke-direct {p1}, Landroidx/collection/w;-><init>()V

    iput-object p1, p0, Landroidx/recyclerview/widget/r$a$a;->a:Landroidx/collection/w;

    return-void
.end method


# virtual methods
.method public a(J)J
    .locals 2

    iget-object v0, p0, Landroidx/recyclerview/widget/r$a$a;->a:Landroidx/collection/w;

    invoke-virtual {v0, p1, p2}, Landroidx/collection/w;->e(J)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/lang/Long;

    if-nez v0, :cond_0

    iget-object v0, p0, Landroidx/recyclerview/widget/r$a$a;->b:Landroidx/recyclerview/widget/r$a;

    invoke-virtual {v0}, Landroidx/recyclerview/widget/r$a;->b()J

    move-result-wide v0

    invoke-static {v0, v1}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    move-result-object v0

    iget-object v1, p0, Landroidx/recyclerview/widget/r$a$a;->a:Landroidx/collection/w;

    invoke-virtual {v1, p1, p2, v0}, Landroidx/collection/w;->j(JLjava/lang/Object;)V

    :cond_0
    invoke-virtual {v0}, Ljava/lang/Long;->longValue()J

    move-result-wide p1

    return-wide p1
.end method
