.class public Landroidx/recyclerview/widget/r$b;
.super Ljava/lang/Object;

# interfaces
.implements Landroidx/recyclerview/widget/r;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/recyclerview/widget/r;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "b"
.end annotation


# instance fields
.field public final a:Landroidx/recyclerview/widget/r$d;


# direct methods
.method public constructor <init>()V
    .locals 1

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    new-instance v0, Landroidx/recyclerview/widget/r$b$a;

    invoke-direct {v0, p0}, Landroidx/recyclerview/widget/r$b$a;-><init>(Landroidx/recyclerview/widget/r$b;)V

    iput-object v0, p0, Landroidx/recyclerview/widget/r$b;->a:Landroidx/recyclerview/widget/r$d;

    return-void
.end method


# virtual methods
.method public a()Landroidx/recyclerview/widget/r$d;
    .locals 1
    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    iget-object v0, p0, Landroidx/recyclerview/widget/r$b;->a:Landroidx/recyclerview/widget/r$d;

    return-object v0
.end method
