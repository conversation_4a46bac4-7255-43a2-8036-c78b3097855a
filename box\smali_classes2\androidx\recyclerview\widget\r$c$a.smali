.class public Landroidx/recyclerview/widget/r$c$a;
.super Ljava/lang/Object;

# interfaces
.implements Landroidx/recyclerview/widget/r$d;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/recyclerview/widget/r$c;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field public final synthetic a:Landroidx/recyclerview/widget/r$c;


# direct methods
.method public constructor <init>(Landroidx/recyclerview/widget/r$c;)V
    .locals 0

    iput-object p1, p0, Landroidx/recyclerview/widget/r$c$a;->a:Landroidx/recyclerview/widget/r$c;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public a(J)J
    .locals 0

    return-wide p1
.end method
