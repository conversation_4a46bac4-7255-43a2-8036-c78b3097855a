.class public interface abstract Landroidx/recyclerview/widget/r;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Landroidx/recyclerview/widget/r$a;,
        Landroidx/recyclerview/widget/r$c;,
        Landroidx/recyclerview/widget/r$b;,
        Landroidx/recyclerview/widget/r$d;
    }
.end annotation


# virtual methods
.method public abstract a()Landroidx/recyclerview/widget/r$d;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end method
