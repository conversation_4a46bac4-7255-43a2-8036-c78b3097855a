.class public interface abstract Landroidx/recyclerview/widget/u;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Landroidx/recyclerview/widget/u$a;,
        Landroidx/recyclerview/widget/u$b;,
        Landroidx/recyclerview/widget/u$c;
    }
.end annotation


# virtual methods
.method public abstract a(I)Landroidx/recyclerview/widget/k;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end method

.method public abstract b(Landroidx/recyclerview/widget/k;)Landroidx/recyclerview/widget/u$c;
    .param p1    # Landroidx/recyclerview/widget/k;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end method
