.class public final Landroidx/room/a0;
.super Ljava/lang/Object;

# interfaces
.implements Ll4/h$c;


# annotations
.annotation runtime Lkotlin/Metadata;
.end annotation


# instance fields
.field public final a:Ljava/lang/String;

.field public final b:Ljava/io/File;

.field public final c:Ljava/util/concurrent/Callable;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/concurrent/Callable<",
            "Ljava/io/InputStream;",
            ">;"
        }
    .end annotation
.end field

.field public final d:Ll4/h$c;


# direct methods
.method public constructor <init>(Ljava/lang/String;Ljava/io/File;Ljava/util/concurrent/Callable;Ll4/h$c;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Ljava/io/File;",
            "Ljava/util/concurrent/Callable<",
            "Ljava/io/InputStream;",
            ">;",
            "Ll4/h$c;",
            ")V"
        }
    .end annotation

    const-string v0, "mDelegate"

    invoke-static {p4, v0}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Landroidx/room/a0;->a:Ljava/lang/String;

    iput-object p2, p0, Landroidx/room/a0;->b:Ljava/io/File;

    iput-object p3, p0, Landroidx/room/a0;->c:Ljava/util/concurrent/Callable;

    iput-object p4, p0, Landroidx/room/a0;->d:Ll4/h$c;

    return-void
.end method


# virtual methods
.method public a(Ll4/h$b;)Ll4/h;
    .locals 8

    const-string v0, "configuration"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    new-instance v0, Landroidx/room/z;

    iget-object v2, p1, Ll4/h$b;->a:Landroid/content/Context;

    iget-object v3, p0, Landroidx/room/a0;->a:Ljava/lang/String;

    iget-object v4, p0, Landroidx/room/a0;->b:Ljava/io/File;

    iget-object v5, p0, Landroidx/room/a0;->c:Ljava/util/concurrent/Callable;

    iget-object v1, p1, Ll4/h$b;->c:Ll4/h$a;

    iget v6, v1, Ll4/h$a;->a:I

    iget-object v1, p0, Landroidx/room/a0;->d:Ll4/h$c;

    invoke-interface {v1, p1}, Ll4/h$c;->a(Ll4/h$b;)Ll4/h;

    move-result-object v7

    move-object v1, v0

    invoke-direct/range {v1 .. v7}, Landroidx/room/z;-><init>(Landroid/content/Context;Ljava/lang/String;Ljava/io/File;Ljava/util/concurrent/Callable;ILl4/h;)V

    return-object v0
.end method
