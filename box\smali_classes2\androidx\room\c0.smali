.class public final synthetic Landroidx/room/c0;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/lang/Runnable;


# instance fields
.field public final synthetic a:Ljava/lang/Runnable;

.field public final synthetic b:Landroidx/room/d0;


# direct methods
.method public synthetic constructor <init>(Ljava/lang/Runnable;Landroidx/room/d0;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Landroidx/room/c0;->a:Ljava/lang/Runnable;

    iput-object p2, p0, Landroidx/room/c0;->b:Landroidx/room/d0;

    return-void
.end method


# virtual methods
.method public final run()V
    .locals 2

    iget-object v0, p0, Landroidx/room/c0;->a:Ljava/lang/Runnable;

    iget-object v1, p0, Landroidx/room/c0;->b:Landroidx/room/d0;

    invoke-static {v0, v1}, Landroidx/room/d0;->a(<PERSON><PERSON><PERSON>/lang/Runnable;Landroidx/room/d0;)V

    return-void
.end method
