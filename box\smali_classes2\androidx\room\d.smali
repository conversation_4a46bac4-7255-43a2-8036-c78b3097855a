.class public final Landroidx/room/d;
.super Ljava/lang/Object;

# interfaces
.implements Ll4/h$c;


# annotations
.annotation runtime Lkotlin/Metadata;
.end annotation


# instance fields
.field public final a:Ll4/h$c;

.field public final b:Landroidx/room/c;


# direct methods
.method public constructor <init>(Ll4/h$c;Landroidx/room/c;)V
    .locals 1

    const-string v0, "delegate"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    const-string v0, "autoCloser"

    invoke-static {p2, v0}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Landroidx/room/d;->a:Ll4/h$c;

    iput-object p2, p0, Landroidx/room/d;->b:Landroidx/room/c;

    return-void
.end method


# virtual methods
.method public bridge synthetic a(Ll4/h$b;)Ll4/h;
    .locals 0

    invoke-virtual {p0, p1}, Landroidx/room/d;->b(Ll4/h$b;)Landroidx/room/AutoClosingRoomOpenHelper;

    move-result-object p1

    return-object p1
.end method

.method public b(Ll4/h$b;)Landroidx/room/AutoClosingRoomOpenHelper;
    .locals 2

    const-string v0, "configuration"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    new-instance v0, Landroidx/room/AutoClosingRoomOpenHelper;

    iget-object v1, p0, Landroidx/room/d;->a:Ll4/h$c;

    invoke-interface {v1, p1}, Ll4/h$c;->a(Ll4/h$b;)Ll4/h;

    move-result-object p1

    iget-object v1, p0, Landroidx/room/d;->b:Landroidx/room/c;

    invoke-direct {v0, p1, v1}, Landroidx/room/AutoClosingRoomOpenHelper;-><init>(Ll4/h;Landroidx/room/c;)V

    return-object v0
.end method
