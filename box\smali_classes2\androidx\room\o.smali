.class public final synthetic Landroidx/room/o;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/lang/Runnable;


# instance fields
.field public final synthetic a:Landroidx/room/p;


# direct methods
.method public synthetic constructor <init>(Landroidx/room/p;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Landroidx/room/o;->a:Landroidx/room/p;

    return-void
.end method


# virtual methods
.method public final run()V
    .locals 1

    iget-object v0, p0, Landroidx/room/o;->a:Landroidx/room/p;

    invoke-static {v0}, Landroidx/room/p;->b(Landroidx/room/p;)V

    return-void
.end method
