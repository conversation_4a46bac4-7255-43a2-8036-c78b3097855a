.class public final Landroidx/room/p$b;
.super Landroidx/room/j$a;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Landroidx/room/p;-><init>(Landroid/content/Context;Ljava/lang/String;Landroid/content/Intent;Landroidx/room/InvalidationTracker;Ljava/util/concurrent/Executor;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation


# instance fields
.field public final synthetic a:Landroidx/room/p;


# direct methods
.method public constructor <init>(Landroidx/room/p;)V
    .locals 0

    iput-object p1, p0, Landroidx/room/p$b;->a:Landroidx/room/p;

    invoke-direct {p0}, Landroidx/room/j$a;-><init>()V

    return-void
.end method

.method public static synthetic D(Landroidx/room/p;[Ljava/lang/String;)V
    .locals 0

    invoke-static {p0, p1}, Landroidx/room/p$b;->H(Landroidx/room/p;[Ljava/lang/String;)V

    return-void
.end method

.method public static final H(Landroidx/room/p;[Ljava/lang/String;)V
    .locals 1

    const-string v0, "this$0"

    invoke-static {p0, v0}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    const-string v0, "$tables"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-virtual {p0}, Landroidx/room/p;->e()Landroidx/room/InvalidationTracker;

    move-result-object p0

    array-length v0, p1

    invoke-static {p1, v0}, Ljava/util/Arrays;->copyOf([Ljava/lang/Object;I)[Ljava/lang/Object;

    move-result-object p1

    check-cast p1, [Ljava/lang/String;

    invoke-virtual {p0, p1}, Landroidx/room/InvalidationTracker;->m([Ljava/lang/String;)V

    return-void
.end method


# virtual methods
.method public X([Ljava/lang/String;)V
    .locals 3

    const-string v0, "tables"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    iget-object v0, p0, Landroidx/room/p$b;->a:Landroidx/room/p;

    invoke-virtual {v0}, Landroidx/room/p;->d()Ljava/util/concurrent/Executor;

    move-result-object v0

    iget-object v1, p0, Landroidx/room/p$b;->a:Landroidx/room/p;

    new-instance v2, Landroidx/room/q;

    invoke-direct {v2, v1, p1}, Landroidx/room/q;-><init>(Landroidx/room/p;[Ljava/lang/String;)V

    invoke-interface {v0, v2}, Ljava/util/concurrent/Executor;->execute(Ljava/lang/Runnable;)V

    return-void
.end method
