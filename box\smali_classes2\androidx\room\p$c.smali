.class public final Landroidx/room/p$c;
.super Ljava/lang/Object;

# interfaces
.implements Landroid/content/ServiceConnection;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Landroidx/room/p;-><init>(Landroid/content/Context;Ljava/lang/String;Landroid/content/Intent;Landroidx/room/InvalidationTracker;Ljava/util/concurrent/Executor;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation


# instance fields
.field public final synthetic a:Landroidx/room/p;


# direct methods
.method public constructor <init>(Landroidx/room/p;)V
    .locals 0

    iput-object p1, p0, Landroidx/room/p$c;->a:Landroidx/room/p;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public onServiceConnected(Landroid/content/ComponentName;Landroid/os/IBinder;)V
    .locals 1

    const-string v0, "name"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    const-string p1, "service"

    invoke-static {p2, p1}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    iget-object p1, p0, Landroidx/room/p$c;->a:Landroidx/room/p;

    invoke-static {p2}, Landroidx/room/k$a;->u(Landroid/os/IBinder;)Landroidx/room/k;

    move-result-object p2

    invoke-virtual {p1, p2}, Landroidx/room/p;->m(Landroidx/room/k;)V

    iget-object p1, p0, Landroidx/room/p$c;->a:Landroidx/room/p;

    invoke-virtual {p1}, Landroidx/room/p;->d()Ljava/util/concurrent/Executor;

    move-result-object p1

    iget-object p2, p0, Landroidx/room/p$c;->a:Landroidx/room/p;

    invoke-virtual {p2}, Landroidx/room/p;->i()Ljava/lang/Runnable;

    move-result-object p2

    invoke-interface {p1, p2}, Ljava/util/concurrent/Executor;->execute(Ljava/lang/Runnable;)V

    return-void
.end method

.method public onServiceDisconnected(Landroid/content/ComponentName;)V
    .locals 1

    const-string v0, "name"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    iget-object p1, p0, Landroidx/room/p$c;->a:Landroidx/room/p;

    invoke-virtual {p1}, Landroidx/room/p;->d()Ljava/util/concurrent/Executor;

    move-result-object p1

    iget-object v0, p0, Landroidx/room/p$c;->a:Landroidx/room/p;

    invoke-virtual {v0}, Landroidx/room/p;->g()Ljava/lang/Runnable;

    move-result-object v0

    invoke-interface {p1, v0}, Ljava/util/concurrent/Executor;->execute(Ljava/lang/Runnable;)V

    iget-object p1, p0, Landroidx/room/p$c;->a:Landroidx/room/p;

    const/4 v0, 0x0

    invoke-virtual {p1, v0}, Landroidx/room/p;->m(Landroidx/room/k;)V

    return-void
.end method
