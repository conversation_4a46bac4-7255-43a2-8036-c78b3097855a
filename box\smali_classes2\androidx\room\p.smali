.class public final Landroidx/room/p;
.super Ljava/lang/Object;


# annotations
.annotation runtime Lkotlin/Metadata;
.end annotation


# instance fields
.field public final a:Ljava/lang/String;

.field public final b:Landroidx/room/InvalidationTracker;

.field public final c:Ljava/util/concurrent/Executor;

.field public final d:Landroid/content/Context;

.field public e:I

.field public f:Landroidx/room/InvalidationTracker$c;

.field public g:Landroidx/room/k;

.field public final h:Landroidx/room/j;

.field public final i:Ljava/util/concurrent/atomic/AtomicBoolean;

.field public final j:Landroid/content/ServiceConnection;

.field public final k:Ljava/lang/Runnable;

.field public final l:Ljava/lang/Runnable;


# direct methods
.method public constructor <init>(Landroid/content/Context;Ljava/lang/String;Landroid/content/Intent;Landroidx/room/InvalidationTracker;Ljava/util/concurrent/Executor;)V
    .locals 1

    const-string v0, "context"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    const-string v0, "name"

    invoke-static {p2, v0}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    const-string v0, "serviceIntent"

    invoke-static {p3, v0}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    const-string v0, "invalidationTracker"

    invoke-static {p4, v0}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    const-string v0, "executor"

    invoke-static {p5, v0}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p2, p0, Landroidx/room/p;->a:Ljava/lang/String;

    iput-object p4, p0, Landroidx/room/p;->b:Landroidx/room/InvalidationTracker;

    iput-object p5, p0, Landroidx/room/p;->c:Ljava/util/concurrent/Executor;

    invoke-virtual {p1}, Landroid/content/Context;->getApplicationContext()Landroid/content/Context;

    move-result-object p1

    iput-object p1, p0, Landroidx/room/p;->d:Landroid/content/Context;

    new-instance p2, Landroidx/room/p$b;

    invoke-direct {p2, p0}, Landroidx/room/p$b;-><init>(Landroidx/room/p;)V

    iput-object p2, p0, Landroidx/room/p;->h:Landroidx/room/j;

    new-instance p2, Ljava/util/concurrent/atomic/AtomicBoolean;

    const/4 p5, 0x0

    invoke-direct {p2, p5}, Ljava/util/concurrent/atomic/AtomicBoolean;-><init>(Z)V

    iput-object p2, p0, Landroidx/room/p;->i:Ljava/util/concurrent/atomic/AtomicBoolean;

    new-instance p2, Landroidx/room/p$c;

    invoke-direct {p2, p0}, Landroidx/room/p$c;-><init>(Landroidx/room/p;)V

    iput-object p2, p0, Landroidx/room/p;->j:Landroid/content/ServiceConnection;

    new-instance v0, Landroidx/room/n;

    invoke-direct {v0, p0}, Landroidx/room/n;-><init>(Landroidx/room/p;)V

    iput-object v0, p0, Landroidx/room/p;->k:Ljava/lang/Runnable;

    new-instance v0, Landroidx/room/o;

    invoke-direct {v0, p0}, Landroidx/room/o;-><init>(Landroidx/room/p;)V

    iput-object v0, p0, Landroidx/room/p;->l:Ljava/lang/Runnable;

    invoke-virtual {p4}, Landroidx/room/InvalidationTracker;->k()Ljava/util/Map;

    move-result-object p4

    invoke-interface {p4}, Ljava/util/Map;->keySet()Ljava/util/Set;

    move-result-object p4

    check-cast p4, Ljava/util/Collection;

    new-array p5, p5, [Ljava/lang/String;

    invoke-interface {p4, p5}, Ljava/util/Collection;->toArray([Ljava/lang/Object;)[Ljava/lang/Object;

    move-result-object p4

    const-string p5, "null cannot be cast to non-null type kotlin.Array<T of kotlin.collections.ArraysKt__ArraysJVMKt.toTypedArray>"

    invoke-static {p4, p5}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/String;)V

    check-cast p4, [Ljava/lang/String;

    new-instance p5, Landroidx/room/p$a;

    invoke-direct {p5, p0, p4}, Landroidx/room/p$a;-><init>(Landroidx/room/p;[Ljava/lang/String;)V

    invoke-virtual {p0, p5}, Landroidx/room/p;->l(Landroidx/room/InvalidationTracker$c;)V

    const/4 p4, 0x1

    invoke-virtual {p1, p3, p2, p4}, Landroid/content/Context;->bindService(Landroid/content/Intent;Landroid/content/ServiceConnection;I)Z

    return-void
.end method

.method public static synthetic a(Landroidx/room/p;)V
    .locals 0

    invoke-static {p0}, Landroidx/room/p;->n(Landroidx/room/p;)V

    return-void
.end method

.method public static synthetic b(Landroidx/room/p;)V
    .locals 0

    invoke-static {p0}, Landroidx/room/p;->k(Landroidx/room/p;)V

    return-void
.end method

.method public static final k(Landroidx/room/p;)V
    .locals 1

    const-string v0, "this$0"

    invoke-static {p0, v0}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    iget-object v0, p0, Landroidx/room/p;->b:Landroidx/room/InvalidationTracker;

    invoke-virtual {p0}, Landroidx/room/p;->f()Landroidx/room/InvalidationTracker$c;

    move-result-object p0

    invoke-virtual {v0, p0}, Landroidx/room/InvalidationTracker;->p(Landroidx/room/InvalidationTracker$c;)V

    return-void
.end method

.method public static final n(Landroidx/room/p;)V
    .locals 3

    const-string v0, "this$0"

    invoke-static {p0, v0}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    :try_start_0
    iget-object v0, p0, Landroidx/room/p;->g:Landroidx/room/k;

    if-eqz v0, :cond_0

    iget-object v1, p0, Landroidx/room/p;->h:Landroidx/room/j;

    iget-object v2, p0, Landroidx/room/p;->a:Ljava/lang/String;

    invoke-interface {v0, v1, v2}, Landroidx/room/k;->g2(Landroidx/room/j;Ljava/lang/String;)I

    move-result v0

    iput v0, p0, Landroidx/room/p;->e:I

    iget-object v0, p0, Landroidx/room/p;->b:Landroidx/room/InvalidationTracker;

    invoke-virtual {p0}, Landroidx/room/p;->f()Landroidx/room/InvalidationTracker$c;

    move-result-object p0

    invoke-virtual {v0, p0}, Landroidx/room/InvalidationTracker;->c(Landroidx/room/InvalidationTracker$c;)V
    :try_end_0
    .catch Landroid/os/RemoteException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception p0

    const-string v0, "ROOM"

    const-string v1, "Cannot register multi-instance invalidation callback"

    invoke-static {v0, v1, p0}, Landroid/util/Log;->w(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Throwable;)I

    :cond_0
    :goto_0
    return-void
.end method


# virtual methods
.method public final c()I
    .locals 1

    iget v0, p0, Landroidx/room/p;->e:I

    return v0
.end method

.method public final d()Ljava/util/concurrent/Executor;
    .locals 1

    iget-object v0, p0, Landroidx/room/p;->c:Ljava/util/concurrent/Executor;

    return-object v0
.end method

.method public final e()Landroidx/room/InvalidationTracker;
    .locals 1

    iget-object v0, p0, Landroidx/room/p;->b:Landroidx/room/InvalidationTracker;

    return-object v0
.end method

.method public final f()Landroidx/room/InvalidationTracker$c;
    .locals 1

    iget-object v0, p0, Landroidx/room/p;->f:Landroidx/room/InvalidationTracker$c;

    if-eqz v0, :cond_0

    return-object v0

    :cond_0
    const-string v0, "observer"

    invoke-static {v0}, Lkotlin/jvm/internal/Intrinsics;->y(Ljava/lang/String;)V

    const/4 v0, 0x0

    return-object v0
.end method

.method public final g()Ljava/lang/Runnable;
    .locals 1

    iget-object v0, p0, Landroidx/room/p;->l:Ljava/lang/Runnable;

    return-object v0
.end method

.method public final h()Landroidx/room/k;
    .locals 1

    iget-object v0, p0, Landroidx/room/p;->g:Landroidx/room/k;

    return-object v0
.end method

.method public final i()Ljava/lang/Runnable;
    .locals 1

    iget-object v0, p0, Landroidx/room/p;->k:Ljava/lang/Runnable;

    return-object v0
.end method

.method public final j()Ljava/util/concurrent/atomic/AtomicBoolean;
    .locals 1

    iget-object v0, p0, Landroidx/room/p;->i:Ljava/util/concurrent/atomic/AtomicBoolean;

    return-object v0
.end method

.method public final l(Landroidx/room/InvalidationTracker$c;)V
    .locals 1

    const-string v0, "<set-?>"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    iput-object p1, p0, Landroidx/room/p;->f:Landroidx/room/InvalidationTracker$c;

    return-void
.end method

.method public final m(Landroidx/room/k;)V
    .locals 0

    iput-object p1, p0, Landroidx/room/p;->g:Landroidx/room/k;

    return-void
.end method
