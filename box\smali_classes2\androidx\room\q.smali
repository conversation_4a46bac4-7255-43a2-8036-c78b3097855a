.class public final synthetic Landroidx/room/q;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/lang/Runnable;


# instance fields
.field public final synthetic a:Landroidx/room/p;

.field public final synthetic b:[Ljava/lang/String;


# direct methods
.method public synthetic constructor <init>(Landroidx/room/p;[Ljava/lang/String;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Landroidx/room/q;->a:Landroidx/room/p;

    iput-object p2, p0, Landroidx/room/q;->b:[Ljava/lang/String;

    return-void
.end method


# virtual methods
.method public final run()V
    .locals 2

    iget-object v0, p0, Landroidx/room/q;->a:Landroidx/room/p;

    iget-object v1, p0, Landroidx/room/q;->b:[Ljava/lang/String;

    invoke-static {v0, v1}, Landroidx/room/p$b;->D(Landroidx/room/p;[Ljava/lang/String;)V

    return-void
.end method
