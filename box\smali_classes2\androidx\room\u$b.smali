.class public abstract Landroidx/room/u$b;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/room/u;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x409
    name = "b"
.end annotation

.annotation runtime L<PERSON>lin/Metadata;
.end annotation


# instance fields
.field public final a:I
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field


# direct methods
.method public constructor <init>(I)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput p1, p0, Landroidx/room/u$b;->a:I

    return-void
.end method


# virtual methods
.method public abstract a(Ll4/g;)V
.end method

.method public abstract b(Ll4/g;)V
.end method

.method public abstract c(Ll4/g;)V
.end method

.method public abstract d(Ll4/g;)V
.end method

.method public abstract e(Ll4/g;)V
.end method

.method public abstract f(Ll4/g;)V
.end method

.method public abstract g(Ll4/g;)Landroidx/room/u$c;
.end method
