.class public final synthetic Landroidx/room/x;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/lang/Runnable;


# instance fields
.field public final synthetic a:Landroidx/room/y;


# direct methods
.method public synthetic constructor <init>(Landroidx/room/y;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Landroidx/room/x;->a:Landroidx/room/y;

    return-void
.end method


# virtual methods
.method public final run()V
    .locals 1

    iget-object v0, p0, Landroidx/room/x;->a:Landroidx/room/y;

    invoke-static {v0}, Landroidx/room/y;->s(Landroidx/room/y;)V

    return-void
.end method
