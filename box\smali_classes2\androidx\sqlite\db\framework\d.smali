.class public final Landroidx/sqlite/db/framework/d;
.super Ljava/lang/Object;

# interfaces
.implements Ll4/h$c;


# annotations
.annotation runtime Lkotlin/Metadata;
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public a(Ll4/h$b;)Ll4/h;
    .locals 7

    const-string v0, "configuration"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    new-instance v0, Landroidx/sqlite/db/framework/FrameworkSQLiteOpenHelper;

    iget-object v2, p1, Ll4/h$b;->a:Landroid/content/Context;

    iget-object v3, p1, Ll4/h$b;->b:Ljava/lang/String;

    iget-object v4, p1, Ll4/h$b;->c:Ll4/h$a;

    iget-boolean v5, p1, Ll4/h$b;->d:Z

    iget-boolean v6, p1, Ll4/h$b;->e:Z

    move-object v1, v0

    invoke-direct/range {v1 .. v6}, Landroidx/sqlite/db/framework/FrameworkSQLiteOpenHelper;-><init>(Landroid/content/Context;Ljava/lang/String;Ll4/h$a;ZZ)V

    return-object v0
.end method
