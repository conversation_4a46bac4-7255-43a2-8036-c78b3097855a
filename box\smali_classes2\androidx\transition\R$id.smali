.class public final Landroidx/transition/R$id;
.super Ljava/lang/Object;


# static fields
.field public static ghost_view:I = 0x7f0a02c1

.field public static ghost_view_holder:I = 0x7f0a02c2

.field public static parent_matrix:I = 0x7f0a0702

.field public static save_non_transition_alpha:I = 0x7f0a07cd

.field public static save_overlay_view:I = 0x7f0a07ce

.field public static transition_clip:I = 0x7f0a093c

.field public static transition_current_scene:I = 0x7f0a093d

.field public static transition_image_transform:I = 0x7f0a093e

.field public static transition_layout_save:I = 0x7f0a093f

.field public static transition_pause_alpha:I = 0x7f0a0940

.field public static transition_position:I = 0x7f0a0941

.field public static transition_scene_layoutid_cache:I = 0x7f0a0942

.field public static transition_transform:I = 0x7f0a0943


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
