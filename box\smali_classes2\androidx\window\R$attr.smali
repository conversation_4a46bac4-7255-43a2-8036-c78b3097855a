.class public final Landroidx/window/R$attr;
.super Ljava/lang/Object;


# static fields
.field public static activityAction:I = 0x7f04002d

.field public static activityName:I = 0x7f04002f

.field public static alwaysExpand:I = 0x7f040043

.field public static clearTop:I = 0x7f040241

.field public static finishPrimaryWithSecondary:I = 0x7f04036a

.field public static finishSecondaryWithPrimary:I = 0x7f04036b

.field public static placeholderActivityName:I = 0x7f0405b1

.field public static primaryActivityName:I = 0x7f0405db

.field public static secondaryActivityAction:I = 0x7f04062f

.field public static secondaryActivityName:I = 0x7f040630

.field public static splitLayoutDirection:I = 0x7f04068a

.field public static splitMinSmallestWidth:I = 0x7f04068b

.field public static splitMinWidth:I = 0x7f04068c

.field public static splitRatio:I = 0x7f04068d


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
