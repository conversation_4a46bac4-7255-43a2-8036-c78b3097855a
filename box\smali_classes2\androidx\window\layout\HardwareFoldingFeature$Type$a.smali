.class public final Landroidx/window/layout/HardwareFoldingFeature$Type$a;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/window/layout/HardwareFoldingFeature$Type;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public synthetic constructor <init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    invoke-direct {p0}, Landroidx/window/layout/HardwareFoldingFeature$Type$a;-><init>()V

    return-void
.end method


# virtual methods
.method public final a()Landroidx/window/layout/HardwareFoldingFeature$Type;
    .locals 1

    invoke-static {}, Landroidx/window/layout/HardwareFoldingFeature$Type;->access$getFOLD$cp()Landroidx/window/layout/HardwareFoldingFeature$Type;

    move-result-object v0

    return-object v0
.end method

.method public final b()Landroidx/window/layout/HardwareFoldingFeature$Type;
    .locals 1

    invoke-static {}, Landroidx/window/layout/HardwareFoldingFeature$Type;->access$getHINGE$cp()Landroidx/window/layout/HardwareFoldingFeature$Type;

    move-result-object v0

    return-object v0
.end method
