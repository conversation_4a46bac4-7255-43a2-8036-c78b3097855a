.class public interface abstract Landroidx/window/layout/n;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Landroidx/window/layout/n$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation


# virtual methods
.method public abstract a(Landroidx/window/layout/n$a;)V
.end method

.method public abstract b(Landroid/app/Activity;)V
.end method

.method public abstract c(Landroid/app/Activity;)V
.end method
