.class public interface abstract Landroidx/window/layout/r;
.super Ljava/lang/Object;

# interfaces
.implements Landroidx/window/layout/l;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Landroidx/window/layout/r$a;,
        Landroidx/window/layout/r$b;,
        Landroidx/window/layout/r$c;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation


# virtual methods
.method public abstract getOrientation()Landroidx/window/layout/r$b;
.end method

.method public abstract getState()Landroidx/window/layout/r$c;
.end method

.method public abstract isSeparating()Z
.end method
