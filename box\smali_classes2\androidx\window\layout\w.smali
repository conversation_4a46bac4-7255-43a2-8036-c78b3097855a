.class public final synthetic Landroidx/window/layout/w;
.super Ljava/lang/Object;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    sget-object v0, Landroidx/window/layout/x;->a:Landroidx/window/layout/x$a;

    return-void
.end method

.method public static a(Landroid/content/Context;)Landroidx/window/layout/x;
    .locals 1
    .annotation build Lkotlin/jvm/JvmName;
    .end annotation

    .annotation runtime Lkotlin/jvm/JvmStatic;
    .end annotation

    sget-object v0, Landroidx/window/layout/x;->a:Landroidx/window/layout/x$a;

    invoke-virtual {v0, p0}, Landroidx/window/layout/x$a;->a(Landroid/content/Context;)Landroidx/window/layout/x;

    move-result-object p0

    return-object p0
.end method
