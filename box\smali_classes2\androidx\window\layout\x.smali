.class public interface abstract Landroidx/window/layout/x;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Landroidx/window/layout/x$a;
    }
.end annotation

.annotation runtime L<PERSON>lin/Metadata;
.end annotation


# static fields
.field public static final a:Landroidx/window/layout/x$a;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    sget-object v0, Landroidx/window/layout/x$a;->a:Landroidx/window/layout/x$a;

    sput-object v0, Landroidx/window/layout/x;->a:Landroidx/window/layout/x$a;

    return-void
.end method


# virtual methods
.method public abstract a(Landroid/app/Activity;)Lkotlinx/coroutines/flow/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/app/Activity;",
            ")",
            "Lkotlinx/coroutines/flow/a<",
            "Landroidx/window/layout/a0;",
            ">;"
        }
    .end annotation
.end method
