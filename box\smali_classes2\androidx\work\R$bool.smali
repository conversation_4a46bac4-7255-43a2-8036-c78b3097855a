.class public final Landroidx/work/R$bool;
.super Ljava/lang/Object;


# static fields
.field public static enable_system_alarm_service_default:I = 0x7f050002

.field public static enable_system_foreground_service_default:I = 0x7f050003

.field public static enable_system_job_service_default:I = 0x7f050004

.field public static workmanager_test_configuration:I = 0x7f050007


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
