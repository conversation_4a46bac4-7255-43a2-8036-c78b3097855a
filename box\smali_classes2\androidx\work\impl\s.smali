.class public interface abstract Landroidx/work/impl/s;
.super Ljava/lang/Object;


# virtual methods
.method public abstract b(Ljava/lang/String;)V
    .param p1    # Ljava/lang/String;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
.end method

.method public abstract d()Z
.end method

.method public varargs abstract e([Lx4/u;)V
    .param p1    # [Lx4/u;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
.end method
