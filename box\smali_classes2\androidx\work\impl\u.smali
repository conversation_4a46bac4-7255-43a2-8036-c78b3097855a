.class public final Landroidx/work/impl/u;
.super Ljava/lang/Object;


# annotations
.annotation runtime L<PERSON><PERSON>/Metadata;
.end annotation


# instance fields
.field public final a:Lx4/m;


# direct methods
.method public constructor <init>(Lx4/m;)V
    .locals 1

    const-string v0, "id"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Landroidx/work/impl/u;->a:Lx4/m;

    return-void
.end method


# virtual methods
.method public final a()Lx4/m;
    .locals 1

    iget-object v0, p0, Landroidx/work/impl/u;->a:Lx4/m;

    return-object v0
.end method
