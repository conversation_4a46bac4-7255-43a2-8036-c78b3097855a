.class public final synthetic Landroidx/work/impl/x;
.super Ljava/lang/Object;

# interfaces
.implements Ll4/h$c;


# instance fields
.field public final synthetic a:Landroid/content/Context;


# direct methods
.method public synthetic constructor <init>(Landroid/content/Context;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Landroidx/work/impl/x;->a:Landroid/content/Context;

    return-void
.end method


# virtual methods
.method public final a(Ll4/h$b;)Ll4/h;
    .locals 1

    iget-object v0, p0, Landroidx/work/impl/x;->a:Landroid/content/Context;

    invoke-static {v0, p1}, Landroidx/work/impl/WorkDatabase$a;->a(Landroid/content/Context;Ll4/h$b;)Ll4/h;

    move-result-object p1

    return-object p1
.end method
