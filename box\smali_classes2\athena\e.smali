.class public final synthetic Lathena/e;
.super Ljava/lang/Object;

# interfaces
.implements Lathena/z$a;


# instance fields
.field public final synthetic a:Lathena/c0;


# direct methods
.method public synthetic constructor <init>(Lathena/c0;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lathena/e;->a:Lathena/c0;

    return-void
.end method


# virtual methods
.method public final a(Lathena/y;)V
    .locals 1

    iget-object v0, p0, Lathena/e;->a:Lathena/c0;

    invoke-static {v0, p1}, Lathena/c0;->f(Lathena/c0;Lathena/y;)V

    return-void
.end method
