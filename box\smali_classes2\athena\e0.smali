.class public Lathena/e0;
.super Lathena/y;


# instance fields
.field private final d:I

.field private final e:Lmk/b;

.field private final f:Ljava/lang/String;

.field private final g:J

.field private final h:I

.field private i:J

.field private final j:Llk/c;


# direct methods
.method public constructor <init>(ILmk/b;Ljava/lang/String;JIJLlk/c;)V
    .locals 0

    invoke-direct {p0}, Lathena/y;-><init>()V

    iput p1, p0, Lathena/e0;->d:I

    iput-object p2, p0, Lathena/e0;->e:Lmk/b;

    iput-object p3, p0, Lathena/e0;->f:Ljava/lang/String;

    iput-wide p4, p0, Lathena/e0;->g:J

    iput p6, p0, Lathena/e0;->h:I

    iput-wide p7, p0, Lathena/e0;->i:J

    iput-object p9, p0, Lathena/e0;->j:Llk/c;

    return-void
.end method


# virtual methods
.method public a()V
    .locals 7

    :try_start_0
    invoke-static {}, Lmk/d;->f()Lorg/json/JSONObject;

    move-result-object v0
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    const-string v1, "sname"

    :try_start_1
    iget v2, p0, Lathena/e0;->d:I

    invoke-static {v2}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v0, v1, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;
    :try_end_1
    .catch Ljava/lang/Exception; {:try_start_1 .. :try_end_1} :catch_0

    const-string v1, "sdkver"

    :try_start_2
    iget v2, p0, Lathena/e0;->d:I

    invoke-static {v2}, Lmk/g;->a(I)Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v0, v1, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    invoke-virtual {v0}, Lorg/json/JSONObject;->toString()Ljava/lang/String;

    move-result-object v0
    :try_end_2
    .catch Ljava/lang/Exception; {:try_start_2 .. :try_end_2} :catch_0

    goto :goto_0

    :catch_0
    move-exception v0

    invoke-virtual {v0}, Ljava/lang/Throwable;->getMessage()Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lathena/a0;->c(Ljava/lang/String;)V

    const-string v0, ""

    :goto_0
    iget-object v1, p0, Lathena/e0;->f:Ljava/lang/String;

    iget-wide v2, p0, Lathena/e0;->g:J

    iget v4, p0, Lathena/e0;->d:I

    invoke-static {v1, v0, v2, v3, v4}, Lathena/v;->b(Ljava/lang/String;Ljava/lang/String;JI)Lathena/y0;

    move-result-object v0

    iget v1, v0, Lathena/y0;->a:I

    const-string v2, "<-- appIdConfig:%s"

    const/4 v3, 0x0

    const/4 v4, 0x1

    if-eqz v1, :cond_1

    if-eq v1, v4, :cond_0

    iget-object v0, p0, Lathena/e0;->e:Lmk/b;

    invoke-virtual {v0}, Lmk/b;->t()I

    move-result v1

    add-int/2addr v1, v4

    invoke-virtual {v0, v1}, Lmk/b;->s(I)V

    goto :goto_1

    :cond_0
    new-array v0, v4, [Ljava/lang/Object;

    const-string v1, "NOT_MODIFIED"

    aput-object v1, v0, v3

    invoke-static {v2, v0}, Lathena/a0;->f(Ljava/lang/String;[Ljava/lang/Object;)V

    iget-object v0, p0, Lathena/e0;->e:Lmk/b;

    iget v1, p0, Lathena/e0;->h:I

    invoke-virtual {v0, v1}, Lmk/b;->i(I)V

    iget-object v0, p0, Lathena/e0;->e:Lmk/b;

    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v1

    iget-wide v4, p0, Lathena/e0;->i:J

    add-long/2addr v1, v4

    invoke-virtual {v0, v1, v2}, Lmk/b;->j(J)V

    goto :goto_1

    :cond_1
    iget-object v0, v0, Lathena/y0;->b:Ljava/lang/Object;

    check-cast v0, Ljava/lang/String;

    new-array v1, v4, [Ljava/lang/Object;

    aput-object v0, v1, v3

    invoke-static {v2, v1}, Lathena/a0;->f(Ljava/lang/String;[Ljava/lang/Object;)V

    invoke-static {}, Llk/b;->a()Llk/a;

    move-result-object v1

    iget-object v2, p0, Lathena/e0;->e:Lmk/b;

    invoke-virtual {v1, v2, v0}, Llk/a;->i(Lmk/b;Ljava/lang/String;)V

    iget-object v0, p0, Lathena/e0;->e:Lmk/b;

    iget v1, p0, Lathena/e0;->h:I

    invoke-virtual {v0, v1}, Lmk/b;->i(I)V

    iget-object v0, p0, Lathena/e0;->e:Lmk/b;

    invoke-virtual {v0}, Lmk/b;->k()I

    move-result v0

    if-lez v0, :cond_2

    iget-object v0, p0, Lathena/e0;->e:Lmk/b;

    invoke-virtual {v0}, Lmk/b;->k()I

    move-result v0

    int-to-long v0, v0

    const-wide/32 v2, 0x36ee80

    mul-long v0, v0, v2

    iput-wide v0, p0, Lathena/e0;->i:J

    :cond_2
    iget-object v0, p0, Lathena/e0;->e:Lmk/b;

    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v1

    iget-wide v5, p0, Lathena/e0;->i:J

    add-long/2addr v1, v5

    invoke-virtual {v0, v1, v2}, Lmk/b;->j(J)V

    const/4 v3, 0x1

    :goto_1
    iget-object v0, p0, Lathena/e0;->j:Llk/c;

    if-eqz v0, :cond_3

    iget-object v1, p0, Lathena/e0;->e:Lmk/b;

    invoke-virtual {v1}, Lmk/b;->a()I

    move-result v1

    invoke-virtual {v0, v1, v3}, Llk/c;->a(IZ)V

    :cond_3
    return-void
.end method

.method public d()Ljava/lang/String;
    .locals 2

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "Retrieve-"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget v1, p0, Lathena/e0;->d:I

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v1, "-Config"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
