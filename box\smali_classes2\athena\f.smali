.class public final synthetic Lathena/f;
.super Ljava/lang/Object;

# interfaces
.implements Lathena/l0;


# direct methods
.method public synthetic constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final a(Ljava/lang/Object;)V
    .locals 0

    check-cast p1, Landroid/util/SparseArray;

    invoke-static {p1}, Lathena/c0;->e(Landroid/util/SparseArray;)V

    return-void
.end method
