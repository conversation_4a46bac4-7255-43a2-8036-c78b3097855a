.class public Lathena/g0;
.super Lathena/y;


# static fields
.field private static final d:[Ljava/lang/String;


# instance fields
.field private e:I


# direct methods
.method static constructor <clinit>()V
    .locals 3

    const-string v0, "https://time.cloudflare.com"

    const-string v1, "https://www.pool.ntp.org"

    const-string v2, "https://time1.google.com"

    filled-new-array {v2, v0, v1}, [Ljava/lang/String;

    move-result-object v0

    sput-object v0, Lathena/g0;->d:[Ljava/lang/String;

    return-void
.end method

.method public constructor <init>()V
    .locals 1

    invoke-direct {p0}, Lathena/y;-><init>()V

    const/4 v0, 0x0

    iput v0, p0, Lathena/g0;->e:I

    return-void
.end method


# virtual methods
.method public a()V
    .locals 3

    iget-object v0, p0, Lathena/y;->b:Landroid/os/Handler;

    const/16 v1, 0x1f7

    invoke-virtual {v0, v1}, Landroid/os/Handler;->obtainMessage(I)Landroid/os/Message;

    move-result-object v0

    const-wide/16 v1, 0x0

    invoke-static {v1, v2}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    move-result-object v1

    iput-object v1, v0, Landroid/os/Message;->obj:Ljava/lang/Object;

    :goto_0
    iget v1, p0, Lathena/g0;->e:I

    const/4 v2, 0x3

    if-ge v1, v2, :cond_1

    sget-object v2, Lathena/g0;->d:[Ljava/lang/String;

    aget-object v1, v2, v1

    invoke-static {v1}, Lathena/v;->j(Ljava/lang/String;)Lathena/y0;

    move-result-object v1

    iget v2, v1, Lathena/y0;->a:I

    if-nez v2, :cond_0

    iget-object v1, v1, Lathena/y0;->b:Ljava/lang/Object;

    iput-object v1, v0, Landroid/os/Message;->obj:Ljava/lang/Object;

    goto :goto_1

    :cond_0
    iget v1, p0, Lathena/g0;->e:I

    add-int/lit8 v1, v1, 0x1

    iput v1, p0, Lathena/g0;->e:I

    goto :goto_0

    :cond_1
    :goto_1
    iget-object v1, p0, Lathena/y;->b:Landroid/os/Handler;

    invoke-virtual {v1, v0}, Landroid/os/Handler;->sendMessageAtFrontOfQueue(Landroid/os/Message;)Z

    return-void
.end method

.method public d()Ljava/lang/String;
    .locals 1

    const-string v0, "Retrieve-Time"

    return-object v0
.end method
