.class Lathena/h$b;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lathena/h;->P()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic a:Lathena/h;


# direct methods
.method public constructor <init>(Lathena/h;)V
    .locals 0

    iput-object p1, p0, Lathena/h$b;->a:Lathena/h;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 3

    iget-object v0, p0, Lathena/h$b;->a:Lathena/h;

    invoke-static {}, Lgm/a;->a()Landroid/content/Context;

    move-result-object v1

    invoke-static {v1}, Lathena/j0;->r(Landroid/content/Context;)Z

    move-result v1

    invoke-static {v0, v1}, Lathena/h;->E(Lathena/h;Z)Z

    iget-object v0, p0, Lathena/h$b;->a:Lathena/h;

    invoke-static {v0}, Lathena/h;->I(Lathena/h;)Landroid/os/Handler;

    move-result-object v0

    const-wide/32 v1, 0x36ee80

    invoke-virtual {v0, p0, v1, v2}, Landroid/os/Handler;->postDelayed(Ljava/lang/Runnable;J)Z

    return-void
.end method
