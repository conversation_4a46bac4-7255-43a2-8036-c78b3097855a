.class public Lathena/h$d;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/transsion/gslb/GslbSdk$InitListener;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lathena/h;->p(IZ)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field public final synthetic a:Ljava/lang/String;

.field public final synthetic b:I

.field public final synthetic c:Lmk/b;

.field public final synthetic d:Lathena/h;


# direct methods
.method public constructor <init>(Lathena/h;Ljava/lang/String;ILmk/b;)V
    .locals 0

    iput-object p1, p0, Lathena/h$d;->d:Lathena/h;

    iput-object p2, p0, Lathena/h$d;->a:Ljava/lang/String;

    iput p3, p0, Lathena/h$d;->b:I

    iput-object p4, p0, Lathena/h$d;->c:Lmk/b;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public onInitFail()V
    .locals 0

    return-void
.end method

.method public onInitSuccess(Ljava/util/Map;)V
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ">;)V"
        }
    .end annotation

    iget-object v0, p0, Lathena/h$d;->d:Lathena/h;

    iget-object v1, p0, Lathena/h$d;->a:Ljava/lang/String;

    iget v2, p0, Lathena/h$d;->b:I

    iget-object v3, p0, Lathena/h$d;->c:Lmk/b;

    if-nez p1, :cond_0

    const-string p1, ""

    goto :goto_0

    :cond_0
    invoke-virtual {p1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object p1

    :goto_0
    invoke-static {v0, v1, v2, v3, p1}, Lathena/h;->v(Lathena/h;Ljava/lang/String;ILmk/b;Ljava/lang/String;)V

    return-void
.end method
