.class public Lathena/h$h;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lathena/h;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "h"
.end annotation


# static fields
.field public static final a:Lathena/h;


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lathena/h;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lathena/h;-><init>(Lathena/h$a;)V

    sput-object v0, Lathena/h$h;->a:Lathena/h;

    return-void
.end method

.method public static synthetic a()Lathena/h;
    .locals 1

    sget-object v0, Lathena/h$h;->a:Lathena/h;

    return-object v0
.end method
