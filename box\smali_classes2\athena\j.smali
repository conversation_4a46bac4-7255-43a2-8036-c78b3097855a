.class public interface abstract Lathena/j;
.super Ljava/lang/Object;


# static fields
.field public static final a:[Ljava/lang/String;


# direct methods
.method static constructor <clinit>()V
    .locals 4

    const-string v0, "dsu-test.shalltry.com"

    const-string v1, "dsc-test.shalltry.com"

    const-string v2, "dsu.shalltry.com"

    const-string v3, "dsc.shalltry.com"

    filled-new-array {v2, v3, v0, v1}, [Ljava/lang/String;

    move-result-object v0

    sput-object v0, Lathena/j;->a:[Ljava/lang/String;

    return-void
.end method
