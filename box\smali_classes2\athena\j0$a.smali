.class public final enum Lathena/j0$a;
.super Ljava/lang/Enum;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lathena/j0;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x4019
    name = "a"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Enum<",
        "Lathena/j0$a;",
        ">;"
    }
.end annotation


# static fields
.field public static final enum a:Lathena/j0$a;

.field public static final enum b:Lathena/j0$a;

.field public static final enum c:Lathena/j0$a;

.field public static final enum d:Lathena/j0$a;

.field public static final enum e:Lathena/j0$a;

.field public static final enum f:Lathena/j0$a;

.field public static final enum g:Lathena/j0$a;


# direct methods
.method static constructor <clinit>()V
    .locals 3

    new-instance v0, Lathena/j0$a;

    const-string v1, "NETWORK_UNKNOWN"

    const/4 v2, 0x0

    invoke-direct {v0, v1, v2}, Lathena/j0$a;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lathena/j0$a;->a:Lathena/j0$a;

    new-instance v0, Lathena/j0$a;

    const-string v1, "NETWORK_WIFI"

    const/4 v2, 0x1

    invoke-direct {v0, v1, v2}, Lathena/j0$a;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lathena/j0$a;->b:Lathena/j0$a;

    new-instance v0, Lathena/j0$a;

    const-string v1, "NETWORK_2G"

    const/4 v2, 0x2

    invoke-direct {v0, v1, v2}, Lathena/j0$a;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lathena/j0$a;->c:Lathena/j0$a;

    new-instance v0, Lathena/j0$a;

    const-string v1, "NETWORK_3G"

    const/4 v2, 0x3

    invoke-direct {v0, v1, v2}, Lathena/j0$a;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lathena/j0$a;->d:Lathena/j0$a;

    new-instance v0, Lathena/j0$a;

    const-string v1, "NETWORK_4G"

    const/4 v2, 0x4

    invoke-direct {v0, v1, v2}, Lathena/j0$a;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lathena/j0$a;->e:Lathena/j0$a;

    new-instance v0, Lathena/j0$a;

    const-string v1, "NETWORK_5G"

    const/4 v2, 0x5

    invoke-direct {v0, v1, v2}, Lathena/j0$a;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lathena/j0$a;->f:Lathena/j0$a;

    new-instance v0, Lathena/j0$a;

    const-string v1, "NETWORK_ETHERNET"

    const/4 v2, 0x6

    invoke-direct {v0, v1, v2}, Lathena/j0$a;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lathena/j0$a;->g:Lathena/j0$a;

    return-void
.end method

.method private constructor <init>(Ljava/lang/String;I)V
    .locals 0

    invoke-direct {p0, p1, p2}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    return-void
.end method
