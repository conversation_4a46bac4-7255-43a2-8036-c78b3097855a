.class public abstract Lathena/l;
.super Ljava/lang/Object;


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static a(Landroid/content/Context;I)Lathena/l;
    .locals 1

    const/4 v0, 0x1

    if-eq p1, v0, :cond_1

    const/4 v0, 0x2

    if-eq p1, v0, :cond_0

    const/4 p0, 0x3

    if-eq p1, p0, :cond_1

    const/4 p0, 0x0

    return-object p0

    :cond_0
    invoke-static {p0}, Lathena/n;->i(Landroid/content/Context;)Lathena/n;

    move-result-object p0

    return-object p0

    :cond_1
    invoke-static {}, Lathena/h$h;->a()Lathena/h;

    move-result-object p0

    return-object p0
.end method


# virtual methods
.method public b()V
    .locals 0

    return-void
.end method

.method public c(I)V
    .locals 0

    return-void
.end method

.method public abstract d(Landroid/os/Message;J)V
.end method

.method public abstract e(Ljava/lang/Runnable;)V
.end method

.method public abstract f(Ljava/lang/String;Lcom/transsion/athena/data/TrackData;J)V
.end method

.method public g()V
    .locals 0

    return-void
.end method

.method public h()V
    .locals 0

    return-void
.end method
