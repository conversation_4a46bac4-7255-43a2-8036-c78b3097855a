.class public final synthetic Lathena/m;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/lang/Runnable;


# instance fields
.field public final synthetic a:Lathena/f0;


# direct methods
.method public synthetic constructor <init>(Lathena/f0;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lathena/m;->a:Lathena/f0;

    return-void
.end method


# virtual methods
.method public final run()V
    .locals 1

    iget-object v0, p0, Lathena/m;->a:Lathena/f0;

    invoke-static {v0}, Lathena/f0;->e(Lathena/f0;)V

    return-void
.end method
