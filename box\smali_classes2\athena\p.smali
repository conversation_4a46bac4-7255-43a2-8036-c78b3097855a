.class public final synthetic Lathena/p;
.super Ljava/lang/Object;

# interfaces
.implements Lathena/l0;


# instance fields
.field public final synthetic a:Lathena/h;


# direct methods
.method public synthetic constructor <init>(Lathena/h;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lathena/p;->a:Lathena/h;

    return-void
.end method


# virtual methods
.method public final a(Ljava/lang/Object;)V
    .locals 1

    iget-object v0, p0, Lathena/p;->a:Lathena/h;

    check-cast p1, Landroid/util/LongSparseArray;

    invoke-static {v0, p1}, Lathena/h;->m(Lathena/h;Landroid/util/LongSparseArray;)V

    return-void
.end method
