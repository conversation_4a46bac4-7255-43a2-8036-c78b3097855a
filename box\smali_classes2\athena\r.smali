.class public final synthetic Lathena/r;
.super Ljava/lang/Object;

# interfaces
.implements Lathena/l0;


# direct methods
.method public synthetic constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final a(Ljava/lang/Object;)V
    .locals 0

    check-cast p1, Ljava/lang/String;

    invoke-static {p1}, Lathena/h;->l(Ljava/lang/String;)V

    return-void
.end method
