.class public final synthetic Lathena/v0;
.super Ljava/lang/Object;

# interfaces
.implements Landroidx/core/util/j;


# direct methods
.method public synthetic constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final test(Ljava/lang/Object;)Z
    .locals 0

    check-cast p1, Lathena/y;

    invoke-static {p1}, Lathena/x0;->b(Lathena/y;)Z

    move-result p1

    return p1
.end method
