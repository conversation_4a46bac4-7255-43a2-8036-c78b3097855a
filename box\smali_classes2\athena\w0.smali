.class public final synthetic Lathena/w0;
.super Ljava/lang/Object;

# interfaces
.implements Landroidx/core/util/j;


# instance fields
.field public final synthetic a:Ljava/lang/String;


# direct methods
.method public synthetic constructor <init>(Ljava/lang/String;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lathena/w0;->a:Ljava/lang/String;

    return-void
.end method


# virtual methods
.method public final test(Ljava/lang/Object;)Z
    .locals 1

    iget-object v0, p0, Lathena/w0;->a:Ljava/lang/String;

    check-cast p1, Lathena/y;

    invoke-static {v0, p1}, Lathena/x0;->a(Ljava/lang/String;Lathena/y;)Z

    move-result p1

    return p1
.end method
