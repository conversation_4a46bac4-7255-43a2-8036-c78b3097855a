.class public final Lb4/d;
.super Ljava/lang/Object;


# instance fields
.field public final a:Ld2/a;

.field public final b:J

.field public final c:J


# direct methods
.method public constructor <init>(Ld2/a;JJ)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lb4/d;->a:Ld2/a;

    iput-wide p2, p0, Lb4/d;->b:J

    iput-wide p4, p0, Lb4/d;->c:J

    return-void
.end method
