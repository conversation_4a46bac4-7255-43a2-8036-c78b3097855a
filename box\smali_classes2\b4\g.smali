.class public final Lb4/g;
.super Ljava/lang/Object;

# interfaces
.implements Lt3/s;


# instance fields
.field public final a:Le2/c0;

.field public final b:Lb4/b;


# direct methods
.method public constructor <init>()V
    .locals 1

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    new-instance v0, Le2/c0;

    invoke-direct {v0}, Le2/c0;-><init>()V

    iput-object v0, p0, Lb4/g;->a:Le2/c0;

    new-instance v0, Lb4/b;

    invoke-direct {v0}, Lb4/b;-><init>()V

    iput-object v0, p0, Lb4/g;->b:Lb4/b;

    return-void
.end method

.method public static e(Le2/c0;)I
    .locals 5

    const/4 v0, -0x1

    const/4 v1, 0x0

    const/4 v2, -0x1

    const/4 v3, 0x0

    :goto_0
    if-ne v2, v0, :cond_3

    invoke-virtual {p0}, Le2/c0;->f()I

    move-result v3

    invoke-virtual {p0}, Le2/c0;->s()Ljava/lang/String;

    move-result-object v2

    if-nez v2, :cond_0

    const/4 v2, 0x0

    goto :goto_0

    :cond_0
    const-string v4, "STYLE"

    invoke-virtual {v4, v2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v4

    if-eqz v4, :cond_1

    const/4 v2, 0x2

    goto :goto_0

    :cond_1
    const-string v4, "NOTE"

    invoke-virtual {v2, v4}, Ljava/lang/String;->startsWith(Ljava/lang/String;)Z

    move-result v2

    if-eqz v2, :cond_2

    const/4 v2, 0x1

    goto :goto_0

    :cond_2
    const/4 v2, 0x3

    goto :goto_0

    :cond_3
    invoke-virtual {p0, v3}, Le2/c0;->U(I)V

    return v2
.end method

.method public static f(Le2/c0;)V
    .locals 1

    :goto_0
    invoke-virtual {p0}, Le2/c0;->s()Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    if-nez v0, :cond_0

    goto :goto_0

    :cond_0
    return-void
.end method


# virtual methods
.method public synthetic a([BII)Lt3/k;
    .locals 0

    invoke-static {p0, p1, p2, p3}, Lt3/r;->b(Lt3/s;[BII)Lt3/k;

    move-result-object p1

    return-object p1
.end method

.method public b([BIILt3/s$b;Le2/h;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "([BII",
            "Lt3/s$b;",
            "Le2/h<",
            "Lt3/e;",
            ">;)V"
        }
    .end annotation

    iget-object v0, p0, Lb4/g;->a:Le2/c0;

    add-int/2addr p3, p2

    invoke-virtual {v0, p1, p3}, Le2/c0;->S([BI)V

    iget-object p1, p0, Lb4/g;->a:Le2/c0;

    invoke-virtual {p1, p2}, Le2/c0;->U(I)V

    new-instance p1, Ljava/util/ArrayList;

    invoke-direct {p1}, Ljava/util/ArrayList;-><init>()V

    :try_start_0
    iget-object p2, p0, Lb4/g;->a:Le2/c0;

    invoke-static {p2}, Lb4/h;->e(Le2/c0;)V
    :try_end_0
    .catch Landroidx/media3/common/ParserException; {:try_start_0 .. :try_end_0} :catch_0

    :goto_0
    iget-object p2, p0, Lb4/g;->a:Le2/c0;

    invoke-virtual {p2}, Le2/c0;->s()Ljava/lang/String;

    move-result-object p2

    invoke-static {p2}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result p2

    if-nez p2, :cond_0

    goto :goto_0

    :cond_0
    new-instance p2, Ljava/util/ArrayList;

    invoke-direct {p2}, Ljava/util/ArrayList;-><init>()V

    :cond_1
    :goto_1
    iget-object p3, p0, Lb4/g;->a:Le2/c0;

    invoke-static {p3}, Lb4/g;->e(Le2/c0;)I

    move-result p3

    if-eqz p3, :cond_5

    const/4 v0, 0x1

    if-ne p3, v0, :cond_2

    iget-object p3, p0, Lb4/g;->a:Le2/c0;

    invoke-static {p3}, Lb4/g;->f(Le2/c0;)V

    goto :goto_1

    :cond_2
    const/4 v0, 0x2

    if-ne p3, v0, :cond_4

    invoke-interface {p2}, Ljava/util/List;->isEmpty()Z

    move-result p3

    if-eqz p3, :cond_3

    iget-object p3, p0, Lb4/g;->a:Le2/c0;

    invoke-virtual {p3}, Le2/c0;->s()Ljava/lang/String;

    iget-object p3, p0, Lb4/g;->b:Lb4/b;

    iget-object v0, p0, Lb4/g;->a:Le2/c0;

    invoke-virtual {p3, v0}, Lb4/b;->d(Le2/c0;)Ljava/util/List;

    move-result-object p3

    invoke-interface {p1, p3}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    goto :goto_1

    :cond_3
    new-instance p1, Ljava/lang/IllegalArgumentException;

    const-string p2, "A style block was found after the first cue."

    invoke-direct {p1, p2}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p1

    :cond_4
    const/4 v0, 0x3

    if-ne p3, v0, :cond_1

    iget-object p3, p0, Lb4/g;->a:Le2/c0;

    invoke-static {p3, p1}, Lb4/e;->m(Le2/c0;Ljava/util/List;)Lb4/d;

    move-result-object p3

    if-eqz p3, :cond_1

    invoke-interface {p2, p3}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_1

    :cond_5
    new-instance p1, Lb4/j;

    invoke-direct {p1, p2}, Lb4/j;-><init>(Ljava/util/List;)V

    invoke-static {p1, p4, p5}, Lt3/i;->c(Lt3/k;Lt3/s$b;Le2/h;)V

    return-void

    :catch_0
    move-exception p1

    new-instance p2, Ljava/lang/IllegalArgumentException;

    invoke-direct {p2, p1}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/Throwable;)V

    throw p2
.end method

.method public synthetic c([BLt3/s$b;Le2/h;)V
    .locals 0

    invoke-static {p0, p1, p2, p3}, Lt3/r;->a(Lt3/s;[BLt3/s$b;Le2/h;)V

    return-void
.end method

.method public d()I
    .locals 1

    const/4 v0, 0x1

    return v0
.end method

.method public synthetic reset()V
    .locals 0

    invoke-static {p0}, Lt3/r;->c(Lt3/s;)V

    return-void
.end method
