.class public Lb7/c;
.super Landroid/view/GestureDetector;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lb7/c$a;
    }
.end annotation


# instance fields
.field public a:Lb7/c$a;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(Landroid/content/Context;)V
    .locals 1
    .param p1    # Landroid/content/Context;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param

    new-instance v0, Lb7/c$a;

    invoke-direct {v0}, Lb7/c$a;-><init>()V

    invoke-direct {p0, p1, v0}, Lb7/c;-><init>(Landroid/content/Context;Lb7/c$a;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Lb7/c$a;)V
    .locals 0
    .param p2    # Lb7/c$a;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param

    invoke-direct {p0, p1, p2}, Landroid/view/GestureDetector;-><init>(Landroid/content/Context;Landroid/view/GestureDetector$OnGestureListener;)V

    iput-object p2, p0, Lb7/c;->a:Lb7/c$a;

    const/4 p1, 0x0

    invoke-virtual {p0, p1}, Landroid/view/GestureDetector;->setIsLongpressEnabled(Z)V

    return-void
.end method


# virtual methods
.method public a()Z
    .locals 1

    iget-object v0, p0, Lb7/c;->a:Lb7/c$a;

    invoke-virtual {v0}, Lb7/c$a;->a()Z

    move-result v0

    return v0
.end method

.method public b(Z)V
    .locals 1

    iget-object v0, p0, Lb7/c;->a:Lb7/c$a;

    iput-boolean p1, v0, Lb7/c$a;->a:Z

    return-void
.end method
