.class public Lbd/g;
.super Landroidx/appcompat/graphics/drawable/b;


# instance fields
.field public final b:I

.field public final c:I


# direct methods
.method public constructor <init>(Landroid/graphics/drawable/Drawable;II)V
    .locals 0
    .param p1    # Landroid/graphics/drawable/Drawable;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param

    invoke-direct {p0, p1}, Landroidx/appcompat/graphics/drawable/b;-><init>(Landroid/graphics/drawable/Drawable;)V

    iput p2, p0, Lbd/g;->b:I

    iput p3, p0, Lbd/g;->c:I

    return-void
.end method


# virtual methods
.method public getIntrinsicHeight()I
    .locals 1

    iget v0, p0, Lbd/g;->c:I

    return v0
.end method

.method public getIntrinsicWidth()I
    .locals 1

    iget v0, p0, Lbd/g;->b:I

    return v0
.end method
