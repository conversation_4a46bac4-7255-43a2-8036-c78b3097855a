.class public final Lc0/b0;
.super Landroid/view/autofill/AutofillManager$AutofillCallback;


# annotations
.annotation build Landroidx/annotation/RequiresApi;
    value = 0x1a
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation


# static fields
.field public static final a:Lc0/b0;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    new-instance v0, Lc0/b0;

    invoke-direct {v0}, Lc0/b0;-><init>()V

    sput-object v0, Lc0/b0;->a:Lc0/b0;

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Landroid/view/autofill/AutofillManager$AutofillCallback;-><init>()V

    return-void
.end method


# virtual methods
.method public final a(Lc0/e;)V
    .locals 1

    invoke-virtual {p1}, Lc0/e;->a()Landroid/view/autofill/AutofillManager;

    move-result-object p1

    invoke-static {p0}, Lc0/y;->a(Lja<PERSON>/lang/Object;)Landroid/view/autofill/AutofillManager$AutofillCallback;

    move-result-object v0

    invoke-static {p1, v0}, Lc0/a0;->a(Landroid/view/autofill/AutofillManager;Landroid/view/autofill/AutofillManager$AutofillCallback;)V

    return-void
.end method

.method public final b(Lc0/e;)V
    .locals 1

    invoke-virtual {p1}, Lc0/e;->a()Landroid/view/autofill/AutofillManager;

    move-result-object p1

    invoke-static {p0}, Lc0/y;->a(Ljava/lang/Object;)Landroid/view/autofill/AutofillManager$AutofillCallback;

    move-result-object v0

    invoke-static {p1, v0}, Lc0/z;->a(Landroid/view/autofill/AutofillManager;Landroid/view/autofill/AutofillManager$AutofillCallback;)V

    return-void
.end method

.method public onAutofillEvent(Landroid/view/View;II)V
    .locals 0

    invoke-super {p0, p1, p2, p3}, Landroid/view/autofill/AutofillManager$AutofillCallback;->onAutofillEvent(Landroid/view/View;II)V

    return-void
.end method
