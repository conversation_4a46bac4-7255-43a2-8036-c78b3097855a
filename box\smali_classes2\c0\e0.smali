.class public final Lc0/e0;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lc0/e0$a;
    }
.end annotation

.annotation runtime L<PERSON>lin/Metadata;
.end annotation

.annotation runtime Lkotlin/jvm/JvmInline;
.end annotation


# static fields
.field public static final b:Lc0/e0$a;

.field public static final c:I

.field public static final d:I

.field public static final e:I

.field public static final f:I

.field public static final g:I


# instance fields
.field public final a:I


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lc0/e0$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lc0/e0$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lc0/e0;->b:Lc0/e0$a;

    const/4 v0, 0x1

    invoke-static {v0}, Lc0/e0;->b(I)I

    move-result v0

    sput v0, Lc0/e0;->c:I

    const/4 v0, 0x3

    invoke-static {v0}, Lc0/e0;->b(I)I

    move-result v0

    sput v0, Lc0/e0;->d:I

    const/4 v0, 0x4

    invoke-static {v0}, Lc0/e0;->b(I)I

    move-result v0

    sput v0, Lc0/e0;->e:I

    const/4 v0, 0x2

    invoke-static {v0}, Lc0/e0;->b(I)I

    move-result v0

    sput v0, Lc0/e0;->f:I

    const/4 v0, 0x0

    invoke-static {v0}, Lc0/e0;->b(I)I

    move-result v0

    sput v0, Lc0/e0;->g:I

    return-void
.end method

.method public static final synthetic a()I
    .locals 1

    sget v0, Lc0/e0;->c:I

    return v0
.end method

.method public static b(I)I
    .locals 0

    return p0
.end method

.method public static c(ILjava/lang/Object;)Z
    .locals 2

    instance-of v0, p1, Lc0/e0;

    const/4 v1, 0x0

    if-nez v0, :cond_0

    return v1

    :cond_0
    check-cast p1, Lc0/e0;

    invoke-virtual {p1}, Lc0/e0;->f()I

    move-result p1

    if-eq p0, p1, :cond_1

    return v1

    :cond_1
    const/4 p0, 0x1

    return p0
.end method

.method public static d(I)I
    .locals 0

    return p0
.end method

.method public static e(I)Ljava/lang/String;
    .locals 2

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "ContentDataType(dataType="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0, p0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const/16 p0, 0x29

    invoke-virtual {v0, p0}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    return-object p0
.end method


# virtual methods
.method public equals(Ljava/lang/Object;)Z
    .locals 1

    iget v0, p0, Lc0/e0;->a:I

    invoke-static {v0, p1}, Lc0/e0;->c(ILjava/lang/Object;)Z

    move-result p1

    return p1
.end method

.method public final synthetic f()I
    .locals 1

    iget v0, p0, Lc0/e0;->a:I

    return v0
.end method

.method public hashCode()I
    .locals 1

    iget v0, p0, Lc0/e0;->a:I

    invoke-static {v0}, Lc0/e0;->d(I)I

    move-result v0

    return v0
.end method

.method public toString()Ljava/lang/String;
    .locals 1

    iget v0, p0, Lc0/e0;->a:I

    invoke-static {v0}, Lc0/e0;->e(I)Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
