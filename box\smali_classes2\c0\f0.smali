.class public final Lc0/f0;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lc0/f0$a;
    }
.end annotation

.annotation runtime L<PERSON><PERSON>/Metadata;
.end annotation


# static fields
.field public static final A:Lc0/f0;

.field public static final B:Lc0/f0;

.field public static final C:Lc0/f0;

.field public static final D:Lc0/f0;

.field public static final E:Lc0/f0;

.field public static final F:Lc0/f0;

.field public static final G:Lc0/f0;

.field public static final H:Lc0/f0;

.field public static final I:Lc0/f0;

.field public static final J:Lc0/f0;

.field public static final K:Lc0/f0;

.field public static final L:Lc0/f0;

.field public static final M:Lc0/f0;

.field public static final b:Lc0/f0$a;

.field public static final c:I

.field public static final d:Lc0/f0;

.field public static final e:Lc0/f0;

.field public static final f:Lc0/f0;

.field public static final g:Lc0/f0;

.field public static final h:Lc0/f0;

.field public static final i:Lc0/f0;

.field public static final j:Lc0/f0;

.field public static final k:Lc0/f0;

.field public static final l:Lc0/f0;

.field public static final m:Lc0/f0;

.field public static final n:Lc0/f0;

.field public static final o:Lc0/f0;

.field public static final p:Lc0/f0;

.field public static final q:Lc0/f0;

.field public static final r:Lc0/f0;

.field public static final s:Lc0/f0;

.field public static final t:Lc0/f0;

.field public static final u:Lc0/f0;

.field public static final v:Lc0/f0;

.field public static final w:Lc0/f0;

.field public static final x:Lc0/f0;

.field public static final y:Lc0/f0;

.field public static final z:Lc0/f0;


# instance fields
.field public final a:Ljava/util/Set;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Set<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lc0/f0$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lc0/f0$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lc0/f0;->b:Lc0/f0$a;

    const/16 v0, 0x8

    sput v0, Lc0/f0;->c:I

    new-instance v0, Lc0/f0;

    const-string v1, "username"

    invoke-direct {v0, v1}, Lc0/f0;-><init>(Ljava/lang/String;)V

    sput-object v0, Lc0/f0;->d:Lc0/f0;

    new-instance v0, Lc0/f0;

    const-string v1, "password"

    invoke-direct {v0, v1}, Lc0/f0;-><init>(Ljava/lang/String;)V

    sput-object v0, Lc0/f0;->e:Lc0/f0;

    new-instance v0, Lc0/f0;

    const-string v1, "emailAddress"

    invoke-direct {v0, v1}, Lc0/f0;-><init>(Ljava/lang/String;)V

    sput-object v0, Lc0/f0;->f:Lc0/f0;

    new-instance v0, Lc0/f0;

    const-string v1, "newUsername"

    invoke-direct {v0, v1}, Lc0/f0;-><init>(Ljava/lang/String;)V

    sput-object v0, Lc0/f0;->g:Lc0/f0;

    new-instance v0, Lc0/f0;

    const-string v1, "newPassword"

    invoke-direct {v0, v1}, Lc0/f0;-><init>(Ljava/lang/String;)V

    sput-object v0, Lc0/f0;->h:Lc0/f0;

    new-instance v0, Lc0/f0;

    const-string v1, "postalAddress"

    invoke-direct {v0, v1}, Lc0/f0;-><init>(Ljava/lang/String;)V

    sput-object v0, Lc0/f0;->i:Lc0/f0;

    new-instance v0, Lc0/f0;

    const-string v1, "postalCode"

    invoke-direct {v0, v1}, Lc0/f0;-><init>(Ljava/lang/String;)V

    sput-object v0, Lc0/f0;->j:Lc0/f0;

    new-instance v0, Lc0/f0;

    const-string v1, "creditCardNumber"

    invoke-direct {v0, v1}, Lc0/f0;-><init>(Ljava/lang/String;)V

    sput-object v0, Lc0/f0;->k:Lc0/f0;

    new-instance v0, Lc0/f0;

    const-string v1, "creditCardSecurityCode"

    invoke-direct {v0, v1}, Lc0/f0;-><init>(Ljava/lang/String;)V

    sput-object v0, Lc0/f0;->l:Lc0/f0;

    new-instance v0, Lc0/f0;

    const-string v1, "creditCardExpirationDate"

    invoke-direct {v0, v1}, Lc0/f0;-><init>(Ljava/lang/String;)V

    sput-object v0, Lc0/f0;->m:Lc0/f0;

    new-instance v0, Lc0/f0;

    const-string v1, "creditCardExpirationMonth"

    invoke-direct {v0, v1}, Lc0/f0;-><init>(Ljava/lang/String;)V

    sput-object v0, Lc0/f0;->n:Lc0/f0;

    new-instance v0, Lc0/f0;

    const-string v1, "creditCardExpirationYear"

    invoke-direct {v0, v1}, Lc0/f0;-><init>(Ljava/lang/String;)V

    sput-object v0, Lc0/f0;->o:Lc0/f0;

    new-instance v0, Lc0/f0;

    const-string v1, "creditCardExpirationDay"

    invoke-direct {v0, v1}, Lc0/f0;-><init>(Ljava/lang/String;)V

    sput-object v0, Lc0/f0;->p:Lc0/f0;

    new-instance v0, Lc0/f0;

    const-string v1, "addressCountry"

    invoke-direct {v0, v1}, Lc0/f0;-><init>(Ljava/lang/String;)V

    sput-object v0, Lc0/f0;->q:Lc0/f0;

    new-instance v0, Lc0/f0;

    const-string v1, "addressRegion"

    invoke-direct {v0, v1}, Lc0/f0;-><init>(Ljava/lang/String;)V

    sput-object v0, Lc0/f0;->r:Lc0/f0;

    new-instance v0, Lc0/f0;

    const-string v1, "addressLocality"

    invoke-direct {v0, v1}, Lc0/f0;-><init>(Ljava/lang/String;)V

    sput-object v0, Lc0/f0;->s:Lc0/f0;

    new-instance v0, Lc0/f0;

    const-string v1, "streetAddress"

    invoke-direct {v0, v1}, Lc0/f0;-><init>(Ljava/lang/String;)V

    sput-object v0, Lc0/f0;->t:Lc0/f0;

    new-instance v0, Lc0/f0;

    const-string v1, "extendedAddress"

    invoke-direct {v0, v1}, Lc0/f0;-><init>(Ljava/lang/String;)V

    sput-object v0, Lc0/f0;->u:Lc0/f0;

    new-instance v0, Lc0/f0;

    const-string v1, "extendedPostalCode"

    invoke-direct {v0, v1}, Lc0/f0;-><init>(Ljava/lang/String;)V

    sput-object v0, Lc0/f0;->v:Lc0/f0;

    new-instance v0, Lc0/f0;

    const-string v1, "personName"

    invoke-direct {v0, v1}, Lc0/f0;-><init>(Ljava/lang/String;)V

    sput-object v0, Lc0/f0;->w:Lc0/f0;

    new-instance v0, Lc0/f0;

    const-string v1, "personGivenName"

    invoke-direct {v0, v1}, Lc0/f0;-><init>(Ljava/lang/String;)V

    sput-object v0, Lc0/f0;->x:Lc0/f0;

    new-instance v0, Lc0/f0;

    const-string v1, "personFamilyName"

    invoke-direct {v0, v1}, Lc0/f0;-><init>(Ljava/lang/String;)V

    sput-object v0, Lc0/f0;->y:Lc0/f0;

    new-instance v0, Lc0/f0;

    const-string v1, "personMiddleName"

    invoke-direct {v0, v1}, Lc0/f0;-><init>(Ljava/lang/String;)V

    sput-object v0, Lc0/f0;->z:Lc0/f0;

    new-instance v0, Lc0/f0;

    const-string v1, "personMiddleInitial"

    invoke-direct {v0, v1}, Lc0/f0;-><init>(Ljava/lang/String;)V

    sput-object v0, Lc0/f0;->A:Lc0/f0;

    new-instance v0, Lc0/f0;

    const-string v1, "personNamePrefix"

    invoke-direct {v0, v1}, Lc0/f0;-><init>(Ljava/lang/String;)V

    sput-object v0, Lc0/f0;->B:Lc0/f0;

    new-instance v0, Lc0/f0;

    const-string v1, "personNameSuffix"

    invoke-direct {v0, v1}, Lc0/f0;-><init>(Ljava/lang/String;)V

    sput-object v0, Lc0/f0;->C:Lc0/f0;

    new-instance v0, Lc0/f0;

    const-string v1, "phoneNumber"

    invoke-direct {v0, v1}, Lc0/f0;-><init>(Ljava/lang/String;)V

    sput-object v0, Lc0/f0;->D:Lc0/f0;

    new-instance v0, Lc0/f0;

    const-string v1, "phoneNumberDevice"

    invoke-direct {v0, v1}, Lc0/f0;-><init>(Ljava/lang/String;)V

    sput-object v0, Lc0/f0;->E:Lc0/f0;

    new-instance v0, Lc0/f0;

    const-string v1, "phoneCountryCode"

    invoke-direct {v0, v1}, Lc0/f0;-><init>(Ljava/lang/String;)V

    sput-object v0, Lc0/f0;->F:Lc0/f0;

    new-instance v0, Lc0/f0;

    const-string v1, "phoneNational"

    invoke-direct {v0, v1}, Lc0/f0;-><init>(Ljava/lang/String;)V

    sput-object v0, Lc0/f0;->G:Lc0/f0;

    new-instance v0, Lc0/f0;

    const-string v1, "gender"

    invoke-direct {v0, v1}, Lc0/f0;-><init>(Ljava/lang/String;)V

    sput-object v0, Lc0/f0;->H:Lc0/f0;

    new-instance v0, Lc0/f0;

    const-string v1, "birthDateFull"

    invoke-direct {v0, v1}, Lc0/f0;-><init>(Ljava/lang/String;)V

    sput-object v0, Lc0/f0;->I:Lc0/f0;

    new-instance v0, Lc0/f0;

    const-string v1, "birthDateDay"

    invoke-direct {v0, v1}, Lc0/f0;-><init>(Ljava/lang/String;)V

    sput-object v0, Lc0/f0;->J:Lc0/f0;

    new-instance v0, Lc0/f0;

    const-string v1, "birthDateMonth"

    invoke-direct {v0, v1}, Lc0/f0;-><init>(Ljava/lang/String;)V

    sput-object v0, Lc0/f0;->K:Lc0/f0;

    new-instance v0, Lc0/f0;

    const-string v1, "birthDateYear"

    invoke-direct {v0, v1}, Lc0/f0;-><init>(Ljava/lang/String;)V

    sput-object v0, Lc0/f0;->L:Lc0/f0;

    new-instance v0, Lc0/f0;

    const-string v1, "smsOTPCode"

    invoke-direct {v0, v1}, Lc0/f0;-><init>(Ljava/lang/String;)V

    sput-object v0, Lc0/f0;->M:Lc0/f0;

    return-void
.end method

.method public constructor <init>(Ljava/lang/String;)V
    .locals 0

    invoke-static {p1}, Lkotlin/collections/SetsKt;->d(Ljava/lang/Object;)Ljava/util/Set;

    move-result-object p1

    invoke-direct {p0, p1}, Lc0/f0;-><init>(Ljava/util/Set;)V

    return-void
.end method

.method public constructor <init>(Ljava/util/Set;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Set<",
            "Ljava/lang/String;",
            ">;)V"
        }
    .end annotation

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lc0/f0;->a:Ljava/util/Set;

    return-void
.end method
