.class public interface abstract Lc2/a;
.super Ljava/lang/Object;


# virtual methods
.method public abstract a(Z)Z
.end method

.method public abstract b(Landroidx/media3/common/g0;)Landroidx/media3/common/g0;
.end method

.method public abstract getAudioProcessors()[Landroidx/media3/common/audio/AudioProcessor;
.end method

.method public abstract getMediaDuration(J)J
.end method

.method public abstract getSkippedOutputFrameCount()J
.end method
