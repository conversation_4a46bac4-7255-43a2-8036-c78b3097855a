.class public final Lc3/a;
.super Ljava/lang/Object;

# interfaces
.implements Lz2/s;


# instance fields
.field public final a:Lz2/o0;


# direct methods
.method public constructor <init>()V
    .locals 4

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    new-instance v0, Lz2/o0;

    const/4 v1, 0x2

    const-string v2, "image/bmp"

    const/16 v3, 0x424d

    invoke-direct {v0, v3, v1, v2}, Lz2/o0;-><init>(IILjava/lang/String;)V

    iput-object v0, p0, Lc3/a;->a:Lz2/o0;

    return-void
.end method


# virtual methods
.method public synthetic b()Lz2/s;
    .locals 1

    invoke-static {p0}, Lz2/r;->a(Lz2/s;)Lz2/s;

    move-result-object v0

    return-object v0
.end method

.method public c(Lz2/u;)V
    .locals 1

    iget-object v0, p0, Lc3/a;->a:Lz2/o0;

    invoke-virtual {v0, p1}, Lz2/o0;->c(Lz2/u;)V

    return-void
.end method

.method public d(Lz2/t;Lz2/l0;)I
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget-object v0, p0, Lc3/a;->a:Lz2/o0;

    invoke-virtual {v0, p1, p2}, Lz2/o0;->d(Lz2/t;Lz2/l0;)I

    move-result p1

    return p1
.end method

.method public e(Lz2/t;)Z
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget-object v0, p0, Lc3/a;->a:Lz2/o0;

    invoke-virtual {v0, p1}, Lz2/o0;->e(Lz2/t;)Z

    move-result p1

    return p1
.end method

.method public release()V
    .locals 0

    return-void
.end method

.method public seek(JJ)V
    .locals 1

    iget-object v0, p0, Lc3/a;->a:Lz2/o0;

    invoke-virtual {v0, p1, p2, p3, p4}, Lz2/o0;->seek(JJ)V

    return-void
.end method
