.class public final Lc4/e;
.super Ljava/lang/Object;

# interfaces
.implements Lz2/s;


# static fields
.field public static final d:Lz2/y;


# instance fields
.field public final a:Lc4/f;

.field public final b:Le2/c0;

.field public c:Z


# direct methods
.method static constructor <clinit>()V
    .locals 1

    new-instance v0, Lc4/d;

    invoke-direct {v0}, Lc4/d;-><init>()V

    sput-object v0, Lc4/e;->d:Lz2/y;

    return-void
.end method

.method public constructor <init>()V
    .locals 2

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    new-instance v0, Lc4/f;

    invoke-direct {v0}, Lc4/f;-><init>()V

    iput-object v0, p0, Lc4/e;->a:Lc4/f;

    new-instance v0, Le2/c0;

    const/16 v1, 0x4000

    invoke-direct {v0, v1}, Le2/c0;-><init>(I)V

    iput-object v0, p0, Lc4/e;->b:Le2/c0;

    return-void
.end method

.method public static synthetic a()[Lz2/s;
    .locals 1

    invoke-static {}, Lc4/e;->f()[Lz2/s;

    move-result-object v0

    return-object v0
.end method

.method private static synthetic f()[Lz2/s;
    .locals 3

    const/4 v0, 0x1

    new-array v0, v0, [Lz2/s;

    new-instance v1, Lc4/e;

    invoke-direct {v1}, Lc4/e;-><init>()V

    const/4 v2, 0x0

    aput-object v1, v0, v2

    return-object v0
.end method


# virtual methods
.method public synthetic b()Lz2/s;
    .locals 1

    invoke-static {p0}, Lz2/r;->a(Lz2/s;)Lz2/s;

    move-result-object v0

    return-object v0
.end method

.method public c(Lz2/u;)V
    .locals 4

    iget-object v0, p0, Lc4/e;->a:Lc4/f;

    new-instance v1, Lc4/i0$d;

    const/4 v2, 0x0

    const/4 v3, 0x1

    invoke-direct {v1, v2, v3}, Lc4/i0$d;-><init>(II)V

    invoke-virtual {v0, p1, v1}, Lc4/f;->d(Lz2/u;Lc4/i0$d;)V

    invoke-interface {p1}, Lz2/u;->endTracks()V

    new-instance v0, Lz2/m0$b;

    const-wide v1, -0x7fffffffffffffffL    # -4.9E-324

    invoke-direct {v0, v1, v2}, Lz2/m0$b;-><init>(J)V

    invoke-interface {p1, v0}, Lz2/u;->g(Lz2/m0;)V

    return-void
.end method

.method public d(Lz2/t;Lz2/l0;)I
    .locals 4
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget-object p2, p0, Lc4/e;->b:Le2/c0;

    invoke-virtual {p2}, Le2/c0;->e()[B

    move-result-object p2

    const/16 v0, 0x4000

    const/4 v1, 0x0

    invoke-interface {p1, p2, v1, v0}, Lz2/t;->read([BII)I

    move-result p1

    const/4 p2, -0x1

    if-ne p1, p2, :cond_0

    return p2

    :cond_0
    iget-object p2, p0, Lc4/e;->b:Le2/c0;

    invoke-virtual {p2, v1}, Le2/c0;->U(I)V

    iget-object p2, p0, Lc4/e;->b:Le2/c0;

    invoke-virtual {p2, p1}, Le2/c0;->T(I)V

    iget-boolean p1, p0, Lc4/e;->c:Z

    if-nez p1, :cond_1

    iget-object p1, p0, Lc4/e;->a:Lc4/f;

    const-wide/16 v2, 0x0

    const/4 p2, 0x4

    invoke-virtual {p1, v2, v3, p2}, Lc4/f;->c(JI)V

    const/4 p1, 0x1

    iput-boolean p1, p0, Lc4/e;->c:Z

    :cond_1
    iget-object p1, p0, Lc4/e;->a:Lc4/f;

    iget-object p2, p0, Lc4/e;->b:Le2/c0;

    invoke-virtual {p1, p2}, Lc4/f;->a(Le2/c0;)V

    return v1
.end method

.method public e(Lz2/t;)Z
    .locals 8
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    new-instance v0, Le2/c0;

    const/16 v1, 0xa

    invoke-direct {v0, v1}, Le2/c0;-><init>(I)V

    const/4 v2, 0x0

    const/4 v3, 0x0

    :goto_0
    invoke-virtual {v0}, Le2/c0;->e()[B

    move-result-object v4

    invoke-interface {p1, v4, v2, v1}, Lz2/t;->peekFully([BII)V

    invoke-virtual {v0, v2}, Le2/c0;->U(I)V

    invoke-virtual {v0}, Le2/c0;->K()I

    move-result v4

    const v5, 0x494433

    if-eq v4, v5, :cond_4

    invoke-interface {p1}, Lz2/t;->resetPeekPosition()V

    invoke-interface {p1, v3}, Lz2/t;->advancePeekPosition(I)V

    move v4, v3

    :goto_1
    const/4 v1, 0x0

    :goto_2
    invoke-virtual {v0}, Le2/c0;->e()[B

    move-result-object v5

    const/4 v6, 0x7

    invoke-interface {p1, v5, v2, v6}, Lz2/t;->peekFully([BII)V

    invoke-virtual {v0, v2}, Le2/c0;->U(I)V

    invoke-virtual {v0}, Le2/c0;->N()I

    move-result v5

    const v6, 0xac40

    if-eq v5, v6, :cond_1

    const v6, 0xac41

    if-eq v5, v6, :cond_1

    invoke-interface {p1}, Lz2/t;->resetPeekPosition()V

    add-int/lit8 v4, v4, 0x1

    sub-int v1, v4, v3

    const/16 v5, 0x2000

    if-lt v1, v5, :cond_0

    return v2

    :cond_0
    invoke-interface {p1, v4}, Lz2/t;->advancePeekPosition(I)V

    goto :goto_1

    :cond_1
    const/4 v6, 0x1

    add-int/2addr v1, v6

    const/4 v7, 0x4

    if-lt v1, v7, :cond_2

    return v6

    :cond_2
    invoke-virtual {v0}, Le2/c0;->e()[B

    move-result-object v6

    invoke-static {v6, v5}, Lz2/c;->e([BI)I

    move-result v5

    const/4 v6, -0x1

    if-ne v5, v6, :cond_3

    return v2

    :cond_3
    add-int/lit8 v5, v5, -0x7

    invoke-interface {p1, v5}, Lz2/t;->advancePeekPosition(I)V

    goto :goto_2

    :cond_4
    const/4 v4, 0x3

    invoke-virtual {v0, v4}, Le2/c0;->V(I)V

    invoke-virtual {v0}, Le2/c0;->G()I

    move-result v4

    add-int/lit8 v5, v4, 0xa

    add-int/2addr v3, v5

    invoke-interface {p1, v4}, Lz2/t;->advancePeekPosition(I)V

    goto :goto_0
.end method

.method public release()V
    .locals 0

    return-void
.end method

.method public seek(JJ)V
    .locals 0

    const/4 p1, 0x0

    iput-boolean p1, p0, Lc4/e;->c:Z

    iget-object p1, p0, Lc4/e;->a:Lc4/f;

    invoke-virtual {p1}, Lc4/f;->seek()V

    return-void
.end method
