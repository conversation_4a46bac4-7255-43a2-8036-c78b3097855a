.class public final Lc4/e0;
.super Lz2/e;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lc4/e0$a;
    }
.end annotation


# direct methods
.method public constructor <init>(Le2/i0;JJII)V
    .locals 16

    new-instance v1, Lz2/e$b;

    invoke-direct {v1}, Lz2/e$b;-><init>()V

    new-instance v2, Lc4/e0$a;

    move-object/from16 v0, p1

    move/from16 v3, p6

    move/from16 v4, p7

    invoke-direct {v2, v3, v0, v4}, Lc4/e0$a;-><init>(ILe2/i0;I)V

    const-wide/16 v5, 0x0

    const-wide/16 v3, 0x1

    add-long v7, p2, v3

    const-wide/16 v9, 0x0

    const-wide/16 v13, 0xbc

    const/16 v15, 0x3ac

    move-object/from16 v0, p0

    move-wide/from16 v3, p2

    move-wide/from16 v11, p4

    invoke-direct/range {v0 .. v15}, Lz2/e;-><init>(Lz2/e$d;Lz2/e$f;JJJJJJI)V

    return-void
.end method
