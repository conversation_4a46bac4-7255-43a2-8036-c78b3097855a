.class public final Lc4/h0;
.super Ljava/lang/Object;

# interfaces
.implements Lz2/s;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lc4/h0$a;,
        Lc4/h0$b;
    }
.end annotation


# static fields
.field public static final v:Lz2/y;
    .annotation runtime Ljava/lang/Deprecated;
    .end annotation
.end field


# instance fields
.field public final a:I

.field public final b:I

.field public final c:I

.field public final d:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Le2/i0;",
            ">;"
        }
    .end annotation
.end field

.field public final e:Le2/c0;

.field public final f:Landroid/util/SparseIntArray;

.field public final g:Lc4/i0$c;

.field public final h:Lt3/s$a;

.field public final i:Landroid/util/SparseArray;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Landroid/util/SparseArray<",
            "Lc4/i0;",
            ">;"
        }
    .end annotation
.end field

.field public final j:Landroid/util/SparseBooleanArray;

.field public final k:Landroid/util/SparseBooleanArray;

.field public final l:Lc4/f0;

.field public m:Lc4/e0;

.field public n:Lz2/u;

.field public o:I

.field public p:Z

.field public q:Z

.field public r:Z

.field public s:Lc4/i0;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public t:I

.field public u:I


# direct methods
.method static constructor <clinit>()V
    .locals 1

    new-instance v0, Lc4/g0;

    invoke-direct {v0}, Lc4/g0;-><init>()V

    sput-object v0, Lc4/h0;->v:Lz2/y;

    return-void
.end method

.method public constructor <init>(IILt3/s$a;Le2/i0;Lc4/i0$c;I)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    invoke-static {p5}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p5

    check-cast p5, Lc4/i0$c;

    iput-object p5, p0, Lc4/h0;->g:Lc4/i0$c;

    iput p6, p0, Lc4/h0;->c:I

    iput p1, p0, Lc4/h0;->a:I

    iput p2, p0, Lc4/h0;->b:I

    iput-object p3, p0, Lc4/h0;->h:Lt3/s$a;

    const/4 p2, 0x1

    if-eq p1, p2, :cond_1

    const/4 p2, 0x2

    if-ne p1, p2, :cond_0

    goto :goto_0

    :cond_0
    new-instance p1, Ljava/util/ArrayList;

    invoke-direct {p1}, Ljava/util/ArrayList;-><init>()V

    iput-object p1, p0, Lc4/h0;->d:Ljava/util/List;

    invoke-interface {p1, p4}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_1

    :cond_1
    :goto_0
    invoke-static {p4}, Ljava/util/Collections;->singletonList(Ljava/lang/Object;)Ljava/util/List;

    move-result-object p1

    iput-object p1, p0, Lc4/h0;->d:Ljava/util/List;

    :goto_1
    new-instance p1, Le2/c0;

    const/16 p2, 0x24b8

    new-array p2, p2, [B

    const/4 p3, 0x0

    invoke-direct {p1, p2, p3}, Le2/c0;-><init>([BI)V

    iput-object p1, p0, Lc4/h0;->e:Le2/c0;

    new-instance p1, Landroid/util/SparseBooleanArray;

    invoke-direct {p1}, Landroid/util/SparseBooleanArray;-><init>()V

    iput-object p1, p0, Lc4/h0;->j:Landroid/util/SparseBooleanArray;

    new-instance p1, Landroid/util/SparseBooleanArray;

    invoke-direct {p1}, Landroid/util/SparseBooleanArray;-><init>()V

    iput-object p1, p0, Lc4/h0;->k:Landroid/util/SparseBooleanArray;

    new-instance p1, Landroid/util/SparseArray;

    invoke-direct {p1}, Landroid/util/SparseArray;-><init>()V

    iput-object p1, p0, Lc4/h0;->i:Landroid/util/SparseArray;

    new-instance p1, Landroid/util/SparseIntArray;

    invoke-direct {p1}, Landroid/util/SparseIntArray;-><init>()V

    iput-object p1, p0, Lc4/h0;->f:Landroid/util/SparseIntArray;

    new-instance p1, Lc4/f0;

    invoke-direct {p1, p6}, Lc4/f0;-><init>(I)V

    iput-object p1, p0, Lc4/h0;->l:Lc4/f0;

    sget-object p1, Lz2/u;->G0:Lz2/u;

    iput-object p1, p0, Lc4/h0;->n:Lz2/u;

    const/4 p1, -0x1

    iput p1, p0, Lc4/h0;->u:I

    invoke-virtual {p0}, Lc4/h0;->y()V

    return-void
.end method

.method public constructor <init>(ILt3/s$a;)V
    .locals 7

    const/4 v1, 0x1

    new-instance v4, Le2/i0;

    const-wide/16 v2, 0x0

    invoke-direct {v4, v2, v3}, Le2/i0;-><init>(J)V

    new-instance v5, Lc4/j;

    const/4 v0, 0x0

    invoke-direct {v5, v0}, Lc4/j;-><init>(I)V

    const v6, 0x1b8a0

    move-object v0, p0

    move v2, p1

    move-object v3, p2

    invoke-direct/range {v0 .. v6}, Lc4/h0;-><init>(IILt3/s$a;Le2/i0;Lc4/i0$c;I)V

    return-void
.end method

.method public static synthetic a()[Lz2/s;
    .locals 1

    invoke-static {}, Lc4/h0;->w()[Lz2/s;

    move-result-object v0

    return-object v0
.end method

.method public static synthetic f(Lc4/h0;)Landroid/util/SparseArray;
    .locals 0

    iget-object p0, p0, Lc4/h0;->i:Landroid/util/SparseArray;

    return-object p0
.end method

.method public static synthetic g(Lc4/h0;)I
    .locals 0

    iget p0, p0, Lc4/h0;->o:I

    return p0
.end method

.method public static synthetic h(Lc4/h0;)Z
    .locals 0

    iget-boolean p0, p0, Lc4/h0;->p:Z

    return p0
.end method

.method public static synthetic i(Lc4/h0;Z)Z
    .locals 0

    iput-boolean p1, p0, Lc4/h0;->p:Z

    return p1
.end method

.method public static synthetic j(Lc4/h0;I)I
    .locals 0

    iput p1, p0, Lc4/h0;->o:I

    return p1
.end method

.method public static synthetic k(Lc4/h0;)I
    .locals 2

    iget v0, p0, Lc4/h0;->o:I

    add-int/lit8 v1, v0, 0x1

    iput v1, p0, Lc4/h0;->o:I

    return v0
.end method

.method public static synthetic l(Lc4/h0;)I
    .locals 0

    iget p0, p0, Lc4/h0;->a:I

    return p0
.end method

.method public static synthetic m(Lc4/h0;)Ljava/util/List;
    .locals 0

    iget-object p0, p0, Lc4/h0;->d:Ljava/util/List;

    return-object p0
.end method

.method public static synthetic n(Lc4/h0;I)I
    .locals 0

    iput p1, p0, Lc4/h0;->u:I

    return p1
.end method

.method public static synthetic o(Lc4/h0;)Lc4/i0;
    .locals 0

    iget-object p0, p0, Lc4/h0;->s:Lc4/i0;

    return-object p0
.end method

.method public static synthetic p(Lc4/h0;Lc4/i0;)Lc4/i0;
    .locals 0

    iput-object p1, p0, Lc4/h0;->s:Lc4/i0;

    return-object p1
.end method

.method public static synthetic q(Lc4/h0;)Lc4/i0$c;
    .locals 0

    iget-object p0, p0, Lc4/h0;->g:Lc4/i0$c;

    return-object p0
.end method

.method public static synthetic r(Lc4/h0;)Lz2/u;
    .locals 0

    iget-object p0, p0, Lc4/h0;->n:Lz2/u;

    return-object p0
.end method

.method public static synthetic s(Lc4/h0;)Landroid/util/SparseBooleanArray;
    .locals 0

    iget-object p0, p0, Lc4/h0;->j:Landroid/util/SparseBooleanArray;

    return-object p0
.end method

.method public static synthetic t(Lc4/h0;)Landroid/util/SparseBooleanArray;
    .locals 0

    iget-object p0, p0, Lc4/h0;->k:Landroid/util/SparseBooleanArray;

    return-object p0
.end method

.method private static synthetic w()[Lz2/s;
    .locals 4

    const/4 v0, 0x1

    new-array v1, v0, [Lz2/s;

    new-instance v2, Lc4/h0;

    sget-object v3, Lt3/s$a;->a:Lt3/s$a;

    invoke-direct {v2, v0, v3}, Lc4/h0;-><init>(ILt3/s$a;)V

    const/4 v0, 0x0

    aput-object v2, v1, v0

    return-object v1
.end method

.method private x(J)V
    .locals 13

    iget-boolean v0, p0, Lc4/h0;->q:Z

    if-nez v0, :cond_1

    const/4 v0, 0x1

    iput-boolean v0, p0, Lc4/h0;->q:Z

    iget-object v0, p0, Lc4/h0;->l:Lc4/f0;

    invoke-virtual {v0}, Lc4/f0;->b()J

    move-result-wide v0

    const-wide v2, -0x7fffffffffffffffL    # -4.9E-324

    cmp-long v4, v0, v2

    if-eqz v4, :cond_0

    new-instance v0, Lc4/e0;

    iget-object v1, p0, Lc4/h0;->l:Lc4/f0;

    invoke-virtual {v1}, Lc4/f0;->c()Le2/i0;

    move-result-object v6

    iget-object v1, p0, Lc4/h0;->l:Lc4/f0;

    invoke-virtual {v1}, Lc4/f0;->b()J

    move-result-wide v7

    iget v11, p0, Lc4/h0;->u:I

    iget v12, p0, Lc4/h0;->c:I

    move-object v5, v0

    move-wide v9, p1

    invoke-direct/range {v5 .. v12}, Lc4/e0;-><init>(Le2/i0;JJII)V

    iput-object v0, p0, Lc4/h0;->m:Lc4/e0;

    iget-object p1, p0, Lc4/h0;->n:Lz2/u;

    invoke-virtual {v0}, Lz2/e;->b()Lz2/m0;

    move-result-object p2

    invoke-interface {p1, p2}, Lz2/u;->g(Lz2/m0;)V

    goto :goto_0

    :cond_0
    iget-object p1, p0, Lc4/h0;->n:Lz2/u;

    new-instance p2, Lz2/m0$b;

    iget-object v0, p0, Lc4/h0;->l:Lc4/f0;

    invoke-virtual {v0}, Lc4/f0;->b()J

    move-result-wide v0

    invoke-direct {p2, v0, v1}, Lz2/m0$b;-><init>(J)V

    invoke-interface {p1, p2}, Lz2/u;->g(Lz2/m0;)V

    :cond_1
    :goto_0
    return-void
.end method


# virtual methods
.method public synthetic b()Lz2/s;
    .locals 1

    invoke-static {p0}, Lz2/r;->a(Lz2/s;)Lz2/s;

    move-result-object v0

    return-object v0
.end method

.method public c(Lz2/u;)V
    .locals 2

    iget v0, p0, Lc4/h0;->b:I

    and-int/lit8 v0, v0, 0x1

    if-nez v0, :cond_0

    new-instance v0, Lt3/u;

    iget-object v1, p0, Lc4/h0;->h:Lt3/s$a;

    invoke-direct {v0, p1, v1}, Lt3/u;-><init>(Lz2/u;Lt3/s$a;)V

    move-object p1, v0

    :cond_0
    iput-object p1, p0, Lc4/h0;->n:Lz2/u;

    return-void
.end method

.method public d(Lz2/t;Lz2/l0;)I
    .locals 16
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    move-object/from16 v0, p0

    move-object/from16 v1, p1

    move-object/from16 v2, p2

    invoke-interface/range {p1 .. p1}, Lz2/t;->getLength()J

    move-result-wide v3

    iget-boolean v5, v0, Lc4/h0;->p:Z

    const-wide/16 v6, -0x1

    const/4 v8, 0x2

    const/4 v9, 0x1

    const/4 v10, 0x0

    if-eqz v5, :cond_2

    cmp-long v5, v3, v6

    if-eqz v5, :cond_0

    iget v5, v0, Lc4/h0;->a:I

    if-eq v5, v8, :cond_0

    iget-object v5, v0, Lc4/h0;->l:Lc4/f0;

    invoke-virtual {v5}, Lc4/f0;->d()Z

    move-result v5

    if-nez v5, :cond_0

    iget-object v3, v0, Lc4/h0;->l:Lc4/f0;

    iget v4, v0, Lc4/h0;->u:I

    invoke-virtual {v3, v1, v2, v4}, Lc4/f0;->e(Lz2/t;Lz2/l0;I)I

    move-result v1

    return v1

    :cond_0
    invoke-direct {v0, v3, v4}, Lc4/h0;->x(J)V

    iget-boolean v5, v0, Lc4/h0;->r:Z

    if-eqz v5, :cond_1

    iput-boolean v10, v0, Lc4/h0;->r:Z

    const-wide/16 v11, 0x0

    invoke-virtual {v0, v11, v12, v11, v12}, Lc4/h0;->seek(JJ)V

    invoke-interface/range {p1 .. p1}, Lz2/t;->getPosition()J

    move-result-wide v13

    cmp-long v5, v13, v11

    if-eqz v5, :cond_1

    iput-wide v11, v2, Lz2/l0;->a:J

    return v9

    :cond_1
    iget-object v5, v0, Lc4/h0;->m:Lc4/e0;

    if-eqz v5, :cond_2

    invoke-virtual {v5}, Lz2/e;->d()Z

    move-result v5

    if-eqz v5, :cond_2

    iget-object v3, v0, Lc4/h0;->m:Lc4/e0;

    invoke-virtual {v3, v1, v2}, Lz2/e;->c(Lz2/t;Lz2/l0;)I

    move-result v1

    return v1

    :cond_2
    invoke-virtual/range {p0 .. p1}, Lc4/h0;->u(Lz2/t;)Z

    move-result v1

    if-nez v1, :cond_3

    const/4 v1, -0x1

    return v1

    :cond_3
    invoke-virtual/range {p0 .. p0}, Lc4/h0;->v()I

    move-result v1

    iget-object v2, v0, Lc4/h0;->e:Le2/c0;

    invoke-virtual {v2}, Le2/c0;->g()I

    move-result v2

    if-le v1, v2, :cond_4

    return v10

    :cond_4
    iget-object v5, v0, Lc4/h0;->e:Le2/c0;

    invoke-virtual {v5}, Le2/c0;->q()I

    move-result v5

    const/high16 v11, 0x800000

    and-int/2addr v11, v5

    if-eqz v11, :cond_5

    iget-object v2, v0, Lc4/h0;->e:Le2/c0;

    invoke-virtual {v2, v1}, Le2/c0;->U(I)V

    return v10

    :cond_5
    const/high16 v11, 0x400000

    and-int/2addr v11, v5

    if-eqz v11, :cond_6

    const/4 v11, 0x1

    goto :goto_0

    :cond_6
    const/4 v11, 0x0

    :goto_0
    const v12, 0x1fff00

    and-int/2addr v12, v5

    shr-int/lit8 v12, v12, 0x8

    and-int/lit8 v13, v5, 0x20

    if-eqz v13, :cond_7

    const/4 v13, 0x1

    goto :goto_1

    :cond_7
    const/4 v13, 0x0

    :goto_1
    and-int/lit8 v14, v5, 0x10

    if-eqz v14, :cond_8

    iget-object v14, v0, Lc4/h0;->i:Landroid/util/SparseArray;

    invoke-virtual {v14, v12}, Landroid/util/SparseArray;->get(I)Ljava/lang/Object;

    move-result-object v14

    check-cast v14, Lc4/i0;

    goto :goto_2

    :cond_8
    const/4 v14, 0x0

    :goto_2
    if-nez v14, :cond_9

    iget-object v2, v0, Lc4/h0;->e:Le2/c0;

    invoke-virtual {v2, v1}, Le2/c0;->U(I)V

    return v10

    :cond_9
    iget v15, v0, Lc4/h0;->a:I

    if-eq v15, v8, :cond_b

    and-int/lit8 v5, v5, 0xf

    iget-object v15, v0, Lc4/h0;->f:Landroid/util/SparseIntArray;

    add-int/lit8 v6, v5, -0x1

    invoke-virtual {v15, v12, v6}, Landroid/util/SparseIntArray;->get(II)I

    move-result v6

    iget-object v7, v0, Lc4/h0;->f:Landroid/util/SparseIntArray;

    invoke-virtual {v7, v12, v5}, Landroid/util/SparseIntArray;->put(II)V

    if-ne v6, v5, :cond_a

    iget-object v2, v0, Lc4/h0;->e:Le2/c0;

    invoke-virtual {v2, v1}, Le2/c0;->U(I)V

    return v10

    :cond_a
    add-int/2addr v6, v9

    and-int/lit8 v6, v6, 0xf

    if-eq v5, v6, :cond_b

    invoke-interface {v14}, Lc4/i0;->seek()V

    :cond_b
    if-eqz v13, :cond_d

    iget-object v5, v0, Lc4/h0;->e:Le2/c0;

    invoke-virtual {v5}, Le2/c0;->H()I

    move-result v5

    iget-object v6, v0, Lc4/h0;->e:Le2/c0;

    invoke-virtual {v6}, Le2/c0;->H()I

    move-result v6

    and-int/lit8 v6, v6, 0x40

    if-eqz v6, :cond_c

    const/4 v6, 0x2

    goto :goto_3

    :cond_c
    const/4 v6, 0x0

    :goto_3
    or-int/2addr v11, v6

    iget-object v6, v0, Lc4/h0;->e:Le2/c0;

    sub-int/2addr v5, v9

    invoke-virtual {v6, v5}, Le2/c0;->V(I)V

    :cond_d
    iget-boolean v5, v0, Lc4/h0;->p:Z

    invoke-virtual {v0, v12}, Lc4/h0;->z(I)Z

    move-result v6

    if-eqz v6, :cond_e

    iget-object v6, v0, Lc4/h0;->e:Le2/c0;

    invoke-virtual {v6, v1}, Le2/c0;->T(I)V

    iget-object v6, v0, Lc4/h0;->e:Le2/c0;

    invoke-interface {v14, v6, v11}, Lc4/i0;->a(Le2/c0;I)V

    iget-object v6, v0, Lc4/h0;->e:Le2/c0;

    invoke-virtual {v6, v2}, Le2/c0;->T(I)V

    :cond_e
    iget v2, v0, Lc4/h0;->a:I

    if-eq v2, v8, :cond_f

    if-nez v5, :cond_f

    iget-boolean v2, v0, Lc4/h0;->p:Z

    if-eqz v2, :cond_f

    const-wide/16 v5, -0x1

    cmp-long v2, v3, v5

    if-eqz v2, :cond_f

    iput-boolean v9, v0, Lc4/h0;->r:Z

    :cond_f
    iget-object v2, v0, Lc4/h0;->e:Le2/c0;

    invoke-virtual {v2, v1}, Le2/c0;->U(I)V

    return v10
.end method

.method public e(Lz2/t;)Z
    .locals 6
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget-object v0, p0, Lc4/h0;->e:Le2/c0;

    invoke-virtual {v0}, Le2/c0;->e()[B

    move-result-object v0

    const/16 v1, 0x3ac

    const/4 v2, 0x0

    invoke-interface {p1, v0, v2, v1}, Lz2/t;->peekFully([BII)V

    const/4 v1, 0x0

    :goto_0
    const/16 v3, 0xbc

    if-ge v1, v3, :cond_2

    const/4 v3, 0x0

    :goto_1
    const/4 v4, 0x5

    if-ge v3, v4, :cond_1

    mul-int/lit16 v4, v3, 0xbc

    add-int/2addr v4, v1

    aget-byte v4, v0, v4

    const/16 v5, 0x47

    if-eq v4, v5, :cond_0

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_0
    add-int/lit8 v3, v3, 0x1

    goto :goto_1

    :cond_1
    invoke-interface {p1, v1}, Lz2/t;->skipFully(I)V

    const/4 p1, 0x1

    return p1

    :cond_2
    return v2
.end method

.method public release()V
    .locals 0

    return-void
.end method

.method public seek(JJ)V
    .locals 10

    iget p1, p0, Lc4/h0;->a:I

    const/4 p2, 0x2

    const/4 v0, 0x1

    const/4 v1, 0x0

    if-eq p1, p2, :cond_0

    const/4 p1, 0x1

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    :goto_0
    invoke-static {p1}, Le2/a;->g(Z)V

    iget-object p1, p0, Lc4/h0;->d:Ljava/util/List;

    invoke-interface {p1}, Ljava/util/List;->size()I

    move-result p1

    const/4 p2, 0x0

    :goto_1
    const-wide/16 v2, 0x0

    if-ge p2, p1, :cond_4

    iget-object v4, p0, Lc4/h0;->d:Ljava/util/List;

    invoke-interface {v4, p2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Le2/i0;

    invoke-virtual {v4}, Le2/i0;->f()J

    move-result-wide v5

    const-wide v7, -0x7fffffffffffffffL    # -4.9E-324

    cmp-long v9, v5, v7

    if-nez v9, :cond_1

    const/4 v5, 0x1

    goto :goto_2

    :cond_1
    const/4 v5, 0x0

    :goto_2
    if-nez v5, :cond_2

    invoke-virtual {v4}, Le2/i0;->d()J

    move-result-wide v5

    cmp-long v9, v5, v7

    if-eqz v9, :cond_3

    cmp-long v7, v5, v2

    if-eqz v7, :cond_3

    cmp-long v2, v5, p3

    if-eqz v2, :cond_3

    goto :goto_3

    :cond_2
    if-eqz v5, :cond_3

    :goto_3
    invoke-virtual {v4, p3, p4}, Le2/i0;->i(J)V

    :cond_3
    add-int/lit8 p2, p2, 0x1

    goto :goto_1

    :cond_4
    cmp-long p1, p3, v2

    if-eqz p1, :cond_5

    iget-object p1, p0, Lc4/h0;->m:Lc4/e0;

    if-eqz p1, :cond_5

    invoke-virtual {p1, p3, p4}, Lz2/e;->h(J)V

    :cond_5
    iget-object p1, p0, Lc4/h0;->e:Le2/c0;

    invoke-virtual {p1, v1}, Le2/c0;->Q(I)V

    iget-object p1, p0, Lc4/h0;->f:Landroid/util/SparseIntArray;

    invoke-virtual {p1}, Landroid/util/SparseIntArray;->clear()V

    const/4 p1, 0x0

    :goto_4
    iget-object p2, p0, Lc4/h0;->i:Landroid/util/SparseArray;

    invoke-virtual {p2}, Landroid/util/SparseArray;->size()I

    move-result p2

    if-ge p1, p2, :cond_6

    iget-object p2, p0, Lc4/h0;->i:Landroid/util/SparseArray;

    invoke-virtual {p2, p1}, Landroid/util/SparseArray;->valueAt(I)Ljava/lang/Object;

    move-result-object p2

    check-cast p2, Lc4/i0;

    invoke-interface {p2}, Lc4/i0;->seek()V

    add-int/lit8 p1, p1, 0x1

    goto :goto_4

    :cond_6
    iput v1, p0, Lc4/h0;->t:I

    return-void
.end method

.method public final u(Lz2/t;)Z
    .locals 6
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget-object v0, p0, Lc4/h0;->e:Le2/c0;

    invoke-virtual {v0}, Le2/c0;->e()[B

    move-result-object v0

    iget-object v1, p0, Lc4/h0;->e:Le2/c0;

    invoke-virtual {v1}, Le2/c0;->f()I

    move-result v1

    rsub-int v1, v1, 0x24b8

    const/4 v2, 0x0

    const/16 v3, 0xbc

    if-ge v1, v3, :cond_1

    iget-object v1, p0, Lc4/h0;->e:Le2/c0;

    invoke-virtual {v1}, Le2/c0;->a()I

    move-result v1

    if-lez v1, :cond_0

    iget-object v4, p0, Lc4/h0;->e:Le2/c0;

    invoke-virtual {v4}, Le2/c0;->f()I

    move-result v4

    invoke-static {v0, v4, v0, v2, v1}, Ljava/lang/System;->arraycopy(Ljava/lang/Object;ILjava/lang/Object;II)V

    :cond_0
    iget-object v4, p0, Lc4/h0;->e:Le2/c0;

    invoke-virtual {v4, v0, v1}, Le2/c0;->S([BI)V

    :cond_1
    :goto_0
    iget-object v1, p0, Lc4/h0;->e:Le2/c0;

    invoke-virtual {v1}, Le2/c0;->a()I

    move-result v1

    if-ge v1, v3, :cond_3

    iget-object v1, p0, Lc4/h0;->e:Le2/c0;

    invoke-virtual {v1}, Le2/c0;->g()I

    move-result v1

    rsub-int v4, v1, 0x24b8

    invoke-interface {p1, v0, v1, v4}, Lz2/t;->read([BII)I

    move-result v4

    const/4 v5, -0x1

    if-ne v4, v5, :cond_2

    return v2

    :cond_2
    iget-object v5, p0, Lc4/h0;->e:Le2/c0;

    add-int/2addr v1, v4

    invoke-virtual {v5, v1}, Le2/c0;->T(I)V

    goto :goto_0

    :cond_3
    const/4 p1, 0x1

    return p1
.end method

.method public final v()I
    .locals 4
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/common/ParserException;
        }
    .end annotation

    iget-object v0, p0, Lc4/h0;->e:Le2/c0;

    invoke-virtual {v0}, Le2/c0;->f()I

    move-result v0

    iget-object v1, p0, Lc4/h0;->e:Le2/c0;

    invoke-virtual {v1}, Le2/c0;->g()I

    move-result v1

    iget-object v2, p0, Lc4/h0;->e:Le2/c0;

    invoke-virtual {v2}, Le2/c0;->e()[B

    move-result-object v2

    invoke-static {v2, v0, v1}, Lc4/j0;->a([BII)I

    move-result v2

    iget-object v3, p0, Lc4/h0;->e:Le2/c0;

    invoke-virtual {v3, v2}, Le2/c0;->U(I)V

    add-int/lit16 v3, v2, 0xbc

    if-le v3, v1, :cond_1

    iget v1, p0, Lc4/h0;->t:I

    sub-int/2addr v2, v0

    add-int/2addr v1, v2

    iput v1, p0, Lc4/h0;->t:I

    iget v0, p0, Lc4/h0;->a:I

    const/4 v2, 0x2

    if-ne v0, v2, :cond_2

    const/16 v0, 0x178

    if-gt v1, v0, :cond_0

    goto :goto_0

    :cond_0
    const-string v0, "Cannot find sync byte. Most likely not a Transport Stream."

    const/4 v1, 0x0

    invoke-static {v0, v1}, Landroidx/media3/common/ParserException;->createForMalformedContainer(Ljava/lang/String;Ljava/lang/Throwable;)Landroidx/media3/common/ParserException;

    move-result-object v0

    throw v0

    :cond_1
    const/4 v0, 0x0

    iput v0, p0, Lc4/h0;->t:I

    :cond_2
    :goto_0
    return v3
.end method

.method public final y()V
    .locals 7

    iget-object v0, p0, Lc4/h0;->j:Landroid/util/SparseBooleanArray;

    invoke-virtual {v0}, Landroid/util/SparseBooleanArray;->clear()V

    iget-object v0, p0, Lc4/h0;->i:Landroid/util/SparseArray;

    invoke-virtual {v0}, Landroid/util/SparseArray;->clear()V

    iget-object v0, p0, Lc4/h0;->g:Lc4/i0$c;

    invoke-interface {v0}, Lc4/i0$c;->createInitialPayloadReaders()Landroid/util/SparseArray;

    move-result-object v0

    invoke-virtual {v0}, Landroid/util/SparseArray;->size()I

    move-result v1

    const/4 v2, 0x0

    const/4 v3, 0x0

    :goto_0
    if-ge v3, v1, :cond_0

    iget-object v4, p0, Lc4/h0;->i:Landroid/util/SparseArray;

    invoke-virtual {v0, v3}, Landroid/util/SparseArray;->keyAt(I)I

    move-result v5

    invoke-virtual {v0, v3}, Landroid/util/SparseArray;->valueAt(I)Ljava/lang/Object;

    move-result-object v6

    check-cast v6, Lc4/i0;

    invoke-virtual {v4, v5, v6}, Landroid/util/SparseArray;->put(ILjava/lang/Object;)V

    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    :cond_0
    iget-object v0, p0, Lc4/h0;->i:Landroid/util/SparseArray;

    new-instance v1, Lc4/c0;

    new-instance v3, Lc4/h0$a;

    invoke-direct {v3, p0}, Lc4/h0$a;-><init>(Lc4/h0;)V

    invoke-direct {v1, v3}, Lc4/c0;-><init>(Lc4/b0;)V

    invoke-virtual {v0, v2, v1}, Landroid/util/SparseArray;->put(ILjava/lang/Object;)V

    const/4 v0, 0x0

    iput-object v0, p0, Lc4/h0;->s:Lc4/i0;

    return-void
.end method

.method public final z(I)Z
    .locals 2

    iget v0, p0, Lc4/h0;->a:I

    const/4 v1, 0x2

    if-eq v0, v1, :cond_0

    iget-boolean v0, p0, Lc4/h0;->p:Z

    if-nez v0, :cond_0

    iget-object v0, p0, Lc4/h0;->k:Landroid/util/SparseBooleanArray;

    const/4 v1, 0x0

    invoke-virtual {v0, p1, v1}, Landroid/util/SparseBooleanArray;->get(IZ)Z

    move-result p1

    if-nez p1, :cond_1

    :cond_0
    const/4 v1, 0x1

    :cond_1
    return v1
.end method
