.class public interface abstract Lc4/i0;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lc4/i0$d;,
        Lc4/i0$a;,
        Lc4/i0$b;,
        Lc4/i0$c;
    }
.end annotation


# virtual methods
.method public abstract a(Le2/c0;I)V
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/common/ParserException;
        }
    .end annotation
.end method

.method public abstract b(Le2/i0;Lz2/u;Lc4/i0$d;)V
.end method

.method public abstract seek()V
.end method
