.class public interface abstract Lc4/m;
.super Ljava/lang/Object;


# virtual methods
.method public abstract a(Le2/c0;)V
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/common/ParserException;
        }
    .end annotation
.end method

.method public abstract c(JI)V
.end method

.method public abstract d(Lz2/u;Lc4/i0$d;)V
.end method

.method public abstract packetFinished()V
.end method

.method public abstract seek()V
.end method
