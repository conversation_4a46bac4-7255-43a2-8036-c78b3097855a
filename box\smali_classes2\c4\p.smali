.class public final Lc4/p;
.super Ljava/lang/Object;

# interfaces
.implements Lc4/m;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lc4/p$b;
    }
.end annotation


# instance fields
.field public final a:Lc4/d0;

.field public final b:Z

.field public final c:Z

.field public final d:Lc4/u;

.field public final e:Lc4/u;

.field public final f:Lc4/u;

.field public g:J

.field public final h:[Z

.field public i:Ljava/lang/String;

.field public j:Lz2/r0;

.field public k:Lc4/p$b;

.field public l:Z

.field public m:J

.field public n:Z

.field public final o:Le2/c0;


# direct methods
.method public constructor <init>(Lc4/d0;ZZ)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lc4/p;->a:Lc4/d0;

    iput-boolean p2, p0, Lc4/p;->b:Z

    iput-boolean p3, p0, Lc4/p;->c:Z

    const/4 p1, 0x3

    new-array p1, p1, [Z

    iput-object p1, p0, Lc4/p;->h:[Z

    new-instance p1, Lc4/u;

    const/4 p2, 0x7

    const/16 p3, 0x80

    invoke-direct {p1, p2, p3}, Lc4/u;-><init>(II)V

    iput-object p1, p0, Lc4/p;->d:Lc4/u;

    new-instance p1, Lc4/u;

    const/16 p2, 0x8

    invoke-direct {p1, p2, p3}, Lc4/u;-><init>(II)V

    iput-object p1, p0, Lc4/p;->e:Lc4/u;

    new-instance p1, Lc4/u;

    const/4 p2, 0x6

    invoke-direct {p1, p2, p3}, Lc4/u;-><init>(II)V

    iput-object p1, p0, Lc4/p;->f:Lc4/u;

    const-wide p1, -0x7fffffffffffffffL    # -4.9E-324

    iput-wide p1, p0, Lc4/p;->m:J

    new-instance p1, Le2/c0;

    invoke-direct {p1}, Le2/c0;-><init>()V

    iput-object p1, p0, Lc4/p;->o:Le2/c0;

    return-void
.end method

.method private b()V
    .locals 1

    iget-object v0, p0, Lc4/p;->j:Lz2/r0;

    invoke-static {v0}, Le2/a;->i(Ljava/lang/Object;)Ljava/lang/Object;

    iget-object v0, p0, Lc4/p;->k:Lc4/p$b;

    invoke-static {v0}, Le2/u0;->i(Ljava/lang/Object;)Ljava/lang/Object;

    return-void
.end method


# virtual methods
.method public a(Le2/c0;)V
    .locals 14

    invoke-direct {p0}, Lc4/p;->b()V

    invoke-virtual {p1}, Le2/c0;->f()I

    move-result v0

    invoke-virtual {p1}, Le2/c0;->g()I

    move-result v1

    invoke-virtual {p1}, Le2/c0;->e()[B

    move-result-object v2

    iget-wide v3, p0, Lc4/p;->g:J

    invoke-virtual {p1}, Le2/c0;->a()I

    move-result v5

    int-to-long v5, v5

    add-long/2addr v3, v5

    iput-wide v3, p0, Lc4/p;->g:J

    iget-object v3, p0, Lc4/p;->j:Lz2/r0;

    invoke-virtual {p1}, Le2/c0;->a()I

    move-result v4

    invoke-interface {v3, p1, v4}, Lz2/r0;->f(Le2/c0;I)V

    :goto_0
    iget-object p1, p0, Lc4/p;->h:[Z

    invoke-static {v2, v0, v1, p1}, Lf2/a;->c([BII[Z)I

    move-result p1

    if-ne p1, v1, :cond_0

    invoke-virtual {p0, v2, v0, v1}, Lc4/p;->f([BII)V

    return-void

    :cond_0
    invoke-static {v2, p1}, Lf2/a;->f([BI)I

    move-result v6

    sub-int v3, p1, v0

    if-lez v3, :cond_1

    invoke-virtual {p0, v2, v0, p1}, Lc4/p;->f([BII)V

    :cond_1
    sub-int v10, v1, p1

    iget-wide v4, p0, Lc4/p;->g:J

    int-to-long v7, v10

    sub-long/2addr v4, v7

    if-gez v3, :cond_2

    neg-int v0, v3

    move v11, v0

    goto :goto_1

    :cond_2
    const/4 v0, 0x0

    const/4 v11, 0x0

    :goto_1
    iget-wide v12, p0, Lc4/p;->m:J

    move-object v7, p0

    move-wide v8, v4

    invoke-virtual/range {v7 .. v13}, Lc4/p;->e(JIIJ)V

    iget-wide v7, p0, Lc4/p;->m:J

    move-object v3, p0

    invoke-virtual/range {v3 .. v8}, Lc4/p;->g(JIJ)V

    add-int/lit8 v0, p1, 0x3

    goto :goto_0
.end method

.method public c(JI)V
    .locals 0

    iput-wide p1, p0, Lc4/p;->m:J

    iget-boolean p1, p0, Lc4/p;->n:Z

    and-int/lit8 p2, p3, 0x2

    if-eqz p2, :cond_0

    const/4 p2, 0x1

    goto :goto_0

    :cond_0
    const/4 p2, 0x0

    :goto_0
    or-int/2addr p1, p2

    iput-boolean p1, p0, Lc4/p;->n:Z

    return-void
.end method

.method public d(Lz2/u;Lc4/i0$d;)V
    .locals 4

    invoke-virtual {p2}, Lc4/i0$d;->a()V

    invoke-virtual {p2}, Lc4/i0$d;->b()Ljava/lang/String;

    move-result-object v0

    iput-object v0, p0, Lc4/p;->i:Ljava/lang/String;

    invoke-virtual {p2}, Lc4/i0$d;->c()I

    move-result v0

    const/4 v1, 0x2

    invoke-interface {p1, v0, v1}, Lz2/u;->track(II)Lz2/r0;

    move-result-object v0

    iput-object v0, p0, Lc4/p;->j:Lz2/r0;

    new-instance v1, Lc4/p$b;

    iget-boolean v2, p0, Lc4/p;->b:Z

    iget-boolean v3, p0, Lc4/p;->c:Z

    invoke-direct {v1, v0, v2, v3}, Lc4/p$b;-><init>(Lz2/r0;ZZ)V

    iput-object v1, p0, Lc4/p;->k:Lc4/p$b;

    iget-object v0, p0, Lc4/p;->a:Lc4/d0;

    invoke-virtual {v0, p1, p2}, Lc4/d0;->b(Lz2/u;Lc4/i0$d;)V

    return-void
.end method

.method public final e(JIIJ)V
    .locals 7

    iget-boolean v0, p0, Lc4/p;->l:Z

    if-eqz v0, :cond_0

    iget-object v0, p0, Lc4/p;->k:Lc4/p$b;

    invoke-virtual {v0}, Lc4/p$b;->c()Z

    move-result v0

    if-eqz v0, :cond_3

    :cond_0
    iget-object v0, p0, Lc4/p;->d:Lc4/u;

    invoke-virtual {v0, p4}, Lc4/u;->b(I)Z

    iget-object v0, p0, Lc4/p;->e:Lc4/u;

    invoke-virtual {v0, p4}, Lc4/u;->b(I)Z

    iget-boolean v0, p0, Lc4/p;->l:Z

    const/4 v1, 0x3

    if-nez v0, :cond_1

    iget-object v0, p0, Lc4/p;->d:Lc4/u;

    invoke-virtual {v0}, Lc4/u;->c()Z

    move-result v0

    if-eqz v0, :cond_3

    iget-object v0, p0, Lc4/p;->e:Lc4/u;

    invoke-virtual {v0}, Lc4/u;->c()Z

    move-result v0

    if-eqz v0, :cond_3

    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iget-object v2, p0, Lc4/p;->d:Lc4/u;

    iget-object v3, v2, Lc4/u;->d:[B

    iget v2, v2, Lc4/u;->e:I

    invoke-static {v3, v2}, Ljava/util/Arrays;->copyOf([BI)[B

    move-result-object v2

    invoke-interface {v0, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    iget-object v2, p0, Lc4/p;->e:Lc4/u;

    iget-object v3, v2, Lc4/u;->d:[B

    iget v2, v2, Lc4/u;->e:I

    invoke-static {v3, v2}, Ljava/util/Arrays;->copyOf([BI)[B

    move-result-object v2

    invoke-interface {v0, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    iget-object v2, p0, Lc4/p;->d:Lc4/u;

    iget-object v3, v2, Lc4/u;->d:[B

    iget v2, v2, Lc4/u;->e:I

    invoke-static {v3, v1, v2}, Lf2/a;->l([BII)Lf2/a$c;

    move-result-object v2

    iget-object v3, p0, Lc4/p;->e:Lc4/u;

    iget-object v4, v3, Lc4/u;->d:[B

    iget v3, v3, Lc4/u;->e:I

    invoke-static {v4, v1, v3}, Lf2/a;->j([BII)Lf2/a$b;

    move-result-object v1

    iget v3, v2, Lf2/a$c;->a:I

    iget v4, v2, Lf2/a$c;->b:I

    iget v5, v2, Lf2/a$c;->c:I

    invoke-static {v3, v4, v5}, Le2/e;->a(III)Ljava/lang/String;

    move-result-object v3

    iget-object v4, p0, Lc4/p;->j:Lz2/r0;

    new-instance v5, Landroidx/media3/common/y$b;

    invoke-direct {v5}, Landroidx/media3/common/y$b;-><init>()V

    iget-object v6, p0, Lc4/p;->i:Ljava/lang/String;

    invoke-virtual {v5, v6}, Landroidx/media3/common/y$b;->X(Ljava/lang/String;)Landroidx/media3/common/y$b;

    move-result-object v5

    const-string v6, "video/avc"

    invoke-virtual {v5, v6}, Landroidx/media3/common/y$b;->k0(Ljava/lang/String;)Landroidx/media3/common/y$b;

    move-result-object v5

    invoke-virtual {v5, v3}, Landroidx/media3/common/y$b;->M(Ljava/lang/String;)Landroidx/media3/common/y$b;

    move-result-object v3

    iget v5, v2, Lf2/a$c;->f:I

    invoke-virtual {v3, v5}, Landroidx/media3/common/y$b;->r0(I)Landroidx/media3/common/y$b;

    move-result-object v3

    iget v5, v2, Lf2/a$c;->g:I

    invoke-virtual {v3, v5}, Landroidx/media3/common/y$b;->V(I)Landroidx/media3/common/y$b;

    move-result-object v3

    new-instance v5, Landroidx/media3/common/k$b;

    invoke-direct {v5}, Landroidx/media3/common/k$b;-><init>()V

    iget v6, v2, Lf2/a$c;->q:I

    invoke-virtual {v5, v6}, Landroidx/media3/common/k$b;->d(I)Landroidx/media3/common/k$b;

    move-result-object v5

    iget v6, v2, Lf2/a$c;->r:I

    invoke-virtual {v5, v6}, Landroidx/media3/common/k$b;->c(I)Landroidx/media3/common/k$b;

    move-result-object v5

    iget v6, v2, Lf2/a$c;->s:I

    invoke-virtual {v5, v6}, Landroidx/media3/common/k$b;->e(I)Landroidx/media3/common/k$b;

    move-result-object v5

    iget v6, v2, Lf2/a$c;->i:I

    add-int/lit8 v6, v6, 0x8

    invoke-virtual {v5, v6}, Landroidx/media3/common/k$b;->g(I)Landroidx/media3/common/k$b;

    move-result-object v5

    iget v6, v2, Lf2/a$c;->j:I

    add-int/lit8 v6, v6, 0x8

    invoke-virtual {v5, v6}, Landroidx/media3/common/k$b;->b(I)Landroidx/media3/common/k$b;

    move-result-object v5

    invoke-virtual {v5}, Landroidx/media3/common/k$b;->a()Landroidx/media3/common/k;

    move-result-object v5

    invoke-virtual {v3, v5}, Landroidx/media3/common/y$b;->N(Landroidx/media3/common/k;)Landroidx/media3/common/y$b;

    move-result-object v3

    iget v5, v2, Lf2/a$c;->h:F

    invoke-virtual {v3, v5}, Landroidx/media3/common/y$b;->g0(F)Landroidx/media3/common/y$b;

    move-result-object v3

    invoke-virtual {v3, v0}, Landroidx/media3/common/y$b;->Y(Ljava/util/List;)Landroidx/media3/common/y$b;

    move-result-object v0

    invoke-virtual {v0}, Landroidx/media3/common/y$b;->I()Landroidx/media3/common/y;

    move-result-object v0

    invoke-interface {v4, v0}, Lz2/r0;->a(Landroidx/media3/common/y;)V

    const/4 v0, 0x1

    iput-boolean v0, p0, Lc4/p;->l:Z

    iget-object v0, p0, Lc4/p;->k:Lc4/p$b;

    invoke-virtual {v0, v2}, Lc4/p$b;->f(Lf2/a$c;)V

    iget-object v0, p0, Lc4/p;->k:Lc4/p$b;

    invoke-virtual {v0, v1}, Lc4/p$b;->e(Lf2/a$b;)V

    iget-object v0, p0, Lc4/p;->d:Lc4/u;

    invoke-virtual {v0}, Lc4/u;->d()V

    iget-object v0, p0, Lc4/p;->e:Lc4/u;

    invoke-virtual {v0}, Lc4/u;->d()V

    goto :goto_0

    :cond_1
    iget-object v0, p0, Lc4/p;->d:Lc4/u;

    invoke-virtual {v0}, Lc4/u;->c()Z

    move-result v0

    if-eqz v0, :cond_2

    iget-object v0, p0, Lc4/p;->d:Lc4/u;

    iget-object v2, v0, Lc4/u;->d:[B

    iget v0, v0, Lc4/u;->e:I

    invoke-static {v2, v1, v0}, Lf2/a;->l([BII)Lf2/a$c;

    move-result-object v0

    iget-object v1, p0, Lc4/p;->k:Lc4/p$b;

    invoke-virtual {v1, v0}, Lc4/p$b;->f(Lf2/a$c;)V

    iget-object v0, p0, Lc4/p;->d:Lc4/u;

    invoke-virtual {v0}, Lc4/u;->d()V

    goto :goto_0

    :cond_2
    iget-object v0, p0, Lc4/p;->e:Lc4/u;

    invoke-virtual {v0}, Lc4/u;->c()Z

    move-result v0

    if-eqz v0, :cond_3

    iget-object v0, p0, Lc4/p;->e:Lc4/u;

    iget-object v2, v0, Lc4/u;->d:[B

    iget v0, v0, Lc4/u;->e:I

    invoke-static {v2, v1, v0}, Lf2/a;->j([BII)Lf2/a$b;

    move-result-object v0

    iget-object v1, p0, Lc4/p;->k:Lc4/p$b;

    invoke-virtual {v1, v0}, Lc4/p$b;->e(Lf2/a$b;)V

    iget-object v0, p0, Lc4/p;->e:Lc4/u;

    invoke-virtual {v0}, Lc4/u;->d()V

    :cond_3
    :goto_0
    iget-object v0, p0, Lc4/p;->f:Lc4/u;

    invoke-virtual {v0, p4}, Lc4/u;->b(I)Z

    move-result p4

    if-eqz p4, :cond_4

    iget-object p4, p0, Lc4/p;->f:Lc4/u;

    iget-object v0, p4, Lc4/u;->d:[B

    iget p4, p4, Lc4/u;->e:I

    invoke-static {v0, p4}, Lf2/a;->q([BI)I

    move-result p4

    iget-object v0, p0, Lc4/p;->o:Le2/c0;

    iget-object v1, p0, Lc4/p;->f:Lc4/u;

    iget-object v1, v1, Lc4/u;->d:[B

    invoke-virtual {v0, v1, p4}, Le2/c0;->S([BI)V

    iget-object p4, p0, Lc4/p;->o:Le2/c0;

    const/4 v0, 0x4

    invoke-virtual {p4, v0}, Le2/c0;->U(I)V

    iget-object p4, p0, Lc4/p;->a:Lc4/d0;

    iget-object v0, p0, Lc4/p;->o:Le2/c0;

    invoke-virtual {p4, p5, p6, v0}, Lc4/d0;->a(JLe2/c0;)V

    :cond_4
    iget-object p4, p0, Lc4/p;->k:Lc4/p$b;

    iget-boolean p5, p0, Lc4/p;->l:Z

    invoke-virtual {p4, p1, p2, p3, p5}, Lc4/p$b;->b(JIZ)Z

    move-result p1

    if-eqz p1, :cond_5

    const/4 p1, 0x0

    iput-boolean p1, p0, Lc4/p;->n:Z

    :cond_5
    return-void
.end method

.method public final f([BII)V
    .locals 1

    iget-boolean v0, p0, Lc4/p;->l:Z

    if-eqz v0, :cond_0

    iget-object v0, p0, Lc4/p;->k:Lc4/p$b;

    invoke-virtual {v0}, Lc4/p$b;->c()Z

    move-result v0

    if-eqz v0, :cond_1

    :cond_0
    iget-object v0, p0, Lc4/p;->d:Lc4/u;

    invoke-virtual {v0, p1, p2, p3}, Lc4/u;->a([BII)V

    iget-object v0, p0, Lc4/p;->e:Lc4/u;

    invoke-virtual {v0, p1, p2, p3}, Lc4/u;->a([BII)V

    :cond_1
    iget-object v0, p0, Lc4/p;->f:Lc4/u;

    invoke-virtual {v0, p1, p2, p3}, Lc4/u;->a([BII)V

    iget-object v0, p0, Lc4/p;->k:Lc4/p$b;

    invoke-virtual {v0, p1, p2, p3}, Lc4/p$b;->a([BII)V

    return-void
.end method

.method public final g(JIJ)V
    .locals 8

    iget-boolean v0, p0, Lc4/p;->l:Z

    if-eqz v0, :cond_0

    iget-object v0, p0, Lc4/p;->k:Lc4/p$b;

    invoke-virtual {v0}, Lc4/p$b;->c()Z

    move-result v0

    if-eqz v0, :cond_1

    :cond_0
    iget-object v0, p0, Lc4/p;->d:Lc4/u;

    invoke-virtual {v0, p3}, Lc4/u;->e(I)V

    iget-object v0, p0, Lc4/p;->e:Lc4/u;

    invoke-virtual {v0, p3}, Lc4/u;->e(I)V

    :cond_1
    iget-object v0, p0, Lc4/p;->f:Lc4/u;

    invoke-virtual {v0, p3}, Lc4/u;->e(I)V

    iget-object v1, p0, Lc4/p;->k:Lc4/p$b;

    iget-boolean v7, p0, Lc4/p;->n:Z

    move-wide v2, p1

    move v4, p3

    move-wide v5, p4

    invoke-virtual/range {v1 .. v7}, Lc4/p$b;->h(JIJZ)V

    return-void
.end method

.method public packetFinished()V
    .locals 0

    return-void
.end method

.method public seek()V
    .locals 2

    const-wide/16 v0, 0x0

    iput-wide v0, p0, Lc4/p;->g:J

    const/4 v0, 0x0

    iput-boolean v0, p0, Lc4/p;->n:Z

    const-wide v0, -0x7fffffffffffffffL    # -4.9E-324

    iput-wide v0, p0, Lc4/p;->m:J

    iget-object v0, p0, Lc4/p;->h:[Z

    invoke-static {v0}, Lf2/a;->a([Z)V

    iget-object v0, p0, Lc4/p;->d:Lc4/u;

    invoke-virtual {v0}, Lc4/u;->d()V

    iget-object v0, p0, Lc4/p;->e:Lc4/u;

    invoke-virtual {v0}, Lc4/u;->d()V

    iget-object v0, p0, Lc4/p;->f:Lc4/u;

    invoke-virtual {v0}, Lc4/u;->d()V

    iget-object v0, p0, Lc4/p;->k:Lc4/p$b;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Lc4/p$b;->g()V

    :cond_0
    return-void
.end method
