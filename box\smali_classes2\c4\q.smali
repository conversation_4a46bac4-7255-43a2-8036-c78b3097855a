.class public final Lc4/q;
.super Ljava/lang/Object;

# interfaces
.implements Lc4/m;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lc4/q$a;
    }
.end annotation


# instance fields
.field public final a:Lc4/d0;

.field public b:Ljava/lang/String;

.field public c:Lz2/r0;

.field public d:Lc4/q$a;

.field public e:Z

.field public final f:[Z

.field public final g:Lc4/u;

.field public final h:Lc4/u;

.field public final i:Lc4/u;

.field public final j:Lc4/u;

.field public final k:Lc4/u;

.field public l:J

.field public m:J

.field public final n:Le2/c0;


# direct methods
.method public constructor <init>(Lc4/d0;)V
    .locals 2

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lc4/q;->a:Lc4/d0;

    const/4 p1, 0x3

    new-array p1, p1, [Z

    iput-object p1, p0, Lc4/q;->f:[Z

    new-instance p1, Lc4/u;

    const/16 v0, 0x20

    const/16 v1, 0x80

    invoke-direct {p1, v0, v1}, Lc4/u;-><init>(II)V

    iput-object p1, p0, Lc4/q;->g:Lc4/u;

    new-instance p1, Lc4/u;

    const/16 v0, 0x21

    invoke-direct {p1, v0, v1}, Lc4/u;-><init>(II)V

    iput-object p1, p0, Lc4/q;->h:Lc4/u;

    new-instance p1, Lc4/u;

    const/16 v0, 0x22

    invoke-direct {p1, v0, v1}, Lc4/u;-><init>(II)V

    iput-object p1, p0, Lc4/q;->i:Lc4/u;

    new-instance p1, Lc4/u;

    const/16 v0, 0x27

    invoke-direct {p1, v0, v1}, Lc4/u;-><init>(II)V

    iput-object p1, p0, Lc4/q;->j:Lc4/u;

    new-instance p1, Lc4/u;

    const/16 v0, 0x28

    invoke-direct {p1, v0, v1}, Lc4/u;-><init>(II)V

    iput-object p1, p0, Lc4/q;->k:Lc4/u;

    const-wide v0, -0x7fffffffffffffffL    # -4.9E-324

    iput-wide v0, p0, Lc4/q;->m:J

    new-instance p1, Le2/c0;

    invoke-direct {p1}, Le2/c0;-><init>()V

    iput-object p1, p0, Lc4/q;->n:Le2/c0;

    return-void
.end method

.method private b()V
    .locals 1

    iget-object v0, p0, Lc4/q;->c:Lz2/r0;

    invoke-static {v0}, Le2/a;->i(Ljava/lang/Object;)Ljava/lang/Object;

    iget-object v0, p0, Lc4/q;->d:Lc4/q$a;

    invoke-static {v0}, Le2/u0;->i(Ljava/lang/Object;)Ljava/lang/Object;

    return-void
.end method

.method private e(JIIJ)V
    .locals 2

    iget-object v0, p0, Lc4/q;->d:Lc4/q$a;

    iget-boolean v1, p0, Lc4/q;->e:Z

    invoke-virtual {v0, p1, p2, p3, v1}, Lc4/q$a;->a(JIZ)V

    iget-boolean p1, p0, Lc4/q;->e:Z

    if-nez p1, :cond_0

    iget-object p1, p0, Lc4/q;->g:Lc4/u;

    invoke-virtual {p1, p4}, Lc4/u;->b(I)Z

    iget-object p1, p0, Lc4/q;->h:Lc4/u;

    invoke-virtual {p1, p4}, Lc4/u;->b(I)Z

    iget-object p1, p0, Lc4/q;->i:Lc4/u;

    invoke-virtual {p1, p4}, Lc4/u;->b(I)Z

    iget-object p1, p0, Lc4/q;->g:Lc4/u;

    invoke-virtual {p1}, Lc4/u;->c()Z

    move-result p1

    if-eqz p1, :cond_0

    iget-object p1, p0, Lc4/q;->h:Lc4/u;

    invoke-virtual {p1}, Lc4/u;->c()Z

    move-result p1

    if-eqz p1, :cond_0

    iget-object p1, p0, Lc4/q;->i:Lc4/u;

    invoke-virtual {p1}, Lc4/u;->c()Z

    move-result p1

    if-eqz p1, :cond_0

    iget-object p1, p0, Lc4/q;->c:Lz2/r0;

    iget-object p2, p0, Lc4/q;->b:Ljava/lang/String;

    iget-object p3, p0, Lc4/q;->g:Lc4/u;

    iget-object v0, p0, Lc4/q;->h:Lc4/u;

    iget-object v1, p0, Lc4/q;->i:Lc4/u;

    invoke-static {p2, p3, v0, v1}, Lc4/q;->g(Ljava/lang/String;Lc4/u;Lc4/u;Lc4/u;)Landroidx/media3/common/y;

    move-result-object p2

    invoke-interface {p1, p2}, Lz2/r0;->a(Landroidx/media3/common/y;)V

    const/4 p1, 0x1

    iput-boolean p1, p0, Lc4/q;->e:Z

    :cond_0
    iget-object p1, p0, Lc4/q;->j:Lc4/u;

    invoke-virtual {p1, p4}, Lc4/u;->b(I)Z

    move-result p1

    const/4 p2, 0x5

    if-eqz p1, :cond_1

    iget-object p1, p0, Lc4/q;->j:Lc4/u;

    iget-object p3, p1, Lc4/u;->d:[B

    iget p1, p1, Lc4/u;->e:I

    invoke-static {p3, p1}, Lf2/a;->q([BI)I

    move-result p1

    iget-object p3, p0, Lc4/q;->n:Le2/c0;

    iget-object v0, p0, Lc4/q;->j:Lc4/u;

    iget-object v0, v0, Lc4/u;->d:[B

    invoke-virtual {p3, v0, p1}, Le2/c0;->S([BI)V

    iget-object p1, p0, Lc4/q;->n:Le2/c0;

    invoke-virtual {p1, p2}, Le2/c0;->V(I)V

    iget-object p1, p0, Lc4/q;->a:Lc4/d0;

    iget-object p3, p0, Lc4/q;->n:Le2/c0;

    invoke-virtual {p1, p5, p6, p3}, Lc4/d0;->a(JLe2/c0;)V

    :cond_1
    iget-object p1, p0, Lc4/q;->k:Lc4/u;

    invoke-virtual {p1, p4}, Lc4/u;->b(I)Z

    move-result p1

    if-eqz p1, :cond_2

    iget-object p1, p0, Lc4/q;->k:Lc4/u;

    iget-object p3, p1, Lc4/u;->d:[B

    iget p1, p1, Lc4/u;->e:I

    invoke-static {p3, p1}, Lf2/a;->q([BI)I

    move-result p1

    iget-object p3, p0, Lc4/q;->n:Le2/c0;

    iget-object p4, p0, Lc4/q;->k:Lc4/u;

    iget-object p4, p4, Lc4/u;->d:[B

    invoke-virtual {p3, p4, p1}, Le2/c0;->S([BI)V

    iget-object p1, p0, Lc4/q;->n:Le2/c0;

    invoke-virtual {p1, p2}, Le2/c0;->V(I)V

    iget-object p1, p0, Lc4/q;->a:Lc4/d0;

    iget-object p2, p0, Lc4/q;->n:Le2/c0;

    invoke-virtual {p1, p5, p6, p2}, Lc4/d0;->a(JLe2/c0;)V

    :cond_2
    return-void
.end method

.method private f([BII)V
    .locals 1

    iget-object v0, p0, Lc4/q;->d:Lc4/q$a;

    invoke-virtual {v0, p1, p2, p3}, Lc4/q$a;->e([BII)V

    iget-boolean v0, p0, Lc4/q;->e:Z

    if-nez v0, :cond_0

    iget-object v0, p0, Lc4/q;->g:Lc4/u;

    invoke-virtual {v0, p1, p2, p3}, Lc4/u;->a([BII)V

    iget-object v0, p0, Lc4/q;->h:Lc4/u;

    invoke-virtual {v0, p1, p2, p3}, Lc4/u;->a([BII)V

    iget-object v0, p0, Lc4/q;->i:Lc4/u;

    invoke-virtual {v0, p1, p2, p3}, Lc4/u;->a([BII)V

    :cond_0
    iget-object v0, p0, Lc4/q;->j:Lc4/u;

    invoke-virtual {v0, p1, p2, p3}, Lc4/u;->a([BII)V

    iget-object v0, p0, Lc4/q;->k:Lc4/u;

    invoke-virtual {v0, p1, p2, p3}, Lc4/u;->a([BII)V

    return-void
.end method

.method public static g(Ljava/lang/String;Lc4/u;Lc4/u;Lc4/u;)Landroidx/media3/common/y;
    .locals 8
    .param p0    # Ljava/lang/String;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    iget v0, p1, Lc4/u;->e:I

    iget v1, p2, Lc4/u;->e:I

    add-int/2addr v1, v0

    iget v2, p3, Lc4/u;->e:I

    add-int/2addr v1, v2

    new-array v1, v1, [B

    iget-object v2, p1, Lc4/u;->d:[B

    const/4 v3, 0x0

    invoke-static {v2, v3, v1, v3, v0}, Ljava/lang/System;->arraycopy(Ljava/lang/Object;ILjava/lang/Object;II)V

    iget-object v0, p2, Lc4/u;->d:[B

    iget v2, p1, Lc4/u;->e:I

    iget v4, p2, Lc4/u;->e:I

    invoke-static {v0, v3, v1, v2, v4}, Ljava/lang/System;->arraycopy(Ljava/lang/Object;ILjava/lang/Object;II)V

    iget-object v0, p3, Lc4/u;->d:[B

    iget p1, p1, Lc4/u;->e:I

    iget v2, p2, Lc4/u;->e:I

    add-int/2addr p1, v2

    iget p3, p3, Lc4/u;->e:I

    invoke-static {v0, v3, v1, p1, p3}, Ljava/lang/System;->arraycopy(Ljava/lang/Object;ILjava/lang/Object;II)V

    iget-object p1, p2, Lc4/u;->d:[B

    const/4 p3, 0x3

    iget p2, p2, Lc4/u;->e:I

    invoke-static {p1, p3, p2}, Lf2/a;->h([BII)Lf2/a$a;

    move-result-object p1

    iget v2, p1, Lf2/a$a;->a:I

    iget-boolean v3, p1, Lf2/a$a;->b:Z

    iget v4, p1, Lf2/a$a;->c:I

    iget v5, p1, Lf2/a$a;->d:I

    iget-object v6, p1, Lf2/a$a;->h:[I

    iget v7, p1, Lf2/a$a;->i:I

    invoke-static/range {v2 .. v7}, Le2/e;->c(IZII[II)Ljava/lang/String;

    move-result-object p2

    new-instance p3, Landroidx/media3/common/y$b;

    invoke-direct {p3}, Landroidx/media3/common/y$b;-><init>()V

    invoke-virtual {p3, p0}, Landroidx/media3/common/y$b;->X(Ljava/lang/String;)Landroidx/media3/common/y$b;

    move-result-object p0

    const-string p3, "video/hevc"

    invoke-virtual {p0, p3}, Landroidx/media3/common/y$b;->k0(Ljava/lang/String;)Landroidx/media3/common/y$b;

    move-result-object p0

    invoke-virtual {p0, p2}, Landroidx/media3/common/y$b;->M(Ljava/lang/String;)Landroidx/media3/common/y$b;

    move-result-object p0

    iget p2, p1, Lf2/a$a;->k:I

    invoke-virtual {p0, p2}, Landroidx/media3/common/y$b;->r0(I)Landroidx/media3/common/y$b;

    move-result-object p0

    iget p2, p1, Lf2/a$a;->l:I

    invoke-virtual {p0, p2}, Landroidx/media3/common/y$b;->V(I)Landroidx/media3/common/y$b;

    move-result-object p0

    new-instance p2, Landroidx/media3/common/k$b;

    invoke-direct {p2}, Landroidx/media3/common/k$b;-><init>()V

    iget p3, p1, Lf2/a$a;->n:I

    invoke-virtual {p2, p3}, Landroidx/media3/common/k$b;->d(I)Landroidx/media3/common/k$b;

    move-result-object p2

    iget p3, p1, Lf2/a$a;->o:I

    invoke-virtual {p2, p3}, Landroidx/media3/common/k$b;->c(I)Landroidx/media3/common/k$b;

    move-result-object p2

    iget p3, p1, Lf2/a$a;->p:I

    invoke-virtual {p2, p3}, Landroidx/media3/common/k$b;->e(I)Landroidx/media3/common/k$b;

    move-result-object p2

    iget p3, p1, Lf2/a$a;->f:I

    add-int/lit8 p3, p3, 0x8

    invoke-virtual {p2, p3}, Landroidx/media3/common/k$b;->g(I)Landroidx/media3/common/k$b;

    move-result-object p2

    iget p3, p1, Lf2/a$a;->g:I

    add-int/lit8 p3, p3, 0x8

    invoke-virtual {p2, p3}, Landroidx/media3/common/k$b;->b(I)Landroidx/media3/common/k$b;

    move-result-object p2

    invoke-virtual {p2}, Landroidx/media3/common/k$b;->a()Landroidx/media3/common/k;

    move-result-object p2

    invoke-virtual {p0, p2}, Landroidx/media3/common/y$b;->N(Landroidx/media3/common/k;)Landroidx/media3/common/y$b;

    move-result-object p0

    iget p1, p1, Lf2/a$a;->m:F

    invoke-virtual {p0, p1}, Landroidx/media3/common/y$b;->g0(F)Landroidx/media3/common/y$b;

    move-result-object p0

    invoke-static {v1}, Ljava/util/Collections;->singletonList(Ljava/lang/Object;)Ljava/util/List;

    move-result-object p1

    invoke-virtual {p0, p1}, Landroidx/media3/common/y$b;->Y(Ljava/util/List;)Landroidx/media3/common/y$b;

    move-result-object p0

    invoke-virtual {p0}, Landroidx/media3/common/y$b;->I()Landroidx/media3/common/y;

    move-result-object p0

    return-object p0
.end method


# virtual methods
.method public a(Le2/c0;)V
    .locals 16

    move-object/from16 v7, p0

    invoke-direct/range {p0 .. p0}, Lc4/q;->b()V

    :cond_0
    invoke-virtual/range {p1 .. p1}, Le2/c0;->a()I

    move-result v0

    if-lez v0, :cond_4

    invoke-virtual/range {p1 .. p1}, Le2/c0;->f()I

    move-result v0

    invoke-virtual/range {p1 .. p1}, Le2/c0;->g()I

    move-result v8

    invoke-virtual/range {p1 .. p1}, Le2/c0;->e()[B

    move-result-object v9

    iget-wide v1, v7, Lc4/q;->l:J

    invoke-virtual/range {p1 .. p1}, Le2/c0;->a()I

    move-result v3

    int-to-long v3, v3

    add-long/2addr v1, v3

    iput-wide v1, v7, Lc4/q;->l:J

    iget-object v1, v7, Lc4/q;->c:Lz2/r0;

    invoke-virtual/range {p1 .. p1}, Le2/c0;->a()I

    move-result v2

    move-object/from16 v10, p1

    invoke-interface {v1, v10, v2}, Lz2/r0;->f(Le2/c0;I)V

    :goto_0
    if-ge v0, v8, :cond_0

    iget-object v1, v7, Lc4/q;->f:[Z

    invoke-static {v9, v0, v8, v1}, Lf2/a;->c([BII[Z)I

    move-result v11

    if-ne v11, v8, :cond_1

    invoke-direct {v7, v9, v0, v8}, Lc4/q;->f([BII)V

    return-void

    :cond_1
    invoke-static {v9, v11}, Lf2/a;->e([BI)I

    move-result v12

    sub-int v1, v11, v0

    if-lez v1, :cond_2

    invoke-direct {v7, v9, v0, v11}, Lc4/q;->f([BII)V

    :cond_2
    sub-int v13, v8, v11

    iget-wide v2, v7, Lc4/q;->l:J

    int-to-long v4, v13

    sub-long v14, v2, v4

    if-gez v1, :cond_3

    neg-int v0, v1

    move v4, v0

    goto :goto_1

    :cond_3
    const/4 v0, 0x0

    const/4 v4, 0x0

    :goto_1
    iget-wide v5, v7, Lc4/q;->m:J

    move-object/from16 v0, p0

    move-wide v1, v14

    move v3, v13

    invoke-direct/range {v0 .. v6}, Lc4/q;->e(JIIJ)V

    iget-wide v5, v7, Lc4/q;->m:J

    move v4, v12

    invoke-virtual/range {v0 .. v6}, Lc4/q;->h(JIIJ)V

    add-int/lit8 v0, v11, 0x3

    goto :goto_0

    :cond_4
    return-void
.end method

.method public c(JI)V
    .locals 0

    iput-wide p1, p0, Lc4/q;->m:J

    return-void
.end method

.method public d(Lz2/u;Lc4/i0$d;)V
    .locals 2

    invoke-virtual {p2}, Lc4/i0$d;->a()V

    invoke-virtual {p2}, Lc4/i0$d;->b()Ljava/lang/String;

    move-result-object v0

    iput-object v0, p0, Lc4/q;->b:Ljava/lang/String;

    invoke-virtual {p2}, Lc4/i0$d;->c()I

    move-result v0

    const/4 v1, 0x2

    invoke-interface {p1, v0, v1}, Lz2/u;->track(II)Lz2/r0;

    move-result-object v0

    iput-object v0, p0, Lc4/q;->c:Lz2/r0;

    new-instance v1, Lc4/q$a;

    invoke-direct {v1, v0}, Lc4/q$a;-><init>(Lz2/r0;)V

    iput-object v1, p0, Lc4/q;->d:Lc4/q$a;

    iget-object v0, p0, Lc4/q;->a:Lc4/d0;

    invoke-virtual {v0, p1, p2}, Lc4/d0;->b(Lz2/u;Lc4/i0$d;)V

    return-void
.end method

.method public final h(JIIJ)V
    .locals 8

    iget-object v0, p0, Lc4/q;->d:Lc4/q$a;

    iget-boolean v7, p0, Lc4/q;->e:Z

    move-wide v1, p1

    move v3, p3

    move v4, p4

    move-wide v5, p5

    invoke-virtual/range {v0 .. v7}, Lc4/q$a;->g(JIIJZ)V

    iget-boolean p1, p0, Lc4/q;->e:Z

    if-nez p1, :cond_0

    iget-object p1, p0, Lc4/q;->g:Lc4/u;

    invoke-virtual {p1, p4}, Lc4/u;->e(I)V

    iget-object p1, p0, Lc4/q;->h:Lc4/u;

    invoke-virtual {p1, p4}, Lc4/u;->e(I)V

    iget-object p1, p0, Lc4/q;->i:Lc4/u;

    invoke-virtual {p1, p4}, Lc4/u;->e(I)V

    :cond_0
    iget-object p1, p0, Lc4/q;->j:Lc4/u;

    invoke-virtual {p1, p4}, Lc4/u;->e(I)V

    iget-object p1, p0, Lc4/q;->k:Lc4/u;

    invoke-virtual {p1, p4}, Lc4/u;->e(I)V

    return-void
.end method

.method public packetFinished()V
    .locals 0

    return-void
.end method

.method public seek()V
    .locals 2

    const-wide/16 v0, 0x0

    iput-wide v0, p0, Lc4/q;->l:J

    const-wide v0, -0x7fffffffffffffffL    # -4.9E-324

    iput-wide v0, p0, Lc4/q;->m:J

    iget-object v0, p0, Lc4/q;->f:[Z

    invoke-static {v0}, Lf2/a;->a([Z)V

    iget-object v0, p0, Lc4/q;->g:Lc4/u;

    invoke-virtual {v0}, Lc4/u;->d()V

    iget-object v0, p0, Lc4/q;->h:Lc4/u;

    invoke-virtual {v0}, Lc4/u;->d()V

    iget-object v0, p0, Lc4/q;->i:Lc4/u;

    invoke-virtual {v0}, Lc4/u;->d()V

    iget-object v0, p0, Lc4/q;->j:Lc4/u;

    invoke-virtual {v0}, Lc4/u;->d()V

    iget-object v0, p0, Lc4/q;->k:Lc4/u;

    invoke-virtual {v0}, Lc4/u;->d()V

    iget-object v0, p0, Lc4/q;->d:Lc4/q$a;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Lc4/q$a;->f()V

    :cond_0
    return-void
.end method
