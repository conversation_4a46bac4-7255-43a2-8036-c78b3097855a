.class public final Lc4/v;
.super Ljava/lang/Object;

# interfaces
.implements Lc4/b0;


# instance fields
.field public a:Landroidx/media3/common/y;

.field public b:Le2/i0;

.field public c:Lz2/r0;


# direct methods
.method public constructor <init>(Ljava/lang/String;)V
    .locals 1

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    new-instance v0, Landroidx/media3/common/y$b;

    invoke-direct {v0}, Landroidx/media3/common/y$b;-><init>()V

    invoke-virtual {v0, p1}, Landroidx/media3/common/y$b;->k0(Ljava/lang/String;)Landroidx/media3/common/y$b;

    move-result-object p1

    invoke-virtual {p1}, Landroidx/media3/common/y$b;->I()Landroidx/media3/common/y;

    move-result-object p1

    iput-object p1, p0, Lc4/v;->a:Landroidx/media3/common/y;

    return-void
.end method


# virtual methods
.method public a(Le2/c0;)V
    .locals 8

    invoke-virtual {p0}, Lc4/v;->c()V

    iget-object v0, p0, Lc4/v;->b:Le2/i0;

    invoke-virtual {v0}, Le2/i0;->e()J

    move-result-wide v2

    iget-object v0, p0, Lc4/v;->b:Le2/i0;

    invoke-virtual {v0}, Le2/i0;->f()J

    move-result-wide v0

    const-wide v4, -0x7fffffffffffffffL    # -4.9E-324

    cmp-long v6, v2, v4

    if-eqz v6, :cond_2

    cmp-long v6, v0, v4

    if-nez v6, :cond_0

    goto :goto_0

    :cond_0
    iget-object v4, p0, Lc4/v;->a:Landroidx/media3/common/y;

    iget-wide v5, v4, Landroidx/media3/common/y;->q:J

    cmp-long v7, v0, v5

    if-eqz v7, :cond_1

    invoke-virtual {v4}, Landroidx/media3/common/y;->b()Landroidx/media3/common/y$b;

    move-result-object v4

    invoke-virtual {v4, v0, v1}, Landroidx/media3/common/y$b;->o0(J)Landroidx/media3/common/y$b;

    move-result-object v0

    invoke-virtual {v0}, Landroidx/media3/common/y$b;->I()Landroidx/media3/common/y;

    move-result-object v0

    iput-object v0, p0, Lc4/v;->a:Landroidx/media3/common/y;

    iget-object v1, p0, Lc4/v;->c:Lz2/r0;

    invoke-interface {v1, v0}, Lz2/r0;->a(Landroidx/media3/common/y;)V

    :cond_1
    invoke-virtual {p1}, Le2/c0;->a()I

    move-result v5

    iget-object v0, p0, Lc4/v;->c:Lz2/r0;

    invoke-interface {v0, p1, v5}, Lz2/r0;->f(Le2/c0;I)V

    iget-object v1, p0, Lc4/v;->c:Lz2/r0;

    const/4 v4, 0x1

    const/4 v6, 0x0

    const/4 v7, 0x0

    invoke-interface/range {v1 .. v7}, Lz2/r0;->e(JIIILz2/r0$a;)V

    :cond_2
    :goto_0
    return-void
.end method

.method public b(Le2/i0;Lz2/u;Lc4/i0$d;)V
    .locals 0

    iput-object p1, p0, Lc4/v;->b:Le2/i0;

    invoke-virtual {p3}, Lc4/i0$d;->a()V

    invoke-virtual {p3}, Lc4/i0$d;->c()I

    move-result p1

    const/4 p3, 0x5

    invoke-interface {p2, p1, p3}, Lz2/u;->track(II)Lz2/r0;

    move-result-object p1

    iput-object p1, p0, Lc4/v;->c:Lz2/r0;

    iget-object p2, p0, Lc4/v;->a:Landroidx/media3/common/y;

    invoke-interface {p1, p2}, Lz2/r0;->a(Landroidx/media3/common/y;)V

    return-void
.end method

.method public final c()V
    .locals 1

    iget-object v0, p0, Lc4/v;->b:Le2/i0;

    invoke-static {v0}, Le2/a;->i(Ljava/lang/Object;)Ljava/lang/Object;

    iget-object v0, p0, Lc4/v;->c:Lz2/r0;

    invoke-static {v0}, Le2/u0;->i(Ljava/lang/Object;)Ljava/lang/Object;

    return-void
.end method
