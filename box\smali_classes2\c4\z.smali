.class public final synthetic Lc4/z;
.super Ljava/lang/Object;

# interfaces
.implements Lz2/y;


# direct methods
.method public synthetic constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public synthetic a(Lt3/s$a;)Lz2/y;
    .locals 0

    invoke-static {p0, p1}, Lz2/x;->c(Lz2/y;Lt3/s$a;)Lz2/y;

    move-result-object p1

    return-object p1
.end method

.method public synthetic b(Landroid/net/Uri;Ljava/util/Map;)[Lz2/s;
    .locals 0

    invoke-static {p0, p1, p2}, Lz2/x;->a(Lz2/y;Landroid/net/Uri;Ljava/util/Map;)[Lz2/s;

    move-result-object p1

    return-object p1
.end method

.method public synthetic c(Z)Lz2/y;
    .locals 0

    invoke-static {p0, p1}, Lz2/x;->b(Lz2/y;Z)Lz2/y;

    move-result-object p1

    return-object p1
.end method

.method public final createExtractors()[Lz2/s;
    .locals 1

    invoke-static {}, Lc4/a0;->a()[Lz2/s;

    move-result-object v0

    return-object v0
.end method
