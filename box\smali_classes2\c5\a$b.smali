.class public final Lc5/a$b;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lc5/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "b"
.end annotation


# instance fields
.field public final a:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lc5/m;",
            ">;"
        }
    .end annotation
.end field

.field public final b:Lc5/u;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lc5/u;)V
    .locals 1
    .param p1    # Lc5/u;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lc5/a$b;->a:Ljava/util/List;

    iput-object p1, p0, Lc5/a$b;->b:Lc5/u;

    return-void
.end method

.method public synthetic constructor <init>(Lc5/u;Lc5/a$a;)V
    .locals 0

    invoke-direct {p0, p1}, Lc5/a$b;-><init>(Lc5/u;)V

    return-void
.end method

.method public static synthetic a(Lc5/a$b;)Ljava/util/List;
    .locals 0

    iget-object p0, p0, Lc5/a$b;->a:Ljava/util/List;

    return-object p0
.end method

.method public static synthetic b(Lc5/a$b;)Lc5/u;
    .locals 0

    iget-object p0, p0, Lc5/a$b;->b:Lc5/u;

    return-object p0
.end method
