.class public interface abstract Lc5/c;
.super Ljava/lang/Object;


# virtual methods
.method public abstract b(Ljava/util/List;Ljava/util/List;)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lc5/c;",
            ">;",
            "Ljava/util/List<",
            "Lc5/c;",
            ">;)V"
        }
    .end annotation
.end method

.method public abstract getName()Ljava/lang/String;
.end method
