.class public Lc6/c;
.super Ljava/lang/Object;


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final a(Ld6/d;)V
    .locals 6

    new-instance v0, Ld6/a;

    const-string v1, "white"

    invoke-direct {v0, v1}, Ld6/a;-><init>(Ljava/lang/String;)V

    const-string v2, "name"

    invoke-static {v2, v1}, Ld6/a;->b(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v1

    iput-object v1, v0, Ld6/a;->d:Ljava/lang/String;

    iget-object v1, p1, Ld6/d;->g:Ljava/util/Hashtable;

    iget-object v3, v0, Ld6/a;->a:Ljava/lang/String;

    invoke-virtual {v1, v3, v0}, Ljava/util/Hashtable;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    new-instance v1, Ld6/a;

    const-string v3, "whiteU"

    invoke-direct {v1, v3, v0}, Ld6/a;-><init>(Ljava/lang/String;Ld6/a;)V

    const/4 v0, 0x1

    iput-boolean v0, v1, Ld6/a;->i:Z

    iget-object v3, p1, Ld6/d;->g:Ljava/util/Hashtable;

    iget-object v4, v1, Ld6/a;->a:Ljava/lang/String;

    invoke-virtual {v3, v4, v1}, Ljava/util/Hashtable;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    new-instance v3, Ld6/a;

    const-string v4, "whiteUI"

    invoke-direct {v3, v4, v1}, Ld6/a;-><init>(Ljava/lang/String;Ld6/a;)V

    iput-boolean v0, v3, Ld6/a;->g:Z

    iget-object v1, p1, Ld6/d;->g:Ljava/util/Hashtable;

    iget-object v4, v3, Ld6/a;->a:Ljava/lang/String;

    invoke-virtual {v1, v4, v3}, Ljava/util/Hashtable;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    new-instance v1, Ld6/a;

    const-string v4, "whiteI"

    invoke-direct {v1, v4, v3}, Ld6/a;-><init>(Ljava/lang/String;Ld6/a;)V

    const/4 v3, 0x0

    iput-boolean v3, v1, Ld6/a;->i:Z

    iget-object v4, p1, Ld6/d;->g:Ljava/util/Hashtable;

    iget-object v5, v1, Ld6/a;->a:Ljava/lang/String;

    invoke-virtual {v4, v5, v1}, Ljava/util/Hashtable;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    new-instance v1, Ld6/a;

    const-string v4, "green"

    invoke-direct {v1, v4}, Ld6/a;-><init>(Ljava/lang/String;)V

    invoke-static {v2, v4}, Ld6/a;->b(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v4

    iput-object v4, v1, Ld6/a;->d:Ljava/lang/String;

    iget-object v4, p1, Ld6/d;->g:Ljava/util/Hashtable;

    iget-object v5, v1, Ld6/a;->a:Ljava/lang/String;

    invoke-virtual {v4, v5, v1}, Ljava/util/Hashtable;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    new-instance v4, Ld6/a;

    const-string v5, "greenU"

    invoke-direct {v4, v5, v1}, Ld6/a;-><init>(Ljava/lang/String;Ld6/a;)V

    iput-boolean v0, v4, Ld6/a;->i:Z

    iget-object v1, p1, Ld6/d;->g:Ljava/util/Hashtable;

    iget-object v5, v4, Ld6/a;->a:Ljava/lang/String;

    invoke-virtual {v1, v5, v4}, Ljava/util/Hashtable;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    new-instance v1, Ld6/a;

    const-string v5, "greenUI"

    invoke-direct {v1, v5, v4}, Ld6/a;-><init>(Ljava/lang/String;Ld6/a;)V

    iput-boolean v0, v1, Ld6/a;->g:Z

    iget-object v4, p1, Ld6/d;->g:Ljava/util/Hashtable;

    iget-object v5, v1, Ld6/a;->a:Ljava/lang/String;

    invoke-virtual {v4, v5, v1}, Ljava/util/Hashtable;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    new-instance v4, Ld6/a;

    const-string v5, "greenI"

    invoke-direct {v4, v5, v1}, Ld6/a;-><init>(Ljava/lang/String;Ld6/a;)V

    iput-boolean v3, v4, Ld6/a;->i:Z

    iget-object v1, p1, Ld6/d;->g:Ljava/util/Hashtable;

    iget-object v5, v4, Ld6/a;->a:Ljava/lang/String;

    invoke-virtual {v1, v5, v4}, Ljava/util/Hashtable;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    new-instance v1, Ld6/a;

    const-string v4, "blue"

    invoke-direct {v1, v4}, Ld6/a;-><init>(Ljava/lang/String;)V

    invoke-static {v2, v4}, Ld6/a;->b(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v4

    iput-object v4, v1, Ld6/a;->d:Ljava/lang/String;

    iget-object v4, p1, Ld6/d;->g:Ljava/util/Hashtable;

    iget-object v5, v1, Ld6/a;->a:Ljava/lang/String;

    invoke-virtual {v4, v5, v1}, Ljava/util/Hashtable;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    new-instance v4, Ld6/a;

    const-string v5, "blueU"

    invoke-direct {v4, v5, v1}, Ld6/a;-><init>(Ljava/lang/String;Ld6/a;)V

    iput-boolean v0, v4, Ld6/a;->i:Z

    iget-object v1, p1, Ld6/d;->g:Ljava/util/Hashtable;

    iget-object v5, v4, Ld6/a;->a:Ljava/lang/String;

    invoke-virtual {v1, v5, v4}, Ljava/util/Hashtable;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    new-instance v1, Ld6/a;

    const-string v5, "blueUI"

    invoke-direct {v1, v5, v4}, Ld6/a;-><init>(Ljava/lang/String;Ld6/a;)V

    iput-boolean v0, v1, Ld6/a;->g:Z

    iget-object v4, p1, Ld6/d;->g:Ljava/util/Hashtable;

    iget-object v5, v1, Ld6/a;->a:Ljava/lang/String;

    invoke-virtual {v4, v5, v1}, Ljava/util/Hashtable;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    new-instance v4, Ld6/a;

    const-string v5, "blueI"

    invoke-direct {v4, v5, v1}, Ld6/a;-><init>(Ljava/lang/String;Ld6/a;)V

    iput-boolean v3, v4, Ld6/a;->i:Z

    iget-object v1, p1, Ld6/d;->g:Ljava/util/Hashtable;

    iget-object v5, v4, Ld6/a;->a:Ljava/lang/String;

    invoke-virtual {v1, v5, v4}, Ljava/util/Hashtable;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    new-instance v1, Ld6/a;

    const-string v4, "cyan"

    invoke-direct {v1, v4}, Ld6/a;-><init>(Ljava/lang/String;)V

    invoke-static {v2, v4}, Ld6/a;->b(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v4

    iput-object v4, v1, Ld6/a;->d:Ljava/lang/String;

    iget-object v4, p1, Ld6/d;->g:Ljava/util/Hashtable;

    iget-object v5, v1, Ld6/a;->a:Ljava/lang/String;

    invoke-virtual {v4, v5, v1}, Ljava/util/Hashtable;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    new-instance v4, Ld6/a;

    const-string v5, "cyanU"

    invoke-direct {v4, v5, v1}, Ld6/a;-><init>(Ljava/lang/String;Ld6/a;)V

    iput-boolean v0, v4, Ld6/a;->i:Z

    iget-object v1, p1, Ld6/d;->g:Ljava/util/Hashtable;

    iget-object v5, v4, Ld6/a;->a:Ljava/lang/String;

    invoke-virtual {v1, v5, v4}, Ljava/util/Hashtable;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    new-instance v1, Ld6/a;

    const-string v5, "cyanUI"

    invoke-direct {v1, v5, v4}, Ld6/a;-><init>(Ljava/lang/String;Ld6/a;)V

    iput-boolean v0, v1, Ld6/a;->g:Z

    iget-object v4, p1, Ld6/d;->g:Ljava/util/Hashtable;

    iget-object v5, v1, Ld6/a;->a:Ljava/lang/String;

    invoke-virtual {v4, v5, v1}, Ljava/util/Hashtable;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    new-instance v4, Ld6/a;

    const-string v5, "cyanI"

    invoke-direct {v4, v5, v1}, Ld6/a;-><init>(Ljava/lang/String;Ld6/a;)V

    iput-boolean v3, v4, Ld6/a;->i:Z

    iget-object v1, p1, Ld6/d;->g:Ljava/util/Hashtable;

    iget-object v5, v4, Ld6/a;->a:Ljava/lang/String;

    invoke-virtual {v1, v5, v4}, Ljava/util/Hashtable;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    new-instance v1, Ld6/a;

    const-string v4, "red"

    invoke-direct {v1, v4}, Ld6/a;-><init>(Ljava/lang/String;)V

    invoke-static {v2, v4}, Ld6/a;->b(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v4

    iput-object v4, v1, Ld6/a;->d:Ljava/lang/String;

    iget-object v4, p1, Ld6/d;->g:Ljava/util/Hashtable;

    iget-object v5, v1, Ld6/a;->a:Ljava/lang/String;

    invoke-virtual {v4, v5, v1}, Ljava/util/Hashtable;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    new-instance v4, Ld6/a;

    const-string v5, "redU"

    invoke-direct {v4, v5, v1}, Ld6/a;-><init>(Ljava/lang/String;Ld6/a;)V

    iput-boolean v0, v4, Ld6/a;->i:Z

    iget-object v1, p1, Ld6/d;->g:Ljava/util/Hashtable;

    iget-object v5, v4, Ld6/a;->a:Ljava/lang/String;

    invoke-virtual {v1, v5, v4}, Ljava/util/Hashtable;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    new-instance v1, Ld6/a;

    const-string v5, "redUI"

    invoke-direct {v1, v5, v4}, Ld6/a;-><init>(Ljava/lang/String;Ld6/a;)V

    iput-boolean v0, v1, Ld6/a;->g:Z

    iget-object v4, p1, Ld6/d;->g:Ljava/util/Hashtable;

    iget-object v5, v1, Ld6/a;->a:Ljava/lang/String;

    invoke-virtual {v4, v5, v1}, Ljava/util/Hashtable;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    new-instance v4, Ld6/a;

    const-string v5, "redI"

    invoke-direct {v4, v5, v1}, Ld6/a;-><init>(Ljava/lang/String;Ld6/a;)V

    iput-boolean v3, v4, Ld6/a;->i:Z

    iget-object v1, p1, Ld6/d;->g:Ljava/util/Hashtable;

    iget-object v5, v4, Ld6/a;->a:Ljava/lang/String;

    invoke-virtual {v1, v5, v4}, Ljava/util/Hashtable;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    new-instance v1, Ld6/a;

    const-string v4, "yellow"

    invoke-direct {v1, v4}, Ld6/a;-><init>(Ljava/lang/String;)V

    invoke-static {v2, v4}, Ld6/a;->b(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v4

    iput-object v4, v1, Ld6/a;->d:Ljava/lang/String;

    iget-object v4, p1, Ld6/d;->g:Ljava/util/Hashtable;

    iget-object v5, v1, Ld6/a;->a:Ljava/lang/String;

    invoke-virtual {v4, v5, v1}, Ljava/util/Hashtable;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    new-instance v4, Ld6/a;

    const-string v5, "yellowU"

    invoke-direct {v4, v5, v1}, Ld6/a;-><init>(Ljava/lang/String;Ld6/a;)V

    iput-boolean v0, v4, Ld6/a;->i:Z

    iget-object v1, p1, Ld6/d;->g:Ljava/util/Hashtable;

    iget-object v5, v4, Ld6/a;->a:Ljava/lang/String;

    invoke-virtual {v1, v5, v4}, Ljava/util/Hashtable;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    new-instance v1, Ld6/a;

    const-string v5, "yellowUI"

    invoke-direct {v1, v5, v4}, Ld6/a;-><init>(Ljava/lang/String;Ld6/a;)V

    iput-boolean v0, v1, Ld6/a;->g:Z

    iget-object v4, p1, Ld6/d;->g:Ljava/util/Hashtable;

    iget-object v5, v1, Ld6/a;->a:Ljava/lang/String;

    invoke-virtual {v4, v5, v1}, Ljava/util/Hashtable;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    new-instance v4, Ld6/a;

    const-string v5, "yellowI"

    invoke-direct {v4, v5, v1}, Ld6/a;-><init>(Ljava/lang/String;Ld6/a;)V

    iput-boolean v3, v4, Ld6/a;->i:Z

    iget-object v1, p1, Ld6/d;->g:Ljava/util/Hashtable;

    iget-object v5, v4, Ld6/a;->a:Ljava/lang/String;

    invoke-virtual {v1, v5, v4}, Ljava/util/Hashtable;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    new-instance v1, Ld6/a;

    const-string v4, "magenta"

    invoke-direct {v1, v4}, Ld6/a;-><init>(Ljava/lang/String;)V

    invoke-static {v2, v4}, Ld6/a;->b(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v4

    iput-object v4, v1, Ld6/a;->d:Ljava/lang/String;

    iget-object v4, p1, Ld6/d;->g:Ljava/util/Hashtable;

    iget-object v5, v1, Ld6/a;->a:Ljava/lang/String;

    invoke-virtual {v4, v5, v1}, Ljava/util/Hashtable;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    new-instance v4, Ld6/a;

    const-string v5, "magentaU"

    invoke-direct {v4, v5, v1}, Ld6/a;-><init>(Ljava/lang/String;Ld6/a;)V

    iput-boolean v0, v4, Ld6/a;->i:Z

    iget-object v1, p1, Ld6/d;->g:Ljava/util/Hashtable;

    iget-object v5, v4, Ld6/a;->a:Ljava/lang/String;

    invoke-virtual {v1, v5, v4}, Ljava/util/Hashtable;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    new-instance v1, Ld6/a;

    const-string v5, "magentaUI"

    invoke-direct {v1, v5, v4}, Ld6/a;-><init>(Ljava/lang/String;Ld6/a;)V

    iput-boolean v0, v1, Ld6/a;->g:Z

    iget-object v4, p1, Ld6/d;->g:Ljava/util/Hashtable;

    iget-object v5, v1, Ld6/a;->a:Ljava/lang/String;

    invoke-virtual {v4, v5, v1}, Ljava/util/Hashtable;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    new-instance v4, Ld6/a;

    const-string v5, "magentaI"

    invoke-direct {v4, v5, v1}, Ld6/a;-><init>(Ljava/lang/String;Ld6/a;)V

    iput-boolean v3, v4, Ld6/a;->i:Z

    iget-object v1, p1, Ld6/d;->g:Ljava/util/Hashtable;

    iget-object v5, v4, Ld6/a;->a:Ljava/lang/String;

    invoke-virtual {v1, v5, v4}, Ljava/util/Hashtable;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    new-instance v1, Ld6/a;

    const-string v4, "black"

    invoke-direct {v1, v4}, Ld6/a;-><init>(Ljava/lang/String;)V

    invoke-static {v2, v4}, Ld6/a;->b(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v2

    iput-object v2, v1, Ld6/a;->d:Ljava/lang/String;

    iget-object v2, p1, Ld6/d;->g:Ljava/util/Hashtable;

    iget-object v4, v1, Ld6/a;->a:Ljava/lang/String;

    invoke-virtual {v2, v4, v1}, Ljava/util/Hashtable;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    new-instance v2, Ld6/a;

    const-string v4, "blackU"

    invoke-direct {v2, v4, v1}, Ld6/a;-><init>(Ljava/lang/String;Ld6/a;)V

    iput-boolean v0, v2, Ld6/a;->i:Z

    iget-object v1, p1, Ld6/d;->g:Ljava/util/Hashtable;

    iget-object v4, v2, Ld6/a;->a:Ljava/lang/String;

    invoke-virtual {v1, v4, v2}, Ljava/util/Hashtable;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    new-instance v1, Ld6/a;

    const-string v4, "blackUI"

    invoke-direct {v1, v4, v2}, Ld6/a;-><init>(Ljava/lang/String;Ld6/a;)V

    iput-boolean v0, v1, Ld6/a;->g:Z

    iget-object v0, p1, Ld6/d;->g:Ljava/util/Hashtable;

    iget-object v2, v1, Ld6/a;->a:Ljava/lang/String;

    invoke-virtual {v0, v2, v1}, Ljava/util/Hashtable;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    new-instance v0, Ld6/a;

    const-string v2, "blackI"

    invoke-direct {v0, v2, v1}, Ld6/a;-><init>(Ljava/lang/String;Ld6/a;)V

    iput-boolean v3, v0, Ld6/a;->i:Z

    iget-object p1, p1, Ld6/d;->g:Ljava/util/Hashtable;

    iget-object v1, v0, Ld6/a;->a:Ljava/lang/String;

    invoke-virtual {p1, v1, v0}, Ljava/util/Hashtable;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    return-void
.end method

.method public b(Ljava/lang/String;Ljava/lang/String;Ljava/io/InputStream;)Ld6/d;
    .locals 27
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;,
            Lcom/avery/subtitle/exception/FatalParsingException;
        }
    .end annotation

    move-object/from16 v1, p0

    move-object/from16 v0, p3

    const-string v2, "/"

    const-string v3, "h:m:s:f/fps"

    const-string v4, ""

    const-string v5, ":"

    new-instance v6, Ld6/d;

    invoke-direct {v6}, Ld6/d;-><init>()V

    move-object/from16 v7, p1

    iput-object v7, v6, Ld6/d;->e:Ljava/lang/String;

    const/16 v7, 0x400

    new-array v8, v7, [B

    const/16 v9, 0x80

    new-array v10, v9, [B

    :try_start_0
    invoke-virtual {v1, v6}, Lc6/c;->a(Ld6/d;)V

    invoke-virtual {v0, v8}, Ljava/io/InputStream;->read([B)I

    move-result v11

    if-lt v11, v7, :cond_c

    const/4 v7, 0x2

    new-array v11, v7, [B

    const/4 v12, 0x6

    aget-byte v13, v8, v12

    const/4 v14, 0x0

    aput-byte v13, v11, v14

    const/4 v13, 0x7

    aget-byte v15, v8, v13

    const/4 v13, 0x1

    aput-byte v15, v11, v13

    new-instance v15, Ljava/lang/String;

    invoke-direct {v15, v11}, Ljava/lang/String;-><init>([B)V

    invoke-static {v15}, Ljava/lang/Integer;->parseInt(Ljava/lang/String;)I

    move-result v11

    new-array v15, v7, [B

    const/16 v16, 0xc

    aget-byte v17, v8, v16

    aput-byte v17, v15, v14

    const/16 v17, 0xd

    aget-byte v17, v8, v17

    aput-byte v17, v15, v13

    new-instance v12, Ljava/lang/String;

    invoke-direct {v12, v15}, Ljava/lang/String;-><init>([B)V

    invoke-static {v12}, Ljava/lang/Integer;->parseInt(Ljava/lang/String;)I

    move-result v12

    const/16 v15, 0x20

    new-array v9, v15, [B

    const/16 v7, 0x10

    invoke-static {v8, v7, v9, v14, v15}, Ljava/lang/System;->arraycopy(Ljava/lang/Object;ILjava/lang/Object;II)V

    new-instance v7, Ljava/lang/String;

    invoke-direct {v7, v9}, Ljava/lang/String;-><init>([B)V

    new-array v9, v15, [B

    const/16 v13, 0x30

    invoke-static {v8, v13, v9, v14, v15}, Ljava/lang/System;->arraycopy(Ljava/lang/Object;ILjava/lang/Object;II)V

    new-instance v13, Ljava/lang/String;

    invoke-direct {v13, v9}, Ljava/lang/String;-><init>([B)V

    const/4 v9, 0x5

    new-array v15, v9, [B

    const/16 v21, 0xee

    aget-byte v21, v8, v21

    aput-byte v21, v15, v14

    const/16 v21, 0xef

    aget-byte v21, v8, v21

    const/16 v20, 0x1

    aput-byte v21, v15, v20

    const/16 v21, 0xf0

    aget-byte v21, v8, v21

    const/16 v18, 0x2

    aput-byte v21, v15, v18

    const/16 v21, 0xf1

    aget-byte v21, v8, v21

    const/16 v22, 0x3

    aput-byte v21, v15, v22

    const/16 v21, 0xf2

    aget-byte v21, v8, v21

    const/4 v14, 0x4

    aput-byte v21, v15, v14

    new-instance v14, Ljava/lang/String;

    invoke-direct {v14, v15}, Ljava/lang/String;-><init>([B)V

    invoke-static {v14}, Ljava/lang/Integer;->parseInt(Ljava/lang/String;)I

    move-result v14

    new-array v15, v9, [B

    const/16 v24, 0xf3

    aget-byte v24, v8, v24

    const/16 v23, 0x0

    aput-byte v24, v15, v23

    const/16 v24, 0xf4

    aget-byte v24, v8, v24

    const/16 v20, 0x1

    aput-byte v24, v15, v20

    const/16 v24, 0xf5

    aget-byte v24, v8, v24

    const/16 v18, 0x2

    aput-byte v24, v15, v18

    const/16 v24, 0xf6

    aget-byte v24, v8, v24

    aput-byte v24, v15, v22

    const/16 v24, 0xf7

    aget-byte v8, v8, v24

    const/16 v21, 0x4

    aput-byte v8, v15, v21

    new-instance v8, Ljava/lang/String;

    invoke-direct {v8, v15}, Ljava/lang/String;-><init>([B)V

    invoke-static {v8}, Ljava/lang/Integer;->parseInt(Ljava/lang/String;)I

    move-result v8

    new-instance v15, Ljava/lang/StringBuilder;

    invoke-direct {v15}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v7}, Ljava/lang/String;->trim()Ljava/lang/String;

    move-result-object v7

    invoke-virtual {v15, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v7, " "

    invoke-virtual {v15, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v13}, Ljava/lang/String;->trim()Ljava/lang/String;

    move-result-object v7

    invoke-virtual {v15, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v15}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v7

    invoke-virtual {v7}, Ljava/lang/String;->trim()Ljava/lang/String;

    move-result-object v7

    iput-object v7, v6, Ld6/d;->a:Ljava/lang/String;

    const/4 v7, 0x4

    if-gt v12, v7, :cond_1

    if-gez v12, :cond_0

    goto :goto_0

    :cond_0
    if-eqz v12, :cond_2

    new-instance v7, Ljava/lang/StringBuilder;

    invoke-direct {v7}, Ljava/lang/StringBuilder;-><init>()V

    iget-object v12, v6, Ld6/d;->j:Ljava/lang/String;

    invoke-virtual {v7, v12}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v12, "Only latin alphabet supported for import from STL, other languages may produce unexpected results.\n\n"

    invoke-virtual {v7, v12}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v7}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v7

    iput-object v7, v6, Ld6/d;->j:Ljava/lang/String;

    goto :goto_1

    :catch_0
    move-exception v0

    goto/16 :goto_6

    :cond_1
    :goto_0
    new-instance v7, Ljava/lang/StringBuilder;

    invoke-direct {v7}, Ljava/lang/StringBuilder;-><init>()V

    iget-object v12, v6, Ld6/d;->j:Ljava/lang/String;

    invoke-virtual {v7, v12}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v12, "Invalid Character Code table number, corrupt data? will try to parse anyways assuming it is latin.\n\n"

    invoke-virtual {v7, v12}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v7}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v7

    iput-object v7, v6, Ld6/d;->j:Ljava/lang/String;

    :cond_2
    :goto_1
    const/4 v7, 0x0

    const/4 v12, 0x0

    const/4 v13, 0x0

    const/4 v15, 0x0

    :goto_2
    if-ge v12, v14, :cond_3

    invoke-virtual {v0, v10}, Ljava/io/InputStream;->read([B)I

    move-result v9

    const/16 v0, 0x80

    if-ge v9, v0, :cond_4

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    iget-object v2, v6, Ld6/d;->j:Ljava/lang/String;

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v2, "Unexpected end of file, "

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0, v12}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v2, " blocks read, expecting "

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0, v14}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v2, " blocks in total.\n\n"

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    iput-object v0, v6, Ld6/d;->j:Ljava/lang/String;

    :cond_3
    move v0, v8

    goto/16 :goto_5

    :cond_4
    if-nez v13, :cond_5

    new-instance v7, Ld6/b;

    invoke-direct {v7}, Ld6/b;-><init>()V

    :cond_5
    const/4 v9, 0x1

    aget-byte v13, v10, v9

    const/4 v9, 0x2

    aget-byte v0, v10, v9

    mul-int/lit16 v0, v0, 0x100

    add-int/2addr v13, v0

    if-eq v13, v15, :cond_6

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    iget-object v13, v6, Ld6/d;->j:Ljava/lang/String;

    invoke-virtual {v0, v13}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v13, "Unexpected subtitle number at TTI block "

    invoke-virtual {v0, v13}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0, v12}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v13, ". Parsing proceeds...\n\n"

    invoke-virtual {v0, v13}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    iput-object v0, v6, Ld6/d;->j:Ljava/lang/String;

    :cond_6
    aget-byte v0, v10, v22

    const/4 v13, -0x1

    if-eq v0, v13, :cond_7

    const/4 v13, 0x1

    goto :goto_3

    :cond_7
    const/4 v13, 0x0

    :goto_3
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v0, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const/16 v18, 0x5

    aget-byte v9, v10, v18

    invoke-virtual {v0, v9}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v0, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move/from16 p2, v14

    const/4 v9, 0x6

    aget-byte v14, v10, v9

    invoke-virtual {v0, v14}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v0, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const/4 v14, 0x7

    aget-byte v9, v10, v14

    invoke-virtual {v0, v9}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v0, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const/16 v9, 0x8

    aget-byte v9, v10, v9

    invoke-virtual {v0, v9}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    new-instance v9, Ljava/lang/StringBuilder;

    invoke-direct {v9}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v9, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const/16 v24, 0x9

    aget-byte v14, v10, v24

    invoke-virtual {v9, v14}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v9, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const/16 v14, 0xa

    aget-byte v14, v10, v14

    invoke-virtual {v9, v14}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v9, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const/16 v14, 0xb

    aget-byte v14, v10, v14

    invoke-virtual {v9, v14}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v9, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    aget-byte v14, v10, v16

    invoke-virtual {v9, v14}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v9}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v9

    const/16 v14, 0xe

    aget-byte v14, v10, v14

    const/16 v24, 0xf

    aget-byte v24, v10, v24

    if-nez v24, :cond_9

    move-object/from16 v24, v4

    const/16 v4, 0x70

    move-object/from16 v25, v5

    new-array v5, v4, [B

    move/from16 v26, v8

    move/from16 v19, v12

    const/4 v8, 0x0

    const/16 v12, 0x10

    invoke-static {v10, v12, v5, v8, v4}, Ljava/lang/System;->arraycopy(Ljava/lang/Object;ILjava/lang/Object;II)V

    if-eqz v13, :cond_8

    invoke-virtual {v1, v7, v5, v14, v6}, Lc6/c;->c(Ld6/b;[BILd6/d;)V

    goto :goto_4

    :cond_8
    new-instance v4, Ld6/c;

    new-instance v8, Ljava/lang/StringBuilder;

    invoke-direct {v8}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v8, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v8, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v8, v11}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v8}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-direct {v4, v3, v0}, Ld6/c;-><init>(Ljava/lang/String;Ljava/lang/String;)V

    iput-object v4, v7, Ld6/b;->b:Ld6/c;

    new-instance v0, Ld6/c;

    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v4, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4, v11}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v4

    invoke-direct {v0, v3, v4}, Ld6/c;-><init>(Ljava/lang/String;Ljava/lang/String;)V

    iput-object v0, v7, Ld6/b;->c:Ld6/c;

    invoke-virtual {v1, v7, v5, v14, v6}, Lc6/c;->c(Ld6/b;[BILd6/d;)V

    goto :goto_4

    :cond_9
    move-object/from16 v24, v4

    move-object/from16 v25, v5

    move/from16 v26, v8

    move/from16 v19, v12

    const/16 v12, 0x10

    :goto_4
    if-nez v13, :cond_a

    add-int/lit8 v15, v15, 0x1

    :cond_a
    add-int/lit8 v0, v19, 0x1

    move/from16 v14, p2

    move v12, v0

    move-object/from16 v4, v24

    move-object/from16 v5, v25

    move/from16 v8, v26

    const/4 v9, 0x5

    move-object/from16 v0, p3

    goto/16 :goto_2

    :goto_5
    if-eq v15, v0, :cond_b

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    iget-object v3, v6, Ld6/d;->j:Ljava/lang/String;

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v3, "Number of parsed subtitles ("

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2, v15}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v3, ") different from expected number of subtitles ("

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v0, ").\n\n"

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    iput-object v0, v6, Ld6/d;->j:Ljava/lang/String;

    :cond_b
    invoke-virtual/range {p3 .. p3}, Ljava/io/InputStream;->close()V

    invoke-virtual {v6}, Ld6/d;->a()V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    const/4 v0, 0x1

    iput-boolean v0, v6, Ld6/d;->m:Z

    return-object v6

    :cond_c
    :try_start_1
    new-instance v0, Lcom/avery/subtitle/exception/FatalParsingException;

    const-string v2, "The file must contain at least a GSI block"

    invoke-direct {v0, v2}, Lcom/avery/subtitle/exception/FatalParsingException;-><init>(Ljava/lang/String;)V

    throw v0
    :try_end_1
    .catch Ljava/lang/Exception; {:try_start_1 .. :try_end_1} :catch_0

    :goto_6
    invoke-virtual {v0}, Ljava/lang/Throwable;->printStackTrace()V

    new-instance v2, Lcom/avery/subtitle/exception/FatalParsingException;

    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v4, "Format error in the file, migth be due to corrupt data.\n"

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/Throwable;->getMessage()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-direct {v2, v0}, Lcom/avery/subtitle/exception/FatalParsingException;-><init>(Ljava/lang/String;)V

    throw v2
.end method

.method public final c(Ld6/b;[BILd6/d;)V
    .locals 16

    move-object/from16 v0, p1

    move-object/from16 v1, p2

    move/from16 v2, p3

    move-object/from16 v3, p4

    const-string v5, "white"

    const-string v6, ""

    move-object v10, v5

    move-object v8, v6

    const/4 v7, 0x0

    const/4 v9, 0x0

    const/4 v11, 0x0

    :goto_0
    array-length v12, v1

    if-ge v7, v12, :cond_e

    aget-byte v12, v1, v7

    const/4 v13, 0x1

    if-gez v12, :cond_b

    const/16 v14, -0x71

    if-gt v12, v14, :cond_1

    add-int/lit8 v15, v7, 0x1

    array-length v4, v1

    if-ge v15, v4, :cond_0

    aget-byte v4, v1, v15

    if-ne v12, v4, :cond_0

    move v7, v15

    :cond_0
    aget-byte v4, v1, v7

    const/16 v12, -0x76

    if-eq v4, v12, :cond_a

    if-eq v4, v14, :cond_2

    packed-switch v4, :pswitch_data_0

    :cond_1
    :goto_1
    const/4 v14, 0x0

    goto/16 :goto_7

    :pswitch_0
    const/4 v9, 0x0

    goto :goto_1

    :pswitch_1
    const/4 v9, 0x1

    goto :goto_1

    :pswitch_2
    const/4 v11, 0x0

    goto :goto_1

    :pswitch_3
    const/4 v11, 0x1

    goto :goto_1

    :cond_2
    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    iget-object v7, v0, Ld6/b;->d:Ljava/lang/String;

    invoke-virtual {v4, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v4

    iput-object v4, v0, Ld6/b;->d:Ljava/lang/String;

    if-eqz v9, :cond_3

    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v4, v10}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v7, "U"

    invoke-virtual {v4, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v10

    :cond_3
    if-eqz v11, :cond_4

    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v4, v10}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v7, "I"

    invoke-virtual {v4, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v10

    :cond_4
    iget-object v4, v3, Ld6/d;->g:Ljava/util/Hashtable;

    invoke-virtual {v4, v10}, Ljava/util/Hashtable;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Ld6/a;

    if-ne v2, v13, :cond_6

    new-instance v7, Ljava/lang/StringBuilder;

    invoke-direct {v7}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v7, v10}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v8, "L"

    invoke-virtual {v7, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v7}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v7

    iget-object v8, v3, Ld6/d;->g:Ljava/util/Hashtable;

    invoke-virtual {v8, v7}, Ljava/util/Hashtable;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v8

    if-nez v8, :cond_5

    new-instance v8, Ld6/a;

    invoke-direct {v8, v7, v4}, Ld6/a;-><init>(Ljava/lang/String;Ld6/a;)V

    const-string v4, "bottom-left"

    iput-object v4, v8, Ld6/a;->f:Ljava/lang/String;

    iget-object v4, v3, Ld6/d;->g:Ljava/util/Hashtable;

    invoke-virtual {v4, v7, v8}, Ljava/util/Hashtable;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    :goto_2
    move-object v10, v7

    move-object v4, v8

    goto :goto_4

    :cond_5
    iget-object v4, v3, Ld6/d;->g:Ljava/util/Hashtable;

    invoke-virtual {v4, v7}, Ljava/util/Hashtable;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Ld6/a;

    :goto_3
    move-object v10, v7

    goto :goto_4

    :cond_6
    const/4 v7, 0x3

    if-ne v2, v7, :cond_8

    new-instance v7, Ljava/lang/StringBuilder;

    invoke-direct {v7}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v7, v10}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v8, "R"

    invoke-virtual {v7, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v7}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v7

    iget-object v8, v3, Ld6/d;->g:Ljava/util/Hashtable;

    invoke-virtual {v8, v7}, Ljava/util/Hashtable;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v8

    if-nez v8, :cond_7

    new-instance v8, Ld6/a;

    invoke-direct {v8, v7, v4}, Ld6/a;-><init>(Ljava/lang/String;Ld6/a;)V

    const-string v4, "bottom-rigth"

    iput-object v4, v8, Ld6/a;->f:Ljava/lang/String;

    iget-object v4, v3, Ld6/d;->g:Ljava/util/Hashtable;

    invoke-virtual {v4, v7, v8}, Ljava/util/Hashtable;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_2

    :cond_7
    iget-object v4, v3, Ld6/d;->g:Ljava/util/Hashtable;

    invoke-virtual {v4, v7}, Ljava/util/Hashtable;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Ld6/a;

    goto :goto_3

    :cond_8
    :goto_4
    iput-object v4, v0, Ld6/b;->a:Ld6/a;

    iget-object v4, v0, Ld6/b;->b:Ld6/c;

    iget v4, v4, Ld6/c;->a:I

    :goto_5
    iget-object v7, v3, Ld6/d;->i:Ljava/util/TreeMap;

    invoke-static {v4}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v8

    invoke-virtual {v7, v8}, Ljava/util/TreeMap;->containsKey(Ljava/lang/Object;)Z

    move-result v7

    if-eqz v7, :cond_9

    add-int/lit8 v4, v4, 0x1

    goto :goto_5

    :cond_9
    iget-object v7, v3, Ld6/d;->i:Ljava/util/TreeMap;

    invoke-static {v4}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v4

    invoke-virtual {v7, v4, v0}, Ljava/util/TreeMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    array-length v7, v1

    :goto_6
    move-object v8, v6

    goto/16 :goto_1

    :cond_a
    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    iget-object v12, v0, Ld6/b;->d:Ljava/lang/String;

    invoke-virtual {v4, v12}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v8, "<br />"

    invoke-virtual {v4, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v4

    iput-object v4, v0, Ld6/b;->d:Ljava/lang/String;

    goto :goto_6

    :cond_b
    const/16 v4, 0x20

    if-ge v12, v4, :cond_d

    add-int/lit8 v4, v7, 0x1

    array-length v14, v1

    if-ge v4, v14, :cond_c

    aget-byte v14, v1, v4

    if-ne v12, v14, :cond_c

    move v7, v4

    :cond_c
    aget-byte v4, v1, v7

    packed-switch v4, :pswitch_data_1

    goto/16 :goto_1

    :pswitch_4
    move-object v10, v5

    goto/16 :goto_1

    :pswitch_5
    const-string v10, "cyan"

    goto/16 :goto_1

    :pswitch_6
    const-string v10, "magenta"

    goto/16 :goto_1

    :pswitch_7
    const-string v10, "blue"

    goto/16 :goto_1

    :pswitch_8
    const-string v10, "yellow"

    goto/16 :goto_1

    :pswitch_9
    const-string v10, "green"

    goto/16 :goto_1

    :pswitch_a
    const-string v10, "red"

    goto/16 :goto_1

    :pswitch_b
    const-string v10, "black"

    goto/16 :goto_1

    :cond_d
    new-array v4, v13, [B

    const/4 v14, 0x0

    aput-byte v12, v4, v14

    new-instance v12, Ljava/lang/StringBuilder;

    invoke-direct {v12}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v12, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    new-instance v8, Ljava/lang/String;

    invoke-direct {v8, v4}, Ljava/lang/String;-><init>([B)V

    invoke-virtual {v12, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v12}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v8

    :goto_7
    add-int/2addr v7, v13

    goto/16 :goto_0

    :cond_e
    return-void

    :pswitch_data_0
    .packed-switch -0x80
        :pswitch_3
        :pswitch_2
        :pswitch_1
        :pswitch_0
    .end packed-switch

    :pswitch_data_1
    .packed-switch 0x0
        :pswitch_b
        :pswitch_a
        :pswitch_9
        :pswitch_8
        :pswitch_7
        :pswitch_6
        :pswitch_5
        :pswitch_4
    .end packed-switch
.end method
