.class public Lcom/airbnb/lottie/LottieAnimationView$a;
.super Lm5/c;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/airbnb/lottie/LottieAnimationView;->addValueCallback(Lf5/d;Ljava/lang/Object;Lm5/e;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lm5/c<",
        "TT;>;"
    }
.end annotation


# instance fields
.field public final synthetic d:Lcom/airbnb/lottie/LottieAnimationView;


# direct methods
.method public constructor <init>(Lcom/airbnb/lottie/LottieAnimationView;Lm5/e;)V
    .locals 0

    iput-object p1, p0, Lcom/airbnb/lottie/LottieAnimationView$a;->d:Lcom/airbnb/lottie/LottieAnimationView;

    invoke-direct {p0}, Lm5/c;-><init>()V

    return-void
.end method


# virtual methods
.method public a(Lm5/b;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lm5/b<",
            "TT;>;)TT;"
        }
    .end annotation

    const/4 p1, 0x0

    throw p1
.end method
