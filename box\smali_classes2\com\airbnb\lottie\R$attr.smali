.class public final Lcom/airbnb/lottie/R$attr;
.super Ljava/lang/Object;


# static fields
.field public static lottieAnimationViewStyle:I = 0x7f0404db

.field public static lottie_asyncUpdates:I = 0x7f0404dc

.field public static lottie_autoPlay:I = 0x7f0404dd

.field public static lottie_cacheComposition:I = 0x7f0404de

.field public static lottie_clipTextToBoundingBox:I = 0x7f0404df

.field public static lottie_clipToCompositionBounds:I = 0x7f0404e0

.field public static lottie_colorFilter:I = 0x7f0404e1

.field public static lottie_defaultFontFileExtension:I = 0x7f0404e2

.field public static lottie_enableMergePathsForKitKatAndAbove:I = 0x7f0404e3

.field public static lottie_fallbackRes:I = 0x7f0404e4

.field public static lottie_fileName:I = 0x7f0404e5

.field public static lottie_ignoreDisabledSystemAnimations:I = 0x7f0404e6

.field public static lottie_imageAssetsFolder:I = 0x7f0404e7

.field public static lottie_loop:I = 0x7f0404e8

.field public static lottie_progress:I = 0x7f0404e9

.field public static lottie_rawRes:I = 0x7f0404ea

.field public static lottie_renderMode:I = 0x7f0404eb

.field public static lottie_repeatCount:I = 0x7f0404ec

.field public static lottie_repeatMode:I = 0x7f0404ed

.field public static lottie_speed:I = 0x7f0404ee

.field public static lottie_url:I = 0x7f0404ef

.field public static lottie_useCompositionFrameRate:I = 0x7f0404f0


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
