.class public final Lcom/airbnb/lottie/R$id;
.super Ljava/lang/Object;


# static fields
.field public static automatic:I = 0x7f0a00b0

.field public static disabled:I = 0x7f0a01cd

.field public static enabled:I = 0x7f0a01f6

.field public static hardware:I = 0x7f0a02ef

.field public static lottie_layer_name:I = 0x7f0a054b

.field public static restart:I = 0x7f0a078a

.field public static reverse:I = 0x7f0a078d

.field public static software:I = 0x7f0a0846


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
