.class public final Lcom/airbnb/lottie/R$styleable;
.super Ljava/lang/Object;


# static fields
.field public static LottieAnimationView:[I = null

.field public static LottieAnimationView_lottie_asyncUpdates:I = 0x0

.field public static LottieAnimationView_lottie_autoPlay:I = 0x1

.field public static LottieAnimationView_lottie_cacheComposition:I = 0x2

.field public static LottieAnimationView_lottie_clipTextToBoundingBox:I = 0x3

.field public static LottieAnimationView_lottie_clipToCompositionBounds:I = 0x4

.field public static LottieAnimationView_lottie_colorFilter:I = 0x5

.field public static LottieAnimationView_lottie_defaultFontFileExtension:I = 0x6

.field public static LottieAnimationView_lottie_enableMergePathsForKitKatAndAbove:I = 0x7

.field public static LottieAnimationView_lottie_fallbackRes:I = 0x8

.field public static LottieAnimationView_lottie_fileName:I = 0x9

.field public static LottieAnimationView_lottie_ignoreDisabledSystemAnimations:I = 0xa

.field public static LottieAnimationView_lottie_imageAssetsFolder:I = 0xb

.field public static LottieAnimationView_lottie_loop:I = 0xc

.field public static LottieAnimationView_lottie_progress:I = 0xd

.field public static LottieAnimationView_lottie_rawRes:I = 0xe

.field public static LottieAnimationView_lottie_renderMode:I = 0xf

.field public static LottieAnimationView_lottie_repeatCount:I = 0x10

.field public static LottieAnimationView_lottie_repeatMode:I = 0x11

.field public static LottieAnimationView_lottie_speed:I = 0x12

.field public static LottieAnimationView_lottie_url:I = 0x13

.field public static LottieAnimationView_lottie_useCompositionFrameRate:I = 0x14


# direct methods
.method public static constructor <clinit>()V
    .locals 1

    const/16 v0, 0x15

    new-array v0, v0, [I

    fill-array-data v0, :array_0

    sput-object v0, Lcom/airbnb/lottie/R$styleable;->LottieAnimationView:[I

    return-void

    :array_0
    .array-data 4
        0x7f0404dc
        0x7f0404dd
        0x7f0404de
        0x7f0404df
        0x7f0404e0
        0x7f0404e1
        0x7f0404e2
        0x7f0404e3
        0x7f0404e4
        0x7f0404e5
        0x7f0404e6
        0x7f0404e7
        0x7f0404e8
        0x7f0404e9
        0x7f0404ea
        0x7f0404eb
        0x7f0404ec
        0x7f0404ed
        0x7f0404ee
        0x7f0404ef
        0x7f0404f0
    .end array-data
.end method

.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
