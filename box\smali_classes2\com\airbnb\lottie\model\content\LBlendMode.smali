.class public final enum Lcom/airbnb/lottie/model/content/LBlendMode;
.super Ljava/lang/Enum;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Enum<",
        "Lcom/airbnb/lottie/model/content/LBlendMode;",
        ">;"
    }
.end annotation


# static fields
.field private static final synthetic $VALUES:[Lcom/airbnb/lottie/model/content/LBlendMode;

.field public static final enum ADD:Lcom/airbnb/lottie/model/content/LBlendMode;

.field public static final enum COLOR:Lcom/airbnb/lottie/model/content/LBlendMode;

.field public static final enum COLOR_BURN:Lcom/airbnb/lottie/model/content/LBlendMode;

.field public static final enum COLOR_DODGE:Lcom/airbnb/lottie/model/content/LBlendMode;

.field public static final enum DARKEN:Lcom/airbnb/lottie/model/content/LBlendMode;

.field public static final enum DIFFERENCE:Lcom/airbnb/lottie/model/content/LBlendMode;

.field public static final enum EXCLUSION:Lcom/airbnb/lottie/model/content/LBlendMode;

.field public static final enum HARD_LIGHT:Lcom/airbnb/lottie/model/content/LBlendMode;

.field public static final enum HARD_MIX:Lcom/airbnb/lottie/model/content/LBlendMode;

.field public static final enum HUE:Lcom/airbnb/lottie/model/content/LBlendMode;

.field public static final enum LIGHTEN:Lcom/airbnb/lottie/model/content/LBlendMode;

.field public static final enum LUMINOSITY:Lcom/airbnb/lottie/model/content/LBlendMode;

.field public static final enum MULTIPLY:Lcom/airbnb/lottie/model/content/LBlendMode;

.field public static final enum NORMAL:Lcom/airbnb/lottie/model/content/LBlendMode;

.field public static final enum OVERLAY:Lcom/airbnb/lottie/model/content/LBlendMode;

.field public static final enum SATURATION:Lcom/airbnb/lottie/model/content/LBlendMode;

.field public static final enum SCREEN:Lcom/airbnb/lottie/model/content/LBlendMode;

.field public static final enum SOFT_LIGHT:Lcom/airbnb/lottie/model/content/LBlendMode;


# direct methods
.method private static synthetic $values()[Lcom/airbnb/lottie/model/content/LBlendMode;
    .locals 3

    const/16 v0, 0x12

    new-array v0, v0, [Lcom/airbnb/lottie/model/content/LBlendMode;

    const/4 v1, 0x0

    sget-object v2, Lcom/airbnb/lottie/model/content/LBlendMode;->NORMAL:Lcom/airbnb/lottie/model/content/LBlendMode;

    aput-object v2, v0, v1

    const/4 v1, 0x1

    sget-object v2, Lcom/airbnb/lottie/model/content/LBlendMode;->MULTIPLY:Lcom/airbnb/lottie/model/content/LBlendMode;

    aput-object v2, v0, v1

    const/4 v1, 0x2

    sget-object v2, Lcom/airbnb/lottie/model/content/LBlendMode;->SCREEN:Lcom/airbnb/lottie/model/content/LBlendMode;

    aput-object v2, v0, v1

    const/4 v1, 0x3

    sget-object v2, Lcom/airbnb/lottie/model/content/LBlendMode;->OVERLAY:Lcom/airbnb/lottie/model/content/LBlendMode;

    aput-object v2, v0, v1

    const/4 v1, 0x4

    sget-object v2, Lcom/airbnb/lottie/model/content/LBlendMode;->DARKEN:Lcom/airbnb/lottie/model/content/LBlendMode;

    aput-object v2, v0, v1

    const/4 v1, 0x5

    sget-object v2, Lcom/airbnb/lottie/model/content/LBlendMode;->LIGHTEN:Lcom/airbnb/lottie/model/content/LBlendMode;

    aput-object v2, v0, v1

    const/4 v1, 0x6

    sget-object v2, Lcom/airbnb/lottie/model/content/LBlendMode;->COLOR_DODGE:Lcom/airbnb/lottie/model/content/LBlendMode;

    aput-object v2, v0, v1

    const/4 v1, 0x7

    sget-object v2, Lcom/airbnb/lottie/model/content/LBlendMode;->COLOR_BURN:Lcom/airbnb/lottie/model/content/LBlendMode;

    aput-object v2, v0, v1

    const/16 v1, 0x8

    sget-object v2, Lcom/airbnb/lottie/model/content/LBlendMode;->HARD_LIGHT:Lcom/airbnb/lottie/model/content/LBlendMode;

    aput-object v2, v0, v1

    const/16 v1, 0x9

    sget-object v2, Lcom/airbnb/lottie/model/content/LBlendMode;->SOFT_LIGHT:Lcom/airbnb/lottie/model/content/LBlendMode;

    aput-object v2, v0, v1

    const/16 v1, 0xa

    sget-object v2, Lcom/airbnb/lottie/model/content/LBlendMode;->DIFFERENCE:Lcom/airbnb/lottie/model/content/LBlendMode;

    aput-object v2, v0, v1

    const/16 v1, 0xb

    sget-object v2, Lcom/airbnb/lottie/model/content/LBlendMode;->EXCLUSION:Lcom/airbnb/lottie/model/content/LBlendMode;

    aput-object v2, v0, v1

    const/16 v1, 0xc

    sget-object v2, Lcom/airbnb/lottie/model/content/LBlendMode;->HUE:Lcom/airbnb/lottie/model/content/LBlendMode;

    aput-object v2, v0, v1

    const/16 v1, 0xd

    sget-object v2, Lcom/airbnb/lottie/model/content/LBlendMode;->SATURATION:Lcom/airbnb/lottie/model/content/LBlendMode;

    aput-object v2, v0, v1

    const/16 v1, 0xe

    sget-object v2, Lcom/airbnb/lottie/model/content/LBlendMode;->COLOR:Lcom/airbnb/lottie/model/content/LBlendMode;

    aput-object v2, v0, v1

    const/16 v1, 0xf

    sget-object v2, Lcom/airbnb/lottie/model/content/LBlendMode;->LUMINOSITY:Lcom/airbnb/lottie/model/content/LBlendMode;

    aput-object v2, v0, v1

    const/16 v1, 0x10

    sget-object v2, Lcom/airbnb/lottie/model/content/LBlendMode;->ADD:Lcom/airbnb/lottie/model/content/LBlendMode;

    aput-object v2, v0, v1

    const/16 v1, 0x11

    sget-object v2, Lcom/airbnb/lottie/model/content/LBlendMode;->HARD_MIX:Lcom/airbnb/lottie/model/content/LBlendMode;

    aput-object v2, v0, v1

    return-object v0
.end method

.method static constructor <clinit>()V
    .locals 3

    new-instance v0, Lcom/airbnb/lottie/model/content/LBlendMode;

    const-string v1, "NORMAL"

    const/4 v2, 0x0

    invoke-direct {v0, v1, v2}, Lcom/airbnb/lottie/model/content/LBlendMode;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lcom/airbnb/lottie/model/content/LBlendMode;->NORMAL:Lcom/airbnb/lottie/model/content/LBlendMode;

    new-instance v0, Lcom/airbnb/lottie/model/content/LBlendMode;

    const-string v1, "MULTIPLY"

    const/4 v2, 0x1

    invoke-direct {v0, v1, v2}, Lcom/airbnb/lottie/model/content/LBlendMode;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lcom/airbnb/lottie/model/content/LBlendMode;->MULTIPLY:Lcom/airbnb/lottie/model/content/LBlendMode;

    new-instance v0, Lcom/airbnb/lottie/model/content/LBlendMode;

    const-string v1, "SCREEN"

    const/4 v2, 0x2

    invoke-direct {v0, v1, v2}, Lcom/airbnb/lottie/model/content/LBlendMode;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lcom/airbnb/lottie/model/content/LBlendMode;->SCREEN:Lcom/airbnb/lottie/model/content/LBlendMode;

    new-instance v0, Lcom/airbnb/lottie/model/content/LBlendMode;

    const-string v1, "OVERLAY"

    const/4 v2, 0x3

    invoke-direct {v0, v1, v2}, Lcom/airbnb/lottie/model/content/LBlendMode;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lcom/airbnb/lottie/model/content/LBlendMode;->OVERLAY:Lcom/airbnb/lottie/model/content/LBlendMode;

    new-instance v0, Lcom/airbnb/lottie/model/content/LBlendMode;

    const-string v1, "DARKEN"

    const/4 v2, 0x4

    invoke-direct {v0, v1, v2}, Lcom/airbnb/lottie/model/content/LBlendMode;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lcom/airbnb/lottie/model/content/LBlendMode;->DARKEN:Lcom/airbnb/lottie/model/content/LBlendMode;

    new-instance v0, Lcom/airbnb/lottie/model/content/LBlendMode;

    const-string v1, "LIGHTEN"

    const/4 v2, 0x5

    invoke-direct {v0, v1, v2}, Lcom/airbnb/lottie/model/content/LBlendMode;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lcom/airbnb/lottie/model/content/LBlendMode;->LIGHTEN:Lcom/airbnb/lottie/model/content/LBlendMode;

    new-instance v0, Lcom/airbnb/lottie/model/content/LBlendMode;

    const-string v1, "COLOR_DODGE"

    const/4 v2, 0x6

    invoke-direct {v0, v1, v2}, Lcom/airbnb/lottie/model/content/LBlendMode;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lcom/airbnb/lottie/model/content/LBlendMode;->COLOR_DODGE:Lcom/airbnb/lottie/model/content/LBlendMode;

    new-instance v0, Lcom/airbnb/lottie/model/content/LBlendMode;

    const-string v1, "COLOR_BURN"

    const/4 v2, 0x7

    invoke-direct {v0, v1, v2}, Lcom/airbnb/lottie/model/content/LBlendMode;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lcom/airbnb/lottie/model/content/LBlendMode;->COLOR_BURN:Lcom/airbnb/lottie/model/content/LBlendMode;

    new-instance v0, Lcom/airbnb/lottie/model/content/LBlendMode;

    const-string v1, "HARD_LIGHT"

    const/16 v2, 0x8

    invoke-direct {v0, v1, v2}, Lcom/airbnb/lottie/model/content/LBlendMode;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lcom/airbnb/lottie/model/content/LBlendMode;->HARD_LIGHT:Lcom/airbnb/lottie/model/content/LBlendMode;

    new-instance v0, Lcom/airbnb/lottie/model/content/LBlendMode;

    const-string v1, "SOFT_LIGHT"

    const/16 v2, 0x9

    invoke-direct {v0, v1, v2}, Lcom/airbnb/lottie/model/content/LBlendMode;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lcom/airbnb/lottie/model/content/LBlendMode;->SOFT_LIGHT:Lcom/airbnb/lottie/model/content/LBlendMode;

    new-instance v0, Lcom/airbnb/lottie/model/content/LBlendMode;

    const-string v1, "DIFFERENCE"

    const/16 v2, 0xa

    invoke-direct {v0, v1, v2}, Lcom/airbnb/lottie/model/content/LBlendMode;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lcom/airbnb/lottie/model/content/LBlendMode;->DIFFERENCE:Lcom/airbnb/lottie/model/content/LBlendMode;

    new-instance v0, Lcom/airbnb/lottie/model/content/LBlendMode;

    const-string v1, "EXCLUSION"

    const/16 v2, 0xb

    invoke-direct {v0, v1, v2}, Lcom/airbnb/lottie/model/content/LBlendMode;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lcom/airbnb/lottie/model/content/LBlendMode;->EXCLUSION:Lcom/airbnb/lottie/model/content/LBlendMode;

    new-instance v0, Lcom/airbnb/lottie/model/content/LBlendMode;

    const-string v1, "HUE"

    const/16 v2, 0xc

    invoke-direct {v0, v1, v2}, Lcom/airbnb/lottie/model/content/LBlendMode;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lcom/airbnb/lottie/model/content/LBlendMode;->HUE:Lcom/airbnb/lottie/model/content/LBlendMode;

    new-instance v0, Lcom/airbnb/lottie/model/content/LBlendMode;

    const-string v1, "SATURATION"

    const/16 v2, 0xd

    invoke-direct {v0, v1, v2}, Lcom/airbnb/lottie/model/content/LBlendMode;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lcom/airbnb/lottie/model/content/LBlendMode;->SATURATION:Lcom/airbnb/lottie/model/content/LBlendMode;

    new-instance v0, Lcom/airbnb/lottie/model/content/LBlendMode;

    const-string v1, "COLOR"

    const/16 v2, 0xe

    invoke-direct {v0, v1, v2}, Lcom/airbnb/lottie/model/content/LBlendMode;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lcom/airbnb/lottie/model/content/LBlendMode;->COLOR:Lcom/airbnb/lottie/model/content/LBlendMode;

    new-instance v0, Lcom/airbnb/lottie/model/content/LBlendMode;

    const-string v1, "LUMINOSITY"

    const/16 v2, 0xf

    invoke-direct {v0, v1, v2}, Lcom/airbnb/lottie/model/content/LBlendMode;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lcom/airbnb/lottie/model/content/LBlendMode;->LUMINOSITY:Lcom/airbnb/lottie/model/content/LBlendMode;

    new-instance v0, Lcom/airbnb/lottie/model/content/LBlendMode;

    const-string v1, "ADD"

    const/16 v2, 0x10

    invoke-direct {v0, v1, v2}, Lcom/airbnb/lottie/model/content/LBlendMode;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lcom/airbnb/lottie/model/content/LBlendMode;->ADD:Lcom/airbnb/lottie/model/content/LBlendMode;

    new-instance v0, Lcom/airbnb/lottie/model/content/LBlendMode;

    const-string v1, "HARD_MIX"

    const/16 v2, 0x11

    invoke-direct {v0, v1, v2}, Lcom/airbnb/lottie/model/content/LBlendMode;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lcom/airbnb/lottie/model/content/LBlendMode;->HARD_MIX:Lcom/airbnb/lottie/model/content/LBlendMode;

    invoke-static {}, Lcom/airbnb/lottie/model/content/LBlendMode;->$values()[Lcom/airbnb/lottie/model/content/LBlendMode;

    move-result-object v0

    sput-object v0, Lcom/airbnb/lottie/model/content/LBlendMode;->$VALUES:[Lcom/airbnb/lottie/model/content/LBlendMode;

    return-void
.end method

.method private constructor <init>(Ljava/lang/String;I)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation

    invoke-direct {p0, p1, p2}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    return-void
.end method

.method public static valueOf(Ljava/lang/String;)Lcom/airbnb/lottie/model/content/LBlendMode;
    .locals 1

    const-class v0, Lcom/airbnb/lottie/model/content/LBlendMode;

    invoke-static {v0, p0}, Ljava/lang/Enum;->valueOf(Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Enum;

    move-result-object p0

    check-cast p0, Lcom/airbnb/lottie/model/content/LBlendMode;

    return-object p0
.end method

.method public static values()[Lcom/airbnb/lottie/model/content/LBlendMode;
    .locals 1

    sget-object v0, Lcom/airbnb/lottie/model/content/LBlendMode;->$VALUES:[Lcom/airbnb/lottie/model/content/LBlendMode;

    invoke-virtual {v0}, [Lcom/airbnb/lottie/model/content/LBlendMode;->clone()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [Lcom/airbnb/lottie/model/content/LBlendMode;

    return-object v0
.end method


# virtual methods
.method public toNativeBlendMode()Landroidx/core/graphics/BlendModeCompat;
    .locals 2
    .annotation build Landroidx/annotation/Nullable;
    .end annotation

    sget-object v0, Lcom/airbnb/lottie/model/content/LBlendMode$a;->a:[I

    invoke-virtual {p0}, Ljava/lang/Enum;->ordinal()I

    move-result v1

    aget v0, v0, v1

    packed-switch v0, :pswitch_data_0

    const/4 v0, 0x0

    return-object v0

    :pswitch_0
    sget-object v0, Landroidx/core/graphics/BlendModeCompat;->PLUS:Landroidx/core/graphics/BlendModeCompat;

    return-object v0

    :pswitch_1
    sget-object v0, Landroidx/core/graphics/BlendModeCompat;->LIGHTEN:Landroidx/core/graphics/BlendModeCompat;

    return-object v0

    :pswitch_2
    sget-object v0, Landroidx/core/graphics/BlendModeCompat;->DARKEN:Landroidx/core/graphics/BlendModeCompat;

    return-object v0

    :pswitch_3
    sget-object v0, Landroidx/core/graphics/BlendModeCompat;->OVERLAY:Landroidx/core/graphics/BlendModeCompat;

    return-object v0

    :pswitch_4
    sget-object v0, Landroidx/core/graphics/BlendModeCompat;->SCREEN:Landroidx/core/graphics/BlendModeCompat;

    return-object v0

    :pswitch_5
    sget-object v0, Landroidx/core/graphics/BlendModeCompat;->MODULATE:Landroidx/core/graphics/BlendModeCompat;

    return-object v0

    nop

    :pswitch_data_0
    .packed-switch 0x2
        :pswitch_5
        :pswitch_4
        :pswitch_3
        :pswitch_2
        :pswitch_1
        :pswitch_0
    .end packed-switch
.end method
