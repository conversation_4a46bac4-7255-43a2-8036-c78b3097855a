.class public final synthetic Lcom/airbnb/lottie/u;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/airbnb/lottie/LottieDrawable$a;


# instance fields
.field public final synthetic a:Lcom/airbnb/lottie/LottieDrawable;

.field public final synthetic b:Lf5/d;

.field public final synthetic c:Ljava/lang/Object;

.field public final synthetic d:Lm5/c;


# direct methods
.method public synthetic constructor <init>(Lcom/airbnb/lottie/LottieDrawable;Lf5/d;Ljava/lang/Object;Lm5/c;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/airbnb/lottie/u;->a:Lcom/airbnb/lottie/LottieDrawable;

    iput-object p2, p0, Lcom/airbnb/lottie/u;->b:Lf5/d;

    iput-object p3, p0, Lcom/airbnb/lottie/u;->c:Ljava/lang/Object;

    iput-object p4, p0, Lcom/airbnb/lottie/u;->d:Lm5/c;

    return-void
.end method


# virtual methods
.method public final a(Lcom/airbnb/lottie/h;)V
    .locals 4

    iget-object v0, p0, Lcom/airbnb/lottie/u;->a:Lcom/airbnb/lottie/LottieDrawable;

    iget-object v1, p0, Lcom/airbnb/lottie/u;->b:Lf5/d;

    iget-object v2, p0, Lcom/airbnb/lottie/u;->c:Ljava/lang/Object;

    iget-object v3, p0, Lcom/airbnb/lottie/u;->d:Lm5/c;

    invoke-static {v0, v1, v2, v3, p1}, Lcom/airbnb/lottie/LottieDrawable;->f(Lcom/airbnb/lottie/LottieDrawable;Lf5/d;Ljava/lang/Object;Lm5/c;Lcom/airbnb/lottie/h;)V

    return-void
.end method
