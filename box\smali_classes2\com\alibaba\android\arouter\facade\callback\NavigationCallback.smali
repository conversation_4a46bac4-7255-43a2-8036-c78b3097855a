.class public interface abstract Lcom/alibaba/android/arouter/facade/callback/NavigationCallback;
.super Ljava/lang/Object;


# virtual methods
.method public abstract onArrival(Lcom/alibaba/android/arouter/facade/Postcard;)V
.end method

.method public abstract onFound(Lcom/alibaba/android/arouter/facade/Postcard;)V
.end method

.method public abstract onInterrupt(Lcom/alibaba/android/arouter/facade/Postcard;)V
.end method

.method public abstract onLost(Lcom/alibaba/android/arouter/facade/Postcard;)V
.end method
