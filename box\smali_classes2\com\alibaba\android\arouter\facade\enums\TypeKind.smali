.class public final enum Lcom/alibaba/android/arouter/facade/enums/TypeKind;
.super Ljava/lang/Enum;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Enum<",
        "Lcom/alibaba/android/arouter/facade/enums/TypeKind;",
        ">;"
    }
.end annotation


# static fields
.field private static final synthetic $VALUES:[Lcom/alibaba/android/arouter/facade/enums/TypeKind;

.field public static final enum BOOLEAN:Lcom/alibaba/android/arouter/facade/enums/TypeKind;

.field public static final enum BYTE:Lcom/alibaba/android/arouter/facade/enums/TypeKind;

.field public static final enum CHAR:Lcom/alibaba/android/arouter/facade/enums/TypeKind;

.field public static final enum DOUBLE:Lcom/alibaba/android/arouter/facade/enums/TypeKind;

.field public static final enum FLOAT:Lcom/alibaba/android/arouter/facade/enums/TypeKind;

.field public static final enum INT:Lcom/alibaba/android/arouter/facade/enums/TypeKind;

.field public static final enum LONG:Lcom/alibaba/android/arouter/facade/enums/TypeKind;

.field public static final enum OBJECT:Lcom/alibaba/android/arouter/facade/enums/TypeKind;

.field public static final enum PARCELABLE:Lcom/alibaba/android/arouter/facade/enums/TypeKind;

.field public static final enum SERIALIZABLE:Lcom/alibaba/android/arouter/facade/enums/TypeKind;

.field public static final enum SHORT:Lcom/alibaba/android/arouter/facade/enums/TypeKind;

.field public static final enum STRING:Lcom/alibaba/android/arouter/facade/enums/TypeKind;


# direct methods
.method private static synthetic $values()[Lcom/alibaba/android/arouter/facade/enums/TypeKind;
    .locals 3

    const/16 v0, 0xc

    new-array v0, v0, [Lcom/alibaba/android/arouter/facade/enums/TypeKind;

    const/4 v1, 0x0

    sget-object v2, Lcom/alibaba/android/arouter/facade/enums/TypeKind;->BOOLEAN:Lcom/alibaba/android/arouter/facade/enums/TypeKind;

    aput-object v2, v0, v1

    const/4 v1, 0x1

    sget-object v2, Lcom/alibaba/android/arouter/facade/enums/TypeKind;->BYTE:Lcom/alibaba/android/arouter/facade/enums/TypeKind;

    aput-object v2, v0, v1

    const/4 v1, 0x2

    sget-object v2, Lcom/alibaba/android/arouter/facade/enums/TypeKind;->SHORT:Lcom/alibaba/android/arouter/facade/enums/TypeKind;

    aput-object v2, v0, v1

    const/4 v1, 0x3

    sget-object v2, Lcom/alibaba/android/arouter/facade/enums/TypeKind;->INT:Lcom/alibaba/android/arouter/facade/enums/TypeKind;

    aput-object v2, v0, v1

    const/4 v1, 0x4

    sget-object v2, Lcom/alibaba/android/arouter/facade/enums/TypeKind;->LONG:Lcom/alibaba/android/arouter/facade/enums/TypeKind;

    aput-object v2, v0, v1

    const/4 v1, 0x5

    sget-object v2, Lcom/alibaba/android/arouter/facade/enums/TypeKind;->CHAR:Lcom/alibaba/android/arouter/facade/enums/TypeKind;

    aput-object v2, v0, v1

    const/4 v1, 0x6

    sget-object v2, Lcom/alibaba/android/arouter/facade/enums/TypeKind;->FLOAT:Lcom/alibaba/android/arouter/facade/enums/TypeKind;

    aput-object v2, v0, v1

    const/4 v1, 0x7

    sget-object v2, Lcom/alibaba/android/arouter/facade/enums/TypeKind;->DOUBLE:Lcom/alibaba/android/arouter/facade/enums/TypeKind;

    aput-object v2, v0, v1

    const/16 v1, 0x8

    sget-object v2, Lcom/alibaba/android/arouter/facade/enums/TypeKind;->STRING:Lcom/alibaba/android/arouter/facade/enums/TypeKind;

    aput-object v2, v0, v1

    const/16 v1, 0x9

    sget-object v2, Lcom/alibaba/android/arouter/facade/enums/TypeKind;->SERIALIZABLE:Lcom/alibaba/android/arouter/facade/enums/TypeKind;

    aput-object v2, v0, v1

    const/16 v1, 0xa

    sget-object v2, Lcom/alibaba/android/arouter/facade/enums/TypeKind;->PARCELABLE:Lcom/alibaba/android/arouter/facade/enums/TypeKind;

    aput-object v2, v0, v1

    const/16 v1, 0xb

    sget-object v2, Lcom/alibaba/android/arouter/facade/enums/TypeKind;->OBJECT:Lcom/alibaba/android/arouter/facade/enums/TypeKind;

    aput-object v2, v0, v1

    return-object v0
.end method

.method static constructor <clinit>()V
    .locals 3

    new-instance v0, Lcom/alibaba/android/arouter/facade/enums/TypeKind;

    const-string v1, "BOOLEAN"

    const/4 v2, 0x0

    invoke-direct {v0, v1, v2}, Lcom/alibaba/android/arouter/facade/enums/TypeKind;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lcom/alibaba/android/arouter/facade/enums/TypeKind;->BOOLEAN:Lcom/alibaba/android/arouter/facade/enums/TypeKind;

    new-instance v0, Lcom/alibaba/android/arouter/facade/enums/TypeKind;

    const-string v1, "BYTE"

    const/4 v2, 0x1

    invoke-direct {v0, v1, v2}, Lcom/alibaba/android/arouter/facade/enums/TypeKind;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lcom/alibaba/android/arouter/facade/enums/TypeKind;->BYTE:Lcom/alibaba/android/arouter/facade/enums/TypeKind;

    new-instance v0, Lcom/alibaba/android/arouter/facade/enums/TypeKind;

    const-string v1, "SHORT"

    const/4 v2, 0x2

    invoke-direct {v0, v1, v2}, Lcom/alibaba/android/arouter/facade/enums/TypeKind;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lcom/alibaba/android/arouter/facade/enums/TypeKind;->SHORT:Lcom/alibaba/android/arouter/facade/enums/TypeKind;

    new-instance v0, Lcom/alibaba/android/arouter/facade/enums/TypeKind;

    const-string v1, "INT"

    const/4 v2, 0x3

    invoke-direct {v0, v1, v2}, Lcom/alibaba/android/arouter/facade/enums/TypeKind;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lcom/alibaba/android/arouter/facade/enums/TypeKind;->INT:Lcom/alibaba/android/arouter/facade/enums/TypeKind;

    new-instance v0, Lcom/alibaba/android/arouter/facade/enums/TypeKind;

    const-string v1, "LONG"

    const/4 v2, 0x4

    invoke-direct {v0, v1, v2}, Lcom/alibaba/android/arouter/facade/enums/TypeKind;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lcom/alibaba/android/arouter/facade/enums/TypeKind;->LONG:Lcom/alibaba/android/arouter/facade/enums/TypeKind;

    new-instance v0, Lcom/alibaba/android/arouter/facade/enums/TypeKind;

    const-string v1, "CHAR"

    const/4 v2, 0x5

    invoke-direct {v0, v1, v2}, Lcom/alibaba/android/arouter/facade/enums/TypeKind;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lcom/alibaba/android/arouter/facade/enums/TypeKind;->CHAR:Lcom/alibaba/android/arouter/facade/enums/TypeKind;

    new-instance v0, Lcom/alibaba/android/arouter/facade/enums/TypeKind;

    const-string v1, "FLOAT"

    const/4 v2, 0x6

    invoke-direct {v0, v1, v2}, Lcom/alibaba/android/arouter/facade/enums/TypeKind;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lcom/alibaba/android/arouter/facade/enums/TypeKind;->FLOAT:Lcom/alibaba/android/arouter/facade/enums/TypeKind;

    new-instance v0, Lcom/alibaba/android/arouter/facade/enums/TypeKind;

    const-string v1, "DOUBLE"

    const/4 v2, 0x7

    invoke-direct {v0, v1, v2}, Lcom/alibaba/android/arouter/facade/enums/TypeKind;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lcom/alibaba/android/arouter/facade/enums/TypeKind;->DOUBLE:Lcom/alibaba/android/arouter/facade/enums/TypeKind;

    new-instance v0, Lcom/alibaba/android/arouter/facade/enums/TypeKind;

    const-string v1, "STRING"

    const/16 v2, 0x8

    invoke-direct {v0, v1, v2}, Lcom/alibaba/android/arouter/facade/enums/TypeKind;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lcom/alibaba/android/arouter/facade/enums/TypeKind;->STRING:Lcom/alibaba/android/arouter/facade/enums/TypeKind;

    new-instance v0, Lcom/alibaba/android/arouter/facade/enums/TypeKind;

    const-string v1, "SERIALIZABLE"

    const/16 v2, 0x9

    invoke-direct {v0, v1, v2}, Lcom/alibaba/android/arouter/facade/enums/TypeKind;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lcom/alibaba/android/arouter/facade/enums/TypeKind;->SERIALIZABLE:Lcom/alibaba/android/arouter/facade/enums/TypeKind;

    new-instance v0, Lcom/alibaba/android/arouter/facade/enums/TypeKind;

    const-string v1, "PARCELABLE"

    const/16 v2, 0xa

    invoke-direct {v0, v1, v2}, Lcom/alibaba/android/arouter/facade/enums/TypeKind;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lcom/alibaba/android/arouter/facade/enums/TypeKind;->PARCELABLE:Lcom/alibaba/android/arouter/facade/enums/TypeKind;

    new-instance v0, Lcom/alibaba/android/arouter/facade/enums/TypeKind;

    const-string v1, "OBJECT"

    const/16 v2, 0xb

    invoke-direct {v0, v1, v2}, Lcom/alibaba/android/arouter/facade/enums/TypeKind;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lcom/alibaba/android/arouter/facade/enums/TypeKind;->OBJECT:Lcom/alibaba/android/arouter/facade/enums/TypeKind;

    invoke-static {}, Lcom/alibaba/android/arouter/facade/enums/TypeKind;->$values()[Lcom/alibaba/android/arouter/facade/enums/TypeKind;

    move-result-object v0

    sput-object v0, Lcom/alibaba/android/arouter/facade/enums/TypeKind;->$VALUES:[Lcom/alibaba/android/arouter/facade/enums/TypeKind;

    return-void
.end method

.method private constructor <init>(Ljava/lang/String;I)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation

    invoke-direct {p0, p1, p2}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    return-void
.end method

.method public static valueOf(Ljava/lang/String;)Lcom/alibaba/android/arouter/facade/enums/TypeKind;
    .locals 1

    const-class v0, Lcom/alibaba/android/arouter/facade/enums/TypeKind;

    invoke-static {v0, p0}, Ljava/lang/Enum;->valueOf(Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Enum;

    move-result-object p0

    check-cast p0, Lcom/alibaba/android/arouter/facade/enums/TypeKind;

    return-object p0
.end method

.method public static values()[Lcom/alibaba/android/arouter/facade/enums/TypeKind;
    .locals 1

    sget-object v0, Lcom/alibaba/android/arouter/facade/enums/TypeKind;->$VALUES:[Lcom/alibaba/android/arouter/facade/enums/TypeKind;

    invoke-virtual {v0}, [Lcom/alibaba/android/arouter/facade/enums/TypeKind;->clone()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [Lcom/alibaba/android/arouter/facade/enums/TypeKind;

    return-object v0
.end method
