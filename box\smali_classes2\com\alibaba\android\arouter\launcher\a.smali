.class public final Lcom/alibaba/android/arouter/launcher/a;
.super Ljava/lang/Object;


# static fields
.field public static volatile a:Lcom/alibaba/android/arouter/launcher/a;

.field public static volatile b:Z

.field public static c:Lcom/alibaba/android/arouter/facade/template/ILogger;


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static c()Z
    .locals 1

    invoke-static {}, Lcom/alibaba/android/arouter/launcher/_ARouter;->i()Z

    move-result v0

    return v0
.end method

.method public static d()Lcom/alibaba/android/arouter/launcher/a;
    .locals 2

    sget-boolean v0, Lcom/alibaba/android/arouter/launcher/a;->b:Z

    if-eqz v0, :cond_2

    sget-object v0, Lcom/alibaba/android/arouter/launcher/a;->a:Lcom/alibaba/android/arouter/launcher/a;

    if-nez v0, :cond_1

    const-class v0, Lcom/alibaba/android/arouter/launcher/a;

    monitor-enter v0

    :try_start_0
    sget-object v1, Lcom/alibaba/android/arouter/launcher/a;->a:Lcom/alibaba/android/arouter/launcher/a;

    if-nez v1, :cond_0

    new-instance v1, Lcom/alibaba/android/arouter/launcher/a;

    invoke-direct {v1}, Lcom/alibaba/android/arouter/launcher/a;-><init>()V

    sput-object v1, Lcom/alibaba/android/arouter/launcher/a;->a:Lcom/alibaba/android/arouter/launcher/a;

    goto :goto_0

    :catchall_0
    move-exception v1

    goto :goto_1

    :cond_0
    :goto_0
    monitor-exit v0

    goto :goto_2

    :goto_1
    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    throw v1

    :cond_1
    :goto_2
    sget-object v0, Lcom/alibaba/android/arouter/launcher/a;->a:Lcom/alibaba/android/arouter/launcher/a;

    return-object v0

    :cond_2
    new-instance v0, Lcom/alibaba/android/arouter/exception/InitException;

    const-string v1, "ARouter::Init::Invoke init(context) first!"

    invoke-direct {v0, v1}, Lcom/alibaba/android/arouter/exception/InitException;-><init>(Ljava/lang/String;)V

    throw v0
.end method

.method public static e(Landroid/app/Application;)V
    .locals 3

    sget-boolean v0, Lcom/alibaba/android/arouter/launcher/a;->b:Z

    if-nez v0, :cond_1

    sget-object v0, Lcom/alibaba/android/arouter/launcher/_ARouter;->a:Lcom/alibaba/android/arouter/facade/template/ILogger;

    sput-object v0, Lcom/alibaba/android/arouter/launcher/a;->c:Lcom/alibaba/android/arouter/facade/template/ILogger;

    const-string v1, "ARouter init start."

    const-string v2, "ARouter::"

    invoke-interface {v0, v2, v1}, Lcom/alibaba/android/arouter/facade/template/ILogger;->info(Ljava/lang/String;Ljava/lang/String;)V

    invoke-static {p0}, Lcom/alibaba/android/arouter/launcher/_ARouter;->l(Landroid/app/Application;)Z

    move-result p0

    sput-boolean p0, Lcom/alibaba/android/arouter/launcher/a;->b:Z

    sget-boolean p0, Lcom/alibaba/android/arouter/launcher/a;->b:Z

    if-eqz p0, :cond_0

    invoke-static {}, Lcom/alibaba/android/arouter/launcher/_ARouter;->e()V

    :cond_0
    sget-object p0, Lcom/alibaba/android/arouter/launcher/_ARouter;->a:Lcom/alibaba/android/arouter/facade/template/ILogger;

    const-string v0, "ARouter init over."

    invoke-interface {p0, v2, v0}, Lcom/alibaba/android/arouter/facade/template/ILogger;->info(Ljava/lang/String;Ljava/lang/String;)V

    :cond_1
    return-void
.end method


# virtual methods
.method public a(Landroid/net/Uri;)Lcom/alibaba/android/arouter/facade/Postcard;
    .locals 1

    invoke-static {}, Lcom/alibaba/android/arouter/launcher/_ARouter;->k()Lcom/alibaba/android/arouter/launcher/_ARouter;

    move-result-object v0

    invoke-virtual {v0, p1}, Lcom/alibaba/android/arouter/launcher/_ARouter;->f(Landroid/net/Uri;)Lcom/alibaba/android/arouter/facade/Postcard;

    move-result-object p1

    return-object p1
.end method

.method public b(Ljava/lang/String;)Lcom/alibaba/android/arouter/facade/Postcard;
    .locals 1

    invoke-static {}, Lcom/alibaba/android/arouter/launcher/_ARouter;->k()Lcom/alibaba/android/arouter/launcher/_ARouter;

    move-result-object v0

    invoke-virtual {v0, p1}, Lcom/alibaba/android/arouter/launcher/_ARouter;->g(Ljava/lang/String;)Lcom/alibaba/android/arouter/facade/Postcard;

    move-result-object p1

    return-object p1
.end method

.method public f(Ljava/lang/Object;)V
    .locals 0

    invoke-static {p1}, Lcom/alibaba/android/arouter/launcher/_ARouter;->m(Ljava/lang/Object;)V

    return-void
.end method

.method public g(Landroid/content/Context;Lcom/alibaba/android/arouter/facade/Postcard;ILcom/alibaba/android/arouter/facade/callback/NavigationCallback;)Ljava/lang/Object;
    .locals 1

    invoke-static {}, Lcom/alibaba/android/arouter/launcher/_ARouter;->k()Lcom/alibaba/android/arouter/launcher/_ARouter;

    move-result-object v0

    invoke-virtual {v0, p1, p2, p3, p4}, Lcom/alibaba/android/arouter/launcher/_ARouter;->n(Landroid/content/Context;Lcom/alibaba/android/arouter/facade/Postcard;ILcom/alibaba/android/arouter/facade/callback/NavigationCallback;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public h(Ljava/lang/Class;)Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">(",
            "Ljava/lang/Class<",
            "+TT;>;)TT;"
        }
    .end annotation

    invoke-static {}, Lcom/alibaba/android/arouter/launcher/_ARouter;->k()Lcom/alibaba/android/arouter/launcher/_ARouter;

    move-result-object v0

    invoke-virtual {v0, p1}, Lcom/alibaba/android/arouter/launcher/_ARouter;->o(Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method
