.class public Lcom/alibaba/android/arouter/routes/ARouter$$Root$$Transfer;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/alibaba/android/arouter/facade/template/IRouteRoot;


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public loadInto(Ljava/util/Map;)V
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/Class<",
            "+",
            "Lcom/alibaba/android/arouter/facade/template/IRouteGroup;",
            ">;>;)V"
        }
    .end annotation

    const-string v0, "transfer"

    const-class v1, Lcom/alibaba/android/arouter/routes/ARouter$$Group$$transfer;

    invoke-interface {p1, v0, v1}, Ljava/util/Map;->put(<PERSON><PERSON>va/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    return-void
.end method
