.class public interface abstract Lcom/alibaba/sdk/android/oss/common/OSSHeaders;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/alibaba/sdk/android/oss/common/utils/HttpHeaders;


# static fields
.field public static final ACCESS_CONTROL_ALLOW_HEADERS:Ljava/lang/String; = "Access-Control-Allow-Headers"

.field public static final ACCESS_CONTROL_ALLOW_METHODS:Ljava/lang/String; = "Access-Control-Allow-Methods"

.field public static final ACCESS_CONTROL_ALLOW_ORIGIN:Ljava/lang/String; = "Access-Control-Allow-Origin"

.field public static final ACCESS_CONTROL_EXPOSE_HEADERS:Ljava/lang/String; = "Access-Control-Expose-Headers"

.field public static final ACCESS_CONTROL_MAX_AGE:Ljava/lang/String; = "Access-Control-Max-Age"

.field public static final ACCESS_CONTROL_REQUEST_HEADER:Ljava/lang/String; = "Access-Control-Request-Headers"

.field public static final ACCESS_CONTROL_REQUEST_METHOD:Ljava/lang/String; = "Access-Control-Request-Method"

.field public static final COPY_OBJECT_METADATA_DIRECTIVE:Ljava/lang/String; = "x-oss-metadata-directive"

.field public static final COPY_OBJECT_SOURCE:Ljava/lang/String; = "x-oss-copy-source"

.field public static final COPY_OBJECT_SOURCE_IF_MATCH:Ljava/lang/String; = "x-oss-copy-source-if-match"

.field public static final COPY_OBJECT_SOURCE_IF_MODIFIED_SINCE:Ljava/lang/String; = "x-oss-copy-source-if-modified-since"

.field public static final COPY_OBJECT_SOURCE_IF_NONE_MATCH:Ljava/lang/String; = "x-oss-copy-source-if-none-match"

.field public static final COPY_OBJECT_SOURCE_IF_UNMODIFIED_SINCE:Ljava/lang/String; = "x-oss-copy-source-if-unmodified-since"

.field public static final COPY_SOURCE_RANGE:Ljava/lang/String; = "x-oss-copy-source-range"

.field public static final GET_OBJECT_IF_MATCH:Ljava/lang/String; = "If-Match"

.field public static final GET_OBJECT_IF_MODIFIED_SINCE:Ljava/lang/String; = "If-Modified-Since"

.field public static final GET_OBJECT_IF_NONE_MATCH:Ljava/lang/String; = "If-None-Match"

.field public static final GET_OBJECT_IF_UNMODIFIED_SINCE:Ljava/lang/String; = "If-Unmodified-Since"

.field public static final HEAD_OBJECT_IF_MATCH:Ljava/lang/String; = "If-Match"

.field public static final HEAD_OBJECT_IF_MODIFIED_SINCE:Ljava/lang/String; = "If-Modified-Since"

.field public static final HEAD_OBJECT_IF_NONE_MATCH:Ljava/lang/String; = "If-None-Match"

.field public static final HEAD_OBJECT_IF_UNMODIFIED_SINCE:Ljava/lang/String; = "If-Unmodified-Since"

.field public static final ORIGIN:Ljava/lang/String; = "origin"

.field public static final OSS_CANNED_ACL:Ljava/lang/String; = "x-oss-acl"

.field public static final OSS_HASH_CRC64_ECMA:Ljava/lang/String; = "x-oss-hash-crc64ecma"

.field public static final OSS_HASH_SHA1:Ljava/lang/String; = "x-oss-hash-sha1"

.field public static final OSS_HEADER_REQUEST_ID:Ljava/lang/String; = "x-oss-request-id"

.field public static final OSS_HEADER_SYMLINK_TARGET:Ljava/lang/String; = "x-oss-symlink-target"

.field public static final OSS_NEXT_APPEND_POSITION:Ljava/lang/String; = "x-oss-next-append-position"

.field public static final OSS_OBJECT_TYPE:Ljava/lang/String; = "x-oss-object-type"

.field public static final OSS_PREFIX:Ljava/lang/String; = "x-oss-"

.field public static final OSS_SECURITY_TOKEN:Ljava/lang/String; = "x-oss-security-token"

.field public static final OSS_SERVER_SIDE_ENCRYPTION:Ljava/lang/String; = "x-oss-server-side-encryption"

.field public static final OSS_USER_METADATA_PREFIX:Ljava/lang/String; = "x-oss-meta-"

.field public static final OSS_VERSION_ID:Ljava/lang/String; = "x-oss-version-id"

.field public static final STORAGE_CLASS:Ljava/lang/String; = "x-oss-storage-class"
