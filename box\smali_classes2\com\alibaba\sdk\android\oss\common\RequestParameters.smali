.class public final Lcom/alibaba/sdk/android/oss/common/RequestParameters;
.super Ljava/lang/Object;


# static fields
.field public static final DELIMITER:Ljava/lang/String; = "delimiter"

.field public static final ENCODING_TYPE:Ljava/lang/String; = "encoding-type"

.field public static final KEY_MARKER:Ljava/lang/String; = "key-marker"

.field public static final MARKER:Ljava/lang/String; = "marker"

.field public static final MAX_KEYS:Ljava/lang/String; = "max-keys"

.field public static final MAX_PARTS:Ljava/lang/String; = "max-parts"

.field public static final MAX_UPLOADS:Ljava/lang/String; = "max-uploads"

.field public static final OSS_ACCESS_KEY_ID:Ljava/lang/String; = "OSSAccessKeyId"

.field public static final PART_NUMBER:Ljava/lang/String; = "partNumber"

.field public static final PART_NUMBER_MARKER:Ljava/lang/String; = "part-number-marker"

.field public static final POSITION:Ljava/lang/String; = "position"

.field public static final PREFIX:Ljava/lang/String; = "prefix"

.field public static final RESPONSE_HEADER_CACHE_CONTROL:Ljava/lang/String; = "response-cache-control"

.field public static final RESPONSE_HEADER_CONTENT_DISPOSITION:Ljava/lang/String; = "response-content-disposition"

.field public static final RESPONSE_HEADER_CONTENT_ENCODING:Ljava/lang/String; = "response-content-encoding"

.field public static final RESPONSE_HEADER_CONTENT_LANGUAGE:Ljava/lang/String; = "response-content-language"

.field public static final RESPONSE_HEADER_CONTENT_TYPE:Ljava/lang/String; = "response-content-type"

.field public static final RESPONSE_HEADER_EXPIRES:Ljava/lang/String; = "response-expires"

.field public static final SECURITY_TOKEN:Ljava/lang/String; = "security-token"

.field public static final SIGNATURE:Ljava/lang/String; = "Signature"

.field public static final SUBRESOURCE_ACL:Ljava/lang/String; = "acl"

.field public static final SUBRESOURCE_APPEND:Ljava/lang/String; = "append"

.field public static final SUBRESOURCE_BUCKETINFO:Ljava/lang/String; = "bucketInfo"

.field public static final SUBRESOURCE_CORS:Ljava/lang/String; = "cors"

.field public static final SUBRESOURCE_DELETE:Ljava/lang/String; = "delete"

.field public static final SUBRESOURCE_LIFECYCLE:Ljava/lang/String; = "lifecycle"

.field public static final SUBRESOURCE_LOCATION:Ljava/lang/String; = "location"

.field public static final SUBRESOURCE_LOGGING:Ljava/lang/String; = "logging"

.field public static final SUBRESOURCE_REFERER:Ljava/lang/String; = "referer"

.field public static final SUBRESOURCE_SEQUENTIAL:Ljava/lang/String; = "sequential"

.field public static final SUBRESOURCE_UPLOADS:Ljava/lang/String; = "uploads"

.field public static final SUBRESOURCE_WEBSITE:Ljava/lang/String; = "website"

.field public static final UPLOAD_ID:Ljava/lang/String; = "uploadId"

.field public static final UPLOAD_ID_MARKER:Ljava/lang/String; = "upload-id-marker"

.field public static final X_OSS_PROCESS:Ljava/lang/String; = "x-oss-process"

.field public static final X_OSS_RESTORE:Ljava/lang/String; = "restore"

.field public static final X_OSS_SYMLINK:Ljava/lang/String; = "symlink"


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
