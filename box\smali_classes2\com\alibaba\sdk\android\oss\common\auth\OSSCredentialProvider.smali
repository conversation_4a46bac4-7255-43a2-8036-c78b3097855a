.class public interface abstract Lcom/alibaba/sdk/android/oss/common/auth/OSSCredentialProvider;
.super Ljava/lang/Object;


# virtual methods
.method public abstract getFederationToken()Lcom/alibaba/sdk/android/oss/common/auth/OSSFederationToken;
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lcom/alibaba/sdk/android/oss/ClientException;
        }
    .end annotation
.end method
