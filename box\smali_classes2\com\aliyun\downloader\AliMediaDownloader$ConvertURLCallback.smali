.class public interface abstract Lcom/aliyun/downloader/AliMediaDownloader$ConvertURLCallback;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/aliyun/downloader/AliMediaDownloader;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "ConvertURLCallback"
.end annotation


# virtual methods
.method public abstract convertURL(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;
.end method
