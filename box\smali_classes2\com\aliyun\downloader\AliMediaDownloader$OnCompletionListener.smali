.class public interface abstract Lcom/aliyun/downloader/AliMediaDownloader$OnCompletionListener;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/aliyun/downloader/AliMediaDownloader;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "OnCompletionListener"
.end annotation


# virtual methods
.method public abstract onCompletion()V
.end method
