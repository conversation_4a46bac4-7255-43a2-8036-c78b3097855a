.class public interface abstract Lcom/aliyun/downloader/AliMediaDownloader$OnPreparedListener;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/aliyun/downloader/AliMediaDownloader;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "OnPreparedListener"
.end annotation


# virtual methods
.method public abstract onPrepared(Lcom/aliyun/player/nativeclass/MediaInfo;)V
.end method
