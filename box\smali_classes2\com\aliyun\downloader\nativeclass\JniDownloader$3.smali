.class Lcom/aliyun/downloader/nativeclass/JniDownloader$3;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/aliyun/downloader/nativeclass/JniDownloader;->onProgress(II)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic this$0:Lcom/aliyun/downloader/nativeclass/JniDownloader;

.field final synthetic val$percent:I

.field final synthetic val$type:I


# direct methods
.method public constructor <init>(Lcom/aliyun/downloader/nativeclass/JniDownloader;II)V
    .locals 0

    iput-object p1, p0, Lcom/aliyun/downloader/nativeclass/JniDownloader$3;->this$0:Lcom/aliyun/downloader/nativeclass/JniDownloader;

    iput p2, p0, Lcom/aliyun/downloader/nativeclass/JniDownloader$3;->val$type:I

    iput p3, p0, <PERSON><PERSON>/aliyun/downloader/nativeclass/JniDownloader$3;->val$percent:I

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 2

    iget-object v0, p0, Lcom/aliyun/downloader/nativeclass/JniDownloader$3;->this$0:Lcom/aliyun/downloader/nativeclass/JniDownloader;

    invoke-static {v0}, Lcom/aliyun/downloader/nativeclass/JniDownloader;->access$300(Lcom/aliyun/downloader/nativeclass/JniDownloader;)Lcom/aliyun/downloader/AliMediaDownloader$OnProgressListener;

    move-result-object v0

    if-eqz v0, :cond_1

    iget v0, p0, Lcom/aliyun/downloader/nativeclass/JniDownloader$3;->val$type:I

    if-nez v0, :cond_0

    iget-object v0, p0, Lcom/aliyun/downloader/nativeclass/JniDownloader$3;->this$0:Lcom/aliyun/downloader/nativeclass/JniDownloader;

    invoke-static {v0}, Lcom/aliyun/downloader/nativeclass/JniDownloader;->access$300(Lcom/aliyun/downloader/nativeclass/JniDownloader;)Lcom/aliyun/downloader/AliMediaDownloader$OnProgressListener;

    move-result-object v0

    iget v1, p0, Lcom/aliyun/downloader/nativeclass/JniDownloader$3;->val$percent:I

    invoke-interface {v0, v1}, Lcom/aliyun/downloader/AliMediaDownloader$OnProgressListener;->onDownloadingProgress(I)V

    goto :goto_0

    :cond_0
    iget-object v0, p0, Lcom/aliyun/downloader/nativeclass/JniDownloader$3;->this$0:Lcom/aliyun/downloader/nativeclass/JniDownloader;

    invoke-static {v0}, Lcom/aliyun/downloader/nativeclass/JniDownloader;->access$300(Lcom/aliyun/downloader/nativeclass/JniDownloader;)Lcom/aliyun/downloader/AliMediaDownloader$OnProgressListener;

    move-result-object v0

    iget v1, p0, Lcom/aliyun/downloader/nativeclass/JniDownloader$3;->val$percent:I

    invoke-interface {v0, v1}, Lcom/aliyun/downloader/AliMediaDownloader$OnProgressListener;->onProcessingProgress(I)V

    :cond_1
    :goto_0
    return-void
.end method
