.class public interface abstract Lcom/aliyun/player/AliListPlayer;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/aliyun/player/UrlListPlayer;
.implements Lcom/aliyun/player/AliPlayer;


# virtual methods
.method public abstract addVid(Ljava/lang/String;Ljava/lang/String;)V
.end method

.method public abstract moveTo(Ljava/lang/String;Lcom/aliyun/player/source/StsInfo;)Z
.end method

.method public abstract moveToNext(Lcom/aliyun/player/source/StsInfo;)Z
.end method

.method public abstract moveToPrev(Lcom/aliyun/player/source/StsInfo;)Z
.end method

.method public abstract setDefinition(Ljava/lang/String;)V
.end method
