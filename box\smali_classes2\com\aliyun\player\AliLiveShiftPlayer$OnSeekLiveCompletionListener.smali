.class public interface abstract Lcom/aliyun/player/AliLiveShiftPlayer$OnSeekLiveCompletionListener;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/aliyun/player/AliLiveShiftPlayer;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "OnSeekLiveCompletionListener"
.end annotation


# virtual methods
.method public abstract onSeekLiveCompletion(J)V
.end method
