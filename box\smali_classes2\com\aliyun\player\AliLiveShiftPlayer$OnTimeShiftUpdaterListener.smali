.class public interface abstract Lcom/aliyun/player/AliLiveShiftPlayer$OnTimeShiftUpdaterListener;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/aliyun/player/AliLiveShiftPlayer;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "OnTimeShiftUpdaterListener"
.end annotation


# virtual methods
.method public abstract onUpdater(JJJ)V
.end method
