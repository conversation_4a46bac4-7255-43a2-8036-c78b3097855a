.class public interface abstract Lcom/aliyun/player/AliPlayerGlobalSettings$OnGetUrlHashCallback;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/aliyun/player/AliPlayerGlobalSettings;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "OnGetUrlHashCallback"
.end annotation


# virtual methods
.method public abstract getUrlHashCallback(Ljava/lang/String;)Ljava/lang/String;
.end method
