.class public final enum Lcom/aliyun/player/AliPlayerGlobalSettings$StreamType;
.super Ljava/lang/Enum;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/aliyun/player/AliPlayerGlobalSettings;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x4019
    name = "StreamType"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Enum<",
        "Lcom/aliyun/player/AliPlayerGlobalSettings$StreamType;",
        ">;"
    }
.end annotation


# static fields
.field private static final synthetic $VALUES:[Lcom/aliyun/player/AliPlayerGlobalSettings$StreamType;

.field public static final enum STREAM_ALARM:Lcom/aliyun/player/AliPlayerGlobalSettings$StreamType;

.field public static final enum STREAM_MUSIC:Lcom/aliyun/player/AliPlayerGlobalSettings$StreamType;

.field public static final enum STREAM_NOTIFICATION:Lcom/aliyun/player/AliPlayerGlobalSettings$StreamType;

.field public static final enum STREAM_RING:Lcom/aliyun/player/AliPlayerGlobalSettings$StreamType;

.field public static final enum STREAM_SYSTEM:Lcom/aliyun/player/AliPlayerGlobalSettings$StreamType;

.field public static final enum STREAM_VOICE_CALL:Lcom/aliyun/player/AliPlayerGlobalSettings$StreamType;


# direct methods
.method static constructor <clinit>()V
    .locals 13

    new-instance v0, Lcom/aliyun/player/AliPlayerGlobalSettings$StreamType;

    const-string v1, "STREAM_VOICE_CALL"

    const/4 v2, 0x0

    invoke-direct {v0, v1, v2}, Lcom/aliyun/player/AliPlayerGlobalSettings$StreamType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lcom/aliyun/player/AliPlayerGlobalSettings$StreamType;->STREAM_VOICE_CALL:Lcom/aliyun/player/AliPlayerGlobalSettings$StreamType;

    new-instance v1, Lcom/aliyun/player/AliPlayerGlobalSettings$StreamType;

    const-string v3, "STREAM_SYSTEM"

    const/4 v4, 0x1

    invoke-direct {v1, v3, v4}, Lcom/aliyun/player/AliPlayerGlobalSettings$StreamType;-><init>(Ljava/lang/String;I)V

    sput-object v1, Lcom/aliyun/player/AliPlayerGlobalSettings$StreamType;->STREAM_SYSTEM:Lcom/aliyun/player/AliPlayerGlobalSettings$StreamType;

    new-instance v3, Lcom/aliyun/player/AliPlayerGlobalSettings$StreamType;

    const-string v5, "STREAM_RING"

    const/4 v6, 0x2

    invoke-direct {v3, v5, v6}, Lcom/aliyun/player/AliPlayerGlobalSettings$StreamType;-><init>(Ljava/lang/String;I)V

    sput-object v3, Lcom/aliyun/player/AliPlayerGlobalSettings$StreamType;->STREAM_RING:Lcom/aliyun/player/AliPlayerGlobalSettings$StreamType;

    new-instance v5, Lcom/aliyun/player/AliPlayerGlobalSettings$StreamType;

    const-string v7, "STREAM_MUSIC"

    const/4 v8, 0x3

    invoke-direct {v5, v7, v8}, Lcom/aliyun/player/AliPlayerGlobalSettings$StreamType;-><init>(Ljava/lang/String;I)V

    sput-object v5, Lcom/aliyun/player/AliPlayerGlobalSettings$StreamType;->STREAM_MUSIC:Lcom/aliyun/player/AliPlayerGlobalSettings$StreamType;

    new-instance v7, Lcom/aliyun/player/AliPlayerGlobalSettings$StreamType;

    const-string v9, "STREAM_ALARM"

    const/4 v10, 0x4

    invoke-direct {v7, v9, v10}, Lcom/aliyun/player/AliPlayerGlobalSettings$StreamType;-><init>(Ljava/lang/String;I)V

    sput-object v7, Lcom/aliyun/player/AliPlayerGlobalSettings$StreamType;->STREAM_ALARM:Lcom/aliyun/player/AliPlayerGlobalSettings$StreamType;

    new-instance v9, Lcom/aliyun/player/AliPlayerGlobalSettings$StreamType;

    const-string v11, "STREAM_NOTIFICATION"

    const/4 v12, 0x5

    invoke-direct {v9, v11, v12}, Lcom/aliyun/player/AliPlayerGlobalSettings$StreamType;-><init>(Ljava/lang/String;I)V

    sput-object v9, Lcom/aliyun/player/AliPlayerGlobalSettings$StreamType;->STREAM_NOTIFICATION:Lcom/aliyun/player/AliPlayerGlobalSettings$StreamType;

    const/4 v11, 0x6

    new-array v11, v11, [Lcom/aliyun/player/AliPlayerGlobalSettings$StreamType;

    aput-object v0, v11, v2

    aput-object v1, v11, v4

    aput-object v3, v11, v6

    aput-object v5, v11, v8

    aput-object v7, v11, v10

    aput-object v9, v11, v12

    sput-object v11, Lcom/aliyun/player/AliPlayerGlobalSettings$StreamType;->$VALUES:[Lcom/aliyun/player/AliPlayerGlobalSettings$StreamType;

    return-void
.end method

.method private constructor <init>(Ljava/lang/String;I)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation

    invoke-direct {p0, p1, p2}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    return-void
.end method

.method public static valueOf(Ljava/lang/String;)Lcom/aliyun/player/AliPlayerGlobalSettings$StreamType;
    .locals 1

    const-class v0, Lcom/aliyun/player/AliPlayerGlobalSettings$StreamType;

    invoke-static {v0, p0}, Ljava/lang/Enum;->valueOf(Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Enum;

    move-result-object p0

    check-cast p0, Lcom/aliyun/player/AliPlayerGlobalSettings$StreamType;

    return-object p0
.end method

.method public static values()[Lcom/aliyun/player/AliPlayerGlobalSettings$StreamType;
    .locals 1

    sget-object v0, Lcom/aliyun/player/AliPlayerGlobalSettings$StreamType;->$VALUES:[Lcom/aliyun/player/AliPlayerGlobalSettings$StreamType;

    invoke-virtual {v0}, [Lcom/aliyun/player/AliPlayerGlobalSettings$StreamType;->clone()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [Lcom/aliyun/player/AliPlayerGlobalSettings$StreamType;

    return-object v0
.end method
