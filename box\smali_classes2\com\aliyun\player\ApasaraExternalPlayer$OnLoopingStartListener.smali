.class public interface abstract Lcom/aliyun/player/ApasaraExternalPlayer$OnLoopingStartListener;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/aliyun/player/ApasaraExternalPlayer;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "OnLoopingStartListener"
.end annotation


# virtual methods
.method public abstract onLoopingStart()V
.end method
