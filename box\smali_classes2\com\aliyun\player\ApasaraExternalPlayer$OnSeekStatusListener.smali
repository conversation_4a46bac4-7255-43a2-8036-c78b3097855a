.class public interface abstract Lcom/aliyun/player/ApasaraExternalPlayer$OnSeekStatusListener;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/aliyun/player/ApasaraExternalPlayer;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "OnSeekStatusListener"
.end annotation


# virtual methods
.method public abstract onSeekEnd(Z)V
.end method

.method public abstract onSeekStart(Z)V
.end method
