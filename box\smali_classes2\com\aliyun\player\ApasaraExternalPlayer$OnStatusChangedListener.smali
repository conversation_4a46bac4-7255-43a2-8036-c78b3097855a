.class public interface abstract Lcom/aliyun/player/ApasaraExternalPlayer$OnStatusChangedListener;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/aliyun/player/ApasaraExternalPlayer;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "OnStatusChangedListener"
.end annotation


# virtual methods
.method public abstract onStatusChanged(II)V
.end method
