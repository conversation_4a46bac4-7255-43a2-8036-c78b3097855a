.class public interface abstract Lcom/aliyun/player/ApasaraExternalPlayer$OnStreamInfoGetListener;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/aliyun/player/ApasaraExternalPlayer;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "OnStreamInfoGetListener"
.end annotation


# virtual methods
.method public abstract OnStreamInfoGet(Lcom/aliyun/player/nativeclass/MediaInfo;)V
.end method
