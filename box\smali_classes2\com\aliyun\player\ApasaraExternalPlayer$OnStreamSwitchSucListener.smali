.class public interface abstract Lcom/aliyun/player/ApasaraExternalPlayer$OnStreamSwitchSucListener;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/aliyun/player/ApasaraExternalPlayer;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "OnStreamSwitchSucListener"
.end annotation


# virtual methods
.method public abstract onStreamSwitchSuc(Lcom/aliyun/player/ApasaraExternalPlayer$StreamType;Lcom/aliyun/player/nativeclass/TrackInfo;)V
.end method
