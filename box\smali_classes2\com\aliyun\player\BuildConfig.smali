.class public final Lcom/aliyun/player/BuildConfig;
.super Ljava/lang/Object;


# static fields
.field public static final BUILD_TYPE:Ljava/lang/String; = "release"

.field public static final DEBUG:Z = false

.field public static final ENABLE_EXTERNALPLAYER:Z = true

.field public static final FLAVOR:Ljava/lang/String; = "corePlayer"

.field public static final LIBRARY_PACKAGE_NAME:Ljava/lang/String; = "com.aliyun.player"

.field public static final VERSION_CODE:I = 0x1

.field public static final VERSION_NAME:Ljava/lang/String; = "1.0"


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
