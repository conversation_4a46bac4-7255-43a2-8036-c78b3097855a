.class public interface abstract Lcom/aliyun/player/HlsKeyGenerator$OnKeyGenerateListener;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/aliyun/player/HlsKeyGenerator;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "OnKeyGenerateListener"
.end annotation


# virtual methods
.method public abstract getHlsKey(Ljava/lang/String;)[B
.end method

.method public abstract onHlsKeyInfoInit(Ljava/lang/String;I)V
.end method
