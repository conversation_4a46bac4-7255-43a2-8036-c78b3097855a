.class public interface abstract Lcom/aliyun/player/IListPlayer;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/aliyun/player/IPlayer;


# virtual methods
.method public abstract clear()V
.end method

.method public abstract getCurrentUid()Ljava/lang/String;
.end method

.method public abstract getMaxPreloadMemorySizeMB()I
.end method

.method public abstract removeSource(Ljava/lang/String;)V
.end method

.method public abstract setMaxPreloadMemorySizeMB(I)V
.end method

.method public abstract setPreloadCount(I)V
.end method
