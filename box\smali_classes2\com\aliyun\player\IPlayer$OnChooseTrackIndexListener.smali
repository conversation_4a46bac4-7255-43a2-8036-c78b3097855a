.class public interface abstract Lcom/aliyun/player/IPlayer$OnChooseTrackIndexListener;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/aliyun/player/IPlayer;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "OnChooseTrackIndexListener"
.end annotation


# virtual methods
.method public abstract onChooseTrackIndex([Lcom/aliyun/player/nativeclass/TrackInfo;)I
.end method
