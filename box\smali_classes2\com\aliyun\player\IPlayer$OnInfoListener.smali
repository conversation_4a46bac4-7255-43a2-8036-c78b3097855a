.class public interface abstract Lcom/aliyun/player/IPlayer$OnInfoListener;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/aliyun/player/IPlayer;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "OnInfoListener"
.end annotation


# virtual methods
.method public abstract onInfo(Lcom/aliyun/player/bean/InfoBean;)V
.end method
