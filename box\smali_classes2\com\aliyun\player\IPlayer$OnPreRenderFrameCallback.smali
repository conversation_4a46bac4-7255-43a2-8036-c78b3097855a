.class public interface abstract Lcom/aliyun/player/IPlayer$OnPreRenderFrameCallback;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/aliyun/player/IPlayer;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "OnPreRenderFrameCallback"
.end annotation


# virtual methods
.method public abstract onPreRenderFrame(Lcom/cicada/player/utils/FrameInfo;)Z
.end method
