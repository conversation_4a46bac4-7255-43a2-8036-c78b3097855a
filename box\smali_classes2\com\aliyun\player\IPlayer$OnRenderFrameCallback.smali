.class public interface abstract Lcom/aliyun/player/IPlayer$OnRenderFrameCallback;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/aliyun/player/IPlayer;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "OnRenderFrameCallback"
.end annotation


# virtual methods
.method public abstract onRenderFrame(Lcom/cicada/player/utils/FrameInfo;)Z
.end method
