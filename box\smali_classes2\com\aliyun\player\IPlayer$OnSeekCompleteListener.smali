.class public interface abstract Lcom/aliyun/player/IPlayer$OnSeekCompleteListener;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/aliyun/player/IPlayer;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "OnSeekCompleteListener"
.end annotation


# virtual methods
.method public abstract onSeekComplete()V
.end method
