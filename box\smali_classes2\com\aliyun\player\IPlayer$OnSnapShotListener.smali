.class public interface abstract Lcom/aliyun/player/IPlayer$OnSnapShotListener;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/aliyun/player/IPlayer;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "OnSnapShotListener"
.end annotation


# virtual methods
.method public abstract onSnapShot(Landroid/graphics/Bitmap;II)V
.end method
