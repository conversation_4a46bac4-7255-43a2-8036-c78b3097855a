.class public interface abstract Lcom/aliyun/player/IPlayer$OnTrackReadyListener;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/aliyun/player/IPlayer;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "OnTrackReadyListener"
.end annotation


# virtual methods
.method public abstract onTrackReady(Lcom/aliyun/player/nativeclass/MediaInfo;)V
.end method
