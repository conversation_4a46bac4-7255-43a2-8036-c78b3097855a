.class public interface abstract Lcom/aliyun/player/UrlListPlayer;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/aliyun/player/IListPlayer;
.implements Lcom/aliyun/player/UrlPlayer;


# virtual methods
.method public abstract addUrl(Ljava/lang/String;Ljava/lang/String;)V
.end method

.method public abstract moveTo(Ljava/lang/String;)Z
.end method

.method public abstract moveToNext()Z
.end method

.method public abstract moveToPrev()Z
.end method
