.class public final enum Lcom/aliyun/player/bean/ErrorCode;
.super Ljava/lang/Enum;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Enum<",
        "Lcom/aliyun/player/bean/ErrorCode;",
        ">;"
    }
.end annotation


# static fields
.field private static final synthetic $VALUES:[Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum DOWNLOAD_ERROR_AUTH_SOURCE_NULL:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum DOWNLOAD_ERROR_AUTH_SOURCE_WRONG:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum DOWNLOAD_ERROR_CANNOT_CREATE_SAVE_DIR:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum DOWNLOAD_ERROR_CLEAN_INVALID_PARAM:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum DOWNLOAD_ERROR_CLEAN_WRONG_STATUS:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum DOWNLOAD_ERROR_DOWNLOAD_SWITCH_OFF:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum DOWNLOAD_ERROR_ENCRYPTION_NOT_SUPPORT:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum DOWNLOAD_ERROR_ENCRYPT_ERROR:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum DOWNLOAD_ERROR_ENCRYPT_FILE_NOT_MATCH:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum DOWNLOAD_ERROR_FILE_NOT_EXIST:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum DOWNLOAD_ERROR_GET_AES_KEY_FAIL:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum DOWNLOAD_ERROR_INVALID_ITEM:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum DOWNLOAD_ERROR_NET_ERROR:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum DOWNLOAD_ERROR_NOT_SELECT_ITEM:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum DOWNLOAD_ERROR_NOT_SET_SAVE_DIR:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum DOWNLOAD_ERROR_NOT_SUPPORT_FORMAT:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum DOWNLOAD_ERROR_NO_DOWNLOAD_ITEM:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum DOWNLOAD_ERROR_NO_SPACE:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum DOWNLOAD_ERROR_STS_SOURCE_NULL:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum DOWNLOAD_ERROR_URL_CANNOT_REACH:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum DOWNLOAD_ERROR_WRITE_ERROR:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_ARTP_ARTP_MEDIA_INFO_TIMEOUT:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_ARTP_DEMUXER_UNIMPLEMENTED:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_ARTP_LOAD_FAILED:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_ARTP_MEDIA_PROBE_FAILED:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_ARTP_PACKET_RECV_TIMEOUT:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_ARTP_PLAY_TIMEOUT:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_ARTP_STREAM_FORBIDDEN:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_ARTP_STREAM_ILLEGAL:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_ARTP_STREAM_NOT_FOUND:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_ARTP_STREAM_STOPPED:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_ARTP_UNKNOWN:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_CODEC_AUDIO_NOT_SUPPORT:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_CODEC_UNKNOWN:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_CODEC_VIDEO_NOT_SUPPORT:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_DATASOURCE_EMPTYURL:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_DECODE_AUDIO:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_DECODE_BASE:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_DECODE_VIDEO:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_DEMUXER_NO_VALID_STREAM:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_DEMUXER_OPENSTREAM:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_DEMUXER_OPENURL:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_DEMUXER_START:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_GENERAL_EIO:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_GENERAL_ENOENT:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_GENERAL_EPERM:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_GENERAL_UNKNOWN:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_INERNAL_EXIT:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_INERNAL_UNKNOWN:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_KEYMANAGER_UNKNOWN:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_LOADING_TIMEOUT:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_NETWORK_CONNECT_TIMEOUT:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_NETWORK_COULD_NOT_CONNECT:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_NETWORK_HTTP_400:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_NETWORK_HTTP_403:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_NETWORK_HTTP_404:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_NETWORK_HTTP_4XX:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_NETWORK_HTTP_5XX:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_NETWORK_HTTP_RANGE:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_NETWORK_RESOLVE:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_NETWORK_UNKNOWN:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_NETWORK_UNSUPPORTED:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_NO_MATCH_QUALITY:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_NO_PLAY_INFO:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_PLAYAUTH_WRONG:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_REQUEST_FAIL:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_SERVER_LIVESHIFT_DATA_PARSER_ERROR:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_SERVER_LIVESHIFT_REQUEST_ERROR:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_SERVER_LIVESHIFT_UNKNOWN:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_SERVER_MPS_AUTH_KEY_EXIST:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_SERVER_MPS_AUTH_KEY_NOT_EXIST:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_SERVER_MPS_CDN_CONFIG_NOT_EXIST:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_SERVER_MPS_CIPHERBLOB_NOT_EXIST:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_SERVER_MPS_DRM_AUTH_ERROR:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_SERVER_MPS_DRM_NOT_ACTIVATED:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_SERVER_MPS_HTTP_REQUEST_FAILED:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_SERVER_MPS_INTERNAL_ERROR:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_SERVER_MPS_INVALID_AUTHINFO:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_SERVER_MPS_INVALID_AUTHTIMEOUT:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_SERVER_MPS_INVALID_CIPHERTEXTBLOB:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_SERVER_MPS_INVALID_END_USER_ID:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_SERVER_MPS_INVALID_FORMATS:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_SERVER_MPS_INVALID_IDENTITY_NOT_ORDER_VIDEO_SERVICE:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_SERVER_MPS_INVALID_MEDIAID:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_SERVER_MPS_INVALID_PARAMETER:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_SERVER_MPS_INVALID_PARAMETER_EMPTY_VALUE:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_SERVER_MPS_INVALID_PARAMETER_NULL_VALUE:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_SERVER_MPS_INVALID_PARAMETER_OUT_OF_BOUND:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_SERVER_MPS_INVALID_PARAMETER_RAND:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_SERVER_MPS_INVALID_SESSION_TIME:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_SERVER_MPS_INVALID_URL:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_SERVER_MPS_MEDIAID_NOT_EXIST:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_SERVER_MPS_MEDIA_NOT_ENCRYPTED:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_SERVER_MPS_MEDIA_NOT_FOUND_CIPHERTEXT:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_SERVER_MPS_MEDIA_NOT_PUBLISHED:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_SERVER_MPS_MEDIA_RESOURCE_NOT_EXIST:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_SERVER_MPS_MEDIA_RESOURCE_NOT_MATCH:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_SERVER_MPS_REDIS_POOL_IS_EMPTY:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_SERVER_MPS_REGION_NOT_SUPPORTED_API:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_SERVER_MPS_SESSION_NOT_EXIST:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_SERVER_MPS_SIGNATURE_CHECK_EXPIREDTIME_FAILED:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_SERVER_MPS_SIGNATURE_CHECK_FAILED:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_SERVER_MPS_SIGNATURE_CHECK_MEDIA_FAILED:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_SERVER_MPS_UNKNOWN:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_SERVER_MPS_UPDATE_CDN_DOMAIN_CONFIGS_FAIL:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_SERVER_MPS_XML_FORMAT_ERROR:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_SERVER_NO_RESPONSE:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_SERVER_POP_ACCESSKEYID_NOT_FOUND:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_SERVER_POP_FORBIDDEN:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_SERVER_POP_INTERNAL_ERROR:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_SERVER_POP_INVALID_PARAMETER:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_SERVER_POP_MISSING_PARAMETER:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_SERVER_POP_OPERATION_DENIED:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_SERVER_POP_OPERATION_SUSPENED:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_SERVER_POP_SECURITYTOKEN_MAILFORMED:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_SERVER_POP_SECURITYTOKEN_MISMATCH_ACCESSKEY:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_SERVER_POP_SERVICE_UNAVALIABLE:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_SERVER_POP_SIGNATUREANONCE_USED:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_SERVER_POP_SIGNATURE_NOT_MATCH:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_SERVER_POP_TOKEN_EXPIRED:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_SERVER_POP_UNKNOWN:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_SERVER_VOD_FORBIDDEN_ALIYUNVODENCRYPTION:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_SERVER_VOD_FORBIDDEN_ILLEGALSTATUS:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_SERVER_VOD_INVALIDAUTHINFO_EXPIRETIME:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_SERVER_VOD_INVALIDAUTH_MEDIAID:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_SERVER_VOD_INVALIDVIDEO_NOSTREAM:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_SERVER_VOD_INVALIDVIDEO_NOTFOUND:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_SERVER_VOD_UNKNOWN:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_SERVER_WRONG_JSON:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_TBDRM_DEMUXER_UNIMPLEMENTED:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_TBDRM_UNKNOWN:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_UNKNOWN:Lcom/aliyun/player/bean/ErrorCode;

.field public static final enum ERROR_UNKNOWN_ERROR:Lcom/aliyun/player/bean/ErrorCode;


# instance fields
.field private mCode:I


# direct methods
.method static constructor <clinit>()V
    .locals 136

    new-instance v0, Lcom/aliyun/player/bean/ErrorCode;

    const v1, 0x20010001

    const-string v2, "ERROR_SERVER_NO_RESPONSE"

    const/4 v3, 0x0

    invoke-direct {v0, v2, v3, v1}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v0, Lcom/aliyun/player/bean/ErrorCode;->ERROR_SERVER_NO_RESPONSE:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v1, Lcom/aliyun/player/bean/ErrorCode;

    const v2, 0x20010002

    const-string v4, "ERROR_SERVER_WRONG_JSON"

    const/4 v5, 0x1

    invoke-direct {v1, v4, v5, v2}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v1, Lcom/aliyun/player/bean/ErrorCode;->ERROR_SERVER_WRONG_JSON:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v2, Lcom/aliyun/player/bean/ErrorCode;

    const v4, 0x20010003

    const-string v6, "ERROR_NO_MATCH_QUALITY"

    const/4 v7, 0x2

    invoke-direct {v2, v6, v7, v4}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v2, Lcom/aliyun/player/bean/ErrorCode;->ERROR_NO_MATCH_QUALITY:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v4, Lcom/aliyun/player/bean/ErrorCode;

    const v6, 0x20010004

    const-string v8, "ERROR_PLAYAUTH_WRONG"

    const/4 v9, 0x3

    invoke-direct {v4, v8, v9, v6}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v4, Lcom/aliyun/player/bean/ErrorCode;->ERROR_PLAYAUTH_WRONG:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v6, Lcom/aliyun/player/bean/ErrorCode;

    const v8, 0x20010005

    const-string v10, "ERROR_REQUEST_FAIL"

    const/4 v11, 0x4

    invoke-direct {v6, v10, v11, v8}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v6, Lcom/aliyun/player/bean/ErrorCode;->ERROR_REQUEST_FAIL:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v8, Lcom/aliyun/player/bean/ErrorCode;

    const v10, 0x20010006

    const-string v12, "ERROR_NO_PLAY_INFO"

    const/4 v13, 0x5

    invoke-direct {v8, v12, v13, v10}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v8, Lcom/aliyun/player/bean/ErrorCode;->ERROR_NO_PLAY_INFO:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v10, Lcom/aliyun/player/bean/ErrorCode;

    const v12, 0x20010100

    const-string v14, "ERROR_SERVER_POP_UNKNOWN"

    const/4 v15, 0x6

    invoke-direct {v10, v14, v15, v12}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v10, Lcom/aliyun/player/bean/ErrorCode;->ERROR_SERVER_POP_UNKNOWN:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v12, Lcom/aliyun/player/bean/ErrorCode;

    const v14, 0x20010101

    const-string v15, "ERROR_SERVER_POP_MISSING_PARAMETER"

    const/4 v13, 0x7

    invoke-direct {v12, v15, v13, v14}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v12, Lcom/aliyun/player/bean/ErrorCode;->ERROR_SERVER_POP_MISSING_PARAMETER:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v14, Lcom/aliyun/player/bean/ErrorCode;

    const v15, 0x20010102

    const-string v13, "ERROR_SERVER_POP_INVALID_PARAMETER"

    const/16 v11, 0x8

    invoke-direct {v14, v13, v11, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v14, Lcom/aliyun/player/bean/ErrorCode;->ERROR_SERVER_POP_INVALID_PARAMETER:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v13, Lcom/aliyun/player/bean/ErrorCode;

    const v15, 0x20010103

    const-string v11, "ERROR_SERVER_POP_OPERATION_DENIED"

    const/16 v9, 0x9

    invoke-direct {v13, v11, v9, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v13, Lcom/aliyun/player/bean/ErrorCode;->ERROR_SERVER_POP_OPERATION_DENIED:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v11, Lcom/aliyun/player/bean/ErrorCode;

    const v15, 0x20010104

    const-string v9, "ERROR_SERVER_POP_OPERATION_SUSPENED"

    const/16 v7, 0xa

    invoke-direct {v11, v9, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v11, Lcom/aliyun/player/bean/ErrorCode;->ERROR_SERVER_POP_OPERATION_SUSPENED:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v9, Lcom/aliyun/player/bean/ErrorCode;

    const v15, 0x20010105

    const-string v7, "ERROR_SERVER_POP_FORBIDDEN"

    const/16 v5, 0xb

    invoke-direct {v9, v7, v5, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v9, Lcom/aliyun/player/bean/ErrorCode;->ERROR_SERVER_POP_FORBIDDEN:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v7, Lcom/aliyun/player/bean/ErrorCode;

    const v15, 0x20010106

    const-string v5, "ERROR_SERVER_POP_INTERNAL_ERROR"

    const/16 v3, 0xc

    invoke-direct {v7, v5, v3, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v7, Lcom/aliyun/player/bean/ErrorCode;->ERROR_SERVER_POP_INTERNAL_ERROR:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v5, Lcom/aliyun/player/bean/ErrorCode;

    const v15, 0x20010107

    const-string v3, "ERROR_SERVER_POP_SERVICE_UNAVALIABLE"

    move-object/from16 v16, v7

    const/16 v7, 0xd

    invoke-direct {v5, v3, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v5, Lcom/aliyun/player/bean/ErrorCode;->ERROR_SERVER_POP_SERVICE_UNAVALIABLE:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v3, Lcom/aliyun/player/bean/ErrorCode;

    const v15, 0x20010108

    const-string v7, "ERROR_SERVER_POP_SIGNATUREANONCE_USED"

    move-object/from16 v17, v5

    const/16 v5, 0xe

    invoke-direct {v3, v7, v5, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v3, Lcom/aliyun/player/bean/ErrorCode;->ERROR_SERVER_POP_SIGNATUREANONCE_USED:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v7, Lcom/aliyun/player/bean/ErrorCode;

    const v15, 0x20010109

    const-string v5, "ERROR_SERVER_POP_SECURITYTOKEN_MAILFORMED"

    move-object/from16 v18, v3

    const/16 v3, 0xf

    invoke-direct {v7, v5, v3, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v7, Lcom/aliyun/player/bean/ErrorCode;->ERROR_SERVER_POP_SECURITYTOKEN_MAILFORMED:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v5, Lcom/aliyun/player/bean/ErrorCode;

    const v15, 0x2001010a

    const-string v3, "ERROR_SERVER_POP_SECURITYTOKEN_MISMATCH_ACCESSKEY"

    move-object/from16 v19, v7

    const/16 v7, 0x10

    invoke-direct {v5, v3, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v5, Lcom/aliyun/player/bean/ErrorCode;->ERROR_SERVER_POP_SECURITYTOKEN_MISMATCH_ACCESSKEY:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v3, Lcom/aliyun/player/bean/ErrorCode;

    const v15, 0x2001010b

    const-string v7, "ERROR_SERVER_POP_SIGNATURE_NOT_MATCH"

    move-object/from16 v20, v5

    const/16 v5, 0x11

    invoke-direct {v3, v7, v5, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v3, Lcom/aliyun/player/bean/ErrorCode;->ERROR_SERVER_POP_SIGNATURE_NOT_MATCH:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v7, Lcom/aliyun/player/bean/ErrorCode;

    const v15, 0x2001010c

    const-string v5, "ERROR_SERVER_POP_ACCESSKEYID_NOT_FOUND"

    move-object/from16 v21, v3

    const/16 v3, 0x12

    invoke-direct {v7, v5, v3, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v7, Lcom/aliyun/player/bean/ErrorCode;->ERROR_SERVER_POP_ACCESSKEYID_NOT_FOUND:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v5, Lcom/aliyun/player/bean/ErrorCode;

    const v15, 0x2001010d

    const-string v3, "ERROR_SERVER_POP_TOKEN_EXPIRED"

    move-object/from16 v22, v7

    const/16 v7, 0x13

    invoke-direct {v5, v3, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v5, Lcom/aliyun/player/bean/ErrorCode;->ERROR_SERVER_POP_TOKEN_EXPIRED:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v3, Lcom/aliyun/player/bean/ErrorCode;

    const v15, 0x20010200

    const-string v7, "ERROR_SERVER_VOD_UNKNOWN"

    move-object/from16 v23, v5

    const/16 v5, 0x14

    invoke-direct {v3, v7, v5, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v3, Lcom/aliyun/player/bean/ErrorCode;->ERROR_SERVER_VOD_UNKNOWN:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v7, Lcom/aliyun/player/bean/ErrorCode;

    const v15, 0x20010201

    const-string v5, "ERROR_SERVER_VOD_FORBIDDEN_ILLEGALSTATUS"

    move-object/from16 v24, v3

    const/16 v3, 0x15

    invoke-direct {v7, v5, v3, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v7, Lcom/aliyun/player/bean/ErrorCode;->ERROR_SERVER_VOD_FORBIDDEN_ILLEGALSTATUS:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v5, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v15, 0x16

    const v3, 0x20010202

    move-object/from16 v25, v7

    const-string v7, "ERROR_SERVER_VOD_INVALIDVIDEO_NOTFOUND"

    invoke-direct {v5, v7, v15, v3}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v5, Lcom/aliyun/player/bean/ErrorCode;->ERROR_SERVER_VOD_INVALIDVIDEO_NOTFOUND:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v3, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x17

    const v15, 0x20010203

    move-object/from16 v26, v5

    const-string v5, "ERROR_SERVER_VOD_INVALIDVIDEO_NOSTREAM"

    invoke-direct {v3, v5, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v3, Lcom/aliyun/player/bean/ErrorCode;->ERROR_SERVER_VOD_INVALIDVIDEO_NOSTREAM:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v5, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x18

    const v15, 0x20010204

    move-object/from16 v27, v3

    const-string v3, "ERROR_SERVER_VOD_FORBIDDEN_ALIYUNVODENCRYPTION"

    invoke-direct {v5, v3, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v5, Lcom/aliyun/player/bean/ErrorCode;->ERROR_SERVER_VOD_FORBIDDEN_ALIYUNVODENCRYPTION:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v3, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x19

    const v15, 0x20010205

    move-object/from16 v28, v5

    const-string v5, "ERROR_SERVER_VOD_INVALIDAUTH_MEDIAID"

    invoke-direct {v3, v5, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v3, Lcom/aliyun/player/bean/ErrorCode;->ERROR_SERVER_VOD_INVALIDAUTH_MEDIAID:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v5, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x1a

    const v15, 0x20010206

    move-object/from16 v29, v3

    const-string v3, "ERROR_SERVER_VOD_INVALIDAUTHINFO_EXPIRETIME"

    invoke-direct {v5, v3, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v5, Lcom/aliyun/player/bean/ErrorCode;->ERROR_SERVER_VOD_INVALIDAUTHINFO_EXPIRETIME:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v3, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x1b

    const v15, 0x20010300

    move-object/from16 v30, v5

    const-string v5, "ERROR_SERVER_MPS_UNKNOWN"

    invoke-direct {v3, v5, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v3, Lcom/aliyun/player/bean/ErrorCode;->ERROR_SERVER_MPS_UNKNOWN:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v5, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x1c

    const v15, 0x20010301

    move-object/from16 v31, v3

    const-string v3, "ERROR_SERVER_MPS_INVALID_MEDIAID"

    invoke-direct {v5, v3, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v5, Lcom/aliyun/player/bean/ErrorCode;->ERROR_SERVER_MPS_INVALID_MEDIAID:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v3, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x1d

    const v15, 0x20010302

    move-object/from16 v32, v5

    const-string v5, "ERROR_SERVER_MPS_INVALID_AUTHTIMEOUT"

    invoke-direct {v3, v5, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v3, Lcom/aliyun/player/bean/ErrorCode;->ERROR_SERVER_MPS_INVALID_AUTHTIMEOUT:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v5, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x1e

    const v15, 0x20010303

    move-object/from16 v33, v3

    const-string v3, "ERROR_SERVER_MPS_INVALID_FORMATS"

    invoke-direct {v5, v3, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v5, Lcom/aliyun/player/bean/ErrorCode;->ERROR_SERVER_MPS_INVALID_FORMATS:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v3, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x1f

    const v15, 0x20010304

    move-object/from16 v34, v5

    const-string v5, "ERROR_SERVER_MPS_INVALID_AUTHINFO"

    invoke-direct {v3, v5, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v3, Lcom/aliyun/player/bean/ErrorCode;->ERROR_SERVER_MPS_INVALID_AUTHINFO:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v5, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x20

    const v15, 0x20010305

    move-object/from16 v35, v3

    const-string v3, "ERROR_SERVER_MPS_SIGNATURE_CHECK_FAILED"

    invoke-direct {v5, v3, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v5, Lcom/aliyun/player/bean/ErrorCode;->ERROR_SERVER_MPS_SIGNATURE_CHECK_FAILED:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v3, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x21

    const v15, 0x20010306

    move-object/from16 v36, v5

    const-string v5, "ERROR_SERVER_MPS_MEDIAID_NOT_EXIST"

    invoke-direct {v3, v5, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v3, Lcom/aliyun/player/bean/ErrorCode;->ERROR_SERVER_MPS_MEDIAID_NOT_EXIST:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v5, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x22

    const v15, 0x20010307

    move-object/from16 v37, v3

    const-string v3, "ERROR_SERVER_MPS_MEDIA_RESOURCE_NOT_EXIST"

    invoke-direct {v5, v3, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v5, Lcom/aliyun/player/bean/ErrorCode;->ERROR_SERVER_MPS_MEDIA_RESOURCE_NOT_EXIST:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v3, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x23

    const v15, 0x20010308

    move-object/from16 v38, v5

    const-string v5, "ERROR_SERVER_MPS_MEDIA_NOT_PUBLISHED"

    invoke-direct {v3, v5, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v3, Lcom/aliyun/player/bean/ErrorCode;->ERROR_SERVER_MPS_MEDIA_NOT_PUBLISHED:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v5, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x24

    const v15, 0x20010309

    move-object/from16 v39, v3

    const-string v3, "ERROR_SERVER_MPS_MEDIA_NOT_ENCRYPTED"

    invoke-direct {v5, v3, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v5, Lcom/aliyun/player/bean/ErrorCode;->ERROR_SERVER_MPS_MEDIA_NOT_ENCRYPTED:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v3, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x25

    const v15, 0x2001030a

    move-object/from16 v40, v5

    const-string v5, "ERROR_SERVER_MPS_INVALID_CIPHERTEXTBLOB"

    invoke-direct {v3, v5, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v3, Lcom/aliyun/player/bean/ErrorCode;->ERROR_SERVER_MPS_INVALID_CIPHERTEXTBLOB:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v5, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x26

    const v15, 0x2001030b

    move-object/from16 v41, v3

    const-string v3, "ERROR_SERVER_MPS_CIPHERBLOB_NOT_EXIST"

    invoke-direct {v5, v3, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v5, Lcom/aliyun/player/bean/ErrorCode;->ERROR_SERVER_MPS_CIPHERBLOB_NOT_EXIST:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v3, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x27

    const v15, 0x2001030c

    move-object/from16 v42, v5

    const-string v5, "ERROR_SERVER_MPS_INTERNAL_ERROR"

    invoke-direct {v3, v5, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v3, Lcom/aliyun/player/bean/ErrorCode;->ERROR_SERVER_MPS_INTERNAL_ERROR:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v5, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x28

    const v15, 0x2001030d

    move-object/from16 v43, v3

    const-string v3, "ERROR_SERVER_MPS_INVALID_IDENTITY_NOT_ORDER_VIDEO_SERVICE"

    invoke-direct {v5, v3, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v5, Lcom/aliyun/player/bean/ErrorCode;->ERROR_SERVER_MPS_INVALID_IDENTITY_NOT_ORDER_VIDEO_SERVICE:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v3, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x29

    const v15, 0x2001030e

    move-object/from16 v44, v5

    const-string v5, "ERROR_SERVER_MPS_UPDATE_CDN_DOMAIN_CONFIGS_FAIL"

    invoke-direct {v3, v5, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v3, Lcom/aliyun/player/bean/ErrorCode;->ERROR_SERVER_MPS_UPDATE_CDN_DOMAIN_CONFIGS_FAIL:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v5, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x2a

    const v15, 0x2001030f

    move-object/from16 v45, v3

    const-string v3, "ERROR_SERVER_MPS_AUTH_KEY_EXIST"

    invoke-direct {v5, v3, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v5, Lcom/aliyun/player/bean/ErrorCode;->ERROR_SERVER_MPS_AUTH_KEY_EXIST:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v3, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x2b

    const v15, 0x20010310

    move-object/from16 v46, v5

    const-string v5, "ERROR_SERVER_MPS_AUTH_KEY_NOT_EXIST"

    invoke-direct {v3, v5, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v3, Lcom/aliyun/player/bean/ErrorCode;->ERROR_SERVER_MPS_AUTH_KEY_NOT_EXIST:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v5, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x2c

    const v15, 0x20010311

    move-object/from16 v47, v3

    const-string v3, "ERROR_SERVER_MPS_INVALID_PARAMETER_OUT_OF_BOUND"

    invoke-direct {v5, v3, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v5, Lcom/aliyun/player/bean/ErrorCode;->ERROR_SERVER_MPS_INVALID_PARAMETER_OUT_OF_BOUND:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v3, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x2d

    const v15, 0x20010312

    move-object/from16 v48, v5

    const-string v5, "ERROR_SERVER_MPS_INVALID_PARAMETER"

    invoke-direct {v3, v5, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v3, Lcom/aliyun/player/bean/ErrorCode;->ERROR_SERVER_MPS_INVALID_PARAMETER:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v5, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x2e

    const v15, 0x20010313

    move-object/from16 v49, v3

    const-string v3, "ERROR_SERVER_MPS_INVALID_PARAMETER_NULL_VALUE"

    invoke-direct {v5, v3, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v5, Lcom/aliyun/player/bean/ErrorCode;->ERROR_SERVER_MPS_INVALID_PARAMETER_NULL_VALUE:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v3, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x2f

    const v15, 0x20010314

    move-object/from16 v50, v5

    const-string v5, "ERROR_SERVER_MPS_INVALID_PARAMETER_EMPTY_VALUE"

    invoke-direct {v3, v5, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v3, Lcom/aliyun/player/bean/ErrorCode;->ERROR_SERVER_MPS_INVALID_PARAMETER_EMPTY_VALUE:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v5, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x30

    const v15, 0x20010315

    move-object/from16 v51, v3

    const-string v3, "ERROR_SERVER_MPS_MEDIA_RESOURCE_NOT_MATCH"

    invoke-direct {v5, v3, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v5, Lcom/aliyun/player/bean/ErrorCode;->ERROR_SERVER_MPS_MEDIA_RESOURCE_NOT_MATCH:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v3, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x31

    const v15, 0x20010316

    move-object/from16 v52, v5

    const-string v5, "ERROR_SERVER_MPS_MEDIA_NOT_FOUND_CIPHERTEXT"

    invoke-direct {v3, v5, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v3, Lcom/aliyun/player/bean/ErrorCode;->ERROR_SERVER_MPS_MEDIA_NOT_FOUND_CIPHERTEXT:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v5, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x32

    const v15, 0x20010317

    move-object/from16 v53, v3

    const-string v3, "ERROR_SERVER_MPS_INVALID_PARAMETER_RAND"

    invoke-direct {v5, v3, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v5, Lcom/aliyun/player/bean/ErrorCode;->ERROR_SERVER_MPS_INVALID_PARAMETER_RAND:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v3, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x33

    const v15, 0x20010318

    move-object/from16 v54, v5

    const-string v5, "ERROR_SERVER_MPS_REDIS_POOL_IS_EMPTY"

    invoke-direct {v3, v5, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v3, Lcom/aliyun/player/bean/ErrorCode;->ERROR_SERVER_MPS_REDIS_POOL_IS_EMPTY:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v5, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x34

    const v15, 0x20010319

    move-object/from16 v55, v3

    const-string v3, "ERROR_SERVER_MPS_SIGNATURE_CHECK_MEDIA_FAILED"

    invoke-direct {v5, v3, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v5, Lcom/aliyun/player/bean/ErrorCode;->ERROR_SERVER_MPS_SIGNATURE_CHECK_MEDIA_FAILED:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v3, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x35

    const v15, 0x2001031a

    move-object/from16 v56, v5

    const-string v5, "ERROR_SERVER_MPS_SIGNATURE_CHECK_EXPIREDTIME_FAILED"

    invoke-direct {v3, v5, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v3, Lcom/aliyun/player/bean/ErrorCode;->ERROR_SERVER_MPS_SIGNATURE_CHECK_EXPIREDTIME_FAILED:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v5, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x36

    const v15, 0x2001031b

    move-object/from16 v57, v3

    const-string v3, "ERROR_SERVER_MPS_INVALID_SESSION_TIME"

    invoke-direct {v5, v3, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v5, Lcom/aliyun/player/bean/ErrorCode;->ERROR_SERVER_MPS_INVALID_SESSION_TIME:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v3, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x37

    const v15, 0x2001031c

    move-object/from16 v58, v5

    const-string v5, "ERROR_SERVER_MPS_INVALID_END_USER_ID"

    invoke-direct {v3, v5, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v3, Lcom/aliyun/player/bean/ErrorCode;->ERROR_SERVER_MPS_INVALID_END_USER_ID:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v5, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x38

    const v15, 0x2001031d

    move-object/from16 v59, v3

    const-string v3, "ERROR_SERVER_MPS_INVALID_URL"

    invoke-direct {v5, v3, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v5, Lcom/aliyun/player/bean/ErrorCode;->ERROR_SERVER_MPS_INVALID_URL:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v3, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x39

    const v15, 0x2001031e

    move-object/from16 v60, v5

    const-string v5, "ERROR_SERVER_MPS_HTTP_REQUEST_FAILED"

    invoke-direct {v3, v5, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v3, Lcom/aliyun/player/bean/ErrorCode;->ERROR_SERVER_MPS_HTTP_REQUEST_FAILED:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v5, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x3a

    const v15, 0x2001031f

    move-object/from16 v61, v3

    const-string v3, "ERROR_SERVER_MPS_XML_FORMAT_ERROR"

    invoke-direct {v5, v3, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v5, Lcom/aliyun/player/bean/ErrorCode;->ERROR_SERVER_MPS_XML_FORMAT_ERROR:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v3, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x3b

    const v15, 0x20010320

    move-object/from16 v62, v5

    const-string v5, "ERROR_SERVER_MPS_SESSION_NOT_EXIST"

    invoke-direct {v3, v5, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v3, Lcom/aliyun/player/bean/ErrorCode;->ERROR_SERVER_MPS_SESSION_NOT_EXIST:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v5, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x3c

    const v15, 0x20010321

    move-object/from16 v63, v3

    const-string v3, "ERROR_SERVER_MPS_REGION_NOT_SUPPORTED_API"

    invoke-direct {v5, v3, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v5, Lcom/aliyun/player/bean/ErrorCode;->ERROR_SERVER_MPS_REGION_NOT_SUPPORTED_API:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v3, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x3d

    const v15, 0x20010322

    move-object/from16 v64, v5

    const-string v5, "ERROR_SERVER_MPS_DRM_NOT_ACTIVATED"

    invoke-direct {v3, v5, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v3, Lcom/aliyun/player/bean/ErrorCode;->ERROR_SERVER_MPS_DRM_NOT_ACTIVATED:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v5, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x3e

    const v15, 0x20010323

    move-object/from16 v65, v3

    const-string v3, "ERROR_SERVER_MPS_DRM_AUTH_ERROR"

    invoke-direct {v5, v3, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v5, Lcom/aliyun/player/bean/ErrorCode;->ERROR_SERVER_MPS_DRM_AUTH_ERROR:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v3, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x3f

    const v15, 0x20010324

    move-object/from16 v66, v5

    const-string v5, "ERROR_SERVER_MPS_CDN_CONFIG_NOT_EXIST"

    invoke-direct {v3, v5, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v3, Lcom/aliyun/player/bean/ErrorCode;->ERROR_SERVER_MPS_CDN_CONFIG_NOT_EXIST:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v5, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x40

    const v15, 0x20010400

    move-object/from16 v67, v3

    const-string v3, "ERROR_SERVER_LIVESHIFT_UNKNOWN"

    invoke-direct {v5, v3, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v5, Lcom/aliyun/player/bean/ErrorCode;->ERROR_SERVER_LIVESHIFT_UNKNOWN:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v3, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x41

    const v15, 0x20010401

    move-object/from16 v68, v5

    const-string v5, "ERROR_SERVER_LIVESHIFT_REQUEST_ERROR"

    invoke-direct {v3, v5, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v3, Lcom/aliyun/player/bean/ErrorCode;->ERROR_SERVER_LIVESHIFT_REQUEST_ERROR:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v5, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x42

    const v15, 0x20010402

    move-object/from16 v69, v3

    const-string v3, "ERROR_SERVER_LIVESHIFT_DATA_PARSER_ERROR"

    invoke-direct {v5, v3, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v5, Lcom/aliyun/player/bean/ErrorCode;->ERROR_SERVER_LIVESHIFT_DATA_PARSER_ERROR:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v3, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x43

    const v15, 0x20011000

    move-object/from16 v70, v5

    const-string v5, "ERROR_KEYMANAGER_UNKNOWN"

    invoke-direct {v3, v5, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v3, Lcom/aliyun/player/bean/ErrorCode;->ERROR_KEYMANAGER_UNKNOWN:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v5, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x44

    const v15, 0x20012000

    move-object/from16 v71, v3

    const-string v3, "ERROR_TBDRM_UNKNOWN"

    invoke-direct {v5, v3, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v5, Lcom/aliyun/player/bean/ErrorCode;->ERROR_TBDRM_UNKNOWN:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v3, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x45

    const v15, 0x20012001

    move-object/from16 v72, v5

    const-string v5, "ERROR_TBDRM_DEMUXER_UNIMPLEMENTED"

    invoke-direct {v3, v5, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v3, Lcom/aliyun/player/bean/ErrorCode;->ERROR_TBDRM_DEMUXER_UNIMPLEMENTED:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v5, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x46

    const v15, 0x20013000

    move-object/from16 v73, v3

    const-string v3, "ERROR_ARTP_UNKNOWN"

    invoke-direct {v5, v3, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v5, Lcom/aliyun/player/bean/ErrorCode;->ERROR_ARTP_UNKNOWN:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v3, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x47

    const v15, 0x20013001

    move-object/from16 v74, v5

    const-string v5, "ERROR_ARTP_DEMUXER_UNIMPLEMENTED"

    invoke-direct {v3, v5, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v3, Lcom/aliyun/player/bean/ErrorCode;->ERROR_ARTP_DEMUXER_UNIMPLEMENTED:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v5, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x48

    const v15, 0x20013002

    move-object/from16 v75, v3

    const-string v3, "ERROR_ARTP_LOAD_FAILED"

    invoke-direct {v5, v3, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v5, Lcom/aliyun/player/bean/ErrorCode;->ERROR_ARTP_LOAD_FAILED:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v3, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x49

    const v15, 0x20013003

    move-object/from16 v76, v5

    const-string v5, "ERROR_ARTP_STREAM_ILLEGAL"

    invoke-direct {v3, v5, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v3, Lcom/aliyun/player/bean/ErrorCode;->ERROR_ARTP_STREAM_ILLEGAL:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v5, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x4a

    const v15, 0x20013004

    move-object/from16 v77, v3

    const-string v3, "ERROR_ARTP_STREAM_FORBIDDEN"

    invoke-direct {v5, v3, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v5, Lcom/aliyun/player/bean/ErrorCode;->ERROR_ARTP_STREAM_FORBIDDEN:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v3, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x4b

    const v15, 0x20013005

    move-object/from16 v78, v5

    const-string v5, "ERROR_ARTP_STREAM_NOT_FOUND"

    invoke-direct {v3, v5, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v3, Lcom/aliyun/player/bean/ErrorCode;->ERROR_ARTP_STREAM_NOT_FOUND:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v5, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x4c

    const v15, 0x20013006

    move-object/from16 v79, v3

    const-string v3, "ERROR_ARTP_STREAM_STOPPED"

    invoke-direct {v5, v3, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v5, Lcom/aliyun/player/bean/ErrorCode;->ERROR_ARTP_STREAM_STOPPED:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v3, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x4d

    const v15, 0x20013007

    move-object/from16 v80, v5

    const-string v5, "ERROR_ARTP_PLAY_TIMEOUT"

    invoke-direct {v3, v5, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v3, Lcom/aliyun/player/bean/ErrorCode;->ERROR_ARTP_PLAY_TIMEOUT:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v5, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x4e

    const v15, 0x20013008

    move-object/from16 v81, v3

    const-string v3, "ERROR_ARTP_ARTP_MEDIA_INFO_TIMEOUT"

    invoke-direct {v5, v3, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v5, Lcom/aliyun/player/bean/ErrorCode;->ERROR_ARTP_ARTP_MEDIA_INFO_TIMEOUT:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v3, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x4f

    const v15, 0x20013009

    move-object/from16 v82, v5

    const-string v5, "ERROR_ARTP_PACKET_RECV_TIMEOUT"

    invoke-direct {v3, v5, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v3, Lcom/aliyun/player/bean/ErrorCode;->ERROR_ARTP_PACKET_RECV_TIMEOUT:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v5, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x50

    const v15, 0x2001300a

    move-object/from16 v83, v3

    const-string v3, "ERROR_ARTP_MEDIA_PROBE_FAILED"

    invoke-direct {v5, v3, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v5, Lcom/aliyun/player/bean/ErrorCode;->ERROR_ARTP_MEDIA_PROBE_FAILED:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v3, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x51

    const v15, 0x2001ffff

    move-object/from16 v84, v5

    const-string v5, "ERROR_UNKNOWN_ERROR"

    invoke-direct {v3, v5, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v3, Lcom/aliyun/player/bean/ErrorCode;->ERROR_UNKNOWN_ERROR:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v5, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x52

    const/high16 v15, 0x20030000

    move-object/from16 v85, v3

    const-string v3, "ERROR_DEMUXER_START"

    invoke-direct {v5, v3, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v5, Lcom/aliyun/player/bean/ErrorCode;->ERROR_DEMUXER_START:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v3, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x53

    const v15, 0x20030001

    move-object/from16 v86, v5

    const-string v5, "ERROR_DEMUXER_OPENURL"

    invoke-direct {v3, v5, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v3, Lcom/aliyun/player/bean/ErrorCode;->ERROR_DEMUXER_OPENURL:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v5, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x54

    const v15, 0x20030002

    move-object/from16 v87, v3

    const-string v3, "ERROR_DEMUXER_NO_VALID_STREAM"

    invoke-direct {v5, v3, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v5, Lcom/aliyun/player/bean/ErrorCode;->ERROR_DEMUXER_NO_VALID_STREAM:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v3, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x55

    const v15, 0x20030003

    move-object/from16 v88, v5

    const-string v5, "ERROR_DEMUXER_OPENSTREAM"

    invoke-direct {v3, v5, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v3, Lcom/aliyun/player/bean/ErrorCode;->ERROR_DEMUXER_OPENSTREAM:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v5, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x56

    const v15, 0x20030004

    move-object/from16 v89, v3

    const-string v3, "ERROR_LOADING_TIMEOUT"

    invoke-direct {v5, v3, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v5, Lcom/aliyun/player/bean/ErrorCode;->ERROR_LOADING_TIMEOUT:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v3, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x57

    const v15, 0x20030005

    move-object/from16 v90, v5

    const-string v5, "ERROR_DATASOURCE_EMPTYURL"

    invoke-direct {v3, v5, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v3, Lcom/aliyun/player/bean/ErrorCode;->ERROR_DATASOURCE_EMPTYURL:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v5, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x58

    const/high16 v15, 0x20040000

    move-object/from16 v91, v3

    const-string v3, "ERROR_DECODE_BASE"

    invoke-direct {v5, v3, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v5, Lcom/aliyun/player/bean/ErrorCode;->ERROR_DECODE_BASE:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v3, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x59

    const v15, 0x20040001

    move-object/from16 v92, v5

    const-string v5, "ERROR_DECODE_VIDEO"

    invoke-direct {v3, v5, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v3, Lcom/aliyun/player/bean/ErrorCode;->ERROR_DECODE_VIDEO:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v5, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x5a

    const v15, 0x20040002

    move-object/from16 v93, v3

    const-string v3, "ERROR_DECODE_AUDIO"

    invoke-direct {v5, v3, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v5, Lcom/aliyun/player/bean/ErrorCode;->ERROR_DECODE_AUDIO:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v3, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x5b

    const/high16 v15, 0x20050000

    move-object/from16 v94, v5

    const-string v5, "ERROR_NETWORK_UNKNOWN"

    invoke-direct {v3, v5, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v3, Lcom/aliyun/player/bean/ErrorCode;->ERROR_NETWORK_UNKNOWN:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v5, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x5c

    const v15, 0x20050001

    move-object/from16 v95, v3

    const-string v3, "ERROR_NETWORK_UNSUPPORTED"

    invoke-direct {v5, v3, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v5, Lcom/aliyun/player/bean/ErrorCode;->ERROR_NETWORK_UNSUPPORTED:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v3, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x5d

    const v15, 0x20050002

    move-object/from16 v96, v5

    const-string v5, "ERROR_NETWORK_RESOLVE"

    invoke-direct {v3, v5, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v3, Lcom/aliyun/player/bean/ErrorCode;->ERROR_NETWORK_RESOLVE:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v5, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x5e

    const v15, 0x20050003

    move-object/from16 v97, v3

    const-string v3, "ERROR_NETWORK_CONNECT_TIMEOUT"

    invoke-direct {v5, v3, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v5, Lcom/aliyun/player/bean/ErrorCode;->ERROR_NETWORK_CONNECT_TIMEOUT:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v3, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x5f

    const v15, 0x20050004

    move-object/from16 v98, v5

    const-string v5, "ERROR_NETWORK_COULD_NOT_CONNECT"

    invoke-direct {v3, v5, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v3, Lcom/aliyun/player/bean/ErrorCode;->ERROR_NETWORK_COULD_NOT_CONNECT:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v5, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x60

    const v15, 0x20050005

    move-object/from16 v99, v3

    const-string v3, "ERROR_NETWORK_HTTP_403"

    invoke-direct {v5, v3, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v5, Lcom/aliyun/player/bean/ErrorCode;->ERROR_NETWORK_HTTP_403:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v3, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x61

    const v15, 0x20050006

    move-object/from16 v100, v5

    const-string v5, "ERROR_NETWORK_HTTP_404"

    invoke-direct {v3, v5, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v3, Lcom/aliyun/player/bean/ErrorCode;->ERROR_NETWORK_HTTP_404:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v5, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x62

    const v15, 0x20050007

    move-object/from16 v101, v3

    const-string v3, "ERROR_NETWORK_HTTP_4XX"

    invoke-direct {v5, v3, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v5, Lcom/aliyun/player/bean/ErrorCode;->ERROR_NETWORK_HTTP_4XX:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v3, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x63

    const v15, 0x20050008

    move-object/from16 v102, v5

    const-string v5, "ERROR_NETWORK_HTTP_5XX"

    invoke-direct {v3, v5, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v3, Lcom/aliyun/player/bean/ErrorCode;->ERROR_NETWORK_HTTP_5XX:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v5, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x64

    const v15, 0x20050009

    move-object/from16 v103, v3

    const-string v3, "ERROR_NETWORK_HTTP_RANGE"

    invoke-direct {v5, v3, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v5, Lcom/aliyun/player/bean/ErrorCode;->ERROR_NETWORK_HTTP_RANGE:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v3, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x65

    const v15, 0x2005000a

    move-object/from16 v104, v5

    const-string v5, "ERROR_NETWORK_HTTP_400"

    invoke-direct {v3, v5, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v3, Lcom/aliyun/player/bean/ErrorCode;->ERROR_NETWORK_HTTP_400:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v5, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x66

    const/high16 v15, 0x20060000

    move-object/from16 v105, v3

    const-string v3, "ERROR_CODEC_UNKNOWN"

    invoke-direct {v5, v3, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v5, Lcom/aliyun/player/bean/ErrorCode;->ERROR_CODEC_UNKNOWN:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v3, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x67

    const v15, 0x20060001

    move-object/from16 v106, v5

    const-string v5, "ERROR_CODEC_VIDEO_NOT_SUPPORT"

    invoke-direct {v3, v5, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v3, Lcom/aliyun/player/bean/ErrorCode;->ERROR_CODEC_VIDEO_NOT_SUPPORT:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v5, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x68

    const v15, 0x20060002

    move-object/from16 v107, v3

    const-string v3, "ERROR_CODEC_AUDIO_NOT_SUPPORT"

    invoke-direct {v5, v3, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v5, Lcom/aliyun/player/bean/ErrorCode;->ERROR_CODEC_AUDIO_NOT_SUPPORT:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v3, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x69

    const/high16 v15, 0x20070000

    move-object/from16 v108, v5

    const-string v5, "ERROR_INERNAL_UNKNOWN"

    invoke-direct {v3, v5, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v3, Lcom/aliyun/player/bean/ErrorCode;->ERROR_INERNAL_UNKNOWN:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v5, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x6a

    const v15, 0x20070001

    move-object/from16 v109, v3

    const-string v3, "ERROR_INERNAL_EXIT"

    invoke-direct {v5, v3, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v5, Lcom/aliyun/player/bean/ErrorCode;->ERROR_INERNAL_EXIT:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v3, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x6b

    const/high16 v15, 0x20080000

    move-object/from16 v110, v5

    const-string v5, "ERROR_GENERAL_UNKNOWN"

    invoke-direct {v3, v5, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v3, Lcom/aliyun/player/bean/ErrorCode;->ERROR_GENERAL_UNKNOWN:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v5, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x6c

    const v15, 0x20080001

    move-object/from16 v111, v3

    const-string v3, "ERROR_GENERAL_EPERM"

    invoke-direct {v5, v3, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v5, Lcom/aliyun/player/bean/ErrorCode;->ERROR_GENERAL_EPERM:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v3, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x6d

    const v15, 0x20080002

    move-object/from16 v112, v5

    const-string v5, "ERROR_GENERAL_ENOENT"

    invoke-direct {v3, v5, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v3, Lcom/aliyun/player/bean/ErrorCode;->ERROR_GENERAL_ENOENT:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v5, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x6e

    const v15, 0x20080005

    move-object/from16 v113, v3

    const-string v3, "ERROR_GENERAL_EIO"

    invoke-direct {v5, v3, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v5, Lcom/aliyun/player/bean/ErrorCode;->ERROR_GENERAL_EIO:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v3, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x6f

    const v15, 0x2fffffff

    move-object/from16 v114, v5

    const-string v5, "ERROR_UNKNOWN"

    invoke-direct {v3, v5, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v3, Lcom/aliyun/player/bean/ErrorCode;->ERROR_UNKNOWN:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v5, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x70

    const/high16 v15, 0x30010000

    move-object/from16 v115, v3

    const-string v3, "DOWNLOAD_ERROR_NOT_SELECT_ITEM"

    invoke-direct {v5, v3, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v5, Lcom/aliyun/player/bean/ErrorCode;->DOWNLOAD_ERROR_NOT_SELECT_ITEM:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v3, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x71

    const v15, 0x30010001

    move-object/from16 v116, v5

    const-string v5, "DOWNLOAD_ERROR_NO_DOWNLOAD_ITEM"

    invoke-direct {v3, v5, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v3, Lcom/aliyun/player/bean/ErrorCode;->DOWNLOAD_ERROR_NO_DOWNLOAD_ITEM:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v5, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x72

    const v15, 0x30010002

    move-object/from16 v117, v3

    const-string v3, "DOWNLOAD_ERROR_STS_SOURCE_NULL"

    invoke-direct {v5, v3, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v5, Lcom/aliyun/player/bean/ErrorCode;->DOWNLOAD_ERROR_STS_SOURCE_NULL:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v3, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x73

    const v15, 0x30010003

    move-object/from16 v118, v5

    const-string v5, "DOWNLOAD_ERROR_AUTH_SOURCE_NULL"

    invoke-direct {v3, v5, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v3, Lcom/aliyun/player/bean/ErrorCode;->DOWNLOAD_ERROR_AUTH_SOURCE_NULL:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v5, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x74

    const v15, 0x30010004

    move-object/from16 v119, v3

    const-string v3, "DOWNLOAD_ERROR_AUTH_SOURCE_WRONG"

    invoke-direct {v5, v3, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v5, Lcom/aliyun/player/bean/ErrorCode;->DOWNLOAD_ERROR_AUTH_SOURCE_WRONG:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v3, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x75

    const v15, 0x30010005

    move-object/from16 v120, v5

    const-string v5, "DOWNLOAD_ERROR_INVALID_ITEM"

    invoke-direct {v3, v5, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v3, Lcom/aliyun/player/bean/ErrorCode;->DOWNLOAD_ERROR_INVALID_ITEM:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v5, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x76

    const v15, 0x30010006

    move-object/from16 v121, v3

    const-string v3, "DOWNLOAD_ERROR_URL_CANNOT_REACH"

    invoke-direct {v5, v3, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v5, Lcom/aliyun/player/bean/ErrorCode;->DOWNLOAD_ERROR_URL_CANNOT_REACH:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v3, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x77

    const v15, 0x30010007

    move-object/from16 v122, v5

    const-string v5, "DOWNLOAD_ERROR_NOT_SUPPORT_FORMAT"

    invoke-direct {v3, v5, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v3, Lcom/aliyun/player/bean/ErrorCode;->DOWNLOAD_ERROR_NOT_SUPPORT_FORMAT:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v5, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x78

    const v15, 0x30010008

    move-object/from16 v123, v3

    const-string v3, "DOWNLOAD_ERROR_ENCRYPT_FILE_NOT_MATCH"

    invoke-direct {v5, v3, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v5, Lcom/aliyun/player/bean/ErrorCode;->DOWNLOAD_ERROR_ENCRYPT_FILE_NOT_MATCH:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v3, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x79

    const v15, 0x30010009

    move-object/from16 v124, v5

    const-string v5, "DOWNLOAD_ERROR_DOWNLOAD_SWITCH_OFF"

    invoke-direct {v3, v5, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v3, Lcom/aliyun/player/bean/ErrorCode;->DOWNLOAD_ERROR_DOWNLOAD_SWITCH_OFF:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v5, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x7a

    const v15, 0x3001000a

    move-object/from16 v125, v3

    const-string v3, "DOWNLOAD_ERROR_NET_ERROR"

    invoke-direct {v5, v3, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v5, Lcom/aliyun/player/bean/ErrorCode;->DOWNLOAD_ERROR_NET_ERROR:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v3, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x7b

    const v15, 0x3001000b

    move-object/from16 v126, v5

    const-string v5, "DOWNLOAD_ERROR_NOT_SET_SAVE_DIR"

    invoke-direct {v3, v5, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v3, Lcom/aliyun/player/bean/ErrorCode;->DOWNLOAD_ERROR_NOT_SET_SAVE_DIR:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v5, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x7c

    const v15, 0x3001000c    # 4.6929993E-10f

    move-object/from16 v127, v3

    const-string v3, "DOWNLOAD_ERROR_CANNOT_CREATE_SAVE_DIR"

    invoke-direct {v5, v3, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v5, Lcom/aliyun/player/bean/ErrorCode;->DOWNLOAD_ERROR_CANNOT_CREATE_SAVE_DIR:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v3, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x7d

    const v15, 0x3001000d    # 4.693E-10f

    move-object/from16 v128, v5

    const-string v5, "DOWNLOAD_ERROR_NO_SPACE"

    invoke-direct {v3, v5, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v3, Lcom/aliyun/player/bean/ErrorCode;->DOWNLOAD_ERROR_NO_SPACE:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v5, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x7e

    const v15, 0x3001000e    # 4.6930004E-10f

    move-object/from16 v129, v3

    const-string v3, "DOWNLOAD_ERROR_WRITE_ERROR"

    invoke-direct {v5, v3, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v5, Lcom/aliyun/player/bean/ErrorCode;->DOWNLOAD_ERROR_WRITE_ERROR:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v3, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x7f

    const v15, 0x3001000f    # 4.693001E-10f

    move-object/from16 v130, v5

    const-string v5, "DOWNLOAD_ERROR_ENCRYPT_ERROR"

    invoke-direct {v3, v5, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v3, Lcom/aliyun/player/bean/ErrorCode;->DOWNLOAD_ERROR_ENCRYPT_ERROR:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v5, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x80

    const v15, 0x30010010

    move-object/from16 v131, v3

    const-string v3, "DOWNLOAD_ERROR_FILE_NOT_EXIST"

    invoke-direct {v5, v3, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v5, Lcom/aliyun/player/bean/ErrorCode;->DOWNLOAD_ERROR_FILE_NOT_EXIST:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v3, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x81

    const v15, 0x30010011

    move-object/from16 v132, v5

    const-string v5, "DOWNLOAD_ERROR_CLEAN_INVALID_PARAM"

    invoke-direct {v3, v5, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v3, Lcom/aliyun/player/bean/ErrorCode;->DOWNLOAD_ERROR_CLEAN_INVALID_PARAM:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v5, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x82

    const v15, 0x30010012

    move-object/from16 v133, v3

    const-string v3, "DOWNLOAD_ERROR_CLEAN_WRONG_STATUS"

    invoke-direct {v5, v3, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v5, Lcom/aliyun/player/bean/ErrorCode;->DOWNLOAD_ERROR_CLEAN_WRONG_STATUS:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v3, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x83

    const v15, 0x30010013

    move-object/from16 v134, v5

    const-string v5, "DOWNLOAD_ERROR_GET_AES_KEY_FAIL"

    invoke-direct {v3, v5, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v3, Lcom/aliyun/player/bean/ErrorCode;->DOWNLOAD_ERROR_GET_AES_KEY_FAIL:Lcom/aliyun/player/bean/ErrorCode;

    new-instance v5, Lcom/aliyun/player/bean/ErrorCode;

    const/16 v7, 0x84

    const v15, 0x30010014

    move-object/from16 v135, v3

    const-string v3, "DOWNLOAD_ERROR_ENCRYPTION_NOT_SUPPORT"

    invoke-direct {v5, v3, v7, v15}, Lcom/aliyun/player/bean/ErrorCode;-><init>(Ljava/lang/String;II)V

    sput-object v5, Lcom/aliyun/player/bean/ErrorCode;->DOWNLOAD_ERROR_ENCRYPTION_NOT_SUPPORT:Lcom/aliyun/player/bean/ErrorCode;

    const/16 v3, 0x85

    new-array v3, v3, [Lcom/aliyun/player/bean/ErrorCode;

    const/4 v7, 0x0

    aput-object v0, v3, v7

    const/4 v0, 0x1

    aput-object v1, v3, v0

    const/4 v0, 0x2

    aput-object v2, v3, v0

    const/4 v0, 0x3

    aput-object v4, v3, v0

    const/4 v0, 0x4

    aput-object v6, v3, v0

    const/4 v0, 0x5

    aput-object v8, v3, v0

    const/4 v0, 0x6

    aput-object v10, v3, v0

    const/4 v0, 0x7

    aput-object v12, v3, v0

    const/16 v0, 0x8

    aput-object v14, v3, v0

    const/16 v0, 0x9

    aput-object v13, v3, v0

    const/16 v0, 0xa

    aput-object v11, v3, v0

    const/16 v0, 0xb

    aput-object v9, v3, v0

    const/16 v0, 0xc

    aput-object v16, v3, v0

    const/16 v0, 0xd

    aput-object v17, v3, v0

    const/16 v0, 0xe

    aput-object v18, v3, v0

    const/16 v0, 0xf

    aput-object v19, v3, v0

    const/16 v0, 0x10

    aput-object v20, v3, v0

    const/16 v0, 0x11

    aput-object v21, v3, v0

    const/16 v0, 0x12

    aput-object v22, v3, v0

    const/16 v0, 0x13

    aput-object v23, v3, v0

    const/16 v0, 0x14

    aput-object v24, v3, v0

    const/16 v0, 0x15

    aput-object v25, v3, v0

    const/16 v0, 0x16

    aput-object v26, v3, v0

    const/16 v0, 0x17

    aput-object v27, v3, v0

    const/16 v0, 0x18

    aput-object v28, v3, v0

    const/16 v0, 0x19

    aput-object v29, v3, v0

    const/16 v0, 0x1a

    aput-object v30, v3, v0

    const/16 v0, 0x1b

    aput-object v31, v3, v0

    const/16 v0, 0x1c

    aput-object v32, v3, v0

    const/16 v0, 0x1d

    aput-object v33, v3, v0

    const/16 v0, 0x1e

    aput-object v34, v3, v0

    const/16 v0, 0x1f

    aput-object v35, v3, v0

    const/16 v0, 0x20

    aput-object v36, v3, v0

    const/16 v0, 0x21

    aput-object v37, v3, v0

    const/16 v0, 0x22

    aput-object v38, v3, v0

    const/16 v0, 0x23

    aput-object v39, v3, v0

    const/16 v0, 0x24

    aput-object v40, v3, v0

    const/16 v0, 0x25

    aput-object v41, v3, v0

    const/16 v0, 0x26

    aput-object v42, v3, v0

    const/16 v0, 0x27

    aput-object v43, v3, v0

    const/16 v0, 0x28

    aput-object v44, v3, v0

    const/16 v0, 0x29

    aput-object v45, v3, v0

    const/16 v0, 0x2a

    aput-object v46, v3, v0

    const/16 v0, 0x2b

    aput-object v47, v3, v0

    const/16 v0, 0x2c

    aput-object v48, v3, v0

    const/16 v0, 0x2d

    aput-object v49, v3, v0

    const/16 v0, 0x2e

    aput-object v50, v3, v0

    const/16 v0, 0x2f

    aput-object v51, v3, v0

    const/16 v0, 0x30

    aput-object v52, v3, v0

    const/16 v0, 0x31

    aput-object v53, v3, v0

    const/16 v0, 0x32

    aput-object v54, v3, v0

    const/16 v0, 0x33

    aput-object v55, v3, v0

    const/16 v0, 0x34

    aput-object v56, v3, v0

    const/16 v0, 0x35

    aput-object v57, v3, v0

    const/16 v0, 0x36

    aput-object v58, v3, v0

    const/16 v0, 0x37

    aput-object v59, v3, v0

    const/16 v0, 0x38

    aput-object v60, v3, v0

    const/16 v0, 0x39

    aput-object v61, v3, v0

    const/16 v0, 0x3a

    aput-object v62, v3, v0

    const/16 v0, 0x3b

    aput-object v63, v3, v0

    const/16 v0, 0x3c

    aput-object v64, v3, v0

    const/16 v0, 0x3d

    aput-object v65, v3, v0

    const/16 v0, 0x3e

    aput-object v66, v3, v0

    const/16 v0, 0x3f

    aput-object v67, v3, v0

    const/16 v0, 0x40

    aput-object v68, v3, v0

    const/16 v0, 0x41

    aput-object v69, v3, v0

    const/16 v0, 0x42

    aput-object v70, v3, v0

    const/16 v0, 0x43

    aput-object v71, v3, v0

    const/16 v0, 0x44

    aput-object v72, v3, v0

    const/16 v0, 0x45

    aput-object v73, v3, v0

    const/16 v0, 0x46

    aput-object v74, v3, v0

    const/16 v0, 0x47

    aput-object v75, v3, v0

    const/16 v0, 0x48

    aput-object v76, v3, v0

    const/16 v0, 0x49

    aput-object v77, v3, v0

    const/16 v0, 0x4a

    aput-object v78, v3, v0

    const/16 v0, 0x4b

    aput-object v79, v3, v0

    const/16 v0, 0x4c

    aput-object v80, v3, v0

    const/16 v0, 0x4d

    aput-object v81, v3, v0

    const/16 v0, 0x4e

    aput-object v82, v3, v0

    const/16 v0, 0x4f

    aput-object v83, v3, v0

    const/16 v0, 0x50

    aput-object v84, v3, v0

    const/16 v0, 0x51

    aput-object v85, v3, v0

    const/16 v0, 0x52

    aput-object v86, v3, v0

    const/16 v0, 0x53

    aput-object v87, v3, v0

    const/16 v0, 0x54

    aput-object v88, v3, v0

    const/16 v0, 0x55

    aput-object v89, v3, v0

    const/16 v0, 0x56

    aput-object v90, v3, v0

    const/16 v0, 0x57

    aput-object v91, v3, v0

    const/16 v0, 0x58

    aput-object v92, v3, v0

    const/16 v0, 0x59

    aput-object v93, v3, v0

    const/16 v0, 0x5a

    aput-object v94, v3, v0

    const/16 v0, 0x5b

    aput-object v95, v3, v0

    const/16 v0, 0x5c

    aput-object v96, v3, v0

    const/16 v0, 0x5d

    aput-object v97, v3, v0

    const/16 v0, 0x5e

    aput-object v98, v3, v0

    const/16 v0, 0x5f

    aput-object v99, v3, v0

    const/16 v0, 0x60

    aput-object v100, v3, v0

    const/16 v0, 0x61

    aput-object v101, v3, v0

    const/16 v0, 0x62

    aput-object v102, v3, v0

    const/16 v0, 0x63

    aput-object v103, v3, v0

    const/16 v0, 0x64

    aput-object v104, v3, v0

    const/16 v0, 0x65

    aput-object v105, v3, v0

    const/16 v0, 0x66

    aput-object v106, v3, v0

    const/16 v0, 0x67

    aput-object v107, v3, v0

    const/16 v0, 0x68

    aput-object v108, v3, v0

    const/16 v0, 0x69

    aput-object v109, v3, v0

    const/16 v0, 0x6a

    aput-object v110, v3, v0

    const/16 v0, 0x6b

    aput-object v111, v3, v0

    const/16 v0, 0x6c

    aput-object v112, v3, v0

    const/16 v0, 0x6d

    aput-object v113, v3, v0

    const/16 v0, 0x6e

    aput-object v114, v3, v0

    const/16 v0, 0x6f

    aput-object v115, v3, v0

    const/16 v0, 0x70

    aput-object v116, v3, v0

    const/16 v0, 0x71

    aput-object v117, v3, v0

    const/16 v0, 0x72

    aput-object v118, v3, v0

    const/16 v0, 0x73

    aput-object v119, v3, v0

    const/16 v0, 0x74

    aput-object v120, v3, v0

    const/16 v0, 0x75

    aput-object v121, v3, v0

    const/16 v0, 0x76

    aput-object v122, v3, v0

    const/16 v0, 0x77

    aput-object v123, v3, v0

    const/16 v0, 0x78

    aput-object v124, v3, v0

    const/16 v0, 0x79

    aput-object v125, v3, v0

    const/16 v0, 0x7a

    aput-object v126, v3, v0

    const/16 v0, 0x7b

    aput-object v127, v3, v0

    const/16 v0, 0x7c

    aput-object v128, v3, v0

    const/16 v0, 0x7d

    aput-object v129, v3, v0

    const/16 v0, 0x7e

    aput-object v130, v3, v0

    const/16 v0, 0x7f

    aput-object v131, v3, v0

    const/16 v0, 0x80

    aput-object v132, v3, v0

    const/16 v0, 0x81

    aput-object v133, v3, v0

    const/16 v0, 0x82

    aput-object v134, v3, v0

    const/16 v0, 0x83

    aput-object v135, v3, v0

    const/16 v0, 0x84

    aput-object v5, v3, v0

    sput-object v3, Lcom/aliyun/player/bean/ErrorCode;->$VALUES:[Lcom/aliyun/player/bean/ErrorCode;

    return-void
.end method

.method private constructor <init>(Ljava/lang/String;II)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I)V"
        }
    .end annotation

    invoke-direct {p0, p1, p2}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    iput p3, p0, Lcom/aliyun/player/bean/ErrorCode;->mCode:I

    return-void
.end method

.method public static valueOf(Ljava/lang/String;)Lcom/aliyun/player/bean/ErrorCode;
    .locals 1

    const-class v0, Lcom/aliyun/player/bean/ErrorCode;

    invoke-static {v0, p0}, Ljava/lang/Enum;->valueOf(Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Enum;

    move-result-object p0

    check-cast p0, Lcom/aliyun/player/bean/ErrorCode;

    return-object p0
.end method

.method public static values()[Lcom/aliyun/player/bean/ErrorCode;
    .locals 1

    sget-object v0, Lcom/aliyun/player/bean/ErrorCode;->$VALUES:[Lcom/aliyun/player/bean/ErrorCode;

    invoke-virtual {v0}, [Lcom/aliyun/player/bean/ErrorCode;->clone()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [Lcom/aliyun/player/bean/ErrorCode;

    return-object v0
.end method


# virtual methods
.method public getValue()I
    .locals 1

    iget v0, p0, Lcom/aliyun/player/bean/ErrorCode;->mCode:I

    return v0
.end method
