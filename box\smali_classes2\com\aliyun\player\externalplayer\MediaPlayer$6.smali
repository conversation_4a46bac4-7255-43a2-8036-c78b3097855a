.class Lcom/aliyun/player/externalplayer/MediaPlayer$6;
.super Ljava/lang/Object;

# interfaces
.implements Landroid/media/MediaPlayer$OnPreparedListener;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/aliyun/player/externalplayer/MediaPlayer;-><init>(Landroid/content/Context;Lcom/aliyun/player/nativeclass/Options;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic this$0:Lcom/aliyun/player/externalplayer/MediaPlayer;


# direct methods
.method public constructor <init>(Lcom/aliyun/player/externalplayer/MediaPlayer;)V
    .locals 0

    iput-object p1, p0, Lcom/aliyun/player/externalplayer/MediaPlayer$6;->this$0:Lcom/aliyun/player/externalplayer/MediaPlayer;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public onPrepared(Landroid/media/MediaPlayer;)V
    .locals 4

    iget-object p1, p0, Lcom/aliyun/player/externalplayer/MediaPlayer$6;->this$0:Lcom/aliyun/player/externalplayer/MediaPlayer;

    invoke-static {p1}, Lcom/aliyun/player/externalplayer/MediaPlayer;->access$1100(Lcom/aliyun/player/externalplayer/MediaPlayer;)Landroid/media/MediaPlayer;

    move-result-object v0

    invoke-virtual {v0}, Landroid/media/MediaPlayer;->getTrackInfo()[Landroid/media/MediaPlayer$TrackInfo;

    move-result-object v0

    invoke-static {p1, v0}, Lcom/aliyun/player/externalplayer/MediaPlayer;->access$1002(Lcom/aliyun/player/externalplayer/MediaPlayer;[Landroid/media/MediaPlayer$TrackInfo;)[Landroid/media/MediaPlayer$TrackInfo;

    iget-object p1, p0, Lcom/aliyun/player/externalplayer/MediaPlayer$6;->this$0:Lcom/aliyun/player/externalplayer/MediaPlayer;

    invoke-static {p1}, Lcom/aliyun/player/externalplayer/MediaPlayer;->access$1200(Lcom/aliyun/player/externalplayer/MediaPlayer;)V

    iget-object p1, p0, Lcom/aliyun/player/externalplayer/MediaPlayer$6;->this$0:Lcom/aliyun/player/externalplayer/MediaPlayer;

    invoke-static {p1}, Lcom/aliyun/player/externalplayer/MediaPlayer;->access$1300(Lcom/aliyun/player/externalplayer/MediaPlayer;)Z

    move-result p1

    if-eqz p1, :cond_1

    iget-object p1, p0, Lcom/aliyun/player/externalplayer/MediaPlayer$6;->this$0:Lcom/aliyun/player/externalplayer/MediaPlayer;

    invoke-static {p1}, Lcom/aliyun/player/externalplayer/MediaPlayer;->access$1400(Lcom/aliyun/player/externalplayer/MediaPlayer;)Lcom/aliyun/player/ApasaraExternalPlayer$OnAutoPlayStartListener;

    move-result-object p1

    if-eqz p1, :cond_0

    iget-object p1, p0, Lcom/aliyun/player/externalplayer/MediaPlayer$6;->this$0:Lcom/aliyun/player/externalplayer/MediaPlayer;

    invoke-static {p1}, Lcom/aliyun/player/externalplayer/MediaPlayer;->access$1400(Lcom/aliyun/player/externalplayer/MediaPlayer;)Lcom/aliyun/player/ApasaraExternalPlayer$OnAutoPlayStartListener;

    move-result-object p1

    invoke-interface {p1}, Lcom/aliyun/player/ApasaraExternalPlayer$OnAutoPlayStartListener;->onAutoPlayStart()V

    :cond_0
    iget-object p1, p0, Lcom/aliyun/player/externalplayer/MediaPlayer$6;->this$0:Lcom/aliyun/player/externalplayer/MediaPlayer;

    sget-object v0, Lcom/aliyun/player/ApasaraExternalPlayer$PlayerStatus;->PLAYER_PREPARED:Lcom/aliyun/player/ApasaraExternalPlayer$PlayerStatus;

    const/4 v1, 0x0

    invoke-static {p1, v0, v1}, Lcom/aliyun/player/externalplayer/MediaPlayer;->access$600(Lcom/aliyun/player/externalplayer/MediaPlayer;Lcom/aliyun/player/ApasaraExternalPlayer$PlayerStatus;Z)V

    iget-object p1, p0, Lcom/aliyun/player/externalplayer/MediaPlayer$6;->this$0:Lcom/aliyun/player/externalplayer/MediaPlayer;

    invoke-virtual {p1}, Lcom/aliyun/player/externalplayer/MediaPlayer;->start()V

    goto :goto_0

    :cond_1
    iget-object p1, p0, Lcom/aliyun/player/externalplayer/MediaPlayer$6;->this$0:Lcom/aliyun/player/externalplayer/MediaPlayer;

    sget-object v0, Lcom/aliyun/player/ApasaraExternalPlayer$PlayerStatus;->PLAYER_PREPARED:Lcom/aliyun/player/ApasaraExternalPlayer$PlayerStatus;

    const/4 v1, 0x1

    invoke-static {p1, v0, v1}, Lcom/aliyun/player/externalplayer/MediaPlayer;->access$600(Lcom/aliyun/player/externalplayer/MediaPlayer;Lcom/aliyun/player/ApasaraExternalPlayer$PlayerStatus;Z)V

    iget-object p1, p0, Lcom/aliyun/player/externalplayer/MediaPlayer$6;->this$0:Lcom/aliyun/player/externalplayer/MediaPlayer;

    invoke-static {p1}, Lcom/aliyun/player/externalplayer/MediaPlayer;->access$1500(Lcom/aliyun/player/externalplayer/MediaPlayer;)Lcom/aliyun/player/ApasaraExternalPlayer$OnPreparedListener;

    move-result-object p1

    if-eqz p1, :cond_2

    iget-object p1, p0, Lcom/aliyun/player/externalplayer/MediaPlayer$6;->this$0:Lcom/aliyun/player/externalplayer/MediaPlayer;

    invoke-static {p1}, Lcom/aliyun/player/externalplayer/MediaPlayer;->access$1500(Lcom/aliyun/player/externalplayer/MediaPlayer;)Lcom/aliyun/player/ApasaraExternalPlayer$OnPreparedListener;

    move-result-object p1

    invoke-interface {p1}, Lcom/aliyun/player/ApasaraExternalPlayer$OnPreparedListener;->onPrepared()V

    :cond_2
    :goto_0
    iget-object p1, p0, Lcom/aliyun/player/externalplayer/MediaPlayer$6;->this$0:Lcom/aliyun/player/externalplayer/MediaPlayer;

    invoke-static {p1}, Lcom/aliyun/player/externalplayer/MediaPlayer;->access$1600(Lcom/aliyun/player/externalplayer/MediaPlayer;)J

    move-result-wide v0

    const-wide/16 v2, 0x0

    cmp-long p1, v0, v2

    if-ltz p1, :cond_3

    iget-object p1, p0, Lcom/aliyun/player/externalplayer/MediaPlayer$6;->this$0:Lcom/aliyun/player/externalplayer/MediaPlayer;

    invoke-static {p1}, Lcom/aliyun/player/externalplayer/MediaPlayer;->access$1600(Lcom/aliyun/player/externalplayer/MediaPlayer;)J

    move-result-wide v0

    iget-object v2, p0, Lcom/aliyun/player/externalplayer/MediaPlayer$6;->this$0:Lcom/aliyun/player/externalplayer/MediaPlayer;

    invoke-static {v2}, Lcom/aliyun/player/externalplayer/MediaPlayer;->access$1700(Lcom/aliyun/player/externalplayer/MediaPlayer;)Z

    move-result v2

    invoke-virtual {p1, v0, v1, v2}, Lcom/aliyun/player/externalplayer/MediaPlayer;->seekTo(JZ)V

    iget-object p1, p0, Lcom/aliyun/player/externalplayer/MediaPlayer$6;->this$0:Lcom/aliyun/player/externalplayer/MediaPlayer;

    const-wide/16 v0, -0x1

    invoke-static {p1, v0, v1}, Lcom/aliyun/player/externalplayer/MediaPlayer;->access$1602(Lcom/aliyun/player/externalplayer/MediaPlayer;J)J

    :cond_3
    return-void
.end method
