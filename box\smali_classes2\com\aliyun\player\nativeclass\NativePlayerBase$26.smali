.class Lcom/aliyun/player/nativeclass/NativePlayerBase$26;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/aliyun/player/nativeclass/NativePlayerBase;->onSubtitleExtAdded(ILjava/lang/String;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic this$0:Lcom/aliyun/player/nativeclass/NativePlayerBase;

.field final synthetic val$content:Ljava/lang/String;

.field final synthetic val$id:I


# direct methods
.method public constructor <init>(Lcom/aliyun/player/nativeclass/NativePlayerBase;ILjava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/aliyun/player/nativeclass/NativePlayerBase$26;->this$0:Lcom/aliyun/player/nativeclass/NativePlayerBase;

    iput p2, p0, <PERSON><PERSON>/aliyun/player/nativeclass/NativePlayerBase$26;->val$id:I

    iput-object p3, p0, Lcom/aliyun/player/nativeclass/NativePlayerBase$26;->val$content:Ljava/lang/String;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 3

    iget-object v0, p0, Lcom/aliyun/player/nativeclass/NativePlayerBase$26;->this$0:Lcom/aliyun/player/nativeclass/NativePlayerBase;

    invoke-static {v0}, Lcom/aliyun/player/nativeclass/NativePlayerBase;->access$1700(Lcom/aliyun/player/nativeclass/NativePlayerBase;)Lcom/aliyun/player/IPlayer$OnSubtitleDisplayListener;

    move-result-object v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/aliyun/player/nativeclass/NativePlayerBase$26;->this$0:Lcom/aliyun/player/nativeclass/NativePlayerBase;

    invoke-static {v0}, Lcom/aliyun/player/nativeclass/NativePlayerBase;->access$1700(Lcom/aliyun/player/nativeclass/NativePlayerBase;)Lcom/aliyun/player/IPlayer$OnSubtitleDisplayListener;

    move-result-object v0

    iget v1, p0, Lcom/aliyun/player/nativeclass/NativePlayerBase$26;->val$id:I

    iget-object v2, p0, Lcom/aliyun/player/nativeclass/NativePlayerBase$26;->val$content:Ljava/lang/String;

    invoke-interface {v0, v1, v2}, Lcom/aliyun/player/IPlayer$OnSubtitleDisplayListener;->onSubtitleExtAdded(ILjava/lang/String;)V

    :cond_0
    return-void
.end method
