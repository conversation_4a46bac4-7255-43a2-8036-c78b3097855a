.class public interface abstract Lcom/aliyun/player/source/BitStreamSource$ReadCallback;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/aliyun/player/source/BitStreamSource;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "ReadCallback"
.end annotation


# virtual methods
.method public abstract read([B)I
.end method
