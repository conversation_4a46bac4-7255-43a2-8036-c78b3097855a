.class public interface abstract Lcom/aliyun/player/source/BitStreamSource$SeekCallback;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/aliyun/player/source/BitStreamSource;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "SeekCallback"
.end annotation


# virtual methods
.method public abstract seek(JI)J
.end method
