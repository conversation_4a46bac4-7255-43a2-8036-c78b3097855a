.class public final enum Lcom/aliyun/player/source/Definition;
.super Ljava/lang/Enum;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Enum<",
        "Lcom/aliyun/player/source/Definition;",
        ">;"
    }
.end annotation


# static fields
.field private static final synthetic $VALUES:[Lcom/aliyun/player/source/Definition;

.field public static final enum DEFINITION_2K:Lcom/aliyun/player/source/Definition;

.field public static final enum DEFINITION_4K:Lcom/aliyun/player/source/Definition;

.field public static final enum DEFINITION_AUTO:Lcom/aliyun/player/source/Definition;

.field public static final enum DEFINITION_FD:Lcom/aliyun/player/source/Definition;

.field public static final enum DEFINITION_HD:Lcom/aliyun/player/source/Definition;

.field public static final enum DEFINITION_HQ:Lcom/aliyun/player/source/Definition;

.field public static final enum DEFINITION_LD:Lcom/aliyun/player/source/Definition;

.field public static final enum DEFINITION_OD:Lcom/aliyun/player/source/Definition;

.field public static final enum DEFINITION_SD:Lcom/aliyun/player/source/Definition;

.field public static final enum DEFINITION_SQ:Lcom/aliyun/player/source/Definition;


# instance fields
.field private mName:Ljava/lang/String;


# direct methods
.method static constructor <clinit>()V
    .locals 16

    new-instance v0, Lcom/aliyun/player/source/Definition;

    const-string v1, "FD"

    const-string v2, "DEFINITION_FD"

    const/4 v3, 0x0

    invoke-direct {v0, v2, v3, v1}, Lcom/aliyun/player/source/Definition;-><init>(Ljava/lang/String;ILjava/lang/String;)V

    sput-object v0, Lcom/aliyun/player/source/Definition;->DEFINITION_FD:Lcom/aliyun/player/source/Definition;

    new-instance v1, Lcom/aliyun/player/source/Definition;

    const-string v2, "LD"

    const-string v4, "DEFINITION_LD"

    const/4 v5, 0x1

    invoke-direct {v1, v4, v5, v2}, Lcom/aliyun/player/source/Definition;-><init>(Ljava/lang/String;ILjava/lang/String;)V

    sput-object v1, Lcom/aliyun/player/source/Definition;->DEFINITION_LD:Lcom/aliyun/player/source/Definition;

    new-instance v2, Lcom/aliyun/player/source/Definition;

    const-string v4, "SD"

    const-string v6, "DEFINITION_SD"

    const/4 v7, 0x2

    invoke-direct {v2, v6, v7, v4}, Lcom/aliyun/player/source/Definition;-><init>(Ljava/lang/String;ILjava/lang/String;)V

    sput-object v2, Lcom/aliyun/player/source/Definition;->DEFINITION_SD:Lcom/aliyun/player/source/Definition;

    new-instance v4, Lcom/aliyun/player/source/Definition;

    const-string v6, "HD"

    const-string v8, "DEFINITION_HD"

    const/4 v9, 0x3

    invoke-direct {v4, v8, v9, v6}, Lcom/aliyun/player/source/Definition;-><init>(Ljava/lang/String;ILjava/lang/String;)V

    sput-object v4, Lcom/aliyun/player/source/Definition;->DEFINITION_HD:Lcom/aliyun/player/source/Definition;

    new-instance v6, Lcom/aliyun/player/source/Definition;

    const-string v8, "OD"

    const-string v10, "DEFINITION_OD"

    const/4 v11, 0x4

    invoke-direct {v6, v10, v11, v8}, Lcom/aliyun/player/source/Definition;-><init>(Ljava/lang/String;ILjava/lang/String;)V

    sput-object v6, Lcom/aliyun/player/source/Definition;->DEFINITION_OD:Lcom/aliyun/player/source/Definition;

    new-instance v8, Lcom/aliyun/player/source/Definition;

    const-string v10, "2K"

    const-string v12, "DEFINITION_2K"

    const/4 v13, 0x5

    invoke-direct {v8, v12, v13, v10}, Lcom/aliyun/player/source/Definition;-><init>(Ljava/lang/String;ILjava/lang/String;)V

    sput-object v8, Lcom/aliyun/player/source/Definition;->DEFINITION_2K:Lcom/aliyun/player/source/Definition;

    new-instance v10, Lcom/aliyun/player/source/Definition;

    const-string v12, "4K"

    const-string v14, "DEFINITION_4K"

    const/4 v15, 0x6

    invoke-direct {v10, v14, v15, v12}, Lcom/aliyun/player/source/Definition;-><init>(Ljava/lang/String;ILjava/lang/String;)V

    sput-object v10, Lcom/aliyun/player/source/Definition;->DEFINITION_4K:Lcom/aliyun/player/source/Definition;

    new-instance v12, Lcom/aliyun/player/source/Definition;

    const-string v14, "SQ"

    const-string v15, "DEFINITION_SQ"

    const/4 v13, 0x7

    invoke-direct {v12, v15, v13, v14}, Lcom/aliyun/player/source/Definition;-><init>(Ljava/lang/String;ILjava/lang/String;)V

    sput-object v12, Lcom/aliyun/player/source/Definition;->DEFINITION_SQ:Lcom/aliyun/player/source/Definition;

    new-instance v14, Lcom/aliyun/player/source/Definition;

    const-string v15, "HQ"

    const-string v13, "DEFINITION_HQ"

    const/16 v11, 0x8

    invoke-direct {v14, v13, v11, v15}, Lcom/aliyun/player/source/Definition;-><init>(Ljava/lang/String;ILjava/lang/String;)V

    sput-object v14, Lcom/aliyun/player/source/Definition;->DEFINITION_HQ:Lcom/aliyun/player/source/Definition;

    new-instance v13, Lcom/aliyun/player/source/Definition;

    const-string v15, "AUTO"

    const-string v11, "DEFINITION_AUTO"

    const/16 v9, 0x9

    invoke-direct {v13, v11, v9, v15}, Lcom/aliyun/player/source/Definition;-><init>(Ljava/lang/String;ILjava/lang/String;)V

    sput-object v13, Lcom/aliyun/player/source/Definition;->DEFINITION_AUTO:Lcom/aliyun/player/source/Definition;

    const/16 v11, 0xa

    new-array v11, v11, [Lcom/aliyun/player/source/Definition;

    aput-object v0, v11, v3

    aput-object v1, v11, v5

    aput-object v2, v11, v7

    const/4 v0, 0x3

    aput-object v4, v11, v0

    const/4 v0, 0x4

    aput-object v6, v11, v0

    const/4 v0, 0x5

    aput-object v8, v11, v0

    const/4 v0, 0x6

    aput-object v10, v11, v0

    const/4 v0, 0x7

    aput-object v12, v11, v0

    const/16 v0, 0x8

    aput-object v14, v11, v0

    aput-object v13, v11, v9

    sput-object v11, Lcom/aliyun/player/source/Definition;->$VALUES:[Lcom/aliyun/player/source/Definition;

    return-void
.end method

.method private constructor <init>(Ljava/lang/String;ILjava/lang/String;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            ")V"
        }
    .end annotation

    invoke-direct {p0, p1, p2}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    iput-object p3, p0, Lcom/aliyun/player/source/Definition;->mName:Ljava/lang/String;

    return-void
.end method

.method public static valueOf(Ljava/lang/String;)Lcom/aliyun/player/source/Definition;
    .locals 1

    const-class v0, Lcom/aliyun/player/source/Definition;

    invoke-static {v0, p0}, Ljava/lang/Enum;->valueOf(Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Enum;

    move-result-object p0

    check-cast p0, Lcom/aliyun/player/source/Definition;

    return-object p0
.end method

.method public static values()[Lcom/aliyun/player/source/Definition;
    .locals 1

    sget-object v0, Lcom/aliyun/player/source/Definition;->$VALUES:[Lcom/aliyun/player/source/Definition;

    invoke-virtual {v0}, [Lcom/aliyun/player/source/Definition;->clone()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [Lcom/aliyun/player/source/Definition;

    return-object v0
.end method


# virtual methods
.method public getName()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/aliyun/player/source/Definition;->mName:Ljava/lang/String;

    return-object v0
.end method
