.class public interface abstract Lcom/aliyun/player/videoview/AliDisplayView$OnViewStatusListener;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/aliyun/player/videoview/AliDisplayView;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "OnViewStatusListener"
.end annotation


# virtual methods
.method public abstract onSurfaceCreated()V
.end method

.method public abstract onSurfaceDestroy()V
.end method

.method public abstract onSurfaceSizeChanged()V
.end method

.method public abstract onViewCreated(Lcom/aliyun/player/videoview/AliDisplayView$DisplayViewType;)V
.end method
