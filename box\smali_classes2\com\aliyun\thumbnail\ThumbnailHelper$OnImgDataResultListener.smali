.class interface abstract Lcom/aliyun/thumbnail/ThumbnailHelper$OnImgDataResultListener;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/aliyun/thumbnail/ThumbnailHelper;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "OnImgDataResultListener"
.end annotation


# virtual methods
.method public abstract onFail()V
.end method

.method public abstract onSuccess([B)V
.end method
