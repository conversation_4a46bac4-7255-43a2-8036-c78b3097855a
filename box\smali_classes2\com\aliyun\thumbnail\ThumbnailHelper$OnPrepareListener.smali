.class public interface abstract Lcom/aliyun/thumbnail/ThumbnailHelper$OnPrepareListener;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/aliyun/thumbnail/ThumbnailHelper;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "OnPrepareListener"
.end annotation


# virtual methods
.method public abstract onPrepareFail()V
.end method

.method public abstract onPrepareSuccess()V
.end method
