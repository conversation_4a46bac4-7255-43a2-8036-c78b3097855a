.class Lcom/aliyun/utils/BaseRequest$2;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/aliyun/utils/BaseRequest;->getAsync()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic this$0:Lcom/aliyun/utils/BaseRequest;


# direct methods
.method public constructor <init>(Lcom/aliyun/utils/BaseRequest;)V
    .locals 0

    iput-object p1, p0, Lcom/aliyun/utils/BaseRequest$2;->this$0:Lcom/aliyun/utils/BaseRequest;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 1

    iget-object v0, p0, Lcom/aliyun/utils/BaseRequest$2;->this$0:Lcom/aliyun/utils/BaseRequest;

    invoke-virtual {v0}, Lcom/aliyun/utils/BaseRequest;->runInBackground()V

    return-void
.end method
