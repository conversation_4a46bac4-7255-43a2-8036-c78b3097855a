.class interface abstract Lcom/aliyun/utils/CpuProcessTracker$RuntimeCallback;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/aliyun/utils/CpuProcessTracker;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "RuntimeCallback"
.end annotation


# virtual methods
.method public abstract onLine(Ljava/lang/String;)V
.end method
