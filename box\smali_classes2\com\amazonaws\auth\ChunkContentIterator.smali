.class Lcom/amazonaws/auth/ChunkContentIterator;
.super Ljava/lang/Object;


# instance fields
.field public final a:[B

.field public b:I


# direct methods
.method public constructor <init>([B)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/amazonaws/auth/ChunkContentIterator;->a:[B

    return-void
.end method


# virtual methods
.method public a()Z
    .locals 2

    iget v0, p0, Lcom/amazonaws/auth/ChunkContentIterator;->b:I

    iget-object v1, p0, Lcom/amazonaws/auth/ChunkContentIterator;->a:[B

    array-length v1, v1

    if-ge v0, v1, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method public b([BII)I
    .locals 2

    if-nez p3, :cond_0

    const/4 p1, 0x0

    return p1

    :cond_0
    invoke-virtual {p0}, Lcom/amazonaws/auth/ChunkContentIterator;->a()Z

    move-result v0

    if-nez v0, :cond_1

    const/4 p1, -0x1

    return p1

    :cond_1
    iget-object v0, p0, Lcom/amazonaws/auth/ChunkContentIterator;->a:[B

    array-length v0, v0

    iget v1, p0, Lcom/amazonaws/auth/ChunkContentIterator;->b:I

    sub-int/2addr v0, v1

    invoke-static {v0, p3}, Ljava/lang/Math;->min(II)I

    move-result p3

    iget-object v0, p0, Lcom/amazonaws/auth/ChunkContentIterator;->a:[B

    iget v1, p0, Lcom/amazonaws/auth/ChunkContentIterator;->b:I

    invoke-static {v0, v1, p1, p2, p3}, Ljava/lang/System;->arraycopy(Ljava/lang/Object;ILjava/lang/Object;II)V

    iget p1, p0, Lcom/amazonaws/auth/ChunkContentIterator;->b:I

    add-int/2addr p1, p3

    iput p1, p0, Lcom/amazonaws/auth/ChunkContentIterator;->b:I

    return p3
.end method
