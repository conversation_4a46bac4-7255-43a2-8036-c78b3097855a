.class public interface abstract Lcom/amazonaws/auth/Signer;
.super Ljava/lang/Object;


# virtual methods
.method public abstract b(Lcom/amazonaws/Request;Lcom/amazonaws/auth/AWSCredentials;)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/amazonaws/Request<",
            "*>;",
            "Lcom/amazonaws/auth/AWSCredentials;",
            ")V"
        }
    .end annotation
.end method
