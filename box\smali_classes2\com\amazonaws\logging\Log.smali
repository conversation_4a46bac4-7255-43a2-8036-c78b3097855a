.class public interface abstract Lcom/amazonaws/logging/Log;
.super Ljava/lang/Object;


# virtual methods
.method public abstract a(Ljava/lang/Object;)V
.end method

.method public abstract b()Z
.end method

.method public abstract c()Z
.end method

.method public abstract d(Ljava/lang/Object;)V
.end method

.method public abstract e(Ljava/lang/Object;Ljava/lang/Throwable;)V
.end method

.method public abstract f(Ljava/lang/Object;)V
.end method

.method public abstract g(Ljava/lang/Object;Ljava/lang/Throwable;)V
.end method

.method public abstract h()Z
.end method

.method public abstract i(Ljava/lang/Object;Ljava/lang/Throwable;)V
.end method

.method public abstract j(Ljava/lang/Object;)V
.end method

.method public abstract k(Ljava/lang/Object;)V
.end method
