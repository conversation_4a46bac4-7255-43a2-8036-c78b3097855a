.class public interface abstract Lcom/amazonaws/services/s3/model/S3DataSource;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/amazonaws/services/s3/model/S3DataSource$Utils;
    }
.end annotation


# virtual methods
.method public abstract setFile(Ljava/io/File;)V
.end method

.method public abstract setInputStream(Ljava/io/InputStream;)V
.end method
