.class public interface abstract Lcom/amazonaws/services/s3/model/analytics/AnalyticsPredicateVisitor;
.super Ljava/lang/Object;


# virtual methods
.method public abstract a(Lcom/amazonaws/services/s3/model/analytics/AnalyticsPrefixPredicate;)V
.end method

.method public abstract b(Lcom/amazonaws/services/s3/model/analytics/AnalyticsTagPredicate;)V
.end method

.method public abstract c(Lcom/amazonaws/services/s3/model/analytics/AnalyticsAndOperator;)V
.end method
