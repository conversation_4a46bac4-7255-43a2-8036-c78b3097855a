.class public abstract Lcom/amazonaws/services/s3/model/lifecycle/LifecycleFilterPredicate;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/io/Serializable;


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public abstract accept(Lcom/amazonaws/services/s3/model/lifecycle/LifecyclePredicateVisitor;)V
.end method
