.class public interface abstract Lcom/amazonaws/services/s3/model/lifecycle/LifecyclePredicateVisitor;
.super Ljava/lang/Object;


# virtual methods
.method public abstract a(Lcom/amazonaws/services/s3/model/lifecycle/LifecyclePrefixPredicate;)V
.end method

.method public abstract b(Lcom/amazonaws/services/s3/model/lifecycle/LifecycleTagPredicate;)V
.end method

.method public abstract c(Lcom/amazonaws/services/s3/model/lifecycle/LifecycleAndOperator;)V
.end method
