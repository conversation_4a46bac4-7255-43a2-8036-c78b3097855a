.class public interface abstract Lcom/amazonaws/services/s3/model/metrics/MetricsPredicateVisitor;
.super Ljava/lang/Object;


# virtual methods
.method public abstract a(Lcom/amazonaws/services/s3/model/metrics/MetricsAndOperator;)V
.end method

.method public abstract b(Lcom/amazonaws/services/s3/model/metrics/MetricsTagPredicate;)V
.end method

.method public abstract c(Lcom/amazonaws/services/s3/model/metrics/MetricsPrefixPredicate;)V
.end method
