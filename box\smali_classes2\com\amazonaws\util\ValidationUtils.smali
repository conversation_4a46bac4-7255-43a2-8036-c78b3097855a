.class public Lcom/amazonaws/util/ValidationUtils;
.super Ljava/lang/Object;


# direct methods
.method public static a(L<PERSON><PERSON>/lang/Object;Ljava/lang/String;)V
    .locals 0

    if-eqz p0, :cond_0

    return-void

    :cond_0
    new-instance p0, Ljava/lang/IllegalArgumentException;

    invoke-direct {p0, p1}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p0
.end method
