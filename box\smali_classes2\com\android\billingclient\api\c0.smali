.class public final Lcom/android/billingclient/api/c0;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/util/concurrent/Callable;


# instance fields
.field public final synthetic a:Ljava/lang/String;

.field public final synthetic b:Lcom/android/billingclient/api/u;

.field public final synthetic c:Lcom/android/billingclient/api/i;


# direct methods
.method public constructor <init>(Lcom/android/billingclient/api/i;Ljava/lang/String;Lcom/android/billingclient/api/u;)V
    .locals 0

    iput-object p1, p0, Lcom/android/billingclient/api/c0;->c:Lcom/android/billingclient/api/i;

    iput-object p2, p0, Lcom/android/billingclient/api/c0;->a:Ljava/lang/String;

    iput-object p3, p0, Lcom/android/billingclient/api/c0;->b:Lcom/android/billingclient/api/u;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final bridge synthetic call()Ljava/lang/Object;
    .locals 3
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/Exception;
        }
    .end annotation

    iget-object v0, p0, Lcom/android/billingclient/api/c0;->c:Lcom/android/billingclient/api/i;

    iget-object v1, p0, Lcom/android/billingclient/api/c0;->a:Ljava/lang/String;

    const/16 v2, 0x9

    invoke-static {v0, v1, v2}, Lcom/android/billingclient/api/i;->E(Lcom/android/billingclient/api/i;Ljava/lang/String;I)Lcom/android/billingclient/api/e1;

    move-result-object v0

    invoke-virtual {v0}, Lcom/android/billingclient/api/e1;->b()Ljava/util/List;

    move-result-object v1

    if-eqz v1, :cond_0

    iget-object v1, p0, Lcom/android/billingclient/api/c0;->b:Lcom/android/billingclient/api/u;

    invoke-virtual {v0}, Lcom/android/billingclient/api/e1;->a()Lcom/android/billingclient/api/n;

    move-result-object v2

    invoke-virtual {v0}, Lcom/android/billingclient/api/e1;->b()Ljava/util/List;

    move-result-object v0

    invoke-interface {v1, v2, v0}, Lcom/android/billingclient/api/u;->a(Lcom/android/billingclient/api/n;Ljava/util/List;)V

    goto :goto_0

    :cond_0
    iget-object v1, p0, Lcom/android/billingclient/api/c0;->b:Lcom/android/billingclient/api/u;

    invoke-virtual {v0}, Lcom/android/billingclient/api/e1;->a()Lcom/android/billingclient/api/n;

    move-result-object v0

    invoke-static {}, Lcom/google/android/gms/internal/play_billing/zzaf;->zzk()Lcom/google/android/gms/internal/play_billing/zzaf;

    move-result-object v2

    invoke-interface {v1, v0, v2}, Lcom/android/billingclient/api/u;->a(Lcom/android/billingclient/api/n;Ljava/util/List;)V

    :goto_0
    const/4 v0, 0x0

    return-object v0
.end method
