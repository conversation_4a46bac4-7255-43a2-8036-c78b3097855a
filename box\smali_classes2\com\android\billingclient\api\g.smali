.class public interface abstract Lcom/android/billingclient/api/g;
.super Ljava/lang/Object;


# virtual methods
.method public abstract a(Lcom/android/billingclient/api/n;Lcom/android/billingclient/api/f;)V
    .param p1    # Lcom/android/billingclient/api/n;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p2    # Lcom/android/billingclient/api/f;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
.end method
