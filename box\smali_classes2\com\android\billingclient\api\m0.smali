.class public final synthetic Lcom/android/billingclient/api/m0;
.super Ljava/lang/Object;


# direct methods
.method public static a(IILcom/android/billingclient/api/n;)Lcom/google/android/gms/internal/play_billing/m3;
    .locals 3
    .annotation build Landroidx/annotation/Nullable;
    .end annotation

    :try_start_0
    invoke-static {}, Lcom/google/android/gms/internal/play_billing/m3;->u()Lcom/google/android/gms/internal/play_billing/l3;

    move-result-object v0

    invoke-static {}, Lcom/google/android/gms/internal/play_billing/w3;->u()Lcom/google/android/gms/internal/play_billing/s3;

    move-result-object v1

    invoke-virtual {p2}, Lcom/android/billingclient/api/n;->b()I

    move-result v2

    invoke-virtual {v1, v2}, Lcom/google/android/gms/internal/play_billing/s3;->i(I)Lcom/google/android/gms/internal/play_billing/s3;

    invoke-virtual {p2}, Lcom/android/billingclient/api/n;->a()Ljava/lang/String;

    move-result-object p2

    invoke-virtual {v1, p2}, Lcom/google/android/gms/internal/play_billing/s3;->h(Ljava/lang/String;)Lcom/google/android/gms/internal/play_billing/s3;

    invoke-virtual {v1, p0}, Lcom/google/android/gms/internal/play_billing/s3;->j(I)Lcom/google/android/gms/internal/play_billing/s3;

    invoke-virtual {v0, v1}, Lcom/google/android/gms/internal/play_billing/l3;->g(Lcom/google/android/gms/internal/play_billing/s3;)Lcom/google/android/gms/internal/play_billing/l3;

    invoke-virtual {v0, p1}, Lcom/google/android/gms/internal/play_billing/l3;->h(I)Lcom/google/android/gms/internal/play_billing/l3;

    invoke-virtual {v0}, Lcom/google/android/gms/internal/play_billing/s0;->c()Lcom/google/android/gms/internal/play_billing/v0;

    move-result-object p0

    check-cast p0, Lcom/google/android/gms/internal/play_billing/m3;
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    return-object p0

    :catch_0
    move-exception p0

    const-string p1, "BillingLogger"

    const-string p2, "Unable to create logging payload"

    invoke-static {p1, p2, p0}, Lcom/google/android/gms/internal/play_billing/j;->l(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Throwable;)V

    const/4 p0, 0x0

    return-object p0
.end method

.method public static b(I)Lcom/google/android/gms/internal/play_billing/q3;
    .locals 2
    .annotation build Landroidx/annotation/Nullable;
    .end annotation

    :try_start_0
    invoke-static {}, Lcom/google/android/gms/internal/play_billing/q3;->t()Lcom/google/android/gms/internal/play_billing/p3;

    move-result-object v0

    invoke-virtual {v0, p0}, Lcom/google/android/gms/internal/play_billing/p3;->g(I)Lcom/google/android/gms/internal/play_billing/p3;

    invoke-virtual {v0}, Lcom/google/android/gms/internal/play_billing/s0;->c()Lcom/google/android/gms/internal/play_billing/v0;

    move-result-object p0

    check-cast p0, Lcom/google/android/gms/internal/play_billing/q3;
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    return-object p0

    :catch_0
    move-exception p0

    const-string v0, "BillingLogger"

    const-string v1, "Unable to create logging payload"

    invoke-static {v0, v1, p0}, Lcom/google/android/gms/internal/play_billing/j;->l(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Throwable;)V

    const/4 p0, 0x0

    return-object p0
.end method
