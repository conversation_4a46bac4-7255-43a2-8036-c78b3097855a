.class public final Lcom/android/billingclient/api/q0;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/android/billingclient/api/n0;


# instance fields
.field public final a:Lcom/google/android/gms/internal/play_billing/c4;

.field public final b:Lcom/android/billingclient/api/s0;


# direct methods
.method public constructor <init>(Landroid/content/Context;Lcom/google/android/gms/internal/play_billing/c4;)V
    .locals 1

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    new-instance v0, Lcom/android/billingclient/api/s0;

    invoke-direct {v0, p1}, Lcom/android/billingclient/api/s0;-><init>(Landroid/content/Context;)V

    iput-object v0, p0, Lcom/android/billingclient/api/q0;->b:Lcom/android/billingclient/api/s0;

    iput-object p2, p0, Lcom/android/billingclient/api/q0;->a:Lcom/google/android/gms/internal/play_billing/c4;

    return-void
.end method


# virtual methods
.method public final a(Lcom/google/android/gms/internal/play_billing/m3;)V
    .locals 2
    .param p1    # Lcom/google/android/gms/internal/play_billing/m3;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    if-nez p1, :cond_0

    return-void

    :cond_0
    :try_start_0
    invoke-static {}, Lcom/google/android/gms/internal/play_billing/j4;->u()Lcom/google/android/gms/internal/play_billing/i4;

    move-result-object v0

    iget-object v1, p0, Lcom/android/billingclient/api/q0;->a:Lcom/google/android/gms/internal/play_billing/c4;

    if-eqz v1, :cond_1

    invoke-virtual {v0, v1}, Lcom/google/android/gms/internal/play_billing/i4;->i(Lcom/google/android/gms/internal/play_billing/c4;)Lcom/google/android/gms/internal/play_billing/i4;

    :cond_1
    invoke-virtual {v0, p1}, Lcom/google/android/gms/internal/play_billing/i4;->g(Lcom/google/android/gms/internal/play_billing/m3;)Lcom/google/android/gms/internal/play_billing/i4;

    iget-object p1, p0, Lcom/android/billingclient/api/q0;->b:Lcom/android/billingclient/api/s0;

    invoke-virtual {v0}, Lcom/google/android/gms/internal/play_billing/s0;->c()Lcom/google/android/gms/internal/play_billing/v0;

    move-result-object v0

    check-cast v0, Lcom/google/android/gms/internal/play_billing/j4;

    invoke-virtual {p1, v0}, Lcom/android/billingclient/api/s0;->a(Lcom/google/android/gms/internal/play_billing/j4;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    return-void

    :catchall_0
    const-string p1, "BillingLogger"

    const-string v0, "Unable to log."

    invoke-static {p1, v0}, Lcom/google/android/gms/internal/play_billing/j;->k(Ljava/lang/String;Ljava/lang/String;)V

    return-void
.end method

.method public final b(Lcom/google/android/gms/internal/play_billing/n4;)V
    .locals 2
    .param p1    # Lcom/google/android/gms/internal/play_billing/n4;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    if-nez p1, :cond_0

    return-void

    :cond_0
    :try_start_0
    invoke-static {}, Lcom/google/android/gms/internal/play_billing/j4;->u()Lcom/google/android/gms/internal/play_billing/i4;

    move-result-object v0

    iget-object v1, p0, Lcom/android/billingclient/api/q0;->a:Lcom/google/android/gms/internal/play_billing/c4;

    if-eqz v1, :cond_1

    invoke-virtual {v0, v1}, Lcom/google/android/gms/internal/play_billing/i4;->i(Lcom/google/android/gms/internal/play_billing/c4;)Lcom/google/android/gms/internal/play_billing/i4;

    :cond_1
    invoke-virtual {v0, p1}, Lcom/google/android/gms/internal/play_billing/i4;->j(Lcom/google/android/gms/internal/play_billing/n4;)Lcom/google/android/gms/internal/play_billing/i4;

    iget-object p1, p0, Lcom/android/billingclient/api/q0;->b:Lcom/android/billingclient/api/s0;

    invoke-virtual {v0}, Lcom/google/android/gms/internal/play_billing/s0;->c()Lcom/google/android/gms/internal/play_billing/v0;

    move-result-object v0

    check-cast v0, Lcom/google/android/gms/internal/play_billing/j4;

    invoke-virtual {p1, v0}, Lcom/android/billingclient/api/s0;->a(Lcom/google/android/gms/internal/play_billing/j4;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    return-void

    :catchall_0
    const-string p1, "BillingLogger"

    const-string v0, "Unable to log."

    invoke-static {p1, v0}, Lcom/google/android/gms/internal/play_billing/j;->k(Ljava/lang/String;Ljava/lang/String;)V

    return-void
.end method

.method public final c(Lcom/google/android/gms/internal/play_billing/q3;)V
    .locals 2
    .param p1    # Lcom/google/android/gms/internal/play_billing/q3;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    if-nez p1, :cond_0

    return-void

    :cond_0
    :try_start_0
    invoke-static {}, Lcom/google/android/gms/internal/play_billing/j4;->u()Lcom/google/android/gms/internal/play_billing/i4;

    move-result-object v0

    iget-object v1, p0, Lcom/android/billingclient/api/q0;->a:Lcom/google/android/gms/internal/play_billing/c4;

    if-eqz v1, :cond_1

    invoke-virtual {v0, v1}, Lcom/google/android/gms/internal/play_billing/i4;->i(Lcom/google/android/gms/internal/play_billing/c4;)Lcom/google/android/gms/internal/play_billing/i4;

    :cond_1
    invoke-virtual {v0, p1}, Lcom/google/android/gms/internal/play_billing/i4;->h(Lcom/google/android/gms/internal/play_billing/q3;)Lcom/google/android/gms/internal/play_billing/i4;

    iget-object p1, p0, Lcom/android/billingclient/api/q0;->b:Lcom/android/billingclient/api/s0;

    invoke-virtual {v0}, Lcom/google/android/gms/internal/play_billing/s0;->c()Lcom/google/android/gms/internal/play_billing/v0;

    move-result-object v0

    check-cast v0, Lcom/google/android/gms/internal/play_billing/j4;

    invoke-virtual {p1, v0}, Lcom/android/billingclient/api/s0;->a(Lcom/google/android/gms/internal/play_billing/j4;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    return-void

    :catchall_0
    const-string p1, "BillingLogger"

    const-string v0, "Unable to log."

    invoke-static {p1, v0}, Lcom/google/android/gms/internal/play_billing/j;->k(Ljava/lang/String;Ljava/lang/String;)V

    return-void
.end method
