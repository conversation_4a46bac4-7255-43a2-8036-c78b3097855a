.class public interface abstract Lcom/android/billingclient/api/s;
.super Ljava/lang/Object;


# virtual methods
.method public abstract a(Lcom/android/billingclient/api/n;Ljava/util/List;)V
    .param p1    # Lcom/android/billingclient/api/n;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p2    # Ljava/util/List;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/android/billingclient/api/n;",
            "Ljava/util/List<",
            "Lcom/android/billingclient/api/r;",
            ">;)V"
        }
    .end annotation
.end method
