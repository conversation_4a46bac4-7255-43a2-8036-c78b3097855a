.class public final Lcom/android/billingclient/api/s0;
.super Ljava/lang/Object;


# instance fields
.field public a:Z

.field public b:Lv8/g;


# direct methods
.method public constructor <init>(Landroid/content/Context;)V
    .locals 4

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    :try_start_0
    invoke-static {p1}, Lcom/google/android/datatransport/runtime/t;->f(Landroid/content/Context;)V

    invoke-static {}, Lcom/google/android/datatransport/runtime/t;->c()Lcom/google/android/datatransport/runtime/t;

    move-result-object p1

    sget-object v0, Lw8/a;->g:Lw8/a;

    invoke-virtual {p1, v0}, Lcom/google/android/datatransport/runtime/t;->g(Lcom/google/android/datatransport/runtime/f;)Lv8/h;

    move-result-object p1

    const-string v0, "PLAY_BILLING_LIBRARY"

    const-class v1, Lcom/google/android/gms/internal/play_billing/j4;

    const-string v2, "proto"

    invoke-static {v2}, Lv8/c;->b(Ljava/lang/String;)Lv8/c;

    move-result-object v2

    sget-object v3, Lcom/android/billingclient/api/r0;->a:Lcom/android/billingclient/api/r0;

    invoke-interface {p1, v0, v1, v2, v3}, Lv8/h;->b(Ljava/lang/String;Ljava/lang/Class;Lv8/c;Lv8/f;)Lv8/g;

    move-result-object p1

    iput-object p1, p0, Lcom/android/billingclient/api/s0;->b:Lv8/g;
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    return-void

    :catchall_0
    const/4 p1, 0x1

    iput-boolean p1, p0, Lcom/android/billingclient/api/s0;->a:Z

    return-void
.end method


# virtual methods
.method public final a(Lcom/google/android/gms/internal/play_billing/j4;)V
    .locals 2

    iget-boolean v0, p0, Lcom/android/billingclient/api/s0;->a:Z

    const-string v1, "BillingLogger"

    if-eqz v0, :cond_0

    const-string p1, "Skipping logging since initialization failed."

    invoke-static {v1, p1}, Lcom/google/android/gms/internal/play_billing/j;->k(Ljava/lang/String;Ljava/lang/String;)V

    return-void

    :cond_0
    :try_start_0
    iget-object v0, p0, Lcom/android/billingclient/api/s0;->b:Lv8/g;

    invoke-static {p1}, Lv8/d;->e(Ljava/lang/Object;)Lv8/d;

    move-result-object p1

    invoke-interface {v0, p1}, Lv8/g;->b(Lv8/d;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    return-void

    :catchall_0
    const-string p1, "logging failed."

    invoke-static {v1, p1}, Lcom/google/android/gms/internal/play_billing/j;->k(Ljava/lang/String;Ljava/lang/String;)V

    return-void
.end method
