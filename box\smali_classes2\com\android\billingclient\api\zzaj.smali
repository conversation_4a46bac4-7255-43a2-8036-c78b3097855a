.class final Lcom/android/billingclient/api/zzaj;
.super Landroid/os/ResultReceiver;


# virtual methods
.method public final onReceiveResult(ILandroid/os/Bundle;)V
    .locals 0
    .param p2    # Landroid/os/Bundle;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    const-string p1, "BillingClient"

    invoke-static {p2, p1}, Lcom/google/android/gms/internal/play_billing/j;->f(Landroid/os/Bundle;Ljava/lang/String;)Lcom/android/billingclient/api/q;

    const/4 p1, 0x0

    throw p1
.end method
