.class public Lcom/android/volley/ClientError;
.super Lcom/android/volley/ServerError;


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Lcom/android/volley/ServerError;-><init>()V

    return-void
.end method

.method public constructor <init>(Lcom/android/volley/g;)V
    .locals 0

    invoke-direct {p0, p1}, Lcom/android/volley/ServerError;-><init>(Lcom/android/volley/g;)V

    return-void
.end method
