.class public interface abstract Lcom/android/volley/c;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/android/volley/c$a;
    }
.end annotation


# virtual methods
.method public abstract a(Ljava/lang/String;)Lcom/android/volley/c$a;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end method

.method public abstract b(Ljava/lang/String;Z)V
.end method

.method public abstract c(Ljava/lang/String;Lcom/android/volley/c$a;)V
.end method

.method public abstract initialize()V
.end method
