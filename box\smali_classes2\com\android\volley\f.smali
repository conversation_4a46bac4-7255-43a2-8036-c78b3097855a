.class public interface abstract Lcom/android/volley/f;
.super Ljava/lang/Object;


# virtual methods
.method public abstract a(Lcom/android/volley/Request;)Lcom/android/volley/g;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/android/volley/Request<",
            "*>;)",
            "Lcom/android/volley/g;"
        }
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lcom/android/volley/VolleyError;
        }
    .end annotation
.end method
