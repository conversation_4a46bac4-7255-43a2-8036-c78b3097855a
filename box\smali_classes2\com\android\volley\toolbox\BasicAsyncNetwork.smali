.class public Lcom/android/volley/toolbox/BasicAsyncNetwork;
.super Lcom/android/volley/b;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/android/volley/toolbox/BasicAsyncNetwork$ResponseParsingTask;,
        Lcom/android/volley/toolbox/BasicAsyncNetwork$InvokeRetryPolicyTask;
    }
.end annotation


# direct methods
.method public static synthetic b(Lcom/android/volley/toolbox/BasicAsyncNetwork;Lcom/android/volley/Request;Lcom/android/volley/b$a;Ljava/io/IOException;JLcom/android/volley/toolbox/e;[B)V
    .locals 0

    const/4 p0, 0x0

    throw p0
.end method

.method public static synthetic c(Lcom/android/volley/toolbox/BasicAsyncNetwork;)Lcom/android/volley/toolbox/ByteArrayPool;
    .locals 0

    const/4 p0, 0x0

    throw p0
.end method

.method public static synthetic d(Lcom/android/volley/toolbox/BasicAsyncNetwork;JILcom/android/volley/toolbox/e;Lcom/android/volley/Request;Lcom/android/volley/b$a;Ljava/util/List;[B)V
    .locals 0

    const/4 p0, 0x0

    throw p0
.end method
