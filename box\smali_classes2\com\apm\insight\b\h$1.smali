.class Lcom/apm/insight/b/h$1;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/apm/insight/b/h$c;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/apm/insight/b/h;-><init>(IZ)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic a:Lcom/apm/insight/b/h;


# direct methods
.method public constructor <init>(Lcom/apm/insight/b/h;)V
    .locals 0

    iput-object p1, p0, Lcom/apm/insight/b/h$1;->a:Lcom/apm/insight/b/h;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
