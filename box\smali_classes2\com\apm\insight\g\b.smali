.class public abstract Lcom/apm/insight/g/b;
.super Ljava/lang/Object;


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public abstract a(Ljava/lang/Throwable;Ljava/lang/Thread;)I
.end method

.method public abstract b(Ljava/lang/Throwable;Ljava/lang/Thread;)V
.end method
