.class public final enum Lcom/apm/insight/k/e$b;
.super Ljava/lang/Enum;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/apm/insight/k/e;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x4019
    name = "b"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Enum<",
        "Lcom/apm/insight/k/e$b;",
        ">;"
    }
.end annotation


# static fields
.field public static final enum a:Lcom/apm/insight/k/e$b;

.field public static final enum b:Lcom/apm/insight/k/e$b;

.field public static final enum c:Lcom/apm/insight/k/e$b;

.field public static final enum d:Lcom/apm/insight/k/e$b;

.field public static final enum e:Lcom/apm/insight/k/e$b;

.field public static final enum f:Lcom/apm/insight/k/e$b;

.field public static final enum g:Lcom/apm/insight/k/e$b;

.field public static final enum h:Lcom/apm/insight/k/e$b;

.field public static final enum i:Lcom/apm/insight/k/e$b;

.field public static final enum j:Lcom/apm/insight/k/e$b;

.field public static final enum k:Lcom/apm/insight/k/e$b;

.field private static final synthetic m:[Lcom/apm/insight/k/e$b;


# instance fields
.field final l:I


# direct methods
.method static constructor <clinit>()V
    .locals 16

    new-instance v0, Lcom/apm/insight/k/e$b;

    const-string v1, "NONE"

    const/4 v2, 0x0

    invoke-direct {v0, v1, v2, v2}, Lcom/apm/insight/k/e$b;-><init>(Ljava/lang/String;II)V

    sput-object v0, Lcom/apm/insight/k/e$b;->a:Lcom/apm/insight/k/e$b;

    new-instance v1, Lcom/apm/insight/k/e$b;

    const-string v3, "MOBILE"

    const/4 v4, 0x1

    invoke-direct {v1, v3, v4, v4}, Lcom/apm/insight/k/e$b;-><init>(Ljava/lang/String;II)V

    sput-object v1, Lcom/apm/insight/k/e$b;->b:Lcom/apm/insight/k/e$b;

    new-instance v3, Lcom/apm/insight/k/e$b;

    const-string v5, "MOBILE_2G"

    const/4 v6, 0x2

    invoke-direct {v3, v5, v6, v6}, Lcom/apm/insight/k/e$b;-><init>(Ljava/lang/String;II)V

    sput-object v3, Lcom/apm/insight/k/e$b;->c:Lcom/apm/insight/k/e$b;

    new-instance v5, Lcom/apm/insight/k/e$b;

    const-string v7, "MOBILE_3G"

    const/4 v8, 0x3

    invoke-direct {v5, v7, v8, v8}, Lcom/apm/insight/k/e$b;-><init>(Ljava/lang/String;II)V

    sput-object v5, Lcom/apm/insight/k/e$b;->d:Lcom/apm/insight/k/e$b;

    new-instance v7, Lcom/apm/insight/k/e$b;

    const-string v9, "WIFI"

    const/4 v10, 0x4

    invoke-direct {v7, v9, v10, v10}, Lcom/apm/insight/k/e$b;-><init>(Ljava/lang/String;II)V

    sput-object v7, Lcom/apm/insight/k/e$b;->e:Lcom/apm/insight/k/e$b;

    new-instance v9, Lcom/apm/insight/k/e$b;

    const-string v11, "MOBILE_4G"

    const/4 v12, 0x5

    invoke-direct {v9, v11, v12, v12}, Lcom/apm/insight/k/e$b;-><init>(Ljava/lang/String;II)V

    sput-object v9, Lcom/apm/insight/k/e$b;->f:Lcom/apm/insight/k/e$b;

    new-instance v11, Lcom/apm/insight/k/e$b;

    const-string v13, "MOBILE_5G"

    const/4 v14, 0x6

    invoke-direct {v11, v13, v14, v14}, Lcom/apm/insight/k/e$b;-><init>(Ljava/lang/String;II)V

    sput-object v11, Lcom/apm/insight/k/e$b;->g:Lcom/apm/insight/k/e$b;

    new-instance v13, Lcom/apm/insight/k/e$b;

    const-string v15, "WIFI_24GHZ"

    const/4 v14, 0x7

    invoke-direct {v13, v15, v14, v14}, Lcom/apm/insight/k/e$b;-><init>(Ljava/lang/String;II)V

    sput-object v13, Lcom/apm/insight/k/e$b;->h:Lcom/apm/insight/k/e$b;

    new-instance v15, Lcom/apm/insight/k/e$b;

    const-string v14, "WIFI_5GHZ"

    const/16 v12, 0x8

    invoke-direct {v15, v14, v12, v12}, Lcom/apm/insight/k/e$b;-><init>(Ljava/lang/String;II)V

    sput-object v15, Lcom/apm/insight/k/e$b;->i:Lcom/apm/insight/k/e$b;

    new-instance v14, Lcom/apm/insight/k/e$b;

    const-string v12, "MOBILE_3G_H"

    const/16 v10, 0x9

    invoke-direct {v14, v12, v10, v10}, Lcom/apm/insight/k/e$b;-><init>(Ljava/lang/String;II)V

    sput-object v14, Lcom/apm/insight/k/e$b;->j:Lcom/apm/insight/k/e$b;

    new-instance v12, Lcom/apm/insight/k/e$b;

    const-string v10, "MOBILE_3G_HP"

    const/16 v8, 0xa

    invoke-direct {v12, v10, v8, v8}, Lcom/apm/insight/k/e$b;-><init>(Ljava/lang/String;II)V

    sput-object v12, Lcom/apm/insight/k/e$b;->k:Lcom/apm/insight/k/e$b;

    const/16 v10, 0xb

    new-array v10, v10, [Lcom/apm/insight/k/e$b;

    aput-object v0, v10, v2

    aput-object v1, v10, v4

    aput-object v3, v10, v6

    const/4 v0, 0x3

    aput-object v5, v10, v0

    const/4 v0, 0x4

    aput-object v7, v10, v0

    const/4 v0, 0x5

    aput-object v9, v10, v0

    const/4 v0, 0x6

    aput-object v11, v10, v0

    const/4 v0, 0x7

    aput-object v13, v10, v0

    const/16 v0, 0x8

    aput-object v15, v10, v0

    const/16 v0, 0x9

    aput-object v14, v10, v0

    aput-object v12, v10, v8

    sput-object v10, Lcom/apm/insight/k/e$b;->m:[Lcom/apm/insight/k/e$b;

    return-void
.end method

.method private constructor <init>(Ljava/lang/String;II)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I)V"
        }
    .end annotation

    invoke-direct {p0, p1, p2}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    iput p3, p0, Lcom/apm/insight/k/e$b;->l:I

    return-void
.end method

.method public static valueOf(Ljava/lang/String;)Lcom/apm/insight/k/e$b;
    .locals 1

    const-class v0, Lcom/apm/insight/k/e$b;

    invoke-static {v0, p0}, Ljava/lang/Enum;->valueOf(Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Enum;

    move-result-object p0

    check-cast p0, Lcom/apm/insight/k/e$b;

    return-object p0
.end method

.method public static values()[Lcom/apm/insight/k/e$b;
    .locals 1

    sget-object v0, Lcom/apm/insight/k/e$b;->m:[Lcom/apm/insight/k/e$b;

    invoke-virtual {v0}, [Lcom/apm/insight/k/e$b;->clone()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [Lcom/apm/insight/k/e$b;

    return-object v0
.end method
