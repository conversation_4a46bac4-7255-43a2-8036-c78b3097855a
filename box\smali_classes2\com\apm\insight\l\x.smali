.class public final Lcom/apm/insight/l/x;
.super Ljava/lang/Object;


# direct methods
.method public static a(L<PERSON>va/lang/Runnable;Ljava/lang/String;)Ljava/lang/Thread;
    .locals 1

    if-eqz p0, :cond_1

    if-nez p1, :cond_0

    new-instance p1, <PERSON><PERSON><PERSON>/lang/Thread;

    invoke-direct {p1, p0}, Ljava/lang/Thread;-><init>(Ljava/lang/Runnable;)V

    goto :goto_0

    :cond_0
    new-instance v0, Ljava/lang/Thread;

    invoke-direct {v0, p0, p1}, L<PERSON>va/lang/Thread;-><init>(Ljava/lang/Runnable;Ljava/lang/String;)V

    move-object p1, v0

    :goto_0
    invoke-virtual {p1}, Ljava/lang/Thread;->start()V

    return-object p1

    :cond_1
    const/4 p0, 0x0

    return-object p0
.end method
