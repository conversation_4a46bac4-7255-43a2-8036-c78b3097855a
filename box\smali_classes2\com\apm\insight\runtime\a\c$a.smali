.class public interface abstract Lcom/apm/insight/runtime/a/c$a;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/apm/insight/runtime/a/c;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "a"
.end annotation


# virtual methods
.method public abstract a(ILcom/apm/insight/entity/a;)Lcom/apm/insight/entity/a;
.end method

.method public abstract a(ILcom/apm/insight/entity/a;Z)Lcom/apm/insight/entity/a;
.end method

.method public abstract a(Ljava/lang/Throwable;)V
.end method
