.class final Lcom/avery/subtitle/SubtitleLoader$loadFromLocalAsync$1$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/avery/subtitle/SubtitleLoader$loadFromLocalAsync$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/k0;",
        "Lkotlin/coroutines/Continuation<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation

.annotation runtime Lkotlin/coroutines/jvm/internal/DebugMetadata;
    c = "com.avery.subtitle.SubtitleLoader$loadFromLocalAsync$1$2"
    f = "SubtitleLoader.kt"
    l = {}
    m = "invokeSuspend"
.end annotation


# instance fields
.field final synthetic $callback:Lcom/avery/subtitle/SubtitleLoader$a;

.field final synthetic $e:Ljava/lang/Exception;

.field label:I


# direct methods
.method public constructor <init>(Lcom/avery/subtitle/SubtitleLoader$a;Ljava/lang/Exception;Lkotlin/coroutines/Continuation;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/avery/subtitle/SubtitleLoader$a;",
            "Ljava/lang/Exception;",
            "Lkotlin/coroutines/Continuation<",
            "-",
            "Lcom/avery/subtitle/SubtitleLoader$loadFromLocalAsync$1$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lcom/avery/subtitle/SubtitleLoader$loadFromLocalAsync$1$2;->$callback:Lcom/avery/subtitle/SubtitleLoader$a;

    iput-object p2, p0, Lcom/avery/subtitle/SubtitleLoader$loadFromLocalAsync$1$2;->$e:Ljava/lang/Exception;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p3}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/Continuation;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/Continuation;)Lkotlin/coroutines/Continuation;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/Continuation<",
            "*>;)",
            "Lkotlin/coroutines/Continuation<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance p1, Lcom/avery/subtitle/SubtitleLoader$loadFromLocalAsync$1$2;

    iget-object v0, p0, Lcom/avery/subtitle/SubtitleLoader$loadFromLocalAsync$1$2;->$callback:Lcom/avery/subtitle/SubtitleLoader$a;

    iget-object v1, p0, Lcom/avery/subtitle/SubtitleLoader$loadFromLocalAsync$1$2;->$e:Ljava/lang/Exception;

    invoke-direct {p1, v0, v1, p2}, Lcom/avery/subtitle/SubtitleLoader$loadFromLocalAsync$1$2;-><init>(Lcom/avery/subtitle/SubtitleLoader$a;Ljava/lang/Exception;Lkotlin/coroutines/Continuation;)V

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    check-cast p1, Lkotlinx/coroutines/k0;

    check-cast p2, Lkotlin/coroutines/Continuation;

    invoke-virtual {p0, p1, p2}, Lcom/avery/subtitle/SubtitleLoader$loadFromLocalAsync$1$2;->invoke(Lkotlinx/coroutines/k0;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/k0;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/k0;",
            "Lkotlin/coroutines/Continuation<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    invoke-virtual {p0, p1, p2}, Lcom/avery/subtitle/SubtitleLoader$loadFromLocalAsync$1$2;->create(Ljava/lang/Object;Lkotlin/coroutines/Continuation;)Lkotlin/coroutines/Continuation;

    move-result-object p1

    check-cast p1, Lcom/avery/subtitle/SubtitleLoader$loadFromLocalAsync$1$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lcom/avery/subtitle/SubtitleLoader$loadFromLocalAsync$1$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    invoke-static {}, Lkotlin/coroutines/intrinsics/IntrinsicsKt;->e()Ljava/lang/Object;

    iget v0, p0, Lcom/avery/subtitle/SubtitleLoader$loadFromLocalAsync$1$2;->label:I

    if-nez v0, :cond_0

    invoke-static {p1}, Lkotlin/ResultKt;->b(Ljava/lang/Object;)V

    iget-object p1, p0, Lcom/avery/subtitle/SubtitleLoader$loadFromLocalAsync$1$2;->$callback:Lcom/avery/subtitle/SubtitleLoader$a;

    iget-object v0, p0, Lcom/avery/subtitle/SubtitleLoader$loadFromLocalAsync$1$2;->$e:Ljava/lang/Exception;

    invoke-interface {p1, v0}, Lcom/avery/subtitle/SubtitleLoader$a;->b(Ljava/lang/Exception;)V

    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    return-object p1

    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw p1
.end method
