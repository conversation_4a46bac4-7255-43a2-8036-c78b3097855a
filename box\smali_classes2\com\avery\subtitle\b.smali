.class public interface abstract Lcom/avery/subtitle/b;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/avery/subtitle/b$a;,
        Lcom/avery/subtitle/b$b;
    }
.end annotation


# virtual methods
.method public abstract destroy()V
.end method

.method public abstract initSubtitle(Lb6/a;Z)V
.end method

.method public abstract pause()V
.end method

.method public abstract refreshImmediately()V
.end method

.method public abstract reset()V
.end method

.method public abstract resume()V
.end method

.method public abstract selectSubtitle(Ljava/lang/String;Ljava/lang/String;Lb6/b;)V
.end method

.method public abstract setDefaultSubtitle(Ljava/lang/String;)V
.end method

.method public abstract setOnSubtitleChangeListener(Lcom/avery/subtitle/b$a;)V
.end method

.method public abstract setOnSubtitlePreparedListener(Lcom/avery/subtitle/b$b;)V
.end method

.method public abstract setSubtitlePath(Ljava/lang/String;Ljava/lang/String;)V
.end method

.method public abstract start()V
.end method

.method public abstract stop()V
.end method

.method public abstract subtitleDelay(J)V
.end method
