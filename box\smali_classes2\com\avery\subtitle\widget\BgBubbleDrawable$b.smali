.class public Lcom/avery/subtitle/widget/BgBubbleDrawable$b;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/avery/subtitle/widget/BgBubbleDrawable;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "b"
.end annotation


# static fields
.field public static A:F = 0.0f

.field public static B:I = -0x4d000000

.field public static C:F = 0.0f

.field public static u:F = 25.0f

.field public static v:F = 25.0f

.field public static w:F = 20.0f

.field public static x:F = 50.0f

.field public static y:I = -0x10000

.field public static z:I = 0xff


# instance fields
.field public a:Landroid/graphics/RectF;

.field public b:F

.field public c:I

.field public d:F

.field public e:F

.field public f:F

.field public g:F

.field public h:F

.field public i:F

.field public j:I

.field public k:I

.field public l:Landroid/graphics/Bitmap;

.field public m:Lcom/avery/subtitle/widget/BgBubbleDrawable$BubbleType;

.field public n:Lcom/avery/subtitle/widget/BgBubbleDrawable$ArrowLocation;

.field public o:Z

.field public p:F

.field public q:F

.field public r:F

.field public s:F

.field public t:[I


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>()V
    .locals 1

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    sget v0, Lcom/avery/subtitle/widget/BgBubbleDrawable$b;->A:F

    iput v0, p0, Lcom/avery/subtitle/widget/BgBubbleDrawable$b;->b:F

    sget v0, Lcom/avery/subtitle/widget/BgBubbleDrawable$b;->B:I

    iput v0, p0, Lcom/avery/subtitle/widget/BgBubbleDrawable$b;->c:I

    sget v0, Lcom/avery/subtitle/widget/BgBubbleDrawable$b;->C:F

    iput v0, p0, Lcom/avery/subtitle/widget/BgBubbleDrawable$b;->d:F

    iput v0, p0, Lcom/avery/subtitle/widget/BgBubbleDrawable$b;->e:F

    sget v0, Lcom/avery/subtitle/widget/BgBubbleDrawable$b;->u:F

    iput v0, p0, Lcom/avery/subtitle/widget/BgBubbleDrawable$b;->f:F

    sget v0, Lcom/avery/subtitle/widget/BgBubbleDrawable$b;->w:F

    iput v0, p0, Lcom/avery/subtitle/widget/BgBubbleDrawable$b;->g:F

    sget v0, Lcom/avery/subtitle/widget/BgBubbleDrawable$b;->v:F

    iput v0, p0, Lcom/avery/subtitle/widget/BgBubbleDrawable$b;->h:F

    sget v0, Lcom/avery/subtitle/widget/BgBubbleDrawable$b;->x:F

    iput v0, p0, Lcom/avery/subtitle/widget/BgBubbleDrawable$b;->i:F

    sget v0, Lcom/avery/subtitle/widget/BgBubbleDrawable$b;->y:I

    iput v0, p0, Lcom/avery/subtitle/widget/BgBubbleDrawable$b;->j:I

    sget v0, Lcom/avery/subtitle/widget/BgBubbleDrawable$b;->z:I

    iput v0, p0, Lcom/avery/subtitle/widget/BgBubbleDrawable$b;->k:I

    sget-object v0, Lcom/avery/subtitle/widget/BgBubbleDrawable$BubbleType;->COLOR:Lcom/avery/subtitle/widget/BgBubbleDrawable$BubbleType;

    iput-object v0, p0, Lcom/avery/subtitle/widget/BgBubbleDrawable$b;->m:Lcom/avery/subtitle/widget/BgBubbleDrawable$BubbleType;

    sget-object v0, Lcom/avery/subtitle/widget/BgBubbleDrawable$ArrowLocation;->LEFT:Lcom/avery/subtitle/widget/BgBubbleDrawable$ArrowLocation;

    iput-object v0, p0, Lcom/avery/subtitle/widget/BgBubbleDrawable$b;->n:Lcom/avery/subtitle/widget/BgBubbleDrawable$ArrowLocation;

    return-void
.end method

.method public static bridge synthetic a(Lcom/avery/subtitle/widget/BgBubbleDrawable$b;)I
    .locals 0

    iget p0, p0, Lcom/avery/subtitle/widget/BgBubbleDrawable$b;->k:I

    return p0
.end method

.method public static bridge synthetic b(Lcom/avery/subtitle/widget/BgBubbleDrawable$b;)Z
    .locals 0

    iget-boolean p0, p0, Lcom/avery/subtitle/widget/BgBubbleDrawable$b;->o:Z

    return p0
.end method

.method public static bridge synthetic c(Lcom/avery/subtitle/widget/BgBubbleDrawable$b;)Landroid/graphics/Bitmap;
    .locals 0

    iget-object p0, p0, Lcom/avery/subtitle/widget/BgBubbleDrawable$b;->l:Landroid/graphics/Bitmap;

    return-object p0
.end method

.method public static bridge synthetic d(Lcom/avery/subtitle/widget/BgBubbleDrawable$b;)I
    .locals 0

    iget p0, p0, Lcom/avery/subtitle/widget/BgBubbleDrawable$b;->j:I

    return p0
.end method

.method public static bridge synthetic e(Lcom/avery/subtitle/widget/BgBubbleDrawable$b;)Lcom/avery/subtitle/widget/BgBubbleDrawable$BubbleType;
    .locals 0

    iget-object p0, p0, Lcom/avery/subtitle/widget/BgBubbleDrawable$b;->m:Lcom/avery/subtitle/widget/BgBubbleDrawable$BubbleType;

    return-object p0
.end method

.method public static bridge synthetic f(Lcom/avery/subtitle/widget/BgBubbleDrawable$b;)F
    .locals 0

    iget p0, p0, Lcom/avery/subtitle/widget/BgBubbleDrawable$b;->g:F

    return p0
.end method

.method public static bridge synthetic g(Lcom/avery/subtitle/widget/BgBubbleDrawable$b;)F
    .locals 0

    iget p0, p0, Lcom/avery/subtitle/widget/BgBubbleDrawable$b;->h:F

    return p0
.end method

.method public static bridge synthetic h(Lcom/avery/subtitle/widget/BgBubbleDrawable$b;)Lcom/avery/subtitle/widget/BgBubbleDrawable$ArrowLocation;
    .locals 0

    iget-object p0, p0, Lcom/avery/subtitle/widget/BgBubbleDrawable$b;->n:Lcom/avery/subtitle/widget/BgBubbleDrawable$ArrowLocation;

    return-object p0
.end method

.method public static bridge synthetic i(Lcom/avery/subtitle/widget/BgBubbleDrawable$b;)F
    .locals 0

    iget p0, p0, Lcom/avery/subtitle/widget/BgBubbleDrawable$b;->i:F

    return p0
.end method

.method public static bridge synthetic j(Lcom/avery/subtitle/widget/BgBubbleDrawable$b;)F
    .locals 0

    iget p0, p0, Lcom/avery/subtitle/widget/BgBubbleDrawable$b;->f:F

    return p0
.end method

.method public static bridge synthetic k(Lcom/avery/subtitle/widget/BgBubbleDrawable$b;)Landroid/graphics/RectF;
    .locals 0

    iget-object p0, p0, Lcom/avery/subtitle/widget/BgBubbleDrawable$b;->a:Landroid/graphics/RectF;

    return-object p0
.end method

.method public static bridge synthetic l(Lcom/avery/subtitle/widget/BgBubbleDrawable$b;)I
    .locals 0

    iget p0, p0, Lcom/avery/subtitle/widget/BgBubbleDrawable$b;->c:I

    return p0
.end method

.method public static bridge synthetic m(Lcom/avery/subtitle/widget/BgBubbleDrawable$b;)F
    .locals 0

    iget p0, p0, Lcom/avery/subtitle/widget/BgBubbleDrawable$b;->d:F

    return p0
.end method

.method public static bridge synthetic n(Lcom/avery/subtitle/widget/BgBubbleDrawable$b;)F
    .locals 0

    iget p0, p0, Lcom/avery/subtitle/widget/BgBubbleDrawable$b;->e:F

    return p0
.end method

.method public static bridge synthetic o(Lcom/avery/subtitle/widget/BgBubbleDrawable$b;)F
    .locals 0

    iget p0, p0, Lcom/avery/subtitle/widget/BgBubbleDrawable$b;->b:F

    return p0
.end method


# virtual methods
.method public p(F)Lcom/avery/subtitle/widget/BgBubbleDrawable$b;
    .locals 1

    const/high16 v0, 0x40000000    # 2.0f

    mul-float p1, p1, v0

    iput p1, p0, Lcom/avery/subtitle/widget/BgBubbleDrawable$b;->g:F

    return-object p0
.end method

.method public q(Lcom/avery/subtitle/widget/BgBubbleDrawable$ArrowLocation;)Lcom/avery/subtitle/widget/BgBubbleDrawable$b;
    .locals 0

    iput-object p1, p0, Lcom/avery/subtitle/widget/BgBubbleDrawable$b;->n:Lcom/avery/subtitle/widget/BgBubbleDrawable$ArrowLocation;

    return-object p0
.end method

.method public r(I)Lcom/avery/subtitle/widget/BgBubbleDrawable$b;
    .locals 0

    iput p1, p0, Lcom/avery/subtitle/widget/BgBubbleDrawable$b;->j:I

    sget-object p1, Lcom/avery/subtitle/widget/BgBubbleDrawable$BubbleType;->COLOR:Lcom/avery/subtitle/widget/BgBubbleDrawable$BubbleType;

    invoke-virtual {p0, p1}, Lcom/avery/subtitle/widget/BgBubbleDrawable$b;->s(Lcom/avery/subtitle/widget/BgBubbleDrawable$BubbleType;)Lcom/avery/subtitle/widget/BgBubbleDrawable$b;

    return-object p0
.end method

.method public s(Lcom/avery/subtitle/widget/BgBubbleDrawable$BubbleType;)Lcom/avery/subtitle/widget/BgBubbleDrawable$b;
    .locals 0

    iput-object p1, p0, Lcom/avery/subtitle/widget/BgBubbleDrawable$b;->m:Lcom/avery/subtitle/widget/BgBubbleDrawable$BubbleType;

    return-object p0
.end method

.method public t()Lcom/avery/subtitle/widget/BgBubbleDrawable;
    .locals 2

    iget-object v0, p0, Lcom/avery/subtitle/widget/BgBubbleDrawable$b;->a:Landroid/graphics/RectF;

    if-eqz v0, :cond_0

    new-instance v0, Lcom/avery/subtitle/widget/BgBubbleDrawable;

    const/4 v1, 0x0

    invoke-direct {v0, p0, v1}, Lcom/avery/subtitle/widget/BgBubbleDrawable;-><init>(Lcom/avery/subtitle/widget/BgBubbleDrawable$b;Lf6/a;)V

    return-object v0

    :cond_0
    new-instance v0, Ljava/lang/IllegalArgumentException;

    const-string v1, "BubbleDrawable Rect can not be null"

    invoke-direct {v0, v1}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw v0
.end method

.method public u(Landroid/graphics/RectF;)Lcom/avery/subtitle/widget/BgBubbleDrawable$b;
    .locals 0

    iput-object p1, p0, Lcom/avery/subtitle/widget/BgBubbleDrawable$b;->a:Landroid/graphics/RectF;

    return-object p0
.end method
