.class public final Lcom/bigkoo/pickerview/R$id;
.super Ljava/lang/Object;


# static fields
.field public static btnCancel:I = 0x7f0a00ef

.field public static btnSubmit:I = 0x7f0a00f9

.field public static content_container:I = 0x7f0a019a

.field public static day:I = 0x7f0a01b1

.field public static hour:I = 0x7f0a030a

.field public static min:I = 0x7f0a065b

.field public static month:I = 0x7f0a065f

.field public static options1:I = 0x7f0a06ed

.field public static options2:I = 0x7f0a06ee

.field public static options3:I = 0x7f0a06ef

.field public static optionspicker:I = 0x7f0a06f0

.field public static outmost_container:I = 0x7f0a06f8

.field public static rv_topbar:I = 0x7f0a07c9

.field public static second:I = 0x7f0a081c

.field public static timepicker:I = 0x7f0a0905

.field public static tvTitle:I = 0x7f0a09ea

.field public static tv_ok:I = 0x7f0a0a9b

.field public static year:I = 0x7f0a0c39


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
