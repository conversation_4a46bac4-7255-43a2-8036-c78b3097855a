.class public final Lcom/bigkoo/pickerview/R$string;
.super Ljava/lang/Object;


# static fields
.field public static ok:I = 0x7f120492

.field public static pickerview_cancel:I = 0x7f1204c8

.field public static pickerview_day:I = 0x7f1204c9

.field public static pickerview_hours:I = 0x7f1204ca

.field public static pickerview_minutes:I = 0x7f1204cb

.field public static pickerview_month:I = 0x7f1204cc

.field public static pickerview_seconds:I = 0x7f1204cd

.field public static pickerview_submit:I = 0x7f1204ce

.field public static pickerview_year:I = 0x7f1204cf


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
