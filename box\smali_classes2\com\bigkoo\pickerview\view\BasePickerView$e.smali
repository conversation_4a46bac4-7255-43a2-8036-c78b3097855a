.class public Lcom/bigkoo/pickerview/view/BasePickerView$e;
.super Ljava/lang/Object;

# interfaces
.implements Landroid/content/DialogInterface$OnDismissListener;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bigkoo/pickerview/view/BasePickerView;->e()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field public final synthetic a:Lcom/bigkoo/pickerview/view/BasePickerView;


# direct methods
.method public constructor <init>(Lcom/bigkoo/pickerview/view/BasePickerView;)V
    .locals 0

    iput-object p1, p0, Lcom/bigkoo/pickerview/view/BasePickerView$e;->a:Lcom/bigkoo/pickerview/view/BasePickerView;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public onDismiss(Landroid/content/DialogInterface;)V
    .locals 0

    iget-object p1, p0, Lcom/bigkoo/pickerview/view/BasePickerView$e;->a:Lcom/bigkoo/pickerview/view/BasePickerView;

    invoke-static {p1}, Lcom/bigkoo/pickerview/view/BasePickerView;->a(Lcom/bigkoo/pickerview/view/BasePickerView;)Lj6/b;

    return-void
.end method
