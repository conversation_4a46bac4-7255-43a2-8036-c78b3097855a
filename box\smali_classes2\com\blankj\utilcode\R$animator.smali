.class public final Lcom/blankj/utilcode/R$animator;
.super Ljava/lang/Object;


# static fields
.field public static design_appbar_state_list_animator:I = 0x7f020000

.field public static design_fab_hide_motion_spec:I = 0x7f020001

.field public static design_fab_show_motion_spec:I = 0x7f020002

.field public static mtrl_btn_state_list_anim:I = 0x7f020015

.field public static mtrl_btn_unelevated_state_list_anim:I = 0x7f020016

.field public static mtrl_chip_state_list_anim:I = 0x7f020018

.field public static mtrl_fab_hide_motion_spec:I = 0x7f02001e

.field public static mtrl_fab_show_motion_spec:I = 0x7f02001f

.field public static mtrl_fab_transformation_sheet_collapse_spec:I = 0x7f020020

.field public static mtrl_fab_transformation_sheet_expand_spec:I = 0x7f020021


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
