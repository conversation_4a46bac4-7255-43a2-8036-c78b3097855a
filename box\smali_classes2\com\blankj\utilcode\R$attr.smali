.class public final Lcom/blankj/utilcode/R$attr;
.super Ljava/lang/Object;


# static fields
.field public static actionBarDivider:I = 0x7f040008

.field public static actionBarItemBackground:I = 0x7f040009

.field public static actionBarPopupTheme:I = 0x7f04000a

.field public static actionBarSize:I = 0x7f04000b

.field public static actionBarSplitStyle:I = 0x7f04000c

.field public static actionBarStyle:I = 0x7f04000d

.field public static actionBarTabBarStyle:I = 0x7f04000e

.field public static actionBarTabStyle:I = 0x7f04000f

.field public static actionBarTabTextStyle:I = 0x7f040010

.field public static actionBarTheme:I = 0x7f040011

.field public static actionBarWidgetTheme:I = 0x7f040012

.field public static actionButtonStyle:I = 0x7f040013

.field public static actionDropDownStyle:I = 0x7f040014

.field public static actionLayout:I = 0x7f040015

.field public static actionMenuTextAppearance:I = 0x7f040016

.field public static actionMenuTextColor:I = 0x7f040017

.field public static actionModeBackground:I = 0x7f040018

.field public static actionModeCloseButtonStyle:I = 0x7f040019

.field public static actionModeCloseDrawable:I = 0x7f04001b

.field public static actionModeCopyDrawable:I = 0x7f04001c

.field public static actionModeCutDrawable:I = 0x7f04001d

.field public static actionModeFindDrawable:I = 0x7f04001e

.field public static actionModePasteDrawable:I = 0x7f04001f

.field public static actionModePopupWindowStyle:I = 0x7f040020

.field public static actionModeSelectAllDrawable:I = 0x7f040021

.field public static actionModeShareDrawable:I = 0x7f040022

.field public static actionModeSplitBackground:I = 0x7f040023

.field public static actionModeStyle:I = 0x7f040024

.field public static actionModeWebSearchDrawable:I = 0x7f040026

.field public static actionOverflowButtonStyle:I = 0x7f040027

.field public static actionOverflowMenuStyle:I = 0x7f040028

.field public static actionProviderClass:I = 0x7f040029

.field public static actionViewClass:I = 0x7f04002b

.field public static activityChooserViewStyle:I = 0x7f04002e

.field public static alertDialogButtonGroupStyle:I = 0x7f040039

.field public static alertDialogCenterButtons:I = 0x7f04003a

.field public static alertDialogStyle:I = 0x7f04003b

.field public static alertDialogTheme:I = 0x7f04003c

.field public static allowStacking:I = 0x7f04003f

.field public static alpha:I = 0x7f040040

.field public static alphabeticModifiers:I = 0x7f040041

.field public static arrowHeadLength:I = 0x7f040051

.field public static arrowShaftLength:I = 0x7f040058

.field public static autoCompleteTextViewStyle:I = 0x7f04005f

.field public static autoSizeMaxTextSize:I = 0x7f040061

.field public static autoSizeMinTextSize:I = 0x7f040062

.field public static autoSizePresetSizes:I = 0x7f040063

.field public static autoSizeStepGranularity:I = 0x7f040064

.field public static autoSizeTextType:I = 0x7f040065

.field public static background:I = 0x7f04006a

.field public static backgroundSplit:I = 0x7f040071

.field public static backgroundStacked:I = 0x7f040072

.field public static backgroundTint:I = 0x7f040073

.field public static backgroundTintMode:I = 0x7f040074

.field public static barLength:I = 0x7f04009d

.field public static behavior_autoHide:I = 0x7f0400a8

.field public static behavior_fitToContents:I = 0x7f0400ac

.field public static behavior_hideable:I = 0x7f0400ae

.field public static behavior_overlapTop:I = 0x7f0400af

.field public static behavior_peekHeight:I = 0x7f0400b0

.field public static behavior_skipCollapsed:I = 0x7f0400b3

.field public static borderWidth:I = 0x7f0401ba

.field public static borderlessButtonStyle:I = 0x7f0401be

.field public static bottomAppBarStyle:I = 0x7f0401bf

.field public static bottomNavigationStyle:I = 0x7f0401c6

.field public static bottomSheetDialogTheme:I = 0x7f0401cc

.field public static bottomSheetStyle:I = 0x7f0401ce

.field public static boxBackgroundColor:I = 0x7f0401cf

.field public static boxBackgroundMode:I = 0x7f0401d0

.field public static boxCollapsedPaddingTop:I = 0x7f0401d1

.field public static boxCornerRadiusBottomEnd:I = 0x7f0401d2

.field public static boxCornerRadiusBottomStart:I = 0x7f0401d3

.field public static boxCornerRadiusTopEnd:I = 0x7f0401d4

.field public static boxCornerRadiusTopStart:I = 0x7f0401d5

.field public static boxStrokeColor:I = 0x7f0401d6

.field public static boxStrokeWidth:I = 0x7f0401d8

.field public static buttonBarButtonStyle:I = 0x7f0401de

.field public static buttonBarNegativeButtonStyle:I = 0x7f0401df

.field public static buttonBarNeutralButtonStyle:I = 0x7f0401e0

.field public static buttonBarPositiveButtonStyle:I = 0x7f0401e1

.field public static buttonBarStyle:I = 0x7f0401e2

.field public static buttonGravity:I = 0x7f0401e4

.field public static buttonIconDimen:I = 0x7f0401e6

.field public static buttonPanelSideLayout:I = 0x7f0401e9

.field public static buttonStyle:I = 0x7f0401eb

.field public static buttonStyleSmall:I = 0x7f0401ec

.field public static buttonTint:I = 0x7f0401ed

.field public static buttonTintMode:I = 0x7f0401ee

.field public static cardBackgroundColor:I = 0x7f0401f6

.field public static cardCornerRadius:I = 0x7f0401f7

.field public static cardElevation:I = 0x7f0401f8

.field public static cardMaxElevation:I = 0x7f0401fa

.field public static cardPreventCornerOverlap:I = 0x7f0401fb

.field public static cardUseCompatPadding:I = 0x7f0401fc

.field public static cardViewStyle:I = 0x7f0401fd

.field public static checkboxStyle:I = 0x7f04020e

.field public static checkedChip:I = 0x7f040210

.field public static checkedIcon:I = 0x7f040211

.field public static checkedIconEnabled:I = 0x7f040212

.field public static checkedIconVisible:I = 0x7f040217

.field public static checkedTextViewStyle:I = 0x7f040219

.field public static chipBackgroundColor:I = 0x7f04021a

.field public static chipCornerRadius:I = 0x7f04021b

.field public static chipEndPadding:I = 0x7f04021c

.field public static chipGroupStyle:I = 0x7f04021d

.field public static chipIcon:I = 0x7f04021e

.field public static chipIconEnabled:I = 0x7f04021f

.field public static chipIconSize:I = 0x7f040220

.field public static chipIconTint:I = 0x7f040221

.field public static chipIconVisible:I = 0x7f040222

.field public static chipMinHeight:I = 0x7f040223

.field public static chipSpacing:I = 0x7f040225

.field public static chipSpacingHorizontal:I = 0x7f040226

.field public static chipSpacingVertical:I = 0x7f040227

.field public static chipStandaloneStyle:I = 0x7f040228

.field public static chipStartPadding:I = 0x7f040229

.field public static chipStrokeColor:I = 0x7f04022a

.field public static chipStrokeWidth:I = 0x7f04022b

.field public static chipStyle:I = 0x7f04022c

.field public static closeIcon:I = 0x7f040248

.field public static closeIconEnabled:I = 0x7f040249

.field public static closeIconEndPadding:I = 0x7f04024a

.field public static closeIconSize:I = 0x7f04024b

.field public static closeIconStartPadding:I = 0x7f04024c

.field public static closeIconTint:I = 0x7f04024d

.field public static closeIconVisible:I = 0x7f04024e

.field public static closeItemLayout:I = 0x7f04024f

.field public static collapseContentDescription:I = 0x7f040250

.field public static collapseIcon:I = 0x7f040251

.field public static collapsedTitleGravity:I = 0x7f040253

.field public static collapsedTitleTextAppearance:I = 0x7f040254

.field public static color:I = 0x7f04025b

.field public static colorAccent:I = 0x7f04025c

.field public static colorBackgroundFloating:I = 0x7f04025d

.field public static colorButtonNormal:I = 0x7f04025e

.field public static colorControlActivated:I = 0x7f040260

.field public static colorControlHighlight:I = 0x7f040261

.field public static colorControlNormal:I = 0x7f040262

.field public static colorError:I = 0x7f040263

.field public static colorPrimary:I = 0x7f04027c

.field public static colorPrimaryDark:I = 0x7f04027e

.field public static colorSecondary:I = 0x7f040285

.field public static colorSwitchThumbNormal:I = 0x7f040294

.field public static commitIcon:I = 0x7f040299

.field public static contentDescription:I = 0x7f0402a3

.field public static contentInsetEnd:I = 0x7f0402a4

.field public static contentInsetEndWithActions:I = 0x7f0402a5

.field public static contentInsetLeft:I = 0x7f0402a6

.field public static contentInsetRight:I = 0x7f0402a7

.field public static contentInsetStart:I = 0x7f0402a8

.field public static contentInsetStartWithNavigation:I = 0x7f0402a9

.field public static contentPadding:I = 0x7f0402aa

.field public static contentPaddingBottom:I = 0x7f0402ab

.field public static contentPaddingLeft:I = 0x7f0402ad

.field public static contentPaddingRight:I = 0x7f0402ae

.field public static contentPaddingTop:I = 0x7f0402b0

.field public static contentScrim:I = 0x7f0402b1

.field public static controlBackground:I = 0x7f0402b3

.field public static coordinatorLayoutStyle:I = 0x7f0402b5

.field public static cornerRadius:I = 0x7f0402be

.field public static counterEnabled:I = 0x7f0402c8

.field public static counterMaxLength:I = 0x7f0402c9

.field public static counterOverflowTextAppearance:I = 0x7f0402ca

.field public static counterTextAppearance:I = 0x7f0402cc

.field public static customNavigationLayout:I = 0x7f0402d9

.field public static defaultQueryHint:I = 0x7f0402e6

.field public static dialogCornerRadius:I = 0x7f0402f1

.field public static dialogPreferredPadding:I = 0x7f0402f2

.field public static dialogTheme:I = 0x7f0402f3

.field public static displayOptions:I = 0x7f0402f4

.field public static divider:I = 0x7f0402f5

.field public static dividerHorizontal:I = 0x7f0402fa

.field public static dividerPadding:I = 0x7f0402fd

.field public static dividerVertical:I = 0x7f0402ff

.field public static drawableSize:I = 0x7f040308

.field public static drawerArrowStyle:I = 0x7f04030d

.field public static dropDownListViewStyle:I = 0x7f040311

.field public static dropdownListPreferredItemHeight:I = 0x7f040312

.field public static editTextBackground:I = 0x7f040315

.field public static editTextColor:I = 0x7f040316

.field public static editTextStyle:I = 0x7f040317

.field public static elevation:I = 0x7f040318

.field public static enforceMaterialTheme:I = 0x7f040338

.field public static enforceTextAppearance:I = 0x7f040339

.field public static errorEnabled:I = 0x7f04033f

.field public static errorTextAppearance:I = 0x7f040344

.field public static expandActivityOverflowButtonDrawable:I = 0x7f040349

.field public static expanded:I = 0x7f04034a

.field public static expandedTitleGravity:I = 0x7f04034c

.field public static expandedTitleMargin:I = 0x7f04034d

.field public static expandedTitleMarginBottom:I = 0x7f04034e

.field public static expandedTitleMarginEnd:I = 0x7f04034f

.field public static expandedTitleMarginStart:I = 0x7f040350

.field public static expandedTitleMarginTop:I = 0x7f040351

.field public static expandedTitleTextAppearance:I = 0x7f040352

.field public static fabAlignmentMode:I = 0x7f04035c

.field public static fabCradleMargin:I = 0x7f040360

.field public static fabCradleRoundedCornerRadius:I = 0x7f040361

.field public static fabCradleVerticalOffset:I = 0x7f040362

.field public static fabCustomSize:I = 0x7f040363

.field public static fabSize:I = 0x7f040364

.field public static fastScrollEnabled:I = 0x7f040365

.field public static fastScrollHorizontalThumbDrawable:I = 0x7f040366

.field public static fastScrollHorizontalTrackDrawable:I = 0x7f040367

.field public static fastScrollVerticalThumbDrawable:I = 0x7f040368

.field public static fastScrollVerticalTrackDrawable:I = 0x7f040369

.field public static firstBaselineToTopHeight:I = 0x7f04036c

.field public static floatingActionButtonStyle:I = 0x7f040383

.field public static font:I = 0x7f040399

.field public static fontFamily:I = 0x7f04039a

.field public static fontProviderAuthority:I = 0x7f04039b

.field public static fontProviderCerts:I = 0x7f04039c

.field public static fontProviderFetchStrategy:I = 0x7f04039d

.field public static fontProviderFetchTimeout:I = 0x7f04039e

.field public static fontProviderPackage:I = 0x7f04039f

.field public static fontProviderQuery:I = 0x7f0403a0

.field public static fontStyle:I = 0x7f0403a2

.field public static fontVariationSettings:I = 0x7f0403a3

.field public static fontWeight:I = 0x7f0403a4

.field public static foregroundInsidePadding:I = 0x7f0403a7

.field public static gapBetweenBars:I = 0x7f0403aa

.field public static goIcon:I = 0x7f0403ad

.field public static headerLayout:I = 0x7f0403ba

.field public static height:I = 0x7f0403bb

.field public static helperText:I = 0x7f0403bc

.field public static helperTextEnabled:I = 0x7f0403bd

.field public static helperTextTextAppearance:I = 0x7f0403be

.field public static hideMotionSpec:I = 0x7f0403c1

.field public static hideOnContentScroll:I = 0x7f0403c3

.field public static hideOnScroll:I = 0x7f0403c4

.field public static hintAnimationEnabled:I = 0x7f0403c7

.field public static hintEnabled:I = 0x7f0403c8

.field public static hintTextAppearance:I = 0x7f0403c9

.field public static homeAsUpIndicator:I = 0x7f0403cb

.field public static homeLayout:I = 0x7f0403cc

.field public static hoveredFocusedTranslationZ:I = 0x7f0403cf

.field public static icon:I = 0x7f0403d0

.field public static iconEndPadding:I = 0x7f0403d1

.field public static iconGravity:I = 0x7f0403d2

.field public static iconPadding:I = 0x7f0403d3

.field public static iconSize:I = 0x7f0403d4

.field public static iconStartPadding:I = 0x7f0403d6

.field public static iconTint:I = 0x7f0403d7

.field public static iconTintMode:I = 0x7f0403d8

.field public static iconifiedByDefault:I = 0x7f0403d9

.field public static imageButtonStyle:I = 0x7f0403de

.field public static indeterminateProgressStyle:I = 0x7f0403e4

.field public static initialActivityCount:I = 0x7f0403f4

.field public static insetForeground:I = 0x7f0403f6

.field public static isLightTheme:I = 0x7f0403f9

.field public static itemBackground:I = 0x7f040428

.field public static itemHorizontalPadding:I = 0x7f04042a

.field public static itemHorizontalTranslationEnabled:I = 0x7f04042b

.field public static itemIconPadding:I = 0x7f04042c

.field public static itemIconSize:I = 0x7f04042d

.field public static itemIconTint:I = 0x7f04042e

.field public static itemPadding:I = 0x7f040431

.field public static itemSpacing:I = 0x7f04043c

.field public static itemTextAppearance:I = 0x7f04043f

.field public static itemTextAppearanceActive:I = 0x7f040440

.field public static itemTextAppearanceInactive:I = 0x7f040442

.field public static itemTextColor:I = 0x7f040443

.field public static keylines:I = 0x7f040459

.field public static labelVisibilityMode:I = 0x7f04045d

.field public static lastBaselineToBottomHeight:I = 0x7f04045f

.field public static layout:I = 0x7f040466

.field public static layoutManager:I = 0x7f040469

.field public static layout_anchor:I = 0x7f04046b

.field public static layout_anchorGravity:I = 0x7f04046c

.field public static layout_behavior:I = 0x7f04046d

.field public static layout_collapseMode:I = 0x7f04046e

.field public static layout_collapseParallaxMultiplier:I = 0x7f04046f

.field public static layout_dodgeInsetEdges:I = 0x7f04049e

.field public static layout_insetEdge:I = 0x7f0404ad

.field public static layout_keyline:I = 0x7f0404ae

.field public static layout_scrollFlags:I = 0x7f0404b8

.field public static layout_scrollInterpolator:I = 0x7f0404b9

.field public static liftOnScroll:I = 0x7f0404bd

.field public static lineHeight:I = 0x7f0404c3

.field public static lineSpacing:I = 0x7f0404c4

.field public static listChoiceBackgroundIndicator:I = 0x7f0404c6

.field public static listDividerAlertDialog:I = 0x7f0404c9

.field public static listItemLayout:I = 0x7f0404ca

.field public static listLayout:I = 0x7f0404cb

.field public static listMenuViewStyle:I = 0x7f0404cc

.field public static listPopupWindowStyle:I = 0x7f0404cd

.field public static listPreferredItemHeight:I = 0x7f0404ce

.field public static listPreferredItemHeightLarge:I = 0x7f0404cf

.field public static listPreferredItemHeightSmall:I = 0x7f0404d0

.field public static listPreferredItemPaddingLeft:I = 0x7f0404d2

.field public static listPreferredItemPaddingRight:I = 0x7f0404d3

.field public static logo:I = 0x7f0404d6

.field public static logoDescription:I = 0x7f0404d8

.field public static materialButtonStyle:I = 0x7f0404fd

.field public static materialCardViewStyle:I = 0x7f040511

.field public static maxActionInlineWidth:I = 0x7f040527

.field public static maxButtonHeight:I = 0x7f040528

.field public static maxImageSize:I = 0x7f04052b

.field public static measureWithLargestChild:I = 0x7f040538

.field public static menu:I = 0x7f040539

.field public static multiChoiceItemLayout:I = 0x7f040578

.field public static navigationContentDescription:I = 0x7f04057a

.field public static navigationIcon:I = 0x7f04057b

.field public static navigationMode:I = 0x7f04057d

.field public static navigationViewStyle:I = 0x7f04057f

.field public static numericModifiers:I = 0x7f04058b

.field public static overlapAnchor:I = 0x7f040594

.field public static paddingBottomNoButtons:I = 0x7f040596

.field public static paddingEnd:I = 0x7f040598

.field public static paddingStart:I = 0x7f04059b

.field public static paddingTopNoTitle:I = 0x7f04059d

.field public static panelBackground:I = 0x7f04059f

.field public static panelMenuListTheme:I = 0x7f0405a0

.field public static panelMenuListWidth:I = 0x7f0405a1

.field public static passwordToggleContentDescription:I = 0x7f0405a2

.field public static passwordToggleDrawable:I = 0x7f0405a3

.field public static passwordToggleEnabled:I = 0x7f0405a4

.field public static passwordToggleTint:I = 0x7f0405a5

.field public static passwordToggleTintMode:I = 0x7f0405a6

.field public static popupMenuStyle:I = 0x7f0405d2

.field public static popupTheme:I = 0x7f0405d3

.field public static popupWindowStyle:I = 0x7f0405d4

.field public static preserveIconSpacing:I = 0x7f0405d9

.field public static pressedTranslationZ:I = 0x7f0405da

.field public static progressBarPadding:I = 0x7f0405dd

.field public static progressBarStyle:I = 0x7f0405de

.field public static queryBackground:I = 0x7f0405ea

.field public static queryHint:I = 0x7f0405eb

.field public static radioButtonStyle:I = 0x7f0405ee

.field public static ratingBarStyle:I = 0x7f0405f1

.field public static ratingBarStyleIndicator:I = 0x7f0405f2

.field public static ratingBarStyleSmall:I = 0x7f0405f3

.field public static reverseLayout:I = 0x7f04060c

.field public static rippleColor:I = 0x7f040615

.field public static scrimAnimationDuration:I = 0x7f040622

.field public static scrimBackground:I = 0x7f040623

.field public static scrimVisibleHeightTrigger:I = 0x7f040624

.field public static searchHintIcon:I = 0x7f04062b

.field public static searchIcon:I = 0x7f04062c

.field public static searchViewStyle:I = 0x7f04062e

.field public static seekBarStyle:I = 0x7f040631

.field public static selectableItemBackground:I = 0x7f040632

.field public static selectableItemBackgroundBorderless:I = 0x7f040633

.field public static showAsAction:I = 0x7f04065e

.field public static showDividers:I = 0x7f040663

.field public static showMotionSpec:I = 0x7f040667

.field public static showText:I = 0x7f04066a

.field public static showTitle:I = 0x7f04066b

.field public static singleChoiceItemLayout:I = 0x7f04067d

.field public static singleLine:I = 0x7f04067e

.field public static singleSelection:I = 0x7f04067f

.field public static snackbarButtonStyle:I = 0x7f040682

.field public static snackbarStyle:I = 0x7f040683

.field public static spanCount:I = 0x7f040685

.field public static spinBars:I = 0x7f040686

.field public static spinnerDropDownItemStyle:I = 0x7f040687

.field public static spinnerStyle:I = 0x7f040688

.field public static splitTrack:I = 0x7f04068e

.field public static srcCompat:I = 0x7f040694

.field public static stackFromEnd:I = 0x7f0406a2

.field public static state_above_anchor:I = 0x7f0406ae

.field public static state_collapsed:I = 0x7f0406af

.field public static state_collapsible:I = 0x7f0406b0

.field public static state_liftable:I = 0x7f0406b4

.field public static state_lifted:I = 0x7f0406b5

.field public static statusBarBackground:I = 0x7f0406b7

.field public static statusBarScrim:I = 0x7f0406b9

.field public static strokeColor:I = 0x7f0406bc

.field public static strokeWidth:I = 0x7f0406bd

.field public static subMenuArrow:I = 0x7f0406bf

.field public static submitBackground:I = 0x7f0406c4

.field public static subtitle:I = 0x7f0406c5

.field public static subtitleTextAppearance:I = 0x7f0406c7

.field public static subtitleTextColor:I = 0x7f0406c8

.field public static subtitleTextStyle:I = 0x7f0406c9

.field public static suggestionRowLayout:I = 0x7f0406cd

.field public static switchMinWidth:I = 0x7f0406d0

.field public static switchPadding:I = 0x7f0406d1

.field public static switchStyle:I = 0x7f0406d2

.field public static switchTextAppearance:I = 0x7f0406d3

.field public static tabBackground:I = 0x7f0406da

.field public static tabContentStart:I = 0x7f0406db

.field public static tabGravity:I = 0x7f0406dc

.field public static tabIconTint:I = 0x7f0406dd

.field public static tabIconTintMode:I = 0x7f0406de

.field public static tabIndicator:I = 0x7f0406df

.field public static tabIndicatorAnimationDuration:I = 0x7f0406e0

.field public static tabIndicatorColor:I = 0x7f0406e2

.field public static tabIndicatorFullWidth:I = 0x7f0406e3

.field public static tabIndicatorGravity:I = 0x7f0406e4

.field public static tabIndicatorHeight:I = 0x7f0406e5

.field public static tabInlineLabel:I = 0x7f0406e6

.field public static tabMaxWidth:I = 0x7f0406e7

.field public static tabMinWidth:I = 0x7f0406e8

.field public static tabMode:I = 0x7f0406e9

.field public static tabPadding:I = 0x7f0406ea

.field public static tabPaddingBottom:I = 0x7f0406eb

.field public static tabPaddingEnd:I = 0x7f0406ec

.field public static tabPaddingStart:I = 0x7f0406ed

.field public static tabPaddingTop:I = 0x7f0406ee

.field public static tabRippleColor:I = 0x7f0406ef

.field public static tabSelectedTextColor:I = 0x7f0406f2

.field public static tabStyle:I = 0x7f0406f3

.field public static tabTextAppearance:I = 0x7f0406f4

.field public static tabTextColor:I = 0x7f0406f5

.field public static tabUnboundedRipple:I = 0x7f0406f6

.field public static textAllCaps:I = 0x7f0406fd

.field public static textAppearanceBody1:I = 0x7f0406fe

.field public static textAppearanceBody2:I = 0x7f0406ff

.field public static textAppearanceButton:I = 0x7f040703

.field public static textAppearanceCaption:I = 0x7f040704

.field public static textAppearanceHeadline1:I = 0x7f040708

.field public static textAppearanceHeadline2:I = 0x7f040709

.field public static textAppearanceHeadline3:I = 0x7f04070a

.field public static textAppearanceHeadline4:I = 0x7f04070b

.field public static textAppearanceHeadline5:I = 0x7f04070c

.field public static textAppearanceHeadline6:I = 0x7f04070d

.field public static textAppearanceLargePopupMenu:I = 0x7f040714

.field public static textAppearanceListItem:I = 0x7f040716

.field public static textAppearanceListItemSecondary:I = 0x7f040717

.field public static textAppearanceListItemSmall:I = 0x7f040718

.field public static textAppearanceOverline:I = 0x7f040719

.field public static textAppearancePopupMenuHeader:I = 0x7f04071a

.field public static textAppearanceSearchResultSubtitle:I = 0x7f04071b

.field public static textAppearanceSearchResultTitle:I = 0x7f04071c

.field public static textAppearanceSmallPopupMenu:I = 0x7f04071d

.field public static textAppearanceSubtitle1:I = 0x7f04071e

.field public static textAppearanceSubtitle2:I = 0x7f04071f

.field public static textColorAlertDialogListItem:I = 0x7f040728

.field public static textColorSearchUrl:I = 0x7f040729

.field public static textEndPadding:I = 0x7f04072a

.field public static textInputStyle:I = 0x7f040733

.field public static textStartPadding:I = 0x7f040739

.field public static theme:I = 0x7f04073f

.field public static thickness:I = 0x7f040740

.field public static thumbTextPadding:I = 0x7f04074b

.field public static thumbTint:I = 0x7f04074c

.field public static thumbTintMode:I = 0x7f04074d

.field public static tickMark:I = 0x7f040753

.field public static tickMarkTint:I = 0x7f040754

.field public static tickMarkTintMode:I = 0x7f040755

.field public static tint:I = 0x7f04075a

.field public static tintMode:I = 0x7f04075b

.field public static title:I = 0x7f040760

.field public static titleEnabled:I = 0x7f040764

.field public static titleMargin:I = 0x7f040766

.field public static titleMarginBottom:I = 0x7f040767

.field public static titleMarginEnd:I = 0x7f040768

.field public static titleMarginStart:I = 0x7f040769

.field public static titleMarginTop:I = 0x7f04076a

.field public static titleMargins:I = 0x7f04076b

.field public static titleTextAppearance:I = 0x7f04076e

.field public static titleTextColor:I = 0x7f04076f

.field public static titleTextStyle:I = 0x7f040772

.field public static toolbarId:I = 0x7f040775

.field public static toolbarNavigationButtonStyle:I = 0x7f040776

.field public static toolbarStyle:I = 0x7f040777

.field public static tooltipForegroundColor:I = 0x7f040779

.field public static tooltipFrameBackground:I = 0x7f04077a

.field public static tooltipText:I = 0x7f04077c

.field public static track:I = 0x7f04078c

.field public static trackTint:I = 0x7f040798

.field public static trackTintMode:I = 0x7f040799

.field public static ttcIndex:I = 0x7f0407a3

.field public static useCompatPadding:I = 0x7f0407af

.field public static viewInflaterClass:I = 0x7f0407b9

.field public static voiceIcon:I = 0x7f0407bf

.field public static windowActionBar:I = 0x7f0407cd

.field public static windowActionBarOverlay:I = 0x7f0407ce

.field public static windowActionModeOverlay:I = 0x7f0407cf

.field public static windowFixedHeightMajor:I = 0x7f0407d0

.field public static windowFixedHeightMinor:I = 0x7f0407d1

.field public static windowFixedWidthMajor:I = 0x7f0407d2

.field public static windowFixedWidthMinor:I = 0x7f0407d3

.field public static windowMinWidthMajor:I = 0x7f0407d4

.field public static windowMinWidthMinor:I = 0x7f0407d5

.field public static windowNoTitle:I = 0x7f0407d6


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
