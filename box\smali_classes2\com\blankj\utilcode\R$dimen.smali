.class public final Lcom/blankj/utilcode/R$dimen;
.super Ljava/lang/Object;


# static fields
.field public static abc_action_bar_content_inset_material:I = 0x7f070000

.field public static abc_action_bar_content_inset_with_nav:I = 0x7f070001

.field public static abc_action_bar_default_height_material:I = 0x7f070002

.field public static abc_action_bar_default_padding_end_material:I = 0x7f070003

.field public static abc_action_bar_default_padding_start_material:I = 0x7f070004

.field public static abc_action_bar_elevation_material:I = 0x7f070005

.field public static abc_action_bar_icon_vertical_padding_material:I = 0x7f070006

.field public static abc_action_bar_overflow_padding_end_material:I = 0x7f070007

.field public static abc_action_bar_overflow_padding_start_material:I = 0x7f070008

.field public static abc_action_bar_stacked_max_height:I = 0x7f070009

.field public static abc_action_bar_stacked_tab_max_width:I = 0x7f07000a

.field public static abc_action_bar_subtitle_bottom_margin_material:I = 0x7f07000b

.field public static abc_action_bar_subtitle_top_margin_material:I = 0x7f07000c

.field public static abc_action_button_min_height_material:I = 0x7f07000d

.field public static abc_action_button_min_width_material:I = 0x7f07000e

.field public static abc_action_button_min_width_overflow_material:I = 0x7f07000f

.field public static abc_alert_dialog_button_bar_height:I = 0x7f070010

.field public static abc_alert_dialog_button_dimen:I = 0x7f070011

.field public static abc_button_inset_horizontal_material:I = 0x7f070012

.field public static abc_button_inset_vertical_material:I = 0x7f070013

.field public static abc_button_padding_horizontal_material:I = 0x7f070014

.field public static abc_button_padding_vertical_material:I = 0x7f070015

.field public static abc_cascading_menus_min_smallest_width:I = 0x7f070016

.field public static abc_config_prefDialogWidth:I = 0x7f070017

.field public static abc_control_corner_material:I = 0x7f070018

.field public static abc_control_inset_material:I = 0x7f070019

.field public static abc_control_padding_material:I = 0x7f07001a

.field public static abc_dialog_corner_radius_material:I = 0x7f07001b

.field public static abc_dialog_fixed_height_major:I = 0x7f07001c

.field public static abc_dialog_fixed_height_minor:I = 0x7f07001d

.field public static abc_dialog_fixed_width_major:I = 0x7f07001e

.field public static abc_dialog_fixed_width_minor:I = 0x7f07001f

.field public static abc_dialog_list_padding_bottom_no_buttons:I = 0x7f070020

.field public static abc_dialog_list_padding_top_no_title:I = 0x7f070021

.field public static abc_dialog_min_width_major:I = 0x7f070022

.field public static abc_dialog_min_width_minor:I = 0x7f070023

.field public static abc_dialog_padding_material:I = 0x7f070024

.field public static abc_dialog_padding_top_material:I = 0x7f070025

.field public static abc_dialog_title_divider_material:I = 0x7f070026

.field public static abc_disabled_alpha_material_dark:I = 0x7f070027

.field public static abc_disabled_alpha_material_light:I = 0x7f070028

.field public static abc_dropdownitem_icon_width:I = 0x7f070029

.field public static abc_dropdownitem_text_padding_left:I = 0x7f07002a

.field public static abc_dropdownitem_text_padding_right:I = 0x7f07002b

.field public static abc_edit_text_inset_bottom_material:I = 0x7f07002c

.field public static abc_edit_text_inset_horizontal_material:I = 0x7f07002d

.field public static abc_edit_text_inset_top_material:I = 0x7f07002e

.field public static abc_floating_window_z:I = 0x7f07002f

.field public static abc_list_item_padding_horizontal_material:I = 0x7f070033

.field public static abc_panel_menu_list_width:I = 0x7f070034

.field public static abc_progress_bar_height_material:I = 0x7f070035

.field public static abc_search_view_preferred_height:I = 0x7f070036

.field public static abc_search_view_preferred_width:I = 0x7f070037

.field public static abc_seekbar_track_background_height_material:I = 0x7f070038

.field public static abc_seekbar_track_progress_height_material:I = 0x7f070039

.field public static abc_select_dialog_padding_start_material:I = 0x7f07003a

.field public static abc_switch_padding:I = 0x7f07003e

.field public static abc_text_size_body_1_material:I = 0x7f07003f

.field public static abc_text_size_body_2_material:I = 0x7f070040

.field public static abc_text_size_button_material:I = 0x7f070041

.field public static abc_text_size_caption_material:I = 0x7f070042

.field public static abc_text_size_display_1_material:I = 0x7f070043

.field public static abc_text_size_display_2_material:I = 0x7f070044

.field public static abc_text_size_display_3_material:I = 0x7f070045

.field public static abc_text_size_display_4_material:I = 0x7f070046

.field public static abc_text_size_headline_material:I = 0x7f070047

.field public static abc_text_size_large_material:I = 0x7f070048

.field public static abc_text_size_medium_material:I = 0x7f070049

.field public static abc_text_size_menu_header_material:I = 0x7f07004a

.field public static abc_text_size_menu_material:I = 0x7f07004b

.field public static abc_text_size_small_material:I = 0x7f07004c

.field public static abc_text_size_subhead_material:I = 0x7f07004d

.field public static abc_text_size_subtitle_material_toolbar:I = 0x7f07004e

.field public static abc_text_size_title_material:I = 0x7f07004f

.field public static abc_text_size_title_material_toolbar:I = 0x7f070050

.field public static cardview_compat_inset_shadow:I = 0x7f070069

.field public static cardview_default_elevation:I = 0x7f07006a

.field public static cardview_default_radius:I = 0x7f07006b

.field public static compat_button_inset_horizontal_material:I = 0x7f07006e

.field public static compat_button_inset_vertical_material:I = 0x7f07006f

.field public static compat_button_padding_horizontal_material:I = 0x7f070070

.field public static compat_button_padding_vertical_material:I = 0x7f070071

.field public static compat_control_corner_material:I = 0x7f070072

.field public static compat_notification_large_icon_max_height:I = 0x7f070073

.field public static compat_notification_large_icon_max_width:I = 0x7f070074

.field public static design_appbar_elevation:I = 0x7f070078

.field public static design_bottom_navigation_active_item_max_width:I = 0x7f070079

.field public static design_bottom_navigation_active_item_min_width:I = 0x7f07007a

.field public static design_bottom_navigation_active_text_size:I = 0x7f07007b

.field public static design_bottom_navigation_elevation:I = 0x7f07007c

.field public static design_bottom_navigation_height:I = 0x7f07007d

.field public static design_bottom_navigation_icon_size:I = 0x7f07007e

.field public static design_bottom_navigation_item_max_width:I = 0x7f07007f

.field public static design_bottom_navigation_item_min_width:I = 0x7f070080

.field public static design_bottom_navigation_margin:I = 0x7f070082

.field public static design_bottom_navigation_shadow_height:I = 0x7f070083

.field public static design_bottom_navigation_text_size:I = 0x7f070084

.field public static design_bottom_sheet_modal_elevation:I = 0x7f070086

.field public static design_bottom_sheet_peek_height_min:I = 0x7f070087

.field public static design_fab_border_width:I = 0x7f070088

.field public static design_fab_elevation:I = 0x7f070089

.field public static design_fab_image_size:I = 0x7f07008a

.field public static design_fab_size_mini:I = 0x7f07008b

.field public static design_fab_size_normal:I = 0x7f07008c

.field public static design_fab_translation_z_hovered_focused:I = 0x7f07008d

.field public static design_fab_translation_z_pressed:I = 0x7f07008e

.field public static design_navigation_elevation:I = 0x7f07008f

.field public static design_navigation_icon_padding:I = 0x7f070090

.field public static design_navigation_icon_size:I = 0x7f070091

.field public static design_navigation_item_horizontal_padding:I = 0x7f070092

.field public static design_navigation_item_icon_padding:I = 0x7f070093

.field public static design_navigation_max_width:I = 0x7f070095

.field public static design_navigation_padding_bottom:I = 0x7f070096

.field public static design_navigation_separator_vertical_padding:I = 0x7f070097

.field public static design_snackbar_action_inline_max_width:I = 0x7f070098

.field public static design_snackbar_background_corner_radius:I = 0x7f07009a

.field public static design_snackbar_elevation:I = 0x7f07009b

.field public static design_snackbar_extra_spacing_horizontal:I = 0x7f07009c

.field public static design_snackbar_max_width:I = 0x7f07009d

.field public static design_snackbar_min_width:I = 0x7f07009e

.field public static design_snackbar_padding_horizontal:I = 0x7f07009f

.field public static design_snackbar_padding_vertical:I = 0x7f0700a0

.field public static design_snackbar_padding_vertical_2lines:I = 0x7f0700a1

.field public static design_snackbar_text_size:I = 0x7f0700a2

.field public static design_tab_max_width:I = 0x7f0700a3

.field public static design_tab_scrollable_min_width:I = 0x7f0700a4

.field public static design_tab_text_size:I = 0x7f0700a5

.field public static design_tab_text_size_2line:I = 0x7f0700a6

.field public static design_textinput_caption_translate_y:I = 0x7f0700a7

.field public static disabled_alpha_material_dark:I = 0x7f0700b0

.field public static disabled_alpha_material_light:I = 0x7f0700b1

.field public static fastscroll_default_thickness:I = 0x7f0700e3

.field public static fastscroll_margin:I = 0x7f0700e4

.field public static fastscroll_minimum_range:I = 0x7f0700e5

.field public static highlight_alpha_material_colored:I = 0x7f0700e7

.field public static highlight_alpha_material_dark:I = 0x7f0700e8

.field public static highlight_alpha_material_light:I = 0x7f0700e9

.field public static hint_alpha_material_dark:I = 0x7f0700ea

.field public static hint_alpha_material_light:I = 0x7f0700eb

.field public static hint_pressed_alpha_material_dark:I = 0x7f0700ec

.field public static hint_pressed_alpha_material_light:I = 0x7f0700ed

.field public static item_touch_helper_max_drag_scroll_per_frame:I = 0x7f070100

.field public static item_touch_helper_swipe_escape_max_velocity:I = 0x7f070101

.field public static item_touch_helper_swipe_escape_velocity:I = 0x7f070102

.field public static mtrl_bottomappbar_fabOffsetEndMode:I = 0x7f0702c6

.field public static mtrl_bottomappbar_fab_cradle_margin:I = 0x7f0702c8

.field public static mtrl_bottomappbar_fab_cradle_rounded_corner_radius:I = 0x7f0702c9

.field public static mtrl_bottomappbar_fab_cradle_vertical_offset:I = 0x7f0702ca

.field public static mtrl_bottomappbar_height:I = 0x7f0702cb

.field public static mtrl_btn_corner_radius:I = 0x7f0702cc

.field public static mtrl_btn_dialog_btn_min_width:I = 0x7f0702cd

.field public static mtrl_btn_disabled_elevation:I = 0x7f0702ce

.field public static mtrl_btn_disabled_z:I = 0x7f0702cf

.field public static mtrl_btn_elevation:I = 0x7f0702d0

.field public static mtrl_btn_focused_z:I = 0x7f0702d1

.field public static mtrl_btn_hovered_z:I = 0x7f0702d2

.field public static mtrl_btn_icon_btn_padding_left:I = 0x7f0702d3

.field public static mtrl_btn_icon_padding:I = 0x7f0702d4

.field public static mtrl_btn_inset:I = 0x7f0702d5

.field public static mtrl_btn_letter_spacing:I = 0x7f0702d6

.field public static mtrl_btn_padding_bottom:I = 0x7f0702d8

.field public static mtrl_btn_padding_left:I = 0x7f0702d9

.field public static mtrl_btn_padding_right:I = 0x7f0702da

.field public static mtrl_btn_padding_top:I = 0x7f0702db

.field public static mtrl_btn_pressed_z:I = 0x7f0702dc

.field public static mtrl_btn_stroke_size:I = 0x7f0702de

.field public static mtrl_btn_text_btn_icon_padding:I = 0x7f0702df

.field public static mtrl_btn_text_btn_padding_left:I = 0x7f0702e0

.field public static mtrl_btn_text_btn_padding_right:I = 0x7f0702e1

.field public static mtrl_btn_text_size:I = 0x7f0702e2

.field public static mtrl_btn_z:I = 0x7f0702e3

.field public static mtrl_card_elevation:I = 0x7f070312

.field public static mtrl_card_spacing:I = 0x7f070313

.field public static mtrl_chip_pressed_translation_z:I = 0x7f070314

.field public static mtrl_chip_text_size:I = 0x7f070315

.field public static mtrl_fab_elevation:I = 0x7f070329

.field public static mtrl_fab_translation_z_hovered_focused:I = 0x7f07032b

.field public static mtrl_fab_translation_z_pressed:I = 0x7f07032c

.field public static mtrl_navigation_elevation:I = 0x7f070338

.field public static mtrl_navigation_item_horizontal_padding:I = 0x7f070339

.field public static mtrl_navigation_item_icon_padding:I = 0x7f07033a

.field public static mtrl_snackbar_background_corner_radius:I = 0x7f070364

.field public static mtrl_snackbar_margin:I = 0x7f070366

.field public static mtrl_textinput_box_corner_radius_medium:I = 0x7f07036f

.field public static mtrl_textinput_box_corner_radius_small:I = 0x7f070370

.field public static mtrl_textinput_box_label_cutout_padding:I = 0x7f070371

.field public static mtrl_textinput_box_stroke_width_default:I = 0x7f070372

.field public static mtrl_textinput_box_stroke_width_focused:I = 0x7f070373

.field public static mtrl_textinput_outline_box_expanded_padding:I = 0x7f070376

.field public static mtrl_toolbar_default_height:I = 0x7f070378

.field public static notification_action_icon_size:I = 0x7f07037f

.field public static notification_action_text_size:I = 0x7f070380

.field public static notification_big_circle_margin:I = 0x7f070381

.field public static notification_content_margin_start:I = 0x7f070382

.field public static notification_large_icon_height:I = 0x7f070383

.field public static notification_large_icon_width:I = 0x7f070384

.field public static notification_main_column_padding_top:I = 0x7f070385

.field public static notification_media_narrow_margin:I = 0x7f070386

.field public static notification_right_icon_size:I = 0x7f070387

.field public static notification_right_side_padding_top:I = 0x7f070388

.field public static notification_small_icon_background_padding:I = 0x7f070389

.field public static notification_small_icon_size_as_large:I = 0x7f07038a

.field public static notification_subtext_size:I = 0x7f07038b

.field public static notification_top_pad:I = 0x7f07038c

.field public static notification_top_pad_large_text:I = 0x7f07038d

.field public static tooltip_corner_radius:I = 0x7f0703be

.field public static tooltip_horizontal_padding:I = 0x7f0703bf

.field public static tooltip_margin:I = 0x7f0703c0

.field public static tooltip_precise_anchor_extra_offset:I = 0x7f0703c1

.field public static tooltip_precise_anchor_threshold:I = 0x7f0703c2

.field public static tooltip_vertical_padding:I = 0x7f0703c3

.field public static tooltip_y_offset_non_touch:I = 0x7f0703c4

.field public static tooltip_y_offset_touch:I = 0x7f0703c5


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
