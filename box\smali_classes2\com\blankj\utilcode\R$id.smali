.class public final Lcom/blankj/utilcode/R$id;
.super Ljava/lang/Object;


# static fields
.field public static action_bar:I = 0x7f0a0059

.field public static action_bar_activity_content:I = 0x7f0a005a

.field public static action_bar_container:I = 0x7f0a005b

.field public static action_bar_root:I = 0x7f0a005c

.field public static action_bar_spinner:I = 0x7f0a005d

.field public static action_bar_subtitle:I = 0x7f0a005e

.field public static action_bar_title:I = 0x7f0a005f

.field public static action_container:I = 0x7f0a0060

.field public static action_context_bar:I = 0x7f0a0061

.field public static action_divider:I = 0x7f0a0062

.field public static action_image:I = 0x7f0a0063

.field public static action_menu_divider:I = 0x7f0a0064

.field public static action_menu_presenter:I = 0x7f0a0065

.field public static action_mode_bar:I = 0x7f0a0066

.field public static action_mode_bar_stub:I = 0x7f0a0067

.field public static action_mode_close_button:I = 0x7f0a0068

.field public static action_text:I = 0x7f0a0069

.field public static actions:I = 0x7f0a006a

.field public static activity_chooser_view_content:I = 0x7f0a006b

.field public static add:I = 0x7f0a008d

.field public static alertTitle:I = 0x7f0a0091

.field public static async:I = 0x7f0a00ab

.field public static auto:I = 0x7f0a00ac

.field public static blocking:I = 0x7f0a00d0

.field public static bottom:I = 0x7f0a00d3

.field public static buttonPanel:I = 0x7f0a011e

.field public static center:I = 0x7f0a0134

.field public static checkbox:I = 0x7f0a0140

.field public static chronometer:I = 0x7f0a0142

.field public static container:I = 0x7f0a0190

.field public static content:I = 0x7f0a0191

.field public static contentPanel:I = 0x7f0a0195

.field public static coordinator:I = 0x7f0a019d

.field public static custom:I = 0x7f0a01ab

.field public static customPanel:I = 0x7f0a01ac

.field public static decor_content_parent:I = 0x7f0a01b4

.field public static default_activity_button:I = 0x7f0a01b5

.field public static design_bottom_sheet:I = 0x7f0a01bb

.field public static design_menu_item_action_area:I = 0x7f0a01bc

.field public static design_menu_item_action_area_stub:I = 0x7f0a01bd

.field public static design_menu_item_text:I = 0x7f0a01be

.field public static design_navigation_view:I = 0x7f0a01bf

.field public static edit_query:I = 0x7f0a01ec

.field public static end:I = 0x7f0a01f7

.field public static expand_activities_button:I = 0x7f0a024d

.field public static expanded_menu:I = 0x7f0a024e

.field public static fill:I = 0x7f0a025a

.field public static filled:I = 0x7f0a025d

.field public static fixed:I = 0x7f0a0269

.field public static forever:I = 0x7f0a02b7

.field public static ghost_view:I = 0x7f0a02c1

.field public static group_divider:I = 0x7f0a02db

.field public static home:I = 0x7f0a0300

.field public static icon:I = 0x7f0a030f

.field public static icon_group:I = 0x7f0a0312

.field public static image:I = 0x7f0a0323

.field public static info:I = 0x7f0a0337

.field public static italic:I = 0x7f0a034a

.field public static item_touch_helper_previous_elevation:I = 0x7f0a0379

.field public static labeled:I = 0x7f0a04ae

.field public static left:I = 0x7f0a04dc

.field public static line1:I = 0x7f0a04e8

.field public static line3:I = 0x7f0a04e9

.field public static listMode:I = 0x7f0a04ec

.field public static list_item:I = 0x7f0a04ed

.field public static masked:I = 0x7f0a0571

.field public static message:I = 0x7f0a0654

.field public static mini:I = 0x7f0a065c

.field public static mtrl_child_content_container:I = 0x7f0a0674

.field public static mtrl_internal_children_alpha_tag:I = 0x7f0a0675

.field public static multiply:I = 0x7f0a0681

.field public static navigation_header_container:I = 0x7f0a069f

.field public static none:I = 0x7f0a06ad

.field public static normal:I = 0x7f0a06ae

.field public static notification_background:I = 0x7f0a06b7

.field public static notification_main_column:I = 0x7f0a06bc

.field public static notification_main_column_container:I = 0x7f0a06bd

.field public static outline:I = 0x7f0a06f7

.field public static parallax:I = 0x7f0a06fe

.field public static parentPanel:I = 0x7f0a0700

.field public static parent_matrix:I = 0x7f0a0702

.field public static pin:I = 0x7f0a0714

.field public static progress_circular:I = 0x7f0a074a

.field public static progress_horizontal:I = 0x7f0a074c

.field public static radio:I = 0x7f0a0757

.field public static right:I = 0x7f0a0791

.field public static right_icon:I = 0x7f0a0796

.field public static right_side:I = 0x7f0a0798

.field public static save_non_transition_alpha:I = 0x7f0a07cd

.field public static screen:I = 0x7f0a07d1

.field public static scrollIndicatorDown:I = 0x7f0a07d3

.field public static scrollIndicatorUp:I = 0x7f0a07d4

.field public static scrollView:I = 0x7f0a07d5

.field public static scrollable:I = 0x7f0a07d8

.field public static search_badge:I = 0x7f0a07db

.field public static search_bar:I = 0x7f0a07dc

.field public static search_button:I = 0x7f0a07dd

.field public static search_close_btn:I = 0x7f0a07de

.field public static search_edit_frame:I = 0x7f0a07e0

.field public static search_go_btn:I = 0x7f0a07e2

.field public static search_mag_icon:I = 0x7f0a07f6

.field public static search_plate:I = 0x7f0a07f7

.field public static search_src_text:I = 0x7f0a0819

.field public static search_voice_btn:I = 0x7f0a081a

.field public static select_dialog_listview:I = 0x7f0a0824

.field public static selected:I = 0x7f0a0827

.field public static shortcut:I = 0x7f0a0833

.field public static snackbar_action:I = 0x7f0a0841

.field public static snackbar_text:I = 0x7f0a0842

.field public static spacer:I = 0x7f0a084d

.field public static split_action_bar:I = 0x7f0a0853

.field public static src_atop:I = 0x7f0a0858

.field public static src_in:I = 0x7f0a0859

.field public static src_over:I = 0x7f0a085a

.field public static start:I = 0x7f0a085d

.field public static stretch:I = 0x7f0a086a

.field public static submenuarrow:I = 0x7f0a08b2

.field public static submit_area:I = 0x7f0a08b4

.field public static tabMode:I = 0x7f0a08cb

.field public static tag_transition_group:I = 0x7f0a08e3

.field public static tag_unhandled_key_event_manager:I = 0x7f0a08e4

.field public static tag_unhandled_key_listeners:I = 0x7f0a08e5

.field public static text:I = 0x7f0a08ea

.field public static text2:I = 0x7f0a08eb

.field public static textSpacerNoButtons:I = 0x7f0a08ed

.field public static textSpacerNoTitle:I = 0x7f0a08ee

.field public static textinput_counter:I = 0x7f0a08f8

.field public static textinput_error:I = 0x7f0a08f9

.field public static textinput_helper_text:I = 0x7f0a08fa

.field public static time:I = 0x7f0a0902

.field public static title:I = 0x7f0a0908

.field public static titleDividerNoCustom:I = 0x7f0a090a

.field public static title_template:I = 0x7f0a0913

.field public static top:I = 0x7f0a091e

.field public static topPanel:I = 0x7f0a0921

.field public static touch_outside:I = 0x7f0a0929

.field public static transition_current_scene:I = 0x7f0a093d

.field public static transition_layout_save:I = 0x7f0a093f

.field public static transition_position:I = 0x7f0a0941

.field public static transition_scene_layoutid_cache:I = 0x7f0a0942

.field public static transition_transform:I = 0x7f0a0943

.field public static uniform:I = 0x7f0a0b41

.field public static unlabeled:I = 0x7f0a0b42

.field public static up:I = 0x7f0a0b43

.field public static utvBottomIconView:I = 0x7f0a0b51

.field public static utvLeftIconView:I = 0x7f0a0b52

.field public static utvRightIconView:I = 0x7f0a0b53

.field public static utvTopIconView:I = 0x7f0a0b54

.field public static view_offset_helper:I = 0x7f0a0c0a

.field public static visible:I = 0x7f0a0c15

.field public static wrap_content:I = 0x7f0a0c33


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
