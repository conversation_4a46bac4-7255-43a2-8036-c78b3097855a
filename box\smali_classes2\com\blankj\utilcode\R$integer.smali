.class public final Lcom/blankj/utilcode/R$integer;
.super Ljava/lang/Object;


# static fields
.field public static abc_config_activityDefaultDur:I = 0x7f0b0000

.field public static abc_config_activityShortDur:I = 0x7f0b0001

.field public static app_bar_elevation_anim_duration:I = 0x7f0b0004

.field public static bottom_sheet_slide_duration:I = 0x7f0b0005

.field public static cancel_button_image_alpha:I = 0x7f0b0006

.field public static config_tooltipAnimTime:I = 0x7f0b0008

.field public static design_snackbar_text_max_lines:I = 0x7f0b000a

.field public static design_tab_indicator_anim_duration_ms:I = 0x7f0b000b

.field public static hide_password_duration:I = 0x7f0b000f

.field public static mtrl_btn_anim_delay_ms:I = 0x7f0b0035

.field public static mtrl_btn_anim_duration_ms:I = 0x7f0b0036

.field public static mtrl_chip_anim_duration:I = 0x7f0b003c

.field public static mtrl_tab_indicator_anim_duration_ms:I = 0x7f0b0045

.field public static show_password_duration:I = 0x7f0b0049

.field public static status_bar_notification_info_maxnum:I = 0x7f0b004a


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
