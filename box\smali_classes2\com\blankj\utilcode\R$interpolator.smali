.class public final Lcom/blankj/utilcode/R$interpolator;
.super Ljava/lang/Object;


# static fields
.field public static mtrl_fast_out_linear_in:I = 0x7f0c000e

.field public static mtrl_fast_out_slow_in:I = 0x7f0c000f

.field public static mtrl_linear:I = 0x7f0c0010

.field public static mtrl_linear_out_slow_in:I = 0x7f0c0011


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
