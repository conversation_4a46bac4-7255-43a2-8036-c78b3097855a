.class public final Lcom/blankj/utilcode/R$styleable;
.super Ljava/lang/Object;


# static fields
.field public static ActionBar:[I = null

.field public static ActionBarLayout:[I = null

.field public static ActionBarLayout_android_layout_gravity:I = 0x0

.field public static ActionBar_background:I = 0x0

.field public static ActionBar_backgroundSplit:I = 0x1

.field public static ActionBar_backgroundStacked:I = 0x2

.field public static ActionBar_contentInsetEnd:I = 0x3

.field public static ActionBar_contentInsetEndWithActions:I = 0x4

.field public static ActionBar_contentInsetLeft:I = 0x5

.field public static ActionBar_contentInsetRight:I = 0x6

.field public static ActionBar_contentInsetStart:I = 0x7

.field public static ActionBar_contentInsetStartWithNavigation:I = 0x8

.field public static ActionBar_customNavigationLayout:I = 0x9

.field public static ActionBar_displayOptions:I = 0xa

.field public static ActionBar_divider:I = 0xb

.field public static ActionBar_elevation:I = 0xc

.field public static ActionBar_height:I = 0xd

.field public static ActionBar_hideOnContentScroll:I = 0xe

.field public static ActionBar_homeAsUpIndicator:I = 0xf

.field public static ActionBar_homeLayout:I = 0x10

.field public static ActionBar_icon:I = 0x11

.field public static ActionBar_indeterminateProgressStyle:I = 0x12

.field public static ActionBar_itemPadding:I = 0x13

.field public static ActionBar_logo:I = 0x14

.field public static ActionBar_navigationMode:I = 0x15

.field public static ActionBar_popupTheme:I = 0x16

.field public static ActionBar_progressBarPadding:I = 0x17

.field public static ActionBar_progressBarStyle:I = 0x18

.field public static ActionBar_subtitle:I = 0x19

.field public static ActionBar_subtitleTextStyle:I = 0x1a

.field public static ActionBar_title:I = 0x1b

.field public static ActionBar_titleTextStyle:I = 0x1c

.field public static ActionMenuItemView:[I = null

.field public static ActionMenuItemView_android_minWidth:I = 0x0

.field public static ActionMenuView:[I = null

.field public static ActionMode:[I = null

.field public static ActionMode_background:I = 0x0

.field public static ActionMode_backgroundSplit:I = 0x1

.field public static ActionMode_closeItemLayout:I = 0x2

.field public static ActionMode_height:I = 0x3

.field public static ActionMode_subtitleTextStyle:I = 0x4

.field public static ActionMode_titleTextStyle:I = 0x5

.field public static ActivityChooserView:[I = null

.field public static ActivityChooserView_expandActivityOverflowButtonDrawable:I = 0x0

.field public static ActivityChooserView_initialActivityCount:I = 0x1

.field public static AlertDialog:[I = null

.field public static AlertDialog_android_layout:I = 0x0

.field public static AlertDialog_buttonIconDimen:I = 0x1

.field public static AlertDialog_buttonPanelSideLayout:I = 0x2

.field public static AlertDialog_listItemLayout:I = 0x3

.field public static AlertDialog_listLayout:I = 0x4

.field public static AlertDialog_multiChoiceItemLayout:I = 0x5

.field public static AlertDialog_showTitle:I = 0x6

.field public static AlertDialog_singleChoiceItemLayout:I = 0x7

.field public static AnimatedStateListDrawableCompat:[I = null

.field public static AnimatedStateListDrawableCompat_android_constantSize:I = 0x3

.field public static AnimatedStateListDrawableCompat_android_dither:I = 0x0

.field public static AnimatedStateListDrawableCompat_android_enterFadeDuration:I = 0x4

.field public static AnimatedStateListDrawableCompat_android_exitFadeDuration:I = 0x5

.field public static AnimatedStateListDrawableCompat_android_variablePadding:I = 0x2

.field public static AnimatedStateListDrawableCompat_android_visible:I = 0x1

.field public static AnimatedStateListDrawableItem:[I = null

.field public static AnimatedStateListDrawableItem_android_drawable:I = 0x1

.field public static AnimatedStateListDrawableItem_android_id:I = 0x0

.field public static AnimatedStateListDrawableTransition:[I = null

.field public static AnimatedStateListDrawableTransition_android_drawable:I = 0x0

.field public static AnimatedStateListDrawableTransition_android_fromId:I = 0x2

.field public static AnimatedStateListDrawableTransition_android_reversible:I = 0x3

.field public static AnimatedStateListDrawableTransition_android_toId:I = 0x1

.field public static AppBarLayout:[I = null

.field public static AppBarLayoutStates:[I = null

.field public static AppBarLayoutStates_state_collapsed:I = 0x0

.field public static AppBarLayoutStates_state_collapsible:I = 0x1

.field public static AppBarLayoutStates_state_liftable:I = 0x2

.field public static AppBarLayoutStates_state_lifted:I = 0x3

.field public static AppBarLayout_Layout:[I = null

.field public static AppBarLayout_Layout_layout_scrollEffect:I = 0x0

.field public static AppBarLayout_Layout_layout_scrollFlags:I = 0x1

.field public static AppBarLayout_Layout_layout_scrollInterpolator:I = 0x2

.field public static AppBarLayout_android_background:I = 0x0

.field public static AppBarLayout_android_keyboardNavigationCluster:I = 0x2

.field public static AppBarLayout_android_touchscreenBlocksFocus:I = 0x1

.field public static AppBarLayout_elevation:I = 0x3

.field public static AppBarLayout_expanded:I = 0x4

.field public static AppBarLayout_liftOnScroll:I = 0x5

.field public static AppBarLayout_liftOnScrollColor:I = 0x6

.field public static AppBarLayout_liftOnScrollTargetViewId:I = 0x7

.field public static AppBarLayout_statusBarForeground:I = 0x8

.field public static AppCompatImageView:[I = null

.field public static AppCompatImageView_android_src:I = 0x0

.field public static AppCompatImageView_srcCompat:I = 0x1

.field public static AppCompatImageView_tint:I = 0x2

.field public static AppCompatImageView_tintMode:I = 0x3

.field public static AppCompatSeekBar:[I = null

.field public static AppCompatSeekBar_android_thumb:I = 0x0

.field public static AppCompatSeekBar_tickMark:I = 0x1

.field public static AppCompatSeekBar_tickMarkTint:I = 0x2

.field public static AppCompatSeekBar_tickMarkTintMode:I = 0x3

.field public static AppCompatTextHelper:[I = null

.field public static AppCompatTextHelper_android_drawableBottom:I = 0x2

.field public static AppCompatTextHelper_android_drawableEnd:I = 0x6

.field public static AppCompatTextHelper_android_drawableLeft:I = 0x3

.field public static AppCompatTextHelper_android_drawableRight:I = 0x4

.field public static AppCompatTextHelper_android_drawableStart:I = 0x5

.field public static AppCompatTextHelper_android_drawableTop:I = 0x1

.field public static AppCompatTextHelper_android_textAppearance:I = 0x0

.field public static AppCompatTextView:[I = null

.field public static AppCompatTextView_android_textAppearance:I = 0x0

.field public static AppCompatTextView_autoSizeMaxTextSize:I = 0x1

.field public static AppCompatTextView_autoSizeMinTextSize:I = 0x2

.field public static AppCompatTextView_autoSizePresetSizes:I = 0x3

.field public static AppCompatTextView_autoSizeStepGranularity:I = 0x4

.field public static AppCompatTextView_autoSizeTextType:I = 0x5

.field public static AppCompatTextView_drawableBottomCompat:I = 0x6

.field public static AppCompatTextView_drawableEndCompat:I = 0x7

.field public static AppCompatTextView_drawableLeftCompat:I = 0x8

.field public static AppCompatTextView_drawableRightCompat:I = 0x9

.field public static AppCompatTextView_drawableStartCompat:I = 0xa

.field public static AppCompatTextView_drawableTint:I = 0xb

.field public static AppCompatTextView_drawableTintMode:I = 0xc

.field public static AppCompatTextView_drawableTopCompat:I = 0xd

.field public static AppCompatTextView_emojiCompatEnabled:I = 0xe

.field public static AppCompatTextView_firstBaselineToTopHeight:I = 0xf

.field public static AppCompatTextView_fontFamily:I = 0x10

.field public static AppCompatTextView_fontVariationSettings:I = 0x11

.field public static AppCompatTextView_lastBaselineToBottomHeight:I = 0x12

.field public static AppCompatTextView_lineHeight:I = 0x13

.field public static AppCompatTextView_textAllCaps:I = 0x14

.field public static AppCompatTextView_textLocale:I = 0x15

.field public static AppCompatTheme:[I = null

.field public static AppCompatTheme_actionBarDivider:I = 0x2

.field public static AppCompatTheme_actionBarItemBackground:I = 0x3

.field public static AppCompatTheme_actionBarPopupTheme:I = 0x4

.field public static AppCompatTheme_actionBarSize:I = 0x5

.field public static AppCompatTheme_actionBarSplitStyle:I = 0x6

.field public static AppCompatTheme_actionBarStyle:I = 0x7

.field public static AppCompatTheme_actionBarTabBarStyle:I = 0x8

.field public static AppCompatTheme_actionBarTabStyle:I = 0x9

.field public static AppCompatTheme_actionBarTabTextStyle:I = 0xa

.field public static AppCompatTheme_actionBarTheme:I = 0xb

.field public static AppCompatTheme_actionBarWidgetTheme:I = 0xc

.field public static AppCompatTheme_actionButtonStyle:I = 0xd

.field public static AppCompatTheme_actionDropDownStyle:I = 0xe

.field public static AppCompatTheme_actionMenuTextAppearance:I = 0xf

.field public static AppCompatTheme_actionMenuTextColor:I = 0x10

.field public static AppCompatTheme_actionModeBackground:I = 0x11

.field public static AppCompatTheme_actionModeCloseButtonStyle:I = 0x12

.field public static AppCompatTheme_actionModeCloseContentDescription:I = 0x13

.field public static AppCompatTheme_actionModeCloseDrawable:I = 0x14

.field public static AppCompatTheme_actionModeCopyDrawable:I = 0x15

.field public static AppCompatTheme_actionModeCutDrawable:I = 0x16

.field public static AppCompatTheme_actionModeFindDrawable:I = 0x17

.field public static AppCompatTheme_actionModePasteDrawable:I = 0x18

.field public static AppCompatTheme_actionModePopupWindowStyle:I = 0x19

.field public static AppCompatTheme_actionModeSelectAllDrawable:I = 0x1a

.field public static AppCompatTheme_actionModeShareDrawable:I = 0x1b

.field public static AppCompatTheme_actionModeSplitBackground:I = 0x1c

.field public static AppCompatTheme_actionModeStyle:I = 0x1d

.field public static AppCompatTheme_actionModeTheme:I = 0x1e

.field public static AppCompatTheme_actionModeWebSearchDrawable:I = 0x1f

.field public static AppCompatTheme_actionOverflowButtonStyle:I = 0x20

.field public static AppCompatTheme_actionOverflowMenuStyle:I = 0x21

.field public static AppCompatTheme_activityChooserViewStyle:I = 0x22

.field public static AppCompatTheme_alertDialogButtonGroupStyle:I = 0x23

.field public static AppCompatTheme_alertDialogCenterButtons:I = 0x24

.field public static AppCompatTheme_alertDialogStyle:I = 0x25

.field public static AppCompatTheme_alertDialogTheme:I = 0x26

.field public static AppCompatTheme_android_windowAnimationStyle:I = 0x1

.field public static AppCompatTheme_android_windowIsFloating:I = 0x0

.field public static AppCompatTheme_autoCompleteTextViewStyle:I = 0x27

.field public static AppCompatTheme_borderlessButtonStyle:I = 0x28

.field public static AppCompatTheme_buttonBarButtonStyle:I = 0x29

.field public static AppCompatTheme_buttonBarNegativeButtonStyle:I = 0x2a

.field public static AppCompatTheme_buttonBarNeutralButtonStyle:I = 0x2b

.field public static AppCompatTheme_buttonBarPositiveButtonStyle:I = 0x2c

.field public static AppCompatTheme_buttonBarStyle:I = 0x2d

.field public static AppCompatTheme_buttonStyle:I = 0x2e

.field public static AppCompatTheme_buttonStyleSmall:I = 0x2f

.field public static AppCompatTheme_checkboxStyle:I = 0x30

.field public static AppCompatTheme_checkedTextViewStyle:I = 0x31

.field public static AppCompatTheme_colorAccent:I = 0x32

.field public static AppCompatTheme_colorBackgroundFloating:I = 0x33

.field public static AppCompatTheme_colorButtonNormal:I = 0x34

.field public static AppCompatTheme_colorControlActivated:I = 0x35

.field public static AppCompatTheme_colorControlHighlight:I = 0x36

.field public static AppCompatTheme_colorControlNormal:I = 0x37

.field public static AppCompatTheme_colorError:I = 0x38

.field public static AppCompatTheme_colorPrimary:I = 0x39

.field public static AppCompatTheme_colorPrimaryDark:I = 0x3a

.field public static AppCompatTheme_colorSwitchThumbNormal:I = 0x3b

.field public static AppCompatTheme_controlBackground:I = 0x3c

.field public static AppCompatTheme_dialogCornerRadius:I = 0x3d

.field public static AppCompatTheme_dialogPreferredPadding:I = 0x3e

.field public static AppCompatTheme_dialogTheme:I = 0x3f

.field public static AppCompatTheme_dividerHorizontal:I = 0x40

.field public static AppCompatTheme_dividerVertical:I = 0x41

.field public static AppCompatTheme_dropDownListViewStyle:I = 0x42

.field public static AppCompatTheme_dropdownListPreferredItemHeight:I = 0x43

.field public static AppCompatTheme_editTextBackground:I = 0x44

.field public static AppCompatTheme_editTextColor:I = 0x45

.field public static AppCompatTheme_editTextStyle:I = 0x46

.field public static AppCompatTheme_homeAsUpIndicator:I = 0x47

.field public static AppCompatTheme_imageButtonStyle:I = 0x48

.field public static AppCompatTheme_listChoiceBackgroundIndicator:I = 0x49

.field public static AppCompatTheme_listChoiceIndicatorMultipleAnimated:I = 0x4a

.field public static AppCompatTheme_listChoiceIndicatorSingleAnimated:I = 0x4b

.field public static AppCompatTheme_listDividerAlertDialog:I = 0x4c

.field public static AppCompatTheme_listMenuViewStyle:I = 0x4d

.field public static AppCompatTheme_listPopupWindowStyle:I = 0x4e

.field public static AppCompatTheme_listPreferredItemHeight:I = 0x4f

.field public static AppCompatTheme_listPreferredItemHeightLarge:I = 0x50

.field public static AppCompatTheme_listPreferredItemHeightSmall:I = 0x51

.field public static AppCompatTheme_listPreferredItemPaddingEnd:I = 0x52

.field public static AppCompatTheme_listPreferredItemPaddingLeft:I = 0x53

.field public static AppCompatTheme_listPreferredItemPaddingRight:I = 0x54

.field public static AppCompatTheme_listPreferredItemPaddingStart:I = 0x55

.field public static AppCompatTheme_panelBackground:I = 0x56

.field public static AppCompatTheme_panelMenuListTheme:I = 0x57

.field public static AppCompatTheme_panelMenuListWidth:I = 0x58

.field public static AppCompatTheme_popupMenuStyle:I = 0x59

.field public static AppCompatTheme_popupWindowStyle:I = 0x5a

.field public static AppCompatTheme_radioButtonStyle:I = 0x5b

.field public static AppCompatTheme_ratingBarStyle:I = 0x5c

.field public static AppCompatTheme_ratingBarStyleIndicator:I = 0x5d

.field public static AppCompatTheme_ratingBarStyleSmall:I = 0x5e

.field public static AppCompatTheme_searchViewStyle:I = 0x5f

.field public static AppCompatTheme_seekBarStyle:I = 0x60

.field public static AppCompatTheme_selectableItemBackground:I = 0x61

.field public static AppCompatTheme_selectableItemBackgroundBorderless:I = 0x62

.field public static AppCompatTheme_spinnerDropDownItemStyle:I = 0x63

.field public static AppCompatTheme_spinnerStyle:I = 0x64

.field public static AppCompatTheme_switchStyle:I = 0x65

.field public static AppCompatTheme_textAppearanceLargePopupMenu:I = 0x66

.field public static AppCompatTheme_textAppearanceListItem:I = 0x67

.field public static AppCompatTheme_textAppearanceListItemSecondary:I = 0x68

.field public static AppCompatTheme_textAppearanceListItemSmall:I = 0x69

.field public static AppCompatTheme_textAppearancePopupMenuHeader:I = 0x6a

.field public static AppCompatTheme_textAppearanceSearchResultSubtitle:I = 0x6b

.field public static AppCompatTheme_textAppearanceSearchResultTitle:I = 0x6c

.field public static AppCompatTheme_textAppearanceSmallPopupMenu:I = 0x6d

.field public static AppCompatTheme_textColorAlertDialogListItem:I = 0x6e

.field public static AppCompatTheme_textColorSearchUrl:I = 0x6f

.field public static AppCompatTheme_toolbarNavigationButtonStyle:I = 0x70

.field public static AppCompatTheme_toolbarStyle:I = 0x71

.field public static AppCompatTheme_tooltipForegroundColor:I = 0x72

.field public static AppCompatTheme_tooltipFrameBackground:I = 0x73

.field public static AppCompatTheme_viewInflaterClass:I = 0x74

.field public static AppCompatTheme_windowActionBar:I = 0x75

.field public static AppCompatTheme_windowActionBarOverlay:I = 0x76

.field public static AppCompatTheme_windowActionModeOverlay:I = 0x77

.field public static AppCompatTheme_windowFixedHeightMajor:I = 0x78

.field public static AppCompatTheme_windowFixedHeightMinor:I = 0x79

.field public static AppCompatTheme_windowFixedWidthMajor:I = 0x7a

.field public static AppCompatTheme_windowFixedWidthMinor:I = 0x7b

.field public static AppCompatTheme_windowMinWidthMajor:I = 0x7c

.field public static AppCompatTheme_windowMinWidthMinor:I = 0x7d

.field public static AppCompatTheme_windowNoTitle:I = 0x7e

.field public static BottomAppBar:[I = null

.field public static BottomAppBar_addElevationShadow:I = 0x0

.field public static BottomAppBar_backgroundTint:I = 0x1

.field public static BottomAppBar_elevation:I = 0x2

.field public static BottomAppBar_fabAlignmentMode:I = 0x3

.field public static BottomAppBar_fabAlignmentModeEndMargin:I = 0x4

.field public static BottomAppBar_fabAnchorMode:I = 0x5

.field public static BottomAppBar_fabAnimationMode:I = 0x6

.field public static BottomAppBar_fabCradleMargin:I = 0x7

.field public static BottomAppBar_fabCradleRoundedCornerRadius:I = 0x8

.field public static BottomAppBar_fabCradleVerticalOffset:I = 0x9

.field public static BottomAppBar_hideOnScroll:I = 0xa

.field public static BottomAppBar_menuAlignmentMode:I = 0xb

.field public static BottomAppBar_navigationIconTint:I = 0xc

.field public static BottomAppBar_paddingBottomSystemWindowInsets:I = 0xd

.field public static BottomAppBar_paddingLeftSystemWindowInsets:I = 0xe

.field public static BottomAppBar_paddingRightSystemWindowInsets:I = 0xf

.field public static BottomAppBar_removeEmbeddedFabElevation:I = 0x10

.field public static BottomNavigationView:[I = null

.field public static BottomNavigationView_android_minHeight:I = 0x0

.field public static BottomNavigationView_compatShadowEnabled:I = 0x1

.field public static BottomNavigationView_itemHorizontalTranslationEnabled:I = 0x2

.field public static BottomNavigationView_shapeAppearance:I = 0x3

.field public static BottomNavigationView_shapeAppearanceOverlay:I = 0x4

.field public static BottomSheetBehavior_Layout:[I = null

.field public static BottomSheetBehavior_Layout_android_elevation:I = 0x2

.field public static BottomSheetBehavior_Layout_android_maxHeight:I = 0x1

.field public static BottomSheetBehavior_Layout_android_maxWidth:I = 0x0

.field public static BottomSheetBehavior_Layout_backgroundTint:I = 0x3

.field public static BottomSheetBehavior_Layout_behavior_draggable:I = 0x4

.field public static BottomSheetBehavior_Layout_behavior_expandedOffset:I = 0x5

.field public static BottomSheetBehavior_Layout_behavior_fitToContents:I = 0x6

.field public static BottomSheetBehavior_Layout_behavior_halfExpandedRatio:I = 0x7

.field public static BottomSheetBehavior_Layout_behavior_hideable:I = 0x8

.field public static BottomSheetBehavior_Layout_behavior_peekHeight:I = 0x9

.field public static BottomSheetBehavior_Layout_behavior_saveFlags:I = 0xa

.field public static BottomSheetBehavior_Layout_behavior_significantVelocityThreshold:I = 0xb

.field public static BottomSheetBehavior_Layout_behavior_skipCollapsed:I = 0xc

.field public static BottomSheetBehavior_Layout_gestureInsetBottomIgnored:I = 0xd

.field public static BottomSheetBehavior_Layout_marginLeftSystemWindowInsets:I = 0xe

.field public static BottomSheetBehavior_Layout_marginRightSystemWindowInsets:I = 0xf

.field public static BottomSheetBehavior_Layout_marginTopSystemWindowInsets:I = 0x10

.field public static BottomSheetBehavior_Layout_paddingBottomSystemWindowInsets:I = 0x11

.field public static BottomSheetBehavior_Layout_paddingLeftSystemWindowInsets:I = 0x12

.field public static BottomSheetBehavior_Layout_paddingRightSystemWindowInsets:I = 0x13

.field public static BottomSheetBehavior_Layout_paddingTopSystemWindowInsets:I = 0x14

.field public static BottomSheetBehavior_Layout_shapeAppearance:I = 0x15

.field public static BottomSheetBehavior_Layout_shapeAppearanceOverlay:I = 0x16

.field public static BottomSheetBehavior_Layout_shouldRemoveExpandedCorners:I = 0x17

.field public static ButtonBarLayout:[I = null

.field public static ButtonBarLayout_allowStacking:I = 0x0

.field public static CardView:[I = null

.field public static CardView_android_minHeight:I = 0x1

.field public static CardView_android_minWidth:I = 0x0

.field public static CardView_cardBackgroundColor:I = 0x2

.field public static CardView_cardCornerRadius:I = 0x3

.field public static CardView_cardElevation:I = 0x4

.field public static CardView_cardMaxElevation:I = 0x5

.field public static CardView_cardPreventCornerOverlap:I = 0x6

.field public static CardView_cardUseCompatPadding:I = 0x7

.field public static CardView_contentPadding:I = 0x8

.field public static CardView_contentPaddingBottom:I = 0x9

.field public static CardView_contentPaddingLeft:I = 0xa

.field public static CardView_contentPaddingRight:I = 0xb

.field public static CardView_contentPaddingTop:I = 0xc

.field public static Chip:[I = null

.field public static ChipGroup:[I = null

.field public static ChipGroup_checkedChip:I = 0x0

.field public static ChipGroup_chipSpacing:I = 0x1

.field public static ChipGroup_chipSpacingHorizontal:I = 0x2

.field public static ChipGroup_chipSpacingVertical:I = 0x3

.field public static ChipGroup_selectionRequired:I = 0x4

.field public static ChipGroup_singleLine:I = 0x5

.field public static ChipGroup_singleSelection:I = 0x6

.field public static Chip_android_checkable:I = 0x6

.field public static Chip_android_ellipsize:I = 0x3

.field public static Chip_android_maxWidth:I = 0x4

.field public static Chip_android_text:I = 0x5

.field public static Chip_android_textAppearance:I = 0x0

.field public static Chip_android_textColor:I = 0x2

.field public static Chip_android_textSize:I = 0x1

.field public static Chip_checkedIcon:I = 0x7

.field public static Chip_checkedIconEnabled:I = 0x8

.field public static Chip_checkedIconTint:I = 0x9

.field public static Chip_checkedIconVisible:I = 0xa

.field public static Chip_chipBackgroundColor:I = 0xb

.field public static Chip_chipCornerRadius:I = 0xc

.field public static Chip_chipEndPadding:I = 0xd

.field public static Chip_chipIcon:I = 0xe

.field public static Chip_chipIconEnabled:I = 0xf

.field public static Chip_chipIconSize:I = 0x10

.field public static Chip_chipIconTint:I = 0x11

.field public static Chip_chipIconVisible:I = 0x12

.field public static Chip_chipMinHeight:I = 0x13

.field public static Chip_chipMinTouchTargetSize:I = 0x14

.field public static Chip_chipStartPadding:I = 0x15

.field public static Chip_chipStrokeColor:I = 0x16

.field public static Chip_chipStrokeWidth:I = 0x17

.field public static Chip_chipSurfaceColor:I = 0x18

.field public static Chip_closeIcon:I = 0x19

.field public static Chip_closeIconEnabled:I = 0x1a

.field public static Chip_closeIconEndPadding:I = 0x1b

.field public static Chip_closeIconSize:I = 0x1c

.field public static Chip_closeIconStartPadding:I = 0x1d

.field public static Chip_closeIconTint:I = 0x1e

.field public static Chip_closeIconVisible:I = 0x1f

.field public static Chip_ensureMinTouchTargetSize:I = 0x20

.field public static Chip_hideMotionSpec:I = 0x21

.field public static Chip_iconEndPadding:I = 0x22

.field public static Chip_iconStartPadding:I = 0x23

.field public static Chip_rippleColor:I = 0x24

.field public static Chip_shapeAppearance:I = 0x25

.field public static Chip_shapeAppearanceOverlay:I = 0x26

.field public static Chip_showMotionSpec:I = 0x27

.field public static Chip_textEndPadding:I = 0x28

.field public static Chip_textStartPadding:I = 0x29

.field public static CollapsingToolbarLayout:[I = null

.field public static CollapsingToolbarLayout_Layout:[I = null

.field public static CollapsingToolbarLayout_Layout_layout_collapseMode:I = 0x0

.field public static CollapsingToolbarLayout_Layout_layout_collapseParallaxMultiplier:I = 0x1

.field public static CollapsingToolbarLayout_collapsedTitleGravity:I = 0x0

.field public static CollapsingToolbarLayout_collapsedTitleTextAppearance:I = 0x1

.field public static CollapsingToolbarLayout_collapsedTitleTextColor:I = 0x2

.field public static CollapsingToolbarLayout_contentScrim:I = 0x3

.field public static CollapsingToolbarLayout_expandedTitleGravity:I = 0x4

.field public static CollapsingToolbarLayout_expandedTitleMargin:I = 0x5

.field public static CollapsingToolbarLayout_expandedTitleMarginBottom:I = 0x6

.field public static CollapsingToolbarLayout_expandedTitleMarginEnd:I = 0x7

.field public static CollapsingToolbarLayout_expandedTitleMarginStart:I = 0x8

.field public static CollapsingToolbarLayout_expandedTitleMarginTop:I = 0x9

.field public static CollapsingToolbarLayout_expandedTitleTextAppearance:I = 0xa

.field public static CollapsingToolbarLayout_expandedTitleTextColor:I = 0xb

.field public static CollapsingToolbarLayout_extraMultilineHeightEnabled:I = 0xc

.field public static CollapsingToolbarLayout_forceApplySystemWindowInsetTop:I = 0xd

.field public static CollapsingToolbarLayout_maxLines:I = 0xe

.field public static CollapsingToolbarLayout_scrimAnimationDuration:I = 0xf

.field public static CollapsingToolbarLayout_scrimVisibleHeightTrigger:I = 0x10

.field public static CollapsingToolbarLayout_statusBarScrim:I = 0x11

.field public static CollapsingToolbarLayout_title:I = 0x12

.field public static CollapsingToolbarLayout_titleCollapseMode:I = 0x13

.field public static CollapsingToolbarLayout_titleEnabled:I = 0x14

.field public static CollapsingToolbarLayout_titlePositionInterpolator:I = 0x15

.field public static CollapsingToolbarLayout_titleTextEllipsize:I = 0x16

.field public static CollapsingToolbarLayout_toolbarId:I = 0x17

.field public static ColorStateListItem:[I = null

.field public static ColorStateListItem_alpha:I = 0x3

.field public static ColorStateListItem_android_alpha:I = 0x1

.field public static ColorStateListItem_android_color:I = 0x0

.field public static ColorStateListItem_android_lStar:I = 0x2

.field public static ColorStateListItem_lStar:I = 0x4

.field public static CompoundButton:[I = null

.field public static CompoundButton_android_button:I = 0x0

.field public static CompoundButton_buttonCompat:I = 0x1

.field public static CompoundButton_buttonTint:I = 0x2

.field public static CompoundButton_buttonTintMode:I = 0x3

.field public static CoordinatorLayout:[I = null

.field public static CoordinatorLayout_Layout:[I = null

.field public static CoordinatorLayout_Layout_android_layout_gravity:I = 0x0

.field public static CoordinatorLayout_Layout_layout_anchor:I = 0x1

.field public static CoordinatorLayout_Layout_layout_anchorGravity:I = 0x2

.field public static CoordinatorLayout_Layout_layout_behavior:I = 0x3

.field public static CoordinatorLayout_Layout_layout_dodgeInsetEdges:I = 0x4

.field public static CoordinatorLayout_Layout_layout_insetEdge:I = 0x5

.field public static CoordinatorLayout_Layout_layout_keyline:I = 0x6

.field public static CoordinatorLayout_keylines:I = 0x0

.field public static CoordinatorLayout_statusBarBackground:I = 0x1

.field public static DrawerArrowToggle:[I = null

.field public static DrawerArrowToggle_arrowHeadLength:I = 0x0

.field public static DrawerArrowToggle_arrowShaftLength:I = 0x1

.field public static DrawerArrowToggle_barLength:I = 0x2

.field public static DrawerArrowToggle_color:I = 0x3

.field public static DrawerArrowToggle_drawableSize:I = 0x4

.field public static DrawerArrowToggle_gapBetweenBars:I = 0x5

.field public static DrawerArrowToggle_spinBars:I = 0x6

.field public static DrawerArrowToggle_thickness:I = 0x7

.field public static FloatingActionButton:[I = null

.field public static FloatingActionButton_Behavior_Layout:[I = null

.field public static FloatingActionButton_Behavior_Layout_behavior_autoHide:I = 0x0

.field public static FloatingActionButton_android_enabled:I = 0x0

.field public static FloatingActionButton_backgroundTint:I = 0x1

.field public static FloatingActionButton_backgroundTintMode:I = 0x2

.field public static FloatingActionButton_borderWidth:I = 0x3

.field public static FloatingActionButton_elevation:I = 0x4

.field public static FloatingActionButton_ensureMinTouchTargetSize:I = 0x5

.field public static FloatingActionButton_fabCustomSize:I = 0x6

.field public static FloatingActionButton_fabSize:I = 0x7

.field public static FloatingActionButton_hideMotionSpec:I = 0x8

.field public static FloatingActionButton_hoveredFocusedTranslationZ:I = 0x9

.field public static FloatingActionButton_maxImageSize:I = 0xa

.field public static FloatingActionButton_pressedTranslationZ:I = 0xb

.field public static FloatingActionButton_rippleColor:I = 0xc

.field public static FloatingActionButton_shapeAppearance:I = 0xd

.field public static FloatingActionButton_shapeAppearanceOverlay:I = 0xe

.field public static FloatingActionButton_showMotionSpec:I = 0xf

.field public static FloatingActionButton_useCompatPadding:I = 0x10

.field public static FlowLayout:[I = null

.field public static FlowLayout_android_gravity:I = 0x0

.field public static FlowLayout_flChildSpacing:I = 0x1

.field public static FlowLayout_flChildSpacingForLastRow:I = 0x2

.field public static FlowLayout_flFlow:I = 0x3

.field public static FlowLayout_flMaxRows:I = 0x4

.field public static FlowLayout_flMinChildSpacing:I = 0x5

.field public static FlowLayout_flRowSpacing:I = 0x6

.field public static FlowLayout_flRowVerticalGravity:I = 0x7

.field public static FlowLayout_flRtl:I = 0x8

.field public static FlowLayout_itemSpacing:I = 0x9

.field public static FlowLayout_lineSpacing:I = 0xa

.field public static FontFamily:[I = null

.field public static FontFamilyFont:[I = null

.field public static FontFamilyFont_android_font:I = 0x0

.field public static FontFamilyFont_android_fontStyle:I = 0x2

.field public static FontFamilyFont_android_fontVariationSettings:I = 0x4

.field public static FontFamilyFont_android_fontWeight:I = 0x1

.field public static FontFamilyFont_android_ttcIndex:I = 0x3

.field public static FontFamilyFont_font:I = 0x5

.field public static FontFamilyFont_fontStyle:I = 0x6

.field public static FontFamilyFont_fontVariationSettings:I = 0x7

.field public static FontFamilyFont_fontWeight:I = 0x8

.field public static FontFamilyFont_ttcIndex:I = 0x9

.field public static FontFamily_fontProviderAuthority:I = 0x0

.field public static FontFamily_fontProviderCerts:I = 0x1

.field public static FontFamily_fontProviderFetchStrategy:I = 0x2

.field public static FontFamily_fontProviderFetchTimeout:I = 0x3

.field public static FontFamily_fontProviderPackage:I = 0x4

.field public static FontFamily_fontProviderQuery:I = 0x5

.field public static FontFamily_fontProviderSystemFontFamily:I = 0x6

.field public static ForegroundLinearLayout:[I = null

.field public static ForegroundLinearLayout_android_foreground:I = 0x0

.field public static ForegroundLinearLayout_android_foregroundGravity:I = 0x1

.field public static ForegroundLinearLayout_foregroundInsidePadding:I = 0x2

.field public static GradientColor:[I = null

.field public static GradientColorItem:[I = null

.field public static GradientColorItem_android_color:I = 0x0

.field public static GradientColorItem_android_offset:I = 0x1

.field public static GradientColor_android_centerColor:I = 0x7

.field public static GradientColor_android_centerX:I = 0x3

.field public static GradientColor_android_centerY:I = 0x4

.field public static GradientColor_android_endColor:I = 0x1

.field public static GradientColor_android_endX:I = 0xa

.field public static GradientColor_android_endY:I = 0xb

.field public static GradientColor_android_gradientRadius:I = 0x5

.field public static GradientColor_android_startColor:I = 0x0

.field public static GradientColor_android_startX:I = 0x8

.field public static GradientColor_android_startY:I = 0x9

.field public static GradientColor_android_tileMode:I = 0x6

.field public static GradientColor_android_type:I = 0x2

.field public static LinearLayoutCompat:[I = null

.field public static LinearLayoutCompat_Layout:[I = null

.field public static LinearLayoutCompat_Layout_android_layout_gravity:I = 0x0

.field public static LinearLayoutCompat_Layout_android_layout_height:I = 0x2

.field public static LinearLayoutCompat_Layout_android_layout_weight:I = 0x3

.field public static LinearLayoutCompat_Layout_android_layout_width:I = 0x1

.field public static LinearLayoutCompat_android_baselineAligned:I = 0x2

.field public static LinearLayoutCompat_android_baselineAlignedChildIndex:I = 0x3

.field public static LinearLayoutCompat_android_gravity:I = 0x0

.field public static LinearLayoutCompat_android_orientation:I = 0x1

.field public static LinearLayoutCompat_android_weightSum:I = 0x4

.field public static LinearLayoutCompat_divider:I = 0x5

.field public static LinearLayoutCompat_dividerPadding:I = 0x6

.field public static LinearLayoutCompat_measureWithLargestChild:I = 0x7

.field public static LinearLayoutCompat_showDividers:I = 0x8

.field public static ListPopupWindow:[I = null

.field public static ListPopupWindow_android_dropDownHorizontalOffset:I = 0x0

.field public static ListPopupWindow_android_dropDownVerticalOffset:I = 0x1

.field public static MaterialButton:[I = null

.field public static MaterialButton_android_background:I = 0x0

.field public static MaterialButton_android_checkable:I = 0x5

.field public static MaterialButton_android_insetBottom:I = 0x4

.field public static MaterialButton_android_insetLeft:I = 0x1

.field public static MaterialButton_android_insetRight:I = 0x2

.field public static MaterialButton_android_insetTop:I = 0x3

.field public static MaterialButton_backgroundTint:I = 0x6

.field public static MaterialButton_backgroundTintMode:I = 0x7

.field public static MaterialButton_cornerRadius:I = 0x8

.field public static MaterialButton_elevation:I = 0x9

.field public static MaterialButton_icon:I = 0xa

.field public static MaterialButton_iconGravity:I = 0xb

.field public static MaterialButton_iconPadding:I = 0xc

.field public static MaterialButton_iconSize:I = 0xd

.field public static MaterialButton_iconTint:I = 0xe

.field public static MaterialButton_iconTintMode:I = 0xf

.field public static MaterialButton_rippleColor:I = 0x10

.field public static MaterialButton_shapeAppearance:I = 0x11

.field public static MaterialButton_shapeAppearanceOverlay:I = 0x12

.field public static MaterialButton_strokeColor:I = 0x13

.field public static MaterialButton_strokeWidth:I = 0x14

.field public static MaterialButton_toggleCheckedStateOnClick:I = 0x15

.field public static MaterialCardView:[I = null

.field public static MaterialCardView_android_checkable:I = 0x0

.field public static MaterialCardView_cardForegroundColor:I = 0x1

.field public static MaterialCardView_checkedIcon:I = 0x2

.field public static MaterialCardView_checkedIconGravity:I = 0x3

.field public static MaterialCardView_checkedIconMargin:I = 0x4

.field public static MaterialCardView_checkedIconSize:I = 0x5

.field public static MaterialCardView_checkedIconTint:I = 0x6

.field public static MaterialCardView_rippleColor:I = 0x7

.field public static MaterialCardView_shapeAppearance:I = 0x8

.field public static MaterialCardView_shapeAppearanceOverlay:I = 0x9

.field public static MaterialCardView_state_dragged:I = 0xa

.field public static MaterialCardView_strokeColor:I = 0xb

.field public static MaterialCardView_strokeWidth:I = 0xc

.field public static MenuGroup:[I = null

.field public static MenuGroup_android_checkableBehavior:I = 0x5

.field public static MenuGroup_android_enabled:I = 0x0

.field public static MenuGroup_android_id:I = 0x1

.field public static MenuGroup_android_menuCategory:I = 0x3

.field public static MenuGroup_android_orderInCategory:I = 0x4

.field public static MenuGroup_android_visible:I = 0x2

.field public static MenuItem:[I = null

.field public static MenuItem_actionLayout:I = 0xd

.field public static MenuItem_actionProviderClass:I = 0xe

.field public static MenuItem_actionViewClass:I = 0xf

.field public static MenuItem_alphabeticModifiers:I = 0x10

.field public static MenuItem_android_alphabeticShortcut:I = 0x9

.field public static MenuItem_android_checkable:I = 0xb

.field public static MenuItem_android_checked:I = 0x3

.field public static MenuItem_android_enabled:I = 0x1

.field public static MenuItem_android_icon:I = 0x0

.field public static MenuItem_android_id:I = 0x2

.field public static MenuItem_android_menuCategory:I = 0x5

.field public static MenuItem_android_numericShortcut:I = 0xa

.field public static MenuItem_android_onClick:I = 0xc

.field public static MenuItem_android_orderInCategory:I = 0x6

.field public static MenuItem_android_title:I = 0x7

.field public static MenuItem_android_titleCondensed:I = 0x8

.field public static MenuItem_android_visible:I = 0x4

.field public static MenuItem_contentDescription:I = 0x11

.field public static MenuItem_iconTint:I = 0x12

.field public static MenuItem_iconTintMode:I = 0x13

.field public static MenuItem_numericModifiers:I = 0x14

.field public static MenuItem_showAsAction:I = 0x15

.field public static MenuItem_tooltipText:I = 0x16

.field public static MenuView:[I = null

.field public static MenuView_android_headerBackground:I = 0x4

.field public static MenuView_android_horizontalDivider:I = 0x2

.field public static MenuView_android_itemBackground:I = 0x5

.field public static MenuView_android_itemIconDisabledAlpha:I = 0x6

.field public static MenuView_android_itemTextAppearance:I = 0x1

.field public static MenuView_android_verticalDivider:I = 0x3

.field public static MenuView_android_windowAnimationStyle:I = 0x0

.field public static MenuView_preserveIconSpacing:I = 0x7

.field public static MenuView_subMenuArrow:I = 0x8

.field public static NavigationView:[I = null

.field public static NavigationView_android_background:I = 0x1

.field public static NavigationView_android_fitsSystemWindows:I = 0x2

.field public static NavigationView_android_layout_gravity:I = 0x0

.field public static NavigationView_android_maxWidth:I = 0x3

.field public static NavigationView_bottomInsetScrimEnabled:I = 0x4

.field public static NavigationView_dividerInsetEnd:I = 0x5

.field public static NavigationView_dividerInsetStart:I = 0x6

.field public static NavigationView_drawerLayoutCornerSize:I = 0x7

.field public static NavigationView_elevation:I = 0x8

.field public static NavigationView_headerLayout:I = 0x9

.field public static NavigationView_itemBackground:I = 0xa

.field public static NavigationView_itemHorizontalPadding:I = 0xb

.field public static NavigationView_itemIconPadding:I = 0xc

.field public static NavigationView_itemIconSize:I = 0xd

.field public static NavigationView_itemIconTint:I = 0xe

.field public static NavigationView_itemMaxLines:I = 0xf

.field public static NavigationView_itemRippleColor:I = 0x10

.field public static NavigationView_itemShapeAppearance:I = 0x11

.field public static NavigationView_itemShapeAppearanceOverlay:I = 0x12

.field public static NavigationView_itemShapeFillColor:I = 0x13

.field public static NavigationView_itemShapeInsetBottom:I = 0x14

.field public static NavigationView_itemShapeInsetEnd:I = 0x15

.field public static NavigationView_itemShapeInsetStart:I = 0x16

.field public static NavigationView_itemShapeInsetTop:I = 0x17

.field public static NavigationView_itemTextAppearance:I = 0x18

.field public static NavigationView_itemTextAppearanceActiveBoldEnabled:I = 0x19

.field public static NavigationView_itemTextColor:I = 0x1a

.field public static NavigationView_itemVerticalPadding:I = 0x1b

.field public static NavigationView_menu:I = 0x1c

.field public static NavigationView_shapeAppearance:I = 0x1d

.field public static NavigationView_shapeAppearanceOverlay:I = 0x1e

.field public static NavigationView_subheaderColor:I = 0x1f

.field public static NavigationView_subheaderInsetEnd:I = 0x20

.field public static NavigationView_subheaderInsetStart:I = 0x21

.field public static NavigationView_subheaderTextAppearance:I = 0x22

.field public static NavigationView_topInsetScrimEnabled:I = 0x23

.field public static PopupWindow:[I = null

.field public static PopupWindowBackgroundState:[I = null

.field public static PopupWindowBackgroundState_state_above_anchor:I = 0x0

.field public static PopupWindow_android_popupAnimationStyle:I = 0x1

.field public static PopupWindow_android_popupBackground:I = 0x0

.field public static PopupWindow_overlapAnchor:I = 0x2

.field public static RecycleListView:[I = null

.field public static RecycleListView_paddingBottomNoButtons:I = 0x0

.field public static RecycleListView_paddingTopNoTitle:I = 0x1

.field public static RecyclerView:[I = null

.field public static RecyclerView_android_clipToPadding:I = 0x1

.field public static RecyclerView_android_descendantFocusability:I = 0x2

.field public static RecyclerView_android_orientation:I = 0x0

.field public static RecyclerView_fastScrollEnabled:I = 0x3

.field public static RecyclerView_fastScrollHorizontalThumbDrawable:I = 0x4

.field public static RecyclerView_fastScrollHorizontalTrackDrawable:I = 0x5

.field public static RecyclerView_fastScrollVerticalThumbDrawable:I = 0x6

.field public static RecyclerView_fastScrollVerticalTrackDrawable:I = 0x7

.field public static RecyclerView_layoutManager:I = 0x8

.field public static RecyclerView_reverseLayout:I = 0x9

.field public static RecyclerView_spanCount:I = 0xa

.field public static RecyclerView_stackFromEnd:I = 0xb

.field public static ScrimInsetsFrameLayout:[I = null

.field public static ScrimInsetsFrameLayout_insetForeground:I = 0x0

.field public static ScrollingViewBehavior_Layout:[I = null

.field public static ScrollingViewBehavior_Layout_behavior_overlapTop:I = 0x0

.field public static SearchView:[I = null

.field public static SearchView_android_focusable:I = 0x1

.field public static SearchView_android_hint:I = 0x4

.field public static SearchView_android_imeOptions:I = 0x6

.field public static SearchView_android_inputType:I = 0x5

.field public static SearchView_android_maxWidth:I = 0x2

.field public static SearchView_android_text:I = 0x3

.field public static SearchView_android_textAppearance:I = 0x0

.field public static SearchView_animateMenuItems:I = 0x7

.field public static SearchView_animateNavigationIcon:I = 0x8

.field public static SearchView_autoShowKeyboard:I = 0x9

.field public static SearchView_backHandlingEnabled:I = 0xa

.field public static SearchView_backgroundTint:I = 0xb

.field public static SearchView_closeIcon:I = 0xc

.field public static SearchView_commitIcon:I = 0xd

.field public static SearchView_defaultQueryHint:I = 0xe

.field public static SearchView_goIcon:I = 0xf

.field public static SearchView_headerLayout:I = 0x10

.field public static SearchView_hideNavigationIcon:I = 0x11

.field public static SearchView_iconifiedByDefault:I = 0x12

.field public static SearchView_layout:I = 0x13

.field public static SearchView_queryBackground:I = 0x14

.field public static SearchView_queryHint:I = 0x15

.field public static SearchView_searchHintIcon:I = 0x16

.field public static SearchView_searchIcon:I = 0x17

.field public static SearchView_searchPrefixText:I = 0x18

.field public static SearchView_submitBackground:I = 0x19

.field public static SearchView_suggestionRowLayout:I = 0x1a

.field public static SearchView_useDrawerArrowDrawable:I = 0x1b

.field public static SearchView_voiceIcon:I = 0x1c

.field public static Snackbar:[I = null

.field public static SnackbarLayout:[I = null

.field public static SnackbarLayout_actionTextColorAlpha:I = 0x1

.field public static SnackbarLayout_android_maxWidth:I = 0x0

.field public static SnackbarLayout_animationMode:I = 0x2

.field public static SnackbarLayout_backgroundOverlayColorAlpha:I = 0x3

.field public static SnackbarLayout_backgroundTint:I = 0x4

.field public static SnackbarLayout_backgroundTintMode:I = 0x5

.field public static SnackbarLayout_elevation:I = 0x6

.field public static SnackbarLayout_maxActionInlineWidth:I = 0x7

.field public static SnackbarLayout_shapeAppearance:I = 0x8

.field public static SnackbarLayout_shapeAppearanceOverlay:I = 0x9

.field public static Snackbar_snackbarButtonStyle:I = 0x0

.field public static Snackbar_snackbarStyle:I = 0x1

.field public static Snackbar_snackbarTextViewStyle:I = 0x2

.field public static Spinner:[I = null

.field public static Spinner_android_dropDownWidth:I = 0x3

.field public static Spinner_android_entries:I = 0x0

.field public static Spinner_android_popupBackground:I = 0x1

.field public static Spinner_android_prompt:I = 0x2

.field public static Spinner_popupTheme:I = 0x4

.field public static StateListDrawable:[I = null

.field public static StateListDrawableItem:[I = null

.field public static StateListDrawableItem_android_drawable:I = 0x0

.field public static StateListDrawable_android_constantSize:I = 0x3

.field public static StateListDrawable_android_dither:I = 0x0

.field public static StateListDrawable_android_enterFadeDuration:I = 0x4

.field public static StateListDrawable_android_exitFadeDuration:I = 0x5

.field public static StateListDrawable_android_variablePadding:I = 0x2

.field public static StateListDrawable_android_visible:I = 0x1

.field public static SwitchCompat:[I = null

.field public static SwitchCompat_android_textOff:I = 0x1

.field public static SwitchCompat_android_textOn:I = 0x0

.field public static SwitchCompat_android_thumb:I = 0x2

.field public static SwitchCompat_showText:I = 0x3

.field public static SwitchCompat_splitTrack:I = 0x4

.field public static SwitchCompat_switchMinWidth:I = 0x5

.field public static SwitchCompat_switchPadding:I = 0x6

.field public static SwitchCompat_switchTextAppearance:I = 0x7

.field public static SwitchCompat_thumbTextPadding:I = 0x8

.field public static SwitchCompat_thumbTint:I = 0x9

.field public static SwitchCompat_thumbTintMode:I = 0xa

.field public static SwitchCompat_track:I = 0xb

.field public static SwitchCompat_trackTint:I = 0xc

.field public static SwitchCompat_trackTintMode:I = 0xd

.field public static TabItem:[I = null

.field public static TabItem_android_icon:I = 0x0

.field public static TabItem_android_layout:I = 0x1

.field public static TabItem_android_text:I = 0x2

.field public static TabLayout:[I = null

.field public static TabLayout_tabBackground:I = 0x0

.field public static TabLayout_tabContentStart:I = 0x1

.field public static TabLayout_tabGravity:I = 0x2

.field public static TabLayout_tabIconTint:I = 0x3

.field public static TabLayout_tabIconTintMode:I = 0x4

.field public static TabLayout_tabIndicator:I = 0x5

.field public static TabLayout_tabIndicatorAnimationDuration:I = 0x6

.field public static TabLayout_tabIndicatorAnimationMode:I = 0x7

.field public static TabLayout_tabIndicatorColor:I = 0x8

.field public static TabLayout_tabIndicatorFullWidth:I = 0x9

.field public static TabLayout_tabIndicatorGravity:I = 0xa

.field public static TabLayout_tabIndicatorHeight:I = 0xb

.field public static TabLayout_tabInlineLabel:I = 0xc

.field public static TabLayout_tabMaxWidth:I = 0xd

.field public static TabLayout_tabMinWidth:I = 0xe

.field public static TabLayout_tabMode:I = 0xf

.field public static TabLayout_tabPadding:I = 0x10

.field public static TabLayout_tabPaddingBottom:I = 0x11

.field public static TabLayout_tabPaddingEnd:I = 0x12

.field public static TabLayout_tabPaddingStart:I = 0x13

.field public static TabLayout_tabPaddingTop:I = 0x14

.field public static TabLayout_tabRippleColor:I = 0x15

.field public static TabLayout_tabSelectedTextAppearance:I = 0x16

.field public static TabLayout_tabSelectedTextColor:I = 0x17

.field public static TabLayout_tabTextAppearance:I = 0x18

.field public static TabLayout_tabTextColor:I = 0x19

.field public static TabLayout_tabUnboundedRipple:I = 0x1a

.field public static TextAppearance:[I = null

.field public static TextAppearance_android_fontFamily:I = 0xa

.field public static TextAppearance_android_shadowColor:I = 0x6

.field public static TextAppearance_android_shadowDx:I = 0x7

.field public static TextAppearance_android_shadowDy:I = 0x8

.field public static TextAppearance_android_shadowRadius:I = 0x9

.field public static TextAppearance_android_textColor:I = 0x3

.field public static TextAppearance_android_textColorHint:I = 0x4

.field public static TextAppearance_android_textColorLink:I = 0x5

.field public static TextAppearance_android_textFontWeight:I = 0xb

.field public static TextAppearance_android_textSize:I = 0x0

.field public static TextAppearance_android_textStyle:I = 0x2

.field public static TextAppearance_android_typeface:I = 0x1

.field public static TextAppearance_fontFamily:I = 0xc

.field public static TextAppearance_fontVariationSettings:I = 0xd

.field public static TextAppearance_textAllCaps:I = 0xe

.field public static TextAppearance_textLocale:I = 0xf

.field public static TextInputLayout:[I = null

.field public static TextInputLayout_android_enabled:I = 0x0

.field public static TextInputLayout_android_hint:I = 0x4

.field public static TextInputLayout_android_maxEms:I = 0x5

.field public static TextInputLayout_android_maxWidth:I = 0x2

.field public static TextInputLayout_android_minEms:I = 0x6

.field public static TextInputLayout_android_minWidth:I = 0x3

.field public static TextInputLayout_android_textColorHint:I = 0x1

.field public static TextInputLayout_boxBackgroundColor:I = 0x7

.field public static TextInputLayout_boxBackgroundMode:I = 0x8

.field public static TextInputLayout_boxCollapsedPaddingTop:I = 0x9

.field public static TextInputLayout_boxCornerRadiusBottomEnd:I = 0xa

.field public static TextInputLayout_boxCornerRadiusBottomStart:I = 0xb

.field public static TextInputLayout_boxCornerRadiusTopEnd:I = 0xc

.field public static TextInputLayout_boxCornerRadiusTopStart:I = 0xd

.field public static TextInputLayout_boxStrokeColor:I = 0xe

.field public static TextInputLayout_boxStrokeErrorColor:I = 0xf

.field public static TextInputLayout_boxStrokeWidth:I = 0x10

.field public static TextInputLayout_boxStrokeWidthFocused:I = 0x11

.field public static TextInputLayout_counterEnabled:I = 0x12

.field public static TextInputLayout_counterMaxLength:I = 0x13

.field public static TextInputLayout_counterOverflowTextAppearance:I = 0x14

.field public static TextInputLayout_counterOverflowTextColor:I = 0x15

.field public static TextInputLayout_counterTextAppearance:I = 0x16

.field public static TextInputLayout_counterTextColor:I = 0x17

.field public static TextInputLayout_cursorColor:I = 0x18

.field public static TextInputLayout_cursorErrorColor:I = 0x19

.field public static TextInputLayout_endIconCheckable:I = 0x1a

.field public static TextInputLayout_endIconContentDescription:I = 0x1b

.field public static TextInputLayout_endIconDrawable:I = 0x1c

.field public static TextInputLayout_endIconMinSize:I = 0x1d

.field public static TextInputLayout_endIconMode:I = 0x1e

.field public static TextInputLayout_endIconScaleType:I = 0x1f

.field public static TextInputLayout_endIconTint:I = 0x20

.field public static TextInputLayout_endIconTintMode:I = 0x21

.field public static TextInputLayout_errorAccessibilityLiveRegion:I = 0x22

.field public static TextInputLayout_errorContentDescription:I = 0x23

.field public static TextInputLayout_errorEnabled:I = 0x24

.field public static TextInputLayout_errorIconDrawable:I = 0x25

.field public static TextInputLayout_errorIconTint:I = 0x26

.field public static TextInputLayout_errorIconTintMode:I = 0x27

.field public static TextInputLayout_errorTextAppearance:I = 0x28

.field public static TextInputLayout_errorTextColor:I = 0x29

.field public static TextInputLayout_expandedHintEnabled:I = 0x2a

.field public static TextInputLayout_helperText:I = 0x2b

.field public static TextInputLayout_helperTextEnabled:I = 0x2c

.field public static TextInputLayout_helperTextTextAppearance:I = 0x2d

.field public static TextInputLayout_helperTextTextColor:I = 0x2e

.field public static TextInputLayout_hintAnimationEnabled:I = 0x2f

.field public static TextInputLayout_hintEnabled:I = 0x30

.field public static TextInputLayout_hintTextAppearance:I = 0x31

.field public static TextInputLayout_hintTextColor:I = 0x32

.field public static TextInputLayout_passwordToggleContentDescription:I = 0x33

.field public static TextInputLayout_passwordToggleDrawable:I = 0x34

.field public static TextInputLayout_passwordToggleEnabled:I = 0x35

.field public static TextInputLayout_passwordToggleTint:I = 0x36

.field public static TextInputLayout_passwordToggleTintMode:I = 0x37

.field public static TextInputLayout_placeholderText:I = 0x38

.field public static TextInputLayout_placeholderTextAppearance:I = 0x39

.field public static TextInputLayout_placeholderTextColor:I = 0x3a

.field public static TextInputLayout_prefixText:I = 0x3b

.field public static TextInputLayout_prefixTextAppearance:I = 0x3c

.field public static TextInputLayout_prefixTextColor:I = 0x3d

.field public static TextInputLayout_shapeAppearance:I = 0x3e

.field public static TextInputLayout_shapeAppearanceOverlay:I = 0x3f

.field public static TextInputLayout_startIconCheckable:I = 0x40

.field public static TextInputLayout_startIconContentDescription:I = 0x41

.field public static TextInputLayout_startIconDrawable:I = 0x42

.field public static TextInputLayout_startIconMinSize:I = 0x43

.field public static TextInputLayout_startIconScaleType:I = 0x44

.field public static TextInputLayout_startIconTint:I = 0x45

.field public static TextInputLayout_startIconTintMode:I = 0x46

.field public static TextInputLayout_suffixText:I = 0x47

.field public static TextInputLayout_suffixTextAppearance:I = 0x48

.field public static TextInputLayout_suffixTextColor:I = 0x49

.field public static ThemeEnforcement:[I = null

.field public static ThemeEnforcement_android_textAppearance:I = 0x0

.field public static ThemeEnforcement_enforceMaterialTheme:I = 0x1

.field public static ThemeEnforcement_enforceTextAppearance:I = 0x2

.field public static Toolbar:[I = null

.field public static Toolbar_android_gravity:I = 0x0

.field public static Toolbar_android_minHeight:I = 0x1

.field public static Toolbar_buttonGravity:I = 0x2

.field public static Toolbar_collapseContentDescription:I = 0x3

.field public static Toolbar_collapseIcon:I = 0x4

.field public static Toolbar_contentInsetEnd:I = 0x5

.field public static Toolbar_contentInsetEndWithActions:I = 0x6

.field public static Toolbar_contentInsetLeft:I = 0x7

.field public static Toolbar_contentInsetRight:I = 0x8

.field public static Toolbar_contentInsetStart:I = 0x9

.field public static Toolbar_contentInsetStartWithNavigation:I = 0xa

.field public static Toolbar_logo:I = 0xb

.field public static Toolbar_logoDescription:I = 0xc

.field public static Toolbar_maxButtonHeight:I = 0xd

.field public static Toolbar_menu:I = 0xe

.field public static Toolbar_navigationContentDescription:I = 0xf

.field public static Toolbar_navigationIcon:I = 0x10

.field public static Toolbar_popupTheme:I = 0x11

.field public static Toolbar_subtitle:I = 0x12

.field public static Toolbar_subtitleTextAppearance:I = 0x13

.field public static Toolbar_subtitleTextColor:I = 0x14

.field public static Toolbar_title:I = 0x15

.field public static Toolbar_titleMargin:I = 0x16

.field public static Toolbar_titleMarginBottom:I = 0x17

.field public static Toolbar_titleMarginEnd:I = 0x18

.field public static Toolbar_titleMarginStart:I = 0x19

.field public static Toolbar_titleMarginTop:I = 0x1a

.field public static Toolbar_titleMargins:I = 0x1b

.field public static Toolbar_titleTextAppearance:I = 0x1c

.field public static Toolbar_titleTextColor:I = 0x1d

.field public static View:[I = null

.field public static ViewBackgroundHelper:[I = null

.field public static ViewBackgroundHelper_android_background:I = 0x0

.field public static ViewBackgroundHelper_backgroundTint:I = 0x1

.field public static ViewBackgroundHelper_backgroundTintMode:I = 0x2

.field public static ViewStubCompat:[I = null

.field public static ViewStubCompat_android_id:I = 0x0

.field public static ViewStubCompat_android_inflatedId:I = 0x2

.field public static ViewStubCompat_android_layout:I = 0x1

.field public static View_android_focusable:I = 0x1

.field public static View_android_theme:I = 0x0

.field public static View_paddingEnd:I = 0x2

.field public static View_paddingStart:I = 0x3

.field public static View_theme:I = 0x4


# direct methods
.method public static constructor <clinit>()V
    .locals 16

    const/16 v0, 0x1d

    new-array v1, v0, [I

    fill-array-data v1, :array_0

    sput-object v1, Lcom/blankj/utilcode/R$styleable;->ActionBar:[I

    const v1, 0x10100b3

    filled-new-array {v1}, [I

    move-result-object v2

    sput-object v2, Lcom/blankj/utilcode/R$styleable;->ActionBarLayout:[I

    const v2, 0x101013f

    filled-new-array {v2}, [I

    move-result-object v2

    sput-object v2, Lcom/blankj/utilcode/R$styleable;->ActionMenuItemView:[I

    const/4 v2, 0x0

    new-array v2, v2, [I

    sput-object v2, Lcom/blankj/utilcode/R$styleable;->ActionMenuView:[I

    const/4 v2, 0x6

    new-array v3, v2, [I

    fill-array-data v3, :array_1

    sput-object v3, Lcom/blankj/utilcode/R$styleable;->ActionMode:[I

    const v3, 0x7f040349

    const v4, 0x7f0403f4

    filled-new-array {v3, v4}, [I

    move-result-object v3

    sput-object v3, Lcom/blankj/utilcode/R$styleable;->ActivityChooserView:[I

    const/16 v3, 0x8

    new-array v4, v3, [I

    fill-array-data v4, :array_2

    sput-object v4, Lcom/blankj/utilcode/R$styleable;->AlertDialog:[I

    new-array v4, v2, [I

    fill-array-data v4, :array_3

    sput-object v4, Lcom/blankj/utilcode/R$styleable;->AnimatedStateListDrawableCompat:[I

    const v4, 0x10100d0

    const v5, 0x1010199

    filled-new-array {v4, v5}, [I

    move-result-object v6

    sput-object v6, Lcom/blankj/utilcode/R$styleable;->AnimatedStateListDrawableItem:[I

    const v6, 0x101044a

    const v7, 0x101044b

    const v8, 0x1010449

    filled-new-array {v5, v8, v6, v7}, [I

    move-result-object v6

    sput-object v6, Lcom/blankj/utilcode/R$styleable;->AnimatedStateListDrawableTransition:[I

    const/16 v6, 0x9

    new-array v7, v6, [I

    fill-array-data v7, :array_4

    sput-object v7, Lcom/blankj/utilcode/R$styleable;->AppBarLayout:[I

    const v7, 0x7f0406b4

    const v8, 0x7f0406b5

    const v9, 0x7f0406af

    const v10, 0x7f0406b0

    filled-new-array {v9, v10, v7, v8}, [I

    move-result-object v7

    sput-object v7, Lcom/blankj/utilcode/R$styleable;->AppBarLayoutStates:[I

    const v7, 0x7f0404b8

    const v8, 0x7f0404b9

    const v9, 0x7f0404b7

    filled-new-array {v9, v7, v8}, [I

    move-result-object v7

    sput-object v7, Lcom/blankj/utilcode/R$styleable;->AppBarLayout_Layout:[I

    const v7, 0x7f04075a

    const v8, 0x7f04075b

    const v9, 0x1010119

    const v10, 0x7f040694

    filled-new-array {v9, v10, v7, v8}, [I

    move-result-object v7

    sput-object v7, Lcom/blankj/utilcode/R$styleable;->AppCompatImageView:[I

    const v7, 0x7f040754

    const v8, 0x7f040755

    const v9, 0x1010142

    const v10, 0x7f040753

    filled-new-array {v9, v10, v7, v8}, [I

    move-result-object v7

    sput-object v7, Lcom/blankj/utilcode/R$styleable;->AppCompatSeekBar:[I

    const/4 v7, 0x7

    new-array v8, v7, [I

    fill-array-data v8, :array_5

    sput-object v8, Lcom/blankj/utilcode/R$styleable;->AppCompatTextHelper:[I

    const/16 v8, 0x16

    new-array v9, v8, [I

    fill-array-data v9, :array_6

    sput-object v9, Lcom/blankj/utilcode/R$styleable;->AppCompatTextView:[I

    const/16 v9, 0x7f

    new-array v9, v9, [I

    fill-array-data v9, :array_7

    sput-object v9, Lcom/blankj/utilcode/R$styleable;->AppCompatTheme:[I

    const/16 v9, 0x11

    new-array v10, v9, [I

    fill-array-data v10, :array_8

    sput-object v10, Lcom/blankj/utilcode/R$styleable;->BottomAppBar:[I

    const v10, 0x7f04063c

    const v11, 0x7f040644

    const v12, 0x1010140

    const v13, 0x7f04029a

    const v14, 0x7f04042b

    filled-new-array {v12, v13, v14, v10, v11}, [I

    move-result-object v10

    sput-object v10, Lcom/blankj/utilcode/R$styleable;->BottomNavigationView:[I

    const/16 v10, 0x18

    new-array v11, v10, [I

    fill-array-data v11, :array_9

    sput-object v11, Lcom/blankj/utilcode/R$styleable;->BottomSheetBehavior_Layout:[I

    const v11, 0x7f04003f

    filled-new-array {v11}, [I

    move-result-object v11

    sput-object v11, Lcom/blankj/utilcode/R$styleable;->ButtonBarLayout:[I

    const/16 v11, 0xd

    new-array v12, v11, [I

    fill-array-data v12, :array_a

    sput-object v12, Lcom/blankj/utilcode/R$styleable;->CardView:[I

    const/16 v12, 0x2a

    new-array v12, v12, [I

    fill-array-data v12, :array_b

    sput-object v12, Lcom/blankj/utilcode/R$styleable;->Chip:[I

    new-array v12, v7, [I

    fill-array-data v12, :array_c

    sput-object v12, Lcom/blankj/utilcode/R$styleable;->ChipGroup:[I

    new-array v10, v10, [I

    fill-array-data v10, :array_d

    sput-object v10, Lcom/blankj/utilcode/R$styleable;->CollapsingToolbarLayout:[I

    const v10, 0x7f04046e

    const v12, 0x7f04046f

    filled-new-array {v10, v12}, [I

    move-result-object v10

    sput-object v10, Lcom/blankj/utilcode/R$styleable;->CollapsingToolbarLayout_Layout:[I

    const v10, 0x7f040040

    const v12, 0x7f04045a

    const v13, 0x10101a5

    const v14, 0x101031f

    const v15, 0x1010647

    filled-new-array {v13, v14, v15, v10, v12}, [I

    move-result-object v10

    sput-object v10, Lcom/blankj/utilcode/R$styleable;->ColorStateListItem:[I

    const v10, 0x7f0401ed

    const v12, 0x7f0401ee

    const v14, 0x1010107

    const v15, 0x7f0401e3

    filled-new-array {v14, v15, v10, v12}, [I

    move-result-object v10

    sput-object v10, Lcom/blankj/utilcode/R$styleable;->CompoundButton:[I

    const v10, 0x7f040459

    const v12, 0x7f0406b7

    filled-new-array {v10, v12}, [I

    move-result-object v10

    sput-object v10, Lcom/blankj/utilcode/R$styleable;->CoordinatorLayout:[I

    new-array v10, v7, [I

    fill-array-data v10, :array_e

    sput-object v10, Lcom/blankj/utilcode/R$styleable;->CoordinatorLayout_Layout:[I

    new-array v3, v3, [I

    fill-array-data v3, :array_f

    sput-object v3, Lcom/blankj/utilcode/R$styleable;->DrawerArrowToggle:[I

    new-array v3, v9, [I

    fill-array-data v3, :array_10

    sput-object v3, Lcom/blankj/utilcode/R$styleable;->FloatingActionButton:[I

    const v3, 0x7f0400a8

    filled-new-array {v3}, [I

    move-result-object v3

    sput-object v3, Lcom/blankj/utilcode/R$styleable;->FloatingActionButton_Behavior_Layout:[I

    const/16 v3, 0xb

    new-array v3, v3, [I

    fill-array-data v3, :array_11

    sput-object v3, Lcom/blankj/utilcode/R$styleable;->FlowLayout:[I

    new-array v3, v7, [I

    fill-array-data v3, :array_12

    sput-object v3, Lcom/blankj/utilcode/R$styleable;->FontFamily:[I

    const/16 v3, 0xa

    new-array v7, v3, [I

    fill-array-data v7, :array_13

    sput-object v7, Lcom/blankj/utilcode/R$styleable;->FontFamilyFont:[I

    const v7, 0x1010200

    const v9, 0x7f0403a7

    const v10, 0x1010109

    filled-new-array {v10, v7, v9}, [I

    move-result-object v7

    sput-object v7, Lcom/blankj/utilcode/R$styleable;->ForegroundLinearLayout:[I

    const/16 v7, 0xc

    new-array v9, v7, [I

    fill-array-data v9, :array_14

    sput-object v9, Lcom/blankj/utilcode/R$styleable;->GradientColor:[I

    const v9, 0x1010514

    filled-new-array {v13, v9}, [I

    move-result-object v9

    sput-object v9, Lcom/blankj/utilcode/R$styleable;->GradientColorItem:[I

    new-array v9, v6, [I

    fill-array-data v9, :array_15

    sput-object v9, Lcom/blankj/utilcode/R$styleable;->LinearLayoutCompat:[I

    const v9, 0x10100f5

    const v10, 0x1010181

    const v12, 0x10100f4

    filled-new-array {v1, v12, v9, v10}, [I

    move-result-object v1

    sput-object v1, Lcom/blankj/utilcode/R$styleable;->LinearLayoutCompat_Layout:[I

    const v1, 0x10102ac

    const v9, 0x10102ad

    filled-new-array {v1, v9}, [I

    move-result-object v1

    sput-object v1, Lcom/blankj/utilcode/R$styleable;->ListPopupWindow:[I

    new-array v1, v8, [I

    fill-array-data v1, :array_16

    sput-object v1, Lcom/blankj/utilcode/R$styleable;->MaterialButton:[I

    new-array v1, v11, [I

    fill-array-data v1, :array_17

    sput-object v1, Lcom/blankj/utilcode/R$styleable;->MaterialCardView:[I

    new-array v1, v2, [I

    fill-array-data v1, :array_18

    sput-object v1, Lcom/blankj/utilcode/R$styleable;->MenuGroup:[I

    const/16 v1, 0x17

    new-array v1, v1, [I

    fill-array-data v1, :array_19

    sput-object v1, Lcom/blankj/utilcode/R$styleable;->MenuItem:[I

    new-array v1, v6, [I

    fill-array-data v1, :array_1a

    sput-object v1, Lcom/blankj/utilcode/R$styleable;->MenuView:[I

    const/16 v1, 0x24

    new-array v1, v1, [I

    fill-array-data v1, :array_1b

    sput-object v1, Lcom/blankj/utilcode/R$styleable;->NavigationView:[I

    const v1, 0x10102c9

    const v6, 0x7f040594

    const v8, 0x1010176

    filled-new-array {v8, v1, v6}, [I

    move-result-object v1

    sput-object v1, Lcom/blankj/utilcode/R$styleable;->PopupWindow:[I

    const v1, 0x7f0406ae

    filled-new-array {v1}, [I

    move-result-object v1

    sput-object v1, Lcom/blankj/utilcode/R$styleable;->PopupWindowBackgroundState:[I

    const v1, 0x7f040596

    const v6, 0x7f04059d

    filled-new-array {v1, v6}, [I

    move-result-object v1

    sput-object v1, Lcom/blankj/utilcode/R$styleable;->RecycleListView:[I

    new-array v1, v7, [I

    fill-array-data v1, :array_1c

    sput-object v1, Lcom/blankj/utilcode/R$styleable;->RecyclerView:[I

    const v1, 0x7f0403f6

    filled-new-array {v1}, [I

    move-result-object v1

    sput-object v1, Lcom/blankj/utilcode/R$styleable;->ScrimInsetsFrameLayout:[I

    const v1, 0x7f0400af

    filled-new-array {v1}, [I

    move-result-object v1

    sput-object v1, Lcom/blankj/utilcode/R$styleable;->ScrollingViewBehavior_Layout:[I

    new-array v0, v0, [I

    fill-array-data v0, :array_1d

    sput-object v0, Lcom/blankj/utilcode/R$styleable;->SearchView:[I

    const v0, 0x7f040683

    const v1, 0x7f040684

    const v6, 0x7f040682

    filled-new-array {v6, v0, v1}, [I

    move-result-object v0

    sput-object v0, Lcom/blankj/utilcode/R$styleable;->Snackbar:[I

    new-array v0, v3, [I

    fill-array-data v0, :array_1e

    sput-object v0, Lcom/blankj/utilcode/R$styleable;->SnackbarLayout:[I

    const v0, 0x1010262

    const v1, 0x7f0405d3

    const v3, 0x10100b2

    const v6, 0x101017b

    filled-new-array {v3, v8, v6, v0, v1}, [I

    move-result-object v0

    sput-object v0, Lcom/blankj/utilcode/R$styleable;->Spinner:[I

    new-array v0, v2, [I

    fill-array-data v0, :array_1f

    sput-object v0, Lcom/blankj/utilcode/R$styleable;->StateListDrawable:[I

    filled-new-array {v5}, [I

    move-result-object v0

    sput-object v0, Lcom/blankj/utilcode/R$styleable;->StateListDrawableItem:[I

    const/16 v0, 0xe

    new-array v0, v0, [I

    fill-array-data v0, :array_20

    sput-object v0, Lcom/blankj/utilcode/R$styleable;->SwitchCompat:[I

    const v0, 0x101014f

    const v1, 0x1010002

    const v2, 0x10100f2

    filled-new-array {v1, v2, v0}, [I

    move-result-object v0

    sput-object v0, Lcom/blankj/utilcode/R$styleable;->TabItem:[I

    const/16 v0, 0x1b

    new-array v0, v0, [I

    fill-array-data v0, :array_21

    sput-object v0, Lcom/blankj/utilcode/R$styleable;->TabLayout:[I

    const/16 v0, 0x10

    new-array v0, v0, [I

    fill-array-data v0, :array_22

    sput-object v0, Lcom/blankj/utilcode/R$styleable;->TextAppearance:[I

    const/16 v0, 0x4a

    new-array v0, v0, [I

    fill-array-data v0, :array_23

    sput-object v0, Lcom/blankj/utilcode/R$styleable;->TextInputLayout:[I

    const v0, 0x7f040338

    const v1, 0x7f040339

    const v3, 0x1010034

    filled-new-array {v3, v0, v1}, [I

    move-result-object v0

    sput-object v0, Lcom/blankj/utilcode/R$styleable;->ThemeEnforcement:[I

    const/16 v0, 0x1e

    new-array v0, v0, [I

    fill-array-data v0, :array_24

    sput-object v0, Lcom/blankj/utilcode/R$styleable;->Toolbar:[I

    const v0, 0x7f04059b

    const v1, 0x7f04073f

    const/high16 v3, 0x1010000

    const v5, 0x10100da

    const v6, 0x7f040598

    filled-new-array {v3, v5, v6, v0, v1}, [I

    move-result-object v0

    sput-object v0, Lcom/blankj/utilcode/R$styleable;->View:[I

    const v0, 0x7f040073

    const v1, 0x7f040074

    const v3, 0x10100d4

    filled-new-array {v3, v0, v1}, [I

    move-result-object v0

    sput-object v0, Lcom/blankj/utilcode/R$styleable;->ViewBackgroundHelper:[I

    const v0, 0x10100f3

    filled-new-array {v4, v2, v0}, [I

    move-result-object v0

    sput-object v0, Lcom/blankj/utilcode/R$styleable;->ViewStubCompat:[I

    return-void

    nop

    :array_0
    .array-data 4
        0x7f04006a
        0x7f040071
        0x7f040072
        0x7f0402a4
        0x7f0402a5
        0x7f0402a6
        0x7f0402a7
        0x7f0402a8
        0x7f0402a9
        0x7f0402d9
        0x7f0402f4
        0x7f0402f5
        0x7f040318
        0x7f0403bb
        0x7f0403c3
        0x7f0403cb
        0x7f0403cc
        0x7f0403d0
        0x7f0403e4
        0x7f040431
        0x7f0404d6
        0x7f04057d
        0x7f0405d3
        0x7f0405dd
        0x7f0405de
        0x7f0406c5
        0x7f0406c9
        0x7f040760
        0x7f040772
    .end array-data

    :array_1
    .array-data 4
        0x7f04006a
        0x7f040071
        0x7f04024f
        0x7f0403bb
        0x7f0406c9
        0x7f040772
    .end array-data

    :array_2
    .array-data 4
        0x10100f2
        0x7f0401e6
        0x7f0401e9
        0x7f0404ca
        0x7f0404cb
        0x7f040578
        0x7f04066b
        0x7f04067d
    .end array-data

    :array_3
    .array-data 4
        0x101011c
        0x1010194
        0x1010195
        0x1010196
        0x101030c
        0x101030d
    .end array-data

    :array_4
    .array-data 4
        0x10100d4
        0x101048f
        0x1010540
        0x7f040318
        0x7f04034a
        0x7f0404bd
        0x7f0404be
        0x7f0404bf
        0x7f0406b8
    .end array-data

    :array_5
    .array-data 4
        0x1010034
        0x101016d
        0x101016e
        0x101016f
        0x1010170
        0x1010392
        0x1010393
    .end array-data

    :array_6
    .array-data 4
        0x1010034
        0x7f040061
        0x7f040062
        0x7f040063
        0x7f040064
        0x7f040065
        0x7f040304
        0x7f040305
        0x7f040306
        0x7f040307
        0x7f040309
        0x7f04030a
        0x7f04030b
        0x7f04030c
        0x7f04031c
        0x7f04036c
        0x7f04039a
        0x7f0403a3
        0x7f04045f
        0x7f0404c3
        0x7f0406fd
        0x7f040734
    .end array-data

    :array_7
    .array-data 4
        0x1010057
        0x10100ae
        0x7f040008
        0x7f040009
        0x7f04000a
        0x7f04000b
        0x7f04000c
        0x7f04000d
        0x7f04000e
        0x7f04000f
        0x7f040010
        0x7f040011
        0x7f040012
        0x7f040013
        0x7f040014
        0x7f040016
        0x7f040017
        0x7f040018
        0x7f040019
        0x7f04001a
        0x7f04001b
        0x7f04001c
        0x7f04001d
        0x7f04001e
        0x7f04001f
        0x7f040020
        0x7f040021
        0x7f040022
        0x7f040023
        0x7f040024
        0x7f040025
        0x7f040026
        0x7f040027
        0x7f040028
        0x7f04002e
        0x7f040039
        0x7f04003a
        0x7f04003b
        0x7f04003c
        0x7f04005f
        0x7f0401be
        0x7f0401de
        0x7f0401df
        0x7f0401e0
        0x7f0401e1
        0x7f0401e2
        0x7f0401eb
        0x7f0401ec
        0x7f04020e
        0x7f040219
        0x7f04025c
        0x7f04025d
        0x7f04025e
        0x7f040260
        0x7f040261
        0x7f040262
        0x7f040263
        0x7f04027c
        0x7f04027e
        0x7f040294
        0x7f0402b3
        0x7f0402f1
        0x7f0402f2
        0x7f0402f3
        0x7f0402fa
        0x7f0402ff
        0x7f040311
        0x7f040312
        0x7f040315
        0x7f040316
        0x7f040317
        0x7f0403cb
        0x7f0403de
        0x7f0404c6
        0x7f0404c7
        0x7f0404c8
        0x7f0404c9
        0x7f0404cc
        0x7f0404cd
        0x7f0404ce
        0x7f0404cf
        0x7f0404d0
        0x7f0404d1
        0x7f0404d2
        0x7f0404d3
        0x7f0404d4
        0x7f04059f
        0x7f0405a0
        0x7f0405a1
        0x7f0405d2
        0x7f0405d4
        0x7f0405ee
        0x7f0405f1
        0x7f0405f2
        0x7f0405f3
        0x7f04062e
        0x7f040631
        0x7f040632
        0x7f040633
        0x7f040687
        0x7f040688
        0x7f0406d2
        0x7f040714
        0x7f040716
        0x7f040717
        0x7f040718
        0x7f04071a
        0x7f04071b
        0x7f04071c
        0x7f04071d
        0x7f040728
        0x7f040729
        0x7f040776
        0x7f040777
        0x7f040779
        0x7f04077a
        0x7f0407b9
        0x7f0407cd
        0x7f0407ce
        0x7f0407cf
        0x7f0407d0
        0x7f0407d1
        0x7f0407d2
        0x7f0407d3
        0x7f0407d4
        0x7f0407d5
        0x7f0407d6
    .end array-data

    :array_8
    .array-data 4
        0x7f040035
        0x7f040073
        0x7f040318
        0x7f04035c
        0x7f04035d
        0x7f04035e
        0x7f04035f
        0x7f040360
        0x7f040361
        0x7f040362
        0x7f0403c4
        0x7f04053a
        0x7f04057c
        0x7f040597
        0x7f040599
        0x7f04059a
        0x7f040607
    .end array-data

    :array_9
    .array-data 4
        0x101011f
        0x1010120
        0x1010440
        0x7f040073
        0x7f0400aa
        0x7f0400ab
        0x7f0400ac
        0x7f0400ad
        0x7f0400ae
        0x7f0400b0
        0x7f0400b1
        0x7f0400b2
        0x7f0400b3
        0x7f0403ab
        0x7f0404f3
        0x7f0404f4
        0x7f0404f5
        0x7f040597
        0x7f040599
        0x7f04059a
        0x7f04059e
        0x7f04063c
        0x7f040644
        0x7f04065c
    .end array-data

    :array_a
    .array-data 4
        0x101013f
        0x1010140
        0x7f0401f6
        0x7f0401f7
        0x7f0401f8
        0x7f0401fa
        0x7f0401fb
        0x7f0401fc
        0x7f0402aa
        0x7f0402ab
        0x7f0402ad
        0x7f0402ae
        0x7f0402b0
    .end array-data

    :array_b
    .array-data 4
        0x1010034
        0x1010095
        0x1010098
        0x10100ab
        0x101011f
        0x101014f
        0x10101e5
        0x7f040211
        0x7f040212
        0x7f040216
        0x7f040217
        0x7f04021a
        0x7f04021b
        0x7f04021c
        0x7f04021e
        0x7f04021f
        0x7f040220
        0x7f040221
        0x7f040222
        0x7f040223
        0x7f040224
        0x7f040229
        0x7f04022a
        0x7f04022b
        0x7f04022d
        0x7f040248
        0x7f040249
        0x7f04024a
        0x7f04024b
        0x7f04024c
        0x7f04024d
        0x7f04024e
        0x7f04033a
        0x7f0403c1
        0x7f0403d1
        0x7f0403d6
        0x7f040615
        0x7f04063c
        0x7f040644
        0x7f040667
        0x7f04072a
        0x7f040739
    .end array-data

    :array_c
    .array-data 4
        0x7f040210
        0x7f040225
        0x7f040226
        0x7f040227
        0x7f040635
        0x7f04067e
        0x7f04067f
    .end array-data

    :array_d
    .array-data 4
        0x7f040253
        0x7f040254
        0x7f040255
        0x7f0402b1
        0x7f04034c
        0x7f04034d
        0x7f04034e
        0x7f04034f
        0x7f040350
        0x7f040351
        0x7f040352
        0x7f040353
        0x7f04035b
        0x7f0403a5
        0x7f04052d
        0x7f040622
        0x7f040624
        0x7f0406b9
        0x7f040760
        0x7f040763
        0x7f040764
        0x7f04076c
        0x7f040770
        0x7f040775
    .end array-data

    :array_e
    .array-data 4
        0x10100b3
        0x7f04046b
        0x7f04046c
        0x7f04046d
        0x7f04049e
        0x7f0404ad
        0x7f0404ae
    .end array-data

    :array_f
    .array-data 4
        0x7f040051
        0x7f040058
        0x7f04009d
        0x7f04025b
        0x7f040308
        0x7f0403aa
        0x7f040686
        0x7f040740
    .end array-data

    :array_10
    .array-data 4
        0x101000e
        0x7f040073
        0x7f040074
        0x7f0401ba
        0x7f040318
        0x7f04033a
        0x7f040363
        0x7f040364
        0x7f0403c1
        0x7f0403cf
        0x7f04052b
        0x7f0405da
        0x7f040615
        0x7f04063c
        0x7f040644
        0x7f040667
        0x7f0407af
    .end array-data

    :array_11
    .array-data 4
        0x10100af
        0x7f04036d
        0x7f04036e
        0x7f04036f
        0x7f040370
        0x7f040371
        0x7f040372
        0x7f040373
        0x7f040374
        0x7f04043c
        0x7f0404c4
    .end array-data

    :array_12
    .array-data 4
        0x7f04039b
        0x7f04039c
        0x7f04039d
        0x7f04039e
        0x7f04039f
        0x7f0403a0
        0x7f0403a1
    .end array-data

    :array_13
    .array-data 4
        0x1010532
        0x1010533
        0x101053f
        0x101056f
        0x1010570
        0x7f040399
        0x7f0403a2
        0x7f0403a3
        0x7f0403a4
        0x7f0407a3
    .end array-data

    :array_14
    .array-data 4
        0x101019d
        0x101019e
        0x10101a1
        0x10101a2
        0x10101a3
        0x10101a4
        0x1010201
        0x101020b
        0x1010510
        0x1010511
        0x1010512
        0x1010513
    .end array-data

    :array_15
    .array-data 4
        0x10100af
        0x10100c4
        0x1010126
        0x1010127
        0x1010128
        0x7f0402f5
        0x7f0402fd
        0x7f040538
        0x7f040663
    .end array-data

    :array_16
    .array-data 4
        0x10100d4
        0x10101b7
        0x10101b8
        0x10101b9
        0x10101ba
        0x10101e5
        0x7f040073
        0x7f040074
        0x7f0402be
        0x7f040318
        0x7f0403d0
        0x7f0403d2
        0x7f0403d3
        0x7f0403d4
        0x7f0403d7
        0x7f0403d8
        0x7f040615
        0x7f04063c
        0x7f040644
        0x7f0406bc
        0x7f0406bd
        0x7f040774
    .end array-data

    :array_17
    .array-data 4
        0x10101e5
        0x7f0401f9
        0x7f040211
        0x7f040213
        0x7f040214
        0x7f040215
        0x7f040216
        0x7f040615
        0x7f04063c
        0x7f040644
        0x7f0406b1
        0x7f0406bc
        0x7f0406bd
    .end array-data

    :array_18
    .array-data 4
        0x101000e
        0x10100d0
        0x1010194
        0x10101de
        0x10101df
        0x10101e0
    .end array-data

    :array_19
    .array-data 4
        0x1010002
        0x101000e
        0x10100d0
        0x1010106
        0x1010194
        0x10101de
        0x10101df
        0x10101e1
        0x10101e2
        0x10101e3
        0x10101e4
        0x10101e5
        0x101026f
        0x7f040015
        0x7f040029
        0x7f04002b
        0x7f040041
        0x7f0402a3
        0x7f0403d7
        0x7f0403d8
        0x7f04058b
        0x7f04065e
        0x7f04077c
    .end array-data

    :array_1a
    .array-data 4
        0x10100ae
        0x101012c
        0x101012d
        0x101012e
        0x101012f
        0x1010130
        0x1010131
        0x7f0405d9
        0x7f0406bf
    .end array-data

    :array_1b
    .array-data 4
        0x10100b3
        0x10100d4
        0x10100dd
        0x101011f
        0x7f0401c0
        0x7f0402fb
        0x7f0402fc
        0x7f04030e
        0x7f040318
        0x7f0403ba
        0x7f040428
        0x7f04042a
        0x7f04042c
        0x7f04042d
        0x7f04042e
        0x7f04042f
        0x7f040434
        0x7f040435
        0x7f040436
        0x7f040437
        0x7f040438
        0x7f040439
        0x7f04043a
        0x7f04043b
        0x7f04043f
        0x7f040441
        0x7f040443
        0x7f040444
        0x7f040539
        0x7f04063c
        0x7f040644
        0x7f0406c0
        0x7f0406c1
        0x7f0406c2
        0x7f0406c3
        0x7f04077d
    .end array-data

    :array_1c
    .array-data 4
        0x10100c4
        0x10100eb
        0x10100f1
        0x7f040365
        0x7f040366
        0x7f040367
        0x7f040368
        0x7f040369
        0x7f040469
        0x7f04060c
        0x7f040685
        0x7f0406a2
    .end array-data

    :array_1d
    .array-data 4
        0x1010034
        0x10100da
        0x101011f
        0x101014f
        0x1010150
        0x1010220
        0x1010264
        0x7f040047
        0x7f040048
        0x7f040060
        0x7f040068
        0x7f040073
        0x7f040248
        0x7f040299
        0x7f0402e6
        0x7f0403ad
        0x7f0403ba
        0x7f0403c2
        0x7f0403d9
        0x7f040466
        0x7f0405ea
        0x7f0405eb
        0x7f04062b
        0x7f04062c
        0x7f04062d
        0x7f0406c4
        0x7f0406cd
        0x7f0407b0
        0x7f0407bf
    .end array-data

    :array_1e
    .array-data 4
        0x101011f
        0x7f04002a
        0x7f04004a
        0x7f040070
        0x7f040073
        0x7f040074
        0x7f040318
        0x7f040527
        0x7f04063c
        0x7f040644
    .end array-data

    :array_1f
    .array-data 4
        0x101011c
        0x1010194
        0x1010195
        0x1010196
        0x101030c
        0x101030d
    .end array-data

    :array_20
    .array-data 4
        0x1010124
        0x1010125
        0x1010142
        0x7f04066a
        0x7f04068e
        0x7f0406d0
        0x7f0406d1
        0x7f0406d3
        0x7f04074b
        0x7f04074c
        0x7f04074d
        0x7f04078c
        0x7f040798
        0x7f040799
    .end array-data

    :array_21
    .array-data 4
        0x7f0406da
        0x7f0406db
        0x7f0406dc
        0x7f0406dd
        0x7f0406de
        0x7f0406df
        0x7f0406e0
        0x7f0406e1
        0x7f0406e2
        0x7f0406e3
        0x7f0406e4
        0x7f0406e5
        0x7f0406e6
        0x7f0406e7
        0x7f0406e8
        0x7f0406e9
        0x7f0406ea
        0x7f0406eb
        0x7f0406ec
        0x7f0406ed
        0x7f0406ee
        0x7f0406ef
        0x7f0406f1
        0x7f0406f2
        0x7f0406f4
        0x7f0406f5
        0x7f0406f6
    .end array-data

    :array_22
    .array-data 4
        0x1010095
        0x1010096
        0x1010097
        0x1010098
        0x101009a
        0x101009b
        0x1010161
        0x1010162
        0x1010163
        0x1010164
        0x10103ac
        0x1010585
        0x7f04039a
        0x7f0403a3
        0x7f0406fd
        0x7f040734
    .end array-data

    :array_23
    .array-data 4
        0x101000e
        0x101009a
        0x101011f
        0x101013f
        0x1010150
        0x1010157
        0x101015a
        0x7f0401cf
        0x7f0401d0
        0x7f0401d1
        0x7f0401d2
        0x7f0401d3
        0x7f0401d4
        0x7f0401d5
        0x7f0401d6
        0x7f0401d7
        0x7f0401d8
        0x7f0401d9
        0x7f0402c8
        0x7f0402c9
        0x7f0402ca
        0x7f0402cb
        0x7f0402cc
        0x7f0402cd
        0x7f0402d0
        0x7f0402d1
        0x7f040330
        0x7f040331
        0x7f040332
        0x7f040333
        0x7f040334
        0x7f040335
        0x7f040336
        0x7f040337
        0x7f04033d
        0x7f04033e
        0x7f04033f
        0x7f040340
        0x7f040341
        0x7f040342
        0x7f040344
        0x7f040345
        0x7f04034b
        0x7f0403bc
        0x7f0403bd
        0x7f0403be
        0x7f0403bf
        0x7f0403c7
        0x7f0403c8
        0x7f0403c9
        0x7f0403ca
        0x7f0405a2
        0x7f0405a3
        0x7f0405a4
        0x7f0405a5
        0x7f0405a6
        0x7f0405b2
        0x7f0405b3
        0x7f0405b4
        0x7f0405d6
        0x7f0405d7
        0x7f0405d8
        0x7f04063c
        0x7f040644
        0x7f0406a7
        0x7f0406a8
        0x7f0406a9
        0x7f0406aa
        0x7f0406ab
        0x7f0406ac
        0x7f0406ad
        0x7f0406ca
        0x7f0406cb
        0x7f0406cc
    .end array-data

    :array_24
    .array-data 4
        0x10100af
        0x1010140
        0x7f0401e4
        0x7f040250
        0x7f040251
        0x7f0402a4
        0x7f0402a5
        0x7f0402a6
        0x7f0402a7
        0x7f0402a8
        0x7f0402a9
        0x7f0404d6
        0x7f0404d8
        0x7f040528
        0x7f040539
        0x7f04057a
        0x7f04057b
        0x7f0405d3
        0x7f0406c5
        0x7f0406c7
        0x7f0406c8
        0x7f040760
        0x7f040766
        0x7f040767
        0x7f040768
        0x7f040769
        0x7f04076a
        0x7f04076b
        0x7f04076e
        0x7f04076f
    .end array-data
.end method

.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
