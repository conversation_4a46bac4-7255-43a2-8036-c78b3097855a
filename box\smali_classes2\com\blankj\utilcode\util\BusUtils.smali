.class public final Lcom/blankj/utilcode/util/BusUtils;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/blankj/utilcode/util/BusUtils$ThreadMode;,
        Lcom/blankj/utilcode/util/BusUtils$a;
    }
.end annotation


# static fields
.field public static final a:Ljava/lang/Object;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    const-string v0, "nULl"

    sput-object v0, Lcom/blankj/utilcode/util/BusUtils;->a:Ljava/lang/Object;

    return-void
.end method

.method public static synthetic a(Lcom/blankj/utilcode/util/BusUtils;Ljava/lang/Object;Ljava/lang/Object;Lcom/blankj/utilcode/util/BusUtils$a;Z)V
    .locals 0

    const/4 p0, 0x0

    throw p0
.end method
