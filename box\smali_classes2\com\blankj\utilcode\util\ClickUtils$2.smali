.class Lcom/blankj/utilcode/util/ClickUtils$2;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/lang/Runnable;


# instance fields
.field final synthetic val$expandSizeBottom:I

.field final synthetic val$expandSizeLeft:I

.field final synthetic val$expandSizeRight:I

.field final synthetic val$expandSizeTop:I

.field final synthetic val$parentView:Landroid/view/View;

.field final synthetic val$view:Landroid/view/View;


# direct methods
.method public constructor <init>(Landroid/view/View;IIIILandroid/view/View;)V
    .locals 0

    iput-object p1, p0, Lcom/blankj/utilcode/util/ClickUtils$2;->val$view:Landroid/view/View;

    iput p2, p0, Lcom/blankj/utilcode/util/ClickUtils$2;->val$expandSizeTop:I

    iput p3, p0, Lcom/blankj/utilcode/util/ClickUtils$2;->val$expandSizeBottom:I

    iput p4, p0, Lcom/blankj/utilcode/util/ClickUtils$2;->val$expandSizeLeft:I

    iput p5, p0, Lcom/blankj/utilcode/util/ClickUtils$2;->val$expandSizeRight:I

    iput-object p6, p0, Lcom/blankj/utilcode/util/ClickUtils$2;->val$parentView:Landroid/view/View;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 4

    new-instance v0, Landroid/graphics/Rect;

    invoke-direct {v0}, Landroid/graphics/Rect;-><init>()V

    iget-object v1, p0, Lcom/blankj/utilcode/util/ClickUtils$2;->val$view:Landroid/view/View;

    invoke-virtual {v1, v0}, Landroid/view/View;->getHitRect(Landroid/graphics/Rect;)V

    iget v1, v0, Landroid/graphics/Rect;->top:I

    iget v2, p0, Lcom/blankj/utilcode/util/ClickUtils$2;->val$expandSizeTop:I

    sub-int/2addr v1, v2

    iput v1, v0, Landroid/graphics/Rect;->top:I

    iget v1, v0, Landroid/graphics/Rect;->bottom:I

    iget v2, p0, Lcom/blankj/utilcode/util/ClickUtils$2;->val$expandSizeBottom:I

    add-int/2addr v1, v2

    iput v1, v0, Landroid/graphics/Rect;->bottom:I

    iget v1, v0, Landroid/graphics/Rect;->left:I

    iget v2, p0, Lcom/blankj/utilcode/util/ClickUtils$2;->val$expandSizeLeft:I

    sub-int/2addr v1, v2

    iput v1, v0, Landroid/graphics/Rect;->left:I

    iget v1, v0, Landroid/graphics/Rect;->right:I

    iget v2, p0, Lcom/blankj/utilcode/util/ClickUtils$2;->val$expandSizeRight:I

    add-int/2addr v1, v2

    iput v1, v0, Landroid/graphics/Rect;->right:I

    iget-object v1, p0, Lcom/blankj/utilcode/util/ClickUtils$2;->val$parentView:Landroid/view/View;

    new-instance v2, Landroid/view/TouchDelegate;

    iget-object v3, p0, Lcom/blankj/utilcode/util/ClickUtils$2;->val$view:Landroid/view/View;

    invoke-direct {v2, v0, v3}, Landroid/view/TouchDelegate;-><init>(Landroid/graphics/Rect;Landroid/view/View;)V

    invoke-virtual {v1, v2}, Landroid/view/View;->setTouchDelegate(Landroid/view/TouchDelegate;)V

    return-void
.end method
