.class public interface abstract Lcom/blankj/utilcode/util/NetworkUtils$a;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/blankj/utilcode/util/NetworkUtils;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "a"
.end annotation


# virtual methods
.method public abstract i(Lcom/blankj/utilcode/util/NetworkUtils$NetworkType;)V
.end method

.method public abstract onDisconnected()V
.end method
