.class public final Lcom/blankj/utilcode/util/ReflectUtils;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/blankj/utilcode/util/ReflectUtils$ReflectException;
    }
.end annotation


# direct methods
.method public static synthetic a(Lcom/blankj/utilcode/util/ReflectUtils;Ljava/lang/Class;)Ljava/lang/Class;
    .locals 0

    const/4 p0, 0x0

    throw p0
.end method
