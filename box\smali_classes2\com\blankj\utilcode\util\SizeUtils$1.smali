.class Lcom/blankj/utilcode/util/SizeUtils$1;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/lang/Runnable;


# instance fields
.field final synthetic val$listener:Lcom/blankj/utilcode/util/f0$a;

.field final synthetic val$view:Landroid/view/View;


# direct methods
.method public constructor <init>(Lcom/blankj/utilcode/util/f0$a;Landroid/view/View;)V
    .locals 0

    iput-object p2, p0, Lcom/blankj/utilcode/util/SizeUtils$1;->val$view:Landroid/view/View;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 0

    return-void
.end method
