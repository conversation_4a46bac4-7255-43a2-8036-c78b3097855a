.class public interface abstract Lcom/blankj/utilcode/util/ToastUtils$b;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/blankj/utilcode/util/ToastUtils;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "b"
.end annotation


# virtual methods
.method public abstract a(I)V
.end method

.method public abstract b(Landroid/view/View;)V
.end method

.method public abstract c(Ljava/lang/CharSequence;)V
.end method

.method public abstract cancel()V
.end method
