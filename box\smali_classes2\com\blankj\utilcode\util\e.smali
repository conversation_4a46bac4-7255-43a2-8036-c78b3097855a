.class public final Lcom/blankj/utilcode/util/e;
.super Ljava/lang/Object;


# direct methods
.method public static synthetic a(Lcom/blankj/utilcode/util/e;)Ljava/util/Map;
    .locals 0

    const/4 p0, 0x0

    throw p0
.end method

.method public static synthetic b(Lcom/blankj/utilcode/util/e;)Ljava/util/concurrent/atomic/AtomicLong;
    .locals 0

    const/4 p0, 0x0

    throw p0
.end method

.method public static synthetic c(Lcom/blankj/utilcode/util/e;)Ljava/util/concurrent/atomic/AtomicInteger;
    .locals 0

    const/4 p0, 0x0

    throw p0
.end method
