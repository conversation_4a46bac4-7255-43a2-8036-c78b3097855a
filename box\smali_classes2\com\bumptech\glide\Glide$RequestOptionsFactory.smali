.class public interface abstract Lcom/bumptech/glide/Glide$RequestOptionsFactory;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bumptech/glide/Glide;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "RequestOptionsFactory"
.end annotation


# virtual methods
.method public abstract build()Lcom/bumptech/glide/request/RequestOptions;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end method
