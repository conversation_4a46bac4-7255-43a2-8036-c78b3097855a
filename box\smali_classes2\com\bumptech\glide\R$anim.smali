.class public final Lcom/bumptech/glide/R$anim;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bumptech/glide/R;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "anim"
.end annotation


# static fields
.field public static abc_fade_in:I = 0x7f010000

.field public static abc_fade_out:I = 0x7f010001

.field public static abc_grow_fade_in_from_bottom:I = 0x7f010002

.field public static abc_popup_enter:I = 0x7f010003

.field public static abc_popup_exit:I = 0x7f010004

.field public static abc_shrink_fade_out_from_bottom:I = 0x7f010005

.field public static abc_slide_in_bottom:I = 0x7f010006

.field public static abc_slide_in_top:I = 0x7f010007

.field public static abc_slide_out_bottom:I = 0x7f010008

.field public static abc_slide_out_top:I = 0x7f010009

.field public static abc_tooltip_enter:I = 0x7f01000a

.field public static abc_tooltip_exit:I = 0x7f01000b

.field public static fragment_fast_out_extra_slow_in:I = 0x7f010029


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
