.class public final Lcom/bumptech/glide/R$animator;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bumptech/glide/R;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "animator"
.end annotation


# static fields
.field public static fragment_close_enter:I = 0x7f020003

.field public static fragment_close_exit:I = 0x7f020004

.field public static fragment_fade_enter:I = 0x7f020005

.field public static fragment_fade_exit:I = 0x7f020006

.field public static fragment_open_enter:I = 0x7f020007

.field public static fragment_open_exit:I = 0x7f020008


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
