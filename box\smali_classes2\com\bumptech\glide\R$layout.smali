.class public final Lcom/bumptech/glide/R$layout;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bumptech/glide/R;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "layout"
.end annotation


# static fields
.field public static abc_action_bar_title_item:I = 0x7f0d0000

.field public static abc_action_bar_up_container:I = 0x7f0d0001

.field public static abc_action_menu_item_layout:I = 0x7f0d0002

.field public static abc_action_menu_layout:I = 0x7f0d0003

.field public static abc_action_mode_bar:I = 0x7f0d0004

.field public static abc_action_mode_close_item_material:I = 0x7f0d0005

.field public static abc_activity_chooser_view:I = 0x7f0d0006

.field public static abc_activity_chooser_view_list_item:I = 0x7f0d0007

.field public static abc_alert_dialog_button_bar_material:I = 0x7f0d0008

.field public static abc_alert_dialog_material:I = 0x7f0d0009

.field public static abc_alert_dialog_title_material:I = 0x7f0d000a

.field public static abc_cascading_menu_item_layout:I = 0x7f0d000b

.field public static abc_dialog_title_material:I = 0x7f0d000c

.field public static abc_expanded_menu_layout:I = 0x7f0d000d

.field public static abc_list_menu_item_checkbox:I = 0x7f0d000e

.field public static abc_list_menu_item_icon:I = 0x7f0d000f

.field public static abc_list_menu_item_layout:I = 0x7f0d0010

.field public static abc_list_menu_item_radio:I = 0x7f0d0011

.field public static abc_popup_menu_header_item_layout:I = 0x7f0d0012

.field public static abc_popup_menu_item_layout:I = 0x7f0d0013

.field public static abc_screen_content_include:I = 0x7f0d0014

.field public static abc_screen_simple:I = 0x7f0d0015

.field public static abc_screen_simple_overlay_action_mode:I = 0x7f0d0016

.field public static abc_screen_toolbar:I = 0x7f0d0017

.field public static abc_search_dropdown_item_icons_2line:I = 0x7f0d0018

.field public static abc_search_view:I = 0x7f0d0019

.field public static abc_select_dialog_material:I = 0x7f0d001a

.field public static abc_tooltip:I = 0x7f0d001b

.field public static custom_dialog:I = 0x7f0d00af

.field public static notification_action:I = 0x7f0d0392

.field public static notification_action_tombstone:I = 0x7f0d0393

.field public static notification_template_custom_big:I = 0x7f0d03a0

.field public static notification_template_icon_group:I = 0x7f0d03a1

.field public static notification_template_part_chronometer:I = 0x7f0d03a5

.field public static notification_template_part_time:I = 0x7f0d03a6

.field public static select_dialog_item_material:I = 0x7f0d03fb

.field public static select_dialog_multichoice_material:I = 0x7f0d03fc

.field public static select_dialog_singlechoice_material:I = 0x7f0d03fd

.field public static support_simple_spinner_dropdown_item:I = 0x7f0d0407


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
