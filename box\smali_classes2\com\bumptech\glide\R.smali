.class public final Lcom/bumptech/glide/R;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/bumptech/glide/R$anim;,
        Lcom/bumptech/glide/R$animator;,
        Lcom/bumptech/glide/R$attr;,
        Lcom/bumptech/glide/R$bool;,
        Lcom/bumptech/glide/R$color;,
        Lcom/bumptech/glide/R$dimen;,
        Lcom/bumptech/glide/R$drawable;,
        Lcom/bumptech/glide/R$id;,
        Lcom/bumptech/glide/R$integer;,
        Lcom/bumptech/glide/R$layout;,
        Lcom/bumptech/glide/R$string;,
        Lcom/bumptech/glide/R$style;,
        Lcom/bumptech/glide/R$styleable;
    }
.end annotation


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
