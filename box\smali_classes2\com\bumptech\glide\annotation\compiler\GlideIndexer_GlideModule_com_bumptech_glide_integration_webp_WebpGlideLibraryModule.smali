.class public Lcom/bumptech/glide/annotation/compiler/GlideIndexer_GlideModule_com_bumptech_glide_integration_webp_WebpGlideLibraryModule;
.super Ljava/lang/Object;


# annotations
.annotation build Lcom/bumptech/glide/annotation/compiler/Index;
    modules = {
        "com.bumptech.glide.integration.webp.WebpGlideLibraryModule"
    }
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
