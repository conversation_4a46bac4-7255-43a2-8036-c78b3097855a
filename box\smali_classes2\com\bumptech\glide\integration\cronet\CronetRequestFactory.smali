.class public interface abstract Lcom/bumptech/glide/integration/cronet/CronetRequestFactory;
.super Ljava/lang/Object;


# virtual methods
.method public abstract newRequest(Ljava/lang/String;ILjava/util/Map;Lorg/chromium/net/UrlRequest$Callback;)Lorg/chromium/net/UrlRequest$Builder;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "I",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ">;",
            "Lorg/chromium/net/UrlRequest$Callback;",
            ")",
            "Lorg/chromium/net/UrlRequest$Builder;"
        }
    .end annotation
.end method
