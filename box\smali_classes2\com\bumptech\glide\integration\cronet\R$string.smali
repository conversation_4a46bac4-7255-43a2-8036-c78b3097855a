.class public final Lcom/bumptech/glide/integration/cronet/R$string;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bumptech/glide/integration/cronet/R;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "string"
.end annotation


# static fields
.field public static CronetProviderClassName:I = 0x7f120000

.field public static common_google_play_services_enable_button:I = 0x7f1200ca

.field public static common_google_play_services_enable_text:I = 0x7f1200cb

.field public static common_google_play_services_enable_title:I = 0x7f1200cc

.field public static common_google_play_services_install_button:I = 0x7f1200cd

.field public static common_google_play_services_install_text:I = 0x7f1200ce

.field public static common_google_play_services_install_title:I = 0x7f1200cf

.field public static common_google_play_services_notification_channel_name:I = 0x7f1200d0

.field public static common_google_play_services_notification_ticker:I = 0x7f1200d1

.field public static common_google_play_services_unknown_issue:I = 0x7f1200d2

.field public static common_google_play_services_unsupported_text:I = 0x7f1200d3

.field public static common_google_play_services_update_button:I = 0x7f1200d4

.field public static common_google_play_services_update_text:I = 0x7f1200d5

.field public static common_google_play_services_update_title:I = 0x7f1200d6

.field public static common_google_play_services_updating_text:I = 0x7f1200d7

.field public static common_google_play_services_wear_update_text:I = 0x7f1200d8

.field public static common_open_on_phone:I = 0x7f1200d9

.field public static common_signin_button_text:I = 0x7f1200da

.field public static common_signin_button_text_long:I = 0x7f1200db

.field public static status_bar_notification_info_overflow:I = 0x7f120606


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
