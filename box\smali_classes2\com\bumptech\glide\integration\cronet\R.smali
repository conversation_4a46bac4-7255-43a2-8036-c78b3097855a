.class public final Lcom/bumptech/glide/integration/cronet/R;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/bumptech/glide/integration/cronet/R$attr;,
        Lcom/bumptech/glide/integration/cronet/R$color;,
        Lcom/bumptech/glide/integration/cronet/R$dimen;,
        Lcom/bumptech/glide/integration/cronet/R$drawable;,
        Lcom/bumptech/glide/integration/cronet/R$id;,
        Lcom/bumptech/glide/integration/cronet/R$integer;,
        Lcom/bumptech/glide/integration/cronet/R$layout;,
        Lcom/bumptech/glide/integration/cronet/R$raw;,
        Lcom/bumptech/glide/integration/cronet/R$string;,
        Lcom/bumptech/glide/integration/cronet/R$style;,
        Lcom/bumptech/glide/integration/cronet/R$styleable;
    }
.end annotation


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
