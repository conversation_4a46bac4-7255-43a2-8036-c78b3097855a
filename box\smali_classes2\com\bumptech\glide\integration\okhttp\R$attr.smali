.class public final Lcom/bumptech/glide/integration/okhttp/R$attr;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bumptech/glide/integration/okhttp/R;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "attr"
.end annotation


# static fields
.field public static actionBarDivider:I = 0x7f040008

.field public static actionBarItemBackground:I = 0x7f040009

.field public static actionBarPopupTheme:I = 0x7f04000a

.field public static actionBarSize:I = 0x7f04000b

.field public static actionBarSplitStyle:I = 0x7f04000c

.field public static actionBarStyle:I = 0x7f04000d

.field public static actionBarTabBarStyle:I = 0x7f04000e

.field public static actionBarTabStyle:I = 0x7f04000f

.field public static actionBarTabTextStyle:I = 0x7f040010

.field public static actionBarTheme:I = 0x7f040011

.field public static actionBarWidgetTheme:I = 0x7f040012

.field public static actionButtonStyle:I = 0x7f040013

.field public static actionDropDownStyle:I = 0x7f040014

.field public static actionLayout:I = 0x7f040015

.field public static actionMenuTextAppearance:I = 0x7f040016

.field public static actionMenuTextColor:I = 0x7f040017

.field public static actionModeBackground:I = 0x7f040018

.field public static actionModeCloseButtonStyle:I = 0x7f040019

.field public static actionModeCloseDrawable:I = 0x7f04001b

.field public static actionModeCopyDrawable:I = 0x7f04001c

.field public static actionModeCutDrawable:I = 0x7f04001d

.field public static actionModeFindDrawable:I = 0x7f04001e

.field public static actionModePasteDrawable:I = 0x7f04001f

.field public static actionModePopupWindowStyle:I = 0x7f040020

.field public static actionModeSelectAllDrawable:I = 0x7f040021

.field public static actionModeShareDrawable:I = 0x7f040022

.field public static actionModeSplitBackground:I = 0x7f040023

.field public static actionModeStyle:I = 0x7f040024

.field public static actionModeWebSearchDrawable:I = 0x7f040026

.field public static actionOverflowButtonStyle:I = 0x7f040027

.field public static actionOverflowMenuStyle:I = 0x7f040028

.field public static actionProviderClass:I = 0x7f040029

.field public static actionViewClass:I = 0x7f04002b

.field public static activityChooserViewStyle:I = 0x7f04002e

.field public static alertDialogButtonGroupStyle:I = 0x7f040039

.field public static alertDialogCenterButtons:I = 0x7f04003a

.field public static alertDialogStyle:I = 0x7f04003b

.field public static alertDialogTheme:I = 0x7f04003c

.field public static allowStacking:I = 0x7f04003f

.field public static alpha:I = 0x7f040040

.field public static alphabeticModifiers:I = 0x7f040041

.field public static arrowHeadLength:I = 0x7f040051

.field public static arrowShaftLength:I = 0x7f040058

.field public static autoCompleteTextViewStyle:I = 0x7f04005f

.field public static autoSizeMaxTextSize:I = 0x7f040061

.field public static autoSizeMinTextSize:I = 0x7f040062

.field public static autoSizePresetSizes:I = 0x7f040063

.field public static autoSizeStepGranularity:I = 0x7f040064

.field public static autoSizeTextType:I = 0x7f040065

.field public static background:I = 0x7f04006a

.field public static backgroundSplit:I = 0x7f040071

.field public static backgroundStacked:I = 0x7f040072

.field public static backgroundTint:I = 0x7f040073

.field public static backgroundTintMode:I = 0x7f040074

.field public static barLength:I = 0x7f04009d

.field public static borderlessButtonStyle:I = 0x7f0401be

.field public static buttonBarButtonStyle:I = 0x7f0401de

.field public static buttonBarNegativeButtonStyle:I = 0x7f0401df

.field public static buttonBarNeutralButtonStyle:I = 0x7f0401e0

.field public static buttonBarPositiveButtonStyle:I = 0x7f0401e1

.field public static buttonBarStyle:I = 0x7f0401e2

.field public static buttonGravity:I = 0x7f0401e4

.field public static buttonIconDimen:I = 0x7f0401e6

.field public static buttonPanelSideLayout:I = 0x7f0401e9

.field public static buttonStyle:I = 0x7f0401eb

.field public static buttonStyleSmall:I = 0x7f0401ec

.field public static buttonTint:I = 0x7f0401ed

.field public static buttonTintMode:I = 0x7f0401ee

.field public static checkboxStyle:I = 0x7f04020e

.field public static checkedTextViewStyle:I = 0x7f040219

.field public static closeIcon:I = 0x7f040248

.field public static closeItemLayout:I = 0x7f04024f

.field public static collapseContentDescription:I = 0x7f040250

.field public static collapseIcon:I = 0x7f040251

.field public static color:I = 0x7f04025b

.field public static colorAccent:I = 0x7f04025c

.field public static colorBackgroundFloating:I = 0x7f04025d

.field public static colorButtonNormal:I = 0x7f04025e

.field public static colorControlActivated:I = 0x7f040260

.field public static colorControlHighlight:I = 0x7f040261

.field public static colorControlNormal:I = 0x7f040262

.field public static colorError:I = 0x7f040263

.field public static colorPrimary:I = 0x7f04027c

.field public static colorPrimaryDark:I = 0x7f04027e

.field public static colorSwitchThumbNormal:I = 0x7f040294

.field public static commitIcon:I = 0x7f040299

.field public static contentDescription:I = 0x7f0402a3

.field public static contentInsetEnd:I = 0x7f0402a4

.field public static contentInsetEndWithActions:I = 0x7f0402a5

.field public static contentInsetLeft:I = 0x7f0402a6

.field public static contentInsetRight:I = 0x7f0402a7

.field public static contentInsetStart:I = 0x7f0402a8

.field public static contentInsetStartWithNavigation:I = 0x7f0402a9

.field public static controlBackground:I = 0x7f0402b3

.field public static coordinatorLayoutStyle:I = 0x7f0402b5

.field public static customNavigationLayout:I = 0x7f0402d9

.field public static defaultQueryHint:I = 0x7f0402e6

.field public static dialogCornerRadius:I = 0x7f0402f1

.field public static dialogPreferredPadding:I = 0x7f0402f2

.field public static dialogTheme:I = 0x7f0402f3

.field public static displayOptions:I = 0x7f0402f4

.field public static divider:I = 0x7f0402f5

.field public static dividerHorizontal:I = 0x7f0402fa

.field public static dividerPadding:I = 0x7f0402fd

.field public static dividerVertical:I = 0x7f0402ff

.field public static drawableSize:I = 0x7f040308

.field public static drawerArrowStyle:I = 0x7f04030d

.field public static dropDownListViewStyle:I = 0x7f040311

.field public static dropdownListPreferredItemHeight:I = 0x7f040312

.field public static editTextBackground:I = 0x7f040315

.field public static editTextColor:I = 0x7f040316

.field public static editTextStyle:I = 0x7f040317

.field public static elevation:I = 0x7f040318

.field public static expandActivityOverflowButtonDrawable:I = 0x7f040349

.field public static firstBaselineToTopHeight:I = 0x7f04036c

.field public static font:I = 0x7f040399

.field public static fontFamily:I = 0x7f04039a

.field public static fontProviderAuthority:I = 0x7f04039b

.field public static fontProviderCerts:I = 0x7f04039c

.field public static fontProviderFetchStrategy:I = 0x7f04039d

.field public static fontProviderFetchTimeout:I = 0x7f04039e

.field public static fontProviderPackage:I = 0x7f04039f

.field public static fontProviderQuery:I = 0x7f0403a0

.field public static fontStyle:I = 0x7f0403a2

.field public static fontVariationSettings:I = 0x7f0403a3

.field public static fontWeight:I = 0x7f0403a4

.field public static gapBetweenBars:I = 0x7f0403aa

.field public static goIcon:I = 0x7f0403ad

.field public static height:I = 0x7f0403bb

.field public static hideOnContentScroll:I = 0x7f0403c3

.field public static homeAsUpIndicator:I = 0x7f0403cb

.field public static homeLayout:I = 0x7f0403cc

.field public static icon:I = 0x7f0403d0

.field public static iconTint:I = 0x7f0403d7

.field public static iconTintMode:I = 0x7f0403d8

.field public static iconifiedByDefault:I = 0x7f0403d9

.field public static imageButtonStyle:I = 0x7f0403de

.field public static indeterminateProgressStyle:I = 0x7f0403e4

.field public static initialActivityCount:I = 0x7f0403f4

.field public static isLightTheme:I = 0x7f0403f9

.field public static itemPadding:I = 0x7f040431

.field public static keylines:I = 0x7f040459

.field public static lastBaselineToBottomHeight:I = 0x7f04045f

.field public static layout:I = 0x7f040466

.field public static layout_anchor:I = 0x7f04046b

.field public static layout_anchorGravity:I = 0x7f04046c

.field public static layout_behavior:I = 0x7f04046d

.field public static layout_dodgeInsetEdges:I = 0x7f04049e

.field public static layout_insetEdge:I = 0x7f0404ad

.field public static layout_keyline:I = 0x7f0404ae

.field public static lineHeight:I = 0x7f0404c3

.field public static listChoiceBackgroundIndicator:I = 0x7f0404c6

.field public static listDividerAlertDialog:I = 0x7f0404c9

.field public static listItemLayout:I = 0x7f0404ca

.field public static listLayout:I = 0x7f0404cb

.field public static listMenuViewStyle:I = 0x7f0404cc

.field public static listPopupWindowStyle:I = 0x7f0404cd

.field public static listPreferredItemHeight:I = 0x7f0404ce

.field public static listPreferredItemHeightLarge:I = 0x7f0404cf

.field public static listPreferredItemHeightSmall:I = 0x7f0404d0

.field public static listPreferredItemPaddingLeft:I = 0x7f0404d2

.field public static listPreferredItemPaddingRight:I = 0x7f0404d3

.field public static logo:I = 0x7f0404d6

.field public static logoDescription:I = 0x7f0404d8

.field public static maxButtonHeight:I = 0x7f040528

.field public static measureWithLargestChild:I = 0x7f040538

.field public static multiChoiceItemLayout:I = 0x7f040578

.field public static navigationContentDescription:I = 0x7f04057a

.field public static navigationIcon:I = 0x7f04057b

.field public static navigationMode:I = 0x7f04057d

.field public static numericModifiers:I = 0x7f04058b

.field public static overlapAnchor:I = 0x7f040594

.field public static paddingBottomNoButtons:I = 0x7f040596

.field public static paddingEnd:I = 0x7f040598

.field public static paddingStart:I = 0x7f04059b

.field public static paddingTopNoTitle:I = 0x7f04059d

.field public static panelBackground:I = 0x7f04059f

.field public static panelMenuListTheme:I = 0x7f0405a0

.field public static panelMenuListWidth:I = 0x7f0405a1

.field public static popupMenuStyle:I = 0x7f0405d2

.field public static popupTheme:I = 0x7f0405d3

.field public static popupWindowStyle:I = 0x7f0405d4

.field public static preserveIconSpacing:I = 0x7f0405d9

.field public static progressBarPadding:I = 0x7f0405dd

.field public static progressBarStyle:I = 0x7f0405de

.field public static queryBackground:I = 0x7f0405ea

.field public static queryHint:I = 0x7f0405eb

.field public static radioButtonStyle:I = 0x7f0405ee

.field public static ratingBarStyle:I = 0x7f0405f1

.field public static ratingBarStyleIndicator:I = 0x7f0405f2

.field public static ratingBarStyleSmall:I = 0x7f0405f3

.field public static searchHintIcon:I = 0x7f04062b

.field public static searchIcon:I = 0x7f04062c

.field public static searchViewStyle:I = 0x7f04062e

.field public static seekBarStyle:I = 0x7f040631

.field public static selectableItemBackground:I = 0x7f040632

.field public static selectableItemBackgroundBorderless:I = 0x7f040633

.field public static showAsAction:I = 0x7f04065e

.field public static showDividers:I = 0x7f040663

.field public static showText:I = 0x7f04066a

.field public static showTitle:I = 0x7f04066b

.field public static singleChoiceItemLayout:I = 0x7f04067d

.field public static spinBars:I = 0x7f040686

.field public static spinnerDropDownItemStyle:I = 0x7f040687

.field public static spinnerStyle:I = 0x7f040688

.field public static splitTrack:I = 0x7f04068e

.field public static srcCompat:I = 0x7f040694

.field public static state_above_anchor:I = 0x7f0406ae

.field public static statusBarBackground:I = 0x7f0406b7

.field public static subMenuArrow:I = 0x7f0406bf

.field public static submitBackground:I = 0x7f0406c4

.field public static subtitle:I = 0x7f0406c5

.field public static subtitleTextAppearance:I = 0x7f0406c7

.field public static subtitleTextColor:I = 0x7f0406c8

.field public static subtitleTextStyle:I = 0x7f0406c9

.field public static suggestionRowLayout:I = 0x7f0406cd

.field public static switchMinWidth:I = 0x7f0406d0

.field public static switchPadding:I = 0x7f0406d1

.field public static switchStyle:I = 0x7f0406d2

.field public static switchTextAppearance:I = 0x7f0406d3

.field public static textAllCaps:I = 0x7f0406fd

.field public static textAppearanceLargePopupMenu:I = 0x7f040714

.field public static textAppearanceListItem:I = 0x7f040716

.field public static textAppearanceListItemSecondary:I = 0x7f040717

.field public static textAppearanceListItemSmall:I = 0x7f040718

.field public static textAppearancePopupMenuHeader:I = 0x7f04071a

.field public static textAppearanceSearchResultSubtitle:I = 0x7f04071b

.field public static textAppearanceSearchResultTitle:I = 0x7f04071c

.field public static textAppearanceSmallPopupMenu:I = 0x7f04071d

.field public static textColorAlertDialogListItem:I = 0x7f040728

.field public static textColorSearchUrl:I = 0x7f040729

.field public static theme:I = 0x7f04073f

.field public static thickness:I = 0x7f040740

.field public static thumbTextPadding:I = 0x7f04074b

.field public static thumbTint:I = 0x7f04074c

.field public static thumbTintMode:I = 0x7f04074d

.field public static tickMark:I = 0x7f040753

.field public static tickMarkTint:I = 0x7f040754

.field public static tickMarkTintMode:I = 0x7f040755

.field public static tint:I = 0x7f04075a

.field public static tintMode:I = 0x7f04075b

.field public static title:I = 0x7f040760

.field public static titleMargin:I = 0x7f040766

.field public static titleMarginBottom:I = 0x7f040767

.field public static titleMarginEnd:I = 0x7f040768

.field public static titleMarginStart:I = 0x7f040769

.field public static titleMarginTop:I = 0x7f04076a

.field public static titleMargins:I = 0x7f04076b

.field public static titleTextAppearance:I = 0x7f04076e

.field public static titleTextColor:I = 0x7f04076f

.field public static titleTextStyle:I = 0x7f040772

.field public static toolbarNavigationButtonStyle:I = 0x7f040776

.field public static toolbarStyle:I = 0x7f040777

.field public static tooltipForegroundColor:I = 0x7f040779

.field public static tooltipFrameBackground:I = 0x7f04077a

.field public static tooltipText:I = 0x7f04077c

.field public static track:I = 0x7f04078c

.field public static trackTint:I = 0x7f040798

.field public static trackTintMode:I = 0x7f040799

.field public static ttcIndex:I = 0x7f0407a3

.field public static viewInflaterClass:I = 0x7f0407b9

.field public static voiceIcon:I = 0x7f0407bf

.field public static windowActionBar:I = 0x7f0407cd

.field public static windowActionBarOverlay:I = 0x7f0407ce

.field public static windowActionModeOverlay:I = 0x7f0407cf

.field public static windowFixedHeightMajor:I = 0x7f0407d0

.field public static windowFixedHeightMinor:I = 0x7f0407d1

.field public static windowFixedWidthMajor:I = 0x7f0407d2

.field public static windowFixedWidthMinor:I = 0x7f0407d3

.field public static windowMinWidthMajor:I = 0x7f0407d4

.field public static windowMinWidthMinor:I = 0x7f0407d5

.field public static windowNoTitle:I = 0x7f0407d6


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
