.class public final Lcom/bumptech/glide/integration/okhttp/R$color;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bumptech/glide/integration/okhttp/R;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "color"
.end annotation


# static fields
.field public static abc_background_cache_hint_selector_material_dark:I = 0x7f060000

.field public static abc_background_cache_hint_selector_material_light:I = 0x7f060001

.field public static abc_btn_colored_borderless_text_material:I = 0x7f060002

.field public static abc_btn_colored_text_material:I = 0x7f060003

.field public static abc_color_highlight_material:I = 0x7f060004

.field public static abc_hint_foreground_material_dark:I = 0x7f060007

.field public static abc_hint_foreground_material_light:I = 0x7f060008

.field public static abc_primary_text_disable_only_material_dark:I = 0x7f060009

.field public static abc_primary_text_disable_only_material_light:I = 0x7f06000a

.field public static abc_primary_text_material_dark:I = 0x7f06000b

.field public static abc_primary_text_material_light:I = 0x7f06000c

.field public static abc_search_url_text:I = 0x7f06000d

.field public static abc_search_url_text_normal:I = 0x7f06000e

.field public static abc_search_url_text_pressed:I = 0x7f06000f

.field public static abc_search_url_text_selected:I = 0x7f060010

.field public static abc_secondary_text_material_dark:I = 0x7f060011

.field public static abc_secondary_text_material_light:I = 0x7f060012

.field public static abc_tint_btn_checkable:I = 0x7f060013

.field public static abc_tint_default:I = 0x7f060014

.field public static abc_tint_edittext:I = 0x7f060015

.field public static abc_tint_seek_thumb:I = 0x7f060016

.field public static abc_tint_spinner:I = 0x7f060017

.field public static abc_tint_switch_track:I = 0x7f060018

.field public static accent_material_dark:I = 0x7f060019

.field public static accent_material_light:I = 0x7f06001a

.field public static background_floating_material_dark:I = 0x7f060029

.field public static background_floating_material_light:I = 0x7f06002a

.field public static background_material_dark:I = 0x7f06002b

.field public static background_material_light:I = 0x7f06002c

.field public static bright_foreground_disabled_material_dark:I = 0x7f06009c

.field public static bright_foreground_disabled_material_light:I = 0x7f06009d

.field public static bright_foreground_inverse_material_dark:I = 0x7f06009e

.field public static bright_foreground_inverse_material_light:I = 0x7f06009f

.field public static bright_foreground_material_dark:I = 0x7f0600a0

.field public static bright_foreground_material_light:I = 0x7f0600a1

.field public static button_material_dark:I = 0x7f0600a8

.field public static button_material_light:I = 0x7f0600a9

.field public static dim_foreground_disabled_material_dark:I = 0x7f06016d

.field public static dim_foreground_disabled_material_light:I = 0x7f06016e

.field public static dim_foreground_material_dark:I = 0x7f06016f

.field public static dim_foreground_material_light:I = 0x7f060170

.field public static error_color_material_dark:I = 0x7f060186

.field public static error_color_material_light:I = 0x7f060187

.field public static foreground_material_dark:I = 0x7f060192

.field public static foreground_material_light:I = 0x7f060193

.field public static highlighted_text_material_dark:I = 0x7f0601ba

.field public static highlighted_text_material_light:I = 0x7f0601bb

.field public static material_blue_grey_800:I = 0x7f060398

.field public static material_blue_grey_900:I = 0x7f060399

.field public static material_blue_grey_950:I = 0x7f06039a

.field public static material_deep_teal_200:I = 0x7f06039c

.field public static material_deep_teal_500:I = 0x7f06039d

.field public static material_grey_100:I = 0x7f0603e8

.field public static material_grey_300:I = 0x7f0603e9

.field public static material_grey_50:I = 0x7f0603ea

.field public static material_grey_600:I = 0x7f0603eb

.field public static material_grey_800:I = 0x7f0603ec

.field public static material_grey_850:I = 0x7f0603ed

.field public static material_grey_900:I = 0x7f0603ee

.field public static notification_action_color_filter:I = 0x7f0604c4

.field public static notification_icon_bg_color:I = 0x7f0604c6

.field public static primary_dark_material_dark:I = 0x7f0604ed

.field public static primary_dark_material_light:I = 0x7f0604ee

.field public static primary_material_dark:I = 0x7f0604ef

.field public static primary_material_light:I = 0x7f0604f0

.field public static primary_text_default_material_dark:I = 0x7f0604f1

.field public static primary_text_default_material_light:I = 0x7f0604f2

.field public static primary_text_disabled_material_dark:I = 0x7f0604f3

.field public static primary_text_disabled_material_light:I = 0x7f0604f4

.field public static ripple_material_dark:I = 0x7f060658

.field public static ripple_material_light:I = 0x7f060659

.field public static secondary_text_default_material_dark:I = 0x7f06065b

.field public static secondary_text_default_material_light:I = 0x7f06065c

.field public static secondary_text_disabled_material_dark:I = 0x7f06065d

.field public static secondary_text_disabled_material_light:I = 0x7f06065e

.field public static switch_thumb_disabled_material_dark:I = 0x7f060678

.field public static switch_thumb_disabled_material_light:I = 0x7f060679

.field public static switch_thumb_material_dark:I = 0x7f06067a

.field public static switch_thumb_material_light:I = 0x7f06067b

.field public static switch_thumb_normal_material_dark:I = 0x7f06067c

.field public static switch_thumb_normal_material_light:I = 0x7f06067d

.field public static tooltip_background_dark:I = 0x7f060691

.field public static tooltip_background_light:I = 0x7f060692


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
