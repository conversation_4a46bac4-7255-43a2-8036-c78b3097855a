.class public final Lcom/bumptech/glide/integration/okhttp/R$integer;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bumptech/glide/integration/okhttp/R;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "integer"
.end annotation


# static fields
.field public static abc_config_activityDefaultDur:I = 0x7f0b0000

.field public static abc_config_activityShortDur:I = 0x7f0b0001

.field public static cancel_button_image_alpha:I = 0x7f0b0006

.field public static config_tooltipAnimTime:I = 0x7f0b0008

.field public static status_bar_notification_info_maxnum:I = 0x7f0b004a


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
