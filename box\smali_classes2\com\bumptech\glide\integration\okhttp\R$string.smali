.class public final Lcom/bumptech/glide/integration/okhttp/R$string;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bumptech/glide/integration/okhttp/R;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "string"
.end annotation


# static fields
.field public static abc_action_bar_home_description:I = 0x7f120010

.field public static abc_action_bar_up_description:I = 0x7f120011

.field public static abc_action_menu_overflow_description:I = 0x7f120012

.field public static abc_action_mode_done:I = 0x7f120013

.field public static abc_activity_chooser_view_see_all:I = 0x7f120014

.field public static abc_activitychooserview_choose_application:I = 0x7f120015

.field public static abc_capital_off:I = 0x7f120016

.field public static abc_capital_on:I = 0x7f120017

.field public static abc_menu_alt_shortcut_label:I = 0x7f120018

.field public static abc_menu_ctrl_shortcut_label:I = 0x7f120019

.field public static abc_menu_delete_shortcut_label:I = 0x7f12001a

.field public static abc_menu_enter_shortcut_label:I = 0x7f12001b

.field public static abc_menu_function_shortcut_label:I = 0x7f12001c

.field public static abc_menu_meta_shortcut_label:I = 0x7f12001d

.field public static abc_menu_shift_shortcut_label:I = 0x7f12001e

.field public static abc_menu_space_shortcut_label:I = 0x7f12001f

.field public static abc_menu_sym_shortcut_label:I = 0x7f120020

.field public static abc_prepend_shortcut_label:I = 0x7f120021

.field public static abc_search_hint:I = 0x7f120022

.field public static abc_searchview_description_clear:I = 0x7f120023

.field public static abc_searchview_description_query:I = 0x7f120024

.field public static abc_searchview_description_search:I = 0x7f120025

.field public static abc_searchview_description_submit:I = 0x7f120026

.field public static abc_searchview_description_voice:I = 0x7f120027

.field public static abc_shareactionprovider_share_with:I = 0x7f120028

.field public static abc_shareactionprovider_share_with_application:I = 0x7f120029

.field public static abc_toolbar_collapse_description:I = 0x7f12002a

.field public static search_menu_title:I = 0x7f120598

.field public static status_bar_notification_info_overflow:I = 0x7f120606


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
