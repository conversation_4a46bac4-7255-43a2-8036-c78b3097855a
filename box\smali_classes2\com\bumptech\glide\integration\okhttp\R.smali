.class public final Lcom/bumptech/glide/integration/okhttp/R;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/bumptech/glide/integration/okhttp/R$anim;,
        Lcom/bumptech/glide/integration/okhttp/R$animator;,
        Lcom/bumptech/glide/integration/okhttp/R$attr;,
        Lcom/bumptech/glide/integration/okhttp/R$bool;,
        Lcom/bumptech/glide/integration/okhttp/R$color;,
        Lcom/bumptech/glide/integration/okhttp/R$dimen;,
        Lcom/bumptech/glide/integration/okhttp/R$drawable;,
        Lcom/bumptech/glide/integration/okhttp/R$id;,
        Lcom/bumptech/glide/integration/okhttp/R$integer;,
        Lcom/bumptech/glide/integration/okhttp/R$layout;,
        Lcom/bumptech/glide/integration/okhttp/R$string;,
        Lcom/bumptech/glide/integration/okhttp/R$style;,
        Lcom/bumptech/glide/integration/okhttp/R$styleable;
    }
.end annotation


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
