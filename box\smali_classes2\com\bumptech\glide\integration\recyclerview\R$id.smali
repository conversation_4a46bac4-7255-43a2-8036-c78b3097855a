.class public final Lcom/bumptech/glide/integration/recyclerview/R$id;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bumptech/glide/integration/recyclerview/R;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "id"
.end annotation


# static fields
.field public static accessibility_action_clickable_span:I = 0x7f0a0034

.field public static accessibility_custom_action_0:I = 0x7f0a0035

.field public static accessibility_custom_action_1:I = 0x7f0a0036

.field public static accessibility_custom_action_10:I = 0x7f0a0037

.field public static accessibility_custom_action_11:I = 0x7f0a0038

.field public static accessibility_custom_action_12:I = 0x7f0a0039

.field public static accessibility_custom_action_13:I = 0x7f0a003a

.field public static accessibility_custom_action_14:I = 0x7f0a003b

.field public static accessibility_custom_action_15:I = 0x7f0a003c

.field public static accessibility_custom_action_16:I = 0x7f0a003d

.field public static accessibility_custom_action_17:I = 0x7f0a003e

.field public static accessibility_custom_action_18:I = 0x7f0a003f

.field public static accessibility_custom_action_19:I = 0x7f0a0040

.field public static accessibility_custom_action_2:I = 0x7f0a0041

.field public static accessibility_custom_action_20:I = 0x7f0a0042

.field public static accessibility_custom_action_21:I = 0x7f0a0043

.field public static accessibility_custom_action_22:I = 0x7f0a0044

.field public static accessibility_custom_action_23:I = 0x7f0a0045

.field public static accessibility_custom_action_24:I = 0x7f0a0046

.field public static accessibility_custom_action_25:I = 0x7f0a0047

.field public static accessibility_custom_action_26:I = 0x7f0a0048

.field public static accessibility_custom_action_27:I = 0x7f0a0049

.field public static accessibility_custom_action_28:I = 0x7f0a004a

.field public static accessibility_custom_action_29:I = 0x7f0a004b

.field public static accessibility_custom_action_3:I = 0x7f0a004c

.field public static accessibility_custom_action_30:I = 0x7f0a004d

.field public static accessibility_custom_action_31:I = 0x7f0a004e

.field public static accessibility_custom_action_4:I = 0x7f0a004f

.field public static accessibility_custom_action_5:I = 0x7f0a0050

.field public static accessibility_custom_action_6:I = 0x7f0a0051

.field public static accessibility_custom_action_7:I = 0x7f0a0052

.field public static accessibility_custom_action_8:I = 0x7f0a0053

.field public static accessibility_custom_action_9:I = 0x7f0a0054

.field public static action_bar:I = 0x7f0a0059

.field public static action_bar_activity_content:I = 0x7f0a005a

.field public static action_bar_container:I = 0x7f0a005b

.field public static action_bar_root:I = 0x7f0a005c

.field public static action_bar_spinner:I = 0x7f0a005d

.field public static action_bar_subtitle:I = 0x7f0a005e

.field public static action_bar_title:I = 0x7f0a005f

.field public static action_container:I = 0x7f0a0060

.field public static action_context_bar:I = 0x7f0a0061

.field public static action_divider:I = 0x7f0a0062

.field public static action_image:I = 0x7f0a0063

.field public static action_menu_divider:I = 0x7f0a0064

.field public static action_menu_presenter:I = 0x7f0a0065

.field public static action_mode_bar:I = 0x7f0a0066

.field public static action_mode_bar_stub:I = 0x7f0a0067

.field public static action_mode_close_button:I = 0x7f0a0068

.field public static action_text:I = 0x7f0a0069

.field public static actions:I = 0x7f0a006a

.field public static activity_chooser_view_content:I = 0x7f0a006b

.field public static add:I = 0x7f0a008d

.field public static alertTitle:I = 0x7f0a0091

.field public static async:I = 0x7f0a00ab

.field public static blocking:I = 0x7f0a00d0

.field public static bottom:I = 0x7f0a00d3

.field public static buttonPanel:I = 0x7f0a011e

.field public static checkbox:I = 0x7f0a0140

.field public static chronometer:I = 0x7f0a0142

.field public static content:I = 0x7f0a0191

.field public static contentPanel:I = 0x7f0a0195

.field public static custom:I = 0x7f0a01ab

.field public static customPanel:I = 0x7f0a01ac

.field public static decor_content_parent:I = 0x7f0a01b4

.field public static default_activity_button:I = 0x7f0a01b5

.field public static dialog_button:I = 0x7f0a01c2

.field public static edit_query:I = 0x7f0a01ec

.field public static end:I = 0x7f0a01f7

.field public static expand_activities_button:I = 0x7f0a024d

.field public static expanded_menu:I = 0x7f0a024e

.field public static forever:I = 0x7f0a02b7

.field public static fragment_container_view_tag:I = 0x7f0a02ba

.field public static glide_custom_view_target_tag:I = 0x7f0a02c3

.field public static group_divider:I = 0x7f0a02db

.field public static home:I = 0x7f0a0300

.field public static icon:I = 0x7f0a030f

.field public static icon_group:I = 0x7f0a0312

.field public static image:I = 0x7f0a0323

.field public static info:I = 0x7f0a0337

.field public static italic:I = 0x7f0a034a

.field public static item_touch_helper_previous_elevation:I = 0x7f0a0379

.field public static left:I = 0x7f0a04dc

.field public static line1:I = 0x7f0a04e8

.field public static line3:I = 0x7f0a04e9

.field public static listMode:I = 0x7f0a04ec

.field public static list_item:I = 0x7f0a04ed

.field public static message:I = 0x7f0a0654

.field public static multiply:I = 0x7f0a0681

.field public static none:I = 0x7f0a06ad

.field public static normal:I = 0x7f0a06ae

.field public static notification_background:I = 0x7f0a06b7

.field public static notification_main_column:I = 0x7f0a06bc

.field public static notification_main_column_container:I = 0x7f0a06bd

.field public static parentPanel:I = 0x7f0a0700

.field public static progress_circular:I = 0x7f0a074a

.field public static progress_horizontal:I = 0x7f0a074c

.field public static radio:I = 0x7f0a0757

.field public static right:I = 0x7f0a0791

.field public static right_icon:I = 0x7f0a0796

.field public static right_side:I = 0x7f0a0798

.field public static screen:I = 0x7f0a07d1

.field public static scrollIndicatorDown:I = 0x7f0a07d3

.field public static scrollIndicatorUp:I = 0x7f0a07d4

.field public static scrollView:I = 0x7f0a07d5

.field public static search_badge:I = 0x7f0a07db

.field public static search_bar:I = 0x7f0a07dc

.field public static search_button:I = 0x7f0a07dd

.field public static search_close_btn:I = 0x7f0a07de

.field public static search_edit_frame:I = 0x7f0a07e0

.field public static search_go_btn:I = 0x7f0a07e2

.field public static search_mag_icon:I = 0x7f0a07f6

.field public static search_plate:I = 0x7f0a07f7

.field public static search_src_text:I = 0x7f0a0819

.field public static search_voice_btn:I = 0x7f0a081a

.field public static select_dialog_listview:I = 0x7f0a0824

.field public static shortcut:I = 0x7f0a0833

.field public static spacer:I = 0x7f0a084d

.field public static special_effects_controller_view_tag:I = 0x7f0a084e

.field public static split_action_bar:I = 0x7f0a0853

.field public static src_atop:I = 0x7f0a0858

.field public static src_in:I = 0x7f0a0859

.field public static src_over:I = 0x7f0a085a

.field public static start:I = 0x7f0a085d

.field public static submenuarrow:I = 0x7f0a08b2

.field public static submit_area:I = 0x7f0a08b4

.field public static tabMode:I = 0x7f0a08cb

.field public static tag_accessibility_actions:I = 0x7f0a08d4

.field public static tag_accessibility_clickable_spans:I = 0x7f0a08d5

.field public static tag_accessibility_heading:I = 0x7f0a08d6

.field public static tag_accessibility_pane_title:I = 0x7f0a08d7

.field public static tag_screen_reader_focusable:I = 0x7f0a08e1

.field public static tag_transition_group:I = 0x7f0a08e3

.field public static tag_unhandled_key_event_manager:I = 0x7f0a08e4

.field public static tag_unhandled_key_listeners:I = 0x7f0a08e5

.field public static text:I = 0x7f0a08ea

.field public static text2:I = 0x7f0a08eb

.field public static textSpacerNoButtons:I = 0x7f0a08ed

.field public static textSpacerNoTitle:I = 0x7f0a08ee

.field public static time:I = 0x7f0a0902

.field public static title:I = 0x7f0a0908

.field public static titleDividerNoCustom:I = 0x7f0a090a

.field public static title_template:I = 0x7f0a0913

.field public static top:I = 0x7f0a091e

.field public static topPanel:I = 0x7f0a0921

.field public static uniform:I = 0x7f0a0b41

.field public static up:I = 0x7f0a0b43

.field public static view_tree_lifecycle_owner:I = 0x7f0a0c10

.field public static view_tree_saved_state_registry_owner:I = 0x7f0a0c12

.field public static view_tree_view_model_store_owner:I = 0x7f0a0c13

.field public static visible_removing_fragment_view_tag:I = 0x7f0a0c16

.field public static wrap_content:I = 0x7f0a0c33


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
