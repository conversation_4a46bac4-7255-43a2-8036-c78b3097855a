.class public final Lcom/bumptech/glide/integration/recyclerview/R;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/bumptech/glide/integration/recyclerview/R$anim;,
        Lcom/bumptech/glide/integration/recyclerview/R$animator;,
        Lcom/bumptech/glide/integration/recyclerview/R$attr;,
        Lcom/bumptech/glide/integration/recyclerview/R$bool;,
        Lcom/bumptech/glide/integration/recyclerview/R$color;,
        Lcom/bumptech/glide/integration/recyclerview/R$dimen;,
        Lcom/bumptech/glide/integration/recyclerview/R$drawable;,
        Lcom/bumptech/glide/integration/recyclerview/R$id;,
        Lcom/bumptech/glide/integration/recyclerview/R$integer;,
        Lcom/bumptech/glide/integration/recyclerview/R$layout;,
        Lcom/bumptech/glide/integration/recyclerview/R$string;,
        Lcom/bumptech/glide/integration/recyclerview/R$style;,
        Lcom/bumptech/glide/integration/recyclerview/R$styleable;
    }
.end annotation


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
