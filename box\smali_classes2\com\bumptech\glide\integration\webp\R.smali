.class public final Lcom/bumptech/glide/integration/webp/R;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/bumptech/glide/integration/webp/R$attr;,
        Lcom/bumptech/glide/integration/webp/R$color;,
        Lcom/bumptech/glide/integration/webp/R$dimen;,
        Lcom/bumptech/glide/integration/webp/R$drawable;,
        Lcom/bumptech/glide/integration/webp/R$id;,
        Lcom/bumptech/glide/integration/webp/R$integer;,
        Lcom/bumptech/glide/integration/webp/R$layout;,
        Lcom/bumptech/glide/integration/webp/R$string;,
        Lcom/bumptech/glide/integration/webp/R$style;,
        Lcom/bumptech/glide/integration/webp/R$styleable;
    }
.end annotation


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
