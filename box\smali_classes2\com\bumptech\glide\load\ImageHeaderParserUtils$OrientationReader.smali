.class interface abstract Lcom/bumptech/glide/load/ImageHeaderParserUtils$OrientationReader;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bumptech/glide/load/ImageHeaderParserUtils;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "OrientationReader"
.end annotation


# virtual methods
.method public abstract getOrientation(Lcom/bumptech/glide/load/ImageHeaderParser;)I
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation
.end method
