.class interface abstract Lcom/bumptech/glide/load/engine/ActiveResources$DequeuedResourceCallback;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bumptech/glide/load/engine/ActiveResources;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "DequeuedResourceCallback"
.end annotation


# virtual methods
.method public abstract onResourceDequeued()V
.end method
