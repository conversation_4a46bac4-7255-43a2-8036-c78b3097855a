.class interface abstract Lcom/bumptech/glide/load/engine/DataFetcherGenerator;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/bumptech/glide/load/engine/DataFetcherGenerator$FetcherReadyCallback;
    }
.end annotation


# virtual methods
.method public abstract cancel()V
.end method

.method public abstract startNext()Z
.end method
