.class interface abstract Lcom/bumptech/glide/load/engine/DecodeJob$DiskCacheProvider;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bumptech/glide/load/engine/DecodeJob;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "DiskCacheProvider"
.end annotation


# virtual methods
.method public abstract getDiskCache()Lcom/bumptech/glide/load/engine/cache/DiskCache;
.end method
