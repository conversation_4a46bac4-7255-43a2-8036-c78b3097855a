.class interface abstract Lcom/bumptech/glide/load/engine/bitmap_recycle/LruBitmapPool$BitmapTracker;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bumptech/glide/load/engine/bitmap_recycle/LruBitmapPool;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "BitmapTracker"
.end annotation


# virtual methods
.method public abstract add(Landroid/graphics/Bitmap;)V
.end method

.method public abstract remove(Landroid/graphics/Bitmap;)V
.end method
