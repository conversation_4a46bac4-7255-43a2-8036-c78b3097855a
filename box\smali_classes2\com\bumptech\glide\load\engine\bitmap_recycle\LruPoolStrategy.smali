.class interface abstract Lcom/bumptech/glide/load/engine/bitmap_recycle/LruPoolStrategy;
.super Ljava/lang/Object;


# virtual methods
.method public abstract get(IILandroid/graphics/Bitmap$Config;)Landroid/graphics/Bitmap;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end method

.method public abstract getSize(Landroid/graphics/Bitmap;)I
.end method

.method public abstract logBitmap(IILandroid/graphics/Bitmap$Config;)Ljava/lang/String;
.end method

.method public abstract logBitmap(Landroid/graphics/Bitmap;)Ljava/lang/String;
.end method

.method public abstract put(Landroid/graphics/Bitmap;)V
.end method

.method public abstract removeLast()Landroid/graphics/Bitmap;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end method
