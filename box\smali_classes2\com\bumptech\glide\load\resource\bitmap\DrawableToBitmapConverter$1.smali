.class Lcom/bumptech/glide/load/resource/bitmap/DrawableToBitmapConverter$1;
.super Lcom/bumptech/glide/load/engine/bitmap_recycle/BitmapPoolAdapter;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bumptech/glide/load/resource/bitmap/DrawableToBitmapConverter;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Lcom/bumptech/glide/load/engine/bitmap_recycle/BitmapPoolAdapter;-><init>()V

    return-void
.end method


# virtual methods
.method public put(Landroid/graphics/Bitmap;)V
    .locals 0

    return-void
.end method
