.class interface abstract Lcom/bumptech/glide/load/resource/bitmap/TransformationUtils$DrawRoundedCornerFn;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bumptech/glide/load/resource/bitmap/TransformationUtils;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "DrawRoundedCornerFn"
.end annotation


# virtual methods
.method public abstract drawRoundedCorners(Landroid/graphics/Canvas;Landroid/graphics/Paint;Landroid/graphics/RectF;)V
.end method
