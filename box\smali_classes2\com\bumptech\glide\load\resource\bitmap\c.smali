.class public final synthetic Lcom/bumptech/glide/load/resource/bitmap/c;
.super Ljava/lang/Object;


# direct methods
.method public static bridge synthetic a(Ljava/nio/ByteBuffer;)Landroid/graphics/ImageDecoder$Source;
    .locals 0

    invoke-static {p0}, Landroid/graphics/ImageDecoder;->createSource(Ljava/nio/ByteBuffer;)Landroid/graphics/ImageDecoder$Source;

    move-result-object p0

    return-object p0
.end method
