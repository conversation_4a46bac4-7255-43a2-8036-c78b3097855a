.class public final synthetic Lcom/bumptech/glide/load/resource/d;
.super Ljava/lang/Object;


# direct methods
.method public static bridge synthetic a(Landroid/graphics/ImageDecoder;Landroid/graphics/ImageDecoder$OnPartialImageListener;)V
    .locals 0

    invoke-virtual {p0, p1}, Landroid/graphics/ImageDecoder;->setOnPartialImageListener(Landroid/graphics/ImageDecoder$OnPartialImageListener;)V

    return-void
.end method
