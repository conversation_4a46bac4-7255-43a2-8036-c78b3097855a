.class public final synthetic Lcom/bumptech/glide/load/resource/g;
.super Ljava/lang/Object;


# direct methods
.method public static bridge synthetic a(Landroid/graphics/ImageDecoder$ImageInfo;)Landroid/graphics/ColorSpace;
    .locals 0

    invoke-virtual {p0}, Landroid/graphics/ImageDecoder$ImageInfo;->getColorSpace()Landroid/graphics/ColorSpace;

    move-result-object p0

    return-object p0
.end method
