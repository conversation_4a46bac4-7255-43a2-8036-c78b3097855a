.class public interface abstract Lcom/bumptech/glide/manager/Lifecycle;
.super Ljava/lang/Object;


# virtual methods
.method public abstract addListener(Lcom/bumptech/glide/manager/LifecycleListener;)V
    .param p1    # Lcom/bumptech/glide/manager/LifecycleListener;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
.end method

.method public abstract removeListener(Lcom/bumptech/glide/manager/LifecycleListener;)V
    .param p1    # Lcom/bumptech/glide/manager/LifecycleListener;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
.end method
