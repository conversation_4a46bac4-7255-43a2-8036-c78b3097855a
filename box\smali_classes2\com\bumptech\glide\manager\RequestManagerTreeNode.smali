.class public interface abstract Lcom/bumptech/glide/manager/RequestManagerTreeNode;
.super Ljava/lang/Object;


# virtual methods
.method public abstract getDescendants()Ljava/util/Set;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Set<",
            "Lcom/bumptech/glide/RequestManager;",
            ">;"
        }
    .end annotation
.end method
