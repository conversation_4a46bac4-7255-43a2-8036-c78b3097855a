.class final Lcom/bumptech/glide/repackaged/com/google/common/collect/ByFunctionOrdering;
.super Lcom/bumptech/glide/repackaged/com/google/common/collect/Ordering;

# interfaces
.implements Ljava/io/Serializable;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "<F:",
        "Ljava/lang/Object;",
        "T:",
        "Ljava/lang/Object;",
        ">",
        "Lcom/bumptech/glide/repackaged/com/google/common/collect/Ordering<",
        "TF;>;",
        "Ljava/io/Serializable;"
    }
.end annotation


# instance fields
.field final function:Lcom/bumptech/glide/repackaged/com/google/common/base/Function;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/bumptech/glide/repackaged/com/google/common/base/Function<",
            "TF;+TT;>;"
        }
    .end annotation
.end field

.field final ordering:Lcom/bumptech/glide/repackaged/com/google/common/collect/Ordering;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/bumptech/glide/repackaged/com/google/common/collect/Ordering<",
            "TT;>;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lcom/bumptech/glide/repackaged/com/google/common/base/Function;Lcom/bumptech/glide/repackaged/com/google/common/collect/Ordering;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/bumptech/glide/repackaged/com/google/common/base/Function<",
            "TF;+TT;>;",
            "Lcom/bumptech/glide/repackaged/com/google/common/collect/Ordering<",
            "TT;>;)V"
        }
    .end annotation

    invoke-direct {p0}, Lcom/bumptech/glide/repackaged/com/google/common/collect/Ordering;-><init>()V

    invoke-static {p1}, Lcom/bumptech/glide/repackaged/com/google/common/base/Preconditions;->checkNotNull(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/bumptech/glide/repackaged/com/google/common/base/Function;

    iput-object p1, p0, Lcom/bumptech/glide/repackaged/com/google/common/collect/ByFunctionOrdering;->function:Lcom/bumptech/glide/repackaged/com/google/common/base/Function;

    invoke-static {p2}, Lcom/bumptech/glide/repackaged/com/google/common/base/Preconditions;->checkNotNull(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/bumptech/glide/repackaged/com/google/common/collect/Ordering;

    iput-object p1, p0, Lcom/bumptech/glide/repackaged/com/google/common/collect/ByFunctionOrdering;->ordering:Lcom/bumptech/glide/repackaged/com/google/common/collect/Ordering;

    return-void
.end method


# virtual methods
.method public compare(Ljava/lang/Object;Ljava/lang/Object;)I
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(TF;TF;)I"
        }
    .end annotation

    iget-object v0, p0, Lcom/bumptech/glide/repackaged/com/google/common/collect/ByFunctionOrdering;->ordering:Lcom/bumptech/glide/repackaged/com/google/common/collect/Ordering;

    iget-object v1, p0, Lcom/bumptech/glide/repackaged/com/google/common/collect/ByFunctionOrdering;->function:Lcom/bumptech/glide/repackaged/com/google/common/base/Function;

    invoke-interface {v1, p1}, Lcom/bumptech/glide/repackaged/com/google/common/base/Function;->apply(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    iget-object v1, p0, Lcom/bumptech/glide/repackaged/com/google/common/collect/ByFunctionOrdering;->function:Lcom/bumptech/glide/repackaged/com/google/common/base/Function;

    invoke-interface {v1, p2}, Lcom/bumptech/glide/repackaged/com/google/common/base/Function;->apply(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p2

    invoke-virtual {v0, p1, p2}, Lcom/bumptech/glide/repackaged/com/google/common/collect/Ordering;->compare(Ljava/lang/Object;Ljava/lang/Object;)I

    move-result p1

    return p1
.end method

.method public equals(Ljava/lang/Object;)Z
    .locals 4

    const/4 v0, 0x1

    if-ne p1, p0, :cond_0

    return v0

    :cond_0
    instance-of v1, p1, Lcom/bumptech/glide/repackaged/com/google/common/collect/ByFunctionOrdering;

    const/4 v2, 0x0

    if-eqz v1, :cond_2

    check-cast p1, Lcom/bumptech/glide/repackaged/com/google/common/collect/ByFunctionOrdering;

    iget-object v1, p0, Lcom/bumptech/glide/repackaged/com/google/common/collect/ByFunctionOrdering;->function:Lcom/bumptech/glide/repackaged/com/google/common/base/Function;

    iget-object v3, p1, Lcom/bumptech/glide/repackaged/com/google/common/collect/ByFunctionOrdering;->function:Lcom/bumptech/glide/repackaged/com/google/common/base/Function;

    invoke-interface {v1, v3}, Lcom/bumptech/glide/repackaged/com/google/common/base/Function;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_1

    iget-object v1, p0, Lcom/bumptech/glide/repackaged/com/google/common/collect/ByFunctionOrdering;->ordering:Lcom/bumptech/glide/repackaged/com/google/common/collect/Ordering;

    iget-object p1, p1, Lcom/bumptech/glide/repackaged/com/google/common/collect/ByFunctionOrdering;->ordering:Lcom/bumptech/glide/repackaged/com/google/common/collect/Ordering;

    invoke-virtual {v1, p1}, Ljava/lang/Object;->equals(Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_1

    goto :goto_0

    :cond_1
    const/4 v0, 0x0

    :goto_0
    return v0

    :cond_2
    return v2
.end method

.method public hashCode()I
    .locals 3

    const/4 v0, 0x2

    new-array v0, v0, [Ljava/lang/Object;

    const/4 v1, 0x0

    iget-object v2, p0, Lcom/bumptech/glide/repackaged/com/google/common/collect/ByFunctionOrdering;->function:Lcom/bumptech/glide/repackaged/com/google/common/base/Function;

    aput-object v2, v0, v1

    const/4 v1, 0x1

    iget-object v2, p0, Lcom/bumptech/glide/repackaged/com/google/common/collect/ByFunctionOrdering;->ordering:Lcom/bumptech/glide/repackaged/com/google/common/collect/Ordering;

    aput-object v2, v0, v1

    invoke-static {v0}, Lcom/bumptech/glide/repackaged/com/google/common/base/Objects;->hashCode([Ljava/lang/Object;)I

    move-result v0

    return v0
.end method

.method public toString()Ljava/lang/String;
    .locals 2

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    iget-object v1, p0, Lcom/bumptech/glide/repackaged/com/google/common/collect/ByFunctionOrdering;->ordering:Lcom/bumptech/glide/repackaged/com/google/common/collect/Ordering;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v1, ".onResultOf("

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v1, p0, Lcom/bumptech/glide/repackaged/com/google/common/collect/ByFunctionOrdering;->function:Lcom/bumptech/glide/repackaged/com/google/common/base/Function;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v1, ")"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
