.class public interface abstract Lcom/bumptech/glide/request/Request;
.super Ljava/lang/Object;


# virtual methods
.method public abstract begin()V
.end method

.method public abstract clear()V
.end method

.method public abstract isAnyResourceSet()Z
.end method

.method public abstract isCleared()Z
.end method

.method public abstract isComplete()Z
.end method

.method public abstract isEquivalentTo(Lcom/bumptech/glide/request/Request;)Z
.end method

.method public abstract isRunning()Z
.end method

.method public abstract pause()V
.end method
