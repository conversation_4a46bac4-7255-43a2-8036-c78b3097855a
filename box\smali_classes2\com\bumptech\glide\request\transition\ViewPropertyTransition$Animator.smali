.class public interface abstract Lcom/bumptech/glide/request/transition/ViewPropertyTransition$Animator;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bumptech/glide/request/transition/ViewPropertyTransition;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "Animator"
.end annotation


# virtual methods
.method public abstract animate(Landroid/view/View;)V
.end method
