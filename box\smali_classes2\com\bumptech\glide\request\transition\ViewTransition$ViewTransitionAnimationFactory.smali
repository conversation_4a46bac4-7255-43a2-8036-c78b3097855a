.class interface abstract Lcom/bumptech/glide/request/transition/ViewTransition$ViewTransitionAnimationFactory;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bumptech/glide/request/transition/ViewTransition;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "ViewTransitionAnimationFactory"
.end annotation


# virtual methods
.method public abstract build(Landroid/content/Context;)Landroid/view/animation/Animation;
.end method
