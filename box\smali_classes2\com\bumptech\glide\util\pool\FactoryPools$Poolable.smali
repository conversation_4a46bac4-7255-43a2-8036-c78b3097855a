.class public interface abstract Lcom/bumptech/glide/util/pool/FactoryPools$Poolable;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bumptech/glide/util/pool/FactoryPools;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "Poolable"
.end annotation


# virtual methods
.method public abstract getVerifier()Lcom/bumptech/glide/util/pool/StateVerifier;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end method
