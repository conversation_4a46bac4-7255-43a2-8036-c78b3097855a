.class public Lcom/bykv/vk/openvk/component/video/Fj/Fj;
.super Ljava/lang/Object;


# static fields
.field public static Fj:I = 0xa

.field private static Ubf:Lcom/bykv/vk/openvk/component/video/api/Fj/ex; = null

.field public static eV:I = 0xa

.field public static ex:I = 0xa

.field public static hjc:I = 0xa


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public static Fj()V
    .locals 1

    sget-object v0, Lcom/bykv/vk/openvk/component/video/Fj/Fj;->Ubf:Lcom/bykv/vk/openvk/component/video/api/Fj/ex;

    if-eqz v0, :cond_0

    invoke-interface {v0}, Lcom/bykv/vk/openvk/component/video/api/Fj/ex;->eV()V

    :cond_0
    return-void
.end method

.method public static Fj(Landroid/content/Context;)V
    .locals 1

    invoke-static {p0}, Lcom/bykv/vk/openvk/component/video/api/WR/Fj;->Fj(Landroid/content/Context;)V

    sget p0, Landroid/os/Build$VERSION;->SDK_INT:I

    const/16 v0, 0x17

    if-ge p0, v0, :cond_0

    invoke-static {}, Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj;->Fj()Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj;

    :cond_0
    return-void
.end method

.method public static Fj(Lcom/bykv/vk/openvk/component/video/api/Fj/ex;)V
    .locals 0

    sput-object p0, Lcom/bykv/vk/openvk/component/video/Fj/Fj;->Ubf:Lcom/bykv/vk/openvk/component/video/api/Fj/ex;

    return-void
.end method

.method public static Fj(Lorg/json/JSONObject;)V
    .locals 2

    if-nez p0, :cond_0

    return-void

    :cond_0
    :try_start_0
    const-string v0, "splash"

    const/16 v1, 0xa

    invoke-virtual {p0, v0, v1}, Lorg/json/JSONObject;->optInt(Ljava/lang/String;I)I

    move-result v0

    sput v0, Lcom/bykv/vk/openvk/component/video/Fj/Fj;->Fj:I

    const-string v0, "reward"

    invoke-virtual {p0, v0, v1}, Lorg/json/JSONObject;->optInt(Ljava/lang/String;I)I

    move-result v0

    sput v0, Lcom/bykv/vk/openvk/component/video/Fj/Fj;->ex:I

    const-string v0, "brand"

    invoke-virtual {p0, v0, v1}, Lorg/json/JSONObject;->optInt(Ljava/lang/String;I)I

    move-result v0

    sput v0, Lcom/bykv/vk/openvk/component/video/Fj/Fj;->hjc:I

    const-string v0, "other"

    invoke-virtual {p0, v0, v1}, Lorg/json/JSONObject;->optInt(Ljava/lang/String;I)I

    move-result p0

    sput p0, Lcom/bykv/vk/openvk/component/video/Fj/Fj;->eV:I

    sget v0, Lcom/bykv/vk/openvk/component/video/Fj/Fj;->Fj:I

    if-gez v0, :cond_1

    sput v1, Lcom/bykv/vk/openvk/component/video/Fj/Fj;->Fj:I

    goto :goto_0

    :catchall_0
    move-exception p0

    goto :goto_1

    :cond_1
    :goto_0
    sget v0, Lcom/bykv/vk/openvk/component/video/Fj/Fj;->ex:I

    if-gez v0, :cond_2

    sput v1, Lcom/bykv/vk/openvk/component/video/Fj/Fj;->ex:I

    :cond_2
    sget v0, Lcom/bykv/vk/openvk/component/video/Fj/Fj;->hjc:I

    if-gez v0, :cond_3

    sput v1, Lcom/bykv/vk/openvk/component/video/Fj/Fj;->hjc:I

    :cond_3
    if-gez p0, :cond_4

    sput v1, Lcom/bykv/vk/openvk/component/video/Fj/Fj;->eV:I
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    :cond_4
    return-void

    :goto_1
    invoke-virtual {p0}, Ljava/lang/Throwable;->getMessage()Ljava/lang/String;

    return-void
.end method

.method public static Ubf()I
    .locals 1

    sget v0, Lcom/bykv/vk/openvk/component/video/Fj/Fj;->eV:I

    return v0
.end method

.method public static eV()I
    .locals 1

    sget v0, Lcom/bykv/vk/openvk/component/video/Fj/Fj;->hjc:I

    return v0
.end method

.method public static ex()I
    .locals 1

    sget v0, Lcom/bykv/vk/openvk/component/video/Fj/Fj;->Fj:I

    return v0
.end method

.method public static hjc()I
    .locals 1

    sget v0, Lcom/bykv/vk/openvk/component/video/Fj/Fj;->ex:I

    return v0
.end method
