.class public Lcom/bykv/vk/openvk/component/video/Fj/Fj/ex/Fj;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bykv/vk/openvk/component/video/api/Ubf/Fj;


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public Fj(Landroid/content/Context;Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;Lcom/bykv/vk/openvk/component/video/api/Ubf/Fj$Fj;)V
    .locals 0

    invoke-static {p1, p2, p3}, Lcom/bykv/vk/openvk/component/video/Fj/Fj/ex/hjc;->Fj(Landroid/content/Context;Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;Lcom/bykv/vk/openvk/component/video/api/Ubf/Fj$Fj;)V

    invoke-static {}, Lcom/bykv/vk/openvk/component/video/Fj/Fj;->Fj()V

    return-void
.end method
