.class public Lcom/bykv/vk/openvk/component/video/Fj/Fj/ex/ex;
.super Ljava/lang/Object;


# instance fields
.field private Fj:Landroid/content/Context;

.field private Ubf:Ljava/io/File;

.field private final WR:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lcom/bykv/vk/openvk/component/video/api/Ubf/Fj$Fj;",
            ">;"
        }
    .end annotation
.end field

.field private eV:Ljava/io/File;

.field private ex:Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;

.field private volatile hjc:Z

.field private volatile svN:Z


# direct methods
.method public constructor <init>(Landroid/content/Context;Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;)V
    .locals 2

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, 0x0

    iput-boolean v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/Fj/ex/ex;->hjc:Z

    const/4 v1, 0x0

    iput-object v1, p0, Lcom/bykv/vk/openvk/component/video/Fj/Fj/ex/ex;->eV:Ljava/io/File;

    iput-object v1, p0, Lcom/bykv/vk/openvk/component/video/Fj/Fj/ex/ex;->Ubf:Ljava/io/File;

    new-instance v1, Ljava/util/ArrayList;

    invoke-direct {v1}, Ljava/util/ArrayList;-><init>()V

    iput-object v1, p0, Lcom/bykv/vk/openvk/component/video/Fj/Fj/ex/ex;->WR:Ljava/util/List;

    iput-boolean v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/Fj/ex/ex;->svN:Z

    iput-object p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/Fj/ex/ex;->Fj:Landroid/content/Context;

    iput-object p2, p0, Lcom/bykv/vk/openvk/component/video/Fj/Fj/ex/ex;->ex:Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;

    invoke-virtual {p2}, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->ex()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p2}, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->Tc()Ljava/lang/String;

    move-result-object v0

    invoke-static {p1, v0}, Lcom/bykv/vk/openvk/component/video/Fj/Ubf/ex;->ex(Ljava/lang/String;Ljava/lang/String;)Ljava/io/File;

    move-result-object p1

    iput-object p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/Fj/ex/ex;->eV:Ljava/io/File;

    invoke-virtual {p2}, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->ex()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p2}, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->Tc()Ljava/lang/String;

    move-result-object p2

    invoke-static {p1, p2}, Lcom/bykv/vk/openvk/component/video/Fj/Ubf/ex;->hjc(Ljava/lang/String;Ljava/lang/String;)Ljava/io/File;

    move-result-object p1

    iput-object p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/Fj/ex/ex;->Ubf:Ljava/io/File;

    return-void
.end method

.method public static synthetic Fj(Lcom/bykv/vk/openvk/component/video/Fj/Fj/ex/ex;)Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;
    .locals 0

    iget-object p0, p0, Lcom/bykv/vk/openvk/component/video/Fj/Fj/ex/ex;->ex:Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;

    return-object p0
.end method

.method public static synthetic Fj(Lcom/bykv/vk/openvk/component/video/Fj/Fj/ex/ex;Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;I)V
    .locals 0

    invoke-direct {p0, p1, p2}, Lcom/bykv/vk/openvk/component/video/Fj/Fj/ex/ex;->ex(Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;I)V

    return-void
.end method

.method public static synthetic Fj(Lcom/bykv/vk/openvk/component/video/Fj/Fj/ex/ex;Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;ILjava/lang/String;)V
    .locals 0

    invoke-direct {p0, p1, p2, p3}, Lcom/bykv/vk/openvk/component/video/Fj/Fj/ex/ex;->Fj(Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;ILjava/lang/String;)V

    return-void
.end method

.method public static synthetic Fj(Lcom/bykv/vk/openvk/component/video/Fj/Fj/ex/ex;Ljava/io/Closeable;)V
    .locals 0

    invoke-direct {p0, p1}, Lcom/bykv/vk/openvk/component/video/Fj/Fj/ex/ex;->Fj(Ljava/io/Closeable;)V

    return-void
.end method

.method private Fj(Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;I)V
    .locals 3

    const-class v0, Lcom/bykv/vk/openvk/component/video/api/Ubf/Fj$Fj;

    monitor-enter v0

    :try_start_0
    iget-object v1, p0, Lcom/bykv/vk/openvk/component/video/Fj/Fj/ex/ex;->WR:Ljava/util/List;

    invoke-interface {v1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :cond_0
    :goto_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_1

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/bykv/vk/openvk/component/video/api/Ubf/Fj$Fj;

    if-eqz v2, :cond_0

    invoke-interface {v2, p1, p2}, Lcom/bykv/vk/openvk/component/video/api/Ubf/Fj$Fj;->Fj(Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;I)V

    goto :goto_0

    :catchall_0
    move-exception p1

    goto :goto_1

    :cond_1
    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    return-void

    :goto_1
    monitor-exit v0

    throw p1
.end method

.method private Fj(Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;ILjava/lang/String;)V
    .locals 3

    const-class v0, Lcom/bykv/vk/openvk/component/video/api/Ubf/Fj$Fj;

    monitor-enter v0

    :try_start_0
    iget-object v1, p0, Lcom/bykv/vk/openvk/component/video/Fj/Fj/ex/ex;->WR:Ljava/util/List;

    invoke-interface {v1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :cond_0
    :goto_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_1

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/bykv/vk/openvk/component/video/api/Ubf/Fj$Fj;

    if-eqz v2, :cond_0

    invoke-interface {v2, p1, p2, p3}, Lcom/bykv/vk/openvk/component/video/api/Ubf/Fj$Fj;->Fj(Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;ILjava/lang/String;)V

    goto :goto_0

    :catchall_0
    move-exception p1

    goto :goto_1

    :cond_1
    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    return-void

    :goto_1
    monitor-exit v0

    throw p1
.end method

.method private Fj(Ljava/io/Closeable;)V
    .locals 0

    if-eqz p1, :cond_0

    :try_start_0
    invoke-interface {p1}, Ljava/io/Closeable;->close()V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    :catch_0
    :cond_0
    return-void
.end method

.method private Ubf()V
    .locals 3

    :try_start_0
    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/Fj/ex/ex;->eV:Ljava/io/File;

    iget-object v1, p0, Lcom/bykv/vk/openvk/component/video/Fj/Fj/ex/ex;->Ubf:Ljava/io/File;

    invoke-virtual {v0, v1}, Ljava/io/File;->renameTo(Ljava/io/File;)Z

    move-result v0

    if-eqz v0, :cond_0

    return-void

    :cond_0
    new-instance v0, Ljava/io/IOException;

    new-instance v1, Ljava/lang/StringBuilder;

    const-string v2, "Error renaming file "

    invoke-direct {v1, v2}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    iget-object v2, p0, Lcom/bykv/vk/openvk/component/video/Fj/Fj/ex/ex;->eV:Ljava/io/File;

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v2, " to "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v2, p0, Lcom/bykv/vk/openvk/component/video/Fj/Fj/ex/ex;->Ubf:Ljava/io/File;

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v2, " for completion!"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-direct {v0, v1}, Ljava/io/IOException;-><init>(Ljava/lang/String;)V

    throw v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    :catchall_0
    move-exception v0

    invoke-virtual {v0}, Ljava/lang/Throwable;->getMessage()Ljava/lang/String;

    return-void
.end method

.method public static synthetic Ubf(Lcom/bykv/vk/openvk/component/video/Fj/Fj/ex/ex;)V
    .locals 0

    invoke-direct {p0}, Lcom/bykv/vk/openvk/component/video/Fj/Fj/ex/ex;->eV()V

    return-void
.end method

.method private eV()V
    .locals 1

    :try_start_0
    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/Fj/ex/ex;->Ubf:Ljava/io/File;

    invoke-virtual {v0}, Ljava/io/File;->delete()Z

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/Fj/ex/ex;->eV:Ljava/io/File;

    invoke-virtual {v0}, Ljava/io/File;->delete()Z
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    :catchall_0
    return-void
.end method

.method public static synthetic eV(Lcom/bykv/vk/openvk/component/video/Fj/Fj/ex/ex;)V
    .locals 0

    invoke-direct {p0}, Lcom/bykv/vk/openvk/component/video/Fj/Fj/ex/ex;->Ubf()V

    return-void
.end method

.method public static synthetic ex(Lcom/bykv/vk/openvk/component/video/Fj/Fj/ex/ex;)Ljava/io/File;
    .locals 0

    iget-object p0, p0, Lcom/bykv/vk/openvk/component/video/Fj/Fj/ex/ex;->eV:Ljava/io/File;

    return-object p0
.end method

.method public static synthetic ex(Lcom/bykv/vk/openvk/component/video/Fj/Fj/ex/ex;Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;I)V
    .locals 0

    invoke-direct {p0, p1, p2}, Lcom/bykv/vk/openvk/component/video/Fj/Fj/ex/ex;->Fj(Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;I)V

    return-void
.end method

.method private ex(Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;I)V
    .locals 3

    const-class v0, Lcom/bykv/vk/openvk/component/video/api/Ubf/Fj$Fj;

    monitor-enter v0

    :try_start_0
    iget-object v1, p0, Lcom/bykv/vk/openvk/component/video/Fj/Fj/ex/ex;->WR:Ljava/util/List;

    invoke-interface {v1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :cond_0
    :goto_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_1

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/bykv/vk/openvk/component/video/api/Ubf/Fj$Fj;

    if-eqz v2, :cond_0

    invoke-interface {v2, p1, p2}, Lcom/bykv/vk/openvk/component/video/api/Ubf/Fj$Fj;->ex(Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;I)V

    goto :goto_0

    :catchall_0
    move-exception p1

    goto :goto_1

    :cond_1
    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    return-void

    :goto_1
    monitor-exit v0

    throw p1
.end method

.method private ex()Z
    .locals 7

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/Fj/ex/ex;->Ubf:Ljava/io/File;

    invoke-virtual {v0}, Ljava/io/File;->exists()Z

    move-result v0

    const/4 v1, 0x1

    if-eqz v0, :cond_0

    return v1

    :cond_0
    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/Fj/ex/ex;->ex:Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;

    invoke-virtual {v0}, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->Ko()Z

    move-result v0

    const/4 v2, 0x0

    if-nez v0, :cond_2

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/Fj/ex/ex;->eV:Ljava/io/File;

    invoke-virtual {v0}, Ljava/io/File;->length()J

    move-result-wide v3

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/Fj/ex/ex;->ex:Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;

    invoke-virtual {v0}, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->hjc()I

    move-result v0

    int-to-long v5, v0

    cmp-long v0, v3, v5

    if-ltz v0, :cond_1

    return v1

    :cond_1
    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/Fj/ex/ex;->ex:Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;

    invoke-virtual {v0}, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->Fj()I

    move-result v0

    if-lez v0, :cond_2

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/Fj/ex/ex;->eV:Ljava/io/File;

    invoke-virtual {v0}, Ljava/io/File;->length()J

    move-result-wide v3

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/Fj/ex/ex;->ex:Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;

    invoke-virtual {v0}, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->Fj()I

    move-result v0

    int-to-long v5, v0

    cmp-long v0, v3, v5

    if-ltz v0, :cond_2

    return v1

    :cond_2
    return v2
.end method

.method private hjc()V
    .locals 12

    invoke-static {}, Lcom/bykv/vk/openvk/component/video/api/hjc;->eV()Lcom/bytedance/sdk/component/ex/Fj/rAx;

    move-result-object v0

    if-eqz v0, :cond_0

    invoke-static {}, Lcom/bykv/vk/openvk/component/video/api/hjc;->eV()Lcom/bytedance/sdk/component/ex/Fj/rAx;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/ex/Fj/rAx;->ex()Lcom/bytedance/sdk/component/ex/Fj/rAx$Fj;

    move-result-object v0

    goto :goto_0

    :cond_0
    new-instance v0, Lcom/bytedance/sdk/component/ex/Fj/rAx$Fj;

    const-string v1, "v_preload"

    invoke-direct {v0, v1}, Lcom/bytedance/sdk/component/ex/Fj/rAx$Fj;-><init>(Ljava/lang/String;)V

    :goto_0
    iget-object v1, p0, Lcom/bykv/vk/openvk/component/video/Fj/Fj/ex/ex;->ex:Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;

    invoke-virtual {v1}, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->JU()I

    move-result v1

    int-to-long v1, v1

    sget-object v3, Ljava/util/concurrent/TimeUnit;->MILLISECONDS:Ljava/util/concurrent/TimeUnit;

    invoke-virtual {v0, v1, v2, v3}, Lcom/bytedance/sdk/component/ex/Fj/rAx$Fj;->Fj(JLjava/util/concurrent/TimeUnit;)Lcom/bytedance/sdk/component/ex/Fj/rAx$Fj;

    move-result-object v1

    iget-object v2, p0, Lcom/bykv/vk/openvk/component/video/Fj/Fj/ex/ex;->ex:Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;

    invoke-virtual {v2}, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->Ql()I

    move-result v2

    int-to-long v4, v2

    invoke-virtual {v1, v4, v5, v3}, Lcom/bytedance/sdk/component/ex/Fj/rAx$Fj;->ex(JLjava/util/concurrent/TimeUnit;)Lcom/bytedance/sdk/component/ex/Fj/rAx$Fj;

    move-result-object v1

    iget-object v2, p0, Lcom/bykv/vk/openvk/component/video/Fj/Fj/ex/ex;->ex:Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;

    invoke-virtual {v2}, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->rS()I

    move-result v2

    int-to-long v4, v2

    invoke-virtual {v1, v4, v5, v3}, Lcom/bytedance/sdk/component/ex/Fj/rAx$Fj;->hjc(JLjava/util/concurrent/TimeUnit;)Lcom/bytedance/sdk/component/ex/Fj/rAx$Fj;

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/ex/Fj/rAx$Fj;->Fj()Lcom/bytedance/sdk/component/ex/Fj/rAx;

    move-result-object v0

    new-instance v1, Lcom/bytedance/sdk/component/ex/Fj/dG$Fj;

    invoke-direct {v1}, Lcom/bytedance/sdk/component/ex/Fj/dG$Fj;-><init>()V

    iget-object v2, p0, Lcom/bykv/vk/openvk/component/video/Fj/Fj/ex/ex;->eV:Ljava/io/File;

    invoke-virtual {v2}, Ljava/io/File;->length()J

    move-result-wide v2

    iget-object v4, p0, Lcom/bykv/vk/openvk/component/video/Fj/Fj/ex/ex;->ex:Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;

    invoke-virtual {v4}, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->hjc()I

    move-result v4

    iget-object v5, p0, Lcom/bykv/vk/openvk/component/video/Fj/Fj/ex/ex;->ex:Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;

    invoke-virtual {v5}, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->Ko()Z

    move-result v5

    iget-object v6, p0, Lcom/bykv/vk/openvk/component/video/Fj/Fj/ex/ex;->ex:Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;

    invoke-virtual {v6}, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->Fj()I

    move-result v6

    if-lez v6, :cond_2

    int-to-long v7, v6

    iget-object v9, p0, Lcom/bykv/vk/openvk/component/video/Fj/Fj/ex/ex;->ex:Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;

    invoke-virtual {v9}, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->mSE()J

    move-result-wide v9

    cmp-long v11, v7, v9

    if-ltz v11, :cond_1

    const/4 v5, 0x1

    goto :goto_1

    :cond_1
    move v4, v6

    :cond_2
    :goto_1
    const-string v6, "-"

    const-string v7, "bytes="

    const-string v8, "RANGE"

    if-eqz v5, :cond_3

    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4, v7}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    invoke-virtual {v4, v2, v3}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    invoke-virtual {v4, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v1, v8, v4}, Lcom/bytedance/sdk/component/ex/Fj/dG$Fj;->Fj(Ljava/lang/String;Ljava/lang/String;)Lcom/bytedance/sdk/component/ex/Fj/dG$Fj;

    move-result-object v4

    iget-object v5, p0, Lcom/bykv/vk/openvk/component/video/Fj/Fj/ex/ex;->ex:Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;

    invoke-virtual {v5}, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->dG()Ljava/lang/String;

    move-result-object v5

    invoke-virtual {v4, v5}, Lcom/bytedance/sdk/component/ex/Fj/dG$Fj;->Fj(Ljava/lang/String;)Lcom/bytedance/sdk/component/ex/Fj/dG$Fj;

    move-result-object v4

    invoke-virtual {v4}, Lcom/bytedance/sdk/component/ex/Fj/dG$Fj;->Fj()Lcom/bytedance/sdk/component/ex/Fj/dG$Fj;

    move-result-object v4

    invoke-virtual {v4}, Lcom/bytedance/sdk/component/ex/Fj/dG$Fj;->ex()Lcom/bytedance/sdk/component/ex/Fj/dG;

    goto :goto_2

    :cond_3
    new-instance v5, Ljava/lang/StringBuilder;

    invoke-direct {v5, v7}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    invoke-virtual {v5, v2, v3}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    invoke-virtual {v5, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v5, v4}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v5}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v1, v8, v4}, Lcom/bytedance/sdk/component/ex/Fj/dG$Fj;->Fj(Ljava/lang/String;Ljava/lang/String;)Lcom/bytedance/sdk/component/ex/Fj/dG$Fj;

    move-result-object v4

    iget-object v5, p0, Lcom/bykv/vk/openvk/component/video/Fj/Fj/ex/ex;->ex:Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;

    invoke-virtual {v5}, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->dG()Ljava/lang/String;

    move-result-object v5

    invoke-virtual {v4, v5}, Lcom/bytedance/sdk/component/ex/Fj/dG$Fj;->Fj(Ljava/lang/String;)Lcom/bytedance/sdk/component/ex/Fj/dG$Fj;

    move-result-object v4

    invoke-virtual {v4}, Lcom/bytedance/sdk/component/ex/Fj/dG$Fj;->Fj()Lcom/bytedance/sdk/component/ex/Fj/dG$Fj;

    move-result-object v4

    invoke-virtual {v4}, Lcom/bytedance/sdk/component/ex/Fj/dG$Fj;->ex()Lcom/bytedance/sdk/component/ex/Fj/dG;

    :goto_2
    invoke-virtual {v1}, Lcom/bytedance/sdk/component/ex/Fj/dG$Fj;->ex()Lcom/bytedance/sdk/component/ex/Fj/dG;

    move-result-object v1

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/component/ex/Fj/rAx;->Fj(Lcom/bytedance/sdk/component/ex/Fj/dG;)Lcom/bytedance/sdk/component/ex/Fj/ex;

    move-result-object v0

    new-instance v1, Lcom/bykv/vk/openvk/component/video/Fj/Fj/ex/ex$1;

    invoke-direct {v1, p0, v2, v3}, Lcom/bykv/vk/openvk/component/video/Fj/Fj/ex/ex$1;-><init>(Lcom/bykv/vk/openvk/component/video/Fj/Fj/ex/ex;J)V

    invoke-interface {v0, v1}, Lcom/bytedance/sdk/component/ex/Fj/ex;->Fj(Lcom/bytedance/sdk/component/ex/Fj/hjc;)V

    return-void
.end method

.method public static synthetic hjc(Lcom/bykv/vk/openvk/component/video/Fj/Fj/ex/ex;)Z
    .locals 0

    iget-boolean p0, p0, Lcom/bykv/vk/openvk/component/video/Fj/Fj/ex/ex;->hjc:Z

    return p0
.end method


# virtual methods
.method public Fj()Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;
    .locals 1

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/Fj/ex/ex;->ex:Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;

    return-object v0
.end method

.method public Fj(Lcom/bykv/vk/openvk/component/video/api/Ubf/Fj$Fj;)V
    .locals 2

    iget-boolean v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/Fj/ex/ex;->svN:Z

    if-eqz v0, :cond_0

    const-class v0, Lcom/bykv/vk/openvk/component/video/api/Ubf/Fj$Fj;

    monitor-enter v0

    :try_start_0
    iget-object v1, p0, Lcom/bykv/vk/openvk/component/video/Fj/Fj/ex/ex;->WR:Ljava/util/List;

    invoke-interface {v1, p1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    return-void

    :catchall_0
    move-exception p1

    monitor-exit v0

    throw p1

    :cond_0
    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/Fj/ex/ex;->WR:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    invoke-direct {p0}, Lcom/bykv/vk/openvk/component/video/Fj/Fj/ex/ex;->ex()Z

    move-result p1

    const/4 v0, 0x1

    if-eqz p1, :cond_1

    iget-object p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/Fj/ex/ex;->ex:Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;

    invoke-virtual {p1, v0}, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->svN(I)V

    iget-object p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/Fj/ex/ex;->ex:Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;

    const/16 v0, 0xc8

    invoke-direct {p0, p1, v0}, Lcom/bykv/vk/openvk/component/video/Fj/Fj/ex/ex;->Fj(Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;I)V

    iget-object p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/Fj/ex/ex;->ex:Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;

    invoke-static {p1}, Lcom/bykv/vk/openvk/component/video/Fj/Fj/ex/hjc;->Fj(Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;)V

    return-void

    :cond_1
    iput-boolean v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/Fj/ex/ex;->svN:Z

    iget-object p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/Fj/ex/ex;->ex:Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;

    const/4 v0, 0x0

    invoke-virtual {p1, v0}, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->svN(I)V

    invoke-direct {p0}, Lcom/bykv/vk/openvk/component/video/Fj/Fj/ex/ex;->hjc()V

    return-void
.end method

.method public Fj(Z)V
    .locals 0

    iput-boolean p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/Fj/ex/ex;->hjc:Z

    return-void
.end method
