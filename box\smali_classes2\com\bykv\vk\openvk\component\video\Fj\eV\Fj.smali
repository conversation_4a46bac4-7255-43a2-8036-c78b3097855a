.class public abstract Lcom/bykv/vk/openvk/component/video/Fj/eV/Fj;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;


# instance fields
.field private BcC:Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc$eV;

.field protected Fj:Z

.field private Ubf:Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc$WR;

.field private WR:Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc$svN;

.field private eV:Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc$Fj;

.field private ex:Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc$Ubf;

.field private hjc:Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc$ex;

.field private svN:Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc$hjc;


# direct methods
.method public constructor <init>()V
    .locals 1

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, 0x0

    iput-boolean v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/Fj;->Fj:Z

    return-void
.end method


# virtual methods
.method public Fj()V
    .locals 1

    const/4 v0, 0x0

    iput-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/Fj;->ex:Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc$Ubf;

    iput-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/Fj;->eV:Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc$Fj;

    iput-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/Fj;->hjc:Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc$ex;

    iput-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/Fj;->Ubf:Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc$WR;

    iput-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/Fj;->WR:Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc$svN;

    iput-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/Fj;->svN:Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc$hjc;

    iput-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/Fj;->BcC:Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc$eV;

    return-void
.end method

.method public final Fj(I)V
    .locals 1

    :try_start_0
    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/Fj;->eV:Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc$Fj;

    if-eqz v0, :cond_0

    invoke-interface {v0, p0, p1}, Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc$Fj;->Fj(Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;I)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    :catchall_0
    :cond_0
    return-void
.end method

.method public final Fj(IIII)V
    .locals 6

    :try_start_0
    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/Fj;->WR:Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc$svN;

    if-eqz v0, :cond_0

    move-object v1, p0

    move v2, p1

    move v3, p2

    move v4, p3

    move v5, p4

    invoke-interface/range {v0 .. v5}, Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc$svN;->Fj(Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;IIII)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    :catchall_0
    :cond_0
    return-void
.end method

.method public final Fj(Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc$Fj;)V
    .locals 0

    iput-object p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/Fj;->eV:Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc$Fj;

    return-void
.end method

.method public final Fj(Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc$Ubf;)V
    .locals 0

    iput-object p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/Fj;->ex:Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc$Ubf;

    return-void
.end method

.method public final Fj(Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc$WR;)V
    .locals 0

    iput-object p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/Fj;->Ubf:Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc$WR;

    return-void
.end method

.method public final Fj(Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc$eV;)V
    .locals 0

    iput-object p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/Fj;->BcC:Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc$eV;

    return-void
.end method

.method public final Fj(Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc$ex;)V
    .locals 0

    iput-object p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/Fj;->hjc:Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc$ex;

    return-void
.end method

.method public final Fj(Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc$hjc;)V
    .locals 0

    iput-object p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/Fj;->svN:Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc$hjc;

    return-void
.end method

.method public final Fj(Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc$svN;)V
    .locals 0

    iput-object p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/Fj;->WR:Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc$svN;

    return-void
.end method

.method public Fj(Z)V
    .locals 0

    iput-boolean p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/Fj;->Fj:Z

    return-void
.end method

.method public final Fj(II)Z
    .locals 2

    const/4 v0, 0x0

    :try_start_0
    iget-object v1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/Fj;->svN:Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc$hjc;

    if-eqz v1, :cond_0

    invoke-interface {v1, p0, p1, p2}, Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc$hjc;->Fj(Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;II)Z

    move-result p1
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    if-eqz p1, :cond_0

    const/4 p1, 0x1

    return p1

    :catchall_0
    :cond_0
    return v0
.end method

.method public final eV()V
    .locals 1

    :try_start_0
    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/Fj;->Ubf:Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc$WR;

    if-eqz v0, :cond_0

    invoke-interface {v0, p0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc$WR;->hjc(Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    :catchall_0
    :cond_0
    return-void
.end method

.method public final ex()V
    .locals 1

    :try_start_0
    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/Fj;->ex:Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc$Ubf;

    if-eqz v0, :cond_0

    invoke-interface {v0, p0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc$Ubf;->ex(Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    :catchall_0
    :cond_0
    return-void
.end method

.method public final ex(II)Z
    .locals 2

    const/4 v0, 0x0

    :try_start_0
    iget-object v1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/Fj;->BcC:Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc$eV;

    if-eqz v1, :cond_0

    invoke-interface {v1, p0, p1, p2}, Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc$eV;->ex(Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;II)Z

    move-result p1
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    if-eqz p1, :cond_0

    const/4 p1, 0x1

    return p1

    :catchall_0
    :cond_0
    return v0
.end method

.method public final hjc()V
    .locals 1

    :try_start_0
    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/Fj;->hjc:Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc$ex;

    if-eqz v0, :cond_0

    invoke-interface {v0, p0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc$ex;->Fj(Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    :catchall_0
    :cond_0
    return-void
.end method
