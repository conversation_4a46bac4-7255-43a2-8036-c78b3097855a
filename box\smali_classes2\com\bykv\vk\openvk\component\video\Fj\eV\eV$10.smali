.class Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$10;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->Af()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;


# direct methods
.method public constructor <init>(Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;)V
    .locals 0

    iput-object p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$10;->Fj:Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 2

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$10;->Fj:Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;

    invoke-static {v0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->Fj(Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;)Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;

    move-result-object v0

    if-nez v0, :cond_1

    :try_start_0
    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$10;->Fj:Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;

    new-instance v1, Lcom/bykv/vk/openvk/component/video/Fj/eV/ex;

    invoke-direct {v1}, Lcom/bykv/vk/openvk/component/video/Fj/eV/ex;-><init>()V

    invoke-static {v0, v1}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->Fj(Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;)Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    goto :goto_0

    :catchall_0
    move-exception v0

    invoke-virtual {v0}, Ljava/lang/Throwable;->getMessage()Ljava/lang/String;

    :goto_0
    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$10;->Fj:Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;

    invoke-static {v0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->Fj(Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;)Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;

    move-result-object v0

    if-nez v0, :cond_0

    return-void

    :cond_0
    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$10;->Fj:Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;

    invoke-static {v0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->Fj(Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;)Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$10;->Fj:Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;

    const-string v1, "0"

    invoke-static {v0, v1}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->Fj(Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;Ljava/lang/String;)Ljava/lang/String;

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$10;->Fj:Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;

    invoke-static {v0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->Fj(Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;)Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;

    move-result-object v0

    iget-object v1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$10;->Fj:Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;

    invoke-interface {v0, v1}, Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;->Fj(Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc$Ubf;)V

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$10;->Fj:Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;

    invoke-static {v0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->Fj(Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;)Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;

    move-result-object v0

    iget-object v1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$10;->Fj:Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;

    invoke-interface {v0, v1}, Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;->Fj(Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc$ex;)V

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$10;->Fj:Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;

    invoke-static {v0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->Fj(Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;)Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;

    move-result-object v0

    iget-object v1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$10;->Fj:Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;

    invoke-interface {v0, v1}, Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;->Fj(Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc$hjc;)V

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$10;->Fj:Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;

    invoke-static {v0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->Fj(Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;)Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;

    move-result-object v0

    iget-object v1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$10;->Fj:Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;

    invoke-interface {v0, v1}, Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;->Fj(Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc$Fj;)V

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$10;->Fj:Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;

    invoke-static {v0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->Fj(Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;)Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;

    move-result-object v0

    iget-object v1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$10;->Fj:Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;

    invoke-interface {v0, v1}, Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;->Fj(Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc$WR;)V

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$10;->Fj:Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;

    invoke-static {v0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->Fj(Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;)Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;

    move-result-object v0

    iget-object v1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$10;->Fj:Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;

    invoke-interface {v0, v1}, Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;->Fj(Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc$eV;)V

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$10;->Fj:Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;

    invoke-static {v0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->Fj(Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;)Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;

    move-result-object v0

    iget-object v1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$10;->Fj:Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;

    invoke-interface {v0, v1}, Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;->Fj(Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc$svN;)V

    const/4 v0, 0x0

    :try_start_1
    iget-object v1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$10;->Fj:Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;

    invoke-static {v1}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->Fj(Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;)Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;

    move-result-object v1

    invoke-interface {v1, v0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;->hjc(Z)V
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_1

    :catchall_1
    iget-object v1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$10;->Fj:Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;

    invoke-static {v1, v0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->ex(Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;Z)Z

    :cond_1
    return-void
.end method
