.class Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$11;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->Ko()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;


# direct methods
.method public constructor <init>(Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;)V
    .locals 0

    iput-object p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$11;->Fj:Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 3

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$11;->Fj:Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;

    invoke-virtual {v0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->svN()Z

    move-result v0

    if-eqz v0, :cond_2

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$11;->Fj:Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;

    invoke-static {v0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->Fj(Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;)Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;

    move-result-object v0

    if-eqz v0, :cond_2

    :try_start_0
    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$11;->Fj:Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;

    invoke-static {v0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->Fj(Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;)Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;

    move-result-object v0

    invoke-interface {v0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;->Ubf()V

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$11;->Fj:Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;

    invoke-static {v0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->mSE(Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;)Ljava/util/List;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :cond_0
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_1

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/lang/ref/WeakReference;

    if-eqz v1, :cond_0

    invoke-virtual {v1}, Ljava/lang/ref/Reference;->get()Ljava/lang/Object;

    move-result-object v2

    if-eqz v2, :cond_0

    invoke-virtual {v1}, Ljava/lang/ref/Reference;->get()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/bykv/vk/openvk/component/video/api/Fj$Fj;

    iget-object v2, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$11;->Fj:Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;

    invoke-interface {v1, v2}, Lcom/bykv/vk/openvk/component/video/api/Fj$Fj;->Ubf(Lcom/bykv/vk/openvk/component/video/api/Fj;)V

    goto :goto_0

    :catchall_0
    move-exception v0

    goto :goto_1

    :cond_1
    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$11;->Fj:Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;

    const/16 v1, 0xce

    invoke-static {v0, v1}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->Fj(Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;I)I
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    return-void

    :goto_1
    invoke-virtual {v0}, Ljava/lang/Throwable;->getMessage()Ljava/lang/String;

    :cond_2
    return-void
.end method
