.class Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$12;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->rAx()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;


# direct methods
.method public constructor <init>(Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;)V
    .locals 0

    iput-object p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$12;->Fj:Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 2

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$12;->Fj:Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;

    invoke-static {v0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->BcC(Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;)Lcom/bytedance/sdk/component/utils/Vq;

    move-result-object v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$12;->Fj:Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;

    invoke-static {v0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->BcC(Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;)Lcom/bytedance/sdk/component/utils/Vq;

    move-result-object v0

    const/16 v1, 0x65

    invoke-virtual {v0, v1}, Landroid/os/Handler;->sendEmptyMessage(I)Z

    :cond_0
    return-void
.end method
