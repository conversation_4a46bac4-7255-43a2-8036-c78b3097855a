.class Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$3;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->Fj(Landroid/graphics/SurfaceTexture;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Landroid/graphics/SurfaceTexture;

.field final synthetic ex:Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;


# direct methods
.method public constructor <init>(Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;Landroid/graphics/SurfaceTexture;)V
    .locals 0

    iput-object p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$3;->ex:Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;

    iput-object p2, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$3;->Fj:Landroid/graphics/SurfaceTexture;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 3

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$3;->ex:Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;

    invoke-static {v0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->rAx(Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;)V

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$3;->ex:Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;

    invoke-static {v0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->BcC(Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;)Lcom/bytedance/sdk/component/utils/Vq;

    move-result-object v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$3;->ex:Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;

    invoke-static {v0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->BcC(Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;)Lcom/bytedance/sdk/component/utils/Vq;

    move-result-object v0

    const/16 v1, 0x6f

    iget-object v2, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$3;->Fj:Landroid/graphics/SurfaceTexture;

    invoke-virtual {v0, v1, v2}, Landroid/os/Handler;->obtainMessage(ILjava/lang/Object;)Landroid/os/Message;

    move-result-object v0

    invoke-virtual {v0}, Landroid/os/Message;->sendToTarget()V

    :cond_0
    return-void
.end method
