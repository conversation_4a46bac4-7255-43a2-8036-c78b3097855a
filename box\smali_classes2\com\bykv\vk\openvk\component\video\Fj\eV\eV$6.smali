.class Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$6;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->nsB()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;


# direct methods
.method public constructor <init>(Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;)V
    .locals 0

    iput-object p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$6;->Fj:Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 2

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$6;->Fj:Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;

    invoke-static {v0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->BcC(Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;)Lcom/bytedance/sdk/component/utils/Vq;

    move-result-object v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$6;->Fj:Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;

    invoke-static {v0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->BcC(Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;)Lcom/bytedance/sdk/component/utils/Vq;

    move-result-object v0

    invoke-virtual {v0}, Landroid/os/Handler;->getLooper()Landroid/os/Looper;

    move-result-object v0

    if-eqz v0, :cond_0

    :try_start_0
    invoke-static {}, Lcom/bytedance/sdk/component/svN/Fj/Fj;->Fj()Lcom/bytedance/sdk/component/svN/Fj/Fj;

    move-result-object v0

    iget-object v1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$6;->Fj:Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;

    invoke-static {v1}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->BcC(Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;)Lcom/bytedance/sdk/component/utils/Vq;

    move-result-object v1

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/component/svN/Fj/Fj;->Fj(Lcom/bytedance/sdk/component/utils/Vq;)Z

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$6;->Fj:Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;

    const/4 v1, 0x0

    invoke-static {v0, v1}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->Fj(Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;Lcom/bytedance/sdk/component/utils/Vq;)Lcom/bytedance/sdk/component/utils/Vq;
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    :catchall_0
    :cond_0
    return-void
.end method
