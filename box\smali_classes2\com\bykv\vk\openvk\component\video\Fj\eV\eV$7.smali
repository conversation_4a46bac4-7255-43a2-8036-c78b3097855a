.class Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$7;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->rf()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;


# direct methods
.method public constructor <init>(Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;)V
    .locals 0

    iput-object p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$7;->Fj:Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 2

    :try_start_0
    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$7;->Fj:Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;

    invoke-static {v0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->Fj(Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;)Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;

    move-result-object v0

    invoke-interface {v0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;->svN()V

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$7;->Fj:Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;

    const/16 v1, 0xcf

    invoke-static {v0, v1}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->Fj(Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;I)I

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$7;->Fj:Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;

    const/4 v1, 0x0

    invoke-static {v0, v1}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->hjc(Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;Z)Z
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    :catchall_0
    return-void
.end method
