.class Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$9;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->Fj(Z)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Z

.field final synthetic ex:Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;


# direct methods
.method public constructor <init>(Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;Z)V
    .locals 0

    iput-object p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$9;->ex:Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;

    iput-boolean p2, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$9;->Fj:Z

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 2

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$9;->ex:Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;

    invoke-static {v0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->Fj(Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;)Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;

    move-result-object v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$9;->ex:Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;

    invoke-static {v0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->Fj(Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;)Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;

    move-result-object v0

    iget-boolean v1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$9;->Fj:Z

    invoke-interface {v0, v1}, Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;->Fj(Z)V

    :cond_0
    return-void
.end method
