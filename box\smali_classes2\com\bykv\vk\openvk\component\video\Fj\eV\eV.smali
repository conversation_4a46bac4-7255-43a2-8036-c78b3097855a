.class public Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc$Fj;
.implements Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc$Ubf;
.implements Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc$WR;
.implements Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc$eV;
.implements Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc$ex;
.implements Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc$hjc;
.implements Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc$svN;
.implements Lcom/bykv/vk/openvk/component/video/api/Fj;
.implements Lcom/bytedance/sdk/component/utils/Vq$Fj;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$Fj;
    }
.end annotation


# static fields
.field private static final nsB:Landroid/util/SparseIntArray;


# instance fields
.field private Af:Ljava/util/ArrayList;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/ArrayList<",
            "Ljava/lang/Runnable;",
            ">;"
        }
    .end annotation
.end field

.field private BcC:Z

.field private Fj:Landroid/graphics/SurfaceTexture;

.field private JU:J

.field private JW:J

.field private Ko:Z

.field private final Moo:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Ljava/lang/ref/WeakReference<",
            "Lcom/bykv/vk/openvk/component/video/api/Fj$Fj;",
            ">;>;"
        }
    .end annotation
.end field

.field private Ql:J

.field private Tc:Z

.field private UYd:J

.field private Ubf:Z

.field private Vq:Z

.field private volatile WR:Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;

.field private cB:Ljava/lang/String;

.field private dG:Lcom/bytedance/sdk/component/utils/Vq;

.field private eV:I

.field private final eh:Ljava/lang/Runnable;

.field private ex:Landroid/view/SurfaceHolder;

.field private fj:Landroid/view/Surface;

.field private gXF:J

.field private hjc:I

.field private iT:Z

.field private final kF:Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$Fj;

.field private volatile lv:I

.field private mC:I

.field private mE:Z

.field private mSE:Z

.field private volatile rAx:I

.field private rS:J

.field private rXP:Ljava/util/concurrent/atomic/AtomicBoolean;

.field private rf:Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;

.field private spi:J

.field private final svN:Z

.field private volatile uM:Z

.field private uy:Z

.field private vYf:J


# direct methods
.method static constructor <clinit>()V
    .locals 1

    new-instance v0, Landroid/util/SparseIntArray;

    invoke-direct {v0}, Landroid/util/SparseIntArray;-><init>()V

    sput-object v0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->nsB:Landroid/util/SparseIntArray;

    return-void
.end method

.method public constructor <init>()V
    .locals 6

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, 0x0

    iput v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->hjc:I

    iput-boolean v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->Ubf:Z

    const/4 v1, 0x0

    iput-object v1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->WR:Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;

    iput-boolean v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->svN:Z

    iput-boolean v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->BcC:Z

    const/16 v2, 0xc9

    iput v2, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->rAx:I

    const-wide/16 v2, -0x1

    iput-wide v2, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->UYd:J

    iput-boolean v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->Tc:Z

    const-wide/16 v2, 0x0

    iput-wide v2, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->JW:J

    const-wide/high16 v4, -0x8000000000000000L

    iput-wide v4, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->JU:J

    iput-wide v2, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->Ql:J

    iput-wide v2, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->rS:J

    iput-wide v2, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->vYf:J

    iput v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->mC:I

    const-string v4, "0"

    iput-object v4, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->cB:Ljava/lang/String;

    new-instance v4, Ljava/util/concurrent/CopyOnWriteArrayList;

    invoke-direct {v4}, Ljava/util/concurrent/CopyOnWriteArrayList;-><init>()V

    iput-object v4, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->Moo:Ljava/util/List;

    iput-object v1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->rf:Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;

    iput-boolean v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->uy:Z

    const/16 v4, 0xc8

    iput v4, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->lv:I

    new-instance v4, Ljava/util/concurrent/atomic/AtomicBoolean;

    invoke-direct {v4, v0}, Ljava/util/concurrent/atomic/AtomicBoolean;-><init>(Z)V

    iput-object v4, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->rXP:Ljava/util/concurrent/atomic/AtomicBoolean;

    iput-object v1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->fj:Landroid/view/Surface;

    new-instance v1, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$1;

    invoke-direct {v1, p0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$1;-><init>(Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;)V

    iput-object v1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->eh:Ljava/lang/Runnable;

    new-instance v1, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$Fj;

    invoke-direct {v1, p0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$Fj;-><init>(Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;)V

    iput-object v1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->kF:Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$Fj;

    iput-wide v2, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->gXF:J

    iput-wide v2, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->spi:J

    iput-boolean v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->iT:Z

    const-string v0, "SSMediaPlayerWrapper"

    invoke-direct {p0, v0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->Fj(Ljava/lang/String;)V

    return-void
.end method

.method private Af()V
    .locals 2

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->dG:Lcom/bytedance/sdk/component/utils/Vq;

    if-eqz v0, :cond_0

    new-instance v1, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$10;

    invoke-direct {v1, p0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$10;-><init>(Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;)V

    invoke-virtual {v0, v1}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    :cond_0
    return-void
.end method

.method public static synthetic BcC(Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;)Lcom/bytedance/sdk/component/utils/Vq;
    .locals 0

    iget-object p0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->dG:Lcom/bytedance/sdk/component/utils/Vq;

    return-object p0
.end method

.method public static synthetic Fj(Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;I)I
    .locals 0

    iput p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->rAx:I

    return p1
.end method

.method public static synthetic Fj(Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;J)J
    .locals 0

    iput-wide p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->Ql:J

    return-wide p1
.end method

.method public static synthetic Fj(Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;)Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;
    .locals 0

    iget-object p0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->WR:Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;

    return-object p0
.end method

.method public static synthetic Fj(Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;)Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;
    .locals 0

    iput-object p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->WR:Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;

    return-object p1
.end method

.method public static synthetic Fj(Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;Lcom/bytedance/sdk/component/utils/Vq;)Lcom/bytedance/sdk/component/utils/Vq;
    .locals 0

    iput-object p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->dG:Lcom/bytedance/sdk/component/utils/Vq;

    return-object p1
.end method

.method public static synthetic Fj(Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;Ljava/lang/String;)Ljava/lang/String;
    .locals 0

    iput-object p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->cB:Ljava/lang/String;

    return-object p1
.end method

.method private Fj(JJ)V
    .locals 8

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->Moo:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :cond_0
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_1

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/lang/ref/WeakReference;

    if-eqz v1, :cond_0

    invoke-virtual {v1}, Ljava/lang/ref/Reference;->get()Ljava/lang/Object;

    move-result-object v2

    if-eqz v2, :cond_0

    invoke-virtual {v1}, Ljava/lang/ref/Reference;->get()Ljava/lang/Object;

    move-result-object v1

    move-object v2, v1

    check-cast v2, Lcom/bykv/vk/openvk/component/video/api/Fj$Fj;

    move-object v3, p0

    move-wide v4, p1

    move-wide v6, p3

    invoke-interface/range {v2 .. v7}, Lcom/bykv/vk/openvk/component/video/api/Fj$Fj;->Fj(Lcom/bykv/vk/openvk/component/video/api/Fj;JJ)V

    goto :goto_0

    :cond_1
    return-void
.end method

.method public static synthetic Fj(Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;II)V
    .locals 0

    invoke-direct {p0, p1, p2}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->ex(II)V

    return-void
.end method

.method public static synthetic Fj(Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;JJ)V
    .locals 0

    invoke-direct {p0, p1, p2, p3, p4}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->Fj(JJ)V

    return-void
.end method

.method private Fj(Ljava/lang/Runnable;)V
    .locals 1

    :try_start_0
    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->Af:Ljava/util/ArrayList;

    if-nez v0, :cond_0

    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->Af:Ljava/util/ArrayList;

    goto :goto_0

    :catchall_0
    move-exception p1

    goto :goto_1

    :cond_0
    :goto_0
    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->Af:Ljava/util/ArrayList;

    invoke-virtual {v0, p1}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    return-void

    :goto_1
    invoke-virtual {p1}, Ljava/lang/Throwable;->getMessage()Ljava/lang/String;

    return-void
.end method

.method private Fj(Ljava/lang/String;)V
    .locals 2

    const/4 v0, 0x0

    iput v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->mC:I

    invoke-static {}, Lcom/bytedance/sdk/component/svN/Fj/Fj;->Fj()Lcom/bytedance/sdk/component/svN/Fj/Fj;

    move-result-object v0

    invoke-static {p1}, Ljava/lang/String;->valueOf(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    const-string v1, "csj_"

    invoke-virtual {v1, p1}, Ljava/lang/String;->concat(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v0, p0, p1}, Lcom/bytedance/sdk/component/svN/Fj/Fj;->Fj(Lcom/bytedance/sdk/component/utils/Vq$Fj;Ljava/lang/String;)Lcom/bytedance/sdk/component/utils/Vq;

    move-result-object p1

    iput-object p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->dG:Lcom/bytedance/sdk/component/utils/Vq;

    const/4 p1, 0x1

    iput-boolean p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->iT:Z

    invoke-direct {p0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->Af()V

    return-void
.end method

.method private Fj(II)Z
    .locals 2

    const/16 v0, -0x3f2

    const/4 v1, 0x1

    if-eq p1, v0, :cond_0

    const/16 v0, -0x3ef

    if-eq p1, v0, :cond_0

    const/16 v0, -0x3ec

    if-eq p1, v0, :cond_0

    const/16 v0, -0x6e

    if-eq p1, v0, :cond_0

    const/16 v0, 0x64

    if-eq p1, v0, :cond_0

    const/16 v0, 0xc8

    if-eq p1, v0, :cond_0

    const/4 p1, 0x0

    goto :goto_0

    :cond_0
    const/4 p1, 0x1

    :goto_0
    if-eq p2, v1, :cond_1

    const/16 v0, 0x2bc

    if-eq p2, v0, :cond_1

    const/16 v0, 0x320

    if-eq p2, v0, :cond_1

    move v1, p1

    :cond_1
    return v1
.end method

.method public static synthetic Fj(Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;Z)Z
    .locals 0

    iput-boolean p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->Tc:Z

    return p1
.end method

.method public static synthetic Ko(Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;)J
    .locals 2

    iget-wide v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->UYd:J

    return-wide v0
.end method

.method private Moo()V
    .locals 5

    invoke-static {}, Landroid/os/SystemClock;->elapsedRealtime()J

    move-result-wide v0

    iget-wide v2, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->vYf:J

    sub-long/2addr v0, v2

    iget-object v2, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->Moo:Ljava/util/List;

    invoke-interface {v2}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v2

    :cond_0
    :goto_0
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    move-result v3

    if-eqz v3, :cond_1

    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Ljava/lang/ref/WeakReference;

    if-eqz v3, :cond_0

    invoke-virtual {v3}, Ljava/lang/ref/Reference;->get()Ljava/lang/Object;

    move-result-object v4

    if-eqz v4, :cond_0

    invoke-virtual {v3}, Ljava/lang/ref/Reference;->get()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lcom/bykv/vk/openvk/component/video/api/Fj$Fj;

    invoke-interface {v3, p0, v0, v1}, Lcom/bykv/vk/openvk/component/video/api/Fj$Fj;->Fj(Lcom/bykv/vk/openvk/component/video/api/Fj;J)V

    goto :goto_0

    :cond_1
    const/4 v0, 0x1

    iput-boolean v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->Ubf:Z

    return-void
.end method

.method public static synthetic Ubf(Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;)I
    .locals 0

    iget p0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->lv:I

    return p0
.end method

.method private Vq()V
    .locals 4

    sget-object v0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->nsB:Landroid/util/SparseIntArray;

    iget v1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->mC:I

    invoke-virtual {v0, v1}, Landroid/util/SparseIntArray;->get(I)I

    move-result v1

    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    const/4 v2, 0x1

    if-nez v1, :cond_0

    iget v1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->mC:I

    invoke-virtual {v0, v1, v2}, Landroid/util/SparseIntArray;->put(II)V

    return-void

    :cond_0
    iget v3, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->mC:I

    invoke-virtual {v1}, Ljava/lang/Integer;->intValue()I

    move-result v1

    add-int/2addr v1, v2

    invoke-virtual {v0, v3, v1}, Landroid/util/SparseIntArray;->put(II)V

    return-void
.end method

.method public static synthetic WR(Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;)J
    .locals 2

    iget-wide v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->JW:J

    return-wide v0
.end method

.method private cB()V
    .locals 2

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->WR:Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;

    if-nez v0, :cond_0

    return-void

    :cond_0
    :try_start_0
    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->WR:Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;

    invoke-interface {v0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;->UYd()V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    :catchall_0
    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->WR:Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;

    const/4 v1, 0x0

    invoke-interface {v0, v1}, Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;->Fj(Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc$ex;)V

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->WR:Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;

    invoke-interface {v0, v1}, Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;->Fj(Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc$svN;)V

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->WR:Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;

    invoke-interface {v0, v1}, Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;->Fj(Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc$Fj;)V

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->WR:Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;

    invoke-interface {v0, v1}, Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;->Fj(Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc$eV;)V

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->WR:Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;

    invoke-interface {v0, v1}, Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;->Fj(Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc$hjc;)V

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->WR:Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;

    invoke-interface {v0, v1}, Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;->Fj(Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc$Ubf;)V

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->WR:Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;

    invoke-interface {v0, v1}, Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;->Fj(Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc$WR;)V

    :try_start_1
    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->WR:Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;

    invoke-interface {v0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;->rAx()V
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_1

    :catchall_1
    return-void
.end method

.method public static synthetic eV(Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;)J
    .locals 2

    iget-wide v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->Ql:J

    return-wide v0
.end method

.method public static synthetic eV(Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;J)J
    .locals 0

    iput-wide p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->UYd:J

    return-wide p1
.end method

.method public static synthetic eV(Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;Z)Z
    .locals 0

    iput-boolean p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->uy:Z

    return p1
.end method

.method public static synthetic ex(Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;)J
    .locals 2

    iget-wide v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->JU:J

    return-wide v0
.end method

.method public static synthetic ex(Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;J)J
    .locals 0

    iput-wide p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->JW:J

    return-wide p1
.end method

.method private ex(II)V
    .locals 7

    const/16 p2, 0x2bd

    const v0, 0x7fffffff

    if-ne p1, p2, :cond_2

    invoke-static {}, Landroid/os/SystemClock;->elapsedRealtime()J

    move-result-wide p1

    iput-wide p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->gXF:J

    iget p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->hjc:I

    add-int/lit8 p1, p1, 0x1

    iput p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->hjc:I

    iget-object p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->Moo:Ljava/util/List;

    invoke-interface {p1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :cond_0
    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result p2

    if-eqz p2, :cond_1

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object p2

    check-cast p2, Ljava/lang/ref/WeakReference;

    if-eqz p2, :cond_0

    invoke-virtual {p2}, Ljava/lang/ref/Reference;->get()Ljava/lang/Object;

    move-result-object v1

    if-eqz v1, :cond_0

    invoke-virtual {p2}, Ljava/lang/ref/Reference;->get()Ljava/lang/Object;

    move-result-object p2

    check-cast p2, Lcom/bykv/vk/openvk/component/video/api/Fj$Fj;

    const/4 v1, 0x0

    invoke-interface {p2, p0, v0, v1, v1}, Lcom/bykv/vk/openvk/component/video/api/Fj$Fj;->Fj(Lcom/bykv/vk/openvk/component/video/api/Fj;III)V

    goto :goto_0

    :cond_1
    return-void

    :cond_2
    const/16 p2, 0x2be

    if-ne p1, p2, :cond_6

    iget-wide p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->gXF:J

    const-wide/16 v1, 0x0

    cmp-long v3, p1, v1

    if-lez v3, :cond_3

    iget-wide p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->spi:J

    invoke-static {}, Landroid/os/SystemClock;->elapsedRealtime()J

    move-result-wide v3

    iget-wide v5, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->gXF:J

    sub-long/2addr v3, v5

    add-long/2addr p1, v3

    iput-wide p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->spi:J

    iput-wide v1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->gXF:J

    :cond_3
    iget-object p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->Moo:Ljava/util/List;

    invoke-interface {p1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :cond_4
    :goto_1
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result p2

    if-eqz p2, :cond_5

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object p2

    check-cast p2, Ljava/lang/ref/WeakReference;

    if-eqz p2, :cond_4

    invoke-virtual {p2}, Ljava/lang/ref/Reference;->get()Ljava/lang/Object;

    move-result-object v1

    if-eqz v1, :cond_4

    invoke-virtual {p2}, Ljava/lang/ref/Reference;->get()Ljava/lang/Object;

    move-result-object p2

    check-cast p2, Lcom/bykv/vk/openvk/component/video/api/Fj$Fj;

    invoke-interface {p2, p0, v0}, Lcom/bykv/vk/openvk/component/video/api/Fj$Fj;->Fj(Lcom/bykv/vk/openvk/component/video/api/Fj;I)V

    goto :goto_1

    :cond_5
    return-void

    :cond_6
    iget-boolean p2, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->iT:Z

    if-eqz p2, :cond_7

    const/4 p2, 0x3

    if-ne p1, p2, :cond_7

    invoke-direct {p0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->lv()V

    invoke-direct {p0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->Moo()V

    iget-boolean p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->uy:Z

    invoke-virtual {p0, p1}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->ex(Z)V

    :cond_7
    return-void
.end method

.method private ex(J)V
    .locals 1

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->kF:Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$Fj;

    invoke-virtual {v0, p1, p2}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$Fj;->Fj(J)V

    iget-boolean p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->Vq:Z

    if-eqz p1, :cond_0

    iget-object p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->kF:Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$Fj;

    invoke-direct {p0, p1}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->ex(Ljava/lang/Runnable;)V

    return-void

    :cond_0
    iget-object p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->rf:Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;

    invoke-direct {p0, p1}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->ex(Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;)Z

    move-result p1

    if-eqz p1, :cond_1

    iget-object p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->kF:Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$Fj;

    invoke-direct {p0, p1}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->ex(Ljava/lang/Runnable;)V

    return-void

    :cond_1
    iget-object p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->kF:Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$Fj;

    invoke-direct {p0, p1}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->Fj(Ljava/lang/Runnable;)V

    return-void
.end method

.method private ex(Ljava/lang/Runnable;)V
    .locals 1

    if-eqz p1, :cond_2

    invoke-virtual {p0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->BcC()Z

    move-result v0

    if-eqz v0, :cond_0

    goto :goto_0

    :cond_0
    iget-boolean v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->Ko:Z

    if-nez v0, :cond_1

    invoke-interface {p1}, Ljava/lang/Runnable;->run()V

    return-void

    :cond_1
    invoke-direct {p0, p1}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->Fj(Ljava/lang/Runnable;)V

    :cond_2
    :goto_0
    return-void
.end method

.method private ex(Ljava/lang/String;)V
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/Throwable;
        }
    .end annotation

    new-instance v0, Ljava/io/FileInputStream;

    invoke-direct {v0, p1}, Ljava/io/FileInputStream;-><init>(Ljava/lang/String;)V

    iget-object p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->WR:Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;

    invoke-virtual {v0}, Ljava/io/FileInputStream;->getFD()Ljava/io/FileDescriptor;

    move-result-object v1

    invoke-interface {p1, v1}, Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;->Fj(Ljava/io/FileDescriptor;)V

    invoke-virtual {v0}, Ljava/io/FileInputStream;->close()V

    return-void
.end method

.method public static synthetic ex(Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;Z)Z
    .locals 0

    iput-boolean p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->BcC:Z

    return p1
.end method

.method private ex(Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;)Z
    .locals 0

    if-eqz p1, :cond_0

    invoke-virtual {p1}, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->eV()Z

    move-result p1

    if-eqz p1, :cond_0

    const/4 p1, 0x1

    return p1

    :cond_0
    const/4 p1, 0x0

    return p1
.end method

.method public static synthetic hjc(Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;J)J
    .locals 0

    iput-wide p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->JU:J

    return-wide p1
.end method

.method public static synthetic hjc(Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;)Z
    .locals 0

    iget-boolean p0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->Tc:Z

    return p0
.end method

.method public static synthetic hjc(Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;Z)Z
    .locals 0

    iput-boolean p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->uM:Z

    return p1
.end method

.method private lv()V
    .locals 1

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->Af:Ljava/util/ArrayList;

    if-eqz v0, :cond_1

    invoke-virtual {v0}, Ljava/util/ArrayList;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_0

    goto :goto_0

    :cond_0
    invoke-direct {p0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->uy()V

    :cond_1
    :goto_0
    return-void
.end method

.method private mC()V
    .locals 1

    new-instance v0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$14;

    invoke-direct {v0, p0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$14;-><init>(Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;)V

    invoke-direct {p0, v0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->ex(Ljava/lang/Runnable;)V

    return-void
.end method

.method private mE()V
    .locals 3

    const-wide/16 v0, 0x0

    iput-wide v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->JW:J

    const/4 v2, 0x0

    iput v2, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->hjc:I

    iput-wide v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->Ql:J

    iput-boolean v2, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->Tc:Z

    const-wide/high16 v0, -0x8000000000000000L

    iput-wide v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->JU:J

    return-void
.end method

.method public static synthetic mSE(Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;)Ljava/util/List;
    .locals 0

    iget-object p0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->Moo:Ljava/util/List;

    return-object p0
.end method

.method private nsB()V
    .locals 2

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->dG:Lcom/bytedance/sdk/component/utils/Vq;

    if-eqz v0, :cond_1

    invoke-virtual {v0}, Landroid/os/Handler;->getLooper()Landroid/os/Looper;

    move-result-object v0

    if-nez v0, :cond_0

    goto :goto_0

    :cond_0
    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->dG:Lcom/bytedance/sdk/component/utils/Vq;

    new-instance v1, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$6;

    invoke-direct {v1, p0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$6;-><init>(Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;)V

    invoke-virtual {v0, v1}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    :cond_1
    :goto_0
    return-void
.end method

.method public static synthetic rAx(Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;)V
    .locals 0

    invoke-direct {p0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->Af()V

    return-void
.end method

.method private rXP()V
    .locals 1

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->Af:Ljava/util/ArrayList;

    if-eqz v0, :cond_1

    invoke-virtual {v0}, Ljava/util/ArrayList;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_0

    goto :goto_0

    :cond_0
    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->Af:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->clear()V

    :cond_1
    :goto_0
    return-void
.end method

.method private rf()V
    .locals 2

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->dG:Lcom/bytedance/sdk/component/utils/Vq;

    if-eqz v0, :cond_0

    new-instance v1, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$7;

    invoke-direct {v1, p0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$7;-><init>(Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;)V

    invoke-virtual {v0, v1}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    :cond_0
    return-void
.end method

.method public static synthetic svN(Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;)I
    .locals 0

    iget p0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->hjc:I

    return p0
.end method

.method private uy()V
    .locals 2

    iget-boolean v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->mSE:Z

    if-eqz v0, :cond_0

    return-void

    :cond_0
    const/4 v0, 0x1

    iput-boolean v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->mSE:Z

    new-instance v0, Ljava/util/ArrayList;

    iget-object v1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->Af:Ljava/util/ArrayList;

    invoke-direct {v0, v1}, Ljava/util/ArrayList;-><init>(Ljava/util/Collection;)V

    invoke-virtual {v0}, Ljava/util/ArrayList;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_1

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/lang/Runnable;

    invoke-interface {v1}, Ljava/lang/Runnable;->run()V

    goto :goto_0

    :cond_1
    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->Af:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->clear()V

    const/4 v0, 0x0

    iput-boolean v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->mSE:Z

    return-void
.end method


# virtual methods
.method public BcC()Z
    .locals 1

    iget-boolean v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->Ko:Z

    return v0
.end method

.method public Fj(I)V
    .locals 1

    invoke-virtual {p0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->BcC()Z

    move-result v0

    if-eqz v0, :cond_0

    return-void

    :cond_0
    iput p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->lv:I

    return-void
.end method

.method public Fj(J)V
    .locals 2

    invoke-virtual {p0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->BcC()Z

    move-result v0

    if-eqz v0, :cond_0

    return-void

    :cond_0
    iget v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->rAx:I

    const/16 v1, 0xcf

    if-eq v0, v1, :cond_1

    iget v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->rAx:I

    const/16 v1, 0xce

    if-eq v0, v1, :cond_1

    iget v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->rAx:I

    const/16 v1, 0xd1

    if-ne v0, v1, :cond_2

    :cond_1
    new-instance v0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$2;

    invoke-direct {v0, p0, p1, p2}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$2;-><init>(Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;J)V

    invoke-direct {p0, v0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->ex(Ljava/lang/Runnable;)V

    :cond_2
    return-void
.end method

.method public Fj(Landroid/graphics/SurfaceTexture;)V
    .locals 1

    invoke-virtual {p0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->BcC()Z

    move-result v0

    if-eqz v0, :cond_0

    return-void

    :cond_0
    iput-object p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->Fj:Landroid/graphics/SurfaceTexture;

    const/4 v0, 0x1

    invoke-virtual {p0, v0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->Fj(Z)V

    new-instance v0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$3;

    invoke-direct {v0, p0, p1}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$3;-><init>(Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;Landroid/graphics/SurfaceTexture;)V

    invoke-direct {p0, v0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->ex(Ljava/lang/Runnable;)V

    return-void
.end method

.method public Fj(Landroid/os/Message;)V
    .locals 14

    iget v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->rAx:I

    iget v1, p1, Landroid/os/Message;->what:I

    iget-object v2, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->WR:Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;

    if-eqz v2, :cond_14

    iget v2, p1, Landroid/os/Message;->what:I

    const-wide/16 v3, 0x0

    const/16 v5, 0xcd

    const/16 v6, 0xca

    const/16 v7, 0xcb

    const/16 v8, 0xc9

    const/16 v9, 0xd0

    const/16 v10, 0xd1

    const/16 v11, 0xce

    const/4 v12, 0x1

    const/16 v13, 0xcf

    packed-switch v2, :pswitch_data_0

    :pswitch_0
    goto/16 :goto_5

    :pswitch_1
    :try_start_0
    iget-object p1, p1, Landroid/os/Message;->obj:Ljava/lang/Object;

    check-cast p1, Landroid/graphics/SurfaceTexture;

    new-instance v0, Landroid/view/Surface;

    invoke-direct {v0, p1}, Landroid/view/Surface;-><init>(Landroid/graphics/SurfaceTexture;)V

    iput-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->fj:Landroid/view/Surface;

    iget-object p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->WR:Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->fj:Landroid/view/Surface;

    invoke-interface {p1, v0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;->Fj(Landroid/view/Surface;)V

    iget-object p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->WR:Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;

    invoke-interface {p1, v12}, Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;->ex(Z)V

    invoke-direct {p0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->lv()V

    goto/16 :goto_5

    :pswitch_2
    iget-object p1, p1, Landroid/os/Message;->obj:Ljava/lang/Object;

    check-cast p1, Landroid/view/SurfaceHolder;

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->WR:Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;

    invoke-interface {v0, p1}, Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;->Fj(Landroid/view/SurfaceHolder;)V

    iget-object p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->WR:Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;

    invoke-interface {p1, v12}, Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;->ex(Z)V

    invoke-direct {p0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->lv()V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_1

    goto/16 :goto_5

    :pswitch_3
    invoke-direct {p0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->mE()V

    iget v2, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->rAx:I

    if-eq v2, v8, :cond_0

    iget v2, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->rAx:I

    if-ne v2, v7, :cond_f

    :cond_0
    :try_start_1
    iget-object p1, p1, Landroid/os/Message;->obj:Ljava/lang/Object;

    check-cast p1, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;

    invoke-virtual {p1}, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->ex()Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    if-eqz v0, :cond_1

    invoke-static {}, Lcom/bykv/vk/openvk/component/video/api/hjc;->ex()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p1, v0}, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->Fj(Ljava/lang/String;)V

    :cond_1
    new-instance v0, Ljava/io/File;

    invoke-virtual {p1}, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->ex()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p1}, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->Tc()Ljava/lang/String;

    move-result-object v2

    invoke-direct {v0, v1, v2}, Ljava/io/File;-><init>(Ljava/lang/String;Ljava/lang/String;)V

    invoke-virtual {v0}, Ljava/io/File;->exists()Z

    move-result v1

    if-eqz v1, :cond_3

    invoke-virtual {v0}, Ljava/io/File;->getAbsolutePath()Ljava/lang/String;

    invoke-static {}, Lcom/bykv/vk/openvk/component/video/api/hjc;->hjc()Z

    move-result p1

    if-eqz p1, :cond_2

    invoke-virtual {v0}, Ljava/io/File;->getAbsolutePath()Ljava/lang/String;

    move-result-object p1

    invoke-direct {p0, p1}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->ex(Ljava/lang/String;)V

    goto :goto_0

    :cond_2
    iget-object p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->WR:Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;

    invoke-virtual {v0}, Ljava/io/File;->getAbsolutePath()Ljava/lang/String;

    move-result-object v0

    invoke-interface {p1, v0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;->Fj(Ljava/lang/String;)V

    goto :goto_0

    :cond_3
    invoke-virtual {p1}, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->dG()Ljava/lang/String;

    iget v0, p1, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->ex:I

    const/16 v1, 0x17

    if-ne v0, v12, :cond_4

    sget v0, Landroid/os/Build$VERSION;->SDK_INT:I

    if-ge v0, v1, :cond_4

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->WR:Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;

    invoke-virtual {p1}, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->dG()Ljava/lang/String;

    move-result-object v1

    invoke-interface {v0, v1}, Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;->Fj(Ljava/lang/String;)V

    invoke-virtual {p1}, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->dG()Ljava/lang/String;

    goto :goto_0

    :cond_4
    sget v0, Landroid/os/Build$VERSION;->SDK_INT:I

    if-lt v0, v1, :cond_5

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->WR:Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;

    invoke-interface {v0, p1}, Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;->Fj(Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;)V

    invoke-virtual {p1}, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->dG()Ljava/lang/String;

    goto :goto_0

    :cond_5
    invoke-static {}, Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj;->Fj()Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj;

    move-result-object v0

    invoke-virtual {v0, p1}, Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj;->ex(Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;)Ljava/lang/String;

    move-result-object p1

    if-eqz p1, :cond_6

    invoke-static {}, Lcom/bykv/vk/openvk/component/video/api/hjc;->hjc()Z

    move-result v0

    if-eqz v0, :cond_6

    const-string v0, "file"

    invoke-virtual {p1, v0}, Ljava/lang/String;->startsWith(Ljava/lang/String;)Z

    move-result v0

    if-eqz v0, :cond_6

    invoke-static {p1}, Landroid/net/Uri;->parse(Ljava/lang/String;)Landroid/net/Uri;

    move-result-object p1

    invoke-virtual {p1}, Landroid/net/Uri;->getPath()Ljava/lang/String;

    move-result-object p1

    invoke-direct {p0, p1}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->ex(Ljava/lang/String;)V

    goto :goto_0

    :cond_6
    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->WR:Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;

    invoke-interface {v0, p1}, Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;->Fj(Ljava/lang/String;)V

    :goto_0
    iput v6, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->rAx:I
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_1

    goto/16 :goto_5

    :pswitch_4
    iget v2, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->rAx:I

    if-eq v2, v11, :cond_7

    iget v2, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->rAx:I

    if-eq v2, v13, :cond_7

    iget v2, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->rAx:I

    if-ne v2, v10, :cond_f

    :cond_7
    :try_start_2
    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->WR:Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;

    iget-object p1, p1, Landroid/os/Message;->obj:Ljava/lang/Object;

    check-cast p1, Ljava/lang/Long;

    invoke-virtual {p1}, Ljava/lang/Long;->longValue()J

    move-result-wide v1

    iget p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->eV:I

    invoke-interface {v0, v1, v2, p1}, Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;->Fj(JI)V
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_1

    goto/16 :goto_5

    :pswitch_5
    iget p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->rAx:I

    if-eq p1, v5, :cond_8

    iget p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->rAx:I

    if-eq p1, v11, :cond_8

    iget p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->rAx:I

    if-eq p1, v9, :cond_8

    iget p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->rAx:I

    if-eq p1, v13, :cond_8

    iget p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->rAx:I

    if-ne p1, v10, :cond_f

    :cond_8
    :try_start_3
    iget-object p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->WR:Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;

    invoke-interface {p1}, Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;->WR()V

    iput v9, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->rAx:I
    :try_end_3
    .catchall {:try_start_3 .. :try_end_3} :catchall_1

    goto/16 :goto_5

    :pswitch_6
    iget p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->rAx:I

    if-eq p1, v6, :cond_9

    iget p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->rAx:I

    if-ne p1, v9, :cond_f

    :cond_9
    :try_start_4
    iget-object p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->WR:Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;

    invoke-interface {p1}, Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;->BcC()V
    :try_end_4
    .catchall {:try_start_4 .. :try_end_4} :catchall_1

    goto/16 :goto_5

    :pswitch_7
    :try_start_5
    invoke-direct {p0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->cB()V
    :try_end_5
    .catchall {:try_start_5 .. :try_end_5} :catchall_0

    :catchall_0
    iget-object p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->Moo:Ljava/util/List;

    invoke-interface {p1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :cond_a
    :goto_1
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_b

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/lang/ref/WeakReference;

    if-eqz v0, :cond_a

    invoke-virtual {v0}, Ljava/lang/ref/Reference;->get()Ljava/lang/Object;

    move-result-object v1

    if-eqz v1, :cond_a

    invoke-virtual {v0}, Ljava/lang/ref/Reference;->get()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/bykv/vk/openvk/component/video/api/Fj$Fj;

    invoke-interface {v0, p0}, Lcom/bykv/vk/openvk/component/video/api/Fj$Fj;->hjc(Lcom/bykv/vk/openvk/component/video/api/Fj;)V

    goto :goto_1

    :cond_b
    iput v7, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->rAx:I

    goto/16 :goto_5

    :pswitch_8
    :try_start_6
    iget-object p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->WR:Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;

    invoke-interface {p1}, Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;->UYd()V

    iput v8, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->rAx:I
    :try_end_6
    .catchall {:try_start_6 .. :try_end_6} :catchall_1

    goto/16 :goto_5

    :pswitch_9
    iget-boolean p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->Tc:Z

    if-eqz p1, :cond_c

    iget-wide v5, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->JW:J

    iget-wide v7, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->Ql:J

    add-long/2addr v5, v7

    iput-wide v5, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->JW:J

    :cond_c
    const/4 p1, 0x0

    iput-boolean p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->Tc:Z

    iput-wide v3, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->Ql:J

    const-wide/high16 v2, -0x8000000000000000L

    iput-wide v2, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->JU:J

    iget v2, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->rAx:I

    if-eq v2, v11, :cond_d

    iget v2, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->rAx:I

    if-eq v2, v13, :cond_d

    iget v2, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->rAx:I

    if-ne v2, v10, :cond_f

    :cond_d
    :try_start_7
    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->WR:Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;

    invoke-interface {v0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;->svN()V

    iput v13, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->rAx:I

    iput-boolean p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->uM:Z

    iget-object p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->Moo:Ljava/util/List;

    invoke-interface {p1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :cond_e
    :goto_2
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_14

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/lang/ref/WeakReference;

    if-eqz v0, :cond_e

    invoke-virtual {v0}, Ljava/lang/ref/Reference;->get()Ljava/lang/Object;

    move-result-object v1

    if-eqz v1, :cond_e

    invoke-virtual {v0}, Ljava/lang/ref/Reference;->get()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/bykv/vk/openvk/component/video/api/Fj$Fj;

    invoke-interface {v0, p0}, Lcom/bykv/vk/openvk/component/video/api/Fj$Fj;->eV(Lcom/bykv/vk/openvk/component/video/api/Fj;)V
    :try_end_7
    .catchall {:try_start_7 .. :try_end_7} :catchall_1

    goto :goto_2

    :pswitch_a
    iget p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->rAx:I

    if-eq p1, v5, :cond_12

    iget p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->rAx:I

    if-eq p1, v13, :cond_12

    iget p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->rAx:I

    if-ne p1, v10, :cond_f

    goto :goto_4

    :cond_f
    const/16 p1, 0xc8

    iput p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->rAx:I

    iget-boolean p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->BcC:Z

    if-nez p1, :cond_14

    new-instance p1, Lcom/bykv/vk/openvk/component/video/api/hjc/Fj;

    const/16 v2, 0x134

    invoke-direct {p1, v2, v1}, Lcom/bykv/vk/openvk/component/video/api/hjc/Fj;-><init>(II)V

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v0, ","

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p1, v0}, Lcom/bykv/vk/openvk/component/video/api/hjc/Fj;->Fj(Ljava/lang/String;)V

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->Moo:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :cond_10
    :goto_3
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_11

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/lang/ref/WeakReference;

    if-eqz v1, :cond_10

    invoke-virtual {v1}, Ljava/lang/ref/Reference;->get()Ljava/lang/Object;

    move-result-object v2

    if-eqz v2, :cond_10

    invoke-virtual {v1}, Ljava/lang/ref/Reference;->get()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/bykv/vk/openvk/component/video/api/Fj$Fj;

    invoke-interface {v1, p0, p1}, Lcom/bykv/vk/openvk/component/video/api/Fj$Fj;->Fj(Lcom/bykv/vk/openvk/component/video/api/Fj;Lcom/bykv/vk/openvk/component/video/api/hjc/Fj;)V

    goto :goto_3

    :cond_11
    iput-boolean v12, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->BcC:Z

    goto :goto_5

    :cond_12
    :goto_4
    :try_start_8
    iget-object p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->WR:Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;

    invoke-interface {p1}, Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;->Ubf()V

    invoke-static {}, Landroid/os/SystemClock;->elapsedRealtime()J

    move-result-wide v0

    iput-wide v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->vYf:J

    iput v11, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->rAx:I

    iget-wide v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->UYd:J

    cmp-long p1, v0, v3

    if-lez p1, :cond_13

    iget-object p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->WR:Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;

    iget-wide v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->UYd:J

    iget v2, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->eV:I

    invoke-interface {p1, v0, v1, v2}, Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;->Fj(JI)V

    const-wide/16 v0, -0x1

    iput-wide v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->UYd:J

    :cond_13
    iget-object p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->rf:Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;

    if-eqz p1, :cond_14

    iget-boolean p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->uy:Z

    invoke-virtual {p0, p1}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->ex(Z)V
    :try_end_8
    .catchall {:try_start_8 .. :try_end_8} :catchall_1

    :catchall_1
    :cond_14
    :goto_5
    return-void

    nop

    :pswitch_data_0
    .packed-switch 0x64
        :pswitch_a
        :pswitch_9
        :pswitch_8
        :pswitch_7
        :pswitch_6
        :pswitch_5
        :pswitch_4
        :pswitch_3
        :pswitch_0
        :pswitch_0
        :pswitch_2
        :pswitch_1
    .end packed-switch
.end method

.method public Fj(Landroid/view/SurfaceHolder;)V
    .locals 1

    invoke-virtual {p0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->BcC()Z

    move-result v0

    if-eqz v0, :cond_0

    return-void

    :cond_0
    iput-object p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->ex:Landroid/view/SurfaceHolder;

    const/4 v0, 0x1

    invoke-virtual {p0, v0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->Fj(Z)V

    new-instance v0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$4;

    invoke-direct {v0, p0, p1}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$4;-><init>(Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;Landroid/view/SurfaceHolder;)V

    invoke-direct {p0, v0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->ex(Ljava/lang/Runnable;)V

    return-void
.end method

.method public Fj(Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;)V
    .locals 2

    const/16 p1, 0xd1

    iput p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->rAx:I

    sget-object p1, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->nsB:Landroid/util/SparseIntArray;

    iget v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->mC:I

    invoke-virtual {p1, v0}, Landroid/util/SparseIntArray;->delete(I)V

    iget-object p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->dG:Lcom/bytedance/sdk/component/utils/Vq;

    if-eqz p1, :cond_0

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->eh:Ljava/lang/Runnable;

    invoke-virtual {p1, v0}, Landroid/os/Handler;->removeCallbacks(Ljava/lang/Runnable;)V

    :cond_0
    iget-object p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->Moo:Ljava/util/List;

    invoke-interface {p1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :cond_1
    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_2

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/lang/ref/WeakReference;

    if-eqz v0, :cond_1

    invoke-virtual {v0}, Ljava/lang/ref/Reference;->get()Ljava/lang/Object;

    move-result-object v1

    if-eqz v1, :cond_1

    invoke-virtual {v0}, Ljava/lang/ref/Reference;->get()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/bykv/vk/openvk/component/video/api/Fj$Fj;

    invoke-interface {v0, p0}, Lcom/bykv/vk/openvk/component/video/api/Fj$Fj;->Fj(Lcom/bykv/vk/openvk/component/video/api/Fj;)V

    goto :goto_0

    :cond_2
    return-void
.end method

.method public Fj(Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;I)V
    .locals 2

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->WR:Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;

    if-eq v0, p1, :cond_0

    return-void

    :cond_0
    iget-object p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->Moo:Ljava/util/List;

    invoke-interface {p1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :cond_1
    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_2

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/lang/ref/WeakReference;

    if-eqz v0, :cond_1

    invoke-virtual {v0}, Ljava/lang/ref/Reference;->get()Ljava/lang/Object;

    move-result-object v1

    if-eqz v1, :cond_1

    invoke-virtual {v0}, Ljava/lang/ref/Reference;->get()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/bykv/vk/openvk/component/video/api/Fj$Fj;

    invoke-interface {v0, p0, p2}, Lcom/bykv/vk/openvk/component/video/api/Fj$Fj;->ex(Lcom/bykv/vk/openvk/component/video/api/Fj;I)V

    goto :goto_0

    :cond_2
    return-void
.end method

.method public Fj(Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;IIII)V
    .locals 0

    iget-object p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->Moo:Ljava/util/List;

    invoke-interface {p1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :cond_0
    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result p4

    if-eqz p4, :cond_1

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object p4

    check-cast p4, Ljava/lang/ref/WeakReference;

    if-eqz p4, :cond_0

    invoke-virtual {p4}, Ljava/lang/ref/Reference;->get()Ljava/lang/Object;

    move-result-object p5

    if-eqz p5, :cond_0

    invoke-virtual {p4}, Ljava/lang/ref/Reference;->get()Ljava/lang/Object;

    move-result-object p4

    check-cast p4, Lcom/bykv/vk/openvk/component/video/api/Fj$Fj;

    invoke-interface {p4, p0, p2, p3}, Lcom/bykv/vk/openvk/component/video/api/Fj$Fj;->Fj(Lcom/bykv/vk/openvk/component/video/api/Fj;II)V

    goto :goto_0

    :cond_1
    return-void
.end method

.method public Fj(Lcom/bykv/vk/openvk/component/video/api/Fj$Fj;)V
    .locals 2

    if-nez p1, :cond_0

    return-void

    :cond_0
    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->Moo:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :cond_1
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_2

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/lang/ref/WeakReference;

    if-eqz v1, :cond_1

    invoke-virtual {v1}, Ljava/lang/ref/Reference;->get()Ljava/lang/Object;

    move-result-object v1

    if-ne v1, p1, :cond_1

    return-void

    :cond_2
    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->Moo:Ljava/util/List;

    new-instance v1, Ljava/lang/ref/WeakReference;

    invoke-direct {v1, p1}, Ljava/lang/ref/WeakReference;-><init>(Ljava/lang/Object;)V

    invoke-interface {v0, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public Fj(Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;)V
    .locals 1

    invoke-virtual {p0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->BcC()Z

    move-result v0

    if-eqz v0, :cond_0

    return-void

    :cond_0
    iput-object p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->rf:Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;

    if-eqz p1, :cond_2

    iget-boolean v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->iT:Z

    if-eqz v0, :cond_1

    invoke-virtual {p1}, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->eV()Z

    move-result v0

    if-nez v0, :cond_1

    const/4 v0, 0x1

    goto :goto_0

    :cond_1
    const/4 v0, 0x0

    :goto_0
    iput-boolean v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->iT:Z

    :cond_2
    new-instance v0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$5;

    invoke-direct {v0, p0, p1}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$5;-><init>(Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;)V

    invoke-direct {p0, v0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->ex(Ljava/lang/Runnable;)V

    return-void
.end method

.method public Fj(Z)V
    .locals 2

    invoke-virtual {p0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->BcC()Z

    move-result v0

    if-eqz v0, :cond_0

    return-void

    :cond_0
    iput-boolean p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->Vq:Z

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->WR:Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;

    if-eqz v0, :cond_1

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->WR:Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;

    invoke-interface {v0, p1}, Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;->Fj(Z)V

    return-void

    :cond_1
    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->dG:Lcom/bytedance/sdk/component/utils/Vq;

    if-eqz v0, :cond_2

    new-instance v1, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$9;

    invoke-direct {v1, p0, p1}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$9;-><init>(Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;Z)V

    invoke-virtual {v0, v1}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    :cond_2
    return-void
.end method

.method public Fj(ZJZ)V
    .locals 2

    invoke-virtual {p0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->BcC()Z

    move-result v0

    if-eqz v0, :cond_0

    return-void

    :cond_0
    invoke-direct {p0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->Af()V

    iput-boolean p4, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->uy:Z

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->rXP:Ljava/util/concurrent/atomic/AtomicBoolean;

    const/4 v1, 0x1

    invoke-virtual {v0, v1}, Ljava/util/concurrent/atomic/AtomicBoolean;->set(Z)V

    const/4 v0, 0x0

    iput-boolean v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->uM:Z

    invoke-virtual {p0, p4}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->ex(Z)V

    if-eqz p1, :cond_1

    iput-wide p2, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->UYd:J

    invoke-direct {p0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->mC()V

    goto :goto_0

    :cond_1
    invoke-direct {p0, p2, p3}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->ex(J)V

    :goto_0
    iget-object p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->dG:Lcom/bytedance/sdk/component/utils/Vq;

    if-eqz p1, :cond_2

    iget-object p2, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->eh:Ljava/lang/Runnable;

    invoke-virtual {p1, p2}, Landroid/os/Handler;->removeCallbacks(Ljava/lang/Runnable;)V

    iget-object p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->dG:Lcom/bytedance/sdk/component/utils/Vq;

    iget-object p2, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->eh:Ljava/lang/Runnable;

    iget p3, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->lv:I

    int-to-long p3, p3

    invoke-virtual {p1, p2, p3, p4}, Landroid/os/Handler;->postDelayed(Ljava/lang/Runnable;J)Z

    :cond_2
    return-void
.end method

.method public Fj()Z
    .locals 1

    iget-boolean v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->Ubf:Z

    return v0
.end method

.method public Fj(Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;II)Z
    .locals 2

    invoke-direct {p0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->Vq()V

    const/16 p1, 0xc8

    iput p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->rAx:I

    iget-object p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->dG:Lcom/bytedance/sdk/component/utils/Vq;

    if-eqz p1, :cond_0

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->eh:Ljava/lang/Runnable;

    invoke-virtual {p1, v0}, Landroid/os/Handler;->removeCallbacks(Ljava/lang/Runnable;)V

    :cond_0
    invoke-direct {p0, p2, p3}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->Fj(II)Z

    move-result p1

    if-eqz p1, :cond_1

    invoke-direct {p0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->nsB()V

    :cond_1
    iget-object p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->rXP:Ljava/util/concurrent/atomic/AtomicBoolean;

    invoke-virtual {p1}, Ljava/util/concurrent/atomic/AtomicBoolean;->get()Z

    move-result p1

    const/4 v0, 0x1

    if-nez p1, :cond_2

    return v0

    :cond_2
    iget-object p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->rXP:Ljava/util/concurrent/atomic/AtomicBoolean;

    const/4 v1, 0x0

    invoke-virtual {p1, v1}, Ljava/util/concurrent/atomic/AtomicBoolean;->set(Z)V

    new-instance p1, Lcom/bykv/vk/openvk/component/video/api/hjc/Fj;

    invoke-direct {p1, p2, p3}, Lcom/bykv/vk/openvk/component/video/api/hjc/Fj;-><init>(II)V

    iget-object p2, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->Moo:Ljava/util/List;

    invoke-interface {p2}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p2

    :cond_3
    :goto_0
    invoke-interface {p2}, Ljava/util/Iterator;->hasNext()Z

    move-result p3

    if-eqz p3, :cond_4

    invoke-interface {p2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object p3

    check-cast p3, Ljava/lang/ref/WeakReference;

    if-eqz p3, :cond_3

    invoke-virtual {p3}, Ljava/lang/ref/Reference;->get()Ljava/lang/Object;

    move-result-object v1

    if-eqz v1, :cond_3

    invoke-virtual {p3}, Ljava/lang/ref/Reference;->get()Ljava/lang/Object;

    move-result-object p3

    check-cast p3, Lcom/bykv/vk/openvk/component/video/api/Fj$Fj;

    invoke-interface {p3, p0, p1}, Lcom/bykv/vk/openvk/component/video/api/Fj$Fj;->Fj(Lcom/bykv/vk/openvk/component/video/api/Fj;Lcom/bykv/vk/openvk/component/video/api/hjc/Fj;)V

    goto :goto_0

    :cond_4
    return v0
.end method

.method public JU()J
    .locals 5

    iget-wide v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->rS:J

    const-wide/16 v2, 0x0

    cmp-long v4, v0, v2

    if-eqz v4, :cond_0

    return-wide v0

    :cond_0
    iget v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->rAx:I

    const/16 v1, 0xce

    if-eq v0, v1, :cond_1

    iget v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->rAx:I

    const/16 v1, 0xcf

    if-ne v0, v1, :cond_2

    :cond_1
    :try_start_0
    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->WR:Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;

    invoke-interface {v0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;->Ko()J

    move-result-wide v0

    iput-wide v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->rS:J
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    :catchall_0
    :cond_2
    iget-wide v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->rS:J

    return-wide v0
.end method

.method public JW()I
    .locals 1

    iget v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->hjc:I

    return v0
.end method

.method public Ko()V
    .locals 2

    invoke-virtual {p0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->BcC()Z

    move-result v0

    if-eqz v0, :cond_0

    return-void

    :cond_0
    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->dG:Lcom/bytedance/sdk/component/utils/Vq;

    if-eqz v0, :cond_1

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->rXP:Ljava/util/concurrent/atomic/AtomicBoolean;

    const/4 v1, 0x1

    invoke-virtual {v0, v1}, Ljava/util/concurrent/atomic/AtomicBoolean;->set(Z)V

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->dG:Lcom/bytedance/sdk/component/utils/Vq;

    new-instance v1, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$11;

    invoke-direct {v1, p0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$11;-><init>(Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;)V

    invoke-virtual {v0, v1}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    :cond_1
    return-void
.end method

.method public Ql()J
    .locals 4

    invoke-virtual {p0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->BcC()Z

    move-result v0

    const-wide/16 v1, 0x0

    if-eqz v0, :cond_0

    return-wide v1

    :cond_0
    iget v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->rAx:I

    const/16 v3, 0xce

    if-eq v0, v3, :cond_1

    iget v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->rAx:I

    const/16 v3, 0xcf

    if-ne v0, v3, :cond_2

    :cond_1
    :try_start_0
    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->WR:Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;

    invoke-interface {v0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;->mSE()J

    move-result-wide v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    return-wide v0

    :catchall_0
    :cond_2
    return-wide v1
.end method

.method public Tc()J
    .locals 5

    sget v0, Landroid/os/Build$VERSION;->SDK_INT:I

    const/16 v1, 0x17

    if-lt v0, v1, :cond_1

    iget-boolean v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->Tc:Z

    if-eqz v0, :cond_0

    iget-wide v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->Ql:J

    const-wide/16 v2, 0x0

    cmp-long v4, v0, v2

    if-lez v4, :cond_0

    iget-wide v2, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->JW:J

    add-long/2addr v2, v0

    return-wide v2

    :cond_0
    iget-wide v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->JW:J

    return-wide v0

    :cond_1
    iget-wide v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->spi:J

    return-wide v0
.end method

.method public UYd()V
    .locals 2

    invoke-virtual {p0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->BcC()Z

    move-result v0

    if-eqz v0, :cond_0

    return-void

    :cond_0
    const/4 v0, 0x1

    iput-boolean v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->Ko:Z

    invoke-direct {p0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->rXP()V

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->dG:Lcom/bytedance/sdk/component/utils/Vq;

    if-eqz v0, :cond_2

    const/4 v1, 0x0

    :try_start_0
    invoke-virtual {v0, v1}, Landroid/os/Handler;->removeCallbacksAndMessages(Ljava/lang/Object;)V

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->WR:Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;

    if-eqz v0, :cond_1

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->dG:Lcom/bytedance/sdk/component/utils/Vq;

    const/16 v1, 0x67

    invoke-virtual {v0, v1}, Landroid/os/Handler;->sendEmptyMessage(I)Z
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    :cond_1
    invoke-direct {p0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->nsB()V

    return-void

    :catchall_0
    invoke-direct {p0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->nsB()V

    :cond_2
    return-void
.end method

.method public Ubf()I
    .locals 1

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->WR:Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;

    if-eqz v0, :cond_0

    invoke-virtual {p0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->BcC()Z

    move-result v0

    if-nez v0, :cond_0

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->WR:Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;

    invoke-interface {v0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;->Tc()I

    move-result v0

    return v0

    :cond_0
    const/4 v0, 0x0

    return v0
.end method

.method public WR()Z
    .locals 2

    iget v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->rAx:I

    const/16 v1, 0xce

    if-eq v0, v1, :cond_0

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->dG:Lcom/bytedance/sdk/component/utils/Vq;

    if-eqz v0, :cond_1

    const/16 v1, 0x64

    invoke-virtual {v0, v1}, Landroid/os/Handler;->hasMessages(I)Z

    move-result v0

    if-eqz v0, :cond_1

    :cond_0
    iget-boolean v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->uM:Z

    if-nez v0, :cond_1

    const/4 v0, 0x1

    return v0

    :cond_1
    const/4 v0, 0x0

    return v0
.end method

.method public dG()Z
    .locals 2

    iget v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->rAx:I

    const/16 v1, 0xcd

    if-ne v0, v1, :cond_0

    const/4 v0, 0x1

    return v0

    :cond_0
    const/4 v0, 0x0

    return v0
.end method

.method public eV()I
    .locals 1

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->WR:Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;

    if-eqz v0, :cond_0

    invoke-virtual {p0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->BcC()Z

    move-result v0

    if-nez v0, :cond_0

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->WR:Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;

    invoke-interface {v0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;->dG()I

    move-result v0

    return v0

    :cond_0
    const/4 v0, 0x0

    return v0
.end method

.method public ex(I)V
    .locals 0

    iput p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->eV:I

    return-void
.end method

.method public ex(Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;)V
    .locals 2

    invoke-virtual {p0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->BcC()Z

    move-result p1

    if-eqz p1, :cond_0

    return-void

    :cond_0
    const/16 p1, 0xcd

    iput p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->rAx:I

    :try_start_0
    iget-object p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->rf:Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;

    if-eqz p1, :cond_1

    invoke-virtual {p1}, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->UYd()F

    move-result p1

    const/4 v0, 0x0

    cmpl-float v0, p1, v0

    if-lez v0, :cond_1

    new-instance v0, Lcom/bykv/vk/openvk/component/video/api/ex;

    invoke-direct {v0}, Lcom/bykv/vk/openvk/component/video/api/ex;-><init>()V

    invoke-virtual {v0, p1}, Lcom/bykv/vk/openvk/component/video/api/ex;->Fj(F)V

    iget-object p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->WR:Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;

    invoke-interface {p1, v0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;->Fj(Lcom/bykv/vk/openvk/component/video/api/ex;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    goto :goto_0

    :catchall_0
    nop

    :cond_1
    :goto_0
    iget-object p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->dG:Lcom/bytedance/sdk/component/utils/Vq;

    if-eqz p1, :cond_3

    iget-boolean p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->uM:Z

    if-eqz p1, :cond_2

    invoke-direct {p0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->rf()V

    goto :goto_1

    :cond_2
    iget-object p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->dG:Lcom/bytedance/sdk/component/utils/Vq;

    const/16 v0, 0x64

    const/4 v1, -0x1

    invoke-virtual {p1, v0, v1, v1}, Landroid/os/Handler;->obtainMessage(III)Landroid/os/Message;

    move-result-object v0

    invoke-virtual {p1, v0}, Landroid/os/Handler;->sendMessage(Landroid/os/Message;)Z

    :cond_3
    :goto_1
    sget-object p1, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->nsB:Landroid/util/SparseIntArray;

    iget v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->mC:I

    invoke-virtual {p1, v0}, Landroid/util/SparseIntArray;->delete(I)V

    iget-boolean p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->iT:Z

    iget-boolean v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->mE:Z

    if-nez p1, :cond_4

    if-nez v0, :cond_4

    invoke-direct {p0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->Moo()V

    const/4 p1, 0x1

    iput-boolean p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->mE:Z

    :cond_4
    iget-object p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->Moo:Ljava/util/List;

    invoke-interface {p1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :cond_5
    :goto_2
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_6

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/lang/ref/WeakReference;

    if-eqz v0, :cond_5

    invoke-virtual {v0}, Ljava/lang/ref/Reference;->get()Ljava/lang/Object;

    move-result-object v1

    if-eqz v1, :cond_5

    invoke-virtual {v0}, Ljava/lang/ref/Reference;->get()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/bykv/vk/openvk/component/video/api/Fj$Fj;

    invoke-interface {v0, p0}, Lcom/bykv/vk/openvk/component/video/api/Fj$Fj;->ex(Lcom/bykv/vk/openvk/component/video/api/Fj;)V

    goto :goto_2

    :cond_6
    return-void
.end method

.method public ex(Z)V
    .locals 2

    invoke-virtual {p0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->BcC()Z

    move-result v0

    if-eqz v0, :cond_0

    return-void

    :cond_0
    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->dG:Lcom/bytedance/sdk/component/utils/Vq;

    if-nez v0, :cond_1

    return-void

    :cond_1
    new-instance v1, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$8;

    invoke-direct {v1, p0, p1}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$8;-><init>(Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;Z)V

    invoke-virtual {v0, v1}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    return-void
.end method

.method public ex()Z
    .locals 2

    iget v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->rAx:I

    const/16 v1, 0xd1

    if-ne v0, v1, :cond_0

    const/4 v0, 0x1

    return v0

    :cond_0
    const/4 v0, 0x0

    return v0
.end method

.method public ex(Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;II)Z
    .locals 4

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->WR:Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;

    const/4 v1, 0x0

    if-eq v0, p1, :cond_0

    return v1

    :cond_0
    const/16 p1, -0x3ec

    if-ne p3, p1, :cond_2

    new-instance p1, Lcom/bykv/vk/openvk/component/video/api/hjc/Fj;

    invoke-direct {p1, p2, p3}, Lcom/bykv/vk/openvk/component/video/api/hjc/Fj;-><init>(II)V

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->Moo:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :cond_1
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_2

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Ljava/lang/ref/WeakReference;

    if-eqz v2, :cond_1

    invoke-virtual {v2}, Ljava/lang/ref/Reference;->get()Ljava/lang/Object;

    move-result-object v3

    if-eqz v3, :cond_1

    invoke-virtual {v2}, Ljava/lang/ref/Reference;->get()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/bykv/vk/openvk/component/video/api/Fj$Fj;

    invoke-interface {v2, p0, p1}, Lcom/bykv/vk/openvk/component/video/api/Fj$Fj;->Fj(Lcom/bykv/vk/openvk/component/video/api/Fj;Lcom/bykv/vk/openvk/component/video/api/hjc/Fj;)V

    goto :goto_0

    :cond_2
    invoke-direct {p0, p2, p3}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->ex(II)V

    return v1
.end method

.method public hjc(Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;)V
    .locals 2

    iget-object p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->Moo:Ljava/util/List;

    invoke-interface {p1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :cond_0
    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_1

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/lang/ref/WeakReference;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Ljava/lang/ref/Reference;->get()Ljava/lang/Object;

    move-result-object v1

    if-eqz v1, :cond_0

    invoke-virtual {v0}, Ljava/lang/ref/Reference;->get()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/bykv/vk/openvk/component/video/api/Fj$Fj;

    const/4 v1, 0x1

    invoke-interface {v0, p0, v1}, Lcom/bykv/vk/openvk/component/video/api/Fj$Fj;->Fj(Lcom/bykv/vk/openvk/component/video/api/Fj;Z)V

    goto :goto_0

    :cond_1
    return-void
.end method

.method public hjc()Z
    .locals 1

    invoke-virtual {p0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->dG()Z

    move-result v0

    if-nez v0, :cond_1

    invoke-virtual {p0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->WR()Z

    move-result v0

    if-nez v0, :cond_1

    invoke-virtual {p0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->svN()Z

    move-result v0

    if-eqz v0, :cond_0

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    return v0

    :cond_1
    :goto_0
    const/4 v0, 0x1

    return v0
.end method

.method public mSE()V
    .locals 4

    invoke-virtual {p0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->BcC()Z

    move-result v0

    if-eqz v0, :cond_0

    return-void

    :cond_0
    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->WR:Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;

    if-nez v0, :cond_1

    return-void

    :cond_1
    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->rXP:Ljava/util/concurrent/atomic/AtomicBoolean;

    const/4 v1, 0x1

    invoke-virtual {v0, v1}, Ljava/util/concurrent/atomic/AtomicBoolean;->set(Z)V

    iget v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->rAx:I

    const/16 v2, 0xce

    if-eq v0, v2, :cond_2

    invoke-direct {p0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->mE()V

    const/4 v0, 0x0

    iput-boolean v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->uM:Z

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->kF:Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$Fj;

    invoke-virtual {v0, v1}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$Fj;->Fj(Z)V

    const-wide/16 v0, 0x0

    invoke-direct {p0, v0, v1}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->ex(J)V

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->dG:Lcom/bytedance/sdk/component/utils/Vq;

    if-eqz v0, :cond_2

    iget-object v1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->eh:Ljava/lang/Runnable;

    invoke-virtual {v0, v1}, Landroid/os/Handler;->removeCallbacks(Ljava/lang/Runnable;)V

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->dG:Lcom/bytedance/sdk/component/utils/Vq;

    iget-object v1, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->eh:Ljava/lang/Runnable;

    iget v2, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->lv:I

    int-to-long v2, v2

    invoke-virtual {v0, v1, v2, v3}, Landroid/os/Handler;->postDelayed(Ljava/lang/Runnable;J)Z

    :cond_2
    return-void
.end method

.method public rAx()V
    .locals 2

    invoke-virtual {p0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->BcC()Z

    move-result v0

    if-eqz v0, :cond_0

    return-void

    :cond_0
    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->dG:Lcom/bytedance/sdk/component/utils/Vq;

    if-eqz v0, :cond_6

    const/16 v1, 0x64

    invoke-virtual {v0, v1}, Landroid/os/Handler;->removeMessages(I)V

    const/4 v0, 0x1

    iput-boolean v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->uM:Z

    iget-boolean v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->iT:Z

    const/16 v1, 0x65

    if-nez v0, :cond_3

    iget-boolean v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->mE:Z

    if-nez v0, :cond_2

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->rf:Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;

    invoke-direct {p0, v0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->ex(Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;)Z

    move-result v0

    if-eqz v0, :cond_1

    goto :goto_0

    :cond_1
    new-instance v0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$12;

    invoke-direct {v0, p0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$12;-><init>(Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;)V

    invoke-direct {p0, v0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->Fj(Ljava/lang/Runnable;)V

    return-void

    :cond_2
    :goto_0
    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->dG:Lcom/bytedance/sdk/component/utils/Vq;

    if-eqz v0, :cond_6

    invoke-virtual {v0, v1}, Landroid/os/Handler;->sendEmptyMessage(I)Z

    return-void

    :cond_3
    iget-boolean v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->Ubf:Z

    if-nez v0, :cond_5

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->rf:Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;

    invoke-direct {p0, v0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->ex(Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;)Z

    move-result v0

    if-eqz v0, :cond_4

    goto :goto_1

    :cond_4
    new-instance v0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$13;

    invoke-direct {v0, p0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV$13;-><init>(Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;)V

    invoke-direct {p0, v0}, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->Fj(Ljava/lang/Runnable;)V

    goto :goto_2

    :cond_5
    :goto_1
    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->dG:Lcom/bytedance/sdk/component/utils/Vq;

    if-eqz v0, :cond_6

    invoke-virtual {v0, v1}, Landroid/os/Handler;->sendEmptyMessage(I)Z

    :cond_6
    :goto_2
    return-void
.end method

.method public rS()Landroid/view/SurfaceHolder;
    .locals 1

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->ex:Landroid/view/SurfaceHolder;

    return-object v0
.end method

.method public svN()Z
    .locals 2

    iget v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->rAx:I

    const/16 v1, 0xcf

    if-eq v0, v1, :cond_0

    iget-boolean v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->uM:Z

    if-eqz v0, :cond_1

    :cond_0
    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->dG:Lcom/bytedance/sdk/component/utils/Vq;

    if-eqz v0, :cond_1

    const/16 v1, 0x64

    invoke-virtual {v0, v1}, Landroid/os/Handler;->hasMessages(I)Z

    move-result v0

    if-nez v0, :cond_1

    const/4 v0, 0x1

    return v0

    :cond_1
    const/4 v0, 0x0

    return v0
.end method

.method public vYf()Landroid/graphics/SurfaceTexture;
    .locals 1

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/eV/eV;->Fj:Landroid/graphics/SurfaceTexture;

    return-object v0
.end method
