.class public interface abstract Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc$WR;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "WR"
.end annotation


# virtual methods
.method public abstract hjc(Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;)V
.end method
