.class public interface abstract Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc$eV;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "eV"
.end annotation


# virtual methods
.method public abstract ex(Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;II)Z
.end method
