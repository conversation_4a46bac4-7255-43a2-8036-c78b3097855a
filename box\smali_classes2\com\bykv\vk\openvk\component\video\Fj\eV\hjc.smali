.class public interface abstract Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc$eV;,
        Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc$hjc;,
        Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc$svN;,
        Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc$WR;,
        Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc$Fj;,
        Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc$ex;,
        Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc$Ubf;
    }
.end annotation


# virtual methods
.method public abstract BcC()V
.end method

.method public abstract Fj(JI)V
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/Throwable;
        }
    .end annotation
.end method

.method public abstract Fj(Landroid/view/Surface;)V
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/Throwable;
        }
    .end annotation
.end method

.method public abstract Fj(Landroid/view/SurfaceHolder;)V
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/Throwable;
        }
    .end annotation
.end method

.method public abstract Fj(Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc$Fj;)V
.end method

.method public abstract Fj(Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc$Ubf;)V
.end method

.method public abstract Fj(Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc$WR;)V
.end method

.method public abstract Fj(Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc$eV;)V
.end method

.method public abstract Fj(Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc$ex;)V
.end method

.method public abstract Fj(Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc$hjc;)V
.end method

.method public abstract Fj(Lcom/bykv/vk/openvk/component/video/Fj/eV/hjc$svN;)V
.end method

.method public abstract Fj(Lcom/bykv/vk/openvk/component/video/api/ex;)V
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/Throwable;
        }
    .end annotation
.end method

.method public abstract Fj(Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;)V
.end method

.method public abstract Fj(Ljava/io/FileDescriptor;)V
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/Throwable;
        }
    .end annotation
.end method

.method public abstract Fj(Ljava/lang/String;)V
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/Throwable;
        }
    .end annotation
.end method

.method public abstract Fj(Z)V
.end method

.method public abstract Ko()J
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/Throwable;
        }
    .end annotation
.end method

.method public abstract Tc()I
.end method

.method public abstract UYd()V
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/Throwable;
        }
    .end annotation
.end method

.method public abstract Ubf()V
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/Throwable;
        }
    .end annotation
.end method

.method public abstract WR()V
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/Throwable;
        }
    .end annotation
.end method

.method public abstract dG()I
.end method

.method public abstract eV(Z)V
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/Throwable;
        }
    .end annotation
.end method

.method public abstract ex(Z)V
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/Throwable;
        }
    .end annotation
.end method

.method public abstract hjc(Z)V
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/Throwable;
        }
    .end annotation
.end method

.method public abstract mSE()J
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/Throwable;
        }
    .end annotation
.end method

.method public abstract rAx()V
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/Throwable;
        }
    .end annotation
.end method

.method public abstract svN()V
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/Throwable;
        }
    .end annotation
.end method
