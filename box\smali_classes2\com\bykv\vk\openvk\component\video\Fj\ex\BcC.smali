.class Lcom/bykv/vk/openvk/component/video/Fj/ex/BcC;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/bykv/vk/openvk/component/video/Fj/ex/BcC$Fj;
    }
.end annotation


# instance fields
.field private final Fj:Ljava/io/RandomAccessFile;


# direct methods
.method public constructor <init>(Ljava/io/File;Ljava/lang/String;)V
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lcom/bykv/vk/openvk/component/video/Fj/ex/BcC$Fj;
        }
    .end annotation

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    :try_start_0
    new-instance v0, Ljava/io/RandomAccessFile;

    invoke-direct {v0, p1, p2}, Ljava/io/RandomAccessFile;-><init>(Ljava/io/File;Ljava/lang/String;)V

    iput-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/ex/BcC;->Fj:Ljava/io/RandomAccessFile;
    :try_end_0
    .catch Ljava/io/FileNotFoundException; {:try_start_0 .. :try_end_0} :catch_0

    return-void

    :catch_0
    move-exception p1

    new-instance p2, Lcom/bykv/vk/openvk/component/video/Fj/ex/BcC$Fj;

    invoke-direct {p2, p1}, Lcom/bykv/vk/openvk/component/video/Fj/ex/BcC$Fj;-><init>(Ljava/lang/Throwable;)V

    throw p2
.end method


# virtual methods
.method public Fj([B)I
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lcom/bykv/vk/openvk/component/video/Fj/ex/BcC$Fj;
        }
    .end annotation

    :try_start_0
    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/ex/BcC;->Fj:Ljava/io/RandomAccessFile;

    invoke-virtual {v0, p1}, Ljava/io/RandomAccessFile;->read([B)I

    move-result p1
    :try_end_0
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_0

    return p1

    :catch_0
    move-exception p1

    new-instance v0, Lcom/bykv/vk/openvk/component/video/Fj/ex/BcC$Fj;

    invoke-direct {v0, p1}, Lcom/bykv/vk/openvk/component/video/Fj/ex/BcC$Fj;-><init>(Ljava/lang/Throwable;)V

    throw v0
.end method

.method public Fj()V
    .locals 1

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/ex/BcC;->Fj:Ljava/io/RandomAccessFile;

    invoke-static {v0}, Lcom/bykv/vk/openvk/component/video/Fj/hjc/Fj;->Fj(Ljava/io/RandomAccessFile;)V

    return-void
.end method

.method public Fj(J)V
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lcom/bykv/vk/openvk/component/video/Fj/ex/BcC$Fj;
        }
    .end annotation

    :try_start_0
    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/ex/BcC;->Fj:Ljava/io/RandomAccessFile;

    invoke-virtual {v0, p1, p2}, Ljava/io/RandomAccessFile;->seek(J)V
    :try_end_0
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_0

    return-void

    :catch_0
    move-exception p1

    new-instance p2, Lcom/bykv/vk/openvk/component/video/Fj/ex/BcC$Fj;

    invoke-direct {p2, p1}, Lcom/bykv/vk/openvk/component/video/Fj/ex/BcC$Fj;-><init>(Ljava/lang/Throwable;)V

    throw p2
.end method

.method public Fj([BII)V
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lcom/bykv/vk/openvk/component/video/Fj/ex/BcC$Fj;
        }
    .end annotation

    :try_start_0
    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/ex/BcC;->Fj:Ljava/io/RandomAccessFile;

    invoke-virtual {v0, p1, p2, p3}, Ljava/io/RandomAccessFile;->write([BII)V
    :try_end_0
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_0

    return-void

    :catch_0
    move-exception p1

    new-instance p2, Lcom/bykv/vk/openvk/component/video/Fj/ex/BcC$Fj;

    invoke-direct {p2, p1}, Lcom/bykv/vk/openvk/component/video/Fj/ex/BcC$Fj;-><init>(Ljava/lang/Throwable;)V

    throw p2
.end method
