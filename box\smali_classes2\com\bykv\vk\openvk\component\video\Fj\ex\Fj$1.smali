.class Lcom/bykv/vk/openvk/component/video/Fj/ex/Fj$1;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bykv/vk/openvk/component/video/Fj/ex/Fj;->Fj(II)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bykv/vk/openvk/component/video/Fj/ex/Fj;


# direct methods
.method public constructor <init>(Lcom/bykv/vk/openvk/component/video/Fj/ex/Fj;)V
    .locals 0

    iput-object p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/ex/Fj$1;->Fj:Lcom/bykv/vk/openvk/component/video/Fj/ex/Fj;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 2

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/ex/Fj$1;->Fj:Lcom/bykv/vk/openvk/component/video/Fj/ex/Fj;

    iget-object v1, v0, Lcom/bykv/vk/openvk/component/video/Fj/ex/Fj;->Ubf:Lcom/bykv/vk/openvk/component/video/Fj/ex/eV/Fj;

    if-eqz v1, :cond_0

    iget-object v0, v0, Lcom/bykv/vk/openvk/component/video/Fj/ex/Fj;->Ko:Lcom/bykv/vk/openvk/component/video/Fj/ex/UYd;

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/ex/Fj$1;->Fj:Lcom/bykv/vk/openvk/component/video/Fj/ex/Fj;

    invoke-static {v0}, Lcom/bykv/vk/openvk/component/video/Fj/ex/Fj;->Fj(Lcom/bykv/vk/openvk/component/video/Fj/ex/Fj;)I

    :cond_0
    return-void
.end method
