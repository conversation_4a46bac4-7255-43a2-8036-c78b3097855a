.class public abstract Lcom/bykv/vk/openvk/component/video/Fj/ex/Fj/Fj;
.super Ljava/lang/Object;


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public Fj(Ljava/lang/String;)V
    .locals 0

    return-void
.end method

.method public abstract eV(Ljava/lang/String;)Ljava/io/File;
.end method

.method public ex(Ljava/lang/String;)V
    .locals 0

    return-void
.end method

.method public abstract hjc(Ljava/lang/String;)Ljava/io/File;
.end method
