.class Lcom/bykv/vk/openvk/component/video/Fj/ex/Fj/hjc$2;
.super Lcom/bytedance/sdk/component/svN/BcC;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bykv/vk/openvk/component/video/Fj/ex/Fj/hjc;-><init>(Ljava/io/File;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bykv/vk/openvk/component/video/Fj/ex/Fj/hjc;


# direct methods
.method public constructor <init>(Lcom/bykv/vk/openvk/component/video/Fj/ex/Fj/hjc;Ljava/lang/String;I)V
    .locals 0

    iput-object p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/ex/Fj/hjc$2;->Fj:Lcom/bykv/vk/openvk/component/video/Fj/ex/Fj/hjc;

    invoke-direct {p0, p2, p3}, Lcom/bytedance/sdk/component/svN/BcC;-><init>(Ljava/lang/String;I)V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 1

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/ex/Fj/hjc$2;->Fj:Lcom/bykv/vk/openvk/component/video/Fj/ex/Fj/hjc;

    invoke-static {v0}, Lcom/bykv/vk/openvk/component/video/Fj/ex/Fj/hjc;->ex(Lcom/bykv/vk/openvk/component/video/Fj/ex/Fj/hjc;)V

    return-void
.end method
