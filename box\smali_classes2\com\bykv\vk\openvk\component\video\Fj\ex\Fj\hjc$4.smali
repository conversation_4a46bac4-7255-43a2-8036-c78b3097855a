.class Lcom/bykv/vk/openvk/component/video/Fj/ex/Fj/hjc$4;
.super Lcom/bytedance/sdk/component/svN/BcC;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bykv/vk/openvk/component/video/Fj/ex/Fj/hjc;->Fj()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bykv/vk/openvk/component/video/Fj/ex/Fj/hjc;


# direct methods
.method public constructor <init>(Lcom/bykv/vk/openvk/component/video/Fj/ex/Fj/hjc;Ljava/lang/String;I)V
    .locals 0

    iput-object p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/ex/Fj/hjc$4;->Fj:Lcom/bykv/vk/openvk/component/video/Fj/ex/Fj/hjc;

    invoke-direct {p0, p2, p3}, Lcom/bytedance/sdk/component/svN/BcC;-><init>(Ljava/lang/String;I)V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 3

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/ex/Fj/hjc$4;->Fj:Lcom/bykv/vk/openvk/component/video/Fj/ex/Fj/hjc;

    const-wide/16 v1, 0x0

    invoke-static {v0, v1, v2}, Lcom/bykv/vk/openvk/component/video/Fj/ex/Fj/hjc;->Fj(Lcom/bykv/vk/openvk/component/video/Fj/ex/Fj/hjc;J)V

    return-void
.end method
