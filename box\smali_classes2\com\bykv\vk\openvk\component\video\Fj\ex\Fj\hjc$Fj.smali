.class public interface abstract Lcom/bykv/vk/openvk/component/video/Fj/ex/Fj/hjc$Fj;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bykv/vk/openvk/component/video/Fj/ex/Fj/hjc;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "Fj"
.end annotation


# virtual methods
.method public abstract Fj(Ljava/lang/String;)V
.end method

.method public abstract Fj(Ljava/util/Set;)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Set<",
            "Ljava/lang/String;",
            ">;)V"
        }
    .end annotation
.end method
