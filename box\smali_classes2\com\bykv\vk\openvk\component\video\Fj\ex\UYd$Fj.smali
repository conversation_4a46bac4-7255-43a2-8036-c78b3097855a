.class public Lcom/bykv/vk/openvk/component/video/Fj/ex/UYd$Fj;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bykv/vk/openvk/component/video/Fj/ex/UYd;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = "Fj"
.end annotation


# instance fields
.field final Fj:Ljava/lang/String;

.field ex:I

.field final synthetic hjc:Lcom/bykv/vk/openvk/component/video/Fj/ex/UYd;


# direct methods
.method public constructor <init>(Lcom/bykv/vk/openvk/component/video/Fj/ex/UYd;Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/ex/UYd$Fj;->hjc:Lcom/bykv/vk/openvk/component/video/Fj/ex/UYd;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p2, p0, Lcom/bykv/vk/openvk/component/video/Fj/ex/UYd$Fj;->Fj:Ljava/lang/String;

    return-void
.end method


# virtual methods
.method public Fj()V
    .locals 2

    invoke-static {}, Lcom/bykv/vk/openvk/component/video/Fj/ex/UYd;->hjc()Ljava/util/Set;

    move-result-object v0

    iget-object v1, p0, Lcom/bykv/vk/openvk/component/video/Fj/ex/UYd$Fj;->Fj:Ljava/lang/String;

    invoke-interface {v0, v1}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public ex()V
    .locals 2

    invoke-static {}, Lcom/bykv/vk/openvk/component/video/Fj/ex/UYd;->eV()Ljava/util/Set;

    move-result-object v0

    iget-object v1, p0, Lcom/bykv/vk/openvk/component/video/Fj/ex/UYd$Fj;->Fj:Ljava/lang/String;

    invoke-interface {v0, v1}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public toString()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/ex/UYd$Fj;->Fj:Ljava/lang/String;

    return-object v0
.end method
