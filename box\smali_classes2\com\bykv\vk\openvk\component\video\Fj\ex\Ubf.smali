.class public Lcom/bykv/vk/openvk/component/video/Fj/ex/Ubf;
.super Ljava/lang/Object;


# static fields
.field public static volatile BcC:I

.field static volatile Fj:Lcom/bykv/vk/openvk/component/video/Fj/ex/Fj/ex;

.field private static volatile Ko:Lcom/bykv/vk/openvk/component/video/Fj/ex/ex/hjc;

.field static volatile Ubf:Z

.field static volatile WR:Z

.field public static volatile eV:Z

.field static volatile ex:Lcom/bykv/vk/openvk/component/video/Fj/ex/Fj/hjc;

.field public static final hjc:Z

.field public static volatile mSE:Ljava/lang/Integer;

.field private static volatile rAx:Landroid/content/Context;
    .annotation build Landroid/annotation/SuppressLint;
        value = {
            "StaticFieldLeak"
        }
    .end annotation
.end field

.field static volatile svN:I


# direct methods
.method static constructor <clinit>()V
    .locals 1

    invoke-static {}, Lcom/bytedance/sdk/component/utils/dG;->eV()Z

    move-result v0

    sput-boolean v0, Lcom/bykv/vk/openvk/component/video/Fj/ex/Ubf;->hjc:Z

    const/4 v0, 0x1

    sput-boolean v0, Lcom/bykv/vk/openvk/component/video/Fj/ex/Ubf;->Ubf:Z

    const/4 v0, 0x0

    sput v0, Lcom/bykv/vk/openvk/component/video/Fj/ex/Ubf;->svN:I

    const/4 v0, 0x3

    sput v0, Lcom/bykv/vk/openvk/component/video/Fj/ex/Ubf;->BcC:I

    return-void
.end method

.method public static Fj()Landroid/content/Context;
    .locals 1

    sget-object v0, Lcom/bykv/vk/openvk/component/video/Fj/ex/Ubf;->rAx:Landroid/content/Context;

    return-object v0
.end method

.method public static Fj(I)V
    .locals 0

    sput p0, Lcom/bykv/vk/openvk/component/video/Fj/ex/Ubf;->svN:I

    return-void
.end method

.method public static Fj(Lcom/bykv/vk/openvk/component/video/Fj/ex/Fj/hjc;Landroid/content/Context;)V
    .locals 1

    if-eqz p0, :cond_2

    if-eqz p1, :cond_2

    invoke-virtual {p1}, Landroid/content/Context;->getApplicationContext()Landroid/content/Context;

    move-result-object v0

    sput-object v0, Lcom/bykv/vk/openvk/component/video/Fj/ex/Ubf;->rAx:Landroid/content/Context;

    sget-object v0, Lcom/bykv/vk/openvk/component/video/Fj/ex/Ubf;->ex:Lcom/bykv/vk/openvk/component/video/Fj/ex/Fj/hjc;

    if-eqz v0, :cond_0

    return-void

    :cond_0
    sget-object v0, Lcom/bykv/vk/openvk/component/video/Fj/ex/Ubf;->Fj:Lcom/bykv/vk/openvk/component/video/Fj/ex/Fj/ex;

    if-nez v0, :cond_1

    sput-object p0, Lcom/bykv/vk/openvk/component/video/Fj/ex/Ubf;->ex:Lcom/bykv/vk/openvk/component/video/Fj/ex/Fj/hjc;

    invoke-static {p1}, Lcom/bykv/vk/openvk/component/video/Fj/ex/ex/hjc;->Fj(Landroid/content/Context;)Lcom/bykv/vk/openvk/component/video/Fj/ex/ex/hjc;

    move-result-object p1

    sput-object p1, Lcom/bykv/vk/openvk/component/video/Fj/ex/Ubf;->Ko:Lcom/bykv/vk/openvk/component/video/Fj/ex/ex/hjc;

    sget-object p1, Lcom/bykv/vk/openvk/component/video/Fj/ex/Ubf;->ex:Lcom/bykv/vk/openvk/component/video/Fj/ex/Fj/hjc;

    new-instance v0, Lcom/bykv/vk/openvk/component/video/Fj/ex/Ubf$1;

    invoke-direct {v0}, Lcom/bykv/vk/openvk/component/video/Fj/ex/Ubf$1;-><init>()V

    invoke-virtual {p1, v0}, Lcom/bykv/vk/openvk/component/video/Fj/ex/Fj/hjc;->Fj(Lcom/bykv/vk/openvk/component/video/Fj/ex/Fj/hjc$Fj;)V

    invoke-static {}, Lcom/bykv/vk/openvk/component/video/Fj/ex/WR;->Fj()Lcom/bykv/vk/openvk/component/video/Fj/ex/WR;

    move-result-object p1

    invoke-virtual {p1, p0}, Lcom/bykv/vk/openvk/component/video/Fj/ex/WR;->Fj(Lcom/bykv/vk/openvk/component/video/Fj/ex/Fj/hjc;)V

    sget-object v0, Lcom/bykv/vk/openvk/component/video/Fj/ex/Ubf;->Ko:Lcom/bykv/vk/openvk/component/video/Fj/ex/ex/hjc;

    invoke-virtual {p1, v0}, Lcom/bykv/vk/openvk/component/video/Fj/ex/WR;->Fj(Lcom/bykv/vk/openvk/component/video/Fj/ex/ex/hjc;)V

    invoke-static {}, Lcom/bykv/vk/openvk/component/video/Fj/ex/eV;->hjc()Lcom/bykv/vk/openvk/component/video/Fj/ex/eV;

    move-result-object p1

    invoke-virtual {p1, p0}, Lcom/bykv/vk/openvk/component/video/Fj/ex/eV;->Fj(Lcom/bykv/vk/openvk/component/video/Fj/ex/Fj/hjc;)V

    sget-object p0, Lcom/bykv/vk/openvk/component/video/Fj/ex/Ubf;->Ko:Lcom/bykv/vk/openvk/component/video/Fj/ex/ex/hjc;

    invoke-virtual {p1, p0}, Lcom/bykv/vk/openvk/component/video/Fj/ex/eV;->Fj(Lcom/bykv/vk/openvk/component/video/Fj/ex/ex/hjc;)V

    return-void

    :cond_1
    const/4 p0, 0x0

    throw p0

    :cond_2
    new-instance p0, Ljava/lang/IllegalArgumentException;

    const-string p1, "DiskLruCache and Context can\'t be null !!!"

    invoke-direct {p0, p1}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p0
.end method

.method public static Fj(Z)V
    .locals 0

    sput-boolean p0, Lcom/bykv/vk/openvk/component/video/Fj/ex/Ubf;->Ubf:Z

    return-void
.end method

.method public static synthetic eV()Lcom/bykv/vk/openvk/component/video/Fj/ex/ex/hjc;
    .locals 1

    sget-object v0, Lcom/bykv/vk/openvk/component/video/Fj/ex/Ubf;->Ko:Lcom/bykv/vk/openvk/component/video/Fj/ex/ex/hjc;

    return-object v0
.end method

.method public static ex()Lcom/bykv/vk/openvk/component/video/Fj/ex/Fj/hjc;
    .locals 1

    sget-object v0, Lcom/bykv/vk/openvk/component/video/Fj/ex/Ubf;->ex:Lcom/bykv/vk/openvk/component/video/Fj/ex/Fj/hjc;

    return-object v0
.end method

.method public static ex(Z)V
    .locals 0

    sput-boolean p0, Lcom/bykv/vk/openvk/component/video/Fj/ex/Ubf;->WR:Z

    return-void
.end method

.method public static hjc()Lcom/bykv/vk/openvk/component/video/Fj/ex/Fj/ex;
    .locals 1

    sget-object v0, Lcom/bykv/vk/openvk/component/video/Fj/ex/Ubf;->Fj:Lcom/bykv/vk/openvk/component/video/Fj/ex/Fj/ex;

    return-object v0
.end method
