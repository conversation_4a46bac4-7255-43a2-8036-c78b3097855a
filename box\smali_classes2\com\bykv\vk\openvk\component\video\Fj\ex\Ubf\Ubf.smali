.class public Lcom/bykv/vk/openvk/component/video/Fj/ex/Ubf/Ubf;
.super Ljava/lang/Object;


# instance fields
.field public Fj:I

.field public Ubf:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field public eV:J

.field public ex:Ljava/lang/String;

.field public hjc:J


# direct methods
.method public constructor <init>()V
    .locals 2

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const-wide/16 v0, -0x1

    iput-wide v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/ex/Ubf/Ubf;->hjc:J

    iput-wide v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/ex/Ubf/Ubf;->eV:J

    return-void
.end method
