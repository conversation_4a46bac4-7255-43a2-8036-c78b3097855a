.class public Lcom/bykv/vk/openvk/component/video/Fj/ex/Ubf/WR;
.super Lcom/bykv/vk/openvk/component/video/Fj/ex/Ubf/Fj;


# instance fields
.field private hjc:Lcom/bytedance/sdk/component/ex/Fj/JW;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/component/ex/Fj/JW;Lcom/bykv/vk/openvk/component/video/Fj/ex/Ubf/Ubf;)V
    .locals 5

    invoke-direct {p0}, Lcom/bykv/vk/openvk/component/video/Fj/ex/Ubf/Fj;-><init>()V

    iput-object p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/ex/Ubf/WR;->hjc:Lcom/bytedance/sdk/component/ex/Fj/JW;

    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/ex/Ubf/Fj;->Fj:Ljava/util/List;

    if-eqz p1, :cond_0

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/ex/Fj/JW;->svN()Lcom/bytedance/sdk/component/ex/Fj/WR;

    move-result-object p1

    if-eqz p1, :cond_0

    const/4 v0, 0x0

    :goto_0
    invoke-virtual {p1}, Lcom/bytedance/sdk/component/ex/Fj/WR;->Fj()I

    move-result v1

    if-ge v0, v1, :cond_0

    iget-object v1, p0, Lcom/bykv/vk/openvk/component/video/Fj/ex/Ubf/Fj;->Fj:Ljava/util/List;

    new-instance v2, Lcom/bykv/vk/openvk/component/video/Fj/ex/mSE$ex;

    invoke-virtual {p1, v0}, Lcom/bytedance/sdk/component/ex/Fj/WR;->Fj(I)Ljava/lang/String;

    move-result-object v3

    invoke-virtual {p1, v0}, Lcom/bytedance/sdk/component/ex/Fj/WR;->ex(I)Ljava/lang/String;

    move-result-object v4

    invoke-direct {v2, v3, v4}, Lcom/bykv/vk/openvk/component/video/Fj/ex/mSE$ex;-><init>(Ljava/lang/String;Ljava/lang/String;)V

    invoke-interface {v1, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :cond_0
    iput-object p2, p0, Lcom/bykv/vk/openvk/component/video/Fj/ex/Ubf/Fj;->ex:Lcom/bykv/vk/openvk/component/video/Fj/ex/Ubf/Ubf;

    return-void
.end method


# virtual methods
.method public Fj()I
    .locals 1

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/ex/Ubf/WR;->hjc:Lcom/bytedance/sdk/component/ex/Fj/JW;

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/ex/Fj/JW;->hjc()I

    move-result v0

    return v0
.end method

.method public Fj(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;
    .locals 1

    invoke-virtual {p0, p1}, Lcom/bykv/vk/openvk/component/video/Fj/ex/Ubf/Fj;->Fj(Ljava/lang/String;)Lcom/bykv/vk/openvk/component/video/Fj/ex/mSE$ex;

    move-result-object v0

    if-eqz v0, :cond_0

    invoke-virtual {p0, p1}, Lcom/bykv/vk/openvk/component/video/Fj/ex/Ubf/Fj;->Fj(Ljava/lang/String;)Lcom/bykv/vk/openvk/component/video/Fj/ex/mSE$ex;

    move-result-object p1

    iget-object p1, p1, Lcom/bykv/vk/openvk/component/video/Fj/ex/mSE$ex;->ex:Ljava/lang/String;

    return-object p1

    :cond_0
    return-object p2
.end method

.method public Ubf()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/ex/Ubf/WR;->hjc:Lcom/bytedance/sdk/component/ex/Fj/JW;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/ex/Fj/JW;->BcC()Lcom/bytedance/sdk/component/ex/Fj/UYd;

    move-result-object v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/ex/Ubf/WR;->hjc:Lcom/bytedance/sdk/component/ex/Fj/JW;

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/ex/Fj/JW;->BcC()Lcom/bytedance/sdk/component/ex/Fj/UYd;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/ex/Fj/UYd;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0

    :cond_0
    const-string v0, "http/1.1"

    return-object v0
.end method

.method public WR()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/ex/Ubf/WR;->hjc:Lcom/bytedance/sdk/component/ex/Fj/JW;

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/ex/Fj/JW;->hjc()I

    move-result v0

    invoke-virtual {p0, v0}, Lcom/bykv/vk/openvk/component/video/Fj/ex/Ubf/Fj;->Fj(I)Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public eV()Ljava/io/InputStream;
    .locals 1

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/ex/Ubf/WR;->hjc:Lcom/bytedance/sdk/component/ex/Fj/JW;

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/ex/Fj/JW;->WR()Lcom/bytedance/sdk/component/ex/Fj/JU;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/ex/Fj/JU;->hjc()Ljava/io/InputStream;

    move-result-object v0

    return-object v0
.end method

.method public ex()Z
    .locals 2

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/ex/Ubf/WR;->hjc:Lcom/bytedance/sdk/component/ex/Fj/JW;

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/ex/Fj/JW;->hjc()I

    move-result v0

    const/16 v1, 0xc8

    if-lt v0, v1, :cond_0

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/ex/Ubf/WR;->hjc:Lcom/bytedance/sdk/component/ex/Fj/JW;

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/ex/Fj/JW;->hjc()I

    move-result v0

    const/16 v1, 0x12c

    if-ge v0, v1, :cond_0

    const/4 v0, 0x1

    return v0

    :cond_0
    const/4 v0, 0x0

    return v0
.end method

.method public hjc()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lcom/bykv/vk/openvk/component/video/Fj/ex/mSE$ex;",
            ">;"
        }
    .end annotation

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/ex/Ubf/Fj;->Fj:Ljava/util/List;

    return-object v0
.end method
