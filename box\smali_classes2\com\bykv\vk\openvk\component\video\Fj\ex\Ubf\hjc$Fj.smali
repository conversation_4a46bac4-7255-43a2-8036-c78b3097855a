.class final Lcom/bykv/vk/openvk/component/video/Fj/ex/Ubf/hjc$Fj;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bykv/vk/openvk/component/video/Fj/ex/Ubf/hjc;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "Fj"
.end annotation


# static fields
.field private static final Fj:Lcom/bykv/vk/openvk/component/video/Fj/ex/Ubf/hjc;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    new-instance v0, Lcom/bykv/vk/openvk/component/video/Fj/ex/Ubf/hjc;

    invoke-direct {v0}, Lcom/bykv/vk/openvk/component/video/Fj/ex/Ubf/hjc;-><init>()V

    sput-object v0, Lcom/bykv/vk/openvk/component/video/Fj/ex/Ubf/hjc$Fj;->Fj:Lcom/bykv/vk/openvk/component/video/Fj/ex/Ubf/hjc;

    return-void
.end method

.method public static synthetic Fj()Lcom/bykv/vk/openvk/component/video/Fj/ex/Ubf/hjc;
    .locals 1

    sget-object v0, Lcom/bykv/vk/openvk/component/video/Fj/ex/Ubf/hjc$Fj;->Fj:Lcom/bykv/vk/openvk/component/video/Fj/ex/Ubf/hjc;

    return-object v0
.end method
