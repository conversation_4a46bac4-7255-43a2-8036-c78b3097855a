.class public Lcom/bykv/vk/openvk/component/video/Fj/ex/Ubf/hjc;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/bykv/vk/openvk/component/video/Fj/ex/Ubf/hjc$Fj;
    }
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static Fj()Lcom/bykv/vk/openvk/component/video/Fj/ex/Ubf/hjc;
    .locals 1

    invoke-static {}, Lcom/bykv/vk/openvk/component/video/Fj/ex/Ubf/hjc$Fj;->Fj()Lcom/bykv/vk/openvk/component/video/Fj/ex/Ubf/hjc;

    move-result-object v0

    return-object v0
.end method


# virtual methods
.method public ex()Lcom/bykv/vk/openvk/component/video/Fj/ex/Ubf/ex;
    .locals 1

    new-instance v0, Lcom/bykv/vk/openvk/component/video/Fj/ex/Ubf/eV;

    invoke-direct {v0}, Lcom/bykv/vk/openvk/component/video/Fj/ex/Ubf/eV;-><init>()V

    return-object v0
.end method
