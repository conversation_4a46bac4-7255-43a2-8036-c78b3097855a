.class Lcom/bykv/vk/openvk/component/video/Fj/ex/WR$2$1;
.super Lcom/bytedance/sdk/component/svN/BcC;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bykv/vk/openvk/component/video/Fj/ex/WR$2;->run()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bykv/vk/openvk/component/video/Fj/ex/svN;

.field final synthetic ex:Lcom/bykv/vk/openvk/component/video/Fj/ex/WR$2;


# direct methods
.method public constructor <init>(Lcom/bykv/vk/openvk/component/video/Fj/ex/WR$2;Ljava/lang/String;ILcom/bykv/vk/openvk/component/video/Fj/ex/svN;)V
    .locals 0

    iput-object p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/ex/WR$2$1;->ex:Lcom/bykv/vk/openvk/component/video/Fj/ex/WR$2;

    iput-object p4, p0, Lcom/bykv/vk/openvk/component/video/Fj/ex/WR$2$1;->Fj:Lcom/bykv/vk/openvk/component/video/Fj/ex/svN;

    invoke-direct {p0, p2, p3}, Lcom/bytedance/sdk/component/svN/BcC;-><init>(Ljava/lang/String;I)V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 1

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/ex/WR$2$1;->Fj:Lcom/bykv/vk/openvk/component/video/Fj/ex/svN;

    invoke-virtual {v0}, Lcom/bykv/vk/openvk/component/video/Fj/ex/svN;->run()V

    return-void
.end method
