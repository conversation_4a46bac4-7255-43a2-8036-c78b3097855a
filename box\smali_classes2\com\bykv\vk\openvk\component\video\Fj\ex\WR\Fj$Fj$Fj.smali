.class Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj$Fj$Fj;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj$Fj;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = "Fj"
.end annotation


# instance fields
.field public Fj:I

.field public Ubf:Ljava/lang/String;

.field public WR:Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;

.field public eV:I

.field public ex:Ljava/lang/String;

.field public hjc:[Ljava/lang/String;

.field final synthetic svN:Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj$Fj;


# direct methods
.method public constructor <init>(Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj$Fj;)V
    .locals 0

    iput-object p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj$Fj$Fj;->svN:Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj$Fj;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
