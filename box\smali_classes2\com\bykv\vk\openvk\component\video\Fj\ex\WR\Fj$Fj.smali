.class Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj$Fj;
.super Ljava/lang/Thread;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = "Fj"
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj$Fj$Fj;
    }
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj;

.field private Ubf:Ljava/util/Queue;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Queue<",
            "Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj$Fj$Fj;",
            ">;"
        }
    .end annotation
.end field

.field private final eV:Ljava/util/Queue;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Queue<",
            "Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj$Fj$Fj;",
            ">;"
        }
    .end annotation
.end field

.field private ex:Ljava/util/Queue;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Queue<",
            "Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj$Fj$Fj;",
            ">;"
        }
    .end annotation
.end field

.field private hjc:Z


# direct methods
.method public constructor <init>(Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj;)V
    .locals 1

    iput-object p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj$Fj;->Fj:Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj;

    invoke-direct {p0}, Ljava/lang/Thread;-><init>()V

    new-instance p1, Ljava/util/concurrent/ArrayBlockingQueue;

    const/16 v0, 0xa

    invoke-direct {p1, v0}, Ljava/util/concurrent/ArrayBlockingQueue;-><init>(I)V

    iput-object p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj$Fj;->eV:Ljava/util/Queue;

    new-instance p1, Ljava/util/concurrent/LinkedBlockingQueue;

    invoke-direct {p1}, Ljava/util/concurrent/LinkedBlockingQueue;-><init>()V

    iput-object p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj$Fj;->ex:Ljava/util/Queue;

    const/4 p1, 0x1

    iput-boolean p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj$Fj;->hjc:Z

    new-instance p1, Ljava/util/concurrent/LinkedBlockingQueue;

    invoke-direct {p1}, Ljava/util/concurrent/LinkedBlockingQueue;-><init>()V

    iput-object p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj$Fj;->Ubf:Ljava/util/Queue;

    return-void
.end method

.method private Fj(ILcom/bykv/vk/openvk/component/video/api/hjc/hjc;)Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj$Fj$Fj;
    .locals 1

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj$Fj;->eV:Ljava/util/Queue;

    invoke-interface {v0}, Ljava/util/Collection;->size()I

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj$Fj;->eV:Ljava/util/Queue;

    invoke-interface {v0}, Ljava/util/Queue;->poll()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj$Fj$Fj;

    if-nez v0, :cond_0

    new-instance v0, Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj$Fj$Fj;

    invoke-direct {v0, p0}, Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj$Fj$Fj;-><init>(Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj$Fj;)V

    :cond_0
    iput p1, v0, Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj$Fj$Fj;->Fj:I

    iput-object p2, v0, Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj$Fj$Fj;->WR:Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;

    return-object v0
.end method

.method private Fj()V
    .locals 2

    :goto_0
    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj$Fj;->Ubf:Ljava/util/Queue;

    invoke-interface {v0}, Ljava/util/Queue;->poll()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj$Fj$Fj;

    if-eqz v0, :cond_2

    iget-object v1, v0, Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj$Fj$Fj;->WR:Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;

    invoke-virtual {v1}, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->dG()Ljava/lang/String;

    move-result-object v1

    iput-object v1, v0, Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj$Fj$Fj;->ex:Ljava/lang/String;

    iget-object v1, v0, Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj$Fj$Fj;->WR:Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;

    invoke-virtual {v1}, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->dG()Ljava/lang/String;

    move-result-object v1

    filled-new-array {v1}, [Ljava/lang/String;

    move-result-object v1

    iput-object v1, v0, Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj$Fj$Fj;->hjc:[Ljava/lang/String;

    iget-object v1, v0, Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj$Fj$Fj;->WR:Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;

    invoke-virtual {v1}, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->Fj()I

    move-result v1

    if-lez v1, :cond_0

    goto :goto_1

    :cond_0
    iget-object v1, v0, Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj$Fj$Fj;->WR:Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;

    invoke-virtual {v1}, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->hjc()I

    move-result v1

    :goto_1
    iput v1, v0, Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj$Fj$Fj;->eV:I

    iget-object v1, v0, Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj$Fj$Fj;->WR:Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;

    invoke-virtual {v1}, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->Tc()Ljava/lang/String;

    move-result-object v1

    iput-object v1, v0, Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj$Fj$Fj;->Ubf:Ljava/lang/String;

    iget-object v1, v0, Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj$Fj$Fj;->WR:Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;

    invoke-virtual {v1}, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->Tc()Ljava/lang/String;

    move-result-object v1

    invoke-static {v1}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v1

    if-nez v1, :cond_1

    iget-object v1, v0, Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj$Fj$Fj;->WR:Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;

    invoke-virtual {v1}, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->Tc()Ljava/lang/String;

    move-result-object v1

    iput-object v1, v0, Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj$Fj$Fj;->ex:Ljava/lang/String;

    :cond_1
    const/4 v1, 0x0

    iput-object v1, v0, Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj$Fj$Fj;->WR:Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;

    invoke-direct {p0, v0}, Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj$Fj;->hjc(Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj$Fj$Fj;)V

    goto :goto_0

    :cond_2
    return-void
.end method

.method private Fj(Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj$Fj$Fj;)V
    .locals 2

    const/4 v0, 0x0

    iput-object v0, p1, Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj$Fj$Fj;->hjc:[Ljava/lang/String;

    iput-object v0, p1, Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj$Fj$Fj;->ex:Ljava/lang/String;

    const/4 v1, -0x1

    iput v1, p1, Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj$Fj$Fj;->Fj:I

    iput-object v0, p1, Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj$Fj$Fj;->WR:Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj$Fj;->eV:Ljava/util/Queue;

    invoke-interface {v0, p1}, Ljava/util/Queue;->offer(Ljava/lang/Object;)Z

    return-void
.end method

.method private declared-synchronized ex(Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj$Fj$Fj;)V
    .locals 1

    monitor-enter p0

    :try_start_0
    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj$Fj;->Ubf:Ljava/util/Queue;

    invoke-interface {v0, p1}, Ljava/util/Queue;->add(Ljava/lang/Object;)Z

    invoke-virtual {p0}, Ljava/lang/Object;->notify()V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    monitor-exit p0

    return-void

    :catchall_0
    move-exception p1

    monitor-exit p0

    throw p1
.end method

.method private hjc(Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj$Fj$Fj;)V
    .locals 1

    if-nez p1, :cond_0

    return-void

    :cond_0
    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj$Fj;->ex:Ljava/util/Queue;

    invoke-interface {v0, p1}, Ljava/util/Queue;->offer(Ljava/lang/Object;)Z

    invoke-virtual {p0}, Ljava/lang/Object;->notify()V

    return-void
.end method


# virtual methods
.method public Fj(Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;)V
    .locals 1

    const/4 v0, 0x0

    invoke-direct {p0, v0, p1}, Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj$Fj;->Fj(ILcom/bykv/vk/openvk/component/video/api/hjc/hjc;)Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj$Fj$Fj;

    move-result-object p1

    invoke-direct {p0, p1}, Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj$Fj;->ex(Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj$Fj$Fj;)V

    return-void
.end method

.method public run()V
    .locals 10

    :goto_0
    iget-boolean v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj$Fj;->hjc:Z

    if-eqz v0, :cond_b

    monitor-enter p0

    :try_start_0
    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj$Fj;->Ubf:Ljava/util/Queue;

    invoke-interface {v0}, Ljava/util/Collection;->isEmpty()Z

    move-result v0

    if-nez v0, :cond_0

    invoke-direct {p0}, Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj$Fj;->Fj()V

    goto :goto_1

    :catchall_0
    move-exception v0

    goto/16 :goto_4

    :cond_0
    :goto_1
    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj$Fj;->ex:Ljava/util/Queue;

    invoke-interface {v0}, Ljava/util/Collection;->isEmpty()Z

    move-result v0

    if-nez v0, :cond_a

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj$Fj;->ex:Ljava/util/Queue;

    invoke-interface {v0}, Ljava/util/Queue;->poll()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj$Fj$Fj;

    if-eqz v0, :cond_0

    iget v1, v0, Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj$Fj$Fj;->Fj:I

    const/4 v2, 0x0

    const/4 v3, 0x1

    if-eqz v1, :cond_6

    if-eq v1, v3, :cond_5

    const/4 v3, 0x2

    if-eq v1, v3, :cond_4

    const/4 v3, 0x3

    if-eq v1, v3, :cond_2

    const/4 v3, 0x4

    if-eq v1, v3, :cond_1

    goto/16 :goto_3

    :cond_1
    invoke-static {}, Lcom/bykv/vk/openvk/component/video/Fj/ex/eV;->hjc()Lcom/bykv/vk/openvk/component/video/Fj/ex/eV;

    move-result-object v1

    invoke-virtual {v1}, Lcom/bykv/vk/openvk/component/video/Fj/ex/eV;->eV()V

    iput-boolean v2, p0, Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj$Fj;->hjc:Z

    goto/16 :goto_3

    :cond_2
    invoke-static {}, Lcom/bykv/vk/openvk/component/video/Fj/ex/eV;->hjc()Lcom/bykv/vk/openvk/component/video/Fj/ex/eV;

    move-result-object v1

    invoke-virtual {v1}, Lcom/bykv/vk/openvk/component/video/Fj/ex/eV;->eV()V

    invoke-static {}, Lcom/bykv/vk/openvk/component/video/Fj/ex/Ubf;->hjc()Lcom/bykv/vk/openvk/component/video/Fj/ex/Fj/ex;

    move-result-object v1

    if-nez v1, :cond_3

    invoke-static {}, Lcom/bykv/vk/openvk/component/video/Fj/ex/Ubf;->ex()Lcom/bykv/vk/openvk/component/video/Fj/ex/Fj/hjc;

    move-result-object v1

    if-eqz v1, :cond_9

    invoke-static {}, Lcom/bykv/vk/openvk/component/video/Fj/ex/Ubf;->ex()Lcom/bykv/vk/openvk/component/video/Fj/ex/Fj/hjc;

    move-result-object v1

    invoke-virtual {v1}, Lcom/bykv/vk/openvk/component/video/Fj/ex/Fj/hjc;->Fj()V

    goto :goto_3

    :cond_3
    invoke-static {}, Lcom/bykv/vk/openvk/component/video/Fj/ex/Ubf;->hjc()Lcom/bykv/vk/openvk/component/video/Fj/ex/Fj/ex;

    const/4 v0, 0x0

    throw v0

    :cond_4
    invoke-static {}, Lcom/bykv/vk/openvk/component/video/Fj/ex/eV;->hjc()Lcom/bykv/vk/openvk/component/video/Fj/ex/eV;

    move-result-object v1

    invoke-virtual {v1}, Lcom/bykv/vk/openvk/component/video/Fj/ex/eV;->eV()V

    goto :goto_3

    :cond_5
    invoke-static {}, Lcom/bykv/vk/openvk/component/video/Fj/ex/eV;->hjc()Lcom/bykv/vk/openvk/component/video/Fj/ex/eV;

    move-result-object v1

    iget-object v2, v0, Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj$Fj$Fj;->ex:Ljava/lang/String;

    invoke-virtual {v1, v2}, Lcom/bykv/vk/openvk/component/video/Fj/ex/eV;->Fj(Ljava/lang/String;)V

    goto :goto_3

    :cond_6
    iget-object v1, v0, Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj$Fj$Fj;->hjc:[Ljava/lang/String;

    if-eqz v1, :cond_9

    array-length v1, v1

    if-lez v1, :cond_9

    new-instance v1, Ljava/util/ArrayList;

    invoke-direct {v1}, Ljava/util/ArrayList;-><init>()V

    iget-object v4, v0, Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj$Fj$Fj;->hjc:[Ljava/lang/String;

    array-length v5, v4

    :goto_2
    if-ge v2, v5, :cond_8

    aget-object v6, v4, v2

    invoke-static {v6}, Lcom/bykv/vk/openvk/component/video/Fj/hjc/Fj;->Fj(Ljava/lang/String;)Z

    move-result v7

    if-eqz v7, :cond_7

    invoke-virtual {v1, v6}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    :cond_7
    add-int/lit8 v2, v2, 0x1

    goto :goto_2

    :cond_8
    invoke-virtual {v1}, Ljava/util/ArrayList;->size()I

    move-result v2

    new-array v2, v2, [Ljava/lang/String;

    invoke-virtual {v1, v2}, Ljava/util/ArrayList;->toArray([Ljava/lang/Object;)[Ljava/lang/Object;

    move-result-object v1

    move-object v9, v1

    check-cast v9, [Ljava/lang/String;

    iget-object v1, v0, Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj$Fj$Fj;->Ubf:Ljava/lang/String;

    invoke-static {v1}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v1

    xor-int/lit8 v6, v1, 0x1

    invoke-static {}, Lcom/bykv/vk/openvk/component/video/Fj/ex/eV;->hjc()Lcom/bykv/vk/openvk/component/video/Fj/ex/eV;

    move-result-object v4

    const/4 v5, 0x0

    iget v7, v0, Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj$Fj$Fj;->eV:I

    iget-object v8, v0, Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj$Fj$Fj;->ex:Ljava/lang/String;

    invoke-virtual/range {v4 .. v9}, Lcom/bykv/vk/openvk/component/video/Fj/ex/eV;->Fj(ZZILjava/lang/String;[Ljava/lang/String;)V

    :cond_9
    :goto_3
    invoke-direct {p0, v0}, Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj$Fj;->Fj(Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj$Fj$Fj;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    goto/16 :goto_1

    :cond_a
    :try_start_1
    invoke-virtual {p0}, Ljava/lang/Object;->wait()V
    :try_end_1
    .catch Ljava/lang/InterruptedException; {:try_start_1 .. :try_end_1} :catch_0
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    :catch_0
    :try_start_2
    monitor-exit p0
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_0

    goto/16 :goto_0

    :goto_4
    monitor-exit p0

    throw v0

    :cond_b
    return-void
.end method
