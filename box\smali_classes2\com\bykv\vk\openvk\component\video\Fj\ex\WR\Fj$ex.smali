.class Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj$ex;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "ex"
.end annotation


# static fields
.field private static final Fj:Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj;


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj;-><init>(Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj$1;)V

    sput-object v0, Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj$ex;->Fj:Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj;

    return-void
.end method

.method public static synthetic Fj()Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj;
    .locals 1

    sget-object v0, Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj$ex;->Fj:Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj;

    return-object v0
.end method
