.class public Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj$Fj;,
        Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj$ex;
    }
.end annotation


# static fields
.field public static final Fj:Z


# instance fields
.field private ex:Ljava/util/HashMap;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/HashMap<",
            "Ljava/lang/String;",
            "Ljava/lang/Boolean;",
            ">;"
        }
    .end annotation
.end field

.field private hjc:Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj$Fj;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    invoke-static {}, Lcom/bykv/vk/openvk/component/video/api/WR/hjc;->hjc()Z

    move-result v0

    sput-boolean v0, Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj;->Fj:Z

    return-void
.end method

.method private constructor <init>()V
    .locals 1

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    iput-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj;->ex:Ljava/util/HashMap;

    invoke-virtual {p0}, Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj;->ex()Z

    return-void
.end method

.method public synthetic constructor <init>(Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj$1;)V
    .locals 0

    invoke-direct {p0}, Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj;-><init>()V

    return-void
.end method

.method public static Fj()Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj;
    .locals 1

    invoke-static {}, Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj$ex;->Fj()Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj;

    move-result-object v0

    return-object v0
.end method

.method private static hjc()Lcom/bykv/vk/openvk/component/video/Fj/ex/Fj/hjc;
    .locals 4

    new-instance v0, Ljava/io/File;

    invoke-static {}, Lcom/bykv/vk/openvk/component/video/api/hjc;->Fj()Landroid/content/Context;

    move-result-object v1

    invoke-virtual {v1}, Landroid/content/Context;->getCacheDir()Ljava/io/File;

    move-result-object v1

    const-string v2, "proxy_cache"

    invoke-direct {v0, v1, v2}, Ljava/io/File;-><init>(Ljava/io/File;Ljava/lang/String;)V

    invoke-virtual {v0}, Ljava/io/File;->exists()Z

    move-result v1

    if-nez v1, :cond_0

    invoke-virtual {v0}, Ljava/io/File;->mkdirs()Z

    :cond_0
    :try_start_0
    new-instance v1, Lcom/bykv/vk/openvk/component/video/Fj/ex/Fj/hjc;

    invoke-direct {v1, v0}, Lcom/bykv/vk/openvk/component/video/Fj/ex/Fj/hjc;-><init>(Ljava/io/File;)V
    :try_end_0
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_0

    const-wide/32 v2, 0x6400000

    :try_start_1
    invoke-virtual {v1, v2, v3}, Lcom/bykv/vk/openvk/component/video/Fj/ex/Fj/hjc;->Fj(J)V
    :try_end_1
    .catch Ljava/io/IOException; {:try_start_1 .. :try_end_1} :catch_1

    goto :goto_0

    :catch_0
    const/4 v1, 0x0

    :catch_1
    :goto_0
    return-object v1
.end method


# virtual methods
.method public Fj(Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;)Z
    .locals 1

    invoke-virtual {p0}, Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj;->ex()Z

    move-result v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj;->hjc:Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj$Fj;

    invoke-virtual {v0, p1}, Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj$Fj;->Fj(Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;)V

    const/4 p1, 0x1

    return p1

    :cond_0
    const/4 p1, 0x0

    return p1
.end method

.method public ex(Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;)Ljava/lang/String;
    .locals 4

    if-nez p1, :cond_0

    const/4 p1, 0x0

    return-object p1

    :cond_0
    invoke-virtual {p1}, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->Tc()Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    xor-int/lit8 v0, v0, 0x1

    if-eqz v0, :cond_1

    invoke-virtual {p1}, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->Tc()Ljava/lang/String;

    move-result-object v1

    goto :goto_0

    :cond_1
    invoke-virtual {p1}, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->dG()Ljava/lang/String;

    move-result-object v1

    :goto_0
    invoke-static {}, Lcom/bykv/vk/openvk/component/video/Fj/ex/WR;->Fj()Lcom/bykv/vk/openvk/component/video/Fj/ex/WR;

    move-result-object v2

    invoke-virtual {p1}, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->dG()Ljava/lang/String;

    move-result-object p1

    filled-new-array {p1}, [Ljava/lang/String;

    move-result-object p1

    const/4 v3, 0x0

    invoke-virtual {v2, v3, v0, v1, p1}, Lcom/bykv/vk/openvk/component/video/Fj/ex/WR;->Fj(ZZLjava/lang/String;[Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method public ex()Z
    .locals 5

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj;->hjc:Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj$Fj;

    const/4 v1, 0x1

    if-eqz v0, :cond_0

    return v1

    :cond_0
    invoke-static {}, Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj;->hjc()Lcom/bykv/vk/openvk/component/video/Fj/ex/Fj/hjc;

    move-result-object v0

    const/4 v2, 0x0

    if-nez v0, :cond_1

    return v2

    :cond_1
    invoke-static {v1}, Lcom/bykv/vk/openvk/component/video/Fj/ex/Ubf;->Fj(Z)V

    invoke-static {v1}, Lcom/bykv/vk/openvk/component/video/Fj/ex/Ubf;->ex(Z)V

    invoke-static {v1}, Lcom/bykv/vk/openvk/component/video/Fj/ex/Ubf;->Fj(I)V

    invoke-static {}, Lcom/bykv/vk/openvk/component/video/Fj/ex/WR;->Fj()Lcom/bykv/vk/openvk/component/video/Fj/ex/WR;

    move-result-object v3

    invoke-virtual {v3}, Lcom/bykv/vk/openvk/component/video/Fj/ex/WR;->eV()V

    :try_start_0
    new-instance v3, Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj$Fj;

    invoke-direct {v3, p0}, Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj$Fj;-><init>(Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj;)V

    iput-object v3, p0, Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj;->hjc:Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj$Fj;

    const-string v4, "csj_video_cache_preloader"

    invoke-virtual {v3, v4}, Ljava/lang/Thread;->setName(Ljava/lang/String;)V

    iget-object v3, p0, Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj;->hjc:Lcom/bykv/vk/openvk/component/video/Fj/ex/WR/Fj$Fj;

    invoke-virtual {v3}, Ljava/lang/Thread;->start()V

    invoke-static {}, Lcom/bykv/vk/openvk/component/video/api/hjc;->Fj()Landroid/content/Context;

    move-result-object v3

    invoke-static {v0, v3}, Lcom/bykv/vk/openvk/component/video/Fj/ex/Ubf;->Fj(Lcom/bykv/vk/openvk/component/video/Fj/ex/Fj/hjc;Landroid/content/Context;)V

    invoke-static {}, Lcom/bykv/vk/openvk/component/video/Fj/ex/eV;->hjc()Lcom/bykv/vk/openvk/component/video/Fj/ex/eV;
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    invoke-static {}, Lcom/bykv/vk/openvk/component/video/Fj/ex/eV;->hjc()Lcom/bykv/vk/openvk/component/video/Fj/ex/eV;

    move-result-object v0

    const v2, 0x9fffff

    invoke-virtual {v0, v2}, Lcom/bykv/vk/openvk/component/video/Fj/ex/eV;->Fj(I)V

    return v1

    :catch_0
    return v2
.end method
