.class Lcom/bykv/vk/openvk/component/video/Fj/ex/eV$1;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bykv/vk/openvk/component/video/Fj/ex/ex$ex;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bykv/vk/openvk/component/video/Fj/ex/eV;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bykv/vk/openvk/component/video/Fj/ex/eV;


# direct methods
.method public constructor <init>(Lcom/bykv/vk/openvk/component/video/Fj/ex/eV;)V
    .locals 0

    iput-object p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/ex/eV$1;->Fj:Lcom/bykv/vk/openvk/component/video/Fj/ex/eV;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public Fj(Lcom/bykv/vk/openvk/component/video/Fj/ex/ex;)V
    .locals 3

    invoke-virtual {p1}, Lcom/bykv/vk/openvk/component/video/Fj/ex/Fj;->WR()I

    move-result v0

    iget-object v1, p0, Lcom/bykv/vk/openvk/component/video/Fj/ex/eV$1;->Fj:Lcom/bykv/vk/openvk/component/video/Fj/ex/eV;

    invoke-static {v1}, Lcom/bykv/vk/openvk/component/video/Fj/ex/eV;->Fj(Lcom/bykv/vk/openvk/component/video/Fj/ex/eV;)Landroid/util/SparseArray;

    move-result-object v1

    monitor-enter v1

    :try_start_0
    iget-object v2, p0, Lcom/bykv/vk/openvk/component/video/Fj/ex/eV$1;->Fj:Lcom/bykv/vk/openvk/component/video/Fj/ex/eV;

    invoke-static {v2}, Lcom/bykv/vk/openvk/component/video/Fj/ex/eV;->Fj(Lcom/bykv/vk/openvk/component/video/Fj/ex/eV;)Landroid/util/SparseArray;

    move-result-object v2

    invoke-virtual {v2, v0}, Landroid/util/SparseArray;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/util/Map;

    if-eqz v0, :cond_0

    iget-object v2, p1, Lcom/bykv/vk/openvk/component/video/Fj/ex/Fj;->BcC:Ljava/lang/String;

    invoke-interface {v0, v2}, Ljava/util/Map;->remove(Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_0

    :catchall_0
    move-exception p1

    goto :goto_1

    :cond_0
    :goto_0
    monitor-exit v1
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    sget-boolean v0, Lcom/bykv/vk/openvk/component/video/Fj/ex/Ubf;->hjc:Z

    if-eqz v0, :cond_1

    new-instance v0, Ljava/lang/StringBuilder;

    const-string v1, "afterExecute, key: "

    invoke-direct {v0, v1}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    iget-object p1, p1, Lcom/bykv/vk/openvk/component/video/Fj/ex/Fj;->BcC:Ljava/lang/String;

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    :cond_1
    return-void

    :goto_1
    monitor-exit v1

    throw p1
.end method
