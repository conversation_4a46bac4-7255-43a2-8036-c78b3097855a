.class final Lcom/bykv/vk/openvk/component/video/Fj/ex/ex$Fj;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bykv/vk/openvk/component/video/Fj/ex/ex;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "Fj"
.end annotation


# instance fields
.field BcC:Lcom/bykv/vk/openvk/component/video/Fj/ex/mSE;

.field Fj:Ljava/lang/String;

.field Ko:Ljava/lang/Object;

.field Ubf:Lcom/bykv/vk/openvk/component/video/Fj/ex/ex/hjc;

.field WR:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lcom/bykv/vk/openvk/component/video/Fj/ex/mSE$ex;",
            ">;"
        }
    .end annotation
.end field

.field eV:Lcom/bykv/vk/openvk/component/video/Fj/ex/Fj/Fj;

.field ex:Ljava/lang/String;

.field hjc:Lcom/bykv/vk/openvk/component/video/Fj/ex/UYd;

.field mSE:Lcom/bykv/vk/openvk/component/video/Fj/ex/ex$ex;

.field svN:I


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public Fj(I)Lcom/bykv/vk/openvk/component/video/Fj/ex/ex$Fj;
    .locals 0

    iput p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/ex/ex$Fj;->svN:I

    return-object p0
.end method

.method public Fj(Lcom/bykv/vk/openvk/component/video/Fj/ex/Fj/Fj;)Lcom/bykv/vk/openvk/component/video/Fj/ex/ex$Fj;
    .locals 1

    if-eqz p1, :cond_0

    iput-object p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/ex/ex$Fj;->eV:Lcom/bykv/vk/openvk/component/video/Fj/ex/Fj/Fj;

    return-object p0

    :cond_0
    new-instance p1, Ljava/lang/IllegalArgumentException;

    const-string v0, "cache == null"

    invoke-direct {p1, v0}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public Fj(Lcom/bykv/vk/openvk/component/video/Fj/ex/UYd;)Lcom/bykv/vk/openvk/component/video/Fj/ex/ex$Fj;
    .locals 1

    if-eqz p1, :cond_0

    iput-object p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/ex/ex$Fj;->hjc:Lcom/bykv/vk/openvk/component/video/Fj/ex/UYd;

    return-object p0

    :cond_0
    new-instance p1, Ljava/lang/IllegalArgumentException;

    const-string v0, "urls is empty"

    invoke-direct {p1, v0}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public Fj(Lcom/bykv/vk/openvk/component/video/Fj/ex/ex$ex;)Lcom/bykv/vk/openvk/component/video/Fj/ex/ex$Fj;
    .locals 0

    iput-object p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/ex/ex$Fj;->mSE:Lcom/bykv/vk/openvk/component/video/Fj/ex/ex$ex;

    return-object p0
.end method

.method public Fj(Lcom/bykv/vk/openvk/component/video/Fj/ex/ex/hjc;)Lcom/bykv/vk/openvk/component/video/Fj/ex/ex$Fj;
    .locals 1

    if-eqz p1, :cond_0

    iput-object p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/ex/ex$Fj;->Ubf:Lcom/bykv/vk/openvk/component/video/Fj/ex/ex/hjc;

    return-object p0

    :cond_0
    new-instance p1, Ljava/lang/IllegalArgumentException;

    const-string v0, "db == null"

    invoke-direct {p1, v0}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public Fj(Lcom/bykv/vk/openvk/component/video/Fj/ex/mSE;)Lcom/bykv/vk/openvk/component/video/Fj/ex/ex$Fj;
    .locals 0

    iput-object p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/ex/ex$Fj;->BcC:Lcom/bykv/vk/openvk/component/video/Fj/ex/mSE;

    return-object p0
.end method

.method public Fj(Ljava/lang/Object;)Lcom/bykv/vk/openvk/component/video/Fj/ex/ex$Fj;
    .locals 0

    iput-object p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/ex/ex$Fj;->Ko:Ljava/lang/Object;

    return-object p0
.end method

.method public Fj(Ljava/lang/String;)Lcom/bykv/vk/openvk/component/video/Fj/ex/ex$Fj;
    .locals 1

    invoke-static {p1}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    if-nez v0, :cond_0

    iput-object p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/ex/ex$Fj;->Fj:Ljava/lang/String;

    return-object p0

    :cond_0
    new-instance p1, Ljava/lang/IllegalArgumentException;

    const-string v0, "rawKey == null"

    invoke-direct {p1, v0}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public Fj(Ljava/util/List;)Lcom/bykv/vk/openvk/component/video/Fj/ex/ex$Fj;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lcom/bykv/vk/openvk/component/video/Fj/ex/mSE$ex;",
            ">;)",
            "Lcom/bykv/vk/openvk/component/video/Fj/ex/ex$Fj;"
        }
    .end annotation

    iput-object p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/ex/ex$Fj;->WR:Ljava/util/List;

    return-object p0
.end method

.method public Fj()Lcom/bykv/vk/openvk/component/video/Fj/ex/ex;
    .locals 1

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/ex/ex$Fj;->eV:Lcom/bykv/vk/openvk/component/video/Fj/ex/Fj/Fj;

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/ex/ex$Fj;->Ubf:Lcom/bykv/vk/openvk/component/video/Fj/ex/ex/hjc;

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/ex/ex$Fj;->Fj:Ljava/lang/String;

    invoke-static {v0}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    if-nez v0, :cond_0

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/ex/ex$Fj;->ex:Ljava/lang/String;

    invoke-static {v0}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    if-nez v0, :cond_0

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/ex/ex$Fj;->hjc:Lcom/bykv/vk/openvk/component/video/Fj/ex/UYd;

    if-eqz v0, :cond_0

    new-instance v0, Lcom/bykv/vk/openvk/component/video/Fj/ex/ex;

    invoke-direct {v0, p0}, Lcom/bykv/vk/openvk/component/video/Fj/ex/ex;-><init>(Lcom/bykv/vk/openvk/component/video/Fj/ex/ex$Fj;)V

    return-object v0

    :cond_0
    new-instance v0, Ljava/lang/IllegalArgumentException;

    invoke-direct {v0}, Ljava/lang/IllegalArgumentException;-><init>()V

    throw v0
.end method

.method public ex(Ljava/lang/String;)Lcom/bykv/vk/openvk/component/video/Fj/ex/ex$Fj;
    .locals 1

    invoke-static {p1}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    if-nez v0, :cond_0

    iput-object p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/ex/ex$Fj;->ex:Ljava/lang/String;

    return-object p0

    :cond_0
    new-instance p1, Ljava/lang/IllegalArgumentException;

    const-string v0, "key == null"

    invoke-direct {p1, v0}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p1
.end method
