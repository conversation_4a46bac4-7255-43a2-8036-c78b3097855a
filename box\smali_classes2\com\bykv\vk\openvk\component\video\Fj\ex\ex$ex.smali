.class public interface abstract Lcom/bykv/vk/openvk/component/video/Fj/ex/ex$ex;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bykv/vk/openvk/component/video/Fj/ex/ex;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "ex"
.end annotation


# virtual methods
.method public abstract Fj(Lcom/bykv/vk/openvk/component/video/Fj/ex/ex;)V
.end method
