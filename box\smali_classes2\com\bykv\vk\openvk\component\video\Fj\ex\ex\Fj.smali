.class public Lcom/bykv/vk/openvk/component/video/Fj/ex/ex/Fj;
.super Ljava/lang/Object;


# instance fields
.field public final Fj:Ljava/lang/String;

.field public final Ubf:Ljava/lang/String;

.field public final eV:I

.field public final ex:Ljava/lang/String;

.field public final hjc:I


# direct methods
.method public constructor <init>(Ljava/lang/String;Ljava/lang/String;IILjava/lang/String;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/ex/ex/Fj;->Fj:Ljava/lang/String;

    iput-object p2, p0, Lcom/bykv/vk/openvk/component/video/Fj/ex/ex/Fj;->ex:Ljava/lang/String;

    iput p3, p0, Lcom/bykv/vk/openvk/component/video/Fj/ex/ex/Fj;->hjc:I

    iput p4, p0, Lcom/bykv/vk/openvk/component/video/Fj/ex/ex/Fj;->eV:I

    iput-object p5, p0, Lcom/bykv/vk/openvk/component/video/Fj/ex/ex/Fj;->Ubf:Ljava/lang/String;

    return-void
.end method
