.class Lcom/bykv/vk/openvk/component/video/Fj/ex/svN$2;
.super Lcom/bytedance/sdk/component/svN/BcC;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bykv/vk/openvk/component/video/Fj/ex/svN;->Fj(Lcom/bykv/vk/openvk/component/video/Fj/ex/ex/Fj;Ljava/io/File;Lcom/bykv/vk/openvk/component/video/Fj/ex/svN$ex;Lcom/bykv/vk/openvk/component/video/Fj/ex/UYd$Fj;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/component/svN/svN;

.field final synthetic ex:Lcom/bykv/vk/openvk/component/video/Fj/ex/svN;


# direct methods
.method public constructor <init>(Lcom/bykv/vk/openvk/component/video/Fj/ex/svN;Ljava/lang/String;Lcom/bytedance/sdk/component/svN/svN;)V
    .locals 0

    iput-object p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/ex/svN$2;->ex:Lcom/bykv/vk/openvk/component/video/Fj/ex/svN;

    iput-object p3, p0, Lcom/bykv/vk/openvk/component/video/Fj/ex/svN$2;->Fj:Lcom/bytedance/sdk/component/svN/svN;

    invoke-direct {p0, p2}, Lcom/bytedance/sdk/component/svN/BcC;-><init>(Ljava/lang/String;)V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 1

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/ex/svN$2;->Fj:Lcom/bytedance/sdk/component/svN/svN;

    invoke-virtual {v0}, Ljava/util/concurrent/FutureTask;->run()V

    return-void
.end method
