.class final Lcom/bykv/vk/openvk/component/video/Fj/ex/svN$Fj;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bykv/vk/openvk/component/video/Fj/ex/svN;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "Fj"
.end annotation


# instance fields
.field Fj:Lcom/bykv/vk/openvk/component/video/Fj/ex/Fj/Fj;

.field eV:Lcom/bykv/vk/openvk/component/video/Fj/ex/svN$hjc;

.field ex:Lcom/bykv/vk/openvk/component/video/Fj/ex/ex/hjc;

.field hjc:Ljava/net/Socket;


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public Fj(Lcom/bykv/vk/openvk/component/video/Fj/ex/ex/hjc;)Lcom/bykv/vk/openvk/component/video/Fj/ex/svN$Fj;
    .locals 1

    if-eqz p1, :cond_0

    iput-object p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/ex/svN$Fj;->ex:Lcom/bykv/vk/openvk/component/video/Fj/ex/ex/hjc;

    return-object p0

    :cond_0
    new-instance p1, Ljava/lang/IllegalArgumentException;

    const-string v0, "db == null"

    invoke-direct {p1, v0}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public Fj(Lcom/bykv/vk/openvk/component/video/Fj/ex/svN$hjc;)Lcom/bykv/vk/openvk/component/video/Fj/ex/svN$Fj;
    .locals 0

    iput-object p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/ex/svN$Fj;->eV:Lcom/bykv/vk/openvk/component/video/Fj/ex/svN$hjc;

    return-object p0
.end method

.method public Fj(Ljava/net/Socket;)Lcom/bykv/vk/openvk/component/video/Fj/ex/svN$Fj;
    .locals 1

    if-eqz p1, :cond_0

    iput-object p1, p0, Lcom/bykv/vk/openvk/component/video/Fj/ex/svN$Fj;->hjc:Ljava/net/Socket;

    return-object p0

    :cond_0
    new-instance p1, Ljava/lang/IllegalArgumentException;

    const-string v0, "socket == null"

    invoke-direct {p1, v0}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public Fj()Lcom/bykv/vk/openvk/component/video/Fj/ex/svN;
    .locals 1

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/ex/svN$Fj;->ex:Lcom/bykv/vk/openvk/component/video/Fj/ex/ex/hjc;

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/Fj/ex/svN$Fj;->hjc:Ljava/net/Socket;

    if-eqz v0, :cond_0

    new-instance v0, Lcom/bykv/vk/openvk/component/video/Fj/ex/svN;

    invoke-direct {v0, p0}, Lcom/bykv/vk/openvk/component/video/Fj/ex/svN;-><init>(Lcom/bykv/vk/openvk/component/video/Fj/ex/svN$Fj;)V

    return-object v0

    :cond_0
    new-instance v0, Ljava/lang/IllegalArgumentException;

    invoke-direct {v0}, Ljava/lang/IllegalArgumentException;-><init>()V

    throw v0
.end method
