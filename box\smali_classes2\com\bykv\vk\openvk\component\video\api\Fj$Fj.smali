.class public interface abstract Lcom/bykv/vk/openvk/component/video/api/Fj$Fj;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bykv/vk/openvk/component/video/api/Fj;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "Fj"
.end annotation


# virtual methods
.method public abstract Fj(Lcom/bykv/vk/openvk/component/video/api/Fj;)V
.end method

.method public abstract Fj(Lcom/bykv/vk/openvk/component/video/api/Fj;I)V
.end method

.method public abstract Fj(Lcom/bykv/vk/openvk/component/video/api/Fj;II)V
.end method

.method public abstract Fj(Lcom/bykv/vk/openvk/component/video/api/Fj;III)V
.end method

.method public abstract Fj(Lcom/bykv/vk/openvk/component/video/api/Fj;J)V
.end method

.method public abstract Fj(Lcom/bykv/vk/openvk/component/video/api/Fj;JJ)V
.end method

.method public abstract Fj(Lcom/bykv/vk/openvk/component/video/api/Fj;Lcom/bykv/vk/openvk/component/video/api/hjc/Fj;)V
.end method

.method public abstract Fj(Lcom/bykv/vk/openvk/component/video/api/Fj;Z)V
.end method

.method public abstract Ubf(Lcom/bykv/vk/openvk/component/video/api/Fj;)V
.end method

.method public abstract eV(Lcom/bykv/vk/openvk/component/video/api/Fj;)V
.end method

.method public abstract ex(Lcom/bykv/vk/openvk/component/video/api/Fj;)V
.end method

.method public abstract ex(Lcom/bykv/vk/openvk/component/video/api/Fj;I)V
.end method

.method public abstract hjc(Lcom/bykv/vk/openvk/component/video/api/Fj;)V
.end method
