.class public interface abstract Lcom/bykv/vk/openvk/component/video/api/Fj;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/bykv/vk/openvk/component/video/api/Fj$Fj;
    }
.end annotation


# virtual methods
.method public abstract BcC()Z
.end method

.method public abstract Fj()Z
.end method

.method public abstract Ubf()I
.end method

.method public abstract WR()Z
.end method

.method public abstract eV()I
.end method

.method public abstract ex()Z
.end method

.method public abstract hjc()Z
.end method

.method public abstract svN()Z
.end method
