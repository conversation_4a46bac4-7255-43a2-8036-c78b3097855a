.class public interface abstract Lcom/bykv/vk/openvk/component/video/api/Fj/ex;
.super Ljava/lang/Object;


# virtual methods
.method public abstract Fj()Ljava/lang/String;
.end method

.method public abstract Fj(Ljava/lang/String;)V
.end method

.method public abstract Fj(Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;)Z
.end method

.method public abstract eV()V
.end method

.method public abstract ex(Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;)J
.end method

.method public abstract ex()Ljava/lang/String;
.end method

.method public abstract hjc()Ljava/lang/String;
.end method
