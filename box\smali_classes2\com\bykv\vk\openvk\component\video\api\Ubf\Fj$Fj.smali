.class public interface abstract Lcom/bykv/vk/openvk/component/video/api/Ubf/Fj$Fj;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bykv/vk/openvk/component/video/api/Ubf/Fj;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "Fj"
.end annotation


# virtual methods
.method public abstract Fj(Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;I)V
.end method

.method public abstract Fj(Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;ILjava/lang/String;)V
.end method

.method public abstract ex(Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;I)V
.end method
