.class public interface abstract Lcom/bykv/vk/openvk/component/video/api/Ubf/Fj;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/bykv/vk/openvk/component/video/api/Ubf/Fj$Fj;
    }
.end annotation


# virtual methods
.method public abstract Fj(Landroid/content/Context;Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;Lcom/bykv/vk/openvk/component/video/api/Ubf/Fj$Fj;)V
.end method
