.class public abstract Lcom/bykv/vk/openvk/component/video/api/Ubf/ex;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bykv/vk/openvk/component/video/api/Ubf/Fj$Fj;


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public ex(Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;I)V
    .locals 0

    return-void
.end method
