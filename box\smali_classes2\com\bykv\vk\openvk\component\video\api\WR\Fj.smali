.class public Lcom/bykv/vk/openvk/component/video/api/WR/Fj;
.super Ljava/lang/Object;


# static fields
.field static Fj:Landroid/content/Context;


# direct methods
.method public static Fj(Landroid/content/Context;)V
    .locals 0

    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    sput-object p0, Lcom/bykv/vk/openvk/component/video/api/WR/Fj;->Fj:Landroid/content/Context;

    return-void
.end method
