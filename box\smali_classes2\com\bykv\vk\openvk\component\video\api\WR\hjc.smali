.class public Lcom/bykv/vk/openvk/component/video/api/WR/hjc;
.super Ljava/lang/Object;


# static fields
.field private static Fj:Z = false

.field private static ex:I = 0x4

.field private static hjc:Ljava/lang/String; = ""


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public static Fj()V
    .locals 1

    const/4 v0, 0x1

    sput-boolean v0, Lcom/bykv/vk/openvk/component/video/api/WR/hjc;->Fj:Z

    const/4 v0, 0x3

    invoke-static {v0}, Lcom/bykv/vk/openvk/component/video/api/WR/hjc;->Fj(I)V

    return-void
.end method

.method public static Fj(I)V
    .locals 0

    sput p0, Lcom/bykv/vk/openvk/component/video/api/WR/hjc;->ex:I

    return-void
.end method

.method public static Fj(Ljava/lang/String;)V
    .locals 0

    sput-object p0, Lcom/bykv/vk/openvk/component/video/api/WR/hjc;->hjc:Ljava/lang/String;

    return-void
.end method

.method public static ex()V
    .locals 1

    const/4 v0, 0x0

    sput-boolean v0, Lcom/bykv/vk/openvk/component/video/api/WR/hjc;->Fj:Z

    const/4 v0, 0x7

    invoke-static {v0}, Lcom/bykv/vk/openvk/component/video/api/WR/hjc;->Fj(I)V

    return-void
.end method

.method public static hjc()Z
    .locals 1

    sget-boolean v0, Lcom/bykv/vk/openvk/component/video/api/WR/hjc;->Fj:Z

    return v0
.end method
