.class public interface abstract Lcom/bykv/vk/openvk/component/video/api/eV/Fj;
.super Ljava/lang/Object;


# virtual methods
.method public abstract Fj(Lcom/bykv/vk/openvk/component/video/api/eV/ex;I)V
.end method

.method public abstract Fj(Lcom/bykv/vk/openvk/component/video/api/eV/ex;IZ)V
.end method

.method public abstract Fj(Lcom/bykv/vk/openvk/component/video/api/eV/ex;Landroid/graphics/SurfaceTexture;)V
.end method

.method public abstract Fj(Lcom/bykv/vk/openvk/component/video/api/eV/ex;Landroid/view/SurfaceHolder;)V
.end method

.method public abstract Fj(Lcom/bykv/vk/openvk/component/video/api/eV/ex;Landroid/view/View;)V
.end method

.method public abstract Fj(Lcom/bykv/vk/openvk/component/video/api/eV/ex;Landroid/view/View;ZZ)V
.end method

.method public abstract Ubf(Lcom/bykv/vk/openvk/component/video/api/eV/ex;Landroid/view/View;)V
.end method

.method public abstract eV(Lcom/bykv/vk/openvk/component/video/api/eV/ex;Landroid/view/View;)V
.end method

.method public abstract ex(Lcom/bykv/vk/openvk/component/video/api/eV/ex;I)V
.end method

.method public abstract ex(Lcom/bykv/vk/openvk/component/video/api/eV/ex;Landroid/graphics/SurfaceTexture;)V
.end method

.method public abstract ex(Lcom/bykv/vk/openvk/component/video/api/eV/ex;Landroid/view/SurfaceHolder;)V
.end method

.method public abstract ex(Lcom/bykv/vk/openvk/component/video/api/eV/ex;Landroid/view/View;)V
.end method

.method public abstract hjc(Lcom/bykv/vk/openvk/component/video/api/eV/ex;Landroid/view/View;)V
.end method
