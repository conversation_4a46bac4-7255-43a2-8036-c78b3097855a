.class public interface abstract Lcom/bykv/vk/openvk/component/video/api/eV/ex;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bykv/vk/openvk/component/video/api/ex/Fj;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "<T:",
        "Ljava/lang/Object;",
        ">",
        "Ljava/lang/Object;",
        "Lcom/bykv/vk/openvk/component/video/api/ex/Fj;"
    }
.end annotation


# virtual methods
.method public abstract Fj()V
.end method

.method public abstract Fj(Landroid/graphics/drawable/Drawable;)V
.end method

.method public abstract Fj(Ljava/lang/Object;Ljava/lang/ref/WeakReference;Z)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(TT;",
            "Ljava/lang/ref/WeakReference<",
            "Landroid/content/Context;",
            ">;Z)V"
        }
    .end annotation
.end method

.method public abstract Fj(Z)V
.end method

.method public abstract ex()V
.end method

.method public abstract hjc()Landroid/view/View;
.end method
