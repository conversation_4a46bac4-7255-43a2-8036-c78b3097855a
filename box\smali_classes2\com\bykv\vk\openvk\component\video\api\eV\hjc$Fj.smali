.class public interface abstract Lcom/bykv/vk/openvk/component/video/api/eV/hjc$Fj;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bykv/vk/openvk/component/video/api/eV/hjc;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "Fj"
.end annotation


# virtual methods
.method public abstract Fj()V
.end method

.method public abstract Fj(JI)V
.end method

.method public abstract Fj(JJ)V
.end method

.method public abstract ex(JI)V
.end method
