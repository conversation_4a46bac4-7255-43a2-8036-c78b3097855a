.class public interface abstract Lcom/bykv/vk/openvk/component/video/api/eV/hjc$hjc;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bykv/vk/openvk/component/video/api/eV/hjc;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "hjc"
.end annotation


# virtual methods
.method public abstract Fj(JJ)V
.end method

.method public abstract b_()V
.end method

.method public abstract e_()V
.end method

.method public abstract f_()V
.end method

.method public abstract g_()V
.end method
