.class public interface abstract Lcom/bykv/vk/openvk/component/video/api/eV/hjc;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/bykv/vk/openvk/component/video/api/eV/hjc$ex;,
        Lcom/bykv/vk/openvk/component/video/api/eV/hjc$eV;,
        Lcom/bykv/vk/openvk/component/video/api/eV/hjc$hjc;,
        Lcom/bykv/vk/openvk/component/video/api/eV/hjc$Fj;
    }
.end annotation


# virtual methods
.method public abstract BcC()J
.end method

.method public abstract Fj()V
.end method

.method public abstract Fj(J)V
.end method

.method public abstract Fj(Lcom/bykv/vk/openvk/component/video/api/eV/hjc$Fj;)V
.end method

.method public abstract Fj(Lcom/bykv/vk/openvk/component/video/api/eV/hjc$eV;)V
.end method

.method public abstract Fj(Lcom/bykv/vk/openvk/component/video/api/eV/hjc$ex;)V
.end method

.method public abstract Fj(Z)V
.end method

.method public abstract Fj(ZI)V
.end method

.method public abstract Fj(Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;)Z
.end method

.method public abstract JU()Z
.end method

.method public abstract JW()Z
.end method

.method public abstract Ko()I
.end method

.method public abstract Ql()Z
.end method

.method public abstract Tc()Z
.end method

.method public abstract UYd()Lcom/bykv/vk/openvk/component/video/api/Fj;
.end method

.method public abstract Ubf()J
.end method

.method public abstract Ubf(Z)V
.end method

.method public abstract WR()J
.end method

.method public abstract dG()Lcom/bykv/vk/openvk/component/video/api/eV/ex;
.end method

.method public abstract eV()V
.end method

.method public abstract eV(Z)V
.end method

.method public abstract ex()V
.end method

.method public abstract ex(J)V
.end method

.method public abstract ex(Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;)V
.end method

.method public abstract ex(Z)V
.end method

.method public abstract hjc()V
.end method

.method public abstract hjc(J)V
.end method

.method public abstract hjc(Z)V
.end method

.method public abstract mSE()J
.end method

.method public abstract rAx()Z
.end method

.method public abstract svN()I
.end method
