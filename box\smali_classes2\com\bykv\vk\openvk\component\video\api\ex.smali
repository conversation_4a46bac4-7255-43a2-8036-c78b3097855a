.class public Lcom/bykv/vk/openvk/component/video/api/ex;
.super Ljava/lang/Object;


# instance fields
.field private Fj:F


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public Fj()F
    .locals 1

    iget v0, p0, Lcom/bykv/vk/openvk/component/video/api/ex;->Fj:F

    return v0
.end method

.method public Fj(F)V
    .locals 0

    iput p1, p0, Lcom/bykv/vk/openvk/component/video/api/ex;->Fj:F

    return-void
.end method
