.class public Lcom/bykv/vk/openvk/component/video/api/hjc;
.super Ljava/lang/Object;


# static fields
.field public static Fj:Z = false

.field private static Ubf:Lcom/bytedance/sdk/component/ex/Fj/rAx; = null

.field private static WR:I = 0x1

.field private static eV:Z

.field private static ex:Landroid/content/Context;

.field private static hjc:Ljava/lang/String;


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public static Fj()Landroid/content/Context;
    .locals 1

    sget-object v0, Lcom/bykv/vk/openvk/component/video/api/hjc;->ex:Landroid/content/Context;

    return-object v0
.end method

.method public static Fj(I)V
    .locals 0

    sput p0, Lcom/bykv/vk/openvk/component/video/api/hjc;->WR:I

    return-void
.end method

.method public static Fj(Landroid/content/Context;Ljava/lang/String;)V
    .locals 0

    sput-object p0, Lcom/bykv/vk/openvk/component/video/api/hjc;->ex:Landroid/content/Context;

    sput-object p1, Lcom/bykv/vk/openvk/component/video/api/hjc;->hjc:Ljava/lang/String;

    return-void
.end method

.method public static Fj(Lcom/bytedance/sdk/component/ex/Fj/rAx;)V
    .locals 0

    sput-object p0, Lcom/bykv/vk/openvk/component/video/api/hjc;->Ubf:Lcom/bytedance/sdk/component/ex/Fj/rAx;

    return-void
.end method

.method public static Fj(Z)V
    .locals 0

    sput-boolean p0, Lcom/bykv/vk/openvk/component/video/api/hjc;->eV:Z

    return-void
.end method

.method public static Ubf()Z
    .locals 1

    sget-boolean v0, Lcom/bykv/vk/openvk/component/video/api/hjc;->Fj:Z

    return v0
.end method

.method public static WR()I
    .locals 1

    sget v0, Lcom/bykv/vk/openvk/component/video/api/hjc;->WR:I

    return v0
.end method

.method public static eV()Lcom/bytedance/sdk/component/ex/Fj/rAx;
    .locals 4

    sget-object v0, Lcom/bykv/vk/openvk/component/video/api/hjc;->Ubf:Lcom/bytedance/sdk/component/ex/Fj/rAx;

    if-nez v0, :cond_0

    new-instance v0, Lcom/bytedance/sdk/component/ex/Fj/rAx$Fj;

    const-string v1, "v_config"

    invoke-direct {v0, v1}, Lcom/bytedance/sdk/component/ex/Fj/rAx$Fj;-><init>(Ljava/lang/String;)V

    sget-object v1, Ljava/util/concurrent/TimeUnit;->MILLISECONDS:Ljava/util/concurrent/TimeUnit;

    const-wide/16 v2, 0x2710

    invoke-virtual {v0, v2, v3, v1}, Lcom/bytedance/sdk/component/ex/Fj/rAx$Fj;->Fj(JLjava/util/concurrent/TimeUnit;)Lcom/bytedance/sdk/component/ex/Fj/rAx$Fj;

    move-result-object v0

    invoke-virtual {v0, v2, v3, v1}, Lcom/bytedance/sdk/component/ex/Fj/rAx$Fj;->ex(JLjava/util/concurrent/TimeUnit;)Lcom/bytedance/sdk/component/ex/Fj/rAx$Fj;

    move-result-object v0

    invoke-virtual {v0, v2, v3, v1}, Lcom/bytedance/sdk/component/ex/Fj/rAx$Fj;->hjc(JLjava/util/concurrent/TimeUnit;)Lcom/bytedance/sdk/component/ex/Fj/rAx$Fj;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/ex/Fj/rAx$Fj;->Fj()Lcom/bytedance/sdk/component/ex/Fj/rAx;

    move-result-object v0

    sput-object v0, Lcom/bykv/vk/openvk/component/video/api/hjc;->Ubf:Lcom/bytedance/sdk/component/ex/Fj/rAx;

    :cond_0
    sget-object v0, Lcom/bykv/vk/openvk/component/video/api/hjc;->Ubf:Lcom/bytedance/sdk/component/ex/Fj/rAx;

    return-object v0
.end method

.method public static ex()Ljava/lang/String;
    .locals 3

    sget-object v0, Lcom/bykv/vk/openvk/component/video/api/hjc;->hjc:Ljava/lang/String;

    invoke-static {v0}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    if-eqz v0, :cond_1

    :try_start_0
    new-instance v0, Ljava/io/File;

    invoke-static {}, Lcom/bykv/vk/openvk/component/video/api/hjc;->Fj()Landroid/content/Context;

    move-result-object v1

    invoke-virtual {v1}, Landroid/content/Context;->getFilesDir()Ljava/io/File;

    move-result-object v1

    const-string v2, "ttad_dir"

    invoke-direct {v0, v1, v2}, Ljava/io/File;-><init>(Ljava/io/File;Ljava/lang/String;)V

    invoke-virtual {v0}, Ljava/io/File;->exists()Z

    move-result v1

    if-nez v1, :cond_0

    invoke-virtual {v0}, Ljava/io/File;->mkdirs()Z

    :cond_0
    invoke-virtual {v0}, Ljava/io/File;->getAbsolutePath()Ljava/lang/String;

    move-result-object v0

    sput-object v0, Lcom/bykv/vk/openvk/component/video/api/hjc;->hjc:Ljava/lang/String;
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    :catchall_0
    :cond_1
    sget-object v0, Lcom/bykv/vk/openvk/component/video/api/hjc;->hjc:Ljava/lang/String;

    return-object v0
.end method

.method public static hjc()Z
    .locals 1

    sget-boolean v0, Lcom/bykv/vk/openvk/component/video/api/hjc;->eV:Z

    return v0
.end method
