.class public Lcom/bykv/vk/openvk/component/video/api/hjc/Fj;
.super Ljava/lang/Object;


# instance fields
.field private Fj:I

.field private ex:I

.field private hjc:Ljava/lang/String;


# direct methods
.method public constructor <init>(II)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput p1, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/Fj;->Fj:I

    iput p2, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/Fj;->ex:I

    return-void
.end method


# virtual methods
.method public Fj()I
    .locals 1

    iget v0, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/Fj;->Fj:I

    return v0
.end method

.method public Fj(Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/Fj;->hjc:Ljava/lang/String;

    return-void
.end method

.method public ex()I
    .locals 1

    iget v0, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/Fj;->ex:I

    return v0
.end method

.method public hjc()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/Fj;->hjc:Ljava/lang/String;

    return-object v0
.end method
