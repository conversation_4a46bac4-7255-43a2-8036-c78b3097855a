.class public Lcom/bykv/vk/openvk/component/video/api/hjc/ex;
.super Ljava/lang/Object;


# instance fields
.field private BcC:Ljava/lang/String;

.field private Fj:I

.field private JU:I

.field private JW:I

.field private Ko:Ljava/lang/String;

.field private Ql:I

.field private Tc:F

.field private UYd:I

.field private Ubf:Ljava/lang/String;

.field private WR:Ljava/lang/String;

.field private dG:I

.field private eV:D

.field private ex:I

.field private hjc:J

.field private mE:I

.field private mSE:Ljava/lang/String;

.field private rAx:D

.field private rS:I

.field private svN:Ljava/lang/String;

.field private vYf:I


# direct methods
.method public constructor <init>()V
    .locals 1

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/high16 v0, -0x40800000    # -1.0f

    iput v0, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->Tc:F

    const/4 v0, 0x0

    iput v0, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->JW:I

    iput v0, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->JU:I

    iput v0, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->Ql:I

    iput v0, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->rS:I

    const v0, 0x4b000

    iput v0, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->vYf:I

    const/4 v0, 0x1

    iput v0, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->mE:I

    return-void
.end method


# virtual methods
.method public Af()Z
    .locals 1

    iget v0, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->Ql:I

    if-nez v0, :cond_0

    const/4 v0, 0x1

    return v0

    :cond_0
    const/4 v0, 0x0

    return v0
.end method

.method public BcC()F
    .locals 1

    iget v0, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->Tc:F

    return v0
.end method

.method public BcC(I)V
    .locals 0

    iput p1, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->JW:I

    return-void
.end method

.method public Fj()I
    .locals 1

    iget v0, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->UYd:I

    return v0
.end method

.method public Fj(D)V
    .locals 0

    iput-wide p1, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->eV:D

    return-void
.end method

.method public Fj(I)V
    .locals 0

    iput p1, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->UYd:I

    return-void
.end method

.method public Fj(J)V
    .locals 0

    iput-wide p1, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->hjc:J

    return-void
.end method

.method public Fj(Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->Ubf:Ljava/lang/String;

    return-void
.end method

.method public JU()I
    .locals 1

    iget v0, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->Ql:I

    return v0
.end method

.method public JW()I
    .locals 5

    iget v0, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->vYf:I

    if-gez v0, :cond_0

    const v0, 0x4b000

    iput v0, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->vYf:I

    :cond_0
    iget v0, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->vYf:I

    int-to-long v0, v0

    iget-wide v2, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->hjc:J

    cmp-long v4, v0, v2

    if-lez v4, :cond_1

    long-to-int v0, v2

    iput v0, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->vYf:I

    :cond_1
    iget v0, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->vYf:I

    return v0
.end method

.method public Ko()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->WR:Ljava/lang/String;

    return-object v0
.end method

.method public Ko(I)V
    .locals 1

    const/4 v0, 0x1

    invoke-static {v0, p1}, Ljava/lang/Math;->max(II)I

    move-result p1

    const/4 v0, 0x4

    invoke-static {v0, p1}, Ljava/lang/Math;->min(II)I

    move-result p1

    iput p1, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->mE:I

    return-void
.end method

.method public Ql()I
    .locals 1

    iget v0, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->rS:I

    return v0
.end method

.method public Tc()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->Ko:Ljava/lang/String;

    invoke-static {v0}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->svN:Ljava/lang/String;

    invoke-static {v0}, Lcom/bykv/vk/openvk/component/video/api/WR/ex;->Fj(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    iput-object v0, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->Ko:Ljava/lang/String;

    :cond_0
    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->Ko:Ljava/lang/String;

    return-object v0
.end method

.method public UYd()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->BcC:Ljava/lang/String;

    return-object v0
.end method

.method public Ubf()J
    .locals 2

    iget-wide v0, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->hjc:J

    return-wide v0
.end method

.method public Ubf(I)V
    .locals 0

    iput p1, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->vYf:I

    return-void
.end method

.method public Ubf(Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->mSE:Ljava/lang/String;

    return-void
.end method

.method public WR()D
    .locals 2

    iget-wide v0, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->eV:D

    return-wide v0
.end method

.method public WR(I)V
    .locals 0

    iput p1, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->Ql:I

    return-void
.end method

.method public WR(Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->Ko:Ljava/lang/String;

    return-void
.end method

.method public dG()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->mSE:Ljava/lang/String;

    return-object v0
.end method

.method public eV()I
    .locals 1

    iget v0, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->dG:I

    return v0
.end method

.method public eV(I)V
    .locals 0

    iput p1, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->dG:I

    return-void
.end method

.method public eV(Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->BcC:Ljava/lang/String;

    return-void
.end method

.method public ex()I
    .locals 1

    iget v0, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->Fj:I

    return v0
.end method

.method public ex(I)V
    .locals 0

    iput p1, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->Fj:I

    return-void
.end method

.method public ex(Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->WR:Ljava/lang/String;

    return-void
.end method

.method public hjc()I
    .locals 1

    iget v0, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->ex:I

    return v0
.end method

.method public hjc(I)V
    .locals 0

    iput p1, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->ex:I

    return-void
.end method

.method public hjc(Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->svN:Ljava/lang/String;

    return-void
.end method

.method public mC()I
    .locals 1

    iget v0, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->mE:I

    return v0
.end method

.method public mE()I
    .locals 1

    iget v0, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->JU:I

    return v0
.end method

.method public mSE()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->Ubf:Ljava/lang/String;

    return-object v0
.end method

.method public mSE(I)V
    .locals 0

    iput p1, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->JU:I

    return-void
.end method

.method public rAx()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->svN:Ljava/lang/String;

    return-object v0
.end method

.method public rS()Lorg/json/JSONObject;
    .locals 6

    new-instance v0, Lorg/json/JSONObject;

    invoke-direct {v0}, Lorg/json/JSONObject;-><init>()V

    :try_start_0
    const-string v1, "cover_height"

    invoke-virtual {p0}, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->ex()I

    move-result v2

    invoke-virtual {v0, v1, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;I)Lorg/json/JSONObject;

    const-string v1, "cover_url"

    invoke-virtual {p0}, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->Ko()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v0, v1, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    const-string v1, "cover_width"

    invoke-virtual {p0}, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->hjc()I

    move-result v2

    invoke-virtual {v0, v1, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;I)Lorg/json/JSONObject;

    const-string v1, "endcard"

    invoke-virtual {p0}, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->UYd()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v0, v1, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    const-string v1, "file_hash"

    invoke-virtual {p0}, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->Tc()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v0, v1, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    const-string v1, "resolution"

    invoke-virtual {p0}, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->mSE()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v0, v1, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    const-string v1, "size"

    invoke-virtual {p0}, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->Ubf()J

    move-result-wide v2

    invoke-virtual {v0, v1, v2, v3}, Lorg/json/JSONObject;->put(Ljava/lang/String;J)Lorg/json/JSONObject;

    const-string v1, "video_duration"

    invoke-virtual {p0}, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->WR()D

    move-result-wide v2

    invoke-virtual {v0, v1, v2, v3}, Lorg/json/JSONObject;->put(Ljava/lang/String;D)Lorg/json/JSONObject;

    const-string v1, "video_url"

    invoke-virtual {p0}, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->rAx()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v0, v1, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    const-string v1, "playable_download_url"

    invoke-virtual {p0}, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->dG()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v0, v1, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    const-string v1, "if_playable_loading_show"

    invoke-virtual {p0}, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->vYf()I

    move-result v2

    invoke-virtual {v0, v1, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;I)Lorg/json/JSONObject;

    const-string v1, "remove_loading_page_type"

    invoke-virtual {p0}, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->mE()I

    move-result v2

    invoke-virtual {v0, v1, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;I)Lorg/json/JSONObject;

    const-string v1, "fallback_endcard_judge"

    invoke-virtual {p0}, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->Fj()I

    move-result v2

    invoke-virtual {v0, v1, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;I)Lorg/json/JSONObject;

    const-string v1, "video_preload_size"

    invoke-virtual {p0}, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->JW()I

    move-result v2

    invoke-virtual {v0, v1, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;I)Lorg/json/JSONObject;

    const-string v1, "reward_video_cached_type"

    invoke-virtual {p0}, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->JU()I

    move-result v2

    invoke-virtual {v0, v1, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;I)Lorg/json/JSONObject;

    const-string v1, "execute_cached_type"

    invoke-virtual {p0}, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->Ql()I

    move-result v2

    invoke-virtual {v0, v1, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;I)Lorg/json/JSONObject;

    const-string v1, "endcard_render"

    invoke-virtual {p0}, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->eV()I

    move-result v2

    invoke-virtual {v0, v1, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;I)Lorg/json/JSONObject;

    const-string v1, "replay_time"

    invoke-virtual {p0}, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->mC()I

    move-result v2

    invoke-virtual {v0, v1, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;I)Lorg/json/JSONObject;

    const-string v1, "play_speed_ratio"

    invoke-virtual {p0}, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->BcC()F

    move-result v2

    float-to-double v2, v2

    invoke-virtual {v0, v1, v2, v3}, Lorg/json/JSONObject;->put(Ljava/lang/String;D)Lorg/json/JSONObject;

    invoke-virtual {p0}, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->svN()D

    move-result-wide v1

    const-wide/16 v3, 0x0

    cmpl-double v5, v1, v3

    if-lez v5, :cond_0

    const-string v1, "start"

    invoke-virtual {p0}, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->svN()D

    move-result-wide v2

    invoke-virtual {v0, v1, v2, v3}, Lorg/json/JSONObject;->put(Ljava/lang/String;D)Lorg/json/JSONObject;
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    :catch_0
    :cond_0
    return-object v0
.end method

.method public svN()D
    .locals 2

    iget-wide v0, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->rAx:D

    return-wide v0
.end method

.method public svN(I)V
    .locals 0

    iput p1, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->rS:I

    return-void
.end method

.method public vYf()I
    .locals 1

    iget v0, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->JW:I

    return v0
.end method
