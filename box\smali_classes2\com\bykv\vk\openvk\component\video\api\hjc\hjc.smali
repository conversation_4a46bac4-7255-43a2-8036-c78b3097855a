.class public Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/io/Serializable;


# instance fields
.field private Af:I

.field private BcC:I

.field public Fj:Ljava/lang/String;

.field private JU:Z

.field private JW:J

.field private Ko:I

.field private Ql:Z

.field private Tc:Ljava/lang/String;

.field private UYd:I

.field private Ubf:Lcom/bykv/vk/openvk/component/video/api/hjc/ex;

.field private WR:Lcom/bykv/vk/openvk/component/video/api/hjc/ex;

.field private cB:I

.field private dG:I

.field public eV:I

.field public ex:I

.field public final hjc:Ljava/util/HashMap;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/HashMap<",
            "Ljava/lang/String;",
            "Ljava/lang/Object;",
            ">;"
        }
    .end annotation
.end field

.field private mC:I

.field private mE:I

.field private mSE:I

.field private nsB:Lorg/json/JSONObject;

.field private rAx:Ljava/lang/String;

.field private rS:I

.field private svN:Ljava/lang/String;

.field private vYf:I


# direct methods
.method public constructor <init>(Ljava/lang/String;Lcom/bykv/vk/openvk/component/video/api/hjc/ex;Lcom/bykv/vk/openvk/component/video/api/hjc/ex;II)V
    .locals 2

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const v0, 0x32000

    iput v0, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->BcC:I

    const/4 v0, 0x0

    iput v0, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->mSE:I

    iput v0, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->Ko:I

    iput v0, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->rS:I

    iput v0, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->vYf:I

    new-instance v1, Ljava/util/HashMap;

    invoke-direct {v1}, Ljava/util/HashMap;-><init>()V

    iput-object v1, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->hjc:Ljava/util/HashMap;

    const/16 v1, 0x2710

    iput v1, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->mE:I

    iput v1, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->Af:I

    iput v1, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->mC:I

    iput v0, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->cB:I

    new-instance v0, Lorg/json/JSONObject;

    invoke-direct {v0}, Lorg/json/JSONObject;-><init>()V

    iput-object v0, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->nsB:Lorg/json/JSONObject;

    iput-object p1, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->svN:Ljava/lang/String;

    iput-object p2, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->Ubf:Lcom/bykv/vk/openvk/component/video/api/hjc/ex;

    iput-object p3, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->WR:Lcom/bykv/vk/openvk/component/video/api/hjc/ex;

    iput p4, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->rS:I

    iput p5, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->vYf:I

    return-void
.end method


# virtual methods
.method public Af()Lcom/bykv/vk/openvk/component/video/api/hjc/ex;
    .locals 1

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->WR:Lcom/bykv/vk/openvk/component/video/api/hjc/ex;

    return-object v0
.end method

.method public BcC()Z
    .locals 1

    iget-boolean v0, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->JU:Z

    return v0
.end method

.method public Fj()I
    .locals 3

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->nsB:Lorg/json/JSONObject;

    const-string v1, "pitaya_cache_size"

    const/4 v2, 0x0

    invoke-virtual {v0, v1, v2}, Lorg/json/JSONObject;->optInt(Ljava/lang/String;I)I

    move-result v0

    return v0
.end method

.method public Fj(I)V
    .locals 0

    iput p1, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->UYd:I

    return-void
.end method

.method public Fj(J)V
    .locals 0

    iput-wide p1, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->JW:J

    return-void
.end method

.method public Fj(Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->svN:Ljava/lang/String;

    return-void
.end method

.method public declared-synchronized Fj(Ljava/lang/String;Ljava/lang/Object;)V
    .locals 1

    monitor-enter p0

    :try_start_0
    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->hjc:Ljava/util/HashMap;

    invoke-virtual {v0, p1, p2}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    monitor-exit p0

    return-void

    :catchall_0
    move-exception p1

    monitor-exit p0

    throw p1
.end method

.method public Fj(Z)V
    .locals 0

    iput-boolean p1, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->JU:Z

    return-void
.end method

.method public JU()I
    .locals 1

    iget v0, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->mE:I

    return v0
.end method

.method public JW()I
    .locals 1

    iget v0, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->rS:I

    return v0
.end method

.method public Ko()Z
    .locals 1

    invoke-virtual {p0}, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->rAx()Z

    move-result v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->WR:Lcom/bykv/vk/openvk/component/video/api/hjc/ex;

    invoke-virtual {v0}, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->Af()Z

    move-result v0

    return v0

    :cond_0
    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->Ubf:Lcom/bykv/vk/openvk/component/video/api/hjc/ex;

    if-eqz v0, :cond_1

    invoke-virtual {v0}, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->Af()Z

    move-result v0

    return v0

    :cond_1
    const/4 v0, 0x1

    return v0
.end method

.method public Ql()I
    .locals 1

    iget v0, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->Af:I

    return v0
.end method

.method public Tc()Ljava/lang/String;
    .locals 1

    invoke-virtual {p0}, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->rAx()Z

    move-result v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->WR:Lcom/bykv/vk/openvk/component/video/api/hjc/ex;

    invoke-virtual {v0}, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->Tc()Ljava/lang/String;

    move-result-object v0

    return-object v0

    :cond_0
    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->Ubf:Lcom/bykv/vk/openvk/component/video/api/hjc/ex;

    if-eqz v0, :cond_1

    invoke-virtual {v0}, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->Tc()Ljava/lang/String;

    move-result-object v0

    return-object v0

    :cond_1
    const/4 v0, 0x0

    return-object v0
.end method

.method public UYd()F
    .locals 1

    invoke-virtual {p0}, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->rAx()Z

    move-result v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->WR:Lcom/bykv/vk/openvk/component/video/api/hjc/ex;

    invoke-virtual {v0}, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->BcC()F

    move-result v0

    return v0

    :cond_0
    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->Ubf:Lcom/bykv/vk/openvk/component/video/api/hjc/ex;

    if-eqz v0, :cond_1

    invoke-virtual {v0}, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->BcC()F

    move-result v0

    return v0

    :cond_1
    const/high16 v0, -0x40800000    # -1.0f

    return v0
.end method

.method public Ubf()I
    .locals 1

    iget v0, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->UYd:I

    return v0
.end method

.method public declared-synchronized Ubf(Ljava/lang/String;)Ljava/lang/Object;
    .locals 1

    monitor-enter p0

    :try_start_0
    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->hjc:Ljava/util/HashMap;

    invoke-virtual {v0, p1}, Ljava/util/HashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    monitor-exit p0

    return-object p1

    :catchall_0
    move-exception p1

    monitor-exit p0

    throw p1
.end method

.method public Ubf(I)V
    .locals 0

    iput p1, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->Af:I

    return-void
.end method

.method public WR()I
    .locals 1

    iget v0, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->dG:I

    return v0
.end method

.method public WR(I)V
    .locals 0

    iput p1, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->mC:I

    return-void
.end method

.method public dG()Ljava/lang/String;
    .locals 1

    invoke-virtual {p0}, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->rAx()Z

    move-result v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->WR:Lcom/bykv/vk/openvk/component/video/api/hjc/ex;

    invoke-virtual {v0}, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->rAx()Ljava/lang/String;

    move-result-object v0

    return-object v0

    :cond_0
    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->Ubf:Lcom/bykv/vk/openvk/component/video/api/hjc/ex;

    if-eqz v0, :cond_1

    invoke-virtual {v0}, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->rAx()Ljava/lang/String;

    move-result-object v0

    return-object v0

    :cond_1
    const/4 v0, 0x0

    return-object v0
.end method

.method public eV(I)V
    .locals 0

    iput p1, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->mE:I

    return-void
.end method

.method public eV(Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->Fj:Ljava/lang/String;

    return-void
.end method

.method public eV()Z
    .locals 1

    iget-boolean v0, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->Ql:Z

    return v0
.end method

.method public ex()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->svN:Ljava/lang/String;

    return-object v0
.end method

.method public ex(I)V
    .locals 0

    iput p1, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->dG:I

    return-void
.end method

.method public ex(Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->rAx:Ljava/lang/String;

    return-void
.end method

.method public hjc()I
    .locals 1

    invoke-virtual {p0}, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->rAx()Z

    move-result v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->WR:Lcom/bykv/vk/openvk/component/video/api/hjc/ex;

    invoke-virtual {v0}, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->JW()I

    move-result v0

    return v0

    :cond_0
    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->Ubf:Lcom/bykv/vk/openvk/component/video/api/hjc/ex;

    if-eqz v0, :cond_1

    invoke-virtual {v0}, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->JW()I

    move-result v0

    return v0

    :cond_1
    const/4 v0, 0x0

    return v0
.end method

.method public hjc(I)V
    .locals 0

    iput p1, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->ex:I

    return-void
.end method

.method public hjc(Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->Tc:Ljava/lang/String;

    return-void
.end method

.method public mE()Lcom/bykv/vk/openvk/component/video/api/hjc/ex;
    .locals 1

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->Ubf:Lcom/bykv/vk/openvk/component/video/api/hjc/ex;

    return-object v0
.end method

.method public mSE()J
    .locals 2

    invoke-virtual {p0}, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->rAx()Z

    move-result v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->WR:Lcom/bykv/vk/openvk/component/video/api/hjc/ex;

    invoke-virtual {v0}, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->Ubf()J

    move-result-wide v0

    return-wide v0

    :cond_0
    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->Ubf:Lcom/bykv/vk/openvk/component/video/api/hjc/ex;

    if-eqz v0, :cond_1

    invoke-virtual {v0}, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->Ubf()J

    move-result-wide v0

    return-wide v0

    :cond_1
    const-wide/16 v0, 0x0

    return-wide v0
.end method

.method public rAx()Z
    .locals 3

    iget v0, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->vYf:I

    const/4 v1, 0x1

    if-ne v0, v1, :cond_1

    iget-object v0, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->WR:Lcom/bykv/vk/openvk/component/video/api/hjc/ex;

    if-eqz v0, :cond_1

    invoke-virtual {v0}, Lcom/bykv/vk/openvk/component/video/api/hjc/ex;->rAx()Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    if-nez v0, :cond_1

    invoke-static {}, Lcom/bykv/vk/openvk/component/video/api/hjc;->WR()I

    move-result v0

    const/4 v2, 0x2

    if-ne v0, v2, :cond_0

    sget v0, Landroid/os/Build$VERSION;->SDK_INT:I

    const/16 v2, 0x1a

    if-lt v0, v2, :cond_1

    goto :goto_0

    :cond_0
    iget v0, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->rS:I

    if-ne v0, v1, :cond_1

    goto :goto_0

    :cond_1
    const/4 v1, 0x0

    :goto_0
    return v1
.end method

.method public rS()I
    .locals 1

    iget v0, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->mC:I

    return v0
.end method

.method public svN()J
    .locals 2

    iget-wide v0, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->JW:J

    return-wide v0
.end method

.method public svN(I)V
    .locals 0

    iput p1, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->cB:I

    return-void
.end method

.method public vYf()I
    .locals 1

    iget v0, p0, Lcom/bykv/vk/openvk/component/video/api/hjc/hjc;->cB:I

    return v0
.end method
