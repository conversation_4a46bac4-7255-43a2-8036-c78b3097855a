.class public interface abstract Lcom/bykv/vk/openvk/component/video/api/renderview/Fj;
.super Ljava/lang/Object;


# virtual methods
.method public abstract Fj(Landroid/graphics/SurfaceTexture;II)V
.end method

.method public abstract Fj(Landroid/view/SurfaceHolder;)V
.end method

.method public abstract Fj(Landroid/view/SurfaceHolder;III)V
.end method

.method public abstract Fj(Landroid/graphics/SurfaceTexture;)Z
.end method

.method public abstract ex(Landroid/view/SurfaceHolder;)V
.end method
