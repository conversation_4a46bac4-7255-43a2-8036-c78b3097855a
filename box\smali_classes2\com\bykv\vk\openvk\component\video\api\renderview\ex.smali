.class public interface abstract Lcom/bykv/vk/openvk/component/video/api/renderview/ex;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/bykv/vk/openvk/component/video/api/renderview/ex$Fj;
    }
.end annotation


# virtual methods
.method public abstract Fj(II)V
.end method

.method public abstract Fj(Lcom/bykv/vk/openvk/component/video/api/renderview/Fj;)V
.end method

.method public abstract getHolder()Landroid/view/SurfaceHolder;
.end method

.method public abstract getView()Landroid/view/View;
.end method

.method public abstract setVisibility(I)V
.end method
