.class public final Lcom/bykv/vk/openvk/preload/a/b/a/n;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/bykv/vk/openvk/preload/a/b/a/n$a;
    }
.end annotation


# static fields
.field public static final A:Lcom/bykv/vk/openvk/preload/a/t;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/bykv/vk/openvk/preload/a/t<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field public static final B:Lcom/bykv/vk/openvk/preload/a/t;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/bykv/vk/openvk/preload/a/t<",
            "Ljava/math/BigDecimal;",
            ">;"
        }
    .end annotation
.end field

.field public static final C:Lcom/bykv/vk/openvk/preload/a/t;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/bykv/vk/openvk/preload/a/t<",
            "Ljava/math/BigInteger;",
            ">;"
        }
    .end annotation
.end field

.field public static final D:Lcom/bykv/vk/openvk/preload/a/u;

.field public static final E:Lcom/bykv/vk/openvk/preload/a/t;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/bykv/vk/openvk/preload/a/t<",
            "Ljava/lang/StringBuilder;",
            ">;"
        }
    .end annotation
.end field

.field public static final F:Lcom/bykv/vk/openvk/preload/a/u;

.field public static final G:Lcom/bykv/vk/openvk/preload/a/t;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/bykv/vk/openvk/preload/a/t<",
            "Ljava/lang/StringBuffer;",
            ">;"
        }
    .end annotation
.end field

.field public static final H:Lcom/bykv/vk/openvk/preload/a/u;

.field public static final I:Lcom/bykv/vk/openvk/preload/a/t;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/bykv/vk/openvk/preload/a/t<",
            "Ljava/net/URL;",
            ">;"
        }
    .end annotation
.end field

.field public static final J:Lcom/bykv/vk/openvk/preload/a/u;

.field public static final K:Lcom/bykv/vk/openvk/preload/a/t;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/bykv/vk/openvk/preload/a/t<",
            "Ljava/net/URI;",
            ">;"
        }
    .end annotation
.end field

.field public static final L:Lcom/bykv/vk/openvk/preload/a/u;

.field public static final M:Lcom/bykv/vk/openvk/preload/a/t;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/bykv/vk/openvk/preload/a/t<",
            "Ljava/net/InetAddress;",
            ">;"
        }
    .end annotation
.end field

.field public static final N:Lcom/bykv/vk/openvk/preload/a/u;

.field public static final O:Lcom/bykv/vk/openvk/preload/a/t;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/bykv/vk/openvk/preload/a/t<",
            "Ljava/util/UUID;",
            ">;"
        }
    .end annotation
.end field

.field public static final P:Lcom/bykv/vk/openvk/preload/a/u;

.field public static final Q:Lcom/bykv/vk/openvk/preload/a/t;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/bykv/vk/openvk/preload/a/t<",
            "Ljava/util/Currency;",
            ">;"
        }
    .end annotation
.end field

.field public static final R:Lcom/bykv/vk/openvk/preload/a/u;

.field public static final S:Lcom/bykv/vk/openvk/preload/a/u;

.field public static final T:Lcom/bykv/vk/openvk/preload/a/t;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/bykv/vk/openvk/preload/a/t<",
            "Ljava/util/Calendar;",
            ">;"
        }
    .end annotation
.end field

.field public static final U:Lcom/bykv/vk/openvk/preload/a/u;

.field public static final V:Lcom/bykv/vk/openvk/preload/a/t;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/bykv/vk/openvk/preload/a/t<",
            "Ljava/util/Locale;",
            ">;"
        }
    .end annotation
.end field

.field public static final W:Lcom/bykv/vk/openvk/preload/a/u;

.field public static final X:Lcom/bykv/vk/openvk/preload/a/t;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/bykv/vk/openvk/preload/a/t<",
            "Lcom/bykv/vk/openvk/preload/a/k;",
            ">;"
        }
    .end annotation
.end field

.field public static final Y:Lcom/bykv/vk/openvk/preload/a/u;

.field public static final Z:Lcom/bykv/vk/openvk/preload/a/u;

.field public static final a:Lcom/bykv/vk/openvk/preload/a/t;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/bykv/vk/openvk/preload/a/t<",
            "Ljava/lang/Class;",
            ">;"
        }
    .end annotation
.end field

.field public static final b:Lcom/bykv/vk/openvk/preload/a/u;

.field public static final c:Lcom/bykv/vk/openvk/preload/a/t;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/bykv/vk/openvk/preload/a/t<",
            "Ljava/util/BitSet;",
            ">;"
        }
    .end annotation
.end field

.field public static final d:Lcom/bykv/vk/openvk/preload/a/u;

.field public static final e:Lcom/bykv/vk/openvk/preload/a/t;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/bykv/vk/openvk/preload/a/t<",
            "Ljava/lang/Boolean;",
            ">;"
        }
    .end annotation
.end field

.field public static final f:Lcom/bykv/vk/openvk/preload/a/t;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/bykv/vk/openvk/preload/a/t<",
            "Ljava/lang/Boolean;",
            ">;"
        }
    .end annotation
.end field

.field public static final g:Lcom/bykv/vk/openvk/preload/a/u;

.field public static final h:Lcom/bykv/vk/openvk/preload/a/t;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/bykv/vk/openvk/preload/a/t<",
            "Ljava/lang/Number;",
            ">;"
        }
    .end annotation
.end field

.field public static final i:Lcom/bykv/vk/openvk/preload/a/u;

.field public static final j:Lcom/bykv/vk/openvk/preload/a/t;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/bykv/vk/openvk/preload/a/t<",
            "Ljava/lang/Number;",
            ">;"
        }
    .end annotation
.end field

.field public static final k:Lcom/bykv/vk/openvk/preload/a/u;

.field public static final l:Lcom/bykv/vk/openvk/preload/a/t;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/bykv/vk/openvk/preload/a/t<",
            "Ljava/lang/Number;",
            ">;"
        }
    .end annotation
.end field

.field public static final m:Lcom/bykv/vk/openvk/preload/a/u;

.field public static final n:Lcom/bykv/vk/openvk/preload/a/t;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/bykv/vk/openvk/preload/a/t<",
            "Ljava/util/concurrent/atomic/AtomicInteger;",
            ">;"
        }
    .end annotation
.end field

.field public static final o:Lcom/bykv/vk/openvk/preload/a/u;

.field public static final p:Lcom/bykv/vk/openvk/preload/a/t;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/bykv/vk/openvk/preload/a/t<",
            "Ljava/util/concurrent/atomic/AtomicBoolean;",
            ">;"
        }
    .end annotation
.end field

.field public static final q:Lcom/bykv/vk/openvk/preload/a/u;

.field public static final r:Lcom/bykv/vk/openvk/preload/a/t;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/bykv/vk/openvk/preload/a/t<",
            "Ljava/util/concurrent/atomic/AtomicIntegerArray;",
            ">;"
        }
    .end annotation
.end field

.field public static final s:Lcom/bykv/vk/openvk/preload/a/u;

.field public static final t:Lcom/bykv/vk/openvk/preload/a/t;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/bykv/vk/openvk/preload/a/t<",
            "Ljava/lang/Number;",
            ">;"
        }
    .end annotation
.end field

.field public static final u:Lcom/bykv/vk/openvk/preload/a/t;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/bykv/vk/openvk/preload/a/t<",
            "Ljava/lang/Number;",
            ">;"
        }
    .end annotation
.end field

.field public static final v:Lcom/bykv/vk/openvk/preload/a/t;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/bykv/vk/openvk/preload/a/t<",
            "Ljava/lang/Number;",
            ">;"
        }
    .end annotation
.end field

.field public static final w:Lcom/bykv/vk/openvk/preload/a/t;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/bykv/vk/openvk/preload/a/t<",
            "Ljava/lang/Number;",
            ">;"
        }
    .end annotation
.end field

.field public static final x:Lcom/bykv/vk/openvk/preload/a/u;

.field public static final y:Lcom/bykv/vk/openvk/preload/a/t;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/bykv/vk/openvk/preload/a/t<",
            "Ljava/lang/Character;",
            ">;"
        }
    .end annotation
.end field

.field public static final z:Lcom/bykv/vk/openvk/preload/a/u;


# direct methods
.method static constructor <clinit>()V
    .locals 4

    new-instance v0, Lcom/bykv/vk/openvk/preload/a/b/a/n$1;

    invoke-direct {v0}, Lcom/bykv/vk/openvk/preload/a/b/a/n$1;-><init>()V

    invoke-virtual {v0}, Lcom/bykv/vk/openvk/preload/a/t;->a()Lcom/bykv/vk/openvk/preload/a/t;

    move-result-object v0

    sput-object v0, Lcom/bykv/vk/openvk/preload/a/b/a/n;->a:Lcom/bykv/vk/openvk/preload/a/t;

    const-class v1, Ljava/lang/Class;

    invoke-static {v1, v0}, Lcom/bykv/vk/openvk/preload/a/b/a/n;->a(Ljava/lang/Class;Lcom/bykv/vk/openvk/preload/a/t;)Lcom/bykv/vk/openvk/preload/a/u;

    move-result-object v0

    sput-object v0, Lcom/bykv/vk/openvk/preload/a/b/a/n;->b:Lcom/bykv/vk/openvk/preload/a/u;

    new-instance v0, Lcom/bykv/vk/openvk/preload/a/b/a/n$12;

    invoke-direct {v0}, Lcom/bykv/vk/openvk/preload/a/b/a/n$12;-><init>()V

    invoke-virtual {v0}, Lcom/bykv/vk/openvk/preload/a/t;->a()Lcom/bykv/vk/openvk/preload/a/t;

    move-result-object v0

    sput-object v0, Lcom/bykv/vk/openvk/preload/a/b/a/n;->c:Lcom/bykv/vk/openvk/preload/a/t;

    const-class v1, Ljava/util/BitSet;

    invoke-static {v1, v0}, Lcom/bykv/vk/openvk/preload/a/b/a/n;->a(Ljava/lang/Class;Lcom/bykv/vk/openvk/preload/a/t;)Lcom/bykv/vk/openvk/preload/a/u;

    move-result-object v0

    sput-object v0, Lcom/bykv/vk/openvk/preload/a/b/a/n;->d:Lcom/bykv/vk/openvk/preload/a/u;

    new-instance v0, Lcom/bykv/vk/openvk/preload/a/b/a/n$23;

    invoke-direct {v0}, Lcom/bykv/vk/openvk/preload/a/b/a/n$23;-><init>()V

    sput-object v0, Lcom/bykv/vk/openvk/preload/a/b/a/n;->e:Lcom/bykv/vk/openvk/preload/a/t;

    new-instance v1, Lcom/bykv/vk/openvk/preload/a/b/a/n$31;

    invoke-direct {v1}, Lcom/bykv/vk/openvk/preload/a/b/a/n$31;-><init>()V

    sput-object v1, Lcom/bykv/vk/openvk/preload/a/b/a/n;->f:Lcom/bykv/vk/openvk/preload/a/t;

    sget-object v1, Ljava/lang/Boolean;->TYPE:Ljava/lang/Class;

    const-class v2, Ljava/lang/Boolean;

    invoke-static {v1, v2, v0}, Lcom/bykv/vk/openvk/preload/a/b/a/n;->a(Ljava/lang/Class;Ljava/lang/Class;Lcom/bykv/vk/openvk/preload/a/t;)Lcom/bykv/vk/openvk/preload/a/u;

    move-result-object v0

    sput-object v0, Lcom/bykv/vk/openvk/preload/a/b/a/n;->g:Lcom/bykv/vk/openvk/preload/a/u;

    new-instance v0, Lcom/bykv/vk/openvk/preload/a/b/a/n$32;

    invoke-direct {v0}, Lcom/bykv/vk/openvk/preload/a/b/a/n$32;-><init>()V

    sput-object v0, Lcom/bykv/vk/openvk/preload/a/b/a/n;->h:Lcom/bykv/vk/openvk/preload/a/t;

    sget-object v1, Ljava/lang/Byte;->TYPE:Ljava/lang/Class;

    const-class v2, Ljava/lang/Byte;

    invoke-static {v1, v2, v0}, Lcom/bykv/vk/openvk/preload/a/b/a/n;->a(Ljava/lang/Class;Ljava/lang/Class;Lcom/bykv/vk/openvk/preload/a/t;)Lcom/bykv/vk/openvk/preload/a/u;

    move-result-object v0

    sput-object v0, Lcom/bykv/vk/openvk/preload/a/b/a/n;->i:Lcom/bykv/vk/openvk/preload/a/u;

    new-instance v0, Lcom/bykv/vk/openvk/preload/a/b/a/n$33;

    invoke-direct {v0}, Lcom/bykv/vk/openvk/preload/a/b/a/n$33;-><init>()V

    sput-object v0, Lcom/bykv/vk/openvk/preload/a/b/a/n;->j:Lcom/bykv/vk/openvk/preload/a/t;

    sget-object v1, Ljava/lang/Short;->TYPE:Ljava/lang/Class;

    const-class v2, Ljava/lang/Short;

    invoke-static {v1, v2, v0}, Lcom/bykv/vk/openvk/preload/a/b/a/n;->a(Ljava/lang/Class;Ljava/lang/Class;Lcom/bykv/vk/openvk/preload/a/t;)Lcom/bykv/vk/openvk/preload/a/u;

    move-result-object v0

    sput-object v0, Lcom/bykv/vk/openvk/preload/a/b/a/n;->k:Lcom/bykv/vk/openvk/preload/a/u;

    new-instance v0, Lcom/bykv/vk/openvk/preload/a/b/a/n$34;

    invoke-direct {v0}, Lcom/bykv/vk/openvk/preload/a/b/a/n$34;-><init>()V

    sput-object v0, Lcom/bykv/vk/openvk/preload/a/b/a/n;->l:Lcom/bykv/vk/openvk/preload/a/t;

    sget-object v1, Ljava/lang/Integer;->TYPE:Ljava/lang/Class;

    const-class v2, Ljava/lang/Integer;

    invoke-static {v1, v2, v0}, Lcom/bykv/vk/openvk/preload/a/b/a/n;->a(Ljava/lang/Class;Ljava/lang/Class;Lcom/bykv/vk/openvk/preload/a/t;)Lcom/bykv/vk/openvk/preload/a/u;

    move-result-object v0

    sput-object v0, Lcom/bykv/vk/openvk/preload/a/b/a/n;->m:Lcom/bykv/vk/openvk/preload/a/u;

    new-instance v0, Lcom/bykv/vk/openvk/preload/a/b/a/n$35;

    invoke-direct {v0}, Lcom/bykv/vk/openvk/preload/a/b/a/n$35;-><init>()V

    invoke-virtual {v0}, Lcom/bykv/vk/openvk/preload/a/t;->a()Lcom/bykv/vk/openvk/preload/a/t;

    move-result-object v0

    sput-object v0, Lcom/bykv/vk/openvk/preload/a/b/a/n;->n:Lcom/bykv/vk/openvk/preload/a/t;

    const-class v1, Ljava/util/concurrent/atomic/AtomicInteger;

    invoke-static {v1, v0}, Lcom/bykv/vk/openvk/preload/a/b/a/n;->a(Ljava/lang/Class;Lcom/bykv/vk/openvk/preload/a/t;)Lcom/bykv/vk/openvk/preload/a/u;

    move-result-object v0

    sput-object v0, Lcom/bykv/vk/openvk/preload/a/b/a/n;->o:Lcom/bykv/vk/openvk/preload/a/u;

    new-instance v0, Lcom/bykv/vk/openvk/preload/a/b/a/n$36;

    invoke-direct {v0}, Lcom/bykv/vk/openvk/preload/a/b/a/n$36;-><init>()V

    invoke-virtual {v0}, Lcom/bykv/vk/openvk/preload/a/t;->a()Lcom/bykv/vk/openvk/preload/a/t;

    move-result-object v0

    sput-object v0, Lcom/bykv/vk/openvk/preload/a/b/a/n;->p:Lcom/bykv/vk/openvk/preload/a/t;

    const-class v1, Ljava/util/concurrent/atomic/AtomicBoolean;

    invoke-static {v1, v0}, Lcom/bykv/vk/openvk/preload/a/b/a/n;->a(Ljava/lang/Class;Lcom/bykv/vk/openvk/preload/a/t;)Lcom/bykv/vk/openvk/preload/a/u;

    move-result-object v0

    sput-object v0, Lcom/bykv/vk/openvk/preload/a/b/a/n;->q:Lcom/bykv/vk/openvk/preload/a/u;

    new-instance v0, Lcom/bykv/vk/openvk/preload/a/b/a/n$2;

    invoke-direct {v0}, Lcom/bykv/vk/openvk/preload/a/b/a/n$2;-><init>()V

    invoke-virtual {v0}, Lcom/bykv/vk/openvk/preload/a/t;->a()Lcom/bykv/vk/openvk/preload/a/t;

    move-result-object v0

    sput-object v0, Lcom/bykv/vk/openvk/preload/a/b/a/n;->r:Lcom/bykv/vk/openvk/preload/a/t;

    const-class v1, Ljava/util/concurrent/atomic/AtomicIntegerArray;

    invoke-static {v1, v0}, Lcom/bykv/vk/openvk/preload/a/b/a/n;->a(Ljava/lang/Class;Lcom/bykv/vk/openvk/preload/a/t;)Lcom/bykv/vk/openvk/preload/a/u;

    move-result-object v0

    sput-object v0, Lcom/bykv/vk/openvk/preload/a/b/a/n;->s:Lcom/bykv/vk/openvk/preload/a/u;

    new-instance v0, Lcom/bykv/vk/openvk/preload/a/b/a/n$3;

    invoke-direct {v0}, Lcom/bykv/vk/openvk/preload/a/b/a/n$3;-><init>()V

    sput-object v0, Lcom/bykv/vk/openvk/preload/a/b/a/n;->t:Lcom/bykv/vk/openvk/preload/a/t;

    new-instance v0, Lcom/bykv/vk/openvk/preload/a/b/a/n$4;

    invoke-direct {v0}, Lcom/bykv/vk/openvk/preload/a/b/a/n$4;-><init>()V

    sput-object v0, Lcom/bykv/vk/openvk/preload/a/b/a/n;->u:Lcom/bykv/vk/openvk/preload/a/t;

    new-instance v0, Lcom/bykv/vk/openvk/preload/a/b/a/n$5;

    invoke-direct {v0}, Lcom/bykv/vk/openvk/preload/a/b/a/n$5;-><init>()V

    sput-object v0, Lcom/bykv/vk/openvk/preload/a/b/a/n;->v:Lcom/bykv/vk/openvk/preload/a/t;

    new-instance v0, Lcom/bykv/vk/openvk/preload/a/b/a/n$6;

    invoke-direct {v0}, Lcom/bykv/vk/openvk/preload/a/b/a/n$6;-><init>()V

    sput-object v0, Lcom/bykv/vk/openvk/preload/a/b/a/n;->w:Lcom/bykv/vk/openvk/preload/a/t;

    const-class v1, Ljava/lang/Number;

    invoke-static {v1, v0}, Lcom/bykv/vk/openvk/preload/a/b/a/n;->a(Ljava/lang/Class;Lcom/bykv/vk/openvk/preload/a/t;)Lcom/bykv/vk/openvk/preload/a/u;

    move-result-object v0

    sput-object v0, Lcom/bykv/vk/openvk/preload/a/b/a/n;->x:Lcom/bykv/vk/openvk/preload/a/u;

    new-instance v0, Lcom/bykv/vk/openvk/preload/a/b/a/n$7;

    invoke-direct {v0}, Lcom/bykv/vk/openvk/preload/a/b/a/n$7;-><init>()V

    sput-object v0, Lcom/bykv/vk/openvk/preload/a/b/a/n;->y:Lcom/bykv/vk/openvk/preload/a/t;

    sget-object v1, Ljava/lang/Character;->TYPE:Ljava/lang/Class;

    const-class v2, Ljava/lang/Character;

    invoke-static {v1, v2, v0}, Lcom/bykv/vk/openvk/preload/a/b/a/n;->a(Ljava/lang/Class;Ljava/lang/Class;Lcom/bykv/vk/openvk/preload/a/t;)Lcom/bykv/vk/openvk/preload/a/u;

    move-result-object v0

    sput-object v0, Lcom/bykv/vk/openvk/preload/a/b/a/n;->z:Lcom/bykv/vk/openvk/preload/a/u;

    new-instance v0, Lcom/bykv/vk/openvk/preload/a/b/a/n$8;

    invoke-direct {v0}, Lcom/bykv/vk/openvk/preload/a/b/a/n$8;-><init>()V

    sput-object v0, Lcom/bykv/vk/openvk/preload/a/b/a/n;->A:Lcom/bykv/vk/openvk/preload/a/t;

    new-instance v1, Lcom/bykv/vk/openvk/preload/a/b/a/n$9;

    invoke-direct {v1}, Lcom/bykv/vk/openvk/preload/a/b/a/n$9;-><init>()V

    sput-object v1, Lcom/bykv/vk/openvk/preload/a/b/a/n;->B:Lcom/bykv/vk/openvk/preload/a/t;

    new-instance v1, Lcom/bykv/vk/openvk/preload/a/b/a/n$10;

    invoke-direct {v1}, Lcom/bykv/vk/openvk/preload/a/b/a/n$10;-><init>()V

    sput-object v1, Lcom/bykv/vk/openvk/preload/a/b/a/n;->C:Lcom/bykv/vk/openvk/preload/a/t;

    const-class v1, Ljava/lang/String;

    invoke-static {v1, v0}, Lcom/bykv/vk/openvk/preload/a/b/a/n;->a(Ljava/lang/Class;Lcom/bykv/vk/openvk/preload/a/t;)Lcom/bykv/vk/openvk/preload/a/u;

    move-result-object v0

    sput-object v0, Lcom/bykv/vk/openvk/preload/a/b/a/n;->D:Lcom/bykv/vk/openvk/preload/a/u;

    new-instance v0, Lcom/bykv/vk/openvk/preload/a/b/a/n$11;

    invoke-direct {v0}, Lcom/bykv/vk/openvk/preload/a/b/a/n$11;-><init>()V

    sput-object v0, Lcom/bykv/vk/openvk/preload/a/b/a/n;->E:Lcom/bykv/vk/openvk/preload/a/t;

    const-class v1, Ljava/lang/StringBuilder;

    invoke-static {v1, v0}, Lcom/bykv/vk/openvk/preload/a/b/a/n;->a(Ljava/lang/Class;Lcom/bykv/vk/openvk/preload/a/t;)Lcom/bykv/vk/openvk/preload/a/u;

    move-result-object v0

    sput-object v0, Lcom/bykv/vk/openvk/preload/a/b/a/n;->F:Lcom/bykv/vk/openvk/preload/a/u;

    new-instance v0, Lcom/bykv/vk/openvk/preload/a/b/a/n$13;

    invoke-direct {v0}, Lcom/bykv/vk/openvk/preload/a/b/a/n$13;-><init>()V

    sput-object v0, Lcom/bykv/vk/openvk/preload/a/b/a/n;->G:Lcom/bykv/vk/openvk/preload/a/t;

    const-class v1, Ljava/lang/StringBuffer;

    invoke-static {v1, v0}, Lcom/bykv/vk/openvk/preload/a/b/a/n;->a(Ljava/lang/Class;Lcom/bykv/vk/openvk/preload/a/t;)Lcom/bykv/vk/openvk/preload/a/u;

    move-result-object v0

    sput-object v0, Lcom/bykv/vk/openvk/preload/a/b/a/n;->H:Lcom/bykv/vk/openvk/preload/a/u;

    new-instance v0, Lcom/bykv/vk/openvk/preload/a/b/a/n$14;

    invoke-direct {v0}, Lcom/bykv/vk/openvk/preload/a/b/a/n$14;-><init>()V

    sput-object v0, Lcom/bykv/vk/openvk/preload/a/b/a/n;->I:Lcom/bykv/vk/openvk/preload/a/t;

    const-class v1, Ljava/net/URL;

    invoke-static {v1, v0}, Lcom/bykv/vk/openvk/preload/a/b/a/n;->a(Ljava/lang/Class;Lcom/bykv/vk/openvk/preload/a/t;)Lcom/bykv/vk/openvk/preload/a/u;

    move-result-object v0

    sput-object v0, Lcom/bykv/vk/openvk/preload/a/b/a/n;->J:Lcom/bykv/vk/openvk/preload/a/u;

    new-instance v0, Lcom/bykv/vk/openvk/preload/a/b/a/n$15;

    invoke-direct {v0}, Lcom/bykv/vk/openvk/preload/a/b/a/n$15;-><init>()V

    sput-object v0, Lcom/bykv/vk/openvk/preload/a/b/a/n;->K:Lcom/bykv/vk/openvk/preload/a/t;

    const-class v1, Ljava/net/URI;

    invoke-static {v1, v0}, Lcom/bykv/vk/openvk/preload/a/b/a/n;->a(Ljava/lang/Class;Lcom/bykv/vk/openvk/preload/a/t;)Lcom/bykv/vk/openvk/preload/a/u;

    move-result-object v0

    sput-object v0, Lcom/bykv/vk/openvk/preload/a/b/a/n;->L:Lcom/bykv/vk/openvk/preload/a/u;

    new-instance v0, Lcom/bykv/vk/openvk/preload/a/b/a/n$16;

    invoke-direct {v0}, Lcom/bykv/vk/openvk/preload/a/b/a/n$16;-><init>()V

    sput-object v0, Lcom/bykv/vk/openvk/preload/a/b/a/n;->M:Lcom/bykv/vk/openvk/preload/a/t;

    const-class v1, Ljava/net/InetAddress;

    invoke-static {v1, v0}, Lcom/bykv/vk/openvk/preload/a/b/a/n;->b(Ljava/lang/Class;Lcom/bykv/vk/openvk/preload/a/t;)Lcom/bykv/vk/openvk/preload/a/u;

    move-result-object v0

    sput-object v0, Lcom/bykv/vk/openvk/preload/a/b/a/n;->N:Lcom/bykv/vk/openvk/preload/a/u;

    new-instance v0, Lcom/bykv/vk/openvk/preload/a/b/a/n$17;

    invoke-direct {v0}, Lcom/bykv/vk/openvk/preload/a/b/a/n$17;-><init>()V

    sput-object v0, Lcom/bykv/vk/openvk/preload/a/b/a/n;->O:Lcom/bykv/vk/openvk/preload/a/t;

    const-class v1, Ljava/util/UUID;

    invoke-static {v1, v0}, Lcom/bykv/vk/openvk/preload/a/b/a/n;->a(Ljava/lang/Class;Lcom/bykv/vk/openvk/preload/a/t;)Lcom/bykv/vk/openvk/preload/a/u;

    move-result-object v0

    sput-object v0, Lcom/bykv/vk/openvk/preload/a/b/a/n;->P:Lcom/bykv/vk/openvk/preload/a/u;

    new-instance v0, Lcom/bykv/vk/openvk/preload/a/b/a/n$18;

    invoke-direct {v0}, Lcom/bykv/vk/openvk/preload/a/b/a/n$18;-><init>()V

    invoke-virtual {v0}, Lcom/bykv/vk/openvk/preload/a/t;->a()Lcom/bykv/vk/openvk/preload/a/t;

    move-result-object v0

    sput-object v0, Lcom/bykv/vk/openvk/preload/a/b/a/n;->Q:Lcom/bykv/vk/openvk/preload/a/t;

    const-class v1, Ljava/util/Currency;

    invoke-static {v1, v0}, Lcom/bykv/vk/openvk/preload/a/b/a/n;->a(Ljava/lang/Class;Lcom/bykv/vk/openvk/preload/a/t;)Lcom/bykv/vk/openvk/preload/a/u;

    move-result-object v0

    sput-object v0, Lcom/bykv/vk/openvk/preload/a/b/a/n;->R:Lcom/bykv/vk/openvk/preload/a/u;

    new-instance v0, Lcom/bykv/vk/openvk/preload/a/b/a/n$19;

    invoke-direct {v0}, Lcom/bykv/vk/openvk/preload/a/b/a/n$19;-><init>()V

    sput-object v0, Lcom/bykv/vk/openvk/preload/a/b/a/n;->S:Lcom/bykv/vk/openvk/preload/a/u;

    new-instance v0, Lcom/bykv/vk/openvk/preload/a/b/a/n$20;

    invoke-direct {v0}, Lcom/bykv/vk/openvk/preload/a/b/a/n$20;-><init>()V

    sput-object v0, Lcom/bykv/vk/openvk/preload/a/b/a/n;->T:Lcom/bykv/vk/openvk/preload/a/t;

    new-instance v1, Lcom/bykv/vk/openvk/preload/a/b/a/n$28;

    const-class v2, Ljava/util/Calendar;

    const-class v3, Ljava/util/GregorianCalendar;

    invoke-direct {v1, v2, v3, v0}, Lcom/bykv/vk/openvk/preload/a/b/a/n$28;-><init>(Ljava/lang/Class;Ljava/lang/Class;Lcom/bykv/vk/openvk/preload/a/t;)V

    sput-object v1, Lcom/bykv/vk/openvk/preload/a/b/a/n;->U:Lcom/bykv/vk/openvk/preload/a/u;

    new-instance v0, Lcom/bykv/vk/openvk/preload/a/b/a/n$21;

    invoke-direct {v0}, Lcom/bykv/vk/openvk/preload/a/b/a/n$21;-><init>()V

    sput-object v0, Lcom/bykv/vk/openvk/preload/a/b/a/n;->V:Lcom/bykv/vk/openvk/preload/a/t;

    const-class v1, Ljava/util/Locale;

    invoke-static {v1, v0}, Lcom/bykv/vk/openvk/preload/a/b/a/n;->a(Ljava/lang/Class;Lcom/bykv/vk/openvk/preload/a/t;)Lcom/bykv/vk/openvk/preload/a/u;

    move-result-object v0

    sput-object v0, Lcom/bykv/vk/openvk/preload/a/b/a/n;->W:Lcom/bykv/vk/openvk/preload/a/u;

    new-instance v0, Lcom/bykv/vk/openvk/preload/a/b/a/n$22;

    invoke-direct {v0}, Lcom/bykv/vk/openvk/preload/a/b/a/n$22;-><init>()V

    sput-object v0, Lcom/bykv/vk/openvk/preload/a/b/a/n;->X:Lcom/bykv/vk/openvk/preload/a/t;

    const-class v1, Lcom/bykv/vk/openvk/preload/a/k;

    invoke-static {v1, v0}, Lcom/bykv/vk/openvk/preload/a/b/a/n;->b(Ljava/lang/Class;Lcom/bykv/vk/openvk/preload/a/t;)Lcom/bykv/vk/openvk/preload/a/u;

    move-result-object v0

    sput-object v0, Lcom/bykv/vk/openvk/preload/a/b/a/n;->Y:Lcom/bykv/vk/openvk/preload/a/u;

    new-instance v0, Lcom/bykv/vk/openvk/preload/a/b/a/n$24;

    invoke-direct {v0}, Lcom/bykv/vk/openvk/preload/a/b/a/n$24;-><init>()V

    sput-object v0, Lcom/bykv/vk/openvk/preload/a/b/a/n;->Z:Lcom/bykv/vk/openvk/preload/a/u;

    return-void
.end method

.method public static a(Lcom/bykv/vk/openvk/preload/a/c/a;Lcom/bykv/vk/openvk/preload/a/t;)Lcom/bykv/vk/openvk/preload/a/u;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<TT:",
            "Ljava/lang/Object;",
            ">(",
            "Lcom/bykv/vk/openvk/preload/a/c/a<",
            "TTT;>;",
            "Lcom/bykv/vk/openvk/preload/a/t<",
            "TTT;>;)",
            "Lcom/bykv/vk/openvk/preload/a/u;"
        }
    .end annotation

    new-instance v0, Lcom/bykv/vk/openvk/preload/a/b/a/n$25;

    invoke-direct {v0, p0, p1}, Lcom/bykv/vk/openvk/preload/a/b/a/n$25;-><init>(Lcom/bykv/vk/openvk/preload/a/c/a;Lcom/bykv/vk/openvk/preload/a/t;)V

    return-object v0
.end method

.method public static a(Ljava/lang/Class;Lcom/bykv/vk/openvk/preload/a/t;)Lcom/bykv/vk/openvk/preload/a/u;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<TT:",
            "Ljava/lang/Object;",
            ">(",
            "Ljava/lang/Class<",
            "TTT;>;",
            "Lcom/bykv/vk/openvk/preload/a/t<",
            "TTT;>;)",
            "Lcom/bykv/vk/openvk/preload/a/u;"
        }
    .end annotation

    new-instance v0, Lcom/bykv/vk/openvk/preload/a/b/a/n$26;

    invoke-direct {v0, p0, p1}, Lcom/bykv/vk/openvk/preload/a/b/a/n$26;-><init>(Ljava/lang/Class;Lcom/bykv/vk/openvk/preload/a/t;)V

    return-object v0
.end method

.method public static a(Ljava/lang/Class;Ljava/lang/Class;Lcom/bykv/vk/openvk/preload/a/t;)Lcom/bykv/vk/openvk/preload/a/u;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<TT:",
            "Ljava/lang/Object;",
            ">(",
            "Ljava/lang/Class<",
            "TTT;>;",
            "Ljava/lang/Class<",
            "TTT;>;",
            "Lcom/bykv/vk/openvk/preload/a/t<",
            "-TTT;>;)",
            "Lcom/bykv/vk/openvk/preload/a/u;"
        }
    .end annotation

    new-instance v0, Lcom/bykv/vk/openvk/preload/a/b/a/n$27;

    invoke-direct {v0, p0, p1, p2}, Lcom/bykv/vk/openvk/preload/a/b/a/n$27;-><init>(Ljava/lang/Class;Ljava/lang/Class;Lcom/bykv/vk/openvk/preload/a/t;)V

    return-object v0
.end method

.method private static b(Ljava/lang/Class;Lcom/bykv/vk/openvk/preload/a/t;)Lcom/bykv/vk/openvk/preload/a/u;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T1:",
            "Ljava/lang/Object;",
            ">(",
            "Ljava/lang/Class<",
            "TT1;>;",
            "Lcom/bykv/vk/openvk/preload/a/t<",
            "TT1;>;)",
            "Lcom/bykv/vk/openvk/preload/a/u;"
        }
    .end annotation

    new-instance v0, Lcom/bykv/vk/openvk/preload/a/b/a/n$29;

    invoke-direct {v0, p0, p1}, Lcom/bykv/vk/openvk/preload/a/b/a/n$29;-><init>(Ljava/lang/Class;Lcom/bykv/vk/openvk/preload/a/t;)V

    return-object v0
.end method
