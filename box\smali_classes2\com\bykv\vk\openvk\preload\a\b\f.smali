.class public abstract Lcom/bykv/vk/openvk/preload/a/b/f;
.super Ljava/lang/Object;


# static fields
.field public static a:Lcom/bykv/vk/openvk/preload/a/b/f;


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public abstract a(Lcom/bykv/vk/openvk/preload/a/d/a;)V
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation
.end method
