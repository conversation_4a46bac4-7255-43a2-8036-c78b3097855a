.class public final Lcom/bykv/vk/openvk/preload/a/c;
.super Ljava/lang/Object;


# instance fields
.field private final a:Ljava/lang/reflect/Field;


# direct methods
.method public constructor <init>(Ljava/lang/reflect/Field;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    invoke-static {p1}, Lcom/bykv/vk/openvk/preload/a/b/a;->a(Ljava/lang/Object;)Ljava/lang/Object;

    iput-object p1, p0, Lcom/bykv/vk/openvk/preload/a/c;->a:Ljava/lang/reflect/Field;

    return-void
.end method
