.class public interface abstract Lcom/bytedance/sdk/component/ex/Fj/hjc;
.super Ljava/lang/Object;


# virtual methods
.method public abstract Fj(Lcom/bytedance/sdk/component/ex/Fj/ex;Lcom/bytedance/sdk/component/ex/Fj/JW;)V
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation
.end method

.method public abstract Fj(Lcom/bytedance/sdk/component/ex/Fj/ex;Ljava/io/IOException;)V
.end method
