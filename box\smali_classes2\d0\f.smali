.class public final Ld0/f;
.super Ljava/lang/Object;


# annotations
.annotation runtime Lkotlin/Metadata;
.end annotation


# direct methods
.method public static final a(Ld0/e;)Ld0/i;
    .locals 4

    new-instance v0, Ld0/i;

    invoke-virtual {p0}, Ld0/e;->b()F

    move-result v1

    invoke-virtual {p0}, Ld0/e;->d()F

    move-result v2

    invoke-virtual {p0}, Ld0/e;->c()F

    move-result v3

    invoke-virtual {p0}, Ld0/e;->a()F

    move-result p0

    invoke-direct {v0, v1, v2, v3, p0}, Ld0/i;-><init>(FFFF)V

    return-object v0
.end method
