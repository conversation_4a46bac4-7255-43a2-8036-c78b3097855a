.class public final Ld0/j;
.super Ljava/lang/Object;


# annotations
.annotation runtime Lkotlin/Metadata;
.end annotation


# direct methods
.method public static final a(JJ)Ld0/i;
    .locals 5

    new-instance v0, Ld0/i;

    invoke-static {p0, p1}, Ld0/g;->m(J)F

    move-result v1

    invoke-static {p0, p1}, Ld0/g;->n(J)F

    move-result v2

    invoke-static {p0, p1}, Ld0/g;->m(J)F

    move-result v3

    invoke-static {p2, p3}, Ld0/m;->i(J)F

    move-result v4

    add-float/2addr v3, v4

    invoke-static {p0, p1}, Ld0/g;->n(J)F

    move-result p0

    invoke-static {p2, p3}, Ld0/m;->g(J)F

    move-result p1

    add-float/2addr p0, p1

    invoke-direct {v0, v1, v2, v3, p0}, Ld0/i;-><init>(FFFF)V

    return-object v0
.end method
