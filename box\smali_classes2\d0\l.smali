.class public final Ld0/l;
.super Ljava/lang/Object;


# annotations
.annotation runtime Lkotlin/Metadata;
.end annotation


# direct methods
.method public static final a(FFFFFF)Ld0/k;
    .locals 15

    invoke-static/range {p4 .. p5}, Ld0/b;->a(FF)J

    move-result-wide v11

    new-instance v14, Ld0/k;

    const/4 v13, 0x0

    move-object v0, v14

    move v1, p0

    move/from16 v2, p1

    move/from16 v3, p2

    move/from16 v4, p3

    move-wide v5, v11

    move-wide v7, v11

    move-wide v9, v11

    invoke-direct/range {v0 .. v13}, Ld0/k;-><init>(FFFFJJJJLkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-object v14
.end method

.method public static final b(Ld0/i;JJJJ)Ld0/k;
    .locals 15

    new-instance v14, Ld0/k;

    invoke-virtual {p0}, Ld0/i;->f()F

    move-result v1

    invoke-virtual {p0}, Ld0/i;->i()F

    move-result v2

    invoke-virtual {p0}, Ld0/i;->g()F

    move-result v3

    invoke-virtual {p0}, Ld0/i;->c()F

    move-result v4

    const/4 v13, 0x0

    move-object v0, v14

    move-wide/from16 v5, p1

    move-wide/from16 v7, p3

    move-wide/from16 v9, p5

    move-wide/from16 v11, p7

    invoke-direct/range {v0 .. v13}, Ld0/k;-><init>(FFFFJJJJLkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-object v14
.end method

.method public static final c(FFFFJ)Ld0/k;
    .locals 6

    invoke-static {p4, p5}, Ld0/a;->d(J)F

    move-result v4

    invoke-static {p4, p5}, Ld0/a;->e(J)F

    move-result v5

    move v0, p0

    move v1, p1

    move v2, p2

    move v3, p3

    invoke-static/range {v0 .. v5}, Ld0/l;->a(FFFFFF)Ld0/k;

    move-result-object p0

    return-object p0
.end method

.method public static final d(Ld0/k;)Ld0/i;
    .locals 4

    new-instance v0, Ld0/i;

    invoke-virtual {p0}, Ld0/k;->e()F

    move-result v1

    invoke-virtual {p0}, Ld0/k;->g()F

    move-result v2

    invoke-virtual {p0}, Ld0/k;->f()F

    move-result v3

    invoke-virtual {p0}, Ld0/k;->a()F

    move-result p0

    invoke-direct {v0, v1, v2, v3, p0}, Ld0/i;-><init>(FFFF)V

    return-object v0
.end method

.method public static final e(Ld0/k;)Z
    .locals 3

    invoke-virtual {p0}, Ld0/k;->h()J

    move-result-wide v0

    invoke-static {v0, v1}, Ld0/a;->d(J)F

    move-result v0

    invoke-virtual {p0}, Ld0/k;->h()J

    move-result-wide v1

    invoke-static {v1, v2}, Ld0/a;->e(J)F

    move-result v1

    cmpg-float v0, v0, v1

    if-nez v0, :cond_0

    invoke-virtual {p0}, Ld0/k;->h()J

    move-result-wide v0

    invoke-static {v0, v1}, Ld0/a;->d(J)F

    move-result v0

    invoke-virtual {p0}, Ld0/k;->i()J

    move-result-wide v1

    invoke-static {v1, v2}, Ld0/a;->d(J)F

    move-result v1

    cmpg-float v0, v0, v1

    if-nez v0, :cond_0

    invoke-virtual {p0}, Ld0/k;->h()J

    move-result-wide v0

    invoke-static {v0, v1}, Ld0/a;->d(J)F

    move-result v0

    invoke-virtual {p0}, Ld0/k;->i()J

    move-result-wide v1

    invoke-static {v1, v2}, Ld0/a;->e(J)F

    move-result v1

    cmpg-float v0, v0, v1

    if-nez v0, :cond_0

    invoke-virtual {p0}, Ld0/k;->h()J

    move-result-wide v0

    invoke-static {v0, v1}, Ld0/a;->d(J)F

    move-result v0

    invoke-virtual {p0}, Ld0/k;->c()J

    move-result-wide v1

    invoke-static {v1, v2}, Ld0/a;->d(J)F

    move-result v1

    cmpg-float v0, v0, v1

    if-nez v0, :cond_0

    invoke-virtual {p0}, Ld0/k;->h()J

    move-result-wide v0

    invoke-static {v0, v1}, Ld0/a;->d(J)F

    move-result v0

    invoke-virtual {p0}, Ld0/k;->c()J

    move-result-wide v1

    invoke-static {v1, v2}, Ld0/a;->e(J)F

    move-result v1

    cmpg-float v0, v0, v1

    if-nez v0, :cond_0

    invoke-virtual {p0}, Ld0/k;->h()J

    move-result-wide v0

    invoke-static {v0, v1}, Ld0/a;->d(J)F

    move-result v0

    invoke-virtual {p0}, Ld0/k;->b()J

    move-result-wide v1

    invoke-static {v1, v2}, Ld0/a;->d(J)F

    move-result v1

    cmpg-float v0, v0, v1

    if-nez v0, :cond_0

    invoke-virtual {p0}, Ld0/k;->h()J

    move-result-wide v0

    invoke-static {v0, v1}, Ld0/a;->d(J)F

    move-result v0

    invoke-virtual {p0}, Ld0/k;->b()J

    move-result-wide v1

    invoke-static {v1, v2}, Ld0/a;->e(J)F

    move-result p0

    cmpg-float p0, v0, p0

    if-nez p0, :cond_0

    const/4 p0, 0x1

    goto :goto_0

    :cond_0
    const/4 p0, 0x0

    :goto_0
    return p0
.end method
