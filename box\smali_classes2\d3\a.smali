.class public final synthetic Ld3/a;
.super Ljava/lang/Object;

# interfaces
.implements Lz2/e$d;


# instance fields
.field public final synthetic a:Lz2/c0;


# direct methods
.method public synthetic constructor <init>(Lz2/c0;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Ld3/a;->a:Lz2/c0;

    return-void
.end method


# virtual methods
.method public final a(J)J
    .locals 1

    iget-object v0, p0, Ld3/a;->a:Lz2/c0;

    invoke-virtual {v0, p1, p2}, Lz2/c0;->i(J)J

    move-result-wide p1

    return-wide p1
.end method
