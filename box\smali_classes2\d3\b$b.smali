.class public final Ld3/b$b;
.super Ljava/lang/Object;

# interfaces
.implements Lz2/e$f;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Ld3/b;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "b"
.end annotation


# instance fields
.field public final a:Lz2/c0;

.field public final b:I

.field public final c:Lz2/z$a;


# direct methods
.method public constructor <init>(Lz2/c0;I)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Ld3/b$b;->a:Lz2/c0;

    iput p2, p0, Ld3/b$b;->b:I

    new-instance p1, Lz2/z$a;

    invoke-direct {p1}, Lz2/z$a;-><init>()V

    iput-object p1, p0, Ld3/b$b;->c:Lz2/z$a;

    return-void
.end method

.method public synthetic constructor <init>(Lz2/c0;ILd3/b$a;)V
    .locals 0

    invoke-direct {p0, p1, p2}, Ld3/b$b;-><init>(Lz2/c0;I)V

    return-void
.end method


# virtual methods
.method public synthetic a()V
    .locals 0

    invoke-static {p0}, Lz2/f;->a(Lz2/e$f;)V

    return-void
.end method

.method public b(Lz2/t;J)Lz2/e$e;
    .locals 10
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    invoke-interface {p1}, Lz2/t;->getPosition()J

    move-result-wide v0

    invoke-virtual {p0, p1}, Ld3/b$b;->c(Lz2/t;)J

    move-result-wide v2

    invoke-interface {p1}, Lz2/t;->getPeekPosition()J

    move-result-wide v4

    iget-object v6, p0, Ld3/b$b;->a:Lz2/c0;

    iget v6, v6, Lz2/c0;->c:I

    const/4 v7, 0x6

    invoke-static {v7, v6}, Ljava/lang/Math;->max(II)I

    move-result v6

    invoke-interface {p1, v6}, Lz2/t;->advancePeekPosition(I)V

    invoke-virtual {p0, p1}, Ld3/b$b;->c(Lz2/t;)J

    move-result-wide v6

    invoke-interface {p1}, Lz2/t;->getPeekPosition()J

    move-result-wide v8

    cmp-long p1, v2, p2

    if-gtz p1, :cond_0

    cmp-long p1, v6, p2

    if-lez p1, :cond_0

    invoke-static {v4, v5}, Lz2/e$e;->e(J)Lz2/e$e;

    move-result-object p1

    return-object p1

    :cond_0
    cmp-long p1, v6, p2

    if-gtz p1, :cond_1

    invoke-static {v6, v7, v8, v9}, Lz2/e$e;->f(JJ)Lz2/e$e;

    move-result-object p1

    return-object p1

    :cond_1
    invoke-static {v2, v3, v0, v1}, Lz2/e$e;->d(JJ)Lz2/e$e;

    move-result-object p1

    return-object p1
.end method

.method public final c(Lz2/t;)J
    .locals 7
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    :goto_0
    invoke-interface {p1}, Lz2/t;->getPeekPosition()J

    move-result-wide v0

    invoke-interface {p1}, Lz2/t;->getLength()J

    move-result-wide v2

    const-wide/16 v4, 0x6

    sub-long/2addr v2, v4

    cmp-long v6, v0, v2

    if-gez v6, :cond_0

    iget-object v0, p0, Ld3/b$b;->a:Lz2/c0;

    iget v1, p0, Ld3/b$b;->b:I

    iget-object v2, p0, Ld3/b$b;->c:Lz2/z$a;

    invoke-static {p1, v0, v1, v2}, Lz2/z;->h(Lz2/t;Lz2/c0;ILz2/z$a;)Z

    move-result v0

    if-nez v0, :cond_0

    const/4 v0, 0x1

    invoke-interface {p1, v0}, Lz2/t;->advancePeekPosition(I)V

    goto :goto_0

    :cond_0
    invoke-interface {p1}, Lz2/t;->getPeekPosition()J

    move-result-wide v0

    invoke-interface {p1}, Lz2/t;->getLength()J

    move-result-wide v2

    sub-long/2addr v2, v4

    cmp-long v4, v0, v2

    if-ltz v4, :cond_1

    invoke-interface {p1}, Lz2/t;->getLength()J

    move-result-wide v0

    invoke-interface {p1}, Lz2/t;->getPeekPosition()J

    move-result-wide v2

    sub-long/2addr v0, v2

    long-to-int v1, v0

    invoke-interface {p1, v1}, Lz2/t;->advancePeekPosition(I)V

    iget-object p1, p0, Ld3/b$b;->a:Lz2/c0;

    iget-wide v0, p1, Lz2/c0;->j:J

    return-wide v0

    :cond_1
    iget-object p1, p0, Ld3/b$b;->c:Lz2/z$a;

    iget-wide v0, p1, Lz2/z$a;->a:J

    return-wide v0
.end method
