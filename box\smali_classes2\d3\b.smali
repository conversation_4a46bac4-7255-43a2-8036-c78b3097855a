.class public final Ld3/b;
.super Lz2/e;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Ld3/b$b;
    }
.end annotation


# direct methods
.method public constructor <init>(Lz2/c0;IJJ)V
    .locals 16

    move-object/from16 v0, p1

    invoke-static/range {p1 .. p1}, Ljava/util/Objects;->requireNonNull(Ljava/lang/Object;)Ljava/lang/Object;

    new-instance v1, Ld3/a;

    invoke-direct {v1, v0}, Ld3/a;-><init>(Lz2/c0;)V

    new-instance v2, Ld3/b$b;

    const/4 v3, 0x0

    move/from16 v4, p2

    invoke-direct {v2, v0, v4, v3}, Ld3/b$b;-><init>(Lz2/c0;ILd3/b$a;)V

    invoke-virtual/range {p1 .. p1}, Lz2/c0;->f()J

    move-result-wide v3

    const-wide/16 v5, 0x0

    iget-wide v7, v0, Lz2/c0;->j:J

    invoke-virtual/range {p1 .. p1}, Lz2/c0;->d()J

    move-result-wide v13

    const/4 v9, 0x6

    iget v0, v0, Lz2/c0;->c:I

    invoke-static {v9, v0}, Ljava/lang/Math;->max(II)I

    move-result v15

    move-object/from16 v0, p0

    move-wide/from16 v9, p3

    move-wide/from16 v11, p5

    invoke-direct/range {v0 .. v15}, Lz2/e;-><init>(Lz2/e$d;Lz2/e$f;JJJJJJI)V

    return-void
.end method
