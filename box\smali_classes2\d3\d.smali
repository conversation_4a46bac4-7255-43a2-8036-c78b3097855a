.class public final Ld3/d;
.super Ljava/lang/Object;

# interfaces
.implements Lz2/s;


# static fields
.field public static final o:Lz2/y;


# instance fields
.field public final a:[B

.field public final b:Le2/c0;

.field public final c:Z

.field public final d:Lz2/z$a;

.field public e:Lz2/u;

.field public f:Lz2/r0;

.field public g:I

.field public h:Landroidx/media3/common/Metadata;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public i:Lz2/c0;

.field public j:I

.field public k:I

.field public l:Ld3/b;

.field public m:I

.field public n:J


# direct methods
.method static constructor <clinit>()V
    .locals 1

    new-instance v0, Ld3/c;

    invoke-direct {v0}, Ld3/c;-><init>()V

    sput-object v0, Ld3/d;->o:Lz2/y;

    return-void
.end method

.method public constructor <init>()V
    .locals 1

    const/4 v0, 0x0

    invoke-direct {p0, v0}, Ld3/d;-><init>(I)V

    return-void
.end method

.method public constructor <init>(I)V
    .locals 3

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/16 v0, 0x2a

    new-array v0, v0, [B

    iput-object v0, p0, Ld3/d;->a:[B

    new-instance v0, Le2/c0;

    const v1, 0x8000

    new-array v1, v1, [B

    const/4 v2, 0x0

    invoke-direct {v0, v1, v2}, Le2/c0;-><init>([BI)V

    iput-object v0, p0, Ld3/d;->b:Le2/c0;

    const/4 v0, 0x1

    and-int/2addr p1, v0

    if-eqz p1, :cond_0

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    iput-boolean v0, p0, Ld3/d;->c:Z

    new-instance p1, Lz2/z$a;

    invoke-direct {p1}, Lz2/z$a;-><init>()V

    iput-object p1, p0, Ld3/d;->d:Lz2/z$a;

    iput v2, p0, Ld3/d;->g:I

    return-void
.end method

.method public static synthetic a()[Lz2/s;
    .locals 1

    invoke-static {}, Ld3/d;->j()[Lz2/s;

    move-result-object v0

    return-object v0
.end method

.method private static synthetic j()[Lz2/s;
    .locals 3

    const/4 v0, 0x1

    new-array v0, v0, [Lz2/s;

    new-instance v1, Ld3/d;

    invoke-direct {v1}, Ld3/d;-><init>()V

    const/4 v2, 0x0

    aput-object v1, v0, v2

    return-object v0
.end method


# virtual methods
.method public synthetic b()Lz2/s;
    .locals 1

    invoke-static {p0}, Lz2/r;->a(Lz2/s;)Lz2/s;

    move-result-object v0

    return-object v0
.end method

.method public c(Lz2/u;)V
    .locals 2

    iput-object p1, p0, Ld3/d;->e:Lz2/u;

    const/4 v0, 0x0

    const/4 v1, 0x1

    invoke-interface {p1, v0, v1}, Lz2/u;->track(II)Lz2/r0;

    move-result-object v0

    iput-object v0, p0, Ld3/d;->f:Lz2/r0;

    invoke-interface {p1}, Lz2/u;->endTracks()V

    return-void
.end method

.method public d(Lz2/t;Lz2/l0;)I
    .locals 3
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget v0, p0, Ld3/d;->g:I

    const/4 v1, 0x0

    if-eqz v0, :cond_5

    const/4 v2, 0x1

    if-eq v0, v2, :cond_4

    const/4 v2, 0x2

    if-eq v0, v2, :cond_3

    const/4 v2, 0x3

    if-eq v0, v2, :cond_2

    const/4 v2, 0x4

    if-eq v0, v2, :cond_1

    const/4 v1, 0x5

    if-ne v0, v1, :cond_0

    invoke-virtual {p0, p1, p2}, Ld3/d;->l(Lz2/t;Lz2/l0;)I

    move-result p1

    return p1

    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    invoke-direct {p1}, Ljava/lang/IllegalStateException;-><init>()V

    throw p1

    :cond_1
    invoke-virtual {p0, p1}, Ld3/d;->g(Lz2/t;)V

    return v1

    :cond_2
    invoke-virtual {p0, p1}, Ld3/d;->n(Lz2/t;)V

    return v1

    :cond_3
    invoke-virtual {p0, p1}, Ld3/d;->o(Lz2/t;)V

    return v1

    :cond_4
    invoke-virtual {p0, p1}, Ld3/d;->i(Lz2/t;)V

    return v1

    :cond_5
    invoke-virtual {p0, p1}, Ld3/d;->m(Lz2/t;)V

    return v1
.end method

.method public e(Lz2/t;)Z
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    const/4 v0, 0x0

    invoke-static {p1, v0}, Lz2/a0;->c(Lz2/t;Z)Landroidx/media3/common/Metadata;

    invoke-static {p1}, Lz2/a0;->a(Lz2/t;)Z

    move-result p1

    return p1
.end method

.method public final f(Le2/c0;Z)J
    .locals 4

    iget-object v0, p0, Ld3/d;->i:Lz2/c0;

    invoke-static {v0}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    invoke-virtual {p1}, Le2/c0;->f()I

    move-result v0

    :goto_0
    invoke-virtual {p1}, Le2/c0;->g()I

    move-result v1

    add-int/lit8 v1, v1, -0x10

    if-gt v0, v1, :cond_1

    invoke-virtual {p1, v0}, Le2/c0;->U(I)V

    iget-object v1, p0, Ld3/d;->i:Lz2/c0;

    iget v2, p0, Ld3/d;->k:I

    iget-object v3, p0, Ld3/d;->d:Lz2/z$a;

    invoke-static {p1, v1, v2, v3}, Lz2/z;->d(Le2/c0;Lz2/c0;ILz2/z$a;)Z

    move-result v1

    if-eqz v1, :cond_0

    invoke-virtual {p1, v0}, Le2/c0;->U(I)V

    iget-object p1, p0, Ld3/d;->d:Lz2/z$a;

    iget-wide p1, p1, Lz2/z$a;->a:J

    return-wide p1

    :cond_0
    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :cond_1
    if-eqz p2, :cond_5

    :goto_1
    invoke-virtual {p1}, Le2/c0;->g()I

    move-result p2

    iget v1, p0, Ld3/d;->j:I

    sub-int/2addr p2, v1

    if-gt v0, p2, :cond_4

    invoke-virtual {p1, v0}, Le2/c0;->U(I)V

    :try_start_0
    iget-object p2, p0, Ld3/d;->i:Lz2/c0;

    iget v1, p0, Ld3/d;->k:I

    iget-object v2, p0, Ld3/d;->d:Lz2/z$a;

    invoke-static {p1, p2, v1, v2}, Lz2/z;->d(Le2/c0;Lz2/c0;ILz2/z$a;)Z

    move-result p2
    :try_end_0
    .catch Ljava/lang/IndexOutOfBoundsException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_2

    :catch_0
    const/4 p2, 0x0

    :goto_2
    invoke-virtual {p1}, Le2/c0;->f()I

    move-result v1

    invoke-virtual {p1}, Le2/c0;->g()I

    move-result v2

    if-le v1, v2, :cond_2

    goto :goto_3

    :cond_2
    if-eqz p2, :cond_3

    invoke-virtual {p1, v0}, Le2/c0;->U(I)V

    iget-object p1, p0, Ld3/d;->d:Lz2/z$a;

    iget-wide p1, p1, Lz2/z$a;->a:J

    return-wide p1

    :cond_3
    :goto_3
    add-int/lit8 v0, v0, 0x1

    goto :goto_1

    :cond_4
    invoke-virtual {p1}, Le2/c0;->g()I

    move-result p2

    invoke-virtual {p1, p2}, Le2/c0;->U(I)V

    goto :goto_4

    :cond_5
    invoke-virtual {p1, v0}, Le2/c0;->U(I)V

    :goto_4
    const-wide/16 p1, -0x1

    return-wide p1
.end method

.method public final g(Lz2/t;)V
    .locals 5
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    invoke-static {p1}, Lz2/a0;->b(Lz2/t;)I

    move-result v0

    iput v0, p0, Ld3/d;->k:I

    iget-object v0, p0, Ld3/d;->e:Lz2/u;

    invoke-static {v0}, Le2/u0;->i(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lz2/u;

    invoke-interface {p1}, Lz2/t;->getPosition()J

    move-result-wide v1

    invoke-interface {p1}, Lz2/t;->getLength()J

    move-result-wide v3

    invoke-virtual {p0, v1, v2, v3, v4}, Ld3/d;->h(JJ)Lz2/m0;

    move-result-object p1

    invoke-interface {v0, p1}, Lz2/u;->g(Lz2/m0;)V

    const/4 p1, 0x5

    iput p1, p0, Ld3/d;->g:I

    return-void
.end method

.method public final h(JJ)Lz2/m0;
    .locals 8

    iget-object v0, p0, Ld3/d;->i:Lz2/c0;

    invoke-static {v0}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    iget-object v2, p0, Ld3/d;->i:Lz2/c0;

    iget-object v0, v2, Lz2/c0;->k:Lz2/c0$a;

    if-eqz v0, :cond_0

    new-instance p3, Lz2/b0;

    invoke-direct {p3, v2, p1, p2}, Lz2/b0;-><init>(Lz2/c0;J)V

    return-object p3

    :cond_0
    const-wide/16 v0, -0x1

    cmp-long v3, p3, v0

    if-eqz v3, :cond_1

    iget-wide v0, v2, Lz2/c0;->j:J

    const-wide/16 v3, 0x0

    cmp-long v5, v0, v3

    if-lez v5, :cond_1

    new-instance v0, Ld3/b;

    iget v3, p0, Ld3/d;->k:I

    move-object v1, v0

    move-wide v4, p1

    move-wide v6, p3

    invoke-direct/range {v1 .. v7}, Ld3/b;-><init>(Lz2/c0;IJJ)V

    iput-object v0, p0, Ld3/d;->l:Ld3/b;

    invoke-virtual {v0}, Lz2/e;->b()Lz2/m0;

    move-result-object p1

    return-object p1

    :cond_1
    new-instance p1, Lz2/m0$b;

    invoke-virtual {v2}, Lz2/c0;->f()J

    move-result-wide p2

    invoke-direct {p1, p2, p3}, Lz2/m0$b;-><init>(J)V

    return-object p1
.end method

.method public final i(Lz2/t;)V
    .locals 3
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget-object v0, p0, Ld3/d;->a:[B

    const/4 v1, 0x0

    array-length v2, v0

    invoke-interface {p1, v0, v1, v2}, Lz2/t;->peekFully([BII)V

    invoke-interface {p1}, Lz2/t;->resetPeekPosition()V

    const/4 p1, 0x2

    iput p1, p0, Ld3/d;->g:I

    return-void
.end method

.method public final k()V
    .locals 11

    iget-wide v0, p0, Ld3/d;->n:J

    const-wide/32 v2, 0xf4240

    mul-long v0, v0, v2

    iget-object v2, p0, Ld3/d;->i:Lz2/c0;

    invoke-static {v2}, Le2/u0;->i(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lz2/c0;

    iget v2, v2, Lz2/c0;->e:I

    int-to-long v2, v2

    div-long v5, v0, v2

    iget-object v0, p0, Ld3/d;->f:Lz2/r0;

    invoke-static {v0}, Le2/u0;->i(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    move-object v4, v0

    check-cast v4, Lz2/r0;

    const/4 v7, 0x1

    iget v8, p0, Ld3/d;->m:I

    const/4 v9, 0x0

    const/4 v10, 0x0

    invoke-interface/range {v4 .. v10}, Lz2/r0;->e(JIIILz2/r0$a;)V

    return-void
.end method

.method public final l(Lz2/t;Lz2/l0;)I
    .locals 6
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget-object v0, p0, Ld3/d;->f:Lz2/r0;

    invoke-static {v0}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    iget-object v0, p0, Ld3/d;->i:Lz2/c0;

    invoke-static {v0}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    iget-object v0, p0, Ld3/d;->l:Ld3/b;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Lz2/e;->d()Z

    move-result v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Ld3/d;->l:Ld3/b;

    invoke-virtual {v0, p1, p2}, Lz2/e;->c(Lz2/t;Lz2/l0;)I

    move-result p1

    return p1

    :cond_0
    iget-wide v0, p0, Ld3/d;->n:J

    const-wide/16 v2, -0x1

    const/4 p2, 0x0

    cmp-long v4, v0, v2

    if-nez v4, :cond_1

    iget-object v0, p0, Ld3/d;->i:Lz2/c0;

    invoke-static {p1, v0}, Lz2/z;->i(Lz2/t;Lz2/c0;)J

    move-result-wide v0

    iput-wide v0, p0, Ld3/d;->n:J

    return p2

    :cond_1
    iget-object v0, p0, Ld3/d;->b:Le2/c0;

    invoke-virtual {v0}, Le2/c0;->g()I

    move-result v0

    const v1, 0x8000

    if-ge v0, v1, :cond_4

    iget-object v4, p0, Ld3/d;->b:Le2/c0;

    invoke-virtual {v4}, Le2/c0;->e()[B

    move-result-object v4

    sub-int/2addr v1, v0

    invoke-interface {p1, v4, v0, v1}, Lz2/t;->read([BII)I

    move-result p1

    const/4 v1, -0x1

    if-ne p1, v1, :cond_2

    const/4 v4, 0x1

    goto :goto_0

    :cond_2
    const/4 v4, 0x0

    :goto_0
    if-nez v4, :cond_3

    iget-object v1, p0, Ld3/d;->b:Le2/c0;

    add-int/2addr v0, p1

    invoke-virtual {v1, v0}, Le2/c0;->T(I)V

    goto :goto_1

    :cond_3
    iget-object p1, p0, Ld3/d;->b:Le2/c0;

    invoke-virtual {p1}, Le2/c0;->a()I

    move-result p1

    if-nez p1, :cond_5

    invoke-virtual {p0}, Ld3/d;->k()V

    return v1

    :cond_4
    const/4 v4, 0x0

    :cond_5
    :goto_1
    iget-object p1, p0, Ld3/d;->b:Le2/c0;

    invoke-virtual {p1}, Le2/c0;->f()I

    move-result p1

    iget v0, p0, Ld3/d;->m:I

    iget v1, p0, Ld3/d;->j:I

    if-ge v0, v1, :cond_6

    iget-object v5, p0, Ld3/d;->b:Le2/c0;

    sub-int/2addr v1, v0

    invoke-virtual {v5}, Le2/c0;->a()I

    move-result v0

    invoke-static {v1, v0}, Ljava/lang/Math;->min(II)I

    move-result v0

    invoke-virtual {v5, v0}, Le2/c0;->V(I)V

    :cond_6
    iget-object v0, p0, Ld3/d;->b:Le2/c0;

    invoke-virtual {p0, v0, v4}, Ld3/d;->f(Le2/c0;Z)J

    move-result-wide v0

    iget-object v4, p0, Ld3/d;->b:Le2/c0;

    invoke-virtual {v4}, Le2/c0;->f()I

    move-result v4

    sub-int/2addr v4, p1

    iget-object v5, p0, Ld3/d;->b:Le2/c0;

    invoke-virtual {v5, p1}, Le2/c0;->U(I)V

    iget-object p1, p0, Ld3/d;->f:Lz2/r0;

    iget-object v5, p0, Ld3/d;->b:Le2/c0;

    invoke-interface {p1, v5, v4}, Lz2/r0;->f(Le2/c0;I)V

    iget p1, p0, Ld3/d;->m:I

    add-int/2addr p1, v4

    iput p1, p0, Ld3/d;->m:I

    cmp-long p1, v0, v2

    if-eqz p1, :cond_7

    invoke-virtual {p0}, Ld3/d;->k()V

    iput p2, p0, Ld3/d;->m:I

    iput-wide v0, p0, Ld3/d;->n:J

    :cond_7
    iget-object p1, p0, Ld3/d;->b:Le2/c0;

    invoke-virtual {p1}, Le2/c0;->a()I

    move-result p1

    const/16 v0, 0x10

    if-ge p1, v0, :cond_8

    iget-object p1, p0, Ld3/d;->b:Le2/c0;

    invoke-virtual {p1}, Le2/c0;->a()I

    move-result p1

    iget-object v0, p0, Ld3/d;->b:Le2/c0;

    invoke-virtual {v0}, Le2/c0;->e()[B

    move-result-object v0

    iget-object v1, p0, Ld3/d;->b:Le2/c0;

    invoke-virtual {v1}, Le2/c0;->f()I

    move-result v1

    iget-object v2, p0, Ld3/d;->b:Le2/c0;

    invoke-virtual {v2}, Le2/c0;->e()[B

    move-result-object v2

    invoke-static {v0, v1, v2, p2, p1}, Ljava/lang/System;->arraycopy(Ljava/lang/Object;ILjava/lang/Object;II)V

    iget-object v0, p0, Ld3/d;->b:Le2/c0;

    invoke-virtual {v0, p2}, Le2/c0;->U(I)V

    iget-object v0, p0, Ld3/d;->b:Le2/c0;

    invoke-virtual {v0, p1}, Le2/c0;->T(I)V

    :cond_8
    return p2
.end method

.method public final m(Lz2/t;)V
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget-boolean v0, p0, Ld3/d;->c:Z

    const/4 v1, 0x1

    xor-int/2addr v0, v1

    invoke-static {p1, v0}, Lz2/a0;->d(Lz2/t;Z)Landroidx/media3/common/Metadata;

    move-result-object p1

    iput-object p1, p0, Ld3/d;->h:Landroidx/media3/common/Metadata;

    iput v1, p0, Ld3/d;->g:I

    return-void
.end method

.method public final n(Lz2/t;)V
    .locals 3
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    new-instance v0, Lz2/a0$a;

    iget-object v1, p0, Ld3/d;->i:Lz2/c0;

    invoke-direct {v0, v1}, Lz2/a0$a;-><init>(Lz2/c0;)V

    const/4 v1, 0x0

    :goto_0
    if-nez v1, :cond_0

    invoke-static {p1, v0}, Lz2/a0;->e(Lz2/t;Lz2/a0$a;)Z

    move-result v1

    iget-object v2, v0, Lz2/a0$a;->a:Lz2/c0;

    invoke-static {v2}, Le2/u0;->i(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lz2/c0;

    iput-object v2, p0, Ld3/d;->i:Lz2/c0;

    goto :goto_0

    :cond_0
    iget-object p1, p0, Ld3/d;->i:Lz2/c0;

    invoke-static {p1}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    iget-object p1, p0, Ld3/d;->i:Lz2/c0;

    iget p1, p1, Lz2/c0;->c:I

    const/4 v0, 0x6

    invoke-static {p1, v0}, Ljava/lang/Math;->max(II)I

    move-result p1

    iput p1, p0, Ld3/d;->j:I

    iget-object p1, p0, Ld3/d;->f:Lz2/r0;

    invoke-static {p1}, Le2/u0;->i(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lz2/r0;

    iget-object v0, p0, Ld3/d;->i:Lz2/c0;

    iget-object v1, p0, Ld3/d;->a:[B

    iget-object v2, p0, Ld3/d;->h:Landroidx/media3/common/Metadata;

    invoke-virtual {v0, v1, v2}, Lz2/c0;->g([BLandroidx/media3/common/Metadata;)Landroidx/media3/common/y;

    move-result-object v0

    invoke-interface {p1, v0}, Lz2/r0;->a(Landroidx/media3/common/y;)V

    const/4 p1, 0x4

    iput p1, p0, Ld3/d;->g:I

    return-void
.end method

.method public final o(Lz2/t;)V
    .locals 0
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    invoke-static {p1}, Lz2/a0;->i(Lz2/t;)V

    const/4 p1, 0x3

    iput p1, p0, Ld3/d;->g:I

    return-void
.end method

.method public release()V
    .locals 0

    return-void
.end method

.method public seek(JJ)V
    .locals 4

    const/4 v0, 0x0

    const-wide/16 v1, 0x0

    cmp-long v3, p1, v1

    if-nez v3, :cond_0

    iput v0, p0, Ld3/d;->g:I

    goto :goto_0

    :cond_0
    iget-object p1, p0, Ld3/d;->l:Ld3/b;

    if-eqz p1, :cond_1

    invoke-virtual {p1, p3, p4}, Lz2/e;->h(J)V

    :cond_1
    :goto_0
    cmp-long p1, p3, v1

    if-nez p1, :cond_2

    goto :goto_1

    :cond_2
    const-wide/16 v1, -0x1

    :goto_1
    iput-wide v1, p0, Ld3/d;->n:J

    iput v0, p0, Ld3/d;->m:I

    iget-object p1, p0, Ld3/d;->b:Le2/c0;

    invoke-virtual {p1, v0}, Le2/c0;->Q(I)V

    return-void
.end method
