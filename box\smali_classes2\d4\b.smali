.class public final Ld4/b;
.super Ljava/lang/Object;

# interfaces
.implements Lz2/s;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Ld4/b$b;,
        Ld4/b$a;,
        Ld4/b$c;
    }
.end annotation


# static fields
.field public static final h:Lz2/y;


# instance fields
.field public a:Lz2/u;

.field public b:Lz2/r0;

.field public c:I

.field public d:J

.field public e:Ld4/b$b;

.field public f:I

.field public g:J


# direct methods
.method static constructor <clinit>()V
    .locals 1

    new-instance v0, Ld4/a;

    invoke-direct {v0}, Ld4/a;-><init>()V

    sput-object v0, Ld4/b;->h:Lz2/y;

    return-void
.end method

.method public constructor <init>()V
    .locals 3

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, 0x0

    iput v0, p0, Ld4/b;->c:I

    const-wide/16 v0, -0x1

    iput-wide v0, p0, Ld4/b;->d:J

    const/4 v2, -0x1

    iput v2, p0, Ld4/b;->f:I

    iput-wide v0, p0, Ld4/b;->g:J

    return-void
.end method

.method public static synthetic a()[Lz2/s;
    .locals 1

    invoke-static {}, Ld4/b;->g()[Lz2/s;

    move-result-object v0

    return-object v0
.end method

.method private f()V
    .locals 1

    iget-object v0, p0, Ld4/b;->b:Lz2/r0;

    invoke-static {v0}, Le2/a;->i(Ljava/lang/Object;)Ljava/lang/Object;

    iget-object v0, p0, Ld4/b;->a:Lz2/u;

    invoke-static {v0}, Le2/u0;->i(Ljava/lang/Object;)Ljava/lang/Object;

    return-void
.end method

.method private static synthetic g()[Lz2/s;
    .locals 3

    const/4 v0, 0x1

    new-array v0, v0, [Lz2/s;

    new-instance v1, Ld4/b;

    invoke-direct {v1}, Ld4/b;-><init>()V

    const/4 v2, 0x0

    aput-object v1, v0, v2

    return-object v0
.end method


# virtual methods
.method public synthetic b()Lz2/s;
    .locals 1

    invoke-static {p0}, Lz2/r;->a(Lz2/s;)Lz2/s;

    move-result-object v0

    return-object v0
.end method

.method public c(Lz2/u;)V
    .locals 2

    iput-object p1, p0, Ld4/b;->a:Lz2/u;

    const/4 v0, 0x0

    const/4 v1, 0x1

    invoke-interface {p1, v0, v1}, Lz2/u;->track(II)Lz2/r0;

    move-result-object v0

    iput-object v0, p0, Ld4/b;->b:Lz2/r0;

    invoke-interface {p1}, Lz2/u;->endTracks()V

    return-void
.end method

.method public d(Lz2/t;Lz2/l0;)I
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    invoke-direct {p0}, Ld4/b;->f()V

    iget p2, p0, Ld4/b;->c:I

    const/4 v0, 0x0

    if-eqz p2, :cond_4

    const/4 v1, 0x1

    if-eq p2, v1, :cond_3

    const/4 v1, 0x2

    if-eq p2, v1, :cond_2

    const/4 v1, 0x3

    if-eq p2, v1, :cond_1

    const/4 v0, 0x4

    if-ne p2, v0, :cond_0

    invoke-virtual {p0, p1}, Ld4/b;->k(Lz2/t;)I

    move-result p1

    return p1

    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    invoke-direct {p1}, Ljava/lang/IllegalStateException;-><init>()V

    throw p1

    :cond_1
    invoke-virtual {p0, p1}, Ld4/b;->l(Lz2/t;)V

    return v0

    :cond_2
    invoke-virtual {p0, p1}, Ld4/b;->i(Lz2/t;)V

    return v0

    :cond_3
    invoke-virtual {p0, p1}, Ld4/b;->j(Lz2/t;)V

    return v0

    :cond_4
    invoke-virtual {p0, p1}, Ld4/b;->h(Lz2/t;)V

    return v0
.end method

.method public e(Lz2/t;)Z
    .locals 0
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    invoke-static {p1}, Ld4/d;->a(Lz2/t;)Z

    move-result p1

    return p1
.end method

.method public final h(Lz2/t;)V
    .locals 6
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    invoke-interface {p1}, Lz2/t;->getPosition()J

    move-result-wide v0

    const-wide/16 v2, 0x0

    const/4 v4, 0x1

    cmp-long v5, v0, v2

    if-nez v5, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    invoke-static {v0}, Le2/a;->g(Z)V

    iget v0, p0, Ld4/b;->f:I

    const/4 v1, -0x1

    if-eq v0, v1, :cond_1

    invoke-interface {p1, v0}, Lz2/t;->skipFully(I)V

    const/4 p1, 0x4

    iput p1, p0, Ld4/b;->c:I

    return-void

    :cond_1
    invoke-static {p1}, Ld4/d;->a(Lz2/t;)Z

    move-result v0

    if-eqz v0, :cond_2

    invoke-interface {p1}, Lz2/t;->getPeekPosition()J

    move-result-wide v0

    invoke-interface {p1}, Lz2/t;->getPosition()J

    move-result-wide v2

    sub-long/2addr v0, v2

    long-to-int v1, v0

    invoke-interface {p1, v1}, Lz2/t;->skipFully(I)V

    iput v4, p0, Ld4/b;->c:I

    return-void

    :cond_2
    const-string p1, "Unsupported or unrecognized wav file type."

    const/4 v0, 0x0

    invoke-static {p1, v0}, Landroidx/media3/common/ParserException;->createForMalformedContainer(Ljava/lang/String;Ljava/lang/Throwable;)Landroidx/media3/common/ParserException;

    move-result-object p1

    throw p1
.end method

.method public final i(Lz2/t;)V
    .locals 6
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    invoke-static {p1}, Ld4/d;->b(Lz2/t;)Ld4/c;

    move-result-object v3

    iget p1, v3, Ld4/c;->a:I

    const/16 v0, 0x11

    if-ne p1, v0, :cond_0

    new-instance p1, Ld4/b$a;

    iget-object v0, p0, Ld4/b;->a:Lz2/u;

    iget-object v1, p0, Ld4/b;->b:Lz2/r0;

    invoke-direct {p1, v0, v1, v3}, Ld4/b$a;-><init>(Lz2/u;Lz2/r0;Ld4/c;)V

    iput-object p1, p0, Ld4/b;->e:Ld4/b$b;

    goto :goto_0

    :cond_0
    const/4 v0, 0x6

    if-ne p1, v0, :cond_1

    new-instance p1, Ld4/b$c;

    iget-object v1, p0, Ld4/b;->a:Lz2/u;

    iget-object v2, p0, Ld4/b;->b:Lz2/r0;

    const-string v4, "audio/g711-alaw"

    const/4 v5, -0x1

    move-object v0, p1

    invoke-direct/range {v0 .. v5}, Ld4/b$c;-><init>(Lz2/u;Lz2/r0;Ld4/c;Ljava/lang/String;I)V

    iput-object p1, p0, Ld4/b;->e:Ld4/b$b;

    goto :goto_0

    :cond_1
    const/4 v0, 0x7

    if-ne p1, v0, :cond_2

    new-instance p1, Ld4/b$c;

    iget-object v1, p0, Ld4/b;->a:Lz2/u;

    iget-object v2, p0, Ld4/b;->b:Lz2/r0;

    const-string v4, "audio/g711-mlaw"

    const/4 v5, -0x1

    move-object v0, p1

    invoke-direct/range {v0 .. v5}, Ld4/b$c;-><init>(Lz2/u;Lz2/r0;Ld4/c;Ljava/lang/String;I)V

    iput-object p1, p0, Ld4/b;->e:Ld4/b$b;

    goto :goto_0

    :cond_2
    iget v0, v3, Ld4/c;->f:I

    invoke-static {p1, v0}, Lz2/v0;->a(II)I

    move-result v5

    if-eqz v5, :cond_3

    new-instance p1, Ld4/b$c;

    iget-object v1, p0, Ld4/b;->a:Lz2/u;

    iget-object v2, p0, Ld4/b;->b:Lz2/r0;

    const-string v4, "audio/raw"

    move-object v0, p1

    invoke-direct/range {v0 .. v5}, Ld4/b$c;-><init>(Lz2/u;Lz2/r0;Ld4/c;Ljava/lang/String;I)V

    iput-object p1, p0, Ld4/b;->e:Ld4/b$b;

    :goto_0
    const/4 p1, 0x3

    iput p1, p0, Ld4/b;->c:I

    return-void

    :cond_3
    new-instance p1, Ljava/lang/StringBuilder;

    invoke-direct {p1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v0, "Unsupported WAV format type: "

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget v0, v3, Ld4/c;->a:I

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-static {p1}, Landroidx/media3/common/ParserException;->createForUnsupportedContainerFeature(Ljava/lang/String;)Landroidx/media3/common/ParserException;

    move-result-object p1

    throw p1
.end method

.method public final j(Lz2/t;)V
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    invoke-static {p1}, Ld4/d;->c(Lz2/t;)J

    move-result-wide v0

    iput-wide v0, p0, Ld4/b;->d:J

    const/4 p1, 0x2

    iput p1, p0, Ld4/b;->c:I

    return-void
.end method

.method public final k(Lz2/t;)I
    .locals 6
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget-wide v0, p0, Ld4/b;->g:J

    const-wide/16 v2, -0x1

    const/4 v4, 0x0

    cmp-long v5, v0, v2

    if-eqz v5, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    invoke-static {v0}, Le2/a;->g(Z)V

    iget-wide v0, p0, Ld4/b;->g:J

    invoke-interface {p1}, Lz2/t;->getPosition()J

    move-result-wide v2

    sub-long/2addr v0, v2

    iget-object v2, p0, Ld4/b;->e:Ld4/b$b;

    invoke-static {v2}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Ld4/b$b;

    invoke-interface {v2, p1, v0, v1}, Ld4/b$b;->c(Lz2/t;J)Z

    move-result p1

    if-eqz p1, :cond_1

    const/4 v4, -0x1

    :cond_1
    return v4
.end method

.method public final l(Lz2/t;)V
    .locals 9
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    invoke-static {p1}, Ld4/d;->e(Lz2/t;)Landroid/util/Pair;

    move-result-object v0

    iget-object v1, v0, Landroid/util/Pair;->first:Ljava/lang/Object;

    check-cast v1, Ljava/lang/Long;

    invoke-virtual {v1}, Ljava/lang/Long;->intValue()I

    move-result v1

    iput v1, p0, Ld4/b;->f:I

    iget-object v0, v0, Landroid/util/Pair;->second:Ljava/lang/Object;

    check-cast v0, Ljava/lang/Long;

    invoke-virtual {v0}, Ljava/lang/Long;->longValue()J

    move-result-wide v0

    iget-wide v2, p0, Ld4/b;->d:J

    const-wide/16 v4, -0x1

    cmp-long v6, v2, v4

    if-eqz v6, :cond_0

    const-wide v6, 0xffffffffL

    cmp-long v8, v0, v6

    if-nez v8, :cond_0

    move-wide v0, v2

    :cond_0
    iget v2, p0, Ld4/b;->f:I

    int-to-long v2, v2

    add-long/2addr v2, v0

    iput-wide v2, p0, Ld4/b;->g:J

    invoke-interface {p1}, Lz2/t;->getLength()J

    move-result-wide v0

    cmp-long p1, v0, v4

    if-eqz p1, :cond_1

    iget-wide v2, p0, Ld4/b;->g:J

    cmp-long p1, v2, v0

    if-lez p1, :cond_1

    new-instance p1, Ljava/lang/StringBuilder;

    invoke-direct {p1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "Data exceeds input length: "

    invoke-virtual {p1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-wide v2, p0, Ld4/b;->g:J

    invoke-virtual {p1, v2, v3}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    const-string v2, ", "

    invoke-virtual {p1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p1, v0, v1}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    const-string v2, "WavExtractor"

    invoke-static {v2, p1}, Le2/o;->h(Ljava/lang/String;Ljava/lang/String;)V

    iput-wide v0, p0, Ld4/b;->g:J

    :cond_1
    iget-object p1, p0, Ld4/b;->e:Ld4/b$b;

    invoke-static {p1}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ld4/b$b;

    iget v0, p0, Ld4/b;->f:I

    iget-wide v1, p0, Ld4/b;->g:J

    invoke-interface {p1, v0, v1, v2}, Ld4/b$b;->a(IJ)V

    const/4 p1, 0x4

    iput p1, p0, Ld4/b;->c:I

    return-void
.end method

.method public release()V
    .locals 0

    return-void
.end method

.method public seek(JJ)V
    .locals 3

    const-wide/16 v0, 0x0

    cmp-long v2, p1, v0

    if-nez v2, :cond_0

    const/4 p1, 0x0

    goto :goto_0

    :cond_0
    const/4 p1, 0x4

    :goto_0
    iput p1, p0, Ld4/b;->c:I

    iget-object p1, p0, Ld4/b;->e:Ld4/b$b;

    if-eqz p1, :cond_1

    invoke-interface {p1, p3, p4}, Ld4/b$b;->b(J)V

    :cond_1
    return-void
.end method
