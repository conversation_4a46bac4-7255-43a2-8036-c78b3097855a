.class public Ld5/e;
.super Ld5/g;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ld5/g<",
        "Lh5/d;",
        ">;"
    }
.end annotation


# instance fields
.field public final i:Lh5/d;


# direct methods
.method public constructor <init>(Ljava/util/List;)V
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lm5/a<",
            "Lh5/d;",
            ">;>;)V"
        }
    .end annotation

    invoke-direct {p0, p1}, Ld5/g;-><init>(Ljava/util/List;)V

    const/4 v0, 0x0

    const/4 v1, 0x0

    :goto_0
    invoke-interface {p1}, Ljava/util/List;->size()I

    move-result v2

    if-ge v0, v2, :cond_1

    invoke-interface {p1, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lm5/a;

    iget-object v2, v2, Lm5/a;->b:Ljava/lang/Object;

    check-cast v2, Lh5/d;

    if-eqz v2, :cond_0

    invoke-virtual {v2}, Lh5/d;->f()I

    move-result v2

    invoke-static {v1, v2}, Ljava/lang/Math;->max(II)I

    move-result v1

    :cond_0
    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :cond_1
    new-instance p1, Lh5/d;

    new-array v0, v1, [F

    new-array v1, v1, [I

    invoke-direct {p1, v0, v1}, Lh5/d;-><init>([F[I)V

    iput-object p1, p0, Ld5/e;->i:Lh5/d;

    return-void
.end method


# virtual methods
.method public bridge synthetic i(Lm5/a;F)Ljava/lang/Object;
    .locals 0

    invoke-virtual {p0, p1, p2}, Ld5/e;->q(Lm5/a;F)Lh5/d;

    move-result-object p1

    return-object p1
.end method

.method public q(Lm5/a;F)Lh5/d;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lm5/a<",
            "Lh5/d;",
            ">;F)",
            "Lh5/d;"
        }
    .end annotation

    iget-object v0, p0, Ld5/e;->i:Lh5/d;

    iget-object v1, p1, Lm5/a;->b:Ljava/lang/Object;

    check-cast v1, Lh5/d;

    iget-object p1, p1, Lm5/a;->c:Ljava/lang/Object;

    check-cast p1, Lh5/d;

    invoke-virtual {v0, v1, p1, p2}, Lh5/d;->g(Lh5/d;Lh5/d;F)V

    iget-object p1, p0, Ld5/e;->i:Lh5/d;

    return-object p1
.end method
