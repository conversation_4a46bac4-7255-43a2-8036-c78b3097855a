.class public abstract Ld5/g;
.super Ld5/a;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "<T:",
        "Ljava/lang/Object;",
        ">",
        "Ld5/a<",
        "TT;TT;>;"
    }
.end annotation


# direct methods
.method public constructor <init>(Ljava/util/List;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "+",
            "Lm5/a<",
            "TT;>;>;)V"
        }
    .end annotation

    invoke-direct {p0, p1}, Ld5/a;-><init>(Ljava/util/List;)V

    return-void
.end method
