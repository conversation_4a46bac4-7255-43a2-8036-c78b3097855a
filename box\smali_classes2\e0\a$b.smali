.class public final Le0/a$b;
.super Ljava/lang/Object;

# interfaces
.implements Le0/d;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Le0/a;-><init>()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation


# instance fields
.field public final a:Le0/j;

.field public b:Landroidx/compose/ui/graphics/layer/GraphicsLayer;

.field public final synthetic c:Le0/a;


# direct methods
.method public constructor <init>(Le0/a;)V
    .locals 0

    iput-object p1, p0, Le0/a$b;->c:Le0/a;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    invoke-static {p0}, Le0/b;->a(Le0/d;)Le0/j;

    move-result-object p1

    iput-object p1, p0, Le0/a$b;->a:Le0/j;

    return-void
.end method


# virtual methods
.method public a()J
    .locals 2

    iget-object v0, p0, Le0/a$b;->c:Le0/a;

    invoke-virtual {v0}, Le0/a;->E()Le0/a$a;

    move-result-object v0

    invoke-virtual {v0}, Le0/a$a;->h()J

    move-result-wide v0

    return-wide v0
.end method

.method public b(Lv0/e;)V
    .locals 1

    iget-object v0, p0, Le0/a$b;->c:Le0/a;

    invoke-virtual {v0}, Le0/a;->E()Le0/a$a;

    move-result-object v0

    invoke-virtual {v0, p1}, Le0/a$a;->j(Lv0/e;)V

    return-void
.end method

.method public c(Landroidx/compose/ui/unit/LayoutDirection;)V
    .locals 1

    iget-object v0, p0, Le0/a$b;->c:Le0/a;

    invoke-virtual {v0}, Le0/a;->E()Le0/a$a;

    move-result-object v0

    invoke-virtual {v0, p1}, Le0/a$a;->k(Landroidx/compose/ui/unit/LayoutDirection;)V

    return-void
.end method

.method public d()Le0/j;
    .locals 1

    iget-object v0, p0, Le0/a$b;->a:Le0/j;

    return-object v0
.end method

.method public e(Landroidx/compose/ui/graphics/layer/GraphicsLayer;)V
    .locals 0

    iput-object p1, p0, Le0/a$b;->b:Landroidx/compose/ui/graphics/layer/GraphicsLayer;

    return-void
.end method

.method public f()Landroidx/compose/ui/graphics/o1;
    .locals 1

    iget-object v0, p0, Le0/a$b;->c:Le0/a;

    invoke-virtual {v0}, Le0/a;->E()Le0/a$a;

    move-result-object v0

    invoke-virtual {v0}, Le0/a$a;->e()Landroidx/compose/ui/graphics/o1;

    move-result-object v0

    return-object v0
.end method

.method public g(J)V
    .locals 1

    iget-object v0, p0, Le0/a$b;->c:Le0/a;

    invoke-virtual {v0}, Le0/a;->E()Le0/a$a;

    move-result-object v0

    invoke-virtual {v0, p1, p2}, Le0/a$a;->l(J)V

    return-void
.end method

.method public getDensity()Lv0/e;
    .locals 1

    iget-object v0, p0, Le0/a$b;->c:Le0/a;

    invoke-virtual {v0}, Le0/a;->E()Le0/a$a;

    move-result-object v0

    invoke-virtual {v0}, Le0/a$a;->f()Lv0/e;

    move-result-object v0

    return-object v0
.end method

.method public getLayoutDirection()Landroidx/compose/ui/unit/LayoutDirection;
    .locals 1

    iget-object v0, p0, Le0/a$b;->c:Le0/a;

    invoke-virtual {v0}, Le0/a;->E()Le0/a$a;

    move-result-object v0

    invoke-virtual {v0}, Le0/a$a;->g()Landroidx/compose/ui/unit/LayoutDirection;

    move-result-object v0

    return-object v0
.end method

.method public h()Landroidx/compose/ui/graphics/layer/GraphicsLayer;
    .locals 1

    iget-object v0, p0, Le0/a$b;->b:Landroidx/compose/ui/graphics/layer/GraphicsLayer;

    return-object v0
.end method

.method public i(Landroidx/compose/ui/graphics/o1;)V
    .locals 1

    iget-object v0, p0, Le0/a$b;->c:Le0/a;

    invoke-virtual {v0}, Le0/a;->E()Le0/a$a;

    move-result-object v0

    invoke-virtual {v0, p1}, Le0/a$a;->i(Landroidx/compose/ui/graphics/o1;)V

    return-void
.end method
