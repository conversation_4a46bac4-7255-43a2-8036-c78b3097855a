.class public final Le0/b;
.super L<PERSON>va/lang/Object;


# annotations
.annotation runtime L<PERSON><PERSON>/<PERSON>adata;
.end annotation


# direct methods
.method public static final synthetic a(Le0/d;)Le0/j;
    .locals 0

    invoke-static {p0}, Le0/b;->b(Le0/d;)Le0/j;

    move-result-object p0

    return-object p0
.end method

.method public static final b(Le0/d;)Le0/j;
    .locals 1

    new-instance v0, Le0/b$a;

    invoke-direct {v0, p0}, Le0/b$a;-><init>(Le0/d;)V

    return-object v0
.end method
