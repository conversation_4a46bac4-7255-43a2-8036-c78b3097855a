.class public interface abstract Le0/d;
.super Ljava/lang/Object;


# annotations
.annotation runtime Lkotlin/Metadata;
.end annotation


# virtual methods
.method public abstract a()J
.end method

.method public abstract b(Lv0/e;)V
.end method

.method public abstract c(Landroidx/compose/ui/unit/LayoutDirection;)V
.end method

.method public abstract d()Le0/j;
.end method

.method public abstract e(Landroidx/compose/ui/graphics/layer/GraphicsLayer;)V
.end method

.method public abstract f()Landroidx/compose/ui/graphics/o1;
.end method

.method public abstract g(J)V
.end method

.method public abstract getDensity()Lv0/e;
.end method

.method public abstract getLayoutDirection()Landroidx/compose/ui/unit/LayoutDirection;
.end method

.method public abstract h()Landroidx/compose/ui/graphics/layer/GraphicsLayer;
.end method

.method public abstract i(Landroidx/compose/ui/graphics/o1;)V
.end method
