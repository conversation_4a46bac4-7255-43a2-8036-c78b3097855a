.class public final Le0/g$a;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Le0/g;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation


# static fields
.field public static final synthetic a:Le0/g$a;

.field public static final b:I

.field public static final c:I


# direct methods
.method static constructor <clinit>()V
    .locals 1

    new-instance v0, Le0/g$a;

    invoke-direct {v0}, Le0/g$a;-><init>()V

    sput-object v0, Le0/g$a;->a:Le0/g$a;

    sget-object v0, Landroidx/compose/ui/graphics/e1;->a:Landroidx/compose/ui/graphics/e1$a;

    invoke-virtual {v0}, Landroidx/compose/ui/graphics/e1$a;->B()I

    move-result v0

    sput v0, Le0/g$a;->b:I

    sget-object v0, Landroidx/compose/ui/graphics/z3;->a:Landroidx/compose/ui/graphics/z3$a;

    invoke-virtual {v0}, Landroidx/compose/ui/graphics/z3$a;->a()I

    move-result v0

    sput v0, Le0/g$a;->c:I

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final a()I
    .locals 1

    sget v0, Le0/g$a;->b:I

    return v0
.end method

.method public final b()I
    .locals 1

    sget v0, Le0/g$a;->c:I

    return v0
.end method
