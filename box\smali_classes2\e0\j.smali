.class public interface abstract Le0/j;
.super Ljava/lang/Object;


# annotations
.annotation runtime Lkotlin/Metadata;
.end annotation


# virtual methods
.method public abstract a(FFFFI)V
.end method

.method public abstract b(Landroidx/compose/ui/graphics/Path;I)V
.end method

.method public abstract c(FF)V
.end method

.method public abstract d([F)V
.end method

.method public abstract e(FFJ)V
.end method

.method public abstract f(FFFF)V
.end method
