.class public final Le0/l;
.super Le0/h;


# annotations
.annotation runtime L<PERSON>lin/Metadata;
.end annotation


# static fields
.field public static final a:Le0/l;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    new-instance v0, Le0/l;

    invoke-direct {v0}, Le0/l;-><init>()V

    sput-object v0, Le0/l;->a:Le0/l;

    return-void
.end method

.method public constructor <init>()V
    .locals 1

    const/4 v0, 0x0

    invoke-direct {p0, v0}, Le0/h;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method
