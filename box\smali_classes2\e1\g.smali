.class public Le1/g;
.super Ljava/lang/Object;


# instance fields
.field public a:Ld1/a;


# direct methods
.method public constructor <init>(Ld1/a;)V
    .locals 0
    .param p1    # Ld1/a;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Le1/g;->a:Ld1/a;

    return-void
.end method
