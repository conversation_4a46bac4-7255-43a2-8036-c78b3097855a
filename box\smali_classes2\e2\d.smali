.class public interface abstract Le2/d;
.super Ljava/lang/Object;


# static fields
.field public static final a:Le2/d;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    new-instance v0, Le2/f0;

    invoke-direct {v0}, Le2/f0;-><init>()V

    sput-object v0, Le2/d;->a:Le2/d;

    return-void
.end method


# virtual methods
.method public abstract a()J
.end method

.method public abstract b()J
.end method

.method public abstract c()V
.end method

.method public abstract createHandler(Landroid/os/Looper;Landroid/os/Handler$Callback;)Le2/j;
    .param p2    # Landroid/os/Handler$Callback;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
.end method

.method public abstract elapsedRealtime()J
.end method

.method public abstract uptimeMillis()J
.end method
