.class public final synthetic Le2/l;
.super Ljava/lang/Object;

# interfaces
.implements Landroid/os/Handler$Callback;


# instance fields
.field public final synthetic a:Le2/n;


# direct methods
.method public synthetic constructor <init>(Le2/n;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Le2/l;->a:Le2/n;

    return-void
.end method


# virtual methods
.method public final handleMessage(Landroid/os/Message;)Z
    .locals 1

    iget-object v0, p0, Le2/l;->a:Le2/n;

    invoke-static {v0, p1}, Le2/n;->b(Le2/n;Landroid/os/Message;)Z

    move-result p1

    return p1
.end method
