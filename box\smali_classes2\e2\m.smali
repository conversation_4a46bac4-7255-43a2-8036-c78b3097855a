.class public final synthetic Le2/m;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/lang/Runnable;


# instance fields
.field public final synthetic a:Ljava/util/concurrent/CopyOnWriteArraySet;

.field public final synthetic b:I

.field public final synthetic c:Le2/n$a;


# direct methods
.method public synthetic constructor <init>(Ljava/util/concurrent/CopyOnWriteArraySet;ILe2/n$a;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Le2/m;->a:Ljava/util/concurrent/CopyOnWriteArraySet;

    iput p2, p0, Le2/m;->b:I

    iput-object p3, p0, Le2/m;->c:Le2/n$a;

    return-void
.end method


# virtual methods
.method public final run()V
    .locals 3

    iget-object v0, p0, Le2/m;->a:Ljava/util/concurrent/CopyOnWriteArraySet;

    iget v1, p0, Le2/m;->b:I

    iget-object v2, p0, Le2/m;->c:Le2/n$a;

    invoke-static {v0, v1, v2}, Le2/n;->a(Ljava/util/concurrent/CopyOnWriteArraySet;ILe2/n$a;)V

    return-void
.end method
