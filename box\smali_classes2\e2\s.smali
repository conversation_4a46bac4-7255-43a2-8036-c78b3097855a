.class public final synthetic Le2/s;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/lang/Runnable;


# instance fields
.field public final synthetic a:Le2/t;

.field public final synthetic b:Le2/t$c;


# direct methods
.method public synthetic constructor <init>(Le2/t;Le2/t$c;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Le2/s;->a:Le2/t;

    iput-object p2, p0, Le2/s;->b:Le2/t$c;

    return-void
.end method


# virtual methods
.method public final run()V
    .locals 2

    iget-object v0, p0, Le2/s;->a:Le2/t;

    iget-object v1, p0, Le2/s;->b:Le2/t$c;

    invoke-static {v0, v1}, Le2/t;->a(Le2/t;Le2/t$c;)V

    return-void
.end method
