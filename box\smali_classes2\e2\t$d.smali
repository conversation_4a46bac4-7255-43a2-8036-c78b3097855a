.class public final Le2/t$d;
.super Landroid/content/BroadcastReceiver;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Le2/t;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x11
    name = "d"
.end annotation


# instance fields
.field public final synthetic a:Le2/t;


# direct methods
.method public constructor <init>(Le2/t;)V
    .locals 0

    iput-object p1, p0, Le2/t$d;->a:Le2/t;

    invoke-direct {p0}, Landroid/content/BroadcastReceiver;-><init>()V

    return-void
.end method

.method public synthetic constructor <init>(Le2/t;Le2/t$a;)V
    .locals 0

    invoke-direct {p0, p1}, Le2/t$d;-><init>(Le2/t;)V

    return-void
.end method


# virtual methods
.method public onReceive(Landroid/content/Context;Landroid/content/Intent;)V
    .locals 2

    invoke-static {p1}, Le2/t;->b(Landroid/content/Context;)I

    move-result p2

    sget v0, Le2/u0;->a:I

    const/16 v1, 0x1f

    if-lt v0, v1, :cond_0

    const/4 v0, 0x5

    if-ne p2, v0, :cond_0

    iget-object p2, p0, Le2/t$d;->a:Le2/t;

    invoke-static {p1, p2}, Le2/t$b;->a(Landroid/content/Context;Le2/t;)V

    goto :goto_0

    :cond_0
    iget-object p1, p0, Le2/t$d;->a:Le2/t;

    invoke-static {p1, p2}, Le2/t;->c(Le2/t;I)V

    :goto_0
    return-void
.end method
