.class public final Lf2/a$a;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lf2/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation


# instance fields
.field public final a:I

.field public final b:Z

.field public final c:I

.field public final d:I

.field public final e:I

.field public final f:I

.field public final g:I

.field public final h:[I

.field public final i:I

.field public final j:I

.field public final k:I

.field public final l:I

.field public final m:F

.field public final n:I

.field public final o:I

.field public final p:I


# direct methods
.method public constructor <init>(IZIIIII[IIIIIFIII)V
    .locals 2

    move-object v0, p0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    move v1, p1

    iput v1, v0, Lf2/a$a;->a:I

    move v1, p2

    iput-boolean v1, v0, Lf2/a$a;->b:Z

    move v1, p3

    iput v1, v0, Lf2/a$a;->c:I

    move v1, p4

    iput v1, v0, Lf2/a$a;->d:I

    move v1, p5

    iput v1, v0, Lf2/a$a;->e:I

    move v1, p6

    iput v1, v0, Lf2/a$a;->f:I

    move v1, p7

    iput v1, v0, Lf2/a$a;->g:I

    move-object v1, p8

    iput-object v1, v0, Lf2/a$a;->h:[I

    move v1, p9

    iput v1, v0, Lf2/a$a;->i:I

    move v1, p10

    iput v1, v0, Lf2/a$a;->j:I

    move v1, p11

    iput v1, v0, Lf2/a$a;->k:I

    move v1, p12

    iput v1, v0, Lf2/a$a;->l:I

    move v1, p13

    iput v1, v0, Lf2/a$a;->m:F

    move/from16 v1, p14

    iput v1, v0, Lf2/a$a;->n:I

    move/from16 v1, p15

    iput v1, v0, Lf2/a$a;->o:I

    move/from16 v1, p16

    iput v1, v0, Lf2/a$a;->p:I

    return-void
.end method
