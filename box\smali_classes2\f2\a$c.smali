.class public final Lf2/a$c;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lf2/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "c"
.end annotation


# instance fields
.field public final a:I

.field public final b:I

.field public final c:I

.field public final d:I

.field public final e:I

.field public final f:I

.field public final g:I

.field public final h:F

.field public final i:I

.field public final j:I

.field public final k:Z

.field public final l:Z

.field public final m:I

.field public final n:I

.field public final o:I

.field public final p:Z

.field public final q:I

.field public final r:I

.field public final s:I


# direct methods
.method public constructor <init>(IIIIIIIFIIZZIIIZIII)V
    .locals 2

    move-object v0, p0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    move v1, p1

    iput v1, v0, Lf2/a$c;->a:I

    move v1, p2

    iput v1, v0, Lf2/a$c;->b:I

    move v1, p3

    iput v1, v0, Lf2/a$c;->c:I

    move v1, p4

    iput v1, v0, Lf2/a$c;->d:I

    move v1, p5

    iput v1, v0, Lf2/a$c;->e:I

    move v1, p6

    iput v1, v0, Lf2/a$c;->f:I

    move v1, p7

    iput v1, v0, Lf2/a$c;->g:I

    move v1, p8

    iput v1, v0, Lf2/a$c;->h:F

    move v1, p9

    iput v1, v0, Lf2/a$c;->i:I

    move v1, p10

    iput v1, v0, Lf2/a$c;->j:I

    move v1, p11

    iput-boolean v1, v0, Lf2/a$c;->k:Z

    move v1, p12

    iput-boolean v1, v0, Lf2/a$c;->l:Z

    move v1, p13

    iput v1, v0, Lf2/a$c;->m:I

    move/from16 v1, p14

    iput v1, v0, Lf2/a$c;->n:I

    move/from16 v1, p15

    iput v1, v0, Lf2/a$c;->o:I

    move/from16 v1, p16

    iput-boolean v1, v0, Lf2/a$c;->p:Z

    move/from16 v1, p17

    iput v1, v0, Lf2/a$c;->q:I

    move/from16 v1, p18

    iput v1, v0, Lf2/a$c;->r:I

    move/from16 v1, p19

    iput v1, v0, Lf2/a$c;->s:I

    return-void
.end method
