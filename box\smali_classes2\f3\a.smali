.class public final Lf3/a;
.super Ljava/lang/Object;

# interfaces
.implements Lz2/s;


# instance fields
.field public final a:Le2/c0;

.field public final b:Lz2/o0;


# direct methods
.method public constructor <init>()V
    .locals 3

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    new-instance v0, Le2/c0;

    const/4 v1, 0x4

    invoke-direct {v0, v1}, Le2/c0;-><init>(I)V

    iput-object v0, p0, Lf3/a;->a:Le2/c0;

    new-instance v0, Lz2/o0;

    const/4 v1, -0x1

    const-string v2, "image/heif"

    invoke-direct {v0, v1, v1, v2}, Lz2/o0;-><init>(IILjava/lang/String;)V

    iput-object v0, p0, Lf3/a;->b:Lz2/o0;

    return-void
.end method


# virtual methods
.method public final a(Lz2/t;I)Z
    .locals 4
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget-object v0, p0, Lf3/a;->a:Le2/c0;

    const/4 v1, 0x4

    invoke-virtual {v0, v1}, Le2/c0;->Q(I)V

    iget-object v0, p0, Lf3/a;->a:Le2/c0;

    invoke-virtual {v0}, Le2/c0;->e()[B

    move-result-object v0

    const/4 v2, 0x0

    invoke-interface {p1, v0, v2, v1}, Lz2/t;->peekFully([BII)V

    iget-object p1, p0, Lf3/a;->a:Le2/c0;

    invoke-virtual {p1}, Le2/c0;->J()J

    move-result-wide v0

    int-to-long p1, p2

    cmp-long v3, v0, p1

    if-nez v3, :cond_0

    const/4 v2, 0x1

    :cond_0
    return v2
.end method

.method public synthetic b()Lz2/s;
    .locals 1

    invoke-static {p0}, Lz2/r;->a(Lz2/s;)Lz2/s;

    move-result-object v0

    return-object v0
.end method

.method public c(Lz2/u;)V
    .locals 1

    iget-object v0, p0, Lf3/a;->b:Lz2/o0;

    invoke-virtual {v0, p1}, Lz2/o0;->c(Lz2/u;)V

    return-void
.end method

.method public d(Lz2/t;Lz2/l0;)I
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget-object v0, p0, Lf3/a;->b:Lz2/o0;

    invoke-virtual {v0, p1, p2}, Lz2/o0;->d(Lz2/t;Lz2/l0;)I

    move-result p1

    return p1
.end method

.method public e(Lz2/t;)Z
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    const/4 v0, 0x4

    invoke-interface {p1, v0}, Lz2/t;->advancePeekPosition(I)V

    const v0, 0x66747970

    invoke-virtual {p0, p1, v0}, Lf3/a;->a(Lz2/t;I)Z

    move-result v0

    if-eqz v0, :cond_0

    const v0, 0x68656963

    invoke-virtual {p0, p1, v0}, Lf3/a;->a(Lz2/t;I)Z

    move-result p1

    if-eqz p1, :cond_0

    const/4 p1, 0x1

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    :goto_0
    return p1
.end method

.method public release()V
    .locals 0

    return-void
.end method

.method public seek(JJ)V
    .locals 1

    iget-object v0, p0, Lf3/a;->b:Lz2/o0;

    invoke-virtual {v0, p1, p2, p3, p4}, Lz2/o0;->seek(JJ)V

    return-void
.end method
