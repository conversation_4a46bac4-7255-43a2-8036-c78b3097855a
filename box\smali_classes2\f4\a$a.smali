.class public final Lf4/a$a;
.super Ljava/lang/Object;


# annotations
.annotation build Landroidx/annotation/RequiresApi;
    value = 0x1e
.end annotation

.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lf4/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation


# static fields
.field public static final a:Lf4/a$a;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    new-instance v0, Lf4/a$a;

    invoke-direct {v0}, Lf4/a$a;-><init>()V

    sput-object v0, Lf4/a$a;->a:Lf4/a$a;

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final a()I
    .locals 1

    const v0, 0xf4240

    invoke-static {v0}, Lh/e;->a(I)I

    move-result v0

    return v0
.end method
