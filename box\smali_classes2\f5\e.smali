.class public interface abstract Lf5/e;
.super Ljava/lang/Object;


# virtual methods
.method public abstract c(Ljava/lang/Object;Lm5/c;)V
    .param p2    # Lm5/c;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">(TT;",
            "Lm5/c<",
            "TT;>;)V"
        }
    .end annotation
.end method

.method public abstract h(Lf5/d;ILjava/util/List;Lf5/d;)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lf5/d;",
            "I",
            "Ljava/util/List<",
            "Lf5/d;",
            ">;",
            "Lf5/d;",
            ")V"
        }
    .end annotation
.end method
