.class public Lg1/a;
.super Ljava/lang/Object;


# instance fields
.field public final a:F

.field public final b:F

.field public final c:F

.field public final d:F

.field public final e:F

.field public final f:F

.field public final g:F

.field public final h:F

.field public final i:F


# direct methods
.method public constructor <init>(FFFFFFFFF)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput p1, p0, Lg1/a;->a:F

    iput p2, p0, Lg1/a;->b:F

    iput p3, p0, Lg1/a;->c:F

    iput p4, p0, Lg1/a;->d:F

    iput p5, p0, Lg1/a;->e:F

    iput p6, p0, Lg1/a;->f:F

    iput p7, p0, Lg1/a;->g:F

    iput p8, p0, Lg1/a;->h:F

    iput p9, p0, Lg1/a;->i:F

    return-void
.end method

.method public static b(FFF)Lg1/a;
    .locals 12
    .annotation build Landroidx/annotation/Nullable;
    .end annotation

    const/4 v0, 0x0

    const/high16 v1, 0x42c80000    # 100.0f

    const/high16 v2, 0x447a0000    # 1000.0f

    const/4 v3, 0x0

    move-object v4, v3

    const/high16 v3, 0x447a0000    # 1000.0f

    const/4 v5, 0x0

    :goto_0
    sub-float v6, v5, v1

    invoke-static {v6}, Ljava/lang/Math;->abs(F)F

    move-result v6

    const v7, 0x3c23d70a    # 0.01f

    cmpl-float v6, v6, v7

    if-lez v6, :cond_3

    sub-float v6, v1, v5

    const/high16 v7, 0x40000000    # 2.0f

    div-float/2addr v6, v7

    add-float/2addr v6, v5

    invoke-static {v6, p1, p0}, Lg1/a;->e(FFF)Lg1/a;

    move-result-object v7

    invoke-virtual {v7}, Lg1/a;->p()I

    move-result v7

    invoke-static {v7}, Lg1/b;->b(I)F

    move-result v8

    sub-float v9, p2, v8

    invoke-static {v9}, Ljava/lang/Math;->abs(F)F

    move-result v9

    const v10, 0x3e4ccccd    # 0.2f

    cmpg-float v10, v9, v10

    if-gez v10, :cond_0

    invoke-static {v7}, Lg1/a;->c(I)Lg1/a;

    move-result-object v7

    invoke-virtual {v7}, Lg1/a;->k()F

    move-result v10

    invoke-virtual {v7}, Lg1/a;->i()F

    move-result v11

    invoke-static {v10, v11, p0}, Lg1/a;->e(FFF)Lg1/a;

    move-result-object v10

    invoke-virtual {v7, v10}, Lg1/a;->a(Lg1/a;)F

    move-result v10

    const/high16 v11, 0x3f800000    # 1.0f

    cmpg-float v11, v10, v11

    if-gtz v11, :cond_0

    move-object v4, v7

    move v2, v9

    move v3, v10

    :cond_0
    cmpl-float v7, v2, v0

    if-nez v7, :cond_1

    cmpl-float v7, v3, v0

    if-nez v7, :cond_1

    goto :goto_1

    :cond_1
    cmpg-float v7, v8, p2

    if-gez v7, :cond_2

    move v5, v6

    goto :goto_0

    :cond_2
    move v1, v6

    goto :goto_0

    :cond_3
    :goto_1
    return-object v4
.end method

.method public static c(I)Lg1/a;
    .locals 14
    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    const/4 v0, 0x7

    new-array v0, v0, [F

    const/4 v1, 0x3

    new-array v2, v1, [F

    sget-object v3, Lg1/l;->k:Lg1/l;

    invoke-static {p0, v3, v0, v2}, Lg1/a;->d(ILg1/l;[F[F)V

    new-instance p0, Lg1/a;

    const/4 v3, 0x0

    aget v5, v2, v3

    const/4 v4, 0x1

    aget v6, v2, v4

    aget v7, v0, v3

    aget v8, v0, v4

    const/4 v2, 0x2

    aget v9, v0, v2

    aget v10, v0, v1

    const/4 v1, 0x4

    aget v11, v0, v1

    const/4 v1, 0x5

    aget v12, v0, v1

    const/4 v1, 0x6

    aget v13, v0, v1

    move-object v4, p0

    invoke-direct/range {v4 .. v13}, Lg1/a;-><init>(FFFFFFFFF)V

    return-object p0
.end method

.method public static d(ILg1/l;[F[F)V
    .locals 19
    .param p1    # Lg1/l;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p2    # [F
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .param p3    # [F
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param

    move/from16 v0, p0

    move-object/from16 v1, p3

    invoke-static {v0, v1}, Lg1/b;->f(I[F)V

    sget-object v0, Lg1/b;->a:[[F

    const/4 v2, 0x0

    aget v3, v1, v2

    aget-object v4, v0, v2

    aget v5, v4, v2

    mul-float v5, v5, v3

    const/4 v6, 0x1

    aget v7, v1, v6

    aget v8, v4, v6

    mul-float v8, v8, v7

    add-float/2addr v5, v8

    const/4 v8, 0x2

    aget v9, v1, v8

    aget v4, v4, v8

    mul-float v4, v4, v9

    add-float/2addr v5, v4

    aget-object v4, v0, v6

    aget v10, v4, v2

    mul-float v10, v10, v3

    aget v11, v4, v6

    mul-float v11, v11, v7

    add-float/2addr v10, v11

    aget v4, v4, v8

    mul-float v4, v4, v9

    add-float/2addr v10, v4

    aget-object v0, v0, v8

    aget v4, v0, v2

    mul-float v3, v3, v4

    aget v4, v0, v6

    mul-float v7, v7, v4

    add-float/2addr v3, v7

    aget v0, v0, v8

    mul-float v9, v9, v0

    add-float/2addr v3, v9

    invoke-virtual/range {p1 .. p1}, Lg1/l;->i()[F

    move-result-object v0

    aget v0, v0, v2

    mul-float v0, v0, v5

    invoke-virtual/range {p1 .. p1}, Lg1/l;->i()[F

    move-result-object v4

    aget v4, v4, v6

    mul-float v4, v4, v10

    invoke-virtual/range {p1 .. p1}, Lg1/l;->i()[F

    move-result-object v5

    aget v5, v5, v8

    mul-float v5, v5, v3

    invoke-virtual/range {p1 .. p1}, Lg1/l;->c()F

    move-result v3

    invoke-static {v0}, Ljava/lang/Math;->abs(F)F

    move-result v7

    mul-float v3, v3, v7

    float-to-double v9, v3

    const-wide/high16 v11, 0x4059000000000000L    # 100.0

    div-double/2addr v9, v11

    const-wide v13, 0x3fdae147ae147ae1L    # 0.42

    invoke-static {v9, v10, v13, v14}, Ljava/lang/Math;->pow(DD)D

    move-result-wide v9

    double-to-float v3, v9

    invoke-virtual/range {p1 .. p1}, Lg1/l;->c()F

    move-result v7

    invoke-static {v4}, Ljava/lang/Math;->abs(F)F

    move-result v9

    mul-float v7, v7, v9

    float-to-double v9, v7

    div-double/2addr v9, v11

    invoke-static {v9, v10, v13, v14}, Ljava/lang/Math;->pow(DD)D

    move-result-wide v9

    double-to-float v7, v9

    invoke-virtual/range {p1 .. p1}, Lg1/l;->c()F

    move-result v9

    invoke-static {v5}, Ljava/lang/Math;->abs(F)F

    move-result v10

    mul-float v9, v9, v10

    float-to-double v9, v9

    div-double/2addr v9, v11

    invoke-static {v9, v10, v13, v14}, Ljava/lang/Math;->pow(DD)D

    move-result-wide v9

    double-to-float v9, v9

    invoke-static {v0}, Ljava/lang/Math;->signum(F)F

    move-result v0

    const/high16 v10, 0x43c80000    # 400.0f

    mul-float v0, v0, v10

    mul-float v0, v0, v3

    const v13, 0x41d90a3d    # 27.13f

    add-float/2addr v3, v13

    div-float/2addr v0, v3

    invoke-static {v4}, Ljava/lang/Math;->signum(F)F

    move-result v3

    mul-float v3, v3, v10

    mul-float v3, v3, v7

    add-float/2addr v7, v13

    div-float/2addr v3, v7

    invoke-static {v5}, Ljava/lang/Math;->signum(F)F

    move-result v4

    mul-float v4, v4, v10

    mul-float v4, v4, v9

    add-float/2addr v9, v13

    div-float/2addr v4, v9

    const-wide/high16 v9, 0x4026000000000000L    # 11.0

    float-to-double v13, v0

    mul-double v13, v13, v9

    const-wide/high16 v9, -0x3fd8000000000000L    # -12.0

    float-to-double v6, v3

    mul-double v6, v6, v9

    add-double/2addr v13, v6

    float-to-double v5, v4

    add-double/2addr v13, v5

    double-to-float v7, v13

    const/high16 v9, 0x41300000    # 11.0f

    div-float/2addr v7, v9

    add-float v9, v0, v3

    float-to-double v9, v9

    const-wide/high16 v13, 0x4000000000000000L    # 2.0

    mul-double v5, v5, v13

    sub-double/2addr v9, v5

    double-to-float v5, v9

    const/high16 v6, 0x41100000    # 9.0f

    div-float/2addr v5, v6

    const/high16 v6, 0x41a00000    # 20.0f

    mul-float v9, v0, v6

    mul-float v3, v3, v6

    add-float/2addr v9, v3

    const/high16 v10, 0x41a80000    # 21.0f

    mul-float v10, v10, v4

    add-float/2addr v9, v10

    div-float/2addr v9, v6

    const/high16 v10, 0x42200000    # 40.0f

    mul-float v0, v0, v10

    add-float/2addr v0, v3

    add-float/2addr v0, v4

    div-float/2addr v0, v6

    float-to-double v3, v5

    float-to-double v11, v7

    invoke-static {v3, v4, v11, v12}, Ljava/lang/Math;->atan2(DD)D

    move-result-wide v3

    double-to-float v3, v3

    const/high16 v4, 0x43340000    # 180.0f

    mul-float v3, v3, v4

    const v6, 0x40490fdb    # (float)Math.PI

    div-float/2addr v3, v6

    const/4 v10, 0x0

    const/high16 v11, 0x43b40000    # 360.0f

    cmpg-float v10, v3, v10

    if-gez v10, :cond_0

    add-float/2addr v3, v11

    goto :goto_0

    :cond_0
    cmpl-float v10, v3, v11

    if-ltz v10, :cond_1

    sub-float/2addr v3, v11

    :cond_1
    :goto_0
    mul-float v6, v6, v3

    div-float/2addr v6, v4

    invoke-virtual/range {p1 .. p1}, Lg1/l;->f()F

    move-result v4

    mul-float v0, v0, v4

    invoke-virtual/range {p1 .. p1}, Lg1/l;->a()F

    move-result v4

    div-float/2addr v0, v4

    move v10, v9

    float-to-double v8, v0

    invoke-virtual/range {p1 .. p1}, Lg1/l;->b()F

    move-result v0

    invoke-virtual/range {p1 .. p1}, Lg1/l;->j()F

    move-result v12

    mul-float v0, v0, v12

    move v12, v5

    float-to-double v4, v0

    invoke-static {v8, v9, v4, v5}, Ljava/lang/Math;->pow(DD)D

    move-result-wide v4

    double-to-float v0, v4

    const/high16 v4, 0x42c80000    # 100.0f

    mul-float v0, v0, v4

    invoke-virtual/range {p1 .. p1}, Lg1/l;->b()F

    move-result v5

    const/high16 v8, 0x40800000    # 4.0f

    div-float v5, v8, v5

    div-float v4, v0, v4

    float-to-double v13, v4

    invoke-static {v13, v14}, Ljava/lang/Math;->sqrt(D)D

    move-result-wide v13

    double-to-float v4, v13

    mul-float v5, v5, v4

    invoke-virtual/range {p1 .. p1}, Lg1/l;->a()F

    move-result v4

    add-float/2addr v4, v8

    mul-float v5, v5, v4

    invoke-virtual/range {p1 .. p1}, Lg1/l;->d()F

    move-result v4

    mul-float v5, v5, v4

    float-to-double v13, v3

    const-wide v17, 0x403423d70a3d70a4L    # 20.14

    cmpg-double v4, v13, v17

    if-gez v4, :cond_2

    add-float/2addr v11, v3

    goto :goto_1

    :cond_2
    move v11, v3

    :goto_1
    float-to-double v13, v11

    const-wide v17, 0x400921fb54442d18L    # Math.PI

    mul-double v13, v13, v17

    const-wide v17, 0x4066800000000000L    # 180.0

    div-double v13, v13, v17

    const-wide/high16 v15, 0x4000000000000000L    # 2.0

    add-double/2addr v13, v15

    invoke-static {v13, v14}, Ljava/lang/Math;->cos(D)D

    move-result-wide v13

    const-wide v15, 0x400e666666666666L    # 3.8

    add-double/2addr v13, v15

    double-to-float v4, v13

    const/high16 v9, 0x3e800000    # 0.25f

    mul-float v4, v4, v9

    const v9, 0x45706276

    mul-float v4, v4, v9

    invoke-virtual/range {p1 .. p1}, Lg1/l;->g()F

    move-result v9

    mul-float v4, v4, v9

    invoke-virtual/range {p1 .. p1}, Lg1/l;->h()F

    move-result v9

    mul-float v4, v4, v9

    mul-float v7, v7, v7

    mul-float v9, v12, v12

    add-float/2addr v7, v9

    float-to-double v11, v7

    invoke-static {v11, v12}, Ljava/lang/Math;->sqrt(D)D

    move-result-wide v11

    double-to-float v7, v11

    mul-float v4, v4, v7

    const v7, 0x3e9c28f6    # 0.305f

    add-float v9, v10, v7

    div-float/2addr v4, v9

    invoke-virtual/range {p1 .. p1}, Lg1/l;->e()F

    move-result v7

    float-to-double v9, v7

    const-wide v11, 0x3fd28f5c28f5c28fL    # 0.29

    invoke-static {v11, v12, v9, v10}, Ljava/lang/Math;->pow(DD)D

    move-result-wide v9

    const-wide v11, 0x3ffa3d70a3d70a3dL    # 1.64

    sub-double/2addr v11, v9

    const-wide v9, 0x3fe75c28f5c28f5cL    # 0.73

    invoke-static {v11, v12, v9, v10}, Ljava/lang/Math;->pow(DD)D

    move-result-wide v9

    double-to-float v7, v9

    float-to-double v9, v4

    const-wide v11, 0x3feccccccccccccdL    # 0.9

    invoke-static {v9, v10, v11, v12}, Ljava/lang/Math;->pow(DD)D

    move-result-wide v9

    double-to-float v4, v9

    mul-float v7, v7, v4

    float-to-double v9, v0

    const-wide/high16 v11, 0x4059000000000000L    # 100.0

    div-double/2addr v9, v11

    invoke-static {v9, v10}, Ljava/lang/Math;->sqrt(D)D

    move-result-wide v9

    double-to-float v4, v9

    mul-float v4, v4, v7

    invoke-virtual/range {p1 .. p1}, Lg1/l;->d()F

    move-result v9

    mul-float v9, v9, v4

    invoke-virtual/range {p1 .. p1}, Lg1/l;->b()F

    move-result v10

    mul-float v7, v7, v10

    invoke-virtual/range {p1 .. p1}, Lg1/l;->a()F

    move-result v10

    add-float/2addr v10, v8

    div-float/2addr v7, v10

    float-to-double v7, v7

    invoke-static {v7, v8}, Ljava/lang/Math;->sqrt(D)D

    move-result-wide v7

    double-to-float v7, v7

    const/high16 v8, 0x42480000    # 50.0f

    mul-float v7, v7, v8

    const v8, 0x3fd9999a    # 1.7f

    mul-float v8, v8, v0

    const v10, 0x3be56042    # 0.007f

    mul-float v10, v10, v0

    const/high16 v11, 0x3f800000    # 1.0f

    add-float/2addr v10, v11

    div-float/2addr v8, v10

    const v10, 0x3cbac711    # 0.0228f

    mul-float v10, v10, v9

    add-float/2addr v10, v11

    float-to-double v10, v10

    invoke-static {v10, v11}, Ljava/lang/Math;->log(D)D

    move-result-wide v10

    double-to-float v10, v10

    const v11, 0x422f7048

    mul-float v10, v10, v11

    float-to-double v11, v6

    invoke-static {v11, v12}, Ljava/lang/Math;->cos(D)D

    move-result-wide v13

    double-to-float v6, v13

    mul-float v6, v6, v10

    invoke-static {v11, v12}, Ljava/lang/Math;->sin(D)D

    move-result-wide v11

    double-to-float v11, v11

    mul-float v10, v10, v11

    aput v3, v1, v2

    const/4 v3, 0x1

    aput v4, v1, v3

    if-eqz p2, :cond_3

    aput v0, p2, v2

    aput v5, p2, v3

    const/4 v0, 0x2

    aput v9, p2, v0

    const/4 v0, 0x3

    aput v7, p2, v0

    const/4 v0, 0x4

    aput v8, p2, v0

    const/4 v0, 0x5

    aput v6, p2, v0

    const/4 v0, 0x6

    aput v10, p2, v0

    :cond_3
    return-void
.end method

.method public static e(FFF)Lg1/a;
    .locals 1
    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    sget-object v0, Lg1/l;->k:Lg1/l;

    invoke-static {p0, p1, p2, v0}, Lg1/a;->f(FFFLg1/l;)Lg1/a;

    move-result-object p0

    return-object p0
.end method

.method public static f(FFFLg1/l;)Lg1/a;
    .locals 13
    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    move v3, p0

    invoke-virtual/range {p3 .. p3}, Lg1/l;->b()F

    move-result v0

    const/high16 v1, 0x40800000    # 4.0f

    div-float v0, v1, v0

    float-to-double v4, v3

    const-wide/high16 v6, 0x4059000000000000L    # 100.0

    div-double/2addr v4, v6

    invoke-static {v4, v5}, Ljava/lang/Math;->sqrt(D)D

    move-result-wide v6

    double-to-float v2, v6

    mul-float v0, v0, v2

    invoke-virtual/range {p3 .. p3}, Lg1/l;->a()F

    move-result v2

    add-float/2addr v2, v1

    mul-float v0, v0, v2

    invoke-virtual/range {p3 .. p3}, Lg1/l;->d()F

    move-result v2

    mul-float v6, v0, v2

    invoke-virtual/range {p3 .. p3}, Lg1/l;->d()F

    move-result v0

    mul-float v7, p1, v0

    invoke-static {v4, v5}, Ljava/lang/Math;->sqrt(D)D

    move-result-wide v4

    double-to-float v0, v4

    div-float v0, p1, v0

    invoke-virtual/range {p3 .. p3}, Lg1/l;->b()F

    move-result v2

    mul-float v0, v0, v2

    invoke-virtual/range {p3 .. p3}, Lg1/l;->a()F

    move-result v2

    add-float/2addr v2, v1

    div-float/2addr v0, v2

    float-to-double v0, v0

    invoke-static {v0, v1}, Ljava/lang/Math;->sqrt(D)D

    move-result-wide v0

    double-to-float v0, v0

    const/high16 v1, 0x42480000    # 50.0f

    mul-float v8, v0, v1

    const v0, 0x40490fdb    # (float)Math.PI

    mul-float v0, v0, p2

    const/high16 v1, 0x43340000    # 180.0f

    div-float/2addr v0, v1

    const v1, 0x3fd9999a    # 1.7f

    mul-float v1, v1, v3

    const v2, 0x3be56042    # 0.007f

    mul-float v2, v2, v3

    const/high16 v4, 0x3f800000    # 1.0f

    add-float/2addr v2, v4

    div-float v9, v1, v2

    const-wide v1, 0x3f9758e219652bd4L    # 0.0228

    float-to-double v4, v7

    mul-double v4, v4, v1

    const-wide/high16 v1, 0x3ff0000000000000L    # 1.0

    add-double/2addr v4, v1

    invoke-static {v4, v5}, Ljava/lang/Math;->log(D)D

    move-result-wide v1

    double-to-float v1, v1

    const v2, 0x422f7048

    mul-float v1, v1, v2

    float-to-double v4, v0

    invoke-static {v4, v5}, Ljava/lang/Math;->cos(D)D

    move-result-wide v10

    double-to-float v0, v10

    mul-float v10, v1, v0

    invoke-static {v4, v5}, Ljava/lang/Math;->sin(D)D

    move-result-wide v4

    double-to-float v0, v4

    mul-float v11, v1, v0

    new-instance v12, Lg1/a;

    move-object v0, v12

    move v1, p2

    move v2, p1

    move v4, v6

    move v5, v7

    move v6, v8

    move v7, v9

    move v8, v10

    move v9, v11

    invoke-direct/range {v0 .. v9}, Lg1/a;-><init>(FFFFFFFFF)V

    return-object v12
.end method

.method public static m(FFF)I
    .locals 1

    sget-object v0, Lg1/l;->k:Lg1/l;

    invoke-static {p0, p1, p2, v0}, Lg1/a;->n(FFFLg1/l;)I

    move-result p0

    return p0
.end method

.method public static n(FFFLg1/l;)I
    .locals 6
    .param p3    # Lg1/l;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param

    float-to-double v0, p1

    const-wide/high16 v2, 0x3ff0000000000000L    # 1.0

    cmpg-double v4, v0, v2

    if-ltz v4, :cond_7

    invoke-static {p2}, Ljava/lang/Math;->round(F)I

    move-result v0

    int-to-double v0, v0

    const-wide/16 v2, 0x0

    cmpg-double v4, v0, v2

    if-lez v4, :cond_7

    invoke-static {p2}, Ljava/lang/Math;->round(F)I

    move-result v0

    int-to-double v0, v0

    const-wide/high16 v2, 0x4059000000000000L    # 100.0

    cmpl-double v4, v0, v2

    if-ltz v4, :cond_0

    goto :goto_3

    :cond_0
    const/4 v0, 0x0

    cmpg-float v1, p0, v0

    if-gez v1, :cond_1

    const/4 p0, 0x0

    goto :goto_0

    :cond_1
    const/high16 v1, 0x43b40000    # 360.0f

    invoke-static {v1, p0}, Ljava/lang/Math;->min(FF)F

    move-result p0

    :goto_0
    const/4 v1, 0x1

    const/4 v2, 0x0

    move v0, p1

    move-object v3, v2

    const/4 v1, 0x0

    const/4 v2, 0x1

    :goto_1
    sub-float v4, v1, p1

    invoke-static {v4}, Ljava/lang/Math;->abs(F)F

    move-result v4

    const v5, 0x3ecccccd    # 0.4f

    cmpl-float v4, v4, v5

    if-ltz v4, :cond_5

    invoke-static {p0, v0, p2}, Lg1/a;->b(FFF)Lg1/a;

    move-result-object v4

    const/high16 v5, 0x40000000    # 2.0f

    if-eqz v2, :cond_3

    if-eqz v4, :cond_2

    invoke-virtual {v4, p3}, Lg1/a;->o(Lg1/l;)I

    move-result p0

    return p0

    :cond_2
    sub-float v0, p1, v1

    div-float/2addr v0, v5

    add-float/2addr v0, v1

    const/4 v2, 0x0

    goto :goto_1

    :cond_3
    if-nez v4, :cond_4

    move p1, v0

    goto :goto_2

    :cond_4
    move v1, v0

    move-object v3, v4

    :goto_2
    sub-float v0, p1, v1

    div-float/2addr v0, v5

    add-float/2addr v0, v1

    goto :goto_1

    :cond_5
    if-nez v3, :cond_6

    invoke-static {p2}, Lg1/b;->a(F)I

    move-result p0

    return p0

    :cond_6
    invoke-virtual {v3, p3}, Lg1/a;->o(Lg1/l;)I

    move-result p0

    return p0

    :cond_7
    :goto_3
    invoke-static {p2}, Lg1/b;->a(F)I

    move-result p0

    return p0
.end method


# virtual methods
.method public a(Lg1/a;)F
    .locals 4
    .param p1    # Lg1/a;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param

    invoke-virtual {p0}, Lg1/a;->l()F

    move-result v0

    invoke-virtual {p1}, Lg1/a;->l()F

    move-result v1

    sub-float/2addr v0, v1

    invoke-virtual {p0}, Lg1/a;->g()F

    move-result v1

    invoke-virtual {p1}, Lg1/a;->g()F

    move-result v2

    sub-float/2addr v1, v2

    invoke-virtual {p0}, Lg1/a;->h()F

    move-result v2

    invoke-virtual {p1}, Lg1/a;->h()F

    move-result p1

    sub-float/2addr v2, p1

    mul-float v0, v0, v0

    mul-float v1, v1, v1

    add-float/2addr v0, v1

    mul-float v2, v2, v2

    add-float/2addr v0, v2

    float-to-double v0, v0

    invoke-static {v0, v1}, Ljava/lang/Math;->sqrt(D)D

    move-result-wide v0

    const-wide v2, 0x3fe428f5c28f5c29L    # 0.63

    invoke-static {v0, v1, v2, v3}, Ljava/lang/Math;->pow(DD)D

    move-result-wide v0

    const-wide v2, 0x3ff68f5c28f5c28fL    # 1.41

    mul-double v0, v0, v2

    double-to-float p1, v0

    return p1
.end method

.method public g()F
    .locals 1

    iget v0, p0, Lg1/a;->h:F

    return v0
.end method

.method public h()F
    .locals 1

    iget v0, p0, Lg1/a;->i:F

    return v0
.end method

.method public i()F
    .locals 1

    iget v0, p0, Lg1/a;->b:F

    return v0
.end method

.method public j()F
    .locals 1

    iget v0, p0, Lg1/a;->a:F

    return v0
.end method

.method public k()F
    .locals 1

    iget v0, p0, Lg1/a;->c:F

    return v0
.end method

.method public l()F
    .locals 1

    iget v0, p0, Lg1/a;->g:F

    return v0
.end method

.method public o(Lg1/l;)I
    .locals 15
    .param p1    # Lg1/l;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param

    invoke-virtual {p0}, Lg1/a;->i()F

    move-result v0

    float-to-double v0, v0

    const-wide/high16 v2, 0x4059000000000000L    # 100.0

    const-wide/16 v4, 0x0

    cmpl-double v6, v0, v4

    if-eqz v6, :cond_1

    invoke-virtual {p0}, Lg1/a;->k()F

    move-result v0

    float-to-double v0, v0

    cmpl-double v6, v0, v4

    if-nez v6, :cond_0

    goto :goto_0

    :cond_0
    invoke-virtual {p0}, Lg1/a;->i()F

    move-result v0

    invoke-virtual {p0}, Lg1/a;->k()F

    move-result v1

    float-to-double v6, v1

    div-double/2addr v6, v2

    invoke-static {v6, v7}, Ljava/lang/Math;->sqrt(D)D

    move-result-wide v6

    double-to-float v1, v6

    div-float/2addr v0, v1

    goto :goto_1

    :cond_1
    :goto_0
    const/4 v0, 0x0

    :goto_1
    float-to-double v0, v0

    invoke-virtual/range {p1 .. p1}, Lg1/l;->e()F

    move-result v6

    float-to-double v6, v6

    const-wide v8, 0x3fd28f5c28f5c28fL    # 0.29

    invoke-static {v8, v9, v6, v7}, Ljava/lang/Math;->pow(DD)D

    move-result-wide v6

    const-wide v8, 0x3ffa3d70a3d70a3dL    # 1.64

    sub-double/2addr v8, v6

    const-wide v6, 0x3fe75c28f5c28f5cL    # 0.73

    invoke-static {v8, v9, v6, v7}, Ljava/lang/Math;->pow(DD)D

    move-result-wide v6

    div-double/2addr v0, v6

    const-wide v6, 0x3ff1c71c71c71c72L    # 1.1111111111111112

    invoke-static {v0, v1, v6, v7}, Ljava/lang/Math;->pow(DD)D

    move-result-wide v0

    double-to-float v0, v0

    invoke-virtual {p0}, Lg1/a;->j()F

    move-result v1

    const v6, 0x40490fdb    # (float)Math.PI

    mul-float v1, v1, v6

    const/high16 v6, 0x43340000    # 180.0f

    div-float/2addr v1, v6

    float-to-double v6, v1

    const-wide/high16 v8, 0x4000000000000000L    # 2.0

    add-double/2addr v8, v6

    invoke-static {v8, v9}, Ljava/lang/Math;->cos(D)D

    move-result-wide v8

    const-wide v10, 0x400e666666666666L    # 3.8

    add-double/2addr v8, v10

    double-to-float v1, v8

    const/high16 v8, 0x3e800000    # 0.25f

    mul-float v1, v1, v8

    invoke-virtual/range {p1 .. p1}, Lg1/l;->a()F

    move-result v8

    invoke-virtual {p0}, Lg1/a;->k()F

    move-result v9

    float-to-double v9, v9

    div-double/2addr v9, v2

    invoke-virtual/range {p1 .. p1}, Lg1/l;->b()F

    move-result v2

    float-to-double v2, v2

    const-wide/high16 v11, 0x3ff0000000000000L    # 1.0

    div-double/2addr v11, v2

    invoke-virtual/range {p1 .. p1}, Lg1/l;->j()F

    move-result v2

    float-to-double v2, v2

    div-double/2addr v11, v2

    invoke-static {v9, v10, v11, v12}, Ljava/lang/Math;->pow(DD)D

    move-result-wide v2

    double-to-float v2, v2

    mul-float v8, v8, v2

    const v2, 0x45706276

    mul-float v1, v1, v2

    invoke-virtual/range {p1 .. p1}, Lg1/l;->g()F

    move-result v2

    mul-float v1, v1, v2

    invoke-virtual/range {p1 .. p1}, Lg1/l;->h()F

    move-result v2

    mul-float v1, v1, v2

    invoke-virtual/range {p1 .. p1}, Lg1/l;->f()F

    move-result v2

    div-float/2addr v8, v2

    invoke-static {v6, v7}, Ljava/lang/Math;->sin(D)D

    move-result-wide v2

    double-to-float v2, v2

    invoke-static {v6, v7}, Ljava/lang/Math;->cos(D)D

    move-result-wide v6

    double-to-float v3, v6

    const v6, 0x3e9c28f6    # 0.305f

    add-float/2addr v6, v8

    const/high16 v7, 0x41b80000    # 23.0f

    mul-float v6, v6, v7

    mul-float v6, v6, v0

    mul-float v1, v1, v7

    const/high16 v7, 0x41300000    # 11.0f

    mul-float v7, v7, v0

    mul-float v7, v7, v3

    add-float/2addr v1, v7

    const/high16 v7, 0x42d80000    # 108.0f

    mul-float v0, v0, v7

    mul-float v0, v0, v2

    add-float/2addr v1, v0

    div-float/2addr v6, v1

    mul-float v3, v3, v6

    mul-float v6, v6, v2

    const/high16 v0, 0x43e60000    # 460.0f

    mul-float v8, v8, v0

    const v0, 0x43e18000    # 451.0f

    mul-float v0, v0, v3

    add-float/2addr v0, v8

    const/high16 v1, 0x43900000    # 288.0f

    mul-float v1, v1, v6

    add-float/2addr v0, v1

    const v1, 0x44af6000    # 1403.0f

    div-float/2addr v0, v1

    const v2, 0x445ec000    # 891.0f

    mul-float v2, v2, v3

    sub-float v2, v8, v2

    const v7, 0x43828000    # 261.0f

    mul-float v7, v7, v6

    sub-float/2addr v2, v7

    div-float/2addr v2, v1

    const/high16 v7, 0x435c0000    # 220.0f

    mul-float v3, v3, v7

    sub-float/2addr v8, v3

    const v3, 0x45c4e000    # 6300.0f

    mul-float v6, v6, v3

    sub-float/2addr v8, v6

    div-float/2addr v8, v1

    invoke-static {v0}, Ljava/lang/Math;->abs(F)F

    move-result v1

    float-to-double v6, v1

    const-wide v9, 0x403b2147ae147ae1L    # 27.13

    mul-double v6, v6, v9

    invoke-static {v0}, Ljava/lang/Math;->abs(F)F

    move-result v1

    float-to-double v11, v1

    const-wide/high16 v13, 0x4079000000000000L    # 400.0

    sub-double v11, v13, v11

    div-double/2addr v6, v11

    invoke-static {v4, v5, v6, v7}, Ljava/lang/Math;->max(DD)D

    move-result-wide v6

    double-to-float v1, v6

    invoke-static {v0}, Ljava/lang/Math;->signum(F)F

    move-result v0

    invoke-virtual/range {p1 .. p1}, Lg1/l;->c()F

    move-result v3

    const/high16 v6, 0x42c80000    # 100.0f

    div-float v3, v6, v3

    mul-float v0, v0, v3

    float-to-double v11, v1

    const-wide v6, 0x40030c30c30c30c3L    # 2.380952380952381

    invoke-static {v11, v12, v6, v7}, Ljava/lang/Math;->pow(DD)D

    move-result-wide v11

    double-to-float v3, v11

    mul-float v0, v0, v3

    invoke-static {v2}, Ljava/lang/Math;->abs(F)F

    move-result v3

    float-to-double v11, v3

    mul-double v11, v11, v9

    invoke-static {v2}, Ljava/lang/Math;->abs(F)F

    move-result v3

    float-to-double v9, v3

    sub-double v9, v13, v9

    div-double/2addr v11, v9

    invoke-static {v4, v5, v11, v12}, Ljava/lang/Math;->max(DD)D

    move-result-wide v9

    double-to-float v3, v9

    invoke-static {v2}, Ljava/lang/Math;->signum(F)F

    move-result v2

    invoke-virtual/range {p1 .. p1}, Lg1/l;->c()F

    move-result v9

    const/high16 v1, 0x42c80000    # 100.0f

    div-float v9, v1, v9

    mul-float v2, v2, v9

    float-to-double v9, v3

    invoke-static {v9, v10, v6, v7}, Ljava/lang/Math;->pow(DD)D

    move-result-wide v9

    double-to-float v3, v9

    mul-float v2, v2, v3

    invoke-static {v8}, Ljava/lang/Math;->abs(F)F

    move-result v3

    float-to-double v9, v3

    const-wide v11, 0x403b2147ae147ae1L    # 27.13

    mul-double v9, v9, v11

    invoke-static {v8}, Ljava/lang/Math;->abs(F)F

    move-result v3

    float-to-double v11, v3

    sub-double/2addr v13, v11

    div-double/2addr v9, v13

    invoke-static {v4, v5, v9, v10}, Ljava/lang/Math;->max(DD)D

    move-result-wide v3

    double-to-float v3, v3

    invoke-static {v8}, Ljava/lang/Math;->signum(F)F

    move-result v4

    invoke-virtual/range {p1 .. p1}, Lg1/l;->c()F

    move-result v5

    const/high16 v1, 0x42c80000    # 100.0f

    div-float/2addr v1, v5

    mul-float v4, v4, v1

    float-to-double v8, v3

    invoke-static {v8, v9, v6, v7}, Ljava/lang/Math;->pow(DD)D

    move-result-wide v5

    double-to-float v1, v5

    mul-float v4, v4, v1

    invoke-virtual/range {p1 .. p1}, Lg1/l;->i()[F

    move-result-object v1

    const/4 v3, 0x0

    aget v1, v1, v3

    div-float/2addr v0, v1

    invoke-virtual/range {p1 .. p1}, Lg1/l;->i()[F

    move-result-object v1

    const/4 v5, 0x1

    aget v1, v1, v5

    div-float/2addr v2, v1

    invoke-virtual/range {p1 .. p1}, Lg1/l;->i()[F

    move-result-object v1

    const/4 v6, 0x2

    aget v1, v1, v6

    div-float/2addr v4, v1

    sget-object v1, Lg1/b;->b:[[F

    aget-object v7, v1, v3

    aget v8, v7, v3

    mul-float v8, v8, v0

    aget v9, v7, v5

    mul-float v9, v9, v2

    add-float/2addr v8, v9

    aget v7, v7, v6

    mul-float v7, v7, v4

    add-float/2addr v8, v7

    aget-object v7, v1, v5

    aget v9, v7, v3

    mul-float v9, v9, v0

    aget v10, v7, v5

    mul-float v10, v10, v2

    add-float/2addr v9, v10

    aget v7, v7, v6

    mul-float v7, v7, v4

    add-float/2addr v9, v7

    aget-object v1, v1, v6

    aget v3, v1, v3

    mul-float v0, v0, v3

    aget v3, v1, v5

    mul-float v2, v2, v3

    add-float/2addr v0, v2

    aget v1, v1, v6

    mul-float v4, v4, v1

    add-float/2addr v0, v4

    float-to-double v1, v8

    float-to-double v3, v9

    float-to-double v5, v0

    invoke-static/range {v1 .. v6}, Lh1/b;->b(DDD)I

    move-result v0

    return v0
.end method

.method public p()I
    .locals 1

    sget-object v0, Lg1/l;->k:Lg1/l;

    invoke-virtual {p0, v0}, Lg1/a;->o(Lg1/l;)I

    move-result v0

    return v0
.end method
