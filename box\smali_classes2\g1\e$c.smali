.class public final Lg1/e$c;
.super Ljava/lang/Object;

# interfaces
.implements Lg1/e$b;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lg1/e;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "c"
.end annotation


# instance fields
.field public final a:[Lg1/e$d;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>([Lg1/e$d;)V
    .locals 0
    .param p1    # [Lg1/e$d;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lg1/e$c;->a:[Lg1/e$d;

    return-void
.end method


# virtual methods
.method public a()[Lg1/e$d;
    .locals 1
    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    iget-object v0, p0, Lg1/e$c;->a:[Lg1/e$d;

    return-object v0
.end method
