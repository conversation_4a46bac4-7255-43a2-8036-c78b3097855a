.class public final Lg3/a;
.super Ljava/lang/Object;

# interfaces
.implements Lz2/s;


# instance fields
.field public final a:Lz2/s;


# direct methods
.method public constructor <init>(I)V
    .locals 3

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    and-int/lit8 p1, p1, 0x1

    if-eqz p1, :cond_0

    new-instance p1, Lz2/o0;

    const/4 v0, 0x2

    const-string v1, "image/jpeg"

    const v2, 0xffd8

    invoke-direct {p1, v2, v0, v1}, Lz2/o0;-><init>(IILjava/lang/String;)V

    iput-object p1, p0, Lg3/a;->a:Lz2/s;

    goto :goto_0

    :cond_0
    new-instance p1, Lg3/b;

    invoke-direct {p1}, Lg3/b;-><init>()V

    iput-object p1, p0, Lg3/a;->a:Lz2/s;

    :goto_0
    return-void
.end method


# virtual methods
.method public synthetic b()Lz2/s;
    .locals 1

    invoke-static {p0}, Lz2/r;->a(Lz2/s;)Lz2/s;

    move-result-object v0

    return-object v0
.end method

.method public c(Lz2/u;)V
    .locals 1

    iget-object v0, p0, Lg3/a;->a:Lz2/s;

    invoke-interface {v0, p1}, Lz2/s;->c(Lz2/u;)V

    return-void
.end method

.method public d(Lz2/t;Lz2/l0;)I
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget-object v0, p0, Lg3/a;->a:Lz2/s;

    invoke-interface {v0, p1, p2}, Lz2/s;->d(Lz2/t;Lz2/l0;)I

    move-result p1

    return p1
.end method

.method public e(Lz2/t;)Z
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget-object v0, p0, Lg3/a;->a:Lz2/s;

    invoke-interface {v0, p1}, Lz2/s;->e(Lz2/t;)Z

    move-result p1

    return p1
.end method

.method public release()V
    .locals 1

    iget-object v0, p0, Lg3/a;->a:Lz2/s;

    invoke-interface {v0}, Lz2/s;->release()V

    return-void
.end method

.method public seek(JJ)V
    .locals 1

    iget-object v0, p0, Lg3/a;->a:Lz2/s;

    invoke-interface {v0, p1, p2, p3, p4}, Lz2/s;->seek(JJ)V

    return-void
.end method
