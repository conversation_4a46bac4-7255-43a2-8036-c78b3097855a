.class public final Lg3/b;
.super Ljava/lang/Object;

# interfaces
.implements Lz2/s;


# instance fields
.field public final a:Le2/c0;

.field public b:Lz2/u;

.field public c:I

.field public d:I

.field public e:I

.field public f:J

.field public g:Landroidx/media3/extractor/metadata/mp4/MotionPhotoMetadata;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public h:Lz2/t;

.field public i:Lg3/d;

.field public j:Lq3/k;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field


# direct methods
.method public constructor <init>()V
    .locals 2

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    new-instance v0, Le2/c0;

    const/4 v1, 0x6

    invoke-direct {v0, v1}, Le2/c0;-><init>(I)V

    iput-object v0, p0, Lg3/b;->a:Le2/c0;

    const-wide/16 v0, -0x1

    iput-wide v0, p0, Lg3/b;->f:J

    return-void
.end method

.method public static g(Ljava/lang/String;J)Landroidx/media3/extractor/metadata/mp4/MotionPhotoMetadata;
    .locals 4
    .annotation build Landroidx/annotation/Nullable;
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    const-wide/16 v0, -0x1

    const/4 v2, 0x0

    cmp-long v3, p1, v0

    if-nez v3, :cond_0

    return-object v2

    :cond_0
    invoke-static {p0}, Lg3/f;->a(Ljava/lang/String;)Lg3/c;

    move-result-object p0

    if-nez p0, :cond_1

    return-object v2

    :cond_1
    invoke-virtual {p0, p1, p2}, Lg3/c;->a(J)Landroidx/media3/extractor/metadata/mp4/MotionPhotoMetadata;

    move-result-object p0

    return-object p0
.end method

.method private k(Lz2/t;)V
    .locals 5
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget v0, p0, Lg3/b;->d:I

    const v1, 0xffe1

    const/4 v2, 0x0

    if-ne v0, v1, :cond_0

    new-instance v0, Le2/c0;

    iget v1, p0, Lg3/b;->e:I

    invoke-direct {v0, v1}, Le2/c0;-><init>(I)V

    invoke-virtual {v0}, Le2/c0;->e()[B

    move-result-object v1

    iget v3, p0, Lg3/b;->e:I

    invoke-interface {p1, v1, v2, v3}, Lz2/t;->readFully([BII)V

    iget-object v1, p0, Lg3/b;->g:Landroidx/media3/extractor/metadata/mp4/MotionPhotoMetadata;

    if-nez v1, :cond_1

    const-string v1, "http://ns.adobe.com/xap/1.0/"

    invoke-virtual {v0}, Le2/c0;->B()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v1, v3}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_1

    invoke-virtual {v0}, Le2/c0;->B()Ljava/lang/String;

    move-result-object v0

    if-eqz v0, :cond_1

    invoke-interface {p1}, Lz2/t;->getLength()J

    move-result-wide v3

    invoke-static {v0, v3, v4}, Lg3/b;->g(Ljava/lang/String;J)Landroidx/media3/extractor/metadata/mp4/MotionPhotoMetadata;

    move-result-object p1

    iput-object p1, p0, Lg3/b;->g:Landroidx/media3/extractor/metadata/mp4/MotionPhotoMetadata;

    if-eqz p1, :cond_1

    iget-wide v0, p1, Landroidx/media3/extractor/metadata/mp4/MotionPhotoMetadata;->videoStartPosition:J

    iput-wide v0, p0, Lg3/b;->f:J

    goto :goto_0

    :cond_0
    iget v0, p0, Lg3/b;->e:I

    invoke-interface {p1, v0}, Lz2/t;->skipFully(I)V

    :cond_1
    :goto_0
    iput v2, p0, Lg3/b;->c:I

    return-void
.end method


# virtual methods
.method public final a(Lz2/t;)V
    .locals 3
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget-object v0, p0, Lg3/b;->a:Le2/c0;

    const/4 v1, 0x2

    invoke-virtual {v0, v1}, Le2/c0;->Q(I)V

    iget-object v0, p0, Lg3/b;->a:Le2/c0;

    invoke-virtual {v0}, Le2/c0;->e()[B

    move-result-object v0

    const/4 v2, 0x0

    invoke-interface {p1, v0, v2, v1}, Lz2/t;->peekFully([BII)V

    iget-object v0, p0, Lg3/b;->a:Le2/c0;

    invoke-virtual {v0}, Le2/c0;->N()I

    move-result v0

    sub-int/2addr v0, v1

    invoke-interface {p1, v0}, Lz2/t;->advancePeekPosition(I)V

    return-void
.end method

.method public synthetic b()Lz2/s;
    .locals 1

    invoke-static {p0}, Lz2/r;->a(Lz2/s;)Lz2/s;

    move-result-object v0

    return-object v0
.end method

.method public c(Lz2/u;)V
    .locals 0

    iput-object p1, p0, Lg3/b;->b:Lz2/u;

    return-void
.end method

.method public d(Lz2/t;Lz2/l0;)I
    .locals 7
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget v0, p0, Lg3/b;->c:I

    const/4 v1, 0x0

    if-eqz v0, :cond_9

    const/4 v2, 0x1

    if-eq v0, v2, :cond_8

    const/4 v3, 0x2

    if-eq v0, v3, :cond_7

    const/4 v3, 0x4

    if-eq v0, v3, :cond_5

    const/4 v1, 0x5

    if-eq v0, v1, :cond_1

    const/4 p1, 0x6

    if-ne v0, p1, :cond_0

    const/4 p1, -0x1

    return p1

    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    invoke-direct {p1}, Ljava/lang/IllegalStateException;-><init>()V

    throw p1

    :cond_1
    iget-object v0, p0, Lg3/b;->i:Lg3/d;

    if-eqz v0, :cond_2

    iget-object v0, p0, Lg3/b;->h:Lz2/t;

    if-eq p1, v0, :cond_3

    :cond_2
    iput-object p1, p0, Lg3/b;->h:Lz2/t;

    new-instance v0, Lg3/d;

    iget-wide v3, p0, Lg3/b;->f:J

    invoke-direct {v0, p1, v3, v4}, Lg3/d;-><init>(Lz2/t;J)V

    iput-object v0, p0, Lg3/b;->i:Lg3/d;

    :cond_3
    iget-object p1, p0, Lg3/b;->j:Lq3/k;

    invoke-static {p1}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lq3/k;

    iget-object v0, p0, Lg3/b;->i:Lg3/d;

    invoke-virtual {p1, v0, p2}, Lq3/k;->d(Lz2/t;Lz2/l0;)I

    move-result p1

    if-ne p1, v2, :cond_4

    iget-wide v0, p2, Lz2/l0;->a:J

    iget-wide v2, p0, Lg3/b;->f:J

    add-long/2addr v0, v2

    iput-wide v0, p2, Lz2/l0;->a:J

    :cond_4
    return p1

    :cond_5
    invoke-interface {p1}, Lz2/t;->getPosition()J

    move-result-wide v3

    iget-wide v5, p0, Lg3/b;->f:J

    cmp-long v0, v3, v5

    if-eqz v0, :cond_6

    iput-wide v5, p2, Lz2/l0;->a:J

    return v2

    :cond_6
    invoke-virtual {p0, p1}, Lg3/b;->m(Lz2/t;)V

    return v1

    :cond_7
    invoke-direct {p0, p1}, Lg3/b;->k(Lz2/t;)V

    return v1

    :cond_8
    invoke-virtual {p0, p1}, Lg3/b;->l(Lz2/t;)V

    return v1

    :cond_9
    invoke-virtual {p0, p1}, Lg3/b;->j(Lz2/t;)V

    return v1
.end method

.method public e(Lz2/t;)Z
    .locals 5
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    invoke-virtual {p0, p1}, Lg3/b;->i(Lz2/t;)I

    move-result v0

    const v1, 0xffd8

    const/4 v2, 0x0

    if-eq v0, v1, :cond_0

    return v2

    :cond_0
    invoke-virtual {p0, p1}, Lg3/b;->i(Lz2/t;)I

    move-result v0

    iput v0, p0, Lg3/b;->d:I

    const v1, 0xffe0

    if-ne v0, v1, :cond_1

    invoke-virtual {p0, p1}, Lg3/b;->a(Lz2/t;)V

    invoke-virtual {p0, p1}, Lg3/b;->i(Lz2/t;)I

    move-result v0

    iput v0, p0, Lg3/b;->d:I

    :cond_1
    iget v0, p0, Lg3/b;->d:I

    const v1, 0xffe1

    if-eq v0, v1, :cond_2

    return v2

    :cond_2
    const/4 v0, 0x2

    invoke-interface {p1, v0}, Lz2/t;->advancePeekPosition(I)V

    iget-object v0, p0, Lg3/b;->a:Le2/c0;

    const/4 v1, 0x6

    invoke-virtual {v0, v1}, Le2/c0;->Q(I)V

    iget-object v0, p0, Lg3/b;->a:Le2/c0;

    invoke-virtual {v0}, Le2/c0;->e()[B

    move-result-object v0

    invoke-interface {p1, v0, v2, v1}, Lz2/t;->peekFully([BII)V

    iget-object p1, p0, Lg3/b;->a:Le2/c0;

    invoke-virtual {p1}, Le2/c0;->J()J

    move-result-wide v0

    const-wide/32 v3, 0x45786966    # 5.758429993E-315

    cmp-long p1, v0, v3

    if-nez p1, :cond_3

    iget-object p1, p0, Lg3/b;->a:Le2/c0;

    invoke-virtual {p1}, Le2/c0;->N()I

    move-result p1

    if-nez p1, :cond_3

    const/4 v2, 0x1

    :cond_3
    return v2
.end method

.method public final f()V
    .locals 4

    iget-object v0, p0, Lg3/b;->b:Lz2/u;

    invoke-static {v0}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lz2/u;

    invoke-interface {v0}, Lz2/u;->endTracks()V

    iget-object v0, p0, Lg3/b;->b:Lz2/u;

    new-instance v1, Lz2/m0$b;

    const-wide v2, -0x7fffffffffffffffL    # -4.9E-324

    invoke-direct {v1, v2, v3}, Lz2/m0$b;-><init>(J)V

    invoke-interface {v0, v1}, Lz2/u;->g(Lz2/m0;)V

    const/4 v0, 0x6

    iput v0, p0, Lg3/b;->c:I

    return-void
.end method

.method public final h(Landroidx/media3/extractor/metadata/mp4/MotionPhotoMetadata;)V
    .locals 5

    iget-object v0, p0, Lg3/b;->b:Lz2/u;

    invoke-static {v0}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lz2/u;

    const/16 v1, 0x400

    const/4 v2, 0x4

    invoke-interface {v0, v1, v2}, Lz2/u;->track(II)Lz2/r0;

    move-result-object v0

    new-instance v1, Landroidx/media3/common/y$b;

    invoke-direct {v1}, Landroidx/media3/common/y$b;-><init>()V

    const-string v2, "image/jpeg"

    invoke-virtual {v1, v2}, Landroidx/media3/common/y$b;->O(Ljava/lang/String;)Landroidx/media3/common/y$b;

    move-result-object v1

    new-instance v2, Landroidx/media3/common/Metadata;

    const/4 v3, 0x1

    new-array v3, v3, [Landroidx/media3/common/Metadata$Entry;

    const/4 v4, 0x0

    aput-object p1, v3, v4

    invoke-direct {v2, v3}, Landroidx/media3/common/Metadata;-><init>([Landroidx/media3/common/Metadata$Entry;)V

    invoke-virtual {v1, v2}, Landroidx/media3/common/y$b;->d0(Landroidx/media3/common/Metadata;)Landroidx/media3/common/y$b;

    move-result-object p1

    invoke-virtual {p1}, Landroidx/media3/common/y$b;->I()Landroidx/media3/common/y;

    move-result-object p1

    invoke-interface {v0, p1}, Lz2/r0;->a(Landroidx/media3/common/y;)V

    return-void
.end method

.method public final i(Lz2/t;)I
    .locals 3
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget-object v0, p0, Lg3/b;->a:Le2/c0;

    const/4 v1, 0x2

    invoke-virtual {v0, v1}, Le2/c0;->Q(I)V

    iget-object v0, p0, Lg3/b;->a:Le2/c0;

    invoke-virtual {v0}, Le2/c0;->e()[B

    move-result-object v0

    const/4 v2, 0x0

    invoke-interface {p1, v0, v2, v1}, Lz2/t;->peekFully([BII)V

    iget-object p1, p0, Lg3/b;->a:Le2/c0;

    invoke-virtual {p1}, Le2/c0;->N()I

    move-result p1

    return p1
.end method

.method public final j(Lz2/t;)V
    .locals 4
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget-object v0, p0, Lg3/b;->a:Le2/c0;

    const/4 v1, 0x2

    invoke-virtual {v0, v1}, Le2/c0;->Q(I)V

    iget-object v0, p0, Lg3/b;->a:Le2/c0;

    invoke-virtual {v0}, Le2/c0;->e()[B

    move-result-object v0

    const/4 v2, 0x0

    invoke-interface {p1, v0, v2, v1}, Lz2/t;->readFully([BII)V

    iget-object p1, p0, Lg3/b;->a:Le2/c0;

    invoke-virtual {p1}, Le2/c0;->N()I

    move-result p1

    iput p1, p0, Lg3/b;->d:I

    const v0, 0xffda

    if-ne p1, v0, :cond_1

    iget-wide v0, p0, Lg3/b;->f:J

    const-wide/16 v2, -0x1

    cmp-long p1, v0, v2

    if-eqz p1, :cond_0

    const/4 p1, 0x4

    iput p1, p0, Lg3/b;->c:I

    goto :goto_0

    :cond_0
    invoke-virtual {p0}, Lg3/b;->f()V

    goto :goto_0

    :cond_1
    const v0, 0xffd0

    if-lt p1, v0, :cond_2

    const v0, 0xffd9

    if-le p1, v0, :cond_3

    :cond_2
    const v0, 0xff01

    if-eq p1, v0, :cond_3

    const/4 p1, 0x1

    iput p1, p0, Lg3/b;->c:I

    :cond_3
    :goto_0
    return-void
.end method

.method public final l(Lz2/t;)V
    .locals 3
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget-object v0, p0, Lg3/b;->a:Le2/c0;

    const/4 v1, 0x2

    invoke-virtual {v0, v1}, Le2/c0;->Q(I)V

    iget-object v0, p0, Lg3/b;->a:Le2/c0;

    invoke-virtual {v0}, Le2/c0;->e()[B

    move-result-object v0

    const/4 v2, 0x0

    invoke-interface {p1, v0, v2, v1}, Lz2/t;->readFully([BII)V

    iget-object p1, p0, Lg3/b;->a:Le2/c0;

    invoke-virtual {p1}, Le2/c0;->N()I

    move-result p1

    sub-int/2addr p1, v1

    iput p1, p0, Lg3/b;->e:I

    iput v1, p0, Lg3/b;->c:I

    return-void
.end method

.method public final m(Lz2/t;)V
    .locals 4
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget-object v0, p0, Lg3/b;->a:Le2/c0;

    invoke-virtual {v0}, Le2/c0;->e()[B

    move-result-object v0

    const/4 v1, 0x0

    const/4 v2, 0x1

    invoke-interface {p1, v0, v1, v2, v2}, Lz2/t;->peekFully([BIIZ)Z

    move-result v0

    if-nez v0, :cond_0

    invoke-virtual {p0}, Lg3/b;->f()V

    goto :goto_0

    :cond_0
    invoke-interface {p1}, Lz2/t;->resetPeekPosition()V

    iget-object v0, p0, Lg3/b;->j:Lq3/k;

    if-nez v0, :cond_1

    new-instance v0, Lq3/k;

    sget-object v1, Lt3/s$a;->a:Lt3/s$a;

    const/16 v2, 0x8

    invoke-direct {v0, v1, v2}, Lq3/k;-><init>(Lt3/s$a;I)V

    iput-object v0, p0, Lg3/b;->j:Lq3/k;

    :cond_1
    new-instance v0, Lg3/d;

    iget-wide v1, p0, Lg3/b;->f:J

    invoke-direct {v0, p1, v1, v2}, Lg3/d;-><init>(Lz2/t;J)V

    iput-object v0, p0, Lg3/b;->i:Lg3/d;

    iget-object p1, p0, Lg3/b;->j:Lq3/k;

    invoke-virtual {p1, v0}, Lq3/k;->e(Lz2/t;)Z

    move-result p1

    if-eqz p1, :cond_2

    iget-object p1, p0, Lg3/b;->j:Lq3/k;

    new-instance v0, Lg3/e;

    iget-wide v1, p0, Lg3/b;->f:J

    iget-object v3, p0, Lg3/b;->b:Lz2/u;

    invoke-static {v3}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lz2/u;

    invoke-direct {v0, v1, v2, v3}, Lg3/e;-><init>(JLz2/u;)V

    invoke-virtual {p1, v0}, Lq3/k;->c(Lz2/u;)V

    invoke-virtual {p0}, Lg3/b;->n()V

    goto :goto_0

    :cond_2
    invoke-virtual {p0}, Lg3/b;->f()V

    :goto_0
    return-void
.end method

.method public final n()V
    .locals 1

    iget-object v0, p0, Lg3/b;->g:Landroidx/media3/extractor/metadata/mp4/MotionPhotoMetadata;

    invoke-static {v0}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroidx/media3/extractor/metadata/mp4/MotionPhotoMetadata;

    invoke-virtual {p0, v0}, Lg3/b;->h(Landroidx/media3/extractor/metadata/mp4/MotionPhotoMetadata;)V

    const/4 v0, 0x5

    iput v0, p0, Lg3/b;->c:I

    return-void
.end method

.method public release()V
    .locals 1

    iget-object v0, p0, Lg3/b;->j:Lq3/k;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Lq3/k;->release()V

    :cond_0
    return-void
.end method

.method public seek(JJ)V
    .locals 3

    const-wide/16 v0, 0x0

    cmp-long v2, p1, v0

    if-nez v2, :cond_0

    const/4 p1, 0x0

    iput p1, p0, Lg3/b;->c:I

    const/4 p1, 0x0

    iput-object p1, p0, Lg3/b;->j:Lq3/k;

    goto :goto_0

    :cond_0
    iget v0, p0, Lg3/b;->c:I

    const/4 v1, 0x5

    if-ne v0, v1, :cond_1

    iget-object v0, p0, Lg3/b;->j:Lq3/k;

    invoke-static {v0}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lq3/k;

    invoke-virtual {v0, p1, p2, p3, p4}, Lq3/k;->seek(JJ)V

    :cond_1
    :goto_0
    return-void
.end method
