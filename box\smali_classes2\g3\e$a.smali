.class public Lg3/e$a;
.super Lz2/e0;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lg3/e;->g(Lz2/m0;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field public final synthetic b:Lz2/m0;

.field public final synthetic c:Lg3/e;


# direct methods
.method public constructor <init>(Lg3/e;Lz2/m0;Lz2/m0;)V
    .locals 0

    iput-object p1, p0, Lg3/e$a;->c:Lg3/e;

    iput-object p3, p0, Lg3/e$a;->b:Lz2/m0;

    invoke-direct {p0, p2}, Lz2/e0;-><init>(Lz2/m0;)V

    return-void
.end method


# virtual methods
.method public getSeekPoints(J)Lz2/m0$a;
    .locals 8

    iget-object v0, p0, Lg3/e$a;->b:Lz2/m0;

    invoke-interface {v0, p1, p2}, Lz2/m0;->getSeekPoints(J)Lz2/m0$a;

    move-result-object p1

    new-instance p2, Lz2/m0$a;

    new-instance v0, Lz2/n0;

    iget-object v1, p1, Lz2/m0$a;->a:Lz2/n0;

    iget-wide v2, v1, Lz2/n0;->a:J

    iget-wide v4, v1, Lz2/n0;->b:J

    iget-object v1, p0, Lg3/e$a;->c:Lg3/e;

    invoke-static {v1}, Lg3/e;->a(Lg3/e;)J

    move-result-wide v6

    add-long/2addr v4, v6

    invoke-direct {v0, v2, v3, v4, v5}, Lz2/n0;-><init>(JJ)V

    new-instance v1, Lz2/n0;

    iget-object p1, p1, Lz2/m0$a;->b:Lz2/n0;

    iget-wide v2, p1, Lz2/n0;->a:J

    iget-wide v4, p1, Lz2/n0;->b:J

    iget-object p1, p0, Lg3/e$a;->c:Lg3/e;

    invoke-static {p1}, Lg3/e;->a(Lg3/e;)J

    move-result-wide v6

    add-long/2addr v4, v6

    invoke-direct {v1, v2, v3, v4, v5}, Lz2/n0;-><init>(JJ)V

    invoke-direct {p2, v0, v1}, Lz2/m0$a;-><init>(Lz2/n0;Lz2/n0;)V

    return-object p2
.end method
