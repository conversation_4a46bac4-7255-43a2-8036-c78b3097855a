.class public final Lg3/e;
.super Ljava/lang/Object;

# interfaces
.implements Lz2/u;


# instance fields
.field public final a:J

.field public final b:Lz2/u;


# direct methods
.method public constructor <init>(JLz2/u;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-wide p1, p0, Lg3/e;->a:J

    iput-object p3, p0, Lg3/e;->b:Lz2/u;

    return-void
.end method

.method public static synthetic a(Lg3/e;)J
    .locals 2

    iget-wide v0, p0, Lg3/e;->a:J

    return-wide v0
.end method


# virtual methods
.method public endTracks()V
    .locals 1

    iget-object v0, p0, Lg3/e;->b:Lz2/u;

    invoke-interface {v0}, Lz2/u;->endTracks()V

    return-void
.end method

.method public g(Lz2/m0;)V
    .locals 2

    iget-object v0, p0, Lg3/e;->b:Lz2/u;

    new-instance v1, Lg3/e$a;

    invoke-direct {v1, p0, p1, p1}, Lg3/e$a;-><init>(Lg3/e;Lz2/m0;Lz2/m0;)V

    invoke-interface {v0, v1}, Lz2/u;->g(Lz2/m0;)V

    return-void
.end method

.method public track(II)Lz2/r0;
    .locals 1

    iget-object v0, p0, Lg3/e;->b:Lz2/u;

    invoke-interface {v0, p1, p2}, Lz2/u;->track(II)Lz2/r0;

    move-result-object p1

    return-object p1
.end method
