.class public Lg5/g;
.super Lg5/p;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lg5/p<",
        "Lm5/d;",
        "Lm5/d;",
        ">;"
    }
.end annotation


# direct methods
.method public constructor <init>(Ljava/util/List;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lm5/a<",
            "Lm5/d;",
            ">;>;)V"
        }
    .end annotation

    invoke-direct {p0, p1}, Lg5/p;-><init>(Ljava/util/List;)V

    return-void
.end method


# virtual methods
.method public a()Ld5/a;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ld5/a<",
            "Lm5/d;",
            "Lm5/d;",
            ">;"
        }
    .end annotation

    new-instance v0, Ld5/l;

    iget-object v1, p0, Lg5/p;->a:Ljava/util/List;

    invoke-direct {v0, v1}, Ld5/l;-><init>(Ljava/util/List;)V

    return-object v0
.end method

.method public bridge synthetic b()Ljava/util/List;
    .locals 1

    invoke-super {p0}, Lg5/p;->b()Ljava/util/List;

    move-result-object v0

    return-object v0
.end method

.method public bridge synthetic c()Z
    .locals 1

    invoke-super {p0}, Lg5/p;->c()Z

    move-result v0

    return v0
.end method

.method public bridge synthetic toString()Ljava/lang/String;
    .locals 1

    invoke-super {p0}, Lg5/p;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
