.class public Lg5/i;
.super Ljava/lang/Object;

# interfaces
.implements Lg5/o;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Lg5/o<",
        "Landroid/graphics/PointF;",
        "Landroid/graphics/PointF;",
        ">;"
    }
.end annotation


# instance fields
.field public final a:Lg5/b;

.field public final b:Lg5/b;


# direct methods
.method public constructor <init>(Lg5/b;Lg5/b;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lg5/i;->a:Lg5/b;

    iput-object p2, p0, Lg5/i;->b:Lg5/b;

    return-void
.end method


# virtual methods
.method public a()Ld5/a;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ld5/a<",
            "Landroid/graphics/PointF;",
            "Landroid/graphics/PointF;",
            ">;"
        }
    .end annotation

    new-instance v0, Ld5/n;

    iget-object v1, p0, Lg5/i;->a:Lg5/b;

    invoke-virtual {v1}, Lg5/b;->d()Ld5/d;

    move-result-object v1

    iget-object v2, p0, Lg5/i;->b:Lg5/b;

    invoke-virtual {v2}, Lg5/b;->d()Ld5/d;

    move-result-object v2

    invoke-direct {v0, v1, v2}, Ld5/n;-><init>(Ld5/a;Ld5/a;)V

    return-object v0
.end method

.method public b()Ljava/util/List;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lm5/a<",
            "Landroid/graphics/PointF;",
            ">;>;"
        }
    .end annotation

    new-instance v0, Ljava/lang/UnsupportedOperationException;

    const-string v1, "Cannot call getKeyframes on AnimatableSplitDimensionPathValue."

    invoke-direct {v0, v1}, Ljava/lang/UnsupportedOperationException;-><init>(Ljava/lang/String;)V

    throw v0
.end method

.method public c()Z
    .locals 1

    iget-object v0, p0, Lg5/i;->a:Lg5/b;

    invoke-virtual {v0}, Lg5/b;->c()Z

    move-result v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Lg5/i;->b:Lg5/b;

    invoke-virtual {v0}, Lg5/b;->c()Z

    move-result v0

    if-eqz v0, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    return v0
.end method
