.class public Lg5/j;
.super Lg5/p;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lg5/p<",
        "Lcom/airbnb/lottie/model/DocumentData;",
        "Lcom/airbnb/lottie/model/DocumentData;",
        ">;"
    }
.end annotation


# direct methods
.method public constructor <init>(Ljava/util/List;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lm5/a<",
            "Lcom/airbnb/lottie/model/DocumentData;",
            ">;>;)V"
        }
    .end annotation

    invoke-direct {p0, p1}, Lg5/p;-><init>(Ljava/util/List;)V

    return-void
.end method


# virtual methods
.method public bridge synthetic a()Ld5/a;
    .locals 1

    invoke-virtual {p0}, Lg5/j;->d()Ld5/o;

    move-result-object v0

    return-object v0
.end method

.method public bridge synthetic b()Ljava/util/List;
    .locals 1

    invoke-super {p0}, Lg5/p;->b()Ljava/util/List;

    move-result-object v0

    return-object v0
.end method

.method public bridge synthetic c()Z
    .locals 1

    invoke-super {p0}, Lg5/p;->c()Z

    move-result v0

    return v0
.end method

.method public d()Ld5/o;
    .locals 2

    new-instance v0, Ld5/o;

    iget-object v1, p0, Lg5/p;->a:Ljava/util/List;

    invoke-direct {v0, v1}, Ld5/o;-><init>(Ljava/util/List;)V

    return-object v0
.end method

.method public bridge synthetic toString()Ljava/lang/String;
    .locals 1

    invoke-super {p0}, Lg5/p;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
