.class public Lg5/l;
.super Ljava/lang/Object;


# instance fields
.field public final a:Lg5/d;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public final b:Lg5/d;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public final c:Lg5/d;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public final d:Lcom/airbnb/lottie/model/content/TextRangeUnits;


# direct methods
.method public constructor <init>(Lg5/d;Lg5/d;Lg5/d;Lcom/airbnb/lottie/model/content/TextRangeUnits;)V
    .locals 0
    .param p1    # Lg5/d;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .param p2    # Lg5/d;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .param p3    # Lg5/d;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lg5/l;->a:Lg5/d;

    iput-object p2, p0, Lg5/l;->b:Lg5/d;

    iput-object p3, p0, Lg5/l;->c:Lg5/d;

    iput-object p4, p0, Lg5/l;->d:Lcom/airbnb/lottie/model/content/TextRangeUnits;

    return-void
.end method
