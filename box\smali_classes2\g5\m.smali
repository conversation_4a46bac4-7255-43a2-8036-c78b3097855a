.class public Lg5/m;
.super Ljava/lang/Object;


# instance fields
.field public final a:Lg5/a;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public final b:Lg5/a;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public final c:Lg5/b;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public final d:Lg5/b;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public final e:Lg5/d;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lg5/a;Lg5/a;Lg5/b;Lg5/b;Lg5/d;)V
    .locals 0
    .param p1    # Lg5/a;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .param p2    # Lg5/a;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .param p3    # Lg5/b;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .param p4    # Lg5/b;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .param p5    # Lg5/d;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lg5/m;->a:Lg5/a;

    iput-object p2, p0, Lg5/m;->b:Lg5/a;

    iput-object p3, p0, Lg5/m;->c:Lg5/b;

    iput-object p4, p0, Lg5/m;->d:Lg5/b;

    iput-object p5, p0, Lg5/m;->e:Lg5/d;

    return-void
.end method
