.class public Lg5/n;
.super Ljava/lang/Object;

# interfaces
.implements Lh5/c;


# instance fields
.field public final a:Lg5/e;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public final b:Lg5/o;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation

    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lg5/o<",
            "Landroid/graphics/PointF;",
            "Landroid/graphics/PointF;",
            ">;"
        }
    .end annotation
.end field

.field public final c:Lg5/g;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public final d:Lg5/b;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public final e:Lg5/d;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public final f:Lg5/b;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public final g:Lg5/b;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public final h:Lg5/b;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public final i:Lg5/b;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public j:Z


# direct methods
.method public constructor <init>()V
    .locals 10

    const/4 v1, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    const/4 v4, 0x0

    const/4 v5, 0x0

    const/4 v6, 0x0

    const/4 v7, 0x0

    const/4 v8, 0x0

    const/4 v9, 0x0

    move-object v0, p0

    invoke-direct/range {v0 .. v9}, Lg5/n;-><init>(Lg5/e;Lg5/o;Lg5/g;Lg5/b;Lg5/d;Lg5/b;Lg5/b;Lg5/b;Lg5/b;)V

    return-void
.end method

.method public constructor <init>(Lg5/e;Lg5/o;Lg5/g;Lg5/b;Lg5/d;Lg5/b;Lg5/b;Lg5/b;Lg5/b;)V
    .locals 1
    .param p1    # Lg5/e;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .param p2    # Lg5/o;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .param p3    # Lg5/g;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .param p4    # Lg5/b;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .param p5    # Lg5/d;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .param p6    # Lg5/b;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .param p7    # Lg5/b;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .param p8    # Lg5/b;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .param p9    # Lg5/b;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lg5/e;",
            "Lg5/o<",
            "Landroid/graphics/PointF;",
            "Landroid/graphics/PointF;",
            ">;",
            "Lg5/g;",
            "Lg5/b;",
            "Lg5/d;",
            "Lg5/b;",
            "Lg5/b;",
            "Lg5/b;",
            "Lg5/b;",
            ")V"
        }
    .end annotation

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, 0x0

    iput-boolean v0, p0, Lg5/n;->j:Z

    iput-object p1, p0, Lg5/n;->a:Lg5/e;

    iput-object p2, p0, Lg5/n;->b:Lg5/o;

    iput-object p3, p0, Lg5/n;->c:Lg5/g;

    iput-object p4, p0, Lg5/n;->d:Lg5/b;

    iput-object p5, p0, Lg5/n;->e:Lg5/d;

    iput-object p6, p0, Lg5/n;->h:Lg5/b;

    iput-object p7, p0, Lg5/n;->i:Lg5/b;

    iput-object p8, p0, Lg5/n;->f:Lg5/b;

    iput-object p9, p0, Lg5/n;->g:Lg5/b;

    return-void
.end method


# virtual methods
.method public a(Lcom/airbnb/lottie/LottieDrawable;Lcom/airbnb/lottie/h;Lcom/airbnb/lottie/model/layer/a;)Lc5/c;
    .locals 0
    .annotation build Landroidx/annotation/Nullable;
    .end annotation

    const/4 p1, 0x0

    return-object p1
.end method

.method public b()Ld5/p;
    .locals 1

    new-instance v0, Ld5/p;

    invoke-direct {v0, p0}, Ld5/p;-><init>(Lg5/n;)V

    return-object v0
.end method

.method public c()Lg5/e;
    .locals 1
    .annotation build Landroidx/annotation/Nullable;
    .end annotation

    iget-object v0, p0, Lg5/n;->a:Lg5/e;

    return-object v0
.end method

.method public d()Lg5/b;
    .locals 1
    .annotation build Landroidx/annotation/Nullable;
    .end annotation

    iget-object v0, p0, Lg5/n;->i:Lg5/b;

    return-object v0
.end method

.method public e()Lg5/d;
    .locals 1
    .annotation build Landroidx/annotation/Nullable;
    .end annotation

    iget-object v0, p0, Lg5/n;->e:Lg5/d;

    return-object v0
.end method

.method public f()Lg5/o;
    .locals 1
    .annotation build Landroidx/annotation/Nullable;
    .end annotation

    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lg5/o<",
            "Landroid/graphics/PointF;",
            "Landroid/graphics/PointF;",
            ">;"
        }
    .end annotation

    iget-object v0, p0, Lg5/n;->b:Lg5/o;

    return-object v0
.end method

.method public g()Lg5/b;
    .locals 1
    .annotation build Landroidx/annotation/Nullable;
    .end annotation

    iget-object v0, p0, Lg5/n;->d:Lg5/b;

    return-object v0
.end method

.method public h()Lg5/g;
    .locals 1
    .annotation build Landroidx/annotation/Nullable;
    .end annotation

    iget-object v0, p0, Lg5/n;->c:Lg5/g;

    return-object v0
.end method

.method public i()Lg5/b;
    .locals 1
    .annotation build Landroidx/annotation/Nullable;
    .end annotation

    iget-object v0, p0, Lg5/n;->f:Lg5/b;

    return-object v0
.end method

.method public j()Lg5/b;
    .locals 1
    .annotation build Landroidx/annotation/Nullable;
    .end annotation

    iget-object v0, p0, Lg5/n;->g:Lg5/b;

    return-object v0
.end method

.method public k()Lg5/b;
    .locals 1
    .annotation build Landroidx/annotation/Nullable;
    .end annotation

    iget-object v0, p0, Lg5/n;->h:Lg5/b;

    return-object v0
.end method

.method public l()Z
    .locals 1

    iget-boolean v0, p0, Lg5/n;->j:Z

    return v0
.end method

.method public m(Z)V
    .locals 0

    iput-boolean p1, p0, Lg5/n;->j:Z

    return-void
.end method
