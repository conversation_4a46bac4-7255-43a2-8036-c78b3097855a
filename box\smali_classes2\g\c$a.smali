.class public Lg/c$a;
.super Ljava/lang/Object;

# interfaces
.implements Landroidx/lifecycle/r;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lg/c;->i(Ljava/lang/String;Landroidx/lifecycle/u;Lh/a;Lg/a;)Lg/b;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field public final synthetic a:Ljava/lang/String;

.field public final synthetic b:Lg/a;

.field public final synthetic c:Lh/a;

.field public final synthetic d:Lg/c;


# direct methods
.method public constructor <init>(Lg/c;Ljava/lang/String;Lg/a;Lh/a;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation

    iput-object p1, p0, Lg/c$a;->d:Lg/c;

    iput-object p2, p0, Lg/c$a;->a:Ljava/lang/String;

    iput-object p3, p0, Lg/c$a;->b:Lg/a;

    iput-object p4, p0, Lg/c$a;->c:Lh/a;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public onStateChanged(Landroidx/lifecycle/u;Landroidx/lifecycle/Lifecycle$Event;)V
    .locals 3
    .param p1    # Landroidx/lifecycle/u;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p2    # Landroidx/lifecycle/Lifecycle$Event;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param

    sget-object p1, Landroidx/lifecycle/Lifecycle$Event;->ON_START:Landroidx/lifecycle/Lifecycle$Event;

    invoke-virtual {p1, p2}, Ljava/lang/Object;->equals(Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_1

    iget-object p1, p0, Lg/c$a;->d:Lg/c;

    iget-object p1, p1, Lg/c;->e:Ljava/util/Map;

    iget-object p2, p0, Lg/c$a;->a:Ljava/lang/String;

    new-instance v0, Lg/c$d;

    iget-object v1, p0, Lg/c$a;->b:Lg/a;

    iget-object v2, p0, Lg/c$a;->c:Lh/a;

    invoke-direct {v0, v1, v2}, Lg/c$d;-><init>(Lg/a;Lh/a;)V

    invoke-interface {p1, p2, v0}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    iget-object p1, p0, Lg/c$a;->d:Lg/c;

    iget-object p1, p1, Lg/c;->f:Ljava/util/Map;

    iget-object p2, p0, Lg/c$a;->a:Ljava/lang/String;

    invoke-interface {p1, p2}, Ljava/util/Map;->containsKey(Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_0

    iget-object p1, p0, Lg/c$a;->d:Lg/c;

    iget-object p1, p1, Lg/c;->f:Ljava/util/Map;

    iget-object p2, p0, Lg/c$a;->a:Ljava/lang/String;

    invoke-interface {p1, p2}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    iget-object p2, p0, Lg/c$a;->d:Lg/c;

    iget-object p2, p2, Lg/c;->f:Ljava/util/Map;

    iget-object v0, p0, Lg/c$a;->a:Ljava/lang/String;

    invoke-interface {p2, v0}, Ljava/util/Map;->remove(Ljava/lang/Object;)Ljava/lang/Object;

    iget-object p2, p0, Lg/c$a;->b:Lg/a;

    invoke-interface {p2, p1}, Lg/a;->a(Ljava/lang/Object;)V

    :cond_0
    iget-object p1, p0, Lg/c$a;->d:Lg/c;

    iget-object p1, p1, Lg/c;->g:Landroid/os/Bundle;

    iget-object p2, p0, Lg/c$a;->a:Ljava/lang/String;

    invoke-virtual {p1, p2}, Landroid/os/Bundle;->getParcelable(Ljava/lang/String;)Landroid/os/Parcelable;

    move-result-object p1

    check-cast p1, Landroidx/activity/result/ActivityResult;

    if-eqz p1, :cond_3

    iget-object p2, p0, Lg/c$a;->d:Lg/c;

    iget-object p2, p2, Lg/c;->g:Landroid/os/Bundle;

    iget-object v0, p0, Lg/c$a;->a:Ljava/lang/String;

    invoke-virtual {p2, v0}, Landroid/os/Bundle;->remove(Ljava/lang/String;)V

    iget-object p2, p0, Lg/c$a;->b:Lg/a;

    iget-object v0, p0, Lg/c$a;->c:Lh/a;

    invoke-virtual {p1}, Landroidx/activity/result/ActivityResult;->c()I

    move-result v1

    invoke-virtual {p1}, Landroidx/activity/result/ActivityResult;->a()Landroid/content/Intent;

    move-result-object p1

    invoke-virtual {v0, v1, p1}, Lh/a;->c(ILandroid/content/Intent;)Ljava/lang/Object;

    move-result-object p1

    invoke-interface {p2, p1}, Lg/a;->a(Ljava/lang/Object;)V

    goto :goto_0

    :cond_1
    sget-object p1, Landroidx/lifecycle/Lifecycle$Event;->ON_STOP:Landroidx/lifecycle/Lifecycle$Event;

    invoke-virtual {p1, p2}, Ljava/lang/Object;->equals(Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_2

    iget-object p1, p0, Lg/c$a;->d:Lg/c;

    iget-object p1, p1, Lg/c;->e:Ljava/util/Map;

    iget-object p2, p0, Lg/c$a;->a:Ljava/lang/String;

    invoke-interface {p1, p2}, Ljava/util/Map;->remove(Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_0

    :cond_2
    sget-object p1, Landroidx/lifecycle/Lifecycle$Event;->ON_DESTROY:Landroidx/lifecycle/Lifecycle$Event;

    invoke-virtual {p1, p2}, Ljava/lang/Object;->equals(Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_3

    iget-object p1, p0, Lg/c$a;->d:Lg/c;

    iget-object p2, p0, Lg/c$a;->a:Ljava/lang/String;

    invoke-virtual {p1, p2}, Lg/c;->l(Ljava/lang/String;)V

    :cond_3
    :goto_0
    return-void
.end method
