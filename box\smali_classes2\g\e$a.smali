.class public final Lg/e$a;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lg/e;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation


# instance fields
.field public a:Lh/f$f;


# direct methods
.method public constructor <init>()V
    .locals 1

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    sget-object v0, Lh/f$b;->a:Lh/f$b;

    iput-object v0, p0, Lg/e$a;->a:Lh/f$f;

    return-void
.end method


# virtual methods
.method public final a()Lg/e;
    .locals 2

    new-instance v0, Lg/e;

    invoke-direct {v0}, Lg/e;-><init>()V

    iget-object v1, p0, Lg/e$a;->a:Lh/f$f;

    invoke-virtual {v0, v1}, Lg/e;->b(Lh/f$f;)V

    return-object v0
.end method

.method public final b(Lh/f$f;)Lg/e$a;
    .locals 1

    const-string v0, "mediaType"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    iput-object p1, p0, Lg/e$a;->a:Lh/f$f;

    return-object p0
.end method
