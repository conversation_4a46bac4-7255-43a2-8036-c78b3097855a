.class public final Lg/e;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lg/e$a;
    }
.end annotation

.annotation runtime L<PERSON>lin/Metadata;
.end annotation


# instance fields
.field public a:Lh/f$f;


# direct methods
.method public constructor <init>()V
    .locals 1

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    sget-object v0, Lh/f$b;->a:Lh/f$b;

    iput-object v0, p0, Lg/e;->a:Lh/f$f;

    return-void
.end method


# virtual methods
.method public final a()Lh/f$f;
    .locals 1

    iget-object v0, p0, Lg/e;->a:Lh/f$f;

    return-object v0
.end method

.method public final b(Lh/f$f;)V
    .locals 1

    const-string v0, "<set-?>"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->g(<PERSON><PERSON><PERSON>/lang/Object;Ljava/lang/String;)V

    iput-object p1, p0, Lg/e;->a:Lh/f$f;

    return-void
.end method
