.class public final Lg/f;
.super Ljava/lang/Object;


# annotations
.annotation runtime L<PERSON>lin/Metadata;
.end annotation


# direct methods
.method public static final a(Lh/f$f;)Lg/e;
    .locals 1

    const-string v0, "mediaType"

    invoke-static {p0, v0}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    new-instance v0, Lg/e$a;

    invoke-direct {v0}, Lg/e$a;-><init>()V

    invoke-virtual {v0, p0}, Lg/e$a;->b(Lh/f$f;)Lg/e$a;

    move-result-object p0

    invoke-virtual {p0}, Lg/e$a;->a()Lg/e;

    move-result-object p0

    return-object p0
.end method
