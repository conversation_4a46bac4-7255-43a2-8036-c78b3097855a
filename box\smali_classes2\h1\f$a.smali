.class public Lh1/f$a;
.super Landroidx/core/provider/g$c;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lh1/f;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "a"
.end annotation


# instance fields
.field public a:Lg1/h$e;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lg1/h$e;)V
    .locals 0
    .param p1    # Lg1/h$e;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    invoke-direct {p0}, Landroidx/core/provider/g$c;-><init>()V

    iput-object p1, p0, Lh1/f$a;->a:Lg1/h$e;

    return-void
.end method


# virtual methods
.method public a(I)V
    .locals 1

    iget-object v0, p0, Lh1/f$a;->a:Lg1/h$e;

    if-eqz v0, :cond_0

    invoke-virtual {v0, p1}, Lg1/h$e;->h(I)V

    :cond_0
    return-void
.end method

.method public b(Landroid/graphics/Typeface;)V
    .locals 1
    .param p1    # Landroid/graphics/Typeface;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param

    iget-object v0, p0, Lh1/f$a;->a:Lg1/h$e;

    if-eqz v0, :cond_0

    invoke-virtual {v0, p1}, Lg1/h$e;->i(Landroid/graphics/Typeface;)V

    :cond_0
    return-void
.end method
