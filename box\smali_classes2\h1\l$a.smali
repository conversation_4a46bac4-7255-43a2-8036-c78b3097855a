.class public Lh1/l$a;
.super Ljava/lang/Object;

# interfaces
.implements Lh1/l$b;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lh1/l;->g([Landroidx/core/provider/g$b;I)Landroidx/core/provider/g$b;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Lh1/l$b<",
        "Landroidx/core/provider/g$b;",
        ">;"
    }
.end annotation


# instance fields
.field public final synthetic a:Lh1/l;


# direct methods
.method public constructor <init>(Lh1/l;)V
    .locals 0

    iput-object p1, p0, Lh1/l$a;->a:Lh1/l;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public bridge synthetic a(Ljava/lang/Object;)Z
    .locals 0

    check-cast p1, Landroidx/core/provider/g$b;

    invoke-virtual {p0, p1}, Lh1/l$a;->d(Landroidx/core/provider/g$b;)Z

    move-result p1

    return p1
.end method

.method public bridge synthetic b(Ljava/lang/Object;)I
    .locals 0

    check-cast p1, Landroidx/core/provider/g$b;

    invoke-virtual {p0, p1}, Lh1/l$a;->c(Landroidx/core/provider/g$b;)I

    move-result p1

    return p1
.end method

.method public c(Landroidx/core/provider/g$b;)I
    .locals 0

    invoke-virtual {p1}, Landroidx/core/provider/g$b;->e()I

    move-result p1

    return p1
.end method

.method public d(Landroidx/core/provider/g$b;)Z
    .locals 0

    invoke-virtual {p1}, Landroidx/core/provider/g$b;->f()Z

    move-result p1

    return p1
.end method
