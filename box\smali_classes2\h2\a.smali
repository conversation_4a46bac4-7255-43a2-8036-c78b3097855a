.class public abstract Lh2/a;
.super Ljava/lang/Object;

# interfaces
.implements Landroidx/media3/datasource/a;


# instance fields
.field public final a:Z

.field public final b:Ljava/util/ArrayList;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/ArrayList<",
            "Lh2/o;",
            ">;"
        }
    .end annotation
.end field

.field public c:I

.field public d:Lh2/g;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field


# direct methods
.method public constructor <init>(Z)V
    .locals 1

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-boolean p1, p0, Lh2/a;->a:Z

    new-instance p1, Ljava/util/ArrayList;

    const/4 v0, 0x1

    invoke-direct {p1, v0}, Ljava/util/ArrayList;-><init>(I)V

    iput-object p1, p0, Lh2/a;->b:L<PERSON><PERSON>/util/ArrayList;

    return-void
.end method


# virtual methods
.method public final c(Lh2/o;)V
    .locals 1

    invoke-static {p1}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    iget-object v0, p0, Lh2/a;->b:Ljava/util/ArrayList;

    invoke-virtual {v0, p1}, Ljava/util/ArrayList;->contains(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_0

    iget-object v0, p0, Lh2/a;->b:Ljava/util/ArrayList;

    invoke-virtual {v0, p1}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    iget p1, p0, Lh2/a;->c:I

    add-int/lit8 p1, p1, 0x1

    iput p1, p0, Lh2/a;->c:I

    :cond_0
    return-void
.end method

.method public final d(I)V
    .locals 4

    iget-object v0, p0, Lh2/a;->d:Lh2/g;

    invoke-static {v0}, Le2/u0;->i(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lh2/g;

    const/4 v1, 0x0

    :goto_0
    iget v2, p0, Lh2/a;->c:I

    if-ge v1, v2, :cond_0

    iget-object v2, p0, Lh2/a;->b:Ljava/util/ArrayList;

    invoke-virtual {v2, v1}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lh2/o;

    iget-boolean v3, p0, Lh2/a;->a:Z

    invoke-interface {v2, p0, v0, v3, p1}, Lh2/o;->h(Landroidx/media3/datasource/a;Lh2/g;ZI)V

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_0
    return-void
.end method

.method public final e()V
    .locals 4

    iget-object v0, p0, Lh2/a;->d:Lh2/g;

    invoke-static {v0}, Le2/u0;->i(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lh2/g;

    const/4 v1, 0x0

    :goto_0
    iget v2, p0, Lh2/a;->c:I

    if-ge v1, v2, :cond_0

    iget-object v2, p0, Lh2/a;->b:Ljava/util/ArrayList;

    invoke-virtual {v2, v1}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lh2/o;

    iget-boolean v3, p0, Lh2/a;->a:Z

    invoke-interface {v2, p0, v0, v3}, Lh2/o;->f(Landroidx/media3/datasource/a;Lh2/g;Z)V

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    iput-object v0, p0, Lh2/a;->d:Lh2/g;

    return-void
.end method

.method public final f(Lh2/g;)V
    .locals 3

    const/4 v0, 0x0

    :goto_0
    iget v1, p0, Lh2/a;->c:I

    if-ge v0, v1, :cond_0

    iget-object v1, p0, Lh2/a;->b:Ljava/util/ArrayList;

    invoke-virtual {v1, v0}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lh2/o;

    iget-boolean v2, p0, Lh2/a;->a:Z

    invoke-interface {v1, p0, p1, v2}, Lh2/o;->g(Landroidx/media3/datasource/a;Lh2/g;Z)V

    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :cond_0
    return-void
.end method

.method public final g(Lh2/g;)V
    .locals 3

    iput-object p1, p0, Lh2/a;->d:Lh2/g;

    const/4 v0, 0x0

    :goto_0
    iget v1, p0, Lh2/a;->c:I

    if-ge v0, v1, :cond_0

    iget-object v1, p0, Lh2/a;->b:Ljava/util/ArrayList;

    invoke-virtual {v1, v0}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lh2/o;

    iget-boolean v2, p0, Lh2/a;->a:Z

    invoke-interface {v1, p0, p1, v2}, Lh2/o;->e(Landroidx/media3/datasource/a;Lh2/g;Z)V

    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :cond_0
    return-void
.end method

.method public synthetic getResponseHeaders()Ljava/util/Map;
    .locals 1

    invoke-static {p0}, Lh2/d;->a(Landroidx/media3/datasource/a;)Ljava/util/Map;

    move-result-object v0

    return-object v0
.end method
