.class public Lh5/a;
.super Ljava/lang/Object;


# instance fields
.field public final a:Lg5/b;


# direct methods
.method public constructor <init>(Lg5/b;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lh5/a;->a:Lg5/b;

    return-void
.end method


# virtual methods
.method public a()Lg5/b;
    .locals 1

    iget-object v0, p0, Lh5/a;->a:Lg5/b;

    return-object v0
.end method
