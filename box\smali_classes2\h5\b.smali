.class public Lh5/b;
.super Ljava/lang/Object;

# interfaces
.implements Lh5/c;


# instance fields
.field public final a:Ljava/lang/String;

.field public final b:Lg5/o;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lg5/o<",
            "Landroid/graphics/PointF;",
            "Landroid/graphics/PointF;",
            ">;"
        }
    .end annotation
.end field

.field public final c:Lg5/f;

.field public final d:Z

.field public final e:Z


# direct methods
.method public constructor <init>(Ljava/lang/String;Lg5/o;Lg5/f;ZZ)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Lg5/o<",
            "Landroid/graphics/PointF;",
            "Landroid/graphics/PointF;",
            ">;",
            "Lg5/f;",
            "ZZ)V"
        }
    .end annotation

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lh5/b;->a:Ljava/lang/String;

    iput-object p2, p0, Lh5/b;->b:Lg5/o;

    iput-object p3, p0, Lh5/b;->c:Lg5/f;

    iput-boolean p4, p0, Lh5/b;->d:Z

    iput-boolean p5, p0, Lh5/b;->e:Z

    return-void
.end method


# virtual methods
.method public a(Lcom/airbnb/lottie/LottieDrawable;Lcom/airbnb/lottie/h;Lcom/airbnb/lottie/model/layer/a;)Lc5/c;
    .locals 0

    new-instance p2, Lc5/f;

    invoke-direct {p2, p1, p3, p0}, Lc5/f;-><init>(Lcom/airbnb/lottie/LottieDrawable;Lcom/airbnb/lottie/model/layer/a;Lh5/b;)V

    return-object p2
.end method

.method public b()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lh5/b;->a:Ljava/lang/String;

    return-object v0
.end method

.method public c()Lg5/o;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lg5/o<",
            "Landroid/graphics/PointF;",
            "Landroid/graphics/PointF;",
            ">;"
        }
    .end annotation

    iget-object v0, p0, Lh5/b;->b:Lg5/o;

    return-object v0
.end method

.method public d()Lg5/f;
    .locals 1

    iget-object v0, p0, Lh5/b;->c:Lg5/f;

    return-object v0
.end method

.method public e()Z
    .locals 1

    iget-boolean v0, p0, Lh5/b;->e:Z

    return v0
.end method

.method public f()Z
    .locals 1

    iget-boolean v0, p0, Lh5/b;->d:Z

    return v0
.end method
