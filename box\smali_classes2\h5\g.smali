.class public Lh5/g;
.super Ljava/lang/Object;

# interfaces
.implements Lh5/c;


# instance fields
.field public final a:Ljava/lang/String;

.field public final b:Lg5/b;

.field public final c:Lg5/b;

.field public final d:Lg5/n;

.field public final e:Z


# direct methods
.method public constructor <init>(Ljava/lang/String;Lg5/b;Lg5/b;Lg5/n;Z)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lh5/g;->a:Ljava/lang/String;

    iput-object p2, p0, Lh5/g;->b:Lg5/b;

    iput-object p3, p0, Lh5/g;->c:Lg5/b;

    iput-object p4, p0, Lh5/g;->d:Lg5/n;

    iput-boolean p5, p0, Lh5/g;->e:Z

    return-void
.end method


# virtual methods
.method public a(Lcom/airbnb/lottie/LottieDrawable;Lcom/airbnb/lottie/h;Lcom/airbnb/lottie/model/layer/a;)Lc5/c;
    .locals 0
    .annotation build Landroidx/annotation/Nullable;
    .end annotation

    new-instance p2, Lc5/p;

    invoke-direct {p2, p1, p3, p0}, Lc5/p;-><init>(Lcom/airbnb/lottie/LottieDrawable;Lcom/airbnb/lottie/model/layer/a;Lh5/g;)V

    return-object p2
.end method

.method public b()Lg5/b;
    .locals 1

    iget-object v0, p0, Lh5/g;->b:Lg5/b;

    return-object v0
.end method

.method public c()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lh5/g;->a:Ljava/lang/String;

    return-object v0
.end method

.method public d()Lg5/b;
    .locals 1

    iget-object v0, p0, Lh5/g;->c:Lg5/b;

    return-object v0
.end method

.method public e()Lg5/n;
    .locals 1

    iget-object v0, p0, Lh5/g;->d:Lg5/n;

    return-object v0
.end method

.method public f()Z
    .locals 1

    iget-boolean v0, p0, Lh5/g;->e:Z

    return v0
.end method
