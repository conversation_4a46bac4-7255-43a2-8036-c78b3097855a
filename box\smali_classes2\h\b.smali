.class public Lh/b;
.super Lh/a;


# annotations
.annotation build Landroidx/annotation/RequiresApi;
    value = 0x12
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lh/b$a;
    }
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lh/a<",
        "Ljava/lang/String;",
        "Ljava/util/List<",
        "Landroid/net/Uri;",
        ">;>;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation


# static fields
.field public static final a:Lh/b$a;


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lh/b$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lh/b$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lh/b;->a:Lh/b$a;

    return-void
.end method
