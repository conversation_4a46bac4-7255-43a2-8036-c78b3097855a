.class public final Lh/f$c;
.super Ljava/lang/Object;

# interfaces
.implements Lh/f$f;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lh/f;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "c"
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation


# static fields
.field public static final a:Lh/f$c;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    new-instance v0, Lh/f$c;

    invoke-direct {v0}, Lh/f$c;-><init>()V

    sput-object v0, Lh/f$c;->a:Lh/f$c;

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
