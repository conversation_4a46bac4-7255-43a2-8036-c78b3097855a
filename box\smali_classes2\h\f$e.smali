.class public final Lh/f$e;
.super Ljava/lang/Object;

# interfaces
.implements Lh/f$f;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lh/f;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "e"
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation


# static fields
.field public static final a:Lh/f$e;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    new-instance v0, Lh/f$e;

    invoke-direct {v0}, Lh/f$e;-><init>()V

    sput-object v0, Lh/f$e;->a:Lh/f$e;

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
