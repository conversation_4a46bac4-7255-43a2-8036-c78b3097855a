.class public Li2/c;
.super Landroidx/media3/exoplayer/video/h;


# static fields
.field public static final J0:I


# instance fields
.field public final H0:I

.field public I0:Landroidx/media3/decoder/av1/Gav1Decoder;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public final Y:I

.field public final Z:I


# direct methods
.method static constructor <clinit>()V
    .locals 3

    const/16 v0, 0x500

    const/16 v1, 0x40

    invoke-static {v0, v1}, Le2/u0;->k(II)I

    move-result v0

    const/16 v2, 0x2d0

    invoke-static {v2, v1}, Le2/u0;->k(II)I

    move-result v1

    mul-int v0, v0, v1

    mul-int/lit16 v0, v0, 0x1800

    div-int/lit8 v0, v0, 0x2

    sput v0, Li2/c;->J0:I

    return-void
.end method

.method public constructor <init>(JLandroid/os/<PERSON>ler;Landroidx/media3/exoplayer/video/f0;I)V
    .locals 9
    .param p3    # Landroid/os/Handler;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .param p4    # Landroidx/media3/exoplayer/video/f0;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    const/4 v6, 0x0

    const/4 v7, 0x4

    const/4 v8, 0x4

    move-object v0, p0

    move-wide v1, p1

    move-object v3, p3

    move-object v4, p4

    move v5, p5

    invoke-direct/range {v0 .. v8}, Li2/c;-><init>(JLandroid/os/Handler;Landroidx/media3/exoplayer/video/f0;IIII)V

    return-void
.end method

.method public constructor <init>(JLandroid/os/Handler;Landroidx/media3/exoplayer/video/f0;IIII)V
    .locals 0
    .param p3    # Landroid/os/Handler;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .param p4    # Landroidx/media3/exoplayer/video/f0;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    invoke-direct/range {p0 .. p5}, Landroidx/media3/exoplayer/video/h;-><init>(JLandroid/os/Handler;Landroidx/media3/exoplayer/video/f0;I)V

    iput p6, p0, Li2/c;->H0:I

    iput p7, p0, Li2/c;->Y:I

    iput p8, p0, Li2/c;->Z:I

    return-void
.end method


# virtual methods
.method public N(Ljava/lang/String;Landroidx/media3/common/y;Landroidx/media3/common/y;)Landroidx/media3/exoplayer/o;
    .locals 7

    new-instance v6, Landroidx/media3/exoplayer/o;

    const/4 v4, 0x3

    const/4 v5, 0x0

    move-object v0, v6

    move-object v1, p1

    move-object v2, p2

    move-object v3, p3

    invoke-direct/range {v0 .. v5}, Landroidx/media3/exoplayer/o;-><init>(Ljava/lang/String;Landroidx/media3/common/y;Landroidx/media3/common/y;II)V

    return-object v6
.end method

.method public bridge synthetic O(Landroidx/media3/common/y;Landroidx/media3/decoder/b;)Landroidx/media3/decoder/g;
    .locals 0
    .param p2    # Landroidx/media3/decoder/b;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/decoder/DecoderException;
        }
    .end annotation

    invoke-virtual {p0, p1, p2}, Li2/c;->z0(Landroidx/media3/common/y;Landroidx/media3/decoder/b;)Landroidx/media3/decoder/av1/Gav1Decoder;

    move-result-object p1

    return-object p1
.end method

.method public final a(Landroidx/media3/common/y;)I
    .locals 2

    iget-object v0, p1, Landroidx/media3/common/y;->m:Ljava/lang/String;

    const-string v1, "video/av01"

    invoke-virtual {v1, v0}, Ljava/lang/String;->equalsIgnoreCase(Ljava/lang/String;)Z

    move-result v0

    const/4 v1, 0x0

    if-eqz v0, :cond_2

    invoke-static {}, Li2/b;->a()Z

    move-result v0

    if-nez v0, :cond_0

    goto :goto_0

    :cond_0
    iget p1, p1, Landroidx/media3/common/y;->I:I

    if-eqz p1, :cond_1

    const/4 p1, 0x2

    invoke-static {p1}, Landroidx/media3/exoplayer/x2;->a(I)I

    move-result p1

    return p1

    :cond_1
    const/4 p1, 0x4

    const/16 v0, 0x10

    invoke-static {p1, v0, v1}, Landroidx/media3/exoplayer/x2;->b(III)I

    move-result p1

    return p1

    :cond_2
    :goto_0
    invoke-static {v1}, Landroidx/media3/exoplayer/x2;->a(I)I

    move-result p1

    return p1
.end method

.method public getName()Ljava/lang/String;
    .locals 1

    const-string v0, "Libgav1VideoRenderer"

    return-object v0
.end method

.method public n0(Landroidx/media3/decoder/VideoDecoderOutputBuffer;Landroid/view/Surface;)V
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/decoder/av1/Gav1DecoderException;
        }
    .end annotation

    iget-object v0, p0, Li2/c;->I0:Landroidx/media3/decoder/av1/Gav1Decoder;

    if-eqz v0, :cond_0

    invoke-virtual {v0, p1, p2}, Landroidx/media3/decoder/av1/Gav1Decoder;->x(Landroidx/media3/decoder/VideoDecoderOutputBuffer;Landroid/view/Surface;)V

    invoke-virtual {p1}, Landroidx/media3/decoder/VideoDecoderOutputBuffer;->release()V

    return-void

    :cond_0
    new-instance p1, Landroidx/media3/decoder/av1/Gav1DecoderException;

    const-string p2, "Failed to render output buffer to surface: decoder is not initialized."

    invoke-direct {p1, p2}, Landroidx/media3/decoder/av1/Gav1DecoderException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public p0(I)V
    .locals 1

    iget-object v0, p0, Li2/c;->I0:Landroidx/media3/decoder/av1/Gav1Decoder;

    if-eqz v0, :cond_0

    invoke-virtual {v0, p1}, Landroidx/media3/decoder/av1/Gav1Decoder;->y(I)V

    :cond_0
    return-void
.end method

.method public final z0(Landroidx/media3/common/y;Landroidx/media3/decoder/b;)Landroidx/media3/decoder/av1/Gav1Decoder;
    .locals 3
    .param p2    # Landroidx/media3/decoder/b;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/decoder/av1/Gav1DecoderException;
        }
    .end annotation

    const-string p2, "createGav1Decoder"

    invoke-static {p2}, Le2/j0;->a(Ljava/lang/String;)V

    iget p1, p1, Landroidx/media3/common/y;->n:I

    const/4 p2, -0x1

    if-eq p1, p2, :cond_0

    goto :goto_0

    :cond_0
    sget p1, Li2/c;->J0:I

    :goto_0
    new-instance p2, Landroidx/media3/decoder/av1/Gav1Decoder;

    iget v0, p0, Li2/c;->Y:I

    iget v1, p0, Li2/c;->Z:I

    iget v2, p0, Li2/c;->H0:I

    invoke-direct {p2, v0, v1, p1, v2}, Landroidx/media3/decoder/av1/Gav1Decoder;-><init>(IIII)V

    iput-object p2, p0, Li2/c;->I0:Landroidx/media3/decoder/av1/Gav1Decoder;

    invoke-static {}, Le2/j0;->c()V

    return-object p2
.end method
