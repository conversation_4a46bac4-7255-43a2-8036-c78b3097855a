.class public abstract Li4/b;
.super Ljava/lang/Object;


# instance fields
.field public final a:I

.field public final b:I


# direct methods
.method public constructor <init>(II)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput p1, p0, Li4/b;->a:I

    iput p2, p0, Li4/b;->b:I

    return-void
.end method


# virtual methods
.method public abstract a(Ll4/g;)V
    .param p1    # Ll4/g;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
.end method
