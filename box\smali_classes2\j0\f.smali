.class public final Lj0/f;
.super Landroidx/compose/ui/f$c;

# interfaces
.implements Lj0/e;


# annotations
.annotation runtime Lkotlin/Metadata;
.end annotation


# instance fields
.field public n:Lkotlin/jvm/functions/Function1;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Lj0/b;",
            "Ljava/lang/Bo<PERSON>an;",
            ">;"
        }
    .end annotation
.end field

.field public o:Lkotlin/jvm/functions/Function1;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Lj0/b;",
            "Ljava/lang/Bo<PERSON>an;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "<PERSON><PERSON>lin/jvm/functions/Function1<",
            "-",
            "Lj0/b;",
            "Ljava/lang/<PERSON>olean;",
            ">;",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Lj0/b;",
            "Ljava/lang/Boolean;",
            ">;)V"
        }
    .end annotation

    invoke-direct {p0}, Landroidx/compose/ui/f$c;-><init>()V

    iput-object p1, p0, Lj0/f;->n:Lkotlin/jvm/functions/Function1;

    iput-object p2, p0, Lj0/f;->o:Lkotlin/jvm/functions/Function1;

    return-void
.end method


# virtual methods
.method public C0(Landroid/view/KeyEvent;)Z
    .locals 1

    iget-object v0, p0, Lj0/f;->n:Lkotlin/jvm/functions/Function1;

    if-eqz v0, :cond_0

    invoke-static {p1}, Lj0/b;->a(Landroid/view/KeyEvent;)Lj0/b;

    move-result-object p1

    invoke-interface {v0, p1}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/lang/Boolean;

    invoke-virtual {p1}, Ljava/lang/Boolean;->booleanValue()Z

    move-result p1

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    :goto_0
    return p1
.end method

.method public final J1(Lkotlin/jvm/functions/Function1;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Lj0/b;",
            "Ljava/lang/Boolean;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lj0/f;->n:Lkotlin/jvm/functions/Function1;

    return-void
.end method

.method public final K1(Lkotlin/jvm/functions/Function1;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Lj0/b;",
            "Ljava/lang/Boolean;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lj0/f;->o:Lkotlin/jvm/functions/Function1;

    return-void
.end method

.method public t0(Landroid/view/KeyEvent;)Z
    .locals 1

    iget-object v0, p0, Lj0/f;->o:Lkotlin/jvm/functions/Function1;

    if-eqz v0, :cond_0

    invoke-static {p1}, Lj0/b;->a(Landroid/view/KeyEvent;)Lj0/b;

    move-result-object p1

    invoke-interface {v0, p1}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/lang/Boolean;

    invoke-virtual {p1}, Ljava/lang/Boolean;->booleanValue()Z

    move-result p1

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    :goto_0
    return p1
.end method
