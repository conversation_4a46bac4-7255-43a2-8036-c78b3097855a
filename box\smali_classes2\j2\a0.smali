.class public final synthetic Lj2/a0;
.super Ljava/lang/Object;

# interfaces
.implements Le2/n$a;


# instance fields
.field public final synthetic a:Lj2/c$a;

.field public final synthetic b:Landroidx/media3/common/Metadata;


# direct methods
.method public synthetic constructor <init>(Lj2/c$a;Landroidx/media3/common/Metadata;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lj2/a0;->a:Lj2/c$a;

    iput-object p2, p0, Lj2/a0;->b:Landroidx/media3/common/Metadata;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)V
    .locals 2

    iget-object v0, p0, Lj2/a0;->a:Lj2/c$a;

    iget-object v1, p0, Lj2/a0;->b:Landroidx/media3/common/Metadata;

    check-cast p1, Lj2/c;

    invoke-static {v0, v1, p1}, Lj2/q1;->V(Lj2/c$a;Landroidx/media3/common/Metadata;Lj2/c;)V

    return-void
.end method
