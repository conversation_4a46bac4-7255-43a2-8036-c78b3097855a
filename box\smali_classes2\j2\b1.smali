.class public final synthetic Lj2/b1;
.super Ljava/lang/Object;

# interfaces
.implements Le2/n$a;


# instance fields
.field public final synthetic a:Lj2/c$a;


# direct methods
.method public synthetic constructor <init>(Lj2/c$a;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lj2/b1;->a:Lj2/c$a;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)V
    .locals 1

    iget-object v0, p0, Lj2/b1;->a:Lj2/c$a;

    check-cast p1, Lj2/c;

    invoke-static {v0, p1}, Lj2/q1;->e0(Lj2/c$a;Lj2/c;)V

    return-void
.end method
