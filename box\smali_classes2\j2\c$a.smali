.class public final Lj2/c$a;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lj2/c;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation


# instance fields
.field public final a:J

.field public final b:Landroidx/media3/common/m0;

.field public final c:I

.field public final d:Landroidx/media3/exoplayer/source/l$b;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public final e:J

.field public final f:Landroidx/media3/common/m0;

.field public final g:I

.field public final h:Landroidx/media3/exoplayer/source/l$b;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public final i:J

.field public final j:J


# direct methods
.method public constructor <init>(JLandroidx/media3/common/m0;ILandroidx/media3/exoplayer/source/l$b;JLandroidx/media3/common/m0;ILandroidx/media3/exoplayer/source/l$b;JJ)V
    .locals 0
    .param p5    # Landroidx/media3/exoplayer/source/l$b;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .param p10    # Landroidx/media3/exoplayer/source/l$b;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-wide p1, p0, Lj2/c$a;->a:J

    iput-object p3, p0, Lj2/c$a;->b:Landroidx/media3/common/m0;

    iput p4, p0, Lj2/c$a;->c:I

    iput-object p5, p0, Lj2/c$a;->d:Landroidx/media3/exoplayer/source/l$b;

    iput-wide p6, p0, Lj2/c$a;->e:J

    iput-object p8, p0, Lj2/c$a;->f:Landroidx/media3/common/m0;

    iput p9, p0, Lj2/c$a;->g:I

    iput-object p10, p0, Lj2/c$a;->h:Landroidx/media3/exoplayer/source/l$b;

    iput-wide p11, p0, Lj2/c$a;->i:J

    iput-wide p13, p0, Lj2/c$a;->j:J

    return-void
.end method


# virtual methods
.method public equals(Ljava/lang/Object;)Z
    .locals 7
    .param p1    # Ljava/lang/Object;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    const/4 v0, 0x1

    if-ne p0, p1, :cond_0

    return v0

    :cond_0
    const/4 v1, 0x0

    if-eqz p1, :cond_3

    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v2

    const-class v3, Lj2/c$a;

    if-eq v3, v2, :cond_1

    goto :goto_1

    :cond_1
    check-cast p1, Lj2/c$a;

    iget-wide v2, p0, Lj2/c$a;->a:J

    iget-wide v4, p1, Lj2/c$a;->a:J

    cmp-long v6, v2, v4

    if-nez v6, :cond_2

    iget v2, p0, Lj2/c$a;->c:I

    iget v3, p1, Lj2/c$a;->c:I

    if-ne v2, v3, :cond_2

    iget-wide v2, p0, Lj2/c$a;->e:J

    iget-wide v4, p1, Lj2/c$a;->e:J

    cmp-long v6, v2, v4

    if-nez v6, :cond_2

    iget v2, p0, Lj2/c$a;->g:I

    iget v3, p1, Lj2/c$a;->g:I

    if-ne v2, v3, :cond_2

    iget-wide v2, p0, Lj2/c$a;->i:J

    iget-wide v4, p1, Lj2/c$a;->i:J

    cmp-long v6, v2, v4

    if-nez v6, :cond_2

    iget-wide v2, p0, Lj2/c$a;->j:J

    iget-wide v4, p1, Lj2/c$a;->j:J

    cmp-long v6, v2, v4

    if-nez v6, :cond_2

    iget-object v2, p0, Lj2/c$a;->b:Landroidx/media3/common/m0;

    iget-object v3, p1, Lj2/c$a;->b:Landroidx/media3/common/m0;

    invoke-static {v2, v3}, Lcom/google/common/base/j;->a(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_2

    iget-object v2, p0, Lj2/c$a;->d:Landroidx/media3/exoplayer/source/l$b;

    iget-object v3, p1, Lj2/c$a;->d:Landroidx/media3/exoplayer/source/l$b;

    invoke-static {v2, v3}, Lcom/google/common/base/j;->a(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_2

    iget-object v2, p0, Lj2/c$a;->f:Landroidx/media3/common/m0;

    iget-object v3, p1, Lj2/c$a;->f:Landroidx/media3/common/m0;

    invoke-static {v2, v3}, Lcom/google/common/base/j;->a(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_2

    iget-object v2, p0, Lj2/c$a;->h:Landroidx/media3/exoplayer/source/l$b;

    iget-object p1, p1, Lj2/c$a;->h:Landroidx/media3/exoplayer/source/l$b;

    invoke-static {v2, p1}, Lcom/google/common/base/j;->a(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_2

    goto :goto_0

    :cond_2
    const/4 v0, 0x0

    :goto_0
    return v0

    :cond_3
    :goto_1
    return v1
.end method

.method public hashCode()I
    .locals 3

    const/16 v0, 0xa

    new-array v0, v0, [Ljava/lang/Object;

    iget-wide v1, p0, Lj2/c$a;->a:J

    invoke-static {v1, v2}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    move-result-object v1

    const/4 v2, 0x0

    aput-object v1, v0, v2

    const/4 v1, 0x1

    iget-object v2, p0, Lj2/c$a;->b:Landroidx/media3/common/m0;

    aput-object v2, v0, v1

    iget v1, p0, Lj2/c$a;->c:I

    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    const/4 v2, 0x2

    aput-object v1, v0, v2

    const/4 v1, 0x3

    iget-object v2, p0, Lj2/c$a;->d:Landroidx/media3/exoplayer/source/l$b;

    aput-object v2, v0, v1

    iget-wide v1, p0, Lj2/c$a;->e:J

    invoke-static {v1, v2}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    move-result-object v1

    const/4 v2, 0x4

    aput-object v1, v0, v2

    const/4 v1, 0x5

    iget-object v2, p0, Lj2/c$a;->f:Landroidx/media3/common/m0;

    aput-object v2, v0, v1

    iget v1, p0, Lj2/c$a;->g:I

    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    const/4 v2, 0x6

    aput-object v1, v0, v2

    const/4 v1, 0x7

    iget-object v2, p0, Lj2/c$a;->h:Landroidx/media3/exoplayer/source/l$b;

    aput-object v2, v0, v1

    iget-wide v1, p0, Lj2/c$a;->i:J

    invoke-static {v1, v2}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    move-result-object v1

    const/16 v2, 0x8

    aput-object v1, v0, v2

    iget-wide v1, p0, Lj2/c$a;->j:J

    invoke-static {v1, v2}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    move-result-object v1

    const/16 v2, 0x9

    aput-object v1, v0, v2

    invoke-static {v0}, Lcom/google/common/base/j;->b([Ljava/lang/Object;)I

    move-result v0

    return v0
.end method
