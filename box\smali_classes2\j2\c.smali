.class public interface abstract Lj2/c;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lj2/c$a;,
        Lj2/c$b;
    }
.end annotation


# virtual methods
.method public abstract A(Lj2/c$a;IJJ)V
.end method

.method public abstract B(Lj2/c$a;ZI)V
.end method

.method public abstract C(Lj2/c$a;IJ)V
.end method

.method public abstract D(Lj2/c$a;)V
.end method

.method public abstract E(Lj2/c$a;Landroidx/media3/exoplayer/n;)V
.end method

.method public abstract F(Lj2/c$a;)V
.end method

.method public abstract G(Lj2/c$a;Landroidx/media3/common/h0$e;Landroidx/media3/common/h0$e;I)V
.end method

.method public abstract H(Lj2/c$a;Z)V
.end method

.method public abstract I(Lj2/c$a;Ljava/util/List;)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lj2/c$a;",
            "Ljava/util/List<",
            "Ld2/a;",
            ">;)V"
        }
    .end annotation

    .annotation runtime Ljava/lang/Deprecated;
    .end annotation
.end method

.method public abstract J(Lj2/c$a;Landroidx/media3/common/PlaybackException;)V
.end method

.method public abstract K(Lj2/c$a;Lu2/n;Lu2/o;)V
.end method

.method public abstract L(Lj2/c$a;Landroidx/media3/common/y;)V
    .annotation runtime Ljava/lang/Deprecated;
    .end annotation
.end method

.method public abstract M(Lj2/c$a;Landroidx/media3/common/d0;)V
.end method

.method public abstract N(Lj2/c$a;I)V
.end method

.method public abstract O(Lj2/c$a;Landroidx/media3/exoplayer/n;)V
.end method

.method public abstract P(Lj2/c$a;)V
    .annotation runtime Ljava/lang/Deprecated;
    .end annotation
.end method

.method public abstract Q(Lj2/c$a;IZ)V
.end method

.method public abstract R(Lj2/c$a;Landroidx/media3/common/p0;)V
.end method

.method public abstract S(Lj2/c$a;I)V
.end method

.method public abstract U(Lj2/c$a;Landroidx/media3/exoplayer/audio/AudioSink$a;)V
.end method

.method public abstract V(Lj2/c$a;Ljava/lang/Object;J)V
.end method

.method public abstract W(Lj2/c$a;Ld2/b;)V
.end method

.method public abstract X(Lj2/c$a;Ljava/lang/Exception;)V
.end method

.method public abstract Y(Lj2/c$a;I)V
.end method

.method public abstract Z(Lj2/c$a;Landroidx/media3/common/g0;)V
.end method

.method public abstract a(Lj2/c$a;Ljava/lang/String;JJ)V
.end method

.method public abstract a0(Lj2/c$a;Ljava/lang/String;)V
.end method

.method public abstract b(Lj2/c$a;Landroidx/media3/common/q0;)V
.end method

.method public abstract b0(Lj2/c$a;II)V
.end method

.method public abstract c(Lj2/c$a;Landroidx/media3/common/y;Landroidx/media3/exoplayer/o;)V
    .param p3    # Landroidx/media3/exoplayer/o;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
.end method

.method public abstract c0(Lj2/c$a;Z)V
.end method

.method public abstract d(Lj2/c$a;Landroidx/media3/common/h0$b;)V
.end method

.method public abstract d0(Lj2/c$a;Landroidx/media3/common/o;)V
.end method

.method public abstract e(Lj2/c$a;Z)V
.end method

.method public abstract e0(Lj2/c$a;F)V
.end method

.method public abstract f(Lj2/c$a;ZI)V
    .annotation runtime Ljava/lang/Deprecated;
    .end annotation
.end method

.method public abstract f0(Lj2/c$a;I)V
.end method

.method public abstract g(Lj2/c$a;Ljava/lang/String;J)V
    .annotation runtime Ljava/lang/Deprecated;
    .end annotation
.end method

.method public abstract g0(Lj2/c$a;Landroidx/media3/common/y;)V
    .annotation runtime Ljava/lang/Deprecated;
    .end annotation
.end method

.method public abstract h(Lj2/c$a;Landroidx/media3/common/t0;)V
.end method

.method public abstract h0(Lj2/c$a;Z)V
    .annotation runtime Ljava/lang/Deprecated;
    .end annotation
.end method

.method public abstract i(Lj2/c$a;Landroidx/media3/common/y;Landroidx/media3/exoplayer/o;)V
    .param p3    # Landroidx/media3/exoplayer/o;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
.end method

.method public abstract i0(Lj2/c$a;Lu2/n;Lu2/o;)V
.end method

.method public abstract j(Lj2/c$a;Ljava/lang/Exception;)V
.end method

.method public abstract k(Lj2/c$a;Ljava/lang/String;JJ)V
.end method

.method public abstract k0(Lj2/c$a;Landroidx/media3/exoplayer/audio/AudioSink$a;)V
.end method

.method public abstract l(Lj2/c$a;Ljava/lang/Exception;)V
.end method

.method public abstract l0(Lj2/c$a;Ljava/lang/String;)V
.end method

.method public abstract m(Lj2/c$a;JI)V
.end method

.method public abstract m0(Lj2/c$a;Z)V
.end method

.method public abstract n(Lj2/c$a;Landroidx/media3/common/b0;I)V
    .param p2    # Landroidx/media3/common/b0;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
.end method

.method public abstract n0(Lj2/c$a;Landroidx/media3/exoplayer/n;)V
.end method

.method public abstract o(Lj2/c$a;I)V
.end method

.method public abstract p(Lj2/c$a;)V
.end method

.method public abstract p0(Lj2/c$a;)V
    .annotation runtime Ljava/lang/Deprecated;
    .end annotation
.end method

.method public abstract q(Lj2/c$a;Lu2/o;)V
.end method

.method public abstract q0(Lj2/c$a;Ljava/lang/String;J)V
    .annotation runtime Ljava/lang/Deprecated;
    .end annotation
.end method

.method public abstract r(Lj2/c$a;Lu2/n;Lu2/o;)V
.end method

.method public abstract r0(Lj2/c$a;Lu2/o;)V
.end method

.method public abstract s(Lj2/c$a;)V
.end method

.method public abstract s0(Lj2/c$a;Landroidx/media3/common/Metadata;)V
.end method

.method public abstract t(Lj2/c$a;Lu2/n;Lu2/o;Ljava/io/IOException;Z)V
.end method

.method public abstract t0(Lj2/c$a;Landroidx/media3/common/PlaybackException;)V
    .param p2    # Landroidx/media3/common/PlaybackException;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
.end method

.method public abstract u(Lj2/c$a;J)V
.end method

.method public abstract u0(Lj2/c$a;Ljava/lang/Exception;)V
.end method

.method public abstract v0(Lj2/c$a;Landroidx/media3/exoplayer/n;)V
.end method

.method public abstract w(Lj2/c$a;IJJ)V
.end method

.method public abstract w0(Lj2/c$a;)V
.end method

.method public abstract x(Landroidx/media3/common/h0;Lj2/c$b;)V
.end method

.method public abstract y(Lj2/c$a;I)V
    .annotation runtime Ljava/lang/Deprecated;
    .end annotation
.end method

.method public abstract z(Lj2/c$a;IIIF)V
    .annotation runtime Ljava/lang/Deprecated;
    .end annotation
.end method
