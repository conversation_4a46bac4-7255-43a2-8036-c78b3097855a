.class public final synthetic Lj2/d;
.super Ljava/lang/Object;

# interfaces
.implements Le2/n$a;


# instance fields
.field public final synthetic a:Lj2/c$a;

.field public final synthetic b:J

.field public final synthetic c:I


# direct methods
.method public synthetic constructor <init>(Lj2/c$a;JI)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lj2/d;->a:Lj2/c$a;

    iput-wide p2, p0, Lj2/d;->b:J

    iput p4, p0, Lj2/d;->c:I

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)V
    .locals 4

    iget-object v0, p0, Lj2/d;->a:Lj2/c$a;

    iget-wide v1, p0, Lj2/d;->b:J

    iget v3, p0, Lj2/d;->c:I

    check-cast p1, Lj2/c;

    invoke-static {v0, v1, v2, v3, p1}, Lj2/q1;->m0(Lj2/c$a;JILj2/c;)V

    return-void
.end method
