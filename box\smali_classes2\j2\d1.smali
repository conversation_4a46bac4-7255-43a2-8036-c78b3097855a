.class public final synthetic Lj2/d1;
.super Ljava/lang/Object;

# interfaces
.implements Le2/n$a;


# instance fields
.field public final synthetic a:Lj2/c$a;

.field public final synthetic b:Ljava/lang/Object;

.field public final synthetic c:J


# direct methods
.method public synthetic constructor <init>(Lj2/c$a;Ljava/lang/Object;J)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lj2/d1;->a:Lj2/c$a;

    iput-object p2, p0, Lj2/d1;->b:Ljava/lang/Object;

    iput-wide p3, p0, Lj2/d1;->c:J

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)V
    .locals 4

    iget-object v0, p0, Lj2/d1;->a:Lj2/c$a;

    iget-object v1, p0, Lj2/d1;->b:Ljava/lang/Object;

    iget-wide v2, p0, Lj2/d1;->c:J

    check-cast p1, Lj2/c;

    invoke-static {v0, v1, v2, v3, p1}, Lj2/q1;->j0(Lj2/c$a;Ljava/lang/Object;JLj2/c;)V

    return-void
.end method
