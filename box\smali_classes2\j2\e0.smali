.class public final synthetic Lj2/e0;
.super Ljava/lang/Object;

# interfaces
.implements Le2/n$a;


# instance fields
.field public final synthetic a:Lj2/c$a;

.field public final synthetic b:Z

.field public final synthetic c:I


# direct methods
.method public synthetic constructor <init>(Lj2/c$a;ZI)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lj2/e0;->a:Lj2/c$a;

    iput-boolean p2, p0, Lj2/e0;->b:Z

    iput p3, p0, Lj2/e0;->c:I

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)V
    .locals 3

    iget-object v0, p0, Lj2/e0;->a:Lj2/c$a;

    iget-boolean v1, p0, Lj2/e0;->b:Z

    iget v2, p0, Lj2/e0;->c:I

    check-cast p1, Lj2/c;

    invoke-static {v0, v1, v2, p1}, Lj2/q1;->i0(Lj2/c$a;ZILj2/c;)V

    return-void
.end method
