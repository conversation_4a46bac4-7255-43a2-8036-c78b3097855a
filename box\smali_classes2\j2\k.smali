.class public final synthetic Lj2/k;
.super Ljava/lang/Object;

# interfaces
.implements Le2/n$a;


# instance fields
.field public final synthetic a:Lj2/c$a;

.field public final synthetic b:I

.field public final synthetic c:Landroidx/media3/common/h0$e;

.field public final synthetic d:Landroidx/media3/common/h0$e;


# direct methods
.method public synthetic constructor <init>(Lj2/c$a;ILandroidx/media3/common/h0$e;Landroidx/media3/common/h0$e;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lj2/k;->a:Lj2/c$a;

    iput p2, p0, Lj2/k;->b:I

    iput-object p3, p0, Lj2/k;->c:Landroidx/media3/common/h0$e;

    iput-object p4, p0, Lj2/k;->d:Landroidx/media3/common/h0$e;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)V
    .locals 4

    iget-object v0, p0, Lj2/k;->a:Lj2/c$a;

    iget v1, p0, Lj2/k;->b:I

    iget-object v2, p0, Lj2/k;->c:Landroidx/media3/common/h0$e;

    iget-object v3, p0, Lj2/k;->d:Landroidx/media3/common/h0$e;

    check-cast p1, Lj2/c;

    invoke-static {v0, v1, v2, v3, p1}, Lj2/q1;->O(Lj2/c$a;ILandroidx/media3/common/h0$e;Landroidx/media3/common/h0$e;Lj2/c;)V

    return-void
.end method
