.class public final synthetic Lj2/n0;
.super Ljava/lang/Object;

# interfaces
.implements Le2/n$a;


# instance fields
.field public final synthetic a:Lj2/c$a;

.field public final synthetic b:Landroidx/media3/common/y;

.field public final synthetic c:Landroidx/media3/exoplayer/o;


# direct methods
.method public synthetic constructor <init>(Lj2/c$a;Landroidx/media3/common/y;Landroidx/media3/exoplayer/o;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lj2/n0;->a:Lj2/c$a;

    iput-object p2, p0, Lj2/n0;->b:Landroidx/media3/common/y;

    iput-object p3, p0, Lj2/n0;->c:Landroidx/media3/exoplayer/o;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)V
    .locals 3

    iget-object v0, p0, Lj2/n0;->a:Lj2/c$a;

    iget-object v1, p0, Lj2/n0;->b:Landroidx/media3/common/y;

    iget-object v2, p0, Lj2/n0;->c:Landroidx/media3/exoplayer/o;

    check-cast p1, Lj2/c;

    invoke-static {v0, v1, v2, p1}, Lj2/q1;->g0(Lj2/c$a;Landroidx/media3/common/y;Landroidx/media3/exoplayer/o;Lj2/c;)V

    return-void
.end method
