.class public final synthetic Lj2/o;
.super Ljava/lang/Object;

# interfaces
.implements Le2/n$a;


# instance fields
.field public final synthetic a:Lj2/c$a;

.field public final synthetic b:Lu2/n;

.field public final synthetic c:Lu2/o;

.field public final synthetic d:Ljava/io/IOException;

.field public final synthetic e:Z


# direct methods
.method public synthetic constructor <init>(Lj2/c$a;Lu2/n;Lu2/o;Ljava/io/IOException;Z)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lj2/o;->a:Lj2/c$a;

    iput-object p2, p0, Lj2/o;->b:Lu2/n;

    iput-object p3, p0, Lj2/o;->c:Lu2/o;

    iput-object p4, p0, Lj2/o;->d:Ljava/io/IOException;

    iput-boolean p5, p0, Lj2/o;->e:Z

    return-void
.end method


# virtual methods
.method public final invoke(Lja<PERSON>/lang/Object;)V
    .locals 6

    iget-object v0, p0, Lj2/o;->a:Lj2/c$a;

    iget-object v1, p0, Lj2/o;->b:Lu2/n;

    iget-object v2, p0, Lj2/o;->c:Lu2/o;

    iget-object v3, p0, Lj2/o;->d:Ljava/io/IOException;

    iget-boolean v4, p0, Lj2/o;->e:Z

    move-object v5, p1

    check-cast v5, Lj2/c;

    invoke-static/range {v0 .. v5}, Lj2/q1;->Y(Lj2/c$a;Lu2/n;Lu2/o;Ljava/io/IOException;ZLj2/c;)V

    return-void
.end method
