.class public final synthetic Lj2/p0;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/lang/Runnable;


# instance fields
.field public final synthetic a:Lj2/q1;


# direct methods
.method public synthetic constructor <init>(Lj2/q1;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lj2/p0;->a:Lj2/q1;

    return-void
.end method


# virtual methods
.method public final run()V
    .locals 1

    iget-object v0, p0, Lj2/p0;->a:Lj2/q1;

    invoke-static {v0}, Lj2/q1;->T(Lj2/q1;)V

    return-void
.end method
