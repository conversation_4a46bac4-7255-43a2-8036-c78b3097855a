.class public final synthetic Lj2/q0;
.super Ljava/lang/Object;

# interfaces
.implements Le2/n$a;


# instance fields
.field public final synthetic a:Lj2/c$a;

.field public final synthetic b:J


# direct methods
.method public synthetic constructor <init>(Lj2/c$a;J)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lj2/q0;->a:Lj2/c$a;

    iput-wide p2, p0, Lj2/q0;->b:J

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)V
    .locals 3

    iget-object v0, p0, Lj2/q0;->a:Lj2/c$a;

    iget-wide v1, p0, Lj2/q0;->b:J

    check-cast p1, Lj2/c;

    invoke-static {v0, v1, v2, p1}, Lj2/q1;->h0(Lj2/c$a;JLj2/c;)V

    return-void
.end method
