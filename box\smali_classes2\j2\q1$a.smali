.class public final Lj2/q1$a;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lj2/q1;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation


# instance fields
.field public final a:Landroidx/media3/common/m0$b;

.field public b:Lcom/google/common/collect/ImmutableList;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/google/common/collect/ImmutableList<",
            "Landroidx/media3/exoplayer/source/l$b;",
            ">;"
        }
    .end annotation
.end field

.field public c:Lcom/google/common/collect/ImmutableMap;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/google/common/collect/ImmutableMap<",
            "Landroidx/media3/exoplayer/source/l$b;",
            "Landroidx/media3/common/m0;",
            ">;"
        }
    .end annotation
.end field

.field public d:Landroidx/media3/exoplayer/source/l$b;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public e:Landroidx/media3/exoplayer/source/l$b;

.field public f:Landroidx/media3/exoplayer/source/l$b;


# direct methods
.method public constructor <init>(Landroidx/media3/common/m0$b;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lj2/q1$a;->a:Landroidx/media3/common/m0$b;

    invoke-static {}, Lcom/google/common/collect/ImmutableList;->of()Lcom/google/common/collect/ImmutableList;

    move-result-object p1

    iput-object p1, p0, Lj2/q1$a;->b:Lcom/google/common/collect/ImmutableList;

    invoke-static {}, Lcom/google/common/collect/ImmutableMap;->of()Lcom/google/common/collect/ImmutableMap;

    move-result-object p1

    iput-object p1, p0, Lj2/q1$a;->c:Lcom/google/common/collect/ImmutableMap;

    return-void
.end method

.method public static synthetic a(Lj2/q1$a;)Lcom/google/common/collect/ImmutableList;
    .locals 0

    iget-object p0, p0, Lj2/q1$a;->b:Lcom/google/common/collect/ImmutableList;

    return-object p0
.end method

.method public static c(Landroidx/media3/common/h0;Lcom/google/common/collect/ImmutableList;Landroidx/media3/exoplayer/source/l$b;Landroidx/media3/common/m0$b;)Landroidx/media3/exoplayer/source/l$b;
    .locals 10
    .param p2    # Landroidx/media3/exoplayer/source/l$b;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .annotation build Landroidx/annotation/Nullable;
    .end annotation

    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroidx/media3/common/h0;",
            "Lcom/google/common/collect/ImmutableList<",
            "Landroidx/media3/exoplayer/source/l$b;",
            ">;",
            "Landroidx/media3/exoplayer/source/l$b;",
            "Landroidx/media3/common/m0$b;",
            ")",
            "Landroidx/media3/exoplayer/source/l$b;"
        }
    .end annotation

    invoke-interface {p0}, Landroidx/media3/common/h0;->getCurrentTimeline()Landroidx/media3/common/m0;

    move-result-object v0

    invoke-interface {p0}, Landroidx/media3/common/h0;->getCurrentPeriodIndex()I

    move-result v1

    invoke-virtual {v0}, Landroidx/media3/common/m0;->q()Z

    move-result v2

    const/4 v3, 0x0

    if-eqz v2, :cond_0

    move-object v2, v3

    goto :goto_0

    :cond_0
    invoke-virtual {v0, v1}, Landroidx/media3/common/m0;->m(I)Ljava/lang/Object;

    move-result-object v2

    :goto_0
    invoke-interface {p0}, Landroidx/media3/common/h0;->isPlayingAd()Z

    move-result v4

    if-nez v4, :cond_2

    invoke-virtual {v0}, Landroidx/media3/common/m0;->q()Z

    move-result v4

    if-eqz v4, :cond_1

    goto :goto_1

    :cond_1
    invoke-virtual {v0, v1, p3}, Landroidx/media3/common/m0;->f(ILandroidx/media3/common/m0$b;)Landroidx/media3/common/m0$b;

    move-result-object v0

    invoke-interface {p0}, Landroidx/media3/common/h0;->getCurrentPosition()J

    move-result-wide v4

    invoke-static {v4, v5}, Le2/u0;->S0(J)J

    move-result-wide v4

    invoke-virtual {p3}, Landroidx/media3/common/m0$b;->o()J

    move-result-wide v6

    sub-long/2addr v4, v6

    invoke-virtual {v0, v4, v5}, Landroidx/media3/common/m0$b;->d(J)I

    move-result p3

    goto :goto_2

    :cond_2
    :goto_1
    const/4 p3, -0x1

    :goto_2
    const/4 v0, 0x0

    :goto_3
    invoke-virtual {p1}, Ljava/util/AbstractCollection;->size()I

    move-result v1

    if-ge v0, v1, :cond_4

    invoke-interface {p1, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Landroidx/media3/exoplayer/source/l$b;

    invoke-interface {p0}, Landroidx/media3/common/h0;->isPlayingAd()Z

    move-result v6

    invoke-interface {p0}, Landroidx/media3/common/h0;->getCurrentAdGroupIndex()I

    move-result v7

    invoke-interface {p0}, Landroidx/media3/common/h0;->getCurrentAdIndexInAdGroup()I

    move-result v8

    move-object v4, v1

    move-object v5, v2

    move v9, p3

    invoke-static/range {v4 .. v9}, Lj2/q1$a;->i(Landroidx/media3/exoplayer/source/l$b;Ljava/lang/Object;ZIII)Z

    move-result v4

    if-eqz v4, :cond_3

    return-object v1

    :cond_3
    add-int/lit8 v0, v0, 0x1

    goto :goto_3

    :cond_4
    invoke-virtual {p1}, Ljava/util/AbstractCollection;->isEmpty()Z

    move-result p1

    if-eqz p1, :cond_5

    if-eqz p2, :cond_5

    invoke-interface {p0}, Landroidx/media3/common/h0;->isPlayingAd()Z

    move-result v6

    invoke-interface {p0}, Landroidx/media3/common/h0;->getCurrentAdGroupIndex()I

    move-result v7

    invoke-interface {p0}, Landroidx/media3/common/h0;->getCurrentAdIndexInAdGroup()I

    move-result v8

    move-object v4, p2

    move-object v5, v2

    move v9, p3

    invoke-static/range {v4 .. v9}, Lj2/q1$a;->i(Landroidx/media3/exoplayer/source/l$b;Ljava/lang/Object;ZIII)Z

    move-result p0

    if-eqz p0, :cond_5

    return-object p2

    :cond_5
    return-object v3
.end method

.method public static i(Landroidx/media3/exoplayer/source/l$b;Ljava/lang/Object;ZIII)Z
    .locals 1
    .param p1    # Ljava/lang/Object;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    iget-object v0, p0, Landroidx/media3/exoplayer/source/l$b;->a:Ljava/lang/Object;

    invoke-virtual {v0, p1}, Ljava/lang/Object;->equals(Ljava/lang/Object;)Z

    move-result p1

    const/4 v0, 0x0

    if-nez p1, :cond_0

    return v0

    :cond_0
    if-eqz p2, :cond_1

    iget p1, p0, Landroidx/media3/exoplayer/source/l$b;->b:I

    if-ne p1, p3, :cond_1

    iget p1, p0, Landroidx/media3/exoplayer/source/l$b;->c:I

    if-eq p1, p4, :cond_2

    :cond_1
    if-nez p2, :cond_3

    iget p1, p0, Landroidx/media3/exoplayer/source/l$b;->b:I

    const/4 p2, -0x1

    if-ne p1, p2, :cond_3

    iget p0, p0, Landroidx/media3/exoplayer/source/l$b;->e:I

    if-ne p0, p5, :cond_3

    :cond_2
    const/4 v0, 0x1

    :cond_3
    return v0
.end method


# virtual methods
.method public final b(Lcom/google/common/collect/ImmutableMap$b;Landroidx/media3/exoplayer/source/l$b;Landroidx/media3/common/m0;)V
    .locals 2
    .param p2    # Landroidx/media3/exoplayer/source/l$b;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/google/common/collect/ImmutableMap$b<",
            "Landroidx/media3/exoplayer/source/l$b;",
            "Landroidx/media3/common/m0;",
            ">;",
            "Landroidx/media3/exoplayer/source/l$b;",
            "Landroidx/media3/common/m0;",
            ")V"
        }
    .end annotation

    if-nez p2, :cond_0

    return-void

    :cond_0
    iget-object v0, p2, Landroidx/media3/exoplayer/source/l$b;->a:Ljava/lang/Object;

    invoke-virtual {p3, v0}, Landroidx/media3/common/m0;->b(Ljava/lang/Object;)I

    move-result v0

    const/4 v1, -0x1

    if-eq v0, v1, :cond_1

    invoke-virtual {p1, p2, p3}, Lcom/google/common/collect/ImmutableMap$b;->g(Ljava/lang/Object;Ljava/lang/Object;)Lcom/google/common/collect/ImmutableMap$b;

    goto :goto_0

    :cond_1
    iget-object p3, p0, Lj2/q1$a;->c:Lcom/google/common/collect/ImmutableMap;

    invoke-virtual {p3, p2}, Lcom/google/common/collect/ImmutableMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p3

    check-cast p3, Landroidx/media3/common/m0;

    if-eqz p3, :cond_2

    invoke-virtual {p1, p2, p3}, Lcom/google/common/collect/ImmutableMap$b;->g(Ljava/lang/Object;Ljava/lang/Object;)Lcom/google/common/collect/ImmutableMap$b;

    :cond_2
    :goto_0
    return-void
.end method

.method public d()Landroidx/media3/exoplayer/source/l$b;
    .locals 1
    .annotation build Landroidx/annotation/Nullable;
    .end annotation

    iget-object v0, p0, Lj2/q1$a;->d:Landroidx/media3/exoplayer/source/l$b;

    return-object v0
.end method

.method public e()Landroidx/media3/exoplayer/source/l$b;
    .locals 1
    .annotation build Landroidx/annotation/Nullable;
    .end annotation

    iget-object v0, p0, Lj2/q1$a;->b:Lcom/google/common/collect/ImmutableList;

    invoke-virtual {v0}, Ljava/util/AbstractCollection;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_0

    const/4 v0, 0x0

    goto :goto_0

    :cond_0
    iget-object v0, p0, Lj2/q1$a;->b:Lcom/google/common/collect/ImmutableList;

    invoke-static {v0}, Lcom/google/common/collect/e0;->g(Ljava/lang/Iterable;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroidx/media3/exoplayer/source/l$b;

    :goto_0
    return-object v0
.end method

.method public f(Landroidx/media3/exoplayer/source/l$b;)Landroidx/media3/common/m0;
    .locals 1
    .annotation build Landroidx/annotation/Nullable;
    .end annotation

    iget-object v0, p0, Lj2/q1$a;->c:Lcom/google/common/collect/ImmutableMap;

    invoke-virtual {v0, p1}, Lcom/google/common/collect/ImmutableMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Landroidx/media3/common/m0;

    return-object p1
.end method

.method public g()Landroidx/media3/exoplayer/source/l$b;
    .locals 1
    .annotation build Landroidx/annotation/Nullable;
    .end annotation

    iget-object v0, p0, Lj2/q1$a;->e:Landroidx/media3/exoplayer/source/l$b;

    return-object v0
.end method

.method public h()Landroidx/media3/exoplayer/source/l$b;
    .locals 1
    .annotation build Landroidx/annotation/Nullable;
    .end annotation

    iget-object v0, p0, Lj2/q1$a;->f:Landroidx/media3/exoplayer/source/l$b;

    return-object v0
.end method

.method public j(Landroidx/media3/common/h0;)V
    .locals 3

    iget-object v0, p0, Lj2/q1$a;->b:Lcom/google/common/collect/ImmutableList;

    iget-object v1, p0, Lj2/q1$a;->e:Landroidx/media3/exoplayer/source/l$b;

    iget-object v2, p0, Lj2/q1$a;->a:Landroidx/media3/common/m0$b;

    invoke-static {p1, v0, v1, v2}, Lj2/q1$a;->c(Landroidx/media3/common/h0;Lcom/google/common/collect/ImmutableList;Landroidx/media3/exoplayer/source/l$b;Landroidx/media3/common/m0$b;)Landroidx/media3/exoplayer/source/l$b;

    move-result-object p1

    iput-object p1, p0, Lj2/q1$a;->d:Landroidx/media3/exoplayer/source/l$b;

    return-void
.end method

.method public k(Ljava/util/List;Landroidx/media3/exoplayer/source/l$b;Landroidx/media3/common/h0;)V
    .locals 1
    .param p2    # Landroidx/media3/exoplayer/source/l$b;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Landroidx/media3/exoplayer/source/l$b;",
            ">;",
            "Landroidx/media3/exoplayer/source/l$b;",
            "Landroidx/media3/common/h0;",
            ")V"
        }
    .end annotation

    invoke-static {p1}, Lcom/google/common/collect/ImmutableList;->copyOf(Ljava/util/Collection;)Lcom/google/common/collect/ImmutableList;

    move-result-object v0

    iput-object v0, p0, Lj2/q1$a;->b:Lcom/google/common/collect/ImmutableList;

    invoke-interface {p1}, Ljava/util/List;->isEmpty()Z

    move-result v0

    if-nez v0, :cond_0

    const/4 v0, 0x0

    invoke-interface {p1, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Landroidx/media3/exoplayer/source/l$b;

    iput-object p1, p0, Lj2/q1$a;->e:Landroidx/media3/exoplayer/source/l$b;

    invoke-static {p2}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Landroidx/media3/exoplayer/source/l$b;

    iput-object p1, p0, Lj2/q1$a;->f:Landroidx/media3/exoplayer/source/l$b;

    :cond_0
    iget-object p1, p0, Lj2/q1$a;->d:Landroidx/media3/exoplayer/source/l$b;

    if-nez p1, :cond_1

    iget-object p1, p0, Lj2/q1$a;->b:Lcom/google/common/collect/ImmutableList;

    iget-object p2, p0, Lj2/q1$a;->e:Landroidx/media3/exoplayer/source/l$b;

    iget-object v0, p0, Lj2/q1$a;->a:Landroidx/media3/common/m0$b;

    invoke-static {p3, p1, p2, v0}, Lj2/q1$a;->c(Landroidx/media3/common/h0;Lcom/google/common/collect/ImmutableList;Landroidx/media3/exoplayer/source/l$b;Landroidx/media3/common/m0$b;)Landroidx/media3/exoplayer/source/l$b;

    move-result-object p1

    iput-object p1, p0, Lj2/q1$a;->d:Landroidx/media3/exoplayer/source/l$b;

    :cond_1
    invoke-interface {p3}, Landroidx/media3/common/h0;->getCurrentTimeline()Landroidx/media3/common/m0;

    move-result-object p1

    invoke-virtual {p0, p1}, Lj2/q1$a;->m(Landroidx/media3/common/m0;)V

    return-void
.end method

.method public l(Landroidx/media3/common/h0;)V
    .locals 3

    iget-object v0, p0, Lj2/q1$a;->b:Lcom/google/common/collect/ImmutableList;

    iget-object v1, p0, Lj2/q1$a;->e:Landroidx/media3/exoplayer/source/l$b;

    iget-object v2, p0, Lj2/q1$a;->a:Landroidx/media3/common/m0$b;

    invoke-static {p1, v0, v1, v2}, Lj2/q1$a;->c(Landroidx/media3/common/h0;Lcom/google/common/collect/ImmutableList;Landroidx/media3/exoplayer/source/l$b;Landroidx/media3/common/m0$b;)Landroidx/media3/exoplayer/source/l$b;

    move-result-object v0

    iput-object v0, p0, Lj2/q1$a;->d:Landroidx/media3/exoplayer/source/l$b;

    invoke-interface {p1}, Landroidx/media3/common/h0;->getCurrentTimeline()Landroidx/media3/common/m0;

    move-result-object p1

    invoke-virtual {p0, p1}, Lj2/q1$a;->m(Landroidx/media3/common/m0;)V

    return-void
.end method

.method public final m(Landroidx/media3/common/m0;)V
    .locals 3

    invoke-static {}, Lcom/google/common/collect/ImmutableMap;->builder()Lcom/google/common/collect/ImmutableMap$b;

    move-result-object v0

    iget-object v1, p0, Lj2/q1$a;->b:Lcom/google/common/collect/ImmutableList;

    invoke-virtual {v1}, Ljava/util/AbstractCollection;->isEmpty()Z

    move-result v1

    if-eqz v1, :cond_1

    iget-object v1, p0, Lj2/q1$a;->e:Landroidx/media3/exoplayer/source/l$b;

    invoke-virtual {p0, v0, v1, p1}, Lj2/q1$a;->b(Lcom/google/common/collect/ImmutableMap$b;Landroidx/media3/exoplayer/source/l$b;Landroidx/media3/common/m0;)V

    iget-object v1, p0, Lj2/q1$a;->f:Landroidx/media3/exoplayer/source/l$b;

    iget-object v2, p0, Lj2/q1$a;->e:Landroidx/media3/exoplayer/source/l$b;

    invoke-static {v1, v2}, Lcom/google/common/base/j;->a(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_0

    iget-object v1, p0, Lj2/q1$a;->f:Landroidx/media3/exoplayer/source/l$b;

    invoke-virtual {p0, v0, v1, p1}, Lj2/q1$a;->b(Lcom/google/common/collect/ImmutableMap$b;Landroidx/media3/exoplayer/source/l$b;Landroidx/media3/common/m0;)V

    :cond_0
    iget-object v1, p0, Lj2/q1$a;->d:Landroidx/media3/exoplayer/source/l$b;

    iget-object v2, p0, Lj2/q1$a;->e:Landroidx/media3/exoplayer/source/l$b;

    invoke-static {v1, v2}, Lcom/google/common/base/j;->a(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_3

    iget-object v1, p0, Lj2/q1$a;->d:Landroidx/media3/exoplayer/source/l$b;

    iget-object v2, p0, Lj2/q1$a;->f:Landroidx/media3/exoplayer/source/l$b;

    invoke-static {v1, v2}, Lcom/google/common/base/j;->a(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_3

    iget-object v1, p0, Lj2/q1$a;->d:Landroidx/media3/exoplayer/source/l$b;

    invoke-virtual {p0, v0, v1, p1}, Lj2/q1$a;->b(Lcom/google/common/collect/ImmutableMap$b;Landroidx/media3/exoplayer/source/l$b;Landroidx/media3/common/m0;)V

    goto :goto_1

    :cond_1
    const/4 v1, 0x0

    :goto_0
    iget-object v2, p0, Lj2/q1$a;->b:Lcom/google/common/collect/ImmutableList;

    invoke-virtual {v2}, Ljava/util/AbstractCollection;->size()I

    move-result v2

    if-ge v1, v2, :cond_2

    iget-object v2, p0, Lj2/q1$a;->b:Lcom/google/common/collect/ImmutableList;

    invoke-interface {v2, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Landroidx/media3/exoplayer/source/l$b;

    invoke-virtual {p0, v0, v2, p1}, Lj2/q1$a;->b(Lcom/google/common/collect/ImmutableMap$b;Landroidx/media3/exoplayer/source/l$b;Landroidx/media3/common/m0;)V

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_2
    iget-object v1, p0, Lj2/q1$a;->b:Lcom/google/common/collect/ImmutableList;

    iget-object v2, p0, Lj2/q1$a;->d:Landroidx/media3/exoplayer/source/l$b;

    invoke-virtual {v1, v2}, Lcom/google/common/collect/ImmutableList;->contains(Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_3

    iget-object v1, p0, Lj2/q1$a;->d:Landroidx/media3/exoplayer/source/l$b;

    invoke-virtual {p0, v0, v1, p1}, Lj2/q1$a;->b(Lcom/google/common/collect/ImmutableMap$b;Landroidx/media3/exoplayer/source/l$b;Landroidx/media3/common/m0;)V

    :cond_3
    :goto_1
    invoke-virtual {v0}, Lcom/google/common/collect/ImmutableMap$b;->d()Lcom/google/common/collect/ImmutableMap;

    move-result-object p1

    iput-object p1, p0, Lj2/q1$a;->c:Lcom/google/common/collect/ImmutableMap;

    return-void
.end method
