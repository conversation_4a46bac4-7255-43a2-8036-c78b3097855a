.class public Lj2/q1;
.super Ljava/lang/Object;

# interfaces
.implements Lj2/a;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lj2/q1$a;
    }
.end annotation


# instance fields
.field public final a:Le2/d;

.field public final b:Landroidx/media3/common/m0$b;

.field public final c:Landroidx/media3/common/m0$c;

.field public final d:Lj2/q1$a;

.field public final e:Landroid/util/SparseArray;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Landroid/util/SparseArray<",
            "Lj2/c$a;",
            ">;"
        }
    .end annotation
.end field

.field public f:Le2/n;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Le2/n<",
            "Lj2/c;",
            ">;"
        }
    .end annotation
.end field

.field public g:Landroidx/media3/common/h0;

.field public h:Le2/j;

.field public i:Z


# direct methods
.method public constructor <init>(Le2/d;)V
    .locals 3

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    invoke-static {p1}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Le2/d;

    iput-object v0, p0, Lj2/q1;->a:Le2/d;

    new-instance v0, Le2/n;

    invoke-static {}, Le2/u0;->V()Landroid/os/Looper;

    move-result-object v1

    new-instance v2, Lj2/f;

    invoke-direct {v2}, Lj2/f;-><init>()V

    invoke-direct {v0, v1, p1, v2}, Le2/n;-><init>(Landroid/os/Looper;Le2/d;Le2/n$b;)V

    iput-object v0, p0, Lj2/q1;->f:Le2/n;

    new-instance p1, Landroidx/media3/common/m0$b;

    invoke-direct {p1}, Landroidx/media3/common/m0$b;-><init>()V

    iput-object p1, p0, Lj2/q1;->b:Landroidx/media3/common/m0$b;

    new-instance v0, Landroidx/media3/common/m0$c;

    invoke-direct {v0}, Landroidx/media3/common/m0$c;-><init>()V

    iput-object v0, p0, Lj2/q1;->c:Landroidx/media3/common/m0$c;

    new-instance v0, Lj2/q1$a;

    invoke-direct {v0, p1}, Lj2/q1$a;-><init>(Landroidx/media3/common/m0$b;)V

    iput-object v0, p0, Lj2/q1;->d:Lj2/q1$a;

    new-instance p1, Landroid/util/SparseArray;

    invoke-direct {p1}, Landroid/util/SparseArray;-><init>()V

    iput-object p1, p0, Lj2/q1;->e:Landroid/util/SparseArray;

    return-void
.end method

.method public static synthetic A0(Lj2/c$a;Landroidx/media3/exoplayer/n;Lj2/c;)V
    .locals 0

    invoke-static {p0, p1, p2}, Lj2/q1;->j1(Lj2/c$a;Landroidx/media3/exoplayer/n;Lj2/c;)V

    return-void
.end method

.method public static synthetic A1(Lj2/c$a;ILj2/c;)V
    .locals 0

    invoke-interface {p2, p0}, Lj2/c;->p0(Lj2/c$a;)V

    invoke-interface {p2, p0, p1}, Lj2/c;->o(Lj2/c$a;I)V

    return-void
.end method

.method public static synthetic B0(Lj2/c$a;Landroidx/media3/exoplayer/n;Lj2/c;)V
    .locals 0

    invoke-static {p0, p1, p2}, Lj2/q1;->i1(Lj2/c$a;Landroidx/media3/exoplayer/n;Lj2/c;)V

    return-void
.end method

.method public static synthetic B1(Lj2/c$a;Ljava/lang/Exception;Lj2/c;)V
    .locals 0

    invoke-interface {p2, p0, p1}, Lj2/c;->j(Lj2/c$a;Ljava/lang/Exception;)V

    return-void
.end method

.method public static synthetic C0(Lj2/q1;Landroidx/media3/common/h0;Lj2/c;Landroidx/media3/common/s;)V
    .locals 0

    invoke-virtual {p0, p1, p2, p3}, Lj2/q1;->o2(Landroidx/media3/common/h0;Lj2/c;Landroidx/media3/common/s;)V

    return-void
.end method

.method public static synthetic C1(Lj2/c$a;Lj2/c;)V
    .locals 0

    invoke-interface {p1, p0}, Lj2/c;->F(Lj2/c$a;)V

    return-void
.end method

.method public static synthetic D0(Lj2/c$a;ZILj2/c;)V
    .locals 0

    invoke-static {p0, p1, p2, p3}, Lj2/q1;->T1(Lj2/c$a;ZILj2/c;)V

    return-void
.end method

.method public static synthetic D1(Lj2/c$a;IJLj2/c;)V
    .locals 0

    invoke-interface {p4, p0, p1, p2, p3}, Lj2/c;->C(Lj2/c$a;IJ)V

    return-void
.end method

.method public static synthetic E0(Lj2/c$a;Ld2/b;Lj2/c;)V
    .locals 0

    invoke-static {p0, p1, p2}, Lj2/q1;->t1(Lj2/c$a;Ld2/b;Lj2/c;)V

    return-void
.end method

.method public static synthetic E1(Lj2/c$a;ZLj2/c;)V
    .locals 0

    invoke-interface {p2, p0, p1}, Lj2/c;->h0(Lj2/c$a;Z)V

    invoke-interface {p2, p0, p1}, Lj2/c;->m0(Lj2/c$a;Z)V

    return-void
.end method

.method public static synthetic F0(Lj2/c$a;ILj2/c;)V
    .locals 0

    invoke-static {p0, p1, p2}, Lj2/q1;->W1(Lj2/c$a;ILj2/c;)V

    return-void
.end method

.method public static synthetic F1(Lj2/c$a;ZLj2/c;)V
    .locals 0

    invoke-interface {p2, p0, p1}, Lj2/c;->e(Lj2/c$a;Z)V

    return-void
.end method

.method public static synthetic G0(Lj2/c$a;Landroidx/media3/exoplayer/n;Lj2/c;)V
    .locals 0

    invoke-static {p0, p1, p2}, Lj2/q1;->h2(Lj2/c$a;Landroidx/media3/exoplayer/n;Lj2/c;)V

    return-void
.end method

.method public static synthetic G1(Lj2/c$a;Lu2/n;Lu2/o;Lj2/c;)V
    .locals 0

    invoke-interface {p3, p0, p1, p2}, Lj2/c;->r(Lj2/c$a;Lu2/n;Lu2/o;)V

    return-void
.end method

.method public static synthetic H(Lj2/c$a;ILj2/c;)V
    .locals 0

    invoke-static {p0, p1, p2}, Lj2/q1;->a2(Lj2/c$a;ILj2/c;)V

    return-void
.end method

.method public static synthetic H0(Lj2/c$a;Ljava/lang/String;Lj2/c;)V
    .locals 0

    invoke-static {p0, p1, p2}, Lj2/q1;->h1(Lj2/c$a;Ljava/lang/String;Lj2/c;)V

    return-void
.end method

.method public static synthetic H1(Lj2/c$a;Lu2/n;Lu2/o;Lj2/c;)V
    .locals 0

    invoke-interface {p3, p0, p1, p2}, Lj2/c;->K(Lj2/c$a;Lu2/n;Lu2/o;)V

    return-void
.end method

.method public static synthetic I0(Lj2/c$a;Ljava/lang/Exception;Lj2/c;)V
    .locals 0

    invoke-static {p0, p1, p2}, Lj2/q1;->B1(Lj2/c$a;Ljava/lang/Exception;Lj2/c;)V

    return-void
.end method

.method public static synthetic I1(Lj2/c$a;Lu2/n;Lu2/o;Ljava/io/IOException;ZLj2/c;)V
    .locals 6

    move-object v0, p5

    move-object v1, p0

    move-object v2, p1

    move-object v3, p2

    move-object v4, p3

    move v5, p4

    invoke-interface/range {v0 .. v5}, Lj2/c;->t(Lj2/c$a;Lu2/n;Lu2/o;Ljava/io/IOException;Z)V

    return-void
.end method

.method public static synthetic J(Lj2/c$a;ZLj2/c;)V
    .locals 0

    invoke-static {p0, p1, p2}, Lj2/q1;->E1(Lj2/c$a;ZLj2/c;)V

    return-void
.end method

.method public static synthetic J0(Lj2/c$a;Ljava/lang/String;JJLj2/c;)V
    .locals 0

    invoke-static/range {p0 .. p6}, Lj2/q1;->f2(Lj2/c$a;Ljava/lang/String;JJLj2/c;)V

    return-void
.end method

.method public static synthetic J1(Lj2/c$a;Lu2/n;Lu2/o;Lj2/c;)V
    .locals 0

    invoke-interface {p3, p0, p1, p2}, Lj2/c;->i0(Lj2/c$a;Lu2/n;Lu2/o;)V

    return-void
.end method

.method public static synthetic K(Lj2/c$a;ZLj2/c;)V
    .locals 0

    invoke-static {p0, p1, p2}, Lj2/q1;->X1(Lj2/c$a;ZLj2/c;)V

    return-void
.end method

.method public static synthetic K0(Lj2/c$a;Ljava/lang/String;Lj2/c;)V
    .locals 0

    invoke-static {p0, p1, p2}, Lj2/q1;->g2(Lj2/c$a;Ljava/lang/String;Lj2/c;)V

    return-void
.end method

.method public static synthetic K1(Lj2/c$a;Landroidx/media3/common/b0;ILj2/c;)V
    .locals 0

    invoke-interface {p3, p0, p1, p2}, Lj2/c;->n(Lj2/c$a;Landroidx/media3/common/b0;I)V

    return-void
.end method

.method public static synthetic L(Lj2/c$a;IJLj2/c;)V
    .locals 0

    invoke-static {p0, p1, p2, p3, p4}, Lj2/q1;->D1(Lj2/c$a;IJLj2/c;)V

    return-void
.end method

.method public static synthetic L0(Lj2/c$a;ILj2/c;)V
    .locals 0

    invoke-static {p0, p1, p2}, Lj2/q1;->A1(Lj2/c$a;ILj2/c;)V

    return-void
.end method

.method public static synthetic L1(Lj2/c$a;Landroidx/media3/common/d0;Lj2/c;)V
    .locals 0

    invoke-interface {p2, p0, p1}, Lj2/c;->M(Lj2/c$a;Landroidx/media3/common/d0;)V

    return-void
.end method

.method public static synthetic M(Lj2/c$a;Lu2/o;Lj2/c;)V
    .locals 0

    invoke-static {p0, p1, p2}, Lj2/q1;->d2(Lj2/c$a;Lu2/o;Lj2/c;)V

    return-void
.end method

.method public static synthetic M0(Lj2/c$a;Lu2/o;Lj2/c;)V
    .locals 0

    invoke-static {p0, p1, p2}, Lj2/q1;->w1(Lj2/c$a;Lu2/o;Lj2/c;)V

    return-void
.end method

.method public static synthetic M1(Lj2/c$a;Landroidx/media3/common/Metadata;Lj2/c;)V
    .locals 0

    invoke-interface {p2, p0, p1}, Lj2/c;->s0(Lj2/c$a;Landroidx/media3/common/Metadata;)V

    return-void
.end method

.method public static synthetic N(Lj2/c$a;Lj2/c;)V
    .locals 0

    invoke-static {p0, p1}, Lj2/q1;->y1(Lj2/c$a;Lj2/c;)V

    return-void
.end method

.method public static synthetic N0(Lj2/c$a;Landroidx/media3/common/o;Lj2/c;)V
    .locals 0

    invoke-static {p0, p1, p2}, Lj2/q1;->u1(Lj2/c$a;Landroidx/media3/common/o;Lj2/c;)V

    return-void
.end method

.method public static synthetic N1(Lj2/c$a;ZILj2/c;)V
    .locals 0

    invoke-interface {p3, p0, p1, p2}, Lj2/c;->B(Lj2/c$a;ZI)V

    return-void
.end method

.method public static synthetic O(Lj2/c$a;ILandroidx/media3/common/h0$e;Landroidx/media3/common/h0$e;Lj2/c;)V
    .locals 0

    invoke-static {p0, p1, p2, p3, p4}, Lj2/q1;->U1(Lj2/c$a;ILandroidx/media3/common/h0$e;Landroidx/media3/common/h0$e;Lj2/c;)V

    return-void
.end method

.method public static synthetic O0(Lj2/c$a;ILj2/c;)V
    .locals 0

    invoke-static {p0, p1, p2}, Lj2/q1;->P1(Lj2/c$a;ILj2/c;)V

    return-void
.end method

.method public static synthetic O1(Lj2/c$a;Landroidx/media3/common/g0;Lj2/c;)V
    .locals 0

    invoke-interface {p2, p0, p1}, Lj2/c;->Z(Lj2/c$a;Landroidx/media3/common/g0;)V

    return-void
.end method

.method public static synthetic P(Lj2/c$a;Ljava/lang/Exception;Lj2/c;)V
    .locals 0

    invoke-static {p0, p1, p2}, Lj2/q1;->f1(Lj2/c$a;Ljava/lang/Exception;Lj2/c;)V

    return-void
.end method

.method public static synthetic P0(Lj2/c$a;IJJLj2/c;)V
    .locals 0

    invoke-static/range {p0 .. p6}, Lj2/q1;->p1(Lj2/c$a;IJJLj2/c;)V

    return-void
.end method

.method public static synthetic P1(Lj2/c$a;ILj2/c;)V
    .locals 0

    invoke-interface {p2, p0, p1}, Lj2/c;->Y(Lj2/c$a;I)V

    return-void
.end method

.method public static synthetic Q(Lj2/c$a;Landroidx/media3/common/q0;Lj2/c;)V
    .locals 0

    invoke-static {p0, p1, p2}, Lj2/q1;->c2(Lj2/c$a;Landroidx/media3/common/q0;Lj2/c;)V

    return-void
.end method

.method public static synthetic Q0(Lj2/c$a;Ljava/lang/Exception;Lj2/c;)V
    .locals 0

    invoke-static {p0, p1, p2}, Lj2/q1;->e2(Lj2/c$a;Ljava/lang/Exception;Lj2/c;)V

    return-void
.end method

.method public static synthetic Q1(Lj2/c$a;ILj2/c;)V
    .locals 0

    invoke-interface {p2, p0, p1}, Lj2/c;->f0(Lj2/c$a;I)V

    return-void
.end method

.method public static synthetic R(Lj2/c$a;Ljava/util/List;Lj2/c;)V
    .locals 0

    invoke-static {p0, p1, p2}, Lj2/q1;->s1(Lj2/c$a;Ljava/util/List;Lj2/c;)V

    return-void
.end method

.method public static synthetic R0(Lj2/c$a;Landroidx/media3/exoplayer/audio/AudioSink$a;Lj2/c;)V
    .locals 0

    invoke-static {p0, p1, p2}, Lj2/q1;->o1(Lj2/c$a;Landroidx/media3/exoplayer/audio/AudioSink$a;Lj2/c;)V

    return-void
.end method

.method public static synthetic R1(Lj2/c$a;Landroidx/media3/common/PlaybackException;Lj2/c;)V
    .locals 0

    invoke-interface {p2, p0, p1}, Lj2/c;->J(Lj2/c$a;Landroidx/media3/common/PlaybackException;)V

    return-void
.end method

.method public static synthetic S(Lj2/c$a;Landroidx/media3/common/t0;Lj2/c;)V
    .locals 0

    invoke-static {p0, p1, p2}, Lj2/q1;->l2(Lj2/c$a;Landroidx/media3/common/t0;Lj2/c;)V

    return-void
.end method

.method public static synthetic S0(Lj2/c;Landroidx/media3/common/s;)V
    .locals 0

    invoke-static {p0, p1}, Lj2/q1;->d1(Lj2/c;Landroidx/media3/common/s;)V

    return-void
.end method

.method public static synthetic S1(Lj2/c$a;Landroidx/media3/common/PlaybackException;Lj2/c;)V
    .locals 0

    invoke-interface {p2, p0, p1}, Lj2/c;->t0(Lj2/c$a;Landroidx/media3/common/PlaybackException;)V

    return-void
.end method

.method public static synthetic T(Lj2/q1;)V
    .locals 0

    invoke-virtual {p0}, Lj2/q1;->p2()V

    return-void
.end method

.method public static synthetic T0(Lj2/c$a;Landroidx/media3/common/b0;ILj2/c;)V
    .locals 0

    invoke-static {p0, p1, p2, p3}, Lj2/q1;->K1(Lj2/c$a;Landroidx/media3/common/b0;ILj2/c;)V

    return-void
.end method

.method public static synthetic T1(Lj2/c$a;ZILj2/c;)V
    .locals 0

    invoke-interface {p3, p0, p1, p2}, Lj2/c;->f(Lj2/c$a;ZI)V

    return-void
.end method

.method public static synthetic U(Lj2/c$a;ZLj2/c;)V
    .locals 0

    invoke-static {p0, p1, p2}, Lj2/q1;->Y1(Lj2/c$a;ZLj2/c;)V

    return-void
.end method

.method public static synthetic U0(Lj2/c$a;Lj2/c;)V
    .locals 0

    invoke-static {p0, p1}, Lj2/q1;->e1(Lj2/c$a;Lj2/c;)V

    return-void
.end method

.method public static synthetic U1(Lj2/c$a;ILandroidx/media3/common/h0$e;Landroidx/media3/common/h0$e;Lj2/c;)V
    .locals 0

    invoke-interface {p4, p0, p1}, Lj2/c;->y(Lj2/c$a;I)V

    invoke-interface {p4, p0, p2, p3, p1}, Lj2/c;->G(Lj2/c$a;Landroidx/media3/common/h0$e;Landroidx/media3/common/h0$e;I)V

    return-void
.end method

.method public static synthetic V(Lj2/c$a;Landroidx/media3/common/Metadata;Lj2/c;)V
    .locals 0

    invoke-static {p0, p1, p2}, Lj2/q1;->M1(Lj2/c$a;Landroidx/media3/common/Metadata;Lj2/c;)V

    return-void
.end method

.method public static synthetic V1(Lj2/c$a;Ljava/lang/Object;JLj2/c;)V
    .locals 0

    invoke-interface {p4, p0, p1, p2, p3}, Lj2/c;->V(Lj2/c$a;Ljava/lang/Object;J)V

    return-void
.end method

.method public static synthetic W(Lj2/c$a;ZLj2/c;)V
    .locals 0

    invoke-static {p0, p1, p2}, Lj2/q1;->F1(Lj2/c$a;ZLj2/c;)V

    return-void
.end method

.method public static synthetic W1(Lj2/c$a;ILj2/c;)V
    .locals 0

    invoke-interface {p2, p0, p1}, Lj2/c;->S(Lj2/c$a;I)V

    return-void
.end method

.method public static synthetic X(Lj2/c$a;Landroidx/media3/exoplayer/n;Lj2/c;)V
    .locals 0

    invoke-static {p0, p1, p2}, Lj2/q1;->i2(Lj2/c$a;Landroidx/media3/exoplayer/n;Lj2/c;)V

    return-void
.end method

.method public static synthetic X1(Lj2/c$a;ZLj2/c;)V
    .locals 0

    invoke-interface {p2, p0, p1}, Lj2/c;->c0(Lj2/c$a;Z)V

    return-void
.end method

.method public static synthetic Y(Lj2/c$a;Lu2/n;Lu2/o;Ljava/io/IOException;ZLj2/c;)V
    .locals 0

    invoke-static/range {p0 .. p5}, Lj2/q1;->I1(Lj2/c$a;Lu2/n;Lu2/o;Ljava/io/IOException;ZLj2/c;)V

    return-void
.end method

.method public static synthetic Y1(Lj2/c$a;ZLj2/c;)V
    .locals 0

    invoke-interface {p2, p0, p1}, Lj2/c;->H(Lj2/c$a;Z)V

    return-void
.end method

.method public static synthetic Z(Lj2/c$a;Landroidx/media3/common/d0;Lj2/c;)V
    .locals 0

    invoke-static {p0, p1, p2}, Lj2/q1;->L1(Lj2/c$a;Landroidx/media3/common/d0;Lj2/c;)V

    return-void
.end method

.method public static synthetic Z1(Lj2/c$a;IILj2/c;)V
    .locals 0

    invoke-interface {p3, p0, p1, p2}, Lj2/c;->b0(Lj2/c$a;II)V

    return-void
.end method

.method public static synthetic a0(Lj2/c$a;Lu2/n;Lu2/o;Lj2/c;)V
    .locals 0

    invoke-static {p0, p1, p2, p3}, Lj2/q1;->G1(Lj2/c$a;Lu2/n;Lu2/o;Lj2/c;)V

    return-void
.end method

.method public static synthetic a2(Lj2/c$a;ILj2/c;)V
    .locals 0

    invoke-interface {p2, p0, p1}, Lj2/c;->N(Lj2/c$a;I)V

    return-void
.end method

.method public static synthetic b0(Lj2/c$a;Landroidx/media3/common/y;Landroidx/media3/exoplayer/o;Lj2/c;)V
    .locals 0

    invoke-static {p0, p1, p2, p3}, Lj2/q1;->k2(Lj2/c$a;Landroidx/media3/common/y;Landroidx/media3/exoplayer/o;Lj2/c;)V

    return-void
.end method

.method public static synthetic b2(Lj2/c$a;Landroidx/media3/common/p0;Lj2/c;)V
    .locals 0

    invoke-interface {p2, p0, p1}, Lj2/c;->R(Lj2/c$a;Landroidx/media3/common/p0;)V

    return-void
.end method

.method public static synthetic c0(Lj2/c$a;Lu2/n;Lu2/o;Lj2/c;)V
    .locals 0

    invoke-static {p0, p1, p2, p3}, Lj2/q1;->J1(Lj2/c$a;Lu2/n;Lu2/o;Lj2/c;)V

    return-void
.end method

.method public static synthetic c2(Lj2/c$a;Landroidx/media3/common/q0;Lj2/c;)V
    .locals 0

    invoke-interface {p2, p0, p1}, Lj2/c;->b(Lj2/c$a;Landroidx/media3/common/q0;)V

    return-void
.end method

.method public static synthetic d0(Lj2/c$a;Lj2/c;)V
    .locals 0

    invoke-static {p0, p1}, Lj2/q1;->z1(Lj2/c$a;Lj2/c;)V

    return-void
.end method

.method public static synthetic d1(Lj2/c;Landroidx/media3/common/s;)V
    .locals 0

    return-void
.end method

.method public static synthetic d2(Lj2/c$a;Lu2/o;Lj2/c;)V
    .locals 0

    invoke-interface {p2, p0, p1}, Lj2/c;->r0(Lj2/c$a;Lu2/o;)V

    return-void
.end method

.method public static synthetic e0(Lj2/c$a;Lj2/c;)V
    .locals 0

    invoke-static {p0, p1}, Lj2/q1;->n2(Lj2/c$a;Lj2/c;)V

    return-void
.end method

.method public static synthetic e1(Lj2/c$a;Lj2/c;)V
    .locals 0

    invoke-interface {p1, p0}, Lj2/c;->P(Lj2/c$a;)V

    return-void
.end method

.method public static synthetic e2(Lj2/c$a;Ljava/lang/Exception;Lj2/c;)V
    .locals 0

    invoke-interface {p2, p0, p1}, Lj2/c;->X(Lj2/c$a;Ljava/lang/Exception;)V

    return-void
.end method

.method public static synthetic f0(Lj2/c$a;Landroidx/media3/exoplayer/audio/AudioSink$a;Lj2/c;)V
    .locals 0

    invoke-static {p0, p1, p2}, Lj2/q1;->n1(Lj2/c$a;Landroidx/media3/exoplayer/audio/AudioSink$a;Lj2/c;)V

    return-void
.end method

.method public static synthetic f1(Lj2/c$a;Ljava/lang/Exception;Lj2/c;)V
    .locals 0

    invoke-interface {p2, p0, p1}, Lj2/c;->u0(Lj2/c$a;Ljava/lang/Exception;)V

    return-void
.end method

.method public static synthetic f2(Lj2/c$a;Ljava/lang/String;JJLj2/c;)V
    .locals 7

    invoke-interface {p6, p0, p1, p2, p3}, Lj2/c;->q0(Lj2/c$a;Ljava/lang/String;J)V

    move-object v0, p6

    move-object v1, p0

    move-object v2, p1

    move-wide v3, p4

    move-wide v5, p2

    invoke-interface/range {v0 .. v6}, Lj2/c;->a(Lj2/c$a;Ljava/lang/String;JJ)V

    return-void
.end method

.method public static synthetic g0(Lj2/c$a;Landroidx/media3/common/y;Landroidx/media3/exoplayer/o;Lj2/c;)V
    .locals 0

    invoke-static {p0, p1, p2, p3}, Lj2/q1;->k1(Lj2/c$a;Landroidx/media3/common/y;Landroidx/media3/exoplayer/o;Lj2/c;)V

    return-void
.end method

.method public static synthetic g1(Lj2/c$a;Ljava/lang/String;JJLj2/c;)V
    .locals 7

    invoke-interface {p6, p0, p1, p2, p3}, Lj2/c;->g(Lj2/c$a;Ljava/lang/String;J)V

    move-object v0, p6

    move-object v1, p0

    move-object v2, p1

    move-wide v3, p4

    move-wide v5, p2

    invoke-interface/range {v0 .. v6}, Lj2/c;->k(Lj2/c$a;Ljava/lang/String;JJ)V

    return-void
.end method

.method public static synthetic g2(Lj2/c$a;Ljava/lang/String;Lj2/c;)V
    .locals 0

    invoke-interface {p2, p0, p1}, Lj2/c;->l0(Lj2/c$a;Ljava/lang/String;)V

    return-void
.end method

.method public static synthetic h0(Lj2/c$a;JLj2/c;)V
    .locals 0

    invoke-static {p0, p1, p2, p3}, Lj2/q1;->l1(Lj2/c$a;JLj2/c;)V

    return-void
.end method

.method public static synthetic h1(Lj2/c$a;Ljava/lang/String;Lj2/c;)V
    .locals 0

    invoke-interface {p2, p0, p1}, Lj2/c;->a0(Lj2/c$a;Ljava/lang/String;)V

    return-void
.end method

.method public static synthetic h2(Lj2/c$a;Landroidx/media3/exoplayer/n;Lj2/c;)V
    .locals 0

    invoke-interface {p2, p0, p1}, Lj2/c;->v0(Lj2/c$a;Landroidx/media3/exoplayer/n;)V

    return-void
.end method

.method public static synthetic i0(Lj2/c$a;ZILj2/c;)V
    .locals 0

    invoke-static {p0, p1, p2, p3}, Lj2/q1;->N1(Lj2/c$a;ZILj2/c;)V

    return-void
.end method

.method public static synthetic i1(Lj2/c$a;Landroidx/media3/exoplayer/n;Lj2/c;)V
    .locals 0

    invoke-interface {p2, p0, p1}, Lj2/c;->E(Lj2/c$a;Landroidx/media3/exoplayer/n;)V

    return-void
.end method

.method public static synthetic i2(Lj2/c$a;Landroidx/media3/exoplayer/n;Lj2/c;)V
    .locals 0

    invoke-interface {p2, p0, p1}, Lj2/c;->O(Lj2/c$a;Landroidx/media3/exoplayer/n;)V

    return-void
.end method

.method public static synthetic j0(Lj2/c$a;Ljava/lang/Object;JLj2/c;)V
    .locals 0

    invoke-static {p0, p1, p2, p3, p4}, Lj2/q1;->V1(Lj2/c$a;Ljava/lang/Object;JLj2/c;)V

    return-void
.end method

.method public static synthetic j1(Lj2/c$a;Landroidx/media3/exoplayer/n;Lj2/c;)V
    .locals 0

    invoke-interface {p2, p0, p1}, Lj2/c;->n0(Lj2/c$a;Landroidx/media3/exoplayer/n;)V

    return-void
.end method

.method public static synthetic j2(Lj2/c$a;JILj2/c;)V
    .locals 0

    invoke-interface {p4, p0, p1, p2, p3}, Lj2/c;->m(Lj2/c$a;JI)V

    return-void
.end method

.method public static synthetic k0(Lj2/c$a;ILj2/c;)V
    .locals 0

    invoke-static {p0, p1, p2}, Lj2/q1;->Q1(Lj2/c$a;ILj2/c;)V

    return-void
.end method

.method public static synthetic k1(Lj2/c$a;Landroidx/media3/common/y;Landroidx/media3/exoplayer/o;Lj2/c;)V
    .locals 0

    invoke-interface {p3, p0, p1}, Lj2/c;->g0(Lj2/c$a;Landroidx/media3/common/y;)V

    invoke-interface {p3, p0, p1, p2}, Lj2/c;->c(Lj2/c$a;Landroidx/media3/common/y;Landroidx/media3/exoplayer/o;)V

    return-void
.end method

.method public static synthetic k2(Lj2/c$a;Landroidx/media3/common/y;Landroidx/media3/exoplayer/o;Lj2/c;)V
    .locals 0

    invoke-interface {p3, p0, p1}, Lj2/c;->L(Lj2/c$a;Landroidx/media3/common/y;)V

    invoke-interface {p3, p0, p1, p2}, Lj2/c;->i(Lj2/c$a;Landroidx/media3/common/y;Landroidx/media3/exoplayer/o;)V

    return-void
.end method

.method public static synthetic l0(Lj2/c$a;Lj2/c;)V
    .locals 0

    invoke-static {p0, p1}, Lj2/q1;->x1(Lj2/c$a;Lj2/c;)V

    return-void
.end method

.method public static synthetic l1(Lj2/c$a;JLj2/c;)V
    .locals 0

    invoke-interface {p3, p0, p1, p2}, Lj2/c;->u(Lj2/c$a;J)V

    return-void
.end method

.method public static synthetic l2(Lj2/c$a;Landroidx/media3/common/t0;Lj2/c;)V
    .locals 6

    invoke-interface {p2, p0, p1}, Lj2/c;->h(Lj2/c$a;Landroidx/media3/common/t0;)V

    iget v2, p1, Landroidx/media3/common/t0;->a:I

    iget v3, p1, Landroidx/media3/common/t0;->b:I

    iget v4, p1, Landroidx/media3/common/t0;->c:I

    iget v5, p1, Landroidx/media3/common/t0;->d:F

    move-object v0, p2

    move-object v1, p0

    invoke-interface/range {v0 .. v5}, Lj2/c;->z(Lj2/c$a;IIIF)V

    return-void
.end method

.method public static synthetic m0(Lj2/c$a;JILj2/c;)V
    .locals 0

    invoke-static {p0, p1, p2, p3, p4}, Lj2/q1;->j2(Lj2/c$a;JILj2/c;)V

    return-void
.end method

.method public static synthetic m1(Lj2/c$a;Ljava/lang/Exception;Lj2/c;)V
    .locals 0

    invoke-interface {p2, p0, p1}, Lj2/c;->l(Lj2/c$a;Ljava/lang/Exception;)V

    return-void
.end method

.method public static synthetic m2(Lj2/c$a;FLj2/c;)V
    .locals 0

    invoke-interface {p2, p0, p1}, Lj2/c;->e0(Lj2/c$a;F)V

    return-void
.end method

.method public static synthetic n0(Lj2/c$a;Lu2/n;Lu2/o;Lj2/c;)V
    .locals 0

    invoke-static {p0, p1, p2, p3}, Lj2/q1;->H1(Lj2/c$a;Lu2/n;Lu2/o;Lj2/c;)V

    return-void
.end method

.method public static synthetic n1(Lj2/c$a;Landroidx/media3/exoplayer/audio/AudioSink$a;Lj2/c;)V
    .locals 0

    invoke-interface {p2, p0, p1}, Lj2/c;->k0(Lj2/c$a;Landroidx/media3/exoplayer/audio/AudioSink$a;)V

    return-void
.end method

.method public static synthetic n2(Lj2/c$a;Lj2/c;)V
    .locals 0

    invoke-interface {p1, p0}, Lj2/c;->p(Lj2/c$a;)V

    return-void
.end method

.method public static synthetic o0(Lj2/c$a;Landroidx/media3/common/h0$b;Lj2/c;)V
    .locals 0

    invoke-static {p0, p1, p2}, Lj2/q1;->q1(Lj2/c$a;Landroidx/media3/common/h0$b;Lj2/c;)V

    return-void
.end method

.method public static synthetic o1(Lj2/c$a;Landroidx/media3/exoplayer/audio/AudioSink$a;Lj2/c;)V
    .locals 0

    invoke-interface {p2, p0, p1}, Lj2/c;->U(Lj2/c$a;Landroidx/media3/exoplayer/audio/AudioSink$a;)V

    return-void
.end method

.method public static synthetic p0(Lj2/c$a;Lj2/c;)V
    .locals 0

    invoke-static {p0, p1}, Lj2/q1;->C1(Lj2/c$a;Lj2/c;)V

    return-void
.end method

.method public static synthetic p1(Lj2/c$a;IJJLj2/c;)V
    .locals 7

    move-object v0, p6

    move-object v1, p0

    move v2, p1

    move-wide v3, p2

    move-wide v5, p4

    invoke-interface/range {v0 .. v6}, Lj2/c;->A(Lj2/c$a;IJJ)V

    return-void
.end method

.method public static synthetic q0(Lj2/c$a;Landroidx/media3/common/PlaybackException;Lj2/c;)V
    .locals 0

    invoke-static {p0, p1, p2}, Lj2/q1;->R1(Lj2/c$a;Landroidx/media3/common/PlaybackException;Lj2/c;)V

    return-void
.end method

.method public static synthetic q1(Lj2/c$a;Landroidx/media3/common/h0$b;Lj2/c;)V
    .locals 0

    invoke-interface {p2, p0, p1}, Lj2/c;->d(Lj2/c$a;Landroidx/media3/common/h0$b;)V

    return-void
.end method

.method public static synthetic r0(Lj2/c$a;Landroidx/media3/common/p0;Lj2/c;)V
    .locals 0

    invoke-static {p0, p1, p2}, Lj2/q1;->b2(Lj2/c$a;Landroidx/media3/common/p0;Lj2/c;)V

    return-void
.end method

.method public static synthetic r1(Lj2/c$a;IJJLj2/c;)V
    .locals 7

    move-object v0, p6

    move-object v1, p0

    move v2, p1

    move-wide v3, p2

    move-wide v5, p4

    invoke-interface/range {v0 .. v6}, Lj2/c;->w(Lj2/c$a;IJJ)V

    return-void
.end method

.method public static synthetic s0(Lj2/c$a;Ljava/lang/Exception;Lj2/c;)V
    .locals 0

    invoke-static {p0, p1, p2}, Lj2/q1;->m1(Lj2/c$a;Ljava/lang/Exception;Lj2/c;)V

    return-void
.end method

.method public static synthetic s1(Lj2/c$a;Ljava/util/List;Lj2/c;)V
    .locals 0

    invoke-interface {p2, p0, p1}, Lj2/c;->I(Lj2/c$a;Ljava/util/List;)V

    return-void
.end method

.method public static synthetic t0(Lj2/c$a;FLj2/c;)V
    .locals 0

    invoke-static {p0, p1, p2}, Lj2/q1;->m2(Lj2/c$a;FLj2/c;)V

    return-void
.end method

.method public static synthetic t1(Lj2/c$a;Ld2/b;Lj2/c;)V
    .locals 0

    invoke-interface {p2, p0, p1}, Lj2/c;->W(Lj2/c$a;Ld2/b;)V

    return-void
.end method

.method public static synthetic u0(Lj2/c$a;IJJLj2/c;)V
    .locals 0

    invoke-static/range {p0 .. p6}, Lj2/q1;->r1(Lj2/c$a;IJJLj2/c;)V

    return-void
.end method

.method public static synthetic u1(Lj2/c$a;Landroidx/media3/common/o;Lj2/c;)V
    .locals 0

    invoke-interface {p2, p0, p1}, Lj2/c;->d0(Lj2/c$a;Landroidx/media3/common/o;)V

    return-void
.end method

.method public static synthetic v0(Lj2/c$a;Ljava/lang/String;JJLj2/c;)V
    .locals 0

    invoke-static/range {p0 .. p6}, Lj2/q1;->g1(Lj2/c$a;Ljava/lang/String;JJLj2/c;)V

    return-void
.end method

.method public static synthetic v1(Lj2/c$a;IZLj2/c;)V
    .locals 0

    invoke-interface {p3, p0, p1, p2}, Lj2/c;->Q(Lj2/c$a;IZ)V

    return-void
.end method

.method public static synthetic w0(Lj2/c$a;Landroidx/media3/common/PlaybackException;Lj2/c;)V
    .locals 0

    invoke-static {p0, p1, p2}, Lj2/q1;->S1(Lj2/c$a;Landroidx/media3/common/PlaybackException;Lj2/c;)V

    return-void
.end method

.method public static synthetic w1(Lj2/c$a;Lu2/o;Lj2/c;)V
    .locals 0

    invoke-interface {p2, p0, p1}, Lj2/c;->q(Lj2/c$a;Lu2/o;)V

    return-void
.end method

.method public static synthetic x0(Lj2/c$a;IILj2/c;)V
    .locals 0

    invoke-static {p0, p1, p2, p3}, Lj2/q1;->Z1(Lj2/c$a;IILj2/c;)V

    return-void
.end method

.method public static synthetic x1(Lj2/c$a;Lj2/c;)V
    .locals 0

    invoke-interface {p1, p0}, Lj2/c;->s(Lj2/c$a;)V

    return-void
.end method

.method public static synthetic y0(Lj2/c$a;Landroidx/media3/common/g0;Lj2/c;)V
    .locals 0

    invoke-static {p0, p1, p2}, Lj2/q1;->O1(Lj2/c$a;Landroidx/media3/common/g0;Lj2/c;)V

    return-void
.end method

.method public static synthetic y1(Lj2/c$a;Lj2/c;)V
    .locals 0

    invoke-interface {p1, p0}, Lj2/c;->w0(Lj2/c$a;)V

    return-void
.end method

.method public static synthetic z0(Lj2/c$a;IZLj2/c;)V
    .locals 0

    invoke-static {p0, p1, p2, p3}, Lj2/q1;->v1(Lj2/c$a;IZLj2/c;)V

    return-void
.end method

.method public static synthetic z1(Lj2/c$a;Lj2/c;)V
    .locals 0

    invoke-interface {p1, p0}, Lj2/c;->D(Lj2/c$a;)V

    return-void
.end method


# virtual methods
.method public final A(ILandroidx/media3/exoplayer/source/l$b;Lu2/n;Lu2/o;Ljava/io/IOException;Z)V
    .locals 6
    .param p2    # Landroidx/media3/exoplayer/source/l$b;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    invoke-virtual {p0, p1, p2}, Lj2/q1;->Z0(ILandroidx/media3/exoplayer/source/l$b;)Lj2/c$a;

    move-result-object p1

    new-instance p2, Lj2/o;

    move-object v0, p2

    move-object v1, p1

    move-object v2, p3

    move-object v3, p4

    move-object v4, p5

    move v5, p6

    invoke-direct/range {v0 .. v5}, Lj2/o;-><init>(Lj2/c$a;Lu2/n;Lu2/o;Ljava/io/IOException;Z)V

    const/16 p3, 0x3eb

    invoke-virtual {p0, p1, p3, p2}, Lj2/q1;->q2(Lj2/c$a;ILe2/n$a;)V

    return-void
.end method

.method public final B(ILandroidx/media3/exoplayer/source/l$b;I)V
    .locals 0
    .param p2    # Landroidx/media3/exoplayer/source/l$b;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    invoke-virtual {p0, p1, p2}, Lj2/q1;->Z0(ILandroidx/media3/exoplayer/source/l$b;)Lj2/c$a;

    move-result-object p1

    new-instance p2, Lj2/z0;

    invoke-direct {p2, p1, p3}, Lj2/z0;-><init>(Lj2/c$a;I)V

    const/16 p3, 0x3fe

    invoke-virtual {p0, p1, p3, p2}, Lj2/q1;->q2(Lj2/c$a;ILe2/n$a;)V

    return-void
.end method

.method public synthetic C(ILandroidx/media3/exoplayer/source/l$b;)V
    .locals 0

    invoke-static {p0, p1, p2}, Ln2/k;->a(Landroidx/media3/exoplayer/drm/b;ILandroidx/media3/exoplayer/source/l$b;)V

    return-void
.end method

.method public final D(ILandroidx/media3/exoplayer/source/l$b;)V
    .locals 1
    .param p2    # Landroidx/media3/exoplayer/source/l$b;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    invoke-virtual {p0, p1, p2}, Lj2/q1;->Z0(ILandroidx/media3/exoplayer/source/l$b;)Lj2/c$a;

    move-result-object p1

    new-instance p2, Lj2/l1;

    invoke-direct {p2, p1}, Lj2/l1;-><init>(Lj2/c$a;)V

    const/16 v0, 0x402

    invoke-virtual {p0, p1, v0, p2}, Lj2/q1;->q2(Lj2/c$a;ILe2/n$a;)V

    return-void
.end method

.method public final E(ILandroidx/media3/exoplayer/source/l$b;Ljava/lang/Exception;)V
    .locals 0
    .param p2    # Landroidx/media3/exoplayer/source/l$b;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    invoke-virtual {p0, p1, p2}, Lj2/q1;->Z0(ILandroidx/media3/exoplayer/source/l$b;)Lj2/c$a;

    move-result-object p1

    new-instance p2, Lj2/u0;

    invoke-direct {p2, p1, p3}, Lj2/u0;-><init>(Lj2/c$a;Ljava/lang/Exception;)V

    const/16 p3, 0x400

    invoke-virtual {p0, p1, p3, p2}, Lj2/q1;->q2(Lj2/c$a;ILe2/n$a;)V

    return-void
.end method

.method public final F(ILandroidx/media3/exoplayer/source/l$b;)V
    .locals 1
    .param p2    # Landroidx/media3/exoplayer/source/l$b;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    invoke-virtual {p0, p1, p2}, Lj2/q1;->Z0(ILandroidx/media3/exoplayer/source/l$b;)Lj2/c$a;

    move-result-object p1

    new-instance p2, Lj2/e1;

    invoke-direct {p2, p1}, Lj2/e1;-><init>(Lj2/c$a;)V

    const/16 v0, 0x401

    invoke-virtual {p0, p1, v0, p2}, Lj2/q1;->q2(Lj2/c$a;ILe2/n$a;)V

    return-void
.end method

.method public final G(ILandroidx/media3/exoplayer/source/l$b;)V
    .locals 1
    .param p2    # Landroidx/media3/exoplayer/source/l$b;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    invoke-virtual {p0, p1, p2}, Lj2/q1;->Z0(ILandroidx/media3/exoplayer/source/l$b;)Lj2/c$a;

    move-result-object p1

    new-instance p2, Lj2/y0;

    invoke-direct {p2, p1}, Lj2/y0;-><init>(Lj2/c$a;)V

    const/16 v0, 0x403

    invoke-virtual {p0, p1, v0, p2}, Lj2/q1;->q2(Lj2/c$a;ILe2/n$a;)V

    return-void
.end method

.method public I(Landroidx/media3/common/h0;Landroid/os/Looper;)V
    .locals 2
    .annotation build Landroidx/annotation/CallSuper;
    .end annotation

    iget-object v0, p0, Lj2/q1;->g:Landroidx/media3/common/h0;

    if-eqz v0, :cond_1

    iget-object v0, p0, Lj2/q1;->d:Lj2/q1$a;

    invoke-static {v0}, Lj2/q1$a;->a(Lj2/q1$a;)Lcom/google/common/collect/ImmutableList;

    move-result-object v0

    invoke-virtual {v0}, Ljava/util/AbstractCollection;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_0

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 v0, 0x1

    :goto_1
    invoke-static {v0}, Le2/a;->g(Z)V

    invoke-static {p1}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroidx/media3/common/h0;

    iput-object v0, p0, Lj2/q1;->g:Landroidx/media3/common/h0;

    iget-object v0, p0, Lj2/q1;->a:Le2/d;

    const/4 v1, 0x0

    invoke-interface {v0, p2, v1}, Le2/d;->createHandler(Landroid/os/Looper;Landroid/os/Handler$Callback;)Le2/j;

    move-result-object v0

    iput-object v0, p0, Lj2/q1;->h:Le2/j;

    iget-object v0, p0, Lj2/q1;->f:Le2/n;

    new-instance v1, Lj2/r;

    invoke-direct {v1, p0, p1}, Lj2/r;-><init>(Lj2/q1;Landroidx/media3/common/h0;)V

    invoke-virtual {v0, p2, v1}, Le2/n;->e(Landroid/os/Looper;Le2/n$b;)Le2/n;

    move-result-object p1

    iput-object p1, p0, Lj2/q1;->f:Le2/n;

    return-void
.end method

.method public final V0()Lj2/c$a;
    .locals 1

    iget-object v0, p0, Lj2/q1;->d:Lj2/q1$a;

    invoke-virtual {v0}, Lj2/q1$a;->d()Landroidx/media3/exoplayer/source/l$b;

    move-result-object v0

    invoke-virtual {p0, v0}, Lj2/q1;->X0(Landroidx/media3/exoplayer/source/l$b;)Lj2/c$a;

    move-result-object v0

    return-object v0
.end method

.method public final W0(Landroidx/media3/common/m0;ILandroidx/media3/exoplayer/source/l$b;)Lj2/c$a;
    .locals 17
    .param p3    # Landroidx/media3/exoplayer/source/l$b;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    move-object/from16 v0, p0

    move-object/from16 v4, p1

    move/from16 v5, p2

    invoke-virtual/range {p1 .. p1}, Landroidx/media3/common/m0;->q()Z

    move-result v1

    if-eqz v1, :cond_0

    const/4 v1, 0x0

    move-object v6, v1

    goto :goto_0

    :cond_0
    move-object/from16 v6, p3

    :goto_0
    iget-object v1, v0, Lj2/q1;->a:Le2/d;

    invoke-interface {v1}, Le2/d;->elapsedRealtime()J

    move-result-wide v2

    iget-object v1, v0, Lj2/q1;->g:Landroidx/media3/common/h0;

    invoke-interface {v1}, Landroidx/media3/common/h0;->getCurrentTimeline()Landroidx/media3/common/m0;

    move-result-object v1

    invoke-virtual {v4, v1}, Landroidx/media3/common/m0;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_1

    iget-object v1, v0, Lj2/q1;->g:Landroidx/media3/common/h0;

    invoke-interface {v1}, Landroidx/media3/common/h0;->u()I

    move-result v1

    if-ne v5, v1, :cond_1

    const/4 v1, 0x1

    goto :goto_1

    :cond_1
    const/4 v1, 0x0

    :goto_1
    const-wide/16 v7, 0x0

    if-eqz v6, :cond_2

    invoke-virtual {v6}, Landroidx/media3/exoplayer/source/l$b;->b()Z

    move-result v9

    if-eqz v9, :cond_2

    if-eqz v1, :cond_5

    iget-object v1, v0, Lj2/q1;->g:Landroidx/media3/common/h0;

    invoke-interface {v1}, Landroidx/media3/common/h0;->getCurrentAdGroupIndex()I

    move-result v1

    iget v9, v6, Landroidx/media3/exoplayer/source/l$b;->b:I

    if-ne v1, v9, :cond_5

    iget-object v1, v0, Lj2/q1;->g:Landroidx/media3/common/h0;

    invoke-interface {v1}, Landroidx/media3/common/h0;->getCurrentAdIndexInAdGroup()I

    move-result v1

    iget v9, v6, Landroidx/media3/exoplayer/source/l$b;->c:I

    if-ne v1, v9, :cond_5

    iget-object v1, v0, Lj2/q1;->g:Landroidx/media3/common/h0;

    invoke-interface {v1}, Landroidx/media3/common/h0;->getCurrentPosition()J

    move-result-wide v7

    goto :goto_2

    :cond_2
    if-eqz v1, :cond_3

    iget-object v1, v0, Lj2/q1;->g:Landroidx/media3/common/h0;

    invoke-interface {v1}, Landroidx/media3/common/h0;->getContentPosition()J

    move-result-wide v7

    goto :goto_2

    :cond_3
    invoke-virtual/range {p1 .. p1}, Landroidx/media3/common/m0;->q()Z

    move-result v1

    if-eqz v1, :cond_4

    goto :goto_2

    :cond_4
    iget-object v1, v0, Lj2/q1;->c:Landroidx/media3/common/m0$c;

    invoke-virtual {v4, v5, v1}, Landroidx/media3/common/m0;->n(ILandroidx/media3/common/m0$c;)Landroidx/media3/common/m0$c;

    move-result-object v1

    invoke-virtual {v1}, Landroidx/media3/common/m0$c;->b()J

    move-result-wide v7

    :cond_5
    :goto_2
    iget-object v1, v0, Lj2/q1;->d:Lj2/q1$a;

    invoke-virtual {v1}, Lj2/q1$a;->d()Landroidx/media3/exoplayer/source/l$b;

    move-result-object v11

    new-instance v16, Lj2/c$a;

    iget-object v1, v0, Lj2/q1;->g:Landroidx/media3/common/h0;

    invoke-interface {v1}, Landroidx/media3/common/h0;->getCurrentTimeline()Landroidx/media3/common/m0;

    move-result-object v9

    iget-object v1, v0, Lj2/q1;->g:Landroidx/media3/common/h0;

    invoke-interface {v1}, Landroidx/media3/common/h0;->u()I

    move-result v10

    iget-object v1, v0, Lj2/q1;->g:Landroidx/media3/common/h0;

    invoke-interface {v1}, Landroidx/media3/common/h0;->getCurrentPosition()J

    move-result-wide v12

    iget-object v1, v0, Lj2/q1;->g:Landroidx/media3/common/h0;

    invoke-interface {v1}, Landroidx/media3/common/h0;->d()J

    move-result-wide v14

    move-object/from16 v1, v16

    move-object/from16 v4, p1

    move/from16 v5, p2

    invoke-direct/range {v1 .. v15}, Lj2/c$a;-><init>(JLandroidx/media3/common/m0;ILandroidx/media3/exoplayer/source/l$b;JLandroidx/media3/common/m0;ILandroidx/media3/exoplayer/source/l$b;JJ)V

    return-object v16
.end method

.method public final X0(Landroidx/media3/exoplayer/source/l$b;)Lj2/c$a;
    .locals 3
    .param p1    # Landroidx/media3/exoplayer/source/l$b;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    iget-object v0, p0, Lj2/q1;->g:Landroidx/media3/common/h0;

    invoke-static {v0}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    const/4 v0, 0x0

    if-nez p1, :cond_0

    move-object v1, v0

    goto :goto_0

    :cond_0
    iget-object v1, p0, Lj2/q1;->d:Lj2/q1$a;

    invoke-virtual {v1, p1}, Lj2/q1$a;->f(Landroidx/media3/exoplayer/source/l$b;)Landroidx/media3/common/m0;

    move-result-object v1

    :goto_0
    if-eqz p1, :cond_2

    if-nez v1, :cond_1

    goto :goto_1

    :cond_1
    iget-object v0, p1, Landroidx/media3/exoplayer/source/l$b;->a:Ljava/lang/Object;

    iget-object v2, p0, Lj2/q1;->b:Landroidx/media3/common/m0$b;

    invoke-virtual {v1, v0, v2}, Landroidx/media3/common/m0;->h(Ljava/lang/Object;Landroidx/media3/common/m0$b;)Landroidx/media3/common/m0$b;

    move-result-object v0

    iget v0, v0, Landroidx/media3/common/m0$b;->c:I

    invoke-virtual {p0, v1, v0, p1}, Lj2/q1;->W0(Landroidx/media3/common/m0;ILandroidx/media3/exoplayer/source/l$b;)Lj2/c$a;

    move-result-object p1

    return-object p1

    :cond_2
    :goto_1
    iget-object p1, p0, Lj2/q1;->g:Landroidx/media3/common/h0;

    invoke-interface {p1}, Landroidx/media3/common/h0;->u()I

    move-result p1

    iget-object v1, p0, Lj2/q1;->g:Landroidx/media3/common/h0;

    invoke-interface {v1}, Landroidx/media3/common/h0;->getCurrentTimeline()Landroidx/media3/common/m0;

    move-result-object v1

    invoke-virtual {v1}, Landroidx/media3/common/m0;->p()I

    move-result v2

    if-ge p1, v2, :cond_3

    goto :goto_2

    :cond_3
    sget-object v1, Landroidx/media3/common/m0;->a:Landroidx/media3/common/m0;

    :goto_2
    invoke-virtual {p0, v1, p1, v0}, Lj2/q1;->W0(Landroidx/media3/common/m0;ILandroidx/media3/exoplayer/source/l$b;)Lj2/c$a;

    move-result-object p1

    return-object p1
.end method

.method public final Y0()Lj2/c$a;
    .locals 1

    iget-object v0, p0, Lj2/q1;->d:Lj2/q1$a;

    invoke-virtual {v0}, Lj2/q1$a;->e()Landroidx/media3/exoplayer/source/l$b;

    move-result-object v0

    invoke-virtual {p0, v0}, Lj2/q1;->X0(Landroidx/media3/exoplayer/source/l$b;)Lj2/c$a;

    move-result-object v0

    return-object v0
.end method

.method public final Z0(ILandroidx/media3/exoplayer/source/l$b;)Lj2/c$a;
    .locals 1
    .param p2    # Landroidx/media3/exoplayer/source/l$b;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    iget-object v0, p0, Lj2/q1;->g:Landroidx/media3/common/h0;

    invoke-static {v0}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    if-eqz p2, :cond_1

    iget-object v0, p0, Lj2/q1;->d:Lj2/q1$a;

    invoke-virtual {v0, p2}, Lj2/q1$a;->f(Landroidx/media3/exoplayer/source/l$b;)Landroidx/media3/common/m0;

    move-result-object v0

    if-eqz v0, :cond_0

    invoke-virtual {p0, p2}, Lj2/q1;->X0(Landroidx/media3/exoplayer/source/l$b;)Lj2/c$a;

    move-result-object p1

    goto :goto_0

    :cond_0
    sget-object v0, Landroidx/media3/common/m0;->a:Landroidx/media3/common/m0;

    invoke-virtual {p0, v0, p1, p2}, Lj2/q1;->W0(Landroidx/media3/common/m0;ILandroidx/media3/exoplayer/source/l$b;)Lj2/c$a;

    move-result-object p1

    :goto_0
    return-object p1

    :cond_1
    iget-object p2, p0, Lj2/q1;->g:Landroidx/media3/common/h0;

    invoke-interface {p2}, Landroidx/media3/common/h0;->getCurrentTimeline()Landroidx/media3/common/m0;

    move-result-object p2

    invoke-virtual {p2}, Landroidx/media3/common/m0;->p()I

    move-result v0

    if-ge p1, v0, :cond_2

    goto :goto_1

    :cond_2
    sget-object p2, Landroidx/media3/common/m0;->a:Landroidx/media3/common/m0;

    :goto_1
    const/4 v0, 0x0

    invoke-virtual {p0, p2, p1, v0}, Lj2/q1;->W0(Landroidx/media3/common/m0;ILandroidx/media3/exoplayer/source/l$b;)Lj2/c$a;

    move-result-object p1

    return-object p1
.end method

.method public final a(Ljava/lang/Exception;)V
    .locals 2

    invoke-virtual {p0}, Lj2/q1;->b1()Lj2/c$a;

    move-result-object v0

    new-instance v1, Lj2/j;

    invoke-direct {v1, v0, p1}, Lj2/j;-><init>(Lj2/c$a;Ljava/lang/Exception;)V

    const/16 p1, 0x3f6

    invoke-virtual {p0, v0, p1, v1}, Lj2/q1;->q2(Lj2/c$a;ILe2/n$a;)V

    return-void
.end method

.method public final a1()Lj2/c$a;
    .locals 1

    iget-object v0, p0, Lj2/q1;->d:Lj2/q1$a;

    invoke-virtual {v0}, Lj2/q1$a;->g()Landroidx/media3/exoplayer/source/l$b;

    move-result-object v0

    invoke-virtual {p0, v0}, Lj2/q1;->X0(Landroidx/media3/exoplayer/source/l$b;)Lj2/c$a;

    move-result-object v0

    return-object v0
.end method

.method public final b(Ljava/lang/String;)V
    .locals 2

    invoke-virtual {p0}, Lj2/q1;->b1()Lj2/c$a;

    move-result-object v0

    new-instance v1, Lj2/o1;

    invoke-direct {v1, v0, p1}, Lj2/o1;-><init>(Lj2/c$a;Ljava/lang/String;)V

    const/16 p1, 0x3fb

    invoke-virtual {p0, v0, p1, v1}, Lj2/q1;->q2(Lj2/c$a;ILe2/n$a;)V

    return-void
.end method

.method public final b1()Lj2/c$a;
    .locals 1

    iget-object v0, p0, Lj2/q1;->d:Lj2/q1$a;

    invoke-virtual {v0}, Lj2/q1$a;->h()Landroidx/media3/exoplayer/source/l$b;

    move-result-object v0

    invoke-virtual {p0, v0}, Lj2/q1;->X0(Landroidx/media3/exoplayer/source/l$b;)Lj2/c$a;

    move-result-object v0

    return-object v0
.end method

.method public final c(Ljava/lang/String;)V
    .locals 2

    invoke-virtual {p0}, Lj2/q1;->b1()Lj2/c$a;

    move-result-object v0

    new-instance v1, Lj2/g1;

    invoke-direct {v1, v0, p1}, Lj2/g1;-><init>(Lj2/c$a;Ljava/lang/String;)V

    const/16 p1, 0x3f4

    invoke-virtual {p0, v0, p1, v1}, Lj2/q1;->q2(Lj2/c$a;ILe2/n$a;)V

    return-void
.end method

.method public final c1(Landroidx/media3/common/PlaybackException;)Lj2/c$a;
    .locals 1
    .param p1    # Landroidx/media3/common/PlaybackException;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    instance-of v0, p1, Landroidx/media3/exoplayer/ExoPlaybackException;

    if-eqz v0, :cond_0

    check-cast p1, Landroidx/media3/exoplayer/ExoPlaybackException;

    iget-object p1, p1, Landroidx/media3/exoplayer/ExoPlaybackException;->mediaPeriodId:Landroidx/media3/exoplayer/source/l$b;

    if-eqz p1, :cond_0

    invoke-virtual {p0, p1}, Lj2/q1;->X0(Landroidx/media3/exoplayer/source/l$b;)Lj2/c$a;

    move-result-object p1

    return-object p1

    :cond_0
    invoke-virtual {p0}, Lj2/q1;->V0()Lj2/c$a;

    move-result-object p1

    return-object p1
.end method

.method public final d(J)V
    .locals 2

    invoke-virtual {p0}, Lj2/q1;->b1()Lj2/c$a;

    move-result-object v0

    new-instance v1, Lj2/q0;

    invoke-direct {v1, v0, p1, p2}, Lj2/q0;-><init>(Lj2/c$a;J)V

    const/16 p1, 0x3f2

    invoke-virtual {p0, v0, p1, v1}, Lj2/q1;->q2(Lj2/c$a;ILe2/n$a;)V

    return-void
.end method

.method public final e(Ljava/lang/Exception;)V
    .locals 2

    invoke-virtual {p0}, Lj2/q1;->b1()Lj2/c$a;

    move-result-object v0

    new-instance v1, Lj2/i;

    invoke-direct {v1, v0, p1}, Lj2/i;-><init>(Lj2/c$a;Ljava/lang/Exception;)V

    const/16 p1, 0x406

    invoke-virtual {p0, v0, p1, v1}, Lj2/q1;->q2(Lj2/c$a;ILe2/n$a;)V

    return-void
.end method

.method public final f(ILandroidx/media3/exoplayer/source/l$b;Lu2/n;Lu2/o;)V
    .locals 0
    .param p2    # Landroidx/media3/exoplayer/source/l$b;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    invoke-virtual {p0, p1, p2}, Lj2/q1;->Z0(ILandroidx/media3/exoplayer/source/l$b;)Lj2/c$a;

    move-result-object p1

    new-instance p2, Lj2/x;

    invoke-direct {p2, p1, p3, p4}, Lj2/x;-><init>(Lj2/c$a;Lu2/n;Lu2/o;)V

    const/16 p3, 0x3ea

    invoke-virtual {p0, p1, p3, p2}, Lj2/q1;->q2(Lj2/c$a;ILe2/n$a;)V

    return-void
.end method

.method public final g(Ljava/lang/Object;J)V
    .locals 2

    invoke-virtual {p0}, Lj2/q1;->b1()Lj2/c$a;

    move-result-object v0

    new-instance v1, Lj2/d1;

    invoke-direct {v1, v0, p1, p2, p3}, Lj2/d1;-><init>(Lj2/c$a;Ljava/lang/Object;J)V

    const/16 p1, 0x1a

    invoke-virtual {p0, v0, p1, v1}, Lj2/q1;->q2(Lj2/c$a;ILe2/n$a;)V

    return-void
.end method

.method public final h(Ljava/lang/Exception;)V
    .locals 2

    invoke-virtual {p0}, Lj2/q1;->b1()Lj2/c$a;

    move-result-object v0

    new-instance v1, Lj2/p;

    invoke-direct {v1, v0, p1}, Lj2/p;-><init>(Lj2/c$a;Ljava/lang/Exception;)V

    const/16 p1, 0x405

    invoke-virtual {p0, v0, p1, v1}, Lj2/q1;->q2(Lj2/c$a;ILe2/n$a;)V

    return-void
.end method

.method public final i(IJJ)V
    .locals 9

    invoke-virtual {p0}, Lj2/q1;->b1()Lj2/c$a;

    move-result-object v7

    new-instance v8, Lj2/x0;

    move-object v0, v8

    move-object v1, v7

    move v2, p1

    move-wide v3, p2

    move-wide v5, p4

    invoke-direct/range {v0 .. v6}, Lj2/x0;-><init>(Lj2/c$a;IJJ)V

    const/16 p1, 0x3f3

    invoke-virtual {p0, v7, p1, v8}, Lj2/q1;->q2(Lj2/c$a;ILe2/n$a;)V

    return-void
.end method

.method public final j(JI)V
    .locals 2

    invoke-virtual {p0}, Lj2/q1;->a1()Lj2/c$a;

    move-result-object v0

    new-instance v1, Lj2/d;

    invoke-direct {v1, v0, p1, p2, p3}, Lj2/d;-><init>(Lj2/c$a;JI)V

    const/16 p1, 0x3fd

    invoke-virtual {p0, v0, p1, v1}, Lj2/q1;->q2(Lj2/c$a;ILe2/n$a;)V

    return-void
.end method

.method public k(Landroidx/media3/exoplayer/audio/AudioSink$a;)V
    .locals 2

    invoke-virtual {p0}, Lj2/q1;->b1()Lj2/c$a;

    move-result-object v0

    new-instance v1, Lj2/f1;

    invoke-direct {v1, v0, p1}, Lj2/f1;-><init>(Lj2/c$a;Landroidx/media3/exoplayer/audio/AudioSink$a;)V

    const/16 p1, 0x407

    invoke-virtual {p0, v0, p1, v1}, Lj2/q1;->q2(Lj2/c$a;ILe2/n$a;)V

    return-void
.end method

.method public l(Landroidx/media3/exoplayer/audio/AudioSink$a;)V
    .locals 2

    invoke-virtual {p0}, Lj2/q1;->b1()Lj2/c$a;

    move-result-object v0

    new-instance v1, Lj2/j1;

    invoke-direct {v1, v0, p1}, Lj2/j1;-><init>(Lj2/c$a;Landroidx/media3/exoplayer/audio/AudioSink$a;)V

    const/16 p1, 0x408

    invoke-virtual {p0, v0, p1, v1}, Lj2/q1;->q2(Lj2/c$a;ILe2/n$a;)V

    return-void
.end method

.method public final m(ILandroidx/media3/exoplayer/source/l$b;Lu2/n;Lu2/o;)V
    .locals 0
    .param p2    # Landroidx/media3/exoplayer/source/l$b;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    invoke-virtual {p0, p1, p2}, Lj2/q1;->Z0(ILandroidx/media3/exoplayer/source/l$b;)Lj2/c$a;

    move-result-object p1

    new-instance p2, Lj2/z;

    invoke-direct {p2, p1, p3, p4}, Lj2/z;-><init>(Lj2/c$a;Lu2/n;Lu2/o;)V

    const/16 p3, 0x3e8

    invoke-virtual {p0, p1, p3, p2}, Lj2/q1;->q2(Lj2/c$a;ILe2/n$a;)V

    return-void
.end method

.method public final n()V
    .locals 3

    iget-boolean v0, p0, Lj2/q1;->i:Z

    if-nez v0, :cond_0

    invoke-virtual {p0}, Lj2/q1;->V0()Lj2/c$a;

    move-result-object v0

    const/4 v1, 0x1

    iput-boolean v1, p0, Lj2/q1;->i:Z

    new-instance v1, Lj2/r0;

    invoke-direct {v1, v0}, Lj2/r0;-><init>(Lj2/c$a;)V

    const/4 v2, -0x1

    invoke-virtual {p0, v0, v2, v1}, Lj2/q1;->q2(Lj2/c$a;ILe2/n$a;)V

    :cond_0
    return-void
.end method

.method public final o(Landroidx/media3/exoplayer/n;)V
    .locals 2

    invoke-virtual {p0}, Lj2/q1;->b1()Lj2/c$a;

    move-result-object v0

    new-instance v1, Lj2/c0;

    invoke-direct {v1, v0, p1}, Lj2/c0;-><init>(Lj2/c$a;Landroidx/media3/exoplayer/n;)V

    const/16 p1, 0x3ef

    invoke-virtual {p0, v0, p1, v1}, Lj2/q1;->q2(Lj2/c$a;ILe2/n$a;)V

    return-void
.end method

.method public final synthetic o2(Landroidx/media3/common/h0;Lj2/c;Landroidx/media3/common/s;)V
    .locals 2

    new-instance v0, Lj2/c$b;

    iget-object v1, p0, Lj2/q1;->e:Landroid/util/SparseArray;

    invoke-direct {v0, p3, v1}, Lj2/c$b;-><init>(Landroidx/media3/common/s;Landroid/util/SparseArray;)V

    invoke-interface {p2, p1, v0}, Lj2/c;->x(Landroidx/media3/common/h0;Lj2/c$b;)V

    return-void
.end method

.method public final onAudioDecoderInitialized(Ljava/lang/String;JJ)V
    .locals 9

    invoke-virtual {p0}, Lj2/q1;->b1()Lj2/c$a;

    move-result-object v7

    new-instance v8, Lj2/b0;

    move-object v0, v8

    move-object v1, v7

    move-object v2, p1

    move-wide v3, p4

    move-wide v5, p2

    invoke-direct/range {v0 .. v6}, Lj2/b0;-><init>(Lj2/c$a;Ljava/lang/String;JJ)V

    const/16 p1, 0x3f0

    invoke-virtual {p0, v7, p1, v8}, Lj2/q1;->q2(Lj2/c$a;ILe2/n$a;)V

    return-void
.end method

.method public onAvailableCommandsChanged(Landroidx/media3/common/h0$b;)V
    .locals 2

    invoke-virtual {p0}, Lj2/q1;->V0()Lj2/c$a;

    move-result-object v0

    new-instance v1, Lj2/l;

    invoke-direct {v1, v0, p1}, Lj2/l;-><init>(Lj2/c$a;Landroidx/media3/common/h0$b;)V

    const/16 p1, 0xd

    invoke-virtual {p0, v0, p1, v1}, Lj2/q1;->q2(Lj2/c$a;ILe2/n$a;)V

    return-void
.end method

.method public final onBandwidthSample(IJJ)V
    .locals 9

    invoke-virtual {p0}, Lj2/q1;->Y0()Lj2/c$a;

    move-result-object v7

    new-instance v8, Lj2/i1;

    move-object v0, v8

    move-object v1, v7

    move v2, p1

    move-wide v3, p2

    move-wide v5, p4

    invoke-direct/range {v0 .. v6}, Lj2/i1;-><init>(Lj2/c$a;IJJ)V

    const/16 p1, 0x3ee

    invoke-virtual {p0, v7, p1, v8}, Lj2/q1;->q2(Lj2/c$a;ILe2/n$a;)V

    return-void
.end method

.method public onCues(Ld2/b;)V
    .locals 2

    invoke-virtual {p0}, Lj2/q1;->V0()Lj2/c$a;

    move-result-object v0

    new-instance v1, Lj2/d0;

    invoke-direct {v1, v0, p1}, Lj2/d0;-><init>(Lj2/c$a;Ld2/b;)V

    const/16 p1, 0x1b

    invoke-virtual {p0, v0, p1, v1}, Lj2/q1;->q2(Lj2/c$a;ILe2/n$a;)V

    return-void
.end method

.method public onCues(Ljava/util/List;)V
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Ld2/a;",
            ">;)V"
        }
    .end annotation

    invoke-virtual {p0}, Lj2/q1;->V0()Lj2/c$a;

    move-result-object v0

    new-instance v1, Lj2/s;

    invoke-direct {v1, v0, p1}, Lj2/s;-><init>(Lj2/c$a;Ljava/util/List;)V

    const/16 p1, 0x1b

    invoke-virtual {p0, v0, p1, v1}, Lj2/q1;->q2(Lj2/c$a;ILe2/n$a;)V

    return-void
.end method

.method public onDeviceInfoChanged(Landroidx/media3/common/o;)V
    .locals 2

    invoke-virtual {p0}, Lj2/q1;->V0()Lj2/c$a;

    move-result-object v0

    new-instance v1, Lj2/m;

    invoke-direct {v1, v0, p1}, Lj2/m;-><init>(Lj2/c$a;Landroidx/media3/common/o;)V

    const/16 p1, 0x1d

    invoke-virtual {p0, v0, p1, v1}, Lj2/q1;->q2(Lj2/c$a;ILe2/n$a;)V

    return-void
.end method

.method public onDeviceVolumeChanged(IZ)V
    .locals 2

    invoke-virtual {p0}, Lj2/q1;->V0()Lj2/c$a;

    move-result-object v0

    new-instance v1, Lj2/q;

    invoke-direct {v1, v0, p1, p2}, Lj2/q;-><init>(Lj2/c$a;IZ)V

    const/16 p1, 0x1e

    invoke-virtual {p0, v0, p1, v1}, Lj2/q1;->q2(Lj2/c$a;ILe2/n$a;)V

    return-void
.end method

.method public final onDroppedFrames(IJ)V
    .locals 2

    invoke-virtual {p0}, Lj2/q1;->a1()Lj2/c$a;

    move-result-object v0

    new-instance v1, Lj2/v;

    invoke-direct {v1, v0, p1, p2, p3}, Lj2/v;-><init>(Lj2/c$a;IJ)V

    const/16 p1, 0x3fa

    invoke-virtual {p0, v0, p1, v1}, Lj2/q1;->q2(Lj2/c$a;ILe2/n$a;)V

    return-void
.end method

.method public onEvents(Landroidx/media3/common/h0;Landroidx/media3/common/h0$c;)V
    .locals 0

    return-void
.end method

.method public final onIsLoadingChanged(Z)V
    .locals 2

    invoke-virtual {p0}, Lj2/q1;->V0()Lj2/c$a;

    move-result-object v0

    new-instance v1, Lj2/v0;

    invoke-direct {v1, v0, p1}, Lj2/v0;-><init>(Lj2/c$a;Z)V

    const/4 p1, 0x3

    invoke-virtual {p0, v0, p1, v1}, Lj2/q1;->q2(Lj2/c$a;ILe2/n$a;)V

    return-void
.end method

.method public onIsPlayingChanged(Z)V
    .locals 2

    invoke-virtual {p0}, Lj2/q1;->V0()Lj2/c$a;

    move-result-object v0

    new-instance v1, Lj2/f0;

    invoke-direct {v1, v0, p1}, Lj2/f0;-><init>(Lj2/c$a;Z)V

    const/4 p1, 0x7

    invoke-virtual {p0, v0, p1, v1}, Lj2/q1;->q2(Lj2/c$a;ILe2/n$a;)V

    return-void
.end method

.method public onLoadingChanged(Z)V
    .locals 0

    return-void
.end method

.method public final onMediaItemTransition(Landroidx/media3/common/b0;I)V
    .locals 2
    .param p1    # Landroidx/media3/common/b0;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    invoke-virtual {p0}, Lj2/q1;->V0()Lj2/c$a;

    move-result-object v0

    new-instance v1, Lj2/i0;

    invoke-direct {v1, v0, p1, p2}, Lj2/i0;-><init>(Lj2/c$a;Landroidx/media3/common/b0;I)V

    const/4 p1, 0x1

    invoke-virtual {p0, v0, p1, v1}, Lj2/q1;->q2(Lj2/c$a;ILe2/n$a;)V

    return-void
.end method

.method public onMediaMetadataChanged(Landroidx/media3/common/d0;)V
    .locals 2

    invoke-virtual {p0}, Lj2/q1;->V0()Lj2/c$a;

    move-result-object v0

    new-instance v1, Lj2/m0;

    invoke-direct {v1, v0, p1}, Lj2/m0;-><init>(Lj2/c$a;Landroidx/media3/common/d0;)V

    const/16 p1, 0xe

    invoke-virtual {p0, v0, p1, v1}, Lj2/q1;->q2(Lj2/c$a;ILe2/n$a;)V

    return-void
.end method

.method public final onMetadata(Landroidx/media3/common/Metadata;)V
    .locals 2

    invoke-virtual {p0}, Lj2/q1;->V0()Lj2/c$a;

    move-result-object v0

    new-instance v1, Lj2/a0;

    invoke-direct {v1, v0, p1}, Lj2/a0;-><init>(Lj2/c$a;Landroidx/media3/common/Metadata;)V

    const/16 p1, 0x1c

    invoke-virtual {p0, v0, p1, v1}, Lj2/q1;->q2(Lj2/c$a;ILe2/n$a;)V

    return-void
.end method

.method public final onPlayWhenReadyChanged(ZI)V
    .locals 2

    invoke-virtual {p0}, Lj2/q1;->V0()Lj2/c$a;

    move-result-object v0

    new-instance v1, Lj2/e0;

    invoke-direct {v1, v0, p1, p2}, Lj2/e0;-><init>(Lj2/c$a;ZI)V

    const/4 p1, 0x5

    invoke-virtual {p0, v0, p1, v1}, Lj2/q1;->q2(Lj2/c$a;ILe2/n$a;)V

    return-void
.end method

.method public final onPlaybackParametersChanged(Landroidx/media3/common/g0;)V
    .locals 2

    invoke-virtual {p0}, Lj2/q1;->V0()Lj2/c$a;

    move-result-object v0

    new-instance v1, Lj2/e;

    invoke-direct {v1, v0, p1}, Lj2/e;-><init>(Lj2/c$a;Landroidx/media3/common/g0;)V

    const/16 p1, 0xc

    invoke-virtual {p0, v0, p1, v1}, Lj2/q1;->q2(Lj2/c$a;ILe2/n$a;)V

    return-void
.end method

.method public final onPlaybackStateChanged(I)V
    .locals 2

    invoke-virtual {p0}, Lj2/q1;->V0()Lj2/c$a;

    move-result-object v0

    new-instance v1, Lj2/h0;

    invoke-direct {v1, v0, p1}, Lj2/h0;-><init>(Lj2/c$a;I)V

    const/4 p1, 0x4

    invoke-virtual {p0, v0, p1, v1}, Lj2/q1;->q2(Lj2/c$a;ILe2/n$a;)V

    return-void
.end method

.method public final onPlaybackSuppressionReasonChanged(I)V
    .locals 2

    invoke-virtual {p0}, Lj2/q1;->V0()Lj2/c$a;

    move-result-object v0

    new-instance v1, Lj2/k0;

    invoke-direct {v1, v0, p1}, Lj2/k0;-><init>(Lj2/c$a;I)V

    const/4 p1, 0x6

    invoke-virtual {p0, v0, p1, v1}, Lj2/q1;->q2(Lj2/c$a;ILe2/n$a;)V

    return-void
.end method

.method public final onPlayerError(Landroidx/media3/common/PlaybackException;)V
    .locals 2

    invoke-virtual {p0, p1}, Lj2/q1;->c1(Landroidx/media3/common/PlaybackException;)Lj2/c$a;

    move-result-object v0

    new-instance v1, Lj2/y;

    invoke-direct {v1, v0, p1}, Lj2/y;-><init>(Lj2/c$a;Landroidx/media3/common/PlaybackException;)V

    const/16 p1, 0xa

    invoke-virtual {p0, v0, p1, v1}, Lj2/q1;->q2(Lj2/c$a;ILe2/n$a;)V

    return-void
.end method

.method public onPlayerErrorChanged(Landroidx/media3/common/PlaybackException;)V
    .locals 2
    .param p1    # Landroidx/media3/common/PlaybackException;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    invoke-virtual {p0, p1}, Lj2/q1;->c1(Landroidx/media3/common/PlaybackException;)Lj2/c$a;

    move-result-object v0

    new-instance v1, Lj2/l0;

    invoke-direct {v1, v0, p1}, Lj2/l0;-><init>(Lj2/c$a;Landroidx/media3/common/PlaybackException;)V

    const/16 p1, 0xa

    invoke-virtual {p0, v0, p1, v1}, Lj2/q1;->q2(Lj2/c$a;ILe2/n$a;)V

    return-void
.end method

.method public final onPlayerStateChanged(ZI)V
    .locals 2

    invoke-virtual {p0}, Lj2/q1;->V0()Lj2/c$a;

    move-result-object v0

    new-instance v1, Lj2/m1;

    invoke-direct {v1, v0, p1, p2}, Lj2/m1;-><init>(Lj2/c$a;ZI)V

    const/4 p1, -0x1

    invoke-virtual {p0, v0, p1, v1}, Lj2/q1;->q2(Lj2/c$a;ILe2/n$a;)V

    return-void
.end method

.method public onPositionDiscontinuity(I)V
    .locals 0

    return-void
.end method

.method public final onPositionDiscontinuity(Landroidx/media3/common/h0$e;Landroidx/media3/common/h0$e;I)V
    .locals 2

    const/4 v0, 0x1

    if-ne p3, v0, :cond_0

    const/4 v0, 0x0

    iput-boolean v0, p0, Lj2/q1;->i:Z

    :cond_0
    iget-object v0, p0, Lj2/q1;->d:Lj2/q1$a;

    iget-object v1, p0, Lj2/q1;->g:Landroidx/media3/common/h0;

    invoke-static {v1}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Landroidx/media3/common/h0;

    invoke-virtual {v0, v1}, Lj2/q1$a;->j(Landroidx/media3/common/h0;)V

    invoke-virtual {p0}, Lj2/q1;->V0()Lj2/c$a;

    move-result-object v0

    new-instance v1, Lj2/k;

    invoke-direct {v1, v0, p3, p1, p2}, Lj2/k;-><init>(Lj2/c$a;ILandroidx/media3/common/h0$e;Landroidx/media3/common/h0$e;)V

    const/16 p1, 0xb

    invoke-virtual {p0, v0, p1, v1}, Lj2/q1;->q2(Lj2/c$a;ILe2/n$a;)V

    return-void
.end method

.method public onRenderedFirstFrame()V
    .locals 0

    return-void
.end method

.method public final onRepeatModeChanged(I)V
    .locals 2

    invoke-virtual {p0}, Lj2/q1;->V0()Lj2/c$a;

    move-result-object v0

    new-instance v1, Lj2/h;

    invoke-direct {v1, v0, p1}, Lj2/h;-><init>(Lj2/c$a;I)V

    const/16 p1, 0x8

    invoke-virtual {p0, v0, p1, v1}, Lj2/q1;->q2(Lj2/c$a;ILe2/n$a;)V

    return-void
.end method

.method public final onShuffleModeEnabledChanged(Z)V
    .locals 2

    invoke-virtual {p0}, Lj2/q1;->V0()Lj2/c$a;

    move-result-object v0

    new-instance v1, Lj2/g0;

    invoke-direct {v1, v0, p1}, Lj2/g0;-><init>(Lj2/c$a;Z)V

    const/16 p1, 0x9

    invoke-virtual {p0, v0, p1, v1}, Lj2/q1;->q2(Lj2/c$a;ILe2/n$a;)V

    return-void
.end method

.method public final onSkipSilenceEnabledChanged(Z)V
    .locals 2

    invoke-virtual {p0}, Lj2/q1;->b1()Lj2/c$a;

    move-result-object v0

    new-instance v1, Lj2/k1;

    invoke-direct {v1, v0, p1}, Lj2/k1;-><init>(Lj2/c$a;Z)V

    const/16 p1, 0x17

    invoke-virtual {p0, v0, p1, v1}, Lj2/q1;->q2(Lj2/c$a;ILe2/n$a;)V

    return-void
.end method

.method public final onSurfaceSizeChanged(II)V
    .locals 2

    invoke-virtual {p0}, Lj2/q1;->b1()Lj2/c$a;

    move-result-object v0

    new-instance v1, Lj2/t0;

    invoke-direct {v1, v0, p1, p2}, Lj2/t0;-><init>(Lj2/c$a;II)V

    const/16 p1, 0x18

    invoke-virtual {p0, v0, p1, v1}, Lj2/q1;->q2(Lj2/c$a;ILe2/n$a;)V

    return-void
.end method

.method public final onTimelineChanged(Landroidx/media3/common/m0;I)V
    .locals 1

    iget-object p1, p0, Lj2/q1;->d:Lj2/q1$a;

    iget-object v0, p0, Lj2/q1;->g:Landroidx/media3/common/h0;

    invoke-static {v0}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroidx/media3/common/h0;

    invoke-virtual {p1, v0}, Lj2/q1$a;->l(Landroidx/media3/common/h0;)V

    invoke-virtual {p0}, Lj2/q1;->V0()Lj2/c$a;

    move-result-object p1

    new-instance v0, Lj2/u;

    invoke-direct {v0, p1, p2}, Lj2/u;-><init>(Lj2/c$a;I)V

    const/4 p2, 0x0

    invoke-virtual {p0, p1, p2, v0}, Lj2/q1;->q2(Lj2/c$a;ILe2/n$a;)V

    return-void
.end method

.method public onTrackSelectionParametersChanged(Landroidx/media3/common/p0;)V
    .locals 2

    invoke-virtual {p0}, Lj2/q1;->V0()Lj2/c$a;

    move-result-object v0

    new-instance v1, Lj2/g;

    invoke-direct {v1, v0, p1}, Lj2/g;-><init>(Lj2/c$a;Landroidx/media3/common/p0;)V

    const/16 p1, 0x13

    invoke-virtual {p0, v0, p1, v1}, Lj2/q1;->q2(Lj2/c$a;ILe2/n$a;)V

    return-void
.end method

.method public onTracksChanged(Landroidx/media3/common/q0;)V
    .locals 2

    invoke-virtual {p0}, Lj2/q1;->V0()Lj2/c$a;

    move-result-object v0

    new-instance v1, Lj2/n;

    invoke-direct {v1, v0, p1}, Lj2/n;-><init>(Lj2/c$a;Landroidx/media3/common/q0;)V

    const/4 p1, 0x2

    invoke-virtual {p0, v0, p1, v1}, Lj2/q1;->q2(Lj2/c$a;ILe2/n$a;)V

    return-void
.end method

.method public final onVideoDecoderInitialized(Ljava/lang/String;JJ)V
    .locals 9

    invoke-virtual {p0}, Lj2/q1;->b1()Lj2/c$a;

    move-result-object v7

    new-instance v8, Lj2/t;

    move-object v0, v8

    move-object v1, v7

    move-object v2, p1

    move-wide v3, p4

    move-wide v5, p2

    invoke-direct/range {v0 .. v6}, Lj2/t;-><init>(Lj2/c$a;Ljava/lang/String;JJ)V

    const/16 p1, 0x3f8

    invoke-virtual {p0, v7, p1, v8}, Lj2/q1;->q2(Lj2/c$a;ILe2/n$a;)V

    return-void
.end method

.method public final onVideoSizeChanged(Landroidx/media3/common/t0;)V
    .locals 2

    invoke-virtual {p0}, Lj2/q1;->b1()Lj2/c$a;

    move-result-object v0

    new-instance v1, Lj2/c1;

    invoke-direct {v1, v0, p1}, Lj2/c1;-><init>(Lj2/c$a;Landroidx/media3/common/t0;)V

    const/16 p1, 0x19

    invoke-virtual {p0, v0, p1, v1}, Lj2/q1;->q2(Lj2/c$a;ILe2/n$a;)V

    return-void
.end method

.method public final onVolumeChanged(F)V
    .locals 2

    invoke-virtual {p0}, Lj2/q1;->b1()Lj2/c$a;

    move-result-object v0

    new-instance v1, Lj2/p1;

    invoke-direct {v1, v0, p1}, Lj2/p1;-><init>(Lj2/c$a;F)V

    const/16 p1, 0x16

    invoke-virtual {p0, v0, p1, v1}, Lj2/q1;->q2(Lj2/c$a;ILe2/n$a;)V

    return-void
.end method

.method public final p(Landroidx/media3/exoplayer/n;)V
    .locals 2

    invoke-virtual {p0}, Lj2/q1;->b1()Lj2/c$a;

    move-result-object v0

    new-instance v1, Lj2/w;

    invoke-direct {v1, v0, p1}, Lj2/w;-><init>(Lj2/c$a;Landroidx/media3/exoplayer/n;)V

    const/16 p1, 0x3f7

    invoke-virtual {p0, v0, p1, v1}, Lj2/q1;->q2(Lj2/c$a;ILe2/n$a;)V

    return-void
.end method

.method public final p2()V
    .locals 3

    invoke-virtual {p0}, Lj2/q1;->V0()Lj2/c$a;

    move-result-object v0

    new-instance v1, Lj2/b1;

    invoke-direct {v1, v0}, Lj2/b1;-><init>(Lj2/c$a;)V

    const/16 v2, 0x404

    invoke-virtual {p0, v0, v2, v1}, Lj2/q1;->q2(Lj2/c$a;ILe2/n$a;)V

    iget-object v0, p0, Lj2/q1;->f:Le2/n;

    invoke-virtual {v0}, Le2/n;->j()V

    return-void
.end method

.method public final q(ILandroidx/media3/exoplayer/source/l$b;Lu2/n;Lu2/o;)V
    .locals 0
    .param p2    # Landroidx/media3/exoplayer/source/l$b;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    invoke-virtual {p0, p1, p2}, Lj2/q1;->Z0(ILandroidx/media3/exoplayer/source/l$b;)Lj2/c$a;

    move-result-object p1

    new-instance p2, Lj2/j0;

    invoke-direct {p2, p1, p3, p4}, Lj2/j0;-><init>(Lj2/c$a;Lu2/n;Lu2/o;)V

    const/16 p3, 0x3e9

    invoke-virtual {p0, p1, p3, p2}, Lj2/q1;->q2(Lj2/c$a;ILe2/n$a;)V

    return-void
.end method

.method public final q2(Lj2/c$a;ILe2/n$a;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lj2/c$a;",
            "I",
            "Le2/n$a<",
            "Lj2/c;",
            ">;)V"
        }
    .end annotation

    iget-object v0, p0, Lj2/q1;->e:Landroid/util/SparseArray;

    invoke-virtual {v0, p2, p1}, Landroid/util/SparseArray;->put(ILjava/lang/Object;)V

    iget-object p1, p0, Lj2/q1;->f:Le2/n;

    invoke-virtual {p1, p2, p3}, Le2/n;->l(ILe2/n$a;)V

    return-void
.end method

.method public final r(Ljava/util/List;Landroidx/media3/exoplayer/source/l$b;)V
    .locals 2
    .param p2    # Landroidx/media3/exoplayer/source/l$b;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Landroidx/media3/exoplayer/source/l$b;",
            ">;",
            "Landroidx/media3/exoplayer/source/l$b;",
            ")V"
        }
    .end annotation

    iget-object v0, p0, Lj2/q1;->d:Lj2/q1$a;

    iget-object v1, p0, Lj2/q1;->g:Landroidx/media3/common/h0;

    invoke-static {v1}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Landroidx/media3/common/h0;

    invoke-virtual {v0, p1, p2, v1}, Lj2/q1$a;->k(Ljava/util/List;Landroidx/media3/exoplayer/source/l$b;Landroidx/media3/common/h0;)V

    return-void
.end method

.method public release()V
    .locals 2
    .annotation build Landroidx/annotation/CallSuper;
    .end annotation

    iget-object v0, p0, Lj2/q1;->h:Le2/j;

    invoke-static {v0}, Le2/a;->i(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Le2/j;

    new-instance v1, Lj2/p0;

    invoke-direct {v1, p0}, Lj2/p0;-><init>(Lj2/q1;)V

    invoke-interface {v0, v1}, Le2/j;->post(Ljava/lang/Runnable;)Z

    return-void
.end method

.method public final s(Landroidx/media3/common/y;Landroidx/media3/exoplayer/o;)V
    .locals 2
    .param p2    # Landroidx/media3/exoplayer/o;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    invoke-virtual {p0}, Lj2/q1;->b1()Lj2/c$a;

    move-result-object v0

    new-instance v1, Lj2/n0;

    invoke-direct {v1, v0, p1, p2}, Lj2/n0;-><init>(Lj2/c$a;Landroidx/media3/common/y;Landroidx/media3/exoplayer/o;)V

    const/16 p1, 0x3f1

    invoke-virtual {p0, v0, p1, v1}, Lj2/q1;->q2(Lj2/c$a;ILe2/n$a;)V

    return-void
.end method

.method public final t(Landroidx/media3/exoplayer/n;)V
    .locals 2

    invoke-virtual {p0}, Lj2/q1;->a1()Lj2/c$a;

    move-result-object v0

    new-instance v1, Lj2/o0;

    invoke-direct {v1, v0, p1}, Lj2/o0;-><init>(Lj2/c$a;Landroidx/media3/exoplayer/n;)V

    const/16 p1, 0x3f5

    invoke-virtual {p0, v0, p1, v1}, Lj2/q1;->q2(Lj2/c$a;ILe2/n$a;)V

    return-void
.end method

.method public u(Lj2/c;)V
    .locals 1
    .annotation build Landroidx/annotation/CallSuper;
    .end annotation

    invoke-static {p1}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    iget-object v0, p0, Lj2/q1;->f:Le2/n;

    invoke-virtual {v0, p1}, Le2/n;->c(Ljava/lang/Object;)V

    return-void
.end method

.method public final v(ILandroidx/media3/exoplayer/source/l$b;Lu2/o;)V
    .locals 0
    .param p2    # Landroidx/media3/exoplayer/source/l$b;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    invoke-virtual {p0, p1, p2}, Lj2/q1;->Z0(ILandroidx/media3/exoplayer/source/l$b;)Lj2/c$a;

    move-result-object p1

    new-instance p2, Lj2/a1;

    invoke-direct {p2, p1, p3}, Lj2/a1;-><init>(Lj2/c$a;Lu2/o;)V

    const/16 p3, 0x3ec

    invoke-virtual {p0, p1, p3, p2}, Lj2/q1;->q2(Lj2/c$a;ILe2/n$a;)V

    return-void
.end method

.method public final w(Landroidx/media3/common/y;Landroidx/media3/exoplayer/o;)V
    .locals 2
    .param p2    # Landroidx/media3/exoplayer/o;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    invoke-virtual {p0}, Lj2/q1;->b1()Lj2/c$a;

    move-result-object v0

    new-instance v1, Lj2/n1;

    invoke-direct {v1, v0, p1, p2}, Lj2/n1;-><init>(Lj2/c$a;Landroidx/media3/common/y;Landroidx/media3/exoplayer/o;)V

    const/16 p1, 0x3f9

    invoke-virtual {p0, v0, p1, v1}, Lj2/q1;->q2(Lj2/c$a;ILe2/n$a;)V

    return-void
.end method

.method public final x(Landroidx/media3/exoplayer/n;)V
    .locals 2

    invoke-virtual {p0}, Lj2/q1;->a1()Lj2/c$a;

    move-result-object v0

    new-instance v1, Lj2/s0;

    invoke-direct {v1, v0, p1}, Lj2/s0;-><init>(Lj2/c$a;Landroidx/media3/exoplayer/n;)V

    const/16 p1, 0x3fc

    invoke-virtual {p0, v0, p1, v1}, Lj2/q1;->q2(Lj2/c$a;ILe2/n$a;)V

    return-void
.end method

.method public final y(ILandroidx/media3/exoplayer/source/l$b;)V
    .locals 1
    .param p2    # Landroidx/media3/exoplayer/source/l$b;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    invoke-virtual {p0, p1, p2}, Lj2/q1;->Z0(ILandroidx/media3/exoplayer/source/l$b;)Lj2/c$a;

    move-result-object p1

    new-instance p2, Lj2/h1;

    invoke-direct {p2, p1}, Lj2/h1;-><init>(Lj2/c$a;)V

    const/16 v0, 0x3ff

    invoke-virtual {p0, p1, v0, p2}, Lj2/q1;->q2(Lj2/c$a;ILe2/n$a;)V

    return-void
.end method

.method public final z(ILandroidx/media3/exoplayer/source/l$b;Lu2/o;)V
    .locals 0
    .param p2    # Landroidx/media3/exoplayer/source/l$b;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    invoke-virtual {p0, p1, p2}, Lj2/q1;->Z0(ILandroidx/media3/exoplayer/source/l$b;)Lj2/c$a;

    move-result-object p1

    new-instance p2, Lj2/w0;

    invoke-direct {p2, p1, p3}, Lj2/w0;-><init>(Lj2/c$a;Lu2/o;)V

    const/16 p3, 0x3ed

    invoke-virtual {p0, p1, p3, p2}, Lj2/q1;->q2(Lj2/c$a;ILe2/n$a;)V

    return-void
.end method
