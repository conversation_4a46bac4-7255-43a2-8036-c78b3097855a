.class public final synthetic Lj2/r;
.super Ljava/lang/Object;

# interfaces
.implements Le2/n$b;


# instance fields
.field public final synthetic a:Lj2/q1;

.field public final synthetic b:Landroidx/media3/common/h0;


# direct methods
.method public synthetic constructor <init>(Lj2/q1;Landroidx/media3/common/h0;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lj2/r;->a:Lj2/q1;

    iput-object p2, p0, Lj2/r;->b:Landroidx/media3/common/h0;

    return-void
.end method


# virtual methods
.method public final a(Ljava/lang/Object;Landroidx/media3/common/s;)V
    .locals 2

    iget-object v0, p0, Lj2/r;->a:Lj2/q1;

    iget-object v1, p0, Lj2/r;->b:Landroidx/media3/common/h0;

    check-cast p1, Lj2/c;

    invoke-static {v0, v1, p1, p2}, Lj2/q1;->C0(Lj2/q1;Landroidx/media3/common/h0;Lj2/c;Landroidx/media3/common/s;)V

    return-void
.end method
