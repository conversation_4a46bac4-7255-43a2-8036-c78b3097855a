.class public final synthetic Lj2/t;
.super Ljava/lang/Object;

# interfaces
.implements Le2/n$a;


# instance fields
.field public final synthetic a:Lj2/c$a;

.field public final synthetic b:Ljava/lang/String;

.field public final synthetic c:J

.field public final synthetic d:J


# direct methods
.method public synthetic constructor <init>(Lj2/c$a;Ljava/lang/String;JJ)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lj2/t;->a:Lj2/c$a;

    iput-object p2, p0, Lj2/t;->b:Ljava/lang/String;

    iput-wide p3, p0, Lj2/t;->c:J

    iput-wide p5, p0, Lj2/t;->d:J

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)V
    .locals 7

    iget-object v0, p0, Lj2/t;->a:Lj2/c$a;

    iget-object v1, p0, Lj2/t;->b:Ljava/lang/String;

    iget-wide v2, p0, Lj2/t;->c:J

    iget-wide v4, p0, Lj2/t;->d:J

    move-object v6, p1

    check-cast v6, Lj2/c;

    invoke-static/range {v0 .. v6}, Lj2/q1;->J0(Lj2/c$a;Ljava/lang/String;JJLj2/c;)V

    return-void
.end method
