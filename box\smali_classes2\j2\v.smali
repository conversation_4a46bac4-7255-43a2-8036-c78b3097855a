.class public final synthetic Lj2/v;
.super Ljava/lang/Object;

# interfaces
.implements Le2/n$a;


# instance fields
.field public final synthetic a:Lj2/c$a;

.field public final synthetic b:I

.field public final synthetic c:J


# direct methods
.method public synthetic constructor <init>(Lj2/c$a;IJ)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lj2/v;->a:Lj2/c$a;

    iput p2, p0, Lj2/v;->b:I

    iput-wide p3, p0, Lj2/v;->c:J

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)V
    .locals 4

    iget-object v0, p0, Lj2/v;->a:Lj2/c$a;

    iget v1, p0, Lj2/v;->b:I

    iget-wide v2, p0, Lj2/v;->c:J

    check-cast p1, Lj2/c;

    invoke-static {v0, v1, v2, v3, p1}, Lj2/q1;->L(Lj2/c$a;IJLj2/c;)V

    return-void
.end method
