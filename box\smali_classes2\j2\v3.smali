.class public final Lj2/v3;
.super Ljava/lang/Object;

# interfaces
.implements Lj2/c;
.implements Lj2/w3$a;


# annotations
.annotation build Landroidx/annotation/RequiresApi;
    value = 0x1f
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lj2/v3$b;,
        Lj2/v3$a;
    }
.end annotation


# instance fields
.field public A:Z

.field public final a:Landroid/content/Context;

.field public final b:Lj2/w3;

.field public final c:Landroid/media/metrics/PlaybackSession;

.field public final d:J

.field public final e:Landroidx/media3/common/m0$c;

.field public final f:Landroidx/media3/common/m0$b;

.field public final g:Ljava/util/HashMap;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/HashMap<",
            "Ljava/lang/String;",
            "Ljava/lang/Long;",
            ">;"
        }
    .end annotation
.end field

.field public final h:Ljava/util/HashMap;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/HashMap<",
            "Ljava/lang/String;",
            "Ljava/lang/Long;",
            ">;"
        }
    .end annotation
.end field

.field public i:Ljava/lang/String;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public j:Landroid/media/metrics/PlaybackMetrics$Builder;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public k:I

.field public l:I

.field public m:I

.field public n:Landroidx/media3/common/PlaybackException;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public o:Lj2/v3$b;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public p:Lj2/v3$b;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public q:Lj2/v3$b;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public r:Landroidx/media3/common/y;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public s:Landroidx/media3/common/y;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public t:Landroidx/media3/common/y;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public u:Z

.field public v:I

.field public w:Z

.field public x:I

.field public y:I

.field public z:I


# direct methods
.method public constructor <init>(Landroid/content/Context;Landroid/media/metrics/PlaybackSession;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    invoke-virtual {p1}, Landroid/content/Context;->getApplicationContext()Landroid/content/Context;

    move-result-object p1

    iput-object p1, p0, Lj2/v3;->a:Landroid/content/Context;

    iput-object p2, p0, Lj2/v3;->c:Landroid/media/metrics/PlaybackSession;

    new-instance p1, Landroidx/media3/common/m0$c;

    invoke-direct {p1}, Landroidx/media3/common/m0$c;-><init>()V

    iput-object p1, p0, Lj2/v3;->e:Landroidx/media3/common/m0$c;

    new-instance p1, Landroidx/media3/common/m0$b;

    invoke-direct {p1}, Landroidx/media3/common/m0$b;-><init>()V

    iput-object p1, p0, Lj2/v3;->f:Landroidx/media3/common/m0$b;

    new-instance p1, Ljava/util/HashMap;

    invoke-direct {p1}, Ljava/util/HashMap;-><init>()V

    iput-object p1, p0, Lj2/v3;->h:Ljava/util/HashMap;

    new-instance p1, Ljava/util/HashMap;

    invoke-direct {p1}, Ljava/util/HashMap;-><init>()V

    iput-object p1, p0, Lj2/v3;->g:Ljava/util/HashMap;

    invoke-static {}, Landroid/os/SystemClock;->elapsedRealtime()J

    move-result-wide p1

    iput-wide p1, p0, Lj2/v3;->d:J

    const/4 p1, 0x0

    iput p1, p0, Lj2/v3;->l:I

    iput p1, p0, Lj2/v3;->m:I

    new-instance p1, Lj2/s1;

    invoke-direct {p1}, Lj2/s1;-><init>()V

    iput-object p1, p0, Lj2/v3;->b:Lj2/w3;

    invoke-interface {p1, p0}, Lj2/w3;->d(Lj2/w3$a;)V

    return-void
.end method

.method public static A0(I)I
    .locals 0
    .annotation build Landroid/annotation/SuppressLint;
        value = {
            "SwitchIntDef"
        }
    .end annotation

    invoke-static {p0}, Le2/u0;->b0(I)I

    move-result p0

    packed-switch p0, :pswitch_data_0

    const/16 p0, 0x1b

    return p0

    :pswitch_0
    const/16 p0, 0x1a

    return p0

    :pswitch_1
    const/16 p0, 0x19

    return p0

    :pswitch_2
    const/16 p0, 0x1c

    return p0

    :pswitch_3
    const/16 p0, 0x18

    return p0

    :pswitch_data_0
    .packed-switch 0x1772
        :pswitch_3
        :pswitch_2
        :pswitch_1
        :pswitch_0
    .end packed-switch
.end method

.method public static B0(Lcom/google/common/collect/ImmutableList;)Landroidx/media3/common/DrmInitData;
    .locals 3
    .annotation build Landroidx/annotation/Nullable;
    .end annotation

    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/google/common/collect/ImmutableList<",
            "Landroidx/media3/common/q0$a;",
            ">;)",
            "Landroidx/media3/common/DrmInitData;"
        }
    .end annotation

    invoke-virtual {p0}, Lcom/google/common/collect/ImmutableList;->iterator()Lcom/google/common/collect/g1;

    move-result-object p0

    :cond_0
    invoke-interface {p0}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_2

    invoke-interface {p0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroidx/media3/common/q0$a;

    const/4 v1, 0x0

    :goto_0
    iget v2, v0, Landroidx/media3/common/q0$a;->a:I

    if-ge v1, v2, :cond_0

    invoke-virtual {v0, v1}, Landroidx/media3/common/q0$a;->h(I)Z

    move-result v2

    if-eqz v2, :cond_1

    invoke-virtual {v0, v1}, Landroidx/media3/common/q0$a;->b(I)Landroidx/media3/common/y;

    move-result-object v2

    iget-object v2, v2, Landroidx/media3/common/y;->p:Landroidx/media3/common/DrmInitData;

    if-eqz v2, :cond_1

    return-object v2

    :cond_1
    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_2
    const/4 p0, 0x0

    return-object p0
.end method

.method public static C0(Landroidx/media3/common/DrmInitData;)I
    .locals 3

    const/4 v0, 0x0

    :goto_0
    iget v1, p0, Landroidx/media3/common/DrmInitData;->schemeDataCount:I

    if-ge v0, v1, :cond_3

    invoke-virtual {p0, v0}, Landroidx/media3/common/DrmInitData;->get(I)Landroidx/media3/common/DrmInitData$SchemeData;

    move-result-object v1

    iget-object v1, v1, Landroidx/media3/common/DrmInitData$SchemeData;->uuid:Ljava/util/UUID;

    sget-object v2, Landroidx/media3/common/j;->d:Ljava/util/UUID;

    invoke-virtual {v1, v2}, Ljava/util/UUID;->equals(Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_0

    const/4 p0, 0x3

    return p0

    :cond_0
    sget-object v2, Landroidx/media3/common/j;->e:Ljava/util/UUID;

    invoke-virtual {v1, v2}, Ljava/util/UUID;->equals(Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_1

    const/4 p0, 0x2

    return p0

    :cond_1
    sget-object v2, Landroidx/media3/common/j;->c:Ljava/util/UUID;

    invoke-virtual {v1, v2}, Ljava/util/UUID;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_2

    const/4 p0, 0x6

    return p0

    :cond_2
    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :cond_3
    const/4 p0, 0x1

    return p0
.end method

.method public static D0(Landroidx/media3/common/PlaybackException;Landroid/content/Context;Z)Lj2/v3$a;
    .locals 9

    iget v0, p0, Landroidx/media3/common/PlaybackException;->errorCode:I

    const/16 v1, 0x3e9

    const/4 v2, 0x0

    if-ne v0, v1, :cond_0

    new-instance p0, Lj2/v3$a;

    const/16 p1, 0x14

    invoke-direct {p0, p1, v2}, Lj2/v3$a;-><init>(II)V

    return-object p0

    :cond_0
    instance-of v0, p0, Landroidx/media3/exoplayer/ExoPlaybackException;

    const/4 v1, 0x1

    if-eqz v0, :cond_2

    move-object v0, p0

    check-cast v0, Landroidx/media3/exoplayer/ExoPlaybackException;

    iget v3, v0, Landroidx/media3/exoplayer/ExoPlaybackException;->type:I

    if-ne v3, v1, :cond_1

    const/4 v3, 0x1

    goto :goto_0

    :cond_1
    const/4 v3, 0x0

    :goto_0
    iget v0, v0, Landroidx/media3/exoplayer/ExoPlaybackException;->rendererFormatSupport:I

    goto :goto_1

    :cond_2
    const/4 v0, 0x0

    const/4 v3, 0x0

    :goto_1
    invoke-virtual {p0}, Ljava/lang/Throwable;->getCause()Ljava/lang/Throwable;

    move-result-object v4

    invoke-static {v4}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Ljava/lang/Throwable;

    instance-of v5, v4, Ljava/io/IOException;

    const/4 v6, 0x3

    const/16 v7, 0x12

    const/16 v8, 0x17

    if-eqz v5, :cond_17

    instance-of v0, v4, Landroidx/media3/datasource/HttpDataSource$InvalidResponseCodeException;

    if-eqz v0, :cond_3

    check-cast v4, Landroidx/media3/datasource/HttpDataSource$InvalidResponseCodeException;

    iget p0, v4, Landroidx/media3/datasource/HttpDataSource$InvalidResponseCodeException;->responseCode:I

    new-instance p1, Lj2/v3$a;

    const/4 p2, 0x5

    invoke-direct {p1, p2, p0}, Lj2/v3$a;-><init>(II)V

    return-object p1

    :cond_3
    instance-of v0, v4, Landroidx/media3/datasource/HttpDataSource$InvalidContentTypeException;

    if-nez v0, :cond_15

    instance-of v0, v4, Landroidx/media3/common/ParserException;

    if-eqz v0, :cond_4

    goto/16 :goto_3

    :cond_4
    instance-of p2, v4, Landroidx/media3/datasource/HttpDataSource$HttpDataSourceException;

    if-nez p2, :cond_10

    instance-of p2, v4, Landroidx/media3/datasource/UdpDataSource$UdpDataSourceException;

    if-eqz p2, :cond_5

    goto/16 :goto_2

    :cond_5
    iget p0, p0, Landroidx/media3/common/PlaybackException;->errorCode:I

    const/16 p1, 0x3ea

    const/16 p2, 0x15

    if-ne p0, p1, :cond_6

    new-instance p0, Lj2/v3$a;

    invoke-direct {p0, p2, v2}, Lj2/v3$a;-><init>(II)V

    return-object p0

    :cond_6
    instance-of p0, v4, Landroidx/media3/exoplayer/drm/DrmSession$DrmSessionException;

    if-eqz p0, :cond_d

    invoke-virtual {v4}, Ljava/lang/Throwable;->getCause()Ljava/lang/Throwable;

    move-result-object p0

    invoke-static {p0}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    check-cast p0, Ljava/lang/Throwable;

    sget p1, Le2/u0;->a:I

    if-lt p1, p2, :cond_7

    instance-of p2, p0, Landroid/media/MediaDrm$MediaDrmStateException;

    if-eqz p2, :cond_7

    check-cast p0, Landroid/media/MediaDrm$MediaDrmStateException;

    invoke-virtual {p0}, Landroid/media/MediaDrm$MediaDrmStateException;->getDiagnosticInfo()Ljava/lang/String;

    move-result-object p0

    invoke-static {p0}, Le2/u0;->c0(Ljava/lang/String;)I

    move-result p0

    invoke-static {p0}, Lj2/v3;->A0(I)I

    move-result p1

    new-instance p2, Lj2/v3$a;

    invoke-direct {p2, p1, p0}, Lj2/v3$a;-><init>(II)V

    return-object p2

    :cond_7
    if-lt p1, v8, :cond_8

    invoke-static {p0}, Lj2/p3;->a(Ljava/lang/Object;)Z

    move-result p2

    if-eqz p2, :cond_8

    new-instance p0, Lj2/v3$a;

    const/16 p1, 0x1b

    invoke-direct {p0, p1, v2}, Lj2/v3$a;-><init>(II)V

    return-object p0

    :cond_8
    if-lt p1, v7, :cond_9

    instance-of p2, p0, Landroid/media/NotProvisionedException;

    if-eqz p2, :cond_9

    new-instance p0, Lj2/v3$a;

    const/16 p1, 0x18

    invoke-direct {p0, p1, v2}, Lj2/v3$a;-><init>(II)V

    return-object p0

    :cond_9
    if-lt p1, v7, :cond_a

    instance-of p1, p0, Landroid/media/DeniedByServerException;

    if-eqz p1, :cond_a

    new-instance p0, Lj2/v3$a;

    const/16 p1, 0x1d

    invoke-direct {p0, p1, v2}, Lj2/v3$a;-><init>(II)V

    return-object p0

    :cond_a
    instance-of p1, p0, Landroidx/media3/exoplayer/drm/UnsupportedDrmException;

    if-eqz p1, :cond_b

    new-instance p0, Lj2/v3$a;

    invoke-direct {p0, v8, v2}, Lj2/v3$a;-><init>(II)V

    return-object p0

    :cond_b
    instance-of p0, p0, Landroidx/media3/exoplayer/drm/DefaultDrmSessionManager$MissingSchemeDataException;

    if-eqz p0, :cond_c

    new-instance p0, Lj2/v3$a;

    const/16 p1, 0x1c

    invoke-direct {p0, p1, v2}, Lj2/v3$a;-><init>(II)V

    return-object p0

    :cond_c
    new-instance p0, Lj2/v3$a;

    const/16 p1, 0x1e

    invoke-direct {p0, p1, v2}, Lj2/v3$a;-><init>(II)V

    return-object p0

    :cond_d
    instance-of p0, v4, Landroidx/media3/datasource/FileDataSource$FileDataSourceException;

    if-eqz p0, :cond_f

    invoke-virtual {v4}, Ljava/lang/Throwable;->getCause()Ljava/lang/Throwable;

    move-result-object p0

    instance-of p0, p0, Ljava/io/FileNotFoundException;

    if-eqz p0, :cond_f

    invoke-virtual {v4}, Ljava/lang/Throwable;->getCause()Ljava/lang/Throwable;

    move-result-object p0

    invoke-static {p0}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    check-cast p0, Ljava/lang/Throwable;

    invoke-virtual {p0}, Ljava/lang/Throwable;->getCause()Ljava/lang/Throwable;

    move-result-object p0

    sget p1, Le2/u0;->a:I

    if-lt p1, p2, :cond_e

    instance-of p1, p0, Landroid/system/ErrnoException;

    if-eqz p1, :cond_e

    check-cast p0, Landroid/system/ErrnoException;

    iget p0, p0, Landroid/system/ErrnoException;->errno:I

    sget p1, Landroid/system/OsConstants;->EACCES:I

    if-ne p0, p1, :cond_e

    new-instance p0, Lj2/v3$a;

    const/16 p1, 0x20

    invoke-direct {p0, p1, v2}, Lj2/v3$a;-><init>(II)V

    return-object p0

    :cond_e
    new-instance p0, Lj2/v3$a;

    const/16 p1, 0x1f

    invoke-direct {p0, p1, v2}, Lj2/v3$a;-><init>(II)V

    return-object p0

    :cond_f
    new-instance p0, Lj2/v3$a;

    const/16 p1, 0x9

    invoke-direct {p0, p1, v2}, Lj2/v3$a;-><init>(II)V

    return-object p0

    :cond_10
    :goto_2
    invoke-static {p1}, Le2/t;->d(Landroid/content/Context;)Le2/t;

    move-result-object p0

    invoke-virtual {p0}, Le2/t;->f()I

    move-result p0

    if-ne p0, v1, :cond_11

    new-instance p0, Lj2/v3$a;

    invoke-direct {p0, v6, v2}, Lj2/v3$a;-><init>(II)V

    return-object p0

    :cond_11
    invoke-virtual {v4}, Ljava/lang/Throwable;->getCause()Ljava/lang/Throwable;

    move-result-object p0

    instance-of p1, p0, Ljava/net/UnknownHostException;

    if-eqz p1, :cond_12

    new-instance p0, Lj2/v3$a;

    const/4 p1, 0x6

    invoke-direct {p0, p1, v2}, Lj2/v3$a;-><init>(II)V

    return-object p0

    :cond_12
    instance-of p0, p0, Ljava/net/SocketTimeoutException;

    if-eqz p0, :cond_13

    new-instance p0, Lj2/v3$a;

    const/4 p1, 0x7

    invoke-direct {p0, p1, v2}, Lj2/v3$a;-><init>(II)V

    return-object p0

    :cond_13
    instance-of p0, v4, Landroidx/media3/datasource/HttpDataSource$HttpDataSourceException;

    if-eqz p0, :cond_14

    check-cast v4, Landroidx/media3/datasource/HttpDataSource$HttpDataSourceException;

    iget p0, v4, Landroidx/media3/datasource/HttpDataSource$HttpDataSourceException;->type:I

    if-ne p0, v1, :cond_14

    new-instance p0, Lj2/v3$a;

    const/4 p1, 0x4

    invoke-direct {p0, p1, v2}, Lj2/v3$a;-><init>(II)V

    return-object p0

    :cond_14
    new-instance p0, Lj2/v3$a;

    const/16 p1, 0x8

    invoke-direct {p0, p1, v2}, Lj2/v3$a;-><init>(II)V

    return-object p0

    :cond_15
    :goto_3
    new-instance p0, Lj2/v3$a;

    if-eqz p2, :cond_16

    const/16 p1, 0xa

    goto :goto_4

    :cond_16
    const/16 p1, 0xb

    :goto_4
    invoke-direct {p0, p1, v2}, Lj2/v3$a;-><init>(II)V

    return-object p0

    :cond_17
    if-eqz v3, :cond_19

    if-eqz v0, :cond_18

    if-ne v0, v1, :cond_19

    :cond_18
    new-instance p0, Lj2/v3$a;

    const/16 p1, 0x23

    invoke-direct {p0, p1, v2}, Lj2/v3$a;-><init>(II)V

    return-object p0

    :cond_19
    if-eqz v3, :cond_1a

    if-ne v0, v6, :cond_1a

    new-instance p0, Lj2/v3$a;

    const/16 p1, 0xf

    invoke-direct {p0, p1, v2}, Lj2/v3$a;-><init>(II)V

    return-object p0

    :cond_1a
    if-eqz v3, :cond_1b

    const/4 p0, 0x2

    if-ne v0, p0, :cond_1b

    new-instance p0, Lj2/v3$a;

    invoke-direct {p0, v8, v2}, Lj2/v3$a;-><init>(II)V

    return-object p0

    :cond_1b
    instance-of p0, v4, Landroidx/media3/exoplayer/mediacodec/MediaCodecRenderer$DecoderInitializationException;

    if-eqz p0, :cond_1c

    check-cast v4, Landroidx/media3/exoplayer/mediacodec/MediaCodecRenderer$DecoderInitializationException;

    iget-object p0, v4, Landroidx/media3/exoplayer/mediacodec/MediaCodecRenderer$DecoderInitializationException;->diagnosticInfo:Ljava/lang/String;

    invoke-static {p0}, Le2/u0;->c0(Ljava/lang/String;)I

    move-result p0

    new-instance p1, Lj2/v3$a;

    const/16 p2, 0xd

    invoke-direct {p1, p2, p0}, Lj2/v3$a;-><init>(II)V

    return-object p1

    :cond_1c
    instance-of p0, v4, Landroidx/media3/exoplayer/mediacodec/MediaCodecDecoderException;

    const/16 p1, 0xe

    if-eqz p0, :cond_1d

    check-cast v4, Landroidx/media3/exoplayer/mediacodec/MediaCodecDecoderException;

    iget-object p0, v4, Landroidx/media3/exoplayer/mediacodec/MediaCodecDecoderException;->diagnosticInfo:Ljava/lang/String;

    invoke-static {p0}, Le2/u0;->c0(Ljava/lang/String;)I

    move-result p0

    new-instance p2, Lj2/v3$a;

    invoke-direct {p2, p1, p0}, Lj2/v3$a;-><init>(II)V

    return-object p2

    :cond_1d
    instance-of p0, v4, Ljava/lang/OutOfMemoryError;

    if-eqz p0, :cond_1e

    new-instance p0, Lj2/v3$a;

    invoke-direct {p0, p1, v2}, Lj2/v3$a;-><init>(II)V

    return-object p0

    :cond_1e
    instance-of p0, v4, Landroidx/media3/exoplayer/audio/AudioSink$InitializationException;

    if-eqz p0, :cond_1f

    check-cast v4, Landroidx/media3/exoplayer/audio/AudioSink$InitializationException;

    iget p0, v4, Landroidx/media3/exoplayer/audio/AudioSink$InitializationException;->audioTrackState:I

    new-instance p1, Lj2/v3$a;

    const/16 p2, 0x11

    invoke-direct {p1, p2, p0}, Lj2/v3$a;-><init>(II)V

    return-object p1

    :cond_1f
    instance-of p0, v4, Landroidx/media3/exoplayer/audio/AudioSink$WriteException;

    if-eqz p0, :cond_20

    check-cast v4, Landroidx/media3/exoplayer/audio/AudioSink$WriteException;

    iget p0, v4, Landroidx/media3/exoplayer/audio/AudioSink$WriteException;->errorCode:I

    new-instance p1, Lj2/v3$a;

    invoke-direct {p1, v7, p0}, Lj2/v3$a;-><init>(II)V

    return-object p1

    :cond_20
    sget p0, Le2/u0;->a:I

    const/16 p1, 0x10

    if-lt p0, p1, :cond_21

    instance-of p0, v4, Landroid/media/MediaCodec$CryptoException;

    if-eqz p0, :cond_21

    check-cast v4, Landroid/media/MediaCodec$CryptoException;

    invoke-virtual {v4}, Landroid/media/MediaCodec$CryptoException;->getErrorCode()I

    move-result p0

    invoke-static {p0}, Lj2/v3;->A0(I)I

    move-result p1

    new-instance p2, Lj2/v3$a;

    invoke-direct {p2, p1, p0}, Lj2/v3$a;-><init>(II)V

    return-object p2

    :cond_21
    new-instance p0, Lj2/v3$a;

    const/16 p1, 0x16

    invoke-direct {p0, p1, v2}, Lj2/v3$a;-><init>(II)V

    return-object p0
.end method

.method public static E0(Ljava/lang/String;)Landroid/util/Pair;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            ")",
            "Landroid/util/Pair<",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    const-string v0, "-"

    invoke-static {p0, v0}, Le2/u0;->o1(Ljava/lang/String;Ljava/lang/String;)[Ljava/lang/String;

    move-result-object p0

    const/4 v0, 0x0

    aget-object v0, p0, v0

    array-length v1, p0

    const/4 v2, 0x2

    if-lt v1, v2, :cond_0

    const/4 v1, 0x1

    aget-object p0, p0, v1

    goto :goto_0

    :cond_0
    const/4 p0, 0x0

    :goto_0
    invoke-static {v0, p0}, Landroid/util/Pair;->create(Ljava/lang/Object;Ljava/lang/Object;)Landroid/util/Pair;

    move-result-object p0

    return-object p0
.end method

.method public static G0(Landroid/content/Context;)I
    .locals 0

    invoke-static {p0}, Le2/t;->d(Landroid/content/Context;)Le2/t;

    move-result-object p0

    invoke-virtual {p0}, Le2/t;->f()I

    move-result p0

    packed-switch p0, :pswitch_data_0

    :pswitch_0
    const/4 p0, 0x1

    return p0

    :pswitch_1
    const/4 p0, 0x7

    return p0

    :pswitch_2
    const/16 p0, 0x8

    return p0

    :pswitch_3
    const/4 p0, 0x3

    return p0

    :pswitch_4
    const/4 p0, 0x6

    return p0

    :pswitch_5
    const/4 p0, 0x5

    return p0

    :pswitch_6
    const/4 p0, 0x4

    return p0

    :pswitch_7
    const/4 p0, 0x2

    return p0

    :pswitch_8
    const/16 p0, 0x9

    return p0

    :pswitch_9
    const/4 p0, 0x0

    return p0

    nop

    :pswitch_data_0
    .packed-switch 0x0
        :pswitch_9
        :pswitch_8
        :pswitch_7
        :pswitch_6
        :pswitch_5
        :pswitch_4
        :pswitch_0
        :pswitch_3
        :pswitch_0
        :pswitch_2
        :pswitch_1
    .end packed-switch
.end method

.method public static H0(Landroidx/media3/common/b0;)I
    .locals 2

    iget-object p0, p0, Landroidx/media3/common/b0;->b:Landroidx/media3/common/b0$h;

    if-nez p0, :cond_0

    const/4 p0, 0x0

    return p0

    :cond_0
    iget-object v0, p0, Landroidx/media3/common/b0$h;->a:Landroid/net/Uri;

    iget-object p0, p0, Landroidx/media3/common/b0$h;->b:Ljava/lang/String;

    invoke-static {v0, p0}, Le2/u0;->C0(Landroid/net/Uri;Ljava/lang/String;)I

    move-result p0

    if-eqz p0, :cond_3

    const/4 v0, 0x1

    if-eq p0, v0, :cond_2

    const/4 v1, 0x2

    if-eq p0, v1, :cond_1

    return v0

    :cond_1
    const/4 p0, 0x4

    return p0

    :cond_2
    const/4 p0, 0x5

    return p0

    :cond_3
    const/4 p0, 0x3

    return p0
.end method

.method public static I0(I)I
    .locals 3

    const/4 v0, 0x2

    const/4 v1, 0x1

    if-eq p0, v1, :cond_2

    const/4 v2, 0x3

    if-eq p0, v0, :cond_1

    if-eq p0, v2, :cond_0

    return v1

    :cond_0
    const/4 p0, 0x4

    return p0

    :cond_1
    return v2

    :cond_2
    return v0
.end method

.method public static y0(Landroid/content/Context;)Lj2/v3;
    .locals 2
    .annotation build Landroidx/annotation/Nullable;
    .end annotation

    const-string v0, "media_metrics"

    invoke-virtual {p0, v0}, Landroid/content/Context;->getSystemService(Ljava/lang/String;)Ljava/lang/Object;

    move-result-object v0

    invoke-static {v0}, Lj2/q3;->a(Ljava/lang/Object;)Landroid/media/metrics/MediaMetricsManager;

    move-result-object v0

    if-nez v0, :cond_0

    const/4 p0, 0x0

    goto :goto_0

    :cond_0
    new-instance v1, Lj2/v3;

    invoke-static {v0}, Lj2/r3;->a(Landroid/media/metrics/MediaMetricsManager;)Landroid/media/metrics/PlaybackSession;

    move-result-object v0

    invoke-direct {v1, p0, v0}, Lj2/v3;-><init>(Landroid/content/Context;Landroid/media/metrics/PlaybackSession;)V

    move-object p0, v1

    :goto_0
    return-object p0
.end method


# virtual methods
.method public synthetic A(Lj2/c$a;IJJ)V
    .locals 0

    invoke-static/range {p0 .. p6}, Lj2/b;->m(Lj2/c;Lj2/c$a;IJJ)V

    return-void
.end method

.method public synthetic B(Lj2/c$a;ZI)V
    .locals 0

    invoke-static {p0, p1, p2, p3}, Lj2/b;->K(Lj2/c;Lj2/c$a;ZI)V

    return-void
.end method

.method public synthetic C(Lj2/c$a;IJ)V
    .locals 0

    invoke-static {p0, p1, p2, p3, p4}, Lj2/b;->z(Lj2/c;Lj2/c$a;IJ)V

    return-void
.end method

.method public synthetic D(Lj2/c$a;)V
    .locals 0

    invoke-static {p0, p1}, Lj2/b;->u(Lj2/c;Lj2/c$a;)V

    return-void
.end method

.method public synthetic E(Lj2/c$a;Landroidx/media3/exoplayer/n;)V
    .locals 0

    invoke-static {p0, p1, p2}, Lj2/b;->e(Lj2/c;Lj2/c$a;Landroidx/media3/exoplayer/n;)V

    return-void
.end method

.method public synthetic F(Lj2/c$a;)V
    .locals 0

    invoke-static {p0, p1}, Lj2/b;->y(Lj2/c;Lj2/c$a;)V

    return-void
.end method

.method public F0()Landroid/media/metrics/LogSessionId;
    .locals 1

    iget-object v0, p0, Lj2/v3;->c:Landroid/media/metrics/PlaybackSession;

    invoke-static {v0}, Lj2/k3;->a(Landroid/media/metrics/PlaybackSession;)Landroid/media/metrics/LogSessionId;

    move-result-object v0

    return-object v0
.end method

.method public G(Lj2/c$a;Landroidx/media3/common/h0$e;Landroidx/media3/common/h0$e;I)V
    .locals 0

    const/4 p1, 0x1

    if-ne p4, p1, :cond_0

    iput-boolean p1, p0, Lj2/v3;->u:Z

    :cond_0
    iput p4, p0, Lj2/v3;->k:I

    return-void
.end method

.method public synthetic H(Lj2/c$a;Z)V
    .locals 0

    invoke-static {p0, p1, p2}, Lj2/b;->W(Lj2/c;Lj2/c$a;Z)V

    return-void
.end method

.method public synthetic I(Lj2/c$a;Ljava/util/List;)V
    .locals 0

    invoke-static {p0, p1, p2}, Lj2/b;->p(Lj2/c;Lj2/c$a;Ljava/util/List;)V

    return-void
.end method

.method public J(Lj2/c$a;Landroidx/media3/common/PlaybackException;)V
    .locals 0

    iput-object p2, p0, Lj2/v3;->n:Landroidx/media3/common/PlaybackException;

    return-void
.end method

.method public final J0(Lj2/c$b;)V
    .locals 4

    const/4 v0, 0x0

    :goto_0
    invoke-virtual {p1}, Lj2/c$b;->d()I

    move-result v1

    if-ge v0, v1, :cond_2

    invoke-virtual {p1, v0}, Lj2/c$b;->b(I)I

    move-result v1

    invoke-virtual {p1, v1}, Lj2/c$b;->c(I)Lj2/c$a;

    move-result-object v2

    if-nez v1, :cond_0

    iget-object v1, p0, Lj2/v3;->b:Lj2/w3;

    invoke-interface {v1, v2}, Lj2/w3;->g(Lj2/c$a;)V

    goto :goto_1

    :cond_0
    const/16 v3, 0xb

    if-ne v1, v3, :cond_1

    iget-object v1, p0, Lj2/v3;->b:Lj2/w3;

    iget v3, p0, Lj2/v3;->k:I

    invoke-interface {v1, v2, v3}, Lj2/w3;->e(Lj2/c$a;I)V

    goto :goto_1

    :cond_1
    iget-object v1, p0, Lj2/v3;->b:Lj2/w3;

    invoke-interface {v1, v2}, Lj2/w3;->c(Lj2/c$a;)V

    :goto_1
    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :cond_2
    return-void
.end method

.method public synthetic K(Lj2/c$a;Lu2/n;Lu2/o;)V
    .locals 0

    invoke-static {p0, p1, p2, p3}, Lj2/b;->E(Lj2/c;Lj2/c$a;Lu2/n;Lu2/o;)V

    return-void
.end method

.method public final K0(J)V
    .locals 4

    iget-object v0, p0, Lj2/v3;->a:Landroid/content/Context;

    invoke-static {v0}, Lj2/v3;->G0(Landroid/content/Context;)I

    move-result v0

    iget v1, p0, Lj2/v3;->m:I

    if-eq v0, v1, :cond_0

    iput v0, p0, Lj2/v3;->m:I

    iget-object v1, p0, Lj2/v3;->c:Landroid/media/metrics/PlaybackSession;

    invoke-static {}, Lj2/l3;->a()Landroid/media/metrics/NetworkEvent$Builder;

    move-result-object v2

    invoke-static {v2, v0}, Lj2/g3;->a(Landroid/media/metrics/NetworkEvent$Builder;I)Landroid/media/metrics/NetworkEvent$Builder;

    move-result-object v0

    iget-wide v2, p0, Lj2/v3;->d:J

    sub-long/2addr p1, v2

    invoke-static {v0, p1, p2}, Lj2/h3;->a(Landroid/media/metrics/NetworkEvent$Builder;J)Landroid/media/metrics/NetworkEvent$Builder;

    move-result-object p1

    invoke-static {p1}, Lj2/i3;->a(Landroid/media/metrics/NetworkEvent$Builder;)Landroid/media/metrics/NetworkEvent;

    move-result-object p1

    invoke-static {v1, p1}, Lj2/j3;->a(Landroid/media/metrics/PlaybackSession;Landroid/media/metrics/NetworkEvent;)V

    :cond_0
    return-void
.end method

.method public synthetic L(Lj2/c$a;Landroidx/media3/common/y;)V
    .locals 0

    invoke-static {p0, p1, p2}, Lj2/b;->i0(Lj2/c;Lj2/c$a;Landroidx/media3/common/y;)V

    return-void
.end method

.method public final L0(J)V
    .locals 7

    iget-object v0, p0, Lj2/v3;->n:Landroidx/media3/common/PlaybackException;

    if-nez v0, :cond_0

    return-void

    :cond_0
    iget-object v1, p0, Lj2/v3;->a:Landroid/content/Context;

    iget v2, p0, Lj2/v3;->v:I

    const/4 v3, 0x4

    const/4 v4, 0x1

    if-ne v2, v3, :cond_1

    const/4 v2, 0x1

    goto :goto_0

    :cond_1
    const/4 v2, 0x0

    :goto_0
    invoke-static {v0, v1, v2}, Lj2/v3;->D0(Landroidx/media3/common/PlaybackException;Landroid/content/Context;Z)Lj2/v3$a;

    move-result-object v1

    iget-object v2, p0, Lj2/v3;->c:Landroid/media/metrics/PlaybackSession;

    invoke-static {}, Lj2/t1;->a()Landroid/media/metrics/PlaybackErrorEvent$Builder;

    move-result-object v3

    iget-wide v5, p0, Lj2/v3;->d:J

    sub-long/2addr p1, v5

    invoke-static {v3, p1, p2}, Lj2/s3;->a(Landroid/media/metrics/PlaybackErrorEvent$Builder;J)Landroid/media/metrics/PlaybackErrorEvent$Builder;

    move-result-object p1

    iget p2, v1, Lj2/v3$a;->a:I

    invoke-static {p1, p2}, Lj2/t3;->a(Landroid/media/metrics/PlaybackErrorEvent$Builder;I)Landroid/media/metrics/PlaybackErrorEvent$Builder;

    move-result-object p1

    iget p2, v1, Lj2/v3$a;->b:I

    invoke-static {p1, p2}, Lj2/u3;->a(Landroid/media/metrics/PlaybackErrorEvent$Builder;I)Landroid/media/metrics/PlaybackErrorEvent$Builder;

    move-result-object p1

    invoke-static {p1, v0}, Lj2/u1;->a(Landroid/media/metrics/PlaybackErrorEvent$Builder;Ljava/lang/Exception;)Landroid/media/metrics/PlaybackErrorEvent$Builder;

    move-result-object p1

    invoke-static {p1}, Lj2/v1;->a(Landroid/media/metrics/PlaybackErrorEvent$Builder;)Landroid/media/metrics/PlaybackErrorEvent;

    move-result-object p1

    invoke-static {v2, p1}, Lj2/w1;->a(Landroid/media/metrics/PlaybackSession;Landroid/media/metrics/PlaybackErrorEvent;)V

    iput-boolean v4, p0, Lj2/v3;->A:Z

    const/4 p1, 0x0

    iput-object p1, p0, Lj2/v3;->n:Landroidx/media3/common/PlaybackException;

    return-void
.end method

.method public synthetic M(Lj2/c$a;Landroidx/media3/common/d0;)V
    .locals 0

    invoke-static {p0, p1, p2}, Lj2/b;->I(Lj2/c;Lj2/c$a;Landroidx/media3/common/d0;)V

    return-void
.end method

.method public final M0(Landroidx/media3/common/h0;Lj2/c$b;J)V
    .locals 3

    invoke-interface {p1}, Landroidx/media3/common/h0;->getPlaybackState()I

    move-result v0

    const/4 v1, 0x2

    const/4 v2, 0x0

    if-eq v0, v1, :cond_0

    iput-boolean v2, p0, Lj2/v3;->u:Z

    :cond_0
    invoke-interface {p1}, Landroidx/media3/common/h0;->c()Landroidx/media3/common/PlaybackException;

    move-result-object v0

    const/4 v1, 0x1

    if-nez v0, :cond_1

    iput-boolean v2, p0, Lj2/v3;->w:Z

    goto :goto_0

    :cond_1
    const/16 v0, 0xa

    invoke-virtual {p2, v0}, Lj2/c$b;->a(I)Z

    move-result p2

    if-eqz p2, :cond_2

    iput-boolean v1, p0, Lj2/v3;->w:Z

    :cond_2
    :goto_0
    invoke-virtual {p0, p1}, Lj2/v3;->U0(Landroidx/media3/common/h0;)I

    move-result p1

    iget p2, p0, Lj2/v3;->l:I

    if-eq p2, p1, :cond_3

    iput p1, p0, Lj2/v3;->l:I

    iput-boolean v1, p0, Lj2/v3;->A:Z

    iget-object p1, p0, Lj2/v3;->c:Landroid/media/metrics/PlaybackSession;

    invoke-static {}, Lj2/a3;->a()Landroid/media/metrics/PlaybackStateEvent$Builder;

    move-result-object p2

    iget v0, p0, Lj2/v3;->l:I

    invoke-static {p2, v0}, Lj2/c3;->a(Landroid/media/metrics/PlaybackStateEvent$Builder;I)Landroid/media/metrics/PlaybackStateEvent$Builder;

    move-result-object p2

    iget-wide v0, p0, Lj2/v3;->d:J

    sub-long/2addr p3, v0

    invoke-static {p2, p3, p4}, Lj2/d3;->a(Landroid/media/metrics/PlaybackStateEvent$Builder;J)Landroid/media/metrics/PlaybackStateEvent$Builder;

    move-result-object p2

    invoke-static {p2}, Lj2/e3;->a(Landroid/media/metrics/PlaybackStateEvent$Builder;)Landroid/media/metrics/PlaybackStateEvent;

    move-result-object p2

    invoke-static {p1, p2}, Lj2/f3;->a(Landroid/media/metrics/PlaybackSession;Landroid/media/metrics/PlaybackStateEvent;)V

    :cond_3
    return-void
.end method

.method public synthetic N(Lj2/c$a;I)V
    .locals 0

    invoke-static {p0, p1, p2}, Lj2/b;->Y(Lj2/c;Lj2/c$a;I)V

    return-void
.end method

.method public final N0(Landroidx/media3/common/h0;Lj2/c$b;J)V
    .locals 3

    const/4 v0, 0x2

    invoke-virtual {p2, v0}, Lj2/c$b;->a(I)Z

    move-result p2

    const/4 v1, 0x0

    if-eqz p2, :cond_3

    invoke-interface {p1}, Landroidx/media3/common/h0;->getCurrentTracks()Landroidx/media3/common/q0;

    move-result-object p1

    invoke-virtual {p1, v0}, Landroidx/media3/common/q0;->c(I)Z

    move-result p2

    const/4 v0, 0x1

    invoke-virtual {p1, v0}, Landroidx/media3/common/q0;->c(I)Z

    move-result v0

    const/4 v2, 0x3

    invoke-virtual {p1, v2}, Landroidx/media3/common/q0;->c(I)Z

    move-result p1

    if-nez p2, :cond_0

    if-nez v0, :cond_0

    if-eqz p1, :cond_3

    :cond_0
    const/4 v2, 0x0

    if-nez p2, :cond_1

    invoke-virtual {p0, p3, p4, v1, v2}, Lj2/v3;->S0(JLandroidx/media3/common/y;I)V

    :cond_1
    if-nez v0, :cond_2

    invoke-virtual {p0, p3, p4, v1, v2}, Lj2/v3;->O0(JLandroidx/media3/common/y;I)V

    :cond_2
    if-nez p1, :cond_3

    invoke-virtual {p0, p3, p4, v1, v2}, Lj2/v3;->Q0(JLandroidx/media3/common/y;I)V

    :cond_3
    iget-object p1, p0, Lj2/v3;->o:Lj2/v3$b;

    invoke-virtual {p0, p1}, Lj2/v3;->x0(Lj2/v3$b;)Z

    move-result p1

    if-eqz p1, :cond_4

    iget-object p1, p0, Lj2/v3;->o:Lj2/v3$b;

    iget-object p2, p1, Lj2/v3$b;->a:Landroidx/media3/common/y;

    iget v0, p2, Landroidx/media3/common/y;->s:I

    const/4 v2, -0x1

    if-eq v0, v2, :cond_4

    iget p1, p1, Lj2/v3$b;->b:I

    invoke-virtual {p0, p3, p4, p2, p1}, Lj2/v3;->S0(JLandroidx/media3/common/y;I)V

    iput-object v1, p0, Lj2/v3;->o:Lj2/v3$b;

    :cond_4
    iget-object p1, p0, Lj2/v3;->p:Lj2/v3$b;

    invoke-virtual {p0, p1}, Lj2/v3;->x0(Lj2/v3$b;)Z

    move-result p1

    if-eqz p1, :cond_5

    iget-object p1, p0, Lj2/v3;->p:Lj2/v3$b;

    iget-object p2, p1, Lj2/v3$b;->a:Landroidx/media3/common/y;

    iget p1, p1, Lj2/v3$b;->b:I

    invoke-virtual {p0, p3, p4, p2, p1}, Lj2/v3;->O0(JLandroidx/media3/common/y;I)V

    iput-object v1, p0, Lj2/v3;->p:Lj2/v3$b;

    :cond_5
    iget-object p1, p0, Lj2/v3;->q:Lj2/v3$b;

    invoke-virtual {p0, p1}, Lj2/v3;->x0(Lj2/v3$b;)Z

    move-result p1

    if-eqz p1, :cond_6

    iget-object p1, p0, Lj2/v3;->q:Lj2/v3$b;

    iget-object p2, p1, Lj2/v3$b;->a:Landroidx/media3/common/y;

    iget p1, p1, Lj2/v3$b;->b:I

    invoke-virtual {p0, p3, p4, p2, p1}, Lj2/v3;->Q0(JLandroidx/media3/common/y;I)V

    iput-object v1, p0, Lj2/v3;->q:Lj2/v3$b;

    :cond_6
    return-void
.end method

.method public synthetic O(Lj2/c$a;Landroidx/media3/exoplayer/n;)V
    .locals 0

    invoke-static {p0, p1, p2}, Lj2/b;->g0(Lj2/c;Lj2/c$a;Landroidx/media3/exoplayer/n;)V

    return-void
.end method

.method public final O0(JLandroidx/media3/common/y;I)V
    .locals 6
    .param p3    # Landroidx/media3/common/y;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    iget-object v0, p0, Lj2/v3;->s:Landroidx/media3/common/y;

    invoke-static {v0, p3}, Le2/u0;->c(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_0

    return-void

    :cond_0
    iget-object v0, p0, Lj2/v3;->s:Landroidx/media3/common/y;

    if-nez v0, :cond_1

    if-nez p4, :cond_1

    const/4 p4, 0x1

    const/4 v5, 0x1

    goto :goto_0

    :cond_1
    move v5, p4

    :goto_0
    iput-object p3, p0, Lj2/v3;->s:Landroidx/media3/common/y;

    const/4 v1, 0x0

    move-object v0, p0

    move-wide v2, p1

    move-object v4, p3

    invoke-virtual/range {v0 .. v5}, Lj2/v3;->T0(IJLandroidx/media3/common/y;I)V

    return-void
.end method

.method public synthetic P(Lj2/c$a;)V
    .locals 0

    invoke-static {p0, p1}, Lj2/b;->U(Lj2/c;Lj2/c$a;)V

    return-void
.end method

.method public final P0(Landroidx/media3/common/h0;Lj2/c$b;)V
    .locals 2

    const/4 v0, 0x0

    invoke-virtual {p2, v0}, Lj2/c$b;->a(I)Z

    move-result v1

    if-eqz v1, :cond_0

    invoke-virtual {p2, v0}, Lj2/c$b;->c(I)Lj2/c$a;

    move-result-object v0

    iget-object v1, p0, Lj2/v3;->j:Landroid/media/metrics/PlaybackMetrics$Builder;

    if-eqz v1, :cond_0

    iget-object v1, v0, Lj2/c$a;->b:Landroidx/media3/common/m0;

    iget-object v0, v0, Lj2/c$a;->d:Landroidx/media3/exoplayer/source/l$b;

    invoke-virtual {p0, v1, v0}, Lj2/v3;->R0(Landroidx/media3/common/m0;Landroidx/media3/exoplayer/source/l$b;)V

    :cond_0
    const/4 v0, 0x2

    invoke-virtual {p2, v0}, Lj2/c$b;->a(I)Z

    move-result v0

    if-eqz v0, :cond_1

    iget-object v0, p0, Lj2/v3;->j:Landroid/media/metrics/PlaybackMetrics$Builder;

    if-eqz v0, :cond_1

    invoke-interface {p1}, Landroidx/media3/common/h0;->getCurrentTracks()Landroidx/media3/common/q0;

    move-result-object p1

    invoke-virtual {p1}, Landroidx/media3/common/q0;->a()Lcom/google/common/collect/ImmutableList;

    move-result-object p1

    invoke-static {p1}, Lj2/v3;->B0(Lcom/google/common/collect/ImmutableList;)Landroidx/media3/common/DrmInitData;

    move-result-object p1

    if-eqz p1, :cond_1

    iget-object v0, p0, Lj2/v3;->j:Landroid/media/metrics/PlaybackMetrics$Builder;

    invoke-static {v0}, Le2/u0;->i(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    invoke-static {v0}, Lj2/o2;->a(Ljava/lang/Object;)Landroid/media/metrics/PlaybackMetrics$Builder;

    move-result-object v0

    invoke-static {p1}, Lj2/v3;->C0(Landroidx/media3/common/DrmInitData;)I

    move-result p1

    invoke-static {v0, p1}, Lj2/q2;->a(Landroid/media/metrics/PlaybackMetrics$Builder;I)Landroid/media/metrics/PlaybackMetrics$Builder;

    :cond_1
    const/16 p1, 0x3f3

    invoke-virtual {p2, p1}, Lj2/c$b;->a(I)Z

    move-result p1

    if-eqz p1, :cond_2

    iget p1, p0, Lj2/v3;->z:I

    add-int/lit8 p1, p1, 0x1

    iput p1, p0, Lj2/v3;->z:I

    :cond_2
    return-void
.end method

.method public synthetic Q(Lj2/c$a;IZ)V
    .locals 0

    invoke-static {p0, p1, p2, p3}, Lj2/b;->r(Lj2/c;Lj2/c$a;IZ)V

    return-void
.end method

.method public final Q0(JLandroidx/media3/common/y;I)V
    .locals 6
    .param p3    # Landroidx/media3/common/y;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    iget-object v0, p0, Lj2/v3;->t:Landroidx/media3/common/y;

    invoke-static {v0, p3}, Le2/u0;->c(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_0

    return-void

    :cond_0
    iget-object v0, p0, Lj2/v3;->t:Landroidx/media3/common/y;

    if-nez v0, :cond_1

    if-nez p4, :cond_1

    const/4 p4, 0x1

    const/4 v5, 0x1

    goto :goto_0

    :cond_1
    move v5, p4

    :goto_0
    iput-object p3, p0, Lj2/v3;->t:Landroidx/media3/common/y;

    const/4 v1, 0x2

    move-object v0, p0

    move-wide v2, p1

    move-object v4, p3

    invoke-virtual/range {v0 .. v5}, Lj2/v3;->T0(IJLandroidx/media3/common/y;I)V

    return-void
.end method

.method public synthetic R(Lj2/c$a;Landroidx/media3/common/p0;)V
    .locals 0

    invoke-static {p0, p1, p2}, Lj2/b;->Z(Lj2/c;Lj2/c$a;Landroidx/media3/common/p0;)V

    return-void
.end method

.method public final R0(Landroidx/media3/common/m0;Landroidx/media3/exoplayer/source/l$b;)V
    .locals 5
    .param p2    # Landroidx/media3/exoplayer/source/l$b;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    iget-object v0, p0, Lj2/v3;->j:Landroid/media/metrics/PlaybackMetrics$Builder;

    if-nez p2, :cond_0

    return-void

    :cond_0
    iget-object p2, p2, Landroidx/media3/exoplayer/source/l$b;->a:Ljava/lang/Object;

    invoke-virtual {p1, p2}, Landroidx/media3/common/m0;->b(Ljava/lang/Object;)I

    move-result p2

    const/4 v1, -0x1

    if-ne p2, v1, :cond_1

    return-void

    :cond_1
    iget-object v1, p0, Lj2/v3;->f:Landroidx/media3/common/m0$b;

    invoke-virtual {p1, p2, v1}, Landroidx/media3/common/m0;->f(ILandroidx/media3/common/m0$b;)Landroidx/media3/common/m0$b;

    iget-object p2, p0, Lj2/v3;->f:Landroidx/media3/common/m0$b;

    iget p2, p2, Landroidx/media3/common/m0$b;->c:I

    iget-object v1, p0, Lj2/v3;->e:Landroidx/media3/common/m0$c;

    invoke-virtual {p1, p2, v1}, Landroidx/media3/common/m0;->n(ILandroidx/media3/common/m0$c;)Landroidx/media3/common/m0$c;

    iget-object p1, p0, Lj2/v3;->e:Landroidx/media3/common/m0$c;

    iget-object p1, p1, Landroidx/media3/common/m0$c;->c:Landroidx/media3/common/b0;

    invoke-static {p1}, Lj2/v3;->H0(Landroidx/media3/common/b0;)I

    move-result p1

    invoke-static {v0, p1}, Lj2/m3;->a(Landroid/media/metrics/PlaybackMetrics$Builder;I)Landroid/media/metrics/PlaybackMetrics$Builder;

    iget-object p1, p0, Lj2/v3;->e:Landroidx/media3/common/m0$c;

    iget-wide v1, p1, Landroidx/media3/common/m0$c;->n:J

    const-wide v3, -0x7fffffffffffffffL    # -4.9E-324

    cmp-long p2, v1, v3

    if-eqz p2, :cond_2

    iget-boolean p2, p1, Landroidx/media3/common/m0$c;->l:Z

    if-nez p2, :cond_2

    iget-boolean p2, p1, Landroidx/media3/common/m0$c;->i:Z

    if-nez p2, :cond_2

    invoke-virtual {p1}, Landroidx/media3/common/m0$c;->f()Z

    move-result p1

    if-nez p1, :cond_2

    iget-object p1, p0, Lj2/v3;->e:Landroidx/media3/common/m0$c;

    invoke-virtual {p1}, Landroidx/media3/common/m0$c;->d()J

    move-result-wide p1

    invoke-static {v0, p1, p2}, Lj2/n3;->a(Landroid/media/metrics/PlaybackMetrics$Builder;J)Landroid/media/metrics/PlaybackMetrics$Builder;

    :cond_2
    iget-object p1, p0, Lj2/v3;->e:Landroidx/media3/common/m0$c;

    invoke-virtual {p1}, Landroidx/media3/common/m0$c;->f()Z

    move-result p1

    const/4 p2, 0x1

    if-eqz p1, :cond_3

    const/4 p1, 0x2

    goto :goto_0

    :cond_3
    const/4 p1, 0x1

    :goto_0
    invoke-static {v0, p1}, Lj2/o3;->a(Landroid/media/metrics/PlaybackMetrics$Builder;I)Landroid/media/metrics/PlaybackMetrics$Builder;

    iput-boolean p2, p0, Lj2/v3;->A:Z

    return-void
.end method

.method public synthetic S(Lj2/c$a;I)V
    .locals 0

    invoke-static {p0, p1, p2}, Lj2/b;->T(Lj2/c;Lj2/c$a;I)V

    return-void
.end method

.method public final S0(JLandroidx/media3/common/y;I)V
    .locals 6
    .param p3    # Landroidx/media3/common/y;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    iget-object v0, p0, Lj2/v3;->r:Landroidx/media3/common/y;

    invoke-static {v0, p3}, Le2/u0;->c(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_0

    return-void

    :cond_0
    iget-object v0, p0, Lj2/v3;->r:Landroidx/media3/common/y;

    if-nez v0, :cond_1

    if-nez p4, :cond_1

    const/4 p4, 0x1

    const/4 v5, 0x1

    goto :goto_0

    :cond_1
    move v5, p4

    :goto_0
    iput-object p3, p0, Lj2/v3;->r:Landroidx/media3/common/y;

    const/4 v1, 0x1

    move-object v0, p0

    move-wide v2, p1

    move-object v4, p3

    invoke-virtual/range {v0 .. v5}, Lj2/v3;->T0(IJLandroidx/media3/common/y;I)V

    return-void
.end method

.method public T(Lj2/c$a;Ljava/lang/String;)V
    .locals 0

    return-void
.end method

.method public final T0(IJLandroidx/media3/common/y;I)V
    .locals 2
    .param p4    # Landroidx/media3/common/y;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    invoke-static {p1}, Lj2/e2;->a(I)Landroid/media/metrics/TrackChangeEvent$Builder;

    move-result-object p1

    iget-wide v0, p0, Lj2/v3;->d:J

    sub-long/2addr p2, v0

    invoke-static {p1, p2, p3}, Lj2/x1;->a(Landroid/media/metrics/TrackChangeEvent$Builder;J)Landroid/media/metrics/TrackChangeEvent$Builder;

    move-result-object p1

    const/4 p2, 0x1

    if-eqz p4, :cond_9

    invoke-static {p1, p2}, Lj2/c2;->a(Landroid/media/metrics/TrackChangeEvent$Builder;I)Landroid/media/metrics/TrackChangeEvent$Builder;

    invoke-static {p5}, Lj2/v3;->I0(I)I

    move-result p3

    invoke-static {p1, p3}, Lj2/g2;->a(Landroid/media/metrics/TrackChangeEvent$Builder;I)Landroid/media/metrics/TrackChangeEvent$Builder;

    iget-object p3, p4, Landroidx/media3/common/y;->l:Ljava/lang/String;

    if-eqz p3, :cond_0

    invoke-static {p1, p3}, Lj2/h2;->a(Landroid/media/metrics/TrackChangeEvent$Builder;Ljava/lang/String;)Landroid/media/metrics/TrackChangeEvent$Builder;

    :cond_0
    iget-object p3, p4, Landroidx/media3/common/y;->m:Ljava/lang/String;

    if-eqz p3, :cond_1

    invoke-static {p1, p3}, Lj2/i2;->a(Landroid/media/metrics/TrackChangeEvent$Builder;Ljava/lang/String;)Landroid/media/metrics/TrackChangeEvent$Builder;

    :cond_1
    iget-object p3, p4, Landroidx/media3/common/y;->j:Ljava/lang/String;

    if-eqz p3, :cond_2

    invoke-static {p1, p3}, Lj2/j2;->a(Landroid/media/metrics/TrackChangeEvent$Builder;Ljava/lang/String;)Landroid/media/metrics/TrackChangeEvent$Builder;

    :cond_2
    iget p3, p4, Landroidx/media3/common/y;->i:I

    const/4 p5, -0x1

    if-eq p3, p5, :cond_3

    invoke-static {p1, p3}, Lj2/k2;->a(Landroid/media/metrics/TrackChangeEvent$Builder;I)Landroid/media/metrics/TrackChangeEvent$Builder;

    :cond_3
    iget p3, p4, Landroidx/media3/common/y;->r:I

    if-eq p3, p5, :cond_4

    invoke-static {p1, p3}, Lj2/l2;->a(Landroid/media/metrics/TrackChangeEvent$Builder;I)Landroid/media/metrics/TrackChangeEvent$Builder;

    :cond_4
    iget p3, p4, Landroidx/media3/common/y;->s:I

    if-eq p3, p5, :cond_5

    invoke-static {p1, p3}, Lj2/m2;->a(Landroid/media/metrics/TrackChangeEvent$Builder;I)Landroid/media/metrics/TrackChangeEvent$Builder;

    :cond_5
    iget p3, p4, Landroidx/media3/common/y;->z:I

    if-eq p3, p5, :cond_6

    invoke-static {p1, p3}, Lj2/n2;->a(Landroid/media/metrics/TrackChangeEvent$Builder;I)Landroid/media/metrics/TrackChangeEvent$Builder;

    :cond_6
    iget p3, p4, Landroidx/media3/common/y;->A:I

    if-eq p3, p5, :cond_7

    invoke-static {p1, p3}, Lj2/y1;->a(Landroid/media/metrics/TrackChangeEvent$Builder;I)Landroid/media/metrics/TrackChangeEvent$Builder;

    :cond_7
    iget-object p3, p4, Landroidx/media3/common/y;->d:Ljava/lang/String;

    if-eqz p3, :cond_8

    invoke-static {p3}, Lj2/v3;->E0(Ljava/lang/String;)Landroid/util/Pair;

    move-result-object p3

    iget-object p5, p3, Landroid/util/Pair;->first:Ljava/lang/Object;

    check-cast p5, Ljava/lang/String;

    invoke-static {p1, p5}, Lj2/z1;->a(Landroid/media/metrics/TrackChangeEvent$Builder;Ljava/lang/String;)Landroid/media/metrics/TrackChangeEvent$Builder;

    iget-object p3, p3, Landroid/util/Pair;->second:Ljava/lang/Object;

    if-eqz p3, :cond_8

    check-cast p3, Ljava/lang/String;

    invoke-static {p1, p3}, Lj2/a2;->a(Landroid/media/metrics/TrackChangeEvent$Builder;Ljava/lang/String;)Landroid/media/metrics/TrackChangeEvent$Builder;

    :cond_8
    iget p3, p4, Landroidx/media3/common/y;->t:F

    const/high16 p4, -0x40800000    # -1.0f

    cmpl-float p4, p3, p4

    if-eqz p4, :cond_a

    invoke-static {p1, p3}, Lj2/b2;->a(Landroid/media/metrics/TrackChangeEvent$Builder;F)Landroid/media/metrics/TrackChangeEvent$Builder;

    goto :goto_0

    :cond_9
    const/4 p3, 0x0

    invoke-static {p1, p3}, Lj2/c2;->a(Landroid/media/metrics/TrackChangeEvent$Builder;I)Landroid/media/metrics/TrackChangeEvent$Builder;

    :cond_a
    :goto_0
    iput-boolean p2, p0, Lj2/v3;->A:Z

    iget-object p2, p0, Lj2/v3;->c:Landroid/media/metrics/PlaybackSession;

    invoke-static {p1}, Lj2/d2;->a(Landroid/media/metrics/TrackChangeEvent$Builder;)Landroid/media/metrics/TrackChangeEvent;

    move-result-object p1

    invoke-static {p2, p1}, Lj2/f2;->a(Landroid/media/metrics/PlaybackSession;Landroid/media/metrics/TrackChangeEvent;)V

    return-void
.end method

.method public synthetic U(Lj2/c$a;Landroidx/media3/exoplayer/audio/AudioSink$a;)V
    .locals 0

    invoke-static {p0, p1, p2}, Lj2/b;->l(Lj2/c;Lj2/c$a;Landroidx/media3/exoplayer/audio/AudioSink$a;)V

    return-void
.end method

.method public final U0(Landroidx/media3/common/h0;)I
    .locals 3

    invoke-interface {p1}, Landroidx/media3/common/h0;->getPlaybackState()I

    move-result v0

    iget-boolean v1, p0, Lj2/v3;->u:Z

    if-eqz v1, :cond_0

    const/4 p1, 0x5

    return p1

    :cond_0
    iget-boolean v1, p0, Lj2/v3;->w:Z

    if-eqz v1, :cond_1

    const/16 p1, 0xd

    return p1

    :cond_1
    const/4 v1, 0x4

    if-ne v0, v1, :cond_2

    const/16 p1, 0xb

    return p1

    :cond_2
    const/4 v2, 0x2

    if-ne v0, v2, :cond_7

    iget v0, p0, Lj2/v3;->l:I

    if-eqz v0, :cond_6

    if-ne v0, v2, :cond_3

    goto :goto_1

    :cond_3
    invoke-interface {p1}, Landroidx/media3/common/h0;->getPlayWhenReady()Z

    move-result v0

    if-nez v0, :cond_4

    const/4 p1, 0x7

    return p1

    :cond_4
    invoke-interface {p1}, Landroidx/media3/common/h0;->k()I

    move-result p1

    if-eqz p1, :cond_5

    const/16 p1, 0xa

    goto :goto_0

    :cond_5
    const/4 p1, 0x6

    :goto_0
    return p1

    :cond_6
    :goto_1
    return v2

    :cond_7
    const/4 v2, 0x3

    if-ne v0, v2, :cond_a

    invoke-interface {p1}, Landroidx/media3/common/h0;->getPlayWhenReady()Z

    move-result v0

    if-nez v0, :cond_8

    return v1

    :cond_8
    invoke-interface {p1}, Landroidx/media3/common/h0;->k()I

    move-result p1

    if-eqz p1, :cond_9

    const/16 v2, 0x9

    :cond_9
    return v2

    :cond_a
    const/4 p1, 0x1

    if-ne v0, p1, :cond_b

    iget p1, p0, Lj2/v3;->l:I

    if-eqz p1, :cond_b

    const/16 p1, 0xc

    return p1

    :cond_b
    iget p1, p0, Lj2/v3;->l:I

    return p1
.end method

.method public synthetic V(Lj2/c$a;Ljava/lang/Object;J)V
    .locals 0

    invoke-static {p0, p1, p2, p3, p4}, Lj2/b;->S(Lj2/c;Lj2/c$a;Ljava/lang/Object;J)V

    return-void
.end method

.method public synthetic W(Lj2/c$a;Ld2/b;)V
    .locals 0

    invoke-static {p0, p1, p2}, Lj2/b;->o(Lj2/c;Lj2/c$a;Ld2/b;)V

    return-void
.end method

.method public synthetic X(Lj2/c$a;Ljava/lang/Exception;)V
    .locals 0

    invoke-static {p0, p1, p2}, Lj2/b;->c0(Lj2/c;Lj2/c$a;Ljava/lang/Exception;)V

    return-void
.end method

.method public synthetic Y(Lj2/c$a;I)V
    .locals 0

    invoke-static {p0, p1, p2}, Lj2/b;->M(Lj2/c;Lj2/c$a;I)V

    return-void
.end method

.method public synthetic Z(Lj2/c$a;Landroidx/media3/common/g0;)V
    .locals 0

    invoke-static {p0, p1, p2}, Lj2/b;->L(Lj2/c;Lj2/c$a;Landroidx/media3/common/g0;)V

    return-void
.end method

.method public synthetic a(Lj2/c$a;Ljava/lang/String;JJ)V
    .locals 0

    invoke-static/range {p0 .. p6}, Lj2/b;->e0(Lj2/c;Lj2/c$a;Ljava/lang/String;JJ)V

    return-void
.end method

.method public synthetic a0(Lj2/c$a;Ljava/lang/String;)V
    .locals 0

    invoke-static {p0, p1, p2}, Lj2/b;->d(Lj2/c;Lj2/c$a;Ljava/lang/String;)V

    return-void
.end method

.method public synthetic b(Lj2/c$a;Landroidx/media3/common/q0;)V
    .locals 0

    invoke-static {p0, p1, p2}, Lj2/b;->a0(Lj2/c;Lj2/c$a;Landroidx/media3/common/q0;)V

    return-void
.end method

.method public synthetic b0(Lj2/c$a;II)V
    .locals 0

    invoke-static {p0, p1, p2, p3}, Lj2/b;->X(Lj2/c;Lj2/c$a;II)V

    return-void
.end method

.method public synthetic c(Lj2/c$a;Landroidx/media3/common/y;Landroidx/media3/exoplayer/o;)V
    .locals 0

    invoke-static {p0, p1, p2, p3}, Lj2/b;->h(Lj2/c;Lj2/c$a;Landroidx/media3/common/y;Landroidx/media3/exoplayer/o;)V

    return-void
.end method

.method public synthetic c0(Lj2/c$a;Z)V
    .locals 0

    invoke-static {p0, p1, p2}, Lj2/b;->V(Lj2/c;Lj2/c$a;Z)V

    return-void
.end method

.method public synthetic d(Lj2/c$a;Landroidx/media3/common/h0$b;)V
    .locals 0

    invoke-static {p0, p1, p2}, Lj2/b;->n(Lj2/c;Lj2/c$a;Landroidx/media3/common/h0$b;)V

    return-void
.end method

.method public synthetic d0(Lj2/c$a;Landroidx/media3/common/o;)V
    .locals 0

    invoke-static {p0, p1, p2}, Lj2/b;->q(Lj2/c;Lj2/c$a;Landroidx/media3/common/o;)V

    return-void
.end method

.method public synthetic e(Lj2/c$a;Z)V
    .locals 0

    invoke-static {p0, p1, p2}, Lj2/b;->C(Lj2/c;Lj2/c$a;Z)V

    return-void
.end method

.method public synthetic e0(Lj2/c$a;F)V
    .locals 0

    invoke-static {p0, p1, p2}, Lj2/b;->l0(Lj2/c;Lj2/c$a;F)V

    return-void
.end method

.method public synthetic f(Lj2/c$a;ZI)V
    .locals 0

    invoke-static {p0, p1, p2, p3}, Lj2/b;->Q(Lj2/c;Lj2/c$a;ZI)V

    return-void
.end method

.method public synthetic f0(Lj2/c$a;I)V
    .locals 0

    invoke-static {p0, p1, p2}, Lj2/b;->N(Lj2/c;Lj2/c$a;I)V

    return-void
.end method

.method public synthetic g(Lj2/c$a;Ljava/lang/String;J)V
    .locals 0

    invoke-static {p0, p1, p2, p3, p4}, Lj2/b;->b(Lj2/c;Lj2/c$a;Ljava/lang/String;J)V

    return-void
.end method

.method public synthetic g0(Lj2/c$a;Landroidx/media3/common/y;)V
    .locals 0

    invoke-static {p0, p1, p2}, Lj2/b;->g(Lj2/c;Lj2/c$a;Landroidx/media3/common/y;)V

    return-void
.end method

.method public h(Lj2/c$a;Landroidx/media3/common/t0;)V
    .locals 3

    iget-object p1, p0, Lj2/v3;->o:Lj2/v3$b;

    if-eqz p1, :cond_0

    iget-object v0, p1, Lj2/v3$b;->a:Landroidx/media3/common/y;

    iget v1, v0, Landroidx/media3/common/y;->s:I

    const/4 v2, -0x1

    if-ne v1, v2, :cond_0

    invoke-virtual {v0}, Landroidx/media3/common/y;->b()Landroidx/media3/common/y$b;

    move-result-object v0

    iget v1, p2, Landroidx/media3/common/t0;->a:I

    invoke-virtual {v0, v1}, Landroidx/media3/common/y$b;->r0(I)Landroidx/media3/common/y$b;

    move-result-object v0

    iget p2, p2, Landroidx/media3/common/t0;->b:I

    invoke-virtual {v0, p2}, Landroidx/media3/common/y$b;->V(I)Landroidx/media3/common/y$b;

    move-result-object p2

    invoke-virtual {p2}, Landroidx/media3/common/y$b;->I()Landroidx/media3/common/y;

    move-result-object p2

    new-instance v0, Lj2/v3$b;

    iget v1, p1, Lj2/v3$b;->b:I

    iget-object p1, p1, Lj2/v3$b;->c:Ljava/lang/String;

    invoke-direct {v0, p2, v1, p1}, Lj2/v3$b;-><init>(Landroidx/media3/common/y;ILjava/lang/String;)V

    iput-object v0, p0, Lj2/v3;->o:Lj2/v3$b;

    :cond_0
    return-void
.end method

.method public synthetic h0(Lj2/c$a;Z)V
    .locals 0

    invoke-static {p0, p1, p2}, Lj2/b;->G(Lj2/c;Lj2/c$a;Z)V

    return-void
.end method

.method public synthetic i(Lj2/c$a;Landroidx/media3/common/y;Landroidx/media3/exoplayer/o;)V
    .locals 0

    invoke-static {p0, p1, p2, p3}, Lj2/b;->j0(Lj2/c;Lj2/c$a;Landroidx/media3/common/y;Landroidx/media3/exoplayer/o;)V

    return-void
.end method

.method public synthetic i0(Lj2/c$a;Lu2/n;Lu2/o;)V
    .locals 0

    invoke-static {p0, p1, p2, p3}, Lj2/b;->F(Lj2/c;Lj2/c$a;Lu2/n;Lu2/o;)V

    return-void
.end method

.method public synthetic j(Lj2/c$a;Ljava/lang/Exception;)V
    .locals 0

    invoke-static {p0, p1, p2}, Lj2/b;->x(Lj2/c;Lj2/c$a;Ljava/lang/Exception;)V

    return-void
.end method

.method public j0(Lj2/c$a;Ljava/lang/String;)V
    .locals 1

    iget-object v0, p1, Lj2/c$a;->d:Landroidx/media3/exoplayer/source/l$b;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Landroidx/media3/exoplayer/source/l$b;->b()Z

    move-result v0

    if-eqz v0, :cond_0

    return-void

    :cond_0
    invoke-virtual {p0}, Lj2/v3;->z0()V

    iput-object p2, p0, Lj2/v3;->i:Ljava/lang/String;

    invoke-static {}, Lj2/p2;->a()Landroid/media/metrics/PlaybackMetrics$Builder;

    move-result-object p2

    const-string v0, "AndroidXMedia3"

    invoke-static {p2, v0}, Lj2/z2;->a(Landroid/media/metrics/PlaybackMetrics$Builder;Ljava/lang/String;)Landroid/media/metrics/PlaybackMetrics$Builder;

    move-result-object p2

    const-string v0, "1.3.1"

    invoke-static {p2, v0}, Lj2/b3;->a(Landroid/media/metrics/PlaybackMetrics$Builder;Ljava/lang/String;)Landroid/media/metrics/PlaybackMetrics$Builder;

    move-result-object p2

    iput-object p2, p0, Lj2/v3;->j:Landroid/media/metrics/PlaybackMetrics$Builder;

    iget-object p2, p1, Lj2/c$a;->b:Landroidx/media3/common/m0;

    iget-object p1, p1, Lj2/c$a;->d:Landroidx/media3/exoplayer/source/l$b;

    invoke-virtual {p0, p2, p1}, Lj2/v3;->R0(Landroidx/media3/common/m0;Landroidx/media3/exoplayer/source/l$b;)V

    return-void
.end method

.method public synthetic k(Lj2/c$a;Ljava/lang/String;JJ)V
    .locals 0

    invoke-static/range {p0 .. p6}, Lj2/b;->c(Lj2/c;Lj2/c$a;Ljava/lang/String;JJ)V

    return-void
.end method

.method public synthetic k0(Lj2/c$a;Landroidx/media3/exoplayer/audio/AudioSink$a;)V
    .locals 0

    invoke-static {p0, p1, p2}, Lj2/b;->k(Lj2/c;Lj2/c$a;Landroidx/media3/exoplayer/audio/AudioSink$a;)V

    return-void
.end method

.method public synthetic l(Lj2/c$a;Ljava/lang/Exception;)V
    .locals 0

    invoke-static {p0, p1, p2}, Lj2/b;->j(Lj2/c;Lj2/c$a;Ljava/lang/Exception;)V

    return-void
.end method

.method public synthetic l0(Lj2/c$a;Ljava/lang/String;)V
    .locals 0

    invoke-static {p0, p1, p2}, Lj2/b;->f0(Lj2/c;Lj2/c$a;Ljava/lang/String;)V

    return-void
.end method

.method public synthetic m(Lj2/c$a;JI)V
    .locals 0

    invoke-static {p0, p1, p2, p3, p4}, Lj2/b;->h0(Lj2/c;Lj2/c$a;JI)V

    return-void
.end method

.method public synthetic m0(Lj2/c$a;Z)V
    .locals 0

    invoke-static {p0, p1, p2}, Lj2/b;->B(Lj2/c;Lj2/c$a;Z)V

    return-void
.end method

.method public synthetic n(Lj2/c$a;Landroidx/media3/common/b0;I)V
    .locals 0

    invoke-static {p0, p1, p2, p3}, Lj2/b;->H(Lj2/c;Lj2/c$a;Landroidx/media3/common/b0;I)V

    return-void
.end method

.method public synthetic n0(Lj2/c$a;Landroidx/media3/exoplayer/n;)V
    .locals 0

    invoke-static {p0, p1, p2}, Lj2/b;->f(Lj2/c;Lj2/c$a;Landroidx/media3/exoplayer/n;)V

    return-void
.end method

.method public synthetic o(Lj2/c$a;I)V
    .locals 0

    invoke-static {p0, p1, p2}, Lj2/b;->w(Lj2/c;Lj2/c$a;I)V

    return-void
.end method

.method public o0(Lj2/c$a;Ljava/lang/String;Ljava/lang/String;)V
    .locals 0

    return-void
.end method

.method public synthetic p(Lj2/c$a;)V
    .locals 0

    invoke-static {p0, p1}, Lj2/b;->P(Lj2/c;Lj2/c$a;)V

    return-void
.end method

.method public synthetic p0(Lj2/c$a;)V
    .locals 0

    invoke-static {p0, p1}, Lj2/b;->v(Lj2/c;Lj2/c$a;)V

    return-void
.end method

.method public q(Lj2/c$a;Lu2/o;)V
    .locals 5

    iget-object v0, p1, Lj2/c$a;->d:Landroidx/media3/exoplayer/source/l$b;

    if-nez v0, :cond_0

    return-void

    :cond_0
    new-instance v0, Lj2/v3$b;

    iget-object v1, p2, Lu2/o;->c:Landroidx/media3/common/y;

    invoke-static {v1}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Landroidx/media3/common/y;

    iget v2, p2, Lu2/o;->d:I

    iget-object v3, p0, Lj2/v3;->b:Lj2/w3;

    iget-object v4, p1, Lj2/c$a;->b:Landroidx/media3/common/m0;

    iget-object p1, p1, Lj2/c$a;->d:Landroidx/media3/exoplayer/source/l$b;

    invoke-static {p1}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Landroidx/media3/exoplayer/source/l$b;

    invoke-interface {v3, v4, p1}, Lj2/w3;->b(Landroidx/media3/common/m0;Landroidx/media3/exoplayer/source/l$b;)Ljava/lang/String;

    move-result-object p1

    invoke-direct {v0, v1, v2, p1}, Lj2/v3$b;-><init>(Landroidx/media3/common/y;ILjava/lang/String;)V

    iget p1, p2, Lu2/o;->b:I

    if-eqz p1, :cond_3

    const/4 p2, 0x1

    if-eq p1, p2, :cond_2

    const/4 p2, 0x2

    if-eq p1, p2, :cond_3

    const/4 p2, 0x3

    if-eq p1, p2, :cond_1

    goto :goto_0

    :cond_1
    iput-object v0, p0, Lj2/v3;->q:Lj2/v3$b;

    goto :goto_0

    :cond_2
    iput-object v0, p0, Lj2/v3;->p:Lj2/v3$b;

    goto :goto_0

    :cond_3
    iput-object v0, p0, Lj2/v3;->o:Lj2/v3$b;

    :goto_0
    return-void
.end method

.method public synthetic q0(Lj2/c$a;Ljava/lang/String;J)V
    .locals 0

    invoke-static {p0, p1, p2, p3, p4}, Lj2/b;->d0(Lj2/c;Lj2/c$a;Ljava/lang/String;J)V

    return-void
.end method

.method public synthetic r(Lj2/c$a;Lu2/n;Lu2/o;)V
    .locals 0

    invoke-static {p0, p1, p2, p3}, Lj2/b;->D(Lj2/c;Lj2/c$a;Lu2/n;Lu2/o;)V

    return-void
.end method

.method public synthetic r0(Lj2/c$a;Lu2/o;)V
    .locals 0

    invoke-static {p0, p1, p2}, Lj2/b;->b0(Lj2/c;Lj2/c$a;Lu2/o;)V

    return-void
.end method

.method public synthetic s(Lj2/c$a;)V
    .locals 0

    invoke-static {p0, p1}, Lj2/b;->s(Lj2/c;Lj2/c$a;)V

    return-void
.end method

.method public synthetic s0(Lj2/c$a;Landroidx/media3/common/Metadata;)V
    .locals 0

    invoke-static {p0, p1, p2}, Lj2/b;->J(Lj2/c;Lj2/c$a;Landroidx/media3/common/Metadata;)V

    return-void
.end method

.method public t(Lj2/c$a;Lu2/n;Lu2/o;Ljava/io/IOException;Z)V
    .locals 0

    iget p1, p3, Lu2/o;->a:I

    iput p1, p0, Lj2/v3;->v:I

    return-void
.end method

.method public synthetic t0(Lj2/c$a;Landroidx/media3/common/PlaybackException;)V
    .locals 0

    invoke-static {p0, p1, p2}, Lj2/b;->O(Lj2/c;Lj2/c$a;Landroidx/media3/common/PlaybackException;)V

    return-void
.end method

.method public synthetic u(Lj2/c$a;J)V
    .locals 0

    invoke-static {p0, p1, p2, p3}, Lj2/b;->i(Lj2/c;Lj2/c$a;J)V

    return-void
.end method

.method public synthetic u0(Lj2/c$a;Ljava/lang/Exception;)V
    .locals 0

    invoke-static {p0, p1, p2}, Lj2/b;->a(Lj2/c;Lj2/c$a;Ljava/lang/Exception;)V

    return-void
.end method

.method public v(Lj2/c$a;Ljava/lang/String;Z)V
    .locals 0

    iget-object p1, p1, Lj2/c$a;->d:Landroidx/media3/exoplayer/source/l$b;

    if-eqz p1, :cond_0

    invoke-virtual {p1}, Landroidx/media3/exoplayer/source/l$b;->b()Z

    move-result p1

    if-nez p1, :cond_2

    :cond_0
    iget-object p1, p0, Lj2/v3;->i:Ljava/lang/String;

    invoke-virtual {p2, p1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result p1

    if-nez p1, :cond_1

    goto :goto_0

    :cond_1
    invoke-virtual {p0}, Lj2/v3;->z0()V

    :cond_2
    :goto_0
    iget-object p1, p0, Lj2/v3;->g:Ljava/util/HashMap;

    invoke-virtual {p1, p2}, Ljava/util/HashMap;->remove(Ljava/lang/Object;)Ljava/lang/Object;

    iget-object p1, p0, Lj2/v3;->h:Ljava/util/HashMap;

    invoke-virtual {p1, p2}, Ljava/util/HashMap;->remove(Ljava/lang/Object;)Ljava/lang/Object;

    return-void
.end method

.method public v0(Lj2/c$a;Landroidx/media3/exoplayer/n;)V
    .locals 1

    iget p1, p0, Lj2/v3;->x:I

    iget v0, p2, Landroidx/media3/exoplayer/n;->g:I

    add-int/2addr p1, v0

    iput p1, p0, Lj2/v3;->x:I

    iget p1, p0, Lj2/v3;->y:I

    iget p2, p2, Landroidx/media3/exoplayer/n;->e:I

    add-int/2addr p1, p2

    iput p1, p0, Lj2/v3;->y:I

    return-void
.end method

.method public w(Lj2/c$a;IJJ)V
    .locals 5

    iget-object p5, p1, Lj2/c$a;->d:Landroidx/media3/exoplayer/source/l$b;

    if-eqz p5, :cond_2

    iget-object p6, p0, Lj2/v3;->b:Lj2/w3;

    iget-object p1, p1, Lj2/c$a;->b:Landroidx/media3/common/m0;

    invoke-static {p5}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p5

    check-cast p5, Landroidx/media3/exoplayer/source/l$b;

    invoke-interface {p6, p1, p5}, Lj2/w3;->b(Landroidx/media3/common/m0;Landroidx/media3/exoplayer/source/l$b;)Ljava/lang/String;

    move-result-object p1

    iget-object p5, p0, Lj2/v3;->h:Ljava/util/HashMap;

    invoke-virtual {p5, p1}, Ljava/util/HashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p5

    check-cast p5, Ljava/lang/Long;

    iget-object p6, p0, Lj2/v3;->g:Ljava/util/HashMap;

    invoke-virtual {p6, p1}, Ljava/util/HashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p6

    check-cast p6, Ljava/lang/Long;

    iget-object v0, p0, Lj2/v3;->h:Ljava/util/HashMap;

    const-wide/16 v1, 0x0

    if-nez p5, :cond_0

    move-wide v3, v1

    goto :goto_0

    :cond_0
    invoke-virtual {p5}, Ljava/lang/Long;->longValue()J

    move-result-wide v3

    :goto_0
    add-long/2addr v3, p3

    invoke-static {v3, v4}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    move-result-object p3

    invoke-virtual {v0, p1, p3}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    iget-object p3, p0, Lj2/v3;->g:Ljava/util/HashMap;

    if-nez p6, :cond_1

    goto :goto_1

    :cond_1
    invoke-virtual {p6}, Ljava/lang/Long;->longValue()J

    move-result-wide v1

    :goto_1
    int-to-long p4, p2

    add-long/2addr v1, p4

    invoke-static {v1, v2}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    move-result-object p2

    invoke-virtual {p3, p1, p2}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    :cond_2
    return-void
.end method

.method public synthetic w0(Lj2/c$a;)V
    .locals 0

    invoke-static {p0, p1}, Lj2/b;->t(Lj2/c;Lj2/c$a;)V

    return-void
.end method

.method public x(Landroidx/media3/common/h0;Lj2/c$b;)V
    .locals 2

    invoke-virtual {p2}, Lj2/c$b;->d()I

    move-result v0

    if-nez v0, :cond_0

    return-void

    :cond_0
    invoke-virtual {p0, p2}, Lj2/v3;->J0(Lj2/c$b;)V

    invoke-static {}, Landroid/os/SystemClock;->elapsedRealtime()J

    move-result-wide v0

    invoke-virtual {p0, p1, p2}, Lj2/v3;->P0(Landroidx/media3/common/h0;Lj2/c$b;)V

    invoke-virtual {p0, v0, v1}, Lj2/v3;->L0(J)V

    invoke-virtual {p0, p1, p2, v0, v1}, Lj2/v3;->N0(Landroidx/media3/common/h0;Lj2/c$b;J)V

    invoke-virtual {p0, v0, v1}, Lj2/v3;->K0(J)V

    invoke-virtual {p0, p1, p2, v0, v1}, Lj2/v3;->M0(Landroidx/media3/common/h0;Lj2/c$b;J)V

    const/16 p1, 0x404

    invoke-virtual {p2, p1}, Lj2/c$b;->a(I)Z

    move-result v0

    if-eqz v0, :cond_1

    iget-object v0, p0, Lj2/v3;->b:Lj2/w3;

    invoke-virtual {p2, p1}, Lj2/c$b;->c(I)Lj2/c$a;

    move-result-object p1

    invoke-interface {v0, p1}, Lj2/w3;->f(Lj2/c$a;)V

    :cond_1
    return-void
.end method

.method public final x0(Lj2/v3$b;)Z
    .locals 1
    .param p1    # Lj2/v3$b;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    if-eqz p1, :cond_0

    iget-object p1, p1, Lj2/v3$b;->c:Ljava/lang/String;

    iget-object v0, p0, Lj2/v3;->b:Lj2/w3;

    invoke-interface {v0}, Lj2/w3;->a()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_0

    const/4 p1, 0x1

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    :goto_0
    return p1
.end method

.method public synthetic y(Lj2/c$a;I)V
    .locals 0

    invoke-static {p0, p1, p2}, Lj2/b;->R(Lj2/c;Lj2/c$a;I)V

    return-void
.end method

.method public synthetic z(Lj2/c$a;IIIF)V
    .locals 0

    invoke-static/range {p0 .. p5}, Lj2/b;->k0(Lj2/c;Lj2/c$a;IIIF)V

    return-void
.end method

.method public final z0()V
    .locals 7

    iget-object v0, p0, Lj2/v3;->j:Landroid/media/metrics/PlaybackMetrics$Builder;

    const/4 v1, 0x0

    if-eqz v0, :cond_3

    iget-boolean v2, p0, Lj2/v3;->A:Z

    if-eqz v2, :cond_3

    iget v2, p0, Lj2/v3;->z:I

    invoke-static {v0, v2}, Lj2/r2;->a(Landroid/media/metrics/PlaybackMetrics$Builder;I)Landroid/media/metrics/PlaybackMetrics$Builder;

    iget-object v0, p0, Lj2/v3;->j:Landroid/media/metrics/PlaybackMetrics$Builder;

    iget v2, p0, Lj2/v3;->x:I

    invoke-static {v0, v2}, Lj2/s2;->a(Landroid/media/metrics/PlaybackMetrics$Builder;I)Landroid/media/metrics/PlaybackMetrics$Builder;

    iget-object v0, p0, Lj2/v3;->j:Landroid/media/metrics/PlaybackMetrics$Builder;

    iget v2, p0, Lj2/v3;->y:I

    invoke-static {v0, v2}, Lj2/t2;->a(Landroid/media/metrics/PlaybackMetrics$Builder;I)Landroid/media/metrics/PlaybackMetrics$Builder;

    iget-object v0, p0, Lj2/v3;->g:Ljava/util/HashMap;

    iget-object v2, p0, Lj2/v3;->i:Ljava/lang/String;

    invoke-virtual {v0, v2}, Ljava/util/HashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/lang/Long;

    iget-object v2, p0, Lj2/v3;->j:Landroid/media/metrics/PlaybackMetrics$Builder;

    const-wide/16 v3, 0x0

    if-nez v0, :cond_0

    move-wide v5, v3

    goto :goto_0

    :cond_0
    invoke-virtual {v0}, Ljava/lang/Long;->longValue()J

    move-result-wide v5

    :goto_0
    invoke-static {v2, v5, v6}, Lj2/u2;->a(Landroid/media/metrics/PlaybackMetrics$Builder;J)Landroid/media/metrics/PlaybackMetrics$Builder;

    iget-object v0, p0, Lj2/v3;->h:Ljava/util/HashMap;

    iget-object v2, p0, Lj2/v3;->i:Ljava/lang/String;

    invoke-virtual {v0, v2}, Ljava/util/HashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/lang/Long;

    iget-object v2, p0, Lj2/v3;->j:Landroid/media/metrics/PlaybackMetrics$Builder;

    if-nez v0, :cond_1

    move-wide v5, v3

    goto :goto_1

    :cond_1
    invoke-virtual {v0}, Ljava/lang/Long;->longValue()J

    move-result-wide v5

    :goto_1
    invoke-static {v2, v5, v6}, Lj2/v2;->a(Landroid/media/metrics/PlaybackMetrics$Builder;J)Landroid/media/metrics/PlaybackMetrics$Builder;

    iget-object v2, p0, Lj2/v3;->j:Landroid/media/metrics/PlaybackMetrics$Builder;

    if-eqz v0, :cond_2

    invoke-virtual {v0}, Ljava/lang/Long;->longValue()J

    move-result-wide v5

    cmp-long v0, v5, v3

    if-lez v0, :cond_2

    const/4 v0, 0x1

    goto :goto_2

    :cond_2
    const/4 v0, 0x0

    :goto_2
    invoke-static {v2, v0}, Lj2/w2;->a(Landroid/media/metrics/PlaybackMetrics$Builder;I)Landroid/media/metrics/PlaybackMetrics$Builder;

    iget-object v0, p0, Lj2/v3;->c:Landroid/media/metrics/PlaybackSession;

    iget-object v2, p0, Lj2/v3;->j:Landroid/media/metrics/PlaybackMetrics$Builder;

    invoke-static {v2}, Lj2/x2;->a(Landroid/media/metrics/PlaybackMetrics$Builder;)Landroid/media/metrics/PlaybackMetrics;

    move-result-object v2

    invoke-static {v0, v2}, Lj2/y2;->a(Landroid/media/metrics/PlaybackSession;Landroid/media/metrics/PlaybackMetrics;)V

    :cond_3
    const/4 v0, 0x0

    iput-object v0, p0, Lj2/v3;->j:Landroid/media/metrics/PlaybackMetrics$Builder;

    iput-object v0, p0, Lj2/v3;->i:Ljava/lang/String;

    iput v1, p0, Lj2/v3;->z:I

    iput v1, p0, Lj2/v3;->x:I

    iput v1, p0, Lj2/v3;->y:I

    iput-object v0, p0, Lj2/v3;->r:Landroidx/media3/common/y;

    iput-object v0, p0, Lj2/v3;->s:Landroidx/media3/common/y;

    iput-object v0, p0, Lj2/v3;->t:Landroidx/media3/common/y;

    iput-boolean v1, p0, Lj2/v3;->A:Z

    return-void
.end method
