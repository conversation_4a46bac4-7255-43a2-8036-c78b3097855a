.class public final Lj2/x3;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lj2/x3$a;
    }
.end annotation


# static fields
.field public static final b:Lj2/x3;


# instance fields
.field public final a:Lj2/x3$a;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    sget v0, Le2/u0;->a:I

    const/16 v1, 0x1f

    if-ge v0, v1, :cond_0

    new-instance v0, Lj2/x3;

    invoke-direct {v0}, Lj2/x3;-><init>()V

    goto :goto_0

    :cond_0
    new-instance v0, Lj2/x3;

    sget-object v1, Lj2/x3$a;->b:Lj2/x3$a;

    invoke-direct {v0, v1}, Lj2/x3;-><init>(Lj2/x3$a;)V

    :goto_0
    sput-object v0, Lj2/x3;->b:Lj2/x3;

    return-void
.end method

.method public constructor <init>()V
    .locals 2

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    sget v0, Le2/u0;->a:I

    const/16 v1, 0x1f

    if-ge v0, v1, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    invoke-static {v0}, Le2/a;->g(Z)V

    const/4 v0, 0x0

    iput-object v0, p0, Lj2/x3;->a:Lj2/x3$a;

    return-void
.end method

.method public constructor <init>(Landroid/media/metrics/LogSessionId;)V
    .locals 1
    .annotation build Landroidx/annotation/RequiresApi;
        value = 0x1f
    .end annotation

    new-instance v0, Lj2/x3$a;

    invoke-direct {v0, p1}, Lj2/x3$a;-><init>(Landroid/media/metrics/LogSessionId;)V

    invoke-direct {p0, v0}, Lj2/x3;-><init>(Lj2/x3$a;)V

    return-void
.end method

.method public constructor <init>(Lj2/x3$a;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lj2/x3;->a:Lj2/x3$a;

    return-void
.end method


# virtual methods
.method public a()Landroid/media/metrics/LogSessionId;
    .locals 1
    .annotation build Landroidx/annotation/RequiresApi;
        value = 0x1f
    .end annotation

    iget-object v0, p0, Lj2/x3;->a:Lj2/x3$a;

    invoke-static {v0}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lj2/x3$a;

    iget-object v0, v0, Lj2/x3$a;->a:Landroid/media/metrics/LogSessionId;

    return-object v0
.end method
