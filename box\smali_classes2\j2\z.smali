.class public final synthetic Lj2/z;
.super Ljava/lang/Object;

# interfaces
.implements Le2/n$a;


# instance fields
.field public final synthetic a:Lj2/c$a;

.field public final synthetic b:Lu2/n;

.field public final synthetic c:Lu2/o;


# direct methods
.method public synthetic constructor <init>(Lj2/c$a;Lu2/n;Lu2/o;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lj2/z;->a:Lj2/c$a;

    iput-object p2, p0, Lj2/z;->b:Lu2/n;

    iput-object p3, p0, Lj2/z;->c:Lu2/o;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)V
    .locals 3

    iget-object v0, p0, Lj2/z;->a:Lj2/c$a;

    iget-object v1, p0, Lj2/z;->b:Lu2/n;

    iget-object v2, p0, Lj2/z;->c:Lu2/o;

    check-cast p1, Lj2/c;

    invoke-static {v0, v1, v2, p1}, Lj2/q1;->c0(Lj2/c$a;Lu2/n;Lu2/o;Lj2/c;)V

    return-void
.end method
