.class public final synthetic Lj2/z0;
.super Ljava/lang/Object;

# interfaces
.implements Le2/n$a;


# instance fields
.field public final synthetic a:Lj2/c$a;

.field public final synthetic b:I


# direct methods
.method public synthetic constructor <init>(Lj2/c$a;I)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lj2/z0;->a:Lj2/c$a;

    iput p2, p0, Lj2/z0;->b:I

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)V
    .locals 2

    iget-object v0, p0, Lj2/z0;->a:Lj2/c$a;

    iget v1, p0, Lj2/z0;->b:I

    check-cast p1, Lj2/c;

    invoke-static {v0, v1, p1}, Lj2/q1;->L0(Lj2/c$a;ILj2/c;)V

    return-void
.end method
