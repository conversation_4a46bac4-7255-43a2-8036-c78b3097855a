.class public interface abstract Lj5/e;
.super Ljava/lang/Object;


# virtual methods
.method public abstract a(Ljava/lang/String;)Lj5/c;
    .param p1    # Ljava/lang/String;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation
.end method
