.class public final Lk2/e$d;
.super Ljava/lang/Object;


# annotations
.annotation build Landroidx/annotation/RequiresApi;
    value = 0x21
.end annotation

.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lk2/e;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "d"
.end annotation


# direct methods
.method public static a(Landroid/media/AudioManager;Landroidx/media3/common/d;)Lk2/e;
    .locals 1

    invoke-virtual {p1}, Landroidx/media3/common/d;->a()Landroidx/media3/common/d$d;

    move-result-object p1

    iget-object p1, p1, Landroidx/media3/common/d$d;->a:Landroid/media/AudioAttributes;

    invoke-static {p0, p1}, Lk2/i;->a(Landroid/media/AudioManager;Landroid/media/AudioAttributes;)Ljava/util/List;

    move-result-object p0

    new-instance p1, Lk2/e;

    invoke-static {p0}, Lk2/e;->a(Ljava/util/List;)Lcom/google/common/collect/ImmutableList;

    move-result-object p0

    const/4 v0, 0x0

    invoke-direct {p1, p0, v0}, Lk2/e;-><init>(Ljava/util/List;Lk2/e$a;)V

    return-object p1
.end method

.method public static b(Landroid/media/AudioManager;Landroidx/media3/common/d;)Lk2/k;
    .locals 1
    .annotation build Landroidx/annotation/Nullable;
    .end annotation

    const/4 v0, 0x0

    :try_start_0
    invoke-static {p0}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    check-cast p0, Landroid/media/AudioManager;

    invoke-virtual {p1}, Landroidx/media3/common/d;->a()Landroidx/media3/common/d$d;

    move-result-object p1

    iget-object p1, p1, Landroidx/media3/common/d$d;->a:Landroid/media/AudioAttributes;

    invoke-static {p0, p1}, Lk2/g;->a(Landroid/media/AudioManager;Landroid/media/AudioAttributes;)Ljava/util/List;

    move-result-object p0
    :try_end_0
    .catch Ljava/lang/RuntimeException; {:try_start_0 .. :try_end_0} :catch_0

    invoke-interface {p0}, Ljava/util/List;->isEmpty()Z

    move-result p1

    if-eqz p1, :cond_0

    return-object v0

    :cond_0
    new-instance p1, Lk2/k;

    const/4 v0, 0x0

    invoke-interface {p0, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p0

    invoke-static {p0}, Lk2/h;->a(Ljava/lang/Object;)Landroid/media/AudioDeviceInfo;

    move-result-object p0

    invoke-direct {p1, p0}, Lk2/k;-><init>(Landroid/media/AudioDeviceInfo;)V

    return-object p1

    :catch_0
    return-object v0
.end method
