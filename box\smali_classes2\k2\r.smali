.class public final synthetic Lk2/r;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/lang/Runnable;


# instance fields
.field public final synthetic a:Landroidx/media3/exoplayer/audio/c$a;

.field public final synthetic b:J


# direct methods
.method public synthetic constructor <init>(Landroidx/media3/exoplayer/audio/c$a;J)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lk2/r;->a:Landroidx/media3/exoplayer/audio/c$a;

    iput-wide p2, p0, Lk2/r;->b:J

    return-void
.end method


# virtual methods
.method public final run()V
    .locals 3

    iget-object v0, p0, Lk2/r;->a:Landroidx/media3/exoplayer/audio/c$a;

    iget-wide v1, p0, Lk2/r;->b:J

    invoke-static {v0, v1, v2}, Landroidx/media3/exoplayer/audio/c$a;->h(Landroidx/media3/exoplayer/audio/c$a;J)V

    return-void
.end method
