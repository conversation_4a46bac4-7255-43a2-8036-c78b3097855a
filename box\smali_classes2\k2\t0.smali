.class public final synthetic Lk2/t0;
.super Ljava/lang/Object;

# interfaces
.implements Landroidx/media3/exoplayer/audio/a$f;


# instance fields
.field public final synthetic a:Landroidx/media3/exoplayer/audio/DefaultAudioSink;


# direct methods
.method public synthetic constructor <init>(Landroidx/media3/exoplayer/audio/DefaultAudioSink;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lk2/t0;->a:Landroidx/media3/exoplayer/audio/DefaultAudioSink;

    return-void
.end method


# virtual methods
.method public final a(Lk2/e;)V
    .locals 1

    iget-object v0, p0, Lk2/t0;->a:Landroidx/media3/exoplayer/audio/DefaultAudioSink;

    invoke-virtual {v0, p1}, Landroidx/media3/exoplayer/audio/DefaultAudioSink;->U(Lk2/e;)V

    return-void
.end method
