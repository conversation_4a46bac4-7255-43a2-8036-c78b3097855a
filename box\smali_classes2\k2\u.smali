.class public final synthetic Lk2/u;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/lang/Runnable;


# instance fields
.field public final synthetic a:Landroidx/media3/exoplayer/audio/c$a;

.field public final synthetic b:Ljava/lang/String;


# direct methods
.method public synthetic constructor <init>(Landroidx/media3/exoplayer/audio/c$a;Ljava/lang/String;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lk2/u;->a:Landroidx/media3/exoplayer/audio/c$a;

    iput-object p2, p0, Lk2/u;->b:Ljava/lang/String;

    return-void
.end method


# virtual methods
.method public final run()V
    .locals 2

    iget-object v0, p0, Lk2/u;->a:Landroidx/media3/exoplayer/audio/c$a;

    iget-object v1, p0, Lk2/u;->b:Ljava/lang/String;

    invoke-static {v0, v1}, Landroidx/media3/exoplayer/audio/c$a;->f(Landroidx/media3/exoplayer/audio/c$a;Ljava/lang/String;)V

    return-void
.end method
