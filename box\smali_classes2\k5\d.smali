.class public Lk5/d;
.super Ljava/lang/Object;


# direct methods
.method public static a(Lcom/airbnb/lottie/parser/moshi/JsonReader;FLcom/airbnb/lottie/h;Lk5/n0;)Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">(",
            "Lcom/airbnb/lottie/parser/moshi/JsonReader;",
            "F",
            "Lcom/airbnb/lottie/h;",
            "Lk5/n0<",
            "TT;>;)",
            "Ljava/util/List<",
            "Lm5/a<",
            "TT;>;>;"
        }
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    const/4 v0, 0x0

    invoke-static {p0, p2, p1, p3, v0}, Lk5/u;->a(Lcom/airbnb/lottie/parser/moshi/<PERSON><PERSON>eader;Lcom/airbnb/lottie/h;FLk5/n0;Z)Ljava/util/List;

    move-result-object p0

    return-object p0
.end method

.method public static b(Lcom/airbnb/lottie/parser/moshi/JsonReader;Lcom/airbnb/lottie/h;Lk5/n0;)Ljava/util/List;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">(",
            "Lcom/airbnb/lottie/parser/moshi/JsonReader;",
            "Lcom/airbnb/lottie/h;",
            "Lk5/n0<",
            "TT;>;)",
            "Ljava/util/List<",
            "Lm5/a<",
            "TT;>;>;"
        }
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    const/high16 v0, 0x3f800000    # 1.0f

    const/4 v1, 0x0

    invoke-static {p0, p1, v0, p2, v1}, Lk5/u;->a(Lcom/airbnb/lottie/parser/moshi/JsonReader;Lcom/airbnb/lottie/h;FLk5/n0;Z)Ljava/util/List;

    move-result-object p0

    return-object p0
.end method

.method public static c(Lcom/airbnb/lottie/parser/moshi/JsonReader;Lcom/airbnb/lottie/h;)Lg5/a;
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    new-instance v0, Lg5/a;

    sget-object v1, Lk5/g;->a:Lk5/g;

    invoke-static {p0, p1, v1}, Lk5/d;->b(Lcom/airbnb/lottie/parser/moshi/JsonReader;Lcom/airbnb/lottie/h;Lk5/n0;)Ljava/util/List;

    move-result-object p0

    invoke-direct {v0, p0}, Lg5/a;-><init>(Ljava/util/List;)V

    return-object v0
.end method

.method public static d(Lcom/airbnb/lottie/parser/moshi/JsonReader;Lcom/airbnb/lottie/h;)Lg5/j;
    .locals 3
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    new-instance v0, Lg5/j;

    invoke-static {}, Ll5/l;->e()F

    move-result v1

    sget-object v2, Lk5/i;->a:Lk5/i;

    invoke-static {p0, v1, p1, v2}, Lk5/d;->a(Lcom/airbnb/lottie/parser/moshi/JsonReader;FLcom/airbnb/lottie/h;Lk5/n0;)Ljava/util/List;

    move-result-object p0

    invoke-direct {v0, p0}, Lg5/j;-><init>(Ljava/util/List;)V

    return-object v0
.end method

.method public static e(Lcom/airbnb/lottie/parser/moshi/JsonReader;Lcom/airbnb/lottie/h;)Lg5/b;
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    const/4 v0, 0x1

    invoke-static {p0, p1, v0}, Lk5/d;->f(Lcom/airbnb/lottie/parser/moshi/JsonReader;Lcom/airbnb/lottie/h;Z)Lg5/b;

    move-result-object p0

    return-object p0
.end method

.method public static f(Lcom/airbnb/lottie/parser/moshi/JsonReader;Lcom/airbnb/lottie/h;Z)Lg5/b;
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    new-instance v0, Lg5/b;

    if-eqz p2, :cond_0

    invoke-static {}, Ll5/l;->e()F

    move-result p2

    goto :goto_0

    :cond_0
    const/high16 p2, 0x3f800000    # 1.0f

    :goto_0
    sget-object v1, Lk5/l;->a:Lk5/l;

    invoke-static {p0, p2, p1, v1}, Lk5/d;->a(Lcom/airbnb/lottie/parser/moshi/JsonReader;FLcom/airbnb/lottie/h;Lk5/n0;)Ljava/util/List;

    move-result-object p0

    invoke-direct {v0, p0}, Lg5/b;-><init>(Ljava/util/List;)V

    return-object v0
.end method

.method public static g(Lcom/airbnb/lottie/parser/moshi/JsonReader;Lcom/airbnb/lottie/h;I)Lg5/c;
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    new-instance v0, Lg5/c;

    new-instance v1, Lk5/o;

    invoke-direct {v1, p2}, Lk5/o;-><init>(I)V

    invoke-static {p0, p1, v1}, Lk5/d;->b(Lcom/airbnb/lottie/parser/moshi/JsonReader;Lcom/airbnb/lottie/h;Lk5/n0;)Ljava/util/List;

    move-result-object p0

    invoke-direct {v0, p0}, Lg5/c;-><init>(Ljava/util/List;)V

    return-object v0
.end method

.method public static h(Lcom/airbnb/lottie/parser/moshi/JsonReader;Lcom/airbnb/lottie/h;)Lg5/d;
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    new-instance v0, Lg5/d;

    sget-object v1, Lk5/r;->a:Lk5/r;

    invoke-static {p0, p1, v1}, Lk5/d;->b(Lcom/airbnb/lottie/parser/moshi/JsonReader;Lcom/airbnb/lottie/h;Lk5/n0;)Ljava/util/List;

    move-result-object p0

    invoke-direct {v0, p0}, Lg5/d;-><init>(Ljava/util/List;)V

    return-object v0
.end method

.method public static i(Lcom/airbnb/lottie/parser/moshi/JsonReader;Lcom/airbnb/lottie/h;)Lg5/f;
    .locals 4
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    new-instance v0, Lg5/f;

    invoke-static {}, Ll5/l;->e()F

    move-result v1

    sget-object v2, Lk5/b0;->a:Lk5/b0;

    const/4 v3, 0x1

    invoke-static {p0, p1, v1, v2, v3}, Lk5/u;->a(Lcom/airbnb/lottie/parser/moshi/JsonReader;Lcom/airbnb/lottie/h;FLk5/n0;Z)Ljava/util/List;

    move-result-object p0

    invoke-direct {v0, p0}, Lg5/f;-><init>(Ljava/util/List;)V

    return-object v0
.end method

.method public static j(Lcom/airbnb/lottie/parser/moshi/JsonReader;Lcom/airbnb/lottie/h;)Lg5/g;
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    new-instance v0, Lg5/g;

    sget-object v1, Lk5/g0;->a:Lk5/g0;

    invoke-static {p0, p1, v1}, Lk5/d;->b(Lcom/airbnb/lottie/parser/moshi/JsonReader;Lcom/airbnb/lottie/h;Lk5/n0;)Ljava/util/List;

    move-result-object p0

    invoke-direct {v0, p0}, Lg5/g;-><init>(Ljava/util/List;)V

    return-object v0
.end method

.method public static k(Lcom/airbnb/lottie/parser/moshi/JsonReader;Lcom/airbnb/lottie/h;)Lg5/h;
    .locals 3
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    new-instance v0, Lg5/h;

    invoke-static {}, Ll5/l;->e()F

    move-result v1

    sget-object v2, Lk5/h0;->a:Lk5/h0;

    invoke-static {p0, v1, p1, v2}, Lk5/d;->a(Lcom/airbnb/lottie/parser/moshi/JsonReader;FLcom/airbnb/lottie/h;Lk5/n0;)Ljava/util/List;

    move-result-object p0

    invoke-direct {v0, p0}, Lg5/h;-><init>(Ljava/util/List;)V

    return-object v0
.end method
