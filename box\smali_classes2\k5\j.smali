.class public Lk5/j;
.super Ljava/lang/Object;


# instance fields
.field public final a:Lg5/a;

.field public final b:Lg5/b;

.field public final c:Lg5/b;

.field public final d:Lg5/b;

.field public final e:Lg5/b;


# direct methods
.method public constructor <init>(Lg5/a;Lg5/b;Lg5/b;Lg5/b;Lg5/b;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lk5/j;->a:Lg5/a;

    iput-object p2, p0, Lk5/j;->b:Lg5/b;

    iput-object p3, p0, Lk5/j;->c:Lg5/b;

    iput-object p4, p0, Lk5/j;->d:Lg5/b;

    iput-object p5, p0, Lk5/j;->e:Lg5/b;

    return-void
.end method


# virtual methods
.method public a()Lg5/a;
    .locals 1

    iget-object v0, p0, Lk5/j;->a:Lg5/a;

    return-object v0
.end method

.method public b()Lg5/b;
    .locals 1

    iget-object v0, p0, Lk5/j;->c:Lg5/b;

    return-object v0
.end method

.method public c()Lg5/b;
    .locals 1

    iget-object v0, p0, Lk5/j;->d:Lg5/b;

    return-object v0
.end method

.method public d()Lg5/b;
    .locals 1

    iget-object v0, p0, Lk5/j;->b:Lg5/b;

    return-object v0
.end method

.method public e()Lg5/b;
    .locals 1

    iget-object v0, p0, Lk5/j;->e:Lg5/b;

    return-object v0
.end method
