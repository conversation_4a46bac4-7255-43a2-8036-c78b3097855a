.class public final synthetic Ll2/a;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/util/Comparator;


# direct methods
.method public synthetic constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final compare(Ljava/lang/Object;Ljava/lang/Object;)I
    .locals 0

    check-cast p1, Lm2/b;

    check-cast p2, Lm2/b;

    invoke-static {p1, p2}, Ll2/b;->a(Lm2/b;Lm2/b;)I

    move-result p1

    return p1
.end method
