.class public interface abstract Ll2/f;
.super Ljava/lang/Object;


# virtual methods
.method public abstract a(JJ)J
.end method

.method public abstract b(JJ)J
.end method

.method public abstract c(JJ)J
.end method

.method public abstract d(J)Lm2/i;
.end method

.method public abstract e(JJ)J
.end method

.method public abstract f(J)J
.end method

.method public abstract g()Z
.end method

.method public abstract getTimeUs(J)J
.end method

.method public abstract h()J
.end method

.method public abstract i(JJ)J
.end method
