.class public final synthetic Ll3/a;
.super Ljava/lang/Object;

# interfaces
.implements Ll3/b$a;


# direct methods
.method public synthetic constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final evaluate(IIIII)Z
    .locals 0

    invoke-static {p1, p2, p3, p4, p5}, Ll3/b;->c(IIIII)Z

    move-result p1

    return p1
.end method
