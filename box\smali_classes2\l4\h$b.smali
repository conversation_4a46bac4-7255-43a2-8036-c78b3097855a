.class public final Ll4/h$b;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Ll4/h;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "b"
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Ll4/h$b$a;,
        Ll4/h$b$b;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation


# static fields
.field public static final f:Ll4/h$b$b;


# instance fields
.field public final a:Landroid/content/Context;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public final b:Ljava/lang/String;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public final c:Ll4/h$a;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public final d:Z
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public final e:Z
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Ll4/h$b$b;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Ll4/h$b$b;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Ll4/h$b;->f:Ll4/h$b$b;

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Ljava/lang/String;Ll4/h$a;ZZ)V
    .locals 1

    const-string v0, "context"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    const-string v0, "callback"

    invoke-static {p3, v0}, Lkotlin/jvm/internal/Intrinsics;->g(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Ll4/h$b;->a:Landroid/content/Context;

    iput-object p2, p0, Ll4/h$b;->b:Ljava/lang/String;

    iput-object p3, p0, Ll4/h$b;->c:Ll4/h$a;

    iput-boolean p4, p0, Ll4/h$b;->d:Z

    iput-boolean p5, p0, Ll4/h$b;->e:Z

    return-void
.end method

.method public static final a(Landroid/content/Context;)Ll4/h$b$a;
    .locals 1
    .annotation runtime Lkotlin/jvm/JvmStatic;
    .end annotation

    sget-object v0, Ll4/h$b;->f:Ll4/h$b$b;

    invoke-virtual {v0, p0}, Ll4/h$b$b;->a(Landroid/content/Context;)Ll4/h$b$a;

    move-result-object p0

    return-object p0
.end method
