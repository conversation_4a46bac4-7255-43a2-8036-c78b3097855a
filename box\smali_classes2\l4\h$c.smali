.class public interface abstract Ll4/h$c;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Ll4/h;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "c"
.end annotation

.annotation runtime L<PERSON>lin/Metadata;
.end annotation


# virtual methods
.method public abstract a(Ll4/h$b;)Ll4/h;
.end method
