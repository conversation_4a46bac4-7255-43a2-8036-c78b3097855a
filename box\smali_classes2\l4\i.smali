.class public interface abstract Ll4/i;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/io/Closeable;


# annotations
.annotation runtime L<PERSON><PERSON>/Metadata;
.end annotation


# virtual methods
.method public abstract a0(ILjava/lang/String;)V
.end method

.method public abstract i(ID)V
.end method

.method public abstract j0(IJ)V
.end method

.method public abstract n0(I[B)V
.end method

.method public abstract u0(I)V
.end method
