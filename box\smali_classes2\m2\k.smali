.class public abstract Lm2/k;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lm2/k$d;,
        Lm2/k$c;,
        Lm2/k$b;,
        Lm2/k$a;,
        Lm2/k$e;
    }
.end annotation


# instance fields
.field public final a:Lm2/i;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public final b:J

.field public final c:J


# direct methods
.method public constructor <init>(Lm2/i;JJ)V
    .locals 0
    .param p1    # Lm2/i;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lm2/k;->a:Lm2/i;

    iput-wide p2, p0, Lm2/k;->b:J

    iput-wide p4, p0, Lm2/k;->c:J

    return-void
.end method


# virtual methods
.method public a(Lm2/j;)Lm2/i;
    .locals 0
    .annotation build Landroidx/annotation/Nullable;
    .end annotation

    iget-object p1, p0, Lm2/k;->a:Lm2/i;

    return-object p1
.end method

.method public b()J
    .locals 6

    iget-wide v0, p0, Lm2/k;->c:J

    const-wide/32 v2, 0xf4240

    iget-wide v4, p0, Lm2/k;->b:J

    invoke-static/range {v0 .. v5}, Le2/u0;->f1(JJJ)J

    move-result-wide v0

    return-wide v0
.end method
