.class public final Lm2/l;
.super Ljava/lang/Object;


# instance fields
.field public final a:J

.field public final b:J

.field public final c:J

.field public final d:F

.field public final e:F


# direct methods
.method public constructor <init>(JJJFF)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-wide p1, p0, Lm2/l;->a:J

    iput-wide p3, p0, Lm2/l;->b:J

    iput-wide p5, p0, Lm2/l;->c:J

    iput p7, p0, Lm2/l;->d:F

    iput p8, p0, Lm2/l;->e:F

    return-void
.end method
