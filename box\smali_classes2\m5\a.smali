.class public Lm5/a;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "<T:",
        "Ljava/lang/Object;",
        ">",
        "Ljava/lang/Object;"
    }
.end annotation


# instance fields
.field public final a:Lcom/airbnb/lottie/h;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public final b:Ljava/lang/Object;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation

    .annotation system Ldalvik/annotation/Signature;
        value = {
            "TT;"
        }
    .end annotation
.end field

.field public c:Ljava/lang/Object;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation

    .annotation system Ldalvik/annotation/Signature;
        value = {
            "TT;"
        }
    .end annotation
.end field

.field public final d:Landroid/view/animation/Interpolator;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public final e:Landroid/view/animation/Interpolator;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public final f:Landroid/view/animation/Interpolator;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public final g:F

.field public h:Ljava/lang/Float;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public i:F

.field public j:F

.field public k:I

.field public l:I

.field public m:F

.field public n:F

.field public o:Landroid/graphics/PointF;

.field public p:Landroid/graphics/PointF;


# direct methods
.method public constructor <init>(Lcom/airbnb/lottie/h;Ljava/lang/Object;Ljava/lang/Object;Landroid/view/animation/Interpolator;FLjava/lang/Float;)V
    .locals 1
    .param p2    # Ljava/lang/Object;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .param p3    # Ljava/lang/Object;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .param p4    # Landroid/view/animation/Interpolator;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .param p6    # Ljava/lang/Float;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/airbnb/lottie/h;",
            "TT;TT;",
            "Landroid/view/animation/Interpolator;",
            "F",
            "Ljava/lang/Float;",
            ")V"
        }
    .end annotation

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const v0, -0x358c9d09

    iput v0, p0, Lm5/a;->i:F

    iput v0, p0, Lm5/a;->j:F

    const v0, 0x2ec8fb09

    iput v0, p0, Lm5/a;->k:I

    iput v0, p0, Lm5/a;->l:I

    const/4 v0, 0x1

    iput v0, p0, Lm5/a;->m:F

    iput v0, p0, Lm5/a;->n:F

    const/4 v0, 0x0

    iput-object v0, p0, Lm5/a;->o:Landroid/graphics/PointF;

    iput-object v0, p0, Lm5/a;->p:Landroid/graphics/PointF;

    iput-object p1, p0, Lm5/a;->a:Lcom/airbnb/lottie/h;

    iput-object p2, p0, Lm5/a;->b:Ljava/lang/Object;

    iput-object p3, p0, Lm5/a;->c:Ljava/lang/Object;

    iput-object p4, p0, Lm5/a;->d:Landroid/view/animation/Interpolator;

    iput-object v0, p0, Lm5/a;->e:Landroid/view/animation/Interpolator;

    iput-object v0, p0, Lm5/a;->f:Landroid/view/animation/Interpolator;

    iput p5, p0, Lm5/a;->g:F

    iput-object p6, p0, Lm5/a;->h:Ljava/lang/Float;

    return-void
.end method

.method public constructor <init>(Lcom/airbnb/lottie/h;Ljava/lang/Object;Ljava/lang/Object;Landroid/view/animation/Interpolator;Landroid/view/animation/Interpolator;FLjava/lang/Float;)V
    .locals 1
    .param p2    # Ljava/lang/Object;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .param p3    # Ljava/lang/Object;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .param p4    # Landroid/view/animation/Interpolator;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .param p5    # Landroid/view/animation/Interpolator;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .param p7    # Ljava/lang/Float;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/airbnb/lottie/h;",
            "TT;TT;",
            "Landroid/view/animation/Interpolator;",
            "Landroid/view/animation/Interpolator;",
            "F",
            "Ljava/lang/Float;",
            ")V"
        }
    .end annotation

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const v0, -0x358c9d09

    iput v0, p0, Lm5/a;->i:F

    iput v0, p0, Lm5/a;->j:F

    const v0, 0x2ec8fb09

    iput v0, p0, Lm5/a;->k:I

    iput v0, p0, Lm5/a;->l:I

    const/4 v0, 0x1

    iput v0, p0, Lm5/a;->m:F

    iput v0, p0, Lm5/a;->n:F

    const/4 v0, 0x0

    iput-object v0, p0, Lm5/a;->o:Landroid/graphics/PointF;

    iput-object v0, p0, Lm5/a;->p:Landroid/graphics/PointF;

    iput-object p1, p0, Lm5/a;->a:Lcom/airbnb/lottie/h;

    iput-object p2, p0, Lm5/a;->b:Ljava/lang/Object;

    iput-object p3, p0, Lm5/a;->c:Ljava/lang/Object;

    iput-object v0, p0, Lm5/a;->d:Landroid/view/animation/Interpolator;

    iput-object p4, p0, Lm5/a;->e:Landroid/view/animation/Interpolator;

    iput-object p5, p0, Lm5/a;->f:Landroid/view/animation/Interpolator;

    iput p6, p0, Lm5/a;->g:F

    iput-object p7, p0, Lm5/a;->h:Ljava/lang/Float;

    return-void
.end method

.method public constructor <init>(Lcom/airbnb/lottie/h;Ljava/lang/Object;Ljava/lang/Object;Landroid/view/animation/Interpolator;Landroid/view/animation/Interpolator;Landroid/view/animation/Interpolator;FLjava/lang/Float;)V
    .locals 1
    .param p2    # Ljava/lang/Object;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .param p3    # Ljava/lang/Object;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .param p4    # Landroid/view/animation/Interpolator;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .param p5    # Landroid/view/animation/Interpolator;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .param p6    # Landroid/view/animation/Interpolator;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .param p8    # Ljava/lang/Float;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/airbnb/lottie/h;",
            "TT;TT;",
            "Landroid/view/animation/Interpolator;",
            "Landroid/view/animation/Interpolator;",
            "Landroid/view/animation/Interpolator;",
            "F",
            "Ljava/lang/Float;",
            ")V"
        }
    .end annotation

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const v0, -0x358c9d09

    iput v0, p0, Lm5/a;->i:F

    iput v0, p0, Lm5/a;->j:F

    const v0, 0x2ec8fb09

    iput v0, p0, Lm5/a;->k:I

    iput v0, p0, Lm5/a;->l:I

    const/4 v0, 0x1

    iput v0, p0, Lm5/a;->m:F

    iput v0, p0, Lm5/a;->n:F

    const/4 v0, 0x0

    iput-object v0, p0, Lm5/a;->o:Landroid/graphics/PointF;

    iput-object v0, p0, Lm5/a;->p:Landroid/graphics/PointF;

    iput-object p1, p0, Lm5/a;->a:Lcom/airbnb/lottie/h;

    iput-object p2, p0, Lm5/a;->b:Ljava/lang/Object;

    iput-object p3, p0, Lm5/a;->c:Ljava/lang/Object;

    iput-object p4, p0, Lm5/a;->d:Landroid/view/animation/Interpolator;

    iput-object p5, p0, Lm5/a;->e:Landroid/view/animation/Interpolator;

    iput-object p6, p0, Lm5/a;->f:Landroid/view/animation/Interpolator;

    iput p7, p0, Lm5/a;->g:F

    iput-object p8, p0, Lm5/a;->h:Ljava/lang/Float;

    return-void
.end method

.method public constructor <init>(Ljava/lang/Object;)V
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(TT;)V"
        }
    .end annotation

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const v0, -0x358c9d09

    iput v0, p0, Lm5/a;->i:F

    iput v0, p0, Lm5/a;->j:F

    const v0, 0x2ec8fb09

    iput v0, p0, Lm5/a;->k:I

    iput v0, p0, Lm5/a;->l:I

    const/4 v0, 0x1

    iput v0, p0, Lm5/a;->m:F

    iput v0, p0, Lm5/a;->n:F

    const/4 v1, 0x0

    iput-object v1, p0, Lm5/a;->o:Landroid/graphics/PointF;

    iput-object v1, p0, Lm5/a;->p:Landroid/graphics/PointF;

    iput-object v1, p0, Lm5/a;->a:Lcom/airbnb/lottie/h;

    iput-object p1, p0, Lm5/a;->b:Ljava/lang/Object;

    iput-object p1, p0, Lm5/a;->c:Ljava/lang/Object;

    iput-object v1, p0, Lm5/a;->d:Landroid/view/animation/Interpolator;

    iput-object v1, p0, Lm5/a;->e:Landroid/view/animation/Interpolator;

    iput-object v1, p0, Lm5/a;->f:Landroid/view/animation/Interpolator;

    iput v0, p0, Lm5/a;->g:F

    const p1, 0x7f7fffff    # Float.MAX_VALUE

    invoke-static {p1}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    move-result-object p1

    iput-object p1, p0, Lm5/a;->h:Ljava/lang/Float;

    return-void
.end method

.method public constructor <init>(Ljava/lang/Object;Ljava/lang/Object;)V
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(TT;TT;)V"
        }
    .end annotation

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const v0, -0x358c9d09

    iput v0, p0, Lm5/a;->i:F

    iput v0, p0, Lm5/a;->j:F

    const v0, 0x2ec8fb09

    iput v0, p0, Lm5/a;->k:I

    iput v0, p0, Lm5/a;->l:I

    const/4 v0, 0x1

    iput v0, p0, Lm5/a;->m:F

    iput v0, p0, Lm5/a;->n:F

    const/4 v1, 0x0

    iput-object v1, p0, Lm5/a;->o:Landroid/graphics/PointF;

    iput-object v1, p0, Lm5/a;->p:Landroid/graphics/PointF;

    iput-object v1, p0, Lm5/a;->a:Lcom/airbnb/lottie/h;

    iput-object p1, p0, Lm5/a;->b:Ljava/lang/Object;

    iput-object p2, p0, Lm5/a;->c:Ljava/lang/Object;

    iput-object v1, p0, Lm5/a;->d:Landroid/view/animation/Interpolator;

    iput-object v1, p0, Lm5/a;->e:Landroid/view/animation/Interpolator;

    iput-object v1, p0, Lm5/a;->f:Landroid/view/animation/Interpolator;

    iput v0, p0, Lm5/a;->g:F

    const p1, 0x7f7fffff    # Float.MAX_VALUE

    invoke-static {p1}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    move-result-object p1

    iput-object p1, p0, Lm5/a;->h:Ljava/lang/Float;

    return-void
.end method


# virtual methods
.method public a(F)Z
    .locals 1

    invoke-virtual {p0}, Lm5/a;->f()F

    move-result v0

    cmpl-float v0, p1, v0

    if-ltz v0, :cond_0

    invoke-virtual {p0}, Lm5/a;->c()F

    move-result v0

    cmpg-float p1, p1, v0

    if-gez p1, :cond_0

    const/4 p1, 0x1

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    :goto_0
    return p1
.end method

.method public b(Ljava/lang/Object;Ljava/lang/Object;)Lm5/a;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(TT;TT;)",
            "Lm5/a<",
            "TT;>;"
        }
    .end annotation

    new-instance v0, Lm5/a;

    invoke-direct {v0, p1, p2}, Lm5/a;-><init>(Ljava/lang/Object;Ljava/lang/Object;)V

    return-object v0
.end method

.method public c()F
    .locals 3

    iget-object v0, p0, Lm5/a;->a:Lcom/airbnb/lottie/h;

    const/high16 v1, 0x3f800000    # 1.0f

    if-nez v0, :cond_0

    return v1

    :cond_0
    iget v0, p0, Lm5/a;->n:F

    const/4 v2, 0x1

    cmpl-float v0, v0, v2

    if-nez v0, :cond_2

    iget-object v0, p0, Lm5/a;->h:Ljava/lang/Float;

    if-nez v0, :cond_1

    iput v1, p0, Lm5/a;->n:F

    goto :goto_0

    :cond_1
    invoke-virtual {p0}, Lm5/a;->f()F

    move-result v0

    iget-object v1, p0, Lm5/a;->h:Ljava/lang/Float;

    invoke-virtual {v1}, Ljava/lang/Float;->floatValue()F

    move-result v1

    iget v2, p0, Lm5/a;->g:F

    sub-float/2addr v1, v2

    iget-object v2, p0, Lm5/a;->a:Lcom/airbnb/lottie/h;

    invoke-virtual {v2}, Lcom/airbnb/lottie/h;->e()F

    move-result v2

    div-float/2addr v1, v2

    add-float/2addr v0, v1

    iput v0, p0, Lm5/a;->n:F

    :cond_2
    :goto_0
    iget v0, p0, Lm5/a;->n:F

    return v0
.end method

.method public d()F
    .locals 2

    iget v0, p0, Lm5/a;->j:F

    const v1, -0x358c9d09

    cmpl-float v0, v0, v1

    if-nez v0, :cond_0

    iget-object v0, p0, Lm5/a;->c:Ljava/lang/Object;

    check-cast v0, Ljava/lang/Float;

    invoke-virtual {v0}, Ljava/lang/Float;->floatValue()F

    move-result v0

    iput v0, p0, Lm5/a;->j:F

    :cond_0
    iget v0, p0, Lm5/a;->j:F

    return v0
.end method

.method public e()I
    .locals 2

    iget v0, p0, Lm5/a;->l:I

    const v1, 0x2ec8fb09

    if-ne v0, v1, :cond_0

    iget-object v0, p0, Lm5/a;->c:Ljava/lang/Object;

    check-cast v0, Ljava/lang/Integer;

    invoke-virtual {v0}, Ljava/lang/Integer;->intValue()I

    move-result v0

    iput v0, p0, Lm5/a;->l:I

    :cond_0
    iget v0, p0, Lm5/a;->l:I

    return v0
.end method

.method public f()F
    .locals 3

    iget-object v0, p0, Lm5/a;->a:Lcom/airbnb/lottie/h;

    if-nez v0, :cond_0

    const/4 v0, 0x0

    return v0

    :cond_0
    iget v1, p0, Lm5/a;->m:F

    const/4 v2, 0x1

    cmpl-float v1, v1, v2

    if-nez v1, :cond_1

    iget v1, p0, Lm5/a;->g:F

    invoke-virtual {v0}, Lcom/airbnb/lottie/h;->p()F

    move-result v0

    sub-float/2addr v1, v0

    iget-object v0, p0, Lm5/a;->a:Lcom/airbnb/lottie/h;

    invoke-virtual {v0}, Lcom/airbnb/lottie/h;->e()F

    move-result v0

    div-float/2addr v1, v0

    iput v1, p0, Lm5/a;->m:F

    :cond_1
    iget v0, p0, Lm5/a;->m:F

    return v0
.end method

.method public g()F
    .locals 2

    iget v0, p0, Lm5/a;->i:F

    const v1, -0x358c9d09

    cmpl-float v0, v0, v1

    if-nez v0, :cond_0

    iget-object v0, p0, Lm5/a;->b:Ljava/lang/Object;

    check-cast v0, Ljava/lang/Float;

    invoke-virtual {v0}, Ljava/lang/Float;->floatValue()F

    move-result v0

    iput v0, p0, Lm5/a;->i:F

    :cond_0
    iget v0, p0, Lm5/a;->i:F

    return v0
.end method

.method public h()I
    .locals 2

    iget v0, p0, Lm5/a;->k:I

    const v1, 0x2ec8fb09

    if-ne v0, v1, :cond_0

    iget-object v0, p0, Lm5/a;->b:Ljava/lang/Object;

    check-cast v0, Ljava/lang/Integer;

    invoke-virtual {v0}, Ljava/lang/Integer;->intValue()I

    move-result v0

    iput v0, p0, Lm5/a;->k:I

    :cond_0
    iget v0, p0, Lm5/a;->k:I

    return v0
.end method

.method public i()Z
    .locals 1

    iget-object v0, p0, Lm5/a;->d:Landroid/view/animation/Interpolator;

    if-nez v0, :cond_0

    iget-object v0, p0, Lm5/a;->e:Landroid/view/animation/Interpolator;

    if-nez v0, :cond_0

    iget-object v0, p0, Lm5/a;->f:Landroid/view/animation/Interpolator;

    if-nez v0, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method public toString()Ljava/lang/String;
    .locals 2

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "Keyframe{startValue="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v1, p0, Lm5/a;->b:Ljava/lang/Object;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v1, ", endValue="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v1, p0, Lm5/a;->c:Ljava/lang/Object;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v1, ", startFrame="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget v1, p0, Lm5/a;->g:F

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(F)Ljava/lang/StringBuilder;

    const-string v1, ", endFrame="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v1, p0, Lm5/a;->h:Ljava/lang/Float;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v1, ", interpolator="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v1, p0, Lm5/a;->d:Landroid/view/animation/Interpolator;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const/16 v1, 0x7d

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
