.class public final synthetic Ln2/l;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/lang/Runnable;


# instance fields
.field public final synthetic a:Landroidx/media3/exoplayer/drm/b$a;

.field public final synthetic b:Landroidx/media3/exoplayer/drm/b;

.field public final synthetic c:I


# direct methods
.method public synthetic constructor <init>(Landroidx/media3/exoplayer/drm/b$a;Landroidx/media3/exoplayer/drm/b;I)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Ln2/l;->a:Landroidx/media3/exoplayer/drm/b$a;

    iput-object p2, p0, Ln2/l;->b:Landroidx/media3/exoplayer/drm/b;

    iput p3, p0, Ln2/l;->c:I

    return-void
.end method


# virtual methods
.method public final run()V
    .locals 3

    iget-object v0, p0, Ln2/l;->a:Landroidx/media3/exoplayer/drm/b$a;

    iget-object v1, p0, Ln2/l;->b:Landroidx/media3/exoplayer/drm/b;

    iget v2, p0, Ln2/l;->c:I

    invoke-static {v0, v1, v2}, Landroidx/media3/exoplayer/drm/b$a;->b(Landroidx/media3/exoplayer/drm/b$a;Landroidx/media3/exoplayer/drm/b;I)V

    return-void
.end method
