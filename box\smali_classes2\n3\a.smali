.class public final Ln3/a;
.super Lh3/c;


# instance fields
.field public final a:Le2/c0;

.field public final b:Le2/b0;

.field public c:Le2/i0;


# direct methods
.method public constructor <init>()V
    .locals 1

    invoke-direct {p0}, Lh3/c;-><init>()V

    new-instance v0, Le2/c0;

    invoke-direct {v0}, Le2/c0;-><init>()V

    iput-object v0, p0, Ln3/a;->a:Le2/c0;

    new-instance v0, Le2/b0;

    invoke-direct {v0}, Le2/b0;-><init>()V

    iput-object v0, p0, Ln3/a;->b:Le2/b0;

    return-void
.end method


# virtual methods
.method public b(Lh3/b;Ljava/nio/ByteBuffer;)Landroidx/media3/common/Metadata;
    .locals 5

    iget-object v0, p0, Ln3/a;->c:Le2/i0;

    if-eqz v0, :cond_0

    iget-wide v1, p1, Lh3/b;->i:J

    invoke-virtual {v0}, Le2/i0;->f()J

    move-result-wide v3

    cmp-long v0, v1, v3

    if-eqz v0, :cond_1

    :cond_0
    new-instance v0, Le2/i0;

    iget-wide v1, p1, Landroidx/media3/decoder/DecoderInputBuffer;->e:J

    invoke-direct {v0, v1, v2}, Le2/i0;-><init>(J)V

    iput-object v0, p0, Ln3/a;->c:Le2/i0;

    iget-wide v1, p1, Landroidx/media3/decoder/DecoderInputBuffer;->e:J

    iget-wide v3, p1, Lh3/b;->i:J

    sub-long/2addr v1, v3

    invoke-virtual {v0, v1, v2}, Le2/i0;->a(J)J

    :cond_1
    invoke-virtual {p2}, Ljava/nio/ByteBuffer;->array()[B

    move-result-object p1

    invoke-virtual {p2}, Ljava/nio/Buffer;->limit()I

    move-result p2

    iget-object v0, p0, Ln3/a;->a:Le2/c0;

    invoke-virtual {v0, p1, p2}, Le2/c0;->S([BI)V

    iget-object v0, p0, Ln3/a;->b:Le2/b0;

    invoke-virtual {v0, p1, p2}, Le2/b0;->o([BI)V

    iget-object p1, p0, Ln3/a;->b:Le2/b0;

    const/16 p2, 0x27

    invoke-virtual {p1, p2}, Le2/b0;->r(I)V

    iget-object p1, p0, Ln3/a;->b:Le2/b0;

    const/4 p2, 0x1

    invoke-virtual {p1, p2}, Le2/b0;->h(I)I

    move-result p1

    int-to-long v0, p1

    const/16 p1, 0x20

    shl-long/2addr v0, p1

    iget-object v2, p0, Ln3/a;->b:Le2/b0;

    invoke-virtual {v2, p1}, Le2/b0;->h(I)I

    move-result p1

    int-to-long v2, p1

    or-long/2addr v0, v2

    iget-object p1, p0, Ln3/a;->b:Le2/b0;

    const/16 v2, 0x14

    invoke-virtual {p1, v2}, Le2/b0;->r(I)V

    iget-object p1, p0, Ln3/a;->b:Le2/b0;

    const/16 v2, 0xc

    invoke-virtual {p1, v2}, Le2/b0;->h(I)I

    move-result p1

    iget-object v2, p0, Ln3/a;->b:Le2/b0;

    const/16 v3, 0x8

    invoke-virtual {v2, v3}, Le2/b0;->h(I)I

    move-result v2

    iget-object v3, p0, Ln3/a;->a:Le2/c0;

    const/16 v4, 0xe

    invoke-virtual {v3, v4}, Le2/c0;->V(I)V

    if-eqz v2, :cond_6

    const/16 v3, 0xff

    if-eq v2, v3, :cond_5

    const/4 p1, 0x4

    if-eq v2, p1, :cond_4

    const/4 p1, 0x5

    if-eq v2, p1, :cond_3

    const/4 p1, 0x6

    if-eq v2, p1, :cond_2

    const/4 p1, 0x0

    goto :goto_0

    :cond_2
    iget-object p1, p0, Ln3/a;->a:Le2/c0;

    iget-object v2, p0, Ln3/a;->c:Le2/i0;

    invoke-static {p1, v0, v1, v2}, Landroidx/media3/extractor/metadata/scte35/TimeSignalCommand;->a(Le2/c0;JLe2/i0;)Landroidx/media3/extractor/metadata/scte35/TimeSignalCommand;

    move-result-object p1

    goto :goto_0

    :cond_3
    iget-object p1, p0, Ln3/a;->a:Le2/c0;

    iget-object v2, p0, Ln3/a;->c:Le2/i0;

    invoke-static {p1, v0, v1, v2}, Landroidx/media3/extractor/metadata/scte35/SpliceInsertCommand;->a(Le2/c0;JLe2/i0;)Landroidx/media3/extractor/metadata/scte35/SpliceInsertCommand;

    move-result-object p1

    goto :goto_0

    :cond_4
    iget-object p1, p0, Ln3/a;->a:Le2/c0;

    invoke-static {p1}, Landroidx/media3/extractor/metadata/scte35/SpliceScheduleCommand;->a(Le2/c0;)Landroidx/media3/extractor/metadata/scte35/SpliceScheduleCommand;

    move-result-object p1

    goto :goto_0

    :cond_5
    iget-object v2, p0, Ln3/a;->a:Le2/c0;

    invoke-static {v2, p1, v0, v1}, Landroidx/media3/extractor/metadata/scte35/PrivateCommand;->a(Le2/c0;IJ)Landroidx/media3/extractor/metadata/scte35/PrivateCommand;

    move-result-object p1

    goto :goto_0

    :cond_6
    new-instance p1, Landroidx/media3/extractor/metadata/scte35/SpliceNullCommand;

    invoke-direct {p1}, Landroidx/media3/extractor/metadata/scte35/SpliceNullCommand;-><init>()V

    :goto_0
    const/4 v0, 0x0

    if-nez p1, :cond_7

    new-instance p1, Landroidx/media3/common/Metadata;

    new-array p2, v0, [Landroidx/media3/common/Metadata$Entry;

    invoke-direct {p1, p2}, Landroidx/media3/common/Metadata;-><init>([Landroidx/media3/common/Metadata$Entry;)V

    goto :goto_1

    :cond_7
    new-instance v1, Landroidx/media3/common/Metadata;

    new-array p2, p2, [Landroidx/media3/common/Metadata$Entry;

    aput-object p1, p2, v0

    invoke-direct {v1, p2}, Landroidx/media3/common/Metadata;-><init>([Landroidx/media3/common/Metadata$Entry;)V

    move-object p1, v1

    :goto_1
    return-object p1
.end method
