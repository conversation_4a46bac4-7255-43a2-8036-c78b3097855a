.class public abstract Ln/b$e;
.super Ln/b$f;

# interfaces
.implements Ljava/util/Iterator;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Ln/b;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x409
    name = "e"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "<K:",
        "Ljava/lang/Object;",
        "V:",
        "Ljava/lang/Object;",
        ">",
        "Ln/b$f<",
        "TK;TV;>;",
        "Ljava/util/Iterator<",
        "Ljava/util/Map$Entry<",
        "TK;TV;>;>;"
    }
.end annotation


# instance fields
.field public a:Ln/b$c;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ln/b$c<",
            "TK;TV;>;"
        }
    .end annotation
.end field

.field public b:Ln/b$c;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ln/b$c<",
            "TK;TV;>;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(Ln/b$c;Ln/b$c;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ln/b$c<",
            "TK;TV;>;",
            "Ln/b$c<",
            "TK;TV;>;)V"
        }
    .end annotation

    invoke-direct {p0}, Ln/b$f;-><init>()V

    iput-object p2, p0, Ln/b$e;->a:Ln/b$c;

    iput-object p1, p0, Ln/b$e;->b:Ln/b$c;

    return-void
.end method


# virtual methods
.method public a(Ln/b$c;)V
    .locals 1
    .param p1    # Ln/b$c;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ln/b$c<",
            "TK;TV;>;)V"
        }
    .end annotation

    iget-object v0, p0, Ln/b$e;->a:Ln/b$c;

    if-ne v0, p1, :cond_0

    iget-object v0, p0, Ln/b$e;->b:Ln/b$c;

    if-ne p1, v0, :cond_0

    const/4 v0, 0x0

    iput-object v0, p0, Ln/b$e;->b:Ln/b$c;

    iput-object v0, p0, Ln/b$e;->a:Ln/b$c;

    :cond_0
    iget-object v0, p0, Ln/b$e;->a:Ln/b$c;

    if-ne v0, p1, :cond_1

    invoke-virtual {p0, v0}, Ln/b$e;->c(Ln/b$c;)Ln/b$c;

    move-result-object v0

    iput-object v0, p0, Ln/b$e;->a:Ln/b$c;

    :cond_1
    iget-object v0, p0, Ln/b$e;->b:Ln/b$c;

    if-ne v0, p1, :cond_2

    invoke-virtual {p0}, Ln/b$e;->f()Ln/b$c;

    move-result-object p1

    iput-object p1, p0, Ln/b$e;->b:Ln/b$c;

    :cond_2
    return-void
.end method

.method public abstract c(Ln/b$c;)Ln/b$c;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ln/b$c<",
            "TK;TV;>;)",
            "Ln/b$c<",
            "TK;TV;>;"
        }
    .end annotation
.end method

.method public abstract d(Ln/b$c;)Ln/b$c;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ln/b$c<",
            "TK;TV;>;)",
            "Ln/b$c<",
            "TK;TV;>;"
        }
    .end annotation
.end method

.method public e()Ljava/util/Map$Entry;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Map$Entry<",
            "TK;TV;>;"
        }
    .end annotation

    iget-object v0, p0, Ln/b$e;->b:Ln/b$c;

    invoke-virtual {p0}, Ln/b$e;->f()Ln/b$c;

    move-result-object v1

    iput-object v1, p0, Ln/b$e;->b:Ln/b$c;

    return-object v0
.end method

.method public final f()Ln/b$c;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ln/b$c<",
            "TK;TV;>;"
        }
    .end annotation

    iget-object v0, p0, Ln/b$e;->b:Ln/b$c;

    iget-object v1, p0, Ln/b$e;->a:Ln/b$c;

    if-eq v0, v1, :cond_1

    if-nez v1, :cond_0

    goto :goto_0

    :cond_0
    invoke-virtual {p0, v0}, Ln/b$e;->d(Ln/b$c;)Ln/b$c;

    move-result-object v0

    return-object v0

    :cond_1
    :goto_0
    const/4 v0, 0x0

    return-object v0
.end method

.method public hasNext()Z
    .locals 1

    iget-object v0, p0, Ln/b$e;->b:Ln/b$c;

    if-eqz v0, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method public bridge synthetic next()Ljava/lang/Object;
    .locals 1

    invoke-virtual {p0}, Ln/b$e;->e()Ljava/util/Map$Entry;

    move-result-object v0

    return-object v0
.end method
