.class public final Lnet/lucode/hackware/magicindicator/R$id;
.super Ljava/lang/Object;


# static fields
.field public static action_container:I = 0x7f0a0060

.field public static action_divider:I = 0x7f0a0062

.field public static action_image:I = 0x7f0a0063

.field public static action_text:I = 0x7f0a0069

.field public static actions:I = 0x7f0a006a

.field public static async:I = 0x7f0a00ab

.field public static blocking:I = 0x7f0a00d0

.field public static chronometer:I = 0x7f0a0142

.field public static forever:I = 0x7f0a02b7

.field public static icon:I = 0x7f0a030f

.field public static icon_group:I = 0x7f0a0312

.field public static indicator_container:I = 0x7f0a0336

.field public static info:I = 0x7f0a0337

.field public static italic:I = 0x7f0a034a

.field public static line1:I = 0x7f0a04e8

.field public static line3:I = 0x7f0a04e9

.field public static normal:I = 0x7f0a06ae

.field public static notification_background:I = 0x7f0a06b7

.field public static notification_main_column:I = 0x7f0a06bc

.field public static notification_main_column_container:I = 0x7f0a06bd

.field public static right_icon:I = 0x7f0a0796

.field public static right_side:I = 0x7f0a0798

.field public static scroll_view:I = 0x7f0a07d7

.field public static tag_transition_group:I = 0x7f0a08e3

.field public static tag_unhandled_key_event_manager:I = 0x7f0a08e4

.field public static tag_unhandled_key_listeners:I = 0x7f0a08e5

.field public static text:I = 0x7f0a08ea

.field public static text2:I = 0x7f0a08eb

.field public static time:I = 0x7f0a0902

.field public static title:I = 0x7f0a0908

.field public static title_container:I = 0x7f0a0910


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
