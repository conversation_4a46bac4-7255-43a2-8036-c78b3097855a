.class public Lo0/g;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lo0/g$a;
    }
.end annotation


# instance fields
.field public final a:Ljava/lang/Object;


# direct methods
.method public constructor <init>(Landroid/view/ViewStructure;)V
    .locals 0
    .param p1    # Landroid/view/ViewStructure;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lo0/g;->a:Ljava/lang/Object;

    return-void
.end method

.method public static i(Landroid/view/ViewStructure;)Lo0/g;
    .locals 1
    .param p0    # Landroid/view/ViewStructure;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    .annotation build Landroidx/annotation/RequiresApi;
        value = 0x17
    .end annotation

    new-instance v0, Lo0/g;

    invoke-direct {v0, p0}, Lo0/g;-><init>(Landroid/view/ViewStructure;)V

    return-object v0
.end method


# virtual methods
.method public a()Landroid/os/Bundle;
    .locals 2
    .annotation build Landroidx/annotation/Nullable;
    .end annotation

    sget v0, Landroid/os/Build$VERSION;->SDK_INT:I

    const/16 v1, 0x17

    if-lt v0, v1, :cond_0

    iget-object v0, p0, Lo0/g;->a:Ljava/lang/Object;

    invoke-static {v0}, Lo0/d;->a(Ljava/lang/Object;)Landroid/view/ViewStructure;

    move-result-object v0

    invoke-static {v0}, Lo0/g$a;->a(Landroid/view/ViewStructure;)Landroid/os/Bundle;

    move-result-object v0

    return-object v0

    :cond_0
    const/4 v0, 0x0

    return-object v0
.end method

.method public b(Ljava/lang/String;)V
    .locals 2
    .param p1    # Ljava/lang/String;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param

    sget v0, Landroid/os/Build$VERSION;->SDK_INT:I

    const/16 v1, 0x17

    if-lt v0, v1, :cond_0

    iget-object v0, p0, Lo0/g;->a:Ljava/lang/Object;

    invoke-static {v0}, Lo0/d;->a(Ljava/lang/Object;)Landroid/view/ViewStructure;

    move-result-object v0

    invoke-static {v0, p1}, Lo0/g$a;->b(Landroid/view/ViewStructure;Ljava/lang/String;)V

    :cond_0
    return-void
.end method

.method public c(Ljava/lang/CharSequence;)V
    .locals 2
    .param p1    # Ljava/lang/CharSequence;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param

    sget v0, Landroid/os/Build$VERSION;->SDK_INT:I

    const/16 v1, 0x17

    if-lt v0, v1, :cond_0

    iget-object v0, p0, Lo0/g;->a:Ljava/lang/Object;

    invoke-static {v0}, Lo0/d;->a(Ljava/lang/Object;)Landroid/view/ViewStructure;

    move-result-object v0

    invoke-static {v0, p1}, Lo0/g$a;->c(Landroid/view/ViewStructure;Ljava/lang/CharSequence;)V

    :cond_0
    return-void
.end method

.method public d(IIIIII)V
    .locals 8

    sget v0, Landroid/os/Build$VERSION;->SDK_INT:I

    const/16 v1, 0x17

    if-lt v0, v1, :cond_0

    iget-object v0, p0, Lo0/g;->a:Ljava/lang/Object;

    invoke-static {v0}, Lo0/d;->a(Ljava/lang/Object;)Landroid/view/ViewStructure;

    move-result-object v1

    move v2, p1

    move v3, p2

    move v4, p3

    move v5, p4

    move v6, p5

    move v7, p6

    invoke-static/range {v1 .. v7}, Lo0/g$a;->d(Landroid/view/ViewStructure;IIIIII)V

    :cond_0
    return-void
.end method

.method public e(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;)V
    .locals 2
    .param p2    # Ljava/lang/String;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .param p3    # Ljava/lang/String;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .param p4    # Ljava/lang/String;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    sget v0, Landroid/os/Build$VERSION;->SDK_INT:I

    const/16 v1, 0x17

    if-lt v0, v1, :cond_0

    iget-object v0, p0, Lo0/g;->a:Ljava/lang/Object;

    invoke-static {v0}, Lo0/d;->a(Ljava/lang/Object;)Landroid/view/ViewStructure;

    move-result-object v0

    invoke-static {v0, p1, p2, p3, p4}, Lo0/g$a;->e(Landroid/view/ViewStructure;ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    :cond_0
    return-void
.end method

.method public f(Ljava/lang/CharSequence;)V
    .locals 2
    .param p1    # Ljava/lang/CharSequence;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param

    sget v0, Landroid/os/Build$VERSION;->SDK_INT:I

    const/16 v1, 0x17

    if-lt v0, v1, :cond_0

    iget-object v0, p0, Lo0/g;->a:Ljava/lang/Object;

    invoke-static {v0}, Lo0/d;->a(Ljava/lang/Object;)Landroid/view/ViewStructure;

    move-result-object v0

    invoke-static {v0, p1}, Lo0/g$a;->f(Landroid/view/ViewStructure;Ljava/lang/CharSequence;)V

    :cond_0
    return-void
.end method

.method public g(FIII)V
    .locals 2

    sget v0, Landroid/os/Build$VERSION;->SDK_INT:I

    const/16 v1, 0x17

    if-lt v0, v1, :cond_0

    iget-object v0, p0, Lo0/g;->a:Ljava/lang/Object;

    invoke-static {v0}, Lo0/d;->a(Ljava/lang/Object;)Landroid/view/ViewStructure;

    move-result-object v0

    invoke-static {v0, p1, p2, p3, p4}, Lo0/g$a;->g(Landroid/view/ViewStructure;FIII)V

    :cond_0
    return-void
.end method

.method public h()Landroid/view/ViewStructure;
    .locals 1
    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    .annotation build Landroidx/annotation/RequiresApi;
        value = 0x17
    .end annotation

    iget-object v0, p0, Lo0/g;->a:Ljava/lang/Object;

    invoke-static {v0}, Lo0/d;->a(Ljava/lang/Object;)Landroid/view/ViewStructure;

    move-result-object v0

    return-object v0
.end method
