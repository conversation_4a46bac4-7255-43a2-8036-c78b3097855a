.class public final Lo3/e$b;
.super Ljava/lang/Object;

# interfaces
.implements Lo3/b;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lo3/e;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x11
    name = "b"
.end annotation


# instance fields
.field public final synthetic a:Lo3/e;


# direct methods
.method public constructor <init>(Lo3/e;)V
    .locals 0

    iput-object p1, p0, Lo3/e$b;->a:Lo3/e;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public synthetic constructor <init>(Lo3/e;Lo3/e$a;)V
    .locals 0

    invoke-direct {p0, p1}, Lo3/e$b;-><init>(Lo3/e;)V

    return-void
.end method


# virtual methods
.method public a(IILz2/t;)V
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget-object v0, p0, Lo3/e$b;->a:Lo3/e;

    invoke-virtual {v0, p1, p2, p3}, Lo3/e;->l(IILz2/t;)V

    return-void
.end method

.method public endMasterElement(I)V
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/common/ParserException;
        }
    .end annotation

    iget-object v0, p0, Lo3/e$b;->a:Lo3/e;

    invoke-virtual {v0, p1}, Lo3/e;->o(I)V

    return-void
.end method

.method public floatElement(ID)V
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/common/ParserException;
        }
    .end annotation

    iget-object v0, p0, Lo3/e$b;->a:Lo3/e;

    invoke-virtual {v0, p1, p2, p3}, Lo3/e;->r(ID)V

    return-void
.end method

.method public getElementType(I)I
    .locals 1

    iget-object v0, p0, Lo3/e$b;->a:Lo3/e;

    invoke-virtual {v0, p1}, Lo3/e;->u(I)I

    move-result p1

    return p1
.end method

.method public integerElement(IJ)V
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/common/ParserException;
        }
    .end annotation

    iget-object v0, p0, Lo3/e$b;->a:Lo3/e;

    invoke-virtual {v0, p1, p2, p3}, Lo3/e;->x(IJ)V

    return-void
.end method

.method public isLevel1Element(I)Z
    .locals 1

    iget-object v0, p0, Lo3/e$b;->a:Lo3/e;

    invoke-virtual {v0, p1}, Lo3/e;->z(I)Z

    move-result p1

    return p1
.end method

.method public startMasterElement(IJJ)V
    .locals 6
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/common/ParserException;
        }
    .end annotation

    iget-object v0, p0, Lo3/e$b;->a:Lo3/e;

    move v1, p1

    move-wide v2, p2

    move-wide v4, p4

    invoke-virtual/range {v0 .. v5}, Lo3/e;->G(IJJ)V

    return-void
.end method

.method public stringElement(ILjava/lang/String;)V
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/common/ParserException;
        }
    .end annotation

    iget-object v0, p0, Lo3/e$b;->a:Lo3/e;

    invoke-virtual {v0, p1, p2}, Lo3/e;->H(ILjava/lang/String;)V

    return-void
.end method
