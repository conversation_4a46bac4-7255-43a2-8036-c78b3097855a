.class public final synthetic Log/n;
.super Ljava/lang/Object;

# interfaces
.implements Lge/h;


# instance fields
.field public final synthetic a:Lge/b0;


# direct methods
.method public synthetic constructor <init>(Lge/b0;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Log/n;->a:Lge/b0;

    return-void
.end method


# virtual methods
.method public final a(Lge/e;)Ljava/lang/Object;
    .locals 1

    iget-object v0, p0, Log/n;->a:Lge/b0;

    invoke-static {v0, p1}, Lcom/google/firebase/remoteconfig/RemoteConfigRegistrar;->a(Lge/b0;Lge/e;)Log/m;

    move-result-object p1

    return-object p1
.end method
