.class public final Lokhttp3/CipherSuite;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lokhttp3/CipherSuite$a;
    }
.end annotation

.annotation runtime L<PERSON>lin/Metadata;
.end annotation


# static fields
.field public static final A:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final A0:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final B:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final B0:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final C:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final C0:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final D:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final D0:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final E:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final E0:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final F:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final F0:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final G:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final G0:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final H:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final H0:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final I:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final I0:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final J:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final J0:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final K:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final K0:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final L:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final L0:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final M:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final M0:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final N:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final N0:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final O:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final O0:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final P:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final P0:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final Q:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final Q0:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final R:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final R0:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final S:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final S0:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final T:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final T0:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final U:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final U0:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final V:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final V0:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final W:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final W0:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final X:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final X0:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final Y:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final Y0:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final Z:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final Z0:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final a0:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final a1:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final b:Ljava/util/Comparator;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Comparator<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field public static final b0:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final b1:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final c:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Lokhttp3/CipherSuite;",
            ">;"
        }
    .end annotation
.end field

.field public static final c0:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final c1:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final d:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final d0:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final d1:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final e:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final e0:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final e1:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final f:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final f0:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final f1:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final g:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final g0:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final g1:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final h:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final h0:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final h1:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final i:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final i0:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final i1:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final j:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final j0:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final j1:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final k:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final k0:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final k1:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final l:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final l0:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final l1:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final m:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final m0:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final m1:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final n:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final n0:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final n1:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final o:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final o0:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final o1:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final p:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final p0:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final p1:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final q:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final q0:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final q1:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final r:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final r0:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final r1:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final s:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final s0:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final s1:Lokhttp3/CipherSuite$a;

.field public static final t:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final t0:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final u:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final u0:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final v:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final v0:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final w:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final w0:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final x:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final x0:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final y:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final y0:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final z:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field

.field public static final z0:Lokhttp3/CipherSuite;
    .annotation build Lkotlin/jvm/JvmField;
    .end annotation
.end field


# instance fields
.field public final a:Ljava/lang/String;


# direct methods
.method static constructor <clinit>()V
    .locals 3

    new-instance v0, Lokhttp3/CipherSuite$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lokhttp3/CipherSuite$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lokhttp3/CipherSuite;->s1:Lokhttp3/CipherSuite$a;

    new-instance v1, Lokhttp3/CipherSuite$Companion$ORDER_BY_NAME$1;

    invoke-direct {v1}, Lokhttp3/CipherSuite$Companion$ORDER_BY_NAME$1;-><init>()V

    sput-object v1, Lokhttp3/CipherSuite;->b:Ljava/util/Comparator;

    new-instance v1, Ljava/util/LinkedHashMap;

    invoke-direct {v1}, Ljava/util/LinkedHashMap;-><init>()V

    sput-object v1, Lokhttp3/CipherSuite;->c:Ljava/util/Map;

    const-string v1, "SSL_RSA_WITH_NULL_MD5"

    const/4 v2, 0x1

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->d:Lokhttp3/CipherSuite;

    const-string v1, "SSL_RSA_WITH_NULL_SHA"

    const/4 v2, 0x2

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->e:Lokhttp3/CipherSuite;

    const-string v1, "SSL_RSA_EXPORT_WITH_RC4_40_MD5"

    const/4 v2, 0x3

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->f:Lokhttp3/CipherSuite;

    const-string v1, "SSL_RSA_WITH_RC4_128_MD5"

    const/4 v2, 0x4

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->g:Lokhttp3/CipherSuite;

    const-string v1, "SSL_RSA_WITH_RC4_128_SHA"

    const/4 v2, 0x5

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->h:Lokhttp3/CipherSuite;

    const-string v1, "SSL_RSA_EXPORT_WITH_DES40_CBC_SHA"

    const/16 v2, 0x8

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->i:Lokhttp3/CipherSuite;

    const-string v1, "SSL_RSA_WITH_DES_CBC_SHA"

    const/16 v2, 0x9

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->j:Lokhttp3/CipherSuite;

    const-string v1, "SSL_RSA_WITH_3DES_EDE_CBC_SHA"

    const/16 v2, 0xa

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->k:Lokhttp3/CipherSuite;

    const-string v1, "SSL_DHE_DSS_EXPORT_WITH_DES40_CBC_SHA"

    const/16 v2, 0x11

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->l:Lokhttp3/CipherSuite;

    const-string v1, "SSL_DHE_DSS_WITH_DES_CBC_SHA"

    const/16 v2, 0x12

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->m:Lokhttp3/CipherSuite;

    const-string v1, "SSL_DHE_DSS_WITH_3DES_EDE_CBC_SHA"

    const/16 v2, 0x13

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->n:Lokhttp3/CipherSuite;

    const-string v1, "SSL_DHE_RSA_EXPORT_WITH_DES40_CBC_SHA"

    const/16 v2, 0x14

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->o:Lokhttp3/CipherSuite;

    const-string v1, "SSL_DHE_RSA_WITH_DES_CBC_SHA"

    const/16 v2, 0x15

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->p:Lokhttp3/CipherSuite;

    const-string v1, "SSL_DHE_RSA_WITH_3DES_EDE_CBC_SHA"

    const/16 v2, 0x16

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->q:Lokhttp3/CipherSuite;

    const-string v1, "SSL_DH_anon_EXPORT_WITH_RC4_40_MD5"

    const/16 v2, 0x17

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->r:Lokhttp3/CipherSuite;

    const-string v1, "SSL_DH_anon_WITH_RC4_128_MD5"

    const/16 v2, 0x18

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->s:Lokhttp3/CipherSuite;

    const-string v1, "SSL_DH_anon_EXPORT_WITH_DES40_CBC_SHA"

    const/16 v2, 0x19

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->t:Lokhttp3/CipherSuite;

    const-string v1, "SSL_DH_anon_WITH_DES_CBC_SHA"

    const/16 v2, 0x1a

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->u:Lokhttp3/CipherSuite;

    const-string v1, "SSL_DH_anon_WITH_3DES_EDE_CBC_SHA"

    const/16 v2, 0x1b

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->v:Lokhttp3/CipherSuite;

    const-string v1, "TLS_KRB5_WITH_DES_CBC_SHA"

    const/16 v2, 0x1e

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->w:Lokhttp3/CipherSuite;

    const-string v1, "TLS_KRB5_WITH_3DES_EDE_CBC_SHA"

    const/16 v2, 0x1f

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->x:Lokhttp3/CipherSuite;

    const-string v1, "TLS_KRB5_WITH_RC4_128_SHA"

    const/16 v2, 0x20

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->y:Lokhttp3/CipherSuite;

    const-string v1, "TLS_KRB5_WITH_DES_CBC_MD5"

    const/16 v2, 0x22

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->z:Lokhttp3/CipherSuite;

    const-string v1, "TLS_KRB5_WITH_3DES_EDE_CBC_MD5"

    const/16 v2, 0x23

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->A:Lokhttp3/CipherSuite;

    const-string v1, "TLS_KRB5_WITH_RC4_128_MD5"

    const/16 v2, 0x24

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->B:Lokhttp3/CipherSuite;

    const-string v1, "TLS_KRB5_EXPORT_WITH_DES_CBC_40_SHA"

    const/16 v2, 0x26

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->C:Lokhttp3/CipherSuite;

    const-string v1, "TLS_KRB5_EXPORT_WITH_RC4_40_SHA"

    const/16 v2, 0x28

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->D:Lokhttp3/CipherSuite;

    const-string v1, "TLS_KRB5_EXPORT_WITH_DES_CBC_40_MD5"

    const/16 v2, 0x29

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->E:Lokhttp3/CipherSuite;

    const-string v1, "TLS_KRB5_EXPORT_WITH_RC4_40_MD5"

    const/16 v2, 0x2b

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->F:Lokhttp3/CipherSuite;

    const-string v1, "TLS_RSA_WITH_AES_128_CBC_SHA"

    const/16 v2, 0x2f

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->G:Lokhttp3/CipherSuite;

    const-string v1, "TLS_DHE_DSS_WITH_AES_128_CBC_SHA"

    const/16 v2, 0x32

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->H:Lokhttp3/CipherSuite;

    const-string v1, "TLS_DHE_RSA_WITH_AES_128_CBC_SHA"

    const/16 v2, 0x33

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->I:Lokhttp3/CipherSuite;

    const-string v1, "TLS_DH_anon_WITH_AES_128_CBC_SHA"

    const/16 v2, 0x34

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->J:Lokhttp3/CipherSuite;

    const-string v1, "TLS_RSA_WITH_AES_256_CBC_SHA"

    const/16 v2, 0x35

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->K:Lokhttp3/CipherSuite;

    const-string v1, "TLS_DHE_DSS_WITH_AES_256_CBC_SHA"

    const/16 v2, 0x38

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->L:Lokhttp3/CipherSuite;

    const-string v1, "TLS_DHE_RSA_WITH_AES_256_CBC_SHA"

    const/16 v2, 0x39

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->M:Lokhttp3/CipherSuite;

    const-string v1, "TLS_DH_anon_WITH_AES_256_CBC_SHA"

    const/16 v2, 0x3a

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->N:Lokhttp3/CipherSuite;

    const-string v1, "TLS_RSA_WITH_NULL_SHA256"

    const/16 v2, 0x3b

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->O:Lokhttp3/CipherSuite;

    const-string v1, "TLS_RSA_WITH_AES_128_CBC_SHA256"

    const/16 v2, 0x3c

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->P:Lokhttp3/CipherSuite;

    const-string v1, "TLS_RSA_WITH_AES_256_CBC_SHA256"

    const/16 v2, 0x3d

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->Q:Lokhttp3/CipherSuite;

    const-string v1, "TLS_DHE_DSS_WITH_AES_128_CBC_SHA256"

    const/16 v2, 0x40

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->R:Lokhttp3/CipherSuite;

    const-string v1, "TLS_RSA_WITH_CAMELLIA_128_CBC_SHA"

    const/16 v2, 0x41

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->S:Lokhttp3/CipherSuite;

    const-string v1, "TLS_DHE_DSS_WITH_CAMELLIA_128_CBC_SHA"

    const/16 v2, 0x44

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->T:Lokhttp3/CipherSuite;

    const-string v1, "TLS_DHE_RSA_WITH_CAMELLIA_128_CBC_SHA"

    const/16 v2, 0x45

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->U:Lokhttp3/CipherSuite;

    const-string v1, "TLS_DHE_RSA_WITH_AES_128_CBC_SHA256"

    const/16 v2, 0x67

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->V:Lokhttp3/CipherSuite;

    const-string v1, "TLS_DHE_DSS_WITH_AES_256_CBC_SHA256"

    const/16 v2, 0x6a

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->W:Lokhttp3/CipherSuite;

    const-string v1, "TLS_DHE_RSA_WITH_AES_256_CBC_SHA256"

    const/16 v2, 0x6b

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->X:Lokhttp3/CipherSuite;

    const-string v1, "TLS_DH_anon_WITH_AES_128_CBC_SHA256"

    const/16 v2, 0x6c

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->Y:Lokhttp3/CipherSuite;

    const-string v1, "TLS_DH_anon_WITH_AES_256_CBC_SHA256"

    const/16 v2, 0x6d

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->Z:Lokhttp3/CipherSuite;

    const-string v1, "TLS_RSA_WITH_CAMELLIA_256_CBC_SHA"

    const/16 v2, 0x84

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->a0:Lokhttp3/CipherSuite;

    const-string v1, "TLS_DHE_DSS_WITH_CAMELLIA_256_CBC_SHA"

    const/16 v2, 0x87

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->b0:Lokhttp3/CipherSuite;

    const-string v1, "TLS_DHE_RSA_WITH_CAMELLIA_256_CBC_SHA"

    const/16 v2, 0x88

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->c0:Lokhttp3/CipherSuite;

    const-string v1, "TLS_PSK_WITH_RC4_128_SHA"

    const/16 v2, 0x8a

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->d0:Lokhttp3/CipherSuite;

    const-string v1, "TLS_PSK_WITH_3DES_EDE_CBC_SHA"

    const/16 v2, 0x8b

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->e0:Lokhttp3/CipherSuite;

    const-string v1, "TLS_PSK_WITH_AES_128_CBC_SHA"

    const/16 v2, 0x8c

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->f0:Lokhttp3/CipherSuite;

    const-string v1, "TLS_PSK_WITH_AES_256_CBC_SHA"

    const/16 v2, 0x8d

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->g0:Lokhttp3/CipherSuite;

    const-string v1, "TLS_RSA_WITH_SEED_CBC_SHA"

    const/16 v2, 0x96

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->h0:Lokhttp3/CipherSuite;

    const-string v1, "TLS_RSA_WITH_AES_128_GCM_SHA256"

    const/16 v2, 0x9c

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->i0:Lokhttp3/CipherSuite;

    const-string v1, "TLS_RSA_WITH_AES_256_GCM_SHA384"

    const/16 v2, 0x9d

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->j0:Lokhttp3/CipherSuite;

    const-string v1, "TLS_DHE_RSA_WITH_AES_128_GCM_SHA256"

    const/16 v2, 0x9e

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->k0:Lokhttp3/CipherSuite;

    const-string v1, "TLS_DHE_RSA_WITH_AES_256_GCM_SHA384"

    const/16 v2, 0x9f

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->l0:Lokhttp3/CipherSuite;

    const-string v1, "TLS_DHE_DSS_WITH_AES_128_GCM_SHA256"

    const/16 v2, 0xa2

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->m0:Lokhttp3/CipherSuite;

    const-string v1, "TLS_DHE_DSS_WITH_AES_256_GCM_SHA384"

    const/16 v2, 0xa3

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->n0:Lokhttp3/CipherSuite;

    const-string v1, "TLS_DH_anon_WITH_AES_128_GCM_SHA256"

    const/16 v2, 0xa6

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->o0:Lokhttp3/CipherSuite;

    const-string v1, "TLS_DH_anon_WITH_AES_256_GCM_SHA384"

    const/16 v2, 0xa7

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->p0:Lokhttp3/CipherSuite;

    const-string v1, "TLS_EMPTY_RENEGOTIATION_INFO_SCSV"

    const/16 v2, 0xff

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->q0:Lokhttp3/CipherSuite;

    const-string v1, "TLS_FALLBACK_SCSV"

    const/16 v2, 0x5600

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->r0:Lokhttp3/CipherSuite;

    const-string v1, "TLS_ECDH_ECDSA_WITH_NULL_SHA"

    const v2, 0xc001

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->s0:Lokhttp3/CipherSuite;

    const-string v1, "TLS_ECDH_ECDSA_WITH_RC4_128_SHA"

    const v2, 0xc002

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->t0:Lokhttp3/CipherSuite;

    const-string v1, "TLS_ECDH_ECDSA_WITH_3DES_EDE_CBC_SHA"

    const v2, 0xc003

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->u0:Lokhttp3/CipherSuite;

    const-string v1, "TLS_ECDH_ECDSA_WITH_AES_128_CBC_SHA"

    const v2, 0xc004

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->v0:Lokhttp3/CipherSuite;

    const-string v1, "TLS_ECDH_ECDSA_WITH_AES_256_CBC_SHA"

    const v2, 0xc005

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->w0:Lokhttp3/CipherSuite;

    const-string v1, "TLS_ECDHE_ECDSA_WITH_NULL_SHA"

    const v2, 0xc006

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->x0:Lokhttp3/CipherSuite;

    const-string v1, "TLS_ECDHE_ECDSA_WITH_RC4_128_SHA"

    const v2, 0xc007

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->y0:Lokhttp3/CipherSuite;

    const-string v1, "TLS_ECDHE_ECDSA_WITH_3DES_EDE_CBC_SHA"

    const v2, 0xc008

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->z0:Lokhttp3/CipherSuite;

    const-string v1, "TLS_ECDHE_ECDSA_WITH_AES_128_CBC_SHA"

    const v2, 0xc009

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->A0:Lokhttp3/CipherSuite;

    const-string v1, "TLS_ECDHE_ECDSA_WITH_AES_256_CBC_SHA"

    const v2, 0xc00a

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->B0:Lokhttp3/CipherSuite;

    const-string v1, "TLS_ECDH_RSA_WITH_NULL_SHA"

    const v2, 0xc00b

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->C0:Lokhttp3/CipherSuite;

    const-string v1, "TLS_ECDH_RSA_WITH_RC4_128_SHA"

    const v2, 0xc00c

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->D0:Lokhttp3/CipherSuite;

    const-string v1, "TLS_ECDH_RSA_WITH_3DES_EDE_CBC_SHA"

    const v2, 0xc00d

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->E0:Lokhttp3/CipherSuite;

    const-string v1, "TLS_ECDH_RSA_WITH_AES_128_CBC_SHA"

    const v2, 0xc00e

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->F0:Lokhttp3/CipherSuite;

    const-string v1, "TLS_ECDH_RSA_WITH_AES_256_CBC_SHA"

    const v2, 0xc00f

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->G0:Lokhttp3/CipherSuite;

    const-string v1, "TLS_ECDHE_RSA_WITH_NULL_SHA"

    const v2, 0xc010

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->H0:Lokhttp3/CipherSuite;

    const-string v1, "TLS_ECDHE_RSA_WITH_RC4_128_SHA"

    const v2, 0xc011

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->I0:Lokhttp3/CipherSuite;

    const-string v1, "TLS_ECDHE_RSA_WITH_3DES_EDE_CBC_SHA"

    const v2, 0xc012

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->J0:Lokhttp3/CipherSuite;

    const-string v1, "TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA"

    const v2, 0xc013

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->K0:Lokhttp3/CipherSuite;

    const-string v1, "TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA"

    const v2, 0xc014

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->L0:Lokhttp3/CipherSuite;

    const-string v1, "TLS_ECDH_anon_WITH_NULL_SHA"

    const v2, 0xc015

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->M0:Lokhttp3/CipherSuite;

    const-string v1, "TLS_ECDH_anon_WITH_RC4_128_SHA"

    const v2, 0xc016

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->N0:Lokhttp3/CipherSuite;

    const-string v1, "TLS_ECDH_anon_WITH_3DES_EDE_CBC_SHA"

    const v2, 0xc017

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->O0:Lokhttp3/CipherSuite;

    const-string v1, "TLS_ECDH_anon_WITH_AES_128_CBC_SHA"

    const v2, 0xc018

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->P0:Lokhttp3/CipherSuite;

    const-string v1, "TLS_ECDH_anon_WITH_AES_256_CBC_SHA"

    const v2, 0xc019

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->Q0:Lokhttp3/CipherSuite;

    const-string v1, "TLS_ECDHE_ECDSA_WITH_AES_128_CBC_SHA256"

    const v2, 0xc023

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->R0:Lokhttp3/CipherSuite;

    const-string v1, "TLS_ECDHE_ECDSA_WITH_AES_256_CBC_SHA384"

    const v2, 0xc024

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->S0:Lokhttp3/CipherSuite;

    const-string v1, "TLS_ECDH_ECDSA_WITH_AES_128_CBC_SHA256"

    const v2, 0xc025

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->T0:Lokhttp3/CipherSuite;

    const-string v1, "TLS_ECDH_ECDSA_WITH_AES_256_CBC_SHA384"

    const v2, 0xc026

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->U0:Lokhttp3/CipherSuite;

    const-string v1, "TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA256"

    const v2, 0xc027

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->V0:Lokhttp3/CipherSuite;

    const-string v1, "TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA384"

    const v2, 0xc028

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->W0:Lokhttp3/CipherSuite;

    const-string v1, "TLS_ECDH_RSA_WITH_AES_128_CBC_SHA256"

    const v2, 0xc029

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->X0:Lokhttp3/CipherSuite;

    const-string v1, "TLS_ECDH_RSA_WITH_AES_256_CBC_SHA384"

    const v2, 0xc02a

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->Y0:Lokhttp3/CipherSuite;

    const-string v1, "TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256"

    const v2, 0xc02b

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->Z0:Lokhttp3/CipherSuite;

    const-string v1, "TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384"

    const v2, 0xc02c

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->a1:Lokhttp3/CipherSuite;

    const-string v1, "TLS_ECDH_ECDSA_WITH_AES_128_GCM_SHA256"

    const v2, 0xc02d

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->b1:Lokhttp3/CipherSuite;

    const-string v1, "TLS_ECDH_ECDSA_WITH_AES_256_GCM_SHA384"

    const v2, 0xc02e

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->c1:Lokhttp3/CipherSuite;

    const-string v1, "TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256"

    const v2, 0xc02f

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->d1:Lokhttp3/CipherSuite;

    const-string v1, "TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384"

    const v2, 0xc030

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->e1:Lokhttp3/CipherSuite;

    const-string v1, "TLS_ECDH_RSA_WITH_AES_128_GCM_SHA256"

    const v2, 0xc031

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->f1:Lokhttp3/CipherSuite;

    const-string v1, "TLS_ECDH_RSA_WITH_AES_256_GCM_SHA384"

    const v2, 0xc032

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->g1:Lokhttp3/CipherSuite;

    const-string v1, "TLS_ECDHE_PSK_WITH_AES_128_CBC_SHA"

    const v2, 0xc035

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->h1:Lokhttp3/CipherSuite;

    const-string v1, "TLS_ECDHE_PSK_WITH_AES_256_CBC_SHA"

    const v2, 0xc036

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->i1:Lokhttp3/CipherSuite;

    const-string v1, "TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305_SHA256"

    const v2, 0xcca8

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->j1:Lokhttp3/CipherSuite;

    const-string v1, "TLS_ECDHE_ECDSA_WITH_CHACHA20_POLY1305_SHA256"

    const v2, 0xcca9

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->k1:Lokhttp3/CipherSuite;

    const-string v1, "TLS_DHE_RSA_WITH_CHACHA20_POLY1305_SHA256"

    const v2, 0xccaa

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->l1:Lokhttp3/CipherSuite;

    const-string v1, "TLS_ECDHE_PSK_WITH_CHACHA20_POLY1305_SHA256"

    const v2, 0xccac

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->m1:Lokhttp3/CipherSuite;

    const-string v1, "TLS_AES_128_GCM_SHA256"

    const/16 v2, 0x1301

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->n1:Lokhttp3/CipherSuite;

    const-string v1, "TLS_AES_256_GCM_SHA384"

    const/16 v2, 0x1302

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->o1:Lokhttp3/CipherSuite;

    const-string v1, "TLS_CHACHA20_POLY1305_SHA256"

    const/16 v2, 0x1303

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->p1:Lokhttp3/CipherSuite;

    const-string v1, "TLS_AES_128_CCM_SHA256"

    const/16 v2, 0x1304

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v1

    sput-object v1, Lokhttp3/CipherSuite;->q1:Lokhttp3/CipherSuite;

    const-string v1, "TLS_AES_128_CCM_8_SHA256"

    const/16 v2, 0x1305

    invoke-static {v0, v1, v2}, Lokhttp3/CipherSuite$a;->a(Lokhttp3/CipherSuite$a;Ljava/lang/String;I)Lokhttp3/CipherSuite;

    move-result-object v0

    sput-object v0, Lokhttp3/CipherSuite;->r1:Lokhttp3/CipherSuite;

    return-void
.end method

.method public constructor <init>(Ljava/lang/String;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lokhttp3/CipherSuite;->a:Ljava/lang/String;

    return-void
.end method

.method public synthetic constructor <init>(Ljava/lang/String;Lkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    invoke-direct {p0, p1}, Lokhttp3/CipherSuite;-><init>(Ljava/lang/String;)V

    return-void
.end method

.method public static final synthetic a()Ljava/util/Map;
    .locals 1

    sget-object v0, Lokhttp3/CipherSuite;->c:Ljava/util/Map;

    return-object v0
.end method

.method public static final synthetic b()Ljava/util/Comparator;
    .locals 1

    sget-object v0, Lokhttp3/CipherSuite;->b:Ljava/util/Comparator;

    return-object v0
.end method


# virtual methods
.method public final c()Ljava/lang/String;
    .locals 1
    .annotation build Lkotlin/jvm/JvmName;
    .end annotation

    iget-object v0, p0, Lokhttp3/CipherSuite;->a:Ljava/lang/String;

    return-object v0
.end method

.method public toString()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lokhttp3/CipherSuite;->a:Ljava/lang/String;

    return-object v0
.end method
