.class public final synthetic Lp2/b;
.super Ljava/lang/Object;

# interfaces
.implements Landroidx/media3/exoplayer/hls/playlist/HlsPlaylistTracker$a;


# direct methods
.method public synthetic constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final a(Landroidx/media3/exoplayer/hls/f;Landroidx/media3/exoplayer/upstream/m;Lp2/f;)Landroidx/media3/exoplayer/hls/playlist/HlsPlaylistTracker;
    .locals 1

    new-instance v0, Landroidx/media3/exoplayer/hls/playlist/a;

    invoke-direct {v0, p1, p2, p3}, Landroidx/media3/exoplayer/hls/playlist/a;-><init>(Landroidx/media3/exoplayer/hls/f;Landroidx/media3/exoplayer/upstream/m;Lp2/f;)V

    return-object v0
.end method
