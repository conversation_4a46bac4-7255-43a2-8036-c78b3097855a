.class public Lp4/a;
.super Lp4/u;


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Lp4/u;-><init>()V

    invoke-virtual {p0}, Lp4/a;->z0()V

    return-void
.end method


# virtual methods
.method public final z0()V
    .locals 3

    const/4 v0, 0x1

    invoke-virtual {p0, v0}, Lp4/u;->w0(I)Lp4/u;

    new-instance v1, Lp4/c;

    const/4 v2, 0x2

    invoke-direct {v1, v2}, Lp4/c;-><init>(I)V

    invoke-virtual {p0, v1}, Lp4/u;->o0(Lp4/j;)Lp4/u;

    move-result-object v1

    new-instance v2, Lp4/b;

    invoke-direct {v2}, Lp4/b;-><init>()V

    invoke-virtual {v1, v2}, Lp4/u;->o0(Lp4/j;)Lp4/u;

    move-result-object v1

    new-instance v2, Lp4/c;

    invoke-direct {v2, v0}, Lp4/c;-><init>(I)V

    invoke-virtual {v1, v2}, Lp4/u;->o0(Lp4/j;)Lp4/u;

    return-void
.end method
