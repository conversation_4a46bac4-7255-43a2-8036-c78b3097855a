.class public Lp4/b$f;
.super Landroid/animation/AnimatorListenerAdapter;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lp4/b;->n(Landroid/view/ViewGroup;Lp4/w;Lp4/w;)Landroid/animation/Animator;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field public final synthetic a:Lp4/b$i;

.field public final synthetic b:Lp4/b;

.field private final mViewBounds:Lp4/b$i;


# direct methods
.method public constructor <init>(Lp4/b;Lp4/b$i;)V
    .locals 0

    iput-object p1, p0, Lp4/b$f;->b:Lp4/b;

    iput-object p2, p0, Lp4/b$f;->a:Lp4/b$i;

    invoke-direct {p0}, Landroid/animation/AnimatorListenerAdapter;-><init>()V

    iput-object p2, p0, Lp4/b$f;->mViewBounds:Lp4/b$i;

    return-void
.end method
