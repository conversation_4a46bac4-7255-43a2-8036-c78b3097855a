.class public Lp4/c0$a;
.super Ljava/lang/Object;


# annotations
.annotation build Landroidx/annotation/RequiresApi;
    value = 0x1d
.end annotation

.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lp4/c0;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "a"
.end annotation


# direct methods
.method public static a(Landroid/view/View;)F
    .locals 0

    invoke-static {p0}, Lp4/a0;->a(Landroid/view/View;)F

    move-result p0

    return p0
.end method

.method public static b(Landroid/view/View;F)V
    .locals 0

    invoke-static {p0, p1}, Lp4/b0;->a(Landroid/view/View;F)V

    return-void
.end method
