.class public Lp4/e$c;
.super Lp4/r;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lp4/e;->n(Ljava/lang/Object;Ljava/lang/Object;Lja<PERSON>/util/ArrayList;<PERSON><PERSON><PERSON>/lang/Object;Ljava/util/ArrayList;Ljava/lang/Object;Ljava/util/ArrayList;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field public final synthetic a:Ljava/lang/Object;

.field public final synthetic b:Ljava/util/ArrayList;

.field public final synthetic c:Ljava/lang/Object;

.field public final synthetic d:Ljava/util/ArrayList;

.field public final synthetic e:Ljava/lang/Object;

.field public final synthetic f:Ljava/util/ArrayList;

.field public final synthetic g:Lp4/e;


# direct methods
.method public constructor <init>(Lp4/e;Lja<PERSON>/lang/Object;<PERSON><PERSON><PERSON>/util/ArrayList;<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/util/ArrayList;Lja<PERSON>/lang/Object;Ljava/util/ArrayList;)V
    .locals 0

    iput-object p1, p0, Lp4/e$c;->g:Lp4/e;

    iput-object p2, p0, Lp4/e$c;->a:Ljava/lang/Object;

    iput-object p3, p0, Lp4/e$c;->b:Ljava/util/ArrayList;

    iput-object p4, p0, Lp4/e$c;->c:Ljava/lang/Object;

    iput-object p5, p0, Lp4/e$c;->d:Ljava/util/ArrayList;

    iput-object p6, p0, Lp4/e$c;->e:Ljava/lang/Object;

    iput-object p7, p0, Lp4/e$c;->f:Ljava/util/ArrayList;

    invoke-direct {p0}, Lp4/r;-><init>()V

    return-void
.end method


# virtual methods
.method public a(Lp4/j;)V
    .locals 3
    .param p1    # Lp4/j;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param

    iget-object p1, p0, Lp4/e$c;->a:Ljava/lang/Object;

    const/4 v0, 0x0

    if-eqz p1, :cond_0

    iget-object v1, p0, Lp4/e$c;->g:Lp4/e;

    iget-object v2, p0, Lp4/e$c;->b:Ljava/util/ArrayList;

    invoke-virtual {v1, p1, v2, v0}, Lp4/e;->y(Ljava/lang/Object;Ljava/util/ArrayList;Ljava/util/ArrayList;)V

    :cond_0
    iget-object p1, p0, Lp4/e$c;->c:Ljava/lang/Object;

    if-eqz p1, :cond_1

    iget-object v1, p0, Lp4/e$c;->g:Lp4/e;

    iget-object v2, p0, Lp4/e$c;->d:Ljava/util/ArrayList;

    invoke-virtual {v1, p1, v2, v0}, Lp4/e;->y(Ljava/lang/Object;Ljava/util/ArrayList;Ljava/util/ArrayList;)V

    :cond_1
    iget-object p1, p0, Lp4/e$c;->e:Ljava/lang/Object;

    if-eqz p1, :cond_2

    iget-object v1, p0, Lp4/e$c;->g:Lp4/e;

    iget-object v2, p0, Lp4/e$c;->f:Ljava/util/ArrayList;

    invoke-virtual {v1, p1, v2, v0}, Lp4/e;->y(Ljava/lang/Object;Ljava/util/ArrayList;Ljava/util/ArrayList;)V

    :cond_2
    return-void
.end method

.method public d(Lp4/j;)V
    .locals 0
    .param p1    # Lp4/j;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param

    invoke-virtual {p1, p0}, Lp4/j;->V(Lp4/j$f;)Lp4/j;

    return-void
.end method
