.class public Lp4/e$d;
.super Ljava/lang/Object;

# interfaces
.implements Lp4/j$f;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lp4/e;->z(Landroidx/fragment/app/Fragment;Ljava/lang/Object;Landroidx/core/os/c;Ljava/lang/Runnable;Ljava/lang/Runnable;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field public final synthetic a:Ljava/lang/Runnable;

.field public final synthetic b:Lp4/e;


# direct methods
.method public constructor <init>(Lp4/e;Ljava/lang/Runnable;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation

    iput-object p1, p0, Lp4/e$d;->b:Lp4/e;

    iput-object p2, p0, Lp4/e$d;->a:Ljava/lang/Runnable;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public a(Lp4/j;)V
    .locals 0
    .param p1    # Lp4/j;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param

    return-void
.end method

.method public b(Lp4/j;)V
    .locals 0
    .param p1    # Lp4/j;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param

    return-void
.end method

.method public c(Lp4/j;)V
    .locals 0
    .param p1    # Lp4/j;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param

    return-void
.end method

.method public d(Lp4/j;)V
    .locals 0
    .param p1    # Lp4/j;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param

    iget-object p1, p0, Lp4/e$d;->a:Ljava/lang/Runnable;

    invoke-interface {p1}, Ljava/lang/Runnable;->run()V

    return-void
.end method

.method public synthetic e(Lp4/j;Z)V
    .locals 0

    invoke-static {p0, p1, p2}, Lp4/k;->b(Lp4/j$f;Lp4/j;Z)V

    return-void
.end method

.method public f(Lp4/j;)V
    .locals 0
    .param p1    # Lp4/j;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param

    return-void
.end method

.method public synthetic g(Lp4/j;Z)V
    .locals 0

    invoke-static {p0, p1, p2}, Lp4/k;->a(Lp4/j$f;Lp4/j;Z)V

    return-void
.end method
