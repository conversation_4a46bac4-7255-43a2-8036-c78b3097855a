.class public Lp4/e$e;
.super Lp4/j$e;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lp4/e;->o(Ljava/lang/Object;Landroid/graphics/Rect;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field public final synthetic a:Landroid/graphics/Rect;

.field public final synthetic b:Lp4/e;


# direct methods
.method public constructor <init>(Lp4/e;Landroid/graphics/Rect;)V
    .locals 0

    iput-object p1, p0, Lp4/e$e;->b:Lp4/e;

    iput-object p2, p0, Lp4/e$e;->a:Landroid/graphics/Rect;

    invoke-direct {p0}, Lp4/j$e;-><init>()V

    return-void
.end method
