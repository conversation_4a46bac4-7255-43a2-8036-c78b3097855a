.class public Lp4/f0$a;
.super Ljava/lang/Object;


# annotations
.annotation build Landroidx/annotation/RequiresApi;
    value = 0x1d
.end annotation

.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lp4/f0;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "a"
.end annotation


# direct methods
.method public static a(Landroid/view/View;Landroid/graphics/Matrix;)V
    .locals 0

    invoke-static {p0, p1}, Lp4/e0;->a(Landroid/view/View;Landroid/graphics/Matrix;)V

    return-void
.end method

.method public static b(Landroid/view/View;Landroid/graphics/Matrix;)V
    .locals 0

    invoke-static {p0, p1}, Landroidx/compose/ui/platform/u0;->a(Landroid/view/View;Landroid/graphics/Matrix;)V

    return-void
.end method

.method public static c(Landroid/view/View;Landroid/graphics/Matrix;)V
    .locals 0

    invoke-static {p0, p1}, Lp4/d0;->a(Landroid/view/View;Landroid/graphics/Matrix;)V

    return-void
.end method
