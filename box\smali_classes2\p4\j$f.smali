.class public interface abstract Lp4/j$f;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lp4/j;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "f"
.end annotation


# virtual methods
.method public abstract a(Lp4/j;)V
    .param p1    # Lp4/j;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
.end method

.method public abstract b(Lp4/j;)V
    .param p1    # Lp4/j;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
.end method

.method public abstract c(Lp4/j;)V
    .param p1    # Lp4/j;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
.end method

.method public abstract d(Lp4/j;)V
    .param p1    # Lp4/j;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
.end method

.method public abstract e(Lp4/j;Z)V
    .param p1    # Lp4/j;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
.end method

.method public abstract f(Lp4/j;)V
    .param p1    # Lp4/j;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
.end method

.method public abstract g(Lp4/j;Z)V
    .param p1    # Lp4/j;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
.end method
