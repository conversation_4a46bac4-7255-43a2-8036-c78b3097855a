.class public interface abstract Lp4/j$g;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lp4/j;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "g"
.end annotation


# static fields
.field public static final a:Lp4/j$g;

.field public static final b:Lp4/j$g;

.field public static final c:Lp4/j$g;

.field public static final d:Lp4/j$g;

.field public static final e:Lp4/j$g;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    new-instance v0, Lp4/l;

    invoke-direct {v0}, Lp4/l;-><init>()V

    sput-object v0, Lp4/j$g;->a:Lp4/j$g;

    new-instance v0, Lp4/m;

    invoke-direct {v0}, Lp4/m;-><init>()V

    sput-object v0, Lp4/j$g;->b:Lp4/j$g;

    new-instance v0, Lp4/n;

    invoke-direct {v0}, Lp4/n;-><init>()V

    sput-object v0, Lp4/j$g;->c:Lp4/j$g;

    new-instance v0, Lp4/o;

    invoke-direct {v0}, Lp4/o;-><init>()V

    sput-object v0, Lp4/j$g;->d:Lp4/j$g;

    new-instance v0, Lp4/p;

    invoke-direct {v0}, Lp4/p;-><init>()V

    sput-object v0, Lp4/j$g;->e:Lp4/j$g;

    return-void
.end method


# virtual methods
.method public abstract a(Lp4/j$f;Lp4/j;Z)V
    .param p1    # Lp4/j$f;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p2    # Lp4/j;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
.end method
