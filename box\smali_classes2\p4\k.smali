.class public final synthetic Lp4/k;
.super Ljava/lang/Object;


# direct methods
.method public static a(Lp4/j$f;Lp4/j;Z)V
    .locals 0
    .param p1    # Lp4/j;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param

    invoke-interface {p0, p1}, Lp4/j$f;->d(Lp4/j;)V

    return-void
.end method

.method public static b(Lp4/j$f;Lp4/j;Z)V
    .locals 0
    .param p1    # Lp4/j;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param

    invoke-interface {p0, p1}, Lp4/j$f;->a(Lp4/j;)V

    return-void
.end method
