.class public final synthetic Lp4/n;
.super Ljava/lang/Object;

# interfaces
.implements Lp4/j$g;


# direct methods
.method public synthetic constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final a(Lp4/j$f;Lp4/j;Z)V
    .locals 0

    invoke-static {p1, p2, p3}, Lp4/q;->a(Lp4/j$f;Lp4/j;Z)V

    return-void
.end method
