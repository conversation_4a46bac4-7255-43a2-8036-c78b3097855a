.class public final synthetic Lp4/q;
.super Ljava/lang/Object;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    sget-object v0, Lp4/j$g;->a:Lp4/j$g;

    return-void
.end method

.method public static synthetic a(Lp4/j$f;Lp4/j;Z)V
    .locals 0

    invoke-interface {p0, p1}, Lp4/j$f;->c(Lp4/j;)V

    return-void
.end method

.method public static synthetic b(Lp4/j$f;Lp4/j;Z)V
    .locals 0

    invoke-interface {p0, p1}, Lp4/j$f;->b(Lp4/j;)V

    return-void
.end method

.method public static synthetic c(Lp4/j$f;Lp4/j;Z)V
    .locals 0

    invoke-interface {p0, p1}, Lp4/j$f;->f(Lp4/j;)V

    return-void
.end method
