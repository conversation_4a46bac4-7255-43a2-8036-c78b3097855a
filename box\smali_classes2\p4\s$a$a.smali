.class public Lp4/s$a$a;
.super Lp4/r;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lp4/s$a;->onPreDraw()Z
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field public final synthetic a:Landroidx/collection/a;

.field public final synthetic b:Lp4/s$a;


# direct methods
.method public constructor <init>(Lp4/s$a;Landroidx/collection/a;)V
    .locals 0

    iput-object p1, p0, Lp4/s$a$a;->b:Lp4/s$a;

    iput-object p2, p0, Lp4/s$a$a;->a:Landroidx/collection/a;

    invoke-direct {p0}, Lp4/r;-><init>()V

    return-void
.end method


# virtual methods
.method public d(Lp4/j;)V
    .locals 2
    .param p1    # Lp4/j;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param

    iget-object v0, p0, Lp4/s$a$a;->a:Landroidx/collection/a;

    iget-object v1, p0, Lp4/s$a$a;->b:Lp4/s$a;

    iget-object v1, v1, Lp4/s$a;->b:Landroid/view/ViewGroup;

    invoke-virtual {v0, v1}, Landroidx/collection/a;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/util/ArrayList;

    invoke-virtual {v0, p1}, Ljava/util/ArrayList;->remove(Ljava/lang/Object;)Z

    invoke-virtual {p1, p0}, Lp4/j;->V(Lp4/j$f;)Lp4/j;

    return-void
.end method
