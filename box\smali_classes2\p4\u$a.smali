.class public Lp4/u$a;
.super Lp4/r;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lp4/u;->a0()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field public final synthetic a:Lp4/j;

.field public final synthetic b:Lp4/u;


# direct methods
.method public constructor <init>(Lp4/u;Lp4/j;)V
    .locals 0

    iput-object p1, p0, Lp4/u$a;->b:Lp4/u;

    iput-object p2, p0, Lp4/u$a;->a:Lp4/j;

    invoke-direct {p0}, Lp4/r;-><init>()V

    return-void
.end method


# virtual methods
.method public d(Lp4/j;)V
    .locals 1
    .param p1    # Lp4/j;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param

    iget-object v0, p0, Lp4/u$a;->a:Lp4/j;

    invoke-virtual {v0}, Lp4/j;->a0()V

    invoke-virtual {p1, p0}, Lp4/j;->V(Lp4/j$f;)Lp4/j;

    return-void
.end method
