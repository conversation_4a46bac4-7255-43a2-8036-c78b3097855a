.class public Lp4/u$b;
.super Lp4/r;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lp4/u;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "b"
.end annotation


# instance fields
.field public a:Lp4/u;


# direct methods
.method public constructor <init>(Lp4/u;)V
    .locals 0

    invoke-direct {p0}, Lp4/r;-><init>()V

    iput-object p1, p0, Lp4/u$b;->a:Lp4/u;

    return-void
.end method


# virtual methods
.method public a(Lp4/j;)V
    .locals 1
    .param p1    # Lp4/j;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param

    iget-object p1, p0, Lp4/u$b;->a:Lp4/u;

    iget-boolean v0, p1, Lp4/u;->P:Z

    if-nez v0, :cond_0

    invoke-virtual {p1}, Lp4/j;->k0()V

    iget-object p1, p0, Lp4/u$b;->a:Lp4/u;

    const/4 v0, 0x1

    iput-boolean v0, p1, Lp4/u;->P:Z

    :cond_0
    return-void
.end method

.method public d(Lp4/j;)V
    .locals 2
    .param p1    # Lp4/j;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param

    iget-object v0, p0, Lp4/u$b;->a:Lp4/u;

    iget v1, v0, Lp4/u;->O:I

    add-int/lit8 v1, v1, -0x1

    iput v1, v0, Lp4/u;->O:I

    if-nez v1, :cond_0

    const/4 v1, 0x0

    iput-boolean v1, v0, Lp4/u;->P:Z

    invoke-virtual {v0}, Lp4/j;->p()V

    :cond_0
    invoke-virtual {p1, p0}, Lp4/j;->V(Lp4/j$f;)Lp4/j;

    return-void
.end method
