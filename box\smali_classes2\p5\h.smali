.class public interface abstract Lp5/h;
.super Ljava/lang/Object;


# virtual methods
.method public abstract a(ILp5/f;)V
.end method

.method public abstract b(Lp5/f;)V
.end method

.method public abstract c(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;)V
.end method

.method public abstract d(II)V
.end method

.method public abstract e(Ljava/lang/Object;)V
.end method

.method public abstract f(II)V
.end method

.method public abstract g(ILjava/lang/String;)V
.end method

.method public abstract h(II)V
.end method

.method public abstract i(I)V
.end method

.method public abstract j(II)V
.end method

.method public abstract k(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;)V
.end method

.method public abstract l()V
.end method
