.class public Lp/k;
.super Ljava/lang/Object;


# instance fields
.field public final a:Lf/a;


# direct methods
.method public constructor <init>(Lf/a;)V
    .locals 0
    .param p1    # Lf/a;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lp/k;->a:Lf/a;

    return-void
.end method

.method public static a(Landroid/os/IBinder;)Lp/k;
    .locals 1
    .param p0    # Landroid/os/IBinder;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .annotation build Landroidx/annotation/Nullable;
    .end annotation

    const/4 v0, 0x0

    if-nez p0, :cond_0

    move-object p0, v0

    goto :goto_0

    :cond_0
    invoke-static {p0}, Lf/a$a;->u(Landroid/os/IBinder;)Lf/a;

    move-result-object p0

    :goto_0
    if-nez p0, :cond_1

    return-object v0

    :cond_1
    new-instance v0, Lp/k;

    invoke-direct {v0, p0}, Lp/k;-><init>(Lf/a;)V

    return-object v0
.end method
