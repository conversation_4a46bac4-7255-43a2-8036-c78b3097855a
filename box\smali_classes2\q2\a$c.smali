.class public final Lq2/a$c;
.super Ljava/lang/Object;

# interfaces
.implements Lq2/c$a;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lq2/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "c"
.end annotation


# instance fields
.field public final b:Lq2/a$b;


# direct methods
.method public constructor <init>()V
    .locals 1

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    new-instance v0, Lq2/b;

    invoke-direct {v0}, Lq2/b;-><init>()V

    iput-object v0, p0, Lq2/a$c;->b:Lq2/a$b;

    return-void
.end method

.method public static synthetic c([BI)Landroid/graphics/Bitmap;
    .locals 0

    invoke-static {p0, p1}, Lq2/a$c;->e([BI)Landroid/graphics/Bitmap;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic e([BI)Landroid/graphics/Bitmap;
    .locals 0
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/exoplayer/image/ImageDecoderException;
        }
    .end annotation

    invoke-static {p0, p1}, Lq2/a;->t([BI)Landroid/graphics/Bitmap;

    move-result-object p0

    return-object p0
.end method


# virtual methods
.method public a(Landroidx/media3/common/y;)I
    .locals 1

    iget-object v0, p1, Landroidx/media3/common/y;->m:Ljava/lang/String;

    if-eqz v0, :cond_2

    invoke-static {v0}, Landroidx/media3/common/f0;->p(Ljava/lang/String;)Z

    move-result v0

    if-nez v0, :cond_0

    goto :goto_1

    :cond_0
    iget-object p1, p1, Landroidx/media3/common/y;->m:Ljava/lang/String;

    invoke-static {p1}, Le2/u0;->G0(Ljava/lang/String;)Z

    move-result p1

    if-eqz p1, :cond_1

    const/4 p1, 0x4

    invoke-static {p1}, Landroidx/media3/exoplayer/x2;->a(I)I

    move-result p1

    goto :goto_0

    :cond_1
    const/4 p1, 0x1

    invoke-static {p1}, Landroidx/media3/exoplayer/x2;->a(I)I

    move-result p1

    :goto_0
    return p1

    :cond_2
    :goto_1
    const/4 p1, 0x0

    invoke-static {p1}, Landroidx/media3/exoplayer/x2;->a(I)I

    move-result p1

    return p1
.end method

.method public bridge synthetic b()Lq2/c;
    .locals 1

    invoke-virtual {p0}, Lq2/a$c;->d()Lq2/a;

    move-result-object v0

    return-object v0
.end method

.method public d()Lq2/a;
    .locals 3

    new-instance v0, Lq2/a;

    iget-object v1, p0, Lq2/a$c;->b:Lq2/a$b;

    const/4 v2, 0x0

    invoke-direct {v0, v1, v2}, Lq2/a;-><init>(Lq2/a$b;Lq2/a$a;)V

    return-object v0
.end method
