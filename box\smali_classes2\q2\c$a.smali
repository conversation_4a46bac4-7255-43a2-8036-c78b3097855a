.class public interface abstract Lq2/c$a;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lq2/c;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "a"
.end annotation


# static fields
.field public static final a:Lq2/c$a;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    new-instance v0, Lq2/a$c;

    invoke-direct {v0}, Lq2/a$c;-><init>()V

    sput-object v0, Lq2/c$a;->a:Lq2/c$a;

    return-void
.end method


# virtual methods
.method public abstract a(Landroidx/media3/common/y;)I
.end method

.method public abstract b()Lq2/c;
.end method
