.class public interface abstract Lq2/d;
.super Ljava/lang/Object;


# static fields
.field public static final a:Lq2/d;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    new-instance v0, Lq2/d$a;

    invoke-direct {v0}, Lq2/d$a;-><init>()V

    sput-object v0, Lq2/d;->a:Lq2/d;

    return-void
.end method


# virtual methods
.method public abstract a()V
.end method

.method public abstract b(JLandroid/graphics/Bitmap;)V
.end method
