.class public Lq2/f;
.super Landroidx/media3/exoplayer/m;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lq2/f$a;,
        Lq2/f$b;
    }
.end annotation


# instance fields
.field public A:I

.field public B:Landroidx/media3/common/y;

.field public C:Lq2/c;

.field public D:Landroidx/media3/decoder/DecoderInputBuffer;

.field public E:Lq2/d;

.field public F:Landroid/graphics/Bitmap;

.field public G:Z

.field public H:Lq2/f$b;

.field public I:Lq2/f$b;

.field public J:I

.field public final r:Lq2/c$a;

.field public final s:Landroidx/media3/decoder/DecoderInputBuffer;

.field public final t:Ljava/util/ArrayDeque;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/ArrayDeque<",
            "Lq2/f$a;",
            ">;"
        }
    .end annotation
.end field

.field public u:Z

.field public v:Z

.field public w:Lq2/f$a;

.field public x:J

.field public y:J

.field public z:I


# direct methods
.method public constructor <init>(Lq2/c$a;Lq2/d;)V
    .locals 1

    const/4 v0, 0x4

    invoke-direct {p0, v0}, Landroidx/media3/exoplayer/m;-><init>(I)V

    iput-object p1, p0, Lq2/f;->r:Lq2/c$a;

    invoke-static {p2}, Lq2/f;->R(Lq2/d;)Lq2/d;

    move-result-object p1

    iput-object p1, p0, Lq2/f;->E:Lq2/d;

    invoke-static {}, Landroidx/media3/decoder/DecoderInputBuffer;->g()Landroidx/media3/decoder/DecoderInputBuffer;

    move-result-object p1

    iput-object p1, p0, Lq2/f;->s:Landroidx/media3/decoder/DecoderInputBuffer;

    sget-object p1, Lq2/f$a;->c:Lq2/f$a;

    iput-object p1, p0, Lq2/f;->w:Lq2/f$a;

    new-instance p1, Ljava/util/ArrayDeque;

    invoke-direct {p1}, Ljava/util/ArrayDeque;-><init>()V

    iput-object p1, p0, Lq2/f;->t:Ljava/util/ArrayDeque;

    const-wide p1, -0x7fffffffffffffffL    # -4.9E-324

    iput-wide p1, p0, Lq2/f;->y:J

    iput-wide p1, p0, Lq2/f;->x:J

    const/4 p1, 0x0

    iput p1, p0, Lq2/f;->z:I

    const/4 p1, 0x1

    iput p1, p0, Lq2/f;->A:I

    return-void
.end method

.method public static R(Lq2/d;)Lq2/d;
    .locals 0

    if-nez p0, :cond_0

    sget-object p0, Lq2/d;->a:Lq2/d;

    :cond_0
    return-object p0
.end method

.method private W(J)V
    .locals 3

    iput-wide p1, p0, Lq2/f;->x:J

    :goto_0
    iget-object v0, p0, Lq2/f;->t:Ljava/util/ArrayDeque;

    invoke-virtual {v0}, Ljava/util/ArrayDeque;->isEmpty()Z

    move-result v0

    if-nez v0, :cond_0

    iget-object v0, p0, Lq2/f;->t:Ljava/util/ArrayDeque;

    invoke-virtual {v0}, Ljava/util/ArrayDeque;->peek()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lq2/f$a;

    iget-wide v0, v0, Lq2/f$a;->a:J

    cmp-long v2, p1, v0

    if-ltz v2, :cond_0

    iget-object v0, p0, Lq2/f;->t:Ljava/util/ArrayDeque;

    invoke-virtual {v0}, Ljava/util/ArrayDeque;->removeFirst()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lq2/f$a;

    iput-object v0, p0, Lq2/f;->w:Lq2/f$a;

    goto :goto_0

    :cond_0
    return-void
.end method


# virtual methods
.method public A(ZZ)V
    .locals 0

    iput p2, p0, Lq2/f;->A:I

    return-void
.end method

.method public C(JZ)V
    .locals 0
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/exoplayer/ExoPlaybackException;
        }
    .end annotation

    const/4 p1, 0x1

    invoke-virtual {p0, p1}, Lq2/f;->U(I)V

    const/4 p1, 0x0

    iput-boolean p1, p0, Lq2/f;->v:Z

    iput-boolean p1, p0, Lq2/f;->u:Z

    const/4 p2, 0x0

    iput-object p2, p0, Lq2/f;->F:Landroid/graphics/Bitmap;

    iput-object p2, p0, Lq2/f;->H:Lq2/f$b;

    iput-object p2, p0, Lq2/f;->I:Lq2/f$b;

    iput-boolean p1, p0, Lq2/f;->G:Z

    iput-object p2, p0, Lq2/f;->D:Landroidx/media3/decoder/DecoderInputBuffer;

    iget-object p1, p0, Lq2/f;->C:Lq2/c;

    if-eqz p1, :cond_0

    invoke-interface {p1}, Landroidx/media3/decoder/g;->flush()V

    :cond_0
    iget-object p1, p0, Lq2/f;->t:Ljava/util/ArrayDeque;

    invoke-virtual {p1}, Ljava/util/ArrayDeque;->clear()V

    return-void
.end method

.method public D()V
    .locals 0

    invoke-virtual {p0}, Lq2/f;->Y()V

    return-void
.end method

.method public F()V
    .locals 1

    invoke-virtual {p0}, Lq2/f;->Y()V

    const/4 v0, 0x1

    invoke-virtual {p0, v0}, Lq2/f;->U(I)V

    return-void
.end method

.method public I([Landroidx/media3/common/y;JJLandroidx/media3/exoplayer/source/l$b;)V
    .locals 4
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/exoplayer/ExoPlaybackException;
        }
    .end annotation

    invoke-super/range {p0 .. p6}, Landroidx/media3/exoplayer/m;->I([Landroidx/media3/common/y;JJLandroidx/media3/exoplayer/source/l$b;)V

    iget-object p1, p0, Lq2/f;->w:Lq2/f$a;

    iget-wide p1, p1, Lq2/f$a;->b:J

    const-wide v0, -0x7fffffffffffffffL    # -4.9E-324

    cmp-long p3, p1, v0

    if-eqz p3, :cond_1

    iget-object p1, p0, Lq2/f;->t:Ljava/util/ArrayDeque;

    invoke-virtual {p1}, Ljava/util/ArrayDeque;->isEmpty()Z

    move-result p1

    if-eqz p1, :cond_0

    iget-wide p1, p0, Lq2/f;->y:J

    cmp-long p3, p1, v0

    if-eqz p3, :cond_1

    iget-wide v2, p0, Lq2/f;->x:J

    cmp-long p3, v2, v0

    if-eqz p3, :cond_0

    cmp-long p3, v2, p1

    if-ltz p3, :cond_0

    goto :goto_0

    :cond_0
    iget-object p1, p0, Lq2/f;->t:Ljava/util/ArrayDeque;

    new-instance p2, Lq2/f$a;

    iget-wide v0, p0, Lq2/f;->y:J

    invoke-direct {p2, v0, v1, p4, p5}, Lq2/f$a;-><init>(JJ)V

    invoke-virtual {p1, p2}, Ljava/util/ArrayDeque;->add(Ljava/lang/Object;)Z

    goto :goto_1

    :cond_1
    :goto_0
    new-instance p1, Lq2/f$a;

    invoke-direct {p1, v0, v1, p4, p5}, Lq2/f$a;-><init>(JJ)V

    iput-object p1, p0, Lq2/f;->w:Lq2/f$a;

    :goto_1
    return-void
.end method

.method public final N(Landroidx/media3/common/y;)Z
    .locals 1

    iget-object v0, p0, Lq2/f;->r:Lq2/c$a;

    invoke-interface {v0, p1}, Lq2/c$a;->a(Landroidx/media3/common/y;)I

    move-result p1

    const/4 v0, 0x4

    invoke-static {v0}, Landroidx/media3/exoplayer/x2;->a(I)I

    move-result v0

    if-eq p1, v0, :cond_1

    const/4 v0, 0x3

    invoke-static {v0}, Landroidx/media3/exoplayer/x2;->a(I)I

    move-result v0

    if-ne p1, v0, :cond_0

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 p1, 0x1

    :goto_1
    return p1
.end method

.method public final O(I)Landroid/graphics/Bitmap;
    .locals 4

    iget-object v0, p0, Lq2/f;->F:Landroid/graphics/Bitmap;

    invoke-static {v0}, Le2/a;->i(Ljava/lang/Object;)Ljava/lang/Object;

    iget-object v0, p0, Lq2/f;->F:Landroid/graphics/Bitmap;

    invoke-virtual {v0}, Landroid/graphics/Bitmap;->getWidth()I

    move-result v0

    iget-object v1, p0, Lq2/f;->B:Landroidx/media3/common/y;

    invoke-static {v1}, Le2/a;->i(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Landroidx/media3/common/y;

    iget v1, v1, Landroidx/media3/common/y;->G:I

    div-int/2addr v0, v1

    iget-object v1, p0, Lq2/f;->F:Landroid/graphics/Bitmap;

    invoke-virtual {v1}, Landroid/graphics/Bitmap;->getHeight()I

    move-result v1

    iget-object v2, p0, Lq2/f;->B:Landroidx/media3/common/y;

    invoke-static {v2}, Le2/a;->i(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Landroidx/media3/common/y;

    iget v2, v2, Landroidx/media3/common/y;->H:I

    div-int/2addr v1, v2

    iget-object v2, p0, Lq2/f;->B:Landroidx/media3/common/y;

    iget v3, v2, Landroidx/media3/common/y;->H:I

    rem-int v3, p1, v3

    mul-int v3, v3, v0

    iget v2, v2, Landroidx/media3/common/y;->G:I

    div-int/2addr p1, v2

    mul-int p1, p1, v1

    iget-object v2, p0, Lq2/f;->F:Landroid/graphics/Bitmap;

    invoke-static {v2, v3, p1, v0, v1}, Landroid/graphics/Bitmap;->createBitmap(Landroid/graphics/Bitmap;IIII)Landroid/graphics/Bitmap;

    move-result-object p1

    return-object p1
.end method

.method public final P(JJ)Z
    .locals 13
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/exoplayer/image/ImageDecoderException;,
            Landroidx/media3/exoplayer/ExoPlaybackException;
        }
    .end annotation

    move-object v8, p0

    iget-object v0, v8, Lq2/f;->F:Landroid/graphics/Bitmap;

    const/4 v9, 0x0

    if-eqz v0, :cond_0

    iget-object v0, v8, Lq2/f;->H:Lq2/f$b;

    if-nez v0, :cond_0

    return v9

    :cond_0
    iget v0, v8, Lq2/f;->A:I

    if-nez v0, :cond_1

    invoke-virtual {p0}, Landroidx/media3/exoplayer/m;->getState()I

    move-result v0

    const/4 v1, 0x2

    if-eq v0, v1, :cond_1

    return v9

    :cond_1
    iget-object v0, v8, Lq2/f;->F:Landroid/graphics/Bitmap;

    const/4 v10, 0x3

    const/4 v11, 0x1

    if-nez v0, :cond_6

    iget-object v0, v8, Lq2/f;->C:Lq2/c;

    invoke-static {v0}, Le2/a;->i(Ljava/lang/Object;)Ljava/lang/Object;

    iget-object v0, v8, Lq2/f;->C:Lq2/c;

    invoke-interface {v0}, Lq2/c;->dequeueOutputBuffer()Lq2/e;

    move-result-object v0

    if-nez v0, :cond_2

    return v9

    :cond_2
    invoke-static {v0}, Le2/a;->i(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lq2/e;

    invoke-virtual {v1}, Landroidx/media3/decoder/a;->isEndOfStream()Z

    move-result v1

    if-eqz v1, :cond_5

    iget v1, v8, Lq2/f;->z:I

    if-ne v1, v10, :cond_3

    invoke-virtual {p0}, Lq2/f;->Y()V

    iget-object v0, v8, Lq2/f;->B:Landroidx/media3/common/y;

    invoke-static {v0}, Le2/a;->i(Ljava/lang/Object;)Ljava/lang/Object;

    invoke-virtual {p0}, Lq2/f;->S()V

    goto :goto_0

    :cond_3
    invoke-static {v0}, Le2/a;->i(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lq2/e;

    invoke-virtual {v0}, Landroidx/media3/decoder/h;->release()V

    iget-object v0, v8, Lq2/f;->t:Ljava/util/ArrayDeque;

    invoke-virtual {v0}, Ljava/util/ArrayDeque;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_4

    iput-boolean v11, v8, Lq2/f;->v:Z

    :cond_4
    :goto_0
    return v9

    :cond_5
    iget-object v1, v0, Lq2/e;->a:Landroid/graphics/Bitmap;

    const-string v2, "Non-EOS buffer came back from the decoder without bitmap."

    invoke-static {v1, v2}, Le2/a;->j(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    iget-object v1, v0, Lq2/e;->a:Landroid/graphics/Bitmap;

    iput-object v1, v8, Lq2/f;->F:Landroid/graphics/Bitmap;

    invoke-static {v0}, Le2/a;->i(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lq2/e;

    invoke-virtual {v0}, Landroidx/media3/decoder/h;->release()V

    :cond_6
    iget-boolean v0, v8, Lq2/f;->G:Z

    if-eqz v0, :cond_e

    iget-object v0, v8, Lq2/f;->F:Landroid/graphics/Bitmap;

    if-eqz v0, :cond_e

    iget-object v0, v8, Lq2/f;->H:Lq2/f$b;

    if-eqz v0, :cond_e

    iget-object v0, v8, Lq2/f;->B:Landroidx/media3/common/y;

    invoke-static {v0}, Le2/a;->i(Ljava/lang/Object;)Ljava/lang/Object;

    iget-object v0, v8, Lq2/f;->B:Landroidx/media3/common/y;

    iget v1, v0, Landroidx/media3/common/y;->G:I

    if-ne v1, v11, :cond_7

    iget v2, v0, Landroidx/media3/common/y;->H:I

    if-eq v2, v11, :cond_8

    :cond_7
    const/4 v2, -0x1

    if-eq v1, v2, :cond_8

    iget v0, v0, Landroidx/media3/common/y;->H:I

    if-eq v0, v2, :cond_8

    const/4 v12, 0x1

    goto :goto_1

    :cond_8
    const/4 v12, 0x0

    :goto_1
    iget-object v0, v8, Lq2/f;->H:Lq2/f$b;

    invoke-virtual {v0}, Lq2/f$b;->d()Z

    move-result v0

    if-nez v0, :cond_a

    iget-object v0, v8, Lq2/f;->H:Lq2/f$b;

    if-eqz v12, :cond_9

    invoke-virtual {v0}, Lq2/f$b;->c()I

    move-result v1

    invoke-virtual {p0, v1}, Lq2/f;->O(I)Landroid/graphics/Bitmap;

    move-result-object v1

    goto :goto_2

    :cond_9
    iget-object v1, v8, Lq2/f;->F:Landroid/graphics/Bitmap;

    invoke-static {v1}, Le2/a;->i(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Landroid/graphics/Bitmap;

    :goto_2
    invoke-virtual {v0, v1}, Lq2/f$b;->e(Landroid/graphics/Bitmap;)V

    :cond_a
    iget-object v0, v8, Lq2/f;->H:Lq2/f$b;

    invoke-virtual {v0}, Lq2/f$b;->b()Landroid/graphics/Bitmap;

    move-result-object v0

    invoke-static {v0}, Le2/a;->i(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    move-object v5, v0

    check-cast v5, Landroid/graphics/Bitmap;

    iget-object v0, v8, Lq2/f;->H:Lq2/f$b;

    invoke-virtual {v0}, Lq2/f$b;->a()J

    move-result-wide v6

    move-object v0, p0

    move-wide v1, p1

    move-wide/from16 v3, p3

    invoke-virtual/range {v0 .. v7}, Lq2/f;->X(JJLandroid/graphics/Bitmap;J)Z

    move-result v0

    if-nez v0, :cond_b

    return v9

    :cond_b
    iget-object v0, v8, Lq2/f;->H:Lq2/f$b;

    invoke-static {v0}, Le2/a;->i(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lq2/f$b;

    invoke-virtual {v0}, Lq2/f$b;->a()J

    move-result-wide v0

    invoke-direct {p0, v0, v1}, Lq2/f;->W(J)V

    iput v10, v8, Lq2/f;->A:I

    const/4 v0, 0x0

    if-eqz v12, :cond_c

    iget-object v1, v8, Lq2/f;->H:Lq2/f$b;

    invoke-static {v1}, Le2/a;->i(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lq2/f$b;

    invoke-virtual {v1}, Lq2/f$b;->c()I

    move-result v1

    iget-object v2, v8, Lq2/f;->B:Landroidx/media3/common/y;

    invoke-static {v2}, Le2/a;->i(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Landroidx/media3/common/y;

    iget v2, v2, Landroidx/media3/common/y;->H:I

    iget-object v3, v8, Lq2/f;->B:Landroidx/media3/common/y;

    invoke-static {v3}, Le2/a;->i(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Landroidx/media3/common/y;

    iget v3, v3, Landroidx/media3/common/y;->G:I

    mul-int v2, v2, v3

    sub-int/2addr v2, v11

    if-ne v1, v2, :cond_d

    :cond_c
    iput-object v0, v8, Lq2/f;->F:Landroid/graphics/Bitmap;

    :cond_d
    iget-object v1, v8, Lq2/f;->I:Lq2/f$b;

    iput-object v1, v8, Lq2/f;->H:Lq2/f$b;

    iput-object v0, v8, Lq2/f;->I:Lq2/f$b;

    return v11

    :cond_e
    return v9
.end method

.method public final Q(J)Z
    .locals 7
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/exoplayer/image/ImageDecoderException;
        }
    .end annotation

    iget-boolean v0, p0, Lq2/f;->G:Z

    const/4 v1, 0x0

    if-eqz v0, :cond_0

    iget-object v0, p0, Lq2/f;->H:Lq2/f$b;

    if-eqz v0, :cond_0

    return v1

    :cond_0
    invoke-virtual {p0}, Landroidx/media3/exoplayer/m;->t()Landroidx/media3/exoplayer/t1;

    move-result-object v0

    iget-object v2, p0, Lq2/f;->C:Lq2/c;

    if-eqz v2, :cond_c

    iget v3, p0, Lq2/f;->z:I

    const/4 v4, 0x3

    if-eq v3, v4, :cond_c

    iget-boolean v3, p0, Lq2/f;->u:Z

    if-eqz v3, :cond_1

    goto/16 :goto_3

    :cond_1
    iget-object v3, p0, Lq2/f;->D:Landroidx/media3/decoder/DecoderInputBuffer;

    if-nez v3, :cond_2

    invoke-interface {v2}, Landroidx/media3/decoder/g;->dequeueInputBuffer()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Landroidx/media3/decoder/DecoderInputBuffer;

    iput-object v2, p0, Lq2/f;->D:Landroidx/media3/decoder/DecoderInputBuffer;

    if-nez v2, :cond_2

    return v1

    :cond_2
    iget v2, p0, Lq2/f;->z:I

    const/4 v3, 0x2

    const/4 v5, 0x0

    if-ne v2, v3, :cond_3

    iget-object p1, p0, Lq2/f;->D:Landroidx/media3/decoder/DecoderInputBuffer;

    invoke-static {p1}, Le2/a;->i(Ljava/lang/Object;)Ljava/lang/Object;

    iget-object p1, p0, Lq2/f;->D:Landroidx/media3/decoder/DecoderInputBuffer;

    const/4 p2, 0x4

    invoke-virtual {p1, p2}, Landroidx/media3/decoder/a;->setFlags(I)V

    iget-object p1, p0, Lq2/f;->C:Lq2/c;

    invoke-static {p1}, Le2/a;->i(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lq2/c;

    iget-object p2, p0, Lq2/f;->D:Landroidx/media3/decoder/DecoderInputBuffer;

    invoke-interface {p1, p2}, Lq2/c;->b(Landroidx/media3/decoder/DecoderInputBuffer;)V

    iput-object v5, p0, Lq2/f;->D:Landroidx/media3/decoder/DecoderInputBuffer;

    iput v4, p0, Lq2/f;->z:I

    return v1

    :cond_3
    iget-object v2, p0, Lq2/f;->D:Landroidx/media3/decoder/DecoderInputBuffer;

    invoke-virtual {p0, v0, v2, v1}, Landroidx/media3/exoplayer/m;->K(Landroidx/media3/exoplayer/t1;Landroidx/media3/decoder/DecoderInputBuffer;I)I

    move-result v2

    const/4 v4, -0x5

    const/4 v6, 0x1

    if-eq v2, v4, :cond_b

    const/4 v0, -0x4

    if-eq v2, v0, :cond_5

    const/4 p1, -0x3

    if-ne v2, p1, :cond_4

    return v1

    :cond_4
    new-instance p1, Ljava/lang/IllegalStateException;

    invoke-direct {p1}, Ljava/lang/IllegalStateException;-><init>()V

    throw p1

    :cond_5
    iget-object v0, p0, Lq2/f;->D:Landroidx/media3/decoder/DecoderInputBuffer;

    invoke-virtual {v0}, Landroidx/media3/decoder/DecoderInputBuffer;->e()V

    iget-object v0, p0, Lq2/f;->D:Landroidx/media3/decoder/DecoderInputBuffer;

    iget-object v0, v0, Landroidx/media3/decoder/DecoderInputBuffer;->c:Ljava/nio/ByteBuffer;

    invoke-static {v0}, Le2/a;->i(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/nio/ByteBuffer;

    invoke-virtual {v0}, Ljava/nio/Buffer;->remaining()I

    move-result v0

    if-gtz v0, :cond_7

    iget-object v0, p0, Lq2/f;->D:Landroidx/media3/decoder/DecoderInputBuffer;

    invoke-static {v0}, Le2/a;->i(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroidx/media3/decoder/DecoderInputBuffer;

    invoke-virtual {v0}, Landroidx/media3/decoder/a;->isEndOfStream()Z

    move-result v0

    if-eqz v0, :cond_6

    goto :goto_0

    :cond_6
    const/4 v0, 0x0

    goto :goto_1

    :cond_7
    :goto_0
    const/4 v0, 0x1

    :goto_1
    if-eqz v0, :cond_8

    iget-object v2, p0, Lq2/f;->D:Landroidx/media3/decoder/DecoderInputBuffer;

    invoke-static {v2}, Le2/a;->i(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Landroidx/media3/decoder/DecoderInputBuffer;

    const/high16 v3, -0x80000000

    invoke-virtual {v2, v3}, Landroidx/media3/decoder/a;->clearFlag(I)V

    iget-object v2, p0, Lq2/f;->C:Lq2/c;

    invoke-static {v2}, Le2/a;->i(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lq2/c;

    iget-object v3, p0, Lq2/f;->D:Landroidx/media3/decoder/DecoderInputBuffer;

    invoke-static {v3}, Le2/a;->i(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Landroidx/media3/decoder/DecoderInputBuffer;

    invoke-interface {v2, v3}, Lq2/c;->b(Landroidx/media3/decoder/DecoderInputBuffer;)V

    iput v1, p0, Lq2/f;->J:I

    :cond_8
    iget-object v2, p0, Lq2/f;->D:Landroidx/media3/decoder/DecoderInputBuffer;

    invoke-static {v2}, Le2/a;->i(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Landroidx/media3/decoder/DecoderInputBuffer;

    invoke-virtual {p0, p1, p2, v2}, Lq2/f;->V(JLandroidx/media3/decoder/DecoderInputBuffer;)V

    iget-object p1, p0, Lq2/f;->D:Landroidx/media3/decoder/DecoderInputBuffer;

    invoke-static {p1}, Le2/a;->i(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Landroidx/media3/decoder/DecoderInputBuffer;

    invoke-virtual {p1}, Landroidx/media3/decoder/a;->isEndOfStream()Z

    move-result p1

    if-eqz p1, :cond_9

    iput-boolean v6, p0, Lq2/f;->u:Z

    iput-object v5, p0, Lq2/f;->D:Landroidx/media3/decoder/DecoderInputBuffer;

    return v1

    :cond_9
    iget-wide p1, p0, Lq2/f;->y:J

    iget-object v1, p0, Lq2/f;->D:Landroidx/media3/decoder/DecoderInputBuffer;

    invoke-static {v1}, Le2/a;->i(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Landroidx/media3/decoder/DecoderInputBuffer;

    iget-wide v1, v1, Landroidx/media3/decoder/DecoderInputBuffer;->e:J

    invoke-static {p1, p2, v1, v2}, Ljava/lang/Math;->max(JJ)J

    move-result-wide p1

    iput-wide p1, p0, Lq2/f;->y:J

    if-eqz v0, :cond_a

    iput-object v5, p0, Lq2/f;->D:Landroidx/media3/decoder/DecoderInputBuffer;

    goto :goto_2

    :cond_a
    iget-object p1, p0, Lq2/f;->D:Landroidx/media3/decoder/DecoderInputBuffer;

    invoke-static {p1}, Le2/a;->i(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Landroidx/media3/decoder/DecoderInputBuffer;

    invoke-virtual {p1}, Landroidx/media3/decoder/DecoderInputBuffer;->clear()V

    :goto_2
    iget-boolean p1, p0, Lq2/f;->G:Z

    xor-int/2addr p1, v6

    return p1

    :cond_b
    iget-object p1, v0, Landroidx/media3/exoplayer/t1;->b:Landroidx/media3/common/y;

    invoke-static {p1}, Le2/a;->i(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Landroidx/media3/common/y;

    iput-object p1, p0, Lq2/f;->B:Landroidx/media3/common/y;

    iput v3, p0, Lq2/f;->z:I

    return v6

    :cond_c
    :goto_3
    return v1
.end method

.method public final S()V
    .locals 3
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/exoplayer/ExoPlaybackException;
        }
    .end annotation

    iget-object v0, p0, Lq2/f;->B:Landroidx/media3/common/y;

    invoke-virtual {p0, v0}, Lq2/f;->N(Landroidx/media3/common/y;)Z

    move-result v0

    if-eqz v0, :cond_1

    iget-object v0, p0, Lq2/f;->C:Lq2/c;

    if-eqz v0, :cond_0

    invoke-interface {v0}, Landroidx/media3/decoder/g;->release()V

    :cond_0
    iget-object v0, p0, Lq2/f;->r:Lq2/c$a;

    invoke-interface {v0}, Lq2/c$a;->b()Lq2/c;

    move-result-object v0

    iput-object v0, p0, Lq2/f;->C:Lq2/c;

    return-void

    :cond_1
    new-instance v0, Landroidx/media3/exoplayer/image/ImageDecoderException;

    const-string v1, "Provided decoder factory can\'t create decoder for format."

    invoke-direct {v0, v1}, Landroidx/media3/exoplayer/image/ImageDecoderException;-><init>(Ljava/lang/String;)V

    iget-object v1, p0, Lq2/f;->B:Landroidx/media3/common/y;

    const/16 v2, 0xfa5

    invoke-virtual {p0, v0, v1, v2}, Landroidx/media3/exoplayer/m;->p(Ljava/lang/Throwable;Landroidx/media3/common/y;I)Landroidx/media3/exoplayer/ExoPlaybackException;

    move-result-object v0

    throw v0
.end method

.method public final T(Lq2/f$b;)Z
    .locals 3

    iget-object v0, p0, Lq2/f;->B:Landroidx/media3/common/y;

    invoke-static {v0}, Le2/a;->i(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroidx/media3/common/y;

    iget v0, v0, Landroidx/media3/common/y;->G:I

    const/4 v1, 0x1

    const/4 v2, -0x1

    if-eq v0, v2, :cond_1

    iget-object v0, p0, Lq2/f;->B:Landroidx/media3/common/y;

    iget v0, v0, Landroidx/media3/common/y;->H:I

    if-eq v0, v2, :cond_1

    invoke-virtual {p1}, Lq2/f$b;->c()I

    move-result p1

    iget-object v0, p0, Lq2/f;->B:Landroidx/media3/common/y;

    invoke-static {v0}, Le2/a;->i(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroidx/media3/common/y;

    iget v0, v0, Landroidx/media3/common/y;->H:I

    iget-object v2, p0, Lq2/f;->B:Landroidx/media3/common/y;

    iget v2, v2, Landroidx/media3/common/y;->G:I

    mul-int v0, v0, v2

    sub-int/2addr v0, v1

    if-ne p1, v0, :cond_0

    goto :goto_0

    :cond_0
    const/4 v1, 0x0

    :cond_1
    :goto_0
    return v1
.end method

.method public final U(I)V
    .locals 1

    iget v0, p0, Lq2/f;->A:I

    invoke-static {v0, p1}, Ljava/lang/Math;->min(II)I

    move-result p1

    iput p1, p0, Lq2/f;->A:I

    return-void
.end method

.method public final V(JLandroidx/media3/decoder/DecoderInputBuffer;)V
    .locals 8

    invoke-virtual {p3}, Landroidx/media3/decoder/a;->isEndOfStream()Z

    move-result v0

    const/4 v1, 0x1

    if-eqz v0, :cond_0

    iput-boolean v1, p0, Lq2/f;->G:Z

    return-void

    :cond_0
    new-instance v0, Lq2/f$b;

    iget v2, p0, Lq2/f;->J:I

    iget-wide v3, p3, Landroidx/media3/decoder/DecoderInputBuffer;->e:J

    invoke-direct {v0, v2, v3, v4}, Lq2/f$b;-><init>(IJ)V

    iput-object v0, p0, Lq2/f;->I:Lq2/f$b;

    iget p3, p0, Lq2/f;->J:I

    add-int/2addr p3, v1

    iput p3, p0, Lq2/f;->J:I

    iget-boolean p3, p0, Lq2/f;->G:Z

    if-nez p3, :cond_5

    invoke-virtual {v0}, Lq2/f$b;->a()J

    move-result-wide v2

    const-wide/16 v4, 0x7530

    sub-long v6, v2, v4

    const/4 p3, 0x0

    cmp-long v0, v6, p1

    if-gtz v0, :cond_1

    add-long/2addr v4, v2

    cmp-long v0, p1, v4

    if-gtz v0, :cond_1

    const/4 v0, 0x1

    goto :goto_0

    :cond_1
    const/4 v0, 0x0

    :goto_0
    iget-object v4, p0, Lq2/f;->H:Lq2/f$b;

    if-eqz v4, :cond_2

    invoke-virtual {v4}, Lq2/f$b;->a()J

    move-result-wide v4

    cmp-long v6, v4, p1

    if-gtz v6, :cond_2

    cmp-long v4, p1, v2

    if-gez v4, :cond_2

    const/4 p1, 0x1

    goto :goto_1

    :cond_2
    const/4 p1, 0x0

    :goto_1
    iget-object p2, p0, Lq2/f;->I:Lq2/f$b;

    invoke-static {p2}, Le2/a;->i(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p2

    check-cast p2, Lq2/f$b;

    invoke-virtual {p0, p2}, Lq2/f;->T(Lq2/f$b;)Z

    move-result p2

    if-nez v0, :cond_4

    if-nez p1, :cond_4

    if-eqz p2, :cond_3

    goto :goto_2

    :cond_3
    const/4 v1, 0x0

    :cond_4
    :goto_2
    iput-boolean v1, p0, Lq2/f;->G:Z

    if-eqz p1, :cond_5

    if-nez v0, :cond_5

    return-void

    :cond_5
    iget-object p1, p0, Lq2/f;->I:Lq2/f$b;

    iput-object p1, p0, Lq2/f;->H:Lq2/f$b;

    const/4 p1, 0x0

    iput-object p1, p0, Lq2/f;->I:Lq2/f$b;

    return-void
.end method

.method public X(JJLandroid/graphics/Bitmap;J)Z
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/exoplayer/ExoPlaybackException;
        }
    .end annotation

    sub-long p1, p6, p1

    invoke-virtual {p0}, Lq2/f;->a0()Z

    move-result p3

    if-nez p3, :cond_1

    const-wide/16 p3, 0x7530

    cmp-long v0, p1, p3

    if-gez v0, :cond_0

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    return p1

    :cond_1
    :goto_0
    iget-object p1, p0, Lq2/f;->E:Lq2/d;

    iget-object p2, p0, Lq2/f;->w:Lq2/f$a;

    iget-wide p2, p2, Lq2/f$a;->b:J

    sub-long/2addr p6, p2

    invoke-interface {p1, p6, p7, p5}, Lq2/d;->b(JLandroid/graphics/Bitmap;)V

    const/4 p1, 0x1

    return p1
.end method

.method public final Y()V
    .locals 3

    const/4 v0, 0x0

    iput-object v0, p0, Lq2/f;->D:Landroidx/media3/decoder/DecoderInputBuffer;

    const/4 v1, 0x0

    iput v1, p0, Lq2/f;->z:I

    const-wide v1, -0x7fffffffffffffffL    # -4.9E-324

    iput-wide v1, p0, Lq2/f;->y:J

    iget-object v1, p0, Lq2/f;->C:Lq2/c;

    if-eqz v1, :cond_0

    invoke-interface {v1}, Landroidx/media3/decoder/g;->release()V

    iput-object v0, p0, Lq2/f;->C:Lq2/c;

    :cond_0
    return-void
.end method

.method public final Z(Lq2/d;)V
    .locals 0

    invoke-static {p1}, Lq2/f;->R(Lq2/d;)Lq2/d;

    move-result-object p1

    iput-object p1, p0, Lq2/f;->E:Lq2/d;

    return-void
.end method

.method public a(Landroidx/media3/common/y;)I
    .locals 1

    iget-object v0, p0, Lq2/f;->r:Lq2/c$a;

    invoke-interface {v0, p1}, Lq2/c$a;->a(Landroidx/media3/common/y;)I

    move-result p1

    return p1
.end method

.method public final a0()Z
    .locals 4

    invoke-virtual {p0}, Landroidx/media3/exoplayer/m;->getState()I

    move-result v0

    const/4 v1, 0x2

    const/4 v2, 0x0

    const/4 v3, 0x1

    if-ne v0, v1, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    iget v1, p0, Lq2/f;->A:I

    if-eqz v1, :cond_3

    if-eq v1, v3, :cond_2

    const/4 v0, 0x3

    if-ne v1, v0, :cond_1

    return v2

    :cond_1
    new-instance v0, Ljava/lang/IllegalStateException;

    invoke-direct {v0}, Ljava/lang/IllegalStateException;-><init>()V

    throw v0

    :cond_2
    return v3

    :cond_3
    return v0
.end method

.method public getName()Ljava/lang/String;
    .locals 1

    const-string v0, "ImageRenderer"

    return-object v0
.end method

.method public handleMessage(ILjava/lang/Object;)V
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/exoplayer/ExoPlaybackException;
        }
    .end annotation

    const/16 v0, 0xf

    if-eq p1, v0, :cond_0

    invoke-super {p0, p1, p2}, Landroidx/media3/exoplayer/m;->handleMessage(ILjava/lang/Object;)V

    goto :goto_1

    :cond_0
    instance-of p1, p2, Lq2/d;

    if-eqz p1, :cond_1

    check-cast p2, Lq2/d;

    goto :goto_0

    :cond_1
    const/4 p2, 0x0

    :goto_0
    invoke-virtual {p0, p2}, Lq2/f;->Z(Lq2/d;)V

    :goto_1
    return-void
.end method

.method public isEnded()Z
    .locals 1

    iget-boolean v0, p0, Lq2/f;->v:Z

    return v0
.end method

.method public isReady()Z
    .locals 2

    iget v0, p0, Lq2/f;->A:I

    const/4 v1, 0x3

    if-eq v0, v1, :cond_1

    if-nez v0, :cond_0

    iget-boolean v0, p0, Lq2/f;->G:Z

    if-eqz v0, :cond_0

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 v0, 0x1

    :goto_1
    return v0
.end method

.method public render(JJ)V
    .locals 3
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/exoplayer/ExoPlaybackException;
        }
    .end annotation

    iget-boolean v0, p0, Lq2/f;->v:Z

    if-eqz v0, :cond_0

    return-void

    :cond_0
    iget-object v0, p0, Lq2/f;->B:Landroidx/media3/common/y;

    if-nez v0, :cond_3

    invoke-virtual {p0}, Landroidx/media3/exoplayer/m;->t()Landroidx/media3/exoplayer/t1;

    move-result-object v0

    iget-object v1, p0, Lq2/f;->s:Landroidx/media3/decoder/DecoderInputBuffer;

    invoke-virtual {v1}, Landroidx/media3/decoder/DecoderInputBuffer;->clear()V

    iget-object v1, p0, Lq2/f;->s:Landroidx/media3/decoder/DecoderInputBuffer;

    const/4 v2, 0x2

    invoke-virtual {p0, v0, v1, v2}, Landroidx/media3/exoplayer/m;->K(Landroidx/media3/exoplayer/t1;Landroidx/media3/decoder/DecoderInputBuffer;I)I

    move-result v1

    const/4 v2, -0x5

    if-ne v1, v2, :cond_1

    iget-object v0, v0, Landroidx/media3/exoplayer/t1;->b:Landroidx/media3/common/y;

    invoke-static {v0}, Le2/a;->i(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroidx/media3/common/y;

    iput-object v0, p0, Lq2/f;->B:Landroidx/media3/common/y;

    invoke-virtual {p0}, Lq2/f;->S()V

    goto :goto_0

    :cond_1
    const/4 p1, -0x4

    if-ne v1, p1, :cond_2

    iget-object p1, p0, Lq2/f;->s:Landroidx/media3/decoder/DecoderInputBuffer;

    invoke-virtual {p1}, Landroidx/media3/decoder/a;->isEndOfStream()Z

    move-result p1

    invoke-static {p1}, Le2/a;->g(Z)V

    const/4 p1, 0x1

    iput-boolean p1, p0, Lq2/f;->u:Z

    iput-boolean p1, p0, Lq2/f;->v:Z

    :cond_2
    return-void

    :cond_3
    :goto_0
    :try_start_0
    const-string v0, "drainAndFeedDecoder"

    invoke-static {v0}, Le2/j0;->a(Ljava/lang/String;)V

    :goto_1
    invoke-virtual {p0, p1, p2, p3, p4}, Lq2/f;->P(JJ)Z

    move-result v0

    if-eqz v0, :cond_4

    goto :goto_1

    :cond_4
    :goto_2
    invoke-virtual {p0, p1, p2}, Lq2/f;->Q(J)Z

    move-result p3

    if-eqz p3, :cond_5

    goto :goto_2

    :cond_5
    invoke-static {}, Le2/j0;->c()V
    :try_end_0
    .catch Landroidx/media3/exoplayer/image/ImageDecoderException; {:try_start_0 .. :try_end_0} :catch_0

    return-void

    :catch_0
    move-exception p1

    const/4 p2, 0x0

    const/16 p3, 0xfa3

    invoke-virtual {p0, p1, p2, p3}, Landroidx/media3/exoplayer/m;->p(Ljava/lang/Throwable;Landroidx/media3/common/y;I)Landroidx/media3/exoplayer/ExoPlaybackException;

    move-result-object p1

    throw p1
.end method

.method public z()V
    .locals 1

    const/4 v0, 0x0

    iput-object v0, p0, Lq2/f;->B:Landroidx/media3/common/y;

    sget-object v0, Lq2/f$a;->c:Lq2/f$a;

    iput-object v0, p0, Lq2/f;->w:Lq2/f$a;

    iget-object v0, p0, Lq2/f;->t:Ljava/util/ArrayDeque;

    invoke-virtual {v0}, Ljava/util/ArrayDeque;->clear()V

    invoke-virtual {p0}, Lq2/f;->Y()V

    iget-object v0, p0, Lq2/f;->E:Lq2/d;

    invoke-interface {v0}, Lq2/d;->a()V

    return-void
.end method
