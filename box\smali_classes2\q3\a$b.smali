.class public final Lq3/a$b;
.super Lq3/a;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lq3/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "b"
.end annotation


# instance fields
.field public final b:Le2/c0;


# direct methods
.method public constructor <init>(ILe2/c0;)V
    .locals 0

    invoke-direct {p0, p1}, Lq3/a;-><init>(I)V

    iput-object p2, p0, Lq3/a$b;->b:Le2/c0;

    return-void
.end method
