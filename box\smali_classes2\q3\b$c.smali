.class public interface abstract Lq3/b$c;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lq3/b;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "c"
.end annotation


# virtual methods
.method public abstract a()I
.end method

.method public abstract getSampleCount()I
.end method

.method public abstract readNextSampleSize()I
.end method
