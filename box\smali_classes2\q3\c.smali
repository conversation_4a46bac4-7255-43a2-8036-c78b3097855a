.class public final Lq3/c;
.super Ljava/lang/Object;


# instance fields
.field public final a:I

.field public final b:I

.field public final c:I

.field public final d:I


# direct methods
.method public constructor <init>(IIII)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput p1, p0, Lq3/c;->a:I

    iput p2, p0, Lq3/c;->b:I

    iput p3, p0, Lq3/c;->c:I

    iput p4, p0, Lq3/c;->d:I

    return-void
.end method
