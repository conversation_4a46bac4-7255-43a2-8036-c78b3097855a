.class public final synthetic Lq3/f;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/google/common/base/f;


# instance fields
.field public final synthetic a:Lq3/g;


# direct methods
.method public synthetic constructor <init>(Lq3/g;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lq3/f;->a:Lq3/g;

    return-void
.end method


# virtual methods
.method public final apply(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    iget-object v0, p0, Lq3/f;->a:Lq3/g;

    check-cast p1, Lq3/p;

    invoke-virtual {v0, p1}, Lq3/g;->n(Lq3/p;)Lq3/p;

    move-result-object p1

    return-object p1
.end method
