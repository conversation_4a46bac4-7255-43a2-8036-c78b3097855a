.class public final synthetic Lq3/j;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/google/common/base/f;


# direct methods
.method public synthetic constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final apply(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    check-cast p1, Lq3/p;

    invoke-static {p1}, Lq3/k;->h(Lq3/p;)Lq3/p;

    move-result-object p1

    return-object p1
.end method
