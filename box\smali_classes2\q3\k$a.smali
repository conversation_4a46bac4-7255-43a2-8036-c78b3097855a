.class public final Lq3/k$a;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lq3/k;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation


# instance fields
.field public final a:Lq3/p;

.field public final b:Lq3/s;

.field public final c:Lz2/r0;

.field public final d:Lz2/s0;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public e:I


# direct methods
.method public constructor <init>(Lq3/p;Lq3/s;Lz2/r0;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lq3/k$a;->a:Lq3/p;

    iput-object p2, p0, Lq3/k$a;->b:Lq3/s;

    iput-object p3, p0, Lq3/k$a;->c:Lz2/r0;

    iget-object p1, p1, Lq3/p;->f:Landroidx/media3/common/y;

    iget-object p1, p1, Landroidx/media3/common/y;->m:Ljava/lang/String;

    const-string p2, "audio/true-hd"

    invoke-virtual {p2, p1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_0

    new-instance p1, Lz2/s0;

    invoke-direct {p1}, Lz2/s0;-><init>()V

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    :goto_0
    iput-object p1, p0, Lq3/k$a;->d:Lz2/s0;

    return-void
.end method
