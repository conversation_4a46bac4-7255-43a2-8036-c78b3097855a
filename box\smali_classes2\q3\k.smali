.class public final Lq3/k;
.super Ljava/lang/Object;

# interfaces
.implements Lz2/s;
.implements Lz2/m0;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lq3/k$a;
    }
.end annotation


# static fields
.field public static final A:Lz2/y;
    .annotation runtime Ljava/lang/Deprecated;
    .end annotation
.end field


# instance fields
.field public final a:Lt3/s$a;

.field public final b:I

.field public final c:Le2/c0;

.field public final d:Le2/c0;

.field public final e:Le2/c0;

.field public final f:Le2/c0;

.field public final g:Ljava/util/ArrayDeque;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/ArrayDeque<",
            "Lq3/a$a;",
            ">;"
        }
    .end annotation
.end field

.field public final h:Lq3/m;

.field public final i:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Landroidx/media3/common/Metadata$Entry;",
            ">;"
        }
    .end annotation
.end field

.field public j:I

.field public k:I

.field public l:J

.field public m:I

.field public n:Le2/c0;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public o:I

.field public p:I

.field public q:I

.field public r:I

.field public s:Z

.field public t:Lz2/u;

.field public u:[Lq3/k$a;

.field public v:[[J

.field public w:I

.field public x:J

.field public y:I

.field public z:Landroidx/media3/extractor/metadata/mp4/MotionPhotoMetadata;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 1

    new-instance v0, Lq3/i;

    invoke-direct {v0}, Lq3/i;-><init>()V

    sput-object v0, Lq3/k;->A:Lz2/y;

    return-void
.end method

.method public constructor <init>()V
    .locals 2
    .annotation runtime Ljava/lang/Deprecated;
    .end annotation

    sget-object v0, Lt3/s$a;->a:Lt3/s$a;

    const/16 v1, 0x10

    invoke-direct {p0, v0, v1}, Lq3/k;-><init>(Lt3/s$a;I)V

    return-void
.end method

.method public constructor <init>(Lt3/s$a;I)V
    .locals 2

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lq3/k;->a:Lt3/s$a;

    iput p2, p0, Lq3/k;->b:I

    const/4 p1, 0x4

    and-int/2addr p2, p1

    const/4 v0, 0x0

    if-eqz p2, :cond_0

    const/4 p2, 0x3

    goto :goto_0

    :cond_0
    const/4 p2, 0x0

    :goto_0
    iput p2, p0, Lq3/k;->j:I

    new-instance p2, Lq3/m;

    invoke-direct {p2}, Lq3/m;-><init>()V

    iput-object p2, p0, Lq3/k;->h:Lq3/m;

    new-instance p2, Ljava/util/ArrayList;

    invoke-direct {p2}, Ljava/util/ArrayList;-><init>()V

    iput-object p2, p0, Lq3/k;->i:Ljava/util/List;

    new-instance p2, Le2/c0;

    const/16 v1, 0x10

    invoke-direct {p2, v1}, Le2/c0;-><init>(I)V

    iput-object p2, p0, Lq3/k;->f:Le2/c0;

    new-instance p2, Ljava/util/ArrayDeque;

    invoke-direct {p2}, Ljava/util/ArrayDeque;-><init>()V

    iput-object p2, p0, Lq3/k;->g:Ljava/util/ArrayDeque;

    new-instance p2, Le2/c0;

    sget-object v1, Lf2/a;->a:[B

    invoke-direct {p2, v1}, Le2/c0;-><init>([B)V

    iput-object p2, p0, Lq3/k;->c:Le2/c0;

    new-instance p2, Le2/c0;

    invoke-direct {p2, p1}, Le2/c0;-><init>(I)V

    iput-object p2, p0, Lq3/k;->d:Le2/c0;

    new-instance p1, Le2/c0;

    invoke-direct {p1}, Le2/c0;-><init>()V

    iput-object p1, p0, Lq3/k;->e:Le2/c0;

    const/4 p1, -0x1

    iput p1, p0, Lq3/k;->o:I

    sget-object p1, Lz2/u;->G0:Lz2/u;

    iput-object p1, p0, Lq3/k;->t:Lz2/u;

    new-array p1, v0, [Lq3/k$a;

    iput-object p1, p0, Lq3/k;->u:[Lq3/k$a;

    return-void
.end method

.method public static B(I)Z
    .locals 1

    const v0, 0x6d6f6f76

    if-eq p0, v0, :cond_1

    const v0, 0x7472616b

    if-eq p0, v0, :cond_1

    const v0, 0x6d646961

    if-eq p0, v0, :cond_1

    const v0, 0x6d696e66

    if-eq p0, v0, :cond_1

    const v0, 0x7374626c

    if-eq p0, v0, :cond_1

    const v0, 0x65647473

    if-eq p0, v0, :cond_1

    const v0, 0x6d657461

    if-ne p0, v0, :cond_0

    goto :goto_0

    :cond_0
    const/4 p0, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 p0, 0x1

    :goto_1
    return p0
.end method

.method public static C(I)Z
    .locals 1

    const v0, 0x6d646864

    if-eq p0, v0, :cond_1

    const v0, 0x6d766864

    if-eq p0, v0, :cond_1

    const v0, 0x68646c72    # 4.3148E24f

    if-eq p0, v0, :cond_1

    const v0, 0x73747364

    if-eq p0, v0, :cond_1

    const v0, 0x73747473

    if-eq p0, v0, :cond_1

    const v0, 0x73747373

    if-eq p0, v0, :cond_1

    const v0, 0x63747473

    if-eq p0, v0, :cond_1

    const v0, 0x656c7374

    if-eq p0, v0, :cond_1

    const v0, 0x73747363

    if-eq p0, v0, :cond_1

    const v0, 0x7374737a

    if-eq p0, v0, :cond_1

    const v0, 0x73747a32

    if-eq p0, v0, :cond_1

    const v0, 0x7374636f

    if-eq p0, v0, :cond_1

    const v0, 0x636f3634

    if-eq p0, v0, :cond_1

    const v0, 0x746b6864

    if-eq p0, v0, :cond_1

    const v0, 0x66747970

    if-eq p0, v0, :cond_1

    const v0, 0x75647461

    if-eq p0, v0, :cond_1

    const v0, 0x6b657973

    if-eq p0, v0, :cond_1

    const v0, 0x696c7374

    if-ne p0, v0, :cond_0

    goto :goto_0

    :cond_0
    const/4 p0, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 p0, 0x1

    :goto_1
    return p0
.end method

.method public static synthetic g()[Lz2/s;
    .locals 1

    invoke-static {}, Lq3/k;->p()[Lz2/s;

    move-result-object v0

    return-object v0
.end method

.method public static synthetic h(Lq3/p;)Lq3/p;
    .locals 0

    invoke-static {p0}, Lq3/k;->o(Lq3/p;)Lq3/p;

    move-result-object p0

    return-object p0
.end method

.method public static i(I)I
    .locals 1

    const v0, 0x68656963

    if-eq p0, v0, :cond_1

    const v0, 0x71742020

    if-eq p0, v0, :cond_0

    const/4 p0, 0x0

    return p0

    :cond_0
    const/4 p0, 0x1

    return p0

    :cond_1
    const/4 p0, 0x2

    return p0
.end method

.method public static j([Lq3/k$a;)[[J
    .locals 15

    array-length v0, p0

    new-array v0, v0, [[J

    array-length v1, p0

    new-array v1, v1, [I

    array-length v2, p0

    new-array v2, v2, [J

    array-length v3, p0

    new-array v3, v3, [Z

    const/4 v4, 0x0

    const/4 v5, 0x0

    :goto_0
    array-length v6, p0

    if-ge v5, v6, :cond_0

    aget-object v6, p0, v5

    iget-object v6, v6, Lq3/k$a;->b:Lq3/s;

    iget v6, v6, Lq3/s;->b:I

    new-array v6, v6, [J

    aput-object v6, v0, v5

    aget-object v6, p0, v5

    iget-object v6, v6, Lq3/k$a;->b:Lq3/s;

    iget-object v6, v6, Lq3/s;->f:[J

    aget-wide v7, v6, v4

    aput-wide v7, v2, v5

    add-int/lit8 v5, v5, 0x1

    goto :goto_0

    :cond_0
    const-wide/16 v5, 0x0

    const/4 v7, 0x0

    :goto_1
    array-length v8, p0

    if-ge v7, v8, :cond_4

    const-wide v8, 0x7fffffffffffffffL

    const/4 v10, -0x1

    const/4 v11, 0x0

    :goto_2
    array-length v12, p0

    if-ge v11, v12, :cond_2

    aget-boolean v12, v3, v11

    if-nez v12, :cond_1

    aget-wide v12, v2, v11

    cmp-long v14, v12, v8

    if-gtz v14, :cond_1

    move v10, v11

    move-wide v8, v12

    :cond_1
    add-int/lit8 v11, v11, 0x1

    goto :goto_2

    :cond_2
    aget v8, v1, v10

    aget-object v9, v0, v10

    aput-wide v5, v9, v8

    aget-object v11, p0, v10

    iget-object v11, v11, Lq3/k$a;->b:Lq3/s;

    iget-object v12, v11, Lq3/s;->d:[I

    aget v12, v12, v8

    int-to-long v12, v12

    add-long/2addr v5, v12

    const/4 v12, 0x1

    add-int/2addr v8, v12

    aput v8, v1, v10

    array-length v9, v9

    if-ge v8, v9, :cond_3

    iget-object v9, v11, Lq3/s;->f:[J

    aget-wide v8, v9, v8

    aput-wide v8, v2, v10

    goto :goto_1

    :cond_3
    aput-boolean v12, v3, v10

    add-int/lit8 v7, v7, 0x1

    goto :goto_1

    :cond_4
    return-object v0
.end method

.method public static m(Lq3/s;J)I
    .locals 2

    invoke-virtual {p0, p1, p2}, Lq3/s;->a(J)I

    move-result v0

    const/4 v1, -0x1

    if-ne v0, v1, :cond_0

    invoke-virtual {p0, p1, p2}, Lq3/s;->b(J)I

    move-result v0

    :cond_0
    return v0
.end method

.method public static synthetic o(Lq3/p;)Lq3/p;
    .locals 0

    return-object p0
.end method

.method public static synthetic p()[Lz2/s;
    .locals 4

    const/4 v0, 0x1

    new-array v0, v0, [Lz2/s;

    new-instance v1, Lq3/k;

    sget-object v2, Lt3/s$a;->a:Lt3/s$a;

    const/16 v3, 0x10

    invoke-direct {v1, v2, v3}, Lq3/k;-><init>(Lt3/s$a;I)V

    const/4 v2, 0x0

    aput-object v1, v0, v2

    return-object v0
.end method

.method public static q(Lq3/s;JJ)J
    .locals 0

    invoke-static {p0, p1, p2}, Lq3/k;->m(Lq3/s;J)I

    move-result p1

    const/4 p2, -0x1

    if-ne p1, p2, :cond_0

    return-wide p3

    :cond_0
    iget-object p0, p0, Lq3/s;->c:[J

    aget-wide p1, p0, p1

    invoke-static {p1, p2, p3, p4}, Ljava/lang/Math;->min(JJ)J

    move-result-wide p0

    return-wide p0
.end method

.method public static u(Le2/c0;)I
    .locals 1

    const/16 v0, 0x8

    invoke-virtual {p0, v0}, Le2/c0;->U(I)V

    invoke-virtual {p0}, Le2/c0;->q()I

    move-result v0

    invoke-static {v0}, Lq3/k;->i(I)I

    move-result v0

    if-eqz v0, :cond_0

    return v0

    :cond_0
    const/4 v0, 0x4

    invoke-virtual {p0, v0}, Le2/c0;->V(I)V

    :cond_1
    invoke-virtual {p0}, Le2/c0;->a()I

    move-result v0

    if-lez v0, :cond_2

    invoke-virtual {p0}, Le2/c0;->q()I

    move-result v0

    invoke-static {v0}, Lq3/k;->i(I)I

    move-result v0

    if-eqz v0, :cond_1

    return v0

    :cond_2
    const/4 p0, 0x0

    return p0
.end method


# virtual methods
.method public final A(Lz2/t;Lz2/l0;)I
    .locals 4
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget-object v0, p0, Lq3/k;->h:Lq3/m;

    iget-object v1, p0, Lq3/k;->i:Ljava/util/List;

    invoke-virtual {v0, p1, p2, v1}, Lq3/m;->c(Lz2/t;Lz2/l0;Ljava/util/List;)I

    move-result p1

    const/4 v0, 0x1

    if-ne p1, v0, :cond_0

    iget-wide v0, p2, Lz2/l0;->a:J

    const-wide/16 v2, 0x0

    cmp-long p2, v0, v2

    if-nez p2, :cond_0

    invoke-virtual {p0}, Lq3/k;->k()V

    :cond_0
    return p1
.end method

.method public final D(Lq3/k$a;J)V
    .locals 3

    iget-object v0, p1, Lq3/k$a;->b:Lq3/s;

    invoke-virtual {v0, p2, p3}, Lq3/s;->a(J)I

    move-result v1

    const/4 v2, -0x1

    if-ne v1, v2, :cond_0

    invoke-virtual {v0, p2, p3}, Lq3/s;->b(J)I

    move-result v1

    :cond_0
    iput v1, p1, Lq3/k$a;->e:I

    return-void
.end method

.method public synthetic b()Lz2/s;
    .locals 1

    invoke-static {p0}, Lz2/r;->a(Lz2/s;)Lz2/s;

    move-result-object v0

    return-object v0
.end method

.method public c(Lz2/u;)V
    .locals 2

    iget v0, p0, Lq3/k;->b:I

    and-int/lit8 v0, v0, 0x10

    if-nez v0, :cond_0

    new-instance v0, Lt3/u;

    iget-object v1, p0, Lq3/k;->a:Lt3/s$a;

    invoke-direct {v0, p1, v1}, Lt3/u;-><init>(Lz2/u;Lt3/s$a;)V

    move-object p1, v0

    :cond_0
    iput-object p1, p0, Lq3/k;->t:Lz2/u;

    return-void
.end method

.method public d(Lz2/t;Lz2/l0;)I
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    :cond_0
    iget v0, p0, Lq3/k;->j:I

    if-eqz v0, :cond_4

    const/4 v1, 0x1

    if-eq v0, v1, :cond_3

    const/4 v1, 0x2

    if-eq v0, v1, :cond_2

    const/4 v1, 0x3

    if-ne v0, v1, :cond_1

    invoke-virtual {p0, p1, p2}, Lq3/k;->A(Lz2/t;Lz2/l0;)I

    move-result p1

    return p1

    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    invoke-direct {p1}, Ljava/lang/IllegalStateException;-><init>()V

    throw p1

    :cond_2
    invoke-virtual {p0, p1, p2}, Lq3/k;->z(Lz2/t;Lz2/l0;)I

    move-result p1

    return p1

    :cond_3
    invoke-virtual {p0, p1, p2}, Lq3/k;->y(Lz2/t;Lz2/l0;)Z

    move-result v0

    if-eqz v0, :cond_0

    return v1

    :cond_4
    invoke-virtual {p0, p1}, Lq3/k;->x(Lz2/t;)Z

    move-result v0

    if-nez v0, :cond_0

    const/4 p1, -0x1

    return p1
.end method

.method public e(Lz2/t;)Z
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget v0, p0, Lq3/k;->b:I

    and-int/lit8 v0, v0, 0x2

    if-eqz v0, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    invoke-static {p1, v0}, Lq3/o;->d(Lz2/t;Z)Z

    move-result p1

    return p1
.end method

.method public getDurationUs()J
    .locals 2

    iget-wide v0, p0, Lq3/k;->x:J

    return-wide v0
.end method

.method public getSeekPoints(J)Lz2/m0$a;
    .locals 1

    const/4 v0, -0x1

    invoke-virtual {p0, p1, p2, v0}, Lq3/k;->l(JI)Lz2/m0$a;

    move-result-object p1

    return-object p1
.end method

.method public isSeekable()Z
    .locals 1

    const/4 v0, 0x1

    return v0
.end method

.method public final k()V
    .locals 1

    const/4 v0, 0x0

    iput v0, p0, Lq3/k;->j:I

    iput v0, p0, Lq3/k;->m:I

    return-void
.end method

.method public l(JI)Lz2/m0$a;
    .locals 16

    move-object/from16 v0, p0

    move-wide/from16 v1, p1

    move/from16 v3, p3

    iget-object v4, v0, Lq3/k;->u:[Lq3/k$a;

    array-length v5, v4

    if-nez v5, :cond_0

    new-instance v1, Lz2/m0$a;

    sget-object v2, Lz2/n0;->c:Lz2/n0;

    invoke-direct {v1, v2}, Lz2/m0$a;-><init>(Lz2/n0;)V

    return-object v1

    :cond_0
    const/4 v5, -0x1

    if-eq v3, v5, :cond_1

    move v6, v3

    goto :goto_0

    :cond_1
    iget v6, v0, Lq3/k;->w:I

    :goto_0
    const-wide v7, -0x7fffffffffffffffL    # -4.9E-324

    const-wide/16 v9, -0x1

    if-eq v6, v5, :cond_3

    aget-object v4, v4, v6

    iget-object v4, v4, Lq3/k$a;->b:Lq3/s;

    invoke-static {v4, v1, v2}, Lq3/k;->m(Lq3/s;J)I

    move-result v6

    if-ne v6, v5, :cond_2

    new-instance v1, Lz2/m0$a;

    sget-object v2, Lz2/n0;->c:Lz2/n0;

    invoke-direct {v1, v2}, Lz2/m0$a;-><init>(Lz2/n0;)V

    return-object v1

    :cond_2
    iget-object v11, v4, Lq3/s;->f:[J

    aget-wide v12, v11, v6

    iget-object v11, v4, Lq3/s;->c:[J

    aget-wide v14, v11, v6

    cmp-long v11, v12, v1

    if-gez v11, :cond_4

    iget v11, v4, Lq3/s;->b:I

    add-int/lit8 v11, v11, -0x1

    if-ge v6, v11, :cond_4

    invoke-virtual {v4, v1, v2}, Lq3/s;->b(J)I

    move-result v1

    if-eq v1, v5, :cond_4

    if-eq v1, v6, :cond_4

    iget-object v2, v4, Lq3/s;->f:[J

    aget-wide v9, v2, v1

    iget-object v2, v4, Lq3/s;->c:[J

    aget-wide v1, v2, v1

    goto :goto_1

    :cond_3
    const-wide v14, 0x7fffffffffffffffL

    move-wide v12, v1

    :cond_4
    move-wide v1, v9

    move-wide v9, v7

    :goto_1
    if-ne v3, v5, :cond_7

    const/4 v3, 0x0

    :goto_2
    iget-object v4, v0, Lq3/k;->u:[Lq3/k$a;

    array-length v5, v4

    if-ge v3, v5, :cond_7

    iget v5, v0, Lq3/k;->w:I

    if-eq v3, v5, :cond_6

    aget-object v4, v4, v3

    iget-object v4, v4, Lq3/k$a;->b:Lq3/s;

    invoke-static {v4, v12, v13, v14, v15}, Lq3/k;->q(Lq3/s;JJ)J

    move-result-wide v5

    cmp-long v11, v9, v7

    if-eqz v11, :cond_5

    invoke-static {v4, v9, v10, v1, v2}, Lq3/k;->q(Lq3/s;JJ)J

    move-result-wide v1

    :cond_5
    move-wide v14, v5

    :cond_6
    add-int/lit8 v3, v3, 0x1

    goto :goto_2

    :cond_7
    new-instance v3, Lz2/n0;

    invoke-direct {v3, v12, v13, v14, v15}, Lz2/n0;-><init>(JJ)V

    cmp-long v4, v9, v7

    if-nez v4, :cond_8

    new-instance v1, Lz2/m0$a;

    invoke-direct {v1, v3}, Lz2/m0$a;-><init>(Lz2/n0;)V

    return-object v1

    :cond_8
    new-instance v4, Lz2/n0;

    invoke-direct {v4, v9, v10, v1, v2}, Lz2/n0;-><init>(JJ)V

    new-instance v1, Lz2/m0$a;

    invoke-direct {v1, v3, v4}, Lz2/m0$a;-><init>(Lz2/n0;Lz2/n0;)V

    return-object v1
.end method

.method public final n(J)I
    .locals 20

    move-object/from16 v0, p0

    const/4 v4, -0x1

    const/4 v6, -0x1

    const/4 v7, 0x0

    const-wide v8, 0x7fffffffffffffffL

    const/4 v10, 0x1

    const-wide v11, 0x7fffffffffffffffL

    const/4 v13, 0x1

    const-wide v14, 0x7fffffffffffffffL

    :goto_0
    iget-object v3, v0, Lq3/k;->u:[Lq3/k$a;

    array-length v5, v3

    if-ge v7, v5, :cond_7

    aget-object v3, v3, v7

    iget v5, v3, Lq3/k$a;->e:I

    iget-object v3, v3, Lq3/k$a;->b:Lq3/s;

    iget v1, v3, Lq3/s;->b:I

    if-ne v5, v1, :cond_0

    goto :goto_3

    :cond_0
    iget-object v1, v3, Lq3/s;->c:[J

    aget-wide v2, v1, v5

    iget-object v1, v0, Lq3/k;->v:[[J

    invoke-static {v1}, Le2/u0;->i(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, [[J

    aget-object v1, v1, v7

    aget-wide v16, v1, v5

    sub-long v2, v2, p1

    const-wide/16 v18, 0x0

    cmp-long v1, v2, v18

    if-ltz v1, :cond_2

    const-wide/32 v18, 0x40000

    cmp-long v1, v2, v18

    if-ltz v1, :cond_1

    goto :goto_1

    :cond_1
    const/4 v1, 0x0

    goto :goto_2

    :cond_2
    :goto_1
    const/4 v1, 0x1

    :goto_2
    if-nez v1, :cond_3

    if-nez v13, :cond_4

    :cond_3
    if-ne v1, v13, :cond_5

    cmp-long v5, v2, v14

    if-gez v5, :cond_5

    :cond_4
    move v13, v1

    move-wide v14, v2

    move v6, v7

    move-wide/from16 v11, v16

    :cond_5
    cmp-long v2, v16, v8

    if-gez v2, :cond_6

    move v10, v1

    move v4, v7

    move-wide/from16 v8, v16

    :cond_6
    :goto_3
    add-int/lit8 v7, v7, 0x1

    goto :goto_0

    :cond_7
    const-wide v1, 0x7fffffffffffffffL

    cmp-long v3, v8, v1

    if-eqz v3, :cond_8

    if-eqz v10, :cond_8

    const-wide/32 v1, 0xa00000

    add-long/2addr v8, v1

    cmp-long v1, v11, v8

    if-gez v1, :cond_9

    :cond_8
    move v4, v6

    :cond_9
    return v4
.end method

.method public final r(Lz2/t;)V
    .locals 3
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget-object v0, p0, Lq3/k;->e:Le2/c0;

    const/16 v1, 0x8

    invoke-virtual {v0, v1}, Le2/c0;->Q(I)V

    iget-object v0, p0, Lq3/k;->e:Le2/c0;

    invoke-virtual {v0}, Le2/c0;->e()[B

    move-result-object v0

    const/4 v2, 0x0

    invoke-interface {p1, v0, v2, v1}, Lz2/t;->peekFully([BII)V

    iget-object v0, p0, Lq3/k;->e:Le2/c0;

    invoke-static {v0}, Lq3/b;->f(Le2/c0;)V

    iget-object v0, p0, Lq3/k;->e:Le2/c0;

    invoke-virtual {v0}, Le2/c0;->f()I

    move-result v0

    invoke-interface {p1, v0}, Lz2/t;->skipFully(I)V

    invoke-interface {p1}, Lz2/t;->resetPeekPosition()V

    return-void
.end method

.method public release()V
    .locals 0

    return-void
.end method

.method public final s(J)V
    .locals 4
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/common/ParserException;
        }
    .end annotation

    :cond_0
    :goto_0
    iget-object v0, p0, Lq3/k;->g:Ljava/util/ArrayDeque;

    invoke-virtual {v0}, Ljava/util/ArrayDeque;->isEmpty()Z

    move-result v0

    const/4 v1, 0x2

    if-nez v0, :cond_2

    iget-object v0, p0, Lq3/k;->g:Ljava/util/ArrayDeque;

    invoke-virtual {v0}, Ljava/util/ArrayDeque;->peek()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lq3/a$a;

    iget-wide v2, v0, Lq3/a$a;->b:J

    cmp-long v0, v2, p1

    if-nez v0, :cond_2

    iget-object v0, p0, Lq3/k;->g:Ljava/util/ArrayDeque;

    invoke-virtual {v0}, Ljava/util/ArrayDeque;->pop()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lq3/a$a;

    iget v2, v0, Lq3/a;->a:I

    const v3, 0x6d6f6f76

    if-ne v2, v3, :cond_1

    invoke-virtual {p0, v0}, Lq3/k;->v(Lq3/a$a;)V

    iget-object v0, p0, Lq3/k;->g:Ljava/util/ArrayDeque;

    invoke-virtual {v0}, Ljava/util/ArrayDeque;->clear()V

    iput v1, p0, Lq3/k;->j:I

    goto :goto_0

    :cond_1
    iget-object v1, p0, Lq3/k;->g:Ljava/util/ArrayDeque;

    invoke-virtual {v1}, Ljava/util/ArrayDeque;->isEmpty()Z

    move-result v1

    if-nez v1, :cond_0

    iget-object v1, p0, Lq3/k;->g:Ljava/util/ArrayDeque;

    invoke-virtual {v1}, Ljava/util/ArrayDeque;->peek()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lq3/a$a;

    invoke-virtual {v1, v0}, Lq3/a$a;->d(Lq3/a$a;)V

    goto :goto_0

    :cond_2
    iget p1, p0, Lq3/k;->j:I

    if-eq p1, v1, :cond_3

    invoke-virtual {p0}, Lq3/k;->k()V

    :cond_3
    return-void
.end method

.method public seek(JJ)V
    .locals 4

    iget-object v0, p0, Lq3/k;->g:Ljava/util/ArrayDeque;

    invoke-virtual {v0}, Ljava/util/ArrayDeque;->clear()V

    const/4 v0, 0x0

    iput v0, p0, Lq3/k;->m:I

    const/4 v1, -0x1

    iput v1, p0, Lq3/k;->o:I

    iput v0, p0, Lq3/k;->p:I

    iput v0, p0, Lq3/k;->q:I

    iput v0, p0, Lq3/k;->r:I

    const-wide/16 v1, 0x0

    cmp-long v3, p1, v1

    if-nez v3, :cond_1

    iget p1, p0, Lq3/k;->j:I

    const/4 p2, 0x3

    if-eq p1, p2, :cond_0

    invoke-virtual {p0}, Lq3/k;->k()V

    goto :goto_1

    :cond_0
    iget-object p1, p0, Lq3/k;->h:Lq3/m;

    invoke-virtual {p1}, Lq3/m;->g()V

    iget-object p1, p0, Lq3/k;->i:Ljava/util/List;

    invoke-interface {p1}, Ljava/util/List;->clear()V

    goto :goto_1

    :cond_1
    iget-object p1, p0, Lq3/k;->u:[Lq3/k$a;

    array-length p2, p1

    :goto_0
    if-ge v0, p2, :cond_3

    aget-object v1, p1, v0

    invoke-virtual {p0, v1, p3, p4}, Lq3/k;->D(Lq3/k$a;J)V

    iget-object v1, v1, Lq3/k$a;->d:Lz2/s0;

    if-eqz v1, :cond_2

    invoke-virtual {v1}, Lz2/s0;->b()V

    :cond_2
    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :cond_3
    :goto_1
    return-void
.end method

.method public final t()V
    .locals 5

    iget v0, p0, Lq3/k;->y:I

    const/4 v1, 0x2

    if-ne v0, v1, :cond_1

    iget v0, p0, Lq3/k;->b:I

    and-int/2addr v0, v1

    if-eqz v0, :cond_1

    iget-object v0, p0, Lq3/k;->t:Lz2/u;

    const/4 v1, 0x4

    const/4 v2, 0x0

    invoke-interface {v0, v2, v1}, Lz2/u;->track(II)Lz2/r0;

    move-result-object v0

    iget-object v1, p0, Lq3/k;->z:Landroidx/media3/extractor/metadata/mp4/MotionPhotoMetadata;

    if-nez v1, :cond_0

    const/4 v1, 0x0

    goto :goto_0

    :cond_0
    new-instance v1, Landroidx/media3/common/Metadata;

    const/4 v3, 0x1

    new-array v3, v3, [Landroidx/media3/common/Metadata$Entry;

    iget-object v4, p0, Lq3/k;->z:Landroidx/media3/extractor/metadata/mp4/MotionPhotoMetadata;

    aput-object v4, v3, v2

    invoke-direct {v1, v3}, Landroidx/media3/common/Metadata;-><init>([Landroidx/media3/common/Metadata$Entry;)V

    :goto_0
    new-instance v2, Landroidx/media3/common/y$b;

    invoke-direct {v2}, Landroidx/media3/common/y$b;-><init>()V

    invoke-virtual {v2, v1}, Landroidx/media3/common/y$b;->d0(Landroidx/media3/common/Metadata;)Landroidx/media3/common/y$b;

    move-result-object v1

    invoke-virtual {v1}, Landroidx/media3/common/y$b;->I()Landroidx/media3/common/y;

    move-result-object v1

    invoke-interface {v0, v1}, Lz2/r0;->a(Landroidx/media3/common/y;)V

    iget-object v0, p0, Lq3/k;->t:Lz2/u;

    invoke-interface {v0}, Lz2/u;->endTracks()V

    iget-object v0, p0, Lq3/k;->t:Lz2/u;

    new-instance v1, Lz2/m0$b;

    const-wide v2, -0x7fffffffffffffffL    # -4.9E-324

    invoke-direct {v1, v2, v3}, Lz2/m0$b;-><init>(J)V

    invoke-interface {v0, v1}, Lz2/u;->g(Lz2/m0;)V

    :cond_1
    return-void
.end method

.method public final v(Lq3/a$a;)V
    .locals 24
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/common/ParserException;
        }
    .end annotation

    move-object/from16 v0, p0

    move-object/from16 v1, p1

    new-instance v9, Ljava/util/ArrayList;

    invoke-direct {v9}, Ljava/util/ArrayList;-><init>()V

    iget v2, v0, Lq3/k;->y:I

    const/4 v10, 0x0

    const/4 v11, 0x1

    if-ne v2, v11, :cond_0

    const/4 v7, 0x1

    goto :goto_0

    :cond_0
    const/4 v7, 0x0

    :goto_0
    new-instance v12, Lz2/f0;

    invoke-direct {v12}, Lz2/f0;-><init>()V

    const v2, 0x75647461

    invoke-virtual {v1, v2}, Lq3/a$a;->g(I)Lq3/a$b;

    move-result-object v2

    if-eqz v2, :cond_1

    invoke-static {v2}, Lq3/b;->C(Lq3/a$b;)Landroidx/media3/common/Metadata;

    move-result-object v2

    invoke-virtual {v12, v2}, Lz2/f0;->c(Landroidx/media3/common/Metadata;)Z

    move-object v14, v2

    goto :goto_1

    :cond_1
    const/4 v14, 0x0

    :goto_1
    const v2, 0x6d657461

    invoke-virtual {v1, v2}, Lq3/a$a;->f(I)Lq3/a$a;

    move-result-object v2

    if-eqz v2, :cond_2

    invoke-static {v2}, Lq3/b;->p(Lq3/a$a;)Landroidx/media3/common/Metadata;

    move-result-object v2

    move-object v15, v2

    goto :goto_2

    :cond_2
    const/4 v15, 0x0

    :goto_2
    new-instance v8, Landroidx/media3/common/Metadata;

    new-array v2, v11, [Landroidx/media3/common/Metadata$Entry;

    const v3, 0x6d766864

    invoke-virtual {v1, v3}, Lq3/a$a;->g(I)Lq3/a$b;

    move-result-object v3

    invoke-static {v3}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lq3/a$b;

    iget-object v3, v3, Lq3/a$b;->b:Le2/c0;

    invoke-static {v3}, Lq3/b;->r(Le2/c0;)Landroidx/media3/container/Mp4TimestampData;

    move-result-object v3

    aput-object v3, v2, v10

    invoke-direct {v8, v2}, Landroidx/media3/common/Metadata;-><init>([Landroidx/media3/common/Metadata$Entry;)V

    iget v2, v0, Lq3/k;->b:I

    and-int/2addr v2, v11

    if-eqz v2, :cond_3

    const/4 v6, 0x1

    goto :goto_3

    :cond_3
    const/4 v6, 0x0

    :goto_3
    const-wide v3, -0x7fffffffffffffffL    # -4.9E-324

    const/4 v5, 0x0

    new-instance v16, Lq3/j;

    invoke-direct/range {v16 .. v16}, Lq3/j;-><init>()V

    move-object/from16 v1, p1

    move-object v2, v12

    move-object/from16 v17, v8

    move-object/from16 v8, v16

    invoke-static/range {v1 .. v8}, Lq3/b;->B(Lq3/a$a;Lz2/f0;JLandroidx/media3/common/DrmInitData;ZZLcom/google/common/base/f;)Ljava/util/List;

    move-result-object v1

    move-wide v7, v3

    const/4 v5, 0x0

    const/4 v6, -0x1

    const/4 v13, 0x0

    :goto_4
    invoke-interface {v1}, Ljava/util/List;->size()I

    move-result v11

    if-ge v5, v11, :cond_d

    invoke-interface {v1, v5}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v11

    check-cast v11, Lq3/s;

    iget v10, v11, Lq3/s;->b:I

    if-nez v10, :cond_4

    move/from16 v21, v13

    move-object/from16 v18, v14

    const/4 v2, -0x1

    const/4 v13, 0x1

    goto/16 :goto_b

    :cond_4
    iget-object v10, v11, Lq3/s;->a:Lq3/p;

    move-object/from16 v18, v14

    move-object/from16 v19, v15

    iget-wide v14, v10, Lq3/p;->e:J

    cmp-long v20, v14, v3

    if-eqz v20, :cond_5

    goto :goto_5

    :cond_5
    iget-wide v14, v11, Lq3/s;->h:J

    :goto_5
    invoke-static {v7, v8, v14, v15}, Ljava/lang/Math;->max(JJ)J

    move-result-wide v7

    new-instance v3, Lq3/k$a;

    iget-object v4, v0, Lq3/k;->t:Lz2/u;

    add-int/lit8 v21, v13, 0x1

    iget v2, v10, Lq3/p;->b:I

    invoke-interface {v4, v13, v2}, Lz2/u;->track(II)Lz2/r0;

    move-result-object v2

    invoke-direct {v3, v10, v11, v2}, Lq3/k$a;-><init>(Lq3/p;Lq3/s;Lz2/r0;)V

    iget-object v2, v10, Lq3/p;->f:Landroidx/media3/common/y;

    iget-object v2, v2, Landroidx/media3/common/y;->m:Ljava/lang/String;

    const-string v4, "audio/true-hd"

    invoke-virtual {v4, v2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_6

    iget v2, v11, Lq3/s;->e:I

    mul-int/lit8 v2, v2, 0x10

    goto :goto_6

    :cond_6
    iget v2, v11, Lq3/s;->e:I

    add-int/lit8 v2, v2, 0x1e

    :goto_6
    iget-object v4, v10, Lq3/p;->f:Landroidx/media3/common/y;

    invoke-virtual {v4}, Landroidx/media3/common/y;->b()Landroidx/media3/common/y$b;

    move-result-object v4

    invoke-virtual {v4, v2}, Landroidx/media3/common/y$b;->c0(I)Landroidx/media3/common/y$b;

    iget v2, v10, Lq3/p;->b:I

    const/4 v13, 0x2

    if-ne v2, v13, :cond_9

    iget v2, v0, Lq3/k;->b:I

    and-int/lit8 v2, v2, 0x8

    if-eqz v2, :cond_8

    iget-object v2, v10, Lq3/p;->f:Landroidx/media3/common/y;

    iget v2, v2, Landroidx/media3/common/y;->f:I

    const/4 v13, -0x1

    if-ne v6, v13, :cond_7

    const/4 v13, 0x1

    goto :goto_7

    :cond_7
    const/4 v13, 0x2

    :goto_7
    or-int/2addr v2, v13

    invoke-virtual {v4, v2}, Landroidx/media3/common/y$b;->i0(I)Landroidx/media3/common/y$b;

    :cond_8
    const-wide/16 v22, 0x0

    cmp-long v2, v14, v22

    if-lez v2, :cond_9

    iget v2, v11, Lq3/s;->b:I

    if-lez v2, :cond_9

    int-to-float v2, v2

    long-to-float v11, v14

    const v13, 0x49742400    # 1000000.0f

    div-float/2addr v11, v13

    div-float/2addr v2, v11

    invoke-virtual {v4, v2}, Landroidx/media3/common/y$b;->U(F)Landroidx/media3/common/y$b;

    :cond_9
    iget v2, v10, Lq3/p;->b:I

    invoke-static {v2, v12, v4}, Lq3/h;->k(ILz2/f0;Landroidx/media3/common/y$b;)V

    iget v2, v10, Lq3/p;->b:I

    const/4 v11, 0x3

    new-array v11, v11, [Landroidx/media3/common/Metadata;

    iget-object v13, v0, Lq3/k;->i:Ljava/util/List;

    invoke-interface {v13}, Ljava/util/List;->isEmpty()Z

    move-result v13

    if-eqz v13, :cond_a

    const/4 v13, 0x0

    :goto_8
    const/4 v14, 0x0

    goto :goto_9

    :cond_a
    new-instance v13, Landroidx/media3/common/Metadata;

    iget-object v14, v0, Lq3/k;->i:Ljava/util/List;

    invoke-direct {v13, v14}, Landroidx/media3/common/Metadata;-><init>(Ljava/util/List;)V

    goto :goto_8

    :goto_9
    aput-object v13, v11, v14

    const/4 v13, 0x1

    aput-object v18, v11, v13

    const/4 v14, 0x2

    aput-object v17, v11, v14

    move-object/from16 v15, v19

    invoke-static {v2, v15, v4, v11}, Lq3/h;->l(ILandroidx/media3/common/Metadata;Landroidx/media3/common/y$b;[Landroidx/media3/common/Metadata;)V

    iget-object v2, v3, Lq3/k$a;->c:Lz2/r0;

    invoke-virtual {v4}, Landroidx/media3/common/y$b;->I()Landroidx/media3/common/y;

    move-result-object v4

    invoke-interface {v2, v4}, Lz2/r0;->a(Landroidx/media3/common/y;)V

    iget v2, v10, Lq3/p;->b:I

    if-ne v2, v14, :cond_b

    const/4 v2, -0x1

    if-ne v6, v2, :cond_c

    invoke-interface {v9}, Ljava/util/List;->size()I

    move-result v6

    goto :goto_a

    :cond_b
    const/4 v2, -0x1

    :cond_c
    :goto_a
    invoke-interface {v9, v3}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    :goto_b
    add-int/lit8 v5, v5, 0x1

    move-object/from16 v14, v18

    move/from16 v13, v21

    const-wide v3, -0x7fffffffffffffffL    # -4.9E-324

    const/4 v10, 0x0

    goto/16 :goto_4

    :cond_d
    iput v6, v0, Lq3/k;->w:I

    iput-wide v7, v0, Lq3/k;->x:J

    const/4 v1, 0x0

    new-array v1, v1, [Lq3/k$a;

    invoke-interface {v9, v1}, Ljava/util/List;->toArray([Ljava/lang/Object;)[Ljava/lang/Object;

    move-result-object v1

    check-cast v1, [Lq3/k$a;

    iput-object v1, v0, Lq3/k;->u:[Lq3/k$a;

    invoke-static {v1}, Lq3/k;->j([Lq3/k$a;)[[J

    move-result-object v1

    iput-object v1, v0, Lq3/k;->v:[[J

    iget-object v1, v0, Lq3/k;->t:Lz2/u;

    invoke-interface {v1}, Lz2/u;->endTracks()V

    iget-object v1, v0, Lq3/k;->t:Lz2/u;

    invoke-interface {v1, v0}, Lz2/u;->g(Lz2/m0;)V

    return-void
.end method

.method public final w(J)V
    .locals 13

    iget v0, p0, Lq3/k;->k:I

    const v1, 0x6d707664

    if-ne v0, v1, :cond_0

    new-instance v0, Landroidx/media3/extractor/metadata/mp4/MotionPhotoMetadata;

    const-wide/16 v3, 0x0

    const-wide v7, -0x7fffffffffffffffL    # -4.9E-324

    iget v1, p0, Lq3/k;->m:I

    int-to-long v5, v1

    add-long v9, p1, v5

    iget-wide v5, p0, Lq3/k;->l:J

    int-to-long v1, v1

    sub-long v11, v5, v1

    move-object v2, v0

    move-wide v5, p1

    invoke-direct/range {v2 .. v12}, Landroidx/media3/extractor/metadata/mp4/MotionPhotoMetadata;-><init>(JJJJJ)V

    iput-object v0, p0, Lq3/k;->z:Landroidx/media3/extractor/metadata/mp4/MotionPhotoMetadata;

    :cond_0
    return-void
.end method

.method public final x(Lz2/t;)Z
    .locals 8
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget v0, p0, Lq3/k;->m:I

    const/4 v1, 0x1

    const/16 v2, 0x8

    const/4 v3, 0x0

    if-nez v0, :cond_1

    iget-object v0, p0, Lq3/k;->f:Le2/c0;

    invoke-virtual {v0}, Le2/c0;->e()[B

    move-result-object v0

    invoke-interface {p1, v0, v3, v2, v1}, Lz2/t;->readFully([BIIZ)Z

    move-result v0

    if-nez v0, :cond_0

    invoke-virtual {p0}, Lq3/k;->t()V

    return v3

    :cond_0
    iput v2, p0, Lq3/k;->m:I

    iget-object v0, p0, Lq3/k;->f:Le2/c0;

    invoke-virtual {v0, v3}, Le2/c0;->U(I)V

    iget-object v0, p0, Lq3/k;->f:Le2/c0;

    invoke-virtual {v0}, Le2/c0;->J()J

    move-result-wide v4

    iput-wide v4, p0, Lq3/k;->l:J

    iget-object v0, p0, Lq3/k;->f:Le2/c0;

    invoke-virtual {v0}, Le2/c0;->q()I

    move-result v0

    iput v0, p0, Lq3/k;->k:I

    :cond_1
    iget-wide v4, p0, Lq3/k;->l:J

    const-wide/16 v6, 0x1

    cmp-long v0, v4, v6

    if-nez v0, :cond_2

    iget-object v0, p0, Lq3/k;->f:Le2/c0;

    invoke-virtual {v0}, Le2/c0;->e()[B

    move-result-object v0

    invoke-interface {p1, v0, v2, v2}, Lz2/t;->readFully([BII)V

    iget v0, p0, Lq3/k;->m:I

    add-int/2addr v0, v2

    iput v0, p0, Lq3/k;->m:I

    iget-object v0, p0, Lq3/k;->f:Le2/c0;

    invoke-virtual {v0}, Le2/c0;->M()J

    move-result-wide v4

    iput-wide v4, p0, Lq3/k;->l:J

    goto :goto_0

    :cond_2
    const-wide/16 v6, 0x0

    cmp-long v0, v4, v6

    if-nez v0, :cond_4

    invoke-interface {p1}, Lz2/t;->getLength()J

    move-result-wide v4

    const-wide/16 v6, -0x1

    cmp-long v0, v4, v6

    if-nez v0, :cond_3

    iget-object v0, p0, Lq3/k;->g:Ljava/util/ArrayDeque;

    invoke-virtual {v0}, Ljava/util/ArrayDeque;->peek()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lq3/a$a;

    if-eqz v0, :cond_3

    iget-wide v4, v0, Lq3/a$a;->b:J

    :cond_3
    cmp-long v0, v4, v6

    if-eqz v0, :cond_4

    invoke-interface {p1}, Lz2/t;->getPosition()J

    move-result-wide v6

    sub-long/2addr v4, v6

    iget v0, p0, Lq3/k;->m:I

    int-to-long v6, v0

    add-long/2addr v4, v6

    iput-wide v4, p0, Lq3/k;->l:J

    :cond_4
    :goto_0
    iget-wide v4, p0, Lq3/k;->l:J

    iget v0, p0, Lq3/k;->m:I

    int-to-long v6, v0

    cmp-long v0, v4, v6

    if-ltz v0, :cond_b

    iget v0, p0, Lq3/k;->k:I

    invoke-static {v0}, Lq3/k;->B(I)Z

    move-result v0

    if-eqz v0, :cond_7

    invoke-interface {p1}, Lz2/t;->getPosition()J

    move-result-wide v2

    iget-wide v4, p0, Lq3/k;->l:J

    add-long/2addr v2, v4

    iget v0, p0, Lq3/k;->m:I

    int-to-long v6, v0

    sub-long/2addr v2, v6

    int-to-long v6, v0

    cmp-long v0, v4, v6

    if-eqz v0, :cond_5

    iget v0, p0, Lq3/k;->k:I

    const v4, 0x6d657461

    if-ne v0, v4, :cond_5

    invoke-virtual {p0, p1}, Lq3/k;->r(Lz2/t;)V

    :cond_5
    iget-object p1, p0, Lq3/k;->g:Ljava/util/ArrayDeque;

    new-instance v0, Lq3/a$a;

    iget v4, p0, Lq3/k;->k:I

    invoke-direct {v0, v4, v2, v3}, Lq3/a$a;-><init>(IJ)V

    invoke-virtual {p1, v0}, Ljava/util/ArrayDeque;->push(Ljava/lang/Object;)V

    iget-wide v4, p0, Lq3/k;->l:J

    iget p1, p0, Lq3/k;->m:I

    int-to-long v6, p1

    cmp-long p1, v4, v6

    if-nez p1, :cond_6

    invoke-virtual {p0, v2, v3}, Lq3/k;->s(J)V

    goto :goto_3

    :cond_6
    invoke-virtual {p0}, Lq3/k;->k()V

    goto :goto_3

    :cond_7
    iget v0, p0, Lq3/k;->k:I

    invoke-static {v0}, Lq3/k;->C(I)Z

    move-result v0

    if-eqz v0, :cond_a

    iget p1, p0, Lq3/k;->m:I

    if-ne p1, v2, :cond_8

    const/4 p1, 0x1

    goto :goto_1

    :cond_8
    const/4 p1, 0x0

    :goto_1
    invoke-static {p1}, Le2/a;->g(Z)V

    iget-wide v4, p0, Lq3/k;->l:J

    const-wide/32 v6, 0x7fffffff

    cmp-long p1, v4, v6

    if-gtz p1, :cond_9

    const/4 p1, 0x1

    goto :goto_2

    :cond_9
    const/4 p1, 0x0

    :goto_2
    invoke-static {p1}, Le2/a;->g(Z)V

    new-instance p1, Le2/c0;

    iget-wide v4, p0, Lq3/k;->l:J

    long-to-int v0, v4

    invoke-direct {p1, v0}, Le2/c0;-><init>(I)V

    iget-object v0, p0, Lq3/k;->f:Le2/c0;

    invoke-virtual {v0}, Le2/c0;->e()[B

    move-result-object v0

    invoke-virtual {p1}, Le2/c0;->e()[B

    move-result-object v4

    invoke-static {v0, v3, v4, v3, v2}, Ljava/lang/System;->arraycopy(Ljava/lang/Object;ILjava/lang/Object;II)V

    iput-object p1, p0, Lq3/k;->n:Le2/c0;

    iput v1, p0, Lq3/k;->j:I

    goto :goto_3

    :cond_a
    invoke-interface {p1}, Lz2/t;->getPosition()J

    move-result-wide v2

    iget p1, p0, Lq3/k;->m:I

    int-to-long v4, p1

    sub-long/2addr v2, v4

    invoke-virtual {p0, v2, v3}, Lq3/k;->w(J)V

    const/4 p1, 0x0

    iput-object p1, p0, Lq3/k;->n:Le2/c0;

    iput v1, p0, Lq3/k;->j:I

    :goto_3
    return v1

    :cond_b
    const-string p1, "Atom size less than header length (unsupported)."

    invoke-static {p1}, Landroidx/media3/common/ParserException;->createForUnsupportedContainerFeature(Ljava/lang/String;)Landroidx/media3/common/ParserException;

    move-result-object p1

    throw p1
.end method

.method public final y(Lz2/t;Lz2/l0;)Z
    .locals 9
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget-wide v0, p0, Lq3/k;->l:J

    iget v2, p0, Lq3/k;->m:I

    int-to-long v2, v2

    sub-long/2addr v0, v2

    invoke-interface {p1}, Lz2/t;->getPosition()J

    move-result-wide v2

    add-long/2addr v2, v0

    iget-object v4, p0, Lq3/k;->n:Le2/c0;

    const/4 v5, 0x1

    const/4 v6, 0x0

    if-eqz v4, :cond_1

    invoke-virtual {v4}, Le2/c0;->e()[B

    move-result-object p2

    iget v7, p0, Lq3/k;->m:I

    long-to-int v1, v0

    invoke-interface {p1, p2, v7, v1}, Lz2/t;->readFully([BII)V

    iget p1, p0, Lq3/k;->k:I

    const p2, 0x66747970

    if-ne p1, p2, :cond_0

    iput-boolean v5, p0, Lq3/k;->s:Z

    invoke-static {v4}, Lq3/k;->u(Le2/c0;)I

    move-result p1

    iput p1, p0, Lq3/k;->y:I

    goto :goto_0

    :cond_0
    iget-object p1, p0, Lq3/k;->g:Ljava/util/ArrayDeque;

    invoke-virtual {p1}, Ljava/util/ArrayDeque;->isEmpty()Z

    move-result p1

    if-nez p1, :cond_3

    iget-object p1, p0, Lq3/k;->g:Ljava/util/ArrayDeque;

    invoke-virtual {p1}, Ljava/util/ArrayDeque;->peek()Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lq3/a$a;

    new-instance p2, Lq3/a$b;

    iget v0, p0, Lq3/k;->k:I

    invoke-direct {p2, v0, v4}, Lq3/a$b;-><init>(ILe2/c0;)V

    invoke-virtual {p1, p2}, Lq3/a$a;->e(Lq3/a$b;)V

    goto :goto_0

    :cond_1
    iget-boolean v4, p0, Lq3/k;->s:Z

    if-nez v4, :cond_2

    iget v4, p0, Lq3/k;->k:I

    const v7, 0x6d646174

    if-ne v4, v7, :cond_2

    iput v5, p0, Lq3/k;->y:I

    :cond_2
    const-wide/32 v7, 0x40000

    cmp-long v4, v0, v7

    if-gez v4, :cond_4

    long-to-int p2, v0

    invoke-interface {p1, p2}, Lz2/t;->skipFully(I)V

    :cond_3
    :goto_0
    const/4 p1, 0x0

    goto :goto_1

    :cond_4
    invoke-interface {p1}, Lz2/t;->getPosition()J

    move-result-wide v7

    add-long/2addr v7, v0

    iput-wide v7, p2, Lz2/l0;->a:J

    const/4 p1, 0x1

    :goto_1
    invoke-virtual {p0, v2, v3}, Lq3/k;->s(J)V

    if-eqz p1, :cond_5

    iget p1, p0, Lq3/k;->j:I

    const/4 p2, 0x2

    if-eq p1, p2, :cond_5

    goto :goto_2

    :cond_5
    const/4 v5, 0x0

    :goto_2
    return v5
.end method

.method public final z(Lz2/t;Lz2/l0;)I
    .locals 18
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    move-object/from16 v0, p0

    move-object/from16 v1, p1

    invoke-interface/range {p1 .. p1}, Lz2/t;->getPosition()J

    move-result-wide v2

    iget v4, v0, Lq3/k;->o:I

    const/4 v5, -0x1

    if-ne v4, v5, :cond_0

    invoke-virtual {v0, v2, v3}, Lq3/k;->n(J)I

    move-result v4

    iput v4, v0, Lq3/k;->o:I

    if-ne v4, v5, :cond_0

    return v5

    :cond_0
    iget-object v4, v0, Lq3/k;->u:[Lq3/k$a;

    iget v6, v0, Lq3/k;->o:I

    aget-object v4, v4, v6

    iget-object v14, v4, Lq3/k$a;->c:Lz2/r0;

    iget v15, v4, Lq3/k$a;->e:I

    iget-object v6, v4, Lq3/k$a;->b:Lq3/s;

    iget-object v7, v6, Lq3/s;->c:[J

    aget-wide v8, v7, v15

    iget-object v6, v6, Lq3/s;->d:[I

    aget v6, v6, v15

    iget-object v13, v4, Lq3/k$a;->d:Lz2/s0;

    sub-long v2, v8, v2

    iget v7, v0, Lq3/k;->p:I

    int-to-long v10, v7

    add-long/2addr v2, v10

    const-wide/16 v10, 0x0

    const/4 v12, 0x1

    cmp-long v7, v2, v10

    if-ltz v7, :cond_c

    const-wide/32 v10, 0x40000

    cmp-long v7, v2, v10

    if-ltz v7, :cond_1

    move-object/from16 v1, p2

    const/16 v17, 0x1

    goto/16 :goto_4

    :cond_1
    iget-object v7, v4, Lq3/k$a;->a:Lq3/p;

    iget v7, v7, Lq3/p;->g:I

    if-ne v7, v12, :cond_2

    const-wide/16 v7, 0x8

    add-long/2addr v2, v7

    add-int/lit8 v6, v6, -0x8

    :cond_2
    long-to-int v3, v2

    invoke-interface {v1, v3}, Lz2/t;->skipFully(I)V

    iget-object v2, v4, Lq3/k$a;->a:Lq3/p;

    iget v3, v2, Lq3/p;->j:I

    const/4 v11, 0x0

    const/4 v10, 0x0

    if-eqz v3, :cond_6

    iget-object v2, v0, Lq3/k;->d:Le2/c0;

    invoke-virtual {v2}, Le2/c0;->e()[B

    move-result-object v2

    aput-byte v10, v2, v10

    aput-byte v10, v2, v12

    const/4 v3, 0x2

    aput-byte v10, v2, v3

    iget-object v3, v4, Lq3/k$a;->a:Lq3/p;

    iget v3, v3, Lq3/p;->j:I

    rsub-int/lit8 v7, v3, 0x4

    :goto_0
    iget v8, v0, Lq3/k;->q:I

    if-ge v8, v6, :cond_5

    iget v8, v0, Lq3/k;->r:I

    if-nez v8, :cond_4

    invoke-interface {v1, v2, v7, v3}, Lz2/t;->readFully([BII)V

    iget v8, v0, Lq3/k;->p:I

    add-int/2addr v8, v3

    iput v8, v0, Lq3/k;->p:I

    iget-object v8, v0, Lq3/k;->d:Le2/c0;

    invoke-virtual {v8, v10}, Le2/c0;->U(I)V

    iget-object v8, v0, Lq3/k;->d:Le2/c0;

    invoke-virtual {v8}, Le2/c0;->q()I

    move-result v8

    if-ltz v8, :cond_3

    iput v8, v0, Lq3/k;->r:I

    iget-object v8, v0, Lq3/k;->c:Le2/c0;

    invoke-virtual {v8, v10}, Le2/c0;->U(I)V

    iget-object v8, v0, Lq3/k;->c:Le2/c0;

    const/4 v9, 0x4

    invoke-interface {v14, v8, v9}, Lz2/r0;->f(Le2/c0;I)V

    iget v8, v0, Lq3/k;->q:I

    add-int/2addr v8, v9

    iput v8, v0, Lq3/k;->q:I

    add-int/2addr v6, v7

    goto :goto_0

    :cond_3
    const-string v1, "Invalid NAL length"

    invoke-static {v1, v11}, Landroidx/media3/common/ParserException;->createForMalformedContainer(Ljava/lang/String;Ljava/lang/Throwable;)Landroidx/media3/common/ParserException;

    move-result-object v1

    throw v1

    :cond_4
    invoke-interface {v14, v1, v8, v10}, Lz2/r0;->c(Landroidx/media3/common/l;IZ)I

    move-result v8

    iget v9, v0, Lq3/k;->p:I

    add-int/2addr v9, v8

    iput v9, v0, Lq3/k;->p:I

    iget v9, v0, Lq3/k;->q:I

    add-int/2addr v9, v8

    iput v9, v0, Lq3/k;->q:I

    iget v9, v0, Lq3/k;->r:I

    sub-int/2addr v9, v8

    iput v9, v0, Lq3/k;->r:I

    goto :goto_0

    :cond_5
    move v1, v6

    goto :goto_2

    :cond_6
    iget-object v2, v2, Lq3/p;->f:Landroidx/media3/common/y;

    iget-object v2, v2, Landroidx/media3/common/y;->m:Ljava/lang/String;

    const-string v3, "audio/ac4"

    invoke-virtual {v3, v2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_8

    iget v2, v0, Lq3/k;->q:I

    if-nez v2, :cond_7

    iget-object v2, v0, Lq3/k;->e:Le2/c0;

    invoke-static {v6, v2}, Lz2/c;->a(ILe2/c0;)V

    iget-object v2, v0, Lq3/k;->e:Le2/c0;

    const/4 v3, 0x7

    invoke-interface {v14, v2, v3}, Lz2/r0;->f(Le2/c0;I)V

    iget v2, v0, Lq3/k;->q:I

    add-int/2addr v2, v3

    iput v2, v0, Lq3/k;->q:I

    :cond_7
    add-int/lit8 v6, v6, 0x7

    goto :goto_1

    :cond_8
    if-eqz v13, :cond_9

    invoke-virtual {v13, v1}, Lz2/s0;->d(Lz2/t;)V

    :cond_9
    :goto_1
    iget v2, v0, Lq3/k;->q:I

    if-ge v2, v6, :cond_5

    sub-int v2, v6, v2

    invoke-interface {v14, v1, v2, v10}, Lz2/r0;->c(Landroidx/media3/common/l;IZ)I

    move-result v2

    iget v3, v0, Lq3/k;->p:I

    add-int/2addr v3, v2

    iput v3, v0, Lq3/k;->p:I

    iget v3, v0, Lq3/k;->q:I

    add-int/2addr v3, v2

    iput v3, v0, Lq3/k;->q:I

    iget v3, v0, Lq3/k;->r:I

    sub-int/2addr v3, v2

    iput v3, v0, Lq3/k;->r:I

    goto :goto_1

    :goto_2
    iget-object v2, v4, Lq3/k$a;->b:Lq3/s;

    iget-object v3, v2, Lq3/s;->f:[J

    aget-wide v8, v3, v15

    iget-object v2, v2, Lq3/s;->g:[I

    aget v2, v2, v15

    if-eqz v13, :cond_a

    const/4 v3, 0x0

    const/16 v16, 0x0

    move-object v6, v13

    move-object v7, v14

    move v10, v2

    move-object v2, v11

    move v11, v1

    const/16 v17, 0x1

    move v12, v3

    move-object v1, v13

    move-object/from16 v13, v16

    invoke-virtual/range {v6 .. v13}, Lz2/s0;->c(Lz2/r0;JIIILz2/r0$a;)V

    add-int/lit8 v15, v15, 0x1

    iget-object v3, v4, Lq3/k$a;->b:Lq3/s;

    iget v3, v3, Lq3/s;->b:I

    if-ne v15, v3, :cond_b

    invoke-virtual {v1, v14, v2}, Lz2/s0;->a(Lz2/r0;Lz2/r0$a;)V

    goto :goto_3

    :cond_a
    const/16 v17, 0x1

    const/4 v11, 0x0

    const/4 v12, 0x0

    move-object v6, v14

    move-wide v7, v8

    move v9, v2

    move v10, v1

    invoke-interface/range {v6 .. v12}, Lz2/r0;->e(JIIILz2/r0$a;)V

    :cond_b
    :goto_3
    iget v1, v4, Lq3/k$a;->e:I

    add-int/lit8 v1, v1, 0x1

    iput v1, v4, Lq3/k$a;->e:I

    iput v5, v0, Lq3/k;->o:I

    const/4 v1, 0x0

    iput v1, v0, Lq3/k;->p:I

    iput v1, v0, Lq3/k;->q:I

    iput v1, v0, Lq3/k;->r:I

    return v1

    :cond_c
    const/16 v17, 0x1

    move-object/from16 v1, p2

    :goto_4
    iput-wide v8, v1, Lz2/l0;->a:J

    return v17
.end method
