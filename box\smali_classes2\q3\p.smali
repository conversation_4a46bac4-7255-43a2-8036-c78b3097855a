.class public final Lq3/p;
.super Ljava/lang/Object;


# instance fields
.field public final a:I

.field public final b:I

.field public final c:J

.field public final d:J

.field public final e:J

.field public final f:Landroidx/media3/common/y;

.field public final g:I

.field public final h:[J
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public final i:[J
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public final j:I

.field public final k:[Lq3/q;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field


# direct methods
.method public constructor <init>(IIJJJLandroidx/media3/common/y;I[Lq3/q;I[J[J)V
    .locals 0
    .param p11    # [Lq3/q;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .param p13    # [J
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .param p14    # [J
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput p1, p0, Lq3/p;->a:I

    iput p2, p0, Lq3/p;->b:I

    iput-wide p3, p0, Lq3/p;->c:J

    iput-wide p5, p0, Lq3/p;->d:J

    iput-wide p7, p0, Lq3/p;->e:J

    iput-object p9, p0, Lq3/p;->f:Landroidx/media3/common/y;

    iput p10, p0, Lq3/p;->g:I

    iput-object p11, p0, Lq3/p;->k:[Lq3/q;

    iput p12, p0, Lq3/p;->j:I

    iput-object p13, p0, Lq3/p;->h:[J

    iput-object p14, p0, Lq3/p;->i:[J

    return-void
.end method


# virtual methods
.method public a(I)Lq3/q;
    .locals 1
    .annotation build Landroidx/annotation/Nullable;
    .end annotation

    iget-object v0, p0, Lq3/p;->k:[Lq3/q;

    if-nez v0, :cond_0

    const/4 p1, 0x0

    goto :goto_0

    :cond_0
    aget-object p1, v0, p1

    :goto_0
    return-object p1
.end method
