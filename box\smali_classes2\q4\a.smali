.class public Lq4/a;
.super Ljava/lang/Object;


# static fields
.field public static final a:[I

.field public static final b:[I

.field public static final c:[I

.field public static final d:[I

.field public static final e:[I

.field public static final f:[I

.field public static final g:[I

.field public static final h:[I

.field public static final i:[I

.field public static final j:[I

.field public static final k:[I

.field public static final l:[I


# direct methods
.method static constructor <clinit>()V
    .locals 6

    const/16 v0, 0x9

    new-array v0, v0, [I

    fill-array-data v0, :array_0

    sput-object v0, Lq4/a;->a:[I

    const/16 v0, 0x8

    new-array v1, v0, [I

    fill-array-data v1, :array_1

    sput-object v1, Lq4/a;->b:[I

    const/16 v1, 0xe

    new-array v1, v1, [I

    fill-array-data v1, :array_2

    sput-object v1, Lq4/a;->c:[I

    const v1, 0x101051e

    const v2, 0x1010003

    const v3, 0x1010405

    filled-new-array {v2, v3, v1}, [I

    move-result-object v1

    sput-object v1, Lq4/a;->d:[I

    const v1, 0x1010199

    filled-new-array {v1}, [I

    move-result-object v1

    sput-object v1, Lq4/a;->e:[I

    const v1, 0x10101cd

    filled-new-array {v2, v1}, [I

    move-result-object v1

    sput-object v1, Lq4/a;->f:[I

    new-array v0, v0, [I

    fill-array-data v0, :array_3

    sput-object v0, Lq4/a;->g:[I

    const v0, 0x10102e2

    filled-new-array {v0}, [I

    move-result-object v0

    sput-object v0, Lq4/a;->h:[I

    const v0, 0x10102de

    const v1, 0x10102df

    const v2, 0x10102e0

    const v4, 0x10102e1

    filled-new-array {v0, v1, v2, v4}, [I

    move-result-object v0

    sput-object v0, Lq4/a;->i:[I

    const v0, 0x1010141

    const v1, 0x10104d8

    const v5, 0x1010024

    filled-new-array {v5, v0, v2, v1}, [I

    move-result-object v0

    sput-object v0, Lq4/a;->j:[I

    const v0, 0x1010474

    const v1, 0x1010475

    filled-new-array {v4, v3, v0, v1}, [I

    move-result-object v0

    sput-object v0, Lq4/a;->k:[I

    const v0, 0x10103fe

    const v1, 0x10103ff

    const v2, 0x10103fc

    const v4, 0x10103fd

    filled-new-array {v2, v4, v0, v1, v3}, [I

    move-result-object v0

    sput-object v0, Lq4/a;->l:[I

    return-void

    :array_0
    .array-data 4
        0x1010003
        0x1010121
        0x1010155
        0x1010159
        0x101031f
        0x10103ea
        0x10103fb
        0x1010402
        0x1010403
    .end array-data

    :array_1
    .array-data 4
        0x1010003
        0x10101b5
        0x10101b6
        0x1010324
        0x1010325
        0x1010326
        0x101045a
        0x101045b
    .end array-data

    :array_2
    .array-data 4
        0x1010003
        0x1010404
        0x1010405
        0x1010406
        0x1010407
        0x1010408
        0x1010409
        0x101040a
        0x101040b
        0x101040c
        0x101040d
        0x10104cb
        0x10104cc
        0x101051e
    .end array-data

    :array_3
    .array-data 4
        0x1010141
        0x1010198
        0x10101be
        0x10101bf
        0x10101c0
        0x10102de
        0x10102df
        0x10102e0
    .end array-data
.end method
