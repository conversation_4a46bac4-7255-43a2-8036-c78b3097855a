.class public interface abstract Lq5/b;
.super Ljava/lang/Object;


# virtual methods
.method public abstract N()V
.end method

.method public abstract O(I)V
.end method

.method public abstract S()Ljava/math/BigDecimal;
.end method

.method public abstract W(C)I
.end method

.method public abstract X()[B
.end method

.method public abstract Y()Ljava/lang/String;
.end method

.method public abstract Z()Ljava/util/TimeZone;
.end method

.method public abstract a()C
.end method

.method public abstract b()I
.end method

.method public abstract b0()Ljava/lang/Number;
.end method

.method public abstract c0()F
.end method

.method public abstract close()V
.end method

.method public abstract e()Ljava/lang/String;
.end method

.method public abstract f()J
.end method

.method public abstract f0(Lq5/i;)Ljava/lang/String;
.end method

.method public abstract g(C)F
.end method

.method public abstract g0()I
.end method

.method public abstract h(Lq5/i;C)Ljava/lang/String;
.end method

.method public abstract h0(C)Ljava/lang/String;
.end method

.method public abstract i0(Ljava/lang/Class;Lq5/i;C)Ljava/lang/Enum;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Class<",
            "*>;",
            "Lq5/i;",
            "C)",
            "Ljava/lang/Enum<",
            "*>;"
        }
    .end annotation
.end method

.method public abstract isEnabled(I)Z
.end method

.method public abstract j(Lcom/alibaba/fastjson/parser/Feature;)Z
.end method

.method public abstract k()I
.end method

.method public abstract k0()V
.end method

.method public abstract l()V
.end method

.method public abstract m(I)V
.end method

.method public abstract m0()V
.end method

.method public abstract n(Lq5/i;)Ljava/lang/String;
.end method

.method public abstract next()C
.end method

.method public abstract o()I
.end method

.method public abstract o0(C)J
.end method

.method public abstract p(C)D
.end method

.method public abstract p0(Z)Ljava/lang/Number;
.end method

.method public abstract q(C)Ljava/math/BigDecimal;
.end method

.method public abstract q0()Ljava/util/Locale;
.end method

.method public abstract r()V
.end method

.method public abstract s()Ljava/lang/String;
.end method

.method public abstract s0()Ljava/lang/String;
.end method

.method public abstract t()Z
.end method

.method public abstract u()Z
.end method

.method public abstract v(C)Z
.end method

.method public abstract w(Lq5/i;)Ljava/lang/String;
.end method

.method public abstract x()V
.end method
