.class public Lq/a;
.super Ljava/lang/Object;

# interfaces
.implements Lq/c;


# annotations
.annotation build Landroidx/annotation/RequiresApi;
    value = 0x15
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public a(Lq/b;)V
    .locals 1

    invoke-virtual {p0, p1}, Lq/a;->k(Lq/b;)F

    move-result v0

    invoke-virtual {p0, p1, v0}, Lq/a;->o(Lq/b;F)V

    return-void
.end method

.method public b(Lq/b;F)V
    .locals 0

    invoke-virtual {p0, p1}, Lq/a;->p(Lq/b;)Lq/d;

    move-result-object p1

    invoke-virtual {p1, p2}, Lq/d;->h(F)V

    return-void
.end method

.method public c(Lq/b;)F
    .locals 0

    invoke-interface {p1}, Lq/b;->f()Landroid/view/View;

    move-result-object p1

    invoke-virtual {p1}, Landroid/view/View;->getElevation()F

    move-result p1

    return p1
.end method

.method public d(Lq/b;)F
    .locals 1

    invoke-virtual {p0, p1}, Lq/a;->i(Lq/b;)F

    move-result p1

    const/high16 v0, 0x40000000    # 2.0f

    mul-float p1, p1, v0

    return p1
.end method

.method public e(Lq/b;F)V
    .locals 0

    invoke-interface {p1}, Lq/b;->f()Landroid/view/View;

    move-result-object p1

    invoke-virtual {p1, p2}, Landroid/view/View;->setElevation(F)V

    return-void
.end method

.method public f(Lq/b;)V
    .locals 4

    invoke-interface {p1}, Lq/b;->c()Z

    move-result v0

    if-nez v0, :cond_0

    const/4 v0, 0x0

    invoke-interface {p1, v0, v0, v0, v0}, Lq/b;->a(IIII)V

    return-void

    :cond_0
    invoke-virtual {p0, p1}, Lq/a;->k(Lq/b;)F

    move-result v0

    invoke-virtual {p0, p1}, Lq/a;->i(Lq/b;)F

    move-result v1

    invoke-interface {p1}, Lq/b;->e()Z

    move-result v2

    invoke-static {v0, v1, v2}, Lq/e;->a(FFZ)F

    move-result v2

    float-to-double v2, v2

    invoke-static {v2, v3}, Ljava/lang/Math;->ceil(D)D

    move-result-wide v2

    double-to-int v2, v2

    invoke-interface {p1}, Lq/b;->e()Z

    move-result v3

    invoke-static {v0, v1, v3}, Lq/e;->b(FFZ)F

    move-result v0

    float-to-double v0, v0

    invoke-static {v0, v1}, Ljava/lang/Math;->ceil(D)D

    move-result-wide v0

    double-to-int v0, v0

    invoke-interface {p1, v2, v0, v2, v0}, Lq/b;->a(IIII)V

    return-void
.end method

.method public g(Lq/b;Landroid/content/Context;Landroid/content/res/ColorStateList;FFF)V
    .locals 0

    new-instance p2, Lq/d;

    invoke-direct {p2, p3, p4}, Lq/d;-><init>(Landroid/content/res/ColorStateList;F)V

    invoke-interface {p1, p2}, Lq/b;->b(Landroid/graphics/drawable/Drawable;)V

    invoke-interface {p1}, Lq/b;->f()Landroid/view/View;

    move-result-object p2

    const/4 p3, 0x1

    invoke-virtual {p2, p3}, Landroid/view/View;->setClipToOutline(Z)V

    invoke-virtual {p2, p5}, Landroid/view/View;->setElevation(F)V

    invoke-virtual {p0, p1, p6}, Lq/a;->o(Lq/b;F)V

    return-void
.end method

.method public h(Lq/b;)F
    .locals 1

    invoke-virtual {p0, p1}, Lq/a;->i(Lq/b;)F

    move-result p1

    const/high16 v0, 0x40000000    # 2.0f

    mul-float p1, p1, v0

    return p1
.end method

.method public i(Lq/b;)F
    .locals 0

    invoke-virtual {p0, p1}, Lq/a;->p(Lq/b;)Lq/d;

    move-result-object p1

    invoke-virtual {p1}, Lq/d;->d()F

    move-result p1

    return p1
.end method

.method public j(Lq/b;)V
    .locals 1

    invoke-virtual {p0, p1}, Lq/a;->k(Lq/b;)F

    move-result v0

    invoke-virtual {p0, p1, v0}, Lq/a;->o(Lq/b;F)V

    return-void
.end method

.method public k(Lq/b;)F
    .locals 0

    invoke-virtual {p0, p1}, Lq/a;->p(Lq/b;)Lq/d;

    move-result-object p1

    invoke-virtual {p1}, Lq/d;->c()F

    move-result p1

    return p1
.end method

.method public l(Lq/b;)Landroid/content/res/ColorStateList;
    .locals 0

    invoke-virtual {p0, p1}, Lq/a;->p(Lq/b;)Lq/d;

    move-result-object p1

    invoke-virtual {p1}, Lq/d;->b()Landroid/content/res/ColorStateList;

    move-result-object p1

    return-object p1
.end method

.method public m()V
    .locals 0

    return-void
.end method

.method public n(Lq/b;Landroid/content/res/ColorStateList;)V
    .locals 0
    .param p2    # Landroid/content/res/ColorStateList;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    invoke-virtual {p0, p1}, Lq/a;->p(Lq/b;)Lq/d;

    move-result-object p1

    invoke-virtual {p1, p2}, Lq/d;->f(Landroid/content/res/ColorStateList;)V

    return-void
.end method

.method public o(Lq/b;F)V
    .locals 3

    invoke-virtual {p0, p1}, Lq/a;->p(Lq/b;)Lq/d;

    move-result-object v0

    invoke-interface {p1}, Lq/b;->c()Z

    move-result v1

    invoke-interface {p1}, Lq/b;->e()Z

    move-result v2

    invoke-virtual {v0, p2, v1, v2}, Lq/d;->g(FZZ)V

    invoke-virtual {p0, p1}, Lq/a;->f(Lq/b;)V

    return-void
.end method

.method public final p(Lq/b;)Lq/d;
    .locals 0

    invoke-interface {p1}, Lq/b;->d()Landroid/graphics/drawable/Drawable;

    move-result-object p1

    check-cast p1, Lq/d;

    return-object p1
.end method
