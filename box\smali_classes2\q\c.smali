.class public interface abstract Lq/c;
.super Ljava/lang/Object;


# virtual methods
.method public abstract a(Lq/b;)V
.end method

.method public abstract b(Lq/b;F)V
.end method

.method public abstract c(Lq/b;)F
.end method

.method public abstract d(Lq/b;)F
.end method

.method public abstract e(Lq/b;F)V
.end method

.method public abstract f(Lq/b;)V
.end method

.method public abstract g(Lq/b;Landroid/content/Context;Landroid/content/res/ColorStateList;FFF)V
.end method

.method public abstract h(Lq/b;)F
.end method

.method public abstract i(Lq/b;)F
.end method

.method public abstract j(Lq/b;)V
.end method

.method public abstract k(Lq/b;)F
.end method

.method public abstract l(Lq/b;)Landroid/content/res/ColorStateList;
.end method

.method public abstract m()V
.end method

.method public abstract n(Lq/b;Landroid/content/res/ColorStateList;)V
    .param p2    # Landroid/content/res/ColorStateList;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
.end method

.method public abstract o(Lq/b;F)V
.end method
