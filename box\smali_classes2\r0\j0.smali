.class public final synthetic Lr0/j0;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/util/Comparator;


# direct methods
.method public synthetic constructor <init>()V
    .locals 0

    invoke-direct {p0}, <PERSON>java/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final compare(Ljava/lang/Object;Ljava/lang/Object;)I
    .locals 0

    check-cast p1, <PERSON><PERSON><PERSON>/Pair;

    check-cast p2, <PERSON><PERSON><PERSON>/Pair;

    invoke-static {p1, p2}, Lr0/k0;->a(<PERSON><PERSON><PERSON>/Pair;L<PERSON><PERSON>/Pair;)I

    move-result p1

    return p1
.end method
