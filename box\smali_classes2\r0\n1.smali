.class public final Lr0/n1;
.super Ljava/lang/Object;

# interfaces
.implements Lr0/o1;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lr0/n1$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation


# static fields
.field public static final a:Lr0/n1$a;

.field public static b:Z

.field public static c:Ljava/lang/reflect/Constructor;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/lang/reflect/Constructor<",
            "Landroid/text/StaticLayout;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lr0/n1$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lr0/n1$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lr0/n1;->a:Lr0/n1$a;

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static final synthetic c()Ljava/lang/reflect/Constructor;
    .locals 1

    sget-object v0, Lr0/n1;->c:Ljava/lang/reflect/Constructor;

    return-object v0
.end method

.method public static final synthetic d()Z
    .locals 1

    sget-boolean v0, Lr0/n1;->b:Z

    return v0
.end method

.method public static final synthetic e(Z)V
    .locals 0

    sput-boolean p0, Lr0/n1;->b:Z

    return-void
.end method

.method public static final synthetic f(Ljava/lang/reflect/Constructor;)V
    .locals 0

    sput-object p0, Lr0/n1;->c:Ljava/lang/reflect/Constructor;

    return-void
.end method


# virtual methods
.method public a(Lr0/p1;)Landroid/text/StaticLayout;
    .locals 16

    const-string v0, "unable to call constructor"

    const-string v1, "StaticLayoutFactory"

    sget-object v2, Lr0/n1;->a:Lr0/n1$a;

    invoke-static {v2}, Lr0/n1$a;->a(Lr0/n1$a;)Ljava/lang/reflect/Constructor;

    move-result-object v2

    const/4 v3, 0x0

    if-eqz v2, :cond_0

    const/16 v4, 0xd

    :try_start_0
    new-array v4, v4, [Ljava/lang/Object;

    invoke-virtual/range {p1 .. p1}, Lr0/p1;->r()Ljava/lang/CharSequence;

    move-result-object v5

    const/4 v6, 0x0

    aput-object v5, v4, v6

    invoke-virtual/range {p1 .. p1}, Lr0/p1;->q()I

    move-result v5

    invoke-static {v5}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v5

    const/4 v6, 0x1

    aput-object v5, v4, v6

    invoke-virtual/range {p1 .. p1}, Lr0/p1;->e()I

    move-result v5

    invoke-static {v5}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v5

    const/4 v6, 0x2

    aput-object v5, v4, v6

    invoke-virtual/range {p1 .. p1}, Lr0/p1;->o()Landroid/text/TextPaint;

    move-result-object v5

    const/4 v6, 0x3

    aput-object v5, v4, v6

    invoke-virtual/range {p1 .. p1}, Lr0/p1;->u()I

    move-result v5

    invoke-static {v5}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v5

    const/4 v6, 0x4

    aput-object v5, v4, v6

    invoke-virtual/range {p1 .. p1}, Lr0/p1;->a()Landroid/text/Layout$Alignment;

    move-result-object v5

    const/4 v6, 0x5

    aput-object v5, v4, v6

    invoke-virtual/range {p1 .. p1}, Lr0/p1;->s()Landroid/text/TextDirectionHeuristic;

    move-result-object v5

    const/4 v6, 0x6

    aput-object v5, v4, v6

    invoke-virtual/range {p1 .. p1}, Lr0/p1;->m()F

    move-result v5

    invoke-static {v5}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    move-result-object v5

    const/4 v6, 0x7

    aput-object v5, v4, v6

    invoke-virtual/range {p1 .. p1}, Lr0/p1;->l()F

    move-result v5

    invoke-static {v5}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    move-result-object v5

    const/16 v6, 0x8

    aput-object v5, v4, v6

    invoke-virtual/range {p1 .. p1}, Lr0/p1;->g()Z

    move-result v5

    invoke-static {v5}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v5

    const/16 v6, 0x9

    aput-object v5, v4, v6

    invoke-virtual/range {p1 .. p1}, Lr0/p1;->c()Landroid/text/TextUtils$TruncateAt;

    move-result-object v5

    const/16 v6, 0xa

    aput-object v5, v4, v6

    invoke-virtual/range {p1 .. p1}, Lr0/p1;->d()I

    move-result v5

    invoke-static {v5}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v5

    const/16 v6, 0xb

    aput-object v5, v4, v6

    invoke-virtual/range {p1 .. p1}, Lr0/p1;->n()I

    move-result v5

    invoke-static {v5}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v5

    const/16 v6, 0xc

    aput-object v5, v4, v6

    invoke-virtual {v2, v4}, Ljava/lang/reflect/Constructor;->newInstance([Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Landroid/text/StaticLayout;
    :try_end_0
    .catch Ljava/lang/IllegalAccessException; {:try_start_0 .. :try_end_0} :catch_2
    .catch Ljava/lang/InstantiationException; {:try_start_0 .. :try_end_0} :catch_1
    .catch Ljava/lang/reflect/InvocationTargetException; {:try_start_0 .. :try_end_0} :catch_0

    move-object v3, v2

    goto :goto_0

    :catch_0
    sput-object v3, Lr0/n1;->c:Ljava/lang/reflect/Constructor;

    invoke-static {v1, v0}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    goto :goto_0

    :catch_1
    sput-object v3, Lr0/n1;->c:Ljava/lang/reflect/Constructor;

    invoke-static {v1, v0}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    goto :goto_0

    :catch_2
    sput-object v3, Lr0/n1;->c:Ljava/lang/reflect/Constructor;

    invoke-static {v1, v0}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    :cond_0
    :goto_0
    if-eqz v3, :cond_1

    return-object v3

    :cond_1
    new-instance v0, Landroid/text/StaticLayout;

    invoke-virtual/range {p1 .. p1}, Lr0/p1;->r()Ljava/lang/CharSequence;

    move-result-object v5

    invoke-virtual/range {p1 .. p1}, Lr0/p1;->q()I

    move-result v6

    invoke-virtual/range {p1 .. p1}, Lr0/p1;->e()I

    move-result v7

    invoke-virtual/range {p1 .. p1}, Lr0/p1;->o()Landroid/text/TextPaint;

    move-result-object v8

    invoke-virtual/range {p1 .. p1}, Lr0/p1;->u()I

    move-result v9

    invoke-virtual/range {p1 .. p1}, Lr0/p1;->a()Landroid/text/Layout$Alignment;

    move-result-object v10

    invoke-virtual/range {p1 .. p1}, Lr0/p1;->m()F

    move-result v11

    invoke-virtual/range {p1 .. p1}, Lr0/p1;->l()F

    move-result v12

    invoke-virtual/range {p1 .. p1}, Lr0/p1;->g()Z

    move-result v13

    invoke-virtual/range {p1 .. p1}, Lr0/p1;->c()Landroid/text/TextUtils$TruncateAt;

    move-result-object v14

    invoke-virtual/range {p1 .. p1}, Lr0/p1;->d()I

    move-result v15

    move-object v4, v0

    invoke-direct/range {v4 .. v15}, Landroid/text/StaticLayout;-><init>(Ljava/lang/CharSequence;IILandroid/text/TextPaint;ILandroid/text/Layout$Alignment;FFZLandroid/text/TextUtils$TruncateAt;I)V

    return-object v0
.end method

.method public b(Landroid/text/StaticLayout;Z)Z
    .locals 0

    const/4 p1, 0x0

    return p1
.end method
