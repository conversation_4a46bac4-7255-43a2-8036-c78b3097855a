.class public Lr2/g$a;
.super Landroid/os/Handler;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lr2/g;->start()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field public final synthetic a:Lr2/g;


# direct methods
.method public constructor <init>(Lr2/g;Landroid/os/Looper;)V
    .locals 0

    iput-object p1, p0, Lr2/g$a;->a:Lr2/g;

    invoke-direct {p0, p2}, Landroid/os/Handler;-><init>(Landroid/os/Looper;)V

    return-void
.end method


# virtual methods
.method public handleMessage(Landroid/os/Message;)V
    .locals 1

    iget-object v0, p0, Lr2/g$a;->a:Lr2/g;

    invoke-static {v0, p1}, Lr2/g;->d(Lr2/g;Landroid/os/Message;)V

    return-void
.end method
