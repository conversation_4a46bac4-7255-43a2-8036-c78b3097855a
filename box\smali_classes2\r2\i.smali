.class public final synthetic Lr2/i;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/lang/Runnable;


# instance fields
.field public final synthetic a:Lr2/j;


# direct methods
.method public synthetic constructor <init>(Lr2/j;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lr2/i;->a:Lr2/j;

    return-void
.end method


# virtual methods
.method public final run()V
    .locals 1

    iget-object v0, p0, Lr2/i;->a:Lr2/j;

    invoke-static {v0}, Lr2/j;->a(Lr2/j;)V

    return-void
.end method
