.class public final synthetic Lr2/y;
.super Ljava/lang/Object;

# interfaces
.implements Landroidx/media3/exoplayer/mediacodec/MediaCodecUtil$f;


# instance fields
.field public final synthetic a:Landroidx/media3/common/y;


# direct methods
.method public synthetic constructor <init>(Landroidx/media3/common/y;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lr2/y;->a:Landroidx/media3/common/y;

    return-void
.end method


# virtual methods
.method public final a(Ljava/lang/Object;)I
    .locals 1

    iget-object v0, p0, Lr2/y;->a:Landroidx/media3/common/y;

    check-cast p1, Landroidx/media3/exoplayer/mediacodec/d;

    invoke-static {v0, p1}, Landroidx/media3/exoplayer/mediacodec/MediaCodecUtil;->d(Landroidx/media3/common/y;Landroidx/media3/exoplayer/mediacodec/d;)I

    move-result p1

    return p1
.end method
