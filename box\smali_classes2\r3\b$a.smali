.class public final Lr3/b$a;
.super Ljava/lang/Object;

# interfaces
.implements Lr3/g;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lr3/b;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation


# instance fields
.field public a:Lz2/c0;

.field public b:Lz2/c0$a;

.field public c:J

.field public d:J


# direct methods
.method public constructor <init>(Lz2/c0;Lz2/c0$a;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lr3/b$a;->a:Lz2/c0;

    iput-object p2, p0, Lr3/b$a;->b:Lz2/c0$a;

    const-wide/16 p1, -0x1

    iput-wide p1, p0, Lr3/b$a;->c:J

    iput-wide p1, p0, Lr3/b$a;->d:J

    return-void
.end method


# virtual methods
.method public a(Lz2/t;)J
    .locals 6

    iget-wide v0, p0, Lr3/b$a;->d:J

    const-wide/16 v2, 0x0

    const-wide/16 v4, -0x1

    cmp-long p1, v0, v2

    if-ltz p1, :cond_0

    const-wide/16 v2, 0x2

    add-long/2addr v0, v2

    neg-long v0, v0

    iput-wide v4, p0, Lr3/b$a;->d:J

    return-wide v0

    :cond_0
    return-wide v4
.end method

.method public b(J)V
    .locals 0

    iput-wide p1, p0, Lr3/b$a;->c:J

    return-void
.end method

.method public createSeekMap()Lz2/m0;
    .locals 5

    iget-wide v0, p0, Lr3/b$a;->c:J

    const-wide/16 v2, -0x1

    cmp-long v4, v0, v2

    if-eqz v4, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    invoke-static {v0}, Le2/a;->g(Z)V

    new-instance v0, Lz2/b0;

    iget-object v1, p0, Lr3/b$a;->a:Lz2/c0;

    iget-wide v2, p0, Lr3/b$a;->c:J

    invoke-direct {v0, v1, v2, v3}, Lz2/b0;-><init>(Lz2/c0;J)V

    return-object v0
.end method

.method public startSeek(J)V
    .locals 2

    iget-object v0, p0, Lr3/b$a;->b:Lz2/c0$a;

    iget-object v0, v0, Lz2/c0$a;->a:[J

    const/4 v1, 0x1

    invoke-static {v0, p1, p2, v1, v1}, Le2/u0;->h([JJZZ)I

    move-result p1

    aget-wide p1, v0, p1

    iput-wide p1, p0, Lr3/b$a;->d:J

    return-void
.end method
