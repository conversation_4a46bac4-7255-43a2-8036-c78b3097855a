.class public interface abstract Lr3/g;
.super Ljava/lang/Object;


# virtual methods
.method public abstract a(Lz2/t;)J
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation
.end method

.method public abstract createSeekMap()Lz2/m0;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end method

.method public abstract startSeek(J)V
.end method
