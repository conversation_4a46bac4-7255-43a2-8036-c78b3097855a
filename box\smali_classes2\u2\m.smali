.class public abstract Lu2/m;
.super Landroidx/media3/common/m0;


# instance fields
.field public final f:Landroidx/media3/common/m0;


# direct methods
.method public constructor <init>(Landroidx/media3/common/m0;)V
    .locals 0

    invoke-direct {p0}, Landroidx/media3/common/m0;-><init>()V

    iput-object p1, p0, Lu2/m;->f:Landroidx/media3/common/m0;

    return-void
.end method


# virtual methods
.method public a(Z)I
    .locals 1

    iget-object v0, p0, Lu2/m;->f:Landroidx/media3/common/m0;

    invoke-virtual {v0, p1}, Landroidx/media3/common/m0;->a(Z)I

    move-result p1

    return p1
.end method

.method public b(Ljava/lang/Object;)I
    .locals 1

    iget-object v0, p0, Lu2/m;->f:Landroidx/media3/common/m0;

    invoke-virtual {v0, p1}, Landroidx/media3/common/m0;->b(Ljava/lang/Object;)I

    move-result p1

    return p1
.end method

.method public c(Z)I
    .locals 1

    iget-object v0, p0, Lu2/m;->f:Landroidx/media3/common/m0;

    invoke-virtual {v0, p1}, Landroidx/media3/common/m0;->c(Z)I

    move-result p1

    return p1
.end method

.method public e(IIZ)I
    .locals 1

    iget-object v0, p0, Lu2/m;->f:Landroidx/media3/common/m0;

    invoke-virtual {v0, p1, p2, p3}, Landroidx/media3/common/m0;->e(IIZ)I

    move-result p1

    return p1
.end method

.method public g(ILandroidx/media3/common/m0$b;Z)Landroidx/media3/common/m0$b;
    .locals 1

    iget-object v0, p0, Lu2/m;->f:Landroidx/media3/common/m0;

    invoke-virtual {v0, p1, p2, p3}, Landroidx/media3/common/m0;->g(ILandroidx/media3/common/m0$b;Z)Landroidx/media3/common/m0$b;

    move-result-object p1

    return-object p1
.end method

.method public i()I
    .locals 1

    iget-object v0, p0, Lu2/m;->f:Landroidx/media3/common/m0;

    invoke-virtual {v0}, Landroidx/media3/common/m0;->i()I

    move-result v0

    return v0
.end method

.method public l(IIZ)I
    .locals 1

    iget-object v0, p0, Lu2/m;->f:Landroidx/media3/common/m0;

    invoke-virtual {v0, p1, p2, p3}, Landroidx/media3/common/m0;->l(IIZ)I

    move-result p1

    return p1
.end method

.method public m(I)Ljava/lang/Object;
    .locals 1

    iget-object v0, p0, Lu2/m;->f:Landroidx/media3/common/m0;

    invoke-virtual {v0, p1}, Landroidx/media3/common/m0;->m(I)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public o(ILandroidx/media3/common/m0$c;J)Landroidx/media3/common/m0$c;
    .locals 1

    iget-object v0, p0, Lu2/m;->f:Landroidx/media3/common/m0;

    invoke-virtual {v0, p1, p2, p3, p4}, Landroidx/media3/common/m0;->o(ILandroidx/media3/common/m0$c;J)Landroidx/media3/common/m0$c;

    move-result-object p1

    return-object p1
.end method

.method public p()I
    .locals 1

    iget-object v0, p0, Lu2/m;->f:Landroidx/media3/common/m0;

    invoke-virtual {v0}, Landroidx/media3/common/m0;->p()I

    move-result v0

    return v0
.end method
