.class public interface abstract Lv2/h$b;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lv2/h;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "b"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "<T::",
        "Lv2/i;",
        ">",
        "Ljava/lang/Object;"
    }
.end annotation


# virtual methods
.method public abstract c(Lv2/h;)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lv2/h<",
            "TT;>;)V"
        }
    .end annotation
.end method
