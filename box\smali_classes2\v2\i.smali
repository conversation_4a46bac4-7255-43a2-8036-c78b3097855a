.class public interface abstract Lv2/i;
.super Ljava/lang/Object;


# virtual methods
.method public abstract b(JLandroidx/media3/exoplayer/b3;)J
.end method

.method public abstract c(Lv2/e;)V
.end method

.method public abstract d(Landroidx/media3/exoplayer/w1;JLjava/util/List;Lv2/g;)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroidx/media3/exoplayer/w1;",
            "J",
            "Ljava/util/List<",
            "+",
            "Lv2/m;",
            ">;",
            "Lv2/g;",
            ")V"
        }
    .end annotation
.end method

.method public abstract e(Lv2/e;ZLandroidx/media3/exoplayer/upstream/m$c;Landroidx/media3/exoplayer/upstream/m;)Z
.end method

.method public abstract f(JLv2/e;Ljava/util/List;)Z
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(J",
            "Lv2/e;",
            "Ljava/util/List<",
            "+",
            "Lv2/m;",
            ">;)Z"
        }
    .end annotation
.end method

.method public abstract getPreferredQueueSize(JLjava/util/List;)I
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(J",
            "Ljava/util/List<",
            "+",
            "Lv2/m;",
            ">;)I"
        }
    .end annotation
.end method

.method public abstract maybeThrowError()V
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation
.end method

.method public abstract release()V
.end method
