.class public interface abstract Lv2/n;
.super Ljava/lang/Object;


# static fields
.field public static final a:Lv2/n;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    new-instance v0, Lv2/n$a;

    invoke-direct {v0}, Lv2/n$a;-><init>()V

    sput-object v0, Lv2/n;->a:Lv2/n;

    return-void
.end method


# virtual methods
.method public abstract a()J
.end method

.method public abstract b()J
.end method

.method public abstract next()Z
.end method
