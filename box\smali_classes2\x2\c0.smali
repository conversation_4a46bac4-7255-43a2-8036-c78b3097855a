.class public interface abstract Lx2/c0;
.super Ljava/lang/Object;


# virtual methods
.method public abstract e(Landroidx/media3/common/y;)I
.end method

.method public abstract getFormat(I)Landroidx/media3/common/y;
.end method

.method public abstract getIndexInTrackGroup(I)I
.end method

.method public abstract getTrackGroup()Landroidx/media3/common/n0;
.end method

.method public abstract indexOf(I)I
.end method

.method public abstract length()I
.end method
