.class public interface abstract Lx2/z;
.super Ljava/lang/Object;

# interfaces
.implements Lx2/c0;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lx2/z$b;,
        Lx2/z$a;
    }
.end annotation


# virtual methods
.method public abstract a()V
.end method

.method public abstract b(Z)V
.end method

.method public abstract c()V
.end method

.method public abstract d(IJ)Z
.end method

.method public abstract disable()V
.end method

.method public abstract enable()V
.end method

.method public abstract evaluateQueueSize(JLjava/util/List;)I
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(J",
            "Ljava/util/List<",
            "+",
            "Lv2/m;",
            ">;)I"
        }
    .end annotation
.end method

.method public abstract f(JLv2/e;Ljava/util/List;)Z
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(J",
            "Lv2/e;",
            "Ljava/util/List<",
            "+",
            "Lv2/m;",
            ">;)Z"
        }
    .end annotation
.end method

.method public abstract g(JJJLjava/util/List;[Lv2/n;)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(JJJ",
            "Ljava/util/List<",
            "+",
            "Lv2/m;",
            ">;[",
            "Lv2/n;",
            ")V"
        }
    .end annotation
.end method

.method public abstract getSelectedFormat()Landroidx/media3/common/y;
.end method

.method public abstract getSelectedIndex()I
.end method

.method public abstract getSelectedIndexInTrackGroup()I
.end method

.method public abstract getSelectionData()Ljava/lang/Object;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end method

.method public abstract getSelectionReason()I
.end method

.method public abstract h(IJ)Z
.end method

.method public abstract onPlaybackSpeed(F)V
.end method
