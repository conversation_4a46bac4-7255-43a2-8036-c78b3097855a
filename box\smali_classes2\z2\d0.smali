.class public Lz2/d0;
.super Ljava/lang/Object;

# interfaces
.implements Lz2/t;


# instance fields
.field public final a:Lz2/t;


# direct methods
.method public constructor <init>(Lz2/t;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lz2/d0;->a:Lz2/t;

    return-void
.end method


# virtual methods
.method public advancePeekPosition(I)V
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget-object v0, p0, Lz2/d0;->a:Lz2/t;

    invoke-interface {v0, p1}, Lz2/t;->advancePeekPosition(I)V

    return-void
.end method

.method public advancePeekPosition(IZ)Z
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget-object v0, p0, Lz2/d0;->a:Lz2/t;

    invoke-interface {v0, p1, p2}, Lz2/t;->advancePeekPosition(IZ)Z

    move-result p1

    return p1
.end method

.method public b([BII)I
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget-object v0, p0, Lz2/d0;->a:Lz2/t;

    invoke-interface {v0, p1, p2, p3}, Lz2/t;->b([BII)I

    move-result p1

    return p1
.end method

.method public getLength()J
    .locals 2

    iget-object v0, p0, Lz2/d0;->a:Lz2/t;

    invoke-interface {v0}, Lz2/t;->getLength()J

    move-result-wide v0

    return-wide v0
.end method

.method public getPeekPosition()J
    .locals 2

    iget-object v0, p0, Lz2/d0;->a:Lz2/t;

    invoke-interface {v0}, Lz2/t;->getPeekPosition()J

    move-result-wide v0

    return-wide v0
.end method

.method public getPosition()J
    .locals 2

    iget-object v0, p0, Lz2/d0;->a:Lz2/t;

    invoke-interface {v0}, Lz2/t;->getPosition()J

    move-result-wide v0

    return-wide v0
.end method

.method public peekFully([BII)V
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget-object v0, p0, Lz2/d0;->a:Lz2/t;

    invoke-interface {v0, p1, p2, p3}, Lz2/t;->peekFully([BII)V

    return-void
.end method

.method public peekFully([BIIZ)Z
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget-object v0, p0, Lz2/d0;->a:Lz2/t;

    invoke-interface {v0, p1, p2, p3, p4}, Lz2/t;->peekFully([BIIZ)Z

    move-result p1

    return p1
.end method

.method public read([BII)I
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget-object v0, p0, Lz2/d0;->a:Lz2/t;

    invoke-interface {v0, p1, p2, p3}, Lz2/t;->read([BII)I

    move-result p1

    return p1
.end method

.method public readFully([BII)V
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget-object v0, p0, Lz2/d0;->a:Lz2/t;

    invoke-interface {v0, p1, p2, p3}, Lz2/t;->readFully([BII)V

    return-void
.end method

.method public readFully([BIIZ)Z
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget-object v0, p0, Lz2/d0;->a:Lz2/t;

    invoke-interface {v0, p1, p2, p3, p4}, Lz2/t;->readFully([BIIZ)Z

    move-result p1

    return p1
.end method

.method public resetPeekPosition()V
    .locals 1

    iget-object v0, p0, Lz2/d0;->a:Lz2/t;

    invoke-interface {v0}, Lz2/t;->resetPeekPosition()V

    return-void
.end method

.method public skip(I)I
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget-object v0, p0, Lz2/d0;->a:Lz2/t;

    invoke-interface {v0, p1}, Lz2/t;->skip(I)I

    move-result p1

    return p1
.end method

.method public skipFully(I)V
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget-object v0, p0, Lz2/d0;->a:Lz2/t;

    invoke-interface {v0, p1}, Lz2/t;->skipFully(I)V

    return-void
.end method
