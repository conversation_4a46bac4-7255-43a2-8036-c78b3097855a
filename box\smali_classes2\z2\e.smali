.class public abstract Lz2/e;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lz2/e$f;,
        Lz2/e$a;,
        Lz2/e$d;,
        Lz2/e$c;,
        Lz2/e$e;,
        Lz2/e$b;
    }
.end annotation


# instance fields
.field public final a:Lz2/e$a;

.field public final b:Lz2/e$f;

.field public c:Lz2/e$c;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public final d:I


# direct methods
.method public constructor <init>(Lz2/e$d;Lz2/e$f;JJJJJJI)V
    .locals 16

    move-object/from16 v0, p0

    invoke-direct/range {p0 .. p0}, Ljava/lang/Object;-><init>()V

    move-object/from16 v1, p2

    iput-object v1, v0, Lz2/e;->b:Lz2/e$f;

    move/from16 v1, p15

    iput v1, v0, Lz2/e;->d:I

    new-instance v15, Lz2/e$a;

    move-object v1, v15

    move-object/from16 v2, p1

    move-wide/from16 v3, p3

    move-wide/from16 v5, p5

    move-wide/from16 v7, p7

    move-wide/from16 v9, p9

    move-wide/from16 v11, p11

    move-wide/from16 v13, p13

    invoke-direct/range {v1 .. v14}, Lz2/e$a;-><init>(Lz2/e$d;JJJJJJ)V

    iput-object v15, v0, Lz2/e;->a:Lz2/e$a;

    return-void
.end method


# virtual methods
.method public a(J)Lz2/e$c;
    .locals 17

    move-object/from16 v0, p0

    new-instance v16, Lz2/e$c;

    iget-object v1, v0, Lz2/e;->a:Lz2/e$a;

    move-wide/from16 v2, p1

    invoke-virtual {v1, v2, v3}, Lz2/e$a;->h(J)J

    move-result-wide v4

    iget-object v1, v0, Lz2/e;->a:Lz2/e$a;

    invoke-static {v1}, Lz2/e$a;->b(Lz2/e$a;)J

    move-result-wide v6

    iget-object v1, v0, Lz2/e;->a:Lz2/e$a;

    invoke-static {v1}, Lz2/e$a;->c(Lz2/e$a;)J

    move-result-wide v8

    iget-object v1, v0, Lz2/e;->a:Lz2/e$a;

    invoke-static {v1}, Lz2/e$a;->d(Lz2/e$a;)J

    move-result-wide v10

    iget-object v1, v0, Lz2/e;->a:Lz2/e$a;

    invoke-static {v1}, Lz2/e$a;->e(Lz2/e$a;)J

    move-result-wide v12

    iget-object v1, v0, Lz2/e;->a:Lz2/e$a;

    invoke-static {v1}, Lz2/e$a;->g(Lz2/e$a;)J

    move-result-wide v14

    move-object/from16 v1, v16

    invoke-direct/range {v1 .. v15}, Lz2/e$c;-><init>(JJJJJJJ)V

    return-object v16
.end method

.method public final b()Lz2/m0;
    .locals 1

    iget-object v0, p0, Lz2/e;->a:Lz2/e$a;

    return-object v0
.end method

.method public c(Lz2/t;Lz2/l0;)I
    .locals 11
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    :goto_0
    iget-object v0, p0, Lz2/e;->c:Lz2/e$c;

    invoke-static {v0}, Le2/a;->i(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lz2/e$c;

    invoke-static {v0}, Lz2/e$c;->b(Lz2/e$c;)J

    move-result-wide v1

    invoke-static {v0}, Lz2/e$c;->c(Lz2/e$c;)J

    move-result-wide v3

    invoke-static {v0}, Lz2/e$c;->d(Lz2/e$c;)J

    move-result-wide v5

    sub-long/2addr v3, v1

    iget v7, p0, Lz2/e;->d:I

    int-to-long v7, v7

    const/4 v9, 0x0

    cmp-long v10, v3, v7

    if-gtz v10, :cond_0

    invoke-virtual {p0, v9, v1, v2}, Lz2/e;->e(ZJ)V

    invoke-virtual {p0, p1, v1, v2, p2}, Lz2/e;->g(Lz2/t;JLz2/l0;)I

    move-result p1

    return p1

    :cond_0
    invoke-virtual {p0, p1, v5, v6}, Lz2/e;->i(Lz2/t;J)Z

    move-result v1

    if-nez v1, :cond_1

    invoke-virtual {p0, p1, v5, v6, p2}, Lz2/e;->g(Lz2/t;JLz2/l0;)I

    move-result p1

    return p1

    :cond_1
    invoke-interface {p1}, Lz2/t;->resetPeekPosition()V

    iget-object v1, p0, Lz2/e;->b:Lz2/e$f;

    invoke-static {v0}, Lz2/e$c;->e(Lz2/e$c;)J

    move-result-wide v2

    invoke-interface {v1, p1, v2, v3}, Lz2/e$f;->b(Lz2/t;J)Lz2/e$e;

    move-result-object v1

    invoke-static {v1}, Lz2/e$e;->a(Lz2/e$e;)I

    move-result v2

    const/4 v3, -0x3

    if-eq v2, v3, :cond_5

    const/4 v3, -0x2

    if-eq v2, v3, :cond_4

    const/4 v3, -0x1

    if-eq v2, v3, :cond_3

    if-nez v2, :cond_2

    invoke-static {v1}, Lz2/e$e;->c(Lz2/e$e;)J

    move-result-wide v2

    invoke-virtual {p0, p1, v2, v3}, Lz2/e;->i(Lz2/t;J)Z

    const/4 v0, 0x1

    invoke-static {v1}, Lz2/e$e;->c(Lz2/e$e;)J

    move-result-wide v2

    invoke-virtual {p0, v0, v2, v3}, Lz2/e;->e(ZJ)V

    invoke-static {v1}, Lz2/e$e;->c(Lz2/e$e;)J

    move-result-wide v0

    invoke-virtual {p0, p1, v0, v1, p2}, Lz2/e;->g(Lz2/t;JLz2/l0;)I

    move-result p1

    return p1

    :cond_2
    new-instance p1, Ljava/lang/IllegalStateException;

    const-string p2, "Invalid case"

    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw p1

    :cond_3
    invoke-static {v1}, Lz2/e$e;->b(Lz2/e$e;)J

    move-result-wide v2

    invoke-static {v1}, Lz2/e$e;->c(Lz2/e$e;)J

    move-result-wide v4

    invoke-static {v0, v2, v3, v4, v5}, Lz2/e$c;->f(Lz2/e$c;JJ)V

    goto :goto_0

    :cond_4
    invoke-static {v1}, Lz2/e$e;->b(Lz2/e$e;)J

    move-result-wide v2

    invoke-static {v1}, Lz2/e$e;->c(Lz2/e$e;)J

    move-result-wide v4

    invoke-static {v0, v2, v3, v4, v5}, Lz2/e$c;->g(Lz2/e$c;JJ)V

    goto/16 :goto_0

    :cond_5
    invoke-virtual {p0, v9, v5, v6}, Lz2/e;->e(ZJ)V

    invoke-virtual {p0, p1, v5, v6, p2}, Lz2/e;->g(Lz2/t;JLz2/l0;)I

    move-result p1

    return p1
.end method

.method public final d()Z
    .locals 1

    iget-object v0, p0, Lz2/e;->c:Lz2/e$c;

    if-eqz v0, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method public final e(ZJ)V
    .locals 1

    const/4 v0, 0x0

    iput-object v0, p0, Lz2/e;->c:Lz2/e$c;

    iget-object v0, p0, Lz2/e;->b:Lz2/e$f;

    invoke-interface {v0}, Lz2/e$f;->a()V

    invoke-virtual {p0, p1, p2, p3}, Lz2/e;->f(ZJ)V

    return-void
.end method

.method public f(ZJ)V
    .locals 0

    return-void
.end method

.method public final g(Lz2/t;JLz2/l0;)I
    .locals 2

    invoke-interface {p1}, Lz2/t;->getPosition()J

    move-result-wide v0

    cmp-long p1, p2, v0

    if-nez p1, :cond_0

    const/4 p1, 0x0

    return p1

    :cond_0
    iput-wide p2, p4, Lz2/l0;->a:J

    const/4 p1, 0x1

    return p1
.end method

.method public final h(J)V
    .locals 3

    iget-object v0, p0, Lz2/e;->c:Lz2/e$c;

    if-eqz v0, :cond_0

    invoke-static {v0}, Lz2/e$c;->a(Lz2/e$c;)J

    move-result-wide v0

    cmp-long v2, v0, p1

    if-nez v2, :cond_0

    return-void

    :cond_0
    invoke-virtual {p0, p1, p2}, Lz2/e;->a(J)Lz2/e$c;

    move-result-object p1

    iput-object p1, p0, Lz2/e;->c:Lz2/e$c;

    return-void
.end method

.method public final i(Lz2/t;J)Z
    .locals 3
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    invoke-interface {p1}, Lz2/t;->getPosition()J

    move-result-wide v0

    sub-long/2addr p2, v0

    const-wide/16 v0, 0x0

    cmp-long v2, p2, v0

    if-ltz v2, :cond_0

    const-wide/32 v0, 0x40000

    cmp-long v2, p2, v0

    if-gtz v2, :cond_0

    long-to-int p3, p2

    invoke-interface {p1, p3}, Lz2/t;->skipFully(I)V

    const/4 p1, 0x1

    return p1

    :cond_0
    const/4 p1, 0x0

    return p1
.end method
