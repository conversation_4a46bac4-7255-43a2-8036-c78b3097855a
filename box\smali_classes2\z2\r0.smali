.class public interface abstract Lz2/r0;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lz2/r0$a;
    }
.end annotation


# virtual methods
.method public abstract a(Landroidx/media3/common/y;)V
.end method

.method public abstract b(Le2/c0;II)V
.end method

.method public abstract c(Landroidx/media3/common/l;IZ)I
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation
.end method

.method public abstract d(Landroidx/media3/common/l;IZI)I
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation
.end method

.method public abstract e(JIIILz2/r0$a;)V
    .param p6    # Lz2/r0$a;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
.end method

.method public abstract f(Le2/c0;I)V
.end method
