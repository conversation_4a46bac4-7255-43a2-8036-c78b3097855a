.class public interface abstract Lz2/u;
.super Ljava/lang/Object;


# static fields
.field public static final G0:Lz2/u;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    new-instance v0, Lz2/u$a;

    invoke-direct {v0}, Lz2/u$a;-><init>()V

    sput-object v0, Lz2/u;->G0:Lz2/u;

    return-void
.end method


# virtual methods
.method public abstract endTracks()V
.end method

.method public abstract g(Lz2/m0;)V
.end method

.method public abstract track(II)Lz2/r0;
.end method
