.class public Lac/e;
.super Ljava/lang/Object;


# static fields
.field public static final b:Lac/e;


# instance fields
.field public a:Lac/d;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 1

    new-instance v0, Lac/e;

    invoke-direct {v0}, Lac/e;-><init>()V

    sput-object v0, Lac/e;->b:Lac/e;

    return-void
.end method

.method public constructor <init>()V
    .locals 1

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, 0x0

    iput-object v0, p0, Lac/e;->a:Lac/d;

    return-void
.end method

.method public static a(Landroid/content/Context;)Lac/d;
    .locals 1
    .param p0    # Landroid/content/Context;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    sget-object v0, Lac/e;->b:<PERSON>/e;

    invoke-virtual {v0, p0}, Lac/e;->b(Landroid/content/Context;)Lac/d;

    move-result-object p0

    return-object p0
.end method


# virtual methods
.method public final declared-synchronized b(Landroid/content/Context;)Lac/d;
    .locals 1
    .param p1    # Landroid/content/Context;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    monitor-enter p0

    :try_start_0
    iget-object v0, p0, Lac/e;->a:Lac/d;

    if-nez v0, :cond_1

    invoke-virtual {p1}, Landroid/content/Context;->getApplicationContext()Landroid/content/Context;

    move-result-object v0

    if-eqz v0, :cond_0

    invoke-virtual {p1}, Landroid/content/Context;->getApplicationContext()Landroid/content/Context;

    move-result-object p1

    goto :goto_0

    :catchall_0
    move-exception p1

    goto :goto_1

    :cond_0
    :goto_0
    new-instance v0, Lac/d;

    invoke-direct {v0, p1}, Lac/d;-><init>(Landroid/content/Context;)V

    iput-object v0, p0, Lac/e;->a:Lac/d;

    :cond_1
    iget-object p1, p0, Lac/e;->a:Lac/d;
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    monitor-exit p0

    return-object p1

    :goto_1
    monitor-exit p0

    throw p1
.end method
