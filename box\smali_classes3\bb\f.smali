.class public final Lbb/f;
.super Landroid/animation/AnimatorListenerAdapter;


# instance fields
.field public final synthetic a:Lcom/google/android/gms/ads/internal/overlay/zzs;


# direct methods
.method public constructor <init>(Lcom/google/android/gms/ads/internal/overlay/zzs;)V
    .locals 0

    iput-object p1, p0, Lbb/f;->a:Lcom/google/android/gms/ads/internal/overlay/zzs;

    invoke-direct {p0}, Landroid/animation/AnimatorListenerAdapter;-><init>()V

    return-void
.end method


# virtual methods
.method public final a(Z)V
    .locals 1

    iget-object v0, p0, Lbb/f;->a:Lcom/google/android/gms/ads/internal/overlay/zzs;

    invoke-virtual {v0, p1}, Landroid/view/View;->setEnabled(Z)V

    iget-object v0, p0, Lbb/f;->a:Lcom/google/android/gms/ads/internal/overlay/zzs;

    invoke-static {v0}, Lcom/google/android/gms/ads/internal/overlay/zzs;->a(Lcom/google/android/gms/ads/internal/overlay/zzs;)Landroid/widget/ImageButton;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/view/View;->setEnabled(Z)V

    return-void
.end method

.method public final onAnimationCancel(Landroid/animation/Animator;)V
    .locals 0

    const/4 p1, 0x1

    invoke-virtual {p0, p1}, Lbb/f;->a(Z)V

    return-void
.end method

.method public final onAnimationEnd(Landroid/animation/Animator;)V
    .locals 0

    const/4 p1, 0x1

    invoke-virtual {p0, p1}, Lbb/f;->a(Z)V

    return-void
.end method

.method public final onAnimationStart(Landroid/animation/Animator;)V
    .locals 0

    const/4 p1, 0x0

    invoke-virtual {p0, p1}, Lbb/f;->a(Z)V

    return-void
.end method
