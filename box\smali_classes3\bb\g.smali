.class public final Lbb/g;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/google/android/gms/internal/ads/dz2;


# instance fields
.field public final synthetic a:Lcom/google/android/gms/ads/internal/overlay/zzx;


# direct methods
.method public constructor <init>(Lcom/google/android/gms/ads/internal/overlay/zzx;)V
    .locals 0

    iput-object p1, p0, Lbb/g;->a:Lcom/google/android/gms/ads/internal/overlay/zzx;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final a(Lcom/google/android/gms/internal/ads/cz2;)V
    .locals 1

    iget-object v0, p0, Lbb/g;->a:Lcom/google/android/gms/ads/internal/overlay/zzx;

    invoke-virtual {v0, p1}, Lcom/google/android/gms/ads/internal/overlay/zzx;->e(Lcom/google/android/gms/internal/ads/cz2;)V

    return-void
.end method
