.class public abstract Lbc/b$a;
.super Lcom/google/android/gms/internal/common/i;

# interfaces
.implements Lbc/b;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lbc/b;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x409
    name = "a"
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 1

    const-string v0, "com.google.android.gms.dynamic.IObjectWrapper"

    invoke-direct {p0, v0}, Lcom/google/android/gms/internal/common/i;-><init>(Ljava/lang/String;)V

    return-void
.end method

.method public static D(Landroid/os/IBinder;)Lbc/b;
    .locals 2
    .param p0    # Landroid/os/IBinder;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    if-nez p0, :cond_0

    const/4 p0, 0x0

    return-object p0

    :cond_0
    const-string v0, "com.google.android.gms.dynamic.IObjectWrapper"

    invoke-interface {p0, v0}, Landroid/os/IBinder;->queryLocalInterface(Ljava/lang/String;)Landroid/os/IInterface;

    move-result-object v0

    instance-of v1, v0, Lbc/b;

    if-eqz v1, :cond_1

    check-cast v0, Lbc/b;

    return-object v0

    :cond_1
    new-instance v0, Lbc/n;

    invoke-direct {v0, p0}, Lbc/n;-><init>(Landroid/os/IBinder;)V

    return-object v0
.end method
