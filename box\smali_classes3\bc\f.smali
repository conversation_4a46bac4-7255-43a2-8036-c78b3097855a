.class public final Lbc/f;
.super Ljava/lang/Object;

# interfaces
.implements Lbc/e;


# instance fields
.field public final synthetic a:Lbc/a;


# direct methods
.method public constructor <init>(Lbc/a;)V
    .locals 0

    iput-object p1, p0, Lbc/f;->a:Lbc/a;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final a(Lbc/c;)V
    .locals 2

    iget-object v0, p0, Lbc/f;->a:Lbc/a;

    invoke-static {v0, p1}, Lbc/a;->r(Lbc/a;Lbc/c;)V

    iget-object p1, p0, Lbc/f;->a:Lbc/a;

    invoke-static {p1}, Lbc/a;->q(Lbc/a;)Ljava/util/LinkedList;

    move-result-object p1

    invoke-virtual {p1}, Ljava/util/AbstractCollection;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_0

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lbc/m;

    iget-object v1, p0, Lbc/f;->a:Lbc/a;

    invoke-static {v1}, Lbc/a;->p(Lbc/a;)Lbc/c;

    move-result-object v1

    invoke-interface {v0, v1}, Lbc/m;->a(Lbc/c;)V

    goto :goto_0

    :cond_0
    iget-object p1, p0, Lbc/f;->a:Lbc/a;

    invoke-static {p1}, Lbc/a;->q(Lbc/a;)Ljava/util/LinkedList;

    move-result-object p1

    invoke-virtual {p1}, Ljava/util/LinkedList;->clear()V

    iget-object p1, p0, Lbc/f;->a:Lbc/a;

    const/4 v0, 0x0

    invoke-static {p1, v0}, Lbc/a;->s(Lbc/a;Landroid/os/Bundle;)V

    return-void
.end method
