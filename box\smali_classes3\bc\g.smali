.class public final Lbc/g;
.super Ljava/lang/Object;

# interfaces
.implements Lbc/m;


# instance fields
.field public final synthetic a:Landroid/app/Activity;

.field public final synthetic b:Landroid/os/Bundle;

.field public final synthetic c:Landroid/os/Bundle;

.field public final synthetic d:Lbc/a;


# direct methods
.method public constructor <init>(Lbc/a;Landroid/app/Activity;Landroid/os/Bundle;Landroid/os/Bundle;)V
    .locals 0

    iput-object p1, p0, Lbc/g;->d:Lbc/a;

    iput-object p2, p0, Lbc/g;->a:Landroid/app/Activity;

    iput-object p3, p0, Lbc/g;->b:Landroid/os/Bundle;

    iput-object p4, p0, Lbc/g;->c:Landroid/os/Bundle;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final a(Lbc/c;)V
    .locals 3

    iget-object p1, p0, Lbc/g;->d:Lbc/a;

    invoke-static {p1}, Lbc/a;->p(Lbc/a;)Lbc/c;

    move-result-object p1

    iget-object v0, p0, Lbc/g;->a:Landroid/app/Activity;

    iget-object v1, p0, Lbc/g;->b:Landroid/os/Bundle;

    iget-object v2, p0, Lbc/g;->c:Landroid/os/Bundle;

    invoke-interface {p1, v0, v1, v2}, Lbc/c;->j(Landroid/app/Activity;Landroid/os/Bundle;Landroid/os/Bundle;)V

    return-void
.end method

.method public final b()I
    .locals 1

    const/4 v0, 0x0

    return v0
.end method
