.class public final Lbc/h;
.super Ljava/lang/Object;

# interfaces
.implements Lbc/m;


# instance fields
.field public final synthetic a:Landroid/os/Bundle;

.field public final synthetic b:Lbc/a;


# direct methods
.method public constructor <init>(Lbc/a;Landroid/os/Bundle;)V
    .locals 0

    iput-object p1, p0, Lbc/h;->b:Lbc/a;

    iput-object p2, p0, Lbc/h;->a:Landroid/os/Bundle;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final a(Lbc/c;)V
    .locals 1

    iget-object p1, p0, Lbc/h;->b:Lbc/a;

    invoke-static {p1}, Lbc/a;->p(Lbc/a;)Lbc/c;

    move-result-object p1

    iget-object v0, p0, Lbc/h;->a:Landroid/os/Bundle;

    invoke-interface {p1, v0}, Lbc/c;->onCreate(Landroid/os/Bundle;)V

    return-void
.end method

.method public final b()I
    .locals 1

    const/4 v0, 0x1

    return v0
.end method
