.class public final Lbc/i;
.super Ljava/lang/Object;

# interfaces
.implements Lbc/m;


# instance fields
.field public final synthetic a:Landroid/widget/FrameLayout;

.field public final synthetic b:Landroid/view/LayoutInflater;

.field public final synthetic c:Landroid/view/ViewGroup;

.field public final synthetic d:Landroid/os/Bundle;

.field public final synthetic e:Lbc/a;


# direct methods
.method public constructor <init>(Lbc/a;Landroid/widget/FrameLayout;Landroid/view/LayoutInflater;Landroid/view/ViewGroup;Landroid/os/Bundle;)V
    .locals 0

    iput-object p1, p0, Lbc/i;->e:Lbc/a;

    iput-object p2, p0, Lbc/i;->a:Landroid/widget/FrameLayout;

    iput-object p3, p0, Lbc/i;->b:Landroid/view/LayoutInflater;

    iput-object p4, p0, Lbc/i;->c:Landroid/view/ViewGroup;

    iput-object p5, p0, Lbc/i;->d:Landroid/os/Bundle;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final a(Lbc/c;)V
    .locals 3

    iget-object p1, p0, Lbc/i;->a:Landroid/widget/FrameLayout;

    invoke-virtual {p1}, Landroid/view/ViewGroup;->removeAllViews()V

    iget-object p1, p0, Lbc/i;->e:Lbc/a;

    invoke-static {p1}, Lbc/a;->p(Lbc/a;)Lbc/c;

    move-result-object p1

    iget-object v0, p0, Lbc/i;->b:Landroid/view/LayoutInflater;

    iget-object v1, p0, Lbc/i;->c:Landroid/view/ViewGroup;

    iget-object v2, p0, Lbc/i;->d:Landroid/os/Bundle;

    invoke-interface {p1, v0, v1, v2}, Lbc/c;->k(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;Landroid/os/Bundle;)Landroid/view/View;

    move-result-object p1

    iget-object v0, p0, Lbc/i;->a:Landroid/widget/FrameLayout;

    invoke-virtual {v0, p1}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    return-void
.end method

.method public final b()I
    .locals 1

    const/4 v0, 0x2

    return v0
.end method
