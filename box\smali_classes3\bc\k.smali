.class public final Lbc/k;
.super Ljava/lang/Object;

# interfaces
.implements Lbc/m;


# instance fields
.field public final synthetic a:Lbc/a;


# direct methods
.method public constructor <init>(Lbc/a;)V
    .locals 0

    iput-object p1, p0, Lbc/k;->a:Lbc/a;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final a(Lbc/c;)V
    .locals 0

    iget-object p1, p0, Lbc/k;->a:Lbc/a;

    invoke-static {p1}, Lbc/a;->p(Lbc/a;)Lbc/c;

    move-result-object p1

    invoke-interface {p1}, Lbc/c;->onStart()V

    return-void
.end method

.method public final b()I
    .locals 1

    const/4 v0, 0x4

    return v0
.end method
