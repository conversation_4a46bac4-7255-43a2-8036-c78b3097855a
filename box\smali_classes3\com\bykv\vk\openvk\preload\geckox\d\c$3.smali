.class final Lcom/bykv/vk/openvk/preload/geckox/d/c$3;
.super Lcom/bykv/vk/openvk/preload/a/c/a;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bykv/vk/openvk/preload/geckox/d/c;->a(Ljava/util/Map;)Ljava/util/Map;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lcom/bykv/vk/openvk/preload/a/c/a<",
        "Lcom/bykv/vk/openvk/preload/geckox/model/Response<",
        "Lcom/bykv/vk/openvk/preload/geckox/model/ComponentModel;",
        ">;>;"
    }
.end annotation


# instance fields
.field final synthetic a:Lcom/bykv/vk/openvk/preload/geckox/d/c;


# direct methods
.method public constructor <init>(Lcom/bykv/vk/openvk/preload/geckox/d/c;)V
    .locals 0

    iput-object p1, p0, Lcom/bykv/vk/openvk/preload/geckox/d/c$3;->a:Lcom/bykv/vk/openvk/preload/geckox/d/c;

    invoke-direct {p0}, Lcom/bykv/vk/openvk/preload/a/c/a;-><init>()V

    return-void
.end method
