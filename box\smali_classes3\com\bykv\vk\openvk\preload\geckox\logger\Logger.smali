.class public interface abstract Lcom/bykv/vk/openvk/preload/geckox/logger/Logger;
.super Ljava/lang/Object;


# virtual methods
.method public varargs abstract d(Ljava/lang/String;[Ljava/lang/Object;)V
.end method

.method public abstract e(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Throwable;)V
.end method

.method public abstract w(Ljava/lang/String;Ljava/lang/String;)V
.end method

.method public abstract w(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Throwable;)V
.end method
