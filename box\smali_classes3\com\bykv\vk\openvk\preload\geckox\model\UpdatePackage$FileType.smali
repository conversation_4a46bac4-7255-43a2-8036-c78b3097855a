.class public final Lcom/bykv/vk/openvk/preload/geckox/model/UpdatePackage$FileType;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bykv/vk/openvk/preload/geckox/model/UpdatePackage;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "FileType"
.end annotation


# static fields
.field public static final COMPRESSED_FILE:I = 0x0

.field public static final MY_ARCHIVE_FILE:I = 0x2

.field public static final UNCOMPRESSED_FILE:I = 0x1


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
