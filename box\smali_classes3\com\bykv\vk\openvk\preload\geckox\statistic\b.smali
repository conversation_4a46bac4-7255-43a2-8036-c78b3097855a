.class public final Lcom/bykv/vk/openvk/preload/geckox/statistic/b;
.super Ljava/lang/Object;


# direct methods
.method public static a(Landroid/content/Context;)Lcom/bykv/vk/openvk/preload/b/b/a;
    .locals 1

    new-instance v0, Lcom/bykv/vk/openvk/preload/geckox/statistic/b$4;

    invoke-direct {v0, p0}, Lcom/bykv/vk/openvk/preload/geckox/statistic/b$4;-><init>(Landroid/content/Context;)V

    return-object v0
.end method

.method public static a(Lcom/bykv/vk/openvk/preload/geckox/b;)Lcom/bykv/vk/openvk/preload/b/b/a;
    .locals 1

    new-instance v0, Lcom/bykv/vk/openvk/preload/geckox/statistic/b$1;

    invoke-direct {v0, p0}, Lcom/bykv/vk/openvk/preload/geckox/statistic/b$1;-><init>(Lcom/bykv/vk/openvk/preload/geckox/b;)V

    return-object v0
.end method

.method public static b(Landroid/content/Context;)Lcom/bykv/vk/openvk/preload/b/b/a;
    .locals 1

    new-instance v0, Lcom/bykv/vk/openvk/preload/geckox/statistic/b$5;

    invoke-direct {v0, p0}, Lcom/bykv/vk/openvk/preload/geckox/statistic/b$5;-><init>(Landroid/content/Context;)V

    return-object v0
.end method
