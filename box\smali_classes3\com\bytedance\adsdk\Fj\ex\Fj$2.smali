.class final Lcom/bytedance/adsdk/Fj/ex/Fj$2;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/adsdk/Fj/ex/hjc/Fj;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bytedance/adsdk/Fj/ex/Fj;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/adsdk/Fj/ex/hjc/Fj/WR;

.field final synthetic ex:Lcom/bytedance/adsdk/Fj/ex/hjc/Fj;


# direct methods
.method public constructor <init>(Lcom/bytedance/adsdk/Fj/ex/hjc/Fj/WR;Lcom/bytedance/adsdk/Fj/ex/hjc/Fj;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/adsdk/Fj/ex/Fj$2;->Fj:Lcom/bytedance/adsdk/Fj/ex/hjc/Fj/WR;

    iput-object p2, p0, Lcom/bytedance/adsdk/Fj/ex/Fj$2;->ex:Lcom/bytedance/adsdk/Fj/ex/hjc/Fj;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public Fj(Ljava/lang/String;ILjava/util/Deque;)I
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "I",
            "Ljava/util/Deque<",
            "Lcom/bytedance/adsdk/Fj/ex/ex/Fj;",
            ">;)I"
        }
    .end annotation

    iget-object v0, p0, Lcom/bytedance/adsdk/Fj/ex/Fj$2;->Fj:Lcom/bytedance/adsdk/Fj/ex/hjc/Fj/WR;

    iget-object v1, p0, Lcom/bytedance/adsdk/Fj/ex/Fj$2;->ex:Lcom/bytedance/adsdk/Fj/ex/hjc/Fj;

    invoke-virtual {v0, p1, p2, p3, v1}, Lcom/bytedance/adsdk/Fj/ex/hjc/Fj/WR;->Fj(Ljava/lang/String;ILjava/util/Deque;Lcom/bytedance/adsdk/Fj/ex/hjc/Fj;)I

    move-result p1

    return p1
.end method
