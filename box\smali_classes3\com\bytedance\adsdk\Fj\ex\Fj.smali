.class public Lcom/bytedance/adsdk/Fj/ex/Fj;
.super Ljava/lang/Object;


# static fields
.field private static final Fj:Lcom/bytedance/adsdk/Fj/ex/hjc/Fj;


# instance fields
.field private Ubf:Ljava/lang/String;

.field private eV:Ljava/util/Deque;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Deque<",
            "Lcom/bytedance/adsdk/Fj/ex/ex/Fj;",
            ">;"
        }
    .end annotation
.end field

.field private final ex:Lcom/bytedance/adsdk/Fj/ex/hjc/Fj;

.field private hjc:Lcom/bytedance/adsdk/Fj/ex/ex/Fj;


# direct methods
.method static constructor <clinit>()V
    .locals 5

    const/16 v0, 0x9

    new-array v0, v0, [Lcom/bytedance/adsdk/Fj/ex/hjc/Fj/WR;

    new-instance v1, Lcom/bytedance/adsdk/Fj/ex/hjc/Fj/Ko;

    invoke-direct {v1}, Lcom/bytedance/adsdk/Fj/ex/hjc/Fj/Ko;-><init>()V

    const/4 v2, 0x0

    aput-object v1, v0, v2

    new-instance v1, Lcom/bytedance/adsdk/Fj/ex/hjc/Fj/eV;

    invoke-direct {v1}, Lcom/bytedance/adsdk/Fj/ex/hjc/Fj/eV;-><init>()V

    const/4 v2, 0x1

    aput-object v1, v0, v2

    new-instance v1, Lcom/bytedance/adsdk/Fj/ex/hjc/Fj/mSE;

    invoke-direct {v1}, Lcom/bytedance/adsdk/Fj/ex/hjc/Fj/mSE;-><init>()V

    const/4 v2, 0x2

    aput-object v1, v0, v2

    new-instance v1, Lcom/bytedance/adsdk/Fj/ex/hjc/Fj/ex;

    invoke-direct {v1}, Lcom/bytedance/adsdk/Fj/ex/hjc/Fj/ex;-><init>()V

    const/4 v2, 0x3

    aput-object v1, v0, v2

    new-instance v1, Lcom/bytedance/adsdk/Fj/ex/hjc/Fj/Ubf;

    invoke-direct {v1}, Lcom/bytedance/adsdk/Fj/ex/hjc/Fj/Ubf;-><init>()V

    const/4 v2, 0x4

    aput-object v1, v0, v2

    new-instance v1, Lcom/bytedance/adsdk/Fj/ex/hjc/Fj/Fj;

    invoke-direct {v1}, Lcom/bytedance/adsdk/Fj/ex/hjc/Fj/Fj;-><init>()V

    const/4 v2, 0x5

    aput-object v1, v0, v2

    new-instance v1, Lcom/bytedance/adsdk/Fj/ex/hjc/Fj/svN;

    invoke-direct {v1}, Lcom/bytedance/adsdk/Fj/ex/hjc/Fj/svN;-><init>()V

    const/4 v2, 0x6

    aput-object v1, v0, v2

    new-instance v1, Lcom/bytedance/adsdk/Fj/ex/hjc/Fj/hjc;

    invoke-direct {v1}, Lcom/bytedance/adsdk/Fj/ex/hjc/Fj/hjc;-><init>()V

    const/4 v2, 0x7

    aput-object v1, v0, v2

    new-instance v1, Lcom/bytedance/adsdk/Fj/ex/hjc/Fj/BcC;

    invoke-direct {v1}, Lcom/bytedance/adsdk/Fj/ex/hjc/Fj/BcC;-><init>()V

    const/16 v2, 0x8

    aput-object v1, v0, v2

    new-instance v1, Lcom/bytedance/adsdk/Fj/ex/Fj$1;

    invoke-direct {v1}, Lcom/bytedance/adsdk/Fj/ex/Fj$1;-><init>()V

    :goto_0
    if-ltz v2, :cond_0

    aget-object v3, v0, v2

    new-instance v4, Lcom/bytedance/adsdk/Fj/ex/Fj$2;

    invoke-direct {v4, v3, v1}, Lcom/bytedance/adsdk/Fj/ex/Fj$2;-><init>(Lcom/bytedance/adsdk/Fj/ex/hjc/Fj/WR;Lcom/bytedance/adsdk/Fj/ex/hjc/Fj;)V

    add-int/lit8 v2, v2, -0x1

    move-object v1, v4

    goto :goto_0

    :cond_0
    sput-object v1, Lcom/bytedance/adsdk/Fj/ex/Fj;->Fj:Lcom/bytedance/adsdk/Fj/ex/hjc/Fj;

    return-void
.end method

.method private constructor <init>(Ljava/lang/String;Lcom/bytedance/adsdk/Fj/ex/hjc/Fj;)V
    .locals 1

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    new-instance v0, Ljava/util/LinkedList;

    invoke-direct {v0}, Ljava/util/LinkedList;-><init>()V

    iput-object v0, p0, Lcom/bytedance/adsdk/Fj/ex/Fj;->eV:Ljava/util/Deque;

    iput-object p2, p0, Lcom/bytedance/adsdk/Fj/ex/Fj;->ex:Lcom/bytedance/adsdk/Fj/ex/hjc/Fj;

    iput-object p1, p0, Lcom/bytedance/adsdk/Fj/ex/Fj;->Ubf:Ljava/lang/String;

    :try_start_0
    invoke-direct {p0}, Lcom/bytedance/adsdk/Fj/ex/Fj;->Fj()V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    return-void

    :catch_0
    move-exception p2

    new-instance v0, Lcom/bytedance/adsdk/Fj/Fj/ex;

    invoke-direct {v0, p1, p2}, Lcom/bytedance/adsdk/Fj/Fj/ex;-><init>(Ljava/lang/String;Ljava/lang/Throwable;)V

    throw v0
.end method

.method public static Fj(Ljava/lang/String;)Lcom/bytedance/adsdk/Fj/ex/Fj;
    .locals 2

    new-instance v0, Lcom/bytedance/adsdk/Fj/ex/Fj;

    sget-object v1, Lcom/bytedance/adsdk/Fj/ex/Fj;->Fj:Lcom/bytedance/adsdk/Fj/ex/hjc/Fj;

    invoke-direct {v0, p0, v1}, Lcom/bytedance/adsdk/Fj/ex/Fj;-><init>(Ljava/lang/String;Lcom/bytedance/adsdk/Fj/ex/hjc/Fj;)V

    return-object v0
.end method

.method private Fj()V
    .locals 6

    iget-object v0, p0, Lcom/bytedance/adsdk/Fj/ex/Fj;->Ubf:Ljava/lang/String;

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v0

    const/4 v1, 0x0

    const/4 v2, 0x0

    :goto_0
    if-ge v2, v0, :cond_1

    iget-object v3, p0, Lcom/bytedance/adsdk/Fj/ex/Fj;->ex:Lcom/bytedance/adsdk/Fj/ex/hjc/Fj;

    iget-object v4, p0, Lcom/bytedance/adsdk/Fj/ex/Fj;->Ubf:Ljava/lang/String;

    iget-object v5, p0, Lcom/bytedance/adsdk/Fj/ex/Fj;->eV:Ljava/util/Deque;

    invoke-interface {v3, v4, v2, v5}, Lcom/bytedance/adsdk/Fj/ex/hjc/Fj;->Fj(Ljava/lang/String;ILjava/util/Deque;)I

    move-result v3

    if-eq v3, v2, :cond_0

    move v2, v3

    goto :goto_0

    :cond_0
    new-instance v0, Ljava/lang/IllegalArgumentException;

    new-instance v3, Ljava/lang/StringBuilder;

    const-string v4, "Unrecognized expression, unrecognized characters encountered during parsing:"

    invoke-direct {v3, v4}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    iget-object v4, p0, Lcom/bytedance/adsdk/Fj/ex/Fj;->Ubf:Ljava/lang/String;

    invoke-virtual {v4, v1, v2}, Ljava/lang/String;->substring(II)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v3, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-direct {v0, v1}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw v0

    :cond_1
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    :goto_1
    iget-object v3, p0, Lcom/bytedance/adsdk/Fj/ex/Fj;->eV:Ljava/util/Deque;

    invoke-interface {v3}, Ljava/util/Deque;->pollFirst()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lcom/bytedance/adsdk/Fj/ex/ex/Fj;

    if-eqz v3, :cond_2

    invoke-interface {v0, v1, v3}, Ljava/util/List;->add(ILjava/lang/Object;)V

    goto :goto_1

    :cond_2
    iget-object v1, p0, Lcom/bytedance/adsdk/Fj/ex/Fj;->Ubf:Ljava/lang/String;

    invoke-static {v0, v1, v2}, Lcom/bytedance/adsdk/Fj/ex/Ubf/ex;->Fj(Ljava/util/List;Ljava/lang/String;I)Lcom/bytedance/adsdk/Fj/ex/ex/Fj;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/adsdk/Fj/ex/Fj;->hjc:Lcom/bytedance/adsdk/Fj/ex/ex/Fj;

    const/4 v0, 0x0

    iput-object v0, p0, Lcom/bytedance/adsdk/Fj/ex/Fj;->eV:Ljava/util/Deque;

    return-void
.end method


# virtual methods
.method public Fj(Ljava/util/Map;)Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">(",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Lorg/json/JSONObject;",
            ">;)TT;"
        }
    .end annotation

    iget-object v0, p0, Lcom/bytedance/adsdk/Fj/ex/Fj;->hjc:Lcom/bytedance/adsdk/Fj/ex/ex/Fj;

    invoke-interface {v0, p1}, Lcom/bytedance/adsdk/Fj/ex/ex/Fj;->Fj(Ljava/util/Map;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public Fj(Lorg/json/JSONObject;)Ljava/lang/Object;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">(",
            "Lorg/json/JSONObject;",
            ")TT;"
        }
    .end annotation

    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    const-string v1, "default_key"

    invoke-interface {v0, v1, p1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    invoke-virtual {p0, v0}, Lcom/bytedance/adsdk/Fj/ex/Fj;->Fj(Ljava/util/Map;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method
