.class public Lcom/bytedance/adsdk/Fj/ex/Ubf/Fj;
.super Ljava/lang/Object;


# direct methods
.method public static Fj(C)Z
    .locals 1

    const/16 v0, 0x20

    if-ne p0, v0, :cond_0

    const/4 p0, 0x1

    return p0

    :cond_0
    const/4 p0, 0x0

    return p0
.end method

.method public static eV(C)Z
    .locals 1

    const/16 v0, 0x2b

    if-eq v0, p0, :cond_1

    const/16 v0, 0x2d

    if-eq v0, p0, :cond_1

    const/16 v0, 0x2a

    if-eq v0, p0, :cond_1

    const/16 v0, 0x2f

    if-eq v0, p0, :cond_1

    const/16 v0, 0x25

    if-eq v0, p0, :cond_1

    const/16 v0, 0x3d

    if-eq v0, p0, :cond_1

    const/16 v0, 0x3e

    if-eq v0, p0, :cond_1

    const/16 v0, 0x3c

    if-eq v0, p0, :cond_1

    const/16 v0, 0x21

    if-eq v0, p0, :cond_1

    const/16 v0, 0x26

    if-eq v0, p0, :cond_1

    const/16 v0, 0x7c

    if-eq v0, p0, :cond_1

    const/16 v0, 0x3f

    if-eq v0, p0, :cond_1

    const/16 v0, 0x3a

    if-ne v0, p0, :cond_0

    goto :goto_0

    :cond_0
    const/4 p0, 0x0

    return p0

    :cond_1
    :goto_0
    const/4 p0, 0x1

    return p0
.end method

.method public static ex(C)Z
    .locals 1

    const/16 v0, 0x41

    if-lt p0, v0, :cond_0

    const/16 v0, 0x5a

    if-le p0, v0, :cond_1

    :cond_0
    const/16 v0, 0x61

    if-lt p0, v0, :cond_2

    const/16 v0, 0x7a

    if-gt p0, v0, :cond_2

    :cond_1
    const/4 p0, 0x1

    return p0

    :cond_2
    const/4 p0, 0x0

    return p0
.end method

.method public static hjc(C)Z
    .locals 1

    const/16 v0, 0x30

    if-lt p0, v0, :cond_0

    const/16 v0, 0x39

    if-gt p0, v0, :cond_0

    const/4 p0, 0x1

    return p0

    :cond_0
    const/4 p0, 0x0

    return p0
.end method
