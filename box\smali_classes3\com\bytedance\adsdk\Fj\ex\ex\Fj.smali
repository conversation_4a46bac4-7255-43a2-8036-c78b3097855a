.class public interface abstract Lcom/bytedance/adsdk/Fj/ex/ex/Fj;
.super Ljava/lang/Object;


# virtual methods
.method public abstract Fj()Lcom/bytedance/adsdk/Fj/ex/eV/Ubf;
.end method

.method public abstract Fj(Ljava/util/Map;)Ljava/lang/Object;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Lorg/json/JSONObject;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation
.end method

.method public abstract ex()Ljava/lang/String;
.end method
