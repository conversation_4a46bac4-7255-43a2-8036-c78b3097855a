.class public Lcom/bytedance/adsdk/Fj/ex/ex/Fj/Af;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/adsdk/Fj/ex/ex/Fj;


# instance fields
.field private Fj:Lcom/bytedance/adsdk/Fj/ex/eV/eV;


# direct methods
.method public constructor <init>(Lcom/bytedance/adsdk/Fj/ex/eV/eV;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/bytedance/adsdk/Fj/ex/ex/Fj/Af;->Fj:Lcom/bytedance/adsdk/Fj/ex/eV/eV;

    return-void
.end method


# virtual methods
.method public Fj()Lcom/bytedance/adsdk/Fj/ex/eV/Ubf;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/Fj/ex/ex/Fj/Af;->Fj:Lcom/bytedance/adsdk/Fj/ex/eV/eV;

    return-object v0
.end method

.method public Fj(Ljava/util/Map;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Lorg/json/JSONObject;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    new-instance p1, Ljava/lang/UnsupportedOperationException;

    invoke-direct {p1}, Ljava/lang/UnsupportedOperationException;-><init>()V

    throw p1
.end method

.method public ex()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/Fj/ex/ex/Fj/Af;->Fj:Lcom/bytedance/adsdk/Fj/ex/eV/eV;

    invoke-virtual {v0}, Lcom/bytedance/adsdk/Fj/ex/eV/eV;->Fj()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public toString()Ljava/lang/String;
    .locals 1

    invoke-virtual {p0}, Lcom/bytedance/adsdk/Fj/ex/ex/Fj/Af;->ex()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
