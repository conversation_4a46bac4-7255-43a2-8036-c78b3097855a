.class public Lcom/bytedance/adsdk/Fj/ex/ex/Fj/Ko;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/adsdk/Fj/ex/ex/Fj;


# instance fields
.field private Fj:[Lcom/bytedance/adsdk/Fj/ex/ex/Fj;

.field private ex:Ljava/lang/String;

.field private hjc:Lcom/bytedance/adsdk/Fj/ex/Fj/Fj;


# direct methods
.method public constructor <init>(Ljava/lang/String;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/bytedance/adsdk/Fj/ex/ex/Fj/Ko;->ex:Ljava/lang/String;

    return-void
.end method


# virtual methods
.method public Fj()Lcom/bytedance/adsdk/Fj/ex/eV/Ubf;
    .locals 1

    sget-object v0, Lcom/bytedance/adsdk/Fj/ex/eV/ex;->Fj:Lcom/bytedance/adsdk/Fj/ex/eV/ex;

    return-object v0
.end method

.method public Fj(Ljava/util/Map;)Ljava/lang/Object;
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Lorg/json/JSONObject;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    new-instance v0, Lcom/bytedance/adsdk/Fj/ex/Fj/Fj;

    invoke-direct {v0}, Lcom/bytedance/adsdk/Fj/ex/Fj/Fj;-><init>()V

    iput-object v0, p0, Lcom/bytedance/adsdk/Fj/ex/ex/Fj/Ko;->hjc:Lcom/bytedance/adsdk/Fj/ex/Fj/Fj;

    iget-object v1, p0, Lcom/bytedance/adsdk/Fj/ex/ex/Fj/Ko;->ex:Ljava/lang/String;

    invoke-virtual {v0, v1}, Lcom/bytedance/adsdk/Fj/ex/Fj/Fj;->Fj(Ljava/lang/String;)V

    iget-object v0, p0, Lcom/bytedance/adsdk/Fj/ex/ex/Fj/Ko;->Fj:[Lcom/bytedance/adsdk/Fj/ex/ex/Fj;

    array-length v0, v0

    new-array v0, v0, [Ljava/lang/Object;

    const/4 v1, 0x0

    :goto_0
    iget-object v2, p0, Lcom/bytedance/adsdk/Fj/ex/ex/Fj/Ko;->Fj:[Lcom/bytedance/adsdk/Fj/ex/ex/Fj;

    array-length v3, v2

    if-ge v1, v3, :cond_1

    aget-object v2, v2, v1

    if-eqz v2, :cond_0

    invoke-interface {v2, p1}, Lcom/bytedance/adsdk/Fj/ex/ex/Fj;->Fj(Ljava/util/Map;)Ljava/lang/Object;

    move-result-object v2

    aput-object v2, v0, v1

    :cond_0
    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_1
    iget-object p1, p0, Lcom/bytedance/adsdk/Fj/ex/ex/Fj/Ko;->hjc:Lcom/bytedance/adsdk/Fj/ex/Fj/Fj;

    invoke-virtual {p1, v0}, Lcom/bytedance/adsdk/Fj/ex/Fj/Fj;->Fj([Ljava/lang/Object;)V

    iget-object p1, p0, Lcom/bytedance/adsdk/Fj/ex/ex/Fj/Ko;->hjc:Lcom/bytedance/adsdk/Fj/ex/Fj/Fj;

    return-object p1
.end method

.method public Fj([Lcom/bytedance/adsdk/Fj/ex/ex/Fj;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/adsdk/Fj/ex/ex/Fj/Ko;->Fj:[Lcom/bytedance/adsdk/Fj/ex/ex/Fj;

    return-void
.end method

.method public ex()Ljava/lang/String;
    .locals 4

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    iget-object v1, p0, Lcom/bytedance/adsdk/Fj/ex/ex/Fj/Ko;->ex:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v1, "("

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v1, p0, Lcom/bytedance/adsdk/Fj/ex/ex/Fj/Ko;->Fj:[Lcom/bytedance/adsdk/Fj/ex/ex/Fj;

    if-eqz v1, :cond_0

    array-length v1, v1

    if-lez v1, :cond_0

    const/4 v1, 0x0

    :goto_0
    iget-object v2, p0, Lcom/bytedance/adsdk/Fj/ex/ex/Fj/Ko;->Fj:[Lcom/bytedance/adsdk/Fj/ex/ex/Fj;

    array-length v3, v2

    if-ge v1, v3, :cond_0

    aget-object v2, v2, v1

    invoke-interface {v2}, Lcom/bytedance/adsdk/Fj/ex/ex/Fj;->ex()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v2, ","

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_0
    const-string v1, ")"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
