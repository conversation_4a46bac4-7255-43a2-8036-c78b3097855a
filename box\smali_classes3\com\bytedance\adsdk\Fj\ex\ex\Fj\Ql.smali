.class public abstract Lcom/bytedance/adsdk/Fj/ex/ex/Fj/Ql;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/adsdk/Fj/ex/ex/Fj;


# instance fields
.field protected Fj:Lcom/bytedance/adsdk/Fj/ex/ex/Fj;

.field protected ex:Lcom/bytedance/adsdk/Fj/ex/ex/Fj;

.field protected hjc:Lcom/bytedance/adsdk/Fj/ex/eV/hjc;


# direct methods
.method public constructor <init>(Lcom/bytedance/adsdk/Fj/ex/eV/hjc;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/bytedance/adsdk/Fj/ex/ex/Fj/Ql;->hjc:Lcom/bytedance/adsdk/Fj/ex/eV/hjc;

    return-void
.end method


# virtual methods
.method public Fj()Lcom/bytedance/adsdk/Fj/ex/eV/Ubf;
    .locals 1

    sget-object v0, Lcom/bytedance/adsdk/Fj/ex/eV/WR;->Fj:Lcom/bytedance/adsdk/Fj/ex/eV/WR;

    return-object v0
.end method

.method public Fj(Lcom/bytedance/adsdk/Fj/ex/ex/Fj;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/adsdk/Fj/ex/ex/Fj/Ql;->Fj:Lcom/bytedance/adsdk/Fj/ex/ex/Fj;

    return-void
.end method

.method public ex()Ljava/lang/String;
    .locals 2

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    iget-object v1, p0, Lcom/bytedance/adsdk/Fj/ex/ex/Fj/Ql;->Fj:Lcom/bytedance/adsdk/Fj/ex/ex/Fj;

    invoke-interface {v1}, Lcom/bytedance/adsdk/Fj/ex/ex/Fj;->ex()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v1, p0, Lcom/bytedance/adsdk/Fj/ex/ex/Fj/Ql;->hjc:Lcom/bytedance/adsdk/Fj/ex/eV/hjc;

    invoke-virtual {v1}, Lcom/bytedance/adsdk/Fj/ex/eV/hjc;->Fj()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v1, p0, Lcom/bytedance/adsdk/Fj/ex/ex/Fj/Ql;->ex:Lcom/bytedance/adsdk/Fj/ex/ex/Fj;

    invoke-interface {v1}, Lcom/bytedance/adsdk/Fj/ex/ex/Fj;->ex()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public ex(Lcom/bytedance/adsdk/Fj/ex/ex/Fj;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/adsdk/Fj/ex/ex/Fj/Ql;->ex:Lcom/bytedance/adsdk/Fj/ex/ex/Fj;

    return-void
.end method

.method public toString()Ljava/lang/String;
    .locals 1

    invoke-virtual {p0}, Lcom/bytedance/adsdk/Fj/ex/ex/Fj/Ql;->ex()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
