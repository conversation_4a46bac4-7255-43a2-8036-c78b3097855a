.class public interface abstract Lcom/bytedance/adsdk/Fj/ex/hjc/Fj;
.super Ljava/lang/Object;


# virtual methods
.method public abstract Fj(Ljava/lang/String;ILjava/util/Deque;)I
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "I",
            "Ljava/util/Deque<",
            "Lcom/bytedance/adsdk/Fj/ex/ex/Fj;",
            ">;)I"
        }
    .end annotation
.end method
