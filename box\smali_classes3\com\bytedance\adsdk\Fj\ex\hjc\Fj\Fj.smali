.class public Lcom/bytedance/adsdk/Fj/ex/hjc/Fj/Fj;
.super Lcom/bytedance/adsdk/Fj/ex/hjc/Fj/WR;


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Lcom/bytedance/adsdk/Fj/ex/hjc/Fj/WR;-><init>()V

    return-void
.end method


# virtual methods
.method public Fj(Ljava/lang/String;ILjava/util/Deque;Lcom/bytedance/adsdk/Fj/ex/hjc/Fj;)I
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "I",
            "Ljava/util/Deque<",
            "Lcom/bytedance/adsdk/Fj/ex/ex/Fj;",
            ">;",
            "Lcom/bytedance/adsdk/Fj/ex/hjc/Fj;",
            ")I"
        }
    .end annotation

    const/16 v0, 0x2c

    invoke-virtual {p0, p2, p1}, Lcom/bytedance/adsdk/Fj/ex/hjc/Fj/WR;->Fj(ILjava/lang/String;)C

    move-result v1

    if-eq v0, v1, :cond_0

    invoke-interface {p4, p1, p2, p3}, Lcom/bytedance/adsdk/Fj/ex/hjc/Fj;->Fj(Ljava/lang/String;ILjava/util/Deque;)I

    move-result p1

    return p1

    :cond_0
    new-instance p1, Lcom/bytedance/adsdk/Fj/ex/ex/Fj/Af;

    sget-object p4, Lcom/bytedance/adsdk/Fj/ex/eV/eV;->Ubf:Lcom/bytedance/adsdk/Fj/ex/eV/eV;

    invoke-direct {p1, p4}, Lcom/bytedance/adsdk/Fj/ex/ex/Fj/Af;-><init>(Lcom/bytedance/adsdk/Fj/ex/eV/eV;)V

    invoke-interface {p3, p1}, Ljava/util/Deque;->push(Ljava/lang/Object;)V

    add-int/lit8 p2, p2, 0x1

    return p2
.end method
