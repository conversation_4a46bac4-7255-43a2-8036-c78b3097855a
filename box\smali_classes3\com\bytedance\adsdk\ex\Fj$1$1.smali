.class Lcom/bytedance/adsdk/ex/Fj$1$1;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/adsdk/ugeno/Fj$Fj;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/adsdk/ex/Fj$1;->Fj(Lcom/bytedance/adsdk/lottie/mSE;)Landroid/graphics/Bitmap;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/adsdk/lottie/mSE;

.field final synthetic ex:Ljava/lang/String;

.field final synthetic hjc:Lcom/bytedance/adsdk/ex/Fj$1;


# direct methods
.method public constructor <init>(Lcom/bytedance/adsdk/ex/Fj$1;Lcom/bytedance/adsdk/lottie/mSE;Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/adsdk/ex/Fj$1$1;->hjc:Lcom/bytedance/adsdk/ex/Fj$1;

    iput-object p2, p0, Lcom/bytedance/adsdk/ex/Fj$1$1;->Fj:Lcom/bytedance/adsdk/lottie/mSE;

    iput-object p3, p0, Lcom/bytedance/adsdk/ex/Fj$1$1;->ex:Ljava/lang/String;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public Fj(Landroid/graphics/Bitmap;)V
    .locals 3

    if-eqz p1, :cond_0

    iget-object v0, p0, Lcom/bytedance/adsdk/ex/Fj$1$1;->Fj:Lcom/bytedance/adsdk/lottie/mSE;

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/mSE;->Fj()I

    move-result v0

    iget-object v1, p0, Lcom/bytedance/adsdk/ex/Fj$1$1;->Fj:Lcom/bytedance/adsdk/lottie/mSE;

    invoke-virtual {v1}, Lcom/bytedance/adsdk/lottie/mSE;->ex()I

    move-result v1

    const/4 v2, 0x0

    invoke-static {p1, v0, v1, v2}, Landroid/graphics/Bitmap;->createScaledBitmap(Landroid/graphics/Bitmap;IIZ)Landroid/graphics/Bitmap;

    move-result-object p1

    iget-object v0, p0, Lcom/bytedance/adsdk/ex/Fj$1$1;->hjc:Lcom/bytedance/adsdk/ex/Fj$1;

    iget-object v0, v0, Lcom/bytedance/adsdk/ex/Fj$1;->Fj:Lcom/bytedance/adsdk/ex/Fj;

    invoke-static {v0}, Lcom/bytedance/adsdk/ex/Fj;->hjc(Lcom/bytedance/adsdk/ex/Fj;)Ljava/util/HashMap;

    move-result-object v0

    iget-object v1, p0, Lcom/bytedance/adsdk/ex/Fj$1$1;->ex:Ljava/lang/String;

    invoke-virtual {v0, v1, p1}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    iget-object v0, p0, Lcom/bytedance/adsdk/ex/Fj$1$1;->hjc:Lcom/bytedance/adsdk/ex/Fj$1;

    iget-object v0, v0, Lcom/bytedance/adsdk/ex/Fj$1;->Fj:Lcom/bytedance/adsdk/ex/Fj;

    invoke-static {v0}, Lcom/bytedance/adsdk/ex/Fj;->eV(Lcom/bytedance/adsdk/ex/Fj;)Landroid/view/View;

    move-result-object v0

    check-cast v0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;

    iget-object v1, p0, Lcom/bytedance/adsdk/ex/Fj$1$1;->Fj:Lcom/bytedance/adsdk/lottie/mSE;

    invoke-virtual {v1}, Lcom/bytedance/adsdk/lottie/mSE;->hjc()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1, p1}, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->Fj(Ljava/lang/String;Landroid/graphics/Bitmap;)Landroid/graphics/Bitmap;

    :cond_0
    return-void
.end method
