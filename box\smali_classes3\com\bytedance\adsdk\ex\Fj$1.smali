.class Lcom/bytedance/adsdk/ex/Fj$1;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/adsdk/lottie/eV;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/adsdk/ex/Fj;->ex()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/adsdk/ex/Fj;


# direct methods
.method public constructor <init>(Lcom/bytedance/adsdk/ex/Fj;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/adsdk/ex/Fj$1;->Fj:Lcom/bytedance/adsdk/ex/Fj;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public Fj(Lcom/bytedance/adsdk/lottie/mSE;)Landroid/graphics/Bitmap;
    .locals 4

    const/4 v0, 0x0

    if-nez p1, :cond_0

    return-object v0

    :cond_0
    invoke-virtual {p1}, Lcom/bytedance/adsdk/lottie/mSE;->eV()Ljava/lang/String;

    move-result-object v1

    invoke-static {v1}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v2

    if-eqz v2, :cond_1

    return-object v0

    :cond_1
    iget-object v0, p0, Lcom/bytedance/adsdk/ex/Fj$1;->Fj:Lcom/bytedance/adsdk/ex/Fj;

    invoke-static {v0}, Lcom/bytedance/adsdk/ex/Fj;->Fj(Lcom/bytedance/adsdk/ex/Fj;)Lorg/json/JSONObject;

    move-result-object v0

    invoke-static {v1, v0}, Lcom/bytedance/adsdk/ugeno/Fj/hjc;->Fj(Ljava/lang/String;Lorg/json/JSONObject;)Ljava/lang/String;

    move-result-object v0

    invoke-static {}, Lcom/bytedance/adsdk/ugeno/hjc;->Fj()Lcom/bytedance/adsdk/ugeno/hjc;

    move-result-object v1

    invoke-virtual {v1}, Lcom/bytedance/adsdk/ugeno/hjc;->ex()Lcom/bytedance/adsdk/ugeno/Fj;

    move-result-object v1

    iget-object v2, p0, Lcom/bytedance/adsdk/ex/Fj$1;->Fj:Lcom/bytedance/adsdk/ex/Fj;

    invoke-static {v2}, Lcom/bytedance/adsdk/ex/Fj;->ex(Lcom/bytedance/adsdk/ex/Fj;)Landroid/content/Context;

    move-result-object v2

    new-instance v3, Lcom/bytedance/adsdk/ex/Fj$1$1;

    invoke-direct {v3, p0, p1, v0}, Lcom/bytedance/adsdk/ex/Fj$1$1;-><init>(Lcom/bytedance/adsdk/ex/Fj$1;Lcom/bytedance/adsdk/lottie/mSE;Ljava/lang/String;)V

    const/high16 p1, -0x40800000    # -1.0f

    invoke-interface {v1, v2, v0, p1, v3}, Lcom/bytedance/adsdk/ugeno/Fj;->Fj(Landroid/content/Context;Ljava/lang/String;FLcom/bytedance/adsdk/ugeno/Fj$Fj;)V

    iget-object p1, p0, Lcom/bytedance/adsdk/ex/Fj$1;->Fj:Lcom/bytedance/adsdk/ex/Fj;

    invoke-static {p1}, Lcom/bytedance/adsdk/ex/Fj;->hjc(Lcom/bytedance/adsdk/ex/Fj;)Ljava/util/HashMap;

    move-result-object p1

    invoke-virtual {p1, v0}, Ljava/util/HashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Landroid/graphics/Bitmap;

    return-object p1
.end method
