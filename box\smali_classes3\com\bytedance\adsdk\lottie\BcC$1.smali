.class Lcom/bytedance/adsdk/lottie/BcC$1;
.super Ljava/lang/Object;

# interfaces
.implements Landroid/animation/ValueAnimator$AnimatorUpdateListener;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bytedance/adsdk/lottie/BcC;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/adsdk/lottie/BcC;


# direct methods
.method public constructor <init>(Lcom/bytedance/adsdk/lottie/BcC;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/BcC$1;->Fj:Lcom/bytedance/adsdk/lottie/BcC;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public onAnimationUpdate(Landroid/animation/ValueAnimator;)V
    .locals 1

    iget-object p1, p0, Lcom/bytedance/adsdk/lottie/BcC$1;->Fj:Lcom/bytedance/adsdk/lottie/BcC;

    invoke-static {p1}, Lcom/bytedance/adsdk/lottie/BcC;->Fj(Lcom/bytedance/adsdk/lottie/BcC;)Lcom/bytedance/adsdk/lottie/hjc/hjc/ex;

    move-result-object p1

    if-eqz p1, :cond_0

    iget-object p1, p0, Lcom/bytedance/adsdk/lottie/BcC$1;->Fj:Lcom/bytedance/adsdk/lottie/BcC;

    invoke-static {p1}, Lcom/bytedance/adsdk/lottie/BcC;->Fj(Lcom/bytedance/adsdk/lottie/BcC;)Lcom/bytedance/adsdk/lottie/hjc/hjc/ex;

    move-result-object p1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC$1;->Fj:Lcom/bytedance/adsdk/lottie/BcC;

    invoke-static {v0}, Lcom/bytedance/adsdk/lottie/BcC;->ex(Lcom/bytedance/adsdk/lottie/BcC;)Lcom/bytedance/adsdk/lottie/WR/hjc;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/WR/hjc;->WR()F

    move-result v0

    invoke-virtual {p1, v0}, Lcom/bytedance/adsdk/lottie/hjc/hjc/ex;->Fj(F)V

    :cond_0
    return-void
.end method
