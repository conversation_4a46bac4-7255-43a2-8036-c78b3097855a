.class Lcom/bytedance/adsdk/lottie/BcC$4;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/adsdk/lottie/BcC$Fj;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/adsdk/lottie/BcC;->hjc(I)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:I

.field final synthetic ex:Lcom/bytedance/adsdk/lottie/BcC;


# direct methods
.method public constructor <init>(Lcom/bytedance/adsdk/lottie/BcC;I)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/BcC$4;->ex:Lcom/bytedance/adsdk/lottie/BcC;

    iput p2, p0, Lcom/bytedance/adsdk/lottie/BcC$4;->Fj:I

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public Fj(Lcom/bytedance/adsdk/lottie/WR;)V
    .locals 1

    iget-object p1, p0, Lcom/bytedance/adsdk/lottie/BcC$4;->ex:Lcom/bytedance/adsdk/lottie/BcC;

    iget v0, p0, Lcom/bytedance/adsdk/lottie/BcC$4;->Fj:I

    invoke-virtual {p1, v0}, Lcom/bytedance/adsdk/lottie/BcC;->hjc(I)V

    return-void
.end method
