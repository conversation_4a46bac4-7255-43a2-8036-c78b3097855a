.class Lcom/bytedance/adsdk/lottie/BcC$6;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/adsdk/lottie/BcC$Fj;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/adsdk/lottie/BcC;->mSE()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/adsdk/lottie/BcC;


# direct methods
.method public constructor <init>(Lcom/bytedance/adsdk/lottie/BcC;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/BcC$6;->Fj:Lcom/bytedance/adsdk/lottie/BcC;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public Fj(Lcom/bytedance/adsdk/lottie/WR;)V
    .locals 0

    iget-object p1, p0, Lcom/bytedance/adsdk/lottie/BcC$6;->Fj:Lcom/bytedance/adsdk/lottie/BcC;

    invoke-virtual {p1}, Lcom/bytedance/adsdk/lottie/BcC;->mSE()V

    return-void
.end method
