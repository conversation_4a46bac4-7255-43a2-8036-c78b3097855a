.class interface abstract Lcom/bytedance/adsdk/lottie/BcC$Fj;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bytedance/adsdk/lottie/BcC;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "Fj"
.end annotation


# virtual methods
.method public abstract Fj(Lcom/bytedance/adsdk/lottie/WR;)V
.end method
