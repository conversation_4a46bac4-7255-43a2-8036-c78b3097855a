.class public Lcom/bytedance/adsdk/lottie/BcC;
.super Landroid/graphics/drawable/Drawable;

# interfaces
.implements Landroid/graphics/drawable/Animatable;
.implements Landroid/graphics/drawable/Drawable$Callback;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/bytedance/adsdk/lottie/BcC$ex;,
        Lcom/bytedance/adsdk/lottie/BcC$Fj;
    }
.end annotation


# instance fields
.field private Af:I

.field private BcC:Z

.field Fj:Ljava/lang/String;

.field private JU:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Landroid/graphics/Typeface;",
            ">;"
        }
    .end annotation
.end field

.field private JW:Lcom/bytedance/adsdk/lottie/ex/Fj;

.field private final Ko:Ljava/util/ArrayList;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/ArrayList<",
            "Lcom/bytedance/adsdk/lottie/BcC$Fj;",
            ">;"
        }
    .end annotation
.end field

.field private Moo:Z

.field private Ql:Z

.field private Tc:Lcom/bytedance/adsdk/lottie/eV;

.field private UYd:Lcom/bytedance/adsdk/lottie/ex/ex;

.field private final Ubf:Lcom/bytedance/adsdk/lottie/WR/hjc;

.field private Vq:Lcom/bytedance/adsdk/lottie/rS;

.field private WR:Z

.field private cB:Z

.field private dG:Ljava/lang/String;

.field private eV:Lcom/bytedance/adsdk/lottie/WR;

.field private efV:Landroid/graphics/Matrix;

.field private eh:Landroid/graphics/Paint;

.field ex:Lcom/bytedance/adsdk/lottie/hjc;

.field private fj:Landroid/graphics/RectF;

.field private gXF:Landroid/graphics/RectF;

.field hjc:Lcom/bytedance/adsdk/lottie/vYf;

.field private iT:Landroid/graphics/Matrix;

.field private kF:Landroid/graphics/Rect;

.field private lv:Landroid/graphics/Canvas;

.field private mC:Z

.field private mE:Lcom/bytedance/adsdk/lottie/hjc/hjc/ex;

.field private mSE:Lcom/bytedance/adsdk/lottie/BcC$ex;

.field private nsB:Z

.field private oX:Z

.field private final rAx:Landroid/animation/ValueAnimator$AnimatorUpdateListener;

.field private rS:Z

.field private rXP:Landroid/graphics/Rect;

.field private final rf:Landroid/graphics/Matrix;

.field private spi:Landroid/graphics/RectF;

.field private svN:Z

.field private uM:Landroid/graphics/Rect;

.field private uy:Landroid/graphics/Bitmap;

.field private vYf:Z


# direct methods
.method public constructor <init>()V
    .locals 4

    invoke-direct {p0}, Landroid/graphics/drawable/Drawable;-><init>()V

    new-instance v0, Lcom/bytedance/adsdk/lottie/WR/hjc;

    invoke-direct {v0}, Lcom/bytedance/adsdk/lottie/WR/hjc;-><init>()V

    iput-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->Ubf:Lcom/bytedance/adsdk/lottie/WR/hjc;

    const/4 v1, 0x1

    iput-boolean v1, p0, Lcom/bytedance/adsdk/lottie/BcC;->WR:Z

    const/4 v2, 0x0

    iput-boolean v2, p0, Lcom/bytedance/adsdk/lottie/BcC;->svN:Z

    iput-boolean v2, p0, Lcom/bytedance/adsdk/lottie/BcC;->BcC:Z

    sget-object v3, Lcom/bytedance/adsdk/lottie/BcC$ex;->Fj:Lcom/bytedance/adsdk/lottie/BcC$ex;

    iput-object v3, p0, Lcom/bytedance/adsdk/lottie/BcC;->mSE:Lcom/bytedance/adsdk/lottie/BcC$ex;

    new-instance v3, Ljava/util/ArrayList;

    invoke-direct {v3}, Ljava/util/ArrayList;-><init>()V

    iput-object v3, p0, Lcom/bytedance/adsdk/lottie/BcC;->Ko:Ljava/util/ArrayList;

    new-instance v3, Lcom/bytedance/adsdk/lottie/BcC$1;

    invoke-direct {v3, p0}, Lcom/bytedance/adsdk/lottie/BcC$1;-><init>(Lcom/bytedance/adsdk/lottie/BcC;)V

    iput-object v3, p0, Lcom/bytedance/adsdk/lottie/BcC;->rAx:Landroid/animation/ValueAnimator$AnimatorUpdateListener;

    iput-boolean v2, p0, Lcom/bytedance/adsdk/lottie/BcC;->rS:Z

    iput-boolean v1, p0, Lcom/bytedance/adsdk/lottie/BcC;->vYf:Z

    const/16 v1, 0xff

    iput v1, p0, Lcom/bytedance/adsdk/lottie/BcC;->Af:I

    sget-object v1, Lcom/bytedance/adsdk/lottie/rS;->Fj:Lcom/bytedance/adsdk/lottie/rS;

    iput-object v1, p0, Lcom/bytedance/adsdk/lottie/BcC;->Vq:Lcom/bytedance/adsdk/lottie/rS;

    iput-boolean v2, p0, Lcom/bytedance/adsdk/lottie/BcC;->Moo:Z

    new-instance v1, Landroid/graphics/Matrix;

    invoke-direct {v1}, Landroid/graphics/Matrix;-><init>()V

    iput-object v1, p0, Lcom/bytedance/adsdk/lottie/BcC;->rf:Landroid/graphics/Matrix;

    iput-boolean v2, p0, Lcom/bytedance/adsdk/lottie/BcC;->oX:Z

    invoke-virtual {v0, v3}, Lcom/bytedance/adsdk/lottie/WR/Fj;->addUpdateListener(Landroid/animation/ValueAnimator$AnimatorUpdateListener;)V

    return-void
.end method

.method public static synthetic Fj(Lcom/bytedance/adsdk/lottie/BcC;)Lcom/bytedance/adsdk/lottie/hjc/hjc/ex;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/adsdk/lottie/BcC;->mE:Lcom/bytedance/adsdk/lottie/hjc/hjc/ex;

    return-object p0
.end method

.method private Fj(Landroid/graphics/Canvas;)V
    .locals 5

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->mE:Lcom/bytedance/adsdk/lottie/hjc/hjc/ex;

    iget-object v1, p0, Lcom/bytedance/adsdk/lottie/BcC;->eV:Lcom/bytedance/adsdk/lottie/WR;

    if-eqz v0, :cond_2

    if-nez v1, :cond_0

    goto :goto_0

    :cond_0
    iget-object v2, p0, Lcom/bytedance/adsdk/lottie/BcC;->rf:Landroid/graphics/Matrix;

    invoke-virtual {v2}, Landroid/graphics/Matrix;->reset()V

    invoke-virtual {p0}, Landroid/graphics/drawable/Drawable;->getBounds()Landroid/graphics/Rect;

    move-result-object v2

    invoke-virtual {v2}, Landroid/graphics/Rect;->isEmpty()Z

    move-result v3

    if-nez v3, :cond_1

    invoke-virtual {v2}, Landroid/graphics/Rect;->width()I

    move-result v3

    int-to-float v3, v3

    invoke-virtual {v1}, Lcom/bytedance/adsdk/lottie/WR;->eV()Landroid/graphics/Rect;

    move-result-object v4

    invoke-virtual {v4}, Landroid/graphics/Rect;->width()I

    move-result v4

    int-to-float v4, v4

    div-float/2addr v3, v4

    invoke-virtual {v2}, Landroid/graphics/Rect;->height()I

    move-result v4

    int-to-float v4, v4

    invoke-virtual {v1}, Lcom/bytedance/adsdk/lottie/WR;->eV()Landroid/graphics/Rect;

    move-result-object v1

    invoke-virtual {v1}, Landroid/graphics/Rect;->height()I

    move-result v1

    int-to-float v1, v1

    div-float/2addr v4, v1

    iget-object v1, p0, Lcom/bytedance/adsdk/lottie/BcC;->rf:Landroid/graphics/Matrix;

    invoke-virtual {v1, v3, v4}, Landroid/graphics/Matrix;->preScale(FF)Z

    iget-object v1, p0, Lcom/bytedance/adsdk/lottie/BcC;->rf:Landroid/graphics/Matrix;

    iget v3, v2, Landroid/graphics/Rect;->left:I

    int-to-float v3, v3

    iget v2, v2, Landroid/graphics/Rect;->top:I

    int-to-float v2, v2

    invoke-virtual {v1, v3, v2}, Landroid/graphics/Matrix;->preTranslate(FF)Z

    :cond_1
    iget-object v1, p0, Lcom/bytedance/adsdk/lottie/BcC;->rf:Landroid/graphics/Matrix;

    iget v2, p0, Lcom/bytedance/adsdk/lottie/BcC;->Af:I

    invoke-virtual {v0, p1, v1, v2}, Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;->Fj(Landroid/graphics/Canvas;Landroid/graphics/Matrix;I)V

    :cond_2
    :goto_0
    return-void
.end method

.method private Fj(Landroid/graphics/Canvas;Lcom/bytedance/adsdk/lottie/hjc/hjc/ex;)V
    .locals 8

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->eV:Lcom/bytedance/adsdk/lottie/WR;

    if-eqz v0, :cond_5

    if-nez p2, :cond_0

    goto/16 :goto_1

    :cond_0
    invoke-direct {p0}, Lcom/bytedance/adsdk/lottie/BcC;->eh()V

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->iT:Landroid/graphics/Matrix;

    invoke-virtual {p1, v0}, Landroid/graphics/Canvas;->getMatrix(Landroid/graphics/Matrix;)V

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->rXP:Landroid/graphics/Rect;

    invoke-virtual {p1, v0}, Landroid/graphics/Canvas;->getClipBounds(Landroid/graphics/Rect;)Z

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->rXP:Landroid/graphics/Rect;

    iget-object v1, p0, Lcom/bytedance/adsdk/lottie/BcC;->fj:Landroid/graphics/RectF;

    invoke-direct {p0, v0, v1}, Lcom/bytedance/adsdk/lottie/BcC;->Fj(Landroid/graphics/Rect;Landroid/graphics/RectF;)V

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->iT:Landroid/graphics/Matrix;

    iget-object v1, p0, Lcom/bytedance/adsdk/lottie/BcC;->fj:Landroid/graphics/RectF;

    invoke-virtual {v0, v1}, Landroid/graphics/Matrix;->mapRect(Landroid/graphics/RectF;)Z

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->fj:Landroid/graphics/RectF;

    iget-object v1, p0, Lcom/bytedance/adsdk/lottie/BcC;->rXP:Landroid/graphics/Rect;

    invoke-direct {p0, v0, v1}, Lcom/bytedance/adsdk/lottie/BcC;->Fj(Landroid/graphics/RectF;Landroid/graphics/Rect;)V

    iget-boolean v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->vYf:Z

    const/4 v1, 0x0

    if-eqz v0, :cond_1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->spi:Landroid/graphics/RectF;

    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/BcC;->getIntrinsicWidth()I

    move-result v2

    int-to-float v2, v2

    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/BcC;->getIntrinsicHeight()I

    move-result v3

    int-to-float v3, v3

    const/4 v4, 0x0

    invoke-virtual {v0, v4, v4, v2, v3}, Landroid/graphics/RectF;->set(FFFF)V

    goto :goto_0

    :cond_1
    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->spi:Landroid/graphics/RectF;

    const/4 v2, 0x0

    invoke-virtual {p2, v0, v2, v1}, Lcom/bytedance/adsdk/lottie/hjc/hjc/ex;->Fj(Landroid/graphics/RectF;Landroid/graphics/Matrix;Z)V

    :goto_0
    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->iT:Landroid/graphics/Matrix;

    iget-object v2, p0, Lcom/bytedance/adsdk/lottie/BcC;->spi:Landroid/graphics/RectF;

    invoke-virtual {v0, v2}, Landroid/graphics/Matrix;->mapRect(Landroid/graphics/RectF;)Z

    invoke-virtual {p0}, Landroid/graphics/drawable/Drawable;->getBounds()Landroid/graphics/Rect;

    move-result-object v0

    invoke-virtual {v0}, Landroid/graphics/Rect;->width()I

    move-result v2

    int-to-float v2, v2

    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/BcC;->getIntrinsicWidth()I

    move-result v3

    int-to-float v3, v3

    div-float/2addr v2, v3

    invoke-virtual {v0}, Landroid/graphics/Rect;->height()I

    move-result v0

    int-to-float v0, v0

    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/BcC;->getIntrinsicHeight()I

    move-result v3

    int-to-float v3, v3

    div-float/2addr v0, v3

    iget-object v3, p0, Lcom/bytedance/adsdk/lottie/BcC;->spi:Landroid/graphics/RectF;

    invoke-direct {p0, v3, v2, v0}, Lcom/bytedance/adsdk/lottie/BcC;->Fj(Landroid/graphics/RectF;FF)V

    invoke-direct {p0}, Lcom/bytedance/adsdk/lottie/BcC;->kF()Z

    move-result v3

    if-nez v3, :cond_2

    iget-object v3, p0, Lcom/bytedance/adsdk/lottie/BcC;->spi:Landroid/graphics/RectF;

    iget-object v4, p0, Lcom/bytedance/adsdk/lottie/BcC;->rXP:Landroid/graphics/Rect;

    iget v5, v4, Landroid/graphics/Rect;->left:I

    int-to-float v5, v5

    iget v6, v4, Landroid/graphics/Rect;->top:I

    int-to-float v6, v6

    iget v7, v4, Landroid/graphics/Rect;->right:I

    int-to-float v7, v7

    iget v4, v4, Landroid/graphics/Rect;->bottom:I

    int-to-float v4, v4

    invoke-virtual {v3, v5, v6, v7, v4}, Landroid/graphics/RectF;->intersect(FFFF)Z

    :cond_2
    iget-object v3, p0, Lcom/bytedance/adsdk/lottie/BcC;->spi:Landroid/graphics/RectF;

    invoke-virtual {v3}, Landroid/graphics/RectF;->width()F

    move-result v3

    float-to-double v3, v3

    invoke-static {v3, v4}, Ljava/lang/Math;->ceil(D)D

    move-result-wide v3

    double-to-int v3, v3

    iget-object v4, p0, Lcom/bytedance/adsdk/lottie/BcC;->spi:Landroid/graphics/RectF;

    invoke-virtual {v4}, Landroid/graphics/RectF;->height()F

    move-result v4

    float-to-double v4, v4

    invoke-static {v4, v5}, Ljava/lang/Math;->ceil(D)D

    move-result-wide v4

    double-to-int v4, v4

    if-eqz v3, :cond_5

    if-nez v4, :cond_3

    goto :goto_1

    :cond_3
    invoke-direct {p0, v3, v4}, Lcom/bytedance/adsdk/lottie/BcC;->ex(II)V

    iget-boolean v5, p0, Lcom/bytedance/adsdk/lottie/BcC;->oX:Z

    if-eqz v5, :cond_4

    iget-object v5, p0, Lcom/bytedance/adsdk/lottie/BcC;->rf:Landroid/graphics/Matrix;

    iget-object v6, p0, Lcom/bytedance/adsdk/lottie/BcC;->iT:Landroid/graphics/Matrix;

    invoke-virtual {v5, v6}, Landroid/graphics/Matrix;->set(Landroid/graphics/Matrix;)V

    iget-object v5, p0, Lcom/bytedance/adsdk/lottie/BcC;->rf:Landroid/graphics/Matrix;

    invoke-virtual {v5, v2, v0}, Landroid/graphics/Matrix;->preScale(FF)Z

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->rf:Landroid/graphics/Matrix;

    iget-object v2, p0, Lcom/bytedance/adsdk/lottie/BcC;->spi:Landroid/graphics/RectF;

    iget v5, v2, Landroid/graphics/RectF;->left:F

    neg-float v5, v5

    iget v2, v2, Landroid/graphics/RectF;->top:F

    neg-float v2, v2

    invoke-virtual {v0, v5, v2}, Landroid/graphics/Matrix;->postTranslate(FF)Z

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->uy:Landroid/graphics/Bitmap;

    invoke-virtual {v0, v1}, Landroid/graphics/Bitmap;->eraseColor(I)V

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->lv:Landroid/graphics/Canvas;

    iget-object v2, p0, Lcom/bytedance/adsdk/lottie/BcC;->rf:Landroid/graphics/Matrix;

    iget v5, p0, Lcom/bytedance/adsdk/lottie/BcC;->Af:I

    invoke-virtual {p2, v0, v2, v5}, Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;->Fj(Landroid/graphics/Canvas;Landroid/graphics/Matrix;I)V

    iget-object p2, p0, Lcom/bytedance/adsdk/lottie/BcC;->iT:Landroid/graphics/Matrix;

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->efV:Landroid/graphics/Matrix;

    invoke-virtual {p2, v0}, Landroid/graphics/Matrix;->invert(Landroid/graphics/Matrix;)Z

    iget-object p2, p0, Lcom/bytedance/adsdk/lottie/BcC;->efV:Landroid/graphics/Matrix;

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->gXF:Landroid/graphics/RectF;

    iget-object v2, p0, Lcom/bytedance/adsdk/lottie/BcC;->spi:Landroid/graphics/RectF;

    invoke-virtual {p2, v0, v2}, Landroid/graphics/Matrix;->mapRect(Landroid/graphics/RectF;Landroid/graphics/RectF;)Z

    iget-object p2, p0, Lcom/bytedance/adsdk/lottie/BcC;->gXF:Landroid/graphics/RectF;

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->uM:Landroid/graphics/Rect;

    invoke-direct {p0, p2, v0}, Lcom/bytedance/adsdk/lottie/BcC;->Fj(Landroid/graphics/RectF;Landroid/graphics/Rect;)V

    :cond_4
    iget-object p2, p0, Lcom/bytedance/adsdk/lottie/BcC;->kF:Landroid/graphics/Rect;

    invoke-virtual {p2, v1, v1, v3, v4}, Landroid/graphics/Rect;->set(IIII)V

    iget-object p2, p0, Lcom/bytedance/adsdk/lottie/BcC;->uy:Landroid/graphics/Bitmap;

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->kF:Landroid/graphics/Rect;

    iget-object v1, p0, Lcom/bytedance/adsdk/lottie/BcC;->uM:Landroid/graphics/Rect;

    iget-object v2, p0, Lcom/bytedance/adsdk/lottie/BcC;->eh:Landroid/graphics/Paint;

    invoke-virtual {p1, p2, v0, v1, v2}, Landroid/graphics/Canvas;->drawBitmap(Landroid/graphics/Bitmap;Landroid/graphics/Rect;Landroid/graphics/Rect;Landroid/graphics/Paint;)V

    :cond_5
    :goto_1
    return-void
.end method

.method private Fj(Landroid/graphics/Rect;Landroid/graphics/RectF;)V
    .locals 3

    iget v0, p1, Landroid/graphics/Rect;->left:I

    int-to-float v0, v0

    iget v1, p1, Landroid/graphics/Rect;->top:I

    int-to-float v1, v1

    iget v2, p1, Landroid/graphics/Rect;->right:I

    int-to-float v2, v2

    iget p1, p1, Landroid/graphics/Rect;->bottom:I

    int-to-float p1, p1

    invoke-virtual {p2, v0, v1, v2, p1}, Landroid/graphics/RectF;->set(FFFF)V

    return-void
.end method

.method private Fj(Landroid/graphics/RectF;FF)V
    .locals 3

    iget v0, p1, Landroid/graphics/RectF;->left:F

    mul-float v0, v0, p2

    iget v1, p1, Landroid/graphics/RectF;->top:F

    mul-float v1, v1, p3

    iget v2, p1, Landroid/graphics/RectF;->right:F

    mul-float v2, v2, p2

    iget p2, p1, Landroid/graphics/RectF;->bottom:F

    mul-float p2, p2, p3

    invoke-virtual {p1, v0, v1, v2, p2}, Landroid/graphics/RectF;->set(FFFF)V

    return-void
.end method

.method private Fj(Landroid/graphics/RectF;Landroid/graphics/Rect;)V
    .locals 5

    iget v0, p1, Landroid/graphics/RectF;->left:F

    float-to-double v0, v0

    invoke-static {v0, v1}, Ljava/lang/Math;->floor(D)D

    move-result-wide v0

    double-to-int v0, v0

    iget v1, p1, Landroid/graphics/RectF;->top:F

    float-to-double v1, v1

    invoke-static {v1, v2}, Ljava/lang/Math;->floor(D)D

    move-result-wide v1

    double-to-int v1, v1

    iget v2, p1, Landroid/graphics/RectF;->right:F

    float-to-double v2, v2

    invoke-static {v2, v3}, Ljava/lang/Math;->ceil(D)D

    move-result-wide v2

    double-to-int v2, v2

    iget p1, p1, Landroid/graphics/RectF;->bottom:F

    float-to-double v3, p1

    invoke-static {v3, v4}, Ljava/lang/Math;->ceil(D)D

    move-result-wide v3

    double-to-int p1, v3

    invoke-virtual {p2, v0, v1, v2, p1}, Landroid/graphics/Rect;->set(IIII)V

    return-void
.end method

.method private Moo()V
    .locals 4

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->eV:Lcom/bytedance/adsdk/lottie/WR;

    if-nez v0, :cond_0

    return-void

    :cond_0
    iget-object v1, p0, Lcom/bytedance/adsdk/lottie/BcC;->Vq:Lcom/bytedance/adsdk/lottie/rS;

    sget v2, Landroid/os/Build$VERSION;->SDK_INT:I

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/WR;->Fj()Z

    move-result v3

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/WR;->ex()I

    move-result v0

    invoke-virtual {v1, v2, v3, v0}, Lcom/bytedance/adsdk/lottie/rS;->Fj(IZI)Z

    move-result v0

    iput-boolean v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->Moo:Z

    return-void
.end method

.method private eh()V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->lv:Landroid/graphics/Canvas;

    if-eqz v0, :cond_0

    return-void

    :cond_0
    new-instance v0, Landroid/graphics/Canvas;

    invoke-direct {v0}, Landroid/graphics/Canvas;-><init>()V

    iput-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->lv:Landroid/graphics/Canvas;

    new-instance v0, Landroid/graphics/RectF;

    invoke-direct {v0}, Landroid/graphics/RectF;-><init>()V

    iput-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->spi:Landroid/graphics/RectF;

    new-instance v0, Landroid/graphics/Matrix;

    invoke-direct {v0}, Landroid/graphics/Matrix;-><init>()V

    iput-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->iT:Landroid/graphics/Matrix;

    new-instance v0, Landroid/graphics/Matrix;

    invoke-direct {v0}, Landroid/graphics/Matrix;-><init>()V

    iput-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->efV:Landroid/graphics/Matrix;

    new-instance v0, Landroid/graphics/Rect;

    invoke-direct {v0}, Landroid/graphics/Rect;-><init>()V

    iput-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->rXP:Landroid/graphics/Rect;

    new-instance v0, Landroid/graphics/RectF;

    invoke-direct {v0}, Landroid/graphics/RectF;-><init>()V

    iput-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->fj:Landroid/graphics/RectF;

    new-instance v0, Lcom/bytedance/adsdk/lottie/Fj/Fj;

    invoke-direct {v0}, Lcom/bytedance/adsdk/lottie/Fj/Fj;-><init>()V

    iput-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->eh:Landroid/graphics/Paint;

    new-instance v0, Landroid/graphics/Rect;

    invoke-direct {v0}, Landroid/graphics/Rect;-><init>()V

    iput-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->kF:Landroid/graphics/Rect;

    new-instance v0, Landroid/graphics/Rect;

    invoke-direct {v0}, Landroid/graphics/Rect;-><init>()V

    iput-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->uM:Landroid/graphics/Rect;

    new-instance v0, Landroid/graphics/RectF;

    invoke-direct {v0}, Landroid/graphics/RectF;-><init>()V

    iput-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->gXF:Landroid/graphics/RectF;

    return-void
.end method

.method public static synthetic ex(Lcom/bytedance/adsdk/lottie/BcC;)Lcom/bytedance/adsdk/lottie/WR/hjc;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/adsdk/lottie/BcC;->Ubf:Lcom/bytedance/adsdk/lottie/WR/hjc;

    return-object p0
.end method

.method private ex(II)V
    .locals 3

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->uy:Landroid/graphics/Bitmap;

    const/4 v1, 0x1

    if-eqz v0, :cond_3

    invoke-virtual {v0}, Landroid/graphics/Bitmap;->getWidth()I

    move-result v0

    if-lt v0, p1, :cond_3

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->uy:Landroid/graphics/Bitmap;

    invoke-virtual {v0}, Landroid/graphics/Bitmap;->getHeight()I

    move-result v0

    if-ge v0, p2, :cond_0

    goto :goto_0

    :cond_0
    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->uy:Landroid/graphics/Bitmap;

    invoke-virtual {v0}, Landroid/graphics/Bitmap;->getWidth()I

    move-result v0

    if-gt v0, p1, :cond_1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->uy:Landroid/graphics/Bitmap;

    invoke-virtual {v0}, Landroid/graphics/Bitmap;->getHeight()I

    move-result v0

    if-le v0, p2, :cond_2

    :cond_1
    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->uy:Landroid/graphics/Bitmap;

    const/4 v2, 0x0

    invoke-static {v0, v2, v2, p1, p2}, Landroid/graphics/Bitmap;->createBitmap(Landroid/graphics/Bitmap;IIII)Landroid/graphics/Bitmap;

    move-result-object p1

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/BcC;->uy:Landroid/graphics/Bitmap;

    iget-object p2, p0, Lcom/bytedance/adsdk/lottie/BcC;->lv:Landroid/graphics/Canvas;

    invoke-virtual {p2, p1}, Landroid/graphics/Canvas;->setBitmap(Landroid/graphics/Bitmap;)V

    iput-boolean v1, p0, Lcom/bytedance/adsdk/lottie/BcC;->oX:Z

    :cond_2
    return-void

    :cond_3
    :goto_0
    sget-object v0, Landroid/graphics/Bitmap$Config;->ARGB_8888:Landroid/graphics/Bitmap$Config;

    invoke-static {p1, p2, v0}, Landroid/graphics/Bitmap;->createBitmap(IILandroid/graphics/Bitmap$Config;)Landroid/graphics/Bitmap;

    move-result-object p1

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/BcC;->uy:Landroid/graphics/Bitmap;

    iget-object p2, p0, Lcom/bytedance/adsdk/lottie/BcC;->lv:Landroid/graphics/Canvas;

    invoke-virtual {p2, p1}, Landroid/graphics/Canvas;->setBitmap(Landroid/graphics/Bitmap;)V

    iput-boolean v1, p0, Lcom/bytedance/adsdk/lottie/BcC;->oX:Z

    return-void
.end method

.method private fj()Landroid/content/Context;
    .locals 3

    invoke-virtual {p0}, Landroid/graphics/drawable/Drawable;->getCallback()Landroid/graphics/drawable/Drawable$Callback;

    move-result-object v0

    const/4 v1, 0x0

    if-nez v0, :cond_0

    return-object v1

    :cond_0
    instance-of v2, v0, Landroid/view/View;

    if-eqz v2, :cond_1

    check-cast v0, Landroid/view/View;

    invoke-virtual {v0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    return-object v0

    :cond_1
    return-object v1
.end method

.method private kF()Z
    .locals 3

    invoke-virtual {p0}, Landroid/graphics/drawable/Drawable;->getCallback()Landroid/graphics/drawable/Drawable$Callback;

    move-result-object v0

    instance-of v1, v0, Landroid/view/View;

    const/4 v2, 0x0

    if-nez v1, :cond_0

    return v2

    :cond_0
    check-cast v0, Landroid/view/View;

    invoke-virtual {v0}, Landroid/view/View;->getParent()Landroid/view/ViewParent;

    move-result-object v0

    instance-of v1, v0, Landroid/view/ViewGroup;

    if-eqz v1, :cond_1

    check-cast v0, Landroid/view/ViewGroup;

    invoke-virtual {v0}, Landroid/view/ViewGroup;->getClipChildren()Z

    move-result v0

    if-nez v0, :cond_1

    const/4 v0, 0x1

    return v0

    :cond_1
    return v2
.end method

.method private lv()Lcom/bytedance/adsdk/lottie/ex/ex;
    .locals 5

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->UYd:Lcom/bytedance/adsdk/lottie/ex/ex;

    if-eqz v0, :cond_0

    invoke-direct {p0}, Lcom/bytedance/adsdk/lottie/BcC;->fj()Landroid/content/Context;

    move-result-object v1

    invoke-virtual {v0, v1}, Lcom/bytedance/adsdk/lottie/ex/ex;->Fj(Landroid/content/Context;)Z

    move-result v0

    if-nez v0, :cond_0

    const/4 v0, 0x0

    iput-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->UYd:Lcom/bytedance/adsdk/lottie/ex/ex;

    :cond_0
    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->UYd:Lcom/bytedance/adsdk/lottie/ex/ex;

    if-nez v0, :cond_1

    new-instance v0, Lcom/bytedance/adsdk/lottie/ex/ex;

    invoke-virtual {p0}, Landroid/graphics/drawable/Drawable;->getCallback()Landroid/graphics/drawable/Drawable$Callback;

    move-result-object v1

    iget-object v2, p0, Lcom/bytedance/adsdk/lottie/BcC;->dG:Ljava/lang/String;

    iget-object v3, p0, Lcom/bytedance/adsdk/lottie/BcC;->Tc:Lcom/bytedance/adsdk/lottie/eV;

    iget-object v4, p0, Lcom/bytedance/adsdk/lottie/BcC;->eV:Lcom/bytedance/adsdk/lottie/WR;

    invoke-virtual {v4}, Lcom/bytedance/adsdk/lottie/WR;->UYd()Ljava/util/Map;

    move-result-object v4

    invoke-direct {v0, v1, v2, v3, v4}, Lcom/bytedance/adsdk/lottie/ex/ex;-><init>(Landroid/graphics/drawable/Drawable$Callback;Ljava/lang/String;Lcom/bytedance/adsdk/lottie/eV;Ljava/util/Map;)V

    iput-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->UYd:Lcom/bytedance/adsdk/lottie/ex/ex;

    :cond_1
    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->UYd:Lcom/bytedance/adsdk/lottie/ex/ex;

    return-object v0
.end method

.method private rXP()Lcom/bytedance/adsdk/lottie/ex/Fj;
    .locals 3

    invoke-virtual {p0}, Landroid/graphics/drawable/Drawable;->getCallback()Landroid/graphics/drawable/Drawable$Callback;

    move-result-object v0

    if-nez v0, :cond_0

    const/4 v0, 0x0

    return-object v0

    :cond_0
    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->JW:Lcom/bytedance/adsdk/lottie/ex/Fj;

    if-nez v0, :cond_1

    new-instance v0, Lcom/bytedance/adsdk/lottie/ex/Fj;

    invoke-virtual {p0}, Landroid/graphics/drawable/Drawable;->getCallback()Landroid/graphics/drawable/Drawable$Callback;

    move-result-object v1

    iget-object v2, p0, Lcom/bytedance/adsdk/lottie/BcC;->ex:Lcom/bytedance/adsdk/lottie/hjc;

    invoke-direct {v0, v1, v2}, Lcom/bytedance/adsdk/lottie/ex/Fj;-><init>(Landroid/graphics/drawable/Drawable$Callback;Lcom/bytedance/adsdk/lottie/hjc;)V

    iput-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->JW:Lcom/bytedance/adsdk/lottie/ex/Fj;

    iget-object v1, p0, Lcom/bytedance/adsdk/lottie/BcC;->Fj:Ljava/lang/String;

    if-eqz v1, :cond_1

    invoke-virtual {v0, v1}, Lcom/bytedance/adsdk/lottie/ex/Fj;->Fj(Ljava/lang/String;)V

    :cond_1
    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->JW:Lcom/bytedance/adsdk/lottie/ex/Fj;

    return-object v0
.end method

.method private rf()V
    .locals 4

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->eV:Lcom/bytedance/adsdk/lottie/WR;

    if-nez v0, :cond_0

    return-void

    :cond_0
    new-instance v1, Lcom/bytedance/adsdk/lottie/hjc/hjc/ex;

    invoke-static {v0}, Lcom/bytedance/adsdk/lottie/Ubf/mC;->Fj(Lcom/bytedance/adsdk/lottie/WR;)Lcom/bytedance/adsdk/lottie/hjc/hjc/eV;

    move-result-object v2

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/WR;->mSE()Ljava/util/List;

    move-result-object v3

    invoke-direct {v1, p0, v2, v3, v0}, Lcom/bytedance/adsdk/lottie/hjc/hjc/ex;-><init>(Lcom/bytedance/adsdk/lottie/BcC;Lcom/bytedance/adsdk/lottie/hjc/hjc/eV;Ljava/util/List;Lcom/bytedance/adsdk/lottie/WR;)V

    iput-object v1, p0, Lcom/bytedance/adsdk/lottie/BcC;->mE:Lcom/bytedance/adsdk/lottie/hjc/hjc/ex;

    iget-boolean v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->cB:Z

    if-eqz v0, :cond_1

    const/4 v0, 0x1

    invoke-virtual {v1, v0}, Lcom/bytedance/adsdk/lottie/hjc/hjc/ex;->Fj(Z)V

    :cond_1
    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->mE:Lcom/bytedance/adsdk/lottie/hjc/hjc/ex;

    iget-boolean v1, p0, Lcom/bytedance/adsdk/lottie/BcC;->vYf:Z

    invoke-virtual {v0, v1}, Lcom/bytedance/adsdk/lottie/hjc/hjc/ex;->ex(Z)V

    return-void
.end method

.method private uy()Z
    .locals 1

    iget-boolean v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->WR:Z

    if-nez v0, :cond_1

    iget-boolean v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->svN:Z

    if-eqz v0, :cond_0

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    return v0

    :cond_1
    :goto_0
    const/4 v0, 0x1

    return v0
.end method


# virtual methods
.method public Af()Z
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->JU:Ljava/util/Map;

    if-nez v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->hjc:Lcom/bytedance/adsdk/lottie/vYf;

    if-nez v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->eV:Lcom/bytedance/adsdk/lottie/WR;

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/WR;->Ko()Landroid/util/SparseArray;

    move-result-object v0

    invoke-virtual {v0}, Landroid/util/SparseArray;->size()I

    move-result v0

    if-lez v0, :cond_0

    const/4 v0, 0x1

    return v0

    :cond_0
    const/4 v0, 0x0

    return v0
.end method

.method public BcC()V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->Ubf:Lcom/bytedance/adsdk/lottie/WR/hjc;

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/WR/hjc;->isRunning()Z

    move-result v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->Ubf:Lcom/bytedance/adsdk/lottie/WR/hjc;

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/WR/hjc;->cancel()V

    invoke-virtual {p0}, Landroid/graphics/drawable/Drawable;->isVisible()Z

    move-result v0

    if-nez v0, :cond_0

    sget-object v0, Lcom/bytedance/adsdk/lottie/BcC$ex;->Fj:Lcom/bytedance/adsdk/lottie/BcC$ex;

    iput-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->mSE:Lcom/bytedance/adsdk/lottie/BcC$ex;

    :cond_0
    const/4 v0, 0x0

    iput-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->eV:Lcom/bytedance/adsdk/lottie/WR;

    iput-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->mE:Lcom/bytedance/adsdk/lottie/hjc/hjc/ex;

    iput-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->UYd:Lcom/bytedance/adsdk/lottie/ex/ex;

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->Ubf:Lcom/bytedance/adsdk/lottie/WR/hjc;

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/WR/hjc;->BcC()V

    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/BcC;->invalidateSelf()V

    return-void
.end method

.method public BcC(Z)V
    .locals 0

    iput-boolean p1, p0, Lcom/bytedance/adsdk/lottie/BcC;->svN:Z

    return-void
.end method

.method public Fj(Ljava/lang/String;Landroid/graphics/Bitmap;)Landroid/graphics/Bitmap;
    .locals 1

    invoke-direct {p0}, Lcom/bytedance/adsdk/lottie/BcC;->lv()Lcom/bytedance/adsdk/lottie/ex/ex;

    move-result-object v0

    if-nez v0, :cond_0

    const/4 p1, 0x0

    return-object p1

    :cond_0
    invoke-virtual {v0, p1, p2}, Lcom/bytedance/adsdk/lottie/ex/ex;->Fj(Ljava/lang/String;Landroid/graphics/Bitmap;)Landroid/graphics/Bitmap;

    move-result-object p1

    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/BcC;->invalidateSelf()V

    return-object p1
.end method

.method public Fj(Lcom/bytedance/adsdk/lottie/hjc/hjc;)Landroid/graphics/Typeface;
    .locals 3
    .annotation build Lcom/bytedance/component/sdk/annotation/RestrictTo;
        value = {
            .enum Lcom/bytedance/component/sdk/annotation/RestrictTo$Scope;->LIBRARY:Lcom/bytedance/component/sdk/annotation/RestrictTo$Scope;
        }
    .end annotation

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->JU:Ljava/util/Map;

    if-eqz v0, :cond_2

    invoke-virtual {p1}, Lcom/bytedance/adsdk/lottie/hjc/hjc;->Fj()Ljava/lang/String;

    move-result-object v1

    invoke-interface {v0, v1}, Ljava/util/Map;->containsKey(Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_0

    invoke-interface {v0, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Landroid/graphics/Typeface;

    return-object p1

    :cond_0
    invoke-virtual {p1}, Lcom/bytedance/adsdk/lottie/hjc/hjc;->ex()Ljava/lang/String;

    move-result-object v1

    invoke-interface {v0, v1}, Ljava/util/Map;->containsKey(Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_1

    invoke-interface {v0, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Landroid/graphics/Typeface;

    return-object p1

    :cond_1
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {p1}, Lcom/bytedance/adsdk/lottie/hjc/hjc;->Fj()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v2, "-"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p1}, Lcom/bytedance/adsdk/lottie/hjc/hjc;->hjc()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-interface {v0, v1}, Ljava/util/Map;->containsKey(Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_2

    invoke-interface {v0, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Landroid/graphics/Typeface;

    return-object p1

    :cond_2
    invoke-direct {p0}, Lcom/bytedance/adsdk/lottie/BcC;->rXP()Lcom/bytedance/adsdk/lottie/ex/Fj;

    move-result-object v0

    if-eqz v0, :cond_3

    invoke-virtual {v0, p1}, Lcom/bytedance/adsdk/lottie/ex/Fj;->Fj(Lcom/bytedance/adsdk/lottie/hjc/hjc;)Landroid/graphics/Typeface;

    move-result-object p1

    return-object p1

    :cond_3
    const/4 p1, 0x0

    return-object p1
.end method

.method public Fj(F)V
    .locals 2

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->eV:Lcom/bytedance/adsdk/lottie/WR;

    if-nez v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->Ko:Ljava/util/ArrayList;

    new-instance v1, Lcom/bytedance/adsdk/lottie/BcC$9;

    invoke-direct {v1, p0, p1}, Lcom/bytedance/adsdk/lottie/BcC$9;-><init>(Lcom/bytedance/adsdk/lottie/BcC;F)V

    invoke-virtual {v0, v1}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    return-void

    :cond_0
    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/WR;->WR()F

    move-result v0

    iget-object v1, p0, Lcom/bytedance/adsdk/lottie/BcC;->eV:Lcom/bytedance/adsdk/lottie/WR;

    invoke-virtual {v1}, Lcom/bytedance/adsdk/lottie/WR;->svN()F

    move-result v1

    invoke-static {v0, v1, p1}, Lcom/bytedance/adsdk/lottie/WR/Ubf;->Fj(FFF)F

    move-result p1

    float-to-int p1, p1

    invoke-virtual {p0, p1}, Lcom/bytedance/adsdk/lottie/BcC;->Fj(I)V

    return-void
.end method

.method public Fj(I)V
    .locals 2

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->eV:Lcom/bytedance/adsdk/lottie/WR;

    if-nez v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->Ko:Ljava/util/ArrayList;

    new-instance v1, Lcom/bytedance/adsdk/lottie/BcC$8;

    invoke-direct {v1, p0, p1}, Lcom/bytedance/adsdk/lottie/BcC$8;-><init>(Lcom/bytedance/adsdk/lottie/BcC;I)V

    invoke-virtual {v0, v1}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    return-void

    :cond_0
    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->Ubf:Lcom/bytedance/adsdk/lottie/WR/hjc;

    invoke-virtual {v0, p1}, Lcom/bytedance/adsdk/lottie/WR/hjc;->Fj(I)V

    return-void
.end method

.method public Fj(II)V
    .locals 2

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->eV:Lcom/bytedance/adsdk/lottie/WR;

    if-nez v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->Ko:Ljava/util/ArrayList;

    new-instance v1, Lcom/bytedance/adsdk/lottie/BcC$3;

    invoke-direct {v1, p0, p1, p2}, Lcom/bytedance/adsdk/lottie/BcC$3;-><init>(Lcom/bytedance/adsdk/lottie/BcC;II)V

    invoke-virtual {v0, v1}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    return-void

    :cond_0
    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->Ubf:Lcom/bytedance/adsdk/lottie/WR/hjc;

    int-to-float p1, p1

    int-to-float p2, p2

    const v1, 0x3f7d70a4    # 0.99f

    add-float/2addr p2, v1

    invoke-virtual {v0, p1, p2}, Lcom/bytedance/adsdk/lottie/WR/hjc;->Fj(FF)V

    return-void
.end method

.method public Fj(Lcom/bytedance/adsdk/lottie/eV;)V
    .locals 1

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/BcC;->Tc:Lcom/bytedance/adsdk/lottie/eV;

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->UYd:Lcom/bytedance/adsdk/lottie/ex/ex;

    if-eqz v0, :cond_0

    invoke-virtual {v0, p1}, Lcom/bytedance/adsdk/lottie/ex/ex;->Fj(Lcom/bytedance/adsdk/lottie/eV;)V

    :cond_0
    return-void
.end method

.method public Fj(Lcom/bytedance/adsdk/lottie/hjc;)V
    .locals 1

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/BcC;->ex:Lcom/bytedance/adsdk/lottie/hjc;

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->JW:Lcom/bytedance/adsdk/lottie/ex/Fj;

    if-eqz v0, :cond_0

    invoke-virtual {v0, p1}, Lcom/bytedance/adsdk/lottie/ex/Fj;->Fj(Lcom/bytedance/adsdk/lottie/hjc;)V

    :cond_0
    return-void
.end method

.method public Fj(Lcom/bytedance/adsdk/lottie/rS;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/BcC;->Vq:Lcom/bytedance/adsdk/lottie/rS;

    invoke-direct {p0}, Lcom/bytedance/adsdk/lottie/BcC;->Moo()V

    return-void
.end method

.method public Fj(Lcom/bytedance/adsdk/lottie/vYf;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/BcC;->hjc:Lcom/bytedance/adsdk/lottie/vYf;

    return-void
.end method

.method public Fj(Ljava/lang/Boolean;)V
    .locals 0

    invoke-virtual {p1}, Ljava/lang/Boolean;->booleanValue()Z

    move-result p1

    iput-boolean p1, p0, Lcom/bytedance/adsdk/lottie/BcC;->WR:Z

    return-void
.end method

.method public Fj(Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/BcC;->dG:Ljava/lang/String;

    return-void
.end method

.method public Fj(Ljava/util/Map;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Landroid/graphics/Typeface;",
            ">;)V"
        }
    .end annotation

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->JU:Ljava/util/Map;

    if-ne p1, v0, :cond_0

    return-void

    :cond_0
    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/BcC;->JU:Ljava/util/Map;

    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/BcC;->invalidateSelf()V

    return-void
.end method

.method public Fj(Z)V
    .locals 1

    iget-boolean v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->Ql:Z

    if-ne v0, p1, :cond_0

    return-void

    :cond_0
    iput-boolean p1, p0, Lcom/bytedance/adsdk/lottie/BcC;->Ql:Z

    iget-object p1, p0, Lcom/bytedance/adsdk/lottie/BcC;->eV:Lcom/bytedance/adsdk/lottie/WR;

    if-eqz p1, :cond_1

    invoke-direct {p0}, Lcom/bytedance/adsdk/lottie/BcC;->rf()V

    :cond_1
    return-void
.end method

.method public Fj()Z
    .locals 1

    iget-boolean v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->Ql:Z

    return v0
.end method

.method public Fj(Lcom/bytedance/adsdk/lottie/WR;)Z
    .locals 3

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->eV:Lcom/bytedance/adsdk/lottie/WR;

    if-ne v0, p1, :cond_0

    const/4 p1, 0x0

    return p1

    :cond_0
    const/4 v0, 0x1

    iput-boolean v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->oX:Z

    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/BcC;->BcC()V

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/BcC;->eV:Lcom/bytedance/adsdk/lottie/WR;

    invoke-direct {p0}, Lcom/bytedance/adsdk/lottie/BcC;->rf()V

    iget-object v1, p0, Lcom/bytedance/adsdk/lottie/BcC;->Ubf:Lcom/bytedance/adsdk/lottie/WR/hjc;

    invoke-virtual {v1, p1}, Lcom/bytedance/adsdk/lottie/WR/hjc;->Fj(Lcom/bytedance/adsdk/lottie/WR;)V

    iget-object v1, p0, Lcom/bytedance/adsdk/lottie/BcC;->Ubf:Lcom/bytedance/adsdk/lottie/WR/hjc;

    invoke-virtual {v1}, Lcom/bytedance/adsdk/lottie/WR/hjc;->getAnimatedFraction()F

    move-result v1

    invoke-virtual {p0, v1}, Lcom/bytedance/adsdk/lottie/BcC;->eV(F)V

    new-instance v1, Ljava/util/ArrayList;

    iget-object v2, p0, Lcom/bytedance/adsdk/lottie/BcC;->Ko:Ljava/util/ArrayList;

    invoke-direct {v1, v2}, Ljava/util/ArrayList;-><init>(Ljava/util/Collection;)V

    invoke-virtual {v1}, Ljava/util/ArrayList;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :goto_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_2

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/bytedance/adsdk/lottie/BcC$Fj;

    if-eqz v2, :cond_1

    invoke-interface {v2, p1}, Lcom/bytedance/adsdk/lottie/BcC$Fj;->Fj(Lcom/bytedance/adsdk/lottie/WR;)V

    :cond_1
    invoke-interface {v1}, Ljava/util/Iterator;->remove()V

    goto :goto_0

    :cond_2
    iget-object v1, p0, Lcom/bytedance/adsdk/lottie/BcC;->Ko:Ljava/util/ArrayList;

    invoke-virtual {v1}, Ljava/util/ArrayList;->clear()V

    iget-boolean v1, p0, Lcom/bytedance/adsdk/lottie/BcC;->mC:Z

    invoke-virtual {p1, v1}, Lcom/bytedance/adsdk/lottie/WR;->ex(Z)V

    invoke-direct {p0}, Lcom/bytedance/adsdk/lottie/BcC;->Moo()V

    invoke-virtual {p0}, Landroid/graphics/drawable/Drawable;->getCallback()Landroid/graphics/drawable/Drawable$Callback;

    move-result-object p1

    instance-of v1, p1, Landroid/widget/ImageView;

    if-eqz v1, :cond_3

    check-cast p1, Landroid/widget/ImageView;

    const/4 v1, 0x0

    invoke-virtual {p1, v1}, Landroid/widget/ImageView;->setImageDrawable(Landroid/graphics/drawable/Drawable;)V

    invoke-virtual {p1, p0}, Landroid/widget/ImageView;->setImageDrawable(Landroid/graphics/drawable/Drawable;)V

    :cond_3
    return v0
.end method

.method public JU()I
    .locals 1
    .annotation build Landroid/annotation/SuppressLint;
        value = {
            "WrongConstant"
        }
    .end annotation

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->Ubf:Lcom/bytedance/adsdk/lottie/WR/hjc;

    invoke-virtual {v0}, Landroid/animation/ValueAnimator;->getRepeatMode()I

    move-result v0

    return v0
.end method

.method public JW()I
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->Ubf:Lcom/bytedance/adsdk/lottie/WR/hjc;

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/WR/hjc;->svN()F

    move-result v0

    float-to-int v0, v0

    return v0
.end method

.method public Ko()V
    .locals 1
    .annotation build Lcom/bytedance/component/sdk/annotation/MainThread;
    .end annotation

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->Ko:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->clear()V

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->Ubf:Lcom/bytedance/adsdk/lottie/WR/hjc;

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/WR/hjc;->UYd()V

    invoke-virtual {p0}, Landroid/graphics/drawable/Drawable;->isVisible()Z

    move-result v0

    if-nez v0, :cond_0

    sget-object v0, Lcom/bytedance/adsdk/lottie/BcC$ex;->Fj:Lcom/bytedance/adsdk/lottie/BcC$ex;

    iput-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->mSE:Lcom/bytedance/adsdk/lottie/BcC$ex;

    :cond_0
    return-void
.end method

.method public Ql()I
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->Ubf:Lcom/bytedance/adsdk/lottie/WR/hjc;

    invoke-virtual {v0}, Landroid/animation/ValueAnimator;->getRepeatCount()I

    move-result v0

    return v0
.end method

.method public Tc()F
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->Ubf:Lcom/bytedance/adsdk/lottie/WR/hjc;

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/WR/hjc;->Ko()F

    move-result v0

    return v0
.end method

.method public UYd()F
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->Ubf:Lcom/bytedance/adsdk/lottie/WR/hjc;

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/WR/hjc;->JW()F

    move-result v0

    return v0
.end method

.method public Ubf(Ljava/lang/String;)Landroid/graphics/Bitmap;
    .locals 1

    invoke-direct {p0}, Lcom/bytedance/adsdk/lottie/BcC;->lv()Lcom/bytedance/adsdk/lottie/ex/ex;

    move-result-object v0

    if-eqz v0, :cond_0

    invoke-virtual {v0, p1}, Lcom/bytedance/adsdk/lottie/ex/ex;->Fj(Ljava/lang/String;)Landroid/graphics/Bitmap;

    move-result-object p1

    return-object p1

    :cond_0
    const/4 p1, 0x0

    return-object p1
.end method

.method public Ubf()Lcom/bytedance/adsdk/lottie/rS;
    .locals 1

    iget-boolean v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->Moo:Z

    if-eqz v0, :cond_0

    sget-object v0, Lcom/bytedance/adsdk/lottie/rS;->hjc:Lcom/bytedance/adsdk/lottie/rS;

    return-object v0

    :cond_0
    sget-object v0, Lcom/bytedance/adsdk/lottie/rS;->ex:Lcom/bytedance/adsdk/lottie/rS;

    return-object v0
.end method

.method public Ubf(I)V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->Ubf:Lcom/bytedance/adsdk/lottie/WR/hjc;

    invoke-virtual {v0, p1}, Landroid/animation/ValueAnimator;->setRepeatCount(I)V

    return-void
.end method

.method public Ubf(Z)V
    .locals 1

    iget-boolean v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->cB:Z

    if-ne v0, p1, :cond_0

    return-void

    :cond_0
    iput-boolean p1, p0, Lcom/bytedance/adsdk/lottie/BcC;->cB:Z

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->mE:Lcom/bytedance/adsdk/lottie/hjc/hjc/ex;

    if-eqz v0, :cond_1

    invoke-virtual {v0, p1}, Lcom/bytedance/adsdk/lottie/hjc/hjc/ex;->Fj(Z)V

    :cond_1
    return-void
.end method

.method public Vq()F
    .locals 1
    .annotation build Lcom/bytedance/component/sdk/annotation/FloatRange;
        from = 0.0
        to = 1.0
    .end annotation

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->Ubf:Lcom/bytedance/adsdk/lottie/WR/hjc;

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/WR/hjc;->WR()F

    move-result v0

    return v0
.end method

.method public WR()Lcom/bytedance/adsdk/lottie/Ql;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->eV:Lcom/bytedance/adsdk/lottie/WR;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/WR;->hjc()Lcom/bytedance/adsdk/lottie/Ql;

    move-result-object v0

    return-object v0

    :cond_0
    const/4 v0, 0x0

    return-object v0
.end method

.method public WR(Ljava/lang/String;)Lcom/bytedance/adsdk/lottie/mSE;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->eV:Lcom/bytedance/adsdk/lottie/WR;

    if-nez v0, :cond_0

    const/4 p1, 0x0

    return-object p1

    :cond_0
    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/WR;->UYd()Ljava/util/Map;

    move-result-object v0

    invoke-interface {v0, p1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/bytedance/adsdk/lottie/mSE;

    return-object p1
.end method

.method public WR(Z)V
    .locals 0

    iput-boolean p1, p0, Lcom/bytedance/adsdk/lottie/BcC;->nsB:Z

    return-void
.end method

.method public cB()V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->Ko:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->clear()V

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->Ubf:Lcom/bytedance/adsdk/lottie/WR/hjc;

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/WR/hjc;->cancel()V

    invoke-virtual {p0}, Landroid/graphics/drawable/Drawable;->isVisible()Z

    move-result v0

    if-nez v0, :cond_0

    sget-object v0, Lcom/bytedance/adsdk/lottie/BcC$ex;->Fj:Lcom/bytedance/adsdk/lottie/BcC$ex;

    iput-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->mSE:Lcom/bytedance/adsdk/lottie/BcC$ex;

    :cond_0
    return-void
.end method

.method public dG()F
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->Ubf:Lcom/bytedance/adsdk/lottie/WR/hjc;

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/WR/hjc;->JU()F

    move-result v0

    return v0
.end method

.method public draw(Landroid/graphics/Canvas;)V
    .locals 2

    const-string v0, "Drawable#draw"

    invoke-static {v0}, Lcom/bytedance/adsdk/lottie/Ubf;->Fj(Ljava/lang/String;)V

    iget-boolean v1, p0, Lcom/bytedance/adsdk/lottie/BcC;->BcC:Z

    if-eqz v1, :cond_1

    :try_start_0
    iget-boolean v1, p0, Lcom/bytedance/adsdk/lottie/BcC;->Moo:Z

    if-eqz v1, :cond_0

    iget-object v1, p0, Lcom/bytedance/adsdk/lottie/BcC;->mE:Lcom/bytedance/adsdk/lottie/hjc/hjc/ex;

    invoke-direct {p0, p1, v1}, Lcom/bytedance/adsdk/lottie/BcC;->Fj(Landroid/graphics/Canvas;Lcom/bytedance/adsdk/lottie/hjc/hjc/ex;)V

    goto :goto_0

    :cond_0
    invoke-direct {p0, p1}, Lcom/bytedance/adsdk/lottie/BcC;->Fj(Landroid/graphics/Canvas;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    goto :goto_0

    :cond_1
    iget-boolean v1, p0, Lcom/bytedance/adsdk/lottie/BcC;->Moo:Z

    if-eqz v1, :cond_2

    iget-object v1, p0, Lcom/bytedance/adsdk/lottie/BcC;->mE:Lcom/bytedance/adsdk/lottie/hjc/hjc/ex;

    invoke-direct {p0, p1, v1}, Lcom/bytedance/adsdk/lottie/BcC;->Fj(Landroid/graphics/Canvas;Lcom/bytedance/adsdk/lottie/hjc/hjc/ex;)V

    goto :goto_0

    :cond_2
    invoke-direct {p0, p1}, Lcom/bytedance/adsdk/lottie/BcC;->Fj(Landroid/graphics/Canvas;)V

    :catchall_0
    :goto_0
    const/4 p1, 0x0

    iput-boolean p1, p0, Lcom/bytedance/adsdk/lottie/BcC;->oX:Z

    invoke-static {v0}, Lcom/bytedance/adsdk/lottie/Ubf;->ex(Ljava/lang/String;)F

    return-void
.end method

.method public eV(F)V
    .locals 3
    .param p1    # F
        .annotation build Lcom/bytedance/component/sdk/annotation/FloatRange;
            from = 0.0
            to = 1.0
        .end annotation
    .end param

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->eV:Lcom/bytedance/adsdk/lottie/WR;

    if-nez v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->Ko:Ljava/util/ArrayList;

    new-instance v1, Lcom/bytedance/adsdk/lottie/BcC$5;

    invoke-direct {v1, p0, p1}, Lcom/bytedance/adsdk/lottie/BcC$5;-><init>(Lcom/bytedance/adsdk/lottie/BcC;F)V

    invoke-virtual {v0, v1}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    return-void

    :cond_0
    const-string v0, "Drawable#setProgress"

    invoke-static {v0}, Lcom/bytedance/adsdk/lottie/Ubf;->Fj(Ljava/lang/String;)V

    iget-object v1, p0, Lcom/bytedance/adsdk/lottie/BcC;->Ubf:Lcom/bytedance/adsdk/lottie/WR/hjc;

    iget-object v2, p0, Lcom/bytedance/adsdk/lottie/BcC;->eV:Lcom/bytedance/adsdk/lottie/WR;

    invoke-virtual {v2, p1}, Lcom/bytedance/adsdk/lottie/WR;->Fj(F)F

    move-result p1

    invoke-virtual {v1, p1}, Lcom/bytedance/adsdk/lottie/WR/hjc;->Fj(F)V

    invoke-static {v0}, Lcom/bytedance/adsdk/lottie/Ubf;->ex(Ljava/lang/String;)F

    return-void
.end method

.method public eV(I)V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->Ubf:Lcom/bytedance/adsdk/lottie/WR/hjc;

    invoke-virtual {v0, p1}, Lcom/bytedance/adsdk/lottie/WR/hjc;->setRepeatMode(I)V

    return-void
.end method

.method public eV(Ljava/lang/String;)V
    .locals 3

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->eV:Lcom/bytedance/adsdk/lottie/WR;

    if-nez v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->Ko:Ljava/util/ArrayList;

    new-instance v1, Lcom/bytedance/adsdk/lottie/BcC$2;

    invoke-direct {v1, p0, p1}, Lcom/bytedance/adsdk/lottie/BcC$2;-><init>(Lcom/bytedance/adsdk/lottie/BcC;Ljava/lang/String;)V

    invoke-virtual {v0, v1}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    return-void

    :cond_0
    invoke-virtual {v0, p1}, Lcom/bytedance/adsdk/lottie/WR;->hjc(Ljava/lang/String;)Lcom/bytedance/adsdk/lottie/hjc/WR;

    move-result-object v0

    if-eqz v0, :cond_1

    iget p1, v0, Lcom/bytedance/adsdk/lottie/hjc/WR;->Fj:F

    float-to-int p1, p1

    iget v0, v0, Lcom/bytedance/adsdk/lottie/hjc/WR;->ex:F

    float-to-int v0, v0

    add-int/2addr v0, p1

    invoke-virtual {p0, p1, v0}, Lcom/bytedance/adsdk/lottie/BcC;->Fj(II)V

    return-void

    :cond_1
    new-instance v0, Ljava/lang/IllegalArgumentException;

    new-instance v1, Ljava/lang/StringBuilder;

    const-string v2, "Cannot find marker with name "

    invoke-direct {v1, v2}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string p1, "."

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-direct {v0, p1}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw v0
.end method

.method public eV(Z)V
    .locals 1

    iput-boolean p1, p0, Lcom/bytedance/adsdk/lottie/BcC;->mC:Z

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->eV:Lcom/bytedance/adsdk/lottie/WR;

    if-eqz v0, :cond_0

    invoke-virtual {v0, p1}, Lcom/bytedance/adsdk/lottie/WR;->ex(Z)V

    :cond_0
    return-void
.end method

.method public eV()Z
    .locals 1

    iget-boolean v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->rS:Z

    return v0
.end method

.method public ex(F)V
    .locals 3
    .param p1    # F
        .annotation build Lcom/bytedance/component/sdk/annotation/FloatRange;
            from = 0.0
            to = 1.0
        .end annotation
    .end param

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->eV:Lcom/bytedance/adsdk/lottie/WR;

    if-nez v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->Ko:Ljava/util/ArrayList;

    new-instance v1, Lcom/bytedance/adsdk/lottie/BcC$11;

    invoke-direct {v1, p0, p1}, Lcom/bytedance/adsdk/lottie/BcC$11;-><init>(Lcom/bytedance/adsdk/lottie/BcC;F)V

    invoke-virtual {v0, v1}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    return-void

    :cond_0
    iget-object v1, p0, Lcom/bytedance/adsdk/lottie/BcC;->Ubf:Lcom/bytedance/adsdk/lottie/WR/hjc;

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/WR;->WR()F

    move-result v0

    iget-object v2, p0, Lcom/bytedance/adsdk/lottie/BcC;->eV:Lcom/bytedance/adsdk/lottie/WR;

    invoke-virtual {v2}, Lcom/bytedance/adsdk/lottie/WR;->svN()F

    move-result v2

    invoke-static {v0, v2, p1}, Lcom/bytedance/adsdk/lottie/WR/Ubf;->Fj(FFF)F

    move-result p1

    invoke-virtual {v1, p1}, Lcom/bytedance/adsdk/lottie/WR/hjc;->ex(F)V

    return-void
.end method

.method public ex(I)V
    .locals 2

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->eV:Lcom/bytedance/adsdk/lottie/WR;

    if-nez v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->Ko:Ljava/util/ArrayList;

    new-instance v1, Lcom/bytedance/adsdk/lottie/BcC$10;

    invoke-direct {v1, p0, p1}, Lcom/bytedance/adsdk/lottie/BcC$10;-><init>(Lcom/bytedance/adsdk/lottie/BcC;I)V

    invoke-virtual {v0, v1}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    return-void

    :cond_0
    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->Ubf:Lcom/bytedance/adsdk/lottie/WR/hjc;

    int-to-float p1, p1

    const v1, 0x3f7d70a4    # 0.99f

    add-float/2addr p1, v1

    invoke-virtual {v0, p1}, Lcom/bytedance/adsdk/lottie/WR/hjc;->ex(F)V

    return-void
.end method

.method public ex(Ljava/lang/String;)V
    .locals 3

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->eV:Lcom/bytedance/adsdk/lottie/WR;

    if-nez v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->Ko:Ljava/util/ArrayList;

    new-instance v1, Lcom/bytedance/adsdk/lottie/BcC$12;

    invoke-direct {v1, p0, p1}, Lcom/bytedance/adsdk/lottie/BcC$12;-><init>(Lcom/bytedance/adsdk/lottie/BcC;Ljava/lang/String;)V

    invoke-virtual {v0, v1}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    return-void

    :cond_0
    invoke-virtual {v0, p1}, Lcom/bytedance/adsdk/lottie/WR;->hjc(Ljava/lang/String;)Lcom/bytedance/adsdk/lottie/hjc/WR;

    move-result-object v0

    if-eqz v0, :cond_1

    iget p1, v0, Lcom/bytedance/adsdk/lottie/hjc/WR;->Fj:F

    float-to-int p1, p1

    invoke-virtual {p0, p1}, Lcom/bytedance/adsdk/lottie/BcC;->Fj(I)V

    return-void

    :cond_1
    new-instance v0, Ljava/lang/IllegalArgumentException;

    new-instance v1, Ljava/lang/StringBuilder;

    const-string v2, "Cannot find marker with name "

    invoke-direct {v1, v2}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string p1, "."

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-direct {v0, p1}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw v0
.end method

.method public ex(Z)V
    .locals 1

    iget-boolean v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->vYf:Z

    if-eq p1, v0, :cond_1

    iput-boolean p1, p0, Lcom/bytedance/adsdk/lottie/BcC;->vYf:Z

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->mE:Lcom/bytedance/adsdk/lottie/hjc/hjc/ex;

    if-eqz v0, :cond_0

    invoke-virtual {v0, p1}, Lcom/bytedance/adsdk/lottie/hjc/hjc/ex;->ex(Z)V

    :cond_0
    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/BcC;->invalidateSelf()V

    :cond_1
    return-void
.end method

.method public ex()Z
    .locals 1

    iget-boolean v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->vYf:Z

    return v0
.end method

.method public getAlpha()I
    .locals 1

    iget v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->Af:I

    return v0
.end method

.method public getIntrinsicHeight()I
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->eV:Lcom/bytedance/adsdk/lottie/WR;

    if-nez v0, :cond_0

    const/4 v0, -0x1

    return v0

    :cond_0
    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/WR;->eV()Landroid/graphics/Rect;

    move-result-object v0

    invoke-virtual {v0}, Landroid/graphics/Rect;->height()I

    move-result v0

    return v0
.end method

.method public getIntrinsicWidth()I
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->eV:Lcom/bytedance/adsdk/lottie/WR;

    if-nez v0, :cond_0

    const/4 v0, -0x1

    return v0

    :cond_0
    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/WR;->eV()Landroid/graphics/Rect;

    move-result-object v0

    invoke-virtual {v0}, Landroid/graphics/Rect;->width()I

    move-result v0

    return v0
.end method

.method public getOpacity()I
    .locals 1

    const/4 v0, -0x3

    return v0
.end method

.method public hjc()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->dG:Ljava/lang/String;

    return-object v0
.end method

.method public hjc(F)V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->Ubf:Lcom/bytedance/adsdk/lottie/WR/hjc;

    invoke-virtual {v0, p1}, Lcom/bytedance/adsdk/lottie/WR/hjc;->hjc(F)V

    return-void
.end method

.method public hjc(I)V
    .locals 2

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->eV:Lcom/bytedance/adsdk/lottie/WR;

    if-nez v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->Ko:Ljava/util/ArrayList;

    new-instance v1, Lcom/bytedance/adsdk/lottie/BcC$4;

    invoke-direct {v1, p0, p1}, Lcom/bytedance/adsdk/lottie/BcC$4;-><init>(Lcom/bytedance/adsdk/lottie/BcC;I)V

    invoke-virtual {v0, v1}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    return-void

    :cond_0
    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->Ubf:Lcom/bytedance/adsdk/lottie/WR/hjc;

    int-to-float p1, p1

    invoke-virtual {v0, p1}, Lcom/bytedance/adsdk/lottie/WR/hjc;->Fj(F)V

    return-void
.end method

.method public hjc(Ljava/lang/String;)V
    .locals 3

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->eV:Lcom/bytedance/adsdk/lottie/WR;

    if-nez v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->Ko:Ljava/util/ArrayList;

    new-instance v1, Lcom/bytedance/adsdk/lottie/BcC$13;

    invoke-direct {v1, p0, p1}, Lcom/bytedance/adsdk/lottie/BcC$13;-><init>(Lcom/bytedance/adsdk/lottie/BcC;Ljava/lang/String;)V

    invoke-virtual {v0, v1}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    return-void

    :cond_0
    invoke-virtual {v0, p1}, Lcom/bytedance/adsdk/lottie/WR;->hjc(Ljava/lang/String;)Lcom/bytedance/adsdk/lottie/hjc/WR;

    move-result-object v0

    if-eqz v0, :cond_1

    iget p1, v0, Lcom/bytedance/adsdk/lottie/hjc/WR;->Fj:F

    iget v0, v0, Lcom/bytedance/adsdk/lottie/hjc/WR;->ex:F

    add-float/2addr p1, v0

    float-to-int p1, p1

    invoke-virtual {p0, p1}, Lcom/bytedance/adsdk/lottie/BcC;->ex(I)V

    return-void

    :cond_1
    new-instance v0, Ljava/lang/IllegalArgumentException;

    new-instance v1, Ljava/lang/StringBuilder;

    const-string v2, "Cannot find marker with name "

    invoke-direct {v1, v2}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string p1, "."

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-direct {v0, p1}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw v0
.end method

.method public hjc(Z)V
    .locals 0

    iput-boolean p1, p0, Lcom/bytedance/adsdk/lottie/BcC;->rS:Z

    return-void
.end method

.method public invalidateDrawable(Landroid/graphics/drawable/Drawable;)V
    .locals 0

    invoke-virtual {p0}, Landroid/graphics/drawable/Drawable;->getCallback()Landroid/graphics/drawable/Drawable$Callback;

    move-result-object p1

    if-nez p1, :cond_0

    return-void

    :cond_0
    invoke-interface {p1, p0}, Landroid/graphics/drawable/Drawable$Callback;->invalidateDrawable(Landroid/graphics/drawable/Drawable;)V

    return-void
.end method

.method public invalidateSelf()V
    .locals 1

    iget-boolean v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->oX:Z

    if-eqz v0, :cond_0

    return-void

    :cond_0
    const/4 v0, 0x1

    iput-boolean v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->oX:Z

    invoke-virtual {p0}, Landroid/graphics/drawable/Drawable;->getCallback()Landroid/graphics/drawable/Drawable$Callback;

    move-result-object v0

    if-eqz v0, :cond_1

    invoke-interface {v0, p0}, Landroid/graphics/drawable/Drawable$Callback;->invalidateDrawable(Landroid/graphics/drawable/Drawable;)V

    :cond_1
    return-void
.end method

.method public isRunning()Z
    .locals 1

    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/BcC;->rS()Z

    move-result v0

    return v0
.end method

.method public mC()Lcom/bytedance/adsdk/lottie/WR;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->eV:Lcom/bytedance/adsdk/lottie/WR;

    return-object v0
.end method

.method public mE()Lcom/bytedance/adsdk/lottie/vYf;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->hjc:Lcom/bytedance/adsdk/lottie/vYf;

    return-object v0
.end method

.method public mSE()V
    .locals 2
    .annotation build Lcom/bytedance/component/sdk/annotation/MainThread;
    .end annotation

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->mE:Lcom/bytedance/adsdk/lottie/hjc/hjc/ex;

    if-nez v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->Ko:Ljava/util/ArrayList;

    new-instance v1, Lcom/bytedance/adsdk/lottie/BcC$6;

    invoke-direct {v1, p0}, Lcom/bytedance/adsdk/lottie/BcC$6;-><init>(Lcom/bytedance/adsdk/lottie/BcC;)V

    invoke-virtual {v0, v1}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    return-void

    :cond_0
    invoke-direct {p0}, Lcom/bytedance/adsdk/lottie/BcC;->Moo()V

    invoke-direct {p0}, Lcom/bytedance/adsdk/lottie/BcC;->uy()Z

    move-result v0

    if-nez v0, :cond_1

    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/BcC;->Ql()I

    move-result v0

    if-nez v0, :cond_3

    :cond_1
    invoke-virtual {p0}, Landroid/graphics/drawable/Drawable;->isVisible()Z

    move-result v0

    if-eqz v0, :cond_2

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->Ubf:Lcom/bytedance/adsdk/lottie/WR/hjc;

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/WR/hjc;->rAx()V

    sget-object v0, Lcom/bytedance/adsdk/lottie/BcC$ex;->Fj:Lcom/bytedance/adsdk/lottie/BcC$ex;

    iput-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->mSE:Lcom/bytedance/adsdk/lottie/BcC$ex;

    goto :goto_0

    :cond_2
    sget-object v0, Lcom/bytedance/adsdk/lottie/BcC$ex;->ex:Lcom/bytedance/adsdk/lottie/BcC$ex;

    iput-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->mSE:Lcom/bytedance/adsdk/lottie/BcC$ex;

    :cond_3
    :goto_0
    invoke-direct {p0}, Lcom/bytedance/adsdk/lottie/BcC;->uy()Z

    move-result v0

    if-nez v0, :cond_5

    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/BcC;->Tc()F

    move-result v0

    const/4 v1, 0x0

    cmpg-float v0, v0, v1

    if-gez v0, :cond_4

    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/BcC;->UYd()F

    move-result v0

    goto :goto_1

    :cond_4
    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/BcC;->dG()F

    move-result v0

    :goto_1
    float-to-int v0, v0

    invoke-virtual {p0, v0}, Lcom/bytedance/adsdk/lottie/BcC;->hjc(I)V

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->Ubf:Lcom/bytedance/adsdk/lottie/WR/hjc;

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/WR/hjc;->UYd()V

    invoke-virtual {p0}, Landroid/graphics/drawable/Drawable;->isVisible()Z

    move-result v0

    if-nez v0, :cond_5

    sget-object v0, Lcom/bytedance/adsdk/lottie/BcC$ex;->Fj:Lcom/bytedance/adsdk/lottie/BcC$ex;

    iput-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->mSE:Lcom/bytedance/adsdk/lottie/BcC$ex;

    :cond_5
    return-void
.end method

.method public mSE(Z)V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->Ubf:Lcom/bytedance/adsdk/lottie/WR/hjc;

    invoke-virtual {v0, p1}, Lcom/bytedance/adsdk/lottie/WR/hjc;->hjc(Z)V

    return-void
.end method

.method public nsB()V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->Ko:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->clear()V

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->Ubf:Lcom/bytedance/adsdk/lottie/WR/hjc;

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/WR/hjc;->dG()V

    invoke-virtual {p0}, Landroid/graphics/drawable/Drawable;->isVisible()Z

    move-result v0

    if-nez v0, :cond_0

    sget-object v0, Lcom/bytedance/adsdk/lottie/BcC$ex;->Fj:Lcom/bytedance/adsdk/lottie/BcC$ex;

    iput-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->mSE:Lcom/bytedance/adsdk/lottie/BcC$ex;

    :cond_0
    return-void
.end method

.method public rAx()V
    .locals 2
    .annotation build Lcom/bytedance/component/sdk/annotation/MainThread;
    .end annotation

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->mE:Lcom/bytedance/adsdk/lottie/hjc/hjc/ex;

    if-nez v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->Ko:Ljava/util/ArrayList;

    new-instance v1, Lcom/bytedance/adsdk/lottie/BcC$7;

    invoke-direct {v1, p0}, Lcom/bytedance/adsdk/lottie/BcC$7;-><init>(Lcom/bytedance/adsdk/lottie/BcC;)V

    invoke-virtual {v0, v1}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    return-void

    :cond_0
    invoke-direct {p0}, Lcom/bytedance/adsdk/lottie/BcC;->Moo()V

    invoke-direct {p0}, Lcom/bytedance/adsdk/lottie/BcC;->uy()Z

    move-result v0

    if-nez v0, :cond_1

    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/BcC;->Ql()I

    move-result v0

    if-nez v0, :cond_3

    :cond_1
    invoke-virtual {p0}, Landroid/graphics/drawable/Drawable;->isVisible()Z

    move-result v0

    if-eqz v0, :cond_2

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->Ubf:Lcom/bytedance/adsdk/lottie/WR/hjc;

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/WR/hjc;->Tc()V

    sget-object v0, Lcom/bytedance/adsdk/lottie/BcC$ex;->Fj:Lcom/bytedance/adsdk/lottie/BcC$ex;

    iput-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->mSE:Lcom/bytedance/adsdk/lottie/BcC$ex;

    goto :goto_0

    :cond_2
    sget-object v0, Lcom/bytedance/adsdk/lottie/BcC$ex;->hjc:Lcom/bytedance/adsdk/lottie/BcC$ex;

    iput-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->mSE:Lcom/bytedance/adsdk/lottie/BcC$ex;

    :cond_3
    :goto_0
    invoke-direct {p0}, Lcom/bytedance/adsdk/lottie/BcC;->uy()Z

    move-result v0

    if-nez v0, :cond_5

    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/BcC;->Tc()F

    move-result v0

    const/4 v1, 0x0

    cmpg-float v0, v0, v1

    if-gez v0, :cond_4

    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/BcC;->UYd()F

    move-result v0

    goto :goto_1

    :cond_4
    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/BcC;->dG()F

    move-result v0

    :goto_1
    float-to-int v0, v0

    invoke-virtual {p0, v0}, Lcom/bytedance/adsdk/lottie/BcC;->hjc(I)V

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->Ubf:Lcom/bytedance/adsdk/lottie/WR/hjc;

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/WR/hjc;->UYd()V

    invoke-virtual {p0}, Landroid/graphics/drawable/Drawable;->isVisible()Z

    move-result v0

    if-nez v0, :cond_5

    sget-object v0, Lcom/bytedance/adsdk/lottie/BcC$ex;->Fj:Lcom/bytedance/adsdk/lottie/BcC$ex;

    iput-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->mSE:Lcom/bytedance/adsdk/lottie/BcC$ex;

    :cond_5
    return-void
.end method

.method public rS()Z
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->Ubf:Lcom/bytedance/adsdk/lottie/WR/hjc;

    if-nez v0, :cond_0

    const/4 v0, 0x0

    return v0

    :cond_0
    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/WR/hjc;->isRunning()Z

    move-result v0

    return v0
.end method

.method public scheduleDrawable(Landroid/graphics/drawable/Drawable;Ljava/lang/Runnable;J)V
    .locals 0

    invoke-virtual {p0}, Landroid/graphics/drawable/Drawable;->getCallback()Landroid/graphics/drawable/Drawable$Callback;

    move-result-object p1

    if-nez p1, :cond_0

    return-void

    :cond_0
    invoke-interface {p1, p0, p2, p3, p4}, Landroid/graphics/drawable/Drawable$Callback;->scheduleDrawable(Landroid/graphics/drawable/Drawable;Ljava/lang/Runnable;J)V

    return-void
.end method

.method public setAlpha(I)V
    .locals 0
    .param p1    # I
        .annotation build Lcom/bytedance/component/sdk/annotation/IntRange;
            from = 0x0L
            to = 0xffL
        .end annotation
    .end param

    iput p1, p0, Lcom/bytedance/adsdk/lottie/BcC;->Af:I

    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/BcC;->invalidateSelf()V

    return-void
.end method

.method public setColorFilter(Landroid/graphics/ColorFilter;)V
    .locals 0

    return-void
.end method

.method public setVisible(ZZ)Z
    .locals 1

    invoke-virtual {p0}, Landroid/graphics/drawable/Drawable;->isVisible()Z

    move-result v0

    xor-int/lit8 v0, v0, 0x1

    invoke-super {p0, p1, p2}, Landroid/graphics/drawable/Drawable;->setVisible(ZZ)Z

    move-result p2

    if-eqz p1, :cond_1

    iget-object p1, p0, Lcom/bytedance/adsdk/lottie/BcC;->mSE:Lcom/bytedance/adsdk/lottie/BcC$ex;

    sget-object v0, Lcom/bytedance/adsdk/lottie/BcC$ex;->ex:Lcom/bytedance/adsdk/lottie/BcC$ex;

    if-ne p1, v0, :cond_0

    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/BcC;->mSE()V

    goto :goto_0

    :cond_0
    sget-object v0, Lcom/bytedance/adsdk/lottie/BcC$ex;->hjc:Lcom/bytedance/adsdk/lottie/BcC$ex;

    if-ne p1, v0, :cond_3

    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/BcC;->rAx()V

    goto :goto_0

    :cond_1
    iget-object p1, p0, Lcom/bytedance/adsdk/lottie/BcC;->Ubf:Lcom/bytedance/adsdk/lottie/WR/hjc;

    invoke-virtual {p1}, Lcom/bytedance/adsdk/lottie/WR/hjc;->isRunning()Z

    move-result p1

    if-eqz p1, :cond_2

    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/BcC;->nsB()V

    sget-object p1, Lcom/bytedance/adsdk/lottie/BcC$ex;->hjc:Lcom/bytedance/adsdk/lottie/BcC$ex;

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/BcC;->mSE:Lcom/bytedance/adsdk/lottie/BcC$ex;

    goto :goto_0

    :cond_2
    if-nez v0, :cond_3

    sget-object p1, Lcom/bytedance/adsdk/lottie/BcC$ex;->Fj:Lcom/bytedance/adsdk/lottie/BcC$ex;

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/BcC;->mSE:Lcom/bytedance/adsdk/lottie/BcC$ex;

    :cond_3
    :goto_0
    return p2
.end method

.method public start()V
    .locals 2
    .annotation build Lcom/bytedance/component/sdk/annotation/MainThread;
    .end annotation

    invoke-virtual {p0}, Landroid/graphics/drawable/Drawable;->getCallback()Landroid/graphics/drawable/Drawable$Callback;

    move-result-object v0

    instance-of v1, v0, Landroid/view/View;

    if-eqz v1, :cond_0

    check-cast v0, Landroid/view/View;

    invoke-virtual {v0}, Landroid/view/View;->isInEditMode()Z

    move-result v0

    if-eqz v0, :cond_0

    return-void

    :cond_0
    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/BcC;->mSE()V

    return-void
.end method

.method public stop()V
    .locals 0
    .annotation build Lcom/bytedance/component/sdk/annotation/MainThread;
    .end annotation

    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/BcC;->Ko()V

    return-void
.end method

.method public svN(Ljava/lang/String;)V
    .locals 1

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/BcC;->Fj:Ljava/lang/String;

    invoke-direct {p0}, Lcom/bytedance/adsdk/lottie/BcC;->rXP()Lcom/bytedance/adsdk/lottie/ex/Fj;

    move-result-object v0

    if-eqz v0, :cond_0

    invoke-virtual {v0, p1}, Lcom/bytedance/adsdk/lottie/ex/Fj;->Fj(Ljava/lang/String;)V

    :cond_0
    return-void
.end method

.method public svN(Z)V
    .locals 0

    iput-boolean p1, p0, Lcom/bytedance/adsdk/lottie/BcC;->BcC:Z

    return-void
.end method

.method public svN()Z
    .locals 1

    iget-boolean v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->nsB:Z

    return v0
.end method

.method public unscheduleDrawable(Landroid/graphics/drawable/Drawable;Ljava/lang/Runnable;)V
    .locals 0

    invoke-virtual {p0}, Landroid/graphics/drawable/Drawable;->getCallback()Landroid/graphics/drawable/Drawable$Callback;

    move-result-object p1

    if-nez p1, :cond_0

    return-void

    :cond_0
    invoke-interface {p1, p0, p2}, Landroid/graphics/drawable/Drawable$Callback;->unscheduleDrawable(Landroid/graphics/drawable/Drawable;Ljava/lang/Runnable;)V

    return-void
.end method

.method public vYf()Z
    .locals 2

    invoke-virtual {p0}, Landroid/graphics/drawable/Drawable;->isVisible()Z

    move-result v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->Ubf:Lcom/bytedance/adsdk/lottie/WR/hjc;

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/WR/hjc;->isRunning()Z

    move-result v0

    return v0

    :cond_0
    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/BcC;->mSE:Lcom/bytedance/adsdk/lottie/BcC$ex;

    sget-object v1, Lcom/bytedance/adsdk/lottie/BcC$ex;->ex:Lcom/bytedance/adsdk/lottie/BcC$ex;

    if-eq v0, v1, :cond_2

    sget-object v1, Lcom/bytedance/adsdk/lottie/BcC$ex;->hjc:Lcom/bytedance/adsdk/lottie/BcC$ex;

    if-ne v0, v1, :cond_1

    goto :goto_0

    :cond_1
    const/4 v0, 0x0

    return v0

    :cond_2
    :goto_0
    const/4 v0, 0x1

    return v0
.end method
