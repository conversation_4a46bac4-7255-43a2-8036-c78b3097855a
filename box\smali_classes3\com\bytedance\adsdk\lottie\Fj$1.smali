.class Lcom/bytedance/adsdk/lottie/Fj$1;
.super Lcom/bytedance/adsdk/lottie/Tc;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/adsdk/lottie/Fj;->ex()Lcom/bytedance/adsdk/lottie/Tc;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lcom/bytedance/adsdk/lottie/Tc<",
        "TE;TE;>;"
    }
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/adsdk/lottie/Fj;


# direct methods
.method public constructor <init>(Lcom/bytedance/adsdk/lottie/Fj;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/Fj$1;->Fj:Lcom/bytedance/adsdk/lottie/Fj;

    invoke-direct {p0}, Lcom/bytedance/adsdk/lottie/Tc;-><init>()V

    return-void
.end method


# virtual methods
.method public Fj()I
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj$1;->Fj:Lcom/bytedance/adsdk/lottie/Fj;

    iget v0, v0, Lcom/bytedance/adsdk/lottie/Fj;->ex:I

    return v0
.end method

.method public Fj(Ljava/lang/Object;)I
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj$1;->Fj:Lcom/bytedance/adsdk/lottie/Fj;

    invoke-virtual {v0, p1}, Lcom/bytedance/adsdk/lottie/Fj;->Fj(Ljava/lang/Object;)I

    move-result p1

    return p1
.end method

.method public Fj(II)Ljava/lang/Object;
    .locals 0

    iget-object p2, p0, Lcom/bytedance/adsdk/lottie/Fj$1;->Fj:Lcom/bytedance/adsdk/lottie/Fj;

    iget-object p2, p2, Lcom/bytedance/adsdk/lottie/Fj;->Fj:[Ljava/lang/Object;

    aget-object p1, p2, p1

    return-object p1
.end method

.method public Fj(I)V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj$1;->Fj:Lcom/bytedance/adsdk/lottie/Fj;

    invoke-virtual {v0, p1}, Lcom/bytedance/adsdk/lottie/Fj;->hjc(I)Ljava/lang/Object;

    return-void
.end method

.method public ex()Ljava/util/Map;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Map<",
            "TE;TE;>;"
        }
    .end annotation

    new-instance v0, Ljava/lang/UnsupportedOperationException;

    const-string v1, "not a map"

    invoke-direct {v0, v1}, Ljava/lang/UnsupportedOperationException;-><init>(Ljava/lang/String;)V

    throw v0
.end method

.method public hjc()V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj$1;->Fj:Lcom/bytedance/adsdk/lottie/Fj;

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/Fj;->clear()V

    return-void
.end method
