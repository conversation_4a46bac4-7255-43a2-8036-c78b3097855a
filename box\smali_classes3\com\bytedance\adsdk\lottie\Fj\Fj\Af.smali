.class public Lcom/bytedance/adsdk/lottie/Fj/Fj/Af;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/adsdk/lottie/Fj/Fj/hjc;
.implements Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$Fj;


# instance fields
.field private final Fj:Ljava/lang/String;

.field private final Ubf:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/bytedance/adsdk/lottie/Fj/ex/Fj<",
            "*",
            "Ljava/lang/Float;",
            ">;"
        }
    .end annotation
.end field

.field private final WR:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/bytedance/adsdk/lottie/Fj/ex/Fj<",
            "*",
            "Ljava/lang/Float;",
            ">;"
        }
    .end annotation
.end field

.field private final eV:Lcom/bytedance/adsdk/lottie/hjc/ex/vYf$Fj;

.field private final ex:Z

.field private final hjc:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$Fj;",
            ">;"
        }
    .end annotation
.end field

.field private final svN:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/bytedance/adsdk/lottie/Fj/ex/Fj<",
            "*",
            "Ljava/lang/Float;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;Lcom/bytedance/adsdk/lottie/hjc/ex/vYf;)V
    .locals 2

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/Af;->hjc:Ljava/util/List;

    invoke-virtual {p2}, Lcom/bytedance/adsdk/lottie/hjc/ex/vYf;->Fj()Ljava/lang/String;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/Af;->Fj:Ljava/lang/String;

    invoke-virtual {p2}, Lcom/bytedance/adsdk/lottie/hjc/ex/vYf;->WR()Z

    move-result v0

    iput-boolean v0, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/Af;->ex:Z

    invoke-virtual {p2}, Lcom/bytedance/adsdk/lottie/hjc/ex/vYf;->ex()Lcom/bytedance/adsdk/lottie/hjc/ex/vYf$Fj;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/Af;->eV:Lcom/bytedance/adsdk/lottie/hjc/ex/vYf$Fj;

    invoke-virtual {p2}, Lcom/bytedance/adsdk/lottie/hjc/ex/vYf;->eV()Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;->Fj()Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/Af;->Ubf:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    invoke-virtual {p2}, Lcom/bytedance/adsdk/lottie/hjc/ex/vYf;->hjc()Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

    move-result-object v1

    invoke-virtual {v1}, Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;->Fj()Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    move-result-object v1

    iput-object v1, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/Af;->WR:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    invoke-virtual {p2}, Lcom/bytedance/adsdk/lottie/hjc/ex/vYf;->Ubf()Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

    move-result-object p2

    invoke-virtual {p2}, Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;->Fj()Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    move-result-object p2

    iput-object p2, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/Af;->svN:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    invoke-virtual {p1, v0}, Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;->Fj(Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;)V

    invoke-virtual {p1, v1}, Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;->Fj(Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;)V

    invoke-virtual {p1, p2}, Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;->Fj(Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;)V

    invoke-virtual {v0, p0}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->Fj(Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$Fj;)V

    invoke-virtual {v1, p0}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->Fj(Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$Fj;)V

    invoke-virtual {p2, p0}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->Fj(Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$Fj;)V

    return-void
.end method


# virtual methods
.method public Fj()V
    .locals 2

    const/4 v0, 0x0

    :goto_0
    iget-object v1, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/Af;->hjc:Ljava/util/List;

    invoke-interface {v1}, Ljava/util/List;->size()I

    move-result v1

    if-ge v0, v1, :cond_0

    iget-object v1, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/Af;->hjc:Ljava/util/List;

    invoke-interface {v1, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$Fj;

    invoke-interface {v1}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$Fj;->Fj()V

    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :cond_0
    return-void
.end method

.method public Fj(Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$Fj;)V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/Af;->hjc:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public Fj(Ljava/util/List;Ljava/util/List;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lcom/bytedance/adsdk/lottie/Fj/Fj/hjc;",
            ">;",
            "Ljava/util/List<",
            "Lcom/bytedance/adsdk/lottie/Fj/Fj/hjc;",
            ">;)V"
        }
    .end annotation

    return-void
.end method

.method public Ubf()Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lcom/bytedance/adsdk/lottie/Fj/ex/Fj<",
            "*",
            "Ljava/lang/Float;",
            ">;"
        }
    .end annotation

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/Af;->svN:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    return-object v0
.end method

.method public WR()Z
    .locals 1

    iget-boolean v0, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/Af;->ex:Z

    return v0
.end method

.method public eV()Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lcom/bytedance/adsdk/lottie/Fj/ex/Fj<",
            "*",
            "Ljava/lang/Float;",
            ">;"
        }
    .end annotation

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/Af;->WR:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    return-object v0
.end method

.method public ex()Lcom/bytedance/adsdk/lottie/hjc/ex/vYf$Fj;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/Af;->eV:Lcom/bytedance/adsdk/lottie/hjc/ex/vYf$Fj;

    return-object v0
.end method

.method public hjc()Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lcom/bytedance/adsdk/lottie/Fj/ex/Fj<",
            "*",
            "Ljava/lang/Float;",
            ">;"
        }
    .end annotation

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/Af;->Ubf:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    return-object v0
.end method
