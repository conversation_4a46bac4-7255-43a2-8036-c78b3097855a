.class public Lcom/bytedance/adsdk/lottie/Fj/Fj/BcC;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/adsdk/lottie/Fj/Fj/Ubf;
.implements Lcom/bytedance/adsdk/lottie/Fj/Fj/rAx;
.implements Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$Fj;


# instance fields
.field private Af:Lcom/bytedance/adsdk/lottie/Fj/ex/hjc;

.field private final BcC:Landroid/graphics/Paint;

.field Fj:F

.field private JU:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/bytedance/adsdk/lottie/Fj/ex/Fj<",
            "Landroid/graphics/ColorFilter;",
            "Landroid/graphics/ColorFilter;",
            ">;"
        }
    .end annotation
.end field

.field private final JW:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/bytedance/adsdk/lottie/Fj/ex/Fj<",
            "Landroid/graphics/PointF;",
            "Landroid/graphics/PointF;",
            ">;"
        }
    .end annotation
.end field

.field private final Ko:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lcom/bytedance/adsdk/lottie/Fj/Fj/dG;",
            ">;"
        }
    .end annotation
.end field

.field private Ql:Lcom/bytedance/adsdk/lottie/Fj/ex/Ql;

.field private final Tc:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/bytedance/adsdk/lottie/Fj/ex/Fj<",
            "Landroid/graphics/PointF;",
            "Landroid/graphics/PointF;",
            ">;"
        }
    .end annotation
.end field

.field private final UYd:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/bytedance/adsdk/lottie/Fj/ex/Fj<",
            "Lcom/bytedance/adsdk/lottie/hjc/ex/eV;",
            "Lcom/bytedance/adsdk/lottie/hjc/ex/eV;",
            ">;"
        }
    .end annotation
.end field

.field private final Ubf:Landroid/util/LongSparseArray;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Landroid/util/LongSparseArray<",
            "Landroid/graphics/LinearGradient;",
            ">;"
        }
    .end annotation
.end field

.field private final WR:Landroid/util/LongSparseArray;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Landroid/util/LongSparseArray<",
            "Landroid/graphics/RadialGradient;",
            ">;"
        }
    .end annotation
.end field

.field private final dG:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/bytedance/adsdk/lottie/Fj/ex/Fj<",
            "Ljava/lang/Integer;",
            "Ljava/lang/Integer;",
            ">;"
        }
    .end annotation
.end field

.field private final eV:Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;

.field private final ex:Ljava/lang/String;

.field private final hjc:Z

.field private mE:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/bytedance/adsdk/lottie/Fj/ex/Fj<",
            "Ljava/lang/Float;",
            "Ljava/lang/Float;",
            ">;"
        }
    .end annotation
.end field

.field private final mSE:Landroid/graphics/RectF;

.field private final rAx:Lcom/bytedance/adsdk/lottie/hjc/ex/svN;

.field private final rS:Lcom/bytedance/adsdk/lottie/BcC;

.field private final svN:Landroid/graphics/Path;

.field private final vYf:I


# direct methods
.method public constructor <init>(Lcom/bytedance/adsdk/lottie/BcC;Lcom/bytedance/adsdk/lottie/WR;Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;Lcom/bytedance/adsdk/lottie/hjc/ex/Ubf;)V
    .locals 3

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    new-instance v0, Landroid/util/LongSparseArray;

    invoke-direct {v0}, Landroid/util/LongSparseArray;-><init>()V

    iput-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/BcC;->Ubf:Landroid/util/LongSparseArray;

    new-instance v0, Landroid/util/LongSparseArray;

    invoke-direct {v0}, Landroid/util/LongSparseArray;-><init>()V

    iput-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/BcC;->WR:Landroid/util/LongSparseArray;

    new-instance v0, Landroid/graphics/Path;

    invoke-direct {v0}, Landroid/graphics/Path;-><init>()V

    iput-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/BcC;->svN:Landroid/graphics/Path;

    new-instance v1, Lcom/bytedance/adsdk/lottie/Fj/Fj;

    const/4 v2, 0x1

    invoke-direct {v1, v2}, Lcom/bytedance/adsdk/lottie/Fj/Fj;-><init>(I)V

    iput-object v1, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/BcC;->BcC:Landroid/graphics/Paint;

    new-instance v1, Landroid/graphics/RectF;

    invoke-direct {v1}, Landroid/graphics/RectF;-><init>()V

    iput-object v1, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/BcC;->mSE:Landroid/graphics/RectF;

    new-instance v1, Ljava/util/ArrayList;

    invoke-direct {v1}, Ljava/util/ArrayList;-><init>()V

    iput-object v1, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/BcC;->Ko:Ljava/util/List;

    const/4 v1, 0x0

    iput v1, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/BcC;->Fj:F

    iput-object p3, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/BcC;->eV:Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;

    invoke-virtual {p4}, Lcom/bytedance/adsdk/lottie/hjc/ex/Ubf;->Fj()Ljava/lang/String;

    move-result-object v1

    iput-object v1, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/BcC;->ex:Ljava/lang/String;

    invoke-virtual {p4}, Lcom/bytedance/adsdk/lottie/hjc/ex/Ubf;->BcC()Z

    move-result v1

    iput-boolean v1, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/BcC;->hjc:Z

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/BcC;->rS:Lcom/bytedance/adsdk/lottie/BcC;

    invoke-virtual {p4}, Lcom/bytedance/adsdk/lottie/hjc/ex/Ubf;->ex()Lcom/bytedance/adsdk/lottie/hjc/ex/svN;

    move-result-object p1

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/BcC;->rAx:Lcom/bytedance/adsdk/lottie/hjc/ex/svN;

    invoke-virtual {p4}, Lcom/bytedance/adsdk/lottie/hjc/ex/Ubf;->hjc()Landroid/graphics/Path$FillType;

    move-result-object p1

    invoke-virtual {v0, p1}, Landroid/graphics/Path;->setFillType(Landroid/graphics/Path$FillType;)V

    invoke-virtual {p2}, Lcom/bytedance/adsdk/lottie/WR;->Ubf()F

    move-result p1

    const/high16 p2, 0x42000000    # 32.0f

    div-float/2addr p1, p2

    float-to-int p1, p1

    iput p1, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/BcC;->vYf:I

    invoke-virtual {p4}, Lcom/bytedance/adsdk/lottie/hjc/ex/Ubf;->eV()Lcom/bytedance/adsdk/lottie/hjc/Fj/hjc;

    move-result-object p1

    invoke-virtual {p1}, Lcom/bytedance/adsdk/lottie/hjc/Fj/hjc;->Fj()Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    move-result-object p1

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/BcC;->UYd:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    invoke-virtual {p1, p0}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->Fj(Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$Fj;)V

    invoke-virtual {p3, p1}, Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;->Fj(Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;)V

    invoke-virtual {p4}, Lcom/bytedance/adsdk/lottie/hjc/ex/Ubf;->Ubf()Lcom/bytedance/adsdk/lottie/hjc/Fj/eV;

    move-result-object p1

    invoke-virtual {p1}, Lcom/bytedance/adsdk/lottie/hjc/Fj/eV;->Fj()Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    move-result-object p1

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/BcC;->dG:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    invoke-virtual {p1, p0}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->Fj(Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$Fj;)V

    invoke-virtual {p3, p1}, Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;->Fj(Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;)V

    invoke-virtual {p4}, Lcom/bytedance/adsdk/lottie/hjc/ex/Ubf;->WR()Lcom/bytedance/adsdk/lottie/hjc/Fj/WR;

    move-result-object p1

    invoke-virtual {p1}, Lcom/bytedance/adsdk/lottie/hjc/Fj/WR;->Fj()Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    move-result-object p1

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/BcC;->Tc:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    invoke-virtual {p1, p0}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->Fj(Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$Fj;)V

    invoke-virtual {p3, p1}, Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;->Fj(Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;)V

    invoke-virtual {p4}, Lcom/bytedance/adsdk/lottie/hjc/ex/Ubf;->svN()Lcom/bytedance/adsdk/lottie/hjc/Fj/WR;

    move-result-object p1

    invoke-virtual {p1}, Lcom/bytedance/adsdk/lottie/hjc/Fj/WR;->Fj()Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    move-result-object p1

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/BcC;->JW:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    invoke-virtual {p1, p0}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->Fj(Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$Fj;)V

    invoke-virtual {p3, p1}, Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;->Fj(Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;)V

    invoke-virtual {p3}, Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;->WR()Lcom/bytedance/adsdk/lottie/hjc/ex/Fj;

    move-result-object p1

    if-eqz p1, :cond_0

    invoke-virtual {p3}, Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;->WR()Lcom/bytedance/adsdk/lottie/hjc/ex/Fj;

    move-result-object p1

    invoke-virtual {p1}, Lcom/bytedance/adsdk/lottie/hjc/ex/Fj;->Fj()Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

    move-result-object p1

    invoke-virtual {p1}, Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;->Fj()Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    move-result-object p1

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/BcC;->mE:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    invoke-virtual {p1, p0}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->Fj(Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$Fj;)V

    iget-object p1, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/BcC;->mE:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    invoke-virtual {p3, p1}, Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;->Fj(Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;)V

    :cond_0
    invoke-virtual {p3}, Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;->svN()Lcom/bytedance/adsdk/lottie/Ubf/Ko;

    move-result-object p1

    if-eqz p1, :cond_1

    new-instance p1, Lcom/bytedance/adsdk/lottie/Fj/ex/hjc;

    invoke-virtual {p3}, Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;->svN()Lcom/bytedance/adsdk/lottie/Ubf/Ko;

    move-result-object p2

    invoke-direct {p1, p0, p3, p2}, Lcom/bytedance/adsdk/lottie/Fj/ex/hjc;-><init>(Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$Fj;Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;Lcom/bytedance/adsdk/lottie/Ubf/Ko;)V

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/BcC;->Af:Lcom/bytedance/adsdk/lottie/Fj/ex/hjc;

    :cond_1
    return-void
.end method

.method private Fj([I)[I
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/BcC;->Ql:Lcom/bytedance/adsdk/lottie/Fj/ex/Ql;

    if-nez v0, :cond_0

    return-object p1

    :cond_0
    const/4 p1, 0x0

    throw p1
.end method

.method private eV()I
    .locals 4

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/BcC;->Tc:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->BcC()F

    move-result v0

    iget v1, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/BcC;->vYf:I

    int-to-float v1, v1

    mul-float v0, v0, v1

    invoke-static {v0}, Ljava/lang/Math;->round(F)I

    move-result v0

    iget-object v1, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/BcC;->JW:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    invoke-virtual {v1}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->BcC()F

    move-result v1

    iget v2, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/BcC;->vYf:I

    int-to-float v2, v2

    mul-float v1, v1, v2

    invoke-static {v1}, Ljava/lang/Math;->round(F)I

    move-result v1

    iget-object v2, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/BcC;->UYd:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    invoke-virtual {v2}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->BcC()F

    move-result v2

    iget v3, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/BcC;->vYf:I

    int-to-float v3, v3

    mul-float v2, v2, v3

    invoke-static {v2}, Ljava/lang/Math;->round(F)I

    move-result v2

    if-eqz v0, :cond_0

    mul-int/lit16 v0, v0, 0x20f

    goto :goto_0

    :cond_0
    const/16 v0, 0x11

    :goto_0
    if-eqz v1, :cond_1

    mul-int/lit8 v0, v0, 0x1f

    mul-int v0, v0, v1

    :cond_1
    if-eqz v2, :cond_2

    mul-int/lit8 v0, v0, 0x1f

    mul-int v0, v0, v2

    :cond_2
    return v0
.end method

.method private ex()Landroid/graphics/LinearGradient;
    .locals 14

    invoke-direct {p0}, Lcom/bytedance/adsdk/lottie/Fj/Fj/BcC;->eV()I

    move-result v0

    iget-object v1, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/BcC;->Ubf:Landroid/util/LongSparseArray;

    int-to-long v2, v0

    invoke-virtual {v1, v2, v3}, Landroid/util/LongSparseArray;->get(J)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroid/graphics/LinearGradient;

    if-eqz v0, :cond_0

    return-object v0

    :cond_0
    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/BcC;->Tc:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->svN()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroid/graphics/PointF;

    iget-object v1, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/BcC;->JW:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    invoke-virtual {v1}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->svN()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Landroid/graphics/PointF;

    iget-object v4, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/BcC;->UYd:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    invoke-virtual {v4}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->svN()Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lcom/bytedance/adsdk/lottie/hjc/ex/eV;

    invoke-virtual {v4}, Lcom/bytedance/adsdk/lottie/hjc/ex/eV;->ex()[I

    move-result-object v5

    invoke-direct {p0, v5}, Lcom/bytedance/adsdk/lottie/Fj/Fj/BcC;->Fj([I)[I

    move-result-object v11

    invoke-virtual {v4}, Lcom/bytedance/adsdk/lottie/hjc/ex/eV;->Fj()[F

    move-result-object v12

    new-instance v4, Landroid/graphics/LinearGradient;

    iget v7, v0, Landroid/graphics/PointF;->x:F

    iget v8, v0, Landroid/graphics/PointF;->y:F

    iget v9, v1, Landroid/graphics/PointF;->x:F

    iget v10, v1, Landroid/graphics/PointF;->y:F

    sget-object v13, Landroid/graphics/Shader$TileMode;->CLAMP:Landroid/graphics/Shader$TileMode;

    move-object v6, v4

    invoke-direct/range {v6 .. v13}, Landroid/graphics/LinearGradient;-><init>(FFFF[I[FLandroid/graphics/Shader$TileMode;)V

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/BcC;->Ubf:Landroid/util/LongSparseArray;

    invoke-virtual {v0, v2, v3, v4}, Landroid/util/LongSparseArray;->put(JLjava/lang/Object;)V

    return-object v4
.end method

.method private hjc()Landroid/graphics/RadialGradient;
    .locals 13

    invoke-direct {p0}, Lcom/bytedance/adsdk/lottie/Fj/Fj/BcC;->eV()I

    move-result v0

    iget-object v1, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/BcC;->WR:Landroid/util/LongSparseArray;

    int-to-long v2, v0

    invoke-virtual {v1, v2, v3}, Landroid/util/LongSparseArray;->get(J)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroid/graphics/RadialGradient;

    if-eqz v0, :cond_0

    return-object v0

    :cond_0
    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/BcC;->Tc:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->svN()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroid/graphics/PointF;

    iget-object v1, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/BcC;->JW:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    invoke-virtual {v1}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->svN()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Landroid/graphics/PointF;

    iget-object v4, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/BcC;->UYd:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    invoke-virtual {v4}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->svN()Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lcom/bytedance/adsdk/lottie/hjc/ex/eV;

    invoke-virtual {v4}, Lcom/bytedance/adsdk/lottie/hjc/ex/eV;->ex()[I

    move-result-object v5

    invoke-direct {p0, v5}, Lcom/bytedance/adsdk/lottie/Fj/Fj/BcC;->Fj([I)[I

    move-result-object v10

    invoke-virtual {v4}, Lcom/bytedance/adsdk/lottie/hjc/ex/eV;->Fj()[F

    move-result-object v11

    iget v7, v0, Landroid/graphics/PointF;->x:F

    iget v8, v0, Landroid/graphics/PointF;->y:F

    iget v0, v1, Landroid/graphics/PointF;->x:F

    iget v1, v1, Landroid/graphics/PointF;->y:F

    sub-float/2addr v0, v7

    float-to-double v4, v0

    sub-float/2addr v1, v8

    float-to-double v0, v1

    invoke-static {v4, v5, v0, v1}, Ljava/lang/Math;->hypot(DD)D

    move-result-wide v0

    double-to-float v0, v0

    const/4 v1, 0x0

    cmpg-float v1, v0, v1

    if-gtz v1, :cond_1

    const v0, 0x3a83126f    # 0.001f

    const v9, 0x3a83126f    # 0.001f

    goto :goto_0

    :cond_1
    move v9, v0

    :goto_0
    new-instance v0, Landroid/graphics/RadialGradient;

    sget-object v12, Landroid/graphics/Shader$TileMode;->CLAMP:Landroid/graphics/Shader$TileMode;

    move-object v6, v0

    invoke-direct/range {v6 .. v12}, Landroid/graphics/RadialGradient;-><init>(FFF[I[FLandroid/graphics/Shader$TileMode;)V

    iget-object v1, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/BcC;->WR:Landroid/util/LongSparseArray;

    invoke-virtual {v1, v2, v3, v0}, Landroid/util/LongSparseArray;->put(JLjava/lang/Object;)V

    return-object v0
.end method


# virtual methods
.method public Fj()V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/BcC;->rS:Lcom/bytedance/adsdk/lottie/BcC;

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/BcC;->invalidateSelf()V

    return-void
.end method

.method public Fj(Landroid/graphics/Canvas;Landroid/graphics/Matrix;I)V
    .locals 5

    iget-boolean v0, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/BcC;->hjc:Z

    if-eqz v0, :cond_0

    return-void

    :cond_0
    const-string v0, "GradientFillContent#draw"

    invoke-static {v0}, Lcom/bytedance/adsdk/lottie/Ubf;->Fj(Ljava/lang/String;)V

    iget-object v1, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/BcC;->svN:Landroid/graphics/Path;

    invoke-virtual {v1}, Landroid/graphics/Path;->reset()V

    const/4 v1, 0x0

    const/4 v2, 0x0

    :goto_0
    iget-object v3, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/BcC;->Ko:Ljava/util/List;

    invoke-interface {v3}, Ljava/util/List;->size()I

    move-result v3

    if-ge v2, v3, :cond_1

    iget-object v3, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/BcC;->svN:Landroid/graphics/Path;

    iget-object v4, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/BcC;->Ko:Ljava/util/List;

    invoke-interface {v4, v2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lcom/bytedance/adsdk/lottie/Fj/Fj/dG;

    invoke-interface {v4}, Lcom/bytedance/adsdk/lottie/Fj/Fj/dG;->eV()Landroid/graphics/Path;

    move-result-object v4

    invoke-virtual {v3, v4, p2}, Landroid/graphics/Path;->addPath(Landroid/graphics/Path;Landroid/graphics/Matrix;)V

    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_1
    iget-object v2, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/BcC;->svN:Landroid/graphics/Path;

    iget-object v3, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/BcC;->mSE:Landroid/graphics/RectF;

    invoke-virtual {v2, v3, v1}, Landroid/graphics/Path;->computeBounds(Landroid/graphics/RectF;Z)V

    iget-object v2, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/BcC;->rAx:Lcom/bytedance/adsdk/lottie/hjc/ex/svN;

    sget-object v3, Lcom/bytedance/adsdk/lottie/hjc/ex/svN;->Fj:Lcom/bytedance/adsdk/lottie/hjc/ex/svN;

    if-ne v2, v3, :cond_2

    invoke-direct {p0}, Lcom/bytedance/adsdk/lottie/Fj/Fj/BcC;->ex()Landroid/graphics/LinearGradient;

    move-result-object v2

    goto :goto_1

    :cond_2
    invoke-direct {p0}, Lcom/bytedance/adsdk/lottie/Fj/Fj/BcC;->hjc()Landroid/graphics/RadialGradient;

    move-result-object v2

    :goto_1
    invoke-virtual {v2, p2}, Landroid/graphics/Shader;->setLocalMatrix(Landroid/graphics/Matrix;)V

    iget-object p2, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/BcC;->BcC:Landroid/graphics/Paint;

    invoke-virtual {p2, v2}, Landroid/graphics/Paint;->setShader(Landroid/graphics/Shader;)Landroid/graphics/Shader;

    iget-object p2, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/BcC;->JU:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    if-eqz p2, :cond_3

    iget-object v2, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/BcC;->BcC:Landroid/graphics/Paint;

    invoke-virtual {p2}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->svN()Ljava/lang/Object;

    move-result-object p2

    check-cast p2, Landroid/graphics/ColorFilter;

    invoke-virtual {v2, p2}, Landroid/graphics/Paint;->setColorFilter(Landroid/graphics/ColorFilter;)Landroid/graphics/ColorFilter;

    :cond_3
    iget-object p2, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/BcC;->mE:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    if-eqz p2, :cond_6

    invoke-virtual {p2}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->svN()Ljava/lang/Object;

    move-result-object p2

    check-cast p2, Ljava/lang/Float;

    invoke-virtual {p2}, Ljava/lang/Float;->floatValue()F

    move-result p2

    const/4 v2, 0x0

    cmpl-float v2, p2, v2

    if-nez v2, :cond_4

    iget-object v2, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/BcC;->BcC:Landroid/graphics/Paint;

    const/4 v3, 0x0

    invoke-virtual {v2, v3}, Landroid/graphics/Paint;->setMaskFilter(Landroid/graphics/MaskFilter;)Landroid/graphics/MaskFilter;

    goto :goto_2

    :cond_4
    iget v2, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/BcC;->Fj:F

    cmpl-float v2, p2, v2

    if-eqz v2, :cond_5

    new-instance v2, Landroid/graphics/BlurMaskFilter;

    sget-object v3, Landroid/graphics/BlurMaskFilter$Blur;->NORMAL:Landroid/graphics/BlurMaskFilter$Blur;

    invoke-direct {v2, p2, v3}, Landroid/graphics/BlurMaskFilter;-><init>(FLandroid/graphics/BlurMaskFilter$Blur;)V

    iget-object v3, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/BcC;->BcC:Landroid/graphics/Paint;

    invoke-virtual {v3, v2}, Landroid/graphics/Paint;->setMaskFilter(Landroid/graphics/MaskFilter;)Landroid/graphics/MaskFilter;

    :cond_5
    :goto_2
    iput p2, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/BcC;->Fj:F

    :cond_6
    iget-object p2, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/BcC;->Af:Lcom/bytedance/adsdk/lottie/Fj/ex/hjc;

    if-eqz p2, :cond_7

    iget-object v2, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/BcC;->BcC:Landroid/graphics/Paint;

    invoke-virtual {p2, v2}, Lcom/bytedance/adsdk/lottie/Fj/ex/hjc;->Fj(Landroid/graphics/Paint;)V

    :cond_7
    int-to-float p2, p3

    const/high16 p3, 0x437f0000    # 255.0f

    div-float/2addr p2, p3

    iget-object v2, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/BcC;->dG:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    invoke-virtual {v2}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->svN()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Ljava/lang/Integer;

    invoke-virtual {v2}, Ljava/lang/Integer;->intValue()I

    move-result v2

    int-to-float v2, v2

    mul-float p2, p2, v2

    const/high16 v2, 0x42c80000    # 100.0f

    div-float/2addr p2, v2

    mul-float p2, p2, p3

    float-to-int p2, p2

    iget-object p3, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/BcC;->BcC:Landroid/graphics/Paint;

    const/16 v2, 0xff

    invoke-static {p2, v1, v2}, Lcom/bytedance/adsdk/lottie/WR/Ubf;->Fj(III)I

    move-result p2

    invoke-virtual {p3, p2}, Landroid/graphics/Paint;->setAlpha(I)V

    iget-object p2, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/BcC;->svN:Landroid/graphics/Path;

    iget-object p3, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/BcC;->BcC:Landroid/graphics/Paint;

    invoke-virtual {p1, p2, p3}, Landroid/graphics/Canvas;->drawPath(Landroid/graphics/Path;Landroid/graphics/Paint;)V

    invoke-static {v0}, Lcom/bytedance/adsdk/lottie/Ubf;->ex(Ljava/lang/String;)F

    return-void
.end method

.method public Fj(Landroid/graphics/RectF;Landroid/graphics/Matrix;Z)V
    .locals 3

    iget-object p3, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/BcC;->svN:Landroid/graphics/Path;

    invoke-virtual {p3}, Landroid/graphics/Path;->reset()V

    const/4 p3, 0x0

    const/4 v0, 0x0

    :goto_0
    iget-object v1, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/BcC;->Ko:Ljava/util/List;

    invoke-interface {v1}, Ljava/util/List;->size()I

    move-result v1

    if-ge v0, v1, :cond_0

    iget-object v1, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/BcC;->svN:Landroid/graphics/Path;

    iget-object v2, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/BcC;->Ko:Ljava/util/List;

    invoke-interface {v2, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/bytedance/adsdk/lottie/Fj/Fj/dG;

    invoke-interface {v2}, Lcom/bytedance/adsdk/lottie/Fj/Fj/dG;->eV()Landroid/graphics/Path;

    move-result-object v2

    invoke-virtual {v1, v2, p2}, Landroid/graphics/Path;->addPath(Landroid/graphics/Path;Landroid/graphics/Matrix;)V

    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :cond_0
    iget-object p2, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/BcC;->svN:Landroid/graphics/Path;

    invoke-virtual {p2, p1, p3}, Landroid/graphics/Path;->computeBounds(Landroid/graphics/RectF;Z)V

    iget p2, p1, Landroid/graphics/RectF;->left:F

    const/high16 p3, 0x3f800000    # 1.0f

    sub-float/2addr p2, p3

    iget v0, p1, Landroid/graphics/RectF;->top:F

    sub-float/2addr v0, p3

    iget v1, p1, Landroid/graphics/RectF;->right:F

    add-float/2addr v1, p3

    iget v2, p1, Landroid/graphics/RectF;->bottom:F

    add-float/2addr v2, p3

    invoke-virtual {p1, p2, v0, v1, v2}, Landroid/graphics/RectF;->set(FFFF)V

    return-void
.end method

.method public Fj(Ljava/util/List;Ljava/util/List;)V
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lcom/bytedance/adsdk/lottie/Fj/Fj/hjc;",
            ">;",
            "Ljava/util/List<",
            "Lcom/bytedance/adsdk/lottie/Fj/Fj/hjc;",
            ">;)V"
        }
    .end annotation

    const/4 p1, 0x0

    :goto_0
    invoke-interface {p2}, Ljava/util/List;->size()I

    move-result v0

    if-ge p1, v0, :cond_1

    invoke-interface {p2, p1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/bytedance/adsdk/lottie/Fj/Fj/hjc;

    instance-of v1, v0, Lcom/bytedance/adsdk/lottie/Fj/Fj/dG;

    if-eqz v1, :cond_0

    iget-object v1, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/BcC;->Ko:Ljava/util/List;

    check-cast v0, Lcom/bytedance/adsdk/lottie/Fj/Fj/dG;

    invoke-interface {v1, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    :cond_0
    add-int/lit8 p1, p1, 0x1

    goto :goto_0

    :cond_1
    return-void
.end method
