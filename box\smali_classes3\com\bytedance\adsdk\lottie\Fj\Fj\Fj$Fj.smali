.class final Lcom/bytedance/adsdk/lottie/Fj/Fj/Fj$Fj;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bytedance/adsdk/lottie/Fj/Fj/Fj;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "Fj"
.end annotation


# instance fields
.field private final Fj:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lcom/bytedance/adsdk/lottie/Fj/Fj/dG;",
            ">;"
        }
    .end annotation
.end field

.field private final ex:Lcom/bytedance/adsdk/lottie/Fj/Fj/Af;


# direct methods
.method private constructor <init>(Lcom/bytedance/adsdk/lottie/Fj/Fj/Af;)V
    .locals 1

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/Fj$Fj;->Fj:Ljava/util/List;

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/Fj$Fj;->ex:Lcom/bytedance/adsdk/lottie/Fj/Fj/Af;

    return-void
.end method

.method public synthetic constructor <init>(Lcom/bytedance/adsdk/lottie/Fj/Fj/Af;Lcom/bytedance/adsdk/lottie/Fj/Fj/Fj$1;)V
    .locals 0

    invoke-direct {p0, p1}, Lcom/bytedance/adsdk/lottie/Fj/Fj/Fj$Fj;-><init>(Lcom/bytedance/adsdk/lottie/Fj/Fj/Af;)V

    return-void
.end method

.method public static synthetic Fj(Lcom/bytedance/adsdk/lottie/Fj/Fj/Fj$Fj;)Ljava/util/List;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/Fj$Fj;->Fj:Ljava/util/List;

    return-object p0
.end method

.method public static synthetic ex(Lcom/bytedance/adsdk/lottie/Fj/Fj/Fj$Fj;)Lcom/bytedance/adsdk/lottie/Fj/Fj/Af;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/Fj$Fj;->ex:Lcom/bytedance/adsdk/lottie/Fj/Fj/Af;

    return-object p0
.end method
