.class public Lcom/bytedance/adsdk/lottie/Fj/Fj/JU;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/adsdk/lottie/Fj/Fj/Ko;
.implements Lcom/bytedance/adsdk/lottie/Fj/Fj/Ubf;
.implements Lcom/bytedance/adsdk/lottie/Fj/Fj/dG;
.implements Lcom/bytedance/adsdk/lottie/Fj/Fj/rAx;
.implements Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$Fj;


# instance fields
.field private final BcC:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/bytedance/adsdk/lottie/Fj/ex/Fj<",
            "Ljava/lang/Float;",
            "Ljava/lang/Float;",
            ">;"
        }
    .end annotation
.end field

.field private final Fj:Landroid/graphics/Matrix;

.field private Ko:Lcom/bytedance/adsdk/lottie/Fj/Fj/eV;

.field private final Ubf:Ljava/lang/String;

.field private final WR:Z

.field private final eV:Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;

.field private final ex:Landroid/graphics/Path;

.field private final hjc:Lcom/bytedance/adsdk/lottie/BcC;

.field private final mSE:Lcom/bytedance/adsdk/lottie/Fj/ex/JU;

.field private final svN:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/bytedance/adsdk/lottie/Fj/ex/Fj<",
            "Ljava/lang/Float;",
            "Ljava/lang/Float;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lcom/bytedance/adsdk/lottie/BcC;Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;Lcom/bytedance/adsdk/lottie/hjc/ex/UYd;)V
    .locals 1

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    new-instance v0, Landroid/graphics/Matrix;

    invoke-direct {v0}, Landroid/graphics/Matrix;-><init>()V

    iput-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/JU;->Fj:Landroid/graphics/Matrix;

    new-instance v0, Landroid/graphics/Path;

    invoke-direct {v0}, Landroid/graphics/Path;-><init>()V

    iput-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/JU;->ex:Landroid/graphics/Path;

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/JU;->hjc:Lcom/bytedance/adsdk/lottie/BcC;

    iput-object p2, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/JU;->eV:Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;

    invoke-virtual {p3}, Lcom/bytedance/adsdk/lottie/hjc/ex/UYd;->Fj()Ljava/lang/String;

    move-result-object p1

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/JU;->Ubf:Ljava/lang/String;

    invoke-virtual {p3}, Lcom/bytedance/adsdk/lottie/hjc/ex/UYd;->Ubf()Z

    move-result p1

    iput-boolean p1, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/JU;->WR:Z

    invoke-virtual {p3}, Lcom/bytedance/adsdk/lottie/hjc/ex/UYd;->ex()Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

    move-result-object p1

    invoke-virtual {p1}, Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;->Fj()Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    move-result-object p1

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/JU;->svN:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    invoke-virtual {p2, p1}, Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;->Fj(Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;)V

    invoke-virtual {p1, p0}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->Fj(Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$Fj;)V

    invoke-virtual {p3}, Lcom/bytedance/adsdk/lottie/hjc/ex/UYd;->hjc()Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

    move-result-object p1

    invoke-virtual {p1}, Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;->Fj()Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    move-result-object p1

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/JU;->BcC:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    invoke-virtual {p2, p1}, Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;->Fj(Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;)V

    invoke-virtual {p1, p0}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->Fj(Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$Fj;)V

    invoke-virtual {p3}, Lcom/bytedance/adsdk/lottie/hjc/ex/UYd;->eV()Lcom/bytedance/adsdk/lottie/hjc/Fj/UYd;

    move-result-object p1

    invoke-virtual {p1}, Lcom/bytedance/adsdk/lottie/hjc/Fj/UYd;->Ko()Lcom/bytedance/adsdk/lottie/Fj/ex/JU;

    move-result-object p1

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/JU;->mSE:Lcom/bytedance/adsdk/lottie/Fj/ex/JU;

    invoke-virtual {p1, p2}, Lcom/bytedance/adsdk/lottie/Fj/ex/JU;->Fj(Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;)V

    invoke-virtual {p1, p0}, Lcom/bytedance/adsdk/lottie/Fj/ex/JU;->Fj(Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$Fj;)V

    return-void
.end method


# virtual methods
.method public Fj()V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/JU;->hjc:Lcom/bytedance/adsdk/lottie/BcC;

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/BcC;->invalidateSelf()V

    return-void
.end method

.method public Fj(Landroid/graphics/Canvas;Landroid/graphics/Matrix;I)V
    .locals 9

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/JU;->svN:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->svN()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/lang/Float;

    invoke-virtual {v0}, Ljava/lang/Float;->floatValue()F

    move-result v0

    iget-object v1, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/JU;->BcC:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    invoke-virtual {v1}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->svN()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/lang/Float;

    invoke-virtual {v1}, Ljava/lang/Float;->floatValue()F

    move-result v1

    iget-object v2, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/JU;->mSE:Lcom/bytedance/adsdk/lottie/Fj/ex/JU;

    invoke-virtual {v2}, Lcom/bytedance/adsdk/lottie/Fj/ex/JU;->ex()Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    move-result-object v2

    invoke-virtual {v2}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->svN()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Ljava/lang/Float;

    invoke-virtual {v2}, Ljava/lang/Float;->floatValue()F

    move-result v2

    const/high16 v3, 0x42c80000    # 100.0f

    div-float/2addr v2, v3

    iget-object v4, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/JU;->mSE:Lcom/bytedance/adsdk/lottie/Fj/ex/JU;

    invoke-virtual {v4}, Lcom/bytedance/adsdk/lottie/Fj/ex/JU;->hjc()Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    move-result-object v4

    invoke-virtual {v4}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->svN()Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Ljava/lang/Float;

    invoke-virtual {v4}, Ljava/lang/Float;->floatValue()F

    move-result v4

    div-float/2addr v4, v3

    float-to-int v3, v0

    add-int/lit8 v3, v3, -0x1

    :goto_0
    if-ltz v3, :cond_0

    iget-object v5, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/JU;->Fj:Landroid/graphics/Matrix;

    invoke-virtual {v5, p2}, Landroid/graphics/Matrix;->set(Landroid/graphics/Matrix;)V

    iget-object v5, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/JU;->Fj:Landroid/graphics/Matrix;

    iget-object v6, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/JU;->mSE:Lcom/bytedance/adsdk/lottie/Fj/ex/JU;

    int-to-float v7, v3

    add-float v8, v7, v1

    invoke-virtual {v6, v8}, Lcom/bytedance/adsdk/lottie/Fj/ex/JU;->ex(F)Landroid/graphics/Matrix;

    move-result-object v6

    invoke-virtual {v5, v6}, Landroid/graphics/Matrix;->preConcat(Landroid/graphics/Matrix;)Z

    int-to-float v5, p3

    div-float/2addr v7, v0

    invoke-static {v2, v4, v7}, Lcom/bytedance/adsdk/lottie/WR/Ubf;->Fj(FFF)F

    move-result v6

    mul-float v5, v5, v6

    iget-object v6, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/JU;->Ko:Lcom/bytedance/adsdk/lottie/Fj/Fj/eV;

    iget-object v7, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/JU;->Fj:Landroid/graphics/Matrix;

    float-to-int v5, v5

    invoke-virtual {v6, p1, v7, v5}, Lcom/bytedance/adsdk/lottie/Fj/Fj/eV;->Fj(Landroid/graphics/Canvas;Landroid/graphics/Matrix;I)V

    add-int/lit8 v3, v3, -0x1

    goto :goto_0

    :cond_0
    return-void
.end method

.method public Fj(Landroid/graphics/RectF;Landroid/graphics/Matrix;Z)V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/JU;->Ko:Lcom/bytedance/adsdk/lottie/Fj/Fj/eV;

    invoke-virtual {v0, p1, p2, p3}, Lcom/bytedance/adsdk/lottie/Fj/Fj/eV;->Fj(Landroid/graphics/RectF;Landroid/graphics/Matrix;Z)V

    return-void
.end method

.method public Fj(Ljava/util/List;Ljava/util/List;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lcom/bytedance/adsdk/lottie/Fj/Fj/hjc;",
            ">;",
            "Ljava/util/List<",
            "Lcom/bytedance/adsdk/lottie/Fj/Fj/hjc;",
            ">;)V"
        }
    .end annotation

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/JU;->Ko:Lcom/bytedance/adsdk/lottie/Fj/Fj/eV;

    invoke-virtual {v0, p1, p2}, Lcom/bytedance/adsdk/lottie/Fj/Fj/eV;->Fj(Ljava/util/List;Ljava/util/List;)V

    return-void
.end method

.method public Fj(Ljava/util/ListIterator;)V
    .locals 8
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/ListIterator<",
            "Lcom/bytedance/adsdk/lottie/Fj/Fj/hjc;",
            ">;)V"
        }
    .end annotation

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/JU;->Ko:Lcom/bytedance/adsdk/lottie/Fj/Fj/eV;

    if-eqz v0, :cond_0

    return-void

    :cond_0
    invoke-interface {p1}, Ljava/util/ListIterator;->hasPrevious()Z

    move-result v0

    if-eqz v0, :cond_1

    invoke-interface {p1}, Ljava/util/ListIterator;->previous()Ljava/lang/Object;

    move-result-object v0

    if-ne v0, p0, :cond_0

    :cond_1
    new-instance v6, Ljava/util/ArrayList;

    invoke-direct {v6}, Ljava/util/ArrayList;-><init>()V

    :goto_0
    invoke-interface {p1}, Ljava/util/ListIterator;->hasPrevious()Z

    move-result v0

    if-eqz v0, :cond_2

    invoke-interface {p1}, Ljava/util/ListIterator;->previous()Ljava/lang/Object;

    move-result-object v0

    invoke-interface {v6, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    invoke-interface {p1}, Ljava/util/ListIterator;->remove()V

    goto :goto_0

    :cond_2
    invoke-static {v6}, Ljava/util/Collections;->reverse(Ljava/util/List;)V

    new-instance p1, Lcom/bytedance/adsdk/lottie/Fj/Fj/eV;

    iget-object v2, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/JU;->hjc:Lcom/bytedance/adsdk/lottie/BcC;

    iget-object v3, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/JU;->eV:Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;

    const-string v4, "Repeater"

    iget-boolean v5, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/JU;->WR:Z

    const/4 v7, 0x0

    move-object v1, p1

    invoke-direct/range {v1 .. v7}, Lcom/bytedance/adsdk/lottie/Fj/Fj/eV;-><init>(Lcom/bytedance/adsdk/lottie/BcC;Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;Ljava/lang/String;ZLjava/util/List;Lcom/bytedance/adsdk/lottie/hjc/Fj/UYd;)V

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/JU;->Ko:Lcom/bytedance/adsdk/lottie/Fj/Fj/eV;

    return-void
.end method

.method public eV()Landroid/graphics/Path;
    .locals 6

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/JU;->Ko:Lcom/bytedance/adsdk/lottie/Fj/Fj/eV;

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/Fj/Fj/eV;->eV()Landroid/graphics/Path;

    move-result-object v0

    iget-object v1, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/JU;->ex:Landroid/graphics/Path;

    invoke-virtual {v1}, Landroid/graphics/Path;->reset()V

    iget-object v1, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/JU;->svN:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    invoke-virtual {v1}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->svN()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/lang/Float;

    invoke-virtual {v1}, Ljava/lang/Float;->floatValue()F

    move-result v1

    iget-object v2, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/JU;->BcC:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    invoke-virtual {v2}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->svN()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Ljava/lang/Float;

    invoke-virtual {v2}, Ljava/lang/Float;->floatValue()F

    move-result v2

    float-to-int v1, v1

    add-int/lit8 v1, v1, -0x1

    :goto_0
    if-ltz v1, :cond_0

    iget-object v3, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/JU;->Fj:Landroid/graphics/Matrix;

    iget-object v4, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/JU;->mSE:Lcom/bytedance/adsdk/lottie/Fj/ex/JU;

    int-to-float v5, v1

    add-float/2addr v5, v2

    invoke-virtual {v4, v5}, Lcom/bytedance/adsdk/lottie/Fj/ex/JU;->ex(F)Landroid/graphics/Matrix;

    move-result-object v4

    invoke-virtual {v3, v4}, Landroid/graphics/Matrix;->set(Landroid/graphics/Matrix;)V

    iget-object v3, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/JU;->ex:Landroid/graphics/Path;

    iget-object v4, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/JU;->Fj:Landroid/graphics/Matrix;

    invoke-virtual {v3, v0, v4}, Landroid/graphics/Path;->addPath(Landroid/graphics/Path;Landroid/graphics/Matrix;)V

    add-int/lit8 v1, v1, -0x1

    goto :goto_0

    :cond_0
    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/JU;->ex:Landroid/graphics/Path;

    return-object v0
.end method
