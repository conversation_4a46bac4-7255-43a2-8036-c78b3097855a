.class public Lcom/bytedance/adsdk/lottie/Fj/Fj/JW;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/adsdk/lottie/Fj/Fj/dG;
.implements Lcom/bytedance/adsdk/lottie/Fj/Fj/rAx;
.implements Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$Fj;


# instance fields
.field private final BcC:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/bytedance/adsdk/lottie/Fj/ex/Fj<",
            "*",
            "Ljava/lang/Float;",
            ">;"
        }
    .end annotation
.end field

.field private final Fj:Landroid/graphics/Path;

.field private Ko:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/bytedance/adsdk/lottie/Fj/ex/Fj<",
            "Ljava/lang/Float;",
            "Ljava/lang/Float;",
            ">;"
        }
    .end annotation
.end field

.field private final Ubf:Lcom/bytedance/adsdk/lottie/BcC;

.field private final WR:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/bytedance/adsdk/lottie/Fj/ex/Fj<",
            "*",
            "Landroid/graphics/PointF;",
            ">;"
        }
    .end annotation
.end field

.field private final eV:Z

.field private final ex:Landroid/graphics/RectF;

.field private final hjc:Ljava/lang/String;

.field private final mSE:Lcom/bytedance/adsdk/lottie/Fj/Fj/ex;

.field private rAx:Z

.field private final svN:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/bytedance/adsdk/lottie/Fj/ex/Fj<",
            "*",
            "Landroid/graphics/PointF;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lcom/bytedance/adsdk/lottie/BcC;Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;Lcom/bytedance/adsdk/lottie/hjc/ex/rAx;)V
    .locals 1

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    new-instance v0, Landroid/graphics/Path;

    invoke-direct {v0}, Landroid/graphics/Path;-><init>()V

    iput-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/JW;->Fj:Landroid/graphics/Path;

    new-instance v0, Landroid/graphics/RectF;

    invoke-direct {v0}, Landroid/graphics/RectF;-><init>()V

    iput-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/JW;->ex:Landroid/graphics/RectF;

    new-instance v0, Lcom/bytedance/adsdk/lottie/Fj/Fj/ex;

    invoke-direct {v0}, Lcom/bytedance/adsdk/lottie/Fj/Fj/ex;-><init>()V

    iput-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/JW;->mSE:Lcom/bytedance/adsdk/lottie/Fj/Fj/ex;

    const/4 v0, 0x0

    iput-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/JW;->Ko:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    invoke-virtual {p3}, Lcom/bytedance/adsdk/lottie/hjc/ex/rAx;->Fj()Ljava/lang/String;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/JW;->hjc:Ljava/lang/String;

    invoke-virtual {p3}, Lcom/bytedance/adsdk/lottie/hjc/ex/rAx;->Ubf()Z

    move-result v0

    iput-boolean v0, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/JW;->eV:Z

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/JW;->Ubf:Lcom/bytedance/adsdk/lottie/BcC;

    invoke-virtual {p3}, Lcom/bytedance/adsdk/lottie/hjc/ex/rAx;->eV()Lcom/bytedance/adsdk/lottie/hjc/Fj/dG;

    move-result-object p1

    invoke-interface {p1}, Lcom/bytedance/adsdk/lottie/hjc/Fj/dG;->Fj()Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    move-result-object p1

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/JW;->WR:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    invoke-virtual {p3}, Lcom/bytedance/adsdk/lottie/hjc/ex/rAx;->hjc()Lcom/bytedance/adsdk/lottie/hjc/Fj/dG;

    move-result-object v0

    invoke-interface {v0}, Lcom/bytedance/adsdk/lottie/hjc/Fj/dG;->Fj()Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/JW;->svN:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    invoke-virtual {p3}, Lcom/bytedance/adsdk/lottie/hjc/ex/rAx;->ex()Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

    move-result-object p3

    invoke-virtual {p3}, Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;->Fj()Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    move-result-object p3

    iput-object p3, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/JW;->BcC:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    invoke-virtual {p2, p1}, Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;->Fj(Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;)V

    invoke-virtual {p2, v0}, Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;->Fj(Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;)V

    invoke-virtual {p2, p3}, Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;->Fj(Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;)V

    invoke-virtual {p1, p0}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->Fj(Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$Fj;)V

    invoke-virtual {v0, p0}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->Fj(Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$Fj;)V

    invoke-virtual {p3, p0}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->Fj(Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$Fj;)V

    return-void
.end method

.method private ex()V
    .locals 1

    const/4 v0, 0x0

    iput-boolean v0, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/JW;->rAx:Z

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/JW;->Ubf:Lcom/bytedance/adsdk/lottie/BcC;

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/BcC;->invalidateSelf()V

    return-void
.end method


# virtual methods
.method public Fj()V
    .locals 0

    invoke-direct {p0}, Lcom/bytedance/adsdk/lottie/Fj/Fj/JW;->ex()V

    return-void
.end method

.method public Fj(Ljava/util/List;Ljava/util/List;)V
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lcom/bytedance/adsdk/lottie/Fj/Fj/hjc;",
            ">;",
            "Ljava/util/List<",
            "Lcom/bytedance/adsdk/lottie/Fj/Fj/hjc;",
            ">;)V"
        }
    .end annotation

    const/4 p2, 0x0

    :goto_0
    invoke-interface {p1}, Ljava/util/List;->size()I

    move-result v0

    if-ge p2, v0, :cond_2

    invoke-interface {p1, p2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/bytedance/adsdk/lottie/Fj/Fj/hjc;

    instance-of v1, v0, Lcom/bytedance/adsdk/lottie/Fj/Fj/Af;

    if-eqz v1, :cond_0

    move-object v1, v0

    check-cast v1, Lcom/bytedance/adsdk/lottie/Fj/Fj/Af;

    invoke-virtual {v1}, Lcom/bytedance/adsdk/lottie/Fj/Fj/Af;->ex()Lcom/bytedance/adsdk/lottie/hjc/ex/vYf$Fj;

    move-result-object v2

    sget-object v3, Lcom/bytedance/adsdk/lottie/hjc/ex/vYf$Fj;->Fj:Lcom/bytedance/adsdk/lottie/hjc/ex/vYf$Fj;

    if-ne v2, v3, :cond_0

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/JW;->mSE:Lcom/bytedance/adsdk/lottie/Fj/Fj/ex;

    invoke-virtual {v0, v1}, Lcom/bytedance/adsdk/lottie/Fj/Fj/ex;->Fj(Lcom/bytedance/adsdk/lottie/Fj/Fj/Af;)V

    invoke-virtual {v1, p0}, Lcom/bytedance/adsdk/lottie/Fj/Fj/Af;->Fj(Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$Fj;)V

    goto :goto_1

    :cond_0
    instance-of v1, v0, Lcom/bytedance/adsdk/lottie/Fj/Fj/Ql;

    if-eqz v1, :cond_1

    check-cast v0, Lcom/bytedance/adsdk/lottie/Fj/Fj/Ql;

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/Fj/Fj/Ql;->ex()Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/JW;->Ko:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    :cond_1
    :goto_1
    add-int/lit8 p2, p2, 0x1

    goto :goto_0

    :cond_2
    return-void
.end method

.method public eV()Landroid/graphics/Path;
    .locals 17

    move-object/from16 v0, p0

    iget-boolean v1, v0, Lcom/bytedance/adsdk/lottie/Fj/Fj/JW;->rAx:Z

    if-eqz v1, :cond_0

    iget-object v1, v0, Lcom/bytedance/adsdk/lottie/Fj/Fj/JW;->Fj:Landroid/graphics/Path;

    return-object v1

    :cond_0
    iget-object v1, v0, Lcom/bytedance/adsdk/lottie/Fj/Fj/JW;->Fj:Landroid/graphics/Path;

    invoke-virtual {v1}, Landroid/graphics/Path;->reset()V

    iget-boolean v1, v0, Lcom/bytedance/adsdk/lottie/Fj/Fj/JW;->eV:Z

    const/4 v2, 0x1

    if-eqz v1, :cond_1

    iput-boolean v2, v0, Lcom/bytedance/adsdk/lottie/Fj/Fj/JW;->rAx:Z

    iget-object v1, v0, Lcom/bytedance/adsdk/lottie/Fj/Fj/JW;->Fj:Landroid/graphics/Path;

    return-object v1

    :cond_1
    iget-object v1, v0, Lcom/bytedance/adsdk/lottie/Fj/Fj/JW;->svN:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    invoke-virtual {v1}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->svN()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Landroid/graphics/PointF;

    iget v3, v1, Landroid/graphics/PointF;->x:F

    const/high16 v4, 0x40000000    # 2.0f

    div-float/2addr v3, v4

    iget v1, v1, Landroid/graphics/PointF;->y:F

    div-float/2addr v1, v4

    iget-object v5, v0, Lcom/bytedance/adsdk/lottie/Fj/Fj/JW;->BcC:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    const/4 v6, 0x0

    if-nez v5, :cond_2

    const/4 v5, 0x0

    goto :goto_0

    :cond_2
    check-cast v5, Lcom/bytedance/adsdk/lottie/Fj/ex/eV;

    invoke-virtual {v5}, Lcom/bytedance/adsdk/lottie/Fj/ex/eV;->mSE()F

    move-result v5

    :goto_0
    cmpl-float v7, v5, v6

    if-nez v7, :cond_3

    iget-object v7, v0, Lcom/bytedance/adsdk/lottie/Fj/Fj/JW;->Ko:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    if-eqz v7, :cond_3

    invoke-virtual {v7}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->svN()Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Ljava/lang/Float;

    invoke-virtual {v5}, Ljava/lang/Float;->floatValue()F

    move-result v5

    invoke-static {v3, v1}, Ljava/lang/Math;->min(FF)F

    move-result v7

    invoke-static {v5, v7}, Ljava/lang/Math;->min(FF)F

    move-result v5

    :cond_3
    invoke-static {v3, v1}, Ljava/lang/Math;->min(FF)F

    move-result v7

    cmpl-float v8, v5, v7

    if-lez v8, :cond_4

    move v5, v7

    :cond_4
    iget-object v7, v0, Lcom/bytedance/adsdk/lottie/Fj/Fj/JW;->WR:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    invoke-virtual {v7}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->svN()Ljava/lang/Object;

    move-result-object v7

    check-cast v7, Landroid/graphics/PointF;

    iget-object v8, v0, Lcom/bytedance/adsdk/lottie/Fj/Fj/JW;->Fj:Landroid/graphics/Path;

    iget v9, v7, Landroid/graphics/PointF;->x:F

    add-float/2addr v9, v3

    iget v10, v7, Landroid/graphics/PointF;->y:F

    sub-float/2addr v10, v1

    add-float/2addr v10, v5

    invoke-virtual {v8, v9, v10}, Landroid/graphics/Path;->moveTo(FF)V

    iget-object v8, v0, Lcom/bytedance/adsdk/lottie/Fj/Fj/JW;->Fj:Landroid/graphics/Path;

    iget v9, v7, Landroid/graphics/PointF;->x:F

    add-float/2addr v9, v3

    iget v10, v7, Landroid/graphics/PointF;->y:F

    add-float/2addr v10, v1

    sub-float/2addr v10, v5

    invoke-virtual {v8, v9, v10}, Landroid/graphics/Path;->lineTo(FF)V

    const/4 v8, 0x0

    const/high16 v9, 0x42b40000    # 90.0f

    cmpl-float v10, v5, v6

    if-lez v10, :cond_5

    iget-object v11, v0, Lcom/bytedance/adsdk/lottie/Fj/Fj/JW;->ex:Landroid/graphics/RectF;

    iget v12, v7, Landroid/graphics/PointF;->x:F

    add-float v13, v12, v3

    mul-float v14, v5, v4

    sub-float/2addr v13, v14

    iget v15, v7, Landroid/graphics/PointF;->y:F

    add-float v16, v15, v1

    sub-float v14, v16, v14

    add-float/2addr v12, v3

    add-float/2addr v15, v1

    invoke-virtual {v11, v13, v14, v12, v15}, Landroid/graphics/RectF;->set(FFFF)V

    iget-object v11, v0, Lcom/bytedance/adsdk/lottie/Fj/Fj/JW;->Fj:Landroid/graphics/Path;

    iget-object v12, v0, Lcom/bytedance/adsdk/lottie/Fj/Fj/JW;->ex:Landroid/graphics/RectF;

    invoke-virtual {v11, v12, v6, v9, v8}, Landroid/graphics/Path;->arcTo(Landroid/graphics/RectF;FFZ)V

    :cond_5
    iget-object v6, v0, Lcom/bytedance/adsdk/lottie/Fj/Fj/JW;->Fj:Landroid/graphics/Path;

    iget v11, v7, Landroid/graphics/PointF;->x:F

    sub-float/2addr v11, v3

    add-float/2addr v11, v5

    iget v12, v7, Landroid/graphics/PointF;->y:F

    add-float/2addr v12, v1

    invoke-virtual {v6, v11, v12}, Landroid/graphics/Path;->lineTo(FF)V

    if-lez v10, :cond_6

    iget-object v6, v0, Lcom/bytedance/adsdk/lottie/Fj/Fj/JW;->ex:Landroid/graphics/RectF;

    iget v11, v7, Landroid/graphics/PointF;->x:F

    sub-float v12, v11, v3

    iget v13, v7, Landroid/graphics/PointF;->y:F

    add-float v14, v13, v1

    mul-float v15, v5, v4

    sub-float/2addr v14, v15

    sub-float/2addr v11, v3

    add-float/2addr v11, v15

    add-float/2addr v13, v1

    invoke-virtual {v6, v12, v14, v11, v13}, Landroid/graphics/RectF;->set(FFFF)V

    iget-object v6, v0, Lcom/bytedance/adsdk/lottie/Fj/Fj/JW;->Fj:Landroid/graphics/Path;

    iget-object v11, v0, Lcom/bytedance/adsdk/lottie/Fj/Fj/JW;->ex:Landroid/graphics/RectF;

    invoke-virtual {v6, v11, v9, v9, v8}, Landroid/graphics/Path;->arcTo(Landroid/graphics/RectF;FFZ)V

    :cond_6
    iget-object v6, v0, Lcom/bytedance/adsdk/lottie/Fj/Fj/JW;->Fj:Landroid/graphics/Path;

    iget v11, v7, Landroid/graphics/PointF;->x:F

    sub-float/2addr v11, v3

    iget v12, v7, Landroid/graphics/PointF;->y:F

    sub-float/2addr v12, v1

    add-float/2addr v12, v5

    invoke-virtual {v6, v11, v12}, Landroid/graphics/Path;->lineTo(FF)V

    if-lez v10, :cond_7

    iget-object v6, v0, Lcom/bytedance/adsdk/lottie/Fj/Fj/JW;->ex:Landroid/graphics/RectF;

    iget v11, v7, Landroid/graphics/PointF;->x:F

    sub-float v12, v11, v3

    iget v13, v7, Landroid/graphics/PointF;->y:F

    sub-float v14, v13, v1

    sub-float/2addr v11, v3

    mul-float v15, v5, v4

    add-float/2addr v11, v15

    sub-float/2addr v13, v1

    add-float/2addr v13, v15

    invoke-virtual {v6, v12, v14, v11, v13}, Landroid/graphics/RectF;->set(FFFF)V

    iget-object v6, v0, Lcom/bytedance/adsdk/lottie/Fj/Fj/JW;->Fj:Landroid/graphics/Path;

    iget-object v11, v0, Lcom/bytedance/adsdk/lottie/Fj/Fj/JW;->ex:Landroid/graphics/RectF;

    const/high16 v12, 0x43340000    # 180.0f

    invoke-virtual {v6, v11, v12, v9, v8}, Landroid/graphics/Path;->arcTo(Landroid/graphics/RectF;FFZ)V

    :cond_7
    iget-object v6, v0, Lcom/bytedance/adsdk/lottie/Fj/Fj/JW;->Fj:Landroid/graphics/Path;

    iget v11, v7, Landroid/graphics/PointF;->x:F

    add-float/2addr v11, v3

    sub-float/2addr v11, v5

    iget v12, v7, Landroid/graphics/PointF;->y:F

    sub-float/2addr v12, v1

    invoke-virtual {v6, v11, v12}, Landroid/graphics/Path;->lineTo(FF)V

    if-lez v10, :cond_8

    iget-object v6, v0, Lcom/bytedance/adsdk/lottie/Fj/Fj/JW;->ex:Landroid/graphics/RectF;

    iget v10, v7, Landroid/graphics/PointF;->x:F

    add-float v11, v10, v3

    mul-float v5, v5, v4

    sub-float/2addr v11, v5

    iget v4, v7, Landroid/graphics/PointF;->y:F

    sub-float v7, v4, v1

    add-float/2addr v10, v3

    sub-float/2addr v4, v1

    add-float/2addr v4, v5

    invoke-virtual {v6, v11, v7, v10, v4}, Landroid/graphics/RectF;->set(FFFF)V

    iget-object v1, v0, Lcom/bytedance/adsdk/lottie/Fj/Fj/JW;->Fj:Landroid/graphics/Path;

    iget-object v3, v0, Lcom/bytedance/adsdk/lottie/Fj/Fj/JW;->ex:Landroid/graphics/RectF;

    const/high16 v4, 0x43870000    # 270.0f

    invoke-virtual {v1, v3, v4, v9, v8}, Landroid/graphics/Path;->arcTo(Landroid/graphics/RectF;FFZ)V

    :cond_8
    iget-object v1, v0, Lcom/bytedance/adsdk/lottie/Fj/Fj/JW;->Fj:Landroid/graphics/Path;

    invoke-virtual {v1}, Landroid/graphics/Path;->close()V

    iget-object v1, v0, Lcom/bytedance/adsdk/lottie/Fj/Fj/JW;->mSE:Lcom/bytedance/adsdk/lottie/Fj/Fj/ex;

    iget-object v3, v0, Lcom/bytedance/adsdk/lottie/Fj/Fj/JW;->Fj:Landroid/graphics/Path;

    invoke-virtual {v1, v3}, Lcom/bytedance/adsdk/lottie/Fj/Fj/ex;->Fj(Landroid/graphics/Path;)V

    iput-boolean v2, v0, Lcom/bytedance/adsdk/lottie/Fj/Fj/JW;->rAx:Z

    iget-object v1, v0, Lcom/bytedance/adsdk/lottie/Fj/Fj/JW;->Fj:Landroid/graphics/Path;

    return-object v1
.end method
