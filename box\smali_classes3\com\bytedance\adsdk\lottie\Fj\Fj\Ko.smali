.class interface abstract Lcom/bytedance/adsdk/lottie/Fj/Fj/Ko;
.super Ljava/lang/Object;


# virtual methods
.method public abstract Fj(Ljava/util/ListIterator;)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/ListIterator<",
            "Lcom/bytedance/adsdk/lottie/Fj/Fj/hjc;",
            ">;)V"
        }
    .end annotation
.end method
