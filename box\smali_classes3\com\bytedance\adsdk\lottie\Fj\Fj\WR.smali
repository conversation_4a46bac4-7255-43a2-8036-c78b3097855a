.class public Lcom/bytedance/adsdk/lottie/Fj/Fj/WR;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/adsdk/lottie/Fj/Fj/dG;
.implements Lcom/bytedance/adsdk/lottie/Fj/Fj/rAx;
.implements Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$Fj;


# instance fields
.field private BcC:Z

.field private final Fj:Landroid/graphics/Path;

.field private final Ubf:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/bytedance/adsdk/lottie/Fj/ex/Fj<",
            "*",
            "Landroid/graphics/PointF;",
            ">;"
        }
    .end annotation
.end field

.field private final WR:Lcom/bytedance/adsdk/lottie/hjc/ex/ex;

.field private final eV:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/bytedance/adsdk/lottie/Fj/ex/Fj<",
            "*",
            "Landroid/graphics/PointF;",
            ">;"
        }
    .end annotation
.end field

.field private final ex:Ljava/lang/String;

.field private final hjc:Lcom/bytedance/adsdk/lottie/BcC;

.field private final svN:Lcom/bytedance/adsdk/lottie/Fj/Fj/ex;


# direct methods
.method public constructor <init>(Lcom/bytedance/adsdk/lottie/BcC;Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;Lcom/bytedance/adsdk/lottie/hjc/ex/ex;)V
    .locals 1

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    new-instance v0, Landroid/graphics/Path;

    invoke-direct {v0}, Landroid/graphics/Path;-><init>()V

    iput-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/WR;->Fj:Landroid/graphics/Path;

    new-instance v0, Lcom/bytedance/adsdk/lottie/Fj/Fj/ex;

    invoke-direct {v0}, Lcom/bytedance/adsdk/lottie/Fj/Fj/ex;-><init>()V

    iput-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/WR;->svN:Lcom/bytedance/adsdk/lottie/Fj/Fj/ex;

    invoke-virtual {p3}, Lcom/bytedance/adsdk/lottie/hjc/ex/ex;->Fj()Ljava/lang/String;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/WR;->ex:Ljava/lang/String;

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/WR;->hjc:Lcom/bytedance/adsdk/lottie/BcC;

    invoke-virtual {p3}, Lcom/bytedance/adsdk/lottie/hjc/ex/ex;->hjc()Lcom/bytedance/adsdk/lottie/hjc/Fj/WR;

    move-result-object p1

    invoke-virtual {p1}, Lcom/bytedance/adsdk/lottie/hjc/Fj/WR;->Fj()Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    move-result-object p1

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/WR;->eV:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    invoke-virtual {p3}, Lcom/bytedance/adsdk/lottie/hjc/ex/ex;->ex()Lcom/bytedance/adsdk/lottie/hjc/Fj/dG;

    move-result-object v0

    invoke-interface {v0}, Lcom/bytedance/adsdk/lottie/hjc/Fj/dG;->Fj()Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/WR;->Ubf:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    iput-object p3, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/WR;->WR:Lcom/bytedance/adsdk/lottie/hjc/ex/ex;

    invoke-virtual {p2, p1}, Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;->Fj(Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;)V

    invoke-virtual {p2, v0}, Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;->Fj(Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;)V

    invoke-virtual {p1, p0}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->Fj(Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$Fj;)V

    invoke-virtual {v0, p0}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->Fj(Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$Fj;)V

    return-void
.end method

.method private ex()V
    .locals 1

    const/4 v0, 0x0

    iput-boolean v0, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/WR;->BcC:Z

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/WR;->hjc:Lcom/bytedance/adsdk/lottie/BcC;

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/BcC;->invalidateSelf()V

    return-void
.end method


# virtual methods
.method public Fj()V
    .locals 0

    invoke-direct {p0}, Lcom/bytedance/adsdk/lottie/Fj/Fj/WR;->ex()V

    return-void
.end method

.method public Fj(Ljava/util/List;Ljava/util/List;)V
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lcom/bytedance/adsdk/lottie/Fj/Fj/hjc;",
            ">;",
            "Ljava/util/List<",
            "Lcom/bytedance/adsdk/lottie/Fj/Fj/hjc;",
            ">;)V"
        }
    .end annotation

    const/4 p2, 0x0

    :goto_0
    invoke-interface {p1}, Ljava/util/List;->size()I

    move-result v0

    if-ge p2, v0, :cond_1

    invoke-interface {p1, p2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/bytedance/adsdk/lottie/Fj/Fj/hjc;

    instance-of v1, v0, Lcom/bytedance/adsdk/lottie/Fj/Fj/Af;

    if-eqz v1, :cond_0

    check-cast v0, Lcom/bytedance/adsdk/lottie/Fj/Fj/Af;

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/Fj/Fj/Af;->ex()Lcom/bytedance/adsdk/lottie/hjc/ex/vYf$Fj;

    move-result-object v1

    sget-object v2, Lcom/bytedance/adsdk/lottie/hjc/ex/vYf$Fj;->Fj:Lcom/bytedance/adsdk/lottie/hjc/ex/vYf$Fj;

    if-ne v1, v2, :cond_0

    iget-object v1, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/WR;->svN:Lcom/bytedance/adsdk/lottie/Fj/Fj/ex;

    invoke-virtual {v1, v0}, Lcom/bytedance/adsdk/lottie/Fj/Fj/ex;->Fj(Lcom/bytedance/adsdk/lottie/Fj/Fj/Af;)V

    invoke-virtual {v0, p0}, Lcom/bytedance/adsdk/lottie/Fj/Fj/Af;->Fj(Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$Fj;)V

    :cond_0
    add-int/lit8 p2, p2, 0x1

    goto :goto_0

    :cond_1
    return-void
.end method

.method public eV()Landroid/graphics/Path;
    .locals 22

    move-object/from16 v0, p0

    iget-boolean v1, v0, Lcom/bytedance/adsdk/lottie/Fj/Fj/WR;->BcC:Z

    if-eqz v1, :cond_0

    iget-object v1, v0, Lcom/bytedance/adsdk/lottie/Fj/Fj/WR;->Fj:Landroid/graphics/Path;

    return-object v1

    :cond_0
    iget-object v1, v0, Lcom/bytedance/adsdk/lottie/Fj/Fj/WR;->Fj:Landroid/graphics/Path;

    invoke-virtual {v1}, Landroid/graphics/Path;->reset()V

    iget-object v1, v0, Lcom/bytedance/adsdk/lottie/Fj/Fj/WR;->WR:Lcom/bytedance/adsdk/lottie/hjc/ex/ex;

    invoke-virtual {v1}, Lcom/bytedance/adsdk/lottie/hjc/ex/ex;->Ubf()Z

    move-result v1

    const/4 v2, 0x1

    if-eqz v1, :cond_1

    iput-boolean v2, v0, Lcom/bytedance/adsdk/lottie/Fj/Fj/WR;->BcC:Z

    iget-object v1, v0, Lcom/bytedance/adsdk/lottie/Fj/Fj/WR;->Fj:Landroid/graphics/Path;

    return-object v1

    :cond_1
    iget-object v1, v0, Lcom/bytedance/adsdk/lottie/Fj/Fj/WR;->eV:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    invoke-virtual {v1}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->svN()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Landroid/graphics/PointF;

    iget v3, v1, Landroid/graphics/PointF;->x:F

    const/high16 v4, 0x40000000    # 2.0f

    div-float/2addr v3, v4

    iget v1, v1, Landroid/graphics/PointF;->y:F

    div-float/2addr v1, v4

    const v4, 0x3f0d6239    # 0.55228f

    mul-float v12, v3, v4

    mul-float v4, v4, v1

    iget-object v5, v0, Lcom/bytedance/adsdk/lottie/Fj/Fj/WR;->Fj:Landroid/graphics/Path;

    invoke-virtual {v5}, Landroid/graphics/Path;->reset()V

    iget-object v5, v0, Lcom/bytedance/adsdk/lottie/Fj/Fj/WR;->WR:Lcom/bytedance/adsdk/lottie/hjc/ex/ex;

    invoke-virtual {v5}, Lcom/bytedance/adsdk/lottie/hjc/ex/ex;->eV()Z

    move-result v5

    const/4 v13, 0x0

    if-eqz v5, :cond_2

    iget-object v5, v0, Lcom/bytedance/adsdk/lottie/Fj/Fj/WR;->Fj:Landroid/graphics/Path;

    neg-float v11, v1

    invoke-virtual {v5, v13, v11}, Landroid/graphics/Path;->moveTo(FF)V

    iget-object v14, v0, Lcom/bytedance/adsdk/lottie/Fj/Fj/WR;->Fj:Landroid/graphics/Path;

    sub-float v8, v13, v12

    neg-float v6, v3

    sub-float v21, v13, v4

    const/16 v20, 0x0

    move v15, v8

    move/from16 v16, v11

    move/from16 v17, v6

    move/from16 v18, v21

    move/from16 v19, v6

    invoke-virtual/range {v14 .. v20}, Landroid/graphics/Path;->cubicTo(FFFFFF)V

    iget-object v5, v0, Lcom/bytedance/adsdk/lottie/Fj/Fj/WR;->Fj:Landroid/graphics/Path;

    add-float/2addr v4, v13

    const/4 v10, 0x0

    move v7, v4

    move v9, v1

    move v14, v11

    move v11, v1

    invoke-virtual/range {v5 .. v11}, Landroid/graphics/Path;->cubicTo(FFFFFF)V

    iget-object v5, v0, Lcom/bytedance/adsdk/lottie/Fj/Fj/WR;->Fj:Landroid/graphics/Path;

    add-float/2addr v12, v13

    const/4 v11, 0x0

    move v6, v12

    move v7, v1

    move v8, v3

    move v9, v4

    move v10, v3

    invoke-virtual/range {v5 .. v11}, Landroid/graphics/Path;->cubicTo(FFFFFF)V

    iget-object v5, v0, Lcom/bytedance/adsdk/lottie/Fj/Fj/WR;->Fj:Landroid/graphics/Path;

    const/4 v10, 0x0

    move v6, v3

    move/from16 v7, v21

    move v8, v12

    move v9, v14

    move v11, v14

    invoke-virtual/range {v5 .. v11}, Landroid/graphics/Path;->cubicTo(FFFFFF)V

    goto :goto_0

    :cond_2
    iget-object v5, v0, Lcom/bytedance/adsdk/lottie/Fj/Fj/WR;->Fj:Landroid/graphics/Path;

    neg-float v15, v1

    invoke-virtual {v5, v13, v15}, Landroid/graphics/Path;->moveTo(FF)V

    iget-object v5, v0, Lcom/bytedance/adsdk/lottie/Fj/Fj/WR;->Fj:Landroid/graphics/Path;

    add-float v14, v12, v13

    sub-float v16, v13, v4

    const/4 v11, 0x0

    move v6, v14

    move v7, v15

    move v8, v3

    move/from16 v9, v16

    move v10, v3

    invoke-virtual/range {v5 .. v11}, Landroid/graphics/Path;->cubicTo(FFFFFF)V

    iget-object v5, v0, Lcom/bytedance/adsdk/lottie/Fj/Fj/WR;->Fj:Landroid/graphics/Path;

    add-float/2addr v4, v13

    const/4 v10, 0x0

    move v6, v3

    move v7, v4

    move v8, v14

    move v9, v1

    move v11, v1

    invoke-virtual/range {v5 .. v11}, Landroid/graphics/Path;->cubicTo(FFFFFF)V

    iget-object v5, v0, Lcom/bytedance/adsdk/lottie/Fj/Fj/WR;->Fj:Landroid/graphics/Path;

    sub-float v17, v13, v12

    neg-float v3, v3

    const/4 v11, 0x0

    move/from16 v6, v17

    move v7, v1

    move v8, v3

    move v9, v4

    move v10, v3

    invoke-virtual/range {v5 .. v11}, Landroid/graphics/Path;->cubicTo(FFFFFF)V

    iget-object v14, v0, Lcom/bytedance/adsdk/lottie/Fj/Fj/WR;->Fj:Landroid/graphics/Path;

    const/16 v19, 0x0

    move v1, v15

    move v15, v3

    move/from16 v18, v1

    move/from16 v20, v1

    invoke-virtual/range {v14 .. v20}, Landroid/graphics/Path;->cubicTo(FFFFFF)V

    :goto_0
    iget-object v1, v0, Lcom/bytedance/adsdk/lottie/Fj/Fj/WR;->Ubf:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    invoke-virtual {v1}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->svN()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Landroid/graphics/PointF;

    iget-object v3, v0, Lcom/bytedance/adsdk/lottie/Fj/Fj/WR;->Fj:Landroid/graphics/Path;

    iget v4, v1, Landroid/graphics/PointF;->x:F

    iget v1, v1, Landroid/graphics/PointF;->y:F

    invoke-virtual {v3, v4, v1}, Landroid/graphics/Path;->offset(FF)V

    iget-object v1, v0, Lcom/bytedance/adsdk/lottie/Fj/Fj/WR;->Fj:Landroid/graphics/Path;

    invoke-virtual {v1}, Landroid/graphics/Path;->close()V

    iget-object v1, v0, Lcom/bytedance/adsdk/lottie/Fj/Fj/WR;->svN:Lcom/bytedance/adsdk/lottie/Fj/Fj/ex;

    iget-object v3, v0, Lcom/bytedance/adsdk/lottie/Fj/Fj/WR;->Fj:Landroid/graphics/Path;

    invoke-virtual {v1, v3}, Lcom/bytedance/adsdk/lottie/Fj/Fj/ex;->Fj(Landroid/graphics/Path;)V

    iput-boolean v2, v0, Lcom/bytedance/adsdk/lottie/Fj/Fj/WR;->BcC:Z

    iget-object v1, v0, Lcom/bytedance/adsdk/lottie/Fj/Fj/WR;->Fj:Landroid/graphics/Path;

    return-object v1
.end method
