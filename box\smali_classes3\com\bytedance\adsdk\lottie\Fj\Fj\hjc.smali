.class public interface abstract Lcom/bytedance/adsdk/lottie/Fj/Fj/hjc;
.super Ljava/lang/Object;


# virtual methods
.method public abstract Fj(Ljava/util/List;Ljava/util/List;)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lcom/bytedance/adsdk/lottie/Fj/Fj/hjc;",
            ">;",
            "Ljava/util/List<",
            "Lcom/bytedance/adsdk/lottie/Fj/Fj/hjc;",
            ">;)V"
        }
    .end annotation
.end method
