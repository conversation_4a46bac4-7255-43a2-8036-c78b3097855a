.class public Lcom/bytedance/adsdk/lottie/Fj/Fj/mE;
.super Lcom/bytedance/adsdk/lottie/Fj/Fj/Fj;


# instance fields
.field private BcC:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/bytedance/adsdk/lottie/Fj/ex/Fj<",
            "Landroid/graphics/ColorFilter;",
            "Landroid/graphics/ColorFilter;",
            ">;"
        }
    .end annotation
.end field

.field private final Ubf:Ljava/lang/String;

.field private final WR:Z

.field private final eV:Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;

.field private final svN:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/bytedance/adsdk/lottie/Fj/ex/Fj<",
            "Ljava/lang/Integer;",
            "Ljava/lang/Integer;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lcom/bytedance/adsdk/lottie/BcC;Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;Lcom/bytedance/adsdk/lottie/hjc/ex/rS;)V
    .locals 11

    invoke-virtual {p3}, Lcom/bytedance/adsdk/lottie/hjc/ex/rS;->svN()Lcom/bytedance/adsdk/lottie/hjc/ex/rS$Fj;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/hjc/ex/rS$Fj;->Fj()Landroid/graphics/Paint$Cap;

    move-result-object v4

    invoke-virtual {p3}, Lcom/bytedance/adsdk/lottie/hjc/ex/rS;->BcC()Lcom/bytedance/adsdk/lottie/hjc/ex/rS$ex;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/hjc/ex/rS$ex;->Fj()Landroid/graphics/Paint$Join;

    move-result-object v5

    invoke-virtual {p3}, Lcom/bytedance/adsdk/lottie/hjc/ex/rS;->mSE()F

    move-result v6

    invoke-virtual {p3}, Lcom/bytedance/adsdk/lottie/hjc/ex/rS;->hjc()Lcom/bytedance/adsdk/lottie/hjc/Fj/eV;

    move-result-object v7

    invoke-virtual {p3}, Lcom/bytedance/adsdk/lottie/hjc/ex/rS;->eV()Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

    move-result-object v8

    invoke-virtual {p3}, Lcom/bytedance/adsdk/lottie/hjc/ex/rS;->Ubf()Ljava/util/List;

    move-result-object v9

    invoke-virtual {p3}, Lcom/bytedance/adsdk/lottie/hjc/ex/rS;->WR()Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

    move-result-object v10

    move-object v1, p0

    move-object v2, p1

    move-object v3, p2

    invoke-direct/range {v1 .. v10}, Lcom/bytedance/adsdk/lottie/Fj/Fj/Fj;-><init>(Lcom/bytedance/adsdk/lottie/BcC;Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;Landroid/graphics/Paint$Cap;Landroid/graphics/Paint$Join;FLcom/bytedance/adsdk/lottie/hjc/Fj/eV;Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;Ljava/util/List;Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;)V

    iput-object p2, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/mE;->eV:Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;

    invoke-virtual {p3}, Lcom/bytedance/adsdk/lottie/hjc/ex/rS;->Fj()Ljava/lang/String;

    move-result-object p1

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/mE;->Ubf:Ljava/lang/String;

    invoke-virtual {p3}, Lcom/bytedance/adsdk/lottie/hjc/ex/rS;->Ko()Z

    move-result p1

    iput-boolean p1, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/mE;->WR:Z

    invoke-virtual {p3}, Lcom/bytedance/adsdk/lottie/hjc/ex/rS;->ex()Lcom/bytedance/adsdk/lottie/hjc/Fj/Fj;

    move-result-object p1

    invoke-virtual {p1}, Lcom/bytedance/adsdk/lottie/hjc/Fj/Fj;->Fj()Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    move-result-object p1

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/mE;->svN:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    invoke-virtual {p1, p0}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->Fj(Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$Fj;)V

    invoke-virtual {p2, p1}, Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;->Fj(Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;)V

    return-void
.end method


# virtual methods
.method public Fj(Landroid/graphics/Canvas;Landroid/graphics/Matrix;I)V
    .locals 2

    iget-boolean v0, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/mE;->WR:Z

    if-eqz v0, :cond_0

    return-void

    :cond_0
    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/Fj;->ex:Landroid/graphics/Paint;

    iget-object v1, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/mE;->svN:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    check-cast v1, Lcom/bytedance/adsdk/lottie/Fj/ex/ex;

    invoke-virtual {v1}, Lcom/bytedance/adsdk/lottie/Fj/ex/ex;->mSE()I

    move-result v1

    invoke-virtual {v0, v1}, Landroid/graphics/Paint;->setColor(I)V

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/mE;->BcC:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    if-eqz v0, :cond_1

    iget-object v1, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/Fj;->ex:Landroid/graphics/Paint;

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->svN()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroid/graphics/ColorFilter;

    invoke-virtual {v1, v0}, Landroid/graphics/Paint;->setColorFilter(Landroid/graphics/ColorFilter;)Landroid/graphics/ColorFilter;

    :cond_1
    invoke-super {p0, p1, p2, p3}, Lcom/bytedance/adsdk/lottie/Fj/Fj/Fj;->Fj(Landroid/graphics/Canvas;Landroid/graphics/Matrix;I)V

    return-void
.end method
