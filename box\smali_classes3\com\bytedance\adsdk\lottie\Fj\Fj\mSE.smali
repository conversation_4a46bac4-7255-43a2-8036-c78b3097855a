.class public Lcom/bytedance/adsdk/lottie/Fj/Fj/mSE;
.super Lcom/bytedance/adsdk/lottie/Fj/Fj/Fj;


# instance fields
.field private final BcC:Landroid/graphics/RectF;

.field private final Ko:I

.field private Tc:Lcom/bytedance/adsdk/lottie/Fj/ex/Ql;

.field private final UYd:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/bytedance/adsdk/lottie/Fj/ex/Fj<",
            "Landroid/graphics/PointF;",
            "Landroid/graphics/PointF;",
            ">;"
        }
    .end annotation
.end field

.field private final Ubf:Z

.field private final WR:Landroid/util/LongSparseArray;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Landroid/util/LongSparseArray<",
            "Landroid/graphics/LinearGradient;",
            ">;"
        }
    .end annotation
.end field

.field private final dG:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/bytedance/adsdk/lottie/Fj/ex/Fj<",
            "Landroid/graphics/PointF;",
            "Landroid/graphics/PointF;",
            ">;"
        }
    .end annotation
.end field

.field private final eV:Ljava/lang/String;

.field private final mSE:Lcom/bytedance/adsdk/lottie/hjc/ex/svN;

.field private final rAx:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/bytedance/adsdk/lottie/Fj/ex/Fj<",
            "Lcom/bytedance/adsdk/lottie/hjc/ex/eV;",
            "Lcom/bytedance/adsdk/lottie/hjc/ex/eV;",
            ">;"
        }
    .end annotation
.end field

.field private final svN:Landroid/util/LongSparseArray;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Landroid/util/LongSparseArray<",
            "Landroid/graphics/RadialGradient;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lcom/bytedance/adsdk/lottie/BcC;Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;Lcom/bytedance/adsdk/lottie/hjc/ex/WR;)V
    .locals 11

    invoke-virtual {p3}, Lcom/bytedance/adsdk/lottie/hjc/ex/WR;->BcC()Lcom/bytedance/adsdk/lottie/hjc/ex/rS$Fj;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/hjc/ex/rS$Fj;->Fj()Landroid/graphics/Paint$Cap;

    move-result-object v4

    invoke-virtual {p3}, Lcom/bytedance/adsdk/lottie/hjc/ex/WR;->mSE()Lcom/bytedance/adsdk/lottie/hjc/ex/rS$ex;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/hjc/ex/rS$ex;->Fj()Landroid/graphics/Paint$Join;

    move-result-object v5

    invoke-virtual {p3}, Lcom/bytedance/adsdk/lottie/hjc/ex/WR;->UYd()F

    move-result v6

    invoke-virtual {p3}, Lcom/bytedance/adsdk/lottie/hjc/ex/WR;->eV()Lcom/bytedance/adsdk/lottie/hjc/Fj/eV;

    move-result-object v7

    invoke-virtual {p3}, Lcom/bytedance/adsdk/lottie/hjc/ex/WR;->svN()Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

    move-result-object v8

    invoke-virtual {p3}, Lcom/bytedance/adsdk/lottie/hjc/ex/WR;->Ko()Ljava/util/List;

    move-result-object v9

    invoke-virtual {p3}, Lcom/bytedance/adsdk/lottie/hjc/ex/WR;->rAx()Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

    move-result-object v10

    move-object v1, p0

    move-object v2, p1

    move-object v3, p2

    invoke-direct/range {v1 .. v10}, Lcom/bytedance/adsdk/lottie/Fj/Fj/Fj;-><init>(Lcom/bytedance/adsdk/lottie/BcC;Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;Landroid/graphics/Paint$Cap;Landroid/graphics/Paint$Join;FLcom/bytedance/adsdk/lottie/hjc/Fj/eV;Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;Ljava/util/List;Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;)V

    new-instance v0, Landroid/util/LongSparseArray;

    invoke-direct {v0}, Landroid/util/LongSparseArray;-><init>()V

    iput-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/mSE;->WR:Landroid/util/LongSparseArray;

    new-instance v0, Landroid/util/LongSparseArray;

    invoke-direct {v0}, Landroid/util/LongSparseArray;-><init>()V

    iput-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/mSE;->svN:Landroid/util/LongSparseArray;

    new-instance v0, Landroid/graphics/RectF;

    invoke-direct {v0}, Landroid/graphics/RectF;-><init>()V

    iput-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/mSE;->BcC:Landroid/graphics/RectF;

    invoke-virtual {p3}, Lcom/bytedance/adsdk/lottie/hjc/ex/WR;->Fj()Ljava/lang/String;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/mSE;->eV:Ljava/lang/String;

    invoke-virtual {p3}, Lcom/bytedance/adsdk/lottie/hjc/ex/WR;->ex()Lcom/bytedance/adsdk/lottie/hjc/ex/svN;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/mSE;->mSE:Lcom/bytedance/adsdk/lottie/hjc/ex/svN;

    invoke-virtual {p3}, Lcom/bytedance/adsdk/lottie/hjc/ex/WR;->dG()Z

    move-result v0

    iput-boolean v0, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/mSE;->Ubf:Z

    invoke-virtual {p1}, Lcom/bytedance/adsdk/lottie/BcC;->mC()Lcom/bytedance/adsdk/lottie/WR;

    move-result-object p1

    invoke-virtual {p1}, Lcom/bytedance/adsdk/lottie/WR;->Ubf()F

    move-result p1

    const/high16 v0, 0x42000000    # 32.0f

    div-float/2addr p1, v0

    float-to-int p1, p1

    iput p1, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/mSE;->Ko:I

    invoke-virtual {p3}, Lcom/bytedance/adsdk/lottie/hjc/ex/WR;->hjc()Lcom/bytedance/adsdk/lottie/hjc/Fj/hjc;

    move-result-object p1

    invoke-virtual {p1}, Lcom/bytedance/adsdk/lottie/hjc/Fj/hjc;->Fj()Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    move-result-object p1

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/mSE;->rAx:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    invoke-virtual {p1, p0}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->Fj(Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$Fj;)V

    invoke-virtual {p2, p1}, Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;->Fj(Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;)V

    invoke-virtual {p3}, Lcom/bytedance/adsdk/lottie/hjc/ex/WR;->Ubf()Lcom/bytedance/adsdk/lottie/hjc/Fj/WR;

    move-result-object p1

    invoke-virtual {p1}, Lcom/bytedance/adsdk/lottie/hjc/Fj/WR;->Fj()Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    move-result-object p1

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/mSE;->UYd:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    invoke-virtual {p1, p0}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->Fj(Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$Fj;)V

    invoke-virtual {p2, p1}, Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;->Fj(Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;)V

    invoke-virtual {p3}, Lcom/bytedance/adsdk/lottie/hjc/ex/WR;->WR()Lcom/bytedance/adsdk/lottie/hjc/Fj/WR;

    move-result-object p1

    invoke-virtual {p1}, Lcom/bytedance/adsdk/lottie/hjc/Fj/WR;->Fj()Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    move-result-object p1

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/mSE;->dG:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    invoke-virtual {p1, p0}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->Fj(Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$Fj;)V

    invoke-virtual {p2, p1}, Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;->Fj(Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;)V

    return-void
.end method

.method private Fj([I)[I
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/mSE;->Tc:Lcom/bytedance/adsdk/lottie/Fj/ex/Ql;

    if-nez v0, :cond_0

    return-object p1

    :cond_0
    const/4 p1, 0x0

    throw p1
.end method

.method private eV()I
    .locals 4

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/mSE;->UYd:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->BcC()F

    move-result v0

    iget v1, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/mSE;->Ko:I

    int-to-float v1, v1

    mul-float v0, v0, v1

    invoke-static {v0}, Ljava/lang/Math;->round(F)I

    move-result v0

    iget-object v1, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/mSE;->dG:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    invoke-virtual {v1}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->BcC()F

    move-result v1

    iget v2, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/mSE;->Ko:I

    int-to-float v2, v2

    mul-float v1, v1, v2

    invoke-static {v1}, Ljava/lang/Math;->round(F)I

    move-result v1

    iget-object v2, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/mSE;->rAx:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    invoke-virtual {v2}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->BcC()F

    move-result v2

    iget v3, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/mSE;->Ko:I

    int-to-float v3, v3

    mul-float v2, v2, v3

    invoke-static {v2}, Ljava/lang/Math;->round(F)I

    move-result v2

    if-eqz v0, :cond_0

    mul-int/lit16 v0, v0, 0x20f

    goto :goto_0

    :cond_0
    const/16 v0, 0x11

    :goto_0
    if-eqz v1, :cond_1

    mul-int/lit8 v0, v0, 0x1f

    mul-int v0, v0, v1

    :cond_1
    if-eqz v2, :cond_2

    mul-int/lit8 v0, v0, 0x1f

    mul-int v0, v0, v2

    :cond_2
    return v0
.end method

.method private ex()Landroid/graphics/LinearGradient;
    .locals 14

    invoke-direct {p0}, Lcom/bytedance/adsdk/lottie/Fj/Fj/mSE;->eV()I

    move-result v0

    iget-object v1, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/mSE;->WR:Landroid/util/LongSparseArray;

    int-to-long v2, v0

    invoke-virtual {v1, v2, v3}, Landroid/util/LongSparseArray;->get(J)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroid/graphics/LinearGradient;

    if-eqz v0, :cond_0

    return-object v0

    :cond_0
    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/mSE;->UYd:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->svN()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroid/graphics/PointF;

    iget-object v1, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/mSE;->dG:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    invoke-virtual {v1}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->svN()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Landroid/graphics/PointF;

    iget-object v4, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/mSE;->rAx:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    invoke-virtual {v4}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->svN()Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lcom/bytedance/adsdk/lottie/hjc/ex/eV;

    invoke-virtual {v4}, Lcom/bytedance/adsdk/lottie/hjc/ex/eV;->ex()[I

    move-result-object v5

    invoke-direct {p0, v5}, Lcom/bytedance/adsdk/lottie/Fj/Fj/mSE;->Fj([I)[I

    move-result-object v11

    invoke-virtual {v4}, Lcom/bytedance/adsdk/lottie/hjc/ex/eV;->Fj()[F

    move-result-object v12

    iget v7, v0, Landroid/graphics/PointF;->x:F

    iget v8, v0, Landroid/graphics/PointF;->y:F

    iget v9, v1, Landroid/graphics/PointF;->x:F

    iget v10, v1, Landroid/graphics/PointF;->y:F

    new-instance v0, Landroid/graphics/LinearGradient;

    sget-object v13, Landroid/graphics/Shader$TileMode;->CLAMP:Landroid/graphics/Shader$TileMode;

    move-object v6, v0

    invoke-direct/range {v6 .. v13}, Landroid/graphics/LinearGradient;-><init>(FFFF[I[FLandroid/graphics/Shader$TileMode;)V

    iget-object v1, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/mSE;->WR:Landroid/util/LongSparseArray;

    invoke-virtual {v1, v2, v3, v0}, Landroid/util/LongSparseArray;->put(JLjava/lang/Object;)V

    return-object v0
.end method

.method private hjc()Landroid/graphics/RadialGradient;
    .locals 13

    invoke-direct {p0}, Lcom/bytedance/adsdk/lottie/Fj/Fj/mSE;->eV()I

    move-result v0

    iget-object v1, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/mSE;->svN:Landroid/util/LongSparseArray;

    int-to-long v2, v0

    invoke-virtual {v1, v2, v3}, Landroid/util/LongSparseArray;->get(J)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroid/graphics/RadialGradient;

    if-eqz v0, :cond_0

    return-object v0

    :cond_0
    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/mSE;->UYd:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->svN()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroid/graphics/PointF;

    iget-object v1, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/mSE;->dG:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    invoke-virtual {v1}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->svN()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Landroid/graphics/PointF;

    iget-object v4, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/mSE;->rAx:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    invoke-virtual {v4}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->svN()Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lcom/bytedance/adsdk/lottie/hjc/ex/eV;

    invoke-virtual {v4}, Lcom/bytedance/adsdk/lottie/hjc/ex/eV;->ex()[I

    move-result-object v5

    invoke-direct {p0, v5}, Lcom/bytedance/adsdk/lottie/Fj/Fj/mSE;->Fj([I)[I

    move-result-object v10

    invoke-virtual {v4}, Lcom/bytedance/adsdk/lottie/hjc/ex/eV;->Fj()[F

    move-result-object v11

    iget v7, v0, Landroid/graphics/PointF;->x:F

    iget v8, v0, Landroid/graphics/PointF;->y:F

    iget v0, v1, Landroid/graphics/PointF;->x:F

    iget v1, v1, Landroid/graphics/PointF;->y:F

    sub-float/2addr v0, v7

    float-to-double v4, v0

    sub-float/2addr v1, v8

    float-to-double v0, v1

    invoke-static {v4, v5, v0, v1}, Ljava/lang/Math;->hypot(DD)D

    move-result-wide v0

    double-to-float v9, v0

    new-instance v0, Landroid/graphics/RadialGradient;

    sget-object v12, Landroid/graphics/Shader$TileMode;->CLAMP:Landroid/graphics/Shader$TileMode;

    move-object v6, v0

    invoke-direct/range {v6 .. v12}, Landroid/graphics/RadialGradient;-><init>(FFF[I[FLandroid/graphics/Shader$TileMode;)V

    iget-object v1, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/mSE;->svN:Landroid/util/LongSparseArray;

    invoke-virtual {v1, v2, v3, v0}, Landroid/util/LongSparseArray;->put(JLjava/lang/Object;)V

    return-object v0
.end method


# virtual methods
.method public Fj(Landroid/graphics/Canvas;Landroid/graphics/Matrix;I)V
    .locals 2

    iget-boolean v0, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/mSE;->Ubf:Z

    if-eqz v0, :cond_0

    return-void

    :cond_0
    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/mSE;->BcC:Landroid/graphics/RectF;

    const/4 v1, 0x0

    invoke-virtual {p0, v0, p2, v1}, Lcom/bytedance/adsdk/lottie/Fj/Fj/Fj;->Fj(Landroid/graphics/RectF;Landroid/graphics/Matrix;Z)V

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/mSE;->mSE:Lcom/bytedance/adsdk/lottie/hjc/ex/svN;

    sget-object v1, Lcom/bytedance/adsdk/lottie/hjc/ex/svN;->Fj:Lcom/bytedance/adsdk/lottie/hjc/ex/svN;

    if-ne v0, v1, :cond_1

    invoke-direct {p0}, Lcom/bytedance/adsdk/lottie/Fj/Fj/mSE;->ex()Landroid/graphics/LinearGradient;

    move-result-object v0

    goto :goto_0

    :cond_1
    invoke-direct {p0}, Lcom/bytedance/adsdk/lottie/Fj/Fj/mSE;->hjc()Landroid/graphics/RadialGradient;

    move-result-object v0

    :goto_0
    invoke-virtual {v0, p2}, Landroid/graphics/Shader;->setLocalMatrix(Landroid/graphics/Matrix;)V

    iget-object v1, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/Fj;->ex:Landroid/graphics/Paint;

    invoke-virtual {v1, v0}, Landroid/graphics/Paint;->setShader(Landroid/graphics/Shader;)Landroid/graphics/Shader;

    invoke-super {p0, p1, p2, p3}, Lcom/bytedance/adsdk/lottie/Fj/Fj/Fj;->Fj(Landroid/graphics/Canvas;Landroid/graphics/Matrix;I)V

    return-void
.end method
