.class public Lcom/bytedance/adsdk/lottie/Fj/Fj/rS;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/adsdk/lottie/Fj/Fj/dG;
.implements Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$Fj;


# instance fields
.field private final Fj:Landroid/graphics/Path;

.field private final Ubf:Lcom/bytedance/adsdk/lottie/Fj/ex/dG;

.field private WR:Z

.field private final eV:Lcom/bytedance/adsdk/lottie/BcC;

.field private final ex:Ljava/lang/String;

.field private final hjc:Z

.field private final svN:Lcom/bytedance/adsdk/lottie/Fj/Fj/ex;


# direct methods
.method public constructor <init>(Lcom/bytedance/adsdk/lottie/BcC;Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;Lcom/bytedance/adsdk/lottie/hjc/ex/Ql;)V
    .locals 1

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    new-instance v0, Landroid/graphics/Path;

    invoke-direct {v0}, Landroid/graphics/Path;-><init>()V

    iput-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/rS;->Fj:Landroid/graphics/Path;

    new-instance v0, Lcom/bytedance/adsdk/lottie/Fj/Fj/ex;

    invoke-direct {v0}, Lcom/bytedance/adsdk/lottie/Fj/Fj/ex;-><init>()V

    iput-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/rS;->svN:Lcom/bytedance/adsdk/lottie/Fj/Fj/ex;

    invoke-virtual {p3}, Lcom/bytedance/adsdk/lottie/hjc/ex/Ql;->Fj()Ljava/lang/String;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/rS;->ex:Ljava/lang/String;

    invoke-virtual {p3}, Lcom/bytedance/adsdk/lottie/hjc/ex/Ql;->hjc()Z

    move-result v0

    iput-boolean v0, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/rS;->hjc:Z

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/rS;->eV:Lcom/bytedance/adsdk/lottie/BcC;

    invoke-virtual {p3}, Lcom/bytedance/adsdk/lottie/hjc/ex/Ql;->ex()Lcom/bytedance/adsdk/lottie/hjc/Fj/BcC;

    move-result-object p1

    invoke-virtual {p1}, Lcom/bytedance/adsdk/lottie/hjc/Fj/BcC;->eV()Lcom/bytedance/adsdk/lottie/Fj/ex/dG;

    move-result-object p1

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/rS;->Ubf:Lcom/bytedance/adsdk/lottie/Fj/ex/dG;

    invoke-virtual {p2, p1}, Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;->Fj(Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;)V

    invoke-virtual {p1, p0}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->Fj(Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$Fj;)V

    return-void
.end method

.method private ex()V
    .locals 1

    const/4 v0, 0x0

    iput-boolean v0, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/rS;->WR:Z

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/rS;->eV:Lcom/bytedance/adsdk/lottie/BcC;

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/BcC;->invalidateSelf()V

    return-void
.end method


# virtual methods
.method public Fj()V
    .locals 0

    invoke-direct {p0}, Lcom/bytedance/adsdk/lottie/Fj/Fj/rS;->ex()V

    return-void
.end method

.method public Fj(Ljava/util/List;Ljava/util/List;)V
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lcom/bytedance/adsdk/lottie/Fj/Fj/hjc;",
            ">;",
            "Ljava/util/List<",
            "Lcom/bytedance/adsdk/lottie/Fj/Fj/hjc;",
            ">;)V"
        }
    .end annotation

    const/4 p2, 0x0

    const/4 v0, 0x0

    :goto_0
    invoke-interface {p1}, Ljava/util/List;->size()I

    move-result v1

    if-ge v0, v1, :cond_3

    invoke-interface {p1, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/bytedance/adsdk/lottie/Fj/Fj/hjc;

    instance-of v2, v1, Lcom/bytedance/adsdk/lottie/Fj/Fj/Af;

    if-eqz v2, :cond_0

    move-object v2, v1

    check-cast v2, Lcom/bytedance/adsdk/lottie/Fj/Fj/Af;

    invoke-virtual {v2}, Lcom/bytedance/adsdk/lottie/Fj/Fj/Af;->ex()Lcom/bytedance/adsdk/lottie/hjc/ex/vYf$Fj;

    move-result-object v3

    sget-object v4, Lcom/bytedance/adsdk/lottie/hjc/ex/vYf$Fj;->Fj:Lcom/bytedance/adsdk/lottie/hjc/ex/vYf$Fj;

    if-ne v3, v4, :cond_0

    iget-object v1, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/rS;->svN:Lcom/bytedance/adsdk/lottie/Fj/Fj/ex;

    invoke-virtual {v1, v2}, Lcom/bytedance/adsdk/lottie/Fj/Fj/ex;->Fj(Lcom/bytedance/adsdk/lottie/Fj/Fj/Af;)V

    invoke-virtual {v2, p0}, Lcom/bytedance/adsdk/lottie/Fj/Fj/Af;->Fj(Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$Fj;)V

    goto :goto_1

    :cond_0
    instance-of v2, v1, Lcom/bytedance/adsdk/lottie/Fj/Fj/vYf;

    if-eqz v2, :cond_2

    if-nez p2, :cond_1

    new-instance p2, Ljava/util/ArrayList;

    invoke-direct {p2}, Ljava/util/ArrayList;-><init>()V

    :cond_1
    check-cast v1, Lcom/bytedance/adsdk/lottie/Fj/Fj/vYf;

    invoke-interface {p2, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    :cond_2
    :goto_1
    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :cond_3
    iget-object p1, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/rS;->Ubf:Lcom/bytedance/adsdk/lottie/Fj/ex/dG;

    invoke-virtual {p1, p2}, Lcom/bytedance/adsdk/lottie/Fj/ex/dG;->Fj(Ljava/util/List;)V

    return-void
.end method

.method public eV()Landroid/graphics/Path;
    .locals 3

    iget-boolean v0, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/rS;->WR:Z

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/rS;->Fj:Landroid/graphics/Path;

    return-object v0

    :cond_0
    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/rS;->Fj:Landroid/graphics/Path;

    invoke-virtual {v0}, Landroid/graphics/Path;->reset()V

    iget-boolean v0, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/rS;->hjc:Z

    const/4 v1, 0x1

    if-eqz v0, :cond_1

    iput-boolean v1, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/rS;->WR:Z

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/rS;->Fj:Landroid/graphics/Path;

    return-object v0

    :cond_1
    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/rS;->Ubf:Lcom/bytedance/adsdk/lottie/Fj/ex/dG;

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->svN()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroid/graphics/Path;

    if-nez v0, :cond_2

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/rS;->Fj:Landroid/graphics/Path;

    return-object v0

    :cond_2
    iget-object v2, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/rS;->Fj:Landroid/graphics/Path;

    invoke-virtual {v2, v0}, Landroid/graphics/Path;->set(Landroid/graphics/Path;)V

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/rS;->Fj:Landroid/graphics/Path;

    sget-object v2, Landroid/graphics/Path$FillType;->EVEN_ODD:Landroid/graphics/Path$FillType;

    invoke-virtual {v0, v2}, Landroid/graphics/Path;->setFillType(Landroid/graphics/Path$FillType;)V

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/rS;->svN:Lcom/bytedance/adsdk/lottie/Fj/Fj/ex;

    iget-object v2, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/rS;->Fj:Landroid/graphics/Path;

    invoke-virtual {v0, v2}, Lcom/bytedance/adsdk/lottie/Fj/Fj/ex;->Fj(Landroid/graphics/Path;)V

    iput-boolean v1, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/rS;->WR:Z

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/Fj/rS;->Fj:Landroid/graphics/Path;

    return-object v0
.end method
