.class public Lcom/bytedance/adsdk/lottie/Fj/ex/BcC;
.super Ljava/lang/Object;


# instance fields
.field private final Fj:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lcom/bytedance/adsdk/lottie/Fj/ex/Fj<",
            "Lcom/bytedance/adsdk/lottie/hjc/ex/Tc;",
            "Landroid/graphics/Path;",
            ">;>;"
        }
    .end annotation
.end field

.field private final ex:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lcom/bytedance/adsdk/lottie/Fj/ex/Fj<",
            "Ljava/lang/Integer;",
            "Ljava/lang/Integer;",
            ">;>;"
        }
    .end annotation
.end field

.field private final hjc:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lcom/bytedance/adsdk/lottie/hjc/ex/BcC;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(Ljava/util/List;)V
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lcom/bytedance/adsdk/lottie/hjc/ex/BcC;",
            ">;)V"
        }
    .end annotation

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/BcC;->hjc:Ljava/util/List;

    new-instance v0, Ljava/util/ArrayList;

    invoke-interface {p1}, Ljava/util/List;->size()I

    move-result v1

    invoke-direct {v0, v1}, Ljava/util/ArrayList;-><init>(I)V

    iput-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/BcC;->Fj:Ljava/util/List;

    new-instance v0, Ljava/util/ArrayList;

    invoke-interface {p1}, Ljava/util/List;->size()I

    move-result v1

    invoke-direct {v0, v1}, Ljava/util/ArrayList;-><init>(I)V

    iput-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/BcC;->ex:Ljava/util/List;

    const/4 v0, 0x0

    :goto_0
    invoke-interface {p1}, Ljava/util/List;->size()I

    move-result v1

    if-ge v0, v1, :cond_0

    iget-object v1, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/BcC;->Fj:Ljava/util/List;

    invoke-interface {p1, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/bytedance/adsdk/lottie/hjc/ex/BcC;

    invoke-virtual {v2}, Lcom/bytedance/adsdk/lottie/hjc/ex/BcC;->ex()Lcom/bytedance/adsdk/lottie/hjc/Fj/BcC;

    move-result-object v2

    invoke-virtual {v2}, Lcom/bytedance/adsdk/lottie/hjc/Fj/BcC;->eV()Lcom/bytedance/adsdk/lottie/Fj/ex/dG;

    move-result-object v2

    invoke-interface {v1, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    invoke-interface {p1, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/bytedance/adsdk/lottie/hjc/ex/BcC;

    invoke-virtual {v1}, Lcom/bytedance/adsdk/lottie/hjc/ex/BcC;->hjc()Lcom/bytedance/adsdk/lottie/hjc/Fj/eV;

    move-result-object v1

    iget-object v2, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/BcC;->ex:Ljava/util/List;

    invoke-virtual {v1}, Lcom/bytedance/adsdk/lottie/hjc/Fj/eV;->Fj()Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    move-result-object v1

    invoke-interface {v2, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :cond_0
    return-void
.end method


# virtual methods
.method public Fj()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lcom/bytedance/adsdk/lottie/hjc/ex/BcC;",
            ">;"
        }
    .end annotation

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/BcC;->hjc:Ljava/util/List;

    return-object v0
.end method

.method public ex()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lcom/bytedance/adsdk/lottie/Fj/ex/Fj<",
            "Lcom/bytedance/adsdk/lottie/hjc/ex/Tc;",
            "Landroid/graphics/Path;",
            ">;>;"
        }
    .end annotation

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/BcC;->Fj:Ljava/util/List;

    return-object v0
.end method

.method public hjc()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lcom/bytedance/adsdk/lottie/Fj/ex/Fj<",
            "Ljava/lang/Integer;",
            "Ljava/lang/Integer;",
            ">;>;"
        }
    .end annotation

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/BcC;->ex:Ljava/util/List;

    return-object v0
.end method
