.class final Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$Ubf;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$hjc;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "Ubf"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "<T:",
        "Ljava/lang/Object;",
        ">",
        "Ljava/lang/Object;",
        "Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$hjc<",
        "TT;>;"
    }
.end annotation


# instance fields
.field private final Fj:Lcom/bytedance/adsdk/lottie/svN/Fj;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/bytedance/adsdk/lottie/svN/Fj<",
            "TT;>;"
        }
    .end annotation
.end field

.field private ex:F


# direct methods
.method public constructor <init>(Ljava/util/List;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "+",
            "Lcom/bytedance/adsdk/lottie/svN/Fj<",
            "TT;>;>;)V"
        }
    .end annotation

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/high16 v0, -0x40800000    # -1.0f

    iput v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$Ubf;->ex:F

    const/4 v0, 0x0

    invoke-interface {p1, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/bytedance/adsdk/lottie/svN/Fj;

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$Ubf;->Fj:Lcom/bytedance/adsdk/lottie/svN/Fj;

    return-void
.end method


# virtual methods
.method public Fj()Z
    .locals 1

    const/4 v0, 0x0

    return v0
.end method

.method public Fj(F)Z
    .locals 0

    iget-object p1, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$Ubf;->Fj:Lcom/bytedance/adsdk/lottie/svN/Fj;

    invoke-virtual {p1}, Lcom/bytedance/adsdk/lottie/svN/Fj;->Ubf()Z

    move-result p1

    if-nez p1, :cond_0

    const/4 p1, 0x1

    return p1

    :cond_0
    const/4 p1, 0x0

    return p1
.end method

.method public eV()F
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$Ubf;->Fj:Lcom/bytedance/adsdk/lottie/svN/Fj;

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/svN/Fj;->eV()F

    move-result v0

    return v0
.end method

.method public ex()Lcom/bytedance/adsdk/lottie/svN/Fj;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lcom/bytedance/adsdk/lottie/svN/Fj<",
            "TT;>;"
        }
    .end annotation

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$Ubf;->Fj:Lcom/bytedance/adsdk/lottie/svN/Fj;

    return-object v0
.end method

.method public ex(F)Z
    .locals 1

    iget v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$Ubf;->ex:F

    cmpl-float v0, v0, p1

    if-nez v0, :cond_0

    const/4 p1, 0x1

    return p1

    :cond_0
    iput p1, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$Ubf;->ex:F

    const/4 p1, 0x0

    return p1
.end method

.method public hjc()F
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$Ubf;->Fj:Lcom/bytedance/adsdk/lottie/svN/Fj;

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/svN/Fj;->hjc()F

    move-result v0

    return v0
.end method
