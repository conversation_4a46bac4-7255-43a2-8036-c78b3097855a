.class final Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$eV;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$hjc;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "eV"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "<T:",
        "Ljava/lang/Object;",
        ">",
        "Ljava/lang/Object;",
        "Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$hjc<",
        "TT;>;"
    }
.end annotation


# instance fields
.field private final Fj:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "+",
            "Lcom/bytedance/adsdk/lottie/svN/Fj<",
            "TT;>;>;"
        }
    .end annotation
.end field

.field private eV:F

.field private ex:Lcom/bytedance/adsdk/lottie/svN/Fj;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/bytedance/adsdk/lottie/svN/Fj<",
            "TT;>;"
        }
    .end annotation
.end field

.field private hjc:Lcom/bytedance/adsdk/lottie/svN/Fj;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/bytedance/adsdk/lottie/svN/Fj<",
            "TT;>;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(Ljava/util/List;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "+",
            "Lcom/bytedance/adsdk/lottie/svN/Fj<",
            "TT;>;>;)V"
        }
    .end annotation

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, 0x0

    iput-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$eV;->hjc:Lcom/bytedance/adsdk/lottie/svN/Fj;

    const/high16 v0, -0x40800000    # -1.0f

    iput v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$eV;->eV:F

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$eV;->Fj:Ljava/util/List;

    const/4 p1, 0x0

    invoke-direct {p0, p1}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$eV;->hjc(F)Lcom/bytedance/adsdk/lottie/svN/Fj;

    move-result-object p1

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$eV;->ex:Lcom/bytedance/adsdk/lottie/svN/Fj;

    return-void
.end method

.method private hjc(F)Lcom/bytedance/adsdk/lottie/svN/Fj;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(F)",
            "Lcom/bytedance/adsdk/lottie/svN/Fj<",
            "TT;>;"
        }
    .end annotation

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$eV;->Fj:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v1

    add-int/lit8 v1, v1, -0x1

    invoke-interface {v0, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/bytedance/adsdk/lottie/svN/Fj;

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/svN/Fj;->hjc()F

    move-result v1

    cmpl-float v1, p1, v1

    if-ltz v1, :cond_0

    return-object v0

    :cond_0
    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$eV;->Fj:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    add-int/lit8 v0, v0, -0x2

    :goto_0
    if-lez v0, :cond_2

    iget-object v1, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$eV;->Fj:Ljava/util/List;

    invoke-interface {v1, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/bytedance/adsdk/lottie/svN/Fj;

    iget-object v2, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$eV;->ex:Lcom/bytedance/adsdk/lottie/svN/Fj;

    if-eq v2, v1, :cond_1

    invoke-virtual {v1, p1}, Lcom/bytedance/adsdk/lottie/svN/Fj;->Fj(F)Z

    move-result v2

    if-eqz v2, :cond_1

    return-object v1

    :cond_1
    add-int/lit8 v0, v0, -0x1

    goto :goto_0

    :cond_2
    iget-object p1, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$eV;->Fj:Ljava/util/List;

    const/4 v0, 0x0

    invoke-interface {p1, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/bytedance/adsdk/lottie/svN/Fj;

    return-object p1
.end method


# virtual methods
.method public Fj()Z
    .locals 1

    const/4 v0, 0x0

    return v0
.end method

.method public Fj(F)Z
    .locals 2

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$eV;->ex:Lcom/bytedance/adsdk/lottie/svN/Fj;

    invoke-virtual {v0, p1}, Lcom/bytedance/adsdk/lottie/svN/Fj;->Fj(F)Z

    move-result v0

    const/4 v1, 0x1

    if-eqz v0, :cond_1

    iget-object p1, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$eV;->ex:Lcom/bytedance/adsdk/lottie/svN/Fj;

    invoke-virtual {p1}, Lcom/bytedance/adsdk/lottie/svN/Fj;->Ubf()Z

    move-result p1

    if-nez p1, :cond_0

    return v1

    :cond_0
    const/4 p1, 0x0

    return p1

    :cond_1
    invoke-direct {p0, p1}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$eV;->hjc(F)Lcom/bytedance/adsdk/lottie/svN/Fj;

    move-result-object p1

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$eV;->ex:Lcom/bytedance/adsdk/lottie/svN/Fj;

    return v1
.end method

.method public eV()F
    .locals 2

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$eV;->Fj:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v1

    add-int/lit8 v1, v1, -0x1

    invoke-interface {v0, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/bytedance/adsdk/lottie/svN/Fj;

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/svN/Fj;->eV()F

    move-result v0

    return v0
.end method

.method public ex()Lcom/bytedance/adsdk/lottie/svN/Fj;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lcom/bytedance/adsdk/lottie/svN/Fj<",
            "TT;>;"
        }
    .end annotation

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$eV;->ex:Lcom/bytedance/adsdk/lottie/svN/Fj;

    return-object v0
.end method

.method public ex(F)Z
    .locals 2

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$eV;->hjc:Lcom/bytedance/adsdk/lottie/svN/Fj;

    iget-object v1, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$eV;->ex:Lcom/bytedance/adsdk/lottie/svN/Fj;

    if-ne v0, v1, :cond_0

    iget v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$eV;->eV:F

    cmpl-float v0, v0, p1

    if-nez v0, :cond_0

    const/4 p1, 0x1

    return p1

    :cond_0
    iput-object v1, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$eV;->hjc:Lcom/bytedance/adsdk/lottie/svN/Fj;

    iput p1, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$eV;->eV:F

    const/4 p1, 0x0

    return p1
.end method

.method public hjc()F
    .locals 2

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$eV;->Fj:Ljava/util/List;

    const/4 v1, 0x0

    invoke-interface {v0, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/bytedance/adsdk/lottie/svN/Fj;

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/svN/Fj;->hjc()F

    move-result v0

    return v0
.end method
