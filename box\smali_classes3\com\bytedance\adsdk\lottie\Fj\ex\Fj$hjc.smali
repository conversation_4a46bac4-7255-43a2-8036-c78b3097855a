.class interface abstract Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$hjc;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "hjc"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "<T:",
        "Ljava/lang/Object;",
        ">",
        "Ljava/lang/Object;"
    }
.end annotation


# virtual methods
.method public abstract Fj()Z
.end method

.method public abstract Fj(F)Z
.end method

.method public abstract eV()F
    .annotation build Lcom/bytedance/component/sdk/annotation/FloatRange;
        from = 0.0
        to = 1.0
    .end annotation
.end method

.method public abstract ex()Lcom/bytedance/adsdk/lottie/svN/Fj;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lcom/bytedance/adsdk/lottie/svN/Fj<",
            "TT;>;"
        }
    .end annotation
.end method

.method public abstract ex(F)Z
.end method

.method public abstract hjc()F
    .annotation build Lcom/bytedance/component/sdk/annotation/FloatRange;
        from = 0.0
        to = 1.0
    .end annotation
.end method
