.class public abstract Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$eV;,
        Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$Ubf;,
        Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$ex;,
        Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$hjc;,
        Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$Fj;
    }
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "<K:",
        "Ljava/lang/Object;",
        "A:",
        "Ljava/lang/Object;",
        ">",
        "Ljava/lang/Object;"
    }
.end annotation


# instance fields
.field private BcC:F

.field final Fj:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$Fj;",
            ">;"
        }
    .end annotation
.end field

.field private final Ubf:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$hjc;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$hjc<",
            "TK;>;"
        }
    .end annotation
.end field

.field private WR:Ljava/lang/Object;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "TA;"
        }
    .end annotation
.end field

.field private eV:Z

.field protected ex:F

.field protected hjc:Lcom/bytedance/adsdk/lottie/svN/ex;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/bytedance/adsdk/lottie/svN/ex<",
            "TA;>;"
        }
    .end annotation
.end field

.field private svN:F


# direct methods
.method public constructor <init>(Ljava/util/List;)V
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "+",
            "Lcom/bytedance/adsdk/lottie/svN/Fj<",
            "TK;>;>;)V"
        }
    .end annotation

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    new-instance v0, Ljava/util/ArrayList;

    const/4 v1, 0x1

    invoke-direct {v0, v1}, Ljava/util/ArrayList;-><init>(I)V

    iput-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->Fj:Ljava/util/List;

    const/4 v0, 0x0

    iput-boolean v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->eV:Z

    const/4 v0, 0x0

    iput v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->ex:F

    const/4 v0, 0x0

    iput-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->WR:Ljava/lang/Object;

    const/high16 v0, -0x40800000    # -1.0f

    iput v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->svN:F

    iput v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->BcC:F

    invoke-static {p1}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->Fj(Ljava/util/List;)Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$hjc;

    move-result-object p1

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->Ubf:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$hjc;

    return-void
.end method

.method private static Fj(Ljava/util/List;)Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$hjc;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">(",
            "Ljava/util/List<",
            "+",
            "Lcom/bytedance/adsdk/lottie/svN/Fj<",
            "TT;>;>;)",
            "Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$hjc<",
            "TT;>;"
        }
    .end annotation

    invoke-interface {p0}, Ljava/util/List;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_0

    new-instance p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$ex;

    const/4 v0, 0x0

    invoke-direct {p0, v0}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$ex;-><init>(Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$1;)V

    return-object p0

    :cond_0
    invoke-interface {p0}, Ljava/util/List;->size()I

    move-result v0

    const/4 v1, 0x1

    if-ne v0, v1, :cond_1

    new-instance v0, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$Ubf;

    invoke-direct {v0, p0}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$Ubf;-><init>(Ljava/util/List;)V

    return-object v0

    :cond_1
    new-instance v0, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$eV;

    invoke-direct {v0, p0}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$eV;-><init>(Ljava/util/List;)V

    return-object v0
.end method

.method private mSE()F
    .locals 2
    .annotation build Lcom/bytedance/component/sdk/annotation/FloatRange;
        from = 0.0
        to = 1.0
    .end annotation

    iget v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->svN:F

    const/high16 v1, -0x40800000    # -1.0f

    cmpl-float v0, v0, v1

    if-nez v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->Ubf:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$hjc;

    invoke-interface {v0}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$hjc;->hjc()F

    move-result v0

    iput v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->svN:F

    :cond_0
    iget v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->svN:F

    return v0
.end method


# virtual methods
.method public BcC()F
    .locals 1

    iget v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->ex:F

    return v0
.end method

.method public abstract Fj(Lcom/bytedance/adsdk/lottie/svN/Fj;F)Ljava/lang/Object;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/bytedance/adsdk/lottie/svN/Fj<",
            "TK;>;F)TA;"
        }
    .end annotation
.end method

.method public Fj(Lcom/bytedance/adsdk/lottie/svN/Fj;FFF)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/bytedance/adsdk/lottie/svN/Fj<",
            "TK;>;FFF)TA;"
        }
    .end annotation

    new-instance p1, Ljava/lang/UnsupportedOperationException;

    const-string p2, "This animation does not support split dimensions!"

    invoke-direct {p1, p2}, Ljava/lang/UnsupportedOperationException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public Fj()V
    .locals 1

    const/4 v0, 0x1

    iput-boolean v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->eV:Z

    return-void
.end method

.method public Fj(F)V
    .locals 1
    .param p1    # F
        .annotation build Lcom/bytedance/component/sdk/annotation/FloatRange;
            from = 0.0
            to = 1.0
        .end annotation
    .end param

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->Ubf:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$hjc;

    invoke-interface {v0}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$hjc;->Fj()Z

    move-result v0

    if-eqz v0, :cond_0

    return-void

    :cond_0
    invoke-direct {p0}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->mSE()F

    move-result v0

    cmpg-float v0, p1, v0

    if-gez v0, :cond_1

    invoke-direct {p0}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->mSE()F

    move-result p1

    goto :goto_0

    :cond_1
    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->WR()F

    move-result v0

    cmpl-float v0, p1, v0

    if-lez v0, :cond_2

    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->WR()F

    move-result p1

    :cond_2
    :goto_0
    iget v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->ex:F

    cmpl-float v0, p1, v0

    if-nez v0, :cond_3

    return-void

    :cond_3
    iput p1, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->ex:F

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->Ubf:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$hjc;

    invoke-interface {v0, p1}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$hjc;->Fj(F)Z

    move-result p1

    if-eqz p1, :cond_4

    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->ex()V

    :cond_4
    return-void
.end method

.method public Fj(Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$Fj;)V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->Fj:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public Ubf()F
    .locals 2

    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->hjc()Lcom/bytedance/adsdk/lottie/svN/Fj;

    move-result-object v0

    if-eqz v0, :cond_1

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/svN/Fj;->Ubf()Z

    move-result v1

    if-eqz v1, :cond_0

    goto :goto_0

    :cond_0
    iget-object v0, v0, Lcom/bytedance/adsdk/lottie/svN/Fj;->hjc:Landroid/view/animation/Interpolator;

    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->eV()F

    move-result v1

    invoke-interface {v0, v1}, Landroid/animation/TimeInterpolator;->getInterpolation(F)F

    move-result v0

    return v0

    :cond_1
    :goto_0
    const/4 v0, 0x0

    return v0
.end method

.method public WR()F
    .locals 2
    .annotation build Lcom/bytedance/component/sdk/annotation/FloatRange;
        from = 0.0
        to = 1.0
    .end annotation

    iget v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->BcC:F

    const/high16 v1, -0x40800000    # -1.0f

    cmpl-float v0, v0, v1

    if-nez v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->Ubf:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$hjc;

    invoke-interface {v0}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$hjc;->eV()F

    move-result v0

    iput v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->BcC:F

    :cond_0
    iget v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->BcC:F

    return v0
.end method

.method public eV()F
    .locals 3

    iget-boolean v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->eV:Z

    const/4 v1, 0x0

    if-eqz v0, :cond_0

    return v1

    :cond_0
    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->hjc()Lcom/bytedance/adsdk/lottie/svN/Fj;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/svN/Fj;->Ubf()Z

    move-result v2

    if-eqz v2, :cond_1

    return v1

    :cond_1
    iget v1, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->ex:F

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/svN/Fj;->hjc()F

    move-result v2

    sub-float/2addr v1, v2

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/svN/Fj;->eV()F

    move-result v2

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/svN/Fj;->hjc()F

    move-result v0

    sub-float/2addr v2, v0

    div-float/2addr v1, v2

    return v1
.end method

.method public ex()V
    .locals 2

    const/4 v0, 0x0

    :goto_0
    iget-object v1, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->Fj:Ljava/util/List;

    invoke-interface {v1}, Ljava/util/List;->size()I

    move-result v1

    if-ge v0, v1, :cond_0

    iget-object v1, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->Fj:Ljava/util/List;

    invoke-interface {v1, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$Fj;

    invoke-interface {v1}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$Fj;->Fj()V

    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :cond_0
    return-void
.end method

.method public hjc()Lcom/bytedance/adsdk/lottie/svN/Fj;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lcom/bytedance/adsdk/lottie/svN/Fj<",
            "TK;>;"
        }
    .end annotation

    const-string v0, "BaseKeyframeAnimation#getCurrentKeyframe"

    invoke-static {v0}, Lcom/bytedance/adsdk/lottie/Ubf;->Fj(Ljava/lang/String;)V

    iget-object v1, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->Ubf:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$hjc;

    invoke-interface {v1}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$hjc;->ex()Lcom/bytedance/adsdk/lottie/svN/Fj;

    move-result-object v1

    invoke-static {v0}, Lcom/bytedance/adsdk/lottie/Ubf;->ex(Ljava/lang/String;)F

    return-object v1
.end method

.method public svN()Ljava/lang/Object;
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()TA;"
        }
    .end annotation

    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->eV()F

    move-result v0

    iget-object v1, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->hjc:Lcom/bytedance/adsdk/lottie/svN/ex;

    if-nez v1, :cond_0

    iget-object v1, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->Ubf:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$hjc;

    invoke-interface {v1, v0}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$hjc;->ex(F)Z

    move-result v1

    if-eqz v1, :cond_0

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->WR:Ljava/lang/Object;

    return-object v0

    :cond_0
    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->hjc()Lcom/bytedance/adsdk/lottie/svN/Fj;

    move-result-object v1

    iget-object v2, v1, Lcom/bytedance/adsdk/lottie/svN/Fj;->eV:Landroid/view/animation/Interpolator;

    if-eqz v2, :cond_1

    iget-object v3, v1, Lcom/bytedance/adsdk/lottie/svN/Fj;->Ubf:Landroid/view/animation/Interpolator;

    if-eqz v3, :cond_1

    invoke-interface {v2, v0}, Landroid/animation/TimeInterpolator;->getInterpolation(F)F

    move-result v2

    iget-object v3, v1, Lcom/bytedance/adsdk/lottie/svN/Fj;->Ubf:Landroid/view/animation/Interpolator;

    invoke-interface {v3, v0}, Landroid/animation/TimeInterpolator;->getInterpolation(F)F

    move-result v3

    invoke-virtual {p0, v1, v0, v2, v3}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->Fj(Lcom/bytedance/adsdk/lottie/svN/Fj;FFF)Ljava/lang/Object;

    move-result-object v0

    goto :goto_0

    :cond_1
    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->Ubf()F

    move-result v0

    invoke-virtual {p0, v1, v0}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->Fj(Lcom/bytedance/adsdk/lottie/svN/Fj;F)Ljava/lang/Object;

    move-result-object v0

    :goto_0
    iput-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->WR:Ljava/lang/Object;

    return-object v0
.end method
