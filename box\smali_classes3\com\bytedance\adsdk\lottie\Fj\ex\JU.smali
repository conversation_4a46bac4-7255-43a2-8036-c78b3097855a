.class public Lcom/bytedance/adsdk/lottie/Fj/ex/JU;
.super Ljava/lang/Object;


# instance fields
.field private BcC:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/bytedance/adsdk/lottie/Fj/ex/Fj<",
            "Lcom/bytedance/adsdk/lottie/svN/hjc;",
            "Lcom/bytedance/adsdk/lottie/svN/hjc;",
            ">;"
        }
    .end annotation
.end field

.field private final Fj:Landroid/graphics/Matrix;

.field private Ko:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/bytedance/adsdk/lottie/Fj/ex/Fj<",
            "Ljava/lang/Integer;",
            "Ljava/lang/Integer;",
            ">;"
        }
    .end annotation
.end field

.field private Tc:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/bytedance/adsdk/lottie/Fj/ex/Fj<",
            "*",
            "Ljava/lang/Float;",
            ">;"
        }
    .end annotation
.end field

.field private UYd:Lcom/bytedance/adsdk/lottie/Fj/ex/eV;

.field private final Ubf:[F

.field private WR:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/bytedance/adsdk/lottie/Fj/ex/Fj<",
            "Landroid/graphics/PointF;",
            "Landroid/graphics/PointF;",
            ">;"
        }
    .end annotation
.end field

.field private dG:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/bytedance/adsdk/lottie/Fj/ex/Fj<",
            "*",
            "Ljava/lang/Float;",
            ">;"
        }
    .end annotation
.end field

.field private final eV:Landroid/graphics/Matrix;

.field private final ex:Landroid/graphics/Matrix;

.field private final hjc:Landroid/graphics/Matrix;

.field private mSE:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/bytedance/adsdk/lottie/Fj/ex/Fj<",
            "Ljava/lang/Float;",
            "Ljava/lang/Float;",
            ">;"
        }
    .end annotation
.end field

.field private rAx:Lcom/bytedance/adsdk/lottie/Fj/ex/eV;

.field private svN:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/bytedance/adsdk/lottie/Fj/ex/Fj<",
            "*",
            "Landroid/graphics/PointF;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lcom/bytedance/adsdk/lottie/hjc/Fj/UYd;)V
    .locals 2

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    new-instance v0, Landroid/graphics/Matrix;

    invoke-direct {v0}, Landroid/graphics/Matrix;-><init>()V

    iput-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/JU;->Fj:Landroid/graphics/Matrix;

    invoke-virtual {p1}, Lcom/bytedance/adsdk/lottie/hjc/Fj/UYd;->Fj()Lcom/bytedance/adsdk/lottie/hjc/Fj/Ubf;

    move-result-object v0

    const/4 v1, 0x0

    if-nez v0, :cond_0

    move-object v0, v1

    goto :goto_0

    :cond_0
    invoke-virtual {p1}, Lcom/bytedance/adsdk/lottie/hjc/Fj/UYd;->Fj()Lcom/bytedance/adsdk/lottie/hjc/Fj/Ubf;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/hjc/Fj/Ubf;->Fj()Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    move-result-object v0

    :goto_0
    iput-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/JU;->WR:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    invoke-virtual {p1}, Lcom/bytedance/adsdk/lottie/hjc/Fj/UYd;->ex()Lcom/bytedance/adsdk/lottie/hjc/Fj/dG;

    move-result-object v0

    if-nez v0, :cond_1

    move-object v0, v1

    goto :goto_1

    :cond_1
    invoke-virtual {p1}, Lcom/bytedance/adsdk/lottie/hjc/Fj/UYd;->ex()Lcom/bytedance/adsdk/lottie/hjc/Fj/dG;

    move-result-object v0

    invoke-interface {v0}, Lcom/bytedance/adsdk/lottie/hjc/Fj/dG;->Fj()Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    move-result-object v0

    :goto_1
    iput-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/JU;->svN:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    invoke-virtual {p1}, Lcom/bytedance/adsdk/lottie/hjc/Fj/UYd;->hjc()Lcom/bytedance/adsdk/lottie/hjc/Fj/svN;

    move-result-object v0

    if-nez v0, :cond_2

    move-object v0, v1

    goto :goto_2

    :cond_2
    invoke-virtual {p1}, Lcom/bytedance/adsdk/lottie/hjc/Fj/UYd;->hjc()Lcom/bytedance/adsdk/lottie/hjc/Fj/svN;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/hjc/Fj/svN;->Fj()Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    move-result-object v0

    :goto_2
    iput-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/JU;->BcC:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    invoke-virtual {p1}, Lcom/bytedance/adsdk/lottie/hjc/Fj/UYd;->eV()Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

    move-result-object v0

    if-nez v0, :cond_3

    move-object v0, v1

    goto :goto_3

    :cond_3
    invoke-virtual {p1}, Lcom/bytedance/adsdk/lottie/hjc/Fj/UYd;->eV()Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;->Fj()Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    move-result-object v0

    :goto_3
    iput-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/JU;->mSE:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    invoke-virtual {p1}, Lcom/bytedance/adsdk/lottie/hjc/Fj/UYd;->BcC()Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

    move-result-object v0

    if-nez v0, :cond_4

    move-object v0, v1

    goto :goto_4

    :cond_4
    invoke-virtual {p1}, Lcom/bytedance/adsdk/lottie/hjc/Fj/UYd;->BcC()Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;->Fj()Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    move-result-object v0

    check-cast v0, Lcom/bytedance/adsdk/lottie/Fj/ex/eV;

    :goto_4
    iput-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/JU;->rAx:Lcom/bytedance/adsdk/lottie/Fj/ex/eV;

    if-eqz v0, :cond_5

    new-instance v0, Landroid/graphics/Matrix;

    invoke-direct {v0}, Landroid/graphics/Matrix;-><init>()V

    iput-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/JU;->ex:Landroid/graphics/Matrix;

    new-instance v0, Landroid/graphics/Matrix;

    invoke-direct {v0}, Landroid/graphics/Matrix;-><init>()V

    iput-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/JU;->hjc:Landroid/graphics/Matrix;

    new-instance v0, Landroid/graphics/Matrix;

    invoke-direct {v0}, Landroid/graphics/Matrix;-><init>()V

    iput-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/JU;->eV:Landroid/graphics/Matrix;

    const/16 v0, 0x9

    new-array v0, v0, [F

    iput-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/JU;->Ubf:[F

    goto :goto_5

    :cond_5
    iput-object v1, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/JU;->ex:Landroid/graphics/Matrix;

    iput-object v1, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/JU;->hjc:Landroid/graphics/Matrix;

    iput-object v1, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/JU;->eV:Landroid/graphics/Matrix;

    iput-object v1, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/JU;->Ubf:[F

    :goto_5
    invoke-virtual {p1}, Lcom/bytedance/adsdk/lottie/hjc/Fj/UYd;->mSE()Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

    move-result-object v0

    if-nez v0, :cond_6

    move-object v0, v1

    goto :goto_6

    :cond_6
    invoke-virtual {p1}, Lcom/bytedance/adsdk/lottie/hjc/Fj/UYd;->mSE()Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;->Fj()Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    move-result-object v0

    check-cast v0, Lcom/bytedance/adsdk/lottie/Fj/ex/eV;

    :goto_6
    iput-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/JU;->UYd:Lcom/bytedance/adsdk/lottie/Fj/ex/eV;

    invoke-virtual {p1}, Lcom/bytedance/adsdk/lottie/hjc/Fj/UYd;->Ubf()Lcom/bytedance/adsdk/lottie/hjc/Fj/eV;

    move-result-object v0

    if-eqz v0, :cond_7

    invoke-virtual {p1}, Lcom/bytedance/adsdk/lottie/hjc/Fj/UYd;->Ubf()Lcom/bytedance/adsdk/lottie/hjc/Fj/eV;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/hjc/Fj/eV;->Fj()Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/JU;->Ko:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    :cond_7
    invoke-virtual {p1}, Lcom/bytedance/adsdk/lottie/hjc/Fj/UYd;->WR()Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

    move-result-object v0

    if-eqz v0, :cond_8

    invoke-virtual {p1}, Lcom/bytedance/adsdk/lottie/hjc/Fj/UYd;->WR()Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;->Fj()Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/JU;->dG:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    goto :goto_7

    :cond_8
    iput-object v1, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/JU;->dG:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    :goto_7
    invoke-virtual {p1}, Lcom/bytedance/adsdk/lottie/hjc/Fj/UYd;->svN()Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

    move-result-object v0

    if-eqz v0, :cond_9

    invoke-virtual {p1}, Lcom/bytedance/adsdk/lottie/hjc/Fj/UYd;->svN()Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

    move-result-object p1

    invoke-virtual {p1}, Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;->Fj()Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    move-result-object p1

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/JU;->Tc:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    return-void

    :cond_9
    iput-object v1, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/JU;->Tc:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    return-void
.end method

.method private Ubf()V
    .locals 3

    const/4 v0, 0x0

    :goto_0
    const/16 v1, 0x9

    if-ge v0, v1, :cond_0

    iget-object v1, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/JU;->Ubf:[F

    const/4 v2, 0x0

    aput v2, v1, v0

    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :cond_0
    return-void
.end method


# virtual methods
.method public Fj()Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lcom/bytedance/adsdk/lottie/Fj/ex/Fj<",
            "*",
            "Ljava/lang/Integer;",
            ">;"
        }
    .end annotation

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/JU;->Ko:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    return-object v0
.end method

.method public Fj(F)V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/JU;->Ko:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    if-eqz v0, :cond_0

    invoke-virtual {v0, p1}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->Fj(F)V

    :cond_0
    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/JU;->dG:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    if-eqz v0, :cond_1

    invoke-virtual {v0, p1}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->Fj(F)V

    :cond_1
    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/JU;->Tc:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    if-eqz v0, :cond_2

    invoke-virtual {v0, p1}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->Fj(F)V

    :cond_2
    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/JU;->WR:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    if-eqz v0, :cond_3

    invoke-virtual {v0, p1}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->Fj(F)V

    :cond_3
    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/JU;->svN:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    if-eqz v0, :cond_4

    invoke-virtual {v0, p1}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->Fj(F)V

    :cond_4
    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/JU;->BcC:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    if-eqz v0, :cond_5

    invoke-virtual {v0, p1}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->Fj(F)V

    :cond_5
    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/JU;->mSE:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    if-eqz v0, :cond_6

    invoke-virtual {v0, p1}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->Fj(F)V

    :cond_6
    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/JU;->rAx:Lcom/bytedance/adsdk/lottie/Fj/ex/eV;

    if-eqz v0, :cond_7

    invoke-virtual {v0, p1}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->Fj(F)V

    :cond_7
    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/JU;->UYd:Lcom/bytedance/adsdk/lottie/Fj/ex/eV;

    if-eqz v0, :cond_8

    invoke-virtual {v0, p1}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->Fj(F)V

    :cond_8
    return-void
.end method

.method public Fj(Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$Fj;)V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/JU;->Ko:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    if-eqz v0, :cond_0

    invoke-virtual {v0, p1}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->Fj(Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$Fj;)V

    :cond_0
    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/JU;->dG:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    if-eqz v0, :cond_1

    invoke-virtual {v0, p1}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->Fj(Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$Fj;)V

    :cond_1
    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/JU;->Tc:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    if-eqz v0, :cond_2

    invoke-virtual {v0, p1}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->Fj(Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$Fj;)V

    :cond_2
    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/JU;->WR:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    if-eqz v0, :cond_3

    invoke-virtual {v0, p1}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->Fj(Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$Fj;)V

    :cond_3
    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/JU;->svN:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    if-eqz v0, :cond_4

    invoke-virtual {v0, p1}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->Fj(Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$Fj;)V

    :cond_4
    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/JU;->BcC:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    if-eqz v0, :cond_5

    invoke-virtual {v0, p1}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->Fj(Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$Fj;)V

    :cond_5
    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/JU;->mSE:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    if-eqz v0, :cond_6

    invoke-virtual {v0, p1}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->Fj(Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$Fj;)V

    :cond_6
    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/JU;->rAx:Lcom/bytedance/adsdk/lottie/Fj/ex/eV;

    if-eqz v0, :cond_7

    invoke-virtual {v0, p1}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->Fj(Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$Fj;)V

    :cond_7
    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/JU;->UYd:Lcom/bytedance/adsdk/lottie/Fj/ex/eV;

    if-eqz v0, :cond_8

    invoke-virtual {v0, p1}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->Fj(Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$Fj;)V

    :cond_8
    return-void
.end method

.method public Fj(Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;)V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/JU;->Ko:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    invoke-virtual {p1, v0}, Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;->Fj(Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;)V

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/JU;->dG:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    invoke-virtual {p1, v0}, Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;->Fj(Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;)V

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/JU;->Tc:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    invoke-virtual {p1, v0}, Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;->Fj(Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;)V

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/JU;->WR:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    invoke-virtual {p1, v0}, Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;->Fj(Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;)V

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/JU;->svN:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    invoke-virtual {p1, v0}, Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;->Fj(Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;)V

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/JU;->BcC:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    invoke-virtual {p1, v0}, Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;->Fj(Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;)V

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/JU;->mSE:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    invoke-virtual {p1, v0}, Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;->Fj(Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;)V

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/JU;->rAx:Lcom/bytedance/adsdk/lottie/Fj/ex/eV;

    invoke-virtual {p1, v0}, Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;->Fj(Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;)V

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/JU;->UYd:Lcom/bytedance/adsdk/lottie/Fj/ex/eV;

    invoke-virtual {p1, v0}, Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;->Fj(Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;)V

    return-void
.end method

.method public eV()Landroid/graphics/Matrix;
    .locals 13

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/JU;->Fj:Landroid/graphics/Matrix;

    invoke-virtual {v0}, Landroid/graphics/Matrix;->reset()V

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/JU;->svN:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    const/4 v1, 0x0

    if-eqz v0, :cond_1

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->svN()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroid/graphics/PointF;

    if-eqz v0, :cond_1

    iget v2, v0, Landroid/graphics/PointF;->x:F

    cmpl-float v3, v2, v1

    if-nez v3, :cond_0

    iget v3, v0, Landroid/graphics/PointF;->y:F

    cmpl-float v3, v3, v1

    if-eqz v3, :cond_1

    :cond_0
    iget-object v3, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/JU;->Fj:Landroid/graphics/Matrix;

    iget v0, v0, Landroid/graphics/PointF;->y:F

    invoke-virtual {v3, v2, v0}, Landroid/graphics/Matrix;->preTranslate(FF)Z

    :cond_1
    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/JU;->mSE:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    if-eqz v0, :cond_3

    instance-of v2, v0, Lcom/bytedance/adsdk/lottie/Fj/ex/Ql;

    if-eqz v2, :cond_2

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->svN()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/lang/Float;

    invoke-virtual {v0}, Ljava/lang/Float;->floatValue()F

    move-result v0

    goto :goto_0

    :cond_2
    check-cast v0, Lcom/bytedance/adsdk/lottie/Fj/ex/eV;

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/Fj/ex/eV;->mSE()F

    move-result v0

    :goto_0
    cmpl-float v2, v0, v1

    if-eqz v2, :cond_3

    iget-object v2, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/JU;->Fj:Landroid/graphics/Matrix;

    invoke-virtual {v2, v0}, Landroid/graphics/Matrix;->preRotate(F)Z

    :cond_3
    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/JU;->rAx:Lcom/bytedance/adsdk/lottie/Fj/ex/eV;

    const/high16 v2, 0x3f800000    # 1.0f

    if-eqz v0, :cond_6

    iget-object v3, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/JU;->UYd:Lcom/bytedance/adsdk/lottie/Fj/ex/eV;

    const/high16 v4, 0x42b40000    # 90.0f

    if-nez v3, :cond_4

    const/4 v3, 0x0

    goto :goto_1

    :cond_4
    invoke-virtual {v3}, Lcom/bytedance/adsdk/lottie/Fj/ex/eV;->mSE()F

    move-result v3

    neg-float v3, v3

    add-float/2addr v3, v4

    float-to-double v5, v3

    invoke-static {v5, v6}, Ljava/lang/Math;->toRadians(D)D

    move-result-wide v5

    invoke-static {v5, v6}, Ljava/lang/Math;->cos(D)D

    move-result-wide v5

    double-to-float v3, v5

    :goto_1
    iget-object v5, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/JU;->UYd:Lcom/bytedance/adsdk/lottie/Fj/ex/eV;

    if-nez v5, :cond_5

    const/high16 v4, 0x3f800000    # 1.0f

    goto :goto_2

    :cond_5
    invoke-virtual {v5}, Lcom/bytedance/adsdk/lottie/Fj/ex/eV;->mSE()F

    move-result v5

    neg-float v5, v5

    add-float/2addr v5, v4

    float-to-double v4, v5

    invoke-static {v4, v5}, Ljava/lang/Math;->toRadians(D)D

    move-result-wide v4

    invoke-static {v4, v5}, Ljava/lang/Math;->sin(D)D

    move-result-wide v4

    double-to-float v4, v4

    :goto_2
    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/Fj/ex/eV;->mSE()F

    move-result v0

    float-to-double v5, v0

    invoke-static {v5, v6}, Ljava/lang/Math;->toRadians(D)D

    move-result-wide v5

    invoke-static {v5, v6}, Ljava/lang/Math;->tan(D)D

    move-result-wide v5

    double-to-float v0, v5

    invoke-direct {p0}, Lcom/bytedance/adsdk/lottie/Fj/ex/JU;->Ubf()V

    iget-object v5, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/JU;->Ubf:[F

    const/4 v6, 0x0

    aput v3, v5, v6

    const/4 v7, 0x1

    aput v4, v5, v7

    neg-float v8, v4

    const/4 v9, 0x3

    aput v8, v5, v9

    const/4 v10, 0x4

    aput v3, v5, v10

    const/16 v11, 0x8

    aput v2, v5, v11

    iget-object v12, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/JU;->ex:Landroid/graphics/Matrix;

    invoke-virtual {v12, v5}, Landroid/graphics/Matrix;->setValues([F)V

    invoke-direct {p0}, Lcom/bytedance/adsdk/lottie/Fj/ex/JU;->Ubf()V

    iget-object v5, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/JU;->Ubf:[F

    aput v2, v5, v6

    aput v0, v5, v9

    aput v2, v5, v10

    aput v2, v5, v11

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/JU;->hjc:Landroid/graphics/Matrix;

    invoke-virtual {v0, v5}, Landroid/graphics/Matrix;->setValues([F)V

    invoke-direct {p0}, Lcom/bytedance/adsdk/lottie/Fj/ex/JU;->Ubf()V

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/JU;->Ubf:[F

    aput v3, v0, v6

    aput v8, v0, v7

    aput v4, v0, v9

    aput v3, v0, v10

    aput v2, v0, v11

    iget-object v3, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/JU;->eV:Landroid/graphics/Matrix;

    invoke-virtual {v3, v0}, Landroid/graphics/Matrix;->setValues([F)V

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/JU;->hjc:Landroid/graphics/Matrix;

    iget-object v3, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/JU;->ex:Landroid/graphics/Matrix;

    invoke-virtual {v0, v3}, Landroid/graphics/Matrix;->preConcat(Landroid/graphics/Matrix;)Z

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/JU;->eV:Landroid/graphics/Matrix;

    iget-object v3, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/JU;->hjc:Landroid/graphics/Matrix;

    invoke-virtual {v0, v3}, Landroid/graphics/Matrix;->preConcat(Landroid/graphics/Matrix;)Z

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/JU;->Fj:Landroid/graphics/Matrix;

    iget-object v3, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/JU;->eV:Landroid/graphics/Matrix;

    invoke-virtual {v0, v3}, Landroid/graphics/Matrix;->preConcat(Landroid/graphics/Matrix;)Z

    :cond_6
    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/JU;->BcC:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    if-eqz v0, :cond_8

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->svN()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/bytedance/adsdk/lottie/svN/hjc;

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/svN/hjc;->Fj()F

    move-result v3

    cmpl-float v3, v3, v2

    if-nez v3, :cond_7

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/svN/hjc;->ex()F

    move-result v3

    cmpl-float v2, v3, v2

    if-eqz v2, :cond_8

    :cond_7
    iget-object v2, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/JU;->Fj:Landroid/graphics/Matrix;

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/svN/hjc;->Fj()F

    move-result v3

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/svN/hjc;->ex()F

    move-result v0

    invoke-virtual {v2, v3, v0}, Landroid/graphics/Matrix;->preScale(FF)Z

    :cond_8
    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/JU;->WR:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    if-eqz v0, :cond_b

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->svN()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroid/graphics/PointF;

    if-eqz v0, :cond_9

    iget v2, v0, Landroid/graphics/PointF;->x:F

    cmpl-float v2, v2, v1

    if-nez v2, :cond_a

    :cond_9
    iget v2, v0, Landroid/graphics/PointF;->y:F

    cmpl-float v1, v2, v1

    if-eqz v1, :cond_b

    :cond_a
    iget-object v1, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/JU;->Fj:Landroid/graphics/Matrix;

    iget v2, v0, Landroid/graphics/PointF;->x:F

    neg-float v2, v2

    iget v0, v0, Landroid/graphics/PointF;->y:F

    neg-float v0, v0

    invoke-virtual {v1, v2, v0}, Landroid/graphics/Matrix;->preTranslate(FF)Z

    :cond_b
    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/JU;->Fj:Landroid/graphics/Matrix;

    return-object v0
.end method

.method public ex(F)Landroid/graphics/Matrix;
    .locals 9

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/JU;->svN:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    const/4 v1, 0x0

    if-nez v0, :cond_0

    move-object v0, v1

    goto :goto_0

    :cond_0
    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->svN()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroid/graphics/PointF;

    :goto_0
    iget-object v2, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/JU;->BcC:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    if-nez v2, :cond_1

    move-object v2, v1

    goto :goto_1

    :cond_1
    invoke-virtual {v2}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->svN()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/bytedance/adsdk/lottie/svN/hjc;

    :goto_1
    iget-object v3, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/JU;->Fj:Landroid/graphics/Matrix;

    invoke-virtual {v3}, Landroid/graphics/Matrix;->reset()V

    if-eqz v0, :cond_2

    iget-object v3, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/JU;->Fj:Landroid/graphics/Matrix;

    iget v4, v0, Landroid/graphics/PointF;->x:F

    mul-float v4, v4, p1

    iget v0, v0, Landroid/graphics/PointF;->y:F

    mul-float v0, v0, p1

    invoke-virtual {v3, v4, v0}, Landroid/graphics/Matrix;->preTranslate(FF)Z

    :cond_2
    if-eqz v2, :cond_3

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/JU;->Fj:Landroid/graphics/Matrix;

    invoke-virtual {v2}, Lcom/bytedance/adsdk/lottie/svN/hjc;->Fj()F

    move-result v3

    float-to-double v3, v3

    float-to-double v5, p1

    invoke-static {v3, v4, v5, v6}, Ljava/lang/Math;->pow(DD)D

    move-result-wide v3

    double-to-float v3, v3

    invoke-virtual {v2}, Lcom/bytedance/adsdk/lottie/svN/hjc;->ex()F

    move-result v2

    float-to-double v7, v2

    invoke-static {v7, v8, v5, v6}, Ljava/lang/Math;->pow(DD)D

    move-result-wide v4

    double-to-float v2, v4

    invoke-virtual {v0, v3, v2}, Landroid/graphics/Matrix;->preScale(FF)Z

    :cond_3
    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/JU;->mSE:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    if-eqz v0, :cond_7

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->svN()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/lang/Float;

    invoke-virtual {v0}, Ljava/lang/Float;->floatValue()F

    move-result v0

    iget-object v2, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/JU;->WR:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    if-nez v2, :cond_4

    goto :goto_2

    :cond_4
    invoke-virtual {v2}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->svN()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Landroid/graphics/PointF;

    :goto_2
    iget-object v2, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/JU;->Fj:Landroid/graphics/Matrix;

    mul-float v0, v0, p1

    const/4 p1, 0x0

    if-nez v1, :cond_5

    const/4 v3, 0x0

    goto :goto_3

    :cond_5
    iget v3, v1, Landroid/graphics/PointF;->x:F

    :goto_3
    if-nez v1, :cond_6

    goto :goto_4

    :cond_6
    iget p1, v1, Landroid/graphics/PointF;->y:F

    :goto_4
    invoke-virtual {v2, v0, v3, p1}, Landroid/graphics/Matrix;->preRotate(FFF)Z

    :cond_7
    iget-object p1, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/JU;->Fj:Landroid/graphics/Matrix;

    return-object p1
.end method

.method public ex()Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lcom/bytedance/adsdk/lottie/Fj/ex/Fj<",
            "*",
            "Ljava/lang/Float;",
            ">;"
        }
    .end annotation

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/JU;->dG:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    return-object v0
.end method

.method public hjc()Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lcom/bytedance/adsdk/lottie/Fj/ex/Fj<",
            "*",
            "Ljava/lang/Float;",
            ">;"
        }
    .end annotation

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/JU;->Tc:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    return-object v0
.end method
