.class public Lcom/bytedance/adsdk/lottie/Fj/ex/Ko;
.super Lcom/bytedance/adsdk/lottie/Fj/ex/svN;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lcom/bytedance/adsdk/lottie/Fj/ex/svN<",
        "Landroid/graphics/PointF;",
        ">;"
    }
.end annotation


# instance fields
.field private final Ubf:[F

.field private final WR:Landroid/graphics/PathMeasure;

.field private final eV:Landroid/graphics/PointF;

.field private svN:Lcom/bytedance/adsdk/lottie/Fj/ex/mSE;


# direct methods
.method public constructor <init>(Ljava/util/List;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "+",
            "Lcom/bytedance/adsdk/lottie/svN/Fj<",
            "Landroid/graphics/PointF;",
            ">;>;)V"
        }
    .end annotation

    invoke-direct {p0, p1}, Lcom/bytedance/adsdk/lottie/Fj/ex/svN;-><init>(Ljava/util/List;)V

    new-instance p1, Landroid/graphics/PointF;

    invoke-direct {p1}, Landroid/graphics/PointF;-><init>()V

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Ko;->eV:Landroid/graphics/PointF;

    const/4 p1, 0x2

    new-array p1, p1, [F

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Ko;->Ubf:[F

    new-instance p1, Landroid/graphics/PathMeasure;

    invoke-direct {p1}, Landroid/graphics/PathMeasure;-><init>()V

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Ko;->WR:Landroid/graphics/PathMeasure;

    return-void
.end method


# virtual methods
.method public synthetic Fj(Lcom/bytedance/adsdk/lottie/svN/Fj;F)Ljava/lang/Object;
    .locals 0

    invoke-virtual {p0, p1, p2}, Lcom/bytedance/adsdk/lottie/Fj/ex/Ko;->ex(Lcom/bytedance/adsdk/lottie/svN/Fj;F)Landroid/graphics/PointF;

    move-result-object p1

    return-object p1
.end method

.method public ex(Lcom/bytedance/adsdk/lottie/svN/Fj;F)Landroid/graphics/PointF;
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/bytedance/adsdk/lottie/svN/Fj<",
            "Landroid/graphics/PointF;",
            ">;F)",
            "Landroid/graphics/PointF;"
        }
    .end annotation

    move-object v0, p1

    check-cast v0, Lcom/bytedance/adsdk/lottie/Fj/ex/mSE;

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/Fj/ex/mSE;->ex()Landroid/graphics/Path;

    move-result-object v1

    if-nez v1, :cond_0

    iget-object p1, p1, Lcom/bytedance/adsdk/lottie/svN/Fj;->Fj:Ljava/lang/Object;

    check-cast p1, Landroid/graphics/PointF;

    return-object p1

    :cond_0
    iget-object p1, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->hjc:Lcom/bytedance/adsdk/lottie/svN/ex;

    const/4 v2, 0x0

    if-nez p1, :cond_2

    iget-object p1, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Ko;->svN:Lcom/bytedance/adsdk/lottie/Fj/ex/mSE;

    const/4 v3, 0x0

    if-eq p1, v0, :cond_1

    iget-object p1, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Ko;->WR:Landroid/graphics/PathMeasure;

    invoke-virtual {p1, v1, v3}, Landroid/graphics/PathMeasure;->setPath(Landroid/graphics/Path;Z)V

    iput-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Ko;->svN:Lcom/bytedance/adsdk/lottie/Fj/ex/mSE;

    :cond_1
    iget-object p1, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Ko;->WR:Landroid/graphics/PathMeasure;

    invoke-virtual {p1}, Landroid/graphics/PathMeasure;->getLength()F

    move-result v0

    mul-float p2, p2, v0

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Ko;->Ubf:[F

    invoke-virtual {p1, p2, v0, v2}, Landroid/graphics/PathMeasure;->getPosTan(F[F[F)Z

    iget-object p1, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Ko;->eV:Landroid/graphics/PointF;

    iget-object p2, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Ko;->Ubf:[F

    aget v0, p2, v3

    const/4 v1, 0x1

    aget p2, p2, v1

    invoke-virtual {p1, v0, p2}, Landroid/graphics/PointF;->set(FF)V

    iget-object p1, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Ko;->eV:Landroid/graphics/PointF;

    return-object p1

    :cond_2
    iget-object p1, v0, Lcom/bytedance/adsdk/lottie/svN/Fj;->svN:Ljava/lang/Float;

    invoke-virtual {p1}, Ljava/lang/Float;->floatValue()F

    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->eV()F

    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->BcC()F

    throw v2
.end method
