.class public Lcom/bytedance/adsdk/lottie/Fj/ex/Ql;
.super Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "<K:",
        "Ljava/lang/Object;",
        "A:",
        "Ljava/lang/Object;",
        ">",
        "Lcom/bytedance/adsdk/lottie/Fj/ex/Fj<",
        "TK;TA;>;"
    }
.end annotation


# virtual methods
.method public Fj(Lcom/bytedance/adsdk/lottie/svN/Fj;F)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/bytedance/adsdk/lottie/svN/Fj<",
            "TK;>;F)TA;"
        }
    .end annotation

    const/4 p1, 0x0

    throw p1
.end method

.method public Fj(F)V
    .locals 0

    iput p1, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->ex:F

    return-void
.end method

.method public WR()F
    .locals 1

    const/high16 v0, 0x3f800000    # 1.0f

    return v0
.end method

.method public ex()V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->hjc:Lcom/bytedance/adsdk/lottie/svN/ex;

    if-eqz v0, :cond_0

    invoke-super {p0}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->ex()V

    :cond_0
    return-void
.end method

.method public svN()Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()TA;"
        }
    .end annotation

    const/4 v0, 0x0

    throw v0
.end method
