.class public Lcom/bytedance/adsdk/lottie/Fj/ex/Tc;
.super Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lcom/bytedance/adsdk/lottie/Fj/ex/Fj<",
        "Landroid/graphics/PointF;",
        "Landroid/graphics/PointF;",
        ">;"
    }
.end annotation


# instance fields
.field private final BcC:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/bytedance/adsdk/lottie/Fj/ex/Fj<",
            "Ljava/lang/Float;",
            "Ljava/lang/Float;",
            ">;"
        }
    .end annotation
.end field

.field protected Ubf:Lcom/bytedance/adsdk/lottie/svN/ex;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/bytedance/adsdk/lottie/svN/ex<",
            "Ljava/lang/Float;",
            ">;"
        }
    .end annotation
.end field

.field private final WR:Landroid/graphics/PointF;

.field protected eV:Lcom/bytedance/adsdk/lottie/svN/ex;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/bytedance/adsdk/lottie/svN/ex<",
            "Ljava/lang/Float;",
            ">;"
        }
    .end annotation
.end field

.field private final mSE:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/bytedance/adsdk/lottie/Fj/ex/Fj<",
            "Ljava/lang/Float;",
            "Ljava/lang/Float;",
            ">;"
        }
    .end annotation
.end field

.field private final svN:Landroid/graphics/PointF;


# direct methods
.method public constructor <init>(Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/bytedance/adsdk/lottie/Fj/ex/Fj<",
            "Ljava/lang/Float;",
            "Ljava/lang/Float;",
            ">;",
            "Lcom/bytedance/adsdk/lottie/Fj/ex/Fj<",
            "Ljava/lang/Float;",
            "Ljava/lang/Float;",
            ">;)V"
        }
    .end annotation

    invoke-static {}, Ljava/util/Collections;->emptyList()Ljava/util/List;

    move-result-object v0

    invoke-direct {p0, v0}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;-><init>(Ljava/util/List;)V

    new-instance v0, Landroid/graphics/PointF;

    invoke-direct {v0}, Landroid/graphics/PointF;-><init>()V

    iput-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Tc;->WR:Landroid/graphics/PointF;

    new-instance v0, Landroid/graphics/PointF;

    invoke-direct {v0}, Landroid/graphics/PointF;-><init>()V

    iput-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Tc;->svN:Landroid/graphics/PointF;

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Tc;->BcC:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    iput-object p2, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Tc;->mSE:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->BcC()F

    move-result p1

    invoke-virtual {p0, p1}, Lcom/bytedance/adsdk/lottie/Fj/ex/Tc;->Fj(F)V

    return-void
.end method


# virtual methods
.method public synthetic Fj(Lcom/bytedance/adsdk/lottie/svN/Fj;F)Ljava/lang/Object;
    .locals 0

    invoke-virtual {p0, p1, p2}, Lcom/bytedance/adsdk/lottie/Fj/ex/Tc;->ex(Lcom/bytedance/adsdk/lottie/svN/Fj;F)Landroid/graphics/PointF;

    move-result-object p1

    return-object p1
.end method

.method public Fj(F)V
    .locals 2

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Tc;->BcC:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    invoke-virtual {v0, p1}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->Fj(F)V

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Tc;->mSE:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    invoke-virtual {v0, p1}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->Fj(F)V

    iget-object p1, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Tc;->WR:Landroid/graphics/PointF;

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Tc;->BcC:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->svN()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/lang/Float;

    invoke-virtual {v0}, Ljava/lang/Float;->floatValue()F

    move-result v0

    iget-object v1, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Tc;->mSE:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    invoke-virtual {v1}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->svN()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/lang/Float;

    invoke-virtual {v1}, Ljava/lang/Float;->floatValue()F

    move-result v1

    invoke-virtual {p1, v0, v1}, Landroid/graphics/PointF;->set(FF)V

    const/4 p1, 0x0

    :goto_0
    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->Fj:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    if-ge p1, v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->Fj:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$Fj;

    invoke-interface {v0}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$Fj;->Fj()V

    add-int/lit8 p1, p1, 0x1

    goto :goto_0

    :cond_0
    return-void
.end method

.method public ex(Lcom/bytedance/adsdk/lottie/svN/Fj;F)Landroid/graphics/PointF;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/bytedance/adsdk/lottie/svN/Fj<",
            "Landroid/graphics/PointF;",
            ">;F)",
            "Landroid/graphics/PointF;"
        }
    .end annotation

    iget-object p1, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Tc;->eV:Lcom/bytedance/adsdk/lottie/svN/ex;

    const/4 p2, 0x0

    if-eqz p1, :cond_1

    iget-object p1, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Tc;->BcC:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    invoke-virtual {p1}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->hjc()Lcom/bytedance/adsdk/lottie/svN/Fj;

    move-result-object p1

    if-eqz p1, :cond_1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Tc;->BcC:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->Ubf()F

    iget-object p1, p1, Lcom/bytedance/adsdk/lottie/svN/Fj;->svN:Ljava/lang/Float;

    if-eqz p1, :cond_0

    invoke-virtual {p1}, Ljava/lang/Float;->floatValue()F

    :cond_0
    throw p2

    :cond_1
    iget-object p1, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Tc;->Ubf:Lcom/bytedance/adsdk/lottie/svN/ex;

    if-eqz p1, :cond_3

    iget-object p1, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Tc;->mSE:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    invoke-virtual {p1}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->hjc()Lcom/bytedance/adsdk/lottie/svN/Fj;

    move-result-object p1

    if-eqz p1, :cond_3

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Tc;->mSE:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->Ubf()F

    iget-object p1, p1, Lcom/bytedance/adsdk/lottie/svN/Fj;->svN:Ljava/lang/Float;

    if-eqz p1, :cond_2

    invoke-virtual {p1}, Ljava/lang/Float;->floatValue()F

    :cond_2
    throw p2

    :cond_3
    iget-object p1, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Tc;->svN:Landroid/graphics/PointF;

    iget-object p2, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Tc;->WR:Landroid/graphics/PointF;

    iget p2, p2, Landroid/graphics/PointF;->x:F

    const/4 v0, 0x0

    invoke-virtual {p1, p2, v0}, Landroid/graphics/PointF;->set(FF)V

    iget-object p1, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Tc;->svN:Landroid/graphics/PointF;

    iget p2, p1, Landroid/graphics/PointF;->x:F

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Tc;->WR:Landroid/graphics/PointF;

    iget v0, v0, Landroid/graphics/PointF;->y:F

    invoke-virtual {p1, p2, v0}, Landroid/graphics/PointF;->set(FF)V

    iget-object p1, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Tc;->svN:Landroid/graphics/PointF;

    return-object p1
.end method

.method public mSE()Landroid/graphics/PointF;
    .locals 2

    const/4 v0, 0x0

    const/4 v1, 0x0

    invoke-virtual {p0, v0, v1}, Lcom/bytedance/adsdk/lottie/Fj/ex/Tc;->ex(Lcom/bytedance/adsdk/lottie/svN/Fj;F)Landroid/graphics/PointF;

    move-result-object v0

    return-object v0
.end method

.method public synthetic svN()Ljava/lang/Object;
    .locals 1

    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/Fj/ex/Tc;->mSE()Landroid/graphics/PointF;

    move-result-object v0

    return-object v0
.end method
