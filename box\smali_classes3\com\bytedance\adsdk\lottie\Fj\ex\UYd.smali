.class public Lcom/bytedance/adsdk/lottie/Fj/ex/UYd;
.super Lcom/bytedance/adsdk/lottie/Fj/ex/svN;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lcom/bytedance/adsdk/lottie/Fj/ex/svN<",
        "Lcom/bytedance/adsdk/lottie/svN/hjc;",
        ">;"
    }
.end annotation


# instance fields
.field private final eV:Lcom/bytedance/adsdk/lottie/svN/hjc;


# direct methods
.method public constructor <init>(Ljava/util/List;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lcom/bytedance/adsdk/lottie/svN/Fj<",
            "Lcom/bytedance/adsdk/lottie/svN/hjc;",
            ">;>;)V"
        }
    .end annotation

    invoke-direct {p0, p1}, Lcom/bytedance/adsdk/lottie/Fj/ex/svN;-><init>(Ljava/util/List;)V

    new-instance p1, Lcom/bytedance/adsdk/lottie/svN/hjc;

    invoke-direct {p1}, Lcom/bytedance/adsdk/lottie/svN/hjc;-><init>()V

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/UYd;->eV:Lcom/bytedance/adsdk/lottie/svN/hjc;

    return-void
.end method


# virtual methods
.method public synthetic Fj(Lcom/bytedance/adsdk/lottie/svN/Fj;F)Ljava/lang/Object;
    .locals 0

    invoke-virtual {p0, p1, p2}, Lcom/bytedance/adsdk/lottie/Fj/ex/UYd;->ex(Lcom/bytedance/adsdk/lottie/svN/Fj;F)Lcom/bytedance/adsdk/lottie/svN/hjc;

    move-result-object p1

    return-object p1
.end method

.method public ex(Lcom/bytedance/adsdk/lottie/svN/Fj;F)Lcom/bytedance/adsdk/lottie/svN/hjc;
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/bytedance/adsdk/lottie/svN/Fj<",
            "Lcom/bytedance/adsdk/lottie/svN/hjc;",
            ">;F)",
            "Lcom/bytedance/adsdk/lottie/svN/hjc;"
        }
    .end annotation

    iget-object v0, p1, Lcom/bytedance/adsdk/lottie/svN/Fj;->Fj:Ljava/lang/Object;

    if-eqz v0, :cond_1

    iget-object v1, p1, Lcom/bytedance/adsdk/lottie/svN/Fj;->ex:Ljava/lang/Object;

    if-eqz v1, :cond_1

    check-cast v0, Lcom/bytedance/adsdk/lottie/svN/hjc;

    check-cast v1, Lcom/bytedance/adsdk/lottie/svN/hjc;

    iget-object v2, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->hjc:Lcom/bytedance/adsdk/lottie/svN/ex;

    if-nez v2, :cond_0

    iget-object p1, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/UYd;->eV:Lcom/bytedance/adsdk/lottie/svN/hjc;

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/svN/hjc;->Fj()F

    move-result v2

    invoke-virtual {v1}, Lcom/bytedance/adsdk/lottie/svN/hjc;->Fj()F

    move-result v3

    invoke-static {v2, v3, p2}, Lcom/bytedance/adsdk/lottie/WR/Ubf;->Fj(FFF)F

    move-result v2

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/svN/hjc;->ex()F

    move-result v0

    invoke-virtual {v1}, Lcom/bytedance/adsdk/lottie/svN/hjc;->ex()F

    move-result v1

    invoke-static {v0, v1, p2}, Lcom/bytedance/adsdk/lottie/WR/Ubf;->Fj(FFF)F

    move-result p2

    invoke-virtual {p1, v2, p2}, Lcom/bytedance/adsdk/lottie/svN/hjc;->Fj(FF)V

    iget-object p1, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/UYd;->eV:Lcom/bytedance/adsdk/lottie/svN/hjc;

    return-object p1

    :cond_0
    iget-object p1, p1, Lcom/bytedance/adsdk/lottie/svN/Fj;->svN:Ljava/lang/Float;

    invoke-virtual {p1}, Ljava/lang/Float;->floatValue()F

    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->eV()F

    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->BcC()F

    const/4 p1, 0x0

    throw p1

    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    const-string p2, "Missing values for keyframe."

    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw p1
.end method
