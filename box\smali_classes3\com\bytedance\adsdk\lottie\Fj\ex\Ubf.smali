.class public Lcom/bytedance/adsdk/lottie/Fj/ex/Ubf;
.super Lcom/bytedance/adsdk/lottie/Fj/ex/svN;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lcom/bytedance/adsdk/lottie/Fj/ex/svN<",
        "Lcom/bytedance/adsdk/lottie/hjc/ex/eV;",
        ">;"
    }
.end annotation


# instance fields
.field private final eV:Lcom/bytedance/adsdk/lottie/hjc/ex/eV;


# direct methods
.method public constructor <init>(Ljava/util/List;)V
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lcom/bytedance/adsdk/lottie/svN/Fj<",
            "Lcom/bytedance/adsdk/lottie/hjc/ex/eV;",
            ">;>;)V"
        }
    .end annotation

    invoke-direct {p0, p1}, Lcom/bytedance/adsdk/lottie/Fj/ex/svN;-><init>(Ljava/util/List;)V

    const/4 v0, 0x0

    invoke-interface {p1, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/bytedance/adsdk/lottie/svN/Fj;

    iget-object p1, p1, Lcom/bytedance/adsdk/lottie/svN/Fj;->Fj:Ljava/lang/Object;

    check-cast p1, Lcom/bytedance/adsdk/lottie/hjc/ex/eV;

    if-nez p1, :cond_0

    goto :goto_0

    :cond_0
    invoke-virtual {p1}, Lcom/bytedance/adsdk/lottie/hjc/ex/eV;->hjc()I

    move-result v0

    :goto_0
    new-instance p1, Lcom/bytedance/adsdk/lottie/hjc/ex/eV;

    new-array v1, v0, [F

    new-array v0, v0, [I

    invoke-direct {p1, v1, v0}, Lcom/bytedance/adsdk/lottie/hjc/ex/eV;-><init>([F[I)V

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Ubf;->eV:Lcom/bytedance/adsdk/lottie/hjc/ex/eV;

    return-void
.end method


# virtual methods
.method public synthetic Fj(Lcom/bytedance/adsdk/lottie/svN/Fj;F)Ljava/lang/Object;
    .locals 0

    invoke-virtual {p0, p1, p2}, Lcom/bytedance/adsdk/lottie/Fj/ex/Ubf;->ex(Lcom/bytedance/adsdk/lottie/svN/Fj;F)Lcom/bytedance/adsdk/lottie/hjc/ex/eV;

    move-result-object p1

    return-object p1
.end method

.method public ex(Lcom/bytedance/adsdk/lottie/svN/Fj;F)Lcom/bytedance/adsdk/lottie/hjc/ex/eV;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/bytedance/adsdk/lottie/svN/Fj<",
            "Lcom/bytedance/adsdk/lottie/hjc/ex/eV;",
            ">;F)",
            "Lcom/bytedance/adsdk/lottie/hjc/ex/eV;"
        }
    .end annotation

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Ubf;->eV:Lcom/bytedance/adsdk/lottie/hjc/ex/eV;

    iget-object v1, p1, Lcom/bytedance/adsdk/lottie/svN/Fj;->Fj:Ljava/lang/Object;

    check-cast v1, Lcom/bytedance/adsdk/lottie/hjc/ex/eV;

    iget-object p1, p1, Lcom/bytedance/adsdk/lottie/svN/Fj;->ex:Ljava/lang/Object;

    check-cast p1, Lcom/bytedance/adsdk/lottie/hjc/ex/eV;

    invoke-virtual {v0, v1, p1, p2}, Lcom/bytedance/adsdk/lottie/hjc/ex/eV;->Fj(Lcom/bytedance/adsdk/lottie/hjc/ex/eV;Lcom/bytedance/adsdk/lottie/hjc/ex/eV;F)V

    iget-object p1, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Ubf;->eV:Lcom/bytedance/adsdk/lottie/hjc/ex/eV;

    return-object p1
.end method
