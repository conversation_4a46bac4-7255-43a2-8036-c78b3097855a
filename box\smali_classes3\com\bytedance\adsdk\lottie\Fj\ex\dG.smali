.class public Lcom/bytedance/adsdk/lottie/Fj/ex/dG;
.super Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lcom/bytedance/adsdk/lottie/Fj/ex/Fj<",
        "Lcom/bytedance/adsdk/lottie/hjc/ex/Tc;",
        "Landroid/graphics/Path;",
        ">;"
    }
.end annotation


# instance fields
.field private final Ubf:Landroid/graphics/Path;

.field private WR:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lcom/bytedance/adsdk/lottie/Fj/Fj/vYf;",
            ">;"
        }
    .end annotation
.end field

.field private final eV:Lcom/bytedance/adsdk/lottie/hjc/ex/Tc;


# direct methods
.method public constructor <init>(Ljava/util/List;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lcom/bytedance/adsdk/lottie/svN/Fj<",
            "Lcom/bytedance/adsdk/lottie/hjc/ex/Tc;",
            ">;>;)V"
        }
    .end annotation

    invoke-direct {p0, p1}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;-><init>(Ljava/util/List;)V

    new-instance p1, Lcom/bytedance/adsdk/lottie/hjc/ex/Tc;

    invoke-direct {p1}, Lcom/bytedance/adsdk/lottie/hjc/ex/Tc;-><init>()V

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/dG;->eV:Lcom/bytedance/adsdk/lottie/hjc/ex/Tc;

    new-instance p1, Landroid/graphics/Path;

    invoke-direct {p1}, Landroid/graphics/Path;-><init>()V

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/dG;->Ubf:Landroid/graphics/Path;

    return-void
.end method


# virtual methods
.method public synthetic Fj(Lcom/bytedance/adsdk/lottie/svN/Fj;F)Ljava/lang/Object;
    .locals 0

    invoke-virtual {p0, p1, p2}, Lcom/bytedance/adsdk/lottie/Fj/ex/dG;->ex(Lcom/bytedance/adsdk/lottie/svN/Fj;F)Landroid/graphics/Path;

    move-result-object p1

    return-object p1
.end method

.method public Fj(Ljava/util/List;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lcom/bytedance/adsdk/lottie/Fj/Fj/vYf;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/dG;->WR:Ljava/util/List;

    return-void
.end method

.method public ex(Lcom/bytedance/adsdk/lottie/svN/Fj;F)Landroid/graphics/Path;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/bytedance/adsdk/lottie/svN/Fj<",
            "Lcom/bytedance/adsdk/lottie/hjc/ex/Tc;",
            ">;F)",
            "Landroid/graphics/Path;"
        }
    .end annotation

    iget-object v0, p1, Lcom/bytedance/adsdk/lottie/svN/Fj;->Fj:Ljava/lang/Object;

    check-cast v0, Lcom/bytedance/adsdk/lottie/hjc/ex/Tc;

    iget-object p1, p1, Lcom/bytedance/adsdk/lottie/svN/Fj;->ex:Ljava/lang/Object;

    check-cast p1, Lcom/bytedance/adsdk/lottie/hjc/ex/Tc;

    iget-object v1, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/dG;->eV:Lcom/bytedance/adsdk/lottie/hjc/ex/Tc;

    invoke-virtual {v1, v0, p1, p2}, Lcom/bytedance/adsdk/lottie/hjc/ex/Tc;->Fj(Lcom/bytedance/adsdk/lottie/hjc/ex/Tc;Lcom/bytedance/adsdk/lottie/hjc/ex/Tc;F)V

    iget-object p1, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/dG;->eV:Lcom/bytedance/adsdk/lottie/hjc/ex/Tc;

    iget-object p2, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/dG;->WR:Ljava/util/List;

    if-eqz p2, :cond_0

    invoke-interface {p2}, Ljava/util/List;->size()I

    move-result p2

    add-int/lit8 p2, p2, -0x1

    :goto_0
    if-ltz p2, :cond_0

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/dG;->WR:Ljava/util/List;

    invoke-interface {v0, p2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/bytedance/adsdk/lottie/Fj/Fj/vYf;

    invoke-interface {v0, p1}, Lcom/bytedance/adsdk/lottie/Fj/Fj/vYf;->Fj(Lcom/bytedance/adsdk/lottie/hjc/ex/Tc;)Lcom/bytedance/adsdk/lottie/hjc/ex/Tc;

    move-result-object p1

    add-int/lit8 p2, p2, -0x1

    goto :goto_0

    :cond_0
    iget-object p2, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/dG;->Ubf:Landroid/graphics/Path;

    invoke-static {p1, p2}, Lcom/bytedance/adsdk/lottie/WR/Ubf;->Fj(Lcom/bytedance/adsdk/lottie/hjc/ex/Tc;Landroid/graphics/Path;)V

    iget-object p1, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/dG;->Ubf:Landroid/graphics/Path;

    return-object p1
.end method
