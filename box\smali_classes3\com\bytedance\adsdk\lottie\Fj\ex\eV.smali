.class public Lcom/bytedance/adsdk/lottie/Fj/ex/eV;
.super Lcom/bytedance/adsdk/lottie/Fj/ex/svN;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lcom/bytedance/adsdk/lottie/Fj/ex/svN<",
        "Ljava/lang/Float;",
        ">;"
    }
.end annotation


# direct methods
.method public constructor <init>(Ljava/util/List;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lcom/bytedance/adsdk/lottie/svN/Fj<",
            "Ljava/lang/Float;",
            ">;>;)V"
        }
    .end annotation

    invoke-direct {p0, p1}, Lcom/bytedance/adsdk/lottie/Fj/ex/svN;-><init>(Ljava/util/List;)V

    return-void
.end method


# virtual methods
.method public synthetic Fj(Lcom/bytedance/adsdk/lottie/svN/Fj;F)Ljava/lang/Object;
    .locals 0

    invoke-virtual {p0, p1, p2}, Lcom/bytedance/adsdk/lottie/Fj/ex/eV;->ex(Lcom/bytedance/adsdk/lottie/svN/Fj;F)Ljava/lang/Float;

    move-result-object p1

    return-object p1
.end method

.method public ex(Lcom/bytedance/adsdk/lottie/svN/Fj;F)Ljava/lang/Float;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/bytedance/adsdk/lottie/svN/Fj<",
            "Ljava/lang/Float;",
            ">;F)",
            "Ljava/lang/Float;"
        }
    .end annotation

    invoke-virtual {p0, p1, p2}, Lcom/bytedance/adsdk/lottie/Fj/ex/eV;->hjc(Lcom/bytedance/adsdk/lottie/svN/Fj;F)F

    move-result p1

    invoke-static {p1}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    move-result-object p1

    return-object p1
.end method

.method public hjc(Lcom/bytedance/adsdk/lottie/svN/Fj;F)F
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/bytedance/adsdk/lottie/svN/Fj<",
            "Ljava/lang/Float;",
            ">;F)F"
        }
    .end annotation

    iget-object v0, p1, Lcom/bytedance/adsdk/lottie/svN/Fj;->Fj:Ljava/lang/Object;

    if-eqz v0, :cond_1

    iget-object v0, p1, Lcom/bytedance/adsdk/lottie/svN/Fj;->ex:Ljava/lang/Object;

    if-eqz v0, :cond_1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->hjc:Lcom/bytedance/adsdk/lottie/svN/ex;

    if-nez v0, :cond_0

    invoke-virtual {p1}, Lcom/bytedance/adsdk/lottie/svN/Fj;->WR()F

    move-result v0

    invoke-virtual {p1}, Lcom/bytedance/adsdk/lottie/svN/Fj;->svN()F

    move-result p1

    invoke-static {v0, p1, p2}, Lcom/bytedance/adsdk/lottie/WR/Ubf;->Fj(FFF)F

    move-result p1

    return p1

    :cond_0
    iget-object p1, p1, Lcom/bytedance/adsdk/lottie/svN/Fj;->svN:Ljava/lang/Float;

    invoke-virtual {p1}, Ljava/lang/Float;->floatValue()F

    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->eV()F

    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->BcC()F

    const/4 p1, 0x0

    throw p1

    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    const-string p2, "Missing values for keyframe."

    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public mSE()F
    .locals 2

    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->hjc()Lcom/bytedance/adsdk/lottie/svN/Fj;

    move-result-object v0

    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->Ubf()F

    move-result v1

    invoke-virtual {p0, v0, v1}, Lcom/bytedance/adsdk/lottie/Fj/ex/eV;->hjc(Lcom/bytedance/adsdk/lottie/svN/Fj;F)F

    move-result v0

    return v0
.end method
