.class public Lcom/bytedance/adsdk/lottie/Fj/ex/hjc;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$Fj;


# instance fields
.field private final Fj:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$Fj;

.field private final Ubf:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/bytedance/adsdk/lottie/Fj/ex/Fj<",
            "Ljava/lang/Float;",
            "Ljava/lang/Float;",
            ">;"
        }
    .end annotation
.end field

.field private final WR:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/bytedance/adsdk/lottie/Fj/ex/Fj<",
            "Ljava/lang/Float;",
            "Ljava/lang/Float;",
            ">;"
        }
    .end annotation
.end field

.field private final eV:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/bytedance/adsdk/lottie/Fj/ex/Fj<",
            "Ljava/lang/Float;",
            "Ljava/lang/Float;",
            ">;"
        }
    .end annotation
.end field

.field private final ex:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/bytedance/adsdk/lottie/Fj/ex/Fj<",
            "Ljava/lang/Integer;",
            "Ljava/lang/Integer;",
            ">;"
        }
    .end annotation
.end field

.field private final hjc:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/bytedance/adsdk/lottie/Fj/ex/Fj<",
            "Ljava/lang/Float;",
            "Ljava/lang/Float;",
            ">;"
        }
    .end annotation
.end field

.field private svN:Z


# direct methods
.method public constructor <init>(Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$Fj;Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;Lcom/bytedance/adsdk/lottie/Ubf/Ko;)V
    .locals 1

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, 0x1

    iput-boolean v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/hjc;->svN:Z

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/hjc;->Fj:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$Fj;

    invoke-virtual {p3}, Lcom/bytedance/adsdk/lottie/Ubf/Ko;->Fj()Lcom/bytedance/adsdk/lottie/hjc/Fj/Fj;

    move-result-object p1

    invoke-virtual {p1}, Lcom/bytedance/adsdk/lottie/hjc/Fj/Fj;->Fj()Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    move-result-object p1

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/hjc;->ex:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    invoke-virtual {p1, p0}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->Fj(Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$Fj;)V

    invoke-virtual {p2, p1}, Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;->Fj(Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;)V

    invoke-virtual {p3}, Lcom/bytedance/adsdk/lottie/Ubf/Ko;->ex()Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

    move-result-object p1

    invoke-virtual {p1}, Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;->Fj()Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    move-result-object p1

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/hjc;->hjc:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    invoke-virtual {p1, p0}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->Fj(Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$Fj;)V

    invoke-virtual {p2, p1}, Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;->Fj(Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;)V

    invoke-virtual {p3}, Lcom/bytedance/adsdk/lottie/Ubf/Ko;->hjc()Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

    move-result-object p1

    invoke-virtual {p1}, Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;->Fj()Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    move-result-object p1

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/hjc;->eV:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    invoke-virtual {p1, p0}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->Fj(Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$Fj;)V

    invoke-virtual {p2, p1}, Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;->Fj(Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;)V

    invoke-virtual {p3}, Lcom/bytedance/adsdk/lottie/Ubf/Ko;->eV()Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

    move-result-object p1

    invoke-virtual {p1}, Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;->Fj()Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    move-result-object p1

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/hjc;->Ubf:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    invoke-virtual {p1, p0}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->Fj(Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$Fj;)V

    invoke-virtual {p2, p1}, Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;->Fj(Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;)V

    invoke-virtual {p3}, Lcom/bytedance/adsdk/lottie/Ubf/Ko;->Ubf()Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

    move-result-object p1

    invoke-virtual {p1}, Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;->Fj()Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    move-result-object p1

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/hjc;->WR:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    invoke-virtual {p1, p0}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->Fj(Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$Fj;)V

    invoke-virtual {p2, p1}, Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;->Fj(Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;)V

    return-void
.end method


# virtual methods
.method public Fj()V
    .locals 1

    const/4 v0, 0x1

    iput-boolean v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/hjc;->svN:Z

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/hjc;->Fj:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$Fj;

    invoke-interface {v0}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$Fj;->Fj()V

    return-void
.end method

.method public Fj(Landroid/graphics/Paint;)V
    .locals 6

    iget-boolean v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/hjc;->svN:Z

    if-nez v0, :cond_0

    return-void

    :cond_0
    const/4 v0, 0x0

    iput-boolean v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/hjc;->svN:Z

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/hjc;->eV:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->svN()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/lang/Float;

    invoke-virtual {v0}, Ljava/lang/Float;->floatValue()F

    move-result v0

    float-to-double v0, v0

    const-wide v2, 0x3f91df46a2529d39L    # 0.017453292519943295

    mul-double v0, v0, v2

    iget-object v2, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/hjc;->Ubf:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    invoke-virtual {v2}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->svN()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Ljava/lang/Float;

    invoke-virtual {v2}, Ljava/lang/Float;->floatValue()F

    move-result v2

    invoke-static {v0, v1}, Ljava/lang/Math;->sin(D)D

    move-result-wide v3

    double-to-float v3, v3

    mul-float v3, v3, v2

    const-wide v4, 0x400921fb54442d18L    # Math.PI

    add-double/2addr v0, v4

    invoke-static {v0, v1}, Ljava/lang/Math;->cos(D)D

    move-result-wide v0

    double-to-float v0, v0

    mul-float v0, v0, v2

    iget-object v1, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/hjc;->ex:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    invoke-virtual {v1}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->svN()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/lang/Integer;

    invoke-virtual {v1}, Ljava/lang/Integer;->intValue()I

    move-result v1

    iget-object v2, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/hjc;->hjc:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    invoke-virtual {v2}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->svN()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Ljava/lang/Float;

    invoke-virtual {v2}, Ljava/lang/Float;->floatValue()F

    move-result v2

    invoke-static {v2}, Ljava/lang/Math;->round(F)I

    move-result v2

    invoke-static {v1}, Landroid/graphics/Color;->red(I)I

    move-result v4

    invoke-static {v1}, Landroid/graphics/Color;->green(I)I

    move-result v5

    invoke-static {v1}, Landroid/graphics/Color;->blue(I)I

    move-result v1

    invoke-static {v2, v4, v5, v1}, Landroid/graphics/Color;->argb(IIII)I

    move-result v1

    iget-object v2, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/hjc;->WR:Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    invoke-virtual {v2}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->svN()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Ljava/lang/Float;

    invoke-virtual {v2}, Ljava/lang/Float;->floatValue()F

    move-result v2

    invoke-virtual {p1, v2, v3, v0, v1}, Landroid/graphics/Paint;->setShadowLayer(FFFI)V

    return-void
.end method
