.class public Lcom/bytedance/adsdk/lottie/Fj/ex/rAx;
.super Lcom/bytedance/adsdk/lottie/Fj/ex/svN;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lcom/bytedance/adsdk/lottie/Fj/ex/svN<",
        "Landroid/graphics/PointF;",
        ">;"
    }
.end annotation


# instance fields
.field private final eV:Landroid/graphics/PointF;


# direct methods
.method public constructor <init>(Ljava/util/List;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lcom/bytedance/adsdk/lottie/svN/Fj<",
            "Landroid/graphics/PointF;",
            ">;>;)V"
        }
    .end annotation

    invoke-direct {p0, p1}, Lcom/bytedance/adsdk/lottie/Fj/ex/svN;-><init>(Ljava/util/List;)V

    new-instance p1, Landroid/graphics/PointF;

    invoke-direct {p1}, Landroid/graphics/PointF;-><init>()V

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/rAx;->eV:Landroid/graphics/PointF;

    return-void
.end method


# virtual methods
.method public synthetic Fj(Lcom/bytedance/adsdk/lottie/svN/Fj;F)Ljava/lang/Object;
    .locals 0

    invoke-virtual {p0, p1, p2}, Lcom/bytedance/adsdk/lottie/Fj/ex/rAx;->ex(Lcom/bytedance/adsdk/lottie/svN/Fj;F)Landroid/graphics/PointF;

    move-result-object p1

    return-object p1
.end method

.method public synthetic Fj(Lcom/bytedance/adsdk/lottie/svN/Fj;FFF)Ljava/lang/Object;
    .locals 0

    invoke-virtual {p0, p1, p2, p3, p4}, Lcom/bytedance/adsdk/lottie/Fj/ex/rAx;->ex(Lcom/bytedance/adsdk/lottie/svN/Fj;FFF)Landroid/graphics/PointF;

    move-result-object p1

    return-object p1
.end method

.method public ex(Lcom/bytedance/adsdk/lottie/svN/Fj;F)Landroid/graphics/PointF;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/bytedance/adsdk/lottie/svN/Fj<",
            "Landroid/graphics/PointF;",
            ">;F)",
            "Landroid/graphics/PointF;"
        }
    .end annotation

    invoke-virtual {p0, p1, p2, p2, p2}, Lcom/bytedance/adsdk/lottie/Fj/ex/rAx;->ex(Lcom/bytedance/adsdk/lottie/svN/Fj;FFF)Landroid/graphics/PointF;

    move-result-object p1

    return-object p1
.end method

.method public ex(Lcom/bytedance/adsdk/lottie/svN/Fj;FFF)Landroid/graphics/PointF;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/bytedance/adsdk/lottie/svN/Fj<",
            "Landroid/graphics/PointF;",
            ">;FFF)",
            "Landroid/graphics/PointF;"
        }
    .end annotation

    iget-object p2, p1, Lcom/bytedance/adsdk/lottie/svN/Fj;->Fj:Ljava/lang/Object;

    if-eqz p2, :cond_1

    iget-object v0, p1, Lcom/bytedance/adsdk/lottie/svN/Fj;->ex:Ljava/lang/Object;

    if-eqz v0, :cond_1

    check-cast p2, Landroid/graphics/PointF;

    check-cast v0, Landroid/graphics/PointF;

    iget-object v1, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->hjc:Lcom/bytedance/adsdk/lottie/svN/ex;

    if-nez v1, :cond_0

    iget-object p1, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/rAx;->eV:Landroid/graphics/PointF;

    iget v1, p2, Landroid/graphics/PointF;->x:F

    iget v2, v0, Landroid/graphics/PointF;->x:F

    sub-float/2addr v2, v1

    mul-float p3, p3, v2

    add-float/2addr v1, p3

    iget p2, p2, Landroid/graphics/PointF;->y:F

    iget p3, v0, Landroid/graphics/PointF;->y:F

    sub-float/2addr p3, p2

    mul-float p4, p4, p3

    add-float/2addr p2, p4

    invoke-virtual {p1, v1, p2}, Landroid/graphics/PointF;->set(FF)V

    iget-object p1, p0, Lcom/bytedance/adsdk/lottie/Fj/ex/rAx;->eV:Landroid/graphics/PointF;

    return-object p1

    :cond_0
    iget-object p1, p1, Lcom/bytedance/adsdk/lottie/svN/Fj;->svN:Ljava/lang/Float;

    invoke-virtual {p1}, Ljava/lang/Float;->floatValue()F

    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->eV()F

    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;->BcC()F

    const/4 p1, 0x0

    throw p1

    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    const-string p2, "Missing values for keyframe."

    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw p1
.end method
