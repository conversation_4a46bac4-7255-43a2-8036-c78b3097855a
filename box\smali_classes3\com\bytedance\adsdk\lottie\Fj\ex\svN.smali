.class abstract Lcom/bytedance/adsdk/lottie/Fj/ex/svN;
.super Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "<T:",
        "Ljava/lang/Object;",
        ">",
        "Lcom/bytedance/adsdk/lottie/Fj/ex/Fj<",
        "TT;TT;>;"
    }
.end annotation


# direct methods
.method public constructor <init>(Ljava/util/List;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "+",
            "Lcom/bytedance/adsdk/lottie/svN/Fj<",
            "TT;>;>;)V"
        }
    .end annotation

    invoke-direct {p0, p1}, Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;-><init>(Ljava/util/List;)V

    return-void
.end method
