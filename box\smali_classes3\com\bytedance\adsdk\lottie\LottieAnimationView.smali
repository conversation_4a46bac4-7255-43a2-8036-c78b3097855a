.class public Lcom/bytedance/adsdk/lottie/LottieAnimationView;
.super Landroid/widget/ImageView;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/bytedance/adsdk/lottie/LottieAnimationView$ex;,
        Lcom/bytedance/adsdk/lottie/LottieAnimationView$Fj;
    }
.end annotation


# static fields
.field private static final Fj:Ljava/lang/String; = "LottieAnimationView"

.field private static final ex:Lcom/bytedance/adsdk/lottie/Ko;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/bytedance/adsdk/lottie/Ko<",
            "Ljava/lang/Throwable;",
            ">;"
        }
    .end annotation
.end field


# instance fields
.field private BcC:Ljava/lang/String;

.field private JU:Lcom/bytedance/adsdk/lottie/UYd;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/bytedance/adsdk/lottie/UYd<",
            "Lcom/bytedance/adsdk/lottie/WR;",
            ">;"
        }
    .end annotation
.end field

.field private final JW:Ljava/util/Set;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Set<",
            "Ljava/lang/Object;",
            ">;"
        }
    .end annotation
.end field

.field private Ko:Lcom/bytedance/adsdk/ugeno/ex;

.field private Ql:Lcom/bytedance/adsdk/lottie/WR;

.field private final Tc:Ljava/util/Set;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Set<",
            "Lcom/bytedance/adsdk/lottie/LottieAnimationView$ex;",
            ">;"
        }
    .end annotation
.end field

.field private UYd:Z

.field private Ubf:Lcom/bytedance/adsdk/lottie/Ko;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/bytedance/adsdk/lottie/Ko<",
            "Ljava/lang/Throwable;",
            ">;"
        }
    .end annotation
.end field

.field private WR:I

.field private dG:Z

.field private final eV:Lcom/bytedance/adsdk/lottie/Ko;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/bytedance/adsdk/lottie/Ko<",
            "Ljava/lang/Throwable;",
            ">;"
        }
    .end annotation
.end field

.field private final hjc:Lcom/bytedance/adsdk/lottie/Ko;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/bytedance/adsdk/lottie/Ko<",
            "Lcom/bytedance/adsdk/lottie/WR;",
            ">;"
        }
    .end annotation
.end field

.field private mSE:I
    .annotation build Lcom/bytedance/component/sdk/annotation/RawRes;
    .end annotation
.end field

.field private rAx:Z

.field private final svN:Lcom/bytedance/adsdk/lottie/BcC;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    new-instance v0, Lcom/bytedance/adsdk/lottie/LottieAnimationView$1;

    invoke-direct {v0}, Lcom/bytedance/adsdk/lottie/LottieAnimationView$1;-><init>()V

    sput-object v0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->ex:Lcom/bytedance/adsdk/lottie/Ko;

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 1

    invoke-direct {p0, p1}, Landroid/widget/ImageView;-><init>(Landroid/content/Context;)V

    new-instance p1, Lcom/bytedance/adsdk/lottie/LottieAnimationView$2;

    invoke-direct {p1, p0}, Lcom/bytedance/adsdk/lottie/LottieAnimationView$2;-><init>(Lcom/bytedance/adsdk/lottie/LottieAnimationView;)V

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->hjc:Lcom/bytedance/adsdk/lottie/Ko;

    new-instance p1, Lcom/bytedance/adsdk/lottie/LottieAnimationView$3;

    invoke-direct {p1, p0}, Lcom/bytedance/adsdk/lottie/LottieAnimationView$3;-><init>(Lcom/bytedance/adsdk/lottie/LottieAnimationView;)V

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->eV:Lcom/bytedance/adsdk/lottie/Ko;

    const/4 p1, 0x0

    iput p1, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->WR:I

    new-instance v0, Lcom/bytedance/adsdk/lottie/BcC;

    invoke-direct {v0}, Lcom/bytedance/adsdk/lottie/BcC;-><init>()V

    iput-object v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->svN:Lcom/bytedance/adsdk/lottie/BcC;

    iput-boolean p1, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->rAx:Z

    iput-boolean p1, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->UYd:Z

    const/4 p1, 0x1

    iput-boolean p1, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->dG:Z

    new-instance p1, Ljava/util/HashSet;

    invoke-direct {p1}, Ljava/util/HashSet;-><init>()V

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->Tc:Ljava/util/Set;

    new-instance p1, Ljava/util/HashSet;

    invoke-direct {p1}, Ljava/util/HashSet;-><init>()V

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->JW:Ljava/util/Set;

    invoke-direct {p0}, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->WR()V

    return-void
.end method

.method private BcC()V
    .locals 1

    const/4 v0, 0x0

    iput-object v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->Ql:Lcom/bytedance/adsdk/lottie/WR;

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->svN:Lcom/bytedance/adsdk/lottie/BcC;

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/BcC;->BcC()V

    return-void
.end method

.method public static synthetic Fj(Lcom/bytedance/adsdk/lottie/LottieAnimationView;)I
    .locals 0

    iget p0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->WR:I

    return p0
.end method

.method private Fj(I)Lcom/bytedance/adsdk/lottie/UYd;
    .locals 2
    .param p1    # I
        .annotation build Lcom/bytedance/component/sdk/annotation/RawRes;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I)",
            "Lcom/bytedance/adsdk/lottie/UYd<",
            "Lcom/bytedance/adsdk/lottie/WR;",
            ">;"
        }
    .end annotation

    invoke-virtual {p0}, Landroid/view/View;->isInEditMode()Z

    move-result v0

    if-eqz v0, :cond_0

    new-instance v0, Lcom/bytedance/adsdk/lottie/UYd;

    new-instance v1, Lcom/bytedance/adsdk/lottie/LottieAnimationView$4;

    invoke-direct {v1, p0, p1}, Lcom/bytedance/adsdk/lottie/LottieAnimationView$4;-><init>(Lcom/bytedance/adsdk/lottie/LottieAnimationView;I)V

    const/4 p1, 0x1

    invoke-direct {v0, v1, p1}, Lcom/bytedance/adsdk/lottie/UYd;-><init>(Ljava/util/concurrent/Callable;Z)V

    return-object v0

    :cond_0
    iget-boolean v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->dG:Z

    if-eqz v0, :cond_1

    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-static {v0, p1}, Lcom/bytedance/adsdk/lottie/svN;->Fj(Landroid/content/Context;I)Lcom/bytedance/adsdk/lottie/UYd;

    move-result-object p1

    return-object p1

    :cond_1
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    const/4 v1, 0x0

    invoke-static {v0, p1, v1}, Lcom/bytedance/adsdk/lottie/svN;->Fj(Landroid/content/Context;ILjava/lang/String;)Lcom/bytedance/adsdk/lottie/UYd;

    move-result-object p1

    return-object p1
.end method

.method private Fj(Ljava/lang/String;)Lcom/bytedance/adsdk/lottie/UYd;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            ")",
            "Lcom/bytedance/adsdk/lottie/UYd<",
            "Lcom/bytedance/adsdk/lottie/WR;",
            ">;"
        }
    .end annotation

    invoke-virtual {p0}, Landroid/view/View;->isInEditMode()Z

    move-result v0

    if-eqz v0, :cond_0

    new-instance v0, Lcom/bytedance/adsdk/lottie/UYd;

    new-instance v1, Lcom/bytedance/adsdk/lottie/LottieAnimationView$5;

    invoke-direct {v1, p0, p1}, Lcom/bytedance/adsdk/lottie/LottieAnimationView$5;-><init>(Lcom/bytedance/adsdk/lottie/LottieAnimationView;Ljava/lang/String;)V

    const/4 p1, 0x1

    invoke-direct {v0, v1, p1}, Lcom/bytedance/adsdk/lottie/UYd;-><init>(Ljava/util/concurrent/Callable;Z)V

    return-object v0

    :cond_0
    iget-boolean v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->dG:Z

    if-eqz v0, :cond_1

    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-static {v0, p1}, Lcom/bytedance/adsdk/lottie/svN;->ex(Landroid/content/Context;Ljava/lang/String;)Lcom/bytedance/adsdk/lottie/UYd;

    move-result-object p1

    return-object p1

    :cond_1
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    const/4 v1, 0x0

    invoke-static {v0, p1, v1}, Lcom/bytedance/adsdk/lottie/svN;->ex(Landroid/content/Context;Ljava/lang/String;Ljava/lang/String;)Lcom/bytedance/adsdk/lottie/UYd;

    move-result-object p1

    return-object p1
.end method

.method private Fj(FZ)V
    .locals 1
    .param p1    # F
        .annotation build Lcom/bytedance/component/sdk/annotation/FloatRange;
            from = 0.0
            to = 1.0
        .end annotation
    .end param

    if-eqz p2, :cond_0

    iget-object p2, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->Tc:Ljava/util/Set;

    sget-object v0, Lcom/bytedance/adsdk/lottie/LottieAnimationView$ex;->ex:Lcom/bytedance/adsdk/lottie/LottieAnimationView$ex;

    invoke-interface {p2, v0}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    :cond_0
    iget-object p2, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->svN:Lcom/bytedance/adsdk/lottie/BcC;

    invoke-virtual {p2, p1}, Lcom/bytedance/adsdk/lottie/BcC;->eV(F)V

    return-void
.end method

.method public static synthetic Ubf()Lcom/bytedance/adsdk/lottie/Ko;
    .locals 1

    sget-object v0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->ex:Lcom/bytedance/adsdk/lottie/Ko;

    return-object v0
.end method

.method private WR()V
    .locals 5

    const/4 v0, 0x0

    invoke-virtual {p0, v0}, Landroid/view/View;->setSaveEnabled(Z)V

    const/4 v1, 0x1

    iput-boolean v1, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->dG:Z

    invoke-virtual {p0, v0}, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->setFallbackResource(I)V

    const-string v2, ""

    invoke-virtual {p0, v2}, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->setImageAssetsFolder(Ljava/lang/String;)V

    const/4 v2, 0x0

    invoke-direct {p0, v2, v0}, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->Fj(FZ)V

    invoke-virtual {p0, v0}, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->Fj(Z)V

    invoke-virtual {p0, v0}, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->setIgnoreDisabledSystemAnimations(Z)V

    iget-object v3, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->svN:Lcom/bytedance/adsdk/lottie/BcC;

    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v4

    invoke-static {v4}, Lcom/bytedance/adsdk/lottie/WR/WR;->Fj(Landroid/content/Context;)F

    move-result v4

    cmpl-float v2, v4, v2

    if-eqz v2, :cond_0

    const/4 v0, 0x1

    :cond_0
    invoke-static {v0}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v0

    invoke-virtual {v3, v0}, Lcom/bytedance/adsdk/lottie/BcC;->Fj(Ljava/lang/Boolean;)V

    return-void
.end method

.method public static synthetic ex(Lcom/bytedance/adsdk/lottie/LottieAnimationView;)Lcom/bytedance/adsdk/lottie/Ko;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->Ubf:Lcom/bytedance/adsdk/lottie/Ko;

    return-object p0
.end method

.method public static synthetic hjc(Lcom/bytedance/adsdk/lottie/LottieAnimationView;)Z
    .locals 0

    iget-boolean p0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->dG:Z

    return p0
.end method

.method private mSE()V
    .locals 2

    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->ex()Z

    move-result v0

    const/4 v1, 0x0

    invoke-virtual {p0, v1}, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->setImageDrawable(Landroid/graphics/drawable/Drawable;)V

    iget-object v1, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->svN:Lcom/bytedance/adsdk/lottie/BcC;

    invoke-virtual {p0, v1}, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->setImageDrawable(Landroid/graphics/drawable/Drawable;)V

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->svN:Lcom/bytedance/adsdk/lottie/BcC;

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/BcC;->rAx()V

    :cond_0
    return-void
.end method

.method private setCompositionTask(Lcom/bytedance/adsdk/lottie/UYd;)V
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/bytedance/adsdk/lottie/UYd<",
            "Lcom/bytedance/adsdk/lottie/WR;",
            ">;)V"
        }
    .end annotation

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->Tc:Ljava/util/Set;

    sget-object v1, Lcom/bytedance/adsdk/lottie/LottieAnimationView$ex;->Fj:Lcom/bytedance/adsdk/lottie/LottieAnimationView$ex;

    invoke-interface {v0, v1}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    invoke-direct {p0}, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->BcC()V

    invoke-direct {p0}, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->svN()V

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->hjc:Lcom/bytedance/adsdk/lottie/Ko;

    invoke-virtual {p1, v0}, Lcom/bytedance/adsdk/lottie/UYd;->Fj(Lcom/bytedance/adsdk/lottie/Ko;)Lcom/bytedance/adsdk/lottie/UYd;

    move-result-object p1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->eV:Lcom/bytedance/adsdk/lottie/Ko;

    invoke-virtual {p1, v0}, Lcom/bytedance/adsdk/lottie/UYd;->hjc(Lcom/bytedance/adsdk/lottie/Ko;)Lcom/bytedance/adsdk/lottie/UYd;

    move-result-object p1

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->JU:Lcom/bytedance/adsdk/lottie/UYd;

    return-void
.end method

.method private svN()V
    .locals 2

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->JU:Lcom/bytedance/adsdk/lottie/UYd;

    if-eqz v0, :cond_0

    iget-object v1, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->hjc:Lcom/bytedance/adsdk/lottie/Ko;

    invoke-virtual {v0, v1}, Lcom/bytedance/adsdk/lottie/UYd;->ex(Lcom/bytedance/adsdk/lottie/Ko;)Lcom/bytedance/adsdk/lottie/UYd;

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->JU:Lcom/bytedance/adsdk/lottie/UYd;

    iget-object v1, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->eV:Lcom/bytedance/adsdk/lottie/Ko;

    invoke-virtual {v0, v1}, Lcom/bytedance/adsdk/lottie/UYd;->eV(Lcom/bytedance/adsdk/lottie/Ko;)Lcom/bytedance/adsdk/lottie/UYd;

    :cond_0
    return-void
.end method


# virtual methods
.method public Fj(Ljava/lang/String;Landroid/graphics/Bitmap;)Landroid/graphics/Bitmap;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->svN:Lcom/bytedance/adsdk/lottie/BcC;

    invoke-virtual {v0, p1, p2}, Lcom/bytedance/adsdk/lottie/BcC;->Fj(Ljava/lang/String;Landroid/graphics/Bitmap;)Landroid/graphics/Bitmap;

    move-result-object p1

    return-object p1
.end method

.method public Fj()V
    .locals 2
    .annotation build Lcom/bytedance/component/sdk/annotation/MainThread;
    .end annotation

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->Tc:Ljava/util/Set;

    sget-object v1, Lcom/bytedance/adsdk/lottie/LottieAnimationView$ex;->WR:Lcom/bytedance/adsdk/lottie/LottieAnimationView$ex;

    invoke-interface {v0, v1}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->svN:Lcom/bytedance/adsdk/lottie/BcC;

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/BcC;->mSE()V

    return-void
.end method

.method public Fj(Lcom/bytedance/adsdk/ugeno/ex;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->Ko:Lcom/bytedance/adsdk/ugeno/ex;

    return-void
.end method

.method public Fj(Ljava/io/InputStream;Ljava/lang/String;)V
    .locals 0

    invoke-static {p1, p2}, Lcom/bytedance/adsdk/lottie/svN;->Fj(Ljava/io/InputStream;Ljava/lang/String;)Lcom/bytedance/adsdk/lottie/UYd;

    move-result-object p1

    invoke-direct {p0, p1}, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->setCompositionTask(Lcom/bytedance/adsdk/lottie/UYd;)V

    return-void
.end method

.method public Fj(Ljava/lang/String;Ljava/lang/String;)V
    .locals 1

    new-instance v0, Ljava/io/ByteArrayInputStream;

    invoke-virtual {p1}, Ljava/lang/String;->getBytes()[B

    move-result-object p1

    invoke-direct {v0, p1}, Ljava/io/ByteArrayInputStream;-><init>([B)V

    invoke-virtual {p0, v0, p2}, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->Fj(Ljava/io/InputStream;Ljava/lang/String;)V

    return-void
.end method

.method public Fj(Z)V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->svN:Lcom/bytedance/adsdk/lottie/BcC;

    invoke-virtual {v0, p1}, Lcom/bytedance/adsdk/lottie/BcC;->Fj(Z)V

    return-void
.end method

.method public eV()V
    .locals 1
    .annotation build Lcom/bytedance/component/sdk/annotation/MainThread;
    .end annotation

    const/4 v0, 0x0

    iput-boolean v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->UYd:Z

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->svN:Lcom/bytedance/adsdk/lottie/BcC;

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/BcC;->nsB()V

    return-void
.end method

.method public ex(Z)V
    .locals 1
    .annotation runtime Ljava/lang/Deprecated;
    .end annotation

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->svN:Lcom/bytedance/adsdk/lottie/BcC;

    if-eqz p1, :cond_0

    const/4 p1, -0x1

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    :goto_0
    invoke-virtual {v0, p1}, Lcom/bytedance/adsdk/lottie/BcC;->Ubf(I)V

    return-void
.end method

.method public ex()Z
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->svN:Lcom/bytedance/adsdk/lottie/BcC;

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/BcC;->rS()Z

    move-result v0

    return v0
.end method

.method public getClipToCompositionBounds()Z
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->svN:Lcom/bytedance/adsdk/lottie/BcC;

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/BcC;->ex()Z

    move-result v0

    return v0
.end method

.method public getComposition()Lcom/bytedance/adsdk/lottie/WR;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->Ql:Lcom/bytedance/adsdk/lottie/WR;

    return-object v0
.end method

.method public getDuration()J
    .locals 2

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->Ql:Lcom/bytedance/adsdk/lottie/WR;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/WR;->Ubf()F

    move-result v0

    float-to-long v0, v0

    return-wide v0

    :cond_0
    const-wide/16 v0, 0x0

    return-wide v0
.end method

.method public getFrame()I
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->svN:Lcom/bytedance/adsdk/lottie/BcC;

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/BcC;->JW()I

    move-result v0

    return v0
.end method

.method public getImageAssetsFolder()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->svN:Lcom/bytedance/adsdk/lottie/BcC;

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/BcC;->hjc()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public getMaintainOriginalImageBounds()Z
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->svN:Lcom/bytedance/adsdk/lottie/BcC;

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/BcC;->eV()Z

    move-result v0

    return v0
.end method

.method public getMaxFrame()F
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->svN:Lcom/bytedance/adsdk/lottie/BcC;

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/BcC;->dG()F

    move-result v0

    return v0
.end method

.method public getMinFrame()F
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->svN:Lcom/bytedance/adsdk/lottie/BcC;

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/BcC;->UYd()F

    move-result v0

    return v0
.end method

.method public getPerformanceTracker()Lcom/bytedance/adsdk/lottie/Ql;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->svN:Lcom/bytedance/adsdk/lottie/BcC;

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/BcC;->WR()Lcom/bytedance/adsdk/lottie/Ql;

    move-result-object v0

    return-object v0
.end method

.method public getProgress()F
    .locals 1
    .annotation build Lcom/bytedance/component/sdk/annotation/FloatRange;
        from = 0.0
        to = 1.0
    .end annotation

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->svN:Lcom/bytedance/adsdk/lottie/BcC;

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/BcC;->Vq()F

    move-result v0

    return v0
.end method

.method public getRenderMode()Lcom/bytedance/adsdk/lottie/rS;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->svN:Lcom/bytedance/adsdk/lottie/BcC;

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/BcC;->Ubf()Lcom/bytedance/adsdk/lottie/rS;

    move-result-object v0

    return-object v0
.end method

.method public getRepeatCount()I
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->svN:Lcom/bytedance/adsdk/lottie/BcC;

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/BcC;->Ql()I

    move-result v0

    return v0
.end method

.method public getRepeatMode()I
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->svN:Lcom/bytedance/adsdk/lottie/BcC;

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/BcC;->JU()I

    move-result v0

    return v0
.end method

.method public getSpeed()F
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->svN:Lcom/bytedance/adsdk/lottie/BcC;

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/BcC;->Tc()F

    move-result v0

    return v0
.end method

.method public hjc()V
    .locals 2
    .annotation build Lcom/bytedance/component/sdk/annotation/MainThread;
    .end annotation

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->Tc:Ljava/util/Set;

    sget-object v1, Lcom/bytedance/adsdk/lottie/LottieAnimationView$ex;->WR:Lcom/bytedance/adsdk/lottie/LottieAnimationView$ex;

    invoke-interface {v0, v1}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->svN:Lcom/bytedance/adsdk/lottie/BcC;

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/BcC;->cB()V

    return-void
.end method

.method public invalidate()V
    .locals 2

    invoke-super {p0}, Landroid/widget/ImageView;->invalidate()V

    invoke-virtual {p0}, Landroid/widget/ImageView;->getDrawable()Landroid/graphics/drawable/Drawable;

    move-result-object v0

    instance-of v1, v0, Lcom/bytedance/adsdk/lottie/BcC;

    if-eqz v1, :cond_0

    check-cast v0, Lcom/bytedance/adsdk/lottie/BcC;

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/BcC;->Ubf()Lcom/bytedance/adsdk/lottie/rS;

    move-result-object v0

    sget-object v1, Lcom/bytedance/adsdk/lottie/rS;->hjc:Lcom/bytedance/adsdk/lottie/rS;

    if-ne v0, v1, :cond_0

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->svN:Lcom/bytedance/adsdk/lottie/BcC;

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/BcC;->invalidateSelf()V

    :cond_0
    return-void
.end method

.method public invalidateDrawable(Landroid/graphics/drawable/Drawable;)V
    .locals 2

    invoke-virtual {p0}, Landroid/widget/ImageView;->getDrawable()Landroid/graphics/drawable/Drawable;

    move-result-object v0

    iget-object v1, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->svN:Lcom/bytedance/adsdk/lottie/BcC;

    if-ne v0, v1, :cond_0

    invoke-super {p0, v1}, Landroid/widget/ImageView;->invalidateDrawable(Landroid/graphics/drawable/Drawable;)V

    return-void

    :cond_0
    invoke-super {p0, p1}, Landroid/widget/ImageView;->invalidateDrawable(Landroid/graphics/drawable/Drawable;)V

    return-void
.end method

.method public onAttachedToWindow()V
    .locals 1

    invoke-super {p0}, Landroid/widget/ImageView;->onAttachedToWindow()V

    invoke-virtual {p0}, Landroid/view/View;->isInEditMode()Z

    move-result v0

    if-nez v0, :cond_0

    iget-boolean v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->UYd:Z

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->svN:Lcom/bytedance/adsdk/lottie/BcC;

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/BcC;->mSE()V

    :cond_0
    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->Ko:Lcom/bytedance/adsdk/ugeno/ex;

    if-eqz v0, :cond_1

    invoke-interface {v0}, Lcom/bytedance/adsdk/ugeno/ex;->WR()V

    :cond_1
    return-void
.end method

.method public onDetachedFromWindow()V
    .locals 1

    invoke-super {p0}, Landroid/widget/ImageView;->onDetachedFromWindow()V

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->Ko:Lcom/bytedance/adsdk/ugeno/ex;

    if-eqz v0, :cond_0

    invoke-interface {v0}, Lcom/bytedance/adsdk/ugeno/ex;->WR()V

    :cond_0
    return-void
.end method

.method public onRestoreInstanceState(Landroid/os/Parcelable;)V
    .locals 2

    instance-of v0, p1, Lcom/bytedance/adsdk/lottie/LottieAnimationView$Fj;

    if-nez v0, :cond_0

    invoke-super {p0, p1}, Landroid/widget/ImageView;->onRestoreInstanceState(Landroid/os/Parcelable;)V

    return-void

    :cond_0
    check-cast p1, Lcom/bytedance/adsdk/lottie/LottieAnimationView$Fj;

    invoke-virtual {p1}, Landroid/view/AbsSavedState;->getSuperState()Landroid/os/Parcelable;

    move-result-object v0

    invoke-super {p0, v0}, Landroid/widget/ImageView;->onRestoreInstanceState(Landroid/os/Parcelable;)V

    iget-object v0, p1, Lcom/bytedance/adsdk/lottie/LottieAnimationView$Fj;->Fj:Ljava/lang/String;

    iput-object v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->BcC:Ljava/lang/String;

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->Tc:Ljava/util/Set;

    sget-object v1, Lcom/bytedance/adsdk/lottie/LottieAnimationView$ex;->Fj:Lcom/bytedance/adsdk/lottie/LottieAnimationView$ex;

    invoke-interface {v0, v1}, Ljava/util/Set;->contains(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->BcC:Ljava/lang/String;

    invoke-static {v0}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    if-nez v0, :cond_1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->BcC:Ljava/lang/String;

    invoke-virtual {p0, v0}, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->setAnimation(Ljava/lang/String;)V

    :cond_1
    iget v0, p1, Lcom/bytedance/adsdk/lottie/LottieAnimationView$Fj;->ex:I

    iput v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->mSE:I

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->Tc:Ljava/util/Set;

    invoke-interface {v0, v1}, Ljava/util/Set;->contains(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_2

    iget v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->mSE:I

    if-eqz v0, :cond_2

    invoke-virtual {p0, v0}, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->setAnimation(I)V

    :cond_2
    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->Tc:Ljava/util/Set;

    sget-object v1, Lcom/bytedance/adsdk/lottie/LottieAnimationView$ex;->ex:Lcom/bytedance/adsdk/lottie/LottieAnimationView$ex;

    invoke-interface {v0, v1}, Ljava/util/Set;->contains(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_3

    iget v0, p1, Lcom/bytedance/adsdk/lottie/LottieAnimationView$Fj;->hjc:F

    const/4 v1, 0x0

    invoke-direct {p0, v0, v1}, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->Fj(FZ)V

    :cond_3
    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->Tc:Ljava/util/Set;

    sget-object v1, Lcom/bytedance/adsdk/lottie/LottieAnimationView$ex;->WR:Lcom/bytedance/adsdk/lottie/LottieAnimationView$ex;

    invoke-interface {v0, v1}, Ljava/util/Set;->contains(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_4

    iget-boolean v0, p1, Lcom/bytedance/adsdk/lottie/LottieAnimationView$Fj;->eV:Z

    if-eqz v0, :cond_4

    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->Fj()V

    :cond_4
    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->Tc:Ljava/util/Set;

    sget-object v1, Lcom/bytedance/adsdk/lottie/LottieAnimationView$ex;->Ubf:Lcom/bytedance/adsdk/lottie/LottieAnimationView$ex;

    invoke-interface {v0, v1}, Ljava/util/Set;->contains(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_5

    iget-object v0, p1, Lcom/bytedance/adsdk/lottie/LottieAnimationView$Fj;->Ubf:Ljava/lang/String;

    invoke-virtual {p0, v0}, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->setImageAssetsFolder(Ljava/lang/String;)V

    :cond_5
    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->Tc:Ljava/util/Set;

    sget-object v1, Lcom/bytedance/adsdk/lottie/LottieAnimationView$ex;->hjc:Lcom/bytedance/adsdk/lottie/LottieAnimationView$ex;

    invoke-interface {v0, v1}, Ljava/util/Set;->contains(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_6

    iget v0, p1, Lcom/bytedance/adsdk/lottie/LottieAnimationView$Fj;->WR:I

    invoke-virtual {p0, v0}, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->setRepeatMode(I)V

    :cond_6
    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->Tc:Ljava/util/Set;

    sget-object v1, Lcom/bytedance/adsdk/lottie/LottieAnimationView$ex;->eV:Lcom/bytedance/adsdk/lottie/LottieAnimationView$ex;

    invoke-interface {v0, v1}, Ljava/util/Set;->contains(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_7

    iget p1, p1, Lcom/bytedance/adsdk/lottie/LottieAnimationView$Fj;->svN:I

    invoke-virtual {p0, p1}, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->setRepeatCount(I)V

    :cond_7
    return-void
.end method

.method public onSaveInstanceState()Landroid/os/Parcelable;
    .locals 2

    invoke-super {p0}, Landroid/widget/ImageView;->onSaveInstanceState()Landroid/os/Parcelable;

    move-result-object v0

    new-instance v1, Lcom/bytedance/adsdk/lottie/LottieAnimationView$Fj;

    invoke-direct {v1, v0}, Lcom/bytedance/adsdk/lottie/LottieAnimationView$Fj;-><init>(Landroid/os/Parcelable;)V

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->BcC:Ljava/lang/String;

    iput-object v0, v1, Lcom/bytedance/adsdk/lottie/LottieAnimationView$Fj;->Fj:Ljava/lang/String;

    iget v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->mSE:I

    iput v0, v1, Lcom/bytedance/adsdk/lottie/LottieAnimationView$Fj;->ex:I

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->svN:Lcom/bytedance/adsdk/lottie/BcC;

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/BcC;->Vq()F

    move-result v0

    iput v0, v1, Lcom/bytedance/adsdk/lottie/LottieAnimationView$Fj;->hjc:F

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->svN:Lcom/bytedance/adsdk/lottie/BcC;

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/BcC;->vYf()Z

    move-result v0

    iput-boolean v0, v1, Lcom/bytedance/adsdk/lottie/LottieAnimationView$Fj;->eV:Z

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->svN:Lcom/bytedance/adsdk/lottie/BcC;

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/BcC;->hjc()Ljava/lang/String;

    move-result-object v0

    iput-object v0, v1, Lcom/bytedance/adsdk/lottie/LottieAnimationView$Fj;->Ubf:Ljava/lang/String;

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->svN:Lcom/bytedance/adsdk/lottie/BcC;

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/BcC;->JU()I

    move-result v0

    iput v0, v1, Lcom/bytedance/adsdk/lottie/LottieAnimationView$Fj;->WR:I

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->svN:Lcom/bytedance/adsdk/lottie/BcC;

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/BcC;->Ql()I

    move-result v0

    iput v0, v1, Lcom/bytedance/adsdk/lottie/LottieAnimationView$Fj;->svN:I

    return-object v1
.end method

.method public setAnimation(I)V
    .locals 1
    .param p1    # I
        .annotation build Lcom/bytedance/component/sdk/annotation/RawRes;
        .end annotation
    .end param

    iput p1, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->mSE:I

    const/4 v0, 0x0

    iput-object v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->BcC:Ljava/lang/String;

    invoke-direct {p0, p1}, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->Fj(I)Lcom/bytedance/adsdk/lottie/UYd;

    move-result-object p1

    invoke-direct {p0, p1}, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->setCompositionTask(Lcom/bytedance/adsdk/lottie/UYd;)V

    return-void
.end method

.method public setAnimation(Ljava/lang/String;)V
    .locals 1

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->BcC:Ljava/lang/String;

    const/4 v0, 0x0

    iput v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->mSE:I

    invoke-direct {p0, p1}, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->Fj(Ljava/lang/String;)Lcom/bytedance/adsdk/lottie/UYd;

    move-result-object p1

    invoke-direct {p0, p1}, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->setCompositionTask(Lcom/bytedance/adsdk/lottie/UYd;)V

    return-void
.end method

.method public setAnimationFromJson(Ljava/lang/String;)V
    .locals 1
    .annotation runtime Ljava/lang/Deprecated;
    .end annotation

    const/4 v0, 0x0

    invoke-virtual {p0, p1, v0}, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->Fj(Ljava/lang/String;Ljava/lang/String;)V

    return-void
.end method

.method public setAnimationFromUrl(Ljava/lang/String;)V
    .locals 2

    iget-boolean v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->dG:Z

    if-eqz v0, :cond_0

    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-static {v0, p1}, Lcom/bytedance/adsdk/lottie/svN;->Fj(Landroid/content/Context;Ljava/lang/String;)Lcom/bytedance/adsdk/lottie/UYd;

    move-result-object p1

    goto :goto_0

    :cond_0
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    const/4 v1, 0x0

    invoke-static {v0, p1, v1}, Lcom/bytedance/adsdk/lottie/svN;->Fj(Landroid/content/Context;Ljava/lang/String;Ljava/lang/String;)Lcom/bytedance/adsdk/lottie/UYd;

    move-result-object p1

    :goto_0
    invoke-direct {p0, p1}, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->setCompositionTask(Lcom/bytedance/adsdk/lottie/UYd;)V

    return-void
.end method

.method public setApplyingOpacityToLayersEnabled(Z)V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->svN:Lcom/bytedance/adsdk/lottie/BcC;

    invoke-virtual {v0, p1}, Lcom/bytedance/adsdk/lottie/BcC;->WR(Z)V

    return-void
.end method

.method public setCacheComposition(Z)V
    .locals 0

    iput-boolean p1, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->dG:Z

    return-void
.end method

.method public setClipToCompositionBounds(Z)V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->svN:Lcom/bytedance/adsdk/lottie/BcC;

    invoke-virtual {v0, p1}, Lcom/bytedance/adsdk/lottie/BcC;->ex(Z)V

    return-void
.end method

.method public setComposition(Lcom/bytedance/adsdk/lottie/WR;)V
    .locals 3

    sget-boolean v0, Lcom/bytedance/adsdk/lottie/Ubf;->Fj:Z

    if-eqz v0, :cond_0

    sget-object v0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->Fj:Ljava/lang/String;

    invoke-static {p1}, Ljava/lang/String;->valueOf(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v1

    const-string v2, "Set Composition \n"

    invoke-virtual {v2, v1}, Ljava/lang/String;->concat(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v1

    invoke-static {v0, v1}, Landroid/util/Log;->v(Ljava/lang/String;Ljava/lang/String;)I

    :cond_0
    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->svN:Lcom/bytedance/adsdk/lottie/BcC;

    invoke-virtual {v0, p0}, Landroid/graphics/drawable/Drawable;->setCallback(Landroid/graphics/drawable/Drawable$Callback;)V

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->Ql:Lcom/bytedance/adsdk/lottie/WR;

    const/4 v0, 0x1

    iput-boolean v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->rAx:Z

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->svN:Lcom/bytedance/adsdk/lottie/BcC;

    invoke-virtual {v0, p1}, Lcom/bytedance/adsdk/lottie/BcC;->Fj(Lcom/bytedance/adsdk/lottie/WR;)Z

    move-result p1

    const/4 v0, 0x0

    iput-boolean v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->rAx:Z

    invoke-virtual {p0}, Landroid/widget/ImageView;->getDrawable()Landroid/graphics/drawable/Drawable;

    move-result-object v0

    iget-object v1, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->svN:Lcom/bytedance/adsdk/lottie/BcC;

    if-ne v0, v1, :cond_1

    if-nez p1, :cond_1

    return-void

    :cond_1
    if-nez p1, :cond_2

    invoke-direct {p0}, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->mSE()V

    :cond_2
    invoke-virtual {p0}, Landroid/view/View;->getVisibility()I

    move-result p1

    invoke-virtual {p0, p0, p1}, Landroid/view/View;->onVisibilityChanged(Landroid/view/View;I)V

    invoke-virtual {p0}, Landroid/view/View;->requestLayout()V

    iget-object p1, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->JW:Ljava/util/Set;

    invoke-interface {p1}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_3

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    goto :goto_0

    :cond_3
    return-void
.end method

.method public setDefaultFontFileExtension(Ljava/lang/String;)V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->svN:Lcom/bytedance/adsdk/lottie/BcC;

    invoke-virtual {v0, p1}, Lcom/bytedance/adsdk/lottie/BcC;->svN(Ljava/lang/String;)V

    return-void
.end method

.method public setFailureListener(Lcom/bytedance/adsdk/lottie/Ko;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/bytedance/adsdk/lottie/Ko<",
            "Ljava/lang/Throwable;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->Ubf:Lcom/bytedance/adsdk/lottie/Ko;

    return-void
.end method

.method public setFallbackResource(I)V
    .locals 0

    iput p1, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->WR:I

    return-void
.end method

.method public setFontAssetDelegate(Lcom/bytedance/adsdk/lottie/hjc;)V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->svN:Lcom/bytedance/adsdk/lottie/BcC;

    invoke-virtual {v0, p1}, Lcom/bytedance/adsdk/lottie/BcC;->Fj(Lcom/bytedance/adsdk/lottie/hjc;)V

    return-void
.end method

.method public setFontMap(Ljava/util/Map;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Landroid/graphics/Typeface;",
            ">;)V"
        }
    .end annotation

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->svN:Lcom/bytedance/adsdk/lottie/BcC;

    invoke-virtual {v0, p1}, Lcom/bytedance/adsdk/lottie/BcC;->Fj(Ljava/util/Map;)V

    return-void
.end method

.method public setFrame(I)V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->svN:Lcom/bytedance/adsdk/lottie/BcC;

    invoke-virtual {v0, p1}, Lcom/bytedance/adsdk/lottie/BcC;->hjc(I)V

    return-void
.end method

.method public setIgnoreDisabledSystemAnimations(Z)V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->svN:Lcom/bytedance/adsdk/lottie/BcC;

    invoke-virtual {v0, p1}, Lcom/bytedance/adsdk/lottie/BcC;->BcC(Z)V

    return-void
.end method

.method public setImageAssetDelegate(Lcom/bytedance/adsdk/lottie/eV;)V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->svN:Lcom/bytedance/adsdk/lottie/BcC;

    invoke-virtual {v0, p1}, Lcom/bytedance/adsdk/lottie/BcC;->Fj(Lcom/bytedance/adsdk/lottie/eV;)V

    return-void
.end method

.method public setImageAssetsFolder(Ljava/lang/String;)V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->svN:Lcom/bytedance/adsdk/lottie/BcC;

    invoke-virtual {v0, p1}, Lcom/bytedance/adsdk/lottie/BcC;->Fj(Ljava/lang/String;)V

    return-void
.end method

.method public setImageBitmap(Landroid/graphics/Bitmap;)V
    .locals 0

    invoke-direct {p0}, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->svN()V

    invoke-super {p0, p1}, Landroid/widget/ImageView;->setImageBitmap(Landroid/graphics/Bitmap;)V

    return-void
.end method

.method public setImageDrawable(Landroid/graphics/drawable/Drawable;)V
    .locals 0

    invoke-direct {p0}, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->svN()V

    invoke-super {p0, p1}, Landroid/widget/ImageView;->setImageDrawable(Landroid/graphics/drawable/Drawable;)V

    return-void
.end method

.method public setImageResource(I)V
    .locals 0

    invoke-direct {p0}, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->svN()V

    invoke-super {p0, p1}, Landroid/widget/ImageView;->setImageResource(I)V

    return-void
.end method

.method public setMaintainOriginalImageBounds(Z)V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->svN:Lcom/bytedance/adsdk/lottie/BcC;

    invoke-virtual {v0, p1}, Lcom/bytedance/adsdk/lottie/BcC;->hjc(Z)V

    return-void
.end method

.method public setMaxFrame(I)V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->svN:Lcom/bytedance/adsdk/lottie/BcC;

    invoke-virtual {v0, p1}, Lcom/bytedance/adsdk/lottie/BcC;->ex(I)V

    return-void
.end method

.method public setMaxFrame(Ljava/lang/String;)V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->svN:Lcom/bytedance/adsdk/lottie/BcC;

    invoke-virtual {v0, p1}, Lcom/bytedance/adsdk/lottie/BcC;->hjc(Ljava/lang/String;)V

    return-void
.end method

.method public setMaxProgress(F)V
    .locals 1
    .param p1    # F
        .annotation build Lcom/bytedance/component/sdk/annotation/FloatRange;
            from = 0.0
            to = 1.0
        .end annotation
    .end param

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->svN:Lcom/bytedance/adsdk/lottie/BcC;

    invoke-virtual {v0, p1}, Lcom/bytedance/adsdk/lottie/BcC;->ex(F)V

    return-void
.end method

.method public setMinAndMaxFrame(Ljava/lang/String;)V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->svN:Lcom/bytedance/adsdk/lottie/BcC;

    invoke-virtual {v0, p1}, Lcom/bytedance/adsdk/lottie/BcC;->eV(Ljava/lang/String;)V

    return-void
.end method

.method public setMinFrame(I)V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->svN:Lcom/bytedance/adsdk/lottie/BcC;

    invoke-virtual {v0, p1}, Lcom/bytedance/adsdk/lottie/BcC;->Fj(I)V

    return-void
.end method

.method public setMinFrame(Ljava/lang/String;)V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->svN:Lcom/bytedance/adsdk/lottie/BcC;

    invoke-virtual {v0, p1}, Lcom/bytedance/adsdk/lottie/BcC;->ex(Ljava/lang/String;)V

    return-void
.end method

.method public setMinProgress(F)V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->svN:Lcom/bytedance/adsdk/lottie/BcC;

    invoke-virtual {v0, p1}, Lcom/bytedance/adsdk/lottie/BcC;->Fj(F)V

    return-void
.end method

.method public setOutlineMasksAndMattes(Z)V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->svN:Lcom/bytedance/adsdk/lottie/BcC;

    invoke-virtual {v0, p1}, Lcom/bytedance/adsdk/lottie/BcC;->Ubf(Z)V

    return-void
.end method

.method public setPerformanceTrackingEnabled(Z)V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->svN:Lcom/bytedance/adsdk/lottie/BcC;

    invoke-virtual {v0, p1}, Lcom/bytedance/adsdk/lottie/BcC;->eV(Z)V

    return-void
.end method

.method public setProgress(F)V
    .locals 1
    .param p1    # F
        .annotation build Lcom/bytedance/component/sdk/annotation/FloatRange;
            from = 0.0
            to = 1.0
        .end annotation
    .end param

    const/4 v0, 0x1

    invoke-direct {p0, p1, v0}, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->Fj(FZ)V

    return-void
.end method

.method public setRenderMode(Lcom/bytedance/adsdk/lottie/rS;)V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->svN:Lcom/bytedance/adsdk/lottie/BcC;

    invoke-virtual {v0, p1}, Lcom/bytedance/adsdk/lottie/BcC;->Fj(Lcom/bytedance/adsdk/lottie/rS;)V

    return-void
.end method

.method public setRepeatCount(I)V
    .locals 2

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->Tc:Ljava/util/Set;

    sget-object v1, Lcom/bytedance/adsdk/lottie/LottieAnimationView$ex;->eV:Lcom/bytedance/adsdk/lottie/LottieAnimationView$ex;

    invoke-interface {v0, v1}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->svN:Lcom/bytedance/adsdk/lottie/BcC;

    invoke-virtual {v0, p1}, Lcom/bytedance/adsdk/lottie/BcC;->Ubf(I)V

    return-void
.end method

.method public setRepeatMode(I)V
    .locals 2

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->Tc:Ljava/util/Set;

    sget-object v1, Lcom/bytedance/adsdk/lottie/LottieAnimationView$ex;->hjc:Lcom/bytedance/adsdk/lottie/LottieAnimationView$ex;

    invoke-interface {v0, v1}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->svN:Lcom/bytedance/adsdk/lottie/BcC;

    invoke-virtual {v0, p1}, Lcom/bytedance/adsdk/lottie/BcC;->eV(I)V

    return-void
.end method

.method public setSafeMode(Z)V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->svN:Lcom/bytedance/adsdk/lottie/BcC;

    invoke-virtual {v0, p1}, Lcom/bytedance/adsdk/lottie/BcC;->svN(Z)V

    return-void
.end method

.method public setSpeed(F)V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->svN:Lcom/bytedance/adsdk/lottie/BcC;

    invoke-virtual {v0, p1}, Lcom/bytedance/adsdk/lottie/BcC;->hjc(F)V

    return-void
.end method

.method public setTextDelegate(Lcom/bytedance/adsdk/lottie/vYf;)V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->svN:Lcom/bytedance/adsdk/lottie/BcC;

    invoke-virtual {v0, p1}, Lcom/bytedance/adsdk/lottie/BcC;->Fj(Lcom/bytedance/adsdk/lottie/vYf;)V

    return-void
.end method

.method public setUseCompositionFrameRate(Z)V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->svN:Lcom/bytedance/adsdk/lottie/BcC;

    invoke-virtual {v0, p1}, Lcom/bytedance/adsdk/lottie/BcC;->mSE(Z)V

    return-void
.end method

.method public unscheduleDrawable(Landroid/graphics/drawable/Drawable;)V
    .locals 2

    iget-boolean v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->rAx:Z

    if-nez v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->svN:Lcom/bytedance/adsdk/lottie/BcC;

    if-ne p1, v0, :cond_0

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/BcC;->rS()Z

    move-result v0

    if-eqz v0, :cond_0

    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->eV()V

    goto :goto_0

    :cond_0
    iget-boolean v0, p0, Lcom/bytedance/adsdk/lottie/LottieAnimationView;->rAx:Z

    if-nez v0, :cond_1

    instance-of v0, p1, Lcom/bytedance/adsdk/lottie/BcC;

    if-eqz v0, :cond_1

    move-object v0, p1

    check-cast v0, Lcom/bytedance/adsdk/lottie/BcC;

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/BcC;->rS()Z

    move-result v1

    if-eqz v1, :cond_1

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/BcC;->nsB()V

    :cond_1
    :goto_0
    invoke-super {p0, p1}, Landroid/widget/ImageView;->unscheduleDrawable(Landroid/graphics/drawable/Drawable;)V

    return-void
.end method
