.class final Lcom/bytedance/adsdk/lottie/Tc$Fj;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/util/Iterator;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bytedance/adsdk/lottie/Tc;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x11
    name = "Fj"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "<T:",
        "Ljava/lang/Object;",
        ">",
        "Ljava/lang/Object;",
        "Ljava/util/Iterator<",
        "TT;>;"
    }
.end annotation


# instance fields
.field final Fj:I

.field final synthetic Ubf:Lcom/bytedance/adsdk/lottie/Tc;

.field eV:Z

.field ex:I

.field hjc:I


# direct methods
.method public constructor <init>(Lcom/bytedance/adsdk/lottie/Tc;I)V
    .locals 1

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/Tc$Fj;->Ubf:Lcom/bytedance/adsdk/lottie/Tc;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, 0x0

    iput-boolean v0, p0, Lcom/bytedance/adsdk/lottie/Tc$Fj;->eV:Z

    iput p2, p0, Lcom/bytedance/adsdk/lottie/Tc$Fj;->Fj:I

    invoke-virtual {p1}, Lcom/bytedance/adsdk/lottie/Tc;->Fj()I

    move-result p1

    iput p1, p0, Lcom/bytedance/adsdk/lottie/Tc$Fj;->ex:I

    return-void
.end method


# virtual methods
.method public hasNext()Z
    .locals 2

    iget v0, p0, Lcom/bytedance/adsdk/lottie/Tc$Fj;->hjc:I

    iget v1, p0, Lcom/bytedance/adsdk/lottie/Tc$Fj;->ex:I

    if-ge v0, v1, :cond_0

    const/4 v0, 0x1

    return v0

    :cond_0
    const/4 v0, 0x0

    return v0
.end method

.method public next()Ljava/lang/Object;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()TT;"
        }
    .end annotation

    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/Tc$Fj;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Tc$Fj;->Ubf:Lcom/bytedance/adsdk/lottie/Tc;

    iget v1, p0, Lcom/bytedance/adsdk/lottie/Tc$Fj;->hjc:I

    iget v2, p0, Lcom/bytedance/adsdk/lottie/Tc$Fj;->Fj:I

    invoke-virtual {v0, v1, v2}, Lcom/bytedance/adsdk/lottie/Tc;->Fj(II)Ljava/lang/Object;

    move-result-object v0

    iget v1, p0, Lcom/bytedance/adsdk/lottie/Tc$Fj;->hjc:I

    const/4 v2, 0x1

    add-int/2addr v1, v2

    iput v1, p0, Lcom/bytedance/adsdk/lottie/Tc$Fj;->hjc:I

    iput-boolean v2, p0, Lcom/bytedance/adsdk/lottie/Tc$Fj;->eV:Z

    return-object v0

    :cond_0
    new-instance v0, Ljava/util/NoSuchElementException;

    invoke-direct {v0}, Ljava/util/NoSuchElementException;-><init>()V

    throw v0
.end method

.method public remove()V
    .locals 2

    iget-boolean v0, p0, Lcom/bytedance/adsdk/lottie/Tc$Fj;->eV:Z

    if-eqz v0, :cond_0

    iget v0, p0, Lcom/bytedance/adsdk/lottie/Tc$Fj;->hjc:I

    add-int/lit8 v0, v0, -0x1

    iput v0, p0, Lcom/bytedance/adsdk/lottie/Tc$Fj;->hjc:I

    iget v1, p0, Lcom/bytedance/adsdk/lottie/Tc$Fj;->ex:I

    add-int/lit8 v1, v1, -0x1

    iput v1, p0, Lcom/bytedance/adsdk/lottie/Tc$Fj;->ex:I

    const/4 v1, 0x0

    iput-boolean v1, p0, Lcom/bytedance/adsdk/lottie/Tc$Fj;->eV:Z

    iget-object v1, p0, Lcom/bytedance/adsdk/lottie/Tc$Fj;->Ubf:Lcom/bytedance/adsdk/lottie/Tc;

    invoke-virtual {v1, v0}, Lcom/bytedance/adsdk/lottie/Tc;->Fj(I)V

    return-void

    :cond_0
    new-instance v0, Ljava/lang/IllegalStateException;

    invoke-direct {v0}, Ljava/lang/IllegalStateException;-><init>()V

    throw v0
.end method
