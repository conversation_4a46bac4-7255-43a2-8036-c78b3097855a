.class public Lcom/bytedance/adsdk/lottie/Ubf/Ko;
.super Ljava/lang/Object;


# instance fields
.field private final Fj:Lcom/bytedance/adsdk/lottie/hjc/Fj/Fj;

.field private final Ubf:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

.field private final eV:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

.field private final ex:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

.field private final hjc:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;


# direct methods
.method public constructor <init>(Lcom/bytedance/adsdk/lottie/hjc/Fj/Fj;Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/Ubf/Ko;->Fj:Lcom/bytedance/adsdk/lottie/hjc/Fj/Fj;

    iput-object p2, p0, Lcom/bytedance/adsdk/lottie/Ubf/Ko;->ex:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

    iput-object p3, p0, Lcom/bytedance/adsdk/lottie/Ubf/Ko;->hjc:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

    iput-object p4, p0, Lcom/bytedance/adsdk/lottie/Ubf/Ko;->eV:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

    iput-object p5, p0, Lcom/bytedance/adsdk/lottie/Ubf/Ko;->Ubf:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

    return-void
.end method


# virtual methods
.method public Fj()Lcom/bytedance/adsdk/lottie/hjc/Fj/Fj;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Ubf/Ko;->Fj:Lcom/bytedance/adsdk/lottie/hjc/Fj/Fj;

    return-object v0
.end method

.method public Ubf()Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Ubf/Ko;->Ubf:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

    return-object v0
.end method

.method public eV()Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Ubf/Ko;->eV:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

    return-object v0
.end method

.method public ex()Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Ubf/Ko;->ex:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

    return-object v0
.end method

.method public hjc()Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/Ubf/Ko;->hjc:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

    return-object v0
.end method
