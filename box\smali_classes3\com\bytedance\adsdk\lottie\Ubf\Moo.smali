.class Lcom/bytedance/adsdk/lottie/Ubf/Moo;
.super Ljava/lang/Object;


# direct methods
.method public static Fj(Landroid/util/JsonReader;Lcom/bytedance/adsdk/lottie/WR;)Lcom/bytedance/adsdk/lottie/Fj/ex/mSE;
    .locals 7
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    invoke-virtual {p0}, Landroid/util/JsonReader;->peek()Landroid/util/JsonToken;

    move-result-object v0

    sget-object v1, Landroid/util/JsonToken;->BEGIN_OBJECT:Landroid/util/JsonToken;

    if-ne v0, v1, :cond_0

    const/4 v0, 0x1

    const/4 v5, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    const/4 v5, 0x0

    :goto_0
    invoke-static {}, Lcom/bytedance/adsdk/lottie/WR/WR;->Fj()F

    move-result v3

    sget-object v4, Lcom/bytedance/adsdk/lottie/Ubf/rf;->Fj:Lcom/bytedance/adsdk/lottie/Ubf/rf;

    const/4 v6, 0x0

    move-object v1, p0

    move-object v2, p1

    invoke-static/range {v1 .. v6}, Lcom/bytedance/adsdk/lottie/Ubf/mE;->Fj(Landroid/util/JsonReader;Lcom/bytedance/adsdk/lottie/WR;FLcom/bytedance/adsdk/lottie/Ubf/mj;ZZ)Lcom/bytedance/adsdk/lottie/svN/Fj;

    move-result-object p0

    new-instance v0, Lcom/bytedance/adsdk/lottie/Fj/ex/mSE;

    invoke-direct {v0, p1, p0}, Lcom/bytedance/adsdk/lottie/Fj/ex/mSE;-><init>(Lcom/bytedance/adsdk/lottie/WR;Lcom/bytedance/adsdk/lottie/svN/Fj;)V

    return-object v0
.end method
