.class public Lcom/bytedance/adsdk/lottie/Ubf/eV;
.super Ljava/lang/Object;


# direct methods
.method public static Fj(Landroid/util/JsonReader;Lcom/bytedance/adsdk/lottie/WR;)Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    const/4 v0, 0x1

    invoke-static {p0, p1, v0}, Lcom/bytedance/adsdk/lottie/Ubf/eV;->Fj(Landroid/util/JsonReader;Lcom/bytedance/adsdk/lottie/WR;Z)Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

    move-result-object p0

    return-object p0
.end method

.method public static Fj(Landroid/util/JsonReader;Lcom/bytedance/adsdk/lottie/WR;Z)Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    new-instance v0, Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

    if-eqz p2, :cond_0

    invoke-static {}, Lcom/bytedance/adsdk/lottie/WR/WR;->Fj()F

    move-result p2

    goto :goto_0

    :cond_0
    const/high16 p2, 0x3f800000    # 1.0f

    :goto_0
    sget-object v1, Lcom/bytedance/adsdk/lottie/Ubf/UYd;->Fj:Lcom/bytedance/adsdk/lottie/Ubf/UYd;

    invoke-static {p0, p2, p1, v1}, Lcom/bytedance/adsdk/lottie/Ubf/eV;->Fj(Landroid/util/JsonReader;FLcom/bytedance/adsdk/lottie/WR;Lcom/bytedance/adsdk/lottie/Ubf/mj;)Ljava/util/List;

    move-result-object p0

    invoke-direct {v0, p0}, Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;-><init>(Ljava/util/List;)V

    return-object v0
.end method

.method public static Fj(Landroid/util/JsonReader;Lcom/bytedance/adsdk/lottie/WR;I)Lcom/bytedance/adsdk/lottie/hjc/Fj/hjc;
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    new-instance v0, Lcom/bytedance/adsdk/lottie/hjc/Fj/hjc;

    new-instance v1, Lcom/bytedance/adsdk/lottie/Ubf/JW;

    invoke-direct {v1, p2}, Lcom/bytedance/adsdk/lottie/Ubf/JW;-><init>(I)V

    invoke-static {p0, p1, v1}, Lcom/bytedance/adsdk/lottie/Ubf/eV;->Fj(Landroid/util/JsonReader;Lcom/bytedance/adsdk/lottie/WR;Lcom/bytedance/adsdk/lottie/Ubf/mj;)Ljava/util/List;

    move-result-object p0

    invoke-direct {v0, p0}, Lcom/bytedance/adsdk/lottie/hjc/Fj/hjc;-><init>(Ljava/util/List;)V

    return-object v0
.end method

.method private static Fj(Landroid/util/JsonReader;FLcom/bytedance/adsdk/lottie/WR;Lcom/bytedance/adsdk/lottie/Ubf/mj;)Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">(",
            "Landroid/util/JsonReader;",
            "F",
            "Lcom/bytedance/adsdk/lottie/WR;",
            "Lcom/bytedance/adsdk/lottie/Ubf/mj<",
            "TT;>;)",
            "Ljava/util/List<",
            "Lcom/bytedance/adsdk/lottie/svN/Fj<",
            "TT;>;>;"
        }
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    const/4 v0, 0x0

    invoke-static {p0, p2, p1, p3, v0}, Lcom/bytedance/adsdk/lottie/Ubf/Af;->Fj(Landroid/util/JsonReader;Lcom/bytedance/adsdk/lottie/WR;FLcom/bytedance/adsdk/lottie/Ubf/mj;Z)Ljava/util/List;

    move-result-object p0

    return-object p0
.end method

.method private static Fj(Landroid/util/JsonReader;Lcom/bytedance/adsdk/lottie/WR;Lcom/bytedance/adsdk/lottie/Ubf/mj;)Ljava/util/List;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">(",
            "Landroid/util/JsonReader;",
            "Lcom/bytedance/adsdk/lottie/WR;",
            "Lcom/bytedance/adsdk/lottie/Ubf/mj<",
            "TT;>;)",
            "Ljava/util/List<",
            "Lcom/bytedance/adsdk/lottie/svN/Fj<",
            "TT;>;>;"
        }
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    const/high16 v0, 0x3f800000    # 1.0f

    const/4 v1, 0x0

    invoke-static {p0, p1, v0, p2, v1}, Lcom/bytedance/adsdk/lottie/Ubf/Af;->Fj(Landroid/util/JsonReader;Lcom/bytedance/adsdk/lottie/WR;FLcom/bytedance/adsdk/lottie/Ubf/mj;Z)Ljava/util/List;

    move-result-object p0

    return-object p0
.end method

.method public static Ubf(Landroid/util/JsonReader;Lcom/bytedance/adsdk/lottie/WR;)Lcom/bytedance/adsdk/lottie/hjc/Fj/BcC;
    .locals 3
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    new-instance v0, Lcom/bytedance/adsdk/lottie/hjc/Fj/BcC;

    invoke-static {}, Lcom/bytedance/adsdk/lottie/WR/WR;->Fj()F

    move-result v1

    sget-object v2, Lcom/bytedance/adsdk/lottie/Ubf/gXF;->Fj:Lcom/bytedance/adsdk/lottie/Ubf/gXF;

    invoke-static {p0, v1, p1, v2}, Lcom/bytedance/adsdk/lottie/Ubf/eV;->Fj(Landroid/util/JsonReader;FLcom/bytedance/adsdk/lottie/WR;Lcom/bytedance/adsdk/lottie/Ubf/mj;)Ljava/util/List;

    move-result-object p0

    invoke-direct {v0, p0}, Lcom/bytedance/adsdk/lottie/hjc/Fj/BcC;-><init>(Ljava/util/List;)V

    return-object v0
.end method

.method public static WR(Landroid/util/JsonReader;Lcom/bytedance/adsdk/lottie/WR;)Lcom/bytedance/adsdk/lottie/hjc/Fj/Ko;
    .locals 3
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    new-instance v0, Lcom/bytedance/adsdk/lottie/hjc/Fj/Ko;

    invoke-static {}, Lcom/bytedance/adsdk/lottie/WR/WR;->Fj()F

    move-result v1

    sget-object v2, Lcom/bytedance/adsdk/lottie/Ubf/mSE;->Fj:Lcom/bytedance/adsdk/lottie/Ubf/mSE;

    invoke-static {p0, v1, p1, v2}, Lcom/bytedance/adsdk/lottie/Ubf/eV;->Fj(Landroid/util/JsonReader;FLcom/bytedance/adsdk/lottie/WR;Lcom/bytedance/adsdk/lottie/Ubf/mj;)Ljava/util/List;

    move-result-object p0

    invoke-direct {v0, p0}, Lcom/bytedance/adsdk/lottie/hjc/Fj/Ko;-><init>(Ljava/util/List;)V

    return-object v0
.end method

.method public static eV(Landroid/util/JsonReader;Lcom/bytedance/adsdk/lottie/WR;)Lcom/bytedance/adsdk/lottie/hjc/Fj/svN;
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    new-instance v0, Lcom/bytedance/adsdk/lottie/hjc/Fj/svN;

    sget-object v1, Lcom/bytedance/adsdk/lottie/Ubf/uM;->Fj:Lcom/bytedance/adsdk/lottie/Ubf/uM;

    invoke-static {p0, p1, v1}, Lcom/bytedance/adsdk/lottie/Ubf/eV;->Fj(Landroid/util/JsonReader;Lcom/bytedance/adsdk/lottie/WR;Lcom/bytedance/adsdk/lottie/Ubf/mj;)Ljava/util/List;

    move-result-object p0

    invoke-direct {v0, p0}, Lcom/bytedance/adsdk/lottie/hjc/Fj/svN;-><init>(Ljava/util/List;)V

    return-object v0
.end method

.method public static ex(Landroid/util/JsonReader;Lcom/bytedance/adsdk/lottie/WR;)Lcom/bytedance/adsdk/lottie/hjc/Fj/eV;
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    new-instance v0, Lcom/bytedance/adsdk/lottie/hjc/Fj/eV;

    sget-object v1, Lcom/bytedance/adsdk/lottie/Ubf/rS;->Fj:Lcom/bytedance/adsdk/lottie/Ubf/rS;

    invoke-static {p0, p1, v1}, Lcom/bytedance/adsdk/lottie/Ubf/eV;->Fj(Landroid/util/JsonReader;Lcom/bytedance/adsdk/lottie/WR;Lcom/bytedance/adsdk/lottie/Ubf/mj;)Ljava/util/List;

    move-result-object p0

    invoke-direct {v0, p0}, Lcom/bytedance/adsdk/lottie/hjc/Fj/eV;-><init>(Ljava/util/List;)V

    return-object v0
.end method

.method public static hjc(Landroid/util/JsonReader;Lcom/bytedance/adsdk/lottie/WR;)Lcom/bytedance/adsdk/lottie/hjc/Fj/WR;
    .locals 4
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    new-instance v0, Lcom/bytedance/adsdk/lottie/hjc/Fj/WR;

    invoke-static {}, Lcom/bytedance/adsdk/lottie/WR/WR;->Fj()F

    move-result v1

    sget-object v2, Lcom/bytedance/adsdk/lottie/Ubf/uy;->Fj:Lcom/bytedance/adsdk/lottie/Ubf/uy;

    const/4 v3, 0x1

    invoke-static {p0, p1, v1, v2, v3}, Lcom/bytedance/adsdk/lottie/Ubf/Af;->Fj(Landroid/util/JsonReader;Lcom/bytedance/adsdk/lottie/WR;FLcom/bytedance/adsdk/lottie/Ubf/mj;Z)Ljava/util/List;

    move-result-object p0

    invoke-direct {v0, p0}, Lcom/bytedance/adsdk/lottie/hjc/Fj/WR;-><init>(Ljava/util/List;)V

    return-object v0
.end method

.method public static svN(Landroid/util/JsonReader;Lcom/bytedance/adsdk/lottie/WR;)Lcom/bytedance/adsdk/lottie/hjc/Fj/Fj;
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    new-instance v0, Lcom/bytedance/adsdk/lottie/hjc/Fj/Fj;

    sget-object v1, Lcom/bytedance/adsdk/lottie/Ubf/svN;->Fj:Lcom/bytedance/adsdk/lottie/Ubf/svN;

    invoke-static {p0, p1, v1}, Lcom/bytedance/adsdk/lottie/Ubf/eV;->Fj(Landroid/util/JsonReader;Lcom/bytedance/adsdk/lottie/WR;Lcom/bytedance/adsdk/lottie/Ubf/mj;)Ljava/util/List;

    move-result-object p0

    invoke-direct {v0, p0}, Lcom/bytedance/adsdk/lottie/hjc/Fj/Fj;-><init>(Ljava/util/List;)V

    return-object v0
.end method
