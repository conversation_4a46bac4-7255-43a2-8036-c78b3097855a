.class public Lcom/bytedance/adsdk/lottie/Ubf/rf;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/adsdk/lottie/Ubf/mj;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Lcom/bytedance/adsdk/lottie/Ubf/mj<",
        "Landroid/graphics/PointF;",
        ">;"
    }
.end annotation


# static fields
.field public static final Fj:Lcom/bytedance/adsdk/lottie/Ubf/rf;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    new-instance v0, Lcom/bytedance/adsdk/lottie/Ubf/rf;

    invoke-direct {v0}, Lcom/bytedance/adsdk/lottie/Ubf/rf;-><init>()V

    sput-object v0, Lcom/bytedance/adsdk/lottie/Ubf/rf;->Fj:Lcom/bytedance/adsdk/lottie/Ubf/rf;

    return-void
.end method

.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public Fj(Landroid/util/JsonReader;F)Landroid/graphics/PointF;
    .locals 0
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    invoke-static {p1, p2}, Lcom/bytedance/adsdk/lottie/Ubf/vYf;->ex(Landroid/util/JsonReader;F)Landroid/graphics/PointF;

    move-result-object p1

    return-object p1
.end method

.method public synthetic ex(Landroid/util/JsonReader;F)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    invoke-virtual {p0, p1, p2}, Lcom/bytedance/adsdk/lottie/Ubf/rf;->Fj(Landroid/util/JsonReader;F)Landroid/graphics/PointF;

    move-result-object p1

    return-object p1
.end method
