.class public Lcom/bytedance/adsdk/lottie/WR;
.super Ljava/lang/Object;


# instance fields
.field private BcC:Landroid/util/LongSparseArray;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Landroid/util/LongSparseArray<",
            "Lcom/bytedance/adsdk/lottie/hjc/hjc/eV;",
            ">;"
        }
    .end annotation
.end field

.field private final Fj:Lcom/bytedance/adsdk/lottie/Ql;

.field private JW:I

.field private Ko:Landroid/graphics/Rect;

.field private Tc:Z

.field private UYd:F

.field private Ubf:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Lcom/bytedance/adsdk/lottie/hjc/hjc;",
            ">;"
        }
    .end annotation
.end field

.field private WR:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lcom/bytedance/adsdk/lottie/hjc/WR;",
            ">;"
        }
    .end annotation
.end field

.field private dG:F

.field private eV:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Lcom/bytedance/adsdk/lottie/mSE;",
            ">;"
        }
    .end annotation
.end field

.field private final ex:Ljava/util/HashSet;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/HashSet<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field private hjc:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/util/List<",
            "Lcom/bytedance/adsdk/lottie/hjc/hjc/eV;",
            ">;>;"
        }
    .end annotation
.end field

.field private mSE:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lcom/bytedance/adsdk/lottie/hjc/hjc/eV;",
            ">;"
        }
    .end annotation
.end field

.field private rAx:F

.field private svN:Landroid/util/SparseArray;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Landroid/util/SparseArray<",
            "Lcom/bytedance/adsdk/lottie/hjc/eV;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>()V
    .locals 1

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    new-instance v0, Lcom/bytedance/adsdk/lottie/Ql;

    invoke-direct {v0}, Lcom/bytedance/adsdk/lottie/Ql;-><init>()V

    iput-object v0, p0, Lcom/bytedance/adsdk/lottie/WR;->Fj:Lcom/bytedance/adsdk/lottie/Ql;

    new-instance v0, Ljava/util/HashSet;

    invoke-direct {v0}, Ljava/util/HashSet;-><init>()V

    iput-object v0, p0, Lcom/bytedance/adsdk/lottie/WR;->ex:Ljava/util/HashSet;

    const/4 v0, 0x0

    iput v0, p0, Lcom/bytedance/adsdk/lottie/WR;->JW:I

    return-void
.end method


# virtual methods
.method public BcC()F
    .locals 1

    iget v0, p0, Lcom/bytedance/adsdk/lottie/WR;->dG:F

    return v0
.end method

.method public Fj(F)F
    .locals 2

    iget v0, p0, Lcom/bytedance/adsdk/lottie/WR;->rAx:F

    iget v1, p0, Lcom/bytedance/adsdk/lottie/WR;->UYd:F

    invoke-static {v0, v1, p1}, Lcom/bytedance/adsdk/lottie/WR/Ubf;->Fj(FFF)F

    move-result p1

    return p1
.end method

.method public Fj(J)Lcom/bytedance/adsdk/lottie/hjc/hjc/eV;
    .locals 1
    .annotation build Lcom/bytedance/component/sdk/annotation/RestrictTo;
        value = {
            .enum Lcom/bytedance/component/sdk/annotation/RestrictTo$Scope;->LIBRARY:Lcom/bytedance/component/sdk/annotation/RestrictTo$Scope;
        }
    .end annotation

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/WR;->BcC:Landroid/util/LongSparseArray;

    invoke-virtual {v0, p1, p2}, Landroid/util/LongSparseArray;->get(J)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV;

    return-object p1
.end method

.method public Fj(I)V
    .locals 1
    .annotation build Lcom/bytedance/component/sdk/annotation/RestrictTo;
        value = {
            .enum Lcom/bytedance/component/sdk/annotation/RestrictTo$Scope;->LIBRARY:Lcom/bytedance/component/sdk/annotation/RestrictTo$Scope;
        }
    .end annotation

    iget v0, p0, Lcom/bytedance/adsdk/lottie/WR;->JW:I

    add-int/2addr v0, p1

    iput v0, p0, Lcom/bytedance/adsdk/lottie/WR;->JW:I

    return-void
.end method

.method public Fj(Landroid/graphics/Rect;FFFLjava/util/List;Landroid/util/LongSparseArray;Ljava/util/Map;Ljava/util/Map;Landroid/util/SparseArray;Ljava/util/Map;Ljava/util/List;)V
    .locals 0
    .annotation build Lcom/bytedance/component/sdk/annotation/RestrictTo;
        value = {
            .enum Lcom/bytedance/component/sdk/annotation/RestrictTo$Scope;->LIBRARY:Lcom/bytedance/component/sdk/annotation/RestrictTo$Scope;
        }
    .end annotation

    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/graphics/Rect;",
            "FFF",
            "Ljava/util/List<",
            "Lcom/bytedance/adsdk/lottie/hjc/hjc/eV;",
            ">;",
            "Landroid/util/LongSparseArray<",
            "Lcom/bytedance/adsdk/lottie/hjc/hjc/eV;",
            ">;",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/util/List<",
            "Lcom/bytedance/adsdk/lottie/hjc/hjc/eV;",
            ">;>;",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Lcom/bytedance/adsdk/lottie/mSE;",
            ">;",
            "Landroid/util/SparseArray<",
            "Lcom/bytedance/adsdk/lottie/hjc/eV;",
            ">;",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Lcom/bytedance/adsdk/lottie/hjc/hjc;",
            ">;",
            "Ljava/util/List<",
            "Lcom/bytedance/adsdk/lottie/hjc/WR;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/WR;->Ko:Landroid/graphics/Rect;

    iput p2, p0, Lcom/bytedance/adsdk/lottie/WR;->rAx:F

    iput p3, p0, Lcom/bytedance/adsdk/lottie/WR;->UYd:F

    iput p4, p0, Lcom/bytedance/adsdk/lottie/WR;->dG:F

    iput-object p5, p0, Lcom/bytedance/adsdk/lottie/WR;->mSE:Ljava/util/List;

    iput-object p6, p0, Lcom/bytedance/adsdk/lottie/WR;->BcC:Landroid/util/LongSparseArray;

    iput-object p7, p0, Lcom/bytedance/adsdk/lottie/WR;->hjc:Ljava/util/Map;

    iput-object p8, p0, Lcom/bytedance/adsdk/lottie/WR;->eV:Ljava/util/Map;

    iput-object p9, p0, Lcom/bytedance/adsdk/lottie/WR;->svN:Landroid/util/SparseArray;

    iput-object p10, p0, Lcom/bytedance/adsdk/lottie/WR;->Ubf:Ljava/util/Map;

    iput-object p11, p0, Lcom/bytedance/adsdk/lottie/WR;->WR:Ljava/util/List;

    return-void
.end method

.method public Fj(Ljava/lang/String;)V
    .locals 1
    .annotation build Lcom/bytedance/component/sdk/annotation/RestrictTo;
        value = {
            .enum Lcom/bytedance/component/sdk/annotation/RestrictTo$Scope;->LIBRARY:Lcom/bytedance/component/sdk/annotation/RestrictTo$Scope;
        }
    .end annotation

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/WR;->ex:Ljava/util/HashSet;

    invoke-virtual {v0, p1}, Ljava/util/HashSet;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public Fj(Z)V
    .locals 0
    .annotation build Lcom/bytedance/component/sdk/annotation/RestrictTo;
        value = {
            .enum Lcom/bytedance/component/sdk/annotation/RestrictTo$Scope;->LIBRARY:Lcom/bytedance/component/sdk/annotation/RestrictTo$Scope;
        }
    .end annotation

    iput-boolean p1, p0, Lcom/bytedance/adsdk/lottie/WR;->Tc:Z

    return-void
.end method

.method public Fj()Z
    .locals 1
    .annotation build Lcom/bytedance/component/sdk/annotation/RestrictTo;
        value = {
            .enum Lcom/bytedance/component/sdk/annotation/RestrictTo$Scope;->LIBRARY:Lcom/bytedance/component/sdk/annotation/RestrictTo$Scope;
        }
    .end annotation

    iget-boolean v0, p0, Lcom/bytedance/adsdk/lottie/WR;->Tc:Z

    return v0
.end method

.method public Ko()Landroid/util/SparseArray;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Landroid/util/SparseArray<",
            "Lcom/bytedance/adsdk/lottie/hjc/eV;",
            ">;"
        }
    .end annotation

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/WR;->svN:Landroid/util/SparseArray;

    return-object v0
.end method

.method public UYd()Ljava/util/Map;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Lcom/bytedance/adsdk/lottie/mSE;",
            ">;"
        }
    .end annotation

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/WR;->eV:Ljava/util/Map;

    return-object v0
.end method

.method public Ubf()F
    .locals 2

    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/WR;->dG()F

    move-result v0

    iget v1, p0, Lcom/bytedance/adsdk/lottie/WR;->dG:F

    div-float/2addr v0, v1

    const/high16 v1, 0x447a0000    # 1000.0f

    mul-float v0, v0, v1

    float-to-long v0, v0

    long-to-float v0, v0

    return v0
.end method

.method public WR()F
    .locals 1

    iget v0, p0, Lcom/bytedance/adsdk/lottie/WR;->rAx:F

    return v0
.end method

.method public dG()F
    .locals 2

    iget v0, p0, Lcom/bytedance/adsdk/lottie/WR;->UYd:F

    iget v1, p0, Lcom/bytedance/adsdk/lottie/WR;->rAx:F

    sub-float/2addr v0, v1

    return v0
.end method

.method public eV()Landroid/graphics/Rect;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/WR;->Ko:Landroid/graphics/Rect;

    return-object v0
.end method

.method public ex()I
    .locals 1
    .annotation build Lcom/bytedance/component/sdk/annotation/RestrictTo;
        value = {
            .enum Lcom/bytedance/component/sdk/annotation/RestrictTo$Scope;->LIBRARY:Lcom/bytedance/component/sdk/annotation/RestrictTo$Scope;
        }
    .end annotation

    iget v0, p0, Lcom/bytedance/adsdk/lottie/WR;->JW:I

    return v0
.end method

.method public ex(Ljava/lang/String;)Ljava/util/List;
    .locals 1
    .annotation build Lcom/bytedance/component/sdk/annotation/RestrictTo;
        value = {
            .enum Lcom/bytedance/component/sdk/annotation/RestrictTo$Scope;->LIBRARY:Lcom/bytedance/component/sdk/annotation/RestrictTo$Scope;
        }
    .end annotation

    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            ")",
            "Ljava/util/List<",
            "Lcom/bytedance/adsdk/lottie/hjc/hjc/eV;",
            ">;"
        }
    .end annotation

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/WR;->hjc:Ljava/util/Map;

    invoke-interface {v0, p1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/util/List;

    return-object p1
.end method

.method public ex(Z)V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/WR;->Fj:Lcom/bytedance/adsdk/lottie/Ql;

    invoke-virtual {v0, p1}, Lcom/bytedance/adsdk/lottie/Ql;->Fj(Z)V

    return-void
.end method

.method public hjc()Lcom/bytedance/adsdk/lottie/Ql;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/WR;->Fj:Lcom/bytedance/adsdk/lottie/Ql;

    return-object v0
.end method

.method public hjc(Ljava/lang/String;)Lcom/bytedance/adsdk/lottie/hjc/WR;
    .locals 4

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/WR;->WR:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    const/4 v1, 0x0

    :goto_0
    if-ge v1, v0, :cond_1

    iget-object v2, p0, Lcom/bytedance/adsdk/lottie/WR;->WR:Ljava/util/List;

    invoke-interface {v2, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/bytedance/adsdk/lottie/hjc/WR;

    invoke-virtual {v2, p1}, Lcom/bytedance/adsdk/lottie/hjc/WR;->Fj(Ljava/lang/String;)Z

    move-result v3

    if-eqz v3, :cond_0

    return-object v2

    :cond_0
    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_1
    const/4 p1, 0x0

    return-object p1
.end method

.method public mSE()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lcom/bytedance/adsdk/lottie/hjc/hjc/eV;",
            ">;"
        }
    .end annotation

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/WR;->mSE:Ljava/util/List;

    return-object v0
.end method

.method public rAx()Ljava/util/Map;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Lcom/bytedance/adsdk/lottie/hjc/hjc;",
            ">;"
        }
    .end annotation

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/WR;->Ubf:Ljava/util/Map;

    return-object v0
.end method

.method public svN()F
    .locals 1

    iget v0, p0, Lcom/bytedance/adsdk/lottie/WR;->UYd:F

    return v0
.end method

.method public toString()Ljava/lang/String;
    .locals 4

    new-instance v0, Ljava/lang/StringBuilder;

    const-string v1, "LottieComposition:\n"

    invoke-direct {v0, v1}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    iget-object v1, p0, Lcom/bytedance/adsdk/lottie/WR;->mSE:Ljava/util/List;

    invoke-interface {v1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :goto_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_0

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV;

    const-string v3, "\t"

    invoke-virtual {v2, v3}, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV;->Fj(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    goto :goto_0

    :cond_0
    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
