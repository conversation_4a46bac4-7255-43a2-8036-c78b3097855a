.class public Lcom/bytedance/adsdk/lottie/WR/hjc;
.super Lcom/bytedance/adsdk/lottie/WR/Fj;

# interfaces
.implements Landroid/view/Choreographer$FrameCallback;


# instance fields
.field private BcC:F

.field protected Fj:Z

.field private Ko:Lcom/bytedance/adsdk/lottie/WR;

.field private Ubf:F

.field private WR:F

.field private eV:J

.field private ex:F

.field private hjc:Z

.field private mSE:F

.field private rAx:Z

.field private svN:I


# direct methods
.method public constructor <init>()V
    .locals 3

    invoke-direct {p0}, Lcom/bytedance/adsdk/lottie/WR/Fj;-><init>()V

    const/high16 v0, 0x3f800000    # 1.0f

    iput v0, p0, Lcom/bytedance/adsdk/lottie/WR/hjc;->ex:F

    const/4 v0, 0x0

    iput-boolean v0, p0, Lcom/bytedance/adsdk/lottie/WR/hjc;->hjc:Z

    const-wide/16 v1, 0x0

    iput-wide v1, p0, Lcom/bytedance/adsdk/lottie/WR/hjc;->eV:J

    const/4 v1, 0x0

    iput v1, p0, Lcom/bytedance/adsdk/lottie/WR/hjc;->Ubf:F

    iput v1, p0, Lcom/bytedance/adsdk/lottie/WR/hjc;->WR:F

    iput v0, p0, Lcom/bytedance/adsdk/lottie/WR/hjc;->svN:I

    const/high16 v1, -0x31000000

    iput v1, p0, Lcom/bytedance/adsdk/lottie/WR/hjc;->BcC:F

    const/high16 v1, 0x4f000000

    iput v1, p0, Lcom/bytedance/adsdk/lottie/WR/hjc;->mSE:F

    iput-boolean v0, p0, Lcom/bytedance/adsdk/lottie/WR/hjc;->Fj:Z

    iput-boolean v0, p0, Lcom/bytedance/adsdk/lottie/WR/hjc;->rAx:Z

    return-void
.end method

.method private Af()V
    .locals 4

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/WR/hjc;->Ko:Lcom/bytedance/adsdk/lottie/WR;

    if-nez v0, :cond_0

    return-void

    :cond_0
    iget v0, p0, Lcom/bytedance/adsdk/lottie/WR/hjc;->WR:F

    iget v1, p0, Lcom/bytedance/adsdk/lottie/WR/hjc;->BcC:F

    cmpg-float v1, v0, v1

    if-ltz v1, :cond_1

    iget v1, p0, Lcom/bytedance/adsdk/lottie/WR/hjc;->mSE:F

    cmpl-float v0, v0, v1

    if-gtz v0, :cond_1

    return-void

    :cond_1
    new-instance v0, Ljava/lang/IllegalStateException;

    const/4 v1, 0x3

    new-array v1, v1, [Ljava/lang/Object;

    iget v2, p0, Lcom/bytedance/adsdk/lottie/WR/hjc;->BcC:F

    invoke-static {v2}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    move-result-object v2

    const/4 v3, 0x0

    aput-object v2, v1, v3

    iget v2, p0, Lcom/bytedance/adsdk/lottie/WR/hjc;->mSE:F

    invoke-static {v2}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    move-result-object v2

    const/4 v3, 0x1

    aput-object v2, v1, v3

    iget v2, p0, Lcom/bytedance/adsdk/lottie/WR/hjc;->WR:F

    invoke-static {v2}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    move-result-object v2

    const/4 v3, 0x2

    aput-object v2, v1, v3

    const-string v2, "Frame must be [%f,%f]. It is %f"

    invoke-static {v2, v1}, Ljava/lang/String;->format(Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v1

    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw v0
.end method

.method private mE()Z
    .locals 2

    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/WR/hjc;->Ko()F

    move-result v0

    const/4 v1, 0x0

    cmpg-float v0, v0, v1

    if-gez v0, :cond_0

    const/4 v0, 0x1

    return v0

    :cond_0
    const/4 v0, 0x0

    return v0
.end method

.method private vYf()F
    .locals 2

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/WR/hjc;->Ko:Lcom/bytedance/adsdk/lottie/WR;

    if-nez v0, :cond_0

    const v0, 0x7f7fffff    # Float.MAX_VALUE

    return v0

    :cond_0
    const v1, 0x4e6e6b28    # 1.0E9f

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/WR;->BcC()F

    move-result v0

    div-float/2addr v1, v0

    iget v0, p0, Lcom/bytedance/adsdk/lottie/WR/hjc;->ex:F

    invoke-static {v0}, Ljava/lang/Math;->abs(F)F

    move-result v0

    div-float/2addr v1, v0

    return v1
.end method


# virtual methods
.method public BcC()V
    .locals 1

    const/4 v0, 0x0

    iput-object v0, p0, Lcom/bytedance/adsdk/lottie/WR/hjc;->Ko:Lcom/bytedance/adsdk/lottie/WR;

    const/high16 v0, -0x31000000

    iput v0, p0, Lcom/bytedance/adsdk/lottie/WR/hjc;->BcC:F

    const/high16 v0, 0x4f000000

    iput v0, p0, Lcom/bytedance/adsdk/lottie/WR/hjc;->mSE:F

    return-void
.end method

.method public Fj(F)V
    .locals 2

    iget v0, p0, Lcom/bytedance/adsdk/lottie/WR/hjc;->Ubf:F

    cmpl-float v0, v0, p1

    if-nez v0, :cond_0

    return-void

    :cond_0
    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/WR/hjc;->JW()F

    move-result v0

    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/WR/hjc;->JU()F

    move-result v1

    invoke-static {p1, v0, v1}, Lcom/bytedance/adsdk/lottie/WR/Ubf;->ex(FFF)F

    move-result p1

    iput p1, p0, Lcom/bytedance/adsdk/lottie/WR/hjc;->Ubf:F

    iget-boolean v0, p0, Lcom/bytedance/adsdk/lottie/WR/hjc;->rAx:Z

    if-eqz v0, :cond_1

    float-to-double v0, p1

    invoke-static {v0, v1}, Ljava/lang/Math;->floor(D)D

    move-result-wide v0

    double-to-float p1, v0

    :cond_1
    iput p1, p0, Lcom/bytedance/adsdk/lottie/WR/hjc;->WR:F

    const-wide/16 v0, 0x0

    iput-wide v0, p0, Lcom/bytedance/adsdk/lottie/WR/hjc;->eV:J

    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/WR/Fj;->hjc()V

    return-void
.end method

.method public Fj(FF)V
    .locals 3

    cmpl-float v0, p1, p2

    if-gtz v0, :cond_4

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/WR/hjc;->Ko:Lcom/bytedance/adsdk/lottie/WR;

    if-nez v0, :cond_0

    const v0, -0x800001

    goto :goto_0

    :cond_0
    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/WR;->WR()F

    move-result v0

    :goto_0
    iget-object v1, p0, Lcom/bytedance/adsdk/lottie/WR/hjc;->Ko:Lcom/bytedance/adsdk/lottie/WR;

    if-nez v1, :cond_1

    const v1, 0x7f7fffff    # Float.MAX_VALUE

    goto :goto_1

    :cond_1
    invoke-virtual {v1}, Lcom/bytedance/adsdk/lottie/WR;->svN()F

    move-result v1

    :goto_1
    invoke-static {p1, v0, v1}, Lcom/bytedance/adsdk/lottie/WR/Ubf;->ex(FFF)F

    move-result p1

    invoke-static {p2, v0, v1}, Lcom/bytedance/adsdk/lottie/WR/Ubf;->ex(FFF)F

    move-result p2

    iget v0, p0, Lcom/bytedance/adsdk/lottie/WR/hjc;->BcC:F

    cmpl-float v0, p1, v0

    if-nez v0, :cond_2

    iget v0, p0, Lcom/bytedance/adsdk/lottie/WR/hjc;->mSE:F

    cmpl-float v0, p2, v0

    if-eqz v0, :cond_3

    :cond_2
    iput p1, p0, Lcom/bytedance/adsdk/lottie/WR/hjc;->BcC:F

    iput p2, p0, Lcom/bytedance/adsdk/lottie/WR/hjc;->mSE:F

    iget v0, p0, Lcom/bytedance/adsdk/lottie/WR/hjc;->WR:F

    invoke-static {v0, p1, p2}, Lcom/bytedance/adsdk/lottie/WR/Ubf;->ex(FFF)F

    move-result p1

    float-to-int p1, p1

    int-to-float p1, p1

    invoke-virtual {p0, p1}, Lcom/bytedance/adsdk/lottie/WR/hjc;->Fj(F)V

    :cond_3
    return-void

    :cond_4
    new-instance v0, Ljava/lang/IllegalArgumentException;

    const/4 v1, 0x2

    new-array v1, v1, [Ljava/lang/Object;

    const/4 v2, 0x0

    invoke-static {p1}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    move-result-object p1

    aput-object p1, v1, v2

    const/4 p1, 0x1

    invoke-static {p2}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    move-result-object p2

    aput-object p2, v1, p1

    const-string p1, "minFrame (%s) must be <= maxFrame (%s)"

    invoke-static {p1, v1}, Ljava/lang/String;->format(Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    invoke-direct {v0, p1}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw v0
.end method

.method public Fj(I)V
    .locals 1

    int-to-float p1, p1

    iget v0, p0, Lcom/bytedance/adsdk/lottie/WR/hjc;->mSE:F

    float-to-int v0, v0

    int-to-float v0, v0

    invoke-virtual {p0, p1, v0}, Lcom/bytedance/adsdk/lottie/WR/hjc;->Fj(FF)V

    return-void
.end method

.method public Fj(Lcom/bytedance/adsdk/lottie/WR;)V
    .locals 2

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/WR/hjc;->Ko:Lcom/bytedance/adsdk/lottie/WR;

    if-nez v0, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/WR/hjc;->Ko:Lcom/bytedance/adsdk/lottie/WR;

    if-eqz v0, :cond_1

    iget v0, p0, Lcom/bytedance/adsdk/lottie/WR/hjc;->BcC:F

    invoke-virtual {p1}, Lcom/bytedance/adsdk/lottie/WR;->WR()F

    move-result v1

    invoke-static {v0, v1}, Ljava/lang/Math;->max(FF)F

    move-result v0

    iget v1, p0, Lcom/bytedance/adsdk/lottie/WR/hjc;->mSE:F

    invoke-virtual {p1}, Lcom/bytedance/adsdk/lottie/WR;->svN()F

    move-result p1

    invoke-static {v1, p1}, Ljava/lang/Math;->min(FF)F

    move-result p1

    invoke-virtual {p0, v0, p1}, Lcom/bytedance/adsdk/lottie/WR/hjc;->Fj(FF)V

    goto :goto_1

    :cond_1
    invoke-virtual {p1}, Lcom/bytedance/adsdk/lottie/WR;->WR()F

    move-result v0

    float-to-int v0, v0

    int-to-float v0, v0

    invoke-virtual {p1}, Lcom/bytedance/adsdk/lottie/WR;->svN()F

    move-result p1

    float-to-int p1, p1

    int-to-float p1, p1

    invoke-virtual {p0, v0, p1}, Lcom/bytedance/adsdk/lottie/WR/hjc;->Fj(FF)V

    :goto_1
    iget p1, p0, Lcom/bytedance/adsdk/lottie/WR/hjc;->WR:F

    const/4 v0, 0x0

    iput v0, p0, Lcom/bytedance/adsdk/lottie/WR/hjc;->WR:F

    iput v0, p0, Lcom/bytedance/adsdk/lottie/WR/hjc;->Ubf:F

    float-to-int p1, p1

    int-to-float p1, p1

    invoke-virtual {p0, p1}, Lcom/bytedance/adsdk/lottie/WR/hjc;->Fj(F)V

    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/WR/Fj;->hjc()V

    return-void
.end method

.method public JU()F
    .locals 3

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/WR/hjc;->Ko:Lcom/bytedance/adsdk/lottie/WR;

    if-nez v0, :cond_0

    const/4 v0, 0x0

    return v0

    :cond_0
    iget v1, p0, Lcom/bytedance/adsdk/lottie/WR/hjc;->mSE:F

    const/high16 v2, 0x4f000000

    cmpl-float v2, v1, v2

    if-nez v2, :cond_1

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/WR;->svN()F

    move-result v0

    return v0

    :cond_1
    return v1
.end method

.method public JW()F
    .locals 3

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/WR/hjc;->Ko:Lcom/bytedance/adsdk/lottie/WR;

    if-nez v0, :cond_0

    const/4 v0, 0x0

    return v0

    :cond_0
    iget v1, p0, Lcom/bytedance/adsdk/lottie/WR/hjc;->BcC:F

    const/high16 v2, -0x31000000

    cmpl-float v2, v1, v2

    if-nez v2, :cond_1

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/WR;->WR()F

    move-result v0

    return v0

    :cond_1
    return v1
.end method

.method public Ko()F
    .locals 1

    iget v0, p0, Lcom/bytedance/adsdk/lottie/WR/hjc;->ex:F

    return v0
.end method

.method public Ql()V
    .locals 1

    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/WR/hjc;->isRunning()Z

    move-result v0

    if-eqz v0, :cond_0

    const/4 v0, 0x0

    invoke-virtual {p0, v0}, Lcom/bytedance/adsdk/lottie/WR/hjc;->eV(Z)V

    invoke-static {}, Landroid/view/Choreographer;->getInstance()Landroid/view/Choreographer;

    move-result-object v0

    invoke-virtual {v0, p0}, Landroid/view/Choreographer;->postFrameCallback(Landroid/view/Choreographer$FrameCallback;)V

    :cond_0
    return-void
.end method

.method public Tc()V
    .locals 2
    .annotation build Lcom/bytedance/component/sdk/annotation/MainThread;
    .end annotation

    const/4 v0, 0x1

    iput-boolean v0, p0, Lcom/bytedance/adsdk/lottie/WR/hjc;->Fj:Z

    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/WR/hjc;->Ql()V

    const-wide/16 v0, 0x0

    iput-wide v0, p0, Lcom/bytedance/adsdk/lottie/WR/hjc;->eV:J

    invoke-direct {p0}, Lcom/bytedance/adsdk/lottie/WR/hjc;->mE()Z

    move-result v0

    if-eqz v0, :cond_0

    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/WR/hjc;->svN()F

    move-result v0

    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/WR/hjc;->JW()F

    move-result v1

    cmpl-float v0, v0, v1

    if-nez v0, :cond_0

    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/WR/hjc;->JU()F

    move-result v0

    invoke-virtual {p0, v0}, Lcom/bytedance/adsdk/lottie/WR/hjc;->Fj(F)V

    goto :goto_0

    :cond_0
    invoke-direct {p0}, Lcom/bytedance/adsdk/lottie/WR/hjc;->mE()Z

    move-result v0

    if-nez v0, :cond_1

    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/WR/hjc;->svN()F

    move-result v0

    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/WR/hjc;->JU()F

    move-result v1

    cmpl-float v0, v0, v1

    if-nez v0, :cond_1

    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/WR/hjc;->JW()F

    move-result v0

    invoke-virtual {p0, v0}, Lcom/bytedance/adsdk/lottie/WR/hjc;->Fj(F)V

    :cond_1
    :goto_0
    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/WR/Fj;->Ubf()V

    return-void
.end method

.method public UYd()V
    .locals 1
    .annotation build Lcom/bytedance/component/sdk/annotation/MainThread;
    .end annotation

    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/WR/hjc;->rS()V

    invoke-direct {p0}, Lcom/bytedance/adsdk/lottie/WR/hjc;->mE()Z

    move-result v0

    invoke-virtual {p0, v0}, Lcom/bytedance/adsdk/lottie/WR/Fj;->ex(Z)V

    return-void
.end method

.method public WR()F
    .locals 3
    .annotation build Lcom/bytedance/component/sdk/annotation/FloatRange;
        from = 0.0
        to = 1.0
    .end annotation

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/WR/hjc;->Ko:Lcom/bytedance/adsdk/lottie/WR;

    if-nez v0, :cond_0

    const/4 v0, 0x0

    return v0

    :cond_0
    iget v1, p0, Lcom/bytedance/adsdk/lottie/WR/hjc;->WR:F

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/WR;->WR()F

    move-result v0

    sub-float/2addr v1, v0

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/WR/hjc;->Ko:Lcom/bytedance/adsdk/lottie/WR;

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/WR;->svN()F

    move-result v0

    iget-object v2, p0, Lcom/bytedance/adsdk/lottie/WR/hjc;->Ko:Lcom/bytedance/adsdk/lottie/WR;

    invoke-virtual {v2}, Lcom/bytedance/adsdk/lottie/WR;->WR()F

    move-result v2

    sub-float/2addr v0, v2

    div-float/2addr v1, v0

    return v1
.end method

.method public cancel()V
    .locals 0
    .annotation build Lcom/bytedance/component/sdk/annotation/MainThread;
    .end annotation

    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/WR/hjc;->ex()V

    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/WR/hjc;->rS()V

    return-void
.end method

.method public dG()V
    .locals 0
    .annotation build Lcom/bytedance/component/sdk/annotation/MainThread;
    .end annotation

    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/WR/hjc;->rS()V

    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/WR/Fj;->eV()V

    return-void
.end method

.method public doFrame(J)V
    .locals 6

    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/WR/hjc;->Ql()V

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/WR/hjc;->Ko:Lcom/bytedance/adsdk/lottie/WR;

    if-eqz v0, :cond_b

    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/WR/hjc;->isRunning()Z

    move-result v0

    if-nez v0, :cond_0

    goto/16 :goto_5

    :cond_0
    const-string v0, "LottieValueAnimator#doFrame"

    invoke-static {v0}, Lcom/bytedance/adsdk/lottie/Ubf;->Fj(Ljava/lang/String;)V

    iget-wide v1, p0, Lcom/bytedance/adsdk/lottie/WR/hjc;->eV:J

    const-wide/16 v3, 0x0

    cmp-long v5, v1, v3

    if-nez v5, :cond_1

    goto :goto_0

    :cond_1
    sub-long v3, p1, v1

    :goto_0
    invoke-direct {p0}, Lcom/bytedance/adsdk/lottie/WR/hjc;->vYf()F

    move-result v1

    long-to-float v2, v3

    div-float/2addr v2, v1

    iget v1, p0, Lcom/bytedance/adsdk/lottie/WR/hjc;->Ubf:F

    invoke-direct {p0}, Lcom/bytedance/adsdk/lottie/WR/hjc;->mE()Z

    move-result v3

    if-eqz v3, :cond_2

    neg-float v2, v2

    :cond_2
    add-float/2addr v1, v2

    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/WR/hjc;->JW()F

    move-result v2

    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/WR/hjc;->JU()F

    move-result v3

    invoke-static {v1, v2, v3}, Lcom/bytedance/adsdk/lottie/WR/Ubf;->hjc(FFF)Z

    move-result v2

    xor-int/lit8 v2, v2, 0x1

    iget v3, p0, Lcom/bytedance/adsdk/lottie/WR/hjc;->Ubf:F

    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/WR/hjc;->JW()F

    move-result v4

    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/WR/hjc;->JU()F

    move-result v5

    invoke-static {v1, v4, v5}, Lcom/bytedance/adsdk/lottie/WR/Ubf;->ex(FFF)F

    move-result v1

    iput v1, p0, Lcom/bytedance/adsdk/lottie/WR/hjc;->Ubf:F

    iget-boolean v4, p0, Lcom/bytedance/adsdk/lottie/WR/hjc;->rAx:Z

    if-eqz v4, :cond_3

    float-to-double v4, v1

    invoke-static {v4, v5}, Ljava/lang/Math;->floor(D)D

    move-result-wide v4

    double-to-float v1, v4

    :cond_3
    iput v1, p0, Lcom/bytedance/adsdk/lottie/WR/hjc;->WR:F

    iput-wide p1, p0, Lcom/bytedance/adsdk/lottie/WR/hjc;->eV:J

    iget-boolean v1, p0, Lcom/bytedance/adsdk/lottie/WR/hjc;->rAx:Z

    if-eqz v1, :cond_4

    iget v1, p0, Lcom/bytedance/adsdk/lottie/WR/hjc;->Ubf:F

    cmpl-float v1, v1, v3

    if-eqz v1, :cond_5

    :cond_4
    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/WR/Fj;->hjc()V

    :cond_5
    if-eqz v2, :cond_a

    invoke-virtual {p0}, Landroid/animation/ValueAnimator;->getRepeatCount()I

    move-result v1

    const/4 v2, -0x1

    if-eq v1, v2, :cond_7

    iget v1, p0, Lcom/bytedance/adsdk/lottie/WR/hjc;->svN:I

    invoke-virtual {p0}, Landroid/animation/ValueAnimator;->getRepeatCount()I

    move-result v2

    if-lt v1, v2, :cond_7

    iget p1, p0, Lcom/bytedance/adsdk/lottie/WR/hjc;->ex:F

    const/4 p2, 0x0

    cmpg-float p1, p1, p2

    if-gez p1, :cond_6

    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/WR/hjc;->JW()F

    move-result p1

    goto :goto_1

    :cond_6
    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/WR/hjc;->JU()F

    move-result p1

    :goto_1
    iput p1, p0, Lcom/bytedance/adsdk/lottie/WR/hjc;->Ubf:F

    iput p1, p0, Lcom/bytedance/adsdk/lottie/WR/hjc;->WR:F

    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/WR/hjc;->rS()V

    invoke-direct {p0}, Lcom/bytedance/adsdk/lottie/WR/hjc;->mE()Z

    move-result p1

    invoke-virtual {p0, p1}, Lcom/bytedance/adsdk/lottie/WR/Fj;->ex(Z)V

    goto :goto_4

    :cond_7
    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/WR/Fj;->Fj()V

    iget v1, p0, Lcom/bytedance/adsdk/lottie/WR/hjc;->svN:I

    add-int/lit8 v1, v1, 0x1

    iput v1, p0, Lcom/bytedance/adsdk/lottie/WR/hjc;->svN:I

    invoke-virtual {p0}, Landroid/animation/ValueAnimator;->getRepeatMode()I

    move-result v1

    const/4 v2, 0x2

    if-ne v1, v2, :cond_8

    iget-boolean v1, p0, Lcom/bytedance/adsdk/lottie/WR/hjc;->hjc:Z

    xor-int/lit8 v1, v1, 0x1

    iput-boolean v1, p0, Lcom/bytedance/adsdk/lottie/WR/hjc;->hjc:Z

    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/WR/hjc;->mSE()V

    goto :goto_3

    :cond_8
    invoke-direct {p0}, Lcom/bytedance/adsdk/lottie/WR/hjc;->mE()Z

    move-result v1

    if-eqz v1, :cond_9

    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/WR/hjc;->JU()F

    move-result v1

    goto :goto_2

    :cond_9
    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/WR/hjc;->JW()F

    move-result v1

    :goto_2
    iput v1, p0, Lcom/bytedance/adsdk/lottie/WR/hjc;->Ubf:F

    iput v1, p0, Lcom/bytedance/adsdk/lottie/WR/hjc;->WR:F

    :goto_3
    iput-wide p1, p0, Lcom/bytedance/adsdk/lottie/WR/hjc;->eV:J

    :cond_a
    :goto_4
    invoke-direct {p0}, Lcom/bytedance/adsdk/lottie/WR/hjc;->Af()V

    invoke-static {v0}, Lcom/bytedance/adsdk/lottie/Ubf;->ex(Ljava/lang/String;)F

    :cond_b
    :goto_5
    return-void
.end method

.method public eV(Z)V
    .locals 1
    .annotation build Lcom/bytedance/component/sdk/annotation/MainThread;
    .end annotation

    invoke-static {}, Landroid/view/Choreographer;->getInstance()Landroid/view/Choreographer;

    move-result-object v0

    invoke-virtual {v0, p0}, Landroid/view/Choreographer;->removeFrameCallback(Landroid/view/Choreographer$FrameCallback;)V

    if-eqz p1, :cond_0

    const/4 p1, 0x0

    iput-boolean p1, p0, Lcom/bytedance/adsdk/lottie/WR/hjc;->Fj:Z

    :cond_0
    return-void
.end method

.method public ex()V
    .locals 1

    invoke-super {p0}, Lcom/bytedance/adsdk/lottie/WR/Fj;->ex()V

    invoke-direct {p0}, Lcom/bytedance/adsdk/lottie/WR/hjc;->mE()Z

    move-result v0

    invoke-virtual {p0, v0}, Lcom/bytedance/adsdk/lottie/WR/Fj;->ex(Z)V

    return-void
.end method

.method public ex(F)V
    .locals 1

    iget v0, p0, Lcom/bytedance/adsdk/lottie/WR/hjc;->BcC:F

    invoke-virtual {p0, v0, p1}, Lcom/bytedance/adsdk/lottie/WR/hjc;->Fj(FF)V

    return-void
.end method

.method public getAnimatedFraction()F
    .locals 3
    .annotation build Lcom/bytedance/component/sdk/annotation/FloatRange;
        from = 0.0
        to = 1.0
    .end annotation

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/WR/hjc;->Ko:Lcom/bytedance/adsdk/lottie/WR;

    if-nez v0, :cond_0

    const/4 v0, 0x0

    return v0

    :cond_0
    invoke-direct {p0}, Lcom/bytedance/adsdk/lottie/WR/hjc;->mE()Z

    move-result v0

    if-eqz v0, :cond_1

    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/WR/hjc;->JU()F

    move-result v0

    iget v1, p0, Lcom/bytedance/adsdk/lottie/WR/hjc;->WR:F

    sub-float/2addr v0, v1

    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/WR/hjc;->JU()F

    move-result v1

    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/WR/hjc;->JW()F

    move-result v2

    :goto_0
    sub-float/2addr v1, v2

    div-float/2addr v0, v1

    return v0

    :cond_1
    iget v0, p0, Lcom/bytedance/adsdk/lottie/WR/hjc;->WR:F

    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/WR/hjc;->JW()F

    move-result v1

    sub-float/2addr v0, v1

    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/WR/hjc;->JU()F

    move-result v1

    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/WR/hjc;->JW()F

    move-result v2

    goto :goto_0
.end method

.method public getAnimatedValue()Ljava/lang/Object;
    .locals 1

    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/WR/hjc;->WR()F

    move-result v0

    invoke-static {v0}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    move-result-object v0

    return-object v0
.end method

.method public getDuration()J
    .locals 2

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/WR/hjc;->Ko:Lcom/bytedance/adsdk/lottie/WR;

    if-nez v0, :cond_0

    const-wide/16 v0, 0x0

    return-wide v0

    :cond_0
    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/WR;->Ubf()F

    move-result v0

    float-to-long v0, v0

    return-wide v0
.end method

.method public hjc(F)V
    .locals 0

    iput p1, p0, Lcom/bytedance/adsdk/lottie/WR/hjc;->ex:F

    return-void
.end method

.method public hjc(Z)V
    .locals 0

    iput-boolean p1, p0, Lcom/bytedance/adsdk/lottie/WR/hjc;->rAx:Z

    return-void
.end method

.method public isRunning()Z
    .locals 1

    iget-boolean v0, p0, Lcom/bytedance/adsdk/lottie/WR/hjc;->Fj:Z

    return v0
.end method

.method public mSE()V
    .locals 1

    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/WR/hjc;->Ko()F

    move-result v0

    neg-float v0, v0

    invoke-virtual {p0, v0}, Lcom/bytedance/adsdk/lottie/WR/hjc;->hjc(F)V

    return-void
.end method

.method public rAx()V
    .locals 2
    .annotation build Lcom/bytedance/component/sdk/annotation/MainThread;
    .end annotation

    const/4 v0, 0x1

    iput-boolean v0, p0, Lcom/bytedance/adsdk/lottie/WR/hjc;->Fj:Z

    invoke-direct {p0}, Lcom/bytedance/adsdk/lottie/WR/hjc;->mE()Z

    move-result v0

    invoke-virtual {p0, v0}, Lcom/bytedance/adsdk/lottie/WR/Fj;->Fj(Z)V

    invoke-direct {p0}, Lcom/bytedance/adsdk/lottie/WR/hjc;->mE()Z

    move-result v0

    if-eqz v0, :cond_0

    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/WR/hjc;->JU()F

    move-result v0

    goto :goto_0

    :cond_0
    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/WR/hjc;->JW()F

    move-result v0

    :goto_0
    float-to-int v0, v0

    int-to-float v0, v0

    invoke-virtual {p0, v0}, Lcom/bytedance/adsdk/lottie/WR/hjc;->Fj(F)V

    const-wide/16 v0, 0x0

    iput-wide v0, p0, Lcom/bytedance/adsdk/lottie/WR/hjc;->eV:J

    const/4 v0, 0x0

    iput v0, p0, Lcom/bytedance/adsdk/lottie/WR/hjc;->svN:I

    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/WR/hjc;->Ql()V

    return-void
.end method

.method public rS()V
    .locals 1
    .annotation build Lcom/bytedance/component/sdk/annotation/MainThread;
    .end annotation

    const/4 v0, 0x1

    invoke-virtual {p0, v0}, Lcom/bytedance/adsdk/lottie/WR/hjc;->eV(Z)V

    return-void
.end method

.method public setRepeatMode(I)V
    .locals 1

    invoke-super {p0, p1}, Landroid/animation/ValueAnimator;->setRepeatMode(I)V

    const/4 v0, 0x2

    if-eq p1, v0, :cond_0

    iget-boolean p1, p0, Lcom/bytedance/adsdk/lottie/WR/hjc;->hjc:Z

    if-eqz p1, :cond_0

    const/4 p1, 0x0

    iput-boolean p1, p0, Lcom/bytedance/adsdk/lottie/WR/hjc;->hjc:Z

    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/WR/hjc;->mSE()V

    :cond_0
    return-void
.end method

.method public svN()F
    .locals 1

    iget v0, p0, Lcom/bytedance/adsdk/lottie/WR/hjc;->WR:F

    return v0
.end method
