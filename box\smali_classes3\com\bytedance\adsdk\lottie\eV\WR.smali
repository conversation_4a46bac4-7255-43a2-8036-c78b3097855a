.class public interface abstract Lcom/bytedance/adsdk/lottie/eV/WR;
.super Ljava/lang/Object;


# virtual methods
.method public abstract Fj(Ljava/lang/String;)Lcom/bytedance/adsdk/lottie/eV/eV;
    .annotation build Lcom/bytedance/component/sdk/annotation/WorkerThread;
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation
.end method
