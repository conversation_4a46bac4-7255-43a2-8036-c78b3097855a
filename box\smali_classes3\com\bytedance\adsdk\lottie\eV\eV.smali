.class public interface abstract Lcom/bytedance/adsdk/lottie/eV/eV;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/io/Closeable;


# virtual methods
.method public abstract Fj()Z
.end method

.method public abstract eV()Ljava/lang/String;
.end method

.method public abstract ex()Ljava/io/InputStream;
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation
.end method

.method public abstract hjc()Ljava/lang/String;
.end method
