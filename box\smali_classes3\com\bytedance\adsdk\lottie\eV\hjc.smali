.class public final enum Lcom/bytedance/adsdk/lottie/eV/hjc;
.super Ljava/lang/Enum;


# annotations
.annotation build Lcom/bytedance/component/sdk/annotation/RestrictTo;
    value = {
        .enum Lcom/bytedance/component/sdk/annotation/RestrictTo$Scope;->LIBRARY:Lcom/bytedance/component/sdk/annotation/RestrictTo$Scope;
    }
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Enum<",
        "Lcom/bytedance/adsdk/lottie/eV/hjc;",
        ">;"
    }
.end annotation


# static fields
.field public static final enum Fj:Lcom/bytedance/adsdk/lottie/eV/hjc;

.field private static final synthetic eV:[Lcom/bytedance/adsdk/lottie/eV/hjc;

.field public static final enum ex:Lcom/bytedance/adsdk/lottie/eV/hjc;


# instance fields
.field public final hjc:Ljava/lang/String;


# direct methods
.method static constructor <clinit>()V
    .locals 6

    new-instance v0, Lcom/bytedance/adsdk/lottie/eV/hjc;

    const-string v1, ".json"

    const-string v2, "JSON"

    const/4 v3, 0x0

    invoke-direct {v0, v2, v3, v1}, Lcom/bytedance/adsdk/lottie/eV/hjc;-><init>(Ljava/lang/String;ILjava/lang/String;)V

    sput-object v0, Lcom/bytedance/adsdk/lottie/eV/hjc;->Fj:Lcom/bytedance/adsdk/lottie/eV/hjc;

    new-instance v1, Lcom/bytedance/adsdk/lottie/eV/hjc;

    const-string v2, ".zip"

    const-string v4, "ZIP"

    const/4 v5, 0x1

    invoke-direct {v1, v4, v5, v2}, Lcom/bytedance/adsdk/lottie/eV/hjc;-><init>(Ljava/lang/String;ILjava/lang/String;)V

    sput-object v1, Lcom/bytedance/adsdk/lottie/eV/hjc;->ex:Lcom/bytedance/adsdk/lottie/eV/hjc;

    const/4 v2, 0x2

    new-array v2, v2, [Lcom/bytedance/adsdk/lottie/eV/hjc;

    aput-object v0, v2, v3

    aput-object v1, v2, v5

    sput-object v2, Lcom/bytedance/adsdk/lottie/eV/hjc;->eV:[Lcom/bytedance/adsdk/lottie/eV/hjc;

    return-void
.end method

.method private constructor <init>(Ljava/lang/String;ILjava/lang/String;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            ")V"
        }
    .end annotation

    invoke-direct {p0, p1, p2}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    iput-object p3, p0, Lcom/bytedance/adsdk/lottie/eV/hjc;->hjc:Ljava/lang/String;

    return-void
.end method

.method public static valueOf(Ljava/lang/String;)Lcom/bytedance/adsdk/lottie/eV/hjc;
    .locals 1

    const-class v0, Lcom/bytedance/adsdk/lottie/eV/hjc;

    invoke-static {v0, p0}, Ljava/lang/Enum;->valueOf(Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Enum;

    move-result-object p0

    check-cast p0, Lcom/bytedance/adsdk/lottie/eV/hjc;

    return-object p0
.end method

.method public static values()[Lcom/bytedance/adsdk/lottie/eV/hjc;
    .locals 1

    sget-object v0, Lcom/bytedance/adsdk/lottie/eV/hjc;->eV:[Lcom/bytedance/adsdk/lottie/eV/hjc;

    invoke-virtual {v0}, [Lcom/bytedance/adsdk/lottie/eV/hjc;->clone()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [Lcom/bytedance/adsdk/lottie/eV/hjc;

    return-object v0
.end method


# virtual methods
.method public Fj()Ljava/lang/String;
    .locals 2

    new-instance v0, Ljava/lang/StringBuilder;

    const-string v1, ".temp"

    invoke-direct {v0, v1}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    iget-object v1, p0, Lcom/bytedance/adsdk/lottie/eV/hjc;->hjc:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public toString()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/eV/hjc;->hjc:Ljava/lang/String;

    return-object v0
.end method
