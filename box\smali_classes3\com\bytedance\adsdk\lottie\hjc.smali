.class public Lcom/bytedance/adsdk/lottie/hjc;
.super Ljava/lang/Object;


# virtual methods
.method public Fj(Ljava/lang/String;)Landroid/graphics/Typeface;
    .locals 0

    const/4 p1, 0x0

    return-object p1
.end method

.method public Fj(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Landroid/graphics/Typeface;
    .locals 0

    const/4 p1, 0x0

    return-object p1
.end method

.method public ex(Ljava/lang/String;)Ljava/lang/String;
    .locals 0

    const/4 p1, 0x0

    return-object p1
.end method

.method public ex(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;
    .locals 0

    const/4 p1, 0x0

    return-object p1
.end method
