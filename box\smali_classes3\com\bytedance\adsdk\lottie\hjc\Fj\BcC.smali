.class public Lcom/bytedance/adsdk/lottie/hjc/Fj/BcC;
.super Lcom/bytedance/adsdk/lottie/hjc/Fj/Tc;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lcom/bytedance/adsdk/lottie/hjc/Fj/Tc<",
        "Lcom/bytedance/adsdk/lottie/hjc/ex/Tc;",
        "Landroid/graphics/Path;",
        ">;"
    }
.end annotation


# direct methods
.method public constructor <init>(Ljava/util/List;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lcom/bytedance/adsdk/lottie/svN/Fj<",
            "Lcom/bytedance/adsdk/lottie/hjc/ex/Tc;",
            ">;>;)V"
        }
    .end annotation

    invoke-direct {p0, p1}, Lcom/bytedance/adsdk/lottie/hjc/Fj/Tc;-><init>(Ljava/util/List;)V

    return-void
.end method


# virtual methods
.method public synthetic Fj()Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;
    .locals 1

    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/hjc/Fj/BcC;->eV()Lcom/bytedance/adsdk/lottie/Fj/ex/dG;

    move-result-object v0

    return-object v0
.end method

.method public eV()Lcom/bytedance/adsdk/lottie/Fj/ex/dG;
    .locals 2

    new-instance v0, Lcom/bytedance/adsdk/lottie/Fj/ex/dG;

    iget-object v1, p0, Lcom/bytedance/adsdk/lottie/hjc/Fj/Tc;->Fj:Ljava/util/List;

    invoke-direct {v0, v1}, Lcom/bytedance/adsdk/lottie/Fj/ex/dG;-><init>(Ljava/util/List;)V

    return-object v0
.end method

.method public bridge synthetic ex()Z
    .locals 1

    invoke-super {p0}, Lcom/bytedance/adsdk/lottie/hjc/Fj/Tc;->ex()Z

    move-result v0

    return v0
.end method

.method public bridge synthetic hjc()Ljava/util/List;
    .locals 1

    invoke-super {p0}, Lcom/bytedance/adsdk/lottie/hjc/Fj/Tc;->hjc()Ljava/util/List;

    move-result-object v0

    return-object v0
.end method

.method public bridge synthetic toString()Ljava/lang/String;
    .locals 1

    invoke-super {p0}, Lcom/bytedance/adsdk/lottie/hjc/Fj/Tc;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
