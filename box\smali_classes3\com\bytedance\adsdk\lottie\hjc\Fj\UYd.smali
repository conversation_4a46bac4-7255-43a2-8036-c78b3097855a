.class public Lcom/bytedance/adsdk/lottie/hjc/Fj/UYd;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/adsdk/lottie/hjc/ex/hjc;


# instance fields
.field private final BcC:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

.field private final Fj:Lcom/bytedance/adsdk/lottie/hjc/Fj/Ubf;

.field private final Ubf:Lcom/bytedance/adsdk/lottie/hjc/Fj/eV;

.field private final WR:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

.field private final eV:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

.field private final ex:Lcom/bytedance/adsdk/lottie/hjc/Fj/dG;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/bytedance/adsdk/lottie/hjc/Fj/dG<",
            "Landroid/graphics/PointF;",
            "Landroid/graphics/PointF;",
            ">;"
        }
    .end annotation
.end field

.field private final hjc:Lcom/bytedance/adsdk/lottie/hjc/Fj/svN;

.field private final mSE:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

.field private final svN:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;


# direct methods
.method public constructor <init>()V
    .locals 10

    const/4 v1, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    const/4 v4, 0x0

    const/4 v5, 0x0

    const/4 v6, 0x0

    const/4 v7, 0x0

    const/4 v8, 0x0

    const/4 v9, 0x0

    move-object v0, p0

    invoke-direct/range {v0 .. v9}, Lcom/bytedance/adsdk/lottie/hjc/Fj/UYd;-><init>(Lcom/bytedance/adsdk/lottie/hjc/Fj/Ubf;Lcom/bytedance/adsdk/lottie/hjc/Fj/dG;Lcom/bytedance/adsdk/lottie/hjc/Fj/svN;Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;Lcom/bytedance/adsdk/lottie/hjc/Fj/eV;Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;)V

    return-void
.end method

.method public constructor <init>(Lcom/bytedance/adsdk/lottie/hjc/Fj/Ubf;Lcom/bytedance/adsdk/lottie/hjc/Fj/dG;Lcom/bytedance/adsdk/lottie/hjc/Fj/svN;Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;Lcom/bytedance/adsdk/lottie/hjc/Fj/eV;Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/bytedance/adsdk/lottie/hjc/Fj/Ubf;",
            "Lcom/bytedance/adsdk/lottie/hjc/Fj/dG<",
            "Landroid/graphics/PointF;",
            "Landroid/graphics/PointF;",
            ">;",
            "Lcom/bytedance/adsdk/lottie/hjc/Fj/svN;",
            "Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;",
            "Lcom/bytedance/adsdk/lottie/hjc/Fj/eV;",
            "Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;",
            "Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;",
            "Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;",
            "Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;",
            ")V"
        }
    .end annotation

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/hjc/Fj/UYd;->Fj:Lcom/bytedance/adsdk/lottie/hjc/Fj/Ubf;

    iput-object p2, p0, Lcom/bytedance/adsdk/lottie/hjc/Fj/UYd;->ex:Lcom/bytedance/adsdk/lottie/hjc/Fj/dG;

    iput-object p3, p0, Lcom/bytedance/adsdk/lottie/hjc/Fj/UYd;->hjc:Lcom/bytedance/adsdk/lottie/hjc/Fj/svN;

    iput-object p4, p0, Lcom/bytedance/adsdk/lottie/hjc/Fj/UYd;->eV:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

    iput-object p5, p0, Lcom/bytedance/adsdk/lottie/hjc/Fj/UYd;->Ubf:Lcom/bytedance/adsdk/lottie/hjc/Fj/eV;

    iput-object p6, p0, Lcom/bytedance/adsdk/lottie/hjc/Fj/UYd;->BcC:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

    iput-object p7, p0, Lcom/bytedance/adsdk/lottie/hjc/Fj/UYd;->mSE:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

    iput-object p8, p0, Lcom/bytedance/adsdk/lottie/hjc/Fj/UYd;->WR:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

    iput-object p9, p0, Lcom/bytedance/adsdk/lottie/hjc/Fj/UYd;->svN:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

    return-void
.end method


# virtual methods
.method public BcC()Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/Fj/UYd;->WR:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

    return-object v0
.end method

.method public Fj(Lcom/bytedance/adsdk/lottie/BcC;Lcom/bytedance/adsdk/lottie/WR;Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;)Lcom/bytedance/adsdk/lottie/Fj/Fj/hjc;
    .locals 0

    const/4 p1, 0x0

    return-object p1
.end method

.method public Fj()Lcom/bytedance/adsdk/lottie/hjc/Fj/Ubf;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/Fj/UYd;->Fj:Lcom/bytedance/adsdk/lottie/hjc/Fj/Ubf;

    return-object v0
.end method

.method public Ko()Lcom/bytedance/adsdk/lottie/Fj/ex/JU;
    .locals 1

    new-instance v0, Lcom/bytedance/adsdk/lottie/Fj/ex/JU;

    invoke-direct {v0, p0}, Lcom/bytedance/adsdk/lottie/Fj/ex/JU;-><init>(Lcom/bytedance/adsdk/lottie/hjc/Fj/UYd;)V

    return-object v0
.end method

.method public Ubf()Lcom/bytedance/adsdk/lottie/hjc/Fj/eV;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/Fj/UYd;->Ubf:Lcom/bytedance/adsdk/lottie/hjc/Fj/eV;

    return-object v0
.end method

.method public WR()Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/Fj/UYd;->BcC:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

    return-object v0
.end method

.method public eV()Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/Fj/UYd;->eV:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

    return-object v0
.end method

.method public ex()Lcom/bytedance/adsdk/lottie/hjc/Fj/dG;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lcom/bytedance/adsdk/lottie/hjc/Fj/dG<",
            "Landroid/graphics/PointF;",
            "Landroid/graphics/PointF;",
            ">;"
        }
    .end annotation

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/Fj/UYd;->ex:Lcom/bytedance/adsdk/lottie/hjc/Fj/dG;

    return-object v0
.end method

.method public hjc()Lcom/bytedance/adsdk/lottie/hjc/Fj/svN;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/Fj/UYd;->hjc:Lcom/bytedance/adsdk/lottie/hjc/Fj/svN;

    return-object v0
.end method

.method public mSE()Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/Fj/UYd;->svN:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

    return-object v0
.end method

.method public svN()Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/Fj/UYd;->mSE:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

    return-object v0
.end method
