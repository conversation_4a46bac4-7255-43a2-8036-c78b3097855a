.class public Lcom/bytedance/adsdk/lottie/hjc/Fj/mSE;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/adsdk/lottie/hjc/Fj/dG;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Lcom/bytedance/adsdk/lottie/hjc/Fj/dG<",
        "Landroid/graphics/PointF;",
        "Landroid/graphics/PointF;",
        ">;"
    }
.end annotation


# instance fields
.field private final Fj:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

.field private final ex:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;


# direct methods
.method public constructor <init>(Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/hjc/Fj/mSE;->Fj:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

    iput-object p2, p0, Lcom/bytedance/adsdk/lottie/hjc/Fj/mSE;->ex:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

    return-void
.end method


# virtual methods
.method public Fj()Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lcom/bytedance/adsdk/lottie/Fj/ex/Fj<",
            "Landroid/graphics/PointF;",
            "Landroid/graphics/PointF;",
            ">;"
        }
    .end annotation

    new-instance v0, Lcom/bytedance/adsdk/lottie/Fj/ex/Tc;

    iget-object v1, p0, Lcom/bytedance/adsdk/lottie/hjc/Fj/mSE;->Fj:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

    invoke-virtual {v1}, Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;->Fj()Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    move-result-object v1

    iget-object v2, p0, Lcom/bytedance/adsdk/lottie/hjc/Fj/mSE;->ex:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

    invoke-virtual {v2}, Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;->Fj()Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;

    move-result-object v2

    invoke-direct {v0, v1, v2}, Lcom/bytedance/adsdk/lottie/Fj/ex/Tc;-><init>(Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;Lcom/bytedance/adsdk/lottie/Fj/ex/Fj;)V

    return-object v0
.end method

.method public ex()Z
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/Fj/mSE;->Fj:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;->ex()Z

    move-result v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/Fj/mSE;->ex:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;->ex()Z

    move-result v0

    if-eqz v0, :cond_0

    const/4 v0, 0x1

    return v0

    :cond_0
    const/4 v0, 0x0

    return v0
.end method

.method public hjc()Ljava/util/List;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lcom/bytedance/adsdk/lottie/svN/Fj<",
            "Landroid/graphics/PointF;",
            ">;>;"
        }
    .end annotation

    new-instance v0, Ljava/lang/UnsupportedOperationException;

    const-string v1, "Cannot call getKeyframes on AnimatableSplitDimensionPathValue."

    invoke-direct {v0, v1}, Ljava/lang/UnsupportedOperationException;-><init>(Ljava/lang/String;)V

    throw v0
.end method
