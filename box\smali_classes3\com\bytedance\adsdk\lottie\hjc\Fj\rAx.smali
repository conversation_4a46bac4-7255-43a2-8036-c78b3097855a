.class public Lcom/bytedance/adsdk/lottie/hjc/Fj/rAx;
.super Ljava/lang/Object;


# instance fields
.field public final Fj:Lcom/bytedance/adsdk/lottie/hjc/Fj/Fj;

.field public final eV:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

.field public final ex:Lcom/bytedance/adsdk/lottie/hjc/Fj/Fj;

.field public final hjc:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;


# direct methods
.method public constructor <init>(Lcom/bytedance/adsdk/lottie/hjc/Fj/Fj;Lcom/bytedance/adsdk/lottie/hjc/Fj/Fj;Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/hjc/Fj/rAx;->Fj:Lcom/bytedance/adsdk/lottie/hjc/Fj/Fj;

    iput-object p2, p0, Lcom/bytedance/adsdk/lottie/hjc/Fj/rAx;->ex:Lcom/bytedance/adsdk/lottie/hjc/Fj/Fj;

    iput-object p3, p0, Lcom/bytedance/adsdk/lottie/hjc/Fj/rAx;->hjc:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

    iput-object p4, p0, Lcom/bytedance/adsdk/lottie/hjc/Fj/rAx;->eV:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

    return-void
.end method
