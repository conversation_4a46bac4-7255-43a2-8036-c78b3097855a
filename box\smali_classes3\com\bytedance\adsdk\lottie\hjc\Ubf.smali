.class public Lcom/bytedance/adsdk/lottie/hjc/Ubf;
.super Ljava/lang/Object;


# annotations
.annotation build Lcom/bytedance/component/sdk/annotation/RestrictTo;
    value = {
        .enum Lcom/bytedance/component/sdk/annotation/RestrictTo$Scope;->LIBRARY:Lcom/bytedance/component/sdk/annotation/RestrictTo$Scope;
    }
.end annotation


# static fields
.field private static final Fj:Lcom/bytedance/adsdk/lottie/hjc/Ubf;


# instance fields
.field private final ex:Lcom/bytedance/adsdk/lottie/dG;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/bytedance/adsdk/lottie/dG<",
            "Ljava/lang/String;",
            "Lcom/bytedance/adsdk/lottie/WR;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 1

    new-instance v0, Lcom/bytedance/adsdk/lottie/hjc/Ubf;

    invoke-direct {v0}, Lcom/bytedance/adsdk/lottie/hjc/Ubf;-><init>()V

    sput-object v0, Lcom/bytedance/adsdk/lottie/hjc/Ubf;->Fj:Lcom/bytedance/adsdk/lottie/hjc/Ubf;

    return-void
.end method

.method public constructor <init>()V
    .locals 2

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    new-instance v0, Lcom/bytedance/adsdk/lottie/dG;

    const/16 v1, 0x14

    invoke-direct {v0, v1}, Lcom/bytedance/adsdk/lottie/dG;-><init>(I)V

    iput-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/Ubf;->ex:Lcom/bytedance/adsdk/lottie/dG;

    return-void
.end method

.method public static Fj()Lcom/bytedance/adsdk/lottie/hjc/Ubf;
    .locals 1

    sget-object v0, Lcom/bytedance/adsdk/lottie/hjc/Ubf;->Fj:Lcom/bytedance/adsdk/lottie/hjc/Ubf;

    return-object v0
.end method


# virtual methods
.method public Fj(Ljava/lang/String;)Lcom/bytedance/adsdk/lottie/WR;
    .locals 1

    if-nez p1, :cond_0

    const/4 p1, 0x0

    return-object p1

    :cond_0
    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/Ubf;->ex:Lcom/bytedance/adsdk/lottie/dG;

    invoke-virtual {v0, p1}, Lcom/bytedance/adsdk/lottie/dG;->Fj(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/bytedance/adsdk/lottie/WR;

    return-object p1
.end method

.method public Fj(Ljava/lang/String;Lcom/bytedance/adsdk/lottie/WR;)V
    .locals 1

    if-nez p1, :cond_0

    return-void

    :cond_0
    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/Ubf;->ex:Lcom/bytedance/adsdk/lottie/dG;

    invoke-virtual {v0, p1, p2}, Lcom/bytedance/adsdk/lottie/dG;->Fj(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    return-void
.end method
