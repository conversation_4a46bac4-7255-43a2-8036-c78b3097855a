.class public Lcom/bytedance/adsdk/lottie/hjc/eV;
.super Ljava/lang/Object;


# annotations
.annotation build Lcom/bytedance/component/sdk/annotation/RestrictTo;
    value = {
        .enum Lcom/bytedance/component/sdk/annotation/RestrictTo$Scope;->LIBRARY:Lcom/bytedance/component/sdk/annotation/RestrictTo$Scope;
    }
.end annotation


# instance fields
.field private final Fj:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lcom/bytedance/adsdk/lottie/hjc/ex/JU;",
            ">;"
        }
    .end annotation
.end field

.field private final Ubf:Ljava/lang/String;

.field private final WR:Ljava/lang/String;

.field private final eV:D

.field private final ex:C

.field private final hjc:D


# direct methods
.method public constructor <init>(Ljava/util/List;CDDLjava/lang/String;Ljava/lang/String;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lcom/bytedance/adsdk/lottie/hjc/ex/JU;",
            ">;CDD",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ")V"
        }
    .end annotation

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/hjc/eV;->Fj:Ljava/util/List;

    iput-char p2, p0, Lcom/bytedance/adsdk/lottie/hjc/eV;->ex:C

    iput-wide p3, p0, Lcom/bytedance/adsdk/lottie/hjc/eV;->hjc:D

    iput-wide p5, p0, Lcom/bytedance/adsdk/lottie/hjc/eV;->eV:D

    iput-object p7, p0, Lcom/bytedance/adsdk/lottie/hjc/eV;->Ubf:Ljava/lang/String;

    iput-object p8, p0, Lcom/bytedance/adsdk/lottie/hjc/eV;->WR:Ljava/lang/String;

    return-void
.end method

.method public static Fj(CLjava/lang/String;Ljava/lang/String;)I
    .locals 0

    mul-int/lit8 p0, p0, 0x1f

    invoke-virtual {p1}, Ljava/lang/String;->hashCode()I

    move-result p1

    add-int/2addr p0, p1

    mul-int/lit8 p0, p0, 0x1f

    invoke-virtual {p2}, Ljava/lang/String;->hashCode()I

    move-result p1

    add-int/2addr p0, p1

    return p0
.end method


# virtual methods
.method public Fj()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lcom/bytedance/adsdk/lottie/hjc/ex/JU;",
            ">;"
        }
    .end annotation

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/eV;->Fj:Ljava/util/List;

    return-object v0
.end method

.method public ex()D
    .locals 2

    iget-wide v0, p0, Lcom/bytedance/adsdk/lottie/hjc/eV;->eV:D

    return-wide v0
.end method

.method public hashCode()I
    .locals 3

    iget-char v0, p0, Lcom/bytedance/adsdk/lottie/hjc/eV;->ex:C

    iget-object v1, p0, Lcom/bytedance/adsdk/lottie/hjc/eV;->WR:Ljava/lang/String;

    iget-object v2, p0, Lcom/bytedance/adsdk/lottie/hjc/eV;->Ubf:Ljava/lang/String;

    invoke-static {v0, v1, v2}, Lcom/bytedance/adsdk/lottie/hjc/eV;->Fj(CLjava/lang/String;Ljava/lang/String;)I

    move-result v0

    return v0
.end method
