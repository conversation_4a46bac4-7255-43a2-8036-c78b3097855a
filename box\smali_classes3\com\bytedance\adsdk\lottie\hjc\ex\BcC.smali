.class public Lcom/bytedance/adsdk/lottie/hjc/ex/BcC;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/bytedance/adsdk/lottie/hjc/ex/BcC$Fj;
    }
.end annotation


# instance fields
.field private final Fj:Lcom/bytedance/adsdk/lottie/hjc/ex/BcC$Fj;

.field private final eV:Z

.field private final ex:Lcom/bytedance/adsdk/lottie/hjc/Fj/BcC;

.field private final hjc:Lcom/bytedance/adsdk/lottie/hjc/Fj/eV;


# direct methods
.method public constructor <init>(Lcom/bytedance/adsdk/lottie/hjc/ex/BcC$Fj;Lcom/bytedance/adsdk/lottie/hjc/Fj/BcC;Lcom/bytedance/adsdk/lottie/hjc/Fj/eV;Z)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/BcC;->Fj:Lcom/bytedance/adsdk/lottie/hjc/ex/BcC$Fj;

    iput-object p2, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/BcC;->ex:Lcom/bytedance/adsdk/lottie/hjc/Fj/BcC;

    iput-object p3, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/BcC;->hjc:Lcom/bytedance/adsdk/lottie/hjc/Fj/eV;

    iput-boolean p4, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/BcC;->eV:Z

    return-void
.end method


# virtual methods
.method public Fj()Lcom/bytedance/adsdk/lottie/hjc/ex/BcC$Fj;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/BcC;->Fj:Lcom/bytedance/adsdk/lottie/hjc/ex/BcC$Fj;

    return-object v0
.end method

.method public eV()Z
    .locals 1

    iget-boolean v0, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/BcC;->eV:Z

    return v0
.end method

.method public ex()Lcom/bytedance/adsdk/lottie/hjc/Fj/BcC;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/BcC;->ex:Lcom/bytedance/adsdk/lottie/hjc/Fj/BcC;

    return-object v0
.end method

.method public hjc()Lcom/bytedance/adsdk/lottie/hjc/Fj/eV;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/BcC;->hjc:Lcom/bytedance/adsdk/lottie/hjc/Fj/eV;

    return-object v0
.end method
