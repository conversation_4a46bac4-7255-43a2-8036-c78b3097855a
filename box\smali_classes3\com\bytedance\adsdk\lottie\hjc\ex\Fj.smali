.class public Lcom/bytedance/adsdk/lottie/hjc/ex/Fj;
.super Ljava/lang/Object;


# instance fields
.field final Fj:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;


# direct methods
.method public constructor <init>(Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/Fj;->Fj:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

    return-void
.end method


# virtual methods
.method public Fj()Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/Fj;->Fj:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

    return-object v0
.end method
