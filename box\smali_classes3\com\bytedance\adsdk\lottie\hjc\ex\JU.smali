.class public Lcom/bytedance/adsdk/lottie/hjc/ex/JU;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/adsdk/lottie/hjc/ex/hjc;


# instance fields
.field private final Fj:Ljava/lang/String;

.field private final ex:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lcom/bytedance/adsdk/lottie/hjc/ex/hjc;",
            ">;"
        }
    .end annotation
.end field

.field private final hjc:Z


# direct methods
.method public constructor <init>(Ljava/lang/String;Ljava/util/List;Z)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Ljava/util/List<",
            "Lcom/bytedance/adsdk/lottie/hjc/ex/hjc;",
            ">;Z)V"
        }
    .end annotation

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/JU;->Fj:Ljava/lang/String;

    iput-object p2, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/JU;->ex:Ljava/util/List;

    iput-boolean p3, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/JU;->hjc:Z

    return-void
.end method


# virtual methods
.method public Fj(Lcom/bytedance/adsdk/lottie/BcC;Lcom/bytedance/adsdk/lottie/WR;Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;)Lcom/bytedance/adsdk/lottie/Fj/Fj/hjc;
    .locals 1

    new-instance v0, Lcom/bytedance/adsdk/lottie/Fj/Fj/eV;

    invoke-direct {v0, p1, p3, p0, p2}, Lcom/bytedance/adsdk/lottie/Fj/Fj/eV;-><init>(Lcom/bytedance/adsdk/lottie/BcC;Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;Lcom/bytedance/adsdk/lottie/hjc/ex/JU;Lcom/bytedance/adsdk/lottie/WR;)V

    return-object v0
.end method

.method public Fj()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/JU;->Fj:Ljava/lang/String;

    return-object v0
.end method

.method public ex()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lcom/bytedance/adsdk/lottie/hjc/ex/hjc;",
            ">;"
        }
    .end annotation

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/JU;->ex:Ljava/util/List;

    return-object v0
.end method

.method public hjc()Z
    .locals 1

    iget-boolean v0, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/JU;->hjc:Z

    return v0
.end method

.method public toString()Ljava/lang/String;
    .locals 2

    new-instance v0, Ljava/lang/StringBuilder;

    const-string v1, "ShapeGroup{name=\'"

    invoke-direct {v0, v1}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    iget-object v1, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/JU;->Fj:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v1, "\' Shapes: "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v1, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/JU;->ex:Ljava/util/List;

    invoke-interface {v1}, Ljava/util/List;->toArray()[Ljava/lang/Object;

    move-result-object v1

    invoke-static {v1}, Ljava/util/Arrays;->toString([Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const/16 v1, 0x7d

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
