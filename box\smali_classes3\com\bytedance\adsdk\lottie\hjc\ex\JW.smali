.class public Lcom/bytedance/adsdk/lottie/hjc/ex/JW;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/adsdk/lottie/hjc/ex/hjc;


# instance fields
.field private final Fj:Z

.field private final Ubf:Lcom/bytedance/adsdk/lottie/hjc/Fj/eV;

.field private final WR:Z

.field private final eV:Lcom/bytedance/adsdk/lottie/hjc/Fj/Fj;

.field private final ex:Landroid/graphics/Path$FillType;

.field private final hjc:Ljava/lang/String;


# direct methods
.method public constructor <init>(Ljava/lang/String;ZLandroid/graphics/Path$FillType;Lcom/bytedance/adsdk/lottie/hjc/Fj/Fj;Lcom/bytedance/adsdk/lottie/hjc/Fj/eV;Z)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/JW;->hjc:Ljava/lang/String;

    iput-boolean p2, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/JW;->Fj:Z

    iput-object p3, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/JW;->ex:Landroid/graphics/Path$FillType;

    iput-object p4, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/JW;->eV:Lcom/bytedance/adsdk/lottie/hjc/Fj/Fj;

    iput-object p5, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/JW;->Ubf:Lcom/bytedance/adsdk/lottie/hjc/Fj/eV;

    iput-boolean p6, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/JW;->WR:Z

    return-void
.end method


# virtual methods
.method public Fj(Lcom/bytedance/adsdk/lottie/BcC;Lcom/bytedance/adsdk/lottie/WR;Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;)Lcom/bytedance/adsdk/lottie/Fj/Fj/hjc;
    .locals 0

    new-instance p2, Lcom/bytedance/adsdk/lottie/Fj/Fj/svN;

    invoke-direct {p2, p1, p3, p0}, Lcom/bytedance/adsdk/lottie/Fj/Fj/svN;-><init>(Lcom/bytedance/adsdk/lottie/BcC;Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;Lcom/bytedance/adsdk/lottie/hjc/ex/JW;)V

    return-object p2
.end method

.method public Fj()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/JW;->hjc:Ljava/lang/String;

    return-object v0
.end method

.method public Ubf()Z
    .locals 1

    iget-boolean v0, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/JW;->WR:Z

    return v0
.end method

.method public eV()Landroid/graphics/Path$FillType;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/JW;->ex:Landroid/graphics/Path$FillType;

    return-object v0
.end method

.method public ex()Lcom/bytedance/adsdk/lottie/hjc/Fj/Fj;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/JW;->eV:Lcom/bytedance/adsdk/lottie/hjc/Fj/Fj;

    return-object v0
.end method

.method public hjc()Lcom/bytedance/adsdk/lottie/hjc/Fj/eV;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/JW;->Ubf:Lcom/bytedance/adsdk/lottie/hjc/Fj/eV;

    return-object v0
.end method

.method public toString()Ljava/lang/String;
    .locals 2

    new-instance v0, Ljava/lang/StringBuilder;

    const-string v1, "ShapeFill{color=, fillEnabled="

    invoke-direct {v0, v1}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    iget-boolean v1, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/JW;->Fj:Z

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    const/16 v1, 0x7d

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
