.class public final enum Lcom/bytedance/adsdk/lottie/hjc/ex/Ko$Fj;
.super Ljava/lang/Enum;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bytedance/adsdk/lottie/hjc/ex/Ko;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x4019
    name = "Fj"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Enum<",
        "Lcom/bytedance/adsdk/lottie/hjc/ex/Ko$Fj;",
        ">;"
    }
.end annotation


# static fields
.field public static final enum Fj:Lcom/bytedance/adsdk/lottie/hjc/ex/Ko$Fj;

.field private static final synthetic eV:[Lcom/bytedance/adsdk/lottie/hjc/ex/Ko$Fj;

.field public static final enum ex:Lcom/bytedance/adsdk/lottie/hjc/ex/Ko$Fj;


# instance fields
.field private final hjc:I


# direct methods
.method static constructor <clinit>()V
    .locals 6

    new-instance v0, Lcom/bytedance/adsdk/lottie/hjc/ex/Ko$Fj;

    const-string v1, "STAR"

    const/4 v2, 0x0

    const/4 v3, 0x1

    invoke-direct {v0, v1, v2, v3}, Lcom/bytedance/adsdk/lottie/hjc/ex/Ko$Fj;-><init>(Ljava/lang/String;II)V

    sput-object v0, Lcom/bytedance/adsdk/lottie/hjc/ex/Ko$Fj;->Fj:Lcom/bytedance/adsdk/lottie/hjc/ex/Ko$Fj;

    new-instance v1, Lcom/bytedance/adsdk/lottie/hjc/ex/Ko$Fj;

    const-string v4, "POLYGON"

    const/4 v5, 0x2

    invoke-direct {v1, v4, v3, v5}, Lcom/bytedance/adsdk/lottie/hjc/ex/Ko$Fj;-><init>(Ljava/lang/String;II)V

    sput-object v1, Lcom/bytedance/adsdk/lottie/hjc/ex/Ko$Fj;->ex:Lcom/bytedance/adsdk/lottie/hjc/ex/Ko$Fj;

    new-array v4, v5, [Lcom/bytedance/adsdk/lottie/hjc/ex/Ko$Fj;

    aput-object v0, v4, v2

    aput-object v1, v4, v3

    sput-object v4, Lcom/bytedance/adsdk/lottie/hjc/ex/Ko$Fj;->eV:[Lcom/bytedance/adsdk/lottie/hjc/ex/Ko$Fj;

    return-void
.end method

.method private constructor <init>(Ljava/lang/String;II)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I)V"
        }
    .end annotation

    invoke-direct {p0, p1, p2}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    iput p3, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/Ko$Fj;->hjc:I

    return-void
.end method

.method public static Fj(I)Lcom/bytedance/adsdk/lottie/hjc/ex/Ko$Fj;
    .locals 5

    invoke-static {}, Lcom/bytedance/adsdk/lottie/hjc/ex/Ko$Fj;->values()[Lcom/bytedance/adsdk/lottie/hjc/ex/Ko$Fj;

    move-result-object v0

    array-length v1, v0

    const/4 v2, 0x0

    :goto_0
    if-ge v2, v1, :cond_1

    aget-object v3, v0, v2

    iget v4, v3, Lcom/bytedance/adsdk/lottie/hjc/ex/Ko$Fj;->hjc:I

    if-ne v4, p0, :cond_0

    return-object v3

    :cond_0
    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_1
    const/4 p0, 0x0

    return-object p0
.end method

.method public static valueOf(Ljava/lang/String;)Lcom/bytedance/adsdk/lottie/hjc/ex/Ko$Fj;
    .locals 1

    const-class v0, Lcom/bytedance/adsdk/lottie/hjc/ex/Ko$Fj;

    invoke-static {v0, p0}, Ljava/lang/Enum;->valueOf(Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Enum;

    move-result-object p0

    check-cast p0, Lcom/bytedance/adsdk/lottie/hjc/ex/Ko$Fj;

    return-object p0
.end method

.method public static values()[Lcom/bytedance/adsdk/lottie/hjc/ex/Ko$Fj;
    .locals 1

    sget-object v0, Lcom/bytedance/adsdk/lottie/hjc/ex/Ko$Fj;->eV:[Lcom/bytedance/adsdk/lottie/hjc/ex/Ko$Fj;

    invoke-virtual {v0}, [Lcom/bytedance/adsdk/lottie/hjc/ex/Ko$Fj;->clone()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [Lcom/bytedance/adsdk/lottie/hjc/ex/Ko$Fj;

    return-object v0
.end method
