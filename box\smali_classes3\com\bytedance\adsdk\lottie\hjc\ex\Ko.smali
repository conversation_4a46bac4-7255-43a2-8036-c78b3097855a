.class public Lcom/bytedance/adsdk/lottie/hjc/ex/Ko;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/adsdk/lottie/hjc/ex/hjc;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/bytedance/adsdk/lottie/hjc/ex/Ko$Fj;
    }
.end annotation


# instance fields
.field private final BcC:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

.field private final Fj:Ljava/lang/String;

.field private final Ko:Z

.field private final Ubf:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

.field private final WR:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

.field private final eV:Lcom/bytedance/adsdk/lottie/hjc/Fj/dG;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/bytedance/adsdk/lottie/hjc/Fj/dG<",
            "Landroid/graphics/PointF;",
            "Landroid/graphics/PointF;",
            ">;"
        }
    .end annotation
.end field

.field private final ex:Lcom/bytedance/adsdk/lottie/hjc/ex/Ko$Fj;

.field private final hjc:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

.field private final mSE:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

.field private final rAx:Z

.field private final svN:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;


# direct methods
.method public constructor <init>(Ljava/lang/String;Lcom/bytedance/adsdk/lottie/hjc/ex/Ko$Fj;Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;Lcom/bytedance/adsdk/lottie/hjc/Fj/dG;Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;ZZ)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Lcom/bytedance/adsdk/lottie/hjc/ex/Ko$Fj;",
            "Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;",
            "Lcom/bytedance/adsdk/lottie/hjc/Fj/dG<",
            "Landroid/graphics/PointF;",
            "Landroid/graphics/PointF;",
            ">;",
            "Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;",
            "Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;",
            "Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;",
            "Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;",
            "Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;",
            "ZZ)V"
        }
    .end annotation

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/Ko;->Fj:Ljava/lang/String;

    iput-object p2, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/Ko;->ex:Lcom/bytedance/adsdk/lottie/hjc/ex/Ko$Fj;

    iput-object p3, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/Ko;->hjc:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

    iput-object p4, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/Ko;->eV:Lcom/bytedance/adsdk/lottie/hjc/Fj/dG;

    iput-object p5, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/Ko;->Ubf:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

    iput-object p6, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/Ko;->WR:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

    iput-object p7, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/Ko;->svN:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

    iput-object p8, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/Ko;->BcC:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

    iput-object p9, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/Ko;->mSE:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

    iput-boolean p10, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/Ko;->Ko:Z

    iput-boolean p11, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/Ko;->rAx:Z

    return-void
.end method


# virtual methods
.method public BcC()Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/Ko;->BcC:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

    return-object v0
.end method

.method public Fj(Lcom/bytedance/adsdk/lottie/BcC;Lcom/bytedance/adsdk/lottie/WR;Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;)Lcom/bytedance/adsdk/lottie/Fj/Fj/hjc;
    .locals 0

    new-instance p2, Lcom/bytedance/adsdk/lottie/Fj/Fj/Tc;

    invoke-direct {p2, p1, p3, p0}, Lcom/bytedance/adsdk/lottie/Fj/Fj/Tc;-><init>(Lcom/bytedance/adsdk/lottie/BcC;Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;Lcom/bytedance/adsdk/lottie/hjc/ex/Ko;)V

    return-object p2
.end method

.method public Fj()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/Ko;->Fj:Ljava/lang/String;

    return-object v0
.end method

.method public Ko()Z
    .locals 1

    iget-boolean v0, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/Ko;->Ko:Z

    return v0
.end method

.method public Ubf()Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/Ko;->Ubf:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

    return-object v0
.end method

.method public WR()Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/Ko;->WR:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

    return-object v0
.end method

.method public eV()Lcom/bytedance/adsdk/lottie/hjc/Fj/dG;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lcom/bytedance/adsdk/lottie/hjc/Fj/dG<",
            "Landroid/graphics/PointF;",
            "Landroid/graphics/PointF;",
            ">;"
        }
    .end annotation

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/Ko;->eV:Lcom/bytedance/adsdk/lottie/hjc/Fj/dG;

    return-object v0
.end method

.method public ex()Lcom/bytedance/adsdk/lottie/hjc/ex/Ko$Fj;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/Ko;->ex:Lcom/bytedance/adsdk/lottie/hjc/ex/Ko$Fj;

    return-object v0
.end method

.method public hjc()Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/Ko;->hjc:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

    return-object v0
.end method

.method public mSE()Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/Ko;->mSE:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

    return-object v0
.end method

.method public rAx()Z
    .locals 1

    iget-boolean v0, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/Ko;->rAx:Z

    return v0
.end method

.method public svN()Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/Ko;->svN:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

    return-object v0
.end method
