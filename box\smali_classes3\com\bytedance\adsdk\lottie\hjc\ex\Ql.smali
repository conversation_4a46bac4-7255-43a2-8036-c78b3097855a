.class public Lcom/bytedance/adsdk/lottie/hjc/ex/Ql;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/adsdk/lottie/hjc/ex/hjc;


# instance fields
.field private final Fj:Ljava/lang/String;

.field private final eV:Z

.field private final ex:I

.field private final hjc:Lcom/bytedance/adsdk/lottie/hjc/Fj/BcC;


# direct methods
.method public constructor <init>(Ljava/lang/String;ILcom/bytedance/adsdk/lottie/hjc/Fj/BcC;Z)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/Ql;->Fj:Ljava/lang/String;

    iput p2, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/Ql;->ex:I

    iput-object p3, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/Ql;->hjc:Lcom/bytedance/adsdk/lottie/hjc/Fj/BcC;

    iput-boolean p4, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/Ql;->eV:Z

    return-void
.end method


# virtual methods
.method public Fj(Lcom/bytedance/adsdk/lottie/BcC;Lcom/bytedance/adsdk/lottie/WR;Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;)Lcom/bytedance/adsdk/lottie/Fj/Fj/hjc;
    .locals 0

    new-instance p2, Lcom/bytedance/adsdk/lottie/Fj/Fj/rS;

    invoke-direct {p2, p1, p3, p0}, Lcom/bytedance/adsdk/lottie/Fj/Fj/rS;-><init>(Lcom/bytedance/adsdk/lottie/BcC;Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;Lcom/bytedance/adsdk/lottie/hjc/ex/Ql;)V

    return-object p2
.end method

.method public Fj()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/Ql;->Fj:Ljava/lang/String;

    return-object v0
.end method

.method public ex()Lcom/bytedance/adsdk/lottie/hjc/Fj/BcC;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/Ql;->hjc:Lcom/bytedance/adsdk/lottie/hjc/Fj/BcC;

    return-object v0
.end method

.method public hjc()Z
    .locals 1

    iget-boolean v0, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/Ql;->eV:Z

    return v0
.end method

.method public toString()Ljava/lang/String;
    .locals 2

    new-instance v0, Ljava/lang/StringBuilder;

    const-string v1, "ShapePath{name="

    invoke-direct {v0, v1}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    iget-object v1, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/Ql;->Fj:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v1, ", index="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget v1, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/Ql;->ex:I

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const/16 v1, 0x7d

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
