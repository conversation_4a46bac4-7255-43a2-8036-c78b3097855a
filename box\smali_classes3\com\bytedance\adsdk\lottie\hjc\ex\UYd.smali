.class public Lcom/bytedance/adsdk/lottie/hjc/ex/UYd;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/adsdk/lottie/hjc/ex/hjc;


# instance fields
.field private final Fj:Ljava/lang/String;

.field private final Ubf:Z

.field private final eV:Lcom/bytedance/adsdk/lottie/hjc/Fj/UYd;

.field private final ex:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

.field private final hjc:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;


# direct methods
.method public constructor <init>(Ljava/lang/String;Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;Lcom/bytedance/adsdk/lottie/hjc/Fj/UYd;Z)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/UYd;->Fj:Ljava/lang/String;

    iput-object p2, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/UYd;->ex:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

    iput-object p3, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/UYd;->hjc:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

    iput-object p4, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/UYd;->eV:Lcom/bytedance/adsdk/lottie/hjc/Fj/UYd;

    iput-boolean p5, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/UYd;->Ubf:Z

    return-void
.end method


# virtual methods
.method public Fj(Lcom/bytedance/adsdk/lottie/BcC;Lcom/bytedance/adsdk/lottie/WR;Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;)Lcom/bytedance/adsdk/lottie/Fj/Fj/hjc;
    .locals 0

    new-instance p2, Lcom/bytedance/adsdk/lottie/Fj/Fj/JU;

    invoke-direct {p2, p1, p3, p0}, Lcom/bytedance/adsdk/lottie/Fj/Fj/JU;-><init>(Lcom/bytedance/adsdk/lottie/BcC;Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;Lcom/bytedance/adsdk/lottie/hjc/ex/UYd;)V

    return-object p2
.end method

.method public Fj()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/UYd;->Fj:Ljava/lang/String;

    return-object v0
.end method

.method public Ubf()Z
    .locals 1

    iget-boolean v0, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/UYd;->Ubf:Z

    return v0
.end method

.method public eV()Lcom/bytedance/adsdk/lottie/hjc/Fj/UYd;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/UYd;->eV:Lcom/bytedance/adsdk/lottie/hjc/Fj/UYd;

    return-object v0
.end method

.method public ex()Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/UYd;->ex:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

    return-object v0
.end method

.method public hjc()Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/UYd;->hjc:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

    return-object v0
.end method
