.class public Lcom/bytedance/adsdk/lottie/hjc/ex/Ubf;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/adsdk/lottie/hjc/ex/hjc;


# instance fields
.field private final BcC:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

.field private final Fj:Lcom/bytedance/adsdk/lottie/hjc/ex/svN;

.field private final Ko:Z

.field private final Ubf:Lcom/bytedance/adsdk/lottie/hjc/Fj/WR;

.field private final WR:Lcom/bytedance/adsdk/lottie/hjc/Fj/WR;

.field private final eV:Lcom/bytedance/adsdk/lottie/hjc/Fj/eV;

.field private final ex:Landroid/graphics/Path$FillType;

.field private final hjc:Lcom/bytedance/adsdk/lottie/hjc/Fj/hjc;

.field private final mSE:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

.field private final svN:Ljava/lang/String;


# direct methods
.method public constructor <init>(Ljava/lang/String;Lcom/bytedance/adsdk/lottie/hjc/ex/svN;Landroid/graphics/Path$FillType;Lcom/bytedance/adsdk/lottie/hjc/Fj/hjc;Lcom/bytedance/adsdk/lottie/hjc/Fj/eV;Lcom/bytedance/adsdk/lottie/hjc/Fj/WR;Lcom/bytedance/adsdk/lottie/hjc/Fj/WR;Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;Z)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p2, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/Ubf;->Fj:Lcom/bytedance/adsdk/lottie/hjc/ex/svN;

    iput-object p3, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/Ubf;->ex:Landroid/graphics/Path$FillType;

    iput-object p4, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/Ubf;->hjc:Lcom/bytedance/adsdk/lottie/hjc/Fj/hjc;

    iput-object p5, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/Ubf;->eV:Lcom/bytedance/adsdk/lottie/hjc/Fj/eV;

    iput-object p6, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/Ubf;->Ubf:Lcom/bytedance/adsdk/lottie/hjc/Fj/WR;

    iput-object p7, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/Ubf;->WR:Lcom/bytedance/adsdk/lottie/hjc/Fj/WR;

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/Ubf;->svN:Ljava/lang/String;

    iput-object p8, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/Ubf;->BcC:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

    iput-object p9, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/Ubf;->mSE:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

    iput-boolean p10, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/Ubf;->Ko:Z

    return-void
.end method


# virtual methods
.method public BcC()Z
    .locals 1

    iget-boolean v0, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/Ubf;->Ko:Z

    return v0
.end method

.method public Fj(Lcom/bytedance/adsdk/lottie/BcC;Lcom/bytedance/adsdk/lottie/WR;Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;)Lcom/bytedance/adsdk/lottie/Fj/Fj/hjc;
    .locals 1

    new-instance v0, Lcom/bytedance/adsdk/lottie/Fj/Fj/BcC;

    invoke-direct {v0, p1, p2, p3, p0}, Lcom/bytedance/adsdk/lottie/Fj/Fj/BcC;-><init>(Lcom/bytedance/adsdk/lottie/BcC;Lcom/bytedance/adsdk/lottie/WR;Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;Lcom/bytedance/adsdk/lottie/hjc/ex/Ubf;)V

    return-object v0
.end method

.method public Fj()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/Ubf;->svN:Ljava/lang/String;

    return-object v0
.end method

.method public Ubf()Lcom/bytedance/adsdk/lottie/hjc/Fj/eV;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/Ubf;->eV:Lcom/bytedance/adsdk/lottie/hjc/Fj/eV;

    return-object v0
.end method

.method public WR()Lcom/bytedance/adsdk/lottie/hjc/Fj/WR;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/Ubf;->Ubf:Lcom/bytedance/adsdk/lottie/hjc/Fj/WR;

    return-object v0
.end method

.method public eV()Lcom/bytedance/adsdk/lottie/hjc/Fj/hjc;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/Ubf;->hjc:Lcom/bytedance/adsdk/lottie/hjc/Fj/hjc;

    return-object v0
.end method

.method public ex()Lcom/bytedance/adsdk/lottie/hjc/ex/svN;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/Ubf;->Fj:Lcom/bytedance/adsdk/lottie/hjc/ex/svN;

    return-object v0
.end method

.method public hjc()Landroid/graphics/Path$FillType;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/Ubf;->ex:Landroid/graphics/Path$FillType;

    return-object v0
.end method

.method public svN()Lcom/bytedance/adsdk/lottie/hjc/Fj/WR;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/Ubf;->WR:Lcom/bytedance/adsdk/lottie/hjc/Fj/WR;

    return-object v0
.end method
