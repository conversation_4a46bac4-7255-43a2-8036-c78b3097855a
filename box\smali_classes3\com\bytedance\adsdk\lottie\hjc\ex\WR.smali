.class public Lcom/bytedance/adsdk/lottie/hjc/ex/WR;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/adsdk/lottie/hjc/ex/hjc;


# instance fields
.field private final BcC:Lcom/bytedance/adsdk/lottie/hjc/ex/rS$Fj;

.field private final Fj:Ljava/lang/String;

.field private final Ko:F

.field private final UYd:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

.field private final Ubf:Lcom/bytedance/adsdk/lottie/hjc/Fj/WR;

.field private final WR:Lcom/bytedance/adsdk/lottie/hjc/Fj/WR;

.field private final dG:Z

.field private final eV:Lcom/bytedance/adsdk/lottie/hjc/Fj/eV;

.field private final ex:Lcom/bytedance/adsdk/lottie/hjc/ex/svN;

.field private final hjc:Lcom/bytedance/adsdk/lottie/hjc/Fj/hjc;

.field private final mSE:Lcom/bytedance/adsdk/lottie/hjc/ex/rS$ex;

.field private final rAx:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;",
            ">;"
        }
    .end annotation
.end field

.field private final svN:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;


# direct methods
.method public constructor <init>(Ljava/lang/String;Lcom/bytedance/adsdk/lottie/hjc/ex/svN;Lcom/bytedance/adsdk/lottie/hjc/Fj/hjc;Lcom/bytedance/adsdk/lottie/hjc/Fj/eV;Lcom/bytedance/adsdk/lottie/hjc/Fj/WR;Lcom/bytedance/adsdk/lottie/hjc/Fj/WR;Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;Lcom/bytedance/adsdk/lottie/hjc/ex/rS$Fj;Lcom/bytedance/adsdk/lottie/hjc/ex/rS$ex;FLjava/util/List;Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;Z)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Lcom/bytedance/adsdk/lottie/hjc/ex/svN;",
            "Lcom/bytedance/adsdk/lottie/hjc/Fj/hjc;",
            "Lcom/bytedance/adsdk/lottie/hjc/Fj/eV;",
            "Lcom/bytedance/adsdk/lottie/hjc/Fj/WR;",
            "Lcom/bytedance/adsdk/lottie/hjc/Fj/WR;",
            "Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;",
            "Lcom/bytedance/adsdk/lottie/hjc/ex/rS$Fj;",
            "Lcom/bytedance/adsdk/lottie/hjc/ex/rS$ex;",
            "F",
            "Ljava/util/List<",
            "Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;",
            ">;",
            "Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;",
            "Z)V"
        }
    .end annotation

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/WR;->Fj:Ljava/lang/String;

    iput-object p2, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/WR;->ex:Lcom/bytedance/adsdk/lottie/hjc/ex/svN;

    iput-object p3, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/WR;->hjc:Lcom/bytedance/adsdk/lottie/hjc/Fj/hjc;

    iput-object p4, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/WR;->eV:Lcom/bytedance/adsdk/lottie/hjc/Fj/eV;

    iput-object p5, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/WR;->Ubf:Lcom/bytedance/adsdk/lottie/hjc/Fj/WR;

    iput-object p6, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/WR;->WR:Lcom/bytedance/adsdk/lottie/hjc/Fj/WR;

    iput-object p7, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/WR;->svN:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

    iput-object p8, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/WR;->BcC:Lcom/bytedance/adsdk/lottie/hjc/ex/rS$Fj;

    iput-object p9, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/WR;->mSE:Lcom/bytedance/adsdk/lottie/hjc/ex/rS$ex;

    iput p10, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/WR;->Ko:F

    iput-object p11, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/WR;->rAx:Ljava/util/List;

    iput-object p12, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/WR;->UYd:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

    iput-boolean p13, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/WR;->dG:Z

    return-void
.end method


# virtual methods
.method public BcC()Lcom/bytedance/adsdk/lottie/hjc/ex/rS$Fj;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/WR;->BcC:Lcom/bytedance/adsdk/lottie/hjc/ex/rS$Fj;

    return-object v0
.end method

.method public Fj(Lcom/bytedance/adsdk/lottie/BcC;Lcom/bytedance/adsdk/lottie/WR;Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;)Lcom/bytedance/adsdk/lottie/Fj/Fj/hjc;
    .locals 0

    new-instance p2, Lcom/bytedance/adsdk/lottie/Fj/Fj/mSE;

    invoke-direct {p2, p1, p3, p0}, Lcom/bytedance/adsdk/lottie/Fj/Fj/mSE;-><init>(Lcom/bytedance/adsdk/lottie/BcC;Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;Lcom/bytedance/adsdk/lottie/hjc/ex/WR;)V

    return-object p2
.end method

.method public Fj()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/WR;->Fj:Ljava/lang/String;

    return-object v0
.end method

.method public Ko()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;",
            ">;"
        }
    .end annotation

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/WR;->rAx:Ljava/util/List;

    return-object v0
.end method

.method public UYd()F
    .locals 1

    iget v0, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/WR;->Ko:F

    return v0
.end method

.method public Ubf()Lcom/bytedance/adsdk/lottie/hjc/Fj/WR;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/WR;->Ubf:Lcom/bytedance/adsdk/lottie/hjc/Fj/WR;

    return-object v0
.end method

.method public WR()Lcom/bytedance/adsdk/lottie/hjc/Fj/WR;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/WR;->WR:Lcom/bytedance/adsdk/lottie/hjc/Fj/WR;

    return-object v0
.end method

.method public dG()Z
    .locals 1

    iget-boolean v0, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/WR;->dG:Z

    return v0
.end method

.method public eV()Lcom/bytedance/adsdk/lottie/hjc/Fj/eV;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/WR;->eV:Lcom/bytedance/adsdk/lottie/hjc/Fj/eV;

    return-object v0
.end method

.method public ex()Lcom/bytedance/adsdk/lottie/hjc/ex/svN;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/WR;->ex:Lcom/bytedance/adsdk/lottie/hjc/ex/svN;

    return-object v0
.end method

.method public hjc()Lcom/bytedance/adsdk/lottie/hjc/Fj/hjc;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/WR;->hjc:Lcom/bytedance/adsdk/lottie/hjc/Fj/hjc;

    return-object v0
.end method

.method public mSE()Lcom/bytedance/adsdk/lottie/hjc/ex/rS$ex;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/WR;->mSE:Lcom/bytedance/adsdk/lottie/hjc/ex/rS$ex;

    return-object v0
.end method

.method public rAx()Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/WR;->UYd:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

    return-object v0
.end method

.method public svN()Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/WR;->svN:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

    return-object v0
.end method
