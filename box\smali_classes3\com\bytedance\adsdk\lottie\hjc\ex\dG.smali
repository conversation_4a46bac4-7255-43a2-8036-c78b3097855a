.class public Lcom/bytedance/adsdk/lottie/hjc/ex/dG;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/adsdk/lottie/hjc/ex/hjc;


# instance fields
.field private final Fj:Ljava/lang/String;

.field private final ex:Lcom/bytedance/adsdk/lottie/hjc/Fj/dG;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/bytedance/adsdk/lottie/hjc/Fj/dG<",
            "Ljava/lang/Float;",
            "Ljava/lang/Float;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(Ljava/lang/String;Lcom/bytedance/adsdk/lottie/hjc/Fj/dG;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Lcom/bytedance/adsdk/lottie/hjc/Fj/dG<",
            "Ljava/lang/Float;",
            "Ljava/lang/Float;",
            ">;)V"
        }
    .end annotation

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/dG;->Fj:Ljava/lang/String;

    iput-object p2, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/dG;->ex:Lcom/bytedance/adsdk/lottie/hjc/Fj/dG;

    return-void
.end method


# virtual methods
.method public Fj(Lcom/bytedance/adsdk/lottie/BcC;Lcom/bytedance/adsdk/lottie/WR;Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;)Lcom/bytedance/adsdk/lottie/Fj/Fj/hjc;
    .locals 0

    new-instance p2, Lcom/bytedance/adsdk/lottie/Fj/Fj/Ql;

    invoke-direct {p2, p1, p3, p0}, Lcom/bytedance/adsdk/lottie/Fj/Fj/Ql;-><init>(Lcom/bytedance/adsdk/lottie/BcC;Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;Lcom/bytedance/adsdk/lottie/hjc/ex/dG;)V

    return-object p2
.end method

.method public Fj()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/dG;->Fj:Ljava/lang/String;

    return-object v0
.end method

.method public ex()Lcom/bytedance/adsdk/lottie/hjc/Fj/dG;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lcom/bytedance/adsdk/lottie/hjc/Fj/dG<",
            "Ljava/lang/Float;",
            "Ljava/lang/Float;",
            ">;"
        }
    .end annotation

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/dG;->ex:Lcom/bytedance/adsdk/lottie/hjc/Fj/dG;

    return-object v0
.end method
