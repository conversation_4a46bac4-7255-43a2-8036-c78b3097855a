.class public Lcom/bytedance/adsdk/lottie/hjc/ex/ex;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/adsdk/lottie/hjc/ex/hjc;


# instance fields
.field private final Fj:Ljava/lang/String;

.field private final Ubf:Z

.field private final eV:Z

.field private final ex:Lcom/bytedance/adsdk/lottie/hjc/Fj/dG;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/bytedance/adsdk/lottie/hjc/Fj/dG<",
            "Landroid/graphics/PointF;",
            "Landroid/graphics/PointF;",
            ">;"
        }
    .end annotation
.end field

.field private final hjc:Lcom/bytedance/adsdk/lottie/hjc/Fj/WR;


# direct methods
.method public constructor <init>(Ljava/lang/String;Lcom/bytedance/adsdk/lottie/hjc/Fj/dG;Lcom/bytedance/adsdk/lottie/hjc/Fj/WR;ZZ)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Lcom/bytedance/adsdk/lottie/hjc/Fj/dG<",
            "Landroid/graphics/PointF;",
            "Landroid/graphics/PointF;",
            ">;",
            "Lcom/bytedance/adsdk/lottie/hjc/Fj/WR;",
            "ZZ)V"
        }
    .end annotation

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/ex;->Fj:Ljava/lang/String;

    iput-object p2, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/ex;->ex:Lcom/bytedance/adsdk/lottie/hjc/Fj/dG;

    iput-object p3, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/ex;->hjc:Lcom/bytedance/adsdk/lottie/hjc/Fj/WR;

    iput-boolean p4, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/ex;->eV:Z

    iput-boolean p5, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/ex;->Ubf:Z

    return-void
.end method


# virtual methods
.method public Fj(Lcom/bytedance/adsdk/lottie/BcC;Lcom/bytedance/adsdk/lottie/WR;Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;)Lcom/bytedance/adsdk/lottie/Fj/Fj/hjc;
    .locals 0

    new-instance p2, Lcom/bytedance/adsdk/lottie/Fj/Fj/WR;

    invoke-direct {p2, p1, p3, p0}, Lcom/bytedance/adsdk/lottie/Fj/Fj/WR;-><init>(Lcom/bytedance/adsdk/lottie/BcC;Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;Lcom/bytedance/adsdk/lottie/hjc/ex/ex;)V

    return-object p2
.end method

.method public Fj()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/ex;->Fj:Ljava/lang/String;

    return-object v0
.end method

.method public Ubf()Z
    .locals 1

    iget-boolean v0, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/ex;->Ubf:Z

    return v0
.end method

.method public eV()Z
    .locals 1

    iget-boolean v0, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/ex;->eV:Z

    return v0
.end method

.method public ex()Lcom/bytedance/adsdk/lottie/hjc/Fj/dG;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lcom/bytedance/adsdk/lottie/hjc/Fj/dG<",
            "Landroid/graphics/PointF;",
            "Landroid/graphics/PointF;",
            ">;"
        }
    .end annotation

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/ex;->ex:Lcom/bytedance/adsdk/lottie/hjc/Fj/dG;

    return-object v0
.end method

.method public hjc()Lcom/bytedance/adsdk/lottie/hjc/Fj/WR;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/ex;->hjc:Lcom/bytedance/adsdk/lottie/hjc/Fj/WR;

    return-object v0
.end method
