.class public Lcom/bytedance/adsdk/lottie/hjc/ex/mSE;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/adsdk/lottie/hjc/ex/hjc;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/bytedance/adsdk/lottie/hjc/ex/mSE$Fj;
    }
.end annotation


# instance fields
.field private final Fj:Ljava/lang/String;

.field private final ex:Lcom/bytedance/adsdk/lottie/hjc/ex/mSE$Fj;

.field private final hjc:Z


# direct methods
.method public constructor <init>(Ljava/lang/String;Lcom/bytedance/adsdk/lottie/hjc/ex/mSE$Fj;Z)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/mSE;->Fj:Ljava/lang/String;

    iput-object p2, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/mSE;->ex:Lcom/bytedance/adsdk/lottie/hjc/ex/mSE$Fj;

    iput-boolean p3, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/mSE;->hjc:Z

    return-void
.end method


# virtual methods
.method public Fj(Lcom/bytedance/adsdk/lottie/BcC;Lcom/bytedance/adsdk/lottie/WR;Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;)Lcom/bytedance/adsdk/lottie/Fj/Fj/hjc;
    .locals 0

    invoke-virtual {p1}, Lcom/bytedance/adsdk/lottie/BcC;->Fj()Z

    move-result p1

    if-nez p1, :cond_0

    const/4 p1, 0x0

    return-object p1

    :cond_0
    new-instance p1, Lcom/bytedance/adsdk/lottie/Fj/Fj/UYd;

    invoke-direct {p1, p0}, Lcom/bytedance/adsdk/lottie/Fj/Fj/UYd;-><init>(Lcom/bytedance/adsdk/lottie/hjc/ex/mSE;)V

    return-object p1
.end method

.method public Fj()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/mSE;->Fj:Ljava/lang/String;

    return-object v0
.end method

.method public ex()Lcom/bytedance/adsdk/lottie/hjc/ex/mSE$Fj;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/mSE;->ex:Lcom/bytedance/adsdk/lottie/hjc/ex/mSE$Fj;

    return-object v0
.end method

.method public hjc()Z
    .locals 1

    iget-boolean v0, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/mSE;->hjc:Z

    return v0
.end method

.method public toString()Ljava/lang/String;
    .locals 2

    new-instance v0, Ljava/lang/StringBuilder;

    const-string v1, "MergePaths{mode="

    invoke-direct {v0, v1}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    iget-object v1, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/mSE;->ex:Lcom/bytedance/adsdk/lottie/hjc/ex/mSE$Fj;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const/16 v1, 0x7d

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
