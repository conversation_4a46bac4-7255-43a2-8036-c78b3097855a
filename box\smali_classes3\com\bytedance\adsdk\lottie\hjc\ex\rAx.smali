.class public Lcom/bytedance/adsdk/lottie/hjc/ex/rAx;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/adsdk/lottie/hjc/ex/hjc;


# instance fields
.field private final Fj:Ljava/lang/String;

.field private final Ubf:Z

.field private final eV:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

.field private final ex:Lcom/bytedance/adsdk/lottie/hjc/Fj/dG;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/bytedance/adsdk/lottie/hjc/Fj/dG<",
            "Landroid/graphics/PointF;",
            "Landroid/graphics/PointF;",
            ">;"
        }
    .end annotation
.end field

.field private final hjc:Lcom/bytedance/adsdk/lottie/hjc/Fj/dG;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/bytedance/adsdk/lottie/hjc/Fj/dG<",
            "Landroid/graphics/PointF;",
            "Landroid/graphics/PointF;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(Ljava/lang/String;Lcom/bytedance/adsdk/lottie/hjc/Fj/dG;Lcom/bytedance/adsdk/lottie/hjc/Fj/dG;Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;Z)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Lcom/bytedance/adsdk/lottie/hjc/Fj/dG<",
            "Landroid/graphics/PointF;",
            "Landroid/graphics/PointF;",
            ">;",
            "Lcom/bytedance/adsdk/lottie/hjc/Fj/dG<",
            "Landroid/graphics/PointF;",
            "Landroid/graphics/PointF;",
            ">;",
            "Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;",
            "Z)V"
        }
    .end annotation

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/rAx;->Fj:Ljava/lang/String;

    iput-object p2, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/rAx;->ex:Lcom/bytedance/adsdk/lottie/hjc/Fj/dG;

    iput-object p3, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/rAx;->hjc:Lcom/bytedance/adsdk/lottie/hjc/Fj/dG;

    iput-object p4, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/rAx;->eV:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

    iput-boolean p5, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/rAx;->Ubf:Z

    return-void
.end method


# virtual methods
.method public Fj(Lcom/bytedance/adsdk/lottie/BcC;Lcom/bytedance/adsdk/lottie/WR;Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;)Lcom/bytedance/adsdk/lottie/Fj/Fj/hjc;
    .locals 0

    new-instance p2, Lcom/bytedance/adsdk/lottie/Fj/Fj/JW;

    invoke-direct {p2, p1, p3, p0}, Lcom/bytedance/adsdk/lottie/Fj/Fj/JW;-><init>(Lcom/bytedance/adsdk/lottie/BcC;Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;Lcom/bytedance/adsdk/lottie/hjc/ex/rAx;)V

    return-object p2
.end method

.method public Fj()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/rAx;->Fj:Ljava/lang/String;

    return-object v0
.end method

.method public Ubf()Z
    .locals 1

    iget-boolean v0, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/rAx;->Ubf:Z

    return v0
.end method

.method public eV()Lcom/bytedance/adsdk/lottie/hjc/Fj/dG;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lcom/bytedance/adsdk/lottie/hjc/Fj/dG<",
            "Landroid/graphics/PointF;",
            "Landroid/graphics/PointF;",
            ">;"
        }
    .end annotation

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/rAx;->ex:Lcom/bytedance/adsdk/lottie/hjc/Fj/dG;

    return-object v0
.end method

.method public ex()Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/rAx;->eV:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

    return-object v0
.end method

.method public hjc()Lcom/bytedance/adsdk/lottie/hjc/Fj/dG;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lcom/bytedance/adsdk/lottie/hjc/Fj/dG<",
            "Landroid/graphics/PointF;",
            "Landroid/graphics/PointF;",
            ">;"
        }
    .end annotation

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/rAx;->hjc:Lcom/bytedance/adsdk/lottie/hjc/Fj/dG;

    return-object v0
.end method

.method public toString()Ljava/lang/String;
    .locals 2

    new-instance v0, Ljava/lang/StringBuilder;

    const-string v1, "RectangleShape{position="

    invoke-direct {v0, v1}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    iget-object v1, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/rAx;->ex:Lcom/bytedance/adsdk/lottie/hjc/Fj/dG;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v1, ", size="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v1, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/rAx;->hjc:Lcom/bytedance/adsdk/lottie/hjc/Fj/dG;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const/16 v1, 0x7d

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
