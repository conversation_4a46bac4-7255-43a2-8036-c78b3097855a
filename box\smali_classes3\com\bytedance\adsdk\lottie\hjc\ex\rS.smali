.class public Lcom/bytedance/adsdk/lottie/hjc/ex/rS;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/adsdk/lottie/hjc/ex/hjc;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/bytedance/adsdk/lottie/hjc/ex/rS$ex;,
        Lcom/bytedance/adsdk/lottie/hjc/ex/rS$Fj;
    }
.end annotation


# instance fields
.field private final BcC:Lcom/bytedance/adsdk/lottie/hjc/ex/rS$ex;

.field private final Fj:Ljava/lang/String;

.field private final Ko:Z

.field private final Ubf:Lcom/bytedance/adsdk/lottie/hjc/Fj/eV;

.field private final WR:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

.field private final eV:Lcom/bytedance/adsdk/lottie/hjc/Fj/Fj;

.field private final ex:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

.field private final hjc:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;",
            ">;"
        }
    .end annotation
.end field

.field private final mSE:F

.field private final svN:Lcom/bytedance/adsdk/lottie/hjc/ex/rS$Fj;


# direct methods
.method public constructor <init>(Ljava/lang/String;Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;Ljava/util/List;Lcom/bytedance/adsdk/lottie/hjc/Fj/Fj;Lcom/bytedance/adsdk/lottie/hjc/Fj/eV;Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;Lcom/bytedance/adsdk/lottie/hjc/ex/rS$Fj;Lcom/bytedance/adsdk/lottie/hjc/ex/rS$ex;FZ)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;",
            "Ljava/util/List<",
            "Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;",
            ">;",
            "Lcom/bytedance/adsdk/lottie/hjc/Fj/Fj;",
            "Lcom/bytedance/adsdk/lottie/hjc/Fj/eV;",
            "Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;",
            "Lcom/bytedance/adsdk/lottie/hjc/ex/rS$Fj;",
            "Lcom/bytedance/adsdk/lottie/hjc/ex/rS$ex;",
            "FZ)V"
        }
    .end annotation

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/rS;->Fj:Ljava/lang/String;

    iput-object p2, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/rS;->ex:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

    iput-object p3, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/rS;->hjc:Ljava/util/List;

    iput-object p4, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/rS;->eV:Lcom/bytedance/adsdk/lottie/hjc/Fj/Fj;

    iput-object p5, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/rS;->Ubf:Lcom/bytedance/adsdk/lottie/hjc/Fj/eV;

    iput-object p6, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/rS;->WR:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

    iput-object p7, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/rS;->svN:Lcom/bytedance/adsdk/lottie/hjc/ex/rS$Fj;

    iput-object p8, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/rS;->BcC:Lcom/bytedance/adsdk/lottie/hjc/ex/rS$ex;

    iput p9, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/rS;->mSE:F

    iput-boolean p10, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/rS;->Ko:Z

    return-void
.end method


# virtual methods
.method public BcC()Lcom/bytedance/adsdk/lottie/hjc/ex/rS$ex;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/rS;->BcC:Lcom/bytedance/adsdk/lottie/hjc/ex/rS$ex;

    return-object v0
.end method

.method public Fj(Lcom/bytedance/adsdk/lottie/BcC;Lcom/bytedance/adsdk/lottie/WR;Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;)Lcom/bytedance/adsdk/lottie/Fj/Fj/hjc;
    .locals 0

    new-instance p2, Lcom/bytedance/adsdk/lottie/Fj/Fj/mE;

    invoke-direct {p2, p1, p3, p0}, Lcom/bytedance/adsdk/lottie/Fj/Fj/mE;-><init>(Lcom/bytedance/adsdk/lottie/BcC;Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;Lcom/bytedance/adsdk/lottie/hjc/ex/rS;)V

    return-object p2
.end method

.method public Fj()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/rS;->Fj:Ljava/lang/String;

    return-object v0
.end method

.method public Ko()Z
    .locals 1

    iget-boolean v0, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/rS;->Ko:Z

    return v0
.end method

.method public Ubf()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;",
            ">;"
        }
    .end annotation

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/rS;->hjc:Ljava/util/List;

    return-object v0
.end method

.method public WR()Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/rS;->ex:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

    return-object v0
.end method

.method public eV()Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/rS;->WR:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

    return-object v0
.end method

.method public ex()Lcom/bytedance/adsdk/lottie/hjc/Fj/Fj;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/rS;->eV:Lcom/bytedance/adsdk/lottie/hjc/Fj/Fj;

    return-object v0
.end method

.method public hjc()Lcom/bytedance/adsdk/lottie/hjc/Fj/eV;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/rS;->Ubf:Lcom/bytedance/adsdk/lottie/hjc/Fj/eV;

    return-object v0
.end method

.method public mSE()F
    .locals 1

    iget v0, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/rS;->mSE:F

    return v0
.end method

.method public svN()Lcom/bytedance/adsdk/lottie/hjc/ex/rS$Fj;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/rS;->svN:Lcom/bytedance/adsdk/lottie/hjc/ex/rS$Fj;

    return-object v0
.end method
