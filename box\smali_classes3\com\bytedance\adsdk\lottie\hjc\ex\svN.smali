.class public final enum Lcom/bytedance/adsdk/lottie/hjc/ex/svN;
.super Ljava/lang/Enum;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Enum<",
        "Lcom/bytedance/adsdk/lottie/hjc/ex/svN;",
        ">;"
    }
.end annotation


# static fields
.field public static final enum Fj:Lcom/bytedance/adsdk/lottie/hjc/ex/svN;

.field public static final enum ex:Lcom/bytedance/adsdk/lottie/hjc/ex/svN;

.field private static final synthetic hjc:[Lcom/bytedance/adsdk/lottie/hjc/ex/svN;


# direct methods
.method static constructor <clinit>()V
    .locals 5

    new-instance v0, Lcom/bytedance/adsdk/lottie/hjc/ex/svN;

    const-string v1, "LINEAR"

    const/4 v2, 0x0

    invoke-direct {v0, v1, v2}, Lcom/bytedance/adsdk/lottie/hjc/ex/svN;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lcom/bytedance/adsdk/lottie/hjc/ex/svN;->Fj:Lcom/bytedance/adsdk/lottie/hjc/ex/svN;

    new-instance v1, Lcom/bytedance/adsdk/lottie/hjc/ex/svN;

    const-string v3, "RADIAL"

    const/4 v4, 0x1

    invoke-direct {v1, v3, v4}, Lcom/bytedance/adsdk/lottie/hjc/ex/svN;-><init>(Ljava/lang/String;I)V

    sput-object v1, Lcom/bytedance/adsdk/lottie/hjc/ex/svN;->ex:Lcom/bytedance/adsdk/lottie/hjc/ex/svN;

    const/4 v3, 0x2

    new-array v3, v3, [Lcom/bytedance/adsdk/lottie/hjc/ex/svN;

    aput-object v0, v3, v2

    aput-object v1, v3, v4

    sput-object v3, Lcom/bytedance/adsdk/lottie/hjc/ex/svN;->hjc:[Lcom/bytedance/adsdk/lottie/hjc/ex/svN;

    return-void
.end method

.method private constructor <init>(Ljava/lang/String;I)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation

    invoke-direct {p0, p1, p2}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    return-void
.end method

.method public static valueOf(Ljava/lang/String;)Lcom/bytedance/adsdk/lottie/hjc/ex/svN;
    .locals 1

    const-class v0, Lcom/bytedance/adsdk/lottie/hjc/ex/svN;

    invoke-static {v0, p0}, Ljava/lang/Enum;->valueOf(Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Enum;

    move-result-object p0

    check-cast p0, Lcom/bytedance/adsdk/lottie/hjc/ex/svN;

    return-object p0
.end method

.method public static values()[Lcom/bytedance/adsdk/lottie/hjc/ex/svN;
    .locals 1

    sget-object v0, Lcom/bytedance/adsdk/lottie/hjc/ex/svN;->hjc:[Lcom/bytedance/adsdk/lottie/hjc/ex/svN;

    invoke-virtual {v0}, [Lcom/bytedance/adsdk/lottie/hjc/ex/svN;->clone()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [Lcom/bytedance/adsdk/lottie/hjc/ex/svN;

    return-object v0
.end method
