.class public Lcom/bytedance/adsdk/lottie/hjc/ex/vYf;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/adsdk/lottie/hjc/ex/hjc;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/bytedance/adsdk/lottie/hjc/ex/vYf$Fj;
    }
.end annotation


# instance fields
.field private final Fj:Ljava/lang/String;

.field private final Ubf:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

.field private final WR:Z

.field private final eV:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

.field private final ex:Lcom/bytedance/adsdk/lottie/hjc/ex/vYf$Fj;

.field private final hjc:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;


# direct methods
.method public constructor <init>(Ljava/lang/String;Lcom/bytedance/adsdk/lottie/hjc/ex/vYf$Fj;Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;Z)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/vYf;->Fj:Ljava/lang/String;

    iput-object p2, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/vYf;->ex:Lcom/bytedance/adsdk/lottie/hjc/ex/vYf$Fj;

    iput-object p3, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/vYf;->hjc:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

    iput-object p4, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/vYf;->eV:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

    iput-object p5, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/vYf;->Ubf:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

    iput-boolean p6, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/vYf;->WR:Z

    return-void
.end method


# virtual methods
.method public Fj(Lcom/bytedance/adsdk/lottie/BcC;Lcom/bytedance/adsdk/lottie/WR;Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;)Lcom/bytedance/adsdk/lottie/Fj/Fj/hjc;
    .locals 0

    new-instance p1, Lcom/bytedance/adsdk/lottie/Fj/Fj/Af;

    invoke-direct {p1, p3, p0}, Lcom/bytedance/adsdk/lottie/Fj/Fj/Af;-><init>(Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;Lcom/bytedance/adsdk/lottie/hjc/ex/vYf;)V

    return-object p1
.end method

.method public Fj()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/vYf;->Fj:Ljava/lang/String;

    return-object v0
.end method

.method public Ubf()Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/vYf;->Ubf:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

    return-object v0
.end method

.method public WR()Z
    .locals 1

    iget-boolean v0, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/vYf;->WR:Z

    return v0
.end method

.method public eV()Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/vYf;->hjc:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

    return-object v0
.end method

.method public ex()Lcom/bytedance/adsdk/lottie/hjc/ex/vYf$Fj;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/vYf;->ex:Lcom/bytedance/adsdk/lottie/hjc/ex/vYf$Fj;

    return-object v0
.end method

.method public hjc()Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/vYf;->eV:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

    return-object v0
.end method

.method public toString()Ljava/lang/String;
    .locals 2

    new-instance v0, Ljava/lang/StringBuilder;

    const-string v1, "Trim Path: {start: "

    invoke-direct {v0, v1}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    iget-object v1, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/vYf;->hjc:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v1, ", end: "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v1, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/vYf;->eV:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v1, ", offset: "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v1, p0, Lcom/bytedance/adsdk/lottie/hjc/ex/vYf;->Ubf:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v1, "}"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
