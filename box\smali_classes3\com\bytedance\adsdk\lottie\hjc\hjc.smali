.class public Lcom/bytedance/adsdk/lottie/hjc/hjc;
.super Ljava/lang/Object;


# annotations
.annotation build Lcom/bytedance/component/sdk/annotation/RestrictTo;
    value = {
        .enum Lcom/bytedance/component/sdk/annotation/RestrictTo$Scope;->LIBRARY:Lcom/bytedance/component/sdk/annotation/RestrictTo$Scope;
    }
.end annotation


# instance fields
.field private final Fj:Ljava/lang/String;

.field private Ubf:Landroid/graphics/Typeface;

.field private final eV:F

.field private final ex:Ljava/lang/String;

.field private final hjc:Ljava/lang/String;


# direct methods
.method public constructor <init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;F)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/hjc/hjc;->Fj:Ljava/lang/String;

    iput-object p2, p0, Lcom/bytedance/adsdk/lottie/hjc/hjc;->ex:Ljava/lang/String;

    iput-object p3, p0, Lcom/bytedance/adsdk/lottie/hjc/hjc;->hjc:Ljava/lang/String;

    iput p4, p0, Lcom/bytedance/adsdk/lottie/hjc/hjc;->eV:F

    return-void
.end method


# virtual methods
.method public Fj()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/hjc;->Fj:Ljava/lang/String;

    return-object v0
.end method

.method public Fj(Landroid/graphics/Typeface;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/hjc/hjc;->Ubf:Landroid/graphics/Typeface;

    return-void
.end method

.method public eV()Landroid/graphics/Typeface;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/hjc;->Ubf:Landroid/graphics/Typeface;

    return-object v0
.end method

.method public ex()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/hjc;->ex:Ljava/lang/String;

    return-object v0
.end method

.method public hjc()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/hjc;->hjc:Ljava/lang/String;

    return-object v0
.end method
