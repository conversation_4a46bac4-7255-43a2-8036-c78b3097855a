.class Lcom/bytedance/adsdk/lottie/hjc/hjc/BcC$1;
.super Landroid/graphics/Paint;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bytedance/adsdk/lottie/hjc/hjc/BcC;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/adsdk/lottie/hjc/hjc/BcC;


# direct methods
.method public constructor <init>(Lcom/bytedance/adsdk/lottie/hjc/hjc/BcC;I)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/hjc/hjc/BcC$1;->Fj:Lcom/bytedance/adsdk/lottie/hjc/hjc/BcC;

    invoke-direct {p0, p2}, Landroid/graphics/Paint;-><init>(I)V

    sget-object p1, Landroid/graphics/Paint$Style;->FILL:Landroid/graphics/Paint$Style;

    invoke-virtual {p0, p1}, Landroid/graphics/Paint;->setStyle(Landroid/graphics/Paint$Style;)V

    return-void
.end method
