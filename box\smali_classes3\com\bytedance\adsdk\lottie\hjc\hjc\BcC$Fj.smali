.class Lcom/bytedance/adsdk/lottie/hjc/hjc/BcC$Fj;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bytedance/adsdk/lottie/hjc/hjc/BcC;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "Fj"
.end annotation


# instance fields
.field private Fj:Ljava/lang/String;

.field private ex:F


# direct methods
.method private constructor <init>()V
    .locals 1

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const-string v0, ""

    iput-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/hjc/BcC$Fj;->Fj:Ljava/lang/String;

    const/4 v0, 0x0

    iput v0, p0, Lcom/bytedance/adsdk/lottie/hjc/hjc/BcC$Fj;->ex:F

    return-void
.end method

.method public synthetic constructor <init>(Lcom/bytedance/adsdk/lottie/hjc/hjc/BcC$1;)V
    .locals 0

    invoke-direct {p0}, Lcom/bytedance/adsdk/lottie/hjc/hjc/BcC$Fj;-><init>()V

    return-void
.end method

.method public static synthetic Fj(Lcom/bytedance/adsdk/lottie/hjc/hjc/BcC$Fj;)F
    .locals 0

    iget p0, p0, Lcom/bytedance/adsdk/lottie/hjc/hjc/BcC$Fj;->ex:F

    return p0
.end method

.method public static synthetic ex(Lcom/bytedance/adsdk/lottie/hjc/hjc/BcC$Fj;)Ljava/lang/String;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/adsdk/lottie/hjc/hjc/BcC$Fj;->Fj:Ljava/lang/String;

    return-object p0
.end method


# virtual methods
.method public Fj(Ljava/lang/String;F)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/hjc/hjc/BcC$Fj;->Fj:Ljava/lang/String;

    iput p2, p0, Lcom/bytedance/adsdk/lottie/hjc/hjc/BcC$Fj;->ex:F

    return-void
.end method
