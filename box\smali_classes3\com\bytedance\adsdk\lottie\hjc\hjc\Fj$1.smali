.class Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj$1;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/adsdk/lottie/Fj/ex/Fj$Fj;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;->BcC()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;


# direct methods
.method public constructor <init>(Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj$1;->Fj:Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public Fj()V
    .locals 3

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj$1;->Fj:Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;

    invoke-static {v0}, Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;->hjc(Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;)Lcom/bytedance/adsdk/lottie/Fj/ex/eV;

    move-result-object v1

    invoke-virtual {v1}, Lcom/bytedance/adsdk/lottie/Fj/ex/eV;->mSE()F

    move-result v1

    const/high16 v2, 0x3f800000    # 1.0f

    cmpl-float v1, v1, v2

    if-nez v1, :cond_0

    const/4 v1, 0x1

    goto :goto_0

    :cond_0
    const/4 v1, 0x0

    :goto_0
    invoke-static {v0, v1}, Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;->Fj(Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;Z)V

    return-void
.end method
