.class public Lcom/bytedance/adsdk/lottie/hjc/hjc/WR;
.super Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;


# instance fields
.field private final BcC:Lcom/bytedance/adsdk/lottie/hjc/hjc/ex;

.field private final svN:Lcom/bytedance/adsdk/lottie/Fj/Fj/eV;


# direct methods
.method public constructor <init>(Lcom/bytedance/adsdk/lottie/BcC;Lcom/bytedance/adsdk/lottie/hjc/hjc/eV;Lcom/bytedance/adsdk/lottie/hjc/hjc/ex;Lcom/bytedance/adsdk/lottie/WR;)V
    .locals 2

    invoke-direct {p0, p1, p2}, Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;-><init>(Lcom/bytedance/adsdk/lottie/BcC;Lcom/bytedance/adsdk/lottie/hjc/hjc/eV;)V

    iput-object p3, p0, Lcom/bytedance/adsdk/lottie/hjc/hjc/WR;->BcC:Lcom/bytedance/adsdk/lottie/hjc/hjc/ex;

    new-instance p3, Lcom/bytedance/adsdk/lottie/hjc/ex/JU;

    invoke-virtual {p2}, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV;->Tc()Ljava/util/List;

    move-result-object p2

    const/4 v0, 0x0

    const-string v1, "__container"

    invoke-direct {p3, v1, p2, v0}, Lcom/bytedance/adsdk/lottie/hjc/ex/JU;-><init>(Ljava/lang/String;Ljava/util/List;Z)V

    new-instance p2, Lcom/bytedance/adsdk/lottie/Fj/Fj/eV;

    invoke-direct {p2, p1, p0, p3, p4}, Lcom/bytedance/adsdk/lottie/Fj/Fj/eV;-><init>(Lcom/bytedance/adsdk/lottie/BcC;Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;Lcom/bytedance/adsdk/lottie/hjc/ex/JU;Lcom/bytedance/adsdk/lottie/WR;)V

    iput-object p2, p0, Lcom/bytedance/adsdk/lottie/hjc/hjc/WR;->svN:Lcom/bytedance/adsdk/lottie/Fj/Fj/eV;

    invoke-static {}, Ljava/util/Collections;->emptyList()Ljava/util/List;

    move-result-object p1

    invoke-static {}, Ljava/util/Collections;->emptyList()Ljava/util/List;

    move-result-object p3

    invoke-virtual {p2, p1, p3}, Lcom/bytedance/adsdk/lottie/Fj/Fj/eV;->Fj(Ljava/util/List;Ljava/util/List;)V

    return-void
.end method


# virtual methods
.method public Fj(Landroid/graphics/RectF;Landroid/graphics/Matrix;Z)V
    .locals 1

    invoke-super {p0, p1, p2, p3}, Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;->Fj(Landroid/graphics/RectF;Landroid/graphics/Matrix;Z)V

    iget-object p2, p0, Lcom/bytedance/adsdk/lottie/hjc/hjc/WR;->svN:Lcom/bytedance/adsdk/lottie/Fj/Fj/eV;

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;->Fj:Landroid/graphics/Matrix;

    invoke-virtual {p2, p1, v0, p3}, Lcom/bytedance/adsdk/lottie/Fj/Fj/eV;->Fj(Landroid/graphics/RectF;Landroid/graphics/Matrix;Z)V

    return-void
.end method

.method public WR()Lcom/bytedance/adsdk/lottie/hjc/ex/Fj;
    .locals 1

    invoke-super {p0}, Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;->WR()Lcom/bytedance/adsdk/lottie/hjc/ex/Fj;

    move-result-object v0

    if-eqz v0, :cond_0

    return-object v0

    :cond_0
    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/hjc/WR;->BcC:Lcom/bytedance/adsdk/lottie/hjc/hjc/ex;

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;->WR()Lcom/bytedance/adsdk/lottie/hjc/ex/Fj;

    move-result-object v0

    return-object v0
.end method

.method public ex(Landroid/graphics/Canvas;Landroid/graphics/Matrix;I)V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/hjc/WR;->svN:Lcom/bytedance/adsdk/lottie/Fj/Fj/eV;

    invoke-virtual {v0, p1, p2, p3}, Lcom/bytedance/adsdk/lottie/Fj/Fj/eV;->Fj(Landroid/graphics/Canvas;Landroid/graphics/Matrix;I)V

    return-void
.end method

.method public svN()Lcom/bytedance/adsdk/lottie/Ubf/Ko;
    .locals 1

    invoke-super {p0}, Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;->svN()Lcom/bytedance/adsdk/lottie/Ubf/Ko;

    move-result-object v0

    if-eqz v0, :cond_0

    return-object v0

    :cond_0
    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/hjc/WR;->BcC:Lcom/bytedance/adsdk/lottie/hjc/hjc/ex;

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/hjc/hjc/Fj;->svN()Lcom/bytedance/adsdk/lottie/Ubf/Ko;

    move-result-object v0

    return-object v0
.end method
