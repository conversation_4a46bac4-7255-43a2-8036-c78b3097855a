.class public final enum Lcom/bytedance/adsdk/lottie/hjc/hjc/eV$Fj;
.super Ljava/lang/Enum;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bytedance/adsdk/lottie/hjc/hjc/eV;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x4019
    name = "Fj"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Enum<",
        "Lcom/bytedance/adsdk/lottie/hjc/hjc/eV$Fj;",
        ">;"
    }
.end annotation


# static fields
.field private static final synthetic BcC:[Lcom/bytedance/adsdk/lottie/hjc/hjc/eV$Fj;

.field public static final enum Fj:Lcom/bytedance/adsdk/lottie/hjc/hjc/eV$Fj;

.field public static final enum Ubf:Lcom/bytedance/adsdk/lottie/hjc/hjc/eV$Fj;

.field public static final enum WR:Lcom/bytedance/adsdk/lottie/hjc/hjc/eV$Fj;

.field public static final enum eV:Lcom/bytedance/adsdk/lottie/hjc/hjc/eV$Fj;

.field public static final enum ex:Lcom/bytedance/adsdk/lottie/hjc/hjc/eV$Fj;

.field public static final enum hjc:Lcom/bytedance/adsdk/lottie/hjc/hjc/eV$Fj;

.field public static final enum svN:Lcom/bytedance/adsdk/lottie/hjc/hjc/eV$Fj;


# direct methods
.method static constructor <clinit>()V
    .locals 15

    new-instance v0, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV$Fj;

    const-string v1, "PRE_COMP"

    const/4 v2, 0x0

    invoke-direct {v0, v1, v2}, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV$Fj;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV$Fj;->Fj:Lcom/bytedance/adsdk/lottie/hjc/hjc/eV$Fj;

    new-instance v1, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV$Fj;

    const-string v3, "SOLID"

    const/4 v4, 0x1

    invoke-direct {v1, v3, v4}, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV$Fj;-><init>(Ljava/lang/String;I)V

    sput-object v1, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV$Fj;->ex:Lcom/bytedance/adsdk/lottie/hjc/hjc/eV$Fj;

    new-instance v3, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV$Fj;

    const-string v5, "IMAGE"

    const/4 v6, 0x2

    invoke-direct {v3, v5, v6}, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV$Fj;-><init>(Ljava/lang/String;I)V

    sput-object v3, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV$Fj;->hjc:Lcom/bytedance/adsdk/lottie/hjc/hjc/eV$Fj;

    new-instance v5, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV$Fj;

    const-string v7, "NULL"

    const/4 v8, 0x3

    invoke-direct {v5, v7, v8}, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV$Fj;-><init>(Ljava/lang/String;I)V

    sput-object v5, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV$Fj;->eV:Lcom/bytedance/adsdk/lottie/hjc/hjc/eV$Fj;

    new-instance v7, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV$Fj;

    const-string v9, "SHAPE"

    const/4 v10, 0x4

    invoke-direct {v7, v9, v10}, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV$Fj;-><init>(Ljava/lang/String;I)V

    sput-object v7, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV$Fj;->Ubf:Lcom/bytedance/adsdk/lottie/hjc/hjc/eV$Fj;

    new-instance v9, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV$Fj;

    const-string v11, "TEXT"

    const/4 v12, 0x5

    invoke-direct {v9, v11, v12}, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV$Fj;-><init>(Ljava/lang/String;I)V

    sput-object v9, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV$Fj;->WR:Lcom/bytedance/adsdk/lottie/hjc/hjc/eV$Fj;

    new-instance v11, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV$Fj;

    const-string v13, "UNKNOWN"

    const/4 v14, 0x6

    invoke-direct {v11, v13, v14}, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV$Fj;-><init>(Ljava/lang/String;I)V

    sput-object v11, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV$Fj;->svN:Lcom/bytedance/adsdk/lottie/hjc/hjc/eV$Fj;

    const/4 v13, 0x7

    new-array v13, v13, [Lcom/bytedance/adsdk/lottie/hjc/hjc/eV$Fj;

    aput-object v0, v13, v2

    aput-object v1, v13, v4

    aput-object v3, v13, v6

    aput-object v5, v13, v8

    aput-object v7, v13, v10

    aput-object v9, v13, v12

    aput-object v11, v13, v14

    sput-object v13, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV$Fj;->BcC:[Lcom/bytedance/adsdk/lottie/hjc/hjc/eV$Fj;

    return-void
.end method

.method private constructor <init>(Ljava/lang/String;I)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation

    invoke-direct {p0, p1, p2}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    return-void
.end method

.method public static valueOf(Ljava/lang/String;)Lcom/bytedance/adsdk/lottie/hjc/hjc/eV$Fj;
    .locals 1

    const-class v0, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV$Fj;

    invoke-static {v0, p0}, Ljava/lang/Enum;->valueOf(Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Enum;

    move-result-object p0

    check-cast p0, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV$Fj;

    return-object p0
.end method

.method public static values()[Lcom/bytedance/adsdk/lottie/hjc/hjc/eV$Fj;
    .locals 1

    sget-object v0, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV$Fj;->BcC:[Lcom/bytedance/adsdk/lottie/hjc/hjc/eV$Fj;

    invoke-virtual {v0}, [Lcom/bytedance/adsdk/lottie/hjc/hjc/eV$Fj;->clone()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [Lcom/bytedance/adsdk/lottie/hjc/hjc/eV$Fj;

    return-object v0
.end method
