.class public Lcom/bytedance/adsdk/lottie/hjc/hjc/eV;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/bytedance/adsdk/lottie/hjc/hjc/eV$ex;,
        Lcom/bytedance/adsdk/lottie/hjc/hjc/eV$Fj;
    }
.end annotation


# instance fields
.field private final Af:Lcom/bytedance/adsdk/lottie/hjc/hjc/eV$ex;

.field private final BcC:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lcom/bytedance/adsdk/lottie/hjc/ex/BcC;",
            ">;"
        }
    .end annotation
.end field

.field private final Fj:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lcom/bytedance/adsdk/lottie/hjc/ex/hjc;",
            ">;"
        }
    .end annotation
.end field

.field private final JU:F

.field private final JW:F

.field private final Ko:I

.field private final Ql:Lcom/bytedance/adsdk/lottie/hjc/Fj/Ko;

.field private final Tc:F

.field private final UYd:I

.field private final Ubf:Lcom/bytedance/adsdk/lottie/hjc/hjc/eV$Fj;

.field private final WR:J

.field private final cB:Lcom/bytedance/adsdk/lottie/hjc/ex/Fj;

.field private final dG:F

.field private final eV:J

.field private final ex:Lcom/bytedance/adsdk/lottie/WR;

.field private final hjc:Ljava/lang/String;

.field private final mC:Z

.field private final mE:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lcom/bytedance/adsdk/lottie/svN/Fj<",
            "Ljava/lang/Float;",
            ">;>;"
        }
    .end annotation
.end field

.field private final mSE:Lcom/bytedance/adsdk/lottie/hjc/Fj/UYd;

.field private final nsB:Lcom/bytedance/adsdk/lottie/Ubf/Ko;

.field private final rAx:I

.field private final rS:Lcom/bytedance/adsdk/lottie/hjc/Fj/rAx;

.field private final svN:Ljava/lang/String;

.field private final vYf:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;


# direct methods
.method public constructor <init>(Ljava/util/List;Lcom/bytedance/adsdk/lottie/WR;Ljava/lang/String;JLcom/bytedance/adsdk/lottie/hjc/hjc/eV$Fj;JLjava/lang/String;Ljava/util/List;Lcom/bytedance/adsdk/lottie/hjc/Fj/UYd;IIIFFFFLcom/bytedance/adsdk/lottie/hjc/Fj/Ko;Lcom/bytedance/adsdk/lottie/hjc/Fj/rAx;Ljava/util/List;Lcom/bytedance/adsdk/lottie/hjc/hjc/eV$ex;Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;ZLcom/bytedance/adsdk/lottie/hjc/ex/Fj;Lcom/bytedance/adsdk/lottie/Ubf/Ko;)V
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lcom/bytedance/adsdk/lottie/hjc/ex/hjc;",
            ">;",
            "Lcom/bytedance/adsdk/lottie/WR;",
            "Ljava/lang/String;",
            "J",
            "Lcom/bytedance/adsdk/lottie/hjc/hjc/eV$Fj;",
            "J",
            "Ljava/lang/String;",
            "Ljava/util/List<",
            "Lcom/bytedance/adsdk/lottie/hjc/ex/BcC;",
            ">;",
            "Lcom/bytedance/adsdk/lottie/hjc/Fj/UYd;",
            "IIIFFFF",
            "Lcom/bytedance/adsdk/lottie/hjc/Fj/Ko;",
            "Lcom/bytedance/adsdk/lottie/hjc/Fj/rAx;",
            "Ljava/util/List<",
            "Lcom/bytedance/adsdk/lottie/svN/Fj<",
            "Ljava/lang/Float;",
            ">;>;",
            "Lcom/bytedance/adsdk/lottie/hjc/hjc/eV$ex;",
            "Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;",
            "Z",
            "Lcom/bytedance/adsdk/lottie/hjc/ex/Fj;",
            "Lcom/bytedance/adsdk/lottie/Ubf/Ko;",
            ")V"
        }
    .end annotation

    move-object v0, p0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    move-object v1, p1

    iput-object v1, v0, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV;->Fj:Ljava/util/List;

    move-object v1, p2

    iput-object v1, v0, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV;->ex:Lcom/bytedance/adsdk/lottie/WR;

    move-object v1, p3

    iput-object v1, v0, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV;->hjc:Ljava/lang/String;

    move-wide v1, p4

    iput-wide v1, v0, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV;->eV:J

    move-object v1, p6

    iput-object v1, v0, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV;->Ubf:Lcom/bytedance/adsdk/lottie/hjc/hjc/eV$Fj;

    move-wide v1, p7

    iput-wide v1, v0, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV;->WR:J

    move-object v1, p9

    iput-object v1, v0, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV;->svN:Ljava/lang/String;

    move-object v1, p10

    iput-object v1, v0, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV;->BcC:Ljava/util/List;

    move-object v1, p11

    iput-object v1, v0, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV;->mSE:Lcom/bytedance/adsdk/lottie/hjc/Fj/UYd;

    move v1, p12

    iput v1, v0, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV;->Ko:I

    move/from16 v1, p13

    iput v1, v0, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV;->rAx:I

    move/from16 v1, p14

    iput v1, v0, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV;->UYd:I

    move/from16 v1, p15

    iput v1, v0, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV;->dG:F

    move/from16 v1, p16

    iput v1, v0, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV;->Tc:F

    move/from16 v1, p17

    iput v1, v0, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV;->JW:F

    move/from16 v1, p18

    iput v1, v0, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV;->JU:F

    move-object/from16 v1, p19

    iput-object v1, v0, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV;->Ql:Lcom/bytedance/adsdk/lottie/hjc/Fj/Ko;

    move-object/from16 v1, p20

    iput-object v1, v0, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV;->rS:Lcom/bytedance/adsdk/lottie/hjc/Fj/rAx;

    move-object/from16 v1, p21

    iput-object v1, v0, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV;->mE:Ljava/util/List;

    move-object/from16 v1, p22

    iput-object v1, v0, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV;->Af:Lcom/bytedance/adsdk/lottie/hjc/hjc/eV$ex;

    move-object/from16 v1, p23

    iput-object v1, v0, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV;->vYf:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

    move/from16 v1, p24

    iput-boolean v1, v0, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV;->mC:Z

    move-object/from16 v1, p25

    iput-object v1, v0, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV;->cB:Lcom/bytedance/adsdk/lottie/hjc/ex/Fj;

    move-object/from16 v1, p26

    iput-object v1, v0, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV;->nsB:Lcom/bytedance/adsdk/lottie/Ubf/Ko;

    return-void
.end method


# virtual methods
.method public Af()Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV;->vYf:Lcom/bytedance/adsdk/lottie/hjc/Fj/ex;

    return-object v0
.end method

.method public BcC()F
    .locals 1

    iget v0, p0, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV;->JW:F

    return v0
.end method

.method public Fj()Lcom/bytedance/adsdk/lottie/WR;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV;->ex:Lcom/bytedance/adsdk/lottie/WR;

    return-object v0
.end method

.method public Fj(Ljava/lang/String;)Ljava/lang/String;
    .locals 6

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV;->WR()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v1, "\n"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v2, p0, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV;->ex:Lcom/bytedance/adsdk/lottie/WR;

    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV;->dG()J

    move-result-wide v3

    invoke-virtual {v2, v3, v4}, Lcom/bytedance/adsdk/lottie/WR;->Fj(J)Lcom/bytedance/adsdk/lottie/hjc/hjc/eV;

    move-result-object v2

    if-eqz v2, :cond_1

    const-string v3, "\t\tParents: "

    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV;->WR()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v3, p0, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV;->ex:Lcom/bytedance/adsdk/lottie/WR;

    invoke-virtual {v2}, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV;->dG()J

    move-result-wide v4

    invoke-virtual {v3, v4, v5}, Lcom/bytedance/adsdk/lottie/WR;->Fj(J)Lcom/bytedance/adsdk/lottie/hjc/hjc/eV;

    move-result-object v2

    :goto_0
    if-eqz v2, :cond_0

    const-string v3, "->"

    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV;->WR()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v3, p0, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV;->ex:Lcom/bytedance/adsdk/lottie/WR;

    invoke-virtual {v2}, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV;->dG()J

    move-result-wide v4

    invoke-virtual {v3, v4, v5}, Lcom/bytedance/adsdk/lottie/WR;->Fj(J)Lcom/bytedance/adsdk/lottie/hjc/hjc/eV;

    move-result-object v2

    goto :goto_0

    :cond_0
    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    :cond_1
    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV;->Ko()Ljava/util/List;

    move-result-object v2

    invoke-interface {v2}, Ljava/util/List;->isEmpty()Z

    move-result v2

    if-nez v2, :cond_2

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v2, "\tMasks: "

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV;->Ko()Ljava/util/List;

    move-result-object v2

    invoke-interface {v2}, Ljava/util/List;->size()I

    move-result v2

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    :cond_2
    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV;->rS()I

    move-result v2

    if-eqz v2, :cond_3

    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV;->Ql()I

    move-result v2

    if-eqz v2, :cond_3

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v2, "\tBackground: "

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    sget-object v2, Ljava/util/Locale;->US:Ljava/util/Locale;

    const/4 v3, 0x3

    new-array v3, v3, [Ljava/lang/Object;

    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV;->rS()I

    move-result v4

    invoke-static {v4}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v4

    const/4 v5, 0x0

    aput-object v4, v3, v5

    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV;->Ql()I

    move-result v4

    invoke-static {v4}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v4

    const/4 v5, 0x1

    aput-object v4, v3, v5

    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV;->JU()I

    move-result v4

    invoke-static {v4}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v4

    const/4 v5, 0x2

    aput-object v4, v3, v5

    const-string v4, "%dx%d %X\n"

    invoke-static {v2, v4, v3}, Ljava/lang/String;->format(Ljava/util/Locale;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    :cond_3
    iget-object v2, p0, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV;->Fj:Ljava/util/List;

    invoke-interface {v2}, Ljava/util/List;->isEmpty()Z

    move-result v2

    if-nez v2, :cond_4

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v2, "\tShapes:\n"

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v2, p0, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV;->Fj:Ljava/util/List;

    invoke-interface {v2}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v2

    :goto_1
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    move-result v3

    if-eqz v3, :cond_4

    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v3

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v4, "\t\t"

    invoke-virtual {v0, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    goto :goto_1

    :cond_4
    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method public JU()I
    .locals 1

    iget v0, p0, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV;->UYd:I

    return v0
.end method

.method public JW()Lcom/bytedance/adsdk/lottie/hjc/Fj/UYd;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV;->mSE:Lcom/bytedance/adsdk/lottie/hjc/Fj/UYd;

    return-object v0
.end method

.method public Ko()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lcom/bytedance/adsdk/lottie/hjc/ex/BcC;",
            ">;"
        }
    .end annotation

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV;->BcC:Ljava/util/List;

    return-object v0
.end method

.method public Ql()I
    .locals 1

    iget v0, p0, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV;->rAx:I

    return v0
.end method

.method public Tc()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lcom/bytedance/adsdk/lottie/hjc/ex/hjc;",
            ">;"
        }
    .end annotation

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV;->Fj:Ljava/util/List;

    return-object v0
.end method

.method public UYd()Lcom/bytedance/adsdk/lottie/hjc/hjc/eV$ex;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV;->Af:Lcom/bytedance/adsdk/lottie/hjc/hjc/eV$ex;

    return-object v0
.end method

.method public Ubf()J
    .locals 2

    iget-wide v0, p0, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV;->eV:J

    return-wide v0
.end method

.method public WR()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV;->hjc:Ljava/lang/String;

    return-object v0
.end method

.method public cB()Lcom/bytedance/adsdk/lottie/hjc/ex/Fj;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV;->cB:Lcom/bytedance/adsdk/lottie/hjc/ex/Fj;

    return-object v0
.end method

.method public dG()J
    .locals 2

    iget-wide v0, p0, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV;->WR:J

    return-wide v0
.end method

.method public eV()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lcom/bytedance/adsdk/lottie/svN/Fj<",
            "Ljava/lang/Float;",
            ">;>;"
        }
    .end annotation

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV;->mE:Ljava/util/List;

    return-object v0
.end method

.method public ex()F
    .locals 1

    iget v0, p0, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV;->dG:F

    return v0
.end method

.method public hjc()F
    .locals 2

    iget v0, p0, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV;->Tc:F

    iget-object v1, p0, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV;->ex:Lcom/bytedance/adsdk/lottie/WR;

    invoke-virtual {v1}, Lcom/bytedance/adsdk/lottie/WR;->dG()F

    move-result v1

    div-float/2addr v0, v1

    return v0
.end method

.method public mC()Z
    .locals 1

    iget-boolean v0, p0, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV;->mC:Z

    return v0
.end method

.method public mE()Lcom/bytedance/adsdk/lottie/hjc/Fj/rAx;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV;->rS:Lcom/bytedance/adsdk/lottie/hjc/Fj/rAx;

    return-object v0
.end method

.method public mSE()F
    .locals 1

    iget v0, p0, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV;->JU:F

    return v0
.end method

.method public nsB()Lcom/bytedance/adsdk/lottie/Ubf/Ko;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV;->nsB:Lcom/bytedance/adsdk/lottie/Ubf/Ko;

    return-object v0
.end method

.method public rAx()Lcom/bytedance/adsdk/lottie/hjc/hjc/eV$Fj;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV;->Ubf:Lcom/bytedance/adsdk/lottie/hjc/hjc/eV$Fj;

    return-object v0
.end method

.method public rS()I
    .locals 1

    iget v0, p0, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV;->Ko:I

    return v0
.end method

.method public svN()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV;->svN:Ljava/lang/String;

    return-object v0
.end method

.method public toString()Ljava/lang/String;
    .locals 1

    const-string v0, ""

    invoke-virtual {p0, v0}, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV;->Fj(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public vYf()Lcom/bytedance/adsdk/lottie/hjc/Fj/Ko;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/hjc/hjc/eV;->Ql:Lcom/bytedance/adsdk/lottie/hjc/Fj/Ko;

    return-object v0
.end method
