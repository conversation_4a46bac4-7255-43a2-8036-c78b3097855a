.class public Lcom/bytedance/adsdk/lottie/mSE;
.super Ljava/lang/Object;


# instance fields
.field private final Fj:I

.field private final Ubf:Ljava/lang/String;

.field private WR:Landroid/graphics/Bitmap;

.field private final eV:Ljava/lang/String;

.field private final ex:I

.field private final hjc:Ljava/lang/String;


# direct methods
.method public constructor <init>(IILjava/lang/String;Ljava/lang/String;Ljava/lang/String;)V
    .locals 0
    .annotation build Lcom/bytedance/component/sdk/annotation/RestrictTo;
        value = {
            .enum Lcom/bytedance/component/sdk/annotation/RestrictTo$Scope;->LIBRARY:Lcom/bytedance/component/sdk/annotation/RestrictTo$Scope;
        }
    .end annotation

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput p1, p0, Lcom/bytedance/adsdk/lottie/mSE;->Fj:I

    iput p2, p0, Lcom/bytedance/adsdk/lottie/mSE;->ex:I

    iput-object p3, p0, Lcom/bytedance/adsdk/lottie/mSE;->hjc:Ljava/lang/String;

    iput-object p4, p0, Lcom/bytedance/adsdk/lottie/mSE;->eV:Ljava/lang/String;

    iput-object p5, p0, Lcom/bytedance/adsdk/lottie/mSE;->Ubf:Ljava/lang/String;

    return-void
.end method


# virtual methods
.method public Fj()I
    .locals 1

    iget v0, p0, Lcom/bytedance/adsdk/lottie/mSE;->Fj:I

    return v0
.end method

.method public Fj(Landroid/graphics/Bitmap;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/mSE;->WR:Landroid/graphics/Bitmap;

    return-void
.end method

.method public Ubf()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/mSE;->Ubf:Ljava/lang/String;

    return-object v0
.end method

.method public WR()Landroid/graphics/Bitmap;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/mSE;->WR:Landroid/graphics/Bitmap;

    return-object v0
.end method

.method public eV()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/mSE;->eV:Ljava/lang/String;

    return-object v0
.end method

.method public ex()I
    .locals 1

    iget v0, p0, Lcom/bytedance/adsdk/lottie/mSE;->ex:I

    return v0
.end method

.method public hjc()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/mSE;->hjc:Ljava/lang/String;

    return-object v0
.end method
