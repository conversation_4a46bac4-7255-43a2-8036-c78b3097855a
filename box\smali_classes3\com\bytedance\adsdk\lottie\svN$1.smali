.class final Lcom/bytedance/adsdk/lottie/svN$1;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/util/concurrent/Callable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/adsdk/lottie/svN;->Fj(Landroid/content/Context;Ljava/lang/String;Ljava/lang/String;)Lcom/bytedance/adsdk/lottie/UYd;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Ljava/util/concurrent/Callable<",
        "Lcom/bytedance/adsdk/lottie/rAx<",
        "Lcom/bytedance/adsdk/lottie/WR;",
        ">;>;"
    }
.end annotation


# instance fields
.field final synthetic Fj:Landroid/content/Context;

.field final synthetic ex:Ljava/lang/String;

.field final synthetic hjc:Ljava/lang/String;


# direct methods
.method public constructor <init>(Landroid/content/Context;Ljava/lang/String;Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/svN$1;->Fj:Landroid/content/Context;

    iput-object p2, p0, Lcom/bytedance/adsdk/lottie/svN$1;->ex:Ljava/lang/String;

    iput-object p3, p0, Lcom/bytedance/adsdk/lottie/svN$1;->hjc:Ljava/lang/String;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public Fj()Lcom/bytedance/adsdk/lottie/rAx;
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lcom/bytedance/adsdk/lottie/rAx<",
            "Lcom/bytedance/adsdk/lottie/WR;",
            ">;"
        }
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/Exception;
        }
    .end annotation

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/svN$1;->Fj:Landroid/content/Context;

    invoke-static {v0}, Lcom/bytedance/adsdk/lottie/Ubf;->Fj(Landroid/content/Context;)Lcom/bytedance/adsdk/lottie/eV/BcC;

    move-result-object v0

    iget-object v1, p0, Lcom/bytedance/adsdk/lottie/svN$1;->Fj:Landroid/content/Context;

    iget-object v2, p0, Lcom/bytedance/adsdk/lottie/svN$1;->ex:Ljava/lang/String;

    iget-object v3, p0, Lcom/bytedance/adsdk/lottie/svN$1;->hjc:Ljava/lang/String;

    invoke-virtual {v0, v1, v2, v3}, Lcom/bytedance/adsdk/lottie/eV/BcC;->Fj(Landroid/content/Context;Ljava/lang/String;Ljava/lang/String;)Lcom/bytedance/adsdk/lottie/rAx;

    move-result-object v0

    iget-object v1, p0, Lcom/bytedance/adsdk/lottie/svN$1;->hjc:Ljava/lang/String;

    if-eqz v1, :cond_0

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/rAx;->Fj()Ljava/lang/Object;

    move-result-object v1

    if-eqz v1, :cond_0

    invoke-static {}, Lcom/bytedance/adsdk/lottie/hjc/Ubf;->Fj()Lcom/bytedance/adsdk/lottie/hjc/Ubf;

    move-result-object v1

    iget-object v2, p0, Lcom/bytedance/adsdk/lottie/svN$1;->hjc:Ljava/lang/String;

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/rAx;->Fj()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lcom/bytedance/adsdk/lottie/WR;

    invoke-virtual {v1, v2, v3}, Lcom/bytedance/adsdk/lottie/hjc/Ubf;->Fj(Ljava/lang/String;Lcom/bytedance/adsdk/lottie/WR;)V

    :cond_0
    return-object v0
.end method

.method public synthetic call()Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/Exception;
        }
    .end annotation

    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/svN$1;->Fj()Lcom/bytedance/adsdk/lottie/rAx;

    move-result-object v0

    return-object v0
.end method
