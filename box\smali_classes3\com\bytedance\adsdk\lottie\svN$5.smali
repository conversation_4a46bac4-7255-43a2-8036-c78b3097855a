.class final Lcom/bytedance/adsdk/lottie/svN$5;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/util/concurrent/Callable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/adsdk/lottie/svN;->Fj(Landroid/content/Context;ILjava/lang/String;)Lcom/bytedance/adsdk/lottie/UYd;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Ljava/util/concurrent/Callable<",
        "Lcom/bytedance/adsdk/lottie/rAx<",
        "Lcom/bytedance/adsdk/lottie/WR;",
        ">;>;"
    }
.end annotation


# instance fields
.field final synthetic Fj:Ljava/lang/ref/WeakReference;

.field final synthetic eV:Ljava/lang/String;

.field final synthetic ex:Landroid/content/Context;

.field final synthetic hjc:I


# direct methods
.method public constructor <init>(Ljava/lang/ref/WeakReference;Landroid/content/Context;ILjava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/svN$5;->Fj:Ljava/lang/ref/WeakReference;

    iput-object p2, p0, Lcom/bytedance/adsdk/lottie/svN$5;->ex:Landroid/content/Context;

    iput p3, p0, Lcom/bytedance/adsdk/lottie/svN$5;->hjc:I

    iput-object p4, p0, Lcom/bytedance/adsdk/lottie/svN$5;->eV:Ljava/lang/String;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public Fj()Lcom/bytedance/adsdk/lottie/rAx;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lcom/bytedance/adsdk/lottie/rAx<",
            "Lcom/bytedance/adsdk/lottie/WR;",
            ">;"
        }
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/Exception;
        }
    .end annotation

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/svN$5;->Fj:Ljava/lang/ref/WeakReference;

    invoke-virtual {v0}, Ljava/lang/ref/Reference;->get()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroid/content/Context;

    if-eqz v0, :cond_0

    goto :goto_0

    :cond_0
    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/svN$5;->ex:Landroid/content/Context;

    :goto_0
    iget v1, p0, Lcom/bytedance/adsdk/lottie/svN$5;->hjc:I

    iget-object v2, p0, Lcom/bytedance/adsdk/lottie/svN$5;->eV:Ljava/lang/String;

    invoke-static {v0, v1, v2}, Lcom/bytedance/adsdk/lottie/svN;->ex(Landroid/content/Context;ILjava/lang/String;)Lcom/bytedance/adsdk/lottie/rAx;

    move-result-object v0

    return-object v0
.end method

.method public synthetic call()Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/Exception;
        }
    .end annotation

    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/svN$5;->Fj()Lcom/bytedance/adsdk/lottie/rAx;

    move-result-object v0

    return-object v0
.end method
