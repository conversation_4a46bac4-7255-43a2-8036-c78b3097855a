.class final Lcom/bytedance/adsdk/lottie/svN$6;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/util/concurrent/Callable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/adsdk/lottie/svN;->Fj(Ljava/io/InputStream;Ljava/lang/String;)Lcom/bytedance/adsdk/lottie/UYd;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Ljava/util/concurrent/Callable<",
        "Lcom/bytedance/adsdk/lottie/rAx<",
        "Lcom/bytedance/adsdk/lottie/WR;",
        ">;>;"
    }
.end annotation


# instance fields
.field final synthetic Fj:Ljava/io/InputStream;

.field final synthetic ex:Ljava/lang/String;


# direct methods
.method public constructor <init>(Ljava/io/InputStream;Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/svN$6;->Fj:Ljava/io/InputStream;

    iput-object p2, p0, Lcom/bytedance/adsdk/lottie/svN$6;->ex:Ljava/lang/String;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public Fj()Lcom/bytedance/adsdk/lottie/rAx;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lcom/bytedance/adsdk/lottie/rAx<",
            "Lcom/bytedance/adsdk/lottie/WR;",
            ">;"
        }
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/Exception;
        }
    .end annotation

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/svN$6;->Fj:Ljava/io/InputStream;

    iget-object v1, p0, Lcom/bytedance/adsdk/lottie/svN$6;->ex:Ljava/lang/String;

    invoke-static {v0, v1}, Lcom/bytedance/adsdk/lottie/svN;->ex(Ljava/io/InputStream;Ljava/lang/String;)Lcom/bytedance/adsdk/lottie/rAx;

    move-result-object v0

    return-object v0
.end method

.method public synthetic call()Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/Exception;
        }
    .end annotation

    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/svN$6;->Fj()Lcom/bytedance/adsdk/lottie/rAx;

    move-result-object v0

    return-object v0
.end method
