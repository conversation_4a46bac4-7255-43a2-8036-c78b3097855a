.class public Lcom/bytedance/adsdk/lottie/svN/Fj;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "<T:",
        "Ljava/lang/Object;",
        ">",
        "Ljava/lang/Object;"
    }
.end annotation


# instance fields
.field public BcC:Landroid/graphics/PointF;

.field public final Fj:Ljava/lang/Object;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "TT;"
        }
    .end annotation
.end field

.field private JU:F

.field private JW:F

.field private final Ko:Lcom/bytedance/adsdk/lottie/WR;

.field private Tc:I

.field private UYd:F

.field public final Ubf:Landroid/view/animation/Interpolator;

.field public final WR:F

.field private dG:I

.field public final eV:Landroid/view/animation/Interpolator;

.field public ex:Ljava/lang/Object;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "TT;"
        }
    .end annotation
.end field

.field public final hjc:Landroid/view/animation/Interpolator;

.field public mSE:Landroid/graphics/PointF;

.field private rAx:F

.field public svN:Ljava/lang/Float;


# direct methods
.method public constructor <init>(Lcom/bytedance/adsdk/lottie/WR;Ljava/lang/Object;Ljava/lang/Object;Landroid/view/animation/Interpolator;FLjava/lang/Float;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/bytedance/adsdk/lottie/WR;",
            "TT;TT;",
            "Landroid/view/animation/Interpolator;",
            "F",
            "Ljava/lang/Float;",
            ")V"
        }
    .end annotation

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const v0, -0x358c9d09

    iput v0, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->rAx:F

    iput v0, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->UYd:F

    const v0, 0x2ec8fb09

    iput v0, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->dG:I

    iput v0, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->Tc:I

    const/4 v0, 0x1

    iput v0, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->JW:F

    iput v0, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->JU:F

    const/4 v0, 0x0

    iput-object v0, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->BcC:Landroid/graphics/PointF;

    iput-object v0, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->mSE:Landroid/graphics/PointF;

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->Ko:Lcom/bytedance/adsdk/lottie/WR;

    iput-object p2, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->Fj:Ljava/lang/Object;

    iput-object p3, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->ex:Ljava/lang/Object;

    iput-object p4, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->hjc:Landroid/view/animation/Interpolator;

    iput-object v0, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->eV:Landroid/view/animation/Interpolator;

    iput-object v0, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->Ubf:Landroid/view/animation/Interpolator;

    iput p5, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->WR:F

    iput-object p6, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->svN:Ljava/lang/Float;

    return-void
.end method

.method public constructor <init>(Lcom/bytedance/adsdk/lottie/WR;Ljava/lang/Object;Ljava/lang/Object;Landroid/view/animation/Interpolator;Landroid/view/animation/Interpolator;FLjava/lang/Float;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/bytedance/adsdk/lottie/WR;",
            "TT;TT;",
            "Landroid/view/animation/Interpolator;",
            "Landroid/view/animation/Interpolator;",
            "F",
            "Ljava/lang/Float;",
            ")V"
        }
    .end annotation

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const v0, -0x358c9d09

    iput v0, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->rAx:F

    iput v0, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->UYd:F

    const v0, 0x2ec8fb09

    iput v0, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->dG:I

    iput v0, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->Tc:I

    const/4 v0, 0x1

    iput v0, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->JW:F

    iput v0, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->JU:F

    const/4 v0, 0x0

    iput-object v0, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->BcC:Landroid/graphics/PointF;

    iput-object v0, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->mSE:Landroid/graphics/PointF;

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->Ko:Lcom/bytedance/adsdk/lottie/WR;

    iput-object p2, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->Fj:Ljava/lang/Object;

    iput-object p3, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->ex:Ljava/lang/Object;

    iput-object v0, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->hjc:Landroid/view/animation/Interpolator;

    iput-object p4, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->eV:Landroid/view/animation/Interpolator;

    iput-object p5, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->Ubf:Landroid/view/animation/Interpolator;

    iput p6, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->WR:F

    iput-object p7, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->svN:Ljava/lang/Float;

    return-void
.end method

.method public constructor <init>(Lcom/bytedance/adsdk/lottie/WR;Ljava/lang/Object;Ljava/lang/Object;Landroid/view/animation/Interpolator;Landroid/view/animation/Interpolator;Landroid/view/animation/Interpolator;FLjava/lang/Float;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/bytedance/adsdk/lottie/WR;",
            "TT;TT;",
            "Landroid/view/animation/Interpolator;",
            "Landroid/view/animation/Interpolator;",
            "Landroid/view/animation/Interpolator;",
            "F",
            "Ljava/lang/Float;",
            ")V"
        }
    .end annotation

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const v0, -0x358c9d09

    iput v0, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->rAx:F

    iput v0, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->UYd:F

    const v0, 0x2ec8fb09

    iput v0, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->dG:I

    iput v0, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->Tc:I

    const/4 v0, 0x1

    iput v0, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->JW:F

    iput v0, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->JU:F

    const/4 v0, 0x0

    iput-object v0, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->BcC:Landroid/graphics/PointF;

    iput-object v0, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->mSE:Landroid/graphics/PointF;

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->Ko:Lcom/bytedance/adsdk/lottie/WR;

    iput-object p2, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->Fj:Ljava/lang/Object;

    iput-object p3, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->ex:Ljava/lang/Object;

    iput-object p4, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->hjc:Landroid/view/animation/Interpolator;

    iput-object p5, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->eV:Landroid/view/animation/Interpolator;

    iput-object p6, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->Ubf:Landroid/view/animation/Interpolator;

    iput p7, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->WR:F

    iput-object p8, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->svN:Ljava/lang/Float;

    return-void
.end method

.method public constructor <init>(Ljava/lang/Object;)V
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(TT;)V"
        }
    .end annotation

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const v0, -0x358c9d09

    iput v0, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->rAx:F

    iput v0, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->UYd:F

    const v0, 0x2ec8fb09

    iput v0, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->dG:I

    iput v0, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->Tc:I

    const/4 v0, 0x1

    iput v0, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->JW:F

    iput v0, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->JU:F

    const/4 v1, 0x0

    iput-object v1, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->BcC:Landroid/graphics/PointF;

    iput-object v1, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->mSE:Landroid/graphics/PointF;

    iput-object v1, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->Ko:Lcom/bytedance/adsdk/lottie/WR;

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->Fj:Ljava/lang/Object;

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->ex:Ljava/lang/Object;

    iput-object v1, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->hjc:Landroid/view/animation/Interpolator;

    iput-object v1, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->eV:Landroid/view/animation/Interpolator;

    iput-object v1, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->Ubf:Landroid/view/animation/Interpolator;

    iput v0, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->WR:F

    const p1, 0x7f7fffff    # Float.MAX_VALUE

    invoke-static {p1}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    move-result-object p1

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->svN:Ljava/lang/Float;

    return-void
.end method

.method private constructor <init>(Ljava/lang/Object;Ljava/lang/Object;)V
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(TT;TT;)V"
        }
    .end annotation

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const v0, -0x358c9d09

    iput v0, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->rAx:F

    iput v0, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->UYd:F

    const v0, 0x2ec8fb09

    iput v0, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->dG:I

    iput v0, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->Tc:I

    const/4 v0, 0x1

    iput v0, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->JW:F

    iput v0, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->JU:F

    const/4 v1, 0x0

    iput-object v1, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->BcC:Landroid/graphics/PointF;

    iput-object v1, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->mSE:Landroid/graphics/PointF;

    iput-object v1, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->Ko:Lcom/bytedance/adsdk/lottie/WR;

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->Fj:Ljava/lang/Object;

    iput-object p2, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->ex:Ljava/lang/Object;

    iput-object v1, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->hjc:Landroid/view/animation/Interpolator;

    iput-object v1, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->eV:Landroid/view/animation/Interpolator;

    iput-object v1, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->Ubf:Landroid/view/animation/Interpolator;

    iput v0, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->WR:F

    const p1, 0x7f7fffff    # Float.MAX_VALUE

    invoke-static {p1}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    move-result-object p1

    iput-object p1, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->svN:Ljava/lang/Float;

    return-void
.end method


# virtual methods
.method public BcC()I
    .locals 2

    iget v0, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->dG:I

    const v1, 0x2ec8fb09

    if-ne v0, v1, :cond_0

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->Fj:Ljava/lang/Object;

    check-cast v0, Ljava/lang/Integer;

    invoke-virtual {v0}, Ljava/lang/Integer;->intValue()I

    move-result v0

    iput v0, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->dG:I

    :cond_0
    iget v0, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->dG:I

    return v0
.end method

.method public Fj(Ljava/lang/Object;Ljava/lang/Object;)Lcom/bytedance/adsdk/lottie/svN/Fj;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(TT;TT;)",
            "Lcom/bytedance/adsdk/lottie/svN/Fj<",
            "TT;>;"
        }
    .end annotation

    new-instance v0, Lcom/bytedance/adsdk/lottie/svN/Fj;

    invoke-direct {v0, p1, p2}, Lcom/bytedance/adsdk/lottie/svN/Fj;-><init>(Ljava/lang/Object;Ljava/lang/Object;)V

    return-object v0
.end method

.method public Fj(F)Z
    .locals 1
    .param p1    # F
        .annotation build Lcom/bytedance/component/sdk/annotation/FloatRange;
            from = 0.0
            to = 1.0
        .end annotation
    .end param

    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/svN/Fj;->hjc()F

    move-result v0

    cmpl-float v0, p1, v0

    if-ltz v0, :cond_0

    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/svN/Fj;->eV()F

    move-result v0

    cmpg-float p1, p1, v0

    if-gez p1, :cond_0

    const/4 p1, 0x1

    return p1

    :cond_0
    const/4 p1, 0x0

    return p1
.end method

.method public Ubf()Z
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->hjc:Landroid/view/animation/Interpolator;

    if-nez v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->eV:Landroid/view/animation/Interpolator;

    if-nez v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->Ubf:Landroid/view/animation/Interpolator;

    if-nez v0, :cond_0

    const/4 v0, 0x1

    return v0

    :cond_0
    const/4 v0, 0x0

    return v0
.end method

.method public WR()F
    .locals 2

    iget v0, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->rAx:F

    const v1, -0x358c9d09

    cmpl-float v0, v0, v1

    if-nez v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->Fj:Ljava/lang/Object;

    check-cast v0, Ljava/lang/Float;

    invoke-virtual {v0}, Ljava/lang/Float;->floatValue()F

    move-result v0

    iput v0, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->rAx:F

    :cond_0
    iget v0, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->rAx:F

    return v0
.end method

.method public eV()F
    .locals 3

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->Ko:Lcom/bytedance/adsdk/lottie/WR;

    const/high16 v1, 0x3f800000    # 1.0f

    if-nez v0, :cond_0

    return v1

    :cond_0
    iget v0, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->JU:F

    const/4 v2, 0x1

    cmpl-float v0, v0, v2

    if-nez v0, :cond_2

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->svN:Ljava/lang/Float;

    if-nez v0, :cond_1

    iput v1, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->JU:F

    goto :goto_0

    :cond_1
    invoke-virtual {p0}, Lcom/bytedance/adsdk/lottie/svN/Fj;->hjc()F

    move-result v0

    iget-object v1, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->svN:Ljava/lang/Float;

    invoke-virtual {v1}, Ljava/lang/Float;->floatValue()F

    move-result v1

    iget v2, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->WR:F

    sub-float/2addr v1, v2

    iget-object v2, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->Ko:Lcom/bytedance/adsdk/lottie/WR;

    invoke-virtual {v2}, Lcom/bytedance/adsdk/lottie/WR;->dG()F

    move-result v2

    div-float/2addr v1, v2

    add-float/2addr v0, v1

    iput v0, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->JU:F

    :cond_2
    :goto_0
    iget v0, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->JU:F

    return v0
.end method

.method public hjc()F
    .locals 3

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->Ko:Lcom/bytedance/adsdk/lottie/WR;

    if-nez v0, :cond_0

    const/4 v0, 0x0

    return v0

    :cond_0
    iget v1, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->JW:F

    const/4 v2, 0x1

    cmpl-float v1, v1, v2

    if-nez v1, :cond_1

    iget v1, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->WR:F

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/WR;->WR()F

    move-result v0

    sub-float/2addr v1, v0

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->Ko:Lcom/bytedance/adsdk/lottie/WR;

    invoke-virtual {v0}, Lcom/bytedance/adsdk/lottie/WR;->dG()F

    move-result v0

    div-float/2addr v1, v0

    iput v1, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->JW:F

    :cond_1
    iget v0, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->JW:F

    return v0
.end method

.method public mSE()I
    .locals 2

    iget v0, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->Tc:I

    const v1, 0x2ec8fb09

    if-ne v0, v1, :cond_0

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->ex:Ljava/lang/Object;

    check-cast v0, Ljava/lang/Integer;

    invoke-virtual {v0}, Ljava/lang/Integer;->intValue()I

    move-result v0

    iput v0, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->Tc:I

    :cond_0
    iget v0, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->Tc:I

    return v0
.end method

.method public svN()F
    .locals 2

    iget v0, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->UYd:F

    const v1, -0x358c9d09

    cmpl-float v0, v0, v1

    if-nez v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->ex:Ljava/lang/Object;

    check-cast v0, Ljava/lang/Float;

    invoke-virtual {v0}, Ljava/lang/Float;->floatValue()F

    move-result v0

    iput v0, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->UYd:F

    :cond_0
    iget v0, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->UYd:F

    return v0
.end method

.method public toString()Ljava/lang/String;
    .locals 2

    new-instance v0, Ljava/lang/StringBuilder;

    const-string v1, "Keyframe{startValue="

    invoke-direct {v0, v1}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    iget-object v1, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->Fj:Ljava/lang/Object;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v1, ", endValue="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v1, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->ex:Ljava/lang/Object;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v1, ", startFrame="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget v1, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->WR:F

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(F)Ljava/lang/StringBuilder;

    const-string v1, ", endFrame="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v1, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->svN:Ljava/lang/Float;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v1, ", interpolator="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v1, p0, Lcom/bytedance/adsdk/lottie/svN/Fj;->hjc:Landroid/view/animation/Interpolator;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const/16 v1, 0x7d

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
