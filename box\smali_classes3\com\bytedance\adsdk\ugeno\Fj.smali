.class public interface abstract Lcom/bytedance/adsdk/ugeno/Fj;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/bytedance/adsdk/ugeno/Fj$Fj;
    }
.end annotation


# virtual methods
.method public abstract Fj(Landroid/content/Context;Ljava/lang/String;FLcom/bytedance/adsdk/ugeno/Fj$Fj;)V
.end method

.method public abstract Fj(Landroid/content/Context;Ljava/lang/String;Landroid/widget/ImageView;)V
.end method
