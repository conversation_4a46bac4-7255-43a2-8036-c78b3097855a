.class public Lcom/bytedance/adsdk/ugeno/Fj/eV;
.super Ljava/lang/Object;


# direct methods
.method public static Fj(Ljava/lang/String;)Lcom/bytedance/adsdk/ugeno/Fj/ex;
    .locals 2

    invoke-static {p0}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    const/4 v1, 0x0

    if-eqz v0, :cond_0

    return-object v1

    :cond_0
    invoke-virtual {p0}, Ljava/lang/String;->hashCode()I

    const-string v0, "find"

    invoke-virtual {p0, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result p0

    if-nez p0, :cond_1

    goto :goto_0

    :cond_1
    new-instance v1, Lcom/bytedance/adsdk/ugeno/Fj/Fj;

    invoke-direct {v1}, Lcom/bytedance/adsdk/ugeno/Fj/Fj;-><init>()V

    :goto_0
    return-object v1
.end method

.method public static Fj(Lcom/bytedance/adsdk/Fj/ex/Fj/Fj;)Ljava/lang/Object;
    .locals 2

    const/4 v0, 0x0

    if-nez p0, :cond_0

    return-object v0

    :cond_0
    invoke-virtual {p0}, Lcom/bytedance/adsdk/Fj/ex/Fj/Fj;->Fj()Ljava/lang/String;

    move-result-object v1

    invoke-static {v1}, Lcom/bytedance/adsdk/ugeno/Fj/eV;->Fj(Ljava/lang/String;)Lcom/bytedance/adsdk/ugeno/Fj/ex;

    move-result-object v1

    if-nez v1, :cond_1

    return-object v0

    :cond_1
    invoke-virtual {p0}, Lcom/bytedance/adsdk/Fj/ex/Fj/Fj;->ex()[Ljava/lang/Object;

    move-result-object p0

    invoke-interface {v1, p0}, Lcom/bytedance/adsdk/ugeno/Fj/ex;->ex([Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    return-object p0
.end method
