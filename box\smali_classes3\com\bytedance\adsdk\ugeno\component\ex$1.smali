.class Lcom/bytedance/adsdk/ugeno/component/ex$1;
.super Ljava/lang/Object;

# interfaces
.implements Landroid/view/View$OnClickListener;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/adsdk/ugeno/component/ex;->ex()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/adsdk/ugeno/component/ex;


# direct methods
.method public constructor <init>(Lcom/bytedance/adsdk/ugeno/component/ex;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/adsdk/ugeno/component/ex$1;->Fj:Lcom/bytedance/adsdk/ugeno/component/ex;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public onClick(Landroid/view/View;)V
    .locals 1

    iget-object p1, p0, Lcom/bytedance/adsdk/ugeno/component/ex$1;->Fj:Lcom/bytedance/adsdk/ugeno/component/ex;

    iget-object v0, p1, Lcom/bytedance/adsdk/ugeno/component/ex;->KZ:Lcom/bytedance/adsdk/ugeno/core/svN;

    if-eqz v0, :cond_0

    invoke-static {p1}, Lcom/bytedance/adsdk/ugeno/component/ex;->hjc(Lcom/bytedance/adsdk/ugeno/component/ex;)Z

    :cond_0
    return-void
.end method
