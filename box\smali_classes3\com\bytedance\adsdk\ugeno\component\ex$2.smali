.class Lcom/bytedance/adsdk/ugeno/component/ex$2;
.super Ljava/lang/Object;

# interfaces
.implements Landroid/view/View$OnClickListener;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/adsdk/ugeno/component/ex;->ex()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/adsdk/ugeno/component/ex;


# direct methods
.method public constructor <init>(Lcom/bytedance/adsdk/ugeno/component/ex;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/adsdk/ugeno/component/ex$2;->Fj:Lcom/bytedance/adsdk/ugeno/component/ex;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public onClick(Landroid/view/View;)V
    .locals 2

    iget-object p1, p0, Lcom/bytedance/adsdk/ugeno/component/ex$2;->Fj:Lcom/bytedance/adsdk/ugeno/component/ex;

    iget-object v0, p1, Lcom/bytedance/adsdk/ugeno/component/ex;->mj:Lcom/bytedance/adsdk/ugeno/core/dG;

    if-eqz v0, :cond_0

    invoke-static {p1}, Lcom/bytedance/adsdk/ugeno/component/ex;->hjc(Lcom/bytedance/adsdk/ugeno/component/ex;)Z

    move-result p1

    if-eqz p1, :cond_0

    iget-object p1, p0, Lcom/bytedance/adsdk/ugeno/component/ex$2;->Fj:Lcom/bytedance/adsdk/ugeno/component/ex;

    iget-object v0, p1, Lcom/bytedance/adsdk/ugeno/component/ex;->mj:Lcom/bytedance/adsdk/ugeno/core/dG;

    iget-object p1, p1, Lcom/bytedance/adsdk/ugeno/component/ex;->qPr:Ljava/util/Map;

    const/4 v1, 0x1

    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    invoke-interface {p1, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/bytedance/adsdk/ugeno/core/rAx;

    iget-object v1, p0, Lcom/bytedance/adsdk/ugeno/component/ex$2;->Fj:Lcom/bytedance/adsdk/ugeno/component/ex;

    invoke-interface {v0, p1, v1, v1}, Lcom/bytedance/adsdk/ugeno/core/dG;->Fj(Lcom/bytedance/adsdk/ugeno/core/rAx;Lcom/bytedance/adsdk/ugeno/core/dG$ex;Lcom/bytedance/adsdk/ugeno/core/dG$Fj;)V

    :cond_0
    return-void
.end method
