.class Lcom/bytedance/adsdk/ugeno/component/ex$3;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/adsdk/ugeno/component/ex;->ex()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/adsdk/ugeno/component/ex;


# direct methods
.method public constructor <init>(Lcom/bytedance/adsdk/ugeno/component/ex;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/adsdk/ugeno/component/ex$3;->Fj:Lcom/bytedance/adsdk/ugeno/component/ex;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
