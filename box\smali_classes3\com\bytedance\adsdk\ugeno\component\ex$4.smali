.class Lcom/bytedance/adsdk/ugeno/component/ex$4;
.super Ljava/lang/Object;

# interfaces
.implements Landroid/view/View$OnTouchListener;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/adsdk/ugeno/component/ex;->ex()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/adsdk/ugeno/core/ex/hjc;

.field final synthetic ex:Lcom/bytedance/adsdk/ugeno/core/ex/eV;

.field final synthetic hjc:Lcom/bytedance/adsdk/ugeno/component/ex;


# direct methods
.method public constructor <init>(Lcom/bytedance/adsdk/ugeno/component/ex;Lcom/bytedance/adsdk/ugeno/core/ex/hjc;Lcom/bytedance/adsdk/ugeno/core/ex/eV;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/adsdk/ugeno/component/ex$4;->hjc:Lcom/bytedance/adsdk/ugeno/component/ex;

    iput-object p2, p0, Lcom/bytedance/adsdk/ugeno/component/ex$4;->Fj:Lcom/bytedance/adsdk/ugeno/core/ex/hjc;

    iput-object p3, p0, Lcom/bytedance/adsdk/ugeno/component/ex$4;->ex:Lcom/bytedance/adsdk/ugeno/core/ex/eV;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public onTouch(Landroid/view/View;Landroid/view/MotionEvent;)Z
    .locals 2

    iget-object p1, p0, Lcom/bytedance/adsdk/ugeno/component/ex$4;->hjc:Lcom/bytedance/adsdk/ugeno/component/ex;

    iget-object v0, p1, Lcom/bytedance/adsdk/ugeno/component/ex;->PpV:Lcom/bytedance/adsdk/ugeno/core/Tc;

    if-eqz v0, :cond_0

    invoke-interface {v0, p1, p2}, Lcom/bytedance/adsdk/ugeno/core/Tc;->Fj(Lcom/bytedance/adsdk/ugeno/component/ex;Landroid/view/MotionEvent;)V

    :cond_0
    iget-object p1, p0, Lcom/bytedance/adsdk/ugeno/component/ex$4;->hjc:Lcom/bytedance/adsdk/ugeno/component/ex;

    const/16 v0, 0x11

    invoke-virtual {p1, v0}, Lcom/bytedance/adsdk/ugeno/component/ex;->Fj(I)Z

    move-result p1

    if-eqz p1, :cond_1

    invoke-virtual {p2}, Landroid/view/MotionEvent;->getAction()I

    move-result p1

    if-nez p1, :cond_1

    iget-object p1, p0, Lcom/bytedance/adsdk/ugeno/component/ex$4;->hjc:Lcom/bytedance/adsdk/ugeno/component/ex;

    iget-object v1, p1, Lcom/bytedance/adsdk/ugeno/component/ex;->mj:Lcom/bytedance/adsdk/ugeno/core/dG;

    iget-object p1, p1, Lcom/bytedance/adsdk/ugeno/component/ex;->qPr:Ljava/util/Map;

    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v0

    invoke-interface {p1, v0}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/bytedance/adsdk/ugeno/core/rAx;

    iget-object v0, p0, Lcom/bytedance/adsdk/ugeno/component/ex$4;->hjc:Lcom/bytedance/adsdk/ugeno/component/ex;

    invoke-interface {v1, p1, v0, v0}, Lcom/bytedance/adsdk/ugeno/core/dG;->Fj(Lcom/bytedance/adsdk/ugeno/core/rAx;Lcom/bytedance/adsdk/ugeno/core/dG$ex;Lcom/bytedance/adsdk/ugeno/core/dG$Fj;)V

    :cond_1
    iget-object p1, p0, Lcom/bytedance/adsdk/ugeno/component/ex$4;->hjc:Lcom/bytedance/adsdk/ugeno/component/ex;

    const/4 v0, 0x1

    invoke-virtual {p1, v0}, Lcom/bytedance/adsdk/ugeno/component/ex;->Fj(I)Z

    move-result p1

    if-eqz p1, :cond_2

    iget-object p1, p0, Lcom/bytedance/adsdk/ugeno/component/ex$4;->hjc:Lcom/bytedance/adsdk/ugeno/component/ex;

    invoke-static {p1}, Lcom/bytedance/adsdk/ugeno/component/ex;->eV(Lcom/bytedance/adsdk/ugeno/component/ex;)Z

    move-result p1

    if-eqz p1, :cond_2

    iget-object p1, p0, Lcom/bytedance/adsdk/ugeno/component/ex$4;->hjc:Lcom/bytedance/adsdk/ugeno/component/ex;

    iget-object v0, p1, Lcom/bytedance/adsdk/ugeno/component/ex;->mj:Lcom/bytedance/adsdk/ugeno/core/dG;

    if-eqz v0, :cond_2

    iget-object v1, p0, Lcom/bytedance/adsdk/ugeno/component/ex$4;->Fj:Lcom/bytedance/adsdk/ugeno/core/ex/hjc;

    if-eqz v1, :cond_2

    invoke-virtual {v1, v0, p1, p2}, Lcom/bytedance/adsdk/ugeno/core/ex/hjc;->Fj(Lcom/bytedance/adsdk/ugeno/core/dG;Lcom/bytedance/adsdk/ugeno/component/ex;Landroid/view/MotionEvent;)Z

    move-result p1

    return p1

    :cond_2
    iget-object p1, p0, Lcom/bytedance/adsdk/ugeno/component/ex$4;->hjc:Lcom/bytedance/adsdk/ugeno/component/ex;

    iget-object v0, p1, Lcom/bytedance/adsdk/ugeno/component/ex;->mj:Lcom/bytedance/adsdk/ugeno/core/dG;

    if-eqz v0, :cond_3

    iget-object v1, p0, Lcom/bytedance/adsdk/ugeno/component/ex$4;->ex:Lcom/bytedance/adsdk/ugeno/core/ex/eV;

    if-eqz v1, :cond_3

    invoke-virtual {v1, v0, p1, p2}, Lcom/bytedance/adsdk/ugeno/core/ex/eV;->Fj(Lcom/bytedance/adsdk/ugeno/core/dG;Lcom/bytedance/adsdk/ugeno/component/ex;Landroid/view/MotionEvent;)Z

    move-result p1

    return p1

    :cond_3
    const/4 p1, 0x0

    return p1
.end method
