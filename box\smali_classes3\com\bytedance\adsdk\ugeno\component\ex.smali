.class public abstract Lcom/bytedance/adsdk/ugeno/component/ex;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/adsdk/ugeno/core/dG$Fj;
.implements Lcom/bytedance/adsdk/ugeno/core/dG$ex;
.implements Lcom/bytedance/adsdk/ugeno/ex;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "<T:",
        "Landroid/view/View;",
        ">",
        "Ljava/lang/Object;",
        "Lcom/bytedance/adsdk/ugeno/core/dG$Fj;",
        "Lcom/bytedance/adsdk/ugeno/core/dG$ex;",
        "Lcom/bytedance/adsdk/ugeno/ex;"
    }
.end annotation


# instance fields
.field protected Af:F

.field private At:Ljava/lang/String;

.field protected BcC:Lcom/bytedance/adsdk/ugeno/core/WR$Fj;

.field private Bzy:Lcom/bytedance/adsdk/ugeno/ex/Fj$Fj;

.field private Eev:Z

.field private Fj:Landroid/graphics/drawable/GradientDrawable;

.field private Gv:Lorg/json/JSONObject;

.field private HQ:Z

.field private HY:Lcom/bytedance/adsdk/ugeno/core/ex/Ubf;

.field protected JU:F

.field protected JW:F

.field private JZ:Z

.field private Jq:Z

.field protected KZ:Lcom/bytedance/adsdk/ugeno/core/svN;

.field private Kk:Lcom/bytedance/adsdk/ugeno/core/ex/ex;

.field protected Ko:Ljava/lang/String;

.field protected Moo:Z

.field private Nyg:Z

.field private OK:Ljava/lang/String;

.field private Obv:Lcom/bytedance/adsdk/ugeno/core/Fj;

.field protected PpV:Lcom/bytedance/adsdk/ugeno/core/Tc;

.field protected Ql:F

.field protected Tc:F

.field protected UYd:F

.field protected Ubf:Landroid/view/View;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "TT;"
        }
    .end annotation
.end field

.field protected Vq:Z

.field protected WR:Lcom/bytedance/adsdk/ugeno/component/Fj;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/bytedance/adsdk/ugeno/component/Fj<",
            "Landroid/view/ViewGroup;",
            ">;"
        }
    .end annotation
.end field

.field private YH:Lcom/bytedance/adsdk/ugeno/core/BcC;

.field protected cB:F

.field private cs:Lcom/bytedance/adsdk/ugeno/core/ex/Fj;

.field protected dG:F

.field protected eV:Lorg/json/JSONObject;

.field protected efV:F

.field protected eh:F

.field private ei:Z

.field protected ex:Landroid/content/Context;

.field protected fj:F

.field private flF:Z

.field protected gXF:F

.field private gci:Z

.field private haP:Lcom/bytedance/adsdk/ugeno/core/JU;

.field protected hjc:Lorg/json/JSONObject;

.field protected iT:I

.field protected kF:F

.field private ks:Z

.field protected lv:Ljava/lang/String;

.field protected mC:F

.field protected mE:F

.field protected mSE:Lcom/bytedance/adsdk/ugeno/core/mSE;

.field protected mj:Lcom/bytedance/adsdk/ugeno/core/dG;

.field protected nsB:Z

.field protected oX:Z

.field protected qPr:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/Integer;",
            "Lcom/bytedance/adsdk/ugeno/core/rAx;",
            ">;"
        }
    .end annotation
.end field

.field private qg:Z

.field protected rAx:Ljava/lang/String;

.field protected rS:F

.field protected rXP:F

.field protected rf:Z

.field protected spi:I

.field protected svN:Lcom/bytedance/adsdk/ugeno/component/Fj;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/bytedance/adsdk/ugeno/component/Fj<",
            "Landroid/view/ViewGroup;",
            ">;"
        }
    .end annotation
.end field

.field protected uM:F

.field protected uy:I

.field protected vYf:F

.field private yo:F


# direct methods
.method public constructor <init>(Landroid/content/Context;)V
    .locals 1

    const/4 v0, 0x0

    invoke-direct {p0, p1, v0}, Lcom/bytedance/adsdk/ugeno/component/ex;-><init>(Landroid/content/Context;Lcom/bytedance/adsdk/ugeno/component/Fj;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Lcom/bytedance/adsdk/ugeno/component/Fj;)V
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/content/Context;",
            "Lcom/bytedance/adsdk/ugeno/component/Fj<",
            "Landroid/view/ViewGroup;",
            ">;)V"
        }
    .end annotation

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/high16 v0, -0x40000000    # -2.0f

    iput v0, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->UYd:F

    iput v0, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->dG:F

    const/4 v0, 0x0

    iput v0, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->iT:I

    const/4 v1, 0x1

    iput-boolean v1, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->oX:Z

    iput-boolean v1, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->Eev:Z

    iput-boolean v0, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->Nyg:Z

    iput-boolean v0, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->HQ:Z

    const/high16 v0, 0x41400000    # 12.0f

    iput v0, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->yo:F

    iput-object p1, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->ex:Landroid/content/Context;

    iput-object p2, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->WR:Lcom/bytedance/adsdk/ugeno/component/Fj;

    new-instance p1, Ljava/util/HashMap;

    invoke-direct {p1}, Ljava/util/HashMap;-><init>()V

    iput-object p1, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->qPr:Ljava/util/Map;

    new-instance p1, Landroid/graphics/drawable/GradientDrawable;

    invoke-direct {p1}, Landroid/graphics/drawable/GradientDrawable;-><init>()V

    iput-object p1, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->Fj:Landroid/graphics/drawable/GradientDrawable;

    invoke-virtual {p0}, Lcom/bytedance/adsdk/ugeno/component/ex;->hjc()Landroid/view/View;

    move-result-object p1

    iput-object p1, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->Ubf:Landroid/view/View;

    return-void
.end method

.method private Fj()V
    .locals 4

    iget-object v0, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->lv:Ljava/lang/String;

    invoke-static {v0}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    if-eqz v0, :cond_3

    iget-object v0, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->Fj:Landroid/graphics/drawable/GradientDrawable;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Landroid/graphics/drawable/GradientDrawable;->setShape(I)V

    iget-boolean v0, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->gci:Z

    if-eqz v0, :cond_1

    iget-object v0, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->Bzy:Lcom/bytedance/adsdk/ugeno/ex/Fj$Fj;

    if-eqz v0, :cond_2

    iget-object v1, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->Fj:Landroid/graphics/drawable/GradientDrawable;

    iget-object v0, v0, Lcom/bytedance/adsdk/ugeno/ex/Fj$Fj;->Fj:Landroid/graphics/drawable/GradientDrawable$Orientation;

    invoke-virtual {v1, v0}, Landroid/graphics/drawable/GradientDrawable;->setOrientation(Landroid/graphics/drawable/GradientDrawable$Orientation;)V

    sget v0, Landroid/os/Build$VERSION;->SDK_INT:I

    const/16 v1, 0x1d

    if-lt v0, v1, :cond_0

    iget-object v0, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->Fj:Landroid/graphics/drawable/GradientDrawable;

    iget-object v1, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->Bzy:Lcom/bytedance/adsdk/ugeno/ex/Fj$Fj;

    iget-object v2, v1, Lcom/bytedance/adsdk/ugeno/ex/Fj$Fj;->ex:[I

    iget-object v1, v1, Lcom/bytedance/adsdk/ugeno/ex/Fj$Fj;->hjc:[F

    invoke-static {v0, v2, v1}, Lcom/bytedance/adsdk/ugeno/component/a;->a(Landroid/graphics/drawable/GradientDrawable;[I[F)V

    goto :goto_0

    :cond_0
    iget-object v0, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->Fj:Landroid/graphics/drawable/GradientDrawable;

    iget-object v1, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->Bzy:Lcom/bytedance/adsdk/ugeno/ex/Fj$Fj;

    iget-object v1, v1, Lcom/bytedance/adsdk/ugeno/ex/Fj$Fj;->ex:[I

    invoke-virtual {v0, v1}, Landroid/graphics/drawable/GradientDrawable;->setColors([I)V

    goto :goto_0

    :cond_1
    iget-object v0, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->Fj:Landroid/graphics/drawable/GradientDrawable;

    iget v1, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->uy:I

    invoke-virtual {v0, v1}, Landroid/graphics/drawable/GradientDrawable;->setColor(I)V

    :cond_2
    :goto_0
    iget-object v0, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->Fj:Landroid/graphics/drawable/GradientDrawable;

    iget v1, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->rXP:F

    invoke-virtual {v0, v1}, Landroid/graphics/drawable/GradientDrawable;->setCornerRadius(F)V

    iget-object v0, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->Fj:Landroid/graphics/drawable/GradientDrawable;

    iget v1, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->gXF:F

    float-to-int v1, v1

    iget v2, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->spi:I

    invoke-virtual {v0, v1, v2}, Landroid/graphics/drawable/GradientDrawable;->setStroke(II)V

    iget-object v0, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->Ubf:Landroid/view/View;

    iget-object v1, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->Fj:Landroid/graphics/drawable/GradientDrawable;

    invoke-virtual {v0, v1}, Landroid/view/View;->setBackground(Landroid/graphics/drawable/Drawable;)V

    return-void

    :cond_3
    iget-object v0, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->lv:Ljava/lang/String;

    const-string v1, "local://"

    invoke-virtual {v0, v1}, Ljava/lang/String;->startsWith(Ljava/lang/String;)Z

    move-result v0

    if-eqz v0, :cond_4

    iget-object v0, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->lv:Ljava/lang/String;

    const-string v2, ""

    invoke-virtual {v0, v1, v2}, Ljava/lang/String;->replace(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Ljava/lang/String;

    move-result-object v0

    :try_start_0
    new-instance v1, Landroid/graphics/BitmapFactory$Options;

    invoke-direct {v1}, Landroid/graphics/BitmapFactory$Options;-><init>()V

    sget-object v2, Landroid/graphics/Bitmap$Config;->RGB_565:Landroid/graphics/Bitmap$Config;

    iput-object v2, v1, Landroid/graphics/BitmapFactory$Options;->inPreferredConfig:Landroid/graphics/Bitmap$Config;

    const/4 v2, 0x1

    iput-boolean v2, v1, Landroid/graphics/BitmapFactory$Options;->inPurgeable:Z

    iput-boolean v2, v1, Landroid/graphics/BitmapFactory$Options;->inInputShareable:Z

    iget-object v2, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->ex:Landroid/content/Context;

    invoke-virtual {v2}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    move-result-object v2

    iget-object v3, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->ex:Landroid/content/Context;

    invoke-static {v3, v0}, Lcom/bytedance/adsdk/ugeno/ex/eV;->ex(Landroid/content/Context;Ljava/lang/String;)I

    move-result v0

    invoke-virtual {v2, v0}, Landroid/content/res/Resources;->openRawResource(I)Ljava/io/InputStream;

    move-result-object v0

    const/4 v2, 0x0

    invoke-static {v0, v2, v1}, Landroid/graphics/BitmapFactory;->decodeStream(Ljava/io/InputStream;Landroid/graphics/Rect;Landroid/graphics/BitmapFactory$Options;)Landroid/graphics/Bitmap;

    move-result-object v0

    new-instance v1, Landroid/graphics/drawable/BitmapDrawable;

    iget-object v2, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->ex:Landroid/content/Context;

    invoke-virtual {v2}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    move-result-object v2

    invoke-direct {v1, v2, v0}, Landroid/graphics/drawable/BitmapDrawable;-><init>(Landroid/content/res/Resources;Landroid/graphics/Bitmap;)V

    iget-object v0, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->Ubf:Landroid/view/View;

    invoke-virtual {v0, v1}, Landroid/view/View;->setBackground(Landroid/graphics/drawable/Drawable;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    :catchall_0
    :cond_4
    return-void
.end method

.method public static synthetic eV(Lcom/bytedance/adsdk/ugeno/component/ex;)Z
    .locals 0

    iget-boolean p0, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->qg:Z

    return p0
.end method

.method public static synthetic hjc(Lcom/bytedance/adsdk/ugeno/component/ex;)Z
    .locals 0

    iget-boolean p0, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->Eev:Z

    return p0
.end method


# virtual methods
.method public Fj(Ljava/lang/String;)Lcom/bytedance/adsdk/ugeno/component/ex;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            ")",
            "Lcom/bytedance/adsdk/ugeno/component/ex<",
            "TT;>;"
        }
    .end annotation

    iget-object v0, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->Ko:Ljava/lang/String;

    invoke-static {v0}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    if-nez v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->Ko:Ljava/lang/String;

    invoke-static {v0, p1}, Landroid/text/TextUtils;->equals(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Z

    move-result p1

    if-eqz p1, :cond_0

    return-object p0

    :cond_0
    const/4 p1, 0x0

    return-object p1
.end method

.method public Fj(IIII)V
    .locals 0

    iget-object p1, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->haP:Lcom/bytedance/adsdk/ugeno/core/JU;

    if-eqz p1, :cond_0

    iget-boolean p1, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->JZ:Z

    if-nez p1, :cond_0

    const/4 p1, 0x1

    iput-boolean p1, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->JZ:Z

    :cond_0
    return-void
.end method

.method public Fj(ILorg/json/JSONObject;Lcom/bytedance/adsdk/ugeno/core/rAx;)V
    .locals 2
    .annotation runtime Ljava/lang/Deprecated;
    .end annotation

    const-string v0, "success"

    invoke-virtual {p2, v0}, Lorg/json/JSONObject;->optJSONObject(Ljava/lang/String;)Lorg/json/JSONObject;

    move-result-object v0

    if-eqz v0, :cond_0

    new-instance v1, Lcom/bytedance/adsdk/ugeno/core/rAx;

    invoke-direct {v1}, Lcom/bytedance/adsdk/ugeno/core/rAx;-><init>()V

    invoke-virtual {v1, v0}, Lcom/bytedance/adsdk/ugeno/core/rAx;->Fj(Lorg/json/JSONObject;)V

    invoke-virtual {v1, p0}, Lcom/bytedance/adsdk/ugeno/core/rAx;->Fj(Lcom/bytedance/adsdk/ugeno/component/ex;)V

    invoke-virtual {p3, v1}, Lcom/bytedance/adsdk/ugeno/core/rAx;->Fj(Lcom/bytedance/adsdk/ugeno/core/rAx;)V

    :cond_0
    const-string v0, "fail"

    invoke-virtual {p2, v0}, Lorg/json/JSONObject;->optJSONObject(Ljava/lang/String;)Lorg/json/JSONObject;

    move-result-object v0

    if-eqz v0, :cond_1

    new-instance v1, Lcom/bytedance/adsdk/ugeno/core/rAx;

    invoke-direct {v1}, Lcom/bytedance/adsdk/ugeno/core/rAx;-><init>()V

    invoke-virtual {v1, v0}, Lcom/bytedance/adsdk/ugeno/core/rAx;->Fj(Lorg/json/JSONObject;)V

    invoke-virtual {v1, p0}, Lcom/bytedance/adsdk/ugeno/core/rAx;->Fj(Lcom/bytedance/adsdk/ugeno/component/ex;)V

    invoke-virtual {p3, v1}, Lcom/bytedance/adsdk/ugeno/core/rAx;->ex(Lcom/bytedance/adsdk/ugeno/core/rAx;)V

    :cond_1
    invoke-virtual {p3, p2}, Lcom/bytedance/adsdk/ugeno/core/rAx;->Fj(Lorg/json/JSONObject;)V

    iget-object p2, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->qPr:Ljava/util/Map;

    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p1

    invoke-interface {p2, p1, p3}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    return-void
.end method

.method public Fj(Landroid/graphics/Canvas;Lcom/bytedance/adsdk/ugeno/core/IAnimation;)V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->YH:Lcom/bytedance/adsdk/ugeno/core/BcC;

    if-eqz v0, :cond_0

    invoke-virtual {v0, p1, p2}, Lcom/bytedance/adsdk/ugeno/core/BcC;->Fj(Landroid/graphics/Canvas;Lcom/bytedance/adsdk/ugeno/core/IAnimation;)V

    :cond_0
    return-void
.end method

.method public Fj(Landroid/view/ViewGroup$LayoutParams;)V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->Ubf:Landroid/view/View;

    if-eqz v0, :cond_0

    invoke-virtual {v0, p1}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    :cond_0
    return-void
.end method

.method public Fj(Lcom/bytedance/adsdk/ugeno/component/Fj;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->WR:Lcom/bytedance/adsdk/ugeno/component/Fj;

    return-void
.end method

.method public Fj(Lcom/bytedance/adsdk/ugeno/core/JU;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->haP:Lcom/bytedance/adsdk/ugeno/core/JU;

    return-void
.end method

.method public Fj(Lcom/bytedance/adsdk/ugeno/core/Tc;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->PpV:Lcom/bytedance/adsdk/ugeno/core/Tc;

    return-void
.end method

.method public Fj(Lcom/bytedance/adsdk/ugeno/core/WR$Fj;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->BcC:Lcom/bytedance/adsdk/ugeno/core/WR$Fj;

    return-void
.end method

.method public Fj(Lcom/bytedance/adsdk/ugeno/core/dG;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->mj:Lcom/bytedance/adsdk/ugeno/core/dG;

    return-void
.end method

.method public Fj(Lcom/bytedance/adsdk/ugeno/core/mSE;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->mSE:Lcom/bytedance/adsdk/ugeno/core/mSE;

    return-void
.end method

.method public Fj(Lcom/bytedance/adsdk/ugeno/core/rAx;)V
    .locals 2

    if-eqz p1, :cond_1

    invoke-virtual {p1}, Lcom/bytedance/adsdk/ugeno/core/rAx;->hjc()Lorg/json/JSONObject;

    move-result-object v0

    if-nez v0, :cond_0

    goto :goto_0

    :cond_0
    invoke-virtual {p1}, Lcom/bytedance/adsdk/ugeno/core/rAx;->hjc()Lorg/json/JSONObject;

    move-result-object v0

    const-string v1, "type"

    invoke-virtual {v0, v1}, Lorg/json/JSONObject;->optString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    const-string v1, "onDismiss"

    invoke-static {v0, v1}, Landroid/text/TextUtils;->equals(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Z

    move-result v0

    if-eqz v0, :cond_1

    invoke-virtual {p1}, Lcom/bytedance/adsdk/ugeno/core/rAx;->hjc()Lorg/json/JSONObject;

    move-result-object p1

    const-string v0, "nodeId"

    invoke-virtual {p1, v0}, Lorg/json/JSONObject;->optString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    const/16 v0, 0x8

    invoke-virtual {p0, v0}, Lcom/bytedance/adsdk/ugeno/component/ex;->ex(I)V

    invoke-virtual {p0, p0}, Lcom/bytedance/adsdk/ugeno/component/ex;->ex(Lcom/bytedance/adsdk/ugeno/component/ex;)Lcom/bytedance/adsdk/ugeno/component/ex;

    move-result-object v1

    check-cast v1, Lcom/bytedance/adsdk/ugeno/component/Fj;

    iput-object v1, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->svN:Lcom/bytedance/adsdk/ugeno/component/Fj;

    invoke-static {p1}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v1

    if-nez v1, :cond_1

    iget-object v1, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->svN:Lcom/bytedance/adsdk/ugeno/component/Fj;

    if-eqz v1, :cond_1

    invoke-virtual {v1, p1}, Lcom/bytedance/adsdk/ugeno/component/ex;->ex(Ljava/lang/String;)Lcom/bytedance/adsdk/ugeno/component/ex;

    move-result-object p1

    if-eqz p1, :cond_1

    invoke-virtual {p1, v0}, Lcom/bytedance/adsdk/ugeno/component/ex;->ex(I)V

    :cond_1
    :goto_0
    return-void
.end method

.method public Fj(Lcom/bytedance/adsdk/ugeno/core/svN;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->KZ:Lcom/bytedance/adsdk/ugeno/core/svN;

    return-void
.end method

.method public Fj(Ljava/lang/String;Ljava/lang/String;)V
    .locals 9

    invoke-static {p1}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    if-eqz v0, :cond_0

    return-void

    :cond_0
    invoke-virtual {p1}, Ljava/lang/String;->hashCode()I

    invoke-virtual {p1}, Ljava/lang/String;->hashCode()I

    move-result v0

    const/16 v1, 0x8

    const/4 v2, 0x4

    const/4 v3, 0x0

    const/4 v4, 0x1

    const/4 v5, -0x1

    sparse-switch v0, :sswitch_data_0

    goto/16 :goto_0

    :sswitch_0
    const-string v0, "availability"

    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_1

    goto/16 :goto_0

    :cond_1
    const/16 v5, 0x2a

    goto/16 :goto_0

    :sswitch_1
    const-string v0, "marginLeft"

    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_2

    goto/16 :goto_0

    :cond_2
    const/16 v5, 0x29

    goto/16 :goto_0

    :sswitch_2
    const-string v0, "visibility"

    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_3

    goto/16 :goto_0

    :cond_3
    const/16 v5, 0x28

    goto/16 :goto_0

    :sswitch_3
    const-string v0, "borderRightBottomRadius"

    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_4

    goto/16 :goto_0

    :cond_4
    const/16 v5, 0x27

    goto/16 :goto_0

    :sswitch_4
    const-string v0, "onLongTap"

    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_5

    goto/16 :goto_0

    :cond_5
    const/16 v5, 0x26

    goto/16 :goto_0

    :sswitch_5
    const-string v0, "onScroll"

    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_6

    goto/16 :goto_0

    :cond_6
    const/16 v5, 0x25

    goto/16 :goto_0

    :sswitch_6
    const-string v0, "borderRadius"

    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_7

    goto/16 :goto_0

    :cond_7
    const/16 v5, 0x24

    goto/16 :goto_0

    :sswitch_7
    const-string v0, "borderLeftTopRadius"

    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_8

    goto/16 :goto_0

    :cond_8
    const/16 v5, 0x23

    goto/16 :goto_0

    :sswitch_8
    const-string v0, "onPullToRefresh"

    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_9

    goto/16 :goto_0

    :cond_9
    const/16 v5, 0x22

    goto/16 :goto_0

    :sswitch_9
    const-string v0, "animatorSet"

    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_a

    goto/16 :goto_0

    :cond_a
    const/16 v5, 0x21

    goto/16 :goto_0

    :sswitch_a
    const-string v0, "onAnimation"

    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_b

    goto/16 :goto_0

    :cond_b
    const/16 v5, 0x20

    goto/16 :goto_0

    :sswitch_b
    const-string v0, "marginRight"

    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_c

    goto/16 :goto_0

    :cond_c
    const/16 v5, 0x1f

    goto/16 :goto_0

    :sswitch_c
    const-string v0, "onExposure"

    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_d

    goto/16 :goto_0

    :cond_d
    const/16 v5, 0x1e

    goto/16 :goto_0

    :sswitch_d
    const-string v0, "borderWidth"

    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_e

    goto/16 :goto_0

    :cond_e
    const/16 v5, 0x1d

    goto/16 :goto_0

    :sswitch_e
    const-string v0, "borderColor"

    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_f

    goto/16 :goto_0

    :cond_f
    const/16 v5, 0x1c

    goto/16 :goto_0

    :sswitch_f
    const-string v0, "paddingRight"

    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_10

    goto/16 :goto_0

    :cond_10
    const/16 v5, 0x1b

    goto/16 :goto_0

    :sswitch_10
    const-string v0, "onLoadMore"

    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_11

    goto/16 :goto_0

    :cond_11
    const/16 v5, 0x1a

    goto/16 :goto_0

    :sswitch_11
    const-string v0, "paddingBottom"

    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_12

    goto/16 :goto_0

    :cond_12
    const/16 v5, 0x19

    goto/16 :goto_0

    :sswitch_12
    const-string v0, "width"

    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_13

    goto/16 :goto_0

    :cond_13
    const/16 v5, 0x18

    goto/16 :goto_0

    :sswitch_13
    const-string v0, "ratio"

    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_14

    goto/16 :goto_0

    :cond_14
    const/16 v5, 0x17

    goto/16 :goto_0

    :sswitch_14
    const-string v0, "onTap"

    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_15

    goto/16 :goto_0

    :cond_15
    const/16 v5, 0x16

    goto/16 :goto_0

    :sswitch_15
    const-string v0, "click"

    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_16

    goto/16 :goto_0

    :cond_16
    const/16 v5, 0x15

    goto/16 :goto_0

    :sswitch_16
    const-string v0, "paddingTop"

    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_17

    goto/16 :goto_0

    :cond_17
    const/16 v5, 0x14

    goto/16 :goto_0

    :sswitch_17
    const-string v0, "name"

    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_18

    goto/16 :goto_0

    :cond_18
    const/16 v5, 0x13

    goto/16 :goto_0

    :sswitch_18
    const-string v0, "i18n"

    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_19

    goto/16 :goto_0

    :cond_19
    const/16 v5, 0x12

    goto/16 :goto_0

    :sswitch_19
    const-string v0, "id"

    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_1a

    goto/16 :goto_0

    :cond_1a
    const/16 v5, 0x11

    goto/16 :goto_0

    :sswitch_1a
    const-string v0, "borderLeftBottomRadius"

    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_1b

    goto/16 :goto_0

    :cond_1b
    const/16 v5, 0x10

    goto/16 :goto_0

    :sswitch_1b
    const-string v0, "marginBottom"

    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_1c

    goto/16 :goto_0

    :cond_1c
    const/16 v5, 0xf

    goto/16 :goto_0

    :sswitch_1c
    const-string v0, "triggerFunc"

    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_1d

    goto/16 :goto_0

    :cond_1d
    const/16 v5, 0xe

    goto/16 :goto_0

    :sswitch_1d
    const-string v0, "padding"

    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_1e

    goto/16 :goto_0

    :cond_1e
    const/16 v5, 0xd

    goto/16 :goto_0

    :sswitch_1e
    const-string v0, "backgroundDrawable"

    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_1f

    goto/16 :goto_0

    :cond_1f
    const/16 v5, 0xc

    goto/16 :goto_0

    :sswitch_1f
    const-string v0, "onDown"

    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_20

    goto/16 :goto_0

    :cond_20
    const/16 v5, 0xb

    goto/16 :goto_0

    :sswitch_20
    const-string v0, "marginTop"

    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_21

    goto/16 :goto_0

    :cond_21
    const/16 v5, 0xa

    goto/16 :goto_0

    :sswitch_21
    const-string v0, "borderRightTopRadius"

    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_22

    goto/16 :goto_0

    :cond_22
    const/16 v5, 0x9

    goto/16 :goto_0

    :sswitch_22
    const-string v0, "margin"

    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_23

    goto/16 :goto_0

    :cond_23
    const/16 v5, 0x8

    goto/16 :goto_0

    :sswitch_23
    const-string v0, "height"

    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_24

    goto :goto_0

    :cond_24
    const/4 v5, 0x7

    goto :goto_0

    :sswitch_24
    const-string v0, "background"

    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_25

    goto :goto_0

    :cond_25
    const/4 v5, 0x6

    goto :goto_0

    :sswitch_25
    const-string v0, "onTimer"

    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_26

    goto :goto_0

    :cond_26
    const/4 v5, 0x5

    goto :goto_0

    :sswitch_26
    const-string v0, "onSlide"

    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_27

    goto :goto_0

    :cond_27
    const/4 v5, 0x4

    goto :goto_0

    :sswitch_27
    const-string v0, "onShake"

    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_28

    goto :goto_0

    :cond_28
    const/4 v5, 0x3

    goto :goto_0

    :sswitch_28
    const-string v0, "onDelay"

    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_29

    goto :goto_0

    :cond_29
    const/4 v5, 0x2

    goto :goto_0

    :sswitch_29
    const-string v0, "paddingLeft"

    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_2a

    goto :goto_0

    :cond_2a
    const/4 v5, 0x1

    goto :goto_0

    :sswitch_2a
    const-string v0, "clickable"

    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_2b

    goto :goto_0

    :cond_2b
    const/4 v5, 0x0

    :goto_0
    const/high16 v0, -0x40000000    # -2.0f

    const-string v6, "wrap_content"

    const/high16 v7, -0x40800000    # -1.0f

    const-string v8, "match_parent"

    packed-switch v5, :pswitch_data_0

    goto/16 :goto_2

    :pswitch_0
    const-string p1, "unavailable"

    invoke-static {p2, p1}, Landroid/text/TextUtils;->equals(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Z

    move-result p1

    xor-int/2addr p1, v4

    iput-boolean p1, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->oX:Z

    return-void

    :pswitch_1
    iget-object p1, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->ex:Landroid/content/Context;

    invoke-static {p1, p2}, Lcom/bytedance/adsdk/ugeno/ex/BcC;->Fj(Landroid/content/Context;Ljava/lang/String;)F

    move-result p1

    iput p1, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->JW:F

    return-void

    :pswitch_2
    const-string p1, "visible"

    invoke-static {p1, p2}, Landroid/text/TextUtils;->equals(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Z

    move-result p1

    if-eqz p1, :cond_2c

    iput v3, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->iT:I

    return-void

    :cond_2c
    const-string p1, "invisible"

    invoke-static {p1, p2}, Landroid/text/TextUtils;->equals(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Z

    move-result p1

    if-eqz p1, :cond_2d

    iput v2, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->iT:I

    return-void

    :cond_2d
    const-string p1, "gone"

    invoke-static {p1, p2}, Landroid/text/TextUtils;->equals(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Z

    move-result p1

    if-eqz p1, :cond_30

    iput v1, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->iT:I

    return-void

    :pswitch_3
    iget-object p1, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->ex:Landroid/content/Context;

    invoke-static {p1, p2}, Lcom/bytedance/adsdk/ugeno/ex/BcC;->Fj(Landroid/content/Context;Ljava/lang/String;)F

    move-result p1

    iput p1, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->uM:F

    return-void

    :pswitch_4
    iget-object p1, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->ex:Landroid/content/Context;

    invoke-static {p1, p2}, Lcom/bytedance/adsdk/ugeno/ex/BcC;->Fj(Landroid/content/Context;Ljava/lang/String;)F

    move-result p1

    iput p1, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->rXP:F

    return-void

    :pswitch_5
    iget-object p1, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->ex:Landroid/content/Context;

    invoke-static {p1, p2}, Lcom/bytedance/adsdk/ugeno/ex/BcC;->Fj(Landroid/content/Context;Ljava/lang/String;)F

    move-result p1

    iput p1, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->fj:F

    return-void

    :pswitch_6
    invoke-static {p2, p0}, Lcom/bytedance/adsdk/ugeno/core/Fj;->Fj(Ljava/lang/String;Lcom/bytedance/adsdk/ugeno/component/ex;)Lcom/bytedance/adsdk/ugeno/core/Fj;

    move-result-object p1

    iput-object p1, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->Obv:Lcom/bytedance/adsdk/ugeno/core/Fj;

    return-void

    :pswitch_7
    iget-object p1, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->ex:Landroid/content/Context;

    invoke-static {p1, p2}, Lcom/bytedance/adsdk/ugeno/ex/BcC;->Fj(Landroid/content/Context;Ljava/lang/String;)F

    move-result p1

    iput p1, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->JU:F

    return-void

    :pswitch_8
    iget-object p1, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->ex:Landroid/content/Context;

    invoke-static {p1, p2}, Lcom/bytedance/adsdk/ugeno/ex/BcC;->Fj(Landroid/content/Context;Ljava/lang/String;)F

    move-result p1

    iput p1, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->gXF:F

    return-void

    :pswitch_9
    invoke-static {p2}, Lcom/bytedance/adsdk/ugeno/ex/Fj;->Fj(Ljava/lang/String;)I

    move-result p1

    iput p1, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->spi:I

    return-void

    :pswitch_a
    iget-object p1, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->ex:Landroid/content/Context;

    invoke-static {p1, p2}, Lcom/bytedance/adsdk/ugeno/ex/BcC;->Fj(Landroid/content/Context;Ljava/lang/String;)F

    move-result p1

    iput p1, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->Af:F

    iput-boolean v4, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->Vq:Z

    return-void

    :pswitch_b
    iget-object p1, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->ex:Landroid/content/Context;

    invoke-static {p1, p2}, Lcom/bytedance/adsdk/ugeno/ex/BcC;->Fj(Landroid/content/Context;Ljava/lang/String;)F

    move-result p1

    iput p1, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->cB:F

    iput-boolean v4, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->rf:Z

    return-void

    :pswitch_c
    invoke-static {p2, v8}, Landroid/text/TextUtils;->equals(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Z

    move-result p1

    if-eqz p1, :cond_2e

    iput v7, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->UYd:F

    goto :goto_1

    :cond_2e
    invoke-static {p2, v6}, Landroid/text/TextUtils;->equals(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Z

    move-result p1

    if-eqz p1, :cond_2f

    iput v0, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->UYd:F

    goto :goto_1

    :cond_2f
    iget-object p1, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->ex:Landroid/content/Context;

    invoke-static {p1, p2}, Lcom/bytedance/adsdk/ugeno/ex/BcC;->Fj(Landroid/content/Context;Ljava/lang/String;)F

    move-result p1

    iput p1, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->UYd:F

    :goto_1
    iput-boolean v4, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->Nyg:Z

    return-void

    :pswitch_d
    const/4 p1, 0x0

    invoke-static {p2, p1}, Lcom/bytedance/adsdk/ugeno/ex/hjc;->Fj(Ljava/lang/String;F)F

    move-result p1

    iput p1, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->efV:F

    return-void

    :pswitch_e
    iput-object p2, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->OK:Ljava/lang/String;

    return-void

    :pswitch_f
    iget-object p1, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->ex:Landroid/content/Context;

    invoke-static {p1, p2}, Lcom/bytedance/adsdk/ugeno/ex/BcC;->Fj(Landroid/content/Context;Ljava/lang/String;)F

    move-result p1

    iput p1, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->mC:F

    iput-boolean v4, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->Moo:Z

    return-void

    :pswitch_10
    iput-object p2, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->rAx:Ljava/lang/String;

    return-void

    :pswitch_11
    const/4 p1, 0x0

    invoke-static {p2, p1}, Lcom/bytedance/adsdk/ugeno/ex/ex;->Fj(Ljava/lang/String;Lorg/json/JSONObject;)Lorg/json/JSONObject;

    move-result-object p1

    iput-object p1, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->Gv:Lorg/json/JSONObject;

    return-void

    :pswitch_12
    iput-object p2, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->Ko:Ljava/lang/String;

    return-void

    :pswitch_13
    iget-object p1, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->ex:Landroid/content/Context;

    invoke-static {p1, p2}, Lcom/bytedance/adsdk/ugeno/ex/BcC;->Fj(Landroid/content/Context;Ljava/lang/String;)F

    move-result p1

    iput p1, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->eh:F

    return-void

    :pswitch_14
    iget-object p1, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->ex:Landroid/content/Context;

    invoke-static {p1, p2}, Lcom/bytedance/adsdk/ugeno/ex/BcC;->Fj(Landroid/content/Context;Ljava/lang/String;)F

    move-result p1

    iput p1, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->rS:F

    return-void

    :pswitch_15
    iput-object p2, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->At:Ljava/lang/String;

    :cond_30
    :goto_2
    return-void

    :pswitch_16
    iget-object p1, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->ex:Landroid/content/Context;

    invoke-static {p1, p2}, Lcom/bytedance/adsdk/ugeno/ex/BcC;->Fj(Landroid/content/Context;Ljava/lang/String;)F

    move-result p1

    iput p1, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->vYf:F

    return-void

    :pswitch_17
    iput-object p2, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->lv:Ljava/lang/String;

    return-void

    :pswitch_18
    iget-object p1, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->ex:Landroid/content/Context;

    invoke-static {p1, p2}, Lcom/bytedance/adsdk/ugeno/ex/BcC;->Fj(Landroid/content/Context;Ljava/lang/String;)F

    move-result p1

    iput p1, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->Ql:F

    return-void

    :pswitch_19
    iget-object p1, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->ex:Landroid/content/Context;

    invoke-static {p1, p2}, Lcom/bytedance/adsdk/ugeno/ex/BcC;->Fj(Landroid/content/Context;Ljava/lang/String;)F

    move-result p1

    iput p1, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->kF:F

    return-void

    :pswitch_1a
    iget-object p1, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->ex:Landroid/content/Context;

    invoke-static {p1, p2}, Lcom/bytedance/adsdk/ugeno/ex/BcC;->Fj(Landroid/content/Context;Ljava/lang/String;)F

    move-result p1

    iput p1, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->Tc:F

    return-void

    :pswitch_1b
    invoke-static {p2, v8}, Landroid/text/TextUtils;->equals(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Z

    move-result p1

    if-eqz p1, :cond_31

    iput v7, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->dG:F

    goto :goto_3

    :cond_31
    invoke-static {p2, v6}, Landroid/text/TextUtils;->equals(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Z

    move-result p1

    if-eqz p1, :cond_32

    iput v0, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->dG:F

    goto :goto_3

    :cond_32
    iget-object p1, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->ex:Landroid/content/Context;

    invoke-static {p1, p2}, Lcom/bytedance/adsdk/ugeno/ex/BcC;->Fj(Landroid/content/Context;Ljava/lang/String;)F

    move-result p1

    iput p1, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->dG:F

    :goto_3
    iput-boolean v4, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->HQ:Z

    return-void

    :pswitch_1c
    invoke-static {p2}, Lcom/bytedance/adsdk/ugeno/ex/Fj;->hjc(Ljava/lang/String;)Z

    move-result p1

    if-eqz p1, :cond_33

    iput-boolean v4, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->gci:Z

    invoke-static {p2}, Lcom/bytedance/adsdk/ugeno/ex/Fj;->ex(Ljava/lang/String;)Lcom/bytedance/adsdk/ugeno/ex/Fj$Fj;

    move-result-object p1

    iput-object p1, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->Bzy:Lcom/bytedance/adsdk/ugeno/ex/Fj$Fj;

    return-void

    :cond_33
    invoke-static {p2}, Lcom/bytedance/adsdk/ugeno/ex/Fj;->Fj(Ljava/lang/String;)I

    move-result p1

    iput p1, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->uy:I

    iput-boolean v3, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->gci:Z

    return-void

    :pswitch_1d
    invoke-virtual {p0, p1, p2}, Lcom/bytedance/adsdk/ugeno/component/ex;->ex(Ljava/lang/String;Ljava/lang/String;)V

    return-void

    :pswitch_1e
    iget-object p1, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->ex:Landroid/content/Context;

    invoke-static {p1, p2}, Lcom/bytedance/adsdk/ugeno/ex/BcC;->Fj(Landroid/content/Context;Ljava/lang/String;)F

    move-result p1

    iput p1, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->mE:F

    iput-boolean v4, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->nsB:Z

    return-void

    :pswitch_1f
    invoke-static {p2, v4}, Lcom/bytedance/adsdk/ugeno/ex/hjc;->Fj(Ljava/lang/String;Z)Z

    move-result p1

    iput-boolean p1, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->Eev:Z

    return-void

    :sswitch_data_0
    .sparse-switch
        -0x751aa91e -> :sswitch_2a
        -0x597a2048 -> :sswitch_29
        -0x5089711c -> :sswitch_28
        -0x4fb4db99 -> :sswitch_27
        -0x4fb2ecee -> :sswitch_26
        -0x4fa6235a -> :sswitch_25
        -0x4f67aad2 -> :sswitch_24
        -0x48c76ed9 -> :sswitch_23
        -0x40737a52 -> :sswitch_22
        -0x3ee27929 -> :sswitch_21
        -0x3e464339 -> :sswitch_20
        -0x3c6760df -> :sswitch_1f
        -0x37a9d414 -> :sswitch_1e
        -0x300fc3ef -> :sswitch_1d
        -0x289caf64 -> :sswitch_1c
        -0x113c6e87 -> :sswitch_1b
        -0xab09770 -> :sswitch_1a
        0xd1b -> :sswitch_19
        0x307a1e -> :sswitch_18
        0x337a8b -> :sswitch_17
        0x55f4784 -> :sswitch_16
        0x5a5c588 -> :sswitch_15
        0x64f7944 -> :sswitch_14
        0x674500b -> :sswitch_13
        0x6be2dc6 -> :sswitch_12
        0xc0fb19c -> :sswitch_11
        0x1318b45a -> :sswitch_10
        0x2a8c788b -> :sswitch_f
        0x2b158697 -> :sswitch_e
        0x2c2c84fa -> :sswitch_d
        0x324da006 -> :sswitch_c
        0x3a1ea90e -> :sswitch_b
        0x40d55865 -> :sswitch_a
        0x44a7dbfb -> :sswitch_9
        0x450b7f7c -> :sswitch_8
        0x4b158134 -> :sswitch_7
        0x506afbde -> :sswitch_6
        0x58dabd8c -> :sswitch_5
        0x646f20a8 -> :sswitch_4
        0x64d75c0d -> :sswitch_3
        0x73b66312 -> :sswitch_2
        0x757a12d5 -> :sswitch_1
        0x7710155b -> :sswitch_0
    .end sparse-switch

    :pswitch_data_0
    .packed-switch 0x0
        :pswitch_1f
        :pswitch_1e
        :pswitch_1d
        :pswitch_1d
        :pswitch_1d
        :pswitch_1d
        :pswitch_1c
        :pswitch_1b
        :pswitch_1a
        :pswitch_19
        :pswitch_18
        :pswitch_1d
        :pswitch_17
        :pswitch_16
        :pswitch_15
        :pswitch_14
        :pswitch_13
        :pswitch_12
        :pswitch_11
        :pswitch_10
        :pswitch_f
        :pswitch_e
        :pswitch_1d
        :pswitch_d
        :pswitch_c
        :pswitch_b
        :pswitch_1d
        :pswitch_a
        :pswitch_9
        :pswitch_8
        :pswitch_1d
        :pswitch_7
        :pswitch_1d
        :pswitch_6
        :pswitch_1d
        :pswitch_5
        :pswitch_4
        :pswitch_1d
        :pswitch_1d
        :pswitch_3
        :pswitch_2
        :pswitch_1
        :pswitch_0
    .end packed-switch
.end method

.method public Fj(Lorg/json/JSONObject;)V
    .locals 5

    iput-object p1, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->eV:Lorg/json/JSONObject;

    iget-object v0, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->hjc:Lorg/json/JSONObject;

    if-nez v0, :cond_0

    return-void

    :cond_0
    invoke-virtual {v0}, Lorg/json/JSONObject;->keys()Ljava/util/Iterator;

    move-result-object v0

    iget-object v1, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->WR:Lcom/bytedance/adsdk/ugeno/component/Fj;

    instance-of v2, v1, Lcom/bytedance/adsdk/ugeno/component/Fj;

    if-eqz v2, :cond_1

    invoke-virtual {v1}, Lcom/bytedance/adsdk/ugeno/component/Fj;->BcC()Lcom/bytedance/adsdk/ugeno/component/Fj$Fj;

    move-result-object v1

    goto :goto_0

    :cond_1
    const/4 v1, 0x0

    :cond_2
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_3

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Ljava/lang/String;

    iget-object v3, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->hjc:Lorg/json/JSONObject;

    invoke-virtual {v3, v2}, Lorg/json/JSONObject;->optString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v3

    invoke-static {v3, p1}, Lcom/bytedance/adsdk/ugeno/Fj/hjc;->Fj(Ljava/lang/String;Lorg/json/JSONObject;)Ljava/lang/String;

    move-result-object v3

    invoke-virtual {p0, v2, v3}, Lcom/bytedance/adsdk/ugeno/component/ex;->Fj(Ljava/lang/String;Ljava/lang/String;)V

    if-eqz v1, :cond_2

    iget-object v4, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->ex:Landroid/content/Context;

    invoke-virtual {v1, v4, v2, v3}, Lcom/bytedance/adsdk/ugeno/component/Fj$Fj;->Fj(Landroid/content/Context;Ljava/lang/String;Ljava/lang/String;)V

    goto :goto_0

    :cond_3
    if-eqz v1, :cond_4

    invoke-virtual {v1}, Lcom/bytedance/adsdk/ugeno/component/Fj$Fj;->Fj()Landroid/view/ViewGroup$LayoutParams;

    move-result-object p1

    invoke-virtual {p0, p1}, Lcom/bytedance/adsdk/ugeno/component/ex;->Fj(Landroid/view/ViewGroup$LayoutParams;)V

    :cond_4
    iget-object p1, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->Gv:Lorg/json/JSONObject;

    if-eqz p1, :cond_5

    iget-object v0, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->eV:Lorg/json/JSONObject;

    if-eqz v0, :cond_5

    :try_start_0
    const-string v1, "i18n"

    invoke-virtual {v0, v1, p1}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    new-instance p1, Ljava/lang/StringBuilder;

    const-string v0, "id: "

    invoke-direct {p1, v0}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    iget-object v0, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->Ko:Ljava/lang/String;

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v0, "; "

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v0, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->Gv:Lorg/json/JSONObject;

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;
    :try_end_0
    .catch Lorg/json/JSONException; {:try_start_0 .. :try_end_0} :catch_0

    :catch_0
    :cond_5
    return-void
.end method

.method public Fj(I)Z
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->qPr:Ljava/util/Map;

    if-eqz v0, :cond_0

    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p1

    invoke-interface {v0, p1}, Ljava/util/Map;->containsKey(Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_0

    const/4 p1, 0x1

    return p1

    :cond_0
    const/4 p1, 0x0

    return p1
.end method

.method public Fj(II)[I
    .locals 4

    iget v0, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->efV:F

    const/4 v1, 0x0

    cmpl-float v0, v0, v1

    if-lez v0, :cond_1

    iget-boolean v0, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->Nyg:Z

    const/high16 v2, 0x40000000    # 2.0f

    if-eqz v0, :cond_0

    invoke-static {p1}, Landroid/view/View$MeasureSpec;->getSize(I)I

    move-result v0

    iget v3, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->efV:F

    cmpl-float v1, v3, v1

    if-eqz v1, :cond_1

    int-to-float p2, v0

    div-float/2addr p2, v3

    float-to-int p2, p2

    invoke-static {p2, v2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    move-result p2

    goto :goto_0

    :cond_0
    iget-boolean v0, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->HQ:Z

    if-eqz v0, :cond_1

    invoke-static {p2}, Landroid/view/View$MeasureSpec;->getSize(I)I

    move-result v0

    iget v3, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->efV:F

    cmpl-float v1, v3, v1

    if-eqz v1, :cond_1

    int-to-float p1, v0

    mul-float p1, p1, v3

    float-to-int p1, p1

    invoke-static {p1, v2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    move-result p1

    :cond_1
    :goto_0
    iget-object v0, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->haP:Lcom/bytedance/adsdk/ugeno/core/JU;

    if-eqz v0, :cond_2

    iget-boolean v0, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->ei:Z

    if-nez v0, :cond_2

    const/4 v0, 0x1

    iput-boolean v0, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->ei:Z

    :cond_2
    filled-new-array {p1, p2}, [I

    move-result-object p1

    return-object p1
.end method

.method public JU()I
    .locals 1

    iget v0, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->dG:F

    float-to-int v0, v0

    return v0
.end method

.method public JW()I
    .locals 1

    iget v0, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->UYd:F

    float-to-int v0, v0

    return v0
.end method

.method public Ko()Lorg/json/JSONObject;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->eV:Lorg/json/JSONObject;

    return-object v0
.end method

.method public Ql()Z
    .locals 1

    iget-boolean v0, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->oX:Z

    return v0
.end method

.method public Tc()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->rAx:Ljava/lang/String;

    return-object v0
.end method

.method public UYd()Lcom/bytedance/adsdk/ugeno/component/Fj;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->WR:Lcom/bytedance/adsdk/ugeno/component/Fj;

    return-object v0
.end method

.method public Ubf()V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->haP:Lcom/bytedance/adsdk/ugeno/core/JU;

    if-eqz v0, :cond_0

    iget-boolean v0, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->ks:Z

    if-nez v0, :cond_0

    const/4 v0, 0x1

    iput-boolean v0, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->ks:Z

    :cond_0
    return-void
.end method

.method public WR()V
    .locals 3

    iget-object v0, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->Obv:Lcom/bytedance/adsdk/ugeno/core/Fj;

    if-eqz v0, :cond_0

    new-instance v1, Lcom/bytedance/adsdk/ugeno/core/BcC;

    iget-object v2, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->Ubf:Landroid/view/View;

    invoke-direct {v1, v2, v0}, Lcom/bytedance/adsdk/ugeno/core/BcC;-><init>(Landroid/view/View;Lcom/bytedance/adsdk/ugeno/core/Fj;)V

    iput-object v1, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->YH:Lcom/bytedance/adsdk/ugeno/core/BcC;

    invoke-virtual {v1}, Lcom/bytedance/adsdk/ugeno/core/BcC;->Fj()V

    :cond_0
    iget-object v0, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->Kk:Lcom/bytedance/adsdk/ugeno/core/ex/ex;

    if-eqz v0, :cond_1

    const/16 v0, 0xa

    invoke-virtual {p0, v0}, Lcom/bytedance/adsdk/ugeno/component/ex;->Fj(I)Z

    move-result v0

    if-eqz v0, :cond_1

    iget-object v0, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->Kk:Lcom/bytedance/adsdk/ugeno/core/ex/ex;

    invoke-virtual {v0}, Lcom/bytedance/adsdk/ugeno/core/ex/ex;->Fj()V

    :cond_1
    iget-object v0, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->HY:Lcom/bytedance/adsdk/ugeno/core/ex/Ubf;

    if-eqz v0, :cond_2

    const/16 v0, 0x9

    invoke-virtual {p0, v0}, Lcom/bytedance/adsdk/ugeno/component/ex;->Fj(I)Z

    move-result v0

    if-eqz v0, :cond_2

    iget-object v0, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->HY:Lcom/bytedance/adsdk/ugeno/core/ex/Ubf;

    invoke-virtual {v0}, Lcom/bytedance/adsdk/ugeno/core/ex/Ubf;->Fj()V

    :cond_2
    return-void
.end method

.method public dG()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->Ko:Ljava/lang/String;

    return-object v0
.end method

.method public eV()V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->haP:Lcom/bytedance/adsdk/ugeno/core/JU;

    if-eqz v0, :cond_0

    iget-boolean v0, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->flF:Z

    if-nez v0, :cond_0

    const/4 v0, 0x1

    iput-boolean v0, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->flF:Z

    :cond_0
    return-void
.end method

.method public eV(Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->rAx:Ljava/lang/String;

    return-void
.end method

.method public ex(Lcom/bytedance/adsdk/ugeno/component/ex;)Lcom/bytedance/adsdk/ugeno/component/ex;
    .locals 1

    invoke-virtual {p1}, Lcom/bytedance/adsdk/ugeno/component/ex;->UYd()Lcom/bytedance/adsdk/ugeno/component/Fj;

    move-result-object v0

    if-nez v0, :cond_0

    instance-of v0, p1, Lcom/bytedance/adsdk/ugeno/component/Fj;

    if-eqz v0, :cond_0

    return-object p1

    :cond_0
    invoke-virtual {p1}, Lcom/bytedance/adsdk/ugeno/component/ex;->UYd()Lcom/bytedance/adsdk/ugeno/component/Fj;

    move-result-object p1

    invoke-virtual {p0, p1}, Lcom/bytedance/adsdk/ugeno/component/ex;->ex(Lcom/bytedance/adsdk/ugeno/component/ex;)Lcom/bytedance/adsdk/ugeno/component/ex;

    move-result-object p1

    return-object p1
.end method

.method public ex(Ljava/lang/String;)Lcom/bytedance/adsdk/ugeno/component/ex;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            ")",
            "Lcom/bytedance/adsdk/ugeno/component/ex<",
            "TT;>;"
        }
    .end annotation

    invoke-virtual {p0, p1}, Lcom/bytedance/adsdk/ugeno/component/ex;->Fj(Ljava/lang/String;)Lcom/bytedance/adsdk/ugeno/component/ex;

    move-result-object p1

    return-object p1
.end method

.method public ex()V
    .locals 7
    .annotation build Landroid/annotation/SuppressLint;
        value = {
            "ClickableViewAccessibility"
        }
    .end annotation

    invoke-direct {p0}, Lcom/bytedance/adsdk/ugeno/component/ex;->Fj()V

    iget-boolean v0, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->nsB:Z

    if-eqz v0, :cond_0

    iget v0, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->mE:F

    goto :goto_0

    :cond_0
    iget v0, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->vYf:F

    :goto_0
    iget-boolean v1, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->Vq:Z

    if-eqz v1, :cond_1

    iget v1, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->Af:F

    goto :goto_1

    :cond_1
    iget v1, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->vYf:F

    :goto_1
    iget-boolean v2, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->Moo:Z

    if-eqz v2, :cond_2

    iget v2, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->mC:F

    goto :goto_2

    :cond_2
    iget v2, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->vYf:F

    :goto_2
    iget-boolean v3, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->rf:Z

    if-eqz v3, :cond_3

    iget v3, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->cB:F

    goto :goto_3

    :cond_3
    iget v3, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->vYf:F

    :goto_3
    iget-object v4, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->Ubf:Landroid/view/View;

    float-to-int v0, v0

    float-to-int v2, v2

    float-to-int v1, v1

    float-to-int v3, v3

    invoke-virtual {v4, v0, v2, v1, v3}, Landroid/view/View;->setPadding(IIII)V

    iget-object v0, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->Ubf:Landroid/view/View;

    iget v1, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->iT:I

    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    iget-object v0, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->BcC:Lcom/bytedance/adsdk/ugeno/core/WR$Fj;

    const/4 v1, 0x1

    if-eqz v0, :cond_4

    invoke-virtual {v0}, Lcom/bytedance/adsdk/ugeno/core/WR$Fj;->ex()Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    if-eqz v0, :cond_4

    iget-object v0, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->Ubf:Landroid/view/View;

    new-instance v2, Lcom/bytedance/adsdk/ugeno/component/ex$1;

    invoke-direct {v2, p0}, Lcom/bytedance/adsdk/ugeno/component/ex$1;-><init>(Lcom/bytedance/adsdk/ugeno/component/ex;)V

    invoke-virtual {v0, v2}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    goto :goto_4

    :cond_4
    invoke-virtual {p0, v1}, Lcom/bytedance/adsdk/ugeno/component/ex;->Fj(I)Z

    move-result v0

    if-eqz v0, :cond_5

    iget-boolean v0, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->qg:Z

    if-nez v0, :cond_5

    iget-object v0, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->Ubf:Landroid/view/View;

    new-instance v2, Lcom/bytedance/adsdk/ugeno/component/ex$2;

    invoke-direct {v2, p0}, Lcom/bytedance/adsdk/ugeno/component/ex$2;-><init>(Lcom/bytedance/adsdk/ugeno/component/ex;)V

    invoke-virtual {v0, v2}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    :cond_5
    :goto_4
    iget-object v0, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->mj:Lcom/bytedance/adsdk/ugeno/core/dG;

    const/4 v2, 0x0

    if-eqz v0, :cond_7

    const/4 v0, 0x4

    invoke-virtual {p0, v0}, Lcom/bytedance/adsdk/ugeno/component/ex;->Fj(I)Z

    move-result v3

    if-eqz v3, :cond_7

    invoke-virtual {p0, v1}, Lcom/bytedance/adsdk/ugeno/component/ex;->Fj(I)Z

    move-result v3

    if-eqz v3, :cond_6

    iput-boolean v1, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->Jq:Z

    new-instance v3, Lcom/bytedance/adsdk/ugeno/core/ex/eV;

    iget-object v4, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->ex:Landroid/content/Context;

    iget-object v5, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->qPr:Ljava/util/Map;

    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v0

    invoke-interface {v5, v0}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/bytedance/adsdk/ugeno/core/rAx;

    iget-object v5, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->qPr:Ljava/util/Map;

    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v6

    invoke-interface {v5, v6}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Lcom/bytedance/adsdk/ugeno/core/rAx;

    iget-boolean v6, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->Jq:Z

    invoke-direct {v3, v4, v0, v5, v6}, Lcom/bytedance/adsdk/ugeno/core/ex/eV;-><init>(Landroid/content/Context;Lcom/bytedance/adsdk/ugeno/core/rAx;Lcom/bytedance/adsdk/ugeno/core/rAx;Z)V

    goto :goto_5

    :cond_6
    new-instance v3, Lcom/bytedance/adsdk/ugeno/core/ex/eV;

    iget-object v4, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->ex:Landroid/content/Context;

    iget-object v5, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->qPr:Ljava/util/Map;

    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v0

    invoke-interface {v5, v0}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/bytedance/adsdk/ugeno/core/rAx;

    iget-boolean v5, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->Jq:Z

    invoke-direct {v3, v4, v0, v5}, Lcom/bytedance/adsdk/ugeno/core/ex/eV;-><init>(Landroid/content/Context;Lcom/bytedance/adsdk/ugeno/core/rAx;Z)V

    goto :goto_5

    :cond_7
    move-object v3, v2

    :goto_5
    iget-object v0, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->mj:Lcom/bytedance/adsdk/ugeno/core/dG;

    if-eqz v0, :cond_8

    invoke-virtual {p0, v1}, Lcom/bytedance/adsdk/ugeno/component/ex;->Fj(I)Z

    move-result v0

    if-eqz v0, :cond_8

    iget-boolean v0, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->qg:Z

    if-eqz v0, :cond_8

    new-instance v2, Lcom/bytedance/adsdk/ugeno/core/ex/hjc;

    iget-object v0, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->ex:Landroid/content/Context;

    iget-object v4, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->qPr:Ljava/util/Map;

    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    invoke-interface {v4, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/bytedance/adsdk/ugeno/core/rAx;

    invoke-direct {v2, v0, v1}, Lcom/bytedance/adsdk/ugeno/core/ex/hjc;-><init>(Landroid/content/Context;Lcom/bytedance/adsdk/ugeno/core/rAx;)V

    :cond_8
    iget-object v0, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->mj:Lcom/bytedance/adsdk/ugeno/core/dG;

    if-eqz v0, :cond_9

    const/4 v0, 0x3

    invoke-virtual {p0, v0}, Lcom/bytedance/adsdk/ugeno/component/ex;->Fj(I)Z

    move-result v0

    if-eqz v0, :cond_9

    new-instance v0, Lcom/bytedance/adsdk/ugeno/core/ex/Fj;

    iget-object v1, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->ex:Landroid/content/Context;

    invoke-direct {v0, v1}, Lcom/bytedance/adsdk/ugeno/core/ex/Fj;-><init>(Landroid/content/Context;)V

    iput-object v0, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->cs:Lcom/bytedance/adsdk/ugeno/core/ex/Fj;

    new-instance v0, Lcom/bytedance/adsdk/ugeno/component/ex$3;

    invoke-direct {v0, p0}, Lcom/bytedance/adsdk/ugeno/component/ex$3;-><init>(Lcom/bytedance/adsdk/ugeno/component/ex;)V

    :cond_9
    iget-object v0, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->mj:Lcom/bytedance/adsdk/ugeno/core/dG;

    if-eqz v0, :cond_a

    const/16 v0, 0x9

    invoke-virtual {p0, v0}, Lcom/bytedance/adsdk/ugeno/component/ex;->Fj(I)Z

    move-result v1

    if-eqz v1, :cond_a

    new-instance v1, Lcom/bytedance/adsdk/ugeno/core/ex/Ubf;

    iget-object v4, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->ex:Landroid/content/Context;

    iget-object v5, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->qPr:Ljava/util/Map;

    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v0

    invoke-interface {v5, v0}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/bytedance/adsdk/ugeno/core/rAx;

    invoke-direct {v1, v4, v0, p0}, Lcom/bytedance/adsdk/ugeno/core/ex/Ubf;-><init>(Landroid/content/Context;Lcom/bytedance/adsdk/ugeno/core/rAx;Lcom/bytedance/adsdk/ugeno/component/ex;)V

    iput-object v1, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->HY:Lcom/bytedance/adsdk/ugeno/core/ex/Ubf;

    iget-object v0, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->mj:Lcom/bytedance/adsdk/ugeno/core/dG;

    invoke-virtual {v1, v0}, Lcom/bytedance/adsdk/ugeno/core/ex/Ubf;->Fj(Lcom/bytedance/adsdk/ugeno/core/dG;)V

    :cond_a
    const/16 v0, 0xa

    invoke-virtual {p0, v0}, Lcom/bytedance/adsdk/ugeno/component/ex;->Fj(I)Z

    move-result v1

    if-eqz v1, :cond_b

    new-instance v1, Lcom/bytedance/adsdk/ugeno/core/ex/ex;

    iget-object v4, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->ex:Landroid/content/Context;

    iget-object v5, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->qPr:Ljava/util/Map;

    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v0

    invoke-interface {v5, v0}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/bytedance/adsdk/ugeno/core/rAx;

    invoke-direct {v1, v4, v0, p0}, Lcom/bytedance/adsdk/ugeno/core/ex/ex;-><init>(Landroid/content/Context;Lcom/bytedance/adsdk/ugeno/core/rAx;Lcom/bytedance/adsdk/ugeno/component/ex;)V

    iput-object v1, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->Kk:Lcom/bytedance/adsdk/ugeno/core/ex/ex;

    iget-object v0, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->mj:Lcom/bytedance/adsdk/ugeno/core/dG;

    invoke-virtual {v1, v0}, Lcom/bytedance/adsdk/ugeno/core/ex/ex;->Fj(Lcom/bytedance/adsdk/ugeno/core/dG;)V

    :cond_b
    iget-object v0, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->Ubf:Landroid/view/View;

    new-instance v1, Lcom/bytedance/adsdk/ugeno/component/ex$4;

    invoke-direct {v1, p0, v2, v3}, Lcom/bytedance/adsdk/ugeno/component/ex$4;-><init>(Lcom/bytedance/adsdk/ugeno/component/ex;Lcom/bytedance/adsdk/ugeno/core/ex/hjc;Lcom/bytedance/adsdk/ugeno/core/ex/eV;)V

    invoke-virtual {v0, v1}, Landroid/view/View;->setOnTouchListener(Landroid/view/View$OnTouchListener;)V

    return-void
.end method

.method public ex(I)V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->Ubf:Landroid/view/View;

    invoke-virtual {v0, p1}, Landroid/view/View;->setVisibility(I)V

    return-void
.end method

.method public ex(IIII)V
    .locals 0

    iget-object p3, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->YH:Lcom/bytedance/adsdk/ugeno/core/BcC;

    if-eqz p3, :cond_0

    invoke-virtual {p3, p1, p2}, Lcom/bytedance/adsdk/ugeno/core/BcC;->Fj(II)V

    :cond_0
    return-void
.end method

.method public ex(Ljava/lang/String;Ljava/lang/String;)V
    .locals 3

    invoke-static {p2}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    if-nez v0, :cond_4

    iget-object v0, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->qPr:Ljava/util/Map;

    if-nez v0, :cond_0

    goto :goto_1

    :cond_0
    :try_start_0
    invoke-static {p1}, Lcom/bytedance/adsdk/ugeno/core/UYd;->Fj(Ljava/lang/String;)Lcom/bytedance/adsdk/ugeno/core/UYd;

    move-result-object p1

    invoke-virtual {p1}, Lcom/bytedance/adsdk/ugeno/core/UYd;->Fj()I

    move-result p1

    new-instance v0, Lcom/bytedance/adsdk/ugeno/core/rAx;

    invoke-direct {v0}, Lcom/bytedance/adsdk/ugeno/core/rAx;-><init>()V

    invoke-virtual {v0, p1}, Lcom/bytedance/adsdk/ugeno/core/rAx;->Fj(I)V

    invoke-virtual {v0, p0}, Lcom/bytedance/adsdk/ugeno/core/rAx;->Fj(Lcom/bytedance/adsdk/ugeno/component/ex;)V

    new-instance v1, Lorg/json/JSONObject;

    invoke-direct {v1, p2}, Lorg/json/JSONObject;-><init>(Ljava/lang/String;)V

    const/4 p2, 0x3

    if-ne p1, p2, :cond_1

    const-string p2, "shakeAmplitude"

    invoke-virtual {v1, p2}, Lorg/json/JSONObject;->optString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p2
    :try_end_0
    .catch Lorg/json/JSONException; {:try_start_0 .. :try_end_0} :catch_1

    :try_start_1
    iget-object v2, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->eV:Lorg/json/JSONObject;

    invoke-static {p2, v2}, Lcom/bytedance/adsdk/ugeno/Fj/hjc;->Fj(Ljava/lang/String;Lorg/json/JSONObject;)Ljava/lang/String;

    move-result-object p2

    invoke-static {p2}, Ljava/lang/Float;->parseFloat(Ljava/lang/String;)F

    move-result p2

    iput p2, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->yo:F
    :try_end_1
    .catch Ljava/lang/NumberFormatException; {:try_start_1 .. :try_end_1} :catch_0
    .catch Lorg/json/JSONException; {:try_start_1 .. :try_end_1} :catch_1

    goto :goto_0

    :catch_0
    const/high16 p2, 0x41400000    # 12.0f

    :try_start_2
    iput p2, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->yo:F

    :cond_1
    :goto_0
    iget-object p2, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->mj:Lcom/bytedance/adsdk/ugeno/core/dG;

    instance-of v2, p2, Lcom/bytedance/adsdk/ugeno/core/Fj/Fj;

    if-nez v2, :cond_2

    invoke-virtual {p0, p1, v1, v0}, Lcom/bytedance/adsdk/ugeno/component/ex;->Fj(ILorg/json/JSONObject;Lcom/bytedance/adsdk/ugeno/core/rAx;)V

    return-void

    :cond_2
    check-cast p2, Lcom/bytedance/adsdk/ugeno/core/Fj/Fj;

    invoke-virtual {p2}, Lcom/bytedance/adsdk/ugeno/core/Fj/Fj;->Fj()Z

    move-result p2

    if-nez p2, :cond_3

    invoke-virtual {p0, p1, v1, v0}, Lcom/bytedance/adsdk/ugeno/component/ex;->Fj(ILorg/json/JSONObject;Lcom/bytedance/adsdk/ugeno/core/rAx;)V

    return-void

    :cond_3
    invoke-virtual {v0, v1}, Lcom/bytedance/adsdk/ugeno/core/rAx;->Fj(Lorg/json/JSONObject;)V

    iget-object p2, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->qPr:Ljava/util/Map;

    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p1

    invoke-interface {p2, p1, v0}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    :try_end_2
    .catch Lorg/json/JSONException; {:try_start_2 .. :try_end_2} :catch_1

    :catch_1
    :cond_4
    :goto_1
    return-void
.end method

.method public ex(Lorg/json/JSONObject;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->hjc:Lorg/json/JSONObject;

    return-void
.end method

.method public hjc()Landroid/view/View;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()TT;"
        }
    .end annotation

    const/4 v0, 0x0

    return-object v0
.end method

.method public hjc(Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->Ko:Ljava/lang/String;

    return-void
.end method

.method public mSE()Landroid/view/View;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()TT;"
        }
    .end annotation

    iget-object v0, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->Ubf:Landroid/view/View;

    return-object v0
.end method

.method public rAx()Lorg/json/JSONObject;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->hjc:Lorg/json/JSONObject;

    return-object v0
.end method

.method public svN()V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->YH:Lcom/bytedance/adsdk/ugeno/core/BcC;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Lcom/bytedance/adsdk/ugeno/core/BcC;->ex()V

    :cond_0
    return-void
.end method
