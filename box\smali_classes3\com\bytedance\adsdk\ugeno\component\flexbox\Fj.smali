.class interface abstract Lcom/bytedance/adsdk/ugeno/component/flexbox/Fj;
.super Ljava/lang/Object;


# virtual methods
.method public abstract Fj(III)I
.end method

.method public abstract Fj(Landroid/view/View;)I
.end method

.method public abstract Fj(Landroid/view/View;II)I
.end method

.method public abstract Fj(I)Landroid/view/View;
.end method

.method public abstract Fj(Landroid/view/View;IILcom/bytedance/adsdk/ugeno/component/flexbox/hjc;)V
.end method

.method public abstract Fj(Lcom/bytedance/adsdk/ugeno/component/flexbox/hjc;)V
.end method

.method public abstract Fj()Z
.end method

.method public abstract ex(III)I
.end method

.method public abstract ex(I)Landroid/view/View;
.end method

.method public abstract getAlignContent()I
.end method

.method public abstract getAlignItems()I
.end method

.method public abstract getFlexDirection()I
.end method

.method public abstract getFlexItemCount()I
.end method

.method public abstract getFlexLinesInternal()Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lcom/bytedance/adsdk/ugeno/component/flexbox/hjc;",
            ">;"
        }
    .end annotation
.end method

.method public abstract getFlexWrap()I
.end method

.method public abstract getLargestMainSize()I
.end method

.method public abstract getMaxLine()I
.end method

.method public abstract getPaddingBottom()I
.end method

.method public abstract getPaddingEnd()I
.end method

.method public abstract getPaddingLeft()I
.end method

.method public abstract getPaddingRight()I
.end method

.method public abstract getPaddingStart()I
.end method

.method public abstract getPaddingTop()I
.end method

.method public abstract getSumOfCrossSize()I
.end method

.method public abstract setFlexLines(Ljava/util/List;)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lcom/bytedance/adsdk/ugeno/component/flexbox/hjc;",
            ">;)V"
        }
    .end annotation
.end method
