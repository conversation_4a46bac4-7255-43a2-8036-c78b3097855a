.class final Lcom/bytedance/adsdk/ugeno/component/flexbox/FlexboxLayout$Fj$1;
.super Ljava/lang/Object;

# interfaces
.implements Landroid/os/Parcelable$Creator;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bytedance/adsdk/ugeno/component/flexbox/FlexboxLayout$Fj;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Landroid/os/Parcelable$Creator<",
        "Lcom/bytedance/adsdk/ugeno/component/flexbox/FlexboxLayout$Fj;",
        ">;"
    }
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public Fj(Landroid/os/Parcel;)Lcom/bytedance/adsdk/ugeno/component/flexbox/FlexboxLayout$Fj;
    .locals 1

    new-instance v0, Lcom/bytedance/adsdk/ugeno/component/flexbox/FlexboxLayout$Fj;

    invoke-direct {v0, p1}, Lcom/bytedance/adsdk/ugeno/component/flexbox/FlexboxLayout$Fj;-><init>(Landroid/os/Parcel;)V

    return-object v0
.end method

.method public Fj(I)[Lcom/bytedance/adsdk/ugeno/component/flexbox/FlexboxLayout$Fj;
    .locals 0

    new-array p1, p1, [Lcom/bytedance/adsdk/ugeno/component/flexbox/FlexboxLayout$Fj;

    return-object p1
.end method

.method public synthetic createFromParcel(Landroid/os/Parcel;)Ljava/lang/Object;
    .locals 0

    invoke-virtual {p0, p1}, Lcom/bytedance/adsdk/ugeno/component/flexbox/FlexboxLayout$Fj$1;->Fj(Landroid/os/Parcel;)Lcom/bytedance/adsdk/ugeno/component/flexbox/FlexboxLayout$Fj;

    move-result-object p1

    return-object p1
.end method

.method public synthetic newArray(I)[Ljava/lang/Object;
    .locals 0

    invoke-virtual {p0, p1}, Lcom/bytedance/adsdk/ugeno/component/flexbox/FlexboxLayout$Fj$1;->Fj(I)[Lcom/bytedance/adsdk/ugeno/component/flexbox/FlexboxLayout$Fj;

    move-result-object p1

    return-object p1
.end method
