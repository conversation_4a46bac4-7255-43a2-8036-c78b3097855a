.class public interface abstract Lcom/bytedance/adsdk/ugeno/component/flexbox/ex;
.super Ljava/lang/Object;

# interfaces
.implements Landroid/os/Parcelable;


# virtual methods
.method public abstract BcC()I
.end method

.method public abstract Fj()I
.end method

.method public abstract Fj(I)V
.end method

.method public abstract JU()I
.end method

.method public abstract JW()I
.end method

.method public abstract Ko()I
.end method

.method public abstract Tc()I
.end method

.method public abstract UYd()F
.end method

.method public abstract Ubf()F
.end method

.method public abstract WR()I
.end method

.method public abstract dG()I
.end method

.method public abstract eV()F
.end method

.method public abstract ex()I
.end method

.method public abstract ex(I)V
.end method

.method public abstract hjc()I
.end method

.method public abstract mSE()I
.end method

.method public abstract rAx()Z
.end method

.method public abstract svN()I
.end method
