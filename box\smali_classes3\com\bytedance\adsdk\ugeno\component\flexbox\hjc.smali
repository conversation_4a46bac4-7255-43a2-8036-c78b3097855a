.class public Lcom/bytedance/adsdk/ugeno/component/flexbox/hjc;
.super Ljava/lang/Object;


# instance fields
.field BcC:I

.field Fj:I

.field JU:I

.field JW:I

.field Ko:F

.field Ql:Z

.field Tc:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Ljava/lang/Integer;",
            ">;"
        }
    .end annotation
.end field

.field UYd:I

.field Ubf:I

.field WR:I

.field dG:I

.field eV:I

.field ex:I

.field hjc:I

.field mSE:I

.field rAx:F

.field rS:Z

.field svN:I


# direct methods
.method public constructor <init>()V
    .locals 1

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const v0, 0x7fffffff

    iput v0, p0, Lcom/bytedance/adsdk/ugeno/component/flexbox/hjc;->Fj:I

    iput v0, p0, Lcom/bytedance/adsdk/ugeno/component/flexbox/hjc;->ex:I

    const/high16 v0, -0x80000000

    iput v0, p0, Lcom/bytedance/adsdk/ugeno/component/flexbox/hjc;->hjc:I

    iput v0, p0, Lcom/bytedance/adsdk/ugeno/component/flexbox/hjc;->eV:I

    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lcom/bytedance/adsdk/ugeno/component/flexbox/hjc;->Tc:Ljava/util/List;

    return-void
.end method


# virtual methods
.method public Fj()I
    .locals 1

    iget v0, p0, Lcom/bytedance/adsdk/ugeno/component/flexbox/hjc;->svN:I

    return v0
.end method

.method public Fj(Landroid/view/View;IIII)V
    .locals 4

    invoke-virtual {p1}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    move-result-object v0

    check-cast v0, Lcom/bytedance/adsdk/ugeno/component/flexbox/ex;

    iget v1, p0, Lcom/bytedance/adsdk/ugeno/component/flexbox/hjc;->Fj:I

    invoke-virtual {p1}, Landroid/view/View;->getLeft()I

    move-result v2

    invoke-interface {v0}, Lcom/bytedance/adsdk/ugeno/component/flexbox/ex;->dG()I

    move-result v3

    sub-int/2addr v2, v3

    sub-int/2addr v2, p2

    invoke-static {v1, v2}, Ljava/lang/Math;->min(II)I

    move-result p2

    iput p2, p0, Lcom/bytedance/adsdk/ugeno/component/flexbox/hjc;->Fj:I

    iget p2, p0, Lcom/bytedance/adsdk/ugeno/component/flexbox/hjc;->ex:I

    invoke-virtual {p1}, Landroid/view/View;->getTop()I

    move-result v1

    invoke-interface {v0}, Lcom/bytedance/adsdk/ugeno/component/flexbox/ex;->Tc()I

    move-result v2

    sub-int/2addr v1, v2

    sub-int/2addr v1, p3

    invoke-static {p2, v1}, Ljava/lang/Math;->min(II)I

    move-result p2

    iput p2, p0, Lcom/bytedance/adsdk/ugeno/component/flexbox/hjc;->ex:I

    iget p2, p0, Lcom/bytedance/adsdk/ugeno/component/flexbox/hjc;->hjc:I

    invoke-virtual {p1}, Landroid/view/View;->getRight()I

    move-result p3

    invoke-interface {v0}, Lcom/bytedance/adsdk/ugeno/component/flexbox/ex;->JW()I

    move-result v1

    add-int/2addr p3, v1

    add-int/2addr p3, p4

    invoke-static {p2, p3}, Ljava/lang/Math;->max(II)I

    move-result p2

    iput p2, p0, Lcom/bytedance/adsdk/ugeno/component/flexbox/hjc;->hjc:I

    iget p2, p0, Lcom/bytedance/adsdk/ugeno/component/flexbox/hjc;->eV:I

    invoke-virtual {p1}, Landroid/view/View;->getBottom()I

    move-result p1

    invoke-interface {v0}, Lcom/bytedance/adsdk/ugeno/component/flexbox/ex;->JU()I

    move-result p3

    add-int/2addr p1, p3

    add-int/2addr p1, p5

    invoke-static {p2, p1}, Ljava/lang/Math;->max(II)I

    move-result p1

    iput p1, p0, Lcom/bytedance/adsdk/ugeno/component/flexbox/hjc;->eV:I

    return-void
.end method

.method public ex()I
    .locals 2

    iget v0, p0, Lcom/bytedance/adsdk/ugeno/component/flexbox/hjc;->BcC:I

    iget v1, p0, Lcom/bytedance/adsdk/ugeno/component/flexbox/hjc;->mSE:I

    sub-int/2addr v0, v1

    return v0
.end method
