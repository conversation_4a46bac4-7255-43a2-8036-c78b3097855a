.class public Lcom/bytedance/adsdk/ugeno/component/frame/Fj;
.super Lcom/bytedance/adsdk/ugeno/component/Fj;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/bytedance/adsdk/ugeno/component/frame/Fj$Fj;
    }
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lcom/bytedance/adsdk/ugeno/component/Fj<",
        "Lcom/bytedance/adsdk/ugeno/component/frame/UGFrameLayout;",
        ">;"
    }
.end annotation


# instance fields
.field private OK:Lcom/bytedance/adsdk/ugeno/component/frame/UGFrameLayout;


# direct methods
.method public constructor <init>(Landroid/content/Context;)V
    .locals 0

    invoke-direct {p0, p1}, Lcom/bytedance/adsdk/ugeno/component/Fj;-><init>(Landroid/content/Context;)V

    return-void
.end method


# virtual methods
.method public BcC()Lcom/bytedance/adsdk/ugeno/component/Fj$Fj;
    .locals 1

    new-instance v0, Lcom/bytedance/adsdk/ugeno/component/frame/Fj$Fj;

    invoke-direct {v0}, Lcom/bytedance/adsdk/ugeno/component/frame/Fj$Fj;-><init>()V

    return-object v0
.end method

.method public ex()V
    .locals 2

    iget-object v0, p0, Lcom/bytedance/adsdk/ugeno/component/frame/Fj;->OK:Lcom/bytedance/adsdk/ugeno/component/frame/UGFrameLayout;

    iget-object v1, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->qPr:Ljava/util/Map;

    invoke-virtual {v0, v1}, Lcom/bytedance/adsdk/ugeno/component/frame/UGFrameLayout;->setEventMap(Ljava/util/Map;)V

    invoke-super {p0}, Lcom/bytedance/adsdk/ugeno/component/Fj;->ex()V

    return-void
.end method

.method public synthetic hjc()Landroid/view/View;
    .locals 1

    invoke-virtual {p0}, Lcom/bytedance/adsdk/ugeno/component/frame/Fj;->rS()Lcom/bytedance/adsdk/ugeno/component/frame/UGFrameLayout;

    move-result-object v0

    return-object v0
.end method

.method public rS()Lcom/bytedance/adsdk/ugeno/component/frame/UGFrameLayout;
    .locals 2

    new-instance v0, Lcom/bytedance/adsdk/ugeno/component/frame/UGFrameLayout;

    iget-object v1, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->ex:Landroid/content/Context;

    invoke-direct {v0, v1}, Lcom/bytedance/adsdk/ugeno/component/frame/UGFrameLayout;-><init>(Landroid/content/Context;)V

    iput-object v0, p0, Lcom/bytedance/adsdk/ugeno/component/frame/Fj;->OK:Lcom/bytedance/adsdk/ugeno/component/frame/UGFrameLayout;

    invoke-virtual {v0, p0}, Lcom/bytedance/adsdk/ugeno/component/frame/UGFrameLayout;->Fj(Lcom/bytedance/adsdk/ugeno/ex;)V

    iget-object v0, p0, Lcom/bytedance/adsdk/ugeno/component/frame/Fj;->OK:Lcom/bytedance/adsdk/ugeno/component/frame/UGFrameLayout;

    return-object v0
.end method
