.class public Lcom/bytedance/adsdk/ugeno/component/scroll/Fj;
.super Lcom/bytedance/adsdk/ugeno/component/Fj;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lcom/bytedance/adsdk/ugeno/component/Fj<",
        "Landroid/widget/ScrollView;",
        ">;"
    }
.end annotation


# direct methods
.method public constructor <init>(Landroid/content/Context;)V
    .locals 0

    invoke-direct {p0, p1}, Lcom/bytedance/adsdk/ugeno/component/Fj;-><init>(Landroid/content/Context;)V

    return-void
.end method


# virtual methods
.method public BcC()Lcom/bytedance/adsdk/ugeno/component/Fj$Fj;
    .locals 1

    new-instance v0, Lcom/bytedance/adsdk/ugeno/component/frame/Fj$Fj;

    invoke-direct {v0}, Lcom/bytedance/adsdk/ugeno/component/frame/Fj$Fj;-><init>()V

    return-object v0
.end method

.method public synthetic hjc()Landroid/view/View;
    .locals 1

    invoke-virtual {p0}, Lcom/bytedance/adsdk/ugeno/component/scroll/Fj;->rS()Landroid/widget/ScrollView;

    move-result-object v0

    return-object v0
.end method

.method public rS()Landroid/widget/ScrollView;
    .locals 2

    new-instance v0, Lcom/bytedance/adsdk/ugeno/component/scroll/UGScrollView;

    iget-object v1, p0, Lcom/bytedance/adsdk/ugeno/component/ex;->ex:Landroid/content/Context;

    invoke-direct {v0, v1}, Lcom/bytedance/adsdk/ugeno/component/scroll/UGScrollView;-><init>(Landroid/content/Context;)V

    invoke-virtual {v0, p0}, Lcom/bytedance/adsdk/ugeno/component/scroll/UGScrollView;->Fj(Lcom/bytedance/adsdk/ugeno/ex;)V

    return-object v0
.end method
