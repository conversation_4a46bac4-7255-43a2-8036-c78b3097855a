.class public Lcom/bytedance/adsdk/ugeno/core/Fj$Fj;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bytedance/adsdk/ugeno/core/Fj;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "Fj"
.end annotation


# instance fields
.field private BcC:[F

.field private Fj:J

.field private Ko:Ljava/lang/String;

.field private Ubf:Ljava/lang/String;

.field private WR:F

.field private eV:J

.field private ex:F

.field private hjc:Ljava/lang/String;

.field private mSE:Ljava/lang/String;

.field private svN:F


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static Fj(Lorg/json/JSONObject;Lcom/bytedance/adsdk/ugeno/component/ex;)Lcom/bytedance/adsdk/ugeno/core/Fj$Fj;
    .locals 4

    if-nez p0, :cond_0

    const/4 p0, 0x0

    return-object p0

    :cond_0
    new-instance v0, Lcom/bytedance/adsdk/ugeno/core/Fj$Fj;

    invoke-direct {v0}, Lcom/bytedance/adsdk/ugeno/core/Fj$Fj;-><init>()V

    const-string v1, "duration"

    invoke-virtual {p0, v1}, Lorg/json/JSONObject;->optLong(Ljava/lang/String;)J

    move-result-wide v1

    invoke-virtual {v0, v1, v2}, Lcom/bytedance/adsdk/ugeno/core/Fj$Fj;->Fj(J)V

    const-string v1, "loop"

    invoke-virtual {p0, v1}, Lorg/json/JSONObject;->optString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v1

    const-string v2, "infinite"

    invoke-static {v2, v1}, Landroid/text/TextUtils;->equals(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Z

    move-result v2

    if-eqz v2, :cond_1

    const/high16 v1, -0x40800000    # -1.0f

    invoke-virtual {v0, v1}, Lcom/bytedance/adsdk/ugeno/core/Fj$Fj;->Fj(F)V

    goto :goto_0

    :cond_1
    :try_start_0
    invoke-static {v1}, Ljava/lang/Float;->parseFloat(Ljava/lang/String;)F

    move-result v1

    invoke-virtual {v0, v1}, Lcom/bytedance/adsdk/ugeno/core/Fj$Fj;->Fj(F)V
    :try_end_0
    .catch Ljava/lang/NumberFormatException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Lcom/bytedance/adsdk/ugeno/core/Fj$Fj;->Fj(F)V

    :goto_0
    const-string v1, "loopMode"

    invoke-virtual {p0, v1}, Lorg/json/JSONObject;->optString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Lcom/bytedance/adsdk/ugeno/core/Fj$Fj;->Fj(Ljava/lang/String;)V

    const-string v1, "type"

    invoke-virtual {p0, v1}, Lorg/json/JSONObject;->optString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Lcom/bytedance/adsdk/ugeno/core/Fj$Fj;->ex(Ljava/lang/String;)V

    invoke-virtual {v0}, Lcom/bytedance/adsdk/ugeno/core/Fj$Fj;->Ubf()Ljava/lang/String;

    move-result-object v1

    const-string v2, "ripple"

    invoke-static {v1, v2}, Landroid/text/TextUtils;->equals(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Z

    move-result v1

    if-eqz v1, :cond_2

    const-string v1, "rippleColor"

    invoke-virtual {p0, v1}, Lorg/json/JSONObject;->optString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Lcom/bytedance/adsdk/ugeno/core/Fj$Fj;->hjc(Ljava/lang/String;)V

    :cond_2
    invoke-virtual {v0}, Lcom/bytedance/adsdk/ugeno/core/Fj$Fj;->Ubf()Ljava/lang/String;

    move-result-object v1

    const-string v2, "backgroundColor"

    invoke-static {v1, v2}, Landroid/text/TextUtils;->equals(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Z

    move-result v1

    const-string v2, "valueFrom"

    const-string v3, "valueTo"

    if-eqz v1, :cond_3

    invoke-virtual {p0, v3}, Lorg/json/JSONObject;->optString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p1}, Lcom/bytedance/adsdk/ugeno/component/ex;->Ko()Lorg/json/JSONObject;

    move-result-object v3

    invoke-static {v1, v3}, Lcom/bytedance/adsdk/ugeno/Fj/hjc;->Fj(Ljava/lang/String;Lorg/json/JSONObject;)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p0, v2}, Lorg/json/JSONObject;->optString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v2

    invoke-static {v2}, Lcom/bytedance/adsdk/ugeno/ex/Fj;->Fj(Ljava/lang/String;)I

    move-result v2

    invoke-static {v1}, Lcom/bytedance/adsdk/ugeno/ex/Fj;->Fj(Ljava/lang/String;)I

    move-result v1

    int-to-float v2, v2

    invoke-virtual {v0, v2}, Lcom/bytedance/adsdk/ugeno/core/Fj$Fj;->ex(F)V

    int-to-float v1, v1

    invoke-virtual {v0, v1}, Lcom/bytedance/adsdk/ugeno/core/Fj$Fj;->hjc(F)V

    goto :goto_1

    :cond_3
    invoke-virtual {p0, v2}, Lorg/json/JSONObject;->optDouble(Ljava/lang/String;)D

    move-result-wide v1

    double-to-float v1, v1

    invoke-virtual {v0, v1}, Lcom/bytedance/adsdk/ugeno/core/Fj$Fj;->ex(F)V

    invoke-virtual {p0, v3}, Lorg/json/JSONObject;->optDouble(Ljava/lang/String;)D

    move-result-wide v1

    double-to-float v1, v1

    invoke-virtual {v0, v1}, Lcom/bytedance/adsdk/ugeno/core/Fj$Fj;->hjc(F)V

    :goto_1
    const-string v1, "interpolator"

    invoke-virtual {p0, v1}, Lorg/json/JSONObject;->optString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Lcom/bytedance/adsdk/ugeno/core/Fj$Fj;->eV(Ljava/lang/String;)V

    const-string v1, "startDelay"

    invoke-virtual {p0, v1}, Lorg/json/JSONObject;->optString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p1}, Lcom/bytedance/adsdk/ugeno/component/ex;->Ko()Lorg/json/JSONObject;

    move-result-object p1

    invoke-static {v1, p1}, Lcom/bytedance/adsdk/ugeno/Fj/hjc;->Fj(Ljava/lang/String;Lorg/json/JSONObject;)Ljava/lang/String;

    move-result-object p1

    const-wide/16 v1, 0x0

    invoke-static {p1, v1, v2}, Lcom/bytedance/adsdk/ugeno/ex/hjc;->Fj(Ljava/lang/String;J)J

    move-result-wide v1

    invoke-virtual {v0, v1, v2}, Lcom/bytedance/adsdk/ugeno/core/Fj$Fj;->ex(J)V

    const-string p1, "values"

    invoke-virtual {p0, p1}, Lorg/json/JSONObject;->optJSONArray(Ljava/lang/String;)Lorg/json/JSONArray;

    move-result-object p0

    if-eqz p0, :cond_5

    invoke-virtual {p0}, Lorg/json/JSONArray;->length()I

    move-result p1

    if-lez p1, :cond_5

    invoke-virtual {p0}, Lorg/json/JSONArray;->length()I

    move-result p1

    new-array p1, p1, [F

    const/4 v1, 0x0

    :goto_2
    invoke-virtual {p0}, Lorg/json/JSONArray;->length()I

    move-result v2

    if-ge v1, v2, :cond_4

    invoke-virtual {p0, v1}, Lorg/json/JSONArray;->optDouble(I)D

    move-result-wide v2

    double-to-float v2, v2

    aput v2, p1, v1

    add-int/lit8 v1, v1, 0x1

    goto :goto_2

    :cond_4
    invoke-virtual {v0, p1}, Lcom/bytedance/adsdk/ugeno/core/Fj$Fj;->Fj([F)V

    :cond_5
    return-object v0
.end method


# virtual methods
.method public BcC()[F
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/ugeno/core/Fj$Fj;->BcC:[F

    return-object v0
.end method

.method public Fj()J
    .locals 2

    iget-wide v0, p0, Lcom/bytedance/adsdk/ugeno/core/Fj$Fj;->Fj:J

    return-wide v0
.end method

.method public Fj(F)V
    .locals 0

    iput p1, p0, Lcom/bytedance/adsdk/ugeno/core/Fj$Fj;->ex:F

    return-void
.end method

.method public Fj(J)V
    .locals 0

    iput-wide p1, p0, Lcom/bytedance/adsdk/ugeno/core/Fj$Fj;->Fj:J

    return-void
.end method

.method public Fj(Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/adsdk/ugeno/core/Fj$Fj;->hjc:Ljava/lang/String;

    return-void
.end method

.method public Fj([F)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/adsdk/ugeno/core/Fj$Fj;->BcC:[F

    return-void
.end method

.method public Ko()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/ugeno/core/Fj$Fj;->Ko:Ljava/lang/String;

    return-object v0
.end method

.method public Ubf()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/ugeno/core/Fj$Fj;->Ubf:Ljava/lang/String;

    return-object v0
.end method

.method public WR()F
    .locals 1

    iget v0, p0, Lcom/bytedance/adsdk/ugeno/core/Fj$Fj;->WR:F

    return v0
.end method

.method public eV()J
    .locals 2

    iget-wide v0, p0, Lcom/bytedance/adsdk/ugeno/core/Fj$Fj;->eV:J

    return-wide v0
.end method

.method public eV(Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/adsdk/ugeno/core/Fj$Fj;->mSE:Ljava/lang/String;

    return-void
.end method

.method public ex()F
    .locals 1

    iget v0, p0, Lcom/bytedance/adsdk/ugeno/core/Fj$Fj;->ex:F

    return v0
.end method

.method public ex(F)V
    .locals 0

    iput p1, p0, Lcom/bytedance/adsdk/ugeno/core/Fj$Fj;->WR:F

    return-void
.end method

.method public ex(J)V
    .locals 0

    iput-wide p1, p0, Lcom/bytedance/adsdk/ugeno/core/Fj$Fj;->eV:J

    return-void
.end method

.method public ex(Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/adsdk/ugeno/core/Fj$Fj;->Ubf:Ljava/lang/String;

    return-void
.end method

.method public hjc()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/ugeno/core/Fj$Fj;->hjc:Ljava/lang/String;

    return-object v0
.end method

.method public hjc(F)V
    .locals 0

    iput p1, p0, Lcom/bytedance/adsdk/ugeno/core/Fj$Fj;->svN:F

    return-void
.end method

.method public hjc(Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/adsdk/ugeno/core/Fj$Fj;->Ko:Ljava/lang/String;

    return-void
.end method

.method public mSE()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/ugeno/core/Fj$Fj;->mSE:Ljava/lang/String;

    return-object v0
.end method

.method public svN()F
    .locals 1

    iget v0, p0, Lcom/bytedance/adsdk/ugeno/core/Fj$Fj;->svN:F

    return v0
.end method
