.class Lcom/bytedance/adsdk/ugeno/core/Fj/Fj$1;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/sdk/component/uchain/listener/IEventChainLifeCycleListener;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/adsdk/ugeno/core/Fj/Fj;->hjc(Lcom/bytedance/adsdk/ugeno/core/rAx;Lcom/bytedance/adsdk/ugeno/core/dG$ex;Lcom/bytedance/adsdk/ugeno/core/dG$Fj;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/adsdk/ugeno/core/Fj/Fj;


# direct methods
.method public constructor <init>(Lcom/bytedance/adsdk/ugeno/core/Fj/Fj;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/adsdk/ugeno/core/Fj/Fj$1;->Fj:Lcom/bytedance/adsdk/ugeno/core/Fj/Fj;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
