.class Lcom/bytedance/adsdk/ugeno/core/Fj/Fj$Fj;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/sdk/component/uchain/listener/ICustomRouter;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bytedance/adsdk/ugeno/core/Fj/Fj;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = "Fj"
.end annotation


# instance fields
.field Fj:Lcom/bytedance/adsdk/ugeno/core/dG$Fj;

.field private eV:Lcom/bytedance/adsdk/ugeno/core/dG$ex;

.field final synthetic ex:Lcom/bytedance/adsdk/ugeno/core/Fj/Fj;

.field private hjc:Lcom/bytedance/adsdk/ugeno/core/rAx;


# direct methods
.method public constructor <init>(Lcom/bytedance/adsdk/ugeno/core/Fj/Fj;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/adsdk/ugeno/core/Fj/Fj$Fj;->ex:Lcom/bytedance/adsdk/ugeno/core/Fj/Fj;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public Fj(Lcom/bytedance/adsdk/ugeno/core/dG$Fj;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/adsdk/ugeno/core/Fj/Fj$Fj;->Fj:Lcom/bytedance/adsdk/ugeno/core/dG$Fj;

    return-void
.end method

.method public Fj(Lcom/bytedance/adsdk/ugeno/core/dG$ex;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/adsdk/ugeno/core/Fj/Fj$Fj;->eV:Lcom/bytedance/adsdk/ugeno/core/dG$ex;

    return-void
.end method

.method public Fj(Lcom/bytedance/adsdk/ugeno/core/rAx;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/adsdk/ugeno/core/Fj/Fj$Fj;->hjc:Lcom/bytedance/adsdk/ugeno/core/rAx;

    return-void
.end method
