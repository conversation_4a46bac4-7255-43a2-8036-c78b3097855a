.class public Lcom/bytedance/adsdk/ugeno/core/Fj/Fj;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/adsdk/ugeno/core/dG;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/bytedance/adsdk/ugeno/core/Fj/Fj$Fj;
    }
.end annotation


# instance fields
.field private Fj:Lcom/bytedance/adsdk/ugeno/core/dG;

.field private volatile Ubf:Lcom/bytedance/adsdk/ugeno/core/Fj/Fj$Fj;

.field private WR:Z

.field private eV:Lorg/json/JSONObject;

.field private ex:Ljava/lang/String;

.field private hjc:Z


# direct methods
.method public constructor <init>(Lcom/bytedance/adsdk/ugeno/core/dG;)V
    .locals 1

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, 0x1

    iput-boolean v0, p0, Lcom/bytedance/adsdk/ugeno/core/Fj/Fj;->hjc:Z

    const/4 v0, 0x0

    iput-boolean v0, p0, Lcom/bytedance/adsdk/ugeno/core/Fj/Fj;->WR:Z

    iput-object p1, p0, Lcom/bytedance/adsdk/ugeno/core/Fj/Fj;->Fj:Lcom/bytedance/adsdk/ugeno/core/dG;

    return-void
.end method

.method private ex()Lcom/bytedance/adsdk/ugeno/core/Fj/Fj$Fj;
    .locals 2

    iget-object v0, p0, Lcom/bytedance/adsdk/ugeno/core/Fj/Fj;->Ubf:Lcom/bytedance/adsdk/ugeno/core/Fj/Fj$Fj;

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/adsdk/ugeno/core/Fj/Fj;->Ubf:Lcom/bytedance/adsdk/ugeno/core/Fj/Fj$Fj;

    return-object v0

    :cond_0
    const-class v0, Lcom/bytedance/adsdk/ugeno/core/Fj/Fj$Fj;

    monitor-enter v0

    :try_start_0
    iget-object v1, p0, Lcom/bytedance/adsdk/ugeno/core/Fj/Fj;->Ubf:Lcom/bytedance/adsdk/ugeno/core/Fj/Fj$Fj;

    if-eqz v1, :cond_1

    iget-object v1, p0, Lcom/bytedance/adsdk/ugeno/core/Fj/Fj;->Ubf:Lcom/bytedance/adsdk/ugeno/core/Fj/Fj$Fj;

    monitor-exit v0

    return-object v1

    :catchall_0
    move-exception v1

    goto :goto_0

    :cond_1
    new-instance v1, Lcom/bytedance/adsdk/ugeno/core/Fj/Fj$Fj;

    invoke-direct {v1, p0}, Lcom/bytedance/adsdk/ugeno/core/Fj/Fj$Fj;-><init>(Lcom/bytedance/adsdk/ugeno/core/Fj/Fj;)V

    iput-object v1, p0, Lcom/bytedance/adsdk/ugeno/core/Fj/Fj;->Ubf:Lcom/bytedance/adsdk/ugeno/core/Fj/Fj$Fj;

    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    iget-object v0, p0, Lcom/bytedance/adsdk/ugeno/core/Fj/Fj;->Ubf:Lcom/bytedance/adsdk/ugeno/core/Fj/Fj$Fj;

    return-object v0

    :goto_0
    monitor-exit v0

    throw v1
.end method

.method private ex(Lcom/bytedance/adsdk/ugeno/core/rAx;Lcom/bytedance/adsdk/ugeno/core/dG$ex;Lcom/bytedance/adsdk/ugeno/core/dG$Fj;)V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/ugeno/core/Fj/Fj;->Fj:Lcom/bytedance/adsdk/ugeno/core/dG;

    if-nez v0, :cond_0

    return-void

    :cond_0
    invoke-interface {v0, p1, p2, p3}, Lcom/bytedance/adsdk/ugeno/core/dG;->Fj(Lcom/bytedance/adsdk/ugeno/core/rAx;Lcom/bytedance/adsdk/ugeno/core/dG$ex;Lcom/bytedance/adsdk/ugeno/core/dG$Fj;)V

    return-void
.end method

.method private hjc(Lcom/bytedance/adsdk/ugeno/core/rAx;Lcom/bytedance/adsdk/ugeno/core/dG$ex;Lcom/bytedance/adsdk/ugeno/core/dG$Fj;)V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/ugeno/core/Fj/Fj;->Ubf:Lcom/bytedance/adsdk/ugeno/core/Fj/Fj$Fj;

    if-nez v0, :cond_0

    invoke-direct {p0}, Lcom/bytedance/adsdk/ugeno/core/Fj/Fj;->ex()Lcom/bytedance/adsdk/ugeno/core/Fj/Fj$Fj;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/adsdk/ugeno/core/Fj/Fj;->Ubf:Lcom/bytedance/adsdk/ugeno/core/Fj/Fj$Fj;

    :cond_0
    iget-object v0, p0, Lcom/bytedance/adsdk/ugeno/core/Fj/Fj;->Ubf:Lcom/bytedance/adsdk/ugeno/core/Fj/Fj$Fj;

    invoke-virtual {v0, p1}, Lcom/bytedance/adsdk/ugeno/core/Fj/Fj$Fj;->Fj(Lcom/bytedance/adsdk/ugeno/core/rAx;)V

    iget-object v0, p0, Lcom/bytedance/adsdk/ugeno/core/Fj/Fj;->Ubf:Lcom/bytedance/adsdk/ugeno/core/Fj/Fj$Fj;

    invoke-virtual {v0, p2}, Lcom/bytedance/adsdk/ugeno/core/Fj/Fj$Fj;->Fj(Lcom/bytedance/adsdk/ugeno/core/dG$ex;)V

    iget-object p2, p0, Lcom/bytedance/adsdk/ugeno/core/Fj/Fj;->Ubf:Lcom/bytedance/adsdk/ugeno/core/Fj/Fj$Fj;

    invoke-virtual {p2, p3}, Lcom/bytedance/adsdk/ugeno/core/Fj/Fj$Fj;->Fj(Lcom/bytedance/adsdk/ugeno/core/dG$Fj;)V

    invoke-virtual {p1}, Lcom/bytedance/adsdk/ugeno/core/rAx;->hjc()Lorg/json/JSONObject;

    move-result-object p1

    if-nez p1, :cond_1

    return-void

    :cond_1
    new-instance p2, Lcom/bytedance/sdk/component/uchain/action/EventChainAction$Builder;

    const-string p3, "type"

    invoke-virtual {p1, p3}, Lorg/json/JSONObject;->optString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    invoke-direct {p2, p1}, Lcom/bytedance/sdk/component/uchain/action/EventChainAction$Builder;-><init>(Ljava/lang/String;)V

    iget-object p1, p0, Lcom/bytedance/adsdk/ugeno/core/Fj/Fj;->eV:Lorg/json/JSONObject;

    invoke-virtual {p2, p1}, Lcom/bytedance/sdk/component/uchain/action/EventChainAction$Builder;->setChainData(Lorg/json/JSONObject;)Lcom/bytedance/sdk/component/uchain/action/EventChainAction$Builder;

    move-result-object p1

    new-instance p2, Lcom/bytedance/adsdk/ugeno/core/Fj/Fj$1;

    invoke-direct {p2, p0}, Lcom/bytedance/adsdk/ugeno/core/Fj/Fj$1;-><init>(Lcom/bytedance/adsdk/ugeno/core/Fj/Fj;)V

    invoke-virtual {p1, p2}, Lcom/bytedance/sdk/component/uchain/action/EventChainAction$Builder;->setEventChainLifeCycleListener(Lcom/bytedance/sdk/component/uchain/listener/IEventChainLifeCycleListener;)Lcom/bytedance/sdk/component/uchain/action/EventChainAction$Builder;

    move-result-object p1

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/uchain/action/EventChainAction$Builder;->build()Lcom/bytedance/sdk/component/uchain/action/EventChainAction;

    move-result-object p1

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/uchain/action/EventChainAction;->run()V

    return-void
.end method


# virtual methods
.method public Fj(Lcom/bytedance/adsdk/ugeno/core/rAx;Lcom/bytedance/adsdk/ugeno/core/dG$ex;Lcom/bytedance/adsdk/ugeno/core/dG$Fj;)V
    .locals 1

    invoke-virtual {p0}, Lcom/bytedance/adsdk/ugeno/core/Fj/Fj;->Fj()Z

    move-result v0

    if-eqz v0, :cond_0

    invoke-direct {p0, p1, p2, p3}, Lcom/bytedance/adsdk/ugeno/core/Fj/Fj;->hjc(Lcom/bytedance/adsdk/ugeno/core/rAx;Lcom/bytedance/adsdk/ugeno/core/dG$ex;Lcom/bytedance/adsdk/ugeno/core/dG$Fj;)V

    return-void

    :cond_0
    invoke-direct {p0, p1, p2, p3}, Lcom/bytedance/adsdk/ugeno/core/Fj/Fj;->ex(Lcom/bytedance/adsdk/ugeno/core/rAx;Lcom/bytedance/adsdk/ugeno/core/dG$ex;Lcom/bytedance/adsdk/ugeno/core/dG$Fj;)V

    return-void
.end method

.method public Fj(Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/adsdk/ugeno/core/Fj/Fj;->ex:Ljava/lang/String;

    return-void
.end method

.method public Fj(Lorg/json/JSONObject;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/adsdk/ugeno/core/Fj/Fj;->eV:Lorg/json/JSONObject;

    return-void
.end method

.method public Fj(Z)V
    .locals 0

    iput-boolean p1, p0, Lcom/bytedance/adsdk/ugeno/core/Fj/Fj;->hjc:Z

    return-void
.end method

.method public Fj()Z
    .locals 2

    iget-boolean v0, p0, Lcom/bytedance/adsdk/ugeno/core/Fj/Fj;->hjc:Z

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/adsdk/ugeno/core/Fj/Fj;->ex:Ljava/lang/String;

    if-eqz v0, :cond_0

    const-string v1, "3"

    invoke-virtual {v1, v0}, Ljava/lang/String;->compareTo(Ljava/lang/String;)I

    move-result v0

    if-gtz v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/adsdk/ugeno/core/Fj/Fj;->eV:Lorg/json/JSONObject;

    if-eqz v0, :cond_0

    const/4 v0, 0x1

    return v0

    :cond_0
    const/4 v0, 0x0

    return v0
.end method

.method public ex(Z)V
    .locals 0

    iput-boolean p1, p0, Lcom/bytedance/adsdk/ugeno/core/Fj/Fj;->WR:Z

    return-void
.end method
