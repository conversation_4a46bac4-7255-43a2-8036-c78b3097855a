.class public Lcom/bytedance/adsdk/ugeno/core/JW;
.super Ljava/lang/Object;


# instance fields
.field private Fj:I

.field private ex:Ljava/lang/String;

.field private hjc:Lcom/bytedance/adsdk/ugeno/component/ex;


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public Fj()I
    .locals 1

    iget v0, p0, Lcom/bytedance/adsdk/ugeno/core/JW;->Fj:I

    return v0
.end method

.method public Fj(I)V
    .locals 0

    iput p1, p0, Lcom/bytedance/adsdk/ugeno/core/JW;->Fj:I

    return-void
.end method

.method public Fj(Lcom/bytedance/adsdk/ugeno/component/ex;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/adsdk/ugeno/core/JW;->hjc:Lcom/bytedance/adsdk/ugeno/component/ex;

    return-void
.end method

.method public Fj(Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/adsdk/ugeno/core/JW;->ex:Ljava/lang/String;

    return-void
.end method

.method public ex()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/ugeno/core/JW;->ex:Ljava/lang/String;

    return-object v0
.end method
