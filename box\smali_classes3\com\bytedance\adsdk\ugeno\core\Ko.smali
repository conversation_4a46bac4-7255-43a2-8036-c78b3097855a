.class public Lcom/bytedance/adsdk/ugeno/core/Ko;
.super Ljava/lang/Object;


# instance fields
.field private BcC:Lcom/bytedance/adsdk/ugeno/core/WR;

.field private Fj:Landroid/content/Context;

.field private Ko:Lorg/json/JSONObject;

.field private UYd:Z

.field private Ubf:Lcom/bytedance/adsdk/ugeno/core/dG;

.field private WR:Lcom/bytedance/adsdk/ugeno/core/JU;

.field private eV:Lcom/bytedance/adsdk/ugeno/core/svN;

.field private ex:Lorg/json/JSONObject;

.field private hjc:Lcom/bytedance/adsdk/ugeno/component/ex;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/bytedance/adsdk/ugeno/component/ex<",
            "Landroid/view/View;",
            ">;"
        }
    .end annotation
.end field

.field private mSE:Lcom/bytedance/adsdk/ugeno/core/mSE;

.field private rAx:Z

.field private svN:Lcom/bytedance/adsdk/ugeno/core/Tc;


# direct methods
.method public constructor <init>(Landroid/content/Context;)V
    .locals 1

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, 0x1

    iput-boolean v0, p0, Lcom/bytedance/adsdk/ugeno/core/Ko;->rAx:Z

    const/4 v0, 0x0

    iput-boolean v0, p0, Lcom/bytedance/adsdk/ugeno/core/Ko;->UYd:Z

    iput-object p1, p0, Lcom/bytedance/adsdk/ugeno/core/Ko;->Fj:Landroid/content/Context;

    return-void
.end method

.method private Fj(Lcom/bytedance/adsdk/ugeno/component/ex;)V
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/bytedance/adsdk/ugeno/component/ex<",
            "Landroid/view/View;",
            ">;)V"
        }
    .end annotation

    if-nez p1, :cond_0

    return-void

    :cond_0
    invoke-virtual {p1}, Lcom/bytedance/adsdk/ugeno/component/ex;->rAx()Lorg/json/JSONObject;

    move-result-object v0

    invoke-virtual {v0}, Lorg/json/JSONObject;->keys()Ljava/util/Iterator;

    move-result-object v1

    invoke-virtual {p1}, Lcom/bytedance/adsdk/ugeno/component/ex;->UYd()Lcom/bytedance/adsdk/ugeno/component/Fj;

    move-result-object v2

    if-eqz v2, :cond_1

    invoke-virtual {v2}, Lcom/bytedance/adsdk/ugeno/component/Fj;->BcC()Lcom/bytedance/adsdk/ugeno/component/Fj$Fj;

    move-result-object v2

    goto :goto_0

    :cond_1
    const/4 v2, 0x0

    :cond_2
    :goto_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v3

    if-eqz v3, :cond_3

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Ljava/lang/String;

    invoke-virtual {v0, v3}, Lorg/json/JSONObject;->optString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v4

    iget-object v5, p0, Lcom/bytedance/adsdk/ugeno/core/Ko;->ex:Lorg/json/JSONObject;

    invoke-static {v4, v5}, Lcom/bytedance/adsdk/ugeno/Fj/hjc;->Fj(Ljava/lang/String;Lorg/json/JSONObject;)Ljava/lang/String;

    move-result-object v4

    invoke-virtual {p1, v3, v4}, Lcom/bytedance/adsdk/ugeno/component/ex;->Fj(Ljava/lang/String;Ljava/lang/String;)V

    iget-object v5, p0, Lcom/bytedance/adsdk/ugeno/core/Ko;->eV:Lcom/bytedance/adsdk/ugeno/core/svN;

    invoke-virtual {p1, v5}, Lcom/bytedance/adsdk/ugeno/component/ex;->Fj(Lcom/bytedance/adsdk/ugeno/core/svN;)V

    iget-object v5, p0, Lcom/bytedance/adsdk/ugeno/core/Ko;->Ubf:Lcom/bytedance/adsdk/ugeno/core/dG;

    invoke-virtual {p1, v5}, Lcom/bytedance/adsdk/ugeno/component/ex;->Fj(Lcom/bytedance/adsdk/ugeno/core/dG;)V

    iget-object v5, p0, Lcom/bytedance/adsdk/ugeno/core/Ko;->svN:Lcom/bytedance/adsdk/ugeno/core/Tc;

    invoke-virtual {p1, v5}, Lcom/bytedance/adsdk/ugeno/component/ex;->Fj(Lcom/bytedance/adsdk/ugeno/core/Tc;)V

    if-eqz v2, :cond_2

    iget-object v5, p0, Lcom/bytedance/adsdk/ugeno/core/Ko;->Fj:Landroid/content/Context;

    invoke-virtual {v2, v5, v3, v4}, Lcom/bytedance/adsdk/ugeno/component/Fj$Fj;->Fj(Landroid/content/Context;Ljava/lang/String;Ljava/lang/String;)V

    goto :goto_0

    :cond_3
    instance-of v0, p1, Lcom/bytedance/adsdk/ugeno/component/Fj;

    if-eqz v0, :cond_4

    move-object v0, p1

    check-cast v0, Lcom/bytedance/adsdk/ugeno/component/Fj;

    invoke-virtual {v0}, Lcom/bytedance/adsdk/ugeno/component/Fj;->Fj()Ljava/util/List;

    move-result-object v0

    if-eqz v0, :cond_4

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v1

    if-lez v1, :cond_4

    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_1
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_4

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/bytedance/adsdk/ugeno/component/ex;

    invoke-direct {p0, v1}, Lcom/bytedance/adsdk/ugeno/core/Ko;->Fj(Lcom/bytedance/adsdk/ugeno/component/ex;)V

    goto :goto_1

    :cond_4
    if-eqz v2, :cond_5

    invoke-virtual {v2}, Lcom/bytedance/adsdk/ugeno/component/Fj$Fj;->Fj()Landroid/view/ViewGroup$LayoutParams;

    move-result-object v0

    invoke-virtual {p1, v0}, Lcom/bytedance/adsdk/ugeno/component/ex;->Fj(Landroid/view/ViewGroup$LayoutParams;)V

    :cond_5
    invoke-virtual {p1}, Lcom/bytedance/adsdk/ugeno/component/ex;->ex()V

    return-void
.end method


# virtual methods
.method public Fj(Lcom/bytedance/adsdk/ugeno/core/WR$Fj;Lcom/bytedance/adsdk/ugeno/component/ex;)Lcom/bytedance/adsdk/ugeno/component/ex;
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/bytedance/adsdk/ugeno/core/WR$Fj;",
            "Lcom/bytedance/adsdk/ugeno/component/ex<",
            "Landroid/view/View;",
            ">;)",
            "Lcom/bytedance/adsdk/ugeno/component/ex<",
            "Landroid/view/View;",
            ">;"
        }
    .end annotation

    invoke-static {p1}, Lcom/bytedance/adsdk/ugeno/core/WR;->Fj(Lcom/bytedance/adsdk/ugeno/core/WR$Fj;)Z

    move-result v0

    const/4 v1, 0x0

    if-nez v0, :cond_0

    return-object v1

    :cond_0
    invoke-virtual {p1}, Lcom/bytedance/adsdk/ugeno/core/WR$Fj;->hjc()Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lcom/bytedance/adsdk/ugeno/core/eV;->Fj(Ljava/lang/String;)Lcom/bytedance/adsdk/ugeno/core/ex;

    move-result-object v2

    if-nez v2, :cond_1

    invoke-static {v0}, Ljava/lang/String;->valueOf(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    const-string p2, "not found component "

    invoke-virtual {p2, p1}, Ljava/lang/String;->concat(Ljava/lang/String;)Ljava/lang/String;

    return-object v1

    :cond_1
    iget-object v3, p0, Lcom/bytedance/adsdk/ugeno/core/Ko;->Fj:Landroid/content/Context;

    invoke-virtual {v2, v3}, Lcom/bytedance/adsdk/ugeno/core/ex;->Fj(Landroid/content/Context;)Lcom/bytedance/adsdk/ugeno/component/ex;

    move-result-object v2

    if-nez v2, :cond_2

    return-object v1

    :cond_2
    invoke-virtual {p1}, Lcom/bytedance/adsdk/ugeno/core/WR$Fj;->Fj()Ljava/lang/String;

    move-result-object v3

    iget-object v4, p0, Lcom/bytedance/adsdk/ugeno/core/Ko;->ex:Lorg/json/JSONObject;

    invoke-static {v3, v4}, Lcom/bytedance/adsdk/ugeno/Fj/hjc;->Fj(Ljava/lang/String;Lorg/json/JSONObject;)Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v2, v3}, Lcom/bytedance/adsdk/ugeno/component/ex;->hjc(Ljava/lang/String;)V

    invoke-virtual {v2, v0}, Lcom/bytedance/adsdk/ugeno/component/ex;->eV(Ljava/lang/String;)V

    invoke-virtual {p1}, Lcom/bytedance/adsdk/ugeno/core/WR$Fj;->eV()Lorg/json/JSONObject;

    move-result-object v0

    invoke-virtual {v2, v0}, Lcom/bytedance/adsdk/ugeno/component/ex;->ex(Lorg/json/JSONObject;)V

    invoke-virtual {v2, p1}, Lcom/bytedance/adsdk/ugeno/component/ex;->Fj(Lcom/bytedance/adsdk/ugeno/core/WR$Fj;)V

    iget-object v0, p0, Lcom/bytedance/adsdk/ugeno/core/Ko;->mSE:Lcom/bytedance/adsdk/ugeno/core/mSE;

    invoke-virtual {v2, v0}, Lcom/bytedance/adsdk/ugeno/component/ex;->Fj(Lcom/bytedance/adsdk/ugeno/core/mSE;)V

    instance-of v0, p2, Lcom/bytedance/adsdk/ugeno/component/Fj;

    if-eqz v0, :cond_3

    check-cast p2, Lcom/bytedance/adsdk/ugeno/component/Fj;

    invoke-virtual {v2, p2}, Lcom/bytedance/adsdk/ugeno/component/ex;->Fj(Lcom/bytedance/adsdk/ugeno/component/Fj;)V

    invoke-virtual {p2}, Lcom/bytedance/adsdk/ugeno/component/Fj;->BcC()Lcom/bytedance/adsdk/ugeno/component/Fj$Fj;

    move-result-object v1

    :cond_3
    invoke-virtual {p1}, Lcom/bytedance/adsdk/ugeno/core/WR$Fj;->eV()Lorg/json/JSONObject;

    move-result-object p2

    invoke-virtual {p2}, Lorg/json/JSONObject;->keys()Ljava/util/Iterator;

    move-result-object p2

    :cond_4
    :goto_0
    invoke-interface {p2}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_5

    invoke-interface {p2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/lang/String;

    invoke-virtual {p1}, Lcom/bytedance/adsdk/ugeno/core/WR$Fj;->eV()Lorg/json/JSONObject;

    move-result-object v3

    invoke-virtual {v3, v0}, Lorg/json/JSONObject;->optString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v3

    iget-object v4, p0, Lcom/bytedance/adsdk/ugeno/core/Ko;->ex:Lorg/json/JSONObject;

    invoke-static {v3, v4}, Lcom/bytedance/adsdk/ugeno/Fj/hjc;->Fj(Ljava/lang/String;Lorg/json/JSONObject;)Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v2, v0, v3}, Lcom/bytedance/adsdk/ugeno/component/ex;->Fj(Ljava/lang/String;Ljava/lang/String;)V

    if-eqz v1, :cond_4

    iget-object v4, p0, Lcom/bytedance/adsdk/ugeno/core/Ko;->Fj:Landroid/content/Context;

    invoke-virtual {v1, v4, v0, v3}, Lcom/bytedance/adsdk/ugeno/component/Fj$Fj;->Fj(Landroid/content/Context;Ljava/lang/String;Ljava/lang/String;)V

    goto :goto_0

    :cond_5
    instance-of p2, v2, Lcom/bytedance/adsdk/ugeno/component/Fj;

    if-eqz p2, :cond_c

    invoke-virtual {p1}, Lcom/bytedance/adsdk/ugeno/core/WR$Fj;->Ubf()Ljava/util/List;

    move-result-object p1

    if-eqz p1, :cond_9

    invoke-interface {p1}, Ljava/util/List;->size()I

    move-result p2

    if-gtz p2, :cond_6

    goto :goto_2

    :cond_6
    invoke-virtual {v2}, Lcom/bytedance/adsdk/ugeno/component/ex;->Tc()Ljava/lang/String;

    move-result-object p2

    const-string v0, "Swiper"

    invoke-static {p2, v0}, Landroid/text/TextUtils;->equals(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Z

    move-result p2

    if-eqz p2, :cond_7

    invoke-interface {p1}, Ljava/util/List;->size()I

    move-result p2

    const/4 v0, 0x1

    if-eq p2, v0, :cond_7

    const-string p2, "UGTemplateEngine"

    const-string v0, "Swiper must be only one widget"

    invoke-static {p2, v0}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    :cond_7
    invoke-interface {p1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :cond_8
    :goto_1
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result p2

    if-eqz p2, :cond_c

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object p2

    check-cast p2, Lcom/bytedance/adsdk/ugeno/core/WR$Fj;

    invoke-virtual {p0, p2, v2}, Lcom/bytedance/adsdk/ugeno/core/Ko;->Fj(Lcom/bytedance/adsdk/ugeno/core/WR$Fj;Lcom/bytedance/adsdk/ugeno/component/ex;)Lcom/bytedance/adsdk/ugeno/component/ex;

    move-result-object p2

    if-eqz p2, :cond_8

    invoke-virtual {p2}, Lcom/bytedance/adsdk/ugeno/component/ex;->Ql()Z

    move-result v0

    if-eqz v0, :cond_8

    move-object v0, v2

    check-cast v0, Lcom/bytedance/adsdk/ugeno/component/Fj;

    invoke-virtual {v0, p2}, Lcom/bytedance/adsdk/ugeno/component/Fj;->Fj(Lcom/bytedance/adsdk/ugeno/component/ex;)V

    goto :goto_1

    :cond_9
    :goto_2
    invoke-virtual {v2}, Lcom/bytedance/adsdk/ugeno/component/ex;->Tc()Ljava/lang/String;

    move-result-object p1

    const-string p2, "RecyclerLayout"

    invoke-static {p1, p2}, Landroid/text/TextUtils;->equals(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Z

    move-result p1

    if-eqz p1, :cond_b

    iget-object p1, p0, Lcom/bytedance/adsdk/ugeno/core/Ko;->BcC:Lcom/bytedance/adsdk/ugeno/core/WR;

    invoke-virtual {p1}, Lcom/bytedance/adsdk/ugeno/core/WR;->hjc()Ljava/util/List;

    move-result-object p1

    if-eqz p1, :cond_b

    invoke-interface {p1}, Ljava/util/List;->size()I

    move-result p2

    if-lez p2, :cond_b

    invoke-interface {p1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :cond_a
    :goto_3
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result p2

    if-eqz p2, :cond_b

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object p2

    check-cast p2, Lcom/bytedance/adsdk/ugeno/core/WR$Fj;

    invoke-virtual {p0, p2, v2}, Lcom/bytedance/adsdk/ugeno/core/Ko;->Fj(Lcom/bytedance/adsdk/ugeno/core/WR$Fj;Lcom/bytedance/adsdk/ugeno/component/ex;)Lcom/bytedance/adsdk/ugeno/component/ex;

    move-result-object p2

    if-eqz p2, :cond_a

    invoke-virtual {p2}, Lcom/bytedance/adsdk/ugeno/component/ex;->Ql()Z

    move-result v0

    if-eqz v0, :cond_a

    move-object v0, v2

    check-cast v0, Lcom/bytedance/adsdk/ugeno/component/Fj;

    invoke-virtual {v0, p2}, Lcom/bytedance/adsdk/ugeno/component/Fj;->Fj(Lcom/bytedance/adsdk/ugeno/component/ex;)V

    goto :goto_3

    :cond_b
    return-object v2

    :cond_c
    if-eqz v1, :cond_d

    invoke-virtual {v1}, Lcom/bytedance/adsdk/ugeno/component/Fj$Fj;->Fj()Landroid/view/ViewGroup$LayoutParams;

    move-result-object p1

    invoke-virtual {v2, p1}, Lcom/bytedance/adsdk/ugeno/component/ex;->Fj(Landroid/view/ViewGroup$LayoutParams;)V

    :cond_d
    iput-object v2, p0, Lcom/bytedance/adsdk/ugeno/core/Ko;->hjc:Lcom/bytedance/adsdk/ugeno/component/ex;

    return-object v2
.end method

.method public Fj(Lorg/json/JSONObject;)Lcom/bytedance/adsdk/ugeno/component/ex;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/json/JSONObject;",
            ")",
            "Lcom/bytedance/adsdk/ugeno/component/ex<",
            "Landroid/view/View;",
            ">;"
        }
    .end annotation

    iget-object v0, p0, Lcom/bytedance/adsdk/ugeno/core/Ko;->WR:Lcom/bytedance/adsdk/ugeno/core/JU;

    if-eqz v0, :cond_0

    invoke-interface {v0}, Lcom/bytedance/adsdk/ugeno/core/JU;->Fj()V

    :cond_0
    new-instance v0, Lcom/bytedance/adsdk/ugeno/core/WR;

    iget-object v1, p0, Lcom/bytedance/adsdk/ugeno/core/Ko;->ex:Lorg/json/JSONObject;

    invoke-direct {v0, p1, v1}, Lcom/bytedance/adsdk/ugeno/core/WR;-><init>(Lorg/json/JSONObject;Lorg/json/JSONObject;)V

    iput-object v0, p0, Lcom/bytedance/adsdk/ugeno/core/Ko;->BcC:Lcom/bytedance/adsdk/ugeno/core/WR;

    iget-object p1, p0, Lcom/bytedance/adsdk/ugeno/core/Ko;->Ubf:Lcom/bytedance/adsdk/ugeno/core/dG;

    instance-of v1, p1, Lcom/bytedance/adsdk/ugeno/core/Fj/Fj;

    if-eqz v1, :cond_1

    check-cast p1, Lcom/bytedance/adsdk/ugeno/core/Fj/Fj;

    invoke-virtual {v0}, Lcom/bytedance/adsdk/ugeno/core/WR;->ex()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p1, v0}, Lcom/bytedance/adsdk/ugeno/core/Fj/Fj;->Fj(Ljava/lang/String;)V

    :cond_1
    iget-object p1, p0, Lcom/bytedance/adsdk/ugeno/core/Ko;->BcC:Lcom/bytedance/adsdk/ugeno/core/WR;

    invoke-virtual {p1}, Lcom/bytedance/adsdk/ugeno/core/WR;->Fj()Lcom/bytedance/adsdk/ugeno/core/WR$Fj;

    move-result-object p1

    const/4 v0, 0x0

    invoke-virtual {p0, p1, v0}, Lcom/bytedance/adsdk/ugeno/core/Ko;->Fj(Lcom/bytedance/adsdk/ugeno/core/WR$Fj;Lcom/bytedance/adsdk/ugeno/component/ex;)Lcom/bytedance/adsdk/ugeno/component/ex;

    move-result-object p1

    iput-object p1, p0, Lcom/bytedance/adsdk/ugeno/core/Ko;->hjc:Lcom/bytedance/adsdk/ugeno/component/ex;

    iget-object p1, p0, Lcom/bytedance/adsdk/ugeno/core/Ko;->WR:Lcom/bytedance/adsdk/ugeno/core/JU;

    if-eqz p1, :cond_2

    invoke-interface {p1}, Lcom/bytedance/adsdk/ugeno/core/JU;->ex()V

    iget-object p1, p0, Lcom/bytedance/adsdk/ugeno/core/Ko;->hjc:Lcom/bytedance/adsdk/ugeno/component/ex;

    iget-object v0, p0, Lcom/bytedance/adsdk/ugeno/core/Ko;->WR:Lcom/bytedance/adsdk/ugeno/core/JU;

    invoke-virtual {p1, v0}, Lcom/bytedance/adsdk/ugeno/component/ex;->Fj(Lcom/bytedance/adsdk/ugeno/core/JU;)V

    :cond_2
    iget-object p1, p0, Lcom/bytedance/adsdk/ugeno/core/Ko;->hjc:Lcom/bytedance/adsdk/ugeno/component/ex;

    return-object p1
.end method

.method public Fj(Lcom/bytedance/adsdk/ugeno/component/ex;Lorg/json/JSONObject;)V
    .locals 1

    if-nez p1, :cond_0

    return-void

    :cond_0
    instance-of v0, p1, Lcom/bytedance/adsdk/ugeno/component/Fj;

    if-eqz v0, :cond_3

    invoke-virtual {p1, p2}, Lcom/bytedance/adsdk/ugeno/component/ex;->Fj(Lorg/json/JSONObject;)V

    check-cast p1, Lcom/bytedance/adsdk/ugeno/component/Fj;

    invoke-virtual {p1}, Lcom/bytedance/adsdk/ugeno/component/Fj;->Fj()Ljava/util/List;

    move-result-object p1

    if-eqz p1, :cond_2

    invoke-interface {p1}, Ljava/util/List;->size()I

    move-result v0

    if-gtz v0, :cond_1

    goto :goto_1

    :cond_1
    invoke-interface {p1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_2

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/bytedance/adsdk/ugeno/component/ex;

    invoke-virtual {p0, v0, p2}, Lcom/bytedance/adsdk/ugeno/core/Ko;->Fj(Lcom/bytedance/adsdk/ugeno/component/ex;Lorg/json/JSONObject;)V

    goto :goto_0

    :cond_2
    :goto_1
    return-void

    :cond_3
    invoke-virtual {p1, p2}, Lcom/bytedance/adsdk/ugeno/component/ex;->Fj(Lorg/json/JSONObject;)V

    return-void
.end method

.method public Fj(Lcom/bytedance/adsdk/ugeno/core/Tc;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/adsdk/ugeno/core/Ko;->svN:Lcom/bytedance/adsdk/ugeno/core/Tc;

    return-void
.end method

.method public Fj(Lcom/bytedance/adsdk/ugeno/core/dG;)V
    .locals 1

    new-instance v0, Lcom/bytedance/adsdk/ugeno/core/Fj/Fj;

    invoke-direct {v0, p1}, Lcom/bytedance/adsdk/ugeno/core/Fj/Fj;-><init>(Lcom/bytedance/adsdk/ugeno/core/dG;)V

    iget-object p1, p0, Lcom/bytedance/adsdk/ugeno/core/Ko;->Ko:Lorg/json/JSONObject;

    invoke-virtual {v0, p1}, Lcom/bytedance/adsdk/ugeno/core/Fj/Fj;->Fj(Lorg/json/JSONObject;)V

    iget-boolean p1, p0, Lcom/bytedance/adsdk/ugeno/core/Ko;->rAx:Z

    invoke-virtual {v0, p1}, Lcom/bytedance/adsdk/ugeno/core/Fj/Fj;->Fj(Z)V

    iget-boolean p1, p0, Lcom/bytedance/adsdk/ugeno/core/Ko;->UYd:Z

    invoke-virtual {v0, p1}, Lcom/bytedance/adsdk/ugeno/core/Fj/Fj;->ex(Z)V

    iget-object p1, p0, Lcom/bytedance/adsdk/ugeno/core/Ko;->BcC:Lcom/bytedance/adsdk/ugeno/core/WR;

    if-eqz p1, :cond_0

    invoke-virtual {p1}, Lcom/bytedance/adsdk/ugeno/core/WR;->ex()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v0, p1}, Lcom/bytedance/adsdk/ugeno/core/Fj/Fj;->Fj(Ljava/lang/String;)V

    :cond_0
    iput-object v0, p0, Lcom/bytedance/adsdk/ugeno/core/Ko;->Ubf:Lcom/bytedance/adsdk/ugeno/core/dG;

    return-void
.end method

.method public ex(Lorg/json/JSONObject;)V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/ugeno/core/Ko;->WR:Lcom/bytedance/adsdk/ugeno/core/JU;

    if-eqz v0, :cond_0

    invoke-interface {v0}, Lcom/bytedance/adsdk/ugeno/core/JU;->hjc()V

    :cond_0
    iput-object p1, p0, Lcom/bytedance/adsdk/ugeno/core/Ko;->ex:Lorg/json/JSONObject;

    iget-object v0, p0, Lcom/bytedance/adsdk/ugeno/core/Ko;->hjc:Lcom/bytedance/adsdk/ugeno/component/ex;

    invoke-virtual {p0, v0, p1}, Lcom/bytedance/adsdk/ugeno/core/Ko;->Fj(Lcom/bytedance/adsdk/ugeno/component/ex;Lorg/json/JSONObject;)V

    iget-object p1, p0, Lcom/bytedance/adsdk/ugeno/core/Ko;->hjc:Lcom/bytedance/adsdk/ugeno/component/ex;

    invoke-direct {p0, p1}, Lcom/bytedance/adsdk/ugeno/core/Ko;->Fj(Lcom/bytedance/adsdk/ugeno/component/ex;)V

    iget-object p1, p0, Lcom/bytedance/adsdk/ugeno/core/Ko;->WR:Lcom/bytedance/adsdk/ugeno/core/JU;

    if-eqz p1, :cond_1

    new-instance p1, Lcom/bytedance/adsdk/ugeno/core/JW;

    invoke-direct {p1}, Lcom/bytedance/adsdk/ugeno/core/JW;-><init>()V

    const/4 v0, 0x0

    invoke-virtual {p1, v0}, Lcom/bytedance/adsdk/ugeno/core/JW;->Fj(I)V

    iget-object v0, p0, Lcom/bytedance/adsdk/ugeno/core/Ko;->hjc:Lcom/bytedance/adsdk/ugeno/component/ex;

    invoke-virtual {p1, v0}, Lcom/bytedance/adsdk/ugeno/core/JW;->Fj(Lcom/bytedance/adsdk/ugeno/component/ex;)V

    iget-object v0, p0, Lcom/bytedance/adsdk/ugeno/core/Ko;->WR:Lcom/bytedance/adsdk/ugeno/core/JU;

    invoke-interface {v0, p1}, Lcom/bytedance/adsdk/ugeno/core/JU;->Fj(Lcom/bytedance/adsdk/ugeno/core/JW;)V

    :cond_1
    return-void
.end method
