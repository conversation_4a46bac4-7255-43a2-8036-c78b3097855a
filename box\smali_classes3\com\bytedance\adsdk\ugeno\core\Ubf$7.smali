.class Lcom/bytedance/adsdk/ugeno/core/Ubf$7;
.super Lcom/bytedance/adsdk/ugeno/core/ex;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/adsdk/ugeno/core/Ubf;->Fj()Ljava/util/List;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/adsdk/ugeno/core/Ubf;


# direct methods
.method public constructor <init>(Lcom/bytedance/adsdk/ugeno/core/Ubf;Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/adsdk/ugeno/core/Ubf$7;->Fj:Lcom/bytedance/adsdk/ugeno/core/Ubf;

    invoke-direct {p0, p2}, Lcom/bytedance/adsdk/ugeno/core/ex;-><init>(Ljava/lang/String;)V

    return-void
.end method


# virtual methods
.method public Fj(Landroid/content/Context;)Lcom/bytedance/adsdk/ugeno/component/ex;
    .locals 1

    new-instance v0, Lcom/bytedance/adsdk/ugeno/component/scroll/Fj;

    invoke-direct {v0, p1}, Lcom/bytedance/adsdk/ugeno/component/scroll/Fj;-><init>(Landroid/content/Context;)V

    return-object v0
.end method
