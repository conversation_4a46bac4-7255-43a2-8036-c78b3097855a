.class public Lcom/bytedance/adsdk/ugeno/core/Ubf;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/adsdk/ugeno/core/hjc;


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public Fj()Ljava/util/List;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lcom/bytedance/adsdk/ugeno/core/ex;",
            ">;"
        }
    .end annotation

    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    new-instance v1, Lcom/bytedance/adsdk/ugeno/core/Ubf$1;

    const-string v2, "Text"

    invoke-direct {v1, p0, v2}, Lcom/bytedance/adsdk/ugeno/core/Ubf$1;-><init>(Lcom/bytedance/adsdk/ugeno/core/Ubf;Ljava/lang/String;)V

    invoke-interface {v0, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    new-instance v1, Lcom/bytedance/adsdk/ugeno/core/Ubf$4;

    const-string v2, "Image"

    invoke-direct {v1, p0, v2}, Lcom/bytedance/adsdk/ugeno/core/Ubf$4;-><init>(Lcom/bytedance/adsdk/ugeno/core/Ubf;Ljava/lang/String;)V

    invoke-interface {v0, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    new-instance v1, Lcom/bytedance/adsdk/ugeno/core/Ubf$5;

    const-string v2, "FlexLayout"

    invoke-direct {v1, p0, v2}, Lcom/bytedance/adsdk/ugeno/core/Ubf$5;-><init>(Lcom/bytedance/adsdk/ugeno/core/Ubf;Ljava/lang/String;)V

    invoke-interface {v0, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    new-instance v1, Lcom/bytedance/adsdk/ugeno/core/Ubf$6;

    const-string v2, "FrameLayout"

    invoke-direct {v1, p0, v2}, Lcom/bytedance/adsdk/ugeno/core/Ubf$6;-><init>(Lcom/bytedance/adsdk/ugeno/core/Ubf;Ljava/lang/String;)V

    invoke-interface {v0, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    new-instance v1, Lcom/bytedance/adsdk/ugeno/core/Ubf$7;

    const-string v2, "ScrollLayout"

    invoke-direct {v1, p0, v2}, Lcom/bytedance/adsdk/ugeno/core/Ubf$7;-><init>(Lcom/bytedance/adsdk/ugeno/core/Ubf;Ljava/lang/String;)V

    invoke-interface {v0, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    new-instance v1, Lcom/bytedance/adsdk/ugeno/core/Ubf$8;

    const-string v2, "RichText"

    invoke-direct {v1, p0, v2}, Lcom/bytedance/adsdk/ugeno/core/Ubf$8;-><init>(Lcom/bytedance/adsdk/ugeno/core/Ubf;Ljava/lang/String;)V

    invoke-interface {v0, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    new-instance v1, Lcom/bytedance/adsdk/ugeno/core/Ubf$9;

    const-string v2, "Input"

    invoke-direct {v1, p0, v2}, Lcom/bytedance/adsdk/ugeno/core/Ubf$9;-><init>(Lcom/bytedance/adsdk/ugeno/core/Ubf;Ljava/lang/String;)V

    invoke-interface {v0, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    new-instance v1, Lcom/bytedance/adsdk/ugeno/core/Ubf$10;

    const-string v2, "Dislike"

    invoke-direct {v1, p0, v2}, Lcom/bytedance/adsdk/ugeno/core/Ubf$10;-><init>(Lcom/bytedance/adsdk/ugeno/core/Ubf;Ljava/lang/String;)V

    invoke-interface {v0, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    new-instance v1, Lcom/bytedance/adsdk/ugeno/core/Ubf$11;

    const-string v2, "RatingBar"

    invoke-direct {v1, p0, v2}, Lcom/bytedance/adsdk/ugeno/core/Ubf$11;-><init>(Lcom/bytedance/adsdk/ugeno/core/Ubf;Ljava/lang/String;)V

    invoke-interface {v0, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    new-instance v1, Lcom/bytedance/adsdk/ugeno/core/Ubf$2;

    const-string v2, "UgenProgressView"

    invoke-direct {v1, p0, v2}, Lcom/bytedance/adsdk/ugeno/core/Ubf$2;-><init>(Lcom/bytedance/adsdk/ugeno/core/Ubf;Ljava/lang/String;)V

    invoke-interface {v0, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    new-instance v1, Lcom/bytedance/adsdk/ugeno/core/Ubf$3;

    const-string v2, "ProgressButton"

    invoke-direct {v1, p0, v2}, Lcom/bytedance/adsdk/ugeno/core/Ubf$3;-><init>(Lcom/bytedance/adsdk/ugeno/core/Ubf;Ljava/lang/String;)V

    invoke-interface {v0, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-object v0
.end method
