.class public Lcom/bytedance/adsdk/ugeno/core/WR$Fj;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bytedance/adsdk/ugeno/core/WR;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "Fj"
.end annotation


# instance fields
.field private Fj:Ljava/lang/String;

.field private Ubf:Lcom/bytedance/adsdk/ugeno/core/WR$Fj;

.field private WR:Ljava/lang/String;

.field private eV:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lcom/bytedance/adsdk/ugeno/core/WR$Fj;",
            ">;"
        }
    .end annotation
.end field

.field private ex:Ljava/lang/String;

.field private hjc:Lorg/json/JSONObject;


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static synthetic Fj(Lcom/bytedance/adsdk/ugeno/core/WR$Fj;Lcom/bytedance/adsdk/ugeno/core/WR$Fj;)Lcom/bytedance/adsdk/ugeno/core/WR$Fj;
    .locals 0

    iput-object p1, p0, Lcom/bytedance/adsdk/ugeno/core/WR$Fj;->Ubf:Lcom/bytedance/adsdk/ugeno/core/WR$Fj;

    return-object p1
.end method

.method public static synthetic Fj(Lcom/bytedance/adsdk/ugeno/core/WR$Fj;Ljava/lang/String;)Ljava/lang/String;
    .locals 0

    iput-object p1, p0, Lcom/bytedance/adsdk/ugeno/core/WR$Fj;->Fj:Ljava/lang/String;

    return-object p1
.end method

.method public static synthetic Fj(Lcom/bytedance/adsdk/ugeno/core/WR$Fj;Lorg/json/JSONObject;)Lorg/json/JSONObject;
    .locals 0

    iput-object p1, p0, Lcom/bytedance/adsdk/ugeno/core/WR$Fj;->hjc:Lorg/json/JSONObject;

    return-object p1
.end method

.method public static synthetic ex(Lcom/bytedance/adsdk/ugeno/core/WR$Fj;Ljava/lang/String;)Ljava/lang/String;
    .locals 0

    iput-object p1, p0, Lcom/bytedance/adsdk/ugeno/core/WR$Fj;->ex:Ljava/lang/String;

    return-object p1
.end method

.method public static synthetic ex(Lcom/bytedance/adsdk/ugeno/core/WR$Fj;)Lorg/json/JSONObject;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/adsdk/ugeno/core/WR$Fj;->hjc:Lorg/json/JSONObject;

    return-object p0
.end method

.method public static synthetic hjc(Lcom/bytedance/adsdk/ugeno/core/WR$Fj;Ljava/lang/String;)Ljava/lang/String;
    .locals 0

    iput-object p1, p0, Lcom/bytedance/adsdk/ugeno/core/WR$Fj;->WR:Ljava/lang/String;

    return-object p1
.end method


# virtual methods
.method public Fj()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/ugeno/core/WR$Fj;->Fj:Ljava/lang/String;

    return-object v0
.end method

.method public Fj(Lcom/bytedance/adsdk/ugeno/core/WR$Fj;)V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/ugeno/core/WR$Fj;->eV:Ljava/util/List;

    if-nez v0, :cond_0

    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lcom/bytedance/adsdk/ugeno/core/WR$Fj;->eV:Ljava/util/List;

    :cond_0
    iget-object v0, p0, Lcom/bytedance/adsdk/ugeno/core/WR$Fj;->eV:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public Ubf()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lcom/bytedance/adsdk/ugeno/core/WR$Fj;",
            ">;"
        }
    .end annotation

    iget-object v0, p0, Lcom/bytedance/adsdk/ugeno/core/WR$Fj;->eV:Ljava/util/List;

    return-object v0
.end method

.method public eV()Lorg/json/JSONObject;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/ugeno/core/WR$Fj;->hjc:Lorg/json/JSONObject;

    return-object v0
.end method

.method public ex()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/ugeno/core/WR$Fj;->WR:Ljava/lang/String;

    return-object v0
.end method

.method public hjc()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/ugeno/core/WR$Fj;->ex:Ljava/lang/String;

    return-object v0
.end method

.method public toString()Ljava/lang/String;
    .locals 3

    new-instance v0, Ljava/lang/StringBuilder;

    const-string v1, "UGNode{id=\'"

    invoke-direct {v0, v1}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    iget-object v1, p0, Lcom/bytedance/adsdk/ugeno/core/WR$Fj;->Fj:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const/16 v1, 0x27

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    const-string v2, ", name=\'"

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v2, p0, Lcom/bytedance/adsdk/ugeno/core/WR$Fj;->ex:Ljava/lang/String;

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    const/16 v1, 0x7d

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
