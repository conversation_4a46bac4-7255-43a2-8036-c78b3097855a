.class public interface abstract Lcom/bytedance/adsdk/ugeno/core/dG$ex;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bytedance/adsdk/ugeno/core/dG;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "ex"
.end annotation


# virtual methods
.method public abstract Fj(Lcom/bytedance/adsdk/ugeno/core/rAx;)V
.end method
