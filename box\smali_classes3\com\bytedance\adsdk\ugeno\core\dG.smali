.class public interface abstract Lcom/bytedance/adsdk/ugeno/core/dG;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/bytedance/adsdk/ugeno/core/dG$Fj;,
        Lcom/bytedance/adsdk/ugeno/core/dG$ex;
    }
.end annotation


# virtual methods
.method public abstract Fj(Lcom/bytedance/adsdk/ugeno/core/rAx;Lcom/bytedance/adsdk/ugeno/core/dG$ex;Lcom/bytedance/adsdk/ugeno/core/dG$Fj;)V
.end method
