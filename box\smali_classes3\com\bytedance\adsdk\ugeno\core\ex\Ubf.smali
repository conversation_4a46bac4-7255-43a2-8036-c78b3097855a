.class public Lcom/bytedance/adsdk/ugeno/core/ex/Ubf;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/adsdk/ugeno/ex/mSE$Fj;


# instance fields
.field private Fj:Z

.field private Ubf:Lcom/bytedance/adsdk/ugeno/core/rAx;

.field private WR:Lcom/bytedance/adsdk/ugeno/component/ex;

.field private eV:Landroid/content/Context;

.field private ex:I

.field private hjc:Lcom/bytedance/adsdk/ugeno/core/dG;

.field private svN:Landroid/os/Handler;


# direct methods
.method public constructor <init>(Landroid/content/Context;Lcom/bytedance/adsdk/ugeno/core/rAx;Lcom/bytedance/adsdk/ugeno/component/ex;)V
    .locals 2

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    new-instance v0, Lcom/bytedance/adsdk/ugeno/ex/mSE;

    invoke-static {}, Landroid/os/Looper;->getMainLooper()Landroid/os/Looper;

    move-result-object v1

    invoke-direct {v0, v1, p0}, Lcom/bytedance/adsdk/ugeno/ex/mSE;-><init>(Landroid/os/Looper;Lcom/bytedance/adsdk/ugeno/ex/mSE$Fj;)V

    iput-object v0, p0, Lcom/bytedance/adsdk/ugeno/core/ex/Ubf;->svN:Landroid/os/Handler;

    iput-object p1, p0, Lcom/bytedance/adsdk/ugeno/core/ex/Ubf;->eV:Landroid/content/Context;

    iput-object p2, p0, Lcom/bytedance/adsdk/ugeno/core/ex/Ubf;->Ubf:Lcom/bytedance/adsdk/ugeno/core/rAx;

    iput-object p3, p0, Lcom/bytedance/adsdk/ugeno/core/ex/Ubf;->WR:Lcom/bytedance/adsdk/ugeno/component/ex;

    return-void
.end method


# virtual methods
.method public Fj()V
    .locals 4

    iget-object v0, p0, Lcom/bytedance/adsdk/ugeno/core/ex/Ubf;->Ubf:Lcom/bytedance/adsdk/ugeno/core/rAx;

    if-nez v0, :cond_0

    return-void

    :cond_0
    invoke-virtual {v0}, Lcom/bytedance/adsdk/ugeno/core/rAx;->hjc()Lorg/json/JSONObject;

    move-result-object v0

    const-string v1, "interval"

    const-string v2, "8000"

    invoke-virtual {v0, v1, v2}, Lorg/json/JSONObject;->optString(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v1

    iget-object v2, p0, Lcom/bytedance/adsdk/ugeno/core/ex/Ubf;->WR:Lcom/bytedance/adsdk/ugeno/component/ex;

    invoke-virtual {v2}, Lcom/bytedance/adsdk/ugeno/component/ex;->Ko()Lorg/json/JSONObject;

    move-result-object v2

    invoke-static {v1, v2}, Lcom/bytedance/adsdk/ugeno/Fj/hjc;->Fj(Ljava/lang/String;Lorg/json/JSONObject;)Ljava/lang/String;

    move-result-object v1

    :try_start_0
    invoke-static {v1}, Ljava/lang/Integer;->parseInt(Ljava/lang/String;)I

    move-result v1

    iput v1, p0, Lcom/bytedance/adsdk/ugeno/core/ex/Ubf;->ex:I

    const-string v1, "repeat"

    invoke-virtual {v0, v1}, Lorg/json/JSONObject;->optBoolean(Ljava/lang/String;)Z

    move-result v0

    iput-boolean v0, p0, Lcom/bytedance/adsdk/ugeno/core/ex/Ubf;->Fj:Z

    iget-object v0, p0, Lcom/bytedance/adsdk/ugeno/core/ex/Ubf;->svN:Landroid/os/Handler;

    iget v1, p0, Lcom/bytedance/adsdk/ugeno/core/ex/Ubf;->ex:I

    int-to-long v1, v1

    const/16 v3, 0x3e9

    invoke-virtual {v0, v3, v1, v2}, Landroid/os/Handler;->sendEmptyMessageDelayed(IJ)Z
    :try_end_0
    .catch Ljava/lang/NumberFormatException; {:try_start_0 .. :try_end_0} :catch_0

    :catch_0
    return-void
.end method

.method public Fj(Landroid/os/Message;)V
    .locals 3

    iget p1, p1, Landroid/os/Message;->what:I

    const/16 v0, 0x3e9

    if-eq p1, v0, :cond_0

    goto :goto_0

    :cond_0
    iget-object p1, p0, Lcom/bytedance/adsdk/ugeno/core/ex/Ubf;->hjc:Lcom/bytedance/adsdk/ugeno/core/dG;

    if-eqz p1, :cond_1

    iget-object v1, p0, Lcom/bytedance/adsdk/ugeno/core/ex/Ubf;->Ubf:Lcom/bytedance/adsdk/ugeno/core/rAx;

    iget-object v2, p0, Lcom/bytedance/adsdk/ugeno/core/ex/Ubf;->WR:Lcom/bytedance/adsdk/ugeno/component/ex;

    invoke-interface {p1, v1, v2, v2}, Lcom/bytedance/adsdk/ugeno/core/dG;->Fj(Lcom/bytedance/adsdk/ugeno/core/rAx;Lcom/bytedance/adsdk/ugeno/core/dG$ex;Lcom/bytedance/adsdk/ugeno/core/dG$Fj;)V

    :cond_1
    iget-boolean p1, p0, Lcom/bytedance/adsdk/ugeno/core/ex/Ubf;->Fj:Z

    if-eqz p1, :cond_2

    iget-object p1, p0, Lcom/bytedance/adsdk/ugeno/core/ex/Ubf;->svN:Landroid/os/Handler;

    iget v1, p0, Lcom/bytedance/adsdk/ugeno/core/ex/Ubf;->ex:I

    int-to-long v1, v1

    invoke-virtual {p1, v0, v1, v2}, Landroid/os/Handler;->sendEmptyMessageDelayed(IJ)Z

    return-void

    :cond_2
    iget-object p1, p0, Lcom/bytedance/adsdk/ugeno/core/ex/Ubf;->svN:Landroid/os/Handler;

    invoke-virtual {p1, v0}, Landroid/os/Handler;->removeMessages(I)V

    :goto_0
    return-void
.end method

.method public Fj(Lcom/bytedance/adsdk/ugeno/core/dG;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/adsdk/ugeno/core/ex/Ubf;->hjc:Lcom/bytedance/adsdk/ugeno/core/dG;

    return-void
.end method
