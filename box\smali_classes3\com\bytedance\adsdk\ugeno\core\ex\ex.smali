.class public Lcom/bytedance/adsdk/ugeno/core/ex/ex;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/adsdk/ugeno/ex/mSE$Fj;


# instance fields
.field private Fj:I

.field private Ubf:Lcom/bytedance/adsdk/ugeno/component/ex;

.field private WR:Landroid/os/Handler;

.field private eV:Lcom/bytedance/adsdk/ugeno/core/rAx;

.field private ex:Lcom/bytedance/adsdk/ugeno/core/dG;

.field private hjc:Landroid/content/Context;


# direct methods
.method public constructor <init>(Landroid/content/Context;Lcom/bytedance/adsdk/ugeno/core/rAx;Lcom/bytedance/adsdk/ugeno/component/ex;)V
    .locals 2

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    new-instance v0, Lcom/bytedance/adsdk/ugeno/ex/mSE;

    invoke-static {}, Landroid/os/Looper;->getMainLooper()Landroid/os/Looper;

    move-result-object v1

    invoke-direct {v0, v1, p0}, Lcom/bytedance/adsdk/ugeno/ex/mSE;-><init>(Landroid/os/Looper;Lcom/bytedance/adsdk/ugeno/ex/mSE$Fj;)V

    iput-object v0, p0, Lcom/bytedance/adsdk/ugeno/core/ex/ex;->WR:Landroid/os/Handler;

    iput-object p1, p0, Lcom/bytedance/adsdk/ugeno/core/ex/ex;->hjc:Landroid/content/Context;

    iput-object p2, p0, Lcom/bytedance/adsdk/ugeno/core/ex/ex;->eV:Lcom/bytedance/adsdk/ugeno/core/rAx;

    iput-object p3, p0, Lcom/bytedance/adsdk/ugeno/core/ex/ex;->Ubf:Lcom/bytedance/adsdk/ugeno/component/ex;

    return-void
.end method


# virtual methods
.method public Fj()V
    .locals 4

    iget-object v0, p0, Lcom/bytedance/adsdk/ugeno/core/ex/ex;->eV:Lcom/bytedance/adsdk/ugeno/core/rAx;

    if-nez v0, :cond_0

    return-void

    :cond_0
    invoke-virtual {v0}, Lcom/bytedance/adsdk/ugeno/core/rAx;->hjc()Lorg/json/JSONObject;

    move-result-object v0

    const-string v1, "delay"

    invoke-virtual {v0, v1}, Lorg/json/JSONObject;->optString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    iget-object v1, p0, Lcom/bytedance/adsdk/ugeno/core/ex/ex;->Ubf:Lcom/bytedance/adsdk/ugeno/component/ex;

    invoke-virtual {v1}, Lcom/bytedance/adsdk/ugeno/component/ex;->Ko()Lorg/json/JSONObject;

    move-result-object v1

    invoke-static {v0, v1}, Lcom/bytedance/adsdk/ugeno/Fj/hjc;->Fj(Ljava/lang/String;Lorg/json/JSONObject;)Ljava/lang/String;

    move-result-object v0

    :try_start_0
    invoke-static {v0}, Ljava/lang/Integer;->parseInt(Ljava/lang/String;)I

    move-result v0

    iput v0, p0, Lcom/bytedance/adsdk/ugeno/core/ex/ex;->Fj:I

    iget-object v1, p0, Lcom/bytedance/adsdk/ugeno/core/ex/ex;->WR:Landroid/os/Handler;

    int-to-long v2, v0

    const/16 v0, 0x3e9

    invoke-virtual {v1, v0, v2, v3}, Landroid/os/Handler;->sendEmptyMessageDelayed(IJ)Z
    :try_end_0
    .catch Ljava/lang/NumberFormatException; {:try_start_0 .. :try_end_0} :catch_0

    :catch_0
    return-void
.end method

.method public Fj(Landroid/os/Message;)V
    .locals 3

    iget p1, p1, Landroid/os/Message;->what:I

    const/16 v0, 0x3e9

    if-eq p1, v0, :cond_0

    goto :goto_1

    :cond_0
    iget-object p1, p0, Lcom/bytedance/adsdk/ugeno/core/ex/ex;->eV:Lcom/bytedance/adsdk/ugeno/core/rAx;

    invoke-virtual {p1}, Lcom/bytedance/adsdk/ugeno/core/rAx;->hjc()Lorg/json/JSONObject;

    move-result-object p1

    const-string v1, "type"

    invoke-virtual {p1, v1}, Lorg/json/JSONObject;->optString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v1

    const-string v2, "onAnimation"

    invoke-static {v1, v2}, Landroid/text/TextUtils;->equals(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Z

    move-result v1

    if-eqz v1, :cond_1

    const-string v1, "nodeId"

    invoke-virtual {p1, v1}, Lorg/json/JSONObject;->optString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v1

    iget-object v2, p0, Lcom/bytedance/adsdk/ugeno/core/ex/ex;->Ubf:Lcom/bytedance/adsdk/ugeno/component/ex;

    invoke-virtual {v2, v2}, Lcom/bytedance/adsdk/ugeno/component/ex;->ex(Lcom/bytedance/adsdk/ugeno/component/ex;)Lcom/bytedance/adsdk/ugeno/component/ex;

    move-result-object v2

    invoke-virtual {v2, v1}, Lcom/bytedance/adsdk/ugeno/component/ex;->ex(Ljava/lang/String;)Lcom/bytedance/adsdk/ugeno/component/ex;

    move-result-object v1

    const-string v2, "animatorSet"

    invoke-virtual {p1, v2}, Lorg/json/JSONObject;->optJSONObject(Ljava/lang/String;)Lorg/json/JSONObject;

    move-result-object p1

    invoke-static {p1, v1}, Lcom/bytedance/adsdk/ugeno/core/Fj;->Fj(Lorg/json/JSONObject;Lcom/bytedance/adsdk/ugeno/component/ex;)Lcom/bytedance/adsdk/ugeno/core/Fj;

    move-result-object p1

    new-instance v2, Lcom/bytedance/adsdk/ugeno/core/BcC;

    invoke-virtual {v1}, Lcom/bytedance/adsdk/ugeno/component/ex;->mSE()Landroid/view/View;

    move-result-object v1

    invoke-direct {v2, v1, p1}, Lcom/bytedance/adsdk/ugeno/core/BcC;-><init>(Landroid/view/View;Lcom/bytedance/adsdk/ugeno/core/Fj;)V

    invoke-virtual {v2}, Lcom/bytedance/adsdk/ugeno/core/BcC;->Fj()V

    goto :goto_0

    :cond_1
    iget-object p1, p0, Lcom/bytedance/adsdk/ugeno/core/ex/ex;->ex:Lcom/bytedance/adsdk/ugeno/core/dG;

    if-eqz p1, :cond_2

    iget-object v1, p0, Lcom/bytedance/adsdk/ugeno/core/ex/ex;->eV:Lcom/bytedance/adsdk/ugeno/core/rAx;

    iget-object v2, p0, Lcom/bytedance/adsdk/ugeno/core/ex/ex;->Ubf:Lcom/bytedance/adsdk/ugeno/component/ex;

    invoke-interface {p1, v1, v2, v2}, Lcom/bytedance/adsdk/ugeno/core/dG;->Fj(Lcom/bytedance/adsdk/ugeno/core/rAx;Lcom/bytedance/adsdk/ugeno/core/dG$ex;Lcom/bytedance/adsdk/ugeno/core/dG$Fj;)V

    :cond_2
    :goto_0
    iget-object p1, p0, Lcom/bytedance/adsdk/ugeno/core/ex/ex;->WR:Landroid/os/Handler;

    invoke-virtual {p1, v0}, Landroid/os/Handler;->removeMessages(I)V

    :goto_1
    return-void
.end method

.method public Fj(Lcom/bytedance/adsdk/ugeno/core/dG;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/adsdk/ugeno/core/ex/ex;->ex:Lcom/bytedance/adsdk/ugeno/core/dG;

    return-void
.end method
