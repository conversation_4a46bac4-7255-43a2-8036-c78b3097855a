.class public Lcom/bytedance/adsdk/ugeno/core/rAx;
.super Ljava/lang/Object;


# instance fields
.field private Fj:Lcom/bytedance/adsdk/ugeno/component/ex;

.field private Ubf:Lcom/bytedance/adsdk/ugeno/core/rAx;

.field private eV:Lcom/bytedance/adsdk/ugeno/core/rAx;

.field private ex:I

.field private hjc:Lorg/json/JSONObject;


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public Fj()Lcom/bytedance/adsdk/ugeno/component/ex;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/ugeno/core/rAx;->Fj:Lcom/bytedance/adsdk/ugeno/component/ex;

    return-object v0
.end method

.method public Fj(I)V
    .locals 0

    iput p1, p0, Lcom/bytedance/adsdk/ugeno/core/rAx;->ex:I

    return-void
.end method

.method public Fj(Lcom/bytedance/adsdk/ugeno/component/ex;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/adsdk/ugeno/core/rAx;->Fj:Lcom/bytedance/adsdk/ugeno/component/ex;

    return-void
.end method

.method public Fj(Lcom/bytedance/adsdk/ugeno/core/rAx;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/adsdk/ugeno/core/rAx;->eV:Lcom/bytedance/adsdk/ugeno/core/rAx;

    return-void
.end method

.method public Fj(Lorg/json/JSONObject;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/adsdk/ugeno/core/rAx;->hjc:Lorg/json/JSONObject;

    return-void
.end method

.method public eV()Lcom/bytedance/adsdk/ugeno/core/rAx;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/ugeno/core/rAx;->eV:Lcom/bytedance/adsdk/ugeno/core/rAx;

    return-object v0
.end method

.method public ex()I
    .locals 1

    iget v0, p0, Lcom/bytedance/adsdk/ugeno/core/rAx;->ex:I

    return v0
.end method

.method public ex(Lcom/bytedance/adsdk/ugeno/core/rAx;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/adsdk/ugeno/core/rAx;->Ubf:Lcom/bytedance/adsdk/ugeno/core/rAx;

    return-void
.end method

.method public hjc()Lorg/json/JSONObject;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/ugeno/core/rAx;->hjc:Lorg/json/JSONObject;

    return-object v0
.end method

.method public toString()Ljava/lang/String;
    .locals 2

    new-instance v0, Ljava/lang/StringBuilder;

    const-string v1, "UGenEvent{mWidget="

    invoke-direct {v0, v1}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    iget-object v1, p0, Lcom/bytedance/adsdk/ugeno/core/rAx;->Fj:Lcom/bytedance/adsdk/ugeno/component/ex;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v1, ", mEventType="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget v1, p0, Lcom/bytedance/adsdk/ugeno/core/rAx;->ex:I

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v1, ", mEvent="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v1, p0, Lcom/bytedance/adsdk/ugeno/core/rAx;->hjc:Lorg/json/JSONObject;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const/16 v1, 0x7d

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
