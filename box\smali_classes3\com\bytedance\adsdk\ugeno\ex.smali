.class public interface abstract Lcom/bytedance/adsdk/ugeno/ex;
.super Ljava/lang/Object;


# virtual methods
.method public abstract Fj(IIII)V
.end method

.method public abstract Fj(Landroid/graphics/Canvas;Lcom/bytedance/adsdk/ugeno/core/IAnimation;)V
.end method

.method public abstract Fj(II)[I
.end method

.method public abstract Ubf()V
.end method

.method public abstract WR()V
.end method

.method public abstract eV()V
.end method

.method public abstract ex(IIII)V
.end method

.method public abstract svN()V
.end method
