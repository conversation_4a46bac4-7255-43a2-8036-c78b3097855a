.class public final Lcom/bytedance/adsdk/ugeno/ex/hjc;
.super Ljava/lang/Object;


# direct methods
.method public static Fj(Ljava/lang/String;F)F
    .locals 0

    :try_start_0
    invoke-static {p0}, Lja<PERSON>/lang/Float;->parseFloat(Ljava/lang/String;)F

    move-result p1
    :try_end_0
    .catch Ljava/lang/NumberFormatException; {:try_start_0 .. :try_end_0} :catch_0

    :catch_0
    return p1
.end method

.method public static Fj(Ljava/lang/String;I)I
    .locals 0

    :try_start_0
    invoke-static {p0}, L<PERSON>va/lang/Integer;->parseInt(Ljava/lang/String;)I

    move-result p1
    :try_end_0
    .catch Ljava/lang/NumberFormatException; {:try_start_0 .. :try_end_0} :catch_0

    :catch_0
    return p1
.end method

.method public static Fj(Ljava/lang/String;J)J
    .locals 0

    :try_start_0
    invoke-static {p0}, <PERSON><PERSON><PERSON>/lang/Long;->parseLong(<PERSON>java/lang/String;)J

    move-result-wide p1
    :try_end_0
    .catch Ljava/lang/NumberFormatException; {:try_start_0 .. :try_end_0} :catch_0

    :catch_0
    return-wide p1
.end method

.method public static Fj(Ljava/lang/String;Z)Z
    .locals 0

    :try_start_0
    invoke-static {p0}, Ljava/lang/Boolean;->parseBoolean(Ljava/lang/String;)Z

    move-result p1
    :try_end_0
    .catch Ljava/lang/NumberFormatException; {:try_start_0 .. :try_end_0} :catch_0

    :catch_0
    return p1
.end method
