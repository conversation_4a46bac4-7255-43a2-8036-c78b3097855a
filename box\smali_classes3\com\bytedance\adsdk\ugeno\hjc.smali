.class public Lcom/bytedance/adsdk/ugeno/hjc;
.super Ljava/lang/Object;


# static fields
.field private static volatile Fj:Lcom/bytedance/adsdk/ugeno/hjc;


# instance fields
.field private eV:Lcom/bytedance/adsdk/ugeno/Fj;

.field private ex:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lcom/bytedance/adsdk/ugeno/core/ex;",
            ">;"
        }
    .end annotation
.end field

.field private hjc:Lcom/bytedance/adsdk/ugeno/core/hjc;


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static Fj()Lcom/bytedance/adsdk/ugeno/hjc;
    .locals 2

    sget-object v0, Lcom/bytedance/adsdk/ugeno/hjc;->Fj:Lcom/bytedance/adsdk/ugeno/hjc;

    if-nez v0, :cond_1

    const-class v0, Lcom/bytedance/adsdk/ugeno/hjc;

    monitor-enter v0

    :try_start_0
    sget-object v1, Lcom/bytedance/adsdk/ugeno/hjc;->Fj:Lcom/bytedance/adsdk/ugeno/hjc;

    if-nez v1, :cond_0

    new-instance v1, Lcom/bytedance/adsdk/ugeno/hjc;

    invoke-direct {v1}, Lcom/bytedance/adsdk/ugeno/hjc;-><init>()V

    sput-object v1, Lcom/bytedance/adsdk/ugeno/hjc;->Fj:Lcom/bytedance/adsdk/ugeno/hjc;

    goto :goto_0

    :catchall_0
    move-exception v1

    goto :goto_1

    :cond_0
    :goto_0
    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    goto :goto_2

    :goto_1
    monitor-exit v0

    throw v1

    :cond_1
    :goto_2
    sget-object v0, Lcom/bytedance/adsdk/ugeno/hjc;->Fj:Lcom/bytedance/adsdk/ugeno/hjc;

    return-object v0
.end method

.method private hjc()V
    .locals 2

    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lcom/bytedance/adsdk/ugeno/hjc;->ex:Ljava/util/List;

    new-instance v0, Lcom/bytedance/adsdk/ugeno/core/Ubf;

    invoke-direct {v0}, Lcom/bytedance/adsdk/ugeno/core/Ubf;-><init>()V

    iget-object v1, p0, Lcom/bytedance/adsdk/ugeno/hjc;->ex:Ljava/util/List;

    invoke-virtual {v0}, Lcom/bytedance/adsdk/ugeno/core/Ubf;->Fj()Ljava/util/List;

    move-result-object v0

    invoke-interface {v1, v0}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    iget-object v0, p0, Lcom/bytedance/adsdk/ugeno/hjc;->hjc:Lcom/bytedance/adsdk/ugeno/core/hjc;

    if-eqz v0, :cond_0

    iget-object v1, p0, Lcom/bytedance/adsdk/ugeno/hjc;->ex:Ljava/util/List;

    invoke-interface {v0}, Lcom/bytedance/adsdk/ugeno/core/hjc;->Fj()Ljava/util/List;

    move-result-object v0

    invoke-interface {v1, v0}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    :cond_0
    iget-object v0, p0, Lcom/bytedance/adsdk/ugeno/hjc;->ex:Ljava/util/List;

    invoke-static {v0}, Lcom/bytedance/adsdk/ugeno/core/eV;->Fj(Ljava/util/List;)V

    return-void
.end method


# virtual methods
.method public Fj(Landroid/content/Context;Lcom/bytedance/adsdk/ugeno/core/hjc;Lcom/bytedance/adsdk/ugeno/Fj;)V
    .locals 0

    iput-object p2, p0, Lcom/bytedance/adsdk/ugeno/hjc;->hjc:Lcom/bytedance/adsdk/ugeno/core/hjc;

    iput-object p3, p0, Lcom/bytedance/adsdk/ugeno/hjc;->eV:Lcom/bytedance/adsdk/ugeno/Fj;

    invoke-direct {p0}, Lcom/bytedance/adsdk/ugeno/hjc;->hjc()V

    return-void
.end method

.method public ex()Lcom/bytedance/adsdk/ugeno/Fj;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/adsdk/ugeno/hjc;->eV:Lcom/bytedance/adsdk/ugeno/Fj;

    return-object v0
.end method
