.class public final Lcom/bytedance/frameworks/encryptor/R$dimen;
.super Ljava/lang/Object;


# static fields
.field public static abc_action_bar_content_inset_material:I = 0x7f070000

.field public static abc_action_bar_content_inset_with_nav:I = 0x7f070001

.field public static abc_action_bar_default_height_material:I = 0x7f070002

.field public static abc_action_bar_default_padding_end_material:I = 0x7f070003

.field public static abc_action_bar_default_padding_start_material:I = 0x7f070004

.field public static abc_action_bar_elevation_material:I = 0x7f070005

.field public static abc_action_bar_icon_vertical_padding_material:I = 0x7f070006

.field public static abc_action_bar_overflow_padding_end_material:I = 0x7f070007

.field public static abc_action_bar_overflow_padding_start_material:I = 0x7f070008

.field public static abc_action_bar_stacked_max_height:I = 0x7f070009

.field public static abc_action_bar_stacked_tab_max_width:I = 0x7f07000a

.field public static abc_action_bar_subtitle_bottom_margin_material:I = 0x7f07000b

.field public static abc_action_bar_subtitle_top_margin_material:I = 0x7f07000c

.field public static abc_action_button_min_height_material:I = 0x7f07000d

.field public static abc_action_button_min_width_material:I = 0x7f07000e

.field public static abc_action_button_min_width_overflow_material:I = 0x7f07000f

.field public static abc_alert_dialog_button_bar_height:I = 0x7f070010

.field public static abc_alert_dialog_button_dimen:I = 0x7f070011

.field public static abc_button_inset_horizontal_material:I = 0x7f070012

.field public static abc_button_inset_vertical_material:I = 0x7f070013

.field public static abc_button_padding_horizontal_material:I = 0x7f070014

.field public static abc_button_padding_vertical_material:I = 0x7f070015

.field public static abc_cascading_menus_min_smallest_width:I = 0x7f070016

.field public static abc_config_prefDialogWidth:I = 0x7f070017

.field public static abc_control_corner_material:I = 0x7f070018

.field public static abc_control_inset_material:I = 0x7f070019

.field public static abc_control_padding_material:I = 0x7f07001a

.field public static abc_dialog_corner_radius_material:I = 0x7f07001b

.field public static abc_dialog_fixed_height_major:I = 0x7f07001c

.field public static abc_dialog_fixed_height_minor:I = 0x7f07001d

.field public static abc_dialog_fixed_width_major:I = 0x7f07001e

.field public static abc_dialog_fixed_width_minor:I = 0x7f07001f

.field public static abc_dialog_list_padding_bottom_no_buttons:I = 0x7f070020

.field public static abc_dialog_list_padding_top_no_title:I = 0x7f070021

.field public static abc_dialog_min_width_major:I = 0x7f070022

.field public static abc_dialog_min_width_minor:I = 0x7f070023

.field public static abc_dialog_padding_material:I = 0x7f070024

.field public static abc_dialog_padding_top_material:I = 0x7f070025

.field public static abc_dialog_title_divider_material:I = 0x7f070026

.field public static abc_disabled_alpha_material_dark:I = 0x7f070027

.field public static abc_disabled_alpha_material_light:I = 0x7f070028

.field public static abc_dropdownitem_icon_width:I = 0x7f070029

.field public static abc_dropdownitem_text_padding_left:I = 0x7f07002a

.field public static abc_dropdownitem_text_padding_right:I = 0x7f07002b

.field public static abc_edit_text_inset_bottom_material:I = 0x7f07002c

.field public static abc_edit_text_inset_horizontal_material:I = 0x7f07002d

.field public static abc_edit_text_inset_top_material:I = 0x7f07002e

.field public static abc_floating_window_z:I = 0x7f07002f

.field public static abc_list_item_padding_horizontal_material:I = 0x7f070033

.field public static abc_panel_menu_list_width:I = 0x7f070034

.field public static abc_progress_bar_height_material:I = 0x7f070035

.field public static abc_search_view_preferred_height:I = 0x7f070036

.field public static abc_search_view_preferred_width:I = 0x7f070037

.field public static abc_seekbar_track_background_height_material:I = 0x7f070038

.field public static abc_seekbar_track_progress_height_material:I = 0x7f070039

.field public static abc_select_dialog_padding_start_material:I = 0x7f07003a

.field public static abc_switch_padding:I = 0x7f07003e

.field public static abc_text_size_body_1_material:I = 0x7f07003f

.field public static abc_text_size_body_2_material:I = 0x7f070040

.field public static abc_text_size_button_material:I = 0x7f070041

.field public static abc_text_size_caption_material:I = 0x7f070042

.field public static abc_text_size_display_1_material:I = 0x7f070043

.field public static abc_text_size_display_2_material:I = 0x7f070044

.field public static abc_text_size_display_3_material:I = 0x7f070045

.field public static abc_text_size_display_4_material:I = 0x7f070046

.field public static abc_text_size_headline_material:I = 0x7f070047

.field public static abc_text_size_large_material:I = 0x7f070048

.field public static abc_text_size_medium_material:I = 0x7f070049

.field public static abc_text_size_menu_header_material:I = 0x7f07004a

.field public static abc_text_size_menu_material:I = 0x7f07004b

.field public static abc_text_size_small_material:I = 0x7f07004c

.field public static abc_text_size_subhead_material:I = 0x7f07004d

.field public static abc_text_size_subtitle_material_toolbar:I = 0x7f07004e

.field public static abc_text_size_title_material:I = 0x7f07004f

.field public static abc_text_size_title_material_toolbar:I = 0x7f070050

.field public static compat_button_inset_horizontal_material:I = 0x7f07006e

.field public static compat_button_inset_vertical_material:I = 0x7f07006f

.field public static compat_button_padding_horizontal_material:I = 0x7f070070

.field public static compat_button_padding_vertical_material:I = 0x7f070071

.field public static compat_control_corner_material:I = 0x7f070072

.field public static compat_notification_large_icon_max_height:I = 0x7f070073

.field public static compat_notification_large_icon_max_width:I = 0x7f070074

.field public static disabled_alpha_material_dark:I = 0x7f0700b0

.field public static disabled_alpha_material_light:I = 0x7f0700b1

.field public static highlight_alpha_material_colored:I = 0x7f0700e7

.field public static highlight_alpha_material_dark:I = 0x7f0700e8

.field public static highlight_alpha_material_light:I = 0x7f0700e9

.field public static hint_alpha_material_dark:I = 0x7f0700ea

.field public static hint_alpha_material_light:I = 0x7f0700eb

.field public static hint_pressed_alpha_material_dark:I = 0x7f0700ec

.field public static hint_pressed_alpha_material_light:I = 0x7f0700ed

.field public static notification_action_icon_size:I = 0x7f07037f

.field public static notification_action_text_size:I = 0x7f070380

.field public static notification_big_circle_margin:I = 0x7f070381

.field public static notification_content_margin_start:I = 0x7f070382

.field public static notification_large_icon_height:I = 0x7f070383

.field public static notification_large_icon_width:I = 0x7f070384

.field public static notification_main_column_padding_top:I = 0x7f070385

.field public static notification_media_narrow_margin:I = 0x7f070386

.field public static notification_right_icon_size:I = 0x7f070387

.field public static notification_right_side_padding_top:I = 0x7f070388

.field public static notification_small_icon_background_padding:I = 0x7f070389

.field public static notification_small_icon_size_as_large:I = 0x7f07038a

.field public static notification_subtext_size:I = 0x7f07038b

.field public static notification_top_pad:I = 0x7f07038c

.field public static notification_top_pad_large_text:I = 0x7f07038d

.field public static tooltip_corner_radius:I = 0x7f0703be

.field public static tooltip_horizontal_padding:I = 0x7f0703bf

.field public static tooltip_margin:I = 0x7f0703c0

.field public static tooltip_precise_anchor_extra_offset:I = 0x7f0703c1

.field public static tooltip_precise_anchor_threshold:I = 0x7f0703c2

.field public static tooltip_vertical_padding:I = 0x7f0703c3

.field public static tooltip_y_offset_non_touch:I = 0x7f0703c4

.field public static tooltip_y_offset_touch:I = 0x7f0703c5


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
