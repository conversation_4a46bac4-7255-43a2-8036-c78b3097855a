.class Lcom/bytedance/sdk/component/Fj/Fj$1;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/component/Fj/Fj;->invokeMethod(Ljava/lang/String;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Ljava/lang/String;

.field final synthetic ex:Lcom/bytedance/sdk/component/Fj/Fj;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/component/Fj/Fj;Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/Fj/Fj$1;->ex:Lcom/bytedance/sdk/component/Fj/Fj;

    iput-object p2, p0, Lcom/bytedance/sdk/component/Fj/Fj$1;->Fj:Ljava/lang/String;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 5

    iget-object v0, p0, Lcom/bytedance/sdk/component/Fj/Fj$1;->ex:Lcom/bytedance/sdk/component/Fj/Fj;

    iget-boolean v0, v0, Lcom/bytedance/sdk/component/Fj/Fj;->WR:Z

    if-eqz v0, :cond_0

    return-void

    :cond_0
    :try_start_0
    iget-object v0, p0, Lcom/bytedance/sdk/component/Fj/Fj$1;->ex:Lcom/bytedance/sdk/component/Fj/Fj;

    new-instance v1, Lorg/json/JSONObject;

    iget-object v2, p0, Lcom/bytedance/sdk/component/Fj/Fj$1;->Fj:Ljava/lang/String;

    invoke-direct {v1, v2}, Lorg/json/JSONObject;-><init>(Ljava/lang/String;)V

    invoke-static {v0, v1}, Lcom/bytedance/sdk/component/Fj/Fj;->Fj(Lcom/bytedance/sdk/component/Fj/Fj;Lorg/json/JSONObject;)Lcom/bytedance/sdk/component/Fj/JU;

    move-result-object v0
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    const/4 v0, 0x0

    :goto_0
    invoke-static {v0}, Lcom/bytedance/sdk/component/Fj/JU;->Fj(Lcom/bytedance/sdk/component/Fj/JU;)Z

    move-result v1

    if-eqz v1, :cond_2

    invoke-static {v0}, Ljava/util/Objects;->toString(Ljava/lang/Object;)Ljava/lang/String;

    if-eqz v0, :cond_1

    iget-object v1, p0, Lcom/bytedance/sdk/component/Fj/Fj$1;->ex:Lcom/bytedance/sdk/component/Fj/Fj;

    new-instance v2, Lcom/bytedance/sdk/component/Fj/rS;

    iget v3, v0, Lcom/bytedance/sdk/component/Fj/JU;->Fj:I

    const-string v4, "Failed to parse invocation."

    invoke-direct {v2, v3, v4}, Lcom/bytedance/sdk/component/Fj/rS;-><init>(ILjava/lang/String;)V

    invoke-static {v2}, Lcom/bytedance/sdk/component/Fj/nsB;->Fj(Ljava/lang/Throwable;)Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v2, v0}, Lcom/bytedance/sdk/component/Fj/Fj;->ex(Ljava/lang/String;Lcom/bytedance/sdk/component/Fj/JU;)V

    :cond_1
    return-void

    :cond_2
    iget-object v1, p0, Lcom/bytedance/sdk/component/Fj/Fj$1;->ex:Lcom/bytedance/sdk/component/Fj/Fj;

    invoke-virtual {v1, v0}, Lcom/bytedance/sdk/component/Fj/Fj;->Fj(Lcom/bytedance/sdk/component/Fj/JU;)V

    return-void
.end method
