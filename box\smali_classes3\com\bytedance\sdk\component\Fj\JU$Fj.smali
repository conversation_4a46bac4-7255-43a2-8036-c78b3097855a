.class public final Lcom/bytedance/sdk/component/Fj/JU$Fj;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bytedance/sdk/component/Fj/JU;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "Fj"
.end annotation


# instance fields
.field private Fj:Ljava/lang/String;

.field private Ubf:Ljava/lang/String;

.field private WR:Ljava/lang/String;

.field private eV:Ljava/lang/String;

.field private ex:Ljava/lang/String;

.field private hjc:Ljava/lang/String;

.field private svN:Ljava/lang/String;


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public synthetic constructor <init>(Lcom/bytedance/sdk/component/Fj/JU$1;)V
    .locals 0

    invoke-direct {p0}, Lcom/bytedance/sdk/component/Fj/JU$Fj;-><init>()V

    return-void
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/component/Fj/JU$Fj;)Ljava/lang/String;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/component/Fj/JU$Fj;->Fj:Ljava/lang/String;

    return-object p0
.end method

.method public static synthetic Ubf(Lcom/bytedance/sdk/component/Fj/JU$Fj;)Ljava/lang/String;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/component/Fj/JU$Fj;->Ubf:Ljava/lang/String;

    return-object p0
.end method

.method public static synthetic WR(Lcom/bytedance/sdk/component/Fj/JU$Fj;)Ljava/lang/String;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/component/Fj/JU$Fj;->WR:Ljava/lang/String;

    return-object p0
.end method

.method public static synthetic eV(Lcom/bytedance/sdk/component/Fj/JU$Fj;)Ljava/lang/String;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/component/Fj/JU$Fj;->eV:Ljava/lang/String;

    return-object p0
.end method

.method public static synthetic ex(Lcom/bytedance/sdk/component/Fj/JU$Fj;)Ljava/lang/String;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/component/Fj/JU$Fj;->ex:Ljava/lang/String;

    return-object p0
.end method

.method public static synthetic hjc(Lcom/bytedance/sdk/component/Fj/JU$Fj;)Ljava/lang/String;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/component/Fj/JU$Fj;->hjc:Ljava/lang/String;

    return-object p0
.end method

.method public static synthetic svN(Lcom/bytedance/sdk/component/Fj/JU$Fj;)Ljava/lang/String;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/component/Fj/JU$Fj;->svN:Ljava/lang/String;

    return-object p0
.end method


# virtual methods
.method public Fj(Ljava/lang/String;)Lcom/bytedance/sdk/component/Fj/JU$Fj;
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/Fj/JU$Fj;->Fj:Ljava/lang/String;

    return-object p0
.end method

.method public Fj()Lcom/bytedance/sdk/component/Fj/JU;
    .locals 2

    new-instance v0, Lcom/bytedance/sdk/component/Fj/JU;

    const/4 v1, 0x0

    invoke-direct {v0, p0, v1}, Lcom/bytedance/sdk/component/Fj/JU;-><init>(Lcom/bytedance/sdk/component/Fj/JU$Fj;Lcom/bytedance/sdk/component/Fj/JU$1;)V

    return-object v0
.end method

.method public Ubf(Ljava/lang/String;)Lcom/bytedance/sdk/component/Fj/JU$Fj;
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/Fj/JU$Fj;->Ubf:Ljava/lang/String;

    return-object p0
.end method

.method public WR(Ljava/lang/String;)Lcom/bytedance/sdk/component/Fj/JU$Fj;
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/Fj/JU$Fj;->WR:Ljava/lang/String;

    return-object p0
.end method

.method public eV(Ljava/lang/String;)Lcom/bytedance/sdk/component/Fj/JU$Fj;
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/Fj/JU$Fj;->eV:Ljava/lang/String;

    return-object p0
.end method

.method public ex(Ljava/lang/String;)Lcom/bytedance/sdk/component/Fj/JU$Fj;
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/Fj/JU$Fj;->ex:Ljava/lang/String;

    return-object p0
.end method

.method public hjc(Ljava/lang/String;)Lcom/bytedance/sdk/component/Fj/JU$Fj;
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/Fj/JU$Fj;->hjc:Ljava/lang/String;

    return-object p0
.end method

.method public svN(Ljava/lang/String;)Lcom/bytedance/sdk/component/Fj/JU$Fj;
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/Fj/JU$Fj;->svN:Ljava/lang/String;

    return-object p0
.end method
