.class public Lcom/bytedance/sdk/component/Fj/JU;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/bytedance/sdk/component/Fj/JU$Fj;
    }
.end annotation


# instance fields
.field public final BcC:Ljava/lang/String;

.field public final Fj:I

.field public final Ubf:Ljava/lang/String;

.field public final WR:Ljava/lang/String;

.field public final eV:Ljava/lang/String;

.field public final ex:Ljava/lang/String;

.field public final hjc:Ljava/lang/String;

.field public final svN:Ljava/lang/String;


# direct methods
.method private constructor <init>(Lcom/bytedance/sdk/component/Fj/JU$Fj;)V
    .locals 1

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    invoke-static {p1}, Lcom/bytedance/sdk/component/Fj/JU$Fj;->Fj(Lcom/bytedance/sdk/component/Fj/JU$Fj;)Ljava/lang/String;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/sdk/component/Fj/JU;->ex:Ljava/lang/String;

    invoke-static {p1}, Lcom/bytedance/sdk/component/Fj/JU$Fj;->ex(Lcom/bytedance/sdk/component/Fj/JU$Fj;)Ljava/lang/String;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/sdk/component/Fj/JU;->hjc:Ljava/lang/String;

    invoke-static {p1}, Lcom/bytedance/sdk/component/Fj/JU$Fj;->hjc(Lcom/bytedance/sdk/component/Fj/JU$Fj;)Ljava/lang/String;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/sdk/component/Fj/JU;->eV:Ljava/lang/String;

    invoke-static {p1}, Lcom/bytedance/sdk/component/Fj/JU$Fj;->eV(Lcom/bytedance/sdk/component/Fj/JU$Fj;)Ljava/lang/String;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/sdk/component/Fj/JU;->Ubf:Ljava/lang/String;

    invoke-static {p1}, Lcom/bytedance/sdk/component/Fj/JU$Fj;->Ubf(Lcom/bytedance/sdk/component/Fj/JU$Fj;)Ljava/lang/String;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/sdk/component/Fj/JU;->WR:Ljava/lang/String;

    invoke-static {p1}, Lcom/bytedance/sdk/component/Fj/JU$Fj;->WR(Lcom/bytedance/sdk/component/Fj/JU$Fj;)Ljava/lang/String;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/sdk/component/Fj/JU;->svN:Ljava/lang/String;

    const/4 v0, 0x1

    iput v0, p0, Lcom/bytedance/sdk/component/Fj/JU;->Fj:I

    invoke-static {p1}, Lcom/bytedance/sdk/component/Fj/JU$Fj;->svN(Lcom/bytedance/sdk/component/Fj/JU$Fj;)Ljava/lang/String;

    move-result-object p1

    iput-object p1, p0, Lcom/bytedance/sdk/component/Fj/JU;->BcC:Ljava/lang/String;

    return-void
.end method

.method public synthetic constructor <init>(Lcom/bytedance/sdk/component/Fj/JU$Fj;Lcom/bytedance/sdk/component/Fj/JU$1;)V
    .locals 0

    invoke-direct {p0, p1}, Lcom/bytedance/sdk/component/Fj/JU;-><init>(Lcom/bytedance/sdk/component/Fj/JU$Fj;)V

    return-void
.end method

.method private constructor <init>(Ljava/lang/String;I)V
    .locals 1

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, 0x0

    iput-object v0, p0, Lcom/bytedance/sdk/component/Fj/JU;->ex:Ljava/lang/String;

    iput-object v0, p0, Lcom/bytedance/sdk/component/Fj/JU;->hjc:Ljava/lang/String;

    iput-object v0, p0, Lcom/bytedance/sdk/component/Fj/JU;->eV:Ljava/lang/String;

    iput-object v0, p0, Lcom/bytedance/sdk/component/Fj/JU;->Ubf:Ljava/lang/String;

    iput-object p1, p0, Lcom/bytedance/sdk/component/Fj/JU;->WR:Ljava/lang/String;

    iput-object v0, p0, Lcom/bytedance/sdk/component/Fj/JU;->svN:Ljava/lang/String;

    iput p2, p0, Lcom/bytedance/sdk/component/Fj/JU;->Fj:I

    iput-object v0, p0, Lcom/bytedance/sdk/component/Fj/JU;->BcC:Ljava/lang/String;

    return-void
.end method

.method public static Fj()Lcom/bytedance/sdk/component/Fj/JU$Fj;
    .locals 2

    new-instance v0, Lcom/bytedance/sdk/component/Fj/JU$Fj;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lcom/bytedance/sdk/component/Fj/JU$Fj;-><init>(Lcom/bytedance/sdk/component/Fj/JU$1;)V

    return-object v0
.end method

.method public static Fj(Ljava/lang/String;I)Lcom/bytedance/sdk/component/Fj/JU;
    .locals 1

    new-instance v0, Lcom/bytedance/sdk/component/Fj/JU;

    invoke-direct {v0, p0, p1}, Lcom/bytedance/sdk/component/Fj/JU;-><init>(Ljava/lang/String;I)V

    return-object v0
.end method

.method public static Fj(Lcom/bytedance/sdk/component/Fj/JU;)Z
    .locals 2

    const/4 v0, 0x1

    if-eqz p0, :cond_1

    iget v1, p0, Lcom/bytedance/sdk/component/Fj/JU;->Fj:I

    if-ne v1, v0, :cond_1

    iget-object v1, p0, Lcom/bytedance/sdk/component/Fj/JU;->eV:Ljava/lang/String;

    invoke-static {v1}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v1

    if-nez v1, :cond_1

    iget-object p0, p0, Lcom/bytedance/sdk/component/Fj/JU;->Ubf:Ljava/lang/String;

    invoke-static {p0}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result p0

    if-eqz p0, :cond_0

    goto :goto_0

    :cond_0
    const/4 p0, 0x0

    return p0

    :cond_1
    :goto_0
    return v0
.end method


# virtual methods
.method public toString()Ljava/lang/String;
    .locals 2

    new-instance v0, Ljava/lang/StringBuilder;

    const-string v1, "methodName: "

    invoke-direct {v0, v1}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    iget-object v1, p0, Lcom/bytedance/sdk/component/Fj/JU;->eV:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v1, ", params: "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v1, p0, Lcom/bytedance/sdk/component/Fj/JU;->Ubf:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v1, ", callbackId: "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v1, p0, Lcom/bytedance/sdk/component/Fj/JU;->WR:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v1, ", type: "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v1, p0, Lcom/bytedance/sdk/component/Fj/JU;->hjc:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v1, ", version: "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v1, p0, Lcom/bytedance/sdk/component/Fj/JU;->ex:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v1, ", "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
