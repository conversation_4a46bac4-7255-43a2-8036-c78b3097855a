.class public Lcom/bytedance/sdk/component/Fj/Ko;
.super Ljava/lang/Object;


# instance fields
.field BcC:Z

.field Fj:Landroid/webkit/WebView;

.field JU:Lcom/bytedance/sdk/component/Fj/rAx$Fj;

.field JW:Z

.field Ko:Lcom/bytedance/sdk/component/Fj/Tc;

.field Tc:Z

.field final UYd:Ljava/util/Set;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Set<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field Ubf:Landroid/content/Context;

.field WR:Z

.field final dG:Ljava/util/Set;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Set<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field eV:Lcom/bytedance/sdk/component/Fj/BcC;

.field ex:Lcom/bytedance/sdk/component/Fj/Fj;

.field hjc:Ljava/lang/String;

.field mSE:Lcom/bytedance/sdk/component/Fj/dG;

.field rAx:Ljava/lang/String;

.field svN:Z


# direct methods
.method public constructor <init>()V
    .locals 1

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const-string v0, "IESJSBridge"

    iput-object v0, p0, Lcom/bytedance/sdk/component/Fj/Ko;->hjc:Ljava/lang/String;

    const-string v0, "host"

    iput-object v0, p0, Lcom/bytedance/sdk/component/Fj/Ko;->rAx:Ljava/lang/String;

    new-instance v0, Ljava/util/LinkedHashSet;

    invoke-direct {v0}, Ljava/util/LinkedHashSet;-><init>()V

    iput-object v0, p0, Lcom/bytedance/sdk/component/Fj/Ko;->UYd:Ljava/util/Set;

    new-instance v0, Ljava/util/LinkedHashSet;

    invoke-direct {v0}, Ljava/util/LinkedHashSet;-><init>()V

    iput-object v0, p0, Lcom/bytedance/sdk/component/Fj/Ko;->dG:Ljava/util/Set;

    return-void
.end method

.method public constructor <init>(Landroid/webkit/WebView;)V
    .locals 1

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const-string v0, "IESJSBridge"

    iput-object v0, p0, Lcom/bytedance/sdk/component/Fj/Ko;->hjc:Ljava/lang/String;

    const-string v0, "host"

    iput-object v0, p0, Lcom/bytedance/sdk/component/Fj/Ko;->rAx:Ljava/lang/String;

    new-instance v0, Ljava/util/LinkedHashSet;

    invoke-direct {v0}, Ljava/util/LinkedHashSet;-><init>()V

    iput-object v0, p0, Lcom/bytedance/sdk/component/Fj/Ko;->UYd:Ljava/util/Set;

    new-instance v0, Ljava/util/LinkedHashSet;

    invoke-direct {v0}, Ljava/util/LinkedHashSet;-><init>()V

    iput-object v0, p0, Lcom/bytedance/sdk/component/Fj/Ko;->dG:Ljava/util/Set;

    iput-object p1, p0, Lcom/bytedance/sdk/component/Fj/Ko;->Fj:Landroid/webkit/WebView;

    return-void
.end method

.method private hjc()V
    .locals 2

    iget-object v0, p0, Lcom/bytedance/sdk/component/Fj/Ko;->Fj:Landroid/webkit/WebView;

    if-nez v0, :cond_0

    iget-boolean v0, p0, Lcom/bytedance/sdk/component/Fj/Ko;->Tc:Z

    if-nez v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/sdk/component/Fj/Ko;->ex:Lcom/bytedance/sdk/component/Fj/Fj;

    if-eqz v0, :cond_2

    :cond_0
    iget-object v0, p0, Lcom/bytedance/sdk/component/Fj/Ko;->hjc:Ljava/lang/String;

    invoke-static {v0}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    if-eqz v0, :cond_1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Fj/Ko;->Fj:Landroid/webkit/WebView;

    if-nez v0, :cond_2

    :cond_1
    iget-object v0, p0, Lcom/bytedance/sdk/component/Fj/Ko;->eV:Lcom/bytedance/sdk/component/Fj/BcC;

    if-eqz v0, :cond_2

    return-void

    :cond_2
    new-instance v0, Ljava/lang/IllegalArgumentException;

    const-string v1, "Requested arguments aren\'t set properly when building JsBridge."

    invoke-direct {v0, v1}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw v0
.end method


# virtual methods
.method public Fj()Lcom/bytedance/sdk/component/Fj/Ko;
    .locals 1

    const/4 v0, 0x1

    iput-boolean v0, p0, Lcom/bytedance/sdk/component/Fj/Ko;->JW:Z

    return-object p0
.end method

.method public Fj(Lcom/bytedance/sdk/component/Fj/Fj;)Lcom/bytedance/sdk/component/Fj/Ko;
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/Fj/Ko;->ex:Lcom/bytedance/sdk/component/Fj/Fj;

    return-object p0
.end method

.method public Fj(Lcom/bytedance/sdk/component/Fj/UYd;)Lcom/bytedance/sdk/component/Fj/Ko;
    .locals 0

    invoke-static {p1}, Lcom/bytedance/sdk/component/Fj/BcC;->Fj(Lcom/bytedance/sdk/component/Fj/UYd;)Lcom/bytedance/sdk/component/Fj/BcC;

    move-result-object p1

    iput-object p1, p0, Lcom/bytedance/sdk/component/Fj/Ko;->eV:Lcom/bytedance/sdk/component/Fj/BcC;

    return-object p0
.end method

.method public Fj(Ljava/lang/String;)Lcom/bytedance/sdk/component/Fj/Ko;
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/Fj/Ko;->hjc:Ljava/lang/String;

    return-object p0
.end method

.method public Fj(Z)Lcom/bytedance/sdk/component/Fj/Ko;
    .locals 0

    iput-boolean p1, p0, Lcom/bytedance/sdk/component/Fj/Ko;->WR:Z

    return-object p0
.end method

.method public ex(Z)Lcom/bytedance/sdk/component/Fj/Ko;
    .locals 0

    iput-boolean p1, p0, Lcom/bytedance/sdk/component/Fj/Ko;->svN:Z

    return-object p0
.end method

.method public ex()Lcom/bytedance/sdk/component/Fj/Ql;
    .locals 1

    invoke-direct {p0}, Lcom/bytedance/sdk/component/Fj/Ko;->hjc()V

    new-instance v0, Lcom/bytedance/sdk/component/Fj/Ql;

    invoke-direct {v0, p0}, Lcom/bytedance/sdk/component/Fj/Ql;-><init>(Lcom/bytedance/sdk/component/Fj/Ko;)V

    return-object v0
.end method
