.class public Lcom/bytedance/sdk/component/Fj/Ql;
.super Ljava/lang/Object;


# static fields
.field static Fj:Lcom/bytedance/sdk/component/Fj/mC;


# instance fields
.field private final Ubf:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lcom/bytedance/sdk/component/Fj/Tc;",
            ">;"
        }
    .end annotation
.end field

.field private volatile WR:Z

.field private final eV:Lcom/bytedance/sdk/component/Fj/Ko;

.field private final ex:Lcom/bytedance/sdk/component/Fj/Fj;

.field private final hjc:Landroid/webkit/WebView;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/component/Fj/Ko;)V
    .locals 3

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lcom/bytedance/sdk/component/Fj/Ql;->Ubf:Ljava/util/List;

    const/4 v1, 0x0

    iput-boolean v1, p0, Lcom/bytedance/sdk/component/Fj/Ql;->WR:Z

    iput-object p1, p0, Lcom/bytedance/sdk/component/Fj/Ql;->eV:Lcom/bytedance/sdk/component/Fj/Ko;

    iget-boolean v1, p1, Lcom/bytedance/sdk/component/Fj/Ko;->BcC:Z

    const/4 v2, 0x0

    if-eqz v1, :cond_1

    sget-object v1, Lcom/bytedance/sdk/component/Fj/Ql;->Fj:Lcom/bytedance/sdk/component/Fj/mC;

    if-nez v1, :cond_0

    goto :goto_0

    :cond_0
    throw v2

    :cond_1
    :goto_0
    iget-object v1, p1, Lcom/bytedance/sdk/component/Fj/Ko;->Fj:Landroid/webkit/WebView;

    if-eqz v1, :cond_3

    iget-object v1, p1, Lcom/bytedance/sdk/component/Fj/Ko;->ex:Lcom/bytedance/sdk/component/Fj/Fj;

    if-nez v1, :cond_2

    new-instance v1, Lcom/bytedance/sdk/component/Fj/Vq;

    invoke-direct {v1}, Lcom/bytedance/sdk/component/Fj/Vq;-><init>()V

    iput-object v1, p0, Lcom/bytedance/sdk/component/Fj/Ql;->ex:Lcom/bytedance/sdk/component/Fj/Fj;

    goto :goto_1

    :cond_2
    iput-object v1, p0, Lcom/bytedance/sdk/component/Fj/Ql;->ex:Lcom/bytedance/sdk/component/Fj/Fj;

    goto :goto_1

    :cond_3
    iget-object v1, p1, Lcom/bytedance/sdk/component/Fj/Ko;->ex:Lcom/bytedance/sdk/component/Fj/Fj;

    iput-object v1, p0, Lcom/bytedance/sdk/component/Fj/Ql;->ex:Lcom/bytedance/sdk/component/Fj/Fj;

    :goto_1
    iget-object v1, p0, Lcom/bytedance/sdk/component/Fj/Ql;->ex:Lcom/bytedance/sdk/component/Fj/Fj;

    invoke-virtual {v1, p1, v2}, Lcom/bytedance/sdk/component/Fj/Fj;->Fj(Lcom/bytedance/sdk/component/Fj/Ko;Lcom/bytedance/sdk/component/Fj/Af;)V

    iget-object v1, p1, Lcom/bytedance/sdk/component/Fj/Ko;->Fj:Landroid/webkit/WebView;

    iput-object v1, p0, Lcom/bytedance/sdk/component/Fj/Ql;->hjc:Landroid/webkit/WebView;

    iget-object v1, p1, Lcom/bytedance/sdk/component/Fj/Ko;->Ko:Lcom/bytedance/sdk/component/Fj/Tc;

    invoke-interface {v0, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    iget-boolean p1, p1, Lcom/bytedance/sdk/component/Fj/Ko;->svN:Z

    invoke-static {p1}, Lcom/bytedance/sdk/component/Fj/nsB;->Fj(Z)V

    return-void
.end method

.method public static Fj(Landroid/webkit/WebView;)Lcom/bytedance/sdk/component/Fj/Ko;
    .locals 1

    new-instance v0, Lcom/bytedance/sdk/component/Fj/Ko;

    invoke-direct {v0, p0}, Lcom/bytedance/sdk/component/Fj/Ko;-><init>(Landroid/webkit/WebView;)V

    return-object v0
.end method

.method private ex()V
    .locals 2

    iget-boolean v0, p0, Lcom/bytedance/sdk/component/Fj/Ql;->WR:Z

    if-eqz v0, :cond_0

    new-instance v0, Ljava/lang/IllegalStateException;

    const-string v1, "JsBridge2 is already released!!!"

    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    invoke-static {v0}, Lcom/bytedance/sdk/component/Fj/mSE;->Fj(Ljava/lang/RuntimeException;)V

    :cond_0
    return-void
.end method


# virtual methods
.method public Fj(Ljava/lang/String;Lcom/bytedance/sdk/component/Fj/Ubf;)Lcom/bytedance/sdk/component/Fj/Ql;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Lcom/bytedance/sdk/component/Fj/Ubf<",
            "**>;)",
            "Lcom/bytedance/sdk/component/Fj/Ql;"
        }
    .end annotation

    const/4 v0, 0x0

    invoke-virtual {p0, p1, v0, p2}, Lcom/bytedance/sdk/component/Fj/Ql;->Fj(Ljava/lang/String;Ljava/lang/String;Lcom/bytedance/sdk/component/Fj/Ubf;)Lcom/bytedance/sdk/component/Fj/Ql;

    move-result-object p1

    return-object p1
.end method

.method public Fj(Ljava/lang/String;Lcom/bytedance/sdk/component/Fj/eV$ex;)Lcom/bytedance/sdk/component/Fj/Ql;
    .locals 1

    const/4 v0, 0x0

    invoke-virtual {p0, p1, v0, p2}, Lcom/bytedance/sdk/component/Fj/Ql;->Fj(Ljava/lang/String;Ljava/lang/String;Lcom/bytedance/sdk/component/Fj/eV$ex;)Lcom/bytedance/sdk/component/Fj/Ql;

    move-result-object p1

    return-object p1
.end method

.method public Fj(Ljava/lang/String;Ljava/lang/String;Lcom/bytedance/sdk/component/Fj/Ubf;)Lcom/bytedance/sdk/component/Fj/Ql;
    .locals 0
    .annotation build Lcom/bytedance/component/sdk/annotation/UiThread;
    .end annotation

    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            "Lcom/bytedance/sdk/component/Fj/Ubf<",
            "**>;)",
            "Lcom/bytedance/sdk/component/Fj/Ql;"
        }
    .end annotation

    invoke-direct {p0}, Lcom/bytedance/sdk/component/Fj/Ql;->ex()V

    iget-object p2, p0, Lcom/bytedance/sdk/component/Fj/Ql;->ex:Lcom/bytedance/sdk/component/Fj/Fj;

    iget-object p2, p2, Lcom/bytedance/sdk/component/Fj/Fj;->svN:Lcom/bytedance/sdk/component/Fj/svN;

    invoke-virtual {p2, p1, p3}, Lcom/bytedance/sdk/component/Fj/svN;->Fj(Ljava/lang/String;Lcom/bytedance/sdk/component/Fj/Ubf;)V

    return-object p0
.end method

.method public Fj(Ljava/lang/String;Ljava/lang/String;Lcom/bytedance/sdk/component/Fj/eV$ex;)Lcom/bytedance/sdk/component/Fj/Ql;
    .locals 0
    .annotation build Lcom/bytedance/component/sdk/annotation/UiThread;
    .end annotation

    invoke-direct {p0}, Lcom/bytedance/sdk/component/Fj/Ql;->ex()V

    iget-object p2, p0, Lcom/bytedance/sdk/component/Fj/Ql;->ex:Lcom/bytedance/sdk/component/Fj/Fj;

    iget-object p2, p2, Lcom/bytedance/sdk/component/Fj/Fj;->svN:Lcom/bytedance/sdk/component/Fj/svN;

    invoke-virtual {p2, p1, p3}, Lcom/bytedance/sdk/component/Fj/svN;->Fj(Ljava/lang/String;Lcom/bytedance/sdk/component/Fj/eV$ex;)V

    return-object p0
.end method

.method public Fj()V
    .locals 2

    iget-boolean v0, p0, Lcom/bytedance/sdk/component/Fj/Ql;->WR:Z

    if-eqz v0, :cond_0

    return-void

    :cond_0
    iget-object v0, p0, Lcom/bytedance/sdk/component/Fj/Ql;->ex:Lcom/bytedance/sdk/component/Fj/Fj;

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/Fj/Fj;->ex()V

    const/4 v0, 0x1

    iput-boolean v0, p0, Lcom/bytedance/sdk/component/Fj/Ql;->WR:Z

    iget-object v0, p0, Lcom/bytedance/sdk/component/Fj/Ql;->Ubf:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_1

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    goto :goto_0

    :cond_1
    return-void
.end method
