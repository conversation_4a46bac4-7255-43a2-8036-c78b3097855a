.class public abstract Lcom/bytedance/sdk/component/Fj/Ubf;
.super Lcom/bytedance/sdk/component/Fj/ex;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "<P:",
        "Ljava/lang/Object;",
        "R:",
        "Ljava/lang/Object;",
        ">",
        "Lcom/bytedance/sdk/component/Fj/ex<",
        "TP;TR;>;"
    }
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Lcom/bytedance/sdk/component/Fj/ex;-><init>()V

    return-void
.end method


# virtual methods
.method public abstract Fj(Ljava/lang/Object;Lcom/bytedance/sdk/component/Fj/WR;)Ljava/lang/Object;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(TP;",
            "Lcom/bytedance/sdk/component/Fj/WR;",
            ")TR;"
        }
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/Exception;
        }
    .end annotation
.end method

.method public bridge synthetic Fj()Ljava/lang/String;
    .locals 1

    invoke-super {p0}, Lcom/bytedance/sdk/component/Fj/ex;->Fj()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
