.class public interface abstract Lcom/bytedance/sdk/component/Fj/eV$ex;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bytedance/sdk/component/Fj/eV;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "ex"
.end annotation


# virtual methods
.method public abstract Fj()Lcom/bytedance/sdk/component/Fj/eV;
.end method
