.class public abstract Lcom/bytedance/sdk/component/Fj/eV;
.super Lcom/bytedance/sdk/component/Fj/ex;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/bytedance/sdk/component/Fj/eV$Fj;,
        Lcom/bytedance/sdk/component/Fj/eV$ex;
    }
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "<P:",
        "Ljava/lang/Object;",
        "R:",
        "Ljava/lang/Object;",
        ">",
        "Lcom/bytedance/sdk/component/Fj/ex<",
        "TP;TR;>;"
    }
.end annotation


# instance fields
.field private Fj:Z

.field private ex:Lcom/bytedance/sdk/component/Fj/eV$Fj;

.field private hjc:Lcom/bytedance/sdk/component/Fj/WR;


# direct methods
.method public constructor <init>()V
    .locals 1

    invoke-direct {p0}, Lcom/bytedance/sdk/component/Fj/ex;-><init>()V

    const/4 v0, 0x1

    iput-boolean v0, p0, Lcom/bytedance/sdk/component/Fj/eV;->Fj:Z

    return-void
.end method

.method private WR()Z
    .locals 3

    iget-boolean v0, p0, Lcom/bytedance/sdk/component/Fj/eV;->Fj:Z

    if-nez v0, :cond_0

    new-instance v0, Ljava/lang/IllegalStateException;

    new-instance v1, Ljava/lang/StringBuilder;

    const-string v2, "Jsb async call already finished: "

    invoke-direct {v1, v2}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Fj/eV;->Fj()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v2, ", hashcode: "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p0}, Ljava/lang/Object;->hashCode()I

    move-result v2

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    invoke-static {v0}, Lcom/bytedance/sdk/component/Fj/mSE;->Fj(Ljava/lang/RuntimeException;)V

    const/4 v0, 0x0

    return v0

    :cond_0
    const/4 v0, 0x1

    return v0
.end method


# virtual methods
.method public bridge synthetic Fj()Ljava/lang/String;
    .locals 1

    invoke-super {p0}, Lcom/bytedance/sdk/component/Fj/ex;->Fj()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public abstract Fj(Ljava/lang/Object;Lcom/bytedance/sdk/component/Fj/WR;)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(TP;",
            "Lcom/bytedance/sdk/component/Fj/WR;",
            ")V"
        }
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/Exception;
        }
    .end annotation
.end method

.method public Fj(Ljava/lang/Object;Lcom/bytedance/sdk/component/Fj/WR;Lcom/bytedance/sdk/component/Fj/eV$Fj;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(TP;",
            "Lcom/bytedance/sdk/component/Fj/WR;",
            "Lcom/bytedance/sdk/component/Fj/eV$Fj;",
            ")V"
        }
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/Exception;
        }
    .end annotation

    iput-object p2, p0, Lcom/bytedance/sdk/component/Fj/eV;->hjc:Lcom/bytedance/sdk/component/Fj/WR;

    iput-object p3, p0, Lcom/bytedance/sdk/component/Fj/eV;->ex:Lcom/bytedance/sdk/component/Fj/eV$Fj;

    invoke-virtual {p0, p1, p2}, Lcom/bytedance/sdk/component/Fj/eV;->Fj(Ljava/lang/Object;Lcom/bytedance/sdk/component/Fj/WR;)V

    return-void
.end method

.method public final Fj(Ljava/lang/Throwable;)V
    .locals 1

    invoke-direct {p0}, Lcom/bytedance/sdk/component/Fj/eV;->WR()Z

    move-result v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/sdk/component/Fj/eV;->ex:Lcom/bytedance/sdk/component/Fj/eV$Fj;

    invoke-interface {v0, p1}, Lcom/bytedance/sdk/component/Fj/eV$Fj;->Fj(Ljava/lang/Throwable;)V

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Fj/eV;->eV()V

    :cond_0
    return-void
.end method

.method public Ubf()V
    .locals 0

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Fj/eV;->eV()V

    return-void
.end method

.method public eV()V
    .locals 1
    .annotation build Lcom/bytedance/component/sdk/annotation/CallSuper;
    .end annotation

    const/4 v0, 0x0

    iput-boolean v0, p0, Lcom/bytedance/sdk/component/Fj/eV;->Fj:Z

    const/4 v0, 0x0

    iput-object v0, p0, Lcom/bytedance/sdk/component/Fj/eV;->hjc:Lcom/bytedance/sdk/component/Fj/WR;

    return-void
.end method

.method public final hjc()V
    .locals 1

    const/4 v0, 0x0

    invoke-virtual {p0, v0}, Lcom/bytedance/sdk/component/Fj/eV;->Fj(Ljava/lang/Throwable;)V

    return-void
.end method
