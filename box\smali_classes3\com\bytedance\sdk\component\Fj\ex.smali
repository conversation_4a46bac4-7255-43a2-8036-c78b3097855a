.class abstract Lcom/bytedance/sdk/component/Fj/ex;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "<P:",
        "Ljava/lang/Object;",
        "R:",
        "Ljava/lang/Object;",
        ">",
        "Ljava/lang/Object;"
    }
.end annotation


# instance fields
.field private Fj:Ljava/lang/String;


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public Fj()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Fj/ex;->Fj:Ljava/lang/String;

    return-object v0
.end method

.method public Fj(Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/Fj/ex;->Fj:Ljava/lang/String;

    return-void
.end method

.method public ex()Lcom/bytedance/sdk/component/Fj/cB;
    .locals 1

    sget-object v0, Lcom/bytedance/sdk/component/Fj/cB;->ex:Lcom/bytedance/sdk/component/Fj/cB;

    return-object v0
.end method
