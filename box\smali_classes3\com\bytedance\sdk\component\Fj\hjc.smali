.class abstract Lcom/bytedance/sdk/component/Fj/hjc;
.super Lcom/bytedance/sdk/component/Fj/ex;


# instance fields
.field Fj:Lcom/bytedance/sdk/component/Fj/cB;


# direct methods
.method public constructor <init>()V
    .locals 1

    invoke-direct {p0}, Lcom/bytedance/sdk/component/Fj/ex;-><init>()V

    invoke-super {p0}, Lcom/bytedance/sdk/component/Fj/ex;->ex()Lcom/bytedance/sdk/component/Fj/cB;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/sdk/component/Fj/hjc;->Fj:Lcom/bytedance/sdk/component/Fj/cB;

    return-void
.end method
