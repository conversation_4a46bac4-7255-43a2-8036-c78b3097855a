.class public interface abstract Lcom/bytedance/sdk/component/Fj/rAx$Fj;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bytedance/sdk/component/Fj/rAx;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "Fj"
.end annotation


# virtual methods
.method public abstract Fj(Ljava/lang/String;)Z
.end method

.method public abstract Fj(Ljava/lang/String;Ljava/lang/String;)Z
.end method
