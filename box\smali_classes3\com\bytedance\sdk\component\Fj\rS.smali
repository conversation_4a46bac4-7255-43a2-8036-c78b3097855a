.class Lcom/bytedance/sdk/component/Fj/rS;
.super Ljava/lang/Exception;


# instance fields
.field Fj:I


# direct methods
.method public constructor <init>(I)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Exception;-><init>()V

    iput p1, p0, Lcom/bytedance/sdk/component/Fj/rS;->Fj:I

    return-void
.end method

.method public constructor <init>(ILjava/lang/String;)V
    .locals 0

    invoke-direct {p0, p2}, Ljava/lang/Exception;-><init>(Ljava/lang/String;)V

    iput p1, p0, Lcom/bytedance/sdk/component/Fj/rS;->Fj:I

    return-void
.end method
