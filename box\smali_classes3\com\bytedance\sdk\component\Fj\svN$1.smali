.class Lcom/bytedance/sdk/component/Fj/svN$1;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/sdk/component/Fj/eV$Fj;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/component/Fj/svN;->Fj(Lcom/bytedance/sdk/component/Fj/JU;Lcom/bytedance/sdk/component/Fj/eV;Lcom/bytedance/sdk/component/Fj/WR;)Lcom/bytedance/sdk/component/Fj/svN$Fj;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/component/Fj/JU;

.field final synthetic ex:Lcom/bytedance/sdk/component/Fj/eV;

.field final synthetic hjc:Lcom/bytedance/sdk/component/Fj/svN;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/component/Fj/svN;Lcom/bytedance/sdk/component/Fj/JU;Lcom/bytedance/sdk/component/Fj/eV;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/Fj/svN$1;->hjc:Lcom/bytedance/sdk/component/Fj/svN;

    iput-object p2, p0, Lcom/bytedance/sdk/component/Fj/svN$1;->Fj:Lcom/bytedance/sdk/component/Fj/JU;

    iput-object p3, p0, Lcom/bytedance/sdk/component/Fj/svN$1;->ex:Lcom/bytedance/sdk/component/Fj/eV;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public Fj(Ljava/lang/Throwable;)V
    .locals 2

    iget-object v0, p0, Lcom/bytedance/sdk/component/Fj/svN$1;->hjc:Lcom/bytedance/sdk/component/Fj/svN;

    invoke-static {v0}, Lcom/bytedance/sdk/component/Fj/svN;->Fj(Lcom/bytedance/sdk/component/Fj/svN;)Lcom/bytedance/sdk/component/Fj/Fj;

    move-result-object v0

    if-nez v0, :cond_0

    return-void

    :cond_0
    iget-object v0, p0, Lcom/bytedance/sdk/component/Fj/svN$1;->hjc:Lcom/bytedance/sdk/component/Fj/svN;

    invoke-static {v0}, Lcom/bytedance/sdk/component/Fj/svN;->Fj(Lcom/bytedance/sdk/component/Fj/svN;)Lcom/bytedance/sdk/component/Fj/Fj;

    move-result-object v0

    invoke-static {p1}, Lcom/bytedance/sdk/component/Fj/nsB;->Fj(Ljava/lang/Throwable;)Ljava/lang/String;

    move-result-object p1

    iget-object v1, p0, Lcom/bytedance/sdk/component/Fj/svN$1;->Fj:Lcom/bytedance/sdk/component/Fj/JU;

    invoke-virtual {v0, p1, v1}, Lcom/bytedance/sdk/component/Fj/Fj;->ex(Ljava/lang/String;Lcom/bytedance/sdk/component/Fj/JU;)V

    iget-object p1, p0, Lcom/bytedance/sdk/component/Fj/svN$1;->hjc:Lcom/bytedance/sdk/component/Fj/svN;

    invoke-static {p1}, Lcom/bytedance/sdk/component/Fj/svN;->ex(Lcom/bytedance/sdk/component/Fj/svN;)Ljava/util/Set;

    move-result-object p1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Fj/svN$1;->ex:Lcom/bytedance/sdk/component/Fj/eV;

    invoke-interface {p1, v0}, Ljava/util/Set;->remove(Ljava/lang/Object;)Z

    return-void
.end method
