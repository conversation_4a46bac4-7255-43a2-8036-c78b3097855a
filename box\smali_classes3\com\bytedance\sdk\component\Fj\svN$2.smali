.class Lcom/bytedance/sdk/component/Fj/svN$2;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/sdk/component/Fj/vYf$Fj;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/component/Fj/svN;->Fj(Lcom/bytedance/sdk/component/Fj/JU;Lcom/bytedance/sdk/component/Fj/hjc;Lcom/bytedance/sdk/component/Fj/cB;)Lcom/bytedance/sdk/component/Fj/svN$Fj;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/component/Fj/JU;

.field final synthetic ex:Lcom/bytedance/sdk/component/Fj/svN;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/component/Fj/svN;Lcom/bytedance/sdk/component/Fj/JU;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/Fj/svN$2;->ex:Lcom/bytedance/sdk/component/Fj/svN;

    iput-object p2, p0, Lcom/bytedance/sdk/component/Fj/svN$2;->Fj:Lcom/bytedance/sdk/component/Fj/JU;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
