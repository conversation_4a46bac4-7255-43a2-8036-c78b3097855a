.class final Lcom/bytedance/sdk/component/Fj/vYf;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/bytedance/sdk/component/Fj/vYf$Fj;
    }
.end annotation


# instance fields
.field private Fj:Ljava/lang/String;

.field private eV:Z

.field private ex:Lcom/bytedance/sdk/component/Fj/cB;

.field private hjc:Lcom/bytedance/sdk/component/Fj/vYf$Fj;


# direct methods
.method public constructor <init>(Ljava/lang/String;Lcom/bytedance/sdk/component/Fj/cB;Lcom/bytedance/sdk/component/Fj/vYf$Fj;)V
    .locals 1

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, 0x1

    iput-boolean v0, p0, Lcom/bytedance/sdk/component/Fj/vYf;->eV:Z

    iput-object p1, p0, Lcom/bytedance/sdk/component/Fj/vYf;->Fj:Ljava/lang/String;

    iput-object p2, p0, Lcom/bytedance/sdk/component/Fj/vYf;->ex:Lcom/bytedance/sdk/component/Fj/cB;

    iput-object p3, p0, Lcom/bytedance/sdk/component/Fj/vYf;->hjc:Lcom/bytedance/sdk/component/Fj/vYf$Fj;

    return-void
.end method
