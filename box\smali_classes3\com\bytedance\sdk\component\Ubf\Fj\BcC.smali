.class public Lcom/bytedance/sdk/component/Ubf/Fj/BcC;
.super Ljava/lang/Object;


# static fields
.field private static volatile Ko:Lcom/bytedance/sdk/component/Ubf/Fj/Ubf/Fj;

.field private static dG:Lcom/bytedance/sdk/component/Ubf/Fj/BcC;


# instance fields
.field private volatile BcC:Z

.field private volatile Fj:Landroid/content/Context;

.field private JW:J

.field private final Tc:Ljava/util/concurrent/atomic/AtomicBoolean;

.field private volatile UYd:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/Integer;",
            "Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc;",
            ">;"
        }
    .end annotation
.end field

.field private volatile Ubf:Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

.field private volatile WR:Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

.field private volatile eV:Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

.field private volatile ex:Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

.field private volatile hjc:Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

.field private volatile mSE:Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;

.field private volatile rAx:Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc;

.field private volatile svN:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Ubf;


# direct methods
.method private constructor <init>()V
    .locals 2

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    new-instance v0, Ljava/util/concurrent/atomic/AtomicBoolean;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Ljava/util/concurrent/atomic/AtomicBoolean;-><init>(Z)V

    iput-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->Tc:Ljava/util/concurrent/atomic/AtomicBoolean;

    return-void
.end method

.method public static Ubf()Lcom/bytedance/sdk/component/Ubf/Fj/Ubf/Fj;
    .locals 2

    sget-object v0, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->Ko:Lcom/bytedance/sdk/component/Ubf/Fj/Ubf/Fj;

    if-nez v0, :cond_1

    const-class v0, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;

    monitor-enter v0

    :try_start_0
    sget-object v1, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->Ko:Lcom/bytedance/sdk/component/Ubf/Fj/Ubf/Fj;

    if-nez v1, :cond_0

    new-instance v1, Lcom/bytedance/sdk/component/Ubf/Fj/Ubf/ex;

    invoke-direct {v1}, Lcom/bytedance/sdk/component/Ubf/Fj/Ubf/ex;-><init>()V

    sput-object v1, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->Ko:Lcom/bytedance/sdk/component/Ubf/Fj/Ubf/Fj;

    goto :goto_0

    :catchall_0
    move-exception v1

    goto :goto_1

    :cond_0
    :goto_0
    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    goto :goto_2

    :goto_1
    monitor-exit v0

    throw v1

    :cond_1
    :goto_2
    sget-object v0, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->Ko:Lcom/bytedance/sdk/component/Ubf/Fj/Ubf/Fj;

    return-object v0
.end method

.method public static declared-synchronized svN()Lcom/bytedance/sdk/component/Ubf/Fj/BcC;
    .locals 2

    const-class v0, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;

    monitor-enter v0

    :try_start_0
    sget-object v1, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->dG:Lcom/bytedance/sdk/component/Ubf/Fj/BcC;

    if-nez v1, :cond_0

    new-instance v1, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;

    invoke-direct {v1}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;-><init>()V

    sput-object v1, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->dG:Lcom/bytedance/sdk/component/Ubf/Fj/BcC;

    goto :goto_0

    :catchall_0
    move-exception v1

    goto :goto_1

    :cond_0
    :goto_0
    sget-object v1, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->dG:Lcom/bytedance/sdk/component/Ubf/Fj/BcC;
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    monitor-exit v0

    return-object v1

    :goto_1
    monitor-exit v0

    throw v1
.end method


# virtual methods
.method public BcC()Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->rAx:Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc;

    return-object v0
.end method

.method public Fj(J)V
    .locals 0

    iput-wide p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->JW:J

    return-void
.end method

.method public Fj(Landroid/content/Context;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->Fj:Landroid/content/Context;

    return-void
.end method

.method public Fj(Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Ubf;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->svN:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Ubf;

    return-void
.end method

.method public Fj(Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->mSE:Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;

    return-void
.end method

.method public Fj(Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;)V
    .locals 2

    if-nez p1, :cond_0

    return-void

    :cond_0
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v0

    invoke-interface {p1, v0, v1}, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;->Fj(J)V

    sget-object v0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;->Fj:Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;

    invoke-interface {p1}, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;->eV()B

    move-result v1

    invoke-virtual {v0, p1, v1}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;->Fj(Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;I)V

    return-void
.end method

.method public Fj(Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->WR:Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

    return-void
.end method

.method public Fj(Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->rAx:Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc;

    return-void
.end method

.method public Fj(Ljava/lang/String;Ljava/util/List;ZLjava/util/Map;ILjava/lang/String;)V
    .locals 7
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;Z",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ">;I",
            "Ljava/lang/String;",
            ")V"
        }
    .end annotation

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/WR/Fj;->Fj()Lcom/bytedance/sdk/component/Ubf/Fj/WR/ex;

    move-result-object v0

    move-object v1, p1

    move-object v2, p2

    move v3, p3

    move-object v4, p4

    move v5, p5

    move-object v6, p6

    invoke-interface/range {v0 .. v6}, Lcom/bytedance/sdk/component/Ubf/Fj/WR/ex;->Fj(Ljava/lang/String;Ljava/util/List;ZLjava/util/Map;ILjava/lang/String;)V

    return-void
.end method

.method public Fj(Ljava/lang/String;Z)V
    .locals 1

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/WR/Fj;->Fj()Lcom/bytedance/sdk/component/Ubf/Fj/WR/ex;

    move-result-object v0

    invoke-interface {v0, p1, p2}, Lcom/bytedance/sdk/component/Ubf/Fj/WR/ex;->Fj(Ljava/lang/String;Z)V

    return-void
.end method

.method public Fj(Z)V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->Tc:Ljava/util/concurrent/atomic/AtomicBoolean;

    invoke-virtual {v0, p1}, Ljava/util/concurrent/atomic/AtomicBoolean;->set(Z)V

    return-void
.end method

.method public Fj()Z
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->Tc:Ljava/util/concurrent/atomic/AtomicBoolean;

    invoke-virtual {v0}, Ljava/util/concurrent/atomic/AtomicBoolean;->get()Z

    move-result v0

    return v0
.end method

.method public JU()Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->mSE:Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;

    return-object v0
.end method

.method public JW()Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->Ubf:Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

    return-object v0
.end method

.method public Ko()Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->WR:Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

    return-object v0
.end method

.method public Ql()J
    .locals 4

    iget-wide v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->JW:J

    const-wide/32 v2, 0x5265c00

    mul-long v0, v0, v2

    return-wide v0
.end method

.method public Tc()Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->eV:Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

    return-object v0
.end method

.method public UYd()Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->ex:Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

    return-object v0
.end method

.method public Ubf(Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->Ubf:Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

    return-void
.end method

.method public WR()Landroid/content/Context;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->Fj:Landroid/content/Context;

    return-object v0
.end method

.method public dG()Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->hjc:Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

    return-object v0
.end method

.method public eV()Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Ubf;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->svN:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Ubf;

    return-object v0
.end method

.method public eV(Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->eV:Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

    return-void
.end method

.method public ex(Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->ex:Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

    return-void
.end method

.method public ex(Z)V
    .locals 0

    iput-boolean p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->BcC:Z

    return-void
.end method

.method public ex()Z
    .locals 1

    iget-boolean v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->BcC:Z

    return v0
.end method

.method public hjc()Ljava/util/Map;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Map<",
            "Ljava/lang/Integer;",
            "Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc;",
            ">;"
        }
    .end annotation

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->UYd:Ljava/util/Map;

    return-object v0
.end method

.method public hjc(Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->hjc:Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

    return-void
.end method

.method public mSE()V
    .locals 1

    sget-object v0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;->Fj:Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;->ex()V

    return-void
.end method

.method public rAx()V
    .locals 1

    sget-object v0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;->Fj:Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;->hjc()V

    return-void
.end method
