.class public Lcom/bytedance/sdk/component/Ubf/Fj/Fj$Fj;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bytedance/sdk/component/Ubf/Fj/Fj;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "Fj"
.end annotation


# instance fields
.field private BcC:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Ubf;

.field private Fj:Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc;

.field private Ko:I

.field private UYd:J

.field private Ubf:Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

.field private WR:Z

.field private eV:Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

.field private ex:Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

.field private hjc:Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

.field private mSE:Z

.field private rAx:I

.field private svN:Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;


# direct methods
.method public constructor <init>()V
    .locals 1

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/16 v0, 0x1388

    iput v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj$Fj;->Ko:I

    const/16 v0, 0xa

    iput v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj$Fj;->rAx:I

    return-void
.end method


# virtual methods
.method public Fj(I)Lcom/bytedance/sdk/component/Ubf/Fj/Fj$Fj;
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj$Fj;->Ko:I

    return-object p0
.end method

.method public Fj(J)Lcom/bytedance/sdk/component/Ubf/Fj/Fj$Fj;
    .locals 0

    iput-wide p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj$Fj;->UYd:J

    return-object p0
.end method

.method public Fj(Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Ubf;)Lcom/bytedance/sdk/component/Ubf/Fj/Fj$Fj;
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj$Fj;->BcC:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Ubf;

    return-object p0
.end method

.method public Fj(Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;)Lcom/bytedance/sdk/component/Ubf/Fj/Fj$Fj;
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj$Fj;->svN:Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;

    return-object p0
.end method

.method public Fj(Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;)Lcom/bytedance/sdk/component/Ubf/Fj/Fj$Fj;
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj$Fj;->ex:Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

    return-object p0
.end method

.method public Fj(Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc;)Lcom/bytedance/sdk/component/Ubf/Fj/Fj$Fj;
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj$Fj;->Fj:Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc;

    return-object p0
.end method

.method public Fj(Z)Lcom/bytedance/sdk/component/Ubf/Fj/Fj$Fj;
    .locals 0

    iput-boolean p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj$Fj;->WR:Z

    return-object p0
.end method

.method public Fj()Lcom/bytedance/sdk/component/Ubf/Fj/Fj;
    .locals 3

    new-instance v0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj;-><init>(Lcom/bytedance/sdk/component/Ubf/Fj/Fj$1;)V

    iget-object v1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj$Fj;->Fj:Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc;

    invoke-static {v0, v1}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj;->Fj(Lcom/bytedance/sdk/component/Ubf/Fj/Fj;Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc;)Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc;

    iget-object v1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj$Fj;->ex:Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

    invoke-static {v0, v1}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj;->Fj(Lcom/bytedance/sdk/component/Ubf/Fj/Fj;Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;)Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

    iget-object v1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj$Fj;->hjc:Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

    invoke-static {v0, v1}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj;->ex(Lcom/bytedance/sdk/component/Ubf/Fj/Fj;Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;)Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

    iget-object v1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj$Fj;->eV:Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

    invoke-static {v0, v1}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj;->hjc(Lcom/bytedance/sdk/component/Ubf/Fj/Fj;Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;)Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

    iget-object v1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj$Fj;->Ubf:Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

    invoke-static {v0, v1}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj;->eV(Lcom/bytedance/sdk/component/Ubf/Fj/Fj;Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;)Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

    iget-boolean v1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj$Fj;->WR:Z

    invoke-static {v0, v1}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj;->Fj(Lcom/bytedance/sdk/component/Ubf/Fj/Fj;Z)Z

    iget-object v1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj$Fj;->svN:Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;

    invoke-static {v0, v1}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj;->Fj(Lcom/bytedance/sdk/component/Ubf/Fj/Fj;Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;)Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;

    iget-object v1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj$Fj;->BcC:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Ubf;

    invoke-static {v0, v1}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj;->Fj(Lcom/bytedance/sdk/component/Ubf/Fj/Fj;Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Ubf;)Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Ubf;

    iget-boolean v1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj$Fj;->mSE:Z

    invoke-static {v0, v1}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj;->ex(Lcom/bytedance/sdk/component/Ubf/Fj/Fj;Z)Z

    iget v1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj$Fj;->rAx:I

    invoke-static {v0, v1}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj;->Fj(Lcom/bytedance/sdk/component/Ubf/Fj/Fj;I)I

    iget v1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj$Fj;->Ko:I

    invoke-static {v0, v1}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj;->ex(Lcom/bytedance/sdk/component/Ubf/Fj/Fj;I)I

    iget-wide v1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj$Fj;->UYd:J

    invoke-static {v0, v1, v2}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj;->Fj(Lcom/bytedance/sdk/component/Ubf/Fj/Fj;J)J

    return-object v0
.end method

.method public ex(I)Lcom/bytedance/sdk/component/Ubf/Fj/Fj$Fj;
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj$Fj;->rAx:I

    return-object p0
.end method

.method public ex(Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;)Lcom/bytedance/sdk/component/Ubf/Fj/Fj$Fj;
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj$Fj;->hjc:Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

    return-object p0
.end method

.method public hjc(Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;)Lcom/bytedance/sdk/component/Ubf/Fj/Fj$Fj;
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj$Fj;->eV:Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

    return-object p0
.end method
