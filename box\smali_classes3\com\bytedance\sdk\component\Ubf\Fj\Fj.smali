.class public Lcom/bytedance/sdk/component/Ubf/Fj/Fj;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/bytedance/sdk/component/Ubf/Fj/Fj$Fj;
    }
.end annotation


# instance fields
.field private BcC:Z

.field private Fj:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Ubf;

.field private Ko:Z

.field private UYd:I

.field private Ubf:Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

.field private WR:Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

.field private dG:J

.field private eV:Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

.field private ex:Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc;

.field private hjc:Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

.field private mSE:Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;

.field private rAx:I

.field private svN:Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;


# direct methods
.method private constructor <init>()V
    .locals 1

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/16 v0, 0xc8

    iput v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj;->rAx:I

    const/16 v0, 0xa

    iput v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj;->UYd:I

    return-void
.end method

.method public synthetic constructor <init>(Lcom/bytedance/sdk/component/Ubf/Fj/Fj$1;)V
    .locals 0

    invoke-direct {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj;-><init>()V

    return-void
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/component/Ubf/Fj/Fj;I)I
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj;->UYd:I

    return p1
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/component/Ubf/Fj/Fj;J)J
    .locals 0

    iput-wide p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj;->dG:J

    return-wide p1
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/component/Ubf/Fj/Fj;Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Ubf;)Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Ubf;
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj;->Fj:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Ubf;

    return-object p1
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/component/Ubf/Fj/Fj;Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;)Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj;->mSE:Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;

    return-object p1
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/component/Ubf/Fj/Fj;Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;)Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj;->hjc:Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

    return-object p1
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/component/Ubf/Fj/Fj;Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc;)Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc;
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj;->ex:Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc;

    return-object p1
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/component/Ubf/Fj/Fj;Z)Z
    .locals 0

    iput-boolean p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj;->BcC:Z

    return p1
.end method

.method public static synthetic eV(Lcom/bytedance/sdk/component/Ubf/Fj/Fj;Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;)Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj;->WR:Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

    return-object p1
.end method

.method public static synthetic ex(Lcom/bytedance/sdk/component/Ubf/Fj/Fj;I)I
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj;->rAx:I

    return p1
.end method

.method public static synthetic ex(Lcom/bytedance/sdk/component/Ubf/Fj/Fj;Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;)Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj;->eV:Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

    return-object p1
.end method

.method public static synthetic ex(Lcom/bytedance/sdk/component/Ubf/Fj/Fj;Z)Z
    .locals 0

    iput-boolean p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj;->Ko:Z

    return p1
.end method

.method public static synthetic hjc(Lcom/bytedance/sdk/component/Ubf/Fj/Fj;Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;)Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj;->Ubf:Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

    return-object p1
.end method


# virtual methods
.method public BcC()Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj;->eV:Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

    return-object v0
.end method

.method public Fj()Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Ubf;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj;->Fj:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Ubf;

    return-object v0
.end method

.method public Ko()Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj;->ex:Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc;

    return-object v0
.end method

.method public UYd()I
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj;->rAx:I

    return v0
.end method

.method public Ubf()J
    .locals 2

    iget-wide v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj;->dG:J

    return-wide v0
.end method

.method public WR()Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj;->svN:Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

    return-object v0
.end method

.method public dG()I
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj;->UYd:I

    return v0
.end method

.method public eV()Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj;->mSE:Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;

    return-object v0
.end method

.method public ex()Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj;->WR:Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

    return-object v0
.end method

.method public hjc()Z
    .locals 1

    iget-boolean v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj;->Ko:Z

    return v0
.end method

.method public mSE()Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj;->Ubf:Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

    return-object v0
.end method

.method public rAx()Z
    .locals 1

    iget-boolean v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj;->BcC:Z

    return v0
.end method

.method public svN()Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj;->hjc:Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

    return-object v0
.end method
