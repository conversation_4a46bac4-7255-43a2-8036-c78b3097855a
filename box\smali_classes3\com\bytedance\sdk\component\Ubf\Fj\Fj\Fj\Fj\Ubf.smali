.class public Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/Ubf;
.super Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/svN;


# direct methods
.method public constructor <init>(Landroid/content/Context;Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;)V
    .locals 0

    invoke-direct {p0, p1, p2}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/svN;-><init>(Landroid/content/Context;Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;)V

    return-void
.end method


# virtual methods
.method public Fj()B
    .locals 1

    const/4 v0, 0x2

    return v0
.end method

.method public ex()Ljava/lang/String;
    .locals 1

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->svN()Lcom/bytedance/sdk/component/Ubf/Fj/BcC;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->eV()Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Ubf;

    move-result-object v0

    invoke-interface {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Ubf;->WR()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public hjc()B
    .locals 1

    const/4 v0, 0x3

    return v0
.end method
