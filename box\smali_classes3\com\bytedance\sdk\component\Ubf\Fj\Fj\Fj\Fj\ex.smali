.class public Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/ex;
.super Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/Fj;


# direct methods
.method public constructor <init>(Landroid/content/Context;Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;)V
    .locals 0

    invoke-direct {p0, p1, p2}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/Fj;-><init>(Landroid/content/Context;Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;)V

    return-void
.end method


# virtual methods
.method public eV()B
    .locals 1

    const/4 v0, 0x3

    return v0
.end method

.method public ex()Ljava/lang/String;
    .locals 1

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->svN()Lcom/bytedance/sdk/component/Ubf/Fj/BcC;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->eV()Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Ubf;

    move-result-object v0

    if-eqz v0, :cond_0

    invoke-interface {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Ubf;->hjc()Ljava/lang/String;

    move-result-object v0

    return-object v0

    :cond_0
    const/4 v0, 0x0

    return-object v0
.end method

.method public hjc()B
    .locals 1

    const/4 v0, 0x2

    return v0
.end method
