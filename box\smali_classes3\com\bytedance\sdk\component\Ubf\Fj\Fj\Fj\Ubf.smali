.class public Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Ubf;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Ubf;


# static fields
.field public static final Fj:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Ubf;


# instance fields
.field private volatile ex:Landroid/database/sqlite/SQLiteDatabase;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    new-instance v0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Ubf;

    invoke-direct {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Ubf;-><init>()V

    sput-object v0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Ubf;->Fj:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Ubf;

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public Fj(Landroid/content/Context;)Landroid/database/sqlite/SQLiteDatabase;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Ubf;->ex:Landroid/database/sqlite/SQLiteDatabase;

    if-nez v0, :cond_1

    monitor-enter p0

    :try_start_0
    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Ubf;->ex:Landroid/database/sqlite/SQLiteDatabase;

    if-nez v0, :cond_0

    new-instance v0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/eV;

    invoke-direct {v0, p1}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/eV;-><init>(Landroid/content/Context;)V

    invoke-virtual {v0}, Landroid/database/sqlite/SQLiteOpenHelper;->getWritableDatabase()Landroid/database/sqlite/SQLiteDatabase;

    move-result-object p1

    iput-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Ubf;->ex:Landroid/database/sqlite/SQLiteDatabase;

    goto :goto_0

    :catchall_0
    move-exception p1

    goto :goto_1

    :cond_0
    :goto_0
    monitor-exit p0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    goto :goto_2

    :goto_1
    monitor-exit p0

    throw p1

    :cond_1
    :goto_2
    iget-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Ubf;->ex:Landroid/database/sqlite/SQLiteDatabase;

    return-object p1
.end method

.method public Fj()Ljava/lang/String;
    .locals 1

    const-string v0, "loghighpriority"

    return-object v0
.end method

.method public Ubf()Ljava/lang/String;
    .locals 1

    const-string v0, "logstatsbatch"

    return-object v0
.end method

.method public WR()Ljava/lang/String;
    .locals 1

    const/4 v0, 0x0

    return-object v0
.end method

.method public eV()Ljava/lang/String;
    .locals 1

    const-string v0, "logstats"

    return-object v0
.end method

.method public ex()Ljava/lang/String;
    .locals 1

    const-string v0, "adevent"

    return-object v0
.end method

.method public hjc()Ljava/lang/String;
    .locals 1

    const/4 v0, 0x0

    return-object v0
.end method
