.class Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/ex;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/ex$Fj;,
        Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/ex$ex;
    }
.end annotation


# instance fields
.field private Fj:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/ex$ex;

.field private ex:Landroid/content/Context;


# direct methods
.method public constructor <init>(Landroid/content/Context;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    :try_start_0
    invoke-virtual {p1}, Landroid/content/Context;->getApplicationContext()Landroid/content/Context;

    move-result-object p1

    iput-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/ex;->ex:Landroid/content/Context;

    iget-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/ex;->Fj:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/ex$ex;

    if-nez p1, :cond_0

    new-instance p1, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/ex$ex;

    invoke-direct {p1, p0}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/ex$ex;-><init>(Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/ex;)V

    iput-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/ex;->Fj:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/ex$ex;
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    :catchall_0
    :cond_0
    return-void
.end method


# virtual methods
.method public Fj()Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/ex$ex;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/ex;->Fj:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/ex$ex;

    return-object v0
.end method
