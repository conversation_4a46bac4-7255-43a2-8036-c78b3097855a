.class public interface abstract Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Ubf;
.super Ljava/lang/Object;


# virtual methods
.method public abstract Fj(Landroid/content/Context;)Landroid/database/sqlite/SQLiteDatabase;
.end method

.method public abstract Fj()Ljava/lang/String;
.end method

.method public abstract Ubf()Ljava/lang/String;
.end method

.method public abstract WR()Ljava/lang/String;
.end method

.method public abstract eV()Ljava/lang/String;
.end method

.method public abstract ex()Ljava/lang/String;
.end method

.method public abstract hjc()Ljava/lang/String;
.end method
