.class public Lcom/bytedance/sdk/component/Ubf/Fj/Fj/WR;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/sdk/component/Ubf/Fj/Fj/eV;


# instance fields
.field private BcC:Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

.field Fj:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Ubf;

.field private Ko:Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

.field private Tc:Ljava/util/Queue;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Queue<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field private UYd:Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

.field private Ubf:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/ex/svN;

.field private WR:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/ex/hjc;

.field private dG:Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

.field private eV:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/ex/ex;

.field private ex:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/ex/Ubf;

.field private hjc:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/ex/Fj;

.field private mSE:Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

.field private rAx:Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

.field private svN:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/ex/WR;


# direct methods
.method public constructor <init>(Ljava/util/Queue;)V
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Queue<",
            "Ljava/lang/String;",
            ">;)V"
        }
    .end annotation

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/WR;->Tc:Ljava/util/Queue;

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->svN()Lcom/bytedance/sdk/component/Ubf/Fj/BcC;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->eV()Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Ubf;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/WR;->Fj:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Ubf;

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj;->Fj()Z

    move-result v0

    if-eqz v0, :cond_0

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->svN()Lcom/bytedance/sdk/component/Ubf/Fj/BcC;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->UYd()Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/WR;->BcC:Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

    new-instance v1, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/ex/Ubf;

    invoke-direct {v1, v0, p1}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/ex/Ubf;-><init>(Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;Ljava/util/Queue;)V

    iput-object v1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/WR;->ex:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/ex/Ubf;

    :cond_0
    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj;->Ubf()Z

    move-result v0

    if-eqz v0, :cond_2

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->svN()Lcom/bytedance/sdk/component/Ubf/Fj/BcC;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->Ko()Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

    move-result-object v0

    if-eqz v0, :cond_1

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->svN()Lcom/bytedance/sdk/component/Ubf/Fj/BcC;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->Ko()Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/WR;->Ko:Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

    goto :goto_0

    :cond_1
    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->svN()Lcom/bytedance/sdk/component/Ubf/Fj/BcC;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->dG()Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/WR;->Ko:Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

    :goto_0
    new-instance v0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/ex/ex;

    iget-object v1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/WR;->Ko:Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

    invoke-direct {v0, v1, p1}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/ex/ex;-><init>(Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;Ljava/util/Queue;)V

    iput-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/WR;->eV:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/ex/ex;

    :cond_2
    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj;->ex()Z

    move-result v0

    if-eqz v0, :cond_3

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->svN()Lcom/bytedance/sdk/component/Ubf/Fj/BcC;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->dG()Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/WR;->mSE:Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

    new-instance v1, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/ex/Fj;

    invoke-direct {v1, v0, p1}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/ex/Fj;-><init>(Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;Ljava/util/Queue;)V

    iput-object v1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/WR;->hjc:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/ex/Fj;

    :cond_3
    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj;->hjc()Z

    move-result v0

    if-eqz v0, :cond_4

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->svN()Lcom/bytedance/sdk/component/Ubf/Fj/BcC;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->dG()Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/WR;->rAx:Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

    new-instance v1, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/ex/svN;

    invoke-direct {v1, v0, p1}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/ex/svN;-><init>(Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;Ljava/util/Queue;)V

    iput-object v1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/WR;->Ubf:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/ex/svN;

    :cond_4
    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj;->eV()Z

    move-result v0

    if-eqz v0, :cond_5

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->svN()Lcom/bytedance/sdk/component/Ubf/Fj/BcC;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->Tc()Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/WR;->UYd:Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

    new-instance v1, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/ex/hjc;

    invoke-direct {v1, v0, p1}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/ex/hjc;-><init>(Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;Ljava/util/Queue;)V

    iput-object v1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/WR;->WR:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/ex/hjc;

    :cond_5
    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj;->WR()Z

    move-result v0

    if-eqz v0, :cond_6

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->svN()Lcom/bytedance/sdk/component/Ubf/Fj/BcC;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->JW()Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/WR;->dG:Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

    new-instance v1, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/ex/WR;

    invoke-direct {v1, v0, p1}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/ex/WR;-><init>(Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;Ljava/util/Queue;)V

    iput-object v1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/WR;->svN:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/ex/WR;

    :cond_6
    return-void
.end method


# virtual methods
.method public Fj(IILjava/util/List;)Ljava/util/List;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(II",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;)",
            "Ljava/util/List<",
            "Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;",
            ">;"
        }
    .end annotation

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj;->Fj()Z

    move-result p3

    const/4 v0, 0x1

    if-eqz p3, :cond_0

    iget-object p3, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/WR;->ex:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/ex/Ubf;

    invoke-virtual {p3, p1, p2}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/ex/eV;->ex(II)Z

    move-result p3

    if-eqz p3, :cond_0

    iget-object p3, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/WR;->ex:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/ex/Ubf;

    invoke-virtual {p3, p1, p2}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/ex/eV;->Fj(II)Ljava/util/List;

    move-result-object p3

    if-eqz p3, :cond_0

    invoke-interface {p3}, Ljava/util/List;->size()I

    move-result v1

    if-eqz v1, :cond_0

    sget-object p1, Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;->eV:Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->rS()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object p1

    invoke-static {p1, v0}, Lcom/bytedance/sdk/component/Ubf/Fj/hjc/ex;->Fj(Ljava/util/concurrent/atomic/AtomicLong;I)V

    return-object p3

    :cond_0
    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj;->Ubf()Z

    move-result p3

    if-eqz p3, :cond_1

    iget-object p3, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/WR;->eV:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/ex/ex;

    invoke-virtual {p3, p1, p2}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/ex/eV;->ex(II)Z

    move-result p3

    if-eqz p3, :cond_1

    iget-object p3, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/WR;->eV:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/ex/ex;

    invoke-virtual {p3, p1, p2}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/ex/eV;->Fj(II)Ljava/util/List;

    move-result-object p3

    if-eqz p3, :cond_1

    invoke-interface {p3}, Ljava/util/List;->size()I

    move-result v1

    if-eqz v1, :cond_1

    return-object p3

    :cond_1
    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj;->ex()Z

    move-result p3

    if-eqz p3, :cond_2

    iget-object p3, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/WR;->hjc:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/ex/Fj;

    invoke-virtual {p3, p1, p2}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/ex/eV;->ex(II)Z

    move-result p3

    if-eqz p3, :cond_2

    iget-object p3, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/WR;->hjc:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/ex/Fj;

    invoke-virtual {p3, p1, p2}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/ex/eV;->Fj(II)Ljava/util/List;

    move-result-object p3

    if-eqz p3, :cond_2

    invoke-interface {p3}, Ljava/util/List;->size()I

    move-result v1

    if-eqz v1, :cond_2

    sget-object p1, Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;->eV:Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->vYf()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object p1

    invoke-static {p1, v0}, Lcom/bytedance/sdk/component/Ubf/Fj/hjc/ex;->Fj(Ljava/util/concurrent/atomic/AtomicLong;I)V

    return-object p3

    :cond_2
    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj;->hjc()Z

    move-result p3

    if-eqz p3, :cond_3

    iget-object p3, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/WR;->Ubf:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/ex/svN;

    invoke-virtual {p3, p1, p2}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/ex/eV;->ex(II)Z

    move-result p3

    if-eqz p3, :cond_3

    iget-object p3, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/WR;->Ubf:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/ex/svN;

    invoke-virtual {p3, p1, p2}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/ex/eV;->Fj(II)Ljava/util/List;

    move-result-object p3

    if-eqz p3, :cond_3

    invoke-interface {p3}, Ljava/util/List;->size()I

    move-result v1

    if-eqz v1, :cond_3

    sget-object p1, Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;->eV:Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->mE()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object p1

    invoke-static {p1, v0}, Lcom/bytedance/sdk/component/Ubf/Fj/hjc/ex;->Fj(Ljava/util/concurrent/atomic/AtomicLong;I)V

    return-object p3

    :cond_3
    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj;->eV()Z

    move-result p3

    if-eqz p3, :cond_4

    iget-object p3, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/WR;->WR:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/ex/hjc;

    invoke-virtual {p3, p1, p2}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/ex/eV;->ex(II)Z

    move-result p3

    if-eqz p3, :cond_4

    iget-object p3, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/WR;->WR:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/ex/hjc;

    invoke-virtual {p3, p1, p2}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/ex/eV;->Fj(II)Ljava/util/List;

    move-result-object p3

    if-eqz p3, :cond_4

    invoke-interface {p3}, Ljava/util/List;->size()I

    move-result v1

    if-eqz v1, :cond_4

    sget-object p1, Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;->eV:Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->Af()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object p1

    invoke-static {p1, v0}, Lcom/bytedance/sdk/component/Ubf/Fj/hjc/ex;->Fj(Ljava/util/concurrent/atomic/AtomicLong;I)V

    return-object p3

    :cond_4
    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj;->WR()Z

    move-result p3

    if-eqz p3, :cond_5

    iget-object p3, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/WR;->svN:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/ex/WR;

    invoke-virtual {p3, p1, p2}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/ex/eV;->ex(II)Z

    move-result p3

    if-eqz p3, :cond_5

    iget-object p3, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/WR;->svN:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/ex/WR;

    invoke-virtual {p3, p1, p2}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/ex/eV;->Fj(II)Ljava/util/List;

    move-result-object p1

    if-eqz p1, :cond_5

    invoke-interface {p1}, Ljava/util/List;->size()I

    move-result p2

    if-eqz p2, :cond_5

    return-object p1

    :cond_5
    const/4 p1, 0x0

    return-object p1
.end method

.method public Fj(IJ)V
    .locals 0

    return-void
.end method

.method public Fj(ILjava/util/List;)V
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I",
            "Ljava/util/List<",
            "Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;",
            ">;)V"
        }
    .end annotation

    if-eqz p2, :cond_5

    invoke-interface {p2}, Ljava/util/List;->size()I

    move-result v0

    if-eqz v0, :cond_5

    const/4 v0, 0x0

    invoke-interface {p2, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v1

    if-eqz v1, :cond_5

    invoke-interface {p2, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;

    invoke-interface {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;->Ubf()B

    move-result v1

    invoke-interface {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;->eV()B

    move-result v0

    const/4 v2, 0x1

    if-nez v0, :cond_0

    if-ne v1, v2, :cond_0

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj;->Fj()Z

    move-result v3

    if-eqz v3, :cond_0

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/WR;->ex:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/ex/Ubf;

    invoke-virtual {v0, p1, p2}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/ex/eV;->Fj(ILjava/util/List;)V

    return-void

    :cond_0
    const/4 v3, 0x3

    const/4 v4, 0x2

    if-ne v0, v3, :cond_1

    if-ne v1, v4, :cond_1

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj;->Ubf()Z

    move-result v5

    if-eqz v5, :cond_1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/WR;->eV:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/ex/ex;

    invoke-virtual {v0, p1, p2}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/ex/eV;->Fj(ILjava/util/List;)V

    return-void

    :cond_1
    if-nez v0, :cond_2

    if-ne v1, v4, :cond_2

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj;->ex()Z

    move-result v5

    if-eqz v5, :cond_2

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/WR;->hjc:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/ex/Fj;

    invoke-virtual {v0, p1, p2}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/ex/eV;->Fj(ILjava/util/List;)V

    return-void

    :cond_2
    if-ne v0, v2, :cond_3

    if-ne v1, v4, :cond_3

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj;->hjc()Z

    move-result v5

    if-eqz v5, :cond_3

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/WR;->Ubf:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/ex/svN;

    invoke-virtual {v0, p1, p2}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/ex/eV;->Fj(ILjava/util/List;)V

    return-void

    :cond_3
    if-ne v0, v2, :cond_4

    if-ne v1, v3, :cond_4

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj;->eV()Z

    move-result v2

    if-eqz v2, :cond_4

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/WR;->WR:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/ex/hjc;

    invoke-virtual {v0, p1, p2}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/ex/eV;->Fj(ILjava/util/List;)V

    return-void

    :cond_4
    if-ne v0, v4, :cond_5

    if-ne v1, v3, :cond_5

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj;->WR()Z

    move-result v0

    if-eqz v0, :cond_5

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/WR;->svN:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/ex/WR;

    invoke-virtual {v0, p1, p2}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/ex/eV;->Fj(ILjava/util/List;)V

    :cond_5
    return-void
.end method

.method public Fj(Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;I)V
    .locals 5

    :try_start_0
    invoke-interface {p1}, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;->eV()B

    move-result p2

    invoke-interface {p1}, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;->Ubf()B

    move-result v0

    const/4 v1, 0x1

    if-nez p2, :cond_0

    if-ne v0, v1, :cond_0

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj;->Fj()Z

    move-result v2

    if-eqz v2, :cond_0

    iget-object p2, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/WR;->ex:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/ex/Ubf;

    invoke-virtual {p2, p1}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/ex/eV;->Fj(Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;)V

    return-void

    :cond_0
    const/4 v2, 0x3

    const/4 v3, 0x2

    if-ne p2, v2, :cond_1

    if-ne v0, v3, :cond_1

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj;->Ubf()Z

    move-result v4

    if-eqz v4, :cond_1

    iget-object p2, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/WR;->eV:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/ex/ex;

    invoke-virtual {p2, p1}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/ex/eV;->Fj(Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;)V

    return-void

    :cond_1
    if-nez p2, :cond_2

    if-ne v0, v3, :cond_2

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj;->ex()Z

    move-result v4

    if-eqz v4, :cond_2

    iget-object p2, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/WR;->hjc:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/ex/Fj;

    invoke-virtual {p2, p1}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/ex/eV;->Fj(Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;)V

    return-void

    :cond_2
    if-ne p2, v1, :cond_3

    if-ne v0, v3, :cond_3

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj;->hjc()Z

    move-result v4

    if-eqz v4, :cond_3

    iget-object p2, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/WR;->Ubf:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/ex/svN;

    invoke-virtual {p2, p1}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/ex/eV;->Fj(Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;)V

    return-void

    :cond_3
    if-ne p2, v1, :cond_4

    if-ne v0, v2, :cond_4

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj;->eV()Z

    move-result v1

    if-eqz v1, :cond_4

    iget-object p2, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/WR;->WR:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/ex/hjc;

    invoke-virtual {p2, p1}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/ex/eV;->Fj(Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;)V

    return-void

    :cond_4
    if-ne p2, v3, :cond_5

    if-ne v0, v2, :cond_5

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj;->WR()Z

    move-result p2

    if-eqz p2, :cond_5

    iget-object p2, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/WR;->svN:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/ex/WR;

    invoke-virtual {p2, p1}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/ex/eV;->Fj(Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    :catchall_0
    :cond_5
    return-void
.end method

.method public Fj(IZ)Z
    .locals 1

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj;->Fj()Z

    move-result p2

    if-eqz p2, :cond_0

    iget-object p2, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/WR;->ex:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/ex/Ubf;

    if-eqz p2, :cond_0

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/WR;->BcC:Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;->Fj()I

    move-result v0

    invoke-virtual {p2, p1, v0}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/ex/eV;->ex(II)Z

    move-result p2

    if-nez p2, :cond_5

    :cond_0
    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj;->Ubf()Z

    move-result p2

    if-eqz p2, :cond_1

    iget-object p2, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/WR;->eV:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/ex/ex;

    if-eqz p2, :cond_1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/WR;->Ko:Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

    if-eqz v0, :cond_1

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;->Fj()I

    move-result v0

    invoke-virtual {p2, p1, v0}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/ex/eV;->ex(II)Z

    move-result p2

    if-nez p2, :cond_5

    :cond_1
    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj;->ex()Z

    move-result p2

    if-eqz p2, :cond_2

    iget-object p2, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/WR;->hjc:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/ex/Fj;

    if-eqz p2, :cond_2

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/WR;->mSE:Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

    if-eqz v0, :cond_2

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;->Fj()I

    move-result v0

    invoke-virtual {p2, p1, v0}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/ex/eV;->ex(II)Z

    move-result p2

    if-nez p2, :cond_5

    :cond_2
    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj;->hjc()Z

    move-result p2

    if-eqz p2, :cond_3

    iget-object p2, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/WR;->Ubf:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/ex/svN;

    if-eqz p2, :cond_3

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/WR;->rAx:Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

    if-eqz v0, :cond_3

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;->Fj()I

    move-result v0

    invoke-virtual {p2, p1, v0}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/ex/eV;->ex(II)Z

    move-result p2

    if-nez p2, :cond_5

    :cond_3
    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj;->eV()Z

    move-result p2

    if-eqz p2, :cond_4

    iget-object p2, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/WR;->WR:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/ex/hjc;

    if-eqz p2, :cond_4

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/WR;->UYd:Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

    if-eqz v0, :cond_4

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;->Fj()I

    move-result v0

    invoke-virtual {p2, p1, v0}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/ex/eV;->ex(II)Z

    move-result p2

    if-nez p2, :cond_5

    :cond_4
    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj;->WR()Z

    move-result p2

    if-eqz p2, :cond_6

    iget-object p2, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/WR;->svN:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/ex/WR;

    if-eqz p2, :cond_6

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/WR;->dG:Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

    if-eqz v0, :cond_6

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;->Fj()I

    move-result v0

    invoke-virtual {p2, p1, v0}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/ex/eV;->ex(II)Z

    move-result p1

    if-eqz p1, :cond_6

    :cond_5
    const/4 p1, 0x1

    return p1

    :cond_6
    const/4 p1, 0x0

    return p1
.end method
