.class public interface abstract Lcom/bytedance/sdk/component/Ubf/Fj/Fj/eV;
.super Ljava/lang/Object;


# virtual methods
.method public abstract Fj(IILjava/util/List;)Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(II",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;)",
            "Ljava/util/List<",
            "Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;",
            ">;"
        }
    .end annotation
.end method

.method public abstract Fj(IJ)V
.end method

.method public abstract Fj(ILjava/util/List;)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I",
            "Ljava/util/List<",
            "Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;",
            ">;)V"
        }
    .end annotation
.end method

.method public abstract Fj(Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;I)V
.end method

.method public abstract Fj(IZ)Z
.end method
