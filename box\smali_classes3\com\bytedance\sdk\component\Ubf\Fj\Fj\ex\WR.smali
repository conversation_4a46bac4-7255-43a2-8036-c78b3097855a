.class public Lcom/bytedance/sdk/component/Ubf/Fj/Fj/ex/WR;
.super Lcom/bytedance/sdk/component/Ubf/Fj/Fj/ex/eV;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lcom/bytedance/sdk/component/Ubf/Fj/Fj/ex/eV<",
        "Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;",
        ">;"
    }
.end annotation


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;Ljava/util/Queue;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;",
            "Ljava/util/Queue<",
            "Ljava/lang/String;",
            ">;)V"
        }
    .end annotation

    const-string v0, "Other"

    invoke-direct {p0, p1, p2, v0}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/ex/eV;-><init>(Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;Ljava/util/Queue;Ljava/lang/String;)V

    return-void
.end method
