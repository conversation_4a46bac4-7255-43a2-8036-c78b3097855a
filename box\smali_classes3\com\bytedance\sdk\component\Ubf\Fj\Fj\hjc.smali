.class public Lcom/bytedance/sdk/component/Ubf/Fj/Fj/hjc;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/sdk/component/Ubf/Fj/Fj/eV;


# instance fields
.field private BcC:Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

.field private Fj:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/eV;

.field private Ko:Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

.field private UYd:Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

.field private Ubf:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/WR;

.field private WR:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/Ubf;

.field private eV:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/svN;

.field private ex:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/Fj;

.field private hjc:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/ex;

.field private mSE:Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

.field private rAx:Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

.field private svN:Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;


# direct methods
.method public constructor <init>()V
    .locals 3

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->svN()Lcom/bytedance/sdk/component/Ubf/Fj/BcC;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->WR()Landroid/content/Context;

    move-result-object v0

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj;->Fj()Z

    move-result v1

    if-eqz v1, :cond_0

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->svN()Lcom/bytedance/sdk/component/Ubf/Fj/BcC;

    move-result-object v1

    invoke-virtual {v1}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->UYd()Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

    move-result-object v1

    iput-object v1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/hjc;->svN:Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

    new-instance v2, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/eV;

    invoke-direct {v2, v0, v1}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/eV;-><init>(Landroid/content/Context;Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;)V

    iput-object v2, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/hjc;->Fj:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/eV;

    :cond_0
    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj;->Ubf()Z

    move-result v1

    if-eqz v1, :cond_2

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->svN()Lcom/bytedance/sdk/component/Ubf/Fj/BcC;

    move-result-object v1

    invoke-virtual {v1}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->Ko()Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

    move-result-object v1

    if-eqz v1, :cond_1

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->svN()Lcom/bytedance/sdk/component/Ubf/Fj/BcC;

    move-result-object v1

    invoke-virtual {v1}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->Ko()Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

    move-result-object v1

    iput-object v1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/hjc;->mSE:Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

    goto :goto_0

    :cond_1
    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->svN()Lcom/bytedance/sdk/component/Ubf/Fj/BcC;

    move-result-object v1

    invoke-virtual {v1}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->dG()Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

    move-result-object v1

    iput-object v1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/hjc;->mSE:Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

    :goto_0
    new-instance v1, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/ex;

    iget-object v2, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/hjc;->mSE:Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

    invoke-direct {v1, v0, v2}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/ex;-><init>(Landroid/content/Context;Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;)V

    iput-object v1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/hjc;->hjc:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/ex;

    :cond_2
    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj;->ex()Z

    move-result v1

    if-eqz v1, :cond_3

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->svN()Lcom/bytedance/sdk/component/Ubf/Fj/BcC;

    move-result-object v1

    invoke-virtual {v1}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->dG()Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

    move-result-object v1

    iput-object v1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/hjc;->BcC:Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

    new-instance v2, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/Fj;

    invoke-direct {v2, v0, v1}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/Fj;-><init>(Landroid/content/Context;Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;)V

    iput-object v2, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/hjc;->ex:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/Fj;

    :cond_3
    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj;->hjc()Z

    move-result v1

    if-eqz v1, :cond_4

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->svN()Lcom/bytedance/sdk/component/Ubf/Fj/BcC;

    move-result-object v1

    invoke-virtual {v1}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->dG()Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

    move-result-object v1

    iput-object v1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/hjc;->Ko:Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

    new-instance v2, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/svN;

    invoke-direct {v2, v0, v1}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/svN;-><init>(Landroid/content/Context;Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;)V

    iput-object v2, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/hjc;->eV:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/svN;

    :cond_4
    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj;->eV()Z

    move-result v1

    if-eqz v1, :cond_5

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->svN()Lcom/bytedance/sdk/component/Ubf/Fj/BcC;

    move-result-object v1

    invoke-virtual {v1}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->Tc()Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

    move-result-object v1

    iput-object v1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/hjc;->rAx:Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

    new-instance v2, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/WR;

    invoke-direct {v2, v0, v1}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/WR;-><init>(Landroid/content/Context;Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;)V

    iput-object v2, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/hjc;->Ubf:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/WR;

    :cond_5
    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj;->WR()Z

    move-result v1

    if-eqz v1, :cond_6

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->svN()Lcom/bytedance/sdk/component/Ubf/Fj/BcC;

    move-result-object v1

    invoke-virtual {v1}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->JW()Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

    move-result-object v1

    iput-object v1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/hjc;->UYd:Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

    new-instance v2, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/Ubf;

    invoke-direct {v2, v0, v1}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/Ubf;-><init>(Landroid/content/Context;Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;)V

    iput-object v2, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/hjc;->WR:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/Ubf;

    :cond_6
    return-void
.end method

.method private Fj(Ljava/util/List;Ljava/util/List;)Z
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;",
            ">;",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;)Z"
        }
    .end annotation

    if-eqz p1, :cond_1

    invoke-interface {p1}, Ljava/util/List;->isEmpty()Z

    move-result v0

    if-nez v0, :cond_1

    if-eqz p2, :cond_1

    invoke-interface {p2}, Ljava/util/List;->isEmpty()Z

    move-result v0

    if-nez v0, :cond_1

    :try_start_0
    invoke-interface {p1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :cond_0
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_1

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;

    if-eqz v1, :cond_0

    invoke-interface {v1}, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;->hjc()Ljava/lang/String;

    move-result-object v1

    invoke-static {v1}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v2

    if-nez v2, :cond_0

    invoke-interface {p2, v1}, Ljava/util/List;->contains(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_0

    invoke-interface {v0}, Ljava/util/Iterator;->remove()V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    goto :goto_0

    :catchall_0
    move-exception p2

    invoke-virtual {p2}, Ljava/lang/Throwable;->getMessage()Ljava/lang/String;

    :cond_1
    if-eqz p1, :cond_2

    invoke-interface {p1}, Ljava/util/List;->isEmpty()Z

    move-result p1

    if-nez p1, :cond_2

    const/4 p1, 0x1

    return p1

    :cond_2
    const/4 p1, 0x0

    return p1
.end method


# virtual methods
.method public Fj(IILjava/util/List;)Ljava/util/List;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(II",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;)",
            "Ljava/util/List<",
            "Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;",
            ">;"
        }
    .end annotation

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj;->Fj()Z

    move-result p1

    const/4 p2, 0x1

    const-string v0, "_id"

    if-eqz p1, :cond_0

    iget-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/hjc;->Fj:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/eV;

    invoke-virtual {p1, v0}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/Fj;->Fj(Ljava/lang/String;)Ljava/util/List;

    move-result-object p1

    invoke-direct {p0, p1, p3}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/hjc;->Fj(Ljava/util/List;Ljava/util/List;)Z

    move-result v1

    if-eqz v1, :cond_0

    invoke-interface {p1}, Ljava/util/List;->size()I

    sget-object p3, Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;->eV:Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;

    invoke-virtual {p3}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->cB()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object p3

    invoke-static {p3, p2}, Lcom/bytedance/sdk/component/Ubf/Fj/hjc/ex;->Fj(Ljava/util/concurrent/atomic/AtomicLong;I)V

    return-object p1

    :cond_0
    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj;->Ubf()Z

    move-result p1

    if-eqz p1, :cond_1

    iget-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/hjc;->hjc:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/ex;

    invoke-virtual {p1, v0}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/Fj;->Fj(Ljava/lang/String;)Ljava/util/List;

    move-result-object p1

    invoke-direct {p0, p1, p3}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/hjc;->Fj(Ljava/util/List;Ljava/util/List;)Z

    move-result v1

    if-eqz v1, :cond_1

    invoke-interface {p1}, Ljava/util/List;->size()I

    return-object p1

    :cond_1
    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj;->ex()Z

    move-result p1

    if-eqz p1, :cond_2

    iget-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/hjc;->ex:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/Fj;

    invoke-virtual {p1, v0}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/Fj;->Fj(Ljava/lang/String;)Ljava/util/List;

    move-result-object p1

    invoke-direct {p0, p1, p3}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/hjc;->Fj(Ljava/util/List;Ljava/util/List;)Z

    move-result v1

    if-eqz v1, :cond_2

    invoke-interface {p1}, Ljava/util/List;->size()I

    sget-object p3, Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;->eV:Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;

    invoke-virtual {p3}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->nsB()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object p3

    invoke-static {p3, p2}, Lcom/bytedance/sdk/component/Ubf/Fj/hjc/ex;->Fj(Ljava/util/concurrent/atomic/AtomicLong;I)V

    return-object p1

    :cond_2
    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj;->hjc()Z

    move-result p1

    if-eqz p1, :cond_3

    iget-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/hjc;->eV:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/svN;

    invoke-virtual {p1, v0}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/svN;->ex(Ljava/lang/String;)Ljava/util/List;

    move-result-object p1

    invoke-direct {p0, p1, p3}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/hjc;->Fj(Ljava/util/List;Ljava/util/List;)Z

    move-result v1

    if-eqz v1, :cond_3

    invoke-interface {p1}, Ljava/util/List;->size()I

    sget-object p3, Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;->eV:Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;

    invoke-virtual {p3}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->Vq()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object p3

    invoke-static {p3, p2}, Lcom/bytedance/sdk/component/Ubf/Fj/hjc/ex;->Fj(Ljava/util/concurrent/atomic/AtomicLong;I)V

    return-object p1

    :cond_3
    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj;->eV()Z

    move-result p1

    if-eqz p1, :cond_4

    iget-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/hjc;->Ubf:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/WR;

    invoke-virtual {p1, v0}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/svN;->ex(Ljava/lang/String;)Ljava/util/List;

    move-result-object p1

    invoke-direct {p0, p1, p3}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/hjc;->Fj(Ljava/util/List;Ljava/util/List;)Z

    move-result v1

    if-eqz v1, :cond_4

    invoke-interface {p1}, Ljava/util/List;->size()I

    sget-object p3, Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;->eV:Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;

    invoke-virtual {p3}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->Moo()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object p3

    invoke-static {p3, p2}, Lcom/bytedance/sdk/component/Ubf/Fj/hjc/ex;->Fj(Ljava/util/concurrent/atomic/AtomicLong;I)V

    return-object p1

    :cond_4
    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj;->WR()Z

    move-result p1

    if-eqz p1, :cond_5

    iget-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/hjc;->WR:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/Ubf;

    invoke-virtual {p1, v0}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/svN;->ex(Ljava/lang/String;)Ljava/util/List;

    move-result-object p1

    invoke-direct {p0, p1, p3}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/hjc;->Fj(Ljava/util/List;Ljava/util/List;)Z

    move-result p2

    if-eqz p2, :cond_5

    invoke-interface {p1}, Ljava/util/List;->size()I

    return-object p1

    :cond_5
    const/4 p1, 0x0

    return-object p1
.end method

.method public Fj(IJ)V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/hjc;->Fj:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/eV;

    if-eqz v0, :cond_0

    invoke-virtual {v0, p1, p2, p3}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/Fj;->Fj(IJ)V

    :cond_0
    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/hjc;->hjc:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/ex;

    if-eqz v0, :cond_1

    invoke-virtual {v0, p1, p2, p3}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/Fj;->Fj(IJ)V

    :cond_1
    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/hjc;->ex:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/Fj;

    if-eqz v0, :cond_2

    invoke-virtual {v0, p1, p2, p3}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/Fj;->Fj(IJ)V

    :cond_2
    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/hjc;->eV:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/svN;

    if-eqz v0, :cond_3

    invoke-virtual {v0, p1, p2, p3}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/svN;->Fj(IJ)V

    :cond_3
    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/hjc;->Ubf:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/WR;

    if-eqz v0, :cond_4

    invoke-virtual {v0, p1, p2, p3}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/svN;->Fj(IJ)V

    :cond_4
    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/hjc;->WR:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/Ubf;

    if-eqz v0, :cond_5

    invoke-virtual {v0, p1, p2, p3}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/svN;->Fj(IJ)V

    :cond_5
    return-void
.end method

.method public Fj(ILjava/util/List;)V
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I",
            "Ljava/util/List<",
            "Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;",
            ">;)V"
        }
    .end annotation

    if-eqz p2, :cond_7

    invoke-interface {p2}, Ljava/util/List;->size()I

    move-result v0

    if-eqz v0, :cond_7

    const/4 v0, 0x0

    invoke-interface {p2, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v1

    if-eqz v1, :cond_7

    invoke-interface {p2, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;

    const/16 v1, 0xc8

    if-eq p1, v1, :cond_0

    const/4 v2, -0x1

    if-ne p1, v2, :cond_7

    :cond_0
    sget-object v2, Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;->eV:Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;

    invoke-virtual {v2}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->cs()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object v3

    invoke-interface {p2}, Ljava/util/List;->size()I

    move-result v4

    invoke-static {v3, v4}, Lcom/bytedance/sdk/component/Ubf/Fj/hjc/ex;->Fj(Ljava/util/concurrent/atomic/AtomicLong;I)V

    if-eq p1, v1, :cond_1

    invoke-virtual {v2}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->At()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object p1

    invoke-interface {p2}, Ljava/util/List;->size()I

    move-result v1

    invoke-static {p1, v1}, Lcom/bytedance/sdk/component/Ubf/Fj/hjc/ex;->Fj(Ljava/util/concurrent/atomic/AtomicLong;I)V

    :cond_1
    invoke-interface {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;->eV()B

    move-result p1

    const/4 v1, 0x1

    if-nez p1, :cond_2

    invoke-interface {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;->Ubf()B

    move-result p1

    if-ne p1, v1, :cond_2

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj;->Fj()Z

    move-result p1

    if-eqz p1, :cond_7

    iget-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/hjc;->Fj:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/eV;

    invoke-virtual {p1, p2}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/Fj;->ex(Ljava/util/List;)V

    return-void

    :cond_2
    invoke-interface {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;->eV()B

    move-result p1

    const/4 v2, 0x3

    const/4 v3, 0x2

    if-ne p1, v2, :cond_3

    invoke-interface {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;->Ubf()B

    move-result p1

    if-ne p1, v3, :cond_3

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj;->Ubf()Z

    move-result p1

    if-eqz p1, :cond_7

    iget-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/hjc;->hjc:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/ex;

    invoke-virtual {p1, p2}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/Fj;->ex(Ljava/util/List;)V

    return-void

    :cond_3
    invoke-interface {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;->eV()B

    move-result p1

    if-nez p1, :cond_4

    invoke-interface {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;->Ubf()B

    move-result p1

    if-ne p1, v3, :cond_4

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj;->ex()Z

    move-result p1

    if-eqz p1, :cond_7

    iget-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/hjc;->ex:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/Fj;

    invoke-virtual {p1, p2}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/Fj;->ex(Ljava/util/List;)V

    return-void

    :cond_4
    invoke-interface {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;->eV()B

    move-result p1

    if-ne p1, v1, :cond_5

    invoke-interface {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;->Ubf()B

    move-result p1

    if-ne p1, v3, :cond_5

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj;->hjc()Z

    move-result p1

    if-eqz p1, :cond_7

    iget-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/hjc;->eV:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/svN;

    invoke-virtual {p1, p2}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/svN;->ex(Ljava/util/List;)V

    return-void

    :cond_5
    invoke-interface {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;->eV()B

    move-result p1

    if-ne p1, v1, :cond_6

    invoke-interface {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;->Ubf()B

    move-result p1

    if-ne p1, v2, :cond_6

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj;->eV()Z

    move-result p1

    if-eqz p1, :cond_7

    iget-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/hjc;->Ubf:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/WR;

    invoke-virtual {p1, p2}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/svN;->ex(Ljava/util/List;)V

    return-void

    :cond_6
    invoke-interface {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;->eV()B

    move-result p1

    if-ne p1, v3, :cond_7

    invoke-interface {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;->Ubf()B

    move-result p1

    if-ne p1, v2, :cond_7

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj;->WR()Z

    move-result p1

    if-eqz p1, :cond_7

    iget-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/hjc;->WR:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/Ubf;

    invoke-virtual {p1, p2}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/svN;->ex(Ljava/util/List;)V

    :cond_7
    return-void
.end method

.method public Fj(Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;I)V
    .locals 3

    if-nez p1, :cond_0

    return-void

    :cond_0
    const/4 p2, 0x1

    :try_start_0
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v0

    invoke-interface {p1, v0, v1}, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;->ex(J)V

    invoke-interface {p1}, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;->eV()B

    move-result v0

    if-nez v0, :cond_1

    invoke-interface {p1}, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;->Ubf()B

    move-result v0

    if-ne v0, p2, :cond_1

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj;->Fj()Z

    move-result v0

    if-eqz v0, :cond_6

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/hjc;->Fj:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/eV;

    invoke-virtual {v0, p1}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/hjc;->Fj(Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;)V

    return-void

    :cond_1
    invoke-interface {p1}, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;->eV()B

    move-result v0

    const/4 v1, 0x3

    const/4 v2, 0x2

    if-ne v0, v1, :cond_2

    invoke-interface {p1}, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;->Ubf()B

    move-result v0

    if-ne v0, v2, :cond_2

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj;->Ubf()Z

    move-result v0

    if-eqz v0, :cond_6

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/hjc;->hjc:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/ex;

    invoke-virtual {v0, p1}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/hjc;->Fj(Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;)V

    return-void

    :cond_2
    invoke-interface {p1}, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;->eV()B

    move-result v0

    if-nez v0, :cond_3

    invoke-interface {p1}, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;->Ubf()B

    move-result v0

    if-ne v0, v2, :cond_3

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj;->ex()Z

    move-result v0

    if-eqz v0, :cond_6

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/hjc;->ex:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/Fj;

    invoke-virtual {v0, p1}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/hjc;->Fj(Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;)V

    return-void

    :cond_3
    invoke-interface {p1}, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;->eV()B

    move-result v0

    if-ne v0, p2, :cond_4

    invoke-interface {p1}, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;->Ubf()B

    move-result v0

    if-ne v0, v2, :cond_4

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj;->hjc()Z

    move-result v0

    if-eqz v0, :cond_6

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/hjc;->eV:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/svN;

    invoke-virtual {v0, p1}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/hjc;->Fj(Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;)V

    return-void

    :cond_4
    invoke-interface {p1}, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;->eV()B

    move-result v0

    if-ne v0, p2, :cond_5

    invoke-interface {p1}, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;->Ubf()B

    move-result v0

    if-ne v0, v1, :cond_5

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj;->eV()Z

    move-result v0

    if-eqz v0, :cond_6

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/hjc;->Ubf:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/WR;

    invoke-virtual {v0, p1}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/hjc;->Fj(Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;)V

    return-void

    :cond_5
    invoke-interface {p1}, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;->eV()B

    move-result v0

    if-ne v0, v2, :cond_6

    invoke-interface {p1}, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;->Ubf()B

    move-result v0

    if-ne v0, v1, :cond_6

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj;->WR()Z

    move-result v0

    if-eqz v0, :cond_6

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/hjc;->WR:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/Ubf;

    invoke-virtual {v0, p1}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/hjc;->Fj(Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    :cond_6
    return-void

    :catchall_0
    sget-object p1, Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;->eV:Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->kF()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object p1

    invoke-static {p1, p2}, Lcom/bytedance/sdk/component/Ubf/Fj/hjc/ex;->Fj(Ljava/util/concurrent/atomic/AtomicLong;I)V

    return-void
.end method

.method public Fj(IZ)Z
    .locals 1

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj;->Fj()Z

    move-result p2

    const/4 v0, 0x1

    if-eqz p2, :cond_0

    iget-object p2, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/hjc;->Fj:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/eV;

    if-eqz p2, :cond_0

    invoke-virtual {p2, p1}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/Fj;->Fj(I)Z

    move-result p2

    if-eqz p2, :cond_0

    sget-object p1, Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;->eV:Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->Tc()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object p1

    invoke-static {p1, v0}, Lcom/bytedance/sdk/component/Ubf/Fj/hjc/ex;->Fj(Ljava/util/concurrent/atomic/AtomicLong;I)V

    return v0

    :cond_0
    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj;->Ubf()Z

    move-result p2

    if-eqz p2, :cond_1

    iget-object p2, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/hjc;->hjc:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/ex;

    if-eqz p2, :cond_1

    invoke-virtual {p2, p1}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/Fj;->Fj(I)Z

    move-result p2

    if-eqz p2, :cond_1

    return v0

    :cond_1
    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj;->ex()Z

    move-result p2

    if-eqz p2, :cond_2

    iget-object p2, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/hjc;->ex:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/Fj;

    if-eqz p2, :cond_2

    invoke-virtual {p2, p1}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/Fj;->Fj(I)Z

    move-result p2

    if-eqz p2, :cond_2

    sget-object p1, Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;->eV:Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->JW()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object p1

    invoke-static {p1, v0}, Lcom/bytedance/sdk/component/Ubf/Fj/hjc/ex;->Fj(Ljava/util/concurrent/atomic/AtomicLong;I)V

    return v0

    :cond_2
    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj;->hjc()Z

    move-result p2

    if-eqz p2, :cond_3

    iget-object p2, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/hjc;->eV:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/svN;

    if-eqz p2, :cond_3

    invoke-virtual {p2, p1}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/svN;->Fj(I)Z

    move-result p2

    if-eqz p2, :cond_3

    sget-object p1, Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;->eV:Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->JU()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object p1

    invoke-static {p1, v0}, Lcom/bytedance/sdk/component/Ubf/Fj/hjc/ex;->Fj(Ljava/util/concurrent/atomic/AtomicLong;I)V

    return v0

    :cond_3
    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj;->eV()Z

    move-result p2

    if-eqz p2, :cond_4

    iget-object p2, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/hjc;->Ubf:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/WR;

    if-eqz p2, :cond_4

    invoke-virtual {p2, p1}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/svN;->Fj(I)Z

    move-result p2

    if-eqz p2, :cond_4

    sget-object p1, Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;->eV:Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->Ql()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object p1

    invoke-static {p1, v0}, Lcom/bytedance/sdk/component/Ubf/Fj/hjc/ex;->Fj(Ljava/util/concurrent/atomic/AtomicLong;I)V

    return v0

    :cond_4
    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj;->WR()Z

    move-result p2

    if-eqz p2, :cond_5

    iget-object p2, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/hjc;->WR:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/Ubf;

    if-eqz p2, :cond_5

    invoke-virtual {p2, p1}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/svN;->Fj(I)Z

    move-result p1

    if-eqz p1, :cond_5

    return v0

    :cond_5
    const/4 p1, 0x0

    return p1
.end method

.method public ex(Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;I)Ljava/util/List;
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;",
            "I)",
            "Ljava/util/List<",
            "Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;",
            ">;"
        }
    .end annotation

    invoke-interface {p1}, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;->eV()B

    move-result v0

    const/4 v1, 0x0

    const-string v2, "_id"

    const/4 v3, 0x1

    if-nez v0, :cond_2

    invoke-interface {p1}, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;->Ubf()B

    move-result v0

    if-ne v0, v3, :cond_2

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj;->Fj()Z

    move-result v0

    if-eqz v0, :cond_2

    iget-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/hjc;->svN:Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;->ex()I

    move-result p1

    if-le p1, p2, :cond_1

    iget-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/hjc;->svN:Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;->ex()I

    move-result p1

    sub-int/2addr p1, p2

    iget-object p2, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/hjc;->Fj:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/eV;

    invoke-virtual {p2, p1, v2}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/Fj;->Fj(ILjava/lang/String;)Ljava/util/List;

    move-result-object p1

    if-eqz p1, :cond_0

    invoke-interface {p1}, Ljava/util/List;->size()I

    move-result p2

    if-eqz p2, :cond_0

    sget-object p2, Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;->eV:Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;

    invoke-virtual {p2}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->rf()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object p2

    invoke-static {p2, v3}, Lcom/bytedance/sdk/component/Ubf/Fj/hjc/ex;->Fj(Ljava/util/concurrent/atomic/AtomicLong;I)V

    :cond_0
    return-object p1

    :cond_1
    return-object v1

    :cond_2
    invoke-interface {p1}, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;->eV()B

    move-result v0

    const/4 v4, 0x3

    const/4 v5, 0x2

    if-ne v0, v4, :cond_3

    invoke-interface {p1}, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;->Ubf()B

    move-result v0

    if-ne v0, v5, :cond_3

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj;->Ubf()Z

    move-result v0

    if-eqz v0, :cond_3

    iget-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/hjc;->mSE:Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;->ex()I

    move-result p1

    if-le p1, p2, :cond_a

    iget-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/hjc;->mSE:Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;->ex()I

    move-result p1

    sub-int/2addr p1, p2

    iget-object p2, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/hjc;->hjc:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/ex;

    invoke-virtual {p2, p1, v2}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/Fj;->Fj(ILjava/lang/String;)Ljava/util/List;

    move-result-object p1

    return-object p1

    :cond_3
    invoke-interface {p1}, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;->eV()B

    move-result v0

    if-nez v0, :cond_5

    invoke-interface {p1}, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;->Ubf()B

    move-result v0

    if-ne v0, v5, :cond_5

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj;->ex()Z

    move-result v0

    if-eqz v0, :cond_5

    iget-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/hjc;->BcC:Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;->ex()I

    move-result p1

    if-le p1, p2, :cond_a

    iget-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/hjc;->BcC:Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;->ex()I

    move-result p1

    sub-int/2addr p1, p2

    iget-object p2, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/hjc;->ex:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/Fj;

    invoke-virtual {p2, p1, v2}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/Fj;->Fj(ILjava/lang/String;)Ljava/util/List;

    move-result-object p1

    if-eqz p1, :cond_4

    invoke-interface {p1}, Ljava/util/List;->size()I

    move-result p2

    if-eqz p2, :cond_4

    sget-object p2, Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;->eV:Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;

    invoke-virtual {p2}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->uy()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object p2

    invoke-static {p2, v3}, Lcom/bytedance/sdk/component/Ubf/Fj/hjc/ex;->Fj(Ljava/util/concurrent/atomic/AtomicLong;I)V

    :cond_4
    return-object p1

    :cond_5
    invoke-interface {p1}, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;->eV()B

    move-result v0

    if-ne v0, v3, :cond_7

    invoke-interface {p1}, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;->Ubf()B

    move-result v0

    if-ne v0, v5, :cond_7

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj;->hjc()Z

    move-result v0

    if-eqz v0, :cond_7

    iget-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/hjc;->Ko:Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;->ex()I

    move-result p1

    if-le p1, p2, :cond_a

    iget-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/hjc;->Ko:Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;->ex()I

    move-result p1

    sub-int/2addr p1, p2

    iget-object p2, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/hjc;->eV:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/svN;

    invoke-virtual {p2, p1, v2}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/svN;->Fj(ILjava/lang/String;)Ljava/util/List;

    move-result-object p1

    if-eqz p1, :cond_6

    invoke-interface {p1}, Ljava/util/List;->size()I

    move-result p2

    if-eqz p2, :cond_6

    sget-object p2, Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;->eV:Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;

    invoke-virtual {p2}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->lv()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object p2

    invoke-static {p2, v3}, Lcom/bytedance/sdk/component/Ubf/Fj/hjc/ex;->Fj(Ljava/util/concurrent/atomic/AtomicLong;I)V

    :cond_6
    return-object p1

    :cond_7
    invoke-interface {p1}, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;->eV()B

    move-result v0

    if-ne v0, v3, :cond_9

    invoke-interface {p1}, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;->Ubf()B

    move-result v0

    if-ne v0, v4, :cond_9

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj;->eV()Z

    move-result v0

    if-eqz v0, :cond_9

    iget-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/hjc;->rAx:Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;->ex()I

    move-result p1

    if-le p1, p2, :cond_a

    iget-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/hjc;->rAx:Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;->ex()I

    move-result p1

    sub-int/2addr p1, p2

    iget-object p2, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/hjc;->Ubf:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/WR;

    invoke-virtual {p2, p1, v2}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/svN;->Fj(ILjava/lang/String;)Ljava/util/List;

    move-result-object p1

    if-eqz p1, :cond_8

    invoke-interface {p1}, Ljava/util/List;->size()I

    move-result p2

    if-eqz p2, :cond_8

    sget-object p2, Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;->eV:Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;

    invoke-virtual {p2}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->rXP()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object p2

    invoke-static {p2, v3}, Lcom/bytedance/sdk/component/Ubf/Fj/hjc/ex;->Fj(Ljava/util/concurrent/atomic/AtomicLong;I)V

    :cond_8
    return-object p1

    :cond_9
    invoke-interface {p1}, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;->eV()B

    move-result v0

    if-ne v0, v5, :cond_a

    invoke-interface {p1}, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;->Ubf()B

    move-result p1

    if-ne p1, v4, :cond_a

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj;->WR()Z

    move-result p1

    if-eqz p1, :cond_a

    iget-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/hjc;->UYd:Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;->ex()I

    move-result p1

    if-le p1, p2, :cond_a

    iget-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/hjc;->UYd:Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;->ex()I

    move-result p1

    sub-int/2addr p1, p2

    iget-object p2, p0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/hjc;->WR:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/Ubf;

    invoke-virtual {p2, p1, v2}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Fj/svN;->Fj(ILjava/lang/String;)Ljava/util/List;

    move-result-object p1

    return-object p1

    :cond_a
    return-object v1
.end method
