.class public interface abstract Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;
.super Ljava/lang/Object;


# virtual methods
.method public abstract BcC()Z
.end method

.method public abstract Fj(Lorg/json/JSONObject;)Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;
.end method

.method public abstract Fj(Ljava/lang/String;)Ljava/lang/String;
.end method

.method public abstract Fj(Z)V
.end method

.method public abstract Fj(ZIJLcom/bytedance/sdk/component/Ubf/Fj/WR/eV;)V
.end method

.method public abstract Fj()Z
.end method

.method public abstract Fj(Landroid/content/Context;)Z
.end method

.method public abstract Ko()Lcom/bytedance/sdk/component/Ubf/Fj/Ubf/hjc;
.end method

.method public abstract Tc()Z
.end method

.method public abstract UYd()Lcom/bytedance/sdk/component/Ubf/Fj/svN;
.end method

.method public abstract Ubf()Ljava/util/concurrent/Executor;
.end method

.method public abstract WR()I
.end method

.method public abstract dG()V
.end method

.method public abstract eV()Ljava/util/concurrent/Executor;
.end method

.method public abstract ex(Ljava/lang/String;)Ljava/lang/String;
.end method

.method public abstract ex()Z
.end method

.method public abstract hjc(Ljava/lang/String;)I
.end method

.method public abstract hjc()Z
.end method

.method public abstract mSE()Ljava/lang/String;
.end method

.method public abstract rAx()Lcom/bytedance/sdk/component/Ubf/Fj/WR;
.end method

.method public abstract svN()Z
.end method
