.class public interface abstract Lcom/bytedance/sdk/component/Ubf/Fj/Ubf/Fj;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "<T:",
        "Ljava/lang/Object;",
        ">",
        "Ljava/lang/Object;"
    }
.end annotation


# virtual methods
.method public abstract Fj(Ljava/util/List;)Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/ex;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "TT;>;)",
            "Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/ex;"
        }
    .end annotation
.end method

.method public abstract Fj(Lorg/json/JSONObject;)Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/ex;
.end method
