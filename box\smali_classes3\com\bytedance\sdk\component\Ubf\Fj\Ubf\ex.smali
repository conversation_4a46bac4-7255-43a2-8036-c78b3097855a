.class public Lcom/bytedance/sdk/component/Ubf/Fj/Ubf/ex;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/sdk/component/Ubf/Fj/Ubf/Fj;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Lcom/bytedance/sdk/component/Ubf/Fj/Ubf/Fj<",
        "Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;",
        ">;"
    }
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public Fj(Ljava/util/List;)Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/ex;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;",
            ">;)",
            "Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/ex;"
        }
    .end annotation

    const/4 p1, 0x0

    return-object p1
.end method

.method public Fj(Lorg/json/JSONObject;)Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/ex;
    .locals 0

    const/4 p1, 0x0

    return-object p1
.end method
