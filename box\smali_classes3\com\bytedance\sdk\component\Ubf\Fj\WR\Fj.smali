.class public Lcom/bytedance/sdk/component/Ubf/Fj/WR/Fj;
.super Ljava/lang/Object;


# static fields
.field private static volatile Fj:Lcom/bytedance/sdk/component/Ubf/Fj/WR/ex;


# direct methods
.method public static Fj()Lcom/bytedance/sdk/component/Ubf/Fj/WR/ex;
    .locals 5

    sget-object v0, Lcom/bytedance/sdk/component/Ubf/Fj/WR/Fj;->Fj:Lcom/bytedance/sdk/component/Ubf/Fj/WR/ex;

    if-nez v0, :cond_1

    const-class v0, Lcom/bytedance/sdk/component/Ubf/Fj/WR/ex;

    monitor-enter v0

    :try_start_0
    sget-object v1, Lcom/bytedance/sdk/component/Ubf/Fj/WR/Fj;->Fj:Lcom/bytedance/sdk/component/Ubf/Fj/WR/ex;

    if-nez v1, :cond_0

    new-instance v1, Lcom/bytedance/sdk/component/Ubf/Fj/WR/hjc;

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->svN()Lcom/bytedance/sdk/component/Ubf/Fj/BcC;

    move-result-object v2

    invoke-virtual {v2}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->WR()Landroid/content/Context;

    move-result-object v2

    new-instance v3, Lcom/bytedance/sdk/component/Ubf/Fj/WR/WR;

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->svN()Lcom/bytedance/sdk/component/Ubf/Fj/BcC;

    move-result-object v4

    invoke-virtual {v4}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->WR()Landroid/content/Context;

    move-result-object v4

    invoke-direct {v3, v4}, Lcom/bytedance/sdk/component/Ubf/Fj/WR/WR;-><init>(Landroid/content/Context;)V

    invoke-direct {v1, v2, v3}, Lcom/bytedance/sdk/component/Ubf/Fj/WR/hjc;-><init>(Landroid/content/Context;Lcom/bytedance/sdk/component/Ubf/Fj/WR/Ubf;)V

    sput-object v1, Lcom/bytedance/sdk/component/Ubf/Fj/WR/Fj;->Fj:Lcom/bytedance/sdk/component/Ubf/Fj/WR/ex;

    goto :goto_0

    :catchall_0
    move-exception v1

    goto :goto_1

    :cond_0
    :goto_0
    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    goto :goto_2

    :goto_1
    monitor-exit v0

    throw v1

    :cond_1
    :goto_2
    sget-object v0, Lcom/bytedance/sdk/component/Ubf/Fj/WR/Fj;->Fj:Lcom/bytedance/sdk/component/Ubf/Fj/WR/ex;

    return-object v0
.end method
