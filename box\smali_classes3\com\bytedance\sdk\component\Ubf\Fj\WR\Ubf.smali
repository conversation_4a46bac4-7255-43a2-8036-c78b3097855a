.class interface abstract Lcom/bytedance/sdk/component/Ubf/Fj/WR/Ubf;
.super Ljava/lang/Object;


# virtual methods
.method public abstract Fj(Ljava/lang/String;)Lcom/bytedance/sdk/component/Ubf/Fj/WR/eV;
.end method

.method public abstract Fj()Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lcom/bytedance/sdk/component/Ubf/Fj/WR/eV;",
            ">;"
        }
    .end annotation
.end method

.method public abstract Fj(Lcom/bytedance/sdk/component/Ubf/Fj/WR/eV;)V
.end method

.method public abstract ex(Lcom/bytedance/sdk/component/Ubf/Fj/WR/eV;)V
.end method

.method public abstract hjc(Lcom/bytedance/sdk/component/Ubf/Fj/WR/eV;)V
.end method
