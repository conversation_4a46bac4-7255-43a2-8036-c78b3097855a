.class public Lcom/bytedance/sdk/component/Ubf/Fj/WR/eV;
.super Ljava/lang/Object;


# instance fields
.field private BcC:Ljava/lang/String;

.field private final Fj:Ljava/lang/String;

.field private Ko:Ljava/lang/String;

.field private final Ubf:I

.field private final WR:Ljava/lang/String;

.field private eV:I

.field private final ex:Ljava/lang/String;

.field private final hjc:Z

.field private mSE:I

.field private rAx:Z

.field private svN:Ljava/lang/String;


# direct methods
.method public constructor <init>(Ljava/lang/String;Ljava/lang/String;ZILjava/lang/String;)V
    .locals 1

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, -0x1

    iput v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/WR/eV;->eV:I

    const/4 v0, 0x0

    iput v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/WR/eV;->mSE:I

    const/4 v0, 0x0

    iput-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/WR/eV;->Ko:Ljava/lang/String;

    iput-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/WR/eV;->Fj:Ljava/lang/String;

    iput-object p2, p0, Lcom/bytedance/sdk/component/Ubf/Fj/WR/eV;->ex:Ljava/lang/String;

    iput-boolean p3, p0, Lcom/bytedance/sdk/component/Ubf/Fj/WR/eV;->hjc:Z

    iput p4, p0, Lcom/bytedance/sdk/component/Ubf/Fj/WR/eV;->Ubf:I

    iput-object p5, p0, Lcom/bytedance/sdk/component/Ubf/Fj/WR/eV;->WR:Ljava/lang/String;

    return-void
.end method


# virtual methods
.method public BcC()I
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/WR/eV;->mSE:I

    return v0
.end method

.method public Fj(Ljava/lang/String;Ljava/util/Map;)Ljava/lang/Runnable;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ">;)",
            "Ljava/lang/Runnable;"
        }
    .end annotation

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/WR/Fj;->Fj()Lcom/bytedance/sdk/component/Ubf/Fj/WR/ex;

    move-result-object v0

    invoke-interface {v0, p0, p1, p2}, Lcom/bytedance/sdk/component/Ubf/Fj/WR/ex;->Fj(Lcom/bytedance/sdk/component/Ubf/Fj/WR/eV;Ljava/lang/String;Ljava/util/Map;)Ljava/lang/Runnable;

    move-result-object p1

    return-object p1
.end method

.method public Fj()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/WR/eV;->Fj:Ljava/lang/String;

    return-object v0
.end method

.method public Fj(I)V
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/WR/eV;->eV:I

    return-void
.end method

.method public Fj(Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/WR/eV;->svN:Ljava/lang/String;

    return-void
.end method

.method public Fj(Z)V
    .locals 0

    iput-boolean p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/WR/eV;->rAx:Z

    return-void
.end method

.method public Ko()Z
    .locals 1

    iget-boolean v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/WR/eV;->rAx:Z

    return v0
.end method

.method public Ubf()I
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/WR/eV;->Ubf:I

    return v0
.end method

.method public WR()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/WR/eV;->WR:Ljava/lang/String;

    return-object v0
.end method

.method public eV()I
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/WR/eV;->eV:I

    return v0
.end method

.method public ex()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/WR/eV;->ex:Ljava/lang/String;

    return-object v0
.end method

.method public ex(I)V
    .locals 1

    iput p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/WR/eV;->mSE:I

    if-nez p1, :cond_0

    return-void

    :cond_0
    iget-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/WR/eV;->svN:Ljava/lang/String;

    invoke-static {p1}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result p1

    if-eqz p1, :cond_1

    iget p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/WR/eV;->mSE:I

    invoke-static {p1}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    move-result-object p1

    iput-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/WR/eV;->svN:Ljava/lang/String;

    return-void

    :cond_1
    new-instance p1, Ljava/lang/StringBuilder;

    invoke-direct {p1}, Ljava/lang/StringBuilder;-><init>()V

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/WR/eV;->svN:Ljava/lang/String;

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v0, ","

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/WR/eV;->mSE:I

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    iput-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/WR/eV;->svN:Ljava/lang/String;

    return-void
.end method

.method public ex(Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/WR/eV;->BcC:Ljava/lang/String;

    return-void
.end method

.method public hjc(Ljava/lang/String;)V
    .locals 1

    iput-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/WR/eV;->Ko:Ljava/lang/String;

    invoke-static {p1}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result p1

    if-eqz p1, :cond_0

    return-void

    :cond_0
    iget-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/WR/eV;->BcC:Ljava/lang/String;

    invoke-static {p1}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result p1

    if-eqz p1, :cond_1

    iget-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/WR/eV;->Ko:Ljava/lang/String;

    invoke-static {p1}, Ljava/lang/String;->valueOf(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    iput-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/WR/eV;->BcC:Ljava/lang/String;

    return-void

    :cond_1
    new-instance p1, Ljava/lang/StringBuilder;

    invoke-direct {p1}, Ljava/lang/StringBuilder;-><init>()V

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/WR/eV;->BcC:Ljava/lang/String;

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v0, ","

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/WR/eV;->Ko:Ljava/lang/String;

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    iput-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/WR/eV;->BcC:Ljava/lang/String;

    return-void
.end method

.method public hjc()Z
    .locals 1

    iget-boolean v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/WR/eV;->hjc:Z

    return v0
.end method

.method public mSE()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/WR/eV;->BcC:Ljava/lang/String;

    return-object v0
.end method

.method public rAx()Z
    .locals 2

    iget v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/WR/eV;->eV:I

    const/4 v1, -0x1

    if-ne v0, v1, :cond_0

    const/4 v0, 0x1

    return v0

    :cond_0
    const/4 v0, 0x0

    return v0
.end method

.method public svN()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/WR/eV;->svN:Ljava/lang/String;

    return-object v0
.end method
