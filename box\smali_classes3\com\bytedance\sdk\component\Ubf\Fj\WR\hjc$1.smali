.class Lcom/bytedance/sdk/component/Ubf/Fj/WR/hjc$1;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/component/Ubf/Fj/WR/hjc;->Fj(Lcom/bytedance/sdk/component/Ubf/Fj/WR/eV;Ljava/lang/String;Ljava/util/Map;)Ljava/lang/Runnable;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/component/Ubf/Fj/WR/eV;

.field final synthetic eV:Lcom/bytedance/sdk/component/Ubf/Fj/WR/hjc;

.field final synthetic ex:Ljava/lang/String;

.field final synthetic hjc:Ljava/util/Map;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/component/Ubf/Fj/WR/hjc;Lcom/bytedance/sdk/component/Ubf/Fj/WR/eV;Ljava/lang/String;Ljava/util/Map;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/WR/hjc$1;->eV:Lcom/bytedance/sdk/component/Ubf/Fj/WR/hjc;

    iput-object p2, p0, Lcom/bytedance/sdk/component/Ubf/Fj/WR/hjc$1;->Fj:Lcom/bytedance/sdk/component/Ubf/Fj/WR/eV;

    iput-object p3, p0, Lcom/bytedance/sdk/component/Ubf/Fj/WR/hjc$1;->ex:Ljava/lang/String;

    iput-object p4, p0, Lcom/bytedance/sdk/component/Ubf/Fj/WR/hjc$1;->hjc:Ljava/util/Map;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 7

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/WR/hjc$1;->eV:Lcom/bytedance/sdk/component/Ubf/Fj/WR/hjc;

    invoke-static {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/WR/hjc;->Fj(Lcom/bytedance/sdk/component/Ubf/Fj/WR/hjc;)Lcom/bytedance/sdk/component/Ubf/Fj/WR/Ubf;

    move-result-object v0

    iget-object v1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/WR/hjc$1;->Fj:Lcom/bytedance/sdk/component/Ubf/Fj/WR/eV;

    invoke-virtual {v1}, Lcom/bytedance/sdk/component/Ubf/Fj/WR/eV;->Fj()Ljava/lang/String;

    move-result-object v1

    invoke-interface {v0, v1}, Lcom/bytedance/sdk/component/Ubf/Fj/WR/Ubf;->Fj(Ljava/lang/String;)Lcom/bytedance/sdk/component/Ubf/Fj/WR/eV;

    move-result-object v0

    if-eqz v0, :cond_0

    new-instance v0, Lcom/bytedance/sdk/component/Ubf/Fj/WR/hjc$Fj;

    iget-object v2, p0, Lcom/bytedance/sdk/component/Ubf/Fj/WR/hjc$1;->eV:Lcom/bytedance/sdk/component/Ubf/Fj/WR/hjc;

    iget-object v3, p0, Lcom/bytedance/sdk/component/Ubf/Fj/WR/hjc$1;->Fj:Lcom/bytedance/sdk/component/Ubf/Fj/WR/eV;

    iget-object v4, p0, Lcom/bytedance/sdk/component/Ubf/Fj/WR/hjc$1;->ex:Ljava/lang/String;

    iget-object v5, p0, Lcom/bytedance/sdk/component/Ubf/Fj/WR/hjc$1;->hjc:Ljava/util/Map;

    const/4 v6, 0x0

    move-object v1, v0

    invoke-direct/range {v1 .. v6}, Lcom/bytedance/sdk/component/Ubf/Fj/WR/hjc$Fj;-><init>(Lcom/bytedance/sdk/component/Ubf/Fj/WR/hjc;Lcom/bytedance/sdk/component/Ubf/Fj/WR/eV;Ljava/lang/String;Ljava/util/Map;Lcom/bytedance/sdk/component/Ubf/Fj/WR/hjc$1;)V

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/WR/hjc$Fj;->run()V

    :cond_0
    return-void
.end method
