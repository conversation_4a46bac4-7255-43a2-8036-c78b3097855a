.class Lcom/bytedance/sdk/component/Ubf/Fj/WR/hjc$2;
.super Lcom/bytedance/sdk/component/Ubf/Fj/Ubf/Ubf;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/component/Ubf/Fj/WR/hjc;->Fj(Ljava/lang/String;Z)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Ljava/lang/String;

.field final synthetic ex:Z

.field final synthetic hjc:Lcom/bytedance/sdk/component/Ubf/Fj/WR/hjc;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/component/Ubf/Fj/WR/hjc;Ljava/lang/String;Ljava/lang/String;Z)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/WR/hjc$2;->hjc:Lcom/bytedance/sdk/component/Ubf/Fj/WR/hjc;

    iput-object p3, p0, Lcom/bytedance/sdk/component/Ubf/Fj/WR/hjc$2;->Fj:Ljava/lang/String;

    iput-boolean p4, p0, Lcom/bytedance/sdk/component/Ubf/Fj/WR/hjc$2;->ex:Z

    invoke-direct {p0, p2}, Lcom/bytedance/sdk/component/Ubf/Fj/Ubf/Ubf;-><init>(Ljava/lang/String;)V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 4

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/WR/hjc$2;->hjc:Lcom/bytedance/sdk/component/Ubf/Fj/WR/hjc;

    invoke-static {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/WR/hjc;->Fj(Lcom/bytedance/sdk/component/Ubf/Fj/WR/hjc;)Lcom/bytedance/sdk/component/Ubf/Fj/WR/Ubf;

    move-result-object v0

    invoke-interface {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/WR/Ubf;->Fj()Ljava/util/List;

    move-result-object v0

    iget-object v1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/WR/hjc$2;->hjc:Lcom/bytedance/sdk/component/Ubf/Fj/WR/hjc;

    iget-object v2, p0, Lcom/bytedance/sdk/component/Ubf/Fj/WR/hjc$2;->Fj:Ljava/lang/String;

    iget-boolean v3, p0, Lcom/bytedance/sdk/component/Ubf/Fj/WR/hjc$2;->ex:Z

    invoke-static {v1, v0, v2, v3}, Lcom/bytedance/sdk/component/Ubf/Fj/WR/hjc;->Fj(Lcom/bytedance/sdk/component/Ubf/Fj/WR/hjc;Ljava/util/List;Ljava/lang/String;Z)V

    return-void
.end method
