.class Lcom/bytedance/sdk/component/Ubf/Fj/eV$1;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/component/Ubf/Fj/eV;->Fj(Lcom/bytedance/sdk/component/Ubf/Fj/Fj;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/component/Ubf/Fj/eV;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/component/Ubf/Fj/eV;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/eV$1;->Fj:Lcom/bytedance/sdk/component/Ubf/Fj/eV;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 0

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/hjc/Fj;->Fj()V

    return-void
.end method
