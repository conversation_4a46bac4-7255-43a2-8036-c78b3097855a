.class Lcom/bytedance/sdk/component/Ubf/Fj/eV$2;
.super Lcom/bytedance/sdk/component/Ubf/Fj/Ubf/Ubf;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/component/Ubf/Fj/eV;->Fj()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;

.field final synthetic ex:Lcom/bytedance/sdk/component/Ubf/Fj/eV;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/component/Ubf/Fj/eV;Ljava/lang/String;Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/eV$2;->ex:Lcom/bytedance/sdk/component/Ubf/Fj/eV;

    iput-object p3, p0, Lcom/bytedance/sdk/component/Ubf/Fj/eV$2;->Fj:Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;

    invoke-direct {p0, p2}, Lcom/bytedance/sdk/component/Ubf/Fj/Ubf/Ubf;-><init>(Ljava/lang/String;)V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 2

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/eV$2;->ex:Lcom/bytedance/sdk/component/Ubf/Fj/eV;

    iget-object v1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/eV$2;->Fj:Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;

    invoke-interface {v1}, Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;->WR()I

    move-result v1

    invoke-static {v0, v1}, Lcom/bytedance/sdk/component/Ubf/Fj/eV;->Fj(Lcom/bytedance/sdk/component/Ubf/Fj/eV;I)V

    return-void
.end method
