.class Lcom/bytedance/sdk/component/Ubf/Fj/eV$4;
.super Lcom/bytedance/sdk/component/Ubf/Fj/Ubf/Ubf;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/component/Ubf/Fj/eV;->ex(Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;

.field final synthetic ex:Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;

.field final synthetic hjc:Lcom/bytedance/sdk/component/Ubf/Fj/eV;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/component/Ubf/Fj/eV;Ljava/lang/String;Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/eV$4;->hjc:Lcom/bytedance/sdk/component/Ubf/Fj/eV;

    iput-object p3, p0, Lcom/bytedance/sdk/component/Ubf/Fj/eV$4;->Fj:Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;

    iput-object p4, p0, Lcom/bytedance/sdk/component/Ubf/Fj/eV$4;->ex:Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;

    invoke-direct {p0, p2}, Lcom/bytedance/sdk/component/Ubf/Fj/Ubf/Ubf;-><init>(Ljava/lang/String;)V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 3

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/eV$4;->hjc:Lcom/bytedance/sdk/component/Ubf/Fj/eV;

    iget-object v1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/eV$4;->Fj:Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;

    iget-object v2, p0, Lcom/bytedance/sdk/component/Ubf/Fj/eV$4;->ex:Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;

    invoke-interface {v2}, Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;->WR()I

    move-result v2

    invoke-static {v0, v1, v2}, Lcom/bytedance/sdk/component/Ubf/Fj/eV;->Fj(Lcom/bytedance/sdk/component/Ubf/Fj/eV;Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;I)V

    return-void
.end method
