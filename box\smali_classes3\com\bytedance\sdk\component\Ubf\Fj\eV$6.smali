.class Lcom/bytedance/sdk/component/Ubf/Fj/eV$6;
.super Lcom/bytedance/sdk/component/Ubf/Fj/Ubf/Ubf;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/component/Ubf/Fj/eV;->Fj(Ljava/lang/String;Z)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Ljava/lang/String;

.field final synthetic eV:Lcom/bytedance/sdk/component/Ubf/Fj/eV;

.field final synthetic ex:Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;

.field final synthetic hjc:Z


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/component/Ubf/Fj/eV;Ljava/lang/String;Ljava/lang/String;Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;Z)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/eV$6;->eV:Lcom/bytedance/sdk/component/Ubf/Fj/eV;

    iput-object p3, p0, Lcom/bytedance/sdk/component/Ubf/Fj/eV$6;->Fj:Ljava/lang/String;

    iput-object p4, p0, Lcom/bytedance/sdk/component/Ubf/Fj/eV$6;->ex:Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;

    iput-boolean p5, p0, Lcom/bytedance/sdk/component/Ubf/Fj/eV$6;->hjc:Z

    invoke-direct {p0, p2}, Lcom/bytedance/sdk/component/Ubf/Fj/Ubf/Ubf;-><init>(Ljava/lang/String;)V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 4

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/eV$6;->eV:Lcom/bytedance/sdk/component/Ubf/Fj/eV;

    iget-object v1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/eV$6;->Fj:Ljava/lang/String;

    iget-object v2, p0, Lcom/bytedance/sdk/component/Ubf/Fj/eV$6;->ex:Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;

    invoke-interface {v2}, Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;->WR()I

    move-result v2

    iget-boolean v3, p0, Lcom/bytedance/sdk/component/Ubf/Fj/eV$6;->hjc:Z

    invoke-static {v0, v1, v2, v3}, Lcom/bytedance/sdk/component/Ubf/Fj/eV;->Fj(Lcom/bytedance/sdk/component/Ubf/Fj/eV;Ljava/lang/String;IZ)V

    return-void
.end method
