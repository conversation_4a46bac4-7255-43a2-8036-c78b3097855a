.class public Lcom/bytedance/sdk/component/Ubf/Fj/eV;
.super Ljava/lang/Object;


# static fields
.field public static final Fj:Lcom/bytedance/sdk/component/Ubf/Fj/eV;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    new-instance v0, Lcom/bytedance/sdk/component/Ubf/Fj/eV;

    invoke-direct {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/eV;-><init>()V

    sput-object v0, Lcom/bytedance/sdk/component/Ubf/Fj/eV;->Fj:Lcom/bytedance/sdk/component/Ubf/Fj/eV;

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method private Fj(I)V
    .locals 1

    if-nez p1, :cond_0

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/ex/Fj;->Fj()V

    return-void

    :cond_0
    const/4 v0, 0x1

    if-ne p1, v0, :cond_1

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/ex/ex;->Fj()V

    :cond_1
    return-void
.end method

.method private Fj(Lcom/bytedance/sdk/component/Ubf/Fj/Fj;)V
    .locals 2

    invoke-static {}, Landroid/os/Looper;->myLooper()Landroid/os/Looper;

    move-result-object v0

    invoke-static {}, Landroid/os/Looper;->getMainLooper()Landroid/os/Looper;

    move-result-object v1

    if-eq v0, v1, :cond_0

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/hjc/Fj;->ex()Z

    move-result v0

    if-eqz v0, :cond_0

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/hjc/Fj;->Fj()V

    return-void

    :cond_0
    invoke-virtual {p1}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj;->eV()Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;

    move-result-object p1

    if-eqz p1, :cond_1

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/hjc/Fj;->ex()Z

    move-result v0

    if-eqz v0, :cond_1

    invoke-interface {p1}, Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;->Ubf()Ljava/util/concurrent/Executor;

    move-result-object p1

    if-eqz p1, :cond_1

    new-instance v0, Lcom/bytedance/sdk/component/Ubf/Fj/eV$1;

    invoke-direct {v0, p0}, Lcom/bytedance/sdk/component/Ubf/Fj/eV$1;-><init>(Lcom/bytedance/sdk/component/Ubf/Fj/eV;)V

    invoke-interface {p1, v0}, Ljava/util/concurrent/Executor;->execute(Ljava/lang/Runnable;)V

    :cond_1
    return-void
.end method

.method private Fj(Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;I)V
    .locals 1

    if-nez p2, :cond_0

    invoke-static {p1}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/ex/Fj;->Fj(Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;)V

    return-void

    :cond_0
    const/4 v0, 0x1

    if-ne p2, v0, :cond_1

    invoke-static {p1}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/ex/ex;->Fj(Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;)V

    :cond_1
    return-void
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/component/Ubf/Fj/eV;I)V
    .locals 0

    invoke-direct {p0, p1}, Lcom/bytedance/sdk/component/Ubf/Fj/eV;->Fj(I)V

    return-void
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/component/Ubf/Fj/eV;Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;I)V
    .locals 0

    invoke-direct {p0, p1, p2}, Lcom/bytedance/sdk/component/Ubf/Fj/eV;->Fj(Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;I)V

    return-void
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/component/Ubf/Fj/eV;Ljava/lang/String;IZ)V
    .locals 0

    invoke-direct {p0, p1, p2, p3}, Lcom/bytedance/sdk/component/Ubf/Fj/eV;->Fj(Ljava/lang/String;IZ)V

    return-void
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/component/Ubf/Fj/eV;Ljava/lang/String;Ljava/util/List;ZIILjava/lang/String;)V
    .locals 0

    invoke-direct/range {p0 .. p6}, Lcom/bytedance/sdk/component/Ubf/Fj/eV;->Fj(Ljava/lang/String;Ljava/util/List;ZIILjava/lang/String;)V

    return-void
.end method

.method private Fj(Ljava/lang/String;IZ)V
    .locals 1

    if-nez p2, :cond_0

    invoke-static {p1}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/ex/Fj;->Fj(Ljava/lang/String;)V

    return-void

    :cond_0
    const/4 v0, 0x1

    if-ne p2, v0, :cond_1

    invoke-static {p1, p3}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/ex/ex;->Fj(Ljava/lang/String;Z)V

    :cond_1
    return-void
.end method

.method private Fj(Ljava/lang/String;Ljava/util/List;ZIILjava/lang/String;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;ZII",
            "Ljava/lang/String;",
            ")V"
        }
    .end annotation

    if-nez p4, :cond_0

    invoke-static {p1, p2, p3}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/ex/Fj;->Fj(Ljava/lang/String;Ljava/util/List;Z)V

    return-void

    :cond_0
    const/4 v0, 0x1

    if-ne p4, v0, :cond_1

    invoke-static {p1, p2, p3, p5, p6}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/ex/ex;->Fj(Ljava/lang/String;Ljava/util/List;ZILjava/lang/String;)V

    :cond_1
    return-void
.end method

.method private Fj(Landroid/content/Context;Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;)Z
    .locals 3

    if-eqz p1, :cond_3

    if-nez p2, :cond_0

    goto :goto_0

    :cond_0
    invoke-interface {p2}, Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;->WR()I

    move-result v0

    const/4 v1, 0x2

    const/4 v2, 0x1

    if-ne v0, v1, :cond_1

    return v2

    :cond_1
    invoke-interface {p2}, Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;->WR()I

    move-result v0

    if-ne v0, v2, :cond_2

    invoke-interface {p2}, Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;->Tc()Z

    move-result p1

    return p1

    :cond_2
    :try_start_0
    invoke-static {p1}, Lcom/bytedance/sdk/component/utils/rS;->Fj(Landroid/content/Context;)Z

    move-result p1
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    return p1

    :catchall_0
    move-exception p1

    invoke-virtual {p1}, Ljava/lang/Throwable;->getMessage()Ljava/lang/String;

    return v2

    :cond_3
    :goto_0
    const/4 p1, 0x0

    return p1
.end method

.method private ex(I)V
    .locals 1

    if-nez p1, :cond_0

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/ex/Fj;->ex()V

    return-void

    :cond_0
    const/4 v0, 0x1

    if-ne p1, v0, :cond_1

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/ex/ex;->ex()V

    :cond_1
    return-void
.end method

.method private ex(Lcom/bytedance/sdk/component/Ubf/Fj/Fj;Landroid/content/Context;)V
    .locals 1

    const-string v0, "context == null"

    invoke-static {p2, v0}, Lcom/bytedance/sdk/component/Ubf/Fj/hjc;->Fj(Ljava/lang/Object;Ljava/lang/String;)V

    const-string p2, "AdLogConfig == null"

    invoke-static {p1, p2}, Lcom/bytedance/sdk/component/Ubf/Fj/hjc;->Fj(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj;->eV()Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;

    move-result-object p1

    const-string p2, "AdLogDepend ==null"

    invoke-static {p1, p2}, Lcom/bytedance/sdk/component/Ubf/Fj/hjc;->Fj(Ljava/lang/Object;Ljava/lang/String;)V

    return-void
.end method

.method private ex(Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;)V
    .locals 4

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->svN()Lcom/bytedance/sdk/component/Ubf/Fj/BcC;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->JU()Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;

    move-result-object v0

    if-eqz p1, :cond_4

    if-eqz v0, :cond_4

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->svN()Lcom/bytedance/sdk/component/Ubf/Fj/BcC;

    move-result-object v1

    invoke-virtual {v1}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->WR()Landroid/content/Context;

    move-result-object v1

    if-eqz v1, :cond_4

    invoke-interface {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;->eV()Ljava/util/concurrent/Executor;

    move-result-object v1

    if-nez v1, :cond_0

    goto :goto_0

    :cond_0
    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->svN()Lcom/bytedance/sdk/component/Ubf/Fj/BcC;

    move-result-object v1

    invoke-virtual {v1}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->ex()Z

    move-result v1

    if-eqz v1, :cond_3

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->svN()Lcom/bytedance/sdk/component/Ubf/Fj/BcC;

    move-result-object v1

    invoke-virtual {v1}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->WR()Landroid/content/Context;

    move-result-object v1

    invoke-direct {p0, v1, v0}, Lcom/bytedance/sdk/component/Ubf/Fj/eV;->Fj(Landroid/content/Context;Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;)Z

    move-result v1

    if-eqz v1, :cond_1

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->svN()Lcom/bytedance/sdk/component/Ubf/Fj/BcC;

    move-result-object v0

    invoke-virtual {v0, p1}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->Fj(Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;)V

    return-void

    :cond_1
    invoke-direct {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/eV;->hjc()Z

    invoke-direct {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/eV;->hjc()Z

    move-result v1

    if-eqz v1, :cond_2

    invoke-interface {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;->eV()Ljava/util/concurrent/Executor;

    move-result-object v1

    new-instance v2, Lcom/bytedance/sdk/component/Ubf/Fj/eV$4;

    const-string v3, "dispatchEvent"

    invoke-direct {v2, p0, v3, p1, v0}, Lcom/bytedance/sdk/component/Ubf/Fj/eV$4;-><init>(Lcom/bytedance/sdk/component/Ubf/Fj/eV;Ljava/lang/String;Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;)V

    invoke-interface {v1, v2}, Ljava/util/concurrent/Executor;->execute(Ljava/lang/Runnable;)V

    return-void

    :cond_2
    invoke-interface {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;->WR()I

    move-result v0

    invoke-direct {p0, p1, v0}, Lcom/bytedance/sdk/component/Ubf/Fj/eV;->Fj(Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;I)V

    return-void

    :cond_3
    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->svN()Lcom/bytedance/sdk/component/Ubf/Fj/BcC;

    move-result-object v0

    invoke-virtual {v0, p1}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->Fj(Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;)V

    :cond_4
    :goto_0
    return-void
.end method

.method public static synthetic ex(Lcom/bytedance/sdk/component/Ubf/Fj/eV;I)V
    .locals 0

    invoke-direct {p0, p1}, Lcom/bytedance/sdk/component/Ubf/Fj/eV;->ex(I)V

    return-void
.end method

.method private hjc()Z
    .locals 2

    invoke-static {}, Ljava/lang/Thread;->currentThread()Ljava/lang/Thread;

    move-result-object v0

    invoke-static {}, Landroid/os/Looper;->getMainLooper()Landroid/os/Looper;

    move-result-object v1

    invoke-virtual {v1}, Landroid/os/Looper;->getThread()Ljava/lang/Thread;

    move-result-object v1

    if-ne v0, v1, :cond_0

    const/4 v0, 0x1

    return v0

    :cond_0
    const/4 v0, 0x0

    return v0
.end method


# virtual methods
.method public Fj()V
    .locals 4

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->svN()Lcom/bytedance/sdk/component/Ubf/Fj/BcC;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->JU()Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;

    move-result-object v0

    if-eqz v0, :cond_4

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->svN()Lcom/bytedance/sdk/component/Ubf/Fj/BcC;

    move-result-object v1

    invoke-virtual {v1}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->WR()Landroid/content/Context;

    move-result-object v1

    if-eqz v1, :cond_4

    invoke-interface {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;->eV()Ljava/util/concurrent/Executor;

    move-result-object v1

    if-nez v1, :cond_0

    goto :goto_0

    :cond_0
    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->svN()Lcom/bytedance/sdk/component/Ubf/Fj/BcC;

    move-result-object v1

    invoke-virtual {v1}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->ex()Z

    move-result v1

    if-eqz v1, :cond_3

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->svN()Lcom/bytedance/sdk/component/Ubf/Fj/BcC;

    move-result-object v1

    invoke-virtual {v1}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->WR()Landroid/content/Context;

    move-result-object v1

    invoke-direct {p0, v1, v0}, Lcom/bytedance/sdk/component/Ubf/Fj/eV;->Fj(Landroid/content/Context;Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;)Z

    move-result v1

    if-eqz v1, :cond_1

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->svN()Lcom/bytedance/sdk/component/Ubf/Fj/BcC;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->mSE()V

    return-void

    :cond_1
    invoke-direct {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/eV;->hjc()Z

    move-result v1

    if-eqz v1, :cond_2

    invoke-interface {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;->eV()Ljava/util/concurrent/Executor;

    move-result-object v1

    new-instance v2, Lcom/bytedance/sdk/component/Ubf/Fj/eV$2;

    const-string v3, "start"

    invoke-direct {v2, p0, v3, v0}, Lcom/bytedance/sdk/component/Ubf/Fj/eV$2;-><init>(Lcom/bytedance/sdk/component/Ubf/Fj/eV;Ljava/lang/String;Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;)V

    invoke-interface {v1, v2}, Ljava/util/concurrent/Executor;->execute(Ljava/lang/Runnable;)V

    return-void

    :cond_2
    invoke-interface {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;->WR()I

    move-result v0

    invoke-direct {p0, v0}, Lcom/bytedance/sdk/component/Ubf/Fj/eV;->Fj(I)V

    return-void

    :cond_3
    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->svN()Lcom/bytedance/sdk/component/Ubf/Fj/BcC;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->mSE()V

    :cond_4
    :goto_0
    return-void
.end method

.method public Fj(Lcom/bytedance/sdk/component/Ubf/Fj/Fj;Landroid/content/Context;)V
    .locals 2

    invoke-direct {p0, p1, p2}, Lcom/bytedance/sdk/component/Ubf/Fj/eV;->ex(Lcom/bytedance/sdk/component/Ubf/Fj/Fj;Landroid/content/Context;)V

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->svN()Lcom/bytedance/sdk/component/Ubf/Fj/BcC;

    move-result-object v0

    invoke-virtual {v0, p2}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->Fj(Landroid/content/Context;)V

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->svN()Lcom/bytedance/sdk/component/Ubf/Fj/BcC;

    move-result-object p2

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj;->Ko()Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc;

    move-result-object v0

    invoke-virtual {p2, v0}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->Fj(Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc;)V

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->svN()Lcom/bytedance/sdk/component/Ubf/Fj/BcC;

    move-result-object p2

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj;->svN()Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

    move-result-object v0

    invoke-virtual {p2, v0}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->ex(Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;)V

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->svN()Lcom/bytedance/sdk/component/Ubf/Fj/BcC;

    move-result-object p2

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj;->BcC()Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

    move-result-object v0

    invoke-virtual {p2, v0}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->hjc(Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;)V

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->svN()Lcom/bytedance/sdk/component/Ubf/Fj/BcC;

    move-result-object p2

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj;->ex()Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

    move-result-object v0

    invoke-virtual {p2, v0}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->Fj(Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;)V

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->svN()Lcom/bytedance/sdk/component/Ubf/Fj/BcC;

    move-result-object p2

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj;->mSE()Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

    move-result-object v0

    invoke-virtual {p2, v0}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->eV(Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;)V

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->svN()Lcom/bytedance/sdk/component/Ubf/Fj/BcC;

    move-result-object p2

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj;->WR()Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;

    move-result-object v0

    invoke-virtual {p2, v0}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->Ubf(Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex/Fj;)V

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->svN()Lcom/bytedance/sdk/component/Ubf/Fj/BcC;

    move-result-object p2

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj;->Fj()Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Ubf;

    move-result-object v0

    if-nez v0, :cond_0

    sget-object v0, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Ubf;->Fj:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Fj/Ubf;

    goto :goto_0

    :cond_0
    invoke-virtual {p1}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj;->Fj()Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Ubf;

    move-result-object v0

    :goto_0
    invoke-virtual {p2, v0}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->Fj(Lcom/bytedance/sdk/component/Ubf/Fj/Fj/Ubf;)V

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->svN()Lcom/bytedance/sdk/component/Ubf/Fj/BcC;

    move-result-object p2

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj;->rAx()Z

    move-result v0

    invoke-virtual {p2, v0}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->ex(Z)V

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->svN()Lcom/bytedance/sdk/component/Ubf/Fj/BcC;

    move-result-object p2

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj;->eV()Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;

    move-result-object v0

    invoke-virtual {p2, v0}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->Fj(Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;)V

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->svN()Lcom/bytedance/sdk/component/Ubf/Fj/BcC;

    move-result-object p2

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj;->hjc()Z

    move-result v0

    invoke-virtual {p2, v0}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->Fj(Z)V

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->svN()Lcom/bytedance/sdk/component/Ubf/Fj/BcC;

    move-result-object p2

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj;->Ubf()J

    move-result-wide v0

    invoke-virtual {p2, v0, v1}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->Fj(J)V

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj;->dG()I

    move-result p2

    invoke-static {p2}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->Fj(I)V

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj;->UYd()I

    move-result p2

    invoke-static {p2}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->ex(I)V

    invoke-direct {p0, p1}, Lcom/bytedance/sdk/component/Ubf/Fj/eV;->Fj(Lcom/bytedance/sdk/component/Ubf/Fj/Fj;)V

    return-void
.end method

.method public Fj(Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;)V
    .locals 0

    invoke-direct {p0, p1}, Lcom/bytedance/sdk/component/Ubf/Fj/eV;->ex(Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;)V

    return-void
.end method

.method public Fj(Ljava/lang/String;Ljava/util/List;ZLjava/util/Map;ILjava/lang/String;)V
    .locals 12
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;Z",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ">;I",
            "Ljava/lang/String;",
            ")V"
        }
    .end annotation

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->svN()Lcom/bytedance/sdk/component/Ubf/Fj/BcC;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->JU()Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;

    move-result-object v6

    if-eqz v6, :cond_0

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->svN()Lcom/bytedance/sdk/component/Ubf/Fj/BcC;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->WR()Landroid/content/Context;

    move-result-object v0

    if-eqz v0, :cond_0

    invoke-interface {v6}, Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;->eV()Ljava/util/concurrent/Executor;

    move-result-object v0

    if-nez v0, :cond_1

    :cond_0
    move-object v9, p0

    goto/16 :goto_1

    :cond_1
    invoke-interface {v6}, Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;->BcC()Z

    move-result v0

    if-nez v0, :cond_2

    return-void

    :cond_2
    invoke-interface {v6}, Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;->WR()I

    move-result v0

    const/4 v1, 0x1

    if-ne v0, v1, :cond_4

    if-eqz p2, :cond_3

    invoke-interface {p2}, Ljava/util/List;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_6

    :cond_3
    return-void

    :cond_4
    invoke-interface {v6}, Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;->WR()I

    move-result v0

    if-nez v0, :cond_6

    invoke-static {p1}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    if-nez v0, :cond_5

    if-eqz p2, :cond_5

    invoke-interface {p2}, Ljava/util/List;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_6

    :cond_5
    return-void

    :cond_6
    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->svN()Lcom/bytedance/sdk/component/Ubf/Fj/BcC;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->ex()Z

    move-result v0

    if-eqz v0, :cond_9

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->svN()Lcom/bytedance/sdk/component/Ubf/Fj/BcC;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->WR()Landroid/content/Context;

    move-result-object v0

    move-object v9, p0

    invoke-direct {p0, v0, v6}, Lcom/bytedance/sdk/component/Ubf/Fj/eV;->Fj(Landroid/content/Context;Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;)Z

    move-result v0

    if-eqz v0, :cond_7

    goto :goto_0

    :cond_7
    invoke-direct {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/eV;->hjc()Z

    move-result v0

    if-eqz v0, :cond_8

    invoke-interface {v6}, Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;->eV()Ljava/util/concurrent/Executor;

    move-result-object v10

    new-instance v11, Lcom/bytedance/sdk/component/Ubf/Fj/eV$5;

    const-string v2, "trackFailed"

    move-object v0, v11

    move-object v1, p0

    move-object v3, p1

    move-object v4, p2

    move v5, p3

    move/from16 v7, p5

    move-object/from16 v8, p6

    invoke-direct/range {v0 .. v8}, Lcom/bytedance/sdk/component/Ubf/Fj/eV$5;-><init>(Lcom/bytedance/sdk/component/Ubf/Fj/eV;Ljava/lang/String;Ljava/lang/String;Ljava/util/List;ZLcom/bytedance/sdk/component/Ubf/Fj/Ubf;ILjava/lang/String;)V

    invoke-interface {v10, v11}, Ljava/util/concurrent/Executor;->execute(Ljava/lang/Runnable;)V

    return-void

    :cond_8
    invoke-interface {v6}, Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;->WR()I

    move-result v4

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    move v3, p3

    move/from16 v5, p5

    move-object/from16 v6, p6

    invoke-direct/range {v0 .. v6}, Lcom/bytedance/sdk/component/Ubf/Fj/eV;->Fj(Ljava/lang/String;Ljava/util/List;ZIILjava/lang/String;)V

    return-void

    :cond_9
    move-object v9, p0

    :goto_0
    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->svN()Lcom/bytedance/sdk/component/Ubf/Fj/BcC;

    move-result-object v0

    move-object v1, p1

    move-object v2, p2

    move v3, p3

    move-object/from16 v4, p4

    move/from16 v5, p5

    move-object/from16 v6, p6

    invoke-virtual/range {v0 .. v6}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->Fj(Ljava/lang/String;Ljava/util/List;ZLjava/util/Map;ILjava/lang/String;)V

    :goto_1
    return-void
.end method

.method public Fj(Ljava/lang/String;Z)V
    .locals 8

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->svN()Lcom/bytedance/sdk/component/Ubf/Fj/BcC;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->JU()Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;

    move-result-object v5

    if-eqz v5, :cond_6

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->svN()Lcom/bytedance/sdk/component/Ubf/Fj/BcC;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->WR()Landroid/content/Context;

    move-result-object v0

    if-eqz v0, :cond_6

    invoke-interface {v5}, Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;->eV()Ljava/util/concurrent/Executor;

    move-result-object v0

    if-nez v0, :cond_0

    goto :goto_1

    :cond_0
    invoke-interface {v5}, Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;->BcC()Z

    move-result v0

    if-nez v0, :cond_1

    return-void

    :cond_1
    invoke-interface {v5}, Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;->WR()I

    move-result v0

    if-nez v0, :cond_2

    invoke-static {p1}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    if-eqz v0, :cond_2

    return-void

    :cond_2
    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->svN()Lcom/bytedance/sdk/component/Ubf/Fj/BcC;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->ex()Z

    move-result v0

    if-eqz v0, :cond_5

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->svN()Lcom/bytedance/sdk/component/Ubf/Fj/BcC;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->WR()Landroid/content/Context;

    move-result-object v0

    invoke-direct {p0, v0, v5}, Lcom/bytedance/sdk/component/Ubf/Fj/eV;->Fj(Landroid/content/Context;Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;)Z

    move-result v0

    if-eqz v0, :cond_3

    goto :goto_0

    :cond_3
    invoke-direct {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/eV;->hjc()Z

    move-result v0

    if-eqz v0, :cond_4

    invoke-interface {v5}, Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;->eV()Ljava/util/concurrent/Executor;

    move-result-object v0

    new-instance v7, Lcom/bytedance/sdk/component/Ubf/Fj/eV$6;

    const-string v3, "trackFailed"

    move-object v1, v7

    move-object v2, p0

    move-object v4, p1

    move v6, p2

    invoke-direct/range {v1 .. v6}, Lcom/bytedance/sdk/component/Ubf/Fj/eV$6;-><init>(Lcom/bytedance/sdk/component/Ubf/Fj/eV;Ljava/lang/String;Ljava/lang/String;Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;Z)V

    invoke-interface {v0, v7}, Ljava/util/concurrent/Executor;->execute(Ljava/lang/Runnable;)V

    return-void

    :cond_4
    invoke-interface {v5}, Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;->WR()I

    move-result v0

    invoke-direct {p0, p1, v0, p2}, Lcom/bytedance/sdk/component/Ubf/Fj/eV;->Fj(Ljava/lang/String;IZ)V

    return-void

    :cond_5
    :goto_0
    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->svN()Lcom/bytedance/sdk/component/Ubf/Fj/BcC;

    move-result-object v0

    invoke-virtual {v0, p1, p2}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->Fj(Ljava/lang/String;Z)V

    :cond_6
    :goto_1
    return-void
.end method

.method public Fj(Z)V
    .locals 1

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->svN()Lcom/bytedance/sdk/component/Ubf/Fj/BcC;

    move-result-object v0

    invoke-virtual {v0, p1}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->Fj(Z)V

    return-void
.end method

.method public ex()V
    .locals 4

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->svN()Lcom/bytedance/sdk/component/Ubf/Fj/BcC;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->JU()Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;

    move-result-object v0

    if-eqz v0, :cond_4

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->svN()Lcom/bytedance/sdk/component/Ubf/Fj/BcC;

    move-result-object v1

    invoke-virtual {v1}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->WR()Landroid/content/Context;

    move-result-object v1

    if-eqz v1, :cond_4

    invoke-interface {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;->eV()Ljava/util/concurrent/Executor;

    move-result-object v1

    if-nez v1, :cond_0

    goto :goto_0

    :cond_0
    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->svN()Lcom/bytedance/sdk/component/Ubf/Fj/BcC;

    move-result-object v1

    invoke-virtual {v1}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->ex()Z

    move-result v1

    if-eqz v1, :cond_3

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->svN()Lcom/bytedance/sdk/component/Ubf/Fj/BcC;

    move-result-object v1

    invoke-virtual {v1}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->WR()Landroid/content/Context;

    move-result-object v1

    invoke-direct {p0, v1, v0}, Lcom/bytedance/sdk/component/Ubf/Fj/eV;->Fj(Landroid/content/Context;Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;)Z

    move-result v1

    if-eqz v1, :cond_1

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->svN()Lcom/bytedance/sdk/component/Ubf/Fj/BcC;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->rAx()V

    return-void

    :cond_1
    invoke-direct {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/eV;->hjc()Z

    move-result v1

    if-eqz v1, :cond_2

    invoke-interface {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;->eV()Ljava/util/concurrent/Executor;

    move-result-object v1

    new-instance v2, Lcom/bytedance/sdk/component/Ubf/Fj/eV$3;

    const-string v3, "stop"

    invoke-direct {v2, p0, v3, v0}, Lcom/bytedance/sdk/component/Ubf/Fj/eV$3;-><init>(Lcom/bytedance/sdk/component/Ubf/Fj/eV;Ljava/lang/String;Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;)V

    invoke-interface {v1, v2}, Ljava/util/concurrent/Executor;->execute(Ljava/lang/Runnable;)V

    return-void

    :cond_2
    invoke-interface {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;->WR()I

    move-result v0

    invoke-direct {p0, v0}, Lcom/bytedance/sdk/component/Ubf/Fj/eV;->ex(I)V

    return-void

    :cond_3
    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->svN()Lcom/bytedance/sdk/component/Ubf/Fj/BcC;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->rAx()V

    :cond_4
    :goto_0
    return-void
.end method
