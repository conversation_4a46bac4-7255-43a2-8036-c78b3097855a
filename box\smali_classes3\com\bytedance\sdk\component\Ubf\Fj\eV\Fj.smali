.class public interface abstract Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;
.super Ljava/lang/Object;


# virtual methods
.method public abstract BcC()J
.end method

.method public abstract Fj()Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj/ex;
.end method

.method public abstract Fj(B)V
.end method

.method public abstract Fj(I)V
.end method

.method public abstract Fj(J)V
.end method

.method public abstract Fj(Ljava/lang/String;)V
.end method

.method public abstract Fj(Lorg/json/JSONObject;)V
.end method

.method public abstract Ko()I
.end method

.method public abstract Ubf()B
.end method

.method public abstract WR()Ljava/lang/String;
.end method

.method public abstract eV()B
.end method

.method public abstract ex()B
.end method

.method public abstract ex(B)V
.end method

.method public abstract ex(J)V
.end method

.method public abstract ex(Ljava/lang/String;)V
.end method

.method public abstract hjc()Ljava/lang/String;
.end method

.method public abstract hjc(J)V
.end method

.method public abstract mSE()J
.end method

.method public abstract svN()Lorg/json/JSONObject;
.end method
