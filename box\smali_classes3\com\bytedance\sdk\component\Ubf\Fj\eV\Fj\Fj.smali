.class public Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj/Fj;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;


# instance fields
.field private BcC:Ljava/lang/String;

.field protected Fj:Lorg/json/JSONObject;

.field private Ko:B

.field private UYd:I

.field private Ubf:J

.field private WR:J

.field private eV:B

.field private ex:Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj/ex;

.field private hjc:B

.field private mSE:Ljava/lang/String;

.field private rAx:Ljava/lang/String;

.field private svN:J


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public constructor <init>(Ljava/lang/String;Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj/ex;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj/Fj;->mSE:Ljava/lang/String;

    iput-object p2, p0, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj/Fj;->ex:Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj/ex;

    return-void
.end method

.method public constructor <init>(Ljava/lang/String;Lorg/json/JSONObject;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj/Fj;->mSE:Ljava/lang/String;

    iput-object p2, p0, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj/Fj;->Fj:Lorg/json/JSONObject;

    return-void
.end method

.method public static hjc(Ljava/lang/String;)Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;
    .locals 3

    :try_start_0
    new-instance v0, Lorg/json/JSONObject;

    invoke-direct {v0, p0}, Lorg/json/JSONObject;-><init>(Ljava/lang/String;)V

    const-string p0, "type"

    invoke-virtual {v0, p0}, Lorg/json/JSONObject;->optInt(Ljava/lang/String;)I

    move-result p0

    const-string v1, "priority"

    invoke-virtual {v0, v1}, Lorg/json/JSONObject;->optInt(Ljava/lang/String;)I

    move-result v1

    new-instance v2, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj/Fj;

    invoke-direct {v2}, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj/Fj;-><init>()V

    int-to-byte p0, p0

    invoke-interface {v2, p0}, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;->Fj(B)V

    int-to-byte p0, v1

    invoke-interface {v2, p0}, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;->ex(B)V

    const-string p0, "event"

    invoke-virtual {v0, p0}, Lorg/json/JSONObject;->optJSONObject(Ljava/lang/String;)Lorg/json/JSONObject;

    move-result-object p0

    invoke-interface {v2, p0}, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;->Fj(Lorg/json/JSONObject;)V

    const-string p0, "localId"

    invoke-virtual {v0, p0}, Lorg/json/JSONObject;->optString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p0

    invoke-interface {v2, p0}, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;->Fj(Ljava/lang/String;)V

    const-string p0, "genTime"

    invoke-virtual {v0, p0}, Lorg/json/JSONObject;->optString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p0

    invoke-interface {v2, p0}, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;->ex(Ljava/lang/String;)V

    const-string p0, "channel"

    invoke-virtual {v0, p0}, Lorg/json/JSONObject;->optInt(Ljava/lang/String;)I

    move-result p0

    invoke-interface {v2, p0}, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;->Fj(I)V
    :try_end_0
    .catch Lorg/json/JSONException; {:try_start_0 .. :try_end_0} :catch_0

    return-object v2

    :catch_0
    const/4 p0, 0x0

    return-object p0
.end method


# virtual methods
.method public BcC()J
    .locals 2

    iget-wide v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj/Fj;->Ubf:J

    return-wide v0
.end method

.method public Fj()Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj/ex;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj/Fj;->ex:Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj/ex;

    return-object v0
.end method

.method public Fj(B)V
    .locals 0

    iput-byte p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj/Fj;->hjc:B

    return-void
.end method

.method public Fj(I)V
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj/Fj;->UYd:I

    return-void
.end method

.method public Fj(J)V
    .locals 0

    iput-wide p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj/Fj;->Ubf:J

    return-void
.end method

.method public Fj(Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj/Fj;->mSE:Ljava/lang/String;

    return-void
.end method

.method public Fj(Lorg/json/JSONObject;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj/Fj;->Fj:Lorg/json/JSONObject;

    return-void
.end method

.method public Ko()I
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj/Fj;->UYd:I

    return v0
.end method

.method public UYd()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj/Fj;->BcC:Ljava/lang/String;

    return-object v0
.end method

.method public Ubf()B
    .locals 1

    iget-byte v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj/Fj;->eV:B

    return v0
.end method

.method public WR()Ljava/lang/String;
    .locals 3

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj/Fj;->mSE:Ljava/lang/String;

    invoke-static {v0}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    if-nez v0, :cond_0

    new-instance v0, Lorg/json/JSONObject;

    invoke-direct {v0}, Lorg/json/JSONObject;-><init>()V

    :try_start_0
    const-string v1, "localId"

    iget-object v2, p0, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj/Fj;->mSE:Ljava/lang/String;

    invoke-virtual {v0, v1, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    const-string v1, "event"

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj/Fj;->svN()Lorg/json/JSONObject;

    move-result-object v2

    invoke-virtual {v0, v1, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    const-string v1, "genTime"

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj/Fj;->UYd()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v0, v1, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    const-string v1, "priority"

    iget-byte v2, p0, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj/Fj;->eV:B

    invoke-virtual {v0, v1, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;I)Lorg/json/JSONObject;

    const-string v1, "type"

    iget-byte v2, p0, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj/Fj;->hjc:B

    invoke-virtual {v0, v1, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;I)Lorg/json/JSONObject;

    const-string v1, "channel"

    iget v2, p0, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj/Fj;->UYd:I

    invoke-virtual {v0, v1, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;I)Lorg/json/JSONObject;
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    :catchall_0
    invoke-virtual {v0}, Lorg/json/JSONObject;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0

    :cond_0
    const/4 v0, 0x0

    return-object v0
.end method

.method public eV()B
    .locals 1

    iget-byte v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj/Fj;->hjc:B

    return v0
.end method

.method public ex()B
    .locals 1

    iget-byte v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj/Fj;->Ko:B

    return v0
.end method

.method public ex(B)V
    .locals 0

    iput-byte p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj/Fj;->eV:B

    return-void
.end method

.method public ex(J)V
    .locals 0

    iput-wide p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj/Fj;->WR:J

    return-void
.end method

.method public ex(Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj/Fj;->BcC:Ljava/lang/String;

    return-void
.end method

.method public hjc()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj/Fj;->mSE:Ljava/lang/String;

    return-object v0
.end method

.method public hjc(B)V
    .locals 0

    iput-byte p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj/Fj;->Ko:B

    return-void
.end method

.method public hjc(J)V
    .locals 0

    iput-wide p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj/Fj;->svN:J

    return-void
.end method

.method public mSE()J
    .locals 2

    iget-wide v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj/Fj;->WR:J

    return-wide v0
.end method

.method public rAx()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj/Fj;->rAx:Ljava/lang/String;

    return-object v0
.end method

.method public declared-synchronized svN()Lorg/json/JSONObject;
    .locals 2

    monitor-enter p0

    :try_start_0
    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj/Fj;->Fj:Lorg/json/JSONObject;

    if-nez v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj/Fj;->ex:Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj/ex;

    if-eqz v0, :cond_0

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj/Fj;->rAx()Ljava/lang/String;

    move-result-object v1

    invoke-interface {v0, v1}, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj/ex;->Fj(Ljava/lang/String;)Lorg/json/JSONObject;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj/Fj;->Fj:Lorg/json/JSONObject;

    goto :goto_0

    :catchall_0
    move-exception v0

    goto :goto_1

    :cond_0
    :goto_0
    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj/Fj;->Fj:Lorg/json/JSONObject;
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    monitor-exit p0

    return-object v0

    :goto_1
    monitor-exit p0

    throw v0
.end method
