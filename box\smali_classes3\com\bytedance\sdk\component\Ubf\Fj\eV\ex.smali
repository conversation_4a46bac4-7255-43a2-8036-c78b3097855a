.class public Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;


# instance fields
.field private Fj:I

.field private ex:B


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public BcC()J
    .locals 2

    const-wide/16 v0, 0x0

    return-wide v0
.end method

.method public Fj()Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj/ex;
    .locals 1

    const/4 v0, 0x0

    return-object v0
.end method

.method public Fj(B)V
    .locals 0

    iput-byte p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex;->ex:B

    return-void
.end method

.method public Fj(I)V
    .locals 0

    return-void
.end method

.method public Fj(J)V
    .locals 0

    return-void
.end method

.method public Fj(Ljava/lang/String;)V
    .locals 0

    return-void
.end method

.method public Fj(Lorg/json/JSONObject;)V
    .locals 0

    return-void
.end method

.method public Ko()I
    .locals 1

    const/4 v0, 0x0

    return v0
.end method

.method public Ubf()B
    .locals 1

    const/4 v0, 0x0

    return v0
.end method

.method public WR()Ljava/lang/String;
    .locals 1

    const/4 v0, 0x0

    return-object v0
.end method

.method public eV()B
    .locals 1

    iget-byte v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex;->ex:B

    return v0
.end method

.method public ex()B
    .locals 1

    const/4 v0, 0x0

    return v0
.end method

.method public ex(B)V
    .locals 0

    return-void
.end method

.method public ex(I)V
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex;->Fj:I

    return-void
.end method

.method public ex(J)V
    .locals 0

    return-void
.end method

.method public ex(Ljava/lang/String;)V
    .locals 0

    return-void
.end method

.method public hjc()Ljava/lang/String;
    .locals 1

    const/4 v0, 0x0

    return-object v0
.end method

.method public hjc(J)V
    .locals 0

    return-void
.end method

.method public mSE()J
    .locals 2

    const-wide/16 v0, 0x0

    return-wide v0
.end method

.method public rAx()I
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex;->Fj:I

    return v0
.end method

.method public svN()Lorg/json/JSONObject;
    .locals 1

    const/4 v0, 0x0

    return-object v0
.end method
