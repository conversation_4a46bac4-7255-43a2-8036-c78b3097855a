.class public Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;
.super Ljava/lang/Object;


# instance fields
.field private Af:Ljava/util/concurrent/atomic/AtomicLong;

.field private At:Ljava/util/concurrent/atomic/AtomicLong;

.field private BcC:Ljava/util/concurrent/atomic/AtomicLong;

.field private Bzy:Ljava/util/concurrent/atomic/AtomicLong;

.field private Fj:Ljava/util/concurrent/atomic/AtomicLong;

.field private HY:Ljava/util/concurrent/atomic/AtomicLong;

.field private JU:Ljava/util/concurrent/atomic/AtomicLong;

.field private JW:Ljava/util/concurrent/atomic/AtomicLong;

.field private JZ:Ljava/util/concurrent/atomic/AtomicLong;

.field private Jq:Ljava/util/concurrent/atomic/AtomicLong;

.field private KZ:Ljava/util/concurrent/atomic/AtomicLong;

.field private Kk:Ljava/util/concurrent/atomic/AtomicLong;

.field private Ko:Ljava/util/concurrent/atomic/AtomicLong;

.field private Moo:Ljava/util/concurrent/atomic/AtomicLong;

.field private OK:Ljava/util/concurrent/atomic/AtomicLong;

.field private Obv:Ljava/util/concurrent/atomic/AtomicLong;

.field private PpV:Ljava/util/concurrent/atomic/AtomicLong;

.field private Ql:Ljava/util/concurrent/atomic/AtomicLong;

.field private Tc:Ljava/util/concurrent/atomic/AtomicLong;

.field private UYd:Ljava/util/concurrent/atomic/AtomicLong;

.field private Ubf:Ljava/util/concurrent/atomic/AtomicLong;

.field private Vq:Ljava/util/concurrent/atomic/AtomicLong;

.field private WR:Ljava/util/concurrent/atomic/AtomicLong;

.field private YH:Ljava/util/concurrent/atomic/AtomicLong;

.field private cB:Ljava/util/concurrent/atomic/AtomicLong;

.field private cs:Ljava/util/concurrent/atomic/AtomicLong;

.field private dG:Ljava/util/concurrent/atomic/AtomicLong;

.field private eV:Ljava/util/concurrent/atomic/AtomicLong;

.field private efV:Ljava/util/concurrent/atomic/AtomicLong;

.field private eh:Ljava/util/concurrent/atomic/AtomicLong;

.field private ei:Ljava/util/concurrent/atomic/AtomicLong;

.field private ex:Ljava/util/concurrent/atomic/AtomicLong;

.field private fj:Ljava/util/concurrent/atomic/AtomicLong;

.field private flF:Ljava/util/concurrent/atomic/AtomicLong;

.field private gXF:Ljava/util/concurrent/atomic/AtomicLong;

.field private gci:Ljava/util/concurrent/atomic/AtomicLong;

.field private haP:Ljava/util/concurrent/atomic/AtomicLong;

.field private hjc:Ljava/util/concurrent/atomic/AtomicLong;

.field private iT:Ljava/util/concurrent/atomic/AtomicLong;

.field private kF:Ljava/util/concurrent/atomic/AtomicLong;

.field private ks:Ljava/util/concurrent/atomic/AtomicLong;

.field private lv:Ljava/util/concurrent/atomic/AtomicLong;

.field private mC:Ljava/util/concurrent/atomic/AtomicLong;

.field private mE:Ljava/util/concurrent/atomic/AtomicLong;

.field private mSE:Ljava/util/concurrent/atomic/AtomicLong;

.field private mj:Ljava/util/concurrent/atomic/AtomicLong;

.field private nsB:Ljava/util/concurrent/atomic/AtomicLong;

.field private oX:Ljava/util/concurrent/atomic/AtomicLong;

.field private qPr:Ljava/util/concurrent/atomic/AtomicLong;

.field private qg:Ljava/util/concurrent/atomic/AtomicLong;

.field private rAx:Ljava/util/concurrent/atomic/AtomicLong;

.field private rS:Ljava/util/concurrent/atomic/AtomicLong;

.field private rXP:Ljava/util/concurrent/atomic/AtomicLong;

.field private rf:Ljava/util/concurrent/atomic/AtomicLong;

.field private spi:Ljava/util/concurrent/atomic/AtomicLong;

.field private svN:Ljava/util/concurrent/atomic/AtomicLong;

.field private uM:Ljava/util/concurrent/atomic/AtomicLong;

.field private uy:Ljava/util/concurrent/atomic/AtomicLong;

.field private vYf:Ljava/util/concurrent/atomic/AtomicLong;


# direct methods
.method public constructor <init>()V
    .locals 3

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    new-instance v0, Ljava/util/concurrent/atomic/AtomicLong;

    const-wide/16 v1, 0x0

    invoke-direct {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;-><init>(J)V

    iput-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->Fj:Ljava/util/concurrent/atomic/AtomicLong;

    new-instance v0, Ljava/util/concurrent/atomic/AtomicLong;

    invoke-direct {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;-><init>(J)V

    iput-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->ex:Ljava/util/concurrent/atomic/AtomicLong;

    new-instance v0, Ljava/util/concurrent/atomic/AtomicLong;

    invoke-direct {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;-><init>(J)V

    iput-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->hjc:Ljava/util/concurrent/atomic/AtomicLong;

    new-instance v0, Ljava/util/concurrent/atomic/AtomicLong;

    invoke-direct {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;-><init>(J)V

    iput-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->eV:Ljava/util/concurrent/atomic/AtomicLong;

    new-instance v0, Ljava/util/concurrent/atomic/AtomicLong;

    invoke-direct {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;-><init>(J)V

    iput-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->Ubf:Ljava/util/concurrent/atomic/AtomicLong;

    new-instance v0, Ljava/util/concurrent/atomic/AtomicLong;

    invoke-direct {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;-><init>(J)V

    iput-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->WR:Ljava/util/concurrent/atomic/AtomicLong;

    new-instance v0, Ljava/util/concurrent/atomic/AtomicLong;

    invoke-direct {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;-><init>(J)V

    iput-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->svN:Ljava/util/concurrent/atomic/AtomicLong;

    new-instance v0, Ljava/util/concurrent/atomic/AtomicLong;

    invoke-direct {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;-><init>(J)V

    iput-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->BcC:Ljava/util/concurrent/atomic/AtomicLong;

    new-instance v0, Ljava/util/concurrent/atomic/AtomicLong;

    invoke-direct {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;-><init>(J)V

    iput-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->mSE:Ljava/util/concurrent/atomic/AtomicLong;

    new-instance v0, Ljava/util/concurrent/atomic/AtomicLong;

    invoke-direct {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;-><init>(J)V

    iput-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->Ko:Ljava/util/concurrent/atomic/AtomicLong;

    new-instance v0, Ljava/util/concurrent/atomic/AtomicLong;

    invoke-direct {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;-><init>(J)V

    iput-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->rAx:Ljava/util/concurrent/atomic/AtomicLong;

    new-instance v0, Ljava/util/concurrent/atomic/AtomicLong;

    invoke-direct {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;-><init>(J)V

    iput-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->UYd:Ljava/util/concurrent/atomic/AtomicLong;

    new-instance v0, Ljava/util/concurrent/atomic/AtomicLong;

    invoke-direct {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;-><init>(J)V

    iput-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->dG:Ljava/util/concurrent/atomic/AtomicLong;

    new-instance v0, Ljava/util/concurrent/atomic/AtomicLong;

    invoke-direct {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;-><init>(J)V

    iput-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->Tc:Ljava/util/concurrent/atomic/AtomicLong;

    new-instance v0, Ljava/util/concurrent/atomic/AtomicLong;

    invoke-direct {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;-><init>(J)V

    iput-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->JW:Ljava/util/concurrent/atomic/AtomicLong;

    new-instance v0, Ljava/util/concurrent/atomic/AtomicLong;

    invoke-direct {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;-><init>(J)V

    iput-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->JU:Ljava/util/concurrent/atomic/AtomicLong;

    new-instance v0, Ljava/util/concurrent/atomic/AtomicLong;

    invoke-direct {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;-><init>(J)V

    iput-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->Ql:Ljava/util/concurrent/atomic/AtomicLong;

    new-instance v0, Ljava/util/concurrent/atomic/AtomicLong;

    invoke-direct {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;-><init>(J)V

    iput-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->rS:Ljava/util/concurrent/atomic/AtomicLong;

    new-instance v0, Ljava/util/concurrent/atomic/AtomicLong;

    invoke-direct {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;-><init>(J)V

    iput-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->vYf:Ljava/util/concurrent/atomic/AtomicLong;

    new-instance v0, Ljava/util/concurrent/atomic/AtomicLong;

    invoke-direct {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;-><init>(J)V

    iput-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->mE:Ljava/util/concurrent/atomic/AtomicLong;

    new-instance v0, Ljava/util/concurrent/atomic/AtomicLong;

    invoke-direct {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;-><init>(J)V

    iput-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->Af:Ljava/util/concurrent/atomic/AtomicLong;

    new-instance v0, Ljava/util/concurrent/atomic/AtomicLong;

    invoke-direct {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;-><init>(J)V

    iput-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->mC:Ljava/util/concurrent/atomic/AtomicLong;

    new-instance v0, Ljava/util/concurrent/atomic/AtomicLong;

    invoke-direct {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;-><init>(J)V

    iput-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->cB:Ljava/util/concurrent/atomic/AtomicLong;

    new-instance v0, Ljava/util/concurrent/atomic/AtomicLong;

    invoke-direct {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;-><init>(J)V

    iput-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->nsB:Ljava/util/concurrent/atomic/AtomicLong;

    new-instance v0, Ljava/util/concurrent/atomic/AtomicLong;

    invoke-direct {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;-><init>(J)V

    iput-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->Vq:Ljava/util/concurrent/atomic/AtomicLong;

    new-instance v0, Ljava/util/concurrent/atomic/AtomicLong;

    invoke-direct {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;-><init>(J)V

    iput-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->Moo:Ljava/util/concurrent/atomic/AtomicLong;

    new-instance v0, Ljava/util/concurrent/atomic/AtomicLong;

    invoke-direct {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;-><init>(J)V

    iput-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->rf:Ljava/util/concurrent/atomic/AtomicLong;

    new-instance v0, Ljava/util/concurrent/atomic/AtomicLong;

    invoke-direct {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;-><init>(J)V

    iput-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->uy:Ljava/util/concurrent/atomic/AtomicLong;

    new-instance v0, Ljava/util/concurrent/atomic/AtomicLong;

    invoke-direct {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;-><init>(J)V

    iput-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->lv:Ljava/util/concurrent/atomic/AtomicLong;

    new-instance v0, Ljava/util/concurrent/atomic/AtomicLong;

    invoke-direct {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;-><init>(J)V

    iput-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->rXP:Ljava/util/concurrent/atomic/AtomicLong;

    new-instance v0, Ljava/util/concurrent/atomic/AtomicLong;

    invoke-direct {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;-><init>(J)V

    iput-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->fj:Ljava/util/concurrent/atomic/AtomicLong;

    new-instance v0, Ljava/util/concurrent/atomic/AtomicLong;

    invoke-direct {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;-><init>(J)V

    iput-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->eh:Ljava/util/concurrent/atomic/AtomicLong;

    new-instance v0, Ljava/util/concurrent/atomic/AtomicLong;

    invoke-direct {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;-><init>(J)V

    iput-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->kF:Ljava/util/concurrent/atomic/AtomicLong;

    new-instance v0, Ljava/util/concurrent/atomic/AtomicLong;

    invoke-direct {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;-><init>(J)V

    iput-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->uM:Ljava/util/concurrent/atomic/AtomicLong;

    new-instance v0, Ljava/util/concurrent/atomic/AtomicLong;

    invoke-direct {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;-><init>(J)V

    iput-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->gXF:Ljava/util/concurrent/atomic/AtomicLong;

    new-instance v0, Ljava/util/concurrent/atomic/AtomicLong;

    invoke-direct {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;-><init>(J)V

    iput-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->spi:Ljava/util/concurrent/atomic/AtomicLong;

    new-instance v0, Ljava/util/concurrent/atomic/AtomicLong;

    invoke-direct {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;-><init>(J)V

    iput-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->iT:Ljava/util/concurrent/atomic/AtomicLong;

    new-instance v0, Ljava/util/concurrent/atomic/AtomicLong;

    invoke-direct {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;-><init>(J)V

    iput-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->efV:Ljava/util/concurrent/atomic/AtomicLong;

    new-instance v0, Ljava/util/concurrent/atomic/AtomicLong;

    invoke-direct {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;-><init>(J)V

    iput-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->oX:Ljava/util/concurrent/atomic/AtomicLong;

    new-instance v0, Ljava/util/concurrent/atomic/AtomicLong;

    invoke-direct {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;-><init>(J)V

    iput-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->KZ:Ljava/util/concurrent/atomic/AtomicLong;

    new-instance v0, Ljava/util/concurrent/atomic/AtomicLong;

    invoke-direct {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;-><init>(J)V

    iput-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->mj:Ljava/util/concurrent/atomic/AtomicLong;

    new-instance v0, Ljava/util/concurrent/atomic/AtomicLong;

    invoke-direct {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;-><init>(J)V

    iput-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->PpV:Ljava/util/concurrent/atomic/AtomicLong;

    new-instance v0, Ljava/util/concurrent/atomic/AtomicLong;

    invoke-direct {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;-><init>(J)V

    iput-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->qPr:Ljava/util/concurrent/atomic/AtomicLong;

    new-instance v0, Ljava/util/concurrent/atomic/AtomicLong;

    invoke-direct {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;-><init>(J)V

    iput-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->OK:Ljava/util/concurrent/atomic/AtomicLong;

    new-instance v0, Ljava/util/concurrent/atomic/AtomicLong;

    invoke-direct {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;-><init>(J)V

    iput-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->gci:Ljava/util/concurrent/atomic/AtomicLong;

    new-instance v0, Ljava/util/concurrent/atomic/AtomicLong;

    invoke-direct {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;-><init>(J)V

    iput-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->Bzy:Ljava/util/concurrent/atomic/AtomicLong;

    new-instance v0, Ljava/util/concurrent/atomic/AtomicLong;

    invoke-direct {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;-><init>(J)V

    iput-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->Obv:Ljava/util/concurrent/atomic/AtomicLong;

    new-instance v0, Ljava/util/concurrent/atomic/AtomicLong;

    invoke-direct {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;-><init>(J)V

    iput-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->HY:Ljava/util/concurrent/atomic/AtomicLong;

    new-instance v0, Ljava/util/concurrent/atomic/AtomicLong;

    invoke-direct {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;-><init>(J)V

    iput-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->Kk:Ljava/util/concurrent/atomic/AtomicLong;

    new-instance v0, Ljava/util/concurrent/atomic/AtomicLong;

    invoke-direct {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;-><init>(J)V

    iput-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->cs:Ljava/util/concurrent/atomic/AtomicLong;

    new-instance v0, Ljava/util/concurrent/atomic/AtomicLong;

    invoke-direct {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;-><init>(J)V

    iput-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->YH:Ljava/util/concurrent/atomic/AtomicLong;

    new-instance v0, Ljava/util/concurrent/atomic/AtomicLong;

    invoke-direct {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;-><init>(J)V

    iput-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->At:Ljava/util/concurrent/atomic/AtomicLong;

    new-instance v0, Ljava/util/concurrent/atomic/AtomicLong;

    invoke-direct {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;-><init>(J)V

    iput-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->qg:Ljava/util/concurrent/atomic/AtomicLong;

    new-instance v0, Ljava/util/concurrent/atomic/AtomicLong;

    invoke-direct {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;-><init>(J)V

    iput-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->Jq:Ljava/util/concurrent/atomic/AtomicLong;

    new-instance v0, Ljava/util/concurrent/atomic/AtomicLong;

    invoke-direct {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;-><init>(J)V

    iput-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->haP:Ljava/util/concurrent/atomic/AtomicLong;

    new-instance v0, Ljava/util/concurrent/atomic/AtomicLong;

    invoke-direct {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;-><init>(J)V

    iput-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->flF:Ljava/util/concurrent/atomic/AtomicLong;

    new-instance v0, Ljava/util/concurrent/atomic/AtomicLong;

    invoke-direct {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;-><init>(J)V

    iput-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->ei:Ljava/util/concurrent/atomic/AtomicLong;

    new-instance v0, Ljava/util/concurrent/atomic/AtomicLong;

    invoke-direct {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;-><init>(J)V

    iput-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->ks:Ljava/util/concurrent/atomic/AtomicLong;

    new-instance v0, Ljava/util/concurrent/atomic/AtomicLong;

    invoke-direct {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;-><init>(J)V

    iput-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->JZ:Ljava/util/concurrent/atomic/AtomicLong;

    return-void
.end method


# virtual methods
.method public Af()Ljava/util/concurrent/atomic/AtomicLong;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->mj:Ljava/util/concurrent/atomic/AtomicLong;

    return-object v0
.end method

.method public At()Ljava/util/concurrent/atomic/AtomicLong;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->svN:Ljava/util/concurrent/atomic/AtomicLong;

    return-object v0
.end method

.method public BcC()Ljava/util/concurrent/atomic/AtomicLong;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->At:Ljava/util/concurrent/atomic/AtomicLong;

    return-object v0
.end method

.method public Bzy()Ljava/util/concurrent/atomic/AtomicLong;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->Ko:Ljava/util/concurrent/atomic/AtomicLong;

    return-object v0
.end method

.method public Eev()Ljava/util/concurrent/atomic/AtomicLong;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->rS:Ljava/util/concurrent/atomic/AtomicLong;

    return-object v0
.end method

.method public Fj()Ljava/util/concurrent/atomic/AtomicLong;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->JZ:Ljava/util/concurrent/atomic/AtomicLong;

    return-object v0
.end method

.method public declared-synchronized Fj(J)V
    .locals 1

    monitor-enter p0

    :try_start_0
    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->Fj:Ljava/util/concurrent/atomic/AtomicLong;

    invoke-virtual {v0, p1, p2}, Ljava/util/concurrent/atomic/AtomicLong;->getAndAdd(J)J

    iget-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->ex:Ljava/util/concurrent/atomic/AtomicLong;

    invoke-virtual {p1}, Ljava/util/concurrent/atomic/AtomicLong;->incrementAndGet()J
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    monitor-exit p0

    return-void

    :catchall_0
    move-exception p1

    monitor-exit p0

    throw p1
.end method

.method public HY()Ljava/util/concurrent/atomic/AtomicLong;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->Fj:Ljava/util/concurrent/atomic/AtomicLong;

    return-object v0
.end method

.method public JU()Ljava/util/concurrent/atomic/AtomicLong;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->OK:Ljava/util/concurrent/atomic/AtomicLong;

    return-object v0
.end method

.method public JW()Ljava/util/concurrent/atomic/AtomicLong;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->qPr:Ljava/util/concurrent/atomic/AtomicLong;

    return-object v0
.end method

.method public JZ()Ljava/util/concurrent/atomic/AtomicLong;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->Ql:Ljava/util/concurrent/atomic/AtomicLong;

    return-object v0
.end method

.method public Jq()Ljava/util/concurrent/atomic/AtomicLong;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->rAx:Ljava/util/concurrent/atomic/AtomicLong;

    return-object v0
.end method

.method public KZ()Ljava/util/concurrent/atomic/AtomicLong;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->Af:Ljava/util/concurrent/atomic/AtomicLong;

    return-object v0
.end method

.method public Kk()Ljava/util/concurrent/atomic/AtomicLong;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->hjc:Ljava/util/concurrent/atomic/AtomicLong;

    return-object v0
.end method

.method public Ko()Ljava/util/concurrent/atomic/AtomicLong;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->cs:Ljava/util/concurrent/atomic/AtomicLong;

    return-object v0
.end method

.method public Moo()Ljava/util/concurrent/atomic/AtomicLong;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->iT:Ljava/util/concurrent/atomic/AtomicLong;

    return-object v0
.end method

.method public OK()Ljava/util/concurrent/atomic/AtomicLong;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->eV:Ljava/util/concurrent/atomic/AtomicLong;

    return-object v0
.end method

.method public Obv()Ljava/util/concurrent/atomic/AtomicLong;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->vYf:Ljava/util/concurrent/atomic/AtomicLong;

    return-object v0
.end method

.method public declared-synchronized PpV()V
    .locals 3

    monitor-enter p0

    :try_start_0
    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->Kk()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object v0

    const-wide/16 v1, 0x0

    invoke-virtual {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;->set(J)V

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->ei()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object v0

    invoke-virtual {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;->set(J)V

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->ks()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object v0

    invoke-virtual {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;->set(J)V

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->flF()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object v0

    invoke-virtual {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;->set(J)V

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->Eev()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object v0

    invoke-virtual {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;->set(J)V

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->JZ()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object v0

    invoke-virtual {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;->set(J)V

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->haP()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object v0

    invoke-virtual {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;->set(J)V

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->Jq()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object v0

    invoke-virtual {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;->set(J)V

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->At()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object v0

    invoke-virtual {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;->set(J)V

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->HY()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object v0

    invoke-virtual {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;->set(J)V

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->cs()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object v0

    invoke-virtual {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;->set(J)V

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->qg()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object v0

    invoke-virtual {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;->set(J)V

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->YH()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object v0

    invoke-virtual {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;->set(J)V

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->Bzy()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object v0

    invoke-virtual {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;->set(J)V

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->OK()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object v0

    invoke-virtual {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;->set(J)V

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->gci()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object v0

    invoke-virtual {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;->set(J)V

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->qPr()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object v0

    invoke-virtual {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;->set(J)V

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->Obv()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object v0

    invoke-virtual {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;->set(J)V

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->mj()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object v0

    invoke-virtual {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;->set(J)V

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->KZ()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object v0

    invoke-virtual {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;->set(J)V

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->efV()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object v0

    invoke-virtual {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;->set(J)V

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->iT()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object v0

    invoke-virtual {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;->set(J)V

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->spi()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object v0

    invoke-virtual {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;->set(J)V

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->gXF()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object v0

    invoke-virtual {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;->set(J)V

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->uM()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object v0

    invoke-virtual {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;->set(J)V

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->kF()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object v0

    invoke-virtual {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;->set(J)V

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->fj()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object v0

    invoke-virtual {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;->set(J)V

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->eh()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object v0

    invoke-virtual {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;->set(J)V

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->rf()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object v0

    invoke-virtual {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;->set(J)V

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->uy()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object v0

    invoke-virtual {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;->set(J)V

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->lv()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object v0

    invoke-virtual {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;->set(J)V

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->rXP()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object v0

    invoke-virtual {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;->set(J)V

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->cB()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object v0

    invoke-virtual {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;->set(J)V

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->nsB()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object v0

    invoke-virtual {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;->set(J)V

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->Vq()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object v0

    invoke-virtual {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;->set(J)V

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->Moo()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object v0

    invoke-virtual {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;->set(J)V

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->mC()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object v0

    invoke-virtual {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;->set(J)V

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->rS()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object v0

    invoke-virtual {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;->set(J)V

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->vYf()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object v0

    invoke-virtual {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;->set(J)V

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->mE()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object v0

    invoke-virtual {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;->set(J)V

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->Af()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object v0

    invoke-virtual {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;->set(J)V

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->Tc()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object v0

    invoke-virtual {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;->set(J)V

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->JW()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object v0

    invoke-virtual {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;->set(J)V

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->JU()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object v0

    invoke-virtual {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;->set(J)V

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->Ql()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object v0

    invoke-virtual {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;->set(J)V

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->dG()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object v0

    invoke-virtual {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;->set(J)V

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->UYd()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object v0

    invoke-virtual {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;->set(J)V

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->Ko()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object v0

    invoke-virtual {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;->set(J)V

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->rAx()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object v0

    invoke-virtual {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;->set(J)V

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->mSE()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object v0

    invoke-virtual {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;->set(J)V

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->BcC()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object v0

    invoke-virtual {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;->set(J)V

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->svN()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object v0

    invoke-virtual {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;->set(J)V

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->WR()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object v0

    invoke-virtual {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;->set(J)V

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->Ubf()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object v0

    invoke-virtual {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;->set(J)V

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->eV()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object v0

    invoke-virtual {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;->set(J)V

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->hjc()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object v0

    invoke-virtual {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;->set(J)V

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->ex()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object v0

    invoke-virtual {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;->set(J)V

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->Fj()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object v0

    invoke-virtual {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicLong;->set(J)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    monitor-exit p0

    return-void

    :catchall_0
    move-exception v0

    monitor-exit p0

    throw v0
.end method

.method public Ql()Ljava/util/concurrent/atomic/AtomicLong;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->gci:Ljava/util/concurrent/atomic/AtomicLong;

    return-object v0
.end method

.method public Tc()Ljava/util/concurrent/atomic/AtomicLong;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->PpV:Ljava/util/concurrent/atomic/AtomicLong;

    return-object v0
.end method

.method public UYd()Ljava/util/concurrent/atomic/AtomicLong;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->Obv:Ljava/util/concurrent/atomic/AtomicLong;

    return-object v0
.end method

.method public Ubf()Ljava/util/concurrent/atomic/AtomicLong;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->flF:Ljava/util/concurrent/atomic/AtomicLong;

    return-object v0
.end method

.method public Vq()Ljava/util/concurrent/atomic/AtomicLong;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->spi:Ljava/util/concurrent/atomic/AtomicLong;

    return-object v0
.end method

.method public WR()Ljava/util/concurrent/atomic/AtomicLong;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->Jq:Ljava/util/concurrent/atomic/AtomicLong;

    return-object v0
.end method

.method public YH()Ljava/util/concurrent/atomic/AtomicLong;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->WR:Ljava/util/concurrent/atomic/AtomicLong;

    return-object v0
.end method

.method public cB()Ljava/util/concurrent/atomic/AtomicLong;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->uM:Ljava/util/concurrent/atomic/AtomicLong;

    return-object v0
.end method

.method public cs()Ljava/util/concurrent/atomic/AtomicLong;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->Ubf:Ljava/util/concurrent/atomic/AtomicLong;

    return-object v0
.end method

.method public dG()Ljava/util/concurrent/atomic/AtomicLong;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->HY:Ljava/util/concurrent/atomic/AtomicLong;

    return-object v0
.end method

.method public eV()Ljava/util/concurrent/atomic/AtomicLong;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->haP:Ljava/util/concurrent/atomic/AtomicLong;

    return-object v0
.end method

.method public efV()Ljava/util/concurrent/atomic/AtomicLong;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->mC:Ljava/util/concurrent/atomic/AtomicLong;

    return-object v0
.end method

.method public eh()Ljava/util/concurrent/atomic/AtomicLong;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->lv:Ljava/util/concurrent/atomic/AtomicLong;

    return-object v0
.end method

.method public ei()Ljava/util/concurrent/atomic/AtomicLong;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->JW:Ljava/util/concurrent/atomic/AtomicLong;

    return-object v0
.end method

.method public ex()Ljava/util/concurrent/atomic/AtomicLong;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->ks:Ljava/util/concurrent/atomic/AtomicLong;

    return-object v0
.end method

.method public ex(J)Lorg/json/JSONObject;
    .locals 12

    new-instance v0, Lorg/json/JSONObject;

    invoke-direct {v0}, Lorg/json/JSONObject;-><init>()V

    :try_start_0
    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->gci()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object v1

    invoke-virtual {v1}, Ljava/util/concurrent/atomic/AtomicLong;->get()J

    move-result-wide v1

    const-string v3, "create_save_cost_ts_avg"

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->HY()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object v4

    invoke-virtual {v4}, Ljava/util/concurrent/atomic/AtomicLong;->get()J

    move-result-wide v4

    long-to-float v4, v4

    const/high16 v5, 0x3f800000    # 1.0f

    mul-float v4, v4, v5

    const-wide/16 v6, 0x1

    const-wide/16 v8, 0x0

    cmp-long v10, v1, v8

    if-nez v10, :cond_0

    move-wide v10, v6

    goto :goto_0

    :cond_0
    move-wide v10, v1

    :goto_0
    long-to-float v10, v10

    div-float/2addr v4, v10

    float-to-double v10, v4

    invoke-virtual {v0, v3, v10, v11}, Lorg/json/JSONObject;->put(Ljava/lang/String;D)Lorg/json/JSONObject;

    const-string v3, "save_success_count"

    invoke-virtual {v0, v3, v1, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;J)Lorg/json/JSONObject;

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->OK()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object v1

    invoke-virtual {v1}, Ljava/util/concurrent/atomic/AtomicLong;->get()J

    move-result-wide v1

    const-string v3, "save_upload_cost_ts_avg"

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->Kk()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object v4

    invoke-virtual {v4}, Ljava/util/concurrent/atomic/AtomicLong;->get()J

    move-result-wide v10

    long-to-float v4, v10

    mul-float v4, v4, v5

    cmp-long v10, v1, v8

    if-nez v10, :cond_1

    move-wide v10, v6

    goto :goto_1

    :cond_1
    move-wide v10, v1

    :goto_1
    long-to-float v10, v10

    div-float/2addr v4, v10

    float-to-double v10, v4

    invoke-virtual {v0, v3, v10, v11}, Lorg/json/JSONObject;->put(Ljava/lang/String;D)Lorg/json/JSONObject;

    const-string v3, "will_send_count"

    invoke-virtual {v0, v3, v1, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;J)Lorg/json/JSONObject;

    const-string v1, "sdk_event_index"

    invoke-virtual {v0, v1, p1, p2}, Lorg/json/JSONObject;->put(Ljava/lang/String;J)Lorg/json/JSONObject;

    const-string p1, "sdk_send_success_count"

    iget-object p2, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->UYd:Ljava/util/concurrent/atomic/AtomicLong;

    invoke-virtual {p2}, Ljava/util/concurrent/atomic/AtomicLong;->get()J

    move-result-wide v1

    invoke-virtual {v0, p1, v1, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;J)Lorg/json/JSONObject;

    const-string p1, "all_delete_count"

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->cs()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object p2

    invoke-virtual {p2}, Ljava/util/concurrent/atomic/AtomicLong;->get()J

    move-result-wide v1

    invoke-virtual {v0, p1, v1, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;J)Lorg/json/JSONObject;

    const-string p1, "success_delete_count"

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->YH()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object p2

    invoke-virtual {p2}, Ljava/util/concurrent/atomic/AtomicLong;->get()J

    move-result-wide v1

    invoke-virtual {v0, p1, v1, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;J)Lorg/json/JSONObject;

    const-string p1, "invalid_delete_count"

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->At()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object p2

    invoke-virtual {p2}, Ljava/util/concurrent/atomic/AtomicLong;->get()J

    move-result-wide v1

    invoke-virtual {v0, p1, v1, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;J)Lorg/json/JSONObject;

    const-string p1, "will_save_count"

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->qg()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object p2

    invoke-virtual {p2}, Ljava/util/concurrent/atomic/AtomicLong;->get()J

    move-result-wide v1

    invoke-virtual {v0, p1, v1, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;J)Lorg/json/JSONObject;

    const-string p1, "did_send_count"

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->Bzy()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object p2

    invoke-virtual {p2}, Ljava/util/concurrent/atomic/AtomicLong;->get()J

    move-result-wide v1

    invoke-virtual {v0, p1, v1, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;J)Lorg/json/JSONObject;

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->Jq()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object p1

    invoke-virtual {p1}, Ljava/util/concurrent/atomic/AtomicLong;->get()J

    move-result-wide p1

    const-string v1, "send_success_valid_count"

    invoke-virtual {v0, v1, p1, p2}, Lorg/json/JSONObject;->put(Ljava/lang/String;J)Lorg/json/JSONObject;

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->qPr()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object p1

    invoke-virtual {p1}, Ljava/util/concurrent/atomic/AtomicLong;->get()J

    move-result-wide p1

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->haP()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object v1

    invoke-virtual {v1}, Ljava/util/concurrent/atomic/AtomicLong;->get()J

    move-result-wide v1

    const-string v3, "send_success_invalid_count"

    invoke-virtual {v0, v3, v1, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;J)Lorg/json/JSONObject;

    const-string v1, "send_fail_count"

    invoke-virtual {v0, v1, p1, p2}, Lorg/json/JSONObject;->put(Ljava/lang/String;J)Lorg/json/JSONObject;

    const-string p1, "before_save_count"

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->Obv()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object p2

    invoke-virtual {p2}, Ljava/util/concurrent/atomic/AtomicLong;->get()J

    move-result-wide v1

    invoke-virtual {v0, p1, v1, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;J)Lorg/json/JSONObject;

    const-string p1, "block_out_times"

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->mj()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object p2

    invoke-virtual {p2}, Ljava/util/concurrent/atomic/AtomicLong;->get()J

    move-result-wide v1

    invoke-virtual {v0, p1, v1, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;J)Lorg/json/JSONObject;

    const-string p1, "tm_count"

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->KZ()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object p2

    invoke-virtual {p2}, Ljava/util/concurrent/atomic/AtomicLong;->get()J

    move-result-wide v1

    invoke-virtual {v0, p1, v1, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;J)Lorg/json/JSONObject;

    const-string p1, "success_tm"

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->efV()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object p2

    invoke-virtual {p2}, Ljava/util/concurrent/atomic/AtomicLong;->get()J

    move-result-wide v1

    invoke-virtual {v0, p1, v1, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;J)Lorg/json/JSONObject;

    const-string p1, "queue_timeout_tm"

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->iT()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object p2

    invoke-virtual {p2}, Ljava/util/concurrent/atomic/AtomicLong;->get()J

    move-result-wide v1

    invoke-virtual {v0, p1, v1, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;J)Lorg/json/JSONObject;

    const-string p1, "after_upload_tm"

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->spi()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object p2

    invoke-virtual {p2}, Ljava/util/concurrent/atomic/AtomicLong;->get()J

    move-result-wide v1

    invoke-virtual {v0, p1, v1, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;J)Lorg/json/JSONObject;

    const-string p1, "net_retry_tm"

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->gXF()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object p2

    invoke-virtual {p2}, Ljava/util/concurrent/atomic/AtomicLong;->get()J

    move-result-wide v1

    invoke-virtual {v0, p1, v1, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;J)Lorg/json/JSONObject;

    const-string p1, "init_tm"

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->uM()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object p2

    invoke-virtual {p2}, Ljava/util/concurrent/atomic/AtomicLong;->get()J

    move-result-wide v1

    invoke-virtual {v0, p1, v1, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;J)Lorg/json/JSONObject;

    const-string p1, "quit_tm"

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->kF()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object p2

    invoke-virtual {p2}, Ljava/util/concurrent/atomic/AtomicLong;->get()J

    move-result-wide v1

    invoke-virtual {v0, p1, v1, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;J)Lorg/json/JSONObject;

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->ei()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object p1

    invoke-virtual {p1}, Ljava/util/concurrent/atomic/AtomicLong;->get()J

    move-result-wide p1

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->ks()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object v1

    invoke-virtual {v1}, Ljava/util/concurrent/atomic/AtomicLong;->get()J

    move-result-wide v1

    const-string v3, "success_request_cost_ts_avg"

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->JZ()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object v4

    invoke-virtual {v4}, Ljava/util/concurrent/atomic/AtomicLong;->get()J

    move-result-wide v10

    long-to-float v4, v10

    mul-float v4, v4, v5

    cmp-long v10, p1, v8

    if-nez v10, :cond_2

    move-wide v10, v6

    goto :goto_2

    :cond_2
    move-wide v10, p1

    :goto_2
    long-to-float v10, v10

    div-float/2addr v4, v10

    float-to-double v10, v4

    invoke-virtual {v0, v3, v10, v11}, Lorg/json/JSONObject;->put(Ljava/lang/String;D)Lorg/json/JSONObject;

    const-string v3, "fail_request_cost_ts_avg"

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->Eev()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object v4

    invoke-virtual {v4}, Ljava/util/concurrent/atomic/AtomicLong;->get()J

    move-result-wide v10

    long-to-float v4, v10

    mul-float v4, v4, v5

    cmp-long v5, v1, v8

    if-nez v5, :cond_3

    goto :goto_3

    :cond_3
    move-wide v6, v1

    :goto_3
    long-to-float v5, v6

    div-float/2addr v4, v5

    float-to-double v4, v4

    invoke-virtual {v0, v3, v4, v5}, Lorg/json/JSONObject;->put(Ljava/lang/String;D)Lorg/json/JSONObject;

    const-string v3, "request_count"

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->flF()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object v4

    invoke-virtual {v4}, Ljava/util/concurrent/atomic/AtomicLong;->get()J

    move-result-wide v4

    invoke-virtual {v0, v3, v4, v5}, Lorg/json/JSONObject;->put(Ljava/lang/String;J)Lorg/json/JSONObject;

    const-string v3, "request_success_count"

    invoke-virtual {v0, v3, p1, p2}, Lorg/json/JSONObject;->put(Ljava/lang/String;J)Lorg/json/JSONObject;

    const-string p1, "request_fail_count"

    invoke-virtual {v0, p1, v1, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;J)Lorg/json/JSONObject;

    const-string p1, "is_multi_process"

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->svN()Lcom/bytedance/sdk/component/Ubf/Fj/BcC;

    move-result-object p2

    invoke-virtual {p2}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->ex()Z

    move-result p2

    invoke-virtual {v0, p1, p2}, Lorg/json/JSONObject;->put(Ljava/lang/String;Z)Lorg/json/JSONObject;

    const-string p1, "stop_counts"

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->mC()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object p2

    invoke-virtual {p2}, Ljava/util/concurrent/atomic/AtomicLong;->get()J

    move-result-wide v1

    invoke-virtual {v0, p1, v1, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;J)Lorg/json/JSONObject;

    const-string p1, "check_return"

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->UYd()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object p2

    invoke-virtual {p2}, Ljava/util/concurrent/atomic/AtomicLong;->get()J

    move-result-wide v1

    invoke-virtual {v0, p1, v1, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;J)Lorg/json/JSONObject;

    const-string p1, "check_result"

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->dG()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object p2

    invoke-virtual {p2}, Ljava/util/concurrent/atomic/AtomicLong;->get()J

    move-result-wide v1

    invoke-virtual {v0, p1, v1, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;J)Lorg/json/JSONObject;

    const-string p1, "dispatch_event_count"

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->mSE()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object p2

    invoke-virtual {p2}, Ljava/util/concurrent/atomic/AtomicLong;->get()J

    move-result-wide v1

    invoke-virtual {v0, p1, v1, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;J)Lorg/json/JSONObject;

    const-string p1, "time_out_count"

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->Ko()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object p2

    invoke-virtual {p2}, Ljava/util/concurrent/atomic/AtomicLong;->get()J

    move-result-wide v1

    invoke-virtual {v0, p1, v1, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;J)Lorg/json/JSONObject;

    const-string p1, "server_net_error"

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->rAx()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object p2

    invoke-virtual {p2}, Ljava/util/concurrent/atomic/AtomicLong;->get()J

    move-result-wide v1

    invoke-virtual {v0, p1, v1, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;J)Lorg/json/JSONObject;

    const-string p1, "message_count"

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->BcC()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object p2

    invoke-virtual {p2}, Ljava/util/concurrent/atomic/AtomicLong;->get()J

    move-result-wide v1

    invoke-virtual {v0, p1, v1, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;J)Lorg/json/JSONObject;

    const-string p1, "after_count"

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->svN()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object p2

    invoke-virtual {p2}, Ljava/util/concurrent/atomic/AtomicLong;->get()J

    move-result-wide v1

    invoke-virtual {v0, p1, v1, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;J)Lorg/json/JSONObject;

    const-string p1, "queue_size"

    sget-object p2, Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;->Fj:Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;

    invoke-virtual {p2}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;->Fj()Ljava/util/concurrent/PriorityBlockingQueue;

    move-result-object p2

    invoke-virtual {p2}, Ljava/util/concurrent/PriorityBlockingQueue;->size()I

    move-result p2

    invoke-virtual {v0, p1, p2}, Lorg/json/JSONObject;->put(Ljava/lang/String;I)Lorg/json/JSONObject;

    const-string p1, "after_init_count"

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->WR()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object p2

    invoke-virtual {p2}, Ljava/util/concurrent/atomic/AtomicLong;->get()J

    move-result-wide v1

    invoke-virtual {v0, p1, v1, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;J)Lorg/json/JSONObject;

    const-string p1, "dispatch_init_count"

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->eV()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object p2

    invoke-virtual {p2}, Ljava/util/concurrent/atomic/AtomicLong;->get()J

    move-result-wide v1

    invoke-virtual {v0, p1, v1, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;J)Lorg/json/JSONObject;

    const-string p1, "notify_init_count"

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->Ubf()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object p2

    invoke-virtual {p2}, Ljava/util/concurrent/atomic/AtomicLong;->get()J

    move-result-wide v1

    invoke-virtual {v0, p1, v1, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;J)Lorg/json/JSONObject;

    const-string p1, "run_false_count"

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->hjc()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object p2

    invoke-virtual {p2}, Ljava/util/concurrent/atomic/AtomicLong;->get()J

    move-result-wide v1

    invoke-virtual {v0, p1, v1, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;J)Lorg/json/JSONObject;

    const-string p1, "all_after_count"

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->ex()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object p2

    invoke-virtual {p2}, Ljava/util/concurrent/atomic/AtomicLong;->get()J

    move-result-wide v1

    invoke-virtual {v0, p1, v1, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;J)Lorg/json/JSONObject;

    const-string p1, "app_start_time"

    sget-wide v1, Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;->svN:J

    invoke-virtual {v0, p1, v1, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;J)Lorg/json/JSONObject;

    const-string p1, "app_first_time"

    sget-wide v1, Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;->BcC:J

    invoke-virtual {v0, p1, v1, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;J)Lorg/json/JSONObject;

    const-string p1, "enter_count"

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->Fj()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object p2

    invoke-virtual {p2}, Ljava/util/concurrent/atomic/AtomicLong;->get()J

    move-result-wide v1

    invoke-virtual {v0, p1, v1, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;J)Lorg/json/JSONObject;

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->svN()Lcom/bytedance/sdk/component/Ubf/Fj/BcC;

    move-result-object p1

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->JU()Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;

    move-result-object p1

    if-eqz p1, :cond_5

    const-string p2, "is_debug"

    invoke-interface {p1}, Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;->ex()Z

    move-result v1

    invoke-virtual {v0, p2, v1}, Lorg/json/JSONObject;->put(Ljava/lang/String;Z)Lorg/json/JSONObject;

    invoke-interface {p1}, Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;->UYd()Lcom/bytedance/sdk/component/Ubf/Fj/svN;

    move-result-object p2

    if-eqz p2, :cond_4

    const-string v1, "is_plugin"

    invoke-interface {p2}, Lcom/bytedance/sdk/component/Ubf/Fj/svN;->hjc()Z

    move-result p2

    invoke-virtual {v0, v1, p2}, Lorg/json/JSONObject;->put(Ljava/lang/String;Z)Lorg/json/JSONObject;

    :cond_4
    invoke-interface {p1}, Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;->Fj()Z

    move-result p1

    if-eqz p1, :cond_5

    const-string p1, "memory_available_count"

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->fj()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object p2

    invoke-virtual {p2}, Ljava/util/concurrent/atomic/AtomicLong;->get()J

    move-result-wide v1

    invoke-virtual {v0, p1, v1, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;J)Lorg/json/JSONObject;

    const-string p1, "db_available_count"

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->eh()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object p2

    invoke-virtual {p2}, Ljava/util/concurrent/atomic/AtomicLong;->get()J

    move-result-wide v1

    invoke-virtual {v0, p1, v1, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;J)Lorg/json/JSONObject;

    const-string p1, "high_times"

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->rf()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object p2

    invoke-virtual {p2}, Ljava/util/concurrent/atomic/AtomicLong;->get()J

    move-result-wide v1

    invoke-virtual {v0, p1, v1, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;J)Lorg/json/JSONObject;

    const-string p1, "adevent_times"

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->uy()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object p2

    invoke-virtual {p2}, Ljava/util/concurrent/atomic/AtomicLong;->get()J

    move-result-wide v1

    invoke-virtual {v0, p1, v1, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;J)Lorg/json/JSONObject;

    const-string p1, "stats_times"

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->lv()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object p2

    invoke-virtual {p2}, Ljava/util/concurrent/atomic/AtomicLong;->get()J

    move-result-wide v1

    invoke-virtual {v0, p1, v1, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;J)Lorg/json/JSONObject;

    const-string p1, "batch_times"

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->rXP()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object p2

    invoke-virtual {p2}, Ljava/util/concurrent/atomic/AtomicLong;->get()J

    move-result-wide v1

    invoke-virtual {v0, p1, v1, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;J)Lorg/json/JSONObject;

    const-string p1, "high_counts"

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->cB()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object p2

    invoke-virtual {p2}, Ljava/util/concurrent/atomic/AtomicLong;->get()J

    move-result-wide v1

    invoke-virtual {v0, p1, v1, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;J)Lorg/json/JSONObject;

    const-string p1, "adevent_counts"

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->nsB()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object p2

    invoke-virtual {p2}, Ljava/util/concurrent/atomic/AtomicLong;->get()J

    move-result-wide v1

    invoke-virtual {v0, p1, v1, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;J)Lorg/json/JSONObject;

    const-string p1, "stats_counts"

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->Vq()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object p2

    invoke-virtual {p2}, Ljava/util/concurrent/atomic/AtomicLong;->get()J

    move-result-wide v1

    invoke-virtual {v0, p1, v1, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;J)Lorg/json/JSONObject;

    const-string p1, "batch_counts"

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->Moo()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object p2

    invoke-virtual {p2}, Ljava/util/concurrent/atomic/AtomicLong;->get()J

    move-result-wide v1

    invoke-virtual {v0, p1, v1, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;J)Lorg/json/JSONObject;

    const-string p1, "high_m_counts"

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->rS()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object p2

    invoke-virtual {p2}, Ljava/util/concurrent/atomic/AtomicLong;->get()J

    move-result-wide v1

    invoke-virtual {v0, p1, v1, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;J)Lorg/json/JSONObject;

    const-string p1, "adevent_m_counts"

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->vYf()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object p2

    invoke-virtual {p2}, Ljava/util/concurrent/atomic/AtomicLong;->get()J

    move-result-wide v1

    invoke-virtual {v0, p1, v1, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;J)Lorg/json/JSONObject;

    const-string p1, "stats_m_counts"

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->mE()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object p2

    invoke-virtual {p2}, Ljava/util/concurrent/atomic/AtomicLong;->get()J

    move-result-wide v1

    invoke-virtual {v0, p1, v1, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;J)Lorg/json/JSONObject;

    const-string p1, "batch_m_counts"

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->Af()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object p2

    invoke-virtual {p2}, Ljava/util/concurrent/atomic/AtomicLong;->get()J

    move-result-wide v1

    invoke-virtual {v0, p1, v1, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;J)Lorg/json/JSONObject;

    const-string p1, "high_db_avi"

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->Tc()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object p2

    invoke-virtual {p2}, Ljava/util/concurrent/atomic/AtomicLong;->get()J

    move-result-wide v1

    invoke-virtual {v0, p1, v1, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;J)Lorg/json/JSONObject;

    const-string p1, "adevent_db_avi"

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->JW()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object p2

    invoke-virtual {p2}, Ljava/util/concurrent/atomic/AtomicLong;->get()J

    move-result-wide v1

    invoke-virtual {v0, p1, v1, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;J)Lorg/json/JSONObject;

    const-string p1, "stats_db_avi"

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->JU()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object p2

    invoke-virtual {p2}, Ljava/util/concurrent/atomic/AtomicLong;->get()J

    move-result-wide v1

    invoke-virtual {v0, p1, v1, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;J)Lorg/json/JSONObject;

    const-string p1, "batch_db_avi"

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->Ql()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object p2

    invoke-virtual {p2}, Ljava/util/concurrent/atomic/AtomicLong;->get()J

    move-result-wide v1

    invoke-virtual {v0, p1, v1, v2}, Lorg/json/JSONObject;->put(Ljava/lang/String;J)Lorg/json/JSONObject;
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    :catch_0
    :cond_5
    return-object v0
.end method

.method public fj()Ljava/util/concurrent/atomic/AtomicLong;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->uy:Ljava/util/concurrent/atomic/AtomicLong;

    return-object v0
.end method

.method public flF()Ljava/util/concurrent/atomic/AtomicLong;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->Tc:Ljava/util/concurrent/atomic/AtomicLong;

    return-object v0
.end method

.method public gXF()Ljava/util/concurrent/atomic/AtomicLong;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->Vq:Ljava/util/concurrent/atomic/AtomicLong;

    return-object v0
.end method

.method public gci()Ljava/util/concurrent/atomic/AtomicLong;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->ex:Ljava/util/concurrent/atomic/AtomicLong;

    return-object v0
.end method

.method public haP()Ljava/util/concurrent/atomic/AtomicLong;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->dG:Ljava/util/concurrent/atomic/AtomicLong;

    return-object v0
.end method

.method public hjc()Ljava/util/concurrent/atomic/AtomicLong;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->ei:Ljava/util/concurrent/atomic/AtomicLong;

    return-object v0
.end method

.method public iT()Ljava/util/concurrent/atomic/AtomicLong;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->cB:Ljava/util/concurrent/atomic/AtomicLong;

    return-object v0
.end method

.method public kF()Ljava/util/concurrent/atomic/AtomicLong;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->rf:Ljava/util/concurrent/atomic/AtomicLong;

    return-object v0
.end method

.method public ks()Ljava/util/concurrent/atomic/AtomicLong;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->JU:Ljava/util/concurrent/atomic/AtomicLong;

    return-object v0
.end method

.method public lv()Ljava/util/concurrent/atomic/AtomicLong;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->eh:Ljava/util/concurrent/atomic/AtomicLong;

    return-object v0
.end method

.method public mC()Ljava/util/concurrent/atomic/AtomicLong;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->Bzy:Ljava/util/concurrent/atomic/AtomicLong;

    return-object v0
.end method

.method public mE()Ljava/util/concurrent/atomic/AtomicLong;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->KZ:Ljava/util/concurrent/atomic/AtomicLong;

    return-object v0
.end method

.method public mSE()Ljava/util/concurrent/atomic/AtomicLong;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->Kk:Ljava/util/concurrent/atomic/AtomicLong;

    return-object v0
.end method

.method public mj()Ljava/util/concurrent/atomic/AtomicLong;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->mE:Ljava/util/concurrent/atomic/AtomicLong;

    return-object v0
.end method

.method public nsB()Ljava/util/concurrent/atomic/AtomicLong;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->gXF:Ljava/util/concurrent/atomic/AtomicLong;

    return-object v0
.end method

.method public oX()Ljava/util/concurrent/atomic/AtomicLong;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->UYd:Ljava/util/concurrent/atomic/AtomicLong;

    return-object v0
.end method

.method public qPr()Ljava/util/concurrent/atomic/AtomicLong;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->BcC:Ljava/util/concurrent/atomic/AtomicLong;

    return-object v0
.end method

.method public qg()Ljava/util/concurrent/atomic/AtomicLong;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->mSE:Ljava/util/concurrent/atomic/AtomicLong;

    return-object v0
.end method

.method public rAx()Ljava/util/concurrent/atomic/AtomicLong;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->YH:Ljava/util/concurrent/atomic/AtomicLong;

    return-object v0
.end method

.method public rS()Ljava/util/concurrent/atomic/AtomicLong;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->efV:Ljava/util/concurrent/atomic/AtomicLong;

    return-object v0
.end method

.method public rXP()Ljava/util/concurrent/atomic/AtomicLong;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->kF:Ljava/util/concurrent/atomic/AtomicLong;

    return-object v0
.end method

.method public rf()Ljava/util/concurrent/atomic/AtomicLong;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->rXP:Ljava/util/concurrent/atomic/AtomicLong;

    return-object v0
.end method

.method public spi()Ljava/util/concurrent/atomic/AtomicLong;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->nsB:Ljava/util/concurrent/atomic/AtomicLong;

    return-object v0
.end method

.method public svN()Ljava/util/concurrent/atomic/AtomicLong;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->qg:Ljava/util/concurrent/atomic/AtomicLong;

    return-object v0
.end method

.method public uM()Ljava/util/concurrent/atomic/AtomicLong;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->Moo:Ljava/util/concurrent/atomic/AtomicLong;

    return-object v0
.end method

.method public uy()Ljava/util/concurrent/atomic/AtomicLong;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->fj:Ljava/util/concurrent/atomic/AtomicLong;

    return-object v0
.end method

.method public vYf()Ljava/util/concurrent/atomic/AtomicLong;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->oX:Ljava/util/concurrent/atomic/AtomicLong;

    return-object v0
.end method
