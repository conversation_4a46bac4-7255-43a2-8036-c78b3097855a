.class Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV$1;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/util/Comparator;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Ljava/util/Comparator<",
        "Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;",
        ">;"
    }
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV$1;->Fj:Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public Fj(Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;)I
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV$1;->Fj:Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;

    invoke-static {v0, p1, p2}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;->Fj(Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;)I

    move-result p1

    return p1
.end method

.method public synthetic compare(Ljava/lang/Object;Ljava/lang/Object;)I
    .locals 0

    check-cast p1, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;

    check-cast p2, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;

    invoke-virtual {p0, p1, p2}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV$1;->Fj(Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;)I

    move-result p1

    return p1
.end method
