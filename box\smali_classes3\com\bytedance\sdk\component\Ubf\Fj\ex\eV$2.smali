.class Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV$2;
.super Lcom/bytedance/sdk/component/Ubf/Fj/Ubf/Ubf;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;->Fj(Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;

.field final synthetic ex:J

.field final synthetic hjc:Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;Ljava/lang/String;Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;J)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV$2;->hjc:Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;

    iput-object p3, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV$2;->Fj:Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;

    iput-wide p4, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV$2;->ex:J

    invoke-direct {p0, p2}, Lcom/bytedance/sdk/component/Ubf/Fj/Ubf/Ubf;-><init>(Ljava/lang/String;)V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 4

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV$2;->hjc:Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;

    iget-object v1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV$2;->Fj:Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;

    iget-wide v2, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV$2;->ex:J

    invoke-static {v0, v1, v2, v3}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;->Fj(Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;J)V

    return-void
.end method
