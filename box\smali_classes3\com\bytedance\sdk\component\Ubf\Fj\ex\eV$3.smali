.class Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV$3;
.super Lcom/bytedance/sdk/component/Ubf/Fj/Ubf/Ubf;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;->Ubf()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;

.field final synthetic ex:Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;Ljava/lang/String;Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV$3;->ex:Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;

    iput-object p3, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV$3;->Fj:Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;

    invoke-direct {p0, p2}, Lcom/bytedance/sdk/component/Ubf/Fj/Ubf/Ubf;-><init>(Ljava/lang/String;)V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 2

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV$3;->Fj:Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;

    if-eqz v0, :cond_0

    const/4 v1, 0x2

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->hjc(I)V

    :cond_0
    return-void
.end method
