.class public interface abstract Lcom/bytedance/sdk/component/Ubf/Fj/ex/ex;
.super Ljava/lang/Object;


# virtual methods
.method public abstract Fj(Ljava/util/List;)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/Fj;",
            ">;)V"
        }
    .end annotation
.end method
