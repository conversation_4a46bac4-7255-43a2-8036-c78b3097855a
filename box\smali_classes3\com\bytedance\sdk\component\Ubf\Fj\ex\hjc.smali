.class public interface abstract Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc;
.super Ljava/lang/Object;


# virtual methods
.method public abstract Fj(Ljava/util/List;Lcom/bytedance/sdk/component/Ubf/Fj/ex/ex;)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;",
            ">;",
            "Lcom/bytedance/sdk/component/Ubf/Fj/ex/ex;",
            ")V"
        }
    .end annotation
.end method
