.class public Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/Fj;
.super Ljava/lang/Object;


# instance fields
.field private final Fj:Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/ex;

.field private final ex:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/ex;Ljava/util/List;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/ex;",
            "Ljava/util/List<",
            "Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;",
            ">;)V"
        }
    .end annotation

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/Fj;->Fj:Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/ex;

    iput-object p2, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/Fj;->ex:Ljava/util/List;

    return-void
.end method


# virtual methods
.method public Fj()Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/ex;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/Fj;->Fj:Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/ex;

    return-object v0
.end method

.method public ex()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;",
            ">;"
        }
    .end annotation

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/Fj;->ex:Ljava/util/List;

    return-object v0
.end method
