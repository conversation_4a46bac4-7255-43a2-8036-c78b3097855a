.class public Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/ex;
.super Ljava/lang/Object;


# instance fields
.field public final Fj:Z

.field public final Ubf:Z

.field public final eV:Ljava/lang/String;

.field public final ex:I

.field public final hjc:Ljava/lang/String;


# direct methods
.method public constructor <init>(ZILjava/lang/String;ZLjava/lang/String;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-boolean p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/ex;->Fj:Z

    iput p2, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/ex;->ex:I

    iput-object p3, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/ex;->hjc:Ljava/lang/String;

    iput-boolean p4, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/ex;->Ubf:Z

    iput-object p5, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/ex;->eV:Ljava/lang/String;

    return-void
.end method
