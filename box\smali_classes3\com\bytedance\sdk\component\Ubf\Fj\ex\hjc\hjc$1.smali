.class Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc$1;
.super Lcom/bytedance/sdk/component/Ubf/Fj/Ubf/Ubf;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->Fj(Ljava/util/List;ZJ)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Ljava/util/List;

.field final synthetic eV:Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;

.field final synthetic ex:Z

.field final synthetic hjc:J


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;Ljava/lang/String;Ljava/util/List;ZJ)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc$1;->eV:Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;

    iput-object p3, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc$1;->Fj:Ljava/util/List;

    iput-boolean p4, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc$1;->ex:Z

    iput-wide p5, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc$1;->hjc:J

    invoke-direct {p0, p2}, Lcom/bytedance/sdk/component/Ubf/Fj/Ubf/Ubf;-><init>(Ljava/lang/String;)V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 6

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc$1;->eV:Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;

    iget-object v1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc$1;->Fj:Ljava/util/List;

    iget-boolean v2, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc$1;->ex:Z

    iget-wide v3, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc$1;->hjc:J

    invoke-static {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->Fj(Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;)I

    move-result v5

    invoke-static/range {v0 .. v5}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->Fj(Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;Ljava/util/List;ZJI)V

    return-void
.end method
