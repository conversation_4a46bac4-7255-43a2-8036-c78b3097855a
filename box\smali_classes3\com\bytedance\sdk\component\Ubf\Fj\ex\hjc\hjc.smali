.class public Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;
.super Landroid/os/HandlerThread;

# interfaces
.implements Landroid/os/Handler$Callback;


# static fields
.field private static Ql:I = 0xa

.field private static rS:I = 0xc8


# instance fields
.field private final Af:I

.field private volatile BcC:J

.field protected Fj:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/eV;

.field private final JU:Ljava/util/concurrent/atomic/AtomicInteger;

.field private final JW:Ljava/util/concurrent/atomic/AtomicInteger;

.field private final Ko:J

.field private final Tc:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;",
            ">;"
        }
    .end annotation
.end field

.field private final UYd:Ljava/util/concurrent/atomic/AtomicInteger;

.field private final Ubf:Ljava/util/concurrent/PriorityBlockingQueue;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/concurrent/PriorityBlockingQueue<",
            "Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;",
            ">;"
        }
    .end annotation
.end field

.field private volatile WR:I

.field private volatile dG:Landroid/os/Handler;

.field private eV:Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc;

.field private volatile ex:Z

.field private final hjc:Ljava/lang/Object;

.field private final mE:I

.field private final mSE:Ljava/util/concurrent/atomic/AtomicInteger;

.field private final rAx:J

.field private volatile svN:J

.field private final vYf:I


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Ljava/util/concurrent/PriorityBlockingQueue;)V
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/concurrent/PriorityBlockingQueue<",
            "Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;",
            ">;)V"
        }
    .end annotation

    const-string v0, "csj_log"

    invoke-direct {p0, v0}, Landroid/os/HandlerThread;-><init>(Ljava/lang/String;)V

    const/4 v0, 0x1

    iput-boolean v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->ex:Z

    new-instance v1, Ljava/lang/Object;

    invoke-direct {v1}, Ljava/lang/Object;-><init>()V

    iput-object v1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->hjc:Ljava/lang/Object;

    const-wide/16 v1, 0x0

    iput-wide v1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->svN:J

    iput-wide v1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->BcC:J

    new-instance v1, Ljava/util/concurrent/atomic/AtomicInteger;

    const/4 v2, 0x0

    invoke-direct {v1, v2}, Ljava/util/concurrent/atomic/AtomicInteger;-><init>(I)V

    iput-object v1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->mSE:Ljava/util/concurrent/atomic/AtomicInteger;

    const-wide/16 v3, 0x1388

    iput-wide v3, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->Ko:J

    const-wide v3, 0x12a05f200L

    iput-wide v3, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->rAx:J

    new-instance v1, Ljava/util/concurrent/atomic/AtomicInteger;

    invoke-direct {v1, v2}, Ljava/util/concurrent/atomic/AtomicInteger;-><init>(I)V

    iput-object v1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->UYd:Ljava/util/concurrent/atomic/AtomicInteger;

    new-instance v1, Ljava/util/ArrayList;

    invoke-direct {v1}, Ljava/util/ArrayList;-><init>()V

    iput-object v1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->Tc:Ljava/util/List;

    new-instance v1, Ljava/util/concurrent/atomic/AtomicInteger;

    invoke-direct {v1, v2}, Ljava/util/concurrent/atomic/AtomicInteger;-><init>(I)V

    iput-object v1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->JW:Ljava/util/concurrent/atomic/AtomicInteger;

    new-instance v1, Ljava/util/concurrent/atomic/AtomicInteger;

    invoke-direct {v1, v2}, Ljava/util/concurrent/atomic/AtomicInteger;-><init>(I)V

    iput-object v1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->JU:Ljava/util/concurrent/atomic/AtomicInteger;

    iput v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->vYf:I

    const/4 v0, 0x2

    iput v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->mE:I

    const/4 v0, 0x3

    iput v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->Af:I

    iput-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->Ubf:Ljava/util/concurrent/PriorityBlockingQueue;

    new-instance p1, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/ex;

    invoke-direct {p1}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/ex;-><init>()V

    iput-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->Fj:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/eV;

    return-void
.end method

.method private BcC()Z
    .locals 2

    sget-object v0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;->Fj:Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;

    iget-boolean v0, v0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;->ex:Z

    if-eqz v0, :cond_1

    iget v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->WR:I

    const/4 v1, 0x4

    if-eq v0, v1, :cond_0

    iget v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->WR:I

    const/4 v1, 0x7

    if-eq v0, v1, :cond_0

    iget v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->WR:I

    const/4 v1, 0x6

    if-eq v0, v1, :cond_0

    iget v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->WR:I

    const/4 v1, 0x5

    if-eq v0, v1, :cond_0

    iget v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->WR:I

    const/4 v1, 0x2

    if-ne v0, v1, :cond_1

    :cond_0
    const/4 v0, 0x1

    return v0

    :cond_1
    const/4 v0, 0x0

    return v0
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;)I
    .locals 0

    iget p0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->WR:I

    return p0
.end method

.method public static Fj(I)V
    .locals 0

    sput p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->Ql:I

    return-void
.end method

.method private Fj(ILjava/util/List;J)V
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I",
            "Ljava/util/List<",
            "Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;",
            ">;J)V"
        }
    .end annotation

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->hjc:Ljava/lang/Object;

    monitor-enter v0

    if-eqz p2, :cond_f

    :try_start_0
    iget-object v1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->dG:Landroid/os/Handler;

    if-nez v1, :cond_0

    goto/16 :goto_1

    :cond_0
    invoke-static {p1, p2, p3, p4}, Lcom/bytedance/sdk/component/Ubf/Fj/hjc/Fj;->Fj(ILjava/util/List;J)V

    iget-object p3, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->Fj:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/eV;

    invoke-interface {p3, p1, p2}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/eV;->Fj(ILjava/util/List;)V

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->svN()Lcom/bytedance/sdk/component/Ubf/Fj/BcC;

    move-result-object p2

    invoke-virtual {p2}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->JU()Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;

    const/4 p2, -0x2

    const/4 p3, 0x1

    const/4 p4, 0x3

    const/4 v1, 0x0

    const/4 v2, 0x2

    if-eq p1, p2, :cond_9

    const/4 p2, -0x1

    if-eq p1, p2, :cond_5

    if-eqz p1, :cond_9

    const/16 p2, 0xc8

    if-eq p1, p2, :cond_5

    const/16 p2, 0x1fd

    if-eq p1, p2, :cond_1

    goto/16 :goto_0

    :cond_1
    sget-object p1, Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;->Fj:Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;

    iput-boolean p3, p1, Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;->ex:Z

    iput-boolean v1, p1, Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;->hjc:Z

    iget-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->dG:Landroid/os/Handler;

    invoke-virtual {p1, v2}, Landroid/os/Handler;->hasMessages(I)Z

    move-result p1

    if-eqz p1, :cond_2

    monitor-exit v0

    return-void

    :catchall_0
    move-exception p1

    goto/16 :goto_2

    :cond_2
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide p1

    iget-wide v3, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->svN:J

    sub-long/2addr p1, v3

    const-wide/16 v3, 0x7530

    cmp-long p3, p1, v3

    if-gez p3, :cond_3

    monitor-exit v0

    return-void

    :cond_3
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide p1

    iput-wide p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->svN:J

    iget-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->dG:Landroid/os/Handler;

    invoke-virtual {p1, p4}, Landroid/os/Handler;->hasMessages(I)Z

    move-result p1

    if-eqz p1, :cond_4

    iget-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->dG:Landroid/os/Handler;

    invoke-virtual {p1, p4}, Landroid/os/Handler;->removeMessages(I)V

    :cond_4
    invoke-virtual {p0, v2, v3, v4}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->Fj(IJ)V

    goto/16 :goto_0

    :cond_5
    sget-object p1, Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;->Fj:Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;

    iget-boolean p2, p1, Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;->ex:Z

    if-nez p2, :cond_6

    iget-boolean p2, p1, Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;->hjc:Z

    if-eqz p2, :cond_d

    :cond_6
    iput-boolean v1, p1, Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;->ex:Z

    iput-boolean v1, p1, Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;->hjc:Z

    iget-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->dG:Landroid/os/Handler;

    invoke-virtual {p1, v2}, Landroid/os/Handler;->hasMessages(I)Z

    move-result p1

    if-eqz p1, :cond_7

    iget-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->dG:Landroid/os/Handler;

    invoke-virtual {p1, v2}, Landroid/os/Handler;->removeMessages(I)V

    :cond_7
    iget-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->dG:Landroid/os/Handler;

    invoke-virtual {p1, p4}, Landroid/os/Handler;->hasMessages(I)Z

    move-result p1

    if-eqz p1, :cond_8

    iget-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->dG:Landroid/os/Handler;

    invoke-virtual {p1, p4}, Landroid/os/Handler;->removeMessages(I)V

    :cond_8
    const-wide/16 p1, 0x0

    iput-wide p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->BcC:J

    iput-wide p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->svN:J

    iget-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->JW:Ljava/util/concurrent/atomic/AtomicInteger;

    invoke-virtual {p1, v1}, Ljava/util/concurrent/atomic/AtomicInteger;->set(I)V

    iget-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->JU:Ljava/util/concurrent/atomic/AtomicInteger;

    invoke-virtual {p1, v1}, Ljava/util/concurrent/atomic/AtomicInteger;->set(I)V

    sget-object p1, Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;->eV:Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->efV()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object p1

    invoke-static {p1, p3}, Lcom/bytedance/sdk/component/Ubf/Fj/hjc/ex;->Fj(Ljava/util/concurrent/atomic/AtomicLong;I)V

    invoke-virtual {p0, v2}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->hjc(I)V

    goto :goto_0

    :cond_9
    sget-object p1, Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;->Fj:Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;

    iput-boolean v1, p1, Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;->ex:Z

    iput-boolean p3, p1, Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;->hjc:Z

    iget-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->dG:Landroid/os/Handler;

    invoke-virtual {p1, p4}, Landroid/os/Handler;->hasMessages(I)Z

    move-result p1

    if-eqz p1, :cond_a

    monitor-exit v0

    return-void

    :cond_a
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide p1

    iget-wide v3, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->BcC:J

    sub-long/2addr p1, v3

    const-wide/16 v3, 0x3a98

    cmp-long p3, p1, v3

    if-gez p3, :cond_b

    monitor-exit v0

    return-void

    :cond_b
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide p1

    iput-wide p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->BcC:J

    iget-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->dG:Landroid/os/Handler;

    invoke-virtual {p1, v2}, Landroid/os/Handler;->hasMessages(I)Z

    move-result p1

    if-eqz p1, :cond_c

    iget-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->dG:Landroid/os/Handler;

    invoke-virtual {p1, v2}, Landroid/os/Handler;->removeMessages(I)V

    :cond_c
    invoke-virtual {p0, p4, v3, v4}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->Fj(IJ)V

    :cond_d
    :goto_0
    iget p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->WR:I

    if-ne p1, v2, :cond_e

    iget-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->hjc:Ljava/lang/Object;

    invoke-virtual {p1}, Ljava/lang/Object;->notify()V

    :cond_e
    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    iget-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->Ubf:Ljava/util/concurrent/PriorityBlockingQueue;

    invoke-virtual {p1}, Ljava/util/concurrent/PriorityBlockingQueue;->size()I

    return-void

    :cond_f
    :goto_1
    :try_start_1
    monitor-exit v0
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    return-void

    :goto_2
    monitor-exit v0

    throw p1
.end method

.method private Fj(Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;)V
    .locals 2

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->mSE:Ljava/util/concurrent/atomic/AtomicInteger;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/util/concurrent/atomic/AtomicInteger;->set(I)V

    sget-object v0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;->Fj:Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;

    iget-boolean v1, v0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;->ex:Z

    if-eqz v1, :cond_0

    const/4 v0, 0x5

    iput v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->WR:I

    goto :goto_0

    :cond_0
    iget-boolean v0, v0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;->hjc:Z

    if-eqz v0, :cond_1

    const/4 v0, 0x7

    iput v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->WR:I

    goto :goto_0

    :cond_1
    const/4 v0, 0x4

    iput v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->WR:I

    :goto_0
    sget-object v0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;->eV:Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->Obv()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object v0

    const/4 v1, 0x1

    invoke-static {v0, v1}, Lcom/bytedance/sdk/component/Ubf/Fj/hjc/ex;->Fj(Ljava/util/concurrent/atomic/AtomicLong;I)V

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->Fj:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/eV;

    iget v1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->WR:I

    invoke-interface {v0, p1, v1}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/eV;->Fj(Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;I)V

    invoke-static {p1}, Lcom/bytedance/sdk/component/Ubf/Fj/hjc/Fj;->svN(Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;)V

    return-void
.end method

.method private Fj(Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;I)V
    .locals 2

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->mSE:Ljava/util/concurrent/atomic/AtomicInteger;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/util/concurrent/atomic/AtomicInteger;->set(I)V

    const/4 v0, 0x1

    if-nez p2, :cond_0

    move-object p2, p1

    check-cast p2, Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex;

    invoke-virtual {p2}, Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex;->rAx()I

    move-result p2

    iput p2, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->WR:I

    iget p2, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->WR:I

    const/4 v1, 0x6

    if-eq p2, v1, :cond_2

    sget-object p2, Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;->eV:Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;

    invoke-virtual {p2}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->KZ()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object p2

    invoke-static {p2, v0}, Lcom/bytedance/sdk/component/Ubf/Fj/hjc/ex;->Fj(Ljava/util/concurrent/atomic/AtomicLong;I)V

    invoke-direct {p0, p1}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->ex(Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;)V

    return-void

    :cond_0
    move-object p2, p1

    check-cast p2, Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex;

    invoke-virtual {p2}, Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex;->rAx()I

    move-result v1

    if-ne v1, v0, :cond_1

    iput v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->WR:I

    invoke-direct {p0, p1}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->ex(Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;)V

    return-void

    :cond_1
    invoke-virtual {p2}, Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex;->rAx()I

    move-result p2

    const/4 v0, 0x2

    if-ne p2, v0, :cond_2

    invoke-direct {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->svN()V

    iput v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->WR:I

    invoke-direct {p0, p1}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->ex(Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;)V

    :cond_2
    return-void
.end method

.method private Fj(Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/ex;Ljava/util/List;)V
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/ex;",
            "Ljava/util/List<",
            "Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;",
            ">;)V"
        }
    .end annotation

    if-eqz p1, :cond_1

    iget-boolean p1, p1, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/ex;->Fj:Z

    if-eqz p1, :cond_1

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/ex;->Fj()Ljava/util/List;

    move-result-object p1

    if-eqz p2, :cond_1

    if-eqz p1, :cond_1

    invoke-interface {p1}, Ljava/util/List;->size()I

    move-result v0

    if-eqz v0, :cond_1

    invoke-interface {p2}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p2

    :cond_0
    invoke-interface {p2}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_1

    invoke-interface {p2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;

    invoke-interface {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;->Ubf()B

    move-result v1

    const/4 v2, 0x1

    if-ne v1, v2, :cond_0

    invoke-static {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/hjc/Fj;->Fj(Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;)Ljava/lang/String;

    invoke-static {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/hjc/Fj;->Ubf(Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;)Ljava/lang/String;

    invoke-interface {p1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_0

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    goto :goto_0

    :cond_1
    return-void
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;Ljava/util/List;ZJI)V
    .locals 0

    invoke-direct/range {p0 .. p5}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->Fj(Ljava/util/List;ZJI)V

    return-void
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;ZLcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/ex;Ljava/util/List;J)V
    .locals 0

    invoke-direct/range {p0 .. p5}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->Fj(ZLcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/ex;Ljava/util/List;J)V

    return-void
.end method

.method private Fj(Ljava/lang/String;)V
    .locals 2

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->dG:Landroid/os/Handler;

    const/16 v1, 0xb

    invoke-virtual {v0, v1}, Landroid/os/Handler;->hasMessages(I)Z

    move-result v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->dG:Landroid/os/Handler;

    invoke-virtual {v0, v1}, Landroid/os/Handler;->removeMessages(I)V

    :cond_0
    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->Tc:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    if-eqz v0, :cond_1

    new-instance v0, Ljava/util/ArrayList;

    iget-object v1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->Tc:Ljava/util/List;

    invoke-direct {v0, v1}, Ljava/util/ArrayList;-><init>(Ljava/util/Collection;)V

    iget-object v1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->Tc:Ljava/util/List;

    invoke-interface {v1}, Ljava/util/List;->clear()V

    invoke-static {p1}, Ljava/lang/String;->valueOf(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    const-string v1, "before_"

    invoke-virtual {v1, p1}, Ljava/lang/String;->concat(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    const/4 v1, 0x0

    invoke-direct {p0, v0, v1, p1}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->Fj(Ljava/util/List;ZLjava/lang/String;)V

    invoke-direct {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->Ko()V

    invoke-interface {v0}, Ljava/util/List;->size()I

    :cond_1
    return-void
.end method

.method private Fj(Ljava/util/List;)V
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;",
            ">;)V"
        }
    .end annotation

    invoke-interface {p1}, Ljava/util/List;->size()I

    move-result v0

    if-eqz v0, :cond_8

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->Ubf:Ljava/util/concurrent/PriorityBlockingQueue;

    invoke-virtual {v0}, Ljava/util/concurrent/PriorityBlockingQueue;->size()I

    move-result v0

    invoke-static {p1, v0}, Lcom/bytedance/sdk/component/Ubf/Fj/hjc/Fj;->Fj(Ljava/util/List;I)V

    invoke-interface {p1}, Ljava/util/List;->size()I

    move-result v0

    const/4 v1, 0x1

    if-gt v0, v1, :cond_7

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/hjc/Fj;->hjc()Z

    move-result v0

    if-eqz v0, :cond_0

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    invoke-interface {p1, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;

    if-eqz v0, :cond_6

    invoke-interface {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;->Ubf()B

    move-result v2

    if-ne v2, v1, :cond_1

    const-string v0, "highPriority"

    invoke-direct {p0, p1, v0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->Fj(Ljava/util/List;Ljava/lang/String;)V

    return-void

    :cond_1
    invoke-interface {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;->eV()B

    move-result v2

    const/4 v3, 0x3

    const/4 v4, 0x2

    if-nez v2, :cond_3

    invoke-interface {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;->Ubf()B

    move-result v2

    if-ne v2, v4, :cond_3

    invoke-interface {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;->ex()B

    move-result v0

    if-ne v0, v3, :cond_2

    const-string v0, "version_v3"

    invoke-direct {p0, p1, v0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->Fj(Ljava/util/List;Ljava/lang/String;)V

    return-void

    :cond_2
    invoke-direct {p0, p1}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->ex(Ljava/util/List;)V

    return-void

    :cond_3
    invoke-interface {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;->eV()B

    move-result v2

    if-ne v2, v1, :cond_4

    const-string v0, "stats"

    invoke-direct {p0, p1, v0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->Fj(Ljava/util/List;Ljava/lang/String;)V

    return-void

    :cond_4
    invoke-interface {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;->eV()B

    move-result v1

    if-ne v1, v3, :cond_5

    const-string v0, "adType_v3"

    invoke-direct {p0, p1, v0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->Fj(Ljava/util/List;Ljava/lang/String;)V

    return-void

    :cond_5
    invoke-interface {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;->eV()B

    move-result v0

    if-ne v0, v4, :cond_6

    const-string v0, "other"

    invoke-direct {p0, p1, v0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->Fj(Ljava/util/List;Ljava/lang/String;)V

    :cond_6
    return-void

    :cond_7
    :goto_0
    const-string v0, "batchRead"

    invoke-direct {p0, p1, v0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->Fj(Ljava/util/List;Ljava/lang/String;)V

    return-void

    :cond_8
    invoke-direct {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->mSE()V

    return-void
.end method

.method private Fj(Ljava/util/List;Ljava/lang/String;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;",
            ">;",
            "Ljava/lang/String;",
            ")V"
        }
    .end annotation

    invoke-direct {p0, p2}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->Fj(Ljava/lang/String;)V

    const/4 v0, 0x0

    invoke-direct {p0, p1, v0, p2}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->Fj(Ljava/util/List;ZLjava/lang/String;)V

    invoke-direct {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->Ko()V

    return-void
.end method

.method private Fj(Ljava/util/List;ZJ)V
    .locals 9
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;",
            ">;ZJ)V"
        }
    .end annotation

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->svN()Lcom/bytedance/sdk/component/Ubf/Fj/BcC;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->JU()Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;

    move-result-object v0

    if-eqz v0, :cond_2

    invoke-interface {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;->Ubf()Ljava/util/concurrent/Executor;

    move-result-object v1

    const/4 v2, 0x0

    invoke-interface {p1, v2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;

    invoke-interface {v2}, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;->Ubf()B

    move-result v2

    const/4 v3, 0x1

    if-ne v2, v3, :cond_0

    invoke-interface {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;->eV()Ljava/util/concurrent/Executor;

    move-result-object v1

    :cond_0
    if-nez v1, :cond_1

    return-void

    :cond_1
    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->UYd:Ljava/util/concurrent/atomic/AtomicInteger;

    invoke-virtual {v0}, Ljava/util/concurrent/atomic/AtomicInteger;->incrementAndGet()I

    new-instance v0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc$1;

    const-string v4, "csj_log_upload"

    move-object v2, v0

    move-object v3, p0

    move-object v5, p1

    move v6, p2

    move-wide v7, p3

    invoke-direct/range {v2 .. v8}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc$1;-><init>(Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;Ljava/lang/String;Ljava/util/List;ZJ)V

    invoke-interface {v1, v0}, Ljava/util/concurrent/Executor;->execute(Ljava/lang/Runnable;)V

    :cond_2
    return-void
.end method

.method private Fj(Ljava/util/List;ZJI)V
    .locals 7
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;",
            ">;ZJI)V"
        }
    .end annotation

    const/4 p5, 0x0

    const/4 v0, 0x1

    :try_start_0
    invoke-interface {p1, p5}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p5

    check-cast p5, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;

    sget-object v1, Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;->eV:Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;

    invoke-virtual {v1}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->flF()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object v1

    invoke-static {v1, v0}, Lcom/bytedance/sdk/component/Ubf/Fj/hjc/ex;->Fj(Ljava/util/concurrent/atomic/AtomicLong;I)V

    invoke-interface {p5}, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;->eV()B

    move-result p5

    if-nez p5, :cond_1

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->Ubf()Lcom/bytedance/sdk/component/Ubf/Fj/Ubf/Fj;

    move-result-object p5

    invoke-interface {p5, p1}, Lcom/bytedance/sdk/component/Ubf/Fj/Ubf/Fj;->Fj(Ljava/util/List;)Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/ex;

    move-result-object p5

    invoke-direct {p0, p5, p1}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->Fj(Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/ex;Ljava/util/List;)V

    if-eqz p5, :cond_0

    iget-object v1, p5, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/ex;->eV:Ljava/lang/String;

    invoke-static {p1, v1}, Lcom/bytedance/sdk/component/Ubf/Fj/hjc/Fj;->Fj(Ljava/util/List;Ljava/lang/String;)V

    goto :goto_0

    :catchall_0
    move-exception p1

    goto :goto_5

    :cond_0
    :goto_0
    move-object v3, p5

    goto :goto_4

    :cond_1
    new-instance p5, Lorg/json/JSONObject;

    invoke-direct {p5}, Lorg/json/JSONObject;-><init>()V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    :try_start_1
    new-instance v1, Lorg/json/JSONArray;

    invoke-direct {v1}, Lorg/json/JSONArray;-><init>()V

    invoke-interface {p1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v2

    :goto_1
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    move-result v3

    if-eqz v3, :cond_2

    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;

    invoke-interface {v3}, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;->svN()Lorg/json/JSONObject;

    move-result-object v3

    invoke-virtual {v1, v3}, Lorg/json/JSONArray;->put(Ljava/lang/Object;)Lorg/json/JSONArray;

    goto :goto_1

    :catch_0
    move-exception v1

    goto :goto_2

    :cond_2
    const-string v2, "stats_list"

    invoke-virtual {p5, v2, v1}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;
    :try_end_1
    .catch Ljava/lang/Exception; {:try_start_1 .. :try_end_1} :catch_0
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    goto :goto_3

    :goto_2
    :try_start_2
    invoke-virtual {v1}, Ljava/lang/Throwable;->getMessage()Ljava/lang/String;

    :goto_3
    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->Ubf()Lcom/bytedance/sdk/component/Ubf/Fj/Ubf/Fj;

    move-result-object v1

    invoke-interface {v1, p5}, Lcom/bytedance/sdk/component/Ubf/Fj/Ubf/Fj;->Fj(Lorg/json/JSONObject;)Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/ex;

    move-result-object p5

    goto :goto_0

    :goto_4
    iget-object p5, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->UYd:Ljava/util/concurrent/atomic/AtomicInteger;

    invoke-virtual {p5}, Ljava/util/concurrent/atomic/AtomicInteger;->decrementAndGet()I

    move-object v1, p0

    move v2, p2

    move-object v4, p1

    move-wide v5, p3

    invoke-direct/range {v1 .. v6}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->Fj(ZLcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/ex;Ljava/util/List;J)V
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_0

    return-void

    :goto_5
    invoke-virtual {p1}, Ljava/lang/Throwable;->getMessage()Ljava/lang/String;

    sget-object p1, Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;->eV:Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->iT()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object p1

    invoke-static {p1, v0}, Lcom/bytedance/sdk/component/Ubf/Fj/hjc/ex;->Fj(Ljava/util/concurrent/atomic/AtomicLong;I)V

    iget-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->UYd:Ljava/util/concurrent/atomic/AtomicInteger;

    invoke-virtual {p1}, Ljava/util/concurrent/atomic/AtomicInteger;->decrementAndGet()I

    return-void
.end method

.method private Fj(Ljava/util/List;ZLjava/lang/String;)V
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;",
            ">;Z",
            "Ljava/lang/String;",
            ")V"
        }
    .end annotation

    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v0

    iget v2, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->WR:I

    invoke-static {p1, v2, p3}, Lcom/bytedance/sdk/component/Ubf/Fj/hjc/Fj;->Fj(Ljava/util/List;ILjava/lang/String;)V

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->svN()Lcom/bytedance/sdk/component/Ubf/Fj/BcC;

    move-result-object p3

    invoke-virtual {p3}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->BcC()Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc;

    move-result-object p3

    iput-object p3, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->eV:Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc;

    if-eqz p3, :cond_0

    invoke-direct {p0, p1, p2, v0, v1}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->ex(Ljava/util/List;ZJ)V

    return-void

    :cond_0
    invoke-direct {p0, p1, p2, v0, v1}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->Fj(Ljava/util/List;ZJ)V

    return-void
.end method

.method private Fj(ZLcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/ex;Ljava/util/List;J)V
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(Z",
            "Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/ex;",
            "Ljava/util/List<",
            "Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;",
            ">;J)V"
        }
    .end annotation

    if-nez p1, :cond_8

    if-eqz p2, :cond_8

    iget p1, p2, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/ex;->ex:I

    iget-boolean v0, p2, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/ex;->Ubf:Z

    const/4 v1, -0x2

    if-eqz v0, :cond_0

    const/4 p1, -0x1

    goto :goto_0

    :cond_0
    if-gez p1, :cond_1

    const/4 p1, -0x2

    :cond_1
    :goto_0
    const/16 v0, 0x1fe

    if-eq p1, v0, :cond_2

    const/16 v0, 0x1ff

    if-ne p1, v0, :cond_3

    :cond_2
    const/4 p1, -0x2

    :cond_3
    iget-boolean p2, p2, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/ex;->Fj:Z

    if-nez p2, :cond_5

    const/16 p2, 0x1f4

    if-lt p1, p2, :cond_4

    const/16 p2, 0x1fd

    if-lt p1, p2, :cond_6

    :cond_4
    const/16 p2, 0x201

    if-le p1, p2, :cond_5

    goto :goto_1

    :cond_5
    move v1, p1

    :cond_6
    :goto_1
    if-eqz p3, :cond_7

    invoke-interface {p3}, Ljava/util/List;->size()I

    iget-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->UYd:Ljava/util/concurrent/atomic/AtomicInteger;

    invoke-virtual {p1}, Ljava/util/concurrent/atomic/AtomicInteger;->get()I

    :cond_7
    invoke-direct {p0, v1, p3, p4, p5}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->Fj(ILjava/util/List;J)V

    :cond_8
    return-void
.end method

.method private Ko()V
    .locals 10

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->dG:Landroid/os/Handler;

    const/16 v1, 0xb

    invoke-virtual {v0, v1}, Landroid/os/Handler;->hasMessages(I)Z

    move-result v0

    const/4 v1, 0x1

    if-eqz v0, :cond_0

    invoke-direct {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->mSE()V

    goto :goto_0

    :cond_0
    invoke-direct {p0, v1}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->Ubf(I)V

    :goto_0
    sget-object v0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;->eV:Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->ex()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object v2

    invoke-static {v2, v1}, Lcom/bytedance/sdk/component/Ubf/Fj/hjc/ex;->Fj(Ljava/util/concurrent/atomic/AtomicLong;I)V

    iget v2, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->WR:I

    const/4 v3, 0x2

    if-ne v2, v3, :cond_6

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->svN()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object v2

    invoke-static {v2, v1}, Lcom/bytedance/sdk/component/Ubf/Fj/hjc/ex;->Fj(Ljava/util/concurrent/atomic/AtomicLong;I)V

    iget-object v2, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->hjc:Ljava/lang/Object;

    monitor-enter v2

    :try_start_0
    invoke-static {}, Ljava/lang/System;->nanoTime()J

    move-result-wide v4

    iget-object v6, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->hjc:Ljava/lang/Object;

    const-wide/16 v7, 0x1388

    invoke-virtual {v6, v7, v8}, Ljava/lang/Object;->wait(J)V

    invoke-static {}, Ljava/lang/System;->nanoTime()J

    move-result-wide v6

    sub-long/2addr v6, v4

    sget-object v4, Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;->Fj:Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;

    iget-boolean v5, v4, Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;->ex:Z

    if-nez v5, :cond_1

    iget-boolean v5, v4, Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;->hjc:Z

    goto :goto_1

    :catchall_0
    move-exception v0

    goto :goto_6

    :catch_0
    move-exception v0

    goto :goto_4

    :cond_1
    :goto_1
    const-wide v8, 0x12a05f200L

    cmp-long v5, v6, v8

    if-gez v5, :cond_5

    sub-long/2addr v8, v6

    const-wide/32 v5, 0x2faf080

    cmp-long v7, v8, v5

    if-gez v7, :cond_2

    goto :goto_3

    :cond_2
    iget-boolean v5, v4, Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;->ex:Z

    if-nez v5, :cond_4

    iget-boolean v4, v4, Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;->hjc:Z

    if-eqz v4, :cond_3

    goto :goto_2

    :cond_3
    invoke-virtual {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->spi()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object v0

    invoke-static {v0, v1}, Lcom/bytedance/sdk/component/Ubf/Fj/hjc/ex;->Fj(Ljava/util/concurrent/atomic/AtomicLong;I)V

    invoke-virtual {p0, v3}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->hjc(I)V

    goto :goto_5

    :cond_4
    :goto_2
    invoke-virtual {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->rAx()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object v0

    invoke-static {v0, v1}, Lcom/bytedance/sdk/component/Ubf/Fj/hjc/ex;->Fj(Ljava/util/concurrent/atomic/AtomicLong;I)V
    :try_end_0
    .catch Ljava/lang/InterruptedException; {:try_start_0 .. :try_end_0} :catch_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    :try_start_1
    monitor-exit v2
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    return-void

    :cond_5
    :goto_3
    :try_start_2
    invoke-virtual {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->Ko()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object v0

    invoke-static {v0, v1}, Lcom/bytedance/sdk/component/Ubf/Fj/hjc/ex;->Fj(Ljava/util/concurrent/atomic/AtomicLong;I)V
    :try_end_2
    .catch Ljava/lang/InterruptedException; {:try_start_2 .. :try_end_2} :catch_0
    .catchall {:try_start_2 .. :try_end_2} :catchall_0

    :try_start_3
    monitor-exit v2

    return-void

    :goto_4
    invoke-virtual {v0}, Ljava/lang/Throwable;->getMessage()Ljava/lang/String;

    :goto_5
    monitor-exit v2
    :try_end_3
    .catchall {:try_start_3 .. :try_end_3} :catchall_0

    return-void

    :goto_6
    monitor-exit v2

    throw v0

    :cond_6
    return-void
.end method

.method private Ubf()V
    .locals 6

    :cond_0
    :goto_0
    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->Fj()Z

    move-result v0

    if-eqz v0, :cond_4

    const/4 v0, 0x1

    :try_start_0
    sget-object v1, Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;->eV:Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;

    invoke-virtual {v1}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->BcC()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object v2

    invoke-static {v2, v0}, Lcom/bytedance/sdk/component/Ubf/Fj/hjc/ex;->Fj(Ljava/util/concurrent/atomic/AtomicLong;I)V

    iget-object v2, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->Ubf:Ljava/util/concurrent/PriorityBlockingQueue;

    sget-object v3, Ljava/util/concurrent/TimeUnit;->MILLISECONDS:Ljava/util/concurrent/TimeUnit;

    const-wide/32 v4, 0xea60

    invoke-virtual {v2, v4, v5, v3}, Ljava/util/concurrent/PriorityBlockingQueue;->poll(JLjava/util/concurrent/TimeUnit;)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;

    iget-object v3, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->Ubf:Ljava/util/concurrent/PriorityBlockingQueue;

    invoke-virtual {v3}, Ljava/util/concurrent/PriorityBlockingQueue;->size()I

    move-result v3

    instance-of v4, v2, Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex;

    if-eqz v4, :cond_1

    invoke-direct {p0, v2, v3}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->Fj(Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;I)V

    goto :goto_0

    :catchall_0
    move-exception v1

    goto :goto_1

    :cond_1
    if-nez v2, :cond_3

    iget-object v2, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->mSE:Ljava/util/concurrent/atomic/AtomicInteger;

    invoke-virtual {v2}, Ljava/util/concurrent/atomic/AtomicInteger;->incrementAndGet()I

    move-result v2

    invoke-virtual {v1}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->mj()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object v1

    invoke-static {v1, v0}, Lcom/bytedance/sdk/component/Ubf/Fj/hjc/ex;->Fj(Ljava/util/concurrent/atomic/AtomicLong;I)V

    invoke-direct {p0, v2}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->eV(I)Z

    move-result v1

    if-eqz v1, :cond_2

    invoke-direct {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->WR()V

    return-void

    :cond_2
    const/4 v1, 0x4

    if-ge v2, v1, :cond_0

    iput v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->WR:I

    const/4 v1, 0x0

    invoke-direct {p0, v1}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->ex(Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;)V

    goto :goto_0

    :cond_3
    invoke-direct {p0, v2}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->Fj(Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;)V

    invoke-direct {p0, v2}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->ex(Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    goto :goto_0

    :goto_1
    invoke-virtual {v1}, Ljava/lang/Throwable;->getMessage()Ljava/lang/String;

    sget-object v1, Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;->eV:Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;

    invoke-virtual {v1}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->iT()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object v1

    invoke-static {v1, v0}, Lcom/bytedance/sdk/component/Ubf/Fj/hjc/ex;->Fj(Ljava/util/concurrent/atomic/AtomicLong;I)V

    goto :goto_0

    :cond_4
    return-void
.end method

.method private Ubf(I)V
    .locals 3

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->Fj()Z

    move-result v0

    const/4 v1, 0x1

    if-nez v0, :cond_4

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->dG:Landroid/os/Handler;

    if-nez v0, :cond_0

    return-void

    :cond_0
    sget-object v0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;->eV:Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->hjc()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object v2

    invoke-static {v2, v1}, Lcom/bytedance/sdk/component/Ubf/Fj/hjc/ex;->Fj(Ljava/util/concurrent/atomic/AtomicLong;I)V

    iget-object v2, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->dG:Landroid/os/Handler;

    invoke-virtual {v2, v1}, Landroid/os/Handler;->hasMessages(I)Z

    move-result v2

    if-nez v2, :cond_5

    if-ne p1, v1, :cond_1

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->WR()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object p1

    invoke-static {p1, v1}, Lcom/bytedance/sdk/component/Ubf/Fj/hjc/ex;->Fj(Ljava/util/concurrent/atomic/AtomicLong;I)V

    goto :goto_0

    :cond_1
    const/4 v2, 0x2

    if-ne p1, v2, :cond_2

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->eV()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object p1

    invoke-static {p1, v1}, Lcom/bytedance/sdk/component/Ubf/Fj/hjc/ex;->Fj(Ljava/util/concurrent/atomic/AtomicLong;I)V

    goto :goto_0

    :cond_2
    const/4 v2, 0x3

    if-ne p1, v2, :cond_3

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->Ubf()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object p1

    invoke-static {p1, v1}, Lcom/bytedance/sdk/component/Ubf/Fj/hjc/ex;->Fj(Ljava/util/concurrent/atomic/AtomicLong;I)V

    :cond_3
    :goto_0
    iget-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->dG:Landroid/os/Handler;

    invoke-virtual {p1, v1}, Landroid/os/Handler;->sendEmptyMessage(I)Z

    return-void

    :cond_4
    sget-object p1, Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;->eV:Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->Fj()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object p1

    invoke-static {p1, v1}, Lcom/bytedance/sdk/component/Ubf/Fj/hjc/ex;->Fj(Ljava/util/concurrent/atomic/AtomicLong;I)V

    :cond_5
    return-void
.end method

.method private WR()V
    .locals 2

    sget-object v0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;->eV:Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->mC()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object v0

    const/4 v1, 0x1

    invoke-static {v0, v1}, Lcom/bytedance/sdk/component/Ubf/Fj/hjc/ex;->Fj(Ljava/util/concurrent/atomic/AtomicLong;I)V

    const/4 v0, 0x0

    invoke-virtual {p0, v0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->Fj(Z)V

    sget-object v0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;->Fj:Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;->hjc()V

    return-void
.end method

.method private eV()V
    .locals 1

    invoke-virtual {p0}, Ljava/lang/Thread;->isAlive()Z

    move-result v0

    if-nez v0, :cond_0

    sget-object v0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;->Fj:Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;->eV()Z

    return-void

    :cond_0
    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->Fj()Z

    move-result v0

    if-nez v0, :cond_1

    const/4 v0, 0x6

    invoke-virtual {p0, v0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->hjc(I)V

    :cond_1
    return-void
.end method

.method private eV(I)Z
    .locals 1

    const/4 v0, 0x4

    if-lt p1, v0, :cond_0

    iget-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->UYd:Ljava/util/concurrent/atomic/AtomicInteger;

    invoke-virtual {p1}, Ljava/util/concurrent/atomic/AtomicInteger;->get()I

    move-result p1

    if-nez p1, :cond_0

    sget-object p1, Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;->Fj:Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;

    iget-boolean v0, p1, Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;->ex:Z

    if-nez v0, :cond_0

    iget-boolean p1, p1, Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;->hjc:Z

    if-nez p1, :cond_0

    const/4 p1, 0x1

    return p1

    :cond_0
    const/4 p1, 0x0

    return p1
.end method

.method public static synthetic ex(Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;)Ljava/util/concurrent/atomic/AtomicInteger;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->UYd:Ljava/util/concurrent/atomic/AtomicInteger;

    return-object p0
.end method

.method private ex()V
    .locals 5

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->svN()Lcom/bytedance/sdk/component/Ubf/Fj/BcC;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->Ql()J

    move-result-wide v0

    const-wide/16 v2, 0x0

    cmp-long v4, v0, v2

    if-gtz v4, :cond_0

    return-void

    :cond_0
    iget-object v2, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->Fj:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/eV;

    const v3, 0x7fffffff

    invoke-interface {v2, v3, v0, v1}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/eV;->Fj(IJ)V

    return-void
.end method

.method public static ex(I)V
    .locals 0

    sput p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->rS:I

    return-void
.end method

.method private ex(Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;)V
    .locals 7

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/hjc/Fj;->ex()Z

    move-result v0

    if-eqz v0, :cond_0

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->svN()Lcom/bytedance/sdk/component/Ubf/Fj/BcC;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->Fj()Z

    move-result v0

    if-eqz v0, :cond_0

    return-void

    :cond_0
    invoke-direct {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->BcC()Z

    move-result v0

    const/4 v1, 0x1

    const/4 v2, 0x0

    if-eqz v0, :cond_3

    iget v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->WR:I

    invoke-static {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/hjc/Fj;->Fj(I)Ljava/lang/String;

    sget-object v0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;->eV:Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->UYd()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object v0

    invoke-static {v0, v1}, Lcom/bytedance/sdk/component/Ubf/Fj/hjc/ex;->Fj(Ljava/util/concurrent/atomic/AtomicLong;I)V

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->Ubf:Ljava/util/concurrent/PriorityBlockingQueue;

    invoke-virtual {v0}, Ljava/util/concurrent/PriorityBlockingQueue;->size()I

    move-result v0

    if-nez v0, :cond_2

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->dG:Landroid/os/Handler;

    const/4 v3, 0x2

    invoke-virtual {v0, v3}, Landroid/os/Handler;->hasMessages(I)Z

    move-result v0

    if-nez v0, :cond_1

    sget-object v0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;->Fj:Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;

    iput-boolean v2, v0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;->ex:Z

    const-wide/16 v3, 0x0

    iput-wide v3, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->BcC:J

    iput-wide v3, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->svN:J

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->JW:Ljava/util/concurrent/atomic/AtomicInteger;

    invoke-virtual {v0, v2}, Ljava/util/concurrent/atomic/AtomicInteger;->set(I)V

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->JU:Ljava/util/concurrent/atomic/AtomicInteger;

    invoke-virtual {v0, v2}, Ljava/util/concurrent/atomic/AtomicInteger;->set(I)V

    goto :goto_0

    :cond_1
    invoke-virtual {p0, v2}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->Fj(Z)V

    :cond_2
    return-void

    :cond_3
    :goto_0
    iget v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->WR:I

    sget-object v3, Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;->Fj:Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;

    iget-boolean v3, v3, Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;->ex:Z

    invoke-virtual {p0, v0, v3}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->Fj(IZ)Z

    move-result v0

    iget v3, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->WR:I

    invoke-static {v0, v3, p1}, Lcom/bytedance/sdk/component/Ubf/Fj/hjc/Fj;->Fj(ZILcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;)V

    sget-object v3, Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;->eV:Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;

    invoke-virtual {v3}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->dG()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object v3

    invoke-static {v3, v1}, Lcom/bytedance/sdk/component/Ubf/Fj/hjc/ex;->Fj(Ljava/util/concurrent/atomic/AtomicLong;I)V

    if-eqz v0, :cond_5

    iget-object v3, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->Fj:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/eV;

    iget v4, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->WR:I

    const/4 v5, -0x1

    const/4 v6, 0x0

    invoke-interface {v3, v4, v5, v6}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/eV;->Fj(IILjava/util/List;)Ljava/util/List;

    move-result-object v3

    if-eqz v3, :cond_4

    invoke-interface {v3}, Ljava/util/List;->size()I

    invoke-direct {p0, v3}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->Fj(Ljava/util/List;)V

    goto :goto_1

    :cond_4
    invoke-direct {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->mSE()V

    goto :goto_1

    :cond_5
    invoke-direct {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->mSE()V

    :goto_1
    add-int/2addr v2, v1

    if-eqz v0, :cond_6

    const/4 v0, 0x6

    if-le v2, v0, :cond_3

    :cond_6
    return-void
.end method

.method private ex(Ljava/util/List;)V
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;",
            ">;)V"
        }
    .end annotation

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->Tc:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    iget-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->Tc:Ljava/util/List;

    invoke-interface {p1}, Ljava/util/List;->size()I

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->svN()Lcom/bytedance/sdk/component/Ubf/Fj/BcC;

    move-result-object p1

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->JU()Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;

    move-result-object p1

    if-eqz p1, :cond_0

    invoke-interface {p1}, Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;->UYd()Lcom/bytedance/sdk/component/Ubf/Fj/svN;

    move-result-object v0

    if-eqz v0, :cond_0

    invoke-interface {p1}, Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;->UYd()Lcom/bytedance/sdk/component/Ubf/Fj/svN;

    move-result-object v0

    invoke-interface {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/svN;->ex()I

    move-result v0

    sput v0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->Ql:I

    :cond_0
    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->Tc:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    sget v1, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->Ql:I

    const/4 v2, 0x0

    const/16 v3, 0xb

    if-lt v0, v1, :cond_2

    iget-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->dG:Landroid/os/Handler;

    invoke-virtual {p1, v3}, Landroid/os/Handler;->hasMessages(I)Z

    move-result p1

    if-eqz p1, :cond_1

    iget-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->dG:Landroid/os/Handler;

    invoke-virtual {p1, v3}, Landroid/os/Handler;->removeMessages(I)V

    :cond_1
    new-instance p1, Ljava/util/ArrayList;

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->Tc:Ljava/util/List;

    invoke-direct {p1, v0}, Ljava/util/ArrayList;-><init>(Ljava/util/Collection;)V

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->Tc:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->clear()V

    const-string v0, "max_size_dispatch"

    invoke-direct {p0, p1, v2, v0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->Fj(Ljava/util/List;ZLjava/lang/String;)V

    invoke-direct {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->Ko()V

    return-void

    :cond_2
    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->Ubf:Ljava/util/concurrent/PriorityBlockingQueue;

    invoke-virtual {v0}, Ljava/util/concurrent/PriorityBlockingQueue;->size()I

    move-result v0

    if-nez v0, :cond_6

    invoke-virtual {p0, v2}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->Fj(Z)V

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->dG:Landroid/os/Handler;

    invoke-virtual {v0, v3}, Landroid/os/Handler;->hasMessages(I)Z

    move-result v0

    if-eqz v0, :cond_3

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->dG:Landroid/os/Handler;

    invoke-virtual {v0, v3}, Landroid/os/Handler;->removeMessages(I)V

    :cond_3
    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->dG:Landroid/os/Handler;

    const/4 v1, 0x1

    invoke-virtual {v0, v1}, Landroid/os/Handler;->hasMessages(I)Z

    move-result v0

    if-eqz v0, :cond_4

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->dG:Landroid/os/Handler;

    invoke-virtual {v0, v1}, Landroid/os/Handler;->removeMessages(I)V

    :cond_4
    sget v0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->rS:I

    int-to-long v0, v0

    if-eqz p1, :cond_5

    invoke-interface {p1}, Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;->UYd()Lcom/bytedance/sdk/component/Ubf/Fj/svN;

    move-result-object v2

    if-eqz v2, :cond_5

    invoke-interface {p1}, Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;->UYd()Lcom/bytedance/sdk/component/Ubf/Fj/svN;

    move-result-object p1

    invoke-interface {p1}, Lcom/bytedance/sdk/component/Ubf/Fj/svN;->Fj()J

    move-result-wide v0

    :cond_5
    iget-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->dG:Landroid/os/Handler;

    invoke-virtual {p1, v3, v0, v1}, Landroid/os/Handler;->sendEmptyMessageDelayed(IJ)Z

    return-void

    :cond_6
    iget-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->Tc:Ljava/util/List;

    invoke-interface {p1}, Ljava/util/List;->size()I

    return-void
.end method

.method private ex(Ljava/util/List;ZJ)V
    .locals 7
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;",
            ">;ZJ)V"
        }
    .end annotation

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->UYd:Ljava/util/concurrent/atomic/AtomicInteger;

    invoke-virtual {v0}, Ljava/util/concurrent/atomic/AtomicInteger;->incrementAndGet()I

    sget-object v0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;->eV:Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->flF()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object v0

    const/4 v1, 0x1

    invoke-static {v0, v1}, Lcom/bytedance/sdk/component/Ubf/Fj/hjc/ex;->Fj(Ljava/util/concurrent/atomic/AtomicLong;I)V

    :try_start_0
    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    invoke-interface {p1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v2

    :goto_0
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    move-result v3

    if-eqz v3, :cond_2

    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;

    if-nez v3, :cond_0

    const/4 v4, 0x0

    goto :goto_1

    :cond_0
    invoke-interface {v3}, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;->Ko()I

    move-result v4

    :goto_1
    invoke-static {v4}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v5

    invoke-interface {v0, v5}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v5

    if-nez v5, :cond_1

    invoke-static {v4}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v5

    new-instance v6, Ljava/util/ArrayList;

    invoke-direct {v6}, Ljava/util/ArrayList;-><init>()V

    invoke-interface {v0, v5, v6}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_2

    :catch_0
    move-exception p1

    goto :goto_5

    :cond_1
    :goto_2
    invoke-static {v4}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v4

    invoke-interface {v0, v4}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Ljava/util/List;

    invoke-interface {v4, v3}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_0

    :cond_2
    invoke-interface {v0}, Ljava/util/Map;->keySet()Ljava/util/Set;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_3
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_5

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Ljava/lang/Integer;

    invoke-virtual {v2}, Ljava/lang/Integer;->intValue()I

    move-result v2

    if-eqz v2, :cond_4

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->svN()Lcom/bytedance/sdk/component/Ubf/Fj/BcC;

    move-result-object v3

    invoke-virtual {v3}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->hjc()Ljava/util/Map;

    move-result-object v3

    if-eqz v3, :cond_4

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->svN()Lcom/bytedance/sdk/component/Ubf/Fj/BcC;

    move-result-object v3

    invoke-virtual {v3}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->hjc()Ljava/util/Map;

    move-result-object v3

    invoke-static {v2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v4

    invoke-interface {v3, v4}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    if-nez v3, :cond_3

    goto :goto_4

    :cond_3
    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->svN()Lcom/bytedance/sdk/component/Ubf/Fj/BcC;

    move-result-object v3

    invoke-virtual {v3}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->hjc()Ljava/util/Map;

    move-result-object v3

    invoke-static {v2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v2

    invoke-interface {v3, v2}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc;

    new-instance v3, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc$3;

    invoke-direct {v3, p0, p2, p3, p4}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc$3;-><init>(Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;ZJ)V

    invoke-interface {v2, p1, v3}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc;->Fj(Ljava/util/List;Lcom/bytedance/sdk/component/Ubf/Fj/ex/ex;)V

    goto :goto_3

    :cond_4
    :goto_4
    iget-object v2, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->eV:Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc;

    new-instance v3, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc$2;

    invoke-direct {v3, p0, p2, p3, p4}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc$2;-><init>(Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;ZJ)V

    invoke-interface {v2, p1, v3}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc;->Fj(Ljava/util/List;Lcom/bytedance/sdk/component/Ubf/Fj/ex/ex;)V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_3

    :cond_5
    return-void

    :goto_5
    invoke-virtual {p1}, Ljava/lang/Throwable;->getMessage()Ljava/lang/String;

    sget-object p1, Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;->eV:Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->iT()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object p1

    invoke-static {p1, v1}, Lcom/bytedance/sdk/component/Ubf/Fj/hjc/ex;->Fj(Ljava/util/concurrent/atomic/AtomicLong;I)V

    iget-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->UYd:Ljava/util/concurrent/atomic/AtomicInteger;

    invoke-virtual {p1}, Ljava/util/concurrent/atomic/AtomicInteger;->decrementAndGet()I

    return-void
.end method

.method private hjc()V
    .locals 2

    invoke-direct {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->eV()V

    sget-object v0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;->eV:Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->gXF()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object v0

    const/4 v1, 0x1

    invoke-static {v0, v1}, Lcom/bytedance/sdk/component/Ubf/Fj/hjc/ex;->Fj(Ljava/util/concurrent/atomic/AtomicLong;I)V

    invoke-virtual {p0, v1}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->hjc(I)V

    return-void
.end method

.method private mSE()V
    .locals 2

    :try_start_0
    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->Ubf:Ljava/util/concurrent/PriorityBlockingQueue;

    invoke-virtual {v0}, Ljava/util/concurrent/PriorityBlockingQueue;->size()I

    move-result v0

    if-nez v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->dG:Landroid/os/Handler;

    const/16 v1, 0xb

    invoke-virtual {v0, v1}, Landroid/os/Handler;->hasMessages(I)Z

    move-result v0

    if-eqz v0, :cond_0

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->Fj()Z

    move-result v0

    if-eqz v0, :cond_0

    const/4 v0, 0x0

    invoke-virtual {p0, v0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->Fj(Z)V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception v0

    goto :goto_1

    :cond_0
    :goto_0
    return-void

    :goto_1
    invoke-virtual {v0}, Ljava/lang/Throwable;->getMessage()Ljava/lang/String;

    return-void
.end method

.method private svN()V
    .locals 4

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->Ubf:Ljava/util/concurrent/PriorityBlockingQueue;

    invoke-virtual {v0}, Ljava/util/concurrent/PriorityBlockingQueue;->size()I

    move-result v0

    const/16 v1, 0x64

    if-lt v0, v1, :cond_1

    const/4 v0, 0x0

    :goto_0
    if-ge v0, v1, :cond_1

    iget-object v2, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->Ubf:Ljava/util/concurrent/PriorityBlockingQueue;

    invoke-virtual {v2}, Ljava/util/concurrent/PriorityBlockingQueue;->poll()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;

    instance-of v3, v2, Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex;

    if-nez v3, :cond_0

    if-eqz v2, :cond_0

    invoke-direct {p0, v2}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->Fj(Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;)V

    :cond_0
    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :cond_1
    return-void
.end method


# virtual methods
.method public Fj(IJ)V
    .locals 4

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->dG:Landroid/os/Handler;

    if-nez v0, :cond_0

    return-void

    :cond_0
    invoke-static {}, Landroid/os/Message;->obtain()Landroid/os/Message;

    move-result-object v0

    iput p1, v0, Landroid/os/Message;->what:I

    const/4 v1, 0x2

    if-ne p1, v1, :cond_1

    iget-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->JW:Ljava/util/concurrent/atomic/AtomicInteger;

    invoke-virtual {p1}, Ljava/util/concurrent/atomic/AtomicInteger;->incrementAndGet()I

    move-result p1

    add-int/lit8 p1, p1, -0x1

    rem-int/lit8 p1, p1, 0x4

    add-int/lit8 p1, p1, 0x1

    int-to-long v1, p1

    mul-long v1, v1, p2

    iget-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->dG:Landroid/os/Handler;

    invoke-virtual {p1, v0, v1, v2}, Landroid/os/Handler;->sendMessageDelayed(Landroid/os/Message;J)Z

    return-void

    :cond_1
    const/4 v1, 0x3

    if-ne p1, v1, :cond_2

    iget-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->JU:Ljava/util/concurrent/atomic/AtomicInteger;

    invoke-virtual {p1}, Ljava/util/concurrent/atomic/AtomicInteger;->incrementAndGet()I

    move-result p1

    iget-object v1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->dG:Landroid/os/Handler;

    add-int/lit8 p1, p1, -0x1

    rem-int/lit8 p1, p1, 0x4

    add-int/lit8 p1, p1, 0x1

    int-to-long v2, p1

    mul-long v2, v2, p2

    invoke-virtual {v1, v0, v2, v3}, Landroid/os/Handler;->sendMessageDelayed(Landroid/os/Message;J)Z

    :cond_2
    return-void
.end method

.method public Fj(Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;Z)V
    .locals 1

    if-nez p1, :cond_0

    return-void

    :cond_0
    invoke-interface {p1}, Lcom/bytedance/sdk/component/Ubf/Fj/eV/Fj;->eV()B

    if-eqz p2, :cond_2

    iget-object p2, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->dG:Landroid/os/Handler;

    if-eqz p2, :cond_1

    new-instance p2, Ljava/util/ArrayList;

    const/4 v0, 0x1

    invoke-direct {p2, v0}, Ljava/util/ArrayList;-><init>(I)V

    invoke-virtual {p2, p1}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    const-string p1, "ignore_result_dispatch"

    invoke-direct {p0, p2, v0, p1}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->Fj(Ljava/util/List;ZLjava/lang/String;)V

    :cond_1
    return-void

    :cond_2
    iget-object p2, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->Ubf:Ljava/util/concurrent/PriorityBlockingQueue;

    invoke-virtual {p2, p1}, Ljava/util/concurrent/PriorityBlockingQueue;->add(Ljava/lang/Object;)Z

    const/4 p1, 0x2

    invoke-direct {p0, p1}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->Ubf(I)V

    return-void
.end method

.method public Fj(Z)V
    .locals 0

    iput-boolean p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->ex:Z

    return-void
.end method

.method public Fj()Z
    .locals 1

    iget-boolean v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->ex:Z

    return v0
.end method

.method public Fj(IZ)Z
    .locals 2

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->svN()Lcom/bytedance/sdk/component/Ubf/Fj/BcC;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->JU()Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;

    move-result-object v0

    if-eqz v0, :cond_1

    invoke-static {}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->svN()Lcom/bytedance/sdk/component/Ubf/Fj/BcC;

    move-result-object v1

    invoke-virtual {v1}, Lcom/bytedance/sdk/component/Ubf/Fj/BcC;->WR()Landroid/content/Context;

    move-result-object v1

    invoke-interface {v0, v1}, Lcom/bytedance/sdk/component/Ubf/Fj/Ubf;->Fj(Landroid/content/Context;)Z

    move-result v0

    if-nez v0, :cond_0

    goto :goto_0

    :cond_0
    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->Fj:Lcom/bytedance/sdk/component/Ubf/Fj/Fj/eV;

    invoke-interface {v0, p1, p2}, Lcom/bytedance/sdk/component/Ubf/Fj/Fj/eV;->Fj(IZ)Z

    move-result p1

    return p1

    :cond_1
    :goto_0
    const/4 p1, 0x0

    return p1
.end method

.method public handleMessage(Landroid/os/Message;)Z
    .locals 3

    iget p1, p1, Landroid/os/Message;->what:I

    const/4 v0, 0x1

    if-eq p1, v0, :cond_2

    const/4 v1, 0x2

    if-eq p1, v1, :cond_1

    const/4 v1, 0x3

    if-eq p1, v1, :cond_1

    const/16 v1, 0xb

    if-eq p1, v1, :cond_0

    goto :goto_1

    :cond_0
    :try_start_0
    new-instance p1, Ljava/util/ArrayList;

    iget-object v1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->Tc:Ljava/util/List;

    invoke-direct {p1, v1}, Ljava/util/ArrayList;-><init>(Ljava/util/Collection;)V

    iget-object v1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->Tc:Ljava/util/List;

    invoke-interface {v1}, Ljava/util/List;->clear()V

    const-string v1, "timeout_dispatch"

    const/4 v2, 0x0

    invoke-direct {p0, p1, v2, v1}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->Fj(Ljava/util/List;ZLjava/lang/String;)V

    invoke-direct {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->Ko()V

    goto :goto_1

    :catchall_0
    move-exception p1

    goto :goto_0

    :cond_1
    invoke-direct {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->hjc()V

    goto :goto_1

    :cond_2
    sget-object p1, Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;->eV:Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/Fj/Fj;->mSE()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object p1

    invoke-static {p1, v0}, Lcom/bytedance/sdk/component/Ubf/Fj/hjc/ex;->Fj(Ljava/util/concurrent/atomic/AtomicLong;I)V

    invoke-direct {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->ex()V

    invoke-virtual {p0, v0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->Fj(Z)V

    invoke-direct {p0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->Ubf()V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    goto :goto_1

    :goto_0
    invoke-virtual {p1}, Ljava/lang/Throwable;->getMessage()Ljava/lang/String;

    :goto_1
    return v0
.end method

.method public hjc(I)V
    .locals 2

    :try_start_0
    sget-object v0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;->Fj:Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;

    iget-boolean v0, v0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;->ex:Z

    invoke-virtual {p0, p1, v0}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->Fj(IZ)Z

    move-result v0

    const/4 v1, 0x6

    if-eq p1, v1, :cond_0

    if-eqz v0, :cond_1

    :cond_0
    new-instance v0, Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex;

    invoke-direct {v0}, Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex;-><init>()V

    invoke-virtual {v0, p1}, Lcom/bytedance/sdk/component/Ubf/Fj/eV/ex;->ex(I)V

    iget-object p1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->Ubf:Ljava/util/concurrent/PriorityBlockingQueue;

    invoke-virtual {p1, v0}, Ljava/util/concurrent/PriorityBlockingQueue;->add(Ljava/lang/Object;)Z

    const/4 p1, 0x3

    invoke-direct {p0, p1}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->Ubf(I)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    :cond_1
    return-void

    :catchall_0
    move-exception p1

    invoke-virtual {p1}, Ljava/lang/Throwable;->getMessage()Ljava/lang/String;

    return-void
.end method

.method public onLooperPrepared()V
    .locals 2

    invoke-super {p0}, Landroid/os/HandlerThread;->onLooperPrepared()V

    new-instance v0, Landroid/os/Handler;

    invoke-virtual {p0}, Landroid/os/HandlerThread;->getLooper()Landroid/os/Looper;

    move-result-object v1

    invoke-direct {v0, v1, p0}, Landroid/os/Handler;-><init>(Landroid/os/Looper;Landroid/os/Handler$Callback;)V

    iput-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->dG:Landroid/os/Handler;

    sget-object v0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;->Fj:Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;

    iget-object v1, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->dG:Landroid/os/Handler;

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/component/Ubf/Fj/ex/eV;->Fj(Landroid/os/Handler;)V

    iget-object v0, p0, Lcom/bytedance/sdk/component/Ubf/Fj/ex/hjc/hjc;->dG:Landroid/os/Handler;

    const/4 v1, 0x1

    invoke-virtual {v0, v1}, Landroid/os/Handler;->sendEmptyMessage(I)Z

    return-void
.end method
