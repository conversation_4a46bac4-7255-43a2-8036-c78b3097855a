.class public Lcom/bytedance/sdk/component/WR/Fj;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/bytedance/sdk/component/WR/Fj$Fj;
    }
.end annotation


# instance fields
.field private Fj:Lcom/bytedance/sdk/component/ex/Fj/rAx;

.field private ex:Lcom/bytedance/sdk/component/WR/hjc/WR;

.field private hjc:I


# direct methods
.method private constructor <init>(Lcom/bytedance/sdk/component/WR/Fj$Fj;)V
    .locals 4

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    new-instance v0, Lcom/bytedance/sdk/component/ex/Fj/rAx$Fj;

    invoke-direct {v0}, Lcom/bytedance/sdk/component/ex/Fj/rAx$Fj;-><init>()V

    iget v1, p1, Lcom/bytedance/sdk/component/WR/Fj$Fj;->Fj:I

    int-to-long v1, v1

    sget-object v3, Ljava/util/concurrent/TimeUnit;->MILLISECONDS:Ljava/util/concurrent/TimeUnit;

    invoke-virtual {v0, v1, v2, v3}, Lcom/bytedance/sdk/component/ex/Fj/rAx$Fj;->Fj(JLjava/util/concurrent/TimeUnit;)Lcom/bytedance/sdk/component/ex/Fj/rAx$Fj;

    move-result-object v0

    iget v1, p1, Lcom/bytedance/sdk/component/WR/Fj$Fj;->hjc:I

    int-to-long v1, v1

    invoke-virtual {v0, v1, v2, v3}, Lcom/bytedance/sdk/component/ex/Fj/rAx$Fj;->hjc(JLjava/util/concurrent/TimeUnit;)Lcom/bytedance/sdk/component/ex/Fj/rAx$Fj;

    move-result-object v0

    iget v1, p1, Lcom/bytedance/sdk/component/WR/Fj$Fj;->ex:I

    int-to-long v1, v1

    invoke-virtual {v0, v1, v2, v3}, Lcom/bytedance/sdk/component/ex/Fj/rAx$Fj;->ex(JLjava/util/concurrent/TimeUnit;)Lcom/bytedance/sdk/component/ex/Fj/rAx$Fj;

    move-result-object v0

    iget-boolean v1, p1, Lcom/bytedance/sdk/component/WR/Fj$Fj;->eV:Z

    if-eqz v1, :cond_0

    new-instance v1, Lcom/bytedance/sdk/component/WR/hjc/WR;

    invoke-direct {v1}, Lcom/bytedance/sdk/component/WR/hjc/WR;-><init>()V

    iput-object v1, p0, Lcom/bytedance/sdk/component/WR/Fj;->ex:Lcom/bytedance/sdk/component/WR/hjc/WR;

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/component/ex/Fj/rAx$Fj;->Fj(Lcom/bytedance/sdk/component/ex/Fj/BcC;)Lcom/bytedance/sdk/component/ex/Fj/rAx$Fj;

    :cond_0
    iget-object v1, p1, Lcom/bytedance/sdk/component/WR/Fj$Fj;->Ubf:Ljava/util/List;

    if-eqz v1, :cond_1

    invoke-interface {v1}, Ljava/util/List;->size()I

    move-result v1

    if-lez v1, :cond_1

    iget-object v1, p1, Lcom/bytedance/sdk/component/WR/Fj$Fj;->Ubf:Ljava/util/List;

    invoke-interface {v1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :goto_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_1

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/bytedance/sdk/component/ex/Fj/BcC;

    invoke-virtual {v0, v2}, Lcom/bytedance/sdk/component/ex/Fj/rAx$Fj;->Fj(Lcom/bytedance/sdk/component/ex/Fj/BcC;)Lcom/bytedance/sdk/component/ex/Fj/rAx$Fj;

    goto :goto_0

    :cond_1
    invoke-static {p1}, Lcom/bytedance/sdk/component/WR/Fj$Fj;->Fj(Lcom/bytedance/sdk/component/WR/Fj$Fj;)Landroid/os/Bundle;

    move-result-object v1

    if-eqz v1, :cond_2

    invoke-static {p1}, Lcom/bytedance/sdk/component/WR/Fj$Fj;->Fj(Lcom/bytedance/sdk/component/WR/Fj$Fj;)Landroid/os/Bundle;

    :cond_2
    invoke-static {p1}, Lcom/bytedance/sdk/component/WR/Fj$Fj;->ex(Lcom/bytedance/sdk/component/WR/Fj$Fj;)Ljava/util/Set;

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/ex/Fj/rAx$Fj;->Fj()Lcom/bytedance/sdk/component/ex/Fj/rAx;

    move-result-object p1

    iput-object p1, p0, Lcom/bytedance/sdk/component/WR/Fj;->Fj:Lcom/bytedance/sdk/component/ex/Fj/rAx;

    return-void
.end method

.method public synthetic constructor <init>(Lcom/bytedance/sdk/component/WR/Fj$Fj;Lcom/bytedance/sdk/component/WR/Fj$1;)V
    .locals 0

    invoke-direct {p0, p1}, Lcom/bytedance/sdk/component/WR/Fj;-><init>(Lcom/bytedance/sdk/component/WR/Fj$Fj;)V

    return-void
.end method

.method public static Fj()V
    .locals 1

    sget-object v0, Lcom/bytedance/sdk/component/WR/eV/hjc$Fj;->Fj:Lcom/bytedance/sdk/component/WR/eV/hjc$Fj;

    invoke-static {v0}, Lcom/bytedance/sdk/component/WR/eV/hjc;->Fj(Lcom/bytedance/sdk/component/WR/eV/hjc$Fj;)V

    return-void
.end method

.method private static Fj(Landroid/content/Context;)Z
    .locals 1

    invoke-static {p0}, Lcom/bytedance/sdk/component/utils/rS;->ex(Landroid/content/Context;)Ljava/lang/String;

    move-result-object p0

    if-eqz p0, :cond_1

    const-string v0, ":push"

    invoke-virtual {p0, v0}, Ljava/lang/String;->endsWith(Ljava/lang/String;)Z

    move-result v0

    if-nez v0, :cond_0

    const-string v0, ":pushservice"

    invoke-virtual {p0, v0}, Ljava/lang/String;->endsWith(Ljava/lang/String;)Z

    move-result p0

    if-eqz p0, :cond_1

    :cond_0
    const/4 p0, 0x1

    return p0

    :cond_1
    const/4 p0, 0x0

    return p0
.end method


# virtual methods
.method public Fj(Landroid/content/Context;Z)V
    .locals 1

    const/4 v0, 0x1

    invoke-static {v0}, Lcom/bytedance/sdk/component/WR/hjc/Fj;->ex(Z)V

    invoke-static {p1}, Lcom/bytedance/sdk/component/WR/Fj;->Fj(Landroid/content/Context;)Z

    move-result v0

    if-nez v0, :cond_0

    invoke-static {p1}, Lcom/bytedance/sdk/component/utils/rS;->Fj(Landroid/content/Context;)Z

    move-result v0

    if-nez v0, :cond_1

    if-eqz p2, :cond_1

    :cond_0
    invoke-static {}, Lcom/bytedance/sdk/component/WR/hjc/svN;->Fj()Lcom/bytedance/sdk/component/WR/hjc/svN;

    move-result-object p2

    iget v0, p0, Lcom/bytedance/sdk/component/WR/Fj;->hjc:I

    invoke-virtual {p2, v0, p1}, Lcom/bytedance/sdk/component/WR/hjc/svN;->Fj(ILandroid/content/Context;)Lcom/bytedance/sdk/component/WR/hjc/Fj;

    move-result-object p2

    invoke-virtual {p2}, Lcom/bytedance/sdk/component/WR/hjc/Fj;->eV()V

    invoke-static {}, Lcom/bytedance/sdk/component/WR/hjc/svN;->Fj()Lcom/bytedance/sdk/component/WR/hjc/svN;

    move-result-object p2

    iget v0, p0, Lcom/bytedance/sdk/component/WR/Fj;->hjc:I

    invoke-virtual {p2, v0, p1}, Lcom/bytedance/sdk/component/WR/hjc/svN;->Fj(ILandroid/content/Context;)Lcom/bytedance/sdk/component/WR/hjc/Fj;

    move-result-object p2

    invoke-virtual {p2}, Lcom/bytedance/sdk/component/WR/hjc/Fj;->Fj()V

    :cond_1
    invoke-static {p1}, Lcom/bytedance/sdk/component/utils/rS;->Fj(Landroid/content/Context;)Z

    move-result p2

    if-nez p2, :cond_2

    return-void

    :cond_2
    invoke-static {}, Lcom/bytedance/sdk/component/WR/hjc/svN;->Fj()Lcom/bytedance/sdk/component/WR/hjc/svN;

    move-result-object p2

    iget v0, p0, Lcom/bytedance/sdk/component/WR/Fj;->hjc:I

    invoke-virtual {p2, v0, p1}, Lcom/bytedance/sdk/component/WR/hjc/svN;->Fj(ILandroid/content/Context;)Lcom/bytedance/sdk/component/WR/hjc/Fj;

    move-result-object p2

    invoke-virtual {p2}, Lcom/bytedance/sdk/component/WR/hjc/Fj;->eV()V

    invoke-static {}, Lcom/bytedance/sdk/component/WR/hjc/svN;->Fj()Lcom/bytedance/sdk/component/WR/hjc/svN;

    move-result-object p2

    iget v0, p0, Lcom/bytedance/sdk/component/WR/Fj;->hjc:I

    invoke-virtual {p2, v0, p1}, Lcom/bytedance/sdk/component/WR/hjc/svN;->Fj(ILandroid/content/Context;)Lcom/bytedance/sdk/component/WR/hjc/Fj;

    move-result-object p1

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/WR/hjc/Fj;->Fj()V

    return-void
.end method

.method public Fj(Landroid/content/Context;ZLcom/bytedance/sdk/component/WR/hjc/ex;)V
    .locals 2

    if-eqz p1, :cond_2

    if-eqz p3, :cond_1

    invoke-interface {p3}, Lcom/bytedance/sdk/component/WR/hjc/ex;->Fj()I

    move-result v0

    iput v0, p0, Lcom/bytedance/sdk/component/WR/Fj;->hjc:I

    iget-object v1, p0, Lcom/bytedance/sdk/component/WR/Fj;->ex:Lcom/bytedance/sdk/component/WR/hjc/WR;

    if-eqz v1, :cond_0

    invoke-virtual {v1, v0}, Lcom/bytedance/sdk/component/WR/hjc/WR;->Fj(I)V

    :cond_0
    invoke-static {}, Lcom/bytedance/sdk/component/WR/hjc/svN;->Fj()Lcom/bytedance/sdk/component/WR/hjc/svN;

    move-result-object v0

    iget v1, p0, Lcom/bytedance/sdk/component/WR/Fj;->hjc:I

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/component/WR/hjc/svN;->Fj(I)Lcom/bytedance/sdk/component/WR/hjc/Ubf;

    move-result-object v0

    invoke-virtual {v0, p2}, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->Fj(Z)V

    invoke-static {}, Lcom/bytedance/sdk/component/WR/hjc/svN;->Fj()Lcom/bytedance/sdk/component/WR/hjc/svN;

    move-result-object p2

    iget v0, p0, Lcom/bytedance/sdk/component/WR/Fj;->hjc:I

    invoke-virtual {p2, v0}, Lcom/bytedance/sdk/component/WR/hjc/svN;->Fj(I)Lcom/bytedance/sdk/component/WR/hjc/Ubf;

    move-result-object p2

    invoke-virtual {p2, p3}, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->Fj(Lcom/bytedance/sdk/component/WR/hjc/ex;)V

    invoke-static {}, Lcom/bytedance/sdk/component/WR/hjc/svN;->Fj()Lcom/bytedance/sdk/component/WR/hjc/svN;

    move-result-object p2

    iget p3, p0, Lcom/bytedance/sdk/component/WR/Fj;->hjc:I

    invoke-virtual {p2, p3}, Lcom/bytedance/sdk/component/WR/hjc/svN;->Fj(I)Lcom/bytedance/sdk/component/WR/hjc/Ubf;

    move-result-object p2

    invoke-static {p1}, Lcom/bytedance/sdk/component/utils/rS;->Fj(Landroid/content/Context;)Z

    move-result p3

    invoke-virtual {p2, p1, p3}, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->Fj(Landroid/content/Context;Z)V

    return-void

    :cond_1
    new-instance p1, Ljava/lang/IllegalArgumentException;

    const-string p2, "tryInitAdTTNet ITTAdNetDepend is null"

    invoke-direct {p1, p2}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p1

    :cond_2
    new-instance p1, Ljava/lang/IllegalArgumentException;

    const-string p2, "tryInitAdTTNet context is null"

    invoke-direct {p1, p2}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public Ubf()Lcom/bytedance/sdk/component/ex/Fj/rAx;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/WR/Fj;->Fj:Lcom/bytedance/sdk/component/ex/Fj/rAx;

    return-object v0
.end method

.method public eV()Lcom/bytedance/sdk/component/WR/ex/Fj;
    .locals 2

    new-instance v0, Lcom/bytedance/sdk/component/WR/ex/Fj;

    iget-object v1, p0, Lcom/bytedance/sdk/component/WR/Fj;->Fj:Lcom/bytedance/sdk/component/ex/Fj/rAx;

    invoke-direct {v0, v1}, Lcom/bytedance/sdk/component/WR/ex/Fj;-><init>(Lcom/bytedance/sdk/component/ex/Fj/rAx;)V

    return-object v0
.end method

.method public ex()Lcom/bytedance/sdk/component/WR/ex/eV;
    .locals 2

    new-instance v0, Lcom/bytedance/sdk/component/WR/ex/eV;

    iget-object v1, p0, Lcom/bytedance/sdk/component/WR/Fj;->Fj:Lcom/bytedance/sdk/component/ex/Fj/rAx;

    invoke-direct {v0, v1}, Lcom/bytedance/sdk/component/WR/ex/eV;-><init>(Lcom/bytedance/sdk/component/ex/Fj/rAx;)V

    return-object v0
.end method

.method public hjc()Lcom/bytedance/sdk/component/WR/ex/ex;
    .locals 2

    new-instance v0, Lcom/bytedance/sdk/component/WR/ex/ex;

    iget-object v1, p0, Lcom/bytedance/sdk/component/WR/Fj;->Fj:Lcom/bytedance/sdk/component/ex/Fj/rAx;

    invoke-direct {v0, v1}, Lcom/bytedance/sdk/component/WR/ex/ex;-><init>(Lcom/bytedance/sdk/component/ex/Fj/rAx;)V

    return-object v0
.end method
