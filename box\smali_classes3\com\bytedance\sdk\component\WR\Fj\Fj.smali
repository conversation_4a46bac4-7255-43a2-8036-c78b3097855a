.class public abstract Lcom/bytedance/sdk/component/WR/Fj/Fj;
.super Ljava/lang/Object;


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public abstract Fj(Lcom/bytedance/sdk/component/WR/ex/hjc;Lcom/bytedance/sdk/component/WR/ex;)V
.end method

.method public abstract Fj(Lcom/bytedance/sdk/component/WR/ex/hjc;Ljava/io/IOException;)V
.end method
