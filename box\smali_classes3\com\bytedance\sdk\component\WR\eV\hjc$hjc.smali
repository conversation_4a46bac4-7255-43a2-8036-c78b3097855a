.class Lcom/bytedance/sdk/component/WR/eV/hjc$hjc;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bytedance/sdk/component/WR/eV/hjc;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "hjc"
.end annotation


# static fields
.field private static final Fj:Lcom/bytedance/sdk/component/WR/eV/hjc;


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lcom/bytedance/sdk/component/WR/eV/hjc;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lcom/bytedance/sdk/component/WR/eV/hjc;-><init>(Lcom/bytedance/sdk/component/WR/eV/hjc$1;)V

    sput-object v0, Lcom/bytedance/sdk/component/WR/eV/hjc$hjc;->Fj:Lcom/bytedance/sdk/component/WR/eV/hjc;

    return-void
.end method

.method public static synthetic Fj()Lcom/bytedance/sdk/component/WR/eV/hjc;
    .locals 1

    sget-object v0, Lcom/bytedance/sdk/component/WR/eV/hjc$hjc;->Fj:Lcom/bytedance/sdk/component/WR/eV/hjc;

    return-object v0
.end method
