.class public Lcom/bytedance/sdk/component/WR/eV/hjc;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/bytedance/sdk/component/WR/eV/hjc$hjc;,
        Lcom/bytedance/sdk/component/WR/eV/hjc$ex;,
        Lcom/bytedance/sdk/component/WR/eV/hjc$Fj;
    }
.end annotation


# instance fields
.field private Fj:Lcom/bytedance/sdk/component/WR/eV/hjc$Fj;

.field private ex:Lcom/bytedance/sdk/component/WR/eV/hjc$ex;


# direct methods
.method private constructor <init>()V
    .locals 1

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    sget-object v0, Lcom/bytedance/sdk/component/WR/eV/hjc$Fj;->eV:Lcom/bytedance/sdk/component/WR/eV/hjc$Fj;

    iput-object v0, p0, Lcom/bytedance/sdk/component/WR/eV/hjc;->Fj:Lcom/bytedance/sdk/component/WR/eV/hjc$Fj;

    new-instance v0, Lcom/bytedance/sdk/component/WR/eV/ex;

    invoke-direct {v0}, Lcom/bytedance/sdk/component/WR/eV/ex;-><init>()V

    iput-object v0, p0, Lcom/bytedance/sdk/component/WR/eV/hjc;->ex:Lcom/bytedance/sdk/component/WR/eV/hjc$ex;

    return-void
.end method

.method public synthetic constructor <init>(Lcom/bytedance/sdk/component/WR/eV/hjc$1;)V
    .locals 0

    invoke-direct {p0}, Lcom/bytedance/sdk/component/WR/eV/hjc;-><init>()V

    return-void
.end method

.method public static Fj(Lcom/bytedance/sdk/component/WR/eV/hjc$Fj;)V
    .locals 2

    const-class v0, Lcom/bytedance/sdk/component/WR/eV/hjc;

    monitor-enter v0

    :try_start_0
    invoke-static {}, Lcom/bytedance/sdk/component/WR/eV/hjc$hjc;->Fj()Lcom/bytedance/sdk/component/WR/eV/hjc;

    move-result-object v1

    iput-object p0, v1, Lcom/bytedance/sdk/component/WR/eV/hjc;->Fj:Lcom/bytedance/sdk/component/WR/eV/hjc$Fj;

    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    return-void

    :catchall_0
    move-exception p0

    monitor-exit v0

    throw p0
.end method
