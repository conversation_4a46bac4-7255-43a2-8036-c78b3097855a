.class public Lcom/bytedance/sdk/component/WR/ex;
.super Ljava/lang/Object;


# instance fields
.field private BcC:Ljava/io/File;

.field final Fj:I

.field private Ko:[B

.field final Ubf:J

.field final WR:J

.field final eV:Ljava/lang/String;

.field final ex:Ljava/lang/String;

.field final hjc:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field private final mSE:Z

.field svN:Lcom/bytedance/sdk/component/ex/Fj/Ko;


# direct methods
.method public constructor <init>(ZILjava/lang/String;Ljava/util/Map;Ljava/lang/String;JJ)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(ZI",
            "Ljava/lang/String;",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ">;",
            "Ljava/lang/String;",
            "JJ)V"
        }
    .end annotation

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, 0x0

    iput-object v0, p0, Lcom/bytedance/sdk/component/WR/ex;->BcC:Ljava/io/File;

    iput-object v0, p0, Lcom/bytedance/sdk/component/WR/ex;->Ko:[B

    iput-boolean p1, p0, Lcom/bytedance/sdk/component/WR/ex;->mSE:Z

    iput p2, p0, Lcom/bytedance/sdk/component/WR/ex;->Fj:I

    iput-object p3, p0, Lcom/bytedance/sdk/component/WR/ex;->ex:Ljava/lang/String;

    iput-object p4, p0, Lcom/bytedance/sdk/component/WR/ex;->hjc:Ljava/util/Map;

    iput-object p5, p0, Lcom/bytedance/sdk/component/WR/ex;->eV:Ljava/lang/String;

    iput-wide p6, p0, Lcom/bytedance/sdk/component/WR/ex;->Ubf:J

    iput-wide p8, p0, Lcom/bytedance/sdk/component/WR/ex;->WR:J

    return-void
.end method


# virtual methods
.method public Fj()I
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/component/WR/ex;->Fj:I

    return v0
.end method

.method public Fj(Lcom/bytedance/sdk/component/ex/Fj/Ko;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/WR/ex;->svN:Lcom/bytedance/sdk/component/ex/Fj/Ko;

    return-void
.end method

.method public Fj(Ljava/io/File;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/WR/ex;->BcC:Ljava/io/File;

    return-void
.end method

.method public Fj([B)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/WR/ex;->Ko:[B

    return-void
.end method

.method public Ubf()Ljava/io/File;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/WR/ex;->BcC:Ljava/io/File;

    return-object v0
.end method

.method public WR()Z
    .locals 1

    iget-boolean v0, p0, Lcom/bytedance/sdk/component/WR/ex;->mSE:Z

    return v0
.end method

.method public eV()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/WR/ex;->eV:Ljava/lang/String;

    return-object v0
.end method

.method public ex()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/WR/ex;->ex:Ljava/lang/String;

    return-object v0
.end method

.method public hjc()Ljava/util/Map;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    iget-object v0, p0, Lcom/bytedance/sdk/component/WR/ex;->hjc:Ljava/util/Map;

    return-object v0
.end method

.method public svN()J
    .locals 4

    iget-wide v0, p0, Lcom/bytedance/sdk/component/WR/ex;->Ubf:J

    iget-wide v2, p0, Lcom/bytedance/sdk/component/WR/ex;->WR:J

    sub-long/2addr v0, v2

    return-wide v0
.end method
