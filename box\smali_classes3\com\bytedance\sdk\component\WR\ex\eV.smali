.class public Lcom/bytedance/sdk/component/WR/ex/eV;
.super Lcom/bytedance/sdk/component/WR/ex/hjc;


# instance fields
.field Fj:Lcom/bytedance/sdk/component/ex/Fj/Tc;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/component/ex/Fj/rAx;)V
    .locals 0

    invoke-direct {p0, p1}, Lcom/bytedance/sdk/component/WR/ex/hjc;-><init>(Lcom/bytedance/sdk/component/ex/Fj/rAx;)V

    const/4 p1, 0x0

    iput-object p1, p0, Lcom/bytedance/sdk/component/WR/ex/eV;->Fj:Lcom/bytedance/sdk/component/ex/Fj/Tc;

    return-void
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/component/WR/ex/eV;Lcom/bytedance/sdk/component/ex/Fj/JU;)Lcom/bytedance/sdk/component/ex/Fj/mSE;
    .locals 0

    invoke-direct {p0, p1}, Lcom/bytedance/sdk/component/WR/ex/eV;->Fj(Lcom/bytedance/sdk/component/ex/Fj/JU;)Lcom/bytedance/sdk/component/ex/Fj/mSE;

    move-result-object p0

    return-object p0
.end method

.method private Fj(Lcom/bytedance/sdk/component/ex/Fj/JU;)Lcom/bytedance/sdk/component/ex/Fj/mSE;
    .locals 0

    :try_start_0
    invoke-virtual {p1}, Lcom/bytedance/sdk/component/ex/Fj/JU;->Ubf()Lcom/bytedance/sdk/component/ex/Fj/mSE;

    move-result-object p1
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    return-object p1

    :catch_0
    const/4 p1, 0x0

    return-object p1
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/component/WR/ex/eV;Lcom/bytedance/sdk/component/ex/Fj/mSE;)Ljava/nio/charset/Charset;
    .locals 0

    invoke-direct {p0, p1}, Lcom/bytedance/sdk/component/WR/ex/eV;->Fj(Lcom/bytedance/sdk/component/ex/Fj/mSE;)Ljava/nio/charset/Charset;

    move-result-object p0

    return-object p0
.end method

.method private Fj(Lcom/bytedance/sdk/component/ex/Fj/mSE;)Ljava/nio/charset/Charset;
    .locals 1

    if-eqz p1, :cond_0

    :try_start_0
    sget-object v0, Lcom/bytedance/sdk/component/ex/Fj/ex/mSE;->Fj:Ljava/nio/charset/Charset;

    invoke-virtual {p1, v0}, Lcom/bytedance/sdk/component/ex/Fj/mSE;->Fj(Ljava/nio/charset/Charset;)Ljava/nio/charset/Charset;

    move-result-object p1

    return-object p1

    :cond_0
    sget-object p1, Lcom/bytedance/sdk/component/ex/Fj/ex/mSE;->Fj:Ljava/nio/charset/Charset;
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    return-object p1

    :catch_0
    sget-object p1, Lcom/bytedance/sdk/component/ex/Fj/ex/mSE;->Fj:Ljava/nio/charset/Charset;

    return-object p1
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/component/WR/ex/eV;Lcom/bytedance/sdk/component/WR/ex;Lcom/bytedance/sdk/component/ex/Fj/JW;)V
    .locals 0

    invoke-direct {p0, p1, p2}, Lcom/bytedance/sdk/component/WR/ex/eV;->Fj(Lcom/bytedance/sdk/component/WR/ex;Lcom/bytedance/sdk/component/ex/Fj/JW;)V

    return-void
.end method

.method private Fj(Lcom/bytedance/sdk/component/WR/ex;Lcom/bytedance/sdk/component/ex/Fj/JW;)V
    .locals 0

    if-eqz p1, :cond_0

    if-eqz p2, :cond_0

    invoke-virtual {p2}, Lcom/bytedance/sdk/component/ex/Fj/JW;->mSE()Lcom/bytedance/sdk/component/ex/Fj/Ko;

    move-result-object p2

    invoke-virtual {p1, p2}, Lcom/bytedance/sdk/component/WR/ex;->Fj(Lcom/bytedance/sdk/component/ex/Fj/Ko;)V

    :cond_0
    return-void
.end method

.method private eV(Ljava/lang/String;)[B
    .locals 5

    const-string v0, "PostExecutor"

    const/4 v1, 0x0

    if-eqz p1, :cond_5

    invoke-virtual {p1}, Ljava/lang/String;->length()I

    move-result v2

    if-nez v2, :cond_0

    goto/16 :goto_7

    :cond_0
    const/4 v2, 0x0

    new-array v2, v2, [B

    :try_start_0
    new-instance v3, Ljava/io/ByteArrayOutputStream;

    invoke-direct {v3}, Ljava/io/ByteArrayOutputStream;-><init>()V
    :try_end_0
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_4
    .catchall {:try_start_0 .. :try_end_0} :catchall_2

    :try_start_1
    new-instance v4, Ljava/util/zip/GZIPOutputStream;

    invoke-direct {v4, v3}, Ljava/util/zip/GZIPOutputStream;-><init>(Ljava/io/OutputStream;)V
    :try_end_1
    .catch Ljava/io/IOException; {:try_start_1 .. :try_end_1} :catch_3
    .catchall {:try_start_1 .. :try_end_1} :catchall_1

    :try_start_2
    const-string v1, "utf-8"

    invoke-virtual {p1, v1}, Ljava/lang/String;->getBytes(Ljava/lang/String;)[B

    move-result-object p1

    invoke-virtual {v4, p1}, Ljava/io/OutputStream;->write([B)V
    :try_end_2
    .catch Ljava/io/IOException; {:try_start_2 .. :try_end_2} :catch_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_0

    :try_start_3
    invoke-virtual {v4}, Ljava/io/OutputStream;->close()V
    :try_end_3
    .catch Ljava/io/IOException; {:try_start_3 .. :try_end_3} :catch_0

    goto :goto_0

    :catch_0
    move-exception p1

    invoke-virtual {p1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-static {v0, p1}, Lcom/bytedance/sdk/component/utils/dG;->Fj(Ljava/lang/String;Ljava/lang/String;)V

    :goto_0
    invoke-virtual {v3}, Ljava/io/ByteArrayOutputStream;->toByteArray()[B

    move-result-object v2

    :try_start_4
    invoke-virtual {v3}, Ljava/io/ByteArrayOutputStream;->close()V
    :try_end_4
    .catch Ljava/io/IOException; {:try_start_4 .. :try_end_4} :catch_1

    goto :goto_3

    :catch_1
    move-exception p1

    invoke-virtual {p1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-static {v0, p1}, Lcom/bytedance/sdk/component/utils/dG;->Fj(Ljava/lang/String;Ljava/lang/String;)V

    goto :goto_3

    :catchall_0
    move-exception p1

    move-object v1, v4

    goto :goto_4

    :catch_2
    move-exception p1

    move-object v1, v4

    goto :goto_1

    :catchall_1
    move-exception p1

    goto :goto_4

    :catch_3
    move-exception p1

    goto :goto_1

    :catchall_2
    move-exception p1

    move-object v3, v1

    goto :goto_4

    :catch_4
    move-exception p1

    move-object v3, v1

    :goto_1
    :try_start_5
    invoke-virtual {p1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-static {v0, p1}, Lcom/bytedance/sdk/component/utils/dG;->Fj(Ljava/lang/String;Ljava/lang/String;)V
    :try_end_5
    .catchall {:try_start_5 .. :try_end_5} :catchall_1

    if-eqz v1, :cond_1

    :try_start_6
    invoke-virtual {v1}, Ljava/io/OutputStream;->close()V
    :try_end_6
    .catch Ljava/io/IOException; {:try_start_6 .. :try_end_6} :catch_5

    goto :goto_2

    :catch_5
    move-exception p1

    invoke-virtual {p1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-static {v0, p1}, Lcom/bytedance/sdk/component/utils/dG;->Fj(Ljava/lang/String;Ljava/lang/String;)V

    :cond_1
    :goto_2
    if-eqz v3, :cond_2

    invoke-virtual {v3}, Ljava/io/ByteArrayOutputStream;->toByteArray()[B

    move-result-object v2

    :try_start_7
    invoke-virtual {v3}, Ljava/io/ByteArrayOutputStream;->close()V
    :try_end_7
    .catch Ljava/io/IOException; {:try_start_7 .. :try_end_7} :catch_1

    :cond_2
    :goto_3
    return-object v2

    :goto_4
    if-eqz v1, :cond_3

    :try_start_8
    invoke-virtual {v1}, Ljava/io/OutputStream;->close()V
    :try_end_8
    .catch Ljava/io/IOException; {:try_start_8 .. :try_end_8} :catch_6

    goto :goto_5

    :catch_6
    move-exception v1

    invoke-virtual {v1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-static {v0, v1}, Lcom/bytedance/sdk/component/utils/dG;->Fj(Ljava/lang/String;Ljava/lang/String;)V

    :cond_3
    :goto_5
    if-eqz v3, :cond_4

    invoke-virtual {v3}, Ljava/io/ByteArrayOutputStream;->toByteArray()[B

    :try_start_9
    invoke-virtual {v3}, Ljava/io/ByteArrayOutputStream;->close()V
    :try_end_9
    .catch Ljava/io/IOException; {:try_start_9 .. :try_end_9} :catch_7

    goto :goto_6

    :catch_7
    move-exception v1

    invoke-virtual {v1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-static {v0, v1}, Lcom/bytedance/sdk/component/utils/dG;->Fj(Ljava/lang/String;Ljava/lang/String;)V

    :cond_4
    :goto_6
    throw p1

    :cond_5
    :goto_7
    return-object v1
.end method


# virtual methods
.method public Fj()Lcom/bytedance/sdk/component/WR/ex;
    .locals 13

    const-string v0, "content-type"

    :try_start_0
    new-instance v1, Lcom/bytedance/sdk/component/ex/Fj/dG$Fj;

    invoke-direct {v1}, Lcom/bytedance/sdk/component/ex/Fj/dG$Fj;-><init>()V

    iget-object v2, p0, Lcom/bytedance/sdk/component/WR/ex/hjc;->WR:Ljava/lang/String;

    invoke-static {v2}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v2

    if-eqz v2, :cond_0

    new-instance v0, Lcom/bytedance/sdk/component/WR/ex;

    const/4 v4, 0x0

    const/16 v5, 0x1388

    const-string v6, "URL_NULL_MSG"

    const/4 v7, 0x0

    const-string v8, "URL_NULL_BODY"

    const-wide/16 v9, 0x1

    const-wide/16 v11, 0x1

    move-object v3, v0

    invoke-direct/range {v3 .. v12}, Lcom/bytedance/sdk/component/WR/ex;-><init>(ZILjava/lang/String;Ljava/util/Map;Ljava/lang/String;JJ)V

    return-object v0

    :catchall_0
    move-exception v0

    goto/16 :goto_3

    :cond_0
    iget-object v2, p0, Lcom/bytedance/sdk/component/WR/ex/hjc;->WR:Ljava/lang/String;

    invoke-virtual {v1, v2}, Lcom/bytedance/sdk/component/ex/Fj/dG$Fj;->Fj(Ljava/lang/String;)Lcom/bytedance/sdk/component/ex/Fj/dG$Fj;

    iget-object v2, p0, Lcom/bytedance/sdk/component/WR/ex/eV;->Fj:Lcom/bytedance/sdk/component/ex/Fj/Tc;

    if-nez v2, :cond_1

    new-instance v0, Lcom/bytedance/sdk/component/WR/ex;

    const/4 v4, 0x0

    const/16 v5, 0x1388

    const-string v6, "BODY_NULL_MSG"

    const/4 v7, 0x0

    const-string v8, "BODY_NULL_BODY"

    const-wide/16 v9, 0x1

    const-wide/16 v11, 0x1

    move-object v3, v0

    invoke-direct/range {v3 .. v12}, Lcom/bytedance/sdk/component/WR/ex;-><init>(ZILjava/lang/String;Ljava/util/Map;Ljava/lang/String;JJ)V

    return-object v0

    :cond_1
    invoke-virtual {p0, v1}, Lcom/bytedance/sdk/component/WR/ex/hjc;->Fj(Lcom/bytedance/sdk/component/ex/Fj/dG$Fj;)V

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/WR/ex/hjc;->ex()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v2}, Lcom/bytedance/sdk/component/ex/Fj/dG$Fj;->Fj(Ljava/lang/Object;)Lcom/bytedance/sdk/component/ex/Fj/dG$Fj;

    iget-object v2, p0, Lcom/bytedance/sdk/component/WR/ex/eV;->Fj:Lcom/bytedance/sdk/component/ex/Fj/Tc;

    invoke-virtual {v1, v2}, Lcom/bytedance/sdk/component/ex/Fj/dG$Fj;->Fj(Lcom/bytedance/sdk/component/ex/Fj/Tc;)Lcom/bytedance/sdk/component/ex/Fj/dG$Fj;

    move-result-object v1

    invoke-virtual {v1}, Lcom/bytedance/sdk/component/ex/Fj/dG$Fj;->ex()Lcom/bytedance/sdk/component/ex/Fj/dG;

    move-result-object v1

    iget-object v2, p0, Lcom/bytedance/sdk/component/WR/ex/hjc;->hjc:Lcom/bytedance/sdk/component/ex/Fj/rAx;

    invoke-virtual {v2, v1}, Lcom/bytedance/sdk/component/ex/Fj/rAx;->Fj(Lcom/bytedance/sdk/component/ex/Fj/dG;)Lcom/bytedance/sdk/component/ex/Fj/ex;

    move-result-object v1

    invoke-interface {v1}, Lcom/bytedance/sdk/component/ex/Fj/ex;->Fj()Lcom/bytedance/sdk/component/ex/Fj/JW;

    move-result-object v1

    if-eqz v1, :cond_7

    new-instance v6, Ljava/util/HashMap;

    invoke-direct {v6}, Ljava/util/HashMap;-><init>()V

    invoke-virtual {v1}, Lcom/bytedance/sdk/component/ex/Fj/JW;->svN()Lcom/bytedance/sdk/component/ex/Fj/WR;

    move-result-object v2

    if-eqz v2, :cond_4

    const/4 v3, 0x0

    :goto_0
    invoke-virtual {v2}, Lcom/bytedance/sdk/component/ex/Fj/WR;->Fj()I

    move-result v4

    if-ge v3, v4, :cond_4

    invoke-virtual {v2, v3}, Lcom/bytedance/sdk/component/ex/Fj/WR;->Fj(I)Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v2, v3}, Lcom/bytedance/sdk/component/ex/Fj/WR;->ex(I)Ljava/lang/String;

    move-result-object v5

    invoke-interface {v6, v4, v5}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    if-eqz v4, :cond_3

    invoke-virtual {v4, v0}, Ljava/lang/String;->equalsIgnoreCase(Ljava/lang/String;)Z

    move-result v4

    if-eqz v4, :cond_3

    if-nez v5, :cond_2

    const-string v4, ""

    goto :goto_1

    :cond_2
    invoke-virtual {v5}, Ljava/lang/String;->toLowerCase()Ljava/lang/String;

    move-result-object v4

    :goto_1
    invoke-interface {v6, v0, v4}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    :cond_3
    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    :cond_4
    invoke-static {v6}, Lcom/bytedance/sdk/component/WR/eV/Fj;->Fj(Ljava/util/Map;)Z

    move-result v0

    if-eqz v0, :cond_5

    invoke-virtual {v1}, Lcom/bytedance/sdk/component/ex/Fj/JW;->WR()Lcom/bytedance/sdk/component/ex/Fj/JU;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/ex/Fj/JU;->eV()[B

    move-result-object v0

    new-instance v12, Lcom/bytedance/sdk/component/WR/ex;

    invoke-virtual {v1}, Lcom/bytedance/sdk/component/ex/Fj/JW;->eV()Z

    move-result v3

    invoke-virtual {v1}, Lcom/bytedance/sdk/component/ex/Fj/JW;->hjc()I

    move-result v4

    invoke-virtual {v1}, Lcom/bytedance/sdk/component/ex/Fj/JW;->Ubf()Ljava/lang/String;

    move-result-object v5

    const/4 v7, 0x0

    invoke-virtual {v1}, Lcom/bytedance/sdk/component/ex/Fj/JW;->ex()J

    move-result-wide v8

    invoke-virtual {v1}, Lcom/bytedance/sdk/component/ex/Fj/JW;->Fj()J

    move-result-wide v10

    move-object v2, v12

    invoke-direct/range {v2 .. v11}, Lcom/bytedance/sdk/component/WR/ex;-><init>(ZILjava/lang/String;Ljava/util/Map;Ljava/lang/String;JJ)V

    invoke-virtual {v12, v0}, Lcom/bytedance/sdk/component/WR/ex;->Fj([B)V

    goto :goto_2

    :cond_5
    iget-boolean v0, p0, Lcom/bytedance/sdk/component/WR/ex/hjc;->svN:Z

    if-eqz v0, :cond_6

    invoke-virtual {v1}, Lcom/bytedance/sdk/component/ex/Fj/JW;->WR()Lcom/bytedance/sdk/component/ex/Fj/JU;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/ex/Fj/JU;->eV()[B

    move-result-object v0

    new-instance v7, Ljava/lang/String;

    invoke-virtual {v1}, Lcom/bytedance/sdk/component/ex/Fj/JW;->WR()Lcom/bytedance/sdk/component/ex/Fj/JU;

    move-result-object v2

    invoke-direct {p0, v2}, Lcom/bytedance/sdk/component/WR/ex/eV;->Fj(Lcom/bytedance/sdk/component/ex/Fj/JU;)Lcom/bytedance/sdk/component/ex/Fj/mSE;

    move-result-object v2

    invoke-direct {p0, v2}, Lcom/bytedance/sdk/component/WR/ex/eV;->Fj(Lcom/bytedance/sdk/component/ex/Fj/mSE;)Ljava/nio/charset/Charset;

    move-result-object v2

    invoke-direct {v7, v0, v2}, Ljava/lang/String;-><init>([BLjava/nio/charset/Charset;)V

    new-instance v12, Lcom/bytedance/sdk/component/WR/ex;

    invoke-virtual {v1}, Lcom/bytedance/sdk/component/ex/Fj/JW;->eV()Z

    move-result v3

    invoke-virtual {v1}, Lcom/bytedance/sdk/component/ex/Fj/JW;->hjc()I

    move-result v4

    invoke-virtual {v1}, Lcom/bytedance/sdk/component/ex/Fj/JW;->Ubf()Ljava/lang/String;

    move-result-object v5

    invoke-virtual {v1}, Lcom/bytedance/sdk/component/ex/Fj/JW;->ex()J

    move-result-wide v8

    invoke-virtual {v1}, Lcom/bytedance/sdk/component/ex/Fj/JW;->Fj()J

    move-result-wide v10

    move-object v2, v12

    invoke-direct/range {v2 .. v11}, Lcom/bytedance/sdk/component/WR/ex;-><init>(ZILjava/lang/String;Ljava/util/Map;Ljava/lang/String;JJ)V

    invoke-virtual {v12, v0}, Lcom/bytedance/sdk/component/WR/ex;->Fj([B)V

    goto :goto_2

    :cond_6
    new-instance v12, Lcom/bytedance/sdk/component/WR/ex;

    invoke-virtual {v1}, Lcom/bytedance/sdk/component/ex/Fj/JW;->eV()Z

    move-result v3

    invoke-virtual {v1}, Lcom/bytedance/sdk/component/ex/Fj/JW;->hjc()I

    move-result v4

    invoke-virtual {v1}, Lcom/bytedance/sdk/component/ex/Fj/JW;->Ubf()Ljava/lang/String;

    move-result-object v5

    invoke-virtual {v1}, Lcom/bytedance/sdk/component/ex/Fj/JW;->WR()Lcom/bytedance/sdk/component/ex/Fj/JU;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/ex/Fj/JU;->ex()Ljava/lang/String;

    move-result-object v7

    invoke-virtual {v1}, Lcom/bytedance/sdk/component/ex/Fj/JW;->ex()J

    move-result-wide v8

    invoke-virtual {v1}, Lcom/bytedance/sdk/component/ex/Fj/JW;->Fj()J

    move-result-wide v10

    move-object v2, v12

    invoke-direct/range {v2 .. v11}, Lcom/bytedance/sdk/component/WR/ex;-><init>(ZILjava/lang/String;Ljava/util/Map;Ljava/lang/String;JJ)V

    :goto_2
    invoke-direct {p0, v12, v1}, Lcom/bytedance/sdk/component/WR/ex/eV;->Fj(Lcom/bytedance/sdk/component/WR/ex;Lcom/bytedance/sdk/component/ex/Fj/JW;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    return-object v12

    :cond_7
    const/4 v0, 0x0

    return-object v0

    :goto_3
    new-instance v11, Lcom/bytedance/sdk/component/WR/ex;

    const/4 v2, 0x0

    const/16 v3, 0x1389

    invoke-virtual {v0}, Ljava/lang/Throwable;->getMessage()Ljava/lang/String;

    move-result-object v4

    const/4 v5, 0x0

    const-string v6, "BODY_NULL_BODY"

    const-wide/16 v7, 0x1

    const-wide/16 v9, 0x1

    move-object v1, v11

    invoke-direct/range {v1 .. v10}, Lcom/bytedance/sdk/component/WR/ex;-><init>(ZILjava/lang/String;Ljava/util/Map;Ljava/lang/String;JJ)V

    return-object v11
.end method

.method public Fj(Lcom/bytedance/sdk/component/WR/Fj/Fj;)V
    .locals 2

    :try_start_0
    new-instance v0, Lcom/bytedance/sdk/component/ex/Fj/dG$Fj;

    invoke-direct {v0}, Lcom/bytedance/sdk/component/ex/Fj/dG$Fj;-><init>()V

    iget-object v1, p0, Lcom/bytedance/sdk/component/WR/ex/hjc;->WR:Ljava/lang/String;

    invoke-static {v1}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v1

    if-eqz v1, :cond_0

    new-instance v0, Ljava/io/IOException;

    const-string v1, "Url is Empty"

    invoke-direct {v0, v1}, Ljava/io/IOException;-><init>(Ljava/lang/String;)V

    invoke-virtual {p1, p0, v0}, Lcom/bytedance/sdk/component/WR/Fj/Fj;->Fj(Lcom/bytedance/sdk/component/WR/ex/hjc;Ljava/io/IOException;)V

    return-void

    :catchall_0
    move-exception v0

    goto :goto_0

    :cond_0
    iget-object v1, p0, Lcom/bytedance/sdk/component/WR/ex/hjc;->WR:Ljava/lang/String;

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/component/ex/Fj/dG$Fj;->Fj(Ljava/lang/String;)Lcom/bytedance/sdk/component/ex/Fj/dG$Fj;

    iget-object v1, p0, Lcom/bytedance/sdk/component/WR/ex/eV;->Fj:Lcom/bytedance/sdk/component/ex/Fj/Tc;

    if-nez v1, :cond_2

    if-eqz p1, :cond_1

    new-instance v0, Ljava/io/IOException;

    const-string v1, "RequestBody is null, content type is not support!!"

    invoke-direct {v0, v1}, Ljava/io/IOException;-><init>(Ljava/lang/String;)V

    invoke-virtual {p1, p0, v0}, Lcom/bytedance/sdk/component/WR/Fj/Fj;->Fj(Lcom/bytedance/sdk/component/WR/ex/hjc;Ljava/io/IOException;)V

    :cond_1
    return-void

    :cond_2
    invoke-virtual {p0, v0}, Lcom/bytedance/sdk/component/WR/ex/hjc;->Fj(Lcom/bytedance/sdk/component/ex/Fj/dG$Fj;)V

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/WR/ex/hjc;->ex()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/component/ex/Fj/dG$Fj;->Fj(Ljava/lang/Object;)Lcom/bytedance/sdk/component/ex/Fj/dG$Fj;

    iget-object v1, p0, Lcom/bytedance/sdk/component/WR/ex/eV;->Fj:Lcom/bytedance/sdk/component/ex/Fj/Tc;

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/component/ex/Fj/dG$Fj;->Fj(Lcom/bytedance/sdk/component/ex/Fj/Tc;)Lcom/bytedance/sdk/component/ex/Fj/dG$Fj;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/ex/Fj/dG$Fj;->ex()Lcom/bytedance/sdk/component/ex/Fj/dG;

    move-result-object v0

    iget-object v1, p0, Lcom/bytedance/sdk/component/WR/ex/hjc;->hjc:Lcom/bytedance/sdk/component/ex/Fj/rAx;

    invoke-virtual {v1, v0}, Lcom/bytedance/sdk/component/ex/Fj/rAx;->Fj(Lcom/bytedance/sdk/component/ex/Fj/dG;)Lcom/bytedance/sdk/component/ex/Fj/ex;

    move-result-object v0

    new-instance v1, Lcom/bytedance/sdk/component/WR/ex/eV$1;

    invoke-direct {v1, p0, p1}, Lcom/bytedance/sdk/component/WR/ex/eV$1;-><init>(Lcom/bytedance/sdk/component/WR/ex/eV;Lcom/bytedance/sdk/component/WR/Fj/Fj;)V

    invoke-interface {v0, v1}, Lcom/bytedance/sdk/component/ex/Fj/ex;->Fj(Lcom/bytedance/sdk/component/ex/Fj/hjc;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    return-void

    :goto_0
    new-instance v1, Ljava/io/IOException;

    invoke-virtual {v0}, Ljava/lang/Throwable;->getMessage()Ljava/lang/String;

    move-result-object v0

    invoke-direct {v1, v0}, Ljava/io/IOException;-><init>(Ljava/lang/String;)V

    invoke-virtual {p1, p0, v1}, Lcom/bytedance/sdk/component/WR/Fj/Fj;->Fj(Lcom/bytedance/sdk/component/WR/ex/hjc;Ljava/io/IOException;)V

    return-void
.end method

.method public Fj(Ljava/lang/String;Z)V
    .locals 0

    if-eqz p2, :cond_0

    invoke-direct {p0, p1}, Lcom/bytedance/sdk/component/WR/ex/eV;->eV(Ljava/lang/String;)[B

    move-result-object p1

    const-string p2, "application/json; charset=utf-8"

    invoke-virtual {p0, p2, p1}, Lcom/bytedance/sdk/component/WR/ex/eV;->Fj(Ljava/lang/String;[B)V

    const-string p1, "Content-Encoding"

    const-string p2, "gzip"

    invoke-virtual {p0, p1, p2}, Lcom/bytedance/sdk/component/WR/ex/hjc;->ex(Ljava/lang/String;Ljava/lang/String;)V

    return-void

    :cond_0
    invoke-virtual {p0, p1}, Lcom/bytedance/sdk/component/WR/ex/eV;->hjc(Ljava/lang/String;)V

    return-void
.end method

.method public Fj(Ljava/lang/String;[B)V
    .locals 0

    invoke-static {p1}, Lcom/bytedance/sdk/component/ex/Fj/mSE;->Fj(Ljava/lang/String;)Lcom/bytedance/sdk/component/ex/Fj/mSE;

    move-result-object p1

    invoke-static {p1, p2}, Lcom/bytedance/sdk/component/ex/Fj/Tc;->Fj(Lcom/bytedance/sdk/component/ex/Fj/mSE;[B)Lcom/bytedance/sdk/component/ex/Fj/Tc;

    move-result-object p1

    iput-object p1, p0, Lcom/bytedance/sdk/component/WR/ex/eV;->Fj:Lcom/bytedance/sdk/component/ex/Fj/Tc;

    return-void
.end method

.method public Fj(Lorg/json/JSONObject;)V
    .locals 1

    if-eqz p1, :cond_0

    invoke-virtual {p1}, Lorg/json/JSONObject;->toString()Ljava/lang/String;

    move-result-object p1

    goto :goto_0

    :cond_0
    const-string p1, "{}"

    :goto_0
    const-string v0, "application/json; charset=utf-8"

    invoke-static {v0}, Lcom/bytedance/sdk/component/ex/Fj/mSE;->Fj(Ljava/lang/String;)Lcom/bytedance/sdk/component/ex/Fj/mSE;

    move-result-object v0

    invoke-static {v0, p1}, Lcom/bytedance/sdk/component/ex/Fj/Tc;->Fj(Lcom/bytedance/sdk/component/ex/Fj/mSE;Ljava/lang/String;)Lcom/bytedance/sdk/component/ex/Fj/Tc;

    move-result-object p1

    iput-object p1, p0, Lcom/bytedance/sdk/component/WR/ex/eV;->Fj:Lcom/bytedance/sdk/component/ex/Fj/Tc;

    return-void
.end method

.method public hjc()Lcom/bytedance/sdk/component/ex/Fj/Tc;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/WR/ex/eV;->Fj:Lcom/bytedance/sdk/component/ex/Fj/Tc;

    return-object v0
.end method

.method public hjc(Ljava/lang/String;)V
    .locals 1

    invoke-static {p1}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    if-eqz v0, :cond_0

    const-string p1, "{}"

    :cond_0
    const-string v0, "application/json; charset=utf-8"

    invoke-static {v0}, Lcom/bytedance/sdk/component/ex/Fj/mSE;->Fj(Ljava/lang/String;)Lcom/bytedance/sdk/component/ex/Fj/mSE;

    move-result-object v0

    invoke-static {v0, p1}, Lcom/bytedance/sdk/component/ex/Fj/Tc;->Fj(Lcom/bytedance/sdk/component/ex/Fj/mSE;Ljava/lang/String;)Lcom/bytedance/sdk/component/ex/Fj/Tc;

    move-result-object p1

    iput-object p1, p0, Lcom/bytedance/sdk/component/WR/ex/eV;->Fj:Lcom/bytedance/sdk/component/ex/Fj/Tc;

    return-void
.end method
