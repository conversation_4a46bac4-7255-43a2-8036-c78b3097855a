.class Lcom/bytedance/sdk/component/WR/ex/ex$1;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/sdk/component/ex/Fj/hjc;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/component/WR/ex/ex;->Fj(Lcom/bytedance/sdk/component/WR/Fj/Fj;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/component/WR/Fj/Fj;

.field final synthetic ex:Lcom/bytedance/sdk/component/WR/ex/ex;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/component/WR/ex/ex;Lcom/bytedance/sdk/component/WR/Fj/Fj;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/WR/ex/ex$1;->ex:Lcom/bytedance/sdk/component/WR/ex/ex;

    iput-object p2, p0, Lcom/bytedance/sdk/component/WR/ex/ex$1;->Fj:Lcom/bytedance/sdk/component/WR/Fj/Fj;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public Fj(Lcom/bytedance/sdk/component/ex/Fj/ex;Lcom/bytedance/sdk/component/ex/Fj/JW;)V
    .locals 10
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget-object p1, p0, Lcom/bytedance/sdk/component/WR/ex/ex$1;->Fj:Lcom/bytedance/sdk/component/WR/Fj/Fj;

    if-eqz p1, :cond_2

    new-instance v4, Ljava/util/HashMap;

    invoke-direct {v4}, Ljava/util/HashMap;-><init>()V

    if-eqz p2, :cond_2

    invoke-virtual {p2}, Lcom/bytedance/sdk/component/ex/Fj/JW;->svN()Lcom/bytedance/sdk/component/ex/Fj/WR;

    move-result-object p1

    if-eqz p1, :cond_0

    const/4 v0, 0x0

    :goto_0
    invoke-virtual {p1}, Lcom/bytedance/sdk/component/ex/Fj/WR;->Fj()I

    move-result v1

    if-ge v0, v1, :cond_0

    invoke-virtual {p1, v0}, Lcom/bytedance/sdk/component/ex/Fj/WR;->Fj(I)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p1, v0}, Lcom/bytedance/sdk/component/ex/Fj/WR;->ex(I)Ljava/lang/String;

    move-result-object v2

    invoke-interface {v4, v1, v2}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :cond_0
    invoke-virtual {p2}, Lcom/bytedance/sdk/component/ex/Fj/JW;->WR()Lcom/bytedance/sdk/component/ex/Fj/JU;

    move-result-object p1

    if-nez p1, :cond_1

    const-string p1, ""

    :goto_1
    move-object v5, p1

    goto :goto_2

    :cond_1
    invoke-virtual {p1}, Lcom/bytedance/sdk/component/ex/Fj/JU;->ex()Ljava/lang/String;

    move-result-object p1

    goto :goto_1

    :goto_2
    new-instance p1, Lcom/bytedance/sdk/component/WR/ex;

    invoke-virtual {p2}, Lcom/bytedance/sdk/component/ex/Fj/JW;->eV()Z

    move-result v1

    invoke-virtual {p2}, Lcom/bytedance/sdk/component/ex/Fj/JW;->hjc()I

    move-result v2

    invoke-virtual {p2}, Lcom/bytedance/sdk/component/ex/Fj/JW;->Ubf()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {p2}, Lcom/bytedance/sdk/component/ex/Fj/JW;->ex()J

    move-result-wide v6

    invoke-virtual {p2}, Lcom/bytedance/sdk/component/ex/Fj/JW;->Fj()J

    move-result-wide v8

    move-object v0, p1

    invoke-direct/range {v0 .. v9}, Lcom/bytedance/sdk/component/WR/ex;-><init>(ZILjava/lang/String;Ljava/util/Map;Ljava/lang/String;JJ)V

    iget-object p2, p0, Lcom/bytedance/sdk/component/WR/ex/ex$1;->Fj:Lcom/bytedance/sdk/component/WR/Fj/Fj;

    iget-object v0, p0, Lcom/bytedance/sdk/component/WR/ex/ex$1;->ex:Lcom/bytedance/sdk/component/WR/ex/ex;

    invoke-virtual {p2, v0, p1}, Lcom/bytedance/sdk/component/WR/Fj/Fj;->Fj(Lcom/bytedance/sdk/component/WR/ex/hjc;Lcom/bytedance/sdk/component/WR/ex;)V

    :cond_2
    return-void
.end method

.method public Fj(Lcom/bytedance/sdk/component/ex/Fj/ex;Ljava/io/IOException;)V
    .locals 1

    iget-object p1, p0, Lcom/bytedance/sdk/component/WR/ex/ex$1;->Fj:Lcom/bytedance/sdk/component/WR/Fj/Fj;

    if-eqz p1, :cond_0

    iget-object v0, p0, Lcom/bytedance/sdk/component/WR/ex/ex$1;->ex:Lcom/bytedance/sdk/component/WR/ex/ex;

    invoke-virtual {p1, v0, p2}, Lcom/bytedance/sdk/component/WR/Fj/Fj;->Fj(Lcom/bytedance/sdk/component/WR/ex/hjc;Ljava/io/IOException;)V

    :cond_0
    return-void
.end method
