.class Lcom/bytedance/sdk/component/WR/hjc/Fj$1;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/component/WR/hjc/Fj;->Fj(Z)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/component/WR/hjc/Fj;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/component/WR/hjc/Fj;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/WR/hjc/Fj$1;->Fj:Lcom/bytedance/sdk/component/WR/hjc/Fj;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/WR/hjc/Fj$1;->Fj:Lcom/bytedance/sdk/component/WR/hjc/Fj;

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/WR/hjc/Fj;->ex()V

    return-void
.end method
