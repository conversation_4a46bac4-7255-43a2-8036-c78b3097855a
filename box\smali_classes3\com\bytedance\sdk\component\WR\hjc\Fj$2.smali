.class Lcom/bytedance/sdk/component/WR/hjc/Fj$2;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/component/WR/hjc/Fj;->hjc()Z
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/component/WR/hjc/Fj;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/component/WR/hjc/Fj;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/WR/hjc/Fj$2;->Fj:Lcom/bytedance/sdk/component/WR/hjc/Fj;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 4

    iget-object v0, p0, Lcom/bytedance/sdk/component/WR/hjc/Fj$2;->Fj:Lcom/bytedance/sdk/component/WR/hjc/Fj;

    invoke-static {v0}, Lcom/bytedance/sdk/component/WR/hjc/Fj;->Fj(Lcom/bytedance/sdk/component/WR/hjc/Fj;)Landroid/content/Context;

    move-result-object v0

    invoke-static {v0}, Lcom/bytedance/sdk/component/WR/eV/Ubf;->Fj(Landroid/content/Context;)Z

    move-result v0

    if-eqz v0, :cond_1

    iget-object v1, p0, Lcom/bytedance/sdk/component/WR/hjc/Fj$2;->Fj:Lcom/bytedance/sdk/component/WR/hjc/Fj;

    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v2

    invoke-static {v1, v2, v3}, Lcom/bytedance/sdk/component/WR/hjc/Fj;->Fj(Lcom/bytedance/sdk/component/WR/hjc/Fj;J)J

    iget-object v1, p0, Lcom/bytedance/sdk/component/WR/hjc/Fj$2;->Fj:Lcom/bytedance/sdk/component/WR/hjc/Fj;

    invoke-static {v1}, Lcom/bytedance/sdk/component/WR/hjc/Fj;->ex(Lcom/bytedance/sdk/component/WR/hjc/Fj;)Ljava/util/concurrent/atomic/AtomicBoolean;

    move-result-object v1

    const/4 v2, 0x0

    const/4 v3, 0x1

    invoke-virtual {v1, v2, v3}, Ljava/util/concurrent/atomic/AtomicBoolean;->compareAndSet(ZZ)Z

    move-result v1

    if-nez v1, :cond_0

    return-void

    :cond_0
    iget-object v1, p0, Lcom/bytedance/sdk/component/WR/hjc/Fj$2;->Fj:Lcom/bytedance/sdk/component/WR/hjc/Fj;

    invoke-virtual {v1, v0}, Lcom/bytedance/sdk/component/WR/hjc/Fj;->hjc(Z)V

    :cond_1
    return-void
.end method
