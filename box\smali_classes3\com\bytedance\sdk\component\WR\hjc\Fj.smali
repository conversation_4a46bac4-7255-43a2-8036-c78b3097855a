.class public Lcom/bytedance/sdk/component/WR/hjc/Fj;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/sdk/component/utils/Vq$Fj;


# static fields
.field private static BcC:Z

.field private static mSE:Ljava/util/concurrent/ThreadPoolExecutor;


# instance fields
.field final Fj:Lcom/bytedance/sdk/component/utils/Vq;

.field private Ko:Ljava/util/concurrent/atomic/AtomicBoolean;

.field private Tc:I

.field private volatile UYd:Z

.field private Ubf:Z

.field private WR:J

.field private dG:Lcom/bytedance/sdk/component/WR/Fj;

.field private eV:Z

.field private final ex:Z

.field private volatile hjc:Z

.field private final rAx:Landroid/content/Context;

.field private svN:J


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;I)V
    .locals 3

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, 0x0

    iput-boolean v0, p0, Lcom/bytedance/sdk/component/WR/hjc/Fj;->hjc:Z

    const/4 v1, 0x1

    iput-boolean v1, p0, Lcom/bytedance/sdk/component/WR/hjc/Fj;->eV:Z

    iput-boolean v0, p0, Lcom/bytedance/sdk/component/WR/hjc/Fj;->Ubf:Z

    const-wide/16 v1, 0x0

    iput-wide v1, p0, Lcom/bytedance/sdk/component/WR/hjc/Fj;->WR:J

    iput-wide v1, p0, Lcom/bytedance/sdk/component/WR/hjc/Fj;->svN:J

    new-instance v1, Ljava/util/concurrent/atomic/AtomicBoolean;

    invoke-direct {v1, v0}, Ljava/util/concurrent/atomic/AtomicBoolean;-><init>(Z)V

    iput-object v1, p0, Lcom/bytedance/sdk/component/WR/hjc/Fj;->Ko:Ljava/util/concurrent/atomic/AtomicBoolean;

    iput-boolean v0, p0, Lcom/bytedance/sdk/component/WR/hjc/Fj;->UYd:Z

    invoke-static {}, Lcom/bytedance/sdk/component/svN/Fj/Fj;->Fj()Lcom/bytedance/sdk/component/svN/Fj/Fj;

    move-result-object v0

    const-string v1, "tt-net"

    invoke-virtual {v0, p0, v1}, Lcom/bytedance/sdk/component/svN/Fj/Fj;->Fj(Lcom/bytedance/sdk/component/utils/Vq$Fj;Ljava/lang/String;)Lcom/bytedance/sdk/component/utils/Vq;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/sdk/component/WR/hjc/Fj;->Fj:Lcom/bytedance/sdk/component/utils/Vq;

    iput-object p1, p0, Lcom/bytedance/sdk/component/WR/hjc/Fj;->rAx:Landroid/content/Context;

    invoke-static {p1}, Lcom/bytedance/sdk/component/utils/rS;->Fj(Landroid/content/Context;)Z

    move-result p1

    iput-boolean p1, p0, Lcom/bytedance/sdk/component/WR/hjc/Fj;->ex:Z

    iput p2, p0, Lcom/bytedance/sdk/component/WR/hjc/Fj;->Tc:I

    return-void
.end method

.method private BcC()Z
    .locals 2

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/WR/hjc/Fj;->WR()[Ljava/lang/String;

    move-result-object v0

    const/4 v1, 0x0

    if-eqz v0, :cond_1

    array-length v0, v0

    if-nez v0, :cond_0

    goto :goto_0

    :cond_0
    invoke-direct {p0, v1}, Lcom/bytedance/sdk/component/WR/hjc/Fj;->Fj(I)V

    :cond_1
    :goto_0
    return v1
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/component/WR/hjc/Fj;J)J
    .locals 0

    iput-wide p1, p0, Lcom/bytedance/sdk/component/WR/hjc/Fj;->svN:J

    return-wide p1
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/component/WR/hjc/Fj;)Landroid/content/Context;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/component/WR/hjc/Fj;->rAx:Landroid/content/Context;

    return-object p0
.end method

.method private Fj(Ljava/lang/String;)Ljava/lang/String;
    .locals 2

    invoke-static {p1}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    if-eqz v0, :cond_0

    const/4 p1, 0x0

    return-object p1

    :cond_0
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "https://"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string p1, "/get_domains/v4/"

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method private Fj(I)V
    .locals 3

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/WR/hjc/Fj;->WR()[Ljava/lang/String;

    move-result-object v0

    const/16 v1, 0x66

    if-eqz v0, :cond_3

    array-length v2, v0

    if-gt v2, p1, :cond_0

    goto :goto_1

    :cond_0
    aget-object v0, v0, p1

    invoke-static {v0}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v2

    if-eqz v2, :cond_1

    invoke-direct {p0, v1}, Lcom/bytedance/sdk/component/WR/hjc/Fj;->ex(I)V

    return-void

    :cond_1
    :try_start_0
    invoke-direct {p0, v0}, Lcom/bytedance/sdk/component/WR/hjc/Fj;->Fj(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v2

    if-eqz v2, :cond_2

    invoke-direct {p0, v1}, Lcom/bytedance/sdk/component/WR/hjc/Fj;->ex(I)V

    return-void

    :catchall_0
    move-exception p1

    goto :goto_0

    :cond_2
    invoke-direct {p0}, Lcom/bytedance/sdk/component/WR/hjc/Fj;->mSE()Lcom/bytedance/sdk/component/WR/Fj;

    move-result-object v1

    invoke-virtual {v1}, Lcom/bytedance/sdk/component/WR/Fj;->hjc()Lcom/bytedance/sdk/component/WR/ex/ex;

    move-result-object v1

    invoke-virtual {v1, v0}, Lcom/bytedance/sdk/component/WR/ex/hjc;->Fj(Ljava/lang/String;)V

    invoke-direct {p0, v1}, Lcom/bytedance/sdk/component/WR/hjc/Fj;->Fj(Lcom/bytedance/sdk/component/WR/ex/ex;)V

    new-instance v0, Lcom/bytedance/sdk/component/WR/hjc/Fj$3;

    invoke-direct {v0, p0, p1}, Lcom/bytedance/sdk/component/WR/hjc/Fj$3;-><init>(Lcom/bytedance/sdk/component/WR/hjc/Fj;I)V

    invoke-virtual {v1, v0}, Lcom/bytedance/sdk/component/WR/ex/ex;->Fj(Lcom/bytedance/sdk/component/WR/Fj/Fj;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    return-void

    :goto_0
    invoke-virtual {p1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    return-void

    :cond_3
    :goto_1
    invoke-direct {p0, v1}, Lcom/bytedance/sdk/component/WR/hjc/Fj;->ex(I)V

    return-void
.end method

.method private Fj(Lcom/bytedance/sdk/component/WR/ex/ex;)V
    .locals 4

    if-nez p1, :cond_0

    return-void

    :cond_0
    invoke-static {}, Lcom/bytedance/sdk/component/WR/hjc/svN;->Fj()Lcom/bytedance/sdk/component/WR/hjc/svN;

    move-result-object v0

    iget v1, p0, Lcom/bytedance/sdk/component/WR/hjc/Fj;->Tc:I

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/component/WR/hjc/svN;->Fj(I)Lcom/bytedance/sdk/component/WR/hjc/Ubf;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->eV()Lcom/bytedance/sdk/component/WR/hjc/ex;

    move-result-object v0

    if-eqz v0, :cond_1

    invoke-static {}, Lcom/bytedance/sdk/component/WR/hjc/svN;->Fj()Lcom/bytedance/sdk/component/WR/hjc/svN;

    move-result-object v0

    iget v1, p0, Lcom/bytedance/sdk/component/WR/hjc/Fj;->Tc:I

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/component/WR/hjc/svN;->Fj(I)Lcom/bytedance/sdk/component/WR/hjc/Ubf;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->eV()Lcom/bytedance/sdk/component/WR/hjc/ex;

    move-result-object v0

    iget-object v1, p0, Lcom/bytedance/sdk/component/WR/hjc/Fj;->rAx:Landroid/content/Context;

    invoke-interface {v0, v1}, Lcom/bytedance/sdk/component/WR/hjc/ex;->Fj(Landroid/content/Context;)Landroid/location/Address;

    move-result-object v0

    goto :goto_0

    :cond_1
    const/4 v0, 0x0

    :goto_0
    if-eqz v0, :cond_2

    invoke-virtual {v0}, Landroid/location/Address;->hasLatitude()Z

    move-result v1

    if-eqz v1, :cond_2

    invoke-virtual {v0}, Landroid/location/Address;->hasLongitude()Z

    move-result v1

    if-eqz v1, :cond_2

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v0}, Landroid/location/Address;->getLatitude()D

    move-result-wide v2

    invoke-virtual {v1, v2, v3}, Ljava/lang/StringBuilder;->append(D)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    const-string v2, "latitude"

    invoke-virtual {p1, v2, v1}, Lcom/bytedance/sdk/component/WR/ex/ex;->Fj(Ljava/lang/String;Ljava/lang/String;)V

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v0}, Landroid/location/Address;->getLongitude()D

    move-result-wide v2

    invoke-virtual {v1, v2, v3}, Ljava/lang/StringBuilder;->append(D)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    const-string v2, "longitude"

    invoke-virtual {p1, v2, v1}, Lcom/bytedance/sdk/component/WR/ex/ex;->Fj(Ljava/lang/String;Ljava/lang/String;)V

    invoke-virtual {v0}, Landroid/location/Address;->getLocality()Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v1

    if-nez v1, :cond_2

    const-string v1, "city"

    invoke-static {v0}, Landroid/net/Uri;->encode(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p1, v1, v0}, Lcom/bytedance/sdk/component/WR/ex/ex;->Fj(Ljava/lang/String;Ljava/lang/String;)V

    :cond_2
    iget-boolean v0, p0, Lcom/bytedance/sdk/component/WR/hjc/Fj;->hjc:Z

    if-eqz v0, :cond_3

    const-string v0, "force"

    const-string v1, "1"

    invoke-virtual {p1, v0, v1}, Lcom/bytedance/sdk/component/WR/ex/ex;->Fj(Ljava/lang/String;Ljava/lang/String;)V

    :cond_3
    :try_start_0
    sget-object v0, Landroid/os/Build;->SUPPORTED_ABIS:[Ljava/lang/String;

    const/4 v1, 0x0

    aget-object v0, v0, v1

    const-string v1, "abi"

    invoke-virtual {p1, v1, v0}, Lcom/bytedance/sdk/component/WR/ex/ex;->Fj(Ljava/lang/String;Ljava/lang/String;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    goto :goto_1

    :catchall_0
    nop

    :goto_1
    invoke-static {}, Lcom/bytedance/sdk/component/WR/hjc/svN;->Fj()Lcom/bytedance/sdk/component/WR/hjc/svN;

    move-result-object v0

    iget v1, p0, Lcom/bytedance/sdk/component/WR/hjc/Fj;->Tc:I

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/component/WR/hjc/svN;->Fj(I)Lcom/bytedance/sdk/component/WR/hjc/Ubf;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->eV()Lcom/bytedance/sdk/component/WR/hjc/ex;

    move-result-object v0

    if-eqz v0, :cond_4

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    invoke-static {}, Lcom/bytedance/sdk/component/WR/hjc/svN;->Fj()Lcom/bytedance/sdk/component/WR/hjc/svN;

    move-result-object v1

    iget v2, p0, Lcom/bytedance/sdk/component/WR/hjc/Fj;->Tc:I

    invoke-virtual {v1, v2}, Lcom/bytedance/sdk/component/WR/hjc/svN;->Fj(I)Lcom/bytedance/sdk/component/WR/hjc/Ubf;

    move-result-object v1

    invoke-virtual {v1}, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->eV()Lcom/bytedance/sdk/component/WR/hjc/ex;

    move-result-object v1

    invoke-interface {v1}, Lcom/bytedance/sdk/component/WR/hjc/ex;->Fj()I

    move-result v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    const-string v1, "aid"

    invoke-virtual {p1, v1, v0}, Lcom/bytedance/sdk/component/WR/ex/ex;->Fj(Ljava/lang/String;Ljava/lang/String;)V

    invoke-static {}, Lcom/bytedance/sdk/component/WR/hjc/svN;->Fj()Lcom/bytedance/sdk/component/WR/hjc/svN;

    move-result-object v0

    iget v1, p0, Lcom/bytedance/sdk/component/WR/hjc/Fj;->Tc:I

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/component/WR/hjc/svN;->Fj(I)Lcom/bytedance/sdk/component/WR/hjc/Ubf;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->eV()Lcom/bytedance/sdk/component/WR/hjc/ex;

    move-result-object v0

    invoke-interface {v0}, Lcom/bytedance/sdk/component/WR/hjc/ex;->hjc()Ljava/lang/String;

    move-result-object v0

    const-string v1, "device_platform"

    invoke-virtual {p1, v1, v0}, Lcom/bytedance/sdk/component/WR/ex/ex;->Fj(Ljava/lang/String;Ljava/lang/String;)V

    invoke-static {}, Lcom/bytedance/sdk/component/WR/hjc/svN;->Fj()Lcom/bytedance/sdk/component/WR/hjc/svN;

    move-result-object v0

    iget v1, p0, Lcom/bytedance/sdk/component/WR/hjc/Fj;->Tc:I

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/component/WR/hjc/svN;->Fj(I)Lcom/bytedance/sdk/component/WR/hjc/Ubf;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->eV()Lcom/bytedance/sdk/component/WR/hjc/ex;

    move-result-object v0

    invoke-interface {v0}, Lcom/bytedance/sdk/component/WR/hjc/ex;->ex()Ljava/lang/String;

    move-result-object v0

    const-string v1, "channel"

    invoke-virtual {p1, v1, v0}, Lcom/bytedance/sdk/component/WR/ex/ex;->Fj(Ljava/lang/String;Ljava/lang/String;)V

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    invoke-static {}, Lcom/bytedance/sdk/component/WR/hjc/svN;->Fj()Lcom/bytedance/sdk/component/WR/hjc/svN;

    move-result-object v1

    iget v2, p0, Lcom/bytedance/sdk/component/WR/hjc/Fj;->Tc:I

    invoke-virtual {v1, v2}, Lcom/bytedance/sdk/component/WR/hjc/svN;->Fj(I)Lcom/bytedance/sdk/component/WR/hjc/Ubf;

    move-result-object v1

    invoke-virtual {v1}, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->eV()Lcom/bytedance/sdk/component/WR/hjc/ex;

    move-result-object v1

    invoke-interface {v1}, Lcom/bytedance/sdk/component/WR/hjc/ex;->eV()I

    move-result v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    const-string v1, "version_code"

    invoke-virtual {p1, v1, v0}, Lcom/bytedance/sdk/component/WR/ex/ex;->Fj(Ljava/lang/String;Ljava/lang/String;)V

    invoke-static {}, Lcom/bytedance/sdk/component/WR/hjc/svN;->Fj()Lcom/bytedance/sdk/component/WR/hjc/svN;

    move-result-object v0

    iget v1, p0, Lcom/bytedance/sdk/component/WR/hjc/Fj;->Tc:I

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/component/WR/hjc/svN;->Fj(I)Lcom/bytedance/sdk/component/WR/hjc/Ubf;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->eV()Lcom/bytedance/sdk/component/WR/hjc/ex;

    move-result-object v0

    invoke-interface {v0}, Lcom/bytedance/sdk/component/WR/hjc/ex;->Ubf()Ljava/lang/String;

    move-result-object v0

    const-string v1, "custom_info_1"

    invoke-virtual {p1, v1, v0}, Lcom/bytedance/sdk/component/WR/ex/ex;->Fj(Ljava/lang/String;Ljava/lang/String;)V

    :cond_4
    return-void
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/component/WR/hjc/Fj;I)V
    .locals 0

    invoke-direct {p0, p1}, Lcom/bytedance/sdk/component/WR/hjc/Fj;->Fj(I)V

    return-void
.end method

.method public static Fj(Ljava/util/concurrent/ThreadPoolExecutor;)V
    .locals 0

    sput-object p0, Lcom/bytedance/sdk/component/WR/hjc/Fj;->mSE:Ljava/util/concurrent/ThreadPoolExecutor;

    return-void
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/component/WR/hjc/Fj;Ljava/lang/Object;)Z
    .locals 0
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/Exception;
        }
    .end annotation

    invoke-direct {p0, p1}, Lcom/bytedance/sdk/component/WR/hjc/Fj;->Fj(Ljava/lang/Object;)Z

    move-result p0

    return p0
.end method

.method private Fj(Ljava/lang/Object;)Z
    .locals 4
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/Exception;
        }
    .end annotation

    instance-of v0, p1, Ljava/lang/String;

    const/4 v1, 0x0

    if-eqz v0, :cond_1

    check-cast p1, Ljava/lang/String;

    invoke-static {p1}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    if-eqz v0, :cond_0

    return v1

    :cond_0
    new-instance v0, Lorg/json/JSONObject;

    invoke-direct {v0, p1}, Lorg/json/JSONObject;-><init>(Ljava/lang/String;)V

    const-string p1, "message"

    invoke-virtual {v0, p1}, Lorg/json/JSONObject;->getString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    const-string v2, "success"

    invoke-virtual {v2, p1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result p1

    if-nez p1, :cond_3

    return v1

    :cond_1
    instance-of v0, p1, Lorg/json/JSONObject;

    if-eqz v0, :cond_2

    move-object v0, p1

    check-cast v0, Lorg/json/JSONObject;

    goto :goto_0

    :cond_2
    const/4 v0, 0x0

    :cond_3
    :goto_0
    if-nez v0, :cond_4

    return v1

    :cond_4
    const-string p1, "data"

    invoke-virtual {v0, p1}, Lorg/json/JSONObject;->getJSONObject(Ljava/lang/String;)Lorg/json/JSONObject;

    move-result-object p1

    monitor-enter p0

    :try_start_0
    iget-object v0, p0, Lcom/bytedance/sdk/component/WR/hjc/Fj;->rAx:Landroid/content/Context;

    const-string v2, "ss_app_config"

    invoke-virtual {v0, v2, v1}, Landroid/content/Context;->getSharedPreferences(Ljava/lang/String;I)Landroid/content/SharedPreferences;

    move-result-object v0

    invoke-interface {v0}, Landroid/content/SharedPreferences;->edit()Landroid/content/SharedPreferences$Editor;

    move-result-object v0

    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v1

    const-string v3, "last_refresh_time"

    invoke-interface {v0, v3, v1, v2}, Landroid/content/SharedPreferences$Editor;->putLong(Ljava/lang/String;J)Landroid/content/SharedPreferences$Editor;

    invoke-interface {v0}, Landroid/content/SharedPreferences$Editor;->apply()V

    monitor-exit p0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    invoke-static {}, Lcom/bytedance/sdk/component/WR/hjc/svN;->Fj()Lcom/bytedance/sdk/component/WR/hjc/svN;

    move-result-object v0

    iget v1, p0, Lcom/bytedance/sdk/component/WR/hjc/Fj;->Tc:I

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/component/WR/hjc/svN;->Fj(I)Lcom/bytedance/sdk/component/WR/hjc/Ubf;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->BcC()Lcom/bytedance/sdk/component/WR/hjc/eV;

    move-result-object v0

    if-eqz v0, :cond_5

    invoke-static {}, Lcom/bytedance/sdk/component/WR/hjc/svN;->Fj()Lcom/bytedance/sdk/component/WR/hjc/svN;

    move-result-object v0

    iget v1, p0, Lcom/bytedance/sdk/component/WR/hjc/Fj;->Tc:I

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/component/WR/hjc/svN;->Fj(I)Lcom/bytedance/sdk/component/WR/hjc/Ubf;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->BcC()Lcom/bytedance/sdk/component/WR/hjc/eV;

    move-result-object v0

    invoke-virtual {v0, p1}, Lcom/bytedance/sdk/component/WR/hjc/eV;->Fj(Lorg/json/JSONObject;)V

    :cond_5
    const/4 p1, 0x1

    return p1

    :catchall_0
    move-exception p1

    monitor-exit p0

    throw p1
.end method

.method private eV(Z)V
    .locals 6

    iget-boolean v0, p0, Lcom/bytedance/sdk/component/WR/hjc/Fj;->Ubf:Z

    if-eqz v0, :cond_0

    return-void

    :cond_0
    iget-boolean v0, p0, Lcom/bytedance/sdk/component/WR/hjc/Fj;->eV:Z

    if-eqz v0, :cond_1

    const/4 v0, 0x0

    iput-boolean v0, p0, Lcom/bytedance/sdk/component/WR/hjc/Fj;->eV:Z

    const-wide/16 v0, 0x0

    iput-wide v0, p0, Lcom/bytedance/sdk/component/WR/hjc/Fj;->WR:J

    iput-wide v0, p0, Lcom/bytedance/sdk/component/WR/hjc/Fj;->svN:J

    :cond_1
    if-eqz p1, :cond_2

    const-wide/32 v0, 0x57e40

    goto :goto_0

    :cond_2
    const-wide/32 v0, 0x2932e00

    :goto_0
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v2

    iget-wide v4, p0, Lcom/bytedance/sdk/component/WR/hjc/Fj;->WR:J

    sub-long v4, v2, v4

    cmp-long p1, v4, v0

    if-lez p1, :cond_4

    iget-wide v0, p0, Lcom/bytedance/sdk/component/WR/hjc/Fj;->svN:J

    sub-long/2addr v2, v0

    const-wide/32 v0, 0x1d4c0

    cmp-long p1, v2, v0

    if-gtz p1, :cond_3

    iget-boolean p1, p0, Lcom/bytedance/sdk/component/WR/hjc/Fj;->UYd:Z

    if-nez p1, :cond_4

    :cond_3
    invoke-virtual {p0}, Lcom/bytedance/sdk/component/WR/hjc/Fj;->hjc()Z

    :cond_4
    return-void
.end method

.method public static synthetic ex(Lcom/bytedance/sdk/component/WR/hjc/Fj;)Ljava/util/concurrent/atomic/AtomicBoolean;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/component/WR/hjc/Fj;->Ko:Ljava/util/concurrent/atomic/AtomicBoolean;

    return-object p0
.end method

.method private ex(I)V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/WR/hjc/Fj;->Fj:Lcom/bytedance/sdk/component/utils/Vq;

    if-eqz v0, :cond_0

    invoke-virtual {v0, p1}, Landroid/os/Handler;->sendEmptyMessage(I)Z

    :cond_0
    return-void
.end method

.method public static synthetic ex(Lcom/bytedance/sdk/component/WR/hjc/Fj;I)V
    .locals 0

    invoke-direct {p0, p1}, Lcom/bytedance/sdk/component/WR/hjc/Fj;->ex(I)V

    return-void
.end method

.method public static ex(Z)V
    .locals 0

    sput-boolean p0, Lcom/bytedance/sdk/component/WR/hjc/Fj;->BcC:Z

    return-void
.end method

.method private mSE()Lcom/bytedance/sdk/component/WR/Fj;
    .locals 4

    iget-object v0, p0, Lcom/bytedance/sdk/component/WR/hjc/Fj;->dG:Lcom/bytedance/sdk/component/WR/Fj;

    if-nez v0, :cond_0

    new-instance v0, Lcom/bytedance/sdk/component/WR/Fj$Fj;

    invoke-direct {v0}, Lcom/bytedance/sdk/component/WR/Fj$Fj;-><init>()V

    sget-object v1, Ljava/util/concurrent/TimeUnit;->SECONDS:Ljava/util/concurrent/TimeUnit;

    const-wide/16 v2, 0xa

    invoke-virtual {v0, v2, v3, v1}, Lcom/bytedance/sdk/component/WR/Fj$Fj;->Fj(JLjava/util/concurrent/TimeUnit;)Lcom/bytedance/sdk/component/WR/Fj$Fj;

    move-result-object v0

    invoke-virtual {v0, v2, v3, v1}, Lcom/bytedance/sdk/component/WR/Fj$Fj;->ex(JLjava/util/concurrent/TimeUnit;)Lcom/bytedance/sdk/component/WR/Fj$Fj;

    move-result-object v0

    invoke-virtual {v0, v2, v3, v1}, Lcom/bytedance/sdk/component/WR/Fj$Fj;->hjc(JLjava/util/concurrent/TimeUnit;)Lcom/bytedance/sdk/component/WR/Fj$Fj;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/WR/Fj$Fj;->Fj()Lcom/bytedance/sdk/component/WR/Fj;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/sdk/component/WR/hjc/Fj;->dG:Lcom/bytedance/sdk/component/WR/Fj;

    :cond_0
    iget-object v0, p0, Lcom/bytedance/sdk/component/WR/hjc/Fj;->dG:Lcom/bytedance/sdk/component/WR/Fj;

    return-object v0
.end method

.method public static svN()Ljava/util/concurrent/ThreadPoolExecutor;
    .locals 9

    sget-object v0, Lcom/bytedance/sdk/component/WR/hjc/Fj;->mSE:Ljava/util/concurrent/ThreadPoolExecutor;

    if-nez v0, :cond_1

    const-class v0, Lcom/bytedance/sdk/component/WR/hjc/Fj;

    monitor-enter v0

    :try_start_0
    sget-object v1, Lcom/bytedance/sdk/component/WR/hjc/Fj;->mSE:Ljava/util/concurrent/ThreadPoolExecutor;

    if-nez v1, :cond_0

    new-instance v1, Ljava/util/concurrent/ThreadPoolExecutor;

    const/4 v3, 0x2

    const/4 v4, 0x2

    const-wide/16 v5, 0x14

    sget-object v7, Ljava/util/concurrent/TimeUnit;->SECONDS:Ljava/util/concurrent/TimeUnit;

    new-instance v8, Ljava/util/concurrent/LinkedBlockingQueue;

    invoke-direct {v8}, Ljava/util/concurrent/LinkedBlockingQueue;-><init>()V

    move-object v2, v1

    invoke-direct/range {v2 .. v8}, Ljava/util/concurrent/ThreadPoolExecutor;-><init>(IIJLjava/util/concurrent/TimeUnit;Ljava/util/concurrent/BlockingQueue;)V

    sput-object v1, Lcom/bytedance/sdk/component/WR/hjc/Fj;->mSE:Ljava/util/concurrent/ThreadPoolExecutor;

    const/4 v2, 0x1

    invoke-virtual {v1, v2}, Ljava/util/concurrent/ThreadPoolExecutor;->allowCoreThreadTimeOut(Z)V

    goto :goto_0

    :catchall_0
    move-exception v1

    goto :goto_1

    :cond_0
    :goto_0
    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    goto :goto_2

    :goto_1
    monitor-exit v0

    throw v1

    :cond_1
    :goto_2
    sget-object v0, Lcom/bytedance/sdk/component/WR/hjc/Fj;->mSE:Ljava/util/concurrent/ThreadPoolExecutor;

    return-object v0
.end method


# virtual methods
.method public Fj()V
    .locals 1

    const/4 v0, 0x0

    invoke-virtual {p0, v0}, Lcom/bytedance/sdk/component/WR/hjc/Fj;->Fj(Z)V

    return-void
.end method

.method public Fj(Landroid/os/Message;)V
    .locals 4

    iget p1, p1, Landroid/os/Message;->what:I

    const/16 v0, 0x65

    const/4 v1, 0x0

    if-eq p1, v0, :cond_2

    const/16 v0, 0x66

    if-eq p1, v0, :cond_0

    goto :goto_0

    :cond_0
    iput-boolean v1, p0, Lcom/bytedance/sdk/component/WR/hjc/Fj;->Ubf:Z

    iget-boolean p1, p0, Lcom/bytedance/sdk/component/WR/hjc/Fj;->eV:Z

    if-eqz p1, :cond_1

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/WR/hjc/Fj;->Fj()V

    :cond_1
    iget-object p1, p0, Lcom/bytedance/sdk/component/WR/hjc/Fj;->Ko:Ljava/util/concurrent/atomic/AtomicBoolean;

    invoke-virtual {p1, v1}, Ljava/util/concurrent/atomic/AtomicBoolean;->set(Z)V

    :goto_0
    return-void

    :cond_2
    iput-boolean v1, p0, Lcom/bytedance/sdk/component/WR/hjc/Fj;->Ubf:Z

    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v2

    iput-wide v2, p0, Lcom/bytedance/sdk/component/WR/hjc/Fj;->WR:J

    iget-boolean p1, p0, Lcom/bytedance/sdk/component/WR/hjc/Fj;->eV:Z

    if-eqz p1, :cond_3

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/WR/hjc/Fj;->Fj()V

    :cond_3
    iget-object p1, p0, Lcom/bytedance/sdk/component/WR/hjc/Fj;->Ko:Ljava/util/concurrent/atomic/AtomicBoolean;

    invoke-virtual {p1, v1}, Ljava/util/concurrent/atomic/AtomicBoolean;->set(Z)V

    return-void
.end method

.method public declared-synchronized Fj(Z)V
    .locals 4

    monitor-enter p0

    :try_start_0
    iget-boolean v0, p0, Lcom/bytedance/sdk/component/WR/hjc/Fj;->ex:Z

    if-eqz v0, :cond_0

    invoke-direct {p0, p1}, Lcom/bytedance/sdk/component/WR/hjc/Fj;->eV(Z)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    monitor-exit p0

    return-void

    :catchall_0
    move-exception p1

    goto :goto_0

    :cond_0
    :try_start_1
    iget-wide v0, p0, Lcom/bytedance/sdk/component/WR/hjc/Fj;->WR:J
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    const-wide/16 v2, 0x0

    cmp-long p1, v0, v2

    if-gtz p1, :cond_1

    :try_start_2
    invoke-static {}, Lcom/bytedance/sdk/component/WR/hjc/Fj;->svN()Ljava/util/concurrent/ThreadPoolExecutor;

    move-result-object p1

    new-instance v0, Lcom/bytedance/sdk/component/WR/hjc/Fj$1;

    invoke-direct {v0, p0}, Lcom/bytedance/sdk/component/WR/hjc/Fj$1;-><init>(Lcom/bytedance/sdk/component/WR/hjc/Fj;)V

    invoke-virtual {p1, v0}, Ljava/util/concurrent/ThreadPoolExecutor;->execute(Ljava/lang/Runnable;)V
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_1

    monitor-exit p0

    return-void

    :catchall_1
    :cond_1
    monitor-exit p0

    return-void

    :goto_0
    monitor-exit p0

    throw p1
.end method

.method public Ubf()V
    .locals 2

    invoke-static {}, Landroid/os/Looper;->myLooper()Landroid/os/Looper;

    move-result-object v0

    invoke-static {}, Landroid/os/Looper;->getMainLooper()Landroid/os/Looper;

    move-result-object v1

    if-ne v0, v1, :cond_0

    return-void

    :cond_0
    :try_start_0
    iget-boolean v0, p0, Lcom/bytedance/sdk/component/WR/hjc/Fj;->ex:Z

    if-eqz v0, :cond_1

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/WR/hjc/Fj;->eV()V

    return-void

    :cond_1
    invoke-virtual {p0}, Lcom/bytedance/sdk/component/WR/hjc/Fj;->ex()V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    :catchall_0
    return-void
.end method

.method public WR()[Ljava/lang/String;
    .locals 2

    invoke-static {}, Lcom/bytedance/sdk/component/WR/hjc/svN;->Fj()Lcom/bytedance/sdk/component/WR/hjc/svN;

    move-result-object v0

    iget v1, p0, Lcom/bytedance/sdk/component/WR/hjc/Fj;->Tc:I

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/component/WR/hjc/svN;->Fj(I)Lcom/bytedance/sdk/component/WR/hjc/Ubf;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->eV()Lcom/bytedance/sdk/component/WR/hjc/ex;

    move-result-object v0

    if-eqz v0, :cond_0

    invoke-static {}, Lcom/bytedance/sdk/component/WR/hjc/svN;->Fj()Lcom/bytedance/sdk/component/WR/hjc/svN;

    move-result-object v0

    iget v1, p0, Lcom/bytedance/sdk/component/WR/hjc/Fj;->Tc:I

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/component/WR/hjc/svN;->Fj(I)Lcom/bytedance/sdk/component/WR/hjc/Ubf;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->eV()Lcom/bytedance/sdk/component/WR/hjc/ex;

    move-result-object v0

    invoke-interface {v0}, Lcom/bytedance/sdk/component/WR/hjc/ex;->WR()[Ljava/lang/String;

    move-result-object v0

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    if-eqz v0, :cond_1

    array-length v1, v0

    if-gtz v1, :cond_2

    :cond_1
    const/4 v0, 0x0

    new-array v0, v0, [Ljava/lang/String;

    :cond_2
    return-object v0
.end method

.method public declared-synchronized eV()V
    .locals 5

    monitor-enter p0

    :try_start_0
    iget-boolean v0, p0, Lcom/bytedance/sdk/component/WR/hjc/Fj;->UYd:Z
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    if-eqz v0, :cond_0

    monitor-exit p0

    return-void

    :cond_0
    const/4 v0, 0x1

    :try_start_1
    iput-boolean v0, p0, Lcom/bytedance/sdk/component/WR/hjc/Fj;->UYd:Z

    iget-object v0, p0, Lcom/bytedance/sdk/component/WR/hjc/Fj;->rAx:Landroid/content/Context;

    const-string v1, "ss_app_config"

    const/4 v2, 0x0

    invoke-virtual {v0, v1, v2}, Landroid/content/Context;->getSharedPreferences(Ljava/lang/String;I)Landroid/content/SharedPreferences;

    move-result-object v0

    const-string v1, "last_refresh_time"

    const-wide/16 v2, 0x0

    invoke-interface {v0, v1, v2, v3}, Landroid/content/SharedPreferences;->getLong(Ljava/lang/String;J)J

    move-result-wide v0

    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v2

    cmp-long v4, v0, v2

    if-lez v4, :cond_1

    move-wide v0, v2

    :cond_1
    iput-wide v0, p0, Lcom/bytedance/sdk/component/WR/hjc/Fj;->WR:J
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    :try_start_2
    invoke-static {}, Lcom/bytedance/sdk/component/WR/hjc/svN;->Fj()Lcom/bytedance/sdk/component/WR/hjc/svN;

    move-result-object v0

    iget v1, p0, Lcom/bytedance/sdk/component/WR/hjc/Fj;->Tc:I

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/component/WR/hjc/svN;->Fj(I)Lcom/bytedance/sdk/component/WR/hjc/Ubf;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->BcC()Lcom/bytedance/sdk/component/WR/hjc/eV;

    move-result-object v0

    if-eqz v0, :cond_2

    invoke-static {}, Lcom/bytedance/sdk/component/WR/hjc/svN;->Fj()Lcom/bytedance/sdk/component/WR/hjc/svN;

    move-result-object v0

    iget v1, p0, Lcom/bytedance/sdk/component/WR/hjc/Fj;->Tc:I

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/component/WR/hjc/svN;->Fj(I)Lcom/bytedance/sdk/component/WR/hjc/Ubf;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->BcC()Lcom/bytedance/sdk/component/WR/hjc/eV;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/WR/hjc/eV;->Fj()V
    :try_end_2
    .catch Ljava/lang/Exception; {:try_start_2 .. :try_end_2} :catch_0
    .catchall {:try_start_2 .. :try_end_2} :catchall_0

    goto :goto_0

    :catchall_0
    move-exception v0

    goto :goto_1

    :cond_2
    :goto_0
    monitor-exit p0

    return-void

    :catch_0
    monitor-exit p0

    return-void

    :goto_1
    monitor-exit p0

    throw v0
.end method

.method public declared-synchronized ex()V
    .locals 5

    monitor-enter p0

    :try_start_0
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v0

    iget-wide v2, p0, Lcom/bytedance/sdk/component/WR/hjc/Fj;->WR:J

    sub-long/2addr v0, v2

    const-wide/32 v2, 0x36ee80

    cmp-long v4, v0, v2

    if-lez v4, :cond_1

    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v0

    iput-wide v0, p0, Lcom/bytedance/sdk/component/WR/hjc/Fj;->WR:J
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    :try_start_1
    invoke-static {}, Lcom/bytedance/sdk/component/WR/hjc/svN;->Fj()Lcom/bytedance/sdk/component/WR/hjc/svN;

    move-result-object v0

    iget v1, p0, Lcom/bytedance/sdk/component/WR/hjc/Fj;->Tc:I

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/component/WR/hjc/svN;->Fj(I)Lcom/bytedance/sdk/component/WR/hjc/Ubf;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->BcC()Lcom/bytedance/sdk/component/WR/hjc/eV;

    move-result-object v0

    if-eqz v0, :cond_0

    invoke-static {}, Lcom/bytedance/sdk/component/WR/hjc/svN;->Fj()Lcom/bytedance/sdk/component/WR/hjc/svN;

    move-result-object v0

    iget v1, p0, Lcom/bytedance/sdk/component/WR/hjc/Fj;->Tc:I

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/component/WR/hjc/svN;->Fj(I)Lcom/bytedance/sdk/component/WR/hjc/Ubf;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->BcC()Lcom/bytedance/sdk/component/WR/hjc/eV;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/WR/hjc/eV;->ex()V
    :try_end_1
    .catch Ljava/lang/Exception; {:try_start_1 .. :try_end_1} :catch_0
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    goto :goto_0

    :catchall_0
    move-exception v0

    goto :goto_1

    :cond_0
    :goto_0
    monitor-exit p0

    return-void

    :catch_0
    :cond_1
    monitor-exit p0

    return-void

    :goto_1
    monitor-exit p0

    throw v0
.end method

.method public hjc(Z)V
    .locals 1

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/WR/hjc/Fj;->eV()V

    const/4 v0, 0x1

    iput-boolean v0, p0, Lcom/bytedance/sdk/component/WR/hjc/Fj;->Ubf:Z

    if-nez p1, :cond_0

    iget-object p1, p0, Lcom/bytedance/sdk/component/WR/hjc/Fj;->Fj:Lcom/bytedance/sdk/component/utils/Vq;

    const/16 v0, 0x66

    invoke-virtual {p1, v0}, Landroid/os/Handler;->sendEmptyMessage(I)Z

    return-void

    :cond_0
    :try_start_0
    invoke-direct {p0}, Lcom/bytedance/sdk/component/WR/hjc/Fj;->BcC()Z
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    return-void

    :catch_0
    iget-object p1, p0, Lcom/bytedance/sdk/component/WR/hjc/Fj;->Ko:Ljava/util/concurrent/atomic/AtomicBoolean;

    const/4 v0, 0x0

    invoke-virtual {p1, v0}, Ljava/util/concurrent/atomic/AtomicBoolean;->set(Z)V

    return-void
.end method

.method public hjc()Z
    .locals 2

    iget-object v0, p0, Lcom/bytedance/sdk/component/WR/hjc/Fj;->Ko:Ljava/util/concurrent/atomic/AtomicBoolean;

    invoke-virtual {v0}, Ljava/util/concurrent/atomic/AtomicBoolean;->get()Z

    invoke-static {}, Lcom/bytedance/sdk/component/WR/hjc/Fj;->svN()Ljava/util/concurrent/ThreadPoolExecutor;

    move-result-object v0

    new-instance v1, Lcom/bytedance/sdk/component/WR/hjc/Fj$2;

    invoke-direct {v1, p0}, Lcom/bytedance/sdk/component/WR/hjc/Fj$2;-><init>(Lcom/bytedance/sdk/component/WR/hjc/Fj;)V

    invoke-virtual {v0, v1}, Ljava/util/concurrent/ThreadPoolExecutor;->execute(Ljava/lang/Runnable;)V

    const/4 v0, 0x1

    return v0
.end method
