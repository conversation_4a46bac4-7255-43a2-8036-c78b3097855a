.class public Lcom/bytedance/sdk/component/WR/hjc/Ubf;
.super Ljava/lang/Object;


# instance fields
.field private BcC:Lcom/bytedance/sdk/component/WR/hjc/eV;

.field Fj:Landroid/os/Handler;

.field private JU:Ljava/util/HashMap;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/HashMap<",
            "Ljava/lang/String;",
            "Ljava/lang/Integer;",
            ">;"
        }
    .end annotation
.end field

.field private JW:Ljava/util/HashMap;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/HashMap<",
            "Ljava/lang/String;",
            "Ljava/lang/Integer;",
            ">;"
        }
    .end annotation
.end field

.field private Ko:J

.field private Ql:Z

.field private Tc:I

.field private UYd:Ljava/util/HashMap;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/HashMap<",
            "Ljava/lang/String;",
            "Ljava/lang/Integer;",
            ">;"
        }
    .end annotation
.end field

.field private Ubf:Lcom/bytedance/sdk/component/WR/hjc/ex;

.field private WR:Z

.field private dG:Ljava/util/HashMap;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/HashMap<",
            "Ljava/lang/String;",
            "Ljava/lang/Integer;",
            ">;"
        }
    .end annotation
.end field

.field private eV:Z

.field private ex:J

.field private hjc:Lcom/bytedance/sdk/component/WR/hjc/Fj;

.field private mSE:I

.field private rAx:I

.field private rS:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/Integer;",
            ">;"
        }
    .end annotation
.end field

.field private svN:Landroid/content/Context;

.field private vYf:I


# direct methods
.method private constructor <init>()V
    .locals 3

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const-wide/16 v0, 0x0

    iput-wide v0, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->ex:J

    const/4 v0, 0x0

    iput-boolean v0, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->WR:Z

    iput v0, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->mSE:I

    const-wide v1, 0x49637af88L

    iput-wide v1, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->Ko:J

    iput v0, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->rAx:I

    new-instance v1, Ljava/util/HashMap;

    invoke-direct {v1}, Ljava/util/HashMap;-><init>()V

    iput-object v1, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->UYd:Ljava/util/HashMap;

    new-instance v1, Ljava/util/HashMap;

    invoke-direct {v1}, Ljava/util/HashMap;-><init>()V

    iput-object v1, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->dG:Ljava/util/HashMap;

    iput v0, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->Tc:I

    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    iput-object v0, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->JW:Ljava/util/HashMap;

    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    iput-object v0, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->JU:Ljava/util/HashMap;

    const/4 v0, 0x1

    iput-boolean v0, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->Ql:Z

    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    iput-object v0, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->rS:Ljava/util/Map;

    new-instance v0, Lcom/bytedance/sdk/component/WR/hjc/Ubf$1;

    invoke-static {}, Landroid/os/Looper;->getMainLooper()Landroid/os/Looper;

    move-result-object v1

    invoke-direct {v0, p0, v1}, Lcom/bytedance/sdk/component/WR/hjc/Ubf$1;-><init>(Lcom/bytedance/sdk/component/WR/hjc/Ubf;Landroid/os/Looper;)V

    iput-object v0, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->Fj:Landroid/os/Handler;

    return-void
.end method

.method public constructor <init>(I)V
    .locals 3

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const-wide/16 v0, 0x0

    iput-wide v0, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->ex:J

    const/4 v0, 0x0

    iput-boolean v0, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->WR:Z

    iput v0, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->mSE:I

    const-wide v1, 0x49637af88L

    iput-wide v1, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->Ko:J

    iput v0, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->rAx:I

    new-instance v1, Ljava/util/HashMap;

    invoke-direct {v1}, Ljava/util/HashMap;-><init>()V

    iput-object v1, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->UYd:Ljava/util/HashMap;

    new-instance v1, Ljava/util/HashMap;

    invoke-direct {v1}, Ljava/util/HashMap;-><init>()V

    iput-object v1, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->dG:Ljava/util/HashMap;

    iput v0, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->Tc:I

    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    iput-object v0, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->JW:Ljava/util/HashMap;

    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    iput-object v0, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->JU:Ljava/util/HashMap;

    const/4 v0, 0x1

    iput-boolean v0, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->Ql:Z

    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    iput-object v0, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->rS:Ljava/util/Map;

    new-instance v0, Lcom/bytedance/sdk/component/WR/hjc/Ubf$1;

    invoke-static {}, Landroid/os/Looper;->getMainLooper()Landroid/os/Looper;

    move-result-object v1

    invoke-direct {v0, p0, v1}, Lcom/bytedance/sdk/component/WR/hjc/Ubf$1;-><init>(Lcom/bytedance/sdk/component/WR/hjc/Ubf;Landroid/os/Looper;)V

    iput-object v0, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->Fj:Landroid/os/Handler;

    iput p1, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->vYf:I

    return-void
.end method

.method private Fj(Lcom/bytedance/sdk/component/ex/Fj/dG;)Ljava/lang/String;
    .locals 2

    const-string v0, ""

    if-eqz p1, :cond_1

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/ex/Fj/dG;->ex()Lcom/bytedance/sdk/component/ex/Fj/svN;

    move-result-object v1

    if-eqz v1, :cond_1

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/ex/Fj/dG;->ex()Lcom/bytedance/sdk/component/ex/Fj/svN;

    move-result-object v1

    invoke-virtual {v1}, Lcom/bytedance/sdk/component/ex/Fj/svN;->Fj()Ljava/net/URL;

    move-result-object v1

    if-nez v1, :cond_0

    goto :goto_0

    :cond_0
    :try_start_0
    invoke-virtual {p1}, Lcom/bytedance/sdk/component/ex/Fj/dG;->ex()Lcom/bytedance/sdk/component/ex/Fj/svN;

    move-result-object p1

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/ex/Fj/svN;->Fj()Ljava/net/URL;

    move-result-object p1

    invoke-virtual {p1}, Ljava/net/URL;->getHost()Ljava/lang/String;

    move-result-object p1

    invoke-static {p1}, Ljava/net/InetAddress;->getByName(Ljava/lang/String;)Ljava/net/InetAddress;

    move-result-object p1

    invoke-virtual {p1}, Ljava/net/InetAddress;->getHostAddress()Ljava/lang/String;

    move-result-object v0
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    :catch_0
    :cond_1
    :goto_0
    return-object v0
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/component/WR/hjc/Ubf;Z)V
    .locals 0

    invoke-direct {p0, p1}, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->ex(Z)V

    return-void
.end method

.method private Fj(Lcom/bytedance/sdk/component/ex/Fj/JW;Ljava/lang/String;)V
    .locals 8

    if-nez p1, :cond_0

    return-void

    :cond_0
    iget-boolean p2, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->Ql:Z

    if-nez p2, :cond_1

    return-void

    :cond_1
    const-string p2, "tnc-cmd"

    const/4 v0, 0x0

    invoke-virtual {p1, p2, v0}, Lcom/bytedance/sdk/component/ex/Fj/JW;->Fj(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    invoke-static {p1}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result p2

    if-eqz p2, :cond_2

    return-void

    :cond_2
    const-string p2, "@"

    invoke-virtual {p1, p2}, Ljava/lang/String;->split(Ljava/lang/String;)[Ljava/lang/String;

    move-result-object p1

    if-eqz p1, :cond_7

    array-length p2, p1

    const/4 v0, 0x2

    if-eq p2, v0, :cond_3

    goto :goto_2

    :cond_3
    const/4 p2, 0x1

    const-wide/16 v0, 0x0

    const/4 v2, 0x0

    :try_start_0
    aget-object v3, p1, v2

    invoke-static {v3}, Ljava/lang/Integer;->parseInt(Ljava/lang/String;)I

    move-result v3
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_1

    :try_start_1
    aget-object p1, p1, p2

    invoke-static {p1}, Ljava/lang/Long;->parseLong(Ljava/lang/String;)J

    move-result-wide v4
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    goto :goto_1

    :catchall_0
    nop

    goto :goto_0

    :catchall_1
    nop

    const/4 v3, 0x0

    :goto_0
    move-wide v4, v0

    :goto_1
    iget-wide v6, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->Ko:J

    cmp-long p1, v4, v6

    if-gtz p1, :cond_4

    return-void

    :cond_4
    iput v3, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->mSE:I

    iput-wide v4, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->Ko:J

    iget-object p1, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->svN:Landroid/content/Context;

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->Fj()Ljava/lang/String;

    move-result-object v6

    invoke-virtual {p1, v6, v2}, Landroid/content/Context;->getSharedPreferences(Ljava/lang/String;I)Landroid/content/SharedPreferences;

    move-result-object p1

    invoke-interface {p1}, Landroid/content/SharedPreferences;->edit()Landroid/content/SharedPreferences$Editor;

    move-result-object p1

    const-string v2, "tnc_probe_cmd"

    invoke-interface {p1, v2, v3}, Landroid/content/SharedPreferences$Editor;->putInt(Ljava/lang/String;I)Landroid/content/SharedPreferences$Editor;

    move-result-object p1

    const-string v2, "tnc_probe_version"

    invoke-interface {p1, v2, v4, v5}, Landroid/content/SharedPreferences$Editor;->putLong(Ljava/lang/String;J)Landroid/content/SharedPreferences$Editor;

    move-result-object p1

    invoke-interface {p1}, Landroid/content/SharedPreferences$Editor;->apply()V

    iget p1, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->mSE:I

    const/16 v2, 0x2710

    if-ne p1, v2, :cond_7

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->WR()Lcom/bytedance/sdk/component/WR/hjc/hjc;

    move-result-object p1

    if-nez p1, :cond_5

    return-void

    :cond_5
    new-instance v2, Ljava/util/Random;

    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v3

    invoke-direct {v2, v3, v4}, Ljava/util/Random;-><init>(J)V

    iget p1, p1, Lcom/bytedance/sdk/component/WR/hjc/hjc;->UYd:I

    if-lez p1, :cond_6

    invoke-virtual {v2, p1}, Ljava/util/Random;->nextInt(I)I

    move-result p1

    int-to-long v0, p1

    const-wide/16 v2, 0x3e8

    mul-long v0, v0, v2

    :cond_6
    invoke-direct {p0, p2, v0, v1}, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->Fj(ZJ)V

    :cond_7
    :goto_2
    return-void
.end method

.method private Fj(ZJ)V
    .locals 3

    iget-object v0, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->Fj:Landroid/os/Handler;

    const/16 v1, 0x2710

    invoke-virtual {v0, v1}, Landroid/os/Handler;->hasMessages(I)Z

    move-result v0

    if-eqz v0, :cond_0

    return-void

    :cond_0
    iget-object v0, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->Fj:Landroid/os/Handler;

    invoke-virtual {v0}, Landroid/os/Handler;->obtainMessage()Landroid/os/Message;

    move-result-object v0

    iput v1, v0, Landroid/os/Message;->what:I

    iput p1, v0, Landroid/os/Message;->arg1:I

    const-wide/16 v1, 0x0

    cmp-long p1, p2, v1

    if-lez p1, :cond_1

    iget-object p1, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->Fj:Landroid/os/Handler;

    invoke-virtual {p1, v0, p2, p3}, Landroid/os/Handler;->sendMessageDelayed(Landroid/os/Message;J)Z

    return-void

    :cond_1
    iget-object p1, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->Fj:Landroid/os/Handler;

    invoke-virtual {p1, v0}, Landroid/os/Handler;->sendMessage(Landroid/os/Message;)Z

    return-void
.end method

.method private Fj(I)Z
    .locals 1

    const/16 v0, 0xc8

    if-lt p1, v0, :cond_0

    const/16 v0, 0x190

    if-ge p1, v0, :cond_0

    const/4 p1, 0x1

    return p1

    :cond_0
    const/4 p1, 0x0

    return p1
.end method

.method private Ko()V
    .locals 2

    const/4 v0, 0x0

    iput v0, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->rAx:I

    iget-object v1, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->UYd:Ljava/util/HashMap;

    invoke-virtual {v1}, Ljava/util/HashMap;->clear()V

    iget-object v1, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->dG:Ljava/util/HashMap;

    invoke-virtual {v1}, Ljava/util/HashMap;->clear()V

    iput v0, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->Tc:I

    iget-object v0, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->JW:Ljava/util/HashMap;

    invoke-virtual {v0}, Ljava/util/HashMap;->clear()V

    iget-object v0, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->JU:Ljava/util/HashMap;

    invoke-virtual {v0}, Ljava/util/HashMap;->clear()V

    return-void
.end method

.method private eV(Ljava/lang/String;)Z
    .locals 2

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->svN()Ljava/util/Map;

    move-result-object v0

    const/4 v1, 0x0

    if-nez v0, :cond_0

    return v1

    :cond_0
    invoke-interface {v0, p1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/lang/String;

    invoke-static {p1}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    if-nez v0, :cond_2

    iget-object v0, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->rS:Ljava/util/Map;

    invoke-interface {v0, p1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    if-nez v0, :cond_1

    goto :goto_0

    :cond_1
    iget-object v0, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->rS:Ljava/util/Map;

    invoke-interface {v0, p1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/lang/Integer;

    invoke-virtual {p1}, Ljava/lang/Integer;->intValue()I

    move-result p1

    const/4 v0, 0x3

    if-lt p1, v0, :cond_2

    const/4 p1, 0x1

    return p1

    :cond_2
    :goto_0
    return v1
.end method

.method private ex(Ljava/lang/String;)V
    .locals 3

    invoke-static {p1}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    if-eqz v0, :cond_0

    return-void

    :cond_0
    invoke-virtual {p0}, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->svN()Ljava/util/Map;

    move-result-object v0

    if-eqz v0, :cond_3

    invoke-interface {v0, p1}, Ljava/util/Map;->containsValue(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_1

    goto :goto_0

    :cond_1
    iget-object v0, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->rS:Ljava/util/Map;

    invoke-interface {v0, p1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    const/4 v1, 0x1

    if-nez v0, :cond_2

    iget-object v0, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->rS:Ljava/util/Map;

    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    invoke-interface {v0, p1, v1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    return-void

    :cond_2
    iget-object v0, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->rS:Ljava/util/Map;

    invoke-interface {v0, p1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/lang/Integer;

    invoke-virtual {v0}, Ljava/lang/Integer;->intValue()I

    move-result v0

    iget-object v2, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->rS:Ljava/util/Map;

    add-int/2addr v0, v1

    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v0

    invoke-interface {v2, p1, v0}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    :cond_3
    :goto_0
    return-void
.end method

.method private ex(Z)V
    .locals 9

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->WR()Lcom/bytedance/sdk/component/WR/hjc/hjc;

    move-result-object v0

    if-nez v0, :cond_0

    return-void

    :cond_0
    invoke-static {}, Landroid/os/SystemClock;->elapsedRealtime()J

    move-result-wide v1

    if-nez p1, :cond_1

    iget-wide v3, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->ex:J

    iget p1, v0, Lcom/bytedance/sdk/component/WR/hjc/hjc;->rAx:I

    int-to-long v5, p1

    const-wide/16 v7, 0x3e8

    mul-long v5, v5, v7

    add-long/2addr v3, v5

    cmp-long p1, v3, v1

    if-lez p1, :cond_1

    return-void

    :cond_1
    iput-wide v1, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->ex:J

    invoke-static {}, Lcom/bytedance/sdk/component/WR/hjc/svN;->Fj()Lcom/bytedance/sdk/component/WR/hjc/svN;

    move-result-object p1

    iget v0, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->vYf:I

    iget-object v1, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->svN:Landroid/content/Context;

    invoke-virtual {p1, v0, v1}, Lcom/bytedance/sdk/component/WR/hjc/svN;->Fj(ILandroid/content/Context;)Lcom/bytedance/sdk/component/WR/hjc/Fj;

    move-result-object p1

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/WR/hjc/Fj;->hjc()Z

    return-void
.end method

.method private ex(I)Z
    .locals 3

    const/16 v0, 0x64

    const/4 v1, 0x1

    if-lt p1, v0, :cond_2

    const/16 v0, 0x3e8

    if-lt p1, v0, :cond_0

    goto :goto_0

    :cond_0
    invoke-virtual {p0}, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->WR()Lcom/bytedance/sdk/component/WR/hjc/hjc;

    move-result-object v0

    if-eqz v0, :cond_1

    iget-object v2, v0, Lcom/bytedance/sdk/component/WR/hjc/hjc;->dG:Ljava/lang/String;

    invoke-static {v2}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v2

    if-nez v2, :cond_1

    iget-object v0, v0, Lcom/bytedance/sdk/component/WR/hjc/hjc;->dG:Ljava/lang/String;

    invoke-static {p1}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v0, p1}, Ljava/lang/String;->contains(Ljava/lang/CharSequence;)Z

    move-result p1

    if-eqz p1, :cond_1

    return v1

    :cond_1
    const/4 p1, 0x0

    return p1

    :cond_2
    :goto_0
    return v1
.end method

.method private hjc(Ljava/lang/String;)V
    .locals 2

    invoke-static {p1}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    if-eqz v0, :cond_0

    return-void

    :cond_0
    iget-object v0, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->rS:Ljava/util/Map;

    invoke-interface {v0, p1}, Ljava/util/Map;->containsKey(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_1

    return-void

    :cond_1
    iget-object v0, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->rS:Ljava/util/Map;

    const/4 v1, 0x0

    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    invoke-interface {v0, p1, v1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    return-void
.end method

.method private mSE()V
    .locals 4

    iget-object v0, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->svN:Landroid/content/Context;

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->Fj()Ljava/lang/String;

    move-result-object v1

    const/4 v2, 0x0

    invoke-virtual {v0, v1, v2}, Landroid/content/Context;->getSharedPreferences(Ljava/lang/String;I)Landroid/content/SharedPreferences;

    move-result-object v0

    const-string v1, "tnc_probe_cmd"

    invoke-interface {v0, v1, v2}, Landroid/content/SharedPreferences;->getInt(Ljava/lang/String;I)I

    move-result v1

    iput v1, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->mSE:I

    const-string v1, "tnc_probe_version"

    const-wide v2, 0x49637af88L

    invoke-interface {v0, v1, v2, v3}, Landroid/content/SharedPreferences;->getLong(Ljava/lang/String;J)J

    move-result-wide v0

    iput-wide v0, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->Ko:J

    return-void
.end method


# virtual methods
.method public BcC()Lcom/bytedance/sdk/component/WR/hjc/eV;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->BcC:Lcom/bytedance/sdk/component/WR/hjc/eV;

    return-object v0
.end method

.method public Fj()Ljava/lang/String;
    .locals 2

    new-instance v0, Ljava/lang/StringBuilder;

    const-string v1, "ttnet_tnc_config"

    invoke-direct {v0, v1}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    iget v1, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->vYf:I

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public Fj(Ljava/lang/String;)Ljava/lang/String;
    .locals 5

    invoke-static {p1}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    if-nez v0, :cond_6

    const-string v0, "/network/get_network"

    invoke-virtual {p1, v0}, Ljava/lang/String;->contains(Ljava/lang/CharSequence;)Z

    move-result v0

    if-nez v0, :cond_6

    const-string v0, "/get_domains/v4"

    invoke-virtual {p1, v0}, Ljava/lang/String;->contains(Ljava/lang/CharSequence;)Z

    move-result v0

    if-nez v0, :cond_6

    const-string v0, "/ies/speed"

    invoke-virtual {p1, v0}, Ljava/lang/String;->contains(Ljava/lang/CharSequence;)Z

    move-result v0

    if-eqz v0, :cond_0

    goto/16 :goto_1

    :cond_0
    const/4 v0, 0x0

    :try_start_0
    new-instance v1, Ljava/net/URL;

    invoke-direct {v1, p1}, Ljava/net/URL;-><init>(Ljava/lang/String;)V

    invoke-virtual {v1}, Ljava/net/URL;->getProtocol()Ljava/lang/String;

    move-result-object v2
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_1

    :try_start_1
    invoke-virtual {v1}, Ljava/net/URL;->getHost()Ljava/lang/String;

    move-result-object v0
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    goto :goto_0

    :catchall_0
    nop

    goto :goto_0

    :catchall_1
    nop

    move-object v2, v0

    :goto_0
    invoke-static {v2}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v1

    if-nez v1, :cond_6

    const-string v1, "http"

    invoke-virtual {v1, v2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_1

    const-string v1, "https"

    invoke-virtual {v1, v2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_6

    :cond_1
    invoke-static {v0}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v1

    if-eqz v1, :cond_2

    goto :goto_1

    :cond_2
    invoke-direct {p0, v0}, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->eV(Ljava/lang/String;)Z

    move-result v1

    if-eqz v1, :cond_3

    return-object p1

    :cond_3
    invoke-virtual {p0}, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->svN()Ljava/util/Map;

    move-result-object v1

    if-eqz v1, :cond_6

    invoke-interface {v1, v0}, Ljava/util/Map;->containsKey(Ljava/lang/Object;)Z

    move-result v3

    if-nez v3, :cond_4

    goto :goto_1

    :cond_4
    invoke-interface {v1, v0}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/lang/String;

    invoke-static {v1}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v3

    if-eqz v3, :cond_5

    return-object p1

    :cond_5
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v3, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v4, "://"

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v3, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p1, v0}, Ljava/lang/String;->startsWith(Ljava/lang/String;)Z

    move-result v2

    if-eqz v2, :cond_6

    invoke-virtual {p1, v0, v1}, Ljava/lang/String;->replaceFirst(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    :cond_6
    :goto_1
    return-object p1
.end method

.method public declared-synchronized Fj(Landroid/content/Context;Z)V
    .locals 2

    monitor-enter p0

    :try_start_0
    iget-boolean v0, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->WR:Z

    if-nez v0, :cond_1

    iput-object p1, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->svN:Landroid/content/Context;

    iput-boolean p2, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->Ql:Z

    new-instance v0, Lcom/bytedance/sdk/component/WR/hjc/eV;

    iget v1, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->vYf:I

    invoke-direct {v0, p1, p2, v1}, Lcom/bytedance/sdk/component/WR/hjc/eV;-><init>(Landroid/content/Context;ZI)V

    iput-object v0, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->BcC:Lcom/bytedance/sdk/component/WR/hjc/eV;

    if-eqz p2, :cond_0

    invoke-direct {p0}, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->mSE()V

    goto :goto_0

    :catchall_0
    move-exception p1

    goto :goto_1

    :cond_0
    :goto_0
    invoke-static {}, Lcom/bytedance/sdk/component/WR/hjc/svN;->Fj()Lcom/bytedance/sdk/component/WR/hjc/svN;

    move-result-object p1

    iget p2, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->vYf:I

    iget-object v0, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->svN:Landroid/content/Context;

    invoke-virtual {p1, p2, v0}, Lcom/bytedance/sdk/component/WR/hjc/svN;->Fj(ILandroid/content/Context;)Lcom/bytedance/sdk/component/WR/hjc/Fj;

    move-result-object p1

    iput-object p1, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->hjc:Lcom/bytedance/sdk/component/WR/hjc/Fj;

    const/4 p1, 0x1

    iput-boolean p1, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->WR:Z
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    :cond_1
    monitor-exit p0

    return-void

    :goto_1
    monitor-exit p0

    throw p1
.end method

.method public Fj(Lcom/bytedance/sdk/component/WR/hjc/ex;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->Ubf:Lcom/bytedance/sdk/component/WR/hjc/ex;

    return-void
.end method

.method public declared-synchronized Fj(Lcom/bytedance/sdk/component/ex/Fj/dG;Lcom/bytedance/sdk/component/ex/Fj/JW;)V
    .locals 5

    monitor-enter p0

    if-eqz p1, :cond_d

    if-nez p2, :cond_0

    goto/16 :goto_2

    :cond_0
    :try_start_0
    iget-boolean v0, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->Ql:Z
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    if-nez v0, :cond_1

    monitor-exit p0

    return-void

    :cond_1
    :try_start_1
    iget-object v0, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->svN:Landroid/content/Context;

    invoke-static {v0}, Lcom/bytedance/sdk/component/WR/eV/Ubf;->Fj(Landroid/content/Context;)Z

    move-result v0
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    if-nez v0, :cond_2

    monitor-exit p0

    return-void

    :cond_2
    :try_start_2
    invoke-virtual {p1}, Lcom/bytedance/sdk/component/ex/Fj/dG;->ex()Lcom/bytedance/sdk/component/ex/Fj/svN;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/ex/Fj/svN;->Fj()Ljava/net/URL;

    move-result-object v0
    :try_end_2
    .catch Ljava/lang/Exception; {:try_start_2 .. :try_end_2} :catch_0
    .catchall {:try_start_2 .. :try_end_2} :catchall_0

    goto :goto_0

    :catchall_0
    move-exception p1

    goto/16 :goto_1

    :catch_0
    const/4 v0, 0x0

    :goto_0
    if-nez v0, :cond_3

    monitor-exit p0

    return-void

    :cond_3
    :try_start_3
    invoke-virtual {v0}, Ljava/net/URL;->getProtocol()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0}, Ljava/net/URL;->getHost()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v0}, Ljava/net/URL;->getPath()Ljava/lang/String;

    move-result-object v0

    invoke-direct {p0, p1}, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->Fj(Lcom/bytedance/sdk/component/ex/Fj/dG;)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p2}, Lcom/bytedance/sdk/component/ex/Fj/JW;->hjc()I

    move-result v3

    const-string v4, "http"

    invoke-virtual {v4, v1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v4

    if-nez v4, :cond_4

    const-string v4, "https"

    invoke-virtual {v4, v1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v1
    :try_end_3
    .catchall {:try_start_3 .. :try_end_3} :catchall_0

    if-nez v1, :cond_4

    monitor-exit p0

    return-void

    :cond_4
    :try_start_4
    invoke-static {p1}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v1
    :try_end_4
    .catchall {:try_start_4 .. :try_end_4} :catchall_0

    if-eqz v1, :cond_5

    monitor-exit p0

    return-void

    :cond_5
    :try_start_5
    invoke-virtual {p0}, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->WR()Lcom/bytedance/sdk/component/WR/hjc/hjc;

    move-result-object v1

    if-eqz v1, :cond_6

    iget-boolean v4, v1, Lcom/bytedance/sdk/component/WR/hjc/hjc;->ex:Z

    if-eqz v4, :cond_6

    invoke-direct {p0, p2, v2}, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->Fj(Lcom/bytedance/sdk/component/ex/Fj/JW;Ljava/lang/String;)V
    :try_end_5
    .catchall {:try_start_5 .. :try_end_5} :catchall_0

    :cond_6
    if-nez v1, :cond_7

    monitor-exit p0

    return-void

    :cond_7
    :try_start_6
    iget-object p2, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->UYd:Ljava/util/HashMap;

    invoke-virtual {p2}, Ljava/util/HashMap;->size()I

    iget-object p2, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->dG:Ljava/util/HashMap;

    invoke-virtual {p2}, Ljava/util/HashMap;->size()I

    iget-object p2, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->JW:Ljava/util/HashMap;

    invoke-virtual {p2}, Ljava/util/HashMap;->size()I

    iget-object p2, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->JU:Ljava/util/HashMap;

    invoke-virtual {p2}, Ljava/util/HashMap;->size()I

    if-lez v3, :cond_c

    invoke-direct {p0, v3}, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->Fj(I)Z

    move-result p2

    if-eqz p2, :cond_a

    iget p1, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->rAx:I

    if-gtz p1, :cond_8

    iget p1, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->Tc:I

    if-lez p1, :cond_9

    :cond_8
    invoke-direct {p0}, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->Ko()V

    :cond_9
    invoke-direct {p0, v2}, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->hjc(Ljava/lang/String;)V
    :try_end_6
    .catchall {:try_start_6 .. :try_end_6} :catchall_0

    monitor-exit p0

    return-void

    :cond_a
    :try_start_7
    invoke-direct {p0, v3}, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->ex(I)Z

    move-result p2

    if-nez p2, :cond_c

    iget p2, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->Tc:I

    add-int/lit8 p2, p2, 0x1

    iput p2, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->Tc:I

    iget-object p2, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->JW:Ljava/util/HashMap;

    const/4 v3, 0x0

    invoke-static {v3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v4

    invoke-virtual {p2, v0, v4}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    iget-object p2, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->JU:Ljava/util/HashMap;

    invoke-static {v3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v0

    invoke-virtual {p2, p1, v0}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    iget p1, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->Tc:I

    iget p2, v1, Lcom/bytedance/sdk/component/WR/hjc/hjc;->BcC:I

    if-lt p1, p2, :cond_b

    iget-object p1, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->JW:Ljava/util/HashMap;

    invoke-virtual {p1}, Ljava/util/HashMap;->size()I

    move-result p1

    iget p2, v1, Lcom/bytedance/sdk/component/WR/hjc/hjc;->mSE:I

    if-lt p1, p2, :cond_b

    iget-object p1, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->JU:Ljava/util/HashMap;

    invoke-virtual {p1}, Ljava/util/HashMap;->size()I

    move-result p1

    iget p2, v1, Lcom/bytedance/sdk/component/WR/hjc/hjc;->Ko:I

    if-lt p1, p2, :cond_b

    const-wide/16 p1, 0x0

    invoke-direct {p0, v3, p1, p2}, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->Fj(ZJ)V

    invoke-direct {p0}, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->Ko()V

    :cond_b
    invoke-direct {p0, v2}, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->ex(Ljava/lang/String;)V
    :try_end_7
    .catchall {:try_start_7 .. :try_end_7} :catchall_0

    :cond_c
    monitor-exit p0

    return-void

    :goto_1
    monitor-exit p0

    throw p1

    :cond_d
    :goto_2
    monitor-exit p0

    return-void
.end method

.method public declared-synchronized Fj(Lcom/bytedance/sdk/component/ex/Fj/dG;Ljava/lang/Exception;)V
    .locals 5

    monitor-enter p0

    if-eqz p1, :cond_7

    :try_start_0
    invoke-virtual {p1}, Lcom/bytedance/sdk/component/ex/Fj/dG;->ex()Lcom/bytedance/sdk/component/ex/Fj/svN;

    move-result-object v0

    if-eqz v0, :cond_7

    if-nez p2, :cond_0

    goto/16 :goto_2

    :cond_0
    iget-boolean p2, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->Ql:Z
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    if-nez p2, :cond_1

    monitor-exit p0

    return-void

    :cond_1
    :try_start_1
    iget-object p2, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->svN:Landroid/content/Context;

    invoke-static {p2}, Lcom/bytedance/sdk/component/WR/eV/Ubf;->Fj(Landroid/content/Context;)Z

    move-result p2
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    if-nez p2, :cond_2

    monitor-exit p0

    return-void

    :cond_2
    :try_start_2
    invoke-virtual {p1}, Lcom/bytedance/sdk/component/ex/Fj/dG;->ex()Lcom/bytedance/sdk/component/ex/Fj/svN;

    move-result-object p2

    invoke-virtual {p2}, Lcom/bytedance/sdk/component/ex/Fj/svN;->Fj()Ljava/net/URL;

    move-result-object p2
    :try_end_2
    .catch Ljava/lang/Exception; {:try_start_2 .. :try_end_2} :catch_0
    .catchall {:try_start_2 .. :try_end_2} :catchall_0

    goto :goto_0

    :catchall_0
    move-exception p1

    goto/16 :goto_1

    :catch_0
    const/4 p2, 0x0

    :goto_0
    if-nez p2, :cond_3

    monitor-exit p0

    return-void

    :cond_3
    :try_start_3
    invoke-virtual {p2}, Ljava/net/URL;->getProtocol()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p2}, Ljava/net/URL;->getHost()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p2}, Ljava/net/URL;->getPath()Ljava/lang/String;

    move-result-object p2

    invoke-direct {p0, p1}, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->Fj(Lcom/bytedance/sdk/component/ex/Fj/dG;)Ljava/lang/String;

    move-result-object p1

    const-string v2, "http"

    invoke-virtual {v2, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v2

    if-nez v2, :cond_4

    const-string v2, "https"

    invoke-virtual {v2, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0
    :try_end_3
    .catchall {:try_start_3 .. :try_end_3} :catchall_0

    if-nez v0, :cond_4

    monitor-exit p0

    return-void

    :cond_4
    :try_start_4
    invoke-virtual {p0}, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->WR()Lcom/bytedance/sdk/component/WR/hjc/hjc;

    move-result-object v0
    :try_end_4
    .catchall {:try_start_4 .. :try_end_4} :catchall_0

    if-nez v0, :cond_5

    monitor-exit p0

    return-void

    :cond_5
    :try_start_5
    iget-object v2, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->UYd:Ljava/util/HashMap;

    invoke-virtual {v2}, Ljava/util/HashMap;->size()I

    iget-object v2, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->dG:Ljava/util/HashMap;

    invoke-virtual {v2}, Ljava/util/HashMap;->size()I

    iget-object v2, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->JW:Ljava/util/HashMap;

    invoke-virtual {v2}, Ljava/util/HashMap;->size()I

    iget-object v2, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->JU:Ljava/util/HashMap;

    invoke-virtual {v2}, Ljava/util/HashMap;->size()I

    iget v2, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->rAx:I

    add-int/lit8 v2, v2, 0x1

    iput v2, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->rAx:I

    iget-object v2, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->UYd:Ljava/util/HashMap;

    const/4 v3, 0x0

    invoke-static {v3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v4

    invoke-virtual {v2, p2, v4}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    iget-object p2, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->dG:Ljava/util/HashMap;

    invoke-static {v3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v2

    invoke-virtual {p2, p1, v2}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    iget p1, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->rAx:I

    iget p2, v0, Lcom/bytedance/sdk/component/WR/hjc/hjc;->Ubf:I

    if-lt p1, p2, :cond_6

    iget-object p1, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->UYd:Ljava/util/HashMap;

    invoke-virtual {p1}, Ljava/util/HashMap;->size()I

    move-result p1

    iget p2, v0, Lcom/bytedance/sdk/component/WR/hjc/hjc;->WR:I

    if-lt p1, p2, :cond_6

    iget-object p1, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->dG:Ljava/util/HashMap;

    invoke-virtual {p1}, Ljava/util/HashMap;->size()I

    move-result p1

    iget p2, v0, Lcom/bytedance/sdk/component/WR/hjc/hjc;->svN:I

    if-lt p1, p2, :cond_6

    const-wide/16 p1, 0x0

    invoke-direct {p0, v3, p1, p2}, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->Fj(ZJ)V

    invoke-direct {p0}, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->Ko()V

    :cond_6
    invoke-direct {p0, v1}, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->ex(Ljava/lang/String;)V
    :try_end_5
    .catchall {:try_start_5 .. :try_end_5} :catchall_0

    monitor-exit p0

    return-void

    :goto_1
    monitor-exit p0

    throw p1

    :cond_7
    :goto_2
    monitor-exit p0

    return-void
.end method

.method public Fj(Z)V
    .locals 0

    iput-boolean p1, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->eV:Z

    return-void
.end method

.method public Ubf()V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->rS:Ljava/util/Map;

    invoke-interface {v0}, Ljava/util/Map;->clear()V

    return-void
.end method

.method public WR()Lcom/bytedance/sdk/component/WR/hjc/hjc;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->BcC:Lcom/bytedance/sdk/component/WR/hjc/eV;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/WR/hjc/eV;->hjc()Lcom/bytedance/sdk/component/WR/hjc/hjc;

    move-result-object v0

    return-object v0

    :cond_0
    const/4 v0, 0x0

    return-object v0
.end method

.method public eV()Lcom/bytedance/sdk/component/WR/hjc/ex;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->Ubf:Lcom/bytedance/sdk/component/WR/hjc/ex;

    return-object v0
.end method

.method public ex()Lcom/bytedance/sdk/component/WR/hjc/Fj;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->hjc:Lcom/bytedance/sdk/component/WR/hjc/Fj;

    return-object v0
.end method

.method public hjc()Z
    .locals 1

    iget-boolean v0, p0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->eV:Z

    return v0
.end method

.method public svN()Ljava/util/Map;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/WR/hjc/Ubf;->WR()Lcom/bytedance/sdk/component/WR/hjc/hjc;

    move-result-object v0

    if-eqz v0, :cond_0

    iget-object v0, v0, Lcom/bytedance/sdk/component/WR/hjc/hjc;->eV:Ljava/util/Map;

    return-object v0

    :cond_0
    const/4 v0, 0x0

    return-object v0
.end method
