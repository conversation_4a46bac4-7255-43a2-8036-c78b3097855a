.class public interface abstract Lcom/bytedance/sdk/component/WR/hjc/ex;
.super Ljava/lang/Object;


# virtual methods
.method public abstract Fj()I
.end method

.method public abstract Fj(Landroid/content/Context;)Landroid/location/Address;
.end method

.method public abstract Fj(Landroid/content/Context;Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;
.end method

.method public abstract Fj(Landroid/content/Context;Ljava/util/Map;)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/content/Context;",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "*>;)V"
        }
    .end annotation
.end method

.method public abstract Ubf()Ljava/lang/String;
.end method

.method public abstract WR()[Ljava/lang/String;
.end method

.method public abstract eV()I
.end method

.method public abstract ex()Ljava/lang/String;
.end method

.method public abstract hjc()Ljava/lang/String;
.end method
