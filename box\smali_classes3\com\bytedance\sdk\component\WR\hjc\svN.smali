.class public Lcom/bytedance/sdk/component/WR/hjc/svN;
.super Ljava/lang/Object;


# static fields
.field private static volatile Fj:Lcom/bytedance/sdk/component/WR/hjc/svN;

.field private static ex:Ljava/util/HashMap;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/HashMap<",
            "Ljava/lang/Integer;",
            "Lcom/bytedance/sdk/component/WR/hjc/Ubf;",
            ">;"
        }
    .end annotation
.end field

.field private static hjc:Ljava/util/HashMap;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/HashMap<",
            "Ljava/lang/Integer;",
            "Lcom/bytedance/sdk/component/WR/hjc/Fj;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method private constructor <init>()V
    .locals 1

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    sput-object v0, Lcom/bytedance/sdk/component/WR/hjc/svN;->ex:Ljava/util/HashMap;

    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    sput-object v0, Lcom/bytedance/sdk/component/WR/hjc/svN;->hjc:Ljava/util/HashMap;

    return-void
.end method

.method public static declared-synchronized Fj()Lcom/bytedance/sdk/component/WR/hjc/svN;
    .locals 3

    const-class v0, Lcom/bytedance/sdk/component/WR/hjc/svN;

    monitor-enter v0

    :try_start_0
    sget-object v1, Lcom/bytedance/sdk/component/WR/hjc/svN;->Fj:Lcom/bytedance/sdk/component/WR/hjc/svN;

    if-nez v1, :cond_1

    const-class v1, Lcom/bytedance/sdk/component/WR/hjc/svN;

    monitor-enter v1
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_1

    :try_start_1
    sget-object v2, Lcom/bytedance/sdk/component/WR/hjc/svN;->Fj:Lcom/bytedance/sdk/component/WR/hjc/svN;

    if-nez v2, :cond_0

    new-instance v2, Lcom/bytedance/sdk/component/WR/hjc/svN;

    invoke-direct {v2}, Lcom/bytedance/sdk/component/WR/hjc/svN;-><init>()V

    sput-object v2, Lcom/bytedance/sdk/component/WR/hjc/svN;->Fj:Lcom/bytedance/sdk/component/WR/hjc/svN;

    goto :goto_0

    :catchall_0
    move-exception v2

    goto :goto_1

    :cond_0
    :goto_0
    monitor-exit v1
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    goto :goto_2

    :goto_1
    :try_start_2
    monitor-exit v1

    throw v2

    :catchall_1
    move-exception v1

    goto :goto_3

    :cond_1
    :goto_2
    sget-object v1, Lcom/bytedance/sdk/component/WR/hjc/svN;->Fj:Lcom/bytedance/sdk/component/WR/hjc/svN;
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_1

    monitor-exit v0

    return-object v1

    :goto_3
    monitor-exit v0

    throw v1
.end method


# virtual methods
.method public Fj(ILandroid/content/Context;)Lcom/bytedance/sdk/component/WR/hjc/Fj;
    .locals 2

    sget-object v0, Lcom/bytedance/sdk/component/WR/hjc/svN;->hjc:Ljava/util/HashMap;

    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/util/HashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/bytedance/sdk/component/WR/hjc/Fj;

    if-nez v0, :cond_0

    new-instance v0, Lcom/bytedance/sdk/component/WR/hjc/Fj;

    invoke-direct {v0, p2, p1}, Lcom/bytedance/sdk/component/WR/hjc/Fj;-><init>(Landroid/content/Context;I)V

    sget-object p2, Lcom/bytedance/sdk/component/WR/hjc/svN;->hjc:Ljava/util/HashMap;

    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p1

    invoke-virtual {p2, p1, v0}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    :cond_0
    return-object v0
.end method

.method public Fj(I)Lcom/bytedance/sdk/component/WR/hjc/Ubf;
    .locals 2

    sget-object v0, Lcom/bytedance/sdk/component/WR/hjc/svN;->ex:Ljava/util/HashMap;

    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/util/HashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;

    if-nez v0, :cond_0

    new-instance v0, Lcom/bytedance/sdk/component/WR/hjc/Ubf;

    invoke-direct {v0, p1}, Lcom/bytedance/sdk/component/WR/hjc/Ubf;-><init>(I)V

    sget-object v1, Lcom/bytedance/sdk/component/WR/hjc/svN;->ex:Ljava/util/HashMap;

    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p1

    invoke-virtual {v1, p1, v0}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    :cond_0
    return-object v0
.end method
