.class public Lcom/bytedance/sdk/component/adexpress/Fj/Fj/Fj;
.super Ljava/lang/Object;


# static fields
.field private static Ubf:Lcom/bytedance/sdk/component/adexpress/Fj/Fj/Fj;


# instance fields
.field private volatile Fj:Lcom/bytedance/sdk/component/adexpress/Fj/Fj/ex;

.field private volatile WR:Lcom/bytedance/sdk/component/adexpress/Fj/Fj/Ubf;

.field private volatile eV:Lcom/bytedance/sdk/component/eV/JW;

.field private volatile ex:Lcom/bytedance/sdk/component/adexpress/Fj/Fj/eV;

.field private volatile hjc:Lcom/bytedance/sdk/component/adexpress/Fj/Fj/hjc;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    new-instance v0, Lcom/bytedance/sdk/component/adexpress/Fj/Fj/Fj;

    invoke-direct {v0}, Lcom/bytedance/sdk/component/adexpress/Fj/Fj/Fj;-><init>()V

    sput-object v0, Lcom/bytedance/sdk/component/adexpress/Fj/Fj/Fj;->Ubf:Lcom/bytedance/sdk/component/adexpress/Fj/Fj/Fj;

    return-void
.end method

.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static Fj()Lcom/bytedance/sdk/component/adexpress/Fj/Fj/Fj;
    .locals 1

    sget-object v0, Lcom/bytedance/sdk/component/adexpress/Fj/Fj/Fj;->Ubf:Lcom/bytedance/sdk/component/adexpress/Fj/Fj/Fj;

    return-object v0
.end method


# virtual methods
.method public Fj(Lcom/bytedance/sdk/component/adexpress/Fj/Fj/eV;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/adexpress/Fj/Fj/Fj;->ex:Lcom/bytedance/sdk/component/adexpress/Fj/Fj/eV;

    return-void
.end method

.method public Fj(Lcom/bytedance/sdk/component/adexpress/Fj/Fj/ex;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/adexpress/Fj/Fj/Fj;->Fj:Lcom/bytedance/sdk/component/adexpress/Fj/Fj/ex;

    return-void
.end method

.method public Fj(Lcom/bytedance/sdk/component/adexpress/Fj/Fj/hjc;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/adexpress/Fj/Fj/Fj;->hjc:Lcom/bytedance/sdk/component/adexpress/Fj/Fj/hjc;

    return-void
.end method

.method public Fj(Lcom/bytedance/sdk/component/eV/JW;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/adexpress/Fj/Fj/Fj;->eV:Lcom/bytedance/sdk/component/eV/JW;

    return-void
.end method

.method public Ubf()Lcom/bytedance/sdk/component/eV/JW;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/Fj/Fj/Fj;->eV:Lcom/bytedance/sdk/component/eV/JW;

    return-object v0
.end method

.method public WR()Lcom/bytedance/sdk/component/adexpress/Fj/Fj/Ubf;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/Fj/Fj/Fj;->WR:Lcom/bytedance/sdk/component/adexpress/Fj/Fj/Ubf;

    return-object v0
.end method

.method public eV()Lcom/bytedance/sdk/component/adexpress/Fj/Fj/eV;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/Fj/Fj/Fj;->ex:Lcom/bytedance/sdk/component/adexpress/Fj/Fj/eV;

    return-object v0
.end method

.method public ex()Lcom/bytedance/sdk/component/adexpress/Fj/Fj/ex;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/Fj/Fj/Fj;->Fj:Lcom/bytedance/sdk/component/adexpress/Fj/Fj/ex;

    return-object v0
.end method

.method public hjc()Lcom/bytedance/sdk/component/adexpress/Fj/Fj/hjc;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/Fj/Fj/Fj;->hjc:Lcom/bytedance/sdk/component/adexpress/Fj/Fj/hjc;

    return-object v0
.end method
