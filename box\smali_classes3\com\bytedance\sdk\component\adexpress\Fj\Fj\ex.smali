.class public interface abstract Lcom/bytedance/sdk/component/adexpress/Fj/Fj/ex;
.super Ljava/lang/Object;


# virtual methods
.method public abstract Fj(Ljava/lang/String;Landroid/content/ContentValues;Ljava/lang/String;[Ljava/lang/String;)I
.end method

.method public abstract Fj(Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;)I
.end method

.method public abstract Fj(Ljava/lang/String;[Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Landroid/database/Cursor;
.end method

.method public abstract Fj(Ljava/lang/String;Landroid/content/ContentValues;)V
.end method
