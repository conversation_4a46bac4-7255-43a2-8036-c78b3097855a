.class public interface abstract Lcom/bytedance/sdk/component/adexpress/Fj/Fj/hjc;
.super Ljava/lang/Object;


# virtual methods
.method public abstract BcC()Ljava/lang/String;
.end method

.method public abstract Fj()I
.end method

.method public abstract Ko()I
.end method

.method public abstract UYd()I
.end method

.method public abstract Ubf()Lcom/bytedance/sdk/component/adexpress/Fj/hjc/Fj;
.end method

.method public abstract WR()Lcom/bytedance/sdk/component/WR/ex/Fj;
.end method

.method public abstract eV()V
.end method

.method public abstract ex()Landroid/content/Context;
.end method

.method public abstract hjc()Landroid/os/Handler;
.end method

.method public abstract mSE()I
.end method

.method public abstract rAx()I
.end method

.method public abstract svN()Lcom/bytedance/sdk/component/WR/ex/ex;
.end method
