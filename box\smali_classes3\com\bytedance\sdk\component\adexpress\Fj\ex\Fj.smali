.class public Lcom/bytedance/sdk/component/adexpress/Fj/ex/Fj;
.super Ljava/lang/Object;


# instance fields
.field private Fj:Landroid/webkit/WebResourceResponse;

.field private ex:I


# direct methods
.method public constructor <init>()V
    .locals 1

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, -0x1

    iput v0, p0, Lcom/bytedance/sdk/component/adexpress/Fj/ex/Fj;->ex:I

    return-void
.end method


# virtual methods
.method public Fj()Landroid/webkit/WebResourceResponse;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/Fj/ex/Fj;->Fj:Landroid/webkit/WebResourceResponse;

    return-object v0
.end method

.method public Fj(I)V
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/component/adexpress/Fj/ex/Fj;->ex:I

    return-void
.end method

.method public Fj(Landroid/webkit/WebResourceResponse;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/adexpress/Fj/ex/Fj;->Fj:Landroid/webkit/WebResourceResponse;

    return-void
.end method

.method public ex()I
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/component/adexpress/Fj/ex/Fj;->ex:I

    return v0
.end method
