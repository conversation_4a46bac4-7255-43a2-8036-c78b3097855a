.class Lcom/bytedance/sdk/component/adexpress/Fj/ex/Ubf$1;
.super Lcom/bytedance/sdk/component/svN/BcC;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/component/adexpress/Fj/ex/Ubf;->Ko()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/component/adexpress/Fj/ex/Ubf;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/component/adexpress/Fj/ex/Ubf;Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/adexpress/Fj/ex/Ubf$1;->Fj:Lcom/bytedance/sdk/component/adexpress/Fj/ex/Ubf;

    invoke-direct {p0, p2}, Lcom/bytedance/sdk/component/svN/BcC;-><init>(Ljava/lang/String;)V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 2

    invoke-static {}, Lcom/bytedance/sdk/component/adexpress/Fj/ex/BcC;->Fj()V

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/Fj/ex/Ubf$1;->Fj:Lcom/bytedance/sdk/component/adexpress/Fj/ex/Ubf;

    invoke-static {v0}, Lcom/bytedance/sdk/component/adexpress/Fj/ex/Ubf;->Fj(Lcom/bytedance/sdk/component/adexpress/Fj/ex/Ubf;)Ljava/util/concurrent/atomic/AtomicBoolean;

    move-result-object v0

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/util/concurrent/atomic/AtomicBoolean;->set(Z)V

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/Fj/ex/Ubf$1;->Fj:Lcom/bytedance/sdk/component/adexpress/Fj/ex/Ubf;

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/adexpress/Fj/ex/Ubf;->eV()V

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/Fj/ex/Ubf$1;->Fj:Lcom/bytedance/sdk/component/adexpress/Fj/ex/Ubf;

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/adexpress/Fj/ex/Ubf;->svN()V

    invoke-static {}, Lcom/bytedance/sdk/component/adexpress/Fj/Fj/Fj;->Fj()Lcom/bytedance/sdk/component/adexpress/Fj/Fj/Fj;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/adexpress/Fj/Fj/Fj;->hjc()Lcom/bytedance/sdk/component/adexpress/Fj/Fj/hjc;

    move-result-object v0

    if-eqz v0, :cond_0

    invoke-static {}, Lcom/bytedance/sdk/component/adexpress/Fj/Fj/Fj;->Fj()Lcom/bytedance/sdk/component/adexpress/Fj/Fj/Fj;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/adexpress/Fj/Fj/Fj;->hjc()Lcom/bytedance/sdk/component/adexpress/Fj/Fj/hjc;

    move-result-object v0

    invoke-interface {v0}, Lcom/bytedance/sdk/component/adexpress/Fj/Fj/hjc;->ex()Landroid/content/Context;

    move-result-object v0

    invoke-static {v0}, Lcom/bytedance/sdk/component/utils/rS;->Fj(Landroid/content/Context;)Z

    move-result v0

    if-eqz v0, :cond_0

    invoke-static {}, Lcom/bytedance/sdk/component/adexpress/Fj/Fj/Fj;->Fj()Lcom/bytedance/sdk/component/adexpress/Fj/Fj/Fj;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/adexpress/Fj/Fj/Fj;->hjc()Lcom/bytedance/sdk/component/adexpress/Fj/Fj/hjc;

    move-result-object v0

    invoke-interface {v0}, Lcom/bytedance/sdk/component/adexpress/Fj/Fj/hjc;->hjc()Landroid/os/Handler;

    move-result-object v0

    new-instance v1, Lcom/bytedance/sdk/component/adexpress/Fj/ex/Ubf$1$1;

    invoke-direct {v1, p0}, Lcom/bytedance/sdk/component/adexpress/Fj/ex/Ubf$1$1;-><init>(Lcom/bytedance/sdk/component/adexpress/Fj/ex/Ubf$1;)V

    invoke-virtual {v0, v1}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    :cond_0
    return-void
.end method
