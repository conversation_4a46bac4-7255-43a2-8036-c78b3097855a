.class Lcom/bytedance/sdk/component/adexpress/Fj/ex/Ubf$2;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/component/adexpress/Fj/ex/Ubf;->Fj(Z)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/component/adexpress/Fj/ex/Ubf;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/component/adexpress/Fj/ex/Ubf;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/adexpress/Fj/ex/Ubf$2;->Fj:Lcom/bytedance/sdk/component/adexpress/Fj/ex/Ubf;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 1

    invoke-static {}, Lcom/bytedance/sdk/component/adexpress/Ubf/Ubf;->Fj()Lcom/bytedance/sdk/component/adexpress/Ubf/Ubf;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/adexpress/Ubf/Ubf;->ex()V

    return-void
.end method
