.class public Lcom/bytedance/sdk/component/adexpress/Fj/ex/eV;
.super Ljava/lang/Object;


# direct methods
.method public static Fj(FFFF)Landroid/view/animation/Interpolator;
    .locals 0

    new-instance p0, Landroid/view/animation/AccelerateDecelerateInterpolator;

    invoke-direct {p0}, Landroid/view/animation/AccelerateDecelerateInterpolator;-><init>()V

    return-object p0
.end method

.method public static Fj()Ljava/io/File;
    .locals 1

    invoke-static {}, Lcom/bytedance/sdk/component/adexpress/Fj/Fj/Fj;->Fj()Lcom/bytedance/sdk/component/adexpress/Fj/Fj/Fj;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/adexpress/Fj/Fj/Fj;->hjc()Lcom/bytedance/sdk/component/adexpress/Fj/Fj/hjc;

    move-result-object v0

    invoke-interface {v0}, Lcom/bytedance/sdk/component/adexpress/Fj/Fj/hjc;->ex()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {v0}, Landroid/content/Context;->getCacheDir()Ljava/io/File;

    move-result-object v0

    return-object v0
.end method
