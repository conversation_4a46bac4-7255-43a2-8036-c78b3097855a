.class Lcom/bytedance/sdk/component/adexpress/Fj/ex/svN$1;
.super Lcom/bytedance/sdk/component/svN/BcC;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/component/adexpress/Fj/ex/svN;->Fj(Lcom/bytedance/sdk/component/adexpress/Fj/hjc/eV;Ljava/lang/String;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Ljava/lang/String;

.field final synthetic Ubf:Ljava/lang/String;

.field final synthetic WR:Ljava/lang/String;

.field final synthetic eV:Ljava/lang/String;

.field final synthetic ex:Ljava/lang/String;

.field final synthetic hjc:Ljava/lang/String;

.field final synthetic svN:Lcom/bytedance/sdk/component/adexpress/Fj/ex/svN;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/component/adexpress/Fj/ex/svN;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/adexpress/Fj/ex/svN$1;->svN:Lcom/bytedance/sdk/component/adexpress/Fj/ex/svN;

    iput-object p3, p0, Lcom/bytedance/sdk/component/adexpress/Fj/ex/svN$1;->Fj:Ljava/lang/String;

    iput-object p4, p0, Lcom/bytedance/sdk/component/adexpress/Fj/ex/svN$1;->ex:Ljava/lang/String;

    iput-object p5, p0, Lcom/bytedance/sdk/component/adexpress/Fj/ex/svN$1;->hjc:Ljava/lang/String;

    iput-object p6, p0, Lcom/bytedance/sdk/component/adexpress/Fj/ex/svN$1;->eV:Ljava/lang/String;

    iput-object p7, p0, Lcom/bytedance/sdk/component/adexpress/Fj/ex/svN$1;->Ubf:Ljava/lang/String;

    iput-object p8, p0, Lcom/bytedance/sdk/component/adexpress/Fj/ex/svN$1;->WR:Ljava/lang/String;

    invoke-direct {p0, p2}, Lcom/bytedance/sdk/component/svN/BcC;-><init>(Ljava/lang/String;)V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 7

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/Fj/ex/svN$1;->svN:Lcom/bytedance/sdk/component/adexpress/Fj/ex/svN;

    iget-object v1, p0, Lcom/bytedance/sdk/component/adexpress/Fj/ex/svN$1;->Fj:Ljava/lang/String;

    iget-object v2, p0, Lcom/bytedance/sdk/component/adexpress/Fj/ex/svN$1;->ex:Ljava/lang/String;

    iget-object v3, p0, Lcom/bytedance/sdk/component/adexpress/Fj/ex/svN$1;->hjc:Ljava/lang/String;

    iget-object v4, p0, Lcom/bytedance/sdk/component/adexpress/Fj/ex/svN$1;->eV:Ljava/lang/String;

    iget-object v5, p0, Lcom/bytedance/sdk/component/adexpress/Fj/ex/svN$1;->Ubf:Ljava/lang/String;

    iget-object v6, p0, Lcom/bytedance/sdk/component/adexpress/Fj/ex/svN$1;->WR:Ljava/lang/String;

    invoke-static/range {v0 .. v6}, Lcom/bytedance/sdk/component/adexpress/Fj/ex/svN;->Fj(Lcom/bytedance/sdk/component/adexpress/Fj/ex/svN;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    return-void
.end method
