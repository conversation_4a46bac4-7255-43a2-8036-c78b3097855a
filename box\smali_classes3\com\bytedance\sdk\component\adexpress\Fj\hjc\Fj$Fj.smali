.class public Lcom/bytedance/sdk/component/adexpress/Fj/hjc/Fj$Fj;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bytedance/sdk/component/adexpress/Fj/hjc/Fj;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "Fj"
.end annotation


# instance fields
.field private Fj:Ljava/lang/String;

.field private ex:Ljava/lang/String;

.field private hjc:I


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public Fj()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/Fj/hjc/Fj$Fj;->Fj:Ljava/lang/String;

    return-object v0
.end method

.method public Fj(I)V
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/component/adexpress/Fj/hjc/Fj$Fj;->hjc:I

    return-void
.end method

.method public Fj(Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/adexpress/Fj/hjc/Fj$Fj;->Fj:Ljava/lang/String;

    return-void
.end method

.method public equals(Ljava/lang/Object;)Z
    .locals 3

    instance-of v0, p1, Lcom/bytedance/sdk/component/adexpress/Fj/hjc/Fj$Fj;

    if-eqz v0, :cond_1

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/Fj/hjc/Fj$Fj;->Fj:Ljava/lang/String;

    const/4 v1, 0x0

    if-eqz v0, :cond_0

    check-cast p1, Lcom/bytedance/sdk/component/adexpress/Fj/hjc/Fj$Fj;

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/adexpress/Fj/hjc/Fj$Fj;->Fj()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v0, v2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/Fj/hjc/Fj$Fj;->ex:Ljava/lang/String;

    if-eqz v0, :cond_0

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/adexpress/Fj/hjc/Fj$Fj;->ex()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v0, p1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_0

    const/4 p1, 0x1

    return p1

    :cond_0
    return v1

    :cond_1
    invoke-super {p0, p1}, Ljava/lang/Object;->equals(Ljava/lang/Object;)Z

    move-result p1

    return p1
.end method

.method public ex()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/Fj/hjc/Fj$Fj;->ex:Ljava/lang/String;

    return-object v0
.end method

.method public ex(Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/adexpress/Fj/hjc/Fj$Fj;->ex:Ljava/lang/String;

    return-void
.end method

.method public hjc()I
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/component/adexpress/Fj/hjc/Fj$Fj;->hjc:I

    return v0
.end method
