.class public Lcom/bytedance/sdk/component/adexpress/Fj/hjc/eV;
.super Ljava/lang/Object;


# instance fields
.field public Fj:Ljava/lang/String;

.field public Ubf:Ljava/lang/String;

.field public WR:Ljava/lang/String;

.field public eV:Ljava/lang/String;

.field public ex:Ljava/lang/String;

.field public hjc:Ljava/lang/String;


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static Fj()Lcom/bytedance/sdk/component/adexpress/Fj/hjc/eV;
    .locals 1

    new-instance v0, Lcom/bytedance/sdk/component/adexpress/Fj/hjc/eV;

    invoke-direct {v0}, Lcom/bytedance/sdk/component/adexpress/Fj/hjc/eV;-><init>()V

    return-object v0
.end method


# virtual methods
.method public Fj(Ljava/lang/String;)Lcom/bytedance/sdk/component/adexpress/Fj/hjc/eV;
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/adexpress/Fj/hjc/eV;->Fj:Ljava/lang/String;

    return-object p0
.end method

.method public Ubf(Ljava/lang/String;)Lcom/bytedance/sdk/component/adexpress/Fj/hjc/eV;
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/adexpress/Fj/hjc/eV;->Ubf:Ljava/lang/String;

    return-object p0
.end method

.method public WR(Ljava/lang/String;)Lcom/bytedance/sdk/component/adexpress/Fj/hjc/eV;
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/adexpress/Fj/hjc/eV;->WR:Ljava/lang/String;

    return-object p0
.end method

.method public eV(Ljava/lang/String;)Lcom/bytedance/sdk/component/adexpress/Fj/hjc/eV;
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/adexpress/Fj/hjc/eV;->eV:Ljava/lang/String;

    return-object p0
.end method

.method public ex(Ljava/lang/String;)Lcom/bytedance/sdk/component/adexpress/Fj/hjc/eV;
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/adexpress/Fj/hjc/eV;->ex:Ljava/lang/String;

    return-object p0
.end method

.method public hjc(Ljava/lang/String;)Lcom/bytedance/sdk/component/adexpress/Fj/hjc/eV;
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/adexpress/Fj/hjc/eV;->hjc:Ljava/lang/String;

    return-object p0
.end method
