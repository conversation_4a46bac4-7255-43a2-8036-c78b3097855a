.class public Lcom/bytedance/sdk/component/adexpress/Fj/hjc/ex;
.super Ljava/lang/Object;


# instance fields
.field private Fj:Ljava/lang/String;

.field private Ubf:Ljava/lang/String;

.field private WR:Ljava/lang/String;

.field private eV:Ljava/lang/String;

.field private ex:Ljava/lang/String;

.field private hjc:Ljava/lang/String;

.field private svN:Ljava/lang/Long;


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public Fj(Ljava/lang/Long;)Lcom/bytedance/sdk/component/adexpress/Fj/hjc/ex;
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/adexpress/Fj/hjc/ex;->svN:Ljava/lang/Long;

    return-object p0
.end method

.method public Fj(Ljava/lang/String;)Lcom/bytedance/sdk/component/adexpress/Fj/hjc/ex;
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/adexpress/Fj/hjc/ex;->Fj:Ljava/lang/String;

    return-object p0
.end method

.method public Fj()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/Fj/hjc/ex;->Fj:Ljava/lang/String;

    return-object v0
.end method

.method public Ubf(Ljava/lang/String;)Lcom/bytedance/sdk/component/adexpress/Fj/hjc/ex;
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/adexpress/Fj/hjc/ex;->Ubf:Ljava/lang/String;

    return-object p0
.end method

.method public Ubf()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/Fj/hjc/ex;->Ubf:Ljava/lang/String;

    return-object v0
.end method

.method public WR(Ljava/lang/String;)Lcom/bytedance/sdk/component/adexpress/Fj/hjc/ex;
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/adexpress/Fj/hjc/ex;->WR:Ljava/lang/String;

    return-object p0
.end method

.method public WR()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/Fj/hjc/ex;->WR:Ljava/lang/String;

    return-object v0
.end method

.method public eV(Ljava/lang/String;)Lcom/bytedance/sdk/component/adexpress/Fj/hjc/ex;
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/adexpress/Fj/hjc/ex;->eV:Ljava/lang/String;

    return-object p0
.end method

.method public eV()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/Fj/hjc/ex;->eV:Ljava/lang/String;

    return-object v0
.end method

.method public ex(Ljava/lang/String;)Lcom/bytedance/sdk/component/adexpress/Fj/hjc/ex;
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/adexpress/Fj/hjc/ex;->ex:Ljava/lang/String;

    return-object p0
.end method

.method public ex()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/Fj/hjc/ex;->ex:Ljava/lang/String;

    return-object v0
.end method

.method public hjc(Ljava/lang/String;)Lcom/bytedance/sdk/component/adexpress/Fj/hjc/ex;
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/adexpress/Fj/hjc/ex;->hjc:Ljava/lang/String;

    return-object p0
.end method

.method public hjc()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/Fj/hjc/ex;->hjc:Ljava/lang/String;

    return-object v0
.end method

.method public svN()Ljava/lang/Long;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/Fj/hjc/ex;->svN:Ljava/lang/Long;

    return-object v0
.end method
