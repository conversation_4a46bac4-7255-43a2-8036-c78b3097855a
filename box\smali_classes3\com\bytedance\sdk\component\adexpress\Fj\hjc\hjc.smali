.class public Lcom/bytedance/sdk/component/adexpress/Fj/hjc/hjc;
.super Ljava/lang/Object;


# instance fields
.field private Fj:Ljava/lang/String;

.field private ex:Ljava/lang/String;

.field private hjc:Ljava/lang/String;


# direct methods
.method public constructor <init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/bytedance/sdk/component/adexpress/Fj/hjc/hjc;->Fj:Ljava/lang/String;

    iput-object p2, p0, Lcom/bytedance/sdk/component/adexpress/Fj/hjc/hjc;->ex:Ljava/lang/String;

    iput-object p3, p0, Lcom/bytedance/sdk/component/adexpress/Fj/hjc/hjc;->hjc:Ljava/lang/String;

    return-void
.end method


# virtual methods
.method public Fj()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/Fj/hjc/hjc;->Fj:Ljava/lang/String;

    return-object v0
.end method

.method public ex()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/Fj/hjc/hjc;->ex:Ljava/lang/String;

    return-object v0
.end method
