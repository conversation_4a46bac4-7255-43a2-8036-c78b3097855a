.class Lcom/bytedance/sdk/component/adexpress/Ubf/Fj$1;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->Fj(Lcom/bytedance/sdk/component/adexpress/ex/Tc;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/component/adexpress/ex/Tc;

.field final synthetic eV:Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;

.field final synthetic ex:F

.field final synthetic hjc:F


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;Lcom/bytedance/sdk/component/adexpress/ex/Tc;FF)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj$1;->eV:Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;

    iput-object p2, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj$1;->Fj:Lcom/bytedance/sdk/component/adexpress/ex/Tc;

    iput p3, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj$1;->ex:F

    iput p4, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj$1;->hjc:F

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 4

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj$1;->eV:Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;

    iget-object v1, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj$1;->Fj:Lcom/bytedance/sdk/component/adexpress/ex/Tc;

    iget v2, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj$1;->ex:F

    iget v3, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj$1;->hjc:F

    invoke-static {v0, v1, v2, v3}, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->Fj(Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;Lcom/bytedance/sdk/component/adexpress/ex/Tc;FF)V

    return-void
.end method
