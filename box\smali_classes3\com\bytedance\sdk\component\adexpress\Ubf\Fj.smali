.class public abstract Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/sdk/component/adexpress/Fj;
.implements Lcom/bytedance/sdk/component/adexpress/ex/eV;
.implements Lcom/bytedance/sdk/component/adexpress/ex/rAx;
.implements Lcom/bytedance/sdk/component/adexpress/theme/Fj;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Lcom/bytedance/sdk/component/adexpress/Fj;",
        "Lcom/bytedance/sdk/component/adexpress/ex/eV<",
        "Lcom/bytedance/sdk/component/widget/SSWebView;",
        ">;",
        "Lcom/bytedance/sdk/component/adexpress/ex/rAx;",
        "Lcom/bytedance/sdk/component/adexpress/theme/Fj;"
    }
.end annotation


# instance fields
.field private BcC:Ljava/lang/String;

.field protected Fj:Lorg/json/JSONObject;

.field private JU:Z

.field private JW:I

.field private volatile Ko:Lcom/bytedance/sdk/component/adexpress/ex/svN;

.field private Tc:Z

.field private UYd:Lcom/bytedance/sdk/component/adexpress/ex/BcC;

.field protected Ubf:Lcom/bytedance/sdk/component/adexpress/Fj/hjc/ex;

.field protected WR:Ljava/util/concurrent/atomic/AtomicBoolean;

.field private dG:Lcom/bytedance/sdk/component/adexpress/ex/dG;

.field protected eV:I

.field protected ex:Z

.field protected hjc:Lcom/bytedance/sdk/component/widget/SSWebView;

.field private mSE:Ljava/lang/String;

.field private rAx:Z

.field private svN:Landroid/content/Context;


# direct methods
.method public constructor <init>(Landroid/content/Context;Lcom/bytedance/sdk/component/adexpress/ex/dG;Lcom/bytedance/sdk/component/adexpress/theme/ThemeStatusBroadcastReceiver;)V
    .locals 2

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, 0x0

    iput-boolean v0, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->rAx:Z

    const/16 v1, 0x8

    iput v1, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->eV:I

    new-instance v1, Ljava/util/concurrent/atomic/AtomicBoolean;

    invoke-direct {v1, v0}, Ljava/util/concurrent/atomic/AtomicBoolean;-><init>(Z)V

    iput-object v1, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->WR:Ljava/util/concurrent/atomic/AtomicBoolean;

    iput-boolean v0, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->JU:Z

    iput-object p1, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->svN:Landroid/content/Context;

    iput-object p2, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->dG:Lcom/bytedance/sdk/component/adexpress/ex/dG;

    invoke-virtual {p2}, Lcom/bytedance/sdk/component/adexpress/ex/dG;->eV()Ljava/lang/String;

    move-result-object p1

    iput-object p1, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->BcC:Ljava/lang/String;

    invoke-virtual {p3, p0}, Lcom/bytedance/sdk/component/adexpress/theme/ThemeStatusBroadcastReceiver;->Fj(Lcom/bytedance/sdk/component/adexpress/theme/Fj;)V

    invoke-static {}, Lcom/bytedance/sdk/component/adexpress/eV;->ex()Z

    move-result p1

    if-eqz p1, :cond_0

    invoke-direct {p0}, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->UYd()V

    return-void

    :cond_0
    invoke-direct {p0}, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->dG()Lcom/bytedance/sdk/component/widget/SSWebView;

    move-result-object p1

    iput-object p1, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->hjc:Lcom/bytedance/sdk/component/widget/SSWebView;

    if-nez p1, :cond_1

    invoke-static {}, Lcom/bytedance/sdk/component/adexpress/eV;->Fj()Landroid/content/Context;

    move-result-object p1

    if-eqz p1, :cond_2

    new-instance p1, Lcom/bytedance/sdk/component/widget/SSWebView;

    invoke-static {}, Lcom/bytedance/sdk/component/adexpress/eV;->Fj()Landroid/content/Context;

    move-result-object p2

    invoke-direct {p1, p2}, Lcom/bytedance/sdk/component/widget/SSWebView;-><init>(Landroid/content/Context;)V

    iput-object p1, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->hjc:Lcom/bytedance/sdk/component/widget/SSWebView;

    return-void

    :cond_1
    const/4 p1, 0x1

    iput-boolean p1, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->rAx:Z

    :cond_2
    return-void
.end method

.method private Fj(FF)V
    .locals 1
    .annotation build Lcom/bytedance/component/sdk/annotation/UiThread;
    .end annotation

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->dG:Lcom/bytedance/sdk/component/adexpress/ex/dG;

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/adexpress/ex/dG;->Ubf()Lcom/bytedance/sdk/component/adexpress/ex/mSE;

    move-result-object v0

    invoke-interface {v0}, Lcom/bytedance/sdk/component/adexpress/ex/mSE;->Ubf()V

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->svN:Landroid/content/Context;

    invoke-static {v0, p1}, Lcom/bytedance/sdk/component/adexpress/eV/WR;->Fj(Landroid/content/Context;F)F

    move-result p1

    float-to-int p1, p1

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->svN:Landroid/content/Context;

    invoke-static {v0, p2}, Lcom/bytedance/sdk/component/adexpress/eV/WR;->Fj(Landroid/content/Context;F)F

    move-result p2

    float-to-int p2, p2

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->Fj()Lcom/bytedance/sdk/component/widget/SSWebView;

    move-result-object v0

    invoke-virtual {v0}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    move-result-object v0

    check-cast v0, Landroid/widget/FrameLayout$LayoutParams;

    if-nez v0, :cond_0

    new-instance v0, Landroid/widget/FrameLayout$LayoutParams;

    invoke-direct {v0, p1, p2}, Landroid/widget/FrameLayout$LayoutParams;-><init>(II)V

    :cond_0
    iput p1, v0, Landroid/widget/FrameLayout$LayoutParams;->width:I

    iput p2, v0, Landroid/widget/FrameLayout$LayoutParams;->height:I

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->Fj()Lcom/bytedance/sdk/component/widget/SSWebView;

    move-result-object p1

    invoke-virtual {p1, v0}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    return-void
.end method

.method private Fj(ILjava/lang/String;)V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->Ko:Lcom/bytedance/sdk/component/adexpress/ex/svN;

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->Ko:Lcom/bytedance/sdk/component/adexpress/ex/svN;

    invoke-interface {v0, p1, p2}, Lcom/bytedance/sdk/component/adexpress/ex/svN;->Fj(ILjava/lang/String;)V

    :cond_0
    return-void
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;Lcom/bytedance/sdk/component/adexpress/ex/Tc;FF)V
    .locals 0

    invoke-direct {p0, p1, p2, p3}, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->Fj(Lcom/bytedance/sdk/component/adexpress/ex/Tc;FF)V

    return-void
.end method

.method private Fj(Lcom/bytedance/sdk/component/adexpress/ex/Tc;FF)V
    .locals 2

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/adexpress/ex/Tc;->rAx()I

    iget-boolean v0, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->ex:Z

    if-eqz v0, :cond_0

    iget-boolean v1, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->Tc:Z

    if-nez v1, :cond_0

    invoke-direct {p0, p2, p3}, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->Fj(FF)V

    iget p2, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->eV:I

    invoke-virtual {p0, p2}, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->ex(I)V

    iget-object p2, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->Ko:Lcom/bytedance/sdk/component/adexpress/ex/svN;

    if-eqz p2, :cond_2

    iget-object p2, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->Ko:Lcom/bytedance/sdk/component/adexpress/ex/svN;

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->Fj()Lcom/bytedance/sdk/component/widget/SSWebView;

    move-result-object p3

    invoke-interface {p2, p3, p1}, Lcom/bytedance/sdk/component/adexpress/ex/svN;->Fj(Landroid/view/View;Lcom/bytedance/sdk/component/adexpress/ex/Tc;)V

    return-void

    :cond_0
    if-nez v0, :cond_1

    invoke-static {}, Lcom/bytedance/sdk/component/adexpress/Ubf/Ubf;->Fj()Lcom/bytedance/sdk/component/adexpress/Ubf/Ubf;

    move-result-object p1

    iget-object p2, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->hjc:Lcom/bytedance/sdk/component/widget/SSWebView;

    invoke-virtual {p1, p2}, Lcom/bytedance/sdk/component/adexpress/Ubf/Ubf;->Ubf(Lcom/bytedance/sdk/component/widget/SSWebView;)Z

    return-void

    :cond_1
    invoke-virtual {p1}, Lcom/bytedance/sdk/component/adexpress/ex/Tc;->rAx()I

    move-result p2

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/adexpress/ex/Tc;->Ko()Ljava/lang/String;

    move-result-object p1

    invoke-direct {p0, p2, p1}, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->Fj(ILjava/lang/String;)V

    :cond_2
    return-void
.end method

.method private Tc()V
    .locals 2

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->dG:Lcom/bytedance/sdk/component/adexpress/ex/dG;

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/adexpress/ex/dG;->Af()Z

    move-result v0

    if-eqz v0, :cond_0

    invoke-static {}, Lcom/bytedance/sdk/component/adexpress/Ubf/Ubf;->Fj()Lcom/bytedance/sdk/component/adexpress/Ubf/Ubf;

    move-result-object v0

    iget-object v1, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->hjc:Lcom/bytedance/sdk/component/widget/SSWebView;

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/component/adexpress/Ubf/Ubf;->ex(Lcom/bytedance/sdk/component/widget/SSWebView;)V

    return-void

    :cond_0
    invoke-static {}, Lcom/bytedance/sdk/component/adexpress/Ubf/Ubf;->Fj()Lcom/bytedance/sdk/component/adexpress/Ubf/Ubf;

    move-result-object v0

    iget-object v1, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->hjc:Lcom/bytedance/sdk/component/widget/SSWebView;

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/component/adexpress/Ubf/Ubf;->hjc(Lcom/bytedance/sdk/component/widget/SSWebView;)V

    return-void
.end method

.method private UYd()V
    .locals 3

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->svN:Landroid/content/Context;

    if-nez v0, :cond_0

    invoke-static {}, Lcom/bytedance/sdk/component/adexpress/eV;->Fj()Landroid/content/Context;

    move-result-object v0

    if-eqz v0, :cond_0

    invoke-static {}, Lcom/bytedance/sdk/component/adexpress/eV;->Fj()Landroid/content/Context;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->svN:Landroid/content/Context;

    :cond_0
    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->svN:Landroid/content/Context;

    if-eqz v0, :cond_2

    invoke-direct {p0}, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->dG()Lcom/bytedance/sdk/component/widget/SSWebView;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->hjc:Lcom/bytedance/sdk/component/widget/SSWebView;

    if-nez v0, :cond_1

    new-instance v0, Lcom/bytedance/sdk/component/widget/SSWebView;

    new-instance v1, Landroid/content/MutableContextWrapper;

    iget-object v2, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->svN:Landroid/content/Context;

    invoke-virtual {v2}, Landroid/content/Context;->getApplicationContext()Landroid/content/Context;

    move-result-object v2

    invoke-direct {v1, v2}, Landroid/content/MutableContextWrapper;-><init>(Landroid/content/Context;)V

    invoke-direct {v0, v1}, Lcom/bytedance/sdk/component/widget/SSWebView;-><init>(Landroid/content/Context;)V

    iput-object v0, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->hjc:Lcom/bytedance/sdk/component/widget/SSWebView;

    return-void

    :cond_1
    const/4 v0, 0x1

    iput-boolean v0, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->rAx:Z

    :cond_2
    return-void
.end method

.method private dG()Lcom/bytedance/sdk/component/widget/SSWebView;
    .locals 3

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->dG:Lcom/bytedance/sdk/component/adexpress/ex/dG;

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/adexpress/ex/dG;->Af()Z

    move-result v0

    if-eqz v0, :cond_0

    invoke-static {}, Lcom/bytedance/sdk/component/adexpress/Ubf/Ubf;->Fj()Lcom/bytedance/sdk/component/adexpress/Ubf/Ubf;

    move-result-object v0

    iget-object v1, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->svN:Landroid/content/Context;

    iget-object v2, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->BcC:Ljava/lang/String;

    invoke-virtual {v0, v1, v2}, Lcom/bytedance/sdk/component/adexpress/Ubf/Ubf;->Fj(Landroid/content/Context;Ljava/lang/String;)Lcom/bytedance/sdk/component/widget/SSWebView;

    move-result-object v0

    return-object v0

    :cond_0
    invoke-static {}, Lcom/bytedance/sdk/component/adexpress/Ubf/Ubf;->Fj()Lcom/bytedance/sdk/component/adexpress/Ubf/Ubf;

    move-result-object v0

    iget-object v1, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->svN:Landroid/content/Context;

    iget-object v2, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->BcC:Ljava/lang/String;

    invoke-virtual {v0, v1, v2}, Lcom/bytedance/sdk/component/adexpress/Ubf/Ubf;->ex(Landroid/content/Context;Ljava/lang/String;)Lcom/bytedance/sdk/component/widget/SSWebView;

    move-result-object v0

    return-object v0
.end method

.method private ex(Landroid/app/Activity;)I
    .locals 0

    invoke-virtual {p1}, Ljava/lang/Object;->hashCode()I

    move-result p1

    return p1
.end method


# virtual methods
.method public BcC()V
    .locals 1

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->mSE()V

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->hjc:Lcom/bytedance/sdk/component/widget/SSWebView;

    invoke-static {v0}, Lcom/bytedance/sdk/component/utils/ex;->Fj(Landroid/view/View;)Landroid/app/Activity;

    move-result-object v0

    if-eqz v0, :cond_0

    invoke-direct {p0, v0}, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->ex(Landroid/app/Activity;)I

    move-result v0

    iput v0, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->JW:I

    :cond_0
    return-void
.end method

.method public Fj()Lcom/bytedance/sdk/component/widget/SSWebView;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->hjc:Lcom/bytedance/sdk/component/widget/SSWebView;

    return-object v0
.end method

.method public Fj(Landroid/app/Activity;)V
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->JW:I

    if-eqz v0, :cond_1

    if-nez p1, :cond_0

    goto :goto_0

    :cond_0
    invoke-virtual {p1}, Ljava/lang/Object;->hashCode()I

    move-result p1

    iget v0, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->JW:I

    if-ne p1, v0, :cond_1

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->eV()V

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->Ko()V

    :cond_1
    :goto_0
    return-void
.end method

.method public Fj(Landroid/view/View;ILcom/bytedance/sdk/component/adexpress/hjc;)V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->UYd:Lcom/bytedance/sdk/component/adexpress/ex/BcC;

    if-eqz v0, :cond_0

    invoke-interface {v0, p1, p2, p3}, Lcom/bytedance/sdk/component/adexpress/ex/BcC;->Fj(Landroid/view/View;ILcom/bytedance/sdk/component/adexpress/hjc;)V

    :cond_0
    return-void
.end method

.method public Fj(Lcom/bytedance/sdk/component/adexpress/ex/BcC;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->UYd:Lcom/bytedance/sdk/component/adexpress/ex/BcC;

    return-void
.end method

.method public Fj(Lcom/bytedance/sdk/component/adexpress/ex/Tc;)V
    .locals 6

    const/16 v0, 0x69

    if-nez p1, :cond_1

    iget-object p1, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->Ko:Lcom/bytedance/sdk/component/adexpress/ex/svN;

    if-eqz p1, :cond_0

    iget-object p1, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->Ko:Lcom/bytedance/sdk/component/adexpress/ex/svN;

    const-string v1, "renderResult is null"

    invoke-interface {p1, v0, v1}, Lcom/bytedance/sdk/component/adexpress/ex/svN;->Fj(ILjava/lang/String;)V

    :cond_0
    return-void

    :cond_1
    invoke-virtual {p1}, Lcom/bytedance/sdk/component/adexpress/ex/Tc;->hjc()Z

    move-result v1

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/adexpress/ex/Tc;->eV()D

    move-result-wide v2

    double-to-float v2, v2

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/adexpress/ex/Tc;->Ubf()D

    move-result-wide v3

    double-to-float v3, v3

    const/4 v4, 0x0

    cmpg-float v5, v2, v4

    if-lez v5, :cond_4

    cmpg-float v4, v3, v4

    if-gtz v4, :cond_2

    goto :goto_0

    :cond_2
    iput-boolean v1, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->ex:Z

    invoke-static {}, Landroid/os/Looper;->myLooper()Landroid/os/Looper;

    move-result-object v0

    invoke-static {}, Landroid/os/Looper;->getMainLooper()Landroid/os/Looper;

    move-result-object v1

    if-ne v0, v1, :cond_3

    invoke-direct {p0, p1, v2, v3}, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->Fj(Lcom/bytedance/sdk/component/adexpress/ex/Tc;FF)V

    return-void

    :cond_3
    new-instance v0, Landroid/os/Handler;

    invoke-static {}, Landroid/os/Looper;->getMainLooper()Landroid/os/Looper;

    move-result-object v1

    invoke-direct {v0, v1}, Landroid/os/Handler;-><init>(Landroid/os/Looper;)V

    new-instance v1, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj$1;

    invoke-direct {v1, p0, p1, v2, v3}, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj$1;-><init>(Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;Lcom/bytedance/sdk/component/adexpress/ex/Tc;FF)V

    invoke-virtual {v0, v1}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    return-void

    :cond_4
    :goto_0
    iget-object p1, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->Ko:Lcom/bytedance/sdk/component/adexpress/ex/svN;

    if-eqz p1, :cond_5

    iget-object p1, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->Ko:Lcom/bytedance/sdk/component/adexpress/ex/svN;

    new-instance v1, Ljava/lang/StringBuilder;

    const-string v4, "width is "

    invoke-direct {v1, v4}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(F)Ljava/lang/StringBuilder;

    const-string v2, "height is "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1, v3}, Ljava/lang/StringBuilder;->append(F)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-interface {p1, v0, v1}, Lcom/bytedance/sdk/component/adexpress/ex/svN;->Fj(ILjava/lang/String;)V

    :cond_5
    return-void
.end method

.method public Fj(Lcom/bytedance/sdk/component/adexpress/ex/svN;)V
    .locals 5

    iput-object p1, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->Ko:Lcom/bytedance/sdk/component/adexpress/ex/svN;

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->Fj()Lcom/bytedance/sdk/component/widget/SSWebView;

    move-result-object p1

    const/4 v0, 0x0

    const/4 v1, 0x1

    const/16 v2, 0x66

    if-eqz p1, :cond_7

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->Fj()Lcom/bytedance/sdk/component/widget/SSWebView;

    move-result-object p1

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/widget/SSWebView;->getWebView()Landroid/webkit/WebView;

    move-result-object p1

    if-nez p1, :cond_0

    goto/16 :goto_0

    :cond_0
    iget-object p1, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->mSE:Ljava/lang/String;

    invoke-static {p1}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result p1

    if-eqz p1, :cond_1

    iget-object p1, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->Ko:Lcom/bytedance/sdk/component/adexpress/ex/svN;

    const-string v0, "url is empty"

    invoke-interface {p1, v2, v0}, Lcom/bytedance/sdk/component/adexpress/ex/svN;->Fj(ILjava/lang/String;)V

    return-void

    :cond_1
    iget-boolean p1, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->JU:Z

    const/16 v3, 0x67

    if-nez p1, :cond_3

    iget-object p1, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->Fj:Lorg/json/JSONObject;

    invoke-static {p1}, Lcom/bytedance/sdk/component/adexpress/Fj/ex/ex;->Fj(Lorg/json/JSONObject;)Z

    move-result p1

    if-nez p1, :cond_3

    iget-object p1, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->Ko:Lcom/bytedance/sdk/component/adexpress/ex/svN;

    new-instance v2, Ljava/lang/StringBuilder;

    const-string v4, "data null is "

    invoke-direct {v2, v4}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    iget-object v4, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->Fj:Lorg/json/JSONObject;

    if-nez v4, :cond_2

    const/4 v0, 0x1

    :cond_2
    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-interface {p1, v3, v0}, Lcom/bytedance/sdk/component/adexpress/ex/svN;->Fj(ILjava/lang/String;)V

    return-void

    :cond_3
    iget-boolean p1, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->JU:Z

    if-eqz p1, :cond_5

    iget-object p1, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->Fj:Lorg/json/JSONObject;

    invoke-static {p1}, Lcom/bytedance/sdk/component/adexpress/Fj/ex/ex;->ex(Lorg/json/JSONObject;)Z

    move-result p1

    if-nez p1, :cond_5

    iget-object p1, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->Ko:Lcom/bytedance/sdk/component/adexpress/ex/svN;

    new-instance v2, Ljava/lang/StringBuilder;

    const-string v4, "choice ad data null is "

    invoke-direct {v2, v4}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    iget-object v4, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->Fj:Lorg/json/JSONObject;

    if-nez v4, :cond_4

    const/4 v0, 0x1

    :cond_4
    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-interface {p1, v3, v0}, Lcom/bytedance/sdk/component/adexpress/ex/svN;->Fj(ILjava/lang/String;)V

    return-void

    :cond_5
    iget-object p1, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->dG:Lcom/bytedance/sdk/component/adexpress/ex/dG;

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/adexpress/ex/dG;->Ubf()Lcom/bytedance/sdk/component/adexpress/ex/mSE;

    move-result-object p1

    iget-boolean v0, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->rAx:Z

    invoke-interface {p1, v0}, Lcom/bytedance/sdk/component/adexpress/ex/mSE;->Fj(Z)V

    iget-boolean p1, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->rAx:Z

    if-eqz p1, :cond_6

    :try_start_0
    const-string p1, "javascript:window.SDK_RESET_RENDER();window.SDK_TRIGGER_RENDER();"

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->hjc:Lcom/bytedance/sdk/component/widget/SSWebView;

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/widget/SSWebView;->dG()V

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->dG:Lcom/bytedance/sdk/component/adexpress/ex/dG;

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/adexpress/ex/dG;->Ubf()Lcom/bytedance/sdk/component/adexpress/ex/mSE;

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->hjc:Lcom/bytedance/sdk/component/widget/SSWebView;

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/widget/SSWebView;->getWebView()Landroid/webkit/WebView;

    move-result-object v0

    invoke-static {v0, p1}, Lcom/bytedance/sdk/component/utils/UYd;->Fj(Landroid/webkit/WebView;Ljava/lang/String;)V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    return-void

    :catch_0
    move-exception p1

    invoke-static {}, Lcom/bytedance/sdk/component/adexpress/Ubf/Ubf;->Fj()Lcom/bytedance/sdk/component/adexpress/Ubf/Ubf;

    move-result-object v0

    iget-object v1, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->hjc:Lcom/bytedance/sdk/component/widget/SSWebView;

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/component/adexpress/Ubf/Ubf;->Ubf(Lcom/bytedance/sdk/component/widget/SSWebView;)Z

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->Ko:Lcom/bytedance/sdk/component/adexpress/ex/svN;

    new-instance v1, Ljava/lang/StringBuilder;

    const-string v3, "load exception is "

    invoke-direct {v1, v3}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    invoke-virtual {p1}, Ljava/lang/Throwable;->getMessage()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-interface {v0, v2, p1}, Lcom/bytedance/sdk/component/adexpress/ex/svN;->Fj(ILjava/lang/String;)V

    return-void

    :cond_6
    invoke-virtual {p0}, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->Fj()Lcom/bytedance/sdk/component/widget/SSWebView;

    move-result-object p1

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/widget/SSWebView;->dG()V

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->dG:Lcom/bytedance/sdk/component/adexpress/ex/dG;

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/adexpress/ex/dG;->Ubf()Lcom/bytedance/sdk/component/adexpress/ex/mSE;

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->mSE:Ljava/lang/String;

    invoke-virtual {p1, v0}, Lcom/bytedance/sdk/component/widget/SSWebView;->a_(Ljava/lang/String;)V

    return-void

    :cond_7
    :goto_0
    iget-object p1, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->Ko:Lcom/bytedance/sdk/component/adexpress/ex/svN;

    new-instance v3, Ljava/lang/StringBuilder;

    const-string v4, "SSWebview null is "

    invoke-direct {v3, v4}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->Fj()Lcom/bytedance/sdk/component/widget/SSWebView;

    move-result-object v4

    if-nez v4, :cond_8

    const/4 v0, 0x1

    :cond_8
    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    const-string v0, " or Webview is null"

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-interface {p1, v2, v0}, Lcom/bytedance/sdk/component/adexpress/ex/svN;->Fj(ILjava/lang/String;)V

    return-void
.end method

.method public Fj(Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->mSE:Ljava/lang/String;

    return-void
.end method

.method public Fj(Lorg/json/JSONObject;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->Fj:Lorg/json/JSONObject;

    return-void
.end method

.method public Fj(Z)V
    .locals 0

    iput-boolean p1, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->Tc:Z

    return-void
.end method

.method public Ko()V
    .locals 0

    return-void
.end method

.method public synthetic Ubf()Landroid/view/View;
    .locals 1

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->ex()Lcom/bytedance/sdk/component/widget/SSWebView;

    move-result-object v0

    return-object v0
.end method

.method public WR()V
    .locals 1

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->Fj()Lcom/bytedance/sdk/component/widget/SSWebView;

    move-result-object v0

    if-nez v0, :cond_0

    return-void

    :cond_0
    :try_start_0
    invoke-virtual {p0}, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->Fj()Lcom/bytedance/sdk/component/widget/SSWebView;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/widget/SSWebView;->getWebView()Landroid/webkit/WebView;

    move-result-object v0

    invoke-virtual {v0}, Landroid/webkit/WebView;->resumeTimers()V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    :catch_0
    return-void
.end method

.method public eV()V
    .locals 2

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->WR:Ljava/util/concurrent/atomic/AtomicBoolean;

    invoke-virtual {v0}, Ljava/util/concurrent/atomic/AtomicBoolean;->get()Z

    move-result v0

    if-eqz v0, :cond_0

    return-void

    :cond_0
    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->WR:Ljava/util/concurrent/atomic/AtomicBoolean;

    const/4 v1, 0x1

    invoke-virtual {v0, v1}, Ljava/util/concurrent/atomic/AtomicBoolean;->set(Z)V

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->svN()V

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->hjc:Lcom/bytedance/sdk/component/widget/SSWebView;

    invoke-virtual {v0}, Landroid/view/View;->getParent()Landroid/view/ViewParent;

    move-result-object v0

    if-eqz v0, :cond_1

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->hjc:Lcom/bytedance/sdk/component/widget/SSWebView;

    invoke-virtual {v0}, Landroid/view/View;->getParent()Landroid/view/ViewParent;

    move-result-object v0

    check-cast v0, Landroid/view/ViewGroup;

    iget-object v1, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->hjc:Lcom/bytedance/sdk/component/widget/SSWebView;

    invoke-virtual {v0, v1}, Landroid/view/ViewGroup;->removeView(Landroid/view/View;)V

    :cond_1
    iget-boolean v0, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->ex:Z

    if-eqz v0, :cond_2

    invoke-direct {p0}, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->Tc()V

    return-void

    :cond_2
    invoke-static {}, Lcom/bytedance/sdk/component/adexpress/Ubf/Ubf;->Fj()Lcom/bytedance/sdk/component/adexpress/Ubf/Ubf;

    move-result-object v0

    iget-object v1, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->hjc:Lcom/bytedance/sdk/component/widget/SSWebView;

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/component/adexpress/Ubf/Ubf;->Ubf(Lcom/bytedance/sdk/component/widget/SSWebView;)Z

    return-void
.end method

.method public ex()Lcom/bytedance/sdk/component/widget/SSWebView;
    .locals 1

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->Fj()Lcom/bytedance/sdk/component/widget/SSWebView;

    move-result-object v0

    return-object v0
.end method

.method public abstract ex(I)V
.end method

.method public ex(Z)V
    .locals 0

    iput-boolean p1, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->JU:Z

    return-void
.end method

.method public hjc()I
    .locals 1

    const/4 v0, 0x0

    return v0
.end method

.method public mSE()V
    .locals 0

    return-void
.end method

.method public rAx()Lcom/bytedance/sdk/component/adexpress/ex/dG;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->dG:Lcom/bytedance/sdk/component/adexpress/ex/dG;

    return-object v0
.end method

.method public abstract svN()V
.end method
