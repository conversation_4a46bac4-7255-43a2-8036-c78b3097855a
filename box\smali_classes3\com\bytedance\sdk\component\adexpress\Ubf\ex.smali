.class public interface abstract Lcom/bytedance/sdk/component/adexpress/Ubf/ex;
.super Ljava/lang/Object;


# virtual methods
.method public abstract Fj(Ljava/lang/String;)V
.end method

.method public abstract adInfo()Ljava/lang/String;
.end method

.method public abstract appInfo()Ljava/lang/String;
.end method

.method public abstract changeVideoState(Ljava/lang/String;)V
.end method

.method public abstract chooseAdResult(Ljava/lang/String;)V
.end method

.method public abstract clickEvent(Ljava/lang/String;)V
.end method

.method public abstract dynamicTrack(Ljava/lang/String;)V
.end method

.method public abstract getCurrentVideoState()Ljava/lang/String;
.end method

.method public abstract getTemplateInfo()Ljava/lang/String;
.end method

.method public abstract initRenderFinish()V
.end method

.method public abstract muteVideo(Ljava/lang/String;)V
.end method

.method public abstract renderDidFinish(Ljava/lang/String;)V
.end method

.method public abstract skipVideo()V
.end method
