.class Lcom/bytedance/sdk/component/adexpress/dynamic/Fj/Fj$2$1;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/component/adexpress/dynamic/Fj/Fj$2;->Fj(Lcom/bytedance/sdk/component/adexpress/dynamic/hjc/BcC;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/component/adexpress/dynamic/hjc/BcC;

.field final synthetic ex:Lcom/bytedance/sdk/component/adexpress/dynamic/Fj/Fj$2;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/component/adexpress/dynamic/Fj/Fj$2;Lcom/bytedance/sdk/component/adexpress/dynamic/hjc/BcC;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/adexpress/dynamic/Fj/Fj$2$1;->ex:Lcom/bytedance/sdk/component/adexpress/dynamic/Fj/Fj$2;

    iput-object p2, p0, Lcom/bytedance/sdk/component/adexpress/dynamic/Fj/Fj$2$1;->Fj:Lcom/bytedance/sdk/component/adexpress/dynamic/hjc/BcC;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 2

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/dynamic/Fj/Fj$2$1;->ex:Lcom/bytedance/sdk/component/adexpress/dynamic/Fj/Fj$2;

    iget-object v0, v0, Lcom/bytedance/sdk/component/adexpress/dynamic/Fj/Fj$2;->Fj:Lcom/bytedance/sdk/component/adexpress/dynamic/Fj/Fj;

    iget-object v1, p0, Lcom/bytedance/sdk/component/adexpress/dynamic/Fj/Fj$2$1;->Fj:Lcom/bytedance/sdk/component/adexpress/dynamic/hjc/BcC;

    invoke-static {v0, v1}, Lcom/bytedance/sdk/component/adexpress/dynamic/Fj/Fj;->hjc(Lcom/bytedance/sdk/component/adexpress/dynamic/Fj/Fj;Lcom/bytedance/sdk/component/adexpress/dynamic/hjc/BcC;)V

    return-void
.end method
