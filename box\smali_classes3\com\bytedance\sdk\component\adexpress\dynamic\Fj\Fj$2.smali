.class Lcom/bytedance/sdk/component/adexpress/dynamic/Fj/Fj$2;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/sdk/component/adexpress/dynamic/Ubf/ex;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/component/adexpress/dynamic/Fj/Fj;->WR()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/component/adexpress/dynamic/Fj/Fj;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/component/adexpress/dynamic/Fj/Fj;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/adexpress/dynamic/Fj/Fj$2;->Fj:Lcom/bytedance/sdk/component/adexpress/dynamic/Fj/Fj;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public Fj(Lcom/bytedance/sdk/component/adexpress/dynamic/hjc/BcC;)V
    .locals 2

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/dynamic/Fj/Fj$2;->Fj:Lcom/bytedance/sdk/component/adexpress/dynamic/Fj/Fj;

    invoke-static {v0}, Lcom/bytedance/sdk/component/adexpress/dynamic/Fj/Fj;->ex(Lcom/bytedance/sdk/component/adexpress/dynamic/Fj/Fj;)V

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/dynamic/Fj/Fj$2;->Fj:Lcom/bytedance/sdk/component/adexpress/dynamic/Fj/Fj;

    invoke-static {v0}, Lcom/bytedance/sdk/component/adexpress/dynamic/Fj/Fj;->hjc(Lcom/bytedance/sdk/component/adexpress/dynamic/Fj/Fj;)Lcom/bytedance/sdk/component/adexpress/ex/dG;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/adexpress/ex/dG;->Ubf()Lcom/bytedance/sdk/component/adexpress/ex/mSE;

    move-result-object v0

    iget-object v1, p0, Lcom/bytedance/sdk/component/adexpress/dynamic/Fj/Fj$2;->Fj:Lcom/bytedance/sdk/component/adexpress/dynamic/Fj/Fj;

    invoke-virtual {v1}, Lcom/bytedance/sdk/component/adexpress/dynamic/Fj/Fj;->hjc()I

    move-result v1

    invoke-interface {v0, v1}, Lcom/bytedance/sdk/component/adexpress/ex/mSE;->hjc(I)V

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/dynamic/Fj/Fj$2;->Fj:Lcom/bytedance/sdk/component/adexpress/dynamic/Fj/Fj;

    invoke-static {v0, p1}, Lcom/bytedance/sdk/component/adexpress/dynamic/Fj/Fj;->Fj(Lcom/bytedance/sdk/component/adexpress/dynamic/Fj/Fj;Lcom/bytedance/sdk/component/adexpress/dynamic/hjc/BcC;)V

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/dynamic/Fj/Fj$2;->Fj:Lcom/bytedance/sdk/component/adexpress/dynamic/Fj/Fj;

    invoke-static {v0, p1}, Lcom/bytedance/sdk/component/adexpress/dynamic/Fj/Fj;->ex(Lcom/bytedance/sdk/component/adexpress/dynamic/Fj/Fj;Lcom/bytedance/sdk/component/adexpress/dynamic/hjc/BcC;)V

    invoke-static {}, Landroid/os/Looper;->getMainLooper()Landroid/os/Looper;

    move-result-object v0

    invoke-static {}, Landroid/os/Looper;->myLooper()Landroid/os/Looper;

    move-result-object v1

    if-ne v0, v1, :cond_0

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/dynamic/Fj/Fj$2;->Fj:Lcom/bytedance/sdk/component/adexpress/dynamic/Fj/Fj;

    invoke-static {v0, p1}, Lcom/bytedance/sdk/component/adexpress/dynamic/Fj/Fj;->hjc(Lcom/bytedance/sdk/component/adexpress/dynamic/Fj/Fj;Lcom/bytedance/sdk/component/adexpress/dynamic/hjc/BcC;)V

    goto :goto_0

    :cond_0
    new-instance v0, Landroid/os/Handler;

    invoke-static {}, Landroid/os/Looper;->getMainLooper()Landroid/os/Looper;

    move-result-object v1

    invoke-direct {v0, v1}, Landroid/os/Handler;-><init>(Landroid/os/Looper;)V

    new-instance v1, Lcom/bytedance/sdk/component/adexpress/dynamic/Fj/Fj$2$1;

    invoke-direct {v1, p0, p1}, Lcom/bytedance/sdk/component/adexpress/dynamic/Fj/Fj$2$1;-><init>(Lcom/bytedance/sdk/component/adexpress/dynamic/Fj/Fj$2;Lcom/bytedance/sdk/component/adexpress/dynamic/hjc/BcC;)V

    invoke-virtual {v0, v1}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    :goto_0
    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/dynamic/Fj/Fj$2;->Fj:Lcom/bytedance/sdk/component/adexpress/dynamic/Fj/Fj;

    invoke-static {v0}, Lcom/bytedance/sdk/component/adexpress/dynamic/Fj/Fj;->eV(Lcom/bytedance/sdk/component/adexpress/dynamic/Fj/Fj;)Lcom/bytedance/sdk/component/adexpress/dynamic/dynamicview/DynamicRootView;

    move-result-object v0

    if-eqz v0, :cond_1

    if-eqz p1, :cond_1

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/dynamic/Fj/Fj$2;->Fj:Lcom/bytedance/sdk/component/adexpress/dynamic/Fj/Fj;

    invoke-static {v0}, Lcom/bytedance/sdk/component/adexpress/dynamic/Fj/Fj;->eV(Lcom/bytedance/sdk/component/adexpress/dynamic/Fj/Fj;)Lcom/bytedance/sdk/component/adexpress/dynamic/dynamicview/DynamicRootView;

    move-result-object v0

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/adexpress/dynamic/hjc/BcC;->Fj()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/component/adexpress/dynamic/dynamicview/DynamicRootView;->setBgColor(Ljava/lang/String;)V

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/dynamic/Fj/Fj$2;->Fj:Lcom/bytedance/sdk/component/adexpress/dynamic/Fj/Fj;

    invoke-static {v0}, Lcom/bytedance/sdk/component/adexpress/dynamic/Fj/Fj;->eV(Lcom/bytedance/sdk/component/adexpress/dynamic/Fj/Fj;)Lcom/bytedance/sdk/component/adexpress/dynamic/dynamicview/DynamicRootView;

    move-result-object v0

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/adexpress/dynamic/hjc/BcC;->ex()Ljava/util/Map;

    move-result-object p1

    invoke-virtual {v0, p1}, Lcom/bytedance/sdk/component/adexpress/dynamic/dynamicview/DynamicRootView;->setBgMaterialCenterCalcColor(Ljava/util/Map;)V

    :cond_1
    return-void
.end method
