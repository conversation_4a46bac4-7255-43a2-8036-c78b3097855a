.class Lcom/bytedance/sdk/component/adexpress/dynamic/animation/Fj/Ko$Fj;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bytedance/sdk/component/adexpress/dynamic/animation/Fj/Ko;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = "Fj"
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/component/adexpress/dynamic/animation/Fj/Ko;

.field private ex:Landroid/view/View;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/component/adexpress/dynamic/animation/Fj/Ko;Landroid/view/View;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/adexpress/dynamic/animation/Fj/Ko$Fj;->Fj:Lcom/bytedance/sdk/component/adexpress/dynamic/animation/Fj/Ko;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p2, p0, Lcom/bytedance/sdk/component/adexpress/dynamic/animation/Fj/Ko$Fj;->ex:Landroid/view/View;

    return-void
.end method


# virtual methods
.method public Fj(I)V
    .locals 4

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/dynamic/animation/Fj/Ko$Fj;->Fj:Lcom/bytedance/sdk/component/adexpress/dynamic/animation/Fj/Ko;

    iget-object v0, v0, Lcom/bytedance/sdk/component/adexpress/dynamic/animation/Fj/eV;->ex:Lcom/bytedance/sdk/component/adexpress/dynamic/hjc/Fj;

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/adexpress/dynamic/hjc/Fj;->Fj()Ljava/lang/String;

    move-result-object v0

    const-string v1, "top"

    invoke-virtual {v1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_1

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/dynamic/animation/Fj/Ko$Fj;->Fj:Lcom/bytedance/sdk/component/adexpress/dynamic/animation/Fj/Ko;

    iget-object v0, v0, Lcom/bytedance/sdk/component/adexpress/dynamic/animation/Fj/eV;->hjc:Landroid/view/View;

    instance-of v0, v0, Landroid/view/ViewGroup;

    if-eqz v0, :cond_0

    const/4 v0, 0x0

    :goto_0
    iget-object v1, p0, Lcom/bytedance/sdk/component/adexpress/dynamic/animation/Fj/Ko$Fj;->Fj:Lcom/bytedance/sdk/component/adexpress/dynamic/animation/Fj/Ko;

    iget-object v1, v1, Lcom/bytedance/sdk/component/adexpress/dynamic/animation/Fj/eV;->hjc:Landroid/view/View;

    check-cast v1, Landroid/view/ViewGroup;

    invoke-virtual {v1}, Landroid/view/ViewGroup;->getChildCount()I

    move-result v1

    if-ge v0, v1, :cond_0

    iget-object v1, p0, Lcom/bytedance/sdk/component/adexpress/dynamic/animation/Fj/Ko$Fj;->Fj:Lcom/bytedance/sdk/component/adexpress/dynamic/animation/Fj/Ko;

    iget-object v1, v1, Lcom/bytedance/sdk/component/adexpress/dynamic/animation/Fj/eV;->hjc:Landroid/view/View;

    check-cast v1, Landroid/view/ViewGroup;

    invoke-virtual {v1, v0}, Landroid/view/ViewGroup;->getChildAt(I)Landroid/view/View;

    move-result-object v1

    int-to-float v2, p1

    iget-object v3, p0, Lcom/bytedance/sdk/component/adexpress/dynamic/animation/Fj/Ko$Fj;->Fj:Lcom/bytedance/sdk/component/adexpress/dynamic/animation/Fj/Ko;

    invoke-static {v3}, Lcom/bytedance/sdk/component/adexpress/dynamic/animation/Fj/Ko;->ex(Lcom/bytedance/sdk/component/adexpress/dynamic/animation/Fj/Ko;)F

    move-result v3

    sub-float/2addr v2, v3

    invoke-virtual {v1, v2}, Landroid/view/View;->setTranslationY(F)V

    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :cond_0
    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/dynamic/animation/Fj/Ko$Fj;->Fj:Lcom/bytedance/sdk/component/adexpress/dynamic/animation/Fj/Ko;

    iget-object v1, v0, Lcom/bytedance/sdk/component/adexpress/dynamic/animation/Fj/eV;->hjc:Landroid/view/View;

    invoke-static {v0}, Lcom/bytedance/sdk/component/adexpress/dynamic/animation/Fj/Ko;->ex(Lcom/bytedance/sdk/component/adexpress/dynamic/animation/Fj/Ko;)F

    move-result v0

    int-to-float p1, p1

    sub-float/2addr v0, p1

    invoke-virtual {v1, v0}, Landroid/view/View;->setTranslationY(F)V

    return-void

    :cond_1
    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/dynamic/animation/Fj/Ko$Fj;->ex:Landroid/view/View;

    invoke-virtual {v0}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    move-result-object v0

    iput p1, v0, Landroid/view/ViewGroup$LayoutParams;->height:I

    iget-object p1, p0, Lcom/bytedance/sdk/component/adexpress/dynamic/animation/Fj/Ko$Fj;->ex:Landroid/view/View;

    invoke-virtual {p1, v0}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    iget-object p1, p0, Lcom/bytedance/sdk/component/adexpress/dynamic/animation/Fj/Ko$Fj;->ex:Landroid/view/View;

    invoke-virtual {p1}, Landroid/view/View;->requestLayout()V

    return-void
.end method
