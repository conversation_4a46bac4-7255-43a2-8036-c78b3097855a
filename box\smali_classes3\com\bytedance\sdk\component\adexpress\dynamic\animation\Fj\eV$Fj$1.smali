.class Lcom/bytedance/sdk/component/adexpress/dynamic/animation/Fj/eV$Fj$1;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/component/adexpress/dynamic/animation/Fj/eV$Fj;->run()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/component/adexpress/dynamic/animation/Fj/eV$Fj;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/component/adexpress/dynamic/animation/Fj/eV$Fj;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/adexpress/dynamic/animation/Fj/eV$Fj$1;->Fj:Lcom/bytedance/sdk/component/adexpress/dynamic/animation/Fj/eV$Fj;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/dynamic/animation/Fj/eV$Fj$1;->Fj:Lcom/bytedance/sdk/component/adexpress/dynamic/animation/Fj/eV$Fj;

    iget-object v0, v0, Lcom/bytedance/sdk/component/adexpress/dynamic/animation/Fj/eV$Fj;->Fj:Landroid/animation/ObjectAnimator;

    invoke-virtual {v0}, Landroid/animation/Animator;->resume()V

    return-void
.end method
