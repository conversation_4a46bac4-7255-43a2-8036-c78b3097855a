.class public interface abstract Lcom/bytedance/sdk/component/adexpress/dynamic/animation/view/IAnimation;
.super Ljava/lang/Object;


# virtual methods
.method public abstract getMarqueeValue()F
.end method

.method public abstract getRippleValue()F
.end method

.method public abstract getShineValue()F
.end method

.method public abstract getStretchValue()F
.end method

.method public abstract setMarqueeValue(F)V
.end method

.method public abstract setRippleValue(F)V
.end method

.method public abstract setShineValue(F)V
.end method

.method public abstract setStretchValue(F)V
.end method
