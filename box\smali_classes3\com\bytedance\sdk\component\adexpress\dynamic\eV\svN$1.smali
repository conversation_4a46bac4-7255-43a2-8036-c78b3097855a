.class Lcom/bytedance/sdk/component/adexpress/dynamic/eV/svN$1;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/component/adexpress/dynamic/eV/svN;->Fj(Lcom/bytedance/sdk/component/adexpress/ex/dG;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/component/adexpress/ex/dG;

.field final synthetic ex:Lcom/bytedance/sdk/component/adexpress/dynamic/eV/svN;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/component/adexpress/dynamic/eV/svN;Lcom/bytedance/sdk/component/adexpress/ex/dG;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/adexpress/dynamic/eV/svN$1;->ex:Lcom/bytedance/sdk/component/adexpress/dynamic/eV/svN;

    iput-object p2, p0, Lcom/bytedance/sdk/component/adexpress/dynamic/eV/svN$1;->Fj:Lcom/bytedance/sdk/component/adexpress/ex/dG;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 2

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/dynamic/eV/svN$1;->ex:Lcom/bytedance/sdk/component/adexpress/dynamic/eV/svN;

    iget-object v1, p0, Lcom/bytedance/sdk/component/adexpress/dynamic/eV/svN$1;->Fj:Lcom/bytedance/sdk/component/adexpress/ex/dG;

    invoke-static {v0, v1}, Lcom/bytedance/sdk/component/adexpress/dynamic/eV/svN;->Fj(Lcom/bytedance/sdk/component/adexpress/dynamic/eV/svN;Lcom/bytedance/sdk/component/adexpress/ex/dG;)V

    return-void
.end method
