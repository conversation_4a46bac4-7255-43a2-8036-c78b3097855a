.class public Lcom/bytedance/sdk/component/adexpress/dynamic/hjc/Fj;
.super Ljava/lang/Object;


# instance fields
.field private Af:I

.field private BcC:Ljava/lang/String;

.field private Fj:Ljava/lang/String;

.field private JU:Ljava/lang/String;

.field private JW:Ljava/lang/String;

.field private Ko:I

.field private Ql:I

.field private Tc:I

.field private UYd:I

.field private Ubf:Ljava/lang/String;

.field private WR:D

.field private dG:I

.field private eV:D

.field private ex:D

.field private hjc:D

.field private mC:Ljava/lang/String;

.field private mE:Ljava/lang/String;

.field private mSE:D

.field private rAx:I

.field private rS:I

.field private svN:I

.field private vYf:J


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public Af()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/dynamic/hjc/Fj;->mC:Ljava/lang/String;

    return-object v0
.end method

.method public BcC()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/dynamic/hjc/Fj;->JW:Ljava/lang/String;

    return-object v0
.end method

.method public BcC(I)V
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/component/adexpress/dynamic/hjc/Fj;->rS:I

    return-void
.end method

.method public Fj()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/dynamic/hjc/Fj;->mE:Ljava/lang/String;

    return-object v0
.end method

.method public Fj(D)V
    .locals 0

    iput-wide p1, p0, Lcom/bytedance/sdk/component/adexpress/dynamic/hjc/Fj;->ex:D

    return-void
.end method

.method public Fj(I)V
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/component/adexpress/dynamic/hjc/Fj;->Ko:I

    return-void
.end method

.method public Fj(J)V
    .locals 0

    iput-wide p1, p0, Lcom/bytedance/sdk/component/adexpress/dynamic/hjc/Fj;->vYf:J

    return-void
.end method

.method public Fj(Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/adexpress/dynamic/hjc/Fj;->JU:Ljava/lang/String;

    return-void
.end method

.method public JU()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/dynamic/hjc/Fj;->BcC:Ljava/lang/String;

    return-object v0
.end method

.method public JW()I
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/component/adexpress/dynamic/hjc/Fj;->svN:I

    return v0
.end method

.method public Ko()D
    .locals 2

    iget-wide v0, p0, Lcom/bytedance/sdk/component/adexpress/dynamic/hjc/Fj;->ex:D

    return-wide v0
.end method

.method public Ql()D
    .locals 2

    iget-wide v0, p0, Lcom/bytedance/sdk/component/adexpress/dynamic/hjc/Fj;->mSE:D

    return-wide v0
.end method

.method public Tc()D
    .locals 2

    iget-wide v0, p0, Lcom/bytedance/sdk/component/adexpress/dynamic/hjc/Fj;->WR:D

    return-wide v0
.end method

.method public UYd()D
    .locals 2

    iget-wide v0, p0, Lcom/bytedance/sdk/component/adexpress/dynamic/hjc/Fj;->eV:D

    return-wide v0
.end method

.method public Ubf()I
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/component/adexpress/dynamic/hjc/Fj;->UYd:I

    return v0
.end method

.method public Ubf(D)V
    .locals 0

    iput-wide p1, p0, Lcom/bytedance/sdk/component/adexpress/dynamic/hjc/Fj;->mSE:D

    return-void
.end method

.method public Ubf(I)V
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/component/adexpress/dynamic/hjc/Fj;->Tc:I

    return-void
.end method

.method public Ubf(Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/adexpress/dynamic/hjc/Fj;->BcC:Ljava/lang/String;

    return-void
.end method

.method public WR()I
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/component/adexpress/dynamic/hjc/Fj;->dG:I

    return v0
.end method

.method public WR(I)V
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/component/adexpress/dynamic/hjc/Fj;->svN:I

    return-void
.end method

.method public WR(Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/adexpress/dynamic/hjc/Fj;->mC:Ljava/lang/String;

    return-void
.end method

.method public dG()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/dynamic/hjc/Fj;->Ubf:Ljava/lang/String;

    return-object v0
.end method

.method public eV()I
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/component/adexpress/dynamic/hjc/Fj;->rAx:I

    return v0
.end method

.method public eV(D)V
    .locals 0

    iput-wide p1, p0, Lcom/bytedance/sdk/component/adexpress/dynamic/hjc/Fj;->WR:D

    return-void
.end method

.method public eV(I)V
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/component/adexpress/dynamic/hjc/Fj;->dG:I

    return-void
.end method

.method public eV(Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/adexpress/dynamic/hjc/Fj;->Ubf:Ljava/lang/String;

    return-void
.end method

.method public ex()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/dynamic/hjc/Fj;->JU:Ljava/lang/String;

    return-object v0
.end method

.method public ex(D)V
    .locals 0

    iput-wide p1, p0, Lcom/bytedance/sdk/component/adexpress/dynamic/hjc/Fj;->hjc:D

    return-void
.end method

.method public ex(I)V
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/component/adexpress/dynamic/hjc/Fj;->rAx:I

    return-void
.end method

.method public ex(Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/adexpress/dynamic/hjc/Fj;->JW:Ljava/lang/String;

    return-void
.end method

.method public hjc()I
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/component/adexpress/dynamic/hjc/Fj;->Ko:I

    return v0
.end method

.method public hjc(D)V
    .locals 0

    iput-wide p1, p0, Lcom/bytedance/sdk/component/adexpress/dynamic/hjc/Fj;->eV:D

    return-void
.end method

.method public hjc(I)V
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/component/adexpress/dynamic/hjc/Fj;->UYd:I

    return-void
.end method

.method public hjc(Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/adexpress/dynamic/hjc/Fj;->Fj:Ljava/lang/String;

    return-void
.end method

.method public mE()I
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/component/adexpress/dynamic/hjc/Fj;->Af:I

    return v0
.end method

.method public mSE()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/dynamic/hjc/Fj;->Fj:Ljava/lang/String;

    return-object v0
.end method

.method public mSE(I)V
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/component/adexpress/dynamic/hjc/Fj;->Af:I

    return-void
.end method

.method public rAx()D
    .locals 2

    iget-wide v0, p0, Lcom/bytedance/sdk/component/adexpress/dynamic/hjc/Fj;->hjc:D

    return-wide v0
.end method

.method public rS()I
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/component/adexpress/dynamic/hjc/Fj;->Ql:I

    return v0
.end method

.method public svN()I
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/component/adexpress/dynamic/hjc/Fj;->Tc:I

    return v0
.end method

.method public svN(I)V
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/component/adexpress/dynamic/hjc/Fj;->Ql:I

    return-void
.end method

.method public vYf()I
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/component/adexpress/dynamic/hjc/Fj;->rS:I

    return v0
.end method
