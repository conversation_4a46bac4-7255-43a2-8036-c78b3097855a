.class public Lcom/bytedance/sdk/component/adexpress/dynamic/hjc/Ko;
.super Ljava/lang/Object;


# instance fields
.field private Fj:I

.field private Ubf:I

.field private WR:Lorg/json/JSONObject;

.field private eV:I

.field private ex:I

.field private hjc:I


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public Fj()I
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/component/adexpress/dynamic/hjc/Ko;->Fj:I

    return v0
.end method

.method public Fj(I)V
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/component/adexpress/dynamic/hjc/Ko;->Fj:I

    return-void
.end method

.method public Fj(Lorg/json/JSONObject;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/adexpress/dynamic/hjc/Ko;->WR:Lorg/json/JSONObject;

    return-void
.end method

.method public Ubf()I
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/component/adexpress/dynamic/hjc/Ko;->Ubf:I

    return v0
.end method

.method public Ubf(I)V
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/component/adexpress/dynamic/hjc/Ko;->Ubf:I

    return-void
.end method

.method public WR()Lorg/json/JSONObject;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/dynamic/hjc/Ko;->WR:Lorg/json/JSONObject;

    return-object v0
.end method

.method public eV()I
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/component/adexpress/dynamic/hjc/Ko;->eV:I

    return v0
.end method

.method public eV(I)V
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/component/adexpress/dynamic/hjc/Ko;->eV:I

    return-void
.end method

.method public ex()I
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/component/adexpress/dynamic/hjc/Ko;->ex:I

    return v0
.end method

.method public ex(I)V
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/component/adexpress/dynamic/hjc/Ko;->ex:I

    return-void
.end method

.method public hjc()I
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/component/adexpress/dynamic/hjc/Ko;->hjc:I

    return v0
.end method

.method public hjc(I)V
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/component/adexpress/dynamic/hjc/Ko;->hjc:I

    return-void
.end method
