.class public Lcom/bytedance/sdk/component/adexpress/dynamic/hjc/ex;
.super Ljava/lang/Object;


# instance fields
.field public BcC:Lcom/bytedance/sdk/component/adexpress/dynamic/hjc/ex;

.field public Fj:F

.field public Ko:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lcom/bytedance/sdk/component/adexpress/dynamic/hjc/BcC;",
            ">;"
        }
    .end annotation
.end field

.field public Ubf:Ljava/lang/String;

.field public WR:Lcom/bytedance/sdk/component/adexpress/dynamic/hjc/BcC;

.field public eV:F

.field public ex:F

.field public hjc:F

.field public mSE:F

.field public svN:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Ljava/util/List<",
            "Lcom/bytedance/sdk/component/adexpress/dynamic/hjc/ex;",
            ">;>;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
