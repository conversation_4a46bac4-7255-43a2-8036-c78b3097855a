.class Lcom/bytedance/sdk/component/adexpress/dynamic/interact/Fj/Fj$1;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/component/adexpress/dynamic/interact/Fj/Fj;-><init>(Lcom/bytedance/sdk/component/adexpress/dynamic/interact/BcC;ILandroid/view/ViewGroup;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Landroid/view/ViewGroup;

.field final synthetic ex:Lcom/bytedance/sdk/component/adexpress/dynamic/interact/Fj/Fj;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/component/adexpress/dynamic/interact/Fj/Fj;Landroid/view/ViewGroup;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/adexpress/dynamic/interact/Fj/Fj$1;->ex:Lcom/bytedance/sdk/component/adexpress/dynamic/interact/Fj/Fj;

    iput-object p2, p0, Lcom/bytedance/sdk/component/adexpress/dynamic/interact/Fj/Fj$1;->Fj:Landroid/view/ViewGroup;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 3

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/dynamic/interact/Fj/Fj$1;->ex:Lcom/bytedance/sdk/component/adexpress/dynamic/interact/Fj/Fj;

    new-instance v1, Ljava/lang/ref/SoftReference;

    iget-object v2, p0, Lcom/bytedance/sdk/component/adexpress/dynamic/interact/Fj/Fj$1;->Fj:Landroid/view/ViewGroup;

    invoke-direct {v1, v2}, Ljava/lang/ref/SoftReference;-><init>(Ljava/lang/Object;)V

    invoke-static {v0, v1}, Lcom/bytedance/sdk/component/adexpress/dynamic/interact/Fj/Fj;->Fj(Lcom/bytedance/sdk/component/adexpress/dynamic/interact/Fj/Fj;Ljava/lang/ref/SoftReference;)Ljava/lang/ref/SoftReference;

    return-void
.end method
