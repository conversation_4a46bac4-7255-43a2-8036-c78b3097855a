.class Lcom/bytedance/sdk/component/adexpress/dynamic/interact/WR$1;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/sdk/component/adexpress/widget/ShakeAnimationView$Fj;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/component/adexpress/dynamic/interact/WR;->Fj(IIILcom/bytedance/sdk/component/adexpress/dynamic/hjc/svN;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/component/adexpress/widget/ShakeClickView;

.field final synthetic ex:Lcom/bytedance/sdk/component/adexpress/dynamic/interact/WR;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/component/adexpress/dynamic/interact/WR;Lcom/bytedance/sdk/component/adexpress/widget/ShakeClickView;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/adexpress/dynamic/interact/WR$1;->ex:Lcom/bytedance/sdk/component/adexpress/dynamic/interact/WR;

    iput-object p2, p0, Lcom/bytedance/sdk/component/adexpress/dynamic/interact/WR$1;->Fj:Lcom/bytedance/sdk/component/adexpress/widget/ShakeClickView;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
