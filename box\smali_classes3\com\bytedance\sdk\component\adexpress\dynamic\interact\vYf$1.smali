.class Lcom/bytedance/sdk/component/adexpress/dynamic/interact/vYf$1;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/sdk/component/adexpress/widget/WriggleGuideAnimationView$Fj;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/component/adexpress/dynamic/interact/vYf;->Ubf()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Landroid/view/View;

.field final synthetic ex:Lcom/bytedance/sdk/component/adexpress/dynamic/Ubf/Fj;

.field final synthetic hjc:Lcom/bytedance/sdk/component/adexpress/dynamic/interact/vYf;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/component/adexpress/dynamic/interact/vYf;Landroid/view/View;Lcom/bytedance/sdk/component/adexpress/dynamic/Ubf/Fj;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/adexpress/dynamic/interact/vYf$1;->hjc:Lcom/bytedance/sdk/component/adexpress/dynamic/interact/vYf;

    iput-object p2, p0, Lcom/bytedance/sdk/component/adexpress/dynamic/interact/vYf$1;->Fj:Landroid/view/View;

    iput-object p3, p0, Lcom/bytedance/sdk/component/adexpress/dynamic/interact/vYf$1;->ex:Lcom/bytedance/sdk/component/adexpress/dynamic/Ubf/Fj;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
