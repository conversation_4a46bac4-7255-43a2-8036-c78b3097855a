.class public abstract Lcom/bytedance/sdk/component/adexpress/ex/Fj;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/sdk/component/adexpress/ex/eV;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "<T:",
        "Landroid/view/View;",
        ">",
        "Ljava/lang/Object;",
        "Lcom/bytedance/sdk/component/adexpress/ex/eV;"
    }
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public abstract Fj(Lcom/bytedance/sdk/component/adexpress/ex/hjc;)V
.end method

.method public hjc()I
    .locals 1

    const/4 v0, 0x1

    return v0
.end method
