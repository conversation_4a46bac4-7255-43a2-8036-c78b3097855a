.class Lcom/bytedance/sdk/component/adexpress/ex/JU$Fj;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bytedance/sdk/component/adexpress/ex/JU;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = "Fj"
.end annotation


# instance fields
.field Fj:Lcom/bytedance/sdk/component/adexpress/ex/Ko$Fj;

.field final synthetic ex:Lcom/bytedance/sdk/component/adexpress/ex/JU;

.field private hjc:I


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/component/adexpress/ex/JU;ILcom/bytedance/sdk/component/adexpress/ex/Ko$Fj;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/adexpress/ex/JU$Fj;->ex:Lcom/bytedance/sdk/component/adexpress/ex/JU;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput p2, p0, Lcom/bytedance/sdk/component/adexpress/ex/JU$Fj;->hjc:I

    iput-object p3, p0, Lcom/bytedance/sdk/component/adexpress/ex/JU$Fj;->Fj:Lcom/bytedance/sdk/component/adexpress/ex/Ko$Fj;

    return-void
.end method


# virtual methods
.method public run()V
    .locals 4

    iget v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/JU$Fj;->hjc:I

    const/4 v1, 0x1

    if-ne v0, v1, :cond_0

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/JU$Fj;->ex:Lcom/bytedance/sdk/component/adexpress/ex/JU;

    invoke-static {v0}, Lcom/bytedance/sdk/component/adexpress/ex/JU;->ex(Lcom/bytedance/sdk/component/adexpress/ex/JU;)Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;

    move-result-object v0

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->Fj(Z)V

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/JU$Fj;->ex:Lcom/bytedance/sdk/component/adexpress/ex/JU;

    iget-object v1, p0, Lcom/bytedance/sdk/component/adexpress/ex/JU$Fj;->Fj:Lcom/bytedance/sdk/component/adexpress/ex/Ko$Fj;

    const/16 v2, 0x6b

    const/4 v3, 0x0

    invoke-static {v0, v1, v2, v3}, Lcom/bytedance/sdk/component/adexpress/ex/JU;->Fj(Lcom/bytedance/sdk/component/adexpress/ex/JU;Lcom/bytedance/sdk/component/adexpress/ex/Ko$Fj;ILjava/lang/String;)V

    :cond_0
    return-void
.end method
