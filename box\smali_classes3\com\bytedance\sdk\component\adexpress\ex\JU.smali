.class public Lcom/bytedance/sdk/component/adexpress/ex/JU;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/sdk/component/adexpress/ex/Ko;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/bytedance/sdk/component/adexpress/ex/JU$Fj;
    }
.end annotation


# instance fields
.field private Fj:Landroid/content/Context;

.field private Ubf:Ljava/util/concurrent/ScheduledFuture;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/concurrent/ScheduledFuture<",
            "*>;"
        }
    .end annotation
.end field

.field private WR:Ljava/util/concurrent/atomic/AtomicBoolean;

.field private eV:Lcom/bytedance/sdk/component/adexpress/ex/dG;

.field private ex:Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;

.field private hjc:Lcom/bytedance/sdk/component/adexpress/ex/BcC;


# direct methods
.method public constructor <init>(Landroid/content/Context;Lcom/bytedance/sdk/component/adexpress/ex/dG;Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;Lcom/bytedance/sdk/component/adexpress/ex/BcC;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/bytedance/sdk/component/adexpress/ex/JU;->Fj:Landroid/content/Context;

    iput-object p2, p0, Lcom/bytedance/sdk/component/adexpress/ex/JU;->eV:Lcom/bytedance/sdk/component/adexpress/ex/dG;

    iput-object p4, p0, Lcom/bytedance/sdk/component/adexpress/ex/JU;->hjc:Lcom/bytedance/sdk/component/adexpress/ex/BcC;

    new-instance p1, Ljava/util/concurrent/atomic/AtomicBoolean;

    const/4 p2, 0x0

    invoke-direct {p1, p2}, Ljava/util/concurrent/atomic/AtomicBoolean;-><init>(Z)V

    iput-object p1, p0, Lcom/bytedance/sdk/component/adexpress/ex/JU;->WR:Ljava/util/concurrent/atomic/AtomicBoolean;

    iput-object p3, p0, Lcom/bytedance/sdk/component/adexpress/ex/JU;->ex:Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;

    iget-object p1, p0, Lcom/bytedance/sdk/component/adexpress/ex/JU;->hjc:Lcom/bytedance/sdk/component/adexpress/ex/BcC;

    invoke-virtual {p3, p1}, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->Fj(Lcom/bytedance/sdk/component/adexpress/ex/BcC;)V

    return-void
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/component/adexpress/ex/JU;)V
    .locals 0

    invoke-direct {p0}, Lcom/bytedance/sdk/component/adexpress/ex/JU;->hjc()V

    return-void
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/component/adexpress/ex/JU;Lcom/bytedance/sdk/component/adexpress/ex/Ko$Fj;ILjava/lang/String;)V
    .locals 0

    invoke-direct {p0, p1, p2, p3}, Lcom/bytedance/sdk/component/adexpress/ex/JU;->Fj(Lcom/bytedance/sdk/component/adexpress/ex/Ko$Fj;ILjava/lang/String;)V

    return-void
.end method

.method private Fj(Lcom/bytedance/sdk/component/adexpress/ex/Ko$Fj;ILjava/lang/String;)V
    .locals 1

    invoke-interface {p1}, Lcom/bytedance/sdk/component/adexpress/ex/Ko$Fj;->hjc()Z

    move-result v0

    if-eqz v0, :cond_0

    return-void

    :cond_0
    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/JU;->WR:Ljava/util/concurrent/atomic/AtomicBoolean;

    invoke-virtual {v0}, Ljava/util/concurrent/atomic/AtomicBoolean;->get()Z

    move-result v0

    if-eqz v0, :cond_1

    return-void

    :cond_1
    invoke-direct {p0}, Lcom/bytedance/sdk/component/adexpress/ex/JU;->hjc()V

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/JU;->eV:Lcom/bytedance/sdk/component/adexpress/ex/dG;

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/adexpress/ex/dG;->Ubf()Lcom/bytedance/sdk/component/adexpress/ex/mSE;

    move-result-object v0

    invoke-interface {v0, p2, p3}, Lcom/bytedance/sdk/component/adexpress/ex/mSE;->Fj(ILjava/lang/String;)V

    invoke-interface {p1, p0}, Lcom/bytedance/sdk/component/adexpress/ex/Ko$Fj;->ex(Lcom/bytedance/sdk/component/adexpress/ex/Ko;)Z

    move-result p3

    const/4 v0, 0x1

    if-eqz p3, :cond_2

    invoke-interface {p1, p0}, Lcom/bytedance/sdk/component/adexpress/ex/Ko$Fj;->Fj(Lcom/bytedance/sdk/component/adexpress/ex/Ko;)V

    goto :goto_0

    :cond_2
    invoke-interface {p1}, Lcom/bytedance/sdk/component/adexpress/ex/Ko$Fj;->hjc()Z

    move-result p3

    if-eqz p3, :cond_3

    return-void

    :cond_3
    invoke-interface {p1}, Lcom/bytedance/sdk/component/adexpress/ex/Ko$Fj;->ex()Lcom/bytedance/sdk/component/adexpress/ex/JW;

    move-result-object p3

    if-nez p3, :cond_4

    return-void

    :cond_4
    invoke-interface {p1, v0}, Lcom/bytedance/sdk/component/adexpress/ex/Ko$Fj;->Fj(Z)V

    invoke-interface {p3, p2}, Lcom/bytedance/sdk/component/adexpress/ex/JW;->a_(I)V

    :goto_0
    iget-object p1, p0, Lcom/bytedance/sdk/component/adexpress/ex/JU;->WR:Ljava/util/concurrent/atomic/AtomicBoolean;

    invoke-virtual {p1, v0}, Ljava/util/concurrent/atomic/AtomicBoolean;->getAndSet(Z)Z

    return-void
.end method

.method public static synthetic ex(Lcom/bytedance/sdk/component/adexpress/ex/JU;)Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/component/adexpress/ex/JU;->ex:Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;

    return-object p0
.end method

.method private hjc()V
    .locals 2

    :try_start_0
    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/JU;->Ubf:Ljava/util/concurrent/ScheduledFuture;

    if-eqz v0, :cond_0

    invoke-interface {v0}, Ljava/util/concurrent/Future;->isCancelled()Z

    move-result v0

    if-nez v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/JU;->Ubf:Ljava/util/concurrent/ScheduledFuture;

    const/4 v1, 0x0

    invoke-interface {v0, v1}, Ljava/util/concurrent/Future;->cancel(Z)Z

    const/4 v0, 0x0

    iput-object v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/JU;->Ubf:Ljava/util/concurrent/ScheduledFuture;
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    :catchall_0
    :cond_0
    return-void
.end method


# virtual methods
.method public Fj()V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/JU;->ex:Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->eV()V

    invoke-direct {p0}, Lcom/bytedance/sdk/component/adexpress/ex/JU;->hjc()V

    return-void
.end method

.method public Fj(Lcom/bytedance/sdk/component/adexpress/ex/Ko$Fj;)Z
    .locals 6

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/JU;->eV:Lcom/bytedance/sdk/component/adexpress/ex/dG;

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/adexpress/ex/dG;->WR()I

    move-result v0

    const/4 v1, 0x1

    if-gez v0, :cond_0

    const-string v2, "time is "

    invoke-static {v0}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v2, v0}, Ljava/lang/String;->concat(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    const/16 v2, 0x6b

    invoke-direct {p0, p1, v2, v0}, Lcom/bytedance/sdk/component/adexpress/ex/JU;->Fj(Lcom/bytedance/sdk/component/adexpress/ex/Ko$Fj;ILjava/lang/String;)V

    goto :goto_0

    :cond_0
    invoke-static {}, Lcom/bytedance/sdk/component/svN/WR;->WR()Ljava/util/concurrent/ScheduledExecutorService;

    move-result-object v2

    new-instance v3, Lcom/bytedance/sdk/component/adexpress/ex/JU$Fj;

    invoke-direct {v3, p0, v1, p1}, Lcom/bytedance/sdk/component/adexpress/ex/JU$Fj;-><init>(Lcom/bytedance/sdk/component/adexpress/ex/JU;ILcom/bytedance/sdk/component/adexpress/ex/Ko$Fj;)V

    int-to-long v4, v0

    sget-object v0, Ljava/util/concurrent/TimeUnit;->MILLISECONDS:Ljava/util/concurrent/TimeUnit;

    invoke-interface {v2, v3, v4, v5, v0}, Ljava/util/concurrent/ScheduledExecutorService;->schedule(Ljava/lang/Runnable;JLjava/util/concurrent/TimeUnit;)Ljava/util/concurrent/ScheduledFuture;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/JU;->Ubf:Ljava/util/concurrent/ScheduledFuture;

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/JU;->ex:Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;

    new-instance v2, Lcom/bytedance/sdk/component/adexpress/ex/JU$1;

    invoke-direct {v2, p0, p1}, Lcom/bytedance/sdk/component/adexpress/ex/JU$1;-><init>(Lcom/bytedance/sdk/component/adexpress/ex/JU;Lcom/bytedance/sdk/component/adexpress/ex/Ko$Fj;)V

    invoke-virtual {v0, v2}, Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;->Fj(Lcom/bytedance/sdk/component/adexpress/ex/svN;)V

    :goto_0
    return v1
.end method

.method public ex()Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/JU;->ex:Lcom/bytedance/sdk/component/adexpress/Ubf/Fj;

    return-object v0
.end method
