.class public interface abstract Lcom/bytedance/sdk/component/adexpress/ex/JW;
.super Ljava/lang/Object;


# virtual methods
.method public abstract Fj(Lcom/bytedance/sdk/component/adexpress/ex/eV;Lcom/bytedance/sdk/component/adexpress/ex/Tc;)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/bytedance/sdk/component/adexpress/ex/eV<",
            "+",
            "Landroid/view/View;",
            ">;",
            "Lcom/bytedance/sdk/component/adexpress/ex/Tc;",
            ")V"
        }
    .end annotation
.end method

.method public abstract a_(I)V
.end method
