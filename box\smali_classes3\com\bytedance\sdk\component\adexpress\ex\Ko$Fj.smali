.class public interface abstract Lcom/bytedance/sdk/component/adexpress/ex/Ko$Fj;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bytedance/sdk/component/adexpress/ex/Ko;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "Fj"
.end annotation


# virtual methods
.method public abstract Fj()V
.end method

.method public abstract Fj(Lcom/bytedance/sdk/component/adexpress/ex/JW;)V
.end method

.method public abstract Fj(Lcom/bytedance/sdk/component/adexpress/ex/Ko;)V
.end method

.method public abstract Fj(Z)V
.end method

.method public abstract ex()Lcom/bytedance/sdk/component/adexpress/ex/JW;
.end method

.method public abstract ex(Lcom/bytedance/sdk/component/adexpress/ex/Ko;)Z
.end method

.method public abstract hjc()Z
.end method
