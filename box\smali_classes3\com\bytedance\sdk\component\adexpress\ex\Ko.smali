.class public interface abstract Lcom/bytedance/sdk/component/adexpress/ex/Ko;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/bytedance/sdk/component/adexpress/ex/Ko$Fj;
    }
.end annotation


# virtual methods
.method public abstract Fj()V
.end method

.method public abstract Fj(Lcom/bytedance/sdk/component/adexpress/ex/Ko$Fj;)Z
.end method
