.class public Lcom/bytedance/sdk/component/adexpress/ex/Tc;
.super Ljava/lang/Object;


# instance fields
.field private BcC:F

.field private Fj:I

.field private JU:Landroid/view/View;

.field private JW:Z

.field private Ko:F

.field private Tc:I

.field private UYd:D

.field private Ubf:D

.field private WR:D

.field private dG:Ljava/lang/String;

.field private eV:D

.field private ex:Z

.field private hjc:D

.field private mSE:F

.field private rAx:D

.field private svN:F


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public BcC()D
    .locals 2

    iget-wide v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/Tc;->rAx:D

    return-wide v0
.end method

.method public Fj()Landroid/view/View;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/Tc;->JU:Landroid/view/View;

    return-object v0
.end method

.method public Fj(D)V
    .locals 0

    iput-wide p1, p0, Lcom/bytedance/sdk/component/adexpress/ex/Tc;->hjc:D

    return-void
.end method

.method public Fj(F)V
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/component/adexpress/ex/Tc;->svN:F

    return-void
.end method

.method public Fj(I)V
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/component/adexpress/ex/Tc;->Fj:I

    return-void
.end method

.method public Fj(Landroid/view/View;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/adexpress/ex/Tc;->JU:Landroid/view/View;

    return-void
.end method

.method public Fj(Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/adexpress/ex/Tc;->dG:Ljava/lang/String;

    return-void
.end method

.method public Fj(Z)V
    .locals 0

    iput-boolean p1, p0, Lcom/bytedance/sdk/component/adexpress/ex/Tc;->ex:Z

    return-void
.end method

.method public JW()F
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/Tc;->Ko:F

    return v0
.end method

.method public Ko()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/Tc;->dG:Ljava/lang/String;

    return-object v0
.end method

.method public Tc()F
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/Tc;->mSE:F

    return v0
.end method

.method public UYd()F
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/Tc;->svN:F

    return v0
.end method

.method public Ubf()D
    .locals 2

    iget-wide v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/Tc;->eV:D

    return-wide v0
.end method

.method public Ubf(D)V
    .locals 0

    iput-wide p1, p0, Lcom/bytedance/sdk/component/adexpress/ex/Tc;->rAx:D

    return-void
.end method

.method public WR()D
    .locals 2

    iget-wide v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/Tc;->Ubf:D

    return-wide v0
.end method

.method public WR(D)V
    .locals 0

    iput-wide p1, p0, Lcom/bytedance/sdk/component/adexpress/ex/Tc;->UYd:D

    return-void
.end method

.method public dG()F
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/Tc;->BcC:F

    return v0
.end method

.method public eV()D
    .locals 2

    iget-wide v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/Tc;->hjc:D

    return-wide v0
.end method

.method public eV(D)V
    .locals 0

    iput-wide p1, p0, Lcom/bytedance/sdk/component/adexpress/ex/Tc;->WR:D

    return-void
.end method

.method public eV(F)V
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/component/adexpress/ex/Tc;->Ko:F

    return-void
.end method

.method public ex()I
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/Tc;->Fj:I

    return v0
.end method

.method public ex(D)V
    .locals 0

    iput-wide p1, p0, Lcom/bytedance/sdk/component/adexpress/ex/Tc;->eV:D

    return-void
.end method

.method public ex(F)V
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/component/adexpress/ex/Tc;->BcC:F

    return-void
.end method

.method public ex(I)V
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/component/adexpress/ex/Tc;->Tc:I

    return-void
.end method

.method public ex(Z)V
    .locals 1

    iget-boolean v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/Tc;->JW:Z

    if-eqz v0, :cond_0

    return-void

    :cond_0
    iput-boolean p1, p0, Lcom/bytedance/sdk/component/adexpress/ex/Tc;->JW:Z

    return-void
.end method

.method public hjc(D)V
    .locals 0

    iput-wide p1, p0, Lcom/bytedance/sdk/component/adexpress/ex/Tc;->Ubf:D

    return-void
.end method

.method public hjc(F)V
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/component/adexpress/ex/Tc;->mSE:F

    return-void
.end method

.method public hjc()Z
    .locals 1

    iget-boolean v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/Tc;->ex:Z

    return v0
.end method

.method public mSE()D
    .locals 2

    iget-wide v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/Tc;->UYd:D

    return-wide v0
.end method

.method public rAx()I
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/Tc;->Tc:I

    return v0
.end method

.method public svN()D
    .locals 2

    iget-wide v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/Tc;->WR:D

    return-wide v0
.end method
