.class public Lcom/bytedance/sdk/component/adexpress/ex/UYd;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/sdk/component/adexpress/ex/Ko$Fj;


# instance fields
.field Fj:Lcom/bytedance/sdk/component/adexpress/ex/JW;

.field private eV:Ljava/util/concurrent/atomic/AtomicBoolean;

.field private ex:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lcom/bytedance/sdk/component/adexpress/ex/Ko;",
            ">;"
        }
    .end annotation
.end field

.field private hjc:Lcom/bytedance/sdk/component/adexpress/ex/mSE;


# direct methods
.method public constructor <init>(Ljava/util/List;Lcom/bytedance/sdk/component/adexpress/ex/mSE;)V
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lcom/bytedance/sdk/component/adexpress/ex/Ko;",
            ">;",
            "Lcom/bytedance/sdk/component/adexpress/ex/mSE;",
            ")V"
        }
    .end annotation

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    new-instance v0, Ljava/util/concurrent/atomic/AtomicBoolean;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Ljava/util/concurrent/atomic/AtomicBoolean;-><init>(Z)V

    iput-object v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/UYd;->eV:Ljava/util/concurrent/atomic/AtomicBoolean;

    iput-object p1, p0, Lcom/bytedance/sdk/component/adexpress/ex/UYd;->ex:Ljava/util/List;

    iput-object p2, p0, Lcom/bytedance/sdk/component/adexpress/ex/UYd;->hjc:Lcom/bytedance/sdk/component/adexpress/ex/mSE;

    return-void
.end method


# virtual methods
.method public Fj()V
    .locals 2

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/UYd;->hjc:Lcom/bytedance/sdk/component/adexpress/ex/mSE;

    invoke-interface {v0}, Lcom/bytedance/sdk/component/adexpress/ex/mSE;->eV()V

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/UYd;->ex:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :cond_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_1

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/bytedance/sdk/component/adexpress/ex/Ko;

    invoke-interface {v1, p0}, Lcom/bytedance/sdk/component/adexpress/ex/Ko;->Fj(Lcom/bytedance/sdk/component/adexpress/ex/Ko$Fj;)Z

    move-result v1

    if-eqz v1, :cond_0

    :cond_1
    return-void
.end method

.method public Fj(Lcom/bytedance/sdk/component/adexpress/ex/JW;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/adexpress/ex/UYd;->Fj:Lcom/bytedance/sdk/component/adexpress/ex/JW;

    return-void
.end method

.method public Fj(Lcom/bytedance/sdk/component/adexpress/ex/Ko;)V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/UYd;->ex:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->indexOf(Ljava/lang/Object;)I

    move-result p1

    if-gez p1, :cond_0

    return-void

    :cond_0
    add-int/lit8 p1, p1, 0x1

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/UYd;->ex:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    if-ge p1, v0, :cond_1

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/UYd;->ex:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/bytedance/sdk/component/adexpress/ex/Ko;

    invoke-interface {v0, p0}, Lcom/bytedance/sdk/component/adexpress/ex/Ko;->Fj(Lcom/bytedance/sdk/component/adexpress/ex/Ko$Fj;)Z

    move-result v0

    if-eqz v0, :cond_0

    :cond_1
    return-void
.end method

.method public Fj(Z)V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/UYd;->eV:Ljava/util/concurrent/atomic/AtomicBoolean;

    invoke-virtual {v0, p1}, Ljava/util/concurrent/atomic/AtomicBoolean;->getAndSet(Z)Z

    return-void
.end method

.method public ex()Lcom/bytedance/sdk/component/adexpress/ex/JW;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/UYd;->Fj:Lcom/bytedance/sdk/component/adexpress/ex/JW;

    return-object v0
.end method

.method public ex(Lcom/bytedance/sdk/component/adexpress/ex/Ko;)Z
    .locals 2

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/UYd;->ex:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->indexOf(Ljava/lang/Object;)I

    move-result p1

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/UYd;->ex:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    const/4 v1, 0x1

    sub-int/2addr v0, v1

    if-ge p1, v0, :cond_0

    if-ltz p1, :cond_0

    return v1

    :cond_0
    const/4 p1, 0x0

    return p1
.end method

.method public hjc()Z
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/UYd;->eV:Ljava/util/concurrent/atomic/AtomicBoolean;

    invoke-virtual {v0}, Ljava/util/concurrent/atomic/AtomicBoolean;->get()Z

    move-result v0

    return v0
.end method
