.class Lcom/bytedance/sdk/component/adexpress/ex/WR$1;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/sdk/component/adexpress/ex/svN;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/component/adexpress/ex/WR;->Fj(Lcom/bytedance/sdk/component/adexpress/ex/Ko$Fj;)Z
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/component/adexpress/ex/Ko$Fj;

.field final synthetic ex:Lcom/bytedance/sdk/component/adexpress/ex/WR;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/component/adexpress/ex/WR;Lcom/bytedance/sdk/component/adexpress/ex/Ko$Fj;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/adexpress/ex/WR$1;->ex:Lcom/bytedance/sdk/component/adexpress/ex/WR;

    iput-object p2, p0, Lcom/bytedance/sdk/component/adexpress/ex/WR$1;->Fj:Lcom/bytedance/sdk/component/adexpress/ex/Ko$Fj;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public Fj(ILjava/lang/String;)V
    .locals 0

    iget-object p2, p0, Lcom/bytedance/sdk/component/adexpress/ex/WR$1;->Fj:Lcom/bytedance/sdk/component/adexpress/ex/Ko$Fj;

    invoke-interface {p2}, Lcom/bytedance/sdk/component/adexpress/ex/Ko$Fj;->ex()Lcom/bytedance/sdk/component/adexpress/ex/JW;

    move-result-object p2

    if-eqz p2, :cond_0

    invoke-interface {p2, p1}, Lcom/bytedance/sdk/component/adexpress/ex/JW;->a_(I)V

    :cond_0
    return-void
.end method

.method public Fj(Landroid/view/View;Lcom/bytedance/sdk/component/adexpress/ex/Tc;)V
    .locals 1

    iget-object p1, p0, Lcom/bytedance/sdk/component/adexpress/ex/WR$1;->Fj:Lcom/bytedance/sdk/component/adexpress/ex/Ko$Fj;

    invoke-interface {p1}, Lcom/bytedance/sdk/component/adexpress/ex/Ko$Fj;->hjc()Z

    move-result p1

    if-eqz p1, :cond_0

    return-void

    :cond_0
    iget-object p1, p0, Lcom/bytedance/sdk/component/adexpress/ex/WR$1;->Fj:Lcom/bytedance/sdk/component/adexpress/ex/Ko$Fj;

    invoke-interface {p1}, Lcom/bytedance/sdk/component/adexpress/ex/Ko$Fj;->ex()Lcom/bytedance/sdk/component/adexpress/ex/JW;

    move-result-object p1

    if-eqz p1, :cond_1

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/WR$1;->ex:Lcom/bytedance/sdk/component/adexpress/ex/WR;

    invoke-static {v0}, Lcom/bytedance/sdk/component/adexpress/ex/WR;->Fj(Lcom/bytedance/sdk/component/adexpress/ex/WR;)Lcom/bytedance/sdk/component/adexpress/ex/Fj;

    move-result-object v0

    invoke-interface {p1, v0, p2}, Lcom/bytedance/sdk/component/adexpress/ex/JW;->Fj(Lcom/bytedance/sdk/component/adexpress/ex/eV;Lcom/bytedance/sdk/component/adexpress/ex/Tc;)V

    :cond_1
    iget-object p1, p0, Lcom/bytedance/sdk/component/adexpress/ex/WR$1;->Fj:Lcom/bytedance/sdk/component/adexpress/ex/Ko$Fj;

    const/4 p2, 0x1

    invoke-interface {p1, p2}, Lcom/bytedance/sdk/component/adexpress/ex/Ko$Fj;->Fj(Z)V

    return-void
.end method
