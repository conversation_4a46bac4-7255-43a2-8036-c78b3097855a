.class public Lcom/bytedance/sdk/component/adexpress/ex/WR;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/sdk/component/adexpress/ex/Ko;


# instance fields
.field private Fj:Landroid/content/Context;

.field private ex:Lcom/bytedance/sdk/component/adexpress/ex/Fj;

.field private hjc:Lcom/bytedance/sdk/component/adexpress/ex/dG;


# direct methods
.method public constructor <init>(Landroid/content/Context;Lcom/bytedance/sdk/component/adexpress/ex/dG;Lcom/bytedance/sdk/component/adexpress/ex/Fj;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/bytedance/sdk/component/adexpress/ex/WR;->Fj:Landroid/content/Context;

    iput-object p3, p0, Lcom/bytedance/sdk/component/adexpress/ex/WR;->ex:Lcom/bytedance/sdk/component/adexpress/ex/Fj;

    iput-object p2, p0, Lcom/bytedance/sdk/component/adexpress/ex/WR;->hjc:Lcom/bytedance/sdk/component/adexpress/ex/dG;

    return-void
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/component/adexpress/ex/WR;)Lcom/bytedance/sdk/component/adexpress/ex/Fj;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/component/adexpress/ex/WR;->ex:Lcom/bytedance/sdk/component/adexpress/ex/Fj;

    return-object p0
.end method


# virtual methods
.method public Fj()V
    .locals 0

    return-void
.end method

.method public Fj(Lcom/bytedance/sdk/component/adexpress/ex/hjc;)V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/WR;->ex:Lcom/bytedance/sdk/component/adexpress/ex/Fj;

    invoke-virtual {v0, p1}, Lcom/bytedance/sdk/component/adexpress/ex/Fj;->Fj(Lcom/bytedance/sdk/component/adexpress/ex/hjc;)V

    return-void
.end method

.method public Fj(Lcom/bytedance/sdk/component/adexpress/ex/Ko$Fj;)Z
    .locals 2

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/WR;->hjc:Lcom/bytedance/sdk/component/adexpress/ex/dG;

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/adexpress/ex/dG;->Ubf()Lcom/bytedance/sdk/component/adexpress/ex/mSE;

    move-result-object v0

    invoke-interface {v0}, Lcom/bytedance/sdk/component/adexpress/ex/mSE;->WR()V

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/WR;->ex:Lcom/bytedance/sdk/component/adexpress/ex/Fj;

    new-instance v1, Lcom/bytedance/sdk/component/adexpress/ex/WR$1;

    invoke-direct {v1, p0, p1}, Lcom/bytedance/sdk/component/adexpress/ex/WR$1;-><init>(Lcom/bytedance/sdk/component/adexpress/ex/WR;Lcom/bytedance/sdk/component/adexpress/ex/Ko$Fj;)V

    invoke-interface {v0, v1}, Lcom/bytedance/sdk/component/adexpress/ex/eV;->Fj(Lcom/bytedance/sdk/component/adexpress/ex/svN;)V

    const/4 p1, 0x1

    return p1
.end method
