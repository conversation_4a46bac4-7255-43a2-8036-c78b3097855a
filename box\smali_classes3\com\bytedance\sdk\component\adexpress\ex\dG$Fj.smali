.class public Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bytedance/sdk/component/adexpress/ex/dG;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "Fj"
.end annotation


# instance fields
.field private Af:I

.field private BcC:Ljava/lang/String;

.field private Fj:Lorg/json/JSONObject;

.field private JU:Z

.field private JW:I

.field private Ko:I

.field private Moo:Z

.field private Ql:Ljava/lang/String;

.field private Tc:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field private UYd:I

.field private Ubf:I

.field private Vq:I

.field private WR:Ljava/lang/String;

.field private cB:Ljava/lang/String;

.field private dG:Ljava/lang/String;

.field private eV:Lcom/bytedance/sdk/component/adexpress/ex/mSE;

.field private ex:Lcom/bytedance/sdk/component/adexpress/ex/Ubf;

.field private hjc:Ljava/lang/String;

.field private lv:Ljava/lang/String;

.field private mC:I

.field private mE:I

.field private mSE:Z

.field private nsB:D

.field private rAx:J

.field private rS:I

.field private rf:Lorg/json/JSONObject;

.field private svN:Ljava/lang/String;

.field private uy:Z

.field private vYf:I


# direct methods
.method public constructor <init>()V
    .locals 1

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, 0x1

    iput-boolean v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;->Moo:Z

    return-void
.end method

.method public static synthetic Af(Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;)I
    .locals 0

    iget p0, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;->Af:I

    return p0
.end method

.method public static synthetic BcC(Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;)Ljava/lang/String;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;->BcC:Ljava/lang/String;

    return-object p0
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;)Lorg/json/JSONObject;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;->Fj:Lorg/json/JSONObject;

    return-object p0
.end method

.method public static synthetic JU(Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;)Z
    .locals 0

    iget-boolean p0, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;->JU:Z

    return p0
.end method

.method public static synthetic JW(Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;)I
    .locals 0

    iget p0, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;->JW:I

    return p0
.end method

.method public static synthetic Ko(Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;)I
    .locals 0

    iget p0, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;->Ko:I

    return p0
.end method

.method public static synthetic Moo(Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;)Z
    .locals 0

    iget-boolean p0, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;->Moo:Z

    return p0
.end method

.method public static synthetic Ql(Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;)Ljava/lang/String;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;->Ql:Ljava/lang/String;

    return-object p0
.end method

.method public static synthetic Tc(Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;)Ljava/util/Map;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;->Tc:Ljava/util/Map;

    return-object p0
.end method

.method public static synthetic UYd(Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;)I
    .locals 0

    iget p0, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;->UYd:I

    return p0
.end method

.method public static synthetic Ubf(Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;)I
    .locals 0

    iget p0, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;->Ubf:I

    return p0
.end method

.method public static synthetic Vq(Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;)I
    .locals 0

    iget p0, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;->Vq:I

    return p0
.end method

.method public static synthetic WR(Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;)Ljava/lang/String;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;->WR:Ljava/lang/String;

    return-object p0
.end method

.method public static synthetic cB(Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;)Ljava/lang/String;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;->cB:Ljava/lang/String;

    return-object p0
.end method

.method public static synthetic dG(Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;)Ljava/lang/String;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;->dG:Ljava/lang/String;

    return-object p0
.end method

.method public static synthetic eV(Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;)Lcom/bytedance/sdk/component/adexpress/ex/mSE;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;->eV:Lcom/bytedance/sdk/component/adexpress/ex/mSE;

    return-object p0
.end method

.method public static synthetic ex(Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;)Lcom/bytedance/sdk/component/adexpress/ex/Ubf;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;->ex:Lcom/bytedance/sdk/component/adexpress/ex/Ubf;

    return-object p0
.end method

.method public static synthetic hjc(Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;)Ljava/lang/String;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;->hjc:Ljava/lang/String;

    return-object p0
.end method

.method public static synthetic lv(Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;)Ljava/lang/String;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;->lv:Ljava/lang/String;

    return-object p0
.end method

.method public static synthetic mC(Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;)I
    .locals 0

    iget p0, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;->mC:I

    return p0
.end method

.method public static synthetic mE(Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;)I
    .locals 0

    iget p0, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;->mE:I

    return p0
.end method

.method public static synthetic mSE(Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;)Z
    .locals 0

    iget-boolean p0, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;->mSE:Z

    return p0
.end method

.method public static synthetic nsB(Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;)D
    .locals 2

    iget-wide v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;->nsB:D

    return-wide v0
.end method

.method public static synthetic rAx(Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;)J
    .locals 2

    iget-wide v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;->rAx:J

    return-wide v0
.end method

.method public static synthetic rS(Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;)I
    .locals 0

    iget p0, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;->rS:I

    return p0
.end method

.method public static synthetic rf(Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;)Lorg/json/JSONObject;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;->rf:Lorg/json/JSONObject;

    return-object p0
.end method

.method public static synthetic svN(Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;)Ljava/lang/String;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;->svN:Ljava/lang/String;

    return-object p0
.end method

.method public static synthetic uy(Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;)Z
    .locals 0

    iget-boolean p0, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;->uy:Z

    return p0
.end method

.method public static synthetic vYf(Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;)I
    .locals 0

    iget p0, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;->vYf:I

    return p0
.end method


# virtual methods
.method public Fj(D)Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;
    .locals 0

    iput-wide p1, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;->nsB:D

    return-object p0
.end method

.method public Fj(I)Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;->Ubf:I

    return-object p0
.end method

.method public Fj(J)Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;
    .locals 0

    iput-wide p1, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;->rAx:J

    return-object p0
.end method

.method public Fj(Lcom/bytedance/sdk/component/adexpress/ex/Ubf;)Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;->ex:Lcom/bytedance/sdk/component/adexpress/ex/Ubf;

    return-object p0
.end method

.method public Fj(Lcom/bytedance/sdk/component/adexpress/ex/mSE;)Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;->eV:Lcom/bytedance/sdk/component/adexpress/ex/mSE;

    return-object p0
.end method

.method public Fj(Ljava/lang/String;)Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;->hjc:Ljava/lang/String;

    return-object p0
.end method

.method public Fj(Ljava/util/Map;)Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ">;)",
            "Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;"
        }
    .end annotation

    iput-object p1, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;->Tc:Ljava/util/Map;

    return-object p0
.end method

.method public Fj(Z)Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;
    .locals 0

    iput-boolean p1, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;->Moo:Z

    return-object p0
.end method

.method public Fj()Lcom/bytedance/sdk/component/adexpress/ex/dG;
    .locals 1

    new-instance v0, Lcom/bytedance/sdk/component/adexpress/ex/dG;

    invoke-direct {v0, p0}, Lcom/bytedance/sdk/component/adexpress/ex/dG;-><init>(Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;)V

    return-object v0
.end method

.method public Ubf(I)Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;->Vq:I

    return-object p0
.end method

.method public Ubf(Ljava/lang/String;)Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;->Ql:Ljava/lang/String;

    return-object p0
.end method

.method public WR(Ljava/lang/String;)Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;->lv:Ljava/lang/String;

    return-object p0
.end method

.method public eV(I)Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;->JW:I

    return-object p0
.end method

.method public eV(Ljava/lang/String;)Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;->BcC:Ljava/lang/String;

    return-object p0
.end method

.method public eV(Z)Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;
    .locals 0

    iput-boolean p1, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;->uy:Z

    return-object p0
.end method

.method public ex(I)Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;->Ko:I

    return-object p0
.end method

.method public ex(Ljava/lang/String;)Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;->WR:Ljava/lang/String;

    return-object p0
.end method

.method public ex(Z)Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;
    .locals 0

    iput-boolean p1, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;->mSE:Z

    return-object p0
.end method

.method public hjc(I)Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;
    .locals 0

    iput p1, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;->UYd:I

    return-object p0
.end method

.method public hjc(Ljava/lang/String;)Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;->svN:Ljava/lang/String;

    return-object p0
.end method

.method public hjc(Z)Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;
    .locals 0

    iput-boolean p1, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;->JU:Z

    return-object p0
.end method
