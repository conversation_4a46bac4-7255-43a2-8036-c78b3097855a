.class public Lcom/bytedance/sdk/component/adexpress/ex/dG;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;
    }
.end annotation


# instance fields
.field private Af:I

.field private BcC:Ljava/lang/String;

.field private Fj:Lorg/json/JSONObject;

.field private JU:Z

.field private JW:I

.field private Ko:I

.field private Moo:Z

.field private Ql:Ljava/lang/String;

.field private Tc:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field private UYd:I

.field private Ubf:I

.field private Vq:I

.field private WR:Ljava/lang/String;

.field private cB:Ljava/lang/String;

.field private dG:Ljava/lang/String;

.field private eV:Lcom/bytedance/sdk/component/adexpress/ex/mSE;

.field private ex:Lcom/bytedance/sdk/component/adexpress/ex/Ubf;

.field private hjc:Ljava/lang/String;

.field private lv:Ljava/lang/String;

.field private mC:I

.field private mE:I

.field private mSE:Z

.field private nsB:D

.field private rAx:J

.field private rS:I

.field private rf:Lorg/json/JSONObject;

.field private svN:Ljava/lang/String;

.field private uy:Z

.field private vYf:I


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;)V
    .locals 2

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    invoke-static {p1}, Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;->Fj(Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;)Lorg/json/JSONObject;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG;->Fj:Lorg/json/JSONObject;

    invoke-static {p1}, Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;->ex(Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;)Lcom/bytedance/sdk/component/adexpress/ex/Ubf;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG;->ex:Lcom/bytedance/sdk/component/adexpress/ex/Ubf;

    invoke-static {p1}, Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;->hjc(Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;)Ljava/lang/String;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG;->hjc:Ljava/lang/String;

    invoke-static {p1}, Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;->eV(Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;)Lcom/bytedance/sdk/component/adexpress/ex/mSE;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG;->eV:Lcom/bytedance/sdk/component/adexpress/ex/mSE;

    invoke-static {p1}, Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;->Ubf(Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;)I

    move-result v0

    iput v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG;->Ubf:I

    invoke-static {p1}, Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;->WR(Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;)Ljava/lang/String;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG;->WR:Ljava/lang/String;

    invoke-static {p1}, Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;->svN(Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;)Ljava/lang/String;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG;->svN:Ljava/lang/String;

    invoke-static {p1}, Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;->BcC(Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;)Ljava/lang/String;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG;->BcC:Ljava/lang/String;

    invoke-static {p1}, Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;->mSE(Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;)Z

    move-result v0

    iput-boolean v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG;->mSE:Z

    invoke-static {p1}, Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;->Ko(Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;)I

    move-result v0

    iput v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG;->Ko:I

    invoke-static {p1}, Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;->rAx(Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;)J

    move-result-wide v0

    iput-wide v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG;->rAx:J

    invoke-static {p1}, Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;->UYd(Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;)I

    move-result v0

    iput v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG;->UYd:I

    invoke-static {p1}, Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;->dG(Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;)Ljava/lang/String;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG;->dG:Ljava/lang/String;

    invoke-static {p1}, Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;->Tc(Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;)Ljava/util/Map;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG;->Tc:Ljava/util/Map;

    invoke-static {p1}, Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;->JW(Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;)I

    move-result v0

    iput v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG;->JW:I

    invoke-static {p1}, Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;->JU(Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;)Z

    move-result v0

    iput-boolean v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG;->JU:Z

    invoke-static {p1}, Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;->Ql(Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;)Ljava/lang/String;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG;->Ql:Ljava/lang/String;

    invoke-static {p1}, Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;->rS(Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;)I

    move-result v0

    iput v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG;->rS:I

    invoke-static {p1}, Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;->vYf(Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;)I

    move-result v0

    iput v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG;->vYf:I

    invoke-static {p1}, Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;->mE(Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;)I

    move-result v0

    iput v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG;->mE:I

    invoke-static {p1}, Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;->Af(Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;)I

    move-result v0

    iput v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG;->Af:I

    invoke-static {p1}, Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;->mC(Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;)I

    move-result v0

    iput v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG;->mC:I

    invoke-static {p1}, Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;->cB(Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;)Ljava/lang/String;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG;->cB:Ljava/lang/String;

    invoke-static {p1}, Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;->nsB(Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;)D

    move-result-wide v0

    iput-wide v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG;->nsB:D

    invoke-static {p1}, Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;->Vq(Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;)I

    move-result v0

    iput v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG;->Vq:I

    invoke-static {p1}, Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;->Moo(Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;)Z

    move-result v0

    iput-boolean v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG;->Moo:Z

    invoke-static {p1}, Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;->rf(Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;)Lorg/json/JSONObject;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG;->rf:Lorg/json/JSONObject;

    invoke-static {p1}, Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;->uy(Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;)Z

    move-result v0

    iput-boolean v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG;->uy:Z

    invoke-static {p1}, Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;->lv(Lcom/bytedance/sdk/component/adexpress/ex/dG$Fj;)Ljava/lang/String;

    move-result-object p1

    iput-object p1, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG;->lv:Ljava/lang/String;

    return-void
.end method


# virtual methods
.method public Af()Z
    .locals 1

    iget-boolean v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG;->uy:Z

    return v0
.end method

.method public BcC()Z
    .locals 1

    iget-boolean v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG;->mSE:Z

    return v0
.end method

.method public Fj()Z
    .locals 1

    iget-boolean v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG;->Moo:Z

    return v0
.end method

.method public JU()I
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG;->vYf:I

    return v0
.end method

.method public JW()I
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG;->rS:I

    return v0
.end method

.method public Ko()I
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG;->UYd:I

    return v0
.end method

.method public Ql()I
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG;->mE:I

    return v0
.end method

.method public Tc()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG;->Ql:Ljava/lang/String;

    return-object v0
.end method

.method public UYd()I
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG;->JW:I

    return v0
.end method

.method public Ubf()Lcom/bytedance/sdk/component/adexpress/ex/mSE;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG;->eV:Lcom/bytedance/sdk/component/adexpress/ex/mSE;

    return-object v0
.end method

.method public WR()I
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG;->Ubf:I

    return v0
.end method

.method public dG()Z
    .locals 1

    iget-boolean v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG;->JU:Z

    return v0
.end method

.method public eV()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG;->hjc:Ljava/lang/String;

    return-object v0
.end method

.method public ex()D
    .locals 2

    iget-wide v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG;->nsB:D

    return-wide v0
.end method

.method public hjc()Lorg/json/JSONObject;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG;->Fj:Lorg/json/JSONObject;

    if-nez v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG;->ex:Lcom/bytedance/sdk/component/adexpress/ex/Ubf;

    if-eqz v0, :cond_0

    invoke-interface {v0}, Lcom/bytedance/sdk/component/adexpress/ex/Ubf;->Fj()Lorg/json/JSONObject;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG;->Fj:Lorg/json/JSONObject;

    :cond_0
    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG;->Fj:Lorg/json/JSONObject;

    return-object v0
.end method

.method public mC()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG;->lv:Ljava/lang/String;

    return-object v0
.end method

.method public mE()I
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG;->mC:I

    return v0
.end method

.method public mSE()J
    .locals 2

    iget-wide v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG;->rAx:J

    return-wide v0
.end method

.method public rAx()Ljava/util/Map;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG;->Tc:Ljava/util/Map;

    return-object v0
.end method

.method public rS()Lorg/json/JSONObject;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG;->rf:Lorg/json/JSONObject;

    return-object v0
.end method

.method public svN()I
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG;->Vq:I

    return v0
.end method

.method public vYf()I
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/dG;->Af:I

    return v0
.end method
