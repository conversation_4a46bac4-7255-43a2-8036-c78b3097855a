.class public interface abstract Lcom/bytedance/sdk/component/adexpress/ex/eV;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "<T:",
        "Landroid/view/View;",
        ">",
        "Ljava/lang/Object;"
    }
.end annotation


# virtual methods
.method public abstract Fj(Lcom/bytedance/sdk/component/adexpress/ex/svN;)V
.end method

.method public abstract Ubf()Landroid/view/View;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()TT;"
        }
    .end annotation
.end method

.method public abstract hjc()I
.end method
