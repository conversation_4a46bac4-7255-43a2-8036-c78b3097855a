.class public Lcom/bytedance/sdk/component/adexpress/ex/ex;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/sdk/component/adexpress/ex/Ko;


# instance fields
.field private Fj:Landroid/content/Context;

.field private Ubf:Lcom/bytedance/sdk/component/adexpress/ex/dG;

.field private WR:I

.field private eV:Lcom/bytedance/sdk/component/adexpress/ex/BcC;

.field private ex:Lcom/bytedance/sdk/component/adexpress/dynamic/Fj/Fj;

.field private hjc:Lcom/bytedance/sdk/component/adexpress/theme/ThemeStatusBroadcastReceiver;


# direct methods
.method public constructor <init>(Landroid/content/Context;Lcom/bytedance/sdk/component/adexpress/ex/dG;Lcom/bytedance/sdk/component/adexpress/theme/ThemeStatusBroadcastReceiver;ZLcom/bytedance/sdk/component/adexpress/dynamic/eV/BcC;Lcom/bytedance/sdk/component/adexpress/ex/BcC;Lcom/bytedance/sdk/component/adexpress/dynamic/Ubf/Fj;Lcom/bytedance/sdk/component/adexpress/dynamic/Fj/Fj;)V
    .locals 7

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/bytedance/sdk/component/adexpress/ex/ex;->Fj:Landroid/content/Context;

    iput-object p2, p0, Lcom/bytedance/sdk/component/adexpress/ex/ex;->Ubf:Lcom/bytedance/sdk/component/adexpress/ex/dG;

    iput-object p3, p0, Lcom/bytedance/sdk/component/adexpress/ex/ex;->hjc:Lcom/bytedance/sdk/component/adexpress/theme/ThemeStatusBroadcastReceiver;

    iput-object p6, p0, Lcom/bytedance/sdk/component/adexpress/ex/ex;->eV:Lcom/bytedance/sdk/component/adexpress/ex/BcC;

    if-eqz p8, :cond_0

    iput-object p8, p0, Lcom/bytedance/sdk/component/adexpress/ex/ex;->ex:Lcom/bytedance/sdk/component/adexpress/dynamic/Fj/Fj;

    goto :goto_0

    :cond_0
    new-instance p6, Lcom/bytedance/sdk/component/adexpress/dynamic/Fj/Fj;

    move-object v0, p6

    move-object v1, p1

    move-object v2, p3

    move v3, p4

    move-object v4, p5

    move-object v5, p2

    move-object v6, p7

    invoke-direct/range {v0 .. v6}, Lcom/bytedance/sdk/component/adexpress/dynamic/Fj/Fj;-><init>(Landroid/content/Context;Lcom/bytedance/sdk/component/adexpress/theme/ThemeStatusBroadcastReceiver;ZLcom/bytedance/sdk/component/adexpress/dynamic/eV/BcC;Lcom/bytedance/sdk/component/adexpress/ex/dG;Lcom/bytedance/sdk/component/adexpress/dynamic/Ubf/Fj;)V

    iput-object p6, p0, Lcom/bytedance/sdk/component/adexpress/ex/ex;->ex:Lcom/bytedance/sdk/component/adexpress/dynamic/Fj/Fj;

    :goto_0
    iget-object p1, p0, Lcom/bytedance/sdk/component/adexpress/ex/ex;->ex:Lcom/bytedance/sdk/component/adexpress/dynamic/Fj/Fj;

    iget-object p2, p0, Lcom/bytedance/sdk/component/adexpress/ex/ex;->eV:Lcom/bytedance/sdk/component/adexpress/ex/BcC;

    invoke-virtual {p1, p2}, Lcom/bytedance/sdk/component/adexpress/dynamic/Fj/Fj;->Fj(Lcom/bytedance/sdk/component/adexpress/ex/BcC;)V

    instance-of p1, p5, Lcom/bytedance/sdk/component/adexpress/dynamic/eV/svN;

    if-eqz p1, :cond_1

    const/4 p1, 0x3

    iput p1, p0, Lcom/bytedance/sdk/component/adexpress/ex/ex;->WR:I

    return-void

    :cond_1
    const/4 p1, 0x2

    iput p1, p0, Lcom/bytedance/sdk/component/adexpress/ex/ex;->WR:I

    return-void
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/component/adexpress/ex/ex;)I
    .locals 0

    iget p0, p0, Lcom/bytedance/sdk/component/adexpress/ex/ex;->WR:I

    return p0
.end method

.method public static synthetic ex(Lcom/bytedance/sdk/component/adexpress/ex/ex;)Lcom/bytedance/sdk/component/adexpress/ex/dG;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/component/adexpress/ex/ex;->Ubf:Lcom/bytedance/sdk/component/adexpress/ex/dG;

    return-object p0
.end method

.method public static synthetic hjc(Lcom/bytedance/sdk/component/adexpress/ex/ex;)Lcom/bytedance/sdk/component/adexpress/dynamic/Fj/Fj;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/component/adexpress/ex/ex;->ex:Lcom/bytedance/sdk/component/adexpress/dynamic/Fj/Fj;

    return-object p0
.end method


# virtual methods
.method public Fj()V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/ex;->ex:Lcom/bytedance/sdk/component/adexpress/dynamic/Fj/Fj;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/adexpress/dynamic/Fj/Fj;->ex()V

    :cond_0
    return-void
.end method

.method public Fj(Lcom/bytedance/sdk/component/adexpress/ex/Ko$Fj;)Z
    .locals 2

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/ex;->Ubf:Lcom/bytedance/sdk/component/adexpress/ex/dG;

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/adexpress/ex/dG;->Ubf()Lcom/bytedance/sdk/component/adexpress/ex/mSE;

    move-result-object v0

    iget v1, p0, Lcom/bytedance/sdk/component/adexpress/ex/ex;->WR:I

    invoke-interface {v0, v1}, Lcom/bytedance/sdk/component/adexpress/ex/mSE;->Fj(I)V

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/ex;->ex:Lcom/bytedance/sdk/component/adexpress/dynamic/Fj/Fj;

    new-instance v1, Lcom/bytedance/sdk/component/adexpress/ex/ex$1;

    invoke-direct {v1, p0, p1}, Lcom/bytedance/sdk/component/adexpress/ex/ex$1;-><init>(Lcom/bytedance/sdk/component/adexpress/ex/ex;Lcom/bytedance/sdk/component/adexpress/ex/Ko$Fj;)V

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/component/adexpress/dynamic/Fj/Fj;->Fj(Lcom/bytedance/sdk/component/adexpress/ex/svN;)V

    const/4 p1, 0x1

    return p1
.end method

.method public ex()Lcom/bytedance/sdk/component/adexpress/dynamic/eV;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/adexpress/ex/ex;->ex:Lcom/bytedance/sdk/component/adexpress/dynamic/Fj/Fj;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/adexpress/dynamic/Fj/Fj;->eV()Lcom/bytedance/sdk/component/adexpress/dynamic/dynamicview/DynamicRootView;

    move-result-object v0

    return-object v0

    :cond_0
    const/4 v0, 0x0

    return-object v0
.end method
