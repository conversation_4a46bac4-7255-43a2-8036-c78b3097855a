.class public interface abstract Lcom/bytedance/sdk/component/eV/BcC;
.super Ljava/lang/Object;


# annotations
.annotation runtime Lcom/bytedance/sdk/openadsdk/ats/ATSApi;
.end annotation


# virtual methods
.method public abstract Fj(Landroid/graphics/Bitmap;)Landroid/graphics/Bitmap;
    .annotation runtime Lcom/bytedance/sdk/openadsdk/ats/ATSMethod;
        value = 0x1
    .end annotation
.end method
