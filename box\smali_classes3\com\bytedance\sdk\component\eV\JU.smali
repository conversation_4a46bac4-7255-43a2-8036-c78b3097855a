.class public interface abstract Lcom/bytedance/sdk/component/eV/JU;
.super Ljava/lang/Object;


# annotations
.annotation runtime Lcom/bytedance/sdk/openadsdk/ats/ATSApi;
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "<T:",
        "Ljava/lang/Object;",
        ">",
        "Ljava/lang/Object;"
    }
.end annotation


# virtual methods
.method public abstract Fj(ILjava/lang/String;Ljava/lang/Throwable;)V
    .annotation runtime Lcom/bytedance/sdk/openadsdk/ats/ATSMethod;
        value = 0x2
    .end annotation
.end method

.method public abstract Fj(Lcom/bytedance/sdk/component/eV/rAx;)V
    .annotation runtime Lcom/bytedance/sdk/openadsdk/ats/ATSMethod;
        value = 0x1
    .end annotation

    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/bytedance/sdk/component/eV/rAx<",
            "TT;>;)V"
        }
    .end annotation
.end method
