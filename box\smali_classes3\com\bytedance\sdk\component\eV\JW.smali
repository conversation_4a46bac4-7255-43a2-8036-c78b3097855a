.class public interface abstract Lcom/bytedance/sdk/component/eV/JW;
.super Ljava/lang/Object;


# annotations
.annotation runtime Lcom/bytedance/sdk/openadsdk/ats/ATSApi;
    value = "img_service"
.end annotation


# virtual methods
.method public abstract Fj(Ljava/lang/String;)Lcom/bytedance/sdk/component/eV/Ko;
    .annotation runtime Lcom/bytedance/sdk/openadsdk/ats/ATSMethod;
        value = 0x1
    .end annotation
.end method

.method public abstract Fj(Ljava/lang/String;Ljava/lang/String;)Ljava/io/InputStream;
    .annotation runtime Lcom/bytedance/sdk/openadsdk/ats/ATSMethod;
        value = 0x6
    .end annotation
.end method

.method public abstract Fj(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Z
    .annotation runtime Lcom/bytedance/sdk/openadsdk/ats/ATSMethod;
        value = 0x8
    .end annotation
.end method
