.class public interface abstract Lcom/bytedance/sdk/component/eV/Ko;
.super Ljava/lang/Object;


# annotations
.annotation runtime Lcom/bytedance/sdk/openadsdk/ats/ATSApi;
.end annotation


# virtual methods
.method public abstract Fj(I)Lcom/bytedance/sdk/component/eV/Ko;
    .annotation runtime Lcom/bytedance/sdk/openadsdk/ats/ATSMethod;
        value = 0xa
    .end annotation
.end method

.method public abstract Fj(Landroid/graphics/Bitmap$Config;)Lcom/bytedance/sdk/component/eV/Ko;
    .annotation runtime Lcom/bytedance/sdk/openadsdk/ats/ATSMethod;
        value = 0x9
    .end annotation
.end method

.method public abstract Fj(Landroid/widget/ImageView$ScaleType;)Lcom/bytedance/sdk/component/eV/Ko;
    .annotation runtime Lcom/bytedance/sdk/openadsdk/ats/ATSMethod;
        value = 0x8
    .end annotation
.end method

.method public abstract Fj(Lcom/bytedance/sdk/component/eV/BcC;)Lcom/bytedance/sdk/component/eV/Ko;
    .annotation runtime Lcom/bytedance/sdk/openadsdk/ats/ATSMethod;
        value = 0x11
    .end annotation
.end method

.method public abstract Fj(Lcom/bytedance/sdk/component/eV/mE;)Lcom/bytedance/sdk/component/eV/Ko;
    .annotation runtime Lcom/bytedance/sdk/openadsdk/ats/ATSMethod;
        value = 0xd
    .end annotation
.end method

.method public abstract Fj(Ljava/lang/String;)Lcom/bytedance/sdk/component/eV/Ko;
    .annotation runtime Lcom/bytedance/sdk/openadsdk/ats/ATSMethod;
        value = 0x5
    .end annotation
.end method

.method public abstract Fj(Z)Lcom/bytedance/sdk/component/eV/Ko;
    .annotation runtime Lcom/bytedance/sdk/openadsdk/ats/ATSMethod;
        value = 0xf
    .end annotation
.end method

.method public abstract Fj(Landroid/widget/ImageView;)Lcom/bytedance/sdk/component/eV/mSE;
    .annotation runtime Lcom/bytedance/sdk/openadsdk/ats/ATSMethod;
        value = 0x3
    .end annotation
.end method

.method public abstract Fj(Lcom/bytedance/sdk/component/eV/JU;)Lcom/bytedance/sdk/component/eV/mSE;
    .annotation runtime Lcom/bytedance/sdk/openadsdk/ats/ATSMethod;
        value = 0x2
    .end annotation
.end method

.method public abstract Fj(Lcom/bytedance/sdk/component/eV/JU;I)Lcom/bytedance/sdk/component/eV/mSE;
    .annotation runtime Lcom/bytedance/sdk/openadsdk/ats/ATSMethod;
        value = 0x1
    .end annotation
.end method

.method public abstract Ubf(I)Lcom/bytedance/sdk/component/eV/Ko;
    .annotation runtime Lcom/bytedance/sdk/openadsdk/ats/ATSMethod;
        value = 0x13
    .end annotation
.end method

.method public abstract eV(I)Lcom/bytedance/sdk/component/eV/Ko;
    .annotation runtime Lcom/bytedance/sdk/openadsdk/ats/ATSMethod;
        value = 0x12
    .end annotation
.end method

.method public abstract ex(I)Lcom/bytedance/sdk/component/eV/Ko;
    .annotation runtime Lcom/bytedance/sdk/openadsdk/ats/ATSMethod;
        value = 0xb
    .end annotation
.end method

.method public abstract ex(Ljava/lang/String;)Lcom/bytedance/sdk/component/eV/Ko;
    .annotation runtime Lcom/bytedance/sdk/openadsdk/ats/ATSMethod;
        value = 0x7
    .end annotation
.end method

.method public abstract hjc(I)Lcom/bytedance/sdk/component/eV/Ko;
    .annotation runtime Lcom/bytedance/sdk/openadsdk/ats/ATSMethod;
        value = 0xc
    .end annotation
.end method
