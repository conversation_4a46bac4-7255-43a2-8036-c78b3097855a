.class public interface abstract Lcom/bytedance/sdk/component/eV/dG;
.super Ljava/lang/Object;


# virtual methods
.method public abstract BcC()Lcom/bytedance/sdk/component/eV/ex;
.end method

.method public abstract Fj()Lcom/bytedance/sdk/component/eV/UYd;
.end method

.method public abstract Ubf()Lcom/bytedance/sdk/component/eV/vYf;
.end method

.method public abstract WR()Lcom/bytedance/sdk/component/eV/hjc;
.end method

.method public abstract eV()Lcom/bytedance/sdk/component/eV/rS;
.end method

.method public abstract ex()Ljava/util/concurrent/ExecutorService;
.end method

.method public abstract hjc()Lcom/bytedance/sdk/component/eV/eV;
.end method

.method public abstract svN()Lcom/bytedance/sdk/component/eV/Ql;
.end method
