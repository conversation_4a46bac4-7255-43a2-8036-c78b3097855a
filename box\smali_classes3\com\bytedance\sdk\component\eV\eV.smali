.class public interface abstract Lcom/bytedance/sdk/component/eV/eV;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "<T::",
        "Lcom/bytedance/sdk/component/eV/WR;",
        ">",
        "Ljava/lang/Object;"
    }
.end annotation


# virtual methods
.method public abstract Fj(Lcom/bytedance/sdk/component/eV/Ubf;)Lcom/bytedance/sdk/component/eV/WR;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/bytedance/sdk/component/eV/Ubf;",
            ")TT;"
        }
    .end annotation
.end method
