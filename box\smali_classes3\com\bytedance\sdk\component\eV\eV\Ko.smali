.class public Lcom/bytedance/sdk/component/eV/eV/Ko;
.super Lcom/bytedance/sdk/component/eV/eV/Fj;


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Lcom/bytedance/sdk/component/eV/eV/Fj;-><init>()V

    return-void
.end method

.method private ex(Lcom/bytedance/sdk/component/eV/hjc/hjc;)Landroid/graphics/Bitmap;
    .locals 3

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/eV/hjc/hjc;->vYf()Lcom/bytedance/sdk/component/eV/hjc/WR;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/eV/hjc/WR;->Fj()Ljava/util/Collection;

    move-result-object v0

    const/4 v1, 0x0

    if-nez v0, :cond_0

    return-object v1

    :cond_0
    invoke-interface {v0}, Ljava/util/Collection;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :cond_1
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_2

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/bytedance/sdk/component/eV/rS;

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/eV/hjc/hjc;->Ubf()Ljava/lang/String;

    move-result-object v2

    invoke-interface {v1, v2}, Lcom/bytedance/sdk/component/eV/Fj;->Fj(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Landroid/graphics/Bitmap;

    if-eqz v1, :cond_1

    :cond_2
    return-object v1
.end method

.method private hjc(Lcom/bytedance/sdk/component/eV/hjc/hjc;)Landroid/graphics/Bitmap;
    .locals 2

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/eV/hjc/hjc;->vYf()Lcom/bytedance/sdk/component/eV/hjc/WR;

    move-result-object v0

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/eV/hjc/hjc;->mE()Lcom/bytedance/sdk/component/eV/ex;

    move-result-object v1

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/component/eV/hjc/WR;->Fj(Lcom/bytedance/sdk/component/eV/ex;)Lcom/bytedance/sdk/component/eV/rS;

    move-result-object v0

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/eV/hjc/hjc;->Ubf()Ljava/lang/String;

    move-result-object p1

    invoke-interface {v0, p1}, Lcom/bytedance/sdk/component/eV/Fj;->Fj(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Landroid/graphics/Bitmap;

    return-object p1
.end method


# virtual methods
.method public Fj()Ljava/lang/String;
    .locals 1

    const-string v0, "memory_cache"

    return-object v0
.end method

.method public Fj(Lcom/bytedance/sdk/component/eV/hjc/hjc;)V
    .locals 4

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/eV/hjc/hjc;->UYd()I

    move-result v0

    const/4 v1, 0x2

    const/4 v2, 0x0

    if-eq v0, v1, :cond_1

    const/4 v1, 0x1

    if-ne v0, v1, :cond_0

    goto :goto_0

    :cond_0
    move-object v0, v2

    goto :goto_2

    :cond_1
    :goto_0
    invoke-virtual {p1}, Lcom/bytedance/sdk/component/eV/hjc/hjc;->Af()Z

    move-result v0

    if-nez v0, :cond_3

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/eV/hjc/hjc;->mE()Lcom/bytedance/sdk/component/eV/ex;

    move-result-object v0

    invoke-interface {v0}, Lcom/bytedance/sdk/component/eV/ex;->WR()Z

    move-result v0

    if-eqz v0, :cond_2

    goto :goto_1

    :cond_2
    invoke-direct {p0, p1}, Lcom/bytedance/sdk/component/eV/eV/Ko;->hjc(Lcom/bytedance/sdk/component/eV/hjc/hjc;)Landroid/graphics/Bitmap;

    move-result-object v0

    goto :goto_2

    :cond_3
    :goto_1
    invoke-direct {p0, p1}, Lcom/bytedance/sdk/component/eV/eV/Ko;->ex(Lcom/bytedance/sdk/component/eV/hjc/hjc;)Landroid/graphics/Bitmap;

    move-result-object v0

    :goto_2
    if-nez v0, :cond_4

    new-instance v0, Lcom/bytedance/sdk/component/eV/eV/UYd;

    invoke-direct {v0}, Lcom/bytedance/sdk/component/eV/eV/UYd;-><init>()V

    invoke-virtual {p1, v0}, Lcom/bytedance/sdk/component/eV/hjc/hjc;->Fj(Lcom/bytedance/sdk/component/eV/eV/mSE;)Z

    return-void

    :cond_4
    new-instance v1, Lcom/bytedance/sdk/component/eV/eV/dG;

    const/4 v3, 0x0

    invoke-direct {v1, v0, v2, v3}, Lcom/bytedance/sdk/component/eV/eV/dG;-><init>(Ljava/lang/Object;Lcom/bytedance/sdk/component/eV/WR;Z)V

    invoke-virtual {p1, v1}, Lcom/bytedance/sdk/component/eV/hjc/hjc;->Fj(Lcom/bytedance/sdk/component/eV/eV/mSE;)Z

    return-void
.end method
