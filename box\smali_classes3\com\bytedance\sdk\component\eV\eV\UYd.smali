.class public Lcom/bytedance/sdk/component/eV/eV/UYd;
.super Lcom/bytedance/sdk/component/eV/eV/Fj;


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Lcom/bytedance/sdk/component/eV/eV/Fj;-><init>()V

    return-void
.end method


# virtual methods
.method public Fj()Ljava/lang/String;
    .locals 1

    const-string v0, "raw_cache"

    return-object v0
.end method

.method public Fj(Lcom/bytedance/sdk/component/eV/hjc/hjc;)V
    .locals 3

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/eV/hjc/hjc;->vYf()Lcom/bytedance/sdk/component/eV/hjc/WR;

    move-result-object v0

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/eV/hjc/hjc;->mE()Lcom/bytedance/sdk/component/eV/ex;

    move-result-object v1

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/component/eV/hjc/WR;->ex(Lcom/bytedance/sdk/component/eV/ex;)Lcom/bytedance/sdk/component/eV/vYf;

    move-result-object v0

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/eV/hjc/hjc;->Ko()Ljava/lang/String;

    move-result-object v1

    invoke-interface {v0, v1}, Lcom/bytedance/sdk/component/eV/Fj;->Fj(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [B

    if-nez v0, :cond_0

    new-instance v0, Lcom/bytedance/sdk/component/eV/eV/WR;

    invoke-direct {v0}, Lcom/bytedance/sdk/component/eV/eV/WR;-><init>()V

    invoke-virtual {p1, v0}, Lcom/bytedance/sdk/component/eV/hjc/hjc;->Fj(Lcom/bytedance/sdk/component/eV/eV/mSE;)Z

    return-void

    :cond_0
    new-instance v1, Lcom/bytedance/sdk/component/eV/eV/ex;

    const/4 v2, 0x0

    invoke-direct {v1, v0, v2}, Lcom/bytedance/sdk/component/eV/eV/ex;-><init>([BLcom/bytedance/sdk/component/eV/WR;)V

    invoke-virtual {p1, v1}, Lcom/bytedance/sdk/component/eV/hjc/hjc;->Fj(Lcom/bytedance/sdk/component/eV/eV/mSE;)Z

    return-void
.end method
