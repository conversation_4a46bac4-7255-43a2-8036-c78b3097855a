.class public Lcom/bytedance/sdk/component/eV/eV/Ubf;
.super Lcom/bytedance/sdk/component/eV/eV/Fj;


# instance fields
.field private Fj:[B

.field private ex:Lcom/bytedance/sdk/component/eV/WR;


# direct methods
.method public constructor <init>([BLcom/bytedance/sdk/component/eV/WR;)V
    .locals 0

    invoke-direct {p0}, Lcom/bytedance/sdk/component/eV/eV/Fj;-><init>()V

    iput-object p1, p0, Lcom/bytedance/sdk/component/eV/eV/Ubf;->Fj:[B

    iput-object p2, p0, Lcom/bytedance/sdk/component/eV/eV/Ubf;->ex:Lcom/bytedance/sdk/component/eV/WR;

    return-void
.end method

.method private Fj(ILjava/lang/String;Ljava/lang/Throwable;Lcom/bytedance/sdk/component/eV/hjc/hjc;)V
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/eV/eV/Ubf;->ex:Lcom/bytedance/sdk/component/eV/WR;

    if-nez v0, :cond_0

    new-instance p1, Lcom/bytedance/sdk/component/eV/eV/rAx;

    invoke-direct {p1}, Lcom/bytedance/sdk/component/eV/eV/rAx;-><init>()V

    invoke-virtual {p4, p1}, Lcom/bytedance/sdk/component/eV/hjc/hjc;->Fj(Lcom/bytedance/sdk/component/eV/eV/mSE;)Z

    return-void

    :cond_0
    new-instance v0, Lcom/bytedance/sdk/component/eV/eV/BcC;

    invoke-direct {v0, p1, p2, p3}, Lcom/bytedance/sdk/component/eV/eV/BcC;-><init>(ILjava/lang/String;Ljava/lang/Throwable;)V

    invoke-virtual {p4, v0}, Lcom/bytedance/sdk/component/eV/hjc/hjc;->Fj(Lcom/bytedance/sdk/component/eV/eV/mSE;)Z

    return-void
.end method


# virtual methods
.method public Fj()Ljava/lang/String;
    .locals 1

    const-string v0, "decode"

    return-object v0
.end method

.method public Fj(Lcom/bytedance/sdk/component/eV/hjc/hjc;)V
    .locals 6

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/eV/hjc/hjc;->vYf()Lcom/bytedance/sdk/component/eV/hjc/WR;

    move-result-object v0

    invoke-virtual {v0, p1}, Lcom/bytedance/sdk/component/eV/hjc/WR;->Fj(Lcom/bytedance/sdk/component/eV/hjc/hjc;)Lcom/bytedance/sdk/component/eV/hjc/ex/Fj;

    move-result-object v1

    const/16 v2, 0x3ea

    :try_start_0
    invoke-virtual {p1}, Lcom/bytedance/sdk/component/eV/hjc/hjc;->mC()Lcom/bytedance/sdk/component/eV/Tc;

    iget-object v3, p0, Lcom/bytedance/sdk/component/eV/eV/Ubf;->Fj:[B

    invoke-virtual {v1, v3}, Lcom/bytedance/sdk/component/eV/hjc/ex/Fj;->Fj([B)Landroid/graphics/Bitmap;

    move-result-object v1

    if-eqz v1, :cond_0

    new-instance v3, Lcom/bytedance/sdk/component/eV/eV/dG;

    iget-object v4, p0, Lcom/bytedance/sdk/component/eV/eV/Ubf;->ex:Lcom/bytedance/sdk/component/eV/WR;

    const/4 v5, 0x0

    invoke-direct {v3, v1, v4, v5}, Lcom/bytedance/sdk/component/eV/eV/dG;-><init>(Ljava/lang/Object;Lcom/bytedance/sdk/component/eV/WR;Z)V

    invoke-virtual {p1, v3}, Lcom/bytedance/sdk/component/eV/hjc/hjc;->Fj(Lcom/bytedance/sdk/component/eV/eV/mSE;)Z

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/eV/hjc/hjc;->Ubf()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/eV/hjc/hjc;->mE()Lcom/bytedance/sdk/component/eV/ex;

    move-result-object v4

    invoke-virtual {v0, v4}, Lcom/bytedance/sdk/component/eV/hjc/WR;->Fj(Lcom/bytedance/sdk/component/eV/ex;)Lcom/bytedance/sdk/component/eV/rS;

    move-result-object v0

    invoke-interface {v0, v3, v1}, Lcom/bytedance/sdk/component/eV/Fj;->Fj(Ljava/lang/Object;Ljava/lang/Object;)Z

    return-void

    :catchall_0
    move-exception v0

    goto :goto_0

    :cond_0
    const-string v0, "decode failed bitmap null"

    const/4 v1, 0x0

    invoke-direct {p0, v2, v0, v1, p1}, Lcom/bytedance/sdk/component/eV/eV/Ubf;->Fj(ILjava/lang/String;Ljava/lang/Throwable;Lcom/bytedance/sdk/component/eV/hjc/hjc;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    return-void

    :goto_0
    new-instance v1, Ljava/lang/StringBuilder;

    const-string v3, "decode failed:"

    invoke-direct {v1, v3}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    invoke-virtual {v0}, Ljava/lang/Throwable;->getMessage()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v1, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-direct {p0, v2, v1, v0, p1}, Lcom/bytedance/sdk/component/eV/eV/Ubf;->Fj(ILjava/lang/String;Ljava/lang/Throwable;Lcom/bytedance/sdk/component/eV/hjc/hjc;)V

    return-void
.end method
