.class public Lcom/bytedance/sdk/component/eV/eV/WR;
.super Lcom/bytedance/sdk/component/eV/eV/Fj;


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Lcom/bytedance/sdk/component/eV/eV/Fj;-><init>()V

    return-void
.end method

.method private Fj(Lcom/bytedance/sdk/component/eV/hjc/hjc;Ljava/lang/String;)[B
    .locals 2

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/eV/hjc/hjc;->vYf()Lcom/bytedance/sdk/component/eV/hjc/WR;

    move-result-object v0

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/eV/hjc/hjc;->mE()Lcom/bytedance/sdk/component/eV/ex;

    move-result-object v1

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/component/eV/hjc/WR;->hjc(Lcom/bytedance/sdk/component/eV/ex;)Lcom/bytedance/sdk/component/eV/hjc;

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/eV/hjc/hjc;->vYf()Lcom/bytedance/sdk/component/eV/hjc/WR;

    move-result-object p1

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/eV/hjc/WR;->hjc()Ljava/util/Collection;

    move-result-object p1

    const/4 v0, 0x0

    if-nez p1, :cond_0

    return-object v0

    :cond_0
    invoke-interface {p1}, Ljava/util/Collection;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :cond_1
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_2

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/bytedance/sdk/component/eV/hjc;

    invoke-interface {v1, p2}, Lcom/bytedance/sdk/component/eV/Fj;->Fj(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, [B

    if-eqz v1, :cond_1

    return-object v1

    :cond_2
    return-object v0
.end method

.method private ex(Lcom/bytedance/sdk/component/eV/hjc/hjc;Ljava/lang/String;)[B
    .locals 1

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/eV/hjc/hjc;->vYf()Lcom/bytedance/sdk/component/eV/hjc/WR;

    move-result-object v0

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/eV/hjc/hjc;->mE()Lcom/bytedance/sdk/component/eV/ex;

    move-result-object p1

    invoke-virtual {v0, p1}, Lcom/bytedance/sdk/component/eV/hjc/WR;->hjc(Lcom/bytedance/sdk/component/eV/ex;)Lcom/bytedance/sdk/component/eV/hjc;

    move-result-object p1

    if-nez p1, :cond_0

    const/4 p1, 0x0

    return-object p1

    :cond_0
    invoke-interface {p1, p2}, Lcom/bytedance/sdk/component/eV/Fj;->Fj(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, [B

    return-object p1
.end method


# virtual methods
.method public Fj()Ljava/lang/String;
    .locals 1

    const-string v0, "disk_cache"

    return-object v0
.end method

.method public Fj(Lcom/bytedance/sdk/component/eV/hjc/hjc;)V
    .locals 4

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/eV/hjc/hjc;->Ko()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/eV/hjc/hjc;->Af()Z

    move-result v1

    if-nez v1, :cond_1

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/eV/hjc/hjc;->mE()Lcom/bytedance/sdk/component/eV/ex;

    move-result-object v1

    invoke-interface {v1}, Lcom/bytedance/sdk/component/eV/ex;->WR()Z

    move-result v1

    if-eqz v1, :cond_0

    goto :goto_0

    :cond_0
    invoke-direct {p0, p1, v0}, Lcom/bytedance/sdk/component/eV/eV/WR;->ex(Lcom/bytedance/sdk/component/eV/hjc/hjc;Ljava/lang/String;)[B

    move-result-object v1

    goto :goto_1

    :cond_1
    :goto_0
    invoke-direct {p0, p1, v0}, Lcom/bytedance/sdk/component/eV/eV/WR;->Fj(Lcom/bytedance/sdk/component/eV/hjc/hjc;Ljava/lang/String;)[B

    move-result-object v1

    :goto_1
    if-nez v1, :cond_2

    new-instance v0, Lcom/bytedance/sdk/component/eV/eV/rAx;

    invoke-direct {v0}, Lcom/bytedance/sdk/component/eV/eV/rAx;-><init>()V

    invoke-virtual {p1, v0}, Lcom/bytedance/sdk/component/eV/hjc/hjc;->Fj(Lcom/bytedance/sdk/component/eV/eV/mSE;)Z

    return-void

    :cond_2
    new-instance v2, Lcom/bytedance/sdk/component/eV/eV/ex;

    const/4 v3, 0x0

    invoke-direct {v2, v1, v3}, Lcom/bytedance/sdk/component/eV/eV/ex;-><init>([BLcom/bytedance/sdk/component/eV/WR;)V

    invoke-virtual {p1, v2}, Lcom/bytedance/sdk/component/eV/hjc/hjc;->Fj(Lcom/bytedance/sdk/component/eV/eV/mSE;)Z

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/eV/hjc/hjc;->vYf()Lcom/bytedance/sdk/component/eV/hjc/WR;

    move-result-object v2

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/eV/hjc/hjc;->mE()Lcom/bytedance/sdk/component/eV/ex;

    move-result-object p1

    invoke-virtual {v2, p1}, Lcom/bytedance/sdk/component/eV/hjc/WR;->ex(Lcom/bytedance/sdk/component/eV/ex;)Lcom/bytedance/sdk/component/eV/vYf;

    move-result-object p1

    invoke-interface {p1, v0, v1}, Lcom/bytedance/sdk/component/eV/Fj;->Fj(Ljava/lang/Object;Ljava/lang/Object;)Z

    return-void
.end method
