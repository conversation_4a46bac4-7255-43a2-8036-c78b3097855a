.class public Lcom/bytedance/sdk/component/eV/eV/dG;
.super Lcom/bytedance/sdk/component/eV/eV/Fj;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "<T:",
        "Ljava/lang/Object;",
        ">",
        "Lcom/bytedance/sdk/component/eV/eV/Fj;"
    }
.end annotation


# instance fields
.field private Fj:Ljava/lang/Object;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "TT;"
        }
    .end annotation
.end field

.field private ex:Lcom/bytedance/sdk/component/eV/WR;

.field private hjc:Z


# direct methods
.method public constructor <init>(Ljava/lang/Object;Lcom/bytedance/sdk/component/eV/WR;Z)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(TT;",
            "Lcom/bytedance/sdk/component/eV/WR;",
            "Z)V"
        }
    .end annotation

    invoke-direct {p0}, Lcom/bytedance/sdk/component/eV/eV/Fj;-><init>()V

    iput-object p1, p0, Lcom/bytedance/sdk/component/eV/eV/dG;->Fj:Ljava/lang/Object;

    iput-object p2, p0, Lcom/bytedance/sdk/component/eV/eV/dG;->ex:Lcom/bytedance/sdk/component/eV/WR;

    iput-boolean p3, p0, Lcom/bytedance/sdk/component/eV/eV/dG;->hjc:Z

    return-void
.end method

.method private ex()Ljava/util/Map;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    iget-object v0, p0, Lcom/bytedance/sdk/component/eV/eV/dG;->ex:Lcom/bytedance/sdk/component/eV/WR;

    if-eqz v0, :cond_0

    invoke-interface {v0}, Lcom/bytedance/sdk/component/eV/WR;->Ubf()Ljava/util/Map;

    move-result-object v0

    return-object v0

    :cond_0
    const/4 v0, 0x0

    return-object v0
.end method

.method private ex(Lcom/bytedance/sdk/component/eV/hjc/hjc;)V
    .locals 5

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/eV/hjc/hjc;->mSE()Lcom/bytedance/sdk/component/eV/JU;

    move-result-object v0

    if-eqz v0, :cond_0

    new-instance v1, Lcom/bytedance/sdk/component/eV/hjc/eV;

    invoke-direct {v1}, Lcom/bytedance/sdk/component/eV/hjc/eV;-><init>()V

    iget-object v2, p0, Lcom/bytedance/sdk/component/eV/eV/dG;->Fj:Ljava/lang/Object;

    invoke-direct {p0}, Lcom/bytedance/sdk/component/eV/eV/dG;->ex()Ljava/util/Map;

    move-result-object v3

    iget-boolean v4, p0, Lcom/bytedance/sdk/component/eV/eV/dG;->hjc:Z

    invoke-virtual {v1, p1, v2, v3, v4}, Lcom/bytedance/sdk/component/eV/hjc/eV;->Fj(Lcom/bytedance/sdk/component/eV/hjc/hjc;Ljava/lang/Object;Ljava/util/Map;Z)Lcom/bytedance/sdk/component/eV/hjc/eV;

    move-result-object p1

    invoke-interface {v0, p1}, Lcom/bytedance/sdk/component/eV/JU;->Fj(Lcom/bytedance/sdk/component/eV/rAx;)V

    :cond_0
    return-void
.end method


# virtual methods
.method public Fj()Ljava/lang/String;
    .locals 1

    const-string v0, "success"

    return-object v0
.end method

.method public Fj(Lcom/bytedance/sdk/component/eV/hjc/hjc;)V
    .locals 4

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/eV/hjc/hjc;->cB()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/eV/hjc/hjc;->vYf()Lcom/bytedance/sdk/component/eV/hjc/WR;

    move-result-object v1

    invoke-virtual {v1}, Lcom/bytedance/sdk/component/eV/hjc/WR;->svN()Ljava/util/Map;

    move-result-object v1

    invoke-interface {v1, v0}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Ljava/util/List;

    if-nez v2, :cond_0

    invoke-direct {p0, p1}, Lcom/bytedance/sdk/component/eV/eV/dG;->ex(Lcom/bytedance/sdk/component/eV/hjc/hjc;)V

    return-void

    :cond_0
    monitor-enter v2

    :try_start_0
    invoke-interface {v2}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v3

    if-eqz v3, :cond_1

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lcom/bytedance/sdk/component/eV/hjc/hjc;

    invoke-direct {p0, v3}, Lcom/bytedance/sdk/component/eV/eV/dG;->ex(Lcom/bytedance/sdk/component/eV/hjc/hjc;)V

    goto :goto_0

    :catchall_0
    move-exception p1

    goto :goto_1

    :cond_1
    invoke-interface {v2}, Ljava/util/List;->clear()V

    invoke-interface {v1, v0}, Ljava/util/Map;->remove(Ljava/lang/Object;)Ljava/lang/Object;

    monitor-exit v2
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    return-void

    :goto_1
    monitor-exit v2

    throw p1
.end method
