.class public Lcom/bytedance/sdk/component/eV/eV/eV;
.super Lcom/bytedance/sdk/component/eV/eV/Fj;


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Lcom/bytedance/sdk/component/eV/eV/Fj;-><init>()V

    return-void
.end method


# virtual methods
.method public Fj()Ljava/lang/String;
    .locals 1

    const-string v0, "cache_policy"

    return-object v0
.end method

.method public Fj(Lcom/bytedance/sdk/component/eV/hjc/hjc;)V
    .locals 2

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/eV/hjc/hjc;->mE()Lcom/bytedance/sdk/component/eV/ex;

    move-result-object v0

    if-eqz v0, :cond_1

    invoke-interface {v0}, Lcom/bytedance/sdk/component/eV/ex;->hjc()Z

    move-result v1

    if-eqz v1, :cond_0

    new-instance v0, Lcom/bytedance/sdk/component/eV/eV/Ko;

    invoke-direct {v0}, Lcom/bytedance/sdk/component/eV/eV/Ko;-><init>()V

    invoke-virtual {p1, v0}, Lcom/bytedance/sdk/component/eV/hjc/hjc;->Fj(Lcom/bytedance/sdk/component/eV/eV/mSE;)Z

    return-void

    :cond_0
    invoke-interface {v0}, Lcom/bytedance/sdk/component/eV/ex;->eV()Z

    move-result v0

    if-eqz v0, :cond_1

    new-instance v0, Lcom/bytedance/sdk/component/eV/eV/WR;

    invoke-direct {v0}, Lcom/bytedance/sdk/component/eV/eV/WR;-><init>()V

    invoke-virtual {p1, v0}, Lcom/bytedance/sdk/component/eV/hjc/hjc;->Fj(Lcom/bytedance/sdk/component/eV/eV/mSE;)Z

    return-void

    :cond_1
    new-instance v0, Lcom/bytedance/sdk/component/eV/eV/rAx;

    invoke-direct {v0}, Lcom/bytedance/sdk/component/eV/eV/rAx;-><init>()V

    invoke-virtual {p1, v0}, Lcom/bytedance/sdk/component/eV/hjc/hjc;->Fj(Lcom/bytedance/sdk/component/eV/eV/mSE;)Z

    return-void
.end method
