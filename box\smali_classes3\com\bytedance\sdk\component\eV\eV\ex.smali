.class public Lcom/bytedance/sdk/component/eV/eV/ex;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/sdk/component/eV/eV/mSE;


# instance fields
.field private Fj:[B

.field private ex:Lcom/bytedance/sdk/component/eV/WR;


# direct methods
.method public constructor <init>([BLcom/bytedance/sdk/component/eV/WR;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/bytedance/sdk/component/eV/eV/ex;->Fj:[B

    iput-object p2, p0, Lcom/bytedance/sdk/component/eV/eV/ex;->ex:Lcom/bytedance/sdk/component/eV/WR;

    return-void
.end method


# virtual methods
.method public Fj()Ljava/lang/String;
    .locals 1

    const-string v0, "image_type"

    return-object v0
.end method

.method public Fj(Lcom/bytedance/sdk/component/eV/hjc/hjc;)V
    .locals 4

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/eV/hjc/hjc;->UYd()I

    move-result v0

    iget-object v1, p0, Lcom/bytedance/sdk/component/eV/eV/ex;->Fj:[B

    array-length v1, v1

    invoke-virtual {p1, v1}, Lcom/bytedance/sdk/component/eV/hjc/hjc;->Fj(I)V

    const/4 v1, 0x2

    if-eq v0, v1, :cond_3

    const/4 v1, 0x3

    if-eq v0, v1, :cond_2

    iget-object v0, p0, Lcom/bytedance/sdk/component/eV/eV/ex;->Fj:[B

    invoke-static {v0}, Lcom/bytedance/sdk/component/eV/hjc/hjc/Fj;->ex([B)Z

    move-result v0

    if-eqz v0, :cond_0

    new-instance v1, Lcom/bytedance/sdk/component/eV/eV/dG;

    iget-object v2, p0, Lcom/bytedance/sdk/component/eV/eV/ex;->Fj:[B

    iget-object v3, p0, Lcom/bytedance/sdk/component/eV/eV/ex;->ex:Lcom/bytedance/sdk/component/eV/WR;

    invoke-direct {v1, v2, v3, v0}, Lcom/bytedance/sdk/component/eV/eV/dG;-><init>(Ljava/lang/Object;Lcom/bytedance/sdk/component/eV/WR;Z)V

    goto :goto_0

    :cond_0
    iget-object v1, p0, Lcom/bytedance/sdk/component/eV/eV/ex;->Fj:[B

    invoke-static {v1}, Lcom/bytedance/sdk/component/eV/hjc/hjc/Fj;->Fj([B)Z

    move-result v1

    if-eqz v1, :cond_1

    new-instance v1, Lcom/bytedance/sdk/component/eV/eV/Ubf;

    iget-object v0, p0, Lcom/bytedance/sdk/component/eV/eV/ex;->Fj:[B

    iget-object v2, p0, Lcom/bytedance/sdk/component/eV/eV/ex;->ex:Lcom/bytedance/sdk/component/eV/WR;

    invoke-direct {v1, v0, v2}, Lcom/bytedance/sdk/component/eV/eV/Ubf;-><init>([BLcom/bytedance/sdk/component/eV/WR;)V

    goto :goto_0

    :cond_1
    new-instance v1, Lcom/bytedance/sdk/component/eV/eV/dG;

    iget-object v2, p0, Lcom/bytedance/sdk/component/eV/eV/ex;->Fj:[B

    iget-object v3, p0, Lcom/bytedance/sdk/component/eV/eV/ex;->ex:Lcom/bytedance/sdk/component/eV/WR;

    invoke-direct {v1, v2, v3, v0}, Lcom/bytedance/sdk/component/eV/eV/dG;-><init>(Ljava/lang/Object;Lcom/bytedance/sdk/component/eV/WR;Z)V

    goto :goto_0

    :cond_2
    new-instance v1, Lcom/bytedance/sdk/component/eV/eV/dG;

    iget-object v0, p0, Lcom/bytedance/sdk/component/eV/eV/ex;->Fj:[B

    iget-object v2, p0, Lcom/bytedance/sdk/component/eV/eV/ex;->ex:Lcom/bytedance/sdk/component/eV/WR;

    invoke-static {v0}, Lcom/bytedance/sdk/component/eV/hjc/hjc/Fj;->ex([B)Z

    move-result v3

    invoke-direct {v1, v0, v2, v3}, Lcom/bytedance/sdk/component/eV/eV/dG;-><init>(Ljava/lang/Object;Lcom/bytedance/sdk/component/eV/WR;Z)V

    goto :goto_0

    :cond_3
    iget-object v0, p0, Lcom/bytedance/sdk/component/eV/eV/ex;->Fj:[B

    invoke-static {v0}, Lcom/bytedance/sdk/component/eV/hjc/hjc/Fj;->Fj([B)Z

    move-result v0

    if-eqz v0, :cond_4

    new-instance v1, Lcom/bytedance/sdk/component/eV/eV/Ubf;

    iget-object v0, p0, Lcom/bytedance/sdk/component/eV/eV/ex;->Fj:[B

    iget-object v2, p0, Lcom/bytedance/sdk/component/eV/eV/ex;->ex:Lcom/bytedance/sdk/component/eV/WR;

    invoke-direct {v1, v0, v2}, Lcom/bytedance/sdk/component/eV/eV/Ubf;-><init>([BLcom/bytedance/sdk/component/eV/WR;)V

    goto :goto_0

    :cond_4
    iget-object v0, p0, Lcom/bytedance/sdk/component/eV/eV/ex;->ex:Lcom/bytedance/sdk/component/eV/WR;

    if-nez v0, :cond_5

    new-instance v1, Lcom/bytedance/sdk/component/eV/eV/rAx;

    invoke-direct {v1}, Lcom/bytedance/sdk/component/eV/eV/rAx;-><init>()V

    goto :goto_0

    :cond_5
    new-instance v1, Lcom/bytedance/sdk/component/eV/eV/BcC;

    const-string v0, "not image format"

    const/4 v2, 0x0

    const/16 v3, 0x3e9

    invoke-direct {v1, v3, v0, v2}, Lcom/bytedance/sdk/component/eV/eV/BcC;-><init>(ILjava/lang/String;Ljava/lang/Throwable;)V

    :goto_0
    invoke-virtual {p1, v1}, Lcom/bytedance/sdk/component/eV/hjc/hjc;->Fj(Lcom/bytedance/sdk/component/eV/eV/mSE;)Z

    return-void
.end method
