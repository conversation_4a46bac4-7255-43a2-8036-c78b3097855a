.class public Lcom/bytedance/sdk/component/eV/eV/hjc;
.super Lcom/bytedance/sdk/component/eV/eV/Fj;


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Lcom/bytedance/sdk/component/eV/eV/Fj;-><init>()V

    return-void
.end method


# virtual methods
.method public Fj()Ljava/lang/String;
    .locals 1

    const-string v0, "generate_key"

    return-object v0
.end method

.method public Fj(Lcom/bytedance/sdk/component/eV/hjc/hjc;)V
    .locals 2

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/eV/hjc/hjc;->Ubf()Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    if-eqz v0, :cond_0

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/eV/hjc/hjc;->vYf()Lcom/bytedance/sdk/component/eV/hjc/WR;

    move-result-object v0

    invoke-virtual {v0}, Lcom/bytedance/sdk/component/eV/hjc/WR;->Ubf()Lcom/bytedance/sdk/component/eV/UYd;

    move-result-object v0

    invoke-interface {v0, p1}, Lcom/bytedance/sdk/component/eV/UYd;->Fj(Lcom/bytedance/sdk/component/eV/mSE;)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p1, v1}, Lcom/bytedance/sdk/component/eV/hjc/hjc;->ex(Ljava/lang/String;)V

    invoke-interface {v0, p1}, Lcom/bytedance/sdk/component/eV/UYd;->ex(Lcom/bytedance/sdk/component/eV/mSE;)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p1, v0}, Lcom/bytedance/sdk/component/eV/hjc/hjc;->Fj(Ljava/lang/String;)V

    :cond_0
    new-instance v0, Lcom/bytedance/sdk/component/eV/eV/svN;

    invoke-direct {v0}, Lcom/bytedance/sdk/component/eV/eV/svN;-><init>()V

    invoke-virtual {p1, v0}, Lcom/bytedance/sdk/component/eV/hjc/hjc;->Fj(Lcom/bytedance/sdk/component/eV/eV/mSE;)Z

    return-void
.end method
