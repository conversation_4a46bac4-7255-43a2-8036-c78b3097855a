.class Lcom/bytedance/sdk/component/eV/eV/rAx$1;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/bytedance/sdk/component/eV/eV/rAx;->Fj(Lcom/bytedance/sdk/component/eV/hjc/hjc;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/component/eV/ex;

.field final synthetic Ubf:[B

.field final synthetic WR:Lcom/bytedance/sdk/component/eV/eV/rAx;

.field final synthetic eV:Ljava/lang/String;

.field final synthetic ex:Lcom/bytedance/sdk/component/eV/hjc/WR;

.field final synthetic hjc:Lcom/bytedance/sdk/component/eV/hjc/hjc;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/component/eV/eV/rAx;Lcom/bytedance/sdk/component/eV/ex;Lcom/bytedance/sdk/component/eV/hjc/WR;Lcom/bytedance/sdk/component/eV/hjc/hjc;Ljava/lang/String;[B)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/eV/eV/rAx$1;->WR:Lcom/bytedance/sdk/component/eV/eV/rAx;

    iput-object p2, p0, Lcom/bytedance/sdk/component/eV/eV/rAx$1;->Fj:Lcom/bytedance/sdk/component/eV/ex;

    iput-object p3, p0, Lcom/bytedance/sdk/component/eV/eV/rAx$1;->ex:Lcom/bytedance/sdk/component/eV/hjc/WR;

    iput-object p4, p0, Lcom/bytedance/sdk/component/eV/eV/rAx$1;->hjc:Lcom/bytedance/sdk/component/eV/hjc/hjc;

    iput-object p5, p0, Lcom/bytedance/sdk/component/eV/eV/rAx$1;->eV:Ljava/lang/String;

    iput-object p6, p0, Lcom/bytedance/sdk/component/eV/eV/rAx$1;->Ubf:[B

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 3

    iget-object v0, p0, Lcom/bytedance/sdk/component/eV/eV/rAx$1;->Fj:Lcom/bytedance/sdk/component/eV/ex;

    invoke-interface {v0}, Lcom/bytedance/sdk/component/eV/ex;->eV()Z

    move-result v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/sdk/component/eV/eV/rAx$1;->ex:Lcom/bytedance/sdk/component/eV/hjc/WR;

    iget-object v1, p0, Lcom/bytedance/sdk/component/eV/eV/rAx$1;->hjc:Lcom/bytedance/sdk/component/eV/hjc/hjc;

    invoke-virtual {v1}, Lcom/bytedance/sdk/component/eV/hjc/hjc;->mE()Lcom/bytedance/sdk/component/eV/ex;

    move-result-object v1

    invoke-virtual {v0, v1}, Lcom/bytedance/sdk/component/eV/hjc/WR;->hjc(Lcom/bytedance/sdk/component/eV/ex;)Lcom/bytedance/sdk/component/eV/hjc;

    move-result-object v0

    iget-object v1, p0, Lcom/bytedance/sdk/component/eV/eV/rAx$1;->eV:Ljava/lang/String;

    iget-object v2, p0, Lcom/bytedance/sdk/component/eV/eV/rAx$1;->Ubf:[B

    invoke-interface {v0, v1, v2}, Lcom/bytedance/sdk/component/eV/Fj;->Fj(Ljava/lang/Object;Ljava/lang/Object;)Z

    :cond_0
    return-void
.end method
