.class public Lcom/bytedance/sdk/component/eV/eV/rAx;
.super Lcom/bytedance/sdk/component/eV/eV/Fj;


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Lcom/bytedance/sdk/component/eV/eV/Fj;-><init>()V

    return-void
.end method

.method private Fj(ILjava/lang/String;Ljava/lang/Throwable;Lcom/bytedance/sdk/component/eV/hjc/hjc;)V
    .locals 1

    new-instance v0, Lcom/bytedance/sdk/component/eV/eV/BcC;

    invoke-direct {v0, p1, p2, p3}, Lcom/bytedance/sdk/component/eV/eV/BcC;-><init>(ILjava/lang/String;Ljava/lang/Throwable;)V

    invoke-virtual {p4, v0}, Lcom/bytedance/sdk/component/eV/hjc/hjc;->Fj(Lcom/bytedance/sdk/component/eV/eV/mSE;)Z

    return-void
.end method


# virtual methods
.method public Fj()Ljava/lang/String;
    .locals 1

    const-string v0, "net_request"

    return-object v0
.end method

.method public Fj(Lcom/bytedance/sdk/component/eV/hjc/hjc;)V
    .locals 9

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/eV/hjc/hjc;->vYf()Lcom/bytedance/sdk/component/eV/hjc/WR;

    move-result-object v3

    invoke-virtual {v3}, Lcom/bytedance/sdk/component/eV/hjc/WR;->eV()Lcom/bytedance/sdk/component/eV/eV;

    move-result-object v0

    const/4 v1, 0x0

    invoke-virtual {p1, v1}, Lcom/bytedance/sdk/component/eV/hjc/hjc;->Fj(Z)V

    :try_start_0
    new-instance v1, Lcom/bytedance/sdk/component/eV/ex/hjc;

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/eV/hjc/hjc;->Fj()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/eV/hjc/hjc;->dG()Z

    move-result v4

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/eV/hjc/hjc;->Tc()Z

    move-result v5

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/eV/hjc/hjc;->mC()Lcom/bytedance/sdk/component/eV/Tc;

    move-result-object v6

    invoke-direct {v1, v2, v4, v5, v6}, Lcom/bytedance/sdk/component/eV/ex/hjc;-><init>(Ljava/lang/String;ZZLcom/bytedance/sdk/component/eV/Tc;)V

    invoke-interface {v0, v1}, Lcom/bytedance/sdk/component/eV/eV;->Fj(Lcom/bytedance/sdk/component/eV/Ubf;)Lcom/bytedance/sdk/component/eV/WR;

    move-result-object v0

    invoke-interface {v0}, Lcom/bytedance/sdk/component/eV/WR;->ex()I

    move-result v1

    invoke-interface {v0}, Lcom/bytedance/sdk/component/eV/WR;->Fj()Lcom/bytedance/sdk/component/eV/svN;

    move-result-object v2

    invoke-virtual {p1, v2}, Lcom/bytedance/sdk/component/eV/hjc/hjc;->Fj(Lcom/bytedance/sdk/component/eV/svN;)V

    invoke-interface {v0}, Lcom/bytedance/sdk/component/eV/WR;->ex()I

    move-result v2

    const/16 v4, 0xc8

    if-ne v2, v4, :cond_1

    invoke-interface {v0}, Lcom/bytedance/sdk/component/eV/WR;->hjc()Ljava/lang/Object;

    move-result-object v1

    move-object v6, v1

    check-cast v6, [B

    new-instance v1, Lcom/bytedance/sdk/component/eV/eV/ex;

    invoke-direct {v1, v6, v0}, Lcom/bytedance/sdk/component/eV/eV/ex;-><init>([BLcom/bytedance/sdk/component/eV/WR;)V

    invoke-virtual {p1, v1}, Lcom/bytedance/sdk/component/eV/hjc/hjc;->Fj(Lcom/bytedance/sdk/component/eV/eV/mSE;)Z

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/eV/hjc/hjc;->Ko()Ljava/lang/String;

    move-result-object v5

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/eV/hjc/hjc;->mE()Lcom/bytedance/sdk/component/eV/ex;

    move-result-object v2

    invoke-interface {v2}, Lcom/bytedance/sdk/component/eV/ex;->hjc()Z

    move-result v0

    if-eqz v0, :cond_0

    invoke-virtual {p1}, Lcom/bytedance/sdk/component/eV/hjc/hjc;->mE()Lcom/bytedance/sdk/component/eV/ex;

    move-result-object v0

    invoke-virtual {v3, v0}, Lcom/bytedance/sdk/component/eV/hjc/WR;->ex(Lcom/bytedance/sdk/component/eV/ex;)Lcom/bytedance/sdk/component/eV/vYf;

    move-result-object v0

    invoke-interface {v0, v5, v6}, Lcom/bytedance/sdk/component/eV/Fj;->Fj(Ljava/lang/Object;Ljava/lang/Object;)Z

    goto :goto_0

    :catchall_0
    move-exception v0

    goto :goto_2

    :cond_0
    :goto_0
    invoke-virtual {v3}, Lcom/bytedance/sdk/component/eV/hjc/WR;->WR()Ljava/util/concurrent/ExecutorService;

    move-result-object v7

    new-instance v8, Lcom/bytedance/sdk/component/eV/eV/rAx$1;

    move-object v0, v8

    move-object v1, p0

    move-object v4, p1

    invoke-direct/range {v0 .. v6}, Lcom/bytedance/sdk/component/eV/eV/rAx$1;-><init>(Lcom/bytedance/sdk/component/eV/eV/rAx;Lcom/bytedance/sdk/component/eV/ex;Lcom/bytedance/sdk/component/eV/hjc/WR;Lcom/bytedance/sdk/component/eV/hjc/hjc;Ljava/lang/String;[B)V

    invoke-interface {v7, v8}, Ljava/util/concurrent/ExecutorService;->submit(Ljava/lang/Runnable;)Ljava/util/concurrent/Future;

    return-void

    :cond_1
    invoke-virtual {v3}, Lcom/bytedance/sdk/component/eV/hjc/WR;->BcC()Lcom/bytedance/sdk/component/eV/Ql;

    invoke-static {v0}, Ljava/lang/String;->valueOf(Ljava/lang/Object;)Ljava/lang/String;

    invoke-interface {v0}, Lcom/bytedance/sdk/component/eV/WR;->hjc()Ljava/lang/Object;

    move-result-object v2

    instance-of v3, v2, Ljava/lang/Throwable;

    if-eqz v3, :cond_2

    check-cast v2, Ljava/lang/Throwable;

    goto :goto_1

    :cond_2
    const/4 v2, 0x0

    :goto_1
    invoke-interface {v0}, Lcom/bytedance/sdk/component/eV/WR;->eV()Ljava/lang/String;

    move-result-object v0

    invoke-direct {p0, v1, v0, v2, p1}, Lcom/bytedance/sdk/component/eV/eV/rAx;->Fj(ILjava/lang/String;Ljava/lang/Throwable;Lcom/bytedance/sdk/component/eV/hjc/hjc;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    return-void

    :goto_2
    const/16 v1, 0x3ec

    const-string v2, "net request failed!"

    invoke-direct {p0, v1, v2, v0, p1}, Lcom/bytedance/sdk/component/eV/eV/rAx;->Fj(ILjava/lang/String;Ljava/lang/Throwable;Lcom/bytedance/sdk/component/eV/hjc/hjc;)V

    return-void
.end method
