.class public interface abstract Lcom/bytedance/sdk/component/eV/ex;
.super Ljava/lang/Object;


# annotations
.annotation runtime Lcom/bytedance/sdk/openadsdk/ats/ATSApi;
.end annotation


# virtual methods
.method public abstract Fj()J
    .annotation runtime Lcom/bytedance/sdk/openadsdk/ats/ATSMethod;
        value = 0x1
    .end annotation
.end method

.method public abstract Ubf()Ljava/io/File;
    .annotation runtime Lcom/bytedance/sdk/openadsdk/ats/ATSMethod;
        value = 0x5
    .end annotation
.end method

.method public abstract WR()Z
    .annotation runtime Lcom/bytedance/sdk/openadsdk/ats/ATSMethod;
        value = 0x6
    .end annotation
.end method

.method public abstract eV()Z
    .annotation runtime Lcom/bytedance/sdk/openadsdk/ats/ATSMethod;
        value = 0x4
    .end annotation
.end method

.method public abstract ex()I
    .annotation runtime Lcom/bytedance/sdk/openadsdk/ats/ATSMethod;
        value = 0x2
    .end annotation
.end method

.method public abstract hjc()Z
    .annotation runtime Lcom/bytedance/sdk/openadsdk/ats/ATSMethod;
        value = 0x3
    .end annotation
.end method
