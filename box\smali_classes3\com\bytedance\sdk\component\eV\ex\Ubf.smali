.class public Lcom/bytedance/sdk/component/eV/ex/Ubf;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/sdk/component/eV/svN;


# instance fields
.field private Fj:J

.field private ex:J

.field private hjc:J


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public Fj(J)V
    .locals 0

    iput-wide p1, p0, Lcom/bytedance/sdk/component/eV/ex/Ubf;->Fj:J

    return-void
.end method

.method public ex(J)V
    .locals 0

    iput-wide p1, p0, Lcom/bytedance/sdk/component/eV/ex/Ubf;->ex:J

    return-void
.end method

.method public hjc(J)V
    .locals 0

    iput-wide p1, p0, Lcom/bytedance/sdk/component/eV/ex/Ubf;->hjc:J

    return-void
.end method
