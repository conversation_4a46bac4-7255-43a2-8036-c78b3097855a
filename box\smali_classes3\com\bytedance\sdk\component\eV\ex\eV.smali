.class public Lcom/bytedance/sdk/component/eV/ex/eV;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/sdk/component/eV/WR;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "<T:",
        "Ljava/lang/Object;",
        ">",
        "Ljava/lang/Object;",
        "Lcom/bytedance/sdk/component/eV/WR;"
    }
.end annotation


# instance fields
.field Fj:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field private Ubf:Lcom/bytedance/sdk/component/eV/svN;

.field private eV:Ljava/lang/String;

.field private ex:I

.field private hjc:Ljava/lang/Object;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "TT;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(ILjava/lang/Object;Ljava/lang/String;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(ITT;",
            "Ljava/lang/String;",
            ")V"
        }
    .end annotation

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput p1, p0, Lcom/bytedance/sdk/component/eV/ex/eV;->ex:I

    iput-object p2, p0, Lcom/bytedance/sdk/component/eV/ex/eV;->hjc:Ljava/lang/Object;

    iput-object p3, p0, Lcom/bytedance/sdk/component/eV/ex/eV;->eV:Ljava/lang/String;

    return-void
.end method

.method public constructor <init>(ILjava/lang/Object;Ljava/lang/String;Ljava/util/Map;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(ITT;",
            "Ljava/lang/String;",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ">;)V"
        }
    .end annotation

    invoke-direct {p0, p1, p2, p3}, Lcom/bytedance/sdk/component/eV/ex/eV;-><init>(ILjava/lang/Object;Ljava/lang/String;)V

    iput-object p4, p0, Lcom/bytedance/sdk/component/eV/ex/eV;->Fj:Ljava/util/Map;

    return-void
.end method


# virtual methods
.method public Fj()Lcom/bytedance/sdk/component/eV/svN;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/eV/ex/eV;->Ubf:Lcom/bytedance/sdk/component/eV/svN;

    return-object v0
.end method

.method public Fj(Lcom/bytedance/sdk/component/eV/svN;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/eV/ex/eV;->Ubf:Lcom/bytedance/sdk/component/eV/svN;

    return-void
.end method

.method public Ubf()Ljava/util/Map;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    iget-object v0, p0, Lcom/bytedance/sdk/component/eV/ex/eV;->Fj:Ljava/util/Map;

    return-object v0
.end method

.method public eV()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/eV/ex/eV;->eV:Ljava/lang/String;

    return-object v0
.end method

.method public ex()I
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/component/eV/ex/eV;->ex:I

    return v0
.end method

.method public hjc()Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()TT;"
        }
    .end annotation

    iget-object v0, p0, Lcom/bytedance/sdk/component/eV/ex/eV;->hjc:Ljava/lang/Object;

    return-object v0
.end method
