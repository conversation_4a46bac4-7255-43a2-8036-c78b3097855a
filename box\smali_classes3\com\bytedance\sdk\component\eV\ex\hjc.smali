.class public Lcom/bytedance/sdk/component/eV/ex/hjc;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/sdk/component/eV/Ubf;


# instance fields
.field private Fj:Ljava/lang/String;

.field private eV:Lcom/bytedance/sdk/component/eV/Tc;

.field private ex:Z

.field private hjc:Z


# direct methods
.method public constructor <init>(Ljava/lang/String;ZZLcom/bytedance/sdk/component/eV/Tc;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/bytedance/sdk/component/eV/ex/hjc;->Fj:Ljava/lang/String;

    iput-boolean p2, p0, Lcom/bytedance/sdk/component/eV/ex/hjc;->ex:Z

    iput-boolean p3, p0, Lcom/bytedance/sdk/component/eV/ex/hjc;->hjc:Z

    iput-object p4, p0, Lcom/bytedance/sdk/component/eV/ex/hjc;->eV:Lcom/bytedance/sdk/component/eV/Tc;

    return-void
.end method


# virtual methods
.method public Fj()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/eV/ex/hjc;->Fj:Ljava/lang/String;

    return-object v0
.end method

.method public ex()Z
    .locals 1

    iget-boolean v0, p0, Lcom/bytedance/sdk/component/eV/ex/hjc;->ex:Z

    return v0
.end method

.method public hjc()Z
    .locals 1

    iget-boolean v0, p0, Lcom/bytedance/sdk/component/eV/ex/hjc;->hjc:Z

    return v0
.end method
