.class public interface abstract Lcom/bytedance/sdk/component/eV/hjc;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/sdk/component/eV/Fj;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Lcom/bytedance/sdk/component/eV/Fj<",
        "Ljava/lang/String;",
        "[B>;"
    }
.end annotation


# virtual methods
.method public abstract Fj(Ljava/lang/String;)Ljava/io/InputStream;
.end method
