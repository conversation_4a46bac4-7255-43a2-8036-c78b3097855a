.class public Lcom/bytedance/sdk/component/eV/hjc/Fj;
.super Ljava/lang/Object;


# instance fields
.field private Fj:I

.field private ex:Ljava/lang/String;

.field private hjc:Ljava/lang/Throwable;


# direct methods
.method public constructor <init>(ILjava/lang/String;Ljava/lang/Throwable;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput p1, p0, Lcom/bytedance/sdk/component/eV/hjc/Fj;->Fj:I

    iput-object p2, p0, Lcom/bytedance/sdk/component/eV/hjc/Fj;->ex:Ljava/lang/String;

    iput-object p3, p0, Lcom/bytedance/sdk/component/eV/hjc/Fj;->hjc:Ljava/lang/Throwable;

    return-void
.end method


# virtual methods
.method public Fj()I
    .locals 1

    iget v0, p0, Lcom/bytedance/sdk/component/eV/hjc/Fj;->Fj:I

    return v0
.end method

.method public ex()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/Fj;->ex:Ljava/lang/String;

    return-object v0
.end method

.method public hjc()Ljava/lang/Throwable;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/Fj;->hjc:Ljava/lang/Throwable;

    return-object v0
.end method
