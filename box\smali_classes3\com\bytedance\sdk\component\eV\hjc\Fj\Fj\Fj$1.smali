.class Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj$1;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/util/concurrent/Callable;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Ljava/util/concurrent/Callable<",
        "Ljava/lang/Void;",
        ">;"
    }
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj$1;->Fj:Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public Fj()Ljava/lang/Void;
    .locals 4
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/Exception;
        }
    .end annotation

    iget-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj$1;->Fj:Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj;

    monitor-enter v0

    :try_start_0
    iget-object v1, p0, Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj$1;->Fj:Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj;

    invoke-static {v1}, Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj;->Fj(Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj;)Ljava/io/Writer;

    move-result-object v1

    const/4 v2, 0x0

    if-nez v1, :cond_0

    monitor-exit v0

    return-object v2

    :catchall_0
    move-exception v1

    goto :goto_0

    :cond_0
    iget-object v1, p0, Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj$1;->Fj:Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj;

    invoke-static {v1}, Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj;->ex(Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj;)V

    iget-object v1, p0, Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj$1;->Fj:Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj;

    invoke-static {v1}, Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj;->hjc(Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj;)Z

    move-result v1

    if-eqz v1, :cond_1

    iget-object v1, p0, Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj$1;->Fj:Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj;

    invoke-static {v1}, Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj;->eV(Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj;)V

    iget-object v1, p0, Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj$1;->Fj:Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj;

    const/4 v3, 0x0

    invoke-static {v1, v3}, Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj;->Fj(Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj;I)I

    :cond_1
    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    return-object v2

    :goto_0
    monitor-exit v0

    throw v1
.end method

.method public synthetic call()Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/Exception;
        }
    .end annotation

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj$1;->Fj()Ljava/lang/Void;

    move-result-object v0

    return-object v0
.end method
