.class public final Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj$Fj;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x11
    name = "Fj"
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj$Fj$Fj;
    }
.end annotation


# instance fields
.field final synthetic Fj:Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj;

.field private Ubf:Z

.field private eV:Z

.field private final ex:Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj$ex;

.field private final hjc:[Z


# direct methods
.method private constructor <init>(Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj;Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj$ex;)V
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj$Fj;->Fj:Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p2, p0, Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj$Fj;->ex:Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj$ex;

    invoke-static {p2}, Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj$ex;->eV(Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj$ex;)Z

    move-result p2

    if-eqz p2, :cond_0

    const/4 p1, 0x0

    goto :goto_0

    :cond_0
    invoke-static {p1}, Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj;->Ubf(Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj;)I

    move-result p1

    new-array p1, p1, [Z

    :goto_0
    iput-object p1, p0, Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj$Fj;->hjc:[Z

    return-void
.end method

.method public synthetic constructor <init>(Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj;Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj$ex;Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj$1;)V
    .locals 0

    invoke-direct {p0, p1, p2}, Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj$Fj;-><init>(Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj;Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj$ex;)V

    return-void
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj$Fj;)Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj$ex;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj$Fj;->ex:Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj$ex;

    return-object p0
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj$Fj;Z)Z
    .locals 0

    iput-boolean p1, p0, Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj$Fj;->eV:Z

    return p1
.end method

.method public static synthetic ex(Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj$Fj;)[Z
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj$Fj;->hjc:[Z

    return-object p0
.end method


# virtual methods
.method public Fj(I)Ljava/io/OutputStream;
    .locals 3
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    if-ltz p1, :cond_2

    iget-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj$Fj;->Fj:Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj;

    invoke-static {v0}, Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj;->Ubf(Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj;)I

    move-result v0

    if-ge p1, v0, :cond_2

    iget-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj$Fj;->Fj:Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj;

    monitor-enter v0

    :try_start_0
    iget-object v1, p0, Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj$Fj;->ex:Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj$ex;

    invoke-static {v1}, Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj$ex;->Fj(Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj$ex;)Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj$Fj;

    move-result-object v1

    if-ne v1, p0, :cond_1

    iget-object v1, p0, Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj$Fj;->ex:Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj$ex;

    invoke-static {v1}, Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj$ex;->eV(Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj$ex;)Z

    move-result v1

    if-nez v1, :cond_0

    iget-object v1, p0, Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj$Fj;->hjc:[Z

    const/4 v2, 0x1

    aput-boolean v2, v1, p1

    goto :goto_0

    :catchall_0
    move-exception p1

    goto :goto_2

    :cond_0
    :goto_0
    iget-object v1, p0, Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj$Fj;->ex:Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj$ex;

    invoke-virtual {v1, p1}, Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj$ex;->ex(I)Ljava/io/File;

    move-result-object p1
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    :try_start_1
    new-instance v1, Ljava/io/FileOutputStream;

    invoke-direct {v1, p1}, Ljava/io/FileOutputStream;-><init>(Ljava/io/File;)V
    :try_end_1
    .catch Ljava/io/FileNotFoundException; {:try_start_1 .. :try_end_1} :catch_0
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    goto :goto_1

    :catch_0
    :try_start_2
    iget-object v1, p0, Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj$Fj;->Fj:Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj;

    invoke-static {v1}, Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj;->WR(Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj;)Ljava/io/File;

    move-result-object v1

    invoke-virtual {v1}, Ljava/io/File;->mkdirs()Z
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_0

    :try_start_3
    new-instance v1, Ljava/io/FileOutputStream;

    invoke-direct {v1, p1}, Ljava/io/FileOutputStream;-><init>(Ljava/io/File;)V
    :try_end_3
    .catch Ljava/io/FileNotFoundException; {:try_start_3 .. :try_end_3} :catch_1
    .catchall {:try_start_3 .. :try_end_3} :catchall_0

    :goto_1
    :try_start_4
    new-instance p1, Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj$Fj$Fj;

    const/4 v2, 0x0

    invoke-direct {p1, p0, v1, v2}, Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj$Fj$Fj;-><init>(Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj$Fj;Ljava/io/OutputStream;Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj$1;)V

    monitor-exit v0

    return-object p1

    :catch_1
    sget-object p1, Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj;->hjc:Ljava/io/OutputStream;

    monitor-exit v0

    return-object p1

    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    invoke-direct {p1}, Ljava/lang/IllegalStateException;-><init>()V

    throw p1
    :try_end_4
    .catchall {:try_start_4 .. :try_end_4} :catchall_0

    :goto_2
    monitor-exit v0

    throw p1

    :cond_2
    new-instance v0, Ljava/lang/IllegalArgumentException;

    new-instance v1, Ljava/lang/StringBuilder;

    const-string v2, "Expected index "

    invoke-direct {v1, v2}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string p1, " to be greater than 0 and less than the maximum value count of "

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object p1, p0, Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj$Fj;->Fj:Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj;

    invoke-static {p1}, Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj;->Ubf(Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj;)I

    move-result p1

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-direct {v0, p1}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw v0
.end method

.method public Fj()V
    .locals 3
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget-boolean v0, p0, Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj$Fj;->eV:Z

    const/4 v1, 0x1

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj$Fj;->Fj:Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj;

    const/4 v2, 0x0

    invoke-static {v0, p0, v2}, Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj;->Fj(Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj;Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj$Fj;Z)V

    iget-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj$Fj;->Fj:Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj;

    iget-object v2, p0, Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj$Fj;->ex:Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj$ex;

    invoke-static {v2}, Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj$ex;->hjc(Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj$ex;)Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v0, v2}, Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj;->hjc(Ljava/lang/String;)Z

    goto :goto_0

    :cond_0
    iget-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj$Fj;->Fj:Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj;

    invoke-static {v0, p0, v1}, Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj;->Fj(Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj;Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj$Fj;Z)V

    :goto_0
    iput-boolean v1, p0, Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj$Fj;->Ubf:Z

    return-void
.end method

.method public ex()V
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj$Fj;->Fj:Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj;

    const/4 v1, 0x0

    invoke-static {v0, p0, v1}, Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj;->Fj(Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj;Lcom/bytedance/sdk/component/eV/hjc/Fj/Fj/Fj$Fj;Z)V

    return-void
.end method
