.class public Lcom/bytedance/sdk/component/eV/hjc/Fj/ex/Fj;
.super Ljava/lang/Object;


# direct methods
.method public static Fj(I)Lcom/bytedance/sdk/component/eV/rS;
    .locals 3

    new-instance v0, Lcom/bytedance/sdk/component/eV/hjc/Fj/ex/eV;

    new-instance v1, Lcom/bytedance/sdk/component/eV/hjc/Fj/ex/ex;

    const v2, 0x7fffffff

    invoke-direct {v1, p0, v2}, Lcom/bytedance/sdk/component/eV/hjc/Fj/ex/ex;-><init>(II)V

    invoke-direct {v0, v1}, Lcom/bytedance/sdk/component/eV/hjc/Fj/ex/eV;-><init>(Lcom/bytedance/sdk/component/eV/rS;)V

    return-object v0
.end method

.method public static Fj(Lcom/bytedance/sdk/component/eV/rS;)Lcom/bytedance/sdk/component/eV/rS;
    .locals 1

    new-instance v0, Lcom/bytedance/sdk/component/eV/hjc/Fj/ex/eV;

    invoke-direct {v0, p0}, Lcom/bytedance/sdk/component/eV/hjc/Fj/ex/eV;-><init>(Lcom/bytedance/sdk/component/eV/rS;)V

    return-object v0
.end method
