.class public Lcom/bytedance/sdk/component/eV/hjc/Fj/ex/Ubf;
.super Ljava/lang/Object;


# direct methods
.method public static Fj(I)Lcom/bytedance/sdk/component/eV/vYf;
    .locals 2

    new-instance v0, Lcom/bytedance/sdk/component/eV/hjc/Fj/ex/hjc;

    const v1, 0x7fffffff

    invoke-direct {v0, p0, v1}, Lcom/bytedance/sdk/component/eV/hjc/Fj/ex/hjc;-><init>(II)V

    return-object v0
.end method
