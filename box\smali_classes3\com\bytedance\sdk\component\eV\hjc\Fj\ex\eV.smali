.class public Lcom/bytedance/sdk/component/eV/hjc/Fj/ex/eV;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/sdk/component/eV/rS;


# instance fields
.field private final Fj:Lcom/bytedance/sdk/component/eV/rS;

.field private final ex:Lcom/bytedance/sdk/component/eV/hjc/Fj/ex;


# direct methods
.method public constructor <init>(Lcom/bytedance/sdk/component/eV/rS;)V
    .locals 1

    const/4 v0, 0x0

    invoke-direct {p0, p1, v0}, Lcom/bytedance/sdk/component/eV/hjc/Fj/ex/eV;-><init>(Lcom/bytedance/sdk/component/eV/rS;Lcom/bytedance/sdk/component/eV/hjc/Fj/ex;)V

    return-void
.end method

.method public constructor <init>(Lcom/bytedance/sdk/component/eV/rS;Lcom/bytedance/sdk/component/eV/hjc/Fj/ex;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/bytedance/sdk/component/eV/hjc/Fj/ex/eV;->Fj:Lcom/bytedance/sdk/component/eV/rS;

    iput-object p2, p0, Lcom/bytedance/sdk/component/eV/hjc/Fj/ex/eV;->ex:Lcom/bytedance/sdk/component/eV/hjc/Fj/ex;

    return-void
.end method


# virtual methods
.method public Fj(Ljava/lang/String;)Landroid/graphics/Bitmap;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/Fj/ex/eV;->Fj:Lcom/bytedance/sdk/component/eV/rS;

    invoke-interface {v0, p1}, Lcom/bytedance/sdk/component/eV/Fj;->Fj(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Landroid/graphics/Bitmap;

    return-object p1
.end method

.method public bridge synthetic Fj(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    check-cast p1, Ljava/lang/String;

    invoke-virtual {p0, p1}, Lcom/bytedance/sdk/component/eV/hjc/Fj/ex/eV;->Fj(Ljava/lang/String;)Landroid/graphics/Bitmap;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic Fj(Ljava/lang/Object;Ljava/lang/Object;)Z
    .locals 0

    check-cast p1, Ljava/lang/String;

    check-cast p2, Landroid/graphics/Bitmap;

    invoke-virtual {p0, p1, p2}, Lcom/bytedance/sdk/component/eV/hjc/Fj/ex/eV;->Fj(Ljava/lang/String;Landroid/graphics/Bitmap;)Z

    move-result p1

    return p1
.end method

.method public Fj(Ljava/lang/String;Landroid/graphics/Bitmap;)Z
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/Fj/ex/eV;->Fj:Lcom/bytedance/sdk/component/eV/rS;

    invoke-interface {v0, p1, p2}, Lcom/bytedance/sdk/component/eV/Fj;->Fj(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result p1

    return p1
.end method

.method public bridge synthetic ex(Ljava/lang/Object;)Z
    .locals 0

    check-cast p1, Ljava/lang/String;

    invoke-virtual {p0, p1}, Lcom/bytedance/sdk/component/eV/hjc/Fj/ex/eV;->ex(Ljava/lang/String;)Z

    move-result p1

    return p1
.end method

.method public ex(Ljava/lang/String;)Z
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/Fj/ex/eV;->Fj:Lcom/bytedance/sdk/component/eV/rS;

    invoke-interface {v0, p1}, Lcom/bytedance/sdk/component/eV/Fj;->ex(Ljava/lang/Object;)Z

    move-result p1

    return p1
.end method
