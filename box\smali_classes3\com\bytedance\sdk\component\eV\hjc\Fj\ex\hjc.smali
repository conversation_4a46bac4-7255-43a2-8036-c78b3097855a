.class public Lcom/bytedance/sdk/component/eV/hjc/Fj/ex/hjc;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/sdk/component/eV/vYf;


# instance fields
.field private Fj:I

.field private ex:I

.field private hjc:Lcom/bytedance/sdk/component/eV/hjc/Fj/hjc;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/bytedance/sdk/component/eV/hjc/Fj/hjc<",
            "Ljava/lang/String;",
            "[B>;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(II)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput p1, p0, Lcom/bytedance/sdk/component/eV/hjc/Fj/ex/hjc;->ex:I

    iput p2, p0, Lcom/bytedance/sdk/component/eV/hjc/Fj/ex/hjc;->Fj:I

    new-instance p2, Lcom/bytedance/sdk/component/eV/hjc/Fj/ex/hjc$1;

    invoke-direct {p2, p0, p1}, Lcom/bytedance/sdk/component/eV/hjc/Fj/ex/hjc$1;-><init>(Lcom/bytedance/sdk/component/eV/hjc/Fj/ex/hjc;I)V

    iput-object p2, p0, Lcom/bytedance/sdk/component/eV/hjc/Fj/ex/hjc;->hjc:Lcom/bytedance/sdk/component/eV/hjc/Fj/hjc;

    return-void
.end method


# virtual methods
.method public bridge synthetic Fj(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    check-cast p1, Ljava/lang/String;

    invoke-virtual {p0, p1}, Lcom/bytedance/sdk/component/eV/hjc/Fj/ex/hjc;->Fj(Ljava/lang/String;)[B

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic Fj(Ljava/lang/Object;Ljava/lang/Object;)Z
    .locals 0

    check-cast p1, Ljava/lang/String;

    check-cast p2, [B

    invoke-virtual {p0, p1, p2}, Lcom/bytedance/sdk/component/eV/hjc/Fj/ex/hjc;->Fj(Ljava/lang/String;[B)Z

    move-result p1

    return p1
.end method

.method public Fj(Ljava/lang/String;[B)Z
    .locals 1

    if-eqz p1, :cond_1

    if-nez p2, :cond_0

    goto :goto_0

    :cond_0
    iget-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/Fj/ex/hjc;->hjc:Lcom/bytedance/sdk/component/eV/hjc/Fj/hjc;

    invoke-virtual {v0, p1, p2}, Lcom/bytedance/sdk/component/eV/hjc/Fj/hjc;->Fj(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const/4 p1, 0x1

    return p1

    :cond_1
    :goto_0
    const/4 p1, 0x0

    return p1
.end method

.method public Fj(Ljava/lang/String;)[B
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/Fj/ex/hjc;->hjc:Lcom/bytedance/sdk/component/eV/hjc/Fj/hjc;

    invoke-virtual {v0, p1}, Lcom/bytedance/sdk/component/eV/hjc/Fj/hjc;->Fj(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, [B

    return-object p1
.end method

.method public bridge synthetic ex(Ljava/lang/Object;)Z
    .locals 0

    check-cast p1, Ljava/lang/String;

    invoke-virtual {p0, p1}, Lcom/bytedance/sdk/component/eV/hjc/Fj/ex/hjc;->ex(Ljava/lang/String;)Z

    move-result p1

    return p1
.end method

.method public ex(Ljava/lang/String;)Z
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/Fj/ex/hjc;->hjc:Lcom/bytedance/sdk/component/eV/hjc/Fj/hjc;

    invoke-virtual {v0, p1}, Lcom/bytedance/sdk/component/eV/hjc/Fj/hjc;->Fj(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    if-eqz p1, :cond_0

    const/4 p1, 0x1

    return p1

    :cond_0
    const/4 p1, 0x0

    return p1
.end method
