.class public Lcom/bytedance/sdk/component/eV/hjc/Ubf$Fj;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bytedance/sdk/component/eV/hjc/Ubf;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "Fj"
.end annotation


# instance fields
.field private BcC:Lcom/bytedance/sdk/component/eV/ex;

.field private Fj:Lcom/bytedance/sdk/component/eV/UYd;

.field private Ubf:Lcom/bytedance/sdk/component/eV/vYf;

.field private WR:Lcom/bytedance/sdk/component/eV/hjc;

.field private eV:Lcom/bytedance/sdk/component/eV/rS;

.field private ex:Ljava/util/concurrent/ExecutorService;

.field private hjc:Lcom/bytedance/sdk/component/eV/eV;

.field private svN:Lcom/bytedance/sdk/component/eV/Ql;


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static synthetic BcC(Lcom/bytedance/sdk/component/eV/hjc/Ubf$Fj;)Lcom/bytedance/sdk/component/eV/Ql;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/component/eV/hjc/Ubf$Fj;->svN:Lcom/bytedance/sdk/component/eV/Ql;

    return-object p0
.end method

.method public static synthetic Fj(Lcom/bytedance/sdk/component/eV/hjc/Ubf$Fj;)Lcom/bytedance/sdk/component/eV/UYd;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/component/eV/hjc/Ubf$Fj;->Fj:Lcom/bytedance/sdk/component/eV/UYd;

    return-object p0
.end method

.method public static synthetic Ubf(Lcom/bytedance/sdk/component/eV/hjc/Ubf$Fj;)Lcom/bytedance/sdk/component/eV/vYf;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/component/eV/hjc/Ubf$Fj;->Ubf:Lcom/bytedance/sdk/component/eV/vYf;

    return-object p0
.end method

.method public static synthetic WR(Lcom/bytedance/sdk/component/eV/hjc/Ubf$Fj;)Lcom/bytedance/sdk/component/eV/hjc;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/component/eV/hjc/Ubf$Fj;->WR:Lcom/bytedance/sdk/component/eV/hjc;

    return-object p0
.end method

.method public static synthetic eV(Lcom/bytedance/sdk/component/eV/hjc/Ubf$Fj;)Lcom/bytedance/sdk/component/eV/rS;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/component/eV/hjc/Ubf$Fj;->eV:Lcom/bytedance/sdk/component/eV/rS;

    return-object p0
.end method

.method public static synthetic ex(Lcom/bytedance/sdk/component/eV/hjc/Ubf$Fj;)Ljava/util/concurrent/ExecutorService;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/component/eV/hjc/Ubf$Fj;->ex:Ljava/util/concurrent/ExecutorService;

    return-object p0
.end method

.method public static synthetic hjc(Lcom/bytedance/sdk/component/eV/hjc/Ubf$Fj;)Lcom/bytedance/sdk/component/eV/eV;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/component/eV/hjc/Ubf$Fj;->hjc:Lcom/bytedance/sdk/component/eV/eV;

    return-object p0
.end method

.method public static synthetic svN(Lcom/bytedance/sdk/component/eV/hjc/Ubf$Fj;)Lcom/bytedance/sdk/component/eV/ex;
    .locals 0

    iget-object p0, p0, Lcom/bytedance/sdk/component/eV/hjc/Ubf$Fj;->BcC:Lcom/bytedance/sdk/component/eV/ex;

    return-object p0
.end method


# virtual methods
.method public Fj(Lcom/bytedance/sdk/component/eV/eV;)Lcom/bytedance/sdk/component/eV/hjc/Ubf$Fj;
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/eV/hjc/Ubf$Fj;->hjc:Lcom/bytedance/sdk/component/eV/eV;

    return-object p0
.end method

.method public Fj(Lcom/bytedance/sdk/component/eV/ex;)Lcom/bytedance/sdk/component/eV/hjc/Ubf$Fj;
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/eV/hjc/Ubf$Fj;->BcC:Lcom/bytedance/sdk/component/eV/ex;

    return-object p0
.end method

.method public Fj(Ljava/util/concurrent/ExecutorService;)Lcom/bytedance/sdk/component/eV/hjc/Ubf$Fj;
    .locals 0

    iput-object p1, p0, Lcom/bytedance/sdk/component/eV/hjc/Ubf$Fj;->ex:Ljava/util/concurrent/ExecutorService;

    return-object p0
.end method

.method public Fj()Lcom/bytedance/sdk/component/eV/hjc/Ubf;
    .locals 2

    new-instance v0, Lcom/bytedance/sdk/component/eV/hjc/Ubf;

    const/4 v1, 0x0

    invoke-direct {v0, p0, v1}, Lcom/bytedance/sdk/component/eV/hjc/Ubf;-><init>(Lcom/bytedance/sdk/component/eV/hjc/Ubf$Fj;Lcom/bytedance/sdk/component/eV/hjc/Ubf$1;)V

    return-object v0
.end method
