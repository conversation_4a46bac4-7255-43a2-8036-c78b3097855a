.class public Lcom/bytedance/sdk/component/eV/hjc/Ubf;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/bytedance/sdk/component/eV/dG;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/bytedance/sdk/component/eV/hjc/Ubf$Fj;
    }
.end annotation


# instance fields
.field private BcC:Lcom/bytedance/sdk/component/eV/ex;

.field private Fj:Lcom/bytedance/sdk/component/eV/UYd;

.field private Ubf:Lcom/bytedance/sdk/component/eV/vYf;

.field private WR:Lcom/bytedance/sdk/component/eV/hjc;

.field private eV:Lcom/bytedance/sdk/component/eV/rS;

.field private ex:Ljava/util/concurrent/ExecutorService;

.field private hjc:Lcom/bytedance/sdk/component/eV/eV;

.field private svN:Lcom/bytedance/sdk/component/eV/Ql;


# direct methods
.method private constructor <init>(Lcom/bytedance/sdk/component/eV/hjc/Ubf$Fj;)V
    .locals 1

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    invoke-static {p1}, Lcom/bytedance/sdk/component/eV/hjc/Ubf$Fj;->Fj(Lcom/bytedance/sdk/component/eV/hjc/Ubf$Fj;)Lcom/bytedance/sdk/component/eV/UYd;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/Ubf;->Fj:Lcom/bytedance/sdk/component/eV/UYd;

    invoke-static {p1}, Lcom/bytedance/sdk/component/eV/hjc/Ubf$Fj;->ex(Lcom/bytedance/sdk/component/eV/hjc/Ubf$Fj;)Ljava/util/concurrent/ExecutorService;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/Ubf;->ex:Ljava/util/concurrent/ExecutorService;

    invoke-static {p1}, Lcom/bytedance/sdk/component/eV/hjc/Ubf$Fj;->hjc(Lcom/bytedance/sdk/component/eV/hjc/Ubf$Fj;)Lcom/bytedance/sdk/component/eV/eV;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/Ubf;->hjc:Lcom/bytedance/sdk/component/eV/eV;

    invoke-static {p1}, Lcom/bytedance/sdk/component/eV/hjc/Ubf$Fj;->eV(Lcom/bytedance/sdk/component/eV/hjc/Ubf$Fj;)Lcom/bytedance/sdk/component/eV/rS;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/Ubf;->eV:Lcom/bytedance/sdk/component/eV/rS;

    invoke-static {p1}, Lcom/bytedance/sdk/component/eV/hjc/Ubf$Fj;->Ubf(Lcom/bytedance/sdk/component/eV/hjc/Ubf$Fj;)Lcom/bytedance/sdk/component/eV/vYf;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/Ubf;->Ubf:Lcom/bytedance/sdk/component/eV/vYf;

    invoke-static {p1}, Lcom/bytedance/sdk/component/eV/hjc/Ubf$Fj;->WR(Lcom/bytedance/sdk/component/eV/hjc/Ubf$Fj;)Lcom/bytedance/sdk/component/eV/hjc;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/Ubf;->WR:Lcom/bytedance/sdk/component/eV/hjc;

    invoke-static {p1}, Lcom/bytedance/sdk/component/eV/hjc/Ubf$Fj;->svN(Lcom/bytedance/sdk/component/eV/hjc/Ubf$Fj;)Lcom/bytedance/sdk/component/eV/ex;

    move-result-object v0

    iput-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/Ubf;->BcC:Lcom/bytedance/sdk/component/eV/ex;

    invoke-static {p1}, Lcom/bytedance/sdk/component/eV/hjc/Ubf$Fj;->BcC(Lcom/bytedance/sdk/component/eV/hjc/Ubf$Fj;)Lcom/bytedance/sdk/component/eV/Ql;

    move-result-object p1

    iput-object p1, p0, Lcom/bytedance/sdk/component/eV/hjc/Ubf;->svN:Lcom/bytedance/sdk/component/eV/Ql;

    return-void
.end method

.method public synthetic constructor <init>(Lcom/bytedance/sdk/component/eV/hjc/Ubf$Fj;Lcom/bytedance/sdk/component/eV/hjc/Ubf$1;)V
    .locals 0

    invoke-direct {p0, p1}, Lcom/bytedance/sdk/component/eV/hjc/Ubf;-><init>(Lcom/bytedance/sdk/component/eV/hjc/Ubf$Fj;)V

    return-void
.end method

.method public static Fj(Landroid/content/Context;)Lcom/bytedance/sdk/component/eV/hjc/Ubf;
    .locals 0

    new-instance p0, Lcom/bytedance/sdk/component/eV/hjc/Ubf$Fj;

    invoke-direct {p0}, Lcom/bytedance/sdk/component/eV/hjc/Ubf$Fj;-><init>()V

    invoke-virtual {p0}, Lcom/bytedance/sdk/component/eV/hjc/Ubf$Fj;->Fj()Lcom/bytedance/sdk/component/eV/hjc/Ubf;

    move-result-object p0

    return-object p0
.end method


# virtual methods
.method public BcC()Lcom/bytedance/sdk/component/eV/ex;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/Ubf;->BcC:Lcom/bytedance/sdk/component/eV/ex;

    return-object v0
.end method

.method public Fj()Lcom/bytedance/sdk/component/eV/UYd;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/Ubf;->Fj:Lcom/bytedance/sdk/component/eV/UYd;

    return-object v0
.end method

.method public Ubf()Lcom/bytedance/sdk/component/eV/vYf;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/Ubf;->Ubf:Lcom/bytedance/sdk/component/eV/vYf;

    return-object v0
.end method

.method public WR()Lcom/bytedance/sdk/component/eV/hjc;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/Ubf;->WR:Lcom/bytedance/sdk/component/eV/hjc;

    return-object v0
.end method

.method public eV()Lcom/bytedance/sdk/component/eV/rS;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/Ubf;->eV:Lcom/bytedance/sdk/component/eV/rS;

    return-object v0
.end method

.method public ex()Ljava/util/concurrent/ExecutorService;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/Ubf;->ex:Ljava/util/concurrent/ExecutorService;

    return-object v0
.end method

.method public hjc()Lcom/bytedance/sdk/component/eV/eV;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/Ubf;->hjc:Lcom/bytedance/sdk/component/eV/eV;

    return-object v0
.end method

.method public svN()Lcom/bytedance/sdk/component/eV/Ql;
    .locals 1

    iget-object v0, p0, Lcom/bytedance/sdk/component/eV/hjc/Ubf;->svN:Lcom/bytedance/sdk/component/eV/Ql;

    return-object v0
.end method
